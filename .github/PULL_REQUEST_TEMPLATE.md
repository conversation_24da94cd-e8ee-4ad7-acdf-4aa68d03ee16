<!--
Please read the [Code of Conduct](https://github.com/nodejs/nodejs.org/blob/main/CODE_OF_CONDUCT.md) and the [Contributing Guidelines](https://github.com/nodejs/nodejs.org/blob/main/CONTRIBUTING.md) before opening a pull request.
-->

## Description

<!-- Write a brief description of the changes introduced by this PR -->

## Validation

<!-- How do you know this is working? What should a reviewer look for? Provide a screenshot if your change is visual.-->

## Related Issues

<!--
  Link to the issue that is fixed by this PR (if there is one)
  e.g. Fixes #1234, Addresses #1234, Related to #1234, etc.
-->

### Check List

<!--
ATTENTION
Please follow this check list to ensure that you've followed all items before opening this PR
You can check the items by adding an `x` between the brackets, like this: `[x]`
-->

- [ ] I have read the [Contributing Guidelines](https://github.com/nodejs/nodejs.org/blob/main/CONTRIBUTING.md) and made commit messages that follow the guideline.
- [ ] I have run `pnpm format` to ensure the code follows the style guide.
- [ ] I have run `pnpm test` to check if all tests are passing.
- [ ] I have run `pnpm build` to check if the website builds without errors.
- [ ] I've covered new added functionality with unit tests if necessary.
