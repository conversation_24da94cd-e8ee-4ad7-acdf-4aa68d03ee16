# Security Notes
# Only selected Actions are allowed within this repository. Please refer to (https://github.com/nodejs/nodejs.org/settings/actions)
# for the full list of available actions. If you want to add a new one, please reach out a maintainer with Admin permissions.
# REVIEWERS, please always double-check security practices before merging a PR that contains Workflow changes!!
# AUTHORS, please only use actions with explicit SHA references, and avoid using `@master` or `@main` references or `@version` tags.

name: Build

on:
  push:
    branches:
      - main
  pull_request_target:
    branches:
      - main
    types:
      - labeled
  merge_group:
  workflow_dispatch:

defaults:
  run:
    # This ensures that the working directory is the root of the repository
    working-directory: ./

permissions:
  contents: read
  actions: read

env:
  # See https://turbo.build/repo/docs/reference/command-line-reference/run#--cache-dir
  TURBO_ARGS: --cache-dir=.turbo/cache
  # See https://turbo.build/repo/docs/reference/command-line-reference/run#--force
  TURBO_FORCE: true

jobs:
  build:
    # This Job should run either on non-`pull_request_target` events,
    # or `pull_request_target` event with a `labeled` action with a label named `github_actions:pull-request`
    # since we want to run Website Builds on all these occasions. As this allows us to be certain the that builds are passing
    if: github.event_name != 'pull_request_target' || github.event.label.name == 'github_actions:pull-request'

    name: Build on ${{ matrix.os }}
    runs-on: ${{ matrix.os }}

    strategy:
      fail-fast: false
      matrix:
        os: [ubuntu-latest, windows-latest]

    steps:
      - name: Harden Runner
        uses: step-security/harden-runner@0634a2670c59f64b4a01f0f96f84700a4088b9f0 # v2.12.0
        with:
          egress-policy: audit

      - name: Use GNU tar instead BSD tar
        # This ensures that we use GNU `tar` which is more efficient for extracting caches's
        if: matrix.os == 'windows-latest'
        shell: cmd
        run: echo C:\Program Files\Git\usr\bin>>"%GITHUB_PATH%"

      - name: Git Checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          # Provides the Pull Request commit SHA or the GitHub merge group ref
          ref: ${{ github.event_name == 'pull_request_target' && github.event.pull_request.head.sha || github.ref }}
          # We only need to fetch the last commit from the head_ref
          # since we're not using the `--filter` operation from turborepo
          # We don't use the `--filter` as we always want to force builds regardless of having changes or not
          # this ensures that our bundle analysis script always runs and that we always ensure next.js is building
          # regardless of having code changes or not
          fetch-depth: 1

      - uses: actions/cache@5a3ec84eff668545956fd18022155c47e93e2684 # v4.2.3
        with:
          path: ${{ github.workspace }}/apps/site/.next/cache
          key: ${{ runner.os }}-nextjs-${{ hashFiles('**/pnpm-lock.yaml') }}-${{ hashFiles('**/*.js', '**/*.jsx', '**/*.ts', '**/*.tsx') }}
          restore-keys: ${{ runner.os }}-nextjs-${{ hashFiles('**/pnpm-lock.yaml') }}-

      - name: Set up pnpm
        uses: pnpm/action-setup@a7487c7e89a18df4991f7f222e4898a00d66ddda # v4.1.0
        with:
          cache: true

      - name: Set up Node.js
        uses: actions/setup-node@49933ea5288caeca8642d1e84afbd3f7d6820020 # v4.4.0
        with:
          # We want to ensure that the Node.js version running here respects our supported versions
          node-version-file: '.nvmrc'
          cache: 'pnpm'

      - name: Install packages
        # We only want to install required production packages
        run: pnpm install --prod --frozen-lockfile

      - name: Build Next.js (ISR)
        # We want a ISR build on CI to ensure that regular Next.js builds work as expected.
        run: pnpm exec turbo build ${{ env.TURBO_ARGS }}
        env:
          # We want to ensure we have enough RAM allocated to the Node.js process
          # this should be a last resort in case by any chances the build memory gets too high
          # but in general this should never happen
          NODE_OPTIONS: '--max_old_space_size=4096'
          # Used for API requests that require GitHub API scopes
          NEXT_GITHUB_API_KEY: ${{ secrets.GITHUB_TOKEN }}

      - name: Build Next.js (Static All Locales)
        # We only run full static builds within Pull Requests. This step is also used to export
        # static output in all languages, and it only works on `push` events.
        if: github.event_name == 'push'
        run: pnpm exec turbo deploy ${{ env.TURBO_ARGS }}
        env:
          # We want to ensure we have enough RAM allocated to the Node.js process
          # this should be a last resort in case by any chances the build memory gets too high
          # but in general this should never happen
          NODE_OPTIONS: '--max_old_space_size=4096'
          # Used for API requests that require GitHub API scopes
          NEXT_GITHUB_API_KEY: ${{ secrets.GITHUB_TOKEN }}
          # We want to ensure that static exports for all locales are triggered only on `push` events to save resources
          # and time.
          NEXT_PUBLIC_STATIC_EXPORT_LOCALE: true

      - name: Build Next.js (Static Default Locale)
        # We want to generate static output in the default language within Pull Requests
        # in order to reduce source wastages and build times.
        # Note that we skip full static builds on Crowdin-based Pull Requests as these PRs should only contain translation changes
        if: |
          (github.event_name == 'pull_request_target' &&
            github.event.pull_request.head.ref != 'chore/crowdin')
        run: pnpm exec turbo deploy ${{ env.TURBO_ARGS }}
        env:
          # We want to ensure we have enough RAM allocated to the Node.js process
          # this should be a last resort in case by any chances the build memory gets too high
          # but in general this should never happen
          NODE_OPTIONS: '--max_old_space_size=4096'
          # Used for API requests that require GitHub API scopes
          NEXT_GITHUB_API_KEY: ${{ secrets.GITHUB_TOKEN }}
          # We want to ensure that static exports for all locales do not occur on `pull_request_target` events
          NEXT_PUBLIC_STATIC_EXPORT_LOCALE: false

      - name: Sync Orama Cloud
        # We only want to sync the Orama Cloud production indexes on `push` events.
        # We also want to sync the Orama Cloud preview (deployment) indexes on `pull_request_target` events (or manual triggers).
        # We also want to ensure that the sync only happens on the `ubuntu-latest` runner to avoid duplicate syncs
        # or Windows-based path issues.
        env:
          ORAMA_INDEX_ID: ${{ github.event_name == 'push' && secrets.ORAMA_PRODUCTION_INDEX_ID || secrets.ORAMA_INDEX_ID }}
          ORAMA_SECRET_KEY: ${{ github.event_name == 'push' && secrets.ORAMA_PRODUCTION_SECRET_KEY || secrets.ORAMA_SECRET_KEY }}
        if: matrix.os == 'ubuntu-latest' && github.event_name != 'merge_group'
        working-directory: apps/site
        run: pnpm sync-orama
