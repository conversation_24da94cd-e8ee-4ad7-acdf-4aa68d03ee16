# Security Notes
# Only selected Actions are allowed within this repository. Please refer to (https://github.com/nodejs/nodejs.org/settings/actions)
# for the full list of available actions. If you want to add a new one, please reach out a maintainer with Admin permissions.
# REVIEWERS, please always double-check security practices before merging a PR that contains Workflow changes!!
# AUTHORS, please only use actions with explicit SHA references, and avoid using `@master` or `@main` references or `@version` tags.

name: Chromatic

on:
  merge_group:
  push:
    branches:
      - main
  pull_request_target:
    branches:
      - main
    types:
      - labeled
  workflow_dispatch:

defaults:
  run:
    # This ensures that the working directory is the root of the repository
    working-directory: ./

permissions:
  contents: read
  actions: read

jobs:
  chromatic:
    # We only need to run Storybook Builds and Storybook Visual Regression Tests within Pull Requests that actually
    # introduce changes to the Storybook. Hence, we skip running these on Crowdin PRs and Dependabot PRs
    if: |
      github.event_name != 'pull_request_target' ||
      (
        github.event.label.name == 'github_actions:pull-request' &&
        github.actor != 'dependabot[bot]' &&
        github.event.pull_request.head.ref != 'chore/crowdin'
      )

    name: Chromatic
    runs-on: ubuntu-latest

    environment:
      name: Storybook
      url: ${{ steps.chromatic-deploy.outputs.storybookUrl }}

    steps:
      - name: Harden Runner
        uses: step-security/harden-runner@0634a2670c59f64b4a01f0f96f84700a4088b9f0 # v2.12.0
        with:
          egress-policy: audit

      - name: Git Checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          # Provides the Pull Request commit SHA or the GitHub merge group ref
          ref: ${{ github.event_name == 'pull_request_target' && github.event.pull_request.head.sha || github.ref }}
          # The Chromatic (@chromaui/action) Action requires a full history of the current branch in order to be able to compare
          # previous changes and previous commits and determine which Storybooks should be tested against and what should be built
          fetch-depth: 0

      - name: Set up pnpm
        uses: pnpm/action-setup@a7487c7e89a18df4991f7f222e4898a00d66ddda # v4.1.0
        with:
          cache: true

      - name: Set up Node.js
        uses: actions/setup-node@49933ea5288caeca8642d1e84afbd3f7d6820020 # v4.4.0
        with:
          # We want to ensure that the Node.js version running here respects our supported versions
          node-version-file: '.nvmrc'
          cache: 'pnpm'

      - name: Install packages
        run: pnpm install --frozen-lockfile

      - name: Start Visual Regression Tests (Chromatic)
        # This assigns the Environment Deployment for Storybook
        # This action must be manually updated to the latest commit from
        # https://github.com/chromaui/action
        id: chromatic-deploy
        uses: chromaui/action@e8cc4c31775280b175a3c440076c00d19a9014d7 # v11.28.2
        with:
          workingDir: packages/ui-components
          buildScriptName: storybook:build
          projectToken: ${{ secrets.CHROMATIC_PROJECT_TOKEN }}
          exitOnceUploaded: true
          onlyChanged: true
