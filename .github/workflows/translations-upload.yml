# This action automates the upload of our source content to crowdin.
# See translations-sync.yml for the automation to download new translations on a schedule
# See translations-pr-lint.yml for quality control we conduct on ingress of new translations.
name: Crowdin Upload

on:
  push:
    branches: [main]

# Cancel any runs on the same branch
concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  upload-to-crowdin:
    runs-on: ubuntu-latest

    steps:
      - name: Harden Runner
        uses: step-security/harden-runner@0634a2670c59f64b4a01f0f96f84700a4088b9f0 # v2.12.0
        with:
          egress-policy: audit

      - name: Checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

        # see all the options at https://github.com/crowdin/github-action
      - name: crowdin action
        uses: crowdin/github-action@b8012bd5491b8aa8578b73ab5b5f5e7c94aaa6e2 # v2.7.0
        with:
          # only upload sources, ensuring this is a one-way operation
          upload_sources: true
          upload_translations: false
          download_translations: false
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          # A numeric ID, found at https://crowdin.com/project/nodejs-web/tools/api
          CROWDIN_PROJECT_ID: ${{ secrets.CROWDIN_PROJECT_ID }}
          # Created from https://crowdin.com/settings#api-key logged in using nodejs-crowdin-bot
          CROWDIN_PERSONAL_TOKEN: ${{ secrets.CROWDIN_PERSONAL_TOKEN }}
