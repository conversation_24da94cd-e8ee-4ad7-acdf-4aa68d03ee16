# Security Notes
# This workflow uses `pull_request_target`, so will run against all PRs automatically (without approval), be careful with allowing any user-provided code to be run here
# Only selected Actions are allowed within this repository. Please refer to (https://github.com/nodejs/nodejs.org/settings/actions)
# for the full list of available actions. If you want to add a new one, please reach out a maintainer with Admin permissions.
# REVIEWERS, please always double-check security practices before merging a PR that contains Workflow changes!!
# AUTHORS, please only use actions with explicit SHA references, and avoid using `@master` or `@main` references or `@version` tags.
# MERGE QUEUE NOTE: This Workflow does not run on `merge_group` trigger, as this Workflow is not required for Merge Queue's

name: Lighthouse

on:
  pull_request_target:
    branches:
      - main
    types:
      - labeled

defaults:
  run:
    # This ensures that the working directory is the root of the repository
    working-directory: ./

permissions:
  contents: read
  actions: read
  # This permission is required by `thollander/actions-comment-pull-request`
  pull-requests: write

jobs:
  get-vercel-preview:
    # We want to skip our lighthouse analysis on Dependabot PRs
    if: |
      startsWith(github.event.pull_request.head.ref, 'dependabot/') == false &&
      github.event.label.name == 'github_actions:pull-request'
    name: Get Vercel Preview
    runs-on: ubuntu-latest
    outputs:
      deployment_found: ${{ steps.set_outputs.outputs.deployment_found }}
      url: ${{ steps.set_outputs.outputs.url }}
    steps:
      - name: Capture Vercel Preview
        id: check_deployment
        uses: patrickedqvist/wait-for-vercel-preview@06c79330064b0e6ef7a2574603b62d3c98789125 # v1.3.2
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          max_timeout: 300 # timeout after 5 minutes
          check_interval: 10 # check every 10 seconds
        continue-on-error: true
      - name: Set Outputs
        if: always()
        id: set_outputs
        run: |
          if [[ -z "${{ steps.check_deployment.outputs.url }}" ]]; then
            echo "deployment_found=false" >> $GITHUB_OUTPUT
          else
            echo "deployment_found=true" >> $GITHUB_OUTPUT
            echo "url=${{ steps.check_deployment.outputs.url }}" >> $GITHUB_OUTPUT
          fi

  lighthouse-ci:
    needs: get-vercel-preview
    if: needs.get-vercel-preview.outputs.deployment_found == 'true'
    name: Lighthouse Report
    runs-on: ubuntu-latest

    steps:
      - name: Harden Runner
        uses: step-security/harden-runner@0634a2670c59f64b4a01f0f96f84700a4088b9f0 # v2.12.0
        with:
          egress-policy: audit

      - name: Git Checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          # Provides the Pull Request commit SHA or the GitHub merge group ref
          ref: ${{ github.event_name == 'pull_request_target' && github.event.pull_request.head.sha || github.ref }}

      - name: Add Comment to PR
        # Signal that a lighthouse run is about to start
        uses: thollander/actions-comment-pull-request@e2c37e53a7d2227b61585343765f73a9ca57eda9 # v3.0.0
        with:
          message: |
            Running Lighthouse audit...
          # Used later to edit the existing comment
          comment-tag: 'lighthouse_audit'

      - name: Audit Preview URL with Lighthouse
        # Conduct the lighthouse audit
        id: lighthouse_audit
        uses: treosh/lighthouse-ci-action@1b0e7c33270fbba31a18a0fbb1de7cc5256b6d39 # v11.4.0
        with:
          # Defines the settings and assertions to audit
          configPath: './.lighthouserc.json'
          # These URLS capture critical pages / site functionality.
          urls: |
            ${{ needs.get-vercel-preview.outputs.url }}/en
            ${{ needs.get-vercel-preview.outputs.url }}/en/about
            ${{ needs.get-vercel-preview.outputs.url }}/en/about/previous-releases
            ${{ needs.get-vercel-preview.outputs.url }}/en/download
            ${{ needs.get-vercel-preview.outputs.url }}/en/blog
          uploadArtifacts: true # save results as a action artifacts
          temporaryPublicStorage: true # upload lighthouse report to the temporary storage

      - name: Format Lighthouse Score
        # Transform the audit results into a single, friendlier output
        id: format_lighthouse_score
        uses: actions/github-script@60a0d83039c74a4aee543508d2ffcb1c3799cdea # v7.0.1
        env:
          # using env as input to our script
          # see https://github.com/actions/github-script#use-env-as-input
          LIGHTHOUSE_RESULT: ${{ steps.lighthouse_audit.outputs.manifest }}
          LIGHTHOUSE_LINKS: ${{ steps.lighthouse_audit.outputs.links }}
          VERCEL_PREVIEW_URL: ${{ needs.get-vercel-preview.outputs.url }}
        with:
          # Run as a separate file so we do not have to inline all of our formatting logic.
          # See https://github.com/actions/github-script#run-a-separate-file for more info.
          script: |
            const { formatLighthouseResults } = await import('${{github.workspace}}/apps/site/scripts/lighthouse/index.mjs')
            await formatLighthouseResults({core})

      - name: Add Comment to PR
        # Replace the previous message with our formatted lighthouse results
        uses: thollander/actions-comment-pull-request@e2c37e53a7d2227b61585343765f73a9ca57eda9 # v3.0.0
        with:
          # Reference the previously created comment
          comment-tag: 'lighthouse_audit'
          message: |
            ${{ steps.format_lighthouse_score.outputs.comment }}
