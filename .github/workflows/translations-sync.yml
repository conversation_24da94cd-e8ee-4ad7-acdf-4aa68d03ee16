# This action automates the synchronization of our crowdin translations, so that a human does not need to kick it off from the crowdin UI
# It also formats incoming content because it is often not adherent to our rules post-translation.

# See translations-upload.yml for automation to upload our source content
# See translations-pr-lint.yml for quality control we conduct on ingress of new translations.
name: Crowdin Download

on:
  workflow_dispatch: # Allow running when we want to, for events such as urgent translation mistakes or 100% completed languages
  schedule:
    - cron: '0 5 * * 5' # At 05:00 on Fridays. This guarantees that we have the 72 hour weekend time to review translations.

# Cancel any runs on the same branch
concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

permissions:
  contents: read

env:
  BRANCH_NAME: chore/crowdin

jobs:
  synchronize-with-crowdin:
    runs-on: ubuntu-latest
    steps:
      - name: Harden Runner
        uses: step-security/harden-runner@0634a2670c59f64b4a01f0f96f84700a4088b9f0 # v2.12.0
        with:
          egress-policy: audit

      - name: Checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          token: ${{ secrets.CROWDIN_GITHUB_BOT_TOKEN }}

        # see all the options at https://github.com/crowdin/github-action
      - name: Crowdin PR
        uses: crowdin/github-action@b8012bd5491b8aa8578b73ab5b5f5e7c94aaa6e2 # v2.7.0
        with:
          # do not upload anything - this is a one-way operation download
          upload_sources: false
          upload_translations: false
          # the rest of this controls how the PR comes in with new translations
          download_translations: true
          localization_branch_name: ${{ env.BRANCH_NAME }}
          create_pull_request: true
          pull_request_title: '[automated]: crowdin sync'
          pull_request_body: 'New Crowdin translations from the [Node.js Crowdin project](https://crowdin.com/project/nodejs-web)'
          commit_message: 'chore: synced translations from crowdin'
        env:
          GITHUB_TOKEN: ${{ secrets.CROWDIN_GITHUB_BOT_TOKEN }}
          # A numeric ID, found at https://crowdin.com/project/nodejs-web/tools/api
          CROWDIN_PROJECT_ID: ${{ secrets.CROWDIN_PROJECT_ID }}
          # Created from https://crowdin.com/settings#api-key logged in using nodejs-crowdin-bot
          CROWDIN_PERSONAL_TOKEN: ${{ secrets.CROWDIN_PERSONAL_TOKEN }}

  format_crowdin_pull_request:
    needs: synchronize-with-crowdin
    runs-on: ubuntu-latest

    permissions:
      # This permission is required by `stefanzweifel/git-auto-commit-action`
      contents: write

    steps:
      - name: Harden Runner
        uses: step-security/harden-runner@0634a2670c59f64b4a01f0f96f84700a4088b9f0 # v2.12.0
        with:
          egress-policy: audit

      - name: Git Checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          ref: ${{ env.BRANCH_NAME }}
          token: ${{ secrets.CROWDIN_GITHUB_BOT_TOKEN }}

      - name: Restore Lint Cache
        uses: actions/cache/restore@5a3ec84eff668545956fd18022155c47e93e2684 # v4.2.3
        with:
          path: |
            apps/site/.eslintmdcache
            apps/site/.prettiercache
          # We want to restore Turborepo Cache and ESlint and Prettier Cache
          # The ESLint and Prettier cache's are useful to reduce the overall runtime of ESLint and Prettier
          # as they will only run on files that have changed since the last cached run
          # this might of course lead to certain files not being checked against the linter, but the chances
          # of such situation from happening are very slim as the checksums of both files would need to match
          key: cache-lint-${{ hashFiles('pnpm-lock.yaml') }}-${{ hashFiles('apps/site/.eslintmdcache') }}
          restore-keys: |
            cache-lint-${{ hashFiles('pnpm-lock.yaml') }}-
            cache-lint-

      - name: Set up pnpm
        uses: pnpm/action-setup@a7487c7e89a18df4991f7f222e4898a00d66ddda # v4.1.0
        with:
          cache: true

      - name: Set up Node.js
        uses: actions/setup-node@49933ea5288caeca8642d1e84afbd3f7d6820020 # v4.4.0
        with:
          # We want to ensure that the Node.js version running here respects our supported versions
          node-version-file: '.nvmrc'
          cache: 'pnpm'

      - name: Install packages
        run: pnpm install --frozen-lockfile

      - name: Run ESLint
        working-directory: apps/site
        run: pnpm lint:md --fix

      - name: Run Prettier
        run: pnpm prettier:fix

      - name: Push Changes back to Pull Request
        uses: stefanzweifel/git-auto-commit-action@b863ae1933cb653a53c021fe36dbb774e1fb9403 # v5.2.0
        with:
          commit_options: '--no-verify --signoff'
          commit_message: 'chore: automated format of translated files'
          branch: 'chore/crowdin'

      - name: Save Lint Cache
        uses: actions/cache/save@5a3ec84eff668545956fd18022155c47e93e2684 # v4.2.3
        with:
          path: |
            apps/site/.eslintmdcache
            apps/site/.prettiercache
          key: cache-lint-${{ hashFiles('pnpm-lock.yaml') }}-${{ hashFiles('apps/site/.eslintmdcache') }}
