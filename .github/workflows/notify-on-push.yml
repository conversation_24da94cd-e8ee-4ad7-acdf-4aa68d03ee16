on:
  push:
    branches:
      - main

name: Notify on Push
permissions:
  contents: read

jobs:
  notify_on_push:
    name: Notify on any direct push to `main`
    if: >
      github.repository == 'nodejs/nodejs.org' && 
      github.actor != 'github-merge-queue[bot]'
    runs-on: ubuntu-latest
    steps:
      - name: Slack Notification
        uses: rtCamp/action-slack-notify@e31e87e03dd19038e411e38ae27cbad084a90661 # 2.3.3
        env:
          SLACK_COLOR: '#DE512A'
          SLACK_ICON: https://github.com/nodejs.png?size=48
          SLACK_TITLE: ${{ github.actor }} directly pushed to ${{ github.ref }}
          SLACK_MESSAGE: |
            A commit was directly pushed to <https://github.com/${{ github.repository }}/tree/${{ github.ref_name }}|${{ github.repository }}@${{ github.ref_name }}> by <https://github.com/${{ github.actor }}|${{ github.actor }}>

            Before: <https://github.com/${{ github.repository }}/commit/${{ github.event.before }}|${{ github.event.before }}>
            After: <https://github.com/${{ github.repository }}/commit/${{ github.event.after }}|${{ github.event.after }}>
          SLACK_USERNAME: nodejs-bot
          SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}
