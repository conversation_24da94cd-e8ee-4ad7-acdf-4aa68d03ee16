@reference "../styles/index.css";

.baseLayout {
  @apply grid
    h-max
    min-h-full
    w-full
    grid-cols-[1fr]
    grid-rows-[auto_1fr_auto];
}

.centeredLayout {
  @apply flex
    w-full
    min-w-0
    items-center
    justify-center
    px-4
    py-14
    md:px-14
    lg:px-28;

  main {
    @apply items-center
      justify-center;
  }
}

.homeLayout {
  @apply gap-8
    md:flex-row
    md:gap-14
    xl:gap-28
    2xl:gap-32;

  section {
    &:nth-of-type(1) {
      @apply flex
        max-w-[500px]
        flex-[1_0]
        flex-col
        gap-8;

      > div {
        @apply flex
          max-w-[400px]
          flex-col
          gap-4;

        p {
          @apply text-base
            md:text-lg;
        }

        small {
          @apply max-xs:text-xs
            text-center
            text-sm
            text-neutral-800
            dark:text-neutral-400;

          sup {
            @apply cursor-help;
          }
        }
      }
    }

    &:nth-of-type(2) {
      @apply flex
        min-w-0
        max-w-full
        flex-[1_1]
        flex-col
        items-center
        gap-4
        md:max-w-2xl
        lg:max-w-3xl;

      > div {
        @apply w-full
          max-w-md
          md:max-w-full;
      }

      > p {
        @apply text-center
          text-sm
          text-neutral-800
          dark:text-neutral-200;
      }
    }
  }
}

.blogLayout,
.downloadLayout {
  @apply bg-gradient-subtle
    dark:bg-gradient-subtle-dark
    max-xs:bg-none
    max-xs:dark:bg-none
    flex
    w-full
    justify-center;

  main {
    @apply max-w-5xl
      gap-4
      px-4
      py-12
      md:px-14
      lg:px-28;
  }
}

.downloadLayout {
  section:nth-last-child(1) {
    @apply flex
      flex-col
      gap-2;

    p {
      @apply text-sm;
    }
  }

  section:nth-last-child(2) p {
    @apply flex
      flex-row
      flex-wrap
      items-center
      gap-2
      text-base;
  }
}

.contentLayout {
  @apply max-w-8xl
    max-xs:m-0
    max-xs:block
    mx-auto
    grid
    w-full
    grid-rows-[1fr]
    sm:grid-cols-[1fr_theme(spacing.52)]
    xl:grid-cols-[1fr_theme(spacing.80)];

  > *:nth-child(1) {
    @apply bg-gradient-subtle
      dark:bg-gradient-subtle-dark
      max-xs:border-l-0
      max-xs:bg-none
      max-xs:pb-4
      max-xs:dark:bg-none
      flex
      w-full
      justify-center
      border-l
      border-l-neutral-200
      px-4
      py-14
      md:px-14
      lg:px-28
      dark:border-l-neutral-900;

    main {
      @apply max-w-[660px]
        gap-4
        [overflow-wrap:_anywhere];
    }
  }

  > *:nth-child(2) {
    @apply xs:mt-0
      xs:max-w-xs
      xs:border-l
      xs:border-t-0
      mt-8
      border-t;
  }
}

.postLayout {
  main {
    > section {
      @apply flex
        flex-row
        items-center
        gap-4;
    }

    > div:nth-of-type(1) {
      @apply mb-4
        mt-2;
    }
  }
}
