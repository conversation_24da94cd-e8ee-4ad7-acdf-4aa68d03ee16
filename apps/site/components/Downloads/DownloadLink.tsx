'use client';

import type { FC, PropsWithChildren } from 'react';

import Link<PERSON>ithArrow from '#site/components/LinkWithArrow';
import { useClientContext } from '#site/hooks';
import type { NodeRelease } from '#site/types';
import type { DownloadKind } from '#site/util/getNodeDownloadUrl';
import { getNodeDownloadUrl } from '#site/util/getNodeDownloadUrl';
import { getUserPlatform } from '#site/util/getUserPlatform';

type DownloadLinkProps = { release: NodeRelease; kind?: DownloadKind };

const DownloadLink: FC<PropsWithChildren<DownloadLinkProps>> = ({
  release: { versionWithPrefix },
  kind = 'installer',
  children,
}) => {
  const { os, bitness, architecture } = useClientContext();

  const platform = getUserPlatform(architecture, bitness);

  const downloadLink = getNodeDownloadUrl(
    versionWithPrefix,
    os,
    platform,
    kind
  );

  return <LinkWithArrow href={downloadLink}>{children}</LinkWithArrow>;
};

export default DownloadLink;
