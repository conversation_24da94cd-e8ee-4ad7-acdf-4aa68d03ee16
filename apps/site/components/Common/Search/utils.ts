export const themeConfig = {
  typography: {
    '--font-primary': 'var(--font-open-sans)',
  },
  colors: {
    light: {
      '--text-color-primary': 'var(--color-neutral-900)',
      '--text-color-accent': 'var(--color-green-600)',
      '--background-color-secondary': 'var(--color-neutral-100)',
      '--background-color-tertiary': 'var(--color-neutral-300)',
      '--border-color-accent': 'var(--color-green-600)',
      '--border-color-primary': 'var(--color-neutral-200)',
      '--border-color-tertiary': 'var(--color-green-700)',
      '--button-background-color-primary': 'var(--color-green-600)',
      '--button-background-color-secondary': 'var(--color-white)',
      '--button-background-color-secondary-hover': 'var(--color-neutral-100)',
      '--button-border-color-secondary': 'var(--color-neutral-300)',
      '--button-text-color-secondary': 'var(--color-neutral-900)',
      '--chat-button-border-color-gradientThree': 'var(--color-green-400)',
      '--chat-button-border-color-gradientFour': 'var(--color-green-700)',
      '--chat-button-background-color-gradientOne': 'var(--color-green-600)',
      '--chat-button-background-color-gradientTwo': 'var(--color-green-300)',
    },
    dark: {
      '--text-color-primary': 'var(--color-neutral-100)',
      '--text-color-accent': 'var(--color-green-400)',
      '--background-color-secondary': 'var(--color-neutral-950)',
      '--background-color-tertiary': 'var(--color-neutral-900)',
      '--border-color-accent': 'var(--color-green-400)',
      '--border-color-primary': 'var(--color-neutral-900)',
      '--border-color-tertiary': 'var(--color-green-300)',
      '--button-background-color-primary': 'var(--color-green-400)',
      '--button-background-color-secondary': 'var(--color-neutral-950)',
      '--button-background-color-secondary-hover': 'var(--color-neutral-900)',
      '--button-border-color-secondary': 'var(--color-neutral-900)',
      '--button-text-color-secondary': 'var(--color-neutral-200)',
      '--chat-button-border-color-gradientThree': 'var(--color-green-400)',
      '--chat-button-border-color-gradientFour': 'var(--color-green-700)',
      '--chat-button-background-color-gradientOne': 'var(--color-green-400)',
      '--chat-button-background-color-gradientTwo': 'var(--color-green-800)',
    },
  },
};
