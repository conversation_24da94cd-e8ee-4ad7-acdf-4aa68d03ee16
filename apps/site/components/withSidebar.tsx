'use client';

import Sidebar from '@node-core/ui-components/Containers/Sidebar';
import { usePathname } from 'next/navigation';
import { useLocale, useTranslations } from 'next-intl';
import type { RichTranslationValues } from 'next-intl';
import type { FC } from 'react';

import Link from '#site/components/Link';
import { useSiteNavigation } from '#site/hooks/server';
import { useRouter } from '#site/navigation.mjs';
import type { NavigationKeys } from '#site/types';

type WithSidebarProps = {
  navKeys: Array<NavigationKeys>;
  context?: Record<string, RichTranslationValues>;
};

const WithSidebar: FC<WithSidebarProps> = ({ navKeys, context }) => {
  const { getSideNavigation } = useSiteNavigation();
  const pathname = usePathname()!;
  const locale = useLocale();
  const t = useTranslations();
  const { push } = useRouter();

  const mappedSidebarItems = getSideNavigation(navKeys, context).map(
    ([, { label, items }]) => ({
      groupName: label,
      items: items.map(([, item]) => item),
    })
  );

  return (
    <Sidebar
      groups={mappedSidebarItems}
      pathname={pathname.replace(`/${locale}`, '')}
      title={t('components.common.sidebar.title')}
      onSelect={value => push(value)}
      as={Link}
    />
  );
};

export default WithSidebar;
