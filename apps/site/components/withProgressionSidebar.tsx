'use client';

import ProgressionSidebar from '@node-core/ui-components/Common/ProgressionSidebar';
import { usePathname } from 'next/navigation';
import { useLocale, useTranslations } from 'next-intl';
import type { RichTranslationValues } from 'next-intl';
import type { FC } from 'react';

import Link from '#site/components/Link';
import { useSiteNavigation } from '#site/hooks/server';
import { useRouter } from '#site/navigation.mjs';
import type { NavigationKeys } from '#site/types';

type WithProgressionSidebarProps = {
  navKey: NavigationKeys;
  context?: Record<string, RichTranslationValues>;
};

const WithProgressionSidebar: FC<WithProgressionSidebarProps> = ({
  navKey,
  context,
}) => {
  const { getSideNavigation } = useSiteNavigation();
  const pathname = usePathname();
  const locale = useLocale();
  const t = useTranslations();
  const { push } = useRouter();
  const [[, sidebarNavigation]] = getSideNavigation([navKey], context);

  const mappedProgressionSidebarItems = sidebarNavigation.items.map(
    ([, { label, items }]) => ({
      groupName: label,
      items: items.map(([, item]) => item),
    })
  );

  return (
    <ProgressionSidebar
      groups={mappedProgressionSidebarItems}
      pathname={pathname?.replace(`/${locale}`, '')}
      title={t('components.common.sidebar.title')}
      onSelect={push}
      as={Link}
    />
  );
};

export default WithProgressionSidebar;
