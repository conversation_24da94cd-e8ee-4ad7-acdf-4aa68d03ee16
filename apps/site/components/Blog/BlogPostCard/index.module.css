@reference "../../../styles/index.css";

.container {
  @apply max-w-full
    flex-1;
}

.subtitle {
  @apply mb-2
    mt-6
    inline-block
    text-xs
    font-semibold
    text-green-600
    dark:text-green-400;
}

.title {
  @apply mb-2
    block
    text-xl
    font-semibold
    text-neutral-900
    dark:text-white;
}

.description {
  @apply mb-6
    line-clamp-3
    text-sm
    text-neutral-800
    dark:text-neutral-200;
}

.footer {
  @apply flex
    gap-x-3;

  div:first-child {
    @apply overflow-visible;
  }
}

.author {
  p {
    @apply text-sm
      font-semibold
      text-neutral-900
      dark:text-white;
  }

  time {
    @apply text-sm
      text-neutral-800
      dark:text-neutral-200;
  }
}
