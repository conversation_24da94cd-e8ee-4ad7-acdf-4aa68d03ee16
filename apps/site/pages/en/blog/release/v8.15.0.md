---
date: '2018-12-26T16:30:33.048Z'
category: release
title: Node v8.15.0 (LTS)
layout: blog-post
author: <PERSON><PERSON>
---

The 8.14.0 security release introduced some unexpected breakages on the 8.x release line.
This is a special release to fix a regression in the HTTP binary upgrade response body and add
a missing CLI flag to adjust the max header size of the http parser.

### Notable Changes

- **cli**:
  - add --max-http-header-size flag (cjihrig) [#24811](https://github.com/nodejs/node/pull/24811)
- **http**:
  - add maxHeaderSize property (cjihrig) [#24860](https://github.com/nodejs/node/pull/24860)

### Commits

- [[`693e362175`](https://github.com/nodejs/node/commit/693e362175)] - **(SEMVER-MINOR)** **cli**: add --max-http-header-size flag (cjihrig) [#24811](https://github.com/nodejs/node/pull/24811)
- [[`4fb5a1be2f`](https://github.com/nodejs/node/commit/4fb5a1be2f)] - **(SEMVER-MINOR)** **deps**: cherry-pick http_parser_set_max_header_size (cjihrig) [#24811](https://github.com/nodejs/node/pull/24811)
- [[`446f8b54e5`](https://github.com/nodejs/node/commit/446f8b54e5)] - **(SEMVER-MINOR)** **http**: add maxHeaderSize property (cjihrig) [#24860](https://github.com/nodejs/node/pull/24860)
- [[`215ecfe4de`](https://github.com/nodejs/node/commit/215ecfe4de)] - **http**: fix regression of binary upgrade response body (Matteo Collina) [#25037](https://github.com/nodejs/node/pull/25037)
- [[`e1fbc26c6a`](https://github.com/nodejs/node/commit/e1fbc26c6a)] - **test**: move test-benchmark-path to sequential (Rich Trott) [#21393](https://github.com/nodejs/node/pull/21393)
- [[`aef71c05a2`](https://github.com/nodejs/node/commit/aef71c05a2)] - **test**: mark test-http2-settings-flood as flaky on Windows (Rich Trott) [#25048](https://github.com/nodejs/node/pull/25048)

Windows 32-bit Installer: https://nodejs.org/dist/v8.15.0/node-v8.15.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v8.15.0/node-v8.15.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v8.15.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v8.15.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v8.15.0/node-v8.15.0.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v8.15.0/node-v8.15.0-darwin-x64.tar.gz \
Linux 32-bit Binary: https://nodejs.org/dist/v8.15.0/node-v8.15.0-linux-x86.tar.xz \
Linux 64-bit Binary: https://nodejs.org/dist/v8.15.0/node-v8.15.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v8.15.0/node-v8.15.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v8.15.0/node-v8.15.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v8.15.0/node-v8.15.0-aix-ppc64.tar.gz \
SmartOS 32-bit Binary: https://nodejs.org/dist/v8.15.0/node-v8.15.0-sunos-x86.tar.xz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v8.15.0/node-v8.15.0-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v8.15.0/node-v8.15.0-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v8.15.0/node-v8.15.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v8.15.0/node-v8.15.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v8.15.0/node-v8.15.0.tar.gz \
Other release files: https://nodejs.org/dist/v8.15.0/ \
Documentation: https://nodejs.org/docs/v8.15.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

ef9db73a1c84129b0549db54299569eb308e5992a1459fe27f5c4c8c7184b382  node-v8.15.0-aix-ppc64.tar.gz
a393971136408f837fbc0f7d71a63754f91cfb1851d48bd612d8219eb61956f1  node-v8.15.0-darwin-x64.tar.gz
7a2662d95d5e89874035233035d56ec067e5798b512f8d0393e805a3d742bd58  node-v8.15.0-darwin-x64.tar.xz
5918d8f4da6c8aed5d27be1c5d318517fd175983ca3514592e3cdbf364d8d316  node-v8.15.0-headers.tar.gz
cb252b265e7f8ee795f0c83b39a4d27988838efa9f28acaf9f9bf906fbca01b7  node-v8.15.0-headers.tar.xz
02ce5c6551c0252c74b12c217d4e4f331147dc605990d6bbfb2fa247f356b5b0  node-v8.15.0-linux-arm64.tar.gz
5985c6dce65b1161ff41253da5aa4e64b8f10eb010a5d2712ea9b659f70179d7  node-v8.15.0-linux-arm64.tar.xz
a19b38d89f87c357569ee6ec99e767789a97c1a053e073676cfb0d68945d584e  node-v8.15.0-linux-armv7l.tar.gz
8cad5b17d90e7e3aaea6c723f0608de502738641e88c2e8406c93d7cd031ac54  node-v8.15.0-linux-armv7l.tar.xz
0a82cd81f13e59811c02dd12b7446fb2d5be86182dd9a6e96bf4fa32296a192a  node-v8.15.0-linux-ppc64le.tar.gz
c7ea47b9c16dc383d859d0abe201d25e25d90fedd64759ea22775eac39937690  node-v8.15.0-linux-ppc64le.tar.xz
c68bf544c3998cfa7803811e3c03ec74077a5a57c15ef487ff847c395c6a35fc  node-v8.15.0-linux-s390x.tar.gz
560620a4e061b94f2ff28bcc42582c15b23f1796ae5892f24daf32003f555740  node-v8.15.0-linux-s390x.tar.xz
dc004e5c0f39c6534232a73100c194bc1446f25e3a6a39b29e2000bb3d139d52  node-v8.15.0-linux-x64.tar.gz
c1f0c5facdba78b5dec5136aec40dcb00b5c7cf404d9236a99c955994f91d969  node-v8.15.0-linux-x64.tar.xz
cb20139da17c6805af5e3e04b4414c8f92cf3369cd1dd7aca3a58e1ad6b806a9  node-v8.15.0-linux-x86.tar.gz
c43e992e1967310091c8f700dbe537559849c7b5c8436f48aba161fa987645fc  node-v8.15.0-linux-x86.tar.xz
c260dd072480208cc4bd8df1a5a022e82d21d685ebdbbf1421a6ebe3c023a566  node-v8.15.0.pkg
ff5ed2508f073cef892992285a21608463054be059d25155618a94099a53df31  node-v8.15.0-sunos-x64.tar.gz
20b3e5798e51406d3d03c6199d4fac573ba58812bad379e30dd799319a63ce7f  node-v8.15.0-sunos-x64.tar.xz
08ed85feac66ae0e5cad2dfb78d85fa3ac1ef681eb4b0aeb885c08dc4fa9ded2  node-v8.15.0-sunos-x86.tar.gz
eed3d477213a5efd1831b1fb8d172f4b5addc0b94a256b10ef3ac37556f1d34d  node-v8.15.0-sunos-x86.tar.xz
590fc8b09c2466f8f7854ce3342ae1d0ba421c104999b7bd54ec0c690321d2aa  node-v8.15.0.tar.gz
968523333947cc3f769d73dedc6c9c60580826d8714bc0e62ca4589de6a7c633  node-v8.15.0.tar.xz
5317b304111f29c274a6c03375f65b9a2229397d0cd0152c79d32c24ede89f8d  node-v8.15.0-win-x64.7z
13d8eab29c191bd16c69a70a556178a5adc988b243a036aaf3d5158861b60d8e  node-v8.15.0-win-x64.zip
0a88b05dd281ce0af7b4cc5237cfd065f2ba54df700c9e3bff334832c7b24387  node-v8.15.0-win-x86.7z
2c9626cff11b476de178e9357ba26808dacbe26b26f34fd74adfb77c998ef022  node-v8.15.0-win-x86.zip
e672b938c8e32872d54352245bfe86dd088e05bb8598d633c487af7af0d3e4e4  node-v8.15.0-x64.msi
161f72c163df147448473d8326cd5f673503e8944ba70f7fd787f1dc5b5c723d  node-v8.15.0-x86.msi
f0cd491948c4a23fe93ea9db694ffff66e20298552f00268d009e25e96cd3482  win-x64/node.exe
069e96dfbe8a55825118705a5acaa941d9e6d4f1d529d89e8f88c20a647dccee  win-x64/node.lib
e6ea656be0c9c3fb86b77b3bdd5f16e9c39d8e55c4b6c4d0da572c0a17385530  win-x64/node_pdb.7z
326b66b69a531eacbcf0bb0c90dd5b540e9f1b2146f0ab1670ede97dde34766a  win-x64/node_pdb.zip
94ee364516cd3603b0673afee7f190a38439e797136fa425aa593ce2d0ef48c2  win-x86/node.exe
2fcc826d69e00d25cb3e373742398fb7d4c6c882fdf2463cb0a8a3ae99a6a9d5  win-x86/node.lib
8f80034574fcf60ffafe17f644ec709491fc6bdd6fde73c500648b7db11d4a12  win-x86/node_pdb.7z
5edeb2b2f748399b008baeefd54e160815f001a1f233176a81e122f5480e60cc  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQEzBAEBCAAdFiEEDv/hvO/ZyE49CYFSkzsB9AtcqUYFAlwjrCEACgkQkzsB9Atc
qUZ+nwf5AW3LgmyJtO1CeYW2o49Z703lG+9jSEgJbL6OYf7xJ0pjZ+uvkLvdHFxj
Ym9QDqIGwgdmIhDY0IZMLSjtJ3ROOcN5jTs/oXdS+xltsjdDWm+yo7oveTOKiMWg
GQx5/fTmvxt1+cA0S5YM0hSxpdKTxIaTGTEcU0uYE1SBqQhBnQo8budqt0qYctsu
bM6AonJcpMcpXf7ciw84laLf71J3+4uVzgNTitwG68uwfubN6/V3rBuvuiyX3qAY
soQ8SK0Xwzs9pFvgrH7O/aaOavy2/CZh6g6rmByDu1dXujjsKfCz9W70EHrPLi/J
WF9Ur2rWjoPtDPM+U2VdEglNMCRWhw==
=rmvT
-----END PGP SIGNATURE-----

```
