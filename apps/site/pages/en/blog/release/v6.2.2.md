---
date: '2016-06-17T15:53:06.031Z'
category: release
title: Node v6.2.2 (Current)
layout: blog-post
author: <PERSON>
---

### Notable changes

- **http**:
  - `req.read(0)` could cause incoming connections to stall and time out under certain conditions. (<PERSON><PERSON>) [#7211](https://github.com/nodejs/node/pull/7211)
  - When freeing the socket to be reused in keep-alive Agent wait for both prefinish and end events. Otherwise the next request may be written before the previous one has finished sending the body, leading to parser errors. (<PERSON><PERSON>) [#7149](https://github.com/nodejs/node/pull/7149)
- **npm**: upgrade npm to 3.9.5 (<PERSON>) [#7139](https://github.com/nodejs/node/pull/7139)

### Commits

- [[`d71ede8113`](https://github.com/nodejs/node/commit/d71ede8113)] - **benchmark**: don't convert arguments to numbers (<PERSON>) [#6570](https://github.com/nodejs/node/pull/6570)
- [[`32f76983e2`](https://github.com/nodejs/node/commit/32f76983e2)] - **benchmark**: increase http token check iterations (Brian White) [#6570](https://github.com/nodejs/node/pull/6570)
- [[`23a495a9a9`](https://github.com/nodejs/node/commit/23a495a9a9)] - **benchmark**: add benchmark for url.format() (Rich Trott) [#7250](https://github.com/nodejs/node/pull/7250)
- [[`27ed7fc56c`](https://github.com/nodejs/node/commit/27ed7fc56c)] - **benchmark**: fix child-process-exec-stdout on win (Bartosz Sosnowski) [#7178](https://github.com/nodejs/node/pull/7178)
- [[`5e5af8b4bb`](https://github.com/nodejs/node/commit/5e5af8b4bb)] - **benchmark**: fix child-process-read on Windows (Bartosz Sosnowski) [#6971](https://github.com/nodejs/node/pull/6971)
- [[`d24e4095bf`](https://github.com/nodejs/node/commit/d24e4095bf)] - **benchmark**: add benchmark for Buffer.concat (Anna Henningsen) [#7054](https://github.com/nodejs/node/pull/7054)
- [[`666b6f9302`](https://github.com/nodejs/node/commit/666b6f9302)] - **build**: add REPLACEME tag for version info in docs (Ben Noordhuis) [#6864](https://github.com/nodejs/node/pull/6864)
- [[`6d3d2d1ae4`](https://github.com/nodejs/node/commit/6d3d2d1ae4)] - **cluster**: don't send messages if no IPC channel (Santiago Gimeno) [#7132](https://github.com/nodejs/node/pull/7132)
- [[`068718c91c`](https://github.com/nodejs/node/commit/068718c91c)] - **debugger**: remove obsolete setTimeout (Rich Trott) [#7154](https://github.com/nodejs/node/pull/7154)
- [[`2961f06f6f`](https://github.com/nodejs/node/commit/2961f06f6f)] - **debugger**: fix --debug-brk interaction with -e (Rich Trott) [#7089](https://github.com/nodejs/node/pull/7089)
- [[`701e699d4f`](https://github.com/nodejs/node/commit/701e699d4f)] - **deps**: upgrade npm to 3.9.5 (Kat Marchán) [#7139](https://github.com/nodejs/node/pull/7139)
- [[`1095ae1ac5`](https://github.com/nodejs/node/commit/1095ae1ac5)] - **doc**: Add CII Best Practices badge to README.md (David A. Wheeler) [#6819](https://github.com/nodejs/node/pull/6819)
- [[`0198987b0d`](https://github.com/nodejs/node/commit/0198987b0d)] - **doc**: add internal link in GOVERNANCE.md (Rich Trott) [#7279](https://github.com/nodejs/node/pull/7279)
- [[`8e14f761bb`](https://github.com/nodejs/node/commit/8e14f761bb)] - **doc**: use `Buffer.byteLength` for Content-Length (kimown) [#7274](https://github.com/nodejs/node/pull/7274)
- [[`5d03bdd94f`](https://github.com/nodejs/node/commit/5d03bdd94f)] - **doc**: add information for IncomingMessage.destroy() (Rich Trott) [#7237](https://github.com/nodejs/node/pull/7237)
- [[`a113734099`](https://github.com/nodejs/node/commit/a113734099)] - **doc**: general improvements to path.md copy (James M Snell) [#7122](https://github.com/nodejs/node/pull/7122)
- [[`b5e44df9a3`](https://github.com/nodejs/node/commit/b5e44df9a3)] - **doc**: make pull request template more concise (Rich Trott) [#7239](https://github.com/nodejs/node/pull/7239)
- [[`40a5974a0e`](https://github.com/nodejs/node/commit/40a5974a0e)] - **doc**: `url.format()` parameter may be a string (Rich Trott) [#7235](https://github.com/nodejs/node/pull/7235)
- [[`a7d813915e`](https://github.com/nodejs/node/commit/a7d813915e)] - **doc**: clarify use of `0` port value (Rich Trott) [#7206](https://github.com/nodejs/node/pull/7206)
- [[`0fc8012b65`](https://github.com/nodejs/node/commit/0fc8012b65)] - **doc**: remove cluster.setupMaster() myth (cjihrig) [#7179](https://github.com/nodejs/node/pull/7179)
- [[`70167fd1d4`](https://github.com/nodejs/node/commit/70167fd1d4)] - **doc**: fix IRC link (Ilkka Myller) [#7210](https://github.com/nodejs/node/pull/7210)
- [[`4f2215fd98`](https://github.com/nodejs/node/commit/4f2215fd98)] - **doc**: fix minor nit introduced in readline.md (James M Snell) [#7198](https://github.com/nodejs/node/pull/7198)
- [[`d31f728e09`](https://github.com/nodejs/node/commit/d31f728e09)] - **doc**: clarify rl.question callback args (James M Snell) [#7022](https://github.com/nodejs/node/pull/7022)
- [[`70f2f357be`](https://github.com/nodejs/node/commit/70f2f357be)] - **doc**: general improvements to readline.md copy (James M Snell) [#7022](https://github.com/nodejs/node/pull/7022)
- [[`c2aba5ba27`](https://github.com/nodejs/node/commit/c2aba5ba27)] - **doc**: consolidate test/lint text in GH PR template (Rich Trott) [#7155](https://github.com/nodejs/node/pull/7155)
- [[`712120112f`](https://github.com/nodejs/node/commit/712120112f)] - **doc**: use consistent typography in streams.md (Rich Trott) [#6986](https://github.com/nodejs/node/pull/6986)
- [[`e2f6f8061b`](https://github.com/nodejs/node/commit/e2f6f8061b)] - **doc**: general improvements to process.md copy (James M Snell) [#7029](https://github.com/nodejs/node/pull/7029)
- [[`84ea6fc57c`](https://github.com/nodejs/node/commit/84ea6fc57c)] - **doc**: general improvements to repl.md copy (James M Snell) [#7002](https://github.com/nodejs/node/pull/7002)
- [[`bfb7e3cc6e`](https://github.com/nodejs/node/commit/bfb7e3cc6e)] - **doc**: add `added:` information for readline (Julian Duque) [#6996](https://github.com/nodejs/node/pull/6996)
- [[`632b411cd0`](https://github.com/nodejs/node/commit/632b411cd0)] - **doc**: improved syntax consistency in console.md (Jonathan Montane) [#7062](https://github.com/nodejs/node/pull/7062)
- [[`826bd99486`](https://github.com/nodejs/node/commit/826bd99486)] - **doc**: specify how to link issues in commit log (Luigi Pinca) [#7161](https://github.com/nodejs/node/pull/7161)
- [[`865644a604`](https://github.com/nodejs/node/commit/865644a604)] - **doc**: general improvements to querystring.md copy (James M Snell) [#7023](https://github.com/nodejs/node/pull/7023)
- [[`dd4c607267`](https://github.com/nodejs/node/commit/dd4c607267)] - **doc**: fix header depth of util.isSymbol (James M Snell) [#7138](https://github.com/nodejs/node/pull/7138)
- [[`5086e5f3ee`](https://github.com/nodejs/node/commit/5086e5f3ee)] - **doc**: general improvements to stream.md copy (James M Snell) [#6947](https://github.com/nodejs/node/pull/6947)
- [[`75d6875034`](https://github.com/nodejs/node/commit/75d6875034)] - **doc**: update licenses (Myles Borins) [#7121](https://github.com/nodejs/node/pull/7121)
- [[`dc8cb93c4f`](https://github.com/nodejs/node/commit/dc8cb93c4f)] - **doc**: add `added:` information for dns (Julian Duque) [#7021](https://github.com/nodejs/node/pull/7021)
- [[`a7c85e6fd5`](https://github.com/nodejs/node/commit/a7c85e6fd5)] - **doc**: add `added:` information for path (Julian Duque) [#6985](https://github.com/nodejs/node/pull/6985)
- [[`026bf17378`](https://github.com/nodejs/node/commit/026bf17378)] - **doc**: add `added` information for net (Italo A. Casas) [#7038](https://github.com/nodejs/node/pull/7038)
- [[`d4a2c82f5f`](https://github.com/nodejs/node/commit/d4a2c82f5f)] - **doc**: general improvements to punycode.md copy (James M Snell) [#7025](https://github.com/nodejs/node/pull/7025)
- [[`51d295efe6`](https://github.com/nodejs/node/commit/51d295efe6)] - **doc**: add links to platform specific mechanisms (Michael Dawson) [#7071](https://github.com/nodejs/node/pull/7071)
- [[`1600966f59`](https://github.com/nodejs/node/commit/1600966f59)] - **fs**: execute mkdtemp's callback with no context (Sakthipriyan Vairamani) [#7068](https://github.com/nodejs/node/pull/7068)
- [[`ad1045c829`](https://github.com/nodejs/node/commit/ad1045c829)] - **http**: fix no dumping after `maybeReadMore` (Fedor Indutny) [#7211](https://github.com/nodejs/node/pull/7211)
- [[`2a462ba1e2`](https://github.com/nodejs/node/commit/2a462ba1e2)] - **http**: optimize checkInvalidHeaderChar() (Brian White) [#6570](https://github.com/nodejs/node/pull/6570)
- [[`4a63be031f`](https://github.com/nodejs/node/commit/4a63be031f)] - **http**: optimize checkIsHttpToken() (Brian White) [#6570](https://github.com/nodejs/node/pull/6570)
- [[`40e49dee82`](https://github.com/nodejs/node/commit/40e49dee82)] - **http**: wait for both prefinish/end to keepalive (Fedor Indutny) [#7149](https://github.com/nodejs/node/pull/7149)
- [[`e8c91e7557`](https://github.com/nodejs/node/commit/e8c91e7557)] - **repl**: refine handling of illegal tokens (Rich Trott) [#7104](https://github.com/nodejs/node/pull/7104)
- [[`cf0928ccb7`](https://github.com/nodejs/node/commit/cf0928ccb7)] - **src**: clean up string_search (Brian White) [#7174](https://github.com/nodejs/node/pull/7174)
- [[`b0225e5926`](https://github.com/nodejs/node/commit/b0225e5926)] - **stream**: ensure awaitDrain is increased once (David Halls) [#7292](https://github.com/nodejs/node/pull/7292)
- [[`9c6b69ec1b`](https://github.com/nodejs/node/commit/9c6b69ec1b)] - **stream**: reset awaitDrain after manual .resume() (Anna Henningsen) [#7160](https://github.com/nodejs/node/pull/7160)
- [[`caa6718a01`](https://github.com/nodejs/node/commit/caa6718a01)] - **test**: fix test-net-\* error code check for getaddrinfo(3) (Natanael Copa) [#5099](https://github.com/nodejs/node/pull/5099)
- [[`535c8dd554`](https://github.com/nodejs/node/commit/535c8dd554)] - **test**: add more http token/value checking tests (Brian White) [#6570](https://github.com/nodejs/node/pull/6570)
- [[`257f4e6202`](https://github.com/nodejs/node/commit/257f4e6202)] - **test**: add note about duration_ms in TAP reporter (Rod Vagg) [#7216](https://github.com/nodejs/node/pull/7216)
- [[`798a737f45`](https://github.com/nodejs/node/commit/798a737f45)] - **_Revert_** "**test**: change duration_ms to duration" (Rod Vagg) [#7216](https://github.com/nodejs/node/pull/7216)
- [[`72e4e43b91`](https://github.com/nodejs/node/commit/72e4e43b91)] - **test**: rebuild add-ons when their sources change (Ben Noordhuis) [#7262](https://github.com/nodejs/node/pull/7262)
- [[`eded11705b`](https://github.com/nodejs/node/commit/eded11705b)] - **test**: use random ports where possible (Brian White) [#7045](https://github.com/nodejs/node/pull/7045)
- [[`d54c7c19a6`](https://github.com/nodejs/node/commit/d54c7c19a6)] - **test**: fix spawn on windows (Brian White) [#7049](https://github.com/nodejs/node/pull/7049)
- [[`e873063a3c`](https://github.com/nodejs/node/commit/e873063a3c)] - **test**: enable test-debug-brk-no-arg (Rich Trott) [#7143](https://github.com/nodejs/node/pull/7143)
- [[`d6091c8194`](https://github.com/nodejs/node/commit/d6091c8194)] - **test**: use common.fixturesDir almost everywhere (Bryan English) [#6997](https://github.com/nodejs/node/pull/6997)
- [[`e8b1456d8b`](https://github.com/nodejs/node/commit/e8b1456d8b)] - **test**: change duration_ms to duration (Gibson Fahnestock) [#7133](https://github.com/nodejs/node/pull/7133)
- [[`6ce26c8c8b`](https://github.com/nodejs/node/commit/6ce26c8c8b)] - **test**: add test for uid/gid setting in spawn (Rich Trott) [#7084](https://github.com/nodejs/node/pull/7084)
- [[`40604b54d4`](https://github.com/nodejs/node/commit/40604b54d4)] - **test**: remove disabled eio race test (Rich Trott) [#7083](https://github.com/nodejs/node/pull/7083)
- [[`9545c41cba`](https://github.com/nodejs/node/commit/9545c41cba)] - **tools**: fix license builder to work with icu-small (Myles Borins) [#7119](https://github.com/nodejs/node/pull/7119)
- [[`6562c9fc75`](https://github.com/nodejs/node/commit/6562c9fc75)] - **tools,doc**: add example usage for REPLACEME tag (Anna Henningsen) [#6864](https://github.com/nodejs/node/pull/6864)

Windows 32-bit Installer: https://nodejs.org/dist/v6.2.2/node-v6.2.2-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v6.2.2/node-v6.2.2-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v6.2.2/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v6.2.2/win-x64/node.exe \
Mac OS X 64-bit Installer: https://nodejs.org/dist/v6.2.2/node-v6.2.2.pkg \
Mac OS X 64-bit Binary: https://nodejs.org/dist/v6.2.2/node-v6.2.2-darwin-x64.tar.gz \
Linux 32-bit Binary: https://nodejs.org/dist/v6.2.2/node-v6.2.2-linux-x86.tar.xz \
Linux 64-bit Binary: https://nodejs.org/dist/v6.2.2/node-v6.2.2-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v6.2.2/node-v6.2.2-linux-ppc64le.tar.xz \
SmartOS 32-bit Binary: https://nodejs.org/dist/v6.2.2/node-v6.2.2-sunos-x86.tar.xz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v6.2.2/node-v6.2.2-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v6.2.2/node-v6.2.2-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v6.2.2/node-v6.2.2-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v6.2.2/node-v6.2.2-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v6.2.2/node-v6.2.2.tar.gz \
Other release files: https://nodejs.org/dist/v6.2.2/ \
Documentation: https://nodejs.org/docs/v6.2.2/api/

Shasums (GPG signing hash: SHA512, file hash: SHA256):

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA512

03b9eadd71d73daf2a25c8ea833454b326cb702f717a39f1b2a1324179cab5fa  node-v6.2.2-darwin-x64.tar.gz
1ef75ae9c3db01eafb0371e4c0e22889b1abd5f37f511569a72b64dcf39c93eb  node-v6.2.2-darwin-x64.tar.xz
c02d78470afb83d4473f196a5ff3725f028cd661c4d50d0337f817c7af02e0e7  node-v6.2.2-headers.tar.gz
2e84e6aa370c06544392a3724c4cbccad1846d854ec5dddba08ccb63d92854ab  node-v6.2.2-headers.tar.xz
1eaac04e632e633197c764a65817909667a700a657b1de463a45efcd40d236c7  node-v6.2.2-linux-arm64.tar.gz
02c5b6b54b036d0e7d7fea58c6a344be9cdb6d40e2c3bef76639827669d6e1e6  node-v6.2.2-linux-arm64.tar.xz
92b41e1c31a64024c5386df3cb6b9c0e00ecb097c2f7d0413364e20b7b836ae9  node-v6.2.2-linux-armv6l.tar.gz
3f6125c849aa7af16732e1e948e236a4ab31efaeb121cca1eb2e19e50396a07a  node-v6.2.2-linux-armv6l.tar.xz
ecaa5ccdad2d3e9efc8944e5c57971753bacbd7c171b6a4445e76e5fb9ebc69b  node-v6.2.2-linux-armv7l.tar.gz
03cc16e0839c65e36c34d8802d7c8e3110d60a53eef49584928314d2b8f18fb4  node-v6.2.2-linux-armv7l.tar.xz
b3e2e27ad4e52610fc971ef7c32cfb7c3f9db03a0b49e8bd422383ff30197263  node-v6.2.2-linux-ppc64le.tar.gz
282b60f64ee8793c0d8a2f8849bdd13130a1a296d54ab947ae527d7bc2eb8b10  node-v6.2.2-linux-ppc64le.tar.xz
ff307b8602808e799cef488c55f1fe646cd169c44464c4c53b56e61d181d06a6  node-v6.2.2-linux-ppc64.tar.gz
cbb956214dd864725aaa5cd6d507faf520886b61fa66fb6f25fb7a4b9a579883  node-v6.2.2-linux-ppc64.tar.xz
7a6df881183e70839857b51653811aaabc49a2ffb93416a1c9bd333dcef84ea3  node-v6.2.2-linux-x64.tar.gz
dd8231b89c57c51b298563fd5e9c594d3ced9cb5f3153dcc05d55444b5d0dc4b  node-v6.2.2-linux-x64.tar.xz
71a3ec010acaa3c5d26429b6670f4895cd494199ffa07b1773e63619559c32c1  node-v6.2.2-linux-x86.tar.gz
c00150ed1e4971394136d4ff086fb726f592b0615fa8f7aa64d14d2e338be2b8  node-v6.2.2-linux-x86.tar.xz
122c74d5864f99530638f35652129c8b2ba3163317a7c404e1a058f97ceba7d4  node-v6.2.2.pkg
d4a4c601ee6ce311193c1445ca2efbad75a6adbe407ce787381c4b620ac62eeb  node-v6.2.2-sunos-x64.tar.gz
4ada036c9b5f5c1b73e305e1bb05f8d31f44f2535495f26f24666b3d8eb9cd52  node-v6.2.2-sunos-x64.tar.xz
94fecfccdc903d0df685850fa8a34c28528e79634bea7569ca7b570a39d2a7ce  node-v6.2.2-sunos-x86.tar.gz
dab2c5cabd9ac7a6bf79fdb902cdb862ea0757799d4dc189d483568418697af3  node-v6.2.2-sunos-x86.tar.xz
b6baee57a0ede496c7c7765001f7495ad74c8dfe8c34f1a6fb2cd5d8d526ffce  node-v6.2.2.tar.gz
2dfeeddba750b52a528b38a1c31e35c1fb40b19cf28fbf430c3c8c7a6517005a  node-v6.2.2.tar.xz
33fffbe79ed5ec422f8be12f7f87f616e98b630ed40f10cfe84e373122c04589  node-v6.2.2-win-x64.7z
df66df9c3c1e069cb5a59d5077dca54af4ccf48fb13d1e85eb1aa72297f9952b  node-v6.2.2-win-x64.zip
db2e898eea4204a247827e288311b05f93f57c6c08f29e6b415b1301b4fce9ca  node-v6.2.2-win-x86.7z
77ad948abdb861bbe23259a18adf77dac094847f66ded0cd9e70871336eb3935  node-v6.2.2-win-x86.zip
d7e9f474de0605addb6bbb1c5d01b45de88b704f1d72e8f026171baa7cbf75d1  node-v6.2.2-x64.msi
2c186a625473796c2fd70948fa85c8a1e087033fbf25ae16866e2f2f347f0e38  node-v6.2.2-x86.msi
b900e6c16576bf389c034580678c10fed1970b640456706182d028697b7a0c1f  win-x64/node.exe
4d4417c20bad483c9a8a7a3ee49278c4ad4b608efdfa4c6d9db671d608ac5ee5  win-x64/node.lib
973c7a6c114ee6d43ffc973764c35ad7af7a5f811172b2b6f36820de727bb6b7  win-x64/node_pdb.7z
06e0a8e5625186a430fb55a09655f47d574a27f886fd5ac83f4ee2a0abd5eb7c  win-x64/node_pdb.zip
9cb8c4f4c427ab6cf73ce38523bfaa2d094bbe45d614805b2d34dc5078f0b821  win-x86/node.exe
f0cc06d127ee17de4213315ba96276dffdcb16708a5b432770f49eaf63007e9e  win-x86/node.lib
0257c8d092b65410f2f75d065284894f0667b68effe873072afb679873d0d323  win-x86/node_pdb.7z
d950aee2eeb69c666e0391b2c692728e69ec2dd2e58a6d38b11e8303c74476dd  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----
Comment: GPGTools - https://gpgtools.org

iQIcBAEBCgAGBQJXZBwgAAoJELY7U1pMIGyp/UUQANLiIQthoyGS7tAlN4ehlyPF
kaKNchNhEMBI8uD0Sd//wtasXOmcxXnpwBcmZ96horx7VnJeyqYRx9/+GQUJ7XhE
X4AtJcCxWqpuzc7FIL01AfK1L8LydHHUb7NxFbqy2VtvRG/rHk4EXsKtYnnwHlQV
k04902E9XC236ED2gIBtrmyWJoYuxTuhuNnv/VXErm7y2gQmWak5FkdVL1sQD2Wg
dkdvZ2EpV8Gato1tAMLwoOoGdDMBloltEbp7EFlxCb1h0Fr5Pi+PQXv9bQebevAy
7cLleiodYHVSWcDfvWYHlss99MZZMbdNh7DGUv0DtC2AkdlebyUMYXe4npQfNzA1
6QeAXjg43RoaINx9uLEmA9wI5Po1Uj/j0wIom7mtfl8KAAUhHdcdSspnqbM2Jlp2
OdX4rnv9yKI+s655mbO7JkHyc8REiFl0qwOc6kIuikr17SpPj4jm7hyCEI1FiBKh
302xwERedltNkPk2YgifvNfiSbbN95q0a+hpQi3g2+r369RSFnjAkiltRLrylkyy
vQWdWyVf+H6BR0nQ9Sst28Xr68ys+1O+IFnXnorbZU98wSTBJQ9x10h8u1OChPzZ
+gZQfwUK6QEmOkALmFliqAUm7B7zRgm9jLrUrAfbjJmYv6qC+gPnf/MlnBQ099ho
vqiNTA+C9QDmER2PoS4r
=U+qh
-----END PGP SIGNATURE-----

```
