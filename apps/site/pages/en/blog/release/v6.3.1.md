---
date: '2016-07-21T22:41:46.632Z'
category: release
title: Node v6.3.1 (Current)
layout: blog-post
author: <PERSON>
---

### Notable changes

- **buffer**:
  - Improve performance of <PERSON><PERSON><PERSON>.from(str, 'hex') and <PERSON><PERSON><PERSON>#write(str, 'hex'). (<PERSON>) [#7602](https://github.com/nodejs/node/pull/7602)
  - Fix creating from zero-length ArrayBuffer. (<PERSON><PERSON><PERSON>) [#7176](https://github.com/nodejs/node/pull/7176)
- **deps**:
  - Upgrade to V8 5.0.71.xx. (<PERSON>) [#7531](https://github.com/nodejs/node/pull/7531)
  - Backport V8 instanceof bugfix (<PERSON><PERSON><PERSON>) [#7638](https://github.com/nodejs/node/pull/7638)
- **repl**: Fix issue with function redeclaration. (<PERSON>) [#7794](https://github.com/nodejs/node/pull/7794)
- **util**: Fix inspecting of boxed symbols. (<PERSON>) [#7641](https://github.com/nodejs/node/pull/7641)

### Commits

- [[`3747d910ec`](https://github.com/nodejs/node/commit/3747d910ec)] - **benchmark**: remove unused variables (Rich Trott) [#7600](https://github.com/nodejs/node/pull/7600)
- [[`41582722c8`](https://github.com/nodejs/node/commit/41582722c8)] - **buffer**: optimize hex_decode (Christopher Jeffrey) [#7602](https://github.com/nodejs/node/pull/7602)
- [[`4a3300e66b`](https://github.com/nodejs/node/commit/4a3300e66b)] - **buffer**: fix creating from zero-length ArrayBuffer (Ingvar Stepanyan) [#7176](https://github.com/nodejs/node/pull/7176)
- [[`71f84b5e6c`](https://github.com/nodejs/node/commit/71f84b5e6c)] - **build**: add conflict marker check during CI lint (Brian White) [#7625](https://github.com/nodejs/node/pull/7625)
- [[`4480b14fda`](https://github.com/nodejs/node/commit/4480b14fda)] - **build**: use BUILDTYPE when building V8 in Makefile (Michaël Zasso) [#7482](https://github.com/nodejs/node/pull/7482)
- [[`94a486a388`](https://github.com/nodejs/node/commit/94a486a388)] - **build**: add v8 requirement to test-v8\* in Makefile (Michaël Zasso) [#7482](https://github.com/nodejs/node/pull/7482)
- [[`e5627278f1`](https://github.com/nodejs/node/commit/e5627278f1)] - **build**: add --enable-d8 configure option (Ben Noordhuis) [#7538](https://github.com/nodejs/node/pull/7538)
- [[`933ff62fa5`](https://github.com/nodejs/node/commit/933ff62fa5)] - **build**: respect --shared-\* flags for inspector deps (Сковорода Никита Андреевич) [#7569](https://github.com/nodejs/node/pull/7569)
- [[`9bb1024dc3`](https://github.com/nodejs/node/commit/9bb1024dc3)] - **child_process**: Check stderr before accessing it (Robert Chiras) [#6877](https://github.com/nodejs/node/pull/6877)
- [[`f574bd4cec`](https://github.com/nodejs/node/commit/f574bd4cec)] - **cluster**: remove bind() and self (cjihrig) [#7710](https://github.com/nodejs/node/pull/7710)
- [[`164981af5f`](https://github.com/nodejs/node/commit/164981af5f)] - **deps**: bump V8 patchlevel for instanceof cherry-picks (Franziska Hinkelmann) [#7638](https://github.com/nodejs/node/pull/7638)
- [[`287006149b`](https://github.com/nodejs/node/commit/287006149b)] - **deps**: cherry-pick 5b5d24b for X87 from V8 upstream (Franziska Hinkelmann) [#7638](https://github.com/nodejs/node/pull/7638)
- [[`e5cce7acfe`](https://github.com/nodejs/node/commit/e5cce7acfe)] - **deps**: cherry-pick 3a903c4 for PPC from V8 upstream (Franziska Hinkelmann) [#7638](https://github.com/nodejs/node/pull/7638)
- [[`e23904523f`](https://github.com/nodejs/node/commit/e23904523f)] - **deps**: cherry-pick 2aa070be from V8 upstream (Franziska Hinkelmann) [#7638](https://github.com/nodejs/node/pull/7638)
- [[`d3f0a6a52f`](https://github.com/nodejs/node/commit/d3f0a6a52f)] - **deps**: cherry-pick 1f53e42 from v8 upstream (Ben Noordhuis) [#7612](https://github.com/nodejs/node/pull/7612)
- [[`cf8a4889db`](https://github.com/nodejs/node/commit/cf8a4889db)] - **deps**: v8_inspector no longer depends on wtf (Ali Ijaz Sheikh) [#7751](https://github.com/nodejs/node/pull/7751)
- [[`939cf6ddb2`](https://github.com/nodejs/node/commit/939cf6ddb2)] - **deps**: no /safeseh for ml64.exe (Fedor Indutny) [#7759](https://github.com/nodejs/node/pull/7759)
- [[`abf86adee1`](https://github.com/nodejs/node/commit/abf86adee1)] - **deps**: back-port d721121 from v8 upstream (Ben Noordhuis) [#7633](https://github.com/nodejs/node/pull/7633)
- [[`dbdcded866`](https://github.com/nodejs/node/commit/dbdcded866)] - **deps**: upgrade to V8 ********* (Ben Noordhuis) [#7531](https://github.com/nodejs/node/pull/7531)
- [[`4839ef37a9`](https://github.com/nodejs/node/commit/4839ef37a9)] - **doc**: correcting misspelling (Vitaly Tomilov) [#7797](https://github.com/nodejs/node/pull/7797)
- [[`3343d46f2c`](https://github.com/nodejs/node/commit/3343d46f2c)] - **doc**: general improvments to events documentation (Sakthipriyan Vairamani) [#7480](https://github.com/nodejs/node/pull/7480)
- [[`e8a6a223ec`](https://github.com/nodejs/node/commit/e8a6a223ec)] - **doc**: update readme with andrasq as a collaborator (Andras) [#7801](https://github.com/nodejs/node/pull/7801)
- [[`59ed303612`](https://github.com/nodejs/node/commit/59ed303612)] - **doc**: update CTC governance information (Rich Trott) [#7719](https://github.com/nodejs/node/pull/7719)
- [[`4b320adb49`](https://github.com/nodejs/node/commit/4b320adb49)] - **doc**: correct sample output of buf.compare (Hargobind S. Khalsa) [#7777](https://github.com/nodejs/node/pull/7777)
- [[`9847f8459c`](https://github.com/nodejs/node/commit/9847f8459c)] - **doc**: add `added:` information for stream (Italo A. Casas) [#7287](https://github.com/nodejs/node/pull/7287)
- [[`1f003590d6`](https://github.com/nodejs/node/commit/1f003590d6)] - **doc**: fix inconsistencies in code style (saadq) [#7745](https://github.com/nodejs/node/pull/7745)
- [[`9c274e32fd`](https://github.com/nodejs/node/commit/9c274e32fd)] - **doc**: Warn against `uncaughtException` dependency. (Lance Ball) [#6378](https://github.com/nodejs/node/pull/6378)
- [[`fc4df0df6c`](https://github.com/nodejs/node/commit/fc4df0df6c)] - **doc**: fix typo in stream doc (Kevin Donahue) [#7738](https://github.com/nodejs/node/pull/7738)
- [[`2a023bfd00`](https://github.com/nodejs/node/commit/2a023bfd00)] - **doc**: removed old git conflict markers from fs.md (Jaime Hidalgo García) [#7590](https://github.com/nodejs/node/pull/7590)
- [[`1d07d29bfe`](https://github.com/nodejs/node/commit/1d07d29bfe)] - **doc**: fix typo in the CHANGELOG_V6 (vsemozhetbyt) [#7568](https://github.com/nodejs/node/pull/7568)
- [[`f15d2d6dae`](https://github.com/nodejs/node/commit/f15d2d6dae)] - **doc**: fix util.deprecate() example (Evan Lucas) [#7674](https://github.com/nodejs/node/pull/7674)
- [[`58b70d34ee`](https://github.com/nodejs/node/commit/58b70d34ee)] - **doc**: link and highlight Object.assign (Sakthipriyan Vairamani) [#7670](https://github.com/nodejs/node/pull/7670)
- [[`cc7fdf429e`](https://github.com/nodejs/node/commit/cc7fdf429e)] - **doc**: grammar fixes to event loop guide (Ryan Lewis) [#7479](https://github.com/nodejs/node/pull/7479)
- [[`a81ff702cc`](https://github.com/nodejs/node/commit/a81ff702cc)] - **doc**: dns.resolve fix callback argument description (Quentin Headen) [#7532](https://github.com/nodejs/node/pull/7532)
- [[`f0c335c347`](https://github.com/nodejs/node/commit/f0c335c347)] - **doc**: add benchmark who-to-CC info (Rich Trott) [#7604](https://github.com/nodejs/node/pull/7604)
- [[`9e0cba0552`](https://github.com/nodejs/node/commit/9e0cba0552)] - **doc**: added information on how to run the linter. (Diosney Sarmiento) [#7534](https://github.com/nodejs/node/pull/7534)
- [[`e13ee29cbd`](https://github.com/nodejs/node/commit/e13ee29cbd)] - **doc**: delete non-existing zlib constants (Franziska Hinkelmann) [#7520](https://github.com/nodejs/node/pull/7520)
- [[`663b103bc5`](https://github.com/nodejs/node/commit/663b103bc5)] - **doc**: fix minor style issues in http.md (Rich Trott) [#7528](https://github.com/nodejs/node/pull/7528)
- [[`6c4d4596cc`](https://github.com/nodejs/node/commit/6c4d4596cc)] - **doc**: updating REPLACEME tag during release (Gibson Fahnestock) [#7514](https://github.com/nodejs/node/pull/7514)
- [[`b4547340ee`](https://github.com/nodejs/node/commit/b4547340ee)] - **doc**: fix detached child stdio example (cjihrig) [#7540](https://github.com/nodejs/node/pull/7540)
- [[`0f7b4efaaf`](https://github.com/nodejs/node/commit/0f7b4efaaf)] - **doc**: add bartosz sosnowski to colaborators (Bartosz Sosnowski) [#7567](https://github.com/nodejs/node/pull/7567)
- [[`77afeb2ec7`](https://github.com/nodejs/node/commit/77afeb2ec7)] - **doc,dgram**: fix addMembership documentation (Santiago Gimeno) [#7244](https://github.com/nodejs/node/pull/7244)
- [[`11d6f1af59`](https://github.com/nodejs/node/commit/11d6f1af59)] - **fs**: rename event to eventType in fs.watch listener (Claudio Rodriguez) [#7506](https://github.com/nodejs/node/pull/7506)
- [[`989a2a1c92`](https://github.com/nodejs/node/commit/989a2a1c92)] - **inspector**: Unify event queues (Eugene Ostroukhov) [#7271](https://github.com/nodejs/node/pull/7271)
- [[`fc0ed2e8c7`](https://github.com/nodejs/node/commit/fc0ed2e8c7)] - **lib,benchmark,test**: implement consistent braces (Rich Trott) [#7630](https://github.com/nodejs/node/pull/7630)
- [[`80ca0630a6`](https://github.com/nodejs/node/commit/80ca0630a6)] - **net**: export isIPv4, isIPv6 directly from cares (Sakthipriyan Vairamani) [#7481](https://github.com/nodejs/node/pull/7481)
- [[`72fc4ebca2`](https://github.com/nodejs/node/commit/72fc4ebca2)] - **repl**: Mitigate vm #548 function redefinition issue (Prince J Wesley) [#7794](https://github.com/nodejs/node/pull/7794)
- [[`f97aa4be39`](https://github.com/nodejs/node/commit/f97aa4be39)] - **src**: remove unnecessary HandleScopes (Ben Noordhuis) [#7711](https://github.com/nodejs/node/pull/7711)
- [[`78dcf0d641`](https://github.com/nodejs/node/commit/78dcf0d641)] - **src**: fix handle leak in UDPWrap::Instantiate() (Ben Noordhuis) [#7711](https://github.com/nodejs/node/pull/7711)
- [[`dc766e6a6f`](https://github.com/nodejs/node/commit/dc766e6a6f)] - **src**: fix handle leak in BuildStatsObject() (Ben Noordhuis) [#7711](https://github.com/nodejs/node/pull/7711)
- [[`96882e14d1`](https://github.com/nodejs/node/commit/96882e14d1)] - **src**: fix handle leak in Buffer::New() (Ben Noordhuis) [#7711](https://github.com/nodejs/node/pull/7711)
- [[`fbc9ef84b8`](https://github.com/nodejs/node/commit/fbc9ef84b8)] - **src**: disable stdio buffering (Ben Noordhuis) [#7610](https://github.com/nodejs/node/pull/7610)
- [[`44c9a72aad`](https://github.com/nodejs/node/commit/44c9a72aad)] - **test**: add regression test for instanceof (Franziska Hinkelmann) [#7638](https://github.com/nodejs/node/pull/7638)
- [[`2e05e65916`](https://github.com/nodejs/node/commit/2e05e65916)] - **test**: add known issue test for #7788 (cjihrig) [#7793](https://github.com/nodejs/node/pull/7793)
- [[`7fb4794e19`](https://github.com/nodejs/node/commit/7fb4794e19)] - **test**: increase RAM requirement for intensive tests (Rich Trott) [#7772](https://github.com/nodejs/node/pull/7772)
- [[`61542e82c1`](https://github.com/nodejs/node/commit/61542e82c1)] - **test**: ensure callback runs in test-vm-sigint (Rich Trott) [#7768](https://github.com/nodejs/node/pull/7768)
- [[`9e9d499b8b`](https://github.com/nodejs/node/commit/9e9d499b8b)] - **test**: use mustCall() for simple flow tracking (cjihrig) [#7753](https://github.com/nodejs/node/pull/7753)
- [[`83cbf3175c`](https://github.com/nodejs/node/commit/83cbf3175c)] - **test**: avoid usage of mixed IPV6 addresses (Gireesh Punathil) [#7702](https://github.com/nodejs/node/pull/7702)
- [[`39f5d9ca7a`](https://github.com/nodejs/node/commit/39f5d9ca7a)] - **test**: fix flaky test-http-server-consumed-timeout (Rich Trott) [#7717](https://github.com/nodejs/node/pull/7717)
- [[`3ed0204f23`](https://github.com/nodejs/node/commit/3ed0204f23)] - **test**: s/assert.fail/common.fail as appropriate (cjihrig) [#7735](https://github.com/nodejs/node/pull/7735)
- [[`f7651d24d4`](https://github.com/nodejs/node/commit/f7651d24d4)] - **test**: improve error message in test-tick-processor (Rich Trott) [#7693](https://github.com/nodejs/node/pull/7693)
- [[`acb976ac26`](https://github.com/nodejs/node/commit/acb976ac26)] - **test**: cleanup IIFE tests (cjihrig) [#7694](https://github.com/nodejs/node/pull/7694)
- [[`432cb353e1`](https://github.com/nodejs/node/commit/432cb353e1)] - **test**: add common.rootDir (cjihrig) [#7685](https://github.com/nodejs/node/pull/7685)
- [[`9797969ad4`](https://github.com/nodejs/node/commit/9797969ad4)] - **test**: fix old tty tests (Jeremiah Senkpiel) [#7613](https://github.com/nodejs/node/pull/7613)
- [[`37dc7954d8`](https://github.com/nodejs/node/commit/37dc7954d8)] - **test**: move parallel/test-tty-\* to pseudo-tty/ (Jeremiah Senkpiel) [#7613](https://github.com/nodejs/node/pull/7613)
- [[`5192bed68c`](https://github.com/nodejs/node/commit/5192bed68c)] - **test**: remove unused var from child-process-fork (Rich Trott) [#7599](https://github.com/nodejs/node/pull/7599)
- [[`e1aedbf671`](https://github.com/nodejs/node/commit/e1aedbf671)] - **test**: remove unused vars from http/https tests (Rich Trott) [#7598](https://github.com/nodejs/node/pull/7598)
- [[`64e2eed662`](https://github.com/nodejs/node/commit/64e2eed662)] - **test**: remove unused var in test-tls-server-verify (Rich Trott) [#7595](https://github.com/nodejs/node/pull/7595)
- [[`8e50413b7e`](https://github.com/nodejs/node/commit/8e50413b7e)] - **test**: fix flaky test-fs-read-buffer-tostring-fail (Rich Trott) [#7575](https://github.com/nodejs/node/pull/7575)
- [[`447a8f26e1`](https://github.com/nodejs/node/commit/447a8f26e1)] - **test**: remove unused var in net-server-try-ports (Rich Trott) [#7597](https://github.com/nodejs/node/pull/7597)
- [[`326006527d`](https://github.com/nodejs/node/commit/326006527d)] - **test**: remove unused var from stream2 test (Rich Trott) [#7596](https://github.com/nodejs/node/pull/7596)
- [[`97167291e7`](https://github.com/nodejs/node/commit/97167291e7)] - **test**: fix flaky test-net-write-slow (Rich Trott) [#7555](https://github.com/nodejs/node/pull/7555)
- [[`657fd7aee9`](https://github.com/nodejs/node/commit/657fd7aee9)] - **test**: skip doctool tests when js-yaml is missing (Anna Henningsen) [#7218](https://github.com/nodejs/node/pull/7218)
- [[`1576730ef3`](https://github.com/nodejs/node/commit/1576730ef3)] - **test,doc**: clarify `buf.indexOf(num)` input range (Anna Henningsen) [#7611](https://github.com/nodejs/node/pull/7611)
- [[`49a6ea1b73`](https://github.com/nodejs/node/commit/49a6ea1b73)] - **timers**: fix processing of nested timers (Jeremy Whitlock) [#3063](https://github.com/nodejs/node/pull/3063)
- [[`5a2ce3633f`](https://github.com/nodejs/node/commit/5a2ce3633f)] - **tools**: consistent .eslintrc formatting (silverwind) [#7691](https://github.com/nodejs/node/pull/7691)
- [[`2a84da5d09`](https://github.com/nodejs/node/commit/2a84da5d09)] - **tools**: increase lint coverage (Rich Trott) [#7647](https://github.com/nodejs/node/pull/7647)
- [[`a82573d480`](https://github.com/nodejs/node/commit/a82573d480)] - **tools**: enforce JS brace style with linting (Rich Trott) [#7630](https://github.com/nodejs/node/pull/7630)
- [[`8efca46e78`](https://github.com/nodejs/node/commit/8efca46e78)] - **tools**: fix broken format string (Sakthipriyan Vairamani) [#7620](https://github.com/nodejs/node/pull/7620)
- [[`2bef583f8a`](https://github.com/nodejs/node/commit/2bef583f8a)] - **tools**: cleanup no-build and build-only options (Sakthipriyan Vairamani) [#7620](https://github.com/nodejs/node/pull/7620)
- [[`df697c486e`](https://github.com/nodejs/node/commit/df697c486e)] - **tools**: update ESLint, fix unused vars bug (Rich Trott) [#7601](https://github.com/nodejs/node/pull/7601)
- [[`1a360d63db`](https://github.com/nodejs/node/commit/1a360d63db)] - **tools**: remove unused variable (Rich Trott) [#7594](https://github.com/nodejs/node/pull/7594)
- [[`fa99dadda4`](https://github.com/nodejs/node/commit/fa99dadda4)] - **tools**: remove unnecessary imports and assignments (Sakthipriyan Vairamani) [#7483](https://github.com/nodejs/node/pull/7483)
- [[`0858e620e9`](https://github.com/nodejs/node/commit/0858e620e9)] - **util**: inspect boxed symbols like other primitives (Anna Henningsen) [#7641](https://github.com/nodejs/node/pull/7641)

Windows 32-bit Installer: https://nodejs.org/dist/v6.3.1/node-v6.3.1-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v6.3.1/node-v6.3.1-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v6.3.1/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v6.3.1/win-x64/node.exe \
Mac OS X 64-bit Installer: https://nodejs.org/dist/v6.3.1/node-v6.3.1.pkg \
Mac OS X 64-bit Binary: https://nodejs.org/dist/v6.3.1/node-v6.3.1-darwin-x64.tar.gz \
Linux 32-bit Binary: https://nodejs.org/dist/v6.3.1/node-v6.3.1-linux-x86.tar.xz \
Linux 64-bit Binary: https://nodejs.org/dist/v6.3.1/node-v6.3.1-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v6.3.1/node-v6.3.1-linux-ppc64le.tar.xz \
SmartOS 32-bit Binary: https://nodejs.org/dist/v6.3.1/node-v6.3.1-sunos-x86.tar.xz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v6.3.1/node-v6.3.1-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v6.3.1/node-v6.3.1-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v6.3.1/node-v6.3.1-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v6.3.1/node-v6.3.1-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v6.3.1/node-v6.3.1.tar.gz \
Other release files: https://nodejs.org/dist/v6.3.1/ \
Documentation: https://nodejs.org/docs/v6.3.1/api/

Shasums (GPG signing hash: SHA512, file hash: SHA256):

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA512

de6d45f63ab281b7454977d8dbf5494015e63a1cd9c9d8fe6f67e2431684f34f  node-v6.3.1-darwin-x64.tar.gz
5a4e891e6ade214915f4907223955ba37ceff385bd650e8031a1f32fe589df32  node-v6.3.1-darwin-x64.tar.xz
2d7a69f69e9d45453fed9ad68302edf4969e64b870b2e40f9b3d5f98ed44587c  node-v6.3.1-headers.tar.gz
aa9c783846e9339b9fe1fac0eb108bbce2a34da87faca2b16867968765a87264  node-v6.3.1-headers.tar.xz
66ef087709f7709f0bf066904df06815ac7ad213181d6dcc2adb4f9dc831704f  node-v6.3.1-linux-arm64.tar.gz
219e4182a2901af94d6fb907714995e240c6916f1dd3c196eab2478cc10eb219  node-v6.3.1-linux-arm64.tar.xz
fe6722b20b71e117be020792e7e69b124adeeae46ed666a406ac3a132870c418  node-v6.3.1-linux-armv6l.tar.gz
6632fdf08afbc2753635fc2698aaab9bffac0b258cb3b3f066469b90195b655c  node-v6.3.1-linux-armv6l.tar.xz
633fc62c5bb2cff7e2746ee5351494b97021468365d5d290f275f56db920337a  node-v6.3.1-linux-armv7l.tar.gz
426efe8025df70d6feb862a034669fd26aaad5c09707ce8a0f2b9a87d20cf412  node-v6.3.1-linux-armv7l.tar.xz
b760514341a29a684b86c9d6754a5b8d576a6b8cf1ec00cea012a38ba7ada7dd  node-v6.3.1-linux-ppc64le.tar.gz
c32c0588be391f23de70c1fbe9f56af520dd47dcbd29a4508a185bc5d703f62b  node-v6.3.1-linux-ppc64le.tar.xz
740824dd86eda983b3c7873e56a952585917f3d8770363f66f8593241009c273  node-v6.3.1-linux-ppc64.tar.gz
09e3d065dba1d80fab9bfaafa01bad57309f93107e0bc41c66a93b1f5b016afc  node-v6.3.1-linux-ppc64.tar.xz
eccc530696d18b07c5785e317b2babbea9c1dd14dbab80be734b820fc241ddea  node-v6.3.1-linux-x64.tar.gz
bf1d9c39fb6f9e4bd471673882e00c1c48b4c78fa7e13492a484a641a104e022  node-v6.3.1-linux-x64.tar.xz
23f07c6467f69c7e572e71a712bbc9c78be14ccad32f694edd52bc45b272a638  node-v6.3.1-linux-x86.tar.gz
2144c4288e9227fb3223d0c6158dc91b8eedc195045682bf28b333f2340043d5  node-v6.3.1-linux-x86.tar.xz
da6bbe14781266dc46c697b4223ed21c22766223989a5dff8f2eb05da53ca219  node-v6.3.1.pkg
a1e8f130b2edab12181a254dcd2cf65c72c4d121aacd0e06037b1a23437f3406  node-v6.3.1-sunos-x64.tar.gz
e40f34a52d9e40f2e7ee70fa1358f4bdeac08461c802765f4170fc8d6cc2709a  node-v6.3.1-sunos-x64.tar.xz
95c0c2d00068976bd96f2fab0dfabd3be0ea7ba7a7b4aaee4d30c7d1849d0e42  node-v6.3.1-sunos-x86.tar.gz
12e2f47999e973579a8bae53ddcb23797f942c0d97cf46fb9dd6857e3dcbe519  node-v6.3.1-sunos-x86.tar.xz
3f6144dcf13c210b11c83dc0b0a841219347a769b5a3b883b20f7ab8dc4008f6  node-v6.3.1.tar.gz
6fe584814b70d5e715be23f54545490609cdf1693372c2cdf47c76c798b02a1b  node-v6.3.1.tar.xz
9574885ea4acfe3ca8773e98e692cc9950dd86223364c808cc8a73122a922c34  node-v6.3.1-win-x64.7z
149addc6b65f7b22ded3c44cd2c720f44ededf4ca4578f105993a846bfa1e0c8  node-v6.3.1-win-x64.zip
57bfe0b948722fd4637193444c94e306b6016063ec53773f430be8c24234bac2  node-v6.3.1-win-x86.7z
d1a13d946f42201bbe827962ac666271210b760548387ee712762ea270b87d95  node-v6.3.1-win-x86.zip
d41bb2d45bbd2dde7539c7c7e4f80865979f0bb15e6af0e7ee9460d9ad47c8ec  node-v6.3.1-x64.msi
ed6c7ef60a0d04bfc5f766b7886c5f5dd3c79041bd2a00df6832fb416237f976  node-v6.3.1-x86.msi
2ab05a292e12f16bc0fef2dc7b3b5df0de2e81dd6a5c12f67d776f144d8e24ac  win-x64/node.exe
efe1403e3ab1cd17527c53a80f5e2736e1dc2b143baa60db9e77e26ce011bee8  win-x64/node.lib
72dc3089935b4a5e0a52e5768ac326ab775a9b14ec8019b634e0e9d4b3567f3a  win-x64/node_pdb.7z
e5d7045e1791d3e3ebebbe978f10f894abc08ec146c4f1112e0c43ed0fbe42b8  win-x64/node_pdb.zip
a1cadd824ac1894d35bb15c00e5ba86f1b96867606925a18666c8dbbec36a312  win-x86/node.exe
9b5322eab3d8af92e581887b80699d75208a40946a0f195589645007a0b37fb0  win-x86/node.lib
203bf697f8936b0d981427865fba20f133ad80deb8d9879f1b27ee1fc7ee4fdf  win-x86/node_pdb.7z
497094669a7fb59d9237c4c687e3a7a7c2ec713ab7abd5e9acf950232bef3d12  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----
Comment: GPGTools - https://gpgtools.org

iQIcBAEBCgAGBQJXkU0KAAoJELY7U1pMIGypLIkQAM2jMeRUzMfy+14u2zL0AeOh
nAeqrbS9zUIwY9KRVaI8NCYaOOsR3O/iWXHZn1qoZiGUCTMn4cbm7SVVqNeKp/Y5
OiFPOd8b0HbWIk0l8fGjJv3XwR3WpW5oYJ5Z015kHQt9ZNVSdl/jNsashIlwO67I
QHfUWvvokejqOYOvvITlG4oy5c7I/0mpaEfup9oPgfQ9Akg2QztpSougll3C11mU
r3EKg8Q0XLGT0vZ6s3Ec4unmCLsksUBfLt27taG1anqs/txDI0qS52Un1rU/GgOH
JjQbFXptTwcTO+hwQJ2QWoRXCgrbmiUE9uXYwNPDLyaCR4bp0LQz/VEnSoRhSzW8
BsUEDG7VpBxnuZCfEG8A61fxjF9XY05gl2sRsaP+NWqu+WERMUqNRpBO2uiJLkZz
84v72nN+akdZZprcNeDTx0a7PaF4CQrujsiVISAeaS2QYr8PudfyEw/8IV/U4Iqk
++0xsa2Ev60PjbkQjkASiVrhNeVPjcIOPAzEwLtnyT+MpqeNyVawazFCspP2nIt3
7nYmrQm/zQxyfvH+1th37L1l08SeUgkFuETqPMVN50KCwFCeeVAcTcCwb2bjoNFR
slK6G/ygI2ZJ76t0Cwx58UWViFY1QW8aaSdRIFwFmJ3sXz7CmOzcDxCH3DbsA8gX
tnze/Dbuv7gAVA+LtWmm
=L52s
-----END PGP SIGNATURE-----

```
