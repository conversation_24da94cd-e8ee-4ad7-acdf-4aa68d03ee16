---
date: '2017-08-01T07:14:18.044Z'
category: release
title: Node v6.11.2 (LTS)
layout: blog-post
author: <PERSON><PERSON>
---

### Notable Changes

- **configure**:
  - add mips64el to valid_arch (<PERSON><PERSON><PERSON>) [#13620](https://github.com/nodejs/node/pull/13620)
- **crypto**:
  - Updated root certificates based on [NSS 3.30](https://developer.mozilla.org/en-US/docs/Mozilla/Projects/NSS/NSS_3.30_release_notes) (<PERSON>)
    - [#13279](https://github.com/nodejs/node/pull/13279)
    - [#12402](https://github.com/nodejs/node/pull/12402)
- **deps**:
  - upgrade OpenSSL to version 1.0.2.l (<PERSON><PERSON><PERSON>) [#12913](https://github.com/nodejs/node/pull/12913)
- **http**:
  - parse errors are now reported when NODE_DEBUG=http (<PERSON>) [#13206](https://github.com/nodejs/node/pull/13206)
  - Agent construction can now be envoked without `new` (cjihrig) [#12927](https://github.com/nodejs/node/pull/12927)
- **zlib**:
  - node will now throw an Error when zlib rejects the value of windowBits, instead of crashing (Alexey Orlenko) [#13098](https://github.com/nodejs/node/pull/13098)

### Commits

- [[`8d043876c1`](https://github.com/nodejs/node/commit/8d043876c1)] - doc/tools: fix more type inconsistencies (Roman Reiss) [#11697](https://github.com/nodejs/node/pull/11697)
- [[`8860117600`](https://github.com/nodejs/node/commit/8860117600)] - **addons**: remove semicolons from after module definition (Gabriel Schulhof) [#12919](https://github.com/nodejs/node/pull/12919)
- [[`bb3f54771b`](https://github.com/nodejs/node/commit/bb3f54771b)] - **benchmark**: update an obsolete path (Vse Mozhet Byt) [#12904](https://github.com/nodejs/node/pull/12904)
- [[`7cc68e2c62`](https://github.com/nodejs/node/commit/7cc68e2c62)] - **benchmark**: add final clean-up to module-loader.js (Vse Mozhet Byt) [#12102](https://github.com/nodejs/node/pull/12102)
- [[`0cc7addcb2`](https://github.com/nodejs/node/commit/0cc7addcb2)] - **benchmark,windows**: TCP.readStart() meaningful only after completion (Refael Ackermann) [#12258](https://github.com/nodejs/node/pull/12258)
- [[`8dec80211e`](https://github.com/nodejs/node/commit/8dec80211e)] - **build**: run test-hash-seed at the end of test-v8 (Michaël Zasso) [#14219](https://github.com/nodejs/node/pull/14219)
- [[`bb1b06a4e5`](https://github.com/nodejs/node/commit/bb1b06a4e5)] - **build**: check for linter in bin rather than lib (Rich Trott) [#13645](https://github.com/nodejs/node/pull/13645)
- [[`f571868b1b`](https://github.com/nodejs/node/commit/f571868b1b)] - **build**: fail linter if linting not available (Gibson Fahnestock) [#13658](https://github.com/nodejs/node/pull/13658)
- [[`b0c6bf829b`](https://github.com/nodejs/node/commit/b0c6bf829b)] - **build**: use existing variable to reduce complexity (Bryce Baril) [#2883](https://github.com/nodejs/node/pull/2883)
- [[`ebbde61927`](https://github.com/nodejs/node/commit/ebbde61927)] - **build**: xz tarball extreme compression (Peter Dave Hello) [#10626](https://github.com/nodejs/node/pull/10626)
- [[`a354134f6a`](https://github.com/nodejs/node/commit/a354134f6a)] - **build**: ignore more VC++ artifacts (Refael Ackermann) [#13208](https://github.com/nodejs/node/pull/13208)
- [[`85829a65e8`](https://github.com/nodejs/node/commit/85829a65e8)] - **build**: avoid /docs/api and /docs/doc/api upload (Rod Vagg) [#12957](https://github.com/nodejs/node/pull/12957)
- [[`7bda9620c9`](https://github.com/nodejs/node/commit/7bda9620c9)] - **build**: simplify `if` in setting of arg_paths (Refael Ackermann) [#12653](https://github.com/nodejs/node/pull/12653)
- [[`2724fe34ef`](https://github.com/nodejs/node/commit/2724fe34ef)] - **build**: add static option to vcbuild.bat (Tony Rice) [#12764](https://github.com/nodejs/node/pull/12764)
- [[`7458d4ef98`](https://github.com/nodejs/node/commit/7458d4ef98)] - **build**: disable -O3 for C++ coverage (Anna Henningsen) [#12406](https://github.com/nodejs/node/pull/12406)
- [[`8b8bf39822`](https://github.com/nodejs/node/commit/8b8bf39822)] - **build**: avoid passing kill empty input in Makefile (Gibson Fahnestock) [#12158](https://github.com/nodejs/node/pull/12158)
- [[`914f368efd`](https://github.com/nodejs/node/commit/914f368efd)] - **build**: clear stalled jobs on POSIX CI hosts (Rich Trott) [#11246](https://github.com/nodejs/node/pull/11246)
- [[`890e210a5f`](https://github.com/nodejs/node/commit/890e210a5f)] - **build**: fix openssl link error on windows (Daniel Bevenius) [#13078](https://github.com/nodejs/node/pull/13078)
- [[`3bb117e310`](https://github.com/nodejs/node/commit/3bb117e310)] - **build**: enable cctest to use generated objects (Daniel Bevenius) [#11956](https://github.com/nodejs/node/pull/11956)
- [[`e5ca046c0a`](https://github.com/nodejs/node/commit/e5ca046c0a)] - **build, doc, tools**: add eslint-plugin-markdown (Vse Mozhet Byt) [#14067](https://github.com/nodejs/node/pull/14067)
- [[`b46cf35526`](https://github.com/nodejs/node/commit/b46cf35526)] - **child_process**: fix deoptimizing use of arguments (Vse Mozhet Byt) [#11535](https://github.com/nodejs/node/pull/11535)
- [[`edbe442938`](https://github.com/nodejs/node/commit/edbe442938)] - **cluster, dns, repl, tls, util**: fix RegExp nits (Vse Mozhet Byt) [#13536](https://github.com/nodejs/node/pull/13536)
- [[`a5f3b6fa7c`](https://github.com/nodejs/node/commit/a5f3b6fa7c)] - **configure**: add mips64el to valid_arch (Aditya Anand) [#13620](https://github.com/nodejs/node/pull/13620)
- [[`3b44e5e32c`](https://github.com/nodejs/node/commit/3b44e5e32c)] - **crypto**: return CHECK_OK in VerifyCallback (Daniel Bevenius) [#13241](https://github.com/nodejs/node/pull/13241)
- [[`1bfd177f09`](https://github.com/nodejs/node/commit/1bfd177f09)] - **crypto**: update root certificates (Ben Noordhuis) [#13279](https://github.com/nodejs/node/pull/13279)
- [[`b6f3581ea4`](https://github.com/nodejs/node/commit/b6f3581ea4)] - **crypto**: update root certificates (Ben Noordhuis) [#12402](https://github.com/nodejs/node/pull/12402)
- [[`1d509801e9`](https://github.com/nodejs/node/commit/1d509801e9)] - **crypto**: throw proper errors if out enc is UTF-16 (Anna Henningsen) [#12752](https://github.com/nodejs/node/pull/12752)
- [[`8f8dd97072`](https://github.com/nodejs/node/commit/8f8dd97072)] - **crypto**: clear err stack after ECDH::BufferToPoint (Ryan Kelly) [#13275](https://github.com/nodejs/node/pull/13275)
- [[`3891759afc`](https://github.com/nodejs/node/commit/3891759afc)] - **deps**: update openssl asm and asm_obsolete files (Shigeki Ohtsu) [#12913](https://github.com/nodejs/node/pull/12913)
- [[`92583c4c81`](https://github.com/nodejs/node/commit/92583c4c81)] - **deps**: cherry-pick 4ae5993 from upstream OpenSSL (Shigeki Ohtsu) [#12913](https://github.com/nodejs/node/pull/12913)
- [[`ee40a73d44`](https://github.com/nodejs/node/commit/ee40a73d44)] - **deps**: update openssl asm and asm_obsolete files (Daniel Bevenius) [#13233](https://github.com/nodejs/node/pull/13233)
- [[`a6a85c49c3`](https://github.com/nodejs/node/commit/a6a85c49c3)] - **deps**: update openssl config files (Daniel Bevenius) [#13233](https://github.com/nodejs/node/pull/13233)
- [[`a579a776a3`](https://github.com/nodejs/node/commit/a579a776a3)] - **deps**: add -no_rand_screen to openssl s_client (Shigeki Ohtsu) [nodejs/io.js#1836](https://github.com/nodejs/io.js/pull/1836)
- [[`b937c41405`](https://github.com/nodejs/node/commit/b937c41405)] - **deps**: fix asm build error of openssl in x86_win32 (Shigeki Ohtsu) [iojs/io.js#1389](https://github.com/iojs/io.js/pull/1389)
- [[`69570d370a`](https://github.com/nodejs/node/commit/69570d370a)] - **deps**: fix openssl assembly error on ia32 win32 (Fedor Indutny) [iojs/io.js#1389](https://github.com/iojs/io.js/pull/1389)
- [[`5703d22849`](https://github.com/nodejs/node/commit/5703d22849)] - **deps**: copy all openssl header files to include dir (Daniel Bevenius) [#13233](https://github.com/nodejs/node/pull/13233)
- [[`77a9198aca`](https://github.com/nodejs/node/commit/77a9198aca)] - **deps**: upgrade openssl sources to 1.0.2l (Daniel Bevenius) [#13233](https://github.com/nodejs/node/pull/13233)
- [[`5b4c431365`](https://github.com/nodejs/node/commit/5b4c431365)] - **deps**: add example of comparing OpenSSL changes (Daniel Bevenius) [#13234](https://github.com/nodejs/node/pull/13234)
- [[`18cbee236d`](https://github.com/nodejs/node/commit/18cbee236d)] - **dns**: fix crash using dns.setServers after resolve4 (XadillaX) [#13050](https://github.com/nodejs/node/pull/13050)
- [[`8c0849d5db`](https://github.com/nodejs/node/commit/8c0849d5db)] - **doc**: conform to rules for eslint-plugin-markdown (Vse Mozhet Byt) [#12563](https://github.com/nodejs/node/pull/12563)
- [[`7deb259ccb`](https://github.com/nodejs/node/commit/7deb259ccb)] - **doc**: prepare js code for eslint-plugin-markdown (Vse Mozhet Byt) [#12563](https://github.com/nodejs/node/pull/12563)
- [[`59eb761797`](https://github.com/nodejs/node/commit/59eb761797)] - **doc**: document and test that methods return this (Sam Roberts) [#13553](https://github.com/nodejs/node/pull/13553)
- [[`fcb27fa7a1`](https://github.com/nodejs/node/commit/fcb27fa7a1)] - **doc**: remove leftover WHATWG url.format section (Roman Reiss) [#14351](https://github.com/nodejs/node/pull/14351)
- [[`e400ef9a76`](https://github.com/nodejs/node/commit/e400ef9a76)] - **doc**: don't suggest setEncoding for binary streams (Rick Bullotta) [#11363](https://github.com/nodejs/node/pull/11363)
- [[`092bba5cbf`](https://github.com/nodejs/node/commit/092bba5cbf)] - **doc**: update backporting guide (Refael Ackermann) [#13749](https://github.com/nodejs/node/pull/13749)
- [[`e2abda87f5`](https://github.com/nodejs/node/commit/e2abda87f5)] - **doc**: mention rebasing of v?.x-staging post release (Anna Henningsen) [#13742](https://github.com/nodejs/node/pull/13742)
- [[`24feb333c8`](https://github.com/nodejs/node/commit/24feb333c8)] - **doc**: `path.relative` uses `cwd` (DuanPengfei) [#13714](https://github.com/nodejs/node/pull/13714)
- [[`71581e9308`](https://github.com/nodejs/node/commit/71581e9308)] - **doc**: small makeover for onboarding.md (Anna Henningsen) [#13413](https://github.com/nodejs/node/pull/13413)
- [[`8f430e774b`](https://github.com/nodejs/node/commit/8f430e774b)] - **doc**: note that EoL platforms are not supported (Gibson Fahnestock) [#12672](https://github.com/nodejs/node/pull/12672)
- [[`9fa70069b3`](https://github.com/nodejs/node/commit/9fa70069b3)] - **doc**: use HTTPS URL for suggested upstream remote (Nikolai Vavilov) [#13602](https://github.com/nodejs/node/pull/13602)
- [[`fa209323af`](https://github.com/nodejs/node/commit/fa209323af)] - **doc**: update new CTC members (Refael Ackermann) [#13534](https://github.com/nodejs/node/pull/13534)
- [[`054f8cdc4d`](https://github.com/nodejs/node/commit/054f8cdc4d)] - **doc**: corrects reference to tlsClientError (Tarun) [#13533](https://github.com/nodejs/node/pull/13533)
- [[`17da29ce84`](https://github.com/nodejs/node/commit/17da29ce84)] - **doc**: emphasize Collaborators in GOVERNANCE.md (Rich Trott) [#13423](https://github.com/nodejs/node/pull/13423)
- [[`aea953abc2`](https://github.com/nodejs/node/commit/aea953abc2)] - **doc**: minimal documentation for Emeritus status (Rich Trott) [#13421](https://github.com/nodejs/node/pull/13421)
- [[`42a42c0892`](https://github.com/nodejs/node/commit/42a42c0892)] - **doc**: remove note highlighting in GOVERNANCE doc (Rich Trott) [#13420](https://github.com/nodejs/node/pull/13420)
- [[`cc492c361f`](https://github.com/nodejs/node/commit/cc492c361f)] - **doc**: resume a stream after pipe() and unpipe() (Matteo Collina) [#13329](https://github.com/nodejs/node/pull/13329)
- [[`ae00f25a69`](https://github.com/nodejs/node/commit/ae00f25a69)] - **doc**: suggest xcode-select --install (Gibson Fahnestock) [#13264](https://github.com/nodejs/node/pull/13264)
- [[`8daab3be31`](https://github.com/nodejs/node/commit/8daab3be31)] - **doc**: remove 'you' from writing-tests.md (Michael Dawson) [#13319](https://github.com/nodejs/node/pull/13319)
- [[`f2ede07f17`](https://github.com/nodejs/node/commit/f2ede07f17)] - **doc**: add tniessen to collaborators (Tobias Nießen) [#13371](https://github.com/nodejs/node/pull/13371)
- [[`a33c6759b6`](https://github.com/nodejs/node/commit/a33c6759b6)] - **doc**: create list of CTC emeriti (Rich Trott) [#13232](https://github.com/nodejs/node/pull/13232)
- [[`3745fbaa5d`](https://github.com/nodejs/node/commit/3745fbaa5d)] - **doc**: remove Gitter badge from README (Rich Trott) [#13231](https://github.com/nodejs/node/pull/13231)
- [[`a7b51af049`](https://github.com/nodejs/node/commit/a7b51af049)] - **doc**: make spelling of behavior consistent (Michael Dawson) [#13245](https://github.com/nodejs/node/pull/13245)
- [[`277de4257d`](https://github.com/nodejs/node/commit/277de4257d)] - **doc**: add jasongin & kunalspathak to collaborators (Jason Ginchereau) [#13200](https://github.com/nodejs/node/pull/13200)
- [[`fb07fbcc81`](https://github.com/nodejs/node/commit/fb07fbcc81)] - **doc**: don't use useless constructors in stream.md (Vse Mozhet Byt) [#13145](https://github.com/nodejs/node/pull/13145)
- [[`cb03bd1f48`](https://github.com/nodejs/node/commit/cb03bd1f48)] - **doc**: update code example for Windows in stream.md (Vse Mozhet Byt) [#13138](https://github.com/nodejs/node/pull/13138)
- [[`079b04e58d`](https://github.com/nodejs/node/commit/079b04e58d)] - **doc**: improve formatting of STYLE_GUIDE.md (Alexey Orlenko) [#13135](https://github.com/nodejs/node/pull/13135)
- [[`5f87252969`](https://github.com/nodejs/node/commit/5f87252969)] - **doc**: fix incorrect keyboard shortcut (Alexey Orlenko) [#13134](https://github.com/nodejs/node/pull/13134)
- [[`d4edc82aa5`](https://github.com/nodejs/node/commit/d4edc82aa5)] - **doc**: edit Error.captureStackTrace html comment (Artur Vieira) [#12962](https://github.com/nodejs/node/pull/12962)
- [[`1f9713362d`](https://github.com/nodejs/node/commit/1f9713362d)] - **doc**: add additional useful ci job to list (Michael Dawson) [#13086](https://github.com/nodejs/node/pull/13086)
- [[`2d5e2e9cab`](https://github.com/nodejs/node/commit/2d5e2e9cab)] - **doc**: document method for reverting commits (Gibson Fahnestock) [#13015](https://github.com/nodejs/node/pull/13015)
- [[`b31e6dfef5`](https://github.com/nodejs/node/commit/b31e6dfef5)] - **doc**: update COLLABORATOR_GUIDE.md (morrme) [#12555](https://github.com/nodejs/node/pull/12555)
- [[`b854d27330`](https://github.com/nodejs/node/commit/b854d27330)] - **doc**: Change options at STEP 5 in CONTRIBUTING.md (kysnm) [#12830](https://github.com/nodejs/node/pull/12830)
- [[`c01a2d545e`](https://github.com/nodejs/node/commit/c01a2d545e)] - **doc**: add docs for server.address() for pipe case (Flarna) [#12907](https://github.com/nodejs/node/pull/12907)
- [[`83f272d4ee`](https://github.com/nodejs/node/commit/83f272d4ee)] - **doc**: fix typo in streams.md (Glenn Schlereth) [#12924](https://github.com/nodejs/node/pull/12924)
- [[`28add410c2`](https://github.com/nodejs/node/commit/28add410c2)] - **doc**: improve path.posix.normalize docs (Steven Lehn) [#12700](https://github.com/nodejs/node/pull/12700)
- [[`023ec46d2c`](https://github.com/nodejs/node/commit/023ec46d2c)] - **doc**: remove test-npm from general build doc (Rich Trott) [#12840](https://github.com/nodejs/node/pull/12840)
- [[`74a6929938`](https://github.com/nodejs/node/commit/74a6929938)] - **doc**: upgrade Clang requirement to 3.4.2 (Michaël Zasso) [#12388](https://github.com/nodejs/node/pull/12388)
- [[`5b379e0aad`](https://github.com/nodejs/node/commit/5b379e0aad)] - **doc**: clarify the callback arguments of dns.resolve (Roman Reiss) [#9532](https://github.com/nodejs/node/pull/9532)
- [[`f6e58c35b2`](https://github.com/nodejs/node/commit/f6e58c35b2)] - **doc**: add missing make command to UPGRADING.md (Daniel Bevenius) [#13233](https://github.com/nodejs/node/pull/13233)
- [[`a7869541e4`](https://github.com/nodejs/node/commit/a7869541e4)] - **doc**: increase Buffer.concat() documentation (cjihrig) [#11845](https://github.com/nodejs/node/pull/11845)
- [[`3b1d9112e0`](https://github.com/nodejs/node/commit/3b1d9112e0)] - **doc**: update readFileSync in fs.md (Aditya Anand) [#12800](https://github.com/nodejs/node/pull/12800)
- [[`bc66495061`](https://github.com/nodejs/node/commit/bc66495061)] - **doc**: document vm timeout option perf impact (Anna Henningsen) [#12751](https://github.com/nodejs/node/pull/12751)
- [[`a3ae360ea6`](https://github.com/nodejs/node/commit/a3ae360ea6)] - **doc**: modernize and fix code examples in repl.md (Vse Mozhet Byt) [#12634](https://github.com/nodejs/node/pull/12634)
- [[`2435af9db6`](https://github.com/nodejs/node/commit/2435af9db6)] - **doc**: update os.uptime() and process.uptime() info (Vse Mozhet Byt) [#12294](https://github.com/nodejs/node/pull/12294)
- [[`b2e58b6c7a`](https://github.com/nodejs/node/commit/b2e58b6c7a)] - **doc**: minor improvements in BUILDING.md (Sakthipriyan Vairamani (thefourtheye)) [#11963](https://github.com/nodejs/node/pull/11963)
- [[`7ba172f56f`](https://github.com/nodejs/node/commit/7ba172f56f)] - **doc**: argument types for https methods (Amelia Clarke) [#11681](https://github.com/nodejs/node/pull/11681)
- [[`eb9e281b6b`](https://github.com/nodejs/node/commit/eb9e281b6b)] - **doc**: update output examples in debugger.md (Vse Mozhet Byt) [#10944](https://github.com/nodejs/node/pull/10944)
- [[`b62cec8b02`](https://github.com/nodejs/node/commit/b62cec8b02)] - **doc**: linkify type\[\] syntax, support lowercase for primitives (Roman Reiss) [#11167](https://github.com/nodejs/node/pull/11167)
- [[`dd1fb98bda`](https://github.com/nodejs/node/commit/dd1fb98bda)] - **doc**: consistent case for primitive types (Roman Reiss) [#11167](https://github.com/nodejs/node/pull/11167)
- [[`c43866954e`](https://github.com/nodejs/node/commit/c43866954e)] - **doc,build**: update configure help messages (Gibson Fahnestock) [#12978](https://github.com/nodejs/node/pull/12978)
- [[`0d35bcdf84`](https://github.com/nodejs/node/commit/0d35bcdf84)] - **doc,stream**: clarify 'data', pipe() and 'readable' (Matteo Collina) [#13432](https://github.com/nodejs/node/pull/13432)
- [[`351be2d5a8`](https://github.com/nodejs/node/commit/351be2d5a8)] - **dtrace**: resolve conversion warnings from SLURP_INT (Christopher J. Brody) [#10143](https://github.com/nodejs/node/pull/10143)
- [[`046bd79cf7`](https://github.com/nodejs/node/commit/046bd79cf7)] - **events**: remove unreachable code (cjihrig) [#12501](https://github.com/nodejs/node/pull/12501)
- [[`8bf64d135f`](https://github.com/nodejs/node/commit/8bf64d135f)] - **events**: do not keep arrays with a single listener (Luigi Pinca) [#12043](https://github.com/nodejs/node/pull/12043)
- [[`f66f09f5d1`](https://github.com/nodejs/node/commit/f66f09f5d1)] - **http**: describe parse err in debug output (Sam Roberts) [#13206](https://github.com/nodejs/node/pull/13206)
- [[`cab1285ccf`](https://github.com/nodejs/node/commit/cab1285ccf)] - **http**: fix first body chunk fast case for UTF-16 (Anna Henningsen) [#12747](https://github.com/nodejs/node/pull/12747)
- [[`01302989a7`](https://github.com/nodejs/node/commit/01302989a7)] - **https**: support rejectUnauthorized for unix sockets (cjihrig) [#13505](https://github.com/nodejs/node/pull/13505)
- [[`d51cd61713`](https://github.com/nodejs/node/commit/d51cd61713)] - **https**: support agent construction without new (cjihrig) [#12927](https://github.com/nodejs/node/pull/12927)
- [[`5eb11ba73e`](https://github.com/nodejs/node/commit/5eb11ba73e)] - **lib**: correct typo in createSecureContext (Daniel Bevenius) [#13653](https://github.com/nodejs/node/pull/13653)
- [[`102671823c`](https://github.com/nodejs/node/commit/102671823c)] - **lib**: "iff" changed to "if and only if" (Jacob Jones) [#13496](https://github.com/nodejs/node/pull/13496)
- [[`1609c7f0c5`](https://github.com/nodejs/node/commit/1609c7f0c5)] - **lib**: remove useless default caught (Jackson Tian) [#12884](https://github.com/nodejs/node/pull/12884)
- [[`ef133b36c5`](https://github.com/nodejs/node/commit/ef133b36c5)] - **lib,test**: use regular expression literals (Rich Trott) [#12807](https://github.com/nodejs/node/pull/12807)
- [[`0cb5bd7268`](https://github.com/nodejs/node/commit/0cb5bd7268)] - **meta**: fix nits in README.md collaborators list (Vse Mozhet Byt) [#12866](https://github.com/nodejs/node/pull/12866)
- [[`4c51d969ee`](https://github.com/nodejs/node/commit/4c51d969ee)] - **openssl**: fix keypress requirement in apps on win32 (Shigeki Ohtsu) [iojs/io.js#1389](https://github.com/iojs/io.js/pull/1389)
- [[`47e702059d`](https://github.com/nodejs/node/commit/47e702059d)] - **os,vm**: fix segfaults and CHECK failure (Tobias Nießen) [#12371](https://github.com/nodejs/node/pull/12371)
- [[`c97b167f47`](https://github.com/nodejs/node/commit/c97b167f47)] - **profiler**: declare missing `printErr` (Fedor Indutny) [#13590](https://github.com/nodejs/node/pull/13590)
- [[`bd323a71a8`](https://github.com/nodejs/node/commit/bd323a71a8)] - **repl**: fix /dev/null history file regression (Brian White) [#12762](https://github.com/nodejs/node/pull/12762)
- [[`b2acb81016`](https://github.com/nodejs/node/commit/b2acb81016)] - **repl**: support hidden history file on Windows (Bartosz Sosnowski) [#12207](https://github.com/nodejs/node/pull/12207)
- [[`79592fe44a`](https://github.com/nodejs/node/commit/79592fe44a)] - **src**: correct indentation for X509ToObject (Daniel Bevenius) [#13543](https://github.com/nodejs/node/pull/13543)
- [[`69143ffcf9`](https://github.com/nodejs/node/commit/69143ffcf9)] - **src**: make IsConstructCall checks consistent (Daniel Bevenius) [#13473](https://github.com/nodejs/node/pull/13473)
- [[`48f00b5170`](https://github.com/nodejs/node/commit/48f00b5170)] - **src**: add comment for TicketKeyCallback (Anna Henningsen) [#13193](https://github.com/nodejs/node/pull/13193)
- [[`37e1929257`](https://github.com/nodejs/node/commit/37e1929257)] - **src**: check IsConstructCall in TLSWrap constructor (Daniel Bevenius) [#13097](https://github.com/nodejs/node/pull/13097)
- [[`2e23da1a12`](https://github.com/nodejs/node/commit/2e23da1a12)] - **src**: remove unused node_buffer.h include (Daniel Bevenius) [#13095](https://github.com/nodejs/node/pull/13095)
- [[`41661287f2`](https://github.com/nodejs/node/commit/41661287f2)] - **src**: split CryptoPemCallback into two functions (Daniel Bevenius) [#12827](https://github.com/nodejs/node/pull/12827)
- [[`f92e065d12`](https://github.com/nodejs/node/commit/f92e065d12)] - **src**: assert that uv_async_init() succeeds (cjihrig) [#13116](https://github.com/nodejs/node/pull/13116)
- [[`f43c969061`](https://github.com/nodejs/node/commit/f43c969061)] - **src**: turn buffer type-CHECK into exception (Anna Henningsen) [#12753](https://github.com/nodejs/node/pull/12753)
- [[`19259f46d0`](https://github.com/nodejs/node/commit/19259f46d0)] - **src**: rename CryptoPemCallback -\> PasswordCallback (Daniel Bevenius) [#12787](https://github.com/nodejs/node/pull/12787)
- [[`7aa5a993b2`](https://github.com/nodejs/node/commit/7aa5a993b2)] - **src**: make cross-context MakeCallback() calls work (Ben Noordhuis) [#9221](https://github.com/nodejs/node/pull/9221)
- [[`b1dc2d455f`](https://github.com/nodejs/node/commit/b1dc2d455f)] - **src**: remove superfluous env_string string (Ben Noordhuis) [#9213](https://github.com/nodejs/node/pull/9213)
- [[`48a923af37`](https://github.com/nodejs/node/commit/48a923af37)] - **stream**: remove unnecessary parameter (Leo) [#12767](https://github.com/nodejs/node/pull/12767)
- [[`9cfec4ba0f`](https://github.com/nodejs/node/commit/9cfec4ba0f)] - **test**: fix RegExp nits (Vse Mozhet Byt) [#13770](https://github.com/nodejs/node/pull/13770)
- [[`a3e2560f7a`](https://github.com/nodejs/node/commit/a3e2560f7a)] - **test**: mark test-npm-install flaky on arm (Refael Ackermann) [#14035](https://github.com/nodejs/node/pull/14035)
- [[`8a7f13bd00`](https://github.com/nodejs/node/commit/8a7f13bd00)] - **test**: mark test-fs-readdir-ucs2 flaky (João Reis) [#13989](https://github.com/nodejs/node/pull/13989)
- [[`34fc7a03d2`](https://github.com/nodejs/node/commit/34fc7a03d2)] - **test**: change deprecated method to recommended (Rich Trott) [#13649](https://github.com/nodejs/node/pull/13649)
- [[`ef3698cad8`](https://github.com/nodejs/node/commit/ef3698cad8)] - **test**: refactor test-cluster-worker-isconnected.js (cjihrig) [#13685](https://github.com/nodejs/node/pull/13685)
- [[`fa75be7901`](https://github.com/nodejs/node/commit/fa75be7901)] - **test**: fix nits in test-fs-mkdir-rmdir.js (Vse Mozhet Byt) [#13680](https://github.com/nodejs/node/pull/13680)
- [[`9e9a9c342c`](https://github.com/nodejs/node/commit/9e9a9c342c)] - **test**: increase bufsize in child process write test (Rich Trott) [#13626](https://github.com/nodejs/node/pull/13626)
- [[`53b345c506`](https://github.com/nodejs/node/commit/53b345c506)] - **test**: fix flaky test-tls-socket-close (Rich Trott) [#13529](https://github.com/nodejs/node/pull/13529)
- [[`a37165a2cc`](https://github.com/nodejs/node/commit/a37165a2cc)] - **test**: exercise once() with varying arguments (cjihrig) [#13524](https://github.com/nodejs/node/pull/13524)
- [[`779402ec5f`](https://github.com/nodejs/node/commit/779402ec5f)] - **test**: validate full error messages (aniketshukla) [#13453](https://github.com/nodejs/node/pull/13453)
- [[`7190d06d1f`](https://github.com/nodejs/node/commit/7190d06d1f)] - **test**: add known_test request with Unicode in the URL (David D Lowe) [#13297](https://github.com/nodejs/node/pull/13297)
- [[`cbcc9c1bbf`](https://github.com/nodejs/node/commit/cbcc9c1bbf)] - **test**: add coverage for socket write after close (cjihrig) [#13171](https://github.com/nodejs/node/pull/13171)
- [[`47d59e7f97`](https://github.com/nodejs/node/commit/47d59e7f97)] - **test**: fix sequential test-net-connect-local-error (Sebastian Plesciuc) [#13064](https://github.com/nodejs/node/pull/13064)
- [[`1d3596561b`](https://github.com/nodejs/node/commit/1d3596561b)] - **test**: bind to 0 in dgram-send-callback-buffer-length (Artur Vieira) [#12943](https://github.com/nodejs/node/pull/12943)
- [[`7909c6d46f`](https://github.com/nodejs/node/commit/7909c6d46f)] - **test**: use dynamic port in test-dgram-send-callback-buffer (Artur Vieira) [#12942](https://github.com/nodejs/node/pull/12942)
- [[`92cc96fa6b`](https://github.com/nodejs/node/commit/92cc96fa6b)] - **test**: allow for absent nobody user in setuid test (Rich Trott) [#13112](https://github.com/nodejs/node/pull/13112)
- [[`253c5aa794`](https://github.com/nodejs/node/commit/253c5aa794)] - **test**: move net reconnect error test to sequential (Artur G Vieira) [#13033](https://github.com/nodejs/node/pull/13033)
- [[`e279eb5aa3`](https://github.com/nodejs/node/commit/e279eb5aa3)] - **test**: ignore spurious 'EMFILE' (Refael Ackermann) [#12698](https://github.com/nodejs/node/pull/12698)
- [[`3e5e38e868`](https://github.com/nodejs/node/commit/3e5e38e868)] - **test**: use dynamic port in test-cluster-dgram-reuse (Artur Vieira) [#12901](https://github.com/nodejs/node/pull/12901)
- [[`5fe68402bd`](https://github.com/nodejs/node/commit/5fe68402bd)] - **test**: refactor test-vm-new-script-new-context (Akshay Iyer) [#13035](https://github.com/nodejs/node/pull/13035)
- [[`2aa68282fc`](https://github.com/nodejs/node/commit/2aa68282fc)] - **test**: track callback invocations (Rich Trott) [#13010](https://github.com/nodejs/node/pull/13010)
- [[`0c83573b61`](https://github.com/nodejs/node/commit/0c83573b61)] - **test**: add a simple abort check in windows (Sreepurna Jasti) [#12914](https://github.com/nodejs/node/pull/12914)
- [[`07137ab4db`](https://github.com/nodejs/node/commit/07137ab4db)] - **test**: fix too optimistic guess in setproctitle (Vse Mozhet Byt) [#12792](https://github.com/nodejs/node/pull/12792)
- [[`7419338b33`](https://github.com/nodejs/node/commit/7419338b33)] - **test**: make the rest of tests path-independent (Vse Mozhet Byt) [#12972](https://github.com/nodejs/node/pull/12972)
- [[`ac400a7b09`](https://github.com/nodejs/node/commit/ac400a7b09)] - **test**: check curve algorithm is supported (Karl Cheng)
- [[`5b74e635e5`](https://github.com/nodejs/node/commit/5b74e635e5)] - **test**: reduce string concatenations (Vse Mozhet Byt) [#12735](https://github.com/nodejs/node/pull/12735)
- [[`c902265b90`](https://github.com/nodejs/node/commit/c902265b90)] - **test**: fix parallel/test-setproctitle.js on alpine (David Cai) [#12413](https://github.com/nodejs/node/pull/12413)
- [[`50bb452510`](https://github.com/nodejs/node/commit/50bb452510)] - **test**: fixed flaky test-net-connect-local-error (Sebastian Plesciuc) [#12964](https://github.com/nodejs/node/pull/12964)
- [[`0cf3e10ce2`](https://github.com/nodejs/node/commit/0cf3e10ce2)] - **test**: remove unneeded string splitting (Vse Mozhet Byt) [#12992](https://github.com/nodejs/node/pull/12992)
- [[`6e7b77fdbb`](https://github.com/nodejs/node/commit/6e7b77fdbb)] - **test**: use mustCall in tls-connect-given-socket (vperezma) [#12592](https://github.com/nodejs/node/pull/12592)
- [[`c10525c562`](https://github.com/nodejs/node/commit/c10525c562)] - **test**: add not-called check to heap-profiler test (Rich Trott) [#12985](https://github.com/nodejs/node/pull/12985)
- [[`2451665157`](https://github.com/nodejs/node/commit/2451665157)] - **test**: move test-dgram-bind-shared-ports to sequential (Rafael Fragoso) [#12452](https://github.com/nodejs/node/pull/12452)
- [[`d35648ffc2`](https://github.com/nodejs/node/commit/d35648ffc2)] - **test**: use dynamic port in test-https-connect-address-family (Artur G Vieira) [#12915](https://github.com/nodejs/node/pull/12915)
- [[`1cd41e7a56`](https://github.com/nodejs/node/commit/1cd41e7a56)] - **test**: dynamic port in cluster disconnect (Sebastian Plesciuc) [#12545](https://github.com/nodejs/node/pull/12545)
- [[`d71de281fa`](https://github.com/nodejs/node/commit/d71de281fa)] - **test**: detect all types of aborts in windows (Gireesh Punathil) [#12856](https://github.com/nodejs/node/pull/12856)
- [[`d743783875`](https://github.com/nodejs/node/commit/d743783875)] - **test**: use assert regexp in tls no cert test (Artur Vieira) [#12891](https://github.com/nodejs/node/pull/12891)
- [[`29d35d0ef1`](https://github.com/nodejs/node/commit/29d35d0ef1)] - **test**: use dynamic port instead of common.PORT (Aditya Anand) [#12473](https://github.com/nodejs/node/pull/12473)
- [[`186c0758b3`](https://github.com/nodejs/node/commit/186c0758b3)] - **test**: added net.connect lookup type check (Luca Maraschi) [#11873](https://github.com/nodejs/node/pull/11873)
- [[`c35f4909f4`](https://github.com/nodejs/node/commit/c35f4909f4)] - **test**: remove unused testpy code (Rich Trott) [#12844](https://github.com/nodejs/node/pull/12844)
- [[`52b7d5ecb1`](https://github.com/nodejs/node/commit/52b7d5ecb1)] - **test**: refactor test-querystring (Łukasz Szewczak) [#12661](https://github.com/nodejs/node/pull/12661)
- [[`8414659d02`](https://github.com/nodejs/node/commit/8414659d02)] - **test**: refactoring test with common.mustCall (weewey) [#12702](https://github.com/nodejs/node/pull/12702)
- [[`608c30913e`](https://github.com/nodejs/node/commit/608c30913e)] - **test**: refactored test-repl-persistent-history (cool88) [#12703](https://github.com/nodejs/node/pull/12703)
- [[`aaf8044a81`](https://github.com/nodejs/node/commit/aaf8044a81)] - **test**: remove common.PORT in test tls ticket cluster (Oscar Martinez) [#12715](https://github.com/nodejs/node/pull/12715)
- [[`802a945d81`](https://github.com/nodejs/node/commit/802a945d81)] - **test**: add mustCall in timers-unrefed-in-callback (Zahidul Islam) [#12594](https://github.com/nodejs/node/pull/12594)
- [[`739c579134`](https://github.com/nodejs/node/commit/739c579134)] - **test**: fix flakyness with `yes.exe` (Refael Ackermann) [#12821](https://github.com/nodejs/node/pull/12821)
- [[`14e835831f`](https://github.com/nodejs/node/commit/14e835831f)] - **test**: dynamic port in dgram tests (Sebastian Plesciuc) [#12623](https://github.com/nodejs/node/pull/12623)
- [[`361bc845dc`](https://github.com/nodejs/node/commit/361bc845dc)] - **test**: verify listener leak is only emitted once (cjihrig) [#12502](https://github.com/nodejs/node/pull/12502)
- [[`f236dcbdd9`](https://github.com/nodejs/node/commit/f236dcbdd9)] - **test**: move WPT to its own testing module (Rich Trott) [#12736](https://github.com/nodejs/node/pull/12736)
- [[`4eb28c80e8`](https://github.com/nodejs/node/commit/4eb28c80e8)] - **test**: introduce `common.crashOnUnhandledRejection` (Anna Henningsen) [#12489](https://github.com/nodejs/node/pull/12489)
- [[`2411318f60`](https://github.com/nodejs/node/commit/2411318f60)] - **test**: add second argument to assert.throws (Michaël Zasso) [#12270](https://github.com/nodejs/node/pull/12270)
- [[`eca9e72a87`](https://github.com/nodejs/node/commit/eca9e72a87)] - **test**: add regex in test_cyclic_link_protection (Clarence Dimitri CHARLES) [#11622](https://github.com/nodejs/node/pull/11622)
- [[`6020e720b5`](https://github.com/nodejs/node/commit/6020e720b5)] - **test**: improve test-fs-open-flags (Vinícius do Carmo) [#10908](https://github.com/nodejs/node/pull/10908)
- [[`e6d6a4111c`](https://github.com/nodejs/node/commit/e6d6a4111c)] - **test**: extended test to makeCallback cb type check (Luca Maraschi) [#12140](https://github.com/nodejs/node/pull/12140)
- [[`d74019d98d`](https://github.com/nodejs/node/commit/d74019d98d)] - **test**: improve test-crypto-rsa-dsa (Adrian Estrada) [#10681](https://github.com/nodejs/node/pull/10681)
- [[`bab8a36f94`](https://github.com/nodejs/node/commit/bab8a36f94)] - **test**: improve the code in test-crypto-dh (Adrian Estrada) [#10734](https://github.com/nodejs/node/pull/10734)
- [[`752bc24943`](https://github.com/nodejs/node/commit/752bc24943)] - **test**: validate errors in test-buffer-indexof (Adrian Estrada) [#10752](https://github.com/nodejs/node/pull/10752)
- [[`9e7f02187a`](https://github.com/nodejs/node/commit/9e7f02187a)] - **test**: improve test-buffer-includes.js (toboid) [#11203](https://github.com/nodejs/node/pull/11203)
- [[`c309bb0695`](https://github.com/nodejs/node/commit/c309bb0695)] - **test**: validate error message from buffer.equals (Sebastian Roeder) [#11215](https://github.com/nodejs/node/pull/11215)
- [[`62c56806fc`](https://github.com/nodejs/node/commit/62c56806fc)] - **test**: add msg validation to test-buffer-compare (Josh Hollandsworth) [#10807](https://github.com/nodejs/node/pull/10807)
- [[`fc9e7a98ed`](https://github.com/nodejs/node/commit/fc9e7a98ed)] - **test**: make tests cwd-independent (Vse Mozhet Byt) [#12812](https://github.com/nodejs/node/pull/12812)
- [[`fff0e39933`](https://github.com/nodejs/node/commit/fff0e39933)] - **test**: add regex check in test-vm-is-context (jeyanthinath) [#12785](https://github.com/nodejs/node/pull/12785)
- [[`74dc86d239`](https://github.com/nodejs/node/commit/74dc86d239)] - **test**: add callback to fs.close() in test-fs-stat (Vse Mozhet Byt) [#12804](https://github.com/nodejs/node/pull/12804)
- [[`a47a9b7cf4`](https://github.com/nodejs/node/commit/a47a9b7cf4)] - **test**: add callback to fs.close() in test-fs-chmod (Vse Mozhet Byt) [#12795](https://github.com/nodejs/node/pull/12795)
- [[`eefa840118`](https://github.com/nodejs/node/commit/eefa840118)] - **test**: increase readline coverage (Anna Henningsen) [#12761](https://github.com/nodejs/node/pull/12761)
- [[`54decfa2ce`](https://github.com/nodejs/node/commit/54decfa2ce)] - **test**: replace indexOf with includes (gwer) [#12604](https://github.com/nodejs/node/pull/12604)
- [[`03adb94ee6`](https://github.com/nodejs/node/commit/03adb94ee6)] - **test**: dynamic port in parallel regress tests (Sebastian Plesciuc) [#12639](https://github.com/nodejs/node/pull/12639)
- [[`8a59f6b038`](https://github.com/nodejs/node/commit/8a59f6b038)] - **test**: dynamic port in cluster worker wait close (Sebastian Plesciuc) [#12466](https://github.com/nodejs/node/pull/12466)
- [[`0383048b76`](https://github.com/nodejs/node/commit/0383048b76)] - **test**: fix coverity UNINIT_CTOR cctest warning (Ben Noordhuis) [#12387](https://github.com/nodejs/node/pull/12387)
- [[`f2467edc62`](https://github.com/nodejs/node/commit/f2467edc62)] - **test**: remove common.PORT from multiple tests (Tarun Batra) [#12451](https://github.com/nodejs/node/pull/12451)
- [[`a23aca4f12`](https://github.com/nodejs/node/commit/a23aca4f12)] - **test**: replace \[\].join() with ''.repeat() (Jackson Tian) [#12305](https://github.com/nodejs/node/pull/12305)
- [[`e512906aab`](https://github.com/nodejs/node/commit/e512906aab)] - **test**: run the addon tests last (Sebastian Van Sande) [#12062](https://github.com/nodejs/node/pull/12062)
- [[`abc2c82bf3`](https://github.com/nodejs/node/commit/abc2c82bf3)] - **test**: remove disabled test-dgram-send-error (Rich Trott) [#12330](https://github.com/nodejs/node/pull/12330)
- [[`d9866ce9c7`](https://github.com/nodejs/node/commit/d9866ce9c7)] - **test**: remove disabled tls_server.js (Rich Trott) [#12275](https://github.com/nodejs/node/pull/12275)
- [[`19d95519c7`](https://github.com/nodejs/node/commit/19d95519c7)] - **test**: add basic cctest for base64.h (Alexey Orlenko) [#12238](https://github.com/nodejs/node/pull/12238)
- [[`01073bc26a`](https://github.com/nodejs/node/commit/01073bc26a)] - **test**: add internal/socket_list tests (DavidCai) [#12109](https://github.com/nodejs/node/pull/12109)
- [[`a5fe098b85`](https://github.com/nodejs/node/commit/a5fe098b85)] - **test**: move common.PORT debug tests to sequential (Gibson Fahnestock) [#13592](https://github.com/nodejs/node/pull/13592)
- [[`0b8adedb88`](https://github.com/nodejs/node/commit/0b8adedb88)] - **test**: move test-debug-brk to sequential (Gibson Fahnestock) [#13580](https://github.com/nodejs/node/pull/13580)
- [[`97b6911ade`](https://github.com/nodejs/node/commit/97b6911ade)] - **test**: enable setuid/setgid test (Rich Trott) [#12403](https://github.com/nodejs/node/pull/12403)
- [[`4dff12849f`](https://github.com/nodejs/node/commit/4dff12849f)] - **test,doc**: document `crashOnUnhandledRejection()` (Anna Henningsen) [#12699](https://github.com/nodejs/node/pull/12699)
- [[`7e6a956a29`](https://github.com/nodejs/node/commit/7e6a956a29)] - **test,lib,doc**: use function declarations (Rich Trott) [#12711](https://github.com/nodejs/node/pull/12711)
- [[`910fa50e0e`](https://github.com/nodejs/node/commit/910fa50e0e)] - **tools**: fix error in custom ESLint rule (Rich Trott) [#13758](https://github.com/nodejs/node/pull/13758)
- [[`bb74da309c`](https://github.com/nodejs/node/commit/bb74da309c)] - **tools**: apply stricter indentation rules to tools (Rich Trott) [#13758](https://github.com/nodejs/node/pull/13758)
- [[`04934b04c3`](https://github.com/nodejs/node/commit/04934b04c3)] - **tools**: fix indentation in required-modules.js (Rich Trott) [#13758](https://github.com/nodejs/node/pull/13758)
- [[`550577749f`](https://github.com/nodejs/node/commit/550577749f)] - **tools**: remove no-useless-regex-char-class-escape (Rich Trott) [#10561](https://github.com/nodejs/node/pull/10561)
- [[`4ffe804c81`](https://github.com/nodejs/node/commit/4ffe804c81)] - **tools**: update ESLint to v4.0.0 (Rich Trott) [#13645](https://github.com/nodejs/node/pull/13645)
- [[`fb214bbcff`](https://github.com/nodejs/node/commit/fb214bbcff)] - **tools**: be explicit about including key-id (Myles Borins) [#13309](https://github.com/nodejs/node/pull/13309)
- [[`f831015928`](https://github.com/nodejs/node/commit/f831015928)] - **tools**: update certdata.txt (Ben Noordhuis) [#13279](https://github.com/nodejs/node/pull/13279)
- [[`bc2e73a05f`](https://github.com/nodejs/node/commit/bc2e73a05f)] - **tools**: update certdata.txt (Ben Noordhuis) [#12402](https://github.com/nodejs/node/pull/12402)
- [[`99da83b54d`](https://github.com/nodejs/node/commit/99da83b54d)] - **tools**: relax lint rule for regexps (Rich Trott) [#12807](https://github.com/nodejs/node/pull/12807)
- [[`3d564a4ed1`](https://github.com/nodejs/node/commit/3d564a4ed1)] - **tools**: require function declarations (Rich Trott) [#12711](https://github.com/nodejs/node/pull/12711)
- [[`6afa5fe348`](https://github.com/nodejs/node/commit/6afa5fe348)] - **tools**: add table parsing capability to the doctool (Roman Reiss) [#9532](https://github.com/nodejs/node/pull/9532)
- [[`9c67032b9a`](https://github.com/nodejs/node/commit/9c67032b9a)] - **tools**: enforce two arguments in assert.throws (Michaël Zasso) [#12270](https://github.com/nodejs/node/pull/12270)
- [[`95d13d59e4`](https://github.com/nodejs/node/commit/95d13d59e4)] - **tools**: remove unused code from test.py (Rich Trott) [#12806](https://github.com/nodejs/node/pull/12806)
- [[`70e9058a8e`](https://github.com/nodejs/node/commit/70e9058a8e)] - **tools**: ignore node_trace.\*.log (Daijiro Wachi) [#12754](https://github.com/nodejs/node/pull/12754)
- [[`61427471af`](https://github.com/nodejs/node/commit/61427471af)] - **tools**: replace custom assert.fail lint rule (Rich Trott) [#12287](https://github.com/nodejs/node/pull/12287)
- [[`b2a08fb130`](https://github.com/nodejs/node/commit/b2a08fb130)] - **tools**: replace custom new-with-error rule (Rich Trott) [#12249](https://github.com/nodejs/node/pull/12249)
- [[`beb8485998`](https://github.com/nodejs/node/commit/beb8485998)] - **tools**: fix lint issue in doctool (Roman Reiss) [#11658](https://github.com/nodejs/node/pull/11658)
- [[`d9a8f80c0d`](https://github.com/nodejs/node/commit/d9a8f80c0d)] - **v8**: fix build errors with g++ 7 (Zuzana Svetlikova) [#12392](https://github.com/nodejs/node/pull/12392)
- [[`8b3aacc96a`](https://github.com/nodejs/node/commit/8b3aacc96a)] - **vm**: fix race condition with timeout param (Marcel Laverdet) [#13074](https://github.com/nodejs/node/pull/13074)
- [[`6e60c838c9`](https://github.com/nodejs/node/commit/6e60c838c9)] - **vm**: fix displayErrors in runIn.. functions (Marcel Laverdet) [#13074](https://github.com/nodejs/node/pull/13074)
- [[`55cbe24c60`](https://github.com/nodejs/node/commit/55cbe24c60)] - **zlib**: fix node crashing on invalid options (Alexey Orlenko) [#13098](https://github.com/nodejs/node/pull/13098)

Windows 32-bit Installer: https://nodejs.org/dist/v6.11.2/node-v6.11.2-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v6.11.2/node-v6.11.2-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v6.11.2/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v6.11.2/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v6.11.2/node-v6.11.2.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v6.11.2/node-v6.11.2-darwin-x64.tar.gz \
Linux 32-bit Binary: https://nodejs.org/dist/v6.11.2/node-v6.11.2-linux-x86.tar.xz \
Linux 64-bit Binary: https://nodejs.org/dist/v6.11.2/node-v6.11.2-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v6.11.2/node-v6.11.2-linux-ppc64le.tar.xz \
Linux PPC BE 64-bit Binary: https://nodejs.org/dist/v6.11.2/node-v6.11.2-linux-ppc64.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v6.11.2/node-v6.11.2-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v6.11.2/node-v6.11.2-aix-ppc64.tar.gz \
SmartOS 32-bit Binary: https://nodejs.org/dist/v6.11.2/node-v6.11.2-sunos-x86.tar.xz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v6.11.2/node-v6.11.2-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v6.11.2/node-v6.11.2-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v6.11.2/node-v6.11.2-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v6.11.2/node-v6.11.2-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v6.11.2/node-v6.11.2.tar.gz \
Other release files: https://nodejs.org/dist/v6.11.2/ \
Documentation: https://nodejs.org/docs/v6.11.2/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

79ead2addfa70ea471406f48917530822e9214acd2351d1f02401b3c4dd7a34a  node-v6.11.2-aix-ppc64.tar.gz
810669aa5f812d02679c121c123a2b200e55abaa5a27aacf00b571f3cf3727d1  node-v6.11.2-darwin-x64.tar.gz
a2e81b5f60bd8a6de90e37c70d024fe61b1caa54dbe2f076a10abe673f490073  node-v6.11.2-darwin-x64.tar.xz
781273b2ae4dd489a04b16c58a4a10d30a5697e7e8bff90832a07208e3d31237  node-v6.11.2-headers.tar.gz
86d677881976c49cb80c6449564b027b3fe577c9f63c6f5111c648018f2aa6cf  node-v6.11.2-headers.tar.xz
914465dd907d2d785a6cb2e166ada7ce1e070f212267ce88ba7a326fe549a076  node-v6.11.2-linux-arm64.tar.gz
c94e2cf7a68c34b2c93af2388b57f71e730a0736cec80b37ad8700210835f7a4  node-v6.11.2-linux-arm64.tar.xz
bc44d3110f9c810a3018abd23627ddd1acfcbb83e8916647ee1435a4e7aa4fa2  node-v6.11.2-linux-armv6l.tar.gz
67e559617317aa85f0dfe3d8b95bc1461946f9835cebea4c1486807a87ef7b80  node-v6.11.2-linux-armv6l.tar.xz
0e758f5a87eb64bfb623d431cc31a50d1800158d83ab1a5d2f4c6b0d4140e850  node-v6.11.2-linux-armv7l.tar.gz
bc5d8f54c68ef1b7596dac4c793a30c44da747a1c41c99edcdbfb3f17be465ff  node-v6.11.2-linux-armv7l.tar.xz
095d0411667b00518f2ff0ea5eb17e01bfe17fe58b8eb8648e0be87bb89d8fb9  node-v6.11.2-linux-ppc64le.tar.gz
166ac9725a1e66cb79a21c42845b3c2ab55c7980fe5e21c3a95d5abce8f28a6c  node-v6.11.2-linux-ppc64le.tar.xz
e2a6501f8af6fcf8446d61084baf06a00c8ae728a012aef6d31fe9e16a39a8e2  node-v6.11.2-linux-ppc64.tar.gz
c25682fdb0749724659763a04dff1abe3182379cc2c05584bf7190497036e097  node-v6.11.2-linux-ppc64.tar.xz
a6a5a6cdcc03fc45467c8bda99b0932776495065dd8b096b680ba17ec0dd478d  node-v6.11.2-linux-s390x.tar.gz
8e566c2345618ec1acd982a7743968038ee0db16303e0f2af77bf8f2f7bc16eb  node-v6.11.2-linux-s390x.tar.xz
1ca74833ff79e6a3a713a88bba8e7f5f5cda5d4008a6ffeb2293a1bf98f83e04  node-v6.11.2-linux-x64.tar.gz
d8e209417b6e69d2c77d662c59d5b082da6d2290c846ca89af9c1239bb7c3626  node-v6.11.2-linux-x64.tar.xz
8fc3878009af0892330fbf1337d6bb0627016baff6581d2fea6ec21225be5149  node-v6.11.2-linux-x86.tar.gz
93b1a4dae0249a191e98a4841d860531c378ee2451cbdb6a2ac913304de3c3ae  node-v6.11.2-linux-x86.tar.xz
5b1a1b465dccf7c1718a312aa03c0fbb80630238cf5b484870fd06ad677e9246  node-v6.11.2.pkg
b767372038624ea92322c32c13a808446525d3053921ea83c5d23d5da23c6438  node-v6.11.2-sunos-x64.tar.gz
cd4883ce896097540559c918a171734fab8ff77095740f0a65cf474828e9b72c  node-v6.11.2-sunos-x64.tar.xz
00561dbf15bccd16e29670f84ccc03905507d31e6434e6a72a1f4a6211fae90e  node-v6.11.2-sunos-x86.tar.gz
21536664e7de358e28d5b52e7f6e070cfc1c376b1ff7e67b438727a14be25746  node-v6.11.2-sunos-x86.tar.xz
20146ed51b638404665737ed8a25cc06e96d7d7259eb90a4bdec4730a78002a6  node-v6.11.2.tar.gz
04af4992238b19124ea56f1bcfda36827613a24eb3b00fc3b50f261a415a26e4  node-v6.11.2.tar.xz
9189de5ef26e40bc77c2f999368a136c8dfb13d16298c0037a7cd2135adee4be  node-v6.11.2-win-x64.7z
b8a7f49baece10b01a51145edc95d5093e385e9e598a38f2e66c247caf6d0286  node-v6.11.2-win-x64.zip
e38c74a43f72cce05caeaf77afb49f6979345b7a891abdcd5d9e4b72d34cc710  node-v6.11.2-win-x86.7z
111631fa1f27b03bc96e3a400460713c79b7f24c924fc0a090983ed207ed8ef7  node-v6.11.2-win-x86.zip
ce57b050e11b3b8d46e18c85512384c8e2b1c0bd9f7832ba9786e175152a02a0  node-v6.11.2-x64.msi
7799df1770f0574c3fbc60343ed77a7823b3104910c9a5ebcb3e08c1e8c8eb28  node-v6.11.2-x86.msi
0640f37d16374247e2ebb2ce8347b93d517fbb69592f7ccf4707a9addc01555c  win-x64/node.exe
a7cc4c74c7281b4dc8ef40e13ce54eff2db4622af07c00b854b368a58938b8f4  win-x64/node.lib
32c279e7997c51cccc65dd445fbecc2c4e2312bc21b22193283a0500c361959a  win-x64/node_pdb.7z
cff0bfadb446b2e49db97916a6cf07f38bdf58e3981c1dd7809e7b5d63255ea9  win-x64/node_pdb.zip
0966b01b8bb9dc4fb294a1b5705c693006b54bdb9a369898e172642fe4ecda2e  win-x86/node.exe
b525d499e708bbf0df6ee04f4381911851ea4ebe374d194a639824e8bd3acf79  win-x86/node.lib
db6cc8f48362502dfb06b9c5e3abc2ded4da1e6981ed157030630a831f9e8800  win-x86/node_pdb.7z
0b3546a91f65688237fd47f5393ebffa2f487ee49828a2f4b7532ea5b368879c  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQEcBAEBCAAGBQJZg0h7AAoJEJM7AfQLXKlGFkwIAI9liIOhS6nfTcS35jmmiAKW
Jkvnob5NHtCIwc83kjnUkcIbTtn0d5GHivLwwjqErVWYE5pv5zt9vCZ7WTY0Tr6K
LEXkgDwDbSI0O321f/2x4KfrXFBAZGVj8MjHNKD4p22rf1mfqBkv2G85g6SsuADa
HCX/ivU1QhgluPWFfanon3xtiAErwYS50+8lmbjkKofYpgTdQCP//sksTV5R8QWa
vbNytDIDxhxJNyNOh94cTqcinEkqQJiCFgb9i1y2X8yz7Og8S0nPwqWKVupMYYC+
0bxSiW3UNccoD6kQ9Oh0lBzmNW1kVZ4RRcsSfz5+m5/WFEzYrfxayizn2xDbaQk=
=mzK1
-----END PGP SIGNATURE-----

```
