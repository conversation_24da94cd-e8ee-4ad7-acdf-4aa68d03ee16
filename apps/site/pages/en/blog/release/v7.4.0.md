---
date: '2017-01-04T18:46:14.549Z'
category: release
title: Node v7.4.0 (Current)
layout: blog-post
author: <PERSON>
---

### Notable changes

- **buffer**:
  - Improve performance of Buffer allocation by ~11%. (<PERSON>) [#10443](https://github.com/nodejs/node/pull/10443)
  - Improve performance of Buffer.from() by ~50%. (<PERSON>) [#10443](https://github.com/nodejs/node/pull/10443)
- **events**: Improve performance of `EventEmitter.once()` by ~27%. (<PERSON>) [#10445](https://github.com/nodejs/node/pull/10445)
- **fs**: Allow passing Uint8Array to fs methods where Buffers are supported. (<PERSON>) [#10382](https://github.com/nodejs/node/pull/10382)
- **http**: Improve performance of http server by ~7%. (<PERSON>) [#6533](https://github.com/nodejs/node/pull/6533)
- **npm**: Upgrade to v4.0.5 (<PERSON>) [#10330](https://github.com/nodejs/node/pull/10330)

### Commits

- [[`d1843ec3a7`](https://github.com/nodejs/node/commit/d1843ec3a7)] - **async_wrap**: clear destroy_ids vector (Trevor Norris) [#10400](https://github.com/nodejs/node/pull/10400)
- [[`6a4e6e9a42`](https://github.com/nodejs/node/commit/6a4e6e9a42)] - **benchmark**: allow benchmarks to specify flags (Joyee Cheung) [#10448](https://github.com/nodejs/node/pull/10448)
- [[`0b2bc5e27b`](https://github.com/nodejs/node/commit/0b2bc5e27b)] - **benchmark**: add benchmark for WHATWG URL properties (Joyee Cheung) [#10408](https://github.com/nodejs/node/pull/10408)
- [[`10b3297e8f`](https://github.com/nodejs/node/commit/10b3297e8f)] - **benchmark**: use commas in non-csv rate output (Brian White) [#10360](https://github.com/nodejs/node/pull/10360)
- [[`6d15e7b528`](https://github.com/nodejs/node/commit/6d15e7b528)] - **benchmark**: refactor buffer benchmarks (Troy Connor) [#10175](https://github.com/nodejs/node/pull/10175)
- [[`797495a84a`](https://github.com/nodejs/node/commit/797495a84a)] - **buffer**: improve allocation performance (Brian White) [#10443](https://github.com/nodejs/node/pull/10443)
- [[`ad5ae922ce`](https://github.com/nodejs/node/commit/ad5ae922ce)] - **build**: add /opt/freeware/... to AIX library path (Stewart X Addison) [#10128](https://github.com/nodejs/node/pull/10128)
- [[`cff57be2b6`](https://github.com/nodejs/node/commit/cff57be2b6)] - **build**: add (not) cross-compiled configure flags (Jesús Leganés-Combarro 'piranna) [#10287](https://github.com/nodejs/node/pull/10287)
- [[`80e798e324`](https://github.com/nodejs/node/commit/80e798e324)] - **crypto**: use CHECK_NE instead of ABORT or abort (Sam Roberts) [#10413](https://github.com/nodejs/node/pull/10413)
- [[`92eacdb5c6`](https://github.com/nodejs/node/commit/92eacdb5c6)] - **(SEMVER-MINOR)** **deps**: upgrade npm to 4.0.5 (Kat Marchán) [#10330](https://github.com/nodejs/node/pull/10330)
- [[`785975d922`](https://github.com/nodejs/node/commit/785975d922)] - **deps**: ICU 58.2 bump download URL (Steven R. Loomis) [#10206](https://github.com/nodejs/node/pull/10206)
- [[`bce0013dd8`](https://github.com/nodejs/node/commit/bce0013dd8)] - **deps**: ICU 58.2 bump (Steven R. Loomis) [#10206](https://github.com/nodejs/node/pull/10206)
- [[`dcc20f12a6`](https://github.com/nodejs/node/commit/dcc20f12a6)] - **doc**: clarify the statement in vm.createContext() (AnnaMag) [#10519](https://github.com/nodejs/node/pull/10519)
- [[`8e78953c88`](https://github.com/nodejs/node/commit/8e78953c88)] - **doc**: add joyeecheung to collaborators (Joyee Cheung) [#10603](https://github.com/nodejs/node/pull/10603)
- [[`d08463a9e3`](https://github.com/nodejs/node/commit/d08463a9e3)] - **doc**: unify dirname and filename description (Sam Roberts) [#10527](https://github.com/nodejs/node/pull/10527)
- [[`7ad0f7bc32`](https://github.com/nodejs/node/commit/7ad0f7bc32)] - **doc**: redirect 'Start a Working Group' to TSC repo (William Kapke) [#9655](https://github.com/nodejs/node/pull/9655)
- [[`deb0917f76`](https://github.com/nodejs/node/commit/deb0917f76)] - **doc**: warn about unvalidated input in child_process (Matthew Garrett) [#10466](https://github.com/nodejs/node/pull/10466)
- [[`96c3c65a86`](https://github.com/nodejs/node/commit/96c3c65a86)] - **doc**: require two-factor authentication (Rich Trott) [#10529](https://github.com/nodejs/node/pull/10529)
- [[`a7c12fef6c`](https://github.com/nodejs/node/commit/a7c12fef6c)] - **doc**: add Working Group dissolution text (William Kapke) [#9656](https://github.com/nodejs/node/pull/9656)
- [[`e86bf27fe8`](https://github.com/nodejs/node/commit/e86bf27fe8)] - **doc**: improve rinfo object documentation (Matt Crummey) [#10050](https://github.com/nodejs/node/pull/10050)
- [[`5b7b457643`](https://github.com/nodejs/node/commit/5b7b457643)] - **doc**: add tls.DEFAULT_ECDH_CURVE (Sam Roberts) [#10264](https://github.com/nodejs/node/pull/10264)
- [[`cf3f75f6f0`](https://github.com/nodejs/node/commit/cf3f75f6f0)] - **doc**: fixup errors in stream.md (Fumiya KARASAWA) [#10411](https://github.com/nodejs/node/pull/10411)
- [[`89fb82214f`](https://github.com/nodejs/node/commit/89fb82214f)] - **doc**: use "Node.js" in V8 guide (Rich Trott) [#10438](https://github.com/nodejs/node/pull/10438)
- [[`aabaef0aa7`](https://github.com/nodejs/node/commit/aabaef0aa7)] - **doc**: more efficient example in the console.md (Vse Mozhet Byt) [#10451](https://github.com/nodejs/node/pull/10451)
- [[`3d181ce4fd`](https://github.com/nodejs/node/commit/3d181ce4fd)] - **doc**: var -> const / let in the console.md (Vse Mozhet Byt) [#10451](https://github.com/nodejs/node/pull/10451)
- [[`9ce28ec3c5`](https://github.com/nodejs/node/commit/9ce28ec3c5)] - **doc**: add the valid link for curl(1) in repl.md (Vse Mozhet Byt) [#10244](https://github.com/nodejs/node/pull/10244)
- [[`cffbfba4df`](https://github.com/nodejs/node/commit/cffbfba4df)] - **doc**: replace anonymous functions in repl.md (Vse Mozhet Byt) [#10244](https://github.com/nodejs/node/pull/10244)
- [[`f281b190d5`](https://github.com/nodejs/node/commit/f281b190d5)] - **doc**: fix a function name in repl.md (Vse Mozhet Byt) [#10244](https://github.com/nodejs/node/pull/10244)
- [[`b8e2711ddd`](https://github.com/nodejs/node/commit/b8e2711ddd)] - **doc**: fix an output example in repl.md (Vse Mozhet Byt) [#10244](https://github.com/nodejs/node/pull/10244)
- [[`ae61232493`](https://github.com/nodejs/node/commit/ae61232493)] - **doc**: white space unification in repl.md (Vse Mozhet Byt) [#10244](https://github.com/nodejs/node/pull/10244)
- [[`37cb971c65`](https://github.com/nodejs/node/commit/37cb971c65)] - **doc**: var => let / const in repl.md (Vse Mozhet Byt) [#10244](https://github.com/nodejs/node/pull/10244)
- [[`6f8c6133e3`](https://github.com/nodejs/node/commit/6f8c6133e3)] - **doc**: update CONTRIBUTING.MD with link to V8 guide (sarahmeyer) [#10070](https://github.com/nodejs/node/pull/10070)
- [[`8a9d68ad7c`](https://github.com/nodejs/node/commit/8a9d68ad7c)] - **doc**: improve common.mustCall() explanation (Rich Trott) [#10390](https://github.com/nodejs/node/pull/10390)
- [[`4365bb45b8`](https://github.com/nodejs/node/commit/4365bb45b8)] - **doc**: consistent 'Returns:' part two (Myles Borins) [#10391](https://github.com/nodejs/node/pull/10391)
- [[`21fca4bdda`](https://github.com/nodejs/node/commit/21fca4bdda)] - **doc**: require() tries first core not native modules (Vicente Jimenez Aguilar) [#10324](https://github.com/nodejs/node/pull/10324)
- [[`6284d83092`](https://github.com/nodejs/node/commit/6284d83092)] - **doc**: clarify macosx-firewall suggestion BUILDING (Chase Starr) [#10311](https://github.com/nodejs/node/pull/10311)
- [[`0c4cf24f70`](https://github.com/nodejs/node/commit/0c4cf24f70)] - **doc**: update process.versions.modules documentation (Kevin Zurawel) [#9901](https://github.com/nodejs/node/pull/9901)
- [[`b67879f6f4`](https://github.com/nodejs/node/commit/b67879f6f4)] - **doc**: clarify the review and landing process (Joyee Cheung) [#10202](https://github.com/nodejs/node/pull/10202)
- [[`9044423bb6`](https://github.com/nodejs/node/commit/9044423bb6)] - **doc**: modernize code examples in the cluster.md (Vse Mozhet Byt) [#10270](https://github.com/nodejs/node/pull/10270)
- [[`2eec9afdb1`](https://github.com/nodejs/node/commit/2eec9afdb1)] - **doc**: add Michaël Zasso to the CTC (Michaël Zasso)
- [[`85d2a2abcf`](https://github.com/nodejs/node/commit/85d2a2abcf)] - **doc**: update writable.write return value (Tanuja-Sawant) [#9468](https://github.com/nodejs/node/pull/9468)
- [[`37563fafca`](https://github.com/nodejs/node/commit/37563fafca)] - **doc**: fix broken link in COLLABORATOR_GUIDE.md (Emanuel Buholzer) [#10337](https://github.com/nodejs/node/pull/10337)
- [[`f9a5c13ff3`](https://github.com/nodejs/node/commit/f9a5c13ff3)] - **dtrace**: resolve conversion warnings from SLURP_INT (Christopher J. Brody) [#10143](https://github.com/nodejs/node/pull/10143)
- [[`bc379fda75`](https://github.com/nodejs/node/commit/bc379fda75)] - **events**: optimize arrayClone by copying forward (Benedikt Meurer) [#10571](https://github.com/nodejs/node/pull/10571)
- [[`7ece950ffe`](https://github.com/nodejs/node/commit/7ece950ffe)] - **events**: improve once() performance (Brian White) [#10445](https://github.com/nodejs/node/pull/10445)
- [[`6629f8f83f`](https://github.com/nodejs/node/commit/6629f8f83f)] - **fs**: cache non-symlinks in realpathSync. (Jeremy Yallop) [#10253](https://github.com/nodejs/node/pull/10253)
- [[`abde7644a5`](https://github.com/nodejs/node/commit/abde7644a5)] - **(SEMVER-MINOR)** **fs**: support Uint8Array input to methods (Anna Henningsen) [#10382](https://github.com/nodejs/node/pull/10382)
- [[`32b6bcdd83`](https://github.com/nodejs/node/commit/32b6bcdd83)] - **http**: optimize headers iteration (Brian White) [#6533](https://github.com/nodejs/node/pull/6533)
- [[`a760d707ad`](https://github.com/nodejs/node/commit/a760d707ad)] - **http**: simplify boolean checks (Brian White) [#6533](https://github.com/nodejs/node/pull/6533)
- [[`c8ad127abc`](https://github.com/nodejs/node/commit/c8ad127abc)] - **http**: extract validation functions (Brian White) [#6533](https://github.com/nodejs/node/pull/6533)
- [[`8a2a763f13`](https://github.com/nodejs/node/commit/8a2a763f13)] - **http**: improve validation performance (Brian White) [#6533](https://github.com/nodejs/node/pull/6533)
- [[`df8b8b257d`](https://github.com/nodejs/node/commit/df8b8b257d)] - **http**: refactor server connection handling (Brian White) [#6533](https://github.com/nodejs/node/pull/6533)
- [[`1f0fd7b35d`](https://github.com/nodejs/node/commit/1f0fd7b35d)] - **http**: misc cleanup and minor optimizations (Brian White) [#6533](https://github.com/nodejs/node/pull/6533)
- [[`b094b49659`](https://github.com/nodejs/node/commit/b094b49659)] - **http**: reuse existing headers array for raw values (Brian White) [#6533](https://github.com/nodejs/node/pull/6533)
- [[`4bed9475d1`](https://github.com/nodejs/node/commit/4bed9475d1)] - **inspector**: fix Coverity defects (Eugene Ostroukhov) [#10240](https://github.com/nodejs/node/pull/10240)
- [[`023956187e`](https://github.com/nodejs/node/commit/023956187e)] - **inspector**: split HTTP/WS server from the inspector (Eugene Ostroukhov) [#9630](https://github.com/nodejs/node/pull/9630)
- [[`aed5e27451`](https://github.com/nodejs/node/commit/aed5e27451)] - **lib**: avoid recompilation of anonymous functions (Brian White) [#6533](https://github.com/nodejs/node/pull/6533)
- [[`064607be58`](https://github.com/nodejs/node/commit/064607be58)] - **meta**: modify pull request template for prepending (Rich Trott) [#10484](https://github.com/nodejs/node/pull/10484)
- [[`75efdeb635`](https://github.com/nodejs/node/commit/75efdeb635)] - **os**: fix os.release() for aix and add test (jBarz) [#10245](https://github.com/nodejs/node/pull/10245)
- [[`6796bf4829`](https://github.com/nodejs/node/commit/6796bf4829)] - **repl**: allow autocompletion for scoped packages (Evan Lucas) [#10296](https://github.com/nodejs/node/pull/10296)
- [[`11ed8007df`](https://github.com/nodejs/node/commit/11ed8007df)] - **src**: describe what NODE_MODULE_VERSION is for (Sam Roberts) [#10414](https://github.com/nodejs/node/pull/10414)
- [[`5e5b1f8b89`](https://github.com/nodejs/node/commit/5e5b1f8b89)] - **src**: return early if nextTickQueue is empty (Trevor Norris) [#10274](https://github.com/nodejs/node/pull/10274)
- [[`5852336207`](https://github.com/nodejs/node/commit/5852336207)] - **test**: add tests for clearBuffer state machine (Safia Abdalla) [#9922](https://github.com/nodejs/node/pull/9922)
- [[`6ec798bdd6`](https://github.com/nodejs/node/commit/6ec798bdd6)] - **test**: update test-cluster-shared-handle-bind-error (cjihrig) [#10547](https://github.com/nodejs/node/pull/10547)
- [[`32401b5069`](https://github.com/nodejs/node/commit/32401b5069)] - **test**: avoid assigning this to variables (cjihrig) [#10548](https://github.com/nodejs/node/pull/10548)
- [[`e1fbd72ae7`](https://github.com/nodejs/node/commit/e1fbd72ae7)] - **test**: s/ASSERT/assert/ (cjihrig) [#10544](https://github.com/nodejs/node/pull/10544)
- [[`05b0092230`](https://github.com/nodejs/node/commit/05b0092230)] - **test**: refactor test-debugger-remote (Sakthipriyan Vairamani (thefourtheye)) [#10455](https://github.com/nodejs/node/pull/10455)
- [[`82575f9341`](https://github.com/nodejs/node/commit/82575f9341)] - **test**: refactor test-stream-unshift-read-race (Rich Trott) [#10532](https://github.com/nodejs/node/pull/10532)
- [[`4d984ecadb`](https://github.com/nodejs/node/commit/4d984ecadb)] - **test**: refactor test-stream-pipe-error-handling (Rich Trott) [#10530](https://github.com/nodejs/node/pull/10530)
- [[`2619236212`](https://github.com/nodejs/node/commit/2619236212)] - **test**: refactor test-tls-alert-handling (Rich Trott) [#10482](https://github.com/nodejs/node/pull/10482)
- [[`8ac9d07805`](https://github.com/nodejs/node/commit/8ac9d07805)] - **test**: fix flaky test-http-client-timeout-with-data (Rich Trott) [#10431](https://github.com/nodejs/node/pull/10431)
- [[`ef5a43a0e3`](https://github.com/nodejs/node/commit/ef5a43a0e3)] - **test**: improve test-http-allow-req-after-204-res (Adrian Estrada) [#10503](https://github.com/nodejs/node/pull/10503)
- [[`4a16f9b054`](https://github.com/nodejs/node/commit/4a16f9b054)] - **test**: improve test-fs-empty-readStream.js (Adrian Estrada) [#10479](https://github.com/nodejs/node/pull/10479)
- [[`5fc93ee841`](https://github.com/nodejs/node/commit/5fc93ee841)] - **test**: refactor the code in test-http-connect (Adrian Estrada) [#10397](https://github.com/nodejs/node/pull/10397)
- [[`78e8aa81c9`](https://github.com/nodejs/node/commit/78e8aa81c9)] - **test**: refactor test-stream-pipe-after-end (Rich Trott) [#10483](https://github.com/nodejs/node/pull/10483)
- [[`0a0c190db5`](https://github.com/nodejs/node/commit/0a0c190db5)] - **test**: use strictEqual in test-http-server (Fabrice Tatieze) [#10478](https://github.com/nodejs/node/pull/10478)
- [[`04d82a5122`](https://github.com/nodejs/node/commit/04d82a5122)] - **test**: refactor test-stdin-from-file (Rob Adelmann) [#10331](https://github.com/nodejs/node/pull/10331)
- [[`00f791af74`](https://github.com/nodejs/node/commit/00f791af74)] - **test**: refactor test-stream2-unpipe-drain (Chris Story) [#10033](https://github.com/nodejs/node/pull/10033)
- [[`eb1adbb48e`](https://github.com/nodejs/node/commit/eb1adbb48e)] - **test**: refactor the code in test-dns-ipv4 (Adrian Estrada) [#10200](https://github.com/nodejs/node/pull/10200)
- [[`dff48af67f`](https://github.com/nodejs/node/commit/dff48af67f)] - **test**: add regex to text-crypto-random (Nate) [#10020](https://github.com/nodejs/node/pull/10020)
- [[`5164b56224`](https://github.com/nodejs/node/commit/5164b56224)] - **test**: add test for SIGWINCH handling by stdio.js (Sarah Meyer) [#10063](https://github.com/nodejs/node/pull/10063)
- [[`1aa3ab1ec6`](https://github.com/nodejs/node/commit/1aa3ab1ec6)] - **test**: refactor the code in test-fs-chmod (Adrian Estrada) [#10440](https://github.com/nodejs/node/pull/10440)
- [[`4f1d9452de`](https://github.com/nodejs/node/commit/4f1d9452de)] - **test**: swap var for let/const throughout (Paul Graham) [#10177](https://github.com/nodejs/node/pull/10177)
- [[`f6ed233546`](https://github.com/nodejs/node/commit/f6ed233546)] - **test**: improve the code in test-pipe.js (Adrian Estrada) [#10452](https://github.com/nodejs/node/pull/10452)
- [[`011bd4675a`](https://github.com/nodejs/node/commit/011bd4675a)] - **test**: improve code in test-fs-readfile-error (Adrian Estrada) [#10367](https://github.com/nodejs/node/pull/10367)
- [[`98fcb221d5`](https://github.com/nodejs/node/commit/98fcb221d5)] - **test**: improve code in test-vm-preserves-property (Adrian Estrada) [#10428](https://github.com/nodejs/node/pull/10428)
- [[`cdf028c5a6`](https://github.com/nodejs/node/commit/cdf028c5a6)] - **test**: improve code in test-vm-symbols (Adrian Estrada) [#10429](https://github.com/nodejs/node/pull/10429)
- [[`94a894acf2`](https://github.com/nodejs/node/commit/94a894acf2)] - **test**: fix and improve debugger-client test (Sakthipriyan Vairamani (thefourtheye)) [#10371](https://github.com/nodejs/node/pull/10371)
- [[`d4c888df88`](https://github.com/nodejs/node/commit/d4c888df88)] - **test**: basic functionality of readUIntLE() (larissayvette) [#10359](https://github.com/nodejs/node/pull/10359)
- [[`a5b8d097c5`](https://github.com/nodejs/node/commit/a5b8d097c5)] - **test**: clean up repl-reset-event file (Kailean Courtney) [#9931](https://github.com/nodejs/node/pull/9931)
- [[`599a2a956b`](https://github.com/nodejs/node/commit/599a2a956b)] - **test**: refactor test-child-process-ipc (malen) [#9990](https://github.com/nodejs/node/pull/9990)
- [[`d33e560929`](https://github.com/nodejs/node/commit/d33e560929)] - **test**: fix and improve debug-break-on-uncaught (Sakthipriyan Vairamani (thefourtheye)) [#10370](https://github.com/nodejs/node/pull/10370)
- [[`9349f086d9`](https://github.com/nodejs/node/commit/9349f086d9)] - **test**: refactor test-internal-modules (Christy Leung) [#10016](https://github.com/nodejs/node/pull/10016)
- [[`2ad9faa19e`](https://github.com/nodejs/node/commit/2ad9faa19e)] - **test**: add second argument to assert.throws() (Ken Russo) [#9987](https://github.com/nodejs/node/pull/9987)
- [[`4bfd9c0a35`](https://github.com/nodejs/node/commit/4bfd9c0a35)] - **test**: refactor test-pipe-file-to-http (Josh Mays) [#10054](https://github.com/nodejs/node/pull/10054)
- [[`1b9f548e7d`](https://github.com/nodejs/node/commit/1b9f548e7d)] - **test**: refactor test-tls-interleave (Brian Chirgwin) [#10017](https://github.com/nodejs/node/pull/10017)
- [[`db3ac5d6e7`](https://github.com/nodejs/node/commit/db3ac5d6e7)] - **test**: refactor test-tls-client-getephemeralkeyinfo (Harish Tejwani) [#9954](https://github.com/nodejs/node/pull/9954)
- [[`bbe618d3e2`](https://github.com/nodejs/node/commit/bbe618d3e2)] - **test**: refactor test-cluster-send-handle-twice.js (Amar Zavery) [#10049](https://github.com/nodejs/node/pull/10049)
- [[`5d64f3d76f`](https://github.com/nodejs/node/commit/5d64f3d76f)] - **test**: update test-tls-check-server-identity.js (Kevin Cox) [#9986](https://github.com/nodejs/node/pull/9986)
- [[`e6702d6d9b`](https://github.com/nodejs/node/commit/e6702d6d9b)] - **test**: fix flaky test-https-timeout (Rich Trott) [#10404](https://github.com/nodejs/node/pull/10404)
- [[`44f4d6001f`](https://github.com/nodejs/node/commit/44f4d6001f)] - **test**: improve test-cluster-net-listen.js (Rico Cai) [#9953](https://github.com/nodejs/node/pull/9953)
- [[`d3bef30b5f`](https://github.com/nodejs/node/commit/d3bef30b5f)] - **test**: refactor test-child-process-stdin (Segu Riluvan) [#10420](https://github.com/nodejs/node/pull/10420)
- [[`e9b2325d68`](https://github.com/nodejs/node/commit/e9b2325d68)] - **test**: test error messages in test-dns-regress-7070 (Wallace Zhang) [#10058](https://github.com/nodejs/node/pull/10058)
- [[`826decf8e5`](https://github.com/nodejs/node/commit/826decf8e5)] - **test**: basic functionality of readUIntBE() (larissayvette) [#10417](https://github.com/nodejs/node/pull/10417)
- [[`91a2dc216d`](https://github.com/nodejs/node/commit/91a2dc216d)] - **test**: improve test-cluster-worker-constructor.js (Adrian Estrada) [#10396](https://github.com/nodejs/node/pull/10396)
- [[`a82be5d44c`](https://github.com/nodejs/node/commit/a82be5d44c)] - **test**: refactor test-init.js (Sakthipriyan Vairamani (thefourtheye)) [#10384](https://github.com/nodejs/node/pull/10384)
- [[`ed76bfa7ba`](https://github.com/nodejs/node/commit/ed76bfa7ba)] - **test**: refactor code in test-cluster-http-pipe (Adrian Estrada) [#10297](https://github.com/nodejs/node/pull/10297)
- [[`9a0711d37f`](https://github.com/nodejs/node/commit/9a0711d37f)] - **test**: improve code in test-http-bind-twice.js (Adrian Estrada) [#10318](https://github.com/nodejs/node/pull/10318)
- [[`9d0220c4de`](https://github.com/nodejs/node/commit/9d0220c4de)] - **test**: fix linter error in whatwg-url-parsing (Sakthipriyan Vairamani (thefourtheye)) [#10421](https://github.com/nodejs/node/pull/10421)
- [[`bee7d7e32c`](https://github.com/nodejs/node/commit/bee7d7e32c)] - **test**: change var declarations, add mustCall check (Daniel Sims) [#9962](https://github.com/nodejs/node/pull/9962)
- [[`a2ec794d3b`](https://github.com/nodejs/node/commit/a2ec794d3b)] - **test**: added validation regex argument to test (Avery, Frank) [#9918](https://github.com/nodejs/node/pull/9918)
- [[`14826d0569`](https://github.com/nodejs/node/commit/14826d0569)] - **test**: refactoring test-cluster-worker-constructor (Christopher Rokita) [#9956](https://github.com/nodejs/node/pull/9956)
- [[`274eef4da0`](https://github.com/nodejs/node/commit/274eef4da0)] - **test**: refactoring test-pipe-head (Travis Bretton) [#10036](https://github.com/nodejs/node/pull/10036)
- [[`7c406e819b`](https://github.com/nodejs/node/commit/7c406e819b)] - **test**: refactor test-stdin-script-child (Emanuel Buholzer) [#10321](https://github.com/nodejs/node/pull/10321)
- [[`501165f0c1`](https://github.com/nodejs/node/commit/501165f0c1)] - **test**: fix timers-same-timeout-wrong-list-deleted (Rich Trott) [#10362](https://github.com/nodejs/node/pull/10362)
- [[`ba63363512`](https://github.com/nodejs/node/commit/ba63363512)] - **test**: refactor test-stream2-writable (Rich Trott) [#10353](https://github.com/nodejs/node/pull/10353)
- [[`a5a738cca7`](https://github.com/nodejs/node/commit/a5a738cca7)] - **test**: refactor test-tls-0-dns-altname (Richard Karmazin) [#9948](https://github.com/nodejs/node/pull/9948)
- [[`12a3b189da`](https://github.com/nodejs/node/commit/12a3b189da)] - **test**: refactor test-cluster-net-listen (Segu Riluvan) [#10047](https://github.com/nodejs/node/pull/10047)
- [[`18a75a085d`](https://github.com/nodejs/node/commit/18a75a085d)] - **test**: test: refactor test-sync-fileread (Jason Wohlgemuth) [#9941](https://github.com/nodejs/node/pull/9941)
- [[`815b5bdcf4`](https://github.com/nodejs/node/commit/815b5bdcf4)] - **test**: change assert.strict to assert.strictEqual() (Ashita Nagesh) [#9988](https://github.com/nodejs/node/pull/9988)
- [[`f1cc0a4d26`](https://github.com/nodejs/node/commit/f1cc0a4d26)] - **test**: add regex check in test-buffer-bad-overload (Sam Shull) [#10038](https://github.com/nodejs/node/pull/10038)
- [[`0684211d12`](https://github.com/nodejs/node/commit/0684211d12)] - **test**: refactor the code in test-http-keep-alive (Adrian Estrada) [#10350](https://github.com/nodejs/node/pull/10350)
- [[`a815a23631`](https://github.com/nodejs/node/commit/a815a23631)] - **test**: improve domain-top-level-error-handler-throw (CodeVana) [#9950](https://github.com/nodejs/node/pull/9950)
- [[`3448e8e522`](https://github.com/nodejs/node/commit/3448e8e522)] - **test**: use strictEqual in test-cwd-enoent-repl.js (Neeraj Sharma) [#9952](https://github.com/nodejs/node/pull/9952)
- [[`fc2fd920ab`](https://github.com/nodejs/node/commit/fc2fd920ab)] - **test**: refactor test-net-reconnect-error (Duy Le) [#9903](https://github.com/nodejs/node/pull/9903)
- [[`a7c9c5685e`](https://github.com/nodejs/node/commit/a7c9c5685e)] - **test**: add test-require-invalid-package (Duy Le) [#9903](https://github.com/nodejs/node/pull/9903)
- [[`d1b4c5dc61`](https://github.com/nodejs/node/commit/d1b4c5dc61)] - **test**: refactor test-child-process-kill (Duy Le) [#9903](https://github.com/nodejs/node/pull/9903)
- [[`2f92945a70`](https://github.com/nodejs/node/commit/2f92945a70)] - **test**: use consistent block spacing (Rich Trott) [#10377](https://github.com/nodejs/node/pull/10377)
- [[`9a9e530291`](https://github.com/nodejs/node/commit/9a9e530291)] - **test**: add known_issues test for #5350 (AnnaMag) [#10319](https://github.com/nodejs/node/pull/10319)
- [[`76b0e5bfbe`](https://github.com/nodejs/node/commit/76b0e5bfbe)] - **test**: refactor test-timers-this (Rich Trott) [#10315](https://github.com/nodejs/node/pull/10315)
- [[`797d9a8e79`](https://github.com/nodejs/node/commit/797d9a8e79)] - **tools**: refactor json.js (Rich Trott) [#10442](https://github.com/nodejs/node/pull/10442)
- [[`05332942e2`](https://github.com/nodejs/node/commit/05332942e2)] - **tools**: enforce linebreak after ternary operators (Michaël Zasso) [#10213](https://github.com/nodejs/node/pull/10213)
- [[`3a7b63b81b`](https://github.com/nodejs/node/commit/3a7b63b81b)] - **tools**: enable block-spacing rule in .eslintrc (Rich Trott) [#10377](https://github.com/nodejs/node/pull/10377)
- [[`3195fb45ae`](https://github.com/nodejs/node/commit/3195fb45ae)] - **url**: set toStringTag for the URL class (James M Snell) [#10562](https://github.com/nodejs/node/pull/10562)
- [[`659d522d7c`](https://github.com/nodejs/node/commit/659d522d7c)] - **url**: fix accidental filemode change (James M Snell) [#10549](https://github.com/nodejs/node/pull/10549)
- [[`6977224059`](https://github.com/nodejs/node/commit/6977224059)] - **url**: fix URL query update if searchParams changes (Michaël Zasso) [#10486](https://github.com/nodejs/node/pull/10486)
- [[`78e867492a`](https://github.com/nodejs/node/commit/78e867492a)] - **url**: improve spec compliance of WHATWG URL (Michaël Zasso) [#10317](https://github.com/nodejs/node/pull/10317)
- [[`2b98ea0dec`](https://github.com/nodejs/node/commit/2b98ea0dec)] - **url**: move originFor, domainToAscii and domainToUnicode (James M Snell) [#10512](https://github.com/nodejs/node/pull/10512)
- [[`e210efad9e`](https://github.com/nodejs/node/commit/e210efad9e)] - **url**: performance improvement in URL implementation (James M Snell) [#10469](https://github.com/nodejs/node/pull/10469)
- [[`7fbd12f876`](https://github.com/nodejs/node/commit/7fbd12f876)] - **url**: make WHATWG URL properties spec compliant (Joyee Cheung) [#10408](https://github.com/nodejs/node/pull/10408)
- [[`495213e545`](https://github.com/nodejs/node/commit/495213e545)] - **url**: mark ignored return value in node::url::Parse(...) (Christopher J. Brody) [#10141](https://github.com/nodejs/node/pull/10141)
- [[`ba46374cb9`](https://github.com/nodejs/node/commit/ba46374cb9)] - **watchdog**: add flag to mark handler as disabled (Bartosz Sosnowski) [#10248](https://github.com/nodejs/node/pull/10248)

Windows 32-bit Installer: https://nodejs.org/dist/v7.4.0/node-v7.4.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v7.4.0/node-v7.4.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v7.4.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v7.4.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v7.4.0/node-v7.4.0.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v7.4.0/node-v7.4.0-darwin-x64.tar.gz \
Linux 32-bit Binary: https://nodejs.org/dist/v7.4.0/node-v7.4.0-linux-x86.tar.xz \
Linux 64-bit Binary: https://nodejs.org/dist/v7.4.0/node-v7.4.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v7.4.0/node-v7.4.0-linux-ppc64le.tar.xz \
Linux PPC BE 64-bit Binary: https://nodejs.org/dist/v7.4.0/node-v7.4.0-linux-ppc64.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v7.4.0/node-v7.4.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v7.4.0/node-v7.4.0-aix-ppc64.tar.gz \
SmartOS 32-bit Binary: https://nodejs.org/dist/v7.4.0/node-v7.4.0-sunos-x86.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v7.4.0/node-v7.4.0-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v7.4.0/node-v7.4.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v7.4.0/node-v7.4.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v7.4.0/node-v7.4.0.tar.gz \
Other release files: https://nodejs.org/dist/v7.4.0/ \
Documentation: https://nodejs.org/docs/v7.4.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

4128cd6fb9e8b844e6b12aaa67fce875434ec8682dd3955c9193dfb69c6cddbf  node-v7.4.0-aix-ppc64.tar.gz
72158cac53f01d1794fa56e75c8c637a9ae3072b339d9221a2bbf7f0744c80bb  node-v7.4.0-darwin-x64.tar.gz
0d987095559666c46dd895eb4b4dac6497e02070586bca8252b624150bb36206  node-v7.4.0-darwin-x64.tar.xz
d2496d9350a6e27fdebf1bb62f3976b68eb8aa8c49eb40fd032b61147d5ab954  node-v7.4.0-headers.tar.gz
9c8521147ff287e7f3c5d16349bf7b5803bac63bd7580f92eb7dd78499d1ddf8  node-v7.4.0-headers.tar.xz
0de18242da7e54a0e69673b58b39268141309937998a9ab3c2c0453fb988b3d7  node-v7.4.0-linux-arm64.tar.gz
585634fd393c4b25998b001b10dcc61fd2b32f9991b7258e56302ea356389380  node-v7.4.0-linux-arm64.tar.xz
99f186fcf3fae8cba0a5bc1b5ce6da72fbc4cc12070b49a60f54af382a3e647d  node-v7.4.0-linux-armv6l.tar.gz
aceaadbff60734b502ed5c137c44612ddf13cd5ffe11b648ff63efe86b32690c  node-v7.4.0-linux-armv6l.tar.xz
d9d7e93e251365555cb8f156538d914ff24f9b3eaacc34d73caf1e90e569ce5e  node-v7.4.0-linux-armv7l.tar.gz
b840bd079e3608407eb27cc171f7d2d895e2af3a197478095b2fc8081cbf2aaa  node-v7.4.0-linux-armv7l.tar.xz
c472b4401a7f6ade6f36b28c88752c447fb6becc3215b880bd698ab721b57d23  node-v7.4.0-linux-ppc64le.tar.gz
8da4d00466569538702a2eba1bc550e04d259cf622418e072b5dfc83ad436636  node-v7.4.0-linux-ppc64le.tar.xz
63954764756dd244626f1d0d2279834cd20edf3f756cbe68aa28d65cb572a35e  node-v7.4.0-linux-ppc64.tar.gz
1359b9f884dcfabd96df1195e7e64b837fc29983bd74c536d2fe405071a021d3  node-v7.4.0-linux-ppc64.tar.xz
d37119ecbe47f25dd36d6849628126afc3f963d6807a6208caeb24a4fc2d2d8f  node-v7.4.0-linux-s390x.tar.gz
3031bb23bbef706833d78d5f26c2e7187e2949f603498211ba61d7c194b0c15b  node-v7.4.0-linux-s390x.tar.xz
8f663492bd288c8f8d978fad61ac412ea648476e2223346a7326180d937171fa  node-v7.4.0-linux-x64.tar.gz
c847251538579d605ac391c5e282ad40b2ead0414df7699f58781d9e6e80248a  node-v7.4.0-linux-x64.tar.xz
f5168076800374ca88cca09f235d1fff1703ae12080d9578b213403e482f48e6  node-v7.4.0-linux-x86.tar.gz
4a9eca1751cb8d3ac6e24a7d7eb1f715a01d6e3e3c09215d5ccd903c9ead4a88  node-v7.4.0-linux-x86.tar.xz
c2dc012f5d8c58116ac2916e3961461db4dddacf63e19cff6d9787afd2e1169b  node-v7.4.0.pkg
c6a5ed2b4203492c713d80d781e766763fd592eca9ea51a14978f473c525df4b  node-v7.4.0-sunos-x86.tar.gz
b61b9de0ad60a92ff38b8a34b9b3df9d29c6c8d98ada8bf70333bfd7382b28f8  node-v7.4.0-sunos-x86.tar.xz
69b76c86e6fc9914fa136089d8c28a4828c14aa8792cbdf946090a5a2afd25b6  node-v7.4.0.tar.gz
9f15b916f8677ec1615c46bdd6d2208ed1b24fad26384f9ac249f5b09d31c32b  node-v7.4.0.tar.xz
3d554cab9253a2e6d561138a456fb4cb2ae39a8209476c85ddae497fbebeadd7  node-v7.4.0-win-x64.7z
01739fabdec4fb63eff761022f7b9a2d241430d2c9a5755a07a39e48b54bb471  node-v7.4.0-win-x64.zip
a6d8374b35de86cb1d4dc693aec2f88d303435420f1ac6bc1aeb3f6363f258ab  node-v7.4.0-win-x86.7z
3eba71c006b3e49dc527f217bd3bbb6ee0f77d58a64bd5362128bc3475a0d900  node-v7.4.0-win-x86.zip
ad9e1312d951f7f00e6dd003141b06230f0296ca81752208d89fb464cdfcfbb1  node-v7.4.0-x64.msi
ae6751ece1f451bd0ca1c395fdddb33ab97e96dfc29e17eba12eb4fa92000f93  node-v7.4.0-x86.msi
77a2f5c5997e75b5c681c628473b8c0b208d00a170e4235fd1e18c6af9b8d123  win-x64/node.exe
d6d183126a47374319cab454dd7f8b711275c7cdbfc69d63a35161c85888c71f  win-x64/node.lib
788d503721d0c40e0ad61191f42bff5fb2d8633a74ddc06724efd2b12a2f747b  win-x64/node_pdb.7z
62bb6929867ee341f1066d810aabea66191ece12cf176949637fafd3d23b43a4  win-x64/node_pdb.zip
083cf59abb4880c40b053cbbc77ff796f991bbc26e570ceb9abaa88fed431261  win-x86/node.exe
aee6086700746f51a2b2757628c15dad195c32e5cc8c1bb50294968925d335a2  win-x86/node.lib
d8bf9ac8af9cde524eb7810dd21818b2f6b1cf14e29c625c6717ac405d5766e1  win-x86/node_pdb.7z
3482d6d6dbe15423821b3bda3aa4e72d5bdd85436cf9baad45070cc0bd31066b  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----
Comment: GPGTools - https://gpgtools.org

iQIcBAEBCAAGBQJYbUJiAAoJELY7U1pMIGyp89AP/jIfXteVtROwPOjkE0geHBo3
oJU86UWvGzqexPhQM/wd/LoJRBw0GcCqecmGYH8DXmF18StGjiK0dN9TEMUeWE1i
y7kY6GszQ1V2wEnhA78WOlpDMjCurtZkAKVqEAIge4q9iOuVGO2KT4lxLUSPaq+1
91NTizdEsI0m1Es/19CukJms65Il2S69JrqwGB2UGoSbHwDaD4SVeFZBpJiZaUZ+
LnP423D047rhZfBJsD7J1eWQODmTfrLaTeZPDI9GfWliaWix+m0CZwx+XiimxW57
NmDeBT/GdlSzD3+v3PKSdKS7oy1Gy6qFpMGabBwKF5JXJboQOzSzLnBX6a4Opo/y
T1dLLaYNDFejTOlaDTp33z0f6ceysEeFOnztD3ohbO3XCRavkEN9tkoKYMwEeg1+
Z04HgZl71kmaKwB98QghHB30Zw3ONyn6KRQ+7lacwspmLIkU5G+JtnsMkw2FiQGm
cqzPMEyq6qgALnF95W/cQ6z1tkXeNsyoy0inziS/AUIMwCetk0kkfChavMGTsxcd
YnqxL3+WQlJ9hvWkjhEEQ1Q3iplL71QtS87h/4mBPC8FZJBseCJ0Q396J0Qrs9WI
trtAhiTOpc7qzV6JsPJsXm6Btes3sfwAJhRp7VvbziUjs0syAwpJpleHDc2NYM81
Ilx0nTSZxvqMDUu1qHFS
=Tho3
-----END PGP SIGNATURE-----

```
