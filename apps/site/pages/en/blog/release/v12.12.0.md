---
date: '2019-10-11T22:00:19.251Z'
category: release
title: Node v12.12.0 (Current)
layout: blog-post
author: <PERSON><PERSON>
---

### Notable changes

- **build**:
  - Add `--force-context-aware` flag to prevent usage of native node addons that aren't context aware [#29631](https://github.com/nodejs/node/pull/29631)
- **deprecations**:
  - Add documentation-only deprecation for `process._tickCallback()` [#29781](https://github.com/nodejs/node/pull/29781)
- **esm**:
  - Using JSON modules is experimental again [#29754](https://github.com/nodejs/node/pull/29754)
- **fs**:
  - Introduce `opendir()` and `fs.Dir` to iterate through directories [#29349](https://github.com/nodejs/node/pull/29349)
- **process**:
  - Add source-map support to stack traces by using `--enable-source-maps`[#29564](https://github.com/nodejs/node/pull/29564)
- **tls**:
  - Honor `pauseOnConnect` option [#29635](https://github.com/nodejs/node/pull/29635)
  - Add option for private keys for OpenSSL engines [#28973](https://github.com/nodejs/node/pull/28973)

### Commits

- [[`d09f2b4170`](https://github.com/nodejs/node/commit/d09f2b4170)] - **build**: build docs on Travis (Richard Lau) [#29783](https://github.com/nodejs/node/pull/29783)
- [[`03ec4cea30`](https://github.com/nodejs/node/commit/03ec4cea30)] - **build**: do not link against librt on linux (Sam Roberts) [#29727](https://github.com/nodejs/node/pull/29727)
- [[`f91778d2c7`](https://github.com/nodejs/node/commit/f91778d2c7)] - **build**: remove unused libatomic on ppc64, s390x (Sam Roberts) [#29727](https://github.com/nodejs/node/pull/29727)
- [[`ab4c53e0ef`](https://github.com/nodejs/node/commit/ab4c53e0ef)] - **crypto**: remove arbitrary UTF16 restriction (Anna Henningsen) [#29795](https://github.com/nodejs/node/pull/29795)
- [[`75f3b28d67`](https://github.com/nodejs/node/commit/75f3b28d67)] - **crypto**: refactor array buffer view validation (Ruben Bridgewater) [#29683](https://github.com/nodejs/node/pull/29683)
- [[`5eb013b854`](https://github.com/nodejs/node/commit/5eb013b854)] - **deps**: update archs files for OpenSSL-1.1.1 (Sam Roberts) [#29550](https://github.com/nodejs/node/pull/29550)
- [[`1766cfcb9e`](https://github.com/nodejs/node/commit/1766cfcb9e)] - **deps**: upgrade openssl sources to 1.1.1d (Sam Roberts) [#29550](https://github.com/nodejs/node/pull/29550)
- [[`3d88b76680`](https://github.com/nodejs/node/commit/3d88b76680)] - **deps**: patch V8 to 7.7.299.13 (Michaël Zasso) [#29869](https://github.com/nodejs/node/pull/29869)
- [[`600478ac13`](https://github.com/nodejs/node/commit/600478ac13)] - **dgram**: use `uv_udp_try_send()` (Anna Henningsen) [#29832](https://github.com/nodejs/node/pull/29832)
- [[`ea6b6abb91`](https://github.com/nodejs/node/commit/ea6b6abb91)] - **doc**: remove spaces inside code span elements (Nick Schonning) [#29329](https://github.com/nodejs/node/pull/29329)
- [[`20b9ef92d1`](https://github.com/nodejs/node/commit/20b9ef92d1)] - **doc**: add more info to fs.Dir and fix typos (Jeremiah Senkpiel) [#29890](https://github.com/nodejs/node/pull/29890)
- [[`f566cd5801`](https://github.com/nodejs/node/commit/f566cd5801)] - **doc**: remove misleading paragraph about the Legacy URL API (Jakob Krigovsky) [#29844](https://github.com/nodejs/node/pull/29844)
- [[`a5c2154534`](https://github.com/nodejs/node/commit/a5c2154534)] - **doc**: add explicit bracket for markdown reference links (Nick Schonning) [#29808](https://github.com/nodejs/node/pull/29808)
- [[`ea9bf4a666`](https://github.com/nodejs/node/commit/ea9bf4a666)] - **doc**: implement minor CSS improvements (XhmikosR) [#29669](https://github.com/nodejs/node/pull/29669)
- [[`a0498606a0`](https://github.com/nodejs/node/commit/a0498606a0)] - **doc**: fix return type for crypto.createDiffieHellmanGroup() (exoego) [#29696](https://github.com/nodejs/node/pull/29696)
- [[`a00cd17b9e`](https://github.com/nodejs/node/commit/a00cd17b9e)] - **doc**: reuse link indexes for n-api.md (legendecas) [#29787](https://github.com/nodejs/node/pull/29787)
- [[`aea0253697`](https://github.com/nodejs/node/commit/aea0253697)] - **doc**: unify place of stability notes (Vse Mozhet Byt) [#29799](https://github.com/nodejs/node/pull/29799)
- [[`8b4f210bf5`](https://github.com/nodejs/node/commit/8b4f210bf5)] - **doc**: add missing deprecation code (cjihrig) [#29820](https://github.com/nodejs/node/pull/29820)
- [[`bede98128f`](https://github.com/nodejs/node/commit/bede98128f)] - **doc**: remove reference to stale CITGM job (Michael Dawson) [#29774](https://github.com/nodejs/node/pull/29774)
- [[`014eb67117`](https://github.com/nodejs/node/commit/014eb67117)] - **(SEMVER-MINOR)** **doc**: add documentation deprecation for process.\_tickCallback (Lucas Holmquist) [#29781](https://github.com/nodejs/node/pull/29781)
- [[`62370efe7e`](https://github.com/nodejs/node/commit/62370efe7e)] - **doc**: add dash between SHA and PR in changelog (Nick Schonning) [#29558](https://github.com/nodejs/node/pull/29558)
- [[`d1a4aa3ca2`](https://github.com/nodejs/node/commit/d1a4aa3ca2)] - **doc**: add missing reference link values (Nick Schonning) [#29558](https://github.com/nodejs/node/pull/29558)
- [[`de4652f55e`](https://github.com/nodejs/node/commit/de4652f55e)] - **doc**: convert old changlogs SHA links to match newer format (Nick Schonning) [#29558](https://github.com/nodejs/node/pull/29558)
- [[`60b1f6f303`](https://github.com/nodejs/node/commit/60b1f6f303)] - **doc**: complete cut off links in old changelog (Nick Schonning) [#29558](https://github.com/nodejs/node/pull/29558)
- [[`906245e1a4`](https://github.com/nodejs/node/commit/906245e1a4)] - **doc**: clarify --pending-deprecation effects on Buffer() usage (Rich Trott) [#29769](https://github.com/nodejs/node/pull/29769)
- [[`401f3e7235`](https://github.com/nodejs/node/commit/401f3e7235)] - **doc**: fix nits in dgram.md (Vse Mozhet Byt) [#29761](https://github.com/nodejs/node/pull/29761)
- [[`bc48646206`](https://github.com/nodejs/node/commit/bc48646206)] - **doc**: improve process.ppid 'added in' info (Thomas Watson) [#29772](https://github.com/nodejs/node/pull/29772)
- [[`0b46bcaaa5`](https://github.com/nodejs/node/commit/0b46bcaaa5)] - **doc**: security maintenance processes (Sam Roberts) [#29685](https://github.com/nodejs/node/pull/29685)
- [[`f39259c079`](https://github.com/nodejs/node/commit/f39259c079)] - **doc**: remove redundant escape (XhmikosR) [#29716](https://github.com/nodejs/node/pull/29716)
- [[`87fb1c297a`](https://github.com/nodejs/node/commit/87fb1c297a)] - **errors**: make sure all Node.js errors show their properties (Ruben Bridgewater) [#29677](https://github.com/nodejs/node/pull/29677)
- [[`df218ce066`](https://github.com/nodejs/node/commit/df218ce066)] - **_Revert_** "**esm**: remove experimental status from JSON modules" (Guy Bedford) [#29754](https://github.com/nodejs/node/pull/29754)
- [[`e7f604f495`](https://github.com/nodejs/node/commit/e7f604f495)] - **esm**: remove proxy for builtin exports (Bradley Farias) [#29737](https://github.com/nodejs/node/pull/29737)
- [[`c56f765cf6`](https://github.com/nodejs/node/commit/c56f765cf6)] - **fs**: remove options.encoding from Dir.read\*() (Jeremiah Senkpiel) [#29908](https://github.com/nodejs/node/pull/29908)
- [[`b76a2e502c`](https://github.com/nodejs/node/commit/b76a2e502c)] - **(SEMVER-MINOR)** **fs**: introduce `opendir()` and `fs.Dir` (Jeremiah Senkpiel) [#29349](https://github.com/nodejs/node/pull/29349)
- [[`2bcde8309c`](https://github.com/nodejs/node/commit/2bcde8309c)] - **(SEMVER-MINOR)** **http2**: allow passing FileHandle to respondWithFD (Anna Henningsen) [#29876](https://github.com/nodejs/node/pull/29876)
- [[`a240d45d1a`](https://github.com/nodejs/node/commit/a240d45d1a)] - **http2**: support passing options of http2.connect to net.connect (ZYSzys) [#29816](https://github.com/nodejs/node/pull/29816)
- [[`3f153789b5`](https://github.com/nodejs/node/commit/3f153789b5)] - **http2**: set default maxConcurrentStreams (ZYSzys) [#29833](https://github.com/nodejs/node/pull/29833)
- [[`6a989da6a0`](https://github.com/nodejs/node/commit/6a989da6a0)] - **http2**: use the latest settings (ZYSzys) [#29780](https://github.com/nodejs/node/pull/29780)
- [[`b2cce13235`](https://github.com/nodejs/node/commit/b2cce13235)] - **inspector**: update faviconUrl (dokugo) [#29562](https://github.com/nodejs/node/pull/29562)
- [[`60296a3612`](https://github.com/nodejs/node/commit/60296a3612)] - **lib**: make tick processor detect xcodebuild errors (Ben Noordhuis) [#29830](https://github.com/nodejs/node/pull/29830)
- [[`9e5d691ee4`](https://github.com/nodejs/node/commit/9e5d691ee4)] - **lib**: introduce no-mixed-operators eslint rule to lib (ZYSzys) [#29834](https://github.com/nodejs/node/pull/29834)
- [[`74a69abd12`](https://github.com/nodejs/node/commit/74a69abd12)] - **lib**: stop using prepareStackTrace (Gus Caplan) [#29777](https://github.com/nodejs/node/pull/29777)
- [[`90562ae356`](https://github.com/nodejs/node/commit/90562ae356)] - **module**: use v8 synthetic modules (Guy Bedford) [#29846](https://github.com/nodejs/node/pull/29846)
- [[`20896f74d6`](https://github.com/nodejs/node/commit/20896f74d6)] - **n-api,doc**: clarify napi_finalize related APIs (legendecas) [#29797](https://github.com/nodejs/node/pull/29797)
- [[`65c475269e`](https://github.com/nodejs/node/commit/65c475269e)] - **net**: emit close on unconnected socket (Robert Nagy) [#29803](https://github.com/nodejs/node/pull/29803)
- [[`ae8b2b4ab7`](https://github.com/nodejs/node/commit/ae8b2b4ab7)] - **(SEMVER-MINOR)** **process**: add source-map support to stack traces (bcoe) [#29564](https://github.com/nodejs/node/pull/29564)
- [[`3f6ce39acf`](https://github.com/nodejs/node/commit/3f6ce39acf)] - **src**: fix ESM path resolution on Windows (Thomas) [#29574](https://github.com/nodejs/node/pull/29574)
- [[`6bfe8f47fa`](https://github.com/nodejs/node/commit/6bfe8f47fa)] - **(SEMVER-MINOR)** **src**: add buildflag to force context-aware addons (Shelley Vohr) [#29631](https://github.com/nodejs/node/pull/29631)
- [[`6c75cc1b11`](https://github.com/nodejs/node/commit/6c75cc1b11)] - **stream**: do not deadlock duplexpair (Robert Nagy) [#29836](https://github.com/nodejs/node/pull/29836)
- [[`320f649539`](https://github.com/nodejs/node/commit/320f649539)] - **stream**: add comment about undocumented API (Robert Nagy) [#29805](https://github.com/nodejs/node/pull/29805)
- [[`5fdf4a474f`](https://github.com/nodejs/node/commit/5fdf4a474f)] - **test**: remove extra process.exit() (cjihrig) [#29873](https://github.com/nodejs/node/pull/29873)
- [[`6a5d401f30`](https://github.com/nodejs/node/commit/6a5d401f30)] - **test**: remove spaces inside code span elements (Nick Schonning) [#29329](https://github.com/nodejs/node/pull/29329)
- [[`adee99883a`](https://github.com/nodejs/node/commit/adee99883a)] - **test**: debug output for dlopen-ping-pong test (Sam Roberts) [#29818](https://github.com/nodejs/node/pull/29818)
- [[`b309e20661`](https://github.com/nodejs/node/commit/b309e20661)] - **test**: add test for HTTP server response with Connection: close (Austin Wright) [#29836](https://github.com/nodejs/node/pull/29836)
- [[`bf1727a3f3`](https://github.com/nodejs/node/commit/bf1727a3f3)] - **test**: add test for writable.write() argument types (Robert Nagy) [#29746](https://github.com/nodejs/node/pull/29746)
- [[`3153dd6766`](https://github.com/nodejs/node/commit/3153dd6766)] - **test**: well-defined DH groups now verify clean (Sam Roberts) [#29550](https://github.com/nodejs/node/pull/29550)
- [[`690a863aaa`](https://github.com/nodejs/node/commit/690a863aaa)] - **test**: simplify force-context-aware test (cjihrig) [#29705](https://github.com/nodejs/node/pull/29705)
- [[`54ef0fd010`](https://github.com/nodejs/node/commit/54ef0fd010)] - **(SEMVER-MINOR)** **test**: --force-context-aware cli flag (Shelley Vohr) [#29631](https://github.com/nodejs/node/pull/29631)
- [[`a7b56a5b01`](https://github.com/nodejs/node/commit/a7b56a5b01)] - **(SEMVER-MINOR)** **tls**: honor pauseOnConnect option (Robert Jensen) [#29635](https://github.com/nodejs/node/pull/29635)
- [[`cf7b4056ca`](https://github.com/nodejs/node/commit/cf7b4056ca)] - **(SEMVER-MINOR)** **tls**: add option for private keys for OpenSSL engines (Anton Gerasimov) [#28973](https://github.com/nodejs/node/pull/28973)
- [[`ba4946a520`](https://github.com/nodejs/node/commit/ba4946a520)] - **tools**: prohibit Error.prepareStackTrace() usage (Ruben Bridgewater) [#29827](https://github.com/nodejs/node/pull/29827)
- [[`79f6cd3606`](https://github.com/nodejs/node/commit/79f6cd3606)] - **tools**: update ESLint to v6.5.1 (Rich Trott) [#29785](https://github.com/nodejs/node/pull/29785)
- [[`6d88f0fef7`](https://github.com/nodejs/node/commit/6d88f0fef7)] - **vm**: refactor SourceTextModule (Gus Caplan) [#29776](https://github.com/nodejs/node/pull/29776)
- [[`a7113048e3`](https://github.com/nodejs/node/commit/a7113048e3)] - **worker**: do not use two-arg NewIsolate (Shelley Vohr) [#29850](https://github.com/nodejs/node/pull/29850)

Windows 32-bit Installer: https://nodejs.org/dist/v12.12.0/node-v12.12.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v12.12.0/node-v12.12.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v12.12.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v12.12.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v12.12.0/node-v12.12.0.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v12.12.0/node-v12.12.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v12.12.0/node-v12.12.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v12.12.0/node-v12.12.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v12.12.0/node-v12.12.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v12.12.0/node-v12.12.0-aix-ppc64.tar.gz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v12.12.0/node-v12.12.0-sunos-x64.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v12.12.0/node-v12.12.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v12.12.0/node-v12.12.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v12.12.0/node-v12.12.0.tar.gz \
Other release files: https://nodejs.org/dist/v12.12.0/ \
Documentation: https://nodejs.org/docs/v12.12.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

b9ccb43e955656ad9c1653428890ce74a7a7ac25dad0218e9b4a259e57fc0d6f  node-v12.12.0-aix-ppc64.tar.gz
14a98237e8859bc22695719dbc2e9db5529a33ada0c6c377df4dc27b5622ffbb  node-v12.12.0-darwin-x64.tar.gz
3848414d2566c22a64e2c5b30f4852f7066c284e5e69538cc91f21d897c87958  node-v12.12.0-darwin-x64.tar.xz
c38a3c07e36a6ec8a0d1e2a15aa5f064a7ef85a6cc5f876d228bb8440d910e19  node-v12.12.0-headers.tar.gz
912b00574abbd67a74f5a9b6575b86d613e04965809b44ea08861eb41e8f9bc2  node-v12.12.0-headers.tar.xz
9c6ff84ad3be0fc79188cfa68b3fab9aedadf219ffb3821d4bf6d01308ed6621  node-v12.12.0-linux-arm64.tar.gz
ad536aba218df64d8e901b28985f4c5f72f634bf903c449b0c84929f6940853c  node-v12.12.0-linux-arm64.tar.xz
1251d6bffaf95fd88ca3bbeb83c9e6eb99b1b60022f9160092afc813b1440ca7  node-v12.12.0-linux-armv7l.tar.gz
7f483b2fcc24d8cd96fc1499c819c22a72aea5d36b2f052146b1eccbefbebb83  node-v12.12.0-linux-armv7l.tar.xz
8135e437f9537c1072a392718c4494037edde1a1eba44dd28c87002ba16b6d38  node-v12.12.0-linux-ppc64le.tar.gz
a916ca07a460845fcdc1fcc4947f6c1d7bc0f6fbc27f472ce440b8c039097df1  node-v12.12.0-linux-ppc64le.tar.xz
511ceb963f079dc5c74528bb1018819ad0fdd00c98fb9782ff22fe2c22fd10f1  node-v12.12.0-linux-s390x.tar.gz
2b8bf5fa4c481b5a4c390271b373aef7dd549ca88928bc665f1201c7df611470  node-v12.12.0-linux-s390x.tar.xz
4b46ffa368bc909fae2611a16daffd1d8c35a5284aea0bb7c45269e72f6638a2  node-v12.12.0-linux-x64.tar.gz
e3a38dfaf1233a3c43c2528869af52e74575781984369a5b705c89d84dfa3ac2  node-v12.12.0-linux-x64.tar.xz
5239deac3a94cd872a5c360a0564f13ea6f5c01f1cc712c0040dfd535e4d4253  node-v12.12.0-sunos-x64.tar.gz
34136fa774ac6c5a10e976fd0c3e5004b9913c8e8486d418fc50d253bb57f870  node-v12.12.0-sunos-x64.tar.xz
867adf371171a8c8bf5a35821b222819a9d163dac718c3c0ef2e436555aed2aa  node-v12.12.0-win-x64.7z
930e7bcd2ae5bcb1d4163c2adf09a392ed0e9a824b069d19daeb4f9f3430a195  node-v12.12.0-win-x64.zip
d1625994a5baf5f90b62526a5d0a421d96279bcded02271bbeb03b09bc037b85  node-v12.12.0-win-x86.7z
aec1a9a9b0bdca80527405da9fa2dbec7e81e5b30d7be4846e43e256ea8fecfa  node-v12.12.0-win-x86.zip
08c89078fe9875712394940854f2cd541a82ed2bf7d4b9b37977fd0de7df7d04  node-v12.12.0-x64.msi
5f8bd359699b16906330598cc57e6c3959b8539a43eab7f253519954a5c7f4a6  node-v12.12.0-x86.msi
e6d1988c241fef87d9672742a924c94ecbcefa2fe622c937791853eaf652ddaa  node-v12.12.0.pkg
6ce681625c09bc2b2b5757165580e648579e4bc7bce5e246fa6339270eec8bde  node-v12.12.0.tar.gz
d9fa875aba9764f1b919115ce1d5e828b02c2a55a6e9e1b6fc771bb6b24a8cc1  node-v12.12.0.tar.xz
10f7a97bd49079ccc567a514e676bdcc1e876398a3065474b5a1e220bc873354  win-x64/node.exe
f359d9b3ebad0748cf1713990da39f8363dbc1df899d02aead3f38b6730c9821  win-x64/node.lib
b8264e68ddedb6e526e015a0b3125ebb5f45c59e671708949f4ab6e5d35fb7e7  win-x64/node_pdb.7z
0021877bb6d58bdc6654ebf927f9a6085b7b804fde07582c130df0ab23ef6f21  win-x64/node_pdb.zip
cab789ba0dcc1039cdf2fcbf743e1647cd75c85a75b8a8b655911c155277c1f8  win-x86/node.exe
16e742472854b33130c4dd726637a225edca9a27f3427a5bb80e238ca0388872  win-x86/node.lib
de5bfeec58411304e9fa87ccbe11085929e662e1f14f5ea4524cec7336718d5c  win-x86/node_pdb.7z
3eea11edae68634f3b8c725064ae3fd1af9532d3d07d04f79cf904ec8c3a0fae  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEpIwr7mgOhBYyzU5E8HSWs+s8F2IFAl2g+vAACgkQ8HSWs+s8
F2LsDA//ZFdQ1DN5vuh2CVGDRTCVjNmBXTA3T8AfDvZuAb78SgSdtMZOPykEBui8
/DWxJCpv7a3jBlL7pVC4WvXRbm5OH2P+iyLIru01ZCNnhvrLgnmkh0KHOyOqN4fs
YWBF9QYKHWY+d7rdXfyrCnlbVRpjV+UCTaImV9yoXksF2U5iGlwjUL5FgNn00yxs
hZf/eY7r/Ub9NoV66E2+Kp6WhfOpTL4H7cKa0KPrFLjMHR5n6H1hfkFGdqSmhXT/
GYUC10xqJ+9nrVINdib/bmCklUOt5ilGXDsDYm9Z0RIcmEfKTHyYsiV0UjHy+p1a
v+WwEs+w2duIEYPLKLUsajKK2ZIT0PwuKP1fxl+fOW4EUPrDUpTpvpSSRyrhOqGl
CWEiANgvkdnocPHWfWYeAgySNsz2cFsn2ZYiJI9sgCphJJmfJ27VX71uyVGfznv5
N4AkqHNaEdhYTZTRZZ3cBPwJnyAiyXP/FjSMLBhjghxMGpwwHMfVA6yhRtgSqzI1
bq4oHpPJf3wWsqbgFLrOjOY3fP2RKcOHf/8WoSI41viT0m2PXLk4MIhm3TiCcoZv
yJKsNSqGYj0Nen3h+PdlQrIRpxTft1M5+W1vsVZ4aRe8a01W+cHAqNftHaK7bfW+
m8pBkDFYHfqrYUaAsesVDZmHZTOKedxowmJEUXSR1I5KgDTEWPs=
=QKOS
-----END PGP SIGNATURE-----

```
