---
date: '2017-08-15T20:08:12.415Z'
category: release
title: Node v8.4.0 (Current)
layout: blog-post
author: <PERSON>
---

### Notable changes

- **HTTP2**

  - Experimental support for the built-in `http2` has been added via the
    `--expose-http2` flag.
    [#14239](https://github.com/nodejs/node/pull/14239)

- **Inspector**

  - `require()` is available in the inspector console now.
    [#8837](https://github.com/nodejs/node/pull/8837)
  - Multiple contexts, as created by the `vm` module, are supported now.
    [#14465](https://github.com/nodejs/node/pull/14465)

- **N-API**

  - New APIs for creating number values have been introduced.
    [#14573](https://github.com/nodejs/node/pull/14573)

- **Stream**

  - For `Duplex` streams, the high water mark option can now be set
    independently for the readable and the writable side.
    [#14636](https://github.com/nodejs/node/pull/14636)

- **Util**
  - `util.format` now supports the `%o` and `%O` specifiers for printing
    objects.
    [#14558](https://github.com/nodejs/node/pull/14558)

### Commits

- [[`a6539ece2c`](https://github.com/nodejs/node/commit/a6539ece2c)] - **assert**: optimize code path for deepEqual Maps (Ruben Bridgewater) [#14501](https://github.com/nodejs/node/pull/14501)
- [[`2716b626b0`](https://github.com/nodejs/node/commit/2716b626b0)] - **async_hooks**: CHECK that resource is not empty (Anna Henningsen) [#14694](https://github.com/nodejs/node/pull/14694)
- [[`b3c1c6ff7f`](https://github.com/nodejs/node/commit/b3c1c6ff7f)] - **benchmark**: fix and extend assert benchmarks (Ruben Bridgewater) [#14147](https://github.com/nodejs/node/pull/14147)
- [[`139b08863e`](https://github.com/nodejs/node/commit/139b08863e)] - **benchmark**: Correct constructor for freelist (Gareth Ellis) [#14627](https://github.com/nodejs/node/pull/14627)
- [[`574cc379b9`](https://github.com/nodejs/node/commit/574cc379b9)] - **benchmark**: remove unused parameters (nishijayaraj) [#14640](https://github.com/nodejs/node/pull/14640)
- [[`fef2aa7e27`](https://github.com/nodejs/node/commit/fef2aa7e27)] - **(SEMVER-MINOR)** **deps**: add nghttp2 dependency (James M Snell) [#14239](https://github.com/nodejs/node/pull/14239)
- [[`2d806f4f71`](https://github.com/nodejs/node/commit/2d806f4f71)] - **deps**: cherry-pick f19b889 from V8 upstream (Alexey Kozyatinskiy) [#14465](https://github.com/nodejs/node/pull/14465)
- [[`dd521d0a28`](https://github.com/nodejs/node/commit/dd521d0a28)] - **deps,tools**: add missing nghttp2 license (Anna Henningsen) [#14806](https://github.com/nodejs/node/pull/14806)
- [[`621c03acfe`](https://github.com/nodejs/node/commit/621c03acfe)] - **doc**: delint (Refael Ackermann) [#14707](https://github.com/nodejs/node/pull/14707)
- [[`230cb55574`](https://github.com/nodejs/node/commit/230cb55574)] - **doc**: fix header level typo (Refael Ackermann) [#14707](https://github.com/nodejs/node/pull/14707)
- [[`af85b6e058`](https://github.com/nodejs/node/commit/af85b6e058)] - **doc**: fix http2 sample code for http2.md (Keita Akutsu) [#14667](https://github.com/nodejs/node/pull/14667)
- [[`1e7ddb200f`](https://github.com/nodejs/node/commit/1e7ddb200f)] - **doc**: explain browser support of http/2 without SSL (Gil Tayar) [#14670](https://github.com/nodejs/node/pull/14670)
- [[`be716d00cc`](https://github.com/nodejs/node/commit/be716d00cc)] - **(SEMVER-MINOR)** **doc**: include http2.md in all.md (James M Snell) [#14239](https://github.com/nodejs/node/pull/14239)
- [[`9e51802f53`](https://github.com/nodejs/node/commit/9e51802f53)] - **doc**: add missing `changes:` metadata for util (Anna Henningsen) [#14810](https://github.com/nodejs/node/pull/14810)
- [[`4811fea553`](https://github.com/nodejs/node/commit/4811fea553)] - **doc**: add missing `changes:` metadata for streams (Anna Henningsen) [#14810](https://github.com/nodejs/node/pull/14810)
- [[`20fb69063a`](https://github.com/nodejs/node/commit/20fb69063a)] - **doc**: fix docs style in util.md (Daijiro Wachi) [#14711](https://github.com/nodejs/node/pull/14711)
- [[`0de63e6888`](https://github.com/nodejs/node/commit/0de63e6888)] - **doc**: fix docs style in intl.md (Daijiro Wachi) [#14711](https://github.com/nodejs/node/pull/14711)
- [[`ee2ae0f30b`](https://github.com/nodejs/node/commit/ee2ae0f30b)] - **doc**: expanded description of buffer.slice (Vishal Bisht) [#14720](https://github.com/nodejs/node/pull/14720)
- [[`9888bb1238`](https://github.com/nodejs/node/commit/9888bb1238)] - **doc**: improve fs.read() doc text (Rich Trott) [#14631](https://github.com/nodejs/node/pull/14631)
- [[`d604173a66`](https://github.com/nodejs/node/commit/d604173a66)] - **doc**: clarify the position argument for fs.read (dcharbonnier) [#14631](https://github.com/nodejs/node/pull/14631)
- [[`d3b072276b`](https://github.com/nodejs/node/commit/d3b072276b)] - **doc**: add docs for AssertionError (Mandeep Singh) [#14261](https://github.com/nodejs/node/pull/14261)
- [[`4e15a6b76a`](https://github.com/nodejs/node/commit/4e15a6b76a)] - **doc**: fix order of AtExit callbacks in addons.md (Daniel Bevenius) [#14048](https://github.com/nodejs/node/pull/14048)
- [[`e07dfffad0`](https://github.com/nodejs/node/commit/e07dfffad0)] - **doc**: remove undef NDEBUG from addons.md (Daniel Bevenius) [#14048](https://github.com/nodejs/node/pull/14048)
- [[`c5ee34e39b`](https://github.com/nodejs/node/commit/c5ee34e39b)] - **encoding**: rudimentary TextDecoder support w/o ICU (Timothy Gu) [#14489](https://github.com/nodejs/node/pull/14489)
- [[`e0001dc601`](https://github.com/nodejs/node/commit/e0001dc601)] - **(SEMVER-MINOR)** **http**: move utcDate to internal/http.js (James M Snell) [#14239](https://github.com/nodejs/node/pull/14239)
- [[`1d40850338`](https://github.com/nodejs/node/commit/1d40850338)] - **http2**: fix \[kInspect\]() output for Http2Stream (Evan Lucas) [#14753](https://github.com/nodejs/node/pull/14753)
- [[`c5740f9111`](https://github.com/nodejs/node/commit/c5740f9111)] - **http2**: name padding buffer fields (Anna Henningsen) [#14744](https://github.com/nodejs/node/pull/14744)
- [[`8a0d101adf`](https://github.com/nodejs/node/commit/8a0d101adf)] - **http2**: use per-environment buffers (Anna Henningsen) [#14744](https://github.com/nodejs/node/pull/14744)
- [[`92c37fe5fd`](https://github.com/nodejs/node/commit/92c37fe5fd)] - **http2**: improve perf of passing headers to C++ (Anna Henningsen) [#14723](https://github.com/nodejs/node/pull/14723)
- [[`47bf705f75`](https://github.com/nodejs/node/commit/47bf705f75)] - **http2**: rename some nghttp2 stream flags (Kelvin Jin) [#14637](https://github.com/nodejs/node/pull/14637)
- [[`723d1af5e7`](https://github.com/nodejs/node/commit/723d1af5e7)] - **(SEMVER-MINOR)** **http2**: fix flakiness in timeout (James M Snell) [#14239](https://github.com/nodejs/node/pull/14239)
- [[`6a30448bac`](https://github.com/nodejs/node/commit/6a30448bac)] - **(SEMVER-MINOR)** **http2**: fix linting after rebase (James M Snell) [#14239](https://github.com/nodejs/node/pull/14239)
- [[`efd929e402`](https://github.com/nodejs/node/commit/efd929e402)] - **(SEMVER-MINOR)** **http2**: fix compilation error after V8 update (James M Snell) [#14239](https://github.com/nodejs/node/pull/14239)
- [[`f46c50b3e2`](https://github.com/nodejs/node/commit/f46c50b3e2)] - **(SEMVER-MINOR)** **http2**: add some doc detail for invalid header chars (James M Snell) [#14239](https://github.com/nodejs/node/pull/14239)
- [[`b43caf92c0`](https://github.com/nodejs/node/commit/b43caf92c0)] - **(SEMVER-MINOR)** **http2**: fix documentation errors (James M Snell) [#14239](https://github.com/nodejs/node/pull/14239)
- [[`33b03b2ab2`](https://github.com/nodejs/node/commit/33b03b2ab2)] - **(SEMVER-MINOR)** **http2**: minor cleanup (James M Snell) [#14239](https://github.com/nodejs/node/pull/14239)
- [[`174ab6fda0`](https://github.com/nodejs/node/commit/174ab6fda0)] - **(SEMVER-MINOR)** **http2**: use static allocated arrays (James M Snell) [#14239](https://github.com/nodejs/node/pull/14239)
- [[`9a4be4adc4`](https://github.com/nodejs/node/commit/9a4be4adc4)] - **(SEMVER-MINOR)** **http2**: get trailers working with the compat api (James M Snell) [#14239](https://github.com/nodejs/node/pull/14239)
- [[`3e5b07a8fb`](https://github.com/nodejs/node/commit/3e5b07a8fb)] - **(SEMVER-MINOR)** **http2**: refactor trailers API (James M Snell) [#14239](https://github.com/nodejs/node/pull/14239)
- [[`26e1f8e01c`](https://github.com/nodejs/node/commit/26e1f8e01c)] - **(SEMVER-MINOR)** **http2**: address initial pr feedback (James M Snell) [#14239](https://github.com/nodejs/node/pull/14239)
- [[`7824fa0b40`](https://github.com/nodejs/node/commit/7824fa0b40)] - **(SEMVER-MINOR)** **http2**: make writeHead behave like HTTP/1. (Matteo Collina) [#14239](https://github.com/nodejs/node/pull/14239)
- [[`b778838337`](https://github.com/nodejs/node/commit/b778838337)] - **(SEMVER-MINOR)** **http2**: doc and fixes to the Compatibility API (Matteo Collina) [#14239](https://github.com/nodejs/node/pull/14239)
- [[`8f3bbd9b68`](https://github.com/nodejs/node/commit/8f3bbd9b68)] - **(SEMVER-MINOR)** **http2**: add range support for respondWith{File|FD} (James M Snell) [#14239](https://github.com/nodejs/node/pull/14239)
- [[`61696f1215`](https://github.com/nodejs/node/commit/61696f1215)] - **(SEMVER-MINOR)** **http2**: fix socketOnTimeout and a segfault (James M Snell) [#14239](https://github.com/nodejs/node/pull/14239)
- [[`2620769e7f`](https://github.com/nodejs/node/commit/2620769e7f)] - **(SEMVER-MINOR)** **http2**: refinement and test for socketError (James M Snell) [#14239](https://github.com/nodejs/node/pull/14239)
- [[`cd0f4c6652`](https://github.com/nodejs/node/commit/cd0f4c6652)] - **(SEMVER-MINOR)** **http2**: fix abort when client.destroy inside end event (James M Snell) [#14239](https://github.com/nodejs/node/pull/14239)
- [[`e8cc193bcc`](https://github.com/nodejs/node/commit/e8cc193bcc)] - **(SEMVER-MINOR)** **http2**: fix documentation nits (James M Snell) [#14239](https://github.com/nodejs/node/pull/14239)
- [[`a49146e446`](https://github.com/nodejs/node/commit/a49146e446)] - **(SEMVER-MINOR)** **http2**: remove redundant return in test (James M Snell) [#14239](https://github.com/nodejs/node/pull/14239)
- [[`3eb61b00de`](https://github.com/nodejs/node/commit/3eb61b00de)] - **(SEMVER-MINOR)** **http2**: add tests and benchmarks (James M Snell) [#14239](https://github.com/nodejs/node/pull/14239)
- [[`9623ee0f99`](https://github.com/nodejs/node/commit/9623ee0f99)] - **(SEMVER-MINOR)** **http2**: introducing HTTP/2 (James M Snell) [#14239](https://github.com/nodejs/node/pull/14239)
- [[`029567a460`](https://github.com/nodejs/node/commit/029567a460)] - **inspector**: support extra contexts (Eugene Ostroukhov) [#14465](https://github.com/nodejs/node/pull/14465)
- [[`d89f9f82b0`](https://github.com/nodejs/node/commit/d89f9f82b0)] - **(SEMVER-MINOR)** **inspector**: allow require in Runtime.evaluate (Jan Krems) [#8837](https://github.com/nodejs/node/pull/8837)
- [[`ac1b81ad75`](https://github.com/nodejs/node/commit/ac1b81ad75)] - **lib**: move deprecationWarned var (Daniel Bevenius) [#14769](https://github.com/nodejs/node/pull/14769)
- [[`8433b1ad37`](https://github.com/nodejs/node/commit/8433b1ad37)] - **lib**: use Timer.now() in readline module (Rich Trott) [#14681](https://github.com/nodejs/node/pull/14681)
- [[`917ace283f`](https://github.com/nodejs/node/commit/917ace283f)] - **(SEMVER-MINOR)** **n-api**: add napi_get_node_version (Anna Henningsen) [#14696](https://github.com/nodejs/node/pull/14696)
- [[`5e2cce59ef`](https://github.com/nodejs/node/commit/5e2cce59ef)] - **(SEMVER-MINOR)** **n-api**: optimize number API performance (Jason Ginchereau) [#14573](https://github.com/nodejs/node/pull/14573)
- [[`c94f346b93`](https://github.com/nodejs/node/commit/c94f346b93)] - **net**: use rest parameters instead of arguments (Tobias Nießen) [#13472](https://github.com/nodejs/node/pull/13472)
- [[`1c00875747`](https://github.com/nodejs/node/commit/1c00875747)] - **repl**: include folder extensions in autocomplete (Teddy Katz) [#14727](https://github.com/nodejs/node/pull/14727)
- [[`59d1d56da6`](https://github.com/nodejs/node/commit/59d1d56da6)] - **src**: remove unused http2_socket_buffer from env (Anna Henningsen) [#14740](https://github.com/nodejs/node/pull/14740)
- [[`268a1ff3f1`](https://github.com/nodejs/node/commit/268a1ff3f1)] - **src**: mention that node options are space-separated (Gabriel Schulhof) [#14709](https://github.com/nodejs/node/pull/14709)
- [[`9237ef868e`](https://github.com/nodejs/node/commit/9237ef868e)] - **src**: avoid creating local data variable (Daniel Bevenius) [#14732](https://github.com/nodejs/node/pull/14732)
- [[`f83827d64b`](https://github.com/nodejs/node/commit/f83827d64b)] - **src**: use local isolate instead of args.GetIsolate (Daniel Bevenius) [#14768](https://github.com/nodejs/node/pull/14768)
- [[`d7d22ead2b`](https://github.com/nodejs/node/commit/d7d22ead2b)] - **src**: add comments for cares library init refcount (Anna Henningsen) [#14743](https://github.com/nodejs/node/pull/14743)
- [[`b87fae927d`](https://github.com/nodejs/node/commit/b87fae927d)] - **src**: remove duplicate loop (Anna Henningsen) [#14750](https://github.com/nodejs/node/pull/14750)
- [[`033773c17b`](https://github.com/nodejs/node/commit/033773c17b)] - **src**: add overlooked handle to cleanup (Anna Henningsen) [#14749](https://github.com/nodejs/node/pull/14749)
- [[`dd6444d401`](https://github.com/nodejs/node/commit/dd6444d401)] - **src,http2**: DRY header/trailer handling code up (Anna Henningsen) [#14688](https://github.com/nodejs/node/pull/14688)
- [[`ef8ac7b5ac`](https://github.com/nodejs/node/commit/ef8ac7b5ac)] - **(SEMVER-MINOR)** **stream**: support readable/writableHWM for Duplex (Guy Margalit) [#14636](https://github.com/nodejs/node/pull/14636)
- [[`6d9f94f93f`](https://github.com/nodejs/node/commit/6d9f94f93f)] - **test**: cover all HTTP methods that parser supports (Oky Antoro) [#14773](https://github.com/nodejs/node/pull/14773)
- [[`e4854fccfc`](https://github.com/nodejs/node/commit/e4854fccfc)] - **test**: use regular expressions in throw assertions (Vincent Xue) [#14318](https://github.com/nodejs/node/pull/14318)
- [[`66788fc4d0`](https://github.com/nodejs/node/commit/66788fc4d0)] - **test**: increase http2 coverage (Michael Albert) [#14701](https://github.com/nodejs/node/pull/14701)
- [[`dbb9c370d4`](https://github.com/nodejs/node/commit/dbb9c370d4)] - **test**: add crypto check to http2 tests (Daniel Bevenius) [#14657](https://github.com/nodejs/node/pull/14657)
- [[`97f622b99e`](https://github.com/nodejs/node/commit/97f622b99e)] - **(SEMVER-MINOR)** **test**: fix flaky test-http2-client-unescaped-path on osx (James M Snell) [#14239](https://github.com/nodejs/node/pull/14239)
- [[`9d752d5282`](https://github.com/nodejs/node/commit/9d752d5282)] - **(SEMVER-MINOR)** **test**: fix flakiness in test-http2-client-upload (James M Snell) [#14239](https://github.com/nodejs/node/pull/14239)
- [[`82c63a55ea`](https://github.com/nodejs/node/commit/82c63a55ea)] - **test**: add test-benchmark-arrays (Rich Trott) [#14728](https://github.com/nodejs/node/pull/14728)
- [[`0eab77c86f`](https://github.com/nodejs/node/commit/0eab77c86f)] - **test**: allow inspector to reopen with same port (Gibson Fahnestock) [#14320](https://github.com/nodejs/node/pull/14320)
- [[`9bbbf12827`](https://github.com/nodejs/node/commit/9bbbf12827)] - **test**: remove redundant `using` in cctest (XadillaX) [#14739](https://github.com/nodejs/node/pull/14739)
- [[`7eb9f6f6e4`](https://github.com/nodejs/node/commit/7eb9f6f6e4)] - **test**: make totalLen snake case (Daniel Bevenius) [#14765](https://github.com/nodejs/node/pull/14765)
- [[`977e22857a`](https://github.com/nodejs/node/commit/977e22857a)] - **test**: make test-tls-connect checks more strict (Rich Trott) [#14695](https://github.com/nodejs/node/pull/14695)
- [[`a781bb4508`](https://github.com/nodejs/node/commit/a781bb4508)] - **_Revert_** "**test**: disable MultipleEnvironmentsPerIsolate" (Anna Henningsen) [#14749](https://github.com/nodejs/node/pull/14749)
- [[`8ff2a5c338`](https://github.com/nodejs/node/commit/8ff2a5c338)] - **_Revert_** "**test**: add DISABLED\_ prefix to commented out test" (Anna Henningsen) [#14749](https://github.com/nodejs/node/pull/14749)
- [[`0bc3124c80`](https://github.com/nodejs/node/commit/0bc3124c80)] - **test**: properly order freeing resources in cctest (Anna Henningsen) [#14749](https://github.com/nodejs/node/pull/14749)
- [[`3f1bb0a551`](https://github.com/nodejs/node/commit/3f1bb0a551)] - **test**: split out load-sensitive readline tests (Rich Trott) [#14681](https://github.com/nodejs/node/pull/14681)
- [[`5d99d7dff2`](https://github.com/nodejs/node/commit/5d99d7dff2)] - **test**: add block scoping to test-readline-interface (Rich Trott) [#14615](https://github.com/nodejs/node/pull/14615)
- [[`58742729da`](https://github.com/nodejs/node/commit/58742729da)] - **test**: set module loading error for aix (Prakash Palaniappan) [#14511](https://github.com/nodejs/node/pull/14511)
- [[`06ba2dae30`](https://github.com/nodejs/node/commit/06ba2dae30)] - **test**: fix conversion of microseconds in test (Nick Stanish) [#14706](https://github.com/nodejs/node/pull/14706)
- [[`30837b3b90`](https://github.com/nodejs/node/commit/30837b3b90)] - **test**: improve check in test-os (Rich Trott) [#14655](https://github.com/nodejs/node/pull/14655)
- [[`55aba6aee7`](https://github.com/nodejs/node/commit/55aba6aee7)] - **test**: replace indexOf with includes (Miguel Angel Asencio Hurtado) [#14630](https://github.com/nodejs/node/pull/14630)
- [[`935d34bd6b`](https://github.com/nodejs/node/commit/935d34bd6b)] - **test**: fix test-readline-interface (Azard) [#14677](https://github.com/nodejs/node/pull/14677)
- [[`2ee3320f2c`](https://github.com/nodejs/node/commit/2ee3320f2c)] - **test**: improve multiple timers tests (James M Snell) [#14616](https://github.com/nodejs/node/pull/14616)
- [[`71f2e76353`](https://github.com/nodejs/node/commit/71f2e76353)] - **test**: use ciphers supported by shared OpenSSL (Jérémy Lal) [#14566](https://github.com/nodejs/node/pull/14566)
- [[`f73f659186`](https://github.com/nodejs/node/commit/f73f659186)] - **test**: mitigate RegEx exceeding 80 chars (Aditya Anand) [#14607](https://github.com/nodejs/node/pull/14607)
- [[`96147c980c`](https://github.com/nodejs/node/commit/96147c980c)] - **test**: read proper inspector message size (Bartosz Sosnowski) [#14596](https://github.com/nodejs/node/pull/14596)
- [[`e84c9d7176`](https://github.com/nodejs/node/commit/e84c9d7176)] - **(SEMVER-MINOR)** **tls**: add tlsSocket.disableRenegotiation() (James M Snell) [#14239](https://github.com/nodejs/node/pull/14239)
- [[`a0e05e884e`](https://github.com/nodejs/node/commit/a0e05e884e)] - **tools**: fix tools/addon-verify.js (Daniel Bevenius) [#14048](https://github.com/nodejs/node/pull/14048)
- [[`116841056a`](https://github.com/nodejs/node/commit/116841056a)] - **util**: improve util.inspect performance (Ruben Bridgewater) [#14492](https://github.com/nodejs/node/pull/14492)
- [[`7203924fea`](https://github.com/nodejs/node/commit/7203924fea)] - **(SEMVER-MINOR)** **util**: implement %o and %O as formatting specifiers (Greg Alexander) [#14558](https://github.com/nodejs/node/pull/14558)

Windows 32-bit Installer: https://nodejs.org/dist/v8.4.0/node-v8.4.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v8.4.0/node-v8.4.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v8.4.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v8.4.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v8.4.0/node-v8.4.0.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v8.4.0/node-v8.4.0-darwin-x64.tar.gz \
Linux 32-bit Binary: https://nodejs.org/dist/v8.4.0/node-v8.4.0-linux-x86.tar.xz \
Linux 64-bit Binary: https://nodejs.org/dist/v8.4.0/node-v8.4.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v8.4.0/node-v8.4.0-linux-ppc64le.tar.xz \
Linux PPC BE 64-bit Binary: https://nodejs.org/dist/v8.4.0/node-v8.4.0-linux-ppc64.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v8.4.0/node-v8.4.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v8.4.0/node-v8.4.0-aix-ppc64.tar.gz \
SmartOS 32-bit Binary: https://nodejs.org/dist/v8.4.0/node-v8.4.0-sunos-x86.tar.xz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v8.4.0/node-v8.4.0-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v8.4.0/node-v8.4.0-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v8.4.0/node-v8.4.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v8.4.0/node-v8.4.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v8.4.0/node-v8.4.0.tar.gz \
Other release files: https://nodejs.org/dist/v8.4.0/ \
Documentation: https://nodejs.org/docs/v8.4.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

9007e961fc9a2a8badb656ebaab52077347113d7618806e7aa502a71eab3f5aa  node-v8.4.0-aix-ppc64.tar.gz
cc10ffbd11586bd27a7cc5e6e2d03fd3e0b341368387a03ee9a0117a0288599d  node-v8.4.0-darwin-x64.tar.gz
e88e41ff5566f1e79f88d116bcc0013ac423741e85bd40d91775ca8f4e5141a1  node-v8.4.0-darwin-x64.tar.xz
724d091c6610255ece8c310e7e3924f0e675d1486d63bca34e7ac0c8c8e07497  node-v8.4.0-headers.tar.gz
bd56ece5c3f66492c178f3affb9a9dc0dfbf121dd21b52c9e04071dc2e9b8b61  node-v8.4.0-headers.tar.xz
a85225930dadf0b8161f95fe7e0e81e8840a8e20623cb5a7b5c61fced10ed7f0  node-v8.4.0-linux-arm64.tar.gz
0a811bbe4905fc879f3cbfc976e5a37cca05bbd609774abe4332b29fea75f073  node-v8.4.0-linux-arm64.tar.xz
c8812b7cdce2de297d320145dab11e521a6734d0a3f42e67f86f80b1bb5984a0  node-v8.4.0-linux-armv6l.tar.gz
b11309baa172bbd6b6ffaf0338f9529adcf27fb64e53de65763936b9f1ff924e  node-v8.4.0-linux-armv6l.tar.xz
11c4e8831c967d152ed098602eaae6fcb0e30ba1f24a9e5781a71c4e7d2314ad  node-v8.4.0-linux-armv7l.tar.gz
a7e79224d98b0f419bd3af0b751f3e369b12a9fb4405b970fc31e552233ae768  node-v8.4.0-linux-armv7l.tar.xz
951a95beb22ccb18543d7ecfe3e81f7f8dfe384dd582789d080d8d56847437a0  node-v8.4.0-linux-ppc64le.tar.gz
e3fa79996878e340ce6fc39cde5704658bde4315ae1ccd11d34100a9d2637838  node-v8.4.0-linux-ppc64le.tar.xz
038c79f078bbbfa873fe89b6c81f791fb7b82960f55d096dbc0e46a3ead84371  node-v8.4.0-linux-ppc64.tar.gz
9e32ed12e2abc2a3245237929674c1b91a7e296bb7cb656c31c364e8f3df1613  node-v8.4.0-linux-ppc64.tar.xz
b6a9e13501b94319e133f6726cc75d9a4c50588ce2db4f1b2e01531694b0d7bb  node-v8.4.0-linux-s390x.tar.gz
5bb1a98dbd9376b7b605450f478de32027afa10e591d0384ba8bab6603ff12f5  node-v8.4.0-linux-s390x.tar.xz
d12bf2389a6b57341528a33de62561edd7ef25c23fbf258d48758fbe3d1d8578  node-v8.4.0-linux-x64.tar.gz
7fd86abad06f96cb2f889c2a0e25686a3de3e9a078ad946ded91ee4f28d8218a  node-v8.4.0-linux-x64.tar.xz
9880c5d4c0dc460643fdc5ac1e3092223a3d659e3528842572d75df8565c22fe  node-v8.4.0-linux-x86.tar.gz
2c5568156e69eca245886c1c583aaab6c26803bc42508aff7271bbcf51710cef  node-v8.4.0-linux-x86.tar.xz
d9565d5fa3388e2c62404a0d661a5071ba004c7a2e606dae72680b6c8d9a2c02  node-v8.4.0.pkg
995de5082fb9ea56c2ab8e2be41d93c283c92efdd2cf735616608a736e2adcb3  node-v8.4.0-sunos-x64.tar.gz
bc9acc06db8c3f683a29028426d7ffb25570a3f2519d05c16f6991b6994b95e4  node-v8.4.0-sunos-x64.tar.xz
3e5f462c579beaa0a6685812a663b7d557a1d3e75dd4314d620e7b1e609b8447  node-v8.4.0-sunos-x86.tar.gz
fd3e432564857ae02f92fb8befe5107435b3a51c0bd92ecf76b936ee066a30aa  node-v8.4.0-sunos-x86.tar.xz
641a15fa822710ef2dc99793fec48d2a8ef75de0040b86568563d4ab296137ef  node-v8.4.0.tar.gz
5d5aa2a101dcc617231a475812eb8ed87cac21491f1dcc7997b9dd463563f361  node-v8.4.0.tar.xz
09f46de9e0dcfc89a2ed9a67a33a40091125f00779198f4cc8df7876abf743f3  node-v8.4.0-win-x64.7z
0f60c99479f74d75c7239795c90698826ba8252019d4c23e82ed0d72ceb8974f  node-v8.4.0-win-x64.zip
cdf4bf3136e2d3a68186297869121a610e26028ade8d6b403f85ba0fe306a341  node-v8.4.0-win-x86.7z
17692976051182b402943f44458b9f9a433c3405deaa033c6c3b5fd7d43434a8  node-v8.4.0-win-x86.zip
8efbd1b94ff8338bd36a1c30a86aba4fae3b80b61e265401fa97e7a4c5478ab2  node-v8.4.0-x64.msi
6724defb5e3cab388d6777f7d999db979d817f51537309b67adaf06569af198f  node-v8.4.0-x86.msi
a1d79afe335d0a8cff2e72100cdadaf6f91f59fcd5a8186da1b99bead9bcd601  win-x64/node.exe
d714da87f8bb03248cbe4e9891cdfb28ec8fd7ed84fc77a1ea16e38f061fc19c  win-x64/node.lib
5d00c65a03e4ae48d3720919bd181ad5aea6f33ebc0f75c485122b84f9ca4fdb  win-x64/node_pdb.7z
49fb81d624e8e34b06897a0336b3830671c34a867c47a90686fe1006aa9248e9  win-x64/node_pdb.zip
ea9399e1f9eafaf6be6608f1401ebb84cf7444ffadabf0b80ba2c186cf7028fa  win-x86/node.exe
28b8412ede7f9ab889680cb5819896e852a466ea176b6d06bba612b39ebfb272  win-x86/node.lib
454b4baa220e4980613345ce057474f311fd6bce91cc6322cf66ac535c44d4ce  win-x86/node_pdb.7z
21cf63479cb6c781a9cbda87f7dbc5abafb815abc3bd59deb4c0755e23190659  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----
Comment: GPGTools - https://gpgtools.org

iQIcBAEBCAAGBQJZk1RDAAoJELY7U1pMIGypr+0P/ik3lMssno9Lz2uQY8Swj/mn
iVcg5ayF1cJnI0Mz8dWNSxnIzmaVK7KZ8o+P9lsI0g5w46J5aoFErA/ltu+pmvOT
JkcONWmWZtX3RTO5tRpTfJz8jn2/NLWSNHOug111ovIP+mSixo8T2leRFUi/ihdG
pwDCBcLvsE40keJjYzYBw/25FjuKbkFwEwpzXoAe2BlAt7zX5LnLP4Jp4IvgRb74
C4e70z7BswYQ5phrAi2SmRJ1gCRo2ceLl+sQTaAoyCn3vI5K8O15pxt/1BuatwQj
MNwty+UIHHCkRp+ia0nr4WvVkt0NwtqLgwlTstMBdnMKmiFlO/BD/8ca2wwUpmY9
L1FwpiFHReutXUtyGsbi6fyoQVd3fV8o/fk7NXK9dWZ1cF+QsIKU84P4F2pyAjLO
F6irrZLyuBfOIRwLF64ZAuS2FtOGPDJL0egFYWkWc16wf2ulct2WtE08MpmqnPD2
QgCg2aQepQEiWaOnT3pjG0ePQ4uWDlf4xJ+Kc2MMpyQnyKUxTi8hXU8rC+gx7eKc
NTmYtkJ6MroW5puHNyUXZHICij/Ssm/sMaW6kv6KnnOmMuNJBefpfFdOsO58RysF
p7Hu7X7GWksBYkpnSPRT+4F3pYSigSQBbEss+pkze+ogOoa6Kp3T5T+tV+KfD6/t
R7iVKurG6n0FECBIKJru
=Tpwj
-----END PGP SIGNATURE-----

```
