---
date: '2022-12-13T13:13:27.743Z'
category: release
title: Node v16.19.0 (LTS)
layout: blog-post
author: <PERSON>
---

### Notable Changes

#### OpenSSL 1.1.1s

This OpenSSL version does not address any security vulnerabilities.

#### Root certificates updated to NSS 3.85

Certificates added:

- Autoridad de Certificacion Firmaprofesional CIF A62634068
- Certainly Root E1
- Certainly Root R1
- D-TRUST BR Root CA 1 2020
- D-TRUST EV Root CA 1 2020
- DigiCert TLS ECC P384 Root G5
- DigiCert TLS RSA4096 Root G5
- E-Tugra Global Root CA ECC v3
- E-Tugra Global Root CA RSA v3
- HiPKI Root CA - G1
- ISRG Root X2
- Security Communication ECC RootCA1
- Security Communication RootCA3
- Telia Root CA v2
- vTrus ECC Root CA
- vTrus Root CA

Certificates removed:

- Cybertrust Global Root
- DST Root CA X3
- GlobalSign Root CA - R2
- Hellenic Academic and Research Institutions RootCA 2011

#### Time zone update to 2022f

Time zone data has been updated to 2022f. This includes changes to Daylight
Savings Time (DST) for Fiji and Mexico. For more information, see
<https://mm.icann.org/pipermail/tz-announce/2022-October/000075.html>.

#### Other Notable Changes

- \[[`33707dcd03`](https://github.com/nodejs/node/commit/33707dcd03)] - **(SEMVER-MINOR)** **dgram**: add dgram send queue info (theanarkh) [#44149](https://github.com/nodejs/node/pull/44149)

Dependency updates:

- \[[`3b2b70d792`](https://github.com/nodejs/node/commit/3b2b70d792)] - **deps**: upgrade npm to 8.19.3 (npm team) [#45322](https://github.com/nodejs/node/pull/45322)

Experimental features:

- \[[`1e0dcd1ee0`](https://github.com/nodejs/node/commit/1e0dcd1ee0)] - **(SEMVER-MINOR)** **cli**: add `--watch` (Moshe Atlow) [#44366](https://github.com/nodejs/node/pull/44366)
- \[[`8c73279ebb`](https://github.com/nodejs/node/commit/8c73279ebb)] - **(SEMVER-MINOR)** **util**: add default value option to parsearg (Manuel Spigolon) [#44631](https://github.com/nodejs/node/pull/44631)

### Commits

- \[[`bbef3c42f6`](https://github.com/nodejs/node/commit/bbef3c42f6)] - **build**: add version info to timezone update PR (Darshan Sen) [#45021](https://github.com/nodejs/node/pull/45021)
- \[[`cc2c7648e0`](https://github.com/nodejs/node/commit/cc2c7648e0)] - **build**: support Python 3.11 (Luigi Pinca) [#45191](https://github.com/nodejs/node/pull/45191)
- \[[`ac24c80663`](https://github.com/nodejs/node/commit/ac24c80663)] - **build**: remove redundant condition from common.gypi (Richard Lau) [#45076](https://github.com/nodejs/node/pull/45076)
- \[[`03dcbe3030`](https://github.com/nodejs/node/commit/03dcbe3030)] - **build**: fix bad upstream merge (Stephen Gallagher) [#44642](https://github.com/nodejs/node/pull/44642)
- \[[`1e0dcd1ee0`](https://github.com/nodejs/node/commit/1e0dcd1ee0)] - **(SEMVER-MINOR)** **cli**: add `--watch` (Moshe Atlow) [#44366](https://github.com/nodejs/node/pull/44366)
- \[[`96d131665e`](https://github.com/nodejs/node/commit/96d131665e)] - **cluster**: use inspector utils (Moshe Atlow) [#44592](https://github.com/nodejs/node/pull/44592)
- \[[`704836033a`](https://github.com/nodejs/node/commit/704836033a)] - **crypto**: update root certificates (Luigi Pinca) [#45490](https://github.com/nodejs/node/pull/45490)
- \[[`5a776d4a69`](https://github.com/nodejs/node/commit/5a776d4a69)] - **deps**: update timezone to 2022f (Richard Lau) [#45613](https://github.com/nodejs/node/pull/45613)
- \[[`3b2b70d792`](https://github.com/nodejs/node/commit/3b2b70d792)] - **deps**: upgrade npm to 8.19.3 (npm team) [#45322](https://github.com/nodejs/node/pull/45322)
- \[[`9fbc8b21db`](https://github.com/nodejs/node/commit/9fbc8b21db)] - **deps**: update corepack to 0.15.1 (Node.js GitHub Bot) [#45331](https://github.com/nodejs/node/pull/45331)
- \[[`87e3d002ca`](https://github.com/nodejs/node/commit/87e3d002ca)] - **deps**: update corepack to 0.15.0 (Node.js GitHub Bot) [#45235](https://github.com/nodejs/node/pull/45235)
- \[[`e972ff7b13`](https://github.com/nodejs/node/commit/e972ff7b13)] - **deps**: V8: backport bbd800c6e359 (Chengzhong Wu) [#44947](https://github.com/nodejs/node/pull/44947)
- \[[`af9d8217c0`](https://github.com/nodejs/node/commit/af9d8217c0)] - **deps**: V8: cherry-pick b95354290941 (Chengzhong Wu) [#44947](https://github.com/nodejs/node/pull/44947)
- \[[`38202d321b`](https://github.com/nodejs/node/commit/38202d321b)] - **deps**: update undici to 5.12.0 (Node.js GitHub Bot) [#45236](https://github.com/nodejs/node/pull/45236)
- \[[`7c0da6adf9`](https://github.com/nodejs/node/commit/7c0da6adf9)] - **deps**: update archs files for OpenSSL-1.1.1s (RafaelGSS) [#45274](https://github.com/nodejs/node/pull/45274)
- \[[`1149ead6f7`](https://github.com/nodejs/node/commit/1149ead6f7)] - **deps**: upgrade openssl sources to OpenSSL_1_1_1s (RafaelGSS) [#45274](https://github.com/nodejs/node/pull/45274)
- \[[`cd54bce4f5`](https://github.com/nodejs/node/commit/cd54bce4f5)] - **deps**: update timezone (Node.js GitHub Bot) [#44950](https://github.com/nodejs/node/pull/44950)
- \[[`2901abe4f0`](https://github.com/nodejs/node/commit/2901abe4f0)] - **deps**: update undici to 5.11.0 (Node.js GitHub Bot) [#44929](https://github.com/nodejs/node/pull/44929)
- \[[`c80cf97033`](https://github.com/nodejs/node/commit/c80cf97033)] - **deps**: update corepack to 0.14.2 (Node.js GitHub Bot) [#44775](https://github.com/nodejs/node/pull/44775)
- \[[`33707dcd03`](https://github.com/nodejs/node/commit/33707dcd03)] - **(SEMVER-MINOR)** **dgram**: add dgram send queue info (theanarkh) [#44149](https://github.com/nodejs/node/pull/44149)
- \[[`c708d9bb94`](https://github.com/nodejs/node/commit/c708d9bb94)] - **doc**: fix typo in parseArgs default value (Tobias Nießen) [#45083](https://github.com/nodejs/node/pull/45083)
- \[[`5a0efa05d2`](https://github.com/nodejs/node/commit/5a0efa05d2)] - **node-api**: handle no support for external buffers (Michael Dawson) [#45181](https://github.com/nodejs/node/pull/45181)
- \[[`db31de634e`](https://github.com/nodejs/node/commit/db31de634e)] - **readline**: refactor to avoid unsafe regex primordials (Antoine du Hamel) [#43475](https://github.com/nodejs/node/pull/43475)
- \[[`fbc52e5729`](https://github.com/nodejs/node/commit/fbc52e5729)] - **src**: disambiguate terms used to refer to builtins and addons (Joyee Cheung) [#44135](https://github.com/nodejs/node/pull/44135)
- \[[`953072d3db`](https://github.com/nodejs/node/commit/953072d3db)] - **src**: let http2 streams end after session close (Santiago Gimeno) [#45153](https://github.com/nodejs/node/pull/45153)
- \[[`54608d8dc3`](https://github.com/nodejs/node/commit/54608d8dc3)] - **src**: split property helpers from node::Environment (Chengzhong Wu) [#44056](https://github.com/nodejs/node/pull/44056)
- \[[`6733556783`](https://github.com/nodejs/node/commit/6733556783)] - **test**: add test to validate changelogs for releases (Richard Lau) [#45325](https://github.com/nodejs/node/pull/45325)
- \[[`821d832cef`](https://github.com/nodejs/node/commit/821d832cef)] - **test**: mark test-watch-mode\* as flaky on all platforms (Pierrick Bouvier) [#45049](https://github.com/nodejs/node/pull/45049)
- \[[`02a18eac69`](https://github.com/nodejs/node/commit/02a18eac69)] - **test**: fix test-runner-inspect (Moshe Atlow) [#44620](https://github.com/nodejs/node/pull/44620)
- \[[`197df63f74`](https://github.com/nodejs/node/commit/197df63f74)] - **test**: add a test to ensure the correctness of timezone upgrades (Darshan Sen) [#45299](https://github.com/nodejs/node/pull/45299)
- \[[`42e9d8016a`](https://github.com/nodejs/node/commit/42e9d8016a)] - **test**: fix textdecoder test for small-icu builds (Richard Lau) [#45225](https://github.com/nodejs/node/pull/45225)
- \[[`6d736a56d8`](https://github.com/nodejs/node/commit/6d736a56d8)] - **test**: fix watch mode test flake (Moshe Atlow) [#44739](https://github.com/nodejs/node/pull/44739)
- \[[`543d3d2bf3`](https://github.com/nodejs/node/commit/543d3d2bf3)] - **test**: deflake watch mode tests (Moshe Atlow) [#44621](https://github.com/nodejs/node/pull/44621)
- \[[`97f6caf4eb`](https://github.com/nodejs/node/commit/97f6caf4eb)] - **test**: split watch mode inspector tests to sequential (Moshe Atlow) [#44551](https://github.com/nodejs/node/pull/44551)
- \[[`499750ff7a`](https://github.com/nodejs/node/commit/499750ff7a)] - **test**: update list of known globals (Antoine du Hamel) [#45255](https://github.com/nodejs/node/pull/45255)
- \[[`64d343af74`](https://github.com/nodejs/node/commit/64d343af74)] - **test_runner**: support using `--inspect` with `--test` (Moshe Atlow) [#44520](https://github.com/nodejs/node/pull/44520)
- \[[`99ee5e484d`](https://github.com/nodejs/node/commit/99ee5e484d)] - **test_runner**: fix `duration_ms` to be milliseconds (Moshe Atlow) [#44450](https://github.com/nodejs/node/pull/44450)
- \[[`37e909251c`](https://github.com/nodejs/node/commit/37e909251c)] - **test_runner**: support programmatically running `--test` (Moshe Atlow) [#44241](https://github.com/nodejs/node/pull/44241)
- \[[`0ae5694f88`](https://github.com/nodejs/node/commit/0ae5694f88)] - **tools**: update certdata.txt (Luigi Pinca) [#45490](https://github.com/nodejs/node/pull/45490)
- \[[`891368cefd`](https://github.com/nodejs/node/commit/891368cefd)] - **tools**: remove faulty early termination logic from update-timezone.mjs (Darshan Sen) [#44870](https://github.com/nodejs/node/pull/44870)
- \[[`543493c242`](https://github.com/nodejs/node/commit/543493c242)] - **tools**: fix timezone update tool (Darshan Sen) [#44870](https://github.com/nodejs/node/pull/44870)
- \[[`c77f660b75`](https://github.com/nodejs/node/commit/c77f660b75)] - **tools**: fix `create-or-update-pull-request-action` hash on GHA (Antoine du Hamel) [#45166](https://github.com/nodejs/node/pull/45166)
- \[[`58c30dd049`](https://github.com/nodejs/node/commit/58c30dd049)] - **tools**: update gr2m/create-or-update-pull-request-action (Luigi Pinca) [#45022](https://github.com/nodejs/node/pull/45022)
- \[[`749a4b3e5e`](https://github.com/nodejs/node/commit/749a4b3e5e)] - **tools**: use Python 3.11 in GitHub Actions workflows (Luigi Pinca) [#45191](https://github.com/nodejs/node/pull/45191)
- \[[`6f541d99a5`](https://github.com/nodejs/node/commit/6f541d99a5)] - **tools**: have test-asan use ubuntu-20.04 (Filip Skokan) [#45581](https://github.com/nodejs/node/pull/45581)
- \[[`e7ed56f501`](https://github.com/nodejs/node/commit/e7ed56f501)] - **tools**: make license-builder.sh comply with shellcheck 0.8.0 (Rich Trott) [#41258](https://github.com/nodejs/node/pull/41258)
- \[[`cc819b4bf8`](https://github.com/nodejs/node/commit/cc819b4bf8)] - **tools**: fix typo in `avoid-prototype-pollution` lint rule (Antoine du Hamel) [#44446](https://github.com/nodejs/node/pull/44446)
- \[[`254358c81e`](https://github.com/nodejs/node/commit/254358c81e)] - **tools**: refactor `avoid-prototype-pollution` lint rule (Antoine du Hamel) [#43476](https://github.com/nodejs/node/pull/43476)
- \[[`8c73279ebb`](https://github.com/nodejs/node/commit/8c73279ebb)] - **(SEMVER-MINOR)** **util**: add default value option to parsearg (Manuel Spigolon) [#44631](https://github.com/nodejs/node/pull/44631)

Windows 32-bit Installer: https://nodejs.org/dist/v16.19.0/node-v16.19.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v16.19.0/node-v16.19.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v16.19.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v16.19.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v16.19.0/node-v16.19.0.pkg \
macOS Apple Silicon 64-bit Binary: https://nodejs.org/dist/v16.19.0/node-v16.19.0-darwin-arm64.tar.gz \
macOS Intel 64-bit Binary: https://nodejs.org/dist/v16.19.0/node-v16.19.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v16.19.0/node-v16.19.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v16.19.0/node-v16.19.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v16.19.0/node-v16.19.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v16.19.0/node-v16.19.0-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v16.19.0/node-v16.19.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v16.19.0/node-v16.19.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v16.19.0/node-v16.19.0.tar.gz \
Other release files: https://nodejs.org/dist/v16.19.0/ \
Documentation: https://nodejs.org/docs/v16.19.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

2697295a083b1be572a98f632f28c510e5eb5af526bbdf4ca1b5060abc01d24e  node-v16.19.0-aix-ppc64.tar.gz
5c9434fbb0f323fecf3d261b23a2e544919380c5043d0046d9745682fefd9cde  node-v16.19.0-darwin-arm64.tar.gz
7313c9db5f5140ece847a97f366032a10bddb6d87fa28a361ada918addcd5c73  node-v16.19.0-darwin-arm64.tar.xz
491e5a5592eca1961dcbb1fae28567428ce56ce9cc7977b04041e163e0c1670c  node-v16.19.0-darwin-x64.tar.gz
601d1d632b9815dbb647f5cd440a95fa651110b5b4bcaf07ed3aa7886525716b  node-v16.19.0-darwin-x64.tar.xz
d0b02ce3d1198d41127daf64f204195252abff6a22da70830aaed846ba6ad864  node-v16.19.0-headers.tar.gz
52b67b813ead4fff6e16903dcec32af9b29eea02abf8163b74854f15d7164bdc  node-v16.19.0-headers.tar.xz
1d5e66db4e23a4ab2380dfa7cfebea1f960438db6bd2a7095020acfc64545542  node-v16.19.0-linux-arm64.tar.gz
9072c995052f832678fe8fab18e960bd9853f30e481787e53f8dd1ec8aaa3bb6  node-v16.19.0-linux-arm64.tar.xz
bd1cb8aa38f0ce29d8808511f9e87da844a204c829d18e85702274516b710d9f  node-v16.19.0-linux-armv7l.tar.gz
6f3baea5ddcedf3b5d80ed34411d0bdba77f2b992de0436d325378a308904aca  node-v16.19.0-linux-armv7l.tar.xz
485e78a5764bd48cdf7aa370d46be470f4941c180fcb37d64c4abcb62237d790  node-v16.19.0-linux-ppc64le.tar.gz
7df473b47c5b6386330215381a6b6a8fe14fcf9dfa93a69fcb0488440ea75a52  node-v16.19.0-linux-ppc64le.tar.xz
17997960c9a2378c48d303880cd5bc5cc83705c5d8110293df77ce7bbad0efe1  node-v16.19.0-linux-s390x.tar.gz
113cb9aac3e23adcc8d67b9ef7bef735e234fd8c72ad7bc29f61e7f06af60a12  node-v16.19.0-linux-s390x.tar.xz
23770ba26a52cb8fedd1096613bbc419b8a033d774a457d9024bb5a0159f3585  node-v16.19.0-linux-x64.tar.gz
c88b52497ab38a3ddf526e5b46a41270320409109c3f74171b241132984fd08f  node-v16.19.0-linux-x64.tar.xz
a474c245bba51a9156bd4ca887c4000eab1975cf5bd8201e5f9b0d29852c2cd5  node-v16.19.0.pkg
8b8a2939fa5f654ff61cae29b12118c24109273458ecbe6162ad8a8858309e0d  node-v16.19.0.tar.gz
4f1fec1aea2392f6eb6d1d040b01e7ee3e51e762a9791dfea590920bc1156706  node-v16.19.0.tar.xz
e07399a4a441091ca0a5506faf7a9236ea1675220146daeea3bee828c2cbda3f  node-v16.19.0-win-x64.7z
534ca7a24e999c81cec847a498cc43d47e2bb158f6edf639e5297f2718350e96  node-v16.19.0-win-x64.zip
d8aaa0db42a43329d17f555b13b3fdc75833bf72075f0898a5a367d8f5aec757  node-v16.19.0-win-x86.7z
17e2bf8ed00b3d15321743104104c8b6ef55677dfc18ee2ed44de64fbf2c4def  node-v16.19.0-win-x86.zip
edfd08c96dd30d24714f6db577e890a82ba9720d8ac37c8366a999a2292fe2ce  node-v16.19.0-x64.msi
3ed815633aae84d43dfe2a8db2e2394d31ef240df123f32843f8eac78337bdd9  node-v16.19.0-x86.msi
e4e7f389fbec9300275defc749246c62bdbe4f66406eb01e7c9a4101e07352da  win-x64/node.exe
d6bada534f8806049a8a05f2a7d99e2a1fcf3675d3269b2a887da6c0c42f0929  win-x64/node.lib
53773c036bdc0002c40823762555e4337b773115b823798efb0e32f039e80363  win-x64/node_pdb.7z
898cfbcd23dbc574504d88173acbca5abd1011d1a806a39d465abd15faa5cbef  win-x64/node_pdb.zip
ff9c07584f5017abcfe7a31fa0d8c1322fe9073120f4d7c018318b660ed04621  win-x86/node.exe
b171f4785f44b475710a24f1d0eb3f0333082a7f288e5a2b7fcc56866f75af51  win-x86/node.lib
0df0485e0e2e44012c840a5cd660425f7aa2aa8da0a35714e791bd14d25f08cf  win-x86/node_pdb.7z
19864f969c639dea1dad45ac61112d592c7acbed2f5e88b1acc5e4a7fa95d9ce  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEyC+jrhy+3Gvka5NgxDzsRcF6uTwFAmOYeEQACgkQxDzsRcF6
uTxzoQ//Qr3pmN9zbmGtMSvAXX7yf7NCZlFdLFnZ6Q2yzSeFpkCCDH4w2DkiuXXA
jsVrRv40R5vabDI27DnNZV+rmLY8KCb06c08EatEAIhO9szsAPQPdlt66rpC7rnR
RDRwEOFwIZdeoF0NneEc+Df4vcw621RdNEgcwd6CJT2i8dAwBzgA6WfV5nTaMirV
8g8f8gthrStId9UGOkVIl9qNeidfo1V79W3b6192ETuxVLJ89GLaVZYdlUPUUy8P
y8MF7cnP524N6zr1RasTFCDxdNvo8FyaQMOllh9bZnyT3dckHjOQog7pwpKWhx7/
lEYrXM+GC0YCuE6OTdNryudO/iNgZ00UZ/lnplqK462jiNeIBdiBOpmg4p3Of1+a
Zxrw3Kl//fPQ79jDkW2Yo0pnc60wS8V8LtqOTAnRB2OX/oQIz/Y+3uee7xTHSSvA
EbH1uB8LHZHQjkpSt3DgQ5B9jz1ZR8Absh6Bud+uHkaMVIulfTeqJI9XeoYd4s4k
/sOhyKrBTTgPRoGJVL1RtE0SdQLDMGT7kDL+YkasRDA+WWGnsV9Bud+V8+/xCjC2
teU+wMiY+EKB8BgQLorT3SXBOW2GU63/4Gx0163dKGWPSQsGLfy/NoJubt0rx51C
wkgfPAMHXvOAHF5xn+xtr4ISw8nZvxcVVnK/AWbCqKX+XRDRVA4=
=AHoz
-----END PGP SIGNATURE-----

```
