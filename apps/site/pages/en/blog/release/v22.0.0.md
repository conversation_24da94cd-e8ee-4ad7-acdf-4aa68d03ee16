---
date: '2024-04-24T18:23:41.746Z'
category: release
title: Node v22.0.0 (Current)
layout: blog-post
author: <PERSON>
---

## 2024-04-24, Version 22.0.0 (Current), @RafaelGSS and @marco-ippolito

We're excited to announce the release of Node.js 22!
Highlights include require()ing ESM graphs, WebSocket client, updates of the V8 JavaScript engine, and more!
As a reminder, Node.js 22 will enter long-term support (LTS) in October, but until then, it will be the "Current" release for the next six months.
We encourage you to explore the new features and benefits offered by this latest release and evaluate their potential impact on your applications.

### Other Notable Changes

- \[[`25c79f3331`](https://github.com/nodejs/node/commit/25c79f3331)] - **esm**: drop support for import assertions (<PERSON><PERSON><PERSON>) [#52104](https://github.com/nodejs/node/pull/52104)
- \[[`818c10e86d`](https://github.com/nodejs/node/commit/818c10e86d)] - **lib**: improve perf of `AbortSignal` creation (<PERSON><PERSON>) [#52408](https://github.com/nodejs/node/pull/52408)
- \[[`4f68c7c1c9`](https://github.com/nodejs/node/commit/4f68c7c1c9)] - **watch**: mark as stable (Moshe Atlow) [#52074](https://github.com/nodejs/node/pull/52074)
- \[[`02b0bc01fe`](https://github.com/nodejs/node/commit/02b0bc01fe)] - **(SEMVER-MAJOR)** **deps**: update V8 to 12.4.254.14 (Michaël Zasso) [#52465](https://github.com/nodejs/node/pull/52465)
- \[[`c975384264`](https://github.com/nodejs/node/commit/c975384264)] - **(SEMVER-MAJOR)** **lib**: enable WebSocket by default (Aras Abbasi) [#51594](https://github.com/nodejs/node/pull/51594)
- \[[`1abff07392`](https://github.com/nodejs/node/commit/1abff07392)] - **(SEMVER-MAJOR)** **stream**: bump default highWaterMark (Robert Nagy) [#52037](https://github.com/nodejs/node/pull/52037)
- \[[`1a5acd0638`](https://github.com/nodejs/node/commit/1a5acd0638)] - **(SEMVER-MAJOR)** **v8**: enable maglev on supported architectures (Keyhan Vakil) [#51360](https://github.com/nodejs/node/pull/51360)
- \[[`128c60d906`](https://github.com/nodejs/node/commit/128c60d906)] - **(SEMVER-MINOR)** **cli**: implement `node --run <script-in-package-json>` (Yagiz Nizipli) [#52190](https://github.com/nodejs/node/pull/52190)
- \[[`151d365ad1`](https://github.com/nodejs/node/commit/151d365ad1)] - **(SEMVER-MINOR)** **fs**: expose glob and globSync (Moshe Atlow) [#51912](https://github.com/nodejs/node/pull/51912)
- \[[`5f7fad2605`](https://github.com/nodejs/node/commit/5f7fad2605)] - **(SEMVER-MINOR)** **module**: support require()ing synchronous ESM graphs (Joyee Cheung) [#51977](https://github.com/nodejs/node/pull/51977)

### Semver-Major Commits

- \[[`2b1e7c2fcb`](https://github.com/nodejs/node/commit/2b1e7c2fcb)] - **(SEMVER-MAJOR)** **build**: compile with C++20 support on Windows (StefanStojanovic) [#52465](https://github.com/nodejs/node/pull/52465)
- \[[`12d00f1479`](https://github.com/nodejs/node/commit/12d00f1479)] - **(SEMVER-MAJOR)** **build**: reset embedder string to "-node.0" (Michaël Zasso) [#52465](https://github.com/nodejs/node/pull/52465)
- \[[`5f08e11a3c`](https://github.com/nodejs/node/commit/5f08e11a3c)] - **(SEMVER-MAJOR)** **build**: reset embedder string to "-node.0" (Michaël Zasso) [#52293](https://github.com/nodejs/node/pull/52293)
- \[[`94f0369d1d`](https://github.com/nodejs/node/commit/94f0369d1d)] - **(SEMVER-MAJOR)** **build**: reset embedder string to "-node.0" (Michaël Zasso) [#51362](https://github.com/nodejs/node/pull/51362)
- \[[`58674cd1d8`](https://github.com/nodejs/node/commit/58674cd1d8)] - **(SEMVER-MAJOR)** **build**: reset embedder string to "-node.0" (Michaël Zasso) [#50115](https://github.com/nodejs/node/pull/50115)
- \[[`60e836427e`](https://github.com/nodejs/node/commit/60e836427e)] - **(SEMVER-MAJOR)** **console**: treat non-strings as separate argument in console.assert() (Jacob Hummer) [#49722](https://github.com/nodejs/node/pull/49722)
- \[[`d62ab3a1ef`](https://github.com/nodejs/node/commit/d62ab3a1ef)] - **(SEMVER-MAJOR)** **crypto**: runtime deprecate hmac constructor (Marco Ippolito) [#52071](https://github.com/nodejs/node/pull/52071)
- \[[`de0602d190`](https://github.com/nodejs/node/commit/de0602d190)] - **(SEMVER-MAJOR)** **crypto**: runtime deprecate Hash constructor (Marco Ippolito) [#51880](https://github.com/nodejs/node/pull/51880)
- \[[`215f4d04b7`](https://github.com/nodejs/node/commit/215f4d04b7)] - **(SEMVER-MAJOR)** **crypto**: move createCipher and createDecipher to eol (Marco Ippolito) [#50973](https://github.com/nodejs/node/pull/50973)
- \[[`30801b8aaf`](https://github.com/nodejs/node/commit/30801b8aaf)] - **(SEMVER-MAJOR)** **deps**: V8: cherry-pick cd10ad7cdbe5 (Joyee Cheung) [#52465](https://github.com/nodejs/node/pull/52465)
- \[[`521b629ab1`](https://github.com/nodejs/node/commit/521b629ab1)] - **(SEMVER-MAJOR)** **deps**: V8: revert CL 5331688 (Michaël Zasso) [#52465](https://github.com/nodejs/node/pull/52465)
- \[[`3795e97e6c`](https://github.com/nodejs/node/commit/3795e97e6c)] - **(SEMVER-MAJOR)** **deps**: patch V8 to support compilation with MSVC (StefanStojanovic) [#52465](https://github.com/nodejs/node/pull/52465)
- \[[`5bde9e677d`](https://github.com/nodejs/node/commit/5bde9e677d)] - **(SEMVER-MAJOR)** **deps**: silence internal V8 deprecation warning (Michaël Zasso) [#52465](https://github.com/nodejs/node/pull/52465)
- \[[`46e628c6f2`](https://github.com/nodejs/node/commit/46e628c6f2)] - **(SEMVER-MAJOR)** **deps**: patch V8 to avoid duplicated zlib symbol (Michaël Zasso) [#52465](https://github.com/nodejs/node/pull/52465)
- \[[`f824e40a82`](https://github.com/nodejs/node/commit/f824e40a82)] - **(SEMVER-MAJOR)** **deps**: remove usage of a C++20 feature from V8 (Michaël Zasso) [#52465](https://github.com/nodejs/node/pull/52465)
- \[[`d2c84c9a13`](https://github.com/nodejs/node/commit/d2c84c9a13)] - **(SEMVER-MAJOR)** **deps**: avoid compilation error with ASan (Michaël Zasso) [#52465](https://github.com/nodejs/node/pull/52465)
- \[[`95d6045bdb`](https://github.com/nodejs/node/commit/95d6045bdb)] - **(SEMVER-MAJOR)** **deps**: disable V8 concurrent sparkplug compilation (Michaël Zasso) [#52465](https://github.com/nodejs/node/pull/52465)
- \[[`00f55f5743`](https://github.com/nodejs/node/commit/00f55f5743)] - **(SEMVER-MAJOR)** **deps**: silence irrelevant V8 warning (Michaël Zasso) [#52465](https://github.com/nodejs/node/pull/52465)
- \[[`764085aa66`](https://github.com/nodejs/node/commit/764085aa66)] - **(SEMVER-MAJOR)** **deps**: always define V8_EXPORT_PRIVATE as no-op (Michaël Zasso) [#52465](https://github.com/nodejs/node/pull/52465)
- \[[`02b0bc01fe`](https://github.com/nodejs/node/commit/02b0bc01fe)] - **(SEMVER-MAJOR)** **deps**: update V8 to 12.4.254.14 (Michaël Zasso) [#52465](https://github.com/nodejs/node/pull/52465)
- \[[`0ec50a19dd`](https://github.com/nodejs/node/commit/0ec50a19dd)] - **(SEMVER-MAJOR)** **deps**: V8: cherry-pick cd10ad7cdbe5 (Joyee Cheung) [#52293](https://github.com/nodejs/node/pull/52293)
- \[[`021b0b7dee`](https://github.com/nodejs/node/commit/021b0b7dee)] - **(SEMVER-MAJOR)** **deps**: V8: backport c4be0a97f981 (Richard Lau) [#52293](https://github.com/nodejs/node/pull/52293)
- \[[`681aaf85c7`](https://github.com/nodejs/node/commit/681aaf85c7)] - **(SEMVER-MAJOR)** **deps**: silence internal V8 deprecation warning (Michaël Zasso) [#52293](https://github.com/nodejs/node/pull/52293)
- \[[`c563a1c4e4`](https://github.com/nodejs/node/commit/c563a1c4e4)] - **(SEMVER-MAJOR)** **deps**: patch V8 to support compilation with MSVC (Stefan Stojanovic) [#52293](https://github.com/nodejs/node/pull/52293)
- \[[`11e94b9987`](https://github.com/nodejs/node/commit/11e94b9987)] - **(SEMVER-MAJOR)** **deps**: patch V8 to avoid duplicated zlib symbol (Michaël Zasso) [#52293](https://github.com/nodejs/node/pull/52293)
- \[[`856163e23c`](https://github.com/nodejs/node/commit/856163e23c)] - **(SEMVER-MAJOR)** **deps**: remove usage of a C++20 feature from V8 (Michaël Zasso) [#52293](https://github.com/nodejs/node/pull/52293)
- \[[`b530214127`](https://github.com/nodejs/node/commit/b530214127)] - **(SEMVER-MAJOR)** **deps**: avoid compilation error with ASan (Michaël Zasso) [#52293](https://github.com/nodejs/node/pull/52293)
- \[[`8054f69dd9`](https://github.com/nodejs/node/commit/8054f69dd9)] - **(SEMVER-MAJOR)** **deps**: disable V8 concurrent sparkplug compilation (Michaël Zasso) [#52293](https://github.com/nodejs/node/pull/52293)
- \[[`dee908be42`](https://github.com/nodejs/node/commit/dee908be42)] - **(SEMVER-MAJOR)** **deps**: silence irrelevant V8 warning (Michaël Zasso) [#52293](https://github.com/nodejs/node/pull/52293)
- \[[`cf069414ee`](https://github.com/nodejs/node/commit/cf069414ee)] - **(SEMVER-MAJOR)** **deps**: always define V8_EXPORT_PRIVATE as no-op (Michaël Zasso) [#52293](https://github.com/nodejs/node/pull/52293)
- \[[`cc5792dd85`](https://github.com/nodejs/node/commit/cc5792dd85)] - **(SEMVER-MAJOR)** **deps**: update V8 to 12.3.219.16 (Michaël Zasso) [#52293](https://github.com/nodejs/node/pull/52293)
- \[[`61a0d3b4c4`](https://github.com/nodejs/node/commit/61a0d3b4c4)] - **(SEMVER-MAJOR)** **deps**: V8: backport c4be0a97f981 (Richard Lau) [#51362](https://github.com/nodejs/node/pull/51362)
- \[[`f55380a725`](https://github.com/nodejs/node/commit/f55380a725)] - **(SEMVER-MAJOR)** **deps**: V8: cherry-pick f8d5e576b814 (Richard Lau) [#51362](https://github.com/nodejs/node/pull/51362)
- \[[`b9d806a2dd`](https://github.com/nodejs/node/commit/b9d806a2dd)] - **(SEMVER-MAJOR)** **deps**: patch V8 to support compilation with MSVC (StefanStojanovic) [#51362](https://github.com/nodejs/node/pull/51362)
- \[[`63b58bc17b`](https://github.com/nodejs/node/commit/63b58bc17b)] - **(SEMVER-MAJOR)** **deps**: patch V8 to avoid duplicated zlib symbol (Michaël Zasso) [#51362](https://github.com/nodejs/node/pull/51362)
- \[[`86056353c4`](https://github.com/nodejs/node/commit/86056353c4)] - **(SEMVER-MAJOR)** **deps**: remove usage of a C++20 feature from V8 (Michaël Zasso) [#51362](https://github.com/nodejs/node/pull/51362)
- \[[`2e0efc1c8d`](https://github.com/nodejs/node/commit/2e0efc1c8d)] - **(SEMVER-MAJOR)** **deps**: avoid compilation error with ASan (Michaël Zasso) [#51362](https://github.com/nodejs/node/pull/51362)
- \[[`59e6f62e34`](https://github.com/nodejs/node/commit/59e6f62e34)] - **(SEMVER-MAJOR)** **deps**: disable V8 concurrent sparkplug compilation (Michaël Zasso) [#51362](https://github.com/nodejs/node/pull/51362)
- \[[`0423f7e27e`](https://github.com/nodejs/node/commit/0423f7e27e)] - **(SEMVER-MAJOR)** **deps**: silence irrelevant V8 warning (Michaël Zasso) [#51362](https://github.com/nodejs/node/pull/51362)
- \[[`f36620806d`](https://github.com/nodejs/node/commit/f36620806d)] - **(SEMVER-MAJOR)** **deps**: always define V8_EXPORT_PRIVATE as no-op (Michaël Zasso) [#51362](https://github.com/nodejs/node/pull/51362)
- \[[`09a8440b45`](https://github.com/nodejs/node/commit/09a8440b45)] - **(SEMVER-MAJOR)** **deps**: update V8 to 12.2.281.27 (Michaël Zasso) [#51362](https://github.com/nodejs/node/pull/51362)
- \[[`0da3beebfc`](https://github.com/nodejs/node/commit/0da3beebfc)] - **(SEMVER-MAJOR)** **deps**: V8: cherry-pick de611e69ad51 (Keyhan Vakil) [#50115](https://github.com/nodejs/node/pull/50115)
- \[[`b982335637`](https://github.com/nodejs/node/commit/b982335637)] - **(SEMVER-MAJOR)** **deps**: V8: cherry-pick 0fd478bcdabd (Joyee Cheung) [#50115](https://github.com/nodejs/node/pull/50115)
- \[[`481a90116c`](https://github.com/nodejs/node/commit/481a90116c)] - **(SEMVER-MAJOR)** **deps**: V8: cherry-pick 0f9ebbc672c7 (Chengzhong Wu) [#50115](https://github.com/nodejs/node/pull/50115)
- \[[`782addbdc3`](https://github.com/nodejs/node/commit/782addbdc3)] - **(SEMVER-MAJOR)** **deps**: V8: cherry-pick 8f0b94671ddb (Lu Yahan) [#50115](https://github.com/nodejs/node/pull/50115)
- \[[`b682e7f540`](https://github.com/nodejs/node/commit/b682e7f540)] - **(SEMVER-MAJOR)** **deps**: V8: cherry-pick f7d000a7ae7b (Luke Albao) [#50115](https://github.com/nodejs/node/pull/50115)
- \[[`a60090c52f`](https://github.com/nodejs/node/commit/a60090c52f)] - **(SEMVER-MAJOR)** **deps**: V8: cherry-pick 25902244ad1a (Joyee Cheung) [#50115](https://github.com/nodejs/node/pull/50115)
- \[[`8441d1fc18`](https://github.com/nodejs/node/commit/8441d1fc18)] - **(SEMVER-MAJOR)** **deps**: patch V8 to avoid duplicated zlib symbol (Michaël Zasso) [#50115](https://github.com/nodejs/node/pull/50115)
- \[[`e8e9bbd7a9`](https://github.com/nodejs/node/commit/e8e9bbd7a9)] - **(SEMVER-MAJOR)** **deps**: remove usage of a C++20 feature from V8 (Michaël Zasso) [#50115](https://github.com/nodejs/node/pull/50115)
- \[[`785d5cd006`](https://github.com/nodejs/node/commit/785d5cd006)] - **(SEMVER-MAJOR)** **deps**: avoid compilation error with ASan (Michaël Zasso) [#50115](https://github.com/nodejs/node/pull/50115)
- \[[`7071c1dafd`](https://github.com/nodejs/node/commit/7071c1dafd)] - **(SEMVER-MAJOR)** **deps**: disable V8 concurrent sparkplug compilation (Michaël Zasso) [#50115](https://github.com/nodejs/node/pull/50115)
- \[[`d1d60b297d`](https://github.com/nodejs/node/commit/d1d60b297d)] - **(SEMVER-MAJOR)** **deps**: silence irrelevant V8 warning (Michaël Zasso) [#50115](https://github.com/nodejs/node/pull/50115)
- \[[`5b240c62f9`](https://github.com/nodejs/node/commit/5b240c62f9)] - **(SEMVER-MAJOR)** **deps**: always define V8_EXPORT_PRIVATE as no-op (Michaël Zasso) [#50115](https://github.com/nodejs/node/pull/50115)
- \[[`d8c97e4857`](https://github.com/nodejs/node/commit/d8c97e4857)] - **(SEMVER-MAJOR)** **deps**: update V8 to ********** (Michaël Zasso) [#50115](https://github.com/nodejs/node/pull/50115)
- \[[`b9df88a8c2`](https://github.com/nodejs/node/commit/b9df88a8c2)] - **(SEMVER-MAJOR)** **doc**: runtime deprecate flag --trace-atomics-wait (marco-ippolito) [#51179](https://github.com/nodejs/node/pull/51179)
- \[[`9ba5df30b4`](https://github.com/nodejs/node/commit/9ba5df30b4)] - **(SEMVER-MAJOR)** **doc**: bump FreeBSD experimental support to 13.2 (Michaël Zasso) [#51231](https://github.com/nodejs/node/pull/51231)
- \[[`900d79caf2`](https://github.com/nodejs/node/commit/900d79caf2)] - **(SEMVER-MAJOR)** **doc**: add migration paths for deprecated utils (Marco Ippolito) [#50488](https://github.com/nodejs/node/pull/50488)
- \[[`8206f6bb7f`](https://github.com/nodejs/node/commit/8206f6bb7f)] - **(SEMVER-MAJOR)** **fs**: runtime deprecate fs.Stats constructor (Marco Ippolito) [#52067](https://github.com/nodejs/node/pull/52067)
- \[[`c14133503a`](https://github.com/nodejs/node/commit/c14133503a)] - **(SEMVER-MAJOR)** **fs**: use private fields instead of symbols for `Dir` (Jungku Lee) [#51037](https://github.com/nodejs/node/pull/51037)
- \[[`abbdc3efaa`](https://github.com/nodejs/node/commit/abbdc3efaa)] - **(SEMVER-MAJOR)** **fs**: make stats date fields lazy (Yagiz Nizipli) [#50908](https://github.com/nodejs/node/pull/50908)
- \[[`4b76ccea95`](https://github.com/nodejs/node/commit/4b76ccea95)] - **(SEMVER-MAJOR)** **http**: preserve raw header duplicates in writeHead after setHeader calls (Tim Perry) [#50394](https://github.com/nodejs/node/pull/50394)
- \[[`c975384264`](https://github.com/nodejs/node/commit/c975384264)] - **(SEMVER-MAJOR)** **lib**: enable WebSocket by default (Aras Abbasi) [#51594](https://github.com/nodejs/node/pull/51594)
- \[[`351495e938`](https://github.com/nodejs/node/commit/351495e938)] - **(SEMVER-MAJOR)** **lib,test**: handle new Iterator global (Michaël Zasso) [#51362](https://github.com/nodejs/node/pull/51362)
- \[[`a8b21fdc90`](https://github.com/nodejs/node/commit/a8b21fdc90)] - **(SEMVER-MAJOR)** **process**: wait for `'exit'` before printing result (Antoine du Hamel) [#52172](https://github.com/nodejs/node/pull/52172)
- \[[`582ff5037c`](https://github.com/nodejs/node/commit/582ff5037c)] - **(SEMVER-MAJOR)** **src**: update NODE_MODULE_VERSION to 127 (Michaël Zasso) [#52465](https://github.com/nodejs/node/pull/52465)
- \[[`c5c4b50260`](https://github.com/nodejs/node/commit/c5c4b50260)] - **(SEMVER-MAJOR)** **src**: update NODE_MODULE_VERSION to 126 (Michaël Zasso) [#52293](https://github.com/nodejs/node/pull/52293)
- \[[`d248639285`](https://github.com/nodejs/node/commit/d248639285)] - **(SEMVER-MAJOR)** **src**: use supported API to get stalled TLA messages (Michaël Zasso) [#51362](https://github.com/nodejs/node/pull/51362)
- \[[`d34b02db4c`](https://github.com/nodejs/node/commit/d34b02db4c)] - **(SEMVER-MAJOR)** **src**: update default V8 platform to override functions with location (Etienne Pierre-Doray) [#51362](https://github.com/nodejs/node/pull/51362)
- \[[`d9c47e9b5f`](https://github.com/nodejs/node/commit/d9c47e9b5f)] - **(SEMVER-MAJOR)** **src**: add missing TryCatch (Michaël Zasso) [#51362](https://github.com/nodejs/node/pull/51362)
- \[[`5cddd3b2d8`](https://github.com/nodejs/node/commit/5cddd3b2d8)] - **(SEMVER-MAJOR)** **src**: update NODE_MODULE_VERSION to 124 (Michaël Zasso) [#51362](https://github.com/nodejs/node/pull/51362)
- \[[`1528846ada`](https://github.com/nodejs/node/commit/1528846ada)] - **(SEMVER-MAJOR)** **src**: use non-deprecated v8::Uint8Array::kMaxLength (Michaël Zasso) [#50115](https://github.com/nodejs/node/pull/50115)
- \[[`7166986626`](https://github.com/nodejs/node/commit/7166986626)] - **(SEMVER-MAJOR)** **src**: adapt to v8::Exception API change (Michaël Zasso) [#50115](https://github.com/nodejs/node/pull/50115)
- \[[`4782818020`](https://github.com/nodejs/node/commit/4782818020)] - **(SEMVER-MAJOR)** **src**: use non-deprecated version of CreateSyntheticModule (Michaël Zasso) [#50115](https://github.com/nodejs/node/pull/50115)
- \[[`2cff0ce411`](https://github.com/nodejs/node/commit/2cff0ce411)] - **(SEMVER-MAJOR)** **src**: update NODE_MODULE_VERSION to 122 (Michaël Zasso) [#50115](https://github.com/nodejs/node/pull/50115)
- \[[`1abff07392`](https://github.com/nodejs/node/commit/1abff07392)] - **(SEMVER-MAJOR)** **stream**: bump default highWaterMark (Robert Nagy) [#52037](https://github.com/nodejs/node/pull/52037)
- \[[`9efc84a2cb`](https://github.com/nodejs/node/commit/9efc84a2cb)] - **(SEMVER-MAJOR)** **test**: mark test-worker-arraybuffer-zerofill as flaky (Michaël Zasso) [#51362](https://github.com/nodejs/node/pull/51362)
- \[[`84c2e712eb`](https://github.com/nodejs/node/commit/84c2e712eb)] - **(SEMVER-MAJOR)** **test**: mark some GC-related tests as flaky (Michaël Zasso) [#51362](https://github.com/nodejs/node/pull/51362)
- \[[`cdc4437b87`](https://github.com/nodejs/node/commit/cdc4437b87)] - **(SEMVER-MAJOR)** **test**: allow slightly more diff in memory leak test (Michaël Zasso) [#51362](https://github.com/nodejs/node/pull/51362)
- \[[`515b007fae`](https://github.com/nodejs/node/commit/515b007fae)] - **(SEMVER-MAJOR)** **test**: replace always-opt flag with alway-turbofan (Michaël Zasso) [#50115](https://github.com/nodejs/node/pull/50115)
- \[[`2341805eb2`](https://github.com/nodejs/node/commit/2341805eb2)] - **(SEMVER-MAJOR)** **test**: remove tests that create very large buffers (Michaël Zasso) [#50115](https://github.com/nodejs/node/pull/50115)
- \[[`941cef5636`](https://github.com/nodejs/node/commit/941cef5636)] - **(SEMVER-MAJOR)** **test**: adapt to new V8 trusted memory spaces (Michaël Zasso) [#50115](https://github.com/nodejs/node/pull/50115)
- \[[`29de7f82cd`](https://github.com/nodejs/node/commit/29de7f82cd)] - **(SEMVER-MAJOR)** **test_runner**: omit filtered test from output (Colin Ihrig) [#52221](https://github.com/nodejs/node/pull/52221)
- \[[`00dc6d9d97`](https://github.com/nodejs/node/commit/00dc6d9d97)] - **(SEMVER-MAJOR)** **test_runner**: improve `--test-name-pattern` to allow matching single test (Michał Drobniak) [#51577](https://github.com/nodejs/node/pull/51577)
- \[[`5def8019d5`](https://github.com/nodejs/node/commit/5def8019d5)] - **(SEMVER-MAJOR)** **tools**: update V8 gypfiles for 12.4 (Michaël Zasso) [#52465](https://github.com/nodejs/node/pull/52465)
- \[[`c22793d050`](https://github.com/nodejs/node/commit/c22793d050)] - **(SEMVER-MAJOR)** **tools**: roughly port v8_abseil to gyp (Michaël Zasso) [#51362](https://github.com/nodejs/node/pull/51362)
- \[[`ffb0302f0c`](https://github.com/nodejs/node/commit/ffb0302f0c)] - **(SEMVER-MAJOR)** **tools**: update V8 gypfiles for 12.2 (Michaël Zasso) [#51362](https://github.com/nodejs/node/pull/51362)
- \[[`aadea12440`](https://github.com/nodejs/node/commit/aadea12440)] - **(SEMVER-MAJOR)** **tools**: update V8 gypfiles for 12.1 (Michaël Zasso) [#51362](https://github.com/nodejs/node/pull/51362)
- \[[`7784773967`](https://github.com/nodejs/node/commit/7784773967)] - **(SEMVER-MAJOR)** **tools**: update V8 gypfiles for 12.0 (Michaël Zasso) [#51362](https://github.com/nodejs/node/pull/51362)
- \[[`9fe0424baa`](https://github.com/nodejs/node/commit/9fe0424baa)] - **(SEMVER-MAJOR)** **trace_events**: use private fields instead of symbols for `Tracing` (Jungku Lee) [#51180](https://github.com/nodejs/node/pull/51180)
- \[[`e96cd25007`](https://github.com/nodejs/node/commit/e96cd25007)] - **(SEMVER-MAJOR)** **util**: runtime deprecate util.log (Marco Ippolito) [#50488](https://github.com/nodejs/node/pull/50488)
- \[[`6cf20d5e43`](https://github.com/nodejs/node/commit/6cf20d5e43)] - **(SEMVER-MAJOR)** **util**: runtime deprecate util.isUndefined (Marco Ippolito) [#50488](https://github.com/nodejs/node/pull/50488)
- \[[`09e424921f`](https://github.com/nodejs/node/commit/09e424921f)] - **(SEMVER-MAJOR)** **util**: runtime deprecate util.isSymbol (Marco Ippolito) [#50488](https://github.com/nodejs/node/pull/50488)
- \[[`80b6bfd4e9`](https://github.com/nodejs/node/commit/80b6bfd4e9)] - **(SEMVER-MAJOR)** **util**: runtime deprecate util.isString (Marco Ippolito) [#50488](https://github.com/nodejs/node/pull/50488)
- \[[`d419edded9`](https://github.com/nodejs/node/commit/d419edded9)] - **(SEMVER-MAJOR)** **util**: runtime deprecate util.isRegExp (Marco Ippolito) [#50488](https://github.com/nodejs/node/pull/50488)
- \[[`e0b8de78ed`](https://github.com/nodejs/node/commit/e0b8de78ed)] - **(SEMVER-MAJOR)** **util**: runtime deprecate util.isPrimitive (Marco Ippolito) [#50488](https://github.com/nodejs/node/pull/50488)
- \[[`5478e1129a`](https://github.com/nodejs/node/commit/5478e1129a)] - **(SEMVER-MAJOR)** **util**: runtime deprecate util.isObject (Marco Ippolito) [#50488](https://github.com/nodejs/node/pull/50488)
- \[[`b05b1dd541`](https://github.com/nodejs/node/commit/b05b1dd541)] - **(SEMVER-MAJOR)** **util**: runtime deprecate util.isNumber (Marco Ippolito) [#50488](https://github.com/nodejs/node/pull/50488)
- \[[`5af9bf5f6a`](https://github.com/nodejs/node/commit/5af9bf5f6a)] - **(SEMVER-MAJOR)** **util**: runtime deprecate util.isNullOrUndefined (Marco Ippolito) [#50488](https://github.com/nodejs/node/pull/50488)
- \[[`860a10e10e`](https://github.com/nodejs/node/commit/860a10e10e)] - **(SEMVER-MAJOR)** **util**: runtime deprecate util.isNull (Marco Ippolito) [#50488](https://github.com/nodejs/node/pull/50488)
- \[[`70330f5c2b`](https://github.com/nodejs/node/commit/70330f5c2b)] - **(SEMVER-MAJOR)** **util**: runtime deprecate util.isFunction (Marco Ippolito) [#50488](https://github.com/nodejs/node/pull/50488)
- \[[`7c69c33acc`](https://github.com/nodejs/node/commit/7c69c33acc)] - **(SEMVER-MAJOR)** **util**: runtime deprecate util.isError (Marco Ippolito) [#50488](https://github.com/nodejs/node/pull/50488)
- \[[`a0c5b871a9`](https://github.com/nodejs/node/commit/a0c5b871a9)] - **(SEMVER-MAJOR)** **util**: runtime deprecate util.isDate (Marco Ippolito) [#50488](https://github.com/nodejs/node/pull/50488)
- \[[`3c670cb15d`](https://github.com/nodejs/node/commit/3c670cb15d)] - **(SEMVER-MAJOR)** **util**: runtime deprecation util.isBuffer (Marco Ippolito) [#50488](https://github.com/nodejs/node/pull/50488)
- \[[`c17a448ca9`](https://github.com/nodejs/node/commit/c17a448ca9)] - **(SEMVER-MAJOR)** **util**: runtime deprecation util.isBoolean (Marco Ippolito) [#50488](https://github.com/nodejs/node/pull/50488)
- \[[`fbb2f891aa`](https://github.com/nodejs/node/commit/fbb2f891aa)] - **(SEMVER-MAJOR)** **util**: runtime deprecate util.isArray (Marco Ippolito) [#50488](https://github.com/nodejs/node/pull/50488)
- \[[`22d8062e42`](https://github.com/nodejs/node/commit/22d8062e42)] - **(SEMVER-MAJOR)** **util**: runtime deprecation util.\_extend (Marco Ippolito) [#50488](https://github.com/nodejs/node/pull/50488)
- \[[`1a5acd0638`](https://github.com/nodejs/node/commit/1a5acd0638)] - **(SEMVER-MAJOR)** **v8**: enable maglev on supported architectures (Keyhan Vakil) [#51360](https://github.com/nodejs/node/pull/51360)

### Semver-Minor Commits

- \[[`128c60d906`](https://github.com/nodejs/node/commit/128c60d906)] - **(SEMVER-MINOR)** **cli**: implement `node --run <script-in-package-json>` (Yagiz Nizipli) [#52190](https://github.com/nodejs/node/pull/52190)
- \[[`f69946b905`](https://github.com/nodejs/node/commit/f69946b905)] - **(SEMVER-MINOR)** **deps**: update simdutf to 5.0.0 (Daniel Lemire) [#52138](https://github.com/nodejs/node/pull/52138)
- \[[`828ad42eee`](https://github.com/nodejs/node/commit/828ad42eee)] - **(SEMVER-MINOR)** **deps**: update undici to 6.3.0 (Node.js GitHub Bot) [#51462](https://github.com/nodejs/node/pull/51462)
- \[[`05f8172188`](https://github.com/nodejs/node/commit/05f8172188)] - **(SEMVER-MINOR)** **deps**: update undici to 6.2.1 (Node.js GitHub Bot) [#51278](https://github.com/nodejs/node/pull/51278)
- \[[`a0c466810a`](https://github.com/nodejs/node/commit/a0c466810a)] - **(SEMVER-MINOR)** **doc**: deprecate fs.Stats public constructor (Marco Ippolito) [#51879](https://github.com/nodejs/node/pull/51879)
- \[[`151d365ad1`](https://github.com/nodejs/node/commit/151d365ad1)] - **(SEMVER-MINOR)** **fs**: expose glob and globSync (Moshe Atlow) [#51912](https://github.com/nodejs/node/pull/51912)
- \[[`5f7fad2605`](https://github.com/nodejs/node/commit/5f7fad2605)] - **(SEMVER-MINOR)** **module**: support require()ing synchronous ESM graphs (Joyee Cheung) [#51977](https://github.com/nodejs/node/pull/51977)
- \[[`009665fb56`](https://github.com/nodejs/node/commit/009665fb56)] - **(SEMVER-MINOR)** **report**: add `--report-exclude-network` option (Ethan Arrowood) [#51645](https://github.com/nodejs/node/pull/51645)
- \[[`80f86e5d02`](https://github.com/nodejs/node/commit/80f86e5d02)] - **(SEMVER-MINOR)** **src**: add C++ ProcessEmitWarningSync() (Joyee Cheung) [#51977](https://github.com/nodejs/node/pull/51977)
- \[[`78be0d0f1c`](https://github.com/nodejs/node/commit/78be0d0f1c)] - **(SEMVER-MINOR)** **src**: add uv_get_available_memory to report and process (theanarkh) [#52023](https://github.com/nodejs/node/pull/52023)
- \[[`b34512e38e`](https://github.com/nodejs/node/commit/b34512e38e)] - **(SEMVER-MINOR)** **src**: preload function for Environment (Cheng Zhao) [#51539](https://github.com/nodejs/node/pull/51539)
- \[[`7d258db1d7`](https://github.com/nodejs/node/commit/7d258db1d7)] - **(SEMVER-MINOR)** **stream**: support typed arrays (IlyasShabi) [#51866](https://github.com/nodejs/node/pull/51866)
- \[[`5276c0d5d4`](https://github.com/nodejs/node/commit/5276c0d5d4)] - **(SEMVER-MINOR)** **test_runner**: add suite() (Colin Ihrig) [#52127](https://github.com/nodejs/node/pull/52127)
- \[[`84de97a61e`](https://github.com/nodejs/node/commit/84de97a61e)] - **(SEMVER-MINOR)** **test_runner**: support forced exit (Colin Ihrig) [#52038](https://github.com/nodejs/node/pull/52038)
- \[[`aac5ad901d`](https://github.com/nodejs/node/commit/aac5ad901d)] - **(SEMVER-MINOR)** **test_runner**: add `test:complete` event to reflect execution order (Moshe Atlow) [#51909](https://github.com/nodejs/node/pull/51909)
- \[[`9a1e01c4ce`](https://github.com/nodejs/node/commit/9a1e01c4ce)] - **(SEMVER-MINOR)** **util**: support array of formats in util.styleText (Marco Ippolito) [#52040](https://github.com/nodejs/node/pull/52040)
- \[[`7f2d61f82a`](https://github.com/nodejs/node/commit/7f2d61f82a)] - **(SEMVER-MINOR)** **v8**: implement v8.queryObjects() for memory leak regression testing (Joyee Cheung) [#51927](https://github.com/nodejs/node/pull/51927)
- \[[`d1d5da22e4`](https://github.com/nodejs/node/commit/d1d5da22e4)] - **(SEMVER-MINOR)** **vm**: harden module type checks (Chengzhong Wu) [#52162](https://github.com/nodejs/node/pull/52162)

### Semver-Patch Commits

- \[[`a760dadec3`](https://github.com/nodejs/node/commit/a760dadec3)] - **benchmark**: add AbortSignal.abort benchmarks (Raz Luvaton) [#52408](https://github.com/nodejs/node/pull/52408)
- \[[`47c934e464`](https://github.com/nodejs/node/commit/47c934e464)] - **benchmark**: conditionally use spawn with taskset for cpu pinning (Ali Hassan) [#52253](https://github.com/nodejs/node/pull/52253)
- \[[`dde0cffb2e`](https://github.com/nodejs/node/commit/dde0cffb2e)] - **benchmark**: add toNamespacedPath bench (Rafael Gonzaga) [#52236](https://github.com/nodejs/node/pull/52236)
- \[[`bda66ad711`](https://github.com/nodejs/node/commit/bda66ad711)] - **benchmark**: add style-text benchmark (Rafael Gonzaga) [#52004](https://github.com/nodejs/node/pull/52004)
- \[[`21211a3fa9`](https://github.com/nodejs/node/commit/21211a3fa9)] - **buffer**: improve `btoa` performance (Yagiz Nizipli) [#52427](https://github.com/nodejs/node/pull/52427)
- \[[`6f504b71ac`](https://github.com/nodejs/node/commit/6f504b71ac)] - **buffer**: use simdutf for `atob` implementation (Yagiz Nizipli) [#52381](https://github.com/nodejs/node/pull/52381)
- \[[`0ce7365856`](https://github.com/nodejs/node/commit/0ce7365856)] - **build**: temporary disable ubsan (Rafael Gonzaga) [#52560](https://github.com/nodejs/node/pull/52560)
- \[[`4e278f0253`](https://github.com/nodejs/node/commit/4e278f0253)] - **build**: speed up compilation of some V8 files (Michaël Zasso) [#52083](https://github.com/nodejs/node/pull/52083)
- \[[`ba06c5c509`](https://github.com/nodejs/node/commit/ba06c5c509)] - **build,tools**: add test-ubsan ci (Rafael Gonzaga) [#46297](https://github.com/nodejs/node/pull/46297)
- \[[`562369f348`](https://github.com/nodejs/node/commit/562369f348)] - **child_process**: use internal addAbortListener (Chemi Atlow) [#52081](https://github.com/nodejs/node/pull/52081)
- \[[`8f61b658de`](https://github.com/nodejs/node/commit/8f61b658de)] - **crypto**: deprecate implicitly shortened GCM tags (Tobias Nießen) [#52345](https://github.com/nodejs/node/pull/52345)
- \[[`08609b5222`](https://github.com/nodejs/node/commit/08609b5222)] - **crypto**: make timingSafeEqual faster for Uint8Array (Tobias Nießen) [#52341](https://github.com/nodejs/node/pull/52341)
- \[[`9f939f5af7`](https://github.com/nodejs/node/commit/9f939f5af7)] - **crypto**: reject Ed25519/Ed448 in Sign/Verify prototypes (Filip Skokan) [#52340](https://github.com/nodejs/node/pull/52340)
- \[[`2241e8c5b3`](https://github.com/nodejs/node/commit/2241e8c5b3)] - **crypto**: validate RSA-PSS saltLength in subtle.sign and subtle.verify (Filip Skokan) [#52262](https://github.com/nodejs/node/pull/52262)
- \[[`6dd1c75f4a`](https://github.com/nodejs/node/commit/6dd1c75f4a)] - **crypto**: fix `input` validation in `crypto.hash` (Antoine du Hamel) [#52070](https://github.com/nodejs/node/pull/52070)
- \[[`a1d48f4a26`](https://github.com/nodejs/node/commit/a1d48f4a26)] - **deps**: update simdutf to 5.2.4 (Node.js GitHub Bot) [#52473](https://github.com/nodejs/node/pull/52473)
- \[[`08ff4a0c9d`](https://github.com/nodejs/node/commit/08ff4a0c9d)] - **deps**: update nghttp2 to 1.61.0 (Node.js GitHub Bot) [#52395](https://github.com/nodejs/node/pull/52395)
- \[[`cf629366b9`](https://github.com/nodejs/node/commit/cf629366b9)] - **deps**: update simdutf to 5.2.3 (Yagiz Nizipli) [#52381](https://github.com/nodejs/node/pull/52381)
- \[[`ad86a12964`](https://github.com/nodejs/node/commit/ad86a12964)] - **deps**: upgrade npm to 10.5.1 (npm team) [#52351](https://github.com/nodejs/node/pull/52351)
- \[[`45cc32c9c6`](https://github.com/nodejs/node/commit/45cc32c9c6)] - **deps**: update c-ares to 1.28.1 (Node.js GitHub Bot) [#52285](https://github.com/nodejs/node/pull/52285)
- \[[`38161c38d9`](https://github.com/nodejs/node/commit/38161c38d9)] - **deps**: update zlib to *******-motley-24c07df (Node.js GitHub Bot) [#52199](https://github.com/nodejs/node/pull/52199)
- \[[`1264414700`](https://github.com/nodejs/node/commit/1264414700)] - **deps**: update simdjson to 3.8.0 (Node.js GitHub Bot) [#52124](https://github.com/nodejs/node/pull/52124)
- \[[`f6996ee150`](https://github.com/nodejs/node/commit/f6996ee150)] - **deps**: V8: backport c4be0a97f981 (Richard Lau) [#52183](https://github.com/nodejs/node/pull/52183)
- \[[`0d4bc4c40e`](https://github.com/nodejs/node/commit/0d4bc4c40e)] - **deps**: V8: cherry-pick f8d5e576b814 (Richard Lau) [#52183](https://github.com/nodejs/node/pull/52183)
- \[[`70a05103c8`](https://github.com/nodejs/node/commit/70a05103c8)] - **deps**: update zlib to *******-motley-24342f6 (Node.js GitHub Bot) [#52123](https://github.com/nodejs/node/pull/52123)
- \[[`4c3e9659ed`](https://github.com/nodejs/node/commit/4c3e9659ed)] - **deps**: update corepack to 0.26.0 (Node.js GitHub Bot) [#52027](https://github.com/nodejs/node/pull/52027)
- \[[`0b4cdb4b42`](https://github.com/nodejs/node/commit/0b4cdb4b42)] - **deps**: update ada to 2.7.7 (Node.js GitHub Bot) [#52028](https://github.com/nodejs/node/pull/52028)
- \[[`b241a1d0ae`](https://github.com/nodejs/node/commit/b241a1d0ae)] - **deps**: update simdutf to 4.0.9 (Node.js GitHub Bot) [#51655](https://github.com/nodejs/node/pull/51655)
- \[[`36dcd399c0`](https://github.com/nodejs/node/commit/36dcd399c0)] - **deps**: upgrade libuv to 1.48.0 (Santiago Gimeno) [#51697](https://github.com/nodejs/node/pull/51697)
- \[[`8cf313cd72`](https://github.com/nodejs/node/commit/8cf313cd72)] - **deps**: update undici to 6.6.0 (Node.js GitHub Bot) [#51630](https://github.com/nodejs/node/pull/51630)
- \[[`dd4767f99f`](https://github.com/nodejs/node/commit/dd4767f99f)] - **deps**: update undici to 6.4.0 (Node.js GitHub Bot) [#51527](https://github.com/nodejs/node/pull/51527)
- \[[`8362caa7d8`](https://github.com/nodejs/node/commit/8362caa7d8)] - **dgram**: use internal addAbortListener (Chemi Atlow) [#52081](https://github.com/nodejs/node/pull/52081)
- \[[`4f3cf4e89a`](https://github.com/nodejs/node/commit/4f3cf4e89a)] - **diagnostics_channel**: early-exit tracing channel trace methods (Stephen Belanger) [#51915](https://github.com/nodejs/node/pull/51915)
- \[[`204018bba6`](https://github.com/nodejs/node/commit/204018bba6)] - **doc**: deprecate --experimental-policy (RafaelGSS) [#52602](https://github.com/nodejs/node/pull/52602)
- \[[`d32a914ac7`](https://github.com/nodejs/node/commit/d32a914ac7)] - **doc**: add lint-js-fix into BUILDING.md (jakecastelli) [#52290](https://github.com/nodejs/node/pull/52290)
- \[[`411503bacd`](https://github.com/nodejs/node/commit/411503bacd)] - **doc**: remove Internet Explorer mention in BUILDING.md (Rich Trott) [#52455](https://github.com/nodejs/node/pull/52455)
- \[[`e9ccf5aba2`](https://github.com/nodejs/node/commit/e9ccf5aba2)] - **doc**: accommodate upcoming stricter .md linting (Rich Trott) [#52454](https://github.com/nodejs/node/pull/52454)
- \[[`b4186ec2c1`](https://github.com/nodejs/node/commit/b4186ec2c1)] - **doc**: add Rafael to steward list (Rafael Gonzaga) [#52452](https://github.com/nodejs/node/pull/52452)
- \[[`7b01bfb2be`](https://github.com/nodejs/node/commit/7b01bfb2be)] - **doc**: correct naming convention in C++ style guide (Mohammed Keyvanzadeh) [#52424](https://github.com/nodejs/node/pull/52424)
- \[[`c82f3c9e80`](https://github.com/nodejs/node/commit/c82f3c9e80)] - **doc**: update `process.execArg` example to be more useful (Jacob Smith) [#52412](https://github.com/nodejs/node/pull/52412)
- \[[`655b327a4d`](https://github.com/nodejs/node/commit/655b327a4d)] - **doc**: call out http(s).globalAgent default (mathis-west-1) [#52392](https://github.com/nodejs/node/pull/52392)
- \[[`2c77be5488`](https://github.com/nodejs/node/commit/2c77be5488)] - **doc**: update the location of `build_with_cmake` (Emmanuel Ferdman) [#52356](https://github.com/nodejs/node/pull/52356)
- \[[`7dd514f2db`](https://github.com/nodejs/node/commit/7dd514f2db)] - **doc**: reserve 125 for Electron 31 (Shelley Vohr) [#52379](https://github.com/nodejs/node/pull/52379)
- \[[`756acd0877`](https://github.com/nodejs/node/commit/756acd0877)] - **doc**: use consistent plural form of "index" (Rich Trott) [#52373](https://github.com/nodejs/node/pull/52373)
- \[[`ba07e4e5e6`](https://github.com/nodejs/node/commit/ba07e4e5e6)] - **doc**: fix typo in cli.md (Daeyeon Jeong) [#52388](https://github.com/nodejs/node/pull/52388)
- \[[`461d9d665d`](https://github.com/nodejs/node/commit/461d9d665d)] - **doc**: add Rafael to sec release stewards (Rafael Gonzaga) [#52354](https://github.com/nodejs/node/pull/52354)
- \[[`d0c364a844`](https://github.com/nodejs/node/commit/d0c364a844)] - **doc**: document missing options of events.on (Chemi Atlow) [#52080](https://github.com/nodejs/node/pull/52080)
- \[[`a63261cf2c`](https://github.com/nodejs/node/commit/a63261cf2c)] - **doc**: add missing space (Augustin Mauroy) [#52360](https://github.com/nodejs/node/pull/52360)
- \[[`dd711d221a`](https://github.com/nodejs/node/commit/dd711d221a)] - **doc**: add tips about vcpkg cause build faild on windows (Cong Zhang) [#52181](https://github.com/nodejs/node/pull/52181)
- \[[`4df34cf6dd`](https://github.com/nodejs/node/commit/4df34cf6dd)] - **doc**: replace "below" with "following" (Rich Trott) [#52315](https://github.com/nodejs/node/pull/52315)
- \[[`d9aa33fdbf`](https://github.com/nodejs/node/commit/d9aa33fdbf)] - **doc**: fix email pattern to be wrapped with `<<` instead of single `<` (Raz Luvaton) [#52284](https://github.com/nodejs/node/pull/52284)
- \[[`903f28e684`](https://github.com/nodejs/node/commit/903f28e684)] - **doc**: update release gpg keyserver (marco-ippolito) [#52257](https://github.com/nodejs/node/pull/52257)
- \[[`fd55458770`](https://github.com/nodejs/node/commit/fd55458770)] - **doc**: add release key for marco-ippolito (marco-ippolito) [#52257](https://github.com/nodejs/node/pull/52257)
- \[[`27493a1dd7`](https://github.com/nodejs/node/commit/27493a1dd7)] - **doc**: fix arrow vertical alignment in HTML version (Akash Yeole) [#52193](https://github.com/nodejs/node/pull/52193)
- \[[`af48641993`](https://github.com/nodejs/node/commit/af48641993)] - **doc**: move TSC members from regular to emeritus (Michael Dawson) [#52209](https://github.com/nodejs/node/pull/52209)
- \[[`fa13ed6d79`](https://github.com/nodejs/node/commit/fa13ed6d79)] - **doc**: add section explaining todo tests (Colin Ihrig) [#52204](https://github.com/nodejs/node/pull/52204)
- \[[`312ebd97c2`](https://github.com/nodejs/node/commit/312ebd97c2)] - **doc**: edit `ChildProcess` `'message'` event docs (theanarkh) [#52154](https://github.com/nodejs/node/pull/52154)
- \[[`f1635f442f`](https://github.com/nodejs/node/commit/f1635f442f)] - **doc**: quote test_runner glob parameters (Fabian Meyer) [#52201](https://github.com/nodejs/node/pull/52201)
- \[[`fc029181df`](https://github.com/nodejs/node/commit/fc029181df)] - **doc**: add mold to speeding up section (Cong Zhang) [#52179](https://github.com/nodejs/node/pull/52179)
- \[[`8bd3cb2f8c`](https://github.com/nodejs/node/commit/8bd3cb2f8c)] - **doc**: http event order correction (wh0) [#51464](https://github.com/nodejs/node/pull/51464)
- \[[`a7f170e45a`](https://github.com/nodejs/node/commit/a7f170e45a)] - **doc**: move gabrielschulhof to TSC emeritus (Gabriel Schulhof) [#52192](https://github.com/nodejs/node/pull/52192)
- \[[`305375ac16`](https://github.com/nodejs/node/commit/305375ac16)] - **doc**: fix `--env-file` docs for valid quotes for defining values (Gabriel Bota) [#52157](https://github.com/nodejs/node/pull/52157)
- \[[`3fcaf7b900`](https://github.com/nodejs/node/commit/3fcaf7b900)] - **doc**: clarify what is supported in NODE_OPTIONS (Michael Dawson) [#52076](https://github.com/nodejs/node/pull/52076)
- \[[`4fe87357f3`](https://github.com/nodejs/node/commit/4fe87357f3)] - **doc**: fix typos in maintaining-dependencies.md (RoboSchmied) [#52160](https://github.com/nodejs/node/pull/52160)
- \[[`f1949ac1ae`](https://github.com/nodejs/node/commit/f1949ac1ae)] - **doc**: add spec for contains module syntax (Geoffrey Booth) [#52059](https://github.com/nodejs/node/pull/52059)
- \[[`707155424b`](https://github.com/nodejs/node/commit/707155424b)] - **doc**: optimize the doc about Unix abstract socket (theanarkh) [#52043](https://github.com/nodejs/node/pull/52043)
- \[[`8a191e4e6a`](https://github.com/nodejs/node/commit/8a191e4e6a)] - **doc**: update pnpm link (Superchupu) [#52113](https://github.com/nodejs/node/pull/52113)
- \[[`454d0806a1`](https://github.com/nodejs/node/commit/454d0806a1)] - **doc**: remove ableist language from crypto (Jamie King) [#52063](https://github.com/nodejs/node/pull/52063)
- \[[`dafe004703`](https://github.com/nodejs/node/commit/dafe004703)] - **doc**: update collaborator email (Ruy Adorno) [#52088](https://github.com/nodejs/node/pull/52088)
- \[[`8824adb031`](https://github.com/nodejs/node/commit/8824adb031)] - **doc**: state that removing npm is a non-goal (Geoffrey Booth) [#51951](https://github.com/nodejs/node/pull/51951)
- \[[`b360532f1a`](https://github.com/nodejs/node/commit/b360532f1a)] - **doc**: mention NodeSource in RafaelGSS steward list (Rafael Gonzaga) [#52057](https://github.com/nodejs/node/pull/52057)
- \[[`57d2e4881c`](https://github.com/nodejs/node/commit/57d2e4881c)] - **doc**: remove ArrayBuffer from crypto.hash() data parameter type (fengmk2) [#52069](https://github.com/nodejs/node/pull/52069)
- \[[`e11c1d2315`](https://github.com/nodejs/node/commit/e11c1d2315)] - **doc**: add some commonly used lables up gront (Michael Dawson) [#52006](https://github.com/nodejs/node/pull/52006)
- \[[`8f9f5db1e8`](https://github.com/nodejs/node/commit/8f9f5db1e8)] - **doc**: document that `const c2 = vm.createContext(c1); c1 === c2` is true (Daniel Kaplan) [#51960](https://github.com/nodejs/node/pull/51960)
- \[[`d78a565713`](https://github.com/nodejs/node/commit/d78a565713)] - **doc**: clarify what moderation issues are for (Antoine du Hamel) [#51990](https://github.com/nodejs/node/pull/51990)
- \[[`4cac07c931`](https://github.com/nodejs/node/commit/4cac07c931)] - **doc**: add Hemanth HM mention to v21.7.0 changelog (Rafael Gonzaga) [#52008](https://github.com/nodejs/node/pull/52008)
- \[[`73025c4dec`](https://github.com/nodejs/node/commit/73025c4dec)] - **doc**: add UlisesGascon as a collaborator (Ulises Gascón) [#51991](https://github.com/nodejs/node/pull/51991)
- \[[`999c6b34fb`](https://github.com/nodejs/node/commit/999c6b34fb)] - **doc**: test for cli options (Aras Abbasi) [#51623](https://github.com/nodejs/node/pull/51623)
- \[[`edd6190836`](https://github.com/nodejs/node/commit/edd6190836)] - **doc**: deprecate hmac public constructor (Marco Ippolito) [#51881](https://github.com/nodejs/node/pull/51881)
- \[[`25c79f3331`](https://github.com/nodejs/node/commit/25c79f3331)] - **esm**: drop support for import assertions (Nicolò Ribaudo) [#52104](https://github.com/nodejs/node/pull/52104)
- \[[`d619aab575`](https://github.com/nodejs/node/commit/d619aab575)] - **events**: rename high & low watermark for consistency (Chemi Atlow) [#52080](https://github.com/nodejs/node/pull/52080)
- \[[`e263946c2e`](https://github.com/nodejs/node/commit/e263946c2e)] - **events**: extract addAbortListener for safe internal use (Chemi Atlow) [#52081](https://github.com/nodejs/node/pull/52081)
- \[[`40ef2da8d6`](https://github.com/nodejs/node/commit/40ef2da8d6)] - **events**: remove abort listener from signal in `on` (Neal Beeken) [#51091](https://github.com/nodejs/node/pull/51091)
- \[[`61e5de1268`](https://github.com/nodejs/node/commit/61e5de1268)] - **fs**: refactor maybeCallback function (Yagiz Nizipli) [#52129](https://github.com/nodejs/node/pull/52129)
- \[[`39f1b899cd`](https://github.com/nodejs/node/commit/39f1b899cd)] - **fs**: fix edge case in readFileSync utf8 fast path (Richard Lau) [#52101](https://github.com/nodejs/node/pull/52101)
- \[[`639c096004`](https://github.com/nodejs/node/commit/639c096004)] - **fs**: validate fd from cpp on `fchown` (Yagiz Nizipli) [#52051](https://github.com/nodejs/node/pull/52051)
- \[[`9ac1fe05d7`](https://github.com/nodejs/node/commit/9ac1fe05d7)] - **fs**: validate fd from cpp on `close` (Yagiz Nizipli) [#52051](https://github.com/nodejs/node/pull/52051)
- \[[`3ec20f25df`](https://github.com/nodejs/node/commit/3ec20f25df)] - **fs**: validate file mode from cpp (Yagiz Nizipli) [#52050](https://github.com/nodejs/node/pull/52050)
- \[[`8c0b723ccb`](https://github.com/nodejs/node/commit/8c0b723ccb)] - **fs,permission**: make handling of buffers consistent (Tobias Nießen) [#52348](https://github.com/nodejs/node/pull/52348)
- \[[`3fc8d2200e`](https://github.com/nodejs/node/commit/3fc8d2200e)] - **http2**: fix h2-over-h2 connection proxying (Tim Perry) [#52368](https://github.com/nodejs/node/pull/52368)
- \[[`b9d8a14a03`](https://github.com/nodejs/node/commit/b9d8a14a03)] - **http2**: use internal addAbortListener (Chemi Atlow) [#52081](https://github.com/nodejs/node/pull/52081)
- \[[`818c10e86d`](https://github.com/nodejs/node/commit/818c10e86d)] - **lib**: improve perf of `AbortSignal` creation (Raz Luvaton) [#52408](https://github.com/nodejs/node/pull/52408)
- \[[`3f5ff8dc20`](https://github.com/nodejs/node/commit/3f5ff8dc20)] - **lib**: .load .save add proper error message when no file passed (Thomas Mauran) [#52225](https://github.com/nodejs/node/pull/52225)
- \[[`0a252c23d9`](https://github.com/nodejs/node/commit/0a252c23d9)] - **lib**: fix type error for \_refreshLine (Jackson Tian) [#52133](https://github.com/nodejs/node/pull/52133)
- \[[`14de082ab4`](https://github.com/nodejs/node/commit/14de082ab4)] - **lib**: emit listening event once when call listen twice (theanarkh) [#52119](https://github.com/nodejs/node/pull/52119)
- \[[`4e9ce7c035`](https://github.com/nodejs/node/commit/4e9ce7c035)] - **lib**: make sure clear the old timer in http server (theanarkh) [#52118](https://github.com/nodejs/node/pull/52118)
- \[[`20525f14b9`](https://github.com/nodejs/node/commit/20525f14b9)] - **lib**: fix listen with handle in cluster worker (theanarkh) [#52056](https://github.com/nodejs/node/pull/52056)
- \[[`8df54481f4`](https://github.com/nodejs/node/commit/8df54481f4)] - **meta**: bump actions/download-artifact from 4.1.3 to 4.1.4 (dependabot\[bot]) [#52314](https://github.com/nodejs/node/pull/52314)
- \[[`bcc102147a`](https://github.com/nodejs/node/commit/bcc102147a)] - **meta**: bump rtCamp/action-slack-notify from 2.2.1 to 2.3.0 (dependabot\[bot]) [#52313](https://github.com/nodejs/node/pull/52313)
- \[[`4e7e0ef9c3`](https://github.com/nodejs/node/commit/4e7e0ef9c3)] - **meta**: bump github/codeql-action from 3.24.6 to 3.24.9 (dependabot\[bot]) [#52312](https://github.com/nodejs/node/pull/52312)
- \[[`14a39881b8`](https://github.com/nodejs/node/commit/14a39881b8)] - **meta**: bump actions/cache from 4.0.1 to 4.0.2 (dependabot\[bot]) [#52311](https://github.com/nodejs/node/pull/52311)
- \[[`2f8f90dadb`](https://github.com/nodejs/node/commit/2f8f90dadb)] - **meta**: bump actions/setup-python from 5.0.0 to 5.1.0 (dependabot\[bot]) [#52310](https://github.com/nodejs/node/pull/52310)
- \[[`95efdaf01a`](https://github.com/nodejs/node/commit/95efdaf01a)] - **meta**: bump codecov/codecov-action from 4.1.0 to 4.1.1 (dependabot\[bot]) [#52308](https://github.com/nodejs/node/pull/52308)
- \[[`24c1a8e739`](https://github.com/nodejs/node/commit/24c1a8e739)] - **meta**: move one or more collaborators to emeritus (Node.js GitHub Bot) [#52300](https://github.com/nodejs/node/pull/52300)
- \[[`60dcfad91e`](https://github.com/nodejs/node/commit/60dcfad91e)] - **meta**: pass Codecov upload token to codecov action (Michaël Zasso) [#51982](https://github.com/nodejs/node/pull/51982)
- \[[`db1746182b`](https://github.com/nodejs/node/commit/db1746182b)] - **module**: disallow CJS <-> ESM edges in a cycle from require(esm) (Joyee Cheung) [#52264](https://github.com/nodejs/node/pull/52264)
- \[[`d6b57f6629`](https://github.com/nodejs/node/commit/d6b57f6629)] - **module**: centralize SourceTextModule compilation for builtin loader (Joyee Cheung) [#52291](https://github.com/nodejs/node/pull/52291)
- \[[`f4a0a3b04b`](https://github.com/nodejs/node/commit/f4a0a3b04b)] - **module**: warn on detection in typeless package (Geoffrey Booth) [#52168](https://github.com/nodejs/node/pull/52168)
- \[[`8bc745944e`](https://github.com/nodejs/node/commit/8bc745944e)] - **module**: eliminate performance cost of detection for cjs entry (Geoffrey Booth) [#52093](https://github.com/nodejs/node/pull/52093)
- \[[`63d04d4d80`](https://github.com/nodejs/node/commit/63d04d4d80)] - **module**: fix detect-module not retrying as esm for cjs-only errors (Geoffrey Booth) [#52024](https://github.com/nodejs/node/pull/52024)
- \[[`575ced8139`](https://github.com/nodejs/node/commit/575ced8139)] - **module**: print location of unsettled top-level await in entry points (Joyee Cheung) [#51999](https://github.com/nodejs/node/pull/51999)
- \[[`075c95f61f`](https://github.com/nodejs/node/commit/075c95f61f)] - **module**: refactor ESM loader initialization and entry point handling (Joyee Cheung) [#51999](https://github.com/nodejs/node/pull/51999)
- \[[`45f0dd0192`](https://github.com/nodejs/node/commit/45f0dd0192)] - **module,win**: fix long path resolve (Stefan Stojanovic) [#51097](https://github.com/nodejs/node/pull/51097)
- \[[`d89fc73d45`](https://github.com/nodejs/node/commit/d89fc73d45)] - **net**: use internal addAbortListener (Chemi Atlow) [#52081](https://github.com/nodejs/node/pull/52081)
- \[[`f0e6acde2d`](https://github.com/nodejs/node/commit/f0e6acde2d)] - **node-api**: make tsfn accept napi_finalize once more (Gabriel Schulhof) [#51801](https://github.com/nodejs/node/pull/51801)
- \[[`ff93f3e1a8`](https://github.com/nodejs/node/commit/ff93f3e1a8)] - **readline**: use internal addAbortListener (Chemi Atlow) [#52081](https://github.com/nodejs/node/pull/52081)
- \[[`4a6ca7a1d4`](https://github.com/nodejs/node/commit/4a6ca7a1d4)] - **src**: remove erroneous CVE-2024-27980 revert option (Tobias Nießen) [#52543](https://github.com/nodejs/node/pull/52543)
- \[[`64b67779f7`](https://github.com/nodejs/node/commit/64b67779f7)] - **src**: disallow direct .bat and .cmd file spawning (Ben Noordhuis) [nodejs-private/node-private#560](https://github.com/nodejs-private/node-private/pull/560)
- \[[`9ef724bc81`](https://github.com/nodejs/node/commit/9ef724bc81)] - **src**: update branch name in node_revert.h (Tobias Nießen) [#52390](https://github.com/nodejs/node/pull/52390)
- \[[`ec1550407b`](https://github.com/nodejs/node/commit/ec1550407b)] - **src**: stop using `v8::BackingStore::Reallocate` (Michaël Zasso) [#52292](https://github.com/nodejs/node/pull/52292)
- \[[`681b0a3df3`](https://github.com/nodejs/node/commit/681b0a3df3)] - **src**: address coverity warning in module_wrap.cc (Michael Dawson) [#52143](https://github.com/nodejs/node/pull/52143)
- \[[`04319228e0`](https://github.com/nodejs/node/commit/04319228e0)] - **src**: fix move after use reported by coverity (Michael Dawson) [#52141](https://github.com/nodejs/node/pull/52141)
- \[[`0eb2b727f6`](https://github.com/nodejs/node/commit/0eb2b727f6)] - **src**: return a number from process.constrainedMemory() constantly (Chengzhong Wu) [#52039](https://github.com/nodejs/node/pull/52039)
- \[[`bec9b5fccc`](https://github.com/nodejs/node/commit/bec9b5fccc)] - **src**: use dedicated routine to compile function for builtin CJS loader (Joyee Cheung) [#52016](https://github.com/nodejs/node/pull/52016)
- \[[`1f193165b9`](https://github.com/nodejs/node/commit/1f193165b9)] - **src**: fix reading empty string views in Blob\[De]serializer (Joyee Cheung) [#52000](https://github.com/nodejs/node/pull/52000)
- \[[`fb356b3305`](https://github.com/nodejs/node/commit/fb356b3305)] - **src**: refactor out FormatErrorMessage for error formatting (Joyee Cheung) [#51999](https://github.com/nodejs/node/pull/51999)
- \[[`1a8ae9d6c0`](https://github.com/nodejs/node/commit/1a8ae9d6c0)] - **src**: use callback-based array iteration in Blob (Joyee Cheung) [#51758](https://github.com/nodejs/node/pull/51758)
- \[[`5cd2ec8bd5`](https://github.com/nodejs/node/commit/5cd2ec8bd5)] - **src**: implement v8 array iteration using the new callback-based API (Joyee Cheung) [#51758](https://github.com/nodejs/node/pull/51758)
- \[[`89a26b451e`](https://github.com/nodejs/node/commit/89a26b451e)] - **src**: fix node_version.h (Joyee Cheung) [#50375](https://github.com/nodejs/node/pull/50375)
- \[[`c02de658a1`](https://github.com/nodejs/node/commit/c02de658a1)] - **stream**: make Duplex inherit destroy from Writable (Luigi Pinca) [#52318](https://github.com/nodejs/node/pull/52318)
- \[[`63391e749d`](https://github.com/nodejs/node/commit/63391e749d)] - **stream**: add `new` when constructing `ERR_MULTIPLE_CALLBACK` (haze) [#52110](https://github.com/nodejs/node/pull/52110)
- \[[`a9528e87b9`](https://github.com/nodejs/node/commit/a9528e87b9)] - **stream**: use internal addAbortListener (Chemi Atlow) [#52081](https://github.com/nodejs/node/pull/52081)
- \[[`ee4fa77624`](https://github.com/nodejs/node/commit/ee4fa77624)] - **test**: fix watch test with require not testing pid (Raz Luvaton) [#52353](https://github.com/nodejs/node/pull/52353)
- \[[`05cb16dc1a`](https://github.com/nodejs/node/commit/05cb16dc1a)] - **test**: simplify ASan build checks (Michaël Zasso) [#52430](https://github.com/nodejs/node/pull/52430)
- \[[`eb53121b77`](https://github.com/nodejs/node/commit/eb53121b77)] - **test**: fix Windows compiler warnings in overlapped-checker (Michaël Zasso) [#52405](https://github.com/nodejs/node/pull/52405)
- \[[`7dfa4750af`](https://github.com/nodejs/node/commit/7dfa4750af)] - **test**: add test for skip+todo combinations (Colin Ihrig) [#52204](https://github.com/nodejs/node/pull/52204)
- \[[`5905596719`](https://github.com/nodejs/node/commit/5905596719)] - **test**: fix incorrect test fixture (Colin Ihrig) [#52185](https://github.com/nodejs/node/pull/52185)
- \[[`bae14b7914`](https://github.com/nodejs/node/commit/bae14b7914)] - **test**: do not set concurrency on parallelized runs (Antoine du Hamel) [#52177](https://github.com/nodejs/node/pull/52177)
- \[[`0b676736a0`](https://github.com/nodejs/node/commit/0b676736a0)] - **test**: add missing cctest/test_path.cc (Yagiz Nizipli) [#52148](https://github.com/nodejs/node/pull/52148)
- \[[`c714cda9a7`](https://github.com/nodejs/node/commit/c714cda9a7)] - **test**: add `spawnSyncAndAssert` util (Antoine du Hamel) [#52132](https://github.com/nodejs/node/pull/52132)
- \[[`978d5a26c9`](https://github.com/nodejs/node/commit/978d5a26c9)] - **test**: reduce flakiness of test-runner-output.mjs (Colin Ihrig) [#52146](https://github.com/nodejs/node/pull/52146)
- \[[`afaf889775`](https://github.com/nodejs/node/commit/afaf889775)] - **test**: skip test for dynamically linked OpenSSL (Richard Lau) [#52542](https://github.com/nodejs/node/pull/52542)
- \[[`be75821a12`](https://github.com/nodejs/node/commit/be75821a12)] - **test**: add test for using `--print` with promises (Antoine du Hamel) [#52137](https://github.com/nodejs/node/pull/52137)
- \[[`4e109e5958`](https://github.com/nodejs/node/commit/4e109e5958)] - **test**: un-set test-emit-after-on-destroyed as flaky (Abdirahim Musse) [#51995](https://github.com/nodejs/node/pull/51995)
- \[[`3f8cc88009`](https://github.com/nodejs/node/commit/3f8cc88009)] - **test_runner**: fix clearing final timeout in own callback (Ben Richeson) [#52332](https://github.com/nodejs/node/pull/52332)
- \[[`52f8dcfccc`](https://github.com/nodejs/node/commit/52f8dcfccc)] - **test_runner**: make end of work check stricter (Colin Ihrig) [#52326](https://github.com/nodejs/node/pull/52326)
- \[[`433bd1b04d`](https://github.com/nodejs/node/commit/433bd1b04d)] - **test_runner**: fix recursive run (Moshe Atlow) [#52322](https://github.com/nodejs/node/pull/52322)
- \[[`e57992ffb2`](https://github.com/nodejs/node/commit/e57992ffb2)] - **test_runner**: hide new line when no error in spec reporter (Moshe Atlow) [#52297](https://github.com/nodejs/node/pull/52297)
- \[[`ac9e5e7527`](https://github.com/nodejs/node/commit/ac9e5e7527)] - **test_runner**: improve describe.only behavior (Moshe Atlow) [#52296](https://github.com/nodejs/node/pull/52296)
- \[[`2c024cd24d`](https://github.com/nodejs/node/commit/2c024cd24d)] - **test_runner**: disable highWatermark on TestsStream (Colin Ihrig) [#52287](https://github.com/nodejs/node/pull/52287)
- \[[`7c02486f1f`](https://github.com/nodejs/node/commit/7c02486f1f)] - **test_runner**: run afterEach hooks in correct order (Colin Ihrig) [#52239](https://github.com/nodejs/node/pull/52239)
- \[[`6af4049810`](https://github.com/nodejs/node/commit/6af4049810)] - **test_runner**: simplify test end time tracking (Colin Ihrig) [#52182](https://github.com/nodejs/node/pull/52182)
- \[[`878047be0b`](https://github.com/nodejs/node/commit/878047be0b)] - **test_runner**: simplify test start time tracking (Colin Ihrig) [#52182](https://github.com/nodejs/node/pull/52182)
- \[[`4648c83dbc`](https://github.com/nodejs/node/commit/4648c83dbc)] - **test_runner**: don't await the same promise for each test (Colin Ihrig) [#52185](https://github.com/nodejs/node/pull/52185)
- \[[`f9755f6f79`](https://github.com/nodejs/node/commit/f9755f6f79)] - **test_runner**: emit diagnostics when watch mode drains (Moshe Atlow) [#52130](https://github.com/nodejs/node/pull/52130)
- \[[`4ba9f45d99`](https://github.com/nodejs/node/commit/4ba9f45d99)] - **test_runner**: ignore todo flag when running suites (Colin Ihrig) [#52117](https://github.com/nodejs/node/pull/52117)
- \[[`6f4d6011ea`](https://github.com/nodejs/node/commit/6f4d6011ea)] - **test_runner**: skip each hooks for skipped tests (Colin Ihrig) [#52115](https://github.com/nodejs/node/pull/52115)
- \[[`05db979c01`](https://github.com/nodejs/node/commit/05db979c01)] - **test_runner**: run top level tests in a microtask (Colin Ihrig) [#52092](https://github.com/nodejs/node/pull/52092)
- \[[`97b2c5344d`](https://github.com/nodejs/node/commit/97b2c5344d)] - **test_runner**: remove redundant report call (Colin Ihrig) [#52089](https://github.com/nodejs/node/pull/52089)
- \[[`780d030bdf`](https://github.com/nodejs/node/commit/780d030bdf)] - **test_runner**: use internal addAbortListener (Chemi Atlow) [#52081](https://github.com/nodejs/node/pull/52081)
- \[[`814fa1ae74`](https://github.com/nodejs/node/commit/814fa1ae74)] - **test_runner**: use source maps when reporting coverage (Moshe Atlow) [#52060](https://github.com/nodejs/node/pull/52060)
- \[[`3c5764a0e2`](https://github.com/nodejs/node/commit/3c5764a0e2)] - **test_runner**: handle undefined test locations (Colin Ihrig) [#52036](https://github.com/nodejs/node/pull/52036)
- \[[`328642bbb9`](https://github.com/nodejs/node/commit/328642bbb9)] - **test_runner**: use paths for test locations (Colin Ihrig) [#52010](https://github.com/nodejs/node/pull/52010)
- \[[`6d625fe616`](https://github.com/nodejs/node/commit/6d625fe616)] - **test_runner**: support source mapped test locations (Colin Ihrig) [#52010](https://github.com/nodejs/node/pull/52010)
- \[[`592c6907bf`](https://github.com/nodejs/node/commit/592c6907bf)] - **test_runner**: avoid overwriting root start time (Colin Ihrig) [#52020](https://github.com/nodejs/node/pull/52020)
- \[[`29b231763e`](https://github.com/nodejs/node/commit/29b231763e)] - **test_runner**: abort unfinished tests on async error (Colin Ihrig) [#51996](https://github.com/nodejs/node/pull/51996)
- \[[`5d13419dbd`](https://github.com/nodejs/node/commit/5d13419dbd)] - **test_runner**: run before hook immediately if test started (Moshe Atlow) [#52003](https://github.com/nodejs/node/pull/52003)
- \[[`8451990668`](https://github.com/nodejs/node/commit/8451990668)] - **test_runner**: add support for null and date value output (Malthe Borch) [#51920](https://github.com/nodejs/node/pull/51920)
- \[[`423ad47e0f`](https://github.com/nodejs/node/commit/423ad47e0f)] - **tools**: change inactive limit to 12 months (Yagiz Nizipli) [#52425](https://github.com/nodejs/node/pull/52425)
- \[[`0d1e64f64c`](https://github.com/nodejs/node/commit/0d1e64f64c)] - **tools**: update stale bot messaging (Wes Todd) [#52423](https://github.com/nodejs/node/pull/52423)
- \[[`5bae73df90`](https://github.com/nodejs/node/commit/5bae73df90)] - **tools**: update lint-md-dependencies to rollup\@4.14.0 (Node.js GitHub Bot) [#52398](https://github.com/nodejs/node/pull/52398)
- \[[`468cb99ba4`](https://github.com/nodejs/node/commit/468cb99ba4)] - **tools**: update Ruff to v0.3.4 (Michaël Zasso) [#52302](https://github.com/nodejs/node/pull/52302)
- \[[`67b9dda003`](https://github.com/nodejs/node/commit/67b9dda003)] - **tools**: run test-ubsan on ubuntu-latest (Michaël Zasso) [#52375](https://github.com/nodejs/node/pull/52375)
- \[[`f1f32d89e0`](https://github.com/nodejs/node/commit/f1f32d89e0)] - **tools**: update lint-md-dependencies to rollup\@4.13.2 (Node.js GitHub Bot) [#52286](https://github.com/nodejs/node/pull/52286)
- \[[`d7aa8fc9da`](https://github.com/nodejs/node/commit/d7aa8fc9da)] - _**Revert**_ "**tools**: run `build-windows` workflow only on source changes" (Michaël Zasso) [#52320](https://github.com/nodejs/node/pull/52320)
- \[[`a3b1fc3f27`](https://github.com/nodejs/node/commit/a3b1fc3f27)] - **tools**: use Python 3.12 in GitHub Actions workflows (Michaël Zasso) [#52301](https://github.com/nodejs/node/pull/52301)
- \[[`021cf91208`](https://github.com/nodejs/node/commit/021cf91208)] - **tools**: allow local updates for llhttp (Paolo Insogna) [#52085](https://github.com/nodejs/node/pull/52085)
- \[[`4d8602046e`](https://github.com/nodejs/node/commit/4d8602046e)] - **tools**: install npm PowerShell scripts on Windows (Luke Karrys) [#52009](https://github.com/nodejs/node/pull/52009)
- \[[`081319d762`](https://github.com/nodejs/node/commit/081319d762)] - **tools**: update lint-md-dependencies to rollup\@4.13.0 (Node.js GitHub Bot) [#52122](https://github.com/nodejs/node/pull/52122)
- \[[`c43a944231`](https://github.com/nodejs/node/commit/c43a944231)] - **tools**: fix error reported by coverity in js2c.cc (Michael Dawson) [#52142](https://github.com/nodejs/node/pull/52142)
- \[[`f05b241f07`](https://github.com/nodejs/node/commit/f05b241f07)] - **tools**: sync ubsan workflow with asan (Michaël Zasso) [#52152](https://github.com/nodejs/node/pull/52152)
- \[[`a21b15a14e`](https://github.com/nodejs/node/commit/a21b15a14e)] - **tools**: update github_reporter to 1.7.0 (Node.js GitHub Bot) [#52121](https://github.com/nodejs/node/pull/52121)
- \[[`d60a871db2`](https://github.com/nodejs/node/commit/d60a871db2)] - **tools**: remove gyp-next .github folder (Marco Ippolito) [#52064](https://github.com/nodejs/node/pull/52064)
- \[[`6ad5353764`](https://github.com/nodejs/node/commit/6ad5353764)] - **tools**: update gyp-next to 0.16.2 (Node.js GitHub Bot) [#52062](https://github.com/nodejs/node/pull/52062)
- \[[`dab85bdc06`](https://github.com/nodejs/node/commit/dab85bdc06)] - **tools**: install manpage to share/man for FreeBSD (Po-Chuan Hsieh) [#51791](https://github.com/nodejs/node/pull/51791)
- \[[`cde37e7b63`](https://github.com/nodejs/node/commit/cde37e7b63)] - **tools**: automate gyp-next update (Marco Ippolito) [#52014](https://github.com/nodejs/node/pull/52014)
- \[[`925a464cb8`](https://github.com/nodejs/node/commit/925a464cb8)] - **url**: remove #context from URLSearchParams (Matt Cowley) [#51520](https://github.com/nodejs/node/pull/51520)
- \[[`893e2cf22b`](https://github.com/nodejs/node/commit/893e2cf22b)] - **watch**: fix some node argument not passed to watched process (Raz Luvaton) [#52358](https://github.com/nodejs/node/pull/52358)
- \[[`fec7e505fc`](https://github.com/nodejs/node/commit/fec7e505fc)] - **watch**: use internal addAbortListener (Chemi Atlow) [#52081](https://github.com/nodejs/node/pull/52081)
- \[[`4f68c7c1c9`](https://github.com/nodejs/node/commit/4f68c7c1c9)] - **watch**: mark as stable (Moshe Atlow) [#52074](https://github.com/nodejs/node/pull/52074)
- \[[`257f32296d`](https://github.com/nodejs/node/commit/257f32296d)] - **watch**: batch file restarts (Moshe Atlow) [#51992](https://github.com/nodejs/node/pull/51992)

Windows 32-bit Installer: https://nodejs.org/dist/v22.0.0/node-v22.0.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v22.0.0/node-v22.0.0-x64.msi \
Windows ARM 64-bit Installer: https://nodejs.org/dist/v22.0.0/node-v22.0.0-arm64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v22.0.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v22.0.0/win-x64/node.exe \
Windows ARM 64-bit Binary: https://nodejs.org/dist/v22.0.0/win-arm64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v22.0.0/node-v22.0.0.pkg \
macOS Apple Silicon 64-bit Binary: https://nodejs.org/dist/v22.0.0/node-v22.0.0-darwin-arm64.tar.gz \
macOS Intel 64-bit Binary: https://nodejs.org/dist/v22.0.0/node-v22.0.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v22.0.0/node-v22.0.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v22.0.0/node-v22.0.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v22.0.0/node-v22.0.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v22.0.0/node-v22.0.0-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v22.0.0/node-v22.0.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v22.0.0/node-v22.0.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v22.0.0/node-v22.0.0.tar.gz \
Other release files: https://nodejs.org/dist/v22.0.0/ \
Documentation: https://nodejs.org/docs/v22.0.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

d7743fddf5e867124f555a91f6c729104fd5c75d9382f10286c3fbb3fd12ef07  node-v22.0.0-aix-ppc64.tar.gz
ffb6e5a0c7e3720e242765ac68025ccd8ade5a35fb9e48d23b99df994f068fe1  node-v22.0.0-arm64.msi
ea96d349cfaa67aa87ceeaa3e5b52c9167f7ac302fd8d1ff162d0785e9dc0785  node-v22.0.0-darwin-arm64.tar.gz
1e4c5dc7144475f52fa7c86819a756126eb981e53469b8484b6dc30f9f08f26f  node-v22.0.0-darwin-arm64.tar.xz
422a3887ff5418f0a4552d89cf99346ab8ab51bb5d384660baa88b8444d2c111  node-v22.0.0-darwin-x64.tar.gz
b8fc7ad15e8f8c3780eb79677400ad0a3f7b7d2aa076856b2719a995157f18d0  node-v22.0.0-darwin-x64.tar.xz
d33e48f25ba714a69c920c584710892f4a77dce90c9bba23a228c5c1136f7a53  node-v22.0.0-headers.tar.gz
124ca312ebe7249454cb62d13d56fba2979db6abb56544e85b4d35476898a76f  node-v22.0.0-headers.tar.xz
1d3547226be7e59aceee5c7d01a9f8fc18de67e015c5a15d8cf385b6e02d062b  node-v22.0.0-linux-arm64.tar.gz
83711d29cbe46375bdffab5419f3d831892e24294169272f6c39edc364556241  node-v22.0.0-linux-arm64.tar.xz
d21db7f57c5ce182aec2b9a83e3295abc37e1731abb73995d3d573e04409977c  node-v22.0.0-linux-armv7l.tar.gz
a1f8dfb8fffef9822ec874732b48e6423d52b957ec15f0e758e057c1fc33b4ad  node-v22.0.0-linux-armv7l.tar.xz
3b098dccf56d446bbb80845d35bbecbdb74612860d1690f3a216387c99c9d620  node-v22.0.0-linux-ppc64le.tar.gz
2b3fb8707a79243bfb3131312b86716ddc3855bce21bb168095b6b916798e5e9  node-v22.0.0-linux-ppc64le.tar.xz
7ec5fdad0c819eea17900eb43c1de948d620a783b1ac94062caa438778a92b30  node-v22.0.0-linux-s390x.tar.gz
89a8efeeb9f94ce9ea251b8109e079c14919f4c0dc2cbc9f545ec47ef0886737  node-v22.0.0-linux-s390x.tar.xz
74bb0f3a80307c529421c3ed84517b8f543867709f41e53cd73df99e6442af4d  node-v22.0.0-linux-x64.tar.gz
9122e50f2642afd5f6078cafd1f52ede60fc464284384f05c18a04d13d07ae5a  node-v22.0.0-linux-x64.tar.xz
467a0aee9a31a1ebe1a6934eb0b71c801ef9a60ffb4e9aacf2ef1e01f382b3fe  node-v22.0.0-win-arm64.7z
376121cf46bd3c025c5e67adae190aff5e25d33a0b58fbc0d8652d73350b38f0  node-v22.0.0-win-arm64.zip
df03e48bd2f98e7e59da264eb27d2cf76b11408bb6fbf2250c1c2226d19c94e8  node-v22.0.0-win-x64.7z
32d639b47d4c0a651ff8f8d7d41a454168a3d4045be37985f9a810cf8cef6174  node-v22.0.0-win-x64.zip
22e77736e0c0e38e59f9d0122a633fbeb82c5973b72f1bfcc3c52be0b66303c7  node-v22.0.0-win-x86.7z
e0134f5017154a337672c7fbcd154e2b2c774b431091185601920d63d0c4b7d0  node-v22.0.0-win-x86.zip
b3f2df95a73c7ca378906dfd7d18e6112ff38965fa31a1e113a4b201db106fcf  node-v22.0.0-x64.msi
8fa3c77f65445010351b9467736d9b42fc84c12ad4cc218bf340c2057af8d553  node-v22.0.0-x86.msi
2a7aa14f78d7b764d1552898bf1181da34d3ce40696742c137b8c3ab4079d078  node-v22.0.0.pkg
47cd57486564a8cb4728ec01f1d98b510c6547e19e96a7f7583cf882d803d0c6  node-v22.0.0.tar.gz
22e28fbff31f69073b8024cb42745e5105f84041f3475b260b97d5a141039d1a  node-v22.0.0.tar.xz
cfcdd81430a39b2bdab0f99017ba5c8c6b8ad6b30e175a7e060dc5b7cb54e797  win-arm64/node.exe
974ffe43afcc27c334e44b92628df5942570f6490a3436c136201ca063e90cec  win-arm64/node.lib
e9f4e1104aaf9b57712e0ef540e74703f3afc4cce7d50c29156ea63f832b049c  win-arm64/node_pdb.7z
f761c807a65f6c15d592a6750b804103c963d8cc9607a0cf17f827434b2550f0  win-arm64/node_pdb.zip
db174445b3d28314772bc3918e39d8543cfbd18d542cf2a662f431f5cdc259ea  win-x64/node.exe
8ded03c4ad28e5a64be37cd4c4278094c96a512569a8fa68a20bccbcc960339f  win-x64/node.lib
2452af2e8bd356bd2a1cf596835cfd835f750ba1f52826e62bd015dd17d35703  win-x64/node_pdb.7z
c4b091dae6a217b4a871a9c8240f0c82330010c4ff35cf3166044356709213b6  win-x64/node_pdb.zip
f3f41409513928f3b0d11f799aa7e44bcb7bec74c0de53ebdf108c3c5c40c016  win-x86/node.exe
59393214b7ce16fd0017502f6feff62d651391f9ad02dc68c955ff13b99c5fb4  win-x86/node.lib
87f527249d447b8a79373d5d7cd8045a7e141049d80c0b2399bc6c62aaa20c57  win-x86/node_pdb.7z
4ec1e6bb26f87add5e3741a118957ff07bf91791a8ddf1f426de15e79e835ff0  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQGzBAEBCAAdFiEEiQwI24V5Fi/uDfnbi+q0389VXvQFAmYpTd0ACgkQi+q0389V
XvQxJQv/YNg/SFottus7N3W7IWJpf1WYZesLhNBtursfLpiSw14QlCa3fhBrUx+W
fTpHrDJQbLyyVqo9U1tQ4D3g2+rYBmOjeUQnmg8zfjvu6PHfWtwLGNc7pQelKYgN
mrMl709lCWqDfELtdyiXcTh4G+rAfLCb2u/ykxbDWncHUzeNuEyVjUVJVwDhTIgZ
QYQ6IfWB2TusVTBTEWEP2YouImupVyoQicrzb1wHHFHKaT16GVKBPG1phPU3O1hM
d2OeFCVjxqMJC7oBAwS+s5PHN8+Er9VUG1nfTBIyzSvj3z8zYmCqXCxlMOkRBYig
ePfh8bRXV8qeQd4Ps2vTxp5XmO8RpSQk2BNPULg3hZpZF3xtOEnLv4tT7WXMVYrI
o7HdBqv20/bEpr1oOQwzS/OeLZ3tn/cj7WjgLLRPVvEYE741D7LROQKOVw5RjBAQ
/4rbN1Pn0TPze34FVbPhvvWzmPrP2d0CyeAjsk8Fu0TOtuhtcrlQIrm0j2eUQ3To
puN9ZCv2
=oENf
-----END PGP SIGNATURE-----
```
