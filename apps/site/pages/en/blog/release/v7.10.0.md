---
date: '2017-05-03T13:10:38.405Z'
category: release
title: Node v7.10.0 (Current)
layout: blog-post
author: <PERSON>
---

### Notable Changes

- **crypto**: add randomFill and randomFillSync (<PERSON>) [#10209](https://github.com/nodejs/node/pull/10209)
- **meta**: Added new collaborators
  - add <PERSON><PERSON><PERSON><PERSON><PERSON> to collaborators (<PERSON>) [#12538](https://github.com/nodejs/node/pull/12538)
  - add <PERSON><PERSON><PERSON>1993 to collaborators (<PERSON>) [#12435](https://github.com/nodejs/node/pull/12435)
  - add jkrems to collaborators (<PERSON>) [#12427](https://github.com/nodejs/node/pull/12427)
  - add <PERSON><PERSON><PERSON> to collaborators (AnnaMag) [#12414](https://github.com/nodejs/node/pull/12414)
- **process**: fix crash when Promise rejection is a Symbol (<PERSON>) [#11640](https://github.com/nodejs/node/pull/11640)
- **url**: make WHATWG URL more spec compliant (<PERSON>) [#12507](https://github.com/nodejs/node/pull/12507)
- **v8**:
  - fix stack overflow in recursive method (Ben Noordhuis) [#12460](https://github.com/nodejs/node/pull/12460)
  - fix build errors with g++ 7 (Ben Noordhuis) [#12392](https://github.com/nodejs/node/pull/12392)

### Commits

- [[`224fd3af97`](https://github.com/nodejs/node/commit/224fd3af97)] - **benchmark**: terminate child process on Windows (Rich Trott) [#12658](https://github.com/nodejs/node/pull/12658)
- [[`373e9f08af`](https://github.com/nodejs/node/commit/373e9f08af)] - **benchmark**: add benchmark for v8.getHeap\*Statistics (James M Snell) [#12681](https://github.com/nodejs/node/pull/12681)
- [[`7d87edc1ba`](https://github.com/nodejs/node/commit/7d87edc1ba)] - **benchmark**: add benchmark for string concatenations (Vse Mozhet Byt) [#12455](https://github.com/nodejs/node/pull/12455)
- [[`08ba9d437c`](https://github.com/nodejs/node/commit/08ba9d437c)] - **benchmark**: fix CLI arguments check in common.js (Vse Mozhet Byt) [#12429](https://github.com/nodejs/node/pull/12429)
- [[`440f4d4eef`](https://github.com/nodejs/node/commit/440f4d4eef)] - **_Revert_** "**benchmark**: fix CLI arguments check in common.js" (James M Snell) [#12474](https://github.com/nodejs/node/pull/12474)
- [[`b7aeed7a7e`](https://github.com/nodejs/node/commit/b7aeed7a7e)] - **benchmark**: improve cli error message (Brian White) [#12421](https://github.com/nodejs/node/pull/12421)
- [[`917534d541`](https://github.com/nodejs/node/commit/917534d541)] - **benchmark**: fix CLI arguments check in common.js (Vse Mozhet Byt) [#12429](https://github.com/nodejs/node/pull/12429)
- [[`f316b50e8d`](https://github.com/nodejs/node/commit/f316b50e8d)] - **benchmark**: replace more \[\].join() with ''.repeat() (Vse Mozhet Byt) [#12317](https://github.com/nodejs/node/pull/12317)
- [[`d58fa7873f`](https://github.com/nodejs/node/commit/d58fa7873f)] - **benchmark,windows**: TCP.readStart() meaningful only after completion (Refael Ackermann) [#12258](https://github.com/nodejs/node/pull/12258)
- [[`e4b2f61fb5`](https://github.com/nodejs/node/commit/e4b2f61fb5)] - **buffer**: use slightly faster NaN check (Brian White) [#12286](https://github.com/nodejs/node/pull/12286)
- [[`ebeb6c0a26`](https://github.com/nodejs/node/commit/ebeb6c0a26)] - **buffer**: optimize write() (Brian White) [#12361](https://github.com/nodejs/node/pull/12361)
- [[`0c0241ff73`](https://github.com/nodejs/node/commit/0c0241ff73)] - **buffer,util**: refactor for performance (Rich Trott) [#12153](https://github.com/nodejs/node/pull/12153)
- [[`caf6506f9f`](https://github.com/nodejs/node/commit/caf6506f9f)] - **build**: add target for checking for perm deopts (Brian White) [#12456](https://github.com/nodejs/node/pull/12456)
- [[`212475b451`](https://github.com/nodejs/node/commit/212475b451)] - **build**: use do_not_edit variable where possible (Ruslan Bekenev) [#12610](https://github.com/nodejs/node/pull/12610)
- [[`78ac637fe2`](https://github.com/nodejs/node/commit/78ac637fe2)] - **build**: fix case in lib names (Refael Ackermann) [#12522](https://github.com/nodejs/node/pull/12522)
- [[`e6be4b951a`](https://github.com/nodejs/node/commit/e6be4b951a)] - **build**: make linter targets silent (Sakthipriyan Vairamani (thefourtheye)) [#12423](https://github.com/nodejs/node/pull/12423)
- [[`4d9e6718f8`](https://github.com/nodejs/node/commit/4d9e6718f8)] - **build**: run cpplint even if jslint failed (Ruslan Bekenev) [#12276](https://github.com/nodejs/node/pull/12276)
- [[`9662ca1a03`](https://github.com/nodejs/node/commit/9662ca1a03)] - **build**: enable cctest to use generated objects (Daniel Bevenius) [#11956](https://github.com/nodejs/node/pull/11956)
- [[`42e940c5e9`](https://github.com/nodejs/node/commit/42e940c5e9)] - **build,win**: limit maxcpucount to 2 for MSBuild (João Reis) [#12184](https://github.com/nodejs/node/pull/12184)
- [[`933b6b57d6`](https://github.com/nodejs/node/commit/933b6b57d6)] - **cluster**: fix permanent deoptimizations (Brian White) [#12456](https://github.com/nodejs/node/pull/12456)
- [[`db585c9b4d`](https://github.com/nodejs/node/commit/db585c9b4d)] - **crypto**: update root certificates (Ben Noordhuis) [#12402](https://github.com/nodejs/node/pull/12402)
- [[`8ac8e50a58`](https://github.com/nodejs/node/commit/8ac8e50a58)] - **crypto**: make LazyTransform compabile with Streams1 (Matteo Collina) [#12380](https://github.com/nodejs/node/pull/12380)
- [[`75f4329e01`](https://github.com/nodejs/node/commit/75f4329e01)] - **(SEMVER-MINOR)** **crypto**: add randomFill and randomFillSync (Evan Lucas) [#10209](https://github.com/nodejs/node/pull/10209)
- [[`d154aafe18`](https://github.com/nodejs/node/commit/d154aafe18)] - **deps**: remove \*\*/node_modules/form-data/README.md (Jeremiah Senkpiel) [#12643](https://github.com/nodejs/node/pull/12643)
- [[`e8595c505b`](https://github.com/nodejs/node/commit/e8595c505b)] - **deps**: cherry-pick 79aee39 from upstream v8 (Ben Noordhuis) [#12412](https://github.com/nodejs/node/pull/12412)
- [[`810f9215e5`](https://github.com/nodejs/node/commit/810f9215e5)] - **deps,win**: increase msvs_shard in gyp for V8 (João Reis) [#12184](https://github.com/nodejs/node/pull/12184)
- [[`c2c62874d4`](https://github.com/nodejs/node/commit/c2c62874d4)] - **doc**: fix formatting of TOC (Refael Ackermann) [#12731](https://github.com/nodejs/node/pull/12731)
- [[`f60a2e9527`](https://github.com/nodejs/node/commit/f60a2e9527)] - **doc**: fixup the collaborators list (Alexey Orlenko) [#12750](https://github.com/nodejs/node/pull/12750)
- [[`360efe48bc`](https://github.com/nodejs/node/commit/360efe48bc)] - **doc**: fix examples in repl.md (Vse Mozhet Byt) [#12684](https://github.com/nodejs/node/pull/12684)
- [[`395380a136`](https://github.com/nodejs/node/commit/395380a136)] - **doc**: add Added-in metadata for WHATWG URL (Timothy Gu) [#12683](https://github.com/nodejs/node/pull/12683)
- [[`fc96d1a573`](https://github.com/nodejs/node/commit/fc96d1a573)] - **doc**: document url.domainTo\* methods separately (Timothy Gu) [#12683](https://github.com/nodejs/node/pull/12683)
- [[`45facc8822`](https://github.com/nodejs/node/commit/45facc8822)] - **doc**: fix an unclear wording in readline.md (Vse Mozhet Byt) [#12605](https://github.com/nodejs/node/pull/12605)
- [[`1316c77b79`](https://github.com/nodejs/node/commit/1316c77b79)] - **doc**: improve randomfill and fix broken link (Sakthipriyan Vairamani (thefourtheye)) [#12541](https://github.com/nodejs/node/pull/12541)
- [[`313b205834`](https://github.com/nodejs/node/commit/313b205834)] - **doc**: prepare js code for eslint-plugin-markdown (Vse Mozhet Byt) [#12563](https://github.com/nodejs/node/pull/12563)
- [[`b52f77df43`](https://github.com/nodejs/node/commit/b52f77df43)] - **doc**: fix typo in doc/api/process.md (morrme) [#12612](https://github.com/nodejs/node/pull/12612)
- [[`47b39928d5`](https://github.com/nodejs/node/commit/47b39928d5)] - **doc**: make commit guidelines easier to reference (Benjamin Fleischer) [#11732](https://github.com/nodejs/node/pull/11732)
- [[`4276c213f2`](https://github.com/nodejs/node/commit/4276c213f2)] - **doc**: add suggestion to use --3way (Michael Dawson) [#12510](https://github.com/nodejs/node/pull/12510)
- [[`3703fc6bbe`](https://github.com/nodejs/node/commit/3703fc6bbe)] - **doc**: update link to Code of Conduct (Alex Autem) [#12552](https://github.com/nodejs/node/pull/12552)
- [[`434873d24b`](https://github.com/nodejs/node/commit/434873d24b)] - **doc**: fix typo in fs.watch() description (Ivo von Putzer Reibegg) [#12550](https://github.com/nodejs/node/pull/12550)
- [[`eb78722922`](https://github.com/nodejs/node/commit/eb78722922)] - **doc**: add lucamaraschi to collaborators (Luca Maraschi) [#12538](https://github.com/nodejs/node/pull/12538)
- [[`9250f02d12`](https://github.com/nodejs/node/commit/9250f02d12)] - **doc**: clarify the callback arguments of dns.resolve (Roman Reiss) [#9532](https://github.com/nodejs/node/pull/9532)
- [[`38278db9c7`](https://github.com/nodejs/node/commit/38278db9c7)] - **doc**: unify spaces in a querystring.md code example (Vse Mozhet Byt) [#12465](https://github.com/nodejs/node/pull/12465)
- [[`3fc25dce82`](https://github.com/nodejs/node/commit/3fc25dce82)] - **doc**: run tests before landing changes (Rich Trott) [#12416](https://github.com/nodejs/node/pull/12416)
- [[`af0067cc5e`](https://github.com/nodejs/node/commit/af0067cc5e)] - **doc**: avoid colloquialism (Rich Trott) [#12417](https://github.com/nodejs/node/pull/12417)
- [[`d0ba631efa`](https://github.com/nodejs/node/commit/d0ba631efa)] - **doc**: fix encoding string in buffer example (MapleUncle) [#12482](https://github.com/nodejs/node/pull/12482)
- [[`3d8878c592`](https://github.com/nodejs/node/commit/3d8878c592)] - **doc**: correct git fix whitespace command (Mateusz Konieczny) [#12445](https://github.com/nodejs/node/pull/12445)
- [[`077187e9a8`](https://github.com/nodejs/node/commit/077187e9a8)] - **doc**: s/origin/upstream/ collaborator guide (Anna Henningsen) [#12436](https://github.com/nodejs/node/pull/12436)
- [[`5a2d358f2e`](https://github.com/nodejs/node/commit/5a2d358f2e)] - **doc**: remove inspector experimental warning (cjihrig) [#12408](https://github.com/nodejs/node/pull/12408)
- [[`320e72b32d`](https://github.com/nodejs/node/commit/320e72b32d)] - **doc**: add missing ) in CONTRIBUTING.md (Mateusz Konieczny) [#12444](https://github.com/nodejs/node/pull/12444)
- [[`4570d9853c`](https://github.com/nodejs/node/commit/4570d9853c)] - **doc**: add guide for backporting prs (Evan Lucas) [#11099](https://github.com/nodejs/node/pull/11099)
- [[`d7d1e923ad`](https://github.com/nodejs/node/commit/d7d1e923ad)] - **doc**: update link for landing PRs (Rich Trott) [#12415](https://github.com/nodejs/node/pull/12415)
- [[`4ce58bd56a`](https://github.com/nodejs/node/commit/4ce58bd56a)] - **doc**: add DavidCai1993 to collaborators (David Cai) [#12435](https://github.com/nodejs/node/pull/12435)
- [[`984232cc2a`](https://github.com/nodejs/node/commit/984232cc2a)] - **doc**: fix typo in streams.md (John Paul Bamberg) [#12428](https://github.com/nodejs/node/pull/12428)
- [[`060b63e91f`](https://github.com/nodejs/node/commit/060b63e91f)] - **doc**: add jkrems to collaborators (Jan Krems) [#12427](https://github.com/nodejs/node/pull/12427)
- [[`6a45be2fa6`](https://github.com/nodejs/node/commit/6a45be2fa6)] - **doc**: path functions ignore trailing slashes (Tobias Nießen) [#12181](https://github.com/nodejs/node/pull/12181)
- [[`92e239d001`](https://github.com/nodejs/node/commit/92e239d001)] - **doc**: add info about serializable types (Shubheksha Jalan) [#12313](https://github.com/nodejs/node/pull/12313)
- [[`5c57feaa27`](https://github.com/nodejs/node/commit/5c57feaa27)] - **doc**: fix formatting in onboarding-extras (Rich Trott) [#12350](https://github.com/nodejs/node/pull/12350)
- [[`e2d66ab809`](https://github.com/nodejs/node/commit/e2d66ab809)] - **doc**: response.write ignores body in some cases (Ruslan Bekenev) [#12314](https://github.com/nodejs/node/pull/12314)
- [[`d1e1832398`](https://github.com/nodejs/node/commit/d1e1832398)] - **doc**: add AnnaMag to collaborators (AnnaMag) [#12414](https://github.com/nodejs/node/pull/12414)
- [[`f3db9a6437`](https://github.com/nodejs/node/commit/f3db9a6437)] - **doc**: limit lines to 80 cols in internal README (Evan Lucas) [#12358](https://github.com/nodejs/node/pull/12358)
- [[`ed130168ed`](https://github.com/nodejs/node/commit/ed130168ed)] - **doc**: add single arg scenario for util.format (Tarun Batra) [#12374](https://github.com/nodejs/node/pull/12374)
- [[`aa511053be`](https://github.com/nodejs/node/commit/aa511053be)] - **doc**: add missing changelog entry for fs.readdir() (Shubheksha Jalan) [#12312](https://github.com/nodejs/node/pull/12312)
- [[`43a39c6786`](https://github.com/nodejs/node/commit/43a39c6786)] - **doc**: modernize and fix code examples in path.md (Vse Mozhet Byt) [#12296](https://github.com/nodejs/node/pull/12296)
- [[`71b39aefc1`](https://github.com/nodejs/node/commit/71b39aefc1)] - **doc**: update os.uptime() and process.uptime() info (Vse Mozhet Byt) [#12294](https://github.com/nodejs/node/pull/12294)
- [[`089a96f337`](https://github.com/nodejs/node/commit/089a96f337)] - **doc**: add link on logo to README (Roman Reiss) [#12307](https://github.com/nodejs/node/pull/12307)
- [[`75f0855f4c`](https://github.com/nodejs/node/commit/75f0855f4c)] - **doc**: c++ unit test guide lines (Daniel Bevenius) [#11956](https://github.com/nodejs/node/pull/11956)
- [[`b1d3f59c59`](https://github.com/nodejs/node/commit/b1d3f59c59)] - **doc**: fix stylistic issues in api/net.md (Alexey Orlenko) [#11786](https://github.com/nodejs/node/pull/11786)
- [[`e48e00b03a`](https://github.com/nodejs/node/commit/e48e00b03a)] - **doc**: document URLSearchParams constructor (Timothy Gu) [#12507](https://github.com/nodejs/node/pull/12507)
- [[`84484b7063`](https://github.com/nodejs/node/commit/84484b7063)] - **fs**: fix permanent deoptimizations (Brian White) [#12456](https://github.com/nodejs/node/pull/12456)
- [[`675244af8e`](https://github.com/nodejs/node/commit/675244af8e)] - **gyp**: inherit parent for `*.host` (Johan Bergström) [#6173](https://github.com/nodejs/node/pull/6173)
- [[`cbdf9a90d1`](https://github.com/nodejs/node/commit/cbdf9a90d1)] - **lib**: fix typo in comments in module.js (WORMSS) [#12528](https://github.com/nodejs/node/pull/12528)
- [[`51eafe8f3c`](https://github.com/nodejs/node/commit/51eafe8f3c)] - **meta**: update authors list (Aashil Patel) [#11533](https://github.com/nodejs/node/pull/11533)
- [[`2e3813f730`](https://github.com/nodejs/node/commit/2e3813f730)] - **meta**: move the Code of Conduct to TSC repository (James M Snell) [#12147](https://github.com/nodejs/node/pull/12147)
- [[`4a082889bd`](https://github.com/nodejs/node/commit/4a082889bd)] - **net**: fix permanent deoptimizations (Brian White) [#12456](https://github.com/nodejs/node/pull/12456)
- [[`9db4f19283`](https://github.com/nodejs/node/commit/9db4f19283)] - **net**: require 'dns' only once (Ben Noordhuis) [#12342](https://github.com/nodejs/node/pull/12342)
- [[`94385c6d70`](https://github.com/nodejs/node/commit/94385c6d70)] - **net**: don't normalize twice in Socket#connect() (Ben Noordhuis) [#12342](https://github.com/nodejs/node/pull/12342)
- [[`0e40e6d3e9`](https://github.com/nodejs/node/commit/0e40e6d3e9)] - **net**: don't concatenate strings in debug logging (Ben Noordhuis) [#12342](https://github.com/nodejs/node/pull/12342)
- [[`d0b1be13f9`](https://github.com/nodejs/node/commit/d0b1be13f9)] - **net**: remove unnecessary process.nextTick() (Ben Noordhuis) [#12342](https://github.com/nodejs/node/pull/12342)
- [[`dcc9e1a7d4`](https://github.com/nodejs/node/commit/dcc9e1a7d4)] - **net**: don't create unnecessary closure (Ben Noordhuis) [#12342](https://github.com/nodejs/node/pull/12342)
- [[`e09202b78b`](https://github.com/nodejs/node/commit/e09202b78b)] - **net**: don't create unnecessary closure (Ben Noordhuis) [#12342](https://github.com/nodejs/node/pull/12342)
- [[`8ac387be3e`](https://github.com/nodejs/node/commit/8ac387be3e)] - **net**: refactor onSlaveClose in Server.close (Claudio Rodriguez) [#12334](https://github.com/nodejs/node/pull/12334)
- [[`6998e8026b`](https://github.com/nodejs/node/commit/6998e8026b)] - **os,vm**: fix segfaults and CHECK failure (Tobias Nießen) [#12371](https://github.com/nodejs/node/pull/12371)
- [[`b573f77b28`](https://github.com/nodejs/node/commit/b573f77b28)] - **process**: fix permanent deoptimizations (Brian White) [#12456](https://github.com/nodejs/node/pull/12456)
- [[`cd54208463`](https://github.com/nodejs/node/commit/cd54208463)] - **process**: cast promise rejection reason to string (Cameron Little) [#11640](https://github.com/nodejs/node/pull/11640)
- [[`0cc37c71a1`](https://github.com/nodejs/node/commit/0cc37c71a1)] - **querystring**: move isHexTable to internal (Timothy Gu) [#12507](https://github.com/nodejs/node/pull/12507)
- [[`1ac331b29f`](https://github.com/nodejs/node/commit/1ac331b29f)] - **readline**: fix permanent deoptimizations (Brian White) [#12456](https://github.com/nodejs/node/pull/12456)
- [[`b09bf51cdf`](https://github.com/nodejs/node/commit/b09bf51cdf)] - **repl**: support hidden history file on Windows (Bartosz Sosnowski) [#12207](https://github.com/nodejs/node/pull/12207)
- [[`da01ff7507`](https://github.com/nodejs/node/commit/da01ff7507)] - **src**: remove invalid comment (cjihrig) [#12645](https://github.com/nodejs/node/pull/12645)
- [[`744ed9477b`](https://github.com/nodejs/node/commit/744ed9477b)] - **src**: expose V8's IsNativeError() in util bindings (cjihrig) [#12546](https://github.com/nodejs/node/pull/12546)
- [[`cfd91446a1`](https://github.com/nodejs/node/commit/cfd91446a1)] - **src**: remove extraneous dot (Myles Borins) [#12626](https://github.com/nodejs/node/pull/12626)
- [[`520f876711`](https://github.com/nodejs/node/commit/520f876711)] - **src**: add fcntl.h include to node.cc (Bartosz Sosnowski) [#12540](https://github.com/nodejs/node/pull/12540)
- [[`08951a1307`](https://github.com/nodejs/node/commit/08951a1307)] - **src**: replace IsConstructCalls with lambda (Daniel Bevenius) [#12533](https://github.com/nodejs/node/pull/12533)
- [[`184941ef0b`](https://github.com/nodejs/node/commit/184941ef0b)] - **src**: remove TODO about uv errno removal (Daniel Bevenius) [#12536](https://github.com/nodejs/node/pull/12536)
- [[`94ddab091b`](https://github.com/nodejs/node/commit/94ddab091b)] - **src**: guard default_inspector_port (Daniel Bevenius) [#12303](https://github.com/nodejs/node/pull/12303)
- [[`513fc62267`](https://github.com/nodejs/node/commit/513fc62267)] - **src**: clean up WHATWG WG parser (Timothy Gu) [#12507](https://github.com/nodejs/node/pull/12507)
- [[`078316c630`](https://github.com/nodejs/node/commit/078316c630)] - **src**: WHATWG URL C++ parser cleanup (Timothy Gu) [#12507](https://github.com/nodejs/node/pull/12507)
- [[`72a3cacc95`](https://github.com/nodejs/node/commit/72a3cacc95)] - **src**: remove explicit UTF-8 validity check in url (Timothy Gu) [#12507](https://github.com/nodejs/node/pull/12507)
- [[`f5a702e763`](https://github.com/nodejs/node/commit/f5a702e763)] - **stream**: Fixes missing 'unpipe' event (Christopher Luke) [#11876](https://github.com/nodejs/node/pull/11876)
- [[`7d0adc6d26`](https://github.com/nodejs/node/commit/7d0adc6d26)] - **stream**: fix permanent deoptimizations (Brian White) [#12456](https://github.com/nodejs/node/pull/12456)
- [[`592db37e2f`](https://github.com/nodejs/node/commit/592db37e2f)] - **test**: move test to sequential for reliability (Rich Trott) [#12704](https://github.com/nodejs/node/pull/12704)
- [[`7af2e7940c`](https://github.com/nodejs/node/commit/7af2e7940c)] - **test**: fix permanent deoptimizations (Brian White) [#12456](https://github.com/nodejs/node/pull/12456)
- [[`9da719ab32`](https://github.com/nodejs/node/commit/9da719ab32)] - **test**: fix test filenames (Brian White) [#12456](https://github.com/nodejs/node/pull/12456)
- [[`d78adccc08`](https://github.com/nodejs/node/commit/d78adccc08)] - **test**: support multiple warnings in checkWarning (Cameron Little) [#11640](https://github.com/nodejs/node/pull/11640)
- [[`819c131f58`](https://github.com/nodejs/node/commit/819c131f58)] - **test**: improve test-tcp-wrap-listen (alohaglenn) [#12599](https://github.com/nodejs/node/pull/12599)
- [[`79dff99428`](https://github.com/nodejs/node/commit/79dff99428)] - **test**: remove eslint comments from test-util.js (cjihrig) [#12669](https://github.com/nodejs/node/pull/12669)
- [[`dd1dced4c1`](https://github.com/nodejs/node/commit/dd1dced4c1)] - **test**: remove eslint comments (cjihrig) [#12669](https://github.com/nodejs/node/pull/12669)
- [[`3e9e6afd8a`](https://github.com/nodejs/node/commit/3e9e6afd8a)] - **test**: use common.mustCall in test-https-strict (weewey) [#12668](https://github.com/nodejs/node/pull/12668)
- [[`75e053be39`](https://github.com/nodejs/node/commit/75e053be39)] - **test**: cleanup test-util-inherits.js (RobotMermaid) [#12602](https://github.com/nodejs/node/pull/12602)
- [[`745dea994e`](https://github.com/nodejs/node/commit/745dea994e)] - **test**: use common.js to check platform (Ruslan Bekenev) [#12629](https://github.com/nodejs/node/pull/12629)
- [[`8e6d4402a0`](https://github.com/nodejs/node/commit/8e6d4402a0)] - **test**: improve test-process-kill-pid (alohaglenn) [#12588](https://github.com/nodejs/node/pull/12588)
- [[`660e58c7eb`](https://github.com/nodejs/node/commit/660e58c7eb)] - **test**: improved type checking with regex (coreybeaumont) [#12591](https://github.com/nodejs/node/pull/12591)
- [[`e36a256c6b`](https://github.com/nodejs/node/commit/e36a256c6b)] - **test**: cleanup test-fs-watch.js (RobotMermaid) [#12595](https://github.com/nodejs/node/pull/12595)
- [[`d15b1c4446`](https://github.com/nodejs/node/commit/d15b1c4446)] - **test**: add mustCall in test-timers-clearImmediate (Zahidul Islam) [#12598](https://github.com/nodejs/node/pull/12598)
- [[`989d344ba9`](https://github.com/nodejs/node/commit/989d344ba9)] - **test**: use block scoped variable names (Neehar Venugopal) [#12544](https://github.com/nodejs/node/pull/12544)
- [[`b82e0769fe`](https://github.com/nodejs/node/commit/b82e0769fe)] - **test**: dynamic port in cluster eaddrinuse (Sebastian Plesciuc) [#12547](https://github.com/nodejs/node/pull/12547)
- [[`8ae5afe2d2`](https://github.com/nodejs/node/commit/8ae5afe2d2)] - **test**: dynamic port in cluster ipc throw (Sebastian Plesciuc) [#12571](https://github.com/nodejs/node/pull/12571)
- [[`80ceb04644`](https://github.com/nodejs/node/commit/80ceb04644)] - **test**: replace assertion error check with regex (thelady) [#12603](https://github.com/nodejs/node/pull/12603)
- [[`2021ea1c12`](https://github.com/nodejs/node/commit/2021ea1c12)] - **test**: refactored context type err message to regex (Muhsin Abdul-Musawwir) [#12596](https://github.com/nodejs/node/pull/12596)
- [[`1a4bf431fc`](https://github.com/nodejs/node/commit/1a4bf431fc)] - **test**: improve test-process-chdir (vperezma) [#12589](https://github.com/nodejs/node/pull/12589)
- [[`14e93f6369`](https://github.com/nodejs/node/commit/14e93f6369)] - **test**: dynamic port in parallel cluster tests (Sebastian Plesciuc) [#12584](https://github.com/nodejs/node/pull/12584)
- [[`d10eb83325`](https://github.com/nodejs/node/commit/d10eb83325)] - **test**: remove flaky designation for test on AIX (Rich Trott) [#12564](https://github.com/nodejs/node/pull/12564)
- [[`f0b5afe721`](https://github.com/nodejs/node/commit/f0b5afe721)] - **test**: dynamic port in cluster worker dgram (Sebastian Plesciuc) [#12487](https://github.com/nodejs/node/pull/12487)
- [[`661ff6dae3`](https://github.com/nodejs/node/commit/661ff6dae3)] - **test**: move test-debugger-repeat-last to sequential (kumarrishav) [#12470](https://github.com/nodejs/node/pull/12470)
- [[`0fb69de277`](https://github.com/nodejs/node/commit/0fb69de277)] - **test**: use duplex streams in duplex stream test (cjihrig) [#12514](https://github.com/nodejs/node/pull/12514)
- [[`f84a5e19b7`](https://github.com/nodejs/node/commit/f84a5e19b7)] - **test**: use JSON.stringify to trigger stack overflow (Yang Guo) [#12481](https://github.com/nodejs/node/pull/12481)
- [[`96b2faa79b`](https://github.com/nodejs/node/commit/96b2faa79b)] - **test**: fix parallel/test-setproctitle.js on alpine (David Cai) [#12413](https://github.com/nodejs/node/pull/12413)
- [[`e3ccc3109c`](https://github.com/nodejs/node/commit/e3ccc3109c)] - **test**: set benchmark-child-process flaky on windows (Rich Trott) [#12561](https://github.com/nodejs/node/pull/12561)
- [[`37261319d6`](https://github.com/nodejs/node/commit/37261319d6)] - **test**: minimize time for child_process benchmark (Rich Trott) [#12518](https://github.com/nodejs/node/pull/12518)
- [[`eac0d70429`](https://github.com/nodejs/node/commit/eac0d70429)] - **test**: console.log removed from test-net-localport (Faiz Halde) [#12483](https://github.com/nodejs/node/pull/12483)
- [[`a213320745`](https://github.com/nodejs/node/commit/a213320745)] - **test**: dynamic port in cluster worker disconnect (Sebastian Plesciuc) [#12457](https://github.com/nodejs/node/pull/12457)
- [[`ddc35282c0`](https://github.com/nodejs/node/commit/ddc35282c0)] - **test**: remove uses of common.PORT in test-tls-client tests (Ahmed Taj elsir) [#12461](https://github.com/nodejs/node/pull/12461)
- [[`f48d06c042`](https://github.com/nodejs/node/commit/f48d06c042)] - **test**: dynamic port in cluster worker send (Sebastian Plesciuc) [#12472](https://github.com/nodejs/node/pull/12472)
- [[`a75fbe024a`](https://github.com/nodejs/node/commit/a75fbe024a)] - **test**: increase coverage for buffer.js (Rich Trott) [#12476](https://github.com/nodejs/node/pull/12476)
- [[`e919941a21`](https://github.com/nodejs/node/commit/e919941a21)] - **test**: add test for child_process benchmark (Joyee Cheung) [#12326](https://github.com/nodejs/node/pull/12326)
- [[`8e3d54aca5`](https://github.com/nodejs/node/commit/8e3d54aca5)] - **test**: complete coverage of lib/child_process.js (cjihrig) [#12367](https://github.com/nodejs/node/pull/12367)
- [[`922c457365`](https://github.com/nodejs/node/commit/922c457365)] - **test**: buffer should always be stringified (Luca Maraschi) [#12355](https://github.com/nodejs/node/pull/12355)
- [[`611c23cca6`](https://github.com/nodejs/node/commit/611c23cca6)] - **test**: use dynamic port in test-cluster-bind-twice (Rich Trott) [#12418](https://github.com/nodejs/node/pull/12418)
- [[`a7e13e012d`](https://github.com/nodejs/node/commit/a7e13e012d)] - **test**: remove common.PORT from test-cluster\*.js (Tarun Batra) [#12441](https://github.com/nodejs/node/pull/12441)
- [[`a34cccceaa`](https://github.com/nodejs/node/commit/a34cccceaa)] - **test**: use dynamic port in 3 test-cluster-worker tests (Sebastian Plesciuc) [#12443](https://github.com/nodejs/node/pull/12443)
- [[`320b80b792`](https://github.com/nodejs/node/commit/320b80b792)] - **test**: add --use-bundled-ca to tls-cnnic-whitelist (Daniel Bevenius) [#12394](https://github.com/nodejs/node/pull/12394)
- [[`3c59d87164`](https://github.com/nodejs/node/commit/3c59d87164)] - **test**: add crypto check to crypto-lazy-transform (Daniel Bevenius) [#12424](https://github.com/nodejs/node/pull/12424)
- [[`0f290f2414`](https://github.com/nodejs/node/commit/0f290f2414)] - **test**: enable setuid/setgid test (Rich Trott) [#12403](https://github.com/nodejs/node/pull/12403)
- [[`4a19062294`](https://github.com/nodejs/node/commit/4a19062294)] - **test**: replace \[\].join() with ''.repeat() (Jackson Tian) [#12305](https://github.com/nodejs/node/pull/12305)
- [[`5ed9ed3317`](https://github.com/nodejs/node/commit/5ed9ed3317)] - **test**: remove common.PORT from test-cluster-basic (Rich Trott) [#12377](https://github.com/nodejs/node/pull/12377)
- [[`0661fdf781`](https://github.com/nodejs/node/commit/0661fdf781)] - **test**: add test-benchmark-crypto (Rich Trott) [#12347](https://github.com/nodejs/node/pull/12347)
- [[`092c7239ee`](https://github.com/nodejs/node/commit/092c7239ee)] - **test**: add hasCrypto check to test-debug-usage (Daniel Bevenius) [#12357](https://github.com/nodejs/node/pull/12357)
- [[`aff0cfc2d7`](https://github.com/nodejs/node/commit/aff0cfc2d7)] - **test**: improve punycode coverage to check surrogate pair (Nao YONASHIRO) [#12354](https://github.com/nodejs/node/pull/12354)
- [[`1ab998f25c`](https://github.com/nodejs/node/commit/1ab998f25c)] - **test**: fix allocUnsafe uninitialized buffer check (Karl Cheng) [#12290](https://github.com/nodejs/node/pull/12290)
- [[`fe45a379e4`](https://github.com/nodejs/node/commit/fe45a379e4)] - **test**: add arrow functions to test-util-inspect (Alexey Orlenko) [#11781](https://github.com/nodejs/node/pull/11781)
- [[`9bdf62f7a7`](https://github.com/nodejs/node/commit/9bdf62f7a7)] - **test**: refactor test-util-inspect.js (Alexey Orlenko) [#11779](https://github.com/nodejs/node/pull/11779)
- [[`1adb08ee92`](https://github.com/nodejs/node/commit/1adb08ee92)] - **test**: synchronize WPT url test data (Daijiro Wachi) [#12507](https://github.com/nodejs/node/pull/12507)
- [[`90bba9fd3e`](https://github.com/nodejs/node/commit/90bba9fd3e)] - **test,doc**: document `crashOnUnhandledRejection()` (Anna Henningsen) [#12699](https://github.com/nodejs/node/pull/12699)
- [[`46a7c297d3`](https://github.com/nodejs/node/commit/46a7c297d3)] - **tools**: use no-useless-concat ESLint rule (Vse Mozhet Byt) [#12613](https://github.com/nodejs/node/pull/12613)
- [[`258eeaa519`](https://github.com/nodejs/node/commit/258eeaa519)] - **tools**: enable no-useless-return eslint rule (cjihrig) [#12577](https://github.com/nodejs/node/pull/12577)
- [[`200e899cc4`](https://github.com/nodejs/node/commit/200e899cc4)] - **tools**: add `root: true` in main .eslintrc.yaml (Vse Mozhet Byt) [#12570](https://github.com/nodejs/node/pull/12570)
- [[`13441eb1e1`](https://github.com/nodejs/node/commit/13441eb1e1)] - **tools**: add table parsing capability to the doctool (Roman Reiss) [#9532](https://github.com/nodejs/node/pull/9532)
- [[`3c8e366c2a`](https://github.com/nodejs/node/commit/3c8e366c2a)] - **tools**: update certdata.txt (Ben Noordhuis) [#12402](https://github.com/nodejs/node/pull/12402)
- [[`6003958872`](https://github.com/nodejs/node/commit/6003958872)] - **tools**: add compile_commands.json gyp generator (Ben Noordhuis) [#12450](https://github.com/nodejs/node/pull/12450)
- [[`83a28eeff8`](https://github.com/nodejs/node/commit/83a28eeff8)] - **tools**: update gyp to eb296f6 (Refael Ackermann) [#12450](https://github.com/nodejs/node/pull/12450)
- [[`5ccafa2a33`](https://github.com/nodejs/node/commit/5ccafa2a33)] - **tools**: replace custom ESLint timers rule (Rich Trott) [#12162](https://github.com/nodejs/node/pull/12162)
- [[`60daaaeff2`](https://github.com/nodejs/node/commit/60daaaeff2)] - **url**: always show password for URL instances (Brian White) [#12420](https://github.com/nodejs/node/pull/12420)
- [[`ac52923308`](https://github.com/nodejs/node/commit/ac52923308)] - **url**: update WHATWG URL API to latest spec (Timothy Gu) [#12523](https://github.com/nodejs/node/pull/12523)
- [[`539ffaef83`](https://github.com/nodejs/node/commit/539ffaef83)] - **url**: improve descriptiveness of identifier (Rich Trott) [#12579](https://github.com/nodejs/node/pull/12579)
- [[`dfc801737f`](https://github.com/nodejs/node/commit/dfc801737f)] - **url**: improve WHATWG URL inspection (Timothy Gu) [#12507](https://github.com/nodejs/node/pull/12507)
- [[`b2a9e60ce1`](https://github.com/nodejs/node/commit/b2a9e60ce1)] - **url**: clean up WHATWG URL origin generation (Timothy Gu) [#12507](https://github.com/nodejs/node/pull/12507)
- [[`29531d2654`](https://github.com/nodejs/node/commit/29531d2654)] - **url**: disallow invalid IPv4 in IPv6 parser (Daijiro Wachi) [#12507](https://github.com/nodejs/node/pull/12507)
- [[`ffb2ef4ff3`](https://github.com/nodejs/node/commit/ffb2ef4ff3)] - **url**: remove javascript URL special case (Daijiro Wachi) [#12507](https://github.com/nodejs/node/pull/12507)
- [[`5a27f6335d`](https://github.com/nodejs/node/commit/5a27f6335d)] - **url**: trim leading slashes of file URL paths (Daijiro Wachi) [#12507](https://github.com/nodejs/node/pull/12507)
- [[`b50a84a259`](https://github.com/nodejs/node/commit/b50a84a259)] - **url**: avoid instanceof for WHATWG URL (Brian White) [#12507](https://github.com/nodejs/node/pull/12507)
- [[`b9b93f2165`](https://github.com/nodejs/node/commit/b9b93f2165)] - **url**: error when domainTo\*() is called w/o argument (Timothy Gu) [#12507](https://github.com/nodejs/node/pull/12507)
- [[`b8d1a45beb`](https://github.com/nodejs/node/commit/b8d1a45beb)] - **url**: change path parsing for non-special URLs (Daijiro Wachi) [#12507](https://github.com/nodejs/node/pull/12507)
- [[`c7de98a7bf`](https://github.com/nodejs/node/commit/c7de98a7bf)] - **url**: add ToObject method to native URL class (James M Snell) [#12507](https://github.com/nodejs/node/pull/12507)
- [[`4b6097d3bd`](https://github.com/nodejs/node/commit/4b6097d3bd)] - **url**: use a class for WHATWG url\[context\] (Timothy Gu) [#12507](https://github.com/nodejs/node/pull/12507)
- [[`01b8839495`](https://github.com/nodejs/node/commit/01b8839495)] - **url**: spec-compliant URLSearchParams parser (Timothy Gu) [#12507](https://github.com/nodejs/node/pull/12507)
- [[`b8ff2c9f37`](https://github.com/nodejs/node/commit/b8ff2c9f37)] - **url**: spec-compliant URLSearchParams serializer (Timothy Gu) [#12507](https://github.com/nodejs/node/pull/12507)
- [[`d6fe91648a`](https://github.com/nodejs/node/commit/d6fe91648a)] - **url**: prioritize toString when stringifying (Timothy Gu) [#12507](https://github.com/nodejs/node/pull/12507)
- [[`7c9ca0f3ce`](https://github.com/nodejs/node/commit/7c9ca0f3ce)] - **url**: enforce valid UTF-8 in WHATWG parser (Timothy Gu) [#12507](https://github.com/nodejs/node/pull/12507)
- [[`b4052e656c`](https://github.com/nodejs/node/commit/b4052e656c)] - **url**: extend URLSearchParams constructor (Timothy Gu) [#12507](https://github.com/nodejs/node/pull/12507)
- [[`e77f1e2177`](https://github.com/nodejs/node/commit/e77f1e2177)] - **v8**: fix stack overflow in recursive method (Ben Noordhuis) [#12460](https://github.com/nodejs/node/pull/12460)
- [[`25b851bdd4`](https://github.com/nodejs/node/commit/25b851bdd4)] - **v8**: fix build errors with g++ 7 (Ben Noordhuis) [#12392](https://github.com/nodejs/node/pull/12392)

Windows 32-bit Installer: https://nodejs.org/dist/v7.10.0/node-v7.10.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v7.10.0/node-v7.10.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v7.10.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v7.10.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v7.10.0/node-v7.10.0.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v7.10.0/node-v7.10.0-darwin-x64.tar.gz \
Linux 32-bit Binary: https://nodejs.org/dist/v7.10.0/node-v7.10.0-linux-x86.tar.xz \
Linux 64-bit Binary: https://nodejs.org/dist/v7.10.0/node-v7.10.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v7.10.0/node-v7.10.0-linux-ppc64le.tar.xz \
Linux PPC BE 64-bit Binary: https://nodejs.org/dist/v7.10.0/node-v7.10.0-linux-ppc64.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v7.10.0/node-v7.10.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v7.10.0/node-v7.10.0-aix-ppc64.tar.gz \
SmartOS 32-bit Binary: https://nodejs.org/dist/v7.10.0/node-v7.10.0-sunos-x86.tar.xz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v7.10.0/node-v7.10.0-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v7.10.0/node-v7.10.0-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v7.10.0/node-v7.10.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v7.10.0/node-v7.10.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v7.10.0/node-v7.10.0.tar.gz \
Other release files: https://nodejs.org/dist/v7.10.0/ \
Documentation: https://nodejs.org/docs/v7.10.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

84c478cc48345be13a08b0dc84da1a80c2b700fb7c78120828231dc1eca3fe18  node-v7.10.0-aix-ppc64.tar.gz
7ec21c06b80924893fff3eba242cbe5c8b1aadcdd6be39707b28dd3cfe8e558f  node-v7.10.0-darwin-x64.tar.gz
16071d4107fb81cb4af9de5d849f5b128c0b5ea16c4af05b9fc38a30e5d51d8c  node-v7.10.0-darwin-x64.tar.xz
1c99ce1ef2c24dcafcf2ae904d97c85faa6c121f3d35ba111b819ba78d3a3458  node-v7.10.0-headers.tar.gz
511edd25c9f6a4636e02de63af0e2e53fc814cd88342549d38232c36cdd91a0c  node-v7.10.0-headers.tar.xz
62566c5c588d86942a076c596bfc504c337ba0db9ac4e6e6bbff3ed15e2ad404  node-v7.10.0-linux-arm64.tar.gz
998ddc1f8eb4adf20e3edcf327e48d25f031444b7bbbf127341631c31657a932  node-v7.10.0-linux-arm64.tar.xz
659992438c2db5c4c4b69532658a0392033c59a4e29e82736bee9913bf7335df  node-v7.10.0-linux-armv6l.tar.gz
7ee520403c0a6e4a4e169a41644d1b1a330d9f34413dd027375a3f7364419e33  node-v7.10.0-linux-armv6l.tar.xz
87adb59f764f7da5adb9fa46f4baa116c93c5449969cdee7b5a64c12512e0c40  node-v7.10.0-linux-armv7l.tar.gz
a493e915693ab06d257a33f443e8bee10498d230dad906a7d0af5c3922289b53  node-v7.10.0-linux-armv7l.tar.xz
4dbd443c19bc5decbe13f032a417f1c9a02d7436fd53999eb856066f774f4916  node-v7.10.0-linux-ppc64le.tar.gz
7a638d2cd3a16c209b50d1023a22b109526ceeb6a5eb9cabc21ac69d51795c99  node-v7.10.0-linux-ppc64le.tar.xz
8285114cd36599e0050c0ed3a5da2f29280dc9e4da3c6823190f3f680f8a40df  node-v7.10.0-linux-ppc64.tar.gz
87fe05719edbb10eeffe1858c4a236859776d5287b73527e992f0b6a5ef4c484  node-v7.10.0-linux-ppc64.tar.xz
2e970ea704862bbf19dc6b2b2922901c05923b681efd7113eb613c4b6436494a  node-v7.10.0-linux-s390x.tar.gz
f9779215f4cb6ae6c6b2e5d11f335af74b69e952c01521106458a7c11e300741  node-v7.10.0-linux-s390x.tar.xz
9da0e99091897795491d21d58c40186f75ca7bf505d145d1a2e558f8c754a81b  node-v7.10.0-linux-x64.tar.gz
6166b9f3fb1a9e861335d864688fee5366f040db808080856a1a2b71b6019786  node-v7.10.0-linux-x64.tar.xz
ca37e75a605497b1f33d085353b1a8315b5a9b4de9463885bf62831f7f4511c1  node-v7.10.0-linux-x86.tar.gz
d9eaa35170eeeb07f3eaa9e5f03e68c5729071d65e4a3d933411136ec82c9edd  node-v7.10.0-linux-x86.tar.xz
f9deccfbbcd76d6e8b5f7fb1b5920a6770e174ddce01fa4d1a630eb93ca1e486  node-v7.10.0.pkg
779672d68114494e085201ab555b8abc6e325a0510a3bf765afb70ab72a19ae4  node-v7.10.0-sunos-x64.tar.gz
e79baad6529c5fba6d6bceded0566e7d3f4ebaa4ff80c361e0ecf959de8f99fe  node-v7.10.0-sunos-x64.tar.xz
40d5b62bab968cd67f8359c4f8d7a6ec8b1e79d31d9316c5f87f0ea38a870c22  node-v7.10.0-sunos-x86.tar.gz
61360a41cc5a2a739bb9687af5fef293dc685227db4236d6480642c5250c985a  node-v7.10.0-sunos-x86.tar.xz
c4843fe8bffb59c2327063a5e67a89af60d7927f7ace7548695a2cf4c1aa6d03  node-v7.10.0.tar.gz
dc34dd15524ba821ffcae7b245eabe8631e2614d5e3cb8ff08fbfdadf5919f21  node-v7.10.0.tar.xz
cbd1a44efcd7c169f037460fbc4d1a05a105030f39631c9875247c6a70d0a91a  node-v7.10.0-win-x64.7z
617a58b000c9d14ba5c6f63681f7145f394178dc315c023fee8f0c9e77311499  node-v7.10.0-win-x64.zip
cef0c0298980b07b4cd94a141a092d4ff1fbcb89bc23fde64a0f5a3659d3ab64  node-v7.10.0-win-x86.7z
957a5e934f22b27824ef4a83f0f402c5212c605caf217b187f308acfaf0dd71e  node-v7.10.0-win-x86.zip
24a9170bab2f9f0afd54b1ca019f249cf30308d682151eb23dca3918dc6afff6  node-v7.10.0-x64.msi
17c036e4bd4698efeb771d81f21624bdc6ab907aa4a8584ad0e801a057eb4cee  node-v7.10.0-x86.msi
723d1fd9bab9512d08d53f01bc6d5c039d07a563786a771c6c2e9ba3778fee4e  win-x64/node.exe
6d78274127db399ae389339ef29807a11d5081decb61aed73d2ee7bd24b89cd2  win-x64/node.lib
78717ea89ec5830eaeb72c49f3eb6bdada2aee1fa8b03c1b411451a130ac7b32  win-x64/node_pdb.7z
5b46fc3f7f84e33b2cc674892d0f06faa43115daa44d6d50c07d6caa21e0cdb3  win-x64/node_pdb.zip
b6e5a398d90f598bf6b6d88d6de04119697341eff020eebd9bb64ad1b1dd710f  win-x86/node.exe
900467795677e25b9c10b5c4b0c7d8749f5f35edc1008700c38944ab7caba8c9  win-x86/node.lib
8b31d6b4ddc9888d456a471f449d228b57e8387a936beef5a0dad3e7b8087692  win-x86/node_pdb.7z
515c19eea8e8b7b9910450fd010cfc77a74c70adc794831f7d9bc2810ee649be  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----
Comment: GPGTools - https://gpgtools.org

iQIcBAEBCAAGBQJZCdYgAAoJELY7U1pMIGypCekP/jB6QfYB7lKXKCveSmV8+wp6
qjQKLcQDobZhTnsMI6gthUSJ8cSeZxuNr/gohxYRniKaFjMtyO9Ch3WoRas+e3JB
LQ7tcFwYzqbN5QeMueWh6B7RF6W3uMYA9nAyFlhoahzsn37zgjfiuL0yw9JA+bDy
N/Bswsu0drpaaOKbrZAf6+TOolm2JCLPOyADdxz0oCMTksoYO69rZf3NXrN0amzc
45SaB++84QkGe0L0WjrJrbudipAY+8h0dLps75bTAhmANwQR/D6Iy5KUqkiSQSpU
HEPkipFYy2yO8RfW15V8pY2nbWCdheK6VsWqQ/FLoP1TgLGAZr/Wh75hHggLy4ZU
Mfbc+lmBL8i47N0JyD8p7u+t4HJtrlJLxqv3BkLlmCWt47lzOBN2tb03M1nEAEF3
AgE3+xs2TEC9rieBCY5XLaoAJoC65T5G/P/yRceEhD+7cYS5GjCaCIm1DWB2NARR
9ZsBr8BWlD+4FA74/V473/OgYo4eacOFRoLxL0JkMvXFsJgCRdAdPTiNz6nNYZTh
m7hjVf9cuPySaTiGNw2FOK/nO4wqqDke2qpRwknYwUtcUTNnFsTBYAOxerhcfJdJ
KRvaukA2qowG1VtHWfmYzG23f3P3odz79+OAi2ZKzP3iukWOkWj06A/NhkVwHBAU
BlSMiAXBLwELmBEjUxfA
=Om1d
-----END PGP SIGNATURE-----

```
