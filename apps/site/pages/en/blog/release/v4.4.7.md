---
date: '2016-06-28T22:54:11.515Z'
category: release
title: Node v4.4.7 (LTS)
layout: blog-post
author: <PERSON><PERSON>
---

### Notable Changes

- **debugger**:
  - All properties of an array (aside from length) can now be printed in the repl (cjihrig) [#6448](https://github.com/nodejs/node/pull/6448)
- **npm**:
  - Upgrade npm to 2.15.8 (<PERSON>) [#7412](https://github.com/nodejs/node/pull/7412)
- **stream**:
  - Fix for a bug that became more prevalent with the stream changes that landed in v4.4.5. (<PERSON>) [#7160](https://github.com/nodejs/node/pull/7160)
- **V8**:
  - Fix for a bug in crankshaft that was causing crashes on arm64 (<PERSON><PERSON>) [#7442](https://github.com/nodejs/node/pull/7442)
  - Add missing classes to postmortem info such as JSMap and JSSet (evan.lucas) [#3792](https://github.com/nodejs/node/pull/3792)

### Commits

- [[`87cdb83a96`](https://github.com/nodejs/node/commit/87cdb83a96)] - **benchmark**: merge url.js with url-resolve.js (Andreas Madsen) [#5177](https://github.com/nodejs/node/pull/5177)
- [[`921e8568d5`](https://github.com/nodejs/node/commit/921e8568d5)] - **benchmark**: move misc to categorized directories (Andreas Madsen) [#5177](https://github.com/nodejs/node/pull/5177)
- [[`c189eec14e`](https://github.com/nodejs/node/commit/c189eec14e)] - **benchmark**: fix configuation parameters (Andreas Madsen) [#5177](https://github.com/nodejs/node/pull/5177)
- [[`58ad451f0b`](https://github.com/nodejs/node/commit/58ad451f0b)] - **benchmark**: move string-decoder to its own category (Andreas Madsen) [#5177](https://github.com/nodejs/node/pull/5177)
- [[`a01caa3166`](https://github.com/nodejs/node/commit/a01caa3166)] - **build**: don't compile with -B, redux (Ben Noordhuis) [#6650](https://github.com/nodejs/node/pull/6650)
- [[`37606caeaf`](https://github.com/nodejs/node/commit/37606caeaf)] - **build**: don't compile with -B (Ben Noordhuis) [#6393](https://github.com/nodejs/node/pull/6393)
- [[`64fb7a1929`](https://github.com/nodejs/node/commit/64fb7a1929)] - **build**: update android-configure script for npm (Robert Chiras) [#6349](https://github.com/nodejs/node/pull/6349)
- [[`43ce6fc8d2`](https://github.com/nodejs/node/commit/43ce6fc8d2)] - **build**: fix DESTCPU detection for binary target (Richard Lau) [#6310](https://github.com/nodejs/node/pull/6310)
- [[`6dfe7aeed5`](https://github.com/nodejs/node/commit/6dfe7aeed5)] - **cares**: Support malloc(0) scenarios for AIX (Gireesh Punathil) [#6305](https://github.com/nodejs/node/pull/6305)
- [[`2389006720`](https://github.com/nodejs/node/commit/2389006720)] - **debugger**: display array contents in repl (cjihrig) [#6448](https://github.com/nodejs/node/pull/6448)
- [[`1c6809ce75`](https://github.com/nodejs/node/commit/1c6809ce75)] - **debugger**: introduce exec method for debugger (Jackson Tian)
- [[`200b3ca9ed`](https://github.com/nodejs/node/commit/200b3ca9ed)] - **deps**: upgrade npm in LTS to 2.15.8 (Rebecca Turner) [#7412](https://github.com/nodejs/node/pull/7412)
- [[`49921e8819`](https://github.com/nodejs/node/commit/49921e8819)] - **deps**: backport 102e3e87e7 from V8 upstream (Myles Borins) [#7442](https://github.com/nodejs/node/pull/7442)
- [[`de00f91041`](https://github.com/nodejs/node/commit/de00f91041)] - **deps**: backport bc2e393 from v8 upstream (evan.lucas) [#3792](https://github.com/nodejs/node/pull/3792)
- [[`1549899531`](https://github.com/nodejs/node/commit/1549899531)] - **dgram,test**: add addMembership/dropMembership tests (Rich Trott) [#6753](https://github.com/nodejs/node/pull/6753)
- [[`0ba3c2ca66`](https://github.com/nodejs/node/commit/0ba3c2ca66)] - **doc**: fix layout problem in v4 changelog (Myles Borins) [#7394](https://github.com/nodejs/node/pull/7394)
- [[`98469ad84d`](https://github.com/nodejs/node/commit/98469ad84d)] - **doc**: correct args for cluster message event (Colin Ihrig) [#7297](https://github.com/nodejs/node/pull/7297)
- [[`67863f110b`](https://github.com/nodejs/node/commit/67863f110b)] - **doc**: update licenses (Myles Borins) [#7127](https://github.com/nodejs/node/pull/7127)
- [[`c31eaad42d`](https://github.com/nodejs/node/commit/c31eaad42d)] - **doc**: clarify buffer class (Steve Mao) [#6914](https://github.com/nodejs/node/pull/6914)
- [[`e0dd476fe5`](https://github.com/nodejs/node/commit/e0dd476fe5)] - **doc**: fix typos in timers topic to aid readability (Kevin Donahue) [#6916](https://github.com/nodejs/node/pull/6916)
- [[`a8391bc9fc`](https://github.com/nodejs/node/commit/a8391bc9fc)] - **doc**: add jhamhader to collaborators (Yuval Brik) [#6946](https://github.com/nodejs/node/pull/6946)
- [[`22ca7b877b`](https://github.com/nodejs/node/commit/22ca7b877b)] - **doc**: add @othiym23 to list of collaborators (Forrest L Norvell) [#6945](https://github.com/nodejs/node/pull/6945)
- [[`2c3c4e5819`](https://github.com/nodejs/node/commit/2c3c4e5819)] - **doc**: reference list of language-specific globals (Anna Henningsen) [#6900](https://github.com/nodejs/node/pull/6900)
- [[`5a1a0b5ed1`](https://github.com/nodejs/node/commit/5a1a0b5ed1)] - **doc**: make the api doc print-friendly (Marian) [#6748](https://github.com/nodejs/node/pull/6748)
- [[`03db88e012`](https://github.com/nodejs/node/commit/03db88e012)] - **doc**: add bengl to collaborators (Bryan English) [#6921](https://github.com/nodejs/node/pull/6921)
- [[`fbf95dde94`](https://github.com/nodejs/node/commit/fbf95dde94)] - **doc**: Update DCO to v1.1 (William Kapke) [#6353](https://github.com/nodejs/node/pull/6353)
- [[`f23a9c39c0`](https://github.com/nodejs/node/commit/f23a9c39c0)] - **doc**: fix typo in Error.captureStackTrace (Mohsen) [#6811](https://github.com/nodejs/node/pull/6811)
- [[`30ab6a890c`](https://github.com/nodejs/node/commit/30ab6a890c)] - **doc**: fix name to match git log (Robert Jefe Lindstaedt) [#6880](https://github.com/nodejs/node/pull/6880)
- [[`2b0f40ca16`](https://github.com/nodejs/node/commit/2b0f40ca16)] - **doc**: add note for fs.watch virtualized env (Robert Jefe Lindstaedt) [#6809](https://github.com/nodejs/node/pull/6809)
- [[`3b461870be`](https://github.com/nodejs/node/commit/3b461870be)] - **doc**: Backport ee.once doc clarifications to 4.x. (Lance Ball) [#7103](https://github.com/nodejs/node/pull/7103)
- [[`eadb7e5b20`](https://github.com/nodejs/node/commit/eadb7e5b20)] - **doc**: subdivide TOC, add auxiliary links (Jeremiah Senkpiel) [#6167](https://github.com/nodejs/node/pull/6167)
- [[`107839c5dd`](https://github.com/nodejs/node/commit/107839c5dd)] - **doc**: no Node.js(1) (Jeremiah Senkpiel) [#6167](https://github.com/nodejs/node/pull/6167)
- [[`401325f9e2`](https://github.com/nodejs/node/commit/401325f9e2)] - **doc**: better example & synopsis (Jeremiah Senkpiel) [#6167](https://github.com/nodejs/node/pull/6167)
- [[`c654184f28`](https://github.com/nodejs/node/commit/c654184f28)] - **doc**: remove link to Sign in crypto.md (Kirill Fomichev) [#6812](https://github.com/nodejs/node/pull/6812)
- [[`3e9288e466`](https://github.com/nodejs/node/commit/3e9288e466)] - **doc**: fix exec example in child_process (Evan Lucas) [#6660](https://github.com/nodejs/node/pull/6660)
- [[`3d820e45b4`](https://github.com/nodejs/node/commit/3d820e45b4)] - **doc**: "a" -> "an" in api/documentation.md (Anchika Agarwal) [#6689](https://github.com/nodejs/node/pull/6689)
- [[`352496daa2`](https://github.com/nodejs/node/commit/352496daa2)] - **doc**: move the readme newcomers section (Jeremiah Senkpiel) [#6681](https://github.com/nodejs/node/pull/6681)
- [[`ac6b921ce5`](https://github.com/nodejs/node/commit/ac6b921ce5)] - **doc**: mention existence/purpose of module wrapper (Matt Harrison) [#6433](https://github.com/nodejs/node/pull/6433)
- [[`97d1fc0fc6`](https://github.com/nodejs/node/commit/97d1fc0fc6)] - **doc**: improve onboarding-extras.md formatting (Jeremiah Senkpiel) [#6548](https://github.com/nodejs/node/pull/6548)
- [[`c9b144ddd4`](https://github.com/nodejs/node/commit/c9b144ddd4)] - **doc**: linkify remaining references to fs.Stats object (Kevin Donahue) [#6485](https://github.com/nodejs/node/pull/6485)
- [[`d909c25a33`](https://github.com/nodejs/node/commit/d909c25a33)] - **doc**: fix the lint of an example in cluster.md (yorkie) [#6516](https://github.com/nodejs/node/pull/6516)
- [[`21d02f460f`](https://github.com/nodejs/node/commit/21d02f460f)] - **doc**: add missing underscore for markdown italics (Kevin Donahue) [#6529](https://github.com/nodejs/node/pull/6529)
- [[`18ecc779bb`](https://github.com/nodejs/node/commit/18ecc779bb)] - **doc**: ensure consistent grammar in node.1 file (justshiv) [#6426](https://github.com/nodejs/node/pull/6426)
- [[`52d9e7b61d`](https://github.com/nodejs/node/commit/52d9e7b61d)] - **doc**: fix a typo in \_\_dirname section (William Luo) [#6473](https://github.com/nodejs/node/pull/6473)
- [[`de20235235`](https://github.com/nodejs/node/commit/de20235235)] - **doc**: remove all scrollbar styling (Claudio Rodriguez) [#6479](https://github.com/nodejs/node/pull/6479)
- [[`a6f45b4eda`](https://github.com/nodejs/node/commit/a6f45b4eda)] - **doc**: Remove extra space in REPL example (Juan) [#6447](https://github.com/nodejs/node/pull/6447)
- [[`feda15b2b8`](https://github.com/nodejs/node/commit/feda15b2b8)] - **doc**: update build instructions for OS X (Rich Trott) [#6309](https://github.com/nodejs/node/pull/6309)
- [[`3d1a3e4a30`](https://github.com/nodejs/node/commit/3d1a3e4a30)] - **doc**: change references to Stable to Current (Myles Borins) [#6318](https://github.com/nodejs/node/pull/6318)
- [[`e28598b1ef`](https://github.com/nodejs/node/commit/e28598b1ef)] - **doc**: update authors (James M Snell) [#6373](https://github.com/nodejs/node/pull/6373)
- [[`0f3a94acbd`](https://github.com/nodejs/node/commit/0f3a94acbd)] - **doc**: add JacksonTian to collaborators (Jackson Tian) [#6388](https://github.com/nodejs/node/pull/6388)
- [[`d7d54c8fd2`](https://github.com/nodejs/node/commit/d7d54c8fd2)] - **doc**: add Minqi Pan to collaborators (Minqi Pan) [#6387](https://github.com/nodejs/node/pull/6387)
- [[`83721c6fd2`](https://github.com/nodejs/node/commit/83721c6fd2)] - **doc**: add eljefedelrodeodeljefe to collaborators (Robert Jefe Lindstaedt) [#6389](https://github.com/nodejs/node/pull/6389)
- [[`b112fd1b4e`](https://github.com/nodejs/node/commit/b112fd1b4e)] - **doc**: add ronkorving to collaborators (ronkorving) [#6385](https://github.com/nodejs/node/pull/6385)
- [[`ac60d9cc86`](https://github.com/nodejs/node/commit/ac60d9cc86)] - **doc**: add estliberitas to collaborators (Alexander Makarenko) [#6386](https://github.com/nodejs/node/pull/6386)
- [[`435cd56de5`](https://github.com/nodejs/node/commit/435cd56de5)] - **doc**: DCO anchor that doesn't change (William Kapke) [#6257](https://github.com/nodejs/node/pull/6257)
- [[`7d8141dd1b`](https://github.com/nodejs/node/commit/7d8141dd1b)] - **doc**: add stefanmb to collaborators (Stefan Budeanu) [#6227](https://github.com/nodejs/node/pull/6227)
- [[`6dfc96326d`](https://github.com/nodejs/node/commit/6dfc96326d)] - **doc**: add iWuzHere to collaborators (Imran Iqbal) [#6226](https://github.com/nodejs/node/pull/6226)
- [[`3dbcc73159`](https://github.com/nodejs/node/commit/3dbcc73159)] - **doc**: add santigimeno to collaborators (Santiago Gimeno) [#6225](https://github.com/nodejs/node/pull/6225)
- [[`ae3eb24a3d`](https://github.com/nodejs/node/commit/ae3eb24a3d)] - **doc**: add addaleax to collaborators (Anna Henningsen) [#6224](https://github.com/nodejs/node/pull/6224)
- [[`46ee7bb4ba`](https://github.com/nodejs/node/commit/46ee7bb4ba)] - **doc**: fix incorrect references in buffer docs (Amery) [#6194](https://github.com/nodejs/node/pull/6194)
- [[`e3f78eb7c1`](https://github.com/nodejs/node/commit/e3f78eb7c1)] - **doc**: improve rendering of v4.4.5 changelog entry (Myles Borins) [#6958](https://github.com/nodejs/node/pull/6958)
- [[`bac87d01d9`](https://github.com/nodejs/node/commit/bac87d01d9)] - **gitignore**: adding .vs/ directory to .gitignore (Mike Kaufman) [#6070](https://github.com/nodejs/node/pull/6070)
- [[`93f2314dc2`](https://github.com/nodejs/node/commit/93f2314dc2)] - **gitignore**: ignore VS 2015 \*.VC.opendb files (Mike Kaufman) [#6070](https://github.com/nodejs/node/pull/6070)
- [[`c98aaf59bf`](https://github.com/nodejs/node/commit/c98aaf59bf)] - **http**: speed up checkIsHttpToken (Jackson Tian) [#4790](https://github.com/nodejs/node/pull/4790)
- [[`552e25cb6b`](https://github.com/nodejs/node/commit/552e25cb6b)] - **lib,test**: update in preparation for linter update (Rich Trott) [#6498](https://github.com/nodejs/node/pull/6498)
- [[`aaeeec4765`](https://github.com/nodejs/node/commit/aaeeec4765)] - **lib,test,tools**: alignment on variable assignments (Rich Trott) [#6869](https://github.com/nodejs/node/pull/6869)
- [[`b3acbc5648`](https://github.com/nodejs/node/commit/b3acbc5648)] - **net**: replace \_\_defineGetter\_\_ with defineProperty (Fedor Indutny) [#6284](https://github.com/nodejs/node/pull/6284)
- [[`4c1eb5bf03`](https://github.com/nodejs/node/commit/4c1eb5bf03)] - **repl**: create history file with mode 0600 (Carl Lei) [#3394](https://github.com/nodejs/node/pull/3394)
- [[`90306bb81d`](https://github.com/nodejs/node/commit/90306bb81d)] - **src**: use size_t for http parser array size fields (Ben Noordhuis) [#5969](https://github.com/nodejs/node/pull/5969)
- [[`af41a63d0f`](https://github.com/nodejs/node/commit/af41a63d0f)] - **src**: replace ARRAY_SIZE with typesafe arraysize (Ben Noordhuis) [#5969](https://github.com/nodejs/node/pull/5969)
- [[`037291e31f`](https://github.com/nodejs/node/commit/037291e31f)] - **src**: make sure Utf8Value always zero-terminates (Anna Henningsen) [#7101](https://github.com/nodejs/node/pull/7101)
- [[`a08a0179e9`](https://github.com/nodejs/node/commit/a08a0179e9)] - **stream**: ensure awaitDrain is increased once (David Halls) [#7292](https://github.com/nodejs/node/pull/7292)
- [[`b73ec46dcb`](https://github.com/nodejs/node/commit/b73ec46dcb)] - **stream**: reset awaitDrain after manual .resume() (Anna Henningsen) [#7160](https://github.com/nodejs/node/pull/7160)
- [[`55319fe798`](https://github.com/nodejs/node/commit/55319fe798)] - **stream_base**: expose `bytesRead` getter (Fedor Indutny) [#6284](https://github.com/nodejs/node/pull/6284)
- [[`0414d882ce`](https://github.com/nodejs/node/commit/0414d882ce)] - **test**: fix test-net-\* error code check for getaddrinfo(3) (Natanael Copa) [#5099](https://github.com/nodejs/node/pull/5099)
- [[`be0bb5f5fc`](https://github.com/nodejs/node/commit/be0bb5f5fc)] - **test**: fix unreliable known_issues test (Rich Trott) [#6555](https://github.com/nodejs/node/pull/6555)
- [[`ab50e82f42`](https://github.com/nodejs/node/commit/ab50e82f42)] - **test**: fix test-process-exec-argv flakiness (Santiago Gimeno) [#7128](https://github.com/nodejs/node/pull/7128)
- [[`4e38655d5f`](https://github.com/nodejs/node/commit/4e38655d5f)] - **test**: refactor test-tls-reuse-host-from-socket (Rich Trott) [#6756](https://github.com/nodejs/node/pull/6756)
- [[`1c4549a31e`](https://github.com/nodejs/node/commit/1c4549a31e)] - **test**: fix flaky test-stdout-close-catch (Santiago Gimeno) [#6808](https://github.com/nodejs/node/pull/6808)
- [[`3b94e31245`](https://github.com/nodejs/node/commit/3b94e31245)] - **test**: robust handling of env for npm-test-install (Myles Borins) [#6797](https://github.com/nodejs/node/pull/6797)
- [[`4067cde7ee`](https://github.com/nodejs/node/commit/4067cde7ee)] - **test**: abstract skip functionality to common (Jeremiah Senkpiel) [#7114](https://github.com/nodejs/node/pull/7114)
- [[`8b396e3d71`](https://github.com/nodejs/node/commit/8b396e3d71)] - **test**: fix test-debugger-repl-break-in-module (Rich Trott) [#6686](https://github.com/nodejs/node/pull/6686)
- [[`847b29c050`](https://github.com/nodejs/node/commit/847b29c050)] - **test**: fix test-debugger-repl-term (Rich Trott) [#6682](https://github.com/nodejs/node/pull/6682)
- [[`1d68bdbe3f`](https://github.com/nodejs/node/commit/1d68bdbe3f)] - **test**: fix error message checks in test-module-loading (James M Snell) [#5986](https://github.com/nodejs/node/pull/5986)
- [[`7e739ae159`](https://github.com/nodejs/node/commit/7e739ae159)] - **test,tools**: adjust function argument alignment (Rich Trott) [#7100](https://github.com/nodejs/node/pull/7100)
- [[`216486c2b6`](https://github.com/nodejs/node/commit/216486c2b6)] - **tools**: lint for function argument alignment (Rich Trott) [#7100](https://github.com/nodejs/node/pull/7100)
- [[`6a76485ad7`](https://github.com/nodejs/node/commit/6a76485ad7)] - **tools**: update ESLint to 2.9.0 (Rich Trott) [#6498](https://github.com/nodejs/node/pull/6498)
- [[`a31153c02c`](https://github.com/nodejs/node/commit/a31153c02c)] - **tools**: remove the minifying logic (Sakthipriyan Vairamani) [#6636](https://github.com/nodejs/node/pull/6636)
- [[`10bd1a73fd`](https://github.com/nodejs/node/commit/10bd1a73fd)] - **tools**: fix license-builder.sh again for ICU (Steven R. Loomis) [#6068](https://github.com/nodejs/node/pull/6068)
- [[`0f6146c6c0`](https://github.com/nodejs/node/commit/0f6146c6c0)] - **tools**: add tests for the doctool (Ian Kronquist) [#6031](https://github.com/nodejs/node/pull/6031)
- [[`cc3645cff3`](https://github.com/nodejs/node/commit/cc3645cff3)] - **tools**: lint for alignment of variable assignments (Rich Trott) [#6869](https://github.com/nodejs/node/pull/6869)

Windows 32-bit Installer: https://nodejs.org/dist/v4.4.7/node-v4.4.7-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v4.4.7/node-v4.4.7-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v4.4.7/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v4.4.7/win-x64/node.exe \
Mac OS X 64-bit Installer: https://nodejs.org/dist/v4.4.7/node-v4.4.7.pkg \
Mac OS X 64-bit Binary: https://nodejs.org/dist/v4.4.7/node-v4.4.7-darwin-x64.tar.gz \
Linux 32-bit Binary: https://nodejs.org/dist/v4.4.7/node-v4.4.7-linux-x86.tar.xz \
Linux 64-bit Binary: https://nodejs.org/dist/v4.4.7/node-v4.4.7-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v4.4.7/node-v4.4.7-linux-ppc64le.tar.xz \
SmartOS 32-bit Binary: https://nodejs.org/dist/v4.4.7/node-v4.4.7-sunos-x86.tar.xz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v4.4.7/node-v4.4.7-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v4.4.7/node-v4.4.7-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v4.4.7/node-v4.4.7-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v4.4.7/node-v4.4.7-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v4.4.7/node-v4.4.7.tar.gz \
Other release files: https://nodejs.org/dist/v4.4.7/ \
Documentation: https://nodejs.org/docs/v4.4.7/api/

Shasums (GPG signing hash: SHA512, file hash: SHA256):

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA1

1971386a2dcf4406b5bc414d111ea40b227cfebf396badd447c37ace0eef2fa9  node-v4.4.7-darwin-x64.tar.gz
33b08574815d20cdfa52cbc18ba68cc068a432b31f2f2ce3cd35b2b6dd83ced1  node-v4.4.7-darwin-x64.tar.xz
6c5cff955e7ffccc59386e79a811d868b97829fdf0accb2fc152da875171efde  node-v4.4.7-headers.tar.gz
de54185703f19087eac4ff29ddd24ca285f7ccbdca12bf38fe9e9a64c4ac6e09  node-v4.4.7-headers.tar.xz
a1e2faf3859976ac7322b950353044863c2e36ad6e2e09a8fc9f80f72fd01b18  node-v4.4.7-linux-arm64.tar.gz
33cc2a5457e56e976e4744b1df87f32f478ae9bb5a0799f01743e937f6f170a5  node-v4.4.7-linux-arm64.tar.xz
ba51e7487a15b31823e352fe3e3c949fa754fba7cffc4b82f7421c374f22980f  node-v4.4.7-linux-armv6l.tar.gz
6ef0397b71f14d3d9a7ccdd8a5e4217923c59ff348ce87bd5c1fdb751578b990  node-v4.4.7-linux-armv6l.tar.xz
68a6b3dd2b6554e89d05ed6df4c9cf33763c3eea63041ee2c8b189400f07567b  node-v4.4.7-linux-armv7l.tar.gz
66e6632e7c6dbde5009d1836b417d5d0d1471d07521dd3ba2af4a4b8edc18cf5  node-v4.4.7-linux-armv7l.tar.xz
a2202e69b97259d409b1db653d50305570f78b57a9f48a55dd63e0ceced2dad6  node-v4.4.7-linux-ppc64le.tar.gz
da3886e025328303c104a2399cb6aa7758e5bdf688e9fa99869227c5411199f0  node-v4.4.7-linux-ppc64le.tar.xz
781cb67ac07312c8e6d1c434965f0dca1cd04a525971e28811287af5025563b0  node-v4.4.7-linux-ppc64.tar.gz
386a725604f927b7dc113909f69567e7a750736616cb74aec3ed63afdf2a22fb  node-v4.4.7-linux-ppc64.tar.xz
5ad10465cc9d837c1fda8db0fd1bdc1a4ce823dd6afbc533ac2127e6a9a64133  node-v4.4.7-linux-x64.tar.gz
f28beed1b553696c14af078e484439e7c0eb6510b5608235f60873ba238e3907  node-v4.4.7-linux-x64.tar.xz
604c4f85ef00a13c172465c9672a2a0f13524921e255eeb75242afb42af6d814  node-v4.4.7-linux-x86.tar.gz
d2750b7efc6c218e9d543acb1639e3b499f8d720a292f0514744bb69def8cd7d  node-v4.4.7-linux-x86.tar.xz
7473a6e4974478d2b3f4fab2f1af804f5c30f06ba6ca736be66b63c410ff16e7  node-v4.4.7.pkg
d86382a337795d054d07677643cd049df545186d46528b8df7041e29ceb84779  node-v4.4.7-sunos-x64.tar.gz
ddded367e605d502e42f3df08895552f0901ccb35a025bfd4c9a2dfe86d044d3  node-v4.4.7-sunos-x64.tar.xz
d7bae99460f1fb35456303630c743d9c467449d2f56bd48f1dd96f1ecabb7da6  node-v4.4.7-sunos-x86.tar.gz
4481f5d0f50fd7eafe6d7a87daf15513ce38d6b0538646c127c39ebc64746e0d  node-v4.4.7-sunos-x86.tar.xz
cbe1c6e421969dd5639d0fbaa6d3c1f56c0463b87efe75be8594638da4d8fc4f  node-v4.4.7.tar.gz
1ef900b9cb3ffb617c433a3247a9d67ff36c9455cbc9c34175bee24bdbfdf731  node-v4.4.7.tar.xz
cb8bddc884cf8c34711f0baae68faebeb5bb81015974ca66044bed918536cc16  node-v4.4.7-x64.msi
e0985e3837921a0f9e5c3c4c472936d6113a7b4c0b202db69adb2b8aa52c8b27  node-v4.4.7-x86.msi
dccadddf0091b1686108061a34e247c552a71b7e1e9f17cd61bead8c041cdd7c  win-x64/node.exe
60233a0e265acb671a0fdb1db7c12a1b8668242a9665de6a8daf84e7610c088c  win-x64/node.lib
3b1b9673e7bcb89eb3dc8c345ee5f6ff567e838736c0f38e965281658421a30a  win-x86/node.exe
550c845c890dbd9cc1a52c7bc7af8a4b3a50708f069dda454393adcfa4428557  win-x86/node.lib
-----BEGIN PGP SIGNATURE-----

iQEcBAEBAgAGBQJXcv4iAAoJEJM7AfQLXKlGX7EH/j7D1dK1M3oT5H75lEwcomc7
Ezy+lq5bztHP9ghWq+4rmQB8/1FaW+0wk4nKyWb0S+NjjbJPeAaZ2mf5QLiKTjmC
5GTwtjEJy9daJ9+OCRVq0DNYJK0XdrsE/FFTGrY/EXE3f6SfKJmd3uW6S8NZt+cX
AaRkNAHvAkUmwDP+3bMKBr6QZZwGVddAQOMpnbXHjcdDHmWv8uh5+sR9KB3FsTJ4
lESEx4xcHkPUVBKveNqVfnbsSivPmpLcW6VYRWNOXyH7wFfYgtUGoeQ46wfhl1q9
D/hFCVLpxBNr2z6qLU+7fxInJzRPT9+2DBk1PN81axTG53AgUSNb11I2naVue/M=
=rofn
-----END PGP SIGNATURE-----

```
