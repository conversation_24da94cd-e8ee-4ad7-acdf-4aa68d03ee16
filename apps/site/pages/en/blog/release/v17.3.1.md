---
date: '2022-01-11T00:02:14.102Z'
category: release
title: Node v17.3.1 (Current)
layout: blog-post
author: <PERSON>
---

### Notable changes

#### Improper handling of URI Subject Alternative Names (Medium)(CVE-2021-44531)

Accepting arbitrary Subject Alternative Name (SAN) types, unless a PKI is specifically defined to use a particular SAN type, can result in bypassing name-constrained intermediates. Node.js was accepting URI SAN types, which PKIs are often not defined to use. Additionally, when a protocol allows URI SANs, Node.js did not match the URI correctly.

Versions of Node.js with the fix for this disable the URI SAN type when checking a certificate against a hostname. This behavior can be reverted through the `--security-revert` command-line option.

More details will be available at [CVE-2021-44531](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2021-44531) after publication.

#### Certificate Verification Bypass via String Injection (Medium)(CVE-2021-44532)

Node.js converts SANs (Subject Alternative Names) to a string format. It uses this string to check peer certificates against hostnames when validating connections. The string format was subject to an injection vulnerability when name constraints were used within a certificate chain, allowing the bypass of these name constraints.

Versions of Node.js with the fix for this escape SANs containing the problematic characters in order to prevent the injection. This behavior can be reverted through the `--security-revert` command-line option.

More details will be available at [CVE-2021-44532](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2021-44532) after publication.

#### Incorrect handling of certificate subject and issuer fields (Medium)(CVE-2021-44533)

Node.js did not handle multi-value Relative Distinguished Names correctly. Attackers could craft certificate subjects containing a single-value Relative Distinguished Name that would be interpreted as a multi-value Relative Distinguished Name, for example, in order to inject a Common Name that would allow bypassing the certificate subject verification.

Affected versions of Node.js do not accept multi-value Relative Distinguished Names and are thus not vulnerable to such attacks themselves. However, third-party code that uses node's ambiguous presentation of certificate subjects may be vulnerable.

More details will be available at [CVE-2021-44533](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2021-44533) after publication.

#### Prototype pollution via `console.table` properties (Low)(CVE-2022-21824)

Due to the formatting logic of the `console.table()` function it was not safe to allow user controlled input to be passed to the `properties` parameter while simultaneously passing a plain object with at least one property as the first parameter, which could be `__proto__`. The prototype pollution has very limited control, in that it only allows an empty string to be assigned numerical keys of the object prototype.

Versions of Node.js with the fix for this use a null protoype for the object these properties are being assigned to.

More details will be available at [CVE-2022-21824](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2022-21824) after publication.

Thanks to Patrik Oldsberg (rugvip) for reporting this vulnerability.

### Commits

- \[[`2a0515f73c`](https://github.com/nodejs/node/commit/2a0515f73c)] - **console**: fix prototype pollution via console.table (Tobias Nießen) [nodejs-private/node-private#307](https://github.com/nodejs-private/node-private/pull/307)
- \[[`2e2c45553d`](https://github.com/nodejs/node/commit/2e2c45553d)] - **crypto,tls**: implement safe x509 GeneralName format (Tobias Nießen) [nodejs-private/node-private#300](https://github.com/nodejs-private/node-private/pull/300)
- \[[`df3141f59b`](https://github.com/nodejs/node/commit/df3141f59b)] - **src**: add cve reverts and associated tests (Michael Dawson) [nodejs-private/node-private#300](https://github.com/nodejs-private/node-private/pull/300)
- \[[`5398548746`](https://github.com/nodejs/node/commit/5398548746)] - **src**: remove unused x509 functions (Tobias Nießen) [nodejs-private/node-private#300](https://github.com/nodejs-private/node-private/pull/300)
- \[[`1f7fdff64a`](https://github.com/nodejs/node/commit/1f7fdff64a)] - **tls**: fix handling of x509 subject and issuer (Tobias Nießen) [nodejs-private/node-private#300](https://github.com/nodejs-private/node-private/pull/300)
- \[[`b11b4cc69d`](https://github.com/nodejs/node/commit/b11b4cc69d)] - **tls**: drop support for URI alternative names (Tobias Nießen) [nodejs-private/node-private#300](https://github.com/nodejs-private/node-private/pull/300)

Windows 32-bit Installer: https://nodejs.org/dist/v17.3.1/node-v17.3.1-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v17.3.1/node-v17.3.1-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v17.3.1/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v17.3.1/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v17.3.1/node-v17.3.1.pkg \
macOS Apple Silicon 64-bit Binary: https://nodejs.org/dist/v17.3.1/node-v17.3.1-darwin-arm64.tar.gz \
macOS Intel 64-bit Binary: https://nodejs.org/dist/v17.3.1/node-v17.3.1-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v17.3.1/node-v17.3.1-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v17.3.1/node-v17.3.1-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v17.3.1/node-v17.3.1-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v17.3.1/node-v17.3.1-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v17.3.1/node-v17.3.1-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v17.3.1/node-v17.3.1-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v17.3.1/node-v17.3.1.tar.gz \
Other release files: https://nodejs.org/dist/v17.3.1/ \
Documentation: https://nodejs.org/docs/v17.3.1/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

499156d09a9f9d589af9da091c57ba545c33a1fac6915d0687c1b27dbe91d77e  node-v17.3.1-aix-ppc64.tar.gz
e664dd753777c813d893aad2b797847e2f0dc4c537cfefc377e3c88716934d38  node-v17.3.1-darwin-arm64.tar.gz
639bf260424f624c99710d4cf8ef2ee0a678306db49703a6a06c7f784d78861f  node-v17.3.1-darwin-arm64.tar.xz
a5d08b39a3f4af25c512247a2604eb84ffd41cbf66426d91df6ef165be94ae08  node-v17.3.1-darwin-x64.tar.gz
e59e7e75c86f04b911de6b3446e3053ffa77cef06a2680c4b4ef4d53b5330b2b  node-v17.3.1-darwin-x64.tar.xz
058e0a308bf6ddcb9d569ac60fd5f11a1cacdfae9fa3da9f7d6886dbbece78d1  node-v17.3.1-headers.tar.gz
b51d6f4d7c322d9342c4ca38cdb23e209f13458d277c1927c08230fae1094734  node-v17.3.1-headers.tar.xz
c9ca324368fdae2e8360cf7a22ce507c9931dc4bfe1e95723bd0fdfae616b31e  node-v17.3.1-linux-arm64.tar.gz
e9496ed8394868a90a04fbc3aa02c5893277d8cfa3d0b63fae085ca0fcc9f770  node-v17.3.1-linux-arm64.tar.xz
7f35d1fbed09ade6240edf2c1b44c715362b7aeeb280a182d449804486565bfd  node-v17.3.1-linux-armv7l.tar.gz
8c8c2d9328f8d3b0ace7f996f87ff72d9fae3b66861f6eecd1056e9378739693  node-v17.3.1-linux-armv7l.tar.xz
d9bb4e3889f42e7aea30c37014e499d29c4ce2c358bc5a55f8029b471f9217ec  node-v17.3.1-linux-ppc64le.tar.gz
7c3fedbba6d09e0a7d440b5715b0a2d649816f32ebed4db823dcccd4fe7c8a09  node-v17.3.1-linux-ppc64le.tar.xz
c2f27d37aa8478f8902f4c95b5b4b02768e43eb1496625742f04d1d1e960caec  node-v17.3.1-linux-s390x.tar.gz
7711c5b76cbd4a9e94947094b178e6f9c76284294524aa64e7015e034db70c73  node-v17.3.1-linux-s390x.tar.xz
7fd238a05ce8c98b19e6799103d12619f16bbab7111a6719f57b7ef190b74cfa  node-v17.3.1-linux-x64.tar.gz
1de7e77d8dd1c36189c0aec74240dc25a91f78ed2e101819f783313752a25523  node-v17.3.1-linux-x64.tar.xz
d44f2b8ec327ca57efa082af4adea7f50ba980d8f5591dae425e8320bb95f162  node-v17.3.1.pkg
34ed7eeb1fd088c59e2719c229dc9587c9f106b45329798ca3945c386ac824ed  node-v17.3.1.tar.gz
cf088f7854aa78d5aef7f9bc58bdb8d7342a0197ba24af62afd2fc3233f21d1c  node-v17.3.1.tar.xz
b99031dbc68c6c9bf81be874e4253c5dc455874466ffd6318543bb72d5a82ab8  node-v17.3.1-win-x64.7z
73115b94c12e854df87b0394fe3e450dcc3844837a31c808aacaddeeb06e9d62  node-v17.3.1-win-x64.zip
98affcaa4c1db124b676610b83afaa3df7c69508ea754828f1ab24cb75171336  node-v17.3.1-win-x86.7z
f9735240d8cb61e953ff7e59b6be5712531c97ac0c5e958c8764fcbdf5c7cc76  node-v17.3.1-win-x86.zip
ba7466dabe9608927b152408c19c57d6e68257c6020296042ed3600f1d00ddc6  node-v17.3.1-x64.msi
3cf1e7bbbd1b4d06071bed30589b5a68913fb42d2dad3a64f2edc559ab40eded  node-v17.3.1-x86.msi
87de0a524be1aac3c3c70a12162ccf778d9686ee7069d8ba47e75e0373d43075  win-x64/node.exe
281c2c43920b76ee23c32f110917b50b92977b15842dceada2ed84d7a29e8d34  win-x64/node.lib
bb5d76fe1d1067243cc77d2f072576e927a69eb14cfc2d9cee3481455d104319  win-x64/node_pdb.7z
a3e7d89d7d1563ef631bbd93238689a4fedf634d418bdc608d5bca0bb5147d3e  win-x64/node_pdb.zip
33c9e9370cd3bf360bc6ece904657bd9d35f5cb91dcde4b08eda50de18f8b0b6  win-x86/node.exe
9d79fc5845cf0e8602ea59057639cf2b3731058eb9ada1b8c3bd52638732f4d7  win-x86/node.lib
c32e0fb323c150f8ab2c24e91989c9fb68277de291a024e24b56e6e503ae3858  win-x86/node_pdb.7z
f6f1f07bc68abad03634b017f3309bb1377129092533ef420986f91535eed32d  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQEzBAEBCAAdFiEETtd49TnjY0x3nIfG1wYoSKGrAFwFAmHcx6YACgkQ1wYoSKGr
AFz7iQf+NjB6JNboKNSQb6GetJT5QEt89oisPl2vGHPPQ6Nsne36TnNhPXhf7xSj
d5tWmeKUDBxY3gBvzTGe3NP764Fhs4Nkv/6bAYUsx5llRfRH7CLgHbWXz8zOj55C
fSuO9O6egB7L8zi0xuPJGscur79lIEe90/vpvMp9fqL632kr7UG0RNwCtQr3nj69
/ZQ4UHn2JPM9Gnsn7FdUJUXK9TWh+7jQ+atzNFbtln2FXNXmL1dXaiAZATJ1bMqT
WQ8wwaFNx3iX0DGuSgi5WwEjcoDoHPqDTclrQgM8cg+5Zi1161TVasBvRAQb+WS1
9Cf5gIh2eYWjBAYAqC1dMtmLqPWtoQ==
=I3Sd
-----END PGP SIGNATURE-----

```
