---
date: '2018-11-15T21:20:56.211Z'
category: release
title: Node v11.2.0 (Current)
layout: blog-post
author: <PERSON><PERSON>
---

### Notable changes

- **deps**:
  - A new experimental HTTP parser (`llhttp`) is now supported. https://github.com/nodejs/node/pull/24059
- **timers**:
  - Fixed an issue that could cause setTimeout to stop working as expected. https://github.com/nodejs/node/pull/24322
- **Windows**
  - A crashing process will now show the names of stack frames if the node.pdb file is available. https://github.com/nodejs/node/pull/23822
  - Continued effort to improve the installer's new stage that installs
    native build tools. https://github.com/nodejs/node/pull/23987, https://github.com/nodejs/node/pull/24348
  - **child_process**:
    - On Windows the `windowsHide` option default was restored to `false`.
      This means `detached` child processes and GUI apps will once again
      start in a new window. https://github.com/nodejs/node/pull/24034
- **Added new collaborators**:
  - [oyyd](https://github.com/oyyd) - <PERSON><PERSON><PERSON>. https://github.com/nodejs/node/pull/24300
  - [psmarshall](https://github.com/psmarshall) - Peter Marshall. https://github.com/nodejs/node/pull/24170
  - [shisama](https://github.com/shisama) - Masashi Hirano. https://github.com/nodejs/node/pull/24136

### Commits

- [[`685724b53d`](https://github.com/nodejs/node/commit/685724b53d)] - **assert**: remove unused catch bindings (cjihrig) [#24079](https://github.com/nodejs/node/pull/24079)
- [[`bb766ae05a`](https://github.com/nodejs/node/commit/bb766ae05a)] - **async_hooks**: add HandleScopes to C++ embedder/addon API (Anna Henningsen) [#24285](https://github.com/nodejs/node/pull/24285)
- [[`ad5c9b4463`](https://github.com/nodejs/node/commit/ad5c9b4463)] - **benchmark**: support more options in startup benchmark (Joyee Cheung) [#24220](https://github.com/nodejs/node/pull/24220)
- [[`d0bf8c2259`](https://github.com/nodejs/node/commit/d0bf8c2259)] - **benchmark**: add dir and withFileTypes option readdir benchmarks (Joyee Cheung) [#24125](https://github.com/nodejs/node/pull/24125)
- [[`40b3ad3eb8`](https://github.com/nodejs/node/commit/40b3ad3eb8)] - **benchmark**: remove unused catch bindings (cjihrig) [#24079](https://github.com/nodejs/node/pull/24079)
- [[`1f3cb63da3`](https://github.com/nodejs/node/commit/1f3cb63da3)] - **bootstrap**: remove unused catch bindings (cjihrig) [#24079](https://github.com/nodejs/node/pull/24079)
- [[`fcc25f9ee8`](https://github.com/nodejs/node/commit/fcc25f9ee8)] - **buffer**: fix writeUInt16BE range check (Brian White) [#24208](https://github.com/nodejs/node/pull/24208)
- [[`e4cd255a85`](https://github.com/nodejs/node/commit/e4cd255a85)] - **buffer**: throw exception when creating from non-Node.js Context (Anna Henningsen) [#23938](https://github.com/nodejs/node/pull/23938)
- [[`44ebdbb860`](https://github.com/nodejs/node/commit/44ebdbb860)] - **build**: fix benchmark tests on CI (Rich Trott) [#24307](https://github.com/nodejs/node/pull/24307)
- [[`1c8b4d7c89`](https://github.com/nodejs/node/commit/1c8b4d7c89)] - **build**: disable openssl asm on arm64 for now (Ben Noordhuis) [#24270](https://github.com/nodejs/node/pull/24270)
- [[`0c9d86f58c`](https://github.com/nodejs/node/commit/0c9d86f58c)] - **build**: use BUILDTYPE in bench-addons-build targets (Daniel Bevenius) [#24033](https://github.com/nodejs/node/pull/24033)
- [[`70699ee09b`](https://github.com/nodejs/node/commit/70699ee09b)] - **build**: lint commit message in separate Travis job (Richard Lau) [#24254](https://github.com/nodejs/node/pull/24254)
- [[`2b282e8f20`](https://github.com/nodejs/node/commit/2b282e8f20)] - **build**: move headers out of c++ src section (Daniel Bevenius) [#24124](https://github.com/nodejs/node/pull/24124)
- [[`a8008d1517`](https://github.com/nodejs/node/commit/a8008d1517)] - **build**: only try to find node when it's needed by the target (Joyee Cheung) [#24115](https://github.com/nodejs/node/pull/24115)
- [[`e4bcb97024`](https://github.com/nodejs/node/commit/e4bcb97024)] - **build**: change repo to https protocol in Makefile (mritunjaygoutam12) [#24073](https://github.com/nodejs/node/pull/24073)
- [[`7083b96c49`](https://github.com/nodejs/node/commit/7083b96c49)] - **build**: use latest node on travis (cjihrig) [#24198](https://github.com/nodejs/node/pull/24198)
- [[`99c2a10f7b`](https://github.com/nodejs/node/commit/99c2a10f7b)] - **build**: fix Travis non-PR builds (Richard Lau) [#24093](https://github.com/nodejs/node/pull/24093)
- [[`3de1c5cadd`](https://github.com/nodejs/node/commit/3de1c5cadd)] - **build**: do not lint on non-PR Travis builds (Anna Henningsen) [#24076](https://github.com/nodejs/node/pull/24076)
- [[`762679efec`](https://github.com/nodejs/node/commit/762679efec)] - **build**: make benchmark/napi all prereq order-only (Daniel Bevenius) [#23951](https://github.com/nodejs/node/pull/23951)
- [[`4651cd721d`](https://github.com/nodejs/node/commit/4651cd721d)] - **build**: add -Werror=undefined-inline to clang builds (Refael Ackermann) [#23961](https://github.com/nodejs/node/pull/23961)
- [[`e7133f1e7c`](https://github.com/nodejs/node/commit/e7133f1e7c)] - **build**: configure default v8_optimized_debug (Refael Ackermann) [#23704](https://github.com/nodejs/node/pull/23704)
- [[`26c19889a8`](https://github.com/nodejs/node/commit/26c19889a8)] - **build,meta**: don't fail Travis for commit message (Refael Ackermann) [#23739](https://github.com/nodejs/node/pull/23739)
- [[`838fb550c6`](https://github.com/nodejs/node/commit/838fb550c6)] - **build,tools**: update make-v8.sh for s390x (Refael Ackermann) [#23839](https://github.com/nodejs/node/pull/23839)
- [[`c07cce368a`](https://github.com/nodejs/node/commit/c07cce368a)] - **_Revert_** "**child_process**: change windowsHide default to true" (Rich Trott) [#24034](https://github.com/nodejs/node/pull/24034)
- [[`a1c7c1902a`](https://github.com/nodejs/node/commit/a1c7c1902a)] - **child_process**: allow 'http_parser' monkey patching again (Jimb Esser) [#24006](https://github.com/nodejs/node/pull/24006)
- [[`4af63ee5d9`](https://github.com/nodejs/node/commit/4af63ee5d9)] - **child_process**: handle undefined/null for fork() args (Shobhit Chittora) [#22416](https://github.com/nodejs/node/pull/22416)
- [[`a2c13fac94`](https://github.com/nodejs/node/commit/a2c13fac94)] - **console**: console.timeLog() using the default label (Marie Terrier) [#24286](https://github.com/nodejs/node/pull/24286)
- [[`9e891327b7`](https://github.com/nodejs/node/commit/9e891327b7)] - **console**: cover .assert with single argument (Morgan Roderick) [#24188](https://github.com/nodejs/node/pull/24188)
- [[`2b48c7169a`](https://github.com/nodejs/node/commit/2b48c7169a)] - **crypto**: put legacy \_handle accessors on prototypes (Michaël Zasso) [#24269](https://github.com/nodejs/node/pull/24269)
- [[`f669817a5a`](https://github.com/nodejs/node/commit/f669817a5a)] - **(SEMVER-MINOR)** **crypto**: add support for chacha20-poly1305 for AEAD (chux0519) [#24081](https://github.com/nodejs/node/pull/24081)
- [[`ef69e7bcca`](https://github.com/nodejs/node/commit/ef69e7bcca)] - **deps**: backport 073073b4f1 from upstream V8 (Yang Guo) [#24274](https://github.com/nodejs/node/pull/24274)
- [[`a39493f4aa`](https://github.com/nodejs/node/commit/a39493f4aa)] - **deps**: cherry-pick b87d408 from upstream V8 (Peter Marshall) [#24272](https://github.com/nodejs/node/pull/24272)
- [[`9cefbba5d7`](https://github.com/nodejs/node/commit/9cefbba5d7)] - **deps**: patch V8 to 7.0.276.38 (Michaël Zasso) [#24271](https://github.com/nodejs/node/pull/24271)
- [[`115c57a240`](https://github.com/nodejs/node/commit/115c57a240)] - **deps**: introduce `llhttp` (Fedor Indutny) [#24059](https://github.com/nodejs/node/pull/24059)
- [[`566399ee31`](https://github.com/nodejs/node/commit/566399ee31)] - **deps**: patch V8 to 7.0.276.36 (Michaël Zasso) [#24109](https://github.com/nodejs/node/pull/24109)
- [[`3e1c53fe37`](https://github.com/nodejs/node/commit/3e1c53fe37)] - **deps**: cherry-pick 0483e9a from upstream V8 (Joyee Cheung) [#24125](https://github.com/nodejs/node/pull/24125)
- [[`6b697d4816`](https://github.com/nodejs/node/commit/6b697d4816)] - **deps**: c-ares float, version number patch (Ben Noordhuis) [#23854](https://github.com/nodejs/node/pull/23854)
- [[`07663694b4`](https://github.com/nodejs/node/commit/07663694b4)] - **deps**: upgrade to c-ares v1.15.0 (Ben Noordhuis) [#23854](https://github.com/nodejs/node/pull/23854)
- [[`eaea90b2ee`](https://github.com/nodejs/node/commit/eaea90b2ee)] - **deps**: remove old c-ares configure files (Ben Noordhuis) [#23854](https://github.com/nodejs/node/pull/23854)
- [[`f49b9e9c84`](https://github.com/nodejs/node/commit/f49b9e9c84)] - **deps**: patch V8 to 7.0.276.35 (Michaël Zasso) [#24056](https://github.com/nodejs/node/pull/24056)
- [[`6e4f238344`](https://github.com/nodejs/node/commit/6e4f238344)] - **deps,v8**: cherry-pick dc704497 (Refael Ackermann) [#23985](https://github.com/nodejs/node/pull/23985)
- [[`ef8b5b4094`](https://github.com/nodejs/node/commit/ef8b5b4094)] - **deps,v8**: fix gypfile bug (Refael Ackermann) [#23704](https://github.com/nodejs/node/pull/23704)
- [[`a01e829fb0`](https://github.com/nodejs/node/commit/a01e829fb0)] - **doc**: document http request.finished boolean (Thomas Watson) [#24319](https://github.com/nodejs/node/pull/24319)
- [[`36e4d0c6f0`](https://github.com/nodejs/node/commit/36e4d0c6f0)] - **doc**: document NODE_TLS_REJECT_UNAUTHORIZED (cjihrig) [#24289](https://github.com/nodejs/node/pull/24289)
- [[`fbd0b037ec`](https://github.com/nodejs/node/commit/fbd0b037ec)] - **doc**: clarify issues and pull requests guidance (Rich Trott) [#24316](https://github.com/nodejs/node/pull/24316)
- [[`a993a488fa`](https://github.com/nodejs/node/commit/a993a488fa)] - **doc**: fix comma splices in process.md (Rich Trott) [#24357](https://github.com/nodejs/node/pull/24357)
- [[`e584cc52fc`](https://github.com/nodejs/node/commit/e584cc52fc)] - **doc**: use real protocol names in ALPN example (Sam Roberts) [#24232](https://github.com/nodejs/node/pull/24232)
- [[`7fc910763e`](https://github.com/nodejs/node/commit/7fc910763e)] - **doc**: update core-validate-commit url (Daijiro Wachi) [#24331](https://github.com/nodejs/node/pull/24331)
- [[`39382ed4e1`](https://github.com/nodejs/node/commit/39382ed4e1)] - **doc**: fix echo example programs (Sam Roberts) [#24235](https://github.com/nodejs/node/pull/24235)
- [[`c68b0ae46e`](https://github.com/nodejs/node/commit/c68b0ae46e)] - **doc**: update fs.open() changes record for optional 'flags' (Rod Vagg) [#24240](https://github.com/nodejs/node/pull/24240)
- [[`6b7e69875d`](https://github.com/nodejs/node/commit/6b7e69875d)] - **doc**: add links to Stream section (Dmitry Igrishin) [#24301](https://github.com/nodejs/node/pull/24301)
- [[`82c64d0008`](https://github.com/nodejs/node/commit/82c64d0008)] - **doc**: correct async_hooks sample outputs (Gerhard Stoebich) [#24050](https://github.com/nodejs/node/pull/24050)
- [[`991d066338`](https://github.com/nodejs/node/commit/991d066338)] - **doc**: add oyyd to collaborators (Ouyang Yadong) [#24300](https://github.com/nodejs/node/pull/24300)
- [[`118d8d0feb`](https://github.com/nodejs/node/commit/118d8d0feb)] - **doc**: edit BUILDING.md (Rich Trott) [#24243](https://github.com/nodejs/node/pull/24243)
- [[`b5b5f9f8bd`](https://github.com/nodejs/node/commit/b5b5f9f8bd)] - **doc**: fix code examples in stream.md (Grant Carthew) [#24112](https://github.com/nodejs/node/pull/24112)
- [[`6ab46b5c47`](https://github.com/nodejs/node/commit/6ab46b5c47)] - **doc**: fix some inconsistent use of hostname (Sam Roberts) [#24199](https://github.com/nodejs/node/pull/24199)
- [[`1b81b348a3`](https://github.com/nodejs/node/commit/1b81b348a3)] - **doc**: describe what tls servername is for (Sam Roberts) [#24236](https://github.com/nodejs/node/pull/24236)
- [[`104b076d3d`](https://github.com/nodejs/node/commit/104b076d3d)] - **doc**: clarify allowed encoding parameter types (Sam Roberts) [#24230](https://github.com/nodejs/node/pull/24230)
- [[`7bcc4ccd8e`](https://github.com/nodejs/node/commit/7bcc4ccd8e)] - **doc**: remove legacy WPT integration guide (Joyee Cheung) [#24035](https://github.com/nodejs/node/pull/24035)
- [[`7cc3b9337f`](https://github.com/nodejs/node/commit/7cc3b9337f)] - **doc**: fix linting errors (cjihrig) [#24229](https://github.com/nodejs/node/pull/24229)
- [[`3ce71f7723`](https://github.com/nodejs/node/commit/3ce71f7723)] - **doc**: wrap GOVERNANCE.md at 80 characters (Rich Trott) [#24094](https://github.com/nodejs/node/pull/24094)
- [[`161be13602`](https://github.com/nodejs/node/commit/161be13602)] - **doc**: add text about error.code stability (Rich Trott) [#24090](https://github.com/nodejs/node/pull/24090)
- [[`2143b56b19`](https://github.com/nodejs/node/commit/2143b56b19)] - **doc**: update System Errors documentation (Rich Trott) [#24090](https://github.com/nodejs/node/pull/24090)
- [[`75ed7ce4a3`](https://github.com/nodejs/node/commit/75ed7ce4a3)] - **doc**: add psmarshall to collaborators (Peter Marshall) [#24170](https://github.com/nodejs/node/pull/24170)
- [[`f9f185a47b`](https://github.com/nodejs/node/commit/f9f185a47b)] - **doc**: add shisama to collaborators (Masashi Hirano) [#24136](https://github.com/nodejs/node/pull/24136)
- [[`d15270507e`](https://github.com/nodejs/node/commit/d15270507e)] - **doc**: implement minor text fixes to path.md (Rich Trott) [#24118](https://github.com/nodejs/node/pull/24118)
- [[`8642eacfff`](https://github.com/nodejs/node/commit/8642eacfff)] - **doc**: inspector security warning for changing host (Сковорода Никита Андреевич) [#23640](https://github.com/nodejs/node/pull/23640)
- [[`5ea48a92b2`](https://github.com/nodejs/node/commit/5ea48a92b2)] - **doc**: fix minor text issues in stream.md (Rich Trott) [#24116](https://github.com/nodejs/node/pull/24116)
- [[`6f54a7ace8`](https://github.com/nodejs/node/commit/6f54a7ace8)] - **doc**: streamline CONTRIBUTING.md (Rich Trott) [#24010](https://github.com/nodejs/node/pull/24010)
- [[`3b1044072c`](https://github.com/nodejs/node/commit/3b1044072c)] - **doc**: add table of contents to release guide (Michaël Zasso) [#24042](https://github.com/nodejs/node/pull/24042)
- [[`667ce42447`](https://github.com/nodejs/node/commit/667ce42447)] - **doc**: add missing comma in net documentation (Rich Trott) [#24074](https://github.com/nodejs/node/pull/24074)
- [[`1d6d384c02`](https://github.com/nodejs/node/commit/1d6d384c02)] - **doc**: correct link to test coverage command (mritunjaygoutam12) [#24049](https://github.com/nodejs/node/pull/24049)
- [[`94f73942ed`](https://github.com/nodejs/node/commit/94f73942ed)] - **doc**: fix socket.connecting description (Anna Henningsen) [#24066](https://github.com/nodejs/node/pull/24066)
- [[`fa84164de4`](https://github.com/nodejs/node/commit/fa84164de4)] - **doc**: add SECURITY.md to readme.md (warnerp18) [#24031](https://github.com/nodejs/node/pull/24031)
- [[`e8078f2693`](https://github.com/nodejs/node/commit/e8078f2693)] - **doc**: edit man page for superfluous "node" usage (Rich Trott) [#24029](https://github.com/nodejs/node/pull/24029)
- [[`a1b75d0cbf`](https://github.com/nodejs/node/commit/a1b75d0cbf)] - **doc**: fix dublication in net.createServer() docs (Ivan Filenko) [#24026](https://github.com/nodejs/node/pull/24026)
- [[`d729f3e198`](https://github.com/nodejs/node/commit/d729f3e198)] - **doc**: correct async_hooks resource names (Gerhard Stoebich) [#24001](https://github.com/nodejs/node/pull/24001)
- [[`7012f72a12`](https://github.com/nodejs/node/commit/7012f72a12)] - **doc**: address bits of proof reading work (Jagannath Bhat) [#23978](https://github.com/nodejs/node/pull/23978)
- [[`399d64b331`](https://github.com/nodejs/node/commit/399d64b331)] - **doc**: revise COLLABORATOR_GUIDE.md (Rich Trott) [#23990](https://github.com/nodejs/node/pull/23990)
- [[`879402b422`](https://github.com/nodejs/node/commit/879402b422)] - **doc**: simplify CODE_OF_CONDUCT.md (Rich Trott) [#23989](https://github.com/nodejs/node/pull/23989)
- [[`7b7155e90b`](https://github.com/nodejs/node/commit/7b7155e90b)] - **doc**: revise CHANGELOG.md text (Rich Trott) [#23988](https://github.com/nodejs/node/pull/23988)
- [[`b8a71bed02`](https://github.com/nodejs/node/commit/b8a71bed02)] - **doc**: improve COLLABORATOR_GUIDE (Jagannath Bhat) [#23977](https://github.com/nodejs/node/pull/23977)
- [[`846e450f51`](https://github.com/nodejs/node/commit/846e450f51)] - **doc**: improve BUILDING.md (Jagannath Bhat) [#23976](https://github.com/nodejs/node/pull/23976)
- [[`b182e2e8b2`](https://github.com/nodejs/node/commit/b182e2e8b2)] - **doc**: add types and their corresponding return values (Ouyang Yadong) [#23998](https://github.com/nodejs/node/pull/23998)
- [[`2d84f45d22`](https://github.com/nodejs/node/commit/2d84f45d22)] - **esm**: remove unused catch bindings (cjihrig) [#24079](https://github.com/nodejs/node/pull/24079)
- [[`5ecfc7cab7`](https://github.com/nodejs/node/commit/5ecfc7cab7)] - **events**: remove unused catch bindings (cjihrig) [#24079](https://github.com/nodejs/node/pull/24079)
- [[`f49b4fc794`](https://github.com/nodejs/node/commit/f49b4fc794)] - **fs**: replace pushValueToArray with pure C++ API (Joyee Cheung) [#24125](https://github.com/nodejs/node/pull/24125)
- [[`d59dc0d6bf`](https://github.com/nodejs/node/commit/d59dc0d6bf)] - **fs**: remove unused catch bindings (cjihrig) [#24079](https://github.com/nodejs/node/pull/24079)
- [[`424be28840`](https://github.com/nodejs/node/commit/424be28840)] - **fs**: handle result of access binding directly in fs.existsSync (Joyee Cheung) [#24015](https://github.com/nodejs/node/pull/24015)
- [[`2aa23cd433`](https://github.com/nodejs/node/commit/2aa23cd433)] - **(SEMVER-MINOR)** **fs,net**: standardize `pending` stream property (Anna Henningsen) [#24067](https://github.com/nodejs/node/pull/24067)
- [[`86aa27f85d`](https://github.com/nodejs/node/commit/86aa27f85d)] - **http**: remove pushValueToArray in Parser::CreateHeaders() (Joyee Cheung) [#24264](https://github.com/nodejs/node/pull/24264)
- [[`c2599a14de`](https://github.com/nodejs/node/commit/c2599a14de)] - **http**: remove obsolete function escapeHeaderValue (Lauri Piisang) [#24173](https://github.com/nodejs/node/pull/24173)
- [[`62fefd8aeb`](https://github.com/nodejs/node/commit/62fefd8aeb)] - **http2**: remove pushValueToArray in Http2Session::HandleOriginFrame (Joyee Cheung) [#24264](https://github.com/nodejs/node/pull/24264)
- [[`7ffbb1f55f`](https://github.com/nodejs/node/commit/7ffbb1f55f)] - **http2**: remove pushValueToArray in Http2Session::HandleHeadersFrame (Joyee Cheung) [#24264](https://github.com/nodejs/node/pull/24264)
- [[`4a0f27656d`](https://github.com/nodejs/node/commit/4a0f27656d)] - **(SEMVER-MINOR)** **http2**: add Http2Stream.bufferSize (Ouyang Yadong) [#23711](https://github.com/nodejs/node/pull/23711)
- [[`c7f471ab97`](https://github.com/nodejs/node/commit/c7f471ab97)] - **http2**: improve http2 code a bit (James M Snell) [#23984](https://github.com/nodejs/node/pull/23984)
- [[`4dbcf7ac1f`](https://github.com/nodejs/node/commit/4dbcf7ac1f)] - **inspector**: remove unused catch bindings (cjihrig) [#24079](https://github.com/nodejs/node/pull/24079)
- [[`5ca0cf7ae6`](https://github.com/nodejs/node/commit/5ca0cf7ae6)] - **lib**: improved conditional check in zlib (Dan Corman) [#24190](https://github.com/nodejs/node/pull/24190)
- [[`d8d93442aa`](https://github.com/nodejs/node/commit/d8d93442aa)] - **lib**: adjust params from uvExceptionWithHostPort (msmichellegar) [#24159](https://github.com/nodejs/node/pull/24159)
- [[`6f80a45528`](https://github.com/nodejs/node/commit/6f80a45528)] - **lib**: combine contructor, tag, Object into a function (Paul Isache) [#24171](https://github.com/nodejs/node/pull/24171)
- [[`1225a0af09`](https://github.com/nodejs/node/commit/1225a0af09)] - **lib**: add crypto dependant modules cannotUseCache (Daniel Bevenius) [#24100](https://github.com/nodejs/node/pull/24100)
- [[`453c96ed2d`](https://github.com/nodejs/node/commit/453c96ed2d)] - **lib**: move process prototype manipulation into setupProcessObject (Joyee Cheung) [#24089](https://github.com/nodejs/node/pull/24089)
- [[`6a6b036042`](https://github.com/nodejs/node/commit/6a6b036042)] - **lib**: move internalBinding whitelisting into loaders.js (Joyee Cheung) [#24088](https://github.com/nodejs/node/pull/24088)
- [[`b4c8158459`](https://github.com/nodejs/node/commit/b4c8158459)] - **lib**: fix grammar error and make it clearer for comments (MaleDong) [#23799](https://github.com/nodejs/node/pull/23799)
- [[`879c0f1f3e`](https://github.com/nodejs/node/commit/879c0f1f3e)] - **lib**: move module exports proxy into a separate method (Joyee Cheung) [#24057](https://github.com/nodejs/node/pull/24057)
- [[`874393bfd0`](https://github.com/nodejs/node/commit/874393bfd0)] - **lib**: remove useless getLibuvNow in internal/timers (ZYSzys) [#23995](https://github.com/nodejs/node/pull/23995)
- [[`7ee0cea028`](https://github.com/nodejs/node/commit/7ee0cea028)] - **lib**: make coverage work for Node.js (Benjamin) [#23941](https://github.com/nodejs/node/pull/23941)
- [[`b3f3ebf3b3`](https://github.com/nodejs/node/commit/b3f3ebf3b3)] - **lib**: repl multiline history support (Anto Aravinth) [#22153](https://github.com/nodejs/node/pull/22153)
- [[`55adc25968`](https://github.com/nodejs/node/commit/55adc25968)] - **(SEMVER-MINOR)** **lib**: enable TypedArray and DataView for the v8 module (Ouyang Yadong) [#23953](https://github.com/nodejs/node/pull/23953)
- [[`5ff1e67ff7`](https://github.com/nodejs/node/commit/5ff1e67ff7)] - **lib**: fix code cache generation (Joyee Cheung) [#23855](https://github.com/nodejs/node/pull/23855)
- [[`164f2444a0`](https://github.com/nodejs/node/commit/164f2444a0)] - **lib**: remove useless cwd in posix.resolve (ZYSzys) [#23902](https://github.com/nodejs/node/pull/23902)
- [[`10156c612d`](https://github.com/nodejs/node/commit/10156c612d)] - **meta,doc**: ping community about new release (Refael Ackermann) [#24064](https://github.com/nodejs/node/pull/24064)
- [[`1dd8191515`](https://github.com/nodejs/node/commit/1dd8191515)] - **(SEMVER-MINOR)** **module**: support multi-dot file extension (Geoffrey Booth) [#23416](https://github.com/nodejs/node/pull/23416)
- [[`72204d114f`](https://github.com/nodejs/node/commit/72204d114f)] - **n-api**: add missing handle scopes (Daniel Bevenius) [#24011](https://github.com/nodejs/node/pull/24011)
- [[`10edc4f186`](https://github.com/nodejs/node/commit/10edc4f186)] - **net**: always invoke after-write callback (Anna Henningsen) [#24291](https://github.com/nodejs/node/pull/24291)
- [[`753f706858`](https://github.com/nodejs/node/commit/753f706858)] - **net**: add comments explaining error check (Steven Gabarro) [#24222](https://github.com/nodejs/node/pull/24222)
- [[`c53117e7ea`](https://github.com/nodejs/node/commit/c53117e7ea)] - **net**: remove unreachable check in internalConnect (Philipp Dunkel) [#24158](https://github.com/nodejs/node/pull/24158)
- [[`74451263a3`](https://github.com/nodejs/node/commit/74451263a3)] - **net**: partially revert "simplify Socket.prototype.\_final" (Anna Henningsen) [#24288](https://github.com/nodejs/node/pull/24288)
- [[`636e4e02a5`](https://github.com/nodejs/node/commit/636e4e02a5)] - **net**: simplify Socket.prototype.\_final (Anna Henningsen) [#24075](https://github.com/nodejs/node/pull/24075)
- [[`cd227eb791`](https://github.com/nodejs/node/commit/cd227eb791)] - **net**: `net.Server.listen()` avoid operations on `null` when fail (Ouyang Yadong) [#23920](https://github.com/nodejs/node/pull/23920)
- [[`293983a112`](https://github.com/nodejs/node/commit/293983a112)] - **os**: do not call into JS to push values to an array in GetCPUInfo (Joyee Cheung) [#24264](https://github.com/nodejs/node/pull/24264)
- [[`ccc3bb73db`](https://github.com/nodejs/node/commit/ccc3bb73db)] - **process**: remove pushValueToArray in GetActiveHandles (Joyee Cheung) [#24264](https://github.com/nodejs/node/pull/24264)
- [[`ba4337d77d`](https://github.com/nodejs/node/commit/ba4337d77d)] - **process**: remove pushValueToArray in GetActiveRequests (Joyee Cheung) [#24264](https://github.com/nodejs/node/pull/24264)
- [[`e5888462f6`](https://github.com/nodejs/node/commit/e5888462f6)] - **process**: remove pushValueToArray in EnvEnumerator (Joyee Cheung) [#24264](https://github.com/nodejs/node/pull/24264)
- [[`52468b33f7`](https://github.com/nodejs/node/commit/52468b33f7)] - **querystring**: remove unused catch bindings (cjihrig) [#24079](https://github.com/nodejs/node/pull/24079)
- [[`213b6293fc`](https://github.com/nodejs/node/commit/213b6293fc)] - **repl**: remove unused catch bindings (cjihrig) [#24079](https://github.com/nodejs/node/pull/24079)
- [[`e27f43201c`](https://github.com/nodejs/node/commit/e27f43201c)] - **repl**: use promise#finally (Weijia Wang) [#23971](https://github.com/nodejs/node/pull/23971)
- [[`b7aded3300`](https://github.com/nodejs/node/commit/b7aded3300)] - **src**: compile native modules and their code cache in C++ (Joyee Cheung) [#24221](https://github.com/nodejs/node/pull/24221)
- [[`92a8cbe87a`](https://github.com/nodejs/node/commit/92a8cbe87a)] - **src**: enable detailed source positions in V8 (Yang Guo) [#24274](https://github.com/nodejs/node/pull/24274)
- [[`f8ed673308`](https://github.com/nodejs/node/commit/f8ed673308)] - **src**: remove pushValueToArray and SetupProcessObject (Joyee Cheung) [#24264](https://github.com/nodejs/node/pull/24264)
- [[`7601cdfe8b`](https://github.com/nodejs/node/commit/7601cdfe8b)] - **src**: bundle persistent-to-local methods as class (Gabriel Schulhof) [#24276](https://github.com/nodejs/node/pull/24276)
- [[`f5945c9279`](https://github.com/nodejs/node/commit/f5945c9279)] - **src**: sort internal binding list (cjihrig) [#24292](https://github.com/nodejs/node/pull/24292)
- [[`e1c792919e`](https://github.com/nodejs/node/commit/e1c792919e)] - **src**: fix v8 compiler warnings in src (Daniel Bevenius) [#24246](https://github.com/nodejs/node/pull/24246)
- [[`81f4fb2b3b`](https://github.com/nodejs/node/commit/81f4fb2b3b)] - **src**: reuse std::make_unique (alyssaq) [#24132](https://github.com/nodejs/node/pull/24132)
- [[`a9053c38ea`](https://github.com/nodejs/node/commit/a9053c38ea)] - **src**: cache the result of GetOptions() in JS land (Joyee Cheung) [#24091](https://github.com/nodejs/node/pull/24091)
- [[`17e80eca95`](https://github.com/nodejs/node/commit/17e80eca95)] - **src**: prefer param function check over args length (Shelley Vohr) [#23835](https://github.com/nodejs/node/pull/23835)
- [[`1cda9b3988`](https://github.com/nodejs/node/commit/1cda9b3988)] - **src**: fix Set() usage in env-inl.h (cjihrig) [#24060](https://github.com/nodejs/node/pull/24060)
- [[`bef1c3b748`](https://github.com/nodejs/node/commit/bef1c3b748)] - **src**: fix Set() usage in node.h (cjihrig) [#24060](https://github.com/nodejs/node/pull/24060)
- [[`2a93882498`](https://github.com/nodejs/node/commit/2a93882498)] - **src**: fix Get() usage in tls_wrap.cc (cjihrig) [#24060](https://github.com/nodejs/node/pull/24060)
- [[`9437aaad26`](https://github.com/nodejs/node/commit/9437aaad26)] - **src**: fix Get() usage in async_wrap.cc (cjihrig) [#24060](https://github.com/nodejs/node/pull/24060)
- [[`cb7d9f9980`](https://github.com/nodejs/node/commit/cb7d9f9980)] - **src**: move error handling code into node_errors.cc (Joyee Cheung) [#24058](https://github.com/nodejs/node/pull/24058)
- [[`fdba226d13`](https://github.com/nodejs/node/commit/fdba226d13)] - **src**: fix compiler warning for debug build (Daniel Bevenius) [#23994](https://github.com/nodejs/node/pull/23994)
- [[`84e5807b1e`](https://github.com/nodejs/node/commit/84e5807b1e)] - **src**: fix CreatePlatform header param mismatch (Shelley Vohr) [#23947](https://github.com/nodejs/node/pull/23947)
- [[`38b0525bc3`](https://github.com/nodejs/node/commit/38b0525bc3)] - **src**: use v8:: for consistency in util (ZYSzys) [#23934](https://github.com/nodejs/node/pull/23934)
- [[`90872c4c6e`](https://github.com/nodejs/node/commit/90872c4c6e)] - **src**: fix fully-static & large-pages combination (Suresh Srinivas) [#23964](https://github.com/nodejs/node/pull/23964)
- [[`063b40edc1`](https://github.com/nodejs/node/commit/063b40edc1)] - **src**: use "constants" string instead of creating new one (Ouyang Yadong) [#23894](https://github.com/nodejs/node/pull/23894)
- [[`24b18645b9`](https://github.com/nodejs/node/commit/24b18645b9)] - **src,win**: informative stack traces (Refael Ackermann) [#23822](https://github.com/nodejs/node/pull/23822)
- [[`13dee430cd`](https://github.com/nodejs/node/commit/13dee430cd)] - **stream**: make `.destroy()` interact better with write queue (Anna Henningsen) [#24062](https://github.com/nodejs/node/pull/24062)
- [[`d6bcf8b98b`](https://github.com/nodejs/node/commit/d6bcf8b98b)] - **(SEMVER-MINOR)** **stream**: add auto-destroy mode (Mathias Buus) [#22795](https://github.com/nodejs/node/pull/22795)
- [[`2593b40f5c`](https://github.com/nodejs/node/commit/2593b40f5c)] - **test**: compare objects not identical by reference (Marie Terrier) [#24189](https://github.com/nodejs/node/pull/24189)
- [[`eeb5cc6305`](https://github.com/nodejs/node/commit/eeb5cc6305)] - **test**: add typeerror for vm/compileFunction params (Dan Corman) [#24179](https://github.com/nodejs/node/pull/24179)
- [[`dc26247e69`](https://github.com/nodejs/node/commit/dc26247e69)] - **test**: deep object to table not covered (Osmond van Hemert) [#24257](https://github.com/nodejs/node/pull/24257)
- [[`29a29f7f97`](https://github.com/nodejs/node/commit/29a29f7f97)] - **test**: add tests for Socket.setNoDelay (James Herrington) [#24250](https://github.com/nodejs/node/pull/24250)
- [[`aa800b097a`](https://github.com/nodejs/node/commit/aa800b097a)] - **test**: assert diff no color (Florin-Daniel BÎLBÎE) [#24181](https://github.com/nodejs/node/pull/24181)
- [[`b6d2819b93`](https://github.com/nodejs/node/commit/b6d2819b93)] - **test**: add process no deprecation (razvanbh) [#24196](https://github.com/nodejs/node/pull/24196)
- [[`dd9864b8d7`](https://github.com/nodejs/node/commit/dd9864b8d7)] - **test**: check for invalid module type in vm.js (alyssaq) [#24161](https://github.com/nodejs/node/pull/24161)
- [[`957ceaabe6`](https://github.com/nodejs/node/commit/957ceaabe6)] - **test**: fix flaky test-vm-timeout-escape-queuemicrotask (Rich Trott) [#24296](https://github.com/nodejs/node/pull/24296)
- [[`89c3388a77`](https://github.com/nodejs/node/commit/89c3388a77)] - **test**: fix arguments order in assertions (Emanuel Kluge) [#24149](https://github.com/nodejs/node/pull/24149)
- [[`ea5d1841af`](https://github.com/nodejs/node/commit/ea5d1841af)] - **test**: remove unused parameters in function definition (Paul Hodgson) [#24268](https://github.com/nodejs/node/pull/24268)
- [[`cb4c2dd33e`](https://github.com/nodejs/node/commit/cb4c2dd33e)] - **test**: esm loader unknown builtin module (Fran Herrero) [#24183](https://github.com/nodejs/node/pull/24183)
- [[`1a86499947`](https://github.com/nodejs/node/commit/1a86499947)] - **test**: fixed order of actual and expected arguments (kiyomizumia) [#24178](https://github.com/nodejs/node/pull/24178)
- [[`77163a9dee`](https://github.com/nodejs/node/commit/77163a9dee)] - **test**: add else and error case for TextDecoder (Lauri Piisang) [#24162](https://github.com/nodejs/node/pull/24162)
- [[`e5e9c6427b`](https://github.com/nodejs/node/commit/e5e9c6427b)] - **test**: dgram socket prints deprecation warnings (Robert Pamely) [#24177](https://github.com/nodejs/node/pull/24177)
- [[`366529654e`](https://github.com/nodejs/node/commit/366529654e)] - **test**: url format path ending hashchar not covered (Osmond van Hemert) [#24259](https://github.com/nodejs/node/pull/24259)
- [[`0a104ef33c`](https://github.com/nodejs/node/commit/0a104ef33c)] - **test**: test add and remove for lib/domain (Petar Dodev) [#24163](https://github.com/nodejs/node/pull/24163)
- [[`fe7ef1ad11`](https://github.com/nodejs/node/commit/fe7ef1ad11)] - **test**: add test for autoDestroy in stream (Daijiro Wachi) [#24127](https://github.com/nodejs/node/pull/24127)
- [[`02e9fa01f3`](https://github.com/nodejs/node/commit/02e9fa01f3)] - **test**: fix args order in process-getactiverequests (Vladyslav Kopylash) [#24186](https://github.com/nodejs/node/pull/24186)
- [[`f805db3620`](https://github.com/nodejs/node/commit/f805db3620)] - **test**: check control characters replacing (Alessandro Gatti) [#24182](https://github.com/nodejs/node/pull/24182)
- [[`75e4f7db40`](https://github.com/nodejs/node/commit/75e4f7db40)] - **test**: fix strictEqual argument order (Martin Kask) [#24153](https://github.com/nodejs/node/pull/24153)
- [[`09a8f4713d`](https://github.com/nodejs/node/commit/09a8f4713d)] - **test**: correct order of args in assert.strictEqual() (Natalie Cluer) [#24157](https://github.com/nodejs/node/pull/24157)
- [[`c83b650a10`](https://github.com/nodejs/node/commit/c83b650a10)] - **test**: add tests for process.initgroups (James Herrington) [#24154](https://github.com/nodejs/node/pull/24154)
- [[`762bb94d72`](https://github.com/nodejs/node/commit/762bb94d72)] - **test**: add test case for completion bash flag (Aivo Paas) [#24168](https://github.com/nodejs/node/pull/24168)
- [[`afcfdec289`](https://github.com/nodejs/node/commit/afcfdec289)] - **test**: add test for deepEqual Float32Array (Yehiyam Livneh) [#24164](https://github.com/nodejs/node/pull/24164)
- [[`b02eed5e3b`](https://github.com/nodejs/node/commit/b02eed5e3b)] - **test**: fix arguments order in assert.strictEqual() (Ulises Santana Suárez) [#24192](https://github.com/nodejs/node/pull/24192)
- [[`768425f21a`](https://github.com/nodejs/node/commit/768425f21a)] - **test**: fix assert.strictEqual argument order (John Mc Quillan) [#24172](https://github.com/nodejs/node/pull/24172)
- [[`26c625c3d2`](https://github.com/nodejs/node/commit/26c625c3d2)] - **test**: fix v8 Set/Get compiler warnings (Daniel Bevenius) [#24246](https://github.com/nodejs/node/pull/24246)
- [[`beb0800ab3`](https://github.com/nodejs/node/commit/beb0800ab3)] - **test**: move benchmark tests out of main test suite (Rich Trott) [#24265](https://github.com/nodejs/node/pull/24265)
- [[`883519679e`](https://github.com/nodejs/node/commit/883519679e)] - **test**: replacing fixture directory with temp (saurabhSiddhu) [#24077](https://github.com/nodejs/node/pull/24077)
- [[`ddbd0e1973`](https://github.com/nodejs/node/commit/ddbd0e1973)] - **test**: increase coverage internal readline (Berry de Witte) [#24150](https://github.com/nodejs/node/pull/24150)
- [[`56cd911cad`](https://github.com/nodejs/node/commit/56cd911cad)] - **test**: use NULL instead of 0 in common.h (Daniel Bevenius) [#24104](https://github.com/nodejs/node/pull/24104)
- [[`a05f2fc46b`](https://github.com/nodejs/node/commit/a05f2fc46b)] - **test**: move test-fs-watch-system-limit from sequential to pummel (Marcus Scott) [#23692](https://github.com/nodejs/node/pull/23692)
- [[`9af7ad592c`](https://github.com/nodejs/node/commit/9af7ad592c)] - **test**: fix uses of deprecated assert.fail with multiple args (ivan.filenko) [#23673](https://github.com/nodejs/node/pull/23673)
- [[`2b0410a3ef`](https://github.com/nodejs/node/commit/2b0410a3ef)] - **test**: use assert.strictEqual instead of assert.equal (ivan.filenko) [#23673](https://github.com/nodejs/node/pull/23673)
- [[`825f0dda5b`](https://github.com/nodejs/node/commit/825f0dda5b)] - **test**: add test for strictDeepEqual (Nikita Malyschkin) [#24197](https://github.com/nodejs/node/pull/24197)
- [[`b16e485910`](https://github.com/nodejs/node/commit/b16e485910)] - **test**: add coverage for systemerror set name (Amer Alimanović) [#24200](https://github.com/nodejs/node/pull/24200)
- [[`bc97b62f35`](https://github.com/nodejs/node/commit/bc97b62f35)] - **test**: fix order of arguments in assert.strictEqual (Alex Seifert) [#24145](https://github.com/nodejs/node/pull/24145)
- [[`4a69d218b6`](https://github.com/nodejs/node/commit/4a69d218b6)] - **test**: add test for 'ERR_INVALID_CALLBACK' (razvanbh) [#24224](https://github.com/nodejs/node/pull/24224)
- [[`8b0626c836`](https://github.com/nodejs/node/commit/8b0626c836)] - **test**: add coverage for escape key switch case (Artur Daschevici) [#24194](https://github.com/nodejs/node/pull/24194)
- [[`92d2d7917f`](https://github.com/nodejs/node/commit/92d2d7917f)] - **test**: fix NewFromUtf8 compiler warning (Daniel Bevenius) [#24216](https://github.com/nodejs/node/pull/24216)
- [[`0c4facfbaf`](https://github.com/nodejs/node/commit/0c4facfbaf)] - **test**: change arguments order in strictEqual (Paul Isache) [#24156](https://github.com/nodejs/node/pull/24156)
- [[`2baa59b897`](https://github.com/nodejs/node/commit/2baa59b897)] - **test**: switch order of strictEqual arguments (Jonah Polack) [#24185](https://github.com/nodejs/node/pull/24185)
- [[`c8d8e5cf2c`](https://github.com/nodejs/node/commit/c8d8e5cf2c)] - **test**: fix the arguments order in `assert.strictEqual` (mzucker) [#24227](https://github.com/nodejs/node/pull/24227)
- [[`4245cbbf49`](https://github.com/nodejs/node/commit/4245cbbf49)] - **test**: fix the arguments order in `assert.strictEqual` (mzucker) [#24226](https://github.com/nodejs/node/pull/24226)
- [[`be40fd1e50`](https://github.com/nodejs/node/commit/be40fd1e50)] - **test**: fix order in assert.strictEqual to actual, expected (Kevin Seidel) [#24184](https://github.com/nodejs/node/pull/24184)
- [[`a1f5179e09`](https://github.com/nodejs/node/commit/a1f5179e09)] - **test**: fix arguments order in assert.strictEqual (szabolcsit) [#24143](https://github.com/nodejs/node/pull/24143)
- [[`5510bec3cc`](https://github.com/nodejs/node/commit/5510bec3cc)] - **test**: fix assert argument order (Manish Poddar) [#24160](https://github.com/nodejs/node/pull/24160)
- [[`e46b8edb58`](https://github.com/nodejs/node/commit/e46b8edb58)] - **test**: add error code tests in dgram test (Mark Arranz) [#24215](https://github.com/nodejs/node/pull/24215)
- [[`6076ccf90d`](https://github.com/nodejs/node/commit/6076ccf90d)] - **test**: fix order of arguments in test-delayed-require assertion (reineke-fox) [#24165](https://github.com/nodejs/node/pull/24165)
- [[`989c2aaf83`](https://github.com/nodejs/node/commit/989c2aaf83)] - **test**: fix flaky test-vm-timeout-escape-nexttick (Rich Trott) [#24251](https://github.com/nodejs/node/pull/24251)
- [[`b68734b66a`](https://github.com/nodejs/node/commit/b68734b66a)] - **test**: initialize test/wpt to run URL and console .js tests (Joyee Cheung) [#24035](https://github.com/nodejs/node/pull/24035)
- [[`c973551eca`](https://github.com/nodejs/node/commit/c973551eca)] - **test**: use URL fixtures under test/fixtures/wpt/url/resources (Joyee Cheung) [#24035](https://github.com/nodejs/node/pull/24035)
- [[`3f935d74e0`](https://github.com/nodejs/node/commit/3f935d74e0)] - **test**: remove WPT tests that are now .any.js in the upstream (Joyee Cheung) [#24035](https://github.com/nodejs/node/pull/24035)
- [[`121a3f8855`](https://github.com/nodejs/node/commit/121a3f8855)] - **test**: use git node wpt to pull WPT into test/fixtures (Joyee Cheung) [#24035](https://github.com/nodejs/node/pull/24035)
- [[`317901174c`](https://github.com/nodejs/node/commit/317901174c)] - **test**: fix arguments order in test-fs-write-buffer (razvanbh) [#24155](https://github.com/nodejs/node/pull/24155)
- [[`9b3c2e5054`](https://github.com/nodejs/node/commit/9b3c2e5054)] - **test**: fix argument order in assert.strictEqual() (Clement) [#24147](https://github.com/nodejs/node/pull/24147)
- [[`2d87ce3d8b`](https://github.com/nodejs/node/commit/2d87ce3d8b)] - **test**: switch arguments in strictEqual (Mathieu Pavageau) [#24141](https://github.com/nodejs/node/pull/24141)
- [[`6c8b128fcc`](https://github.com/nodejs/node/commit/6c8b128fcc)] - **test**: fix arguments order (Simona Cotin) [#24151](https://github.com/nodejs/node/pull/24151)
- [[`3d19a04b51`](https://github.com/nodejs/node/commit/3d19a04b51)] - **test**: fixe argument order in assert.strictEqual (Marc Posth) [#24140](https://github.com/nodejs/node/pull/24140)
- [[`a0681b7211`](https://github.com/nodejs/node/commit/a0681b7211)] - **test**: removed extraneous argument 's' (Jackson Chui) [#24213](https://github.com/nodejs/node/pull/24213)
- [[`12429812bc`](https://github.com/nodejs/node/commit/12429812bc)] - **test**: fixing arguments order in `assert.strictEqual()` (G. Carcaci) [#24152](https://github.com/nodejs/node/pull/24152)
- [[`fc494cdb16`](https://github.com/nodejs/node/commit/fc494cdb16)] - **test**: add tests for OutgoingMessage setTimeout (Robin Drexler) [#24148](https://github.com/nodejs/node/pull/24148)
- [[`ce124aca65`](https://github.com/nodejs/node/commit/ce124aca65)] - **test**: swap expected and actual in assert.strictEqual (Florin-Daniel BÎLBÎE) [#24146](https://github.com/nodejs/node/pull/24146)
- [[`737f897b51`](https://github.com/nodejs/node/commit/737f897b51)] - **test**: fix assert parameter order (Roland Broekema) [#24144](https://github.com/nodejs/node/pull/24144)
- [[`d85161cbfd`](https://github.com/nodejs/node/commit/d85161cbfd)] - **test**: change order of assert.strictEqual() (Remy Parzinski) [#24142](https://github.com/nodejs/node/pull/24142)
- [[`fb58ada9dd`](https://github.com/nodejs/node/commit/fb58ada9dd)] - **test**: fix invalid argument order in test-http-expect-continue.js (Morgan Roderick) [#24138](https://github.com/nodejs/node/pull/24138)
- [[`7cc0a46e85`](https://github.com/nodejs/node/commit/7cc0a46e85)] - **test**: strictEqual argument order (actual, expected) (Ahmad Nassri) [#24137](https://github.com/nodejs/node/pull/24137)
- [[`a5ac7b94ea`](https://github.com/nodejs/node/commit/a5ac7b94ea)] - **test**: fixed the arguments order in `assert.strictEqual` (mzucker) [#24135](https://github.com/nodejs/node/pull/24135)
- [[`71545e6284`](https://github.com/nodejs/node/commit/71545e6284)] - **test**: swap the order of arguments (Musa Hamwala) [#24134](https://github.com/nodejs/node/pull/24134)
- [[`a8908f16f7`](https://github.com/nodejs/node/commit/a8908f16f7)] - **test**: fs readfile, swap arguments in strictEqual (Petar Dodev) [#24133](https://github.com/nodejs/node/pull/24133)
- [[`7c04fe07a6`](https://github.com/nodejs/node/commit/7c04fe07a6)] - **test**: fix arguments order (Fran Herrero) [#24131](https://github.com/nodejs/node/pull/24131)
- [[`6f80a5eeda`](https://github.com/nodejs/node/commit/6f80a5eeda)] - **test**: http-client-timeout error assert arguments (Tadhg Creedon) [#24130](https://github.com/nodejs/node/pull/24130)
- [[`415fcded15`](https://github.com/nodejs/node/commit/415fcded15)] - **test**: fix flaky VM timeout test on Raspberry Pi (Rich Trott) [#24238](https://github.com/nodejs/node/pull/24238)
- [[`a2e2c91cfa`](https://github.com/nodejs/node/commit/a2e2c91cfa)] - **test**: disable color formating for test-internal-errors.js (Refael Ackermann) [#24204](https://github.com/nodejs/node/pull/24204)
- [[`a35bcd5ef5`](https://github.com/nodejs/node/commit/a35bcd5ef5)] - **test**: remove unused catch bindings (cjihrig) [#24079](https://github.com/nodejs/node/pull/24079)
- [[`9bf36bc6c3`](https://github.com/nodejs/node/commit/9bf36bc6c3)] - **test**: add a test for `tls.Socket` with `allowHalfOpen` (Ouyang Yadong) [#23866](https://github.com/nodejs/node/pull/23866)
- [[`8a3836ec72`](https://github.com/nodejs/node/commit/8a3836ec72)] - **test**: add crypto check to test-benchmark-http2 (Daniel Bevenius) [#24096](https://github.com/nodejs/node/pull/24096)
- [[`b86a89b9ad`](https://github.com/nodejs/node/commit/b86a89b9ad)] - **test**: increase --stack_size test-async-wrap-pop (Daniel Bevenius) [#23996](https://github.com/nodejs/node/pull/23996)
- [[`1b97dbd6b5`](https://github.com/nodejs/node/commit/1b97dbd6b5)] - **test**: assert that invalidcmd throws error code (Jerome Covington) [#23942](https://github.com/nodejs/node/pull/23942)
- [[`63778b7ae1`](https://github.com/nodejs/node/commit/63778b7ae1)] - **test**: fix strictEqual arguments order (Esteban Sotillo) [#23956](https://github.com/nodejs/node/pull/23956)
- [[`dccf4a6c38`](https://github.com/nodejs/node/commit/dccf4a6c38)] - **test**: add property for RangeError in test-buffer-copy (mritunjaygoutam12) [#23968](https://github.com/nodejs/node/pull/23968)
- [[`8bffd90933`](https://github.com/nodejs/node/commit/8bffd90933)] - **test**: fix test-fs-watch-system-limit (Ali Ijaz Sheikh) [#23986](https://github.com/nodejs/node/pull/23986)
- [[`7a2134c414`](https://github.com/nodejs/node/commit/7a2134c414)] - **test**: run code cache test by default and test generator (Joyee Cheung) [#23855](https://github.com/nodejs/node/pull/23855)
- [[`5b9ef11e35`](https://github.com/nodejs/node/commit/5b9ef11e35)] - **timers**: fix priority queue removeAt (Anatoli Papirovski) [#24322](https://github.com/nodejs/node/pull/24322)
- [[`d6f91ba139`](https://github.com/nodejs/node/commit/d6f91ba139)] - **(SEMVER-MINOR)** **tls**: get the local certificate after tls handshake (Sam Roberts) [#24261](https://github.com/nodejs/node/pull/24261)
- [[`ad72e40e5b`](https://github.com/nodejs/node/commit/ad72e40e5b)] - **tools**: update ESLint to 5.9.0 (cjihrig) [#24280](https://github.com/nodejs/node/pull/24280)
- [[`6fdc5d9c9a`](https://github.com/nodejs/node/commit/6fdc5d9c9a)] - **tools**: enable 80-char line length markdown linting (Rich Trott) [#24094](https://github.com/nodejs/node/pull/24094)
- [[`b3c163f11b`](https://github.com/nodejs/node/commit/b3c163f11b)] - **tools**: lint for unused catch bindings (cjihrig) [#24079](https://github.com/nodejs/node/pull/24079)
- [[`1541c7f401`](https://github.com/nodejs/node/commit/1541c7f401)] - **tools**: add script to lint first PR commit message (Richard Lau) [#24030](https://github.com/nodejs/node/pull/24030)
- [[`4d7fbc3e0f`](https://github.com/nodejs/node/commit/4d7fbc3e0f)] - **tools**: update alternative docs versions (Richard Lau) [#23980](https://github.com/nodejs/node/pull/23980)
- [[`8de1030a70`](https://github.com/nodejs/node/commit/8de1030a70)] - **tracing**: fix static destruction order issue (Anna Henningsen) [#24123](https://github.com/nodejs/node/pull/24123)
- [[`0063448b04`](https://github.com/nodejs/node/commit/0063448b04)] - **url**: make the context non-enumerable (Joyee Cheung) [#24218](https://github.com/nodejs/node/pull/24218)
- [[`953697a7b8`](https://github.com/nodejs/node/commit/953697a7b8)] - **util**: deleted unreachable code from util.inspect (kiyomizumia) [#24187](https://github.com/nodejs/node/pull/24187)
- [[`fb7c1b3e81`](https://github.com/nodejs/node/commit/fb7c1b3e81)] - **v8_prof_polyfill**: remove unused catch bindings (cjihrig) [#24079](https://github.com/nodejs/node/pull/24079)
- [[`9c15124aa8`](https://github.com/nodejs/node/commit/9c15124aa8)] - **vm**: clarify timeout option in vm (Vladimir de Turckheim) [#23512](https://github.com/nodejs/node/pull/23512)
- [[`2331181410`](https://github.com/nodejs/node/commit/2331181410)] - **vm**: allow `cachedData` to also be TypedArray|DataView (Benjamin Chen) [#22921](https://github.com/nodejs/node/pull/22921)
- [[`4709fe676d`](https://github.com/nodejs/node/commit/4709fe676d)] - **win**: add customization warning to tools script (João Reis) [#24348](https://github.com/nodejs/node/pull/24348)
- [[`57a2b957de`](https://github.com/nodejs/node/commit/57a2b957de)] - **win**: add prompt to tools installation script (João Reis) [#23987](https://github.com/nodejs/node/pull/23987)
- [[`df1ca0fd82`](https://github.com/nodejs/node/commit/df1ca0fd82)] - **win**: clarify Boxstarter behavior on install tools (Rob Reynolds) [#23987](https://github.com/nodejs/node/pull/23987)

Windows 32-bit Installer: https://nodejs.org/dist/v11.2.0/node-v11.2.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v11.2.0/node-v11.2.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v11.2.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v11.2.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v11.2.0/node-v11.2.0.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v11.2.0/node-v11.2.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v11.2.0/node-v11.2.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v11.2.0/node-v11.2.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v11.2.0/node-v11.2.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v11.2.0/node-v11.2.0-aix-ppc64.tar.gz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v11.2.0/node-v11.2.0-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v11.2.0/node-v11.2.0-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v11.2.0/node-v11.2.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v11.2.0/node-v11.2.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v11.2.0/node-v11.2.0.tar.gz \
Other release files: https://nodejs.org/dist/v11.2.0/ \
Documentation: https://nodejs.org/docs/v11.2.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

f593158350894ba43ec665ca04ea89ac72585aafef8db35e39895862d86f1935  node-v11.2.0-aix-ppc64.tar.gz
cd17fafcdb21ae80fb94e455e63567c70b3bceedf982b93ae9e4aea411f6a6ea  node-v11.2.0-darwin-x64.tar.gz
1d968dd44c97e9cd73dc74aed0597a1d1cc4d74b3acf1d8e7cb58a6e165e28f0  node-v11.2.0-darwin-x64.tar.xz
12231b9323685deb78cf4640a2524377ac63451baa0abd4980c75031706c777b  node-v11.2.0-headers.tar.gz
6cd5ae892b0c8151f2738eddfc27a4d4846e0ccf22f04781c3929cd2461e6266  node-v11.2.0-headers.tar.xz
dda16cf93a365e5f853ca29dfc7be8866881558a44fd751ff5843015f4ad93ff  node-v11.2.0-linux-arm64.tar.gz
3e6c47a29b85030b4305bdc2e15cbe37e1b54a2338ce1790637384ce349ed1fd  node-v11.2.0-linux-arm64.tar.xz
b9d1c40321163c222f3c38e4593b2f316566d02e7c2854d1bcd5e24035076aa1  node-v11.2.0-linux-armv6l.tar.gz
70540d5d4a946ea800994af24b456171842ac1b31980b7ca95cd05d2693d09c4  node-v11.2.0-linux-armv6l.tar.xz
342b94a8b59558bcbb4d7dea53cdf8b1fd9ae9dcf769acbf0b9a9bba4530d865  node-v11.2.0-linux-armv7l.tar.gz
b825005280d50d1de7299e461d96cfd1b5e6c4bae15acac6f981803c8d07ecf6  node-v11.2.0-linux-armv7l.tar.xz
9deb2ab8590a26703df3c16a724aa061b5fd62f31dd6e8d336b082ea4fa8eded  node-v11.2.0-linux-ppc64le.tar.gz
bdde717c64d13160e9525a4c0dd43c1bc7bc89a8e0b994f150a58116de08f6cb  node-v11.2.0-linux-ppc64le.tar.xz
5f4dff065f6573d09db16cf119dc590b951b8360a714151e9b0319b7b1b67391  node-v11.2.0-linux-s390x.tar.gz
844d45d346aa53fbd5d61392cededb82e923f50c20904ef172c6783a05ef541d  node-v11.2.0-linux-s390x.tar.xz
2528f860cb0d33fe2f807f56dac347730f32772b9f987f8ec379af9277cae71f  node-v11.2.0-linux-x64.tar.gz
629a603443c275f8ecd851994b81a9c02a36343dbcbafc279cbecb9ccd9cf906  node-v11.2.0-linux-x64.tar.xz
e5fc79d4a840913f53b6ff6be66f6c2eec23a9ebe2c606bb690b32d984226ffc  node-v11.2.0.pkg
f4db74f788bcd11240042ab4133ccf814ac0c541cf330f0104d92291bdf257f9  node-v11.2.0-sunos-x64.tar.gz
8b2560008f530cba6761aa0266cea423c13f4e55abc4879d15d6f4e15da984d0  node-v11.2.0-sunos-x64.tar.xz
2766fea693bc7a4750feef16d3c109df44d4319d4763678d60a5e8f177d0fa9e  node-v11.2.0.tar.gz
354ee26f20e63ebfbf7bfe4e94cc0bbaa4840a8a3787c564054efb3214379fa6  node-v11.2.0.tar.xz
3079f2cc30d442171295fa02c621fdbaaa78ac33e3d18fa944e8246043aa7fcc  node-v11.2.0-win-x64.7z
3440b2880b4e3b78c9c18865df263317e7d3c3e179dad960d526004dc7e6ba9a  node-v11.2.0-win-x64.zip
0d0d7db24bd5e2f423b1b8b5a0185c5de5743bf3826ba9e2e15ff2eccdeaf1e1  node-v11.2.0-win-x86.7z
39e1b82d48fd06170a76e6c3de9419b4752f35ad1f3a73bad19ab5505f33ecfd  node-v11.2.0-win-x86.zip
d00d4d31a61e2903617de8f9bb69312dee8f3a475aca4e5483e68995c761a170  node-v11.2.0-x64.msi
c9ed92040f436d9a1fd6dcb7edb2cc5214134e803fd1989a270c8f378ff6c64f  node-v11.2.0-x86.msi
57506f7936eb6997e71094546d9ff129b4df73911baefd7f194d7311aa607573  win-x64/node.exe
a1fa4507ed3e9488994bed6ecaf2db750c98de1471f8cd12a7e171e5afcb2126  win-x64/node.lib
fd9d3fb89a7a991a31f650ae0819b4f7472d0e8fc742ed3c5755a4b60bbe5ce4  win-x64/node_pdb.7z
3d646db7a0c798e4a6032abe2dd51476536b3abbfc378669bef2db94c02c1573  win-x64/node_pdb.zip
4d97129d22bddd7d27b0e9115e2990253218e878d997cca3f124279f902e97de  win-x86/node.exe
f0402a224ca37793e9377c2964bfe850a0c1e1cf2df4ff1f15f233f452f2c707  win-x86/node.lib
46fba38a10dc90986c30bdf4d74ac58806a02234bd3d1e5ab4a80335f99978fc  win-x86/node_pdb.7z
840c0e318be29dd76609e3fe0d15b96e72fbfc1bea3f6adf148960130af48187  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEj8yhP+8dDC6RAI4Jdw96mlrhVgAFAlvt4qQACgkQdw96mlrh
VgDR8g//XfqZ4iUe6bm16bqa3kKtHd28KwnNR30RoNKVQHEPBmWKaUDsot0bYXcv
3TOMqNWNgwdl9vfQtY5iKTZIlk1F+A3fN5UWw51cksWLzhnqw1mZnogc69SkVfhP
55oqR2R1QqbEaP4UMn7H40ciWfC3IkqMulwSYQvg6ugk+c/dlQA3rGZpcurJx9KF
O5eTsG9MCrbI84HeiRMARVVjZ38WBRmPk0WBpl7zJNSptHZZ+xa6dy/ImoLylivV
Z2ufMoqem+RdxDgD+6ncQ+V7rNYaU5TCOehDvuMcdfKXEmFzPvrlkx2JnYFXs58k
vThso1xP+G0TWvwT9fjupw6dRmzTvuAQeSklQRBu5puxsgzTssVgFFeq4R4Pz90e
tnKaX0qEuxL/RxW42qj4PirzyOpOyUy8yoyisyTL4f9SiAqN7ksjy9kJz7Qu94Q0
CJQw7loi7cbFwHeKAS5JYOQRU5k92J8lQRItzD3IGME35N0Mko0qRARDZp1uBXOf
hOz//TB17//WWyXi9HFJpx7mBTEM2hHYzB8AQrlCf/DTWF3LO7bCJaz286zdhVmK
S9BQQ8SK5m1Hrb88mkYqPYbmTiiGHReyvi9c9uRSGkIaGHyDentpDMZ4v1tuMOXY
wK+weuceuSAgx1TGkFG314/Ae9O2zZ1vGcERHvIF35GqAsBvx70=
=xDiR
-----END PGP SIGNATURE-----

```
