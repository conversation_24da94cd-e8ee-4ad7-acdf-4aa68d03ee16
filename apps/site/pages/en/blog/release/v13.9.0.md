---
date: '2020-02-18T20:58:12.491Z'
category: release
title: Node v13.9.0 (Current)
layout: blog-post
author: <PERSON>
---

### Notable changes

- **async_hooks**
  - add executionAsyncResource (<PERSON>) [#30959](https://github.com/nodejs/node/pull/30959)
- **crypto**
  - add crypto.di<PERSON><PERSON><PERSON> (<PERSON>) [#31178](https://github.com/nodejs/node/pull/31178)
  - add DH support to generateKeyPair (<PERSON>) [#31178](https://github.com/nodejs/node/pull/31178)
  - simplify DH groups (<PERSON>) [#31178](https://github.com/nodejs/node/pull/31178)
  - add key type 'dh' (<PERSON>) [#31178](https://github.com/nodejs/node/pull/31178)
- **test**
  - skip keygen tests on arm systems (<PERSON>) [#31178](https://github.com/nodejs/node/pull/31178)
- **perf_hooks**
  - add property flags to GCPerformanceEntry (<PERSON><PERSON>) [#29547](https://github.com/nodejs/node/pull/29547)
- **process**
  - report ArrayBuffer memory in `memoryUsage()` (Anna Henningsen) [#31550](https://github.com/nodejs/node/pull/31550)
- **readline**
  - make tab size configurable (Ruben Bridgewater) [#31318](https://github.com/nodejs/node/pull/31318)
- **report**
  - add support for Workers (Anna Henningsen) [#31386](https://github.com/nodejs/node/pull/31386)
- **worker**
  - add ability to take heap snapshot from parent thread (Anna Henningsen) [#31569](https://github.com/nodejs/node/pull/31569)
- **added new collaborators**
  - add ronag to collaborators (Robert Nagy) [#31498](https://github.com/nodejs/node/pull/31498)

### Commits

- [[`2db7593838`](https://github.com/nodejs/node/commit/2db7593838)] - **assert**: align character indicators properly (Ruben Bridgewater) [#31429](https://github.com/nodejs/node/pull/31429)
- [[`a840e9d639`](https://github.com/nodejs/node/commit/a840e9d639)] - **async_hooks**: ensure event after been emitted on runInAsyncScope (legendecas) [#31784](https://github.com/nodejs/node/pull/31784)
- [[`6be51296e4`](https://github.com/nodejs/node/commit/6be51296e4)] - **(SEMVER-MINOR)** **async_hooks**: add executionAsyncResource (Matteo Collina) [#30959](https://github.com/nodejs/node/pull/30959)
- [[`2de085fe93`](https://github.com/nodejs/node/commit/2de085fe93)] - **benchmark**: use let instead of var (Daniele Belardi) [#31592](https://github.com/nodejs/node/pull/31592)
- [[`e37f5100e5`](https://github.com/nodejs/node/commit/e37f5100e5)] - **benchmark**: swap var for let in benchmarks (Alex Ramirez) [#28958](https://github.com/nodejs/node/pull/28958)
- [[`819fb76ba5`](https://github.com/nodejs/node/commit/819fb76ba5)] - **_Revert_** "**benchmark**: refactor helper into a class" (Anna Henningsen) [#31722](https://github.com/nodejs/node/pull/31722)
- [[`8974fa794c`](https://github.com/nodejs/node/commit/8974fa794c)] - **_Revert_** "**benchmark**: add `test` and `all` options and improve errors" (Anna Henningsen) [#31722](https://github.com/nodejs/node/pull/31722)
- [[`30f55cebb6`](https://github.com/nodejs/node/commit/30f55cebb6)] - **_Revert_** "**benchmark**: remove special test entries" (Anna Henningsen) [#31722](https://github.com/nodejs/node/pull/31722)
- [[`1484f5ab6e`](https://github.com/nodejs/node/commit/1484f5ab6e)] - **benchmark**: remove special test entries (Ruben Bridgewater) [#31396](https://github.com/nodejs/node/pull/31396)
- [[`ca343caee3`](https://github.com/nodejs/node/commit/ca343caee3)] - **benchmark**: add `test` and `all` options and improve errors (Ruben Bridgewater) [#31396](https://github.com/nodejs/node/pull/31396)
- [[`9f2c742626`](https://github.com/nodejs/node/commit/9f2c742626)] - **benchmark**: refactor helper into a class (Ruben Bridgewater) [#31396](https://github.com/nodejs/node/pull/31396)
- [[`161db608ae`](https://github.com/nodejs/node/commit/161db608ae)] - **benchmark**: check for and fix multiple end() (Brian White) [#31624](https://github.com/nodejs/node/pull/31624)
- [[`6fe8eda3ca`](https://github.com/nodejs/node/commit/6fe8eda3ca)] - **benchmark**: clean up config resolution in multiple benchmarks (Denys Otrishko) [#31581](https://github.com/nodejs/node/pull/31581)
- [[`ebdcafafeb`](https://github.com/nodejs/node/commit/ebdcafafeb)] - **benchmark**: add MessagePort benchmark (Anna Henningsen) [#31568](https://github.com/nodejs/node/pull/31568)
- [[`eb3c6e9127`](https://github.com/nodejs/node/commit/eb3c6e9127)] - **benchmark**: use let and const instead of var (Daniele Belardi) [#31518](https://github.com/nodejs/node/pull/31518)
- [[`b29badad81`](https://github.com/nodejs/node/commit/b29badad81)] - **benchmark**: fix getStringWidth() benchmark (Rich Trott) [#31476](https://github.com/nodejs/node/pull/31476)
- [[`519134ddb0`](https://github.com/nodejs/node/commit/519134ddb0)] - **buffer**: improve from() performance (Brian White) [#31615](https://github.com/nodejs/node/pull/31615)
- [[`769154de07`](https://github.com/nodejs/node/commit/769154de07)] - **buffer**: improve concat() performance (Brian White) [#31522](https://github.com/nodejs/node/pull/31522)
- [[`9d45393e95`](https://github.com/nodejs/node/commit/9d45393e95)] - **buffer**: improve fill(number) performance (Brian White) [#31489](https://github.com/nodejs/node/pull/31489)
- [[`60a69770f5`](https://github.com/nodejs/node/commit/60a69770f5)] - **build**: add configure option to debug only Node.js part of the binary (Anna Henningsen) [#31644](https://github.com/nodejs/node/pull/31644)
- [[`10f9abe81d`](https://github.com/nodejs/node/commit/10f9abe81d)] - **build**: ignore all the "Debug","Release" folders (ConorDavenport) [#31565](https://github.com/nodejs/node/pull/31565)
- [[`03eade01d7`](https://github.com/nodejs/node/commit/03eade01d7)] - **build**: enable loading internal modules from disk (Gus Caplan) [#31321](https://github.com/nodejs/node/pull/31321)
- [[`a2b7006847`](https://github.com/nodejs/node/commit/a2b7006847)] - **build**: build docs in GitHub Actions CI workflow (Richard Lau) [#31504](https://github.com/nodejs/node/pull/31504)
- [[`2e216aebcb`](https://github.com/nodejs/node/commit/2e216aebcb)] - **build**: do not use setup-node in build workflows (Richard Lau) [#31349](https://github.com/nodejs/node/pull/31349)
- [[`825d089763`](https://github.com/nodejs/node/commit/825d089763)] - **crypto**: fix performance regression (Robert Nagy) [#31742](https://github.com/nodejs/node/pull/31742)
- [[`3c6545f0b4`](https://github.com/nodejs/node/commit/3c6545f0b4)] - **crypto**: improve randomBytes() performance (Brian White) [#31519](https://github.com/nodejs/node/pull/31519)
- [[`f84b34d42c`](https://github.com/nodejs/node/commit/f84b34d42c)] - **crypto**: improve errors in DiffieHellmanGroup (Tobias Nießen) [#31445](https://github.com/nodejs/node/pull/31445)
- [[`4591202e66`](https://github.com/nodejs/node/commit/4591202e66)] - **crypto**: assign and use ERR_CRYPTO_UNKNOWN_CIPHER (Tobias Nießen) [#31437](https://github.com/nodejs/node/pull/31437)
- [[`bf46c304dd`](https://github.com/nodejs/node/commit/bf46c304dd)] - **(SEMVER-MINOR)** **crypto**: add crypto.diffieHellman (Tobias Nießen) [#31178](https://github.com/nodejs/node/pull/31178)
- [[`0d3e095941`](https://github.com/nodejs/node/commit/0d3e095941)] - **(SEMVER-MINOR)** **crypto**: add DH support to generateKeyPair (Tobias Nießen) [#31178](https://github.com/nodejs/node/pull/31178)
- [[`15bd2c9f0c`](https://github.com/nodejs/node/commit/15bd2c9f0c)] - **(SEMVER-MINOR)** **crypto**: simplify DH groups (Tobias Nießen) [#31178](https://github.com/nodejs/node/pull/31178)
- [[`572322fddf`](https://github.com/nodejs/node/commit/572322fddf)] - **(SEMVER-MINOR)** **crypto**: add key type 'dh' (Tobias Nießen) [#31178](https://github.com/nodejs/node/pull/31178)
- [[`0ac124b6b9`](https://github.com/nodejs/node/commit/0ac124b6b9)] - **deps**: upgrade npm to 6.13.7 (Michael Perrotte) [#31558](https://github.com/nodejs/node/pull/31558)
- [[`bf7097c77d`](https://github.com/nodejs/node/commit/bf7097c77d)] - **deps**: switch to chromium's zlib implementation (Brian White) [#31201](https://github.com/nodejs/node/pull/31201)
- [[`2eeaa5ce40`](https://github.com/nodejs/node/commit/2eeaa5ce40)] - **deps**: uvwasi: cherry-pick 7b5b6f9 (cjihrig) [#31495](https://github.com/nodejs/node/pull/31495)
- [[`464f4afa66`](https://github.com/nodejs/node/commit/464f4afa66)] - **deps**: upgrade to libuv 1.34.2 (cjihrig) [#31477](https://github.com/nodejs/node/pull/31477)
- [[`9811ebe0c5`](https://github.com/nodejs/node/commit/9811ebe0c5)] - **deps**: uvwasi: cherry-pick eea4508 (cjihrig) [#31432](https://github.com/nodejs/node/pull/31432)
- [[`2fe0ed3a2e`](https://github.com/nodejs/node/commit/2fe0ed3a2e)] - **deps**: uvwasi: cherry-pick c3bef8e (cjihrig) [#31432](https://github.com/nodejs/node/pull/31432)
- [[`09566be899`](https://github.com/nodejs/node/commit/09566be899)] - **deps**: uvwasi: cherry-pick ea73af5 (cjihrig) [#31432](https://github.com/nodejs/node/pull/31432)
- [[`04f2799ed2`](https://github.com/nodejs/node/commit/04f2799ed2)] - **deps**: update to uvwasi 0.0.5 (cjihrig) [#31432](https://github.com/nodejs/node/pull/31432)
- [[`7c4f1ed030`](https://github.com/nodejs/node/commit/7c4f1ed030)] - **deps**: uvwasi: cherry-pick 941bedf (cjihrig) [#31363](https://github.com/nodejs/node/pull/31363)
- [[`00e38a749a`](https://github.com/nodejs/node/commit/00e38a749a)] - **deps**: port uvwasi@676ba9a to gyp (cjihrig) [#31363](https://github.com/nodejs/node/pull/31363)
- [[`5bd3f6c258`](https://github.com/nodejs/node/commit/5bd3f6c258)] - **deps,test**: update to uvwasi 0.0.4 (cjihrig) [#31363](https://github.com/nodejs/node/pull/31363)
- [[`2cd8461e56`](https://github.com/nodejs/node/commit/2cd8461e56)] - **doc**: add glossary.md (gengjiawen) [#27517](https://github.com/nodejs/node/pull/27517)
- [[`c4613c6b8b`](https://github.com/nodejs/node/commit/c4613c6b8b)] - **doc**: add prerequisites information for Arch (Ujjwal Sharma) [#31669](https://github.com/nodejs/node/pull/31669)
- [[`b35f83e69b`](https://github.com/nodejs/node/commit/b35f83e69b)] - **doc**: fix typo on fs docs (Juan José Arboleda) [#31620](https://github.com/nodejs/node/pull/31620)
- [[`2ff812ca84`](https://github.com/nodejs/node/commit/2ff812ca84)] - **doc**: update contact email for @ryzokuken (Ujjwal Sharma) [#31670](https://github.com/nodejs/node/pull/31670)
- [[`2c83946757`](https://github.com/nodejs/node/commit/2c83946757)] - **doc**: fix default server timeout description for https (Andrey Pechkurov) [#31692](https://github.com/nodejs/node/pull/31692)
- [[`b56a21fdad`](https://github.com/nodejs/node/commit/b56a21fdad)] - **doc**: add directions to mark a release line as lts (Danielle Adams) [#31724](https://github.com/nodejs/node/pull/31724)
- [[`5ae40cd2b2`](https://github.com/nodejs/node/commit/5ae40cd2b2)] - **doc**: expand C++ README with information about exception handling (Anna Henningsen) [#31720](https://github.com/nodejs/node/pull/31720)
- [[`94a0ec1b99`](https://github.com/nodejs/node/commit/94a0ec1b99)] - **doc**: update foundation name in onboarding (Tobias Nießen) [#31719](https://github.com/nodejs/node/pull/31719)
- [[`fda97fa772`](https://github.com/nodejs/node/commit/fda97fa772)] - **doc**: reword possessive form of Node.js in zlib.md (Rich Trott) [#31713](https://github.com/nodejs/node/pull/31713)
- [[`eea58cd3d5`](https://github.com/nodejs/node/commit/eea58cd3d5)] - **doc**: reword possessive form of Node.js in modules.md (Rich Trott) [#31713](https://github.com/nodejs/node/pull/31713)
- [[`d0238190a1`](https://github.com/nodejs/node/commit/d0238190a1)] - **doc**: reword possessive form of Node.js in repl.md (Rich Trott) [#31713](https://github.com/nodejs/node/pull/31713)
- [[`55a25b3bbe`](https://github.com/nodejs/node/commit/55a25b3bbe)] - **doc**: reword section title in addons.md (Rich Trott) [#31713](https://github.com/nodejs/node/pull/31713)
- [[`ba9fae058a`](https://github.com/nodejs/node/commit/ba9fae058a)] - **doc**: revise deepEqual() legacy assertion mode text (Rich Trott) [#31704](https://github.com/nodejs/node/pull/31704)
- [[`f6d78f959f`](https://github.com/nodejs/node/commit/f6d78f959f)] - **doc**: improve strict assertion mode color text (Rich Trott) [#31703](https://github.com/nodejs/node/pull/31703)
- [[`22cf3e3d4e`](https://github.com/nodejs/node/commit/22cf3e3d4e)] - **doc**: consolidate introductory text (Rich Trott) [#31667](https://github.com/nodejs/node/pull/31667)
- [[`1e2327d9e6`](https://github.com/nodejs/node/commit/1e2327d9e6)] - **doc**: simplify async_hooks overview (Rich Trott) [#31660](https://github.com/nodejs/node/pull/31660)
- [[`77ec381ea2`](https://github.com/nodejs/node/commit/77ec381ea2)] - **doc**: clarify Worker exit/message event ordering (Anna Henningsen) [#31642](https://github.com/nodejs/node/pull/31642)
- [[`4b0085c7e3`](https://github.com/nodejs/node/commit/4b0085c7e3)] - **doc**: update TSC name in "Release Process" (Tobias Nießen) [#31652](https://github.com/nodejs/node/pull/31652)
- [[`2e6c737281`](https://github.com/nodejs/node/commit/2e6c737281)] - **doc**: remove .github/ISSUE_TEMPLATE.md in favor of the template folder (Joyee Cheung) [#31656](https://github.com/nodejs/node/pull/31656)
- [[`b61b85ccf9`](https://github.com/nodejs/node/commit/b61b85ccf9)] - **doc**: add note in BUILDING.md about running `make distclean` (Swagat Konchada) [#31542](https://github.com/nodejs/node/pull/31542)
- [[`2991e7c0e3`](https://github.com/nodejs/node/commit/2991e7c0e3)] - **doc**: correct getting an ArrayBuffer's length (tsabolov) [#31632](https://github.com/nodejs/node/pull/31632)
- [[`e27f24987e`](https://github.com/nodejs/node/commit/e27f24987e)] - **doc**: ask more questions in the bug report template (Joyee Cheung) [#31611](https://github.com/nodejs/node/pull/31611)
- [[`b50a6cc54d`](https://github.com/nodejs/node/commit/b50a6cc54d)] - **doc**: add example to fs.promises.readdir (Conor ONeill) [#31552](https://github.com/nodejs/node/pull/31552)
- [[`1dbe765b0b`](https://github.com/nodejs/node/commit/1dbe765b0b)] - **doc**: add AsyncResource + Worker pool example (Anna Henningsen) [#31601](https://github.com/nodejs/node/pull/31601)
- [[`f40264980e`](https://github.com/nodejs/node/commit/f40264980e)] - **doc**: fix numbering (Steffen) [#31575](https://github.com/nodejs/node/pull/31575)
- [[`3ba0a22c57`](https://github.com/nodejs/node/commit/3ba0a22c57)] - **doc**: clarify socket.setNoDelay() explanation (Rusty Conover) [#31541](https://github.com/nodejs/node/pull/31541)
- [[`faec87b7f1`](https://github.com/nodejs/node/commit/faec87b7f1)] - **doc**: list largepage values in --help (cjihrig) [#31537](https://github.com/nodejs/node/pull/31537)
- [[`2638110cce`](https://github.com/nodejs/node/commit/2638110cce)] - **doc**: clarify require() OS independence (Denys Otrishko) [#31571](https://github.com/nodejs/node/pull/31571)
- [[`7fe9d5ebd4`](https://github.com/nodejs/node/commit/7fe9d5ebd4)] - **doc**: add protocol option in http2.connect() (Michael Lumish) [#31560](https://github.com/nodejs/node/pull/31560)
- [[`6626c4de3c`](https://github.com/nodejs/node/commit/6626c4de3c)] - **doc**: clarify that `v8.serialize()` is not deterministic (Anna Henningsen) [#31548](https://github.com/nodejs/node/pull/31548)
- [[`cde4b51a92`](https://github.com/nodejs/node/commit/cde4b51a92)] - **doc**: update job reference in COLLABORATOR_GUIDE.md (Richard Lau) [#31557](https://github.com/nodejs/node/pull/31557)
- [[`4cac2cccd6`](https://github.com/nodejs/node/commit/4cac2cccd6)] - **doc**: simultaneous blog and email of sec announce (Sam Roberts) [#31483](https://github.com/nodejs/node/pull/31483)
- [[`e2b3e4e0e3`](https://github.com/nodejs/node/commit/e2b3e4e0e3)] - **doc**: update collaborator guide citgm instructions (Robert Nagy) [#31549](https://github.com/nodejs/node/pull/31549)
- [[`43186e0046`](https://github.com/nodejs/node/commit/43186e0046)] - **doc**: change error message testing policy (Tobias Nießen) [#31421](https://github.com/nodejs/node/pull/31421)
- [[`a52df55b9a`](https://github.com/nodejs/node/commit/a52df55b9a)] - **doc**: remove redundant properties from headers (XhmikosR) [#31492](https://github.com/nodejs/node/pull/31492)
- [[`04d783ae71`](https://github.com/nodejs/node/commit/04d783ae71)] - **doc**: update maintaining-V8.md (kenzo-spaulding) [#31503](https://github.com/nodejs/node/pull/31503)
- [[`f75fe9ab71`](https://github.com/nodejs/node/commit/f75fe9ab71)] - **doc**: enable visual code indication in headers (Rich Trott) [#31493](https://github.com/nodejs/node/pull/31493)
- [[`8f25e51e4e`](https://github.com/nodejs/node/commit/8f25e51e4e)] - **doc**: clean up and streamline vm.md examples (Denys Otrishko) [#31474](https://github.com/nodejs/node/pull/31474)
- [[`729b96137e`](https://github.com/nodejs/node/commit/729b96137e)] - **doc**: further fix async iterator example (Robert Nagy) [#31367](https://github.com/nodejs/node/pull/31367)
- [[`15b24b71ce`](https://github.com/nodejs/node/commit/15b24b71ce)] - **doc**: add ronag to collaborators (Robert Nagy) [#31498](https://github.com/nodejs/node/pull/31498)
- [[`e9462b4d44`](https://github.com/nodejs/node/commit/e9462b4d44)] - **doc**: fix code display in header glitch (Rich Trott) [#31460](https://github.com/nodejs/node/pull/31460)
- [[`b1c745877b`](https://github.com/nodejs/node/commit/b1c745877b)] - **doc**: fix syntax in N-API documentation (Tobias Nießen) [#31466](https://github.com/nodejs/node/pull/31466)
- [[`67d8967f98`](https://github.com/nodejs/node/commit/67d8967f98)] - **doc**: add explanatory to path.resolve description (Yakov Litvin) [#31430](https://github.com/nodejs/node/pull/31430)
- [[`1099524452`](https://github.com/nodejs/node/commit/1099524452)] - **doc**: document process.std\*.fd (Harshitha KP) [#31395](https://github.com/nodejs/node/pull/31395)
- [[`843c5c6f46`](https://github.com/nodejs/node/commit/843c5c6f46)] - **doc**: fix several child_process doc typos (cjihrig) [#31393](https://github.com/nodejs/node/pull/31393)
- [[`d77099856a`](https://github.com/nodejs/node/commit/d77099856a)] - **doc**: fix a broken link in fs.md (himself65) [#31373](https://github.com/nodejs/node/pull/31373)
- [[`1e08d3c2f1`](https://github.com/nodejs/node/commit/1e08d3c2f1)] - **doc**: correct added version for --abort-on-uncaught-exception (Anna Henningsen) [#31360](https://github.com/nodejs/node/pull/31360)
- [[`6055134db6`](https://github.com/nodejs/node/commit/6055134db6)] - **doc**: explain `hex` encoding in Buffer API (Harshitha KP) [#31352](https://github.com/nodejs/node/pull/31352)
- [[`bd54abe3f7`](https://github.com/nodejs/node/commit/bd54abe3f7)] - **doc**: explain \_writev() API (Harshitha KP) [#31356](https://github.com/nodejs/node/pull/31356)
- [[`91f5e9b0f7`](https://github.com/nodejs/node/commit/91f5e9b0f7)] - **doc**: document missing properties in child_process (Harshitha KP) [#31342](https://github.com/nodejs/node/pull/31342)
- [[`6874deef28`](https://github.com/nodejs/node/commit/6874deef28)] - **doc,assert**: rename "mode" to "assertion mode" (Rich Trott) [#31635](https://github.com/nodejs/node/pull/31635)
- [[`788ea36ce0`](https://github.com/nodejs/node/commit/788ea36ce0)] - **doc,net**: reword Unix domain path paragraph in net.md (Rich Trott) [#31684](https://github.com/nodejs/node/pull/31684)
- [[`e3e40a12b0`](https://github.com/nodejs/node/commit/e3e40a12b0)] - **doc,util**: revise util.md introductory paragraph (Rich Trott) [#31685](https://github.com/nodejs/node/pull/31685)
- [[`e46cfaf146`](https://github.com/nodejs/node/commit/e46cfaf146)] - **errors**: make use of "cannot" consistent (Tobias Nießen) [#31420](https://github.com/nodejs/node/pull/31420)
- [[`f6392e9fde`](https://github.com/nodejs/node/commit/f6392e9fde)] - **esm**: import.meta.resolve with nodejs: builtins (Guy Bedford) [#31032](https://github.com/nodejs/node/pull/31032)
- [[`21fc81821f`](https://github.com/nodejs/node/commit/21fc81821f)] - **fs**: set path when mkdir recursive called on file (bcoe) [#31607](https://github.com/nodejs/node/pull/31607)
- [[`8669ecc8a2`](https://github.com/nodejs/node/commit/8669ecc8a2)] - **fs**: bail on permission error in recursive directory creation (bcoe) [#31505](https://github.com/nodejs/node/pull/31505)
- [[`2c2b3ba39c`](https://github.com/nodejs/node/commit/2c2b3ba39c)] - **fs**: do not emit 'close' twice if emitClose enabled (Robert Nagy) [#31383](https://github.com/nodejs/node/pull/31383)
- [[`32ac1be372`](https://github.com/nodejs/node/commit/32ac1be372)] - **fs**: unset FileHandle fd after close (Anna Henningsen) [#31389](https://github.com/nodejs/node/pull/31389)
- [[`9ecae58643`](https://github.com/nodejs/node/commit/9ecae58643)] - **lib**: delete dead code in SourceMap (Justin Ridgewell) [#31512](https://github.com/nodejs/node/pull/31512)
- [[`7ecf842429`](https://github.com/nodejs/node/commit/7ecf842429)] - **lib,src**: switch Buffer::kMaxLength to size_t (Ben Noordhuis) [#31406](https://github.com/nodejs/node/pull/31406)
- [[`15c8d9ead1`](https://github.com/nodejs/node/commit/15c8d9ead1)] - **meta**: move princejwesley to emeritus (Rich Trott) [#31730](https://github.com/nodejs/node/pull/31730)
- [[`f5ae510e03`](https://github.com/nodejs/node/commit/f5ae510e03)] - **meta**: move vkurchatkin to emeritus (Rich Trott) [#31729](https://github.com/nodejs/node/pull/31729)
- [[`cd520ddfef`](https://github.com/nodejs/node/commit/cd520ddfef)] - **meta**: move calvinmetcalf to emeritus (Rich Trott) [#31736](https://github.com/nodejs/node/pull/31736)
- [[`832255df89`](https://github.com/nodejs/node/commit/832255df89)] - **meta**: fix collaborator list errors in README.md (James M Snell) [#31655](https://github.com/nodejs/node/pull/31655)
- [[`aa266628ba`](https://github.com/nodejs/node/commit/aa266628ba)] - **module**: drop support for extensionless main entry points in esm (Geoffrey Booth) [#31415](https://github.com/nodejs/node/pull/31415)
- [[`ca81af7d73`](https://github.com/nodejs/node/commit/ca81af7d73)] - **module**: correct docs about when extensionless files are supported (Geoffrey Booth) [#31415](https://github.com/nodejs/node/pull/31415)
- [[`6797656d86`](https://github.com/nodejs/node/commit/6797656d86)] - **module**: revert #31021 (Geoffrey Booth) [#31415](https://github.com/nodejs/node/pull/31415)
- [[`ae2141effc`](https://github.com/nodejs/node/commit/ae2141effc)] - **n-api**: free instance data as reference (Gabriel Schulhof) [#31638](https://github.com/nodejs/node/pull/31638)
- [[`c8215699ab`](https://github.com/nodejs/node/commit/c8215699ab)] - **n-api**: rename 'promise' parameter to 'value' (Tobias Nießen) [#31544](https://github.com/nodejs/node/pull/31544)
- [[`5982726ef9`](https://github.com/nodejs/node/commit/5982726ef9)] - **net**: track state of setNoDelay() and prevent unnecessary system calls (Rusty Conover) [#31543](https://github.com/nodejs/node/pull/31543)
- [[`e7fea14c7b`](https://github.com/nodejs/node/commit/e7fea14c7b)] - **(SEMVER-MINOR)** **perf_hooks**: add property flags to GCPerformanceEntry (Kirill Fomichev) [#29547](https://github.com/nodejs/node/pull/29547)
- [[`672315651d`](https://github.com/nodejs/node/commit/672315651d)] - **(SEMVER-MINOR)** **process**: report ArrayBuffer memory in `memoryUsage()` (Anna Henningsen) [#31550](https://github.com/nodejs/node/pull/31550)
- [[`cd754337f8`](https://github.com/nodejs/node/commit/cd754337f8)] - **process**: fix two overflow cases in SourceMap VLQ decoding (Justin Ridgewell) [#31490](https://github.com/nodejs/node/pull/31490)
- [[`98f3028c30`](https://github.com/nodejs/node/commit/98f3028c30)] - **readline**: remove intermediate variable (cjihrig) [#31676](https://github.com/nodejs/node/pull/31676)
- [[`148dfde1d4`](https://github.com/nodejs/node/commit/148dfde1d4)] - **(SEMVER-MINOR)** **readline**: make tab size configurable (Ruben Bridgewater) [#31318](https://github.com/nodejs/node/pull/31318)
- [[`1bcf2f9423`](https://github.com/nodejs/node/commit/1bcf2f9423)] - **report**: add support for Workers (Anna Henningsen) [#31386](https://github.com/nodejs/node/pull/31386)
- [[`7c2d33f38f`](https://github.com/nodejs/node/commit/7c2d33f38f)] - **src**: use hex not decimal in IsArrayIndex (Shelley Vohr) [#31758](https://github.com/nodejs/node/pull/31758)
- [[`a095ef0d52`](https://github.com/nodejs/node/commit/a095ef0d52)] - **src**: keep main-thread Isolate attached to platform during Dispose (Anna Henningsen) [#31795](https://github.com/nodejs/node/pull/31795)
- [[`1dec9d196f`](https://github.com/nodejs/node/commit/1dec9d196f)] - **src**: wrap HostPort in ExclusiveAccess (Ben Noordhuis) [#31717](https://github.com/nodejs/node/pull/31717)
- [[`e23023d685`](https://github.com/nodejs/node/commit/e23023d685)] - **src**: add ExclusiveAccess class (Ben Noordhuis) [#31717](https://github.com/nodejs/node/pull/31717)
- [[`54caf76210`](https://github.com/nodejs/node/commit/54caf76210)] - **src**: allow to reuse env options handling (Denys Otrishko) [#31711](https://github.com/nodejs/node/pull/31711)
- [[`6ad8ca5ecf`](https://github.com/nodejs/node/commit/6ad8ca5ecf)] - **src**: do not unnecessarily re-assign uv handle data (Anna Henningsen) [#31696](https://github.com/nodejs/node/pull/31696)
- [[`2837788849`](https://github.com/nodejs/node/commit/2837788849)] - **src**: fix compile warnings in node_url.cc (Anna Henningsen) [#31689](https://github.com/nodejs/node/pull/31689)
- [[`1d34ab5e43`](https://github.com/nodejs/node/commit/1d34ab5e43)] - **src**: modernized unique_ptr construction (Yuhanun Citgez) [#31654](https://github.com/nodejs/node/pull/31654)
- [[`0e44902b85`](https://github.com/nodejs/node/commit/0e44902b85)] - **src**: remove dead code in InternalMakeCallback (Gerhard Stoebich) [#31622](https://github.com/nodejs/node/pull/31622)
- [[`348c7871b6`](https://github.com/nodejs/node/commit/348c7871b6)] - **src**: remove fixed-size GetHumanReadableProcessName (Ben Noordhuis) [#31633](https://github.com/nodejs/node/pull/31633)
- [[`8964077935`](https://github.com/nodejs/node/commit/8964077935)] - **src**: fix OOB reads in process.title getter (Ben Noordhuis) [#31633](https://github.com/nodejs/node/pull/31633)
- [[`af612bcc21`](https://github.com/nodejs/node/commit/af612bcc21)] - **src**: various minor improvements to node_url (James M Snell) [#31651](https://github.com/nodejs/node/pull/31651)
- [[`f0ffa4cb80`](https://github.com/nodejs/node/commit/f0ffa4cb80)] - **src**: fix inspecting `MessagePort` from `init` async hook (Anna Henningsen) [#31600](https://github.com/nodejs/node/pull/31600)
- [[`425662e2d6`](https://github.com/nodejs/node/commit/425662e2d6)] - **src**: remove unused `Worker::child\_port\_` member (Anna Henningsen) [#31599](https://github.com/nodejs/node/pull/31599)
- [[`43e2c2e643`](https://github.com/nodejs/node/commit/43e2c2e643)] - **src**: change Fill() to use ParseArrayIndex() (ConorDavenport) [#31591](https://github.com/nodejs/node/pull/31591)
- [[`42b835412d`](https://github.com/nodejs/node/commit/42b835412d)] - **src**: remove duplicate field env in CryptoJob class (ConorDavenport) [#31554](https://github.com/nodejs/node/pull/31554)
- [[`9fd1e717e6`](https://github.com/nodejs/node/commit/9fd1e717e6)] - **src**: fix console debug output on Windows (Denys Otrishko) [#31580](https://github.com/nodejs/node/pull/31580)
- [[`277980d288`](https://github.com/nodejs/node/commit/277980d288)] - **src**: use \_\_executable_start for linux hugepages (Ben Noordhuis) [#31547](https://github.com/nodejs/node/pull/31547)
- [[`6d5c3cd7ac`](https://github.com/nodejs/node/commit/6d5c3cd7ac)] - **src**: remove preview for heap dump utilities (Anna Henningsen) [#31570](https://github.com/nodejs/node/pull/31570)
- [[`c167ae0a87`](https://github.com/nodejs/node/commit/c167ae0a87)] - **src**: fix minor typo in base_object.h (Daniel Bevenius) [#31535](https://github.com/nodejs/node/pull/31535)
- [[`f04576ede0`](https://github.com/nodejs/node/commit/f04576ede0)] - **src**: fix debug crash handling null strings (Rusty Conover) [#31523](https://github.com/nodejs/node/pull/31523)
- [[`ef4d081660`](https://github.com/nodejs/node/commit/ef4d081660)] - **src**: simplify native immediate queue running (Anna Henningsen) [#31502](https://github.com/nodejs/node/pull/31502)
- [[`bc0c1420f0`](https://github.com/nodejs/node/commit/bc0c1420f0)] - **src**: define noreturn attribute for windows (Alexander Smarus) [#31467](https://github.com/nodejs/node/pull/31467)
- [[`9e9dbd44fe`](https://github.com/nodejs/node/commit/9e9dbd44fe)] - **src**: reduce code duplication in BootstrapNode (Denys Otrishko) [#31465](https://github.com/nodejs/node/pull/31465)
- [[`76aad0e5e1`](https://github.com/nodejs/node/commit/76aad0e5e1)] - **src**: use custom fprintf alike to write errors to stderr (Anna Henningsen) [#31446](https://github.com/nodejs/node/pull/31446)
- [[`a685827a55`](https://github.com/nodejs/node/commit/a685827a55)] - **src**: add C++-style sprintf utility (Anna Henningsen) [#31446](https://github.com/nodejs/node/pull/31446)
- [[`049a1727d4`](https://github.com/nodejs/node/commit/049a1727d4)] - **src**: harden running native `SetImmediate()`s slightly (Anna Henningsen) [#31468](https://github.com/nodejs/node/pull/31468)
- [[`f56de5a3b4`](https://github.com/nodejs/node/commit/f56de5a3b4)] - **src**: move MemoryInfo() for worker code to .cc files (Anna Henningsen) [#31386](https://github.com/nodejs/node/pull/31386)
- [[`0cacc1facf`](https://github.com/nodejs/node/commit/0cacc1facf)] - **src**: add interrupts to Environments/Workers (Anna Henningsen) [#31386](https://github.com/nodejs/node/pull/31386)
- [[`f8c45b277f`](https://github.com/nodejs/node/commit/f8c45b277f)] - **src**: remove AsyncRequest (Anna Henningsen) [#31386](https://github.com/nodejs/node/pull/31386)
- [[`600e96ec04`](https://github.com/nodejs/node/commit/600e96ec04)] - **src**: add a threadsafe variant of SetImmediate() (Anna Henningsen) [#31386](https://github.com/nodejs/node/pull/31386)
- [[`74a7cdbe05`](https://github.com/nodejs/node/commit/74a7cdbe05)] - **src**: exclude C++ SetImmediate() from count (Anna Henningsen) [#31386](https://github.com/nodejs/node/pull/31386)
- [[`53e566bc50`](https://github.com/nodejs/node/commit/53e566bc50)] - **src**: better encapsulate native immediate list (Anna Henningsen) [#31386](https://github.com/nodejs/node/pull/31386)
- [[`b8face28e7`](https://github.com/nodejs/node/commit/b8face28e7)] - **src**: reduce large pages code duplication (Gabriel Schulhof) [#31385](https://github.com/nodejs/node/pull/31385)
- [[`83dd65a469`](https://github.com/nodejs/node/commit/83dd65a469)] - **src**: fix ignore GCC -Wcast-function-type for older compilers (Denys Otrishko) [#31524](https://github.com/nodejs/node/pull/31524)
- [[`13c6965703`](https://github.com/nodejs/node/commit/13c6965703)] - **src**: ignore GCC -Wcast-function-type for v8.h (Daniel Bevenius) [#31475](https://github.com/nodejs/node/pull/31475)
- [[`3dd4089b9a`](https://github.com/nodejs/node/commit/3dd4089b9a)] - **(SEMVER-MINOR)** **src,lib**: make ^C print a JS stack trace (legendecas) [#29207](https://github.com/nodejs/node/pull/29207)
- [[`6d0b2267ce`](https://github.com/nodejs/node/commit/6d0b2267ce)] - **stream**: fix finished w/ 'close' before 'finish' (Robert Nagy) [#31534](https://github.com/nodejs/node/pull/31534)
- [[`80e75ab389`](https://github.com/nodejs/node/commit/80e75ab389)] - **stream**: add regression test for async iteration completion (Matteo Collina) [#31508](https://github.com/nodejs/node/pull/31508)
- [[`538582b43d`](https://github.com/nodejs/node/commit/538582b43d)] - **_Revert_** "**stream**: fix async iterator destroyed error propagation" (Matteo Collina) [#31508](https://github.com/nodejs/node/pull/31508)
- [[`f255053033`](https://github.com/nodejs/node/commit/f255053033)] - **stream**: fix finished writable/readable state (Robert Nagy) [#31527](https://github.com/nodejs/node/pull/31527)
- [[`3046648580`](https://github.com/nodejs/node/commit/3046648580)] - **stream**: implement throw for async iterator (Robert Nagy) [#31316](https://github.com/nodejs/node/pull/31316)
- [[`5a95fa4aeb`](https://github.com/nodejs/node/commit/5a95fa4aeb)] - **stream**: normalize async iterator stream destroy (Robert Nagy) [#31316](https://github.com/nodejs/node/pull/31316)
- [[`20d0a0e9a7`](https://github.com/nodejs/node/commit/20d0a0e9a7)] - **stream**: add async iterator support for v1 streams (Robert Nagy) [#31316](https://github.com/nodejs/node/pull/31316)
- [[`0654e6790d`](https://github.com/nodejs/node/commit/0654e6790d)] - **test**: mark test-fs-stat-bigint flaky on FreeBSD (Rich Trott) [#31728](https://github.com/nodejs/node/pull/31728)
- [[`6dbe6bde56`](https://github.com/nodejs/node/commit/6dbe6bde56)] - **test**: fix flaky parallel/test-repl-history-navigation test (Ruben Bridgewater) [#31708](https://github.com/nodejs/node/pull/31708)
- [[`1dae7dc6bc`](https://github.com/nodejs/node/commit/1dae7dc6bc)] - **test**: improve test-fs-stat-bigint (Rich Trott) [#31726](https://github.com/nodejs/node/pull/31726)
- [[`fa9b59276d`](https://github.com/nodejs/node/commit/fa9b59276d)] - **test**: remove flaky designation for test-fs-stat-bigint (Rich Trott) [#30437](https://github.com/nodejs/node/pull/30437)
- [[`d36ba2b555`](https://github.com/nodejs/node/commit/d36ba2b555)] - **test**: fix flaky test-fs-stat-bigint (Duncan Healy) [#30437](https://github.com/nodejs/node/pull/30437)
- [[`5b3c4b3e7d`](https://github.com/nodejs/node/commit/5b3c4b3e7d)] - **_Revert_** "**test**: refactor all benchmark tests to use the new test option" (Anna Henningsen) [#31722](https://github.com/nodejs/node/pull/31722)
- [[`2c0f3028c9`](https://github.com/nodejs/node/commit/2c0f3028c9)] - **test**: add debugging output to test-net-listen-after-destroy-stdin (Rich Trott) [#31698](https://github.com/nodejs/node/pull/31698)
- [[`2224211609`](https://github.com/nodejs/node/commit/2224211609)] - **test**: improve assertion message in test-dns-any (Rich Trott) [#31697](https://github.com/nodejs/node/pull/31697)
- [[`b0e37b7180`](https://github.com/nodejs/node/commit/b0e37b7180)] - **test**: fix flaky test-trace-sigint-on-idle (Anna Henningsen) [#31645](https://github.com/nodejs/node/pull/31645)
- [[`58f17c0e6b`](https://github.com/nodejs/node/commit/58f17c0e6b)] - **test**: stricter assert color test (Ruben Bridgewater) [#31429](https://github.com/nodejs/node/pull/31429)
- [[`89dcf733c6`](https://github.com/nodejs/node/commit/89dcf733c6)] - **test**: improve logged errors (Ruben Bridgewater) [#31425](https://github.com/nodejs/node/pull/31425)
- [[`4878c7a197`](https://github.com/nodejs/node/commit/4878c7a197)] - **test**: refactor all benchmark tests to use the new test option (Ruben Bridgewater) [#31396](https://github.com/nodejs/node/pull/31396)
- [[`3bcc2da887`](https://github.com/nodejs/node/commit/3bcc2da887)] - **test**: fix test-benchmark-http (Rich Trott) [#31686](https://github.com/nodejs/node/pull/31686)
- [[`6139d4ea3b`](https://github.com/nodejs/node/commit/6139d4ea3b)] - **test**: fix flaky test-inspector-connect-main-thread (Anna Henningsen) [#31637](https://github.com/nodejs/node/pull/31637)
- [[`13c256d31d`](https://github.com/nodejs/node/commit/13c256d31d)] - **test**: add test-dns-promises-lookupService (Rich Trott) [#31640](https://github.com/nodejs/node/pull/31640)
- [[`23fefba84c`](https://github.com/nodejs/node/commit/23fefba84c)] - **test**: fix flaky test-http2-stream-destroy-event-order (Anna Henningsen) [#31610](https://github.com/nodejs/node/pull/31610)
- [[`435b9c977a`](https://github.com/nodejs/node/commit/435b9c977a)] - **test**: abstract common assertions in readline-interface test (Ruben Bridgewater) [#31423](https://github.com/nodejs/node/pull/31423)
- [[`d2a12d3af8`](https://github.com/nodejs/node/commit/d2a12d3af8)] - **test**: refactor test-readline-interface.js (Ruben Bridgewater) [#31423](https://github.com/nodejs/node/pull/31423)
- [[`7c3cc94b9f`](https://github.com/nodejs/node/commit/7c3cc94b9f)] - **test**: unset NODE_OPTIONS for cctest (Anna Henningsen) [#31594](https://github.com/nodejs/node/pull/31594)
- [[`62d0c6029d`](https://github.com/nodejs/node/commit/62d0c6029d)] - **test**: simplify test-https-simple.js (Sam Roberts) [#31584](https://github.com/nodejs/node/pull/31584)
- [[`49be50051c`](https://github.com/nodejs/node/commit/49be50051c)] - **test**: show child stderr output in largepages test (Ben Noordhuis) [#31612](https://github.com/nodejs/node/pull/31612)
- [[`c3247fedd9`](https://github.com/nodejs/node/commit/c3247fedd9)] - **test**: mark additional tests as flaky on Windows (Anna Henningsen) [#31606](https://github.com/nodejs/node/pull/31606)
- [[`3fdec1c790`](https://github.com/nodejs/node/commit/3fdec1c790)] - **test**: fix flaky test-memory-usage (Anna Henningsen) [#31602](https://github.com/nodejs/node/pull/31602)
- [[`23da559ab2`](https://github.com/nodejs/node/commit/23da559ab2)] - **test**: verify threadId in reports (Dylan Coakley) [#31556](https://github.com/nodejs/node/pull/31556)
- [[`5a12cd636b`](https://github.com/nodejs/node/commit/5a12cd636b)] - **test**: remove --experimental-worker flag comment (Harshitha KP) [#31563](https://github.com/nodejs/node/pull/31563)
- [[`07525c317e`](https://github.com/nodejs/node/commit/07525c317e)] - **test**: make test-http2-buffersize more correct (Anna Henningsen) [#31502](https://github.com/nodejs/node/pull/31502)
- [[`c4a2f94a11`](https://github.com/nodejs/node/commit/c4a2f94a11)] - **test**: cover property n-api null cases (Gabriel Schulhof) [#31488](https://github.com/nodejs/node/pull/31488)
- [[`f2dc694805`](https://github.com/nodejs/node/commit/f2dc694805)] - **test**: fix test-heapdump-worker (Anna Henningsen) [#31494](https://github.com/nodejs/node/pull/31494)
- [[`b25ea9b1dc`](https://github.com/nodejs/node/commit/b25ea9b1dc)] - **test**: add tests for main() argument handling (cjihrig) [#31426](https://github.com/nodejs/node/pull/31426)
- [[`38ea53629b`](https://github.com/nodejs/node/commit/38ea53629b)] - **test**: add wasi test for freopen() (cjihrig) [#31432](https://github.com/nodejs/node/pull/31432)
- [[`c2792aad44`](https://github.com/nodejs/node/commit/c2792aad44)] - **test**: remove bluebird remnants from test fixture (Rich Trott) [#31435](https://github.com/nodejs/node/pull/31435)
- [[`583d1d9f55`](https://github.com/nodejs/node/commit/583d1d9f55)] - **test**: improve wasi stat test (cjihrig) [#31413](https://github.com/nodejs/node/pull/31413)
- [[`676b84a803`](https://github.com/nodejs/node/commit/676b84a803)] - **(SEMVER-MINOR)** **test**: skip keygen tests on arm systems (Tobias Nießen) [#31178](https://github.com/nodejs/node/pull/31178)
- [[`099c921f40`](https://github.com/nodejs/node/commit/099c921f40)] - **test**: add wasi test for symlink() and readlink() (cjihrig) [#31403](https://github.com/nodejs/node/pull/31403)
- [[`6256d0ae92`](https://github.com/nodejs/node/commit/6256d0ae92)] - **test**: update postmortem test with v12 constants (Matheus Marchini) [#31391](https://github.com/nodejs/node/pull/31391)
- [[`0bafb5c8c8`](https://github.com/nodejs/node/commit/0bafb5c8c8)] - **test**: export public symbols in addons tests (Ben Noordhuis) [#28717](https://github.com/nodejs/node/pull/28717)
- [[`6833f62e9d`](https://github.com/nodejs/node/commit/6833f62e9d)] - **test**: add promises metadata to postmortem test (Matheus Marchini) [#31357](https://github.com/nodejs/node/pull/31357)
- [[`41524282b5`](https://github.com/nodejs/node/commit/41524282b5)] - **test,benchmark**: fix test-benchmark-zlib (Rich Trott) [#31538](https://github.com/nodejs/node/pull/31538)
- [[`c34872e464`](https://github.com/nodejs/node/commit/c34872e464)] - **test,dns**: add coverage for dns exception (Rich Trott) [#31678](https://github.com/nodejs/node/pull/31678)
- [[`03aac4e65d`](https://github.com/nodejs/node/commit/03aac4e65d)] - **tls**: simplify errors using ThrowCryptoError (Tobias Nießen) [#31436](https://github.com/nodejs/node/pull/31436)
- [[`95d509e974`](https://github.com/nodejs/node/commit/95d509e974)] - **tools**: update Markdown linter to be cross-platform (Derek Lewis) [#31239](https://github.com/nodejs/node/pull/31239)
- [[`328b8a6444`](https://github.com/nodejs/node/commit/328b8a6444)] - **tools**: unify make-v8.sh for ppc64le and s390x (Richard Lau) [#31628](https://github.com/nodejs/node/pull/31628)
- [[`39c86bbe4c`](https://github.com/nodejs/node/commit/39c86bbe4c)] - **tools**: replace deprecated iteritems() for items() (Giovanny Andres Gongora Granada (Gioyik)) [#31528](https://github.com/nodejs/node/pull/31528)
- [[`be55f3ec4f`](https://github.com/nodejs/node/commit/be55f3ec4f)] - **tty**: do not end in an infinite warning recursion (Ruben Bridgewater) [#31429](https://github.com/nodejs/node/pull/31429)
- [[`a0c1ceddbc`](https://github.com/nodejs/node/commit/a0c1ceddbc)] - **util**: throw if unreachable TypedArray checking code is reached (Rich Trott) [#31737](https://github.com/nodejs/node/pull/31737)
- [[`7b9d6d08f4`](https://github.com/nodejs/node/commit/7b9d6d08f4)] - **util**: add coverage for util.inspect.colors alias setter (Rich Trott) [#31743](https://github.com/nodejs/node/pull/31743)
- [[`9f9edc2c78`](https://github.com/nodejs/node/commit/9f9edc2c78)] - **util**: throw if unreachable code is reached (Rich Trott) [#31712](https://github.com/nodejs/node/pull/31712)
- [[`5e1bee817c`](https://github.com/nodejs/node/commit/5e1bee817c)] - **util**: fix inspection of typed arrays with unusual length (Ruben Bridgewater) [#31458](https://github.com/nodejs/node/pull/31458)
- [[`3da4d5174c`](https://github.com/nodejs/node/commit/3da4d5174c)] - **util**: improve unicode support (Ruben Bridgewater) [#31319](https://github.com/nodejs/node/pull/31319)
- [[`822f2ac640`](https://github.com/nodejs/node/commit/822f2ac640)] - **worker**: add support for .cjs extension (Antoine du HAMEL) [#31662](https://github.com/nodejs/node/pull/31662)
- [[`cd99dc7368`](https://github.com/nodejs/node/commit/cd99dc7368)] - **worker**: properly handle env and NODE_OPTIONS in workers (Denys Otrishko) [#31711](https://github.com/nodejs/node/pull/31711)
- [[`1592c474da`](https://github.com/nodejs/node/commit/1592c474da)] - **worker**: reset `Isolate` stack limit after entering `Locker` (Anna Henningsen) [#31593](https://github.com/nodejs/node/pull/31593)
- [[`3e5803f91b`](https://github.com/nodejs/node/commit/3e5803f91b)] - **worker**: improve MessagePort performance (Anna Henningsen) [#31605](https://github.com/nodejs/node/pull/31605)
- [[`8d3ffbeb55`](https://github.com/nodejs/node/commit/8d3ffbeb55)] - **(SEMVER-MINOR)** **worker**: add ability to take heap snapshot from parent thread (Anna Henningsen) [#31569](https://github.com/nodejs/node/pull/31569)
- [[`6fdef457c6`](https://github.com/nodejs/node/commit/6fdef457c6)] - **worker**: remove redundant closing of child port (aaccttrr) [#31555](https://github.com/nodejs/node/pull/31555)
- [[`5656ec9f71`](https://github.com/nodejs/node/commit/5656ec9f71)] - **worker**: move JoinThread() back into exit callback (Anna Henningsen) [#31468](https://github.com/nodejs/node/pull/31468)

Windows 32-bit Installer: https://nodejs.org/dist/v13.9.0/node-v13.9.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v13.9.0/node-v13.9.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v13.9.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v13.9.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v13.9.0/node-v13.9.0.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v13.9.0/node-v13.9.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v13.9.0/node-v13.9.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v13.9.0/node-v13.9.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v13.9.0/node-v13.9.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v13.9.0/node-v13.9.0-aix-ppc64.tar.gz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v13.9.0/node-v13.9.0-sunos-x64.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v13.9.0/node-v13.9.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v13.9.0/node-v13.9.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v13.9.0/node-v13.9.0.tar.gz \
Other release files: https://nodejs.org/dist/v13.9.0/ \
Documentation: https://nodejs.org/docs/v13.9.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

0330f9649df64a665d073226d27a7ea418e09bb3f3a59abfdbabcffe5efd6d10  node-v13.9.0-aix-ppc64.tar.gz
b2a5a539b9b2d1733bda301913c99d220968de801bf313b762fa932820ea797b  node-v13.9.0-darwin-x64.tar.gz
e0c139c419339cd21dad975f7776b5cd84b1abfb19989fb16b2451f0b3d2ff8d  node-v13.9.0-darwin-x64.tar.xz
1332565e973135961ccfc95059539c7e39321e3e0a1b15129677f1e4cb11cbad  node-v13.9.0-headers.tar.gz
4cf754ba4b8c301bb5df9a7188b7233c0b86aa687508be148525e2c2c3ec91fc  node-v13.9.0-headers.tar.xz
8d253978fec837a6cd9d2ba9665bda14d62e7453d44123438971d0026df469dd  node-v13.9.0-linux-arm64.tar.gz
c668afc06e4094b68ae758b823fc3244b34f0468fc0c2feee45278788989b8a1  node-v13.9.0-linux-arm64.tar.xz
562f3704f81b09e476e0a05f752aa68e9c945728c0d07f4390d1200cf246e2c2  node-v13.9.0-linux-armv7l.tar.gz
59a4342ed0f45f0e26c178e17cdf0ec98a858788ad2f509f5d8a79bf96d214ca  node-v13.9.0-linux-armv7l.tar.xz
786f71b87e09d167492aa0251fddc354b19eab3bff2a4b36fb02a4d0e5974e20  node-v13.9.0-linux-ppc64le.tar.gz
ca574bba059e4384b98dd6d5fc12ec80e415684fb0d9b35b27145333d98eb33d  node-v13.9.0-linux-ppc64le.tar.xz
87b5b4ae6a5b3f5725a1c6451f8653af29854ad2a05338fca8d02cf7983817e3  node-v13.9.0-linux-s390x.tar.gz
d88879137e076604feebd30ed3cef216906ea6bc2d3b38deb750793830629185  node-v13.9.0-linux-s390x.tar.xz
5cd26d58edabb7b636c3f1c6e62ea074849bd0f077f870066aaac485572cacef  node-v13.9.0-linux-x64.tar.gz
f1e093303468032a1ecb0e290e19b43bf7771d4efbf589560df0060149614272  node-v13.9.0-linux-x64.tar.xz
72d13b279f5447b3e6dfac95a519865c0ec742d78b96c55be73575a7e1a22da6  node-v13.9.0.pkg
10d2637b88d8ae146bf9cfaefa59619af2c37a7bcb152bfb6c85d94dfa359696  node-v13.9.0-sunos-x64.tar.gz
6945d784031ae5fd2011570167d623a85db36878452f190c70f8d1e4347ed7d9  node-v13.9.0-sunos-x64.tar.xz
1953121fcf5a8933e0bd282f78e6b8b25fb6d55bb50d47416ae7a7dbbdc288bb  node-v13.9.0.tar.gz
fcf7d2c3231abddbc07d203029295932255a643d0792c016910a79f18784c87c  node-v13.9.0.tar.xz
0a8bf288f3dab710abf606c7e238e7b77670983de38c17d1a69a5361cfcddcff  node-v13.9.0-win-x64.7z
ec0a55bb703906494e738cd3d09e3274b34f0a3fbe207b9e67502092ed017500  node-v13.9.0-win-x64.zip
8232a2a922ce86e926c572097df3d52bd9bd0f15e84bc646dc76226f253b4a21  node-v13.9.0-win-x86.7z
3c6bfe1403d8cc9bfca24be497085a996fe1f03c6944a0cb6ecdb07f1d055c8b  node-v13.9.0-win-x86.zip
2fc2539e073bc2e738166657ff1dff6394fb6cffb70a67c8d48e87f950ef5938  node-v13.9.0-x64.msi
7ce10b8aabea81f155206f5291a5ce936d9a6b3db1872e664255a77f62fcd8d8  node-v13.9.0-x86.msi
d3d491a04d555db6fa09f1a98d26ee500b45fdacfdc988d71ed10a3da2b01e3e  win-x64/node.exe
93d8299044f598d700f67480a72d710a59888a14fea2e9f0d5fcc248b0ec02e2  win-x64/node.lib
57eda69e9334e9e751021baf88ee37e8cc5947c2202afe78dfdb4e22765c3fef  win-x64/node_pdb.7z
e52c713c19feda501e2fd3ee701bc91e13303234592cabbc000f476617a7049e  win-x64/node_pdb.zip
07e40109ebba41dc24d4eb5c8dff96b77cd7b31dfc28f1de2b5d51042353f497  win-x86/node.exe
876a8db699a1d5f5d298900b998b07d1bcd15273633c684f266718940cc211f5  win-x86/node.lib
6532ed1d55e7187010ad1aaf3b517a2accc248101395526edad776963c49ecdd  win-x86/node_pdb.7z
00b9b88e1ec7bcfb723b673eff161e8db6a34ba42381034b30ae1bd1e66437d9  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIyBAEBCAAdFiEEueL1mBqm4M0oFg2f8TmTp1WZZTwFAl5MTzsACgkQ8TmTp1WZ
ZTwTIg/4ixU+2xa7YOjRrZFO+kpmszPMwfZdZYNneR9VsMo8bCDHw7DOQfCl433U
rs6ZXjWS/ths5evkJ0j5EQbLigNBkanksX5yItGduFe3zS7MWrOAB4OnORuG9Jt6
d6iQpuqVp7z3f2fa6MOXC6uwT0L7ypkCiXe3ZtMn1Rt9IhDGdxJg6x13vnWVEGY/
4qiiz85B1Tu9Xx85zzP8U4XO86/UwFFaqX5f7npx7blSBqlRUUMU6zglvCopwCmX
kI+/eKIPNwEiKXuvb8KDBpCHZlqzp0Ic7hSOquZOEEnM7S7tVt6e5s/FGDBz0EpX
jsgfhXNC7FcTi+ZCpucDRbLmXkDL/3vlnAj1PnAvDH2vBpfn4x5Pzi4ZALEPNM/5
Kvu1DoKrr45Z7WGJluNx0swsXK315P81EwEh1+pEiIE/JD60rigEVLtNtCTzJam0
t8qrKyDMZDS7WJvuMRnXCP8+km94LPskcw6gvlt8yBTUUK9VMVNbh8JympbPerdv
0CGJIhqUVZ5jv4m9ItYakLVo1zs+s2CfbazRwa7NNK7G1FYzguYrrPgEPIXYpBe1
usHkIoCSOvJvgngR/s7Ahk+GSzI9MVXO7XDqdaSY2C5nnUzScvUW1Mz0MTWVdxSX
SgOKPPeY5LATo7+U7/o8vQ7kBcVyDbCa2uBruCJVyXxMYyJPxw==
=xai/
-----END PGP SIGNATURE-----

```
