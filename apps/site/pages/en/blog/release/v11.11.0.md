---
date: '2019-03-06T19:36:59.784Z'
category: release
title: Node v11.11.0 (Current)
layout: blog-post
author: <PERSON><PERSON>
---

### Notable Changes

- **n-api**:
  - Implement date object (<PERSON><PERSON><PERSON>) [#25917](https://github.com/nodejs/node/pull/25917)
- **util**:
  - Add compact depth mode for `util.inspect()` (<PERSON><PERSON>) [#26269](https://github.com/nodejs/node/pull/26269)
- **worker**:
  - Improve integration with native addons (<PERSON>) [#26175](https://github.com/nodejs/node/pull/26175)
  - MessagePort.prototype.onmessage takes arguments closer to the Web specification now (<PERSON>) [#26082](https://github.com/nodejs/node/pull/26082)

### Commits

- [[`d66cb4a116`](https://github.com/nodejs/node/commit/d66cb4a116)] - **benchmark,doc,lib,test**: capitalize comments (<PERSON><PERSON>) [#26223](https://github.com/nodejs/node/pull/26223)
- [[`f4955fde60`](https://github.com/nodejs/node/commit/f4955fde60)] - **benchmark,test**: refactoring (Refael Ackermann) [#26119](https://github.com/nodejs/node/pull/26119)
- [[`5e4aa28e1c`](https://github.com/nodejs/node/commit/5e4aa28e1c)] - **buffer**: avoid materializing ArrayBuffer for creation (Anna Henningsen) [#26301](https://github.com/nodejs/node/pull/26301)
- [[`05e6ec0143`](https://github.com/nodejs/node/commit/05e6ec0143)] - **build**: make 'floating patch' message informational (Ben Noordhuis) [#26349](https://github.com/nodejs/node/pull/26349)
- [[`e2baa6836b`](https://github.com/nodejs/node/commit/e2baa6836b)] - **build**: remove v8_typed_array_max_size_in_heap option (Anna Henningsen) [#26301](https://github.com/nodejs/node/pull/26301)
- [[`fa8110a60e`](https://github.com/nodejs/node/commit/fa8110a60e)] - **build**: silence cpp lint by default (Ruben Bridgewater) [#26252](https://github.com/nodejs/node/pull/26252)
- [[`dbbcedae6d`](https://github.com/nodejs/node/commit/dbbcedae6d)] - **build**: tidy up comments in `create_expfile.sh` (Richard Lau) [#26220](https://github.com/nodejs/node/pull/26220)
- [[`f408d78914`](https://github.com/nodejs/node/commit/f408d78914)] - **build**: fixed clang's warning when building openssl (Thang Tran) [#25954](https://github.com/nodejs/node/pull/25954)
- [[`a3f7471d35`](https://github.com/nodejs/node/commit/a3f7471d35)] - **build,test**: guard eslint with crypto check (Daniel Bevenius) [#26182](https://github.com/nodejs/node/pull/26182)
- [[`a70bafb3cc`](https://github.com/nodejs/node/commit/a70bafb3cc)] - **console**: prevent constructing console methods (Thomas) [#26096](https://github.com/nodejs/node/pull/26096)
- [[`1333dccede`](https://github.com/nodejs/node/commit/1333dccede)] - **crypto**: fix unencrypted DER PKCS8 parsing (Tobias Nießen) [#26236](https://github.com/nodejs/node/pull/26236)
- [[`70e463c294`](https://github.com/nodejs/node/commit/70e463c294)] - **crypto**: fix error condition in Verify::VerifyFinal (Tobias Nießen) [#26238](https://github.com/nodejs/node/pull/26238)
- [[`108c698f44`](https://github.com/nodejs/node/commit/108c698f44)] - **crypto**: make ConvertKey clear openssl error stack (Ben Noordhuis) [#26153](https://github.com/nodejs/node/pull/26153)
- [[`c8d30a7313`](https://github.com/nodejs/node/commit/c8d30a7313)] - **deps**: update acorn to 6.1.0 (gengjiawen) [#26102](https://github.com/nodejs/node/pull/26102)
- [[`7f08e0238a`](https://github.com/nodejs/node/commit/7f08e0238a)] - **deps**: V8: cherry-pick d3308d0 (Anna Henningsen) [#26207](https://github.com/nodejs/node/pull/26207)
- [[`206e4b043b`](https://github.com/nodejs/node/commit/206e4b043b)] - **deps**: V8: backport 74571c8 (Ruben Bridgewater) [#25941](https://github.com/nodejs/node/pull/25941)
- [[`f0a81664c7`](https://github.com/nodejs/node/commit/f0a81664c7)] - **deps**: backport ICU fix for ARM64 Windows (Jon Kunkee) [#26090](https://github.com/nodejs/node/pull/26090)
- [[`ea26ac0f2b`](https://github.com/nodejs/node/commit/ea26ac0f2b)] - **dns**: refactor QueryWrap lifetime management (Anna Henningsen) [#26253](https://github.com/nodejs/node/pull/26253)
- [[`846cba056e`](https://github.com/nodejs/node/commit/846cba056e)] - **doc**: edit deprecation identifier info in Collaborator Guide (Rich Trott) [#26372](https://github.com/nodejs/node/pull/26372)
- [[`3f4b27d681`](https://github.com/nodejs/node/commit/3f4b27d681)] - **doc**: maxReservedRemoteStreams value constraints (Sebastiaan Deckers) [#26309](https://github.com/nodejs/node/pull/26309)
- [[`bc5771ec91`](https://github.com/nodejs/node/commit/bc5771ec91)] - **doc**: correct typos in various docs (Beni von Cheni) [#26312](https://github.com/nodejs/node/pull/26312)
- [[`3560c3abeb`](https://github.com/nodejs/node/commit/3560c3abeb)] - **doc**: sort http.request() options alphabetically (Luigi Pinca) [#26152](https://github.com/nodejs/node/pull/26152)
- [[`86982558ad`](https://github.com/nodejs/node/commit/86982558ad)] - **doc**: add documentation for the defaultPort option (Luigi Pinca) [#26152](https://github.com/nodejs/node/pull/26152)
- [[`7bf6309f0b`](https://github.com/nodejs/node/commit/7bf6309f0b)] - **doc**: napi_get_value_bigint_words argument order (Michael Wei) [#26300](https://github.com/nodejs/node/pull/26300)
- [[`40a5a93b41`](https://github.com/nodejs/node/commit/40a5a93b41)] - **doc**: add example for setting Vary: Accept-Encoding header in zlib.md (Mukul Khanna) [#26308](https://github.com/nodejs/node/pull/26308)
- [[`85840681a4`](https://github.com/nodejs/node/commit/85840681a4)] - **doc**: revise deprecation semverness info in Collaborator Guide (Rich Trott) [#26232](https://github.com/nodejs/node/pull/26232)
- [[`ff57a1c321`](https://github.com/nodejs/node/commit/ff57a1c321)] - **doc**: clarify http.ClientRequest path description (Minwoo Jung) [#26259](https://github.com/nodejs/node/pull/26259)
- [[`5e44768e9f`](https://github.com/nodejs/node/commit/5e44768e9f)] - **doc**: revise deprecation level explanations in Collaborator Guide (Rich Trott) [#26197](https://github.com/nodejs/node/pull/26197)
- [[`823f0ce952`](https://github.com/nodejs/node/commit/823f0ce952)] - **doc**: revise Style Guide (Rich Trott) [#26176](https://github.com/nodejs/node/pull/26176)
- [[`8fac54a22f`](https://github.com/nodejs/node/commit/8fac54a22f)] - **doc**: fix code lang in repl.md (gengjiawen) [#26075](https://github.com/nodejs/node/pull/26075)
- [[`e5dae20ed6`](https://github.com/nodejs/node/commit/e5dae20ed6)] - **doc**: remove deprecation definition in Collaborator Guide (Rich Trott) [#26157](https://github.com/nodejs/node/pull/26157)
- [[`e108c32865`](https://github.com/nodejs/node/commit/e108c32865)] - **doc**: eliminate use of "note that" from child_process.md (Rich Trott) [#26141](https://github.com/nodejs/node/pull/26141)
- [[`e506f6a2d6`](https://github.com/nodejs/node/commit/e506f6a2d6)] - **doc**: remove unnecessary italics from child_process.md (Rich Trott) [#26141](https://github.com/nodejs/node/pull/26141)
- [[`b48a04bc32`](https://github.com/nodejs/node/commit/b48a04bc32)] - **doc**: remove unnecessary bold text from child_process.md (Rich Trott) [#26141](https://github.com/nodejs/node/pull/26141)
- [[`789b818ad1`](https://github.com/nodejs/node/commit/789b818ad1)] - **doc**: remove unnecessary bold italics from child_process.md (Rich Trott) [#26141](https://github.com/nodejs/node/pull/26141)
- [[`4d1c87ed6b`](https://github.com/nodejs/node/commit/4d1c87ed6b)] - **doc**: remove all-caps shouting from child_process.md (Rich Trott) [#26141](https://github.com/nodejs/node/pull/26141)
- [[`c810ced543`](https://github.com/nodejs/node/commit/c810ced543)] - **doc**: wrap child_process.md at 80 characters (Rich Trott) [#26141](https://github.com/nodejs/node/pull/26141)
- [[`a18b847d18`](https://github.com/nodejs/node/commit/a18b847d18)] - **doc**: improve worker_threads documentation (Anna Henningsen) [#26110](https://github.com/nodejs/node/pull/26110)
- [[`a9c44372e1`](https://github.com/nodejs/node/commit/a9c44372e1)] - **doc**: consolidate N-API material in Collaborator Guide (Rich Trott) [#26094](https://github.com/nodejs/node/pull/26094)
- [[`82bc68b08e`](https://github.com/nodejs/node/commit/82bc68b08e)] - **doc**: fix notable changes in v11 changelog (Michaël Zasso)
- [[`3971510b66`](https://github.com/nodejs/node/commit/3971510b66)] - **doc**: fix changelog entry (Colin Ihrig) [#26114](https://github.com/nodejs/node/pull/26114)
- [[`2ff1644b34`](https://github.com/nodejs/node/commit/2ff1644b34)] - **doc**: fix notable changes list format for 11.9.0 & 11.10.0 (Kai) [#26129](https://github.com/nodejs/node/pull/26129)
- [[`8814d03d4d`](https://github.com/nodejs/node/commit/8814d03d4d)] - **doc,lib,test**: rename node-report to report (cjihrig) [#26371](https://github.com/nodejs/node/pull/26371)
- [[`0034820f67`](https://github.com/nodejs/node/commit/0034820f67)] - **errors**: add ERR_INSPECTOR_COMMAND error (cjihrig) [#26255](https://github.com/nodejs/node/pull/26255)
- [[`030b744941`](https://github.com/nodejs/node/commit/030b744941)] - **esm**: process proxy Symbol.toString fix (Guy Bedford) [#25963](https://github.com/nodejs/node/pull/25963)
- [[`14cf22f860`](https://github.com/nodejs/node/commit/14cf22f860)] - **fs, src, lib**: fix `blksize` & `blocks` on Windows (Richard Lau) [#26056](https://github.com/nodejs/node/pull/26056)
- [[`2595fbc8b1`](https://github.com/nodejs/node/commit/2595fbc8b1)] - **http2**: improve compatibility with http/1 (Sagi Tsofan) [#23908](https://github.com/nodejs/node/pull/23908)
- [[`8a551b9d3b`](https://github.com/nodejs/node/commit/8a551b9d3b)] - **http2**: shrink memory to match read data (Anna Henningsen) [#26201](https://github.com/nodejs/node/pull/26201)
- [[`3bc012373a`](https://github.com/nodejs/node/commit/3bc012373a)] - **inspector**: print all listening addresses (Ben Noordhuis) [#26008](https://github.com/nodejs/node/pull/26008)
- [[`b0c310dcf0`](https://github.com/nodejs/node/commit/b0c310dcf0)] - **inspector**: return Error objects on error (cjihrig) [#26255](https://github.com/nodejs/node/pull/26255)
- [[`be671c3bf5`](https://github.com/nodejs/node/commit/be671c3bf5)] - **inspector**: forward errors from InspectorConsoleCall (Anna Henningsen) [#26113](https://github.com/nodejs/node/pull/26113)
- [[`0c4353a444`](https://github.com/nodejs/node/commit/0c4353a444)] - **inspector**: make sure timer handles are cleaned up (Anna Henningsen) [#26088](https://github.com/nodejs/node/pull/26088)
- [[`bf61050e91`](https://github.com/nodejs/node/commit/bf61050e91)] - **lib**: converted element to lowercase in tty.js (Abhishek Agarwal) [#26121](https://github.com/nodejs/node/pull/26121)
- [[`733beb70ae`](https://github.com/nodejs/node/commit/733beb70ae)] - **lib**: convert legacy process.binding to internalBinding (ZYSzys) [#26095](https://github.com/nodejs/node/pull/26095)
- [[`b25694d7ad`](https://github.com/nodejs/node/commit/b25694d7ad)] - **meta**: update note about building on smartOS 16 (Refael Ackermann) [#25684](https://github.com/nodejs/node/pull/25684)
- [[`6d014a6c3d`](https://github.com/nodejs/node/commit/6d014a6c3d)] - **meta**: remove the useless GitHub Account (MaleDong) [#26146](https://github.com/nodejs/node/pull/26146)
- [[`143b844db2`](https://github.com/nodejs/node/commit/143b844db2)] - **meta**: moving jasnell temporarily to TSC emeritus (jasnell) [#26106](https://github.com/nodejs/node/pull/26106)
- [[`d94f4c23fe`](https://github.com/nodejs/node/commit/d94f4c23fe)] - **module**: fix stat cache (Ruben Bridgewater) [#26266](https://github.com/nodejs/node/pull/26266)
- [[`2a66cd34fa`](https://github.com/nodejs/node/commit/2a66cd34fa)] - **module**: simpler shebang function (Ruben Bridgewater) [#26266](https://github.com/nodejs/node/pull/26266)
- [[`54896a6961`](https://github.com/nodejs/node/commit/54896a6961)] - **module**: revert module.\_compile to original state if module is patched (Ujjwal Sharma) [#21573](https://github.com/nodejs/node/pull/21573)
- [[`b338edbb0a`](https://github.com/nodejs/node/commit/b338edbb0a)] - **module**: use compileFunction over Module.wrap (Ujjwal Sharma) [#21573](https://github.com/nodejs/node/pull/21573)
- [[`e72cb94df6`](https://github.com/nodejs/node/commit/e72cb94df6)] - **(SEMVER-MINOR)** **n-api**: implement date object (Jarrod Connolly) [#25917](https://github.com/nodejs/node/pull/25917)
- [[`2335bcd6e6`](https://github.com/nodejs/node/commit/2335bcd6e6)] - **n-api**: turn NAPI_CALL_INTO_MODULE into a function (Anna Henningsen) [#26128](https://github.com/nodejs/node/pull/26128)
- [[`1ce5e63987`](https://github.com/nodejs/node/commit/1ce5e63987)] - **n-api**: do not call into JS when that is not allowed (Anna Henningsen) [#26127](https://github.com/nodejs/node/pull/26127)
- [[`5b8ac58ed8`](https://github.com/nodejs/node/commit/5b8ac58ed8)] - **path**: refactor code for clarity (Ruben Bridgewater) [#25278](https://github.com/nodejs/node/pull/25278)
- [[`348f1fbcb3`](https://github.com/nodejs/node/commit/348f1fbcb3)] - **path**: refactor for less indentation (Ruben Bridgewater) [#25278](https://github.com/nodejs/node/pull/25278)
- [[`e00c8cd54a`](https://github.com/nodejs/node/commit/e00c8cd54a)] - **path**: simplify code and remove obsolete checks (Ruben Bridgewater) [#25278](https://github.com/nodejs/node/pull/25278)
- [[`55d6b4961a`](https://github.com/nodejs/node/commit/55d6b4961a)] - **path**: refactor logic for to reduce code branches (Ruben Bridgewater) [#25278](https://github.com/nodejs/node/pull/25278)
- [[`6c7cd9ee5a`](https://github.com/nodejs/node/commit/6c7cd9ee5a)] - **path**: minor refactoring (Ruben Bridgewater) [#25278](https://github.com/nodejs/node/pull/25278)
- [[`cccc44b854`](https://github.com/nodejs/node/commit/cccc44b854)] - **path**: refactor more path code for simplicity (Ruben Bridgewater) [#25278](https://github.com/nodejs/node/pull/25278)
- [[`6c44e68f63`](https://github.com/nodejs/node/commit/6c44e68f63)] - **path**: more small refactorings (Ruben Bridgewater) [#25278](https://github.com/nodejs/node/pull/25278)
- [[`b0cde2c4cf`](https://github.com/nodejs/node/commit/b0cde2c4cf)] - **path**: minor refactoring (Ruben Bridgewater) [#25278](https://github.com/nodejs/node/pull/25278)
- [[`d91520724c`](https://github.com/nodejs/node/commit/d91520724c)] - **process**: use common operations to define browser globals (Joyee Cheung) [#26230](https://github.com/nodejs/node/pull/26230)
- [[`b1e739d881`](https://github.com/nodejs/node/commit/b1e739d881)] - **process**: move initialization of node-report into pre_execution.js (Joyee Cheung) [#26227](https://github.com/nodejs/node/pull/26227)
- [[`57179a0aab`](https://github.com/nodejs/node/commit/57179a0aab)] - **process**: setup signal handler in prepareMainThreadExecution (Joyee Cheung) [#26227](https://github.com/nodejs/node/pull/26227)
- [[`966546ceaa`](https://github.com/nodejs/node/commit/966546ceaa)] - **process**: simplify the setup of async hooks trace events (Joyee Cheung) [#26062](https://github.com/nodejs/node/pull/26062)
- [[`cd10e25bd6`](https://github.com/nodejs/node/commit/cd10e25bd6)] - **process**: move test-process-uptime to parallel (Joyee Cheung) [#26206](https://github.com/nodejs/node/pull/26206)
- [[`fde40116c4`](https://github.com/nodejs/node/commit/fde40116c4)] - **process**: fix calculation in process.uptime() (Joyee Cheung) [#26206](https://github.com/nodejs/node/pull/26206)
- [[`230e98b54a`](https://github.com/nodejs/node/commit/230e98b54a)] - **process**: start coverage collection before bootstrap (Joyee Cheung) [#26006](https://github.com/nodejs/node/pull/26006)
- [[`b5fe27ccc9`](https://github.com/nodejs/node/commit/b5fe27ccc9)] - **process**: delay setup of global exception handlers (Joyee Cheung) [#26061](https://github.com/nodejs/node/pull/26061)
- [[`0d660d9646`](https://github.com/nodejs/node/commit/0d660d9646)] - **readline**: improve Unicode handling (Avi ד) [#25723](https://github.com/nodejs/node/pull/25723)
- [[`4c254d6294`](https://github.com/nodejs/node/commit/4c254d6294)] - **repl**: use object writer for thrown errors (Anna Henningsen) [#26361](https://github.com/nodejs/node/pull/26361)
- [[`2a74a1ed60`](https://github.com/nodejs/node/commit/2a74a1ed60)] - **repl**: hide editor mode if not used in a terminal (Ruben Bridgewater) [#26240](https://github.com/nodejs/node/pull/26240)
- [[`2fa8170e51`](https://github.com/nodejs/node/commit/2fa8170e51)] - **repl**: add new line on ctrl+d (Ruben Bridgewater) [#26240](https://github.com/nodejs/node/pull/26240)
- [[`f636f15315`](https://github.com/nodejs/node/commit/f636f15315)] - **repl**: add more information (Ruben Bridgewater) [#26240](https://github.com/nodejs/node/pull/26240)
- [[`2908e6313b`](https://github.com/nodejs/node/commit/2908e6313b)] - **report**: rename location to trigger (cjihrig) [#26386](https://github.com/nodejs/node/pull/26386)
- [[`0579f4283f`](https://github.com/nodejs/node/commit/0579f4283f)] - **report**: use triggerReport() to handle signals (cjihrig) [#26386](https://github.com/nodejs/node/pull/26386)
- [[`b2c77ec081`](https://github.com/nodejs/node/commit/b2c77ec081)] - **report**: use triggerReport() to handle exceptions (cjihrig) [#26386](https://github.com/nodejs/node/pull/26386)
- [[`b62e2289d9`](https://github.com/nodejs/node/commit/b62e2289d9)] - **report**: add fallback for uv_getnameinfo() failures (Richard Lau) [#26140](https://github.com/nodejs/node/pull/26140)
- [[`2fe9886f6f`](https://github.com/nodejs/node/commit/2fe9886f6f)] - **report**: fix build warning in node_report.cc (Richard Lau) [#26265](https://github.com/nodejs/node/pull/26265)
- [[`ba5f31ac45`](https://github.com/nodejs/node/commit/ba5f31ac45)] - **report**: use ru_stime for system CPU calculation (cjihrig) [#26286](https://github.com/nodejs/node/pull/26286)
- [[`d2d94537b2`](https://github.com/nodejs/node/commit/d2d94537b2)] - **report**: simplify heap space iteration (cjihrig) [#26285](https://github.com/nodejs/node/pull/26285)
- [[`6d2a14d385`](https://github.com/nodejs/node/commit/6d2a14d385)] - **report**: refactor argument validation (cjihrig) [#26276](https://github.com/nodejs/node/pull/26276)
- [[`8e2cc5e440`](https://github.com/nodejs/node/commit/8e2cc5e440)] - **report**: refactor triggerReport() (cjihrig) [#26268](https://github.com/nodejs/node/pull/26268)
- [[`8a40468635`](https://github.com/nodejs/node/commit/8a40468635)] - **report**: remove verbose setting (cjihrig) [#26195](https://github.com/nodejs/node/pull/26195)
- [[`0e89d7add6`](https://github.com/nodejs/node/commit/0e89d7add6)] - **report**: simplify OnFatalError() handling (cjihrig) [#26191](https://github.com/nodejs/node/pull/26191)
- [[`633c1eac29`](https://github.com/nodejs/node/commit/633c1eac29)] - **report**: simplify TriggerNodeReport() (cjihrig) [#26174](https://github.com/nodejs/node/pull/26174)
- [[`fc9ba36fb2`](https://github.com/nodejs/node/commit/fc9ba36fb2)] - **src**: fix typo in callback.cc (gengjiawen) [#26337](https://github.com/nodejs/node/pull/26337)
- [[`63942de82c`](https://github.com/nodejs/node/commit/63942de82c)] - **src**: extra-semi warning in node_platform.h (Jeremy Apthorp) [#26330](https://github.com/nodejs/node/pull/26330)
- [[`cb62c24e1b`](https://github.com/nodejs/node/commit/cb62c24e1b)] - **src**: reduce to simple `const char*` in OptionsParser (ZYSzys) [#26297](https://github.com/nodejs/node/pull/26297)
- [[`3093617c0e`](https://github.com/nodejs/node/commit/3093617c0e)] - **src**: remove unused variable (cjihrig) [#26386](https://github.com/nodejs/node/pull/26386)
- [[`b216f44513`](https://github.com/nodejs/node/commit/b216f44513)] - **src**: remove unnecessary function declaration (cjihrig) [#26386](https://github.com/nodejs/node/pull/26386)
- [[`cb2cbf2eca`](https://github.com/nodejs/node/commit/cb2cbf2eca)] - **src**: remove already elevated Isolate namespce (Juan José Arboleda) [#26294](https://github.com/nodejs/node/pull/26294)
- [[`2438a4350d`](https://github.com/nodejs/node/commit/2438a4350d)] - **src**: remove unused macro in env.cc (gengjiawen) [#26273](https://github.com/nodejs/node/pull/26273)
- [[`4df82f0f1b`](https://github.com/nodejs/node/commit/4df82f0f1b)] - **src**: remove unused macro in node_http2.h (gengjiawen) [#26204](https://github.com/nodejs/node/pull/26204)
- [[`af2a6935ab`](https://github.com/nodejs/node/commit/af2a6935ab)] - **src**: remove redundant cast in PipeWrap::Fchmod (gengjiawen) [#26242](https://github.com/nodejs/node/pull/26242)
- [[`06d592c551`](https://github.com/nodejs/node/commit/06d592c551)] - **src**: simplify native immediate by using v8::Global (Anna Henningsen) [#26254](https://github.com/nodejs/node/pull/26254)
- [[`9b4eec0aad`](https://github.com/nodejs/node/commit/9b4eec0aad)] - **src**: allow not materializing ArrayBuffers from C++ (Anna Henningsen) [#26301](https://github.com/nodejs/node/pull/26301)
- [[`30f0a3b4bd`](https://github.com/nodejs/node/commit/30f0a3b4bd)] - **src**: remove dead inspector code (Anna Henningsen) [#26295](https://github.com/nodejs/node/pull/26295)
- [[`c37b6796df`](https://github.com/nodejs/node/commit/c37b6796df)] - **src**: remove unused Converter object (Anna Henningsen) [#26243](https://github.com/nodejs/node/pull/26243)
- [[`6f9ab5e15b`](https://github.com/nodejs/node/commit/6f9ab5e15b)] - **src**: remove redundant cast in method AfterStringPath (gengjiawen) [#26218](https://github.com/nodejs/node/pull/26218)
- [[`33d6a3fcb7`](https://github.com/nodejs/node/commit/33d6a3fcb7)] - **src**: clean up `StreamPipe` in destructor (Anna Henningsen) [#26256](https://github.com/nodejs/node/pull/26256)
- [[`75ae77d99f`](https://github.com/nodejs/node/commit/75ae77d99f)] - **src**: do not access Environment-owned handles after cleanup (Anna Henningsen) [#26256](https://github.com/nodejs/node/pull/26256)
- [[`d6759db15b`](https://github.com/nodejs/node/commit/d6759db15b)] - **src**: remove cast for unsupported openssl (Sam Roberts) [#26305](https://github.com/nodejs/node/pull/26305)
- [[`1abe1d1c06`](https://github.com/nodejs/node/commit/1abe1d1c06)] - **src**: track memory retainer fields (Gireesh Punathil) [#26161](https://github.com/nodejs/node/pull/26161)
- [[`3e0978d7a3`](https://github.com/nodejs/node/commit/3e0978d7a3)] - **src**: clean unused macro in inspector_socket.cc (gengjiawen) [#26158](https://github.com/nodejs/node/pull/26158)
- [[`4001b24f79`](https://github.com/nodejs/node/commit/4001b24f79)] - **src**: remove unimplemented method in class SSLWrap (gengjiawen) [#26203](https://github.com/nodejs/node/pull/26203)
- [[`8b515b24af`](https://github.com/nodejs/node/commit/8b515b24af)] - **src**: apply clang-tidy rule modernize-deprecated-headers (gengjiawen) [#26159](https://github.com/nodejs/node/pull/26159)
- [[`3c11b4eec2`](https://github.com/nodejs/node/commit/3c11b4eec2)] - **src**: allocate Buffer memory using ArrayBuffer allocator (Anna Henningsen) [#26207](https://github.com/nodejs/node/pull/26207)
- [[`282607644b`](https://github.com/nodejs/node/commit/282607644b)] - **src**: add allocation utils to env (Anna Henningsen) [#26207](https://github.com/nodejs/node/pull/26207)
- [[`238fa5704b`](https://github.com/nodejs/node/commit/238fa5704b)] - **src**: add debugging array allocator (Anna Henningsen) [#26207](https://github.com/nodejs/node/pull/26207)
- [[`437bb25d92`](https://github.com/nodejs/node/commit/437bb25d92)] - **src**: make IsolateData store ArrayBufferAllocator (Anna Henningsen) [#26207](https://github.com/nodejs/node/pull/26207)
- [[`68accb5b04`](https://github.com/nodejs/node/commit/68accb5b04)] - **src**: use smart pointer in UDPWrap::OnSend (Daniel Bevenius) [#26233](https://github.com/nodejs/node/pull/26233)
- [[`3abdcfc813`](https://github.com/nodejs/node/commit/3abdcfc813)] - **src**: remove unimplemented method in class StreamPipe (gengjiawen) [#26202](https://github.com/nodejs/node/pull/26202)
- [[`7e26ca6750`](https://github.com/nodejs/node/commit/7e26ca6750)] - **src**: simplify AliasedBuffer lifetime management (Anna Henningsen) [#26196](https://github.com/nodejs/node/pull/26196)
- [[`831aa9acb6`](https://github.com/nodejs/node/commit/831aa9acb6)] - **src**: make `node::SignalWrap::OnSignal` into lambda (Gireesh Punathil) [#26184](https://github.com/nodejs/node/pull/26184)
- [[`619b5e7c2e`](https://github.com/nodejs/node/commit/619b5e7c2e)] - **src**: simplify loop arithmetic in `GetCPUInfo` (Gireesh Punathil) [#26183](https://github.com/nodejs/node/pull/26183)
- [[`ddd71f4a92`](https://github.com/nodejs/node/commit/ddd71f4a92)] - **src**: move function from header to source file (Ben Noordhuis) [#26173](https://github.com/nodejs/node/pull/26173)
- [[`5cc2574fac`](https://github.com/nodejs/node/commit/5cc2574fac)] - **src**: move async hooks trace events setup to pre_execution.js (Joyee Cheung) [#26062](https://github.com/nodejs/node/pull/26062)
- [[`8881c0baaa`](https://github.com/nodejs/node/commit/8881c0baaa)] - **src**: simplify InspectorConsoleCall (Anna Henningsen) [#26168](https://github.com/nodejs/node/pull/26168)
- [[`c6d5af53be`](https://github.com/nodejs/node/commit/c6d5af53be)] - **src**: move req_wrap_queue to base class of ReqWrap (Anna Henningsen) [#26148](https://github.com/nodejs/node/pull/26148)
- [[`a39cd45ce8`](https://github.com/nodejs/node/commit/a39cd45ce8)] - **src**: remove `process.binding('config').fipsForced` (Joyee Cheung) [#26178](https://github.com/nodejs/node/pull/26178)
- [[`bd40a127f9`](https://github.com/nodejs/node/commit/bd40a127f9)] - **src**: only call .ReThrow() if not terminating (Anna Henningsen) [#26130](https://github.com/nodejs/node/pull/26130)
- [[`6b7d8369e3`](https://github.com/nodejs/node/commit/6b7d8369e3)] - **src**: add missing includes for vtune build (Uttam Pawar) [#26136](https://github.com/nodejs/node/pull/26136)
- [[`25ddbc9a36`](https://github.com/nodejs/node/commit/25ddbc9a36)] - **src**: apply clang-tidy rule performance-unnecessary-value-param (gengjiawen) [#26042](https://github.com/nodejs/node/pull/26042)
- [[`82df851bb5`](https://github.com/nodejs/node/commit/82df851bb5)] - **src**: unify uptime base used across the code base (Joyee Cheung) [#26016](https://github.com/nodejs/node/pull/26016)
- [[`778db675c1`](https://github.com/nodejs/node/commit/778db675c1)] - **src**: remove invalid casts in options parser (Anna Henningsen) [#26139](https://github.com/nodejs/node/pull/26139)
- [[`4ca07898d7`](https://github.com/nodejs/node/commit/4ca07898d7)] - **src**: use PauseOnNextJavascriptStatement to implement --inspect-brk-node (Joyee Cheung) [#26034](https://github.com/nodejs/node/pull/26034)
- [[`e6949b4241`](https://github.com/nodejs/node/commit/e6949b4241)] - **src**: apply clang-tidy rule modernize-use-override (gengjiawen) [#26103](https://github.com/nodejs/node/pull/26103)
- [[`d550de4fe1`](https://github.com/nodejs/node/commit/d550de4fe1)] - **src**: remove inspector main_thread_request\_ field (Anna Henningsen) [#26137](https://github.com/nodejs/node/pull/26137)
- [[`ee71952a25`](https://github.com/nodejs/node/commit/ee71952a25)] - **src**: check HasCaught() in JSStream calls (Anna Henningsen) [#26124](https://github.com/nodejs/node/pull/26124)
- [[`f44f33569d`](https://github.com/nodejs/node/commit/f44f33569d)] - **src**: extract common sockaddr creation code (Daniel Bevenius) [#26070](https://github.com/nodejs/node/pull/26070)
- [[`cbd3cf083a`](https://github.com/nodejs/node/commit/cbd3cf083a)] - **src**: add debug CHECKs against empty handles (Anna Henningsen) [#26125](https://github.com/nodejs/node/pull/26125)
- [[`0408966a9d`](https://github.com/nodejs/node/commit/0408966a9d)] - **src**: remove unused macro in node_file.cc (gengjiawen) [#26073](https://github.com/nodejs/node/pull/26073)
- [[`497d9d8ab2`](https://github.com/nodejs/node/commit/497d9d8ab2)] - **src**: use same parameter name in node_report.cc (gengjiawen) [#26046](https://github.com/nodejs/node/pull/26046)
- [[`e314681420`](https://github.com/nodejs/node/commit/e314681420)] - **src**: use more stable cast where possible (Gireesh Punathil) [#26052](https://github.com/nodejs/node/pull/26052)
- [[`7612574e42`](https://github.com/nodejs/node/commit/7612574e42)] - **stream**: make \_read() be called indefinitely if the user wants so (Matteo Collina) [#26135](https://github.com/nodejs/node/pull/26135)
- [[`50e42c9d64`](https://github.com/nodejs/node/commit/50e42c9d64)] - **test**: improve test coverage in perf_hooks (Juan José Arboleda) [#26290](https://github.com/nodejs/node/pull/26290)
- [[`a41138b0cf`](https://github.com/nodejs/node/commit/a41138b0cf)] - **test**: remove duplicated buffer negative allocation test (ZYSzys) [#26160](https://github.com/nodejs/node/pull/26160)
- [[`93d7fa3df3`](https://github.com/nodejs/node/commit/93d7fa3df3)] - **test**: only inspect on failure (Ruben Bridgewater) [#26360](https://github.com/nodejs/node/pull/26360)
- [[`91b61452c3`](https://github.com/nodejs/node/commit/91b61452c3)] - **test**: always activate colors if necessary (Ruben Bridgewater) [#26264](https://github.com/nodejs/node/pull/26264)
- [[`11bd5e07cb`](https://github.com/nodejs/node/commit/11bd5e07cb)] - **test**: rename node-report suite to report (cjihrig) [#26371](https://github.com/nodejs/node/pull/26371)
- [[`7ccffcbcb6`](https://github.com/nodejs/node/commit/7ccffcbcb6)] - **test**: improve validation of report output (cjihrig) [#26289](https://github.com/nodejs/node/pull/26289)
- [[`4561cf351f`](https://github.com/nodejs/node/commit/4561cf351f)] - **test**: verify heap buffer allocations occur (Anna Henningsen) [#26301](https://github.com/nodejs/node/pull/26301)
- [[`0c8e9ee62e`](https://github.com/nodejs/node/commit/0c8e9ee62e)] - **test**: fix for activities in tick objects prune function (Alexander Sattelmaier) [#26163](https://github.com/nodejs/node/pull/26163)
- [[`69154e405c`](https://github.com/nodejs/node/commit/69154e405c)] - **test**: refactor tick objects prune function (Alexander Sattelmaier) [#26163](https://github.com/nodejs/node/pull/26163)
- [[`d8f5f55b78`](https://github.com/nodejs/node/commit/d8f5f55b78)] - **test**: eliminate port collision (Gireesh Punathil) [#26298](https://github.com/nodejs/node/pull/26298)
- [[`88256d7ba2`](https://github.com/nodejs/node/commit/88256d7ba2)] - **test**: simplify node-report/test-exception.js (cjihrig) [#26277](https://github.com/nodejs/node/pull/26277)
- [[`e8995d1b80`](https://github.com/nodejs/node/commit/e8995d1b80)] - **test**: increase getReport() coverage (cjihrig) [#26276](https://github.com/nodejs/node/pull/26276)
- [[`33fe892ec6`](https://github.com/nodejs/node/commit/33fe892ec6)] - **test**: increase triggerReport() coverage (cjihrig) [#26268](https://github.com/nodejs/node/pull/26268)
- [[`a382b52fd8`](https://github.com/nodejs/node/commit/a382b52fd8)] - **test**: consolidate triggerReport() tests (cjihrig) [#26268](https://github.com/nodejs/node/pull/26268)
- [[`6f9a764b52`](https://github.com/nodejs/node/commit/6f9a764b52)] - **test**: remove node-report/test-api.js (cjihrig) [#26219](https://github.com/nodejs/node/pull/26219)
- [[`bc114152d0`](https://github.com/nodejs/node/commit/bc114152d0)] - **test**: simplify test-api-nohooks.js (cjihrig) [#26217](https://github.com/nodejs/node/pull/26217)
- [[`ca18525896`](https://github.com/nodejs/node/commit/ca18525896)] - **test**: improve performance of test-crypto-timing-safe-equal-benchmarks (Rich Trott) [#26237](https://github.com/nodejs/node/pull/26237)
- [[`28758b8d69`](https://github.com/nodejs/node/commit/28758b8d69)] - **test**: add test for dynamically enabling node.async_hooks tracing (Joyee Cheung) [#26062](https://github.com/nodejs/node/pull/26062)
- [[`dcbd907142`](https://github.com/nodejs/node/commit/dcbd907142)] - **test**: add test for node.async_hooks tracing in workers (Joyee Cheung) [#26062](https://github.com/nodejs/node/pull/26062)
- [[`007b2fa198`](https://github.com/nodejs/node/commit/007b2fa198)] - **test**: increase run time in test-worker-prof (Anna Henningsen) [#26172](https://github.com/nodejs/node/pull/26172)
- [[`a1fcde035e`](https://github.com/nodejs/node/commit/a1fcde035e)] - **test**: simplify test-api-getreport.js (cjihrig) [#26169](https://github.com/nodejs/node/pull/26169)
- [[`818b280a39`](https://github.com/nodejs/node/commit/818b280a39)] - **test**: remove unnecessary default tmpdir value in test (Rich Trott) [#26177](https://github.com/nodejs/node/pull/26177)
- [[`59ca9e9ccf`](https://github.com/nodejs/node/commit/59ca9e9ccf)] - **test**: consolidate assertions in ipv6only test (Rich Trott) [#26149](https://github.com/nodejs/node/pull/26149)
- [[`38a87d5521`](https://github.com/nodejs/node/commit/38a87d5521)] - **test**: increase coverage of node_report_module.cc (Richard Lau) [#26116](https://github.com/nodejs/node/pull/26116)
- [[`76c2f4f46b`](https://github.com/nodejs/node/commit/76c2f4f46b)] - **test**: simplify test-worker-syntax-error (Rich Trott) [#26144](https://github.com/nodejs/node/pull/26144)
- [[`441b5453a0`](https://github.com/nodejs/node/commit/441b5453a0)] - **test**: fix flaky test-worker-ref-onexit (Anna Henningsen) [#26170](https://github.com/nodejs/node/pull/26170)
- [[`d3525d7505`](https://github.com/nodejs/node/commit/d3525d7505)] - **test**: add --test-root option to test.py (Yang Guo) [#26093](https://github.com/nodejs/node/pull/26093)
- [[`a920721175`](https://github.com/nodejs/node/commit/a920721175)] - **test**: silence compiler warning in openssl-binding (Daniel Bevenius) [#26067](https://github.com/nodejs/node/pull/26067)
- [[`2d0242a69b`](https://github.com/nodejs/node/commit/2d0242a69b)] - **test**: increase coverage for assertion_error.js (Rich Trott) [#26065](https://github.com/nodejs/node/pull/26065)
- [[`dd60cd60b3`](https://github.com/nodejs/node/commit/dd60cd60b3)] - **test**: add arg to narrow http benchmark test (Refael Ackermann) [#26101](https://github.com/nodejs/node/pull/26101)
- [[`fbf6dd558a`](https://github.com/nodejs/node/commit/fbf6dd558a)] - **test,inspector**: add heap allocation tracker test (Anna Henningsen) [#26089](https://github.com/nodejs/node/pull/26089)
- [[`db94ab778f`](https://github.com/nodejs/node/commit/db94ab778f)] - **test,worker**: posting undefined/null message to message port (legendecas) [#26123](https://github.com/nodejs/node/pull/26123)
- [[`d1e3724b5d`](https://github.com/nodejs/node/commit/d1e3724b5d)] - **test,worker**: add more tests for worker.ref()/.unref() (Anna Henningsen) [#26083](https://github.com/nodejs/node/pull/26083)
- [[`96a5765491`](https://github.com/nodejs/node/commit/96a5765491)] - **tools**: update extend to 3.0.2 (Rich Trott) [#26392](https://github.com/nodejs/node/pull/26392)
- [[`6e9a7e1048`](https://github.com/nodejs/node/commit/6e9a7e1048)] - **tools**: remove unneeded .gitignore entries (Rich Trott) [#26370](https://github.com/nodejs/node/pull/26370)
- [[`123fad6e1c`](https://github.com/nodejs/node/commit/123fad6e1c)] - **tools**: update babel-eslint to 10.0.1 (Rich Trott) [#26347](https://github.com/nodejs/node/pull/26347)
- [[`347dd99251`](https://github.com/nodejs/node/commit/347dd99251)] - **tools**: update eslint-plugin-markdown to 1.0.0 (Rich Trott) [#26345](https://github.com/nodejs/node/pull/26345)
- [[`adcbcf5bd6`](https://github.com/nodejs/node/commit/adcbcf5bd6)] - **tools**: use latest rather than next for markdown linting plugin (Rich Trott) [#26345](https://github.com/nodejs/node/pull/26345)
- [[`0080350f1a`](https://github.com/nodejs/node/commit/0080350f1a)] - **tools**: update markdown linter (Rich Trott) [#26281](https://github.com/nodejs/node/pull/26281)
- [[`dff0149d57`](https://github.com/nodejs/node/commit/dff0149d57)] - **tools**: update ESLint to 5.14.1 (cjihrig) [#26190](https://github.com/nodejs/node/pull/26190)
- [[`28d607444d`](https://github.com/nodejs/node/commit/28d607444d)] - **tools**: update ESLint to 5.14.0 (cjihrig) [#26142](https://github.com/nodejs/node/pull/26142)
- [[`1766b8c341`](https://github.com/nodejs/node/commit/1766b8c341)] - **trace_events**: fix trace events JS API writing (Kelvin Jin) [#24945](https://github.com/nodejs/node/pull/24945)
- [[`34c685b406`](https://github.com/nodejs/node/commit/34c685b406)] - **tracing**: use ‘real’ atomics (Anna Henningsen) [#26156](https://github.com/nodejs/node/pull/26156)
- [[`b6355ef602`](https://github.com/nodejs/node/commit/b6355ef602)] - **tty**: improve color detection (Ruben Bridgewater) [#26264](https://github.com/nodejs/node/pull/26264)
- [[`001785520a`](https://github.com/nodejs/node/commit/001785520a)] - **url**: handle quasi-WHATWG URLs in urlToOptions() (cjihrig) [#26226](https://github.com/nodejs/node/pull/26226)
- [[`6828fbb2ef`](https://github.com/nodejs/node/commit/6828fbb2ef)] - **(SEMVER-MINOR)** **util**: group array elements together (Ruben Bridgewater) [#26269](https://github.com/nodejs/node/pull/26269)
- [[`4500ed85e9`](https://github.com/nodejs/node/commit/4500ed85e9)] - **(SEMVER-MINOR)** **util**: add compact depth mode (Ruben Bridgewater) [#26269](https://github.com/nodejs/node/pull/26269)
- [[`34905fc2b9`](https://github.com/nodejs/node/commit/34905fc2b9)] - **util**: mark iterator entries as such (Ruben Bridgewater) [#26222](https://github.com/nodejs/node/pull/26222)
- [[`4bf58ac13d`](https://github.com/nodejs/node/commit/4bf58ac13d)] - **util**: update set iterator entries inspection (Ruben Bridgewater) [#25941](https://github.com/nodejs/node/pull/25941)
- [[`7d66d47dba`](https://github.com/nodejs/node/commit/7d66d47dba)] - **vm**: do not overwrite error when creating context (Anna Henningsen) [#26112](https://github.com/nodejs/node/pull/26112)
- [[`8cf4170c94`](https://github.com/nodejs/node/commit/8cf4170c94)] - **worker**: provide process.execArgv (Anna Henningsen) [#26267](https://github.com/nodejs/node/pull/26267)
- [[`6fdc502a32`](https://github.com/nodejs/node/commit/6fdc502a32)] - **worker**: make MessagePort `uv_async_t` inline field (Anna Henningsen) [#26271](https://github.com/nodejs/node/pull/26271)
- [[`51f01aa25b`](https://github.com/nodejs/node/commit/51f01aa25b)] - **worker**: remove MessagePort::AddToIncomingQueue (Anna Henningsen) [#26271](https://github.com/nodejs/node/pull/26271)
- [[`74d11e7d0e`](https://github.com/nodejs/node/commit/74d11e7d0e)] - **worker**: refactor thread life cycle management (Gireesh Punathil) [#26099](https://github.com/nodejs/node/pull/26099)
- [[`20dc172011`](https://github.com/nodejs/node/commit/20dc172011)] - **worker**: copy transferList ArrayBuffers on unknown allocator (Anna Henningsen) [#26207](https://github.com/nodejs/node/pull/26207)
- [[`7e7023373a`](https://github.com/nodejs/node/commit/7e7023373a)] - **worker**: serialize errors if stack getter throws (Rich Trott) [#26145](https://github.com/nodejs/node/pull/26145)
- [[`a9a2c5869c`](https://github.com/nodejs/node/commit/a9a2c5869c)] - **(SEMVER-MINOR)** **worker**: improve integration with native addons (Anna Henningsen) [#26175](https://github.com/nodejs/node/pull/26175)
- [[`dab3d71243`](https://github.com/nodejs/node/commit/dab3d71243)] - **worker**: ignore --abort-on-uncaught-exception for terminate() (Anna Henningsen) [#26111](https://github.com/nodejs/node/pull/26111)
- [[`dab64bb0e8`](https://github.com/nodejs/node/commit/dab64bb0e8)] - **worker**: spin uv_run twice before closing loop (Anna Henningsen) [#26138](https://github.com/nodejs/node/pull/26138)
- [[`24debc9d5c`](https://github.com/nodejs/node/commit/24debc9d5c)] - **worker**: do not add removed methods to MessagePort (Anna Henningsen) [#26109](https://github.com/nodejs/node/pull/26109)
- [[`8045e40917`](https://github.com/nodejs/node/commit/8045e40917)] - **worker**: remove duplicate call (Gireesh Punathil) [#26104](https://github.com/nodejs/node/pull/26104)
- [[`69298713af`](https://github.com/nodejs/node/commit/69298713af)] - **worker**: switch to internal assert module (Rich Trott) [#26091](https://github.com/nodejs/node/pull/26091)
- [[`77a944cdee`](https://github.com/nodejs/node/commit/77a944cdee)] - **worker**: use fake MessageEvent for port.onmessage (Anna Henningsen) [#26082](https://github.com/nodejs/node/pull/26082)
- [[`851a691678`](https://github.com/nodejs/node/commit/851a691678)] - **zlib**: report premature ends earlier (Anna Henningsen) [#26363](https://github.com/nodejs/node/pull/26363)

Windows 32-bit Installer: https://nodejs.org/dist/v11.11.0/node-v11.11.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v11.11.0/node-v11.11.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v11.11.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v11.11.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v11.11.0/node-v11.11.0.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v11.11.0/node-v11.11.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v11.11.0/node-v11.11.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v11.11.0/node-v11.11.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v11.11.0/node-v11.11.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v11.11.0/node-v11.11.0-aix-ppc64.tar.gz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v11.11.0/node-v11.11.0-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v11.11.0/node-v11.11.0-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v11.11.0/node-v11.11.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v11.11.0/node-v11.11.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v11.11.0/node-v11.11.0.tar.gz \
Other release files: https://nodejs.org/dist/v11.11.0/ \
Documentation: https://nodejs.org/docs/v11.11.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

74e0c2e1183a33e592854c203a64b2471fa68ce51ed49f25a7a2845233d653ee  node-v11.11.0-aix-ppc64.tar.gz
6b7c8d93096bf065de0d0a42e8f027c424172c82df6f015d3149a5dbfa03c1cc  node-v11.11.0-darwin-x64.tar.gz
2c668071582026f15409a514a131e1b58d6776e24957ed9183139898d091eb81  node-v11.11.0-darwin-x64.tar.xz
898f517553a6d9ad0ccb6355923502e1b79758b073a6a163ecb884fbd5b760fa  node-v11.11.0-headers.tar.gz
eb2487e866303a97a7b48433755e13be2cf1320df601c2f966e9a79e4fb51100  node-v11.11.0-headers.tar.xz
2eeebc2323bdc91493cc2f485b140e4034bb37e52ce3ea0caa6e80f4a9fb64b0  node-v11.11.0-linux-arm64.tar.gz
febb698e74264ec0ecc738c86347e6bfb5d27e33ed492a031626df988a5fb4f3  node-v11.11.0-linux-arm64.tar.xz
d5ed0f63f0ed79a5fbd86b351ccd760ad7260360f5ce4f3dba5946590c0e9180  node-v11.11.0-linux-armv6l.tar.gz
eb9e192954b0b429a99e9497cb73bab95f2f0c7138e447b2ac4fe95971817691  node-v11.11.0-linux-armv6l.tar.xz
4de6250ae9974ae0830405b6a47f007725ecd868bacc760d44eeed5453b5ad03  node-v11.11.0-linux-armv7l.tar.gz
c6428db25944ffacda54e321cf15327efe000dc721480329992ef265da5d24b2  node-v11.11.0-linux-armv7l.tar.xz
e65427840c8ccdc7caaef3f750bfa6dc3d28ba850e988420afc7651e42011050  node-v11.11.0-linux-ppc64le.tar.gz
1a2946346f86f220f5077ff5a7c373d2d99b74fe41567844bb456d557ee7d1c7  node-v11.11.0-linux-ppc64le.tar.xz
66367054ad22d5529c9d06bf467ee79cade507e3f715f10ae40302c4d4683018  node-v11.11.0-linux-s390x.tar.gz
65aee43ca942e334c12166f9978e3ec73c7ecf6425f5f2d969299acc4e05bbde  node-v11.11.0-linux-s390x.tar.xz
f749e64a56dc71938fa5d2774b4e53068d19ad9f48b4a62257633b25459bffa6  node-v11.11.0-linux-x64.tar.gz
ee09c7712e741f84948f76a72b651bf088e516c3f38569b9e6f30c38c026c262  node-v11.11.0-linux-x64.tar.xz
34290b4add54db600489634162f66e3f79e866e52994a3eaf8081c231ee13438  node-v11.11.0-sunos-x64.tar.gz
2b823095e26528ec36ea44182d948f581f56664d78902f35ddf829271fd9fe80  node-v11.11.0-sunos-x64.tar.xz
29a89097988f5ae8b004028ba681b37d83c953e00f9c002bfc3f0248c0df23f9  node-v11.11.0-win-x64.7z
154a5b7ca7033382743c4752fa465a5d9902f4abed4263a38ea3f41ffa113910  node-v11.11.0-win-x64.zip
33ba369c417fb4f567ba65b394edece1efb269ec38ab3b75991f98f896f4e236  node-v11.11.0-win-x86.7z
175a0dabe78a4983ec24a2c4c776dee46feaa9547383dee80d06cdc94b7eccc0  node-v11.11.0-win-x86.zip
215473345ea3893790b01588170e795070ab769533556f2c7baa3fa9bfac4b4e  node-v11.11.0-x64.msi
495a3c9438d69f7f00aa6b3dcb01f9079d16f093f0ccf17595d60b497ee78a07  node-v11.11.0-x86.msi
36c8172c007bffcca95a60c1e65ec572126342a43c14fcb5d2cf6555036ce484  node-v11.11.0.pkg
8cbf2c62359901a5587fcc6699200495490751ce6fb31255c788ac6eb90a1107  node-v11.11.0.tar.gz
a6d8eaf900eebd43f1c419649a98a5a90bd8a5101e9fc9474048fd5ed296629c  node-v11.11.0.tar.xz
76179b4214504de7104884ea5c731d2507647a11bc033f59f62c929c81f477ca  win-x64/node.exe
d0253c66b35fd33038d562e31d6d99badb4eda0742d0de917fc85a03b37515e5  win-x64/node.lib
5a8f767c6606f4daf2123cdaef2e393984f7381761aa38224dca4ed1432efd00  win-x64/node_pdb.7z
b9685da335c008cb4c16e1a06507c9f4e03156a53180acdecc84d3e21dd89504  win-x64/node_pdb.zip
764af434e12b6a698c08c5441da7a1358850102d77f11edea700e3a3a6ebaa71  win-x86/node.exe
8f296a7bf120d9d9f9d98aa7f5e2293a2ed64bd9beae75899d7bc92b14c589a2  win-x86/node.lib
fb6eec3325db215790aee2cca648ffb7e6ef9c089748185fbd5542550f2e48b8  win-x86/node_pdb.7z
23b8bff4f428a9f5b3e20b96d9b7c9a8be63ee9f2685eedb2320a9288a41740d  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEpIwr7mgOhBYyzU5E8HSWs+s8F2IFAlyAH+wACgkQ8HSWs+s8
F2LimQ//Soq290/kv162dCXxL4Q6D0oP6B2AOlo/mSJyimr8IhgDKb7g4+/3Zi/W
SHwQ3zoEBnf5LWNyloQKs4kmJfInAKE4p1GN4FwjbCuIJRdmddXq9CZfzjju4wc6
UK1N+ZpdhTR8hI1cieSM0FaAbk5/KmssqgG17eTZcRMeiu9OQx/RIPK9HD3sc5ml
eXzg4G/uzMrGpisY9yuK4y8DWB0sw4Dwk/fYdemzLK3XQGgvPax70hnURzl9tLvq
h+ZKvxdxxKVBxTmEH6kMWmEYCigrr3VdbxQK+LorV6piqlbBtpppWLY/EM9ptwro
6FK85gRtf9ldDgaRVabsTJgOygoGXI+DgyuvwDJdKNSPcDFrXy31gpzeOdUV8Tfs
dJxPPhPF6SJPTEFnAmL3QoJUOzDGwLbz8/VKfbd+iZwJGXAj4+1bQnGsO3Zz80y+
CfIUntYFo6+Uhj2FhbrkG/UPy/iI3+H64PrjtLCbevx/7pAYy2a6k5j3nwrjXqtX
n9Jk4gNAU7k2jZKEzwgPwqZEbD3WbNAe0idqmCHWeXLwQwHOHmHRHKw2nG4//MEt
VO0+FlrwWPfetcICR1IDSkHCI/eXyXrZb+Ji630twPJ+e/u23S88Lo0HVMwhxHrZ
qeKc2AyzwA9Iznrgda7ydQ1d4olKbYH082j3ZNnTU3E7NKTR58o=
=GjgQ
-----END PGP SIGNATURE-----

```
