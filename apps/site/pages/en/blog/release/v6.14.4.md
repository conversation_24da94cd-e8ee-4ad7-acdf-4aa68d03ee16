---
date: '2018-08-16T01:53:09.713Z'
category: release
title: Node v6.14.4 (LTS)
layout: blog-post
author: <PERSON>
---

**This is a security release, fixing a number of vulnerabilities in OpenSSL and Node.js.** Refer to the [August 2018 Security Releases](/blog/vulnerability/august-2018-security-releases/) announcement for full details.

### Notable Changes

- **buffer**: Fix out-of-bounds (OOB) write in `Buffer.write()` for UCS-2 encoding (CVE-2018-12115)
- **deps**: Upgrade to OpenSSL 1.0.2p, fixing:
  - Client DoS due to large DH parameter (CVE-2018-0732)
  - ECDSA key extraction via local side-channel (CVE not assigned)

### Commits

- [[`0052926476`](https://github.com/nodejs/node/commit/0052926476)] - **buffer**: avoid overrun on UCS-2 string write (<PERSON> Vagg) [nodejs-private/node-private#138](https://github.com/nodejs-private/node-private/pull/138)
- [[`dbe6551b89`](https://github.com/nodejs/node/commit/dbe6551b89)] - **deps**: add -no_rand_screen to openssl s_client (Shigeki Ohtsu) [#1836](https://github.com/nodejs/node/pull/1836)
- [[`7829bbcacb`](https://github.com/nodejs/node/commit/7829bbcacb)] - **deps**: fix asm build error of openssl in x86_win32 (Shigeki Ohtsu) [#1389](https://github.com/nodejs/node/pull/1389)
- [[`cddca629b5`](https://github.com/nodejs/node/commit/cddca629b5)] - **deps**: fix openssl assembly error on ia32 win32 (Fedor Indutny) [#1389](https://github.com/nodejs/node/pull/1389)
- [[`e6014aed52`](https://github.com/nodejs/node/commit/e6014aed52)] - **deps**: copy all openssl header files to include dir (Shigeki Ohtsu) [#22320](https://github.com/nodejs/node/pull/22320)
- [[`37ddce514d`](https://github.com/nodejs/node/commit/37ddce514d)] - **deps**: upgrade openssl sources to 1.0.2p (Shigeki Ohtsu) [#22320](https://github.com/nodejs/node/pull/22320)
- [[`08a150fcca`](https://github.com/nodejs/node/commit/08a150fcca)] - **inspector**: don't bind to 0.0.0.0 by default (Ben Noordhuis) [#21376](https://github.com/nodejs/node/pull/21376)
- [[`19b9d7fd77`](https://github.com/nodejs/node/commit/19b9d7fd77)] - **openssl**: fix keypress requirement in apps on win32 (Shigeki Ohtsu) [#1389](https://github.com/nodejs/node/pull/1389)
- [[`7ccb0422fc`](https://github.com/nodejs/node/commit/7ccb0422fc)] - **test**: fix error messages for OpenSSL-1.0.2p (Shigeki Ohtsu) [#22320](https://github.com/nodejs/node/pull/22320)
- [[`58b9497ca8`](https://github.com/nodejs/node/commit/58b9497ca8)] - **test**: update certificates and private keys (Fedor Indutny) [#22184](https://github.com/nodejs/node/pull/22184)
- [[`9863e11ea8`](https://github.com/nodejs/node/commit/9863e11ea8)] - **test**: update keys/Makefile to clean and build all (Daniel Bevenius) [#19975](https://github.com/nodejs/node/pull/19975)

Windows 32-bit Installer: https://nodejs.org/dist/v6.14.4/node-v6.14.4-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v6.14.4/node-v6.14.4-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v6.14.4/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v6.14.4/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v6.14.4/node-v6.14.4.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v6.14.4/node-v6.14.4-darwin-x64.tar.gz \
Linux 32-bit Binary: https://nodejs.org/dist/v6.14.4/node-v6.14.4-linux-x86.tar.xz \
Linux 64-bit Binary: https://nodejs.org/dist/v6.14.4/node-v6.14.4-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v6.14.4/node-v6.14.4-linux-ppc64le.tar.xz \
Linux PPC BE 64-bit Binary: https://nodejs.org/dist/v6.14.4/node-v6.14.4-linux-ppc64.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v6.14.4/node-v6.14.4-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v6.14.4/node-v6.14.4-aix-ppc64.tar.gz \
SmartOS 32-bit Binary: https://nodejs.org/dist/v6.14.4/node-v6.14.4-sunos-x86.tar.xz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v6.14.4/node-v6.14.4-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v6.14.4/node-v6.14.4-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v6.14.4/node-v6.14.4-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v6.14.4/node-v6.14.4-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v6.14.4/node-v6.14.4.tar.gz \
Other release files: https://nodejs.org/dist/v6.14.4/ \
Documentation: https://nodejs.org/docs/v6.14.4/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

91ba62cef4f8d2d3f4d4764a7116ba1eae963a4e8847290ac599dfc459ab1058  node-v6.14.4-aix-ppc64.tar.gz
799c10d67b42962ce62673d92a8352a3a2f69fc0aa3723fa0cb62522d0af0687  node-v6.14.4-darwin-x64.tar.gz
2a20a4fda9fa197e9c4b82d3eed7714e58cf002cd39effbb4886232e6615ca94  node-v6.14.4-darwin-x64.tar.xz
f6baca4f7dfad1de06f4389e67bf599b90d7daf4fdd180d29d10abd6ec56559e  node-v6.14.4-headers.tar.gz
bf248b14f3c0bf178a41f524edef6f95003369231ae28539f4bf4529a67c6428  node-v6.14.4-headers.tar.xz
08d5af19fb0abe879ee9a62a1243cb027acbedae1b4fa5498a6183cc458773ee  node-v6.14.4-linux-arm64.tar.gz
fa1a78ae8e384c1b76bde41060e806c412ecd9b4c0775c5c7ec53ebf9a70998b  node-v6.14.4-linux-arm64.tar.xz
63a5b6b2da949f2c45c2b2615e17c757c86080470da425477adc6291252aaf8c  node-v6.14.4-linux-armv6l.tar.gz
771a2a6124dc55ea019e3af92f84090694e25cfb4e4bee10a0b9c27802f6adb9  node-v6.14.4-linux-armv6l.tar.xz
88f5ec52dbbf547a52af66b7df5fbb6206e43a307a410805eeba08bc13c45a2d  node-v6.14.4-linux-armv7l.tar.gz
21f2e3c729417de1b7394217404ffb99dc3af194680970ba02b2eb065b0eb5d4  node-v6.14.4-linux-armv7l.tar.xz
2d15a78a2b47dba29890be40bc1397807a8c1176a33ce8d8c73db20036430652  node-v6.14.4-linux-ppc64le.tar.gz
097656c05ed29d43c5fc629269137ed9a5b64a6e07f5f9f4d870ca03eec9802e  node-v6.14.4-linux-ppc64le.tar.xz
00d988391f8083f1da3b699734d2fb6e3fca0c91d5404ab402cd14ca4ba22af4  node-v6.14.4-linux-ppc64.tar.gz
8d07a9f3d788f6dd1c64749f0a0a7b9e9af733fb729e5d7668862e4be5511cf1  node-v6.14.4-linux-ppc64.tar.xz
1f129a089acd6efa01a99dd068478482314151d95d53168e4ef1c7b20c1fa4bc  node-v6.14.4-linux-s390x.tar.gz
bb8dd6a71d308ecec753251ea1b830008f3b1126b49de4cd22806b6d6edd77f9  node-v6.14.4-linux-s390x.tar.xz
1b80ddc7847e85ae31c5eb515ee76230fed1e2e70303a7db9891404a830128ba  node-v6.14.4-linux-x64.tar.gz
66d052fe10f90e3b05fdda3117f26bc24da10d436eecf6e298c317f437647aab  node-v6.14.4-linux-x64.tar.xz
d394ea61a51ba3c070838f8696a38f9e47be55af6978e7a074addb742ba0cddc  node-v6.14.4-linux-x86.tar.gz
ba508f7cad8fab34ef3d72c64b32e06dc7c65a911e14eaf985beeae90975bb06  node-v6.14.4-linux-x86.tar.xz
487c425a78f5f82a3c480007664b6a670af22d7ee719bd7e5a25c8c6950d2400  node-v6.14.4.pkg
d01a474d78b08fe69a6d0c86742f77ebf98c106ce5e469e2f34ecdcb3556f49a  node-v6.14.4-sunos-x64.tar.gz
e87024933e826d3b2c3b392a9c36324b977071ba6791c2093b35ba10eeee4d7a  node-v6.14.4-sunos-x64.tar.xz
f51c0fea195a25bf442a2beb27b62a56e3bc765390354bcd5643178ee8566037  node-v6.14.4-sunos-x86.tar.gz
67dc68a5ac1f870f76e27e726895fa2c0fee93e01af7ac04eee4cd133f8fbb6c  node-v6.14.4-sunos-x86.tar.xz
10aff5317908af04107e9ae26a07a84ee11a213657701df05c189afa4586cf37  node-v6.14.4.tar.gz
9a4bfc99787f8bdb07d5ae8b1f00ec3757e7b09c99d11f0e8a5e9a16a134ec0f  node-v6.14.4.tar.xz
4f5b078447bc5d293e3b2a418f14cd843cad408740d646358200a21d01b1f59c  node-v6.14.4-win-x64.7z
6f9dc686dac27e13f142626018d7bf23257356b40a7eabbf5c1843acc1ab80f9  node-v6.14.4-win-x64.zip
b9595f7822cf55d1fd2db70ec6cde91a29960a86fdf25b365b479c03b63882c5  node-v6.14.4-win-x86.7z
cba294e7660099d10b76b239e716995a75e15fbebaf5467ac75ca9b506435927  node-v6.14.4-win-x86.zip
70ab33aa352fa5d9c9c0b12070404e688d03b15d28cfa55ec719f2c4fd095001  node-v6.14.4-x64.msi
260472d50d282574bb8b01378c5c8509613ae77b54d2d22a78fb92bd71634291  node-v6.14.4-x86.msi
fd8a5ec2e4d6f67895aeb9ee07af4c562331d19e4e2e644f974f347ede72453c  win-x64/node.exe
98173b6cb0848827a630a981eb6ae352c2494a3970bcbf0466382dbfddc2f159  win-x64/node.lib
e53037a68e15a73be916845b687696bb73f448f9842458ac41cedf44a682bfb3  win-x64/node_pdb.7z
f77a8848ec7868f0c963b5a74170f0e64e205f1e2c27ab2be309a014f2a75da3  win-x64/node_pdb.zip
7f93a13e70c79fa27c841daf7fe8ebdc3ddf34b3f17ceba66d5ffc9d1594ea89  win-x86/node.exe
dcef85f280ee81fdc1f469a20354124eb2ff83c5877f40a9b89c09471f53abd4  win-x86/node.lib
80062110f0cbbe94f934c709e3256471b73c71411908cfc986b7e125dca66831  win-x86/node_pdb.7z
d7a22eeb96adbf2da748fb323ce9e989b74936159014f86c714e077f3f4e658d  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQEzBAEBCAAdFiEE3Y8jOLrnUB491ax4wnN5L32DVF0FAlt01zQACgkQwnN5L32D
VF3c8Qf/dZSQtJDH1Ow7Apf1pESSBhB0kzQXokn9ovQfFdzzc4jYSu7StFdMDOtK
pijf6ZpAigjH0PsAFGU7p2nCDQhen6/J3SUNUCz/oxZ+ox9aR436ZUjmKcV4/ECr
Lmse7B7z1BLvSWucZQ45d9jrPG9tjVkybXgzcd8IxsFrEFOdY1WaSnJRI56uHzaW
Hvw9erXdiQHhyTXBsZcM/5+AN7WSnVccwKXdfcHi1B7MdDd7NucQYmj5cQz7Dz0p
qWKvgYqdtd2H4ZFWX8z8lbswtWeftNYP1+nGEBPC1IpFEzLQ+HPXSjpeccAFmS6e
8Y8mEfBsbRQVObRbU5dNeNOwI6xsKA==
=6h1+
-----END PGP SIGNATURE-----

```
