---
date: '2020-02-06T03:13:05.970Z'
category: release
title: Node v12.15.0 (LTS)
layout: blog-post
author: <PERSON>
---

### Notable changes

This is a security release.

Vulnerabilities fixed:

- **CVE-2019-15606**: HTTP header values do not have trailing OWS trimmed.
- **CVE-2019-15605**: HTTP request smuggling using malformed Transfer-Encoding header.
- **CVE-2019-15604**: Remotely trigger an assertion on a TLS server with a malformed certificate string.

Also, HTTP parsing is more strict to be more secure. Since this may
cause problems in interoperability with some non-conformant HTTP
implementations, it is possible to disable the strict checks with the
`--insecure-http-parser` command line flag, or the `insecureHTTPParser`
http option. Using the insecure HTTP parser should be avoided.

### Commits

- [[`209767c7a2`](https://github.com/nodejs/node/commit/209767c7a2)] - **benchmark**: support optional headers with wrk (<PERSON>) [nodejs-private/node-private#189](https://github.com/nodejs-private/node-private/pull/189)
- [[`02c8905051`](https://github.com/nodejs/node/commit/02c8905051)] - **crypto**: fix assertion caused by unsupported ext (Fedor Indutny) [nodejs-private/node-private#175](https://github.com/nodejs-private/node-private/pull/175)
- [[`25d6011912`](https://github.com/nodejs/node/commit/25d6011912)] - **deps**: update llhttp to 2.0.4 (Beth Griggs) [nodejs-private/llhttp-private#1](https://github.com/nodejs-private/llhttp-private/pull/1)
- [[`8162f0e194`](https://github.com/nodejs/node/commit/8162f0e194)] - **deps**: upgrade http-parser to v2.9.3 (Sam Roberts) [nodejs-private/http-parser-private#4](https://github.com/nodejs-private/http-parser-private/pull/4)
- [[`d41314ef99`](https://github.com/nodejs/node/commit/d41314ef99)] - **(SEMVER-MINOR)** **deps**: upgrade http-parser to v2.9.1 (Sam Roberts) [#30473](https://github.com/nodejs/node/pull/30473)
- [[`7fc565666c`](https://github.com/nodejs/node/commit/7fc565666c)] - **(SEMVER-MINOR)** **http**: make --insecure-http-parser configurable per-stream or per-server (Anna Henningsen) [#31448](https://github.com/nodejs/node/pull/31448)
- [[`496736ff78`](https://github.com/nodejs/node/commit/496736ff78)] - **(SEMVER-MINOR)** **http**: opt-in insecure HTTP header parsing (Sam Roberts) [#30567](https://github.com/nodejs/node/pull/30567)
- [[`76fd8910e9`](https://github.com/nodejs/node/commit/76fd8910e9)] - **http**: strip trailing OWS from header values (Sam Roberts) [nodejs-private/node-private#189](https://github.com/nodejs-private/node-private/pull/189)
- [[`9cd155eb4a`](https://github.com/nodejs/node/commit/9cd155eb4a)] - **test**: using TE to smuggle reqs is not possible (Sam Roberts) [nodejs-private/node-private#192](https://github.com/nodejs-private/node-private/pull/192)
- [[`ab1fcb89cb`](https://github.com/nodejs/node/commit/ab1fcb89cb)] - **test**: check that --insecure-http-parser works (Sam Roberts) [#31253](https://github.com/nodejs/node/pull/31253)

Windows 32-bit Installer: https://nodejs.org/dist/v12.15.0/node-v12.15.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v12.15.0/node-v12.15.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v12.15.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v12.15.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v12.15.0/node-v12.15.0.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v12.15.0/node-v12.15.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v12.15.0/node-v12.15.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v12.15.0/node-v12.15.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v12.15.0/node-v12.15.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v12.15.0/node-v12.15.0-aix-ppc64.tar.gz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v12.15.0/node-v12.15.0-sunos-x64.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v12.15.0/node-v12.15.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v12.15.0/node-v12.15.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v12.15.0/node-v12.15.0.tar.gz \
Other release files: https://nodejs.org/dist/v12.15.0/ \
Documentation: https://nodejs.org/docs/v12.15.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

4674d94402b8c0d79a9e20d4320652830032f0a7171cea2c815858b76d3d2ee9  node-v12.15.0-aix-ppc64.tar.gz
b6449cec39ac15b37abe4e59ef0eae50dcdfbf060c5276a01cc590f2a3372b7d  node-v12.15.0-darwin-x64.tar.gz
fc2b68255c61663fe74338d372678fbb1fec367fef46924498e6d1b5e220c0b1  node-v12.15.0-darwin-x64.tar.xz
48e8ba40339e6cb9edc820f320b690b3401643e2c6bd36a7f2267ebf84cf98f2  node-v12.15.0-headers.tar.gz
44217fee8c0d003888783f65c36eec871d36b93d7347333b659633e30cbd85db  node-v12.15.0-headers.tar.xz
9349bb00a522da9ecd0d2f9453b500904ccd56e271852ab2defb51a8c77a1aca  node-v12.15.0-linux-arm64.tar.gz
c582cb65a0ec7f648618d3d33b4f87c374a3f930518b57eca1693828c965d6e5  node-v12.15.0-linux-arm64.tar.xz
53c4b21b23b8dc31da0ab9b5bdae7041d87fd7b7247e0b6cecaca36ca1d7e05d  node-v12.15.0-linux-armv7l.tar.gz
a865e69914c568fcb28be7a1bf970236725a06a8fc66530799300181d2584a49  node-v12.15.0-linux-armv7l.tar.xz
91c4d019e2c86d088fcb0874f3dab6a074c354d4fe0fcce1fa2f535c86396f5e  node-v12.15.0-linux-ppc64le.tar.gz
9b0c4faf637e9cd272964052933e7ff2ca05c50d89a473cf6f40031f55783fd0  node-v12.15.0-linux-ppc64le.tar.xz
3594746ef6a98d21e56b4f1c7a60526c53fd97750c9ba2347ffc3df24ea3b21a  node-v12.15.0-linux-s390x.tar.gz
30f150720cc1a9f53d4d9a881961431428603dfe151cd487cdaec555f29db0c7  node-v12.15.0-linux-s390x.tar.xz
218279a33603b8bc958c46cce04c14851fd9d685bd21f5a39d6b98d08d80aae5  node-v12.15.0-linux-x64.tar.gz
63df953deb091c1500e1044bef01d1953117970e757e74e90d915e1a4a0d1c9c  node-v12.15.0-linux-x64.tar.xz
f3c3103bcceb33898a8bc44c54d337c5bcb709dcdca95408e9307b6d4a86ca31  node-v12.15.0.pkg
4783ba63bcdfb8fa54cd4526401051df32835dc112cf7c11d64d98451ece20a6  node-v12.15.0-sunos-x64.tar.gz
b14aab80ccdf268f6347b17bd449217139fe46df3cceac00960e59ef835cec8b  node-v12.15.0-sunos-x64.tar.xz
fb357de9262f60425ea3970dd09afd63312006a7b8355a808d358694e867f4fd  node-v12.15.0.tar.gz
d2fb4fa80ccf321570552b0a6e6b5f2aedeb281a8450207b057cf54c54d5a81b  node-v12.15.0.tar.xz
5b7d4beb9fc4c16b08b06de394b738caeec94f3d22d38f78ca02f386b9897f83  node-v12.15.0-win-x64.7z
48b29cab597962f12b0aac081522e6192bc8642c582cd0fc1bf51557273888da  node-v12.15.0-win-x64.zip
a033ff4501155b3f0d63f08a40bf02d3bebd98485895ffd0cb638c6071eb6f6c  node-v12.15.0-win-x86.7z
e76057fda953d964e9f53c1a210ca4daf7104072574744616eb079e053a0d3d9  node-v12.15.0-win-x86.zip
615fe16ec99ebb7de92cb0e1c343979f7ac9d943373e61cff17cd6a9f76547e8  node-v12.15.0-x64.msi
f558f19deea306c6caf167a687fe1ee65c4b191b1d327188c7f0d9fa7966d818  node-v12.15.0-x86.msi
51d09b8ce41fa3e6b7206141609e52e92f435b99e44932592cd529a9463bbf17  win-x64/node.exe
bd0c3ac36b1e484ddbc3de5e04abe399c1221057fabf16e74155b44af012504d  win-x64/node.lib
9a644dfb80d9d6a4e01e83993b8c5de219130a39e5324ea78696d0ca7303c3bb  win-x64/node_pdb.7z
7f7e3cff51f3d475e9f5427a74968cb83b2ef9625bcea006a9ac8ea1bc4fdf96  win-x64/node_pdb.zip
839ef38f0ed23a9dcca4f0a807e205f95e26370f35ec9ea9915721c06eda04f2  win-x86/node.exe
95fd2202f2ba9695cf4db59e932f50d3c96052e1d171797dfa955e348d96df1f  win-x86/node.lib
d3659e3381a6c6fa7eec2ee0b340bc6049e47f23c055ef0d724d25bacabe4fdc  win-x86/node_pdb.7z
cf15fcc38600ef4dc89c9a6e9eeb58a44ca35f9a59d9823dd0f839a8d6ee95d4  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQEzBAEBCAAdFiEETtd49TnjY0x3nIfG1wYoSKGrAFwFAl47g5kACgkQ1wYoSKGr
AFxTTAf+KUOw9HAEJkE+e6VF3S3bnadaT0ItS42lyOIqaRgeIVh15HR3vi4+KBc3
3ifsE7b8blLBFiyX/TQWl4prlK/fRvPmw9Q6VMpiu/qqdiwGFncAjhGiJBEJBGFs
9jdQJbHnL0n7UTNQcBPljWEk9JHvyExRFn+WFY52VEV6R2jO1FtWoMEwj1HEA73Y
ICiwVss0gduFiYVrBghLtC7BaI/gtf3Z5XTon07cB+cXXQqnZJCSLDitXGdk7Iwk
kHmTrLligB0iWnNffQtAkfvjqXJith9Ya2hPQhZJsdz16U+L3K5q59s6jeOdrgv9
L1vXMNIioydW/l9BcN4FjTrhm35CSg==
=oRsp
-----END PGP SIGNATURE-----

```
