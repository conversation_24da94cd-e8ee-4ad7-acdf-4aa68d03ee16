---
date: '2017-03-29T03:06:10.686Z'
category: release
title: Node v7.8.0 (Current)
layout: blog-post
author: <PERSON><PERSON>
---

### Notable Changes

- **buffer**:
  - do not segfault on out-of-range index (<PERSON>) [#11927](https://github.com/nodejs/node/pull/11927)
- **crypto**:
  - Fix memory leak if certificate is revoked (<PERSON>) [#12089](https://github.com/nodejs/node/pull/12089)
- **deps**:
  - upgrade npm to 4.2.0 (<PERSON>) [#11389](https://github.com/nodejs/node/pull/11389)
  - fix async await desugaring in V8 (<PERSON><PERSON><PERSON>) [#12004](https://github.com/nodejs/node/pull/12004)
- **readline**:
  - add option to stop duplicates in history (<PERSON>) [#2982](https://github.com/nodejs/node/pull/2982)

### Commits

- [[`51c8d8088a`](https://github.com/nodejs/node/commit/51c8d8088a)] - Partial revert "tls: keep track of stream that is closed" (<PERSON>) [#11947](https://github.com/nodejs/node/pull/11947)
- [[`751c1153a4`](https://github.com/nodejs/node/commit/751c1153a4)] - **benchmark**: check end() argument to be \> 0 (Vse Mozhet Byt) [#12030](https://github.com/nodejs/node/pull/12030)
- [[`210250465a`](https://github.com/nodejs/node/commit/210250465a)] - **benchmark**: update obsolete information pointer (Rich Trott) [#12026](https://github.com/nodejs/node/pull/12026)
- [[`7aeeee3276`](https://github.com/nodejs/node/commit/7aeeee3276)] - **benchmark**: repair the fs/readfile benchmark (Sorin Baltateanu) [#7818](https://github.com/nodejs/node/pull/7818)
- [[`90acb773be`](https://github.com/nodejs/node/commit/90acb773be)] - **benchmark**: allow multiple values for same config (Nikolai Vavilov) [#11819](https://github.com/nodejs/node/pull/11819)
- [[`2f4ad6fea2`](https://github.com/nodejs/node/commit/2f4ad6fea2)] - **benchmark**: harmonize progress bar + stderr output (Vse Mozhet Byt) [#11925](https://github.com/nodejs/node/pull/11925)
- [[`d62ddbe145`](https://github.com/nodejs/node/commit/d62ddbe145)] - **benchmark**: fix fs\bench-realpathSync.js (Vse Mozhet Byt) [#11904](https://github.com/nodejs/node/pull/11904)
- [[`85eb1bc0a9`](https://github.com/nodejs/node/commit/85eb1bc0a9)] - **benchmark**: remove v8ForceOptimization calls (Lucas Lago) [#11908](https://github.com/nodejs/node/pull/11908)
- [[`17d16e8f3d`](https://github.com/nodejs/node/commit/17d16e8f3d)] - **buffer**: remove unneeded eslint-disable comment (Rich Trott) [#11906](https://github.com/nodejs/node/pull/11906)
- [[`fb41ee3983`](https://github.com/nodejs/node/commit/fb41ee3983)] - **build**: add lint option to vcbuild.bat help (Morgan Brenner) [#11992](https://github.com/nodejs/node/pull/11992)
- [[`3e4ecca0be`](https://github.com/nodejs/node/commit/3e4ecca0be)] - **build**: don't create directory for NDK toolchain (TheBeastOfCaerbannog) [#11916](https://github.com/nodejs/node/pull/11916)
- [[`a64aa442c1`](https://github.com/nodejs/node/commit/a64aa442c1)] - **crypto**: fix memory leak if certificate is revoked (Tom Atkinson) [#12089](https://github.com/nodejs/node/pull/12089)
- [[`2767e2d3cc`](https://github.com/nodejs/node/commit/2767e2d3cc)] - **(SEMVER-MINOR)** **deps**: upgrade npm to 4.2.0 (Kat Marchán) [#11389](https://github.com/nodejs/node/pull/11389)
- [[`d22346de40`](https://github.com/nodejs/node/commit/d22346de40)] - **deps**: fix async await desugaring in V8 (Michaël Zasso) [#12004](https://github.com/nodejs/node/pull/12004)
- [[`fade55b025`](https://github.com/nodejs/node/commit/fade55b025)] - **doc**: clarify out-of-bounds behavior of buf\[index\] (Nikolai Vavilov) [#11286](https://github.com/nodejs/node/pull/11286)
- [[`63a19c7012`](https://github.com/nodejs/node/commit/63a19c7012)] - **doc**: update and modernize examples in fs.ms (Vse Mozhet Byt) [#12035](https://github.com/nodejs/node/pull/12035)
- [[`4b5f177e3d`](https://github.com/nodejs/node/commit/4b5f177e3d)] - **doc**: fix https.timeout docs (Ahmad Nassri) [#12039](https://github.com/nodejs/node/pull/12039)
- [[`af051f6528`](https://github.com/nodejs/node/commit/af051f6528)] - **doc**: fix http properties documented as methods (Ahmad Nassri) [#12039](https://github.com/nodejs/node/pull/12039)
- [[`18a586a278`](https://github.com/nodejs/node/commit/18a586a278)] - **doc**: edit the benchmark guide (Rich Trott) [#12041](https://github.com/nodejs/node/pull/12041)
- [[`5e3d429613`](https://github.com/nodejs/node/commit/5e3d429613)] - **doc**: stdout/err/in are all Duplex streams (Sebastian Van Sande) [#11194](https://github.com/nodejs/node/pull/11194)
- [[`7f6b03fd0f`](https://github.com/nodejs/node/commit/7f6b03fd0f)] - **doc**: fix process.stdout fd number (Fumiya KARASAWA) [#12055](https://github.com/nodejs/node/pull/12055)
- [[`1f7fe55c97`](https://github.com/nodejs/node/commit/1f7fe55c97)] - **doc**: add richardlau to collaborators (Richard Lau) [#12020](https://github.com/nodejs/node/pull/12020)
- [[`924f34606d`](https://github.com/nodejs/node/commit/924f34606d)] - **doc**: update collaborator email address (Rich Trott) [#11996](https://github.com/nodejs/node/pull/11996)
- [[`41bec5cff4`](https://github.com/nodejs/node/commit/41bec5cff4)] - **doc**: correct info in child_process.md (Vse Mozhet Byt) [#11949](https://github.com/nodejs/node/pull/11949)
- [[`96ad336d9e`](https://github.com/nodejs/node/commit/96ad336d9e)] - **doc**: remove superfluous sample assert code (Rich Trott) [#11933](https://github.com/nodejs/node/pull/11933)
- [[`486bd1bd9b`](https://github.com/nodejs/node/commit/486bd1bd9b)] - **doc**: require uses fs root for '/' prefix (Bradley Farias) [#11897](https://github.com/nodejs/node/pull/11897)
- [[`04fa28e6dc`](https://github.com/nodejs/node/commit/04fa28e6dc)] - **doc**: fix gitter badge in README (Roman Reiss) [#11944](https://github.com/nodejs/node/pull/11944)
- [[`68b23be51f`](https://github.com/nodejs/node/commit/68b23be51f)] - **doc**: add missing word in stream.md (Jyotman Singh) [#11914](https://github.com/nodejs/node/pull/11914)
- [[`0f2642ee36`](https://github.com/nodejs/node/commit/0f2642ee36)] - **errors**: remove needless lazyAssert (DavidCai) [#11891](https://github.com/nodejs/node/pull/11891)
- [[`5bdd54925a`](https://github.com/nodejs/node/commit/5bdd54925a)] - **lib**: add comment to script eval \_tickCallback (Gibson Fahnestock) [#12050](https://github.com/nodejs/node/pull/12050)
- [[`7347860966`](https://github.com/nodejs/node/commit/7347860966)] - **lib**: clarify the usage of 'else' (Jackson Tian) [#11148](https://github.com/nodejs/node/pull/11148)
- [[`837ff4ba59`](https://github.com/nodejs/node/commit/837ff4ba59)] - **lib**: remove an unnecessary coverage check (Jeremiah Senkpiel) [#12023](https://github.com/nodejs/node/pull/12023)
- [[`6c803db7b9`](https://github.com/nodejs/node/commit/6c803db7b9)] - **lib**: fix event race condition with -e (Ben Noordhuis) [#11958](https://github.com/nodejs/node/pull/11958)
- [[`ac92d0249b`](https://github.com/nodejs/node/commit/ac92d0249b)] - **net**: refactor net module to module.exports (Claudio Rodriguez) [#11698](https://github.com/nodejs/node/pull/11698)
- [[`2462fd8009`](https://github.com/nodejs/node/commit/2462fd8009)] - **process**: maintain constructor descriptor (Bryan English) [#9306](https://github.com/nodejs/node/pull/9306)
- [[`91a2700721`](https://github.com/nodejs/node/commit/91a2700721)] - **readline**: rename `deDupeHistory` option (Danny Nemer) [#11950](https://github.com/nodejs/node/pull/11950)
- [[`8ab26cf508`](https://github.com/nodejs/node/commit/8ab26cf508)] - **(SEMVER-MINOR)** **readline**: add option to stop duplicates in history (Danny Nemer) [#2982](https://github.com/nodejs/node/pull/2982)
- [[`6a6c431eec`](https://github.com/nodejs/node/commit/6a6c431eec)] - **src**: use persistent strings from node::Environment (Ben Noordhuis) [#11945](https://github.com/nodejs/node/pull/11945)
- [[`d0c2d67083`](https://github.com/nodejs/node/commit/d0c2d67083)] - **src**: add native URL class (James M Snell) [#11801](https://github.com/nodejs/node/pull/11801)
- [[`019a20adb5`](https://github.com/nodejs/node/commit/019a20adb5)] - **src**: make PercentDecode return void (Timothy Gu) [#11922](https://github.com/nodejs/node/pull/11922)
- [[`d6da1705cd`](https://github.com/nodejs/node/commit/d6da1705cd)] - **src**: ensure that fd 0-2 are valid on windows (Bartosz Sosnowski) [#11863](https://github.com/nodejs/node/pull/11863)
- [[`59f71f5661`](https://github.com/nodejs/node/commit/59f71f5661)] - **src, buffer**: do not segfault on out-of-range index (Timothy Gu) [#11927](https://github.com/nodejs/node/pull/11927)
- [[`4051184106`](https://github.com/nodejs/node/commit/4051184106)] - **stream_base,tls_wrap**: notify on destruct (Trevor Norris) [#11947](https://github.com/nodejs/node/pull/11947)
- [[`d8b71be183`](https://github.com/nodejs/node/commit/d8b71be183)] - **test**: fix misleading comment (Franziska Hinkelmann) [#12048](https://github.com/nodejs/node/pull/12048)
- [[`8b2b93f148`](https://github.com/nodejs/node/commit/8b2b93f148)] - **test**: mark child-process-exec-kill-throws flaky (Gibson Fahnestock) [#12054](https://github.com/nodejs/node/pull/12054)
- [[`948b99deab`](https://github.com/nodejs/node/commit/948b99deab)] - **test**: fix broken tests in test-buffer-includes (Alexey Orlenko) [#12040](https://github.com/nodejs/node/pull/12040)
- [[`d112aad78b`](https://github.com/nodejs/node/commit/d112aad78b)] - **test**: replace throw with common.fail (Dejon "DJ" Gill) [#9700](https://github.com/nodejs/node/pull/9700)
- [[`41284fbc5b`](https://github.com/nodejs/node/commit/41284fbc5b)] - **test**: cover thrown errors from exec() kill (cjihrig) [#11038](https://github.com/nodejs/node/pull/11038)
- [[`414df6c93b`](https://github.com/nodejs/node/commit/414df6c93b)] - **test**: test validity of prefix in mkdtempSync (Luca Maraschi) [#12009](https://github.com/nodejs/node/pull/12009)
- [[`1c0435b1f3`](https://github.com/nodejs/node/commit/1c0435b1f3)] - **test**: add regex for expected error message (John F. Mercer) [#12011](https://github.com/nodejs/node/pull/12011)
- [[`a73dea9499`](https://github.com/nodejs/node/commit/a73dea9499)] - **test**: add second argument to assert.throws() (Rj Bernaldo) [#12016](https://github.com/nodejs/node/pull/12016)
- [[`ade64e61cd`](https://github.com/nodejs/node/commit/ade64e61cd)] - **test**: refactor test-cluster-disconnect (Rich Trott) [#11981](https://github.com/nodejs/node/pull/11981)
- [[`3d21bfe6b9`](https://github.com/nodejs/node/commit/3d21bfe6b9)] - **test**: invalid chars in http client path (Luca Maraschi) [#11964](https://github.com/nodejs/node/pull/11964)
- [[`e70ed3cb31`](https://github.com/nodejs/node/commit/e70ed3cb31)] - **test**: improve test-vm-cached-data.js (Nick Peleh) [#11974](https://github.com/nodejs/node/pull/11974)
- [[`b48f13af95`](https://github.com/nodejs/node/commit/b48f13af95)] - **test**: add minimal test for net benchmarks (Rich Trott) [#11979](https://github.com/nodejs/node/pull/11979)
- [[`764a00e6e5`](https://github.com/nodejs/node/commit/764a00e6e5)] - **test**: add test for url (Yuta Hiroto) [#11999](https://github.com/nodejs/node/pull/11999)
- [[`bb2de4a5a1`](https://github.com/nodejs/node/commit/bb2de4a5a1)] - **test**: do not use `more` command on Windows (Vse Mozhet Byt) [#11953](https://github.com/nodejs/node/pull/11953)
- [[`55a112689a`](https://github.com/nodejs/node/commit/55a112689a)] - **test**: add test for child_process.execFile() (Rich Trott) [#11929](https://github.com/nodejs/node/pull/11929)
- [[`9ba551f7e3`](https://github.com/nodejs/node/commit/9ba551f7e3)] - **test**: fix flaky test-tls-socket-close (Rich Trott) [#11921](https://github.com/nodejs/node/pull/11921)
- [[`114f9d619d`](https://github.com/nodejs/node/commit/114f9d619d)] - **test**: add hasCrypto check to tls-socket-close (Daniel Bevenius) [#11911](https://github.com/nodejs/node/pull/11911)
- [[`169f187f16`](https://github.com/nodejs/node/commit/169f187f16)] - **test**: synchronize WPT url setters tests data (Daijiro Wachi) [#11887](https://github.com/nodejs/node/pull/11887)
- [[`4b1b6b85a9`](https://github.com/nodejs/node/commit/4b1b6b85a9)] - **timers**: fix not to close reused timer handle (Shigeki Ohtsu) [#11646](https://github.com/nodejs/node/pull/11646)
- [[`fd93622f8a`](https://github.com/nodejs/node/commit/fd93622f8a)] - **tls**: fix SecurePair external memory reporting (Ben Noordhuis) [#11896](https://github.com/nodejs/node/pull/11896)
- [[`126dcb76af`](https://github.com/nodejs/node/commit/126dcb76af)] - **url**: name anonymous functions in url (Pedro lima) [#9225](https://github.com/nodejs/node/pull/9225)
- [[`f6755182e5`](https://github.com/nodejs/node/commit/f6755182e5)] - **url**: show input in parse error message (Joyee Cheung) [#11934](https://github.com/nodejs/node/pull/11934)
- [[`c51d925c84`](https://github.com/nodejs/node/commit/c51d925c84)] - **url**: restrict setting protocol to "file" (Daijiro Wachi) [#11887](https://github.com/nodejs/node/pull/11887)

Windows 32-bit Installer: https://nodejs.org/dist/v7.8.0/node-v7.8.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v7.8.0/node-v7.8.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v7.8.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v7.8.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v7.8.0/node-v7.8.0.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v7.8.0/node-v7.8.0-darwin-x64.tar.gz \
Linux 32-bit Binary: https://nodejs.org/dist/v7.8.0/node-v7.8.0-linux-x86.tar.xz \
Linux 64-bit Binary: https://nodejs.org/dist/v7.8.0/node-v7.8.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v7.8.0/node-v7.8.0-linux-ppc64le.tar.xz \
Linux PPC BE 64-bit Binary: https://nodejs.org/dist/v7.8.0/node-v7.8.0-linux-ppc64.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v7.8.0/node-v7.8.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v7.8.0/node-v7.8.0-aix-ppc64.tar.gz \
SmartOS 32-bit Binary: https://nodejs.org/dist/v7.8.0/node-v7.8.0-sunos-x86.tar.xz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v7.8.0/node-v7.8.0-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v7.8.0/node-v7.8.0-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v7.8.0/node-v7.8.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v7.8.0/node-v7.8.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v7.8.0/node-v7.8.0.tar.gz \
Other release files: https://nodejs.org/dist/v7.8.0/ \
Documentation: https://nodejs.org/docs/v7.8.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

3b433801907437aac7680588519492052de7854eab74ce7d70fc22182fa469ee  node-v7.8.0-aix-ppc64.tar.gz
b5d041668be876a8f863a202f1ad616d8be143324aaee2650d9948e19ef4f101  node-v7.8.0-darwin-x64.tar.gz
085186355abdd35f5f28bbae3c925dac786bc362cb94eb06960a040c1b748535  node-v7.8.0-darwin-x64.tar.xz
9fe432ffb56cca01bf383ca7dfb16d9dc632fc32fa00963c71fe42673bcba836  node-v7.8.0-headers.tar.gz
47be80fcabff8d3c78026a18d48c098f0ab745c2ea85a5a2caa9eb3180c7d3dd  node-v7.8.0-headers.tar.xz
513ffbd9abc1c1c9de0ae2d872f5483c45f055989b0e697913e7963f15d26fee  node-v7.8.0-linux-arm64.tar.gz
3aa16eb5cf873e4a9540f30338e99713afa52bd16028034d7a09d8bb2c30f634  node-v7.8.0-linux-arm64.tar.xz
17244fea0ab0e94fbdae10b5998951c1d83fdab9d5b91209debdedc94a4fc7a1  node-v7.8.0-linux-armv6l.tar.gz
d0979c71c1b3c1a88cbdcdff47185cd5dd1dc880b8c7fa993add47c00279d377  node-v7.8.0-linux-armv6l.tar.xz
059eb25579bee5e8edeeb7060a2937dfef4975a67d86bea3b7d54839716d5552  node-v7.8.0-linux-armv7l.tar.gz
c11ce4b49bdf0e1fe4803efcc3817cf1a88e2f351f702dfb6612605a705d367e  node-v7.8.0-linux-armv7l.tar.xz
c5981e0f6375ef6e92b302c6593e9dd8fb58ef2f9dcd20d5393e7ff3b43e2844  node-v7.8.0-linux-ppc64le.tar.gz
d8c90c7f4a5f7b327b7461dcda184ae163f6031891b393f1935d11fd31c04600  node-v7.8.0-linux-ppc64le.tar.xz
422d544c84ae0393e424978d09170d6d3e6030db5af185faea61440f24ec1d48  node-v7.8.0-linux-ppc64.tar.gz
6cad4dded33d646a8dd75dcdb917ab48135f7561b30bf1fb0d9f7bdac6becb68  node-v7.8.0-linux-ppc64.tar.xz
5c802f3bd892c004a2997e5a47fcb110ff6aa243cd036f8b78b021db838e627c  node-v7.8.0-linux-s390x.tar.gz
4cb1de9fcf6c73485e40989983c1ee2e3933608ff26cc505cfec85adc27cca89  node-v7.8.0-linux-s390x.tar.xz
0bd86f2a39221b532172c7d1acb57f0b0cba88c7b82ea74ba9d1208b9f6f9697  node-v7.8.0-linux-x64.tar.gz
1fca4e71d6f00f7f727994fccc604716160f06aa1ad6d8689d84cd3ca5227312  node-v7.8.0-linux-x64.tar.xz
84d71d14ced742371c9ba538b3a9d9578f85c4359f7d50f6e9a48c61243c71ec  node-v7.8.0-linux-x86.tar.gz
f91834b75909ab1af4c818ee5c7316e0fb1d6a4190fe07378c50ca81c910c116  node-v7.8.0-linux-x86.tar.xz
43efa8c996d9666cd1659142d1dbfb17ef0ef79957dc42398e149ef36aff46d1  node-v7.8.0.pkg
9f3cf97eb59c078f948a0e164f8b03b5075055d6576d7de6f1916bfc09f8a8e6  node-v7.8.0-sunos-x64.tar.gz
98eccfa5f7e025ae7f495a340bebd7ad1647b6e7d6970c553c7bc6f852dbf205  node-v7.8.0-sunos-x64.tar.xz
c8cdaac00fa35f865cbe8c567be3e870b49e222cee69c94ad0174467f281a700  node-v7.8.0-sunos-x86.tar.gz
922e0903d0965c355fb072bbf6e4e777fc0fc10b50b26183ea69db63255e33e8  node-v7.8.0-sunos-x86.tar.xz
6aa2ab52763f33ce09b63e913f3caf67d9d4b1120c70b087b50155bd647b76da  node-v7.8.0.tar.gz
6821aaee58bbc8bc8d08fec6989a42278b725a21382500dc20fd9d9f71398f02  node-v7.8.0.tar.xz
64b132d01e217b98d0a7313d15094b8a215178ba34ffb87c961e8af760447a73  node-v7.8.0-win-x64.7z
49eb820e2e8a01c6b9c2f94e019ee4149ce01553a809dc39eebdc83a1fa1792d  node-v7.8.0-win-x64.zip
0863f25ef0485b9685bdde7133677a7f477a106d8fe29d7187c6e3c58fd230d9  node-v7.8.0-win-x86.7z
5eb6b7d6b6b79f8737a0789b0ac4bfff6cad75ce4408cabc00a9cef6bdb934a1  node-v7.8.0-win-x86.zip
f66a1774086f31e6e9480f6dd67e31f1853a58d26970a19672b40ba0b318a442  node-v7.8.0-x64.msi
0eda7b5dabd72865f96985d5ef0ad7048df87a79ff52a77b352ca293f52e2930  node-v7.8.0-x86.msi
11cc70b68f9cd252756fcad2383342919db6b51bda32e1c99f2cf831c54191f3  win-x64/node.exe
b996750a68d315638193540d5e7350717fd4b0d15fc67cdd05d0450a1c9f2255  win-x64/node.lib
4284743095dd7e23d3b62a742cae758f768b0597cb67b62572afa84e5864f755  win-x64/node_pdb.7z
32480ce6403f7adc0370daad59556071198d2bfa94d8926e7221221c2ddc7824  win-x64/node_pdb.zip
af59dea578eac42ffefa327671092de383e248ab9d16de0372319629129b8f82  win-x86/node.exe
9ae61b802586830d80e5424cd217bfe75bd12bc62fa530125210151df84fcd6b  win-x86/node.lib
9ac46e57142439d5a950dbad5fd525b127cb66ed59e5db07a0db8bc9f0acaaa6  win-x86/node_pdb.7z
edf86c803d205b64349870bf040b87142bd5771663cf6f58f3838c32bc85e455  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQEcBAEBCAAGBQJY2yPBAAoJEJM7AfQLXKlG+ZAH/RMpoGvTnWNsltteMlZ2j6xq
jhvVbI9TvNyqW9KtiNgE+pbWKatS4BJUtxAPtrgv3szspNHQe7cmhN1q5qPtXzXm
p3iunRT5PssX6cR0KK0lHAWzmvdd7QImU3TPJCI2Tcg3LCv9NvWn6iC9kj/45iH5
cyaLK0oxpB63+cQcr6hJs5HRyvTy+WeCZRKKRDWvzzO/hvanFCufkD0Vw2o72i57
yrvBw4eGMRJm9ZsfO+wBvD+ZOHZoLS/0080zT5jBskCPDH45s75O7dTHtHFoXlWw
eT0HghqhMqiWPNg9i8OwPD/gH8NjebDONMmVZr09zdpbGB3umDFChCRO8V2UUdQ=
=hSu6
-----END PGP SIGNATURE-----

```
