---
date: '2013-01-18T19:38:32.000Z'
category: release
title: Node v0.9.7 (Unstable)
layout: blog-post
author: The Node.js Project
---

2013.01.18, Version 0.9.7 (Unstable)

- V8: Upgrade to 3.15.11.7

- npm: Upgrade to 1.2.2

- punycode: Upgrade to 1.2.0 (<PERSON>)

- repl: make built-in modules available by default (<PERSON>)

- windows: add support for '\_Total' perf counters (<PERSON>)

- cluster: make --prof work for workers (<PERSON>)

- child_process: do not keep list of sent sockets (Fedor Indutny)

- tls: Follow RFC6125 more strictly (<PERSON>or <PERSON>du<PERSON>)

- buffer: floating point read/write improvements (<PERSON>)

- TypedArrays: Improve dataview perf without endian param (<PERSON>)

- module: assert require() called with a non-empty string (<PERSON>, <PERSON>)

- stdio: Set readable/writable flags properly (isaacs)

- stream: Properly handle large reads from push-streams (isaacs)

Source Code: https://nodejs.org/dist/v0.9.7/node-v0.9.7.tar.gz

Macintosh Installer (Universal): https://nodejs.org/dist/v0.9.7/node-v0.9.7.pkg

Windows Installer: https://nodejs.org/dist/v0.9.7/node-v0.9.7-x86.msi

Windows x64 Installer: https://nodejs.org/dist/v0.9.7/x64/node-v0.9.7-x64.msi

Windows x64 Files: https://nodejs.org/dist/v0.9.7/x64/

Linux 32-bit Binary: https://nodejs.org/dist/v0.9.7/node-v0.9.7-linux-x86.tar.gz

Linux 64-bit Binary: https://nodejs.org/dist/v0.9.7/node-v0.9.7-linux-x64.tar.gz

Solaris 32-bit Binary: https://nodejs.org/dist/v0.9.7/node-v0.9.7-sunos-x86.tar.gz

Solaris 64-bit Binary: https://nodejs.org/dist/v0.9.7/node-v0.9.7-sunos-x64.tar.gz

Other release files: https://nodejs.org/dist/v0.9.7/

Website: https://nodejs.org/docs/v0.9.7/

Documentation: https://nodejs.org/docs/v0.9.7/api/

Shasums:

```
fa5771999205beae787e56bbffa3be3c496dbb3e  node-v0.9.7-darwin-x64.tar.gz
3790a0323e82598d9286470a3ca8b079b25d815c  node-v0.9.7-darwin-x86.tar.gz
70da0da05a2d76dfba389b413112aa1c31289114  node-v0.9.7-linux-x64.tar.gz
b7448f020820302a6c648744a9ba4b6e1979fbf8  node-v0.9.7-linux-x86.tar.gz
a0148c804c37ecbcfd4039213e469ba2757b6125  node-v0.9.7-sunos-x64.tar.gz
121cd6fe2fbab0ca20644256914433bfa02ca6c4  node-v0.9.7-sunos-x86.tar.gz
8938d57fc9cff896bb13901a43b5cff989785a23  node-v0.9.7-x86.msi
23b86861de7c6111311f869c722431cf84d93761  node-v0.9.7.pkg
527a86ee094f7ed77967eda3dff4b2aff3f29384  node-v0.9.7.tar.gz
5231f327979f900d4ba927f2e63e67635001268c  node.exe
6a78465d3f6e34aab82a529839c522abbab715ac  node.exp
d814bd0733ba22a3cd9c086d34c68706d6c77663  node.lib
5c9df4dcf16c9baafce9b6982907ada586e878a2  node.pdb
f49f1e0706ef38facba45af75cfdf44678b21f5a  x64/node-v0.9.7-x64.msi
13f9ee5c81b89bf77c5bfe46993905de4e3384d7  x64/node.exe
3f9c67381a7f2b3fc2f6c14b66036dde02de2820  x64/node.exp
cac98eebe21aeb05741a2fec6e2c7eaf5bb111a6  x64/node.lib
563932b95c08ebdb923358cd8b68ee17ac7ad781  x64/node.pdb
```
