---
date: '2021-08-25T20:59:23.383Z'
category: release
title: Node v16.8.0 (Current)
layout: blog-post
author: <PERSON><PERSON><PERSON>
---

### Notable Changes

- [[`2e90b10f35`](https://github.com/nodejs/node/commit/2e90b10f35)] - **doc**: deprecate type coercion for `dns.lookup` options (<PERSON>) [#38906](https://github.com/nodejs/node/pull/38906)
- [[`a6d50a18a0`](https://github.com/nodejs/node/commit/a6d50a18a0)] - **(SEMVER-MINOR)** **stream**: add `stream.Duplex.from` utility (<PERSON>) [#39519](https://github.com/nodejs/node/pull/39519)
- [[`af7047a815`](https://github.com/nodejs/node/commit/af7047a815)] - **(SEMVER-MINOR)** **stream**: add `isDisturbed` helper (<PERSON>) [#39628](https://github.com/nodejs/node/pull/39628)
- [[`66400374de`](https://github.com/nodejs/node/commit/66400374de)] - **(SEMVER-MINOR)** **util**: expose `toUSVString` (Robert Nagy) [#39814](https://github.com/nodejs/node/pull/39814)

### Commits

- [[`90bf247a55`](https://github.com/nodejs/node/commit/90bf247a55)] - **build**: fix update authors commit (Mestery) [#39858](https://github.com/nodejs/node/pull/39858)
- [[`c968372e37`](https://github.com/nodejs/node/commit/c968372e37)] - **build**: add authors.yml (Tierney Cyren) [#35831](https://github.com/nodejs/node/pull/35831)
- [[`3f284cf65c`](https://github.com/nodejs/node/commit/3f284cf65c)] - **build**: add option to hide console window (Cheng Zhao) [#39712](https://github.com/nodejs/node/pull/39712)
- [[`a01e3ab41d`](https://github.com/nodejs/node/commit/a01e3ab41d)] - **deps**: V8: cherry-pick 00bb1a77c03e (Darshan Sen) [#39829](https://github.com/nodejs/node/pull/39829)
- [[`cce95c4c5b`](https://github.com/nodejs/node/commit/cce95c4c5b)] - **deps**: upgrade npm to 7.21.0 (Myles Borins) [#39813](https://github.com/nodejs/node/pull/39813)
- [[`254810a22e`](https://github.com/nodejs/node/commit/254810a22e)] - **doc**: add duplicate CVE check in sec. release doc (Daniel Bevenius) [#39845](https://github.com/nodejs/node/pull/39845)
- [[`8c50d16712`](https://github.com/nodejs/node/commit/8c50d16712)] - **doc**: improve description of the triagers team (Michaël Zasso) [#39833](https://github.com/nodejs/node/pull/39833)
- [[`c02165d992`](https://github.com/nodejs/node/commit/c02165d992)] - **doc**: update instructions for cc (Michael Dawson) [#39674](https://github.com/nodejs/node/pull/39674)
- [[`208305fd8f`](https://github.com/nodejs/node/commit/208305fd8f)] - **doc**: move util.toUSVString() outside of deprecated group (Luigi Pinca) [#39840](https://github.com/nodejs/node/pull/39840)
- [[`2e90b10f35`](https://github.com/nodejs/node/commit/2e90b10f35)] - **doc**: deprecate type coercion for `dns.lookup` options (Antoine du Hamel) [#38906](https://github.com/nodejs/node/pull/38906)
- [[`8460a3216c`](https://github.com/nodejs/node/commit/8460a3216c)] - **doc**: deprecate using non-boolean values in the `verbatim` option (Antoine du Hamel) [#38906](https://github.com/nodejs/node/pull/38906)
- [[`3041d57201`](https://github.com/nodejs/node/commit/3041d57201)] - **doc**: fix malformed changelog entries (Rich Trott) [#39791](https://github.com/nodejs/node/pull/39791)
- [[`2b02f747c3`](https://github.com/nodejs/node/commit/2b02f747c3)] - **doc**: fix lint errors in packages.md (Rich Trott) [#39792](https://github.com/nodejs/node/pull/39792)
- [[`a387600d8f`](https://github.com/nodejs/node/commit/a387600d8f)] - **doc**: add example of self-reference in scoped packages (Jesús Leganés-Combarro 'piranna) [#37630](https://github.com/nodejs/node/pull/37630)
- [[`7a25bf3a6d`](https://github.com/nodejs/node/commit/7a25bf3a6d)] - **doc**: add himadriganguly as a triager (Himadri Ganguly) [#39757](https://github.com/nodejs/node/pull/39757)
- [[`d1900f43ce`](https://github.com/nodejs/node/commit/d1900f43ce)] - **fs**: combine require() and destructure (Colin Ihrig) [#39806](https://github.com/nodejs/node/pull/39806)
- [[`158d4464d2`](https://github.com/nodejs/node/commit/158d4464d2)] - **meta**: add gyp as owner of gyp files and tools/gyp (Mary Marchini) [#34847](https://github.com/nodejs/node/pull/34847)
- [[`8fa38500f2`](https://github.com/nodejs/node/commit/8fa38500f2)] - **policy**: canonicalize before resolving specifiers (Bradley Farias) [#37863](https://github.com/nodejs/node/pull/37863)
- [[`a7a217be13`](https://github.com/nodejs/node/commit/a7a217be13)] - **repl**: fix tla function hoisting (Don Jayamanne) [#39745](https://github.com/nodejs/node/pull/39745)
- [[`3a8399ee61`](https://github.com/nodejs/node/commit/3a8399ee61)] - **src**: return Maybe\<bool\> from InitializeContextRuntime() (Darshan Sen) [#39695](https://github.com/nodejs/node/pull/39695)
- [[`a704c9dfce`](https://github.com/nodejs/node/commit/a704c9dfce)] - **(SEMVER-MINOR)** **src**: call overload ctor from the original ctor (Darshan Sen) [#39768](https://github.com/nodejs/node/pull/39768)
- [[`0918ea0683`](https://github.com/nodejs/node/commit/0918ea0683)] - **(SEMVER-MINOR)** **src**: add a constructor overload for CallbackScope (Darshan Sen) [#39768](https://github.com/nodejs/node/pull/39768)
- [[`a6d50a18a0`](https://github.com/nodejs/node/commit/a6d50a18a0)] - **(SEMVER-MINOR)** **stream**: duplexify (Robert Nagy) [#39519](https://github.com/nodejs/node/pull/39519)
- [[`af7047a815`](https://github.com/nodejs/node/commit/af7047a815)] - **(SEMVER-MINOR)** **stream**: add isDisturbed helper (Robert Nagy) [#39628](https://github.com/nodejs/node/pull/39628)
- [[`f98311a7c8`](https://github.com/nodejs/node/commit/f98311a7c8)] - **tools**: update workflow to open a pull request (Rich Trott) [#39825](https://github.com/nodejs/node/pull/39825)
- [[`d33f897509`](https://github.com/nodejs/node/commit/d33f897509)] - **tools**: use find-inactive-collaborators to modify README.md (Rich Trott) [#39825](https://github.com/nodejs/node/pull/39825)
- [[`d82ee96861`](https://github.com/nodejs/node/commit/d82ee96861)] - **tools**: update gyp-next to v0.9.5 (Jiawen Geng) [#39818](https://github.com/nodejs/node/pull/39818)
- [[`79079ea01b`](https://github.com/nodejs/node/commit/79079ea01b)] - **tools**: fix markdown linting (Rich Trott) [#39832](https://github.com/nodejs/node/pull/39832)
- [[`01093b07cc`](https://github.com/nodejs/node/commit/01093b07cc)] - **tools**: update markdown linter dependencies and move to ESM (Antoine du Hamel) [#39801](https://github.com/nodejs/node/pull/39801)
- [[`9dc0c91392`](https://github.com/nodejs/node/commit/9dc0c91392)] - **tools**: update rollup to latest version in markdown linter (Rich Trott) [#39797](https://github.com/nodejs/node/pull/39797)
- [[`c34e2534ab`](https://github.com/nodejs/node/commit/c34e2534ab)] - **tools**: update markdown lint dependencies (Rich Trott) [#39770](https://github.com/nodejs/node/pull/39770)
- [[`66400374de`](https://github.com/nodejs/node/commit/66400374de)] - **(SEMVER-MINOR)** **util**: expose toUSVString (Robert Nagy) [#39814](https://github.com/nodejs/node/pull/39814)

Windows 32-bit Installer: https://nodejs.org/dist/v16.8.0/node-v16.8.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v16.8.0/node-v16.8.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v16.8.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v16.8.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v16.8.0/node-v16.8.0.pkg \
macOS Apple Silicon 64-bit Binary: https://nodejs.org/dist/v16.8.0/node-v16.8.0-darwin-arm64.tar.gz \
macOS Intel 64-bit Binary: https://nodejs.org/dist/v16.8.0/node-v16.8.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v16.8.0/node-v16.8.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v16.8.0/node-v16.8.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v16.8.0/node-v16.8.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v16.8.0/node-v16.8.0-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v16.8.0/node-v16.8.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v16.8.0/node-v16.8.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v16.8.0/node-v16.8.0.tar.gz \
Other release files: https://nodejs.org/dist/v16.8.0/ \
Documentation: https://nodejs.org/docs/v16.8.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

cc9e8e2600c2ad9ee80be45a850453a5f0237650f8bd4364db4a9f941a5b6e57  node-v16.8.0-aix-ppc64.tar.gz
891e72d166abbb1b838b5113cc8cfaf2fe905dfe77afe84a5af56e426ff74ddf  node-v16.8.0-darwin-arm64.tar.gz
f1aa35e99542bdaab51e46917203cf215463982a16862199ecfa200534190119  node-v16.8.0-darwin-arm64.tar.xz
9c013cb82830ab5adb9630ff28046f420a7805bb4a930ec2b3f5b162c5f6de88  node-v16.8.0-darwin-x64.tar.gz
fdcc827192f349bb1f3ec58361cb6a9a783b84d3bc19d51ca2fd5b0968b89f3b  node-v16.8.0-darwin-x64.tar.xz
fa04f9a4fd9126fe2c0bf43e3d464d730dd328cc130c3599478530f64489be7f  node-v16.8.0-headers.tar.gz
c24263b3f90bc490fba030a7836485b3df0351c8b68c7a83011dd01ed45d95be  node-v16.8.0-headers.tar.xz
3f8cbdd3165fb9bf499f0e35bbd2ae4b301f2af5e9f349f82beacdb7278539bb  node-v16.8.0-linux-arm64.tar.gz
4d08cda750e42e691d18881b4a443de4c19e0e2c1195e56a15a3ec45aeed895c  node-v16.8.0-linux-arm64.tar.xz
7d325b9b8f189c4b59196df933fcb5a8009684f9073977f00a75dc66924fb03d  node-v16.8.0-linux-armv7l.tar.gz
86b4d718aa0003e895b845db86851720a9f9ce3e66f031324bfeebd63e4b922f  node-v16.8.0-linux-armv7l.tar.xz
437d5de90cb77837d5fc0faae08c3aaa17dbad0309341c5a1934db994196e9c9  node-v16.8.0-linux-ppc64le.tar.gz
eb0a02f5c7b4c2187d034a536ad42311283ac91d761af9d69bd46f2ecbe5abbe  node-v16.8.0-linux-ppc64le.tar.xz
45a1784f04b7ff6aee9d15badc8d559ade43c413949592d4d7b7cf727dc9b957  node-v16.8.0-linux-s390x.tar.gz
cefc32542c3521bbd075b218bd2f7444589a537e6c82ff6b64f46765efb07392  node-v16.8.0-linux-s390x.tar.xz
aa1f366b522a9565332096fdc32ed0cd58a2049c0875d839703d3fe58b4c226d  node-v16.8.0-linux-x64.tar.gz
85880c0e63c254faa75f8cf6512bc353f1587ba6e65a5e1d7366bf684684ae74  node-v16.8.0-linux-x64.tar.xz
b91e3e368e681cb6e91e01c379382b0921f2d585f2444c7196fdf68d5688547f  node-v16.8.0.pkg
0cc13572bc7c5a9bf7a2c5cb2800ff045780e43d0fa6e3eb0f1be46b4bf9a1c6  node-v16.8.0.tar.gz
b8790226312970ba5d8fd98229380c48bf0366eb1a3633091e350a34a4b46392  node-v16.8.0.tar.xz
11032b8841444015f01f56370580e1f14eab997bf4d497a249f719caa4418e52  node-v16.8.0-win-x64.7z
16193b45e18c116ddd062cc1a7ac5d96de9cb2198d4334f345d5718ea6d603f7  node-v16.8.0-win-x64.zip
d2aa238864ae9adb0f3e565ab89021e713bcb5911dbaa4046a59307400d5bbb5  node-v16.8.0-win-x86.7z
d3322f8f1174a92ca72900ae7a189a40b3a402ba7781a00ec1996ff346bdccc8  node-v16.8.0-win-x86.zip
fe18c039bfcca609e2601c10c26fddd335794c1c3065e80e58985e744bb6ffe5  node-v16.8.0-x64.msi
6a2868f3f4df8b0f4d060ae1f305f27d499248c0b260759c058d27d6c90ba66b  node-v16.8.0-x86.msi
17031318847f432785f33f2ed9db6328d3ad4ce15602ece56c8077cf6eaca45d  win-x64/node.exe
33b4ae0ab35e7180d8d1b5c2f47da889f9edc75617a42cacf249a4c1ac7635b1  win-x64/node.lib
54ad71ddc4cfd97c0319874cc8a2656a35db0cff7b5a45ebe5b970fb0110248b  win-x64/node_pdb.7z
0c30e3b24e8096e8a45331186ff1b6729a65f9693bb6409627fb03436765fb9f  win-x64/node_pdb.zip
86daf79b0e3090153d8985ec5eb200eb213da34280eac88ebe7df05cb8ea0c45  win-x86/node.exe
407a64a2b21512f6cd8ec1a98c2a8228475d3de02352a191b98bdedb738f3193  win-x86/node.lib
1b2f4e1cb25aae99d48443635778a5793d19b8cb9d868a265c532bedf788e950  win-x86/node_pdb.7z
f58094285001c98e62fa231a0885ec3d3bc0e4407130c41f70682159f12dd2f4  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEj8yhP+8dDC6RAI4Jdw96mlrhVgAFAmEmrsQACgkQdw96mlrh
VgCDrA/9Ebnj2yLp0/A9x47LwZpZsabDzsg2avWYiZzR7CbuHAsFfT8LeBWrzNZJ
Vorvo397IrNq88MwgkScS6GFXYCUt0WCL0OJ302li1kadp3kAySGU9nXv+rkPAw9
dU+6JCaqcSqicrxVb2dt3/3cx2wo5hhcbcZGq/ubm6Vq0xDRg9XDevkfJyazPar8
Q111uWr0RdmaiX+/5wLMju6j9GDw94goiVQRibaqQzIQVB4i03z3k9KUfXWJXTUS
qeCh2uSZXPFnNNAHbvpt0QyS/DUwlAqW/kpW1frYF8m98kDvunVHIaHLwAdJJWAP
877KGMt6k4I/dkGNCYeM06QFL8o2rhacP8BvuOQhi7uCptl7wvqYYiba40m/W4YP
8vsZN9pBdhBHlXRf6TlBO26kkL1JRAkJEjxzDjy2vVrzLY1R67SkRBF757EweF8F
7T3flaJrbe997uWvgSLChzBildVd8xt/1ob5rXTUXyKrx6FeD20yl8nEyyFvvZd/
Zg5ln94HgHidxitV6j0OUaUVd5g/DFPUN0s4NjZKLvmn34xSqXFsvhrdMNVpxUw6
MUsqiWQh5a/xtN8/DPTTvcuAlm6KIelTgfqB6R1Q694AihvcibIf+fe5HCbQ5elQ
H6M9S6sGU3PfPnz8IrwFWwwFJjZapcBTuCe5lB02AdyZDtXw+eU=
=YB9N
-----END PGP SIGNATURE-----

```
