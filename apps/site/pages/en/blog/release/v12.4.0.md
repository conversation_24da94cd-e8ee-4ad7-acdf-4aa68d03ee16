---
date: '2019-06-04T14:05:15.812Z'
category: release
title: Node v12.4.0 (Current)
layout: blog-post
author: <PERSON><PERSON><PERSON>
---

### Notable changes

- **doc**:
  - The JSON variant of the API documentation is no longer experimental (<PERSON>) [#27842](https://github.com/nodejs/node/pull/27842).
- **esm**:
  - JSON module support is always enabled under `--experimental-modules`. The
    `--experimental-json-modules` flag has been removed (<PERSON><PERSON>) [#27752](https://github.com/nodejs/node/pull/27752).
- **http,http2**:
  - A new flag has been added for overriding the default HTTP server socket
    timeout (which is two minutes). Pass `--http-server-default-timeout=milliseconds`
    or `--http-server-default-timeout=0` to respectively change or disable the timeout.
    Starting with Node.js 13.0.0, the timeout will be disabled by default (<PERSON>) [#27704](https://github.com/nodejs/node/pull/27704).
- **inspector**:
  - Added an experimental `--heap-prof` flag to start the V8 heap profiler
    on startup and write the heap profile to disk before exit (<PERSON><PERSON>) [#27596](https://github.com/nodejs/node/pull/27596).
- **stream**:
  - The `readable.unshift()` method now correctly converts strings to buffers.
    Additionally, a new optional argument is accepted to specify the string's
    encoding, such as `'utf8'` or `'ascii'` (<PERSON> <PERSON>grande) [#27194](https://github.com/nodejs/node/pull/27194).
- **v8**:
  - The object returned by `v8.getHeapStatistics()` has two new properties:
    `number_of_native_contexts` and `number_of_detached_contexts` (Yuriy Vasiyarov) [#27933](https://github.com/nodejs/node/pull/27933).

### Commits

- [[`5bbc6d79c3`](https://github.com/nodejs/node/commit/5bbc6d79c3)] - **assert**: remove unreachable code (Rich Trott) [#27840](https://github.com/nodejs/node/pull/27840)
- [[`530e63a4eb`](https://github.com/nodejs/node/commit/530e63a4eb)] - **assert**: remove unreachable code (Rich Trott) [#27786](https://github.com/nodejs/node/pull/27786)
- [[`9b08c458be`](https://github.com/nodejs/node/commit/9b08c458be)] - **build,aix**: link with `noerrmsg` to eliminate warnings (Refael Ackermann) [#27773](https://github.com/nodejs/node/pull/27773)
- [[`08b0ca9645`](https://github.com/nodejs/node/commit/08b0ca9645)] - **build,win**: create junction instead of symlink to `out\%config%` (Refael Ackermann) [#27736](https://github.com/nodejs/node/pull/27736)
- [[`ea2d550507`](https://github.com/nodejs/node/commit/ea2d550507)] - **child_process**: move exports to bottom for consistent code style (himself65) [#27845](https://github.com/nodejs/node/pull/27845)
- [[`a9f95572c3`](https://github.com/nodejs/node/commit/a9f95572c3)] - **child_process**: remove extra shallow copy (zero1five) [#27801](https://github.com/nodejs/node/pull/27801)
- [[`449ee8dd42`](https://github.com/nodejs/node/commit/449ee8dd42)] - **console**: fix table() output (Brian White) [#27917](https://github.com/nodejs/node/pull/27917)
- [[`9220a68a76`](https://github.com/nodejs/node/commit/9220a68a76)] - **crypto**: fix KeyObject handle type error message (Alexander Avakov) [#27904](https://github.com/nodejs/node/pull/27904)
- [[`3b6424fa29`](https://github.com/nodejs/node/commit/3b6424fa29)] - **deps**: histogram: unexport symbols (Ben Noordhuis) [#27779](https://github.com/nodejs/node/pull/27779)
- [[`ef25ac5223`](https://github.com/nodejs/node/commit/ef25ac5223)] - **doc**: clarify wording in modules.md (Alex Temny) [#27853](https://github.com/nodejs/node/pull/27853)
- [[`c683cd99d7`](https://github.com/nodejs/node/commit/c683cd99d7)] - **doc**: improve explanation for directory with fs.rename() (Rich Trott) [#27963](https://github.com/nodejs/node/pull/27963)
- [[`70b485478c`](https://github.com/nodejs/node/commit/70b485478c)] - **doc**: fix the wrong name of AssertionError (Kyle Zhang) [#27982](https://github.com/nodejs/node/pull/27982)
- [[`11c3ddb4cb`](https://github.com/nodejs/node/commit/11c3ddb4cb)] - **doc**: simplify system call material in doc overview (Rich Trott) [#27966](https://github.com/nodejs/node/pull/27966)
- [[`c56640138a`](https://github.com/nodejs/node/commit/c56640138a)] - **doc**: warn about relying on fs gc close behavior (Benjamin Gruenbaum) [#27972](https://github.com/nodejs/node/pull/27972)
- [[`bab9f5a891`](https://github.com/nodejs/node/commit/bab9f5a891)] - **doc**: add information to revoked deprecations (cjihrig) [#27952](https://github.com/nodejs/node/pull/27952)
- [[`f4fc75d245`](https://github.com/nodejs/node/commit/f4fc75d245)] - **doc**: add missing status to DEP0121 (cjihrig) [#27950](https://github.com/nodejs/node/pull/27950)
- [[`77ff597faa`](https://github.com/nodejs/node/commit/77ff597faa)] - **doc**: add missing --experimental-wasm-modules docs (cjihrig) [#27948](https://github.com/nodejs/node/pull/27948)
- [[`6ca4f03ccf`](https://github.com/nodejs/node/commit/6ca4f03ccf)] - **doc**: revise additional Experimental status text (Rich Trott) [#27931](https://github.com/nodejs/node/pull/27931)
- [[`a1788de0a4`](https://github.com/nodejs/node/commit/a1788de0a4)] - **doc**: adds link to nightly code coverage report (Tariq Ramlall) [#27922](https://github.com/nodejs/node/pull/27922)
- [[`b7cd0de145`](https://github.com/nodejs/node/commit/b7cd0de145)] - **doc**: fix typo in pipe from async iterator example (Luigi Pinca) [#27870](https://github.com/nodejs/node/pull/27870)
- [[`f621b8f178`](https://github.com/nodejs/node/commit/f621b8f178)] - **doc**: reword Experimental stability index (Rich Trott) [#27879](https://github.com/nodejs/node/pull/27879)
- [[`7a7fc4e7e6`](https://github.com/nodejs/node/commit/7a7fc4e7e6)] - **doc**: update n-api support matrix (teams2ua) [#27567](https://github.com/nodejs/node/pull/27567)
- [[`9d9b32eff5`](https://github.com/nodejs/node/commit/9d9b32eff5)] - **doc**: fix for OutgoingMessage.prototype.\_headers/\_headerNames (Daniel Nalborczyk) [#27574](https://github.com/nodejs/node/pull/27574)
- [[`263e53317b`](https://github.com/nodejs/node/commit/263e53317b)] - **doc**: reposition "How to Contribute" README section (Anish Asrani) [#27811](https://github.com/nodejs/node/pull/27811)
- [[`85f505c292`](https://github.com/nodejs/node/commit/85f505c292)] - **doc**: add version info for types (Michael Dawson) [#27754](https://github.com/nodejs/node/pull/27754)
- [[`e3bb2aef60`](https://github.com/nodejs/node/commit/e3bb2aef60)] - **doc**: remove experimental status for JSON documentation (Rich Trott) [#27842](https://github.com/nodejs/node/pull/27842)
- [[`6981565c20`](https://github.com/nodejs/node/commit/6981565c20)] - **doc**: edit stability index overview (Rich Trott) [#27831](https://github.com/nodejs/node/pull/27831)
- [[`1a8e67cc1f`](https://github.com/nodejs/node/commit/1a8e67cc1f)] - **doc**: simplify contributing documentation (Rich Trott) [#27785](https://github.com/nodejs/node/pull/27785)
- [[`041b2220be`](https://github.com/nodejs/node/commit/041b2220be)] - **doc,n-api**: fix typo in N-API introduction (Richard Lau) [#27833](https://github.com/nodejs/node/pull/27833)
- [[`6cd64c8279`](https://github.com/nodejs/node/commit/6cd64c8279)] - **doc,test**: clarify that Http2Stream is destroyed after data is read (Alba Mendez) [#27891](https://github.com/nodejs/node/pull/27891)
- [[`cc69d5af8e`](https://github.com/nodejs/node/commit/cc69d5af8e)] - **doc,tools**: get altDocs versions from CHANGELOG.md (Richard Lau) [#27661](https://github.com/nodejs/node/pull/27661)
- [[`e72d4aa522`](https://github.com/nodejs/node/commit/e72d4aa522)] - **errors**: create internal connResetException (Rich Trott) [#27953](https://github.com/nodejs/node/pull/27953)
- [[`be1166fd01`](https://github.com/nodejs/node/commit/be1166fd01)] - **esm**: refactor createDynamicModule() (cjihrig) [#27809](https://github.com/nodejs/node/pull/27809)
- [[`e66648e887`](https://github.com/nodejs/node/commit/e66648e887)] - **(SEMVER-MINOR)** **esm**: remove experimental status from JSON modules (Myles Borins) [#27752](https://github.com/nodejs/node/pull/27752)
- [[`d948656635`](https://github.com/nodejs/node/commit/d948656635)] - **http**: fix deferToConnect comments (Robert Nagy) [#27876](https://github.com/nodejs/node/pull/27876)
- [[`24eaeed393`](https://github.com/nodejs/node/commit/24eaeed393)] - **http**: fix socketOnWrap edge cases (Anatoli Papirovski) [#27968](https://github.com/nodejs/node/pull/27968)
- [[`8b38dfbf39`](https://github.com/nodejs/node/commit/8b38dfbf39)] - **http**: call write callback even if there is no message body (Luigi Pinca) [#27777](https://github.com/nodejs/node/pull/27777)
- [[`588fd0c20d`](https://github.com/nodejs/node/commit/588fd0c20d)] - **(SEMVER-MINOR)** **http, http2**: flag for overriding server timeout (Ali Ijaz Sheikh) [#27704](https://github.com/nodejs/node/pull/27704)
- [[`799aeca134`](https://github.com/nodejs/node/commit/799aeca134)] - **http2**: respect inspect() depth (cjihrig) [#27983](https://github.com/nodejs/node/pull/27983)
- [[`83aaef87d0`](https://github.com/nodejs/node/commit/83aaef87d0)] - **http2**: fix tracking received data for maxSessionMemory (Anna Henningsen) [#27914](https://github.com/nodejs/node/pull/27914)
- [[`8c35198499`](https://github.com/nodejs/node/commit/8c35198499)] - **http2**: support net.Server options (Luigi Pinca) [#27782](https://github.com/nodejs/node/pull/27782)
- [[`23119cacf8`](https://github.com/nodejs/node/commit/23119cacf8)] - **inspector**: supported NodeRuntime domain in worker (Aleksei Koziatinskii) [#27706](https://github.com/nodejs/node/pull/27706)
- [[`89483be254`](https://github.com/nodejs/node/commit/89483be254)] - **inspector**: more conservative minimum stack size (Ben Noordhuis) [#27855](https://github.com/nodejs/node/pull/27855)
- [[`512ab1fddf`](https://github.com/nodejs/node/commit/512ab1fddf)] - **inspector**: removing checking of non existent field in lib/inspector.js (Keroosha) [#27919](https://github.com/nodejs/node/pull/27919)
- [[`d99e70381e`](https://github.com/nodejs/node/commit/d99e70381e)] - **SEMVER-MINOR** **inspector**: implement --heap-prof (Joyee Cheung) [#27596](https://github.com/nodejs/node/pull/27596)
- [[`25eb05a97a`](https://github.com/nodejs/node/commit/25eb05a97a)] - **lib**: removed unnecessary fs.realpath `options` arg check + tests (Alex Pry) [#27909](https://github.com/nodejs/node/pull/27909)
- [[`9b90385825`](https://github.com/nodejs/node/commit/9b90385825)] - **_Revert_** "**lib**: print to stdout/stderr directly instead of using console" (Richard Lau) [#27823](https://github.com/nodejs/node/pull/27823)
- [[`18650579e8`](https://github.com/nodejs/node/commit/18650579e8)] - **meta**: correct personal info (Refael Ackermann (רפאל פלחי)) [#27940](https://github.com/nodejs/node/pull/27940)
- [[`d982f0b7e2`](https://github.com/nodejs/node/commit/d982f0b7e2)] - **meta**: create github support file (Gus Caplan) [#27926](https://github.com/nodejs/node/pull/27926)
- [[`2b7ad122b2`](https://github.com/nodejs/node/commit/2b7ad122b2)] - **n-api**: DRY napi_coerce_to_x() API methods (Ben Noordhuis) [#27796](https://github.com/nodejs/node/pull/27796)
- [[`1da5acbf91`](https://github.com/nodejs/node/commit/1da5acbf91)] - **os**: assume UTF-8 for hostname (Anna Henningsen) [#27849](https://github.com/nodejs/node/pull/27849)
- [[`d406785814`](https://github.com/nodejs/node/commit/d406785814)] - **src**: unimplement deprecated v8-platform methods (Michaël Zasso) [#27872](https://github.com/nodejs/node/pull/27872)
- [[`33236b7c54`](https://github.com/nodejs/node/commit/33236b7c54)] - **(SEMVER-MINOR)** **src**: export number_of_native_contexts and number_of_detached_contexts (Yuriy Vasiyarov) [#27933](https://github.com/nodejs/node/pull/27933)
- [[`1a179e1736`](https://github.com/nodejs/node/commit/1a179e1736)] - **src**: use ArrayBufferViewContents more frequently (Anna Henningsen) [#27920](https://github.com/nodejs/node/pull/27920)
- [[`b9cc4072e6`](https://github.com/nodejs/node/commit/b9cc4072e6)] - **src**: make UNREACHABLE variadic (Refael Ackermann) [#27877](https://github.com/nodejs/node/pull/27877)
- [[`44846aebd2`](https://github.com/nodejs/node/commit/44846aebd2)] - **src**: move DiagnosticFilename inlines into a -inl.h (Sam Roberts) [#27839](https://github.com/nodejs/node/pull/27839)
- [[`d774ea5cce`](https://github.com/nodejs/node/commit/d774ea5cce)] - **src**: remove env-inl.h from header files (Sam Roberts) [#27755](https://github.com/nodejs/node/pull/27755)
- [[`02f794a53f`](https://github.com/nodejs/node/commit/02f794a53f)] - **src**: remove memory_tracker-inl.h from header files (Sam Roberts) [#27755](https://github.com/nodejs/node/pull/27755)
- [[`940577bd76`](https://github.com/nodejs/node/commit/940577bd76)] - **src**: move ThreadPoolWork inlines into a -inl.h (Sam Roberts) [#27755](https://github.com/nodejs/node/pull/27755)
- [[`c0cf17388c`](https://github.com/nodejs/node/commit/c0cf17388c)] - **src**: ignore SIGXFSZ, don't terminate (ulimit -f) (Ben Noordhuis) [#27798](https://github.com/nodejs/node/pull/27798)
- [[`a47ee80114`](https://github.com/nodejs/node/commit/a47ee80114)] - **(SEMVER-MINOR)** **stream**: convert string to Buffer when calling `unshift(<string>)` (Marcos Casagrande) [#27194](https://github.com/nodejs/node/pull/27194)
- [[`5eccd642ef`](https://github.com/nodejs/node/commit/5eccd642ef)] - **stream**: convert existing buffer when calling .setEncoding (Anna Henningsen) [#27936](https://github.com/nodejs/node/pull/27936)
- [[`6a5ce36fb8`](https://github.com/nodejs/node/commit/6a5ce36fb8)] - **test**: handle unknown message type in worker threads (Rich Trott) [#27995](https://github.com/nodejs/node/pull/27995)
- [[`182725651b`](https://github.com/nodejs/node/commit/182725651b)] - **test**: add coverage for unserializable worker thread error (Rich Trott) [#27995](https://github.com/nodejs/node/pull/27995)
- [[`887dd604f1`](https://github.com/nodejs/node/commit/887dd604f1)] - **test**: simplify fs promises test (Daniel Nalborczyk) [#27242](https://github.com/nodejs/node/pull/27242)
- [[`9229825496`](https://github.com/nodejs/node/commit/9229825496)] - **test**: covering destroying when worker already disconnected (Keroosha) [#27896](https://github.com/nodejs/node/pull/27896)
- [[`10bdd13972`](https://github.com/nodejs/node/commit/10bdd13972)] - **test**: rename test-performance to test-perf-hooks (Ujjwal Sharma) [#27969](https://github.com/nodejs/node/pull/27969)
- [[`6129376cd9`](https://github.com/nodejs/node/commit/6129376cd9)] - **test**: add coverage for sparse array maxArrayLength (went.out) [#27901](https://github.com/nodejs/node/pull/27901)
- [[`38e3827ca8`](https://github.com/nodejs/node/commit/38e3827ca8)] - **test**: add util inspect null getter test (Mikhail Kuklin) [#27884](https://github.com/nodejs/node/pull/27884)
- [[`0e1ce2055e`](https://github.com/nodejs/node/commit/0e1ce2055e)] - **test**: rsa-pss generateKeyPairSync invalid option hash (Evgenii Shchepotev) [#27883](https://github.com/nodejs/node/pull/27883)
- [[`0d74198123`](https://github.com/nodejs/node/commit/0d74198123)] - **test**: cover import of a \*.node file with a policy manifest (Evgenii Shchepotev) [#27903](https://github.com/nodejs/node/pull/27903)
- [[`6f9aa3f722`](https://github.com/nodejs/node/commit/6f9aa3f722)] - **test**: add test cases for paramEncoding 'explicit' (oksana) [#27900](https://github.com/nodejs/node/pull/27900)
- [[`682319f449`](https://github.com/nodejs/node/commit/682319f449)] - **test**: switch assertEqual arguments (Evgenii Shchepotev) [#27910](https://github.com/nodejs/node/pull/27910)
- [[`b5b234deff`](https://github.com/nodejs/node/commit/b5b234deff)] - **test**: add testcase for SourceTextModule custom inspect (Grigory Gorshkov) [#27889](https://github.com/nodejs/node/pull/27889)
- [[`630cc3ac30`](https://github.com/nodejs/node/commit/630cc3ac30)] - **test**: cover util.inspect on boxed primitive with colors (Alexander Avakov) [#27897](https://github.com/nodejs/node/pull/27897)
- [[`67b692bdb9`](https://github.com/nodejs/node/commit/67b692bdb9)] - **test**: add test case for checking typeof mgf1Hash (Levin Eugene) [#27892](https://github.com/nodejs/node/pull/27892)
- [[`2a509d40f4`](https://github.com/nodejs/node/commit/2a509d40f4)] - **test**: switch assertEqual arguments (Evgenii Shchepotev) [#27912](https://github.com/nodejs/node/pull/27912)
- [[`3ba354aaaa`](https://github.com/nodejs/node/commit/3ba354aaaa)] - **test**: add test for util.inspect (Levin Eugene) [#27906](https://github.com/nodejs/node/pull/27906)
- [[`313077ea62`](https://github.com/nodejs/node/commit/313077ea62)] - **test**: expect wpt/encoding/encodeInto.any.js to fail (Joyee Cheung) [#27860](https://github.com/nodejs/node/pull/27860)
- [[`8fc6914d09`](https://github.com/nodejs/node/commit/8fc6914d09)] - **test**: update wpt/encoding to 7287608f90 (Joyee Cheung) [#27860](https://github.com/nodejs/node/pull/27860)
- [[`0f86c2b185`](https://github.com/nodejs/node/commit/0f86c2b185)] - **test**: run WPT in subdirectories (Joyee Cheung) [#27860](https://github.com/nodejs/node/pull/27860)
- [[`51ccdae445`](https://github.com/nodejs/node/commit/51ccdae445)] - **test**: expect wpt/encoding/streams to fail (Joyee Cheung) [#27860](https://github.com/nodejs/node/pull/27860)
- [[`652cadba1c`](https://github.com/nodejs/node/commit/652cadba1c)] - **test**: fix arguments order of comparsion functions (martyns0n) [#27907](https://github.com/nodejs/node/pull/27907)
- [[`b117f6d5d8`](https://github.com/nodejs/node/commit/b117f6d5d8)] - **test**: switch assertEqual arguments (Evgenii Shchepotev) [#27913](https://github.com/nodejs/node/pull/27913)
- [[`e7966bcb80`](https://github.com/nodejs/node/commit/e7966bcb80)] - **test**: unhardcode server port (MurkyMeow) [#27908](https://github.com/nodejs/node/pull/27908)
- [[`b83571d236`](https://github.com/nodejs/node/commit/b83571d236)] - **test**: add a test case for the path.posix.resolve (Grigorii K. Shartsev) [#27905](https://github.com/nodejs/node/pull/27905)
- [[`f5bb1b380f`](https://github.com/nodejs/node/commit/f5bb1b380f)] - **test**: switch actual value argument and expected in deepStrictEqual call (Kopachyov Vitaliy) [#27888](https://github.com/nodejs/node/pull/27888)
- [[`531669b917`](https://github.com/nodejs/node/commit/531669b917)] - **test**: fix test-http2-multiheaders-raw (Grigorii K. Shartsev) [#27885](https://github.com/nodejs/node/pull/27885)
- [[`724d9c89bc`](https://github.com/nodejs/node/commit/724d9c89bc)] - **test**: change expected and actual values in assert call (oksana) [#27881](https://github.com/nodejs/node/pull/27881)
- [[`34ef9e4a2b`](https://github.com/nodejs/node/commit/34ef9e4a2b)] - **test**: detect missing postmortem metadata (cjihrig) [#27828](https://github.com/nodejs/node/pull/27828)
- [[`bfcbab4c0c`](https://github.com/nodejs/node/commit/bfcbab4c0c)] - **test**: fix test-https-agent-additional-options (Rich Trott) [#27830](https://github.com/nodejs/node/pull/27830)
- [[`a4c1fd5ffc`](https://github.com/nodejs/node/commit/a4c1fd5ffc)] - **test**: refactor test-https-agent-additional-options (Rich Trott) [#27830](https://github.com/nodejs/node/pull/27830)
- [[`17abc8c942`](https://github.com/nodejs/node/commit/17abc8c942)] - **test**: favor arrow functions for anonymous callbacks (Rich Trott) [#27830](https://github.com/nodejs/node/pull/27830)
- [[`155b947251`](https://github.com/nodejs/node/commit/155b947251)] - **test**: replace flag with option (Rich Trott) [#27830](https://github.com/nodejs/node/pull/27830)
- [[`144db48b6d`](https://github.com/nodejs/node/commit/144db48b6d)] - **test**: update wpt/url to 418f7fabeb (Joyee Cheung) [#27822](https://github.com/nodejs/node/pull/27822)
- [[`65d4f734e0`](https://github.com/nodejs/node/commit/65d4f734e0)] - **test**: use ShellTestEnvironment in WPT (Joyee Cheung) [#27822](https://github.com/nodejs/node/pull/27822)
- [[`a9a400e604`](https://github.com/nodejs/node/commit/a9a400e604)] - **test**: update wpt/resources to e1fddfbf80 (Joyee Cheung) [#27822](https://github.com/nodejs/node/pull/27822)
- [[`8040d8b321`](https://github.com/nodejs/node/commit/8040d8b321)] - **test**: increase debugging information on failure (Rich Trott) [#27790](https://github.com/nodejs/node/pull/27790)
- [[`6548b91835`](https://github.com/nodejs/node/commit/6548b91835)] - **tls**: trace errors can show up as SSL errors (Sam Roberts) [#27841](https://github.com/nodejs/node/pull/27841)
- [[`0fe16edfab`](https://github.com/nodejs/node/commit/0fe16edfab)] - **tls**: group chunks into TLS segments (Alba Mendez) [#27861](https://github.com/nodejs/node/pull/27861)
- [[`e8fa0671a4`](https://github.com/nodejs/node/commit/e8fa0671a4)] - **tls**: destroy trace BIO instead of leaking it (Sam Roberts) [#27834](https://github.com/nodejs/node/pull/27834)
- [[`10e0d7f2ac`](https://github.com/nodejs/node/commit/10e0d7f2ac)] - **tls**: support the hints option (Luigi Pinca) [#27816](https://github.com/nodejs/node/pull/27816)
- [[`4716caa12e`](https://github.com/nodejs/node/commit/4716caa12e)] - **tls**: set tlsSocket.servername as early as possible (oyyd) [#27759](https://github.com/nodejs/node/pull/27759)
- [[`2ce24a9452`](https://github.com/nodejs/node/commit/2ce24a9452)] - **tools**: fix js2c regression (Refael Ackermann) [#27980](https://github.com/nodejs/node/pull/27980)
- [[`a75a59d3e3`](https://github.com/nodejs/node/commit/a75a59d3e3)] - **tools**: update inspector_protocol to 0aafd2 (Michaël Zasso) [#27770](https://github.com/nodejs/node/pull/27770)
- [[`728bc2f59a`](https://github.com/nodejs/node/commit/728bc2f59a)] - **tools**: update dependencies in tools/doc (Rich Trott) [#27927](https://github.com/nodejs/node/pull/27927)
- [[`b54f3e0405`](https://github.com/nodejs/node/commit/b54f3e0405)] - **tools**: edit .eslintrc.js for minor maintainability improvements (Rich Trott) [#27789](https://github.com/nodejs/node/pull/27789)

Windows 32-bit Installer: https://nodejs.org/dist/v12.4.0/node-v12.4.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v12.4.0/node-v12.4.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v12.4.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v12.4.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v12.4.0/node-v12.4.0.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v12.4.0/node-v12.4.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v12.4.0/node-v12.4.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v12.4.0/node-v12.4.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v12.4.0/node-v12.4.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v12.4.0/node-v12.4.0-aix-ppc64.tar.gz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v12.4.0/node-v12.4.0-sunos-x64.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v12.4.0/node-v12.4.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v12.4.0/node-v12.4.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v12.4.0/node-v12.4.0.tar.gz \
Other release files: https://nodejs.org/dist/v12.4.0/ \
Documentation: https://nodejs.org/docs/v12.4.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

b5fbeca9b72725a9825cad0a73af3f804ec01f8a7bac335fc9547c89443ad7f6  node-v12.4.0-aix-ppc64.tar.gz
aaff97d59cda775165ef966ae74e70f55f3267e86d735ed3740ae9bf1d40531e  node-v12.4.0-darwin-x64.tar.gz
2457811f736d94ee33f94c6cc31cd5463ff526fc7f0d9bcc020c3c605c6077fd  node-v12.4.0-darwin-x64.tar.xz
3a5bbb6ad1a5f3ba912981c13910d7c617bb8fa6b6ae2026d6aee2edcd590a45  node-v12.4.0-headers.tar.gz
00ec93780abfc32ab207c25319ac370a75f383efe84d541b5be4e29583c4ea35  node-v12.4.0-headers.tar.xz
312a7942f5fbd0aa83d6e624a06681275db2cb3c3eeaf3e452ad04aac17b6de5  node-v12.4.0-linux-arm64.tar.gz
bb2e7eabebdceb52424516dabce94cb37f55419e6ed19493916843a323c423cd  node-v12.4.0-linux-arm64.tar.xz
12929c2128f740a47690c5e991c401b213a0b0923383f40ed53c1ca44ae99ec2  node-v12.4.0-linux-armv7l.tar.gz
5f3334d313804671dac22720ea8ef801efac231a1933e255beebe9217c9294d7  node-v12.4.0-linux-armv7l.tar.xz
7fc99ee4ea2fb145599e115f04f68070e221c83677a1089fc2d5a271133c6f7d  node-v12.4.0-linux-ppc64le.tar.gz
6de87f20b43cd621b745f4c476965e705d3416e0acdc8acb67aa69ed62a98e99  node-v12.4.0-linux-ppc64le.tar.xz
bf7c825cdad31696bdc947a621434bbf3f48280161c333037853752591b60832  node-v12.4.0-linux-s390x.tar.gz
8dfa841d83e47db766d0ac6f7866d233276138691f3a56ff2f6e2a5c7f00d9d0  node-v12.4.0-linux-s390x.tar.xz
9a16909157e68d4e409a73b008994ed05b4b6bc952b65ffa7fbc5abb973d31e9  node-v12.4.0-linux-x64.tar.gz
9aec6a2a50c1791704a6069cbda6da62781361e44814d024e8bbaaf0deb41c5e  node-v12.4.0-linux-x64.tar.xz
df5f09b785f34fe545beb43dd73d410de5f4577fe1631f88b49d736a009f93d1  node-v12.4.0.pkg
69c143a49e7f8276b8058e7dd35c07b723aa6003d0a24cd3842818328c502b7e  node-v12.4.0-sunos-x64.tar.gz
b91deefabdb52ce12645e04f2d089ec8b9daa63adc9e4b4898664c7060198a64  node-v12.4.0-sunos-x64.tar.xz
cf39befb8ea486363cac6f297d8f451fd0d5dd8ad21dca51e9c2ea86b242baa0  node-v12.4.0.tar.gz
20b52366d3502c60d4aec0bd5052c841a94bf1cd5d74b2ea1e498c1a2ada8ce3  node-v12.4.0.tar.xz
0044440b9d2b6f99d5c93eee6da3ecbf9d186130e2a1c61664f03a1b52797c41  node-v12.4.0-win-x64.7z
ec8623e2528a35d3219200308e7ed41e24d4f7cd96530a4e6ac2513e44f7fad1  node-v12.4.0-win-x64.zip
b85b199e84fb3a3912481c6ccf01031857717ce91b2137e4150b0ba6aff0de7c  node-v12.4.0-win-x86.7z
9240ba7da7f8704aa5d1e9caeac8914bd3d38965564c5f5a49bb9727e15b2cb4  node-v12.4.0-win-x86.zip
323a41a8c5ec6fc6f48dc853e9db886a1c23853d6d08a679907a77eda6ae5e61  node-v12.4.0-x64.msi
2bb09ebce2b62da1382f8f7b28116bea7aa37f5d1d9509e9daa4a94a76860cf7  node-v12.4.0-x86.msi
7c042a1980ba080199486ecf32272e28f205bcffcae54a1fa72c150910fbb3f8  win-x64/node.exe
5b198b078cb2924b29148de188296151ce8fca9683735c63d3104969e132bdd0  win-x64/node.lib
b83d389a4e0b4397703fd7b0f3bcf6516923d377816e009435566e68f34eb110  win-x64/node_pdb.7z
f32cb2c6d468a1bd2fa38d064eb39cfd069c49bce6a505e5de120c3d5d0925dd  win-x64/node_pdb.zip
15618b6a205b8a7814122124348d0154cdfc2512682c2ab75a4f26bb78d5a44a  win-x86/node.exe
094cf74776ea08a116e65d19e1facf705c2ac64e6f04c347aa4bf2293c1378c0  win-x86/node.lib
2d6e1fa44f9484a7f415109adb927ed5f558c4343e2939c3d50f7f9611c19b69  win-x86/node_pdb.7z
72b09155fce8fe93644f2af062682bcfaea1908302e12f1576189e145d07cda1  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEj8yhP+8dDC6RAI4Jdw96mlrhVgAFAlz2eiAACgkQdw96mlrh
VgDfexAAlvr9rWDSXETbJBMc98KoFsguCiDPmrE1m87S70DhATfgEtmJe9gFckQj
5toAyH2m7BvgaKUiCUCadJXalciIS7i5vIP+c3H9/FQAaw/uO7a/Q4Ye+Onb5YUR
myAiBtAF9fHjyioxV7rW62LQHZukHxCREHznvLrUQ4WcB14RmR8JT6Udw1DQCrX2
GjJhAIQrS4h3fLOBf1B2On8sDq26+k17pbn++YiMd9z9053WxQJrN6xjYfRORRcg
BgISlddh1vQ/xJMtd51bt1VJU+U5a2yfyyPIcZSsbkBMlUspu3hifAhRAbSARHdw
uRQxKUmxIjoZMNyGuGx3qTRIhZkurhtKnw96ZQSTKwNx2t2x897qebYnbmTgF8un
xsVT3FA/Wq67PgZCTH3Hhn1OJICB2K9uciWV9MfH9b9VXdGY79PrTvdoLYJJMXwT
hwaMtR1zisWukpsD/cTH7ftiKB/Kt6rIS1YesmQdtpXjf+Zyejgbg6foJGsMVYZJ
44ZEoSeFxzS4AL/QCvI8X6oqbZjDyk30pgL1DmSIvIDBA/4xcWmvLLs28sMf63F5
U2lPH/is/WbmtMI2+ACvzkAZygWARNE3Vu88rpD4CKRfrcnqaQ04uxkyQRU4ZbKH
nmeLvopym5OvIbsEKvjh0S2VXAkJ1CZ4+ZKYNfaw/ggyiw6+x+0=
=XgQX
-----END PGP SIGNATURE-----

```
