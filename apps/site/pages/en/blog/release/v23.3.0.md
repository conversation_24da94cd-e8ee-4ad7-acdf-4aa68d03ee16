---
date: '2024-11-20T23:01:40.107Z'
category: release
title: Node v23.3.0 (Current)
layout: blog-post
author: <PERSON>
---

## 2024-11-20, Version 23.3.0 (Current), @RafaelGSS

### Notable Changes

- \[[`5767b76c30`](https://github.com/nodejs/node/commit/5767b76c30)] - **doc**: enforce strict policy to semver-major releases (<PERSON>) [#55732](https://github.com/nodejs/node/pull/55732)
- \[[`ccb69bb8d5`](https://github.com/nodejs/node/commit/ccb69bb8d5)] - **(SEMVER-MINOR)** **src**: add cli option to preserve env vars on diagnostic reports (<PERSON>) [#55697](https://github.com/nodejs/node/pull/55697)
- \[[`d4e792643d`](https://github.com/nodejs/node/commit/d4e792643d)] - **(SEMVER-MINOR)** **util**: add sourcemap support to getCallSites (<PERSON>) [#55589](https://github.com/nodejs/node/pull/55589)
- \[[`00e092bb4b`](https://github.com/nodejs/node/commit/00e092bb4b)] - **(SEMVER-MINOR)** **util**: fix util.getCallSites plurality (Chengzhong Wu) [#55626](https://github.com/nodejs/node/pull/55626)

### Commits

- \[[`9862912d41`](https://github.com/nodejs/node/commit/9862912d41)] - **assert**: differentiate cases where `cause` is `undefined` or missing (Antoine du Hamel) [#55738](https://github.com/nodejs/node/pull/55738)
- \[[`32e5bbca95`](https://github.com/nodejs/node/commit/32e5bbca95)] - **benchmark**: add `test-reporters` (Aviv Keller) [#55757](https://github.com/nodejs/node/pull/55757)
- \[[`c2103354e6`](https://github.com/nodejs/node/commit/c2103354e6)] - **benchmark**: add `test_runner/mock-fn` (Aviv Keller) [#55771](https://github.com/nodejs/node/pull/55771)
- \[[`472d55e3e4`](https://github.com/nodejs/node/commit/472d55e3e4)] - **build**: implement node_use_amaro flag in GN build (Cheng) [#55798](https://github.com/nodejs/node/pull/55798)
- \[[`77735674eb`](https://github.com/nodejs/node/commit/77735674eb)] - **build**: use glob for dependencies of out/Makefile (Richard Lau) [#55789](https://github.com/nodejs/node/pull/55789)
- \[[`bba7323d51`](https://github.com/nodejs/node/commit/bba7323d51)] - **build**: apply cpp linting and formatting to ncrypto (Aviv Keller) [#55362](https://github.com/nodejs/node/pull/55362)
- \[[`e0c222525e`](https://github.com/nodejs/node/commit/e0c222525e)] - **crypto**: allow length=0 for HKDF and PBKDF2 in SubtleCrypto.deriveBits (Filip Skokan) [#55866](https://github.com/nodejs/node/pull/55866)
- \[[`cad557ec53`](https://github.com/nodejs/node/commit/cad557ec53)] - **deps**: update simdutf to 5.6.1 (Node.js GitHub Bot) [#55850](https://github.com/nodejs/node/pull/55850)
- \[[`dc8aca3692`](https://github.com/nodejs/node/commit/dc8aca3692)] - **deps**: update undici to 6.21.0 (Node.js GitHub Bot) [#55851](https://github.com/nodejs/node/pull/55851)
- \[[`e0db9ede4f`](https://github.com/nodejs/node/commit/e0db9ede4f)] - **deps**: update c-ares to v1.34.3 (Node.js GitHub Bot) [#55803](https://github.com/nodejs/node/pull/55803)
- \[[`e147935144`](https://github.com/nodejs/node/commit/e147935144)] - **deps**: update icu to 76.1 (Node.js GitHub Bot) [#55551](https://github.com/nodejs/node/pull/55551)
- \[[`e0ef65b8d5`](https://github.com/nodejs/node/commit/e0ef65b8d5)] - **doc**: remove non-working example (Antoine du Hamel) [#55856](https://github.com/nodejs/node/pull/55856)
- \[[`ec953bca09`](https://github.com/nodejs/node/commit/ec953bca09)] - **doc**: add `node:sqlite` to mandatory `node:` prefix list (翠 / green) [#55846](https://github.com/nodejs/node/pull/55846)
- \[[`1b863b96d5`](https://github.com/nodejs/node/commit/1b863b96d5)] - **doc**: add `-S` flag release preparation example (Antoine du Hamel) [#55836](https://github.com/nodejs/node/pull/55836)
- \[[`a8311847d1`](https://github.com/nodejs/node/commit/a8311847d1)] - **doc**: clarify UV_THREADPOOL_SIZE env var usage (Preveen P) [#55832](https://github.com/nodejs/node/pull/55832)
- \[[`787e51e603`](https://github.com/nodejs/node/commit/787e51e603)] - **doc**: add notable-change mention to sec release (Rafael Gonzaga) [#55830](https://github.com/nodejs/node/pull/55830)
- \[[`e56265cc18`](https://github.com/nodejs/node/commit/e56265cc18)] - **doc**: fix history info for `URL.prototype.toJSON` (Antoine du Hamel) [#55818](https://github.com/nodejs/node/pull/55818)
- \[[`c5afdaf5cb`](https://github.com/nodejs/node/commit/c5afdaf5cb)] - **doc**: correct max-semi-space-size statement (Joe Bowbeer) [#55812](https://github.com/nodejs/node/pull/55812)
- \[[`65ffb2cae3`](https://github.com/nodejs/node/commit/65ffb2cae3)] - **doc**: update unflag info of `import.meta.resolve` (skyclouds2001) [#55810](https://github.com/nodejs/node/pull/55810)
- \[[`9aeb671677`](https://github.com/nodejs/node/commit/9aeb671677)] - **doc**: run license-builder (github-actions\[bot]) [#55813](https://github.com/nodejs/node/pull/55813)
- \[[`df5ea1a5b3`](https://github.com/nodejs/node/commit/df5ea1a5b3)] - **doc**: clarify triager role (Gireesh Punathil) [#55775](https://github.com/nodejs/node/pull/55775)
- \[[`aa12de0f03`](https://github.com/nodejs/node/commit/aa12de0f03)] - **doc**: sort --report-exclude alphabetically (Rafael Gonzaga) [#55788](https://github.com/nodejs/node/pull/55788)
- \[[`8576ca9897`](https://github.com/nodejs/node/commit/8576ca9897)] - **doc**: clarify removal of experimental API does not require a deprecation (Antoine du Hamel) [#55746](https://github.com/nodejs/node/pull/55746)
- \[[`5767b76c30`](https://github.com/nodejs/node/commit/5767b76c30)] - **doc**: enforce strict policy to semver-major releases (Rafael Gonzaga) [#55732](https://github.com/nodejs/node/pull/55732)
- \[[`1f2fcf1dc8`](https://github.com/nodejs/node/commit/1f2fcf1dc8)] - **doc**: add history entries for JSON modules stabilization (Antoine du Hamel) [#55855](https://github.com/nodejs/node/pull/55855)
- \[[`83ba688d8f`](https://github.com/nodejs/node/commit/83ba688d8f)] - **esm**: fix import.meta.resolve crash (Marco Ippolito) [#55777](https://github.com/nodejs/node/pull/55777)
- \[[`bdb6d12e7a`](https://github.com/nodejs/node/commit/bdb6d12e7a)] - **events**: add hasEventListener util for validate (Sunghoon) [#55230](https://github.com/nodejs/node/pull/55230)
- \[[`d41cb49516`](https://github.com/nodejs/node/commit/d41cb49516)] - **fs**: prevent unwanted `dependencyOwners` removal (Carlos Espa) [#55565](https://github.com/nodejs/node/pull/55565)
- \[[`db0d648d8f`](https://github.com/nodejs/node/commit/db0d648d8f)] - **fs**: fix bufferSize option for opendir recursive (Ethan Arrowood) [#55744](https://github.com/nodejs/node/pull/55744)
- \[[`693fda0802`](https://github.com/nodejs/node/commit/693fda0802)] - **lib**: remove unused file `fetch_module` (Michaël Zasso) [#55880](https://github.com/nodejs/node/pull/55880)
- \[[`156873303a`](https://github.com/nodejs/node/commit/156873303a)] - **lib**: prefer symbol to number in webidl `type` function (Antoine du Hamel) [#55737](https://github.com/nodejs/node/pull/55737)
- \[[`cfe28b161a`](https://github.com/nodejs/node/commit/cfe28b161a)] - **lib**: remove unnecessary optional chaining (Gürgün Dayıoğlu) [#55728](https://github.com/nodejs/node/pull/55728)
- \[[`bbb8f5914d`](https://github.com/nodejs/node/commit/bbb8f5914d)] - **lib**: use `Promise.withResolvers()` in timers (Yagiz Nizipli) [#55720](https://github.com/nodejs/node/pull/55720)
- \[[`11e1bdd409`](https://github.com/nodejs/node/commit/11e1bdd409)] - **module**: tidy code string concat → string templates (Jacob Smith) [#55820](https://github.com/nodejs/node/pull/55820)
- \[[`9c99255468`](https://github.com/nodejs/node/commit/9c99255468)] - **permission**: ignore internalModuleStat on module loading (Rafael Gonzaga) [#55797](https://github.com/nodejs/node/pull/55797)
- \[[`5a437c446f`](https://github.com/nodejs/node/commit/5a437c446f)] - **report**: fix network queries in getReport libuv with exclude-network (Adrien Foulon) [#55602](https://github.com/nodejs/node/pull/55602)
- \[[`bcbba723de`](https://github.com/nodejs/node/commit/bcbba723de)] - **sqlite**: add support for SQLite Session Extension (Bart Louwers) [#54181](https://github.com/nodejs/node/pull/54181)
- \[[`49d55228de`](https://github.com/nodejs/node/commit/49d55228de)] - **src**: use env strings to create sqlite results (Michaël Zasso) [#55785](https://github.com/nodejs/node/pull/55785)
- \[[`58d7a6ec10`](https://github.com/nodejs/node/commit/58d7a6ec10)] - _**Revert**_ "**src**: migrate `String::Value` to `String::ValueView`" (Michaël Zasso) [#55828](https://github.com/nodejs/node/pull/55828)
- \[[`16786a6df8`](https://github.com/nodejs/node/commit/16786a6df8)] - **src**: improve `node:os` userInfo performance (Yagiz Nizipli) [#55719](https://github.com/nodejs/node/pull/55719)
- \[[`ccb69bb8d5`](https://github.com/nodejs/node/commit/ccb69bb8d5)] - **(SEMVER-MINOR)** **src**: add cli option to preserve env vars on dr (Rafael Gonzaga) [#55697](https://github.com/nodejs/node/pull/55697)
- \[[`770670c52c`](https://github.com/nodejs/node/commit/770670c52c)] - **test**: fix permission fixtures lint (Rafael Gonzaga) [#55819](https://github.com/nodejs/node/pull/55819)
- \[[`84c47478d0`](https://github.com/nodejs/node/commit/84c47478d0)] - **test**: improve test coverage for child process message sending (Juan José) [#55710](https://github.com/nodejs/node/pull/55710)
- \[[`e1f54e2527`](https://github.com/nodejs/node/commit/e1f54e2527)] - **test**: ensure that test priority is not higher than current priority (Livia Medeiros) [#55739](https://github.com/nodejs/node/pull/55739)
- \[[`e1b42e7637`](https://github.com/nodejs/node/commit/e1b42e7637)] - **test**: add buffer to fs_permission tests (Rafael Gonzaga) [#55734](https://github.com/nodejs/node/pull/55734)
- \[[`d1ad43e9ae`](https://github.com/nodejs/node/commit/d1ad43e9ae)] - **test**: improve test coverage for `ServerResponse` (Juan José) [#55711](https://github.com/nodejs/node/pull/55711)
- \[[`034505e037`](https://github.com/nodejs/node/commit/034505e037)] - **test_runner**: error on mocking an already mocked date (Aviv Keller) [#55858](https://github.com/nodejs/node/pull/55858)
- \[[`44324aa7e9`](https://github.com/nodejs/node/commit/44324aa7e9)] - **tools**: bump @eslint/plugin-kit from 0.2.0 to 0.2.3 in /tools/eslint (dependabot\[bot]) [#55875](https://github.com/nodejs/node/pull/55875)
- \[[`3cfacd3fbb`](https://github.com/nodejs/node/commit/3cfacd3fbb)] - **tools**: fix exclude labels for commit-queue (Richard Lau) [#55809](https://github.com/nodejs/node/pull/55809)
- \[[`8111a7655d`](https://github.com/nodejs/node/commit/8111a7655d)] - **tools**: make commit-queue check blocked label (Marco Ippolito) [#55781](https://github.com/nodejs/node/pull/55781)
- \[[`419ea068fb`](https://github.com/nodejs/node/commit/419ea068fb)] - **tools**: remove non-existent file from eslint config (Aviv Keller) [#55772](https://github.com/nodejs/node/pull/55772)
- \[[`7814669377`](https://github.com/nodejs/node/commit/7814669377)] - **tools**: fix c-ares updater script for Node.js 18 (Richard Lau) [#55717](https://github.com/nodejs/node/pull/55717)
- \[[`3a9733cc4f`](https://github.com/nodejs/node/commit/3a9733cc4f)] - **util**: do not mark experimental feature as deprecated (Antoine du Hamel) [#55740](https://github.com/nodejs/node/pull/55740)
- \[[`d4e792643d`](https://github.com/nodejs/node/commit/d4e792643d)] - **(SEMVER-MINOR)** **util**: add sourcemap support to getCallSites (Marco Ippolito) [#55589](https://github.com/nodejs/node/pull/55589)
- \[[`00e092bb4b`](https://github.com/nodejs/node/commit/00e092bb4b)] - **(SEMVER-MINOR)** **util**: fix util.getCallSites plurality (Chengzhong Wu) [#55626](https://github.com/nodejs/node/pull/55626)

Windows 64-bit Installer: https://nodejs.org/dist/v23.3.0/node-v23.3.0-x64.msi \
Windows ARM 64-bit Installer: https://nodejs.org/dist/v23.3.0/node-v23.3.0-arm64.msi \
Windows 64-bit Binary: https://nodejs.org/dist/v23.3.0/win-x64/node.exe \
Windows ARM 64-bit Binary: https://nodejs.org/dist/v23.3.0/win-arm64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v23.3.0/node-v23.3.0.pkg \
macOS Apple Silicon 64-bit Binary: https://nodejs.org/dist/v23.3.0/node-v23.3.0-darwin-arm64.tar.gz \
macOS Intel 64-bit Binary: https://nodejs.org/dist/v23.3.0/node-v23.3.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v23.3.0/node-v23.3.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v23.3.0/node-v23.3.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v23.3.0/node-v23.3.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v23.3.0/node-v23.3.0-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v23.3.0/node-v23.3.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v23.3.0/node-v23.3.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v23.3.0/node-v23.3.0.tar.gz \
Other release files: https://nodejs.org/dist/v23.3.0/ \
Documentation: https://nodejs.org/docs/v23.3.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

7573dd593cc56600ef573be8c5020ea82847ff26ad0fe94c0fa08470ba2e95ea  node-v23.3.0-aix-ppc64.tar.gz
5488e9c0a25fa69107dcc25b91fa1a27caa6ce2ced24947f3932c2cf6f646bc5  node-v23.3.0-arm64.msi
472b9109dd9987ea49916f12b01ed2f09f1c4bf4befcf5695c1b9fd9cff3d177  node-v23.3.0-darwin-arm64.tar.gz
4a6258e4f61b86990e9bd7575cfb22d2819ae4f5b9fa2f93badc507f5ebd8feb  node-v23.3.0-darwin-arm64.tar.xz
d1898f7901277968b78066f6b512cbc3bd7f2406950377826f9d8e02d4e24fe6  node-v23.3.0-darwin-x64.tar.gz
32adb685dfde2ff57bf7f97ab2d469b4860332ba0b250417e02edf5c34dbbbd0  node-v23.3.0-darwin-x64.tar.xz
c018d7f1bafe884b0d941994db425902a9defbce2fdb2624a2f62c6de355f873  node-v23.3.0-headers.tar.gz
cb67347c0f3ab9a9dc077b7c330b55a3fcac2a4a598d9de231bd6fdbe4213f2a  node-v23.3.0-headers.tar.xz
af48591482236007d21665aa2bd3d145f490aa953e85c1ca7a7c7bf04b711341  node-v23.3.0-linux-arm64.tar.gz
fd6b744f8689504918f3ce180ca2839bf51099632877451de2586138598a8d9e  node-v23.3.0-linux-arm64.tar.xz
95771bae444dcf2e7736c84ec328750267315f8118fdd49bc22eecdd2fe04998  node-v23.3.0-linux-armv7l.tar.gz
8d0366e3345be802ba658713cf30801bf4ce442be4a1dccc4a5f4224f087f18d  node-v23.3.0-linux-armv7l.tar.xz
0b1b243804d2e97ae5013161a14b861d8fcdd44586b51d16260665aab5134603  node-v23.3.0-linux-ppc64le.tar.gz
9bd1bb597c0abed9ff742cd2be7e5ee7732e8bfa8941f0f9717341dc59cd6b60  node-v23.3.0-linux-ppc64le.tar.xz
3413be8e927c49b6836fdd10b0f89f16f811b4c3c9085cd1165ce3443f381353  node-v23.3.0-linux-s390x.tar.gz
a9d37a934135b8d3371cd6662d5c5b7e423355c1015b7fdf2b48758adae19cbd  node-v23.3.0-linux-s390x.tar.xz
a4ceaf6e41d76017ee56705badc5d492cf3d5feca607c7a7d3ae54f7db14913b  node-v23.3.0-linux-x64.tar.gz
98e8201aa842efbf55c11c2a04fb6610d5793523b0cfbb34d61cd0495195c17d  node-v23.3.0-linux-x64.tar.xz
9656926b25dab9c0522d3e0b3ac3d19363d2e4917022de6a1a33d7b805128378  node-v23.3.0-win-arm64.7z
9c0e84e160d3730741782bf389534ca1b41f58427c2fc8c5ca1541b2b089103c  node-v23.3.0-win-arm64.zip
b6a5e9b1a94c52af3e37dea8251509a81b06f6a41847a48a28397bad71e5062a  node-v23.3.0-win-x64.7z
868cd7af6f0c042944c38b1bc1212d608177478da2a141bd404f9366d8fbd2dd  node-v23.3.0-win-x64.zip
fd842e751de2d3ed9c30b678d4abc3d3cd8d137eaf7b92aa3a1b6deeea7af0e1  node-v23.3.0-x64.msi
2864f99aad3e27555f09f473f0b3dbbbc4b6332a93ab39c374064f0ced340f18  node-v23.3.0.pkg
256b2282698cf9eb1124485a5fde9415e0d106a670476dd6dc23bc228d7c2df9  node-v23.3.0.tar.gz
42a6b5611aeec6723f4b6f98f1c205fc1fa32399df41dbed6a27083afd48c5c1  node-v23.3.0.tar.xz
e997f328611ae7770958018336bde3364715681e46bffe96b95d719bcbed9fa2  win-arm64/node.exe
a663818787224e59b0d571dc8346b8b9e8fc99786753971120fad7879bdcf24d  win-arm64/node.lib
0b824aefcb1eb19f919cc8541fccba952998ad7c0fde3f1d22925bde75c222df  win-arm64/node_pdb.7z
f37c1c7c5c2b5c0ba3749929022c79b3d6a8e4ca1a639f50478adfe5430d51ed  win-arm64/node_pdb.zip
838d91e322a08e0955eb8c605d30e654544efca412dedce9dd2b8b83cb6bb997  win-x64/node.exe
16d5b600ec216018913aea9a62de20765fdefbc1ec2cfd9ad6c39fb92e26678c  win-x64/node.lib
8c03806384c118eac882c8c609de05ae96b29cd4579059e7b554062247aa5ac5  win-x64/node_pdb.7z
33468332f69aa45ea55fe26f616971eb3ecb914e6d190367f71b08ebd856aa93  win-x64/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQGzBAEBCAAdFiEEiQwI24V5Fi/uDfnbi+q0389VXvQFAmc+afkACgkQi+q0389V
XvSZpgwAnaNVEDlSpRUsl7ugD0N6wwZzfLszOQYe6hlzNRGnYOYAOO6WnuWJLYKv
YDwU/9Z/LBsMQogQQ128PZW7S7ojcndB2FN0iR56zWfvqnniZUxRcngz82Mr9sIP
9wXPv43IXG3taOgVa4V+c37dB37Uah3Udz3IqmUjmwn+BBxIiPxswYsfseTanCn5
dYLeiCYg2nBGKnFLBRK1+E6XHH8uGY+whCgSj0F7e18wTfQ1Xp7aS9DyBW6fs+ax
D6hOpsk2RbpX9fkAhHkxTkiDCL1JqoQLZZdz3xzObkuWA2yVyuh/28C/yz+O4QDc
TfxduDHkaRAABci8eEYGxuCv2Pp2H+7zxARRP/LtZZqXCvdFzO5EyvKX+x9Hwveq
cPMUZEv0cqxsPK4Kp4tXOTisalcAA7g55ccU47cyx0CKHh3ORl+fbXdstbUwicNh
LlH5hpT1PObb6kb341oXZ07jH3y6u2ksuKBNKGZUtdOXDVLEnkhnXS21OufBUpzR
sn52cpl3
=rs4F
-----END PGP SIGNATURE-----
```
