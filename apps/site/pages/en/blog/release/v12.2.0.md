---
date: '2019-05-07T18:52:27.936Z'
category: release
title: Node v12.2.0 (Current)
layout: blog-post
author: <PERSON><PERSON><PERSON>
---

### Notable changes

- **deps**:
  - Updated llhttp to 1.1.3. This fixes a bug that made Node.js' HTTP parser
    refuse any request URL that contained the "|" (vertical bar) character (<PERSON><PERSON>) [#27595](https://github.com/nodejs/node/pull/27595).
- **tls**:
  - Added an `enableTrace()` method to `TLSSocket` and an `enableTrace` option
    to `tls.createServer()`. When enabled, TSL packet trace information is
    written to `stderr`. This can be used to debug TLS connection problems (cjihrig) [#27497](https://github.com/nodejs/node/pull/27497), (<PERSON>) [#27376](https://github.com/nodejs/node/pull/27376).
- **cli**:
  - Added a `--trace-tls` command-line flag that enables tracing of TLS
    connections without the need to modify existing application code (cjihrig) [#27497](https://github.com/nodejs/node/pull/27497).
  - Added a `--cpu-prof-interval` command-line flag. It can be used to specify
    the sampling interval for the CPU profiles generated by `--cpu-prof` (Joyee Cheung) [#27535](https://github.com/nodejs/node/pull/27535).
- **module**:
  - Added the `createRequire()` method. It allows to create a require function
    from a file URL object, a file URL string or an absolute path string. The
    existing `createRequireFromPath()` method is now deprecated (Myles Borins) [#27405](https://github.com/nodejs/node/pull/27405).
  - Throw on `require('./path.mjs')`. This is technically a breaking change that
    should have landed with Node.js 12.0.0. It is necessary to have this to keep
    the possibility for a future minor version to load ES Modules with the
    require function (Myles Borins) [#27417](https://github.com/nodejs/node/pull/27417).
- **repl**:
  - The REPL now supports multi-line statements using `BigInt` literals as well
    as public and private class fields and methods (Ruben Bridgewater) [#27400](https://github.com/nodejs/node/pull/27400).
  - The REPL now supports tab autocompletion of file paths with `fs` methods (Anto Aravinth) [#26648](https://github.com/nodejs/node/pull/26648).
- **meta**:
  - Added [Christian Clauss](https://github.com/cclauss) to collaborators [#27554](https://github.com/nodejs/node/pull/27554).

### Commits

- [[`c0ab2a141b`](https://github.com/nodejs/node/commit/c0ab2a141b)] - **assert**: use new language features (Ruben Bridgewater) [#27400](https://github.com/nodejs/node/pull/27400)
- [[`4b3d0d1953`](https://github.com/nodejs/node/commit/4b3d0d1953)] - **async_hooks**: fixup do not reuse HTTPParser (Gerhard Stoebich) [#27477](https://github.com/nodejs/node/pull/27477)
- [[`cfc7bdd303`](https://github.com/nodejs/node/commit/cfc7bdd303)] - **benchmark**: add benchmark for node -p (Joyee Cheung) [#27320](https://github.com/nodejs/node/pull/27320)
- [[`53eefeb73e`](https://github.com/nodejs/node/commit/53eefeb73e)] - **buffer**: remove unreachable code (Rich Trott) [#27445](https://github.com/nodejs/node/pull/27445)
- [[`cac584d260`](https://github.com/nodejs/node/commit/cac584d260)] - **buffer,errors**: improve bigint, big numbers and more (Ruben Bridgewater) [#27228](https://github.com/nodejs/node/pull/27228)
- [[`22a5a05785`](https://github.com/nodejs/node/commit/22a5a05785)] - **build**: delegate building from Makefile to ninja (Refael Ackermann) [#27504](https://github.com/nodejs/node/pull/27504)
- [[`67205f5941`](https://github.com/nodejs/node/commit/67205f5941)] - **build**: remove unsupported Python 2.6 from configure (cclauss) [#27381](https://github.com/nodejs/node/pull/27381)
- [[`615d386390`](https://github.com/nodejs/node/commit/615d386390)] - **child_process**: only stop readable side of stream passed to proc (Anna Henningsen) [#27373](https://github.com/nodejs/node/pull/27373)
- [[`8e876e60aa`](https://github.com/nodejs/node/commit/8e876e60aa)] - **console**: use consolePropAttributes for k-bind properties (reland) (Ruben Bridgewater) [#27352](https://github.com/nodejs/node/pull/27352)
- [[`55804e1726`](https://github.com/nodejs/node/commit/55804e1726)] - **deps**: update llhttp to 1.1.2 (Fedor Indutny) [#27513](https://github.com/nodejs/node/pull/27513)
- [[`f142363cfa`](https://github.com/nodejs/node/commit/f142363cfa)] - **deps**: update llhttp to 1.1.3 (Fedor Indutny) [#27595](https://github.com/nodejs/node/pull/27595)
- [[`5f72246499`](https://github.com/nodejs/node/commit/5f72246499)] - **deps**: add acorn stage-3 plugins (Ruben Bridgewater) [#27400](https://github.com/nodejs/node/pull/27400)
- [[`230a773e32`](https://github.com/nodejs/node/commit/230a773e32)] - **(SEMVER-MINOR)** **deps**: update archs files for OpenSSL-1.1.1b (Sam Roberts) [#27376](https://github.com/nodejs/node/pull/27376)
- [[`b68132e01a`](https://github.com/nodejs/node/commit/b68132e01a)] - **(SEMVER-MINOR)** **deps**: configure OpenSSL's SSL_trace to be built (Sam Roberts) [#27376](https://github.com/nodejs/node/pull/27376)
- [[`7c25dce7ba`](https://github.com/nodejs/node/commit/7c25dce7ba)] - **deps**: V8: cherry-pick 5d0cf6b (Joyee Cheung) [#27423](https://github.com/nodejs/node/pull/27423)
- [[`2c3c0d7d3e`](https://github.com/nodejs/node/commit/2c3c0d7d3e)] - **doc**: add cclauss to collaborators (cclauss) [#27554](https://github.com/nodejs/node/pull/27554)
- [[`b51dcf62b8`](https://github.com/nodejs/node/commit/b51dcf62b8)] - **doc**: add Electron 6 to abi_version_registry (Jeremy Apthorp) [#27288](https://github.com/nodejs/node/pull/27288)
- [[`cb97de7a9b`](https://github.com/nodejs/node/commit/cb97de7a9b)] - **doc**: move James back onto TSC (Michael Dawson) [#27411](https://github.com/nodejs/node/pull/27411)
- [[`a9748bc124`](https://github.com/nodejs/node/commit/a9748bc124)] - **doc**: describe API ERR_INVALID_PROTOCOL context (Sam Roberts) [#27393](https://github.com/nodejs/node/pull/27393)
- [[`a0353fdbe2`](https://github.com/nodejs/node/commit/a0353fdbe2)] - **fs**: align fs.ReadStream buffer pool writes to 8-byte boundary (ptaylor) [#24838](https://github.com/nodejs/node/pull/24838)
- [[`7be1e0af44`](https://github.com/nodejs/node/commit/7be1e0af44)] - **fs**: added tests for util file preprocessSymlinkDestination (Ruwan Geeganage) [#27468](https://github.com/nodejs/node/pull/27468)
- [[`f882c9b09b`](https://github.com/nodejs/node/commit/f882c9b09b)] - **(SEMVER-MINOR)** **http**: `servername === false` should disable SNI (Fedor Indutny) [#27316](https://github.com/nodejs/node/pull/27316)
- [[`de337bb37c`](https://github.com/nodejs/node/commit/de337bb37c)] - **(SEMVER-MINOR)** **inspector**: implement --cpu-prof-interval (Joyee Cheung) [#27535](https://github.com/nodejs/node/pull/27535)
- [[`9c842f4119`](https://github.com/nodejs/node/commit/9c842f4119)] - **lib**: remove Reflect.apply where appropriate (Anatoli Papirovski) [#27349](https://github.com/nodejs/node/pull/27349)
- [[`47d311b3f0`](https://github.com/nodejs/node/commit/47d311b3f0)] - **lib**: remove outdated optimizations (Weijia Wang) [#27380](https://github.com/nodejs/node/pull/27380)
- [[`c2a03d58c3`](https://github.com/nodejs/node/commit/c2a03d58c3)] - **lib**: print to stdout/stderr directly instead of using console (Joyee Cheung) [#27320](https://github.com/nodejs/node/pull/27320)
- [[`b68ecf3e17`](https://github.com/nodejs/node/commit/b68ecf3e17)] - **meta**: move andrasq to Collaborator Emeriti list (Rich Trott) [#27546](https://github.com/nodejs/node/pull/27546)
- [[`fd17f37a83`](https://github.com/nodejs/node/commit/fd17f37a83)] - **meta**: move stefanmb to Collaborator Emeriti list (Rich Trott) [#27502](https://github.com/nodejs/node/pull/27502)
- [[`8495e8bceb`](https://github.com/nodejs/node/commit/8495e8bceb)] - **meta**: move Forrest Norvell to Collaborator Emeriti list (Rich Trott) [#27437](https://github.com/nodejs/node/pull/27437)
- [[`7d1c90b614`](https://github.com/nodejs/node/commit/7d1c90b614)] - **meta**: move @vsemozhetbyt to collaborator emeriti (Vse Mozhet Byt) [#27412](https://github.com/nodejs/node/pull/27412)
- [[`014a9fd46f`](https://github.com/nodejs/node/commit/014a9fd46f)] - **module**: throw on require('./path.mjs'); (Myles Borins) [#27417](https://github.com/nodejs/node/pull/27417)
- [[`5bcd7700ca`](https://github.com/nodejs/node/commit/5bcd7700ca)] - **(SEMVER-MINOR)** **module**: add createRequire method (Myles Borins) [#27405](https://github.com/nodejs/node/pull/27405)
- [[`be9a1ec1d1`](https://github.com/nodejs/node/commit/be9a1ec1d1)] - **module**: allow passing a directory to createRequireFromPath (Gilles De Mey) [#23818](https://github.com/nodejs/node/pull/23818)
- [[`e5fdc30bd1`](https://github.com/nodejs/node/commit/e5fdc30bd1)] - **n-api**: make napi_get_property_names return strings (Anna Henningsen) [#27524](https://github.com/nodejs/node/pull/27524)
- [[`826fb66729`](https://github.com/nodejs/node/commit/826fb66729)] - **process**: compatibility patch to backport 1d022e8 (Ruben Bridgewater) [#27483](https://github.com/nodejs/node/pull/27483)
- [[`91b7f5e103`](https://github.com/nodejs/node/commit/91b7f5e103)] - **process**: improve cwd performance (Ruben Bridgewater) [#27224](https://github.com/nodejs/node/pull/27224)
- [[`05cea679a3`](https://github.com/nodejs/node/commit/05cea679a3)] - **repl**: handle stage-3 language features properly (Ruben Bridgewater) [#27400](https://github.com/nodejs/node/pull/27400)
- [[`01d632d7e8`](https://github.com/nodejs/node/commit/01d632d7e8)] - **repl**: add new language features to top level await statements (Ruben Bridgewater) [#27400](https://github.com/nodejs/node/pull/27400)
- [[`149412ca02`](https://github.com/nodejs/node/commit/149412ca02)] - **repl**: add autocomplete for filesystem modules (Anto Aravinth) [#26648](https://github.com/nodejs/node/pull/26648)
- [[`a55457c713`](https://github.com/nodejs/node/commit/a55457c713)] - **report**: use const reference in node_report.cc (gengjiawen) [#27479](https://github.com/nodejs/node/pull/27479)
- [[`8724229155`](https://github.com/nodejs/node/commit/8724229155)] - **src**: make deleted function public in node_native_module.h (gengjiawen) [#27509](https://github.com/nodejs/node/pull/27509)
- [[`1489d12735`](https://github.com/nodejs/node/commit/1489d12735)] - **src**: make deleted function public in node_main_instance.h (gengjiawen) [#27509](https://github.com/nodejs/node/pull/27509)
- [[`294d2ea71d`](https://github.com/nodejs/node/commit/294d2ea71d)] - **(SEMVER-MINOR)** **src**: refactor V8ProfilerConnection::DispatchMessage() (Joyee Cheung) [#27535](https://github.com/nodejs/node/pull/27535)
- [[`a758f9bdf5`](https://github.com/nodejs/node/commit/a758f9bdf5)] - **src**: remove node_options-inl.h from header files (Sam Roberts) [#27538](https://github.com/nodejs/node/pull/27538)
- [[`bb373d0def`](https://github.com/nodejs/node/commit/bb373d0def)] - **src**: remove unnecessary semicolons after macros (Yang Guo) [#27529](https://github.com/nodejs/node/pull/27529)
- [[`0c9bc02b96`](https://github.com/nodejs/node/commit/0c9bc02b96)] - **src**: refactor V8ProfilerConnection to be more reusable (Joyee Cheung) [#27475](https://github.com/nodejs/node/pull/27475)
- [[`c787bb85cd`](https://github.com/nodejs/node/commit/c787bb85cd)] - **src**: refactor profile initialization (Joyee Cheung) [#27475](https://github.com/nodejs/node/pull/27475)
- [[`600048b1b7`](https://github.com/nodejs/node/commit/600048b1b7)] - **src**: move Environment::context out of strong properties (Joyee Cheung) [#27430](https://github.com/nodejs/node/pull/27430)
- [[`33702913b1`](https://github.com/nodejs/node/commit/33702913b1)] - **src**: prefer v8::Global over node::Persistent (Anna Henningsen) [#27287](https://github.com/nodejs/node/pull/27287)
- [[`9d6d45e7d2`](https://github.com/nodejs/node/commit/9d6d45e7d2)] - **stream**: remove TODO and add a description instead (Ruben Bridgewater) [#27086](https://github.com/nodejs/node/pull/27086)
- [[`bb1eaeec75`](https://github.com/nodejs/node/commit/bb1eaeec75)] - **test**: mark test-tls-enable-trace-cli flaky (cjihrig) [#27559](https://github.com/nodejs/node/pull/27559)
- [[`d648ecc488`](https://github.com/nodejs/node/commit/d648ecc488)] - **test**: improve test-async-hooks-http-parser-destroy (Rich Trott) [#27319](https://github.com/nodejs/node/pull/27319)
- [[`ca720b3a55`](https://github.com/nodejs/node/commit/ca720b3a55)] - **test**: converting NghttpError to string in HTTP2 module (Ruwan Geeganage) [#27506](https://github.com/nodejs/node/pull/27506)
- [[`99e4a576eb`](https://github.com/nodejs/node/commit/99e4a576eb)] - **test**: add mustCall to openssl-client-cert-engine (Boxuan Li) [#27474](https://github.com/nodejs/node/pull/27474)
- [[`e1d88aa880`](https://github.com/nodejs/node/commit/e1d88aa880)] - **test**: document NODE_COMMON_PORT env var (cjihrig) [#27507](https://github.com/nodejs/node/pull/27507)
- [[`66cf706521`](https://github.com/nodejs/node/commit/66cf706521)] - **test**: allow EAI_FAIL in test-http-dns-error.js (cjihrig) [#27500](https://github.com/nodejs/node/pull/27500)
- [[`df4246e3b6`](https://github.com/nodejs/node/commit/df4246e3b6)] - **test**: refactor and deflake test-tls-sni-server-client (Luigi Pinca) [#27426](https://github.com/nodejs/node/pull/27426)
- [[`a278814818`](https://github.com/nodejs/node/commit/a278814818)] - **test**: make sure weak references are not GCed too early (Ruben Bridgewater) [#27482](https://github.com/nodejs/node/pull/27482)
- [[`aa281d284a`](https://github.com/nodejs/node/commit/aa281d284a)] - **test**: better output for test-report-uv-handles.js (gengjiawen) [#27479](https://github.com/nodejs/node/pull/27479)
- [[`86c27c6005`](https://github.com/nodejs/node/commit/86c27c6005)] - **test**: add mustcall in test-net-bytes-read.js (imhype) [#27471](https://github.com/nodejs/node/pull/27471)
- [[`33fead3f5e`](https://github.com/nodejs/node/commit/33fead3f5e)] - **_Revert_** "**test**: skip test-cpu-prof in debug builds with code cache" (Anna Henningsen) [#27469](https://github.com/nodejs/node/pull/27469)
- [[`a9a85d6271`](https://github.com/nodejs/node/commit/a9a85d6271)] - **test**: check `napi_get_reference_value()` during finalization (Anna Henningsen) [#27470](https://github.com/nodejs/node/pull/27470)
- [[`16af9435a0`](https://github.com/nodejs/node/commit/16af9435a0)] - **test**: remove flaky designation for test-tls-sni-option (Luigi Pinca) [#27425](https://github.com/nodejs/node/pull/27425)
- [[`1b94d025bc`](https://github.com/nodejs/node/commit/1b94d025bc)] - **test**: add missing line breaks to keep-alive header of slow headers test (Shuhei Kagawa) [#27442](https://github.com/nodejs/node/pull/27442)
- [[`fefbbd90af`](https://github.com/nodejs/node/commit/fefbbd90af)] - **test**: add tests for new language features (Ruben Bridgewater) [#27400](https://github.com/nodejs/node/pull/27400)
- [[`3711684ccf`](https://github.com/nodejs/node/commit/3711684ccf)] - **test**: add mustCall for parallel/test-net-connect-paused-connection (sujunfei) [#27463](https://github.com/nodejs/node/pull/27463)
- [[`0e4f8788eb`](https://github.com/nodejs/node/commit/0e4f8788eb)] - **test**: add mustCallAtLeast to test-fs-read-stream-resume.js (heben) [#27456](https://github.com/nodejs/node/pull/27456)
- [[`e89b6fee3a`](https://github.com/nodejs/node/commit/e89b6fee3a)] - **test**: adding mustCall in test-fs-readfile-empty.js (陈健) [#27455](https://github.com/nodejs/node/pull/27455)
- [[`457549b67d`](https://github.com/nodejs/node/commit/457549b67d)] - **test**: add common.mustCall in test-http-abort-client.js (OneNail) [#27449](https://github.com/nodejs/node/pull/27449)
- [[`f4124d5ba5`](https://github.com/nodejs/node/commit/f4124d5ba5)] - **test**: add mustCall to http-abort-queued test (Yaphet Ye) [#27447](https://github.com/nodejs/node/pull/27447)
- [[`e21f035666`](https://github.com/nodejs/node/commit/e21f035666)] - **test**: add mustCall in test-fs-readfilesync-pipe-large.js (sinoon) [#27458](https://github.com/nodejs/node/pull/27458)
- [[`1dd0205f10`](https://github.com/nodejs/node/commit/1dd0205f10)] - **test**: add mustCall to test-dgram-connect-send-multi-buffer-copy.js (XGHeaven) [#27465](https://github.com/nodejs/node/pull/27465)
- [[`0dfe5bebb2`](https://github.com/nodejs/node/commit/0dfe5bebb2)] - **test**: add test of policy about parse error (Daiki Ihara) [#26873](https://github.com/nodejs/node/pull/26873)
- [[`eeab007b25`](https://github.com/nodejs/node/commit/eeab007b25)] - **test**: add mustCall to test-net-after-close test (xuqinggang) [#27459](https://github.com/nodejs/node/pull/27459)
- [[`c1b04652f5`](https://github.com/nodejs/node/commit/c1b04652f5)] - **test**: add "mustCall" to test-fs-readfile-unlink (wuchenkai) [#27453](https://github.com/nodejs/node/pull/27453)
- [[`b6c65c1351`](https://github.com/nodejs/node/commit/b6c65c1351)] - **test**: add missing ToC entries (cjihrig) [#27434](https://github.com/nodejs/node/pull/27434)
- [[`66bff5071f`](https://github.com/nodejs/node/commit/66bff5071f)] - **test**: document report helper module (cjihrig) [#27434](https://github.com/nodejs/node/pull/27434)
- [[`2c335928cd`](https://github.com/nodejs/node/commit/2c335928cd)] - **test**: document NODE_SKIP_FLAG_CHECK (cjihrig) [#27434](https://github.com/nodejs/node/pull/27434)
- [[`115d06cdbb`](https://github.com/nodejs/node/commit/115d06cdbb)] - **test**: document NODE_TEST_KNOWN_GLOBALS (cjihrig) [#27434](https://github.com/nodejs/node/pull/27434)
- [[`51fc672da9`](https://github.com/nodejs/node/commit/51fc672da9)] - **test**: add mustCallAtLeast to test-fs-read-stream-inherit (nilianzhu) [#27457](https://github.com/nodejs/node/pull/27457)
- [[`4b9d109518`](https://github.com/nodejs/node/commit/4b9d109518)] - **test**: add mustCall to test-dgram-implicit-bind.js (Chenxi Yuan) [#27452](https://github.com/nodejs/node/pull/27452)
- [[`c4d67f2af5`](https://github.com/nodejs/node/commit/c4d67f2af5)] - **test**: add common.mustCall test-dgram-listen-after-bind (zhoujiamin) [#27454](https://github.com/nodejs/node/pull/27454)
- [[`23fb430e03`](https://github.com/nodejs/node/commit/23fb430e03)] - **test**: add mustCall to test-dgram-connect-send-callback-buffer (shenchen) [#27466](https://github.com/nodejs/node/pull/27466)
- [[`a37ca245ff`](https://github.com/nodejs/node/commit/a37ca245ff)] - **test**: add mustCallAtLeast to test-fs-read-stream-fd test (hardfist) [#27461](https://github.com/nodejs/node/pull/27461)
- [[`cf84f20453`](https://github.com/nodejs/node/commit/cf84f20453)] - **test**: skip fs-copyfile-respect-permission if root (Daniel Bevenius) [#27378](https://github.com/nodejs/node/pull/27378)
- [[`7d80999454`](https://github.com/nodejs/node/commit/7d80999454)] - **test**: add mustCall to net-can-reset-timeout (xinyulee) [#27462](https://github.com/nodejs/node/pull/27462)
- [[`9fa5ba8b3c`](https://github.com/nodejs/node/commit/9fa5ba8b3c)] - **test**: add mustCall to test-fs-readfile-pipe-large (luoyu) [#27460](https://github.com/nodejs/node/pull/27460)
- [[`e8d5b6226a`](https://github.com/nodejs/node/commit/e8d5b6226a)] - **test**: add "mustCall" for test-net-buffersize (lixin.atom) [#27451](https://github.com/nodejs/node/pull/27451)
- [[`d784ecb1ad`](https://github.com/nodejs/node/commit/d784ecb1ad)] - **test**: add mustCall to test-net-eaddrinuse test (tongshouyu) [#27448](https://github.com/nodejs/node/pull/27448)
- [[`6fd1384a43`](https://github.com/nodejs/node/commit/6fd1384a43)] - **test**: add mustcall in test-dgram-connect-send-callback-buffer-length (jyjunyz) [#27464](https://github.com/nodejs/node/pull/27464)
- [[`7a35077197`](https://github.com/nodejs/node/commit/7a35077197)] - **test**: add mustCall to test-fs-readfile-pipe (tonyhty) [#27450](https://github.com/nodejs/node/pull/27450)
- [[`af29ae0344`](https://github.com/nodejs/node/commit/af29ae0344)] - **test**: add mustCall to net-connect-buffer test (Rongjian Zhang) [#27446](https://github.com/nodejs/node/pull/27446)
- [[`bdabf699eb`](https://github.com/nodejs/node/commit/bdabf699eb)] - **(SEMVER-MINOR)** **tls**: add --tls-min-v1.2 CLI switch (Sam Roberts) [#27520](https://github.com/nodejs/node/pull/27520)
- [[`7bbf951095`](https://github.com/nodejs/node/commit/7bbf951095)] - **tls**: disallow conflicting TLS protocol options (Sam Roberts) [#27521](https://github.com/nodejs/node/pull/27521)
- [[`84a2768c25`](https://github.com/nodejs/node/commit/84a2768c25)] - **(SEMVER-MINOR)** **tls**: support enableTrace in TLSSocket() (cjihrig) [#27497](https://github.com/nodejs/node/pull/27497)
- [[`576fe339a1`](https://github.com/nodejs/node/commit/576fe339a1)] - **(SEMVER-MINOR)** **tls**: simplify enableTrace logic (cjihrig) [#27497](https://github.com/nodejs/node/pull/27497)
- [[`30a72e8c7b`](https://github.com/nodejs/node/commit/30a72e8c7b)] - **(SEMVER-MINOR)** **tls**: allow enabling the TLS debug trace (Sam Roberts) [#27376](https://github.com/nodejs/node/pull/27376)
- [[`f1efe6dae0`](https://github.com/nodejs/node/commit/f1efe6dae0)] - **(SEMVER-MINOR)** **tls,cli**: add --trace-tls command-line flag (cjihrig) [#27497](https://github.com/nodejs/node/pull/27497)
- [[`3d37414002`](https://github.com/nodejs/node/commit/3d37414002)] - **tools**: fix node-core/required-modules eslint rule (Ben Noordhuis) [#27545](https://github.com/nodejs/node/pull/27545)
- [[`29e2793a87`](https://github.com/nodejs/node/commit/29e2793a87)] - **tools**: add Release and Debug symlinks to .gitignore (Gerhard Stoebich) [#27484](https://github.com/nodejs/node/pull/27484)
- [[`76af4f0d05`](https://github.com/nodejs/node/commit/76af4f0d05)] - **tools**: prohibit `assert.doesNotReject()` in Node.js core (Ruben Bridgewater) [#27402](https://github.com/nodejs/node/pull/27402)
- [[`95498df1cf`](https://github.com/nodejs/node/commit/95498df1cf)] - **util**: inspect constructor closer (Ruben Bridgewater) [#27522](https://github.com/nodejs/node/pull/27522)
- [[`7b5bd93ced`](https://github.com/nodejs/node/commit/7b5bd93ced)] - **util**: compatibility patch to backport d0667e8 (Ruben Bridgewater) [#27570](https://github.com/nodejs/node/pull/27570)
- [[`52d4f1febf`](https://github.com/nodejs/node/commit/52d4f1febf)] - **util**: improve function inspection (Ruben Bridgewater) [#27227](https://github.com/nodejs/node/pull/27227)
- [[`caab7d4664`](https://github.com/nodejs/node/commit/caab7d4664)] - **util**: better number formatters (Ruben Bridgewater) [#27499](https://github.com/nodejs/node/pull/27499)

Windows 32-bit Installer: https://nodejs.org/dist/v12.2.0/node-v12.2.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v12.2.0/node-v12.2.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v12.2.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v12.2.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v12.2.0/node-v12.2.0.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v12.2.0/node-v12.2.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v12.2.0/node-v12.2.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v12.2.0/node-v12.2.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v12.2.0/node-v12.2.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v12.2.0/node-v12.2.0-aix-ppc64.tar.gz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v12.2.0/node-v12.2.0-sunos-x64.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v12.2.0/node-v12.2.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v12.2.0/node-v12.2.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v12.2.0/node-v12.2.0.tar.gz \
Other release files: https://nodejs.org/dist/v12.2.0/ \
Documentation: https://nodejs.org/docs/v12.2.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

94533317caae6c62ba0d139641e3d873cd678cc9a1105c96f89c423fdea46b76  node-v12.2.0-aix-ppc64.tar.gz
c72ae8a2b989138c6e6e9b393812502df8c28546a016cf24e7a82dd27e3838af  node-v12.2.0-darwin-x64.tar.gz
58fc2de3b6c414a7c446b9e27db5d8bc6bb0763fe262d389a1e748812c7ed97c  node-v12.2.0-darwin-x64.tar.xz
6e1e8c4b72602308a5c7b193a2efcdd2844cca4b4db48c421b6ae5bb7e6e76d1  node-v12.2.0-headers.tar.gz
7f646cee03427a17a7f3a5c68655d2ca288987fae58ec04e1c51f011f38e9f30  node-v12.2.0-headers.tar.xz
abc9adedbbbd48f46163399c0f7a7948c14df184cb500b925c6980c921988d13  node-v12.2.0-linux-arm64.tar.gz
42e4a5c6ad0b402e281daf46a27a269249bdb8b4e31ff8869701aaddac2cc8c4  node-v12.2.0-linux-arm64.tar.xz
a8345403a7daf60a6119afa24d61cedae3fab8e80f34dc8754a826e710fe3b44  node-v12.2.0-linux-armv7l.tar.gz
271462ffabec18aacc575961c7d7970ce2c0c7e07a5bb52c99b0ec2588474e1f  node-v12.2.0-linux-armv7l.tar.xz
5100db086cf075599a66056ff08ec555b41fd0510cfbf8c40340a9380671fbbe  node-v12.2.0-linux-ppc64le.tar.gz
2e56fecce454a93a13fe49f7130869d473a029177d1c2dcf5667677ff03040ac  node-v12.2.0-linux-ppc64le.tar.xz
e7989a9ca7da772742f421fe3cd6045629543ab23b9bfebae17f3bb924dfc1ff  node-v12.2.0-linux-s390x.tar.gz
1cdab471441cfaca2f58c2349d85ba376cac6878edfbe3ece0586342c47f5d20  node-v12.2.0-linux-s390x.tar.xz
ba6afb9967ea6934d0807e0f79da80e063601d91c98da12bda3cf4675720bfb2  node-v12.2.0-linux-x64.tar.gz
89059969861606e2a435ff2619c4df6f41c040120e507d9c4f03374353357307  node-v12.2.0-linux-x64.tar.xz
80a45502c1a5810f6ae87935b2a9f1612d4cd76cdccddc490dca6d0cbdfa8c3d  node-v12.2.0.pkg
d843ca0a05c63f59c335450097015257cb99654fa2ec8763135731633ca614a1  node-v12.2.0-sunos-x64.tar.gz
bbe5e463b90fd8f44ad06d5894124ea88181947b291d6cb4bbb492fcfacb97d3  node-v12.2.0-sunos-x64.tar.xz
55f935d35ee201f47fb3b79fdb249dae0ba25f3e98190cfc548a9e3d30c90979  node-v12.2.0.tar.gz
c5d19744fa506ae1e4bc963dcf760267c6ff2193ecd0f3be56acb69db1477983  node-v12.2.0.tar.xz
cb65032a92702fd3506f860888c74ca77fb70d60732d1f7458528cc3b44c28c9  node-v12.2.0-win-x64.7z
c1e7fb3c1c15d8f2ab5c1db9c9662097f9c682164b3f7397955ccce946442c97  node-v12.2.0-win-x64.zip
59eaa4659267c5aef27e870d8fb7af7b4fde17e755fad747d7eb5c781a858e7b  node-v12.2.0-win-x86.7z
0f9192f071777e000e18e5254b19473e00bee9ba4f7c2c8460c8bcf4866d3925  node-v12.2.0-win-x86.zip
6aaa1fb3015572af480091c06ecb25f77a6535268a05dd2362b24c9338420cbb  node-v12.2.0-x64.msi
e6a329727b363c6f9ce295957bcf62ea69925ac517dedc8e9d1333466c169408  node-v12.2.0-x86.msi
d7eb6406181d4ea3d32f22c0665b85a9593a0f215e61ca61903d2f493202aed8  win-x64/node.exe
76876e9a1a292e511c7f4a9f842d3d0a4059391c6a7460d53061beecc0a7340b  win-x64/node.lib
4f8ac717d8228b81d09ab3349cf02333bdc4fd0941507bfcf082a5c0f3df9584  win-x64/node_pdb.7z
e4ef3364dcf762d9fd118d65778487888fce0a1000951ff0209a3d42245d9480  win-x64/node_pdb.zip
7222b07bb7396fb60be5a0eb9401bb7e00b4577cd1bc50253cfeddbb6c20d47d  win-x86/node.exe
80e632bbfeaadbfa8aa630ee9a1f56719ffad468cb4b0aad31b3c9a19f0d0511  win-x86/node.lib
1a33e3a99dc5bc19b8482b38f7e6d2e9fff4e1578058184be5f0232373e23a93  win-x86/node_pdb.7z
4edaffecfc9bb2c82718cbd9731978aa455f8d6a75a62502f24251045c23a9dd  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEj8yhP+8dDC6RAI4Jdw96mlrhVgAFAlzR0PUACgkQdw96mlrh
VgCXYw//S5Du8jkBoai4rGegryZ7nshBm4aM2iR3ksipl/O/+aPjZMX0qtpNr9XI
TlnkxyEwdf+mjXzJZ6kXkx4T1mH5umULomu5Q7qoEzFSqXnxbvfnjzNZXDzSi5s8
ZOBtuGzxQrWfrxA5EOU5ViIw5knwoSyftT+O5TqgYGC+pnbFVoIAK/lRQ/tXrAdD
rpiMp6NOhU/VI2uVokWMnbhNbM5mmioiyjMsskAG52YMU74mYMT7nKJ39aQfDr7y
f664SeGzUCBxbYHUzjb6Il6Qyn+4GWnrr1Zw+fbG67vOV/hLNkTFf6WiHahCCAto
VZfatXTJPcjL1Psv1mGs+YrqtmSngob4TBpOh6Oq4mrxRTz+Wpi11+SC9y4zmvPg
d8VFrXo4urXd86oD4pAGSqmdoz8qJ3oRmMVzjgWBjaUQOZF+mt9+nH29p0DyDW6d
Uhl/5SUZd+So84209R9yoZ0luPx8RYpVPJVqXtUEt8u3bxqKuNZZ3krJ24/3lKYk
SLpMzzmWjSXm25OCVmeYxNcRYNQkyfQ5TshqyRbvvwZMgU6O/AApJIhjYkVesWPr
GIyG+yx6OIUsbZoZu6CPMhpmml7VZEtQPrNrs7cffy6Y1w22V/h6jT1k8OajyqA8
AOZut3Nsv0yQ7zzc9me+8uOFLrS7A29mFT6wuXsrGHkpls/g/gA=
=UY1y
-----END PGP SIGNATURE-----

```
