---
date: '2016-06-23T23:31:02.257Z'
category: release
title: Node v5.12.0 (Stable)
layout: blog-post
author: <PERSON>
---

### Notable changes

This is a security release. All Node.js users should consult the security release summary at /blog/vulnerability/june-2016-security-releases for details on patched vulnerabilities.

- **buffer**
  - backport allocUnsafeSlow (Сковорода Никита Андреевич) [#7169](https://github.com/nodejs/node/pull/7169)
  - ignore negative allocation lengths (<PERSON>) [#7221](https://github.com/nodejs/node/pull/7221)
- **deps**: backport 3a9bfec from v8 upstream (<PERSON>) [nodejs/node-private#40](https://github.com/nodejs/node-private/pull/40)
  - Fixes a Buffer overflow vulnerability discovered in v8. More details can be found in the CVE (CVE-2016-1699).

### Commits

- [[`0ca0827b71`](https://github.com/nodejs/node/commit/0ca0827b71)] - **(SEMVER-MINOR)** **buffer**: backport allocUnsafeSlow (Сковорода Никита Андреевич) [#7169](https://github.com/nodejs/node/pull/7169)
- [[`27785aeb37`](https://github.com/nodejs/node/commit/27785aeb37)] - **buffer**: ignore negative allocation lengths (Anna Henningsen) [#7221](https://github.com/nodejs/node/pull/7221)
- [[`34b96c1322`](https://github.com/nodejs/node/commit/34b96c1322)] - **deps**: backport 3a9bfec from v8 upstream (Ben Noordhuis) [nodejs/node-private#40](https://github.com/nodejs/node-private/pull/40)
- [[`2ebeb82852`](https://github.com/nodejs/node/commit/2ebeb82852)] - **test**: fix test-net-\* error code check for getaddrinfo(3) (Natanael Copa) [#5099](https://github.com/nodejs/node/pull/5099)
- [[`03d36aea4f`](https://github.com/nodejs/node/commit/03d36aea4f)] - **(SEMVER-MINOR)** **test**: add buffer testcase for resetting kZeroFill (Сковорода Никита Андреевич) [#7169](https://github.com/nodejs/node/pull/7169)

Windows 32-bit Installer: https://nodejs.org/dist/v5.12.0/node-v5.12.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v5.12.0/node-v5.12.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v5.12.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v5.12.0/win-x64/node.exe \
Mac OS X 64-bit Installer: https://nodejs.org/dist/v5.12.0/node-v5.12.0.pkg \
Mac OS X 64-bit Binary: https://nodejs.org/dist/v5.12.0/node-v5.12.0-darwin-x64.tar.gz \
Linux 32-bit Binary: https://nodejs.org/dist/v5.12.0/node-v5.12.0-linux-x86.tar.xz \
Linux 64-bit Binary: https://nodejs.org/dist/v5.12.0/node-v5.12.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v5.12.0/node-v5.12.0-linux-ppc64le.tar.xz \
SmartOS 32-bit Binary: https://nodejs.org/dist/v5.12.0/node-v5.12.0-sunos-x86.tar.xz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v5.12.0/node-v5.12.0-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v5.12.0/node-v5.12.0-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v5.12.0/node-v5.12.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v5.12.0/node-v5.12.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v5.12.0/node-v5.12.0.tar.gz \
Other release files: https://nodejs.org/dist/v5.12.0/ \
Documentation: https://nodejs.org/docs/v5.12.0/api/

Shasums (GPG signing hash: SHA512, file hash: SHA256):

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA512

bcbfb16896d4b13e08184343420ab00822e9ef09a72f9dbc41ef0cfcc84b99c2  node-v5.12.0-darwin-x64.tar.gz
2778a6344e5285af6412c66ef967cd3da2edb3262f19c06e484a062191f43019  node-v5.12.0-darwin-x64.tar.xz
a36e815ac5a4d7d3f486deda97359ae8e01d42259bf4a2be4fbbf41efd585c70  node-v5.12.0-headers.tar.gz
27deab94f424f38202a6ea8e945a542da71fe8f0808d69899ba4a855293bbb92  node-v5.12.0-headers.tar.xz
db02351d2c205a3c60218f937a41a8b8d665f326e7dfa263954ab39f8a8a2bc3  node-v5.12.0-linux-arm64.tar.gz
3eeaed6ce895c551903164966f950533c200ac24f68bdaebaab0e29e9923dc6d  node-v5.12.0-linux-arm64.tar.xz
f58b9db77eb82830157f814704e8c3b3ba3420079a8ded3ad39302a33e3a30af  node-v5.12.0-linux-armv6l.tar.gz
88a102d0d2614b088987759089b9d4351e9941a2e7095350f352a5eb4eafd0c6  node-v5.12.0-linux-armv6l.tar.xz
da51a7025772766453f330b3274a12381995e07d68c99c55357767171af4c538  node-v5.12.0-linux-armv7l.tar.gz
6ac1d3ec0cd5cb797cb641bfd170bfb19ffe96967fc90ed166c710e8a0841b5f  node-v5.12.0-linux-armv7l.tar.xz
5cde9c115aade3d3eac494106ad29a2e0743a19516aa8a4ed531783292c7641f  node-v5.12.0-linux-ppc64le.tar.gz
0413665e0d3fca6c58fec9301485bcbbc6f0336d64c0a591121538a7f969bab6  node-v5.12.0-linux-ppc64le.tar.xz
a57642f7aa59f7a5248fd5368217f7c7218ae889664ab8974af468797bbfa7bb  node-v5.12.0-linux-ppc64.tar.gz
f9b252f201b29b10c7d774db79eddeae782a5b8d1478e0ed5146036d4dbe5bb2  node-v5.12.0-linux-ppc64.tar.xz
c0f459152aa87aba8a019a95899352170db0d8d52c860715c88356cb253fe2c4  node-v5.12.0-linux-x64.tar.gz
619fc1d14ee0c92894cf01bf1a47f5bae321ac04dfeb4cea6a2b6ce65832df79  node-v5.12.0-linux-x64.tar.xz
1ee1ef4e9f8bfb2976f35ca10658f3828cae10ef462d3fee7c3f159a3e21365b  node-v5.12.0-linux-x86.tar.gz
6a83c52fa00c72cbc90a94dc392636fa4a5f1543311a4838c6ad87f703d67200  node-v5.12.0-linux-x86.tar.xz
8835e19bff36fe2a0120e8b189aa33ea87e99eb8b08605212a5ddb8fb63bcaf2  node-v5.12.0.pkg
e5738517163e9409457ff2fe434aa410b70064ba8639a38173decc430a87c546  node-v5.12.0-sunos-x64.tar.gz
d3f72ebebb31c081b794c09941184e4744852552b1b32e4121cdc129c41d6a58  node-v5.12.0-sunos-x64.tar.xz
b3831faef7112f4bd71dd4dff3f2296c8ee1f4f8e7b3adfdd205caa91adde198  node-v5.12.0-sunos-x86.tar.gz
ddd41de09925860e383ed55d0251e5040ff5637c6504c9a6891eb167d3ff74db  node-v5.12.0-sunos-x86.tar.xz
250c12a561d7319e71e142ee92ab682494c7823d81ce24703c80eb52bdf9ba42  node-v5.12.0.tar.gz
4f926373f11f2a25156eee1804ec012eb912c42e5d34fc2909889da22efdadfe  node-v5.12.0.tar.xz
afc6be86edc989c40a7019dfce370819448a2b43c1a641d12ee9efc6391a23a8  node-v5.12.0-x64.msi
b3fc857e4925418ec43063606ce01134f91a20be8c7b6a0dbf0a9093636b40f9  node-v5.12.0-x86.msi
cf27938a7fb5c983eb46cf9f27190016832d254ed96227d7720af299d2d75f1e  win-x64/node.exe
2938e21edafdb5bfaabc7ea14b34c77b9d8af4a7a5d389178ca902c06f5a3d6b  win-x64/node.lib
92c22479607ab269eae7c6040cb059757fd0a29ddc188d3a25e46ee485e6e221  win-x86/node.exe
aebf98d723036e2a3c458baf21a93eb45f1a42da794fc29dc47d01b5c70dae3d  win-x86/node.lib
-----BEGIN PGP SIGNATURE-----
Comment: GPGTools - https://gpgtools.org

iQIcBAEBCgAGBQJXbG57AAoJELY7U1pMIGypxHIQAKJnkwx/SDOJGquRw1Wl4V9W
A6NzAzRDfCE2NYeglWMqinTjSFJkeYP98z4x63cE9uctxJns23z+PfPkRHZDbsSB
Y/mHAm5ntbk8cndISgBiK55QiFaYzVFaw3A1XoIozTGR4irbPbaj28iUbY4T8q2+
O3GfcDKD+SXS1AtTZ+P2GE6rxE0qkBYHMxM7b7lD5VXnzGr4Cyg4jSaD7w6PK2yG
u986AW9EJjKigZtvy3nkO27+ZNx3jDNskWBpT6YjhAKSs18/qTGHgOL2Jtk6rdfv
Vd/sumfIIlDUFXT/g01/leYbOHyIMmQYASm5XBA5UBoQuuapRvCVAe0SuRKe/9wV
bD/DfDORzdJUuebvdxx9K+iMbFGD2ADKmFAomjxyTynZup0VrmeSUQtbpGucp0X/
16Li8NDBD4WRXHYjcqXujJ4+cs/DZ6DUuqloCcB4cigs4sAyuqPVnZu5HTfBMzox
T1HWbDh2hE6HqsXcYbCM1G7yXLH7VTGaJJifZNP1AL2C24Xa8zWbhOAcJ4JB1Z8J
NQ2nW68AnXMh54PJhR/aBQLj3BC16i3RbWc8bhu85SpTVAO4y8W6i14FALJapXNz
6tEa/LTc9aH5RiugUfZzWBBdl/FfdeKyRofaN7PbJwTqo7XCJxZ11wbYwYR35Edo
4nsbRK+UN1gwWqSMvLXz
=H+Rd
-----END PGP SIGNATURE-----

```
