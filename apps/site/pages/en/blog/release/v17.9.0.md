---
date: '2022-04-07T22:57:27.899Z'
category: release
title: Node v17.9.0 (Current)
layout: blog-post
author: <PERSON>
---

### Notable Changes

- \[[`7124f91cbf`](https://github.com/nodejs/node/commit/7124f91cbf)] - **(SEMVER-MINOR)** **crypto**: make authTagLength optional for CC20P1305 (<PERSON>) [#42427](https://github.com/nodejs/node/pull/42427)
- \[[`30dc6dd3fb`](https://github.com/nodejs/node/commit/30dc6dd3fb)] - **deps**: update undici to 4.16.0 (Node.js GitHub Bot) [#42414](https://github.com/nodejs/node/pull/42414)
- \[[`f0fc2744a5`](https://github.com/nodejs/node/commit/f0fc2744a5)] - **doc**: add @meixg to collaborators (<PERSON><PERSON><PERSON>) [#42576](https://github.com/nodejs/node/pull/42576)

### Commits

- \[[`bb71433334`](https://github.com/nodejs/node/commit/bb71433334)] - **async_hooks**: remove destroyed symbol on Promises (Gerhard Stöbich) [#42402](https://github.com/nodejs/node/pull/42402)
- \[[`b48a6cb3f9`](https://github.com/nodejs/node/commit/b48a6cb3f9)] - **bootstrap**: reset process.\_exit and process.exitCode in pre-execution (Joyee Cheung) [#42466](https://github.com/nodejs/node/pull/42466)
- \[[`b89f038537`](https://github.com/nodejs/node/commit/b89f038537)] - **bootstrap**: run inspector and event loop in snapshot builder (Joyee Cheung) [#42466](https://github.com/nodejs/node/pull/42466)
- \[[`177558600e`](https://github.com/nodejs/node/commit/177558600e)] - **bootstrap**: make I/O streams work with user-land snapshot (Joyee Cheung) [#42466](https://github.com/nodejs/node/pull/42466)
- \[[`e3683cb34d`](https://github.com/nodejs/node/commit/e3683cb34d)] - **bootstrap**: refresh options in pre-execution (Joyee Cheung) [#42466](https://github.com/nodejs/node/pull/42466)
- \[[`d302d2f0d2`](https://github.com/nodejs/node/commit/d302d2f0d2)] - **bootstrap**: use SnapshotData to pass snapshot data around (Joyee Cheung) [#42360](https://github.com/nodejs/node/pull/42360)
- \[[`eb3dfc00f0`](https://github.com/nodejs/node/commit/eb3dfc00f0)] - **buffer**: improve Blob constructor error message when passing a string (Xuguang Mei) [#42338](https://github.com/nodejs/node/pull/42338)
- \[[`f45d5537c1`](https://github.com/nodejs/node/commit/f45d5537c1)] - **buffer**: fix `atob` input validation (Antoine du Hamel) [#42539](https://github.com/nodejs/node/pull/42539)
- \[[`fb6a5ba8d7`](https://github.com/nodejs/node/commit/fb6a5ba8d7)] - **build**: remove precompiled header and debug information for host builds (Niyas Sait) [#42538](https://github.com/nodejs/node/pull/42538)
- \[[`1f7d2e800c`](https://github.com/nodejs/node/commit/1f7d2e800c)] - **build**: windows/arm64 native compilation support (Niyas Sait) [#42408](https://github.com/nodejs/node/pull/42408)
- \[[`d9a1d7866c`](https://github.com/nodejs/node/commit/d9a1d7866c)] - **build**: consolidate JS and md linting GitHub Actions (Rich Trott) [#42572](https://github.com/nodejs/node/pull/42572)
- \[[`ecb5be845d`](https://github.com/nodejs/node/commit/ecb5be845d)] - **build**: set stale action back to running nightly (Michael Dawson) [#42549](https://github.com/nodejs/node/pull/42549)
- \[[`f9fb7f6d96`](https://github.com/nodejs/node/commit/f9fb7f6d96)] - **build**: add --node-snapshot-main configure option (Joyee Cheung) [#42466](https://github.com/nodejs/node/pull/42466)
- \[[`c6808f088b`](https://github.com/nodejs/node/commit/c6808f088b)] - **build**: bump actions/checkout (Eliaz Bobadilla) [#42460](https://github.com/nodejs/node/pull/42460)
- \[[`9a54acb7c6`](https://github.com/nodejs/node/commit/9a54acb7c6)] - **child_process**: add env contents types in JSDoc (Rich Trott) [#42494](https://github.com/nodejs/node/pull/42494)
- \[[`a2f07380ea`](https://github.com/nodejs/node/commit/a2f07380ea)] - **crypto**: do not add undefined hash in webcrypto normalizeAlgorithm (Filip Skokan) [#42559](https://github.com/nodejs/node/pull/42559)
- \[[`9b4bd7d031`](https://github.com/nodejs/node/commit/9b4bd7d031)] - **crypto**: cleanup webcrypto jwk code (Filip Skokan) [#42562](https://github.com/nodejs/node/pull/42562)
- \[[`541a1328b0`](https://github.com/nodejs/node/commit/541a1328b0)] - **crypto**: fix webcrypto derive key lengths (Filip Skokan) [#42542](https://github.com/nodejs/node/pull/42542)
- \[[`7124f91cbf`](https://github.com/nodejs/node/commit/7124f91cbf)] - **(SEMVER-MINOR)** **crypto**: make authTagLength optional for CC20P1305 (Tobias Nießen) [#42427](https://github.com/nodejs/node/pull/42427)
- \[[`30dc6dd3fb`](https://github.com/nodejs/node/commit/30dc6dd3fb)] - **deps**: update undici to 4.16.0 (Node.js GitHub Bot) [#42414](https://github.com/nodejs/node/pull/42414)
- \[[`6e56924274`](https://github.com/nodejs/node/commit/6e56924274)] - **doc**: simplify Http2Stream encoding text (Rich Trott) [#42597](https://github.com/nodejs/node/pull/42597)
- \[[`261672b1da`](https://github.com/nodejs/node/commit/261672b1da)] - **doc**: remove obsolete stream API selection text (Rich Trott) [#42586](https://github.com/nodejs/node/pull/42586)
- \[[`beffed1880`](https://github.com/nodejs/node/commit/beffed1880)] - **doc**: remove faulty justification for 128-bit AES (Tobias Nießen) [#42578](https://github.com/nodejs/node/pull/42578)
- \[[`71f4a39086`](https://github.com/nodejs/node/commit/71f4a39086)] - **doc**: fix documentation of `FileHandle.prototype.appendFile` (Antoine du Hamel) [#42588](https://github.com/nodejs/node/pull/42588)
- \[[`c83ea22f7c`](https://github.com/nodejs/node/commit/c83ea22f7c)] - **doc**: change "OCSP Request" to "OCSP request" (Tobias Nießen) [#42582](https://github.com/nodejs/node/pull/42582)
- \[[`71ab0dea35`](https://github.com/nodejs/node/commit/71ab0dea35)] - **doc**: aes webcrypto unwrap is not a node-specific extensions (Filip Skokan) [#42561](https://github.com/nodejs/node/pull/42561)
- \[[`1c614184da`](https://github.com/nodejs/node/commit/1c614184da)] - **doc**: simplify recommendations in process.md (Rich Trott) [#42556](https://github.com/nodejs/node/pull/42556)
- \[[`c036800ddc`](https://github.com/nodejs/node/commit/c036800ddc)] - **doc**: clarify recommendations in stream.md (Rich Trott) [#42555](https://github.com/nodejs/node/pull/42555)
- \[[`dcf0abf8c7`](https://github.com/nodejs/node/commit/dcf0abf8c7)] - **doc**: simplify recommendation in webcrypto.md (Rich Trott) [#42554](https://github.com/nodejs/node/pull/42554)
- \[[`8333fa063b`](https://github.com/nodejs/node/commit/8333fa063b)] - **doc**: update DEP0102 text (Rich Trott) [#42553](https://github.com/nodejs/node/pull/42553)
- \[[`8b08bff682`](https://github.com/nodejs/node/commit/8b08bff682)] - **doc**: remove util.promisify() content in readline.md (Rich Trott) [#42552](https://github.com/nodejs/node/pull/42552)
- \[[`94492424ba`](https://github.com/nodejs/node/commit/94492424ba)] - **doc**: add introduction sentence for CJS (Antoine du Hamel) [#42491](https://github.com/nodejs/node/pull/42491)
- \[[`f0fc2744a5`](https://github.com/nodejs/node/commit/f0fc2744a5)] - **doc**: add @meixg to collaborators (Xuguang Mei) [#42576](https://github.com/nodejs/node/pull/42576)
- \[[`d935fef594`](https://github.com/nodejs/node/commit/d935fef594)] - **doc**: consolidate CI sections (Rich Trott) [#42534](https://github.com/nodejs/node/pull/42534)
- \[[`fd45df314b`](https://github.com/nodejs/node/commit/fd45df314b)] - **doc**: document breaking change in `http.IncomingMessage` `'close'` event (Paolo Insogna) [#42521](https://github.com/nodejs/node/pull/42521)
- \[[`53584fa750`](https://github.com/nodejs/node/commit/53584fa750)] - **doc**: remove extraneous comma (Rich Trott) [#42548](https://github.com/nodejs/node/pull/42548)
- \[[`b819af6509`](https://github.com/nodejs/node/commit/b819af6509)] - **doc**: guide towards x509.fingerprint256 (Tobias Nießen) [#42516](https://github.com/nodejs/node/pull/42516)
- \[[`f2355e41ed`](https://github.com/nodejs/node/commit/f2355e41ed)] - **doc**: fix internal link in collaborator-guide.md (Daeyeon Jeong) [#42551](https://github.com/nodejs/node/pull/42551)
- \[[`ffc6776996`](https://github.com/nodejs/node/commit/ffc6776996)] - **doc**: add suggestion for OpenSSL only sec releases (Michael Dawson) [#42456](https://github.com/nodejs/node/pull/42456)
- \[[`1454c0297d`](https://github.com/nodejs/node/commit/1454c0297d)] - **doc**: fix comment text in async_hooks example (Rich Trott) [#42499](https://github.com/nodejs/node/pull/42499)
- \[[`b9ab9867f4`](https://github.com/nodejs/node/commit/b9ab9867f4)] - **doc**: add `stability` class to legacy status description (Daniel Roe) [#42525](https://github.com/nodejs/node/pull/42525)
- \[[`6c13988d53`](https://github.com/nodejs/node/commit/6c13988d53)] - **doc**: suggest checkHost in checkServerIdentity docs (Tobias Nießen) [#42495](https://github.com/nodejs/node/pull/42495)
- \[[`28665a9dd6`](https://github.com/nodejs/node/commit/28665a9dd6)] - **doc**: update security release onboarding (Joe Sepi) [#42333](https://github.com/nodejs/node/pull/42333)
- \[[`d335addf0c`](https://github.com/nodejs/node/commit/d335addf0c)] - **doc**: fix question promise API example (Xuguang Mei) [#42465](https://github.com/nodejs/node/pull/42465)
- \[[`7cf9febcb4`](https://github.com/nodejs/node/commit/7cf9febcb4)] - **doc**: remove comma splice in events.md (Rich Trott) [#42484](https://github.com/nodejs/node/pull/42484)
- \[[`3c3684d9f1`](https://github.com/nodejs/node/commit/3c3684d9f1)] - **doc**: clarify napi_finalize behavior (Alba Mendez) [#42461](https://github.com/nodejs/node/pull/42461)
- \[[`334cc1936b`](https://github.com/nodejs/node/commit/334cc1936b)] - **doc**: expand history for conditional exports changes in v12 (Greg Poole) [#42339](https://github.com/nodejs/node/pull/42339)
- \[[`fb146f9eaf`](https://github.com/nodejs/node/commit/fb146f9eaf)] - **doc**: change comma-splice to two sentences (Rich Trott) [#42455](https://github.com/nodejs/node/pull/42455)
- \[[`ce4b823946`](https://github.com/nodejs/node/commit/ce4b823946)] - **doc**: add link to section (Rich Trott) [#42428](https://github.com/nodejs/node/pull/42428)
- \[[`5869275479`](https://github.com/nodejs/node/commit/5869275479)] - **doc**: fix typo in async_context.md (Anupama Codippily) [#42444](https://github.com/nodejs/node/pull/42444)
- \[[`48bd9fa2c7`](https://github.com/nodejs/node/commit/48bd9fa2c7)] - **doc**: add `trace_gc` to diagnostic tooling support document (Tony Gorez) [#42346](https://github.com/nodejs/node/pull/42346)
- \[[`00f693b6b1`](https://github.com/nodejs/node/commit/00f693b6b1)] - **doc**: make header smaller and dropdown click-driven when JS is on (Paolo Insogna) [#42165](https://github.com/nodejs/node/pull/42165)
- \[[`abbb23620a`](https://github.com/nodejs/node/commit/abbb23620a)] - **doc**: standardize typography for \_semantic versioning\_ (Rich Trott) [#42401](https://github.com/nodejs/node/pull/42401)
- \[[`e763e575c6`](https://github.com/nodejs/node/commit/e763e575c6)] - **doc**: unify import order in CCM example (Tobias Nießen) [#42394](https://github.com/nodejs/node/pull/42394)
- \[[`10d638a735`](https://github.com/nodejs/node/commit/10d638a735)] - **doc**: update property name (Rich Trott) [#42398](https://github.com/nodejs/node/pull/42398)
- \[[`5589a448b7`](https://github.com/nodejs/node/commit/5589a448b7)] - **doc,test**: clarify ChaCha20-Poly1305 usage (Tobias Nießen) [#42323](https://github.com/nodejs/node/pull/42323)
- \[[`902776e674`](https://github.com/nodejs/node/commit/902776e674)] - **esm**: emit experimental warnings in common place (Jacob Smith) [#42314](https://github.com/nodejs/node/pull/42314)
- \[[`8009cb0a78`](https://github.com/nodejs/node/commit/8009cb0a78)] - **fs**: fix write methods param validation and docs (Livia Medeiros) [#42631](https://github.com/nodejs/node/pull/42631)
- \[[`a9dc3a92d9`](https://github.com/nodejs/node/commit/a9dc3a92d9)] - **lib**: prepare files for no-var lint rule (Rich Trott) [#42573](https://github.com/nodejs/node/pull/42573)
- \[[`3306fee824`](https://github.com/nodejs/node/commit/3306fee824)] - **lib**: source maps filter null prefix (Fabian Cook) [#42522](https://github.com/nodejs/node/pull/42522)
- \[[`3bac969655`](https://github.com/nodejs/node/commit/3bac969655)] - **lib**: improve the coverage of the validator (mawaregetsuka) [#42443](https://github.com/nodejs/node/pull/42443)
- \[[`b74de21cc3`](https://github.com/nodejs/node/commit/b74de21cc3)] - **lib**: update JSDoc for linting (Rich Trott) [#42489](https://github.com/nodejs/node/pull/42489)
- \[[`7766bf954f`](https://github.com/nodejs/node/commit/7766bf954f)] - **meta**: update .mailmap and AUTHORS (Rich Trott) [#42602](https://github.com/nodejs/node/pull/42602)
- \[[`93ffc5535a`](https://github.com/nodejs/node/commit/93ffc5535a)] - **meta**: move one or more collaborators to emeritus (Node.js GitHub Bot) [#42500](https://github.com/nodejs/node/pull/42500)
- \[[`256509056d`](https://github.com/nodejs/node/commit/256509056d)] - **meta**: update AUTHORS (Node.js GitHub Bot) [#42585](https://github.com/nodejs/node/pull/42585)
- \[[`41c2a32390`](https://github.com/nodejs/node/commit/41c2a32390)] - **meta**: update AUTHORS (Node.js GitHub Bot) [#42488](https://github.com/nodejs/node/pull/42488)
- \[[`b71a8107c0`](https://github.com/nodejs/node/commit/b71a8107c0)] - **net,dns**: trace tcp connection and dns by perf_hooks (theanarkh) [#42390](https://github.com/nodejs/node/pull/42390)
- \[[`f9f3b6e45d`](https://github.com/nodejs/node/commit/f9f3b6e45d)] - **node-api**: format Node-API related code (Vladimir Morozov) [#42396](https://github.com/nodejs/node/pull/42396)
- \[[`0bd9d9e24f`](https://github.com/nodejs/node/commit/0bd9d9e24f)] - **os**: avoid unnecessary usage of var (Mohammed Keyvanzadeh) [#42563](https://github.com/nodejs/node/pull/42563)
- \[[`e798e26dfd`](https://github.com/nodejs/node/commit/e798e26dfd)] - **src**: add proper mutexes for accessing FIPS state (Anna Henningsen) [#42278](https://github.com/nodejs/node/pull/42278)
- \[[`a1fe0d2222`](https://github.com/nodejs/node/commit/a1fe0d2222)] - **src**: fix typo in InspectorIoDelegate constructor (Kohei Ueno) [#42520](https://github.com/nodejs/node/pull/42520)
- \[[`0c54f3637b`](https://github.com/nodejs/node/commit/0c54f3637b)] - **src**: remove unnecessary static qualifier in crypto_dh.cc (Darshan Sen) [#42492](https://github.com/nodejs/node/pull/42492)
- \[[`2e6a66d7d7`](https://github.com/nodejs/node/commit/2e6a66d7d7)] - **src**: address 3 useless call coverity warnings (Michael Dawson) [#42426](https://github.com/nodejs/node/pull/42426)
- \[[`ce9d840079`](https://github.com/nodejs/node/commit/ce9d840079)] - **src**: properly report exceptions from AddressToJS() (Darshan Sen) [#42054](https://github.com/nodejs/node/pull/42054)
- \[[`c6a558c61b`](https://github.com/nodejs/node/commit/c6a558c61b)] - **src**: suppress false coverity warning (Michael Dawson) [#42284](https://github.com/nodejs/node/pull/42284)
- \[[`878148c266`](https://github.com/nodejs/node/commit/878148c266)] - **src**: refactor IsSupportedAuthenticatedMode (Tobias Nießen) [#42368](https://github.com/nodejs/node/pull/42368)
- \[[`970483ffd3`](https://github.com/nodejs/node/commit/970483ffd3)] - **src,crypto**: handle empty maybe correctly in crypto_dh.cc (Darshan Sen) [#42492](https://github.com/nodejs/node/pull/42492)
- \[[`a348f8ac1a`](https://github.com/nodejs/node/commit/a348f8ac1a)] - **src,crypto**: remove uses of AllocatedBuffer from crypto_dh.cc (Darshan Sen) [#42492](https://github.com/nodejs/node/pull/42492)
- \[[`fb25ba435c`](https://github.com/nodejs/node/commit/fb25ba435c)] - **test**: improve lib/internal/readline/promises.js coverage (MURAKAMI Masahiko) [#42420](https://github.com/nodejs/node/pull/42420)
- \[[`4cbb1ea910`](https://github.com/nodejs/node/commit/4cbb1ea910)] - **test**: remove hack for `atob` and `btoa` WPT tests (Antoine du Hamel) [#42540](https://github.com/nodejs/node/pull/42540)
- \[[`f41a4780d5`](https://github.com/nodejs/node/commit/f41a4780d5)] - **test**: pass data into napi_create_external (Joyee Cheung) [#42532](https://github.com/nodejs/node/pull/42532)
- \[[`90554572b5`](https://github.com/nodejs/node/commit/90554572b5)] - **test**: improve `FileHandle.prototype.write` coverage (Antoine du Hamel) [#42541](https://github.com/nodejs/node/pull/42541)
- \[[`797994e4c0`](https://github.com/nodejs/node/commit/797994e4c0)] - **test**: add test for exception handlings in debugger (Kohei Ueno) [#42327](https://github.com/nodejs/node/pull/42327)
- \[[`8c9b5e9a36`](https://github.com/nodejs/node/commit/8c9b5e9a36)] - **test**: fix typo in common/wpt.js (Ikko Ashimine) [#42567](https://github.com/nodejs/node/pull/42567)
- \[[`2f682091cd`](https://github.com/nodejs/node/commit/2f682091cd)] - **test**: fix typos in test/parallel (Daeyeon Jeong) [#42502](https://github.com/nodejs/node/pull/42502)
- \[[`52d1c8d6d9`](https://github.com/nodejs/node/commit/52d1c8d6d9)] - **test**: add trace-gc flag test (Tony Gorez) [#42471](https://github.com/nodejs/node/pull/42471)
- \[[`19c933c79a`](https://github.com/nodejs/node/commit/19c933c79a)] - **test,fs**: add fs.rm() tests for .git directories (Darshan Sen) [#42410](https://github.com/nodejs/node/pull/42410)
- \[[`d64c4fb94d`](https://github.com/nodejs/node/commit/d64c4fb94d)] - **tools**: enable no-var ESLint rule for lib (Rich Trott) [#42573](https://github.com/nodejs/node/pull/42573)
- \[[`a9f2636d12`](https://github.com/nodejs/node/commit/a9f2636d12)] - **tools**: fixed bug causing JSON format to be broken (mawaregetsuka) [#41565](https://github.com/nodejs/node/pull/41565)
- \[[`bff9cae16a`](https://github.com/nodejs/node/commit/bff9cae16a)] - **tools**: update GHA actions version (Antoine du Hamel) [#42498](https://github.com/nodejs/node/pull/42498)
- \[[`c6bfb225cd`](https://github.com/nodejs/node/commit/c6bfb225cd)] - **tools**: update eslint to 8.12.0 (Node.js GitHub Bot) [#42489](https://github.com/nodejs/node/pull/42489)
- \[[`464e57ffc7`](https://github.com/nodejs/node/commit/464e57ffc7)] - **tools**: update lint-md-dependencies to vfile-reporter\@7.0.4 (Node.js GitHub Bot) [#42487](https://github.com/nodejs/node/pull/42487)
- \[[`13dd8e73df`](https://github.com/nodejs/node/commit/13dd8e73df)] - **tools**: refloat 7 Node.js patches to cpplint.py (Rich Trott) [#42416](https://github.com/nodejs/node/pull/42416)
- \[[`3a1b0e5b87`](https://github.com/nodejs/node/commit/3a1b0e5b87)] - **tools**: bump cpplint to 1.6.0 (Rich Trott) [#42416](https://github.com/nodejs/node/pull/42416)
- \[[`9344a06d9c`](https://github.com/nodejs/node/commit/9344a06d9c)] - **tools**: fix skip PR if CI is still running (Xuguang Mei) [#42377](https://github.com/nodejs/node/pull/42377)

Windows 32-bit Installer: https://nodejs.org/dist/v17.9.0/node-v17.9.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v17.9.0/node-v17.9.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v17.9.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v17.9.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v17.9.0/node-v17.9.0.pkg \
macOS Apple Silicon 64-bit Binary: https://nodejs.org/dist/v17.9.0/node-v17.9.0-darwin-arm64.tar.gz \
macOS Intel 64-bit Binary: https://nodejs.org/dist/v17.9.0/node-v17.9.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v17.9.0/node-v17.9.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v17.9.0/node-v17.9.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v17.9.0/node-v17.9.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v17.9.0/node-v17.9.0-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v17.9.0/node-v17.9.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v17.9.0/node-v17.9.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v17.9.0/node-v17.9.0.tar.gz \
Other release files: https://nodejs.org/dist/v17.9.0/ \
Documentation: https://nodejs.org/docs/v17.9.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

0e6d3b1677c1b9b1228a0df241ae2fa72952a35dba5d1fe165b0cac5a8e230db  node-v17.9.0-aix-ppc64.tar.gz
bad50341f8a1fd737c53efc01aa3f4eaf63df5601adf9ba036a8adb695d13428  node-v17.9.0-darwin-arm64.tar.gz
ce5aef9ac80bce2bb754f0f69cdbc029423b11fd805815b9797099b25b8e3dc7  node-v17.9.0-darwin-arm64.tar.xz
0920116e6507fdc8dcf16bdd717e08797b6d1b97a7a6990294bbf62da9471256  node-v17.9.0-darwin-x64.tar.gz
5fce23a7df1614f4d7354ddd306d26a7d066ac74490abe9d2541da7b29826f71  node-v17.9.0-darwin-x64.tar.xz
ab5adc472d893975e53214082c1d2a8e00ad6633dab6d4bc3355e4c6b4e04209  node-v17.9.0-headers.tar.gz
3160a497558465aeba75bff450e18d87891f794c7d44e22be550ede0f44f56ba  node-v17.9.0-headers.tar.xz
d4acf5c0380c96c867428d0232666d3327dc5fa83a694d7b63f728a76ece84b2  node-v17.9.0-linux-arm64.tar.gz
ddbb32c9de3f93270bbdace4c840022c5ed89faaf76e9abaf9ebb45c07d5cee3  node-v17.9.0-linux-arm64.tar.xz
912bfd3cbeaf395a4593ac752759202727839ceaae34d20df1766b56b741c764  node-v17.9.0-linux-armv7l.tar.gz
b3916f407724dfb3c8769abbeb2275db9dab6d42a21fafc70692fe8d10bc742f  node-v17.9.0-linux-armv7l.tar.xz
fa42d94e3904cabc43022f3bbb0ff761be67f8850b5a1750063609ba825b4d6e  node-v17.9.0-linux-ppc64le.tar.gz
5c515e59e86550fb15c8e9f316fa3c465602a6a08da01486c77dd2425dfffe42  node-v17.9.0-linux-ppc64le.tar.xz
2f9ccbaa5b575110af64c71d6d973f5b24d8169984fa59f90ceff5823b3eedc5  node-v17.9.0-linux-s390x.tar.gz
456dbfcc1e7df8775cec9042f3e6254ab5cd1bd00f1a7b9c8a60d2cc75abe51b  node-v17.9.0-linux-s390x.tar.xz
8c9f4c95c254336fcb2c768e746f4316b8176adc0fb599cbbb460d0933991d12  node-v17.9.0-linux-x64.tar.gz
213b5dc5f4dac726b79a4b2a1da7eb03c5d2e6b8a47202682e37a1e21307f996  node-v17.9.0-linux-x64.tar.xz
e0189d1acf63388de63c86d35c61bd5a2e85fbe332689f3212efad431d1404ce  node-v17.9.0.pkg
25d8c6559456d548823e3f7f4fae7dc8c7d5a0f7772c79c8d902528e801ba09a  node-v17.9.0.tar.gz
5659f6da66dc4c959f59ab63e8b9171892847c3440f4d1cb68394dba67ca39e0  node-v17.9.0.tar.xz
6fedf28770d956939e79449679cb196ceecccfdcbf7f9dc816103e5b824c44aa  node-v17.9.0-win-x64.7z
f72fe1f0f961c598275a253b411a2fada2ad2c4e3434bcf6e9cf81228013f022  node-v17.9.0-win-x64.zip
3897af062b68a41f7ff5224ee0bf6a31932e15c262fae13c20f6d68f8825eb5a  node-v17.9.0-win-x86.7z
1e6dbf14a285f0cfe525619ee0982118042b2ef4140dc5275f69aa59c63593b5  node-v17.9.0-win-x86.zip
4deda07362a8322144466e20337f0b956162dbc0c13c2212826014d290ef3beb  node-v17.9.0-x64.msi
fcb98e5489b9b7da8e5bc21024e91cc9263157619bf56b5efb5ee88f896326a3  node-v17.9.0-x86.msi
2acbdd083c6456e38af5165252f8882e92bdbc5b12c9e064cc6c543fa0ea5178  win-x64/node.exe
998cc6721961278e2baa41598ea68ab7c04e8ab032bd3b36ed94e1d60d462c42  win-x64/node.lib
0efb2607cadd3080fe759f52834501a09181524d68ed0c8f063a7e0036bb9079  win-x64/node_pdb.7z
3bfc01d149f6318d6755c9bfa6a7caf9360c7ca4aa8cb4e795c5435a90a7174d  win-x64/node_pdb.zip
516a611d02eb9eb5980f1620c9f81cca5d579e2bd283aab9b3dc38e1a125eda2  win-x86/node.exe
4dd1b950bfa0bf82923b12fa5b67912aa4d931e774593755aeb7ec1191d6579b  win-x86/node.lib
b0b30feb037a5582557659580698afe197fbc9bde0c8e24bfafa34beba23833f  win-x86/node_pdb.7z
c8800285ff8e0dac51695e003799ab25cc316abc5a3f0549cd8775f95771ed00  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQEzBAEBCAAdFiEETtd49TnjY0x3nIfG1wYoSKGrAFwFAmJPa7IACgkQ1wYoSKGr
AFzdsAf/V+8frxCG9lM3BsOjAIXNQIAI7fEGKTpIiH0Jh+zZu5AayUgUesVZDNli
wi1ZGHowVG+UAEohA3H47pWJPOdXE1GhOYu1SPd/ViFGE3AtKs1hSoFZw4zAmqFm
r05U+Nybg/W/mvpgeQtobGnblLZjikc9EAMK0O/8zu7G/PlKAkfih0eB1T+XlCrv
txjPj0W23Q2uhFTbge6/iTAQ/EpaM+HxU7ou8Y6DRcH2JjaN6jv/YojFUhTbW94f
asMjtZF2Ss9ZhzX2sLB/gTwnsWiC0oI5lXhurfmF5iWjE73Zn4Es2BjYw2HsZq2h
mBR2ishh/SaGrDRG3mcIPckVHzJcfg==
=LzNv
-----END PGP SIGNATURE-----

```
