---
date: '2017-04-04T12:36:54.192Z'
category: release
title: Node v6.10.2 (LTS)
layout: blog-post
author: <PERSON><PERSON>
---

### Notable changes

- **crypto**:
  - fix memory leak if certificate is revoked (<PERSON>) [#12089](https://github.com/nodejs/node/pull/12089)
- **deps**:
  - upgrade zlib to 1.2.11 (<PERSON>) [#10980](https://github.com/nodejs/node/pull/10980)
  - backport V8 fixes for spread syntax regression causing segfaults (<PERSON><PERSON><PERSON>) [#12037](https://github.com/nodejs/node/pull/12037)
- **repl**:
  - Revert commit that broke REPL display on Windows (<PERSON><PERSON>) [#12123](https://github.com/nodejs/node/pull/12123)

### Commits

- [[`5f644d2f6f`](https://github.com/nodejs/node/commit/5f644d2f6f)] - **crypto**: fix memory leak if certificate is revoked (<PERSON>) [#12089](https://github.com/nodejs/node/pull/12089)
- [[`912f78a566`](https://github.com/nodejs/node/commit/912f78a566)] - **deps**: fix CLEAR_HASH macro to be usable as a single statement (Sam Roberts) [#11616](https://github.com/nodejs/node/pull/11616)
- [[`abe9132011`](https://github.com/nodejs/node/commit/abe9132011)] - **deps**: upgrade zlib to 1.2.11 (Sam Roberts) [#10980](https://github.com/nodejs/node/pull/10980)
- [[`1ff512c185`](https://github.com/nodejs/node/commit/1ff512c185)] - **deps**: backport e427300 from upstream V8 (Michaël Zasso) [#12037](https://github.com/nodejs/node/pull/12037)
- [[`8dfc710a06`](https://github.com/nodejs/node/commit/8dfc710a06)] - **deps**: cherry-pick b9f682b from upstream V8 (Michaël Zasso) [#12037](https://github.com/nodejs/node/pull/12037)
- [[`52bdb8f246`](https://github.com/nodejs/node/commit/52bdb8f246)] - **deps**: backport 2cabc86 from upstream V8 (Michaël Zasso) [#12037](https://github.com/nodejs/node/pull/12037)
- [[`64fc5a4541`](https://github.com/nodejs/node/commit/d60ceb8a02)] - **repl** Revert: "Revert "repl: disable Ctrl+C support..." (Myles Borins) [#12123](https://github.com/nodejs/node/pull/12123)

Windows 32-bit Installer: https://nodejs.org/dist/v6.10.2/node-v6.10.2-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v6.10.2/node-v6.10.2-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v6.10.2/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v6.10.2/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v6.10.2/node-v6.10.2.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v6.10.2/node-v6.10.2-darwin-x64.tar.gz \
Linux 32-bit Binary: https://nodejs.org/dist/v6.10.2/node-v6.10.2-linux-x86.tar.xz \
Linux 64-bit Binary: https://nodejs.org/dist/v6.10.2/node-v6.10.2-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v6.10.2/node-v6.10.2-linux-ppc64le.tar.xz \
Linux PPC BE 64-bit Binary: https://nodejs.org/dist/v6.10.2/node-v6.10.2-linux-ppc64.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v6.10.2/node-v6.10.2-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v6.10.2/node-v6.10.2-aix-ppc64.tar.gz \
SmartOS 32-bit Binary: https://nodejs.org/dist/v6.10.2/node-v6.10.2-sunos-x86.tar.xz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v6.10.2/node-v6.10.2-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v6.10.2/node-v6.10.2-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v6.10.2/node-v6.10.2-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v6.10.2/node-v6.10.2-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v6.10.2/node-v6.10.2.tar.gz \
Other release files: https://nodejs.org/dist/v6.10.2/ \
Documentation: https://nodejs.org/docs/v6.10.2/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

625947ad107156105f0b1ee05cde078f0175793ea59022e831bab85b7c19d6be  node-v6.10.2-aix-ppc64.tar.gz
63383d322612777b38cdb81b66a7f4748a9c803d60ab710a79375204cb033129  node-v6.10.2-darwin-x64.tar.gz
360b887361b2597613f18968e3fc0e920079a363d0535fc4e40532e3426fc6eb  node-v6.10.2-darwin-x64.tar.xz
5b446d065c86e6567da3ee84f7930b70a0edf5d0ddc90bf1e246320ac72eea08  node-v6.10.2-headers.tar.gz
716d403769bd218ed33fd1ed0f55054a1544adc9b3f67c6b5a8fbd61327c1909  node-v6.10.2-headers.tar.xz
97de0340b6dbf38e3d995df880a94c58d403c3054676d8fc9192b83a3735f0b8  node-v6.10.2-linux-arm64.tar.gz
87b7c2c7c9335a8c01fd702caa6463ed6cf40e0c3be533f6fe33e61d8254f787  node-v6.10.2-linux-arm64.tar.xz
ef7ebad44c97e117994ac743f37798efaab7c0846c5476992ceac304cec5be41  node-v6.10.2-linux-armv6l.tar.gz
2ea5938bd50ea1f7e9ed18f70adbde6f08f2ceed703ffdb425969b06c1f1a3d3  node-v6.10.2-linux-armv6l.tar.xz
9f87b4e1cb96140b37c4421308b6f914f9e5832c285dea735dc0c427451126c1  node-v6.10.2-linux-armv7l.tar.gz
a2087c8e37f66677f0bcdb7ce6da8e5489972db6a921dad8c1fd406f4ce19338  node-v6.10.2-linux-armv7l.tar.xz
527f90e40c6dd966d6025319f8bc6935e80e81250b7446613a93c528885ee418  node-v6.10.2-linux-ppc64le.tar.gz
35caa0b91ebfe35d897f13adacbfc0bf0da4db15498ac64eb1f0da272f69d223  node-v6.10.2-linux-ppc64le.tar.xz
27bfa1e70c82fb6c910d8208bdb0af92386a98f6d8e85ef981a90a9703562af5  node-v6.10.2-linux-ppc64.tar.gz
f48b02523da44404c7c7e10c61854a048d3989abbd5b4052bc2bb3178c1418f0  node-v6.10.2-linux-ppc64.tar.xz
f1163d323b0f0c2b8533382a2a84888b2080e4b42a9192e91e489b5a0613fcbb  node-v6.10.2-linux-s390x.tar.gz
949e4d8a511dc9c9f7a8048af0251bcaace3fda12dffe6bda4e08467acfd1c75  node-v6.10.2-linux-s390x.tar.xz
35accd2d9ccac747eff0f236e2843bc2198ba7765e2340441d6230861bae4e1b  node-v6.10.2-linux-x64.tar.gz
b519cd616b0671ab789d2645c5c026deb7e016d73a867ab4b1b8c9ceba9c3503  node-v6.10.2-linux-x64.tar.xz
6721221fab4e3b3a1be6573900b9e368c7a74ac1c1c3ae982e49c5583e8962e3  node-v6.10.2-linux-x86.tar.gz
d2179b2d14a399973298c1f4598871058f4cca23c77ea3aca098fa6ff809d55c  node-v6.10.2-linux-x86.tar.xz
6a6fb245648236dfd80f50b5d35b54584ad666207b41decfa965581b96d68cd9  node-v6.10.2.pkg
aa3de2a247de9cebbe0a8712fb0eba4c66f82e004bbf5b57c8583fbfb6a01ab7  node-v6.10.2-sunos-x64.tar.gz
f2c61aad222fbfad383e8c6a92520b610d2e5711c764724627d5103536d8c9e9  node-v6.10.2-sunos-x64.tar.xz
b07246e9da31e3a762469d609d03558318bd160cd2a04a822bd6725291f10999  node-v6.10.2-sunos-x86.tar.gz
4c1e3afe305fb586a6fe68c814e61e501dca573419751edabea975386cbde64f  node-v6.10.2-sunos-x86.tar.xz
9b897dd6604d50ae5fff25fd14b1c4035462d0598735799e0cfb4f17cb6e0d19  node-v6.10.2.tar.gz
80aa11333da99813973a99646e2113c6be5b63f665c0731ed14ecb94cbe846b6  node-v6.10.2.tar.xz
08572d6714ab8e76cfac6211b63fcf2062ff443355be28a0f106d2cc05c45e46  node-v6.10.2-win-x64.7z
d778ed84685c6604192cfcf40192004e27fb11c9e65c3ce4b283d90703b4192c  node-v6.10.2-win-x64.zip
4149533ef54039cef1e49eb5e41cbb1688e5509751a4f518b05d330e38dc5eea  node-v6.10.2-win-x86.7z
ee0e456bd5cc2d689a19a3a73947989b979bad9e7026685da2f537a4c93f2c91  node-v6.10.2-win-x86.zip
4d0a8b82649dc38f606b400006a2ab2d3585b65c44b4f5b71621444b3c3f7754  node-v6.10.2-x64.msi
bc1febcfd2e8fbaf6bdcd06c1eeee5d33ae61ae37f74e84392ea3b46da5ea550  node-v6.10.2-x86.msi
463301bd94198a53793bb5c28ba19b0520fb775de34689c69cc6ce41b71c8b9b  win-x64/node.exe
9f4bb26e4329ba72068314361b163a827e8845ac810b3daa55160fa269119dee  win-x64/node.lib
0f3f45eb67eca91cd9395b423750b79dd28e76069f108b4feeed9b6dc52b16f3  win-x64/node_pdb.7z
a12a60a9d655beaeda5a12bc9d3463777df20e2974ab6e3e464acc2c6fa9b009  win-x64/node_pdb.zip
91fec6e9b7dc9cb2f7b54f15d8d8c24ff1d294d5fa912d2c641d2e7de627597b  win-x86/node.exe
c7a55bd27f6be0605dcb876a6244541404ef868745c0060b1daf3167483fcc95  win-x86/node.lib
f37179ac8564b21e6828c4ad5c725edadfefd6ff8c5356722cd96051f1f9986a  win-x86/node_pdb.7z
04afc75644154b80c0bc3e740ea5925cbba46e92cd75093ca62baaa4a08265f2  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQEcBAEBCAAGBQJY44/KAAoJEJM7AfQLXKlG43AIAKrAzGLa25oAcPKE3fl2wsyZ
wiBo8Mj+Rvy8tSXjDlaLPPPhw7+3IHY8TvQC2eXVPiH4a42lpgFWpupgN0pL7y+M
PAmnn+q53wnBA5CtJH9L0NkB2p4KJuP/TTt/UmZiB1pXyI4y3DZ59mQpnmPYeW4b
xAtlPbGtqofF/W3ChnXrsxjS0O5iSETj9xOus93RTKOsqknBpVS5VJHO1oSkYlRB
Z3IbF0LrP8SJN4NOynLTDZleRCLXWkzbMzFId8/Ork23Y8e5UC4dy47YC/xry8yq
bwokir2TA5vprJAxaLjfuz+XXUaB0B2MhrnErFXBHlAaGpSNDJIPznzcH6QERSA=
=fTqQ
-----END PGP SIGNATURE-----

```
