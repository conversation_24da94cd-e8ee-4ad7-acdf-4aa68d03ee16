---
date: '2021-04-06T20:50:15.313Z'
category: release
title: Node v15.14.0 (Current)
layout: blog-post
author: <PERSON><PERSON>
---

### Notable Changes

Vulnerabilties Fixed:

- **CVE-2021-3450**: OpenSSL - CA certificate check bypass with X509_V_FLAG_X509_STRICT (High)
  - This is a vulnerability in OpenSSL which may be exploited through Node.js. You can read more about it in https://www.openssl.org/news/secadv/20210325.txt
  - Impacts:
    - All versions of the 15.x, 14.x, 12.x and 10.x releases lines
- **CVE-2021-3449**: OpenSSL - NULL pointer deref in signature_algorithms processing (High)
  - This is a vulnerability in OpenSSL which may be exploited through Node.js. You can read more about it in https://www.openssl.org/news/secadv/20210325.txt
  - Impacts:
    - All versions of the 15.x, 14.x, 12.x and 10.x releases lines
- **CVE-2020-7774**: npm upgrade - Update y18n to fix Prototype-Pollution (High)
  - This is a vulnerability in the y18n npm module which may be exploited by prototype pollution. You can read more about it in https://github.com/advisories/GHSA-c4w7-xm78-47vh
  - Impacts:
    - All versions of the 14.x, 12.x and 10.x releases lines

Other Notable Changes:

- [[`b6f4901221`](https://github.com/nodejs/node/commit/b6f4901221)] - **(SEMVER-MINOR)** **fs**: add support for async iterators to `fsPromises.writeFile` (HiroyukiYagihashi) [#37490](https://github.com/nodejs/node/pull/37490)
- [[`0709cbb7fe`](https://github.com/nodejs/node/commit/0709cbb7fe)] - **(SEMVER-MINOR)** **net**: allow net.BlockList to use net.SocketAddress objects (James M Snell) [#37917](https://github.com/nodejs/node/pull/37917)
- [[`daa8a7bbcf`](https://github.com/nodejs/node/commit/daa8a7bbcf)] - **(SEMVER-MINOR)** **net**: add SocketAddress class (James M Snell) [#37917](https://github.com/nodejs/node/pull/37917)
- [[`a4169ce519`](https://github.com/nodejs/node/commit/a4169ce519)] - **(SEMVER-MINOR)** **net**: make net.BlockList cloneable (James M Snell) [#37917](https://github.com/nodejs/node/pull/37917)
- [[`669b81c68b`](https://github.com/nodejs/node/commit/669b81c68b)] - **(SEMVER-MINOR)** **net,tls**: add abort signal support to connect (Nitzan Uziely) [#37735](https://github.com/nodejs/node/pull/37735)
- [[`a1123f0a29`](https://github.com/nodejs/node/commit/a1123f0a29)] - **(SEMVER-MINOR)** **readline**: add AbortSignal support to interface (Nitzan Uziely) [#37932](https://github.com/nodejs/node/pull/37932)

### Commits

- [[`ac69b95e47`](https://github.com/nodejs/node/commit/ac69b95e47)] - **crypto**: use correct webcrypto RSASSA-PKCS1-v1_5 algorithm name (Filip Skokan) [#38029](https://github.com/nodejs/node/pull/38029)
- [[`960c6be229`](https://github.com/nodejs/node/commit/960c6be229)] - **crypto**: add buffering to randomInt (Tobias Nießen) [#35110](https://github.com/nodejs/node/pull/35110)
- [[`4ef102d34e`](https://github.com/nodejs/node/commit/4ef102d34e)] - **deps**: update to cjs-module-lexer@1.1.1 (Guy Bedford) [#37992](https://github.com/nodejs/node/pull/37992)
- [[`f0e77149a4`](https://github.com/nodejs/node/commit/f0e77149a4)] - **deps**: update archs files for OpenSSL-1.1.1k (Hassaan Pasha) [#37916](https://github.com/nodejs/node/pull/37916)
- [[`bbdcdad2c6`](https://github.com/nodejs/node/commit/bbdcdad2c6)] - **deps**: upgrade openssl sources to 1.1.1k+quic (Hassaan Pasha) [#37916](https://github.com/nodejs/node/pull/37916)
- [[`913ec56798`](https://github.com/nodejs/node/commit/913ec56798)] - **deps**: cjs-module-lexer: cherry-pick 22093e765f (pezhmanparsaee) [#37895](https://github.com/nodejs/node/pull/37895)
- [[`afc6ab2122`](https://github.com/nodejs/node/commit/afc6ab2122)] - **doc**: fix asyncLocalStorage.run() description (Darkripper214) [#38023](https://github.com/nodejs/node/pull/38023)
- [[`b40d35d649`](https://github.com/nodejs/node/commit/b40d35d649)] - **doc**: document how to unref stdin when using readline.Interface (Anu Pasumarthy) [#38019](https://github.com/nodejs/node/pull/38019)
- [[`ce14080473`](https://github.com/nodejs/node/commit/ce14080473)] - **doc**: move psmarshall to collaborators emeriti (Peter Marshall) [#37994](https://github.com/nodejs/node/pull/37994)
- [[`ae70aa3c63`](https://github.com/nodejs/node/commit/ae70aa3c63)] - **doc**: add distinctive color for code elements inside links (Antoine du Hamel) [#37950](https://github.com/nodejs/node/pull/37950)
- [[`8792c7c96b`](https://github.com/nodejs/node/commit/8792c7c96b)] - **doc**: add missing events.on metadata (Anna Henningsen) [#37965](https://github.com/nodejs/node/pull/37965)
- [[`a57dc06adf`](https://github.com/nodejs/node/commit/a57dc06adf)] - **doc**: improve Buffer's encoding documentation (Michaël Zasso) [#37945](https://github.com/nodejs/node/pull/37945)
- [[`f3fabb57cf`](https://github.com/nodejs/node/commit/f3fabb57cf)] - **doc**: add missing cleanup step in OpenSSL upgrade (Tobias Nießen) [#37927](https://github.com/nodejs/node/pull/37927)
- [[`13c3924af8`](https://github.com/nodejs/node/commit/13c3924af8)] - **doc**: add Windows-specific info to subprocess.kill() (João Lucas Lucchetta) [#34867](https://github.com/nodejs/node/pull/34867)
- [[`b6f4901221`](https://github.com/nodejs/node/commit/b6f4901221)] - **(SEMVER-MINOR)** **fs**: add support for async iterators to `fsPromises.writeFile` (HiroyukiYagihashi) [#37490](https://github.com/nodejs/node/pull/37490)
- [[`ad7e34446c`](https://github.com/nodejs/node/commit/ad7e34446c)] - **fs**: fix chown abort (Darshan Sen) [#38004](https://github.com/nodejs/node/pull/38004)
- [[`d86aca9a77`](https://github.com/nodejs/node/commit/d86aca9a77)] - **http**: optimize debug function correctly (Michaël Zasso) [#37966](https://github.com/nodejs/node/pull/37966)
- [[`062541aae5`](https://github.com/nodejs/node/commit/062541aae5)] - **http2**: add specific error code for custom frames (Anna Henningsen) [#37936](https://github.com/nodejs/node/pull/37936)
- [[`8525231902`](https://github.com/nodejs/node/commit/8525231902)] - **lib**: change wording in lib/domain.js comment (Akhil Marsonya) [#37933](https://github.com/nodejs/node/pull/37933)
- [[`21e399be4c`](https://github.com/nodejs/node/commit/21e399be4c)] - **lib**: change wording in lib/internal/child_process comment (Akhil Marsonya) [#37903](https://github.com/nodejs/node/pull/37903)
- [[`3ab9619e56`](https://github.com/nodejs/node/commit/3ab9619e56)] - **module**: improve error message for invalid data URL (Antoine du Hamel) [#37701](https://github.com/nodejs/node/pull/37701)
- [[`0709cbb7fe`](https://github.com/nodejs/node/commit/0709cbb7fe)] - **(SEMVER-MINOR)** **net**: allow net.BlockList to use net.SocketAddress objects (James M Snell) [#37917](https://github.com/nodejs/node/pull/37917)
- [[`daa8a7bbcf`](https://github.com/nodejs/node/commit/daa8a7bbcf)] - **(SEMVER-MINOR)** **net**: add SocketAddress class (James M Snell) [#37917](https://github.com/nodejs/node/pull/37917)
- [[`a4169ce519`](https://github.com/nodejs/node/commit/a4169ce519)] - **(SEMVER-MINOR)** **net**: make net.BlockList cloneable (James M Snell) [#37917](https://github.com/nodejs/node/pull/37917)
- [[`669b81c68b`](https://github.com/nodejs/node/commit/669b81c68b)] - **(SEMVER-MINOR)** **net,tls**: add abort signal support to connect (Nitzan Uziely) [#37735](https://github.com/nodejs/node/pull/37735)
- [[`a94cc27cbe`](https://github.com/nodejs/node/commit/a94cc27cbe)] - **path**: refactor to use more primordials (Akhil Marsonya) [#37893](https://github.com/nodejs/node/pull/37893)
- [[`6cc1e15669`](https://github.com/nodejs/node/commit/6cc1e15669)] - **readline**: fix pre-aborted signal question handling (Nitzan Uziely) [#37929](https://github.com/nodejs/node/pull/37929)
- [[`a1123f0a29`](https://github.com/nodejs/node/commit/a1123f0a29)] - **(SEMVER-MINOR)** **readline**: add AbortSignal support to interface (Nitzan Uziely) [#37932](https://github.com/nodejs/node/pull/37932)
- [[`629e72e9f4`](https://github.com/nodejs/node/commit/629e72e9f4)] - **src**: fix typo in node_mutex (Tobias Nießen) [#38011](https://github.com/nodejs/node/pull/38011)
- [[`e61cc0bfb0`](https://github.com/nodejs/node/commit/e61cc0bfb0)] - **src**: fix typos in crypto comments (Tobias Nießen) [#38024](https://github.com/nodejs/node/pull/38024)
- [[`6ad0b6f0f5`](https://github.com/nodejs/node/commit/6ad0b6f0f5)] - **src**: fix error handling for CryptoJob::ToResult (Tobias Nießen) [#37076](https://github.com/nodejs/node/pull/37076)
- [[`3175559bed`](https://github.com/nodejs/node/commit/3175559bed)] - **test**: add extra space in test failure output (Qingyu Deng) [#37957](https://github.com/nodejs/node/pull/37957)
- [[`0243376cfc`](https://github.com/nodejs/node/commit/0243376cfc)] - **test**: use faster variant for rss (Pooja D P) [#36839](https://github.com/nodejs/node/pull/36839)
- [[`b02c352ad6`](https://github.com/nodejs/node/commit/b02c352ad6)] - **test**: fix test-tls-no-sslv3 for OpenSSL 3 (Richard Lau) [#38027](https://github.com/nodejs/node/pull/38027)
- [[`0db1a1eacf`](https://github.com/nodejs/node/commit/0db1a1eacf)] - **test**: deflake test-fs-read-optional-params (Luigi Pinca) [#37991](https://github.com/nodejs/node/pull/37991)
- [[`4d50975cd7`](https://github.com/nodejs/node/commit/4d50975cd7)] - **test**: improve clarity of ALS-enable-disable.js (Darkripper214) [#38008](https://github.com/nodejs/node/pull/38008)
- [[`5e15ae05d0`](https://github.com/nodejs/node/commit/5e15ae05d0)] - **test**: add DataView test case for v8 serdes (Rich Trott) [#37955](https://github.com/nodejs/node/pull/37955)
- [[`6d28a24f1c`](https://github.com/nodejs/node/commit/6d28a24f1c)] - **tools**: update ESLint to 7.23.0 (Luigi Pinca) [#37979](https://github.com/nodejs/node/pull/37979)
- [[`51e7a33d54`](https://github.com/nodejs/node/commit/51e7a33d54)] - **tools,doc**: add "legacy" badge in the TOC (Antoine du Hamel) [#37949](https://github.com/nodejs/node/pull/37949)
- [[`570fbcef93`](https://github.com/nodejs/node/commit/570fbcef93)] - **url**: forbid pipe in URL host (Darshan Sen) [#37877](https://github.com/nodejs/node/pull/37877)

Windows 32-bit Installer: https://nodejs.org/dist/v15.14.0/node-v15.14.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v15.14.0/node-v15.14.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v15.14.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v15.14.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v15.14.0/node-v15.14.0.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v15.14.0/node-v15.14.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v15.14.0/node-v15.14.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v15.14.0/node-v15.14.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v15.14.0/node-v15.14.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v15.14.0/node-v15.14.0-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v15.14.0/node-v15.14.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v15.14.0/node-v15.14.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v15.14.0/node-v15.14.0.tar.gz \
Other release files: https://nodejs.org/dist/v15.14.0/ \
Documentation: https://nodejs.org/docs/v15.14.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

cb133ef05eb9c20c23a2f267f216dca0a66166bb5cdbf1a1871c114a439f8767  node-v15.14.0-aix-ppc64.tar.gz
a3ea5f2da4868de1513664de76ce09cc8234312a0918223b19e40d3ca4890bf2  node-v15.14.0-darwin-x64.tar.gz
f355aeda5049fdbac4acca23a7bb6f66e145a179a52bd2489e3f76fbe0feb161  node-v15.14.0-darwin-x64.tar.xz
fa591c23cb61cb91f09df6ffaebd68dce470073749e5e924e5f3436fbc121132  node-v15.14.0-headers.tar.gz
147c7d622b185cabb7304f80a74a78a1b21a06eecdd2caf5f56fbf07a816d680  node-v15.14.0-headers.tar.xz
6d5e0074fe4a45d444bc581aa1fd7ce7081b8491b0f785414a6e5cc30c42854a  node-v15.14.0-linux-arm64.tar.gz
23108e22efd5b9684ffe357ef25605aba9abc9dd4c6f29f34d0a4680f29ffb45  node-v15.14.0-linux-arm64.tar.xz
1cef461a73a124dd3f212e2b8230638f4d16b5cc0915425ffad8aabac050d9fb  node-v15.14.0-linux-armv7l.tar.gz
3636dfdfeedf11c76486692ea8730dfb585f4a7846512e4cb9fc4b725d61cb25  node-v15.14.0-linux-armv7l.tar.xz
ad286636152e4ba060d2e13eccb166eb8eda8dda04a39ce76f026e9127e90137  node-v15.14.0-linux-ppc64le.tar.gz
f8870a4716f6bfe7ffbb34f0a16abe56bea83761cbca0b856dc45b7fb0268f02  node-v15.14.0-linux-ppc64le.tar.xz
20dcd3f97e4c72dfbdc0eaaa8301caf50e2204601559797270062e3d6fefabf5  node-v15.14.0-linux-s390x.tar.gz
f9a6129724e7a48c6719e21081e6bb09adc0dcc88067a34d8c1084f6c096e6c7  node-v15.14.0-linux-s390x.tar.xz
23f8adb7afbd9969f0f9b8b2da0ba3e0a9db57c547aa0c5e0885f0b2aae6081c  node-v15.14.0-linux-x64.tar.gz
f40a52c77a5a98203d24d6e5213c1a189bfc9736d0d9f667cb61151e9431b2a8  node-v15.14.0-linux-x64.tar.xz
2900b61708fb27679f4c92619752790b78587ece5a6c2a4e6946b810ea93250e  node-v15.14.0.pkg
f3a35c1b29b58846575085fdee7774d78b75ff4cf1e52572afce7f38685b159a  node-v15.14.0.tar.gz
8122dc4eea4f00af32a1d14ca85a1d4d6ca7b2dcffd9a731bda149fc5593a66e  node-v15.14.0.tar.xz
30bb88b225e3138e8dff60cc8cf6c815ec9b7680933cede18d7ed3a947efcb41  node-v15.14.0-win-x64.7z
efd8d6fba2030d97172a693c05ed4fc446ca5b2258ef2fa6f03f32abb402f036  node-v15.14.0-win-x64.zip
741a0e9a93ff12d08fdfb661fa44ebd155d626d853016fd7eb6815d3c09fbbbb  node-v15.14.0-win-x86.7z
192905800143973a5499985b418deff121d42087f8afc68e986ab0ad5baea741  node-v15.14.0-win-x86.zip
fa460c0483126bad8296be312f23bdc8c8baf0974f512427b1c90846971af29f  node-v15.14.0-x64.msi
d22b24e8fe60b6d730f9241c36b0f6b1a34b735eece478dd24c0792e2576c1c8  node-v15.14.0-x86.msi
225c8dd246f110d8939e2bd9c3b86704375d7ee644f575119d5bf0c3a730ac92  win-x64/node.exe
cef6b29471f8faa5291be30c049822267cfcfe3437c2d724d720b01f6480a827  win-x64/node.lib
9c3bda01748f88b5ecc35b16ffd5ff21d93cecf8b5729f181bed9536ae69dab8  win-x64/node_pdb.7z
a4a119e72529eeadee8fe367663b5761a7b5979ccf77abd3da097870b5e50fb5  win-x64/node_pdb.zip
fc1bced2ecc0ccd3b33c60e3b8230cdb48feae75c5a45ae241f27e12da310825  win-x86/node.exe
889e03560e730464fe438f9b167e0907b61d1d47a19d05ede27e68c5da136991  win-x86/node.lib
59de7e99d800e474df2b6bc287d18f85f2e4e471897792b40f7b86cd09f38208  win-x86/node_pdb.7z
c1daba7b20b6990382467dcbceb72f980dd4d6ae11f67c85a244ef65400cd565  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQEzBAEBCAAdFiEEDv/hvO/ZyE49CYFSkzsB9AtcqUYFAmBsyXAACgkQkzsB9Atc
qUaU6wgAthF8ouuSj6Ag9n/0i+DMbdYnTcLsLFbtlPdUgjPCRvxjivqH2GqO7jXq
8WKuHxj9sqKP4t+hdujbWqEY/Oj4RsHT3h4NARlREjl2ZKCKt27rPJpqcHjS3D1x
xS7tEOSJ9flq2fddXOFHYPkK5FH9s4kaXtpDywnb4UiEmO1mKICO5yhJt8NYEKlN
67+d/u17Pmgo3YgqMpa4IT+u1oy1XkiLwiBfkZmUtmYLFrLtwbYAalhoCn4HDOyj
JWie/9fK/A9lcfFtB6xLC2mudDBpQM4YSbON1gh7VriLpKPz4mjK6TpkNfxpAeii
1BbOrsrDwXoTWnxH4QpmQCXYY7N/iw==
=oRXA
-----END PGP SIGNATURE-----

```
