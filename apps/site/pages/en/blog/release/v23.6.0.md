---
date: '2025-01-07T17:33:37.965Z'
category: release
title: Node v23.6.0 (Current)
layout: blog-post
author: <PERSON>
---

## 2025-01-07, Version 23.6.0 (Current), @marco-ippolito

### Notable Changes

#### Unflagging --experimental-strip-types

This release enables the flag `--experimental-strip-types` by default.
Node.js will be able to execute TypeScript files without additional configuration:

```bash
node file.ts
```

There are some limitations in the supported syntax documented at <https://nodejs.org/api/typescript.html#type-stripping>
This feature is experimental and is subject to change.

Contributed by <PERSON> in [#56350](https://github.com/nodejs/node/pull/56350)

### Other Notable Changes

- \[[`c1023284c3`](https://github.com/nodejs/node/commit/c1023284c3)] - **(SEMVER-MINOR)** **lib**: add typescript support to STDIN eval (<PERSON>) [#56359](https://github.com/nodejs/node/pull/56359)
- \[[`8dc39e5e2e`](https://github.com/nodejs/node/commit/8dc39e5e2e)] - **(SEMVER-MINOR)** **process**: add process.ref() and process.unref() methods (James M Snell) [#56400](https://github.com/nodejs/node/pull/56400)
- \[[`8b20cc212b`](https://github.com/nodejs/node/commit/8b20cc212b)] - **(SEMVER-MINOR)** **worker**: add eval ts input (Marco Ippolito) [#56394](https://github.com/nodejs/node/pull/56394)

### Commits

- \[[`7b4d288116`](https://github.com/nodejs/node/commit/7b4d288116)] - **assert**: make partialDeepStrictEqual throw when comparing \[0] with \[-0] (Giovanni) [#56237](https://github.com/nodejs/node/pull/56237)
- \[[`0ec2ed0a0b`](https://github.com/nodejs/node/commit/0ec2ed0a0b)] - **build**: fix GN build for ngtcp2 (Cheng) [#56300](https://github.com/nodejs/node/pull/56300)
- \[[`ab3e64630b`](https://github.com/nodejs/node/commit/ab3e64630b)] - **build**: test macos-13 on GitHub actions (Michaël Zasso) [#56307](https://github.com/nodejs/node/pull/56307)
- \[[`46fb69daca`](https://github.com/nodejs/node/commit/46fb69daca)] - **build**: build v8 with -fvisibility=hidden on macOS (Joyee Cheung) [#56275](https://github.com/nodejs/node/pull/56275)
- \[[`9d4930b993`](https://github.com/nodejs/node/commit/9d4930b993)] - **deps**: update simdutf to 5.7.2 (Node.js GitHub Bot) [#56388](https://github.com/nodejs/node/pull/56388)
- \[[`6afe36397e`](https://github.com/nodejs/node/commit/6afe36397e)] - **deps**: update amaro to 0.2.1 (Node.js GitHub Bot) [#56390](https://github.com/nodejs/node/pull/56390)
- \[[`195990a0ee`](https://github.com/nodejs/node/commit/195990a0ee)] - **deps**: update googletest to 7d76a23 (Node.js GitHub Bot) [#56387](https://github.com/nodejs/node/pull/56387)
- \[[`b9c0852fc6`](https://github.com/nodejs/node/commit/b9c0852fc6)] - **deps**: update googletest to e54519b (Node.js GitHub Bot) [#56370](https://github.com/nodejs/node/pull/56370)
- \[[`eaefd90128`](https://github.com/nodejs/node/commit/eaefd90128)] - **deps**: update ngtcp2 to 1.10.0 (Node.js GitHub Bot) [#56334](https://github.com/nodejs/node/pull/56334)
- \[[`06de0c65cf`](https://github.com/nodejs/node/commit/06de0c65cf)] - **deps**: update simdutf to 5.7.0 (Node.js GitHub Bot) [#56332](https://github.com/nodejs/node/pull/56332)
- \[[`03df76cdec`](https://github.com/nodejs/node/commit/03df76cdec)] - **doc**: add example for piping ReadableStream (Gabriel Schulhof) [#56415](https://github.com/nodejs/node/pull/56415)
- \[[`38ce249b07`](https://github.com/nodejs/node/commit/38ce249b07)] - **doc**: expand description of `parseArg`'s `default` (Kevin Gibbons) [#54431](https://github.com/nodejs/node/pull/54431)
- \[[`ecc718cef2`](https://github.com/nodejs/node/commit/ecc718cef2)] - **doc**: use `<ul>` instead of `<ol>` in `SECURITY.md` (Antoine du Hamel) [#56346](https://github.com/nodejs/node/pull/56346)
- \[[`3db4809130`](https://github.com/nodejs/node/commit/3db4809130)] - **doc**: clarify that WASM is trusted (Matteo Collina) [#56345](https://github.com/nodejs/node/pull/56345)
- \[[`384ccbacd5`](https://github.com/nodejs/node/commit/384ccbacd5)] - **doc**: update macOS and Xcode versions for releases (Michaël Zasso) [#56337](https://github.com/nodejs/node/pull/56337)
- \[[`3943986e88`](https://github.com/nodejs/node/commit/3943986e88)] - **doc**: fix the `crc32` documentation (Kevin Toshihiro Uehara) [#55898](https://github.com/nodejs/node/pull/55898)
- \[[`710b8fc6ed`](https://github.com/nodejs/node/commit/710b8fc6ed)] - **doc**: add entry to changelog about SQLite Session Extension (Bart Louwers) [#56318](https://github.com/nodejs/node/pull/56318)
- \[[`4c978b4d77`](https://github.com/nodejs/node/commit/4c978b4d77)] - **doc**: fix links in `module.md` (Antoine du Hamel) [#56283](https://github.com/nodejs/node/pull/56283)
- \[[`cdb631efe7`](https://github.com/nodejs/node/commit/cdb631efe7)] - **esm**: add experimental support for addon modules (Chengzhong Wu) [#55844](https://github.com/nodejs/node/pull/55844)
- \[[`db83d2f0ee`](https://github.com/nodejs/node/commit/db83d2f0ee)] - _**Revert**_ "**events**: add hasEventListener util for validate" (origranot) [#56282](https://github.com/nodejs/node/pull/56282)
- \[[`c2baae84ce`](https://github.com/nodejs/node/commit/c2baae84ce)] - **lib**: refactor execution.js (Marco Ippolito) [#56358](https://github.com/nodejs/node/pull/56358)
- \[[`c1023284c3`](https://github.com/nodejs/node/commit/c1023284c3)] - **(SEMVER-MINOR)** **lib**: add typescript support to STDIN eval (Marco Ippolito) [#56359](https://github.com/nodejs/node/pull/56359)
- \[[`e4b795ec4a`](https://github.com/nodejs/node/commit/e4b795ec4a)] - **lib**: optimize `prepareStackTrace` on builtin frames (Chengzhong Wu) [#56299](https://github.com/nodejs/node/pull/56299)
- \[[`d1b009b623`](https://github.com/nodejs/node/commit/d1b009b623)] - **lib**: suppress source map lookup exceptions (Chengzhong Wu) [#56299](https://github.com/nodejs/node/pull/56299)
- \[[`c2837f0805`](https://github.com/nodejs/node/commit/c2837f0805)] - **meta**: move one or more collaborators to emeritus (Node.js GitHub Bot) [#56342](https://github.com/nodejs/node/pull/56342)
- \[[`72336233f2`](https://github.com/nodejs/node/commit/72336233f2)] - **meta**: move MoLow to TSC regular member (Moshe Atlow) [#56276](https://github.com/nodejs/node/pull/56276)
- \[[`4f77920a9d`](https://github.com/nodejs/node/commit/4f77920a9d)] - **module**: fix async resolution error within the sync `findPackageJSON` (Jacob Smith) [#56382](https://github.com/nodejs/node/pull/56382)
- \[[`e5ba216501`](https://github.com/nodejs/node/commit/e5ba216501)] - **(SEMVER-MINOR)** **module**: unflag --experimental-strip-types (Marco Ippolito) [#56350](https://github.com/nodejs/node/pull/56350)
- \[[`959f133a22`](https://github.com/nodejs/node/commit/959f133a22)] - **module**: support eval with ts syntax detection (Marco Ippolito) [#56285](https://github.com/nodejs/node/pull/56285)
- \[[`717cfa4fac`](https://github.com/nodejs/node/commit/717cfa4fac)] - **module**: use buffer.toString base64 (Chengzhong Wu) [#56315](https://github.com/nodejs/node/pull/56315)
- \[[`c2f4d8d688`](https://github.com/nodejs/node/commit/c2f4d8d688)] - **node-api**: define version 10 (Gabriel Schulhof) [#55676](https://github.com/nodejs/node/pull/55676)
- \[[`417a8ebdec`](https://github.com/nodejs/node/commit/417a8ebdec)] - **node-api**: remove deprecated attribute from napi_module_register (Vladimir Morozov) [#56162](https://github.com/nodejs/node/pull/56162)
- \[[`8dc39e5e2e`](https://github.com/nodejs/node/commit/8dc39e5e2e)] - **(SEMVER-MINOR)** **process**: add process.ref() and process.unref() methods (James M Snell) [#56400](https://github.com/nodejs/node/pull/56400)
- \[[`d194f1ab5f`](https://github.com/nodejs/node/commit/d194f1ab5f)] - **sqlite**: pass conflict type to conflict resolution handler (Bart Louwers) [#56352](https://github.com/nodejs/node/pull/56352)
- \[[`29f5d70452`](https://github.com/nodejs/node/commit/29f5d70452)] - **src**: use v8::LocalVector consistently with other minor cleanups (James M Snell) [#56417](https://github.com/nodejs/node/pull/56417)
- \[[`2a5543b78e`](https://github.com/nodejs/node/commit/2a5543b78e)] - **src**: use starts_with in fs_permission.cc (ishabi) [#55811](https://github.com/nodejs/node/pull/55811)
- \[[`3a3f5c9a64`](https://github.com/nodejs/node/commit/3a3f5c9a64)] - **stream**: validate undefined sizeAlgorithm in WritableStream (Jason Zhang) [#56067](https://github.com/nodejs/node/pull/56067)
- \[[`6e6f6b071a`](https://github.com/nodejs/node/commit/6e6f6b071a)] - **test**: add ts eval snapshots (Marco Ippolito) [#56358](https://github.com/nodejs/node/pull/56358)
- \[[`8a87e39052`](https://github.com/nodejs/node/commit/8a87e39052)] - **test**: remove empty lines from snapshots (Marco Ippolito) [#56358](https://github.com/nodejs/node/pull/56358)
- \[[`510649f617`](https://github.com/nodejs/node/commit/510649f617)] - **test**: use unusual chars in the path to ensure our tests are robust (Antoine du Hamel) [#48409](https://github.com/nodejs/node/pull/48409)
- \[[`54f6d681a0`](https://github.com/nodejs/node/commit/54f6d681a0)] - **test**: remove flaky designation (Luigi Pinca) [#56369](https://github.com/nodejs/node/pull/56369)
- \[[`20ace0bb01`](https://github.com/nodejs/node/commit/20ace0bb01)] - **test**: remove test-worker-arraybuffer-zerofill flaky designation (Luigi Pinca) [#56364](https://github.com/nodejs/node/pull/56364)
- \[[`b757e40525`](https://github.com/nodejs/node/commit/b757e40525)] - **test**: remove test-net-write-fully-async-hex-string flaky designation (Luigi Pinca) [#56365](https://github.com/nodejs/node/pull/56365)
- \[[`64556baddc`](https://github.com/nodejs/node/commit/64556baddc)] - **test**: improve abort signal dropping test (Edy Silva) [#56339](https://github.com/nodejs/node/pull/56339)
- \[[`accbdad329`](https://github.com/nodejs/node/commit/accbdad329)] - **test**: enable ts test on win arm64 (Marco Ippolito) [#56349](https://github.com/nodejs/node/pull/56349)
- \[[`4188ee00d1`](https://github.com/nodejs/node/commit/4188ee00d1)] - **test**: deflake test-watch-file-shared-dependency (Luigi Pinca) [#56344](https://github.com/nodejs/node/pull/56344)
- \[[`079cee0609`](https://github.com/nodejs/node/commit/079cee0609)] - **test**: skip `test-sqlite-extensions` when SQLite is not built by us (Antoine du Hamel) [#56341](https://github.com/nodejs/node/pull/56341)
- \[[`96a38044ee`](https://github.com/nodejs/node/commit/96a38044ee)] - **test**: increase spin for eventloop test on s390 (Michael Dawson) [#56228](https://github.com/nodejs/node/pull/56228)
- \[[`c062ffc242`](https://github.com/nodejs/node/commit/c062ffc242)] - **test**: add coverage for pipeline (jakecastelli) [#56278](https://github.com/nodejs/node/pull/56278)
- \[[`d4404f0d0e`](https://github.com/nodejs/node/commit/d4404f0d0e)] - **test**: migrate message eval tests from Python to JS (Yiyun Lei) [#50482](https://github.com/nodejs/node/pull/50482)
- \[[`9369942745`](https://github.com/nodejs/node/commit/9369942745)] - **test**: check typescript loader (Marco Ippolito) [#54657](https://github.com/nodejs/node/pull/54657)
- \[[`4930244484`](https://github.com/nodejs/node/commit/4930244484)] - **test**: remove async-hooks/test-writewrap flaky designation (Luigi Pinca) [#56048](https://github.com/nodejs/node/pull/56048)
- \[[`7819bfec69`](https://github.com/nodejs/node/commit/7819bfec69)] - **test**: deflake test-esm-loader-hooks-inspect-brk (Luigi Pinca) [#56050](https://github.com/nodejs/node/pull/56050)
- \[[`e9762bf005`](https://github.com/nodejs/node/commit/e9762bf005)] - **test**: add test case for listeners (origranot) [#56282](https://github.com/nodejs/node/pull/56282)
- \[[`c1627e9d19`](https://github.com/nodejs/node/commit/c1627e9d19)] - **test**: make `test-permission-sqlite-load-extension` more robust (Antoine du Hamel) [#56295](https://github.com/nodejs/node/pull/56295)
- \[[`97d854e1d5`](https://github.com/nodejs/node/commit/97d854e1d5)] - **test_runner,cli**: mark test isolation as stable (Colin Ihrig) [#56298](https://github.com/nodejs/node/pull/56298)
- \[[`a4f336fdd4`](https://github.com/nodejs/node/commit/a4f336fdd4)] - **tools**: fix `require-common-first` lint rule from subfolder (Antoine du Hamel) [#56325](https://github.com/nodejs/node/pull/56325)
- \[[`dc3dafcb50`](https://github.com/nodejs/node/commit/dc3dafcb50)] - **tools**: add release line label when opening release proposal (Antoine du Hamel) [#56317](https://github.com/nodejs/node/pull/56317)
- \[[`2a5ac932ac`](https://github.com/nodejs/node/commit/2a5ac932ac)] - **url**: use resolved path to convert UNC paths to URL (Antoine du Hamel) [#56302](https://github.com/nodejs/node/pull/56302)
- \[[`8b20cc212b`](https://github.com/nodejs/node/commit/8b20cc212b)] - **(SEMVER-MINOR)** **worker**: add eval ts input (Marco Ippolito) [#56394](https://github.com/nodejs/node/pull/56394)

Windows 64-bit Installer: https://nodejs.org/dist/v23.6.0/node-v23.6.0-x64.msi \
Windows ARM 64-bit Installer: https://nodejs.org/dist/v23.6.0/node-v23.6.0-arm64.msi \
Windows 64-bit Binary: https://nodejs.org/dist/v23.6.0/win-x64/node.exe \
Windows ARM 64-bit Binary: https://nodejs.org/dist/v23.6.0/win-arm64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v23.6.0/node-v23.6.0.pkg \
macOS Apple Silicon 64-bit Binary: https://nodejs.org/dist/v23.6.0/node-v23.6.0-darwin-arm64.tar.gz \
macOS Intel 64-bit Binary: https://nodejs.org/dist/v23.6.0/node-v23.6.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v23.6.0/node-v23.6.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v23.6.0/node-v23.6.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v23.6.0/node-v23.6.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v23.6.0/node-v23.6.0-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v23.6.0/node-v23.6.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v23.6.0/node-v23.6.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v23.6.0/node-v23.6.0.tar.gz \
Other release files: https://nodejs.org/dist/v23.6.0/ \
Documentation: https://nodejs.org/docs/v23.6.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

6320c05068ec7d34997d11a343eab0360ff7c59711c69116446056918c868b45  node-v23.6.0-aix-ppc64.tar.gz
d45c16ed0206db0fa92d8ca65d60797140f8bfa2a9e454e41cec6a4f0ba957a6  node-v23.6.0-arm64.msi
93e84485e41e7f35246e11329ea920ee5a8e7e12e90bfcea2f8205953c869bc2  node-v23.6.0-darwin-arm64.tar.gz
764857a0ab3cb956f16baeb05f89007d9ee1d9daa70c98d45005da0a35e769a0  node-v23.6.0-darwin-arm64.tar.xz
009f4b4955ddbebaad86e306ad4c65b568f06fd76d855e7fd617eb2748cd5f2d  node-v23.6.0-darwin-x64.tar.gz
ebc91565c93053b4fd9d04770960752328091b40df123c8ef8a7d4c61ae05934  node-v23.6.0-darwin-x64.tar.xz
f0c76e11bfbc9ca671a726c212946f8be3ba5608ecc5eff75032cb25fbf2a6a3  node-v23.6.0-headers.tar.gz
d748448b26bba978acd274eb0a04c8e883d4fbc156dbc6b7ea5b6dedd0e6a74f  node-v23.6.0-headers.tar.xz
f851e86f5022949a02329230fd1cf374a7485bf8826596b090c4c950698b9e0d  node-v23.6.0-linux-arm64.tar.gz
7554f6ed6171d0e25938978a67868cadb6eed6f0393ed72b6aaf8f1195028ec2  node-v23.6.0-linux-arm64.tar.xz
60a319b55afa11c646066a6c1f1a368d586e40bd27fa38c855acccae7f262065  node-v23.6.0-linux-armv7l.tar.gz
c2f09d6f7f7d1c7d0a9fa489bf892405c3edd7edefdff3e864584cf23d869ac4  node-v23.6.0-linux-armv7l.tar.xz
6fcf84abbd9fa95856664e3cbd28b8359dde7d46b6831dc8d240184285fea93a  node-v23.6.0-linux-ppc64le.tar.gz
9b1ce4f04edd33f9cc356ca5af54eb1264b3f4a1de58c246c41d34518df16903  node-v23.6.0-linux-ppc64le.tar.xz
3d3a5fe270de4f0e5f3b3c4a9023d9fdbf3c269656e37c4b52c17f2ad67f547c  node-v23.6.0-linux-s390x.tar.gz
3cfd38f7ee8578d9c1358a86e648ea8084c5f6afc379b4bd4c5676275f07794e  node-v23.6.0-linux-s390x.tar.xz
14a2b49629e74820bd6d94b92498f658774e3819bb71b0cb8b3717d9c0c81b70  node-v23.6.0-linux-x64.tar.gz
90e3c96e2464978e8309db2e8bb7c5c1b606f85afa80314195f01c30eccf4ffc  node-v23.6.0-linux-x64.tar.xz
cfe71d64ded340cae85cc734645a4aacab2355186ec3a45f43953f2af71ad859  node-v23.6.0.pkg
9cd2aaa44d7d658833bb03ab4ce4c57273ba4c9b716094c931c709e6948fd71d  node-v23.6.0.tar.gz
d6f3f136dc26d61bd1bafae2a9fc0d5e7f713a6f0067c5e10351bc7ca6574dcd  node-v23.6.0.tar.xz
f3cbe62de5d09e95ec574af1b4004da34590c8c32720057b8fd3d5dfbd4de0b2  node-v23.6.0-win-arm64.7z
e553f0841582570875b667aaa0bd9b94c37e558c909cab9505a85db23f3a7c65  node-v23.6.0-win-arm64.zip
f68d5ac65b8b5607eb5cc613c7b70c5bae09cad827e5a9c35beacfbeace92911  node-v23.6.0-win-x64.7z
9daeb5894273b820fb3bf2485aa433ff9653feb2c1a3daebd1a06b0e4fbe4309  node-v23.6.0-win-x64.zip
d6113ec6743a5da957c9cd47189942c5e7d5a2304d1e12e3cdf255f1cfb84d43  node-v23.6.0-x64.msi
f580fef0a0b05a0c191196c6dbca1b46b6eda8081b0be20f4db205048de26a7a  win-arm64/node.exe
46a85ee5432885387948e02ced2499975b34cc2080ea3ed4e99b6c7323d38d74  win-arm64/node.lib
bd85da81c92e27372a1a1aff63f3950407db919ad7a3fcdb176ce66bbd5faecd  win-arm64/node_pdb.7z
466beaa7ec64d742340711c04f936f2ec49af16aaf15bcc85a62ba416e181620  win-arm64/node_pdb.zip
6436502ba62375ca9bc30840428145bc9e34f21d80129d2684c9e42277b10067  win-x64/node.exe
4a54d5aeab30bb14dbad9260f6430c81f1e1afb430c68c40f6e5fec7ce288e2e  win-x64/node.lib
409233a718d91323ca0c1e7d356510ce06d10ddbc7442158d411053e3ffd99e2  win-x64/node_pdb.7z
bd10fad5a054735243b113dccb41ed7c94878f15f188356fbedd5db2ad561fa2  win-x64/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEzGj1oxBv9EgyLkjtJ/XjjVsKIV8FAmd9ZJAACgkQJ/XjjVsK
IV9w6w//TDx7zCLA3y59coOIujjdhW3H0azpcCtKXT5KnjlajUKBg/FNwXbNg8ja
plzm2/C0UkZmoJFkF7rX+Lmb9SY3QfuWdFYJsX1akX5fQuh7bKG3neupWOcS+WF8
oJk2a2QaGhZFVCXCYI8JNKWtMtAxfgeop/sTlBDw3iqgdPGNFxUbv8ukv89lHMrK
rWKxd3DIglugRPPWN4LA1bKvpSONhSKOrjV+ct3WY1DY/tSFFpgIRyqOXIJ+LBuk
vSlbjZLfPpeFYUlPC9pIXOkLaWFb4s+Cjm164z76+YQb5lC+EgTpDoJGGQ3qn/Ux
Gj1KPiFb1WTKJ0IcLl6xDx7lHnmknVKxchNGEHpp7tZGdWt23Gjty/pbDBEiGCuo
CxMS7ATx7vOMMDBsvtlCnDx3d3bVDXbfrvO4SiDwjChm3YoaAQqzz1Q4eN6HKxDH
Ca8kpOW+nVBLPz7DLIfIc/+QpNquEIAwSTX2DKdGVA5xYQkXYaPVrQOiaIAp33jB
sMMlTqNWqpIB+mHLyW5VfwKT4vGfAG+0ZJSYDxRa99hhu+nuvjEV9syc8xyRtOXw
hw1MAwuRDMeo8W7lK0602RWEo+GHz3VmWdX9ft7dZ2FIziyIG/rFw6aP8tFE2ETJ
ZlwU3KdrCyaqmjitSBLlPL7vOB3z4I2QohUgkAHYzoH8sRo7Jps=
=5/XI
-----END PGP SIGNATURE-----
```
