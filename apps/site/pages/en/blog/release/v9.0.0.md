---
date: '2017-10-31T20:03:44.706Z'
category: release
title: Node v9.0.0 (Current)
layout: blog-post
author: <PERSON>
---

### Notable Changes

- **Async hooks**

  - Older experimental APIs have been removed. [[`d731369b1d`](https://github.com/nodejs/node/commit/d731369b1d)] [#14414](https://github.com/nodejs/node/pull/14414)

- **Errors**

  - Improvements have been made to `buffer` module error messages. [[`9e0f771224`](https://github.com/nodejs/node/commit/9e0f771224)] [#14975](https://github.com/nodejs/node/pull/14975)
  - The assignment of static error codes to Node.js error continues:
    - `buffer`: [[`e79a61cf80`](https://github.com/nodejs/node/commit/e79a61cf80)] [#16352](https://github.com/nodejs/node/pull/16352), [[`dbfe8c4ea2`](https://github.com/nodejs/node/commit/dbfe8c4ea2)] [#13976](https://github.com/nodejs/node/pull/13976)
    - `child_process`: [[`fe730d34ce`](https://github.com/nodejs/node/commit/fe730d34ce)] [#14009](https://github.com/nodejs/node/pull/14009)
    - `console`: [[`0ecdf29340`](https://github.com/nodejs/node/commit/0ecdf29340)] [#11340](https://github.com/nodejs/node/pull/11340)
    - `crypto`: [[`ee76f3153b`](https://github.com/nodejs/node/commit/ee76f3153b)] [#16428](https://github.com/nodejs/node/pull/16428), [[`df8c6c3651`](https://github.com/nodejs/node/commit/df8c6c3651)] [#16453](https://github.com/nodejs/node/pull/16453), [[`0a03e350fb`](https://github.com/nodejs/node/commit/0a03e350fb)] [#16454](https://github.com/nodejs/node/pull/16454), [[`eeada6ca63`](https://github.com/nodejs/node/commit/eeada6ca63)] [#16448](https://github.com/nodejs/node/pull/16448), [[`a78327f48b`](https://github.com/nodejs/node/commit/a78327f48b)] [#16429](https://github.com/nodejs/node/pull/16429), [[`b8bc652869`](https://github.com/nodejs/node/commit/b8bc652869)] [#15757](https://github.com/nodejs/node/pull/15757), [[`7124b466d9`](https://github.com/nodejs/node/commit/7124b466d9)] [#15746](https://github.com/nodejs/node/pull/15746), [[`3ddc88b5c2`](https://github.com/nodejs/node/commit/3ddc88b5c2)] [#15756](https://github.com/nodejs/node/pull/15756)
    - `dns`: [[`9cb390d899`](https://github.com/nodejs/node/commit/9cb390d899)] [#14212](https://github.com/nodejs/node/pull/14212)
    - `events`: [[`e5ad5456a2`](https://github.com/nodejs/node/commit/e5ad5456a2)] [#15623](https://github.com/nodejs/node/pull/15623)
    - `fs`: [[`219932a9f7`](https://github.com/nodejs/node/commit/219932a9f7)] [#15043](https://github.com/nodejs/node/pull/15043), [[`b61cab2234`](https://github.com/nodejs/node/commit/b61cab2234)] [#11317](https://github.com/nodejs/node/pull/11317)
    - `http`: [[`11a2ca29ba`](https://github.com/nodejs/node/commit/11a2ca29ba)] [#14735](https://github.com/nodejs/node/pull/14735), [[`a9f798ebcc`](https://github.com/nodejs/node/commit/a9f798ebcc)] [#13301](https://github.com/nodejs/node/pull/13301), [[`bdfbce9241`](https://github.com/nodejs/node/commit/bdfbce9241)] [#14423](https://github.com/nodejs/node/pull/14423), [[`4843c2f415`](https://github.com/nodejs/node/commit/4843c2f415)] [#15603](https://github.com/nodejs/node/pull/15603)
    - `inspector`: [[`4cf56ad6f2`](https://github.com/nodejs/node/commit/4cf56ad6f2)] [#15619](https://github.com/nodejs/node/pull/15619)
    - `net`: [[`a03d8cee1f`](https://github.com/nodejs/node/commit/a03d8cee1f)] [#11356](https://github.com/nodejs/node/pull/11356), [[`7f55349079`](https://github.com/nodejs/node/commit/7f55349079)] [#14782](https://github.com/nodejs/node/pull/14782)
    - `path`: [[`dcfbbacba8`](https://github.com/nodejs/node/commit/dcfbbacba8)] [#11319](https://github.com/nodejs/node/pull/11319)
    - `process`: [[`a0f7284346`](https://github.com/nodejs/node/commit/a0f7284346)] [#13739](https://github.com/nodejs/node/pull/13739), [[`062071a9c3`](https://github.com/nodejs/node/commit/062071a9c3)] [#13285](https://github.com/nodejs/node/pull/13285), [[`3129b2c035`](https://github.com/nodejs/node/commit/3129b2c035)] [#13982](https://github.com/nodejs/node/pull/13982)
    - `querystring`: [[`9788e96836`](https://github.com/nodejs/node/commit/9788e96836)] [#15565](https://github.com/nodejs/node/pull/15565)
    - `readline`: [[`7f3f72c19b`](https://github.com/nodejs/node/commit/7f3f72c19b)] [#11390](https://github.com/nodejs/node/pull/11390)
    - `repl`: [[`aff8d358fa`](https://github.com/nodejs/node/commit/aff8d358fa)] [#11347](https://github.com/nodejs/node/pull/11347), [[`28227963fa`](https://github.com/nodejs/node/commit/28227963fa)] [#13299](https://github.com/nodejs/node/pull/13299)
    - `streams`: [[`d50a802feb`](https://github.com/nodejs/node/commit/d50a802feb)] [#13310](https://github.com/nodejs/node/pull/13310), [[`d2913384aa`](https://github.com/nodejs/node/commit/d2913384aa)] [#13291](https://github.com/nodejs/node/pull/13291), [[`6e86a6651c`](https://github.com/nodejs/node/commit/6e86a6651c)] [#16589](https://github.com/nodejs/node/pull/16589), [[`88fb359c57`](https://github.com/nodejs/node/commit/88fb359c57)] [#15042](https://github.com/nodejs/node/pull/15042), [[`db7d1339c3`](https://github.com/nodejs/node/commit/db7d1339c3)] [#15665](https://github.com/nodejs/node/pull/15665)
    - `string_decoder`: [[`eb4940e2d2`](https://github.com/nodejs/node/commit/eb4940e2d2)] [#14682](https://github.com/nodejs/node/pull/14682)
    - `timers`: [[`4d893e093a`](https://github.com/nodejs/node/commit/4d893e093a)] [#14659](https://github.com/nodejs/node/pull/14659)
    - `tls`: [[`f67aa566a6`](https://github.com/nodejs/node/commit/f67aa566a6)] [#13476](https://github.com/nodejs/node/pull/13476), [[`3ccfeb483d`](https://github.com/nodejs/node/commit/3ccfeb483d)] [#13994](https://github.com/nodejs/node/pull/13994)
    - `url`: [[`473f0eff29`](https://github.com/nodejs/node/commit/473f0eff29)] [#13963](https://github.com/nodejs/node/pull/13963)
    - `util`: [[`de4a749788`](https://github.com/nodejs/node/commit/de4a749788)] [#11301](https://github.com/nodejs/node/pull/11301), [[`1609899142`](https://github.com/nodejs/node/commit/1609899142)] [#13293](https://github.com/nodejs/node/pull/13293)
    - `v8`: [[`ef238fb485`](https://github.com/nodejs/node/commit/ef238fb485)] [#16535](https://github.com/nodejs/node/pull/16535)
    - `zlib`: [[`896eaf6820`](https://github.com/nodejs/node/commit/896eaf6820)] [#16540](https://github.com/nodejs/node/pull/16540), [[`74891412f1`](https://github.com/nodejs/node/commit/74891412f1)] [#15618](https://github.com/nodejs/node/pull/15618)

- **Child Processes**

  - Errors are emitted on process nextTick. [[`f2b01cba7b`](https://github.com/nodejs/node/commit/f2b01cba7b)] [#4670](https://github.com/nodejs/node/pull/4670)

- **Domains**

  - The long-deprecated `.dispose()` method has been removed [[`602fd36d95`](https://github.com/nodejs/node/commit/602fd36d95)] [#15412](https://github.com/nodejs/node/pull/15412)

- **fs**

  - The `fs.ReadStream` and `fs.WriteStream` classes now use `destroy()`. [[`e5c290bed9`](https://github.com/nodejs/node/commit/e5c290bed9)] [#15407](https://github.com/nodejs/node/pull/15407)
  - `fs` module callbacks are now invoked with an undefined context. [[`2249234fee`](https://github.com/nodejs/node/commit/2249234fee)] [#14645](https://github.com/nodejs/node/pull/14645)

- **HTTP/1**

  - A 400 Bad Request response will now be sent when parsing fails. [[`f2f391e575`](https://github.com/nodejs/node/commit/f2f391e575)] [#15324](https://github.com/nodejs/node/pull/15324)
  - Socket timeout will be set when the socket connects. [[`10be20a0e8`](https://github.com/nodejs/node/commit/10be20a0e8)] [#8895](https://github.com/nodejs/node/pull/8895)
  - A bug causing the request `'error'` event to fire twice was fixed. [[`620ba41694`](https://github.com/nodejs/node/commit/620ba41694)] [#14659](https://github.com/nodejs/node/pull/14659)
  - HTTP clients may now use generic `Duplex` streams in addition to `net.Socket`. [[`3e25e4d00f`](https://github.com/nodejs/node/commit/3e25e4d00f)] [#16267](https://github.com/nodejs/node/pull/16267)

- **Intl**

  - The deprecated `Intl.v8BreakIterator` has been removed. [[`668ad44922`](https://github.com/nodejs/node/commit/668ad44922)] [#15238](https://github.com/nodejs/node/pull/15238)

- **OS**

  - The `os.EOL` property is now read-only [[`f6caeb9526`](https://github.com/nodejs/node/commit/f6caeb9526)] [#14622](https://github.com/nodejs/node/pull/14622)

- **Timers**
  - `setTimeout()` will emit a warning if the timeout is larger than the maximum 32-bit unsigned integer. [[`ce3586da31`](https://github.com/nodejs/node/commit/ce3586da31)] [#15627](https://github.com/nodejs/node/pull/15627)

### Commits

#### Semver-Major

- [[`de4a749788`](https://github.com/nodejs/node/commit/de4a749788)] - **(SEMVER-MAJOR)** internal/util: use internal/errors.js (Sebastian Van Sande) [#11301](https://github.com/nodejs/node/pull/11301)
- [[`db2e093e05`](https://github.com/nodejs/node/commit/db2e093e05)] - **(SEMVER-MAJOR)** **assert**: handle enumerable symbol keys (Ruben Bridgewater) [#15169](https://github.com/nodejs/node/pull/15169)
- [[`b0d3bec95c`](https://github.com/nodejs/node/commit/b0d3bec95c)] - **(SEMVER-MAJOR)** **assert**: use Same-value equality in deepStrictEqual (Ruben Bridgewater) [#15398](https://github.com/nodejs/node/pull/15398)
- [[`e13d1df89b`](https://github.com/nodejs/node/commit/e13d1df89b)] - **(SEMVER-MAJOR)** **assert**: support custom errors (geek) [#15304](https://github.com/nodejs/node/pull/15304)
- [[`ea2e6363f2`](https://github.com/nodejs/node/commit/ea2e6363f2)] - **(SEMVER-MAJOR)** **assert**: use SameValueZero in deepStrictEqual (Ruben Bridgewater) [#15036](https://github.com/nodejs/node/pull/15036)
- [[`c53db1e8e9`](https://github.com/nodejs/node/commit/c53db1e8e9)] - **(SEMVER-MAJOR)** **assert**: show thrown message in doesNotThrow() (Ruslan Bekenev) [#12167](https://github.com/nodejs/node/pull/12167)
- [[`fc463639fa`](https://github.com/nodejs/node/commit/fc463639fa)] - **(SEMVER-MAJOR)** **assert**: fix assert.fail with zero arguments (Ruben Bridgewater) [#13974](https://github.com/nodejs/node/pull/13974)
- [[`07d71c94ef`](https://github.com/nodejs/node/commit/07d71c94ef)] - **(SEMVER-MAJOR)** **async_hooks**: enable runtime checks by default (Andreas Madsen) [#16318](https://github.com/nodejs/node/pull/16318)
- [[`d731369b1d`](https://github.com/nodejs/node/commit/d731369b1d)] - **(SEMVER-MAJOR)** **async_hooks**: remove deprecated APIs (Anna Henningsen) [#14414](https://github.com/nodejs/node/pull/14414)
- [[`97c43940c8`](https://github.com/nodejs/node/commit/97c43940c8)] - **(SEMVER-MAJOR)** **benchmark**: cover more nextTick() code (Rich Trott) [#14645](https://github.com/nodejs/node/pull/14645)
- [[`e79a61cf80`](https://github.com/nodejs/node/commit/e79a61cf80)] - **(SEMVER-MAJOR)** **buffer**: buffer.transcode to use internal/errors (Weijia Wang) [#16352](https://github.com/nodejs/node/pull/16352)
- [[`9e0f771224`](https://github.com/nodejs/node/commit/9e0f771224)] - **(SEMVER-MAJOR)** **buffer**: improve error messages (Weijia Wang) [#14975](https://github.com/nodejs/node/pull/14975)
- [[`70832bc353`](https://github.com/nodejs/node/commit/70832bc353)] - **(SEMVER-MAJOR)** **build**: add V8 embedder version string (Michaël Zasso) [#15785](https://github.com/nodejs/node/pull/15785)
- [[`c5eb5bfc2e`](https://github.com/nodejs/node/commit/c5eb5bfc2e)] - **(SEMVER-MAJOR)** **build**: enable runtime linking (jBarz) [#15286](https://github.com/nodejs/node/pull/15286)
- [[`2062a69879`](https://github.com/nodejs/node/commit/2062a69879)] - **(SEMVER-MAJOR)** **build**: stop support building addons with VS 2013 (Michaël Zasso) [#14764](https://github.com/nodejs/node/pull/14764)
- [[`f2b01cba7b`](https://github.com/nodejs/node/commit/f2b01cba7b)] - **(SEMVER-MAJOR)** **child_process**: defer error to next tick (Tristian Flanagan) [#4670](https://github.com/nodejs/node/pull/4670)
- [[`fe730d34ce`](https://github.com/nodejs/node/commit/fe730d34ce)] - **(SEMVER-MAJOR)** **child_process**: use internal/errors (Tobias Nießen) [#14009](https://github.com/nodejs/node/pull/14009)
- [[`448c4c62d2`](https://github.com/nodejs/node/commit/448c4c62d2)] - **(SEMVER-MAJOR)** **child_process**: do not extend result for \*Sync() (Brian White) [#13601](https://github.com/nodejs/node/pull/13601)
- [[`1fcb76e8f2`](https://github.com/nodejs/node/commit/1fcb76e8f2)] - **(SEMVER-MAJOR)** **cluster**: remove deprecated property (James M Snell) [#13702](https://github.com/nodejs/node/pull/13702)
- [[`4da8b99a74`](https://github.com/nodejs/node/commit/4da8b99a74)] - **(SEMVER-MAJOR)** **console**: coerce label to string in console.time() (James M Snell) [#14643](https://github.com/nodejs/node/pull/14643)
- [[`ee76f3153b`](https://github.com/nodejs/node/commit/ee76f3153b)] - **(SEMVER-MAJOR)** **crypto**: migrate setFipsCrypto to internal/errors (James M Snell) [#16428](https://github.com/nodejs/node/pull/16428)
- [[`df8c6c3651`](https://github.com/nodejs/node/commit/df8c6c3651)] - **(SEMVER-MAJOR)** **crypto**: use CHECK instead in getSSLCiphers (James M Snell) [#16453](https://github.com/nodejs/node/pull/16453)
- [[`0a03e350fb`](https://github.com/nodejs/node/commit/0a03e350fb)] - **(SEMVER-MAJOR)** **crypto**: migrate crypto.randomBytes to internal/errors (James M Snell) [#16454](https://github.com/nodejs/node/pull/16454)
- [[`eeada6ca63`](https://github.com/nodejs/node/commit/eeada6ca63)] - **(SEMVER-MAJOR)** **crypto**: migrate timingSafeEqual to internal/errors (James M Snell) [#16448](https://github.com/nodejs/node/pull/16448)
- [[`a78327f48b`](https://github.com/nodejs/node/commit/a78327f48b)] - **(SEMVER-MAJOR)** **crypto**: migrate setEngine to internal/errors (James M Snell) [#16429](https://github.com/nodejs/node/pull/16429)
- [[`b8bc652869`](https://github.com/nodejs/node/commit/b8bc652869)] - **(SEMVER-MAJOR)** **crypto**: migrate crypto sign to internal/errors (James M Snell) [#15757](https://github.com/nodejs/node/pull/15757)
- [[`7124b466d9`](https://github.com/nodejs/node/commit/7124b466d9)] - **(SEMVER-MAJOR)** **crypto**: refactor argument validation for pbkdf2 (James M Snell) [#15746](https://github.com/nodejs/node/pull/15746)
- [[`3ddc88b5c2`](https://github.com/nodejs/node/commit/3ddc88b5c2)] - **(SEMVER-MAJOR)** **crypto**: migrate Certificate to internal/errors (James M Snell) [#15756](https://github.com/nodejs/node/pull/15756)
- [[`c75f87cc4c`](https://github.com/nodejs/node/commit/c75f87cc4c)] - **(SEMVER-MAJOR)** **crypto**: refactor the crypto module (James M Snell) [#15231](https://github.com/nodejs/node/pull/15231)
- [[`484bfa2e37`](https://github.com/nodejs/node/commit/484bfa2e37)] - **(SEMVER-MAJOR)** **crypto**: accept decimal Number in randomBytes (Benjamin Gruenbaum) [#15130](https://github.com/nodejs/node/pull/15130)
- [[`c39caa997c`](https://github.com/nodejs/node/commit/c39caa997c)] - **(SEMVER-MAJOR)** **deps**: backport 0f1dfae from V8 upstream (Tobias Tebbi) [#15362](https://github.com/nodejs/node/pull/15362)
- [[`2780f01392`](https://github.com/nodejs/node/commit/2780f01392)] - **(SEMVER-MAJOR)** **deps**: backport b096c44 from upstream V8 (Michaël Zasso) [#15785](https://github.com/nodejs/node/pull/15785)
- [[`3d1b3df948`](https://github.com/nodejs/node/commit/3d1b3df948)] - **(SEMVER-MAJOR)** **deps**: update V8 to 6.2.414.32 (Michaël Zasso) [#15362](https://github.com/nodejs/node/pull/15362)
- [[`acb9b8f73c`](https://github.com/nodejs/node/commit/acb9b8f73c)] - **(SEMVER-MAJOR)** **deps**: backport b096c44 from upstream V8 (Michaël Zasso) [#15785](https://github.com/nodejs/node/pull/15785)
- [[`d82e1075db`](https://github.com/nodejs/node/commit/d82e1075db)] - **(SEMVER-MAJOR)** **deps**: update V8 to 6.1.534.36 (Michaël Zasso) [#14730](https://github.com/nodejs/node/pull/14730)
- [[`0a66b223e1`](https://github.com/nodejs/node/commit/0a66b223e1)] - **(SEMVER-MAJOR)** **deps**: update V8 to 6.0.286.52 (Myles Borins) [#14004](https://github.com/nodejs/node/pull/14004)
- [[`2db2857c72`](https://github.com/nodejs/node/commit/2db2857c72)] - **(SEMVER-MAJOR)** **deps**: cherry-pick 6d38f89 from upstream V8 (Michaël Zasso) [#13263](https://github.com/nodejs/node/pull/13263)
- [[`bc8e4878c0`](https://github.com/nodejs/node/commit/bc8e4878c0)] - **(SEMVER-MAJOR)** **deps**: add missing include to V8 i18n.cc (Michaël Zasso) [#13263](https://github.com/nodejs/node/pull/13263)
- [[`9b4a891ca2`](https://github.com/nodejs/node/commit/9b4a891ca2)] - **(SEMVER-MAJOR)** **deps**: run memory hungry V8 test in exclusive mode (Michaël Zasso) [#13263](https://github.com/nodejs/node/pull/13263)
- [[`3dc8c3bed4`](https://github.com/nodejs/node/commit/3dc8c3bed4)] - **(SEMVER-MAJOR)** **deps**: update V8 to 5.9.211.32 (Michaël Zasso) [#13263](https://github.com/nodejs/node/pull/13263)
- [[`1a452f1928`](https://github.com/nodejs/node/commit/1a452f1928)] - **(SEMVER-MAJOR)** **dgram,process,util**: refactor Error to TypeError (Ruben Bridgewater) [#13857](https://github.com/nodejs/node/pull/13857)
- [[`758a17f1d5`](https://github.com/nodejs/node/commit/758a17f1d5)] - **(SEMVER-MAJOR)** **dns**: return TypeError on invalid resolve() input (Rich Trott) [#13090](https://github.com/nodejs/node/pull/13090)
- [[`1789dcfc87`](https://github.com/nodejs/node/commit/1789dcfc87)] - **(SEMVER-MAJOR)** **doc**: add missing changelogs to assert docs (Ruben Bridgewater) [#15036](https://github.com/nodejs/node/pull/15036)
- [[`8ca9338655`](https://github.com/nodejs/node/commit/8ca9338655)] - **(SEMVER-MAJOR)** **doc**: document missing error types (Ruben Bridgewater) [#13857](https://github.com/nodejs/node/pull/13857)
- [[`3fab9f2cd7`](https://github.com/nodejs/node/commit/3fab9f2cd7)] - **(SEMVER-MAJOR)** **doc**: EOL deprecated API and update notes (James M Snell) [#13702](https://github.com/nodejs/node/pull/13702)
- [[`602fd36d95`](https://github.com/nodejs/node/commit/602fd36d95)] - **(SEMVER-MAJOR)** **domain**: remove `.dispose()` (Anna Henningsen) [#15412](https://github.com/nodejs/node/pull/15412)
- [[`219932a9f7`](https://github.com/nodejs/node/commit/219932a9f7)] - **(SEMVER-MAJOR)** **errors**: convert 'fs' (matzavinos) [#15043](https://github.com/nodejs/node/pull/15043)
- [[`11a2ca29ba`](https://github.com/nodejs/node/commit/11a2ca29ba)] - **(SEMVER-MAJOR)** **errors**: migrate \_http_outgoing (Weijia Wang) [#14735](https://github.com/nodejs/node/pull/14735)
- [[`9cb390d899`](https://github.com/nodejs/node/commit/9cb390d899)] - **(SEMVER-MAJOR)** **errors**: migrate dns to use internal/errors (Weijia Wang) [#14212](https://github.com/nodejs/node/pull/14212)
- [[`a03d8cee1f`](https://github.com/nodejs/node/commit/a03d8cee1f)] - **(SEMVER-MAJOR)** **errors**: migrate socket_list to internal/errors (Bougarfaoui El houcine) [#11356](https://github.com/nodejs/node/pull/11356)
- [[`f67aa566a6`](https://github.com/nodejs/node/commit/f67aa566a6)] - **(SEMVER-MAJOR)** **errors**: migrate tls_wrap to use internal/errors (Bidisha Pyne) [#13476](https://github.com/nodejs/node/pull/13476)
- [[`b61cab2234`](https://github.com/nodejs/node/commit/b61cab2234)] - **(SEMVER-MAJOR)** **errors**: port internal/fs errors to internal/errors (Gunar C. Gessner) [#11317](https://github.com/nodejs/node/pull/11317)
- [[`1698c8e165`](https://github.com/nodejs/node/commit/1698c8e165)] - **(SEMVER-MAJOR)** **errors**: fix and improve error types (Ruben Bridgewater) [#13857](https://github.com/nodejs/node/pull/13857)
- [[`3e178848a5`](https://github.com/nodejs/node/commit/3e178848a5)] - **(SEMVER-MAJOR)** **errors**: improve ERR_INVALID_ARG_TYPE (Ruben Bridgewater) [#13730](https://github.com/nodejs/node/pull/13730)
- [[`0ecdf29340`](https://github.com/nodejs/node/commit/0ecdf29340)] - **(SEMVER-MAJOR)** **errors**: migrate lib/console (mskec) [#11340](https://github.com/nodejs/node/pull/11340)
- [[`7f3f72c19b`](https://github.com/nodejs/node/commit/7f3f72c19b)] - **(SEMVER-MAJOR)** **errors, readline**: migrate to use internal/errors.js (Scott McKenzie) [#11390](https://github.com/nodejs/node/pull/11390)
- [[`aff8d358fa`](https://github.com/nodejs/node/commit/aff8d358fa)] - **(SEMVER-MAJOR)** **errors, repl**: migrate to use internal/errors.js (Dan Homola) [#11347](https://github.com/nodejs/node/pull/11347)
- [[`dbfe8c4ea2`](https://github.com/nodejs/node/commit/dbfe8c4ea2)] - **(SEMVER-MAJOR)** **errors,buffer**: port errors to internal/errors (starkwang) [#13976](https://github.com/nodejs/node/pull/13976)
- [[`a9f798ebcc`](https://github.com/nodejs/node/commit/a9f798ebcc)] - **(SEMVER-MAJOR)** **errors,http_server**: migrate to use internal/errors.js (Bidisha Pyne) [#13301](https://github.com/nodejs/node/pull/13301)
- [[`a0f7284346`](https://github.com/nodejs/node/commit/a0f7284346)] - **(SEMVER-MAJOR)** **errors,process**: fix error message of hrtime() (Tobias Nießen) [#13739](https://github.com/nodejs/node/pull/13739)
- [[`062071a9c3`](https://github.com/nodejs/node/commit/062071a9c3)] - **(SEMVER-MAJOR)** **errors,process**: migrate to use internal/errors.js (sreepurnajasti) [#13285](https://github.com/nodejs/node/pull/13285)
- [[`28227963fa`](https://github.com/nodejs/node/commit/28227963fa)] - **(SEMVER-MAJOR)** **errors,repl**: migrate to use internal/errors.js (sreepurnajasti) [#13299](https://github.com/nodejs/node/pull/13299)
- [[`d50a802feb`](https://github.com/nodejs/node/commit/d50a802feb)] - **(SEMVER-MAJOR)** **errors,stream-transform**: migrate to use internal/errors.js (sreepurnajasti) [#13310](https://github.com/nodejs/node/pull/13310)
- [[`d2913384aa`](https://github.com/nodejs/node/commit/d2913384aa)] - **(SEMVER-MAJOR)** **errors,stream_wrap**: use internal/errors.js (LAKSHMI SWETHA GOPIREDDY) [#13291](https://github.com/nodejs/node/pull/13291)
- [[`473f0eff29`](https://github.com/nodejs/node/commit/473f0eff29)] - **(SEMVER-MAJOR)** **errors,url**: port url errors to internal/errors (starkwang) [#13963](https://github.com/nodejs/node/pull/13963)
- [[`1609899142`](https://github.com/nodejs/node/commit/1609899142)] - **(SEMVER-MAJOR)** **errors,util**: migrate to use internal/errors.js (Bidisha Pyne) [#13293](https://github.com/nodejs/node/pull/13293)
- [[`e5ad5456a2`](https://github.com/nodejs/node/commit/e5ad5456a2)] - **(SEMVER-MAJOR)** **events**: migrate to internal/errors (James M Snell) [#15623](https://github.com/nodejs/node/pull/15623)
- [[`e5c290bed9`](https://github.com/nodejs/node/commit/e5c290bed9)] - **(SEMVER-MAJOR)** **fs**: refactor close to use destroy (Matteo Collina) [#15407](https://github.com/nodejs/node/pull/15407)
- [[`2249234fee`](https://github.com/nodejs/node/commit/2249234fee)] - **(SEMVER-MAJOR)** **fs**: invoke callbacks with undefined context (Rich Trott) [#14645](https://github.com/nodejs/node/pull/14645)
- [[`f2f391e575`](https://github.com/nodejs/node/commit/f2f391e575)] - **(SEMVER-MAJOR)** **http**: send 400 bad request on parse error (mog422) [#15324](https://github.com/nodejs/node/pull/15324)
- [[`10be20a0e8`](https://github.com/nodejs/node/commit/10be20a0e8)] - **(SEMVER-MAJOR)** **http**: set socket timeout when socket connects (Luigi Pinca) [#8895](https://github.com/nodejs/node/pull/8895)
- [[`620ba41694`](https://github.com/nodejs/node/commit/620ba41694)] - **(SEMVER-MAJOR)** **http**: don't double-fire the req error event (fengmk2) [#14659](https://github.com/nodejs/node/pull/14659)
- [[`156549d8ff`](https://github.com/nodejs/node/commit/156549d8ff)] - **(SEMVER-MAJOR)** **http**: disable OutgoingMessage pipe method (Roee Kasher) [#14358](https://github.com/nodejs/node/pull/14358)
- [[`2fa2a60721`](https://github.com/nodejs/node/commit/2fa2a60721)] - **(SEMVER-MAJOR)** **http**: simplify if statement (Ruben Bridgewater) [#13857](https://github.com/nodejs/node/pull/13857)
- [[`80c9ef0b6b`](https://github.com/nodejs/node/commit/80c9ef0b6b)] - **(SEMVER-MAJOR)** **http**: edit \_storeHeader to check for Trailer header (Artur G Vieira) [#12990](https://github.com/nodejs/node/pull/12990)
- [[`f55ee6e24a`](https://github.com/nodejs/node/commit/f55ee6e24a)] - **(SEMVER-MAJOR)** **http2**: make --expose-http2 flag a non-op (James M Snell) [#15535](https://github.com/nodejs/node/pull/15535)
- [[`bdfbce9241`](https://github.com/nodejs/node/commit/bdfbce9241)] - **(SEMVER-MAJOR)** **http_client, errors**: migrate to internal/errors (Weijia Wang) [#14423](https://github.com/nodejs/node/pull/14423)
- [[`4843c2f415`](https://github.com/nodejs/node/commit/4843c2f415)] - **(SEMVER-MAJOR)** **https**: convert to using internal/errors (Rami Moshe) [#15603](https://github.com/nodejs/node/pull/15603)
- [[`4cf56ad6f2`](https://github.com/nodejs/node/commit/4cf56ad6f2)] - **(SEMVER-MAJOR)** **inspector**: migrate to internal/errors (James M Snell) [#15619](https://github.com/nodejs/node/pull/15619)
- [[`668ad44922`](https://github.com/nodejs/node/commit/668ad44922)] - **(SEMVER-MAJOR)** **intl**: unexpose Intl.v8BreakIterator (Ben Noordhuis) [#15238](https://github.com/nodejs/node/pull/15238)
- [[`c885ea727d`](https://github.com/nodejs/node/commit/c885ea727d)] - **(SEMVER-MAJOR)** **lib**: deprecate fd usage for fs.truncate(Sync) (r1cebank) [#15990](https://github.com/nodejs/node/pull/15990)
- [[`095357e26e`](https://github.com/nodejs/node/commit/095357e26e)] - **(SEMVER-MAJOR)** **lib**: tweak use of internal/errors (Ruben Bridgewater) [#13829](https://github.com/nodejs/node/pull/13829)
- [[`8520e6f280`](https://github.com/nodejs/node/commit/8520e6f280)] - **(SEMVER-MAJOR)** **lib**: fix urlObject parameter name in url.format (Eduardo Leggiero) [#14031](https://github.com/nodejs/node/pull/14031)
- [[`9836cf5717`](https://github.com/nodejs/node/commit/9836cf5717)] - **(SEMVER-MAJOR)** **lib**: lazy instantiation of fs.Stats dates (Daniel Pihlstrom) [#12818](https://github.com/nodejs/node/pull/12818)
- [[`234353a1b8`](https://github.com/nodejs/node/commit/234353a1b8)] - **(SEMVER-MAJOR)** **lib,src**: refactor buffer out of range index (larissayvette) [#11296](https://github.com/nodejs/node/pull/11296)
- [[`9d7574eef5`](https://github.com/nodejs/node/commit/9d7574eef5)] - **(SEMVER-MAJOR)** **module**: deprecate Module.\_debug (Jackson Tian) [#13948](https://github.com/nodejs/node/pull/13948)
- [[`a517466aa7`](https://github.com/nodejs/node/commit/a517466aa7)] - **(SEMVER-MAJOR)** **module**: mark DEP0019 as EOL and remove compat code (Roman Reiss) [#3384](https://github.com/nodejs/node/pull/3384)
- [[`7f55349079`](https://github.com/nodejs/node/commit/7f55349079)] - **(SEMVER-MAJOR)** **net**: convert to using internal/errors (matzavinos) [#14782](https://github.com/nodejs/node/pull/14782)
- [[`b24e269a48`](https://github.com/nodejs/node/commit/b24e269a48)] - **(SEMVER-MAJOR)** **net**: multiple listen() events fail silently (Eduard Bondarenko) [#13149](https://github.com/nodejs/node/pull/13149)
- [[`75a19fb379`](https://github.com/nodejs/node/commit/75a19fb379)] - **(SEMVER-MAJOR)** **net,child_process**: improve naming in internal code (Anna Henningsen) [#14449](https://github.com/nodejs/node/pull/14449)
- [[`f6caeb9526`](https://github.com/nodejs/node/commit/f6caeb9526)] - **(SEMVER-MAJOR)** **os**: make EOL configurable and read only (XadillaX) [#14622](https://github.com/nodejs/node/pull/14622)
- [[`1f8d527e94`](https://github.com/nodejs/node/commit/1f8d527e94)] - **(SEMVER-MAJOR)** **path**: deprecate internal \_makeLong, replace (James M Snell) [#14956](https://github.com/nodejs/node/pull/14956)
- [[`dcfbbacba8`](https://github.com/nodejs/node/commit/dcfbbacba8)] - **(SEMVER-MAJOR)** **path**: use internal/errors.js (Sebastian Van Sande) [#11319](https://github.com/nodejs/node/pull/11319)
- [[`a253704446`](https://github.com/nodejs/node/commit/a253704446)] - **(SEMVER-MAJOR)** **process**: make `this` value consistent (Rich Trott) [#14645](https://github.com/nodejs/node/pull/14645)
- [[`43e105f645`](https://github.com/nodejs/node/commit/43e105f645)] - **(SEMVER-MAJOR)** **process**: improve hrtime() error message (Rich Trott) [#14324](https://github.com/nodejs/node/pull/14324)
- [[`3129b2c035`](https://github.com/nodejs/node/commit/3129b2c035)] - **(SEMVER-MAJOR)** **process**: use internal/errors in internalNextTick (Tobias Nießen) [#13982](https://github.com/nodejs/node/pull/13982)
- [[`9788e96836`](https://github.com/nodejs/node/commit/9788e96836)] - **(SEMVER-MAJOR)** **querystring**: convert to using internal/errors (Rami Moshe) [#15565](https://github.com/nodejs/node/pull/15565)
- [[`7a29f44071`](https://github.com/nodejs/node/commit/7a29f44071)] - **(SEMVER-MAJOR)** **repl**: deprecate REPLServer.prototype.memory (Lance Ball) [#16242](https://github.com/nodejs/node/pull/16242)
- [[`e416b3ee36`](https://github.com/nodejs/node/commit/e416b3ee36)] - **(SEMVER-MAJOR)** **repl**: deprecate turnOffEditorMode (Lance Ball) [#15136](https://github.com/nodejs/node/pull/15136)
- [[`ed1ba4580b`](https://github.com/nodejs/node/commit/ed1ba4580b)] - **(SEMVER-MAJOR)** **repl**: remove REPLServer.createContext side effects (Lance Ball) [#14331](https://github.com/nodejs/node/pull/14331)
- [[`2ca9f94e33`](https://github.com/nodejs/node/commit/2ca9f94e33)] - **(SEMVER-MAJOR)** **repl**: make REPLServer.bufferedCommand private (Lance Ball) [#13687](https://github.com/nodejs/node/pull/13687)
- [[`3d9e7bb1d4`](https://github.com/nodejs/node/commit/3d9e7bb1d4)] - **(SEMVER-MAJOR)** **repl**: remove unused function convertToContext (Nikolai Vavilov) [#13434](https://github.com/nodejs/node/pull/13434)
- [[`33b2b10b68`](https://github.com/nodejs/node/commit/33b2b10b68)] - **(SEMVER-MAJOR)** **src**: fix rename of entry frame in v8abbr.h (geek) [#15362](https://github.com/nodejs/node/pull/15362)
- [[`8f9e738a69`](https://github.com/nodejs/node/commit/8f9e738a69)] - **(SEMVER-MAJOR)** **src**: update ustack offset identifiers (geek) [#15362](https://github.com/nodejs/node/pull/15362)
- [[`205a4d2331`](https://github.com/nodejs/node/commit/205a4d2331)] - **(SEMVER-MAJOR)** **src**: update NODE_MODULE_VERSION to 59 (Michaël Zasso) [#15362](https://github.com/nodejs/node/pull/15362)
- [[`ddc16e505b`](https://github.com/nodejs/node/commit/ddc16e505b)] - **(SEMVER-MAJOR)** **src**: update NODE_MODULE_VERSION to 58 (Michaël Zasso) [#14730](https://github.com/nodejs/node/pull/14730)
- [[`5f22375922`](https://github.com/nodejs/node/commit/5f22375922)] - **(SEMVER-MAJOR)** **src**: add support to pass flags to dlopen (Ezequiel Garcia) [#12794](https://github.com/nodejs/node/pull/12794)
- [[`784c6d40f8`](https://github.com/nodejs/node/commit/784c6d40f8)] - **(SEMVER-MAJOR)** **src**: use proper errors as coming from StringBytes (Anna Henningsen) [#14579](https://github.com/nodejs/node/pull/14579)
- [[`80ebb4282d`](https://github.com/nodejs/node/commit/80ebb4282d)] - **(SEMVER-MAJOR)** **src**: adjust windows abort behavior (Jared Kantrowitz) [#13947](https://github.com/nodejs/node/pull/13947)
- [[`db476fc8b5`](https://github.com/nodejs/node/commit/db476fc8b5)] - **(SEMVER-MAJOR)** **src**: update NODE_MODULE_VERSION to 57 (Myles Borins) [#14004](https://github.com/nodejs/node/pull/14004)
- [[`24709b2e4a`](https://github.com/nodejs/node/commit/24709b2e4a)] - **(SEMVER-MAJOR)** **src**: update NODE_MODULE_VERSION to 56 (Michaël Zasso) [#13263](https://github.com/nodejs/node/pull/13263)
- [[`6e86a6651c`](https://github.com/nodejs/node/commit/6e86a6651c)] - **(SEMVER-MAJOR)** **stream**: complete migration to internal/errors (Matteo Collina) [#16589](https://github.com/nodejs/node/pull/16589)
- [[`88fb359c57`](https://github.com/nodejs/node/commit/88fb359c57)] - **(SEMVER-MAJOR)** **stream**: migrate \_stream_readable use error codes (Ben Halverson) [#15042](https://github.com/nodejs/node/pull/15042)
- [[`db7d1339c3`](https://github.com/nodejs/node/commit/db7d1339c3)] - **(SEMVER-MAJOR)** **stream**: migrate to internal/errors (Ruben Bridgewater) [#15665](https://github.com/nodejs/node/pull/15665)
- [[`4536128e7c`](https://github.com/nodejs/node/commit/4536128e7c)] - **(SEMVER-MAJOR)** **stream**: remove dead code (Ruben Bridgewater) [#15665](https://github.com/nodejs/node/pull/15665)
- [[`eb4940e2d2`](https://github.com/nodejs/node/commit/eb4940e2d2)] - **(SEMVER-MAJOR)** **string_decoder**: Migrate to use internal/errors (Weijia Wang) [#14682](https://github.com/nodejs/node/pull/14682)
- [[`a7487c92e2`](https://github.com/nodejs/node/commit/a7487c92e2)] - **(SEMVER-MAJOR)** **test**: fix message test after V8 upgrade (Michaël Zasso) [#15362](https://github.com/nodejs/node/pull/15362)
- [[`fca7e49e44`](https://github.com/nodejs/node/commit/fca7e49e44)] - **(SEMVER-MAJOR)** **test**: adjust windows failed alloc test to V8 6.2 (Bartosz Sosnowski) [#14730](https://github.com/nodejs/node/pull/14730)
- [[`95c8df18f1`](https://github.com/nodejs/node/commit/95c8df18f1)] - **(SEMVER-MAJOR)** **test**: add test to verify ErrnoException path (Daniel Bevenius) [#13958](https://github.com/nodejs/node/pull/13958)
- [[`0d3ef5b0f8`](https://github.com/nodejs/node/commit/0d3ef5b0f8)] - **(SEMVER-MAJOR)** **test**: check `this` value for `nextTick()` (Rich Trott) [#14645](https://github.com/nodejs/node/pull/14645)
- [[`c6126b1308`](https://github.com/nodejs/node/commit/c6126b1308)] - **(SEMVER-MAJOR)** **test**: refactor test-fs-stat (Rich Trott) [#14645](https://github.com/nodejs/node/pull/14645)
- [[`eaaec57332`](https://github.com/nodejs/node/commit/eaaec57332)] - **(SEMVER-MAJOR)** **test**: use worker.exitedAfterDisconnect consistently (James M Snell) [#13702](https://github.com/nodejs/node/pull/13702)
- [[`839faae45a`](https://github.com/nodejs/node/commit/839faae45a)] - **(SEMVER-MAJOR)** **timers**: cleanup extraneous property on Immediates (Jeremiah Senkpiel) [#16355](https://github.com/nodejs/node/pull/16355)
- [[`ce3586da31`](https://github.com/nodejs/node/commit/ce3586da31)] - **(SEMVER-MAJOR)** **timers**: warn on overflowed timeout duration (Jeremiah Senkpiel) [#15627](https://github.com/nodejs/node/pull/15627)
- [[`11f7dcf91e`](https://github.com/nodejs/node/commit/11f7dcf91e)] - **(SEMVER-MAJOR)** **timers**: do not expose .unref().\_handle.\_list (Jeremiah Senkpiel) [#8422](https://github.com/nodejs/node/pull/8422)
- [[`4d893e093a`](https://github.com/nodejs/node/commit/4d893e093a)] - **(SEMVER-MAJOR)** **timers**: Migrate to use internal/errors (Weijia Wang) [#14659](https://github.com/nodejs/node/pull/14659)
- [[`468110b327`](https://github.com/nodejs/node/commit/468110b327)] - **(SEMVER-MAJOR)** **tls**: deprecate parseCertString & move to internal (XadillaX) [#14249](https://github.com/nodejs/node/pull/14249)
- [[`0f7c06eb2d`](https://github.com/nodejs/node/commit/0f7c06eb2d)] - **(SEMVER-MAJOR)** **tls**: fix object prototype type confusion (Ben Noordhuis) [#14447](https://github.com/nodejs/node/pull/14447)
- [[`a7dccd040d`](https://github.com/nodejs/node/commit/a7dccd040d)] - **(SEMVER-MAJOR)** **tls**: type checking for `key`, `cert` and `ca` options (Jimmy Cann) [#14807](https://github.com/nodejs/node/pull/14807)
- [[`3ccfeb483d`](https://github.com/nodejs/node/commit/3ccfeb483d)] - **(SEMVER-MAJOR)** **tls**: migrate tls.js to use internal/errors.js (Michael Dawson) [#13994](https://github.com/nodejs/node/pull/13994)
- [[`c88ba036b4`](https://github.com/nodejs/node/commit/c88ba036b4)] - **(SEMVER-MAJOR)** **url**: ensure search property is consistently null vs empty (Justin Beckwith) [#13606](https://github.com/nodejs/node/pull/13606)
- [[`b1c8f15c5f`](https://github.com/nodejs/node/commit/b1c8f15c5f)] - **(SEMVER-MAJOR)** **util**: use constructor name (Ruben Bridgewater) [#14886](https://github.com/nodejs/node/pull/14886)
- [[`3b0e800f18`](https://github.com/nodejs/node/commit/3b0e800f18)] - **(SEMVER-MAJOR)** **util**: make util.debuglog() consistent with doc (Vse Mozhet Byt) [#13841](https://github.com/nodejs/node/pull/13841)
- [[`58831b2f24`](https://github.com/nodejs/node/commit/58831b2f24)] - **(SEMVER-MAJOR)** **uv**: improvements to process.binding('uv') (James M Snell) [#14933](https://github.com/nodejs/node/pull/14933)
- [[`ef238fb485`](https://github.com/nodejs/node/commit/ef238fb485)] - **(SEMVER-MAJOR)** **v8**: migrate setFlagsFromString to internal/errors (James M Snell) [#16535](https://github.com/nodejs/node/pull/16535)
- [[`b3e5c4621d`](https://github.com/nodejs/node/commit/b3e5c4621d)] - **(SEMVER-MAJOR)** **v8**: add new to the throw statement (Ruben Bridgewater) [#13857](https://github.com/nodejs/node/pull/13857)
- [[`88e55fe5e0`](https://github.com/nodejs/node/commit/88e55fe5e0)] - **(SEMVER-MAJOR)** **vm**: deprecate vm.runInDebugContext (Josh Gavant) [#12815](https://github.com/nodejs/node/pull/12815)
- [[`896eaf6820`](https://github.com/nodejs/node/commit/896eaf6820)] - **(SEMVER-MAJOR)** **zlib**: finish migrating to internal/errors (James M Snell) [#16540](https://github.com/nodejs/node/pull/16540)
- [[`74891412f1`](https://github.com/nodejs/node/commit/74891412f1)] - **(SEMVER-MAJOR)** **zlib**: migrate to internal/errors (James M Snell) [#15618](https://github.com/nodejs/node/pull/15618)

#### Semver-Minor

- [[`3e25e4d00f`](https://github.com/nodejs/node/commit/3e25e4d00f)] - **(SEMVER-MINOR)** **http**: support generic `Duplex` streams (Anna Henningsen) [#16267](https://github.com/nodejs/node/pull/16267)
- [[`af3aa682ac`](https://github.com/nodejs/node/commit/af3aa682ac)] - **(SEMVER-MINOR)** **util**: add callbackify (Refael Ackermann) [#12712](https://github.com/nodejs/node/pull/12712)
- [[`36732084db`](https://github.com/nodejs/node/commit/36732084db)] - **(SEMVER-MINOR)** **util,assert**: expose util.isDeepStrictEqual() (Rich Trott) [#16084](https://github.com/nodejs/node/pull/16084)

#### Semver-Patch

- [[`6e86a70da2`](https://github.com/nodejs/node/commit/6e86a70da2)] - **assert**: replace many if's with if-else statement (kuroljov) [#14043](https://github.com/nodejs/node/pull/14043)
- [[`f8063d51d7`](https://github.com/nodejs/node/commit/f8063d51d7)] - **benchmark**: fix punycode test for --without-intl (Timothy Gu) [#16251](https://github.com/nodejs/node/pull/16251)
- [[`095c0de94d`](https://github.com/nodejs/node/commit/095c0de94d)] - **benchmark,lib,test**: use braces for multiline block (Rich Trott) [#13828](https://github.com/nodejs/node/pull/13828)
- [[`8172f4547e`](https://github.com/nodejs/node/commit/8172f4547e)] - **buffer**: move setupBufferJS to internal (Bryan English) [#16391](https://github.com/nodejs/node/pull/16391)
- [[`355523fcfb`](https://github.com/nodejs/node/commit/355523fcfb)] - **buffer**: refactor module.exports, imports (James M Snell) [#13807](https://github.com/nodejs/node/pull/13807)
- [[`e0340af455`](https://github.com/nodejs/node/commit/e0340af455)] - **buffer**: fix indentation nits (Rich Trott) [#14224](https://github.com/nodejs/node/pull/14224)
- [[`aa011a111d`](https://github.com/nodejs/node/commit/aa011a111d)] - **_Revert_** "**build**: don't add libraries when --enable-static" (Ben Noordhuis) [#14893](https://github.com/nodejs/node/pull/14893)
- [[`be63c26e8c`](https://github.com/nodejs/node/commit/be63c26e8c)] - **build**: don't add libraries when --enable-static (Daniel Bevenius) [#14837](https://github.com/nodejs/node/pull/14837)
- [[`556ebab30e`](https://github.com/nodejs/node/commit/556ebab30e)] - **child_process**: restore exec{File}Sync error props (Michaël Zasso) [#16060](https://github.com/nodejs/node/pull/16060)
- [[`9bc4f86201`](https://github.com/nodejs/node/commit/9bc4f86201)] - **crypto**: make createXYZ inlineable (Matteo Collina) [#16067](https://github.com/nodejs/node/pull/16067)
- [[`43e7e8d106`](https://github.com/nodejs/node/commit/43e7e8d106)] - **crypto**: remove useless if statement (Weijia Wang) [#15041](https://github.com/nodejs/node/pull/15041)
- [[`237067d54e`](https://github.com/nodejs/node/commit/237067d54e)] - **deps**: manually add 9.x support to npm (Myles Borins) [#16509](https://github.com/nodejs/node/pull/16509)
- [[`0ea8ff3deb`](https://github.com/nodejs/node/commit/0ea8ff3deb)] - **deps**: backport 4ca695819 from npm upstream (Myles Borins) [#16509](https://github.com/nodejs/node/pull/16509)
- [[`664512678d`](https://github.com/nodejs/node/commit/664512678d)] - **_Revert_** "**deps**: update V8 to 6.2.414.33" (Michaël Zasso) [#16513](https://github.com/nodejs/node/pull/16513)
- [[`d4033c1547`](https://github.com/nodejs/node/commit/d4033c1547)] - **deps**: update V8 to 6.2.414.33 (Michaël Zasso) [#16412](https://github.com/nodejs/node/pull/16412)
- [[`801e61ad5a`](https://github.com/nodejs/node/commit/801e61ad5a)] - **deps**: cherry-pick 37a3a15c3 from V8 upstream (Franziska Hinkelmann) [#16294](https://github.com/nodejs/node/pull/16294)
- [[`34d125f16c`](https://github.com/nodejs/node/commit/34d125f16c)] - **deps**: c-ares float, win ipv6 bad fec0 prefix (Rod Vagg) [#15378](https://github.com/nodejs/node/pull/15378)
- [[`af171b7ba2`](https://github.com/nodejs/node/commit/af171b7ba2)] - **deps**: c-ares float, manual ares_ssize_t definition (Rod Vagg) [#15378](https://github.com/nodejs/node/pull/15378)
- [[`13c74706ef`](https://github.com/nodejs/node/commit/13c74706ef)] - **deps**: upgrade to c-ares v1.13.0 (Rod Vagg) [#15378](https://github.com/nodejs/node/pull/15378)
- [[`d0d1eba872`](https://github.com/nodejs/node/commit/d0d1eba872)] - **deps**: update license-builder & LICENSE for c-ares (Rod Vagg) [#15378](https://github.com/nodejs/node/pull/15378)
- [[`a9f125449e`](https://github.com/nodejs/node/commit/a9f125449e)] - **deps**: upgrade to c-ares v1.12.0 (Rod Vagg) [#15378](https://github.com/nodejs/node/pull/15378)
- [[`8dce05fa71`](https://github.com/nodejs/node/commit/8dce05fa71)] - **deps**: backport rehash strings after deserialization (Yang Guo) [#14345](https://github.com/nodejs/node/pull/14345)
- [[`785a9e5a57`](https://github.com/nodejs/node/commit/785a9e5a57)] - **deps**: cherry-pick 6cb999b97b from V8 upstream (Igor Sheludko) [#14188](https://github.com/nodejs/node/pull/14188)
- [[`31349e2245`](https://github.com/nodejs/node/commit/31349e2245)] - **deps**: cherry-pick 3f4536894ac from V8 upstream (ochang) [#13985](https://github.com/nodejs/node/pull/13985)
- [[`0ba74dbcc6`](https://github.com/nodejs/node/commit/0ba74dbcc6)] - **deps**: backport c0f1ff2 from upstream V8 (Michaël Zasso) [#13517](https://github.com/nodejs/node/pull/13517)
- [[`7cdcca7623`](https://github.com/nodejs/node/commit/7cdcca7623)] - **deps**: cherry-pick 866ee63 from upstream V8 (Michaël Zasso) [#13630](https://github.com/nodejs/node/pull/13630)
- [[`8f907b6baf`](https://github.com/nodejs/node/commit/8f907b6baf)] - **deps**: update V8 to 5.9.211.37 (Michaël Zasso) [#13631](https://github.com/nodejs/node/pull/13631)
- [[`554fa24916`](https://github.com/nodejs/node/commit/554fa24916)] - **deps**: cherry-pick f5fad6d from upstream v8 (daniel.bevenius) [#12826](https://github.com/nodejs/node/pull/12826)
- [[`36ba9e6e0c`](https://github.com/nodejs/node/commit/36ba9e6e0c)] - **deps**: cherry-pick bfae9db from upstream v8 (Ben Noordhuis) [#12722](https://github.com/nodejs/node/pull/12722)
- [[`863d1922df`](https://github.com/nodejs/node/commit/863d1922df)] - **doc**: add link for stream.pipe() (Jon Moss) [#16593](https://github.com/nodejs/node/pull/16593)
- [[`fb477f3fa5`](https://github.com/nodejs/node/commit/fb477f3fa5)] - **doc**: add missing error codes (James M Snell) [#16450](https://github.com/nodejs/node/pull/16450)
- [[`1261b94a3f`](https://github.com/nodejs/node/commit/1261b94a3f)] - **doc**: fix unassigned deprecation code (James M Snell) [#15741](https://github.com/nodejs/node/pull/15741)
- [[`cd1b55a942`](https://github.com/nodejs/node/commit/cd1b55a942)] - **doc**: delete link to removed doc part (Vse Mozhet Byt) [#15510](https://github.com/nodejs/node/pull/15510)
- [[`a5916107dd`](https://github.com/nodejs/node/commit/a5916107dd)] - **doc**: fix wrong history entry in deepStrictEqual (hisener) [#15381](https://github.com/nodejs/node/pull/15381)
- [[`8b2c61c169`](https://github.com/nodejs/node/commit/8b2c61c169)] - **doc**: fix api docs style (Daijiro Wachi) [#13970](https://github.com/nodejs/node/pull/13970)
- [[`102e1aa4e3`](https://github.com/nodejs/node/commit/102e1aa4e3)] - **doc**: fix ordering error in errors.md (Rich Trott) [#13274](https://github.com/nodejs/node/pull/13274)
- [[`8a8a6865c0`](https://github.com/nodejs/node/commit/8a8a6865c0)] - **doc,net**: assign deprecation code (Anna Henningsen) [#14576](https://github.com/nodejs/node/pull/14576)
- [[`55d49eb3cc`](https://github.com/nodejs/node/commit/55d49eb3cc)] - **errors**: replace `.split()` with `.replace()` (Rich Trott) [#15545](https://github.com/nodejs/node/pull/15545)
- [[`cef6e1c55f`](https://github.com/nodejs/node/commit/cef6e1c55f)] - **errors**: refactor `invalidArgType()` (Rich Trott) [#15544](https://github.com/nodejs/node/pull/15544)
- [[`324aa6488f`](https://github.com/nodejs/node/commit/324aa6488f)] - **errors**: alphabetize error codes (Jon Moss) [#15083](https://github.com/nodejs/node/pull/15083)
- [[`fa73087fcf`](https://github.com/nodejs/node/commit/fa73087fcf)] - **errors**: keep error codes in alphabetical order (Weijia Wang) [#14242](https://github.com/nodejs/node/pull/14242)
- [[`873e2f270f`](https://github.com/nodejs/node/commit/873e2f270f)] - **errors**: add missing ERR\_ prefix on util.callbackify error (James M Snell) [#13604](https://github.com/nodejs/node/pull/13604)
- [[`5f469446e1`](https://github.com/nodejs/node/commit/5f469446e1)] - **errors,tools**: ASCIIbetical instead of alphabetical (Refael Ackermann) [#15578](https://github.com/nodejs/node/pull/15578)
- [[`fe13e0077f`](https://github.com/nodejs/node/commit/fe13e0077f)] - **events**: onceWrapper apply directly with arguments (Anatoli Papirovski) [#16212](https://github.com/nodejs/node/pull/16212)
- [[`d5fb78982a`](https://github.com/nodejs/node/commit/d5fb78982a)] - **events**: use spread function param in emit (Anatoli Papirovski) [#16212](https://github.com/nodejs/node/pull/16212)
- [[`fd166a8759`](https://github.com/nodejs/node/commit/fd166a8759)] - **events**: return values directly in listeners (Anatoli Papirovski) [#16212](https://github.com/nodejs/node/pull/16212)
- [[`c8d4ff1d52`](https://github.com/nodejs/node/commit/c8d4ff1d52)] - **events**: remove unnecessary console instantiation (Anatoli Papirovski) [#16212](https://github.com/nodejs/node/pull/16212)
- [[`f61cc15c6a`](https://github.com/nodejs/node/commit/f61cc15c6a)] - **events**: stricter prop & variable checks for perf (Anatoli Papirovski) [#16212](https://github.com/nodejs/node/pull/16212)
- [[`5d99a9bf65`](https://github.com/nodejs/node/commit/5d99a9bf65)] - **http**: emit close as the last event in the client (Robert Nagy) [#15588](https://github.com/nodejs/node/pull/15588)
- [[`f912080bf2`](https://github.com/nodejs/node/commit/f912080bf2)] - **_Revert_** "**http2**: refactor error handling" (Rich Trott) [#15047](https://github.com/nodejs/node/pull/15047)
- [[`a6973a3811`](https://github.com/nodejs/node/commit/a6973a3811)] - **_Revert_** "**inspector**: rewrite inspector test helper" (Anna Henningsen) [#14777](https://github.com/nodejs/node/pull/14777)
- [[`2296b677fb`](https://github.com/nodejs/node/commit/2296b677fb)] - **inspector**: rewrite inspector test helper (Eugene Ostroukhov) [#14460](https://github.com/nodejs/node/pull/14460)
- [[`e6dfd59be0`](https://github.com/nodejs/node/commit/e6dfd59be0)] - **lib**: pass internalBinding more implicitly (Anna Henningsen) [#16218](https://github.com/nodejs/node/pull/16218)
- [[`a577bde917`](https://github.com/nodejs/node/commit/a577bde917)] - **lib**: fix off-by-one indentation (Rich Trott) [#14064](https://github.com/nodejs/node/pull/14064)
- [[`c474f88987`](https://github.com/nodejs/node/commit/c474f88987)] - **lib**: fix typos (Ruben Bridgewater) [#13741](https://github.com/nodejs/node/pull/13741)
- [[`ae6c7044c8`](https://github.com/nodejs/node/commit/ae6c7044c8)] - **_Revert_** "**lib**: lazy instantiation of fs.Stats dates" (Anna Henningsen) [#13256](https://github.com/nodejs/node/pull/13256)
- [[`45873d24e4`](https://github.com/nodejs/node/commit/45873d24e4)] - **module**: revert #3384 DEP0019 EOL (Myles Borins) [#16634](https://github.com/nodejs/node/pull/16634)
- [[`44256bb0aa`](https://github.com/nodejs/node/commit/44256bb0aa)] - **path**: fix incorrect use of ERR_INVALID_ARG_TYPE (Tobias Nießen) [#14011](https://github.com/nodejs/node/pull/14011)
- [[`c5f54b1fad`](https://github.com/nodejs/node/commit/c5f54b1fad)] - **repl**: remove internal frames from runtime errors (Lance Ball) [#15351](https://github.com/nodejs/node/pull/15351)
- [[`da40050b59`](https://github.com/nodejs/node/commit/da40050b59)] - **repl**: fix deprecation code (Ruben Bridgewater) [#15668](https://github.com/nodejs/node/pull/15668)
- [[`766506a2e9`](https://github.com/nodejs/node/commit/766506a2e9)] - **repl**: deprecate REPLServer.parseREPLKeyword (Lance Ball)
- [[`f0b871bada`](https://github.com/nodejs/node/commit/f0b871bada)] - **src**: remove unused warning in node_contextify (Michaël Zasso) [#16408](https://github.com/nodejs/node/pull/16408)
- [[`f1d6b04ac9`](https://github.com/nodejs/node/commit/f1d6b04ac9)] - **src**: use new V8 API in vm module (Franziska Hinkelmann) [#16293](https://github.com/nodejs/node/pull/16293)
- [[`2146c88bc7`](https://github.com/nodejs/node/commit/2146c88bc7)] - **src**: fix NewContext for --without-intl (Timothy Gu) [#16251](https://github.com/nodejs/node/pull/16251)
- [[`a84c3be075`](https://github.com/nodejs/node/commit/a84c3be075)] - **src**: unset `NODE_VERSION_IS_RELEASE` (Anna Henningsen) [#14005](https://github.com/nodejs/node/pull/14005)
- [[`1b54371c50`](https://github.com/nodejs/node/commit/1b54371c50)] - **stream**: use more explicit statements (Ruben Bridgewater) [#13863](https://github.com/nodejs/node/pull/13863)
- [[`9702ac5088`](https://github.com/nodejs/node/commit/9702ac5088)] - **test**: add test for WrapStream readStop (Ashish Kaila) [#16356](https://github.com/nodejs/node/pull/16356)
- [[`a37a0ad5f6`](https://github.com/nodejs/node/commit/a37a0ad5f6)] - **test**: add test for prop interceptors on sandbox (Michaël Zasso) [#16409](https://github.com/nodejs/node/pull/16409)
- [[`ed116dc3c6`](https://github.com/nodejs/node/commit/ed116dc3c6)] - **test**: fix test for inherited properties on vm (Franziska Hinkelmann) [#16411](https://github.com/nodejs/node/pull/16411)
- [[`438e7fdaf2`](https://github.com/nodejs/node/commit/438e7fdaf2)] - **test**: remove --harmony-sharedarraybuffer usage (Ben Smith) [#16343](https://github.com/nodejs/node/pull/16343)
- [[`cd5ee52d70`](https://github.com/nodejs/node/commit/cd5ee52d70)] - **test**: add tests for eslint rules (Teddy Katz) [#16138](https://github.com/nodejs/node/pull/16138)
- [[`16ed116203`](https://github.com/nodejs/node/commit/16ed116203)] - **test**: clean up string concat in dlopen-ping-pong (agilbert) [#15820](https://github.com/nodejs/node/pull/15820)
- [[`2e215f169a`](https://github.com/nodejs/node/commit/2e215f169a)] - **test**: fix and refactor test-http-invalid-urls (Rich Trott) [#15678](https://github.com/nodejs/node/pull/15678)
- [[`44d486500d`](https://github.com/nodejs/node/commit/44d486500d)] - **test**: increase coverage for internal/errors.js (Weijia Wang) [#15044](https://github.com/nodejs/node/pull/15044)
- [[`467385a49b`](https://github.com/nodejs/node/commit/467385a49b)] - **test**: use invalid host according to RFC2606 (Tobias Nießen) [#14863](https://github.com/nodejs/node/pull/14863)
- [[`f417add1f4`](https://github.com/nodejs/node/commit/f417add1f4)] - **test**: add test-benchmark-zlib (Rich Trott) [#14763](https://github.com/nodejs/node/pull/14763)
- [[`3566195196`](https://github.com/nodejs/node/commit/3566195196)] - **test**: replace concatenation with template literals (xeodou) [#14281](https://github.com/nodejs/node/pull/14281)
- [[`b923b9dee1`](https://github.com/nodejs/node/commit/b923b9dee1)] - **test**: replace string concat in test-child-process-constructor (mac-haojin) [#14283](https://github.com/nodejs/node/pull/14283)
- [[`2a621d4051`](https://github.com/nodejs/node/commit/2a621d4051)] - **test**: validate more properties in expectsError (Ruben Bridgewater) [#14058](https://github.com/nodejs/node/pull/14058)
- [[`5ffb5b6fce`](https://github.com/nodejs/node/commit/5ffb5b6fce)] - **test**: improve the test common documentation (Ruben Bridgewater) [#14148](https://github.com/nodejs/node/pull/14148)
- [[`1b2733f272`](https://github.com/nodejs/node/commit/1b2733f272)] - **test**: common.expectsError should be a must call (Ruben Bridgewater) [#14088](https://github.com/nodejs/node/pull/14088)
- [[`d69ecc6f51`](https://github.com/nodejs/node/commit/d69ecc6f51)] - **_Revert_** "**test**: improve test-process-kill-null for Windows" (Refael Ackermann) [#14142](https://github.com/nodejs/node/pull/14142)
- [[`d6fece1436`](https://github.com/nodejs/node/commit/d6fece1436)] - **test**: add optional throw fn to expectsError (Ruben Bridgewater) [#14089](https://github.com/nodejs/node/pull/14089)
- [[`44483b6898`](https://github.com/nodejs/node/commit/44483b6898)] - **test**: improve test-process-kill-null for Windows (starkwang) [#14099](https://github.com/nodejs/node/pull/14099)
- [[`5723b5dbbc`](https://github.com/nodejs/node/commit/5723b5dbbc)] - **tls**: improve TLSSocket & Server performance (Anatoli Papirovski) [#15575](https://github.com/nodejs/node/pull/15575)
- [[`1403d28e7d`](https://github.com/nodejs/node/commit/1403d28e7d)] - **tls**: re-allow falsey option values (Anna Henningsen) [#15131](https://github.com/nodejs/node/pull/15131)
- [[`5723c4c5f0`](https://github.com/nodejs/node/commit/5723c4c5f0)] - **tls**: replace forEach with for (Brian White) [#15053](https://github.com/nodejs/node/pull/15053)
- [[`193926ecab`](https://github.com/nodejs/node/commit/193926ecab)] - **tls,doc**: fix unallocated deprecation code (James M Snell) [#15534](https://github.com/nodejs/node/pull/15534)
- [[`76b8803630`](https://github.com/nodejs/node/commit/76b8803630)] - **tools**: add eslint rule for documented errors (James M Snell) [#16450](https://github.com/nodejs/node/pull/16450)
- [[`50fe1a8409`](https://github.com/nodejs/node/commit/50fe1a8409)] - **tools, benchmark**: test util benchmark (Sarah Meyer) [#16050](https://github.com/nodejs/node/pull/16050)
- [[`44f5523260`](https://github.com/nodejs/node/commit/44f5523260)] - **v8**: fix stack overflow in recursive method (Ben Noordhuis) [#12460](https://github.com/nodejs/node/pull/12460)
- [[`241eb6122e`](https://github.com/nodejs/node/commit/241eb6122e)] - **zlib**: gracefully set windowBits from 8 to 9 (Myles Borins) [#16511](https://github.com/nodejs/node/pull/16511)
- [[`2421984727`](https://github.com/nodejs/node/commit/2421984727)] - **zlib**: check cleanup return values (Anna Henningsen) [#14673](https://github.com/nodejs/node/pull/14673)
- [[`add4b0ab8c`](https://github.com/nodejs/node/commit/add4b0ab8c)] - **zlib**: improve performance (Brian White) [#13322](https://github.com/nodejs/node/pull/13322)

Windows 32-bit Installer: https://nodejs.org/dist/v9.0.0/node-v9.0.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v9.0.0/node-v9.0.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v9.0.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v9.0.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v9.0.0/node-v9.0.0.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v9.0.0/node-v9.0.0-darwin-x64.tar.gz \
Linux 32-bit Binary: https://nodejs.org/dist/v9.0.0/node-v9.0.0-linux-x86.tar.xz \
Linux 64-bit Binary: https://nodejs.org/dist/v9.0.0/node-v9.0.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v9.0.0/node-v9.0.0-linux-ppc64le.tar.xz \
Linux PPC BE 64-bit Binary: https://nodejs.org/dist/v9.0.0/node-v9.0.0-linux-ppc64.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v9.0.0/node-v9.0.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v9.0.0/node-v9.0.0-aix-ppc64.tar.gz \
SmartOS 32-bit Binary: https://nodejs.org/dist/v9.0.0/node-v9.0.0-sunos-x86.tar.xz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v9.0.0/node-v9.0.0-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v9.0.0/node-v9.0.0-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v9.0.0/node-v9.0.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v9.0.0/node-v9.0.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v9.0.0/node-v9.0.0.tar.gz \
Other release files: https://nodejs.org/dist/v9.0.0/ \
Documentation: https://nodejs.org/docs/v9.0.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

7eafe17599d9cc63c1c0ddbf4d8f7daacbef11804ae52376fa79033addbdd341  node-v9.0.0-aix-ppc64.tar.gz
40fe905e492deadbf84c46baba413294e516fa844ed8e68adb4117f8acabe9a9  node-v9.0.0-darwin-x64.tar.gz
170d30261e49adbbf4e77a90e16b8c7283a5ad79dc4e878f801b3f748ced56fa  node-v9.0.0-darwin-x64.tar.xz
05ccd192be6407de5e407136ee01b568b38c52ee1197f4367c3d747e77441f4c  node-v9.0.0-headers.tar.gz
ef7bd85922b81bc4f7b3db6b4e6eb456a737082309c3020fcf7cc519e8c5bb16  node-v9.0.0-headers.tar.xz
c866c8e67f0f3f9be7e7195c6109cda4cf5a91fdd5e881920557b70924521034  node-v9.0.0-linux-arm64.tar.gz
0250e13705259dcf736ac4216833c2ade459e3ade94d89af68bd98ded1783cb6  node-v9.0.0-linux-arm64.tar.xz
604cacbd4d97e5432990dba8066a3eaaefeb8450172787d77a5ebd5b8f187551  node-v9.0.0-linux-armv7l.tar.gz
b17c8b2db30fd4c2d6bb830818670de589c84ac78c04391827df9626700c7cec  node-v9.0.0-linux-armv7l.tar.xz
b7d4fb173fac23778140920046356b4c915c080ab27e03c6bb0fd2ac90a6c192  node-v9.0.0-linux-ppc64le.tar.gz
69a8ebdbedeea4c1537157df124c278f7c517d9e02665bf033c9830511e24757  node-v9.0.0-linux-ppc64le.tar.xz
d559ee8a72593d877b51b5bdd02797e0d74840bc04e94d50dbd842f55f95960a  node-v9.0.0-linux-ppc64.tar.gz
5d63c7bd7e206acc176d324ff300514adcfb7da1aa60e4ebd306b604adc06b50  node-v9.0.0-linux-ppc64.tar.xz
c57aec2c2887c2dc239eeb0ab930c490e26192c2c7339499dbdee9170d2f7040  node-v9.0.0-linux-s390x.tar.gz
0201874562e163d80a6b492f9e3af59d9e597414fc7f8aeda64aae2cec0eb263  node-v9.0.0-linux-s390x.tar.xz
9bd9ef8c2df8dc0a2cd66cdbb7b6a1c62a12912efd9218e307ce63db871b813d  node-v9.0.0-linux-x64.tar.gz
8313d2f6d69dbea4cb860803a156f093041cbe352a843c06f9f05cab1f30e9cc  node-v9.0.0-linux-x64.tar.xz
3fdddbdd5a154c1578f186ae41d92ecd363764c2ae112ee0c45cc5e25a872e2a  node-v9.0.0-linux-x86.tar.gz
1d4f2e0db1efa0f9850f272327243ee17aa5bef2e46807bb1adf2e5ba2b56c59  node-v9.0.0-linux-x86.tar.xz
375fc6a834373e26d24ead2cc60e5a9e4b3db184fe7deac6bc9b39ca4db635a7  node-v9.0.0.pkg
9f566f30b7abe977bd5a33b42e794859147662486773ae21d576f11a8b038397  node-v9.0.0-sunos-x64.tar.gz
ad66172b52e5fe6ee1f58863726b822c8322c5d8e31fd9ea7e74a53ee3cbe56c  node-v9.0.0-sunos-x64.tar.xz
d4696300e158bfb66bfb2fa008b14d309f3764195b3b7ff3ccf6dbccf19c062c  node-v9.0.0-sunos-x86.tar.gz
73b4f27c0e03c748e401f6659024c6ce71114c1dbe9d997838e421532c748ea3  node-v9.0.0-sunos-x86.tar.xz
fe06dafd4f034d2372d34bb064c65ebf5ab4d3d6e04d1745fd108c2a97a9d424  node-v9.0.0.tar.gz
5b52bd6a90a611a42e11a908022ccfc1c2e77dcc70bfe38054a18dc57a3d5fa5  node-v9.0.0.tar.xz
27dc184ef054c83b75fe636a62564487feefe2081bbcc0761df729a6419e1af2  node-v9.0.0-win-x64.7z
d25901007e7c48da3af162bc1917d5bdd78c7e3b9cb64f16f90c38b59ef7b412  node-v9.0.0-win-x64.zip
c462067842335936f30f2a7d37d2482788e2dbe26b1f7494d925ed451e625371  node-v9.0.0-win-x86.7z
825e9f788b2b47c25f7c441fee902db1b103f3f6b5951a868cf62a76252ccffd  node-v9.0.0-win-x86.zip
e5334db7999ba31fece387f081d1d6a0eb595cc6b10844bd301adf27ff357732  node-v9.0.0-x64.msi
1f6932e69a5af9f8cd7dc072ef40979fb2b2e7e840810b9a01f58afeca2fee52  node-v9.0.0-x86.msi
f42ad895888f366d85ff5fa74382df0b8c11f664df188fb5a094a2177ade3475  win-x64/node.exe
3e46877c1c237c275844d2ed6fb2043d561eb7a3538d303f118c4a499a2557c0  win-x64/node.lib
633e83a247a16ade33a6d789f219e3825cbae8c3a5d95f33f277099d445c831d  win-x64/node_pdb.7z
1fdafeddb3576ad34f94858e7ec2748c2f721338a3300948393bc7a18f5e873e  win-x64/node_pdb.zip
9f8b253bed4043b1c2354de5e9f8dbc66ad1f88977eb50a37221fc9f23b09b51  win-x86/node.exe
c6ed378e1cbfc3c2f616f0e63fcc45d570c8b692ec75db4657650fc4ed8187f1  win-x86/node.lib
8029acfcc219b22b599902843fbcbb03270d60d314a560d7692c385cc4ce8678  win-x86/node_pdb.7z
12049450a219f26eb8b33738c8329e86672f916c0a7a2b416cb70158c5965603  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQEcBAEBCAAGBQJZ+NazAAoJEHNBsVwHCHes830H/0ZlTFCcTTaGK5xmjAKCFZHR
IMTk+d93Yxu32pWKEYA/842J1cr09vPzv0KKLbJw6MZ2a5+tsTsi/xM7OYPtFzJd
lfQzfX63fI3g5/BrooDY9smvtNDRTtAs9ASvrPiX5owBTLJgZGsYfxH08jXk8lkk
WTiaGG38HiYNItFHM81UD4PmWHCHb7JA8ZdY9eHI4EgR0FrLZGy3lMQy6r7JIWoW
FCF/1aWLniGXNW3/lv93lKyvQqvox8S/PgpiojvQveapcxnZUQhbrgz17Z9EJgrc
CGlIdOjqceHTJPV8lHQFcrZm/hmN20h+RmzFXae/ibgzmyuYsdYTE5p0JJ62E+I=
=LMNW
-----END PGP SIGNATURE-----

```
