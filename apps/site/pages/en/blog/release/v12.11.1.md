---
date: '2019-10-01T16:46:36.702Z'
category: release
title: Node v12.11.1 (Current)
layout: blog-post
author: <PERSON><PERSON><PERSON>
---

### Notable changes

- **build**:
  - This release fixes a regression that prevented from building Node.js using
    the official source tarball (<PERSON>) [#29712](https://github.com/nodejs/node/pull/29712).
- **deps**:
  - Updated small-icu data to support "unit" style in the `Intl.NumberFormat` API (<PERSON><PERSON><PERSON>) [#29735](https://github.com/nodejs/node/pull/29735).

### Commits

- [[`35e1d8c5ba`](https://github.com/nodejs/node/commit/35e1d8c5ba)] - **build**: include deps/v8/test/torque in source tarball (<PERSON>) [#29712](https://github.com/nodejs/node/pull/29712)
- [[`ae461964a7`](https://github.com/nodejs/node/commit/ae461964a7)] - **build,win**: goto lint only after defining node_exe (<PERSON>) [#29616](https://github.com/nodejs/node/pull/29616)
- [[`588b388181`](https://github.com/nodejs/node/commit/588b388181)] - **crypto**: use byteLength in timingSafeEqual (Tobias Nießen) [#29657](https://github.com/nodejs/node/pull/29657)
- [[`298d92785c`](https://github.com/nodejs/node/commit/298d92785c)] - **deps**: enable unit data in small-icu (Michaël Zasso) [#29735](https://github.com/nodejs/node/pull/29735)
- [[`0041f1c0d3`](https://github.com/nodejs/node/commit/0041f1c0d3)] - **doc**: sync security policy with nodejs.org (Sam Roberts) [#29682](https://github.com/nodejs/node/pull/29682)
- [[`038cbb08de`](https://github.com/nodejs/node/commit/038cbb08de)] - **doc**: fix output in inspector HeapProfile example (Kirill Fomichev) [#29711](https://github.com/nodejs/node/pull/29711)
- [[`d86f10cf0b`](https://github.com/nodejs/node/commit/d86f10cf0b)] - **doc**: add KeyObject to type for crypto.createDecipheriv() argument (exoego) [#29689](https://github.com/nodejs/node/pull/29689)
- [[`1303e3551f`](https://github.com/nodejs/node/commit/1303e3551f)] - **doc**: clarify description of `readable.push()` method (imhype) [#29687](https://github.com/nodejs/node/pull/29687)
- [[`d258e0242c`](https://github.com/nodejs/node/commit/d258e0242c)] - **doc**: clarify stream errors while reading and writing (Robert Nagy) [#29653](https://github.com/nodejs/node/pull/29653)
- [[`0fc85ff96a`](https://github.com/nodejs/node/commit/0fc85ff96a)] - **doc**: specify `display=fallback` for Google Fonts (XhmikosR) [#29688](https://github.com/nodejs/node/pull/29688)
- [[`c2791dcd9c`](https://github.com/nodejs/node/commit/c2791dcd9c)] - **doc**: fix some recent nits (Vse Mozhet Byt) [#29670](https://github.com/nodejs/node/pull/29670)
- [[`7a6b05a26f`](https://github.com/nodejs/node/commit/7a6b05a26f)] - **doc**: fix 404 links (XhmikosR) [#29661](https://github.com/nodejs/node/pull/29661)
- [[`2b76cb6dda`](https://github.com/nodejs/node/commit/2b76cb6dda)] - **doc**: remove align from tables (XhmikosR) [#29668](https://github.com/nodejs/node/pull/29668)
- [[`3de1fc6958`](https://github.com/nodejs/node/commit/3de1fc6958)] - **doc**: document that iv may be null when using createCipheriv() (Ruben Bridgewater) [#29684](https://github.com/nodejs/node/pull/29684)
- [[`91e4cc7500`](https://github.com/nodejs/node/commit/91e4cc7500)] - **doc**: update AUTHORS list (Anna Henningsen) [#29608](https://github.com/nodejs/node/pull/29608)
- [[`2ea4cc0732`](https://github.com/nodejs/node/commit/2ea4cc0732)] - **doc**: clarify pipeline stream cleanup (Robert Nagy) [#29738](https://github.com/nodejs/node/pull/29738)
- [[`ab060bfdab`](https://github.com/nodejs/node/commit/ab060bfdab)] - **doc**: clarify fs.symlink() usage (Simon A. Eugster) [#29700](https://github.com/nodejs/node/pull/29700)
- [[`b5c24dfbe8`](https://github.com/nodejs/node/commit/b5c24dfbe8)] - **doc**: fix type of atime/mtime (TATSUNO Yasuhiro) [#29666](https://github.com/nodejs/node/pull/29666)
- [[`6579b1a547`](https://github.com/nodejs/node/commit/6579b1a547)] - **doc,http**: indicate callback is optional for message.setTimeout() (Trivikram Kamat) [#29654](https://github.com/nodejs/node/pull/29654)
- [[`a04fc86723`](https://github.com/nodejs/node/commit/a04fc86723)] - **http2**: optimize the altsvc Max bytes limit, define and use constants (rickyes) [#29673](https://github.com/nodejs/node/pull/29673)
- [[`d1f4befd09`](https://github.com/nodejs/node/commit/d1f4befd09)] - **module**: pass full URL to loader for top-level load (Guy Bedford) [#29736](https://github.com/nodejs/node/pull/29736)
- [[`3f028551a8`](https://github.com/nodejs/node/commit/3f028551a8)] - **module**: move cjs type check behind flag (Guy Bedford) [#29732](https://github.com/nodejs/node/pull/29732)
- [[`c3a1303bc2`](https://github.com/nodejs/node/commit/c3a1303bc2)] - **src**: rename --loader to --experimental-loader (Alex Aubuchon) [#29752](https://github.com/nodejs/node/pull/29752)
- [[`17c3478d78`](https://github.com/nodejs/node/commit/17c3478d78)] - **src**: fix asan build for gcc/clang (David Carlier) [#29383](https://github.com/nodejs/node/pull/29383)
- [[`64740d44b5`](https://github.com/nodejs/node/commit/64740d44b5)] - **src**: fix compiler warning in inspector_profiler.cc (Daniel Bevenius) [#29660](https://github.com/nodejs/node/pull/29660)
- [[`a86b71f745`](https://github.com/nodejs/node/commit/a86b71f745)] - **src**: disconnect inspector before exiting out of fatal exception (Joyee Cheung) [#29611](https://github.com/nodejs/node/pull/29611)
- [[`8d88010277`](https://github.com/nodejs/node/commit/8d88010277)] - **src**: try showing stack traces when process.\_fatalException is not set (Joyee Cheung) [#29624](https://github.com/nodejs/node/pull/29624)
- [[`2a6b7b0476`](https://github.com/nodejs/node/commit/2a6b7b0476)] - **test**: fix flaky test-cluster-net-listen-ipv6only-none (Rich Trott) [#29681](https://github.com/nodejs/node/pull/29681)
- [[`69f26340e9`](https://github.com/nodejs/node/commit/69f26340e9)] - **tls**: simplify setSecureContext() option parsing (cjihrig) [#29704](https://github.com/nodejs/node/pull/29704)
- [[`c361180c07`](https://github.com/nodejs/node/commit/c361180c07)] - **tools**: make mailmap processing for author list case-insensitive (Anna Henningsen) [#29608](https://github.com/nodejs/node/pull/29608)
- [[`ef033d046a`](https://github.com/nodejs/node/commit/ef033d046a)] - **worker**: fix process.\_fatalException return type (Ruben Bridgewater) [#29706](https://github.com/nodejs/node/pull/29706)
- [[`04df7dbadb`](https://github.com/nodejs/node/commit/04df7dbadb)] - **worker**: keep allocators for transferred SAB instances alive longer (Anna Henningsen) [#29637](https://github.com/nodejs/node/pull/29637)

Windows 32-bit Installer: https://nodejs.org/dist/v12.11.1/node-v12.11.1-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v12.11.1/node-v12.11.1-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v12.11.1/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v12.11.1/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v12.11.1/node-v12.11.1.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v12.11.1/node-v12.11.1-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v12.11.1/node-v12.11.1-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v12.11.1/node-v12.11.1-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v12.11.1/node-v12.11.1-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v12.11.1/node-v12.11.1-aix-ppc64.tar.gz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v12.11.1/node-v12.11.1-sunos-x64.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v12.11.1/node-v12.11.1-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v12.11.1/node-v12.11.1-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v12.11.1/node-v12.11.1.tar.gz \
Other release files: https://nodejs.org/dist/v12.11.1/ \
Documentation: https://nodejs.org/docs/v12.11.1/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

5242f490a320584dbfb21a7009fe6c316d43cffc9fa661ee852bdbb0875e27f4  node-v12.11.1-aix-ppc64.tar.gz
7dd24ee6d81668e65ce1b77b4bb4cdaf517d8f80bb19740d286606028506970b  node-v12.11.1-darwin-x64.tar.gz
ad265269189a5f29db107e8d69e0ec255ee5815da349a637df44db7ef987a95c  node-v12.11.1-darwin-x64.tar.xz
4bb4754fa82dc1d9e1600b0855c410320719b536257c9c9def120b31fa1f8528  node-v12.11.1-headers.tar.gz
2a87283189530328ccc7c1ac63f21979870fe10152f19fe1a990a9673288daa2  node-v12.11.1-headers.tar.xz
a9973aeb9f942b4ffa8fe40149dfb3e0ddf9377049fc3cc7e789c5dfdc22ffd0  node-v12.11.1-linux-arm64.tar.gz
12777294258da80410fd7d5cbed46ead5d3cabacf376ee10ddb9e1a335b114e7  node-v12.11.1-linux-arm64.tar.xz
e6e52f71420bc959a1936db26084d2d78bc6767d4a483c5998afbc4bf999012c  node-v12.11.1-linux-armv7l.tar.gz
8fffbe516021fc0f1d6beb2ba8d221b04af6d8880e1b74681f9ffd96545f896d  node-v12.11.1-linux-armv7l.tar.xz
13a5fda091794604fdf2965b04fdc76c311c83c41b98533c8f91d0d1bebaf30a  node-v12.11.1-linux-ppc64le.tar.gz
145d7b3fabbf3218e2685d34478b87567571eddb7f3d357ec6cd9eb20a2fce05  node-v12.11.1-linux-ppc64le.tar.xz
b16a0a01ee002589669ad665ede842465890a969b0f1a463560d400d9432317b  node-v12.11.1-linux-s390x.tar.gz
1315f896d8095e4dbf2bc438974df7018428d52011abd20439c9264fccda13cf  node-v12.11.1-linux-s390x.tar.xz
ac6c76af7c13cc3688aba072c4c728cb6fa2c40b340b1dcc4795e2705b1869dc  node-v12.11.1-linux-x64.tar.gz
00f7a0b59ff38c1c74d81732df925aa5ac5788b58412437327421f796878793d  node-v12.11.1-linux-x64.tar.xz
8b42fa40fb96756dabfc43f7a69eaf4e10e5b78db3094dcc5469207f21992eb3  node-v12.11.1.pkg
67c94186141c8ba8d87b73c2d9d7443a11471ed7b67cd897d99ddf93b75f98c1  node-v12.11.1-sunos-x64.tar.gz
9cbff79397a3ff39acdfbcb431b4b99353856ddbccea7fd8eb495217fa60e32b  node-v12.11.1-sunos-x64.tar.xz
37043ddbe60f18a52ec31e0381b66adebbc0ec24669d72f132a1fdfacdf029ae  node-v12.11.1.tar.gz
e53fbc7985ab8a34fe895852f30baee3f9f590ff9c2569e1bce791f78eec24ee  node-v12.11.1.tar.xz
0fc231561e9e2a83fb2329a8d0f5ade4977c1baf3cba96b80b24cc4c686b46bd  node-v12.11.1-win-x64.7z
0bab4473cd2ba03511b8859ddf2202bb012d5c541f9d57b555a5bbbf101fcb35  node-v12.11.1-win-x64.zip
4d890c580fb9c2a3e9860fb973cb7f410a7a083d7b286f17892567229c7e0563  node-v12.11.1-win-x86.7z
c60aace3faaaa061206c02be730c4b8a2534e5dda6e1a987123548e747c0165a  node-v12.11.1-win-x86.zip
4d1d6a9ba99e83aedc4c62c2621eeb5304c7b7ce2a98c3fc57d420c28960374a  node-v12.11.1-x64.msi
3406839dcdcc7394330f0085f79839204acac701ce9b4dd3cab970613e928693  node-v12.11.1-x86.msi
85d36856be4cc71ecd31792cccc44dfa98099fb701d34842a1ef70af873e24ab  win-x64/node.exe
af5a725e43d1b2db398f1de782bf777ff2b67a28970c10166f79b74c28700a77  win-x64/node.lib
e6b84807ea0d055625e3ea0452c77ff5bb36fe284bfde38d2ed50c33c3b7bf61  win-x64/node_pdb.7z
37da86c4e63ee664bd5c398efeb5cb347e0f5e1377f3e0eba36a539edcab0a8c  win-x64/node_pdb.zip
6cb03afe988be0a314061fbd13b1d55b0e7f87557a6c12eb7d7590cc1b5869b5  win-x86/node.exe
a6d2a3240366b683aaedbe58a5a0a637b3bd1054ae485c22d25dbb83d590ea29  win-x86/node.lib
216a1a8ccb7d8e098338514d2a1c7d4cb0f99ed6055002b6e8504a571b38a10b  win-x86/node_pdb.7z
e7c83fe80935af51668dd530e0c0a78e61aa9a86bd4671676bf1fcf36904fbf5  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEj8yhP+8dDC6RAI4Jdw96mlrhVgAFAl2TgpoACgkQdw96mlrh
VgD1aA/5ARBTbVpAm8v/0FzgjfjkVZNnFcaXQDJ+HQsGUVTowPyzfg2w5TB62jp8
wzCeCA1dkzzBAhrQzMyxg+rUw+WzvNkuM75/+KCMzRw873VxP+wHk+2wLAnjfdBQ
/Qnprk+C3ovqSRkeDEG2OMlV6hGZmsw/EdL5CvuY39lGpMrNkkmKKokxRU7QS0TK
xcyd4zxRIXYFvdxr17ioHJYHIbwEXZeEWpRQb6VdGa9hAd6EPCMCgRXTzOJ2BJPT
TCg8hq58KanWDZqIehwLpF/x4dHDLl5e71GPkP+9UPTfodoFZfUiBqDYNjJcxAwM
j99v46hp0/q41uySIFw7tB9hh7XysvQyK9/ipEb8tDh7w35k0US7tr6yjnQuOm/3
Y7o3wUkzltBAtOpij4hA2Ivb1munt1lNuSSp279SqLp6w3ynbFFn6ktTZx7AFrkE
27mIMvWL7nqW5jmCgeQMxC7hjRpcwk0Tg8F/VsFo6ETiwdZTL7ofsfUWB7deKA1a
vb2ozsuYphmQM3eY9lcqC0SykaWb2ZxO+73o7lFKXJItdgkZ86k5+UnQIkA/Q1cT
9W0vgmUk1DbBMp0e7pQ882BNbSQpZ1Gj3e07JnipZ5WyaClgSDOVh44yz5mg7FUF
WOP3QKycGMEpHjWXsPxJeG+c5Y09TEEJIFAzr39rfCRJ7mb4Oow=
=Q3Q2
-----END PGP SIGNATURE-----

```
