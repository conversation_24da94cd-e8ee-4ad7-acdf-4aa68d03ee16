---
date: '2017-02-01T01:04:43.112Z'
category: release
title: Node v4.7.3 (LTS)
layout: blog-post
author: <PERSON><PERSON>
---

### Notable Changes

- **deps**: upgrade openssl sources to 1.0.2k (<PERSON><PERSON><PERSON>) [#11021](https://github.com/nodejs/node/pull/11021)

### Commits

- [[`8029f64135`](https://github.com/nodejs/node/commit/8029f64135)] - **deps**: update openssl asm and asm_obsolete files (Shige<PERSON> Oh<PERSON>) [#11021](https://github.com/nodejs/node/pull/11021)
- [[`0081659a41`](https://github.com/nodejs/node/commit/0081659a41)] - **deps**: add -no_rand_screen to openssl s_client (Shige<PERSON> Ohtsu) [nodejs/io.js#1836](https://github.com/nodejs/io.js/pull/1836)
- [[`e55c3f4e21`](https://github.com/nodejs/node/commit/e55c3f4e21)] - **deps**: fix asm build error of openssl in x86_win32 (Shigeki <PERSON>) [iojs/io.js#1389](https://github.com/iojs/io.js/pull/1389)
- [[`24640f9278`](https://github.com/nodejs/node/commit/24640f9278)] - **deps**: fix openssl assembly error on ia32 win32 (Fedor Indutny) [iojs/io.js#1389](https://github.com/iojs/io.js/pull/1389)
- [[`6c7bdf58e0`](https://github.com/nodejs/node/commit/6c7bdf58e0)] - **deps**: copy all openssl header files to include dir (Shigeki Ohtsu) [#11021](https://github.com/nodejs/node/pull/11021)
- [[`c80844769c`](https://github.com/nodejs/node/commit/c80844769c)] - **deps**: upgrade openssl sources to 1.0.2k (Shigeki Ohtsu) [#11021](https://github.com/nodejs/node/pull/11021)
- [[`e3915a415b`](https://github.com/nodejs/node/commit/e3915a415b)] - **openssl**: fix keypress requirement in apps on win32 (Shigeki Ohtsu) [iojs/io.js#1389](https://github.com/iojs/io.js/pull/1389)

Windows 32-bit Installer: https://nodejs.org/dist/v4.7.3/node-v4.7.3-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v4.7.3/node-v4.7.3-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v4.7.3/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v4.7.3/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v4.7.3/node-v4.7.3.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v4.7.3/node-v4.7.3-darwin-x64.tar.gz \
Linux 32-bit Binary: https://nodejs.org/dist/v4.7.3/node-v4.7.3-linux-x86.tar.xz \
Linux 64-bit Binary: https://nodejs.org/dist/v4.7.3/node-v4.7.3-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v4.7.3/node-v4.7.3-linux-ppc64le.tar.xz \
Linux PPC BE 64-bit Binary: https://nodejs.org/dist/v4.7.3/node-v4.7.3-linux-ppc64.tar.xz \
SmartOS 32-bit Binary: https://nodejs.org/dist/v4.7.3/node-v4.7.3-sunos-x86.tar.xz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v4.7.3/node-v4.7.3-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v4.7.3/node-v4.7.3-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v4.7.3/node-v4.7.3-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v4.7.3/node-v4.7.3-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v4.7.3/node-v4.7.3.tar.gz \
Other release files: https://nodejs.org/dist/v4.7.3/ \
Documentation: https://nodejs.org/docs/v4.7.3/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

1d69b0919250cac5997be5623eb916ae7087f5c91c8bad17afffe32ac1cbced8  node-v4.7.3-darwin-x64.tar.gz
2b711d96678537a91686b3deda4ac4c30702048e351219db3bdf40f1b12217ab  node-v4.7.3-darwin-x64.tar.xz
4c9c36f1bec9e31e20df7a7baa4d53fe54ca2effa05dcbaf3f916f2dc411ddf0  node-v4.7.3-headers.tar.gz
aec18e4360154770f9ab3d4a5b1c76f1dd0eabb509982c3a353d685c99b2d527  node-v4.7.3-headers.tar.xz
4d3199e69259452d1e1cfc06da851e8c26590613f845a997d613228954225124  node-v4.7.3-linux-arm64.tar.gz
4b963bba23c9f8dc7172c7d528f57cffe79b2b0b6131f35e60d38ecaab8cc199  node-v4.7.3-linux-arm64.tar.xz
e6d8e00b8c8afe046e99759b2b450d0fc09ea12ff8ff5ac9707b7c18e159b5bd  node-v4.7.3-linux-armv6l.tar.gz
8ebdeb1cf4002474a39557790c8f5f4396ee43965e08b801ca9c285ae8a9a895  node-v4.7.3-linux-armv6l.tar.xz
954b51068273cf228f831b1f701a47dd14268be108f8a6c89d4d49413c63935f  node-v4.7.3-linux-armv7l.tar.gz
c3dd8964f2e063e0b77f0095c7e8b42d427f2a9bd2ae5965a2fe009993a56419  node-v4.7.3-linux-armv7l.tar.xz
9a53a29314e6b929eb94eb4ca81720f306eb51399eb42a63ae8e643245387718  node-v4.7.3-linux-ppc64le.tar.gz
ffb91e0b38a476ac87e9050d9567e465fc77910b4052026008926faa1d85db04  node-v4.7.3-linux-ppc64le.tar.xz
5ab32ef9e5908f3ace49976b4bdd628df70ebfea39b35ee9e681f39a3d429078  node-v4.7.3-linux-ppc64.tar.gz
53b374cbe3d4bd969f9c6624bb8da4d54fe787cd8acf6460eb448ade500d47d4  node-v4.7.3-linux-ppc64.tar.xz
720b62ed3a733578c429448c8c373743866f55db6e9763a11b87bb324740a33b  node-v4.7.3-linux-x64.tar.gz
7f53fa4b6de5d4ba131bcaff10bb24e73f6f14486fc7333fbcebbc217874c0c1  node-v4.7.3-linux-x64.tar.xz
7cca3b061ff2791c46303655fdd57a85223bc2382a44a39b90edff43434e34c6  node-v4.7.3-linux-x86.tar.gz
8e6a2351f4876fb141755379ea9c23dc825f931d130c68c749d705dbcb7be1ca  node-v4.7.3-linux-x86.tar.xz
99a4ba7e59dcb1cc0229df9f9ce3b4835492338ea5e3c4e67151f5ac88a569c8  node-v4.7.3.pkg
3ab3a2123639ee18a4fc5253f2fbfcbd7a43404fe34d1cf51568d45838a3b15f  node-v4.7.3-sunos-x64.tar.gz
c4b577ce25bdc70532b195b017c5715024195cd5ed829cebfaabadc55f035d0c  node-v4.7.3-sunos-x64.tar.xz
9efef37f072ef48444d15faeec8f4ab8af7e6c71a03e47031b76781929854062  node-v4.7.3-sunos-x86.tar.gz
877ae980f0152c136a4fdcb04ad1e6cbdf9834944ba1e83b5e31575616ae41ab  node-v4.7.3-sunos-x86.tar.xz
1549d4e0aeb9419ddb85ba4dffdba8d0e2f9a9b904e80765b044c4f7d9f485ed  node-v4.7.3.tar.gz
7f35dc7adf0b348857b98e543a54c2b2cafb92c27d9d32799a81ac66cbf035f0  node-v4.7.3.tar.xz
816960d115fb3cc41356397d83c8fbe3c105caad919b9f0307bc009997d84008  node-v4.7.3-win-x64.7z
09750d65972c22759bc57276c10bd191207cd121a081e1a855cc6d34c59e0e76  node-v4.7.3-win-x64.zip
e34738b8af5fa6b3d27b8935a2765be13d75a7a1f60273aee7a03253eb0f271c  node-v4.7.3-win-x86.7z
a4da7000b41433da3944db9bf31e38c8984af5188f77ca76c5fc7bb8a8866e2e  node-v4.7.3-win-x86.zip
a8955cc0578214ab0a303332c565bd2df35db42a4b4c7f9c6e898197ac6645b8  node-v4.7.3-x64.msi
09bc8958dc4d5a074a3829a2446faa5de3ec30f249fde46a54591062e900d02e  node-v4.7.3-x86.msi
89e92970d4f3f0ef5925c2ee769e875c30fa7fcfb209df96acfe0ff2da2a774a  win-x64/node.exe
f50c7fa61ae78067ca7b462a6e8e361bd571f2cbc2116b0d86116d5e686c56d3  win-x64/node.lib
74ce34398a2633c71ca16b260957ef49c8e0e36ed468770067800232c30cfbb6  win-x64/node_pdb.7z
28ef69cf8e6c1e60e49c89d9060d677cbf2d24e6b27df2cfa2d40a474d786a05  win-x64/node_pdb.zip
f7ec11abe97babe7230b9aa6b67f8be104c1f4bb923bd0bcb5e7ddff4bf2f5f4  win-x86/node.exe
f5d00b3ec2f9cfc236cfad7568b1f0acc632eae7815df61b8eaceea71c4732a4  win-x86/node.lib
37daa3c3dc6a261e59bf01f40ca1686ebcf102774a2c186029b9e5cccd1156aa  win-x86/node_pdb.7z
ab4c08a25531e54906da9d239b0a14e663be976805dcb07c47487dd68e333185  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQEcBAEBCAAGBQJYkTJRAAoJEJM7AfQLXKlGop4H/RfU8QjEdDgmoqe/pFQA9mVN
SvA6D5rfKADdnVN/aJdcKkjF/pLTOSfo1H7f8isaq5K/uBSrZSzfTT6KDP9gCVNE
8Bxeqi8IiWf3gU3QkworvqY7auvFINCmZ50xbnWUXZKIraAb9ayZIL6EyvSD6tbs
KzsO46ixHroxmM4xTjN+UOOJIDpb3QgLlNLnZaFixuWYeIduXCrFcm0yGvF9F5bW
3TsyIZeuO6gJiEvTGvLRDs4FB38vKwba2n2zykvsTmBHrE13uKmveF97q8+GXJBm
sPaH6foKSkWzUFYxK4GnmotUa7v4qfzcdkm/kdbn/Ukiol7s/idJYthXJzzW/MQ=
=+yW2
-----END PGP SIGNATURE-----

```
