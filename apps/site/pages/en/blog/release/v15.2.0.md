---
date: '2020-11-10T23:00:05.544Z'
category: release
title: Node v15.2.0 (Current)
layout: blog-post
author: <PERSON>
---

### Notable changes

- **events**:
  - getEventListeners static (<PERSON>) [#35991](https://github.com/nodejs/node/pull/35991)
- **fs**:
  - support abortsignal in writeFile (<PERSON>) [#35993](https://github.com/nodejs/node/pull/35993)
  - add support for AbortSignal in readFile (<PERSON>) [#35911](https://github.com/nodejs/node/pull/35911)
- **stream**:
  - fix thrown object reference (<PERSON>) [#36065](https://github.com/nodejs/node/pull/36065)

### Commits

- [[`9d9a044c1b`](https://github.com/nodejs/node/commit/9d9a044c1b)] - **benchmark**: ignore build artifacts for napi addons (<PERSON>) [#35970](https://github.com/nodejs/node/pull/35970)
- [[`4c6de854be`](https://github.com/nodejs/node/commit/4c6de854be)] - **benchmark**: remove modules that require intl (Richard Lau) [#35968](https://github.com/nodejs/node/pull/35968)
- [[`292915a6a8`](https://github.com/nodejs/node/commit/292915a6a8)] - **bootstrap**: refactor to use more primordials (Antoine du Hamel) [#35999](https://github.com/nodejs/node/pull/35999)
- [[`10c9ea771d`](https://github.com/nodejs/node/commit/10c9ea771d)] - **build**: fix zlib inlining for IA-32 (raisinten) [#35679](https://github.com/nodejs/node/pull/35679)
- [[`6ac9c8f31b`](https://github.com/nodejs/node/commit/6ac9c8f31b)] - **build, tools**: look for local installation of NASM (Richard Lau) [#36014](https://github.com/nodejs/node/pull/36014)
- [[`9757b47c44`](https://github.com/nodejs/node/commit/9757b47c44)] - **console**: use more primordials (Antoine du Hamel) [#35734](https://github.com/nodejs/node/pull/35734)
- [[`0d7422651b`](https://github.com/nodejs/node/commit/0d7422651b)] - **crypto**: refactor to use more primordials (Antoine du Hamel) [#36012](https://github.com/nodejs/node/pull/36012)
- [[`dc4936ba50`](https://github.com/nodejs/node/commit/dc4936ba50)] - **crypto**: fix comment in ByteSource (Tobias Nießen) [#35972](https://github.com/nodejs/node/pull/35972)
- [[`7cb5c0911e`](https://github.com/nodejs/node/commit/7cb5c0911e)] - **deps**: cherry-pick 9a49b22 from V8 upstream (Daniel Bevenius) [#35939](https://github.com/nodejs/node/pull/35939)
- [[`4b03670877`](https://github.com/nodejs/node/commit/4b03670877)] - **dns**: fix trace_events name for resolveCaa() (Rich Trott) [#35979](https://github.com/nodejs/node/pull/35979)
- [[`dcb27600da`](https://github.com/nodejs/node/commit/dcb27600da)] - **doc**: escape asterisk in cctest gtest-filter (raisinten) [#36034](https://github.com/nodejs/node/pull/36034)
- [[`923276ca53`](https://github.com/nodejs/node/commit/923276ca53)] - **doc**: move v8.getHeapCodeStatistics() (Rich Trott) [#36027](https://github.com/nodejs/node/pull/36027)
- [[`71fa9c6b24`](https://github.com/nodejs/node/commit/71fa9c6b24)] - **doc**: add note regarding file structure in src/README.md (Denys Otrishko) [#35000](https://github.com/nodejs/node/pull/35000)
- [[`99cb36238d`](https://github.com/nodejs/node/commit/99cb36238d)] - **doc**: advise users to import the full set of trusted release keys (Reşat SABIQ) [#32655](https://github.com/nodejs/node/pull/32655)
- [[`06cc400160`](https://github.com/nodejs/node/commit/06cc400160)] - **doc**: fix crypto doc linter errors (Antoine du Hamel) [#36035](https://github.com/nodejs/node/pull/36035)
- [[`01129a7b39`](https://github.com/nodejs/node/commit/01129a7b39)] - **doc**: revise v8.getHeapSnapshot() (Rich Trott) [#35849](https://github.com/nodejs/node/pull/35849)
- [[`77d33c9b2f`](https://github.com/nodejs/node/commit/77d33c9b2f)] - **doc**: update core-validate-commit link in guide (Daijiro Wachi) [#35938](https://github.com/nodejs/node/pull/35938)
- [[`6d56ba03e2`](https://github.com/nodejs/node/commit/6d56ba03e2)] - **doc**: update benchmark CI test indicator in README (Rich Trott) [#35945](https://github.com/nodejs/node/pull/35945)
- [[`8bd364a9b3`](https://github.com/nodejs/node/commit/8bd364a9b3)] - **doc**: add new wordings to the API description (Pooja D.P) [#35588](https://github.com/nodejs/node/pull/35588)
- [[`acd3617e1a`](https://github.com/nodejs/node/commit/acd3617e1a)] - **doc**: option --prof documentation help added (krank2me) [#34991](https://github.com/nodejs/node/pull/34991)
- [[`6968b0fd49`](https://github.com/nodejs/node/commit/6968b0fd49)] - **doc**: fix release-schedule link in backport guide (Daijiro Wachi) [#35920](https://github.com/nodejs/node/pull/35920)
- [[`efbfeff62b`](https://github.com/nodejs/node/commit/efbfeff62b)] - **doc**: fix incorrect heading level (Bryan Field) [#35965](https://github.com/nodejs/node/pull/35965)
- [[`9c4b360d08`](https://github.com/nodejs/node/commit/9c4b360d08)] - **doc,crypto**: added sign/verify method changes about dsaEncoding (Filip Skokan) [#35480](https://github.com/nodejs/node/pull/35480)
- [[`85cf30541d`](https://github.com/nodejs/node/commit/85cf30541d)] - **doc,fs**: document value of stats.isDirectory on symbolic links (coderaiser) [#27413](https://github.com/nodejs/node/pull/27413)
- [[`d6bd78ff82`](https://github.com/nodejs/node/commit/d6bd78ff82)] - **doc,net**: document socket.timeout (Brandon Kobel) [#34543](https://github.com/nodejs/node/pull/34543)
- [[`36c20d939a`](https://github.com/nodejs/node/commit/36c20d939a)] - **doc,stream**: write(chunk, encoding, cb) encoding can be null (dev-script) [#35372](https://github.com/nodejs/node/pull/35372)
- [[`9d26c4d496`](https://github.com/nodejs/node/commit/9d26c4d496)] - **domain**: refactor to use more primordials (Antoine du Hamel) [#35885](https://github.com/nodejs/node/pull/35885)
- [[`d83e253065`](https://github.com/nodejs/node/commit/d83e253065)] - **errors**: refactor to use more primordials (Antoine du Hamel) [#35944](https://github.com/nodejs/node/pull/35944)
- [[`567f8d8caf`](https://github.com/nodejs/node/commit/567f8d8caf)] - **(SEMVER-MINOR)** **events**: getEventListeners static (Benjamin Gruenbaum) [#35991](https://github.com/nodejs/node/pull/35991)
- [[`9e673723e3`](https://github.com/nodejs/node/commit/9e673723e3)] - **events**: fire handlers in correct oder (Benjamin Gruenbaum) [#35931](https://github.com/nodejs/node/pull/35931)
- [[`ff59fcdf7b`](https://github.com/nodejs/node/commit/ff59fcdf7b)] - **events**: define abort on prototype (Benjamin Gruenbaum) [#35931](https://github.com/nodejs/node/pull/35931)
- [[`ab0eb4f2c9`](https://github.com/nodejs/node/commit/ab0eb4f2c9)] - **events**: support event handlers on prototypes (Benjamin Gruenbaum) [#35931](https://github.com/nodejs/node/pull/35931)
- [[`33e2ee58a7`](https://github.com/nodejs/node/commit/33e2ee58a7)] - **events**: define event handler as enumerable (Benjamin Gruenbaum) [#35931](https://github.com/nodejs/node/pull/35931)
- [[`a7d0c76f86`](https://github.com/nodejs/node/commit/a7d0c76f86)] - **events**: support emit on nodeeventtarget (Benjamin Gruenbaum) [#35851](https://github.com/nodejs/node/pull/35851)
- [[`76332a0439`](https://github.com/nodejs/node/commit/76332a0439)] - **events**: port some wpt tests (Benjamin Gruenbaum) [#33621](https://github.com/nodejs/node/pull/33621)
- [[`ccf9f0e62e`](https://github.com/nodejs/node/commit/ccf9f0e62e)] - **(SEMVER-MINOR)** **fs**: support abortsignal in writeFile (Benjamin Gruenbaum) [#35993](https://github.com/nodejs/node/pull/35993)
- [[`7ef9c707e9`](https://github.com/nodejs/node/commit/7ef9c707e9)] - **fs**: replace finally with PromisePrototypeFinally (Baruch Odem (Rothkoff)) [#35995](https://github.com/nodejs/node/pull/35995)
- [[`ccbe267515`](https://github.com/nodejs/node/commit/ccbe267515)] - **fs**: remove unnecessary Function#bind() in fs/promises (Ben Noordhuis) [#35208](https://github.com/nodejs/node/pull/35208)
- [[`6011bfdec5`](https://github.com/nodejs/node/commit/6011bfdec5)] - **fs**: remove unused assignment (Rich Trott) [#35882](https://github.com/nodejs/node/pull/35882)
- [[`92bdfd141b`](https://github.com/nodejs/node/commit/92bdfd141b)] - **(SEMVER-MINOR)** **fs**: add support for AbortSignal in readFile (Benjamin Gruenbaum) [#35911](https://github.com/nodejs/node/pull/35911)
- [[`11f592450b`](https://github.com/nodejs/node/commit/11f592450b)] - **http2**: add has method to proxySocketHandler (masx200) [#35197](https://github.com/nodejs/node/pull/35197)
- [[`28ed7d062e`](https://github.com/nodejs/node/commit/28ed7d062e)] - **http2**: centralise socket event binding in Http2Session (Momtchil Momtchev) [#35772](https://github.com/nodejs/node/pull/35772)
- [[`429113ebfb`](https://github.com/nodejs/node/commit/429113ebfb)] - **http2**: move events to the JSStreamSocket (Momtchil Momtchev) [#35772](https://github.com/nodejs/node/pull/35772)
- [[`1dd744a420`](https://github.com/nodejs/node/commit/1dd744a420)] - **http2**: fix error stream write followed by destroy (David Halls) [#35951](https://github.com/nodejs/node/pull/35951)
- [[`af2a560c42`](https://github.com/nodejs/node/commit/af2a560c42)] - **lib**: add %TypedArray% abstract constructor to primordials (ExE Boss) [#36016](https://github.com/nodejs/node/pull/36016)
- [[`b700900d02`](https://github.com/nodejs/node/commit/b700900d02)] - **lib**: refactor to use more primordials (Antoine du Hamel) [#35875](https://github.com/nodejs/node/pull/35875)
- [[`7a375902ff`](https://github.com/nodejs/node/commit/7a375902ff)] - **module**: refactor to use more primordials (Antoine du Hamel) [#36024](https://github.com/nodejs/node/pull/36024)
- [[`8d76db86b5`](https://github.com/nodejs/node/commit/8d76db86b5)] - **module**: refactor to use iterable-weak-map (Benjamin Coe) [#35915](https://github.com/nodejs/node/pull/35915)
- [[`9b6512f7de`](https://github.com/nodejs/node/commit/9b6512f7de)] - **n-api**: unlink reference during its destructor (Gabriel Schulhof) [#35933](https://github.com/nodejs/node/pull/35933)
- [[`1b277d97f3`](https://github.com/nodejs/node/commit/1b277d97f3)] - **src**: remove ERR prefix in crypto status enums (Daniel Bevenius) [#35867](https://github.com/nodejs/node/pull/35867)
- [[`9774b4cc72`](https://github.com/nodejs/node/commit/9774b4cc72)] - **stream**: fix thrown object reference (Gil Pedersen) [#36065](https://github.com/nodejs/node/pull/36065)
- [[`359a6590b0`](https://github.com/nodejs/node/commit/359a6590b0)] - **stream**: writableNeedDrain (Robert Nagy) [#35348](https://github.com/nodejs/node/pull/35348)
- [[`b7aa5e2296`](https://github.com/nodejs/node/commit/b7aa5e2296)] - **stream**: remove isPromise utility function (Antoine du Hamel) [#35925](https://github.com/nodejs/node/pull/35925)
- [[`fdae9ad188`](https://github.com/nodejs/node/commit/fdae9ad188)] - **test**: fix races in test-performance-eventlooputil (Gerhard Stoebich) [#36028](https://github.com/nodejs/node/pull/36028)
- [[`0a4c96a7df`](https://github.com/nodejs/node/commit/0a4c96a7df)] - **test**: use global.EventTarget instead of internals (Antoine du Hamel) [#36002](https://github.com/nodejs/node/pull/36002)
- [[`f73b8d84db`](https://github.com/nodejs/node/commit/f73b8d84db)] - **test**: improve error message for policy failures (Bradley Meck) [#35633](https://github.com/nodejs/node/pull/35633)
- [[`cb6f0d3d89`](https://github.com/nodejs/node/commit/cb6f0d3d89)] - **test**: update old comment style test_util.cc (raisinten) [#35884](https://github.com/nodejs/node/pull/35884)
- [[`23f0d0c45c`](https://github.com/nodejs/node/commit/23f0d0c45c)] - **test**: fix error in test/internet/test-dns.js (Rich Trott) [#35969](https://github.com/nodejs/node/pull/35969)
- [[`77e4f19701`](https://github.com/nodejs/node/commit/77e4f19701)] - **timers**: cleanup abort listener on awaitable timers (James M Snell) [#36006](https://github.com/nodejs/node/pull/36006)
- [[`a7350b3a8f`](https://github.com/nodejs/node/commit/a7350b3a8f)] - **tools**: don't print gold linker warning w/o flag (Myles Borins) [#35955](https://github.com/nodejs/node/pull/35955)
- [[`1f27214480`](https://github.com/nodejs/node/commit/1f27214480)] - **tools**: add new ESLint rule: prefer-primordials (Leko) [#35448](https://github.com/nodejs/node/pull/35448)
- [[`da3c2ab828`](https://github.com/nodejs/node/commit/da3c2ab828)] - **tools,doc**: enable ecmaVersion 2021 in acorn parser (Antoine du Hamel) [#35994](https://github.com/nodejs/node/pull/35994)
- [[`f8098c3e43`](https://github.com/nodejs/node/commit/f8098c3e43)] - **tools,lib**: recommend using safe primordials (Antoine du Hamel) [#36026](https://github.com/nodejs/node/pull/36026)
- [[`eea7e3b0d0`](https://github.com/nodejs/node/commit/eea7e3b0d0)] - **tools,lib**: tighten prefer-primordials rules for Error statics (Antoine du Hamel) [#36017](https://github.com/nodejs/node/pull/36017)
- [[`7a2edea7ed`](https://github.com/nodejs/node/commit/7a2edea7ed)] - **win, build**: fix build time on Windows (Bartosz Sosnowski) [#35932](https://github.com/nodejs/node/pull/35932)

Windows 32-bit Installer: https://nodejs.org/dist/v15.2.0/node-v15.2.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v15.2.0/node-v15.2.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v15.2.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v15.2.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v15.2.0/node-v15.2.0.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v15.2.0/node-v15.2.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v15.2.0/node-v15.2.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v15.2.0/node-v15.2.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v15.2.0/node-v15.2.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v15.2.0/node-v15.2.0-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v15.2.0/node-v15.2.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v15.2.0/node-v15.2.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v15.2.0/node-v15.2.0.tar.gz \
Other release files: https://nodejs.org/dist/v15.2.0/ \
Documentation: https://nodejs.org/docs/v15.2.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

72d8f863c0e6aa2a971d4ea19d8da1a84f28fc21d5532f8e284a2cfe62e07928  node-v15.2.0-aix-ppc64.tar.gz
31cd7d98b2eeddf0895e75b650d005af0f4103d6ce54a93554b32080a0b79780  node-v15.2.0-darwin-x64.tar.gz
858ad0e4331a3bab81d17f287073357dbb14bf659782455d5afa5ed28b583540  node-v15.2.0-darwin-x64.tar.xz
43c1be0b0bbcfcd460e6a6ed7341219042b168b1c9195c0fc0cca301d9a0972c  node-v15.2.0-headers.tar.gz
3d86fd77079a9e23997aa79ba221e785253276a8262d1cfcad3264aefc97f369  node-v15.2.0-headers.tar.xz
c8203934787e3e7ab136eff96689d04abedda5e037785a55fdc26a43bbfd867d  node-v15.2.0-linux-arm64.tar.gz
3c3b12366023c895328a05d9c43842b4a26c372000ee351f02ee90a8844a5211  node-v15.2.0-linux-arm64.tar.xz
9173a50f52f5b3f9af6a5143d4bb42e1a38e0e4f3b785afcaf797845fd7d922f  node-v15.2.0-linux-armv7l.tar.gz
7935ff1e6f0f05619994df42ae4dca01045951d47ae8bfb0ea0329e41d855284  node-v15.2.0-linux-armv7l.tar.xz
d43ae7aee5f8fce23e1c4100740b6d7db6407b0322dfcb564c71267a062c4389  node-v15.2.0-linux-ppc64le.tar.gz
178fe628c0cda69b80f5ef70db69787cedec58811c1e6af6f66a6a3ab0d87ba2  node-v15.2.0-linux-ppc64le.tar.xz
890329de43bc725617ba8cdbd9c71795cd6608e8a82ef3ff8206df653b56f0fb  node-v15.2.0-linux-s390x.tar.gz
9768d32c762b876964db640a0108918a61ba31840d04a6fd1deb4f648a011079  node-v15.2.0-linux-s390x.tar.xz
c23d26e9f6dce4543be39eff8e97b9871c40773d06b76c42b4b5e4f94d417962  node-v15.2.0-linux-x64.tar.gz
3eb7c8e991af347c87817d9c1e1e16efaf31dfbd95b35fbd404d598fb4b14739  node-v15.2.0-linux-x64.tar.xz
5c14788400e6085d4e46b6c64cae729065c86f5b80cd387a49f637d1a1116469  node-v15.2.0.pkg
a4c807cebda11a5f9ec50f340b0a916fa64423864c553c6f56da4404ece7a384  node-v15.2.0.tar.gz
f67a017a753cf6336f47307610f1954e275799883046c6f7ab8638a993e05308  node-v15.2.0.tar.xz
7eddf31e8a60af8c7f049a876ae70e841f3e9fbb04070fa351308ed6cf3e419c  node-v15.2.0-win-x64.7z
2331e3a4dbfce4edc1ba1e146de47899eef5f76d6470c433826f2b8766c0e4e4  node-v15.2.0-win-x64.zip
9c4b0ca0b3bb4eeb18f93592d571233484af6f0b3316220a39477deb09627616  node-v15.2.0-win-x86.7z
768a93ec4f6e500e7fe42223807250fe6f9a968136eaa74c3783bc56d0fc7c8e  node-v15.2.0-win-x86.zip
3a395dbc33d6f1877c6b63391b15bbbf79e41ae10954523788a262bbac866dba  node-v15.2.0-x64.msi
24f91e8d930e24426b31fee27b7f9bb75553a0819e82af65286b29f45a2cf0d0  node-v15.2.0-x86.msi
70a539fa6a4018024886f553ba448adf9c9ca47241c10a7dd30a3cb230305e77  win-x64/node.exe
1f8c07c1c667779d00d18dd046cab836ab8e54854575d8a6b414a26941eadd0e  win-x64/node.lib
df6a46a98c600b71fe6e1fb319e28cd2c10cd6ecd77c09ae0bb4dbf3cea9d76b  win-x64/node_pdb.7z
76d99b5040c8ece0b8dab4f7d74d0540c5ac6be35d9c1d02515de9e9f92085b2  win-x64/node_pdb.zip
0b0980a03a58e85e31f12781618eb369b131b045b4af0df3c62718c8add86d1b  win-x86/node.exe
59a78ea6e64aec7bee2d4bff0285f769c9b5e8ffc4ff9a1a0a42334aaf484434  win-x86/node.lib
69fdac03c1b3d31748533c6fc83bfc8ab824a4f6a13cd319bc846ab600e58869  win-x86/node_pdb.7z
60126ebfe0587e8956548540c48b0a1437d77ea595072538be229bda9b53f459  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQEzBAEBCAAdFiEEHAUImTNCRKivdeU3ku9mHYZ7nfoFAl+rGkYACgkQku9mHYZ7
nfqUywf/YlvwqnmFeKU9J4SYWDpMtdlZSDnL0VO1wuS28C22AR+6k/ooQ2dM1EFc
ZjAQnRfZPAo5Jd7reX5anL3FKAvJqkLzogBMoquh9y0Og0apd6ad+dGNXocZSNtb
nZE5bhM9vRhLUdn9zZwG4rB7/eRnJhMThUvbEy42urifgqHb82Fw1u44ZCUCZ1Ns
1+AQR10wNZsJezJLcfHaAEgx3RNEN/drYqlbDIZLqdiRipCFeIHE6p+A73O5MNlH
bs4CFHaZd64XtZMv6PM16w6XQ6gBlo1CVsoRQZbFRr8CqBhFn7fuVAgNC2JnKtXo
canp5d5G/S5U0DAA/yIxFEwx72vOtQ==
=fjiA
-----END PGP SIGNATURE-----

```
