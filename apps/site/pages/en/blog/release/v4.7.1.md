---
date: '2017-01-04T00:13:29.176Z'
category: release
title: Node v4.7.1 (LTS)
layout: blog-post
author: <PERSON><PERSON>
---

While promoting additional platforms for v4.7.1 after the release, the tarballs
on the release server were overwritten and now have different shasums. In order
to remove any ambiguity around the release we have opted to do a semver patch
release, v4.7.2, with no changes.

### Notable Changes

- **build**: shared library support is now working for AIX builds (<PERSON>) [#9675](https://github.com/nodejs/node/pull/9675)
- **repl**: Passing options to the repl will no longer overwrite defaults (cjihrig) [#7826](https://github.com/nodejs/node/pull/7826)
- **timers**: Re canceling a cancelled timers will no longer throw (<PERSON>) [#9685](https://github.com/nodejs/node/pull/9685)

### Commits

- [[`c5f82b8421`](https://github.com/nodejs/node/commit/c5f82b8421)] - **assert**: fix deepEqual/deepStrictEqual on equivalent typed arrays (<PERSON><PERSON>) [#8002](https://github.com/nodejs/node/pull/8002)
- [[`60883de30f`](https://github.com/nodejs/node/commit/60883de30f)] - **async_wrap**: call destroy() callback in uv_idle_t (<PERSON>)
- [[`28dbc460c6`](https://github.com/nodejs/node/commit/28dbc460c6)] - **async_wrap**: make Initialize a static class member (Trevor Norris)
- [[`bb05cd13db`](https://github.com/nodejs/node/commit/bb05cd13db)] - **async_wrap**: mode constructor/destructor to .cc (Trevor Norris)
- [[`b1075f6193`](https://github.com/nodejs/node/commit/b1075f6193)] - **benchmark**: split timers benchmark and refactor (Rich Trott) [#9497](https://github.com/nodejs/node/pull/9497)
- [[`7b4268b889`](https://github.com/nodejs/node/commit/7b4268b889)] - **benchmark,lib,test,tools**: remove unneeded . escape (Rich Trott) [#9449](https://github.com/nodejs/node/pull/9449)
- [[`54f2ce8ea0`](https://github.com/nodejs/node/commit/54f2ce8ea0)] - **build**: prioritise --shared-X-Y over pkg-config (Rod Vagg) [#9368](https://github.com/nodejs/node/pull/9368)
- [[`61d377ddcd`](https://github.com/nodejs/node/commit/61d377ddcd)] - **build**: Make configure file parseable on python3 (kalrover) [#9657](https://github.com/nodejs/node/pull/9657)
- [[`38e0f95d24`](https://github.com/nodejs/node/commit/38e0f95d24)] - **build**: add MAKEFLAGS="-j1" to node-gyp (Daniel Bevenius) [#9450](https://github.com/nodejs/node/pull/9450)
- [[`d1b6407395`](https://github.com/nodejs/node/commit/d1b6407395)] - **build**: make node-gyp output silent (Sakthipriyan Vairamani (thefourtheye)) [#8990](https://github.com/nodejs/node/pull/8990)
- [[`ae2eff2997`](https://github.com/nodejs/node/commit/ae2eff2997)] - **build**: start comments at beginning of line (Sakthipriyan Vairamani (thefourtheye)) [#9375](https://github.com/nodejs/node/pull/9375)
- [[`6f1f955b33`](https://github.com/nodejs/node/commit/6f1f955b33)] - **build**: default to ppc64 on AIX (Gibson Fahnestock) [#9645](https://github.com/nodejs/node/pull/9645)
- [[`f8d4577762`](https://github.com/nodejs/node/commit/f8d4577762)] - **build**: Add option to compile for coverage reports (Wayne Andrews) [#9463](https://github.com/nodejs/node/pull/9463)
- [[`f2b00985f0`](https://github.com/nodejs/node/commit/f2b00985f0)] - **build**: add shared library support to AIX build (Stewart Addison) [#9675](https://github.com/nodejs/node/pull/9675)
- [[`e2c5f41ddf`](https://github.com/nodejs/node/commit/e2c5f41ddf)] - **crypto**: use SSL_get_servername. (Adam Langley) [#9347](https://github.com/nodejs/node/pull/9347)
- [[`724910a991`](https://github.com/nodejs/node/commit/724910a991)] - **debugger**: refactor \_debugger.js (Rich Trott) [#9860](https://github.com/nodejs/node/pull/9860)
- [[`52f14931a2`](https://github.com/nodejs/node/commit/52f14931a2)] - **deps**: backport GYP fix to fix AIX shared suffix (Stewart Addison) [#9675](https://github.com/nodejs/node/pull/9675)
- [[`c77ba8ce14`](https://github.com/nodejs/node/commit/c77ba8ce14)] - **doc**: consistent 'Returns:' (Roman Reiss) [#9554](https://github.com/nodejs/node/pull/9554)
- [[`aecb2cac37`](https://github.com/nodejs/node/commit/aecb2cac37)] - **doc**: adding missing - in README (Italo A. Casas) [#10170](https://github.com/nodejs/node/pull/10170)
- [[`52c022992e`](https://github.com/nodejs/node/commit/52c022992e)] - **doc**: removing extra space in README (Italo A. Casas) [#10168](https://github.com/nodejs/node/pull/10168)
- [[`e8c57bbe77`](https://github.com/nodejs/node/commit/e8c57bbe77)] - **doc**: add people to cc for async_wrap (Anna Henningsen) [#9471](https://github.com/nodejs/node/pull/9471)
- [[`b5eae4463c`](https://github.com/nodejs/node/commit/b5eae4463c)] - **doc**: add link to `net.Server` in tls.md (Devon Rifkin) [#10109](https://github.com/nodejs/node/pull/10109)
- [[`ad841a29d1`](https://github.com/nodejs/node/commit/ad841a29d1)] - **doc**: clarify fs.createReadStream options (Wes Tyler) [#10078](https://github.com/nodejs/node/pull/10078)
- [[`338014ef24`](https://github.com/nodejs/node/commit/338014ef24)] - **doc**: rename writing_tests.md to writing-tests.md (Safia Abdalla) [#9867](https://github.com/nodejs/node/pull/9867)
- [[`b06b2343bc`](https://github.com/nodejs/node/commit/b06b2343bc)] - **doc**: it’s -> its in api/child_process.md (Devon Rifkin) [#10090](https://github.com/nodejs/node/pull/10090)
- [[`4885573080`](https://github.com/nodejs/node/commit/4885573080)] - **doc**: update Collaborators list in README (Rich Trott) [#9846](https://github.com/nodejs/node/pull/9846)
- [[`3105becb2c`](https://github.com/nodejs/node/commit/3105becb2c)] - **doc**: remove minor contradiction in debugger doc (Rich Trott) [#9832](https://github.com/nodejs/node/pull/9832)
- [[`a858e98921`](https://github.com/nodejs/node/commit/a858e98921)] - **doc**: clarify introductory module material (Rich Trott) [#9816](https://github.com/nodejs/node/pull/9816)
- [[`18c38819fe`](https://github.com/nodejs/node/commit/18c38819fe)] - **doc**: improve description of module `exports` (Sam Roberts) [#9622](https://github.com/nodejs/node/pull/9622)
- [[`9e68b8d329`](https://github.com/nodejs/node/commit/9e68b8d329)] - **doc**: fix crypto Verify cut-n-paste from Sign (子丶言) [#9796](https://github.com/nodejs/node/pull/9796)
- [[`fd1a48c9c9`](https://github.com/nodejs/node/commit/fd1a48c9c9)] - **doc**: minor fixes event-loop-timers-and-nexttick.md (Dan Koster) [#9126](https://github.com/nodejs/node/pull/9126)
- [[`107735a6e1`](https://github.com/nodejs/node/commit/107735a6e1)] - **doc**: changed order of invocations in https.request() example. (atrioom) [#9614](https://github.com/nodejs/node/pull/9614)
- [[`eb5972fe9b`](https://github.com/nodejs/node/commit/eb5972fe9b)] - **doc**: fix crypto "decipher.setAAD()" typo (子丶言) [#9782](https://github.com/nodejs/node/pull/9782)
- [[`dc4c348ea3`](https://github.com/nodejs/node/commit/dc4c348ea3)] - **doc**: fix typo in assert code example (Vse Mozhet Byt) [#9704](https://github.com/nodejs/node/pull/9704)
- [[`16e97ab6c6`](https://github.com/nodejs/node/commit/16e97ab6c6)] - **doc**: fix typo in BUILDING.md (monkick) [#9569](https://github.com/nodejs/node/pull/9569)
- [[`4f2e25441e`](https://github.com/nodejs/node/commit/4f2e25441e)] - **doc**: remove backtick escaping for manpage refs (Anna Henningsen) [#9632](https://github.com/nodejs/node/pull/9632)
- [[`c0d44dfcc7`](https://github.com/nodejs/node/commit/c0d44dfcc7)] - **doc**: remove invalid padding from privateEncrypt (JungMinu) [#9611](https://github.com/nodejs/node/pull/9611)
- [[`0f523583c3`](https://github.com/nodejs/node/commit/0f523583c3)] - **doc**: remove Sam Roberts from release team (Sam Roberts) [#9862](https://github.com/nodejs/node/pull/9862)
- [[`4eeac8eb8c`](https://github.com/nodejs/node/commit/4eeac8eb8c)] - **doc**: add guide for maintaining V8 (Ali Ijaz Sheikh) [#9777](https://github.com/nodejs/node/pull/9777)
- [[`34405ddb83`](https://github.com/nodejs/node/commit/34405ddb83)] - **doc**: move TSC and CTC meeting minutes out of core repo (James M Snell) [#9503](https://github.com/nodejs/node/pull/9503)
- [[`198463a0ff`](https://github.com/nodejs/node/commit/198463a0ff)] - **doc**: fix a typo in the assert.md (Vse Mozhet Byt) [#9598](https://github.com/nodejs/node/pull/9598)
- [[`aca0ede0d3`](https://github.com/nodejs/node/commit/aca0ede0d3)] - **doc**: fix typo e.g., => e.g. (Daijiro Yamada) [#9563](https://github.com/nodejs/node/pull/9563)
- [[`c7997939f2`](https://github.com/nodejs/node/commit/c7997939f2)] - **doc**: fix typo about cluster doc, (eg. -> e.g.) (YutamaKotaro) [#9568](https://github.com/nodejs/node/pull/9568)
- [[`229fa6921f`](https://github.com/nodejs/node/commit/229fa6921f)] - **doc**: fix e.g., to e.g. in doc/http.md (ikasumi_wt) [#9564](https://github.com/nodejs/node/pull/9564)
- [[`3ad7430f12`](https://github.com/nodejs/node/commit/3ad7430f12)] - **doc**: fix the index order in pseudocode of modules (kohta ito) [#9562](https://github.com/nodejs/node/pull/9562)
- [[`06732babd3`](https://github.com/nodejs/node/commit/06732babd3)] - **doc**: remove Roadmap Working Group (William Kapke) [#9545](https://github.com/nodejs/node/pull/9545)
- [[`6775163a94`](https://github.com/nodejs/node/commit/6775163a94)] - **doc**: fix minor style issue in code examples (Daniel Bevenius) [#9482](https://github.com/nodejs/node/pull/9482)
- [[`aa25c74fe6`](https://github.com/nodejs/node/commit/aa25c74fe6)] - **doc**: grammar and structure revisions of wg doc (Ryan Lewis) [#9495](https://github.com/nodejs/node/pull/9495)
- [[`1e06ed7e9d`](https://github.com/nodejs/node/commit/1e06ed7e9d)] - **doc**: clarify the exit code part of writing_tests (Jeremiah Senkpiel) [#9502](https://github.com/nodejs/node/pull/9502)
- [[`3f39a39657`](https://github.com/nodejs/node/commit/3f39a39657)] - **doc**: Fix inaccuracy in https.request docs (Andreas Lind) [#9453](https://github.com/nodejs/node/pull/9453)
- [[`8380154e22`](https://github.com/nodejs/node/commit/8380154e22)] - **doc**: add npm link to README (Oscar Morrison) [#7894](https://github.com/nodejs/node/pull/7894)
- [[`65e134ff12`](https://github.com/nodejs/node/commit/65e134ff12)] - **meta**: whitelist dotfiles in .gitignore (Claudio Rodriguez) [#8016](https://github.com/nodejs/node/pull/8016)
- [[`698bf2e829`](https://github.com/nodejs/node/commit/698bf2e829)] - **repl**: don't override all internal repl defaults (cjihrig) [#7826](https://github.com/nodejs/node/pull/7826)
- [[`3d45b35f73`](https://github.com/nodejs/node/commit/3d45b35f73)] - **repl**: refactor lib/repl.js (Rich Trott) [#9374](https://github.com/nodejs/node/pull/9374)
- [[`f5b952b221`](https://github.com/nodejs/node/commit/f5b952b221)] - **test**: refactor and fix test-dns (Michaël Zasso) [#9811](https://github.com/nodejs/node/pull/9811)
- [[`8b733dca05`](https://github.com/nodejs/node/commit/8b733dca05)] - **test**: refactor test-crypto-binary-default (Michaël Zasso) [#9810](https://github.com/nodejs/node/pull/9810)
- [[`45af7857d7`](https://github.com/nodejs/node/commit/45af7857d7)] - **test**: refactor and fix test-crypto (Michaël Zasso) [#9807](https://github.com/nodejs/node/pull/9807)
- [[`e0c8aafad8`](https://github.com/nodejs/node/commit/e0c8aafad8)] - **test**: fix test-buffer-slow (Michaël Zasso) [#9809](https://github.com/nodejs/node/pull/9809)
- [[`e72dfce2c8`](https://github.com/nodejs/node/commit/e72dfce2c8)] - **test**: added validation regex argument to test (Avery, Frank) [#9918](https://github.com/nodejs/node/pull/9918)
- [[`a779e7ffec`](https://github.com/nodejs/node/commit/a779e7ffec)] - **test**: clean up repl-reset-event file (Kailean Courtney) [#9931](https://github.com/nodejs/node/pull/9931)
- [[`4022579b6e`](https://github.com/nodejs/node/commit/4022579b6e)] - **test**: improve domain-top-level-error-handler-throw (CodeVana) [#9950](https://github.com/nodejs/node/pull/9950)
- [[`d3edaa3dc3`](https://github.com/nodejs/node/commit/d3edaa3dc3)] - **test**: replace var with const in test-require-dot (Amar Zavery) [#9916](https://github.com/nodejs/node/pull/9916)
- [[`8694811ef0`](https://github.com/nodejs/node/commit/8694811ef0)] - **test**: refactor test-net-pingpong (Michaël Zasso) [#9812](https://github.com/nodejs/node/pull/9812)
- [[`e849dd0ff3`](https://github.com/nodejs/node/commit/e849dd0ff3)] - **test**: Use strictEqual in test-tls-writewrap-leak (Aaron Petcoff) [#9666](https://github.com/nodejs/node/pull/9666)
- [[`0662429268`](https://github.com/nodejs/node/commit/0662429268)] - **test**: fix test-tls-connect-address-family (mkamakura) [#9573](https://github.com/nodejs/node/pull/9573)
- [[`420e7f17d9`](https://github.com/nodejs/node/commit/420e7f17d9)] - **test**: fix test-http-status-reason-invalid-chars (Yosuke Saito) [#9572](https://github.com/nodejs/node/pull/9572)
- [[`13cace140f`](https://github.com/nodejs/node/commit/13cace140f)] - **test**: fix helper-debugger-repl.js (Rich Trott) [#9486](https://github.com/nodejs/node/pull/9486)
- [[`aebbc965f9`](https://github.com/nodejs/node/commit/aebbc965f9)] - **test**: refactor large event emitter tests (cjihrig) [#6446](https://github.com/nodejs/node/pull/6446)
- [[`b5012f3de2`](https://github.com/nodejs/node/commit/b5012f3de2)] - **test**: add expectWarning to common (Michaël Zasso) [#8662](https://github.com/nodejs/node/pull/8662)
- [[`b98813d97c`](https://github.com/nodejs/node/commit/b98813d97c)] - **test**: refactor test-fs-non-number-arguments-throw (Michaël Zasso) [#9844](https://github.com/nodejs/node/pull/9844)
- [[`80a752708a`](https://github.com/nodejs/node/commit/80a752708a)] - **test**: refactor test-dgram-exclusive-implicit-bind (Cesar Hernandez) [#10066](https://github.com/nodejs/node/pull/10066)
- [[`9b974b4d54`](https://github.com/nodejs/node/commit/9b974b4d54)] - **test**: use `assert.strictEqual` (anoff) [#9975](https://github.com/nodejs/node/pull/9975)
- [[`bc125bd729`](https://github.com/nodejs/node/commit/bc125bd729)] - **test**: change assert.equal to assert.strictEqual (Aileen) [#9946](https://github.com/nodejs/node/pull/9946)
- [[`5049a10278`](https://github.com/nodejs/node/commit/5049a10278)] - **test**: changed assert.equal to assert.strictEqual (vazina robertson) [#10015](https://github.com/nodejs/node/pull/10015)
- [[`b5c60edeed`](https://github.com/nodejs/node/commit/b5c60edeed)] - **test**: renamed assert.Equal to assert.strictEqual (Jared Young)
- [[`f44e828a36`](https://github.com/nodejs/node/commit/f44e828a36)] - **test**: improves test-tls-client-verify (Paul Graham) [#10051](https://github.com/nodejs/node/pull/10051)
- [[`a1e3967f69`](https://github.com/nodejs/node/commit/a1e3967f69)] - **test**: refactor test-https-agent-session-reuse (Diego Paez) [#10105](https://github.com/nodejs/node/pull/10105)
- [[`9e46af6412`](https://github.com/nodejs/node/commit/9e46af6412)] - **test**: refactor test-beforeexit-event (Rob Adelmann) [#10121](https://github.com/nodejs/node/pull/10121)
- [[`adcd6ea66f`](https://github.com/nodejs/node/commit/adcd6ea66f)] - **test**: refactor test-domain-from-timer (Daniel Sims) [#9889](https://github.com/nodejs/node/pull/9889)
- [[`1377ea87eb`](https://github.com/nodejs/node/commit/1377ea87eb)] - **test**: refactor test-domain-exit-dispose-again (Ethan Arrowood) [#10003](https://github.com/nodejs/node/pull/10003)
- [[`8a9af6843d`](https://github.com/nodejs/node/commit/8a9af6843d)] - **test**: use const and strictEqual in test-os-homedir-no-envvar (CodeVana) [#9899](https://github.com/nodejs/node/pull/9899)
- [[`ee038c0e71`](https://github.com/nodejs/node/commit/ee038c0e71)] - **test**: refactor test-dgram-bind-default-address (Michael-Bryant Choa) [#9947](https://github.com/nodejs/node/pull/9947)
- [[`a090899e93`](https://github.com/nodejs/node/commit/a090899e93)] - **test**: assert.throws() should include a RegExp (Chris Bystrek) [#9976](https://github.com/nodejs/node/pull/997://github.com/nodejs/node/pull/9976)
- [[`542b40f410`](https://github.com/nodejs/node/commit/542b40f410)] - **test**: refactor test-event-emitter-method-names (Rodrigo Palma) [#10027](https://github.com/nodejs/node/pull/10027)
- [[`a2023a9d97`](https://github.com/nodejs/node/commit/a2023a9d97)] - **test**: refactor tls-ticket-cluster (Yojan Shrestha) [#10023](https://github.com/nodejs/node/pull/10023)
- [[`a64f40680f`](https://github.com/nodejs/node/commit/a64f40680f)] - **test**: refactor test-domain-exit-dispose (Chris Henney) [#9938](https://github.com/nodejs/node/pull/9938)
- [[`a896d4ed36`](https://github.com/nodejs/node/commit/a896d4ed36)] - **test**: refactor test-stdin-from-file.js (amrios) [#10012](https://github.com/nodejs/node/pull/10012)
- [[`ce14c1e51f`](https://github.com/nodejs/node/commit/ce14c1e51f)] - **test**: refactor test-require-extensions-main (Daryl Thayil) [#9912](https://github.com/nodejs/node/pull/9912)
- [[`b9c45026f7`](https://github.com/nodejs/node/commit/b9c45026f7)] - **test**: clean up tls junk test (Danny Guo) [#9940](https://github.com/nodejs/node/pull/9940)
- [[`e3712334a3`](https://github.com/nodejs/node/commit/e3712334a3)] - **test**: update test-stdout-to-file (scalkpdev) [#9939](https://github.com/nodejs/node/pull/9939)
- [[`63f571e69c`](https://github.com/nodejs/node/commit/63f571e69c)] - **test**: changed assert.Equal to asset.strictEqual (Paul Chin) [#9973](https://github.com/nodejs/node/pull/9973)
- [[`c3a3480606`](https://github.com/nodejs/node/commit/c3a3480606)] - **test**: refactor test-domain-multi (Wes Tyler) [#9963](https://github.com/nodejs/node/pull/9963)
- [[`ad27555ff8`](https://github.com/nodejs/node/commit/ad27555ff8)] - **test**: use assert.strictEqual in test-cli-eval (Nigel Kibodeaux) [#9919](https://github.com/nodejs/node/pull/9919)
- [[`cffd51e815`](https://github.com/nodejs/node/commit/cffd51e815)] - **test**: refactor test-tls-connect-simple (Russell Sherman) [#9934](https://github.com/nodejs/node/pull/9934)
- [[`1424c25f3e`](https://github.com/nodejs/node/commit/1424c25f3e)] - **test**: refactor test-signal-unregister (mark hughes) [#9920](https://github.com/nodejs/node/pull/9920)
- [[`920737180f`](https://github.com/nodejs/node/commit/920737180f)] - **test**: refactor test-require-resolve (blugavere) [#10120](https://github.com/nodejs/node/pull/10120)
- [[`71ab88cc80`](https://github.com/nodejs/node/commit/71ab88cc80)] - **test**: refactor test-fs-read-stream-resume (Matt Webb) [#9927](https://github.com/nodejs/node/pull/9927)
- [[`6a485da87c`](https://github.com/nodejs/node/commit/6a485da87c)] - **test**: replace equal with strictEqual (Tracy Hinds) [#10011](https://github.com/nodejs/node/pull/10011)
- [[`b5d87569e1`](https://github.com/nodejs/node/commit/b5d87569e1)] - **test**: use strictEqual instead of equal (Uttam Pawar) [#9921](https://github.com/nodejs/node/pull/9921)
- [[`c94c2fde8a`](https://github.com/nodejs/node/commit/c94c2fde8a)] - **test**: using const and strictEqual (Fabrice Tatieze) [#9926](https://github.com/nodejs/node/pull/9926)
- [[`16164b5b44`](https://github.com/nodejs/node/commit/16164b5b44)] - **test**: test-file-write-stream3.js refactor (Richard Karmazin) [#10035](https://github.com/nodejs/node/pull/10035)
- [[`7391983729`](https://github.com/nodejs/node/commit/7391983729)] - **test**: implemented es6 conventions (Erez Weiss) [#9669](https://github.com/nodejs/node/pull/9669)
- [[`50ce3f91d7`](https://github.com/nodejs/node/commit/50ce3f91d7)] - **test**: update assert.equal() to assert.strictEqual() (Peter Diaz) [#10024](https://github.com/nodejs/node/pull/10024)
- [[`3f9d75c481`](https://github.com/nodejs/node/commit/3f9d75c481)] - **test**: use const or let and assert.strictEqual (Christopher Rokita) [#10001](https://github.com/nodejs/node/pull/10001)
- [[`98afba5676`](https://github.com/nodejs/node/commit/98afba5676)] - **test**: use strictEqual() domain-http (cdnadmin) [#9996](https://github.com/nodejs/node/pull/9996)
- [[`07680b65fe`](https://github.com/nodejs/node/commit/07680b65fe)] - **test**: refactor test-cluster-worker-events (fmizzell) [#9994](https://github.com/nodejs/node/pull/9994)
- [[`a3db54416f`](https://github.com/nodejs/node/commit/a3db54416f)] - **test**: update repl tests (makenova) [#9991](https://github.com/nodejs/node/pull/9991)
- [[`db3cdd2449`](https://github.com/nodejs/node/commit/db3cdd2449)] - **test**: adding strictEqual to test-buffer-indexof.js (Eric Gonzalez) [#9955](https://github.com/nodejs/node/pull/9955)
- [[`f670b05603`](https://github.com/nodejs/node/commit/f670b05603)] - **test**: strictEqual in test-beforeexit-event.js (CodeTheInternet) [#10004](https://github.com/nodejs/node/pull/10004)
- [[`70b4d7d3a2`](https://github.com/nodejs/node/commit/70b4d7d3a2)] - **test**: refactor test-child-process-double-pipe (Dan Villa) [#9930](https://github.com/nodejs/node/pull/9930)
- [[`1e53cf4764`](https://github.com/nodejs/node/commit/1e53cf4764)] - **test**: updated test-stream-pipe-unpipe-stream (Raja Panidepu) [#10100](https://github.com/nodejs/node/pull/10100)
- [[`57d48ac3f4`](https://github.com/nodejs/node/commit/57d48ac3f4)] - **test**: refactor test-crypto-ecb (michael6) [#10029](https://github.com/nodejs/node/pull/10029)
- [[`89feb8dc4d`](https://github.com/nodejs/node/commit/89feb8dc4d)] - **test**: refactor test-require-exceptions (Oscar Martinez) [#9882](https://github.com/nodejs/node/pull/9882)
- [[`59f259c487`](https://github.com/nodejs/node/commit/59f259c487)] - **test**: refactor test-crypto-certificate (Josh Mays) [#9911](https://github.com/nodejs/node/pull/9911)
- [[`815715d850`](https://github.com/nodejs/node/commit/815715d850)] - **test**: refactor test-domain (Johnny Reading) [#9890](https://github.com/nodejs/node/pull/9890)
- [[`08cc269338`](https://github.com/nodejs/node/commit/08cc269338)] - **test**: refactor test-cli-syntax (Exlipse7) [#10057](https://github.com/nodejs/node/pull/10057)
- [[`91d27ce4db`](https://github.com/nodejs/node/commit/91d27ce4db)] - **test**: refactor test-child-process-constructor (k3kathy) [#10060](https://github.com/nodejs/node/pull/10060)
- [[`ae9e2a21c1`](https://github.com/nodejs/node/commit/ae9e2a21c1)] - **test**: var to const, assert.equal to assert.strictEqual in net (Sean Villars) [#9907](https://github.com/nodejs/node/pull/9907)
- [[`30c9474286`](https://github.com/nodejs/node/commit/30c9474286)] - **test**: changed vars to const in test-net-better-error-messages-listen-path.js (anoff) [#9905](https://github.com/nodejs/node/pull/9905)
- [[`bcbf50d9ba`](https://github.com/nodejs/node/commit/bcbf50d9ba)] - **test**: refactor test-http-dns-error (Outsider) [#10062](https://github.com/nodejs/node/pull/10062)
- [[`00f08640ce`](https://github.com/nodejs/node/commit/00f08640ce)] - **test**: assert.equal -> assert.strictEqual (davidmarkclements) [#10065](https://github.com/nodejs/node/pull/10065)
- [[`d9cca393e9`](https://github.com/nodejs/node/commit/d9cca393e9)] - **test**: assert.equal -> assert.strictEqual (davidmarkclements) [#10067](https://github.com/nodejs/node/pull/10067)
- [[`6c64f6c445`](https://github.com/nodejs/node/commit/6c64f6c445)] - **test**: improve test for crypto padding (Julian Duque) [#9906](https://github.com/nodejs/node/pull/9906)
- [[`37d734ae36`](https://github.com/nodejs/node/commit/37d734ae36)] - **test**: polish test-net-better-error-messages-listen (Hitesh Kanwathirtha) [#10087](https://github.com/nodejs/node/pull/10087)
- [[`f126b44a3a`](https://github.com/nodejs/node/commit/f126b44a3a)] - **test**: change var to const in test-tls-key-mismatch.js (bjdelro) [#9897](https://github.com/nodejs/node/pull/9897)
- [[`7538dd5c93`](https://github.com/nodejs/node/commit/7538dd5c93)] - **test**: use strictEqual in cwd-enoent (JDHarmon) [#10077](https://github.com/nodejs/node/pull/10077)
- [[`39816a43af`](https://github.com/nodejs/node/commit/39816a43af)] - **test**: refactor test-fs-read-stream-inherit.js (Jonathan Darling) [#9894](https://github.com/nodejs/node/pull/9894)
- [[`7615a0f2cd`](https://github.com/nodejs/node/commit/7615a0f2cd)] - **test**: refactor test-child-process-stdio-inherit (Wes Tyler) [#9893](https://github.com/nodejs/node/pull/9893)
- [[`2a9ab8ea2a`](https://github.com/nodejs/node/commit/2a9ab8ea2a)] - **test**: change var to const for require and strict equality checks (Harish Tejwani) [#9892](https://github.com/nodejs/node/pull/9892)
- [[`5cd7e7aaf1`](https://github.com/nodejs/node/commit/5cd7e7aaf1)] - **test**: Update to const and use regex for assertions (Daniel Flores) [#9891](https://github.com/nodejs/node/pull/9891)
- [[`1a73cc5357`](https://github.com/nodejs/node/commit/1a73cc5357)] - **test**: swap var->const/let and equal->strictEqual (Peter Masucci) [#9888](https://github.com/nodejs/node/pull/9888)
- [[`552169e950`](https://github.com/nodejs/node/commit/552169e950)] - **test**: replace equal with strictEqual in crypto (Julian Duque) [#9886](https://github.com/nodejs/node/pull/9886)
- [[`49900e78b0`](https://github.com/nodejs/node/commit/49900e78b0)] - **test**: replace equal with strictEqual (Julian Duque) [#9879](https://github.com/nodejs/node/pull/9879)
- [[`998db3a003`](https://github.com/nodejs/node/commit/998db3a003)] - **test**: refactor test-tls-timeout-server-2 (Devon Rifkin) [#9876](https://github.com/nodejs/node/pull/9876)
- [[`aaab51047f`](https://github.com/nodejs/node/commit/aaab51047f)] - **test**: Changed assert.equal to assert.strictEqual (Daniel Pittman) [#9902](https://github.com/nodejs/node/pull/9902)
- [[`a4488c3cbd`](https://github.com/nodejs/node/commit/a4488c3cbd)] - **test**: refactor test-vm-syntax-error-stderr.js (Jay Brownlee) [#9900](https://github.com/nodejs/node/pull/9900)
- [[`cff80a5c0e`](https://github.com/nodejs/node/commit/cff80a5c0e)] - **test**: refactor test-tls-destroy-whilst-write (Chris Bystrek) [#10064](https://github.com/nodejs/node/pull/10064)
- [[`8257671bdc`](https://github.com/nodejs/node/commit/8257671bdc)] - **test**: refactor test-https-truncate (davidmarkclements) [#10074](https://github.com/nodejs/node/pull/10074)
- [[`457af874b5`](https://github.com/nodejs/node/commit/457af874b5)] - **test**: use strictEqual in test-cli-eval-event.js (Richard Karmazin) [#9964](https://github.com/nodejs/node/pull/9964)
- [[`2890f0d904`](https://github.com/nodejs/node/commit/2890f0d904)] - **test**: refactor test-tls-friendly-error-message.js (Adrian Estrada) [#9967](https://github.com/nodejs/node/pull/9967)
- [[`c37ae4a1b6`](https://github.com/nodejs/node/commit/c37ae4a1b6)] - **test**: refactor test-vm-static-this.js (David Bradford) [#9887](https://github.com/nodejs/node/pull/9887)
- [[`9473fc6c2f`](https://github.com/nodejs/node/commit/9473fc6c2f)] - **test**: refactor test-crypto-cipheriv-decipheriv (Aileen) [#10018](https://github.com/nodejs/node/pull/10018)
- [[`6ecc4ffb1c`](https://github.com/nodejs/node/commit/6ecc4ffb1c)] - **test**: refactor test for crypto cipher/decipher iv (Julian Duque) [#9943](https://github.com/nodejs/node/pull/9943)
- [[`a486f6bad4`](https://github.com/nodejs/node/commit/a486f6bad4)] - **test**: refactor test-cluster-setup-master-argv (Oscar Martinez) [#9960](https://github.com/nodejs/node/pull/9960)
- [[`384c954698`](https://github.com/nodejs/node/commit/384c954698)] - **test**: refactor test-cluster-setup-master-argv (Christine Hong) [#9993](https://github.com/nodejs/node/pull/9993)
- [[`76645e8781`](https://github.com/nodejs/node/commit/76645e8781)] - **test**: use assert.strictEqual in test-crypto-ecb (Daniel Pittman) [#9980](https://github.com/nodejs/node/pull/9980)
- [[`9103c3d3fe`](https://github.com/nodejs/node/commit/9103c3d3fe)] - **test**: update to const iin cluster test (Greg Valdez) [#10007](https://github.com/nodejs/node/pull/10007)
- [[`27c9171586`](https://github.com/nodejs/node/commit/27c9171586)] - **test**: use assert.strictEqual() cluster test (Bidur Adhikari) [#10042](https://github.com/nodejs/node/pull/10042)
- [[`2453d64aa7`](https://github.com/nodejs/node/commit/2453d64aa7)] - **test**: var -> let/const, .equal -> .strictEqual (shiya) [#9913](https://github.com/nodejs/node/pull/9913)
- [[`1467c964a4`](https://github.com/nodejs/node/commit/1467c964a4)] - **test**: increase coverage for timers (lrlna) [#10068](https://github.com/nodejs/node/pull/10068)
- [[`e47195cf78`](https://github.com/nodejs/node/commit/e47195cf78)] - **test**: change equal to strictEqual (Kevin Zurawel) [#9872](https://github.com/nodejs/node/pull/9872)
- [[`33da22aba1`](https://github.com/nodejs/node/commit/33da22aba1)] - **test**: add toASCII and toUnicode punycode tests (Claudio Rodriguez) [#9741](https://github.com/nodejs/node/pull/9741)
- [[`4c5d24b632`](https://github.com/nodejs/node/commit/4c5d24b632)] - **test**: refine test-http-status-reason-invalid-chars (Rich Trott) [#9802](https://github.com/nodejs/node/pull/9802)
- [[`81d49aaeb2`](https://github.com/nodejs/node/commit/81d49aaeb2)] - **test**: exclude no_interleaved_stdio test for AIX (Michael Dawson) [#9772](https://github.com/nodejs/node/pull/9772)
- [[`b59cf582e4`](https://github.com/nodejs/node/commit/b59cf582e4)] - **test**: refactor test-async-wrap-\* (Rich Trott) [#9663](https://github.com/nodejs/node/pull/9663)
- [[`57cc5cb277`](https://github.com/nodejs/node/commit/57cc5cb277)] - **test**: use setImmediate() in test of stream2 (masashi.g) [#9583](https://github.com/nodejs/node/pull/9583)
- [[`8345ffb0a0`](https://github.com/nodejs/node/commit/8345ffb0a0)] - **test**: add test case of PassThrough (Yoshiya Hinosawa) [#9581](https://github.com/nodejs/node/pull/9581)
- [[`beb147a08b`](https://github.com/nodejs/node/commit/beb147a08b)] - **test**: check that `process.execPath` is a realpath (Anna Henningsen) [#9229](https://github.com/nodejs/node/pull/9229)
- [[`cef5b1fa14`](https://github.com/nodejs/node/commit/cef5b1fa14)] - **test**: add test for broken child process stdio (cjihrig) [#9528](https://github.com/nodejs/node/pull/9528)
- [[`29ab76b791`](https://github.com/nodejs/node/commit/29ab76b791)] - **test**: ensure nextTick is not scheduled in exit (Jeremiah Senkpiel) [#9555](https://github.com/nodejs/node/pull/9555)
- [[`b87fe250d2`](https://github.com/nodejs/node/commit/b87fe250d2)] - **test**: change from setTimeout to setImmediate (MURAKAMI Masahiko) [#9578](https://github.com/nodejs/node/pull/9578)
- [[`eca12d4316`](https://github.com/nodejs/node/commit/eca12d4316)] - **test**: improve test-stream2-objects.js (Yoshiya Hinosawa) [#9565](https://github.com/nodejs/node/pull/9565)
- [[`4e36a14c15`](https://github.com/nodejs/node/commit/4e36a14c15)] - **test**: refactor test-next-tick-error-spin (Rich Trott) [#9537](https://github.com/nodejs/node/pull/9537)
- [[`b2b2bc2293`](https://github.com/nodejs/node/commit/b2b2bc2293)] - **test**: move timer-dependent test to sequential (Rich Trott) [#9487](https://github.com/nodejs/node/pull/9487)
- [[`1436fd70f5`](https://github.com/nodejs/node/commit/1436fd70f5)] - **test**: convert assert.equal to assert.strictEqual (Jonathan Darling) [#9925](https://github.com/nodejs/node/pull/9925)
- [[`c9ed49da6e`](https://github.com/nodejs/node/commit/c9ed49da6e)] - **test**: run cpplint on files in test/cctest (Ben Noordhuis) [#9787](https://github.com/nodejs/node/pull/9787)
- [[`10d4f470f8`](https://github.com/nodejs/node/commit/10d4f470f8)] - **test**: enable addons test to pass with debug build (Daniel Bevenius) [#8836](https://github.com/nodejs/node/pull/8836)
- [[`550393dc78`](https://github.com/nodejs/node/commit/550393dc78)] - **test**: add new.target add-on regression test (Ben Noordhuis) [#9689](https://github.com/nodejs/node/pull/9689)
- [[`76245b2156`](https://github.com/nodejs/node/commit/76245b2156)] - **test**: refactor large event emitter tests (cjihrig) [#6446](https://github.com/nodejs/node/pull/6446)
- [[`02e8187751`](https://github.com/nodejs/node/commit/02e8187751)] - **test**: allow globals to be whitelisted (cjihrig) [#7826](https://github.com/nodejs/node/pull/7826)
- [[`c0c5608bfc`](https://github.com/nodejs/node/commit/c0c5608bfc)] - **test,assert**: add deepEqual/deepStrictEqual tests for typed arrays (Feross Aboukhadijeh) [#8002](https://github.com/nodejs/node/pull/8002)
- [[`759e8fdd18`](https://github.com/nodejs/node/commit/759e8fdd18)] - **timers**: bail from intervals if \_repeat is bad (Jeremiah Senkpiel) [#10365](https://github.com/nodejs/node/pull/10365)
- [[`553d95da15`](https://github.com/nodejs/node/commit/553d95da15)] - **timers**: use consistent checks for canceled timers (Jeremiah Senkpiel) [#9685](https://github.com/nodejs/node/pull/9685)
- [[`5c6d908dd7`](https://github.com/nodejs/node/commit/5c6d908dd7)] - **tools**: enable final newline in .editorconfig (Roman Reiss) [#9410](https://github.com/nodejs/node/pull/9410)
- [[`06e8120928`](https://github.com/nodejs/node/commit/06e8120928)] - **tools**: remove unneeded escaping in generate.js (Rich Trott) [#9781](https://github.com/nodejs/node/pull/9781)
- [[`fd6b305421`](https://github.com/nodejs/node/commit/fd6b305421)] - **tools**: use better regexp for manpage references (Anna Henningsen) [#9632](https://github.com/nodejs/node/pull/9632)
- [[`9b36469a3c`](https://github.com/nodejs/node/commit/9b36469a3c)] - **tools**: improve docopen target in Makefile (Sakthipriyan Vairamani (thefourtheye)) [#9436](https://github.com/nodejs/node/pull/9436)
- [[`e3dc05d01b`](https://github.com/nodejs/node/commit/e3dc05d01b)] - **tools**: make run-valgrind.py useful (Ben Noordhuis) [#9520](https://github.com/nodejs/node/pull/9520)
- [[`7b1b11a11c`](https://github.com/nodejs/node/commit/7b1b11a11c)] - **tools**: fix run-valgrind.py script (Ben Noordhuis) [#9520](https://github.com/nodejs/node/pull/9520)
- [[`011ee0ba8b`](https://github.com/nodejs/node/commit/011ee0ba8b)] - **tools**: copy run-valgrind.py to tools/ (Ben Noordhuis) [#9520](https://github.com/nodejs/node/pull/9520)

Windows 32-bit Installer: https://nodejs.org/dist/v4.7.1/node-v4.7.1-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v4.7.1/node-v4.7.1-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v4.7.1/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v4.7.1/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v4.7.1/node-v4.7.1.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v4.7.1/node-v4.7.1-darwin-x64.tar.gz \
Linux 32-bit Binary: https://nodejs.org/dist/v4.7.1/node-v4.7.1-linux-x86.tar.xz \
Linux 64-bit Binary: https://nodejs.org/dist/v4.7.1/node-v4.7.1-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v4.7.1/node-v4.7.1-linux-ppc64le.tar.xz \
Linux PPC BE 64-bit Binary: https://nodejs.org/dist/v4.7.1/node-v4.7.1-linux-ppc64.tar.xz \
SmartOS 32-bit Binary: https://nodejs.org/dist/v4.7.1/node-v4.7.1-sunos-x86.tar.xz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v4.7.1/node-v4.7.1-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v4.7.1/node-v4.7.1-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v4.7.1/node-v4.7.1-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v4.7.1/node-v4.7.1-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v4.7.1/node-v4.7.1.tar.gz \
Other release files: https://nodejs.org/dist/v4.7.1/ \
Documentation: https://nodejs.org/docs/v4.7.1/api/

### SHASUMS

#### The Original SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

1341d2a535f2c92d87607e94ef4389ce365769c5c582da93604ba0328e59c620  node-v4.7.1-darwin-x64.tar.gz
2cb9fcb62e99296005bf5fbc6f691eeb77a298730e75ab96d4d50578fdf412f8  node-v4.7.1-darwin-x64.tar.xz
7eb5c43e61eb70115792ead0a1b4b66d4afe5f2a040214e7c2753cbd09113093  node-v4.7.1-headers.tar.gz
ce4e3573c3705ab24545476f3c3c73c3c59bbcd4d0096e071c1fba11d60ba8e5  node-v4.7.1-headers.tar.xz
b0a7b4addbcfbeedb442ea7b88d7f4ff8aef50f0d7a6de98c788553df6c2d19a  node-v4.7.1-linux-arm64.tar.gz
3e58c0a68d446abece3523010f893ce1c8c1489cedf7602cc5cf9fff3d8475fd  node-v4.7.1-linux-arm64.tar.xz
258de26c220dfa26fd03fad037f4b5c5cf28212161be50fbeda865c224c73252  node-v4.7.1-linux-armv6l.tar.gz
6d40254861755b33f2c0daffa28ceee6b4f96e2619a5e7097cd3b162f4473a45  node-v4.7.1-linux-armv6l.tar.xz
5d63cfb9745b105f525af20c926fbb33414c6d86153152a007d013ecf892811f  node-v4.7.1-linux-armv7l.tar.gz
fe4eb933d4418804cde40c608d4ea4d0e669cabd1b2d8f4b82fd6afd540c235e  node-v4.7.1-linux-armv7l.tar.xz
e91732824d87b497453084c8538226bf1e19b83c904570cecadb8708287b23de  node-v4.7.1-linux-ppc64le.tar.gz
b3f9f9d9f1cf0e25ab83db221a526552033d59512466133dd23887d4df24a0ae  node-v4.7.1-linux-ppc64le.tar.xz
db655217795afaa143c8c658af235a1c9f9f20ab6cc60eea7bb26d576b19aeeb  node-v4.7.1-linux-ppc64.tar.gz
d9330fe5687d20f31b6c0d18200558cac7692da8edd2e80a4a1d8164270c1bac  node-v4.7.1-linux-ppc64.tar.xz
316f7ad0d5784ba74a4077bca6389f365d37911378df91e041ff623260c744dc  node-v4.7.1-linux-x64.tar.gz
5253ec96a33ead31de0a0d4b62866f95af05dfa4827c41af77c3b1996d7e1ee9  node-v4.7.1-linux-x64.tar.xz
7efbe01e7b941f6b6f115e4412a589b3ffc87bfd8893e5292eb9bd741d5b7931  node-v4.7.1-linux-x86.tar.gz
16907abad9769e7a4f764ba4834c49861faf3d777661d5487bc821ca11ba1f45  node-v4.7.1-linux-x86.tar.xz
532526080700598f67f2fa532cf6e893d90ff573aa798ad25f43be3d7b2c9a5f  node-v4.7.1.pkg
fed7ec607905b5246a2e7a2362343dcda9d7c4b8273da6a7fdc0057ea9289dfe  node-v4.7.1-sunos-x86.tar.gz
82ab907b667963072a833cd122b9550fce42f079fe99a5962218664d45c15dc1  node-v4.7.1-sunos-x86.tar.xz
bc1e4c21aac3051b6aaebe146eaabee2d0cf873b9879f368963aa5a917dc91c6  node-v4.7.1.tar.gz
2c1980f14d4a2ea6afea9e444635c4c0f076db02883779df2d2af4b2a0d4c55f  node-v4.7.1.tar.xz
64cee970484f58b7fc7b4491010bb633dc45c281da095e1990dd2b602b6b3f23  node-v4.7.1-win-x64.7z
3e0f646ae187cfd44d553bbcc86d8b892a21bb6e37999507081dbf0bd320a336  node-v4.7.1-win-x64.zip
f941cd02f221a4b6cda81b49f88d6e5f6b519b30362d0c43f012fba906420e11  node-v4.7.1-win-x86.7z
675c64041abb6f2fc5b58231df7653db4f37aae4a61c3533bca0a0f2f9c4dd06  node-v4.7.1-win-x86.zip
2db709e00bdd11bde3c7f817273f1d639b55ea7233374837247dc179c3ae1e7c  node-v4.7.1-x64.msi
fef5561d9e7d49d66d51cabd88a245b8298784e066ff3c45d3c03840d5377e89  node-v4.7.1-x86.msi
d2f15623cfa9ceed2828042ba215242d42a6a2e51ea2cc49e95f9aaa32e2b48f  win-x64/node.exe
1edd28059971fd59c64ae6d32e3df9ffa9a27131fd4231f1ecc357af4cd4eb06  win-x64/node.lib
1cf68f32680fafa76f3537c59e7d3852eb0dd9fd7f7e9ccc069a60eaf01ab903  win-x64/node_pdb.7z
25270abf45ca87db2253f8fcac2273b251747d0623d3bc52839c2fd42073b101  win-x64/node_pdb.zip
3c4256cb24b693c052b52c29ed28adaac3aa0b319846de69bd959bbd85465779  win-x86/node.exe
ec7d1ba229c5690d7a4c2098012b6e0275a4de05a15dea978ddeab700daac398  win-x86/node.lib
87b6b6626ee2058b5bfebaa22eafc72e18ec331dabc86ad2a327001357881b75  win-x86/node_pdb.7z
5eef0e60d12ed8a3611d20e6b855163d5730aa643f7f702a350e6de7b804fef2  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQEcBAEBCAAGBQJYbDzVAAoJEJM7AfQLXKlGMF0H/0xIHCM5gMmvMkc55LcX30Y4
hGw/MBA3ojTed6Ma4Tc1GbvIpGr211IOZN+oDr+FPrZhUTZDHOs/rI/7B64j1cPs
oXC2xavoeLS/NYTzo+Nb05NI9msVGMW+31cH1LBmf7O9ici4hCBmqIHEABYx2CGR
LNb4qXW1Fdd7d+kYRnG6dO3+1GEs1rDJAHGYswJq3MEHj29ZU7azKgBkHpkLYAco
RNItvr99s2mYREnPIvEgwm1qxHN8Z/f1wWNqLkGSibLNXrMgWKAVNqi+WQ1gxYqA
z0p3tvpVaqsXPiGF82Eja1mXpFk5iiuC43fuL6QS8/LAA5A2rxzVqgO8lB89WMU=
=WUoR
-----END PGP SIGNATURE-----
```

#### The Current SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

4567240caa1c59e702a42704f3af2c79df488a139dd8ab37cf0fce24b5f74020  node-v4.7.1-darwin-x64.tar.gz
c4f00486231565b38a34fc0eab57a04d40a0778fb46fecca3de1e99fa5eac24b  node-v4.7.1-darwin-x64.tar.xz
65662b3bb57577e7a8fa254522befa688620a786d2648061900050ade00d33f5  node-v4.7.1-headers.tar.gz
1a4e8471cd7e3046e1d9533e50fdc28445b01f1d0901c413dedc6d5f50fc0d8f  node-v4.7.1-headers.tar.xz
011d4a50b31941b4dc09e3f9b5b082dc1176f9483a7a758c02de161a36778e7a  node-v4.7.1-linux-arm64.tar.gz
0558537391e9a0a5eed74da174cd59bbe7a81f20b826fa4f5b628ea36ea6518e  node-v4.7.1-linux-arm64.tar.xz
900fa798c5dee90b8252ec77de6296d5597e2950f4d0549c0a4ded77a89e48de  node-v4.7.1-linux-armv6l.tar.gz
99461ce059c2e0febf10fc0fb2749db3106e997d2541843007c1b27d9ce714f0  node-v4.7.1-linux-armv6l.tar.xz
94bc1a26a141e1d5505133cd6757c38f86aa4f6c45e6bf7bdbd67df558f14897  node-v4.7.1-linux-armv7l.tar.gz
6041279519c0e13913729b7f68e1b6010a5abc95b29b13acbe4248b1219e9038  node-v4.7.1-linux-armv7l.tar.xz
bf30bf15edaa0c23dd81424e37167bb1a4718af6d81a3f9f487c823e04a481ed  node-v4.7.1-linux-ppc64le.tar.gz
d83f1e7d370069ef20faec3e3451bedbcea85852ad7766e4f3634efd57b1acbd  node-v4.7.1-linux-ppc64le.tar.xz
658bbd4c4de4a603d1d4cc5ed8eab9120fda1c1bcab87423636f7342d6bb2f2c  node-v4.7.1-linux-ppc64.tar.gz
92593e98f1caff6910c9c9cd5046e23e51768d87dc8c8a9c6a539a3149adb655  node-v4.7.1-linux-ppc64.tar.xz
8076452319f8f30cb860cdcfed78853afa091643658d258306cb1005b4ccb083  node-v4.7.1-linux-x64.tar.gz
bb2e97d675ff30c1526dc35489b42cb3fe4c74a6e476636be440c735236b640d  node-v4.7.1-linux-x64.tar.xz
69292d3f44363a2e1671dd0342bc054f6b533b6fdfeba8a946efbbec945721b8  node-v4.7.1-linux-x86.tar.gz
964bd168e95bf3abd9671dd45dd853d6e19e3769ec284fb35aac8e561908c8b8  node-v4.7.1-linux-x86.tar.xz
5e2f6100414196054c2554f0e167cf61eb92b090b000304b25afc7cbc7a737df  node-v4.7.1.pkg
e10263c854b1414eee4295a5e8f505a6f0844f1717f83934319eaaaf3d5673f6  node-v4.7.1-sunos-x64.tar.gz
799854bf32435b6fd777548456c7c719abe82b0af2665982f3c2de29a48e4415  node-v4.7.1-sunos-x64.tar.xz
c96025827ea9e86887d5c958701373b360e822b6147f5fcd60ab2c423f5b4db9  node-v4.7.1-sunos-x86.tar.gz
f2b289e78d4412e7d48c99571deb6fdbf0883ad07bac328a59d95dcd7d2af5ad  node-v4.7.1-sunos-x86.tar.xz
9c0472b8faf03758b6ff86e4a05d03e399b94b6b83c6c0847e5eaedb448a1c49  node-v4.7.1.tar.gz
1e8582c5ef7905f55792cbae59ea32991d6d2821c6d52ef974ac3194d3728888  node-v4.7.1.tar.xz
9a258648eda0adce92d1a05776bd5611083b3cdaaa78a2dede82ffd5641dd1b1  node-v4.7.1-win-x64.7z
306e843bd80ce26536a72e43dca472f67a9f04d7b919e3dfce1fab2dab15aed2  node-v4.7.1-win-x64.zip
a99c6d22da2457c7c3fd1776970f5ed57b805f1a2760e4086b3b3d21c96ac688  node-v4.7.1-win-x86.7z
88a2a0338ef998c5347c2d04313feac07cc6e34b89b20b2481eb2adb715a997b  node-v4.7.1-win-x86.zip
635f25715730799d2ee4a6034495d53c21f475e0b96fe5a5766db049e06cd671  node-v4.7.1-x64.msi
822ca8434ff546c69b390ab40f58c2f1df45e77da72e160efee327ab3ccfa093  node-v4.7.1-x86.msi
67ba62648b3ff1d921dd40969cfbed11056b162e9ba7334c28f72f5d53440560  win-x64/node.exe
92105a369fdbf589220a6f03582b7a0806bdf3c05afa2b9d3b02898667d03b30  win-x64/node.lib
45f73c3650b50352dfd07b564b8fc8820d3a880202408f573f3a1756eb74ffc4  win-x64/node_pdb.7z
cad47a13e7d2e7cd84013c2b29bf5eb4082a7144d7e496a23693327049894b11  win-x64/node_pdb.zip
8a85d55ab766c6bee2c16bb30425d90d0afe4128151fb5a5942d675c0ed8d934  win-x86/node.exe
53f126f2288063f7b896d110e47d30e9f7b8fa9af1268db11f876be4092ab340  win-x86/node.lib
cf420c118076e021f01eab2ea1b94fc26736cecff6d7713e773f5c880a57255e  win-x86/node_pdb.7z
c674f91b6ac312b6ff3e44c32ee35bd87f3dbd492a291fb313be09704e6eff0c  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQEcBAEBCAAGBQJYboDxAAoJEJM7AfQLXKlGSe8H/1J5leva7Rm4aM3n1kJmSggk
RYMb/PVfPe1EsYlor5HzxOgTONAqsNHF0WEy8zOKtRpAndnG4reTa0ZuVqp6v3o9
M8ScEKPYe6llqZe9s9saQn4zmNygzSjrXuikkfPlSHkTo0iTZ6bita0d3V7oTQdS
jj95Qg/vSDQFjInkDZ2yhDBMZ6lsCagu2ZdX/+XVPMo3PTwCpJt9N82VpKuPL/L2
ZNDiYbXUWcOEzCRnnmULV5lvegW2NeMmxSnT+2w5zfL7MW62ltNJZTVoekf9+LTs
j2YhP+82Xs8Q52PpjXLN4U6EAfAbRpqe3cKMKOnVDvJEnsidwHgkgmN7SiTl/7o=
=vn1k
-----END PGP SIGNATURE-----
```
