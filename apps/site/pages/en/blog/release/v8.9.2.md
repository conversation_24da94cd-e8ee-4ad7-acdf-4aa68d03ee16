---
date: '2017-12-05T22:22:52.004Z'
category: release
title: Node v8.9.2 (LTS)
layout: blog-post
author: <PERSON>
---

### Notable Changes

- **console**:
  - avoid adding infinite error listeners (<PERSON>) [#16770](https://github.com/nodejs/node/pull/16770)
- **http2**:
  - improve errors thrown in header validation (<PERSON><PERSON>) [#16718](https://github.com/nodejs/node/pull/16718)

### Commits

- [[`1bf6250b99`](https://github.com/nodejs/node/commit/1bf6250b99)] - doc : mention constant-time in crypto doc (<PERSON><PERSON><PERSON>) [#16604](https://github.com/nodejs/node/pull/16604)
- [[`585f8698af`](https://github.com/nodejs/node/commit/585f8698af)] - **build**: include src\tracing when linting on win (<PERSON>) [#16720](https://github.com/nodejs/node/pull/16720)
- [[`d9a18beaa6`](https://github.com/nodejs/node/commit/d9a18beaa6)] - **build**: suppress lint-md output (Gibson Fahnestock) [#16551](https://github.com/nodejs/node/pull/16551)
- [[`4e848d4afb`](https://github.com/nodejs/node/commit/4e848d4afb)] - **build**: add missing comma in sources list (Daniel Bevenius) [#16613](https://github.com/nodejs/node/pull/16613)
- [[`9df1e8f10e`](https://github.com/nodejs/node/commit/9df1e8f10e)] - **console**: avoid adding infinite error listeners (Matteo Collina) [#16770](https://github.com/nodejs/node/pull/16770)
- [[`7ba037592d`](https://github.com/nodejs/node/commit/7ba037592d)] - **deps**: cherry-pick cc55747 from V8 upstream (Franziska Hinkelmann) [#16890](https://github.com/nodejs/node/pull/16890)
- [[`c3c9a8d4bf`](https://github.com/nodejs/node/commit/c3c9a8d4bf)] - **doc**: recommend node-core-utils for metadata (Rich Trott) [#16978](https://github.com/nodejs/node/pull/16978)
- [[`891ddad93c`](https://github.com/nodejs/node/commit/891ddad93c)] - **doc**: fix typo in http2 doc (Gus Caplan) [#16993](https://github.com/nodejs/node/pull/16993)
- [[`ccd36467f8`](https://github.com/nodejs/node/commit/ccd36467f8)] - **doc**: reorganize COLLABORATOR_GUIDE.md (Rich Trott) [#15710](https://github.com/nodejs/node/pull/15710)
- [[`8f0793ff93`](https://github.com/nodejs/node/commit/8f0793ff93)] - **doc**: clarify the prerequisites for building with VS2017 (Nikolai Vavilov) [#16903](https://github.com/nodejs/node/pull/16903)
- [[`6e7a444a91`](https://github.com/nodejs/node/commit/6e7a444a91)] - **doc**: outline commit message for breaking changes (Maton Anthony) [#16846](https://github.com/nodejs/node/pull/16846)
- [[`6eb550da34`](https://github.com/nodejs/node/commit/6eb550da34)] - **doc**: remove duplicate 'the' from http2 API doc (Vipin Menon) [#16924](https://github.com/nodejs/node/pull/16924)
- [[`0b8a400cad`](https://github.com/nodejs/node/commit/0b8a400cad)] - **doc**: correct the spelling of omitting in dgram.md (Vidya Subramanyam) [#16910](https://github.com/nodejs/node/pull/16910)
- [[`adb8f08c36`](https://github.com/nodejs/node/commit/adb8f08c36)] - **doc**: fix a typo in the documentation (Mamatha J V) [#16909](https://github.com/nodejs/node/pull/16909)
- [[`d721c0bb5e`](https://github.com/nodejs/node/commit/d721c0bb5e)] - **doc**: improve documentation for the vm module (Franziska Hinkelmann) [#16867](https://github.com/nodejs/node/pull/16867)
- [[`360f40354e`](https://github.com/nodejs/node/commit/360f40354e)] - **doc**: fix typo in assert.md (Andres Kalle) [#16866](https://github.com/nodejs/node/pull/16866)
- [[`c4634bf506`](https://github.com/nodejs/node/commit/c4634bf506)] - **doc**: update subprocess.killed (cjihrig) [#16748](https://github.com/nodejs/node/pull/16748)
- [[`eafc0a1314`](https://github.com/nodejs/node/commit/eafc0a1314)] - **doc**: fix a link in dgram.md (Vse Mozhet Byt) [#16854](https://github.com/nodejs/node/pull/16854)
- [[`fab55980be`](https://github.com/nodejs/node/commit/fab55980be)] - **doc**: add isTTY property documentation (SonaySevik) [#16828](https://github.com/nodejs/node/pull/16828)
- [[`f2a9c024ed`](https://github.com/nodejs/node/commit/f2a9c024ed)] - **doc**: fix json generator warnings (Luigi Pinca) [#16742](https://github.com/nodejs/node/pull/16742)
- [[`3319b2092f`](https://github.com/nodejs/node/commit/3319b2092f)] - **doc**: update license to include node-inspect (Myles Borins) [#16659](https://github.com/nodejs/node/pull/16659)
- [[`7618567b4f`](https://github.com/nodejs/node/commit/7618567b4f)] - **doc**: add docs for Zlib#close() (Luigi Pinca) [#16592](https://github.com/nodejs/node/pull/16592)
- [[`2cc05e0657`](https://github.com/nodejs/node/commit/2cc05e0657)] - **doc**: add nodejs/gyp team for GYP related issues (Gibson Fahnestock) [#16638](https://github.com/nodejs/node/pull/16638)
- [[`542f3b9cc0`](https://github.com/nodejs/node/commit/542f3b9cc0)] - **doc**: add details about rss on process.memoryUsage (Anthony Nandaa) [#16566](https://github.com/nodejs/node/pull/16566)
- [[`13866b8b1b`](https://github.com/nodejs/node/commit/13866b8b1b)] - **doc**: add windowsVerbatimArguments docs (Andrew Stucki) [#16299](https://github.com/nodejs/node/pull/16299)
- [[`d2e4a87321`](https://github.com/nodejs/node/commit/d2e4a87321)] - **doc**: howto decode buffers extending from Writable (dicearr) [#16403](https://github.com/nodejs/node/pull/16403)
- [[`a2fd9a3cf2`](https://github.com/nodejs/node/commit/a2fd9a3cf2)] - **doc**: add \*-inl.h include rule to C++ style guide (Joyee Cheung) [#16548](https://github.com/nodejs/node/pull/16548)
- [[`9b8e2a68d8`](https://github.com/nodejs/node/commit/9b8e2a68d8)] - **http**: use arrow fns for lexical `this` in Agent (Bryan English) [#16475](https://github.com/nodejs/node/pull/16475)
- [[`29efb02f12`](https://github.com/nodejs/node/commit/29efb02f12)] - **http2**: multiple smaller code cleanups (James M Snell) [#16764](https://github.com/nodejs/node/pull/16764)
- [[`658301664f`](https://github.com/nodejs/node/commit/658301664f)] - **http2**: improve errors thrown in header validation (Joyee Cheung) [#16718](https://github.com/nodejs/node/pull/16718)
- [[`8cf8a327c8`](https://github.com/nodejs/node/commit/8cf8a327c8)] - **http2**: refactor settings handling (James M Snell) [#16668](https://github.com/nodejs/node/pull/16668)
- [[`4faf2ec783`](https://github.com/nodejs/node/commit/4faf2ec783)] - **lib**: replace string concatenation with template (Suryanarayana Murthy N) [#16933](https://github.com/nodejs/node/pull/16933)
- [[`14f8cee401`](https://github.com/nodejs/node/commit/14f8cee401)] - **lib**: guard inspector console using process var (Daniel Bevenius) [#15008](https://github.com/nodejs/node/pull/15008)
- [[`2ad051d62c`](https://github.com/nodejs/node/commit/2ad051d62c)] - **lib**: change concatenated string to template (Pawan Jangid) [#16930](https://github.com/nodejs/node/pull/16930)
- [[`28f036045b`](https://github.com/nodejs/node/commit/28f036045b)] - **lib**: change concatenated string to template (Nayana Das K) [#16925](https://github.com/nodejs/node/pull/16925)
- [[`134c2f31f2`](https://github.com/nodejs/node/commit/134c2f31f2)] - **lib**: replace string concatenation with template (subrahmanya chari p) [#16917](https://github.com/nodejs/node/pull/16917)
- [[`dc14c25ee9`](https://github.com/nodejs/node/commit/dc14c25ee9)] - **loader**: test search module (Cyril Lakech) [#16795](https://github.com/nodejs/node/pull/16795)
- [[`d27ec13cd3`](https://github.com/nodejs/node/commit/d27ec13cd3)] - **repl**: avoid crashing from null and undefined errors (cPhost) [#16574](https://github.com/nodejs/node/pull/16574)
- [[`40880897fe`](https://github.com/nodejs/node/commit/40880897fe)] - **src**: use unrefed async for GC tracking (Anna Henningsen) [#16758](https://github.com/nodejs/node/pull/16758)
- [[`f7411b5df7`](https://github.com/nodejs/node/commit/f7411b5df7)] - **src**: make StreamBase prototype accessors robust (Joyee Cheung) [#16860](https://github.com/nodejs/node/pull/16860)
- [[`8d31294b3b`](https://github.com/nodejs/node/commit/8d31294b3b)] - **src**: CHECK() for argument overflow in Spawn() (cjihrig) [#16761](https://github.com/nodejs/node/pull/16761)
- [[`57b377ef93`](https://github.com/nodejs/node/commit/57b377ef93)] - **src**: improve module loader readability (Anna Henningsen) [#16536](https://github.com/nodejs/node/pull/16536)
- [[`82076ed91f`](https://github.com/nodejs/node/commit/82076ed91f)] - **src**: pass context to Get() operations for cares_wrap (Evan Lucas) [#16641](https://github.com/nodejs/node/pull/16641)
- [[`79e1d7719d`](https://github.com/nodejs/node/commit/79e1d7719d)] - **src**: remove unused includes in string_bytes.h (Daniel Bevenius) [#16606](https://github.com/nodejs/node/pull/16606)
- [[`cecd1e3def`](https://github.com/nodejs/node/commit/cecd1e3def)] - **src**: fix etw provider include on Windows (Joyee Cheung) [#16639](https://github.com/nodejs/node/pull/16639)
- [[`255fffbbc8`](https://github.com/nodejs/node/commit/255fffbbc8)] - **src**: do not include x.h if x-inl.h is included (Joyee Cheung) [#16548](https://github.com/nodejs/node/pull/16548)
- [[`efdd7c8cae`](https://github.com/nodejs/node/commit/efdd7c8cae)] - **test**: reuse existing PassThrough implementation (Tobias Nießen) [#16936](https://github.com/nodejs/node/pull/16936)
- [[`375bec00a4`](https://github.com/nodejs/node/commit/375bec00a4)] - **test**: use fixtures module for path resolve (sercan yersen) [#16842](https://github.com/nodejs/node/pull/16842)
- [[`6ab706d7f0`](https://github.com/nodejs/node/commit/6ab706d7f0)] - **test**: refactor comments in test-child-process-spawnsync-maxbuf (ChrBergert) [#16829](https://github.com/nodejs/node/pull/16829)
- [[`315fba8bfd`](https://github.com/nodejs/node/commit/315fba8bfd)] - **test**: used fixturesDir from fixtures modules (Klemen Kogovsek) [#16813](https://github.com/nodejs/node/pull/16813)
- [[`5c8fb6a976`](https://github.com/nodejs/node/commit/5c8fb6a976)] - **test**: refactor fs.write() test (Patrick Heneise) [#16827](https://github.com/nodejs/node/pull/16827)
- [[`4f587e5a30`](https://github.com/nodejs/node/commit/4f587e5a30)] - **test**: add a test description (Grant Gasparyan) [#16833](https://github.com/nodejs/node/pull/16833)
- [[`af8b17a314`](https://github.com/nodejs/node/commit/af8b17a314)] - **test**: use common/fixtures module in hash-seed test (Javier Blanco) [#16823](https://github.com/nodejs/node/pull/16823)
- [[`3a3792b0a0`](https://github.com/nodejs/node/commit/3a3792b0a0)] - **test**: improve template value for test message (Stephan Smith) [#16826](https://github.com/nodejs/node/pull/16826)
- [[`c3e6491a51`](https://github.com/nodejs/node/commit/c3e6491a51)] - **test**: unmark flaky test (Anna Henningsen) [#16758](https://github.com/nodejs/node/pull/16758)
- [[`bf9eb04abe`](https://github.com/nodejs/node/commit/bf9eb04abe)] - **test**: change concatenated string to template (Deepthi Sebastian) [#16929](https://github.com/nodejs/node/pull/16929)
- [[`7168a7e044`](https://github.com/nodejs/node/commit/7168a7e044)] - **test**: change concatenated string to template (Anawesha Khuntia) [#16912](https://github.com/nodejs/node/pull/16912)
- [[`febd1bf519`](https://github.com/nodejs/node/commit/febd1bf519)] - **test**: change string concatenation to template (Suryanarayana Murthy N) [#16919](https://github.com/nodejs/node/pull/16919)
- [[`7164d9a6b8`](https://github.com/nodejs/node/commit/7164d9a6b8)] - **test**: use template string for concatenation (Vipin Menon) [#16918](https://github.com/nodejs/node/pull/16918)
- [[`ae7106cc75`](https://github.com/nodejs/node/commit/ae7106cc75)] - **test**: replace string concatenation with template (Kabir Islam) [#16916](https://github.com/nodejs/node/pull/16916)
- [[`81a6c4f785`](https://github.com/nodejs/node/commit/81a6c4f785)] - **test**: enable mustCall() during child exit (Vipin Menon) [#16915](https://github.com/nodejs/node/pull/16915)
- [[`41f905bb00`](https://github.com/nodejs/node/commit/41f905bb00)] - **test**: replace string concatenation with template (Sabari Lakshmi Krishnamoorthy) [#16914](https://github.com/nodejs/node/pull/16914)
- [[`be920aa372`](https://github.com/nodejs/node/commit/be920aa372)] - **test**: replace string concatenation with template (Tanvi Kini) [#16913](https://github.com/nodejs/node/pull/16913)
- [[`26d529e60f`](https://github.com/nodejs/node/commit/26d529e60f)] - **test**: cover vm.runInNewContext() (cjihrig) [#16906](https://github.com/nodejs/node/pull/16906)
- [[`6c57399c6b`](https://github.com/nodejs/node/commit/6c57399c6b)] - **test**: improve assertion messages (Neil Vass) [#16885](https://github.com/nodejs/node/pull/16885)
- [[`1522562ffd`](https://github.com/nodejs/node/commit/1522562ffd)] - **test**: pass process.env to child processes (Rod Vagg) [#16405](https://github.com/nodejs/node/pull/16405)
- [[`0bc16cd9b6`](https://github.com/nodejs/node/commit/0bc16cd9b6)] - **test**: improve assert messages in stream test (Katie Stockton Roberts) [#16884](https://github.com/nodejs/node/pull/16884)
- [[`7c9aee3348`](https://github.com/nodejs/node/commit/7c9aee3348)] - **test**: improve assertion in test-require-dot (Adam Wegrzynek) [#16805](https://github.com/nodejs/node/pull/16805)
- [[`1b1bd261dc`](https://github.com/nodejs/node/commit/1b1bd261dc)] - **test**: add values to error message (Adam Jeffery) [#16831](https://github.com/nodejs/node/pull/16831)
- [[`e66a7ae6e3`](https://github.com/nodejs/node/commit/e66a7ae6e3)] - **test**: replace common.fixtiresDir with fixtures.readKey() (woj) [#16817](https://github.com/nodejs/node/pull/16817)
- [[`c1309d6b80`](https://github.com/nodejs/node/commit/c1309d6b80)] - **test**: use tmpDir in test-fs-utimes (Rich Trott) [#16774](https://github.com/nodejs/node/pull/16774)
- [[`2f1f7e1de0`](https://github.com/nodejs/node/commit/2f1f7e1de0)] - **test**: remove message argument in cluster setup test (mbornath) [#16838](https://github.com/nodejs/node/pull/16838)
- [[`d64fe485c5`](https://github.com/nodejs/node/commit/d64fe485c5)] - **test**: check session timeout in http2 (Anatoli Papirovski) [#16754](https://github.com/nodejs/node/pull/16754)
- [[`4fcb03c0ae`](https://github.com/nodejs/node/commit/4fcb03c0ae)] - **test**: move test-http-keepalive-maxsockets to sequential (Rich Trott) [#16777](https://github.com/nodejs/node/pull/16777)
- [[`71c11d67f4`](https://github.com/nodejs/node/commit/71c11d67f4)] - **test**: improve assert messages in test-global (Mark McNelis) [#16843](https://github.com/nodejs/node/pull/16843)
- [[`ca278802ff`](https://github.com/nodejs/node/commit/ca278802ff)] - **test**: use default assertion message (jonask) [#16819](https://github.com/nodejs/node/pull/16819)
- [[`ec4c3f5777`](https://github.com/nodejs/node/commit/ec4c3f5777)] - **test**: improve message in test-fs-readfile-pipe-large (fjau) [#16840](https://github.com/nodejs/node/pull/16840)
- [[`562d8fca15`](https://github.com/nodejs/node/commit/562d8fca15)] - **test**: remove custom message from assertion (Nicolas Morel) [#16824](https://github.com/nodejs/node/pull/16824)
- [[`0ebded4376`](https://github.com/nodejs/node/commit/0ebded4376)] - **test**: show incorrect value on test failure (Sean Karson) [#16818](https://github.com/nodejs/node/pull/16818)
- [[`2bbc76eb1f`](https://github.com/nodejs/node/commit/2bbc76eb1f)] - **test**: include file mode in assert message (Sascha Tandel) [#16815](https://github.com/nodejs/node/pull/16815)
- [[`33f2fff52b`](https://github.com/nodejs/node/commit/33f2fff52b)] - **test**: refactor tls test to use fixtres.readSync (Brian O'Connell) [#16816](https://github.com/nodejs/node/pull/16816)
- [[`b307582d10`](https://github.com/nodejs/node/commit/b307582d10)] - **test**: use fixtures module in test-repl (Maring, Damian Lion) [#16809](https://github.com/nodejs/node/pull/16809)
- [[`5719beaf83`](https://github.com/nodejs/node/commit/5719beaf83)] - **test**: update test to use fixtures.readKey (Dara Hayes) [#16811](https://github.com/nodejs/node/pull/16811)
- [[`b166b6b1b3`](https://github.com/nodejs/node/commit/b166b6b1b3)] - **test**: fix typos in read-buffer tests (Jimi van der Woning) [#16834](https://github.com/nodejs/node/pull/16834)
- [[`c4176eb722`](https://github.com/nodejs/node/commit/c4176eb722)] - **test**: replace fixturesDir with usage of fixtures module (Octavian Ionescu) [#16810](https://github.com/nodejs/node/pull/16810)
- [[`af13678889`](https://github.com/nodejs/node/commit/af13678889)] - **test**: clarified assert message for test-require-json.js (Matthias Reis) [#16807](https://github.com/nodejs/node/pull/16807)
- [[`0fa659cdcd`](https://github.com/nodejs/node/commit/0fa659cdcd)] - **test**: replace common.fixturesDir with fixtures module (Dumitru Glavan) [#16803](https://github.com/nodejs/node/pull/16803)
- [[`1e6845d024`](https://github.com/nodejs/node/commit/1e6845d024)] - **test**: replace common.fixturesDir with fixtures.readSync() (Adri Van Houdt) [#16802](https://github.com/nodejs/node/pull/16802)
- [[`7b1491711d`](https://github.com/nodejs/node/commit/7b1491711d)] - **test**: replace `common.fixturesDir` usage (Sascha Tandel) [#16800](https://github.com/nodejs/node/pull/16800)
- [[`480f14a55e`](https://github.com/nodejs/node/commit/480f14a55e)] - **test**: update test to use fixtures (Adam Wegrzynek) [#16799](https://github.com/nodejs/node/pull/16799)
- [[`c52ac92661`](https://github.com/nodejs/node/commit/c52ac92661)] - **test**: fix malformed parallel.status line (Rich Trott) [#16702](https://github.com/nodejs/node/pull/16702)
- [[`a41cc020fd`](https://github.com/nodejs/node/commit/a41cc020fd)] - **test**: fix flaky test-http2-server-rst-stream.js (Anatoli Papirovski) [#16690](https://github.com/nodejs/node/pull/16690)
- [[`1e8a421159`](https://github.com/nodejs/node/commit/1e8a421159)] - **test**: pause child until parent is ready (jBarz) [#15774](https://github.com/nodejs/node/pull/15774)
- [[`b3032d29c9`](https://github.com/nodejs/node/commit/b3032d29c9)] - **test**: increase coverage for ModuleMap (Rob Paton) [#16045](https://github.com/nodejs/node/pull/16045)
- [[`2f66faf6cf`](https://github.com/nodejs/node/commit/2f66faf6cf)] - **test**: use fixtures module in test-https-pfx (Ken Takagi) [#15895](https://github.com/nodejs/node/pull/15895)
- [[`981a1ef0c2`](https://github.com/nodejs/node/commit/981a1ef0c2)] - **test**: use ES6 classes instead of util.inherits (Tobias Nießen) [#16938](https://github.com/nodejs/node/pull/16938)
- [[`47b1c3b43c`](https://github.com/nodejs/node/commit/47b1c3b43c)] - **test**: add test for WrapStream readStop (Ashish Kaila) [#16356](https://github.com/nodejs/node/pull/16356)
- [[`72c34cf706`](https://github.com/nodejs/node/commit/72c34cf706)] - **test,net**: remove scatological terminology (Rich Trott) [#16599](https://github.com/nodejs/node/pull/16599)
- [[`2b903bff05`](https://github.com/nodejs/node/commit/2b903bff05)] - **tools**: enforce no unused trailing arguments tools directory (Rich Trott) [#16953](https://github.com/nodejs/node/pull/16953)
- [[`57937e5746`](https://github.com/nodejs/node/commit/57937e5746)] - **tools**: remove unused trailing function arguments (Rich Trott) [#16953](https://github.com/nodejs/node/pull/16953)
- [[`85fd7bb8f7`](https://github.com/nodejs/node/commit/85fd7bb8f7)] - **tools**: fix inspector-check reporting (Daniel Bevenius) [#16902](https://github.com/nodejs/node/pull/16902)
- [[`8538354139`](https://github.com/nodejs/node/commit/8538354139)] - **tools**: add direct anchors for error codes (Joyee Cheung) [#16779](https://github.com/nodejs/node/pull/16779)
- [[`79006dab87`](https://github.com/nodejs/node/commit/79006dab87)] - **tools**: don't lint files that have not changed (Joyee Cheung) [#16581](https://github.com/nodejs/node/pull/16581)
- [[`cb08f5d6fe`](https://github.com/nodejs/node/commit/cb08f5d6fe)] - **tools**: remove unneeded parentheses in doc/html.js (Vse Mozhet Byt) [#16845](https://github.com/nodejs/node/pull/16845)
- [[`60c918ac7a`](https://github.com/nodejs/node/commit/60c918ac7a)] - **tools**: replace string concatenation with template literals (Kevin Yu) [#16804](https://github.com/nodejs/node/pull/16804)
- [[`aaf7e83d62`](https://github.com/nodejs/node/commit/aaf7e83d62)] - **tools**: replace string concatenation with template literals (Giovanni Lela) [#16806](https://github.com/nodejs/node/pull/16806)
- [[`40fa970914`](https://github.com/nodejs/node/commit/40fa970914)] - **tools**: replace string concetation with templates (Patrick Heneise) [#16801](https://github.com/nodejs/node/pull/16801)
- [[`0d4f62c85f`](https://github.com/nodejs/node/commit/0d4f62c85f)] - **tools,build**: allow build without `remark-cli` (Refael Ackermann) [#16893](https://github.com/nodejs/node/pull/16893)

Windows 32-bit Installer: https://nodejs.org/dist/v8.9.2/node-v8.9.2-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v8.9.2/node-v8.9.2-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v8.9.2/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v8.9.2/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v8.9.2/node-v8.9.2.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v8.9.2/node-v8.9.2-darwin-x64.tar.gz \
Linux 32-bit Binary: https://nodejs.org/dist/v8.9.2/node-v8.9.2-linux-x86.tar.xz \
Linux 64-bit Binary: https://nodejs.org/dist/v8.9.2/node-v8.9.2-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v8.9.2/node-v8.9.2-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v8.9.2/node-v8.9.2-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v8.9.2/node-v8.9.2-aix-ppc64.tar.gz \
SmartOS 32-bit Binary: https://nodejs.org/dist/v8.9.2/node-v8.9.2-sunos-x86.tar.xz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v8.9.2/node-v8.9.2-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v8.9.2/node-v8.9.2-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v8.9.2/node-v8.9.2-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v8.9.2/node-v8.9.2-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v8.9.2/node-v8.9.2.tar.gz \
Other release files: https://nodejs.org/dist/v8.9.2/ \
Documentation: https://nodejs.org/docs/v8.9.2/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

7d04fd8133c8ab7d1951dbbd7a1c89385f713c414d92eadba98a12947f31d605  node-v8.9.2-aix-ppc64.tar.gz
ba03ae4c0ebd33e8661b5b241211ddb9f7e3b5b959d8cbd68f5941cb1ed5784d  node-v8.9.2-darwin-x64.tar.gz
df8d67c7b785822f3cac3003fd30d63b8ec04c57366f67584e15cd8db5547b29  node-v8.9.2-darwin-x64.tar.xz
35ac226795678ef2b9a8f55c3eb65984bb49f71858e771c9719452d14926e83e  node-v8.9.2-headers.tar.gz
e747cc36283fadc77b4ca893afba1aec1be90eae42e75a47b6118ed1e8ab6e95  node-v8.9.2-headers.tar.xz
1aaadf2e0a44bd49dbf528fb918032dcdbc3b450dc91e8cbd92b28afbb1e004a  node-v8.9.2-linux-arm64.tar.gz
cc222b4f910ff27ff66ccc96d5c7e2117942bcd161ec253d83cf430146b79bdf  node-v8.9.2-linux-arm64.tar.xz
f86df6b4b017199da7ede78173c100c131f1da32234b0c0c9d47fd9ac1f8c77a  node-v8.9.2-linux-armv6l.tar.gz
3a442f6155ed23a524e362c9feaab5819e201d60a7243243767dc45fd92af939  node-v8.9.2-linux-armv6l.tar.xz
bee604965e2ae7d3f96976832c5a70a439a1e0e65a4e617b9e7d4aa9b84f84eb  node-v8.9.2-linux-armv7l.tar.gz
666b9913ce01b3a40c32f3ec7ffb3c89742fb37d2834afebb3a090c9ab53f1a1  node-v8.9.2-linux-armv7l.tar.xz
4e576bbee450380e3d448ac7f8a39c8c1292a2318fab24be69b3ce0b12ed0cb2  node-v8.9.2-linux-ppc64le.tar.gz
70335fd0a9d104e06e171a49eb3373c2190da23749bf71982dd51d7ec0fd24bf  node-v8.9.2-linux-ppc64le.tar.xz
61dfe13289f04caf946ae97f55f0550e4fce6d91a0109b5168b67b71768e0572  node-v8.9.2-linux-s390x.tar.gz
57178c23a555209b8b84475f24fa16338deb938baf6ca4b633d080f2dc57263f  node-v8.9.2-linux-s390x.tar.xz
bb649307300622133dbf147f24e1e695d7570f1b265c6fd2c1e36406226d1e88  node-v8.9.2-linux-x64.tar.gz
d4065724e7f5f11e999f78de50fb0faac74341799cb0c0dafcbe87e0ecb0be86  node-v8.9.2-linux-x64.tar.xz
dad907b7cf93f1c308c5d107c4312c3f7e4a0fae26e470bbc7f1e61e9bf485de  node-v8.9.2-linux-x86.tar.gz
4d04b748a206f5cc2c368f8007237d2f55323f65d2b90680c7a82208ff757c98  node-v8.9.2-linux-x86.tar.xz
45d09a623d3c1ab8cab411161a49c21f4bb5c9e5424427bf6e1ab26f89354936  node-v8.9.2.pkg
86bdc20413478c34eabcfb0f32bd9c1059e685e1f406ac6497586aa308e5e119  node-v8.9.2-sunos-x64.tar.gz
3034f20e77cb1bd19a6d53dfc18a054c97695a6563fb55f85a0f325b7291d275  node-v8.9.2-sunos-x64.tar.xz
3b41672daf4b19285f93dc260ba1a8f477df84ddbd71c7d8748680185783c79e  node-v8.9.2-sunos-x86.tar.gz
fae14a5c978adbf597bc0f6ee69f8f28fc980f10cde55fad93ed0a0cd3aa24a9  node-v8.9.2-sunos-x86.tar.xz
8038fa61ac4562786fcc6d3229f3caa07a864b94bd5922fe75942a38ea1c3a3b  node-v8.9.2.tar.gz
53a51eda2347681c88b83236c6a005db9d696c3ae5d78496f0921804d5937b59  node-v8.9.2.tar.xz
4046a954c21aa58a209b4c21f981dfa9f15621cc77abc09a6b232b28e28b2c0d  node-v8.9.2-win-x64.7z
2afa8b899c0dddea50dcf5dda66ae7b0ca32326dbf66c52f947c082e7c95d090  node-v8.9.2-win-x64.zip
17d68bbb061347e55757c25d6016227c03f82db69b802561aefd335e12fb25c9  node-v8.9.2-win-x86.7z
d039808a605fc7811a79464305eb7f9829be1e6ebf0c6057fbed6b9bcbe5e23e  node-v8.9.2-win-x86.zip
3d7a60a5268f4c3d3fc8b37e3e3ec9f52012e048d0f1598eb1d6c5d7717e6a86  node-v8.9.2-x64.msi
a4c70a98755d011e2e3922786f7c6df553a19a32196f1ebf3ccae48e078f2c09  node-v8.9.2-x86.msi
6d0e28abcdb6341a2f62b0ecedc660ec9cdfbc34fb92726a32cf8dde32e1be41  win-x64/node.exe
c99d83b6f7b9683b0378d0f98fb4d1c509b7096e9cfe9af0205cd55370b73dd9  win-x64/node.lib
65dfb9732edfa1aa05ef4eb75b281533b089092f7d578bc8c9ff2a264993906f  win-x64/node_pdb.7z
e9b9ce83a33fb67cddd7c5a7a35f3ebf4f521a8b4cad69fe751b2da9fe9c4b12  win-x64/node_pdb.zip
286fd6d36e55bd511598a19ca40ef57df568b8a3228e9fb25ee5483f42a9173e  win-x86/node.exe
00724f4499082f139cf063e5443e4fc17c4211856ecc475e05f380b3c9819524  win-x86/node.lib
124924dfd144312b12ae610b47399a72e2be571b3d4b201c9c45e26f5d49409a  win-x86/node_pdb.7z
11fe5f300d699f8a7a3d6cd959bba8d5c73829090cd09cc440122c9df3d9067a  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEd5hKmG68KqeGvA9msB+7koIcWHoFAlonG14ACgkQsB+7koIc
WHrjEA/+NOLYo5yGrVI3eRKIvjEgo5OaPRp0qaygvUISBGFb3PhSJFO6h3tlq0yS
J5kkPRgBmBCfbqBcnewHTD7U4hsvf6DZpd378T8ysxgpCwZHL6ulKVhAdOSdLB/3
GR9YuKO9q4znAZZvU1tDqEyHEsy2hG1geQOxQeMjPvgQTqVWiAUTraCQUhzy1R0t
IOT5QIEJ+NbJbpBJFZDiseFhr89lz3pgwnmA8j4WnX5EOUSmAsHMkTbhMPzj2xmr
Rs51err3jpUZhaleIE894M+RqUC1BmMgd17T8pfd5QtaYpJOK44NzIbbl/n14Os3
MpWMDSm0/t6mDDeg7BMkuZqRGecYu4cpBphtZG8vpSTJJwsBtopTH+c6wfn0W0BO
wlYKuqyhFlwA5KLIYgJo1Sw0hz5UxeNPgaDctfAmunZ5Qnae4CqMAT5hDnFjukdn
9+aT5mv2FCsNS7uZm/KpPDLuP/RaqOt91N1Acf7166vQ1GNmucGFlITuERXjdDWT
GNVwojuvu71UXFjQQ2OR3x01ADUty7ExqathHIuQ4zRgZqPoFZ35ksAEpoRsjrYF
jg2x7fRtFXcXFtkstIajNWAqfAA1tEkYg+RyhFr++vIuvcnUkLCUIDJUliLgql++
TiO+AcZUH6g654w7cRtID6YU5VNBiPm+S25z0ny52gLbqIeQLpc=
=1jLy
-----END PGP SIGNATURE-----

```
