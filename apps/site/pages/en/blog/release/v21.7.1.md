---
date: '2024-03-08T21:54:44.390Z'
category: release
title: Node v21.7.1 (Current)
layout: blog-post
author: <PERSON><PERSON><PERSON>
---

## 2024-03-08, Version 21.7.1 (Current), @targos

### Notable Changes

This release reverts [#51389](https://github.com/nodejs/node/pull/51389), which
landed in Node.js 21.7.0. It is a documented feature that `t.after()` hooks are
run even if a test has no subtests. The hook can be used to clean up the test
itself.

### Commits

- \[[`0dfe810ac7`](https://github.com/nodejs/node/commit/0dfe810ac7)] - **benchmark**: update iterations of benchmark/async_hooks/async-local- (<PERSON><PERSON>) [#51420](https://github.com/nodejs/node/pull/51420)
- \[[`625c9e0ac9`](https://github.com/nodejs/node/commit/625c9e0ac9)] - **benchmark**: update iterations of benchmark/domain/domain-fn-args.js (<PERSON><PERSON>) [#51408](https://github.com/nodejs/node/pull/51408)
- \[[`7ff3551bad`](https://github.com/nodejs/node/commit/7ff3551bad)] - **build**: fix arm64 host cross-compilation in GN (Cheng Zhao) [#51903](https://github.com/nodejs/node/pull/51903)
- \[[`fd86ea8b71`](https://github.com/nodejs/node/commit/fd86ea8b71)] - _**Revert**_ "**build**: workaround for node-core-utils" (Richard Lau) [#51975](https://github.com/nodejs/node/pull/51975)
- \[[`23c32ab3a7`](https://github.com/nodejs/node/commit/23c32ab3a7)] - **build**: respect the `NODE` env variable in `Makefile` (Antoine du Hamel) [#51743](https://github.com/nodejs/node/pull/51743)
- \[[`9617adc064`](https://github.com/nodejs/node/commit/9617adc064)] - _**Revert**_ "**build**: fix warning in cares under GN build" (Luigi Pinca) [#51865](https://github.com/nodejs/node/pull/51865)
- \[[`5864534095`](https://github.com/nodejs/node/commit/5864534095)] - **deps**: update nghttp2 to 1.60.0 (Node.js GitHub Bot) [#51948](https://github.com/nodejs/node/pull/51948)
- \[[`fcf235d623`](https://github.com/nodejs/node/commit/fcf235d623)] - **doc**: add policy for distribution (Geoffrey Booth) [#51918](https://github.com/nodejs/node/pull/51918)
- \[[`87d2acc8b1`](https://github.com/nodejs/node/commit/87d2acc8b1)] - **doc**: fix actual result of example is different in events (Deokjin Kim) [#51925](https://github.com/nodejs/node/pull/51925)
- \[[`5908c121c6`](https://github.com/nodejs/node/commit/5908c121c6)] - **doc**: clarify Corepack threat model (Antoine du Hamel) [#51917](https://github.com/nodejs/node/pull/51917)
- \[[`20e0ba3b94`](https://github.com/nodejs/node/commit/20e0ba3b94)] - **doc,module**: clarify hook chain execution sequence (Jacob Smith) [#51884](https://github.com/nodejs/node/pull/51884)
- \[[`4d997971ac`](https://github.com/nodejs/node/commit/4d997971ac)] - **lib**: make sure close net server (theanarkh) [#51929](https://github.com/nodejs/node/pull/51929)
- \[[`fcc6d54aa3`](https://github.com/nodejs/node/commit/fcc6d54aa3)] - **lib**: return directly if udp socket close before lookup (theanarkh) [#51914](https://github.com/nodejs/node/pull/51914)
- \[[`10aaabd158`](https://github.com/nodejs/node/commit/10aaabd158)] - **meta**: bump github/codeql-action from 3.23.2 to 3.24.6 (dependabot\[bot]) [#51942](https://github.com/nodejs/node/pull/51942)
- \[[`78f38a0143`](https://github.com/nodejs/node/commit/78f38a0143)] - **meta**: bump actions/upload-artifact from 4.3.0 to 4.3.1 (dependabot\[bot]) [#51941](https://github.com/nodejs/node/pull/51941)
- \[[`42ca5452c4`](https://github.com/nodejs/node/commit/42ca5452c4)] - **meta**: bump codecov/codecov-action from 4.0.1 to 4.1.0 (dependabot\[bot]) [#51940](https://github.com/nodejs/node/pull/51940)
- \[[`015a157375`](https://github.com/nodejs/node/commit/015a157375)] - **meta**: bump actions/cache from 4.0.0 to 4.0.1 (dependabot\[bot]) [#51939](https://github.com/nodejs/node/pull/51939)
- \[[`e476cb4a32`](https://github.com/nodejs/node/commit/e476cb4a32)] - **meta**: bump actions/download-artifact from 4.1.1 to 4.1.3 (dependabot\[bot]) [#51938](https://github.com/nodejs/node/pull/51938)
- \[[`67e8001790`](https://github.com/nodejs/node/commit/67e8001790)] - **meta**: bump actions/setup-node from 4.0.1 to 4.0.2 (dependabot\[bot]) [#51937](https://github.com/nodejs/node/pull/51937)
- \[[`50343636e8`](https://github.com/nodejs/node/commit/50343636e8)] - **src**: fix --disable-single-executable-application (Joyee Cheung) [#51808](https://github.com/nodejs/node/pull/51808)
- \[[`a48c9ca0db`](https://github.com/nodejs/node/commit/a48c9ca0db)] - **stream**: do not defer construction by one microtick (Matteo Collina) [#52005](https://github.com/nodejs/node/pull/52005)
- \[[`bee3b364f9`](https://github.com/nodejs/node/commit/bee3b364f9)] - **test**: add regression test for test_runner after hook (Colin Ihrig) [#51998](https://github.com/nodejs/node/pull/51998)
- \[[`fff7f48f50`](https://github.com/nodejs/node/commit/fff7f48f50)] - **test**: reduce flakiness of `test-runner-output` (Antoine du Hamel) [#51952](https://github.com/nodejs/node/pull/51952)
- \[[`57ba8f5acb`](https://github.com/nodejs/node/commit/57ba8f5acb)] - **test**: fix flaky http-chunk-extensions-limit test (Ethan Arrowood) [#51943](https://github.com/nodejs/node/pull/51943)
- \[[`9d2c03990a`](https://github.com/nodejs/node/commit/9d2c03990a)] - **test**: remove flaky designation (Luigi Pinca) [#51736](https://github.com/nodejs/node/pull/51736)
- \[[`e992af81d3`](https://github.com/nodejs/node/commit/e992af81d3)] - **test**: skip SEA tests when SEA generation fails (Joyee Cheung) [#51887](https://github.com/nodejs/node/pull/51887)
- \[[`85aa6ca850`](https://github.com/nodejs/node/commit/85aa6ca850)] - _**Revert**_ "**test_runner**: do not invoke after hook when test is empty" (Colin Ihrig) [#51998](https://github.com/nodejs/node/pull/51998)

Windows 32-bit Installer: https://nodejs.org/dist/v21.7.1/node-v21.7.1-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v21.7.1/node-v21.7.1-x64.msi \
Windows ARM 64-bit Installer: https://nodejs.org/dist/v21.7.1/node-v21.7.1-arm64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v21.7.1/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v21.7.1/win-x64/node.exe \
Windows ARM 64-bit Binary: https://nodejs.org/dist/v21.7.1/win-arm64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v21.7.1/node-v21.7.1.pkg \
macOS Apple Silicon 64-bit Binary: https://nodejs.org/dist/v21.7.1/node-v21.7.1-darwin-arm64.tar.gz \
macOS Intel 64-bit Binary: https://nodejs.org/dist/v21.7.1/node-v21.7.1-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v21.7.1/node-v21.7.1-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v21.7.1/node-v21.7.1-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v21.7.1/node-v21.7.1-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v21.7.1/node-v21.7.1-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v21.7.1/node-v21.7.1-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v21.7.1/node-v21.7.1-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v21.7.1/node-v21.7.1.tar.gz \
Other release files: https://nodejs.org/dist/v21.7.1/ \
Documentation: https://nodejs.org/docs/v21.7.1/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

cba4d1d6c05684edeb88f06351c61c3acdb3d8d6df46b9dcd8ca89af847268e5  node-v21.7.1-aix-ppc64.tar.gz
d21f34e3864d93a8491591002e67a704a8a8ac5f9035d9da890327f884edd1c6  node-v21.7.1-arm64.msi
0a4dde483479dcf272f3d4d7afb509c3d45cf7a1ea451ce108f434dad4fa43e4  node-v21.7.1-darwin-arm64.tar.gz
20b26630c1c6d2c3db9815fca135931bdbe2b5c1c228a3f1f6a9ed6dde91ef76  node-v21.7.1-darwin-arm64.tar.xz
a406f4fbe68ff33319d513fa645aa7b9508c2cb65299f0a2de1757c2a6333c85  node-v21.7.1-darwin-x64.tar.gz
9212f90b3aaaadaf38fe32639e12ceee3c82380b50cc67402f660a9daecb7e87  node-v21.7.1-darwin-x64.tar.xz
deb71c9d4698eb835ff748671d5f04b2e0c7f53e0de457d4ee9f926ab9d285d9  node-v21.7.1-headers.tar.gz
c33217484a2f8a3aa556a4c46144757031d1a8955867e004a3db6996b67c3415  node-v21.7.1-headers.tar.xz
466647785722c5b9b9f2e430e11645e16f1d112b303b0ffdf2d5fa0eb95e647c  node-v21.7.1-linux-arm64.tar.gz
d384c843621ccb80f1367fdff85d4e3870bc934ffe37ed48eb320ebebba8ddbb  node-v21.7.1-linux-arm64.tar.xz
75a2787505079e972fe91d9507be0a13be2a7cf009db7a520a725bc280225704  node-v21.7.1-linux-armv7l.tar.gz
e375b24d57c8126d58ae31b807c91df6532c52e3441e22d2b3cde5116553c8fb  node-v21.7.1-linux-armv7l.tar.xz
5c70f1b4d960130751334c064e5b164c3366900baf8d3ee2a11d8c444ba9c84f  node-v21.7.1-linux-ppc64le.tar.gz
28b98d0ec62937bd0769327272615683caabef5c0a5112bd19b9b02d9332e732  node-v21.7.1-linux-ppc64le.tar.xz
6bf591654202c04037ee24de8c3940aff0b54d7449b9c13850f204a9bee11bce  node-v21.7.1-linux-s390x.tar.gz
30d5f6eeb3b94ff150d0fc2c07527cd70c8a1ff0384b3ef191663ffe66cb4934  node-v21.7.1-linux-s390x.tar.xz
c7b15146aed968b781c235b6a8f67608be559c4615de9526a9851ae28660cc09  node-v21.7.1-linux-x64.tar.gz
cb25d7a4aa57d15f280ce45cd72f95e9d2020702b7ca75c7fe632444f7c0452c  node-v21.7.1-linux-x64.tar.xz
3a8ecf4f887cbe37a1c7a1b720ed259c9e5cdb3648986a148a02211b4a77c6fa  node-v21.7.1.pkg
0ba90deb3e4de7c4665cdaabafe2c50d48c6b47e44863bb557ae1b7f01112f40  node-v21.7.1.tar.gz
1272b6e129d564dbde17527b844210b971c20a70ae729268186b7cb9d990a64b  node-v21.7.1.tar.xz
6ae0f60b24443708b4673b856c66827c2361957c01ee0c9628281486f0191615  node-v21.7.1-win-arm64.7z
c8931f7130c38e175aa55dfbe4235b20af033ec59d174f4dabf8809b79abe0d5  node-v21.7.1-win-arm64.zip
7c55e73c25e491a22e302e3919dd58145030a2f14bc6e9b2fed0a45c7dd6f867  node-v21.7.1-win-x64.7z
debff16a17e92d084dc19b98b21be35b15d9627befab1c8311b4ff946bf51773  node-v21.7.1-win-x64.zip
a64136c1aeafb096e8461c304c18eaa910d81f75e1ead5155a5548f4e3733a9b  node-v21.7.1-win-x86.7z
5db22af240445b0afadadedac497c8b57960f6d27828bb03040be90dfe7561f6  node-v21.7.1-win-x86.zip
e6354ee73967ce6b2ae401edb1d54adaedb321123308e2af1dec71a497e73eaa  node-v21.7.1-x64.msi
8673470064c13f491e594b4f4522eba504fc9082db728eed9ba43987b88de69b  node-v21.7.1-x86.msi
00d60e58adb4884085675d48064426c0745799169c79e553e1523ffe88f26fd4  win-arm64/node.exe
b068c2ffb9fa47420d55d44bb24ae42211007bbc34426cf68a663b34f8187a41  win-arm64/node.lib
600be209fe1a2a4693eef98393cea9ab3bbc3f08b974ad004cf0aaa944ed2488  win-arm64/node_pdb.7z
53273a368d2079975617d3a5566c5006ee25af68e58408b7cd0842e342acac17  win-arm64/node_pdb.zip
39908c8a16f867e5a2e9666ba8089dd497c4fa48a8008bed9d52cd78181944b2  win-x64/node.exe
96d09c2055c2f252122c86b65d2aabd5f90b1a075844f24bf8bcdbab05baf53e  win-x64/node.lib
1f4ece18dbfb3ea96f242f1ca94b309c1e56a50bc6138c33c842d45b198abc7f  win-x64/node_pdb.7z
73f6d43a1678df35720662d9052a0a80321c9e92e9c87ef7dc9d70da31197fe9  win-x64/node_pdb.zip
1f8c02748de3c6aefe7f40939db330ec03afe95fff23bbe12ba93fa9f0c180bc  win-x86/node.exe
19f86a492bf3b2a2854ebf0d05734afd9866348fc6474ed5b8c53c62ec9b7aa2  win-x86/node.lib
316254a7e269bcdcc37bb831b280c59f565745be01294b4024b8aef19507fe0b  win-x86/node_pdb.7z
36186f7fa5b9eb53ed12945b9506d91c487e0fc78b27267c949f609f0e36803e  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEj8yhP+8dDC6RAI4Jdw96mlrhVgAFAmXriKcACgkQdw96mlrh
VgBrkw//SzO0ZJzCWC0IbIEiQdhMwsC/MAtO1xM1icSKq8aY1egcQBFVN+e3j+Bz
tYKcNRfl6+N+pnGHP3ilWUQlmVnZSu7LwBCz9RO78mTRJ/a+iwAJRXiuH632DbKj
JX9vRhkPn6I57Yl5G1xswA5vdfwSFksFARtFGasnGvW1R1fcxvQqpwpkU6Mm4SdM
k2YtOoWjrg6NzE2J+36IsYvd/6Ld7BqJrUqnLEgqitLODgcgwgDYD1eteUiTNnEX
UMn2VFXYtNGT4P67pfaGPOqyKNisVsbS+V3LfV+uaOicMVO9TLm2Q0OQjjNU1QaF
VkpdS6iJ0/hGokG8V1FI+Ev/S+U8qCsO8e/ZEegZuh5bbhwpfq2EImhGLMozARsD
S6OyNuvjg66GamSp8/Cg32rfhYzIk9UAZVf7BOocuXMSNS/gUwQGwSItoOp0lepP
kiAiOq13CgPIQW/ULWTnmbtkKWfx6MWiyEqNGTPnl5gy7/MRfwMKiHtXUBjecNwL
+3xunOeABKzM2KeGFMTl8ePYjPZPdpNewN4gYXafKPcErrg8NLnguhU52Ut94Uu2
tY00SqJdqmyGYTGfUtfwm/aRxeF1pUA4B8rGMVxbw3pyXONRXBVjwv6st1VWP5CU
yxDkt8AQlEj70zoXQcCK5vYgHbQ6c0kmtnDlqPWUCPOG5nr8Aes=
=hnXW
-----END PGP SIGNATURE-----
```
