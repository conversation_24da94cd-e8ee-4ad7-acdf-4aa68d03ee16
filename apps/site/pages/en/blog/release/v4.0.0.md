---
date: '2015-09-08T12:10:06.000Z'
category: release
title: Node v4.0.0 (Current)
layout: blog-post
author: The Node.js Project
---

The collaborators of the Node.js project and the members of the Node.js Foundation are proud to offer v4.0.0 for general release. This release represents countless hours of hard work encapsulated in both the Node.js project and the io.js project that are now combined in a single codebase. The Node.js project is now operated by a team of 44 collaborators, 15 of which form its Technical Steering Committee (TSC). Further, over 100 new individuals have been added to the list of people contributing code to core since v0.12.7.

Node.js v4.0.0 contains V8 v4.5, the same version of V8 shipping with the Chrome web browser today. This brings with it many bonuses for Node.js users, most notably a raft of new [ES6](https://nodejs.org/docs/es6/) features that are enabled by default including block scoping, classes, typed arrays (Node's `Buffer` is now backed by `Uint8Array`), generators, Promises, Symbols, template strings, collections (Map, Set, etc.) and, new to V8 v4.5, arrow functions.

Over the past year thanks to donations from companies such as Rackspace, DigitalOcean, Scaleway, miniNodes, Voxer, Joyent, CloudFlare, ARM Holdings, and individuals from the community, we've built an amazing test cluster that includes an impressive line-up of Linux variants, OS X, Windows, FreeBSD and SmartOS. New to Node.js, we also have first-class support for ARM processors. Our test suite must be continually passing on ARMv6, ARMv7 and the new 64-bit ARMv8 processors. So Node.js is officially ready for use by hobbyists and ARM server users alike.

Node.js v4 also brings a plan for [long-term support (LTS)](https://github.com/nodejs/LTS/) and a regular release cycle. Release versioning now follows [SemVer](http://semver.org/), so expect increments of both minor and patch version over the coming weeks as we fix bugs and add features. What we won't be doing within the v4.x branch is adding breaking changes.

However, we have mapped a quick initial path to our first LTS version which will come in October. At the same time, a new v5.x branch will be cut and we will begin making a new series of Stable releases available, starting with v5.0.0. These will likely contain a new version of V8 and any breaking changes that may be necessary. The v4.x line of releases will continue for an additional 18 months in LTS, with a focus on stability and security and no unnecessary additions in functionality. Beyond that, v4.x will move in to Maintenance for an additional 12 months where it will continue to receive attention for critical bugs and security fixes.

In parallel, we will be branching a new Stable line of releases every 6 months, one in October and one in April each year. Stable lines will be made up of regular releases as development continues, expect minor and patch version bumps every week or two from these. Every second Stable release line will continue life in LTS form by the time October comes around. This means that there will be overlapping LTS branches being maintained throughout the year, each receiving attention for a total of 30 months (LTS plus Maintenance). We believe that the Stable and LTS cycles provide the room for the project to continue its **innovation** and stay with the cutting-edge of JavaScript while also serving the need for **predictable long-term stability and security** demanded by the growing line-up of enterprise users proudly adopting Node.js as a key technology; some of who already count themselves as [members of the Node.js Foundation](https://foundation.nodejs.org/members/).

As always with a major release, we don't expect the adoption of Node.js v4 to be painless. It will take time to adapt, particularly as compiled add-ons catch up with the new version of V8 (hint: see [NAN](https://github.com/nodejs/nan) for our official solution to the compatibility problem). Thankfully, the io.js v3.x series of releases have given many add-on authors a chance to prepare their code for Node.js v4, so much of the npm ecosystem is ready today.

An initial list of breaking changes between v0.12 and v4 can be found on the [LTS wiki](https://github.com/nodejs/LTS/wiki/Breaking-changes-between-v0.12-and-next-LTS-release), in the coming days we will continue to populate both this wiki and the [core wiki](https://github.com/nodejs/node/wiki) with documentation regarding the changes you should be aware of.

Please file any bugs at our new home on GitHub, <https://github.com/nodejs/node>, and consider joining the growing crew of collaborators by contributing to core!

### Notable changes

This list of changes is relative to the last io.js v3.x branch release, v3.3.0. Please see the list of notable changes in the v3.x, v2.x and v1.x releases compiled in unified [CHANGELOG](https://github.com/nodejs/node/blob/main/CHANGELOG.md) for a more complete list of changes from 0.12.x. Note, that some changes in the v3.x series as well as major breaking changes in this release constitute changes required for full convergence of the Node.js and io.js projects.

- **child_process**: `ChildProcess.prototype.send()` and `process.send()` operate asynchronously across all platforms so an optional callback parameter has been introduced that will be invoked once the message has been sent, i.e. `.send(message[, sendHandle][, callback])` (Ben Noordhuis) [#2620](https://github.com/nodejs/node/pull/2620).
- **node**: Rename "io.js" code to "Node.js" (cjihrig) [#2367](https://github.com/nodejs/node/pull/2367).
- **node-gyp**: This release bundles an updated version of node-gyp that works with all versions of Node.js and io.js including nightly and release candidate builds. From io.js v3 and Node.js v4 onward, it will only download a headers tarball when building addons rather than the entire source. (Rod Vagg) [#2700](https://github.com/nodejs/node/pull/2700)
- **npm**: Upgrade to version 2.14.2 from 2.13.3, includes a security update, see https://github.com/npm/npm/releases/tag/v2.14.2 for more details, (Kat Marchán) [#2696](https://github.com/nodejs/node/pull/2696).
- **timers**: Improved timer performance from porting the 0.12 implementation, plus minor fixes (Jeremiah Senkpiel) [#2540](https://github.com/nodejs/node/pull/2540), (Julien Gilli) [nodejs/node-v0.x-archive#8751](https://github.com/nodejs/node-v0.x-archive/pull/8751) [nodejs/node-v0.x-archive#8905](https://github.com/nodejs/node-v0.x-archive/pull/8905)
- **util**: The `util.is*()` functions have been deprecated, beginning with deprecation warnings in the documentation for this release, users are encouraged to seek more robust alternatives in the npm registry, (Sakthipriyan Vairamani) [#2447](https://github.com/nodejs/node/pull/2447).
- **v8**: Upgrade to version ********** from ********* (Ali Ijaz Sheikh) [#2632](https://github.com/nodejs/node/pull/2632).
  - Implement new `TypedArray` prototype methods: `copyWithin()`, `every()`, `fill()`, `filter()`, `find()`, `findIndex()`, `forEach()`, `indexOf()`, `join()`, `lastIndexOf()`, `map()`, `reduce()`, `reduceRight()`, `reverse()`, `slice()`, `some()`, `sort()`. See https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/TypedArray for further information.
  - Implement new `TypedArray.from()` and `TypedArray.of()` functions. See https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/TypedArray for further information.
  - Implement arrow functions, see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Functions/Arrow_functions for further information.
  - Full ChangeLog available at https://github.com/v8/v8-git-mirror/blob/4.5.103/ChangeLog

### Known issues

See https://github.com/nodejs/node/labels/confirmed-bug for complete and current list of known issues.

- Some uses of computed object shorthand properties are not handled correctly by the current version of V8. e.g. `[{ [prop]: val }]` evaluates to `[{}]`. [#2507](https://github.com/nodejs/node/issues/2507)
- Some problems with unreferenced timers running during `beforeExit` are still to be resolved. See [#1264](https://github.com/nodejs/node/issues/1264).
- Surrogate pair in REPL can freeze terminal. [#690](https://github.com/nodejs/node/issues/690)
- Calling `dns.setServers()` while a DNS query is in progress can cause the process to crash on a failed assertion. [#894](https://github.com/nodejs/node/issues/894)
- `url.resolve` may transfer the auth portion of the url when resolving between two full hosts, see [#1435](https://github.com/nodejs/node/issues/1435).

### Commits

- [[`4f50d3fb90`](https://github.com/nodejs/node/commit/4f50d3fb90)] - **(SEMVER-MAJOR)** This commit sets the value of process.release.name to "node". (cjihrig) [#2367](https://github.com/nodejs/node/pull/2367)
- [[`d3178d8b1b`](https://github.com/nodejs/node/commit/d3178d8b1b)] - **buffer**: SlowBuffer only accept valid numeric values (Michaël Zasso) [#2635](https://github.com/nodejs/node/pull/2635)
- [[`0cb0f4a6e4`](https://github.com/nodejs/node/commit/0cb0f4a6e4)] - **build**: fix v8_enable_handle_zapping override (Karl Skomski) [#2731](https://github.com/nodejs/node/pull/2731)
- [[`a7596d7efc`](https://github.com/nodejs/node/commit/a7596d7efc)] - **build**: remote commands on staging in single session (Rod Vagg) [#2717](https://github.com/nodejs/node/pull/2717)
- [[`be427e9efa`](https://github.com/nodejs/node/commit/be427e9efa)] - **build**: make .msi install to "nodejs", not "node" (Rod Vagg) [#2701](https://github.com/nodejs/node/pull/2701)
- [[`5652ce0dbc`](https://github.com/nodejs/node/commit/5652ce0dbc)] - **build**: fix .pkg creation tooling (Rod Vagg) [#2687](https://github.com/nodejs/node/pull/2687)
- [[`101db80111`](https://github.com/nodejs/node/commit/101db80111)] - **build**: add --enable-asan with builtin leakcheck (Karl Skomski) [#2376](https://github.com/nodejs/node/pull/2376)
- [[`2c3939c9c0`](https://github.com/nodejs/node/commit/2c3939c9c0)] - **child_process**: use stdio.fd even if it is 0 (Evan Lucas) [#2727](https://github.com/nodejs/node/pull/2727)
- [[`609db5a1dd`](https://github.com/nodejs/node/commit/609db5a1dd)] - **child_process**: check execFile and fork args (James M Snell) [#2667](https://github.com/nodejs/node/pull/2667)
- [[`d010568c23`](https://github.com/nodejs/node/commit/d010568c23)] - **(SEMVER-MAJOR)** **child_process**: add callback parameter to .send() (Ben Noordhuis) [#2620](https://github.com/nodejs/node/pull/2620)
- [[`c60857a81a`](https://github.com/nodejs/node/commit/c60857a81a)] - **cluster**: allow shared reused dgram sockets (Fedor Indutny) [#2548](https://github.com/nodejs/node/pull/2548)
- [[`b2ecbb6191`](https://github.com/nodejs/node/commit/b2ecbb6191)] - **contextify**: ignore getters during initialization (Fedor Indutny) [#2091](https://github.com/nodejs/node/pull/2091)
- [[`3711934095`](https://github.com/nodejs/node/commit/3711934095)] - **cpplint**: make it possible to run outside git repo (Ben Noordhuis) [#2710](https://github.com/nodejs/node/pull/2710)
- [[`03f900ab25`](https://github.com/nodejs/node/commit/03f900ab25)] - **crypto**: replace rwlocks with simple mutexes (Ben Noordhuis) [#2723](https://github.com/nodejs/node/pull/2723)
- [[`847459c29b`](https://github.com/nodejs/node/commit/847459c29b)] - **(SEMVER-MAJOR)** **crypto**: show exponent in decimal and hex (Chad Johnston) [#2320](https://github.com/nodejs/node/pull/2320)
- [[`e1c976184d`](https://github.com/nodejs/node/commit/e1c976184d)] - **deps**: improve ArrayBuffer performance in v8 (Fedor Indutny) [#2732](https://github.com/nodejs/node/pull/2732)
- [[`cc0ab17a23`](https://github.com/nodejs/node/commit/cc0ab17a23)] - **deps**: float node-gyp v3.0.0 (Rod Vagg) [#2700](https://github.com/nodejs/node/pull/2700)
- [[`b2c3c6d727`](https://github.com/nodejs/node/commit/b2c3c6d727)] - **deps**: create .npmrc during npm tests (Kat Marchán) [#2696](https://github.com/nodejs/node/pull/2696)
- [[`babdbfdbd5`](https://github.com/nodejs/node/commit/babdbfdbd5)] - **deps**: upgrade to npm 2.14.2 (Kat Marchán) [#2696](https://github.com/nodejs/node/pull/2696)
- [[`155783d876`](https://github.com/nodejs/node/commit/155783d876)] - **deps**: backport 75e43a6 from v8 upstream (again) (saper) [#2692](https://github.com/nodejs/node/pull/2692)
- [[`5424d6fcf0`](https://github.com/nodejs/node/commit/5424d6fcf0)] - **deps**: upgrade V8 to ********** (Ali Ijaz Sheikh) [#2632](https://github.com/nodejs/node/pull/2632)
- [[`c43172578e`](https://github.com/nodejs/node/commit/c43172578e)] - **(SEMVER-MAJOR)** **deps**: upgrade V8 to 4.5.103.24 (Ali Ijaz Sheikh) [#2509](https://github.com/nodejs/node/pull/2509)
- [[`714e96e8b9`](https://github.com/nodejs/node/commit/714e96e8b9)] - **deps**: backport 75e43a6 from v8 upstream (saper) [#2636](https://github.com/nodejs/node/pull/2636)
- [[`8637755cbf`](https://github.com/nodejs/node/commit/8637755cbf)] - **doc**: add TSC meeting minutes 2015-09-02 (Rod Vagg) [#2674](https://github.com/nodejs/node/pull/2674)
- [[`d3d5b93214`](https://github.com/nodejs/node/commit/d3d5b93214)] - **doc**: update environment vars in manpage and --help (Roman Reiss) [#2690](https://github.com/nodejs/node/pull/2690)
- [[`29f586ac0a`](https://github.com/nodejs/node/commit/29f586ac0a)] - **doc**: update url doc to account for escaping (Jeremiah Senkpiel) [#2605](https://github.com/nodejs/node/pull/2605)
- [[`ba50cfebef`](https://github.com/nodejs/node/commit/ba50cfebef)] - **doc**: reorder collaborators by their usernames (Johan Bergström) [#2322](https://github.com/nodejs/node/pull/2322)
- [[`8a9a3bf798`](https://github.com/nodejs/node/commit/8a9a3bf798)] - **doc**: update changelog for io.js v3.3.0 (Rod Vagg) [#2653](https://github.com/nodejs/node/pull/2653)
- [[`6cd0e2664b`](https://github.com/nodejs/node/commit/6cd0e2664b)] - **doc**: update io.js reference (Ben Noordhuis) [#2580](https://github.com/nodejs/node/pull/2580)
- [[`f9539c19e8`](https://github.com/nodejs/node/commit/f9539c19e8)] - **doc**: update changelog for io.js v3.2.0 (Rod Vagg) [#2512](https://github.com/nodejs/node/pull/2512)
- [[`cded6e7993`](https://github.com/nodejs/node/commit/cded6e7993)] - **doc**: fix CHANGELOG.md on master (Roman Reiss) [#2513](https://github.com/nodejs/node/pull/2513)
- [[`93e2830686`](https://github.com/nodejs/node/commit/93e2830686)] - **(SEMVER-MINOR)** **doc**: document deprecation of util.is\* functions (Sakthipriyan Vairamani) [#2447](https://github.com/nodejs/node/pull/2447)
- [[`7038388558`](https://github.com/nodejs/node/commit/7038388558)] - **doc,test**: enable recursive file watching in Windows (Sakthipriyan Vairamani) [#2649](https://github.com/nodejs/node/pull/2649)
- [[`f3696f64a1`](https://github.com/nodejs/node/commit/f3696f64a1)] - **events,lib**: don't require EE#listenerCount() (Jeremiah Senkpiel) [#2661](https://github.com/nodejs/node/pull/2661)
- [[`45a2046f5d`](https://github.com/nodejs/node/commit/45a2046f5d)] - **(SEMVER-MAJOR)** **installer**: fix installers for node.js rename (Frederic Hemberger) [#2367](https://github.com/nodejs/node/pull/2367)
- [[`7a999a1376`](https://github.com/nodejs/node/commit/7a999a1376)] - **(SEMVER-MAJOR)** **lib**: add net.Socket#localFamily property (Ben Noordhuis) [#956](https://github.com/nodejs/node/pull/956)
- [[`de88255b0f`](https://github.com/nodejs/node/commit/de88255b0f)] - **_Revert_** "**lib,src**: add unix socket getsockname/getpeername" (Ben Noordhuis) [#2584](https://github.com/nodejs/node/pull/2584)
- [[`f337595441`](https://github.com/nodejs/node/commit/f337595441)] - **(SEMVER-MAJOR)** **lib,src**: add unix socket getsockname/getpeername (Ben Noordhuis) [#956](https://github.com/nodejs/node/pull/956)
- [[`3b602527d1`](https://github.com/nodejs/node/commit/3b602527d1)] - **(SEMVER-MAJOR)** **node**: additional cleanup for node rename (cjihrig) [#2367](https://github.com/nodejs/node/pull/2367)
- [[`a69ab27ab4`](https://github.com/nodejs/node/commit/a69ab27ab4)] - **(SEMVER-MAJOR)** **node**: rename from io.js to node (cjihrig) [#2367](https://github.com/nodejs/node/pull/2367)
- [[`9358eee9dd`](https://github.com/nodejs/node/commit/9358eee9dd)] - **node-gyp**: float 3.0.1, minor fix for download url (Rod Vagg) [#2737](https://github.com/nodejs/node/pull/2737)
- [[`d2d981252b`](https://github.com/nodejs/node/commit/d2d981252b)] - **src**: s/ia32/x86 for process.release.libUrl for win (Rod Vagg) [#2699](https://github.com/nodejs/node/pull/2699)
- [[`eba3d3dccd`](https://github.com/nodejs/node/commit/eba3d3dccd)] - **src**: use standard conform snprintf on windows (Karl Skomski) [#2404](https://github.com/nodejs/node/pull/2404)
- [[`cddbec231f`](https://github.com/nodejs/node/commit/cddbec231f)] - **src**: fix buffer overflow for long exception lines (Karl Skomski) [#2404](https://github.com/nodejs/node/pull/2404)
- [[`dd3f3417c7`](https://github.com/nodejs/node/commit/dd3f3417c7)] - **src**: re-enable fast math on arm (Michaël Zasso) [#2592](https://github.com/nodejs/node/pull/2592)
- [[`e137c1177c`](https://github.com/nodejs/node/commit/e137c1177c)] - **(SEMVER-MAJOR)** **src**: enable vector ics on arm again (Ali Ijaz Sheikh) [#2509](https://github.com/nodejs/node/pull/2509)
- [[`7ce749d722`](https://github.com/nodejs/node/commit/7ce749d722)] - **src**: replace usage of v8::Handle with v8::Local (Michaël Zasso) [#2202](https://github.com/nodejs/node/pull/2202)
- [[`b1a2d9509f`](https://github.com/nodejs/node/commit/b1a2d9509f)] - **src**: enable v8 deprecation warnings and fix them (Ben Noordhuis) [#2091](https://github.com/nodejs/node/pull/2091)
- [[`808de0da03`](https://github.com/nodejs/node/commit/808de0da03)] - **(SEMVER-MAJOR)** **src**: apply debug force load fixups from 41e63fb (Ali Ijaz Sheikh) [#2509](https://github.com/nodejs/node/pull/2509)
- [[`5201cb0ff1`](https://github.com/nodejs/node/commit/5201cb0ff1)] - **src**: fix memory leak in ExternString (Karl Skomski) [#2402](https://github.com/nodejs/node/pull/2402)
- [[`2308a27c0a`](https://github.com/nodejs/node/commit/2308a27c0a)] - **src**: only set v8 flags if argc > 1 (Evan Lucas) [#2646](https://github.com/nodejs/node/pull/2646)
- [[`384effed20`](https://github.com/nodejs/node/commit/384effed20)] - **test**: fix use of `common` before required (Rod Vagg) [#2685](https://github.com/nodejs/node/pull/2685)
- [[`f146f686b7`](https://github.com/nodejs/node/commit/f146f686b7)] - **(SEMVER-MAJOR)** **test**: fix test-repl-tab-complete.js for V8 4.5 (Ali Ijaz Sheikh) [#2509](https://github.com/nodejs/node/pull/2509)
- [[`fe4b309fd3`](https://github.com/nodejs/node/commit/fe4b309fd3)] - **test**: refactor to eliminate flaky test (Rich Trott) [#2609](https://github.com/nodejs/node/pull/2609)
- [[`619721e6b8`](https://github.com/nodejs/node/commit/619721e6b8)] - **test**: mark eval_messages as flaky (Alexis Campailla) [#2648](https://github.com/nodejs/node/pull/2648)
- [[`93ba585b66`](https://github.com/nodejs/node/commit/93ba585b66)] - **test**: mark test-vm-syntax-error-stderr as flaky (João Reis) [#2662](https://github.com/nodejs/node/pull/2662)
- [[`367140bca0`](https://github.com/nodejs/node/commit/367140bca0)] - **test**: mark test-repl-persistent-history as flaky (João Reis) [#2659](https://github.com/nodejs/node/pull/2659)
- [[`f6b093343d`](https://github.com/nodejs/node/commit/f6b093343d)] - **timers**: minor `_unrefActive` fixes and improvements (Jeremiah Senkpiel) [#2540](https://github.com/nodejs/node/pull/2540)
- [[`403d7ee7d1`](https://github.com/nodejs/node/commit/403d7ee7d1)] - **timers**: don't mutate unref list while iterating it (Julien Gilli) [#2540](https://github.com/nodejs/node/pull/2540)
- [[`7a8c3e08c3`](https://github.com/nodejs/node/commit/7a8c3e08c3)] - **timers**: Avoid linear scan in `_unrefActive`. (Julien Gilli) [#2540](https://github.com/nodejs/node/pull/2540)
- [[`b630ebaf43`](https://github.com/nodejs/node/commit/b630ebaf43)] - **win,msi**: Upgrade from old upgrade code (João Reis) [#2439](https://github.com/nodejs/node/pull/2439)

Windows 32-bit Installer: https://nodejs.org/dist/v4.0.0/node-v4.0.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v4.0.0/node-v4.0.0-x64.msi \
Mac OS X 64-bit Installer: https://nodejs.org/dist/v4.0.0/node-v4.0.0.pkg \
Mac OS X 64-bit Binary: https://nodejs.org/dist/v4.0.0/node-v4.0.0-darwin-x64.tar.gz \
Linux 32-bit Binary: https://nodejs.org/dist/v4.0.0/node-v4.0.0-linux-x86.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v4.0.0/node-v4.0.0-linux-x64.tar.gz \
SmartOS 32-bit Binary: https://nodejs.org/dist/v4.0.0/node-v4.0.0-sunos-x86.tar.gz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v4.0.0/node-v4.0.0-sunos-x64.tar.gz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v4.0.0/node-v4.0.0-linux-armv6l.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v4.0.0/node-v4.0.0-linux-armv7l.tar.gz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v4.0.0/node-v4.0.0-linux-arm64.tar.gz \
Source Code: https://nodejs.org/dist/v4.0.0/node-v4.0.0.tar.gz \
Other release files: https://nodejs.org/dist/v4.0.0/ \
Documentation: https://nodejs.org/docs/v4.0.0/api/

Shasums (GPG signing hash: SHA1, file hash: SHA256):

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA1

4893cadaa55fe88b214371b23257d7fae4b30661ef01d352e3f930ba9d402ee0  node-v4.0.0-darwin-x64.tar.gz
1f2b278d4708d7d956f2a17be139af26955114b92cb4340735a10bb5f0585c0a  node-v4.0.0-darwin-x64.tar.xz
8812b88e81ee258f7f9446b548f6b364edcdd455f73f06589f14dd71dca415d0  node-v4.0.0-headers.tar.gz
776962bd95bf295e336fbf04f0d8764bb9f6236e0c699358b17e4e2a95ee3d6f  node-v4.0.0-headers.tar.xz
0436f107e1d82c61c3ee4f916781466a49bece74bf4d4fb4bf4d53a57b81df85  node-v4.0.0-linux-arm64.tar.gz
7fdb978ddda803b188958b368db13b97dcaacfbf96d40c99d13ed385499d3fdd  node-v4.0.0-linux-arm64.tar.xz
6f88e532bbf55cff36d4940898b80359761de3a274cc214fbb58cc176ec49ba2  node-v4.0.0-linux-armv6l.tar.gz
d7c1e9fefb360bde70c543d8981d2cce9a1c8444e847d19a89604756570d3075  node-v4.0.0-linux-armv6l.tar.xz
a788ed1b04de00344f4bbb8a278b317c69f9672f5fd03adb24f53d7c3d6fc8f6  node-v4.0.0-linux-armv7l.tar.gz
1ed96526e87c29b4d3b5618b5d2e3e65e1aef6a5e40f90ec3a3afd21047517cf  node-v4.0.0-linux-armv7l.tar.xz
df8ada31840e3dc48c7fe7291c7eba70b2ce5a6b6d959ac01157b04731c8a88f  node-v4.0.0-linux-x64.tar.gz
cc4eae3e4c2dd8b068d69e6d1d9849a863c51e3ec3d7f85cfb1e91e63912b66a  node-v4.0.0-linux-x64.tar.xz
444b0c2e38602699103e4adb76c9f15b76c405f58b1308751cdc194041a39b93  node-v4.0.0-linux-x86.tar.gz
6fe4ab43eae74c1cb376169f4f161bddef04ff8a693a772875de651fafa4252b  node-v4.0.0-linux-x86.tar.xz
387dd01caf168267749b0ddc7702b2fef7c95fbe1b2dab9a41ded02c11f16889  node-v4.0.0.pkg
f17ea8b1ffa6d63ba4eaa37708a365e5aefdb7de136f8b1a5751f2f258507026  node-v4.0.0-sunos-x64.tar.gz
69188f3568bbc9c071c70cf92551dd15e6fb79c9bd5d8b3da136ed65af4adea1  node-v4.0.0-sunos-x64.tar.xz
4537640f912b1db217351271e15d092d4654ecbe7cdb0169313988de5e453cd3  node-v4.0.0-sunos-x86.tar.gz
99ce4830b6b4f10dfca050296c001beb60e47d3eda455bc5b287b4ecc0773c04  node-v4.0.0-sunos-x86.tar.xz
e110e5a066f3a6fe565ede7dd66f3727384b9b5c5fbf46f8db723d726e2f5900  node-v4.0.0.tar.gz
2e3c5069933bead1022848009a68426a10689e67c4204707798fa319a6847bb5  node-v4.0.0.tar.xz
28dfb26fddb3ff56456bfc0ec186db27e6b84d9ce98752f34d48758f4af05fbe  node-v4.0.0-x64.msi
ba67b46504afeedfd43d0ebb51844e2b427d64b631fe05d6fc35f7fc59d05307  node-v4.0.0-x86.msi
4c468e9a5e664f700f664fde42576260a7dd2bd5625ab824a713845e231f8275  win-x64/node.exe
c8189c310c774ce3cbe960bd9920e49f47db97a19f1256b9b5bfa1f9d83d4dca  win-x64/node.lib
02814e5fdddbf77840e14042e98de3b50e0c0167aa1a8cccdf3222e643dd016f  win-x86/node.exe
1bbe7009ea25e3ffe72cc7da92725146536be698cefd960e0e22269d02368c25  win-x86/node.lib
-----BEGIN PGP SIGNATURE-----
Version: GnuPG v1

iQEcBAEBAgAGBQJV71yWAAoJEMJzeS99g1RdcXkH/3sBJ89TZdqvzGEB/FZ5x+5C
A4iAWfEa+RjSvJbtmo3noOUA7+OZPWVue6yEv3ZX4EEdk5ZIUEcf/4tYoW/D5cCm
SBlnCB5TXMp8ZafCni3OvR52gBPIB4EFPlqPDLLA6wloUYm7xhyJ5V+Z9DeTzvGl
CC6JDs0RkcqMT3799KBgrS/ud0G5BXCC1imnHxGUCgYc1BAcExHThbLs1zFyMuXg
SSQJVJGoxR7LWfvnGFPnEbliSCuUfjGIT3DHAgovJgjQirj/vLssLBYyxMhCVBEa
eR2+UlptmdbsIAsPvyNp2Tr466KlIawD99haYOfgmVnA1l1PWbAjJMdQZJPk+Mg=
=tBAw
-----END PGP SIGNATURE-----
```
