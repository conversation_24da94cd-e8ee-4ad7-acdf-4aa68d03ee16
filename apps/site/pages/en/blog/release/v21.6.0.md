---
date: '2024-01-15T14:52:59.834Z'
category: release
title: Node v21.6.0 (Current)
layout: blog-post
author: <PERSON>
---

## 2024-01-15, Version 21.6.0 (Current), @RafaelGSS

### New connection attempt events

Three new events were added in the `net.createConnection` flow:

- `connectionAttempt`: Emitted when a new connection attempt is established. In case of Happy Eyeballs, this might emitted multiple times.
- `connectionAttemptFailed`: Emitted when a connection attempt failed. In case of Happy Eyeballs, this might emitted multiple times.
- `connectionAttemptTimeout`: Emitted when a connection attempt timed out. In case of Happy Eyeballs, this will not be emitted for the last attempt. This is not emitted at all if Happy Eyeballs is not used.

Additionally, a previous bug has been fixed where a new connection attempt could have been started after a previous one failed and after the connection was destroyed by the user.
This led to a failed assertion.

Contributed by <PERSON> in [#51045](https://github.com/nodejs/node/pull/51045).

### Changes to the Permission Model

Node.js 21.6.0 comes with several fixes for the experimental permission model and two new semver-minor commits.
We're adding a new flag `--allow-addons` to enable addon usage when using the Permission Model.

```console
$ node --experimental-permission --allow-addons
```

Contributed by <PERSON> Gonzaga in [#51183](https://github.com/nodejs/node/pull/51183)

And relative paths are now supported through the `--allow-fs-*` flags.
Therefore, with this release one can use:

```console
$ node --experimental-permission --allow-fs-read=./index.js
```

To give only read access to the entrypoint of the application.

Contributed by Rafael Gonzaga and Carlos Espa in [#50758](https://github.com/nodejs/node/pull/50758)

### Support configurable snapshot through `--build-snapshot-config` flag

We are adding a new flag `--build-snapshot-config` to configure snapshots through a custom JSON configuration file.

```console
$ node --build-snapshot-config=/path/to/myconfig.json
```

When using this flag, additional script files provided on the command line will
not be executed and instead be interpreted as regular command line arguments.

These changes were contributed by Joyee Cheung and Anna Henningsen in [#50453](https://github.com/nodejs/node/pull/50453)

### Other Notable Changes

- \[[`c31ed51373`](https://github.com/nodejs/node/commit/c31ed51373)] - **(SEMVER-MINOR)** **timers**: export timers.promises (Marco Ippolito) [#51246](https://github.com/nodejs/node/pull/51246)

### Commits

- \[[`13a1241b83`](https://github.com/nodejs/node/commit/13a1241b83)] - **assert,crypto**: make KeyObject and CryptoKey testable for equality (Filip Skokan) [#50897](https://github.com/nodejs/node/pull/50897)
- \[[`4dcc5114aa`](https://github.com/nodejs/node/commit/4dcc5114aa)] - **benchmark**: remove dependency on unshipped tools (Adam Majer) [#51146](https://github.com/nodejs/node/pull/51146)
- \[[`2eb41f86b3`](https://github.com/nodejs/node/commit/2eb41f86b3)] - **build**: fix for VScode "Reopen in Container" (Serg Kryvonos) [#51271](https://github.com/nodejs/node/pull/51271)
- \[[`e03ac83c19`](https://github.com/nodejs/node/commit/e03ac83c19)] - **build**: fix arm64 cross-compilation (Michaël Zasso) [#51256](https://github.com/nodejs/node/pull/51256)
- \[[`cd61fce34e`](https://github.com/nodejs/node/commit/cd61fce34e)] - **build**: add `-flax-vector-conversions` to V8 build (Michaël Zasso) [#51257](https://github.com/nodejs/node/pull/51257)
- \[[`e5017a522e`](https://github.com/nodejs/node/commit/e5017a522e)] - **crypto**: update CryptoKey symbol properties (Filip Skokan) [#50897](https://github.com/nodejs/node/pull/50897)
- \[[`c0d2e8be11`](https://github.com/nodejs/node/commit/c0d2e8be11)] - **deps**: update corepack to 0.24.0 (Node.js GitHub Bot) [#51318](https://github.com/nodejs/node/pull/51318)
- \[[`24a9a72492`](https://github.com/nodejs/node/commit/24a9a72492)] - **deps**: update acorn to 8.11.3 (Node.js GitHub Bot) [#51317](https://github.com/nodejs/node/pull/51317)
- \[[`e53cbb22c2`](https://github.com/nodejs/node/commit/e53cbb22c2)] - **deps**: update ngtcp2 and nghttp3 (James M Snell) [#51291](https://github.com/nodejs/node/pull/51291)
- \[[`f00f1204f1`](https://github.com/nodejs/node/commit/f00f1204f1)] - **deps**: update brotli to 1.1.0 (Node.js GitHub Bot) [#50804](https://github.com/nodejs/node/pull/50804)
- \[[`a41dca0c51`](https://github.com/nodejs/node/commit/a41dca0c51)] - **deps**: update zlib to *******-motley-40e35a7 (Node.js GitHub Bot) [#51274](https://github.com/nodejs/node/pull/51274)
- \[[`efa12a89c6`](https://github.com/nodejs/node/commit/efa12a89c6)] - **deps**: update simdutf to 4.0.8 (Node.js GitHub Bot) [#51000](https://github.com/nodejs/node/pull/51000)
- \[[`25eba3d20b`](https://github.com/nodejs/node/commit/25eba3d20b)] - **deps**: V8: cherry-pick de611e69ad51 (Keyhan Vakil) [#51200](https://github.com/nodejs/node/pull/51200)
- \[[`a07d6e23e4`](https://github.com/nodejs/node/commit/a07d6e23e4)] - **deps**: update simdjson to 3.6.3 (Node.js GitHub Bot) [#51104](https://github.com/nodejs/node/pull/51104)
- \[[`6d1bfcb2dd`](https://github.com/nodejs/node/commit/6d1bfcb2dd)] - **deps**: update googletest to 530d5c8 (Node.js GitHub Bot) [#51191](https://github.com/nodejs/node/pull/51191)
- \[[`75e5615c43`](https://github.com/nodejs/node/commit/75e5615c43)] - **deps**: update acorn-walk to 8.3.1 (Node.js GitHub Bot) [#50457](https://github.com/nodejs/node/pull/50457)
- \[[`3ecc7dcc00`](https://github.com/nodejs/node/commit/3ecc7dcc00)] - **deps**: update acorn-walk to 8.3.0 (Node.js GitHub Bot) [#50457](https://github.com/nodejs/node/pull/50457)
- \[[`e2f8d741c8`](https://github.com/nodejs/node/commit/e2f8d741c8)] - **deps**: update zlib to *******-motley-dd5fc13 (Node.js GitHub Bot) [#51105](https://github.com/nodejs/node/pull/51105)
- \[[`4a5d3bda72`](https://github.com/nodejs/node/commit/4a5d3bda72)] - **doc**: the GN files should use Node's license (Cheng Zhao) [#50694](https://github.com/nodejs/node/pull/50694)
- \[[`84127514ba`](https://github.com/nodejs/node/commit/84127514ba)] - **doc**: improve localWindowSize event descriptions (Davy Landman) [#51071](https://github.com/nodejs/node/pull/51071)
- \[[`8ee882a49c`](https://github.com/nodejs/node/commit/8ee882a49c)] - **doc**: mark `--jitless` as experimental (Antoine du Hamel) [#51247](https://github.com/nodejs/node/pull/51247)
- \[[`876743ece1`](https://github.com/nodejs/node/commit/876743ece1)] - **doc**: run license-builder (github-actions\[bot]) [#51199](https://github.com/nodejs/node/pull/51199)
- \[[`ec6fcff009`](https://github.com/nodejs/node/commit/ec6fcff009)] - **doc**: fix limitations and known issues in pm (Rafael Gonzaga) [#51184](https://github.com/nodejs/node/pull/51184)
- \[[`c13a5c0373`](https://github.com/nodejs/node/commit/c13a5c0373)] - **doc**: mention node:wasi in the Threat Model (Rafael Gonzaga) [#51211](https://github.com/nodejs/node/pull/51211)
- \[[`4b19e62444`](https://github.com/nodejs/node/commit/4b19e62444)] - **doc**: remove ambiguous 'considered' (Rich Trott) [#51207](https://github.com/nodejs/node/pull/51207)
- \[[`5453abd6ad`](https://github.com/nodejs/node/commit/5453abd6ad)] - **doc**: set exit code in custom test runner example (Matteo Collina) [#51056](https://github.com/nodejs/node/pull/51056)
- \[[`f9d4e07faf`](https://github.com/nodejs/node/commit/f9d4e07faf)] - **doc**: remove version from `maintaining-dependencies.md` (Antoine du Hamel) [#51195](https://github.com/nodejs/node/pull/51195)
- \[[`df8927a073`](https://github.com/nodejs/node/commit/df8927a073)] - **doc**: mention native addons are restricted in pm (Rafael Gonzaga) [#51185](https://github.com/nodejs/node/pull/51185)
- \[[`e636d83914`](https://github.com/nodejs/node/commit/e636d83914)] - **doc**: correct note on behavior of stats.isDirectory (Nick Reilingh) [#50946](https://github.com/nodejs/node/pull/50946)
- \[[`1c71435c2a`](https://github.com/nodejs/node/commit/1c71435c2a)] - **doc**: fix `TestsStream` parent class (Jungku Lee) [#51181](https://github.com/nodejs/node/pull/51181)
- \[[`2c227b0d64`](https://github.com/nodejs/node/commit/2c227b0d64)] - **doc**: fix simdjson wrong link (Marco Ippolito) [#51177](https://github.com/nodejs/node/pull/51177)
- \[[`efa13e1943`](https://github.com/nodejs/node/commit/efa13e1943)] - **(SEMVER-MINOR)** **doc**: add documentation for --build-snapshot-config (Anna Henningsen) [#50453](https://github.com/nodejs/node/pull/50453)
- \[[`941aedc6fc`](https://github.com/nodejs/node/commit/941aedc6fc)] - **errors**: fix stacktrace of SystemError (uzlopak) [#49956](https://github.com/nodejs/node/pull/49956)
- \[[`47548d9e61`](https://github.com/nodejs/node/commit/47548d9e61)] - **esm**: fix hint on invalid module specifier (Antoine du Hamel) [#51223](https://github.com/nodejs/node/pull/51223)
- \[[`091098f40a`](https://github.com/nodejs/node/commit/091098f40a)] - **fs**: fix fs.promises.realpath for long paths on Windows (翠 / green) [#51032](https://github.com/nodejs/node/pull/51032)
- \[[`e5a8fa01aa`](https://github.com/nodejs/node/commit/e5a8fa01aa)] - **fs**: make offset, position & length args in fh.read() optional (Pulkit Gupta) [#51087](https://github.com/nodejs/node/pull/51087)
- \[[`c87e5d51cc`](https://github.com/nodejs/node/commit/c87e5d51cc)] - **fs**: add missing jsdoc parameters to `readSync` (Yagiz Nizipli) [#51225](https://github.com/nodejs/node/pull/51225)
- \[[`e24249cf37`](https://github.com/nodejs/node/commit/e24249cf37)] - **fs**: remove `internalModuleReadJSON` binding (Yagiz Nizipli) [#51224](https://github.com/nodejs/node/pull/51224)
- \[[`7421467812`](https://github.com/nodejs/node/commit/7421467812)] - **fs**: improve mkdtemp performance for buffer prefix (Yagiz Nizipli) [#51078](https://github.com/nodejs/node/pull/51078)
- \[[`5b229d775f`](https://github.com/nodejs/node/commit/5b229d775f)] - **fs**: validate fd synchronously on c++ (Yagiz Nizipli) [#51027](https://github.com/nodejs/node/pull/51027)
- \[[`c7a135962d`](https://github.com/nodejs/node/commit/c7a135962d)] - **http**: remove misleading warning (Luigi Pinca) [#51204](https://github.com/nodejs/node/pull/51204)
- \[[`a325746ff4`](https://github.com/nodejs/node/commit/a325746ff4)] - **http**: do not override user-provided options object (KuthorX) [#33633](https://github.com/nodejs/node/pull/33633)
- \[[`89eee7763f`](https://github.com/nodejs/node/commit/89eee7763f)] - **http2**: addtl http/2 settings (Marten Richter) [#49025](https://github.com/nodejs/node/pull/49025)
- \[[`624142947f`](https://github.com/nodejs/node/commit/624142947f)] - **lib**: fix use of `--frozen-intrinsics` with `--jitless` (Antoine du Hamel) [#51248](https://github.com/nodejs/node/pull/51248)
- \[[`8f845eb001`](https://github.com/nodejs/node/commit/8f845eb001)] - **lib**: move function declaration outside of loop (Sanjaiyan Parthipan) [#51242](https://github.com/nodejs/node/pull/51242)
- \[[`ed7305e49b`](https://github.com/nodejs/node/commit/ed7305e49b)] - **lib**: reduce overhead of `SafePromiseAllSettledReturnVoid` calls (Antoine du Hamel) [#51243](https://github.com/nodejs/node/pull/51243)
- \[[`291265ce27`](https://github.com/nodejs/node/commit/291265ce27)] - **lib**: expose default prepareStackTrace (Chengzhong Wu) [#50827](https://github.com/nodejs/node/pull/50827)
- \[[`8ff6bc45ca`](https://github.com/nodejs/node/commit/8ff6bc45ca)] - **lib,permission**: handle buffer on fs.symlink (Rafael Gonzaga) [#51212](https://github.com/nodejs/node/pull/51212)
- \[[`416b4f8063`](https://github.com/nodejs/node/commit/416b4f8063)] - **(SEMVER-MINOR)** **lib,src,permission**: port path.resolve to C++ (Rafael Gonzaga) [#50758](https://github.com/nodejs/node/pull/50758)
- \[[`6648a5c576`](https://github.com/nodejs/node/commit/6648a5c576)] - **meta**: notify tsc on changes in SECURITY.md (Rafael Gonzaga) [#51259](https://github.com/nodejs/node/pull/51259)
- \[[`83a99ccedd`](https://github.com/nodejs/node/commit/83a99ccedd)] - **meta**: update artifact actions to v4 (Michaël Zasso) [#51219](https://github.com/nodejs/node/pull/51219)
- \[[`b621ada69a`](https://github.com/nodejs/node/commit/b621ada69a)] - **module**: move the CJS exports cache to internal/modules/cjs/loader (Joyee Cheung) [#51157](https://github.com/nodejs/node/pull/51157)
- \[[`e4be5b60f0`](https://github.com/nodejs/node/commit/e4be5b60f0)] - **(SEMVER-MINOR)** **net**: add connection attempt events (Paolo Insogna) [#51045](https://github.com/nodejs/node/pull/51045)
- \[[`3a492056e2`](https://github.com/nodejs/node/commit/3a492056e2)] - **node-api**: type tag external values without v8::Private (Chengzhong Wu) [#51149](https://github.com/nodejs/node/pull/51149)
- \[[`b2135ae7dc`](https://github.com/nodejs/node/commit/b2135ae7dc)] - **node-api**: segregate nogc APIs from rest via type system (Gabriel Schulhof) [#50060](https://github.com/nodejs/node/pull/50060)
- \[[`8f4325dcd5`](https://github.com/nodejs/node/commit/8f4325dcd5)] - **permission**: fix wildcard when children > 1 (Rafael Gonzaga) [#51209](https://github.com/nodejs/node/pull/51209)
- \[[`7ecf99404e`](https://github.com/nodejs/node/commit/7ecf99404e)] - **quic**: update quic impl to use latest ngtcp2/nghttp3 (James M Snell) [#51291](https://github.com/nodejs/node/pull/51291)
- \[[`5b32e21f3b`](https://github.com/nodejs/node/commit/5b32e21f3b)] - **quic**: add quic internalBinding, refine Endpoint, add types (James M Snell) [#51112](https://github.com/nodejs/node/pull/51112)
- \[[`3310095bea`](https://github.com/nodejs/node/commit/3310095bea)] - **repl**: fix prepareStackTrace frames array order (Chengzhong Wu) [#50827](https://github.com/nodejs/node/pull/50827)
- \[[`a0ff00b526`](https://github.com/nodejs/node/commit/a0ff00b526)] - **src**: avoid draining platform tasks at FreeEnvironment (Chengzhong Wu) [#51290](https://github.com/nodejs/node/pull/51290)
- \[[`115e0585cd`](https://github.com/nodejs/node/commit/115e0585cd)] - **src**: add fast api for Histogram (James M Snell) [#51296](https://github.com/nodejs/node/pull/51296)
- \[[`29b81576c6`](https://github.com/nodejs/node/commit/29b81576c6)] - **src**: refactor `GetCreationContext` calls (Yagiz Nizipli) [#51287](https://github.com/nodejs/node/pull/51287)
- \[[`54dd978400`](https://github.com/nodejs/node/commit/54dd978400)] - **src**: enter isolate before destructing IsolateData (Ben Noordhuis) [#51138](https://github.com/nodejs/node/pull/51138)
- \[[`864ecb0dfa`](https://github.com/nodejs/node/commit/864ecb0dfa)] - **src**: do not treat all paths ending with node_modules as such (Michaël Zasso) [#51269](https://github.com/nodejs/node/pull/51269)
- \[[`df31c8114c`](https://github.com/nodejs/node/commit/df31c8114c)] - **src**: eliminate duplicate code in histogram.cc (James M Snell) [#51263](https://github.com/nodejs/node/pull/51263)
- \[[`17c73e6d0c`](https://github.com/nodejs/node/commit/17c73e6d0c)] - **src**: fix unix abstract socket path for trace event (theanarkh) [#50858](https://github.com/nodejs/node/pull/50858)
- \[[`96d64edc94`](https://github.com/nodejs/node/commit/96d64edc94)] - **src**: use BignumPointer and use BN_clear_free (James M Snell) [#50454](https://github.com/nodejs/node/pull/50454)
- \[[`8a2dd93a14`](https://github.com/nodejs/node/commit/8a2dd93a14)] - **src**: implement FastByteLengthUtf8 with simdutf::utf8_length_from_latin1 (Daniel Lemire) [#50840](https://github.com/nodejs/node/pull/50840)
- \[[`e54ddf898f`](https://github.com/nodejs/node/commit/e54ddf898f)] - **(SEMVER-MINOR)** **src**: support configurable snapshot (Joyee Cheung) [#50453](https://github.com/nodejs/node/pull/50453)
- \[[`a69c7d7bc3`](https://github.com/nodejs/node/commit/a69c7d7bc3)] - **(SEMVER-MINOR)** **src,permission**: add --allow-addon flag (Rafael Gonzaga) [#51183](https://github.com/nodejs/node/pull/51183)
- \[[`e7925e66fc`](https://github.com/nodejs/node/commit/e7925e66fc)] - **src,stream**: improve WriteString (ywave620) [#51155](https://github.com/nodejs/node/pull/51155)
- \[[`82de6603af`](https://github.com/nodejs/node/commit/82de6603af)] - **stream**: fix code style (Mattias Buelens) [#51168](https://github.com/nodejs/node/pull/51168)
- \[[`e443953656`](https://github.com/nodejs/node/commit/e443953656)] - **stream**: fix cloned webstreams not being unref'd (James M Snell) [#51255](https://github.com/nodejs/node/pull/51255)
- \[[`757a84c9ea`](https://github.com/nodejs/node/commit/757a84c9ea)] - **test**: fix flaky conditions for ppc64 SEA tests (Richard Lau) [#51422](https://github.com/nodejs/node/pull/51422)
- \[[`85ee2f7255`](https://github.com/nodejs/node/commit/85ee2f7255)] - **test**: replace forEach() with for...of (Alexander Jones) [#50608](https://github.com/nodejs/node/pull/50608)
- \[[`549e4b4142`](https://github.com/nodejs/node/commit/549e4b4142)] - **test**: replace forEach with for...of (Ospite Privilegiato) [#50787](https://github.com/nodejs/node/pull/50787)
- \[[`ef44f9bef2`](https://github.com/nodejs/node/commit/ef44f9bef2)] - **test**: replace foreach with for of (lucacapocci94-dev) [#50790](https://github.com/nodejs/node/pull/50790)
- \[[`652af45485`](https://github.com/nodejs/node/commit/652af45485)] - **test**: replace forEach() with for...of (Jia) [#50610](https://github.com/nodejs/node/pull/50610)
- \[[`684dd9db2f`](https://github.com/nodejs/node/commit/684dd9db2f)] - **test**: fix inconsistency write size in `test-fs-readfile-tostring-fail` (Jungku Lee) [#51141](https://github.com/nodejs/node/pull/51141)
- \[[`aaf710f535`](https://github.com/nodejs/node/commit/aaf710f535)] - **test**: replace forEach test-http-server-multiheaders2 (Marco Mac) [#50794](https://github.com/nodejs/node/pull/50794)
- \[[`57c64550cc`](https://github.com/nodejs/node/commit/57c64550cc)] - **test**: replace forEach with for-of in test-webcrypto-export-import-ec (Chiara Ricciardi) [#51249](https://github.com/nodejs/node/pull/51249)
- \[[`88e865181b`](https://github.com/nodejs/node/commit/88e865181b)] - **test**: move to for of loop in test-http-hostname-typechecking.js (Luca Del Puppo) [#50782](https://github.com/nodejs/node/pull/50782)
- \[[`3db376f67a`](https://github.com/nodejs/node/commit/3db376f67a)] - **test**: skip test-watch-mode-inspect on arm (Michael Dawson) [#51210](https://github.com/nodejs/node/pull/51210)
- \[[`38232d1c52`](https://github.com/nodejs/node/commit/38232d1c52)] - **test**: replace forEach with for of in file test-trace-events-net.js (Ianna83) [#50789](https://github.com/nodejs/node/pull/50789)
- \[[`f1cb58355a`](https://github.com/nodejs/node/commit/f1cb58355a)] - **test**: replace forEach() with for...of in test/parallel/test-util-log.js (Edoardo Dusi) [#50783](https://github.com/nodejs/node/pull/50783)
- \[[`9bfd84c117`](https://github.com/nodejs/node/commit/9bfd84c117)] - **test**: replace forEach with for of in test-trace-events-api.js (Andrea Pavone) [#50784](https://github.com/nodejs/node/pull/50784)
- \[[`7e9834915a`](https://github.com/nodejs/node/commit/7e9834915a)] - **test**: replace forEach with for-of in test-v8-serders.js (Mattia Iannone) [#50791](https://github.com/nodejs/node/pull/50791)
- \[[`b6f232e841`](https://github.com/nodejs/node/commit/b6f232e841)] - **test**: add URL tests to fs-read in pm (Rafael Gonzaga) [#51213](https://github.com/nodejs/node/pull/51213)
- \[[`8a2178c5f5`](https://github.com/nodejs/node/commit/8a2178c5f5)] - **test**: use tmpdir.refresh() in test-esm-loader-resolve-type.mjs (Luigi Pinca) [#51206](https://github.com/nodejs/node/pull/51206)
- \[[`7e9a0b192a`](https://github.com/nodejs/node/commit/7e9a0b192a)] - **test**: use tmpdir.refresh() in test-esm-json.mjs (Luigi Pinca) [#51205](https://github.com/nodejs/node/pull/51205)
- \[[`d7c2572fe0`](https://github.com/nodejs/node/commit/d7c2572fe0)] - **test**: fix flakiness in worker\*.test-free-called (Jithil P Ponnan) [#51013](https://github.com/nodejs/node/pull/51013)
- \[[`979cebc955`](https://github.com/nodejs/node/commit/979cebc955)] - **test_runner**: fixed test object is incorrectly passed to setup() (Pulkit Gupta) [#50982](https://github.com/nodejs/node/pull/50982)
- \[[`63db82abe6`](https://github.com/nodejs/node/commit/63db82abe6)] - **test_runner**: fixed to run after hook if before throws an error (Pulkit Gupta) [#51062](https://github.com/nodejs/node/pull/51062)
- \[[`c31ed51373`](https://github.com/nodejs/node/commit/c31ed51373)] - **(SEMVER-MINOR)** **timers**: export timers.promises (Marco Ippolito) [#51246](https://github.com/nodejs/node/pull/51246)
- \[[`fc10f889eb`](https://github.com/nodejs/node/commit/fc10f889eb)] - **tools**: update lint-md-dependencies to rollup\@4.9.2 (Node.js GitHub Bot) [#51320](https://github.com/nodejs/node/pull/51320)
- \[[`d5a5f12d15`](https://github.com/nodejs/node/commit/d5a5f12d15)] - **tools**: fix dep_updaters dir updates (Michaël Zasso) [#51294](https://github.com/nodejs/node/pull/51294)
- \[[`bdcb5ed510`](https://github.com/nodejs/node/commit/bdcb5ed510)] - **tools**: update inspector_protocol to c488ba2 (cola119) [#51293](https://github.com/nodejs/node/pull/51293)
- \[[`69a46add77`](https://github.com/nodejs/node/commit/69a46add77)] - **tools**: update inspector_protocol to 9b4a4aa (cola119) [#51293](https://github.com/nodejs/node/pull/51293)
- \[[`e325f49d19`](https://github.com/nodejs/node/commit/e325f49d19)] - **tools**: update inspector_protocol to 2f51e05 (cola119) [#51293](https://github.com/nodejs/node/pull/51293)
- \[[`60d804851b`](https://github.com/nodejs/node/commit/60d804851b)] - **tools**: update inspector_protocol to d7b099b (cola119) [#51293](https://github.com/nodejs/node/pull/51293)
- \[[`d18168489f`](https://github.com/nodejs/node/commit/d18168489f)] - **tools**: update inspector_protocol to 912eb68 (cola119) [#51293](https://github.com/nodejs/node/pull/51293)
- \[[`ef4f46fc39`](https://github.com/nodejs/node/commit/ef4f46fc39)] - **tools**: update inspector_protocol to 547c5b8 (cola119) [#51293](https://github.com/nodejs/node/pull/51293)
- \[[`c3126fc016`](https://github.com/nodejs/node/commit/c3126fc016)] - **tools**: update inspector_protocol to ca525fc (cola119) [#51293](https://github.com/nodejs/node/pull/51293)
- \[[`917d887dde`](https://github.com/nodejs/node/commit/917d887dde)] - **tools**: update lint-md-dependencies to rollup\@4.9.1 (Node.js GitHub Bot) [#51276](https://github.com/nodejs/node/pull/51276)
- \[[`37594918e0`](https://github.com/nodejs/node/commit/37594918e0)] - **tools**: check timezone current version (Marco Ippolito) [#51178](https://github.com/nodejs/node/pull/51178)
- \[[`d0d2faf899`](https://github.com/nodejs/node/commit/d0d2faf899)] - **tools**: update lint-md-dependencies to rollup\@4.9.0 (Node.js GitHub Bot) [#51193](https://github.com/nodejs/node/pull/51193)
- \[[`c96ef6533c`](https://github.com/nodejs/node/commit/c96ef6533c)] - **tools**: update eslint to 8.56.0 (Node.js GitHub Bot) [#51194](https://github.com/nodejs/node/pull/51194)
- \[[`f4f781d493`](https://github.com/nodejs/node/commit/f4f781d493)] - **util**: pass invalidSubtypeIndex instead of trimmedSubtype to error (Gaurish Sethia) [#51264](https://github.com/nodejs/node/pull/51264)
- \[[`867b484429`](https://github.com/nodejs/node/commit/867b484429)] - **watch**: clarify that the fileName parameter can be null (Luigi Pinca) [#51305](https://github.com/nodejs/node/pull/51305)
- \[[`56e8969b65`](https://github.com/nodejs/node/commit/56e8969b65)] - **watch**: fix null `fileName` on windows systems (vnc5) [#49891](https://github.com/nodejs/node/pull/49891)
- \[[`3f4fd6efbb`](https://github.com/nodejs/node/commit/3f4fd6efbb)] - **watch**: fix infinite loop when passing --watch=true flag (Pulkit Gupta) [#51160](https://github.com/nodejs/node/pull/51160)

Windows 32-bit Installer: https://nodejs.org/dist/v21.6.0/node-v21.6.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v21.6.0/node-v21.6.0-x64.msi \
Windows ARM 64-bit Installer: https://nodejs.org/dist/v21.6.0/node-v21.6.0-arm64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v21.6.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v21.6.0/win-x64/node.exe \
Windows ARM 64-bit Binary: https://nodejs.org/dist/v21.6.0/win-arm64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v21.6.0/node-v21.6.0.pkg \
macOS Apple Silicon 64-bit Binary: https://nodejs.org/dist/v21.6.0/node-v21.6.0-darwin-arm64.tar.gz \
macOS Intel 64-bit Binary: https://nodejs.org/dist/v21.6.0/node-v21.6.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v21.6.0/node-v21.6.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v21.6.0/node-v21.6.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v21.6.0/node-v21.6.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v21.6.0/node-v21.6.0-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v21.6.0/node-v21.6.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v21.6.0/node-v21.6.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v21.6.0/node-v21.6.0.tar.gz \
Other release files: https://nodejs.org/dist/v21.6.0/ \
Documentation: https://nodejs.org/docs/v21.6.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

a70b758f571fad0df2a6cd22d2814bc2e55704aa5a97486088575ad75bc9339c  node-v21.6.0-aix-ppc64.tar.gz
0a3a5c0155d700790ad3e9bba291ff9d6e5b0236deb6548e77ec7f2347db2dac  node-v21.6.0-arm64.msi
425d80f28edf6ec2e4d53388d839cf45e0ec771e6276028559d433348ca75f7b  node-v21.6.0-darwin-arm64.tar.gz
90e5870ad047e38485e62bb90f7bc50f6e0cd0357ac3ce810f0257c2e5cecb07  node-v21.6.0-darwin-arm64.tar.xz
a2310fbfe0109d764ed8024f17c6fcdba5ef9327fb810c03bca9011eccae777b  node-v21.6.0-darwin-x64.tar.gz
277276ecad902a693282f00f440bc6a496c4392055114041fd9014b8fb9469a6  node-v21.6.0-darwin-x64.tar.xz
48e6a8c5db5987c1d5823e6602a41fc59f750cb6032589bc70cb16e150fe967a  node-v21.6.0-headers.tar.gz
a68b28f36c7cd9f63128cbdbaea05de8ad1d6f215f2c8d34cb1dd8163d1b1ff6  node-v21.6.0-headers.tar.xz
4c795134f243ac95db587b9d5de94bb066b96b3821604a806d3c1853ee44ec56  node-v21.6.0-linux-arm64.tar.gz
000d0ba34c5ea1b1213411a86adacbd8a7a898f1d3a58f3fcc93c3458952b910  node-v21.6.0-linux-arm64.tar.xz
2792ddcdc78250e62339f0c157e08fec5a4c32ad967b71536b0d813ee0770ce6  node-v21.6.0-linux-armv7l.tar.gz
90867ffd2e4a2e2afec8c40d6353f0f5a5366b5f76231e1e88607abc943ecac4  node-v21.6.0-linux-armv7l.tar.xz
251b191acbaef8aea0a79a7b5ff2a1ae401ad03b13e979aa495b2dc59375b65c  node-v21.6.0-linux-ppc64le.tar.gz
f14318f7dcd0d68a9390f1d4ec4e7b360aeb316fd32a7bd02e840c343a903ef2  node-v21.6.0-linux-ppc64le.tar.xz
c136bfbd49e07570ea95dbdcdf82e063f4577098839ec002894e9e49ad3abedf  node-v21.6.0-linux-s390x.tar.gz
b76424a4ab99daa12c3a8beb7935726c4ee04c4f78e79ef2cecdd816839cea51  node-v21.6.0-linux-s390x.tar.xz
d12a6fc04091aa246402b4cac67215cd2578f178300a361cfa9e28b2ca16d679  node-v21.6.0-linux-x64.tar.gz
d940589762748bdbfc1a39132d27a16455b9d283ac3d8a84c3415005269effe4  node-v21.6.0-linux-x64.tar.xz
6c2ec18037423f99c0315c9c1ac6d85f392abe7a60c7469fd02416c930036459  node-v21.6.0-win-arm64.7z
4244090df0aff16e4d173dede4b272dd7d67755e789fec30b5d972d2a4b81536  node-v21.6.0-win-arm64.zip
c60bf0351a0c8639f5f24033951b8abe2ddeb9ab83c265a13719d1b8576ffa8f  node-v21.6.0-win-x64.7z
e00bfadcb0f0636c1f824a6f19d8c984ab5fb53619999d34433bf1a82c16e245  node-v21.6.0-win-x64.zip
5bd52433b2903b5d5c9a8f885e99f3818975882744a9eeade1f2386a06e17751  node-v21.6.0-win-x86.7z
844e01e770f8a374736100548de96b45df922c2de8e48185bf8bf79bcc3bf1f7  node-v21.6.0-win-x86.zip
7d7a049c74550039ed771a737fe75a49664da5230356fc2865992777724bb90b  node-v21.6.0-x64.msi
5367694e14bf4897e8089ff1e25b1278e61df2ac11f8530ffea63c8b8111b283  node-v21.6.0-x86.msi
ef3962c561ec6ac8204b11fda9235482eb4c37cf55c49339cf35bf4d003d4efd  node-v21.6.0.pkg
35e342f1607398ec54447e44590abcb235b9f6254cc6a8deefba8350c23d89b5  node-v21.6.0.tar.gz
20265bfcfa73c8b46b32378641d38b009dfc980eb28192c3d5ab7f6986fdb1e3  node-v21.6.0.tar.xz
4a3b2c764e7cb06200ddfc54aa1e310d65037a9f254aa1a34ce5bfdb5f1f6746  win-arm64/node.exe
7bf3d6ec7d4f20a33ec4bc6140a196da9bae6ea7ade0bfc312bde82fcf1e341e  win-arm64/node.lib
f1c15acb25e718e1ce07501b2a49f9273bfdb5f681a7dade46447be7be492f6e  win-arm64/node_pdb.7z
c579ddbbcc6fba44865211daf99cd9eda857db8538bf4cbb777daadfd6c31c92  win-arm64/node_pdb.zip
c9d866b5b52accad28801401d1803eb8e970aa06006728f390013b10895d04d0  win-x64/node.exe
062c4519e957aac29ee4e03ca5ff80212d72cd5f075e5d6e58f98fdd01d8b244  win-x64/node.lib
14f63e1fe1e17f0022c16a41c1b910b149d5c61532c62f84f4245989baabedda  win-x64/node_pdb.7z
501d1211f0c5e3f474c2c1353b015ed01fa3544b3d01e3900deeedd352aa529c  win-x64/node_pdb.zip
898cfd28e258ae42ec60be621992909d5b9042b3ee9ec2ddf1160be0c6897dfe  win-x86/node.exe
4f88998a357e7746390f717a36891459ae236655f70d38ce65df4072934303e8  win-x86/node.lib
3d3735484ce3af855a499a014483dc8d22067cde01b07212d2ff9355307e8f7a  win-x86/node_pdb.7z
ba0d3fe711324791fedb08ac5c0360e78dda6b0df5aea2e68bfced539cb5b0ea  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQGzBAEBCAAdFiEEiQwI24V5Fi/uDfnbi+q0389VXvQFAmWlRq8ACgkQi+q0389V
XvSk1gv7BbzRt+XDRwcxAiQryNnibqia4Hjyj+TnFnkWm1s906FKU+v4R5qnwXix
VFr1ee6UXkf+RX6SNXfhb3u5sAb8R3NkHIKdx6Sr/UchZX3yzPy3aR9wF7DLS2Ol
68flnuQIrrRvP+9RwcKaFmOrdu0F/2psQyykhVkUh92+lNK4FTIQpNAVoGtPmnh1
amGkKJDwU0XhQ00FUOKqWCJKkPbCZLPr+YHTUpUGgFJx6r2sDQYqNam9qkFE7czR
tSS3PxCiUdyAwsijID8muy9WHAzu6FJTI7k9/XMXYi5XaIYYoAM5yOIG4FYGIe5V
ZGlGKjRzhQFspA6l2Z+LErafVsLHtnJHj8tOUl00gJVJ/p74QldsPUDMuu1dZmXP
MLx+5UQhU9KGxR+8raHl8pdXLgIM+nHRl2KRo58kBCOEPo4asM3mUy+TPF4iwacR
A8xfJd5K/3t2Wb/1a6HfkKcVfWoHF49ZmPHMzK2vOgoedYldpFk+G1eH4Kbv6TOw
75r+PSbE
=kWlT
-----END PGP SIGNATURE-----
```
