---
date: '2023-08-09T16:51:54.265Z'
category: release
title: Node v16.20.2 (LTS)
layout: blog-post
author: <PERSON>
---

### Notable Changes

The following CVEs are fixed in this release:

- [CVE-2023-32002](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2023-32002): Policies can be bypassed via Module.\_load (High)
- [CVE-2023-32006](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2023-32006): Policies can be bypassed by module.constructor.createRequire (Medium)
- [CVE-2023-32559](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2023-32559): Policies can be bypassed via process.binding (Medium)
- OpenSSL Security Releases
  - [OpenSSL security advisory 14th July](https://mta.openssl.org/pipermail/openssl-announce/2023-July/000264.html).
  - [OpenSSL security advisory 19th July](https://mta.openssl.org/pipermail/openssl-announce/2023-July/000265.html).
  - [OpenSSL security advisory 31st July](https://mta.openssl.org/pipermail/openssl-announce/2023-July/000267.html)

More detailed information on each of the vulnerabilities can be found in [August 2023 Security Releases](/blog/vulnerability/august-2023-security-releases/) blog post.

### Commits

- \[[`40c3958a5a`](https://github.com/nodejs/node/commit/40c3958a5a)] - **deps**: update archs files for OpenSSL-1.1.1v (RafaelGSS) [#49043](https://github.com/nodejs/node/pull/49043)
- \[[`a9ac9da89a`](https://github.com/nodejs/node/commit/a9ac9da89a)] - **deps**: fix openssl crypto clean (RafaelGSS) [#49043](https://github.com/nodejs/node/pull/49043)
- \[[`362d4c7494`](https://github.com/nodejs/node/commit/362d4c7494)] - **deps**: upgrade openssl sources to OpenSSL_1_1_1v (RafaelGSS) [#49043](https://github.com/nodejs/node/pull/49043)
- \[[`d8ccfe9ad4`](https://github.com/nodejs/node/commit/d8ccfe9ad4)] - **policy**: handle Module.constructor and main.extensions bypass (RafaelGSS) [nodejs-private/node-private#445](https://github.com/nodejs-private/node-private/pull/445)
- \[[`242aaa0caa`](https://github.com/nodejs/node/commit/242aaa0caa)] - **policy**: disable process.binding() when enabled (Tobias Nießen) [nodejs-private/node-private#459](https://github.com/nodejs-private/node-private/pull/459)

Windows 32-bit Installer: https://nodejs.org/dist/v16.20.2/node-v16.20.2-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v16.20.2/node-v16.20.2-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v16.20.2/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v16.20.2/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v16.20.2/node-v16.20.2.pkg \
macOS Apple Silicon 64-bit Binary: https://nodejs.org/dist/v16.20.2/node-v16.20.2-darwin-arm64.tar.gz \
macOS Intel 64-bit Binary: https://nodejs.org/dist/v16.20.2/node-v16.20.2-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v16.20.2/node-v16.20.2-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v16.20.2/node-v16.20.2-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v16.20.2/node-v16.20.2-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v16.20.2/node-v16.20.2-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v16.20.2/node-v16.20.2-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v16.20.2/node-v16.20.2-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v16.20.2/node-v16.20.2.tar.gz \
Other release files: https://nodejs.org/dist/v16.20.2/ \
Documentation: https://nodejs.org/docs/v16.20.2/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

e3866679e6fbb13f5156c328bb77623b4d63493c8aaa448686a2bc53007005b8  node-v16.20.2-aix-ppc64.tar.gz
6a5c4108475871362d742b988566f3fe307f6a67ce14634eb3fbceb4f9eea88c  node-v16.20.2-darwin-arm64.tar.gz
fb87e01f0b2c8545afb8dd0769f7eb2439fb4fc8731efa956744fb0e0bc98105  node-v16.20.2-darwin-arm64.tar.xz
d7a46eaf2b57ffddeda16ece0d887feb2e31a91ad33f8774da553da0249dc4a6  node-v16.20.2-darwin-x64.tar.gz
107ae8d56a9c0aa85c8952231ac44d5e6df7c1ea3e9a36e2ef022ae36c98ccec  node-v16.20.2-darwin-x64.tar.xz
d6feed11c7f02601de746da81138f2d74fafbedff0b3ea9076974ca81ca62894  node-v16.20.2-headers.tar.gz
f46e93d3bb7981c7a19bbf3b7d377e0965d4a3fab3ba69cf77616e9da245b421  node-v16.20.2-headers.tar.xz
b6945fcc9ad220386bb814bfae7137189fd17297f2959a744105e1bee006035a  node-v16.20.2-linux-arm64.tar.gz
e88d86154d1ce53dc52fd74d79d4bfdf0b05f58c0bb2639adfa36e9378b770c4  node-v16.20.2-linux-arm64.tar.xz
88ea2ddef7db491d2e93c150d27fbec422a4d06d7a63bf34d46e6d20d30eed43  node-v16.20.2-linux-armv7l.tar.gz
5f2a2a34d2f19931b8ef39416bde96933e6666f91a2d1a2b92af30627a8e8429  node-v16.20.2-linux-armv7l.tar.xz
731d9ecc82ff59449566bf73959b16cc368b1060212b51d03edf3ec5e9937ced  node-v16.20.2-linux-ppc64le.tar.gz
17a85d996bd4810b45f66f121f923e707399def25c7a87c4be9eec661d368b59  node-v16.20.2-linux-ppc64le.tar.xz
49c7aa3e26a5569f5b399dfd6e86566fddefc9a62c40cc1d7a42daa7f4268b06  node-v16.20.2-linux-s390x.tar.gz
dcccafa1559e2096b13ca0fb7c4df76512121b9f2414c7585c136d2b3c815a74  node-v16.20.2-linux-s390x.tar.xz
c9193e6c414891694759febe846f4f023bf48410a6924a8b1520c46565859665  node-v16.20.2-linux-x64.tar.gz
874463523f26ed528634580247f403d200ba17a31adf2de98a7b124c6eb33d87  node-v16.20.2-linux-x64.tar.xz
697ee21f4693625cc9d060d25f2c29d12dffcb38d49e28dd446c6cb84eacfdf1  node-v16.20.2.pkg
33188eb11b977113adb65b2e09d71bddd63f12168ba73ceadae6c27938dc9e93  node-v16.20.2.tar.gz
576f1a03c455e491a8d132b587eb6b3b84651fc8974bb3638433dd44d22c8f49  node-v16.20.2.tar.xz
ebb0c50b1e4943cfeb005e71f5b9c6733f0a9586df3138df199d855d6abac77c  node-v16.20.2-win-x64.7z
f8bb35f6c08dc7bf14ac753509c06ed1a7ebf5b390cd3fbdc8f8c1aedd020ec3  node-v16.20.2-win-x64.zip
b0b2ef51565a01e34e4743ab5b26d76d34bb5403479c8b591f5ae434e0ab4adc  node-v16.20.2-win-x86.7z
c9c0b774328374973d5af5d72c4c6ce3932a1988c7efd32d84c35ba4771df41a  node-v16.20.2-win-x86.zip
8f78f8cea29743f9bfd4544b9c01106929e62e5211f019dd1f0a4b02916683d2  node-v16.20.2-x64.msi
5cdabf8c2d117da96f5238930df6e06d18d30f2b21c1dd91f84fb1098422b74a  node-v16.20.2-x86.msi
9f498591fd05b3aca332ce4ad966e77cd6e5c150751079ce8bad8ee499945b79  win-x64/node.exe
e2c00776781232ec8d756a00d086da89f527775a02267f6f604a4320893463c7  win-x64/node.lib
c61115bda7099df52d5fc44ad0cfb71c3e23813e4b4379adc72cdf5b467cdd72  win-x64/node_pdb.7z
ac79e3a3bf490b0e32cdefa201abad0633a737c4f1741de4469631cd4335e177  win-x64/node_pdb.zip
b6334209a452cc547fb9cadc28d26ef9e48f0015fcd8514724a6938aff3e0460  win-x86/node.exe
5890587b08eacbdab20239bf8ff350392794d9beb193096e941596b06ceeafbf  win-x86/node.lib
f3f0cdbbf416b3a1606f6670ecec16b33c763acd91ed1da841355a92bc554b15  win-x86/node_pdb.7z
7f6371282bea7b08863236cfea69c5f3ffaf6be6309e4872e5510d5fcd062693  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQGzBAEBCAAdFiEEiQwI24V5Fi/uDfnbi+q0389VXvQFAmTTwWoACgkQi+q0389V
XvT1iQwAl3P2WHhXJEERM0WAg+cvKDM7DR9Q83dXpvhIWxDjOlvSrvumQ0t3PNXl
qAgTUfJGy1qJ1TciSEEscD0ooyJ/nbheSgK60t7zsMrmzqvUQR3djOAydFTPwfh4
TIT2xxvaZ6mN9Wb2/UyxW80BqOxxaGMtqTTQrz95KuG02vtPVp4pnzbx/9UWj8gd
k27igpG2Ecfds+aS/gxX6rjUQb4Y4v0xY55oE9sDV9cFHg6n5nOmWjXD+cRohcX/
1sscjdd1/NJ4rVXsmc5jNIpX9eeRUBrBMZcIp77aAKMXPBEUK6N7oEOJ+M/6Sce3
cNstHPSB+L1xlraB8PtlqBbphrviHP3zLN8Z2CUIqUIR+n9PIXcgSWe6ft9X4JB1
EJS86K2A9oDAPnqtObNzjkUt0A8TYg1tuAXlIvHkrw5fby/srqauyN+iBJpgcT/d
FsSn7DKUOXYjapLf0qpYsMqxbhr7CsXjD71pFboXqJNHQDqlJZBGVyD4ppUwIqQL
GK5kUbB0
=Ju7G
-----END PGP SIGNATURE-----

```
