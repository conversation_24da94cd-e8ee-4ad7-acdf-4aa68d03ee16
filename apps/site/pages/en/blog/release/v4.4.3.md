---
date: '2016-04-12T22:08:19.877Z'
category: release
title: Node v4.4.3 (LTS)
layout: blog-post
author: <PERSON><PERSON>
---

### Notable Changes

- **deps**:
  - Fix `--gdbjit` for embedders. Backported from v8 upstream. (<PERSON>) [#5577](https://github.com/nodejs/node/pull/5577)
- **etw**:
  - Correctly display descriptors for ETW events 9 and 23 on the windows platform. (<PERSON>) [#5742](https://github.com/nodejs/node/pull/5742)
- **querystring**:
  - <PERSON><PERSON> throw when attempting to stringify bad surrogate pair. (<PERSON>) [#5858](https://github.com/nodejs/node/pull/5858)

### Commits

- [[`f949c273cd`](https://github.com/nodejs/node/commit/f949c273cd)] - **assert**: Check typed array view type in deepEqual (<PERSON>) [#5910](https://github.com/nodejs/node/pull/5910)
- [[`132acea0d4`](https://github.com/nodejs/node/commit/132acea0d4)] - **build**: introduce ci targets for lint/benchmark (<PERSON>st<PERSON>) [#5921](https://github.com/nodejs/node/pull/5921)
- [[`9a8f922dee`](https://github.com/nodejs/node/commit/9a8f922dee)] - **build**: add missing `openssl_fips%` to common.gypi (Fedor Indutny) [#5919](https://github.com/nodejs/node/pull/5919)
- [[`d275cdf202`](https://github.com/nodejs/node/commit/d275cdf202)] - **child_process**: refactor self=this in socket_list (Benjamin Gruenbaum) [#5860](https://github.com/nodejs/node/pull/5860)
- [[`aadf356aa2`](https://github.com/nodejs/node/commit/aadf356aa2)] - **deps**: backport 8d00c2c from v8 upstream (Ben Noordhuis) [#5577](https://github.com/nodejs/node/pull/5577)
- [[`200f763c43`](https://github.com/nodejs/node/commit/200f763c43)] - **deps**: completely upgrade npm in LTS to 2.15.1 (Forrest L Norvell) [#5989](https://github.com/nodejs/node/pull/5989)
- [[`86e3903626`](https://github.com/nodejs/node/commit/86e3903626)] - **dns**: Use object without protoype for map (Benjamin Gruenbaum) [#5843](https://github.com/nodejs/node/pull/5843)
- [[`9a33f43f73`](https://github.com/nodejs/node/commit/9a33f43f73)] - **doc**: update openssl LICENSE using license-builder.sh (Steven R. Loomis) [#6065](https://github.com/nodejs/node/pull/6065)
- [[`9679e2dc70`](https://github.com/nodejs/node/commit/9679e2dc70)] - **doc**: clarify that \_\_dirname is module local (James M Snell) [#6018](https://github.com/nodejs/node/pull/6018)
- [[`86d2af58d6`](https://github.com/nodejs/node/commit/86d2af58d6)] - **doc**: simple doc typo fix (Brendon Pierson) [#6041](https://github.com/nodejs/node/pull/6041)
- [[`f16802f3ca`](https://github.com/nodejs/node/commit/f16802f3ca)] - **doc**: note about Android support (Rich Trott) [#6040](https://github.com/nodejs/node/pull/6040)
- [[`8c2befe176`](https://github.com/nodejs/node/commit/8c2befe176)] - **doc**: note assert.throws() pitfall (Rich Trott) [#6029](https://github.com/nodejs/node/pull/6029)
- [[`0870ac65f2`](https://github.com/nodejs/node/commit/0870ac65f2)] - **doc**: use HTTPS for links where possible (Rich Trott) [#6019](https://github.com/nodejs/node/pull/6019)
- [[`56755de96e`](https://github.com/nodejs/node/commit/56755de96e)] - **doc**: clarify stdout/stderr arguments to callback (James M Snell) [#6015](https://github.com/nodejs/node/pull/6015)
- [[`bb603b89a2`](https://github.com/nodejs/node/commit/bb603b89a2)] - **doc**: add 'Command Line Options' to 'View on single page' (firedfox) [#6011](https://github.com/nodejs/node/pull/6011)
- [[`c91f3d897a`](https://github.com/nodejs/node/commit/c91f3d897a)] - **doc**: add copy about how to curl SHA256.txt (Myles Borins) [#6120](https://github.com/nodejs/node/pull/6120)
- [[`f9cf232284`](https://github.com/nodejs/node/commit/f9cf232284)] - **doc**: add example using algorithms not directly exposed (Brad Hill) [#6108](https://github.com/nodejs/node/pull/6108)
- [[`f60ce1078d`](https://github.com/nodejs/node/commit/f60ce1078d)] - **doc**: document unspecified behavior for buf.write\* methods (James M Snell) [#5925](https://github.com/nodejs/node/pull/5925)
- [[`02401a6cbd`](https://github.com/nodejs/node/commit/02401a6cbd)] - **doc**: fix scrolling on iOS devices (Luigi Pinca) [#5878](https://github.com/nodejs/node/pull/5878)
- [[`aed22d0855`](https://github.com/nodejs/node/commit/aed22d0855)] - **doc**: path.format provide more examples (John Eversole) [#5838](https://github.com/nodejs/node/pull/5838)
- [[`6e2bfbe1fd`](https://github.com/nodejs/node/commit/6e2bfbe1fd)] - **doc**: fix doc for Buffer.readInt32LE() (ghaiklor) [#5890](https://github.com/nodejs/node/pull/5890)
- [[`940d204401`](https://github.com/nodejs/node/commit/940d204401)] - **doc**: consolidate timers docs in timers.markdown (Bryan English) [#5837](https://github.com/nodejs/node/pull/5837)
- [[`505faf6360`](https://github.com/nodejs/node/commit/505faf6360)] - **doc**: refine child_process detach behaviour (Robert Jefe Lindstaedt) [#5330](https://github.com/nodejs/node/pull/5330)
- [[`feedca7879`](https://github.com/nodejs/node/commit/feedca7879)] - **doc**: add topic - event loop, timers, `nextTick()` (Jeff Harris) [#4936](https://github.com/nodejs/node/pull/4936)
- [[`6d3822c12b`](https://github.com/nodejs/node/commit/6d3822c12b)] - **etw**: fix descriptors of events 9 and 23 (João Reis) [#5742](https://github.com/nodejs/node/pull/5742)
- [[`56dda6f336`](https://github.com/nodejs/node/commit/56dda6f336)] - **fs**: Remove unused branches (Benjamin Gruenbaum) [#5289](https://github.com/nodejs/node/pull/5289)
- [[`dfe9e157c1`](https://github.com/nodejs/node/commit/dfe9e157c1)] - **governance**: remove target size for CTC (Rich Trott) [#5879](https://github.com/nodejs/node/pull/5879)
- [[`c4103b154f`](https://github.com/nodejs/node/commit/c4103b154f)] - **lib**: refactor code with startsWith/endsWith (Jackson Tian) [#5753](https://github.com/nodejs/node/pull/5753)
- [[`16216a81de`](https://github.com/nodejs/node/commit/16216a81de)] - **meta**: add "joining a wg" section to WORKING_GROUPS.md (Matteo Collina) [#5488](https://github.com/nodejs/node/pull/5488)
- [[`65fc4e36ce`](https://github.com/nodejs/node/commit/65fc4e36ce)] - **querystring**: don't stringify bad surrogate pair (Brian White) [#5858](https://github.com/nodejs/node/pull/5858)
- [[`4f683ab912`](https://github.com/nodejs/node/commit/4f683ab912)] - **src,tools**: use template literals (Rich Trott) [#5778](https://github.com/nodejs/node/pull/5778)
- [[`ac40a4510d`](https://github.com/nodejs/node/commit/ac40a4510d)] - **test**: explicitly set global in test-repl (Rich Trott) [#6026](https://github.com/nodejs/node/pull/6026)
- [[`a7b3a7533a`](https://github.com/nodejs/node/commit/a7b3a7533a)] - **test**: be explicit about polluting of `global` (Rich Trott) [#6017](https://github.com/nodejs/node/pull/6017)
- [[`73e3b7b9a8`](https://github.com/nodejs/node/commit/73e3b7b9a8)] - **test**: make use of globals explicit (Rich Trott) [#6014](https://github.com/nodejs/node/pull/6014)
- [[`e7877e61b6`](https://github.com/nodejs/node/commit/e7877e61b6)] - **test**: fix flaky test-net-socket-timeout-unref (Rich Trott) [#6003](https://github.com/nodejs/node/pull/6003)
- [[`a39051f5b3`](https://github.com/nodejs/node/commit/a39051f5b3)] - **test**: make arch available in status files (Santiago Gimeno) [#5997](https://github.com/nodejs/node/pull/5997)
- [[`ccf90b651a`](https://github.com/nodejs/node/commit/ccf90b651a)] - **test**: fix test-dns.js flakiness (Rich Trott) [#5996](https://github.com/nodejs/node/pull/5996)
- [[`1994ac0912`](https://github.com/nodejs/node/commit/1994ac0912)] - **test**: add test for piping large input from stdin (Anna Henningsen) [#5949](https://github.com/nodejs/node/pull/5949)
- [[`cc1aab9f6a`](https://github.com/nodejs/node/commit/cc1aab9f6a)] - **test**: mitigate flaky test-https-agent (Rich Trott) [#5939](https://github.com/nodejs/node/pull/5939)
- [[`10fe79b809`](https://github.com/nodejs/node/commit/10fe79b809)] - **test**: fix offending max-len linter error (Sakthipriyan Vairamani) [#5980](https://github.com/nodejs/node/pull/5980)
- [[`63d82960fd`](https://github.com/nodejs/node/commit/63d82960fd)] - **test**: stdin is not always a net.Socket (Jeremiah Senkpiel) [#5935](https://github.com/nodejs/node/pull/5935)
- [[`fe0233b923`](https://github.com/nodejs/node/commit/fe0233b923)] - **test**: add known_issues test for GH-2148 (Rich Trott) [#5920](https://github.com/nodejs/node/pull/5920)
- [[`d59be4d248`](https://github.com/nodejs/node/commit/d59be4d248)] - **test**: ensure \_handle property existence (Rich Trott) [#5916](https://github.com/nodejs/node/pull/5916)
- [[`9702153107`](https://github.com/nodejs/node/commit/9702153107)] - **test**: fix flaky test-repl (Brian White) [#5914](https://github.com/nodejs/node/pull/5914)
- [[`a0a2e69097`](https://github.com/nodejs/node/commit/a0a2e69097)] - **test**: move dns test to test/internet (Ben Noordhuis) [#5905](https://github.com/nodejs/node/pull/5905)
- [[`8462d8f465`](https://github.com/nodejs/node/commit/8462d8f465)] - **test**: fix flaky test-net-socket-timeout (Brian White) [#5902](https://github.com/nodejs/node/pull/5902)
- [[`e0b283af73`](https://github.com/nodejs/node/commit/e0b283af73)] - **test**: fix flaky test-http-set-timeout (Rich Trott) [#5856](https://github.com/nodejs/node/pull/5856)
- [[`5853fec36f`](https://github.com/nodejs/node/commit/5853fec36f)] - **test**: fix test-debugger-client.js (Rich Trott) [#5851](https://github.com/nodejs/node/pull/5851)
- [[`ea83c382f9`](https://github.com/nodejs/node/commit/ea83c382f9)] - **test**: ensure win32.isAbsolute() is consistent (Brian White) [#6043](https://github.com/nodejs/node/pull/6043)
- [[`c33a23fd1e`](https://github.com/nodejs/node/commit/c33a23fd1e)] - **tools**: fix json doc generation (firedfox) [#5943](https://github.com/nodejs/node/pull/5943)
- [[`6f0bd64122`](https://github.com/nodejs/node/commit/6f0bd64122)] - **tools,doc**: fix incomplete json produced by doctool (firedfox) [#5966](https://github.com/nodejs/node/pull/5966)
- [[`f7eb48302c`](https://github.com/nodejs/node/commit/f7eb48302c)] - **win,build**: build and test add-ons on test-ci (Bogdan Lobor) [#5886](https://github.com/nodejs/node/pull/5886)

Windows 32-bit Installer: https://nodejs.org/dist/v4.4.3/node-v4.4.3-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v4.4.3/node-v4.4.3-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v4.4.3/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v4.4.3/win-x64/node.exe \
Mac OS X 64-bit Installer: https://nodejs.org/dist/v4.4.3/node-v4.4.3.pkg \
Mac OS X 64-bit Binary: https://nodejs.org/dist/v4.4.3/node-v4.4.3-darwin-x64.tar.gz \
Linux 32-bit Binary: https://nodejs.org/dist/v4.4.3/node-v4.4.3-linux-x86.tar.xz \
Linux 64-bit Binary: https://nodejs.org/dist/v4.4.3/node-v4.4.3-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v4.4.3/node-v4.4.3-linux-ppc64le.tar.xz \
SmartOS 32-bit Binary: https://nodejs.org/dist/v4.4.3/node-v4.4.3-sunos-x86.tar.xz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v4.4.3/node-v4.4.3-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v4.4.3/node-v4.4.3-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v4.4.3/node-v4.4.3-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v4.4.3/node-v4.4.3-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v4.4.3/node-v4.4.3.tar.gz \
Other release files: https://nodejs.org/dist/v4.4.3/ \
Documentation: https://nodejs.org/docs/v4.4.3/api/

Shasums (GPG signing hash: SHA512, file hash: SHA256):

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA1

7cbb9819926d90c9c9f31c3db4c83499a3d9518ad3c7d9915fa4c66d6d5875df  node-v4.4.3-darwin-x64.tar.gz
9aabc5221d664ff6030abbbd8c94c74c1579cfd91cc39f82508a1da4cff2c42d  node-v4.4.3-darwin-x64.tar.xz
e24fb3eb7c7fe4c5b1ac64a93b63cfc109d56b59bedf2e88a2001f7ce3b1673c  node-v4.4.3-headers.tar.gz
286a884fe4ff5601afcd1352257b6133cf04304859e126cac533c9f1662af38c  node-v4.4.3-headers.tar.xz
261646b9d606ab3cc42f870d4bcaab79a40f18f7b13740762127598ef29d4ffc  node-v4.4.3-linux-arm64.tar.gz
fe65d3992219389cd965cee015571b222948ce46c676e5e0224450439fa26637  node-v4.4.3-linux-arm64.tar.xz
04d8abedf4199d1ca9b963d18458eb9e99f80cc5663e6f61041735dc28c9eb5f  node-v4.4.3-linux-armv6l.tar.gz
b570ba9a8dcb287fa833c0d0dd05f3ad008853a4a445805ef1e17e2ce7c01d03  node-v4.4.3-linux-armv6l.tar.xz
b1cd7144bea5564ae8ca0f653f1e1daf45c63e2a0d0e5c391519aaf7167dbd9e  node-v4.4.3-linux-armv7l.tar.gz
437716a5a9e751196eb25486fc486786a431fe3a632c61728949f4628ccbe83d  node-v4.4.3-linux-armv7l.tar.xz
dd9d9f8a25b1ba67c73af4724ca1abe4e0661d3ffac3d48e57ef3480c8f3cb2a  node-v4.4.3-linux-ppc64le.tar.gz
20af624f1ef5214f34ad5e7fab3cf11cb9bbadce4efeae727602eae03fb037bd  node-v4.4.3-linux-ppc64le.tar.xz
28ff2b23a837526ecfea66b0db42d43ec84368949998f2cb26dd742e8988ec1f  node-v4.4.3-linux-x64.tar.gz
7d8919fa7c6fa927c0fe295fcb16c7025c974b717350e97ddbd7a46d3b60fbef  node-v4.4.3-linux-x64.tar.xz
ffdbb81573d77a2bf14415fc1c0d65b110cfc2ba5a20dcdf8b62015519acd079  node-v4.4.3-linux-x86.tar.gz
93bcf48a9e5c6fdcd030ccfa0bde626466daaf0d8924a98f235e89f517addb76  node-v4.4.3-linux-x86.tar.xz
2e7cfa53c916524add24d7407572ede6d199a0a7b539ba6da67fd5a97c7ebab2  node-v4.4.3.pkg
72ceeafe74e911a418baa5c73cdb2888105c85d62f15245d55009f0e93da543a  node-v4.4.3-sunos-x64.tar.gz
24ab0b4363718b3062b5a1aeb6ce5c41d6444eb5315a8e97be7982cb3ee8049f  node-v4.4.3-sunos-x64.tar.xz
182963950c657aeecb18494c4d3765bcf51970aca938dd4848cf6942382fc730  node-v4.4.3-sunos-x86.tar.gz
4ad7229ca766aa80c412d69d1968a41172090698547829e3f34bcd3e9bd86fa6  node-v4.4.3-sunos-x86.tar.xz
8e67b95721aab7bd721179da2fe5dd97f9acc1306c15c9712ee103bcd6381638  node-v4.4.3.tar.gz
57499bb0b1b86080459d4066e3c138579a278b2d0b1f5b2f19e66c69b4e8433c  node-v4.4.3.tar.xz
388197c171805d97746d29b64d1e98c87dca378e70d5e947875327772bb2d560  node-v4.4.3-x64.msi
1ea07653ae47f71201c9ea0691fdfe4384416666ea70f725030f3b0140d729ca  node-v4.4.3-x86.msi
484b02a6559d6938ee90ac7f86e8b46b22025f3900c8677162af1f381c48c554  win-x64/node.exe
050a3d2743ea783ee63846872e5922f2afeeef0aa576994deafcf25565090b26  win-x64/node.lib
50ceedced779dd320da68e54ca403e9094c0f264cb3ba818cde1bf46ace415b3  win-x86/node.exe
0393695ea383baf27dee1feabd288b2162ed9354ebf5fe6e89ef6bc3f8333d04  win-x86/node.lib
-----BEGIN PGP SIGNATURE-----

iQEcBAEBAgAGBQJXDXGUAAoJEJM7AfQLXKlGIwUIALrSiVgRpJI8k93c3ZsAfiPq
h8Q2JSKlk5vvCu/e2GttxaSlwUY6GBCpOyrsD1zEny+9ni37eOKph/T64MVr4rwt
ZXNDOTuz+6/dAUUmPpnkVtJD9c0sWiDXM6PrLjAKnTuL2v8qqrtetKnUaVVtzMB8
DE2bRpWGII/ULOKIAl7Er+G2J5Il5m1sMUnTHxMjrWyauj3kclehmStPSmXgTDjZ
Lmm6BHIEQbm4qESJNsJJ+hhmdblcdMyfXH+vKxtT01XL9zTFzfXuDr8Awj+BHpfC
A5B5lcCTgXo5meJyFisu7MA6zqt9Y7pajr0ecv1JqffwiFURWJUAva2suJ/xMH8=
=OM4J
-----END PGP SIGNATURE-----

```
