---
date: '2019-10-09T20:36:31.174Z'
category: release
title: Node v8.16.2 (LTS)
layout: blog-post
author: <PERSON>
---

### Notable changes

- **deps**: upgrade openssl sources to 1.0.2s (<PERSON>) [#28230](https://github.com/nodejs/node/pull/28230)

### Commits

- [[`cc9d005628`](https://github.com/nodejs/node/commit/cc9d005628)] - **crypto**: update root certificates (<PERSON>) [#28808](https://github.com/nodejs/node/pull/28808)
- [[`347fcd35e3`](https://github.com/nodejs/node/commit/347fcd35e3)] - **crypto**: update root certificates (<PERSON>) [#27374](https://github.com/nodejs/node/pull/27374)
- [[`b2a6b3254d`](https://github.com/nodejs/node/commit/b2a6b3254d)] - **crypto**: update root certificates (<PERSON>) [#25113](https://github.com/nodejs/node/pull/25113)
- [[`5682e50325`](https://github.com/nodejs/node/commit/5682e50325)] - **deps**: add -no_rand_screen to openssl s_client (Shigeki Ohtsu) [nodejs/io.js#1836](https://github.com/nodejs/io.js/pull/1836)
- [[`9663ae3546`](https://github.com/nodejs/node/commit/9663ae3546)] - **deps**: fix asm build error of openssl in x86_win32 (Shigeki Ohtsu) [iojs/io.js#1389](https://github.com/iojs/io.js/pull/1389)
- [[`87eee99466`](https://github.com/nodejs/node/commit/87eee99466)] - **deps**: fix openssl assembly error on ia32 win32 (Fedor Indutny) [iojs/io.js#1389](https://github.com/iojs/io.js/pull/1389)
- [[`da99d3f972`](https://github.com/nodejs/node/commit/da99d3f972)] - **deps**: copy all openssl header files to include dir (Sam Roberts) [#28230](https://github.com/nodejs/node/pull/28230)
- [[`dc9d645ac4`](https://github.com/nodejs/node/commit/dc9d645ac4)] - **deps**: upgrade openssl sources to 1.0.2s (Sam Roberts) [#28230](https://github.com/nodejs/node/pull/28230)
- [[`37e24b19a0`](https://github.com/nodejs/node/commit/37e24b19a0)] - **deps**: V8: backport d520ebb (Michaël Zasso) [#27358](https://github.com/nodejs/node/pull/27358)
- [[`1a5dc6a3e7`](https://github.com/nodejs/node/commit/1a5dc6a3e7)] - **http**: check for existance in resetHeadersTimeoutOnReqEnd (Matteo Collina) [#26402](https://github.com/nodejs/node/pull/26402)
- [[`e45b6a3b98`](https://github.com/nodejs/node/commit/e45b6a3b98)] - **http2**: do not start reading after write if new write is on wire (Anna Henningsen) [#29399](https://github.com/nodejs/node/pull/29399)
- [[`559a8e342b`](https://github.com/nodejs/node/commit/559a8e342b)] - **http2**: do not crash on stream listener removal w/ destroyed session (Anna Henningsen) [#29459](https://github.com/nodejs/node/pull/29459)
- [[`dd285968c4`](https://github.com/nodejs/node/commit/dd285968c4)] - **openssl**: fix keypress requirement in apps on win32 (Shigeki Ohtsu) [iojs/io.js#1389](https://github.com/iojs/io.js/pull/1389)
- [[`3ee076f03d`](https://github.com/nodejs/node/commit/3ee076f03d)] - **stream**: ensure writable.destroy() emits error once (Luigi Pinca) [#26057](https://github.com/nodejs/node/pull/26057)
- [[`a7e5fe1f06`](https://github.com/nodejs/node/commit/a7e5fe1f06)] - **test**: unskip tests that now pass on AIX (Sam Roberts) [#29054](https://github.com/nodejs/node/pull/29054)
- [[`65e9b0f5a2`](https://github.com/nodejs/node/commit/65e9b0f5a2)] - **test**: specialize OOM check for AIX (Sam Roberts) [#28857](https://github.com/nodejs/node/pull/28857)
- [[`7aca9cb09b`](https://github.com/nodejs/node/commit/7aca9cb09b)] - **test**: fix pty test hangs on aix (Ben Noordhuis) [#28600](https://github.com/nodejs/node/pull/28600)
- [[`588b761fca`](https://github.com/nodejs/node/commit/588b761fca)] - **test**: skip stringbytes-external-exceed-max on AIX (Sam Roberts) [#28516](https://github.com/nodejs/node/pull/28516)
- [[`930647d0fe`](https://github.com/nodejs/node/commit/930647d0fe)] - **test**: skip tests related to CI failures on AIX (Sam Roberts) [#28469](https://github.com/nodejs/node/pull/28469)
- [[`92a2f8bbe3`](https://github.com/nodejs/node/commit/92a2f8bbe3)] - **test,win**: cleanup exec-timeout processes (João Reis) [#28723](https://github.com/nodejs/node/pull/28723)
- [[`d57f79726d`](https://github.com/nodejs/node/commit/d57f79726d)] - **tls**: partially backport pull request #26415 (Ben Noordhuis) [#26415](https://github.com/nodejs/node/pull/26415)
- [[`c582fef5cc`](https://github.com/nodejs/node/commit/c582fef5cc)] - **tools**: update certdata.txt (Sam Roberts) [#28808](https://github.com/nodejs/node/pull/28808)
- [[`4fbadf6a9e`](https://github.com/nodejs/node/commit/4fbadf6a9e)] - **tools**: update certdata.txt (Sam Roberts) [#27374](https://github.com/nodejs/node/pull/27374)
- [[`529b2ad25f`](https://github.com/nodejs/node/commit/529b2ad25f)] - **tools**: update certdata.txt (Sam Roberts) [#25113](https://github.com/nodejs/node/pull/25113)

Windows 32-bit Installer: https://nodejs.org/dist/v8.16.2/node-v8.16.2-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v8.16.2/node-v8.16.2-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v8.16.2/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v8.16.2/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v8.16.2/node-v8.16.2.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v8.16.2/node-v8.16.2-darwin-x64.tar.gz \
Linux 32-bit Binary: https://nodejs.org/dist/v8.16.2/node-v8.16.2-linux-x86.tar.xz \
Linux 64-bit Binary: https://nodejs.org/dist/v8.16.2/node-v8.16.2-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v8.16.2/node-v8.16.2-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v8.16.2/node-v8.16.2-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v8.16.2/node-v8.16.2-aix-ppc64.tar.gz \
SmartOS 32-bit Binary: https://nodejs.org/dist/v8.16.2/node-v8.16.2-sunos-x86.tar.xz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v8.16.2/node-v8.16.2-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v8.16.2/node-v8.16.2-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v8.16.2/node-v8.16.2-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v8.16.2/node-v8.16.2-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v8.16.2/node-v8.16.2.tar.gz \
Other release files: https://nodejs.org/dist/v8.16.2/ \
Documentation: https://nodejs.org/docs/v8.16.2/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

8ed8a96300c116eb9533beb70510882fb5a9bc4b1f9acfa31bb718d52213e4a3  node-v8.16.2-aix-ppc64.tar.gz
359331120e83f0707219398fc543b05eee9476446ecce549c20c4d9c7c103d29  node-v8.16.2-darwin-x64.tar.gz
5bbb5be5c388276b78d6c8a17cb1ce0dd2c7e157c08fe7f3eda7414c1f450b39  node-v8.16.2-darwin-x64.tar.xz
a43156bc0e2fd2f60a4e4a9194564d69b3e9f0f956a175b7ebe95d2610239f1c  node-v8.16.2-headers.tar.gz
ed608f070fcc84e80ef4caf9004fe955067d003a62ff76c2bf2c1c9c5bfb01b7  node-v8.16.2-headers.tar.xz
19b8c246dd12840ee6a94c89df683f853ed91cbbf6a133820fb163181d77202d  node-v8.16.2-linux-arm64.tar.gz
5afe366affb05136d25e99ec97a7a1ee1b690cc26df43567af0509f36b45e682  node-v8.16.2-linux-arm64.tar.xz
9f3b47244f502a150a09d6ad7f7eb68c835d148712b326d2af8db2b7ca81c14f  node-v8.16.2-linux-armv6l.tar.gz
0fb8e030651776c490e7a8612e97b0d11af27d6e89461a612fa7fab6afda6c95  node-v8.16.2-linux-armv6l.tar.xz
291f507d32b79fcd8c16739515516464f27b77cadb8b86f7a8ab43e74032cf69  node-v8.16.2-linux-armv7l.tar.gz
9b7070578d58785da2bd31d3256b42f91d993a2fb4091e87a21fb9e66cca3b6e  node-v8.16.2-linux-armv7l.tar.xz
9eb932ac222cbd6a1feeee145e515b2d41b18ad2ff686eb23782dc0e7205f838  node-v8.16.2-linux-ppc64le.tar.gz
f442590923136024b23bf8c40533f5d6a7b5fb23ae34a889f77f1539ed9bfe8c  node-v8.16.2-linux-ppc64le.tar.xz
0dd28c879c3bdfdb7e71b84cd74bf5667146a582b1af7afba25963772303aefb  node-v8.16.2-linux-s390x.tar.gz
7c40b92a0466ffcade181f750d7fa659fd335a10bf156179c0d4b5bc0b40f10e  node-v8.16.2-linux-s390x.tar.xz
722d07291a8886384388c6795a747ec2055073f83dc73c0a97efba0022cc23ff  node-v8.16.2-linux-x64.tar.gz
88617a293f5828cc94ee99c94a43fbea12b989e34fe643fc14885a14748a8da6  node-v8.16.2-linux-x64.tar.xz
938cc9651ce38091eba8c72ef2b235339fe3bed3980c04d1429f01412c68546c  node-v8.16.2-linux-x86.tar.gz
3c1be98f792e41ab2a26bf03ed061ec2f98977218662f91daa394f8a23b94127  node-v8.16.2-linux-x86.tar.xz
5721d8c76809cb138a19a4cf9897faf875cd385df6c115cd004412ef01807e08  node-v8.16.2.pkg
f9905b0e69884e7f8c95b8a924cb6c4e3a5ec4fd6390a6f6daa5110214b5f841  node-v8.16.2-sunos-x64.tar.gz
2eb39c7dfb21ed384924761e3315e754c427a1a29da0e49cb1e52d8469ab7ee3  node-v8.16.2-sunos-x64.tar.xz
73a156c47ec5e3c628e5960d15d798786073afb02a31621f0e7a4e92f0f484e1  node-v8.16.2-sunos-x86.tar.gz
5793c4c8f47e9da65bb17a749a357767c01640436bff0d8e5098c1804494a0af  node-v8.16.2-sunos-x86.tar.xz
4f94361bbe087bc3a9d48ff69c2cefabb6460c64cb7d959204726467b9ee84b8  node-v8.16.2.tar.gz
8c16b500ad74c1b1bde099996c287eeed5a4b2ab0efdf5d94d1d683cc2654ec3  node-v8.16.2.tar.xz
c27a170595851fc85b9b419c9fc76b6c70e237f7d60d483fa5ec3d8f8cea76c1  node-v8.16.2-win-x64.7z
98c615221500434155a8a5aff5fe96cd000400f3e76858ca97e6624f1d15eb73  node-v8.16.2-win-x64.zip
c4b7eb2839b19f8217717ac359c6b23725d9e25872338d6a56f4d9f74af11324  node-v8.16.2-win-x86.7z
6cadb66e46a7aa40f5401ddcbfd514f886d7e9b17e1c9d3ac89c594c338d64e5  node-v8.16.2-win-x86.zip
573f4780464942794d7496d7deef0a49c44f8b2c220dfe7383e288f9ada8662a  node-v8.16.2-x64.msi
a9e5c53ff8ce29d82fe0c78a8c5ed7fa9db21d11cc64f6961dd10a79fdb3521b  node-v8.16.2-x86.msi
e4e2c2aadcae1f6b5fe1936a4606b2fb59fbcfda263114e3abd10892d1433024  win-x64/node.exe
99959198ff808d2888dc9cdaf8080f05936452069e1542c0d3d4942834152d93  win-x64/node.lib
e52e4fced1bb038cbfc009d798e41f9c1a8b001040e59c42407e704c791ec509  win-x64/node_pdb.7z
37f34a8583edede6bc286652c6a9e6b6d4de2981ae41614b66c828b3e0dc86aa  win-x64/node_pdb.zip
521f0f095ad104bbe7a3446c357a91f59b4227d8f68d719cf7132525df00dcab  win-x86/node.exe
c63832cd5413211cde419c9ca95d1d79914853891d593ecac334d714cdc6f701  win-x86/node.lib
1a44e95ee27ecb933bb89daf9defaf4f418bdecae3416a71e7ba0ac55a565c0f  win-x86/node_pdb.7z
bd5a46e4a3705f74707b7d808ebed93d58414c6784e678293c97acb40c536a56  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQEzBAEBCAAdFiEETtd49TnjY0x3nIfG1wYoSKGrAFwFAl2eRHIACgkQ1wYoSKGr
AFz7YwgAlEA416hJN2HxEyff+wGgQCpn8RJXXQ/m2Dk6YqeBfzinC0fglv3AW5d2
RNVussQH0wywVRAzcdqC3bPmRy9Gyn1F/U77hKA26SSxVydHK+XUSeoF0R5P6RJm
pXLmzpEPPbVcGDkgkDDEIjE1Qnn9FnwuWMtd9reBv2l9WE2dIne3UQR4cLWxJ1u9
S9tbFKh5NtaeXJwHRlTL+QvM18GJvOoOM1i5J6VztbgLiQ4BeJdLzJu2b16OfGLQ
x3j7Ta2EwOF2J9dnqj22rVYsF5ZhrSAzOmHnWosAO0TWxtAoMRk7lbonm6EPaEgq
WWzjHwDLoSdyVb0BSJfVA4qsKzkadA==
=Rlrs
-----END PGP SIGNATURE-----

```
