---
date: '2018-04-24T19:29:37.119Z'
category: release
title: Node v10.0.0 (Current)
layout: blog-post
author: <PERSON>
---

Node.js 10.0.0 is the seventh major Node.js release since the launch of the
Node.js Foundation. In October of 2018, it will become the next [Active Long
Term Support](https://github.com/nodejs/release) branch.

Partially in celebration of the N-API native addon API graduating from
experimental status, this Node.js release also comes with a coordinated
experimental release of [Node-ChakraCore](https://nodejs.org/download/chakracore-release/v10.0.0/) that has full support for N-API and
advances the Time-Travel innovation with easier getting started using a
[VSCode extension](https://marketplace.visualstudio.com/items?itemName=ttd-trace-tools.node-chakracore-time-travel-debugger).

Feature Highlights for the Node-ChakraCore release include:

- Full support for N-API
- Easy getting started with Time-Travel Debugging via a new [Visual Studio Code Extension](https://marketplace.visualstudio.com/items?itemName=ttd-trace-tools.node-chakracore-time-travel-debugger)
- TTD support for generators and async functions
- Support for Inspector protocol
- Increased stability and other assorted improvements

The full set of changes for the Node.js 10.0.0 release are detailed below.

### Notable Changes

- Assert
  - Calling `assert.fail()` with more than one argument is deprecated. [[`70dcacd710`](https://github.com/nodejs/node/commit/70dcacd710)]
  - Calling `assert.ifError()` will now throw with any argument other than `undefined` or `null`. Previously the method would throw with any truthy value. [[`e65a6e81ef`](https://github.com/nodejs/node/commit/e65a6e81ef)]
  - The `assert.rejects()` and `assert.doesNotReject()` methods have been added for working with async functions. [[`599337f43e`](https://github.com/nodejs/node/commit/599337f43e)]
  - Assertion errors will show a diff in case objects are used. [[`2d9e87695e`](https://github.com/nodejs/node/commit/2d9e87695e)]
  - `assert.throws()` accepts an object for comparison to the error. [[`2d374916eb`](https://github.com/nodejs/node/commit/2d374916eb)]
  - The error message from `assert.ok(expression)` now also contains the expression itself. [[`f76ef50432`](https://github.com/nodejs/node/commit/f76ef50432)]
- Async_hooks
  - Older experimental async_hooks APIs have been removed. [[`1cc6b993b9`](https://github.com/nodejs/node/commit/1cc6b993b9)]
- Buffer
  - Uses of `new Buffer()` and `Buffer()` outside of the `node_modules` directory will now emit a runtime deprecation warning. [[`9d4ab90117`](https://github.com/nodejs/node/commit/9d4ab90117)]
  - `Buffer.isEncoding()` now returns `undefined` for falsy values, including an empty string. [[`452eed956e`](https://github.com/nodejs/node/commit/452eed956e)]
  - `Buffer.fill()` will throw if an attempt is made to fill with an empty `Buffer`. [[`1e802539b2`](https://github.com/nodejs/node/commit/1e802539b2)]
  - `noAssert` argument was removed from all `Buffer` read and write functions. [[`e8bb1f35df`](https://github.com/nodejs/node/commit/e8bb1f35df)]
- Child Process
  - Undefined properties of env are ignored. [[`38ee25e2e2`](https://github.com/nodejs/node/commit/38ee25e2e2)], [[`85739b6c5b`](https://github.com/nodejs/node/commit/85739b6c5b)]
- Console
  - The `console.table()` method has been added. [[`97ace04492`](https://github.com/nodejs/node/commit/97ace04492)]
- Crypto
  - The `crypto.createCipher()` and `crypto.createDecipher()` methods have been deprecated. Please use `crypto.createCipheriv()` and `crypto.createDecipheriv()` instead. [[`81f88e30dd`](https://github.com/nodejs/node/commit/81f88e30dd)]
  - The `decipher.finaltol()` method has been deprecated. [[`19f3927d92`](https://github.com/nodejs/node/commit/19f3927d92)]
  - The `crypto.DEFAULT_ENCODING` property has been deprecated. [[`6035beea93`](https://github.com/nodejs/node/commit/6035beea93)]
  - The `ECDH.convertKey()` method has been added. [[`f2e02883e7`](https://github.com/nodejs/node/commit/f2e02883e7)]
  - The `crypto.fips` property has been deprecated. [[`6e7992e8b8`](https://github.com/nodejs/node/commit/6e7992e8b8)]
  - The AES-CCM algorithm has been implemented. [[`1e07acd476`](https://github.com/nodejs/node/commit/1e07acd476)]
- Dependencies
  - V8 has been updated to 6.6. [[`9daebb48d6`](https://github.com/nodejs/node/commit/9daebb48d6)]
  - OpenSSL has been updated to 1.1.0h. [[`66cb29e646`](https://github.com/nodejs/node/commit/66cb29e646)]
- EventEmitter
  - The `EventEmitter.prototype.off()` method has been added as an alias for `EventEmitter.prototype.removeListener()`. [[`3bb6f07d52`](https://github.com/nodejs/node/commit/3bb6f07d52)]
- File System
  - The `fs/promises` API provides experimental promisified versions of the `fs` functions. [[`329fc78e49`](https://github.com/nodejs/node/commit/329fc78e49)]
  - Invalid path errors are now thrown synchronously. [[`d8f73385e2`](https://github.com/nodejs/node/commit/d8f73385e2)]
  - The `fs.readFile()` method now partitions reads to avoid thread pool exhaustion. [[`67a4ce1c6e`](https://github.com/nodejs/node/commit/67a4ce1c6e)]
- HTTP
  - Processing of HTTP Status codes `100`, `102-199` has been improved. [[`baf8495078`](https://github.com/nodejs/node/commit/baf8495078)]
  - Multi-byte characters in URL paths are now forbidden. [[`b961d9fd83`](https://github.com/nodejs/node/commit/b961d9fd83)]
- N-API
  - The n-api is no longer experimental. [[`cd7d7b15c1`](https://github.com/nodejs/node/commit/cd7d7b15c1)]
- Net
  - The `'close'` event will be emitted after `'end'`. [[`9b7a6914a7`](https://github.com/nodejs/node/commit/9b7a6914a7)]
- Perf_hooks
  - The `PerformanceObserver` class is now an `AsyncResource` and can be monitored using `async_hooks`. [[`009e41826f`](https://github.com/nodejs/node/commit/009e41826f)]
  - Trace events are now emitted for performance events. [[`9e509b622b`](https://github.com/nodejs/node/commit/9e509b622b)]
  - The `performance` API has been simplified. [[`2ec6995555`](https://github.com/nodejs/node/commit/2ec6995555)]
  - Performance milestone marks will be emitted as trace events. [[`96cb4fb795`](https://github.com/nodejs/node/commit/96cb4fb795)]
- Process
  - Using non-string values for `process.env` is deprecated. [[`5826fe4e79`](https://github.com/nodejs/node/commit/5826fe4e79)]
  - The `process.assert()` method is deprecated. [[`703e37cf3f`](https://github.com/nodejs/node/commit/703e37cf3f)]
- REPL
  - REPL now experimentally supports top-level await when using the `--experimental-repl-await` flag. [[`eeab7bc068`](https://github.com/nodejs/node/commit/eeab7bc068)]
  - The previously deprecated "magic mode" has been removed. [[`4893f70d12`](https://github.com/nodejs/node/commit/4893f70d12)]
  - The previously deprecated `NODE_REPL_HISTORY_FILE` environment variable has been removed. [[`60c9ad7979`](https://github.com/nodejs/node/commit/60c9ad7979)]
  - Proxy objects are shown as Proxy objects when inspected. [[`90a43906ab`](https://github.com/nodejs/node/commit/90a43906ab)]
- Streams
  - The `'readable'` event is now always deferred with nextTick. [[`1e0f3315c7`](https://github.com/nodejs/node/commit/1e0f3315c7)]
  - A new `pipeline()` method has been provided for building end-to-data stream pipelines. [[`a5cf3feaf1`](https://github.com/nodejs/node/commit/a5cf3feaf1)]
  - Experimental support for async for-await has been added to `stream.Readable`. [[`61b4d60c5d`](https://github.com/nodejs/node/commit/61b4d60c5d)]
- Timers
  - The `enroll()` and `unenroll()` methods have been deprecated. [[`68783ae0b8`](https://github.com/nodejs/node/commit/68783ae0b8)]
- TLS
  - The `tls.convertNPNProtocols()` method has been deprecated. [[`9204a0db6e`](https://github.com/nodejs/node/commit/9204a0db6e)]
  - Support for NPN (next protocol negotiation) has been dropped. [[`5bfbe5ceae`](https://github.com/nodejs/node/commit/5bfbe5ceae)]
  - The `ecdhCurve` default is now `'auto'`. [[`af78840b19`](https://github.com/nodejs/node/commit/af78840b19)]
- Trace Events
  - A new `trace_events` top-level module allows trace event categories to be enabled/disabled at runtime. [[`da5d818a54`](https://github.com/nodejs/node/commit/da5d818a54)]
- URL
  - The WHATWG URL API is now a global. [[`312414662b`](https://github.com/nodejs/node/commit/312414662b)]
- Util
  - `util.types.is[…]` type checks have been added. [[`b20af8088a`](https://github.com/nodejs/node/commit/b20af8088a)]
  - Support for bigint formatting has been added to `util.inspect()`. [[`39dc947409`](https://github.com/nodejs/node/commit/39dc947409)]
  - `util.inspect()` custom inspection with `inspect` property has been deprecated at runtime. [[`617e3e96e6`](https://github.com/nodejs/node/commit/617e3e96e6)]

#### Deprecations:

The following APIs have been deprecated in Node.js 10.0.0

- Passing more than one argument to `assert.fail()` will emit a runtime deprecation warning. [[`70dcacd710`](https://github.com/nodejs/node/commit/70dcacd710)]
- Previously deprecated legacy async_hooks APIs have reached end-of-life and have been removed. [[`1cc6b993b9`](https://github.com/nodejs/node/commit/1cc6b993b9)]
- Using `require()` to access several of Node.js' own internal dependencies will emit a runtime deprecation. [[`0e10717e43`](https://github.com/nodejs/node/commit/0e10717e43)]
- The `crypto.createCipher()` and `crypto.createDecipher()` methods have been deprecated in documentation.[[`81f88e30dd`](https://github.com/nodejs/node/commit/81f88e30dd)]
- Using the `Decipher.finaltol()` method will emit a runtime deprecation warning. [[`19f3927d92`](https://github.com/nodejs/node/commit/19f3927d92)]
- Using the `crypto.DEFAULT_ENCODING` property will emit a runtime deprecation warning. [[`6035beea93`](https://github.com/nodejs/node/commit/6035beea93)]
- Use by native addons of the `MakeCallback()` variant that passes a `Domain` will emit a runtime deprecation warning. [[`14bc3e22f3`](https://github.com/nodejs/node/commit/14bc3e22f3)], [[`efb32592e1`](https://github.com/nodejs/node/commit/efb32592e1)]
- Previously deprecated internal getters/setters on `net.Server` has reached end-of-life and have been removed. [[`3701b02309`](https://github.com/nodejs/node/commit/3701b02309)]
- Use of non-string values for `process.env` has been deprecated in documentation. [[`5826fe4e79`](https://github.com/nodejs/node/commit/5826fe4e79)]
- Use of `process.assert()` will emit a runtime deprecation warning. [[`703e37cf3f`](https://github.com/nodejs/node/commit/703e37cf3f)]
- Previously deprecated `NODE_REPL_HISTORY_FILE` environment variable has reached end-of-life and has been removed. [[`60c9ad7979`](https://github.com/nodejs/node/commit/60c9ad7979)]
- Use of the `timers.enroll()` and `timers.unenroll()` methods will emit a runtime deprecation warning. [[`68783ae0b8`](https://github.com/nodejs/node/commit/68783ae0b8)]
- Use of the `tls.convertNPNProtocols()` method will emit a runtime deprecation warning. Support for NPN has been removed from Node.js. [[`9204a0db6e`](https://github.com/nodejs/node/commit/9204a0db6e)]
- The `crypto.fips` property has been deprecated in documentation. [[`6e7992e8b8`](https://github.com/nodejs/node/commit/6e7992e8b8)]

### Commits

#### Semver-major

- [[`c9bb91af33`](https://github.com/nodejs/node/commit/c9bb91af33)] - **(SEMVER-MAJOR)** **assert**: remove `errorDiff` property (Ruben Bridgewater) [#19467](https://github.com/nodejs/node/pull/19467)
- [[`eb427caadd`](https://github.com/nodejs/node/commit/eb427caadd)] - **(SEMVER-MAJOR)** **assert**: improve default error messages (Ruben Bridgewater) [#19467](https://github.com/nodejs/node/pull/19467)
- [[`1964978fb8`](https://github.com/nodejs/node/commit/1964978fb8)] - **(SEMVER-MAJOR)** **assert**: detect faulty throws usage (Ruben Bridgewater) [#19867](https://github.com/nodejs/node/pull/19867)
- [[`9743e756e2`](https://github.com/nodejs/node/commit/9743e756e2)] - **(SEMVER-MAJOR)** **assert**: provide info about actual error (Ruben Bridgewater) [#19884](https://github.com/nodejs/node/pull/19884)
- [[`70dcacd710`](https://github.com/nodejs/node/commit/70dcacd710)] - **(SEMVER-MAJOR)** **assert**: deprecate assert.fail partially (Ruben Bridgewater) [#18418](https://github.com/nodejs/node/pull/18418)
- [[`3cd7977a42`](https://github.com/nodejs/node/commit/3cd7977a42)] - **(SEMVER-MAJOR)** **assert**: use a default message in assert (Ruben Bridgewater) [#18319](https://github.com/nodejs/node/pull/18319)
- [[`e65a6e81ef`](https://github.com/nodejs/node/commit/e65a6e81ef)] - **(SEMVER-MAJOR)** **assert**: stricter ifError (Ruben Bridgewater) [#18247](https://github.com/nodejs/node/pull/18247)
- [[`72bb4445c6`](https://github.com/nodejs/node/commit/72bb4445c6)] - **(SEMVER-MAJOR)** **assert**: wrap original error in ifError (Ruben Bridgewater) [#18247](https://github.com/nodejs/node/pull/18247)
- [[`d07c6f9739`](https://github.com/nodejs/node/commit/d07c6f9739)] - **(SEMVER-MAJOR)** **assert**: throw without args in ok (Ruben Bridgewater) [#17581](https://github.com/nodejs/node/pull/17581)
- [[`f76ef50432`](https://github.com/nodejs/node/commit/f76ef50432)] - **(SEMVER-MAJOR)** **assert**: improve simple assert (Ruben Bridgewater) [#17581](https://github.com/nodejs/node/pull/17581)
- [[`493340f56e`](https://github.com/nodejs/node/commit/493340f56e)] - **(SEMVER-MAJOR)** **assert**: use Object.is comparison in .strictEqual (Ruben Bridgewater) [#17003](https://github.com/nodejs/node/pull/17003)
- [[`1cc6b993b9`](https://github.com/nodejs/node/commit/1cc6b993b9)] - **(SEMVER-MAJOR)** **async_hooks**: remove deprecated API (Andreas Madsen) [#17147](https://github.com/nodejs/node/pull/17147)
- [[`81aaab75ca`](https://github.com/nodejs/node/commit/81aaab75ca)] - **(SEMVER-MAJOR)** **benchmark**: remove noAssert argument (Ruben Bridgewater) [#18395](https://github.com/nodejs/node/pull/18395)
- [[`876836b135`](https://github.com/nodejs/node/commit/876836b135)] - **(SEMVER-MAJOR)** **benchmark**: rename file (Ruben Bridgewater) [#18790](https://github.com/nodejs/node/pull/18790)
- [[`e9ec9ff269`](https://github.com/nodejs/node/commit/e9ec9ff269)] - **(SEMVER-MAJOR)** **benchmark**: add buffer fill benchmark (Ruben Bridgewater) [#18790](https://github.com/nodejs/node/pull/18790)
- [[`94d64877ff`](https://github.com/nodejs/node/commit/94d64877ff)] - **(SEMVER-MAJOR)** **benchmark**: improve buffer.readInt(B|L)E benchmarks (Rich Trott) [#11146](https://github.com/nodejs/node/pull/11146)
- [[`9d4ab90117`](https://github.com/nodejs/node/commit/9d4ab90117)] - **(SEMVER-MAJOR)** **buffer**: do deprecation warning outside `node_modules` (Anna Henningsen) [#19524](https://github.com/nodejs/node/pull/19524)
- [[`e8bb1f35df`](https://github.com/nodejs/node/commit/e8bb1f35df)] - **(SEMVER-MAJOR)** **buffer**: refactor all read/write functions (Ruben Bridgewater) [#18395](https://github.com/nodejs/node/pull/18395)
- [[`a6c490cc8e`](https://github.com/nodejs/node/commit/a6c490cc8e)] - **(SEMVER-MAJOR)** **buffer**: remove double ln (Ruben Bridgewater) [#18395](https://github.com/nodejs/node/pull/18395)
- [[`1411b30f46`](https://github.com/nodejs/node/commit/1411b30f46)] - **(SEMVER-MAJOR)** **buffer**: move c++ float functions to js (Ruben Bridgewater) [#18395](https://github.com/nodejs/node/pull/18395)
- [[`452eed956e`](https://github.com/nodejs/node/commit/452eed956e)] - **(SEMVER-MAJOR)** **buffer**: stricter isEncoding (Ruben Bridgewater) [#18790](https://github.com/nodejs/node/pull/18790)
- [[`177b7314cf`](https://github.com/nodejs/node/commit/177b7314cf)] - **(SEMVER-MAJOR)** **buffer**: improve Buffer#fill performance (Ruben Bridgewater) [#18790](https://github.com/nodejs/node/pull/18790)
- [[`1e802539b2`](https://github.com/nodejs/node/commit/1e802539b2)] - **(SEMVER-MAJOR)** **buffer**: throw when filling with empty buffers (cjihrig) [#18129](https://github.com/nodejs/node/pull/18129)
- [[`9fea7eae9a`](https://github.com/nodejs/node/commit/9fea7eae9a)] - **(SEMVER-MAJOR)** **buffer**: check byteLength in readUInt(B|L)E (Rich Trott) [#11146](https://github.com/nodejs/node/pull/11146)
- [[`d964ffeec3`](https://github.com/nodejs/node/commit/d964ffeec3)] - **(SEMVER-MAJOR)** **buffer**: check byteLength in readInt(B|L)E (Sebastian Van Sande) [#11146](https://github.com/nodejs/node/pull/11146)
- [[`cd174df353`](https://github.com/nodejs/node/commit/cd174df353)] - **(SEMVER-MAJOR)** **buffer**: throw on failed fill attempts (cjihrig) [#17427](https://github.com/nodejs/node/pull/17427)
- [[`010587b7c4`](https://github.com/nodejs/node/commit/010587b7c4)] - **(SEMVER-MAJOR)** **build**: remove implied support for win 2012 not R2 (Beth Griggs) [#19378](https://github.com/nodejs/node/pull/19378)
- [[`36a02d401c`](https://github.com/nodejs/node/commit/36a02d401c)] - **(SEMVER-MAJOR)** **build**: add option to build v8 with GN (Yang Guo) [#19201](https://github.com/nodejs/node/pull/19201)
- [[`608557a1fc`](https://github.com/nodejs/node/commit/608557a1fc)] - **(SEMVER-MAJOR)** **build**: update node.gyp to reference gypfiles/v8.gyp (Joyee Cheung) [#19201](https://github.com/nodejs/node/pull/19201)
- [[`3542411fda`](https://github.com/nodejs/node/commit/3542411fda)] - **(SEMVER-MAJOR)** **build**: reset embedder string to "-node.0" (Myles Borins) [#19201](https://github.com/nodejs/node/pull/19201)
- [[`08af7dba2a`](https://github.com/nodejs/node/commit/08af7dba2a)] - **(SEMVER-MAJOR)** **build**: add OpenSSL-1.1.0 support (Shigeki Ohtsu) [#19794](https://github.com/nodejs/node/pull/19794)
- [[`549b280b87`](https://github.com/nodejs/node/commit/549b280b87)] - **(SEMVER-MAJOR)** **build**: reset embedder string to "-node.0" (Michaël Zasso) [#18453](https://github.com/nodejs/node/pull/18453)
- [[`56ee94f184`](https://github.com/nodejs/node/commit/56ee94f184)] - **(SEMVER-MAJOR)** **build**: compile V8 using system compiler (Ben Noordhuis) [#17489](https://github.com/nodejs/node/pull/17489)
- [[`e9bcb39ef2`](https://github.com/nodejs/node/commit/e9bcb39ef2)] - **(SEMVER-MAJOR)** **build**: remove --no-i18n from V8 test options (Michaël Zasso) [#17489](https://github.com/nodejs/node/pull/17489)
- [[`4a16a5d988`](https://github.com/nodejs/node/commit/4a16a5d988)] - **(SEMVER-MAJOR)** **build**: compile with -std=gnu++1y (Michaël Zasso) [#17489](https://github.com/nodejs/node/pull/17489)
- [[`fe6bcce9af`](https://github.com/nodejs/node/commit/fe6bcce9af)] - **(SEMVER-MAJOR)** **build**: reset embedder string to "-node.0" (Michaël Zasso) [#17489](https://github.com/nodejs/node/pull/17489)
- [[`2c75b52af8`](https://github.com/nodejs/node/commit/2c75b52af8)] - **(SEMVER-MAJOR)** **build**: replace runtime flag with compiler option (Peter Marshall) [#16271](https://github.com/nodejs/node/pull/16271)
- [[`6e7028ea76`](https://github.com/nodejs/node/commit/6e7028ea76)] - **(SEMVER-MAJOR)** **build**: reset embedder string to "-node.0" (Michaël Zasso) [#16271](https://github.com/nodejs/node/pull/16271)
- [[`0e10717e43`](https://github.com/nodejs/node/commit/0e10717e43)] - **(SEMVER-MAJOR)** **build**: runtime-deprecate requiring deps (Timothy Gu) [#16392](https://github.com/nodejs/node/pull/16392)
- [[`eec659c138`](https://github.com/nodejs/node/commit/eec659c138)] - **(SEMVER-MAJOR)** **build, tools, win**: add nasm detection for OpenSSL (João Reis) [#19794](https://github.com/nodejs/node/pull/19794)
- [[`9bfe55e184`](https://github.com/nodejs/node/commit/9bfe55e184)] - **(SEMVER-MAJOR)** **child_process**: better spawn error message (Bartosz Sosnowski) [#19305](https://github.com/nodejs/node/pull/19305)
- [[`11b6c0de41`](https://github.com/nodejs/node/commit/11b6c0de41)] - **(SEMVER-MAJOR)** **child_process**: define EACCES as a runtime error (Gireesh Punathil) [#19294](https://github.com/nodejs/node/pull/19294)
- [[`38ee25e2e2`](https://github.com/nodejs/node/commit/38ee25e2e2)] - **(SEMVER-MAJOR)** **child_process**: do not ignore proto values of env (Anatoli Papirovski) [#18210](https://github.com/nodejs/node/pull/18210)
- [[`85739b6c5b`](https://github.com/nodejs/node/commit/85739b6c5b)] - **(SEMVER-MAJOR)** **child_process**: ignore undef/proto values of env (现充) [#15089](https://github.com/nodejs/node/pull/15089)
- [[`15d880bcb6`](https://github.com/nodejs/node/commit/15d880bcb6)] - **(SEMVER-MAJOR)** **console**: make .assert standard compliant (Ruben Bridgewater) [#17706](https://github.com/nodejs/node/pull/17706)
- [[`970ce14f61`](https://github.com/nodejs/node/commit/970ce14f61)] - **(SEMVER-MAJOR)** **crypto**: remove deperecated methods of TLS version (Shigeki Ohtsu) [#19794](https://github.com/nodejs/node/pull/19794)
- [[`1e07acd476`](https://github.com/nodejs/node/commit/1e07acd476)] - **(SEMVER-MAJOR)** **crypto**: add support for AES-CCM (Tobias Nießen) [#18138](https://github.com/nodejs/node/pull/18138)
- [[`333adf61eb`](https://github.com/nodejs/node/commit/333adf61eb)] - **(SEMVER-MAJOR)** **crypto**: fix error handling (Ruben Bridgewater) [#19445](https://github.com/nodejs/node/pull/19445)
- [[`81f88e30dd`](https://github.com/nodejs/node/commit/81f88e30dd)] - **(SEMVER-MAJOR)** **crypto**: doc-only deprecate createCipher/Decipher (Tobias Nießen) [#19343](https://github.com/nodejs/node/pull/19343)
- [[`19f3927d92`](https://github.com/nodejs/node/commit/19f3927d92)] - **(SEMVER-MAJOR)** **crypto**: deprecate Decipher.finaltol (Tobias Nießen) [#19353](https://github.com/nodejs/node/pull/19353)
- [[`6035beea93`](https://github.com/nodejs/node/commit/6035beea93)] - **(SEMVER-MAJOR)** **crypto**: runtime deprecate DEFAULT_ENCODING (James M Snell) [#18333](https://github.com/nodejs/node/pull/18333)
- [[`858b48b692`](https://github.com/nodejs/node/commit/858b48b692)] - **(SEMVER-MAJOR)** **crypto**: assign deprecation code for setAuthTag/GCM (Tobias Nießen) [#18017](https://github.com/nodejs/node/pull/18017)
- [[`845633a7c6`](https://github.com/nodejs/node/commit/845633a7c6)] - **(SEMVER-MAJOR)** **crypto**: better docs for cases where peer's public key is invalid (Jose M. Palacios Diaz) [#16849](https://github.com/nodejs/node/pull/16849)
- [[`e567402aba`](https://github.com/nodejs/node/commit/e567402aba)] - **(SEMVER-MAJOR)** **crypto**: migrate CipherBase to internal/errors (James M Snell) [#16527](https://github.com/nodejs/node/pull/16527)
- [[`2a3f8c3a83`](https://github.com/nodejs/node/commit/2a3f8c3a83)] - **(SEMVER-MAJOR)** **deps**: patch the V8 API to be forward compatible with 6.7 (Peter Marshall) [#19999](https://github.com/nodejs/node/pull/19999)
- [[`ea9de2c81a`](https://github.com/nodejs/node/commit/ea9de2c81a)] - **(SEMVER-MAJOR)** **deps**: split v8_monolith target into separate file (Yang Guo) [#19201](https://github.com/nodejs/node/pull/19201)
- [[`e8fc6b6901`](https://github.com/nodejs/node/commit/e8fc6b6901)] - **(SEMVER-MAJOR)** **deps**: update v8.gyp (Michaël Zasso) [#19201](https://github.com/nodejs/node/pull/19201)
- [[`9daebb48d6`](https://github.com/nodejs/node/commit/9daebb48d6)] - **(SEMVER-MAJOR)** **deps**: update V8 to 6.6.346.23 (Myles Borins) [#19201](https://github.com/nodejs/node/pull/19201)
- [[`7812ec735b`](https://github.com/nodejs/node/commit/7812ec735b)] - **(SEMVER-MAJOR)** **deps**: update archs files for OpenSSL-1.1.0 (Shigeki Ohtsu) [#19794](https://github.com/nodejs/node/pull/19794)
- [[`99eb744842`](https://github.com/nodejs/node/commit/99eb744842)] - **(SEMVER-MAJOR)** **deps**: add gyp, header and Makefile for openssl110 (Shigeki Ohtsu) [#19794](https://github.com/nodejs/node/pull/19794)
- [[`1bcb6c0d26`](https://github.com/nodejs/node/commit/1bcb6c0d26)] - **(SEMVER-MAJOR)** **deps**: add s390 asm rules for OpenSSL-1.1.0 (Shigeki Ohtsu) [#19794](https://github.com/nodejs/node/pull/19794)
- [[`6bab3c23b1`](https://github.com/nodejs/node/commit/6bab3c23b1)] - **(SEMVER-MAJOR)** **deps**: delete files of OpenSSL-1.0.2 (Shigeki Ohtsu) [#19794](https://github.com/nodejs/node/pull/19794)
- [[`66cb29e646`](https://github.com/nodejs/node/commit/66cb29e646)] - **(SEMVER-MAJOR)** **deps**: upgrade openssl sources to 1.1.0h (Shigeki Ohtsu) [#19794](https://github.com/nodejs/node/pull/19794)
- [[`9759573997`](https://github.com/nodejs/node/commit/9759573997)] - **(SEMVER-MAJOR)** **deps**: cherry-pick 46c4979 from upstream V8 (Michaël Zasso) [#18453](https://github.com/nodejs/node/pull/18453)
- [[`b4c1222acc`](https://github.com/nodejs/node/commit/b4c1222acc)] - **(SEMVER-MAJOR)** **deps**: skip some V8 tests for ppc and s390 (Michaël Zasso) [#18453](https://github.com/nodejs/node/pull/18453)
- [[`9396a9f02c`](https://github.com/nodejs/node/commit/9396a9f02c)] - **(SEMVER-MAJOR)** **deps**: cherry-pick 8bfbe25 from upstream V8 (Michaël Zasso) [#18453](https://github.com/nodejs/node/pull/18453)
- [[`d68ee7eab7`](https://github.com/nodejs/node/commit/d68ee7eab7)] - **(SEMVER-MAJOR)** **deps**: cherry-pick 04a06c9 from upstream V8 (Michaël Zasso) [#18453](https://github.com/nodejs/node/pull/18453)
- [[`88786fecff`](https://github.com/nodejs/node/commit/88786fecff)] - **(SEMVER-MAJOR)** **deps**: update V8 to ********** (Michaël Zasso) [#18453](https://github.com/nodejs/node/pull/18453)
- [[`142d6237b6`](https://github.com/nodejs/node/commit/142d6237b6)] - **(SEMVER-MAJOR)** **deps**: V8: reintroduce missing whitespace in test (Ali Ijaz Sheikh) [#18360](https://github.com/nodejs/node/pull/18360)
- [[`b06440356d`](https://github.com/nodejs/node/commit/b06440356d)] - **(SEMVER-MAJOR)** **deps**: cherry-pick c3bb73f from upstream V8 (Ali Ijaz Sheikh) [#18196](https://github.com/nodejs/node/pull/18196)
- [[`a1c5dddbb2`](https://github.com/nodejs/node/commit/a1c5dddbb2)] - **(SEMVER-MAJOR)** **deps**: cherry-pick 814577e from upstream V8 (Ali Ijaz Sheikh) [#18196](https://github.com/nodejs/node/pull/18196)
- [[`4c4af643e5`](https://github.com/nodejs/node/commit/4c4af643e5)] - **(SEMVER-MAJOR)** **deps**: update V8 to 6.4.388.40 (Michaël Zasso) [#17489](https://github.com/nodejs/node/pull/17489)
- [[`51054dac54`](https://github.com/nodejs/node/commit/51054dac54)] - **(SEMVER-MAJOR)** **deps**: cherry-pick c3bb73f from upstream V8 (Ali Ijaz Sheikh) [#18196](https://github.com/nodejs/node/pull/18196)
- [[`7d7a549219`](https://github.com/nodejs/node/commit/7d7a549219)] - **(SEMVER-MAJOR)** **deps**: cherry-pick 814577e from upstream V8 (Ali Ijaz Sheikh) [#18196](https://github.com/nodejs/node/pull/18196)
- [[`1854ba04e9`](https://github.com/nodejs/node/commit/1854ba04e9)] - **(SEMVER-MAJOR)** **deps**: update V8 to 6.3.292.46 (Michaël Zasso) [#16271](https://github.com/nodejs/node/pull/16271)
- [[`9ad994befb`](https://github.com/nodejs/node/commit/9ad994befb)] - **(SEMVER-MAJOR)** **dgram**: migrate bufferSize to use internal/errors (James M Snell) [#16567](https://github.com/nodejs/node/pull/16567)
- [[`8a5b7b2afe`](https://github.com/nodejs/node/commit/8a5b7b2afe)] - **(SEMVER-MAJOR)** **doc**: update required compiler level for AIX (Michael Dawson) [#20153](https://github.com/nodejs/node/pull/20153)
- [[`ae096ba27c`](https://github.com/nodejs/node/commit/ae096ba27c)] - **(SEMVER-MAJOR)** **doc**: fix API descriptions for OpenSSL-1.1.0 (Shigeki Ohtsu) [#19794](https://github.com/nodejs/node/pull/19794)
- [[`c111e133ae`](https://github.com/nodejs/node/commit/c111e133ae)] - **(SEMVER-MAJOR)** **doc**: add deprecation notice (Ruben Bridgewater) [#18395](https://github.com/nodejs/node/pull/18395)
- [[`740c426b21`](https://github.com/nodejs/node/commit/740c426b21)] - **(SEMVER-MAJOR)** **doc**: add a deprecation message for removing lttng (Glen Keane) [#18982](https://github.com/nodejs/node/pull/18982)
- [[`300f5ce346`](https://github.com/nodejs/node/commit/300f5ce346)] - **(SEMVER-MAJOR)** **doc**: deprecate top-level `this` (Hackzzila) [#16878](https://github.com/nodejs/node/pull/16878)
- [[`dbdcf12187`](https://github.com/nodejs/node/commit/dbdcf12187)] - **(SEMVER-MAJOR)** **doc**: correct buffer changelog ordering (cjihrig) [#18129](https://github.com/nodejs/node/pull/18129)
- [[`4319780389`](https://github.com/nodejs/node/commit/4319780389)] - **(SEMVER-MAJOR)** **doc**: remove double line break (Ruben Bridgewater) [#17581](https://github.com/nodejs/node/pull/17581)
- [[`ccc87ebb33`](https://github.com/nodejs/node/commit/ccc87ebb33)] - **(SEMVER-MAJOR)** **doc**: improve documentation for util.deprecate() (Rich Trott) [#16393](https://github.com/nodejs/node/pull/16393)
- [[`14bc3e22f3`](https://github.com/nodejs/node/commit/14bc3e22f3)] - **(SEMVER-MAJOR)** **domain**: runtime deprecate MakeCallback (Andreas Madsen) [#17417](https://github.com/nodejs/node/pull/17417)
- [[`5135e24133`](https://github.com/nodejs/node/commit/5135e24133)] - **(SEMVER-MAJOR)** **errors**: alter ERR_INVALID_CURSOR_POS (davidmarkclements) [#19960](https://github.com/nodejs/node/pull/19960)
- [[`eca95a9ea5`](https://github.com/nodejs/node/commit/eca95a9ea5)] - **(SEMVER-MAJOR)** **errors**: alter ERR_INVALID_PROTOCOL (davidmarkclements) [#19983](https://github.com/nodejs/node/pull/19983)
- [[`afb4d55ac4`](https://github.com/nodejs/node/commit/afb4d55ac4)] - **(SEMVER-MAJOR)** **errors**: alter ERR_INVALID_DOMAIN_NAME (davidmarkclements) [#19961](https://github.com/nodejs/node/pull/19961)
- [[`83a8261764`](https://github.com/nodejs/node/commit/83a8261764)] - **(SEMVER-MAJOR)** **errors**: alter and test ERR_INVALID_REPL_EVAL_CONFIG (davidmarkclements) [#19984](https://github.com/nodejs/node/pull/19984)
- [[`b40efa43bd`](https://github.com/nodejs/node/commit/b40efa43bd)] - **(SEMVER-MAJOR)** **errors**: alter ERR_INVALID_IP_ADDRESS (davidmarkclements) [#19979](https://github.com/nodejs/node/pull/19979)
- [[`d28211ec3d`](https://github.com/nodejs/node/commit/d28211ec3d)] - **(SEMVER-MAJOR)** **errors**: validate input arguments (Ruben Bridgewater) [#19924](https://github.com/nodejs/node/pull/19924)
- [[`b29c36b807`](https://github.com/nodejs/node/commit/b29c36b807)] - **(SEMVER-MAJOR)** **errors**: make dns errors consistent (Ruben Bridgewater) [#19754](https://github.com/nodejs/node/pull/19754)
- [[`7d06761f83`](https://github.com/nodejs/node/commit/7d06761f83)] - **(SEMVER-MAJOR)** **errors**: improve SystemError messages (Joyee Cheung) [#19514](https://github.com/nodejs/node/pull/19514)
- [[`28e4e43e51`](https://github.com/nodejs/node/commit/28e4e43e51)] - **(SEMVER-MAJOR)** **errors**: make input mandatory (Ruben Bridgewater) [#19445](https://github.com/nodejs/node/pull/19445)
- [[`6ef17303a7`](https://github.com/nodejs/node/commit/6ef17303a7)] - **(SEMVER-MAJOR)** **errors**: only init colors when util is not loaded (Joyee Cheung) [#18359](https://github.com/nodejs/node/pull/18359)
- [[`b1e6c0d44c`](https://github.com/nodejs/node/commit/b1e6c0d44c)] - **(SEMVER-MAJOR)** **errors, child_process**: use internal/errors codes (Jon Moss) [#14998](https://github.com/nodejs/node/pull/14998)
- [[`3bb6f07d52`](https://github.com/nodejs/node/commit/3bb6f07d52)] - **(SEMVER-MAJOR)** **events**: add off alias to removeListener (Ulmanb) [#17156](https://github.com/nodejs/node/pull/17156)
- [[`acc3c770e7`](https://github.com/nodejs/node/commit/acc3c770e7)] - **(SEMVER-MAJOR)** **fs**: fix error handling (Ruben Bridgewater) [#19445](https://github.com/nodejs/node/pull/19445)
- [[`897f7b6c6b`](https://github.com/nodejs/node/commit/897f7b6c6b)] - **(SEMVER-MAJOR)** **fs**: improve errors in watchFile and unwatchFile (Joyee Cheung) [#19345](https://github.com/nodejs/node/pull/19345)
- [[`301f6cc553`](https://github.com/nodejs/node/commit/301f6cc553)] - **(SEMVER-MAJOR)** **fs**: remove watcher state errors for fs.watch (Joyee Cheung) [#19345](https://github.com/nodejs/node/pull/19345)
- [[`6c25f2ea49`](https://github.com/nodejs/node/commit/6c25f2ea49)] - **(SEMVER-MAJOR)** **fs**: improve errors thrown from fs.watch() (Joyee Cheung) [#19089](https://github.com/nodejs/node/pull/19089)
- [[`f7e5b385a7`](https://github.com/nodejs/node/commit/f7e5b385a7)] - **(SEMVER-MAJOR)** **fs**: remove unused SYNC\_\* helpers (Joyee Cheung) [#19041](https://github.com/nodejs/node/pull/19041)
- [[`80bd2da6e1`](https://github.com/nodejs/node/commit/80bd2da6e1)] - **(SEMVER-MAJOR)** **fs**: use SyncCall in WriteBuffers (Joyee Cheung) [#19041](https://github.com/nodejs/node/pull/19041)
- [[`49dd80935c`](https://github.com/nodejs/node/commit/49dd80935c)] - **(SEMVER-MAJOR)** **fs**: throw futimesSync errors in JS (Joyee Cheung) [#19041](https://github.com/nodejs/node/pull/19041)
- [[`994320b07b`](https://github.com/nodejs/node/commit/994320b07b)] - **(SEMVER-MAJOR)** **fs**: throw writeSync errors in JS (Joyee Cheung) [#19041](https://github.com/nodejs/node/pull/19041)
- [[`1650eaeac4`](https://github.com/nodejs/node/commit/1650eaeac4)] - **(SEMVER-MAJOR)** **fs**: throw fchownSync errors in JS (Joyee Cheung) [#19041](https://github.com/nodejs/node/pull/19041)
- [[`79b195437c`](https://github.com/nodejs/node/commit/79b195437c)] - **(SEMVER-MAJOR)** **fs**: throw fchmodSync errors in JS (Joyee Cheung) [#19041](https://github.com/nodejs/node/pull/19041)
- [[`c6acfdb3ac`](https://github.com/nodejs/node/commit/c6acfdb3ac)] - **(SEMVER-MAJOR)** **fs**: throw readSync errors in JS (Joyee Cheung) [#19041](https://github.com/nodejs/node/pull/19041)
- [[`4eb45b884d`](https://github.com/nodejs/node/commit/4eb45b884d)] - **(SEMVER-MAJOR)** **fs**: throw copyFileSync errors in JS (Joyee Cheung) [#18871](https://github.com/nodejs/node/pull/18871)
- [[`d2dc2a5011`](https://github.com/nodejs/node/commit/d2dc2a5011)] - **(SEMVER-MAJOR)** **fs**: throw fs.mkdtempSync errors in JS land (Joyee Cheung) [#18871](https://github.com/nodejs/node/pull/18871)
- [[`82523d3b6e`](https://github.com/nodejs/node/commit/82523d3b6e)] - **(SEMVER-MAJOR)** **fs**: throw fs.utimesSync errors in JS land (Joyee Cheung) [#18871](https://github.com/nodejs/node/pull/18871)
- [[`8fb5a6cd81`](https://github.com/nodejs/node/commit/8fb5a6cd81)] - **(SEMVER-MAJOR)** **fs**: throw fs.chownSync errors in JS land (Joyee Cheung) [#18871](https://github.com/nodejs/node/pull/18871)
- [[`437c756493`](https://github.com/nodejs/node/commit/437c756493)] - **(SEMVER-MAJOR)** **fs**: throw fs.chmodSync errors in JS land (Joyee Cheung) [#18871](https://github.com/nodejs/node/pull/18871)
- [[`e8ec898a7d`](https://github.com/nodejs/node/commit/e8ec898a7d)] - **(SEMVER-MAJOR)** **fs**: use SyncCall in OpenFileHandle (Joyee Cheung) [#18871](https://github.com/nodejs/node/pull/18871)
- [[`fea5dda1d1`](https://github.com/nodejs/node/commit/fea5dda1d1)] - **(SEMVER-MAJOR)** **fs**: throw openSync errors in JS (Joyee Cheung) [#18871](https://github.com/nodejs/node/pull/18871)
- [[`d2c4f5082f`](https://github.com/nodejs/node/commit/d2c4f5082f)] - **(SEMVER-MAJOR)** **fs**: throw readdirSync errors in JS (Joyee Cheung) [#18871](https://github.com/nodejs/node/pull/18871)
- [[`72d150ea6f`](https://github.com/nodejs/node/commit/72d150ea6f)] - **(SEMVER-MAJOR)** **fs**: throw realpathSync.native errors in JS (Joyee Cheung) [#18871](https://github.com/nodejs/node/pull/18871)
- [[`77b42e34de`](https://github.com/nodejs/node/commit/77b42e34de)] - **(SEMVER-MAJOR)** **fs**: throw mkdirSync errors in JS (Joyee Cheung) [#18871](https://github.com/nodejs/node/pull/18871)
- [[`46164ba212`](https://github.com/nodejs/node/commit/46164ba212)] - **(SEMVER-MAJOR)** **fs**: throw rmdirSync errors in JS (Joyee Cheung) [#18871](https://github.com/nodejs/node/pull/18871)
- [[`c3eb3efa31`](https://github.com/nodejs/node/commit/c3eb3efa31)] - **(SEMVER-MAJOR)** **fs**: fix functions executed in wrong context (Ruben Bridgewater) [#18668](https://github.com/nodejs/node/pull/18668)
- [[`e9f2cecf1a`](https://github.com/nodejs/node/commit/e9f2cecf1a)] - **(SEMVER-MAJOR)** **_Revert_** "**fs**: Revert throw on invalid callbacks" (Ruben Bridgewater) [#18668](https://github.com/nodejs/node/pull/18668)
- [[`d8f73385e2`](https://github.com/nodejs/node/commit/d8f73385e2)] - **(SEMVER-MAJOR)** **fs**: throw errors on invalid paths synchronously (Joyee Cheung) [#18308](https://github.com/nodejs/node/pull/18308)
- [[`67a4ce1c6e`](https://github.com/nodejs/node/commit/67a4ce1c6e)] - **(SEMVER-MAJOR)** **fs**: partition readFile against pool exhaustion (Jamie Davis) [#17054](https://github.com/nodejs/node/pull/17054)
- [[`776f6cdfc4`](https://github.com/nodejs/node/commit/776f6cdfc4)] - **(SEMVER-MAJOR)** **fs**: throw errors from fs.unlinkSync in JS (Joyee Cheung) [#18348](https://github.com/nodejs/node/pull/18348)
- [[`eca93e631f`](https://github.com/nodejs/node/commit/eca93e631f)] - **(SEMVER-MAJOR)** **fs**: throw errors from fs.fsyncSync in JS (Joyee Cheung) [#18348](https://github.com/nodejs/node/pull/18348)
- [[`f5e287ba20`](https://github.com/nodejs/node/commit/f5e287ba20)] - **(SEMVER-MAJOR)** **fs**: throw errors from fs.fdatasyncSync in JS (Joyee Cheung) [#18348](https://github.com/nodejs/node/pull/18348)
- [[`b3a7df7c6d`](https://github.com/nodejs/node/commit/b3a7df7c6d)] - **(SEMVER-MAJOR)** **fs**: throw errors from fs.ftruncateSync in JS (Joyee Cheung) [#18348](https://github.com/nodejs/node/pull/18348)
- [[`5583981c52`](https://github.com/nodejs/node/commit/5583981c52)] - **(SEMVER-MAJOR)** **fs**: throw errors from fs.renameSync in JS (Joyee Cheung) [#18348](https://github.com/nodejs/node/pull/18348)
- [[`09da11e5e1`](https://github.com/nodejs/node/commit/09da11e5e1)] - **(SEMVER-MAJOR)** **fs**: throw errors from fs.readlinkSync in JS (Joyee Cheung) [#18348](https://github.com/nodejs/node/pull/18348)
- [[`167e22937c`](https://github.com/nodejs/node/commit/167e22937c)] - **(SEMVER-MAJOR)** **fs**: throw errors from fs.linkSync in JS (Joyee Cheung) [#18348](https://github.com/nodejs/node/pull/18348)
- [[`32bf0f6c5b`](https://github.com/nodejs/node/commit/32bf0f6c5b)] - **(SEMVER-MAJOR)** **fs**: throw errors from fs.symlinkSync in JS (Joyee Cheung) [#18348](https://github.com/nodejs/node/pull/18348)
- [[`8c00a809bc`](https://github.com/nodejs/node/commit/8c00a809bc)] - **(SEMVER-MAJOR)** **fs**: throw fs.fstat{Sync} errors in JS (Joyee Cheung) [#17914](https://github.com/nodejs/node/pull/17914)
- [[`da7804f259`](https://github.com/nodejs/node/commit/da7804f259)] - **(SEMVER-MAJOR)** **fs**: throw fs.lstat{Sync} errors in JS (Joyee Cheung) [#17914](https://github.com/nodejs/node/pull/17914)
- [[`57d7638af3`](https://github.com/nodejs/node/commit/57d7638af3)] - **(SEMVER-MAJOR)** **fs**: throw fs.stat{Sync} errors in JS (Joyee Cheung) [#17914](https://github.com/nodejs/node/pull/17914)
- [[`791975d189`](https://github.com/nodejs/node/commit/791975d189)] - **(SEMVER-MAJOR)** **fs**: return errno and take fs_req_wrap in SyncCall (Joyee Cheung) [#17914](https://github.com/nodejs/node/pull/17914)
- [[`71396a200d`](https://github.com/nodejs/node/commit/71396a200d)] - **(SEMVER-MAJOR)** **fs**: validate path in fs.exists{Sync} (Joyee Cheung) [#17852](https://github.com/nodejs/node/pull/17852)
- [[`9ec700b073`](https://github.com/nodejs/node/commit/9ec700b073)] - **(SEMVER-MAJOR)** **fs**: validate path in fs.readFile (Joyee Cheung) [#17852](https://github.com/nodejs/node/pull/17852)
- [[`8599465d33`](https://github.com/nodejs/node/commit/8599465d33)] - **(SEMVER-MAJOR)** **fs**: migrate errors to internal/errors (Steven) [#17719](https://github.com/nodejs/node/pull/17719)
- [[`6100e12667`](https://github.com/nodejs/node/commit/6100e12667)] - **(SEMVER-MAJOR)** **fs**: move type checking to js (James M Snell) [#17667](https://github.com/nodejs/node/pull/17667)
- [[`805dca199a`](https://github.com/nodejs/node/commit/805dca199a)] - **(SEMVER-MAJOR)** **fs**: remove unnecessary throw on fs.mkdtemp (James M Snell) [#17334](https://github.com/nodejs/node/pull/17334)
- [[`163869879e`](https://github.com/nodejs/node/commit/163869879e)] - **(SEMVER-MAJOR)** **fs**: move type checking for fs.read to js (James M Snell) [#17334](https://github.com/nodejs/node/pull/17334)
- [[`448ec0b5aa`](https://github.com/nodejs/node/commit/448ec0b5aa)] - **(SEMVER-MAJOR)** **fs**: move type checking in fs.futimes to js (James M Snell) [#17334](https://github.com/nodejs/node/pull/17334)
- [[`82eb459e3f`](https://github.com/nodejs/node/commit/82eb459e3f)] - **(SEMVER-MAJOR)** **fs**: move type checking for fs.fchown to js (James M Snell) [#17334](https://github.com/nodejs/node/pull/17334)
- [[`0a01aa8e94`](https://github.com/nodejs/node/commit/0a01aa8e94)] - **(SEMVER-MAJOR)** **fs**: move type checking for fs.fchmod to js (James M Snell) [#17334](https://github.com/nodejs/node/pull/17334)
- [[`d453fac33b`](https://github.com/nodejs/node/commit/d453fac33b)] - **(SEMVER-MAJOR)** **fs**: move type checking for fs.ftruncate to js (James M Snell) [#17334](https://github.com/nodejs/node/pull/17334)
- [[`8cb080c486`](https://github.com/nodejs/node/commit/8cb080c486)] - **(SEMVER-MAJOR)** **fs**: move type checking for fs.sync to js (James M Snell) [#17334](https://github.com/nodejs/node/pull/17334)
- [[`956f97b875`](https://github.com/nodejs/node/commit/956f97b875)] - **(SEMVER-MAJOR)** **fs**: move type checking for fs.fdatasync to js (James M Snell) [#17334](https://github.com/nodejs/node/pull/17334)
- [[`639096855e`](https://github.com/nodejs/node/commit/639096855e)] - **(SEMVER-MAJOR)** **fs**: move type checking on fs.fstat to js (James M Snell) [#17334](https://github.com/nodejs/node/pull/17334)
- [[`8974df15a9`](https://github.com/nodejs/node/commit/8974df15a9)] - **(SEMVER-MAJOR)** **fs**: move type checking for fs.close to js (James M Snell) [#17334](https://github.com/nodejs/node/pull/17334)
- [[`07d34092b1`](https://github.com/nodejs/node/commit/07d34092b1)] - **(SEMVER-MAJOR)** **fs**: throw fs.access errors in JS (Joyee Cheung) [#17160](https://github.com/nodejs/node/pull/17160)
- [[`ab8bf26994`](https://github.com/nodejs/node/commit/ab8bf26994)] - **(SEMVER-MAJOR)** **fs,cluster,net**: assign error codes to remaining errors (Michaël Zasso) [#19373](https://github.com/nodejs/node/pull/19373)
- [[`33ce9a6409`](https://github.com/nodejs/node/commit/33ce9a6409)] - **(SEMVER-MAJOR)** **http**: relax requirements on upgrade listener (Anatoli Papirovski) [#19981](https://github.com/nodejs/node/pull/19981)
- [[`29be1e5f84`](https://github.com/nodejs/node/commit/29be1e5f84)] - **(SEMVER-MAJOR)** **http**: do not replace .read() in IncomingMessage (Matteo Collina) [#18939](https://github.com/nodejs/node/pull/18939)
- [[`51be03cd57`](https://github.com/nodejs/node/commit/51be03cd57)] - **(SEMVER-MAJOR)** **http**: remove default 'error' listener on upgrade (Luigi Pinca) [#18868](https://github.com/nodejs/node/pull/18868)
- [[`8118da7430`](https://github.com/nodejs/node/commit/8118da7430)] - **(SEMVER-MAJOR)** **http**: OutgoingMessage.end() should return this (Matteo Collina) [#18780](https://github.com/nodejs/node/pull/18780)
- [[`baf8495078`](https://github.com/nodejs/node/commit/baf8495078)] - **(SEMVER-MAJOR)** **http**: process 100, 102-199 according to specs. (Miles Elam) [#18033](https://github.com/nodejs/node/pull/18033)
- [[`b961d9fd83`](https://github.com/nodejs/node/commit/b961d9fd83)] - **(SEMVER-MAJOR)** **http**: disallow two-byte characters in URL path (Benno Fünfstück) [#16237](https://github.com/nodejs/node/pull/16237)
- [[`0a84e95cd9`](https://github.com/nodejs/node/commit/0a84e95cd9)] - **(SEMVER-MAJOR)** **http**: improve errors thrown in header validation (Joyee Cheung) [#16719](https://github.com/nodejs/node/pull/16719)
- [[`3d93f39190`](https://github.com/nodejs/node/commit/3d93f39190)] - **(SEMVER-MAJOR)** **http2**: make response.end() return this (Matteo Collina) [#18780](https://github.com/nodejs/node/pull/18780)
- [[`fc61ee32fe`](https://github.com/nodejs/node/commit/fc61ee32fe)] - **(SEMVER-MAJOR)** **http2**: use session kUpdateTimer from kUpdateTimer (Jeremiah Senkpiel) [#17704](https://github.com/nodejs/node/pull/17704)
- [[`93eb68e6d2`](https://github.com/nodejs/node/commit/93eb68e6d2)] - **(SEMVER-MAJOR)** **http2**: use actual Timeout instances (Jeremiah Senkpiel) [#17704](https://github.com/nodejs/node/pull/17704)
- [[`4e1f0907da`](https://github.com/nodejs/node/commit/4e1f0907da)] - **(SEMVER-MAJOR)** **inspector**: migrate errors from C++ to JS (Michaël Zasso) [#19387](https://github.com/nodejs/node/pull/19387)
- [[`0876a0314d`](https://github.com/nodejs/node/commit/0876a0314d)] - **(SEMVER-MAJOR)** **lib**: ensure --check flag works with --require (John-David Dalton) [#19600](https://github.com/nodejs/node/pull/19600)
- [[`b38c81cb44`](https://github.com/nodejs/node/commit/b38c81cb44)] - **(SEMVER-MAJOR)** **lib**: improve error handling (Ruben Bridgewater) [#19445](https://github.com/nodejs/node/pull/19445)
- [[`c6b6c92185`](https://github.com/nodejs/node/commit/c6b6c92185)] - **(SEMVER-MAJOR)** **lib**: always show ERR_INVALID_ARG_TYPE received part (Ruben Bridgewater) [#19445](https://github.com/nodejs/node/pull/19445)
- [[`1d2fd8b65b`](https://github.com/nodejs/node/commit/1d2fd8b65b)] - **(SEMVER-MAJOR)** **lib**: port remaining errors to new system (Michaël Zasso) [#19137](https://github.com/nodejs/node/pull/19137)
- [[`1e8d110e64`](https://github.com/nodejs/node/commit/1e8d110e64)] - **(SEMVER-MAJOR)** **lib**: port errors to new system (Michaël Zasso) [#19034](https://github.com/nodejs/node/pull/19034)
- [[`341770fedf`](https://github.com/nodejs/node/commit/341770fedf)] - **(SEMVER-MAJOR)** **lib**: improve normalize encoding performance (Ruben Bridgewater) [#18790](https://github.com/nodejs/node/pull/18790)
- [[`e99ae7764d`](https://github.com/nodejs/node/commit/e99ae7764d)] - **(SEMVER-MAJOR)** **lib**: make console writable and non-enumerable (Ruben Bridgewater) [#17708](https://github.com/nodejs/node/pull/17708)
- [[`d3ac18a176`](https://github.com/nodejs/node/commit/d3ac18a176)] - **(SEMVER-MAJOR)** **lib**: migrate \_http_outgoing.js's remaining errors (Anton Paras) [#17837](https://github.com/nodejs/node/pull/17837)
- [[`d022cb1bdd`](https://github.com/nodejs/node/commit/d022cb1bdd)] - **(SEMVER-MAJOR)** **lib**: combine similar error codes (Weijia Wang) [#17648](https://github.com/nodejs/node/pull/17648)
- [[`05948d8e4e`](https://github.com/nodejs/node/commit/05948d8e4e)] - **(SEMVER-MAJOR)** **lib**: remove use of Debug.MakeMirror() (Ben Noordhuis) [#13295](https://github.com/nodejs/node/pull/13295)
- [[`6f724e1563`](https://github.com/nodejs/node/commit/6f724e1563)] - **(SEMVER-MAJOR)** **lib,src**: remove vm.runInDebugContext() (Ben Noordhuis) [#13295](https://github.com/nodejs/node/pull/13295)
- [[`c1278e5329`](https://github.com/nodejs/node/commit/c1278e5329)] - **(SEMVER-MAJOR)** **lib,test**: minor refactoring (Ruben Bridgewater) [#19445](https://github.com/nodejs/node/pull/19445)
- [[`77b52fd58f`](https://github.com/nodejs/node/commit/77b52fd58f)] - **(SEMVER-MAJOR)** **module**: move options checks from C++ to JS (Michaël Zasso) [#19822](https://github.com/nodejs/node/pull/19822)
- [[`1ed36aeb53`](https://github.com/nodejs/node/commit/1ed36aeb53)] - **(SEMVER-MAJOR)** **module**: check file ext before dir as documented (Bradley Farias) [#15015](https://github.com/nodejs/node/pull/15015)
- [[`bd4773a043`](https://github.com/nodejs/node/commit/bd4773a043)] - **(SEMVER-MAJOR)** **module**: use undefined if no main (Rich Trott) [#18593](https://github.com/nodejs/node/pull/18593)
- [[`9fb91fe1d6`](https://github.com/nodejs/node/commit/9fb91fe1d6)] - **(SEMVER-MAJOR)** **module**: validate request in require.resolve.paths (Joyee Cheung) [#18359](https://github.com/nodejs/node/pull/18359)
- [[`d4dd0665f5`](https://github.com/nodejs/node/commit/d4dd0665f5)] - **(SEMVER-MAJOR)** **module**: validate request in require.resolve (Joyee Cheung) [#18359](https://github.com/nodejs/node/pull/18359)
- [[`b21715403b`](https://github.com/nodejs/node/commit/b21715403b)] - **(SEMVER-MAJOR)** **module**: use internal/errors.js in module.require (Joyee Cheung) [#18359](https://github.com/nodejs/node/pull/18359)
- [[`fea1e05ba5`](https://github.com/nodejs/node/commit/fea1e05ba5)] - **(SEMVER-MAJOR)** **module**: rename internalModuleReadFile to internalModuleReadJSON (John-David Dalton) [#17084](https://github.com/nodejs/node/pull/17084)
- [[`0fdd88a374`](https://github.com/nodejs/node/commit/0fdd88a374)] - **(SEMVER-MAJOR)** **module**: speed up package.json parsing more (Ben Noordhuis) [#15767](https://github.com/nodejs/node/pull/15767)
- [[`fdbb6dd042`](https://github.com/nodejs/node/commit/fdbb6dd042)] - **(SEMVER-MAJOR)** **module**: speed up package.json parsing (Ben Noordhuis) [#15767](https://github.com/nodejs/node/pull/15767)
- [[`9b7a6914a7`](https://github.com/nodejs/node/commit/9b7a6914a7)] - **(SEMVER-MAJOR)** **net**: emit 'close' after 'end' (Luigi Pinca) [#19241](https://github.com/nodejs/node/pull/19241)
- [[`b98aaa312e`](https://github.com/nodejs/node/commit/b98aaa312e)] - **(SEMVER-MAJOR)** **net**: migrate errors to internal/errors (kysnm) [#17766](https://github.com/nodejs/node/pull/17766)
- [[`24dd92e77f`](https://github.com/nodejs/node/commit/24dd92e77f)] - **(SEMVER-MAJOR)** **net**: use actual Timeout instance on Sockets (Jeremiah Senkpiel) [#17704](https://github.com/nodejs/node/pull/17704)
- [[`3701b02309`](https://github.com/nodejs/node/commit/3701b02309)] - **(SEMVER-MAJOR)** **net**: remove deprecated getters for internals (Anna Henningsen) [#17141](https://github.com/nodejs/node/pull/17141)
- [[`056b858e57`](https://github.com/nodejs/node/commit/056b858e57)] - **(SEMVER-MAJOR)** **os**: migrate node_os.cc to internal/errors (James M Snell) [#16567](https://github.com/nodejs/node/pull/16567)
- [[`058e7fb8e6`](https://github.com/nodejs/node/commit/058e7fb8e6)] - **(SEMVER-MAJOR)** **process**: fix error handling (Ruben Bridgewater) [#19445](https://github.com/nodejs/node/pull/19445)
- [[`5826fe4e79`](https://github.com/nodejs/node/commit/5826fe4e79)] - **(SEMVER-MAJOR)** **process**: doc-only deprecate non-string env value (Timothy Gu) [#18990](https://github.com/nodejs/node/pull/18990)
- [[`b32bcf7e9c`](https://github.com/nodejs/node/commit/b32bcf7e9c)] - **(SEMVER-MAJOR)** **process**: unify error message from chdir() errors (Sarat Addepalli) [#19088](https://github.com/nodejs/node/pull/19088)
- [[`703e37cf3f`](https://github.com/nodejs/node/commit/703e37cf3f)] - **(SEMVER-MAJOR)** **process**: deprecate process.assert() (Ruben Bridgewater) [#18666](https://github.com/nodejs/node/pull/18666)
- [[`4893f70d12`](https://github.com/nodejs/node/commit/4893f70d12)] - **(SEMVER-MAJOR)** **repl**: remove magic mode (Ruben Bridgewater) [#19187](https://github.com/nodejs/node/pull/19187)
- [[`60c9ad7979`](https://github.com/nodejs/node/commit/60c9ad7979)] - **(SEMVER-MAJOR)** **repl**: remove deprecated NODE_REPL_HISTORY_FILE (Ruben Bridgewater) [#13876](https://github.com/nodejs/node/pull/13876)
- [[`ab5a2aba38`](https://github.com/nodejs/node/commit/ab5a2aba38)] - **(SEMVER-MAJOR)** **repl**: migrate errors to internal/errors (kysnm) [#17716](https://github.com/nodejs/node/pull/17716)
- [[`90a43906ab`](https://github.com/nodejs/node/commit/90a43906ab)] - **(SEMVER-MAJOR)** **repl**: show proxies as Proxy objects (Ben Noordhuis) [#16485](https://github.com/nodejs/node/pull/16485)
- [[`a6be27a77f`](https://github.com/nodejs/node/commit/a6be27a77f)] - **(SEMVER-MAJOR)** **src**: throw ERR_MISSING_ARGS in node_crypto.cc (Joyee Cheung) [#20121](https://github.com/nodejs/node/pull/20121)
- [[`f042929c3c`](https://github.com/nodejs/node/commit/f042929c3c)] - **(SEMVER-MAJOR)** **src**: throw ERR_INVALID_ARG_VALUE in node_crypto.cc (Joyee Cheung) [#20121](https://github.com/nodejs/node/pull/20121)
- [[`7946910475`](https://github.com/nodejs/node/commit/7946910475)] - **(SEMVER-MAJOR)** **src**: throw ERR_MISSING_MODULE in module_wrap.cc (Joyee Cheung) [#20121](https://github.com/nodejs/node/pull/20121)
- [[`02db891bcc`](https://github.com/nodejs/node/commit/02db891bcc)] - **(SEMVER-MAJOR)** **src**: throw ERR_BUFFER_OUT_OF_BOUNDS in node_buffer.cc (Joyee Cheung) [#20121](https://github.com/nodejs/node/pull/20121)
- [[`0fdf39aefa`](https://github.com/nodejs/node/commit/0fdf39aefa)] - **(SEMVER-MAJOR)** **src**: throw ERR_INVALID_ARG_TYPE in C++ argument checks (Joyee Cheung) [#20121](https://github.com/nodejs/node/pull/20121)
- [[`1d0ad63887`](https://github.com/nodejs/node/commit/1d0ad63887)] - **(SEMVER-MAJOR)** **src**: migrate ERR_INDEX_OUT_OF_RANGE in C++ (Joyee Cheung) [#20121](https://github.com/nodejs/node/pull/20121)
- [[`c218854bc8`](https://github.com/nodejs/node/commit/c218854bc8)] - **(SEMVER-MAJOR)** **src**: add THROW_ERR\_\* helpers (Joyee Cheung) [#20121](https://github.com/nodejs/node/pull/20121)
- [[`03f8c4f039`](https://github.com/nodejs/node/commit/03f8c4f039)] - **(SEMVER-MAJOR)** **src**: update NODE_MODULE_VERSION to 63 (Myles Borins) [#19201](https://github.com/nodejs/node/pull/19201)
- [[`63eb267c34`](https://github.com/nodejs/node/commit/63eb267c34)] - **(SEMVER-MAJOR)** **src**: migrate string_bytes.cc to throw errors with code (Joyee Cheung) [#19739](https://github.com/nodejs/node/pull/19739)
- [[`289d152ce0`](https://github.com/nodejs/node/commit/289d152ce0)] - **(SEMVER-MAJOR)** **src**: add error code helpers to src/node_errors.h (Joyee Cheung) [#19739](https://github.com/nodejs/node/pull/19739)
- [[`3b1e5d9cf7`](https://github.com/nodejs/node/commit/3b1e5d9cf7)] - **(SEMVER-MAJOR)** **src**: request code cache explicitly (Mythri Alle) [#18453](https://github.com/nodejs/node/pull/18453)
- [[`a9755d493e`](https://github.com/nodejs/node/commit/a9755d493e)] - **(SEMVER-MAJOR)** **src**: update NODE_MODULE_VERSION to 62 (Michaël Zasso) [#18453](https://github.com/nodejs/node/pull/18453)
- [[`30fd3d25df`](https://github.com/nodejs/node/commit/30fd3d25df)] - **(SEMVER-MAJOR)** **src**: Remove lttng support. (Glen Keane) [#18982](https://github.com/nodejs/node/pull/18982)
- [[`efb32592e1`](https://github.com/nodejs/node/commit/efb32592e1)] - **(SEMVER-MAJOR)** **src**: deprecate legacy node::MakeCallback (Ali Ijaz Sheikh) [#18632](https://github.com/nodejs/node/pull/18632)
- [[`3154d83a02`](https://github.com/nodejs/node/commit/3154d83a02)] - **(SEMVER-MAJOR)** **src**: update postmortem constant name (cjihrig) [#17489](https://github.com/nodejs/node/pull/17489)
- [[`0398debe81`](https://github.com/nodejs/node/commit/0398debe81)] - **(SEMVER-MAJOR)** **src**: update NODE_MODULE_VERSION to 61 (Michaël Zasso) [#17489](https://github.com/nodejs/node/pull/17489)
- [[`98d9540dd7`](https://github.com/nodejs/node/commit/98d9540dd7)] - **(SEMVER-MAJOR)** **src**: use uv_hrtime as tracing timestamp (Ali Ijaz Sheikh) [#18196](https://github.com/nodejs/node/pull/18196)
- [[`2a61ce5996`](https://github.com/nodejs/node/commit/2a61ce5996)] - **(SEMVER-MAJOR)** **src**: validate args length in Access and Close (Sakthipriyan Vairamani (thefourtheye)) [#18203](https://github.com/nodejs/node/pull/18203)
- [[`a1ed29b1c6`](https://github.com/nodejs/node/commit/a1ed29b1c6)] - **(SEMVER-MAJOR)** **src**: implement getting current time in NodePlatform (Sergei Datsenko) [#16271](https://github.com/nodejs/node/pull/16271)
- [[`a7c5fe9ba6`](https://github.com/nodejs/node/commit/a7c5fe9ba6)] - **(SEMVER-MAJOR)** **src**: update NODE_MODULE_VERSION to 60 (Michaël Zasso) [#16271](https://github.com/nodejs/node/pull/16271)
- [[`804eb3cd73`](https://github.com/nodejs/node/commit/804eb3cd73)] - **(SEMVER-MAJOR)** **src**: remove process.\_debugPause() (Ben Noordhuis) [#17060](https://github.com/nodejs/node/pull/17060)
- [[`c3dc0e0d75`](https://github.com/nodejs/node/commit/c3dc0e0d75)] - **(SEMVER-MAJOR)** **src**: add CollectExceptionInfo & errors.SystemError (James M Snell) [#16567](https://github.com/nodejs/node/pull/16567)
- [[`3d20190a3a`](https://github.com/nodejs/node/commit/3d20190a3a)] - **(SEMVER-MAJOR)** **src**: remove throws in set/getHiddenValue (James M Snell) [#16544](https://github.com/nodejs/node/pull/16544)
- [[`67c8511ea1`](https://github.com/nodejs/node/commit/67c8511ea1)] - **(SEMVER-MAJOR)** **src**: use internal/errors for startSigintWatchdog (James M Snell) [#16546](https://github.com/nodejs/node/pull/16546)
- [[`cf5f9867ff`](https://github.com/nodejs/node/commit/cf5f9867ff)] - **(SEMVER-MAJOR)** **stream**: 'readable' have precedence over flowing (Matteo Collina) [#18994](https://github.com/nodejs/node/pull/18994)
- [[`c9794880e8`](https://github.com/nodejs/node/commit/c9794880e8)] - **(SEMVER-MAJOR)** **stream**: make virtual methods errors consistent (Luigi Pinca) [#18813](https://github.com/nodejs/node/pull/18813)
- [[`5e3f51648e`](https://github.com/nodejs/node/commit/5e3f51648e)] - **(SEMVER-MAJOR)** **stream**: updated streams error handling (Mathias Buus) [#18438](https://github.com/nodejs/node/pull/18438)
- [[`f6721c20df`](https://github.com/nodejs/node/commit/f6721c20df)] - **(SEMVER-MAJOR)** **stream**: writable.end should return this. (Matteo Collina) [#18780](https://github.com/nodejs/node/pull/18780)
- [[`faeee11c1f`](https://github.com/nodejs/node/commit/faeee11c1f)] - **(SEMVER-MAJOR)** **stream**: readable continues to read when `push('')` (陈刚) [#18211](https://github.com/nodejs/node/pull/18211)
- [[`46e0a55b84`](https://github.com/nodejs/node/commit/46e0a55b84)] - **(SEMVER-MAJOR)** **stream**: add type and range check for highWaterMark (Tobias Nießen) [#18098](https://github.com/nodejs/node/pull/18098)
- [[`9d3958102e`](https://github.com/nodejs/node/commit/9d3958102e)] - **(SEMVER-MAJOR)** **stream**: add custom inspect to BufferList (Ruben Bridgewater) [#17907](https://github.com/nodejs/node/pull/17907)
- [[`1e0f3315c7`](https://github.com/nodejs/node/commit/1e0f3315c7)] - **(SEMVER-MAJOR)** **stream**: always defer 'readable' with nextTick (Matteo Collina) [#17979](https://github.com/nodejs/node/pull/17979)
- [[`dd49778938`](https://github.com/nodejs/node/commit/dd49778938)] - **(SEMVER-MAJOR)** **test**: fix promise message test after V8 update (Michaël Zasso) [#19201](https://github.com/nodejs/node/pull/19201)
- [[`61f87837a9`](https://github.com/nodejs/node/commit/61f87837a9)] - **(SEMVER-MAJOR)** **test**: remove test for shared array buffers transfer (Malcolm White) [#19201](https://github.com/nodejs/node/pull/19201)
- [[`425c5ca27d`](https://github.com/nodejs/node/commit/425c5ca27d)] - **(SEMVER-MAJOR)** **test**: remove openssl -no_rand_screen opts (Shigeki Ohtsu) [#19794](https://github.com/nodejs/node/pull/19794)
- [[`3e0d40d4af`](https://github.com/nodejs/node/commit/3e0d40d4af)] - **(SEMVER-MAJOR)** **test**: add info option to common.expectsError (Joyee Cheung) [#19514](https://github.com/nodejs/node/pull/19514)
- [[`74553465e6`](https://github.com/nodejs/node/commit/74553465e6)] - **(SEMVER-MAJOR)** **test**: refactor test-cluster-send-deadlock (Luigi Pinca) [#19241](https://github.com/nodejs/node/pull/19241)
- [[`5c8937c3c6`](https://github.com/nodejs/node/commit/5c8937c3c6)] - **(SEMVER-MAJOR)** **test**: fix esm message tests after V8 update (Michaël Zasso) [#18453](https://github.com/nodejs/node/pull/18453)
- [[`bde8de8892`](https://github.com/nodejs/node/commit/bde8de8892)] - **(SEMVER-MAJOR)** **test**: update postmortem metadata test (cjihrig) [#18453](https://github.com/nodejs/node/pull/18453)
- [[`069dd10ca2`](https://github.com/nodejs/node/commit/069dd10ca2)] - **(SEMVER-MAJOR)** **test**: remove vulgar language (Ruben Bridgewater) [#18395](https://github.com/nodejs/node/pull/18395)
- [[`ac2af1361e`](https://github.com/nodejs/node/commit/ac2af1361e)] - **(SEMVER-MAJOR)** **test**: fix inspector test after V8 upgrade (Michaël Zasso) [#17489](https://github.com/nodejs/node/pull/17489)
- [[`4e51512148`](https://github.com/nodejs/node/commit/4e51512148)] - **(SEMVER-MAJOR)** **test**: update postmortem metadata test (cjihrig) [#17489](https://github.com/nodejs/node/pull/17489)
- [[`7809f386b0`](https://github.com/nodejs/node/commit/7809f386b0)] - **(SEMVER-MAJOR)** **test**: improve console tests (Ruben Bridgewater) [#17708](https://github.com/nodejs/node/pull/17708)
- [[`6ff52b69cc`](https://github.com/nodejs/node/commit/6ff52b69cc)] - **(SEMVER-MAJOR)** **test**: add standard console tests (wandalen) [#17708](https://github.com/nodejs/node/pull/17708)
- [[`1312db5651`](https://github.com/nodejs/node/commit/1312db5651)] - **(SEMVER-MAJOR)** **test**: test error messages from fs.realpath{Sync} (Joyee Cheung) [#17914](https://github.com/nodejs/node/pull/17914)
- [[`5eccbb09fa`](https://github.com/nodejs/node/commit/5eccbb09fa)] - **(SEMVER-MAJOR)** **test**: verify errors thrown from fs stat APIs (Joyee Cheung) [#17914](https://github.com/nodejs/node/pull/17914)
- [[`7939a5e708`](https://github.com/nodejs/node/commit/7939a5e708)] - **(SEMVER-MAJOR)** **test**: change test expectation for string decoder (Marja Hölttä) [#16271](https://github.com/nodejs/node/pull/16271)
- [[`60698c2455`](https://github.com/nodejs/node/commit/60698c2455)] - **(SEMVER-MAJOR)** **test**: apply eslint exceptions narrowly (Rich Trott) [#16393](https://github.com/nodejs/node/pull/16393)
- [[`47a984ada0`](https://github.com/nodejs/node/commit/47a984ada0)] - **(SEMVER-MAJOR)** **timers**: prevent event loop blocking (Anatoli Papirovski) [#18486](https://github.com/nodejs/node/pull/18486)
- [[`d7894f3969`](https://github.com/nodejs/node/commit/d7894f3969)] - **(SEMVER-MAJOR)** **timers**: use start instead of stop + start (Anatoli Papirovski) [#18486](https://github.com/nodejs/node/pull/18486)
- [[`71c0d0370a`](https://github.com/nodejs/node/commit/71c0d0370a)] - **(SEMVER-MAJOR)** **timers**: use const as appropriate (Anatoli Papirovski) [#18486](https://github.com/nodejs/node/pull/18486)
- [[`a986158cbf`](https://github.com/nodejs/node/commit/a986158cbf)] - **(SEMVER-MAJOR)** **timers**: re-enter C++ less frequently (Anatoli Papirovski) [#18486](https://github.com/nodejs/node/pull/18486)
- [[`9b8e1c2e4f`](https://github.com/nodejs/node/commit/9b8e1c2e4f)] - **(SEMVER-MAJOR)** **timers**: refactor error handling (Anatoli Papirovski) [#18486](https://github.com/nodejs/node/pull/18486)
- [[`68783ae0b8`](https://github.com/nodejs/node/commit/68783ae0b8)] - **(SEMVER-MAJOR)** **timers**: runtime-deprecate {un}enroll() (Jeremiah Senkpiel) [#18066](https://github.com/nodejs/node/pull/18066)
- [[`1385e1bc63`](https://github.com/nodejs/node/commit/1385e1bc63)] - **(SEMVER-MAJOR)** **timers**: setInterval interval includes cb duration (zhangzifa) [#14815](https://github.com/nodejs/node/pull/14815)
- [[`593941ac0b`](https://github.com/nodejs/node/commit/593941ac0b)] - **(SEMVER-MAJOR)** **timers**: extract enroll() validation into a fn (Jeremiah Senkpiel) [#17704](https://github.com/nodejs/node/pull/17704)
- [[`9204a0db6e`](https://github.com/nodejs/node/commit/9204a0db6e)] - **(SEMVER-MAJOR)** **tls**: runtime-deprecate tls.convertNPNProtocols() (Ben Noordhuis) [#19403](https://github.com/nodejs/node/pull/19403)
- [[`5bfbe5ceae`](https://github.com/nodejs/node/commit/5bfbe5ceae)] - **(SEMVER-MAJOR)** **tls**: drop NPN (next protocol negotiation) support (Ben Noordhuis) [#19403](https://github.com/nodejs/node/pull/19403)
- [[`eda702104b`](https://github.com/nodejs/node/commit/eda702104b)] - **(SEMVER-MAJOR)** **tls**: better error message for socket disconnect (Anna Henningsen) [#18989](https://github.com/nodejs/node/pull/18989)
- [[`1c29da8236`](https://github.com/nodejs/node/commit/1c29da8236)] - **(SEMVER-MAJOR)** **tls**: migrate C++ errors to internal/errors.js (Joyee Cheung) [#18125](https://github.com/nodejs/node/pull/18125)
- [[`9ffebeab48`](https://github.com/nodejs/node/commit/9ffebeab48)] - **(SEMVER-MAJOR)** **tls**: migrate argument type-checking errors (Joyee Cheung) [#18125](https://github.com/nodejs/node/pull/18125)
- [[`9301b8a9c6`](https://github.com/nodejs/node/commit/9301b8a9c6)] - **(SEMVER-MAJOR)** **tls**: make deprecated tls.createSecurePair() use public API (Anna Henningsen) [#17882](https://github.com/nodejs/node/pull/17882)
- [[`79261f3003`](https://github.com/nodejs/node/commit/79261f3003)] - **(SEMVER-MAJOR)** **tls**: migrate errors in \_tls_wrap.js (Mir Mufaqam Ali) [#17792](https://github.com/nodejs/node/pull/17792)
- [[`af78840b19`](https://github.com/nodejs/node/commit/af78840b19)] - **(SEMVER-MAJOR)** **tls**: set ecdhCurve default to 'auto' (Hativ) [#16853](https://github.com/nodejs/node/pull/16853)
- [[`7aa64b9fb9`](https://github.com/nodejs/node/commit/7aa64b9fb9)] - **(SEMVER-MAJOR)** **tools**: implement ninja build with --build-v8-with-gn (Yang Guo) [#19201](https://github.com/nodejs/node/pull/19201)
- [[`91a5ee1137`](https://github.com/nodejs/node/commit/91a5ee1137)] - **(SEMVER-MAJOR)** **tools**: fix make test-v8 (Michaël Zasso) [#19201](https://github.com/nodejs/node/pull/19201)
- [[`2b235830fb`](https://github.com/nodejs/node/commit/2b235830fb)] - **(SEMVER-MAJOR)** **tools**: install all header files OpenSSL-1.1.0 (Shigeki Ohtsu) [#19794](https://github.com/nodejs/node/pull/19794)
- [[`6a9f049968`](https://github.com/nodejs/node/commit/6a9f049968)] - **(SEMVER-MAJOR)** **tools,lib**: forbid native Error constructors (Michaël Zasso) [#19373](https://github.com/nodejs/node/pull/19373)
- [[`da5d818a54`](https://github.com/nodejs/node/commit/da5d818a54)] - **(SEMVER-MAJOR)** **trace_events**: adds a new trace_events api (James M Snell) [#19803](https://github.com/nodejs/node/pull/19803)
- [[`3d9d84940a`](https://github.com/nodejs/node/commit/3d9d84940a)] - **(SEMVER-MAJOR)** **tty**: convert to internal/errors using SystemError (James M Snell) [#16567](https://github.com/nodejs/node/pull/16567)
- [[`312414662b`](https://github.com/nodejs/node/commit/312414662b)] - **(SEMVER-MAJOR)** **url**: expose the WHATWG URL API globally (Michaël Zasso) [#18281](https://github.com/nodejs/node/pull/18281)
- [[`f848c60f64`](https://github.com/nodejs/node/commit/f848c60f64)] - **(SEMVER-MAJOR)** **util**: inspect arguments properly (Ruben Bridgewater) [#19467](https://github.com/nodejs/node/pull/19467)
- [[`be4950d58c`](https://github.com/nodejs/node/commit/be4950d58c)] - **(SEMVER-MAJOR)** **util**: add type check functions for BigInt arrays (Michaël Zasso) [#19201](https://github.com/nodejs/node/pull/19201)
- [[`1029dd3686`](https://github.com/nodejs/node/commit/1029dd3686)] - **(SEMVER-MAJOR)** **util**: show Weak(Set|Map) entries in inspect (Ruben Bridgewater) [#19259](https://github.com/nodejs/node/pull/19259)
- [[`0fbd4b1d02`](https://github.com/nodejs/node/commit/0fbd4b1d02)] - **(SEMVER-MAJOR)** **util**: improve iterator inspect output (Ruben Bridgewater) [#19259](https://github.com/nodejs/node/pull/19259)
- [[`8f153092d8`](https://github.com/nodejs/node/commit/8f153092d8)] - **(SEMVER-MAJOR)** **util**: change %o depth default (Ruben Bridgewater) [#17907](https://github.com/nodejs/node/pull/17907)
- [[`b994b8eff6`](https://github.com/nodejs/node/commit/b994b8eff6)] - **(SEMVER-MAJOR)** **util**: change util.inspect depth default (Ruben Bridgewater) [#17907](https://github.com/nodejs/node/pull/17907)
- [[`c64ca56def`](https://github.com/nodejs/node/commit/c64ca56def)] - **(SEMVER-MAJOR)** **util**: improve error message of \_errnoException (Weijia Wang) [#17626](https://github.com/nodejs/node/pull/17626)
- [[`31e0dbc0c7`](https://github.com/nodejs/node/commit/31e0dbc0c7)] - **(SEMVER-MAJOR)** **util**: use @@toStringTag (Gus Caplan) [#16956](https://github.com/nodejs/node/pull/16956)
- [[`617e3e96e6`](https://github.com/nodejs/node/commit/617e3e96e6)] - **(SEMVER-MAJOR)** **util**: runtime deprecation for custom .inspect() (Rich Trott) [#16393](https://github.com/nodejs/node/pull/16393)
- [[`07d39a2262`](https://github.com/nodejs/node/commit/07d39a2262)] - **(SEMVER-MAJOR)** **util**: emit deprecation code only once (Rich Trott) [#16393](https://github.com/nodejs/node/pull/16393)
- [[`34d988f122`](https://github.com/nodejs/node/commit/34d988f122)] - **(SEMVER-MAJOR)** **vm**: move options checks from C++ to JS (Michaël Zasso) [#19398](https://github.com/nodejs/node/pull/19398)
- [[`49b2969ef4`](https://github.com/nodejs/node/commit/49b2969ef4)] - **(SEMVER-MAJOR)** **vm**: migrate isContext to internal/errors (dustinnewman98) [#19268](https://github.com/nodejs/node/pull/19268)
- [[`da886d9a4c`](https://github.com/nodejs/node/commit/da886d9a4c)] - **(SEMVER-MAJOR)** **zlib**: improve zlib errors (Joyee Cheung) [#18675](https://github.com/nodejs/node/pull/18675)

#### Semver-minor

- [[`b3c1bd38f6`](https://github.com/nodejs/node/commit/b3c1bd38f6)] - **(SEMVER-MINOR)** **assert**: add direct promises support in rejects (Ruben Bridgewater) [#19885](https://github.com/nodejs/node/pull/19885)
- [[`599337f43e`](https://github.com/nodejs/node/commit/599337f43e)] - **(SEMVER-MINOR)** **assert**: add rejects() and doesNotReject() (feugy) [#18023](https://github.com/nodejs/node/pull/18023)
- [[`559e23a459`](https://github.com/nodejs/node/commit/559e23a459)] - **(SEMVER-MINOR)** **console**: auto-detect color support by default (Anna Henningsen) [#19372](https://github.com/nodejs/node/pull/19372)
- [[`3f1562dea8`](https://github.com/nodejs/node/commit/3f1562dea8)] - **(SEMVER-MINOR)** **console**: add color support (Anna Henningsen) [#19372](https://github.com/nodejs/node/pull/19372)
- [[`4fe51755ff`](https://github.com/nodejs/node/commit/4fe51755ff)] - **(SEMVER-MINOR)** **console**: allow `options` object as constructor arg (Anna Henningsen) [#19372](https://github.com/nodejs/node/pull/19372)
- [[`97ace04492`](https://github.com/nodejs/node/commit/97ace04492)] - **(SEMVER-MINOR)** **console**: add table method (Gus Caplan) [#18137](https://github.com/nodejs/node/pull/18137)
- [[`f2e02883e7`](https://github.com/nodejs/node/commit/f2e02883e7)] - **(SEMVER-MINOR)** **crypto**: add ECDH.convertKey to convert public keys (Wei-Wei Wu) [#19080](https://github.com/nodejs/node/pull/19080)
- [[`6e7992e8b8`](https://github.com/nodejs/node/commit/6e7992e8b8)] - **(SEMVER-MINOR)** **crypto**: docs-only deprecate crypto.fips, replace (James M Snell) [#18335](https://github.com/nodejs/node/pull/18335)
- [[`5303a509fb`](https://github.com/nodejs/node/commit/5303a509fb)] - **(SEMVER-MINOR)** **deps**: cherry-pick 39d546a from upstream V8 (Gus Caplan) [#20016](https://github.com/nodejs/node/pull/20016)
- [[`25a816dcda`](https://github.com/nodejs/node/commit/25a816dcda)] - **(SEMVER-MINOR)** **deps**: upgrade npm to 5.8.0 (FallenRiteMonk) [#19560](https://github.com/nodejs/node/pull/19560)
- [[`5bd9d68a45`](https://github.com/nodejs/node/commit/5bd9d68a45)] - **(SEMVER-MINOR)** **doc**: improve assert documentation (Ruben Bridgewater) [#19885](https://github.com/nodejs/node/pull/19885)
- [[`63565e1063`](https://github.com/nodejs/node/commit/63565e1063)] - **(SEMVER-MINOR)** **doc**: document `Console(…, ignoreErrors)` option (Anna Henningsen) [#19372](https://github.com/nodejs/node/pull/19372)
- [[`bd6e0be0df`](https://github.com/nodejs/node/commit/bd6e0be0df)] - **(SEMVER-MINOR)** **doc**: provide replacements for deprecated util methods (Anna Henningsen) [#18415](https://github.com/nodejs/node/pull/18415)
- [[`5b705cddcc`](https://github.com/nodejs/node/commit/5b705cddcc)] - **(SEMVER-MINOR)** **fs**: add 'close' event to FSWatcher (Alec Larson) [#19900](https://github.com/nodejs/node/pull/19900)
- [[`a16d88d9e9`](https://github.com/nodejs/node/commit/a16d88d9e9)] - **(SEMVER-MINOR)** **fs**: expose copy-on-write flags for fs.copyFile() (cjihrig) [#19759](https://github.com/nodejs/node/pull/19759)
- [[`329fc78e49`](https://github.com/nodejs/node/commit/329fc78e49)] - **(SEMVER-MINOR)** **fs**: add initial set of fs.promises APIs (James M Snell) [#18297](https://github.com/nodejs/node/pull/18297)
- [[`85b37db684`](https://github.com/nodejs/node/commit/85b37db684)] - **(SEMVER-MINOR)** **fs**: add FileHandle object fd wrapper (James M Snell) [#18297](https://github.com/nodejs/node/pull/18297)
- [[`7154bc097c`](https://github.com/nodejs/node/commit/7154bc097c)] - **(SEMVER-MINOR)** **fs**: add FSReqPromise (James M Snell) [#18297](https://github.com/nodejs/node/pull/18297)
- [[`cd7d7b15c1`](https://github.com/nodejs/node/commit/cd7d7b15c1)] - **(SEMVER-MINOR)** **n-api**: take n-api out of experimental (Michael Dawson) [#19262](https://github.com/nodejs/node/pull/19262)
- [[`009e41826f`](https://github.com/nodejs/node/commit/009e41826f)] - **(SEMVER-MINOR)** **perf_hooks**: make PerformanceObserver an AsyncResource (James M Snell) [#18789](https://github.com/nodejs/node/pull/18789)
- [[`9e509b622b`](https://github.com/nodejs/node/commit/9e509b622b)] - **(SEMVER-MINOR)** **perf_hooks**: emit trace events for marks, measures, and timerify (James M Snell) [#18789](https://github.com/nodejs/node/pull/18789)
- [[`aca8e764da`](https://github.com/nodejs/node/commit/aca8e764da)] - **(SEMVER-MINOR)** **perf_hooks**: eliminate deprecation warning (James M Snell) [#18789](https://github.com/nodejs/node/pull/18789)
- [[`cf4e6fd03f`](https://github.com/nodejs/node/commit/cf4e6fd03f)] - **(SEMVER-MINOR)** **process**: add version constants and compare (Gus Caplan) [#19587](https://github.com/nodejs/node/pull/19587)
- [[`982e3bdb1f`](https://github.com/nodejs/node/commit/982e3bdb1f)] - **(SEMVER-MINOR)** **process**: add more version properties to release (Gus Caplan) [#19438](https://github.com/nodejs/node/pull/19438)
- [[`446c1ecfda`](https://github.com/nodejs/node/commit/446c1ecfda)] - **(SEMVER-MINOR)** **src, tools**: add debug symbols for node internals (Matheus Marchini) [#14901](https://github.com/nodejs/node/pull/14901)
- [[`a5cf3feaf1`](https://github.com/nodejs/node/commit/a5cf3feaf1)] - **(SEMVER-MINOR)** **stream**: add pipeline and finished (Mathias Buus) [#19828](https://github.com/nodejs/node/pull/19828)
- [[`61b4d60c5d`](https://github.com/nodejs/node/commit/61b4d60c5d)] - **(SEMVER-MINOR)** **stream**: added experimental support for for-await (Matteo Collina) [#17755](https://github.com/nodejs/node/pull/17755)
- [[`c667c87528`](https://github.com/nodejs/node/commit/c667c87528)] - **(SEMVER-MINOR)** **tools**: add eslintrc rule for `assert.rejects` (Ruben Bridgewater) [#19885](https://github.com/nodejs/node/pull/19885)
- [[`4b733834fc`](https://github.com/nodejs/node/commit/4b733834fc)] - **(SEMVER-MINOR)** **util**: introduce types.isModuleNamespaceObject (Gus Caplan) [#20016](https://github.com/nodejs/node/pull/20016)
- [[`678f2c261a`](https://github.com/nodejs/node/commit/678f2c261a)] - **(SEMVER-MINOR)** **util**: introduce `formatWithOptions()` (Anna Henningsen) [#19372](https://github.com/nodejs/node/pull/19372)
- [[`b20af8088a`](https://github.com/nodejs/node/commit/b20af8088a)] - **(SEMVER-MINOR)** **util**: introduce `util.types.is[…]` type checks (Anna Henningsen) [#18415](https://github.com/nodejs/node/pull/18415)
- [[`39dc947409`](https://github.com/nodejs/node/commit/39dc947409)] - **(SEMVER-MINOR)** **util**: add bigint formatting to util.inspect (Gus Caplan) [#18412](https://github.com/nodejs/node/pull/18412)
- [[`cb5f358ee7`](https://github.com/nodejs/node/commit/cb5f358ee7)] - **(SEMVER-MINOR)** **vm**: add code generation options (Gus Caplan) [#19016](https://github.com/nodejs/node/pull/19016)
- [[`49fd9c63d2`](https://github.com/nodejs/node/commit/49fd9c63d2)] - **(SEMVER-MINOR)** **zlib**: use `.bytesWritten` instead of `.bytesRead` (Anna Henningsen) [#19414](https://github.com/nodejs/node/pull/19414)

#### Semver-patch

- [[`655ab65a90`](https://github.com/nodejs/node/commit/655ab65a90)] - **assert**: validate the block return type (Ruben Bridgewater) [#19886](https://github.com/nodejs/node/pull/19886)
- [[`e9a33da58c`](https://github.com/nodejs/node/commit/e9a33da58c)] - **assert**: fix actual & expected input (Ruben Bridgewater) [#19925](https://github.com/nodejs/node/pull/19925)
- [[`9c06770443`](https://github.com/nodejs/node/commit/9c06770443)] - **assert**: lazy load acorn (Ruben Bridgewater) [#19863](https://github.com/nodejs/node/pull/19863)
- [[`252eb2deb2`](https://github.com/nodejs/node/commit/252eb2deb2)] - **assert**: fix error message (Ruben Bridgewater) [#19865](https://github.com/nodejs/node/pull/19865)
- [[`fdb35d8960`](https://github.com/nodejs/node/commit/fdb35d8960)] - **assert**: ensure .rejects() disallows sync throws (Teddy Katz) [#19650](https://github.com/nodejs/node/pull/19650)
- [[`2e6dd93aaa`](https://github.com/nodejs/node/commit/2e6dd93aaa)] - **assert**: fix diff color output (Ruben Bridgewater) [#19464](https://github.com/nodejs/node/pull/19464)
- [[`a1c96f8e07`](https://github.com/nodejs/node/commit/a1c96f8e07)] - **assert**: improve assert.throws (Ruben Bridgewater) [#19463](https://github.com/nodejs/node/pull/19463)
- [[`5d6d1fedcf`](https://github.com/nodejs/node/commit/5d6d1fedcf)] - **assert**: add warning about `assert.doesNotReject` (Ruben Bridgewater) [#19462](https://github.com/nodejs/node/pull/19462)
- [[`3c61b87e59`](https://github.com/nodejs/node/commit/3c61b87e59)] - **assert**: improve assert()/assert.ok() performance (Brian White) [#19292](https://github.com/nodejs/node/pull/19292)
- [[`a27f48d619`](https://github.com/nodejs/node/commit/a27f48d619)] - **assert**: fix generatedMessage (Ruben Bridgewater) [#18322](https://github.com/nodejs/node/pull/18322)
- [[`3e910fb8f7`](https://github.com/nodejs/node/commit/3e910fb8f7)] - **assert**: do not read Node.js modules (Ruben Bridgewater) [#18322](https://github.com/nodejs/node/pull/18322)
- [[`8c46fa6903`](https://github.com/nodejs/node/commit/8c46fa6903)] - **async_hooks**: remove async_wrap from async_hooks.js (Daniel Bevenius) [#19368](https://github.com/nodejs/node/pull/19368)
- [[`e9ac80bb39`](https://github.com/nodejs/node/commit/e9ac80bb39)] - **async_hooks**: clean up usage in internal code (Anatoli Papirovski) [#18720](https://github.com/nodejs/node/pull/18720)
- [[`4d074343dd`](https://github.com/nodejs/node/commit/4d074343dd)] - **async_hooks,process**: remove internalNextTick (Anatoli Papirovski) [#19147](https://github.com/nodejs/node/pull/19147)
- [[`abc87862ff`](https://github.com/nodejs/node/commit/abc87862ff)] - **async_wrap**: fix use-after-free for inspector session (Anna Henningsen) [#19381](https://github.com/nodejs/node/pull/19381)
- [[`f572927147`](https://github.com/nodejs/node/commit/f572927147)] - **benchmark**: do not multiply n by 1e6 in arrays (Anatoli Papirovski) [#20125](https://github.com/nodejs/node/pull/20125)
- [[`b80da63b99`](https://github.com/nodejs/node/commit/b80da63b99)] - **benchmark**: changed millions and thousands to n (juggernaut451) [#18917](https://github.com/nodejs/node/pull/18917)
- [[`e136903700`](https://github.com/nodejs/node/commit/e136903700)] - **benchmark**: remove excessive value from http2 benchmark (Anna Henningsen) [#18936](https://github.com/nodejs/node/pull/18936)
- [[`d7994764fa`](https://github.com/nodejs/node/commit/d7994764fa)] - **buffer**: fix deprecation warning emit (Anatoli Papirovski) [#20163](https://github.com/nodejs/node/pull/20163)
- [[`cdacafc8bb`](https://github.com/nodejs/node/commit/cdacafc8bb)] - **buffer**: use a default offset (Ruben Bridgewater) [#19749](https://github.com/nodejs/node/pull/19749)
- [[`d6ce4ecb57`](https://github.com/nodejs/node/commit/d6ce4ecb57)] - **buffer**: do not emit deprecation notice on Buffer.of (Timothy Gu) [#19682](https://github.com/nodejs/node/pull/19682)
- [[`daef2e7fd7`](https://github.com/nodejs/node/commit/daef2e7fd7)] - **buffer**: removed unneeded FastBuffer constructor (Timothy Gu) [#19684](https://github.com/nodejs/node/pull/19684)
- [[`e5f8924064`](https://github.com/nodejs/node/commit/e5f8924064)] - **buffer**: reduce overhead of StringBytes::Encode for UCS2 (Joyee Cheung) [#19798](https://github.com/nodejs/node/pull/19798)
- [[`3d61e14704`](https://github.com/nodejs/node/commit/3d61e14704)] - **buffer**: shorten deprecation warning (Rich Trott) [#19741](https://github.com/nodejs/node/pull/19741)
- [[`f4e5f969ba`](https://github.com/nodejs/node/commit/f4e5f969ba)] - **buffer**: improve write(U)Int functions (Ruben Bridgewater) [#19289](https://github.com/nodejs/node/pull/19289)
- [[`b935e63710`](https://github.com/nodejs/node/commit/b935e63710)] - **build**: limit assembler version check on x86 (Shigeki Ohtsu) [#20226](https://github.com/nodejs/node/pull/20226)
- [[`adc3e8ad87`](https://github.com/nodejs/node/commit/adc3e8ad87)] - **build**: require --openssl-no-asm if old assembler (Rod Vagg) [#20226](https://github.com/nodejs/node/pull/20226)
- [[`160d2d5a9a`](https://github.com/nodejs/node/commit/160d2d5a9a)] - **build**: extract error() function in configure (Rod Vagg) [#20226](https://github.com/nodejs/node/pull/20226)
- [[`a4cba2d7a4`](https://github.com/nodejs/node/commit/a4cba2d7a4)] - **build**: normalise test.py calls to use PARALLEL_ARGS (Chris Miller) [#20124](https://github.com/nodejs/node/pull/20124)
- [[`f421fb33a7`](https://github.com/nodejs/node/commit/f421fb33a7)] - **build**: check without_ssl in warn openssl_no_asm (Daniel Bevenius) [#19934](https://github.com/nodejs/node/pull/19934)
- [[`8170f4f463`](https://github.com/nodejs/node/commit/8170f4f463)] - **build**: add support for IBM i platform (Jesse Gorzinski) [#19667](https://github.com/nodejs/node/pull/19667)
- [[`a972ed4d50`](https://github.com/nodejs/node/commit/a972ed4d50)] - **build**: allow vcbuild to merely build addon tests (Gabriel Schulhof) [#19637](https://github.com/nodejs/node/pull/19637)
- [[`c5928ab631`](https://github.com/nodejs/node/commit/c5928ab631)] - **build**: make lint-ci work properly on Linux make (Rod Vagg) [#19746](https://github.com/nodejs/node/pull/19746)
- [[`c6ae8a2810`](https://github.com/nodejs/node/commit/c6ae8a2810)] - **build**: disable V8 untrusted code mitigations (Michaël Zasso) [#19222](https://github.com/nodejs/node/pull/19222)
- [[`f05eaa4a53`](https://github.com/nodejs/node/commit/f05eaa4a53)] - **build**: lint .eslintrc.js file (Rich Trott) [#19122](https://github.com/nodejs/node/pull/19122)
- [[`b13233aa39`](https://github.com/nodejs/node/commit/b13233aa39)] - **build**: remove support for VS2015 (Nikolai Vavilov) [#16969](https://github.com/nodejs/node/pull/16969)
- [[`cd4766d1d3`](https://github.com/nodejs/node/commit/cd4766d1d3)] - **build, win**: opt-in openssl_no_asm if no nasm found (Shigeki Ohtsu) [#19943](https://github.com/nodejs/node/pull/19943)
- [[`57bd27eda8`](https://github.com/nodejs/node/commit/57bd27eda8)] - **_Revert_** "**build,test**: make building addon tests less fragile" (Rod Vagg) [#18287](https://github.com/nodejs/node/pull/18287)
- [[`d9b59def72`](https://github.com/nodejs/node/commit/d9b59def72)] - **build,test**: make building addon tests less fragile (Ben Noordhuis) [#17407](https://github.com/nodejs/node/pull/17407)
- [[`d5d024d6ec`](https://github.com/nodejs/node/commit/d5d024d6ec)] - **_Revert_** "**build,tools**: check freshness of doc addons" (Rod Vagg) [#18287](https://github.com/nodejs/node/pull/18287)
- [[`2cb9e2a6f7`](https://github.com/nodejs/node/commit/2cb9e2a6f7)] - **build,tools**: check freshness of doc addons (Ben Noordhuis) [#17407](https://github.com/nodejs/node/pull/17407)
- [[`53035b142b`](https://github.com/nodejs/node/commit/53035b142b)] - **build,windows**: make vcbuild fail if upload fails (Refael Ackermann)
- [[`4f68133568`](https://github.com/nodejs/node/commit/4f68133568)] - **console**: fix class inheritance regression (Anatoli Papirovski) [#20158](https://github.com/nodejs/node/pull/20158)
- [[`f274e6921f`](https://github.com/nodejs/node/commit/f274e6921f)] - **crypto**: fix explanation in CipherBase::SetAuthTag (Tobias Nießen) [#20197](https://github.com/nodejs/node/pull/20197)
- [[`2ac6658296`](https://github.com/nodejs/node/commit/2ac6658296)] - **crypto,doc**: fix unassignd deprecation codes (James M Snell) [#18492](https://github.com/nodejs/node/pull/18492)
- [[`ffd57cd7b2`](https://github.com/nodejs/node/commit/ffd57cd7b2)] - **deps**: upgrade to libuv 1.20.2 (cjihrig) [#20129](https://github.com/nodejs/node/pull/20129)
- [[`60eb95ad7d`](https://github.com/nodejs/node/commit/60eb95ad7d)] - **deps**: bump V8 embedder string (Myles Borins) [#20105](https://github.com/nodejs/node/pull/20105)
- [[`1f01112b6f`](https://github.com/nodejs/node/commit/1f01112b6f)] - **deps**: patch V8 to 6.6.346.24 (Myles Borins) [#19995](https://github.com/nodejs/node/pull/19995)
- [[`aa5ae9e91d`](https://github.com/nodejs/node/commit/aa5ae9e91d)] - **deps**: c-ares float, win ipv6 bad fec0 prefix (Rod Vagg) [#19939](https://github.com/nodejs/node/pull/19939)
- [[`dbc6163977`](https://github.com/nodejs/node/commit/dbc6163977)] - **deps**: c-ares float, manual ares_ssize_t definition (Rod Vagg) [#19939](https://github.com/nodejs/node/pull/19939)
- [[`b82f905a8b`](https://github.com/nodejs/node/commit/b82f905a8b)] - **deps**: upgrade to c-ares v1.14.0 (Rod Vagg) [#19939](https://github.com/nodejs/node/pull/19939)
- [[`b6aec1d00a`](https://github.com/nodejs/node/commit/b6aec1d00a)] - **deps**: cherry-pick b767cde1e7 from upstream V8 (Ben Noordhuis) [#19980](https://github.com/nodejs/node/pull/19980)
- [[`a6db6404ff`](https://github.com/nodejs/node/commit/a6db6404ff)] - **deps**: cherry-pick b767cde1e7 from upstream V8 (Ben Noordhuis) [#19710](https://github.com/nodejs/node/pull/19710)
- [[`e37effe4ce`](https://github.com/nodejs/node/commit/e37effe4ce)] - **_Revert_** "**deps**: upgrade npm to 5.8.0" (Anna Henningsen) [#19837](https://github.com/nodejs/node/pull/19837)
- [[`026f6b787a`](https://github.com/nodejs/node/commit/026f6b787a)] - **_Revert_** "**deps**: manually add 10.x support to npm" (Anna Henningsen) [#19837](https://github.com/nodejs/node/pull/19837)
- [[`55557babca`](https://github.com/nodejs/node/commit/55557babca)] - **deps**: manually add 10.x support to npm (Myles Borins) [#17777](https://github.com/nodejs/node/pull/17777)
- [[`ae2b5bcb7c`](https://github.com/nodejs/node/commit/ae2b5bcb7c)] - **deps**: upgrade libuv to 1.20.0 (cjihrig) [#19758](https://github.com/nodejs/node/pull/19758)
- [[`b22a189b43`](https://github.com/nodejs/node/commit/b22a189b43)] - **deps**: fix typo in openssl upgrading doc (Daniel Bevenius) [#19789](https://github.com/nodejs/node/pull/19789)
- [[`b3f23910a2`](https://github.com/nodejs/node/commit/b3f23910a2)] - **deps**: patch V8 to 6.5.254.43 (Myles Borins) [#19615](https://github.com/nodejs/node/pull/19615)
- [[`41193bcf2f`](https://github.com/nodejs/node/commit/41193bcf2f)] - **deps**: patch V8 to 6.5.254.41 (Myles Borins) [#19432](https://github.com/nodejs/node/pull/19432)
- [[`9c9324768f`](https://github.com/nodejs/node/commit/9c9324768f)] - **deps**: patch V8 to 6.5.254.40 (Myles Borins) [#19380](https://github.com/nodejs/node/pull/19380)
- [[`cac4da05ad`](https://github.com/nodejs/node/commit/cac4da05ad)] - **deps**: allow disabling V8 untrusted code mitigations (Michaël Zasso) [#19222](https://github.com/nodejs/node/pull/19222)
- [[`040dd244de`](https://github.com/nodejs/node/commit/040dd244de)] - **deps**: patch V8 to 6.5.254.38 (Myles Borins) [#19303](https://github.com/nodejs/node/pull/19303)
- [[`13cb056e4c`](https://github.com/nodejs/node/commit/13cb056e4c)] - **deps**: cherry-pick 46c4979e86 from upstream v8 (Ben Noordhuis) [#18920](https://github.com/nodejs/node/pull/18920)
- [[`81232320aa`](https://github.com/nodejs/node/commit/81232320aa)] - **deps**: patch V8 to 6.4.388.46 (Myles Borins) [#18827](https://github.com/nodejs/node/pull/18827)
- [[`36386dc4e3`](https://github.com/nodejs/node/commit/36386dc4e3)] - **deps**: patch V8 to 6.4.388.45 (Myles Borins) [#18751](https://github.com/nodejs/node/pull/18751)
- [[`b6000d8285`](https://github.com/nodejs/node/commit/b6000d8285)] - **deps**: patch V8 to 6.4.388.44 (Myles Borins) [#18687](https://github.com/nodejs/node/pull/18687)
- [[`d0e4d4e0a1`](https://github.com/nodejs/node/commit/d0e4d4e0a1)] - **deps**: patch V8 to 6.4.388.42 (Myles Borins) [#18578](https://github.com/nodejs/node/pull/18578)
- [[`1f7648272e`](https://github.com/nodejs/node/commit/1f7648272e)] - **deps**: patch V8 to 6.4.388.41 (Myles Borins) [#18522](https://github.com/nodejs/node/pull/18522)
- [[`70277d6170`](https://github.com/nodejs/node/commit/70277d6170)] - **deps**: V8: resolve remaining whitespace diff (Myles Borins) [#18366](https://github.com/nodejs/node/pull/18366)
- [[`cbd634947d`](https://github.com/nodejs/node/commit/cbd634947d)] - **deps**: manually add 10.x support to npm (Myles Borins) [#17777](https://github.com/nodejs/node/pull/17777)
- [[`d3b1c971bc`](https://github.com/nodejs/node/commit/d3b1c971bc)] - **deps**: upgrade npm to 5.6.0 (Kat Marchán) [#17777](https://github.com/nodejs/node/pull/17777)
- [[`b5d415311b`](https://github.com/nodejs/node/commit/b5d415311b)] - **deps**: patch V8 to 6.3.292.48 (Myles Borins) [#17773](https://github.com/nodejs/node/pull/17773)
- [[`e01a210c7f`](https://github.com/nodejs/node/commit/e01a210c7f)] - **deps**: cherry-pick 37a3a15c3 from V8 upstream (Franziska Hinkelmann) [#16294](https://github.com/nodejs/node/pull/16294)
- [[`e38570fe56`](https://github.com/nodejs/node/commit/e38570fe56)] - **deps**: import acorn@5.2.1 (Timothy Gu) [#15566](https://github.com/nodejs/node/pull/15566)
- [[`4c6a47f7d7`](https://github.com/nodejs/node/commit/4c6a47f7d7)] - **doc**: add parameters for Http2Session:error event (Ujjwal Sharma) [#20206](https://github.com/nodejs/node/pull/20206)
- [[`b7d1e19e30`](https://github.com/nodejs/node/commit/b7d1e19e30)] - **doc**: update trace events categories description (Beni von Cheni) [#20092](https://github.com/nodejs/node/pull/20092)
- [[`4125a9f8de`](https://github.com/nodejs/node/commit/4125a9f8de)] - **doc**: fix incorrect net listen signature (Anatoli Papirovski) [#20209](https://github.com/nodejs/node/pull/20209)
- [[`8ff73aa82d`](https://github.com/nodejs/node/commit/8ff73aa82d)] - **doc**: modify net.Server.listen arg list (musgravejw) [#20142](https://github.com/nodejs/node/pull/20142)
- [[`a4975cab41`](https://github.com/nodejs/node/commit/a4975cab41)] - **doc**: detail CI sub-tasks rerunning (Vse Mozhet Byt) [#20200](https://github.com/nodejs/node/pull/20200)
- [[`3d7605561f`](https://github.com/nodejs/node/commit/3d7605561f)] - **doc**: remove "For example" expression in N-API doc (Gabriel Schulhof) [#20187](https://github.com/nodejs/node/pull/20187)
- [[`0d56982e56`](https://github.com/nodejs/node/commit/0d56982e56)] - **doc**: fix a typo in console documentation (Mykola Bilochub) [#20176](https://github.com/nodejs/node/pull/20176)
- [[`9214d64760`](https://github.com/nodejs/node/commit/9214d64760)] - **doc**: Uint8Array support in Buffer functions (SheetJS) [#19949](https://github.com/nodejs/node/pull/19949)
- [[`9495d9477b`](https://github.com/nodejs/node/commit/9495d9477b)] - **doc**: wrap buffer.md at 80 characters (Rich Trott) [#19546](https://github.com/nodejs/node/pull/19546)
- [[`6e05a96125`](https://github.com/nodejs/node/commit/6e05a96125)] - **doc**: add flags section to document all flags (Indranil Dasgupta) [#20042](https://github.com/nodejs/node/pull/20042)
- [[`0b7e626fed`](https://github.com/nodejs/node/commit/0b7e626fed)] - **doc**: fix inconsistency in documentation for building (Spencer Greene) [#20091](https://github.com/nodejs/node/pull/20091)
- [[`193d808c25`](https://github.com/nodejs/node/commit/193d808c25)] - **doc**: improve buf.write() text in buffer.md (Rich Trott) [#20115](https://github.com/nodejs/node/pull/20115)
- [[`9566603f35`](https://github.com/nodejs/node/commit/9566603f35)] - **doc**: add hiding comments note to contributor guide (Vse Mozhet Byt) [#20149](https://github.com/nodejs/node/pull/20149)
- [[`5c1580c99d`](https://github.com/nodejs/node/commit/5c1580c99d)] - **doc**: add myself to list of TSC members (Timothy Gu) [#20132](https://github.com/nodejs/node/pull/20132)
- [[`56d6e82b0a`](https://github.com/nodejs/node/commit/56d6e82b0a)] - **doc**: fully document --experimental-repl-await (Timothy Gu) [#20133](https://github.com/nodejs/node/pull/20133)
- [[`c31f0d0ba2`](https://github.com/nodejs/node/commit/c31f0d0ba2)] - **doc**: fix misplaced entries in test/common doc (Rich Trott) [#20117](https://github.com/nodejs/node/pull/20117)
- [[`c798adcc1c`](https://github.com/nodejs/node/commit/c798adcc1c)] - **doc**: move mikeal to Collaborator Emeriti list (Rich Trott) [#20113](https://github.com/nodejs/node/pull/20113)
- [[`793bf211d7`](https://github.com/nodejs/node/commit/793bf211d7)] - **doc**: adjust slightly awkward wording in buffer.md (Rich Trott) [#20037](https://github.com/nodejs/node/pull/20037)
- [[`efda6fbce6`](https://github.com/nodejs/node/commit/efda6fbce6)] - **doc**: update links and names for DevTools Protocol (Vse Mozhet Byt) [#20111](https://github.com/nodejs/node/pull/20111)
- [[`ed45a8b0cc`](https://github.com/nodejs/node/commit/ed45a8b0cc)] - **doc**: prevent one more false-positive linkification (Vse Mozhet Byt) [#20087](https://github.com/nodejs/node/pull/20087)
- [[`b6fa3ae41e`](https://github.com/nodejs/node/commit/b6fa3ae41e)] - **doc**: fix suspicious heading emphasis in n-api.md (Vse Mozhet Byt) [#20086](https://github.com/nodejs/node/pull/20086)
- [[`0a99cb1a3d`](https://github.com/nodejs/node/commit/0a99cb1a3d)] - **doc**: add ryzokuken to collaborators (Ujjwal Sharma) [#20081](https://github.com/nodejs/node/pull/20081)
- [[`fc0ddaa114`](https://github.com/nodejs/node/commit/fc0ddaa114)] - **doc**: fix two sorting nits in fs.md (Vse Mozhet Byt) [#20078](https://github.com/nodejs/node/pull/20078)
- [[`eca96f57fd`](https://github.com/nodejs/node/commit/eca96f57fd)] - **doc**: add tools/doc/README link in doc/STYLE_GUIDE (Vse Mozhet Byt) [#20071](https://github.com/nodejs/node/pull/20071)
- [[`27e6fd3983`](https://github.com/nodejs/node/commit/27e6fd3983)] - **doc**: unify and compact some fragments in fs.md (Vse Mozhet Byt) [#20050](https://github.com/nodejs/node/pull/20050)
- [[`a93a0ec9cf`](https://github.com/nodejs/node/commit/a93a0ec9cf)] - **doc**: update tools/doc/README.md (Vse Mozhet Byt) [#20047](https://github.com/nodejs/node/pull/20047)
- [[`ae327d6d1e`](https://github.com/nodejs/node/commit/ae327d6d1e)] - **doc**: unify more headings (Vse Mozhet Byt) [#20046](https://github.com/nodejs/node/pull/20046)
- [[`6d1c3e5ffc`](https://github.com/nodejs/node/commit/6d1c3e5ffc)] - **doc**: clarify url doc (James M Snell) [#19899](https://github.com/nodejs/node/pull/19899)
- [[`faf563e6a1`](https://github.com/nodejs/node/commit/faf563e6a1)] - **doc**: unify format of iterables (Vse Mozhet Byt) [#20036](https://github.com/nodejs/node/pull/20036)
- [[`5008c5a273`](https://github.com/nodejs/node/commit/5008c5a273)] - **doc**: improved flow for macOS firewall script (Joseph Gordon) [#18689](https://github.com/nodejs/node/pull/18689)
- [[`7248171e4c`](https://github.com/nodejs/node/commit/7248171e4c)] - **doc**: unify section structures (Vse Mozhet Byt) [#20028](https://github.com/nodejs/node/pull/20028)
- [[`98008dc6a0`](https://github.com/nodejs/node/commit/98008dc6a0)] - **doc**: close event does not take arguments (Indranil Dasgupta) [#20031](https://github.com/nodejs/node/pull/20031)
- [[`b806b04688`](https://github.com/nodejs/node/commit/b806b04688)] - **doc**: include error code in buffer documentation (Rich Trott) [#19982](https://github.com/nodejs/node/pull/19982)
- [[`846f4e1c9f`](https://github.com/nodejs/node/commit/846f4e1c9f)] - **doc**: add missing type=misc top comments (Vse Mozhet Byt) [#20022](https://github.com/nodejs/node/pull/20022)
- [[`86c1f19a8c`](https://github.com/nodejs/node/commit/86c1f19a8c)] - **doc**: add missing YAML keyword in v8.md metadata (Vse Mozhet Byt) [#20023](https://github.com/nodejs/node/pull/20023)
- [[`cb2e78aca3`](https://github.com/nodejs/node/commit/cb2e78aca3)] - **doc**: remove \_writableState reference (Anatoli Papirovski) [#20004](https://github.com/nodejs/node/pull/20004)
- [[`e635723157`](https://github.com/nodejs/node/commit/e635723157)] - **doc**: add net socket write signature (Gurin, Sebastian) [#19967](https://github.com/nodejs/node/pull/19967)
- [[`ba438fe592`](https://github.com/nodejs/node/commit/ba438fe592)] - **doc**: improve http.setHeader and getHeader typeinfo (Gerhard Stoebich) [#19902](https://github.com/nodejs/node/pull/19902)
- [[`fbf9e0609b`](https://github.com/nodejs/node/commit/fbf9e0609b)] - **doc**: fix wrong response.end() at request.socket (ikasumiwt) [#19507](https://github.com/nodejs/node/pull/19507)
- [[`15e8bdf95c`](https://github.com/nodejs/node/commit/15e8bdf95c)] - **doc**: fix typo in README (Tobias Nießen) [#20011](https://github.com/nodejs/node/pull/20011)
- [[`0d1b77eeb2`](https://github.com/nodejs/node/commit/0d1b77eeb2)] - **doc**: mention CCM along with GCM in crypto APIs (Tobias Nießen) [#19945](https://github.com/nodejs/node/pull/19945)
- [[`fc17e2dcb3`](https://github.com/nodejs/node/commit/fc17e2dcb3)] - **doc**: add pronouns for ofrobots (Ali Ijaz Sheikh) [#19992](https://github.com/nodejs/node/pull/19992)
- [[`4d7bbe8ad2`](https://github.com/nodejs/node/commit/4d7bbe8ad2)] - **doc**: move trevnorris to TSC Emeritus (Trevor Norris) [#19985](https://github.com/nodejs/node/pull/19985)
- [[`cdc1171af3`](https://github.com/nodejs/node/commit/cdc1171af3)] - **doc**: fix errors in sample code comments (Rich Trott) [#19963](https://github.com/nodejs/node/pull/19963)
- [[`90fc496da4`](https://github.com/nodejs/node/commit/90fc496da4)] - **doc**: fix punctuation and wrapping in buffer.md (Rich Trott) [#19964](https://github.com/nodejs/node/pull/19964)
- [[`c29f2f26c8`](https://github.com/nodejs/node/commit/c29f2f26c8)] - **doc**: added ready events to fs/streams,net/socket (Matei Copot) [#19968](https://github.com/nodejs/node/pull/19968)
- [[`4766f51823`](https://github.com/nodejs/node/commit/4766f51823)] - **doc**: remove superfluous word from crypto doc (Tobias Nießen) [#19946](https://github.com/nodejs/node/pull/19946)
- [[`105980f6e4`](https://github.com/nodejs/node/commit/105980f6e4)] - **doc**: fix parameter type format (Vse Mozhet Byt) [#19957](https://github.com/nodejs/node/pull/19957)
- [[`a8533cf543`](https://github.com/nodejs/node/commit/a8533cf543)] - **doc**: add quotes for event names + fix similar nits (Vse Mozhet Byt) [#19915](https://github.com/nodejs/node/pull/19915)
- [[`a60e4989cb`](https://github.com/nodejs/node/commit/a60e4989cb)] - **doc**: `vm.runIn*Context` can accept a string as options (Gerhard Stoebich) [#19910](https://github.com/nodejs/node/pull/19910)
- [[`0a553d56b6`](https://github.com/nodejs/node/commit/0a553d56b6)] - **doc**: improve buf.lastIndexOf() text (Rich Trott) [#19904](https://github.com/nodejs/node/pull/19904)
- [[`31b5ed49e0`](https://github.com/nodejs/node/commit/31b5ed49e0)] - **doc**: add and unify even more return values (Vse Mozhet Byt) [#19955](https://github.com/nodejs/node/pull/19955)
- [[`0be14def2c`](https://github.com/nodejs/node/commit/0be14def2c)] - **doc**: replace unneeded snake cases (Vse Mozhet Byt) [#19951](https://github.com/nodejs/node/pull/19951)
- [[`4c70616c7b`](https://github.com/nodejs/node/commit/4c70616c7b)] - **doc**: move evanlucas to TSC Emeritus (Evan Lucas) [#19953](https://github.com/nodejs/node/pull/19953)
- [[`7d2814e790`](https://github.com/nodejs/node/commit/7d2814e790)] - **doc**: unify End-of-Life marker (Tobias Nießen) [#19942](https://github.com/nodejs/node/pull/19942)
- [[`e590cfceed`](https://github.com/nodejs/node/commit/e590cfceed)] - **doc**: add missing backticks around code fragments. (Vse Mozhet Byt) [#19938](https://github.com/nodejs/node/pull/19938)
- [[`645516cd43`](https://github.com/nodejs/node/commit/645516cd43)] - **doc**: add Http2Session.connecting property (Pieter Mees) [#19842](https://github.com/nodejs/node/pull/19842)
- [[`5e6817261c`](https://github.com/nodejs/node/commit/5e6817261c)] - **doc**: prevent a false-positive linkification (Vse Mozhet Byt) [#19913](https://github.com/nodejs/node/pull/19913)
- [[`87880466b1`](https://github.com/nodejs/node/commit/87880466b1)] - **doc**: fix about `decodeStrings` property of `stream.Writable` (Ryusei Yamaguchi) [#19752](https://github.com/nodejs/node/pull/19752)
- [[`28e5c462d4`](https://github.com/nodejs/node/commit/28e5c462d4)] - **doc**: improve buf.indexOf() documentation style (Rich Trott) [#19861](https://github.com/nodejs/node/pull/19861)
- [[`38c97f5dc7`](https://github.com/nodejs/node/commit/38c97f5dc7)] - **doc**: fix punctuation in doc/releases.md (erwinwahyura) [#19774](https://github.com/nodejs/node/pull/19774)
- [[`51c2c51029`](https://github.com/nodejs/node/commit/51c2c51029)] - **doc**: explain edge case when assigning port to url (nodeav) [#19645](https://github.com/nodejs/node/pull/19645)
- [[`99c77dc018`](https://github.com/nodejs/node/commit/99c77dc018)] - **doc**: improve CCM example (Tobias Nießen) [#19851](https://github.com/nodejs/node/pull/19851)
- [[`dff214153f`](https://github.com/nodejs/node/commit/dff214153f)] - **doc**: specify definite Array types (Vse Mozhet Byt) [#19895](https://github.com/nodejs/node/pull/19895)
- [[`321c178faa`](https://github.com/nodejs/node/commit/321c178faa)] - **doc**: add missing quotes in default string values (Vse Mozhet Byt) [#19894](https://github.com/nodejs/node/pull/19894)
- [[`0cd8359652`](https://github.com/nodejs/node/commit/0cd8359652)] - **doc**: remove wrong default value in buffer.md (Vse Mozhet Byt) [#19883](https://github.com/nodejs/node/pull/19883)
- [[`0bd3da15a0`](https://github.com/nodejs/node/commit/0bd3da15a0)] - **doc**: add and unify return statements in crypto.md (Vse Mozhet Byt) [#19853](https://github.com/nodejs/node/pull/19853)
- [[`08a36a0666`](https://github.com/nodejs/node/commit/08a36a0666)] - **doc**: unify property sections (Vse Mozhet Byt) [#19869](https://github.com/nodejs/node/pull/19869)
- [[`0a679327be`](https://github.com/nodejs/node/commit/0a679327be)] - **doc**: update language regarding key stretching (Ujjwal Sharma) [#19810](https://github.com/nodejs/node/pull/19810)
- [[`0ac6ced2e9`](https://github.com/nodejs/node/commit/0ac6ced2e9)] - **doc**: fix some links (Vse Mozhet Byt) [#19860](https://github.com/nodejs/node/pull/19860)
- [[`4545cc17b9`](https://github.com/nodejs/node/commit/4545cc17b9)] - **doc**: improve buf.fill() documentation (Rich Trott) [#19846](https://github.com/nodejs/node/pull/19846)
- [[`0c55abf5d1`](https://github.com/nodejs/node/commit/0c55abf5d1)] - **doc**: added missing reference to test coverage info (Mithun Sasidharan) [#19825](https://github.com/nodejs/node/pull/19825)
- [[`53aaa55a3a`](https://github.com/nodejs/node/commit/53aaa55a3a)] - **doc**: clarify lifecycle of domain sockets (Gireesh Punathil) [#19471](https://github.com/nodejs/node/pull/19471)
- [[`dca09a77d5`](https://github.com/nodejs/node/commit/dca09a77d5)] - **doc**: update AUTHORS list (Michaël Zasso) [#19768](https://github.com/nodejs/node/pull/19768)
- [[`617946779c`](https://github.com/nodejs/node/commit/617946779c)] - **doc**: improve prepositions in buffer.md (Rich Trott) [#19817](https://github.com/nodejs/node/pull/19817)
- [[`3db0d62c68`](https://github.com/nodejs/node/commit/3db0d62c68)] - **doc**: reword poolSize explanation in buffer.md (Rich Trott) [#19785](https://github.com/nodejs/node/pull/19785)
- [[`8b1db6df80`](https://github.com/nodejs/node/commit/8b1db6df80)] - **doc**: add instructions to update local git config (Trivikram Kamat) [#19777](https://github.com/nodejs/node/pull/19777)
- [[`f02e4b90a2`](https://github.com/nodejs/node/commit/f02e4b90a2)] - **doc**: create list for commonly edited files in PRs (Trivikram Kamat) [#19776](https://github.com/nodejs/node/pull/19776)
- [[`422ac61535`](https://github.com/nodejs/node/commit/422ac61535)] - **doc**: remove link to "breaking changes" wiki (Trivikram Kamat) [#19795](https://github.com/nodejs/node/pull/19795)
- [[`acc328ef58`](https://github.com/nodejs/node/commit/acc328ef58)] - **doc**: move mafintosh to Collaborators (Rich Trott) [#19806](https://github.com/nodejs/node/pull/19806)
- [[`3567ea034e`](https://github.com/nodejs/node/commit/3567ea034e)] - **doc**: fix added value for `assert` module (Ruben Bridgewater) [#19724](https://github.com/nodejs/node/pull/19724)
- [[`5bdd6a7b9e`](https://github.com/nodejs/node/commit/5bdd6a7b9e)] - **doc**: properly document AssertionError (Ruben Bridgewater) [#19724](https://github.com/nodejs/node/pull/19724)
- [[`9125479be9`](https://github.com/nodejs/node/commit/9125479be9)] - **doc**: add `http2` to performanceEntry.entryType (Yuta Hiroto) [#19584](https://github.com/nodejs/node/pull/19584)
- [[`54fbbb1037`](https://github.com/nodejs/node/commit/54fbbb1037)] - **doc**: add metadata for vm code generation options (TomCoded) [#19440](https://github.com/nodejs/node/pull/19440)
- [[`d1720bddf4`](https://github.com/nodejs/node/commit/d1720bddf4)] - **doc**: fix linting issue in process.md (Vse Mozhet Byt) [#19542](https://github.com/nodejs/node/pull/19542)
- [[`3662934b5a`](https://github.com/nodejs/node/commit/3662934b5a)] - **doc**: fix paragraph order in stream.md (Vse Mozhet Byt) [#19501](https://github.com/nodejs/node/pull/19501)
- [[`45c86e33e1`](https://github.com/nodejs/node/commit/45c86e33e1)] - **doc**: add note to readable stream async iterator (Ivan Filenko) [#19331](https://github.com/nodejs/node/pull/19331)
- [[`9a70b27254`](https://github.com/nodejs/node/commit/9a70b27254)] - **doc**: fix punctuation issue in async_hooks.md (Rich Trott) [#19364](https://github.com/nodejs/node/pull/19364)
- [[`8d336dd8b1`](https://github.com/nodejs/node/commit/8d336dd8b1)] - **doc**: improve text in async_hooks.md (Rich Trott) [#19312](https://github.com/nodejs/node/pull/19312)
- [[`a2c0fcc0d8`](https://github.com/nodejs/node/commit/a2c0fcc0d8)] - **doc**: add returned values and options to stream.md (Ivan Filenko) [#19361](https://github.com/nodejs/node/pull/19361)
- [[`603afe25c8`](https://github.com/nodejs/node/commit/603afe25c8)] - **doc**: fix some recent nits in assert.md (Vse Mozhet Byt) [#19284](https://github.com/nodejs/node/pull/19284)
- [[`0eec0735d0`](https://github.com/nodejs/node/commit/0eec0735d0)] - **doc**: update internal errors documentation (Michaël Zasso) [#19203](https://github.com/nodejs/node/pull/19203)
- [[`1a5ec837ca`](https://github.com/nodejs/node/commit/1a5ec837ca)] - **doc**: fix max length on stream.md (Matteo Collina) [#19169](https://github.com/nodejs/node/pull/19169)
- [[`35c7238bb7`](https://github.com/nodejs/node/commit/35c7238bb7)] - **doc**: replace to Node.js (Yuta Hiroto) [#19056](https://github.com/nodejs/node/pull/19056)
- [[`e6b823d84a`](https://github.com/nodejs/node/commit/e6b823d84a)] - **doc**: remove redundant `the` (Leko) [#19008](https://github.com/nodejs/node/pull/19008)
- [[`a29089d7c8`](https://github.com/nodejs/node/commit/a29089d7c8)] - **doc**: add new documentation lint rule (estrada9166) [#18726](https://github.com/nodejs/node/pull/18726)
- [[`1bd32087ee`](https://github.com/nodejs/node/commit/1bd32087ee)] - **doc**: fix deprecation number (Ruben Bridgewater) [#18818](https://github.com/nodejs/node/pull/18818)
- [[`80ac941407`](https://github.com/nodejs/node/commit/80ac941407)] - **doc**: make linter happy (Anna Henningsen) [#18769](https://github.com/nodejs/node/pull/18769)
- [[`dbd1d1d43f`](https://github.com/nodejs/node/commit/dbd1d1d43f)] - **doc**: fix arg definition in fs (Anatoli Papirovski) [#18678](https://github.com/nodejs/node/pull/18678)
- [[`ac829f0135`](https://github.com/nodejs/node/commit/ac829f0135)] - **doc**: add missing URL types in fs promise API (Vse Mozhet Byt) [#18599](https://github.com/nodejs/node/pull/18599)
- [[`05e702d9f1`](https://github.com/nodejs/node/commit/05e702d9f1)] - **doc**: fix `REPLACEME` in changelog PR URLs (Anna Henningsen) [#18561](https://github.com/nodejs/node/pull/18561)
- [[`359a232348`](https://github.com/nodejs/node/commit/359a232348)] - **doc**: fix typo in esm.md (Rich Trott) [#18142](https://github.com/nodejs/node/pull/18142)
- [[`6c76de13c5`](https://github.com/nodejs/node/commit/6c76de13c5)] - **doc**: add missing link references (Vse Mozhet Byt) [#18222](https://github.com/nodejs/node/pull/18222)
- [[`4d1baf82ae`](https://github.com/nodejs/node/commit/4d1baf82ae)] - **doc**: fix links in errors.md (Vse Mozhet Byt) [#17829](https://github.com/nodejs/node/pull/17829)
- [[`3b9803838c`](https://github.com/nodejs/node/commit/3b9803838c)] - **doc**: clarify util.inspect usage intent (Gus Caplan) [#17375](https://github.com/nodejs/node/pull/17375)
- [[`70f23ec9c0`](https://github.com/nodejs/node/commit/70f23ec9c0)] - **doc**: fix typo in Buffer.prototype.fill() (cjihrig) [#17501](https://github.com/nodejs/node/pull/17501)
- [[`c60c93cba2`](https://github.com/nodejs/node/commit/c60c93cba2)] - **doc, http2**: add sections for server.close() (Chris Miller) [#19802](https://github.com/nodejs/node/pull/19802)
- [[`04491db1d3`](https://github.com/nodejs/node/commit/04491db1d3)] - **doc, src**: sort + fill up cli options and env vars (willhayslett) [#19878](https://github.com/nodejs/node/pull/19878)
- [[`f600e95ff0`](https://github.com/nodejs/node/commit/f600e95ff0)] - **doc, tools**: make type parsing more strict (Vse Mozhet Byt) [#19881](https://github.com/nodejs/node/pull/19881)
- [[`82a7347050`](https://github.com/nodejs/node/commit/82a7347050)] - **doc,assert,timers**: assign deprecation codes (Anna Henningsen) [#18564](https://github.com/nodejs/node/pull/18564)
- [[`0799b60f50`](https://github.com/nodejs/node/commit/0799b60f50)] - **doc,http2**: add parameters for Http2Session:connect event (Ujjwal Sharma) [#20193](https://github.com/nodejs/node/pull/20193)
- [[`237cbe10fb`](https://github.com/nodejs/node/commit/237cbe10fb)] - **doc,tools**: formalize, unify, codify default values (Vse Mozhet Byt) [#19737](https://github.com/nodejs/node/pull/19737)
- [[`cf46ca76ff`](https://github.com/nodejs/node/commit/cf46ca76ff)] - **domain**: converted anonymous to named function (Daven Casia) [#20021](https://github.com/nodejs/node/pull/20021)
- [[`f086354d3b`](https://github.com/nodejs/node/commit/f086354d3b)] - **errors**: alter ERR_HTTP2_PSEUDOHEADER_NOT_ALLOWED (davidmarkclements) [#19958](https://github.com/nodejs/node/pull/19958)
- [[`b6fbe16b41`](https://github.com/nodejs/node/commit/b6fbe16b41)] - **errors**: alter ERR_HTTP2_INVALID_CONNECTION_HEADERS (davidmarkclements) [#19807](https://github.com/nodejs/node/pull/19807)
- [[`2a3a66afb3`](https://github.com/nodejs/node/commit/2a3a66afb3)] - **errors**: pass missing `message` parameter to `internalAssert` (Ayush Gupta) [#19908](https://github.com/nodejs/node/pull/19908)
- [[`ef07d6570f`](https://github.com/nodejs/node/commit/ef07d6570f)] - **errors**: change ERR_HTTP2_HEADER_SINGLE_VALUE to TypeError (davidmarkclements) [#19805](https://github.com/nodejs/node/pull/19805)
- [[`add1c02bda`](https://github.com/nodejs/node/commit/add1c02bda)] - **errors**: alter ERR_HTTP2_INVALID_PSEUDOHEADER (davidmarkclements) [#19808](https://github.com/nodejs/node/pull/19808)
- [[`f8b3774d85`](https://github.com/nodejs/node/commit/f8b3774d85)] - **errors**: fix typo in internal/errors.js (davidmarkclements) [#19800](https://github.com/nodejs/node/pull/19800)
- [[`22da2f731d`](https://github.com/nodejs/node/commit/22da2f731d)] - **errors**: make message non-enumerable (Ruben Bridgewater) [#19719](https://github.com/nodejs/node/pull/19719)
- [[`95bae85809`](https://github.com/nodejs/node/commit/95bae85809)] - **errors**: simplify sysError (Ruben Bridgewater) [#18857](https://github.com/nodejs/node/pull/18857)
- [[`65e62e5665`](https://github.com/nodejs/node/commit/65e62e5665)] - **fs**: return stats to JS in sync methods (Joyee Cheung) [#20167](https://github.com/nodejs/node/pull/20167)
- [[`e3579a007f`](https://github.com/nodejs/node/commit/e3579a007f)] - **fs**: handle long files reading in fs.promises (Antoine du HAMEL) [#19643](https://github.com/nodejs/node/pull/19643)
- [[`d7b162cfa0`](https://github.com/nodejs/node/commit/d7b162cfa0)] - **fs**: complete error message for validate function (buji) [#19909](https://github.com/nodejs/node/pull/19909)
- [[`6e2d5af0e4`](https://github.com/nodejs/node/commit/6e2d5af0e4)] - **fs**: fix missing 'error' event in (Read|Write)Stream#destroy (Kohei Hiraga) [#19735](https://github.com/nodejs/node/pull/19735)
- [[`38a692963f`](https://github.com/nodejs/node/commit/38a692963f)] - **fs**: make ReadStream throw TypeError on NaN (Ujjwal Sharma) [#19775](https://github.com/nodejs/node/pull/19775)
- [[`f7049a2006`](https://github.com/nodejs/node/commit/f7049a2006)] - **fs**: refactor stats array to be more generic (Joyee Cheung) [#19714](https://github.com/nodejs/node/pull/19714)
- [[`e06ad5faf9`](https://github.com/nodejs/node/commit/e06ad5faf9)] - **fs**: use encoding in readFile (Benjamin Gruenbaum) [#19296](https://github.com/nodejs/node/pull/19296)
- [[`897cec43c6`](https://github.com/nodejs/node/commit/897cec43c6)] - **fs**: fix memory leak in WriteString (Joyee Cheung) [#19357](https://github.com/nodejs/node/pull/19357)
- [[`f96bd54dd5`](https://github.com/nodejs/node/commit/f96bd54dd5)] - **fs**: simplify FSReqBase slightly (Anna Henningsen) [#19174](https://github.com/nodejs/node/pull/19174)
- [[`523d44a66e`](https://github.com/nodejs/node/commit/523d44a66e)] - **fs**: replace duplicate conditions by function (Sergey Golovin) [#18717](https://github.com/nodejs/node/pull/18717)
- [[`96b2d8d3dc`](https://github.com/nodejs/node/commit/96b2d8d3dc)] - **fs**: check for symlink support in fs-promises test (Seth Brenith) [#19018](https://github.com/nodejs/node/pull/19018)
- [[`12412ef43f`](https://github.com/nodejs/node/commit/12412ef43f)] - **fs**: fix potential segfault in async calls (Joyee Cheung) [#18811](https://github.com/nodejs/node/pull/18811)
- [[`513d939720`](https://github.com/nodejs/node/commit/513d939720)] - **fs**: move fs.promises API to fs/promises (Michaël Zasso) [#18777](https://github.com/nodejs/node/pull/18777)
- [[`2620358624`](https://github.com/nodejs/node/commit/2620358624)] - **fs**: move utility functions to internal/fs (Michaël Zasso) [#18777](https://github.com/nodejs/node/pull/18777)
- [[`3e1e450f92`](https://github.com/nodejs/node/commit/3e1e450f92)] - **fs**: use Persistent::Reset() for resetting handles (Anna Henningsen) [#18650](https://github.com/nodejs/node/pull/18650)
- [[`28dc56dc71`](https://github.com/nodejs/node/commit/28dc56dc71)] - **fs**: fix typo in promises.lchmod & lchown (Sho Miyamoto) [#18783](https://github.com/nodejs/node/pull/18783)
- [[`b2e20b002b`](https://github.com/nodejs/node/commit/b2e20b002b)] - **fs**: extract binding error handling into a helper (Joyee Cheung) [#18642](https://github.com/nodejs/node/pull/18642)
- [[`b1c6ecb2c6`](https://github.com/nodejs/node/commit/b1c6ecb2c6)] - **fs**: fix misplaced errors in fs.symlinkSync (Joyee Cheung) [#18548](https://github.com/nodejs/node/pull/18548)
- [[`030384833f`](https://github.com/nodejs/node/commit/030384833f)] - **fs**: do not call new when creating uvException (Joyee Cheung) [#18546](https://github.com/nodejs/node/pull/18546)
- [[`d09d87821d`](https://github.com/nodejs/node/commit/d09d87821d)] - **fs**: use AliasedBuffer for fs_stats_field_array (Joyee Cheung) [#18276](https://github.com/nodejs/node/pull/18276)
- [[`4b9ba9b833`](https://github.com/nodejs/node/commit/4b9ba9b833)] - **fs**: encapsulate FSReqWrap more (James M Snell) [#18112](https://github.com/nodejs/node/pull/18112)
- [[`eca73a2f82`](https://github.com/nodejs/node/commit/eca73a2f82)] - **fs**: migrate ASYNC_CALL to AsyncCall (Joyee Cheung) [#18144](https://github.com/nodejs/node/pull/18144)
- [[`8aec3638ce`](https://github.com/nodejs/node/commit/8aec3638ce)] - **fs**: extract out validateUint32 and validateLen functions (Jon Moss) [#17682](https://github.com/nodejs/node/pull/17682)
- [[`46e1d69bd1`](https://github.com/nodejs/node/commit/46e1d69bd1)] - **fs**: extract out validatePath function (Jon Moss) [#17682](https://github.com/nodejs/node/pull/17682)
- [[`300ea7396f`](https://github.com/nodejs/node/commit/300ea7396f)] - **fs**: extract out validateOffsetLengthWrite function (Jon Moss) [#17682](https://github.com/nodejs/node/pull/17682)
- [[`8983405508`](https://github.com/nodejs/node/commit/8983405508)] - **fs**: extract out validateBuffer function (Jon Moss) [#17682](https://github.com/nodejs/node/pull/17682)
- [[`fc8c1b1ded`](https://github.com/nodejs/node/commit/fc8c1b1ded)] - **fs**: extract out validateOffsetLengthRead function (Jon Moss) [#17682](https://github.com/nodejs/node/pull/17682)
- [[`b9b8294dda`](https://github.com/nodejs/node/commit/b9b8294dda)] - **fs**: extract out validateFd function (Jon Moss) [#17682](https://github.com/nodejs/node/pull/17682)
- [[`9f122e3b55`](https://github.com/nodejs/node/commit/9f122e3b55)] - **fs**: throw fs.close errors in JS (Joyee Cheung) [#17338](https://github.com/nodejs/node/pull/17338)
- [[`6ca10de946`](https://github.com/nodejs/node/commit/6ca10de946)] - **fs**: simplify the error context collection in C++ (Joyee Cheung) [#17338](https://github.com/nodejs/node/pull/17338)
- [[`14ad0bd6a0`](https://github.com/nodejs/node/commit/14ad0bd6a0)] - **fs**: remove unused macro (James M Snell) [#17689](https://github.com/nodejs/node/pull/17689)
- [[`c0d6327dcf`](https://github.com/nodejs/node/commit/c0d6327dcf)] - **fs**: refactor After for easier maintainability (James M Snell) [#17689](https://github.com/nodejs/node/pull/17689)
- [[`2ca227f642`](https://github.com/nodejs/node/commit/2ca227f642)] - **fs**: refactor FSReqWrap and After (James M Snell) [#17689](https://github.com/nodejs/node/pull/17689)
- [[`49275c450a`](https://github.com/nodejs/node/commit/49275c450a)] - **http**: remove duplicate parser unset (Anatoli Papirovski) [#20126](https://github.com/nodejs/node/pull/20126)
- [[`cda94b2bb8`](https://github.com/nodejs/node/commit/cda94b2bb8)] - **http**: cleanup parser properties (Anatoli Papirovski) [#20126](https://github.com/nodejs/node/pull/20126)
- [[`ea60148c16`](https://github.com/nodejs/node/commit/ea60148c16)] - **http**: remove duplicate comment (Anatoli Papirovski) [#20126](https://github.com/nodejs/node/pull/20126)
- [[`6886dd1a6c`](https://github.com/nodejs/node/commit/6886dd1a6c)] - **http**: cleanup \_http_common.js (Anatoli Papirovski) [#20126](https://github.com/nodejs/node/pull/20126)
- [[`28834542c8`](https://github.com/nodejs/node/commit/28834542c8)] - **http**: simplify connection: close search (Anatoli Papirovski) [#20131](https://github.com/nodejs/node/pull/20131)
- [[`4fe1b60c5d`](https://github.com/nodejs/node/commit/4fe1b60c5d)] - **http**: use switch in matchHeader (Anatoli Papirovski) [#20131](https://github.com/nodejs/node/pull/20131)
- [[`c449eb5e8a`](https://github.com/nodejs/node/commit/c449eb5e8a)] - **http**: simplify isCookieField (Anatoli Papirovski) [#20131](https://github.com/nodejs/node/pull/20131)
- [[`299da1f503`](https://github.com/nodejs/node/commit/299da1f503)] - **http**: fix \_dump regression (Anatoli Papirovski) [#20088](https://github.com/nodejs/node/pull/20088)
- [[`54a2e933c2`](https://github.com/nodejs/node/commit/54a2e933c2)] - **http**: fix undefined error in parser event (Anatoli Papirovski) [#20029](https://github.com/nodejs/node/pull/20029)
- [[`1ac1424476`](https://github.com/nodejs/node/commit/1ac1424476)] - **http**: align parser with StreamBase interface changes (Anna Henningsen) [#18936](https://github.com/nodejs/node/pull/18936)
- [[`648d668fcc`](https://github.com/nodejs/node/commit/648d668fcc)] - **http**: emit timeout duration overflow warning sync (Anna Henningsen) [#18906](https://github.com/nodejs/node/pull/18906)
- [[`f94eec0218`](https://github.com/nodejs/node/commit/f94eec0218)] - **http**: convert utcDate to use setTimeout (Jeremiah Senkpiel) [#17800](https://github.com/nodejs/node/pull/17800)
- [[`2ecdb6d54f`](https://github.com/nodejs/node/commit/2ecdb6d54f)] - **http2**: refactor how trailers are done (James M Snell) [#19959](https://github.com/nodejs/node/pull/19959)
- [[`a890864d79`](https://github.com/nodejs/node/commit/a890864d79)] - **http2**: fix ping duration calculation (James M Snell) [#19956](https://github.com/nodejs/node/pull/19956)
- [[`db307bd628`](https://github.com/nodejs/node/commit/db307bd628)] - **http2**: emit session connect on next tick (Pieter Mees) [#19842](https://github.com/nodejs/node/pull/19842)
- [[`cef909797a`](https://github.com/nodejs/node/commit/cef909797a)] - **http2**: do not emit our own close emit in Http2Stream (James M Snell) [#19451](https://github.com/nodejs/node/pull/19451)
- [[`12b9ec09b0`](https://github.com/nodejs/node/commit/12b9ec09b0)] - **http2**: remove regular-file-only restriction (Anna Henningsen) [#18936](https://github.com/nodejs/node/pull/18936)
- [[`1eb6b01fca`](https://github.com/nodejs/node/commit/1eb6b01fca)] - **http2**: use native pipe instead of synchronous I/O (Anna Henningsen) [#18936](https://github.com/nodejs/node/pull/18936)
- [[`0812ebda88`](https://github.com/nodejs/node/commit/0812ebda88)] - **http2**: fix kUpdateTimer timer refresh (Jeremiah Senkpiel) [#18062](https://github.com/nodejs/node/pull/18062)
- [[`4a96a5041b`](https://github.com/nodejs/node/commit/4a96a5041b)] - **inspector**: migrate node to js_protocol.pdl (Alexey Kozyatinskiy) [#20141](https://github.com/nodejs/node/pull/20141)
- [[`e8ea61be41`](https://github.com/nodejs/node/commit/e8ea61be41)] - **lib**: remove unnecessary assignment of exports (Daniel Bevenius) [#20143](https://github.com/nodejs/node/pull/20143)
- [[`881fca418c`](https://github.com/nodejs/node/commit/881fca418c)] - **lib**: remove unused binding const (Daniel Bevenius) [#20144](https://github.com/nodejs/node/pull/20144)
- [[`c64632ea3b`](https://github.com/nodejs/node/commit/c64632ea3b)] - **lib**: remove duplicate require calls in tls.js (Daniel Bevenius) [#20099](https://github.com/nodejs/node/pull/20099)
- [[`beaa7bb671`](https://github.com/nodejs/node/commit/beaa7bb671)] - **lib**: make c, ca and certs const in \_tls_common (Daniel Bevenius) [#20073](https://github.com/nodejs/node/pull/20073)
- [[`6348ec869f`](https://github.com/nodejs/node/commit/6348ec869f)] - **lib**: use object destructuring tls.js (Daniel Bevenius) [#20070](https://github.com/nodejs/node/pull/20070)
- [[`445a89f6a9`](https://github.com/nodejs/node/commit/445a89f6a9)] - **lib**: fix coverage reporting (Anna Henningsen) [#20035](https://github.com/nodejs/node/pull/20035)
- [[`e88cd882f5`](https://github.com/nodejs/node/commit/e88cd882f5)] - **lib**: move Pipe/TCPConnectWrap to obj destructuring (Daniel Bevenius) [#19611](https://github.com/nodejs/node/pull/19611)
- [[`f2b10799ef`](https://github.com/nodejs/node/commit/f2b10799ef)] - **lib**: rename js source to lower snake_case (Daniel Bevenius) [#19556](https://github.com/nodejs/node/pull/19556)
- [[`c2835e5e47`](https://github.com/nodejs/node/commit/c2835e5e47)] - **lib**: merge stream code for http2 streams & net.Socket (Ashok) [#19527](https://github.com/nodejs/node/pull/19527)
- [[`49963f4da9`](https://github.com/nodejs/node/commit/49963f4da9)] - **lib**: remove unused internal error constructors (Michaël Zasso) [#19203](https://github.com/nodejs/node/pull/19203)
- [[`42258d7e54`](https://github.com/nodejs/node/commit/42258d7e54)] - **lib**: include missing profiler file (cjihrig) [#18455](https://github.com/nodejs/node/pull/18455)
- [[`916cfeca77`](https://github.com/nodejs/node/commit/916cfeca77)] - **lib,src**: audit process.env in lib/ for setuid binary (Jose M. Palacios Diaz) [#18511](https://github.com/nodejs/node/pull/18511)
- [[`742ae6141c`](https://github.com/nodejs/node/commit/742ae6141c)] - **lib,src**: port isIPv4() to js (Ben Noordhuis) [#18398](https://github.com/nodejs/node/pull/18398)
- [[`6934792eb3`](https://github.com/nodejs/node/commit/6934792eb3)] - **lint**: move eslint to new plugin system (Gus Caplan) [#18566](https://github.com/nodejs/node/pull/18566)
- [[`d591a59ac1`](https://github.com/nodejs/node/commit/d591a59ac1)] - **meta**: document commit msg exception for long URLs (Vse Mozhet Byt) [#20207](https://github.com/nodejs/node/pull/20207)
- [[`b34a1e1785`](https://github.com/nodejs/node/commit/b34a1e1785)] - **module**: fix `e.stack` error when throwing undefined or null (Zhenzhen Zhan) [#19282](https://github.com/nodejs/node/pull/19282)
- [[`070a82e82c`](https://github.com/nodejs/node/commit/070a82e82c)] - **module**: replace "magic" numbers by constants (Sergey Golovin) [#18869](https://github.com/nodejs/node/pull/18869)
- [[`c86fe511f4`](https://github.com/nodejs/node/commit/c86fe511f4)] - **module**: replace magic numbers by constants (Sergey Golovin) [#18785](https://github.com/nodejs/node/pull/18785)
- [[`3b9cc424a4`](https://github.com/nodejs/node/commit/3b9cc424a4)] - **module**: remove unused code in module.js (Rich Trott) [#18768](https://github.com/nodejs/node/pull/18768)
- [[`c529168249`](https://github.com/nodejs/node/commit/c529168249)] - **n-api**: add more `int64_t` tests (Kyle Farnung) [#19402](https://github.com/nodejs/node/pull/19402)
- [[`a342cd693c`](https://github.com/nodejs/node/commit/a342cd693c)] - **net**: honor default values in Socket constructor (Santiago Gimeno) [#19971](https://github.com/nodejs/node/pull/19971)
- [[`923fb5cc18`](https://github.com/nodejs/node/commit/923fb5cc18)] - **net**: track bytesWritten in C++ land (Anna Henningsen) [#19551](https://github.com/nodejs/node/pull/19551)
- [[`7c73cd4c70`](https://github.com/nodejs/node/commit/7c73cd4c70)] - **net**: emit error on invalid address family (cjihrig) [#19415](https://github.com/nodejs/node/pull/19415)
- [[`67b5985c08`](https://github.com/nodejs/node/commit/67b5985c08)] - **net**: fix usage of writeBuffer in makeSyncWrite (Joyee Cheung) [#19103](https://github.com/nodejs/node/pull/19103)
- [[`03ddd13d8a`](https://github.com/nodejs/node/commit/03ddd13d8a)] - **net**: use `_final` instead of `on('finish')` (Anna Henningsen) [#18608](https://github.com/nodejs/node/pull/18608)
- [[`e85c20b511`](https://github.com/nodejs/node/commit/e85c20b511)] - **net,http2**: merge write error handling & property names (Anna Henningsen) [#19734](https://github.com/nodejs/node/pull/19734)
- [[`496d6023e0`](https://github.com/nodejs/node/commit/496d6023e0)] - **net,stream**: remove DuplexBase (Luigi Pinca) [#19779](https://github.com/nodejs/node/pull/19779)
- [[`2ec6995555`](https://github.com/nodejs/node/commit/2ec6995555)] - **perf_hooks**: simplify perf_hooks (James M Snell) [#19563](https://github.com/nodejs/node/pull/19563)
- [[`1f356a26ae`](https://github.com/nodejs/node/commit/1f356a26ae)] - **perf_hooks,trace_events**: fix timescale on bootstrap marks (James M Snell) [#19450](https://github.com/nodejs/node/pull/19450)
- [[`96cb4fb795`](https://github.com/nodejs/node/commit/96cb4fb795)] - **perf_hooks,trace_events**: emit perf milestone trace events (James M Snell) [#19175](https://github.com/nodejs/node/pull/19175)
- [[`fccff2702e`](https://github.com/nodejs/node/commit/fccff2702e)] - **_Revert_** "**process**: add version constants and compare" (Rod Vagg) [#20062](https://github.com/nodejs/node/pull/20062)
- [[`eeb1b9dcb7`](https://github.com/nodejs/node/commit/eeb1b9dcb7)] - **_Revert_** "**process**: add more version properties to release" (Tobias Nießen) [#19577](https://github.com/nodejs/node/pull/19577)
- [[`f2396ee60c`](https://github.com/nodejs/node/commit/f2396ee60c)] - **repl**: hide top-level await feature behind a flag (Timothy Gu) [#19604](https://github.com/nodejs/node/pull/19604)
- [[`1fc373bdf6`](https://github.com/nodejs/node/commit/1fc373bdf6)] - **_Revert_** "**repl**: refactor tests to not rely on timing" (Ruben Bridgewater) [#18715](https://github.com/nodejs/node/pull/18715)
- [[`de848ac1e0`](https://github.com/nodejs/node/commit/de848ac1e0)] - **repl**: refactor tests to not rely on timing (Bradley Farias) [#17828](https://github.com/nodejs/node/pull/17828)
- [[`727339e9c2`](https://github.com/nodejs/node/commit/727339e9c2)] - **repl**: fix util.inspect() coloring regression (Ben Noordhuis) [#17565](https://github.com/nodejs/node/pull/17565)
- [[`eeab7bc068`](https://github.com/nodejs/node/commit/eeab7bc068)] - **repl**: support top-level await (Timothy Gu) [#15566](https://github.com/nodejs/node/pull/15566)
- [[`ab64b6d799`](https://github.com/nodejs/node/commit/ab64b6d799)] - **repl**: add async and await as keywords (Timothy Gu) [#15566](https://github.com/nodejs/node/pull/15566)
- [[`cff6e69057`](https://github.com/nodejs/node/commit/cff6e69057)] - **repl**: add an internal "paused" mode (Timothy Gu) [#15566](https://github.com/nodejs/node/pull/15566)
- [[`69495436e2`](https://github.com/nodejs/node/commit/69495436e2)] - **src**: cover extra load-via-special-symbol scenario (Gabriel Schulhof) [#20186](https://github.com/nodejs/node/pull/20186)
- [[`51164dd6ad`](https://github.com/nodejs/node/commit/51164dd6ad)] - **src**: CancelTerminateExecution before throwing errors (Joyee Cheung) [#20146](https://github.com/nodejs/node/pull/20146)
- [[`80c46c1576`](https://github.com/nodejs/node/commit/80c46c1576)] - **src**: remove `MarkIndependent()` calls (Anna Henningsen) [#20108](https://github.com/nodejs/node/pull/20108)
- [[`1aa74cc2e5`](https://github.com/nodejs/node/commit/1aa74cc2e5)] - **src**: move v8::HandleScope call to Emit (Ujjwal Sharma) [#20045](https://github.com/nodejs/node/pull/20045)
- [[`8e969b6a77`](https://github.com/nodejs/node/commit/8e969b6a77)] - **src**: remove req_wrap-inl.h from stream_base.h (Daniel Bevenius) [#20063](https://github.com/nodejs/node/pull/20063)
- [[`1396996b02`](https://github.com/nodejs/node/commit/1396996b02)] - **src**: use v8:: namepace consistently in node_file (Daniel Bevenius) [#20059](https://github.com/nodejs/node/pull/20059)
- [[`2d40895797`](https://github.com/nodejs/node/commit/2d40895797)] - **src**: add sync trace to fs (Chin Huang) [#19649](https://github.com/nodejs/node/pull/19649)
- [[`80de8302e0`](https://github.com/nodejs/node/commit/80de8302e0)] - **src**: add HandleScope to fix error (Ujjwal Sharma) [#19972](https://github.com/nodejs/node/pull/19972)
- [[`17deb5fe85`](https://github.com/nodejs/node/commit/17deb5fe85)] - **src**: add node_internal.h includes for arraysize (Daniel Bevenius) [#19916](https://github.com/nodejs/node/pull/19916)
- [[`f3e107aeef`](https://github.com/nodejs/node/commit/f3e107aeef)] - **src**: add punctuation in --inspector doc url message (Nick Filatov) [#19871](https://github.com/nodejs/node/pull/19871)
- [[`362694401f`](https://github.com/nodejs/node/commit/362694401f)] - **src**: rename ERR_STRING_TOO_LARGE to ERR_STRING_TOO_LONG (Joyee Cheung) [#19864](https://github.com/nodejs/node/pull/19864)
- [[`3650972bfb`](https://github.com/nodejs/node/commit/3650972bfb)] - **src**: remove unused util.h from tls_wrap.h (Daniel Bevenius) [#19849](https://github.com/nodejs/node/pull/19849)
- [[`ed86cc570e`](https://github.com/nodejs/node/commit/ed86cc570e)] - **src**: rename req_wrap with -async/-sync suffix (Daniel Bevenius) [#19628](https://github.com/nodejs/node/pull/19628)
- [[`b7cfd278a5`](https://github.com/nodejs/node/commit/b7cfd278a5)] - **src**: clean up `req.bytes` tracking (Anna Henningsen) [#19551](https://github.com/nodejs/node/pull/19551)
- [[`852ba3a0f9`](https://github.com/nodejs/node/commit/852ba3a0f9)] - **src**: sort ENVIRONMENT_STRONG_PERSISTENT_PROPERTIES (Daniel Bevenius) [#19627](https://github.com/nodejs/node/pull/19627)
- [[`376f949510`](https://github.com/nodejs/node/commit/376f949510)] - **src**: rename fs_req_wrap -\> FSReqWrapSync (Daniel Bevenius) [#19614](https://github.com/nodejs/node/pull/19614)
- [[`2d94f77fd3`](https://github.com/nodejs/node/commit/2d94f77fd3)] - **src**: ensure that `SetImmediate()`s have `HandleScope`s (Anna Henningsen) [#19470](https://github.com/nodejs/node/pull/19470)
- [[`e8c2917b44`](https://github.com/nodejs/node/commit/e8c2917b44)] - **src**: simplify http2 perf tracking code (Anna Henningsen) [#19470](https://github.com/nodejs/node/pull/19470)
- [[`a1a409a8ca`](https://github.com/nodejs/node/commit/a1a409a8ca)] - **src**: simplify Environment::HandleCleanup (Joyee Cheung) [#19319](https://github.com/nodejs/node/pull/19319)
- [[`855dabd675`](https://github.com/nodejs/node/commit/855dabd675)] - **src**: call CleanupHandles in FreeEnvironment (Joyee Cheung) [#19319](https://github.com/nodejs/node/pull/19319)
- [[`d93c48bf61`](https://github.com/nodejs/node/commit/d93c48bf61)] - **src**: use ObjectTemplate for creating stream req objs (Anna Henningsen) [#18936](https://github.com/nodejs/node/pull/18936)
- [[`67f1d76956`](https://github.com/nodejs/node/commit/67f1d76956)] - **src**: introduce native-layer stream piping (Anna Henningsen) [#18936](https://github.com/nodejs/node/pull/18936)
- [[`f7f1437d44`](https://github.com/nodejs/node/commit/f7f1437d44)] - **src**: add helper for before/after scope without JS calls (Anna Henningsen) [#18936](https://github.com/nodejs/node/pull/18936)
- [[`f734b3eb04`](https://github.com/nodejs/node/commit/f734b3eb04)] - **src**: give StreamBases the capability to ask for data (Anna Henningsen) [#18936](https://github.com/nodejs/node/pull/18936)
- [[`c412150582`](https://github.com/nodejs/node/commit/c412150582)] - **src**: make `FileHandle` a (readonly) `StreamBase` (Anna Henningsen) [#18936](https://github.com/nodejs/node/pull/18936)
- [[`8695273948`](https://github.com/nodejs/node/commit/8695273948)] - **src**: tighten handle scopes for stream operations (Anna Henningsen) [#18936](https://github.com/nodejs/node/pull/18936)
- [[`a7e298a4a2`](https://github.com/nodejs/node/commit/a7e298a4a2)] - **src**: init emit_env_nonstring_warning\_ (Daniel Bevenius) [#19283](https://github.com/nodejs/node/pull/19283)
- [[`e0bd2f31e5`](https://github.com/nodejs/node/commit/e0bd2f31e5)] - **src**: move `Environment` ctor/dtor into env.cc (Anna Henningsen) [#19202](https://github.com/nodejs/node/pull/19202)
- [[`1dd9c9787b`](https://github.com/nodejs/node/commit/1dd9c9787b)] - **src**: add tracing category macros (James M Snell) [#19155](https://github.com/nodejs/node/pull/19155)
- [[`50b1cb39bd`](https://github.com/nodejs/node/commit/50b1cb39bd)] - **src**: fix deprecation id for non-string env value (Sakthipriyan Vairamani (thefourtheye)) [#19209](https://github.com/nodejs/node/pull/19209)
- [[`c9b4de55c0`](https://github.com/nodejs/node/commit/c9b4de55c0)] - **src**: standardise context embedder indices (Gus Caplan) [#19135](https://github.com/nodejs/node/pull/19135)
- [[`ca79fc5373`](https://github.com/nodejs/node/commit/ca79fc5373)] - **src**: replace var for (let|const) in utilities module (jvelezpo) [#18814](https://github.com/nodejs/node/pull/18814)
- [[`197258bda7`](https://github.com/nodejs/node/commit/197258bda7)] - **src**: changing node_file's usage of v8::Resolver (Jimmy Thomson) [#18765](https://github.com/nodejs/node/pull/18765)
- [[`42c14c5c17`](https://github.com/nodejs/node/commit/42c14c5c17)] - **src**: set thread local env in CreateEnvironment (Daniel Bevenius) [#18573](https://github.com/nodejs/node/pull/18573)
- [[`a16081cbad`](https://github.com/nodejs/node/commit/a16081cbad)] - **src**: use non-deprecated V8 microtasks API (Michaël Zasso) [#18753](https://github.com/nodejs/node/pull/18753)
- [[`d8ec49ed9d`](https://github.com/nodejs/node/commit/d8ec49ed9d)] - **src**: update trace event macros to v8 6.4 version (Kelvin Jin) [#17640](https://github.com/nodejs/node/pull/17640)
- [[`28708677d9`](https://github.com/nodejs/node/commit/28708677d9)] - **src**: resolve issues reported by coverity (cjihrig) [#18629](https://github.com/nodejs/node/pull/18629)
- [[`8ccd320549`](https://github.com/nodejs/node/commit/8ccd320549)] - **src**: don't abort when package.json is a directory (Ben Noordhuis) [#18270](https://github.com/nodejs/node/pull/18270)
- [[`1573e4563a`](https://github.com/nodejs/node/commit/1573e4563a)] - **src**: move GetNow to Environment (Anatoli Papirovski) [#18562](https://github.com/nodejs/node/pull/18562)
- [[`b2b9d11a14`](https://github.com/nodejs/node/commit/b2b9d11a14)] - **src**: fix fs.write() externalized string handling (Ben Noordhuis) [#18216](https://github.com/nodejs/node/pull/18216)
- [[`e1c29f2c52`](https://github.com/nodejs/node/commit/e1c29f2c52)] - **src**: clean up argument assertions in node_file.cc (Joyee Cheung) [#18192](https://github.com/nodejs/node/pull/18192)
- [[`5f6478759b`](https://github.com/nodejs/node/commit/5f6478759b)] - **src**: fix -Wunused-but-set-variable warnings (Ben Noordhuis) [#18205](https://github.com/nodejs/node/pull/18205)
- [[`c8ac188e3f`](https://github.com/nodejs/node/commit/c8ac188e3f)] - **src**: remove unused function (cjihrig) [#17671](https://github.com/nodejs/node/pull/17671)
- [[`02bad59f00`](https://github.com/nodejs/node/commit/02bad59f00)] - **src**: fix -Wunused-result warning (Ben Noordhuis) [#16726](https://github.com/nodejs/node/pull/16726)
- [[`088bba31a3`](https://github.com/nodejs/node/commit/088bba31a3)] - **_Revert_** "**src, tools**: add debug symbols for node internals" (Ben Noordhuis) [#17272](https://github.com/nodejs/node/pull/17272)
- [[`9e1a6f8cb1`](https://github.com/nodejs/node/commit/9e1a6f8cb1)] - **src,lib**: implement import.meta (Michaël Zasso) [#18368](https://github.com/nodejs/node/pull/18368)
- [[`22819aa998`](https://github.com/nodejs/node/commit/22819aa998)] - **stream**: prevent 'end' to be emitted after 'error' (Matteo Collina) [#20104](https://github.com/nodejs/node/pull/20104)
- [[`3c1ad38e64`](https://github.com/nodejs/node/commit/3c1ad38e64)] - **stream**: fix incorrect comment in \_stream_readable.js (Snehil Verma) [#19882](https://github.com/nodejs/node/pull/19882)
- [[`a7c25b7d42`](https://github.com/nodejs/node/commit/a7c25b7d42)] - **stream**: always emit error before close (Mathias Buus) [#19836](https://github.com/nodejs/node/pull/19836)
- [[`d37e59fa6a`](https://github.com/nodejs/node/commit/d37e59fa6a)] - **stream**: fix backpressure when multiple sync (Matteo Collina) [#19613](https://github.com/nodejs/node/pull/19613)
- [[`d111d7b91c`](https://github.com/nodejs/node/commit/d111d7b91c)] - **stream**: give error message if `write()` cb called twice (Anna Henningsen) [#19510](https://github.com/nodejs/node/pull/19510)
- [[`86c659ba61`](https://github.com/nodejs/node/commit/86c659ba61)] - **stream**: add a test case for the underlying cause. (陈刚) [#18575](https://github.com/nodejs/node/pull/18575)
- [[`87b9bceacb`](https://github.com/nodejs/node/commit/87b9bceacb)] - **stream**: always defer readable in EOF when sync (Matteo Collina) [#18615](https://github.com/nodejs/node/pull/18615)
- [[`e7cb694a60`](https://github.com/nodejs/node/commit/e7cb694a60)] - **stream**: always reset awaitDrain when emitting data (Anna Henningsen) [#18516](https://github.com/nodejs/node/pull/18516)
- [[`563fff2938`](https://github.com/nodejs/node/commit/563fff2938)] - **stream**: defer readable and flow when sync (Mathias Buus) [#18515](https://github.com/nodejs/node/pull/18515)
- [[`ccf64e5f22`](https://github.com/nodejs/node/commit/ccf64e5f22)] - **stream**: augment BufferList.prototype (Luigi Pinca) [#18353](https://github.com/nodejs/node/pull/18353)
- [[`0778f79cb3`](https://github.com/nodejs/node/commit/0778f79cb3)] - **stream**: do not emit readable if the stream ended (Mathias Buus) [#18372](https://github.com/nodejs/node/pull/18372)
- [[`e782715d0a`](https://github.com/nodejs/node/commit/e782715d0a)] - **string_decoder**: fix regressions (Anatoli Papirovski) [#18723](https://github.com/nodejs/node/pull/18723)
- [[`180af17b52`](https://github.com/nodejs/node/commit/180af17b52)] - **string_decoder**: reimplement in C++ (Anna Henningsen) [#18537](https://github.com/nodejs/node/pull/18537)
- [[`0ddc06098d`](https://github.com/nodejs/node/commit/0ddc06098d)] - **test**: improve http res write and end dont take array (Ilya Sotov) [#20199](https://github.com/nodejs/node/pull/20199)
- [[`d7ff4f0750`](https://github.com/nodejs/node/commit/d7ff4f0750)] - **test**: fix test when NODE_OPTIONS env var is set to --trace-warnings (Ashok) [#20027](https://github.com/nodejs/node/pull/20027)
- [[`c322fca2ad`](https://github.com/nodejs/node/commit/c322fca2ad)] - **test**: improve common.expectsError (Ruben Bridgewater) [#19797](https://github.com/nodejs/node/pull/19797)
- [[`bb3ead8ba6`](https://github.com/nodejs/node/commit/bb3ead8ba6)] - **test**: update non-string header names should throw (Dhansuhu Uzumaki) [#20172](https://github.com/nodejs/node/pull/20172)
- [[`2fd7284add`](https://github.com/nodejs/node/commit/2fd7284add)] - **test**: add test for loading read-only modules (Bill Ticehurst) [#20138](https://github.com/nodejs/node/pull/20138)
- [[`bc6965bdd2`](https://github.com/nodejs/node/commit/bc6965bdd2)] - **test**: fix arg names in benchmark string_decoder (Anatoli Papirovski) [#20125](https://github.com/nodejs/node/pull/20125)
- [[`6c90aee0b5`](https://github.com/nodejs/node/commit/6c90aee0b5)] - **test**: reduce duration in misc benchmark (Anatoli Papirovski) [#20125](https://github.com/nodejs/node/pull/20125)
- [[`26a0a4659d`](https://github.com/nodejs/node/commit/26a0a4659d)] - **test**: fix missing param in url benchmark (Anatoli Papirovski) [#20125](https://github.com/nodejs/node/pull/20125)
- [[`77dc257f67`](https://github.com/nodejs/node/commit/77dc257f67)] - **test**: use correct arg name in domains benchmark (Anatoli Papirovski) [#20125](https://github.com/nodejs/node/pull/20125)
- [[`8c3e672543`](https://github.com/nodejs/node/commit/8c3e672543)] - **test**: use correct arg name in assert benchmark (Anatoli Papirovski) [#20125](https://github.com/nodejs/node/pull/20125)
- [[`6278c4b268`](https://github.com/nodejs/node/commit/6278c4b268)] - **test**: fix long-running http benchmarks (Anatoli Papirovski) [#20125](https://github.com/nodejs/node/pull/20125)
- [[`c61db33865`](https://github.com/nodejs/node/commit/c61db33865)] - **test**: fix long-running buffer benchmarks (Anatoli Papirovski) [#20125](https://github.com/nodejs/node/pull/20125)
- [[`f4a559b240`](https://github.com/nodejs/node/commit/f4a559b240)] - **test**: add strictEqual method to assert (Christine E. Taylor) [#20189](https://github.com/nodejs/node/pull/20189)
- [[`b4e86f12f8`](https://github.com/nodejs/node/commit/b4e86f12f8)] - **test**: resolve process.setgid() error on Ubuntu (Divyanshu Singh) [#19755](https://github.com/nodejs/node/pull/19755)
- [[`18f41dda90`](https://github.com/nodejs/node/commit/18f41dda90)] - **test**: remove message from strictEqual assertions (Bryan Azofeifa) [#20174](https://github.com/nodejs/node/pull/20174)
- [[`82c52b5841`](https://github.com/nodejs/node/commit/82c52b5841)] - **test**: fix test-child-process-send-returns-boolean (Rich Trott) [#20136](https://github.com/nodejs/node/pull/20136)
- [[`392ed93bf0`](https://github.com/nodejs/node/commit/392ed93bf0)] - **test**: fix flaky http-server-keep-alive-timeout (Santiago Gimeno) [#20093](https://github.com/nodejs/node/pull/20093)
- [[`bed57ef2be`](https://github.com/nodejs/node/commit/bed57ef2be)] - **test**: removes string literals from test-domain-timer.js (Palash Nigam) [#20120](https://github.com/nodejs/node/pull/20120)
- [[`1fab8b44c3`](https://github.com/nodejs/node/commit/1fab8b44c3)] - **test**: remove message from strictEqual assertions (isurusiri) [#20067](https://github.com/nodejs/node/pull/20067)
- [[`77f3c1f8f7`](https://github.com/nodejs/node/commit/77f3c1f8f7)] - **test**: fix http-agent-destroyed-socket cb not firing (Anatoli Papirovski) [#20006](https://github.com/nodejs/node/pull/20006)
- [[`8a0b994de3`](https://github.com/nodejs/node/commit/8a0b994de3)] - **test**: remove expectations based on v8 headers from types test (Gus Caplan) [#20003](https://github.com/nodejs/node/pull/20003)
- [[`eb0cfaf88e`](https://github.com/nodejs/node/commit/eb0cfaf88e)] - **test**: remove test case 0 from tls-cnnic-whitelist (Daniel Bevenius) [#19767](https://github.com/nodejs/node/pull/19767)
- [[`0ffd79b60c`](https://github.com/nodejs/node/commit/0ffd79b60c)] - **test**: set clientOpts.port property (Daniel Bevenius) [#19767](https://github.com/nodejs/node/pull/19767)
- [[`43ed1eedd5`](https://github.com/nodejs/node/commit/43ed1eedd5)] - **test**: update keys/Makefile to clean and build all (Daniel Bevenius) [#19975](https://github.com/nodejs/node/pull/19975)
- [[`65bd225f13`](https://github.com/nodejs/node/commit/65bd225f13)] - **test**: fix warning in dlopen-ping-pong/binding.cc (Daniel Bevenius) [#19966](https://github.com/nodejs/node/pull/19966)
- [[`974d07dca5`](https://github.com/nodejs/node/commit/974d07dca5)] - **test**: fixed flaky test-http-readable-data-event (Matteo Collina) [#19931](https://github.com/nodejs/node/pull/19931)
- [[`fde5a6b65a`](https://github.com/nodejs/node/commit/fde5a6b65a)] - **test**: fix test-http-dump-req-when-res-ends (Luigi Pinca) [#19866](https://github.com/nodejs/node/pull/19866)
- [[`e14585fa27`](https://github.com/nodejs/node/commit/e14585fa27)] - **test**: move require('http2') after crypto check (Daniel Bevenius) [#19907](https://github.com/nodejs/node/pull/19907)
- [[`4162115326`](https://github.com/nodejs/node/commit/4162115326)] - **test**: add check for root user (Daniel Bevenius) [#19850](https://github.com/nodejs/node/pull/19850)
- [[`dfea13a168`](https://github.com/nodejs/node/commit/dfea13a168)] - **test**: verify inspector help url works (cjihrig) [#19887](https://github.com/nodejs/node/pull/19887)
- [[`d1156da815`](https://github.com/nodejs/node/commit/d1156da815)] - **test**: refactor parallel/test-tls-async-cb-after-socket-end (juggernaut451) [#18985](https://github.com/nodejs/node/pull/18985)
- [[`cbc7eb7eec`](https://github.com/nodejs/node/commit/cbc7eb7eec)] - **test**: refactor parallel/test-tls-cert-chains-concat (juggernaut451) [#19096](https://github.com/nodejs/node/pull/19096)
- [[`5b8c62c60d`](https://github.com/nodejs/node/commit/5b8c62c60d)] - **test**: fix flaky http-client-timeout-agent (Santiago Gimeno) [#19856](https://github.com/nodejs/node/pull/19856)
- [[`e048b15523`](https://github.com/nodejs/node/commit/e048b15523)] - **test**: refactor parallel/test-tls-delayed-attach (juggernaut451) [#19421](https://github.com/nodejs/node/pull/19421)
- [[`244af7a9d5`](https://github.com/nodejs/node/commit/244af7a9d5)] - **test**: verify multiple init via well-known symbol (Gabriel Schulhof) [#19875](https://github.com/nodejs/node/pull/19875)
- [[`3217c70718`](https://github.com/nodejs/node/commit/3217c70718)] - **test**: update assert messages to show expected and actual values (Dave O'Mahony) [#19420](https://github.com/nodejs/node/pull/19420)
- [[`a639ec4ca8`](https://github.com/nodejs/node/commit/a639ec4ca8)] - **test**: move test-http-dump-req-when-res-end (Rich Trott) [#19819](https://github.com/nodejs/node/pull/19819)
- [[`95dc59010b`](https://github.com/nodejs/node/commit/95dc59010b)] - **test**: move http-client-timeout-agent to sequential (Rich Trott) [#19809](https://github.com/nodejs/node/pull/19809)
- [[`fde93f82b2`](https://github.com/nodejs/node/commit/fde93f82b2)] - **test**: rename test cases (Rajkumar Purushothaman) [#19765](https://github.com/nodejs/node/pull/19765)
- [[`3dc5404105`](https://github.com/nodejs/node/commit/3dc5404105)] - **test**: resolve process.setegid error on Ubuntu (Divyanshu Singh) [#19757](https://github.com/nodejs/node/pull/19757)
- [[`682b85036e`](https://github.com/nodejs/node/commit/682b85036e)] - **test**: fix multiple expectedWarnings bug (Daniel Bevenius) [#19766](https://github.com/nodejs/node/pull/19766)
- [[`126b03e2f9`](https://github.com/nodejs/node/commit/126b03e2f9)] - **test**: add tests for fs/promises.js fileHandle methods (willhayslett) [#19605](https://github.com/nodejs/node/pull/19605)
- [[`2fef227a61`](https://github.com/nodejs/node/commit/2fef227a61)] - **test**: check all properties in common.expectsError (Ruben Bridgewater) [#19722](https://github.com/nodejs/node/pull/19722)
- [[`8995125c14`](https://github.com/nodejs/node/commit/8995125c14)] - **test**: ensure failed assertions cause build to fail (Teddy Katz) [#19650](https://github.com/nodejs/node/pull/19650)
- [[`1dc8eb4bd3`](https://github.com/nodejs/node/commit/1dc8eb4bd3)] - **test**: add regression test for large write (Anna Henningsen) [#19551](https://github.com/nodejs/node/pull/19551)
- [[`42c740212d`](https://github.com/nodejs/node/commit/42c740212d)] - **test**: fix incorrect assumptions on uid and gid (garwahl) [#19554](https://github.com/nodejs/node/pull/19554)
- [[`ce2ed584c8`](https://github.com/nodejs/node/commit/ce2ed584c8)] - **test**: improve tty.getColorDepth coverage (Ruben Bridgewater) [#19446](https://github.com/nodejs/node/pull/19446)
- [[`8fb4ea9f75`](https://github.com/nodejs/node/commit/8fb4ea9f75)] - **test**: add deprecation code to expectWarning (Daniel Bevenius) [#19474](https://github.com/nodejs/node/pull/19474)
- [[`fddcd6253b`](https://github.com/nodejs/node/commit/fddcd6253b)] - **test**: move ESM fixtures to fixtures dir (Michaël Zasso) [#19409](https://github.com/nodejs/node/pull/19409)
- [[`3ad7c1ae97`](https://github.com/nodejs/node/commit/3ad7c1ae97)] - **test**: remove unused deprecation code (Daniel Bevenius) [#19317](https://github.com/nodejs/node/pull/19317)
- [[`8181c607e5`](https://github.com/nodejs/node/commit/8181c607e5)] - **test**: add regression tests (Ruben Bridgewater) [#19188](https://github.com/nodejs/node/pull/19188)
- [[`4bfc03b57d`](https://github.com/nodejs/node/commit/4bfc03b57d)] - **_Revert_** "**test**: add test cases for fsPromises" (Rich Trott) [#19101](https://github.com/nodejs/node/pull/19101)
- [[`c05c73a634`](https://github.com/nodejs/node/commit/c05c73a634)] - **test**: add test cases for fsPromises (Daijiro Wachi) [#19064](https://github.com/nodejs/node/pull/19064)
- [[`fecc64d6dc`](https://github.com/nodejs/node/commit/fecc64d6dc)] - **test**: fix test-http-connect (Ruben Bridgewater) [#18941](https://github.com/nodejs/node/pull/18941)
- [[`0a26280388`](https://github.com/nodejs/node/commit/0a26280388)] - **test**: really test the ttywrap bits of getasyncid (Jeremiah Senkpiel) [#18886](https://github.com/nodejs/node/pull/18886)
- [[`5055c29e82`](https://github.com/nodejs/node/commit/5055c29e82)] - **test**: use runWithInvalidFD() in tests expecting EBADF (Joyee Cheung) [#18864](https://github.com/nodejs/node/pull/18864)
- [[`acf2fd39f7`](https://github.com/nodejs/node/commit/acf2fd39f7)] - **test**: introduce common.runWithInvalidFD() (Joyee Cheung) [#18864](https://github.com/nodejs/node/pull/18864)
- [[`53a5d87bec`](https://github.com/nodejs/node/commit/53a5d87bec)] - **test**: fix deprecation warning in binding.cc (Daniel Bevenius) [#18877](https://github.com/nodejs/node/pull/18877)
- [[`7514eb3cff`](https://github.com/nodejs/node/commit/7514eb3cff)] - **test**: actually test tty `getColorDepth()` (Jeremiah Senkpiel) [#18800](https://github.com/nodejs/node/pull/18800)
- [[`92bf2492cd`](https://github.com/nodejs/node/commit/92bf2492cd)] - **test**: move getTTYfd() to common helpers (Jeremiah Senkpiel) [#18800](https://github.com/nodejs/node/pull/18800)
- [[`94e8d2a5ff`](https://github.com/nodejs/node/commit/94e8d2a5ff)] - **test**: fix unrelated variable name changes (Anatoli Papirovski) [#18823](https://github.com/nodejs/node/pull/18823)
- [[`3e8af961b3`](https://github.com/nodejs/node/commit/3e8af961b3)] - **test**: formalize exposure of internal bindings (Gus Caplan) [#18698](https://github.com/nodejs/node/pull/18698)
- [[`cf52ab19dc`](https://github.com/nodejs/node/commit/cf52ab19dc)] - **test**: remove unused using declarations (Daniel Bevenius) [#18637](https://github.com/nodejs/node/pull/18637)
- [[`1729af2ce9`](https://github.com/nodejs/node/commit/1729af2ce9)] - **test**: convert new tests to use error types (Jack Horton) [#18581](https://github.com/nodejs/node/pull/18581)
- [[`075eef5956`](https://github.com/nodejs/node/commit/075eef5956)] - **test**: added input validation test for fchmod (Luca Maraschi) [#18217](https://github.com/nodejs/node/pull/18217)
- [[`4372185ef8`](https://github.com/nodejs/node/commit/4372185ef8)] - **test**: fix builds (Ruben Bridgewater) [#18500](https://github.com/nodejs/node/pull/18500)
- [[`a025723e01`](https://github.com/nodejs/node/commit/a025723e01)] - **test**: fix flaky test-process-fatal-execption-tick (Rich Trott) [#18461](https://github.com/nodejs/node/pull/18461)
- [[`94e36f1f31`](https://github.com/nodejs/node/commit/94e36f1f31)] - **test**: fix flaky test-fs-write (Joyee Cheung) [#18374](https://github.com/nodejs/node/pull/18374)
- [[`bea5f26d34`](https://github.com/nodejs/node/commit/bea5f26d34)] - **test**: fix if-error-has-good-stack (Joyee Cheung) [#18378](https://github.com/nodejs/node/pull/18378)
- [[`63f78f5ddc`](https://github.com/nodejs/node/commit/63f78f5ddc)] - **test**: improve fs error message test (Joyee Cheung) [#18277](https://github.com/nodejs/node/pull/18277)
- [[`080a72c349`](https://github.com/nodejs/node/commit/080a72c349)] - **test**: check fs.read and fs.readsync input (Omar Crisostomo) [#17910](https://github.com/nodejs/node/pull/17910)
- [[`f576341dc2`](https://github.com/nodejs/node/commit/f576341dc2)] - **test**: replace assert.equal with assert.strictEqual (Sho Miyamoto) [#18119](https://github.com/nodejs/node/pull/18119)
- [[`b88da496ef`](https://github.com/nodejs/node/commit/b88da496ef)] - **test**: fix flaky interval test (Anatoli Papirovski) [#18161](https://github.com/nodejs/node/pull/18161)
- [[`8d043238de`](https://github.com/nodejs/node/commit/8d043238de)] - **test**: fix flaky interval test (Anatoli Papirovski) [#18140](https://github.com/nodejs/node/pull/18140)
- [[`6707903fae`](https://github.com/nodejs/node/commit/6707903fae)] - **test**: improve coverage for Cipheriv and Decipheriv (Leko) [#17458](https://github.com/nodejs/node/pull/17458)
- [[`84b7a86786`](https://github.com/nodejs/node/commit/84b7a86786)] - **test**: improve coverage for Cipher and Decipher (Leko) [#17449](https://github.com/nodejs/node/pull/17449)
- [[`7ce6d23387`](https://github.com/nodejs/node/commit/7ce6d23387)] - **test**: add test for importing acorn (Timothy Gu) [#15566](https://github.com/nodejs/node/pull/15566)
- [[`1f29963eac`](https://github.com/nodejs/node/commit/1f29963eac)] - **test,http**: fix http dump test (Matteo Collina) [#19823](https://github.com/nodejs/node/pull/19823)
- [[`d784dbf36a`](https://github.com/nodejs/node/commit/d784dbf36a)] - **timers**: call destroy on interval error (Anatoli Papirovski) [#20001](https://github.com/nodejs/node/pull/20001)
- [[`f395c2107e`](https://github.com/nodejs/node/commit/f395c2107e)] - **timers**: fix subsequent enroll calls not working (Anatoli Papirovski) [#19936](https://github.com/nodejs/node/pull/19936)
- [[`12bad69871`](https://github.com/nodejs/node/commit/12bad69871)] - **timers**: fix clearInterval to work with timers from setTimeout (Rémi Berson) [#19952](https://github.com/nodejs/node/pull/19952)
- [[`28f3ffba0f`](https://github.com/nodejs/node/commit/28f3ffba0f)] - **timers**: add helper fn for async init (Anatoli Papirovski) [#18825](https://github.com/nodejs/node/pull/18825)
- [[`2aa3e3b00f`](https://github.com/nodejs/node/commit/2aa3e3b00f)] - **timers**: fix enroll deprecation wording (Anatoli Papirovski) [#18704](https://github.com/nodejs/node/pull/18704)
- [[`0f9efef05d`](https://github.com/nodejs/node/commit/0f9efef05d)] - **timers**: refactor timer list processing (Anatoli Papirovski) [#18582](https://github.com/nodejs/node/pull/18582)
- [[`8204b0f9c6`](https://github.com/nodejs/node/commit/8204b0f9c6)] - **timers**: simplify clearTimeout & clearInterval (Anatoli Papirovski) [#18579](https://github.com/nodejs/node/pull/18579)
- [[`c11cb038a1`](https://github.com/nodejs/node/commit/c11cb038a1)] - **timers**: async track unref timers (Anatoli Papirovski) [#18579](https://github.com/nodejs/node/pull/18579)
- [[`568b6a5c9e`](https://github.com/nodejs/node/commit/568b6a5c9e)] - **timers**: be more defensive with intervals (Anatoli Papirovski) [#18579](https://github.com/nodejs/node/pull/18579)
- [[`1b05d7bc86`](https://github.com/nodejs/node/commit/1b05d7bc86)] - **timers**: remove unused variable (Anatoli Papirovski) [#18579](https://github.com/nodejs/node/pull/18579)
- [[`bb5575aa75`](https://github.com/nodejs/node/commit/bb5575aa75)] - **timers**: add internal \[@@ refresh()\] function (Jeremiah Senkpiel) [#18065](https://github.com/nodejs/node/pull/18065)
- [[`54fe0a6cbb`](https://github.com/nodejs/node/commit/54fe0a6cbb)] - **timers**: reposition getTimers definition internally (Jeremiah Senkpiel) [#18065](https://github.com/nodejs/node/pull/18065)
- [[`92ba624fa1`](https://github.com/nodejs/node/commit/92ba624fa1)] - **tls**: provide now value from C++ (Anatoli Papirovski) [#18562](https://github.com/nodejs/node/pull/18562)
- [[`1bdd3b0dcf`](https://github.com/nodejs/node/commit/1bdd3b0dcf)] - **tools**: improve heading type detection in json.js (Vse Mozhet Byt) [#20074](https://github.com/nodejs/node/pull/20074)
- [[`0a3876d025`](https://github.com/nodejs/node/commit/0a3876d025)] - **tools**: delete redundant .eslintignore rule (Vse Mozhet Byt) [#20203](https://github.com/nodejs/node/pull/20203)
- [[`44856f72cb`](https://github.com/nodejs/node/commit/44856f72cb)] - **tools**: fix broken link in icu notes (Harry Sarson) [#20030](https://github.com/nodejs/node/pull/20030)
- [[`512a6a55b7`](https://github.com/nodejs/node/commit/512a6a55b7)] - **tools**: stricter no-undef eslint rule (Ruben Bridgewater) [#19926](https://github.com/nodejs/node/pull/19926)
- [[`4f4e716dc3`](https://github.com/nodejs/node/commit/4f4e716dc3)] - **tools**: treat SIGABRT as crash (Anna Henningsen) [#19990](https://github.com/nodejs/node/pull/19990)
- [[`b43866cbaa`](https://github.com/nodejs/node/commit/b43866cbaa)] - **tools**: include exit code in TAP log (Refael Ackermann) [#19855](https://github.com/nodejs/node/pull/19855)
- [[`114ba67b02`](https://github.com/nodejs/node/commit/114ba67b02)] - **tools**: include exit code in test failures (Rich Trott) [#19855](https://github.com/nodejs/node/pull/19855)
- [[`9a6dd07e8d`](https://github.com/nodejs/node/commit/9a6dd07e8d)] - **tools**: overhaul tools/doc/json.js (Vse Mozhet Byt) [#19832](https://github.com/nodejs/node/pull/19832)
- [[`254058109f`](https://github.com/nodejs/node/commit/254058109f)] - **tools**: add 'spaced-comment' into eslint rules (Weijia Wang) [#19596](https://github.com/nodejs/node/pull/19596)
- [[`ebfed17fec`](https://github.com/nodejs/node/commit/ebfed17fec)] - **tools**: enable ESLint quote-props rule (Rich Trott) [#19156](https://github.com/nodejs/node/pull/19156)
- [[`c221355c50`](https://github.com/nodejs/node/commit/c221355c50)] - **tools**: alphabetize ESLint rules (Rich Trott) [#19100](https://github.com/nodejs/node/pull/19100)
- [[`3a2e912b2d`](https://github.com/nodejs/node/commit/3a2e912b2d)] - **tools**: update eslint rule (Ruben Bridgewater) [#18831](https://github.com/nodejs/node/pull/18831)
- [[`cbc6f39b71`](https://github.com/nodejs/node/commit/cbc6f39b71)] - **tools**: enable eslint no-whitespace-before-property rule (Ruben Bridgewater) [#18831](https://github.com/nodejs/node/pull/18831)
- [[`13637d23f7`](https://github.com/nodejs/node/commit/13637d23f7)] - **tools**: add falsely removed eslint rules (Ruben Bridgewater) [#18933](https://github.com/nodejs/node/pull/18933)
- [[`bff5d5b8f0`](https://github.com/nodejs/node/commit/bff5d5b8f0)] - **tools**: add FileHandle to doc/type-parser.js (Vse Mozhet Byt) [#18601](https://github.com/nodejs/node/pull/18601)
- [[`fa9f31a4fd`](https://github.com/nodejs/node/commit/fa9f31a4fd)] - **_Revert_** "**tools**: teach gyp to write an 'all deps' rule" (Rod Vagg) [#18287](https://github.com/nodejs/node/pull/18287)
- [[`90f882e50d`](https://github.com/nodejs/node/commit/90f882e50d)] - **_Revert_** "**tools**: simplify tools/doc/addon-verify.js" (Rod Vagg) [#18287](https://github.com/nodejs/node/pull/18287)
- [[`c6682636be`](https://github.com/nodejs/node/commit/c6682636be)] - **tools**: simplify tools/doc/addon-verify.js (Ben Noordhuis) [#17407](https://github.com/nodejs/node/pull/17407)
- [[`920c13203d`](https://github.com/nodejs/node/commit/920c13203d)] - **tools**: teach gyp to write an 'all deps' rule (Ben Noordhuis) [#17407](https://github.com/nodejs/node/pull/17407)
- [[`e46c3f743d`](https://github.com/nodejs/node/commit/e46c3f743d)] - **tools**: fix typo in gen-postmortem-metadata.py (Daniel Bevenius) [#17268](https://github.com/nodejs/node/pull/17268)
- [[`ceaeee0120`](https://github.com/nodejs/node/commit/ceaeee0120)] - **tty**: add color support for more terminals (Ruben Bridgewater) [#19730](https://github.com/nodejs/node/pull/19730)
- [[`1b715221b9`](https://github.com/nodejs/node/commit/1b715221b9)] - **tty**: add attribution for chalk (Ruben Bridgewater) [#19730](https://github.com/nodejs/node/pull/19730)
- [[`fa2d43bd3e`](https://github.com/nodejs/node/commit/fa2d43bd3e)] - **url**: make urlToOptions() handle IPv6 literals (Luigi Pinca) [#19720](https://github.com/nodejs/node/pull/19720)
- [[`a892d9a0c1`](https://github.com/nodejs/node/commit/a892d9a0c1)] - **url**: use existing handlers instead of duplicated code (Sergey Golovin) [#19267](https://github.com/nodejs/node/pull/19267)
- [[`cc6abc6e84`](https://github.com/nodejs/node/commit/cc6abc6e84)] - **url**: fix error type (Benjamin Gruenbaum) [#19299](https://github.com/nodejs/node/pull/19299)
- [[`354849eeb5`](https://github.com/nodejs/node/commit/354849eeb5)] - **url**: replace "magic" numbers by constants (Sergey Golovin) [#19300](https://github.com/nodejs/node/pull/19300)
- [[`7f3838bb3e`](https://github.com/nodejs/node/commit/7f3838bb3e)] - **util**: improve inspect performance (Ruben Bridgewater) [#20009](https://github.com/nodejs/node/pull/20009)
- [[`554ed3740d`](https://github.com/nodejs/node/commit/554ed3740d)] - **_Revert_** "**util**: change util.inspect depth default" (Anna Henningsen) [#20089](https://github.com/nodejs/node/pull/20089)
- [[`47af0a0eda`](https://github.com/nodejs/node/commit/47af0a0eda)] - **_Revert_** "**util**: change %o depth default" (Anna Henningsen) [#20089](https://github.com/nodejs/node/pull/20089)
- [[`a167b6aab6`](https://github.com/nodejs/node/commit/a167b6aab6)] - **util**: fix inspect performance bug (Ruben Bridgewater) [#20007](https://github.com/nodejs/node/pull/20007)
- [[`1329844a08`](https://github.com/nodejs/node/commit/1329844a08)] - **_Revert_** "**util**: use blue on non-windows systems for number/bigint" (Ruben Bridgewater) [#19256](https://github.com/nodejs/node/pull/19256)
- [[`893432ad92`](https://github.com/nodejs/node/commit/893432ad92)] - **util**: add boxed BigInt formatting to util.inspect (Michaël Zasso) [#19341](https://github.com/nodejs/node/pull/19341)
- [[`ae3137049f`](https://github.com/nodejs/node/commit/ae3137049f)] - **util**: assign missed deprecation number (Anna Henningsen) [#19149](https://github.com/nodejs/node/pull/19149)
- [[`1708af369b`](https://github.com/nodejs/node/commit/1708af369b)] - **util**: use blue on non-windows systems for number/bigint (Gus Caplan) [#18925](https://github.com/nodejs/node/pull/18925)
- [[`07ba9141e4`](https://github.com/nodejs/node/commit/07ba9141e4)] - **vm**: add support for import.meta to Module (punteek) [#19277](https://github.com/nodejs/node/pull/19277)
- [[`7b46503706`](https://github.com/nodejs/node/commit/7b46503706)] - **win, tools**: add nasm to boxstarter script (Bartosz Sosnowski) [#19950](https://github.com/nodejs/node/pull/19950)
- [[`7bc5151d5e`](https://github.com/nodejs/node/commit/7bc5151d5e)] - **zlib**: fix windowBits validation to allow 0 for decompression mode (Anand Suresh) [#19686](https://github.com/nodejs/node/pull/19686)
- [[`e765257283`](https://github.com/nodejs/node/commit/e765257283)] - **zlib,stream**: use “official” util.types typechecks (Anna Henningsen) [#19602](https://github.com/nodejs/node/pull/19602)

Windows 32-bit Installer: https://nodejs.org/dist/v10.0.0/node-v10.0.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v10.0.0/node-v10.0.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v10.0.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v10.0.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v10.0.0/node-v10.0.0.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v10.0.0/node-v10.0.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v10.0.0/node-v10.0.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v10.0.0/node-v10.0.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v10.0.0/node-v10.0.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v10.0.0/node-v10.0.0-aix-ppc64.tar.gz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v10.0.0/node-v10.0.0-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v10.0.0/node-v10.0.0-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v10.0.0/node-v10.0.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v10.0.0/node-v10.0.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v10.0.0/node-v10.0.0.tar.gz \
Other release files: https://nodejs.org/dist/v10.0.0/ \
Documentation: https://nodejs.org/docs/v10.0.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

1b9849a20b335229bcdbea04db213846553c87f50002b6457a5c9c4b299411b5  node-v10.0.0-aix-ppc64.tar.gz
37447fdb5f5cbcf1307ca1661ed67e6e911e0e988c0cb6d15f92eebb211dce88  node-v10.0.0-darwin-x64.tar.gz
9916d7ce2d6b63a91350776003f8e2cc707d83bdb70315d2200037a3498557e0  node-v10.0.0-darwin-x64.tar.xz
ad334214114ac8ac96fe3f120f92cc58db00985211a634e25da898e9b713f25c  node-v10.0.0-headers.tar.gz
5e3e1fecb6b9dc92ddf3724e5d07dda7941ca14d29130120755534a102a15f3b  node-v10.0.0-headers.tar.xz
36adc17e9cceab32179d3314da9cb9c737ffb11f0de4e688f407ad6d9ca32201  node-v10.0.0-linux-arm64.tar.gz
18f626a967d72294d969bdf000ca80ef483a8bb75482a2c4d14f5e0141626611  node-v10.0.0-linux-arm64.tar.xz
5371f34a2682b6278d26bbe4537e521bff014110f6f22bab12b87b3e57969987  node-v10.0.0-linux-armv6l.tar.gz
0434d15a67a02e8ae35e24cef7bab146aaffdff22d23cbe24b05182bf747d34f  node-v10.0.0-linux-armv6l.tar.xz
63b94e0b4c4ffb71131cfe5ec222b2ec7844b0c5498a946c4cbe279869140079  node-v10.0.0-linux-armv7l.tar.gz
cfab679f9d0f8459ee991b72ae915f79954a676029a64199461c1a6335de3b07  node-v10.0.0-linux-armv7l.tar.xz
eb0b11a26ae32544204a701ec06f5ef023ad5957d950944af4cc8b9bdc77bffa  node-v10.0.0-linux-ppc64le.tar.gz
fbd3cb757047b81dd763d34bef085614c439038bd46ea5dfc2e17cc5f463a845  node-v10.0.0-linux-ppc64le.tar.xz
0ea38c8a9e9934d41adb1cf18d3e5b40464bd1ab2fd8642cf00b6698c941873d  node-v10.0.0-linux-s390x.tar.gz
51ade51e758cbf83d4ed2bed7094ba1a6e92e12094a0b51d9901539662facf9f  node-v10.0.0-linux-s390x.tar.xz
763a7b03ba2a3db56f3f59d104e7283161979b36e36479dc7bf68f6a471b2e33  node-v10.0.0-linux-x64.tar.gz
d57c391daef40e706ca71abeaf9d53271c9d0fdb9cd18a80f6296b04dbaf2d5a  node-v10.0.0-linux-x64.tar.xz
a681315eb21f3f9509383eaa85ac5695884baa58f062e5ab5d5c25d5e8546911  node-v10.0.0.pkg
661b7b377cccfb53454e55bdaf1fa949b23c1ef6d7f4dcc6a4af307fd4eff230  node-v10.0.0-sunos-x64.tar.gz
87949f6e47f37e39d078a04b6a8348a5939acb8791d4b0e9fba8a3af49169eff  node-v10.0.0-sunos-x64.tar.xz
49dd5e3409214d1b03f748d004d014a2afe0d27978696be144752469a32c5892  node-v10.0.0.tar.gz
e239109020755db8a58e6bcb8b9375439e31cf3bbe92d0670a320a47a4e8ab50  node-v10.0.0.tar.xz
98de0ae3b1aa9f5190fe0cdcbda7fca35d231d1cb21330b98a1b5781396235c3  node-v10.0.0-win-x64.7z
a95d88e2c28cbcbadb1fa431ec0b686f196dda00d4a25b0829450dc8f5214ec3  node-v10.0.0-win-x64.zip
d2711f9f10e4753ca413519c49d4bf2c9d7732c6310082729225a94607ba0bdf  node-v10.0.0-win-x86.7z
bae0181b0d3eb5991e3863dc91c415059c50edd93ef26cd36486660a7579ce9a  node-v10.0.0-win-x86.zip
1fc52538bfaed268d53146c1dc305c46691c1873eae0a60ed94d8f5900d4d689  node-v10.0.0-x64.msi
196ab87dc6036ed2360b8456d433b1834b8a0cb5d3a1cd889110cd0db53eb5bc  node-v10.0.0-x86.msi
c73de46311d68a84cb46c57d6dd9d0a54d756bd6d75d338cada6973313951f86  win-x64/node.exe
6208de40b1996964b678eda15816df967071cdaf34c30a595c9654bb3834b10a  win-x64/node.lib
d55251b1783000e2e8ba42fb53b9b05948c2b86cb8c4d72e73df31a658993d6e  win-x64/node_pdb.7z
55a0d312520417548ce864882192a9d1be028bd365a56fccb13d9986eac106ce  win-x64/node_pdb.zip
fba07546c5bec24e45517d360cb8283656952760d747cc86016dfad03f00b381  win-x86/node.exe
099d3d4f3c3bb75f38c33b6f9e300b5f3f671ab5b83c60a57fd838663a079c5e  win-x86/node.lib
86332111c93b6def1320bf8b4389341008a2dafab4cde38a1815419d8477abaa  win-x86/node_pdb.7z
fd742bfe54b9275115dc6a5724098f3e7e8216fcda8f269eba6e2d204f4fd1c5  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQEcBAEBCAAGBQJa34TaAAoJEHNBsVwHCHesAGMH/0af+qsziOGQ4viqBID0KRad
LxnzPQ3JeAuxc9Gk9xm2816q488cBgo+6cOMjoubKoUfc5A0B8ei5JpqunBPE5Pg
Icl8J2oC06OclWJxYnFZ6763an4P3pUy1vTQIR7ThIH46I7roYh0FokOU1AOdVo7
KBizICG0Iuwwmf3G97j4pGT4gNgYlk722l1qPx1KbHUfEyZ6Qt1pgTeHgRyko5Zx
K2W0flOPRv415MDxAy5olwkJKeQiLAtNhFOVErpcdfVxFu5bLu64p2efZTr+Sf4A
wTX4wjbGX11JEHuM64bmTCbsJ9kB18vpowuhoiIImhRUu7YOy25o5CtGQRpg3Jo=
=k0Qd
-----END PGP SIGNATURE-----

```
