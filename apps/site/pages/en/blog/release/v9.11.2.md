---
date: '2018-06-12T23:51:23.298Z'
category: release
title: Node v9.11.2 (Current)
layout: blog-post
author: <PERSON>
---

### Notable Changes

- **Fixes memory exhaustion DoS** (CVE-2018-7164): Fixes a bug introduced in 9.7.0 that increases the memory consumed when reading from the network into JavaScript using the net.Socket object directly as a stream.
- **buffer** (CVE-2018-7167): Fixes Denial of Service vulnerability where calling Buffer.fill() could hang
- **http2**
  - (CVE-2018-7161): Fixes Denial of Service vulnerability by updating the http2 implementation to not crash under certain circumstances during cleanup
  - (CVE-2018-1000168): Fixes Denial of Service vulnerability by upgrading nghttp2 to 1.32.0
- **tls** (CVE-2018-7162): Fixes Denial of Service vulnerability by updating the TLS implementation to not crash upon receiving

### Commits

- [[`65ed3213ca`](https://github.com/nodejs/node/commit/65ed3213ca)] - **deps**: update to nghttp2 1.32.0 (<PERSON>) [nodejs-private/node-private#124](https://github.com/nodejs-private/node-private/pull/124)
- [[`f0af3b09bd`](https://github.com/nodejs/node/commit/f0af3b09bd)] - **doc**: buffer.fill() can zero-fill on invalid input (Сковорода Никита Андреевич) [nodejs-private/node-private#120](https://github.com/nodejs-private/node-private/pull/120)
- [[`828159fcd4`](https://github.com/nodejs/node/commit/828159fcd4)] - **http2**: fixup http2stream cleanup and other nits (James M Snell) [nodejs-private/node-private#122](https://github.com/nodejs-private/node-private/pull/122)
- [[`be103eba41`](https://github.com/nodejs/node/commit/be103eba41)] - **src**: re-add `Realloc()` shrink after reading stream data (Anna Henningsen) [nodejs-private/node-private#129](https://github.com/nodejs-private/node-private/pull/129)
- [[`555696df51`](https://github.com/nodejs/node/commit/555696df51)] - **src**: avoid hanging on Buffer#fill 0-length input (Сковорода Никита Андреевич) [nodejs-private/node-private#120](https://github.com/nodejs-private/node-private/pull/120)
- [[`7684ba63c4`](https://github.com/nodejs/node/commit/7684ba63c4)] - **test**: add tls write error regression test (Shigeki Ohtsu) [nodejs-private/node-private#130](https://github.com/nodejs-private/node-private/pull/130)
- [[`0ab90acaf3`](https://github.com/nodejs/node/commit/0ab90acaf3)] - **test**: add regression test for nghttp2 CVE-2018-1000168 (James M Snell) [nodejs-private/node-private#124](https://github.com/nodejs-private/node-private/pull/124)
- [[`84f23d2f12`](https://github.com/nodejs/node/commit/84f23d2f12)] - **tls**: fix SSL write error handling (Anna Henningsen) [nodejs-private/node-private#130](https://github.com/nodejs-private/node-private/pull/130)

Windows 32-bit Installer: https://nodejs.org/dist/v9.11.2/node-v9.11.2-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v9.11.2/node-v9.11.2-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v9.11.2/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v9.11.2/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v9.11.2/node-v9.11.2.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v9.11.2/node-v9.11.2-darwin-x64.tar.gz \
Linux 32-bit Binary: https://nodejs.org/dist/v9.11.2/node-v9.11.2-linux-x86.tar.xz \
Linux 64-bit Binary: https://nodejs.org/dist/v9.11.2/node-v9.11.2-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v9.11.2/node-v9.11.2-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v9.11.2/node-v9.11.2-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v9.11.2/node-v9.11.2-aix-ppc64.tar.gz \
SmartOS 32-bit Binary: https://nodejs.org/dist/v9.11.2/node-v9.11.2-sunos-x86.tar.xz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v9.11.2/node-v9.11.2-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v9.11.2/node-v9.11.2-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v9.11.2/node-v9.11.2-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v9.11.2/node-v9.11.2-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v9.11.2/node-v9.11.2.tar.gz \
Other release files: https://nodejs.org/dist/v9.11.2/ \
Documentation: https://nodejs.org/docs/v9.11.2/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

b2a6ce3c9161436e5219748f42c7ce2d0cbc9a1ab05ee404d47a39115da0181f  node-v9.11.2-aix-ppc64.tar.gz
340993096108d1dc0e0b598560d87ec645fc94b0fc83a423c36343f2da45f4b8  node-v9.11.2-darwin-x64.tar.gz
9f2441d902d3dd6c02a21099ee1f579a91bd55916c925a1ab7a3b9011fe7dda6  node-v9.11.2-darwin-x64.tar.xz
48a88d158dc83aa211e6fb59aeacb535c9a791f3b7ad8e6d041ada5c43c02a19  node-v9.11.2-headers.tar.gz
a1112577b10108c0499a38740fed60fa0379cdd3fd0d25adaf33a5e3a64ab22e  node-v9.11.2-headers.tar.xz
78f600a8690ae34aac8079142c77bb0f0f09c6ddea2272b9a135285610ad71d1  node-v9.11.2-linux-arm64.tar.gz
9a713a6f4473425b83bf67e13e4b3c9f1f683c996b913f1f6854d208996367b6  node-v9.11.2-linux-arm64.tar.xz
d784e5c862c1e34ce241c57ceaac4dc8242ec038b0d315d26a89fd620b82be06  node-v9.11.2-linux-armv6l.tar.gz
21b789165d04a205ed914ee6ddd59099a6bc7cb0c5435079a44978243a98d152  node-v9.11.2-linux-armv6l.tar.xz
319b64f32ffd43d66eb97abecdd14d2f74217142e1bf33192accd9e49c9c98e4  node-v9.11.2-linux-armv7l.tar.gz
9e00140edde72b8a9ff7dd110e80655e268583ec2e32a0fc73d32f942bc6e382  node-v9.11.2-linux-armv7l.tar.xz
00f1ff033fa728a0c516be08b06c46e5eea1155b8d1ee944ae6f96739dd559a7  node-v9.11.2-linux-ppc64le.tar.gz
55daf9d83072b0a36e599210fb51427fcbdd81821e0b06efa7bd9fae039d6564  node-v9.11.2-linux-ppc64le.tar.xz
6b6391abf64f6ff10c64fb98d4e77580f2d9d0104e959fab6551d40b7007e62c  node-v9.11.2-linux-s390x.tar.gz
7e7c7b88615bd6df3c8846a6034f595e7c367d30985596cbbfedafeb436e9a92  node-v9.11.2-linux-s390x.tar.xz
bbb46f86c64abe96ee98faa733424fc76f20a38d12f59bdcd60057efa5f1ce89  node-v9.11.2-linux-x64.tar.gz
a2e7fe4ee3c4e3f31e00dff241c92c2ed779a9f36735578603d2be966f938a4b  node-v9.11.2-linux-x64.tar.xz
166cc28bd9c8217c533b2921edd5e980b14f1d670d828e9d40c1d5b37f51496d  node-v9.11.2-linux-x86.tar.gz
b4ba2b95e6a8a22dcfd7f402f25710719e67af144d32a003b538d05a57626b00  node-v9.11.2-linux-x86.tar.xz
276f82d8742e7069d47667cec243305d587534a74a7aa8a6b7d92df98767f53b  node-v9.11.2.pkg
079dabdc51bf9210b5844cebe6b88e4a2597698e59364362977a698d471a22a2  node-v9.11.2-sunos-x64.tar.gz
de46c7a599d76a57781aa22a350909859fd1b204ae4fe6f31143df96fc8e40de  node-v9.11.2-sunos-x64.tar.xz
b195eca0163a2f90bf88ad00ad4233293a4d00080b05441df5ac4eb0728f420c  node-v9.11.2-sunos-x86.tar.gz
2316a57b45f7ad1a3a2bf665dfb491b32a48f20151a67fe1c5e0a5d023dfddad  node-v9.11.2-sunos-x86.tar.xz
4a9cf0bfdf6a0e8c454d21517f70fc2c05a99d7055571826939096172a7040f6  node-v9.11.2.tar.gz
06484eac7f6b7c87e96983a039422347c0047284bc4d373d90884ae3966dc213  node-v9.11.2.tar.xz
46f9d8584a16d61c02c81ef5eb5bf00793fa94ff35647e9549453992abf91c81  node-v9.11.2-win-x64.7z
051db8f4d3c0503e3082173f16b25e2362de8f9a5e509c403992b2069d826c25  node-v9.11.2-win-x64.zip
a7451b05db2a28ebdc095a31e79ec55ae1aae40ad701827d0013b884492ddffc  node-v9.11.2-win-x86.7z
e9f55afb5a22b8289880836e59eb13ed012d16e235a81315018a7ce3a12498c2  node-v9.11.2-win-x86.zip
6a9a125a01e377063b11578cd033163068b0f1a0f030907cbbc32db3b9b18c1d  node-v9.11.2-x64.msi
82982c3322fb8c25eda90acbecb5a07b298dc6bf4d2de8db68c5beaf11fd5b4d  node-v9.11.2-x86.msi
8c33626a10282e4b19094826bf9c932497563bf93286ca728f72b35e6f9860c9  win-x64/node.exe
902e8984223c2c4e34a2548d1c5f9f58b781f7b1a6401236c4e83268a7090cc2  win-x64/node.lib
2afcd24f40e8bded935b5a8fe3fd218259ce311f80f8a5bd0f6661f03dd4e95b  win-x64/node_pdb.7z
6b69110408f0a106ec32241c46570dcd28debb6bb883d20d7964a8c741affc26  win-x64/node_pdb.zip
d045eb4305c20d84a115c7e1d7c68e6822c14f0900ca7ea4b0ad642ea7121015  win-x86/node.exe
3cdb7b4d2c6a49cd7d811ecc3082c071fe511caf7295739f6c6b16994e2010db  win-x86/node.lib
ea6c59600915e3006a7d76fd7031986fbf38e1714cdab6eb133bef83ce59ac25  win-x86/node_pdb.7z
9fab06214591a5129808b1ef9f12a9ef9e4b6e0fb03115dbe2dce33fcb55d3c0  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEua6ZBf/XgD8lcUZhtjtTWkwgbKkFAlsgVsUACgkQtjtTWkwg
bKl6khAAjm0jyHmBkerH9lF4hFUSfC40ngG7/TPXREfxmDge7QzMsaPZYRJpFMYE
O5r5zAy7e/t+4FIr+wpZbUfGzZC5a5Q1/mRxNigGeotoTb5JW3FxxOfkabl/h7dx
0xaZ61aSTotb6jfqnp+6lOktmm4bVLdPSMfW1mhgTa9uMG4Mnlk6V+FCSh+REOzY
FoZL3wqN0nE14PNk8nrwFKkvW3W85sA09qppxOHEJPithepfsSxijltYcr4BChhF
dgQSHn4SiIonIEE5wfxwTmJHqMltJyHkZ0LSl31+5+Hw4NPhUJvg4P0o74UFX6uQ
oUU1U5LX0eYgP1+WSyrQ2EtXTzZXSawOp95vLPYE07H/u9yCbLdY9auUYCLS31CP
lwIHg7qUl6qTARvRgWCJ/U3WSoMaOLeocqxvhjXS+fLkEaf9IJkuGACwjSq7Yshi
DPVX/l33PLLfmKG3/9kIDn43b+pfYYGG0mEMwCSRHCDPrHB8hORolcItucZFA8Nd
G3TdQiZmMQ/VFNkb0GsARAupmoKbVgTzOX4bKLU41Tkq+eSy3UyDbUf58LdQMbqr
WyKkEhXa6aR84UGnF61tCaoFw/7VU2All7iW8k1vHrSUfCq7gYLJiety5KsoS9bB
aAxNDshKZ3tzOOXGuZq++t+xkFZOJGtUeSAq7g011kFK1jWTTDA=
=Ih7m
-----END PGP SIGNATURE-----

```
