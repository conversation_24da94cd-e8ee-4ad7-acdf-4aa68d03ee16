---
date: '2016-09-28T00:49:35.459Z'
category: release
title: Node v6.7.0 (Current)
layout: blog-post
author: <PERSON>
---

**This is an important security release**. All Node.js users should consult the [security release summary](/blog/vulnerability/september-2016-security-releases) at for details on patched vulnerabilities.

### Notable changes

Semver Minor:

- **openssl**:
  - Upgrade to 1.0.2i, fixes a number of defects impacting Node.js: CVE-2016-6304 ("OCSP Status Request extension unbounded memory growth", high severity), CVE-2016-2183, CVE-2016-2178 and CVE-2016-6306. (<PERSON><PERSON><PERSON>) [#8714](https://github.com/nodejs/node/pull/8714)
  - Upgrade to 1.0.2j, fixes a defect included in 1.0.2i resulting in a crash when using CRLs, CVE-2016-7052. (<PERSON><PERSON><PERSON>) [#8786](https://github.com/nodejs/node/pull/8786)
  - Remove support for loading dynamic third-party engine modules. An attacker may be able to hide malicious code to be inserted into Node.js at runtime by masquerading as one of the dynamic engine modules. Originally reported by <PERSON> (Sky<PERSON>). (<PERSON>) [nodejs/node-private#73](https://github.com/nodejs/node-private/pull/73)
- **http**: CVE-2016-5325 - Properly validate for allowable characters in the `reason` argument in `ServerResponse#writeHead()`. Fixes a possible response splitting attack vector. This introduces a new case where `throw` may occur when configuring HTTP responses, users should already be adopting try/catch here. Originally reported independently by Evan Lucas and Romain Gaucher. (Evan Lucas) [nodejs/node-private#60](https://github.com/nodejs/node-private/pull/60)

Semver Patch:

- **buffer**: Zero-fill excess bytes in new `Buffer` objects created with `Buffer.concat()` while providing a `totalLength` parameter that exceeds the total length of the original `Buffer` objects being concatenated. (Сковорода Никита Андреевич) [nodejs/node-private#64](https://github.com/nodejs/node-private/pull/64)
- **src**: Fix regression where passing an empty password and/or salt to crypto.pbkdf2() would cause a fatal error (Rich Trott) [#8572](https://github.com/nodejs/node/pull/8572)
- **tls**: CVE-2016-7099 - Fix invalid wildcard certificate validation check whereby a TLS server may be able to serve an invalid wildcard certificate for its hostname due to improper validation of `*.` in the wildcard string. Originally reported by Alexander Minozhenko and James Bunton (Atlassian). (Ben Noordhuis) [nodejs/node-private#75](https://github.com/nodejs/node-private/pull/75)
- **v8**: Fix regression where a regex on a frozen object was broken (Myles Borins) [#8673](https://github.com/nodejs/node/pull/8673)

### Commits

- [[`8fb8c46303`](https://github.com/nodejs/node/commit/8fb8c46303)] - **buffer**: zero-fill uninitialized bytes in .concat() (Сковорода Никита Андреевич) [nodejs/node-private#64](https://github.com/nodejs/node-private/pull/64)
- [[`e5998c44b4`](https://github.com/nodejs/node/commit/e5998c44b4)] - **crypto**: don't build hardware engines (Ben Noordhuis) [nodejs/node-private#73](https://github.com/nodejs/node-private/pull/73)
- [[`ed4cd2eebe`](https://github.com/nodejs/node/commit/ed4cd2eebe)] - **deps**: cherry-pick 34880eb3dc from V8 upstream (Myles Borins) [#8673](https://github.com/nodejs/node/pull/8673)
- [[`f8ad0dc0e2`](https://github.com/nodejs/node/commit/f8ad0dc0e2)] - **deps**: add -no_rand_screen to openssl s_client (Shigeki Ohtsu) [nodejs/io.js#1836](https://github.com/nodejs/io.js/pull/1836)
- [[`9181def9d4`](https://github.com/nodejs/node/commit/9181def9d4)] - **deps**: fix asm build error of openssl in x86_win32 (Shigeki Ohtsu) [iojs/io.js#1389](https://github.com/iojs/io.js/pull/1389)
- [[`2dee4af5c3`](https://github.com/nodejs/node/commit/2dee4af5c3)] - **deps**: fix openssl assembly error on ia32 win32 (Fedor Indutny) [iojs/io.js#1389](https://github.com/iojs/io.js/pull/1389)
- [[`4255dc82a9`](https://github.com/nodejs/node/commit/4255dc82a9)] - **deps**: copy all openssl header files to include dir (Shigeki Ohtsu) [#8786](https://github.com/nodejs/node/pull/8786)
- [[`c08d81df50`](https://github.com/nodejs/node/commit/c08d81df50)] - **deps**: upgrade openssl sources to 1.0.2j (Shigeki Ohtsu) [#8786](https://github.com/nodejs/node/pull/8786)
- [[`2573efc9df`](https://github.com/nodejs/node/commit/2573efc9df)] - **deps**: update openssl asm and asm_obsolete files (Shigeki Ohtsu) [#8714](https://github.com/nodejs/node/pull/8714)
- [[`67751f3d7e`](https://github.com/nodejs/node/commit/67751f3d7e)] - **deps**: add -no_rand_screen to openssl s_client (Shigeki Ohtsu) [nodejs/io.js#1836](https://github.com/nodejs/io.js/pull/1836)
- [[`4382de338b`](https://github.com/nodejs/node/commit/4382de338b)] - **deps**: fix asm build error of openssl in x86_win32 (Shigeki Ohtsu) [iojs/io.js#1389](https://github.com/iojs/io.js/pull/1389)
- [[`cfa00611b0`](https://github.com/nodejs/node/commit/cfa00611b0)] - **deps**: fix openssl assembly error on ia32 win32 (Fedor Indutny) [iojs/io.js#1389](https://github.com/iojs/io.js/pull/1389)
- [[`3e4ea603b3`](https://github.com/nodejs/node/commit/3e4ea603b3)] - **deps**: copy all openssl header files to include dir (Shigeki Ohtsu) [#8714](https://github.com/nodejs/node/pull/8714)
- [[`8937fd0dbb`](https://github.com/nodejs/node/commit/8937fd0dbb)] - **deps**: upgrade openssl sources to 1.0.2i (Shigeki Ohtsu) [#8714](https://github.com/nodejs/node/pull/8714)
- [[`c0f13e56a2`](https://github.com/nodejs/node/commit/c0f13e56a2)] - **http**: check reason chars in writeHead (Evan Lucas) [nodejs/node-private#60](https://github.com/nodejs/node-private/pull/60)
- [[`743f0c9164`](https://github.com/nodejs/node/commit/743f0c9164)] - **lib**: make tls.checkServerIdentity() more strict (Ben Noordhuis) [nodejs/node-private#75](https://github.com/nodejs/node-private/pull/75)
- [[`38bed98a92`](https://github.com/nodejs/node/commit/38bed98a92)] - **openssl**: fix keypress requirement in apps on win32 (Shigeki Ohtsu) [iojs/io.js#1389](https://github.com/iojs/io.js/pull/1389)
- [[`a25fc3f715`](https://github.com/nodejs/node/commit/a25fc3f715)] - **openssl**: fix keypress requirement in apps on win32 (Shigeki Ohtsu) [iojs/io.js#1389](https://github.com/iojs/io.js/pull/1389)
- [[`5902ba3989`](https://github.com/nodejs/node/commit/5902ba3989)] - **src**: Malloc/Calloc size 0 returns non-null pointer (Rich Trott) [#8572](https://github.com/nodejs/node/pull/8572)
- [[`a14d832884`](https://github.com/nodejs/node/commit/a14d832884)] - **test**: remove openssl options of -no\_\<prot> (Shigeki Ohtsu) [#8714](https://github.com/nodejs/node/pull/8714)

Windows 32-bit Installer: https://nodejs.org/dist/v6.7.0/node-v6.7.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v6.7.0/node-v6.7.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v6.7.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v6.7.0/win-x64/node.exe \
Mac OS X 64-bit Installer: https://nodejs.org/dist/v6.7.0/node-v6.7.0.pkg \
Mac OS X 64-bit Binary: https://nodejs.org/dist/v6.7.0/node-v6.7.0-darwin-x64.tar.gz \
Linux 32-bit Binary: https://nodejs.org/dist/v6.7.0/node-v6.7.0-linux-x86.tar.xz \
Linux 64-bit Binary: https://nodejs.org/dist/v6.7.0/node-v6.7.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v6.7.0/node-v6.7.0-linux-ppc64le.tar.xz \
SmartOS 32-bit Binary: https://nodejs.org/dist/v6.7.0/node-v6.7.0-sunos-x86.tar.xz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v6.7.0/node-v6.7.0-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v6.7.0/node-v6.7.0-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v6.7.0/node-v6.7.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v6.7.0/node-v6.7.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v6.7.0/node-v6.7.0.tar.gz \
Other release files: https://nodejs.org/dist/v6.7.0/ \
Documentation: https://nodejs.org/docs/v6.7.0/api/

Shasums (GPG signing hash: SHA512, file hash: SHA256):

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA512

69fab7a1ebeee54d5e3160eb9366e88a61500731fad86dee98c79c4a14b56bc6  node-v6.7.0-darwin-x64.tar.gz
b10270f028be73771fa8536f0c69b33f30e1bb1fd15d9a493a7365cde56af0d2  node-v6.7.0-darwin-x64.tar.xz
42f0fdcd937409842d76a5852782acfdb0b6fa8e3520df6694f2abaa7c9e942c  node-v6.7.0-headers.tar.gz
f6ddea52f8b13bcece38c965d13f6073bd7980dadcfd903b8c1872f6d8bbacb7  node-v6.7.0-headers.tar.xz
45ffd727bcab41a544ad7862fe985f6beac4fcd96c63e116ca467d1147ba6454  node-v6.7.0-linux-arm64.tar.gz
87f59a19d9a44ff938a553425c5c82a46be1f0cd43bf7c256e1c4fb61833a82a  node-v6.7.0-linux-arm64.tar.xz
0f8e0dbaa6bcccd22db75077fed1afb28c2b225b6cdc185913178ae395a68ef9  node-v6.7.0-linux-armv6l.tar.gz
bf07229d718ebe3a8d8bc59cbf06d945b89045285e44f36f516c8e0f65a5302b  node-v6.7.0-linux-armv6l.tar.xz
1e7e138ba8c54d7a0fbf5e3f188442a14a70409dc154b74b17635bcff74e4a81  node-v6.7.0-linux-armv7l.tar.gz
18b6d9df3e2aaba2d72e1f4c3cfdd3b963c23faa64d3ad72d5690959bc3dde08  node-v6.7.0-linux-armv7l.tar.xz
87f7dd1776bffd569d4f619dde99caca215c073f7d26853e16d6f93fcf681113  node-v6.7.0-linux-ppc64le.tar.gz
fc1b70f866a1c1972c55cc5d2a506f0dcc623e749d6cbe470d650c7c631f231e  node-v6.7.0-linux-ppc64le.tar.xz
e8ce540b592d337304a10f4eb19bb4efee889c6676c5f188d072bfb2a8089927  node-v6.7.0-linux-ppc64.tar.gz
6ee874b6567f7f06708f6ccb66a27dc7317b4c493b7a6c3bb49c1e53c4bdf31e  node-v6.7.0-linux-ppc64.tar.xz
e0f2616b4beb4c2505edb19e3cbedbf3d1c958441517cc9a1e918f6feaa4b95b  node-v6.7.0-linux-s390x.tar.gz
647bacc68bd0101b04074d5740492a6511dee69e23a7ff03765674d7a9fa93df  node-v6.7.0-linux-s390x.tar.xz
abe81b4150917cdbbeebc6c6b85003b80c972d32c8f5dfd2970d32e52a6877af  node-v6.7.0-linux-x64.tar.gz
09263a844c31933c6f31e576e580faf01d3bbb056efb8713388dc8d09674f8c2  node-v6.7.0-linux-x64.tar.xz
fa94c93a4a3600d68e003a399f5cf5e109c2d10505b4d9456373c25953eb9bf5  node-v6.7.0-linux-x86.tar.gz
e89a77020bd579186adbc46f6a668d3524f980c5fc75f63e1d5b5362423bcebb  node-v6.7.0-linux-x86.tar.xz
c5d46d0f105ed652c9a353d8411558c9c4610db874f01ef07e57ad613e5d4237  node-v6.7.0.pkg
33f240dac58a1293a08b66ecd01a70849c693e3a9e58a94cb7ee92126a894984  node-v6.7.0-sunos-x64.tar.gz
9df8f3d1048065fccb1c1746cfd1f3a22ef46075399e7dc92d38949e37607de7  node-v6.7.0-sunos-x64.tar.xz
80dacf34a5e17f3eeabe15e212005daeaa189caa424a83a23f302a9fa5996565  node-v6.7.0-sunos-x86.tar.gz
14bdf36ae5510a6565af69e1db651d34e75d2846a363610b6669b8c78e404b2a  node-v6.7.0-sunos-x86.tar.xz
02b8ee1719a11b9ab22bef9279519efaaf31dd0d39cba4c3a1176ccda400b8d6  node-v6.7.0.tar.gz
ceb028324aab1ee8c7ea6a62026f036f3ea71f5ef5212593d0f833f999dd3be5  node-v6.7.0.tar.xz
2eb013b15c718ec2b26768e6a325e73571ed1d2028af411307a1bbd549f709ed  node-v6.7.0-win-x64.7z
59971f8ea9fb1ac4c55ca36303fe32a0714049cf8a10843dbb5924a5d0624659  node-v6.7.0-win-x64.zip
8b4448e56223aed8c316b67336fdd1d94aeee71033934fb6bc071a358c5c8719  node-v6.7.0-win-x86.7z
d75bebea562a1da0965adc8f94d2c5a38a22cfc57959d37c5c1aeec4ea9f1c83  node-v6.7.0-win-x86.zip
2185a16f8a32087b75708b08d7e482a14597765c41d6b3711a8e6ed3a3358213  node-v6.7.0-x64.msi
0801c283200ac263cc0ca014f5d8e0ee531e1ebec6e99645a0bdd04b996c1605  node-v6.7.0-x86.msi
7ab8714273a9a1fa464ded6b543b52b6fb56c7d2c3d08ebe312b1c1be72a1477  win-x64/node.exe
17c521d3b19886f826f818b9806bb4d2af4615d5bf850c257dcdaef897e8ccaa  win-x64/node.lib
fa2f32b9f8ac97d26bb58da59c8a7299de5bd9f25b8879f8787ecd0ffbd36c3b  win-x64/node_pdb.7z
815d202704373b9894c5dc113d2a2812be5e73431f8061707e0fd012cbc0b8cc  win-x64/node_pdb.zip
a2e58867917250e013a49c4210b36aba04715159a71edcfb510c6225e3dab184  win-x86/node.exe
641503fd3d41c1bc04a95ee38e6dc2ffd9c9e5a35b3f5bfecb25965c963f2d17  win-x86/node.lib
36265c44782a8456b289c4776bf5de5ed648982f2b8dc97e17acd76258e249cf  win-x86/node_pdb.7z
2d8a25ba8e212715050ee03e4b0b9f547163ffaeb75288c9508a06e469677ff6  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----
Comment: GPGTools - https://gpgtools.org

iQIcBAEBCgAGBQJX6xF5AAoJELY7U1pMIGypw8sQAM0o8jAf5AlM3apyFJIm8q3P
8z7LsyOm/gX04h6U4xONVybx0uxqkoCs/Bk9JSJdxdc9qMM4FLxbf9W3se61FZQa
fYva3cXiAm/riUxO5vbghBNzKgE8aLFeOiuEIL5c+UzaxjFs8eY6oRfqYN87jNcB
95T3krvYGY9pA39Cyr1mBbCJq8AdjcM/ve6wqcjQDGswZF5Yq+yd94Z1qzCWzB2u
8yLcLaaoyVy2BvFg/11XgK9xK7w1KpxHK+jIzVVDE9dVoQc1pcQQZR72mYVIQFUt
jCJ69T5Usnil1ucswmfk7d1vvX1NdIawaol6AjEErO1APZsVew+TCJyvwq4uLAA2
kvgiVF/8Pe3ZdF1x9+vWz1JvEXpmehOlJ9fRv3bZn20W3/GIeS5NA+AgJrr6H65D
Trv6juvBceu2wGeBer8XCzql+9Pk4k87vARcExEKxsOU57lZ4mgaX5aMfTaNtTnp
zm5+ajGNnEZ4tjR91do6JXMF8/5MHmP93vFOwsrANabBe4sjd3u3BIbAVlkS74z0
bLZeo2dM18p3RdDw2Szo5jTSjuIhCV+5wNKxLoNjLSoYC6fSwAod+FuCXoWZZi6/
1eF4uFXik9m3ifInj6KKjCfKql8Rh6siHu2AsOjxaE/UyKY27HpXbtWpIhvM4Ht5
A0X+c5AUaeTAxt30L2PG
=+Moe
-----END PGP SIGNATURE-----

```
