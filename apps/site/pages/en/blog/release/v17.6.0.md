---
date: '2022-02-23T00:01:12.242Z'
category: release
title: Node v17.6.0 (Current)
layout: blog-post
author: <PERSON> English
---

### Notable Changes

- \[[`6b88595142`](https://github.com/nodejs/node/commit/6b88595142)] - **doc**: deprecate notice for process methods (<PERSON><PERSON>) [#41587](https://github.com/nodejs/node/pull/41587)
- \[[`77462b6083`](https://github.com/nodejs/node/commit/77462b6083)] - **stream**: revert `map` spec compliance (<PERSON>) [#41931](https://github.com/nodejs/node/pull/41931)
- \[[`b968d89fc9`](https://github.com/nodejs/node/commit/b968d89fc9)] - **build**: remove broken x32 arch support (<PERSON>) [#41905](https://github.com/nodejs/node/pull/41905)
- \[[`ba639d052b`](https://github.com/nodejs/node/commit/ba639d052b)] - **(SEMVER-MINOR)** **esm**: support https remotely and http locally under flag (Bradley Farias) [#36328](https://github.com/nodejs/node/pull/36328)
- \[[`214d17650b`](https://github.com/nodejs/node/commit/214d17650b)] - **(SEMVER-MINOR)** **fs**: support copy of relative links with cp and cpSync (Marcos Bjoerkelund) [#41819](https://github.com/nodejs/node/pull/41819)
- \[[`ec0d623bd7`](https://github.com/nodejs/node/commit/ec0d623bd7)] - **(SEMVER-MINOR)** **lib**: add FormData global when fetch is enabled (Michaël Zasso) [#41956](https://github.com/nodejs/node/pull/41956)
- \[[`3243701997`](https://github.com/nodejs/node/commit/3243701997)] - **(SEMVER-MINOR)** **readline**: bind keystroke `ctrl`+`6` to redo (Ray) [#41662](https://github.com/nodejs/node/pull/41662)
- \[[`5796c3a7ea`](https://github.com/nodejs/node/commit/5796c3a7ea)] - **process**: deprecate multipleResolves (Benjamin Gruenbaum) [#41872](https://github.com/nodejs/node/pull/41872)
  - Documentation-only.

### Dependency Updates

- \[[`d1ef43f2aa`](https://github.com/nodejs/node/commit/d1ef43f2aa)] - **deps**: upgrade npm to 8.5.1 (npm-robot) [#42039](https://github.com/nodejs/node/pull/42039)
- \[[`09c17481f6`](https://github.com/nodejs/node/commit/09c17481f6)] - **deps**: update undici to 3e267ece5f (Michaël Zasso) [#41955](https://github.com/nodejs/node/pull/41955)
- \[[`0a6f0b416f`](https://github.com/nodejs/node/commit/0a6f0b416f)] - **deps**: upgrade npm to 8.5.0 (npm-robot) [#41925](https://github.com/nodejs/node/pull/41925)

### New Collaborators

- \[[`90d90e3868`](https://github.com/nodejs/node/commit/90d90e3868)] - **doc**: add marsonya to collaborators (Akhil Marsonya) [#41991](https://github.com/nodejs/node/pull/41991)
- \[[`0de611f53a`](https://github.com/nodejs/node/commit/0de611f53a)] - **doc**: add joesepi to collaborators (Joe Sepi) [#41914](https://github.com/nodejs/node/pull/41914)

### Commits

- \[[`c673953f6d`](https://github.com/nodejs/node/commit/c673953f6d)] - **build**: cancel running CI jobs when a PR is updated (Michaël Zasso) [#42017](https://github.com/nodejs/node/pull/42017)
- \[[`782506c099`](https://github.com/nodejs/node/commit/782506c099)] - **build**: increase operations and test tag (Michael Dawson) [#42038](https://github.com/nodejs/node/pull/42038)
- \[[`d3e6ad1ed5`](https://github.com/nodejs/node/commit/d3e6ad1ed5)] - **build**: allow manual run of feature request action (Michael Dawson) [#42037](https://github.com/nodejs/node/pull/42037)
- \[[`69d7736e08`](https://github.com/nodejs/node/commit/69d7736e08)] - **build**: add action to close stale feature requests (Michael Dawson) [#41981](https://github.com/nodejs/node/pull/41981)
- \[[`1315f1eb25`](https://github.com/nodejs/node/commit/1315f1eb25)] - **build**: prevent concurrent CI and CQ workflow runs (Michaël Zasso) [#42016](https://github.com/nodejs/node/pull/42016)
- \[[`1e8e8bc22b`](https://github.com/nodejs/node/commit/1e8e8bc22b)] - **build**: use zoslib_include_dir provided by node-gyp (alexcfyung) [#41713](https://github.com/nodejs/node/pull/41713)
- \[[`4e2e2cb821`](https://github.com/nodejs/node/commit/4e2e2cb821)] - **build**: move tool updates workflow runtime a day later (Rich Trott) [#41982](https://github.com/nodejs/node/pull/41982)
- \[[`cd04ed1382`](https://github.com/nodejs/node/commit/cd04ed1382)] - **build**: extend yamllint configuration (Mestery) [#41756](https://github.com/nodejs/node/pull/41756)
- \[[`b968d89fc9`](https://github.com/nodejs/node/commit/b968d89fc9)] - **build**: remove broken x32 arch support (Ben Noordhuis) [#41905](https://github.com/nodejs/node/pull/41905)
- \[[`2b1dabfb16`](https://github.com/nodejs/node/commit/2b1dabfb16)] - **build**: revert fast-track changes (Filip Skokan) [#41892](https://github.com/nodejs/node/pull/41892)
- \[[`182e6b9ee9`](https://github.com/nodejs/node/commit/182e6b9ee9)] - **build**: improve consistency between workflows (Mestery) [#41791](https://github.com/nodejs/node/pull/41791)
- \[[`f4cb72600d`](https://github.com/nodejs/node/commit/f4cb72600d)] - **build,tools**: change the quotes in YAML (Mestery) [#41756](https://github.com/nodejs/node/pull/41756)
- \[[`24b0a563a4`](https://github.com/nodejs/node/commit/24b0a563a4)] - **child_process**: kHideConsoleWindows for spawnSync (Raymond Zhao) [#41412](https://github.com/nodejs/node/pull/41412)
- \[[`c41e402c06`](https://github.com/nodejs/node/commit/c41e402c06)] - **cluster**: respect listen backlog set by workers (Elad Nava) [#41623](https://github.com/nodejs/node/pull/41623)
- \[[`84b1e4fba7`](https://github.com/nodejs/node/commit/84b1e4fba7)] - **crypto**: check result of BIO_new in X509ToObject (Tobias Nießen) [#41979](https://github.com/nodejs/node/pull/41979)
- \[[`946ac96817`](https://github.com/nodejs/node/commit/946ac96817)] - **crypto**: rename X509_NAME_FLAGS (Tobias Nießen) [#42001](https://github.com/nodejs/node/pull/42001)
- \[[`60682917dd`](https://github.com/nodejs/node/commit/60682917dd)] - **(SEMVER-MINOR)** **crypto**: expose Web Crypto API on the global scope (Antoine du Hamel) [#41938](https://github.com/nodejs/node/pull/41938)
- \[[`0fff4d2314`](https://github.com/nodejs/node/commit/0fff4d2314)] - **crypto**: do not advertise unsupported algorithms (Brian White) [#41864](https://github.com/nodejs/node/pull/41864)
- \[[`67f1ebf9b4`](https://github.com/nodejs/node/commit/67f1ebf9b4)] - **debugger**: add debugger alias for exec(expr) (Xuguang Mei) [#41907](https://github.com/nodejs/node/pull/41907)
- \[[`1eac0a1677`](https://github.com/nodejs/node/commit/1eac0a1677)] - **debugger**: remove duplicate setPauseOnException call (Balakrishna Avulapati) [#41838](https://github.com/nodejs/node/pull/41838)
- \[[`d1ef43f2aa`](https://github.com/nodejs/node/commit/d1ef43f2aa)] - **deps**: upgrade npm to 8.5.1 (npm-robot) [#42039](https://github.com/nodejs/node/pull/42039)
- \[[`09c17481f6`](https://github.com/nodejs/node/commit/09c17481f6)] - **deps**: update undici to 3e267ece5f (Michaël Zasso) [#41955](https://github.com/nodejs/node/pull/41955)
- \[[`0a6f0b416f`](https://github.com/nodejs/node/commit/0a6f0b416f)] - **deps**: upgrade npm to 8.5.0 (npm-robot) [#41925](https://github.com/nodejs/node/pull/41925)
- \[[`fea682ca94`](https://github.com/nodejs/node/commit/fea682ca94)] - **doc**: fix typo in http2 endAfterHeaders description (Alexandru Comanescu) [#42060](https://github.com/nodejs/node/pull/42060)
- \[[`9c618ad729`](https://github.com/nodejs/node/commit/9c618ad729)] - **doc**: make minor fixes to contributing guides (T-O-R-U-S) [#41966](https://github.com/nodejs/node/pull/41966)
- \[[`60bbacdd91`](https://github.com/nodejs/node/commit/60bbacdd91)] - **doc**: use openpgp.org for keyserver examples (Nick Schonning) [#39227](https://github.com/nodejs/node/pull/39227)
- \[[`2e252b3c95`](https://github.com/nodejs/node/commit/2e252b3c95)] - **doc**: adjust a/an word usage (Derek Wolpert) [#41915](https://github.com/nodejs/node/pull/41915)
- \[[`c7d590c66e`](https://github.com/nodejs/node/commit/c7d590c66e)] - **doc**: remove extra \<td> (Ray) [#42006](https://github.com/nodejs/node/pull/42006)
- \[[`06f225c6a6`](https://github.com/nodejs/node/commit/06f225c6a6)] - **doc**: capitalize valgrind (T•Ø•R•Ü•S) [#41986](https://github.com/nodejs/node/pull/41986)
- \[[`6195de506a`](https://github.com/nodejs/node/commit/6195de506a)] - **doc**: fix deprecation number (Antoine du Hamel) [#41990](https://github.com/nodejs/node/pull/41990)
- \[[`90d90e3868`](https://github.com/nodejs/node/commit/90d90e3868)] - **doc**: add marsonya to collaborators (Akhil Marsonya) [#41991](https://github.com/nodejs/node/pull/41991)
- \[[`6b88595142`](https://github.com/nodejs/node/commit/6b88595142)] - **doc**: deprecate notice for process methods (Yash Ladha) [#41587](https://github.com/nodejs/node/pull/41587)
- \[[`d34ee45134`](https://github.com/nodejs/node/commit/d34ee45134)] - **doc**: fix bug in `readable.unshift` code example (Xuguang Mei) [#41944](https://github.com/nodejs/node/pull/41944)
- \[[`8748b1e61d`](https://github.com/nodejs/node/commit/8748b1e61d)] - **doc**: add missing `buffer` in the `stream/consumers` import example (Mestery) [#41961](https://github.com/nodejs/node/pull/41961)
- \[[`cb9dba034a`](https://github.com/nodejs/node/commit/cb9dba034a)] - **doc**: fix typo in readableStream.locked description (Tobias Nießen) [#41923](https://github.com/nodejs/node/pull/41923)
- \[[`43a5161cee`](https://github.com/nodejs/node/commit/43a5161cee)] - **doc**: fix wrong indent in stream documentation (Xuguang Mei) [#41943](https://github.com/nodejs/node/pull/41943)
- \[[`dca2692ae3`](https://github.com/nodejs/node/commit/dca2692ae3)] - **doc**: emphasize security warning in vm module doc (Rich Trott) [#41916](https://github.com/nodejs/node/pull/41916)
- \[[`b0e8ccb84c`](https://github.com/nodejs/node/commit/b0e8ccb84c)] - **doc**: clarify `tls.Server` `'connection'` event documentation (Austin Cheney) [#41917](https://github.com/nodejs/node/pull/41917)
- \[[`6a7b0160df`](https://github.com/nodejs/node/commit/6a7b0160df)] - **doc**: improve scrollbar appearance in dark mode (Aaron Xie) [#41890](https://github.com/nodejs/node/pull/41890)
- \[[`aa684a78fe`](https://github.com/nodejs/node/commit/aa684a78fe)] - **doc**: replace text with paths in toggle SVGs (Brian White) [#41885](https://github.com/nodejs/node/pull/41885)
- \[[`fc9d4e96c1`](https://github.com/nodejs/node/commit/fc9d4e96c1)] - **doc**: edit process.config deprecation text (Rich Trott) [#41889](https://github.com/nodejs/node/pull/41889)
- \[[`0de611f53a`](https://github.com/nodejs/node/commit/0de611f53a)] - **doc**: add joesepi to collaborators (Joe Sepi) [#41914](https://github.com/nodejs/node/pull/41914)
- \[[`ba5cb89134`](https://github.com/nodejs/node/commit/ba5cb89134)] - **doc**: remove unneeded ellipsis in header (Rich Trott) [#41888](https://github.com/nodejs/node/pull/41888)
- \[[`291fe631dd`](https://github.com/nodejs/node/commit/291fe631dd)] - **doc**: fix webstream close reject state state doc (Benjamin Gruenbaum) [#41860](https://github.com/nodejs/node/pull/41860)
- \[[`c0f4289c65`](https://github.com/nodejs/node/commit/c0f4289c65)] - **esm**: improve `fetch_module` test coverage and remove hack (Antoine du Hamel) [#41947](https://github.com/nodejs/node/pull/41947)
- \[[`a95f7a0c25`](https://github.com/nodejs/node/commit/a95f7a0c25)] - **esm**: remove erroneous `context.parentURL` property passed to `load` hook (Jacob Smith) [#41975](https://github.com/nodejs/node/pull/41975)
- \[[`66c9565f99`](https://github.com/nodejs/node/commit/66c9565f99)] - **esm**: fix typo in `fetch_module` (Michael Scovetta) [#41924](https://github.com/nodejs/node/pull/41924)
- \[[`ba639d052b`](https://github.com/nodejs/node/commit/ba639d052b)] - **(SEMVER-MINOR)** **esm**: support https remotely and http locally under flag (Bradley Farias) [#36328](https://github.com/nodejs/node/pull/36328)
- \[[`0660ba3cd0`](https://github.com/nodejs/node/commit/0660ba3cd0)] - **events**: set default handler value (Benjamin Gruenbaum) [#41970](https://github.com/nodejs/node/pull/41970)
- \[[`5a807c5927`](https://github.com/nodejs/node/commit/5a807c5927)] - **fs**: fix default `length` parameter for `fs.read` (wbt) [#40349](https://github.com/nodejs/node/pull/40349)
- \[[`214d17650b`](https://github.com/nodejs/node/commit/214d17650b)] - **(SEMVER-MINOR)** **fs**: support copy of relative links with cp and cpSync (Marcos Bjoerkelund) [#41819](https://github.com/nodejs/node/pull/41819)
- \[[`be5143b3f5`](https://github.com/nodejs/node/commit/be5143b3f5)] - **fs**: fix cb/sync writev empty array behavior (Benjamin Gruenbaum) [#41932](https://github.com/nodejs/node/pull/41932)
- \[[`b4db004855`](https://github.com/nodejs/node/commit/b4db004855)] - **fs**: fix writev empty array error behavior (Benjamin Gruenbaum) [#41919](https://github.com/nodejs/node/pull/41919)
- \[[`528c585b2b`](https://github.com/nodejs/node/commit/528c585b2b)] - **fs**: refactor to use ES2020 syntax (小菜) [#41903](https://github.com/nodejs/node/pull/41903)
- \[[`ec0d623bd7`](https://github.com/nodejs/node/commit/ec0d623bd7)] - **(SEMVER-MINOR)** **lib**: add FormData global when fetch is enabled (Michaël Zasso) [#41956](https://github.com/nodejs/node/pull/41956)
- \[[`c3b0e72d01`](https://github.com/nodejs/node/commit/c3b0e72d01)] - **lib**: stop installing webcrypto if `no_browser_globals` is `true` (Mestery) [#41971](https://github.com/nodejs/node/pull/41971)
- \[[`ae36276148`](https://github.com/nodejs/node/commit/ae36276148)] - **lib**: allow respondWithNewView on byob auto allocated streams (Sean Quinlan) [#41887](https://github.com/nodejs/node/pull/41887)
- \[[`68ec8d5824`](https://github.com/nodejs/node/commit/68ec8d5824)] - **lib**: stop installing fetch if no_browser_globals is true (Darshan Sen) [#41969](https://github.com/nodejs/node/pull/41969)
- \[[`f9bfdc3a65`](https://github.com/nodejs/node/commit/f9bfdc3a65)] - **lib**: restrict usage of fetch related globals in core internals (Antoine du Hamel) [#41957](https://github.com/nodejs/node/pull/41957)
- \[[`3a121345e7`](https://github.com/nodejs/node/commit/3a121345e7)] - **lib**: move kEnumerableProperty to internal/util (Mestery) [#41877](https://github.com/nodejs/node/pull/41877)
- \[[`fa3a9135ad`](https://github.com/nodejs/node/commit/fa3a9135ad)] - **lib**: add internal genericNodeError() function (Rich Trott) [#41879](https://github.com/nodejs/node/pull/41879)
- \[[`1e698e65a7`](https://github.com/nodejs/node/commit/1e698e65a7)] - **meta**: update AUTHORS (Node.js GitHub Bot) [#42058](https://github.com/nodejs/node/pull/42058)
- \[[`f51b55dae4`](https://github.com/nodejs/node/commit/f51b55dae4)] - **meta**: use plain emojis instead of escape sequences (Mestery) [#41989](https://github.com/nodejs/node/pull/41989)
- \[[`3d998112be`](https://github.com/nodejs/node/commit/3d998112be)] - **meta**: update AUTHORS and .mailmap (Rich Trott) [#41953](https://github.com/nodejs/node/pull/41953)
- \[[`391f4e5c25`](https://github.com/nodejs/node/commit/391f4e5c25)] - **perf_hooks**: use arrays to store EntryBuffers (Xuguang Mei) [#42032](https://github.com/nodejs/node/pull/42032)
- \[[`5796c3a7ea`](https://github.com/nodejs/node/commit/5796c3a7ea)] - **process**: deprecate multipleResolves (Benjamin Gruenbaum) [#41872](https://github.com/nodejs/node/pull/41872)
- \[[`3243701997`](https://github.com/nodejs/node/commit/3243701997)] - **(SEMVER-MINOR)** **readline**: bind keystroke `ctrl`+`6` to redo (Ray) [#41662](https://github.com/nodejs/node/pull/41662)
- \[[`c766159e68`](https://github.com/nodejs/node/commit/c766159e68)] - **repl**: #41690 REPL gives wrong autocomplete on literals (Xuguang Mei) [#41883](https://github.com/nodejs/node/pull/41883)
- \[[`f80cae8be5`](https://github.com/nodejs/node/commit/f80cae8be5)] - **src**: merge ToJsSet into ToV8Value (Anna Henningsen) [#41757](https://github.com/nodejs/node/pull/41757)
- \[[`e2a74d2e04`](https://github.com/nodejs/node/commit/e2a74d2e04)] - **src**: remove separate definitions for static constexpr members (Anna Henningsen) [#41755](https://github.com/nodejs/node/pull/41755)
- \[[`d91a956419`](https://github.com/nodejs/node/commit/d91a956419)] - **src**: use const reference instead of pointer in URL::SerializeURL (Anna Henningsen) [#41759](https://github.com/nodejs/node/pull/41759)
- \[[`0330e2d796`](https://github.com/nodejs/node/commit/0330e2d796)] - **src**: reserve string allocation space early in URL::SerializeURL (Anna Henningsen) [#41759](https://github.com/nodejs/node/pull/41759)
- \[[`364ec5ba8b`](https://github.com/nodejs/node/commit/364ec5ba8b)] - **src**: fix query/fragment serialization in URL::SerializeURL (Anna Henningsen) [#41759](https://github.com/nodejs/node/pull/41759)
- \[[`6076291168`](https://github.com/nodejs/node/commit/6076291168)] - **src**: don't print interface if sin6_scope_id is 0 (Santiago Gimeno) [#41547](https://github.com/nodejs/node/pull/41547)
- \[[`43c12f4962`](https://github.com/nodejs/node/commit/43c12f4962)] - **src,buffer**: evaluate THROW_AND_RETURN_IF_OOB() expression only once (Darshan Sen) [#41945](https://github.com/nodejs/node/pull/41945)
- \[[`b8cbb83222`](https://github.com/nodejs/node/commit/b8cbb83222)] - **stream**: add more forEach tests (Benjamin Gruenbaum) [#41937](https://github.com/nodejs/node/pull/41937)
- \[[`f5d7339b25`](https://github.com/nodejs/node/commit/f5d7339b25)] - **stream**: add more filter tests (Benjamin Gruenbaum) [#41936](https://github.com/nodejs/node/pull/41936)
- \[[`77462b6083`](https://github.com/nodejs/node/commit/77462b6083)] - **stream**: revert `map` spec compliance (Benjamin Gruenbaum) [#41931](https://github.com/nodejs/node/pull/41931)
- \[[`3e38506d1b`](https://github.com/nodejs/node/commit/3e38506d1b)] - **stream**: avoid usage of deprecated APIs (Mohammed Keyvanzadeh) [#41871](https://github.com/nodejs/node/pull/41871)
- \[[`5195479919`](https://github.com/nodejs/node/commit/5195479919)] - **stream**: refactor to use more validators (Mohammed Keyvanzadeh) [#41871](https://github.com/nodejs/node/pull/41871)
- \[[`07463b3374`](https://github.com/nodejs/node/commit/07463b3374)] - **stream**: use standard for loop instead of for..of (Mohammed Keyvanzadeh) [#41871](https://github.com/nodejs/node/pull/41871)
- \[[`d9008915fe`](https://github.com/nodejs/node/commit/d9008915fe)] - **test**: remove test-stdout-close-catch flaky designation (Rich Trott) [#42045](https://github.com/nodejs/node/pull/42045)
- \[[`fe66d2b98c`](https://github.com/nodejs/node/commit/fe66d2b98c)] - **test**: check that fetch globals are disabled by default (Michaël Zasso) [#42015](https://github.com/nodejs/node/pull/42015)
- \[[`bcf53a89f7`](https://github.com/nodejs/node/commit/bcf53a89f7)] - **test**: improve vm test coverage (Yoshiki Kurihara) [#41847](https://github.com/nodejs/node/pull/41847)
- \[[`9825ceddeb`](https://github.com/nodejs/node/commit/9825ceddeb)] - **test**: run webmessaging/broadcastchannel WPT (Ben Noordhuis) [#41962](https://github.com/nodejs/node/pull/41962)
- \[[`f8e302c813`](https://github.com/nodejs/node/commit/f8e302c813)] - **test**: fix names of tests marked flaky on IBM i (Richard Lau) [#41984](https://github.com/nodejs/node/pull/41984)
- \[[`ab73bde8c1`](https://github.com/nodejs/node/commit/ab73bde8c1)] - **test**: fix typographical error (Rich Trott) [#41983](https://github.com/nodejs/node/pull/41983)
- \[[`520dcbc9f5`](https://github.com/nodejs/node/commit/520dcbc9f5)] - **test**: fix intl tests on small-icu builds (Antoine du Hamel) [#41939](https://github.com/nodejs/node/pull/41939)
- \[[`83b913cb90`](https://github.com/nodejs/node/commit/83b913cb90)] - **test**: improve code coverage for streams/duplexify (Erick Wendel) [#41862](https://github.com/nodejs/node/pull/41862)
- \[[`dc14bd85ee`](https://github.com/nodejs/node/commit/dc14bd85ee)] - **test**: remove lint rule for setTimeout() arguments (Rich Trott) [#41901](https://github.com/nodejs/node/pull/41901)
- \[[`76f0f1c06d`](https://github.com/nodejs/node/commit/76f0f1c06d)] - **test**: move test-crypto-engine to addon (Michael Dawson) [#41830](https://github.com/nodejs/node/pull/41830)
- \[[`b1505a1250`](https://github.com/nodejs/node/commit/b1505a1250)] - **tls**: avoid throw in onerror for bad TLSSocket obj (Valters Jansons) [#41523](https://github.com/nodejs/node/pull/41523)
- \[[`2cdbf0304b`](https://github.com/nodejs/node/commit/2cdbf0304b)] - **tools**: update lint-md-dependencies to rollup\@2.67.3 (Node.js GitHub Bot) [#42057](https://github.com/nodejs/node/pull/42057)
- \[[`6742625126`](https://github.com/nodejs/node/commit/6742625126)] - **tools**: enable es2022 env in ESLint config (Michaël Zasso) [#42043](https://github.com/nodejs/node/pull/42043)
- \[[`896ccc13ce`](https://github.com/nodejs/node/commit/896ccc13ce)] - **tools**: add `console` to the list of restricted globals (Antoine du Hamel) [#42012](https://github.com/nodejs/node/pull/42012)
- \[[`1b4ee6cfd9`](https://github.com/nodejs/node/commit/1b4ee6cfd9)] - **tools**: lint deprecation codes (Antoine du Hamel) [#41992](https://github.com/nodejs/node/pull/41992)
- \[[`e895e6422f`](https://github.com/nodejs/node/commit/e895e6422f)] - **tools**: fix bugs in prefer-primordials linter rule (Antoine du Hamel) [#42010](https://github.com/nodejs/node/pull/42010)
- \[[`47bb7dd52c`](https://github.com/nodejs/node/commit/47bb7dd52c)] - **tools**: add timers functions to the list of restricted globals (Antoine du Hamel) [#42013](https://github.com/nodejs/node/pull/42013)
- \[[`afeed46578`](https://github.com/nodejs/node/commit/afeed46578)] - **tools**: reduce list of globals in ESLint config (Rich Trott) [#41996](https://github.com/nodejs/node/pull/41996)
- \[[`2e905a63d8`](https://github.com/nodejs/node/commit/2e905a63d8)] - **tools**: update remark-preset-lint-node to 3.3.1 (Rich Trott) [#41999](https://github.com/nodejs/node/pull/41999)
- \[[`35832feb6e`](https://github.com/nodejs/node/commit/35832feb6e)] - **tools**: update ESLint to 8.9.0 (Luigi Pinca) [#41973](https://github.com/nodejs/node/pull/41973)
- \[[`06117c084e`](https://github.com/nodejs/node/commit/06117c084e)] - **tools**: update lint-md-dependencies to rollup\@2.67.2 (Node.js GitHub Bot) [#41941](https://github.com/nodejs/node/pull/41941)
- \[[`f30b5e3a6d`](https://github.com/nodejs/node/commit/f30b5e3a6d)] - **tools**: fix typo in prefer-primordials.js (Ikko Ashimine) [#41891](https://github.com/nodejs/node/pull/41891)
- \[[`cfafb45c2b`](https://github.com/nodejs/node/commit/cfafb45c2b)] - **tools,lib**: remove `global` and `Intl` from the list of globals (Antoine du Hamel) [#42014](https://github.com/nodejs/node/pull/42014)
- \[[`ba35b6ccd1`](https://github.com/nodejs/node/commit/ba35b6ccd1)] - **worker**: fix typo in debug statement (Antoine du Hamel) [#42011](https://github.com/nodejs/node/pull/42011)

Windows 32-bit Installer: https://nodejs.org/dist/v17.6.0/node-v17.6.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v17.6.0/node-v17.6.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v17.6.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v17.6.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v17.6.0/node-v17.6.0.pkg \
macOS Apple Silicon 64-bit Binary: https://nodejs.org/dist/v17.6.0/node-v17.6.0-darwin-arm64.tar.gz \
macOS Intel 64-bit Binary: https://nodejs.org/dist/v17.6.0/node-v17.6.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v17.6.0/node-v17.6.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v17.6.0/node-v17.6.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v17.6.0/node-v17.6.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v17.6.0/node-v17.6.0-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v17.6.0/node-v17.6.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v17.6.0/node-v17.6.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v17.6.0/node-v17.6.0.tar.gz \
Other release files: https://nodejs.org/dist/v17.6.0/ \
Documentation: https://nodejs.org/docs/v17.6.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

484d55ba3921f3ab056957517cd39f72cb787e21c1ceb91135e8d9d4c6a56efa  node-v17.6.0-aix-ppc64.tar.gz
801ec54f30e43c0513aa390b0a5e67afd2287c3bd81861afa744df6e7e8c109f  node-v17.6.0-darwin-arm64.tar.gz
6dc2df29a1a5d896a496f2824b5724a1c31b9fe585a6dfdd67597276d863054a  node-v17.6.0-darwin-arm64.tar.xz
0a88e772b11eb0145272ee70bf2785f1c159ce6783237dc7b4f865ce97c8f916  node-v17.6.0-darwin-x64.tar.gz
649915cc62ed253be86fc59d1686f2a91fad35257226ebd34e2d8183d93b78bb  node-v17.6.0-darwin-x64.tar.xz
7b244a30cc6dc0f0624538403532f907818f891b6148c6dc5f3e14431fb8f10e  node-v17.6.0-headers.tar.gz
4cb8ecd9c9350045bf2a141e5b62a826cb451f74f2b9dacf58cb5c34b3cbc23b  node-v17.6.0-headers.tar.xz
287b1e42aff686563ee80165c14e9246370cd2e9ae4662787493964dffed1da9  node-v17.6.0-linux-arm64.tar.gz
67a18317778ac56c1c976d0bdac8c39cc053b50c7f1ecb059c807573593c2d6f  node-v17.6.0-linux-arm64.tar.xz
31786cf6387c85a34f1eb85be5838facaad40f50f61030557e42a4af4bb31294  node-v17.6.0-linux-armv7l.tar.gz
4107c8bb9bbe43e805fe9ff54d5e7156e799857ce4835aa393221b8df86c1f19  node-v17.6.0-linux-armv7l.tar.xz
f54c3a00e0fd949c24268a8eff25d6b8f1da1d33cf1ccb8e29fe05d5375b0101  node-v17.6.0-linux-ppc64le.tar.gz
ee6be73393b7b37d8c6d8d1ff7a22ba779b0480632d128ea4aadf35c34acc2f0  node-v17.6.0-linux-ppc64le.tar.xz
e745b97dbef89b85d76c44d393b2ed9d8969f09dafc3824d7e65d37109897409  node-v17.6.0-linux-s390x.tar.gz
6fbb134c24dd978cd063682b3312954ebb454fa48fda10d3315105e505509be3  node-v17.6.0-linux-s390x.tar.xz
de9596fda9cc88451d03146278806687e954c03413e8aa0ee98ad46442d6cb1c  node-v17.6.0-linux-x64.tar.gz
65728b619e8c498c32e592433fff0057a31ab5256407a659f0cab880001e25f1  node-v17.6.0-linux-x64.tar.xz
b2afd457920573a998f9a4bc5b222154b5f3e7a58ceb855fbdad65b9f626988f  node-v17.6.0.pkg
bef8773c632e70ac4b76964a78f1eb48b597588e23e90275f2194c701e194592  node-v17.6.0.tar.gz
ea6aaa70aba9c974ee145f19af36e7edd06b07017b4b2c697e337812080d83fd  node-v17.6.0.tar.xz
3b858acbe24115b93a7a93d4c4d27c0f28c85eb33f440bd74bf07c1c760bb1ec  node-v17.6.0-win-x64.7z
eb9ff1dde916436716fe9054e6b5fd310e7f6bac3599bbd0cb335d4ac8b0cc96  node-v17.6.0-win-x64.zip
7198fd3482a528f404c28c74bfdd202a57b138b8ad8911968941ec25761c520d  node-v17.6.0-win-x86.7z
a42628be3f534db7b0afea475599ef1fdc531fe63006873b11bc3c305f11d94f  node-v17.6.0-win-x86.zip
ef3f0172f25a3868f053ebaf18a9286c23c602b3a8dabab9dd773a7265023b68  node-v17.6.0-x64.msi
dfd6925eb8b2045b1e0e479df53dc3ca591077168d5fbdf2e9532b0475d71455  node-v17.6.0-x86.msi
7b47df21d0f089efdcdf03f1596a7c53414083e84e296cad4119723fed263bab  win-x64/node.exe
f2d246cf6ebda02d031e9c9a18d0ab9c7ca9f10b1828a5a9c8d484ef35cc5b9e  win-x64/node.lib
578979a9a40dbb49a4bdc100c19802f3c65e8dfe6f0a863afe51803b8a342baa  win-x64/node_pdb.7z
1466640e2035ad7fbecb530f27a1afae9136b728cefdf6c7cd2f21fda22599a7  win-x64/node_pdb.zip
e9a22257b6be0ba8cdd5230b4b8b5ae97fec065b8b278fa95f44200b1fb1e1b8  win-x86/node.exe
027e0af731057cca0fad701f184101818aa64f1ae72d5a211e7b31709b13f5f4  win-x86/node.lib
0441ad0055cea14a175f6621791dd0b12d6ab609a51548653f426dc469fa9679  win-x86/node_pdb.7z
2367075a4f83fe4a052824d9f26466aec27e3e2a0845cc560afa6fb936895742  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQEzBAEBCAAdFiEETtd49TnjY0x3nIfG1wYoSKGrAFwFAmIVeE4ACgkQ1wYoSKGr
AFyItgf/XXiy6JcW1zM6EppHh74ExwC+QuMmodR/HoFiQTGlbW0K4m0YAnTZQodd
UTktPLHJPkVnZxdQn+QJiW9yYPqN8BT/FkRg2QZNWARRk0tv/taCWuzrrI/yfONK
zp3yXN7jJLa2aFc34/c3LMUiy0/N3ufiWYyGeLVzqA8oLOmugbo81xqMccQpBgdZ
kRZFYvfUyU9PNDuLNQcw/xDX8atouEIsPjFDzA1/zd4FGtezgZY9BIpxC8nQqrKU
S4AG8AWM/sQlB7JEcN6mfJt34gRMGKL+HeFXTgTQCa6Ixi8jNX0ZEEHMGMobi7Eg
bqBnJ80nbCKYxKDWbJLe2DzVo6iGig==
=P9s2
-----END PGP SIGNATURE-----

```
