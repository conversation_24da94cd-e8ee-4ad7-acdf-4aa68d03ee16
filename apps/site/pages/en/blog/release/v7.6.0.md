---
date: '2017-02-22T15:48:48.856Z'
category: release
title: Node v7.6.0 (Current)
layout: blog-post
author: <PERSON><PERSON> <PERSON><PERSON>
---

### Notable changes

- **deps**:
  - update V8 to 5.5 (<PERSON><PERSON><PERSON>) [#11029](https://github.com/nodejs/node/pull/11029)
  - upgrade libuv to 1.11.0 (c<PERSON><PERSON><PERSON>) [#11094](https://github.com/nodejs/node/pull/11094)
  - add node-inspect 1.10.4 (<PERSON>) [#10187](https://github.com/nodejs/node/pull/10187)
  - upgrade zlib to 1.2.11 (<PERSON>) [#10980](https://github.com/nodejs/node/pull/10980)
- **lib**: build `node inspect` into `node` (<PERSON>) [#10187](https://github.com/nodejs/node/pull/10187)
- **crypto**: Remove expired certs from CNNIC whitelist (<PERSON><PERSON><PERSON>) [#9469](https://github.com/nodejs/node/pull/9469)
- **inspector**: add --inspect-brk (<PERSON>) [#11149](https://github.com/nodejs/node/pull/11149)
- **fs**: allow WHATWG URL objects as paths (James M Snell) [#10739](https://github.com/nodejs/node/pull/10739)
- **src**: support UTF-8 in compiled-in JS source files (Ben Noordhuis) [#11129](https://github.com/nodejs/node/pull/11129)
- **url**: extend url.format to support WHATWG URL (James M Snell) [#10857](https://github.com/nodejs/node/pull/10857)

### Commits

- [[`5059b6fcee`](https://github.com/nodejs/node/commit/5059b6fcee)] - **benchmark**: fix typos (Nikolai Vavilov) [#11287](https://github.com/nodejs/node/pull/11287)
- [[`b4f3a300de`](https://github.com/nodejs/node/commit/b4f3a300de)] - **benchmark**: URLSearchParams v.s. querystring (Joyee Cheung) [#11170](https://github.com/nodejs/node/pull/11170)
- [[`6d2797bd80`](https://github.com/nodejs/node/commit/6d2797bd80)] - **benchmark**: fix first call to URL in useWHATWG (Joyee Cheung) [#11170](https://github.com/nodejs/node/pull/11170)
- [[`8f34181b95`](https://github.com/nodejs/node/commit/8f34181b95)] - **benchmark**: add assert.deep\[Strict\]Equal benchmarks (Joyee Cheung) [#11092](https://github.com/nodejs/node/pull/11092)
- [[`94555c949a`](https://github.com/nodejs/node/commit/94555c949a)] - **benchmark**: simplify URLSearchParams import (Timothy Gu) [#11111](https://github.com/nodejs/node/pull/11111)
- [[`599c947276`](https://github.com/nodejs/node/commit/599c947276)] - **benchmarks**: add spread operator benchmark (James M Snell) [#11227](https://github.com/nodejs/node/pull/11227)
- [[`8fdfa08ed0`](https://github.com/nodejs/node/commit/8fdfa08ed0)] - **(SEMVER-MINOR)** **build**: add node-inspect integration test (Jan Krems) [#10187](https://github.com/nodejs/node/pull/10187)
- [[`67d4dc061c`](https://github.com/nodejs/node/commit/67d4dc061c)] - **build**: clear stalled jobs on POSIX CI hosts (Rich Trott) [#11246](https://github.com/nodejs/node/pull/11246)
- [[`ae39dcbffb`](https://github.com/nodejs/node/commit/ae39dcbffb)] - **build**: disable C4267 conversion compiler warning (Ben Noordhuis) [#11205](https://github.com/nodejs/node/pull/11205)
- [[`92ed2b5001`](https://github.com/nodejs/node/commit/92ed2b5001)] - **(SEMVER-MINOR)** **build**: support for mips64el (nanxiongchao) [#10991](https://github.com/nodejs/node/pull/10991)
- [[`1dc438fd8b`](https://github.com/nodejs/node/commit/1dc438fd8b)] - **crypto**: remove unused access of tlsext_hostname (David Benjamin) [#10882](https://github.com/nodejs/node/pull/10882)
- [[`7af03ba3f6`](https://github.com/nodejs/node/commit/7af03ba3f6)] - **crypto**: Remove expired certs from CNNIC whitelist (Shigeki Ohtsu) [#9469](https://github.com/nodejs/node/pull/9469)
- [[`5e98e34648`](https://github.com/nodejs/node/commit/5e98e34648)] - **crypto**: add cert check issued by StartCom/WoSign (Shigeki Ohtsu) [#9469](https://github.com/nodejs/node/pull/9469)
- [[`af0154535c`](https://github.com/nodejs/node/commit/af0154535c)] - **deps**: upgrade zlib to 1.2.11 (Sam Roberts) [#10980](https://github.com/nodejs/node/pull/10980)
- [[`85f54908bf`](https://github.com/nodejs/node/commit/85f54908bf)] - **(SEMVER-MINOR)** **deps**: add node-inspect 1.10.2 (Jan Krems) [#10187](https://github.com/nodejs/node/pull/10187)
- [[`445794e0c9`](https://github.com/nodejs/node/commit/445794e0c9)] - **deps**: upgrade libuv to 1.11.0 (cjihrig) [#11094](https://github.com/nodejs/node/pull/11094)
- [[`20127e0c0a`](https://github.com/nodejs/node/commit/20127e0c0a)] - **deps**: back-port b049d1a from V8 upstream (Ben Noordhuis) [#11204](https://github.com/nodejs/node/pull/11204)
- [[`5446fa7e8c`](https://github.com/nodejs/node/commit/5446fa7e8c)] - **(SEMVER-MINOR)** **deps**: work around SmartOS 14 incompatibility (Michaël Zasso) [#11029](https://github.com/nodejs/node/pull/11029)
- [[`028bb632b2`](https://github.com/nodejs/node/commit/028bb632b2)] - **(SEMVER-MINOR)** **deps**: revert breaking UTF-8 decoder changes in V8 (Michaël Zasso) [#11029](https://github.com/nodejs/node/pull/11029)
- [[`22e2288f3a`](https://github.com/nodejs/node/commit/22e2288f3a)] - **(SEMVER-MINOR)** **deps**: ensure V8 5.4 ABI compatibility (Michaël Zasso) [#11029](https://github.com/nodejs/node/pull/11029)
- [[`53e00e1617`](https://github.com/nodejs/node/commit/53e00e1617)] - **(SEMVER-MINOR)** **deps**: limit regress/regress-crbug-514081 v8 test (Michael Dawson) [#11029](https://github.com/nodejs/node/pull/11029)
- [[`7fea966a1d`](https://github.com/nodejs/node/commit/7fea966a1d)] - **(SEMVER-MINOR)** **deps**: cherry-pick workaround for clang-3.4 ICE (Michaël Zasso) [#11029](https://github.com/nodejs/node/pull/11029)
- [[`61870b429a`](https://github.com/nodejs/node/commit/61870b429a)] - **(SEMVER-MINOR)** **deps**: update V8 to 5.5.372.40 (Michaël Zasso) [#11029](https://github.com/nodejs/node/pull/11029)
- [[`d9ed965ae1`](https://github.com/nodejs/node/commit/d9ed965ae1)] - **dgram**: remove this aliases (cjihrig) [#11243](https://github.com/nodejs/node/pull/11243)
- [[`2f1ce2952d`](https://github.com/nodejs/node/commit/2f1ce2952d)] - **doc**: update link to V8 Embedder's guide (Franziska Hinkelmann) [#11336](https://github.com/nodejs/node/pull/11336)
- [[`3db54c93f8`](https://github.com/nodejs/node/commit/3db54c93f8)] - **doc**: update email and add personal pronoun (JungMinu) [#11318](https://github.com/nodejs/node/pull/11318)
- [[`1b08f766b1`](https://github.com/nodejs/node/commit/1b08f766b1)] - **doc**: drop "and io.js" from release section (Ben Noordhuis) [#11054](https://github.com/nodejs/node/pull/11054)
- [[`a5e8176fee`](https://github.com/nodejs/node/commit/a5e8176fee)] - **doc**: improve consistency in documentation titles (Vse Mozhet Byt) [#11230](https://github.com/nodejs/node/pull/11230)
- [[`5d2ba44fca`](https://github.com/nodejs/node/commit/5d2ba44fca)] - **doc**: edit maxBuffer/Unicode paragraph for clarity (Rich Trott) [#11228](https://github.com/nodejs/node/pull/11228)
- [[`d5b1a4b265`](https://github.com/nodejs/node/commit/d5b1a4b265)] - **doc**: clarify the behavior of Buffer.byteLength (Nikolai Vavilov) [#11238](https://github.com/nodejs/node/pull/11238)
- [[`0d4b0edb56`](https://github.com/nodejs/node/commit/0d4b0edb56)] - **doc**: add links between cork() and uncork() (Matteo Collina) [#11222](https://github.com/nodejs/node/pull/11222)
- [[`266c41c2b1`](https://github.com/nodejs/node/commit/266c41c2b1)] - **doc**: add and fix System Error properties (Daiki Arai) [#10986](https://github.com/nodejs/node/pull/10986)
- [[`71f8a23da4`](https://github.com/nodejs/node/commit/71f8a23da4)] - **doc**: fix typo in dgram doc (Rich Trott) [#11186](https://github.com/nodejs/node/pull/11186)
- [[`73b32a31e0`](https://github.com/nodejs/node/commit/73b32a31e0)] - **doc**: remove extraneous paragraph from assert doc (Rich Trott) [#11174](https://github.com/nodejs/node/pull/11174)
- [[`abae26421e`](https://github.com/nodejs/node/commit/abae26421e)] - **doc**: improve testing guide (Joyee Cheung) [#11150](https://github.com/nodejs/node/pull/11150)
- [[`803f6b3091`](https://github.com/nodejs/node/commit/803f6b3091)] - **doc**: fix linting command for vcbuild (Rich Trott) [#11151](https://github.com/nodejs/node/pull/11151)
- [[`177e9797cd`](https://github.com/nodejs/node/commit/177e9797cd)] - **doc**: add common.WPT to test README (Rich Trott) [#11127](https://github.com/nodejs/node/pull/11127)
- [[`1fbbcc3c07`](https://github.com/nodejs/node/commit/1fbbcc3c07)] - **doc**: add not-an-aardvark as ESLint contact (Rich Trott) [#11169](https://github.com/nodejs/node/pull/11169)
- [[`5649174dda`](https://github.com/nodejs/node/commit/5649174dda)] - **doc**: typographical fixes in COLLABORATOR_GUIDE.md (Anna Henningsen) [#11163](https://github.com/nodejs/node/pull/11163)
- [[`ae33a15d01`](https://github.com/nodejs/node/commit/ae33a15d01)] - **doc**: fix "initial delay" link in http.md (Timo Tijhof) [#11108](https://github.com/nodejs/node/pull/11108)
- [[`5d58756b41`](https://github.com/nodejs/node/commit/5d58756b41)] - **doc**: remove assertions about assert (Rich Trott) [#11113](https://github.com/nodejs/node/pull/11113)
- [[`3ebe306bb0`](https://github.com/nodejs/node/commit/3ebe306bb0)] - **doc**: edit stability text for clarity and style (Rich Trott) [#11112](https://github.com/nodejs/node/pull/11112)
- [[`535492d321`](https://github.com/nodejs/node/commit/535492d321)] - **doc**: clarify msg when doc/api/cli.md not updated (Stewart X Addison) [#10872](https://github.com/nodejs/node/pull/10872)
- [[`3ae25a0bca`](https://github.com/nodejs/node/commit/3ae25a0bca)] - **doc**: add personal pronouns option (Rich Trott) [#11089](https://github.com/nodejs/node/pull/11089)
- [[`265a59b60f`](https://github.com/nodejs/node/commit/265a59b60f)] - **doc**: replace newlines in deprecation with space (Sakthipriyan Vairamani (thefourtheye)) [#11074](https://github.com/nodejs/node/pull/11074)
- [[`598d35c087`](https://github.com/nodejs/node/commit/598d35c087)] - **doc**: fix confusing example in dns.md (Vse Mozhet Byt) [#11022](https://github.com/nodejs/node/pull/11022)
- [[`989d2cdbac`](https://github.com/nodejs/node/commit/989d2cdbac)] - **doc**: edit CONTRIBUTING.md for clarity (Rich Trott) [#11045](https://github.com/nodejs/node/pull/11045)
- [[`6cf06cf518`](https://github.com/nodejs/node/commit/6cf06cf518)] - **(SEMVER-MINOR)** **fs**: allow WHATWG URL and file: URLs as paths (James M Snell) [#10739](https://github.com/nodejs/node/pull/10739)
- [[`9339891b07`](https://github.com/nodejs/node/commit/9339891b07)] - **fs**: re-enable watch facility in AIX (Gireesh Punathil) [#10085](https://github.com/nodejs/node/pull/10085)
- [[`2952512b86`](https://github.com/nodejs/node/commit/2952512b86)] - **lib**: replace \u2019 with regular ascii quote (Ben Noordhuis) [#11129](https://github.com/nodejs/node/pull/11129)
- [[`3596d156c1`](https://github.com/nodejs/node/commit/3596d156c1)] - **(SEMVER-MINOR)** **lib**: build `node inspect` into `node` (Anna Henningsen) [#10187](https://github.com/nodejs/node/pull/10187)
- [[`3074c6de7e`](https://github.com/nodejs/node/commit/3074c6de7e)] - **meta**: adding Italo A. Casas PGP Fingerprint (Italo A. Casas) [#11202](https://github.com/nodejs/node/pull/11202)
- [[`e530b5ae43`](https://github.com/nodejs/node/commit/e530b5ae43)] - **meta**: remove Chris Dickinson from CTC (Chris Dickinson) [#11267](https://github.com/nodejs/node/pull/11267)
- [[`17314eb9ca`](https://github.com/nodejs/node/commit/17314eb9ca)] - **meta**: add explicit deprecation and semver-major policy (James M Snell) [#7964](https://github.com/nodejs/node/pull/7964)
- [[`6a45c81edd`](https://github.com/nodejs/node/commit/6a45c81edd)] - **readline**: update 6 comparions to strict (Umair Ishaq) [#11078](https://github.com/nodejs/node/pull/11078)
- [[`fe2f058f17`](https://github.com/nodejs/node/commit/fe2f058f17)] - **(SEMVER-MINOR)** **repl**: remove workaround for function redefinition (Michaël Zasso) [#11029](https://github.com/nodejs/node/pull/11029)
- [[`3380cd5fdb`](https://github.com/nodejs/node/commit/3380cd5fdb)] - **src**: support UTF-8 in compiled-in JS source files (Ben Noordhuis) [#11129](https://github.com/nodejs/node/pull/11129)
- [[`308df11658`](https://github.com/nodejs/node/commit/308df11658)] - **src**: fix delete operator on vm context (Franziska Hinkelmann) [#11266](https://github.com/nodejs/node/pull/11266)
- [[`af06f62e35`](https://github.com/nodejs/node/commit/af06f62e35)] - **src**: fix -Wunused-result compiler warning (Ben Noordhuis) [#11197](https://github.com/nodejs/node/pull/11197)
- [[`44b17a21ad`](https://github.com/nodejs/node/commit/44b17a21ad)] - **src**: refactor CopyProperties to remove JS (AnnaMag) [#11102](https://github.com/nodejs/node/pull/11102)
- [[`ce3dcca619`](https://github.com/nodejs/node/commit/ce3dcca619)] - **src**: update v8_platform.StartInspector signature (Myk Melez) [#11157](https://github.com/nodejs/node/pull/11157)
- [[`d8a5e1c37f`](https://github.com/nodejs/node/commit/d8a5e1c37f)] - **src**: don't overwrite non-writable vm globals (Franziska Hinkelmann) [#11109](https://github.com/nodejs/node/pull/11109)
- [[`9264131fb3`](https://github.com/nodejs/node/commit/9264131fb3)] - **src**: unconsume stream fix in internal http impl (Roee Kasher) [#11015](https://github.com/nodejs/node/pull/11015)
- [[`c5210b203d`](https://github.com/nodejs/node/commit/c5210b203d)] - **src**: remove usage of V8 deprecated API in node_url.cc (Timothy Gu) [#11066](https://github.com/nodejs/node/pull/11066)
- [[`0b64f7fc0e`](https://github.com/nodejs/node/commit/0b64f7fc0e)] - **src, inspector**: add --inspect-brk (Josh Gavant) [#11149](https://github.com/nodejs/node/pull/11149)
- [[`0d52aced0c`](https://github.com/nodejs/node/commit/0d52aced0c)] - **stream**: move legacy to lib/internal dir (yorkie) [#8197](https://github.com/nodejs/node/pull/8197)
- [[`0610cc707b`](https://github.com/nodejs/node/commit/0610cc707b)] - **test**: skip IPv6 test on non-IPv6 systems (Rich Trott) [#11432](https://github.com/nodejs/node/pull/11432)
- [[`93d3a3a6b5`](https://github.com/nodejs/node/commit/93d3a3a6b5)] - **test**: add coverage for dgram \_createSocketHandle() (cjihrig) [#11291](https://github.com/nodejs/node/pull/11291)
- [[`b140dec930`](https://github.com/nodejs/node/commit/b140dec930)] - **test**: refactor test-repl-sigint-nested-eval (Rich Trott) [#11303](https://github.com/nodejs/node/pull/11303)
- [[`1085a4675a`](https://github.com/nodejs/node/commit/1085a4675a)] - **test**: skip when openssl CLI doesn't exist (Sota Yamashita) [#11095](https://github.com/nodejs/node/pull/11095)
- [[`6f866ae002`](https://github.com/nodejs/node/commit/6f866ae002)] - **test**: improve punycode test coverage (Sebastian Van Sande) [#11144](https://github.com/nodejs/node/pull/11144)
- [[`68eb97442d`](https://github.com/nodejs/node/commit/68eb97442d)] - **test**: cover cluster error during dgram socket bind (cjihrig) [#11295](https://github.com/nodejs/node/pull/11295)
- [[`5350f04e42`](https://github.com/nodejs/node/commit/5350f04e42)] - **test**: refactor test-repl-sigint (Rich Trott) [#11309](https://github.com/nodejs/node/pull/11309)
- [[`1f3eee4f5d`](https://github.com/nodejs/node/commit/1f3eee4f5d)] - **test**: increase setMulticastLoopback() coverage (cjihrig) [#11277](https://github.com/nodejs/node/pull/11277)
- [[`6ee11f82b3`](https://github.com/nodejs/node/commit/6ee11f82b3)] - **test**: refactor test-dgram-address.js (cjihrig) [#11271](https://github.com/nodejs/node/pull/11271)
- [[`d2ee7e20b2`](https://github.com/nodejs/node/commit/d2ee7e20b2)] - **test**: refactor test-readline-keys (Rich Trott) [#11281](https://github.com/nodejs/node/pull/11281)
- [[`f096235d04`](https://github.com/nodejs/node/commit/f096235d04)] - **test**: improve test-assert.js (jobala) [#11193](https://github.com/nodejs/node/pull/11193)
- [[`b4056994c4`](https://github.com/nodejs/node/commit/b4056994c4)] - **test**: improve test-http-agent-destroyed-socket.js (Shubheksha Jalan) [#11201](https://github.com/nodejs/node/pull/11201)
- [[`803be085be`](https://github.com/nodejs/node/commit/803be085be)] - **test**: querystring.escape with multibyte characters (Daijiro Wachi) [#11251](https://github.com/nodejs/node/pull/11251)
- [[`809aea3081`](https://github.com/nodejs/node/commit/809aea3081)] - **test**: refactor test-dgram-setBroadcast.js (cjihrig) [#11252](https://github.com/nodejs/node/pull/11252)
- [[`69f5a754e2`](https://github.com/nodejs/node/commit/69f5a754e2)] - **test**: add vm module edge cases (Franziska Hinkelmann) [#11265](https://github.com/nodejs/node/pull/11265)
- [[`2f15efb05b`](https://github.com/nodejs/node/commit/2f15efb05b)] - **test**: adapt test-debugger-pid to localized Windows (Vse Mozhet Byt) [#11270](https://github.com/nodejs/node/pull/11270)
- [[`5e5d72eb5a`](https://github.com/nodejs/node/commit/5e5d72eb5a)] - **test**: remove nan + weak (Ben Noordhuis) [#11239](https://github.com/nodejs/node/pull/11239)
- [[`969b85cdf5`](https://github.com/nodejs/node/commit/969b85cdf5)] - **test**: remove dependency on node-weak (Ben Noordhuis) [#11239](https://github.com/nodejs/node/pull/11239)
- [[`0cded6aac1`](https://github.com/nodejs/node/commit/0cded6aac1)] - **test**: don't call process.exit() in gc tests (Ben Noordhuis) [#11239](https://github.com/nodejs/node/pull/11239)
- [[`7ff32bf705`](https://github.com/nodejs/node/commit/7ff32bf705)] - **test**: add coverage for dgram send() errors (cjihrig) [#11248](https://github.com/nodejs/node/pull/11248)
- [[`e1beb9fbfc`](https://github.com/nodejs/node/commit/e1beb9fbfc)] - **test**: add coverage for string array dgram send() (cjihrig) [#11247](https://github.com/nodejs/node/pull/11247)
- [[`2333cd3155`](https://github.com/nodejs/node/commit/2333cd3155)] - **test**: increase dgram ref()/unref() coverage (cjihrig) [#11240](https://github.com/nodejs/node/pull/11240)
- [[`480d4cc9df`](https://github.com/nodejs/node/commit/480d4cc9df)] - **test**: add coverage to dgram receive error case (cjihrig) [#11241](https://github.com/nodejs/node/pull/11241)
- [[`ccd1163b46`](https://github.com/nodejs/node/commit/ccd1163b46)] - **test**: refactor test-fs-buffer (Rich Trott) [#11232](https://github.com/nodejs/node/pull/11232)
- [[`25226ced6a`](https://github.com/nodejs/node/commit/25226ced6a)] - **test**: improve checks in test-path-parse-format (cjihrig) [#11223](https://github.com/nodejs/node/pull/11223)
- [[`540dca1d18`](https://github.com/nodejs/node/commit/540dca1d18)] - **test**: fix incorrect indentation (cjihrig) [#11219](https://github.com/nodejs/node/pull/11219)
- [[`f0eba7811d`](https://github.com/nodejs/node/commit/f0eba7811d)] - **test**: add common.mustNotCall() (cjihrig) [#11152](https://github.com/nodejs/node/pull/11152)
- [[`f6dfc3193a`](https://github.com/nodejs/node/commit/f6dfc3193a)] - **test**: remove obsolete comment from dgram test (ALJCepeda) [#8689](https://github.com/nodejs/node/pull/8689)
- [[`9d5ffa6e49`](https://github.com/nodejs/node/commit/9d5ffa6e49)] - **test**: add test cases to test-readline-keys.js (abouthiroppy) [#10772](https://github.com/nodejs/node/pull/10772)
- [[`7ec6a69a7d`](https://github.com/nodejs/node/commit/7ec6a69a7d)] - **test**: add missing initialization in test-assert (Rich Trott) [#11191](https://github.com/nodejs/node/pull/11191)
- [[`b766dab81c`](https://github.com/nodejs/node/commit/b766dab81c)] - **test**: increase specificity in dgram test (Rich Trott) [#11187](https://github.com/nodejs/node/pull/11187)
- [[`9c729211e4`](https://github.com/nodejs/node/commit/9c729211e4)] - **test**: improve crypto.setEngine coverage to check for errors (Sebastian Van Sande) [#11143](https://github.com/nodejs/node/pull/11143)
- [[`3ca483f4cc`](https://github.com/nodejs/node/commit/3ca483f4cc)] - **test**: throw Error objects instead of literals (Rich Trott) [#11168](https://github.com/nodejs/node/pull/11168)
- [[`8612a004a3`](https://github.com/nodejs/node/commit/8612a004a3)] - **(SEMVER-MINOR)** **test**: move test-vm-function-redefinition to parallel (Franziska Hinkelmann) [#11029](https://github.com/nodejs/node/pull/11029)
- [[`fbd495583e`](https://github.com/nodejs/node/commit/fbd495583e)] - **test**: simplify output handling in repl tests (Rich Trott) [#11124](https://github.com/nodejs/node/pull/11124)
- [[`7f9b436c4b`](https://github.com/nodejs/node/commit/7f9b436c4b)] - **test**: make module testing stricter (Rich Trott) [#11116](https://github.com/nodejs/node/pull/11116)
- [[`cf098688e4`](https://github.com/nodejs/node/commit/cf098688e4)] - **test**: fix test.py command line options processing (Julien Gilli) [#11153](https://github.com/nodejs/node/pull/11153)
- [[`e9f6bc60e9`](https://github.com/nodejs/node/commit/e9f6bc60e9)] - **test**: improve coverage on removeListeners functions (matsuda-koushi) [#11140](https://github.com/nodejs/node/pull/11140)
- [[`815e668209`](https://github.com/nodejs/node/commit/815e668209)] - **test**: add --abort-on-timeout option to test.py (Julien Gilli) [#11086](https://github.com/nodejs/node/pull/11086)
- [[`cf3700b0e8`](https://github.com/nodejs/node/commit/cf3700b0e8)] - **test**: fix timing sensitivity in debugger test (Ali Ijaz Sheikh) [#11008](https://github.com/nodejs/node/pull/11008)
- [[`3d35dcff9a`](https://github.com/nodejs/node/commit/3d35dcff9a)] - **test**: make test-fs-access stricter (Rich Trott) [#11087](https://github.com/nodejs/node/pull/11087)
- [[`e2d9c23e72`](https://github.com/nodejs/node/commit/e2d9c23e72)] - **test**: use repeat() instead of new Array().join() (Jackson Tian) [#11071](https://github.com/nodejs/node/pull/11071)
- [[`ea5bef5efe`](https://github.com/nodejs/node/commit/ea5bef5efe)] - **test**: add path.join's test (Yuta Hiroto) [#11063](https://github.com/nodejs/node/pull/11063)
- [[`8d2a9138fc`](https://github.com/nodejs/node/commit/8d2a9138fc)] - **test**: improve error messages in test-npm-install (Gonen Dukas) [#11027](https://github.com/nodejs/node/pull/11027)
- [[`8ac6a709b9`](https://github.com/nodejs/node/commit/8ac6a709b9)] - **test**: add fs-assert-encoding's test (abouthiroppy) [#10913](https://github.com/nodejs/node/pull/10913)
- [[`e4b139d300`](https://github.com/nodejs/node/commit/e4b139d300)] - **timer**: remove duplicated word in comment (asafdav2) [#11323](https://github.com/nodejs/node/pull/11323)
- [[`a2948fbe74`](https://github.com/nodejs/node/commit/a2948fbe74)] - **tools**: enable ES2017 syntax support in ESLint (Michaël Zasso) [#11211](https://github.com/nodejs/node/pull/11211)
- [[`7e465b9c21`](https://github.com/nodejs/node/commit/7e465b9c21)] - **tools**: add compile_commands.json gyp generator (Ben Noordhuis) [#7986](https://github.com/nodejs/node/pull/7986)
- [[`2dc8aac1a9`](https://github.com/nodejs/node/commit/2dc8aac1a9)] - **tools**: enable no-throw-literal ESLint rule (Rich Trott) [#11168](https://github.com/nodejs/node/pull/11168)
- [[`8547871ea2`](https://github.com/nodejs/node/commit/8547871ea2)] - **url**: fix setting `url.search` to the empty string (Timothy Gu) [#11105](https://github.com/nodejs/node/pull/11105)
- [[`322fc20333`](https://github.com/nodejs/node/commit/322fc20333)] - **(SEMVER-MINOR)** **url**: extend url.format to support WHATWG URL (James M Snell) [#10857](https://github.com/nodejs/node/pull/10857)
- [[`cfadbc2661`](https://github.com/nodejs/node/commit/cfadbc2661)] - **util**: improve inspect for AsyncFunction (Michaël Zasso) [#11211](https://github.com/nodejs/node/pull/11211)

Windows 32-bit Installer: https://nodejs.org/dist/v7.6.0/node-v7.6.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v7.6.0/node-v7.6.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v7.6.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v7.6.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v7.6.0/node-v7.6.0.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v7.6.0/node-v7.6.0-darwin-x64.tar.gz \
Linux 32-bit Binary: https://nodejs.org/dist/v7.6.0/node-v7.6.0-linux-x86.tar.xz \
Linux 64-bit Binary: https://nodejs.org/dist/v7.6.0/node-v7.6.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v7.6.0/node-v7.6.0-linux-ppc64le.tar.xz \
Linux PPC BE 64-bit Binary: https://nodejs.org/dist/v7.6.0/node-v7.6.0-linux-ppc64.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v7.6.0/node-v7.6.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v7.6.0/node-v7.6.0-aix-ppc64.tar.gz \
SmartOS 32-bit Binary: https://nodejs.org/dist/v7.6.0/node-v7.6.0-sunos-x86.tar.xz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v7.6.0/node-v7.6.0-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v7.6.0/node-v7.6.0-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v7.6.0/node-v7.6.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v7.6.0/node-v7.6.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v7.6.0/node-v7.6.0.tar.gz \
Other release files: https://nodejs.org/dist/v7.6.0/ \
Documentation: https://nodejs.org/docs/v7.6.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

242445e5353b20b14d3c986dbb7b84b4017982340b4c0c049935e2e983608a7f  node-v7.6.0-aix-ppc64.tar.gz
da91ae27b942151feac38a7ec424c8dc1bdd72a5db5eb6fa792f0fcf4c1e80b0  node-v7.6.0-darwin-x64.tar.gz
7f0a825f9416b9f06b4bcb9c0d0373f617bd7e1c505629669fdee8aa81c5dd54  node-v7.6.0-darwin-x64.tar.xz
562774922e9dcc3a61e4393821a8568fb31183c4ef2254ed513f41c1ca2e65dc  node-v7.6.0-headers.tar.gz
dc034c2c95d11354fcae873a3f894a1d95f85e6325ce4b4acddf44477ebc0896  node-v7.6.0-headers.tar.xz
5d45b0990200431d95696db51094198eeb6a90bdcbd38c317e8fe420d63552d9  node-v7.6.0-linux-arm64.tar.gz
bf1aba762fe0e2da14484afc12b6d3f91b00330e132d29d3343ffb4e983c6240  node-v7.6.0-linux-arm64.tar.xz
32e360e35e1c04379cf8ca6915e9d28ce409521a8b2b58754c7135b464c8feee  node-v7.6.0-linux-armv6l.tar.gz
0d777cacad08268729db112acf5ef910975466503e494fcae86eeadbd0742d10  node-v7.6.0-linux-armv6l.tar.xz
6afb4e68303641a398b6701b36d0af9de774847e7e853679fe7765ef62d04aff  node-v7.6.0-linux-armv7l.tar.gz
707c4685413077f3d0294e3495f64abfa79fb6fe161b4c5c7aea7c0451959d04  node-v7.6.0-linux-armv7l.tar.xz
066a819e5ad16a5ef14f948f187586b3c742632e94ade50e7f3a09153d5979d2  node-v7.6.0-linux-ppc64le.tar.gz
96916ca4f300cc78b28ed037ad2ba9b99ae5278e7de8e73b7acc99fb057f45c9  node-v7.6.0-linux-ppc64le.tar.xz
351caf5e6a3a18f99a8fd98c05c089115a6b91fa2e6484e5fdfab24ff2eeb32f  node-v7.6.0-linux-ppc64.tar.gz
1914d0362ed62810ffcac9a56515a788cd75f1b5508b8d0d7cb0a56871386deb  node-v7.6.0-linux-ppc64.tar.xz
ea071c2e281f310117b8eac7af364bd3bc08d0e0e9b59427befc80074e06c581  node-v7.6.0-linux-s390x.tar.gz
89e70608616b72a7ec4eef03eb4499367ff0d917f0c3443be970f9cdae594418  node-v7.6.0-linux-s390x.tar.xz
0a8da7b260a93dae7c43a6f49b81fed5a3c19689feee67ce711e85b5a218b44e  node-v7.6.0-linux-x64.tar.gz
97c6483fdb4fe8ae43dbcf95733cb7e9c6fa10abd63c5f880890bdc8fbc0ded5  node-v7.6.0-linux-x64.tar.xz
3fafdacd96b6edbedad9ae8201272dd622d4ac5fb0dce5235ec7f7eb498ec984  node-v7.6.0-linux-x86.tar.gz
18736a7d26c55f172abebad13254b913b1be17a0fa6dd7817415252a2f1aca84  node-v7.6.0-linux-x86.tar.xz
e4a06a6d521f3000c969fb82f68df2b5b00723a1dfb62c804283afcf02775751  node-v7.6.0.pkg
675be245f79e3ed6991154780158cd3fe7a5823d0989e59bab0d9ce2c5e7a297  node-v7.6.0-sunos-x64.tar.gz
151c4df389ed5adb457388b401b74a0270942f0ec1b1b4e4bb4750fd1615a630  node-v7.6.0-sunos-x64.tar.xz
f5447f0e8e9f74471346f25c99722f0fffd9939d3f5179f15d0852c4089af5e8  node-v7.6.0-sunos-x86.tar.gz
29db67287ac97e6c309b795b4c212f38773c0ebd692222ae8bf034ed32854202  node-v7.6.0-sunos-x86.tar.xz
809e80265e332fe1a8268e5a73eb219c356810fe86c69fd2d931c52e07211970  node-v7.6.0.tar.gz
6ff9042696fff0b49647f5864e71cb495e554e4f66e61443494210f5e16ab4a9  node-v7.6.0.tar.xz
c8bc4d97a72e114b8dce2c4769444aa006a3a26a0257b15102eaeb438913ff83  node-v7.6.0-win-x64.7z
9cc8fd129483aa64557155842dc8f1e7ed288efeab1a7fbb0210314bc7213058  node-v7.6.0-win-x64.zip
9a6bfa5d86714ab046e0cba272b4bb6553465ac7952570c9f4661ceb5e789db6  node-v7.6.0-win-x86.7z
738604089007e243980db223bdccaa491b693703565a68998355a1184169557e  node-v7.6.0-win-x86.zip
91c67cb35090999abab87485f5c793d95c59d415a0722b9e6b214b842a9edcbd  node-v7.6.0-x64.msi
e678efafda218d0e2c69e57356d65a765fdae747dcee18790360dc8f0c44ab5f  node-v7.6.0-x86.msi
f1ad8802b3cc6cb914514c548eda0c307f1d28fa7b3ad214ec902e141eb091e9  win-x64/node.exe
00536645f19ad4e0b3bb14f472ca56f866635c9bb61f50eef128e1d50cb3d0d0  win-x64/node.lib
e9a846fde142a267a705fcc09489d186f2b8d61abbe102efb13de9ec00d6a9de  win-x64/node_pdb.7z
07245480af59727da08d45744201fd4434f89c0198cc7a484469d4df170d6008  win-x64/node_pdb.zip
d767a2d38ae8f0b4ed9de3d6ecf55235b666721e3ced10f0703244b618dccdc5  win-x86/node.exe
5b65d348828dfc914ac92b1dea8c6eeda631327d7411c23252824fd1fd6dbfd6  win-x86/node.lib
c3003d17f2270ff773dc1639e7ae13ec7929f3a75af85d13cf5b1363a27999ba  win-x86/node_pdb.7z
b83068fb0f19293ed89c07275be622943e7245a3f35570ce78fccd35b718117c  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----
Version: GnuPG v2

iQIcBAEBCAAGBQJYramhAAoJECPv7+k8TP/+GHoQAKh7X4xa59FzHI0KjpmpXmZ+
rscRHfLNq0/R2f5OeDKc0BwNyy44E7+o6X6hy95lTGAbzNPz5ovrBLlalvalQX/r
NB3GnMiwdVZI3ZSzgUpsbt1WwvvTnwhuB0uB4yQi1qUodNWf7m8eTT+8igtY69b3
XzXZRIP9to2vhYVAx5tveat/UTap4d5z8zgw4c9T+vfQ/nGqZcVeY1yDPba11c6e
3kzR2WeuPWXDM5sfYBgg5DUzGrVTl3Rd+fridAiQc7aH9fEow6x5dIQCn/fzuXbj
Cvs5eWmmtqrcjCWKuVuEx++q2p5yIKvOoRVg3ejgCvzliksqYaHLEOnlx/HV4+gb
VsmWpwBWoakfMKoMj/YZbdXAoelB6t9uPo63/m8qe6A5qaGd509qbFbwC8c00/iT
G53bZwpYWwqeqXmJ/e+LBBQ8ugTvOy5ImsyDjlms+0hzRBt1PDgvBcTeOs28Smft
mPeJotG1vuWrEiTOL1qj8KXo1Q8cXkWucpNiLC98kHNlMCWJPpMswk0COJYHg+PV
73L1aVuoVIZnUlj4rKga0KFgsrgApvHnGTdhAwsb5Sa1DlaSkcEdJ2Prl74hCWpK
gs5tGje3/lvAvepsEJAGD85MSHxWf96LVmSw6V7oEwcIdmN2QYUP86JBTtj2U+0L
LKLV4f+z1M5je6nKjefg
=gEN2
-----END PGP SIGNATURE-----

```
