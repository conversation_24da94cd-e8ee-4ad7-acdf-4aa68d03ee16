---
date: '2016-04-21T20:35:53.135Z'
category: release
title: Node v5.11.0 (Stable)
layout: blog-post
author: <PERSON><PERSON>
---

### Notable Changes

- **Buffer**:
  - `Buffer.prototype.compare` can now compare sub-ranges of two Buffers. (<PERSON>) [#5880](https://github.com/nodejs/node/pull/5880)
- **deps**:
  - update to http-parser 2.7.0 (<PERSON><PERSON>) [#6279](https://github.com/nodejs/node/pull/6279)
  - update ESLint to 2.7.0 (silverwind) [#6132](https://github.com/nodejs/node/pull/6132)
- **net**:
  - adds support for passing DNS lookup hints to `createConnection()` (<PERSON>) [#6000](https://github.com/nodejs/node/pull/6000)
- **node**:
  - Make the builtin libraries available for the `--eval` and `--print` CLI options (<PERSON>) [#6207](https://github.com/nodejs/node/pull/6207)
- **npm**:
  - upgrade npm to 3.8.6 (<PERSON>) [#6153](https://github.com/nodejs/node/pull/6153)
- **repl**:
  - Pressing enter in the repl will repeat the last command by default if no input has been received. This behaviour was in node previously and was not removed intentionally. (Rich Trott) [#6090](https://github.com/nodejs/node/pull/6090)
- **src**:
  - add SIGINFO to supported signals (James Reggio) [#6093](https://github.com/nodejs/node/pull/6093)
- **streams**:
  - Fix a regression that caused by net streams requesting multiple chunks synchronously when combined with cork/uncork (Matteo Collina) [#6164](https://github.com/nodejs/node/pull/6164)
- **zlib**:
  - The flushing flag is now configurable allowing for decompression of partial data (Anna Henningsen) [#6069](https://github.com/nodejs/node/pull/6069)

### Commits

- [[`14fcb1dded`](https://github.com/nodejs/node/commit/14fcb1dded)] - **assert**: respect assert.doesNotThrow message. (Ilya Shaisultanov) [#2407](https://github.com/nodejs/node/pull/2407)
- [[`332f7382bb`](https://github.com/nodejs/node/commit/332f7382bb)] - **benchmark**: add module loader benchmark parameter (Brian White) [#5172](https://github.com/nodejs/node/pull/5172)
- [[`473f086a94`](https://github.com/nodejs/node/commit/473f086a94)] - **(SEMVER-MINOR)** **buffer**: add Buffer.prototype.compare by offset (James M Snell) [#5880](https://github.com/nodejs/node/pull/5880)
- [[`d44540f5af`](https://github.com/nodejs/node/commit/d44540f5af)] - **buffer**: standardize array index check (Trevor Norris) [#6084](https://github.com/nodejs/node/pull/6084)
- [[`bd12d72e0c`](https://github.com/nodejs/node/commit/bd12d72e0c)] - **build**: fix make tar-headers for Linux (Gibson Fahnestock) [#5978](https://github.com/nodejs/node/pull/5978)
- [[`3c8d404a82`](https://github.com/nodejs/node/commit/3c8d404a82)] - **build**: allow test-ci to run tests in parallel (Johan Bergström) [#6208](https://github.com/nodejs/node/pull/6208)
- [[`a5f8d0c6ef`](https://github.com/nodejs/node/commit/a5f8d0c6ef)] - **build**: remove -f{data,function}-sections flags (Ben Noordhuis) [#6077](https://github.com/nodejs/node/pull/6077)
- [[`adfb1a4bb0`](https://github.com/nodejs/node/commit/adfb1a4bb0)] - **child_process**: add nullptr checks after allocs (Anna Henningsen) [#6256](https://github.com/nodejs/node/pull/6256)
- [[`1fb40524ee`](https://github.com/nodejs/node/commit/1fb40524ee)] - **(SEMVER-MINOR)** **debugger**: run last command on presssing enter (Rich Trott) [#6090](https://github.com/nodejs/node/pull/6090)
- [[`5305831184`](https://github.com/nodejs/node/commit/5305831184)] - **deps**: update to http-parser 2.7.0 (Fedor Indutny) [#6279](https://github.com/nodejs/node/pull/6279)
- [[`8ae200c768`](https://github.com/nodejs/node/commit/8ae200c768)] - **deps**: floating fix for npm's test-node script (Kat Marchán) [#6153](https://github.com/nodejs/node/pull/6153)
- [[`e3e544eb96`](https://github.com/nodejs/node/commit/e3e544eb96)] - **deps**: upgrade npm to 3.8.6 (Kat Marchán) [#6153](https://github.com/nodejs/node/pull/6153)
- [[`a7104e4516`](https://github.com/nodejs/node/commit/a7104e4516)] - **deps**: cherry-pick 1383d00 from v8 upstream (Fedor Indutny) [#6179](https://github.com/nodejs/node/pull/6179)
- [[`632e6b9617`](https://github.com/nodejs/node/commit/632e6b9617)] - **deps**: backport 125ac66 from v8 upstream (Myles Borins) [#6086](https://github.com/nodejs/node/pull/6086)
- [[`4b8376986a`](https://github.com/nodejs/node/commit/4b8376986a)] - **doc**: git mv to .md (Robert Jefe Lindstaedt) [#4747](https://github.com/nodejs/node/pull/4747)
- [[`e6f4a175d4`](https://github.com/nodejs/node/commit/e6f4a175d4)] - **doc**: add full example for zlib.flush() (Anna Henningsen) [#6172](https://github.com/nodejs/node/pull/6172)
- [[`50f3f10ce6`](https://github.com/nodejs/node/commit/50f3f10ce6)] - **doc**: note that zlib.flush acts after pending writes (Anna Henningsen) [#6172](https://github.com/nodejs/node/pull/6172)
- [[`985685d170`](https://github.com/nodejs/node/commit/985685d170)] - **doc**: fix broken references (Alexander Gromnitsky) [#6100](https://github.com/nodejs/node/pull/6100)
- [[`d66d883a85`](https://github.com/nodejs/node/commit/d66d883a85)] - **doc**: path.format provide more examples (John Eversole) [#5838](https://github.com/nodejs/node/pull/5838)
- [[`dc1552e321`](https://github.com/nodejs/node/commit/dc1552e321)] - **doc**: replace functions with arrow functions (abouthiroppy) [#6203](https://github.com/nodejs/node/pull/6203)
- [[`fa04dfc307`](https://github.com/nodejs/node/commit/fa04dfc307)] - **doc**: DCO anchor that doesn't change (William Kapke) [#6257](https://github.com/nodejs/node/pull/6257)
- [[`b49a5b33b5`](https://github.com/nodejs/node/commit/b49a5b33b5)] - **doc**: fix http response event, Agent#getName (Matthew Douglass) [#5993](https://github.com/nodejs/node/pull/5993)
- [[`3b00d7a5b1`](https://github.com/nodejs/node/commit/3b00d7a5b1)] - **doc**: document intention and dangers of fs module Buffer API (Nikolai Vavilov) [#6020](https://github.com/nodejs/node/pull/6020)
- [[`3bc31526bb`](https://github.com/nodejs/node/commit/3bc31526bb)] - **doc**: explain differences in console.assert between node and browsers (James M Snell) [#6169](https://github.com/nodejs/node/pull/6169)
- [[`3f73502662`](https://github.com/nodejs/node/commit/3f73502662)] - **doc**: native module reloading is not supported (Bryan English) [#6168](https://github.com/nodejs/node/pull/6168)
- [[`5f9c8297f1`](https://github.com/nodejs/node/commit/5f9c8297f1)] - **doc**: clarify fs.watch() and inodes on linux, os x (Joran Dirk Greef) [#6099](https://github.com/nodejs/node/pull/6099)
- [[`f3c0b78ae4`](https://github.com/nodejs/node/commit/f3c0b78ae4)] - **doc**: add domain postmortem (Trevor Norris) [#6159](https://github.com/nodejs/node/pull/6159)
- [[`a91834e743`](https://github.com/nodejs/node/commit/a91834e743)] - **doc**: add stefanmb to collaborators (Stefan Budeanu) [#6227](https://github.com/nodejs/node/pull/6227)
- [[`117348d082`](https://github.com/nodejs/node/commit/117348d082)] - **doc**: add iWuzHere to collaborators (Imran Iqbal) [#6226](https://github.com/nodejs/node/pull/6226)
- [[`a1c46b63e8`](https://github.com/nodejs/node/commit/a1c46b63e8)] - **doc**: add santigimeno to collaborators (Santiago Gimeno) [#6225](https://github.com/nodejs/node/pull/6225)
- [[`976e4bb3da`](https://github.com/nodejs/node/commit/976e4bb3da)] - **doc**: add addaleax to collaborators (Anna Henningsen) [#6224](https://github.com/nodejs/node/pull/6224)
- [[`4fa949ef75`](https://github.com/nodejs/node/commit/4fa949ef75)] - **doc**: fix incorrect references in buffer docs (Amery) [#6194](https://github.com/nodejs/node/pull/6194)
- [[`b26fea1595`](https://github.com/nodejs/node/commit/b26fea1595)] - **doc**: add copy about how to curl SHA256.txt (Myles Borins) [#6120](https://github.com/nodejs/node/pull/6120)
- [[`daaad47099`](https://github.com/nodejs/node/commit/daaad47099)] - **doc**: clarification for maxBuffer and Unicode output (James M Snell) [#6030](https://github.com/nodejs/node/pull/6030)
- [[`5e6915f374`](https://github.com/nodejs/node/commit/5e6915f374)] - **doc**: describe child.kill() pitfalls on linux (Robert Jefe Lindstaedt) [#2098](https://github.com/nodejs/node/issues/2098)
- [[`a40d0e8f9d`](https://github.com/nodejs/node/commit/a40d0e8f9d)] - **doc**: fix scrolling on iOS devices (Luigi Pinca) [#5878](https://github.com/nodejs/node/pull/5878)
- [[`a81fca4f99`](https://github.com/nodejs/node/commit/a81fca4f99)] - **doc**: add topic - event loop, timers, `nextTick()` (Jeff Harris) [#4936](https://github.com/nodejs/node/pull/4936)
- [[`440d1172fd`](https://github.com/nodejs/node/commit/440d1172fd)] - **doc**: add example using algorithms not directly exposed (Brad Hill) [#6108](https://github.com/nodejs/node/pull/6108)
- [[`96ad5c5303`](https://github.com/nodejs/node/commit/96ad5c5303)] - **doc**: update openssl LICENSE using license-builder.sh (Steven R. Loomis) [#6065](https://github.com/nodejs/node/pull/6065)
- [[`07829b0bc9`](https://github.com/nodejs/node/commit/07829b0bc9)] - **doc**: simple doc typo fix (Brendon Pierson) [#6041](https://github.com/nodejs/node/pull/6041)
- [[`bc0ee06226`](https://github.com/nodejs/node/commit/bc0ee06226)] - **doc**: note about Android support (Rich Trott) [#6040](https://github.com/nodejs/node/pull/6040)
- [[`60a73a2ed2`](https://github.com/nodejs/node/commit/60a73a2ed2)] - **doc**: fix a typo in 5.10.1's changelog (Vladimir Varankin) [#6076](https://github.com/nodejs/node/pull/6076)
- [[`b57be92c1b`](https://github.com/nodejs/node/commit/b57be92c1b)] - **gitignore**: adding .vs/ directory to .gitignore (Mike Kaufman) [#6070](https://github.com/nodejs/node/pull/6070)
- [[`6e891c7ad4`](https://github.com/nodejs/node/commit/6e891c7ad4)] - **gitignore**: ignore VS 2015 \*.VC.opendb files (Mike Kaufman) [#6070](https://github.com/nodejs/node/pull/6070)
- [[`abd101be1a`](https://github.com/nodejs/node/commit/abd101be1a)] - **http**: disallow sending obviously invalid status codes (Brian White) [#6291](https://github.com/nodejs/node/pull/6291)
- [[`16b23b2c28`](https://github.com/nodejs/node/commit/16b23b2c28)] - **http**: skip body and next message of CONNECT res (Fedor Indutny) [#6279](https://github.com/nodejs/node/pull/6279)
- [[`a259ee4018`](https://github.com/nodejs/node/commit/a259ee4018)] - **http**: unref socket timer on parser execute (Fedor Indutny) [#6286](https://github.com/nodejs/node/pull/6286)
- [[`d4abca5b27`](https://github.com/nodejs/node/commit/d4abca5b27)] - **lib**: remove bootstrap global context indirection (Jeremiah Senkpiel) [#5881](https://github.com/nodejs/node/pull/5881)
- [[`c8783aff21`](https://github.com/nodejs/node/commit/c8783aff21)] - **lib,test,tools**: alignment on variable assignments (Rich Trott) [#6242](https://github.com/nodejs/node/pull/6242)
- [[`d5d4f194f1`](https://github.com/nodejs/node/commit/d5d4f194f1)] - **net**: replace \_\_defineGetter\_\_ with defineProperty (Fedor Indutny) [#6284](https://github.com/nodejs/node/pull/6284)
- [[`6d9c0c9aa7`](https://github.com/nodejs/node/commit/6d9c0c9aa7)] - **(SEMVER-MINOR)** **net**: support DNS hints in createConnection() (Colin Ihrig) [#6000](https://github.com/nodejs/node/pull/6000)
- [[`457f24f19c`](https://github.com/nodejs/node/commit/457f24f19c)] - **(SEMVER-MINOR)** **node**: make builtin libs available for `--eval` (Anna Henningsen) [#6207](https://github.com/nodejs/node/pull/6207)
- [[`fc89d17656`](https://github.com/nodejs/node/commit/fc89d17656)] - **path**: fixing a test that breaks on some machines. (Mike Kaufman) [#6067](https://github.com/nodejs/node/pull/6067)
- [[`1d408099b7`](https://github.com/nodejs/node/commit/1d408099b7)] - **process**: fix incorrect usage of assert.fail() (Rich Trott) [#6211](https://github.com/nodejs/node/pull/6211)
- [[`07c9f981d6`](https://github.com/nodejs/node/commit/07c9f981d6)] - **(SEMVER-MINOR)** **repl**: keep the built-in modules non-enumerable (Anna Henningsen) [#6207](https://github.com/nodejs/node/pull/6207)
- [[`5382deaa18`](https://github.com/nodejs/node/commit/5382deaa18)] - **repl**: don’t complete non-simple expressions (Anna Henningsen) [#6192](https://github.com/nodejs/node/pull/6192)
- [[`2254f1a175`](https://github.com/nodejs/node/commit/2254f1a175)] - **repl**: refactor repl.js (Rich Trott) [#6071](https://github.com/nodejs/node/pull/6071)
- [[`7d54d85269`](https://github.com/nodejs/node/commit/7d54d85269)] - **(SEMVER-MINOR)** **src**: add SIGINFO to supported signals (James Reggio) [#6093](https://github.com/nodejs/node/pull/6093)
- [[`fbc99ba4f1`](https://github.com/nodejs/node/commit/fbc99ba4f1)] - **src**: add missing 'inline' keywords (Ben Noordhuis) [#6056](https://github.com/nodejs/node/pull/6056)
- [[`20bb92f5c8`](https://github.com/nodejs/node/commit/20bb92f5c8)] - **src**: use size_t for http parser array size fields (Ben Noordhuis) [#5969](https://github.com/nodejs/node/pull/5969)
- [[`2fd8be2dbe`](https://github.com/nodejs/node/commit/2fd8be2dbe)] - **src**: replace ARRAY_SIZE with typesafe arraysize (Ben Noordhuis) [#5969](https://github.com/nodejs/node/pull/5969)
- [[`4392b4aee0`](https://github.com/nodejs/node/commit/4392b4aee0)] - **stream**: Fix readableState.awaitDrain mechanism (Anna Henningsen) [#6023](https://github.com/nodejs/node/pull/6023)
- [[`20dcdd365b`](https://github.com/nodejs/node/commit/20dcdd365b)] - **stream_base**: expose `bytesRead` getter (Fedor Indutny) [#6284](https://github.com/nodejs/node/pull/6284)
- [[`f69416c06e`](https://github.com/nodejs/node/commit/f69416c06e)] - **streams**: support unlimited synchronous cork/uncork cycles (Matteo Collina) [#6164](https://github.com/nodejs/node/pull/6164)
- [[`4bfed26d1a`](https://github.com/nodejs/node/commit/4bfed26d1a)] - **test**: add zlib close-after-error regression test (Anna Henningsen) [#6270](https://github.com/nodejs/node/pull/6270)
- [[`99d0a61441`](https://github.com/nodejs/node/commit/99d0a61441)] - **test**: move more tests from sequential to parallel (Santiago Gimeno) [#6187](https://github.com/nodejs/node/pull/6187)
- [[`96be986a77`](https://github.com/nodejs/node/commit/96be986a77)] - **test**: assert - fixed error messages to match the tests (surya panikkal) [#6241](https://github.com/nodejs/node/pull/6241)
- [[`4e4efb756e`](https://github.com/nodejs/node/commit/4e4efb756e)] - **test**: add test for responses to HTTP CONNECT req (Josh Leder) [#6279](https://github.com/nodejs/node/pull/6279)
- [[`5b42ef5dfe`](https://github.com/nodejs/node/commit/5b42ef5dfe)] - **test**: move debugger tests to sequential (Rich Trott) [#6205](https://github.com/nodejs/node/pull/6205)
- [[`9856b804e9`](https://github.com/nodejs/node/commit/9856b804e9)] - **test**: move some test from sequential to parallel (Santiago Gimeno) [#6087](https://github.com/nodejs/node/pull/6087)
- [[`1d130d0203`](https://github.com/nodejs/node/commit/1d130d0203)] - **test**: move the debugger tests back to parallel (Santiago Gimeno) [#6246](https://github.com/nodejs/node/pull/6246)
- [[`c0e9c94868`](https://github.com/nodejs/node/commit/c0e9c94868)] - **test**: fix issues for ESLint 2.7.0 (silverwind) [#6132](https://github.com/nodejs/node/pull/6132)
- [[`056a258173`](https://github.com/nodejs/node/commit/056a258173)] - **test**: fix flaky test-http-set-timeout-server (Santiago Gimeno) [#6248](https://github.com/nodejs/node/pull/6248)
- [[`be993fcf6c`](https://github.com/nodejs/node/commit/be993fcf6c)] - **test**: fix test-net-settimeout flakiness (Santiago Gimeno) [#6166](https://github.com/nodejs/node/pull/6166)
- [[`a38b614ae9`](https://github.com/nodejs/node/commit/a38b614ae9)] - **test**: fix flaky test-child-process-fork-net (Rich Trott) [#6138](https://github.com/nodejs/node/pull/6138)
- [[`476535cc0e`](https://github.com/nodejs/node/commit/476535cc0e)] - **test**: fix flaky test-http-client-abort (Rich Trott) [#6124](https://github.com/nodejs/node/pull/6124)
- [[`6bb7999bd6`](https://github.com/nodejs/node/commit/6bb7999bd6)] - **test**: refactor test-file-write-stream3 (Rich Trott) [#6050](https://github.com/nodejs/node/pull/6050)
- [[`a27e95231e`](https://github.com/nodejs/node/commit/a27e95231e)] - **test**: enforce strict mode for test-domain-crypto (Rich Trott) [#6047](https://github.com/nodejs/node/pull/6047)
- [[`8da4bad1c9`](https://github.com/nodejs/node/commit/8da4bad1c9)] - **test**: fix pummel test failures (Rich Trott) [#6012](https://github.com/nodejs/node/pull/6012)
- [[`edd8a15508`](https://github.com/nodejs/node/commit/edd8a15508)] - **test,repl**: use deepStrictEqual for false-y values (Jeremiah Senkpiel) [#6196](https://github.com/nodejs/node/pull/6196)
- [[`48ecc0b6b5`](https://github.com/nodejs/node/commit/48ecc0b6b5)] - **test,tools**: enable linting for undefined vars (Rich Trott) [#6255](https://github.com/nodejs/node/pull/6255)
- [[`d809c84bf8`](https://github.com/nodejs/node/commit/d809c84bf8)] - **test,vm**: enable strict mode for vm tests (Rich Trott) [#6209](https://github.com/nodejs/node/pull/6209)
- [[`4a1dfdcc0f`](https://github.com/nodejs/node/commit/4a1dfdcc0f)] - **tools**: lint rule for assert.fail() (Rich Trott) [#6261](https://github.com/nodejs/node/pull/6261)
- [[`fff6a84da5`](https://github.com/nodejs/node/commit/fff6a84da5)] - **tools**: enable linting for v8_prof_processor.js (Rich Trott) [#6262](https://github.com/nodejs/node/pull/6262)
- [[`a2ca347803`](https://github.com/nodejs/node/commit/a2ca347803)] - **tools**: move message listener to worker objects (Brian White) [#6212](https://github.com/nodejs/node/pull/6212)
- [[`f201b01bf7`](https://github.com/nodejs/node/commit/f201b01bf7)] - **tools**: improve js linter (Brian White) [#5638](https://github.com/nodejs/node/pull/5638)
- [[`be070d775f`](https://github.com/nodejs/node/commit/be070d775f)] - **tools**: lint for alignment of variable assignments (Rich Trott) [#6242](https://github.com/nodejs/node/pull/6242)
- [[`d9b8758f47`](https://github.com/nodejs/node/commit/d9b8758f47)] - **tools**: update ESLint to 2.7.0 (silverwind) [#6132](https://github.com/nodejs/node/pull/6132)
- [[`a6056c453e`](https://github.com/nodejs/node/commit/a6056c453e)] - **tools**: fix license-builder.sh again for ICU (Steven R. Loomis) [#6068](https://github.com/nodejs/node/pull/6068)
- [[`fabc33a075`](https://github.com/nodejs/node/commit/fabc33a075)] - **tools**: remove simplejson dependency (Sakthipriyan Vairamani) [#6101](https://github.com/nodejs/node/pull/6101)
- [[`d516412cd5`](https://github.com/nodejs/node/commit/d516412cd5)] - **tools,doc**: parse types in braces everywhere (Alexander Makarenko) [#5329](https://github.com/nodejs/node/pull/5329)
- [[`69eb4a6834`](https://github.com/nodejs/node/commit/69eb4a6834)] - **tools,doc**: fix json for grouped optional params (firedfox) [#5977](https://github.com/nodejs/node/pull/5977)
- [[`a2dd848764`](https://github.com/nodejs/node/commit/a2dd848764)] - **tools,doc**: fix incomplete json produced by doctool (firedfox) [#5966](https://github.com/nodejs/node/pull/5966)
- [[`bad006f2e1`](https://github.com/nodejs/node/commit/bad006f2e1)] - **zlib**: fix use after null when calling .close (James Lal) [#5982](https://github.com/nodejs/node/pull/5982)
- [[`83bc0a2ac9`](https://github.com/nodejs/node/commit/83bc0a2ac9)] - **(SEMVER-MINOR)** **zlib**: Make the finish flush flag configurable (Anna Henningsen) [#6069](https://github.com/nodejs/node/pull/6069)
- [[`2c23e14d5d`](https://github.com/nodejs/node/commit/2c23e14d5d)] - **(SEMVER-MINOR)** **zlib**: detect gzip files when using unzip\* (Anna Henningsen) [#5884](https://github.com/nodejs/node/pull/5884)
- [[`61167c3e23`](https://github.com/nodejs/node/commit/61167c3e23)] - **zlib**: fix gzip member head/buffer boundary issue (Anna Henningsen) [#5973](https://github.com/nodejs/node/pull/5973)

Windows 32-bit Installer: https://nodejs.org/dist/v5.11.0/node-v5.11.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v5.11.0/node-v5.11.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v5.11.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v5.11.0/win-x64/node.exe \
Mac OS X 64-bit Installer: https://nodejs.org/dist/v5.11.0/node-v5.11.0.pkg \
Mac OS X 64-bit Binary: https://nodejs.org/dist/v5.11.0/node-v5.11.0-darwin-x64.tar.gz \
Linux 32-bit Binary: https://nodejs.org/dist/v5.11.0/node-v5.11.0-linux-x86.tar.xz \
Linux 64-bit Binary: https://nodejs.org/dist/v5.11.0/node-v5.11.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v5.11.0/node-v5.11.0-linux-ppc64le.tar.xz \
SmartOS 32-bit Binary: https://nodejs.org/dist/v5.11.0/node-v5.11.0-sunos-x86.tar.xz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v5.11.0/node-v5.11.0-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v5.11.0/node-v5.11.0-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v5.11.0/node-v5.11.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v5.11.0/node-v5.11.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v5.11.0/node-v5.11.0.tar.gz \
Other release files: https://nodejs.org/dist/v5.11.0/ \
Documentation: https://nodejs.org/docs/v5.11.0/api/

Shasums (GPG signing hash: SHA512, file hash: SHA256):

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA1

4992e1ba18cdac51383a089b1494131dbca465a5328938dbd4835cbf780f7ecc  node-v5.11.0-darwin-x64.tar.gz
b450fc3f380c23f98e800abb4feb79760b54ddbc1e504c23f69ac87fabc942be  node-v5.11.0-darwin-x64.tar.xz
d6850717f78dd94238510ec3cb804181a6bd152397def7de4a90dedf08a0eae1  node-v5.11.0-headers.tar.gz
ab0b262cf38de15aa8930d6f497173062832e09f47c95257e8fa433f31e7ab58  node-v5.11.0-headers.tar.xz
b6cc0dd471f07b607367b76a3f2ec1f11d9bc05f2fccbcda7b85ce76d31a3e2a  node-v5.11.0-linux-arm64.tar.gz
646fc0e65744fa8bb5af552ba4727fb588a327aab905bbae3b510a1498951f3f  node-v5.11.0-linux-arm64.tar.xz
6bf29cbf6d78e95a895bfb77774fde49fc3a565d601320b91b7ed5849f01a08d  node-v5.11.0-linux-armv6l.tar.gz
9873fb842c1e632c89a6aa18a1c2bb97abb6860e93d4128e0098cef2cb619753  node-v5.11.0-linux-armv6l.tar.xz
c07487fab4434f667a4b456fab098deffa76d73853e6636ebe05045e7db875de  node-v5.11.0-linux-armv7l.tar.gz
f5e6be7af2f06d63118d181e5d7000c41599cb1b118c79fc8c38de0a52a9620c  node-v5.11.0-linux-armv7l.tar.xz
6d267c7e1294d15d99747437ee74104b941111e7861382da84466c86d7fca139  node-v5.11.0-linux-ppc64le.tar.gz
c8882683c4f02e6eec1b7442ddc9b60e01bfb964524ec9bc9d24f43c4c8fa624  node-v5.11.0-linux-ppc64le.tar.xz
92602b815ce1c64c63b94d6f72f9b96b427415d023d5f9466c7ffef334bf0386  node-v5.11.0-linux-x64.tar.gz
8fffbfc5152e05434f2ff2a80f2ae9fd08952bebd54f57ca731d8e3ffee855ef  node-v5.11.0-linux-x64.tar.xz
f167a644eb6bf94f9a289d28d8140a6a497b02190571b02c7919458e12431ce0  node-v5.11.0-linux-x86.tar.gz
b462a02a0e80c4eb35736a0c2e15c882c443bcceec51fd937bc979aa86efbf6d  node-v5.11.0-linux-x86.tar.xz
a5637b0eb69df94b74744a60603344e6fbba0de9145b944a136474920b8f3d4d  node-v5.11.0.pkg
386fed5acd84eb0d9efe958c951db2e9af2dd469d82e745179285b52a087b553  node-v5.11.0-sunos-x64.tar.gz
20782223853cdfd0c64c18aa7ad5e2789e0a330fadfc79bb341562d54849ec9d  node-v5.11.0-sunos-x64.tar.xz
44dd2e9bae4513bbf586fde1fea11ea310219feece915c21b3a45d4715e14c5e  node-v5.11.0-sunos-x86.tar.gz
9bfd684df327d32e58bb7802c9eba9799a3e5e111720949b7339d11dabee8a4e  node-v5.11.0-sunos-x86.tar.xz
ef41b01a9e49de5a5d160071166f911d0bc9ff6a0bd0e88ea4863df572d95e91  node-v5.11.0.tar.gz
083766aff72fc5c78f6999dd0d00bc0340a052ab270fd60d6928b719ca2b3fd1  node-v5.11.0.tar.xz
5d4185ca686c32fad6d62fa2fcea13f437a04f1fbec2e4ec19998189941c472b  node-v5.11.0-x64.msi
51d7c57d2246d92fbeb325f3bb8b878452829e36edb57110b67e402913cdc194  node-v5.11.0-x86.msi
47d7c3d295be704c7423cfe556b5da99816404c9bb18d49f30947f28b70d358c  win-x64/node.exe
f09c4aa7056f16f019b55b4e592c87fa72a72a94e680545cfdfdcf7ea2cc2f7b  win-x64/node.lib
7167ebafaf12659f0b8adc1853af1fbffc974ecc6b2d74ca4adfbabf21856dfe  win-x86/node.exe
3bb913a5edcb70bbd73eaff625852ebafa843aca081294981a17408e0d0edd40  win-x86/node.lib
-----BEGIN PGP SIGNATURE-----

iQEcBAEBAgAGBQJXGTmPAAoJEJM7AfQLXKlGl8sH+wWBJ7sh6SaNEuzAPFGJHDg7
DfnT5ZtdOmKhcFU1TNYv1UkoddhOYGuiU6o64+RaoRQVNTbrS0CktmbZNebe+Ar5
SVNf4ZPVYsqf78lzHkxBgBV8ysxXWL0G0GbOYCbc8PxE8oAmJz+TN5gsxSe3rNci
ksRISXLL0Nt91ef/CEjpODo9Lpf8onmnvFemXz7lm5kLBKZBUGVVRA1XAyg/MgEE
IWlpaGVJRCifLooy+nHBe+cXMomNMZUQZDFitgesHmNbMgNk7jlxorCjrYm/R69f
h7Ojc19+84SaALBwFQ/gqTo8MV47pqNodLo4xP5A8SEwGpDzzTNkVeio4Go64hc=
=imFa
-----END PGP SIGNATURE-----

```
