---
date: '2020-03-05T01:40:51.286Z'
category: release
title: Node v13.10.1 (Current)
layout: blog-post
author: <PERSON><PERSON>
---

### Notable Changes

In Node.js 13.9.0 deps/zlib was switched to the chromium maintained implementation. This change
had the unforseen consequence of breaking building from the tarballs we release as we were too
aggressively removing `unneccessary files` from the `deps/zlib` folder. This release includes
a patch that ensures that individuals will once again be able to build Node.js from source.

### Commits

- [[`723aa41d96`](https://github.com/nodejs/node/commit/723aa41d96)] - **build**: fix zlib tarball generation (<PERSON>) [#32094](https://github.com/nodejs/node/pull/32094)
- [[`9c1ac50fc5`](https://github.com/nodejs/node/commit/9c1ac50fc5)] - **build**: fix building with ninja (<PERSON>) [#32071](https://github.com/nodejs/node/pull/32071)
- [[`478450d6b3`](https://github.com/nodejs/node/commit/478450d6b3)] - **build**: add asan check in Github action (gengjiawen) [#31902](https://github.com/nodejs/node/pull/31902)
- [[`0fc45f80b5`](https://github.com/nodejs/node/commit/0fc45f80b5)] - **crypto**: simplify exportKeyingMaterial (Tobias Nießen) [#31922](https://github.com/nodejs/node/pull/31922)
- [[`4dc59b91a7`](https://github.com/nodejs/node/commit/4dc59b91a7)] - **dgram**: make UDPWrap more reusable (Anna Henningsen) [#31871](https://github.com/nodejs/node/pull/31871)
- [[`4ed720e940`](https://github.com/nodejs/node/commit/4ed720e940)] - **doc**: visibility of Worker threads cli options (Harshitha KP) [#31380](https://github.com/nodejs/node/pull/31380)
- [[`2518213a1b`](https://github.com/nodejs/node/commit/2518213a1b)] - **doc**: improve doc/markdown file organization coherence (ConorDavenport) [#31792](https://github.com/nodejs/node/pull/31792)
- [[`ba3f7ff94d`](https://github.com/nodejs/node/commit/ba3f7ff94d)] - **doc**: update stream.pipeline() signature (vsemozhetbyt) [#31789](https://github.com/nodejs/node/pull/31789)
- [[`3c8daa3aa0`](https://github.com/nodejs/node/commit/3c8daa3aa0)] - **events**: convert errorMonitor to a normal property (Gerhard Stoebich) [#31848](https://github.com/nodejs/node/pull/31848)
- [[`6b44df2415`](https://github.com/nodejs/node/commit/6b44df2415)] - **perf,src**: add HistogramBase and internal/histogram.js (James M Snell) [#31988](https://github.com/nodejs/node/pull/31988)
- [[`6a9cea9ed2`](https://github.com/nodejs/node/commit/6a9cea9ed2)] - **src**: pass resource object along with InternalMakeCallback (Anna Henningsen) [#32063](https://github.com/nodejs/node/pull/32063)
- [[`70f046010c`](https://github.com/nodejs/node/commit/70f046010c)] - **src**: start the .text section with an asm symbol (Gabriel Schulhof) [#31981](https://github.com/nodejs/node/pull/31981)
- [[`755da035ce`](https://github.com/nodejs/node/commit/755da035ce)] - **src**: add node_crypto_common and refactor (James M Snell) [#32016](https://github.com/nodejs/node/pull/32016)
- [[`4d5318c164`](https://github.com/nodejs/node/commit/4d5318c164)] - **src**: improve handling of internal field counting (James M Snell) [#31960](https://github.com/nodejs/node/pull/31960)
- [[`1539928ed9`](https://github.com/nodejs/node/commit/1539928ed9)] - **test**: add GC test for disabled AsyncLocalStorage (Andrey Pechkurov) [#31995](https://github.com/nodejs/node/pull/31995)
- [[`be90817558`](https://github.com/nodejs/node/commit/be90817558)] - **test**: remove common.port from test-tls-securepair-client (Rich Trott) [#32024](https://github.com/nodejs/node/pull/32024)

Windows 32-bit Installer: https://nodejs.org/dist/v13.10.1/node-v13.10.1-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v13.10.1/node-v13.10.1-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v13.10.1/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v13.10.1/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v13.10.1/node-v13.10.1.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v13.10.1/node-v13.10.1-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v13.10.1/node-v13.10.1-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v13.10.1/node-v13.10.1-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v13.10.1/node-v13.10.1-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v13.10.1/node-v13.10.1-aix-ppc64.tar.gz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v13.10.1/node-v13.10.1-sunos-x64.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v13.10.1/node-v13.10.1-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v13.10.1/node-v13.10.1-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v13.10.1/node-v13.10.1.tar.gz \
Other release files: https://nodejs.org/dist/v13.10.1/ \
Documentation: https://nodejs.org/docs/v13.10.1/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

6e0a311b0e8ef5fb3ede6745dc19fd2d37b6120b66a0fd3bcb82178361d2bc6f  node-v13.10.1-aix-ppc64.tar.gz
a6a66fdc79e70267fc191f10ee045793240974e1268fdea6c2d28afbc1d635e8  node-v13.10.1-darwin-x64.tar.gz
9b5281ef32d311c570a9fc5f7bcbc187b2693d208e2990b14d8c0f41e3a3a500  node-v13.10.1-darwin-x64.tar.xz
04df27a554b07224199bef88eb8ce8b60c22a25e8d05225fb0963563aac7d95d  node-v13.10.1-headers.tar.gz
ca5e943dca20013f0d938f925ace0d97cdd166b014197d8c4df3507983a29708  node-v13.10.1-headers.tar.xz
2106cf90ddbe47957b7782caed787cf4927656087d28ec7eb11f0d44c49234e9  node-v13.10.1-linux-arm64.tar.gz
f73effcef784251e53b5e3938b8316c36bd49628c3588de7976e8569e560c12c  node-v13.10.1-linux-arm64.tar.xz
cf02c306b2d789969e9cc9bd0990858ad3cfc96049a933b7dd66599ffe23cb8c  node-v13.10.1-linux-armv7l.tar.gz
deb8e21267d5a9d0c1741bb64a1b4e49814aa56e397261a420ed3b9ba9734601  node-v13.10.1-linux-armv7l.tar.xz
a1d061a12dd7ab81e06bcd025ce31f58beef60369960553f351690778e991309  node-v13.10.1-linux-ppc64le.tar.gz
f89024199eeece281e2e30a1fbcf457c9dfbf4eca52a53f2b9c0342ea9ca5fcf  node-v13.10.1-linux-ppc64le.tar.xz
67a5382cdc5c0820859eef3e01f22972fb2b72cebda1d5e908083e84eed4488a  node-v13.10.1-linux-s390x.tar.gz
eb5c2816ec287b7809ba77c372784426f84e30bdd077833bd1dc97a2ae08b424  node-v13.10.1-linux-s390x.tar.xz
985cc834f3d95c0dc99ac6d7fa6ec7fd7aca74ec71ccc706650f59aec37b6384  node-v13.10.1-linux-x64.tar.gz
69d69165282d88f321e751f03ee5d3370db65e5ca4c587af24994b12f31d4827  node-v13.10.1-linux-x64.tar.xz
49a180a6e6420ca6328dc48b982afe6369ef038f7ad75cb36d98bdd948fee268  node-v13.10.1.pkg
d9c71526ae520b7022fde8d5c8030c7c8d664b83f2e9326d001e15c1bb5f4821  node-v13.10.1-sunos-x64.tar.gz
bff50ca4d5b73d888b137c87b35747203a48339689ee126c22c578691bceb463  node-v13.10.1-sunos-x64.tar.xz
d5b829176ae2d712dba07aa53e0a96eed59ce8cb7e98a175e18fe9737c92aee1  node-v13.10.1.tar.gz
83899ee1db6db806338c84c908a21e320ba30880f58381ec0741601a21c7fb92  node-v13.10.1.tar.xz
52d684cf5f0fe4f261a9b536c4d112c7ec2695eb62bb8fb32ec32b11e39496bb  node-v13.10.1-win-x64.7z
f9d0aac273a44dbd52dd8cdb3d6c684b68b860d128af58d77a0c08f39f51f229  node-v13.10.1-win-x64.zip
4886a322e195c6d82c09888326282accab946aa49ce7f5b92d6f95f4624e42cc  node-v13.10.1-win-x86.7z
570c865afdd61ff1c08bae4fccd8a9efb6bee3dac13c9587848a4b5caf006b6c  node-v13.10.1-win-x86.zip
b065bd36375a01098830311658231e5d8a964b36e7e02ccc2e2afe445bbbc970  node-v13.10.1-x64.msi
01d1763873c8292bb106993df7fd0ef04a3c61afaf20ab8336fd9261142f8b92  node-v13.10.1-x86.msi
067d14318fa5bb3fadfd5de3e68e9ff3fc33954fe1bd1e1477149e39a1f76693  win-x64/node.exe
1f21d29b063cd16bb401331f0f62efae7682a70147074ef0f316750241ed5cba  win-x64/node.lib
112c9bff98179f8edc2612b5ee19773add7e73779ae87d62c03c00e17eb6fb71  win-x64/node_pdb.7z
4fe9a08933f23f2363d6d5c575050fca4db7abb87cdec5c72c2f6c28eafdb000  win-x64/node_pdb.zip
a6bf8593e455d8a67ecdedfff5c659d546046220cc54c78930655f31ed69584c  win-x86/node.exe
2d07645620eaa61500d5e06603a5dd0595321950b4c5fd9e8e3fd5bf82891483  win-x86/node.lib
a395d5b9e4f4623a5f74a127a0a02c4490e931c5f4f8344e1538151a80c69033  win-x86/node_pdb.7z
c56c3edd581f9a5891000f35ed2827c1f6817336152fce92e27a301e891d84de  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQEzBAEBCAAdFiEEDv/hvO/ZyE49CYFSkzsB9AtcqUYFAl5gWEIACgkQkzsB9Atc
qUZd5Af/cKpmstI4I00de+qDMtmrCO2o8L8kW7HSMLf8SP8sPrzqBzkb8rXIu5h9
P1cvlSdl/Nc1SO/38VuEEo7S6Rn8jqMWX70uxaC+bToTGdRRCld4oF+x5R6A7gn+
fBFXS7TuLJSvgIHdBfXrwahLFlER6gLcWQqzOuzbCCVaf36+NC8XVtRXei6stuI+
tEUaBw41hrofUvFkOUlM1TB88c4N3noxeR/Ya5Be1y3Q7lcgYq1EqAUE9W41dzVm
QDoNDw98VQUgZAaPCw+GolLpYlRz7Kb3YS4pRqOu0acW9LbUDxsuSU964Ac7DGeS
ltdDzZL4acISxEqwxkIBJnGLVTdX4Q==
=BmIA
-----END PGP SIGNATURE-----

```
