---
date: '2017-02-22T02:11:30.036Z'
category: release
title: Node v6.10.0 (LTS)
layout: blog-post
author: <PERSON><PERSON>
---

### Notable Changes

The SEMVER-MINOR changes include:

- **crypto**: allow adding extra certs to well-known CAs (<PERSON>) [#9139](https://github.com/nodejs/node/pull/9139)
- **deps**: Upgrade INTL ICU to version 58 (<PERSON>) [#9234](https://github.com/nodejs/node/pull/9234)
- **process**: add `process.memoryUsage.external` (Fedor Indutny) [#9587](https://github.com/nodejs/node/pull/9587)
- **src**: add wrapper for process.emitWarning() (<PERSON>) [#9139](https://github.com/nodejs/node/pull/9139)

Notable SEMVER-PATCH changes include:

- **fs**: cache non-symlinks in realpathSync. (<PERSON>) [#10253](https://github.com/nodejs/node/pull/10253)
- **repl**: allow autocompletion for scoped packages (<PERSON>) [#10296](https://github.com/nodejs/node/pull/10296)

### Commits

- [[`d532d7497a`](https://github.com/nodejs/node/commit/d532d7497a)] - **async_wrap**: clear destroy_ids vector (<PERSON>) [#10400](https://github.com/nodejs/node/pull/10400)
- [[`75d6f111aa`](https://github.com/nodejs/node/commit/75d6f111aa)] - **benchmark**: refactor buffer benchmarks (Troy Connor) [#10175](https://github.com/nodejs/node/pull/10175)
- [[`40c7ec62e0`](https://github.com/nodejs/node/commit/40c7ec62e0)] - **buffer**: fix single-character string filling (Anna Henningsen) [#9837](https://github.com/nodejs/node/pull/9837)
- [[`03f0d2ac21`](https://github.com/nodejs/node/commit/03f0d2ac21)] - **buffer**: handle UCS2 `.fill()` properly on BE (Anna Henningsen) [#9837](https://github.com/nodejs/node/pull/9837)
- [[`9e76350372`](https://github.com/nodejs/node/commit/9e76350372)] - **build**: add /opt/freeware/... to AIX library path (Stewart X Addison) [#10128](https://github.com/nodejs/node/pull/10128)
- [[`7d519fa87c`](https://github.com/nodejs/node/commit/7d519fa87c)] - **build**: add (not) cross-compiled configure flags (Jesús Leganés-Combarro 'piranna) [#10287](https://github.com/nodejs/node/pull/10287)
- [[`a2f02859b0`](https://github.com/nodejs/node/commit/a2f02859b0)] - **(SEMVER-MINOR)** **crypto**: allow adding extra certs to well-known CAs (Sam Roberts) [#9139](https://github.com/nodejs/node/pull/9139)
- [[`4e1a5a71c1`](https://github.com/nodejs/node/commit/4e1a5a71c1)] - **crypto**: fix handling of root_cert_store. (Adam Langley) [#9409](https://github.com/nodejs/node/pull/9409)
- [[`8c6ecce743`](https://github.com/nodejs/node/commit/8c6ecce743)] - **crypto**: Use reference count to manage cert_store (Adam Majer) [#9409](https://github.com/nodejs/node/pull/9409)
- [[`8bccd9ed67`](https://github.com/nodejs/node/commit/8bccd9ed67)] - **debugger**: call `this.resume()` after `this.run()` (Lance Ball) [#10099](https://github.com/nodejs/node/pull/10099)
- [[`2a39d1c7a4`](https://github.com/nodejs/node/commit/2a39d1c7a4)] - **deps**: backport 7c3748a from upstream V8 (Cristian Cavalli) [#10881](https://github.com/nodejs/node/pull/10881)
- [[`5c5f5fb415`](https://github.com/nodejs/node/commit/5c5f5fb415)] - **deps**: backport 224d376 from V8 upstream (jBarz) [#10546](https://github.com/nodejs/node/pull/10546)
- [[`687137eced`](https://github.com/nodejs/node/commit/687137eced)] - **deps**: ICU 58.2 bump download URL (Steven R. Loomis) [#10206](https://github.com/nodejs/node/pull/10206)
- [[`ae477b7b62`](https://github.com/nodejs/node/commit/ae477b7b62)] - **deps**: ICU 58.2 bump (Steven R. Loomis) [#10206](https://github.com/nodejs/node/pull/10206)
- [[`ad807ad29b`](https://github.com/nodejs/node/commit/ad807ad29b)] - **(SEMVER-MINOR)** **deps**: Intl: ICU 58 bump - small icu (BIG COMMIT) (Steven R. Loomis) [#9234](https://github.com/nodejs/node/pull/9234)
- [[`0ee665c4ed`](https://github.com/nodejs/node/commit/0ee665c4ed)] - **(SEMVER-MINOR)** **deps**: Intl: ICU 58 bump: configure/LICENSE/docs (Steven R. Loomis) [#9234](https://github.com/nodejs/node/pull/9234)
- [[`4197b9b041`](https://github.com/nodejs/node/commit/4197b9b041)] - **deps**: update patch level in V8 (Myles Borins) [#10666](https://github.com/nodejs/node/pull/10666)
- [[`e71129ebbc`](https://github.com/nodejs/node/commit/e71129ebbc)] - **deps**: cherry-pick a715957 from V8 upstream (Myles Borins) [#10666](https://github.com/nodejs/node/pull/10666)
- [[`87839ca036`](https://github.com/nodejs/node/commit/87839ca036)] - **deps**: cherry-pick 7a88ff3 from V8 upstream (Myles Borins) [#10666](https://github.com/nodejs/node/pull/10666)
- [[`13983d474a`](https://github.com/nodejs/node/commit/13983d474a)] - **deps**: cherry-pick d800a65 from V8 upstream (Myles Borins) [#10666](https://github.com/nodejs/node/pull/10666)
- [[`f77fcf893f`](https://github.com/nodejs/node/commit/f77fcf893f)] - **deps**: cherry-pick baba152 from V8 upstream (Michaël Zasso) [#10689](https://github.com/nodejs/node/pull/10689)
- [[`fdc373d639`](https://github.com/nodejs/node/commit/fdc373d639)] - **deps**: fix compile bug in v8/lookup.h (Matthew Avery) [#10525](https://github.com/nodejs/node/pull/10525)
- [[`055f666065`](https://github.com/nodejs/node/commit/055f666065)] - **doc**: change logical to bitwise OR in dns lookup (Sakthipriyan Vairamani (thefourtheye)) [#11037](https://github.com/nodejs/node/pull/11037)
- [[`78b83e7249`](https://github.com/nodejs/node/commit/78b83e7249)] - **doc**: killSignal option accepts integer values (Sakthipriyan Vairamani (thefourtheye)) [#10424](https://github.com/nodejs/node/pull/10424)
- [[`76e6e7ef55`](https://github.com/nodejs/node/commit/76e6e7ef55)] - **doc**: correct vcbuild options for windows testing (Jonathan Boarman) [#10686](https://github.com/nodejs/node/pull/10686)
- [[`50c2ecdf0e`](https://github.com/nodejs/node/commit/50c2ecdf0e)] - **doc**: replace newlines in deprecation with space (Sakthipriyan Vairamani (thefourtheye)) [#11074](https://github.com/nodejs/node/pull/11074)
- [[`15df5c08ea`](https://github.com/nodejs/node/commit/15df5c08ea)] - **doc**: fix changelog for v6 (Myles Borins) [#11090](https://github.com/nodejs/node/pull/11090)
- [[`03302d6133`](https://github.com/nodejs/node/commit/03302d6133)] - **doc**: add joyeecheung to collaborators (Joyee Cheung) [#10603](https://github.com/nodejs/node/pull/10603)
- [[`447287c432`](https://github.com/nodejs/node/commit/447287c432)] - **doc**: unify dirname and filename description (Sam Roberts) [#10527](https://github.com/nodejs/node/pull/10527)
- [[`c3882f4d8b`](https://github.com/nodejs/node/commit/c3882f4d8b)] - **doc**: warn about unvalidated input in child_process (Matthew Garrett) [#10466](https://github.com/nodejs/node/pull/10466)
- [[`11d8f2439b`](https://github.com/nodejs/node/commit/11d8f2439b)] - **doc**: require two-factor authentication (Rich Trott) [#10529](https://github.com/nodejs/node/pull/10529)
- [[`017764018c`](https://github.com/nodejs/node/commit/017764018c)] - **doc**: use "Node.js" in V8 guide (Rich Trott) [#10438](https://github.com/nodejs/node/pull/10438)
- [[`636335a1c3`](https://github.com/nodejs/node/commit/636335a1c3)] - **doc**: require() tries first core not native modules (Vicente Jimenez Aguilar) [#10324](https://github.com/nodejs/node/pull/10324)
- [[`f7c0eb8ba6`](https://github.com/nodejs/node/commit/f7c0eb8ba6)] - **doc**: clarify the review and landing process (Joyee Cheung) [#10202](https://github.com/nodejs/node/pull/10202)
- [[`b814b4cec7`](https://github.com/nodejs/node/commit/b814b4cec7)] - **doc**: update writable.write return value (Tanuja-Sawant) [#9468](https://github.com/nodejs/node/pull/9468)
- [[`3079ba6e78`](https://github.com/nodejs/node/commit/3079ba6e78)] - **doc**: redirect 'Start a Working Group' to TSC repo (William Kapke) [#9655](https://github.com/nodejs/node/pull/9655)
- [[`8dbba48f70`](https://github.com/nodejs/node/commit/8dbba48f70)] - **doc**: add Working Group dissolution text (William Kapke) [#9656](https://github.com/nodejs/node/pull/9656)
- [[`1dc7b8918d`](https://github.com/nodejs/node/commit/1dc7b8918d)] - **doc**: fixup errors in stream.md (Fumiya KARASAWA) [#10411](https://github.com/nodejs/node/pull/10411)
- [[`c2156fcba1`](https://github.com/nodejs/node/commit/c2156fcba1)] - **doc**: more efficient example in the console.md (Vse Mozhet Byt) [#10451](https://github.com/nodejs/node/pull/10451)
- [[`809ae9da29`](https://github.com/nodejs/node/commit/809ae9da29)] - **doc**: var -> const / let in the console.md (Vse Mozhet Byt) [#10451](https://github.com/nodejs/node/pull/10451)
- [[`3f289a3efe`](https://github.com/nodejs/node/commit/3f289a3efe)] - **doc**: improve common.mustCall() explanation (Rich Trott) [#10390](https://github.com/nodejs/node/pull/10390)
- [[`59aa4e9e29`](https://github.com/nodejs/node/commit/59aa4e9e29)] - **doc**: consistent 'Returns:' part two (Myles Borins) [#10391](https://github.com/nodejs/node/pull/10391)
- [[`54dec23aba`](https://github.com/nodejs/node/commit/54dec23aba)] - **doc**: clarify macosx-firewall suggestion BUILDING (Chase Starr) [#10311](https://github.com/nodejs/node/pull/10311)
- [[`c9c9b5c47e`](https://github.com/nodejs/node/commit/c9c9b5c47e)] - **doc**: modernize code examples in the cluster.md (Vse Mozhet Byt) [#10270](https://github.com/nodejs/node/pull/10270)
- [[`540ff7c123`](https://github.com/nodejs/node/commit/540ff7c123)] - **doc**: add Michaël Zasso to the CTC (Michaël Zasso)
- [[`c95adab452`](https://github.com/nodejs/node/commit/c95adab452)] - **doc**: fix broken link in COLLABORATOR_GUIDE.md (Emanuel Buholzer) [#10337](https://github.com/nodejs/node/pull/10337)
- [[`24bf75309a`](https://github.com/nodejs/node/commit/24bf75309a)] - **doc**: fix typo in ecdhCurve, a tls property name (Sam Roberts) [#10345](https://github.com/nodejs/node/pull/10345)
- [[`2eccea06b5`](https://github.com/nodejs/node/commit/2eccea06b5)] - **doc**: expand common module material in test guide (Rich Trott) [#10251](https://github.com/nodejs/node/pull/10251)
- [[`843d4557e2`](https://github.com/nodejs/node/commit/843d4557e2)] - **doc**: fix broken link in COLLABORATOR_GUIDE.md (Michael Dawson) [#10267](https://github.com/nodejs/node/pull/10267)
- [[`b662de6301`](https://github.com/nodejs/node/commit/b662de6301)] - **doc**: rework tls for accuracy and clarity (Sam Roberts) [#9800](https://github.com/nodejs/node/pull/9800)
- [[`e53262cda9`](https://github.com/nodejs/node/commit/e53262cda9)] - **doc**: modernize child_process example code (Vse Mozhet Byt) [#10102](https://github.com/nodejs/node/pull/10102)
- [[`9988f02025`](https://github.com/nodejs/node/commit/9988f02025)] - **doc**: fix typo in code example of 'path' module (pallxk) [#10136](https://github.com/nodejs/node/pull/10136)
- [[`718b5902bc`](https://github.com/nodejs/node/commit/718b5902bc)] - **doc**: standardizing on make -j4 (Jonathan Darling) [#9961](https://github.com/nodejs/node/pull/9961)
- [[`5b6317b10f`](https://github.com/nodejs/node/commit/5b6317b10f)] - **doc**: add note to parallelize make (Jonathan Darling) [#9961](https://github.com/nodejs/node/pull/9961)
- [[`7815efa5c1`](https://github.com/nodejs/node/commit/7815efa5c1)] - **doc**: add some info on `tty#setRawMode()` (Jeremiah Senkpiel) [#10147](https://github.com/nodejs/node/pull/10147)
- [[`639ef411b4`](https://github.com/nodejs/node/commit/639ef411b4)] - **doc**: update `path.format` description and examples (anoff) [#10046](https://github.com/nodejs/node/pull/10046)
- [[`e6c74b37b3`](https://github.com/nodejs/node/commit/e6c74b37b3)] - **fs**: remove needless assignment of null (Francis Gulotta) [#10260](https://github.com/nodejs/node/pull/10260)
- [[`709b9b4660`](https://github.com/nodejs/node/commit/709b9b4660)] - **fs**: cache non-symlinks in realpathSync. (Jeremy Yallop) [#10253](https://github.com/nodejs/node/pull/10253)
- [[`b5f747187d`](https://github.com/nodejs/node/commit/b5f747187d)] - **http**: remove stale timeout listeners (Karl Böhlmark) [#9440](https://github.com/nodejs/node/pull/9440)
- [[`90bd36bd15`](https://github.com/nodejs/node/commit/90bd36bd15)] - **inspector**: check if connected before waiting (Eugene Ostroukhov) [#10094](https://github.com/nodejs/node/pull/10094)
- [[`5ddd508304`](https://github.com/nodejs/node/commit/5ddd508304)] - **lib,test**: use consistent operator linebreak style (Michaël Zasso) [#10178](https://github.com/nodejs/node/pull/10178)
- [[`3eb9373095`](https://github.com/nodejs/node/commit/3eb9373095)] - **os**: fix os.release() for aix and add test (jBarz) [#10245](https://github.com/nodejs/node/pull/10245)
- [[`8ea4487ca7`](https://github.com/nodejs/node/commit/8ea4487ca7)] - **(SEMVER-MINOR)** **process**: add `process.memoryUsage.external` (Fedor Indutny) [#9587](https://github.com/nodejs/node/pull/9587)
- [[`6f8b32e754`](https://github.com/nodejs/node/commit/6f8b32e754)] - **promise**: better stack traces for --trace-warnings (Anna Henningsen) [#9525](https://github.com/nodejs/node/pull/9525)
- [[`1d400ea484`](https://github.com/nodejs/node/commit/1d400ea484)] - **_Revert_** "**repl**: disable Ctrl+C support on win32 for now" (Anna Henningsen) [#8645](https://github.com/nodejs/node/pull/8645)
- [[`57c4c6f5ae`](https://github.com/nodejs/node/commit/57c4c6f5ae)] - **repl**: allow autocompletion for scoped packages (Evan Lucas) [#10296](https://github.com/nodejs/node/pull/10296)
- [[`5e07bce166`](https://github.com/nodejs/node/commit/5e07bce166)] - **(SEMVER-MINOR)** **src**: add wrapper for process.emitWarning() (Sam Roberts) [#9139](https://github.com/nodejs/node/pull/9139)
- [[`7da06088eb`](https://github.com/nodejs/node/commit/7da06088eb)] - **src**: describe what NODE_MODULE_VERSION is for (Sam Roberts) [#10414](https://github.com/nodejs/node/pull/10414)
- [[`7897e7685f`](https://github.com/nodejs/node/commit/7897e7685f)] - **src**: fix string format mistake for 32 bit node (Alex Newman) [#10082](https://github.com/nodejs/node/pull/10082)
- [[`cfa1b5a9e7`](https://github.com/nodejs/node/commit/cfa1b5a9e7)] - **src**: fix memory leak introduced in 34febfbf4 (Ben Noordhuis) [#9604](https://github.com/nodejs/node/pull/9604)
- [[`cc0c736bcc`](https://github.com/nodejs/node/commit/cc0c736bcc)] - **src,tools**: speed up startup by 2.5% (Ben Noordhuis) [#5458](https://github.com/nodejs/node/pull/5458)
- [[`9a8416258d`](https://github.com/nodejs/node/commit/9a8416258d)] - **stream, test**: test \_readableState.emittedReadable (Joyee Cheung) [#10249](https://github.com/nodejs/node/pull/10249)
- [[`f9227fe944`](https://github.com/nodejs/node/commit/f9227fe944)] - **stream_base**: homogenize req_wrap_obj use (Fedor Indutny) [#10184](https://github.com/nodejs/node/pull/10184)
- [[`8f00f70d19`](https://github.com/nodejs/node/commit/8f00f70d19)] - **test**: fix test.py command line options processing (Julien Gilli) [#11153](https://github.com/nodejs/node/pull/11153)
- [[`fce1d10153`](https://github.com/nodejs/node/commit/fce1d10153)] - **test**: add --abort-on-timeout option to test.py (Julien Gilli) [#11086](https://github.com/nodejs/node/pull/11086)
- [[`1c6e171de9`](https://github.com/nodejs/node/commit/1c6e171de9)] - **test**: add tests for clearBuffer state machine (Safia Abdalla) [#9922](https://github.com/nodejs/node/pull/9922)
- [[`8ede25964b`](https://github.com/nodejs/node/commit/8ede25964b)] - **test**: update test-cluster-shared-handle-bind-error (cjihrig) [#10547](https://github.com/nodejs/node/pull/10547)
- [[`e34af8d647`](https://github.com/nodejs/node/commit/e34af8d647)] - **test**: avoid assigning this to variables (cjihrig) [#10548](https://github.com/nodejs/node/pull/10548)
- [[`c07cfc83e4`](https://github.com/nodejs/node/commit/c07cfc83e4)] - **test**: improve test-http-allow-req-after-204-res (Adrian Estrada) [#10503](https://github.com/nodejs/node/pull/10503)
- [[`e067c48889`](https://github.com/nodejs/node/commit/e067c48889)] - **test**: improve test-fs-empty-readStream.js (Adrian Estrada) [#10479](https://github.com/nodejs/node/pull/10479)
- [[`aca927e928`](https://github.com/nodejs/node/commit/aca927e928)] - **test**: refactor test-stream-pipe-after-end (Rich Trott) [#10483](https://github.com/nodejs/node/pull/10483)
- [[`82f4a33359`](https://github.com/nodejs/node/commit/82f4a33359)] - **test**: use strictEqual in test-http-server (Fabrice Tatieze) [#10478](https://github.com/nodejs/node/pull/10478)
- [[`683b060050`](https://github.com/nodejs/node/commit/683b060050)] - **test**: refactor test-stream2-unpipe-drain (Chris Story) [#10033](https://github.com/nodejs/node/pull/10033)
- [[`f1dea3fa41`](https://github.com/nodejs/node/commit/f1dea3fa41)] - **test**: add test for SIGWINCH handling by stdio.js (Sarah Meyer) [#10063](https://github.com/nodejs/node/pull/10063)
- [[`c5ccffd387`](https://github.com/nodejs/node/commit/c5ccffd387)] - **test**: improve code in test-vm-preserves-property (Adrian Estrada) [#10428](https://github.com/nodejs/node/pull/10428)
- [[`c9ca82e58e`](https://github.com/nodejs/node/commit/c9ca82e58e)] - **test**: basic functionality of readUIntLE() (larissayvette) [#10359](https://github.com/nodejs/node/pull/10359)
- [[`b1f2aeb801`](https://github.com/nodejs/node/commit/b1f2aeb801)] - **test**: fix flaky test-https-timeout (Rich Trott) [#10404](https://github.com/nodejs/node/pull/10404)
- [[`9546ad7d4d`](https://github.com/nodejs/node/commit/9546ad7d4d)] - **test**: basic functionality of readUIntBE() (larissayvette) [#10417](https://github.com/nodejs/node/pull/10417)
- [[`b0adda0335`](https://github.com/nodejs/node/commit/b0adda0335)] - **test**: improve test-cluster-worker-constructor.js (Adrian Estrada) [#10396](https://github.com/nodejs/node/pull/10396)
- [[`d37443ca8d`](https://github.com/nodejs/node/commit/d37443ca8d)] - **test**: add known_issues test for #5350 (AnnaMag) [#10319](https://github.com/nodejs/node/pull/10319)
- [[`959860f55c`](https://github.com/nodejs/node/commit/959860f55c)] - **test**: stream readable resumeScheduled state (Italo A. Casas) [#10299](https://github.com/nodejs/node/pull/10299)
- [[`c604016cb4`](https://github.com/nodejs/node/commit/c604016cb4)] - **test**: add known_issues test for #6287 (AnnaMag) [#10272](https://github.com/nodejs/node/pull/10272)
- [[`a24a35f668`](https://github.com/nodejs/node/commit/a24a35f668)] - **test**: stream readable needReadable state (Joyee Cheung) [#10241](https://github.com/nodejs/node/pull/10241)
- [[`8d2f722541`](https://github.com/nodejs/node/commit/8d2f722541)] - **test**: clean up domain-no-error-handler test (weyj4) [#10291](https://github.com/nodejs/node/pull/10291)
- [[`c5ef631fdc`](https://github.com/nodejs/node/commit/c5ef631fdc)] - **test**: update test-domain-uncaught-exception.js (Andy Chen) [#10193](https://github.com/nodejs/node/pull/10193)
- [[`4b5587c5db`](https://github.com/nodejs/node/commit/4b5587c5db)] - **test**: refactor test-domain.js (Siddhartha Sahai) [#10207](https://github.com/nodejs/node/pull/10207)
- [[`99ba710bca`](https://github.com/nodejs/node/commit/99ba710bca)] - **test**: fail for missing output files (Anna Henningsen) [#10150](https://github.com/nodejs/node/pull/10150)
- [[`25d6eed654`](https://github.com/nodejs/node/commit/25d6eed654)] - **test**: stream readableState readingMore state (Gregory) [#9868](https://github.com/nodejs/node/pull/9868)
- [[`f3d1b7209d`](https://github.com/nodejs/node/commit/f3d1b7209d)] - **test**: s/ASSERT/assert/ (cjihrig) [#10544](https://github.com/nodejs/node/pull/10544)
- [[`9cee6786f7`](https://github.com/nodejs/node/commit/9cee6786f7)] - **test**: refactor test-stream-unshift-read-race (Rich Trott) [#10532](https://github.com/nodejs/node/pull/10532)
- [[`19b3015201`](https://github.com/nodejs/node/commit/19b3015201)] - **test**: refactor test-stream-pipe-error-handling (Rich Trott) [#10530](https://github.com/nodejs/node/pull/10530)
- [[`33c47a6415`](https://github.com/nodejs/node/commit/33c47a6415)] - **test**: refactor test-tls-alert-handling (Rich Trott) [#10482](https://github.com/nodejs/node/pull/10482)
- [[`1a7ca46544`](https://github.com/nodejs/node/commit/1a7ca46544)] - **test**: fix flaky test-http-client-timeout-with-data (Rich Trott) [#10431](https://github.com/nodejs/node/pull/10431)
- [[`328c14512f`](https://github.com/nodejs/node/commit/328c14512f)] - **test**: refactor the code in test-http-connect (Adrian Estrada) [#10397](https://github.com/nodejs/node/pull/10397)
- [[`99c9cda6d1`](https://github.com/nodejs/node/commit/99c9cda6d1)] - **test**: refactor test-stdin-from-file (Rob Adelmann) [#10331](https://github.com/nodejs/node/pull/10331)
- [[`38d9c15edd`](https://github.com/nodejs/node/commit/38d9c15edd)] - **test**: refactor the code in test-dns-ipv4 (Adrian Estrada) [#10200](https://github.com/nodejs/node/pull/10200)
- [[`4f18943810`](https://github.com/nodejs/node/commit/4f18943810)] - **test**: refactor the code in test-fs-chmod (Adrian Estrada) [#10440](https://github.com/nodejs/node/pull/10440)
- [[`d89587c1bf`](https://github.com/nodejs/node/commit/d89587c1bf)] - **test**: swap var for let/const throughout (Paul Graham) [#10177](https://github.com/nodejs/node/pull/10177)
- [[`d2ce3909b1`](https://github.com/nodejs/node/commit/d2ce3909b1)] - **test**: improve the code in test-pipe.js (Adrian Estrada) [#10452](https://github.com/nodejs/node/pull/10452)
- [[`3c642ee2ce`](https://github.com/nodejs/node/commit/3c642ee2ce)] - **test**: improve code in test-fs-readfile-error (Adrian Estrada) [#10367](https://github.com/nodejs/node/pull/10367)
- [[`f1075a1726`](https://github.com/nodejs/node/commit/f1075a1726)] - **test**: improve code in test-vm-symbols (Adrian Estrada) [#10429](https://github.com/nodejs/node/pull/10429)
- [[`5f18f0c448`](https://github.com/nodejs/node/commit/5f18f0c448)] - **test**: fix and improve debug-break-on-uncaught (Sakthipriyan Vairamani (thefourtheye)) [#10370](https://github.com/nodejs/node/pull/10370)
- [[`12d86aba49`](https://github.com/nodejs/node/commit/12d86aba49)] - **test**: refactor test-init.js (Sakthipriyan Vairamani (thefourtheye)) [#10384](https://github.com/nodejs/node/pull/10384)
- [[`6370cbe9dc`](https://github.com/nodejs/node/commit/6370cbe9dc)] - **test**: refactor code in test-cluster-http-pipe (Adrian Estrada) [#10297](https://github.com/nodejs/node/pull/10297)
- [[`781d04a1b3`](https://github.com/nodejs/node/commit/781d04a1b3)] - **test**: improve code in test-http-bind-twice.js (Adrian Estrada) [#10318](https://github.com/nodejs/node/pull/10318)
- [[`390cab8d1a`](https://github.com/nodejs/node/commit/390cab8d1a)] - **test**: change var declarations, add mustCall check (Daniel Sims) [#9962](https://github.com/nodejs/node/pull/9962)
- [[`e60be9ccc3`](https://github.com/nodejs/node/commit/e60be9ccc3)] - **test**: refactor test-stdin-script-child (Emanuel Buholzer) [#10321](https://github.com/nodejs/node/pull/10321)
- [[`8b44fb30a1`](https://github.com/nodejs/node/commit/8b44fb30a1)] - **test**: fix timers-same-timeout-wrong-list-deleted (Rich Trott) [#10362](https://github.com/nodejs/node/pull/10362)
- [[`1b9c125325`](https://github.com/nodejs/node/commit/1b9c125325)] - **test**: refactor test-stream2-writable (Rich Trott) [#10353](https://github.com/nodejs/node/pull/10353)
- [[`4cf11d9f4a`](https://github.com/nodejs/node/commit/4cf11d9f4a)] - **test**: refactor test-cluster-net-listen (Segu Riluvan) [#10047](https://github.com/nodejs/node/pull/10047)
- [[`0e5ef4164d`](https://github.com/nodejs/node/commit/0e5ef4164d)] - **test**: change assert.strict to assert.strictEqual() (Ashita Nagesh) [#9988](https://github.com/nodejs/node/pull/9988)
- [[`37ced4d324`](https://github.com/nodejs/node/commit/37ced4d324)] - **test**: refactor the code in test-http-keep-alive (Adrian Estrada) [#10350](https://github.com/nodejs/node/pull/10350)
- [[`61105d75fb`](https://github.com/nodejs/node/commit/61105d75fb)] - **test**: use strictEqual in test-cwd-enoent-repl.js (Neeraj Sharma) [#9952](https://github.com/nodejs/node/pull/9952)
- [[`40c55e73be`](https://github.com/nodejs/node/commit/40c55e73be)] - **test**: refactor test-net-reconnect-error (Duy Le) [#9903](https://github.com/nodejs/node/pull/9903)
- [[`0c31802ea9`](https://github.com/nodejs/node/commit/0c31802ea9)] - **test**: add test-require-invalid-package (Duy Le) [#9903](https://github.com/nodejs/node/pull/9903)
- [[`c706a92373`](https://github.com/nodejs/node/commit/c706a92373)] - **test**: refactor test-child-process-kill (Duy Le) [#9903](https://github.com/nodejs/node/pull/9903)
- [[`d7a36fc2da`](https://github.com/nodejs/node/commit/d7a36fc2da)] - **test**: use consistent block spacing (Rich Trott) [#10377](https://github.com/nodejs/node/pull/10377)
- [[`03bf87c703`](https://github.com/nodejs/node/commit/03bf87c703)] - **test**: refactor test-timers-this (Rich Trott) [#10315](https://github.com/nodejs/node/pull/10315)
- [[`8792fb1788`](https://github.com/nodejs/node/commit/8792fb1788)] - **test**: improve code in test-fs-open.js (Adrian Estrada) [#10312](https://github.com/nodejs/node/pull/10312)
- [[`d8405da44c`](https://github.com/nodejs/node/commit/d8405da44c)] - **test**: refactor the code in test-dns-ipv6 (Adrian Estrada) [#10219](https://github.com/nodejs/node/pull/10219)
- [[`a18f72d8d2`](https://github.com/nodejs/node/commit/a18f72d8d2)] - **test**: improve test-child-process-fork-and-spawn (Adrian Estrada) [#10273](https://github.com/nodejs/node/pull/10273)
- [[`4cba20c1c8`](https://github.com/nodejs/node/commit/4cba20c1c8)] - **test**: fix flaky test-http-client-timeout-event (Rich Trott) [#10293](https://github.com/nodejs/node/pull/10293)
- [[`70f70478de`](https://github.com/nodejs/node/commit/70f70478de)] - **test**: improve test-child-process-exec-buffer (Adrian Estrada) [#10275](https://github.com/nodejs/node/pull/10275)
- [[`3a6fdd805d`](https://github.com/nodejs/node/commit/3a6fdd805d)] - **test**: refactor test-fs-read-stream-inherit (Rich Trott) [#10246](https://github.com/nodejs/node/pull/10246)
- [[`92e3f8f26e`](https://github.com/nodejs/node/commit/92e3f8f26e)] - **test**: refactor test-dgram-send-callback-multi-buffer (mfrance) [#9999](https://github.com/nodejs/node/pull/9999)
- [[`5ff6011cec`](https://github.com/nodejs/node/commit/5ff6011cec)] - **test**: refactor test-tls-ecdh-disable (Aaron Williams) [#9989](https://github.com/nodejs/node/pull/9989)
- [[`be3334709d`](https://github.com/nodejs/node/commit/be3334709d)] - **test**: fix http-client-timeout-option-listeners (Rich Trott) [#10224](https://github.com/nodejs/node/pull/10224)
- [[`713f04ce1d`](https://github.com/nodejs/node/commit/713f04ce1d)] - **test**: refactor test-stream-big-push (Rich Trott) [#10226](https://github.com/nodejs/node/pull/10226)
- [[`373755cad0`](https://github.com/nodejs/node/commit/373755cad0)] - **test**: refactor test-http-dns-fail (Adrian Estrada) [#10243](https://github.com/nodejs/node/pull/10243)
- [[`2f64d5a294`](https://github.com/nodejs/node/commit/2f64d5a294)] - **test**: refactor test-crypto-random (Rich Trott) [#10232](https://github.com/nodejs/node/pull/10232)
- [[`70e4fb8ca1`](https://github.com/nodejs/node/commit/70e4fb8ca1)] - **test**: refactor test-http-pause-resume-one-end (Rich Trott) [#10210](https://github.com/nodejs/node/pull/10210)
- [[`b0a5a3bb70`](https://github.com/nodejs/node/commit/b0a5a3bb70)] - **test**: fix flaky test-dgram-exclusive-implicit-bind (Rich Trott) [#10212](https://github.com/nodejs/node/pull/10212)
- [[`e3f926594e`](https://github.com/nodejs/node/commit/e3f926594e)] - **test**: improvements in test fixtures symlinked (Adrian Estrada) [#10182](https://github.com/nodejs/node/pull/10182)
- [[`217e2c6fcf`](https://github.com/nodejs/node/commit/217e2c6fcf)] - **test**: refactor test-fs-fsync (Rob Adelmann) [#10176](https://github.com/nodejs/node/pull/10176)
- [[`10747f4102`](https://github.com/nodejs/node/commit/10747f4102)] - **test**: refactor test-http-after-connect.js (larissayvette) [#10229](https://github.com/nodejs/node/pull/10229)
- [[`0b243ca178`](https://github.com/nodejs/node/commit/0b243ca178)] - **test**: refactor assert.equal, update syntax to ES6 (Prieto, Marcos)
- [[`c57d72089f`](https://github.com/nodejs/node/commit/c57d72089f)] - **test**: refactor http pipelined socket test (Rich Trott) [#10189](https://github.com/nodejs/node/pull/10189)
- [[`3f17c180be`](https://github.com/nodejs/node/commit/3f17c180be)] - **test**: var to const in tls-no-cert-required (Sam Roberts) [#9800](https://github.com/nodejs/node/pull/9800)
- [[`6b8d4cab41`](https://github.com/nodejs/node/commit/6b8d4cab41)] - **test**: tls key/cert ordering not necessary (Sam Roberts) [#9800](https://github.com/nodejs/node/pull/9800)
- [[`b2b2774325`](https://github.com/nodejs/node/commit/b2b2774325)] - **test**: refactor test-handle-wrap-close-abort (Rich Trott) [#10188](https://github.com/nodejs/node/pull/10188)
- [[`c65dfa9b12`](https://github.com/nodejs/node/commit/c65dfa9b12)] - **test**: add ES6 and strictEqual to test-fs-truncate (Adrian Estrada) [#10167](https://github.com/nodejs/node/pull/10167)
- [[`951ddb3d53`](https://github.com/nodejs/node/commit/951ddb3d53)] - **test**: improving crypto fips (James Tenenbaum) [#10002](https://github.com/nodejs/node/pull/10002)
- [[`a75883ec7c`](https://github.com/nodejs/node/commit/a75883ec7c)] - **test**: stream readableListening internal state (Italo A. Casas) [#9864](https://github.com/nodejs/node/pull/9864)
- [[`56a512f7be`](https://github.com/nodejs/node/commit/56a512f7be)] - **test**: check for error on invalid signal (Matt Phillips) [#10026](https://github.com/nodejs/node/pull/10026)
- [[`8e7f150a8e`](https://github.com/nodejs/node/commit/8e7f150a8e)] - **test**: refactor test-http-unix-socket (davidmarkclements) [#10072](https://github.com/nodejs/node/pull/10072)
- [[`717b4b4e8a`](https://github.com/nodejs/node/commit/717b4b4e8a)] - **test**: increase test coverage of BufferList (joyeecheung) [#10171](https://github.com/nodejs/node/pull/10171)
- [[`a56b22e881`](https://github.com/nodejs/node/commit/a56b22e881)] - **test**: fix flaky test-net-socket-timeout (Rich Trott) [#10172](https://github.com/nodejs/node/pull/10172)
- [[`58c5bdc57c`](https://github.com/nodejs/node/commit/58c5bdc57c)] - **test**: refactor test-net-keepalive.js (Kyle Corsi) [#9995](https://github.com/nodejs/node/pull/9995)
- [[`b868ce6763`](https://github.com/nodejs/node/commit/b868ce6763)] - **timers**: fix handling of cleared immediates (hveldstra) [#9759](https://github.com/nodejs/node/pull/9759)
- [[`95a0a67ff3`](https://github.com/nodejs/node/commit/95a0a67ff3)] - **tls**: do not refer to secureOptions as flags (Sam Roberts) [#9800](https://github.com/nodejs/node/pull/9800)
- [[`d50e8d8af9`](https://github.com/nodejs/node/commit/d50e8d8af9)] - **tls**: document and test option-less createServer (Sam Roberts) [#9800](https://github.com/nodejs/node/pull/9800)
- [[`89db5fcc23`](https://github.com/nodejs/node/commit/89db5fcc23)] - **tls**: fix/annotate connect arg comments (Sam Roberts) [#9800](https://github.com/nodejs/node/pull/9800)
- [[`20665f408b`](https://github.com/nodejs/node/commit/20665f408b)] - **tools**: enable block-spacing rule in .eslintrc (Rich Trott) [#10377](https://github.com/nodejs/node/pull/10377)
- [[`e8effa434e`](https://github.com/nodejs/node/commit/e8effa434e)] - **tools**: enforce consistent operator linebreak style (Michaël Zasso) [#10178](https://github.com/nodejs/node/pull/10178)
- [[`0162908708`](https://github.com/nodejs/node/commit/0162908708)] - **tools**: add macosx-firwall script to avoid popups (Daniel Bevenius) [#10114](https://github.com/nodejs/node/pull/10114)
- [[`3ac9e01faa`](https://github.com/nodejs/node/commit/3ac9e01faa)] - **url**: add a got host pattern in url.js (Axel Monroy) [#9653](https://github.com/nodejs/node/pull/9653)
- [[`9eaf2e9517`](https://github.com/nodejs/node/commit/9eaf2e9517)] - **watchdog**: add flag to mark handler as disabled (Bartosz Sosnowski) [#10248](https://github.com/nodejs/node/pull/10248)
- [[`969dcab5aa`](https://github.com/nodejs/node/commit/969dcab5aa)] - **win,msi**: add required UIRef for localized strings (Bill Ticehurst) [#8884](https://github.com/nodejs/node/pull/8884)

Windows 32-bit Installer: https://nodejs.org/dist/v6.10.0/node-v6.10.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v6.10.0/node-v6.10.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v6.10.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v6.10.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v6.10.0/node-v6.10.0.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v6.10.0/node-v6.10.0-darwin-x64.tar.gz \
Linux 32-bit Binary: https://nodejs.org/dist/v6.10.0/node-v6.10.0-linux-x86.tar.xz \
Linux 64-bit Binary: https://nodejs.org/dist/v6.10.0/node-v6.10.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v6.10.0/node-v6.10.0-linux-ppc64le.tar.xz \
Linux PPC BE 64-bit Binary: https://nodejs.org/dist/v6.10.0/node-v6.10.0-linux-ppc64.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v6.10.0/node-v6.10.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v6.10.0/node-v6.10.0-aix-ppc64.tar.gz \
SmartOS 32-bit Binary: https://nodejs.org/dist/v6.10.0/node-v6.10.0-sunos-x86.tar.xz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v6.10.0/node-v6.10.0-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v6.10.0/node-v6.10.0-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v6.10.0/node-v6.10.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v6.10.0/node-v6.10.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v6.10.0/node-v6.10.0.tar.gz \
Other release files: https://nodejs.org/dist/v6.10.0/ \
Documentation: https://nodejs.org/docs/v6.10.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

28ec25cbe3b1f4000a2a9f3bb565c36ff89745db35d5db07b99d223eb52941c8  node-v6.10.0-aix-ppc64.tar.gz
ff0c23dad9202c8a70049aa73582eff6d5324100d8db810e9553576817aa59eb  node-v6.10.0-darwin-x64.tar.gz
d522ab3cda6b66ab23535f4aaf1d2c6a9ab049074bf75a598249b95d52933b05  node-v6.10.0-darwin-x64.tar.xz
c00c9cb2a90b59c5d7d6960729a28dca03c04521ec8386710d48fe5676eccf11  node-v6.10.0-headers.tar.gz
12b4dc83ffdf149d9a38351b9bf22c167faa196c632d0a2047d5d06aacddf427  node-v6.10.0-headers.tar.xz
5f4024d2df1708ef80c5e7b1606d972e8f9779b350df832932174ce651e7795f  node-v6.10.0-linux-arm64.tar.gz
fc020ac1c3e6d3148ff353dd3b2b11017e556cabf405e385392f599ce34cdf16  node-v6.10.0-linux-arm64.tar.xz
dc529455b6f85a5c9ba2e6709ca90d580ac297cb39a8444a90e2d579761f211f  node-v6.10.0-linux-armv6l.tar.gz
41f5c6c106e8f3da4e139364739fcbbbc21012984533e2b6b33a61b234a40b3f  node-v6.10.0-linux-armv6l.tar.xz
7b068e6a1090ca0765455f35f99a2c090690ac8312d4a410ef481960f79216d8  node-v6.10.0-linux-armv7l.tar.gz
95efb476886df15cc6586dd26ecc50834a768e347cf95e861461853cfb40fc78  node-v6.10.0-linux-armv7l.tar.xz
44a9fbd5870d7fa45a50fcb1c14864b3c9d7500d2e42e611e5ad71d9c99e176b  node-v6.10.0-linux-ppc64le.tar.gz
2909cd33e7706cad999c0a424a8598c48be5894582a0d5afbc1051d3bb5a7347  node-v6.10.0-linux-ppc64le.tar.xz
182aa9ab11daa9dbb14a724c64a3cff52feaf010aae4bd27fab3a86644578126  node-v6.10.0-linux-ppc64.tar.gz
7ae1a65f4d62f899840cacd2017af4062e4167426b5d4ac56e186d91c2d8f4f8  node-v6.10.0-linux-ppc64.tar.xz
29ca73aec6d2adf33c9dfed20e62d7bea77ec5dc9b54001a1698c23d8bb815b7  node-v6.10.0-linux-s390x.tar.gz
e4dcd6cc4fccaa12fc9077e652a435090641391954853ecb718293fcbb9bd508  node-v6.10.0-linux-s390x.tar.xz
20b144da9bc3c314abfb760e90580a94091037257fc0b2c32871bc29257f7545  node-v6.10.0-linux-x64.tar.gz
0f28bef128ef8ce2d9b39b9e46d2ebaeaa8a301f57726f2eba46da194471f224  node-v6.10.0-linux-x64.tar.xz
e2e0fea44df5770f209bb80b8ec0269832958b67756445884e5e1abf20505eff  node-v6.10.0-linux-x86.tar.gz
684b1121730af2d1373c157608329c5234decd57f2f335051ad734c250b28f6f  node-v6.10.0-linux-x86.tar.xz
92d4da1a674b8a3376a6eb85f10ef3c3cbca50e4402e743309b9576af657834b  node-v6.10.0.pkg
295f4658b41b892e1ed510807e5f2269a51530ac96e6bfb49edce047606ef30e  node-v6.10.0-sunos-x64.tar.gz
f6abb0b6444cad6c4b46ae832ee12250d640a97d06741a85bac00d632128483b  node-v6.10.0-sunos-x64.tar.xz
c594c2659e4153c6064bb5d8dfc9233d044ac83286f522d9d19b0ace2d5086a2  node-v6.10.0-sunos-x86.tar.gz
23b1ee584898a4122882ae53f6c0636b9ae78fa717dc946ac133c115d11d08a3  node-v6.10.0-sunos-x86.tar.xz
01dae00dc0faa37a4b7a84098e2f04631827fc42e319eb66ccc3ab1d561ea42d  node-v6.10.0.tar.gz
f65d5d4b7253ee29f3ba4edabd3473845075e43569bceea4267e7bf3e00ebb96  node-v6.10.0.tar.xz
b48f5006d8a5104cae150775ad7568c833381e17330c349f4531e632af5b2850  node-v6.10.0-win-x64.7z
39c809fe02863963a4bb1eaa0daec59abcc92c0331447c3d3e06392a7f72b79c  node-v6.10.0-win-x64.zip
686514b71b98c03524c392db9064ec5ff794855c5d7c998152db4b80f793744e  node-v6.10.0-win-x86.7z
8ae92356479c43e1d1c07bb41a0769b6a38b121c50df993912bbe10d744ae099  node-v6.10.0-win-x86.zip
0fd99472edd0ef518ac557c24c037f83f4c82341348a2b397d51410550b6217c  node-v6.10.0-x64.msi
65add5aebb209705dacc8462a5376dcb612e19b3f2af1ce1e8a0c7e1c95e3e81  node-v6.10.0-x86.msi
9869e13d1157bcd08f3b57681d56e97060097124de1fc6c037f304b1ceb3ee28  win-x64/node.exe
b922a2fd0da33c495bada48958c0bd99714c967d0ed6a0899c3e29de6830cdc6  win-x64/node.lib
1416e93efaa418bf0457358e7039b4d06a8a6ebbc0a09fb0bff2f33a16b48dae  win-x64/node_pdb.7z
7326846399b53562a24eca0a6b10159fbd2f9823f2878fba487ab688d6529b61  win-x64/node_pdb.zip
0fb45b3dbecb2f8a1612667ba53f1fb01e967ccf30156dbdc1e6bbc1b240f3ee  win-x86/node.exe
525900301fa12a9a86834a99b9480aa1d97795d6b263e2d42ca2bb0b2a8c9bbf  win-x86/node.lib
eccab63147ec453ac2103ebfcb719a83b0dc0778c6a5c096b314ed42c011d58a  win-x86/node_pdb.7z
53eeebba3f95fbc886944cf9f2945cf2e87fe81a40910da0b76cb5bf1c1c513a  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQEcBAEBCAAGBQJYraegAAoJEJM7AfQLXKlGWUQH/0o5j/V/Zr1+wNPAJ8nThmx0
04hWMPYh6rU0wWi+QY7dUilEOjKgixNM5EgzcrEtdvTmFtfUFgaABduxVyIcd8L0
QGYSmUbcdygS4CfTcIRnf9yvVPF3wB9q+YObATtOgTEUspp3+02A85enQLV1CCb9
4vdRnHmlArg1eWcpLkgIliQ2DySHcAjB/wE63T60TxiTnrayza1L0D+ecnaqJYfw
aVRQp4o2xO8cYCLOvVArBECzJzE9lPtcImN177f2wLyES5laEYAOXfl5ZHnxJt9I
go6+ePtLEgTtOubm9jUox3U5ZdcYPNv+1yJhlYImqp4O9sWMI+ruNIWXCwkqqrE=
=YG3A
-----END PGP SIGNATURE-----

```
