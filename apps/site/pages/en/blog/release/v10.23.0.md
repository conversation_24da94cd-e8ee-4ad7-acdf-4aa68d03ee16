---
date: '2020-10-27T16:05:19.232Z'
category: release
title: Node v10.23.0 (LTS)
layout: blog-post
author: <PERSON>
---

### Notable changes

- **deps**:
  - upgrade npm to 6.14.8 (<PERSON><PERSON>) [#34834](https://github.com/nodejs/node/pull/34834)
- **n-api**:
  - create N-API version 7 (<PERSON>) [#35199](https://github.com/nodejs/node/pull/35199)
  - expose napi_build_version variable (<PERSON><PERSON><PERSON><PERSON>) [#27835](https://github.com/nodejs/node/pull/27835)
- **tools**:
  - add debug entitlements for macOS 10.15+ (<PERSON><PERSON>) [#34378](https://github.com/nodejs/node/pull/34378)

### Commits

- [[`b83f9a56fc`](https://github.com/nodejs/node/commit/b83f9a56fc)] - **build**: expose napi_build_version variable (<PERSON><PERSON><PERSON><PERSON>) [#27835](https://github.com/nodejs/node/pull/27835)
- [[`020ba1a2b8`](https://github.com/nodejs/node/commit/020ba1a2b8)] - **build**: enable backtrace when V8 is built for PPC and S390x (Michaël Zasso) [#32113](https://github.com/nodejs/node/pull/32113)
- [[`eee9412a8c`](https://github.com/nodejs/node/commit/eee9412a8c)] - **deps**: upgrade npm to 6.14.8 (Ruy Adorno) [#34834](https://github.com/nodejs/node/pull/34834)
- [[`038593d5ff`](https://github.com/nodejs/node/commit/038593d5ff)] - **deps**: upgrade npm to 6.14.7 (claudiahdz) [#34468](https://github.com/nodejs/node/pull/34468)
- [[`3564424625`](https://github.com/nodejs/node/commit/3564424625)] - **deps**: V8: cherry-pick eec10a2fd8fa (Stephen Belanger) [#33778](https://github.com/nodejs/node/pull/33778)
- [[`e9e86e1b60`](https://github.com/nodejs/node/commit/e9e86e1b60)] - **http2**: support non-empty DATA frame with END_STREAM flag (Carlos Lopez) [#33875](https://github.com/nodejs/node/pull/33875)
- [[`751820b6c2`](https://github.com/nodejs/node/commit/751820b6c2)] - **http2,doc**: minor fixes (Alba Mendez) [#28044](https://github.com/nodejs/node/pull/28044)
- [[`54c2bc2e62`](https://github.com/nodejs/node/commit/54c2bc2e62)] - **(SEMVER-MINOR)** **n-api**: create N-API version 7 (Gabriel Schulhof) [#35199](https://github.com/nodejs/node/pull/35199)
- [[`2eb627301c`](https://github.com/nodejs/node/commit/2eb627301c)] - **src**: allows escaping NODE_OPTIONS with backslashes (Maël Nison) [#24065](https://github.com/nodejs/node/pull/24065)
- [[`5170d14b36`](https://github.com/nodejs/node/commit/5170d14b36)] - **test**: fix test-linux-perf flakiness (Matheus Marchini) [#27615](https://github.com/nodejs/node/pull/27615)
- [[`21b86d7f19`](https://github.com/nodejs/node/commit/21b86d7f19)] - **test,v8**: skip less and stabilize test-linux-perf.js (Refael Ackermann) [#27364](https://github.com/nodejs/node/pull/27364)
- [[`ee11ab50a7`](https://github.com/nodejs/node/commit/ee11ab50a7)] - **tools**: add debug entitlements for macOS 10.15+ (Gabriele Greco) [#34378](https://github.com/nodejs/node/pull/34378)

Windows 32-bit Installer: https://nodejs.org/dist/v10.23.0/node-v10.23.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v10.23.0/node-v10.23.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v10.23.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v10.23.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v10.23.0/node-v10.23.0.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v10.23.0/node-v10.23.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v10.23.0/node-v10.23.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v10.23.0/node-v10.23.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v10.23.0/node-v10.23.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v10.23.0/node-v10.23.0-aix-ppc64.tar.gz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v10.23.0/node-v10.23.0-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v10.23.0/node-v10.23.0-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v10.23.0/node-v10.23.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v10.23.0/node-v10.23.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v10.23.0/node-v10.23.0.tar.gz \
Other release files: https://nodejs.org/dist/v10.23.0/ \
Documentation: https://nodejs.org/docs/v10.23.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

b7967e12fc50c73e9b70be8fd744539bb90f845b05947e93715c55f00e09d484  node-v10.23.0-aix-ppc64.tar.gz
c4dcaee7806b1fa1a2e832abd817bdd9b31a9c84181e7686067fd4eb5e3b12c3  node-v10.23.0-darwin-x64.tar.gz
ccc9eaf1d23bbd8e12c8381ead3d5570980d308b709887ca4795e200c0af92a8  node-v10.23.0-darwin-x64.tar.xz
4f1395cb9712f3192d6b1770ae91fae640ffacbd7f477ec144373895e3dbd433  node-v10.23.0-headers.tar.gz
002a2454f7826fd822418ee134282cabe0a1cd2dd26911dc856bbc19bc866777  node-v10.23.0-headers.tar.xz
d66f4912a0cb84678124d9a311bee7b204665fc62f83b0fc0d10b2f385feb524  node-v10.23.0-linux-arm64.tar.gz
611bc364ac089718ed8bc94226c3fce76446caeb3b57a8c8ebb8f1753ca9cad6  node-v10.23.0-linux-arm64.tar.xz
3bbf3183e960a0344d505feecaa2d151cc7346b4c629f9151497441c4b7b5d0b  node-v10.23.0-linux-armv6l.tar.gz
00d5b684e2c5ea0bac1cdabddf3cad2a0c7fea2c12a3a134378986aa1630a4da  node-v10.23.0-linux-armv6l.tar.xz
01118226e883c69c1dc324ab42093201ae5ef46e98116abbb6acd3775b8f9c58  node-v10.23.0-linux-armv7l.tar.gz
8284e4dee2a3edced8b2ac4f5ae69a3bf12ad86b4f8fd691531cc67180156a6d  node-v10.23.0-linux-armv7l.tar.xz
4148b1ba71a4577744797e65b03e3d9cd121a28cc23dd34c20c9d61250925e71  node-v10.23.0-linux-ppc64le.tar.gz
c3ba476c7616281541290ab0b461caaaa5f61241855ac9b3eda4a16096c61513  node-v10.23.0-linux-ppc64le.tar.xz
ec34919ef15c4bc3dfb2a4f7cf1c81fde003a5fb76fe36b3d35dc029bcb2f65f  node-v10.23.0-linux-s390x.tar.gz
421216ff143357a6ef27a293ac1aa1a3ad0eb4ca26f5b62d8301048cf5a2d63a  node-v10.23.0-linux-s390x.tar.xz
19cccb78f0881a78051291a50200200a0303649ee84e5489c771d3b4e4bd0e51  node-v10.23.0-linux-x64.tar.gz
3293b1ea9b9e08d9dbc5bb8717a80084e24cadfe3b030a8ebfdadea5f963dfc9  node-v10.23.0-linux-x64.tar.xz
5a3207d39da25c3e65c8734efc30412534979f1b56840b81cd54629502f59d22  node-v10.23.0.pkg
bf2d3a8adb0892e5905345e0b2f6a040ea3245c69e0ea58705a1f0eccece5751  node-v10.23.0-sunos-x64.tar.gz
74f24147b362b5db04764fdd7605c80968060bd3e0eaa9171624213395fd92de  node-v10.23.0-sunos-x64.tar.xz
d93041d1d1e75cef6c562c29b63333b2f39a16de3cb0310b695130cea2ae7f27  node-v10.23.0.tar.gz
fd356039d5c223f020db514b64e4619b285e669f8f5f0b6232458d4440c6741f  node-v10.23.0.tar.xz
6108dce9300ee4c7ba207dd704e6f8ec32df6634f0ad30542d6149b2074019ca  node-v10.23.0-win-x64.7z
efafacc17f72ed96b4a08eda185b61e98f7aeb8a26a6776312055b8c0c492313  node-v10.23.0-win-x64.zip
610b696bf66b3bcec1b16c29aa3e6bb0e43ec392e3442de71545fbb4af5f1c5b  node-v10.23.0-win-x86.7z
c486d13ae7c4dab3786b148b12c6850b8c584b1e978d5a246d418cb7e750021b  node-v10.23.0-win-x86.zip
762e66005f192ac59f3de9476291e6685166088408e33ed73e208614a0c82480  node-v10.23.0-x64.msi
e1eec162932ecba196115e3dfe6fb7bf7bf6ee59fb701840ab5257a375132128  node-v10.23.0-x86.msi
26b725d80a42f84dd45d70e8923f10f50e5d226b4ba5b160a218f9b91f231c92  win-x64/node.exe
22a69cca8fcd3b6b7bbac34abf41f4accb977eaa5c471518145cc3359b395fa1  win-x64/node.lib
d15410605b77cf8c138f6e84e578d294f6be5d04d741614a4f5f3e5e9563b8a4  win-x64/node_pdb.7z
57267416a4b06b4e6e9b6e2c985ed831caf6cdebee8500d806e1df5613bf2021  win-x64/node_pdb.zip
9bafc33376eb145f172a6ae183936ecedeacb083083fe51cac688006590fa125  win-x86/node.exe
d9c5f1c12ddeded3354be28ccf88393105bbc97e4a66a8847500946a9d8ce287  win-x86/node.lib
b53e9ca4f0749a50cce028c859cc0711bb3c37edd114b1c5f46bd88ad1b4a3f2  win-x86/node_pdb.7z
156acafb8b51091de4ac65316c6dc6d0b2d179964354262eb2e393ad30216172  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----
Version: GnuPG v2.0.22 (GNU/Linux)

iQIcBAEBCAAGBQJfmESPAAoJEMQ87EXBerk8848QAIKTeJ0QEwljuzODo7q260h8
Wjs9CpmT78V++QY+FO6vcPt1us+Eq1xHvhnbE7tTwgLPFLzIPTZbBYvmPFYi+lZF
0vXveHzhaSZ6OqC6VTC/H/ww4xLiSX9YIuthYxeSzTz+PFybIFRRgormPQ0FRgjK
sm5BD6TfYnhzWZXGBZlcPGs0wHseTzKJ7Mc95chLUy5AAr/yfmkGH0gOtbe1Kl1X
8coJEBCUhvgLgpTtRpfxZ2xz6GbWUOVk5yj1UxnOxmxp51eTjnQVwYGH5SRdyLTk
2wTyjTTVGYyO6h0flNo4nR7++6Zhd3+p27aPVNGQR4d4CEWZmQ7UimlRBifpIPgv
xfJXQ8+ROD0OWMuEvEeUWDH/t+OQC+1aOO/1sNiJiW8CQwGokfACY/1hbgoQULk4
MkL6p8Fai8hBbo7BSh6OafZQUMcxg29sPQ0msqIJWamDra8ULXf3R/FMsrDKZu0b
uMfxK0dvQoH20s5QUvSjCP9wIxkYFmkXqdAWsyaS+1Tu8zqUf9Szs6J743QuSuLz
IuIDsl3/fT+3oVWhRteZlOFvl2e9vjLUW4lzmpVDGVE283rQmngFMCKcSDAt1LDQ
R6N6gtWM5Cls/h+3WC+qBkgHI1jkE/xyLw4VR3jVFwJwwGyoF/lKVp+EbTJrn8Qc
MTBiPVWFXP8ICFTDCLeT
=0Rrr
-----END PGP SIGNATURE-----

```
