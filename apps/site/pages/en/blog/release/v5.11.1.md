---
date: '2016-05-05T23:00:21.696Z'
category: release
title: Node v5.11.1 (Stable)
layout: blog-post
author: <PERSON>
---

Please see our [blog post](/blog/vulnerability/openssl-may-2016) for more info on the security contents of this release.

### Notable changes

- **buffer**: safeguard against accidental kNo<PERSON><PERSON><PERSON>ill (Сковорода Никита Андреевич) [nodejs/node-private#35](https://github.com/nodejs/node-private/pull/35)
- **deps**: upgrade openssl sources to 1.0.2h (Shi<PERSON><PERSON>) [#6552](https://github.com/nodejs/node/pull/6552)

### Commits

- [[`35f06df782`](https://github.com/nodejs/node/commit/35f06df782)] - **buffer**: safeguard against accidental kNo<PERSON><PERSON>Fill (Сковорода Никита Андреевич) [nodejs/node-private#35](https://github.com/nodejs/node-private/pull/35)
- [[`99920480ae`](https://github.com/nodejs/node/commit/99920480ae)] - **buffer**: fix a typo in Buffer example code (Mr C0B) [#6361](https://github.com/nodejs/node/pull/6361)
- [[`d9f7b025d4`](https://github.com/nodejs/node/commit/d9f7b025d4)] - **deps**: update openssl asm and asm_obsolete files (Shigeki Ohtsu) [#6552](https://github.com/nodejs/node/pull/6552)
- [[`f316fd20a0`](https://github.com/nodejs/node/commit/f316fd20a0)] - **deps**: add -no_rand_screen to openssl s_client (Shigeki Ohtsu) [#1836](https://github.com/nodejs/node/pull/1836)
- [[`263cc34657`](https://github.com/nodejs/node/commit/263cc34657)] - **deps**: fix asm build error of openssl in x86_win32 (Shigeki Ohtsu) [#1389](https://github.com/nodejs/node/pull/1389)
- [[`889d1151de`](https://github.com/nodejs/node/commit/889d1151de)] - **deps**: fix openssl assembly error on ia32 win32 (Fedor Indutny) [#1389](https://github.com/nodejs/node/pull/1389)
- [[`ba49b636b8`](https://github.com/nodejs/node/commit/ba49b636b8)] - **deps**: copy all openssl header files to include dir (Shigeki Ohtsu) [#6552](https://github.com/nodejs/node/pull/6552)
- [[`cdad83a789`](https://github.com/nodejs/node/commit/cdad83a789)] - **deps**: upgrade openssl sources to 1.0.2h (Shigeki Ohtsu) [#6552](https://github.com/nodejs/node/pull/6552)
- [[`c1ddefdd79`](https://github.com/nodejs/node/commit/c1ddefdd79)] - **openssl**: fix keypress requirement in apps on win32 (Shigeki Ohtsu) [#1389](https://github.com/nodejs/node/pull/1389)
- [[`bec5d50f1e`](https://github.com/nodejs/node/commit/bec5d50f1e)] - **test**: fix alpn tests for openssl1.0.2h (Shigeki Ohtsu) [#6552](https://github.com/nodejs/node/pull/6552)

Windows 32-bit Installer: https://nodejs.org/dist/v5.11.1/node-v5.11.1-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v5.11.1/node-v5.11.1-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v5.11.1/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v5.11.1/win-x64/node.exe \
Mac OS X 64-bit Installer: https://nodejs.org/dist/v5.11.1/node-v5.11.1.pkg \
Mac OS X 64-bit Binary: https://nodejs.org/dist/v5.11.1/node-v5.11.1-darwin-x64.tar.gz \
Linux 32-bit Binary: https://nodejs.org/dist/v5.11.1/node-v5.11.1-linux-x86.tar.xz \
Linux 64-bit Binary: https://nodejs.org/dist/v5.11.1/node-v5.11.1-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v5.11.1/node-v5.11.1-linux-ppc64le.tar.xz \
SmartOS 32-bit Binary: https://nodejs.org/dist/v5.11.1/node-v5.11.1-sunos-x86.tar.xz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v5.11.1/node-v5.11.1-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v5.11.1/node-v5.11.1-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v5.11.1/node-v5.11.1-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v5.11.1/node-v5.11.1-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v5.11.1/node-v5.11.1.tar.gz \
Other release files: https://nodejs.org/dist/v5.11.1/ \
Documentation: https://nodejs.org/docs/v5.11.1/api/

Shasums (GPG signing hash: SHA512, file hash: SHA256):

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA512

7a992f61dc535c696ba2e236e3664ba669680f7e1a204e42166412cc3476503a  node-v5.11.1-darwin-x64.tar.gz
0fe7b4f9ad94777e642ba5efff0bd899e15fa9fb6799cd22794ac4bd5006cc23  node-v5.11.1-darwin-x64.tar.xz
85072d2f231a947862984ec79f44b990287a6ba1589342c0ac4fea54d9ec88e2  node-v5.11.1-headers.tar.gz
b2a607a0cfb1252014bc01007b7c4d7790c78ba9fbe009accf89c16c06257397  node-v5.11.1-headers.tar.xz
8df5fa56ea1f79efc6f8baa9a6784bb1b0596fb7ef1d631694e35a89b3840de6  node-v5.11.1-linux-arm64.tar.gz
cb09abcd897d44959069c17d29ce6361ad20bd15e94cf72c1c5cd0976a08fa91  node-v5.11.1-linux-arm64.tar.xz
d6ffd43fd0546a5830117bb76979f074a04f9f2323bfc786ca5bd25f254149bc  node-v5.11.1-linux-armv6l.tar.gz
a9f2c90cd7be087dff8f4fb15cf603c1bba9e95f23bd66958c41cbedc72e13a7  node-v5.11.1-linux-armv6l.tar.xz
d69ebad9bb86e2d54b6c8774e5bfd055b507dfd4da4b4a0a9ad8543358fd57de  node-v5.11.1-linux-armv7l.tar.gz
17db8b2a9221ea7455c4471198965a9b6d6bc0c97187265e64925826a072dcaa  node-v5.11.1-linux-armv7l.tar.xz
a349b77703902702ec2ec35de27150d19f73ca44045577c577312a5bde9e0b04  node-v5.11.1-linux-ppc64le.tar.gz
d0653882535519ddc6637a04eeb797b02a4ee708f14dd6cc4d84f44fcb65d0a3  node-v5.11.1-linux-ppc64le.tar.xz
d8e30e79a1e4ad56f55ef59facdf913c950e9664528f59f4388e85fdd899dfde  node-v5.11.1-linux-x64.tar.gz
35ffd25963254decbec1069da318597ed2fb032d2782f7cbdfdd41dc6af99ee9  node-v5.11.1-linux-x64.tar.xz
5c8269b21400327eaae94f52d220127a90eca40ab7574f9da07811c48d25570f  node-v5.11.1-linux-x86.tar.gz
01bcebec40d2030ebfd87fec82249fe1e2919bedcbc4998ee0b1c0365a33e7b4  node-v5.11.1-linux-x86.tar.xz
f06c2d54057c55913749991a6636e78c4180b989a6ee67bdc0017252bfc6c9c9  node-v5.11.1.pkg
65e80b25ba648b62faae99f4f4df86eeea6ed448c592cb638349a63eefcb5d0f  node-v5.11.1-sunos-x64.tar.gz
61507107d14720f1dd24a25bea2bd626ebda63da5d495e5d8f2cdb1a5c03a696  node-v5.11.1-sunos-x64.tar.xz
24e9095e7fdb1765d2acf569209a194b16c048374a804a97fc5fec123b1b9217  node-v5.11.1-sunos-x86.tar.gz
c211f830ccb2198571f0e843101b7c9917ef0feae165c7e28a795b61798968a5  node-v5.11.1-sunos-x86.tar.xz
be1211ce415ecc48412ce3e9d5b48cf9ec8e99b34f25b4e1909a02679cc72fab  node-v5.11.1.tar.gz
f11bc3727e7ea74cffa940be258beaf1f81bee90537d13caa9e748ae6105661f  node-v5.11.1.tar.xz
53c1744bfc845deb4c3a727d9e581f845c34652eb994a81f651a804f67ae749f  node-v5.11.1-x64.msi
b7c6f647a037f17d1dbdb602af8c35934dd06fa4fb8b35108bb311cbddbcb2ab  node-v5.11.1-x86.msi
df52219a3fde636f0fb73e065c85b28904eefd81c16c34ec5e6576b7fd0d886b  win-x64/node.exe
fe0654ca23f0f3c5b290d76efd8f18ba6a6d60445257eb4d59641dc647d33770  win-x64/node.lib
11160ea9fd4ae6db9c0fcee529cfae3d9fb3eed15a0428febbf6033c00494646  win-x86/node.exe
fa3fa032d8b04e65ff20cd34bb0e690f5f88ca023d9783aa5672215f568117b1  win-x86/node.lib
-----BEGIN PGP SIGNATURE-----
Comment: GPGTools - https://gpgtools.org

iQIcBAEBCgAGBQJXK8+rAAoJELY7U1pMIGypxY8QANK8IsVkwb0u1ycZO65j+zIn
ChB6k+0f5wTo8ueIrq8uGNKyvYJpGmqN0/a7C61OGizfDMQcEXts+S4uEDkYDKlC
qR+TiwbKQBEE52T7+jcJOnXb2A1aJFNAItNEq/GVM6mjxgxNBA3gM/9PMEoBvfHI
hp5jF8CgKmc4ZfrYWUuHuycfeyuCu0/99TV9nS8EDLyFpgovEBRngGOmWmn/xV0h
Gv9bu0Ixih6oOEHBL3+dH9BJ6Fvf75gsazumKsQSXyoa7f/JfoLPbT8ecUvS9Mm/
JX+sNiNw9iBbx0iFSHVh03b5CsH/ateFalSDjKhNqOMykniUNI+FELs5/1/wU+6B
VzniJpbRR9tU2A0FdARvbnFMj1KJfUmsXGRlh399k2OmD6IoYkWtbsHcyFB6vGXE
F8w8UihBHQ69GAzTltQnyn3QO7s/0kF9UqozAJ3ApvA8Sq9cSIXmE1s5+a5GD+Cc
btrehhpUOgUZQ2ol4N2BOuKVc+svUWgiZ5QF1e2lNdOWnYCP+fSFqkf5PRRd4xW2
oMwnFO3Ehc5VSGHzVwGKfltmot+vkGD9xbDJZH1vCzYuj9zf66TbduPdFk0Exf0f
1gp5Na7EypJRgcLjLVSRriXVz6WYybgbVbA+7WcDw8jmKYwHdMUIRMRUFGMy/uts
YkqY3iJ4b5wydOEg+0Jt
=nE6V
-----END PGP SIGNATURE-----

```
