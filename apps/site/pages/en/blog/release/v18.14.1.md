---
date: '2023-02-16T22:22:52.706Z'
category: release
title: Node v18.14.1 (LTS)
layout: blog-post
author: <PERSON>
---

### Notable Changes

The following CVEs are fixed in this release:

- **[CVE-2023-23918](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2023-23918)**: Node.js Permissions policies can be bypassed via process.mainModule (High)
- **[CVE-2023-23919](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2023-23919)**: Node.js OpenSSL error handling issues in nodejs crypto library (Medium)
- **[CVE-2023-23936](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2023-23936)**: Fetch API in Node.js did not protect against CRLF injection in host headers (Medium)
- **[CVE-2023-24807](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2023-24807)**: Regular Expression Denial of Service in Headers in Node.js fetch API (Low)
- **[CVE-2023-23920](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2023-23920)**: Node.js insecure loading of ICU data through ICU_DATA environment variable (Low)

More detailed information on each of the vulnerabilities can be found in [February 2023 Security Releases](/blog/vulnerability/february-2023-security-releases/) blog post.

This security release includes OpenSSL security updates as outlined in the recent
[OpenSSL security advisory](https://www.openssl.org/news/secadv/20230207.txt).

### Commits

- \[[`8393ebc72d`](https://github.com/nodejs/node/commit/8393ebc72d)] - **build**: build ICU with ICU_NO_USER_DATA_OVERRIDE (RafaelGSS) [nodejs-private/node-private#379](https://github.com/nodejs-private/node-private/pull/379)
- \[[`004e34d046`](https://github.com/nodejs/node/commit/004e34d046)] - **crypto**: clear OpenSSL error on invalid ca cert (RafaelGSS) [#46572](https://github.com/nodejs/node/pull/46572)
- \[[`5e0142a852`](https://github.com/nodejs/node/commit/5e0142a852)] - **deps**: cherry-pick Windows ARM64 fix for openssl (Richard Lau) [#46572](https://github.com/nodejs/node/pull/46572)
- \[[`f71fe278a6`](https://github.com/nodejs/node/commit/f71fe278a6)] - **deps**: update archs files for quictls/openssl-3.0.8+quic (RafaelGSS) [#46572](https://github.com/nodejs/node/pull/46572)
- \[[`2c6817e42b`](https://github.com/nodejs/node/commit/2c6817e42b)] - **deps**: upgrade openssl sources to quictls/openssl-3.0.8+quic (RafaelGSS) [#46572](https://github.com/nodejs/node/pull/46572)
- \[[`f0afa0bfe5`](https://github.com/nodejs/node/commit/f0afa0bfe5)] - **deps**: update undici to 5.19.1 (Node.js GitHub Bot) [#46634](https://github.com/nodejs/node/pull/46634)
- \[[`c26a34c13e`](https://github.com/nodejs/node/commit/c26a34c13e)] - **deps**: update undici to 5.18.0 (Node.js GitHub Bot) [#46634](https://github.com/nodejs/node/pull/46634)
- \[[`db93ee4a15`](https://github.com/nodejs/node/commit/db93ee4a15)] - **deps**: update undici to 5.17.1 (Node.js GitHub Bot) [#46634](https://github.com/nodejs/node/pull/46634)
- \[[`b4e49fb02c`](https://github.com/nodejs/node/commit/b4e49fb02c)] - **deps**: update undici to 5.16.0 (Node.js GitHub Bot) [#46634](https://github.com/nodejs/node/pull/46634)
- \[[`90994e6a2c`](https://github.com/nodejs/node/commit/90994e6a2c)] - **deps**: update undici to 5.15.1 (Node.js GitHub Bot) [#46634](https://github.com/nodejs/node/pull/46634)
- \[[`00302fc7ac`](https://github.com/nodejs/node/commit/00302fc7ac)] - **deps**: update undici to 5.15.0 (Node.js GitHub Bot) [#46634](https://github.com/nodejs/node/pull/46634)
- \[[`0e3b796cc5`](https://github.com/nodejs/node/commit/0e3b796cc5)] - **lib**: makeRequireFunction patch when experimental policy (RafaelGSS) [nodejs-private/node-private#371](https://github.com/nodejs-private/node-private/pull/371)
- \[[`7cccd5565f`](https://github.com/nodejs/node/commit/7cccd5565f)] - **policy**: makeRequireFunction on mainModule.require (RafaelGSS) [nodejs-private/node-private#371](https://github.com/nodejs-private/node-private/pull/371)

Windows 32-bit Installer: https://nodejs.org/dist/v18.14.1/node-v18.14.1-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v18.14.1/node-v18.14.1-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v18.14.1/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v18.14.1/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v18.14.1/node-v18.14.1.pkg \
macOS Apple Silicon 64-bit Binary: https://nodejs.org/dist/v18.14.1/node-v18.14.1-darwin-arm64.tar.gz \
macOS Intel 64-bit Binary: https://nodejs.org/dist/v18.14.1/node-v18.14.1-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v18.14.1/node-v18.14.1-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v18.14.1/node-v18.14.1-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v18.14.1/node-v18.14.1-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v18.14.1/node-v18.14.1-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v18.14.1/node-v18.14.1-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v18.14.1/node-v18.14.1-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v18.14.1/node-v18.14.1.tar.gz \
Other release files: https://nodejs.org/dist/v18.14.1/ \
Documentation: https://nodejs.org/docs/v18.14.1/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

702bfe7aaebb49870487ca7a4060ae143caf662bdfdfd3f4c9f033863898f41b  node-v18.14.1-aix-ppc64.tar.gz
513f3ab25244c5ea3eedc6218b7418dcd7a0f20e143fdbf8955f0ea74f53c2d8  node-v18.14.1-darwin-arm64.tar.gz
caeaef362c2c41ce918339f002b35c8d55dba841fc3fa26695411e8aa92b7ad9  node-v18.14.1-darwin-arm64.tar.xz
940cfc29b42d174bf8fa271644508ab357b282a669b73adf0935982382ef5f38  node-v18.14.1-darwin-x64.tar.gz
adc24c3d08e88ee7e1191d77984dd911fceb3b50d5d5042541e323ce03c820cf  node-v18.14.1-darwin-x64.tar.xz
918b35ae943fcd1bb3ad590ceb7f0441881ecdf5940a2039e4fb5762c40480d2  node-v18.14.1-headers.tar.gz
54ccb6f7d47311888acbddd5a6e6dc29a84bfc63b6b0e741a011e7f3edb6d0c0  node-v18.14.1-headers.tar.xz
608af6ad3cf5a171c889c022cb51a460bdbf57fbb8fbcd40612ea8063aa95f07  node-v18.14.1-linux-arm64.tar.gz
52f33439dcca865bbb70194091f6ce0c02b2a32d5a744901f175fff1ca6d0c86  node-v18.14.1-linux-arm64.tar.xz
f2d25e36289ce702e38ed9c86e3c7a848166b89cb8b54db4e05c9fcd98613aca  node-v18.14.1-linux-armv7l.tar.gz
851095516b968bc199e779d5be1ea87c443004ede4db3ec9f122156840ce699f  node-v18.14.1-linux-armv7l.tar.xz
988198c90fd9f731e754a592b4f4ef85bbb289b54aa4a35603460df39b3abe9c  node-v18.14.1-linux-ppc64le.tar.gz
44c02b1a48b3a503c1e31669bdfd20cb791b0be8dd0a2b12afb4d9f437c3a51a  node-v18.14.1-linux-ppc64le.tar.xz
50be74fc7a5eb00c41b5a19bfd0000ca818187f3b28f6796877aafe4f5f2b67a  node-v18.14.1-linux-s390x.tar.gz
6f4d9f65b439e0940abb813d1b5711839bc42ba81a4286f263b7b4e3e7cd32cf  node-v18.14.1-linux-s390x.tar.xz
6a7c6862b86cb01b892ca5967dba14bd3122dbfed9d5c9fedd30585d5974f1f6  node-v18.14.1-linux-x64.tar.gz
ea3bd72ad5ccaa1d9bcb40da69a60f249d29672d41740fe8f3e976d0e9078f65  node-v18.14.1-linux-x64.tar.xz
f461ac7eb1d18098fd37e9ba83b8cfcef9fddebcbf59ea6b638f887e4541d912  node-v18.14.1.pkg
2e597cd6c56ffc14b174f2bddb459dd3ce227e26505008805c5d4f55bb1e46f1  node-v18.14.1.tar.gz
eec353438266fd0aef53a9446be10b32ee6e74d08e32dd5454b382ff6793da04  node-v18.14.1.tar.xz
13ce74a476ccf7917dd8c9ecd7635f6174665a14ab629c3f2dc0709cd31d68d6  node-v18.14.1-win-x64.7z
b0ec709961ab9b2e46d616d33f26d0ff1548b02045c7650f018e809e7a6cbbda  node-v18.14.1-win-x64.zip
33d7b2abb88b9a400c02bd9ebb2a61c522d62d73aa5e103fff86779af81c0dc9  node-v18.14.1-win-x86.7z
903decc09eca1e6d4ad67b4ba22b24ea62a3d3d9b8101a60116932050cc03295  node-v18.14.1-win-x86.zip
4a42e49e5c6e2340be14b5e3d72d9a75abf8fc8894d816d46321c28ea0ed4baa  node-v18.14.1-x64.msi
7a3fdde01f758fc4d6cb9f491db9ac30c39f20c19a27982898a9e0b4e61317d2  node-v18.14.1-x86.msi
3888921fe297b0cc2f050c12ef8181bdf397823edf4f62d9e153cc5b424cfd72  win-x64/node.exe
28973fbe8ae770a132b4851b0166ec9ba435595f786bb05fc47c3611fefb1cf2  win-x64/node.lib
ea9ace5ce2488b6b274e70cc314e4bc17d4cfc142105d8ff57f65c7b085de03e  win-x64/node_pdb.7z
8c569d8992c94de0bf2c00cd3746268762d33116dff3dc8e98e6a9bbb18bc4e0  win-x64/node_pdb.zip
cbbdc31c70de4c6c09ce9206064e972767527a6d098176ff76f5b0bd3f524a73  win-x86/node.exe
a10d89fbd502902abaf307fe73b604a4733d403ac5c577e536607c1f3b16ac14  win-x86/node.lib
c3f9e82b72c8118be7f84c9599d6f67889b6662263d2b913b3be9f03a54c5887  win-x86/node_pdb.7z
1158e74809497fafb7269515ee97d9a0f2bd761d0feb68799103374d65aff40b  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQGzBAEBCAAdFiEEiQwI24V5Fi/uDfnbi+q0389VXvQFAmPuq70ACgkQi+q0389V
XvSlQwv+I/0rceTegCp5ngUJ4E9GfvqKcBzJg7eZv3JvHv2td8tAZi3xwZIFgkZt
twoDkGDu86QlaM/wSyQCoJQ+T71IRfZj7QZnofjhogNdOXZomZHmD88edBGAa1Qt
1UqYO92ht2DnZ1vEdwEVmAFYw6e141Ccx+crgjviCm7lU5P5cm+8Ll0MjLRJ7piB
hha5QnipmAom5oNpncv/wnXiJMgTkt/nwnjg9U+3XrlALbaz4a4sMIKyaQMJflow
jZCh80lXS3QqCGR72OlYWdtDrnqj5g9KDhwlrFiv86CezbRAqv7ms/UiH+sqMnOD
SQPFZwHoTj9fAwodLx42CoCnMEyCBiAxLd/7es+GHu4F463LzBCC7mI1HsQySk42
mO5CvfQo7APjwQ3Gl1Rc61wl2KNevX8+tq7vuxARgoRcaRUjCt4fU4LB5sBqMwVH
2uPYAuH3PtJhxKklDyDWpzK8ADUTl1GxusXHlKzVecfZq8crHvfJ+ojhumMkuz3F
jln0bvQH
=xxvM
-----END PGP SIGNATURE-----

```
