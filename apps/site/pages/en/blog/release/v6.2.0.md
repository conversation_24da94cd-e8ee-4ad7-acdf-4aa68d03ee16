---
date: '2016-05-17T21:12:15.115Z'
category: release
title: Node v6.2.0 (Current)
layout: blog-post
author: <PERSON>
---

### Notable Changes

- **buffer**: fix lastIndexOf and indexOf in various edge cases (<PERSON>) [#6511](https://github.com/nodejs/node/pull/6511)
- **child_process**: use /system/bin/sh on android (<PERSON>) [#6745](https://github.com/nodejs/node/pull/6745)
- **deps**:
  - upgrade npm to 3.8.9 (<PERSON>) [#6664](https://github.com/nodejs/node/pull/6664)
  - upgrade to V8 5.0.71.47 (<PERSON>) [#6572](https://github.com/nodejs/node/pull/6572)
  - upgrade libuv to 1.9.1 (<PERSON><PERSON>) [#6796](https://github.com/nodejs/node/pull/6796)
  - Intl: ICU 57 bump (<PERSON>) [#6088](https://github.com/nodejs/node/pull/6088)
- **repl**:
  - copying tabs shouldn't trigger completion (<PERSON>) [#5958](https://github.com/nodejs/node/pull/5958)
  - exports `Recoverable` (Blake Embrey) [#3488](https://github.com/nodejs/node/pull/3488)
- **src**: add O_NOATIME constant (Rich Trott) [#6492](https://github.com/nodejs/node/pull/6492)
- **src,module**: add --preserve-symlinks command line flag (James M Snell) [#6537](https://github.com/nodejs/node/pull/6537)
- **util**: adhere to `noDeprecation` set at runtime (Anna Henningsen) [#6683](https://github.com/nodejs/node/pull/6683)

As of this release the 6.X line now includes 64-bit binaries for Linux on Power Systems
running in big endian mode in addition to the existing 64-bit binaries for running in little endian mode.

### Commits

- [[`955c90d725`](https://github.com/nodejs/node/commit/955c90d725)] - **benchmark,test,lib**: remove extra spaces (Rich Trott) [#6645](https://github.com/nodejs/node/pull/6645)
- [[`9cd14ced09`](https://github.com/nodejs/node/commit/9cd14ced09)] - **buffer**: fix UCS2 indexOf for odd buffer length (Anna Henningsen) [#6511](https://github.com/nodejs/node/pull/6511)
- [[`a550ddbf3c`](https://github.com/nodejs/node/commit/a550ddbf3c)] - **buffer**: fix needle length misestimation for UCS2 (Anna Henningsen) [#6511](https://github.com/nodejs/node/pull/6511)
- [[`6fc20c5a97`](https://github.com/nodejs/node/commit/6fc20c5a97)] - **buffer**: fix lastIndexOf crash for overlong needle (Anna Henningsen) [#6511](https://github.com/nodejs/node/pull/6511)
- [[`44015754a3`](https://github.com/nodejs/node/commit/44015754a3)] - **buffer**: fix lastIndexOf index underflow issue (Anna Henningsen) [#6511](https://github.com/nodejs/node/pull/6511)
- [[`6032dc25cc`](https://github.com/nodejs/node/commit/6032dc25cc)] - **build**: add Make `doc-only` target (Jesse McCarthy) [#3888](https://github.com/nodejs/node/pull/3888)
- [[`3af9382a5d`](https://github.com/nodejs/node/commit/3af9382a5d)] - **build**: don't compile with -B, redux (Ben Noordhuis) [#6650](https://github.com/nodejs/node/pull/6650)
- [[`5149d66702`](https://github.com/nodejs/node/commit/5149d66702)] - **build**: fix DESTCPU detection for binary target (Richard Lau) [#6310](https://github.com/nodejs/node/pull/6310)
- [[`6eed6a3ac0`](https://github.com/nodejs/node/commit/6eed6a3ac0)] - **build,test**: fix build-addons dependency chain (Ben Noordhuis) [#6652](https://github.com/nodejs/node/pull/6652)
- [[`e0240ab592`](https://github.com/nodejs/node/commit/e0240ab592)] - **child_process**: use /system/bin/sh on android (Ben Noordhuis) [#6745](https://github.com/nodejs/node/pull/6745)
- [[`e8c9f01cdd`](https://github.com/nodejs/node/commit/e8c9f01cdd)] - **crypto**: disable ssl compression at build time (Ben Noordhuis) [#6582](https://github.com/nodejs/node/pull/6582)
- [[`62690aa0be`](https://github.com/nodejs/node/commit/62690aa0be)] - **deps**: update comment about PURIFY define (Ben Noordhuis) [#6582](https://github.com/nodejs/node/pull/6582)
- [[`bddf413412`](https://github.com/nodejs/node/commit/bddf413412)] - **deps**: upgrade npm to 3.8.9 (Rebecca Turner) [#6664](https://github.com/nodejs/node/pull/6664)
- [[`a6ca5e559a`](https://github.com/nodejs/node/commit/a6ca5e559a)] - **deps**: upgrade to V8 5.0.71.47 (Ali Ijaz Sheikh) [#6572](https://github.com/nodejs/node/pull/6572)
- [[`16159c23ed`](https://github.com/nodejs/node/commit/16159c23ed)] - **deps**: limit regress/regress-crbug-514081 v8 test (Michael Dawson) [#6678](https://github.com/nodejs/node/pull/6678)
- [[`2d84ac735a`](https://github.com/nodejs/node/commit/2d84ac735a)] - **deps**: upgrade libuv to 1.9.1 (Saúl Ibarra Corretgé) [#6796](https://github.com/nodejs/node/pull/6796)
- [[`7a6d2ad181`](https://github.com/nodejs/node/commit/7a6d2ad181)] - **deps**: Intl: Check in "small-icu" 57.1 (Steven R. Loomis) [#6088](https://github.com/nodejs/node/pull/6088)
- [[`ee1e5a267d`](https://github.com/nodejs/node/commit/ee1e5a267d)] - **deps**: Intl: ICU 57 bump (Steven R. Loomis) [#6088](https://github.com/nodejs/node/pull/6088)
- [[`a4ed7dfb3d`](https://github.com/nodejs/node/commit/a4ed7dfb3d)] - **doc**: Add CTC meeting minutes for 2016-05-04 (Michael Dawson) [#6579](https://github.com/nodejs/node/pull/6579)
- [[`5c7da210df`](https://github.com/nodejs/node/commit/5c7da210df)] - **doc**: refactor the changelog by version (James M Snell) [#6503](https://github.com/nodejs/node/pull/6503)
- [[`4f2a55f92f`](https://github.com/nodejs/node/commit/4f2a55f92f)] - **doc**: fix issues related to page scrolling (Roman Reiss)
- [[`b4fb95eade`](https://github.com/nodejs/node/commit/b4fb95eade)] - **doc**: add `added:` information for assert (Rich Trott) [#6688](https://github.com/nodejs/node/pull/6688)
- [[`64fcba2a2e`](https://github.com/nodejs/node/commit/64fcba2a2e)] - **doc**: appendFileSync accepts fd as well (Faiz Halde) [#6707](https://github.com/nodejs/node/pull/6707)
- [[`520369d8e0`](https://github.com/nodejs/node/commit/520369d8e0)] - **doc**: fix exec example in child_process (Evan Lucas) [#6660](https://github.com/nodejs/node/pull/6660)
- [[`51d1960955`](https://github.com/nodejs/node/commit/51d1960955)] - **doc**: undocument fs.open's 'rs' mode (Saúl Ibarra Corretgé) [#6732](https://github.com/nodejs/node/pull/6732)
- [[`f1c773d18b`](https://github.com/nodejs/node/commit/f1c773d18b)] - **doc**: add `added:` information for v8 (Rich Trott) [#6684](https://github.com/nodejs/node/pull/6684)
- [[`29b28a233c`](https://github.com/nodejs/node/commit/29b28a233c)] - **doc**: server.listen truncates socket path on unix (Jean Regisser) [#6659](https://github.com/nodejs/node/pull/6659)
- [[`c1d5f2e96e`](https://github.com/nodejs/node/commit/c1d5f2e96e)] - **doc**: update releases.md with new changelog structure (James M Snell) [#6503](https://github.com/nodejs/node/pull/6503)
- [[`d962fbafb2`](https://github.com/nodejs/node/commit/d962fbafb2)] - **doc**: "a" -> "an" in api/documentation.md (Anchika Agarwal) [#6689](https://github.com/nodejs/node/pull/6689)
- [[`26e22e200a`](https://github.com/nodejs/node/commit/26e22e200a)] - **doc**: move the readme newcomers section (Jeremiah Senkpiel) [#6681](https://github.com/nodejs/node/pull/6681)
- [[`8f526494b5`](https://github.com/nodejs/node/commit/8f526494b5)] - **doc**: fix deprecation warnings in addon examples (Ben Noordhuis) [#6652](https://github.com/nodejs/node/pull/6652)
- [[`d34343f0de`](https://github.com/nodejs/node/commit/d34343f0de)] - **doc**: mention existence/purpose of module wrapper (Matt Harrison) [#6433](https://github.com/nodejs/node/pull/6433)
- [[`5c154a87e0`](https://github.com/nodejs/node/commit/5c154a87e0)] - **doc**: add steps for running addons + npm tests (Myles Borins) [#6231](https://github.com/nodejs/node/pull/6231)
- [[`6ea43d12f4`](https://github.com/nodejs/node/commit/6ea43d12f4)] - **doc**: improve onboarding-extras.md formatting (Jeremiah Senkpiel) [#6548](https://github.com/nodejs/node/pull/6548)
- [[`38f5603e97`](https://github.com/nodejs/node/commit/38f5603e97)] - **doc**: fix linewrap in node.1 (Jeremiah Senkpiel) [#6532](https://github.com/nodejs/node/pull/6532)
- [[`5b47accfa6`](https://github.com/nodejs/node/commit/5b47accfa6)] - **doc**: v8 options can use either `_` or `-` (Jeremiah Senkpiel) [#6532](https://github.com/nodejs/node/pull/6532)
- [[`fa94a91bbd`](https://github.com/nodejs/node/commit/fa94a91bbd)] - **doc**: v8 functions as methods on v8 (Bryan English) [#6615](https://github.com/nodejs/node/pull/6615)
- [[`d49b49e8b1`](https://github.com/nodejs/node/commit/d49b49e8b1)] - **doc**: discourage use of util.inherits (Robert Jefe Lindstaedt) [#6514](https://github.com/nodejs/node/pull/6514)
- [[`925fc36dd9`](https://github.com/nodejs/node/commit/925fc36dd9)] - **doc**: add `added:` information for os (Bryan English) [#6609](https://github.com/nodejs/node/pull/6609)
- [[`58cd11c844`](https://github.com/nodejs/node/commit/58cd11c844)] - **doc**: add `added:` information for process (Bryan English) [#6589](https://github.com/nodejs/node/pull/6589)
- [[`d5f7f24aec`](https://github.com/nodejs/node/commit/d5f7f24aec)] - **doc**: add `added:` information for url (Bryan English) [#6593](https://github.com/nodejs/node/pull/6593)
- [[`715b48c1d3`](https://github.com/nodejs/node/commit/715b48c1d3)] - **doc**: add `added:` information for querystring (Bryan English) [#6593](https://github.com/nodejs/node/pull/6593)
- [[`04697a5023`](https://github.com/nodejs/node/commit/04697a5023)] - **doc**: correct anchors for buffer.md (Jackson Tian) [#6542](https://github.com/nodejs/node/pull/6542)
- [[`2d677411a4`](https://github.com/nodejs/node/commit/2d677411a4)] - **doc**: add `added:` information for buffer (Anna Henningsen) [#6495](https://github.com/nodejs/node/pull/6495)
- [[`49af20c2e6`](https://github.com/nodejs/node/commit/49af20c2e6)] - **doc**: update fs callback example error stack (DavidCai) [#6617](https://github.com/nodejs/node/pull/6617)
- [[`9196d87704`](https://github.com/nodejs/node/commit/9196d87704)] - **doc**: add `added:` info for `string_decoder` (Rich Trott) [#6741](https://github.com/nodejs/node/pull/6741)
- [[`688f563ca8`](https://github.com/nodejs/node/commit/688f563ca8)] - **doc**: update vm.runInDebugContext() example (Ben Noordhuis) [#6757](https://github.com/nodejs/node/pull/6757)
- [[`2273971a69`](https://github.com/nodejs/node/commit/2273971a69)] - **doc**: readline.emitKeypressEvents and raw mode (Arve Seljebu) [#6628](https://github.com/nodejs/node/pull/6628)
- [[`1c7b6e276f`](https://github.com/nodejs/node/commit/1c7b6e276f)] - **doc**: improve zlib docs (James M Snell) [#6746](https://github.com/nodejs/node/pull/6746)
- [[`897934a719`](https://github.com/nodejs/node/commit/897934a719)] - **doc**: copyedit maxBuffer note for child_process (Rich Trott) [#6760](https://github.com/nodejs/node/pull/6760)
- [[`c1bf3fc0b1`](https://github.com/nodejs/node/commit/c1bf3fc0b1)] - **doc**: fix links in socket.connecting (Kirill Fomichev) [#6657](https://github.com/nodejs/node/pull/6657)
- [[`ad895f490b`](https://github.com/nodejs/node/commit/ad895f490b)] - **doc**: trim GitHub template comments (Rich Trott) [#6755](https://github.com/nodejs/node/pull/6755)
- [[`af096f1172`](https://github.com/nodejs/node/commit/af096f1172)] - **doc**: add `added` info for `dgram.setBroadcast()` (Rich Trott) [#6750](https://github.com/nodejs/node/pull/6750)
- [[`e8c0dba4bd`](https://github.com/nodejs/node/commit/e8c0dba4bd)] - **doc,events**: fix a link typo (yorkie) [#6640](https://github.com/nodejs/node/pull/6640)
- [[`f31a5ec34a`](https://github.com/nodejs/node/commit/f31a5ec34a)] - **handle_wrap**: IsRefed() -> HasRef() (Jeremiah Senkpiel) [#6546](https://github.com/nodejs/node/pull/6546)
- [[`cc2af793d2`](https://github.com/nodejs/node/commit/cc2af793d2)] - **_Revert_** "**handle_wrap**: IsRefed -> Unrefed, no isAlive check" (Jeremiah Senkpiel) [#6546](https://github.com/nodejs/node/pull/6546)
- [[`2000072903`](https://github.com/nodejs/node/commit/2000072903)] - **handle_wrap**: IsRefed -> Unrefed, no isAlive check (Jeremiah Senkpiel) [#6204](https://github.com/nodejs/node/pull/6204)
- [[`d3132048cb`](https://github.com/nodejs/node/commit/d3132048cb)] - **(SEMVER-MINOR)** **handle_wrap**: expose an `isRefed()` check to JS (Jeremiah Senkpiel) [#5834](https://github.com/nodejs/node/pull/5834)
- [[`59666502c5`](https://github.com/nodejs/node/commit/59666502c5)] - **intl**: Don't crash if v8BreakIterator not available (Steven R. Loomis) [#4253](https://github.com/nodejs/node/pull/4253)
- [[`74582aa590`](https://github.com/nodejs/node/commit/74582aa590)] - **lib**: replace legacy uses of \_\_defineGetter\_\_ (James M Snell) [#6768](https://github.com/nodejs/node/pull/6768)
- [[`559c2583e0`](https://github.com/nodejs/node/commit/559c2583e0)] - **lib,test**: update in preparation for linter update (Rich Trott) [#6498](https://github.com/nodejs/node/pull/6498)
- [[`226b9668db`](https://github.com/nodejs/node/commit/226b9668db)] - **(SEMVER-MINOR)** **repl**: copying tabs shouldn't trigger completion (Eugene Obrezkov) [#5958](https://github.com/nodejs/node/pull/5958)
- [[`ce2d5be4a1`](https://github.com/nodejs/node/commit/ce2d5be4a1)] - **(SEMVER-MINOR)** **repl**: exports `Recoverable` (Blake Embrey) [#3488](https://github.com/nodejs/node/pull/3488)
- [[`635357958d`](https://github.com/nodejs/node/commit/635357958d)] - **repl**: create history file with mode 0600 (Carl Lei) [#3394](https://github.com/nodejs/node/pull/3394)
- [[`e3920d12ef`](https://github.com/nodejs/node/commit/e3920d12ef)] - **src**: fix check-imports.py linter errors (Sakthipriyan Vairamani) [#6105](https://github.com/nodejs/node/pull/6105)
- [[`bbf3b3ebbb`](https://github.com/nodejs/node/commit/bbf3b3ebbb)] - **src**: simplify handlewrap state tracking logic (Ben Noordhuis) [#6395](https://github.com/nodejs/node/pull/6395)
- [[`965274d384`](https://github.com/nodejs/node/commit/965274d384)] - **src**: use libuv's refcounting directly (Ben Noordhuis) [#6395](https://github.com/nodejs/node/pull/6395)
- [[`316871f268`](https://github.com/nodejs/node/commit/316871f268)] - **src**: fix -Wunused-variable compiler warning (Ben Noordhuis) [#6129](https://github.com/nodejs/node/pull/6129)
- [[`1def098b9f`](https://github.com/nodejs/node/commit/1def098b9f)] - **src**: fix sporadic deadlock in SIGUSR1 handler (Ben Noordhuis) [#5904](https://github.com/nodejs/node/pull/5904)
- [[`477e61db9f`](https://github.com/nodejs/node/commit/477e61db9f)] - **src**: don't use locale-sensitive strcasecmp() (Ben Noordhuis) [#6582](https://github.com/nodejs/node/pull/6582)
- [[`1e99643fc9`](https://github.com/nodejs/node/commit/1e99643fc9)] - **src**: remove unused #include statement (Ben Noordhuis) [#6582](https://github.com/nodejs/node/pull/6582)
- [[`62593bd44c`](https://github.com/nodejs/node/commit/62593bd44c)] - **src**: remove pre-openssl 1.0 legacy code (Ben Noordhuis) [#6582](https://github.com/nodejs/node/pull/6582)
- [[`27c17ce9d1`](https://github.com/nodejs/node/commit/27c17ce9d1)] - **src**: fix FindFirstCharacter argument alignment (Anna Henningsen) [#6511](https://github.com/nodejs/node/pull/6511)
- [[`37736f4dad`](https://github.com/nodejs/node/commit/37736f4dad)] - **(SEMVER-MINOR)** **src**: add O_NOATIME constant (Rich Trott) [#6492](https://github.com/nodejs/node/pull/6492)
- [[`bd4454fa0f`](https://github.com/nodejs/node/commit/bd4454fa0f)] - **src,lib**: minor --debug-brk cleanup (Ali Ijaz Sheikh) [#6599](https://github.com/nodejs/node/pull/6599)
- [[`95b7560d8e`](https://github.com/nodejs/node/commit/95b7560d8e)] - **(SEMVER-MINOR)** **src,module**: add --preserve-symlinks command line flag (James M Snell) [#6537](https://github.com/nodejs/node/pull/6537)
- [[`8a7e68ff83`](https://github.com/nodejs/node/commit/8a7e68ff83)] - **test**: check that 2nd handle.close() call is a nop (Ben Noordhuis) [#6395](https://github.com/nodejs/node/pull/6395)
- [[`ccbc78cfc6`](https://github.com/nodejs/node/commit/ccbc78cfc6)] - **test**: remove common.getServiceName() (Rich Trott) [#6709](https://github.com/nodejs/node/pull/6709)
- [[`8c634d78f8`](https://github.com/nodejs/node/commit/8c634d78f8)] - **test**: favor strictEqual() in addon test (Rich Trott) [#6704](https://github.com/nodejs/node/pull/6704)
- [[`1389a4fc5e`](https://github.com/nodejs/node/commit/1389a4fc5e)] - **test**: fix flaky test-preload (Rich Trott) [#6728](https://github.com/nodejs/node/pull/6728)
- [[`adb2d610e6`](https://github.com/nodejs/node/commit/adb2d610e6)] - **test**: include component in tap output (Ben Noordhuis) [#6653](https://github.com/nodejs/node/pull/6653)
- [[`abb063a6a3`](https://github.com/nodejs/node/commit/abb063a6a3)] - **test**: fix test-debugger-repl-break-in-module (Rich Trott) [#6686](https://github.com/nodejs/node/pull/6686)
- [[`5701599767`](https://github.com/nodejs/node/commit/5701599767)] - **test**: fix test-debugger-repl-term (Rich Trott) [#6682](https://github.com/nodejs/node/pull/6682)
- [[`71c91747ef`](https://github.com/nodejs/node/commit/71c91747ef)] - **test**: add tests for stream3 buffering using cork (Alex J Burke) [#6493](https://github.com/nodejs/node/pull/6493)
- [[`b1f58edd54`](https://github.com/nodejs/node/commit/b1f58edd54)] - **test**: abstract skip functionality to common (Jeremiah Senkpiel) [#6697](https://github.com/nodejs/node/pull/6697)
- [[`7d3f5751b3`](https://github.com/nodejs/node/commit/7d3f5751b3)] - **test**: make sure O_NOATIME is present only in Linux (Sakthipriyan Vairamani) [#6614](https://github.com/nodejs/node/pull/6614)
- [[`75adc6a026`](https://github.com/nodejs/node/commit/75adc6a026)] - **test**: move test-debugger-debug-brk to sequential (Rich Trott) [#6731](https://github.com/nodejs/node/pull/6731)
- [[`0bc1784529`](https://github.com/nodejs/node/commit/0bc1784529)] - **test**: refactor doctool tests (Rich Trott) [#6719](https://github.com/nodejs/node/pull/6719)
- [[`89b25fac4c`](https://github.com/nodejs/node/commit/89b25fac4c)] - **test**: fix test-process-exec-argv flakiness (Santiago Gimeno) [#6575](https://github.com/nodejs/node/pull/6575)
- [[`3f9d72408f`](https://github.com/nodejs/node/commit/3f9d72408f)] - **test**: pass python path to node-gyp (hefangshi) [#6646](https://github.com/nodejs/node/pull/6646)
- [[`0c62bd13fb`](https://github.com/nodejs/node/commit/0c62bd13fb)] - **test**: ensure test-npm-install uses correct node (Myles Borins) [#6658](https://github.com/nodejs/node/pull/6658)
- [[`1b71231dd2`](https://github.com/nodejs/node/commit/1b71231dd2)] - **test**: fix test-vm-cached-data to work with old v8 (Myles Borins) [#6317](https://github.com/nodejs/node/pull/6317)
- [[`0eb25cb4b5`](https://github.com/nodejs/node/commit/0eb25cb4b5)] - **test**: test preloaded modules using stdin or repl (Bradley Meck) [#2253](https://github.com/nodejs/node/pull/2253)
- [[`577e132f00`](https://github.com/nodejs/node/commit/577e132f00)] - **test**: fix test-debugger-pid (Santiago Gimeno) [#6584](https://github.com/nodejs/node/pull/6584)
- [[`d74062454b`](https://github.com/nodejs/node/commit/d74062454b)] - **test**: make stdout buffer test more robust (Rich Trott) [#6633](https://github.com/nodejs/node/pull/6633)
- [[`f264749c5c`](https://github.com/nodejs/node/commit/f264749c5c)] - **test**: build addons with V8_DEPRECATION_WARNINGS=1 (Ben Noordhuis) [#6652](https://github.com/nodejs/node/pull/6652)
- [[`01f010f669`](https://github.com/nodejs/node/commit/01f010f669)] - **test**: allow out-of-order replies in dgram tests (Anna Henningsen) [#6607](https://github.com/nodejs/node/pull/6607)
- [[`be241c3262`](https://github.com/nodejs/node/commit/be241c3262)] - **test**: run known_issues tests in CI (Rich Trott) [#6559](https://github.com/nodejs/node/pull/6559)
- [[`8141c2fce2`](https://github.com/nodejs/node/commit/8141c2fce2)] - **test**: add tests for console.\[info|error|warn\] (Bryan English) [#6538](https://github.com/nodejs/node/pull/6538)
- [[`83dab801e2`](https://github.com/nodejs/node/commit/83dab801e2)] - **test**: fix unreliable known_issues test (Rich Trott) [#6555](https://github.com/nodejs/node/pull/6555)
- [[`8c434e6d84`](https://github.com/nodejs/node/commit/8c434e6d84)] - **test**: unmark test-http-regr-gh-2928 as flaky (Rich Trott) [#6540](https://github.com/nodejs/node/pull/6540)
- [[`916e694b2b`](https://github.com/nodejs/node/commit/916e694b2b)] - **test**: avoid test-cluster-master-\* flakiness (Stefan Budeanu) [#6531](https://github.com/nodejs/node/pull/6531)
- [[`be5386e0cf`](https://github.com/nodejs/node/commit/be5386e0cf)] - **test,dgram**: add tests for setBroadcast() (Rich Trott) [#6750](https://github.com/nodejs/node/pull/6750)
- [[`1370fdcad5`](https://github.com/nodejs/node/commit/1370fdcad5)] - **test,tools**: test yaml parsing of doctool (Anna Henningsen) [#6495](https://github.com/nodejs/node/pull/6495)
- [[`347abf341d`](https://github.com/nodejs/node/commit/347abf341d)] - **tools**: enforce linting for unix-style line endings (Rich Trott) [#6685](https://github.com/nodejs/node/pull/6685)
- [[`a63c556c11`](https://github.com/nodejs/node/commit/a63c556c11)] - **tools**: remove the minifying logic (Sakthipriyan Vairamani) [#6636](https://github.com/nodejs/node/pull/6636)
- [[`efcbafa3f7`](https://github.com/nodejs/node/commit/efcbafa3f7)] - **tools**: fix regression in doctool (Myles Borins) [#6680](https://github.com/nodejs/node/pull/6680)
- [[`edb29b8096`](https://github.com/nodejs/node/commit/edb29b8096)] - **tools**: lint for object literal spacing (Rich Trott) [#6592](https://github.com/nodejs/node/pull/6592)
- [[`6806ebb608`](https://github.com/nodejs/node/commit/6806ebb608)] - **tools**: lint for use of space in template strings (Rich Trott) [#6591](https://github.com/nodejs/node/pull/6591)
- [[`341eaf202e`](https://github.com/nodejs/node/commit/341eaf202e)] - **tools**: update marked dependency (Daniel Wang) [#6396](https://github.com/nodejs/node/pull/6396)
- [[`94f82553aa`](https://github.com/nodejs/node/commit/94f82553aa)] - **tools**: allow multiple added: version entries (Anna Henningsen) [#6495](https://github.com/nodejs/node/pull/6495)
- [[`1de25f208c`](https://github.com/nodejs/node/commit/1de25f208c)] - **tools**: do not build addons during compilation (Myles Borins) [#6723](https://github.com/nodejs/node/pull/6723)
- [[`26805c97d0`](https://github.com/nodejs/node/commit/26805c97d0)] - **tools**: fix tools/doc/addon-verify.js regression (Anna Henningsen) [#6652](https://github.com/nodejs/node/pull/6652)
- [[`cea17775e0`](https://github.com/nodejs/node/commit/cea17775e0)] - **tools**: parse documentation metadata (Tristian Flanagan) [#6495](https://github.com/nodejs/node/pull/6495)
- [[`a568ad4205`](https://github.com/nodejs/node/commit/a568ad4205)] - **tools**: add mock-y js-yaml dependency to doctool (Anna Henningsen) [#6495](https://github.com/nodejs/node/pull/6495)
- [[`dce6413576`](https://github.com/nodejs/node/commit/dce6413576)] - **tools**: Check in tools for shrinking ICU size, change default to small-icu (Steven R. Loomis) [#6088](https://github.com/nodejs/node/pull/6088)
- [[`d629f265fe`](https://github.com/nodejs/node/commit/d629f265fe)] - **tools**: update ESLint to 2.9.0 (Rich Trott) [#6498](https://github.com/nodejs/node/pull/6498)
- [[`6aa92d5a98`](https://github.com/nodejs/node/commit/6aa92d5a98)] - **tools**: disallow multiple spaces except indentation (Rich Trott) [#6645](https://github.com/nodejs/node/pull/6645)
- [[`ceeae4b180`](https://github.com/nodejs/node/commit/ceeae4b180)] - **tools,test**: make argument linting more stringent (Rich Trott) [#6720](https://github.com/nodejs/node/pull/6720)
- [[`6d1527bb37`](https://github.com/nodejs/node/commit/6d1527bb37)] - **util**: fix invalid date output with util.inspect (Rumkin) [#6504](https://github.com/nodejs/node/pull/6504)
- [[`1d6c17efd7`](https://github.com/nodejs/node/commit/1d6c17efd7)] - **util**: adhere to `noDeprecation` set at runtime (Anna Henningsen) [#6683](https://github.com/nodejs/node/pull/6683)

Windows 32-bit Installer: https://nodejs.org/dist/v6.2.0/node-v6.2.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v6.2.0/node-v6.2.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v6.2.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v6.2.0/win-x64/node.exe \
Mac OS X 64-bit Installer: https://nodejs.org/dist/v6.2.0/node-v6.2.0.pkg \
Mac OS X 64-bit Binary: https://nodejs.org/dist/v6.2.0/node-v6.2.0-darwin-x64.tar.gz \
Linux 32-bit Binary: https://nodejs.org/dist/v6.2.0/node-v6.2.0-linux-x86.tar.xz \
Linux 64-bit Binary: https://nodejs.org/dist/v6.2.0/node-v6.2.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v6.2.0/node-v6.2.0-linux-ppc64le.tar.xz \
SmartOS 32-bit Binary: https://nodejs.org/dist/v6.2.0/node-v6.2.0-sunos-x86.tar.xz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v6.2.0/node-v6.2.0-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v6.2.0/node-v6.2.0-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v6.2.0/node-v6.2.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v6.2.0/node-v6.2.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v6.2.0/node-v6.2.0.tar.gz \
Other release files: https://nodejs.org/dist/v6.2.0/ \
Documentation: https://nodejs.org/docs/v6.2.0/api/

Shasums (GPG signing hash: SHA512, file hash: SHA256):

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA512

1c8c16bb3df22c7c98cf0c118bd183838b848e36ef1448992cf511c044339823  node-v6.2.0-darwin-x64.tar.gz
e1c890b47f078a840896b05a3111080e6f410103cc6d338de2a32ebc5a58d545  node-v6.2.0-darwin-x64.tar.xz
1127a30aa0e728786e4c1b2a568dd74eab8957a271764cec06d3562febee6baa  node-v6.2.0-headers.tar.gz
7ac466b74c01f1d0e0cf46d62dba32392b6fa4157a8b957fbdec490701d06d35  node-v6.2.0-headers.tar.xz
bac296f56d071e147e33e7f027efc8b0422a786e3943b18c64c8c8fbf2abae96  node-v6.2.0-linux-arm64.tar.gz
d81cd7d9a04a68d5a6bd9b36c410e0ac3394d1cec03c647737a41d5772d4f542  node-v6.2.0-linux-arm64.tar.xz
c297855fd11dc9d58c523f3665b18e5ff932145aabb27c389da3b6b27553fe37  node-v6.2.0-linux-armv6l.tar.gz
3c0fda6205425a641cac8b71f522bdef28de0d17fa53d3b0987f66a58c416ee1  node-v6.2.0-linux-armv6l.tar.xz
4804a486b0b0db0f52fc273ccf688f83c06e3fc17641824877e2921518d0e50d  node-v6.2.0-linux-armv7l.tar.gz
7316c8026c5da22cbda06c6b8f556a326c7c2c81408b9a1f04a01859c6d95787  node-v6.2.0-linux-armv7l.tar.xz
fbbeaaa5431c6cc5007567c35f13b87c2917d704ae6fd8f6db7792f1ba5349b0  node-v6.2.0-linux-ppc64le.tar.gz
75d3ed1dda3e29e12a708a4c5084f7a4aaa5485f10a68b4e21c8fe7333720cc1  node-v6.2.0-linux-ppc64le.tar.xz
69f8f3ffec15f1cddca3f8fca53471123e22db40ae58bea9bd65503d900267d8  node-v6.2.0-linux-ppc64.tar.gz
7697c259171529ba7ea902437ef8186bde64fb9613ee77ade882549b3cc43ffd  node-v6.2.0-linux-ppc64.tar.xz
661dba369c277603fa6d0182c4ea7ff074ba6bacd19171826271f872afd6aaa7  node-v6.2.0-linux-x64.tar.gz
ab28c6af235045def1f65fca7f4848de3c2de4fb62ebce37052f1a10f0b40263  node-v6.2.0-linux-x64.tar.xz
47c3d613362afd9c64e6468fba0d3f6407ead7d3ea6447deb297c6551dd9d0d4  node-v6.2.0-linux-x86.tar.gz
6aaf649e9798fdc40c03aa27f63715cfe4c33916f8081607067bdfc16a2a2d9a  node-v6.2.0-linux-x86.tar.xz
4ce1462c3cf3c327891154ab076f2f5ef16e61b150001d90d8dcaf2ae4e49772  node-v6.2.0.pkg
f1e8447ff644598c2c24eb6effa12a9d32c6fdad7fa832065dd5f381339b723f  node-v6.2.0-sunos-x64.tar.gz
8dacb9a2fbc71c78a157c1c6cf1ccb126b7f919775834e7570821b19514603ae  node-v6.2.0-sunos-x64.tar.xz
bbfce19c47265676fb44a6a1fa10e2e9ec710c8a332a801aa7e3e58648e851a3  node-v6.2.0-sunos-x86.tar.gz
effdeef23786210846c4d19249bac2ddbabc9f8647deb6803345725acbadec73  node-v6.2.0-sunos-x86.tar.xz
fcfd81847d78abb690aafaf46223d3b59aedc54fb200a2ab2bfd9a39ad6e94b9  node-v6.2.0.tar.gz
8633fe606fd1f2235d26901c6bc4c11b5b88fd3c772af18a902e3efd1254e492  node-v6.2.0.tar.xz
bd1d766e15f75f67befa76738bc0212ee3016444eb0396b29c6fc319658e21a2  node-v6.2.0-x64.msi
e0e20da53fd7e8ccdd61acf93b4f268189b32c43c15516a272df57808d94b941  node-v6.2.0-x86.msi
6e8aff84a875d60785d23a3c8fbf6cff82835dde0886e5ed476150b82f1f61fd  win-x64/node.exe
df99926fcfb98fdf764ab3a4e46fd31ee23863b26bb9b24fcd3b6398aff21d13  win-x64/node.lib
8003b76b7e45cfd6d44308e17e7d055ed3dd9322a5c0a3bb8f66670a5e8ecb5f  win-x86/node.exe
9fd0bd7229d637d5e9d2c02bb0325a99fd38bbed6c2f7a37942d6a5951fac344  win-x86/node.lib
-----BEGIN PGP SIGNATURE-----
Comment: GPGTools - https://gpgtools.org

iQIcBAEBCgAGBQJXO4jZAAoJELY7U1pMIGypvGgP/RWxnq3GhqgMKXEMnkljpSen
KDNHGGaZJrA6LU6L3my1v0GsP+Pfj+okbzHcZ5jwg9p2tTjXRFBnC+UFz+00G86d
MFzX1Y9MZk4+9OkJWjO0c5carxtYD68nryohJsIMMh2ipos47SDqYwO7oLVDEZAd
orc5K1Vtpvkx66VEAVAgTo4eEmyDM3NLHve5oaOHnJ5kdasqqDj+LFXYUT5rKDoQ
yUMbIZbvj8zrqCd76XTJpw/e9RmQ3v9G2aBXQ+jhzxtADu0vevoyTldld/CiMTkX
4mDhGMP3AToiF/o5F91GmuzVQJnnOowS02/4k5UpUIyRAvD25podlIJHHs+M1lDs
NQC7W691mD6WRf6OZROkrWOvrYpoIYYn79GBxyTzLZz2bYCmGxjttB/ETnLpG7mf
CjMDjOq6B8LTC24JtvqYDt6HIlkOkzZ5vOxAqCqCQC3l6hQUEqVAp7Dack/FOHNv
pbUqSQwfSu9j+gcoMkUmNDdjSeWEyCHN09XH3dFOQ1Cd6olRh900nkhITmTKEsD7
U7QZDMp7s6QmtbzF62TskH8jj/QIhQx6qFuyyquHpWAwggrcWbauhMKQFAHc9vI5
iz2XUKB/yKlcFVomojq+sm4NoBvgy4IZAIHUjv+g4yYzPHmyZrP0I4bMUT9s/1b6
LunWBUGBWXCXRm4Q6Qar
=Q/h1
-----END PGP SIGNATURE-----

```
