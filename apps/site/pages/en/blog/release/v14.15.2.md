---
date: '2020-12-15T23:57:34.908Z'
category: release
title: Node v14.15.2 (LTS)
layout: blog-post
author: <PERSON>
---

### Notable Changes

- **deps**:
  - upgrade npm to 6.14.9 (<PERSON><PERSON>) [#36450](https://github.com/nodejs/node/pull/36450)
  - update acorn to v8.0.4 (<PERSON><PERSON><PERSON>) [#35791](https://github.com/nodejs/node/pull/35791)
- **doc**: add release key for <PERSON> (<PERSON>) [#35545](https://github.com/nodejs/node/pull/35545)
- **http2**: check write not scheduled in scope destructor (<PERSON>) [#36241](https://github.com/nodejs/node/pull/36241)
- **stream**: fix regression on duplex end (<PERSON><PERSON><PERSON>) [#35941](https://github.com/nodejs/node/pull/35941)

### Commits

- [[`c508bfc66b`](https://github.com/nodejs/node/commit/c508bfc66b)] - **assert**: refactor to use more primordials (<PERSON> du <PERSON>el) [#35998](https://github.com/nodejs/node/pull/35998)
- [[`a9d3a0df29`](https://github.com/nodejs/node/commit/a9d3a0df29)] - **assert,repl**: enable ecmaVersion 2021 in acorn parser (Michaël Zasso) [#35827](https://github.com/nodejs/node/pull/35827)
- [[`6d43c8dd69`](https://github.com/nodejs/node/commit/6d43c8dd69)] - **async_hooks**: refactor to use more primordials (Antoine du Hamel) [#36168](https://github.com/nodejs/node/pull/36168)
- [[`029ea16a24`](https://github.com/nodejs/node/commit/029ea16a24)] - **async_hooks**: fix leak in AsyncLocalStorage exit (Stephen Belanger) [#35779](https://github.com/nodejs/node/pull/35779)
- [[`d49e0ca73a`](https://github.com/nodejs/node/commit/d49e0ca73a)] - **benchmark**: fix build warnings (Gabriel Schulhof) [#36157](https://github.com/nodejs/node/pull/36157)
- [[`d027be0551`](https://github.com/nodejs/node/commit/d027be0551)] - **benchmark**: ignore build artifacts for napi addons (Richard Lau) [#35970](https://github.com/nodejs/node/pull/35970)
- [[`fdb1c0d31c`](https://github.com/nodejs/node/commit/fdb1c0d31c)] - **benchmark**: remove modules that require intl (Richard Lau) [#35968](https://github.com/nodejs/node/pull/35968)
- [[`f6487960b5`](https://github.com/nodejs/node/commit/f6487960b5)] - **benchmark**: make the benchmark tool work with Node 10 (Joyee Cheung) [#35817](https://github.com/nodejs/node/pull/35817)
- [[`21d3ccf5df`](https://github.com/nodejs/node/commit/21d3ccf5df)] - **benchmark**: add startup benchmark for loading public modules (Joyee Cheung) [#35816](https://github.com/nodejs/node/pull/35816)
- [[`0477e000bf`](https://github.com/nodejs/node/commit/0477e000bf)] - **bootstrap**: refactor to use more primordials (Antoine du Hamel) [#35999](https://github.com/nodejs/node/pull/35999)
- [[`699bb348d9`](https://github.com/nodejs/node/commit/699bb348d9)] - **build**: replace which with command -v (raisinten) [#36118](https://github.com/nodejs/node/pull/36118)
- [[`304e269001`](https://github.com/nodejs/node/commit/304e269001)] - **build**: try “python3” as a last resort for 3.x (Ole André Vadla Ravnås) [#35983](https://github.com/nodejs/node/pull/35983)
- [[`6bafe04911`](https://github.com/nodejs/node/commit/6bafe04911)] - **build**: conditionally clear vcinstalldir (Brian Ingenito) [#36009](https://github.com/nodejs/node/pull/36009)
- [[`f498127c41`](https://github.com/nodejs/node/commit/f498127c41)] - **build**: fix zlib inlining for IA-32 (raisinten) [#35679](https://github.com/nodejs/node/pull/35679)
- [[`f33fa264cc`](https://github.com/nodejs/node/commit/f33fa264cc)] - **build**: fix lint-js-fix target (Antoine du Hamel) [#35927](https://github.com/nodejs/node/pull/35927)
- [[`67d31827ac`](https://github.com/nodejs/node/commit/67d31827ac)] - **build**: add vcbuilt test-doc target (Antoine du Hamel) [#35708](https://github.com/nodejs/node/pull/35708)
- [[`2a8c2ddcb1`](https://github.com/nodejs/node/commit/2a8c2ddcb1)] - **build**: add license-builder GitHub Action (Tierney Cyren) [#35712](https://github.com/nodejs/node/pull/35712)
- [[`6c61b9372b`](https://github.com/nodejs/node/commit/6c61b9372b)] - **build**: use make functions instead of echo (Antoine du Hamel) [#35707](https://github.com/nodejs/node/pull/35707)
- [[`4813d913e3`](https://github.com/nodejs/node/commit/4813d913e3)] - **build**: use GITHUB_ENV file to set env variables (Michaël Zasso) [#35638](https://github.com/nodejs/node/pull/35638)
- [[`71e0f33751`](https://github.com/nodejs/node/commit/71e0f33751)] - **build**: do not install jq in workflows (Michaël Zasso) [#35638](https://github.com/nodejs/node/pull/35638)
- [[`8ab7f258d4`](https://github.com/nodejs/node/commit/8ab7f258d4)] - **build, tools**: look for local installation of NASM (Richard Lau) [#36014](https://github.com/nodejs/node/pull/36014)
- [[`50552facb7`](https://github.com/nodejs/node/commit/50552facb7)] - **build,tools**: gitHub Actions: use Node.js Fermium (Antoine du Hamel) [#35840](https://github.com/nodejs/node/pull/35840)
- [[`77b7c985f6`](https://github.com/nodejs/node/commit/77b7c985f6)] - **build,tools**: add lint-js-doc target (Antoine du Hamel) [#35708](https://github.com/nodejs/node/pull/35708)
- [[`929e1272ee`](https://github.com/nodejs/node/commit/929e1272ee)] - **cluster**: refactor to use more primordials (Antoine du Hamel) [#36011](https://github.com/nodejs/node/pull/36011)
- [[`568e6177c9`](https://github.com/nodejs/node/commit/568e6177c9)] - **console**: use more primordials (Antoine du Hamel) [#35734](https://github.com/nodejs/node/pull/35734)
- [[`6cea3152fe`](https://github.com/nodejs/node/commit/6cea3152fe)] - **deps**: upgrade npm to 6.14.9 (Myles Borins) [#36450](https://github.com/nodejs/node/pull/36450)
- [[`d2ee676eb9`](https://github.com/nodejs/node/commit/d2ee676eb9)] - **deps**: cherry-pick 9a49b22 from V8 upstream (Daniel Bevenius) [#35939](https://github.com/nodejs/node/pull/35939)
- [[`7367e6c6be`](https://github.com/nodejs/node/commit/7367e6c6be)] - **deps**: update acorn to v8.0.4 (Michaël Zasso) [#35791](https://github.com/nodejs/node/pull/35791)
- [[`4937a34be6`](https://github.com/nodejs/node/commit/4937a34be6)] - **deps**: fix typo in zlib.gyp that break arm-fpu-neon build (lucasg) [#35659](https://github.com/nodejs/node/pull/35659)
- [[`1e8dfb9d2c`](https://github.com/nodejs/node/commit/1e8dfb9d2c)] - **deps**: upgrade to cjs-module-lexer@1.0.0 (Guy Bedford) [#35928](https://github.com/nodejs/node/pull/35928)
- [[`0356963f0e`](https://github.com/nodejs/node/commit/0356963f0e)] - **deps**: update to cjs-module-lexer@0.5.2 (Guy Bedford) [#35901](https://github.com/nodejs/node/pull/35901)
- [[`172be4ffe0`](https://github.com/nodejs/node/commit/172be4ffe0)] - **deps**: upgrade to cjs-module-lexer@0.5.0 (Guy Bedford) [#35871](https://github.com/nodejs/node/pull/35871)
- [[`1f7740691d`](https://github.com/nodejs/node/commit/1f7740691d)] - **deps**: update to cjs-module-lexer@0.4.3 (Guy Bedford) [#35745](https://github.com/nodejs/node/pull/35745)
- [[`47bd445e56`](https://github.com/nodejs/node/commit/47bd445e56)] - **doc**: remove stray comma in url.md (Rich Trott) [#36175](https://github.com/nodejs/node/pull/36175)
- [[`2f76a75fc6`](https://github.com/nodejs/node/commit/2f76a75fc6)] - **doc**: revise agent.destroy() text (Rich Trott) [#36163](https://github.com/nodejs/node/pull/36163)
- [[`72fb6f88ab`](https://github.com/nodejs/node/commit/72fb6f88ab)] - **doc**: add compatibility/interop technical value (Geoffrey Booth) [#35323](https://github.com/nodejs/node/pull/35323)
- [[`f5efd54727`](https://github.com/nodejs/node/commit/f5efd54727)] - **doc**: de-emphasize wrapping in napi_define_class (Gabriel Schulhof) [#36159](https://github.com/nodejs/node/pull/36159)
- [[`8a7c2b951d`](https://github.com/nodejs/node/commit/8a7c2b951d)] - **doc**: clarify text about process not responding (Rich Trott) [#36117](https://github.com/nodejs/node/pull/36117)
- [[`800e1db83d`](https://github.com/nodejs/node/commit/800e1db83d)] - **doc**: esm docs consolidation and reordering (Guy Bedford) [#36046](https://github.com/nodejs/node/pull/36046)
- [[`4fad888fe1`](https://github.com/nodejs/node/commit/4fad888fe1)] - **doc**: move shigeki to emeritus (Rich Trott) [#36093](https://github.com/nodejs/node/pull/36093)
- [[`c088434b4d`](https://github.com/nodejs/node/commit/c088434b4d)] - **doc**: document the error when cwd not exists in child_process.spawn (FeelyChau) [#34505](https://github.com/nodejs/node/pull/34505)
- [[`4dbbbaa2e9`](https://github.com/nodejs/node/commit/4dbbbaa2e9)] - **doc**: fix typo in debugger.md (Rich Trott) [#36066](https://github.com/nodejs/node/pull/36066)
- [[`d796bc7348`](https://github.com/nodejs/node/commit/d796bc7348)] - **doc**: update list styles for remark-parse@9 rendering (Rich Trott) [#36049](https://github.com/nodejs/node/pull/36049)
- [[`6daf204f32`](https://github.com/nodejs/node/commit/6daf204f32)] - **doc**: escape asterisk in cctest gtest-filter (raisinten) [#36034](https://github.com/nodejs/node/pull/36034)
- [[`9470bf5872`](https://github.com/nodejs/node/commit/9470bf5872)] - **doc**: move v8.getHeapCodeStatistics() (Rich Trott) [#36027](https://github.com/nodejs/node/pull/36027)
- [[`30cd797c15`](https://github.com/nodejs/node/commit/30cd797c15)] - **doc**: add note regarding file structure in src/README.md (Denys Otrishko) [#35000](https://github.com/nodejs/node/pull/35000)
- [[`cddcfcde9f`](https://github.com/nodejs/node/commit/cddcfcde9f)] - **doc**: advise users to import the full set of trusted release keys (Reşat SABIQ) [#32655](https://github.com/nodejs/node/pull/32655)
- [[`1ca1f262a5`](https://github.com/nodejs/node/commit/1ca1f262a5)] - **doc**: fix crypto doc linter errors (Antoine du Hamel) [#36035](https://github.com/nodejs/node/pull/36035)
- [[`b11725eb9e`](https://github.com/nodejs/node/commit/b11725eb9e)] - **doc**: revise v8.getHeapSnapshot() (Rich Trott) [#35849](https://github.com/nodejs/node/pull/35849)
- [[`990facbc3e`](https://github.com/nodejs/node/commit/990facbc3e)] - **doc**: update core-validate-commit link in guide (Daijiro Wachi) [#35938](https://github.com/nodejs/node/pull/35938)
- [[`773685c2a4`](https://github.com/nodejs/node/commit/773685c2a4)] - **doc**: update benchmark CI test indicator in README (Rich Trott) [#35945](https://github.com/nodejs/node/pull/35945)
- [[`c90571ff2a`](https://github.com/nodejs/node/commit/c90571ff2a)] - **doc**: add new wordings to the API description (Pooja D.P) [#35588](https://github.com/nodejs/node/pull/35588)
- [[`6259c2d231`](https://github.com/nodejs/node/commit/6259c2d231)] - **doc**: option --prof documentation help added (krank2me) [#34991](https://github.com/nodejs/node/pull/34991)
- [[`98e4b77b89`](https://github.com/nodejs/node/commit/98e4b77b89)] - **doc**: fix release-schedule link in backport guide (Daijiro Wachi) [#35920](https://github.com/nodejs/node/pull/35920)
- [[`51ce1a2fa8`](https://github.com/nodejs/node/commit/51ce1a2fa8)] - **doc**: update tables in README files for linting changes (Rich Trott) [#35905](https://github.com/nodejs/node/pull/35905)
- [[`513bed2776`](https://github.com/nodejs/node/commit/513bed2776)] - **doc**: temporarily disable list-item-bullet-indent (Nick Schonning) [#35647](https://github.com/nodejs/node/pull/35647)
- [[`733c9da1e9`](https://github.com/nodejs/node/commit/733c9da1e9)] - **doc**: disable no-undefined-references workarounds (Nick Schonning) [#35647](https://github.com/nodejs/node/pull/35647)
- [[`6e1612fa15`](https://github.com/nodejs/node/commit/6e1612fa15)] - **doc**: adjust table alignment for remark v13 (Nick Schonning) [#35647](https://github.com/nodejs/node/pull/35647)
- [[`a15dede26d`](https://github.com/nodejs/node/commit/a15dede26d)] - **doc**: move bnoordhuis to emeritus (Ben Noordhuis) [#35865](https://github.com/nodejs/node/pull/35865)
- [[`26e42939f2`](https://github.com/nodejs/node/commit/26e42939f2)] - **doc**: add on statement in the APIs docs (Pooja D.P) [#35610](https://github.com/nodejs/node/pull/35610)
- [[`9486f5fc37`](https://github.com/nodejs/node/commit/9486f5fc37)] - **doc**: move ronkorving to emeritus (Rich Trott) [#35828](https://github.com/nodejs/node/pull/35828)
- [[`3f3d2d781b`](https://github.com/nodejs/node/commit/3f3d2d781b)] - **doc**: recommend test-doc instead of lint-md (Antoine du Hamel) [#35708](https://github.com/nodejs/node/pull/35708)
- [[`8131d954d9`](https://github.com/nodejs/node/commit/8131d954d9)] - **doc**: fix reference to googletest test fixture (Tobias Nießen) [#35813](https://github.com/nodejs/node/pull/35813)
- [[`34d6ca3bef`](https://github.com/nodejs/node/commit/34d6ca3bef)] - **doc**: add conditional example for setBreakpoint() (Chris Opperwall) [#35823](https://github.com/nodejs/node/pull/35823)
- [[`29849743b8`](https://github.com/nodejs/node/commit/29849743b8)] - **doc**: make small improvements to REPL doc (Rich Trott) [#35808](https://github.com/nodejs/node/pull/35808)
- [[`02f9a2a77a`](https://github.com/nodejs/node/commit/02f9a2a77a)] - **doc**: update MessagePort documentation for EventTarget inheritance (Anna Henningsen) [#35839](https://github.com/nodejs/node/pull/35839)
- [[`9c7d4bd0f3`](https://github.com/nodejs/node/commit/9c7d4bd0f3)] - **doc**: use case-sensitive in the example (Pooja D.P) [#35624](https://github.com/nodejs/node/pull/35624)
- [[`600cffae3c`](https://github.com/nodejs/node/commit/600cffae3c)] - **doc**: consolidate and clarify breakOnSigInt text (Rich Trott) [#35787](https://github.com/nodejs/node/pull/35787)
- [[`0de3f564b2`](https://github.com/nodejs/node/commit/0de3f564b2)] - **doc**: add a subsystems header in pull-requests.md (Pooja D.P) [#35718](https://github.com/nodejs/node/pull/35718)
- [[`47b4b2be29`](https://github.com/nodejs/node/commit/47b4b2be29)] - **doc**: add require statement in the example (Pooja D.P) [#35554](https://github.com/nodejs/node/pull/35554)
- [[`77cfcba7c8`](https://github.com/nodejs/node/commit/77cfcba7c8)] - **doc**: modified memory set statement set size (Pooja D.P) [#35517](https://github.com/nodejs/node/pull/35517)
- [[`41937f76f0`](https://github.com/nodejs/node/commit/41937f76f0)] - **doc**: use kbd element in readline doc prose (Rich Trott) [#35737](https://github.com/nodejs/node/pull/35737)
- [[`eee62b05f6`](https://github.com/nodejs/node/commit/eee62b05f6)] - **doc**: fix header level in fs.md (ax1) [#35771](https://github.com/nodejs/node/pull/35771)
- [[`63533d7d56`](https://github.com/nodejs/node/commit/63533d7d56)] - **doc**: remove stability warning in v8 module doc (Rich Trott) [#35774](https://github.com/nodejs/node/pull/35774)
- [[`62bf1a63d6`](https://github.com/nodejs/node/commit/62bf1a63d6)] - **doc**: mark optional parameters in timers.md (Vse Mozhe Buty) [#35764](https://github.com/nodejs/node/pull/35764)
- [[`4dc5e4a354`](https://github.com/nodejs/node/commit/4dc5e4a354)] - **doc**: add a example code to API doc property (Pooja D.P) [#35738](https://github.com/nodejs/node/pull/35738)
- [[`8ef0652566`](https://github.com/nodejs/node/commit/8ef0652566)] - **doc**: update console.error example (Lee, Bonggi) [#34964](https://github.com/nodejs/node/pull/34964)
- [[`47ba12265e`](https://github.com/nodejs/node/commit/47ba12265e)] - **doc**: improve text for breakOnSigint (Rich Trott) [#35692](https://github.com/nodejs/node/pull/35692)
- [[`c0d9756163`](https://github.com/nodejs/node/commit/c0d9756163)] - **doc**: this prints replaced with this is printed (Pooja D.P) [#35515](https://github.com/nodejs/node/pull/35515)
- [[`2feb86e635`](https://github.com/nodejs/node/commit/2feb86e635)] - **doc**: update package.json field definitions (Myles Borins) [#35741](https://github.com/nodejs/node/pull/35741)
- [[`d0d67c67c0`](https://github.com/nodejs/node/commit/d0d67c67c0)] - **doc**: add Installing Node.js header in BUILDING.md (Pooja D.P) [#35710](https://github.com/nodejs/node/pull/35710)
- [[`7c089ad04c`](https://github.com/nodejs/node/commit/7c089ad04c)] - **doc**: use kbd element in readline doc (Rich Trott) [#35698](https://github.com/nodejs/node/pull/35698)
- [[`ba623ef35a`](https://github.com/nodejs/node/commit/ba623ef35a)] - **doc**: add release key for Danielle Adams (Danielle Adams) [#35545](https://github.com/nodejs/node/pull/35545)
- [[`df4043bed3`](https://github.com/nodejs/node/commit/df4043bed3)] - **doc**: use kbd element in os doc (Rich Trott) [#35656](https://github.com/nodejs/node/pull/35656)
- [[`4d72e982de`](https://github.com/nodejs/node/commit/4d72e982de)] - **doc**: add a statement in the documentation. (Pooja D.P) [#35585](https://github.com/nodejs/node/pull/35585)
- [[`238885288d`](https://github.com/nodejs/node/commit/238885288d)] - **doc**: clarify experimental API elements in vm.md (Rich Trott) [#35594](https://github.com/nodejs/node/pull/35594)
- [[`806a269a83`](https://github.com/nodejs/node/commit/806a269a83)] - **doc**: importModuleDynamically gets Script, not Module (Simen Bekkhus) [#35593](https://github.com/nodejs/node/pull/35593)
- [[`6c4e697f56`](https://github.com/nodejs/node/commit/6c4e697f56)] - **doc**: fix EventEmitter examples (Sourav Shaw) [#33513](https://github.com/nodejs/node/pull/33513)
- [[`f6ebd81693`](https://github.com/nodejs/node/commit/f6ebd81693)] - **doc**: add example code for process.getgroups() (Pooja D.P) [#35625](https://github.com/nodejs/node/pull/35625)
- [[`2c342662e5`](https://github.com/nodejs/node/commit/2c342662e5)] - **doc**: use kbd element in tty doc (Rich Trott) [#35613](https://github.com/nodejs/node/pull/35613)
- [[`f723335f9e`](https://github.com/nodejs/node/commit/f723335f9e)] - **doc**: remove documentation for stream.\_construct() (Luigi Pinca) [#36119](https://github.com/nodejs/node/pull/36119)
- [[`e71b4baa88`](https://github.com/nodejs/node/commit/e71b4baa88)] - **doc**: Remove reference to io.js (Hussaina Begum Nandyala) [#35618](https://github.com/nodejs/node/pull/35618)
- [[`4faf71b474`](https://github.com/nodejs/node/commit/4faf71b474)] - **doc,crypto**: added sign/verify method changes about dsaEncoding (Filip Skokan) [#35480](https://github.com/nodejs/node/pull/35480)
- [[`e9d485f878`](https://github.com/nodejs/node/commit/e9d485f878)] - **doc,esm**: document experimental warning removal (Antoine du Hamel) [#35750](https://github.com/nodejs/node/pull/35750)
- [[`17c3fc67cf`](https://github.com/nodejs/node/commit/17c3fc67cf)] - **doc,fs**: document value of stats.isDirectory on symbolic links (coderaiser) [#27413](https://github.com/nodejs/node/pull/27413)
- [[`fc17ead531`](https://github.com/nodejs/node/commit/fc17ead531)] - **doc,net**: document socket.timeout (Brandon Kobel) [#34543](https://github.com/nodejs/node/pull/34543)
- [[`dc589b541f`](https://github.com/nodejs/node/commit/dc589b541f)] - **doc,src,test**: revise C++ code for linter update (Rich Trott) [#35719](https://github.com/nodejs/node/pull/35719)
- [[`0a944a42c0`](https://github.com/nodejs/node/commit/0a944a42c0)] - **doc,stream**: write(chunk, encoding, cb) encoding can be null (dev-script) [#35372](https://github.com/nodejs/node/pull/35372)
- [[`be79250aad`](https://github.com/nodejs/node/commit/be79250aad)] - **doc,test**: update v8 method doc and comment (Rich Trott) [#35795](https://github.com/nodejs/node/pull/35795)
- [[`8fdf077efc`](https://github.com/nodejs/node/commit/8fdf077efc)] - **doc,url**: fix url.hostname example (Rishabh Mehan) [#33735](https://github.com/nodejs/node/pull/33735)
- [[`3a08afc402`](https://github.com/nodejs/node/commit/3a08afc402)] - **domain**: refactor to use more primordials (Antoine du Hamel) [#35885](https://github.com/nodejs/node/pull/35885)
- [[`8d672b8e53`](https://github.com/nodejs/node/commit/8d672b8e53)] - **esm**: refactor to use more primordials (Antoine du Hamel) [#36019](https://github.com/nodejs/node/pull/36019)
- [[`570a8bfe12`](https://github.com/nodejs/node/commit/570a8bfe12)] - **events**: port some wpt tests (Benjamin Gruenbaum) [#33621](https://github.com/nodejs/node/pull/33621)
- [[`8ef4557c65`](https://github.com/nodejs/node/commit/8ef4557c65)] - **events**: make eventTarget.removeAllListeners() return this (Luigi Pinca) [#35805](https://github.com/nodejs/node/pull/35805)
- [[`d27e56356b`](https://github.com/nodejs/node/commit/d27e56356b)] - **fs**: remove experimental from promises.rmdir recursive (Anders Kaseorg) [#36131](https://github.com/nodejs/node/pull/36131)
- [[`8d84bdc46b`](https://github.com/nodejs/node/commit/8d84bdc46b)] - **fs**: filehandle read now accepts object as argument (Nikola Glavina) [#34180](https://github.com/nodejs/node/pull/34180)
- [[`7c3b6f17e3`](https://github.com/nodejs/node/commit/7c3b6f17e3)] - **fs**: replace finally with PromisePrototypeFinally (Baruch Odem (Rothkoff)) [#35995](https://github.com/nodejs/node/pull/35995)
- [[`2f692c4cc6`](https://github.com/nodejs/node/commit/2f692c4cc6)] - **fs**: remove unnecessary Function#bind() in fs/promises (Ben Noordhuis) [#35208](https://github.com/nodejs/node/pull/35208)
- [[`5f0c8142b7`](https://github.com/nodejs/node/commit/5f0c8142b7)] - **fs**: remove unused assignment (Rich Trott) [#35642](https://github.com/nodejs/node/pull/35642)
- [[`e2b8734d20`](https://github.com/nodejs/node/commit/e2b8734d20)] - **gyp,build**: consistent shared library location (Rod Vagg) [#35635](https://github.com/nodejs/node/pull/35635)
- [[`45aee0d25e`](https://github.com/nodejs/node/commit/45aee0d25e)] - **http**: fix typo in comment (Hollow Man) [#36193](https://github.com/nodejs/node/pull/36193)
- [[`b58725c4c0`](https://github.com/nodejs/node/commit/b58725c4c0)] - **http**: lazy create IncomingMessage.headers (Robert Nagy) [#35281](https://github.com/nodejs/node/pull/35281)
- [[`71c3efe278`](https://github.com/nodejs/node/commit/71c3efe278)] - **http2**: check write not scheduled in scope destructor (David Halls) [#36241](https://github.com/nodejs/node/pull/36241)
- [[`ab2b066fc1`](https://github.com/nodejs/node/commit/ab2b066fc1)] - **http2**: delay session.receive() by a tick (Szymon Marczak) [#35985](https://github.com/nodejs/node/pull/35985)
- [[`c4e17cfa25`](https://github.com/nodejs/node/commit/c4e17cfa25)] - **http2**: add has method to proxySocketHandler (masx200) [#35197](https://github.com/nodejs/node/pull/35197)
- [[`c455b848d9`](https://github.com/nodejs/node/commit/c455b848d9)] - **http2**: centralise socket event binding in Http2Session (Momtchil Momtchev) [#35772](https://github.com/nodejs/node/pull/35772)
- [[`dce01fd27f`](https://github.com/nodejs/node/commit/dce01fd27f)] - **http2**: move events to the JSStreamSocket (Momtchil Momtchev) [#35772](https://github.com/nodejs/node/pull/35772)
- [[`92bd7b522a`](https://github.com/nodejs/node/commit/92bd7b522a)] - **http2**: fix error stream write followed by destroy (David Halls) [#35951](https://github.com/nodejs/node/pull/35951)
- [[`ec9fae96bc`](https://github.com/nodejs/node/commit/ec9fae96bc)] - **http2**: fix reinjection check (Momtchil Momtchev) [#35678](https://github.com/nodejs/node/pull/35678)
- [[`57f2fe0609`](https://github.com/nodejs/node/commit/57f2fe0609)] - **http2**: reinject data received before http2 is attached (Momtchil Momtchev) [#35678](https://github.com/nodejs/node/pull/35678)
- [[`2dbaaf92e5`](https://github.com/nodejs/node/commit/2dbaaf92e5)] - **http2**: remove unsupported %.\* specifier (Momtchil Momtchev) [#35694](https://github.com/nodejs/node/pull/35694)
- [[`de3c8045ac`](https://github.com/nodejs/node/commit/de3c8045ac)] - **lib**: refactor to use more primordials (Antoine du Hamel) [#35875](https://github.com/nodejs/node/pull/35875)
- [[`41d997cc72`](https://github.com/nodejs/node/commit/41d997cc72)] - **lib**: use primordials when calling methods of Error (Antoine du Hamel) [#35837](https://github.com/nodejs/node/pull/35837)
- [[`d58a466da0`](https://github.com/nodejs/node/commit/d58a466da0)] - **lib**: honor setUncaughtExceptionCaptureCallback (Gireesh Punathil) [#35595](https://github.com/nodejs/node/pull/35595)
- [[`1fdf72765b`](https://github.com/nodejs/node/commit/1fdf72765b)] - **module**: only try to enrich CJS syntax errors (Michaël Zasso) [#35691](https://github.com/nodejs/node/pull/35691)
- [[`81b0562c62`](https://github.com/nodejs/node/commit/81b0562c62)] - **n-api**: clean up binding creation (Gabriel Schulhof) [#36170](https://github.com/nodejs/node/pull/36170)
- [[`7a01e241ee`](https://github.com/nodejs/node/commit/7a01e241ee)] - **n-api**: fix test_async_context warnings (Gabriel Schulhof) [#36171](https://github.com/nodejs/node/pull/36171)
- [[`dde727e72f`](https://github.com/nodejs/node/commit/dde727e72f)] - **n-api**: improve consistency of how we get context (Michael Dawson) [#36068](https://github.com/nodejs/node/pull/36068)
- [[`08657e7e11`](https://github.com/nodejs/node/commit/08657e7e11)] - **n-api**: factor out calling pattern (Gabriel Schulhof) [#36113](https://github.com/nodejs/node/pull/36113)
- [[`88aa4e0d25`](https://github.com/nodejs/node/commit/88aa4e0d25)] - **n-api**: unlink reference during its destructor (Gabriel Schulhof) [#35933](https://github.com/nodejs/node/pull/35933)
- [[`1cb50c17d3`](https://github.com/nodejs/node/commit/1cb50c17d3)] - **n-api**: napi_make_callback emit async init with resource of async_context (legendecas) [#32930](https://github.com/nodejs/node/pull/32930)
- [[`f1e84f4dd8`](https://github.com/nodejs/node/commit/f1e84f4dd8)] - **n-api**: revert change to finalization (Michael Dawson) [#35777](https://github.com/nodejs/node/pull/35777)
- [[`e16124979d`](https://github.com/nodejs/node/commit/e16124979d)] - **querystring**: reduce memory usage by Int8Array (sapics) [#34179](https://github.com/nodejs/node/pull/34179)
- [[`5c81a1071e`](https://github.com/nodejs/node/commit/5c81a1071e)] - **src**: refactor using-declarations node_env_var.cc (raisinten) [#36128](https://github.com/nodejs/node/pull/36128)
- [[`2770cd941e`](https://github.com/nodejs/node/commit/2770cd941e)] - **src**: remove duplicate logic for getting buffer (Yash Ladha) [#34553](https://github.com/nodejs/node/pull/34553)
- [[`f2300390aa`](https://github.com/nodejs/node/commit/f2300390aa)] - **src**: create helper for reading Uint32BE (Juan José Arboleda) [#34944](https://github.com/nodejs/node/pull/34944)
- [[`34c870e9f0`](https://github.com/nodejs/node/commit/34c870e9f0)] - **src**: use MaybeLocal.ToLocal instead of IsEmpty (Daniel Bevenius) [#35716](https://github.com/nodejs/node/pull/35716)
- [[`00d9499b14`](https://github.com/nodejs/node/commit/00d9499b14)] - **src**: large pages support in illumos/solaris systems (David Carlier) [#34320](https://github.com/nodejs/node/pull/34320)
- [[`7c99885a9b`](https://github.com/nodejs/node/commit/7c99885a9b)] - **stream**: fix thrown object reference (Gil Pedersen) [#36065](https://github.com/nodejs/node/pull/36065)
- [[`1cefb7e710`](https://github.com/nodejs/node/commit/1cefb7e710)] - **stream**: fix regression on duplex end (Momtchil Momtchev) [#35941](https://github.com/nodejs/node/pull/35941)
- [[`d1fd3f27e4`](https://github.com/nodejs/node/commit/d1fd3f27e4)] - **stream**: remove redundant context from comments (Yash Ladha) [#35728](https://github.com/nodejs/node/pull/35728)
- [[`fb14acb22c`](https://github.com/nodejs/node/commit/fb14acb22c)] - **stream**: move to internal/streams (Matteo Collina) [#35239](https://github.com/nodejs/node/pull/35239)
- [[`40d59281f7`](https://github.com/nodejs/node/commit/40d59281f7)] - **test**: update comments in test-fs-read-offset-null (Rich Trott) [#36152](https://github.com/nodejs/node/pull/36152)
- [[`a563f79d80`](https://github.com/nodejs/node/commit/a563f79d80)] - **test**: fix typo in inspector-helper.js (Luigi Pinca) [#36127](https://github.com/nodejs/node/pull/36127)
- [[`3e77536c6b`](https://github.com/nodejs/node/commit/3e77536c6b)] - **test**: deflake test-http-destroyed-socket-write2 (Luigi Pinca) [#36120](https://github.com/nodejs/node/pull/36120)
- [[`402e29a87c`](https://github.com/nodejs/node/commit/402e29a87c)] - **test**: make test-http2-client-jsstream-destroy.js reliable (Rich Trott) [#36129](https://github.com/nodejs/node/pull/36129)
- [[`b6aa42c349`](https://github.com/nodejs/node/commit/b6aa42c349)] - **test**: add test for fs.read when offset key is null (mayank agarwal) [#35918](https://github.com/nodejs/node/pull/35918)
- [[`8516c2ef90`](https://github.com/nodejs/node/commit/8516c2ef90)] - **test**: improve test-stream-duplex-readable-end (Luigi Pinca) [#36056](https://github.com/nodejs/node/pull/36056)
- [[`b53068ec0d`](https://github.com/nodejs/node/commit/b53068ec0d)] - **test**: add util.inspect test for null maxStringLength (Rich Trott) [#36086](https://github.com/nodejs/node/pull/36086)
- [[`3029872631`](https://github.com/nodejs/node/commit/3029872631)] - **test**: replace var with const (Aleksandr Krutko) [#36069](https://github.com/nodejs/node/pull/36069)
- [[`b05cdfee64`](https://github.com/nodejs/node/commit/b05cdfee64)] - **test**: remove flaky designation for fixed test (Rich Trott) [#35961](https://github.com/nodejs/node/pull/35961)
- [[`002005f537`](https://github.com/nodejs/node/commit/002005f537)] - **test**: improve error message for policy failures (Bradley Meck) [#35633](https://github.com/nodejs/node/pull/35633)
- [[`1453de1381`](https://github.com/nodejs/node/commit/1453de1381)] - **test**: update old comment style test_util.cc (raisinten) [#35884](https://github.com/nodejs/node/pull/35884)
- [[`de375e16f4`](https://github.com/nodejs/node/commit/de375e16f4)] - **test**: add missing ref comments to parallel.status (Rich Trott) [#35896](https://github.com/nodejs/node/pull/35896)
- [[`cab65fbe63`](https://github.com/nodejs/node/commit/cab65fbe63)] - **test**: mark test-worker-eventlooputil flaky (Myles Borins) [#35886](https://github.com/nodejs/node/pull/35886)
- [[`4ed4b64293`](https://github.com/nodejs/node/commit/4ed4b64293)] - **test**: mark test-http2-respond-file-error-pipe-offset flaky (Myles Borins) [#35883](https://github.com/nodejs/node/pull/35883)
- [[`a5b94180fe`](https://github.com/nodejs/node/commit/a5b94180fe)] - **test**: fix reference to WPT testharness.js (Tobias Nießen) [#35814](https://github.com/nodejs/node/pull/35814)
- [[`3bb7f3602b`](https://github.com/nodejs/node/commit/3bb7f3602b)] - **test**: add onerror test cases to policy (Daijiro Wachi) [#35797](https://github.com/nodejs/node/pull/35797)
- [[`0aba12218a`](https://github.com/nodejs/node/commit/0aba12218a)] - **test**: add upstream test cases to encoding (Daijiro Wachi) [#35794](https://github.com/nodejs/node/pull/35794)
- [[`f535d6252f`](https://github.com/nodejs/node/commit/f535d6252f)] - **test**: add test for listen callback runtime binding (H Adinarayana) [#35657](https://github.com/nodejs/node/pull/35657)
- [[`d62e72b341`](https://github.com/nodejs/node/commit/d62e72b341)] - **test**: refactor test-https-host-headers (himself65) [#32805](https://github.com/nodejs/node/pull/32805)
- [[`70cb70812d`](https://github.com/nodejs/node/commit/70cb70812d)] - **test**: add common.mustSucceed (Tobias Nießen) [#35086](https://github.com/nodejs/node/pull/35086)
- [[`226c1800a8`](https://github.com/nodejs/node/commit/226c1800a8)] - **test**: check for AbortController existence (James M Snell) [#35616](https://github.com/nodejs/node/pull/35616)
- [[`41aac465cc`](https://github.com/nodejs/node/commit/41aac465cc)] - **timers**: correct explanation in comment (Turner Jabbour) [#35437](https://github.com/nodejs/node/pull/35437)
- [[`713d1ebe75`](https://github.com/nodejs/node/commit/713d1ebe75)] - **tools**: bump unist-util-find@1.0.1 to unist-util-find@1.0.2 (Rich Trott) [#36106](https://github.com/nodejs/node/pull/36106)
- [[`127a4fb810`](https://github.com/nodejs/node/commit/127a4fb810)] - **tools**: only use 2 cores for macos action (Myles Borins) [#36169](https://github.com/nodejs/node/pull/36169)
- [[`75e49b833b`](https://github.com/nodejs/node/commit/75e49b833b)] - **tools**: remove bashisms from license builder script (Antoine du Hamel) [#36122](https://github.com/nodejs/node/pull/36122)
- [[`28d6283f96`](https://github.com/nodejs/node/commit/28d6283f96)] - **tools**: hide commit queue action link (Antoine du Hamel) [#36124](https://github.com/nodejs/node/pull/36124)
- [[`b7441ea4d2`](https://github.com/nodejs/node/commit/b7441ea4d2)] - **tools**: update doc tools to remark-parse@9.0.0 (Rich Trott) [#36049](https://github.com/nodejs/node/pull/36049)
- [[`5a41282ef5`](https://github.com/nodejs/node/commit/5a41282ef5)] - **tools**: enforce use of single quotes in editorconfig (Antoine du Hamel) [#36020](https://github.com/nodejs/node/pull/36020)
- [[`23dd2b00dd`](https://github.com/nodejs/node/commit/23dd2b00dd)] - **tools**: fix config serialization w/ long strings (Ole André Vadla Ravnås) [#35982](https://github.com/nodejs/node/pull/35982)
- [[`**********`](https://github.com/nodejs/node/commit/**********)] - **tools**: don't print gold linker warning w/o flag (Myles Borins) [#35955](https://github.com/nodejs/node/pull/35955)
- [[`dfd6ad9d99`](https://github.com/nodejs/node/commit/dfd6ad9d99)] - **tools**: refloat 7 Node.js patches to cpplint.py (Rich Trott) [#35866](https://github.com/nodejs/node/pull/35866)
- [[`d177cb3993`](https://github.com/nodejs/node/commit/d177cb3993)] - **tools**: bump cpplint to 1.5.1 (Rich Trott) [#35866](https://github.com/nodejs/node/pull/35866)
- [[`b19a85ed2a`](https://github.com/nodejs/node/commit/b19a85ed2a)] - **tools**: add update-npm script (Myles Borins) [#35822](https://github.com/nodejs/node/pull/35822)
- [[`07e5d35d14`](https://github.com/nodejs/node/commit/07e5d35d14)] - **tools**: refloat 7 Node.js patches to cpplint.py (Rich Trott) [#35719](https://github.com/nodejs/node/pull/35719)
- [[`c7301533de`](https://github.com/nodejs/node/commit/c7301533de)] - **tools**: bump cpplint to 1.5.0 (Rich Trott) [#35719](https://github.com/nodejs/node/pull/35719)
- [[`985efdfa09`](https://github.com/nodejs/node/commit/985efdfa09)] - **tools**: update gyp-next to v0.6.2 (Michaël Zasso) [#35690](https://github.com/nodejs/node/pull/35690)
- [[`baca8ee873`](https://github.com/nodejs/node/commit/baca8ee873)] - **tools**: update gyp-next to v0.6.0 (Ujjwal Sharma) [#35635](https://github.com/nodejs/node/pull/35635)
- [[`3e7598da9b`](https://github.com/nodejs/node/commit/3e7598da9b)] - **tools,doc**: enable ecmaVersion 2021 in acorn parser (Antoine du Hamel) [#35994](https://github.com/nodejs/node/pull/35994)
- [[`dfb353b882`](https://github.com/nodejs/node/commit/dfb353b882)] - **util**: fix to inspect getters that access this (raisinten) [#36052](https://github.com/nodejs/node/pull/36052)
- [[`1906f19e49`](https://github.com/nodejs/node/commit/1906f19e49)] - **vm**: refactor to use more primordials (Antoine du Hamel) [#36023](https://github.com/nodejs/node/pull/36023)
- [[`ffe517b40d`](https://github.com/nodejs/node/commit/ffe517b40d)] - **win, build**: fix build time on Windows (Bartosz Sosnowski) [#35932](https://github.com/nodejs/node/pull/35932)
- [[`c4c8541621`](https://github.com/nodejs/node/commit/c4c8541621)] - **win,build,tools**: support VS prerelease (Baruch Odem) [#36033](https://github.com/nodejs/node/pull/36033)
- [[`f59e225675`](https://github.com/nodejs/node/commit/f59e225675)] - **zlib**: test BrotliCompress throws invalid arg value (raisinten) [#35830](https://github.com/nodejs/node/pull/35830)

Windows 32-bit Installer: https://nodejs.org/dist/v14.15.2/node-v14.15.2-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v14.15.2/node-v14.15.2-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v14.15.2/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v14.15.2/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v14.15.2/node-v14.15.2.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v14.15.2/node-v14.15.2-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v14.15.2/node-v14.15.2-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v14.15.2/node-v14.15.2-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v14.15.2/node-v14.15.2-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v14.15.2/node-v14.15.2-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v14.15.2/node-v14.15.2-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v14.15.2/node-v14.15.2-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v14.15.2/node-v14.15.2.tar.gz \
Other release files: https://nodejs.org/dist/v14.15.2/ \
Documentation: https://nodejs.org/docs/v14.15.2/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

ced5810b721cf1ffd97b3214e6e1749981928d7d787785ab6e93656c7eecef57  node-v14.15.2-aix-ppc64.tar.gz
b3e48891b6290cec6d97f3711cd6298aebfb88642b239c90a4018752be5088ba  node-v14.15.2-darwin-x64.tar.gz
d93b508a7be00fd10316f25a657a804b217aaada1595e8f45bd4f2948da74163  node-v14.15.2-darwin-x64.tar.xz
4bb648845c93273350ad408693a7f0fbb4099de762ebff60ef846d251230c046  node-v14.15.2-headers.tar.gz
a071f0239eaec720197d5a8d0a26c372613e5f1d9a5fd74105af33e39df83dbe  node-v14.15.2-headers.tar.xz
77277dc58534fa86f56591d952e914e04156ff00e95f31b4a2a34205f7222fa8  node-v14.15.2-linux-arm64.tar.gz
a3c4c88b82e547aae7d06bcf0c550b4fd808c2baf55b8e303c9a19eec52656e9  node-v14.15.2-linux-arm64.tar.xz
083cb06b246190b42d7266f219d8d15e544f1b19540b68a75bb3a37caf452785  node-v14.15.2-linux-armv7l.tar.gz
091709311701a49c1d23736dd1650b8101e03e09a742709c6f9d01f7c99f9ca6  node-v14.15.2-linux-armv7l.tar.xz
089a63509b6f03937453d27d53738964f87e749a219883f5b3d723758ffe7198  node-v14.15.2-linux-ppc64le.tar.gz
63009f7c8b41b384ace809a997906dcbd8bd5c4ff1ec9e411af2f7e5595e4363  node-v14.15.2-linux-ppc64le.tar.xz
9048751cf404083e3b65e3d634be0c4ac850ff166de51d5293bf25619333e571  node-v14.15.2-linux-s390x.tar.gz
cfe3b6b9ad6d9401d83b5cdc4d7f8dd70489df2ad4b9b5ee182284b630a4c174  node-v14.15.2-linux-s390x.tar.xz
c1d1d550fa9fa50af926da9e161a9e138f5dbd821479e29cb19a9cac25333369  node-v14.15.2-linux-x64.tar.gz
3c9de4caab293ea5f368ad9d0a8b506f15799629dac6eb07e9bb401eb208a5cb  node-v14.15.2-linux-x64.tar.xz
f8895bcf8e969281c5fa804363e21c012c2a65d3e88f0b6850ddcb8470a61699  node-v14.15.2.pkg
f3a6558bd4991f6bb2b4c9d1fc8454518003cebfc732a82aab15dc99069952de  node-v14.15.2.tar.gz
e029df267f969ef2a7b6d096d85539286f83f44aa53cdc3863cbe9ae53d8e680  node-v14.15.2.tar.xz
f32bf658e8abd0589187fee6d05e526bbcd649c98ee61dbb946044c04bcf6d5c  node-v14.15.2-win-x64.7z
473d85b830922f5d3acd4436f26309cf1ad207272c499b5f3c0a7d84a8c8aa47  node-v14.15.2-win-x64.zip
0ae4356e24abaea8bb2ee271ff06cdd08e3b16a317d0bc3c1e1f9e840ac93892  node-v14.15.2-win-x86.7z
39d82cfa5412d18311761b9dbf02c53e9620763b87fc87041d4b0a64640d05cf  node-v14.15.2-win-x86.zip
1ce38ebb35cca0b926776b98f1c74c88d096a34978c2cab33ee15996923c7832  node-v14.15.2-x64.msi
2adfa32e0c0e5a82cae92a509750e49ac8aa16cda6e91677d73d72e72b37ccf2  node-v14.15.2-x86.msi
9ea524ae107592f4aa3f9ae086fb8cd38d6f9f250f4232512d69d732e11573d0  win-x64/node.exe
32f62aef1ee247241be0e1d96d7db7c8af0ae6d1ab82ac5f41526b226a1381f6  win-x64/node.lib
001d4a44c45453652f6ff320e9006c2e2dc14c3b5d849c81c9d76c123e2f65d5  win-x64/node_pdb.7z
99d9b88aa11c3276a730cd845173bb0713273777bd92fe4d359136d4a6def2c5  win-x64/node_pdb.zip
53bff31771f23328914190b69182171c0de93685f3bf2c5b3e77b027a080fe24  win-x86/node.exe
060745e3cef2f5505cf94cc96686470b407983d333a6e9d0f5713cdd98a9472e  win-x86/node.lib
7af2ad5dcb53fd28d5da570fb240d13827f2805894b5d3e5ad4245f02d3aee71  win-x86/node_pdb.7z
9b0609020b0859856f2ffc529a6faff768ae2753677799b3a4221e4003c40713  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQEzBAEBCAAdFiEETtd49TnjY0x3nIfG1wYoSKGrAFwFAl/ZTPYACgkQ1wYoSKGr
AFw2KAgAmrZe/X6lusajfTI9+vD1neBQFj1iL85trctxk94u75AqEw59c0rCYTZ4
1jOd9VMsA5WKQMNwWb8EGY8OwgNnFoUkSskFw1pb6aB4a4c7r4GZKuyvS7XtdfRu
um1+1zgcz/L1tZHv0sqYz7KAorRXbULmJ6e25XH/DdzHxuchTqFnzUvvP7kOyOQI
d757imOCObuI03O+U9EoQA+voqaAcoFuNzIPLAlmfdnKyEjW+pX74erB3h0Rxf/A
HUqIlT/GMu1PHbUw20x4EOf1IES3ilRcG4Cy3xQc1eQA9a8I3S0HWsktqOb9fYn1
6TVSaHEQMts5aJiC2sS4NOw8TIhRdQ==
=yRJs
-----END PGP SIGNATURE-----

```
