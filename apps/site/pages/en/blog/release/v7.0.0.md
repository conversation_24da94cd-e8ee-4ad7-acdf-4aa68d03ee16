---
date: '2016-10-25T14:17:02.177Z'
category: release
title: Node v7.0.0 (Current)
layout: blog-post
author: <PERSON>
---

Node.js v7 has arrived!

This new major version of Node.js includes: V8 5.4 which brings along with 98% coverage of ES6 language features, improved reliability and performance, and a new experimental URL parser based on the [WHATWG URL standard](https://url.spec.whatwg.org/).

As an odd numbered release, in accordance with our [Long Term Support plan](https://github.com/nodejs/lts), Node.js v7 will be supported only until about June, 2017, with Node.js v8 currently scheduled for release in April, 2017.

The general rule for deciding which version of Node.js to use is:

- Stay on Node.js v4 or upgrade to Node.js v6 if you need _stability_ and have a complex production environment, e.g. you are a medium or large enterprise.
- Upgrade to Node.js v7 if you have the ability to upgrade versions quickly and easily without disrupting your environment and want to play with the latest features as they arrive.

The release notes below are annotated with the main breaking changes. Note that because this new version of Node.js is shipping with a new version of V8, existing native addons will need to be recompiled or runtime errors will occur when trying to load them. Use `npm rebuild` or simply remove your `node_modules` and `npm install` from scratch.

### Notable changes

- Buffer
  - Passing invalid input to Buffer.byteLength will now throw an error [#8946](https://github.com/nodejs/node/pull/8946).
  - Calling Buffer without new is now deprecated and will emit a process warning [#8169](https://github.com/nodejs/node/pull/8169).
  - Passing a negative number to allocUnsafe will now throw an error [#7079](https://github.com/nodejs/node/pull/7079).
- Child Process
  - The fork and execFile methods now have stronger argument validation [#7399](https://github.com/nodejs/node/pull/7399).
- Cluster
  - The worker.suicide method is deprecated and will emit a process warning [#3747](https://github.com/nodejs/node/pull/3747).
- Deps
  - V8 has been updated to 5.4.500.36 [#8317](https://github.com/nodejs/node/pull/8317), [#8852](https://github.com/nodejs/node/pull/8852),
    [#9253](https://github.com/nodejs/node/pull/9253).
  - NODE_MODULE_VERSION has been updated to 51 [#8808](https://github.com/nodejs/node/pull/8808).
- File System
  - A process warning is emitted if a callback is not passed to async file system methods [#7897](https://github.com/nodejs/node/pull/7897).
- Intl
  - Intl.v8BreakIterator constructor has been deprecated and will emit a process warning [#8908](https://github.com/nodejs/node/pull/8908).
- Promises
  - Unhandled Promise rejections have been deprecated and will emit a process warning [#8217](https://github.com/nodejs/node/pull/8217).
- Punycode
  - The `punycode` module has been deprecated [#7941](https://github.com/nodejs/node/pull/7941).
- URL
  - An Experimental WHATWG URL Parser has been introduced [#7448](https://github.com/nodejs/node/pull/7448).

### Commits

- [[`1043f5d08e`](https://github.com/nodejs/node/commit/1043f5d08e)] - **assert**: name anonymous functions (Miguel Angel Asencio Hurtado) [#9051](https://github.com/nodejs/node/pull/9051)
- [[`06f37471aa`](https://github.com/nodejs/node/commit/06f37471aa)] - **benchmark**: use node v4 syntax in common.js (Andreas Madsen) [#9064](https://github.com/nodejs/node/pull/9064)
- [[`8b152fcf47`](https://github.com/nodejs/node/commit/8b152fcf47)] - **benchmark**: change the execution order (Andreas Madsen) [#9064](https://github.com/nodejs/node/pull/9064)
- [[`a5046bf8ef`](https://github.com/nodejs/node/commit/a5046bf8ef)] - **benchmark**: fixes csv parsing given no parameters (Andreas Madsen) [#9064](https://github.com/nodejs/node/pull/9064)
- [[`af01865d66`](https://github.com/nodejs/node/commit/af01865d66)] - **benchmark**: add info about required Unix tools (Bartosz Sosnowski) [#8788](https://github.com/nodejs/node/pull/8788)
- [[`dfb5f301cf`](https://github.com/nodejs/node/commit/dfb5f301cf)] - **benchmark**: make v8-bench.js output consistent (Bartosz Sosnowski) [#8564](https://github.com/nodejs/node/pull/8564)
- [[`84481f9157`](https://github.com/nodejs/node/commit/84481f9157)] - **benchmark**: add --expose_internals switch (Bartosz Sosnowski) [#8547](https://github.com/nodejs/node/pull/8547)
- [[`d3834a1fa3`](https://github.com/nodejs/node/commit/d3834a1fa3)] - **benchmark**: ignore significance when using --runs 1 (Andreas Madsen) [#8299](https://github.com/nodejs/node/pull/8299)
- [[`b1bbc68fb1`](https://github.com/nodejs/node/commit/b1bbc68fb1)] - **benchmark**: support for multiple http benchmarkers (Bartosz Sosnowski) [#8140](https://github.com/nodejs/node/pull/8140)
- [[`474e629ddb`](https://github.com/nodejs/node/commit/474e629ddb)] - **benchmark**: add --format csv option (Adrian Nitu) [#7961](https://github.com/nodejs/node/pull/7961)
- [[`4b527a4129`](https://github.com/nodejs/node/commit/4b527a4129)] - **benchmark**: update compare.js exit method (Adrian Nitu) [#7961](https://github.com/nodejs/node/pull/7961)
- [[`9e7fd8e810`](https://github.com/nodejs/node/commit/9e7fd8e810)] - **benchmark**: fix comment typos and code format (Adrian Nitu) [#7961](https://github.com/nodejs/node/pull/7961)
- [[`d525e6c92a`](https://github.com/nodejs/node/commit/d525e6c92a)] - **(SEMVER-MAJOR)** **benchmark**: remove broken string-creation.js (Andreas Madsen) [#7094](https://github.com/nodejs/node/pull/7094)
- [[`6edef1deb9`](https://github.com/nodejs/node/commit/6edef1deb9)] - **(SEMVER-MAJOR)** **benchmark**: update docs after refactor (Andreas Madsen) [#7094](https://github.com/nodejs/node/pull/7094)
- [[`0c0f34e2fe`](https://github.com/nodejs/node/commit/0c0f34e2fe)] - **(SEMVER-MAJOR)** **benchmark**: add script for creating scatter plot (Andreas Madsen) [#7094](https://github.com/nodejs/node/pull/7094)
- [[`855009af7f`](https://github.com/nodejs/node/commit/855009af7f)] - **(SEMVER-MAJOR)** **benchmark**: use t-test for comparing node versions (Andreas Madsen) [#7094](https://github.com/nodejs/node/pull/7094)
- [[`8bb59fdb12`](https://github.com/nodejs/node/commit/8bb59fdb12)] - **(SEMVER-MAJOR)** **benchmark**: missing process.exit after bench.end (Andreas Madsen) [#7094](https://github.com/nodejs/node/pull/7094)
- [[`f99471b2ae`](https://github.com/nodejs/node/commit/f99471b2ae)] - **(SEMVER-MAJOR)** **benchmark**: refactor to use process.send (Andreas Madsen) [#7094](https://github.com/nodejs/node/pull/7094)
- [[`0f9bfaa7c5`](https://github.com/nodejs/node/commit/0f9bfaa7c5)] - **(SEMVER-MAJOR)** **benchmark**: move cli parts of common.js into run.js (Andreas Madsen) [#7094](https://github.com/nodejs/node/pull/7094)
- [[`edbed3f3fd`](https://github.com/nodejs/node/commit/edbed3f3fd)] - **(SEMVER-MAJOR)** **benchmark**: move http_simple.js to http directory (Andreas Madsen) [#7094](https://github.com/nodejs/node/pull/7094)
- [[`ee2843b4ea`](https://github.com/nodejs/node/commit/ee2843b4ea)] - **(SEMVER-MAJOR)** **benchmark**: remove unused files (Andreas Madsen) [#7094](https://github.com/nodejs/node/pull/7094)
- [[`60042ca70e`](https://github.com/nodejs/node/commit/60042ca70e)] - **buffer**: fix range checks for slice() (Trevor Norris) [#9174](https://github.com/nodejs/node/pull/9174)
- [[`14d1a8a631`](https://github.com/nodejs/node/commit/14d1a8a631)] - **buffer**: coerce slice parameters consistently (Sakthipriyan Vairamani (thefourtheye)) [#9101](https://github.com/nodejs/node/pull/9101)
- [[`96b501d338`](https://github.com/nodejs/node/commit/96b501d338)] - **(SEMVER-MAJOR)** **buffer**: make byteLength throw on invalid input (Brian White) [#8946](https://github.com/nodejs/node/pull/8946)
- [[`c21458a15d`](https://github.com/nodejs/node/commit/c21458a15d)] - **(SEMVER-MINOR)** **buffer**: expose underlying buffer object always (Sakthipriyan Vairamani) [#8311](https://github.com/nodejs/node/pull/8311)
- [[`2c9a86f01e`](https://github.com/nodejs/node/commit/2c9a86f01e)] - **buffer**: directly use ArrayBuffer as the pool (Anna Henningsen) [#8302](https://github.com/nodejs/node/pull/8302)
- [[`f2fe5583c4`](https://github.com/nodejs/node/commit/f2fe5583c4)] - **(SEMVER-MAJOR)** **buffer**: runtime deprecation of calling Buffer without new (Nikolai Vavilov) [#8169](https://github.com/nodejs/node/pull/8169)
- [[`9cee8b1b62`](https://github.com/nodejs/node/commit/9cee8b1b62)] - **(SEMVER-MAJOR)** **buffer**: alias toLocaleString to toString (James M Snell) [#8148](https://github.com/nodejs/node/pull/8148)
- [[`8f90dcc1b8`](https://github.com/nodejs/node/commit/8f90dcc1b8)] - **(SEMVER-MAJOR)** **buffer**: throw on negative .allocUnsafe() argument (Anna Henningsen) [#7079](https://github.com/nodejs/node/pull/7079)
- [[`bd23290657`](https://github.com/nodejs/node/commit/bd23290657)] - **buffer**: remove obsolete and confusing comment (Nikolai Vavilov) [#7264](https://github.com/nodejs/node/pull/7264)
- [[`5292a1358f`](https://github.com/nodejs/node/commit/5292a1358f)] - **buffer**: improve creation performance. (Ingvar Stepanyan) [#6893](https://github.com/nodejs/node/pull/6893)
- [[`c5f5bcb331`](https://github.com/nodejs/node/commit/c5f5bcb331)] - **build**: fix config.gypi target (Daniel Bevenius) [#9053](https://github.com/nodejs/node/pull/9053)
- [[`b311906abf`](https://github.com/nodejs/node/commit/b311906abf)] - **(SEMVER-MAJOR)** **build**: do not clean V8 gtest directory (Michaël Zasso) [#8317](https://github.com/nodejs/node/pull/8317)
- [[`94f68b5b97`](https://github.com/nodejs/node/commit/94f68b5b97)] - **(SEMVER-MAJOR)** **build**: fix mkpeephole configuration (Ali Ijaz Sheikh) [#8317](https://github.com/nodejs/node/pull/8317)
- [[`8481ea1ca4`](https://github.com/nodejs/node/commit/8481ea1ca4)] - **(SEMVER-MAJOR)** **build**: use libc++ on OSX (Ali Ijaz Sheikh) [#8317](https://github.com/nodejs/node/pull/8317)
- [[`197d18795e`](https://github.com/nodejs/node/commit/197d18795e)] - **(SEMVER-MAJOR)** **build**: define icu_use_data_file_flag (Ali Ijaz Sheikh) [#8317](https://github.com/nodejs/node/pull/8317)
- [[`eab418f7f9`](https://github.com/nodejs/node/commit/eab418f7f9)] - **(SEMVER-MAJOR)** **build**: update V8 gypfile paths (Michaël Zasso) [#8317](https://github.com/nodejs/node/pull/8317)
- [[`88e862ba82`](https://github.com/nodejs/node/commit/88e862ba82)] - **build**: windows sharedlib support (Stefan Budeanu) [#7487](https://github.com/nodejs/node/pull/7487)
- [[`6eece7773e`](https://github.com/nodejs/node/commit/6eece7773e)] - **child_process**: update outdated comment (Tanuja-Sawant)
- [[`0548e5d12a`](https://github.com/nodejs/node/commit/0548e5d12a)] - **(SEMVER-MAJOR)** **child_process**: add fork/execFile arg validation (Rich Trott) [#7399](https://github.com/nodejs/node/pull/7399)
- [[`b90f3da9de`](https://github.com/nodejs/node/commit/b90f3da9de)] - **(SEMVER-MAJOR)** **child_process, win**: fix shell spawn with AutoRun (Bartosz Sosnowski) [#8063](https://github.com/nodejs/node/pull/8063)
- [[`f44b18f010`](https://github.com/nodejs/node/commit/f44b18f010)] - **(SEMVER-MAJOR)** **cluster**: deprecate worker.suicide (Evan Lucas) [#3747](https://github.com/nodejs/node/pull/3747)
- [[`bd7d7a7e17`](https://github.com/nodejs/node/commit/bd7d7a7e17)] - **console**: name anonymous functions (Tyler Brazier) [#9047](https://github.com/nodejs/node/pull/9047)
- [[`c60d43b6d9`](https://github.com/nodejs/node/commit/c60d43b6d9)] - **crypto**: fix faulty logic in iv size check (Ben Noordhuis) [#9032](https://github.com/nodejs/node/pull/9032)
- [[`72f1c41fb6`](https://github.com/nodejs/node/commit/72f1c41fb6)] - **crypto**: naming anonymous functions (solebox) [#8993](https://github.com/nodejs/node/pull/8993)
- [[`89643b645e`](https://github.com/nodejs/node/commit/89643b645e)] - **crypto**: use SSL_get_SSL_CTX. (Adam Langley) [#8995](https://github.com/nodejs/node/pull/8995)
- [[`f4aa2c2c93`](https://github.com/nodejs/node/commit/f4aa2c2c93)] - **(SEMVER-MAJOR)** **crypto**: remove POINT_CONVERSION_HYBRID from documentation. (Adam Langley) [#4956](https://github.com/nodejs/node/pull/4956)
- [[`6bbdd668bd`](https://github.com/nodejs/node/commit/6bbdd668bd)] - **deps**: update V8 to 5.4.500.36 (Michaël Zasso) [#9253](https://github.com/nodejs/node/pull/9253)
- [[`5e3a480ad5`](https://github.com/nodejs/node/commit/5e3a480ad5)] - **deps**: revert default gtest reporter change (Brian White) [#8948](https://github.com/nodejs/node/pull/8948)
- [[`c0a3ac2e94`](https://github.com/nodejs/node/commit/c0a3ac2e94)] - **deps**: cherry-pick missing v8 floating patch (Michael Dawson) [#8907](https://github.com/nodejs/node/pull/8907)
- [[`bef4b3bfda`](https://github.com/nodejs/node/commit/bef4b3bfda)] - **deps**: update V8 to 5.4.500.31 (Michaël Zasso) [#8852](https://github.com/nodejs/node/pull/8852)
- [[`a88bb3a758`](https://github.com/nodejs/node/commit/a88bb3a758)] - **(SEMVER-MAJOR)** **deps**: cherry-pick workaround for clang-3.4 ICE (Michaël Zasso) [#8317](https://github.com/nodejs/node/pull/8317)
- [[`90efff6000`](https://github.com/nodejs/node/commit/90efff6000)] - **(SEMVER-MAJOR)** **deps**: update V8 to 5.4.500.27 (Michaël Zasso) [#8317](https://github.com/nodejs/node/pull/8317)
- [[`245ac302f5`](https://github.com/nodejs/node/commit/245ac302f5)] - **(SEMVER-MINOR)** **deps**: update V8 to 5.1.281.75 (Ben Noordhuis) [#7615](https://github.com/nodejs/node/pull/7615)
- [[`dc17432208`](https://github.com/nodejs/node/commit/dc17432208)] - **deps**: fix V8 5.1 tests (Michaël Zasso) [#7488](https://github.com/nodejs/node/pull/7488)
- [[`2cc2951796`](https://github.com/nodejs/node/commit/2cc2951796)] - **(SEMVER-MINOR)** **deps**: update V8 to 5.1.281.69 (Michaël Zasso) [#7016](https://github.com/nodejs/node/pull/7016)
- [[`e9b6fbbf17`](https://github.com/nodejs/node/commit/e9b6fbbf17)] - **(SEMVER-MAJOR)** **dgram**: prefer strict equality, type validation (Claudio Rodriguez) [#8011](https://github.com/nodejs/node/pull/8011)
- [[`260f41f2cc`](https://github.com/nodejs/node/commit/260f41f2cc)] - **dns**: name anonymous functions (Miguel Angel Asencio Hurtado) [#9052](https://github.com/nodejs/node/pull/9052)
- [[`3238f15234`](https://github.com/nodejs/node/commit/3238f15234)] - **doc**: change os x tag to macos (Gibson Fahnestock) [#9009](https://github.com/nodejs/node/pull/9009)
- [[`1794456ebc`](https://github.com/nodejs/node/commit/1794456ebc)] - **doc**: change solaris tag to smartos (Gibson Fahnestock) [#9009](https://github.com/nodejs/node/pull/9009)
- [[`98ca07bfe0`](https://github.com/nodejs/node/commit/98ca07bfe0)] - **doc**: add teams for platform-specific issues (Gibson Fahnestock) [#9009](https://github.com/nodejs/node/pull/9009)
- [[`e4e60b45e1`](https://github.com/nodejs/node/commit/e4e60b45e1)] - **doc**: add s390 and ppc architecture labels (Gibson Fahnestock) [#9009](https://github.com/nodejs/node/pull/9009)
- [[`3b580145c4`](https://github.com/nodejs/node/commit/3b580145c4)] - **doc**: fixes formatting in process (Rod Machen) [#9235](https://github.com/nodejs/node/pull/9235)
- [[`3e2bafe053`](https://github.com/nodejs/node/commit/3e2bafe053)] - **doc**: improve header styling for API docs (Jeremiah Senkpiel) [#8811](https://github.com/nodejs/node/pull/8811)
- [[`0a307f90cd`](https://github.com/nodejs/node/commit/0a307f90cd)] - **doc**: clarify fs.link and fs.linkSync arguments (Kyle E. Mitchell) [#9145](https://github.com/nodejs/node/pull/9145)
- [[`38cf1d4739`](https://github.com/nodejs/node/commit/38cf1d4739)] - **doc**: remove confusing reference in governance doc (Rich Trott) [#9073](https://github.com/nodejs/node/pull/9073)
- [[`5bf215d6b9`](https://github.com/nodejs/node/commit/5bf215d6b9)] - **doc**: suggest nodejs/help for general support (Myles Borins) [#9128](https://github.com/nodejs/node/pull/9128)
- [[`5e26980937`](https://github.com/nodejs/node/commit/5e26980937)] - **doc**: fix header level for crypto.constants (Evan Lucas) [#9187](https://github.com/nodejs/node/pull/9187)
- [[`89b920fead`](https://github.com/nodejs/node/commit/89b920fead)] - **doc**: add ctc-review label information (Rich Trott) [#9072](https://github.com/nodejs/node/pull/9072)
- [[`e6d1d54230`](https://github.com/nodejs/node/commit/e6d1d54230)] - **doc**: fix typo in zlib.md (Parambir Singh) [#9123](https://github.com/nodejs/node/pull/9123)
- [[`3b63b64992`](https://github.com/nodejs/node/commit/3b63b64992)] - **doc**: further improve child_process doc types (Indrek Ardel) [#9095](https://github.com/nodejs/node/pull/9095)
- [[`f56bdecde1`](https://github.com/nodejs/node/commit/f56bdecde1)] - **doc**: edit Stream api grammar (Benji Marinacci) [#9100](https://github.com/nodejs/node/pull/9100)
- [[`95d45d750a`](https://github.com/nodejs/node/commit/95d45d750a)] - **doc**: improved example for http.get (marzelin) [#9065](https://github.com/nodejs/node/pull/9065)
- [[`0022bfe42e`](https://github.com/nodejs/node/commit/0022bfe42e)] - **doc**: update reference to list hash algorithms in crypto.md (scott stern) [#9043](https://github.com/nodejs/node/pull/9043)
- [[`b0da43104f`](https://github.com/nodejs/node/commit/b0da43104f)] - **doc**: specify that errno is a number, not a string (John Vilk) [#9007](https://github.com/nodejs/node/pull/9007)
- [[`c258dc89d9`](https://github.com/nodejs/node/commit/c258dc89d9)] - **doc**: highlight deprecated API in ToC (Ilya Frolov) [#7189](https://github.com/nodejs/node/pull/7189)
- [[`d529a46416`](https://github.com/nodejs/node/commit/d529a46416)] - **doc**: explains why Reviewed-By is added in PRs (jessicaquynh) [#9044](https://github.com/nodejs/node/pull/9044)
- [[`482995e7cc`](https://github.com/nodejs/node/commit/482995e7cc)] - **doc**: explain why GitHub merge button is not used (jessicaquynh) [#9044](https://github.com/nodejs/node/pull/9044)
- [[`3735f22480`](https://github.com/nodejs/node/commit/3735f22480)] - **doc**: fix typo (Nikolai Vavilov) [#9089](https://github.com/nodejs/node/pull/9089)
- [[`839f1f02ed`](https://github.com/nodejs/node/commit/839f1f02ed)] - **doc**: fix broken links in changelogs (Evan Lucas) [#8122](https://github.com/nodejs/node/pull/8122)
- [[`8f6589a0ba`](https://github.com/nodejs/node/commit/8f6589a0ba)] - **doc**: revise http documentation (Timothy Gu) [#8486](https://github.com/nodejs/node/pull/8486)
- [[`f3f5a89a10`](https://github.com/nodejs/node/commit/f3f5a89a10)] - **doc**: \*.md formatting fixes in the benchmark dir (Сковорода Никита Андреевич) [#7727](https://github.com/nodejs/node/pull/7727)
- [[`9744928cf5`](https://github.com/nodejs/node/commit/9744928cf5)] - **doc**: fix layout problem in v4 changelog (Myles Borins) [#7394](https://github.com/nodejs/node/pull/7394)
- [[`d976d66cfc`](https://github.com/nodejs/node/commit/d976d66cfc)] - **doc**: clarify fs.access works on directories too. (Lance Ball) [#7113](https://github.com/nodejs/node/pull/7113)
- [[`8c1d5e58d4`](https://github.com/nodejs/node/commit/8c1d5e58d4)] - **doc**: improve rendering of v4.4.5 changelog entry (Myles Borins) [#6958](https://github.com/nodejs/node/pull/6958)
- [[`2bceda6493`](https://github.com/nodejs/node/commit/2bceda6493)] - **doc**: get rid of sneaky hard tabs in CHANGELOG (Myles Borins) [#6608](https://github.com/nodejs/node/pull/6608)
- [[`29e49fc286`](https://github.com/nodejs/node/commit/29e49fc286)] - **(SEMVER-MAJOR)** **doc, punycode**: soft-deprecation of the punycode module (James M Snell) [#7941](https://github.com/nodejs/node/pull/7941)
- [[`3b8ec68a3a`](https://github.com/nodejs/node/commit/3b8ec68a3a)] - **(SEMVER-MAJOR)** **domain**: add message for dispose deprecation (Brian White) [#7053](https://github.com/nodejs/node/pull/7053)
- [[`983775d457`](https://github.com/nodejs/node/commit/983775d457)] - **(SEMVER-MAJOR)** **events**: make memory leak warning name more verbose (Anna Henningsen) [#8341](https://github.com/nodejs/node/pull/8341)
- [[`b7a8a691b4`](https://github.com/nodejs/node/commit/b7a8a691b4)] - **(SEMVER-MAJOR)** **events**: unwrap #once listeners in #listeners (Owen Smith) [#6881](https://github.com/nodejs/node/pull/6881)
- [[`108c1fbbe2`](https://github.com/nodejs/node/commit/108c1fbbe2)] - **fs**: clarify fs.link and fs.linkSync arguments (Kyle E. Mitchell) [#9145](https://github.com/nodejs/node/pull/9145)
- [[`7f7d1d385d`](https://github.com/nodejs/node/commit/7f7d1d385d)] - **(SEMVER-MAJOR)** **fs**: move stringToFlags() to lib/internal (Ben Noordhuis) [#7162](https://github.com/nodejs/node/pull/7162)
- [[`fe9f5bcd75`](https://github.com/nodejs/node/commit/fe9f5bcd75)] - **fs**: don't alter user provided `options` object (Sakthipriyan Vairamani (thefourtheye)) [#7831](https://github.com/nodejs/node/pull/7831)
- [[`169f485289`](https://github.com/nodejs/node/commit/169f485289)] - **(SEMVER-MAJOR)** **fs**: refactor "options" processing as a function (Sakthipriyan Vairamani) [#7165](https://github.com/nodejs/node/pull/7165)
- [[`21124ba23a`](https://github.com/nodejs/node/commit/21124ba23a)] - **(SEMVER-MAJOR)** **fs**: do not emit 'stop' watch event synchronously (Claudio Rodriguez) [#8524](https://github.com/nodejs/node/pull/8524)
- [[`b50557b51b`](https://github.com/nodejs/node/commit/b50557b51b)] - **fs**: use process.emitWarning to print deprecation warning (James M Snell) [#8166](https://github.com/nodejs/node/pull/8166)
- [[`dc7277909b`](https://github.com/nodejs/node/commit/dc7277909b)] - **fs**: move SyncWriteStream to internal/fs (James M Snell) [#6749](https://github.com/nodejs/node/pull/6749)
- [[`49ef3ae90a`](https://github.com/nodejs/node/commit/49ef3ae90a)] - **(SEMVER-MAJOR)** **_Revert_** "**fs**: add a temporary fix for re-evaluation support" (James M Snell) [#6413](https://github.com/nodejs/node/pull/6413)
- [[`f8f283b8f3`](https://github.com/nodejs/node/commit/f8f283b8f3)] - **(SEMVER-MAJOR)** **fs**: warn if no callback is passed to async calls (Sakthipriyan Vairamani) [#7897](https://github.com/nodejs/node/pull/7897)
- [[`6f27bedfba`](https://github.com/nodejs/node/commit/6f27bedfba)] - **governance**: expand use of CTC issue tracker (Rich Trott) [#8945](https://github.com/nodejs/node/pull/8945)
- [[`179150091f`](https://github.com/nodejs/node/commit/179150091f)] - **http**: name anonymous functions in http (maasencioh) [#9055](https://github.com/nodejs/node/pull/9055)
- [[`73a8d3b0da`](https://github.com/nodejs/node/commit/73a8d3b0da)] - **http**: name anonymous functions in \_http_server (maasencioh) [#9055](https://github.com/nodejs/node/pull/9055)
- [[`ffa5c9ea30`](https://github.com/nodejs/node/commit/ffa5c9ea30)] - **http**: name anonymous functions in \_http_outgoing (maasencioh) [#9055](https://github.com/nodejs/node/pull/9055)
- [[`fa035ada90`](https://github.com/nodejs/node/commit/fa035ada90)] - **http**: name anonymous functions in \_http_incoming (maasencioh) [#9055](https://github.com/nodejs/node/pull/9055)
- [[`ec17e76656`](https://github.com/nodejs/node/commit/ec17e76656)] - **http**: name anonymous functions in \_http_client (maasencioh) [#9055](https://github.com/nodejs/node/pull/9055)
- [[`9099a43073`](https://github.com/nodejs/node/commit/9099a43073)] - **http**: name anonymous functions (maasencioh) [#9054](https://github.com/nodejs/node/pull/9054)
- [[`cde2ca96e4`](https://github.com/nodejs/node/commit/cde2ca96e4)] - **http**: reject control characters in http.request() (Ben Noordhuis) [#8923](https://github.com/nodejs/node/pull/8923)
- [[`2cc7fa5e7d`](https://github.com/nodejs/node/commit/2cc7fa5e7d)] - **(SEMVER-MAJOR)** **http**: remove deprecated Client interface (Brian White) [#8104](https://github.com/nodejs/node/pull/8104)
- [[`31bef6b704`](https://github.com/nodejs/node/commit/31bef6b704)] - **(SEMVER-MAJOR)** **http**: correct error message for invalid trailer (Bryan English) [#6308](https://github.com/nodejs/node/pull/6308)
- [[`a54ec7f49c`](https://github.com/nodejs/node/commit/a54ec7f49c)] - **inspector**: no URLs when the debugger is connected (Eugene Ostroukhov) [#8919](https://github.com/nodejs/node/pull/8919)
- [[`626a07df5b`](https://github.com/nodejs/node/commit/626a07df5b)] - **inspector**: restore 9229 as a default port (Eugene Ostroukhov) [#8550](https://github.com/nodejs/node/pull/8550)
- [[`9f1f7e2a34`](https://github.com/nodejs/node/commit/9f1f7e2a34)] - **(SEMVER-MAJOR)** **inspector**: listen on process.debugPort (cjihrig) [#8386](https://github.com/nodejs/node/pull/8386)
- [[`7b73f55902`](https://github.com/nodejs/node/commit/7b73f55902)] - **internal/util**: remove printDeprecationWarning (James M Snell) [#8166](https://github.com/nodejs/node/pull/8166)
- [[`9ad3082b1c`](https://github.com/nodejs/node/commit/9ad3082b1c)] - **(SEMVER-MAJOR)** **intl**: add deprecation warning for v8BreakIterator (Michaël Zasso) [#8908](https://github.com/nodejs/node/pull/8908)
- [[`15eaba98a1`](https://github.com/nodejs/node/commit/15eaba98a1)] - **lib**: use emitWarning instead of printDeprecationMessage (James M Snell) [#8166](https://github.com/nodejs/node/pull/8166)
- [[`3a3996315c`](https://github.com/nodejs/node/commit/3a3996315c)] - **lib,src**: reset zero fill flag on exception (Ben Noordhuis) [#7093](https://github.com/nodejs/node/pull/7093)
- [[`27e84ddd4e`](https://github.com/nodejs/node/commit/27e84ddd4e)] - **lib,src**: clean up ArrayBufferAllocator (Ben Noordhuis) [#7082](https://github.com/nodejs/node/pull/7082)
- [[`334ef4f19d`](https://github.com/nodejs/node/commit/334ef4f19d)] - **lib,src**: drop dependency on v8::Private::ForApi() (Ben Noordhuis) [#7082](https://github.com/nodejs/node/pull/7082)
- [[`d582193613`](https://github.com/nodejs/node/commit/d582193613)] - **(SEMVER-MAJOR)** **module**: Remove deprecated function requireRepl. (Adri Van Houdt) [#8575](https://github.com/nodejs/node/pull/8575)
- [[`6f1cae70eb`](https://github.com/nodejs/node/commit/6f1cae70eb)] - **net**: fix ambiguity in EOF handling (Fedor Indutny) [#9066](https://github.com/nodejs/node/pull/9066)
- [[`fd6af98c2d`](https://github.com/nodejs/node/commit/fd6af98c2d)] - **(SEMVER-MAJOR)** **net**: refactor Server.prototype.listen (Jan Schär) [#4039](https://github.com/nodejs/node/pull/4039)
- [[`5e5ec2cd1e`](https://github.com/nodejs/node/commit/5e5ec2cd1e)] - **(SEMVER-MAJOR)** **os**: deprecate `tmpDir()` in favour of `tmpdir()` (Jeremiah Senkpiel) [#6739](https://github.com/nodejs/node/pull/6739)
- [[`aedb72e03b`](https://github.com/nodejs/node/commit/aedb72e03b)] - **process**: improve performance of nextTick (Evan Lucas) [#8932](https://github.com/nodejs/node/pull/8932)
- [[`bf91035364`](https://github.com/nodejs/node/commit/bf91035364)] - **process**: fix handling of process.noDeprecation in emitWarning (James M Snell) [#8166](https://github.com/nodejs/node/pull/8166)
- [[`62b544290a`](https://github.com/nodejs/node/commit/62b544290a)] - **(SEMVER-MAJOR)** **process**: remove deprecated process.EventEmitter (cjihrig) [#6862](https://github.com/nodejs/node/pull/6862)
- [[`07dbf7313d`](https://github.com/nodejs/node/commit/07dbf7313d)] - **(SEMVER-MAJOR)** **promise**: hard deprecation for unhandled promise rejection (James M Snell) [#8217](https://github.com/nodejs/node/pull/8217)
- [[`ecf474ceba`](https://github.com/nodejs/node/commit/ecf474ceba)] - **(SEMVER-MAJOR)** **promise**: warn on unhandled rejections (Benjamin Gruenbaum) [#8217](https://github.com/nodejs/node/pull/8217)
- [[`1a9e247c79`](https://github.com/nodejs/node/commit/1a9e247c79)] - **(SEMVER-MAJOR)** **readline**: show completions only after 2nd TAB (Anna Henningsen) [#7754](https://github.com/nodejs/node/pull/7754)
- [[`8a87b29034`](https://github.com/nodejs/node/commit/8a87b29034)] - **(SEMVER-MAJOR)** **readline**: remove deprecated methods (cjihrig) [#6423](https://github.com/nodejs/node/pull/6423)
- [[`488d28d391`](https://github.com/nodejs/node/commit/488d28d391)] - **(SEMVER-MAJOR)** **repl**: deprecate unused function convertToContext (Prince J Wesley) [#7829](https://github.com/nodejs/node/pull/7829)
- [[`b2be04ac85`](https://github.com/nodejs/node/commit/b2be04ac85)] - **src**: refactor contextify (Franziska Hinkelmann) [#8909](https://github.com/nodejs/node/pull/8909)
- [[`e175188a94`](https://github.com/nodejs/node/commit/e175188a94)] - **src**: fix typo rval to value (Miguel Angel Asencio Hurtado) [#9023](https://github.com/nodejs/node/pull/9023)
- [[`1fda657cac`](https://github.com/nodejs/node/commit/1fda657cac)] - **(SEMVER-MAJOR)** **src**: update module version mismatch error message (James M Snell) [#8391](https://github.com/nodejs/node/pull/8391)
- [[`96933df2ff`](https://github.com/nodejs/node/commit/96933df2ff)] - **(SEMVER-MAJOR)** **src**: update NODE_MODULE_VERSION to 51 (Myles Borins) [#8808](https://github.com/nodejs/node/pull/8808)
- [[`b032f1cfc3`](https://github.com/nodejs/node/commit/b032f1cfc3)] - **(SEMVER-MAJOR)** **src**: no longer need to use std::tr1:: (Michaël Zasso) [#8317](https://github.com/nodejs/node/pull/8317)
- [[`ebad04326d`](https://github.com/nodejs/node/commit/ebad04326d)] - **src**: notify V8 for low memory when alloc fails (Anna Henningsen) [#8482](https://github.com/nodejs/node/pull/8482)
- [[`aed9792ff4`](https://github.com/nodejs/node/commit/aed9792ff4)] - **src**: provide allocation + nullptr check shortcuts (Anna Henningsen) [#8482](https://github.com/nodejs/node/pull/8482)
- [[`d2470d4dff`](https://github.com/nodejs/node/commit/d2470d4dff)] - **src**: pass desired return type to allocators (Anna Henningsen) [#8482](https://github.com/nodejs/node/pull/8482)
- [[`de946013c2`](https://github.com/nodejs/node/commit/de946013c2)] - **src**: add Malloc() size param + overflow detection (Anna Henningsen) [#8482](https://github.com/nodejs/node/pull/8482)
- [[`5bf94357a9`](https://github.com/nodejs/node/commit/5bf94357a9)] - **src**: remove unused StringValue macro parameters (Daniel Bevenius) [#7905](https://github.com/nodejs/node/pull/7905)
- [[`cc00be6ace`](https://github.com/nodejs/node/commit/cc00be6ace)] - **src**: fix -Wunused-result warning (Santiago Gimeno) [#8450](https://github.com/nodejs/node/pull/8450)
- [[`8e7cbe2546`](https://github.com/nodejs/node/commit/8e7cbe2546)] - **(SEMVER-MAJOR)** **src**: make debugger listen on 127.0.0.1 by default (Ben Noordhuis) [#8106](https://github.com/nodejs/node/pull/8106)
- [[`781713d5ef`](https://github.com/nodejs/node/commit/781713d5ef)] - **src**: remove unused isolate member (Ben Noordhuis) [#7334](https://github.com/nodejs/node/pull/7334)
- [[`de4161d367`](https://github.com/nodejs/node/commit/de4161d367)] - **src**: remove unused internals from node.cc (Anna Henningsen) [#7117](https://github.com/nodejs/node/pull/7117)
- [[`ac0665c908`](https://github.com/nodejs/node/commit/ac0665c908)] - **src**: fix ArrayBuffer size for zero fill flag (Anna Henningsen) [#7142](https://github.com/nodejs/node/pull/7142)
- [[`aac79dfd78`](https://github.com/nodejs/node/commit/aac79dfd78)] - **src**: use stack-allocated Environment instances (Ben Noordhuis) [#7090](https://github.com/nodejs/node/pull/7090)
- [[`58cec4e85b`](https://github.com/nodejs/node/commit/58cec4e85b)] - **src**: move env init logic into Environment class (Ben Noordhuis) [#7090](https://github.com/nodejs/node/pull/7090)
- [[`c3cd453cba`](https://github.com/nodejs/node/commit/c3cd453cba)] - **src**: make IsolateData creation explicit (Ben Noordhuis) [#7082](https://github.com/nodejs/node/pull/7082)
- [[`0301ce9f55`](https://github.com/nodejs/node/commit/0301ce9f55)] - **src**: move IsolateData out of Environment (Ben Noordhuis) [#7082](https://github.com/nodejs/node/pull/7082)
- [[`a3c5567eb4`](https://github.com/nodejs/node/commit/a3c5567eb4)] - **(SEMVER-MAJOR)** **src,win**: use correct exit code in old versions (yorkie) [#8204](https://github.com/nodejs/node/pull/8204)
- [[`2f05af4c06`](https://github.com/nodejs/node/commit/2f05af4c06)] - **(SEMVER-MAJOR)** **stream**: improve stream error messages (Italo A. Casas) [#8801](https://github.com/nodejs/node/pull/8801)
- [[`9983af0347`](https://github.com/nodejs/node/commit/9983af0347)] - **(SEMVER-MAJOR)** **stream**: improve unimplemented \_write() error (ratikesh9) [#7671](https://github.com/nodejs/node/pull/7671)
- [[`0cd0118334`](https://github.com/nodejs/node/commit/0cd0118334)] - **(SEMVER-MAJOR)** **stream**: 'data' argument on callback of Transform.\_flush() (Jesús Leganés Combarro "piranna) [#3708](https://github.com/nodejs/node/pull/3708)
- [[`a717be87a3`](https://github.com/nodejs/node/commit/a717be87a3)] - **test**: fix flaky test-timers-blocking-callback (Rich Trott) [#9198](https://github.com/nodejs/node/pull/9198)
- [[`5ba02bf5db`](https://github.com/nodejs/node/commit/5ba02bf5db)] - **test**: remove arbitrary timer (Rich Trott) [#9197](https://github.com/nodejs/node/pull/9197)
- [[`1518cc1e70`](https://github.com/nodejs/node/commit/1518cc1e70)] - **test**: remove duplicate required module (Rich Trott) [#9169](https://github.com/nodejs/node/pull/9169)
- [[`d62e7bd1f9`](https://github.com/nodejs/node/commit/d62e7bd1f9)] - **test**: add regression test for instanceof (Franziska Hinkelmann) [#9178](https://github.com/nodejs/node/pull/9178)
- [[`bb1e6064c1`](https://github.com/nodejs/node/commit/bb1e6064c1)] - **test**: rename target to exports for consistency (Daniel Bevenius) [#9135](https://github.com/nodejs/node/pull/9135)
- [[`8788d009f8`](https://github.com/nodejs/node/commit/8788d009f8)] - **test**: checking if error constructor is assert.AssertionError (larissayvette) [#9119](https://github.com/nodejs/node/pull/9119)
- [[`68157bd8b9`](https://github.com/nodejs/node/commit/68157bd8b9)] - **test**: remove unneeded escaping in template strings (Rich Trott) [#9112](https://github.com/nodejs/node/pull/9112)
- [[`0591362887`](https://github.com/nodejs/node/commit/0591362887)] - **test**: remove unused common.libDir (Rich Trott) [#9124](https://github.com/nodejs/node/pull/9124)
- [[`0f2f4d2425`](https://github.com/nodejs/node/commit/0f2f4d2425)] - **test**: fix flaky test-child-process-fork-dgram (Rich Trott) [#9098](https://github.com/nodejs/node/pull/9098)
- [[`47863a5837`](https://github.com/nodejs/node/commit/47863a5837)] - **test**: use npm sandbox in test-npm-install (João Reis) [#9079](https://github.com/nodejs/node/pull/9079)
- [[`67e2b92e21`](https://github.com/nodejs/node/commit/67e2b92e21)] - **test**: enable node-module-version/test.js with debug (Daniel Bevenius) [#9093](https://github.com/nodejs/node/pull/9093)
- [[`d5bdd65c6a`](https://github.com/nodejs/node/commit/d5bdd65c6a)] - **test**: move module out of fixture directory (Rich Trott) [#9022](https://github.com/nodejs/node/pull/9022)
- [[`0ad0e6addb`](https://github.com/nodejs/node/commit/0ad0e6addb)] - **test**: fix issues reported by Coverity (Eugene Ostroukhov) [#8870](https://github.com/nodejs/node/pull/8870)
- [[`aac93a5c64`](https://github.com/nodejs/node/commit/aac93a5c64)] - **test**: refactor test-file-\* (Jenna Vuong) [#8999](https://github.com/nodejs/node/pull/8999)
- [[`1bb1b3abe4`](https://github.com/nodejs/node/commit/1bb1b3abe4)] - **test**: fixes that do not affect performance (larissayvette) [#9011](https://github.com/nodejs/node/pull/9011)
- [[`96faba6ad8`](https://github.com/nodejs/node/commit/96faba6ad8)] - **test**: add cluster inspector debug port test (cjihrig) [#8958](https://github.com/nodejs/node/pull/8958)
- [[`7926886bf3`](https://github.com/nodejs/node/commit/7926886bf3)] - **test**: fix test-debug-signal-cluster.js flakyness (Julien Gilli) [#8568](https://github.com/nodejs/node/pull/8568)
- [[`99cfd53097`](https://github.com/nodejs/node/commit/99cfd53097)] - **(SEMVER-MAJOR)** **test**: test execFile/fork arg validation (Chuck Langford) [#7399](https://github.com/nodejs/node/pull/7399)
- [[`15cd45c6fc`](https://github.com/nodejs/node/commit/15cd45c6fc)] - **test**: fix tests for non-crypto builds (Anna Henningsen) [#7056](https://github.com/nodejs/node/pull/7056)
- [[`fea3070ec4`](https://github.com/nodejs/node/commit/fea3070ec4)] - **test**: add buffer testcase for resetting kZeroFill (Сковорода Никита Андреевич) [#7093](https://github.com/nodejs/node/pull/7093)
- [[`2cdd5ccef9`](https://github.com/nodejs/node/commit/2cdd5ccef9)] - **test,lib,benchmark**: match function names (Rich Trott) [#9113](https://github.com/nodejs/node/pull/9113)
- [[`827660e03e`](https://github.com/nodejs/node/commit/827660e03e)] - **tools**: enable ES2016 syntax support in ESLint (Michaël Zasso) [#9218](https://github.com/nodejs/node/pull/9218)
- [[`a83354a567`](https://github.com/nodejs/node/commit/a83354a567)] - **tools**: replace custom lint rule for getter/setter (Rich Trott) [#9194](https://github.com/nodejs/node/pull/9194)
- [[`3ab8be07cb`](https://github.com/nodejs/node/commit/3ab8be07cb)] - **tools**: fix release script on macOS 10.12 (Evan Lucas) [#8824](https://github.com/nodejs/node/pull/8824)
- [[`72fa9f5663`](https://github.com/nodejs/node/commit/72fa9f5663)] - **tools**: update ESLint to v3.8.0 (Rich Trott) [#9112](https://github.com/nodejs/node/pull/9112)
- [[`8ac29bd7c1`](https://github.com/nodejs/node/commit/8ac29bd7c1)] - **tools**: avoid let in for loops (jessicaquynh) [#9049](https://github.com/nodejs/node/pull/9049)
- [[`1a93e03a0e`](https://github.com/nodejs/node/commit/1a93e03a0e)] - **(SEMVER-MAJOR)** **tools**: do not disable ICU's transliteration (Michaël Zasso) [#8317](https://github.com/nodejs/node/pull/8317)
- [[`6a3dbdacd6`](https://github.com/nodejs/node/commit/6a3dbdacd6)] - **(SEMVER-MAJOR)** **udp**: remove ancient check (Saúl Ibarra Corretgé) [#8088](https://github.com/nodejs/node/pull/8088)
- [[`1afd7c166e`](https://github.com/nodejs/node/commit/1afd7c166e)] - **url**: fix building when using --without-intl (James M Snell) [#9041](https://github.com/nodejs/node/pull/9041)
- [[`a8ece149e2`](https://github.com/nodejs/node/commit/a8ece149e2)] - **(SEMVER-MINOR)** **url**: adding WHATWG URL support (James M Snell) [#7448](https://github.com/nodejs/node/pull/7448)
- [[`336b027411`](https://github.com/nodejs/node/commit/336b027411)] - **(SEMVER-MAJOR)** **url**: return valid file: urls fom url.format() (Rich Trott) [#7234](https://github.com/nodejs/node/pull/7234)
- [[`197a465280`](https://github.com/nodejs/node/commit/197a465280)] - **(SEMVER-MAJOR)** **zlib**: move constants into zlib.constants (James M Snell) [#7203](https://github.com/nodejs/node/pull/7203)

Windows 32-bit Installer: https://nodejs.org/dist/v7.0.0/node-v7.0.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v7.0.0/node-v7.0.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v7.0.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v7.0.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v7.0.0/node-v7.0.0.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v7.0.0/node-v7.0.0-darwin-x64.tar.gz \
Linux 32-bit Binary: https://nodejs.org/dist/v7.0.0/node-v7.0.0-linux-x86.tar.xz \
Linux 64-bit Binary: https://nodejs.org/dist/v7.0.0/node-v7.0.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v7.0.0/node-v7.0.0-linux-ppc64le.tar.xz \
SmartOS 32-bit Binary: https://nodejs.org/dist/v7.0.0/node-v7.0.0-sunos-x86.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v7.0.0/node-v7.0.0-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v7.0.0/node-v7.0.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v7.0.0/node-v7.0.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v7.0.0/node-v7.0.0.tar.gz \
Other release files: https://nodejs.org/dist/v7.0.0/ \
Documentation: https://nodejs.org/docs/v7.0.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

0977be0f261dd0e61433e875fbf0014773d1560b8bb5a882637e6e0289906d84  node-v7.0.0-aix-ppc64.tar.gz
2e94b2d3bae3ccac9c376379bc4d2243935c333bc1235b3d948d20d40ef26069  node-v7.0.0-darwin-x64.tar.gz
cd8f13980f935420bf0236cd7ed3c299c671b4d4f7efbf9f171de36f2904e740  node-v7.0.0-darwin-x64.tar.xz
f4792c0f40bd2eee2334424c9d65e4b4b30830507cca1b0cb88507d76044c0f7  node-v7.0.0-headers.tar.gz
8ef711371041f31faf0d0774e497a4cfc348323876613b9f5173fbbddac6b48e  node-v7.0.0-headers.tar.xz
375e897d16956b80e6c7b298f17d5b6b4a3bac1a50b297575bd50f34ce4c612c  node-v7.0.0-linux-arm64.tar.gz
9382f478f4e76f566ac34ca549ed5609f451d19df226839dd35cd86a0d654024  node-v7.0.0-linux-arm64.tar.xz
7378832687d7475074281f06dc98b2ddfc6c07cdc40e7ce4ea08a95347fd2292  node-v7.0.0-linux-armv6l.tar.gz
eb118194b7623f1389b9a0d315fd798a13fb638095b23069f6993532b53b1a1a  node-v7.0.0-linux-armv6l.tar.xz
dbcf05b10f71acf845d4c1357c627ebf2d401a11c80464cb44e475c5f0eca7e4  node-v7.0.0-linux-armv7l.tar.gz
34d65a4e27f31e716d9bdd5d62b0a0b3b2a7079613457b94b0a0b88f544bdc26  node-v7.0.0-linux-armv7l.tar.xz
5d615eefcbf76c25f760de7e94f4d4c3c1a5376978e595800313f524d9634fe5  node-v7.0.0-linux-ppc64le.tar.gz
57aae8873b09d33f362d72d5cf65fbd49faa6fb5e5c977a47f16a40f01238662  node-v7.0.0-linux-ppc64le.tar.xz
d5297fa59942a0807f785d279f311ebe8346cdedcf49b6aba6e1bf75d84d664c  node-v7.0.0-linux-ppc64.tar.gz
6d2ff843e90b279b9cd093c6b9e70c959d0b0ef9be10444a0b6596ae3ddc6101  node-v7.0.0-linux-ppc64.tar.xz
e92985271cf5efe6f0ebbf4768bcf7e7a98147da154dd5ca417d180e3c921262  node-v7.0.0-linux-s390x.tar.gz
89dd9ab01bcc3cd890cb9353cc64e6127bd3973617b5e90629c88dbf367c5b3e  node-v7.0.0-linux-s390x.tar.xz
4bc5ce31d2485f0b25e55bf8691d5dabf72c61f0c06b363728b70bfc0292ce7f  node-v7.0.0-linux-x64.tar.gz
78e042f9eb81eefed73c5e2fdccce89cbadbb4adb79de18585425be7ee8842f0  node-v7.0.0-linux-x64.tar.xz
96c61f58506725246ba246606c288809a5b7243f20ddfa464136978dd32d6917  node-v7.0.0-linux-x86.tar.gz
00526c2fbc4a84e2d00f0b90ae21618cfb8bb9ffd21d25d067bfc2a5ab37ca9c  node-v7.0.0-linux-x86.tar.xz
5d935d0e2e864920720623e629e2d4fb0d65238c110db5fbe71f73de8568c024  node-v7.0.0.pkg
4b6dcc2345b7b5229676c342d7757578973139166129cdb4c7dfddcaab3ef598  node-v7.0.0-sunos-x86.tar.gz
8663063dd1eb9d85a3f41a263f8a0769e02448266d5addf1eef8c8627c277b90  node-v7.0.0-sunos-x86.tar.xz
95ae4a37b8cf57816f96588ebe9ce5dbbb2c5452d177edd75fcfe6caf3b12b9b  node-v7.0.0.tar.gz
e16c3c76c2d6756bbfd711189cbdaf5676049b443e6817007bb537f243ca899a  node-v7.0.0.tar.xz
aa026c4e12769f39df8959374b90b481925bf44684d66bfca28d7f547e361ea6  node-v7.0.0-win-x64.7z
af130e7c5abb9be0ba96c35cfcd2002cc35535ddcda4d068b7647ce6d2ee1baf  node-v7.0.0-win-x64.zip
dd8a1049b64b121f025c8dd2870114c595300b48ec3efd18ff383fbbe142f273  node-v7.0.0-win-x86.7z
f60d01ccef511487887059f6b0f8df971b033e40862db53bbabf06278eced49c  node-v7.0.0-win-x86.zip
4729fbe4f31122b2aed25d6f82412dac255ca233ccb00c870707936e330f69d0  node-v7.0.0-x64.msi
75f9b61962884661fad941bb9c00bfc89dea78f4758cc661bb895f10d3c762d9  node-v7.0.0-x86.msi
68a150109422a9c8043fb5deae8432b38c932f0ae9dbeb6d917df1cf62693247  win-x64/node.exe
bf7efdcbd128a27aa5cc1cd03b093057ce8bbb9998ce3fd6a7371af6ebfcfb06  win-x64/node.lib
f2acc4ff4ea736ff1cfdb7e09c38fa0d0c056f4dfe2dd0b76d11530c67cf4c28  win-x64/node_pdb.7z
d787e5d9fb40d9aad86794cdcec04052be64b860c144472fa2907d80a012a48d  win-x64/node_pdb.zip
2ba6f11049660a65b0c2b761e2e8e1d150ca1460758a9163096a00541ce1687a  win-x86/node.exe
d29d974921a3a730911f0926d78c3af3db9955432355b8d20bcc53fae00235e3  win-x86/node.lib
38aa5d1f901cd7d3aeb33965de21cf576132665d46b749b5a3e6b5df7ec68ba2  win-x86/node_pdb.7z
4b0285e40131e5c8ffa10a70fb2ff6190a5cbab90ab0bcdcf9a372290aee70b7  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----
Comment: GPGTools - https://gpgtools.org

iQEcBAEBCAAGBQJYD2hbAAoJEHNBsVwHCHesM8UH/2GAeBw7PJl4HDO6rEdGSwER
DJ1Yb+4HJbfxTMkTiYQawfRkmL/5N5YCOWPfWuj8D7fmm7PvAC95Mq0X6PnI6/pt
h8iO2FVIkwog6Mpd5hB5NMLBy19mLKZ59ZnHUFEpXJwt1pZMimfqy2vAFGb4JIhj
gatsurF+ieqrqztxhVVyCqLoe1qLYi0VzaImzeKHcCGgdp2zkQtMmYHVH0H0/zS9
nozF1q8XSwEVbbPOSyjMkz5OvJlRftEEn711rkTXVRoiB5c+1pKl3E3j5GRR1M52
jGhdfEKfDpRpjcXRMKipLjg4eW4hZFfSnsGqWuuG/VCVX/fUx8dqKlkWWYr9sA0=
=GyFW
-----END PGP SIGNATURE-----

```
