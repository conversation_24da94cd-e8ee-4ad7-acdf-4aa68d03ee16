---
date: '2021-11-30T17:49:51.432Z'
category: release
title: Node v17.2.0 (Current)
layout: blog-post
author: <PERSON><PERSON><PERSON>
---

### Notable Changes

- \[[`06916490af`](https://github.com/nodejs/node/commit/06916490af)] - **(SEMVER-MINOR)** **async_hooks**: expose async_wrap providers (<PERSON>) [#40760](https://github.com/nodejs/node/pull/40760)
- \[[`371ee64c92`](https://github.com/nodejs/node/commit/371ee64c92)] - **(SEMVER-MINOR)** **deps**: update V8 to 9.6.180.14 (<PERSON><PERSON><PERSON>) [#40488](https://github.com/nodejs/node/pull/40488)
- \[[`675c210b04`](https://github.com/nodejs/node/commit/675c210b04)] - **(SEMVER-MINOR)** **lib**: add reason to <PERSON>bor<PERSON><PERSON><PERSON><PERSON> (<PERSON>) [#40807](https://github.com/nodejs/node/pull/40807)
- \[[`0de2850680`](https://github.com/nodejs/node/commit/0de2850680)] - **(SEMVER-MINOR)** **src**: add x509.fingerprint512 to crypto module (3nprob) [#39809](https://github.com/nodejs/node/pull/39809)
- \[[`fa9b5c35d2`](https://github.com/nodejs/node/commit/fa9b5c35d2)] - **stream**: deprecate thenable support (Antoine du Hamel) [#40860](https://github.com/nodejs/node/pull/40860)
- \[[`534409d4e7`](https://github.com/nodejs/node/commit/534409d4e7)] - **stream**: fix finished regression when working with legacy Stream (Matteo Collina) [#40858](https://github.com/nodejs/node/pull/40858)

### Commits

- \[[`48157c44c2`](https://github.com/nodejs/node/commit/48157c44c2)] - _**Revert**_ "**async_hooks**: merge resource_symbol with owner_symbol" (Darshan Sen) [#40741](https://github.com/nodejs/node/pull/40741)
- \[[`4a971f67e4`](https://github.com/nodejs/node/commit/4a971f67e4)] - **async_hooks**: eliminate require side effects (Stephen Belanger) [#40782](https://github.com/nodejs/node/pull/40782)
- \[[`06916490af`](https://github.com/nodejs/node/commit/06916490af)] - **(SEMVER-MINOR)** **async_hooks**: expose async_wrap providers (Rafael Gonzaga) [#40760](https://github.com/nodejs/node/pull/40760)
- \[[`65b33ba510`](https://github.com/nodejs/node/commit/65b33ba510)] - **build**: remove extraneous quotation marks from commit body (Rich Trott) [#40963](https://github.com/nodejs/node/pull/40963)
- \[[`05d652a555`](https://github.com/nodejs/node/commit/05d652a555)] - **build**: fix branch name for lint-md-dependencies update (Rich Trott) [#40924](https://github.com/nodejs/node/pull/40924)
- \[[`1482c4415f`](https://github.com/nodejs/node/commit/1482c4415f)] - **build**: fix `make` invocation in tools.yml (Rich Trott) [#40890](https://github.com/nodejs/node/pull/40890)
- \[[`69de8c8143`](https://github.com/nodejs/node/commit/69de8c8143)] - **build**: reset embedder string to "-node.0" (Michaël Zasso) [#40488](https://github.com/nodejs/node/pull/40488)
- \[[`e793331322`](https://github.com/nodejs/node/commit/e793331322)] - **build**: fix tools.yml errors (Rich Trott) [#40870](https://github.com/nodejs/node/pull/40870)
- \[[`51ac59b047`](https://github.com/nodejs/node/commit/51ac59b047)] - **build**: add GitHub Action to update tools modules (Rich Trott) [#40644](https://github.com/nodejs/node/pull/40644)
- \[[`a8cc8b6554`](https://github.com/nodejs/node/commit/a8cc8b6554)] - **crypto**: trim input for NETSCAPE_SPKI_b64_decode (Shelley Vohr) [#40757](https://github.com/nodejs/node/pull/40757)
- \[[`2979c58fb0`](https://github.com/nodejs/node/commit/2979c58fb0)] - **crypto**: throw errors in SignTraits::DeriveBits (Tobias Nießen) [#40796](https://github.com/nodejs/node/pull/40796)
- \[[`7f5931d03f`](https://github.com/nodejs/node/commit/7f5931d03f)] - **crypto**: fix build without scrypt (Martin Jansa) [#40613](https://github.com/nodejs/node/pull/40613)
- \[[`90f35fc329`](https://github.com/nodejs/node/commit/90f35fc329)] - **deps**: upgrade npm to 8.1.4 (npm team) [#40865](https://github.com/nodejs/node/pull/40865)
- \[[`d461603d71`](https://github.com/nodejs/node/commit/d461603d71)] - **deps**: V8: cherry-pick cced52a97ee9 (Ray Wang) [#40656](https://github.com/nodejs/node/pull/40656)
- \[[`d6ae50ff96`](https://github.com/nodejs/node/commit/d6ae50ff96)] - **deps**: V8: cherry-pick 7ae0b77628f6 (Ray Wang) [#40882](https://github.com/nodejs/node/pull/40882)
- \[[`e60053deee`](https://github.com/nodejs/node/commit/e60053deee)] - **deps**: V8: cherry-pick 2a0bc36dec12 (Michaël Zasso) [#40178](https://github.com/nodejs/node/pull/40178)
- \[[`94acaae3d4`](https://github.com/nodejs/node/commit/94acaae3d4)] - **deps**: V8: patch jinja2 for Python 3.10 compat (Michaël Zasso) [#40296](https://github.com/nodejs/node/pull/40296)
- \[[`37c9828d39`](https://github.com/nodejs/node/commit/37c9828d39)] - **deps**: disable trap handler for Windows cross-compiler (Michaël Zasso) [#40488](https://github.com/nodejs/node/pull/40488)
- \[[`dfb97fb501`](https://github.com/nodejs/node/commit/dfb97fb501)] - **deps**: silence irrelevant V8 warning (Michaël Zasso) [#38990](https://github.com/nodejs/node/pull/38990)
- \[[`9ecf4be0c2`](https://github.com/nodejs/node/commit/9ecf4be0c2)] - **deps**: silence irrelevant V8 warning (Michaël Zasso) [#37587](https://github.com/nodejs/node/pull/37587)
- \[[`82a8736dec`](https://github.com/nodejs/node/commit/82a8736dec)] - **deps**: fix V8 build issue with inline methods (Jiawen Geng) [#38807](https://github.com/nodejs/node/pull/38807)
- \[[`eab5ef5505`](https://github.com/nodejs/node/commit/eab5ef5505)] - **deps**: make v8.h compatible with VS2015 (Joao Reis) [#32116](https://github.com/nodejs/node/pull/32116)
- \[[`d6e5dd56ff`](https://github.com/nodejs/node/commit/d6e5dd56ff)] - **deps**: V8: forward declaration of `Rtl*FunctionTable` (Refael Ackermann) [#32116](https://github.com/nodejs/node/pull/32116)
- \[[`29a6e15480`](https://github.com/nodejs/node/commit/29a6e15480)] - **deps**: V8: patch register-arm64.h (Refael Ackermann) [#32116](https://github.com/nodejs/node/pull/32116)
- \[[`1ff83c4945`](https://github.com/nodejs/node/commit/1ff83c4945)] - **deps**: V8: un-cherry-pick bd019bd (Refael Ackermann) [#32116](https://github.com/nodejs/node/pull/32116)
- \[[`371ee64c92`](https://github.com/nodejs/node/commit/371ee64c92)] - **(SEMVER-MINOR)** **deps**: update V8 to 9.6.180.14 (Michaël Zasso) [#40488](https://github.com/nodejs/node/pull/40488)
- \[[`6506a42c16`](https://github.com/nodejs/node/commit/6506a42c16)] - **deps**: V8: cherry-pick cced52a97ee9 (Ray Wang) [#40656](https://github.com/nodejs/node/pull/40656)
- \[[`a612ecb42f`](https://github.com/nodejs/node/commit/a612ecb42f)] - **deps**: V8: cherry-pick 7ae0b77628f6 (Ray Wang) [#40882](https://github.com/nodejs/node/pull/40882)
- \[[`b46184df5e`](https://github.com/nodejs/node/commit/b46184df5e)] - **deps**: update to uvwasi 0.0.12 (Colin Ihrig) [#40847](https://github.com/nodejs/node/pull/40847)
- \[[`fa37428472`](https://github.com/nodejs/node/commit/fa37428472)] - **deps**: add -fno-strict-aliasing flag to libuv (Daniel Bevenius) [#40631](https://github.com/nodejs/node/pull/40631)
- \[[`9711ccfe08`](https://github.com/nodejs/node/commit/9711ccfe08)] - **deps**: upgrade npm to 8.1.3 (npm team) [#40726](https://github.com/nodejs/node/pull/40726)
- \[[`8e525634c6`](https://github.com/nodejs/node/commit/8e525634c6)] - **deps**: update ICU to 70.1 (Michaël Zasso) [#40658](https://github.com/nodejs/node/pull/40658)
- \[[`3bfc9f5b47`](https://github.com/nodejs/node/commit/3bfc9f5b47)] - **doc**: add information on suppressing initial break in debugger (Rich Trott) [#40960](https://github.com/nodejs/node/pull/40960)
- \[[`8966ab3c57`](https://github.com/nodejs/node/commit/8966ab3c57)] - **doc**: improve grammar in API description (Tobias Nießen) [#40959](https://github.com/nodejs/node/pull/40959)
- \[[`130777a61c`](https://github.com/nodejs/node/commit/130777a61c)] - **doc**: update BUILDING.md section on running tests (Rich Trott) [#40933](https://github.com/nodejs/node/pull/40933)
- \[[`6e9b6652e5`](https://github.com/nodejs/node/commit/6e9b6652e5)] - **doc**: remove experimental abortcontroller flag (FrankQiu) [#38968](https://github.com/nodejs/node/pull/38968)
- \[[`b92416ff02`](https://github.com/nodejs/node/commit/b92416ff02)] - **doc**: fix spelling of 'WebAssembly' (Geoffrey Booth) [#40785](https://github.com/nodejs/node/pull/40785)
- \[[`cf495a6293`](https://github.com/nodejs/node/commit/cf495a6293)] - **doc**: clarify more optional parameters in node-api (Michael Dawson) [#40888](https://github.com/nodejs/node/pull/40888)
- \[[`694012b392`](https://github.com/nodejs/node/commit/694012b392)] - **doc**: define "types", "deno" community conditions (Guy Bedford) [#40708](https://github.com/nodejs/node/pull/40708)
- \[[`4c47b0150b`](https://github.com/nodejs/node/commit/4c47b0150b)] - **doc**: document optional params in napi_get_cb_info (Michael Dawson) [#40821](https://github.com/nodejs/node/pull/40821)
- \[[`dfdf68f4d0`](https://github.com/nodejs/node/commit/dfdf68f4d0)] - **doc**: improve README.md lede section (Rich Trott) [#40837](https://github.com/nodejs/node/pull/40837)
- \[[`9c200e1de4`](https://github.com/nodejs/node/commit/9c200e1de4)] - **doc**: add pref to using draft PR versus WIP label (Michael Dawson) [#40824](https://github.com/nodejs/node/pull/40824)
- \[[`fe2cd09750`](https://github.com/nodejs/node/commit/fe2cd09750)] - **doc**: fix `added:` info for `Readable.fromWeb()` (Luigi Pinca) [#40820](https://github.com/nodejs/node/pull/40820)
- \[[`c91a9ab095`](https://github.com/nodejs/node/commit/c91a9ab095)] - **doc**: tweak guidance for modules in core (Michael Dawson) [#40601](https://github.com/nodejs/node/pull/40601)
- \[[`2ea08e9b55`](https://github.com/nodejs/node/commit/2ea08e9b55)] - **doc**: claim ABI version for Electron 18 (Keeley Hammond) [#40768](https://github.com/nodejs/node/pull/40768)
- \[[`8166b07ddc`](https://github.com/nodejs/node/commit/8166b07ddc)] - **doc**: fix transform stream example (Evan Lucas) [#40777](https://github.com/nodejs/node/pull/40777)
- \[[`5ceb06cddf`](https://github.com/nodejs/node/commit/5ceb06cddf)] - **doc**: fix linter-enforced formatting in crypto.md (Mohammed Keyvanzadeh) [#40780](https://github.com/nodejs/node/pull/40780)
- \[[`d3070d8eea`](https://github.com/nodejs/node/commit/d3070d8eea)] - **doc**: fix corepack grammar for `--force` flag (Steven) [#40762](https://github.com/nodejs/node/pull/40762)
- \[[`9271f23e3a`](https://github.com/nodejs/node/commit/9271f23e3a)] - **doc**: update maintaining ICU guide (Michaël Zasso) [#40658](https://github.com/nodejs/node/pull/40658)
- \[[`20d7d657bb`](https://github.com/nodejs/node/commit/20d7d657bb)] - **doc**: clarify getAuthTag with authTagLength (Tobias Nießen) [#40713](https://github.com/nodejs/node/pull/40713)
- \[[`75288fbc6b`](https://github.com/nodejs/node/commit/75288fbc6b)] - **doc**: fix order of announce work (Michael Dawson) [#40725](https://github.com/nodejs/node/pull/40725)
- \[[`429915aa6c`](https://github.com/nodejs/node/commit/429915aa6c)] - **doc**: add initial list of technical priorities (Michael Dawson) [#40235](https://github.com/nodejs/node/pull/40235)
- \[[`a5a1691514`](https://github.com/nodejs/node/commit/a5a1691514)] - **fs**: nullish coalescing to respect zero positional reads (Omar El-Mihilmy) [#40716](https://github.com/nodejs/node/pull/40716)
- \[[`bddb4c69b7`](https://github.com/nodejs/node/commit/bddb4c69b7)] - **http**: add missing initialization (Michael Dawson) [#40555](https://github.com/nodejs/node/pull/40555)
- \[[`80ce97f514`](https://github.com/nodejs/node/commit/80ce97f514)] - **http**: change totalSocketCount only on socket creation/close (Subhi Al Hasan) [#40572](https://github.com/nodejs/node/pull/40572)
- \[[`675c210b04`](https://github.com/nodejs/node/commit/675c210b04)] - **(SEMVER-MINOR)** **lib**: add reason to AbortSignal (James M Snell) [#40807](https://github.com/nodejs/node/pull/40807)
- \[[`b614b17525`](https://github.com/nodejs/node/commit/b614b17525)] - _**Revert**_ "**lib**: use helper for readability" (Darshan Sen) [#40741](https://github.com/nodejs/node/pull/40741)
- \[[`10a842d2d1`](https://github.com/nodejs/node/commit/10a842d2d1)] - **lib**: fix typos in lib code comments (Yoshiki) [#40792](https://github.com/nodejs/node/pull/40792)
- \[[`3ec78d1570`](https://github.com/nodejs/node/commit/3ec78d1570)] - **meta**: add feature request label for issue template (Mestery) [#40970](https://github.com/nodejs/node/pull/40970)
- \[[`9c897b69a1`](https://github.com/nodejs/node/commit/9c897b69a1)] - **meta**: update AUTHORS (Node.js GitHub Bot) [#40900](https://github.com/nodejs/node/pull/40900)
- \[[`dfc6fc987a`](https://github.com/nodejs/node/commit/dfc6fc987a)] - **meta**: update name and email (Viero Fernando) [#40848](https://github.com/nodejs/node/pull/40848)
- \[[`813cf746a8`](https://github.com/nodejs/node/commit/813cf746a8)] - **meta**: update AUTHORS (Node.js GitHub Bot) [#40809](https://github.com/nodejs/node/pull/40809)
- \[[`e451ec9ec1`](https://github.com/nodejs/node/commit/e451ec9ec1)] - **meta**: edit GOVERNANCE.md for minor updates (Rich Trott) [#40798](https://github.com/nodejs/node/pull/40798)
- \[[`2536be7528`](https://github.com/nodejs/node/commit/2536be7528)] - **meta**: update AUTHORS (Node.js GitHub Bot) [#40748](https://github.com/nodejs/node/pull/40748)
- \[[`3c8aa211cd`](https://github.com/nodejs/node/commit/3c8aa211cd)] - **module**: import assertions improvements (Geoffrey Booth) [#40785](https://github.com/nodejs/node/pull/40785)
- \[[`3a4f3873be`](https://github.com/nodejs/node/commit/3a4f3873be)] - **process**: support hrtime in the snapshot (Joyee Cheung) [#40649](https://github.com/nodejs/node/pull/40649)
- \[[`1555dbdddc`](https://github.com/nodejs/node/commit/1555dbdddc)] - **repl**: fix and extend require/import tab complete (Mestery) [#40216](https://github.com/nodejs/node/pull/40216)
- \[[`c2259c974b`](https://github.com/nodejs/node/commit/c2259c974b)] - **src**: reset error struct if error code is napi_ok (JckXia) [#40552](https://github.com/nodejs/node/pull/40552)
- \[[`3be49d6853`](https://github.com/nodejs/node/commit/3be49d6853)] - **src**: prevent extra copies of `TimerWrap::TimerCb` (Darshan Sen) [#40665](https://github.com/nodejs/node/pull/40665)
- \[[`0de2850680`](https://github.com/nodejs/node/commit/0de2850680)] - **(SEMVER-MINOR)** **src**: add x509.fingerprint512 to crypto module (3nprob) [#39809](https://github.com/nodejs/node/pull/39809)
- \[[`924d29e88f`](https://github.com/nodejs/node/commit/924d29e88f)] - **src**: add and fix some preprocessor comments (Tobias Nießen) [#40701](https://github.com/nodejs/node/pull/40701)
- \[[`acbd8220a7`](https://github.com/nodejs/node/commit/acbd8220a7)] - **src,crypto**: use `ByteSource::ToBuffer()` in `crypto_dh` (Darshan Sen) [#40903](https://github.com/nodejs/node/pull/40903)
- \[[`82b422663d`](https://github.com/nodejs/node/commit/82b422663d)] - **src,crypto**: remove `AllocatedBuffer`s from `crypto_spkac` (Darshan Sen) [#40752](https://github.com/nodejs/node/pull/40752)
- \[[`415b42fbdf`](https://github.com/nodejs/node/commit/415b42fbdf)] - **src,crypto**: refactor `crypto_tls.*` (Darshan Sen) [#40675](https://github.com/nodejs/node/pull/40675)
- \[[`88b57bc9d3`](https://github.com/nodejs/node/commit/88b57bc9d3)] - **src,doc**: add SyntaxError napi support (Idan Attias) [#40736](https://github.com/nodejs/node/pull/40736)
- \[[`70bd90e039`](https://github.com/nodejs/node/commit/70bd90e039)] - **stream**: stricter isReadableNodeStream (Robert Nagy) [#40941](https://github.com/nodejs/node/pull/40941)
- \[[`86d1c0b19d`](https://github.com/nodejs/node/commit/86d1c0b19d)] - **stream**: drain Transform with 0 highWaterMark (Robert Nagy) [#40947](https://github.com/nodejs/node/pull/40947)
- \[[`fa9b5c35d2`](https://github.com/nodejs/node/commit/fa9b5c35d2)] - **stream**: deprecate thenable support (Antoine du Hamel) [#40860](https://github.com/nodejs/node/pull/40860)
- \[[`dc99ae3bd6`](https://github.com/nodejs/node/commit/dc99ae3bd6)] - **stream**: fix the ReadableStreamBYOBReader error message (James M Snell) [#40846](https://github.com/nodejs/node/pull/40846)
- \[[`650c9bd022`](https://github.com/nodejs/node/commit/650c9bd022)] - **stream**: pipeline with end option (Robert Nagy) [#40886](https://github.com/nodejs/node/pull/40886)
- \[[`534409d4e7`](https://github.com/nodejs/node/commit/534409d4e7)] - **stream**: fix finished regression when working with legacy Stream (Matteo Collina) [#40858](https://github.com/nodejs/node/pull/40858)
- \[[`16ee8423d6`](https://github.com/nodejs/node/commit/16ee8423d6)] - **stream**: pipeline should drain empty readable (Robert Nagy) [#40654](https://github.com/nodejs/node/pull/40654)
- \[[`7d127d2fc1`](https://github.com/nodejs/node/commit/7d127d2fc1)] - **stream**: fix non readable Duplex readableAborted (Robert Nagy) [#40801](https://github.com/nodejs/node/pull/40801)
- \[[`7c4f9a34ae`](https://github.com/nodejs/node/commit/7c4f9a34ae)] - **stream**: allow calling callback before promise (Robert Nagy) [#40772](https://github.com/nodejs/node/pull/40772)
- \[[`62171eb516`](https://github.com/nodejs/node/commit/62171eb516)] - **test**: mark test-fs-watch-non-recursive flaky on Windows (Luigi Pinca) [#40916](https://github.com/nodejs/node/pull/40916)
- \[[`ae12a609a5`](https://github.com/nodejs/node/commit/ae12a609a5)] - **test**: improve test coverage of readline/promises (Yoshiki Kurihara) [#40876](https://github.com/nodejs/node/pull/40876)
- \[[`4288c6a26e`](https://github.com/nodejs/node/commit/4288c6a26e)] - **test**: deflake cluster-concurrent-disconnect (Luigi Pinca) [#40877](https://github.com/nodejs/node/pull/40877)
- \[[`009ab4d9bb`](https://github.com/nodejs/node/commit/009ab4d9bb)] - **test**: deflake fs-promises-watch (Luigi Pinca) [#40863](https://github.com/nodejs/node/pull/40863)
- \[[`522a30f469`](https://github.com/nodejs/node/commit/522a30f469)] - **test**: fix argument order in assertion (Luigi Pinca) [#40842](https://github.com/nodejs/node/pull/40842)
- \[[`b190b0e77b`](https://github.com/nodejs/node/commit/b190b0e77b)] - **test**: deflake http2-pipe-named-pipe (Luigi Pinca) [#40842](https://github.com/nodejs/node/pull/40842)
- \[[`c485460793`](https://github.com/nodejs/node/commit/c485460793)] - **test**: use descriptive name for destination file (Luigi Pinca) [#40842](https://github.com/nodejs/node/pull/40842)
- \[[`43e86508ae`](https://github.com/nodejs/node/commit/43e86508ae)] - **test**: add AsyncLocalStorage tests using udp, tcp and tls sockets (Darshan Sen) [#40741](https://github.com/nodejs/node/pull/40741)
- \[[`896073be47`](https://github.com/nodejs/node/commit/896073be47)] - **test**: deflake child-process-pipe-dataflow (Luigi Pinca) [#40838](https://github.com/nodejs/node/pull/40838)
- \[[`150c0f1b76`](https://github.com/nodejs/node/commit/150c0f1b76)] - **test**: add tests for invalid UTF-8 (git-srinivas) [#40351](https://github.com/nodejs/node/pull/40351)
- \[[`3d896231cd`](https://github.com/nodejs/node/commit/3d896231cd)] - **test**: fix flaky test-policy-integrity (Rich Trott) [#40763](https://github.com/nodejs/node/pull/40763)
- \[[`dc9e68899d`](https://github.com/nodejs/node/commit/dc9e68899d)] - **test**: add semicolons for linter update (Rich Trott) [#40720](https://github.com/nodejs/node/pull/40720)
- \[[`747247bc59`](https://github.com/nodejs/node/commit/747247bc59)] - **tools**: update gyp-next to v0.10.1 (Michaël Zasso) [#40934](https://github.com/nodejs/node/pull/40934)
- \[[`6ebbd0d9dc`](https://github.com/nodejs/node/commit/6ebbd0d9dc)] - **tools**: fix commit-lint GH Actions CI (Antoine du Hamel) [#40845](https://github.com/nodejs/node/pull/40845)
- \[[`0201f3f604`](https://github.com/nodejs/node/commit/0201f3f604)] - **tools**: ping TSC members identified as inactive (Rich Trott) [#40915](https://github.com/nodejs/node/pull/40915)
- \[[`22265e3fb6`](https://github.com/nodejs/node/commit/22265e3fb6)] - **tools**: update lint-md-dependencies to rollup\@2.60.1 (Node.js GitHub Bot) [#40929](https://github.com/nodejs/node/pull/40929)
- \[[`3d593aa4ed`](https://github.com/nodejs/node/commit/3d593aa4ed)] - **tools**: update lint-md dependencies (Rich Trott) [#40894](https://github.com/nodejs/node/pull/40894)
- \[[`e674f61720`](https://github.com/nodejs/node/commit/e674f61720)] - **tools**: update ESLint to 8.3.0 (Luigi Pinca) [#40917](https://github.com/nodejs/node/pull/40917)
- \[[`878ba91bf6`](https://github.com/nodejs/node/commit/878ba91bf6)] - **tools**: add find-inactive-tsc (Rich Trott) [#40884](https://github.com/nodejs/node/pull/40884)
- \[[`d97ad3080f`](https://github.com/nodejs/node/commit/d97ad3080f)] - **tools**: disable trap handler for Windows cross-compiler (Michaël Zasso) [#40488](https://github.com/nodejs/node/pull/40488)
- \[[`e3f8988118`](https://github.com/nodejs/node/commit/e3f8988118)] - **tools**: update V8 gypfiles for 9.6 (Michaël Zasso) [#40488](https://github.com/nodejs/node/pull/40488)
- \[[`de4d0180dc`](https://github.com/nodejs/node/commit/de4d0180dc)] - **tools**: update @babel/eslint-parser to 7.16.3 (Node.js GitHub Bot) [#40889](https://github.com/nodejs/node/pull/40889)
- \[[`727b34ec95`](https://github.com/nodejs/node/commit/727b34ec95)] - **tools**: only validate first commit message of a PR (Antoine du Hamel) [#40740](https://github.com/nodejs/node/pull/40740)
- \[[`5b08e908ea`](https://github.com/nodejs/node/commit/5b08e908ea)] - **tools**: update babel-eslint-parser to 7.16.0 (Rich Trott) [#40720](https://github.com/nodejs/node/pull/40720)
- \[[`30623c283a`](https://github.com/nodejs/node/commit/30623c283a)] - **tools**: improve update scripts (Rich Trott) [#40644](https://github.com/nodejs/node/pull/40644)

Windows 32-bit Installer: https://nodejs.org/dist/v17.2.0/node-v17.2.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v17.2.0/node-v17.2.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v17.2.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v17.2.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v17.2.0/node-v17.2.0.pkg \
macOS Apple Silicon 64-bit Binary: https://nodejs.org/dist/v17.2.0/node-v17.2.0-darwin-arm64.tar.gz \
macOS Intel 64-bit Binary: https://nodejs.org/dist/v17.2.0/node-v17.2.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v17.2.0/node-v17.2.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v17.2.0/node-v17.2.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v17.2.0/node-v17.2.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v17.2.0/node-v17.2.0-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v17.2.0/node-v17.2.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v17.2.0/node-v17.2.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v17.2.0/node-v17.2.0.tar.gz \
Other release files: https://nodejs.org/dist/v17.2.0/ \
Documentation: https://nodejs.org/docs/v17.2.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

1fcfaad85f298d45e8e8c86bc007b536117583040ba1a9e3d2323152dcf6a0d3  node-v17.2.0-aix-ppc64.tar.gz
436d2c069069302615d24fd663494277eca183af25776fb4ce40008422666c6e  node-v17.2.0-darwin-arm64.tar.gz
bd2f1ccf2778bdb61fb15c040e4b61c7a84cf6f52a9e6da65191a7ca1b3f8a2f  node-v17.2.0-darwin-arm64.tar.xz
6fc9e1c428cc6427d4926e8895dfc5c476718b2d02c3c5c1b6aa845c13602caa  node-v17.2.0-darwin-x64.tar.gz
f8bbfb2b09d09f4d72b2a36ad2619f4db2affcee511c7cf3056702382276d7e8  node-v17.2.0-darwin-x64.tar.xz
1fa4dc7704e6bc0304b3e3f0699cc212670cd8bb0f1aba6dfc4a1a49f2c7029d  node-v17.2.0-headers.tar.gz
78b99be2050144d443c164bea48ddae1056f7793ae3326274db202868a910d05  node-v17.2.0-headers.tar.xz
d5fbc40f78be71007960b8490d9dc605bc006c2986d44812b4071116e6499ca4  node-v17.2.0-linux-arm64.tar.gz
20c2c8a90422fe28aec344a1f4eb5ac4fee84f2411e4e5a86e67624ca3d824fd  node-v17.2.0-linux-arm64.tar.xz
d342932c01f4bbea67c83cfb7432d07e0653da1d2f9644e684be5c731caa1d2c  node-v17.2.0-linux-armv7l.tar.gz
2180ded08087600c45673f1eca43fe98f183e55926758a0d4536f02e21da118b  node-v17.2.0-linux-armv7l.tar.xz
609f3095d742eca2d22ec2afa3227f8872ef9295cbe2577f58e4a519bdf3afc2  node-v17.2.0-linux-ppc64le.tar.gz
2875c4a04114dd7b57b45db74016330b127cd5ad0e834e24b49983514d8cf530  node-v17.2.0-linux-ppc64le.tar.xz
6ea6a9bdd696300edda20277f39c76dc0984ef57aaa1526fa1d8fab4b297d503  node-v17.2.0-linux-s390x.tar.gz
5c7668005390617faf72d29354581542e61e1555715633ae0f68bec4ffe95a83  node-v17.2.0-linux-s390x.tar.xz
a26491670f11d6ef4f919d3c2678fe65292c9e1d7e9184ec551a011816d92f0d  node-v17.2.0-linux-x64.tar.gz
0b5a6db351f31edf5282c63be7b923e40064ac6d54b5222fdd419ab8f1bedf61  node-v17.2.0-linux-x64.tar.xz
fe9b0ba29cda7605b6596c407e8f7f8e64314ef58ba19bc89199962ec913d2f8  node-v17.2.0.pkg
ce690b637fd2e19c4961d6e726dab4797be631420bcbb76a90747779a45f0441  node-v17.2.0.tar.gz
2b47cc7b5ec189d7b637454732f36f8d3c2c0ef81bec3c278b566f67159e659a  node-v17.2.0.tar.xz
f8b8a54eadcbcf6b3858c6740e5b353cc7d004bed5cb5cd3ecd015a7956b2380  node-v17.2.0-win-x64.7z
a9ee91709e5c4c3812e5897cf2af471037528b16dbd766908217af592d0e9545  node-v17.2.0-win-x64.zip
b75a5b63c841b6fead6ded38dea7eb24a57c2e58bad1c62cc0f11bc6170c3e83  node-v17.2.0-win-x86.7z
ef224258f68a0d68dc0b564b0303ab3f2288db8ff508a4666c9b100b164b1812  node-v17.2.0-win-x86.zip
79483d60fa471d77dd6dc6c51e52c31f05bc1cdf5200f8b79acb5b9ac68df6fb  node-v17.2.0-x64.msi
4e172c90a8b452bfadec85e591d7cc078e38f4d91b5dfcfadfa0a0be2d43eabc  node-v17.2.0-x86.msi
0a8c547f3154536627e8ed03c9ad1ee634512f8b14a134042cd037b16d07120c  win-x64/node.exe
d745f5c061f7e61725a94afe4ea98063e2306729a2113c9d17ec137ba42c8071  win-x64/node.lib
0cac424d27220c9fc4168699b11308cb902d1df446d87dbaeafe7a301203fff2  win-x64/node_pdb.7z
3c6da400bf115a6e24fd5dc67e199679605736b04cf7f1acb5e4b764fe1b32bc  win-x64/node_pdb.zip
a94497d94b3f89f0192501e3d239e04f6bb0bb2274971564b5607b41f399e2a5  win-x86/node.exe
da719c010c6937643f59a4ec1d616243a9c12721624a319ab7f79dc1aca924be  win-x86/node.lib
e5908d13b5ab122362a5896a4149955932e70631df3d1cfcbebd5016d42ec2ae  win-x86/node_pdb.7z
9e5c761c4d9a4062ae1911a1ac6ba1be45f0ec5a31b3dd976a183441f95b7b08  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEj8yhP+8dDC6RAI4Jdw96mlrhVgAFAmGmY9gACgkQdw96mlrh
VgApmw//Yd6z5OGix4alQf8uNX5XiQj1TpUkYlj/jg8yoJu01p/onSWwp5z5dv4V
ccDlRAi7JqlJYfOM2rABrUcIME+0SdrXZnudnaK2DeVU1k2fVfzOd0YK3aiGSuS7
bmo0H3HzwKQTfV+GrZU4LTPAg5PIE8Rf7oDfxUHgzXx27Fkq/5MABwVWyvpElF6j
d4ac+ZYL8alAtMIHFZGqNWc6TyxWsUaiiKWoOq07TMKAow8TPYECie5wvF1mwIfp
/Gvl7UTPvSv/X4IZiQA/fHrMKnRmS8MZ+OtTdl1dSjTPsOJGrNTpROUmjW++z2Wy
fBxbEDdUcWpLEJJhj3WpKJQ8ks+zwIWJp3TzMrfkIEu1Jk3S8bKfIDTgo3Byse5V
Bd5VBy0TQdS4EcM92YCSlrua16Rm6V6vuJbgtrVS2mOaxxpeARcWxiwLjyBYmDwx
Dbfm48QCFUqiKtyDRVIB8zeNpmAIdeCNPpQ+h41ZQ2EJCDJMon8/YeOX+w3Wdk32
VdBF3aJlpHoxCarySzuosM6DpoeyzbQco9SIjAXXms6G5j209E17g/vUCerfsLAY
qtw4MFMjFwxL7+bp5rEAN3163GeOkk/4quY3P/fPMTqIUD6sc8sUc0/Omm+n55O8
x9f36walNtKnwKstIKT0EoVuKH1uhI8o64uhnoLqfpMXKQlcdJE=
=nP5D
-----END PGP SIGNATURE-----

```
