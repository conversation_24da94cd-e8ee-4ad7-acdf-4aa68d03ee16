---
date: '2021-03-17T22:31:55.878Z'
category: release
title: Node v15.12.0 (Current)
layout: blog-post
author: <PERSON>
---

### Notable Changes

- **crypto**:
  - add optional callback to crypto.sign and crypto.verify (<PERSON><PERSON>) [#37500](https://github.com/nodejs/node/pull/37500)
  - support JWK objects in create\*Key (<PERSON><PERSON>) [#37254](https://github.com/nodejs/node/pull/37254)
- **deps**:
  - switch openssl to quictls/openssl (<PERSON>) [#37601](https://github.com/nodejs/node/pull/37601)
  - update to cjs-module-lexer@1.1.0 (<PERSON>) [#37712](https://github.com/nodejs/node/pull/37712)
- **fs**:
  - improve fsPromises writeFile performance (<PERSON><PERSON><PERSON>) [#37610](https://github.com/nodejs/node/pull/37610)
  - improve fsPromises readFile performance (<PERSON><PERSON><PERSON>) [#37608](https://github.com/nodejs/node/pull/37608)
- **lib**:
  - implement AbortSignal.abort() (<PERSON> Snell) [#37693](https://github.com/nodejs/node/pull/37693)
- **node-api**:
  - define version 8 (Gabriel Schulhof) [#37652](https://github.com/nodejs/node/pull/37652)
- **worker**:
  - add setEnvironmentData/getEnvironmentData (James M Snell) [#37486](https://github.com/nodejs/node/pull/37486)

### Commits

- [[`44514600b2`](https://github.com/nodejs/node/commit/44514600b2)] - **assert,util**: fix commutativity edge case (Ruben Bridgewater) [#37711](https://github.com/nodejs/node/pull/37711)
- [[`8666d777cc`](https://github.com/nodejs/node/commit/8666d777cc)] - **benchmark**: add benchmark for fsPromises.writeFile (Nitzan Uziely) [#37610](https://github.com/nodejs/node/pull/37610)
- [[`e9028eb646`](https://github.com/nodejs/node/commit/e9028eb646)] - **cluster**: restructure to same prototype for cluster child (Yash Ladha) [#36610](https://github.com/nodejs/node/pull/36610)
- [[`8e1257e26d`](https://github.com/nodejs/node/commit/8e1257e26d)] - **cluster**: clarify construct Handle (Jackson Tian) [#37385](https://github.com/nodejs/node/pull/37385)
- [[`341ee31e15`](https://github.com/nodejs/node/commit/341ee31e15)] - **crypto**: reconcile duplicated code (James M Snell) [#37704](https://github.com/nodejs/node/pull/37704)
- [[`a2d08d5dfd`](https://github.com/nodejs/node/commit/a2d08d5dfd)] - **crypto**: add internal error codes (Darshan Sen) [#37650](https://github.com/nodejs/node/pull/37650)
- [[`922f2f0eb2`](https://github.com/nodejs/node/commit/922f2f0eb2)] - **(SEMVER-MINOR)** **crypto**: add optional callback to crypto.sign and crypto.verify (Filip Skokan) [#37500](https://github.com/nodejs/node/pull/37500)
- [[`55e522ca23`](https://github.com/nodejs/node/commit/55e522ca23)] - **(SEMVER-MINOR)** **crypto**: support JWK objects in create\*Key (Filip Skokan) [#37254](https://github.com/nodejs/node/pull/37254)
- [[`33180fad81`](https://github.com/nodejs/node/commit/33180fad81)] - **crypto**: add separate error for INVALID_KEY_TYPE (Darshan Sen) [#37555](https://github.com/nodejs/node/pull/37555)
- [[`d81b9af1fc`](https://github.com/nodejs/node/commit/d81b9af1fc)] - **crypto**: improve randomUUID performance (Dawid Rusnak) [#37243](https://github.com/nodejs/node/pull/37243)
- [[`23d654105f`](https://github.com/nodejs/node/commit/23d654105f)] - **crypto,test**: improve hmac coverage with webcrypto tests (obi-el) [#37571](https://github.com/nodejs/node/pull/37571)
- [[`dfca2fac24`](https://github.com/nodejs/node/commit/dfca2fac24)] - **(SEMVER-MINOR)** **deps**: update to cjs-module-lexer@1.1.0 (Guy Bedford) [#37712](https://github.com/nodejs/node/pull/37712)
- [[`ce357c0c11`](https://github.com/nodejs/node/commit/ce357c0c11)] - **(SEMVER-MINOR)** **deps**: update archs files for OpenSSL-1.1.1+quic (James M Snell) [#37601](https://github.com/nodejs/node/pull/37601)
- [[`6d77b6174f`](https://github.com/nodejs/node/commit/6d77b6174f)] - **(SEMVER-MINOR)** **deps**: switch openssl to quictls/openssl (James M Snell) [#37601](https://github.com/nodejs/node/pull/37601)
- [[`3e1a46a6a8`](https://github.com/nodejs/node/commit/3e1a46a6a8)] - **deps**: upgrade npm to 7.6.3 (Ruy Adorno) [#37721](https://github.com/nodejs/node/pull/37721)
- [[`b2fd00398c`](https://github.com/nodejs/node/commit/b2fd00398c)] - **deps**: V8: cherry-pick 1648e050cade (Colin Ihrig) [#37664](https://github.com/nodejs/node/pull/37664)
- [[`7422453072`](https://github.com/nodejs/node/commit/7422453072)] - **deps**: upgrade npm to 7.6.1 (Ruy Adorno) [#37606](https://github.com/nodejs/node/pull/37606)
- [[`89f3aa92b4`](https://github.com/nodejs/node/commit/89f3aa92b4)] - **doc**: add marsonya as a triager (marsonya) [#37667](https://github.com/nodejs/node/pull/37667)
- [[`3710857de3`](https://github.com/nodejs/node/commit/3710857de3)] - **doc**: add hints to http.request() options (Luigi Pinca) [#37745](https://github.com/nodejs/node/pull/37745)
- [[`5d793737d7`](https://github.com/nodejs/node/commit/5d793737d7)] - **(SEMVER-MINOR)** **doc**: update maintaining-openssl guide (James M Snell) [#37601](https://github.com/nodejs/node/pull/37601)
- [[`1022d3d947`](https://github.com/nodejs/node/commit/1022d3d947)] - **doc**: recommend checking abortSignal.aborted first (James M Snell) [#37714](https://github.com/nodejs/node/pull/37714)
- [[`764aa2dcee`](https://github.com/nodejs/node/commit/764aa2dcee)] - **doc**: fix link to googletest fixtures (Tobias Nießen) [#37698](https://github.com/nodejs/node/pull/37698)
- [[`0d3cc2dc82`](https://github.com/nodejs/node/commit/0d3cc2dc82)] - **doc**: fix typo in description of close event (Tobias Nießen) [#37662](https://github.com/nodejs/node/pull/37662)
- [[`e55058fed1`](https://github.com/nodejs/node/commit/e55058fed1)] - **doc**: use sentence case in README.md headers (marsonya) [#37645](https://github.com/nodejs/node/pull/37645)
- [[`e7fc7a4c23`](https://github.com/nodejs/node/commit/e7fc7a4c23)] - **doc**: crypto esm examples (James M Snell) [#37594](https://github.com/nodejs/node/pull/37594)
- [[`a3abd52e1e`](https://github.com/nodejs/node/commit/a3abd52e1e)] - **doc**: add localPort to http.request() options (Luigi Pinca) [#37586](https://github.com/nodejs/node/pull/37586)
- [[`705bdfbe3e`](https://github.com/nodejs/node/commit/705bdfbe3e)] - **doc**: fix grammar errors in http document (Qingyu Deng) [#37265](https://github.com/nodejs/node/pull/37265)
- [[`e5f7179d1e`](https://github.com/nodejs/node/commit/e5f7179d1e)] - **doc**: add document for http.OutgoingMessage (Qingyu Deng) [#37265](https://github.com/nodejs/node/pull/37265)
- [[`7c0ce17e65`](https://github.com/nodejs/node/commit/7c0ce17e65)] - **doc**: fix typo in doc/guides/collaborator-guide.md (marsonya) [#37643](https://github.com/nodejs/node/pull/37643)
- [[`60d8afa9ab`](https://github.com/nodejs/node/commit/60d8afa9ab)] - **doc**: document that module.evaluate fulfills as undefined (James M Snell) [#37663](https://github.com/nodejs/node/pull/37663)
- [[`6192315cf3`](https://github.com/nodejs/node/commit/6192315cf3)] - **doc**: remove generated from dsaEncoding description (Marko Kaznovac) [#37459](https://github.com/nodejs/node/pull/37459)
- [[`e4c8c50b28`](https://github.com/nodejs/node/commit/e4c8c50b28)] - **doc**: fix typos in /doc/api/fs.md (Merlin Luntke) [#37557](https://github.com/nodejs/node/pull/37557)
- [[`ebc6f41072`](https://github.com/nodejs/node/commit/ebc6f41072)] - **doc**: fix linter issue (Antoine du Hamel) [#37657](https://github.com/nodejs/node/pull/37657)
- [[`d17aab1775`](https://github.com/nodejs/node/commit/d17aab1775)] - **doc**: add esm examples for assert (James M Snell) [#37607](https://github.com/nodejs/node/pull/37607)
- [[`366772bf87`](https://github.com/nodejs/node/commit/366772bf87)] - **doc**: add return type of readline.createInterface (Darshan Sen) [#37600](https://github.com/nodejs/node/pull/37600)
- [[`f50db89a52`](https://github.com/nodejs/node/commit/f50db89a52)] - **doc**: change lang info string in fs JS snippets (Antoine du Hamel) [#37605](https://github.com/nodejs/node/pull/37605)
- [[`5a9196e0e4`](https://github.com/nodejs/node/commit/5a9196e0e4)] - **doc**: apply sentence case to headers in pull-requests.md (marsonya) [#37602](https://github.com/nodejs/node/pull/37602)
- [[`05badcf755`](https://github.com/nodejs/node/commit/05badcf755)] - **doc**: fix small typo in 15.11.0 release (Tierney Cyren) [#37590](https://github.com/nodejs/node/pull/37590)
- [[`e0e7aa1058`](https://github.com/nodejs/node/commit/e0e7aa1058)] - **doc**: add top-level await syntax in vm.md (Antoine du Hamel) [#37077](https://github.com/nodejs/node/pull/37077)
- [[`732d8ca811`](https://github.com/nodejs/node/commit/732d8ca811)] - **doc**: clarify that columnOffset applies only to the first line (James M Snell) [#37563](https://github.com/nodejs/node/pull/37563)
- [[`267bbe3412`](https://github.com/nodejs/node/commit/267bbe3412)] - **doc**: document that NODE_EXTRA_CA_CERTS is read only once (James M Snell) [#37562](https://github.com/nodejs/node/pull/37562)
- [[`f56a805a0d`](https://github.com/nodejs/node/commit/f56a805a0d)] - **doc**: refactor signal info in child_process.md (Darshan Sen) [#37528](https://github.com/nodejs/node/pull/37528)
- [[`236ba04a79`](https://github.com/nodejs/node/commit/236ba04a79)] - **domain**: add name to monkey-patched emit function (Colin Ihrig) [#37550](https://github.com/nodejs/node/pull/37550)
- [[`1c09776106`](https://github.com/nodejs/node/commit/1c09776106)] - **domain**: show falsy names as anonymous for DEP0097 (Colin Ihrig) [#37550](https://github.com/nodejs/node/pull/37550)
- [[`5a49e3139e`](https://github.com/nodejs/node/commit/5a49e3139e)] - **errors**: remove experimental from --enable-source-maps (Benjamin Coe) [#37743](https://github.com/nodejs/node/pull/37743)
- [[`e384291c90`](https://github.com/nodejs/node/commit/e384291c90)] - **events**: remove return value on addEventListener (James M Snell) [#37696](https://github.com/nodejs/node/pull/37696)
- [[`ba91ef2d08`](https://github.com/nodejs/node/commit/ba91ef2d08)] - **fs**: improve fsPromises writeFile performance (Nitzan Uziely) [#37610](https://github.com/nodejs/node/pull/37610)
- [[`3572299fc2`](https://github.com/nodejs/node/commit/3572299fc2)] - **fs**: add promisified readFile benchmark (Nitzan Uziely) [#37608](https://github.com/nodejs/node/pull/37608)
- [[`b277776845`](https://github.com/nodejs/node/commit/b277776845)] - **fs**: improve fsPromises readFile performance (Nitzan Uziely) [#37608](https://github.com/nodejs/node/pull/37608)
- [[`6688569a50`](https://github.com/nodejs/node/commit/6688569a50)] - **http**: refactor to avoid unsafe array iteration (Antoine du Hamel) [#37654](https://github.com/nodejs/node/pull/37654)
- [[`c737df64fe`](https://github.com/nodejs/node/commit/c737df64fe)] - **http2**: make res.req a normal property (Colin Ihrig) [#37706](https://github.com/nodejs/node/pull/37706)
- [[`ac2f50b3fd`](https://github.com/nodejs/node/commit/ac2f50b3fd)] - **(SEMVER-MINOR)** **lib**: implement AbortSignal.abort() (James M Snell) [#37693](https://github.com/nodejs/node/pull/37693)
- [[`12fb2ffc33`](https://github.com/nodejs/node/commit/12fb2ffc33)] - **lib**: use AbortError consistently (James M Snell) [#37715](https://github.com/nodejs/node/pull/37715)
- [[`e63a25e2ff`](https://github.com/nodejs/node/commit/e63a25e2ff)] - **lib**: fix typo in lib/internal/http2/core.js (marsonya) [#37695](https://github.com/nodejs/node/pull/37695)
- [[`852f53ed7e`](https://github.com/nodejs/node/commit/852f53ed7e)] - **lib**: fix typo in lib/internal/bootstrap/loaders.js (marsonya) [#37644](https://github.com/nodejs/node/pull/37644)
- [[`daa4ac54c5`](https://github.com/nodejs/node/commit/daa4ac54c5)] - **lib**: remove use of array destructuring (Antoine du Hamel) [#36818](https://github.com/nodejs/node/pull/36818)
- [[`ae0e76c264`](https://github.com/nodejs/node/commit/ae0e76c264)] - **module**: refactor NativeModule to avoid unsafe array iteration (Antoine du Hamel) [#37656](https://github.com/nodejs/node/pull/37656)
- [[`a86334fbb9`](https://github.com/nodejs/node/commit/a86334fbb9)] - **(SEMVER-MINOR)** **node-api**: define version 8 (Gabriel Schulhof) [#37652](https://github.com/nodejs/node/pull/37652)
- [[`d28ce328ed`](https://github.com/nodejs/node/commit/d28ce328ed)] - **src**: fix variable name of OnCloseReceived callback (Tobias Nießen) [#37521](https://github.com/nodejs/node/pull/37521)
- [[`d59c6de7e8`](https://github.com/nodejs/node/commit/d59c6de7e8)] - **src**: add error formatting support (Gus Caplan) [#37598](https://github.com/nodejs/node/pull/37598)
- [[`33436e39fe`](https://github.com/nodejs/node/commit/33436e39fe)] - **src**: make BaseObject::is_snapshotable virtual (Anna Henningsen) [#37539](https://github.com/nodejs/node/pull/37539)
- [[`30c62dee1c`](https://github.com/nodejs/node/commit/30c62dee1c)] - **src,test**: support dynamically linking OpenSSL 3.0 (Daniel Bevenius) [#37669](https://github.com/nodejs/node/pull/37669)
- [[`4bf1f333c7`](https://github.com/nodejs/node/commit/4bf1f333c7)] - **stream,util**: fix "the the" typo in comments (Luigi Pinca) [#37674](https://github.com/nodejs/node/pull/37674)
- [[`1b53087541`](https://github.com/nodejs/node/commit/1b53087541)] - **(SEMVER-MINOR)** **test**: update dom/abort tests (James M Snell) [#37693](https://github.com/nodejs/node/pull/37693)
- [[`c2cb153646`](https://github.com/nodejs/node/commit/c2cb153646)] - **(SEMVER-MINOR)** **test**: fixup test to account for quic openssl version (James M Snell) [#37601](https://github.com/nodejs/node/pull/37601)
- [[`ede34aa128`](https://github.com/nodejs/node/commit/ede34aa128)] - **test**: address flaky wpt/test-timers (Rich Trott) [#37691](https://github.com/nodejs/node/pull/37691)
- [[`ed32cd4e67`](https://github.com/nodejs/node/commit/ed32cd4e67)] - **test**: fixup flaky test-crypto-x509 (Filip Skokan) [#37709](https://github.com/nodejs/node/pull/37709)
- [[`013b3ff2d4`](https://github.com/nodejs/node/commit/013b3ff2d4)] - **test**: remove unnecessary V8 flag (Antoine du Hamel) [#37671](https://github.com/nodejs/node/pull/37671)
- [[`cc48816826`](https://github.com/nodejs/node/commit/cc48816826)] - **test**: fix WPT URL tests that fetch JSON data (Michaël Zasso) [#37624](https://github.com/nodejs/node/pull/37624)
- [[`b0ed1e790e`](https://github.com/nodejs/node/commit/b0ed1e790e)] - **test**: improve error reporting in test-child-process-pipe-dataflow (Rich Trott) [#37632](https://github.com/nodejs/node/pull/37632)
- [[`f7edb07ec2`](https://github.com/nodejs/node/commit/f7edb07ec2)] - **test**: terminate WPT workers after test completion (Michaël Zasso) [#37627](https://github.com/nodejs/node/pull/37627)
- [[`b7ef829dac`](https://github.com/nodejs/node/commit/b7ef829dac)] - **test**: ignore WPT worker errors after tests finished (Michaël Zasso) [#37626](https://github.com/nodejs/node/pull/37626)
- [[`257b1ab225`](https://github.com/nodejs/node/commit/257b1ab225)] - **test**: update Web Platform Tests (Michaël Zasso) [#37620](https://github.com/nodejs/node/pull/37620)
- [[`1f6341852f`](https://github.com/nodejs/node/commit/1f6341852f)] - **test**: remove FLAKY status for test-async-hooks-http-parser-destroy (Rich Trott) [#37636](https://github.com/nodejs/node/pull/37636)
- [[`044fd2fc86`](https://github.com/nodejs/node/commit/044fd2fc86)] - **test**: remove FLAKY status for fixed test (Rich Trott) [#37633](https://github.com/nodejs/node/pull/37633)
- [[`d5ff50d2a7`](https://github.com/nodejs/node/commit/d5ff50d2a7)] - **test**: clear flaky designation for test-stream-pipeline-http2 (Rich Trott) [#37631](https://github.com/nodejs/node/pull/37631)
- [[`381fb98061`](https://github.com/nodejs/node/commit/381fb98061)] - **test**: clear FLAKY designation for test-http2-pipe (Rich Trott) [#37631](https://github.com/nodejs/node/pull/37631)
- [[`0582c51754`](https://github.com/nodejs/node/commit/0582c51754)] - **test**: fix wasi/test-return-on-exit on 32-bit systems (Colin Ihrig) [#37615](https://github.com/nodejs/node/pull/37615)
- [[`0d04b6c043`](https://github.com/nodejs/node/commit/0d04b6c043)] - **test**: fix flaky test-child-process-exec-abortcontroller-promisified (Antoine du Hamel) [#37572](https://github.com/nodejs/node/pull/37572)
- [[`a44daff34d`](https://github.com/nodejs/node/commit/a44daff34d)] - **test**: update all Web Platform Tests (Michaël Zasso) [#37467](https://github.com/nodejs/node/pull/37467)
- [[`c09bd77daf`](https://github.com/nodejs/node/commit/c09bd77daf)] - **test**: redownload wpt fixtures with correct encoding (Michaël Zasso) [#37467](https://github.com/nodejs/node/pull/37467)
- [[`57319770bb`](https://github.com/nodejs/node/commit/57319770bb)] - **test,crypto**: ensure promises resolve in webcrypto tests (Antoine du Hamel) [#37653](https://github.com/nodejs/node/pull/37653)
- [[`2d9b624668`](https://github.com/nodejs/node/commit/2d9b624668)] - **tls**: refactor to avoid unsafe array iteration (Antoine du Hamel) [#37655](https://github.com/nodejs/node/pull/37655)
- [[`72af5d9895`](https://github.com/nodejs/node/commit/72af5d9895)] - **tools**: parse changelogs only in the default branch (Antoine du Hamel) [#37768](https://github.com/nodejs/node/pull/37768)
- [[`bd62771a22`](https://github.com/nodejs/node/commit/bd62771a22)] - **tools**: use bundled npm in update scripts (Ruy Adorno) [#37613](https://github.com/nodejs/node/pull/37613)
- [[`4de3b8483a`](https://github.com/nodejs/node/commit/4de3b8483a)] - **tools**: update glob-parent to 5.1.2 (Rich Trott) [#37646](https://github.com/nodejs/node/pull/37646)
- [[`ec71a0f817`](https://github.com/nodejs/node/commit/ec71a0f817)] - **tools**: check version number in YAML comments from changelogs (Antoine du Hamel) [#37599](https://github.com/nodejs/node/pull/37599)
- [[`07fc61b900`](https://github.com/nodejs/node/commit/07fc61b900)] - **tools**: add support for mjs and cjs JS snippet linting (Antoine du Hamel) [#37311](https://github.com/nodejs/node/pull/37311)
- [[`440c944420`](https://github.com/nodejs/node/commit/440c944420)] - **tools**: fix object name in prefer-assert-methods.js (Tobias Nießen) [#37544](https://github.com/nodejs/node/pull/37544)
- [[`7042ec89f1`](https://github.com/nodejs/node/commit/7042ec89f1)] - **tools**: update remark-preset-lint-node to 2.1.1 (Rich Trott) [#37604](https://github.com/nodejs/node/pull/37604)
- [[`82e78f7c12`](https://github.com/nodejs/node/commit/82e78f7c12)] - **tools**: fix compiler warning in inspector_protocol (Darshan Sen) [#37573](https://github.com/nodejs/node/pull/37573)
- [[`fd7234c52f`](https://github.com/nodejs/node/commit/fd7234c52f)] - **tools**: make update-eslint.sh work with npm@7 (Luigi Pinca) [#37566](https://github.com/nodejs/node/pull/37566)
- [[`057c6a842a`](https://github.com/nodejs/node/commit/057c6a842a)] - **tools**: add ESLint rule no-array-destructuring (Antoine du Hamel) [#36818](https://github.com/nodejs/node/pull/36818)
- [[`25a5f0b3b8`](https://github.com/nodejs/node/commit/25a5f0b3b8)] - **tools**: update eslint-plugin-markdown configuration (Colin Ihrig) [#37549](https://github.com/nodejs/node/pull/37549)
- [[`7a1de1fce9`](https://github.com/nodejs/node/commit/7a1de1fce9)] - **tools**: update ESLint to 7.21.0 (Luigi Pinca) [#37546](https://github.com/nodejs/node/pull/37546)
- [[`9c0ca4689d`](https://github.com/nodejs/node/commit/9c0ca4689d)] - **tools,doc**: add support for several flavors of JS code snippets (Antoine du Hamel) [#37162](https://github.com/nodejs/node/pull/37162)
- [[`80af610d95`](https://github.com/nodejs/node/commit/80af610d95)] - **util**: inspect \_\_proto\_\_ key as written in object literal (Anna Henningsen) [#37713](https://github.com/nodejs/node/pull/37713)
- [[`0d135e8316`](https://github.com/nodejs/node/commit/0d135e8316)] - **(SEMVER-MINOR)** **worker**: add setEnvironmentData/getEnvironmentData (James M Snell) [#37486](https://github.com/nodejs/node/pull/37486)
- [[`8024ffbba4`](https://github.com/nodejs/node/commit/8024ffbba4)] - **worker**: add ports property to MessageEvents (Anna Henningsen) [#37538](https://github.com/nodejs/node/pull/37538)
- [[`f4fd3fb6a7`](https://github.com/nodejs/node/commit/f4fd3fb6a7)] - **worker**: allow BroadcastChannel in receiveMessageOnPort (Anna Henningsen) [#37535](https://github.com/nodejs/node/pull/37535)

Windows 32-bit Installer: https://nodejs.org/dist/v15.12.0/node-v15.12.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v15.12.0/node-v15.12.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v15.12.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v15.12.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v15.12.0/node-v15.12.0.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v15.12.0/node-v15.12.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v15.12.0/node-v15.12.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v15.12.0/node-v15.12.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v15.12.0/node-v15.12.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v15.12.0/node-v15.12.0-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v15.12.0/node-v15.12.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v15.12.0/node-v15.12.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v15.12.0/node-v15.12.0.tar.gz \
Other release files: https://nodejs.org/dist/v15.12.0/ \
Documentation: https://nodejs.org/docs/v15.12.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

b05beb9caa359aba0402412937a6f7434459adab016021c6e30f4c67b5ab35d9  node-v15.12.0-aix-ppc64.tar.gz
c04a77a3bf4a9ea7f5c0d34773cb8fcc4af594b202fa69ea41edc372ecd28df0  node-v15.12.0-darwin-x64.tar.gz
adcdfe6e147a67741ac32bd454f0ed69717387486b2275df33758b276d00de83  node-v15.12.0-darwin-x64.tar.xz
9514682405d3f1567838e289eeace2546a05fb88fc7d77f8ee566cadd92025ba  node-v15.12.0-headers.tar.gz
96fe0238aef09626ffed5288871ce7fe556a5aa3aeb529fe70ce8c8c9e722c60  node-v15.12.0-headers.tar.xz
cf1e276e381bbe58998feb584f907980ad2c6cd8c88dd4662a089b177b7ab0b2  node-v15.12.0-linux-arm64.tar.gz
be93231a6616356e2a33d5007de19ffd573d5a05a67662c1583edbb7b3ecb234  node-v15.12.0-linux-arm64.tar.xz
e90567de53a7a5a7ff4ff1088516ed5bbfa1db2ba7a7f9b7010cfbb3784ba408  node-v15.12.0-linux-armv7l.tar.gz
2bcebc5bc59acbd239e42967e2342e9385b407546d5570c09d5dde90a9b3bf77  node-v15.12.0-linux-armv7l.tar.xz
5f635bf5b6249562e220d40527a0d9dcb45f551660a4ee3948f639948c39ba90  node-v15.12.0-linux-ppc64le.tar.gz
a3d719ad30b90305a732414a3467c0833f8b58b1b8170c88e35c0c8e72958606  node-v15.12.0-linux-ppc64le.tar.xz
b8d0f1e206ff1efa512aa33b556359cde46724e8d9f8d6e6d1da27d109388bff  node-v15.12.0-linux-s390x.tar.gz
b04f13b4e34450d2794feac4bbc67028a595893f95fab63829763487b136e81c  node-v15.12.0-linux-s390x.tar.xz
0fa13e94e07e05b64819f44de92a81f4a95c8d952278e85eba3c7dd11fe63aea  node-v15.12.0-linux-x64.tar.gz
3892b4058cf12823c6cf39e4a3e9ce6da3f908cbfd211ddfc0a9df73eecd8bdc  node-v15.12.0-linux-x64.tar.xz
b8df5c09ed9bd191cf244d1d0d910e1f90bdfb17d4a163b690cff9794b061e37  node-v15.12.0.pkg
cfb13ad6443e9e08d2c37468e57d2d79b91c5372e9d9d3f03756a0e31d984497  node-v15.12.0.tar.gz
5ebbbe5024787a45134c4fbd77f50ae494c9cd49c87ce2c9f327af77d8af1a31  node-v15.12.0.tar.xz
e103b811f4be35cf3388a398ff487fd6a53e07c9acbfea52533a97e3668affd3  node-v15.12.0-win-x64.7z
630943523e7289545af6fba3a4ab615d3bef466b00e504be044c55bc8fa100e5  node-v15.12.0-win-x64.zip
b0a41cf8eb23f88ad187c6658ae470462c4b04e1385d20e95c1a5b800c6239ae  node-v15.12.0-win-x86.7z
8ba62e8ffa1e04c724e88522b07a395398bcd3215d908ec2d6b4bb8e313088ca  node-v15.12.0-win-x86.zip
238cc9aa94527ab2fbcef4a9bcf81bb01667fb4864dfe490aead1ae474d5584c  node-v15.12.0-x64.msi
7729c52a1dfd162217c698ca53c487abb300c011bdfd2b806b3cefa7ea10a82c  node-v15.12.0-x86.msi
a6e114a50a51dcdce880dac9ab48293132541cb682d21a5200001c354f93df6c  win-x64/node.exe
efec0902e39ae60a3c5e54db9e9fff3ebcec88744851f6df911da9ad9eb39a2d  win-x64/node.lib
68009b608bd60afbada5c08f447ecf37e60bd7c8f9299cb083dde8b41871fc61  win-x64/node_pdb.7z
a45dfbcfa66672328593d4a6840efb99124a0560bde7fc936d163617388f08fe  win-x64/node_pdb.zip
5bfd61ce67f5e4ef040ad2ed478644f28dd94d8be03be5ad8fa8f87930c09cc4  win-x86/node.exe
68abf8d368843410f4ca9ea0b3f8d31dbf9477c527a33a9039bbd9208236a732  win-x86/node.lib
6c445a037a3db38fedc6b2defae40b1a3f26876d11a75b70ffd04d57882fde3a  win-x86/node_pdb.7z
c11857ae1384e337034f54e188b05a3403cd69206870fd65bdba92b329c5ad4e  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEdPEmArbxxOkT+qN606iWE2Q7YgEFAmBSgPAACgkQ06iWE2Q7
YgHAxQ//X02ypoNObdBJR7Blg+tmi8Sz/sdDJ+8Etb1WwB0rzMqFr94vkZotx46l
GUSTZiVwKyi+rjAjJ+uJHJ0ywnLOj8hFLP6eL+yzn35nnoX0sBeg+2nML9N72eSM
BCWPSNOi5CsZdUFntBDJ5ZkMTZBaleIyqbgnBORTnUEqfzjmZGb2aErFXxYa6Gym
1vQyd+Ie+ZpKwWn+HhjjG+mqxnHmXHdS8DS1gBCZx507QiW8t5D0rbnR92jjstpo
ZNDcMvYXSzHxm6YvV5y8Aafkvb978gxkMww398eRQxxMFw6jKG7yARwjtB2qKoF+
8MDM7wg1RF5iP7rxFcWlA2rAk+/g3h2O9GGLkmpHc9YWrsrZXP2ejqWEgZaSLF2i
1HY/zz1IyE+rdWMvYY6Yc/8IUyLQX8yihnuUSlF70ziI4Ndpyqm6A2ZfrNfnO2e2
+zYt9eCymsn5avQ8PSoVPSdqRbqEU0l8ugIa4b3UQOLo1RfdkTKPg/NuH0p0OET6
a2mjnDWbQ1ycf7dDQ6o/6lCquIlRGgdrPYM5ptFN+DmdF2qjU1B+IsQa2m0uc7ab
iutwdgMKGRA2ynOdDkpHdCruWWcFczG29gf9AeSitLMxvNGU50y+AoeMy/FcUv1M
QSTd17389IYKzlYe7506olRf8iR03hQaP6S2pC5N6ZWNMLpX2vo=
=KqiK
-----END PGP SIGNATURE-----

```
