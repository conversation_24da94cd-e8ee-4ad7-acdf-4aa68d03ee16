---
date: '2021-10-12T15:35:37.909Z'
category: release
title: Node v14.18.1 (LTS)
layout: blog-post
author: <PERSON>
---

### Notable changes

- **CVE-2021-22959**: HTTP Request Smuggling due to spaced in headers (Medium)
  - The http parser accepts requests with a space (SP) right after the header name before the colon. This can lead to HTTP Request Smuggling (HRS). More details will be available at [CVE-2021-22959](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2021-22959) after publication.
- **CVE-2021-22960**: HTTP Request Smuggling when parsing the body (Medium)
  - The parse ignores chunk extensions when parsing the body of chunked requests. This leads to HTTP Request Smuggling (HRS) under certain conditions. More details will be available at [CVE-2021-22960](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2021-22960) after publication.

### Commits

- [[`8c254ca7e4`](https://github.com/nodejs/node/commit/8c254ca7e4)] - **deps**: update llhttp to 2.1.4 (Fedor Indutny) [nodejs-private/node-private#285](https://github.com/nodejs-private/node-private/pull/285)
- [[`9b92ae2499`](https://github.com/nodejs/node/commit/9b92ae2499)] - **http**: add regression test for smuggling content length (Matteo Collina) [nodejs-private/node-private#285](https://github.com/nodejs-private/node-private/pull/285)
- [[`f467539719`](https://github.com/nodejs/node/commit/f467539719)] - **http**: add regression test for chunked smuggling (Matteo Collina) [nodejs-private/node-private#285](https://github.com/nodejs-private/node-private/pull/285)

Windows 32-bit Installer: https://nodejs.org/dist/v14.18.1/node-v14.18.1-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v14.18.1/node-v14.18.1-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v14.18.1/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v14.18.1/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v14.18.1/node-v14.18.1.pkg \
macOS Intel 64-bit Binary: https://nodejs.org/dist/v14.18.1/node-v14.18.1-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v14.18.1/node-v14.18.1-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v14.18.1/node-v14.18.1-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v14.18.1/node-v14.18.1-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v14.18.1/node-v14.18.1-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v14.18.1/node-v14.18.1-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v14.18.1/node-v14.18.1-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v14.18.1/node-v14.18.1.tar.gz \
Other release files: https://nodejs.org/dist/v14.18.1/ \
Documentation: https://nodejs.org/docs/v14.18.1/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

01092d92ec076778b9291983fa6854befd19a11655f261a3b3ec1c675154ecfb  node-v14.18.1-aix-ppc64.tar.gz
78731152378577decf681167f4c6be6c31134dfef07403c1cebfbd3289d3886f  node-v14.18.1-darwin-x64.tar.gz
4ad95cb566313c4a1c84449701f153aeb105b785852ae6ed7576916405b723ab  node-v14.18.1-darwin-x64.tar.xz
3352973a1a737b47f2b1822ab9fd5d401427c7ba3b538ecae9928924d06b60a5  node-v14.18.1-headers.tar.gz
02ef2a81ad18e111cbb4a8bd1bde91f066b020d2b8a3fd71f2d0e59c1329cca6  node-v14.18.1-headers.tar.xz
3fcd1c6c008c2dfddea60ede3c735696982fb038288e45c2d35ef6b2098c8220  node-v14.18.1-linux-arm64.tar.gz
15c2ba182bafcc006c01a0e6f736a6ff3df101dec4c45026add51025095ab60d  node-v14.18.1-linux-arm64.tar.xz
6da18f92ce3320e07f710435843d45e97040a2a51667e92af40bb005ebf651d1  node-v14.18.1-linux-armv7l.tar.gz
a3109ffe919af2974dbad16ab634c8a9f47f51369d7321992d975637e6556ce3  node-v14.18.1-linux-armv7l.tar.xz
dbd177d5033dcf3168068a69afbd43e4b8b518a979cd4843b11aec631d840edb  node-v14.18.1-linux-ppc64le.tar.gz
a4eb863418ecc1179a90b397ab4f221eacdb58336b41d30c7c7a322b25659e32  node-v14.18.1-linux-ppc64le.tar.xz
7019e933833f09bc880cbba7a06c77135b1caff8d5f88d700d21883ecfb286fb  node-v14.18.1-linux-s390x.tar.gz
e01f11308371b4268341a1b25dfb4433ae2e2403940f5acbfeb820b128ca7e27  node-v14.18.1-linux-s390x.tar.xz
088498c67bab31871a1cab40dbc9b7b82c1abf53a2cf740e061bd6033a74839d  node-v14.18.1-linux-x64.tar.gz
ad1e3baa1aee8028b43206da3b2be9b8867cb598b4318bc88a0ae4518cc062a2  node-v14.18.1-linux-x64.tar.xz
9021f68782bcdd65705fc15bb9e178c3c584efbe15586604ce053e6f803d85c0  node-v14.18.1.pkg
89d22d34fd4ba3715252dcd2dd94d1699338436463b277163ed950040c7b621a  node-v14.18.1.tar.gz
3fa1d71adddfab2f5e3e41874b4eddbdf92b65cade4a43922fb1e437afcf89ed  node-v14.18.1.tar.xz
36f51353a4650ed8c842b33991fe2aaad29dd0f80aae7864d591a52430a64503  node-v14.18.1-win-x64.7z
86737cd4544c4f8cda2abd8e60709a87dbf46119062c5f1d4ec297f71a9e204b  node-v14.18.1-win-x64.zip
bcd5b62a214479ec2b047f17224d5cdde14978cbb68ff58a8c2d23aa10bce9bd  node-v14.18.1-win-x86.7z
ba12288e444c6c2bf7e4d605bdcc8c34cad9c5dffea3910b5ecb67ae34f9e0bf  node-v14.18.1-win-x86.zip
10d8e278201c3b1509d989425bcc6d81e772d2d7703613e0cba764579601397c  node-v14.18.1-x64.msi
41d230dae23966f7d6de50b39a8dfc52f93c15153ff0616637b786cc632931fe  node-v14.18.1-x86.msi
6d519ba12fbaa38485d50007e084ae552c55219d9534a0b6fec4c33f989fb5c6  win-x64/node.exe
8dc2fec0ce78af200bce0f60a59cd4544d8798ba6f50d6326ab7b9cc4246721e  win-x64/node.lib
80d76f552c53d60b349b06c2d86193e49997a63114b72f7904e7802dd33dda76  win-x64/node_pdb.7z
aeec6012f23a717337f1d9e008a736389e0ec7e266832767758710e14a94b574  win-x64/node_pdb.zip
22554f36f3a5ac26d6990f08043ae303043487a97a250daa2c4a083af42d8b9b  win-x86/node.exe
a02fb6e31fcd3cbdebb8966581cba025c77f9ca86727c9f1c2b5c988b0a4c19c  win-x86/node.lib
3058b6a2187b9beee5da2f78be1d30175e53da4101c43cc614eca4414b6d9a14  win-x86/node_pdb.7z
29ab2a2a959c87e8f2b877302b9de9d0d3460839235af334cef5af9fb8ae2978  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEdPEmArbxxOkT+qN606iWE2Q7YgEFAmFlqEgACgkQ06iWE2Q7
YgGu6A/+J0TW1ngT+wCWZ2cxvIdBEjkbFIhKzCt3nEvh3cGect6t6aIGywMJllrN
fNrUSvtedxDjePznVZR+mfYtQso+cbcygMYfQZIFmlEXdeHioIp6guqqWPuGYuBo
toNVHxJjBEA/DNOVwLZdbWERjPcjpOVjksOHc5ZWkiFPxCBrb5MyFHn3WwTjSNx7
opMQDeo/DA59t6WroTP4cdXqP78hbZY72Xg7R6WHVL/hazVZ+67LDHzinvj/XqAN
YErz7lHvKXBAio9NhK+0ddIfFGrzB3yqe229GFlVV63XWLljNmRvR2gnVyFkDpuP
TbKuQOcDwUV7+BliY/dqAzeqjbNAvoiA1KpQ6b0slPyoLn0Sdx2pjpG92bgU84Ed
89xlmwc/ZlQ8a0KvlOsjx6c4NioS1RZsZW7l3t09vNhMte76yl5xKjsjxMlZW/yH
mpHaoteu5nTBXzTQ6Ykns1KbfUQdl8fxf20uePmeA90h9udoVCreep4GoaHeSgEB
zj02mUQ4vO5Ki/A4Jpn7aqrtkcdoZjI46pw1PYxOvHZjF+rmmSKD/deYj1CmeYnS
f2rNBR0ThaJHCFYemMnsbBcFi2NDAqOiZuy3d58QBf0Pa3sQW07YOjJkeidOCqgV
PSnQSv9cgTToYDIe84Gvyv28siZuRG43QyfVZdIacxiIrXDU6+k=
=EI1x
-----END PGP SIGNATURE-----

```
