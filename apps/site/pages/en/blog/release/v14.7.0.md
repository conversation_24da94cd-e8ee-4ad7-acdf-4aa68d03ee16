---
date: '2020-07-29T18:46:03.170Z'
category: release
title: Node v14.7.0 (Current)
layout: blog-post
author: <PERSON><PERSON>
---

### Notable Changes

- **deps**:
  - upgrade npm to 6.14.7 (claudiahdz) [#34468](https://github.com/nodejs/node/pull/34468)
- **dgram**:
  - **(SEMVER-MINOR)** add IPv6 scope id suffix to received udp6 dgrams (<PERSON>ek<PERSON>) [#14500](https://github.com/nodejs/node/pull/14500)
- **src**:
  - **(SEMVER-MINOR)** allow preventing SetPromiseRejectCallback (<PERSON>) [#34387](https://github.com/nodejs/node/pull/34387)
  - **(SEMVER-MINOR)** allow setting a dir for all diagnostic output (AshCripps) [#33584](https://github.com/nodejs/node/pull/33584)
- **worker**:
  - **(SEMVER-MINOR)** make MessagePort inherit from <PERSON><PERSON><PERSON><PERSON> (<PERSON>) [#34057](https://github.com/nodejs/node/pull/34057)
- **zlib**:
  - switch to lazy init for zlib streams (Andrey Pechkurov) [#34048](https://github.com/nodejs/node/pull/34048)
- **New Collaborators**:
  - add rexagod to collaborators (Pranshu Srivastava) [#34457](https://github.com/nodejs/node/pull/34457)
  - add AshCripps to collaborators (AshCripps) [#34494](https://github.com/nodejs/node/pull/34494)
  - add HarshithaKP to collaborators (Harshitha K P) [#34417](https://github.com/nodejs/node/pull/34417)
  - add release key for Richard Lau (Richard Lau) [#34397](https://github.com/nodejs/node/pull/34397)

### Commits

- [[`dd2988917f`](https://github.com/nodejs/node/commit/dd2988917f)] - **async_hooks**: optimize fast-path promise hook for ALS (Andrey Pechkurov) [#34512](https://github.com/nodejs/node/pull/34512)
- [[`358b934284`](https://github.com/nodejs/node/commit/358b934284)] - **build**: fix test-ci-js task in Makefile (Rich Trott) [#34433](https://github.com/nodejs/node/pull/34433)
- [[`24e1beb829`](https://github.com/nodejs/node/commit/24e1beb829)] - **build**: do not run benchmark tests on 'make test' (Rich Trott) [#34434](https://github.com/nodejs/node/pull/34434)
- [[`b24f254472`](https://github.com/nodejs/node/commit/b24f254472)] - **build**: add benchmark tests to CI runs (Rich Trott) [#34288](https://github.com/nodejs/node/pull/34288)
- [[`a4806e2d12`](https://github.com/nodejs/node/commit/a4806e2d12)] - **build**: speed up source tarball creation (Richard Lau) [#34508](https://github.com/nodejs/node/pull/34508)
- [[`cce1f3e3a8`](https://github.com/nodejs/node/commit/cce1f3e3a8)] - **build**: don't run test-asan workflow on non-master pushes (Richard Lau) [#34509](https://github.com/nodejs/node/pull/34509)
- [[`70f23eb405`](https://github.com/nodejs/node/commit/70f23eb405)] - **build**: remove test-tarball action for windows + osx (Myles Borins) [#34440](https://github.com/nodejs/node/pull/34440)
- [[`3fda3d4bf3`](https://github.com/nodejs/node/commit/3fda3d4bf3)] - **build**: don't run Actions on non-master pushes (Shelley Vohr) [#34464](https://github.com/nodejs/node/pull/34464)
- [[`f7600d5ab6`](https://github.com/nodejs/node/commit/f7600d5ab6)] - **deps**: upgrade npm to 6.14.7 (claudiahdz) [#34468](https://github.com/nodejs/node/pull/34468)
- [[`02ae6d65d4`](https://github.com/nodejs/node/commit/02ae6d65d4)] - **(SEMVER-MINOR)** **dgram**: add IPv6 scope id suffix to received udp6 dgrams (Pekka Nikander) [#14500](https://github.com/nodejs/node/pull/14500)
- [[`e5f380052f`](https://github.com/nodejs/node/commit/e5f380052f)] - **_Revert_** "**doc**: move ronkorving to emeritus" (Rich Trott) [#34507](https://github.com/nodejs/node/pull/34507)
- [[`17bca62428`](https://github.com/nodejs/node/commit/17bca62428)] - **doc**: use sentence-case for GOVERNANCE.md headers (Rich Trott) [#34503](https://github.com/nodejs/node/pull/34503)
- [[`37752cde43`](https://github.com/nodejs/node/commit/37752cde43)] - **doc**: revise onboarding-extras (Rich Trott) [#34496](https://github.com/nodejs/node/pull/34496)
- [[`050866ddf1`](https://github.com/nodejs/node/commit/050866ddf1)] - **doc**: remove breaking-change-helper from onboarding-extras (Rich Trott) [#34497](https://github.com/nodejs/node/pull/34497)
- [[`2297d74fd8`](https://github.com/nodejs/node/commit/2297d74fd8)] - **doc**: add Triagers section to table of contents in GOVERNANCE.md (Rich Trott) [#34504](https://github.com/nodejs/node/pull/34504)
- [[`99a648738c`](https://github.com/nodejs/node/commit/99a648738c)] - **doc**: onboarding process extras (Gireesh Punathil) [#34455](https://github.com/nodejs/node/pull/34455)
- [[`bbc7eeadd9`](https://github.com/nodejs/node/commit/bbc7eeadd9)] - **doc**: mention triage in GOVERNANCE.md (Gireesh Punathil) [#34426](https://github.com/nodejs/node/pull/34426)
- [[`92c57b284b`](https://github.com/nodejs/node/commit/92c57b284b)] - **doc**: move thefourtheye to emeritus (Rich Trott) [#34471](https://github.com/nodejs/node/pull/34471)
- [[`657f2d78ee`](https://github.com/nodejs/node/commit/657f2d78ee)] - **doc**: move ronkorving to emeritus (Rich Trott) [#34471](https://github.com/nodejs/node/pull/34471)
- [[`455dd9cc76`](https://github.com/nodejs/node/commit/455dd9cc76)] - **doc**: match link text in index to doc headline (Rich Trott) [#34449](https://github.com/nodejs/node/pull/34449)
- [[`f4a63f3d9a`](https://github.com/nodejs/node/commit/f4a63f3d9a)] - **doc**: add AshCripps to collaborators (AshCripps) [#34494](https://github.com/nodejs/node/pull/34494)
- [[`7d058a4c01`](https://github.com/nodejs/node/commit/7d058a4c01)] - **doc**: add author-ready label ref to onboarding doc (Ruy Adorno) [#34381](https://github.com/nodejs/node/pull/34381)
- [[`a3c9f75b7e`](https://github.com/nodejs/node/commit/a3c9f75b7e)] - **doc**: add HarshithaKP to collaborators (Harshitha K P) [#34417](https://github.com/nodejs/node/pull/34417)
- [[`4b4eb5f130`](https://github.com/nodejs/node/commit/4b4eb5f130)] - **doc**: add rexagod to collaborators (Pranshu Srivastava) [#34457](https://github.com/nodejs/node/pull/34457)
- [[`29ad6fb34e`](https://github.com/nodejs/node/commit/29ad6fb34e)] - **doc**: add statement of purpose to documentation style guide (Rich Trott) [#34424](https://github.com/nodejs/node/pull/34424)
- [[`631dd21709`](https://github.com/nodejs/node/commit/631dd21709)] - **doc**: mark Node.js 13 as End-of-Life (Antoine du Hamel) [#34436](https://github.com/nodejs/node/pull/34436)
- [[`905e3d18c0`](https://github.com/nodejs/node/commit/905e3d18c0)] - **doc**: fix line length in worker_threads.md (Jucke) [#34419](https://github.com/nodejs/node/pull/34419)
- [[`d67a2b8d38`](https://github.com/nodejs/node/commit/d67a2b8d38)] - **doc**: fix typos in n-api, tls and worker_threads (Jucke) [#34419](https://github.com/nodejs/node/pull/34419)
- [[`39894f8842`](https://github.com/nodejs/node/commit/39894f8842)] - **doc**: add release key for Richard Lau (Richard Lau) [#34397](https://github.com/nodejs/node/pull/34397)
- [[`4a828c6c06`](https://github.com/nodejs/node/commit/4a828c6c06)] - **doc**: use correct identifier for callback argument (Rich Trott) [#34405](https://github.com/nodejs/node/pull/34405)
- [[`10830732f6`](https://github.com/nodejs/node/commit/10830732f6)] - **doc**: add changes metadata to TLS newSession event (Tobias Nießen) [#34294](https://github.com/nodejs/node/pull/34294)
- [[`10962c81e1`](https://github.com/nodejs/node/commit/10962c81e1)] - **doc**: introduce a triager role (Gireesh Punathil) [#34295](https://github.com/nodejs/node/pull/34295)
- [[`50fd2b9de9`](https://github.com/nodejs/node/commit/50fd2b9de9)] - **doc**: strengthen suggestion in errors.md (Rich Trott) [#34390](https://github.com/nodejs/node/pull/34390)
- [[`346c201c4e`](https://github.com/nodejs/node/commit/346c201c4e)] - **doc**: strengthen wording about fs.access() misuse (Rich Trott) [#34352](https://github.com/nodejs/node/pull/34352)
- [[`c28453aff4`](https://github.com/nodejs/node/commit/c28453aff4)] - **doc**: fix typo in assert.md (Ye-hyoung Kang) [#34316](https://github.com/nodejs/node/pull/34316)
- [[`f60e58b6c9`](https://github.com/nodejs/node/commit/f60e58b6c9)] - **doc,tools**: syntax highlight api docs at compile-time (Francisco Ryan Tolmasky I) [#34148](https://github.com/nodejs/node/pull/34148)
- [[`d90967b346`](https://github.com/nodejs/node/commit/d90967b346)] - **events**: re-use the same isTrusted getter (Anna Henningsen) [#34459](https://github.com/nodejs/node/pull/34459)
- [[`c93a898028`](https://github.com/nodejs/node/commit/c93a898028)] - **(SEMVER-MINOR)** **events**: expand NodeEventTarget functionality (Anna Henningsen) [#34057](https://github.com/nodejs/node/pull/34057)
- [[`9b91467aac`](https://github.com/nodejs/node/commit/9b91467aac)] - **http**: don't write error to socket (Robert Nagy) [#34465](https://github.com/nodejs/node/pull/34465)
- [[`098b193eab`](https://github.com/nodejs/node/commit/098b193eab)] - **http2**: avoid unnecessary buffer resize (Denys Otrishko) [#34480](https://github.com/nodejs/node/pull/34480)
- [[`3024927c9b`](https://github.com/nodejs/node/commit/3024927c9b)] - **lib**: initialize instance members in class constructors (Joyee Cheung) [#32984](https://github.com/nodejs/node/pull/32984)
- [[`82fad58ade`](https://github.com/nodejs/node/commit/82fad58ade)] - **lib**: simplify assignment (sapics) [#33718](https://github.com/nodejs/node/pull/33718)
- [[`e1199af50a`](https://github.com/nodejs/node/commit/e1199af50a)] - **module**: self referential modules in repl or `-r` (Daniele Belardi) [#32261](https://github.com/nodejs/node/pull/32261)
- [[`e7c64af404`](https://github.com/nodejs/node/commit/e7c64af404)] - **n-api**: run all finalizers via SetImmediate() (Gabriel Schulhof) [#34386](https://github.com/nodejs/node/pull/34386)
- [[`668632d531`](https://github.com/nodejs/node/commit/668632d531)] - **net**: allow wider regex in interface name (Stewart X Addison) [#34364](https://github.com/nodejs/node/pull/34364)
- [[`c05b63d8b2`](https://github.com/nodejs/node/commit/c05b63d8b2)] - **src**: skip weak references for memory tracking (Anna Henningsen) [#34469](https://github.com/nodejs/node/pull/34469)
- [[`b12211eeca`](https://github.com/nodejs/node/commit/b12211eeca)] - **src**: prefer internal fields in ModuleWrap (Anna Henningsen) [#34470](https://github.com/nodejs/node/pull/34470)
- [[`cbe6385880`](https://github.com/nodejs/node/commit/cbe6385880)] - **src**: remove unused variable in node_file.cc (sapics) [#34317](https://github.com/nodejs/node/pull/34317)
- [[`d6ee1fd0c2`](https://github.com/nodejs/node/commit/d6ee1fd0c2)] - **src**: do not crash if ToggleAsyncHook fails during termination (Anna Henningsen) [#34362](https://github.com/nodejs/node/pull/34362)
- [[`bd9ab00acd`](https://github.com/nodejs/node/commit/bd9ab00acd)] - **(SEMVER-MINOR)** **src**: allow preventing SetPromiseRejectCallback (Shelley Vohr) [#34387](https://github.com/nodejs/node/pull/34387)
- [[`5c943588bc`](https://github.com/nodejs/node/commit/5c943588bc)] - **(SEMVER-MINOR)** **src**: allow setting a dir for all diagnostic output (AshCripps) [#33584](https://github.com/nodejs/node/pull/33584)
- [[`9d40af54a6`](https://github.com/nodejs/node/commit/9d40af54a6)] - **src**: avoid strcmp in SecureContext::Init (Tobias Nießen) [#34329](https://github.com/nodejs/node/pull/34329)
- [[`aef41e5b52`](https://github.com/nodejs/node/commit/aef41e5b52)] - **src**: refactor CertCbDone to avoid goto statement (Tobias Nießen) [#34325](https://github.com/nodejs/node/pull/34325)
- [[`3d4f608e42`](https://github.com/nodejs/node/commit/3d4f608e42)] - **stream**: rename opts to options (rickyes) [#34339](https://github.com/nodejs/node/pull/34339)
- [[`fced3ce5ad`](https://github.com/nodejs/node/commit/fced3ce5ad)] - **test**: add ref comment to test-regress-GH-814_2 (Rich Trott) [#34516](https://github.com/nodejs/node/pull/34516)
- [[`d5c8b386c6`](https://github.com/nodejs/node/commit/d5c8b386c6)] - **test**: add ref comment to test-regress-GH-814 (Rich Trott) [#34516](https://github.com/nodejs/node/pull/34516)
- [[`cc279db29f`](https://github.com/nodejs/node/commit/cc279db29f)] - **test**: remove superfluous check in pummel/test-timers (Rich Trott) [#34488](https://github.com/nodejs/node/pull/34488)
- [[`3f11ba1c69`](https://github.com/nodejs/node/commit/3f11ba1c69)] - **test**: fix test-heapdump-zlib (Andrey Pechkurov) [#34499](https://github.com/nodejs/node/pull/34499)
- [[`81eaaa27d5`](https://github.com/nodejs/node/commit/81eaaa27d5)] - **test**: remove duplicate checks in pummel/test-timers (Rich Trott) [#34473](https://github.com/nodejs/node/pull/34473)
- [[`1a9138d679`](https://github.com/nodejs/node/commit/1a9138d679)] - **test**: delete invalid test (Anna Henningsen) [#34445](https://github.com/nodejs/node/pull/34445)
- [[`4e2f5fa907`](https://github.com/nodejs/node/commit/4e2f5fa907)] - **test**: fixup worker + source map test (Anna Henningsen) [#34446](https://github.com/nodejs/node/pull/34446)
- [[`cd35d00518`](https://github.com/nodejs/node/commit/cd35d00518)] - **test**: force resigning of app (Colin Ihrig) [#34331](https://github.com/nodejs/node/pull/34331)
- [[`eecb92c9da`](https://github.com/nodejs/node/commit/eecb92c9da)] - **test**: fix flaky test-watch-file (Rich Trott) [#34420](https://github.com/nodejs/node/pull/34420)
- [[`30da332314`](https://github.com/nodejs/node/commit/30da332314)] - **test**: fix flaky test-heapdump-http2 (Rich Trott) [#34415](https://github.com/nodejs/node/pull/34415)
- [[`77542a4a7a`](https://github.com/nodejs/node/commit/77542a4a7a)] - **test**: do not write to fixtures dir in test-watch-file (Rich Trott) [#34376](https://github.com/nodejs/node/pull/34376)
- [[`699da05b29`](https://github.com/nodejs/node/commit/699da05b29)] - **test**: remove common.localhostIPv6 (Rich Trott) [#34373](https://github.com/nodejs/node/pull/34373)
- [[`ec1393db63`](https://github.com/nodejs/node/commit/ec1393db63)] - **test**: fix test-net-pingpong pummel test for non-IPv6 hosts (Rich Trott) [#34359](https://github.com/nodejs/node/pull/34359)
- [[`8ca80427db`](https://github.com/nodejs/node/commit/8ca80427db)] - **test**: fix flaky test-net-connect-econnrefused (Rich Trott) [#34330](https://github.com/nodejs/node/pull/34330)
- [[`e9c7722ea4`](https://github.com/nodejs/node/commit/e9c7722ea4)] - **tls**: remove setMaxSendFragment guards (Tobias Nießen) [#34323](https://github.com/nodejs/node/pull/34323)
- [[`f4d61c7ce9`](https://github.com/nodejs/node/commit/f4d61c7ce9)] - **tools**: update ESLint to 7.5.0 (Colin Ihrig) [#34423](https://github.com/nodejs/node/pull/34423)
- [[`74da2c44ca`](https://github.com/nodejs/node/commit/74da2c44ca)] - **util**: improve getStringWidth performance (Ruben Bridgewater) [#33674](https://github.com/nodejs/node/pull/33674)
- [[`c9b652f13f`](https://github.com/nodejs/node/commit/c9b652f13f)] - **vm**: add tests for function declarations using \[\[DefineOwnProperty\]\] (ExE Boss) [#34032](https://github.com/nodejs/node/pull/34032)
- [[`0aa3809b6b`](https://github.com/nodejs/node/commit/0aa3809b6b)] - **(SEMVER-MINOR)** **worker**: make MessagePort inherit from EventTarget (Anna Henningsen) [#34057](https://github.com/nodejs/node/pull/34057)
- [[`252f37630a`](https://github.com/nodejs/node/commit/252f37630a)] - **zlib**: switch to lazy init for zlib streams (Andrey Pechkurov) [#34048](https://github.com/nodejs/node/pull/34048)

Windows 32-bit Installer: https://nodejs.org/dist/v14.7.0/node-v14.7.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v14.7.0/node-v14.7.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v14.7.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v14.7.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v14.7.0/node-v14.7.0.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v14.7.0/node-v14.7.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v14.7.0/node-v14.7.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v14.7.0/node-v14.7.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v14.7.0/node-v14.7.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v14.7.0/node-v14.7.0-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v14.7.0/node-v14.7.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v14.7.0/node-v14.7.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v14.7.0/node-v14.7.0.tar.gz \
Other release files: https://nodejs.org/dist/v14.7.0/ \
Documentation: https://nodejs.org/docs/v14.7.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

c9dc58ddc4eac5db92d7803359d2dc4a90428cc5bab1c91b96c1e2f5717b8caf  node-v14.7.0-aix-ppc64.tar.gz
47c94ec84706fd6851db27af54abdab569941fcbfcdc28e386d8fa7d49c6a619  node-v14.7.0-darwin-x64.tar.gz
4f3f1c989184dd7d9fb937cfd7f23cc41f968b3358f7b0ad4d3085f2ce6bbb4f  node-v14.7.0-darwin-x64.tar.xz
479534bb51eab200643269ee0d84749c5bae3503f008d1c1e9607a734e1ac200  node-v14.7.0-headers.tar.gz
f1dfdcd11049fa8a1c808313be0fbc56af15f91d885b44c3f99ae58b65767f53  node-v14.7.0-headers.tar.xz
64bb4171ad823fafef3d36eea416e8a5ebefa60ce7043eb52a3ece1060b1a115  node-v14.7.0-linux-arm64.tar.gz
12540328aeb5baa524a4e9c3b493b5eceb54cfa630f8bce64c19e674871e2f4b  node-v14.7.0-linux-arm64.tar.xz
cf4080a42c62a14b5dfa5989400d4870364370ca1ed561e834432bdef3538742  node-v14.7.0-linux-armv7l.tar.gz
fb08af54fbd5e6429cba7cb45840b1095f633f228f55a2c5d8840d36ce63c01f  node-v14.7.0-linux-armv7l.tar.xz
f9c1152091f69c08e6cd3de918339232fbb1ea4a714045db019e2f6eecb21c7c  node-v14.7.0-linux-ppc64le.tar.gz
1357929b3989242e6d15d562c77f6360d120a81092740f8c75f42f0c446c3949  node-v14.7.0-linux-ppc64le.tar.xz
5486597c34f8b1fb83b09c101cfe658995c61581eab3786993e07c8a071d6ac8  node-v14.7.0-linux-s390x.tar.gz
20bce6123739afa10472de10b2cca310ddf66d7024180d8c9af58036dbdd6d49  node-v14.7.0-linux-s390x.tar.xz
9c40796c5d1bbbbc27c80b692473a254933fe0b19697d007728b6cf397a2b306  node-v14.7.0-linux-x64.tar.gz
48929b03deb2915b64ba67355d2deffeed3c8df798b0c5f2b821ffc7a8116a23  node-v14.7.0-linux-x64.tar.xz
5d745077263ce4e237158b26ec6137185bdec58e6c8b7095774691dc8ffc91f5  node-v14.7.0.pkg
2a34cc5b7386259b1c601ae9e538a3a10403493fa42d3bcd95004782331a56ef  node-v14.7.0.tar.gz
ca2f1c63f3f2bf22247d7386bfc31e0295caa953f39f7079210170a886288e6f  node-v14.7.0.tar.xz
5b2f2455a407b9a873878d3703006647d394924184ebbe2cbdad5a5725db8a37  node-v14.7.0-win-x64.7z
a899693c9a31089a1eda14b1e613cf8cd60361e6e574b351551d832cf864c8f8  node-v14.7.0-win-x64.zip
8d7a88c1298ed2b6f9c140afc620befd3be9fd216e5c5cfc64a2b3cb799e706f  node-v14.7.0-win-x86.7z
4da7e8ebaf575bef6aca53827447e3ac6d42e7f2fb9d03002045eada4022a1ab  node-v14.7.0-win-x86.zip
900c6d4e694f49e51ccc31e1264ac542343bf35e6b24aa8e62b827d4c0f62e05  node-v14.7.0-x64.msi
25a6e57440232174f056c42d96441e0f66b081ac64cf3da9589b044ce7beb0cb  node-v14.7.0-x86.msi
6d9f7be3621a4d995040812eaaff7fb964515891cc5c461bc3251deb737dd5f9  win-x64/node.exe
203ffb5c4e9351e762af1cbaca1e08cef784784c665a60657d26d10ad200a65c  win-x64/node.lib
53c9b2a5fd950bf3b82a3c56b2289ee3f28d97fe590f7b8fc88e750ea06e8814  win-x64/node_pdb.7z
e9c562de62727dcd2a88b9ead915c1434448fcfe3ab612053e99ccbb71ef1b90  win-x64/node_pdb.zip
11b13c5d2df2dd55df90c4832fcf877f391d3034a4f3d473b6bad8336a636b71  win-x86/node.exe
49b13b05e148d6593269174efc0e537b4bc57290b2703658a26f037a398307b4  win-x86/node.lib
6631d58bf51f3fa1b8dd1bc7d577b7bd1890cc243c1044884e4c34f89bfc8251  win-x86/node_pdb.7z
4728a2ea505d6484439d95410aef56b5e6e63b8b2755a6aa78b6f9ae6aad9148  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQEzBAEBCAAdFiEEDv/hvO/ZyE49CYFSkzsB9AtcqUYFAl8hwqYACgkQkzsB9Atc
qUZJLggAgAEIy8Mcd/BI9AxzTMBmCyYbsaxwGqgZGQyfe05W2MXQy2+SnWRFP1Yx
ZVPPMR41lbFCWhK/SNZQfzNg6NaWm6r/QoPyyj2oiBH4JtuZSC6LLcDeXgGuv9tJ
cObBnTgLf2Ggs60QGmzDn50F6Y1RrAYS+xMXA/wnCKV1rc/ENCepP8j0WtqtJuWD
ebtbexRDxhf9+LhnAvYpLSwKfMcf8Jiw+TxABeQQRlq/8C+BRdCdXnr+L+1Qk1J+
rk6d57sN4Pv5xSCudOrqICB+2SNKqU50v4vy6J72JSpQOS9L0lQMcW/djDyaCy+3
5Dt3iqt9NTPnbWbx0pOdh5NuE4hv8Q==
=qVho
-----END PGP SIGNATURE-----

```
