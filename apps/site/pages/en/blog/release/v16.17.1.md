---
date: '2022-09-23T16:14:42.309Z'
category: release
title: Node v16.17.1 (LTS)
layout: blog-post
author: <PERSON><PERSON>
---

This is a security release.

### Notable changes

The following CVEs are fixed in this release:

- **[CVE-2022-32212](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2022-32212)**: DNS rebinding in --inspect on macOS (High)
- **[CVE-2022-32213](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2022-32213)**: bypass via obs-fold mechanic (Medium)
- **[CVE-2022-35255](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2022-35255)**: Weak randomness in WebCrypto keygen
- **[CVE-2022-35256](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2022-35256)**: HTTP Request Smuggling - Incorrect Parsing of Header Fields (Medium)

More detailed information on each of the vulnerabilities can be found in [September 22nd 2022 Security Releases](/blog/vulnerability/september-2022-security-releases/) blog post.

### Commits

- \[[`a54283a638`](https://github.com/nodejs/node/commit/a54283a638)] - **crypto**: fix weak randomness in WebCrypto keygen (Ben Noordhuis) [nodejs-private/node-private#346](https://github.com/nodejs-private/node-private/pull/346)
- \[[`0713e21240`](https://github.com/nodejs/node/commit/0713e21240)] - **http**: disable chunked encoding when using OBS fold is used (Paolo Insogna) [nodejs-private/node-private#341](https://github.com/nodejs-private/node-private/pull/341)
- \[[`77fe2f32e4`](https://github.com/nodejs/node/commit/77fe2f32e4)] - **src**: fix IPv4 non routable validation (RafaelGSS) [nodejs-private/node-private#337](https://github.com/nodejs-private/node-private/pull/337)

Windows 32-bit Installer: https://nodejs.org/dist/v16.17.1/node-v16.17.1-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v16.17.1/node-v16.17.1-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v16.17.1/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v16.17.1/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v16.17.1/node-v16.17.1.pkg \
macOS Apple Silicon 64-bit Binary: https://nodejs.org/dist/v16.17.1/node-v16.17.1-darwin-arm64.tar.gz \
macOS Intel 64-bit Binary: https://nodejs.org/dist/v16.17.1/node-v16.17.1-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v16.17.1/node-v16.17.1-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v16.17.1/node-v16.17.1-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v16.17.1/node-v16.17.1-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v16.17.1/node-v16.17.1-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v16.17.1/node-v16.17.1-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v16.17.1/node-v16.17.1-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v16.17.1/node-v16.17.1.tar.gz \
Other release files: https://nodejs.org/dist/v16.17.1/ \
Documentation: https://nodejs.org/docs/v16.17.1/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

dfb37570ef34ac04f34c26d0ec558df60a9665df5961c01c1657c0ca495f2f01  node-v16.17.1-aix-ppc64.tar.gz
f9f02f7872e2e8ee54320fce13deb9d56904f32bb0615b6e21aa3371d8899150  node-v16.17.1-darwin-arm64.tar.gz
09a45f60bfb9dfbea4f69044dc733ef983945acd92ca89ccccac267f3d71bd44  node-v16.17.1-darwin-arm64.tar.xz
3db26761ad8493b894d42260d7e65094b7af9bc473588739e61bc1c32d6ff955  node-v16.17.1-darwin-x64.tar.gz
8e7089956fa01cf7d0045945c0863d282dc6818fb0476237c1396497e29a4254  node-v16.17.1-darwin-x64.tar.xz
35ccb95caf02cda3bd680da4350a8ae5d666a7a9eae3afe5c2a1b3ef29aef108  node-v16.17.1-headers.tar.gz
554c8d1b4b16e0f4c073b9df7c49c893716a3a533f25ac646f23619f5ccee7df  node-v16.17.1-headers.tar.xz
adc7032888d4e672a4aac886baede8c04fccdd1a2e7ab4bcf325e3f336f44a3d  node-v16.17.1-linux-arm64.tar.gz
3dfb8fd8f6b97df69cdc56524abc906c50ef1d0bf091188616802e6c7c731389  node-v16.17.1-linux-arm64.tar.xz
aeab05e35f1d2824ecfb88ca321f1408b44d292b2775f2890972c828e00216d0  node-v16.17.1-linux-armv7l.tar.gz
a035ceefb5e16f5fce98c8ddfdf721b96eec20542c72fb8781bcbb6ef20c5550  node-v16.17.1-linux-armv7l.tar.xz
1f48de7bed99e973c4c50f1b7fc99fc9af5144d093fd6d2b50a1e43b5818bf05  node-v16.17.1-linux-ppc64le.tar.gz
70305934661f89fca64053b85317a75f233d5e3fdb2caa6546a19262a519cf20  node-v16.17.1-linux-ppc64le.tar.xz
029dad48018bda07b481213816549b632059fc673c30fdc7a353e04619128344  node-v16.17.1-linux-s390x.tar.gz
1a47f604944c6aff37cb7483503155671cdb34bda9bfb8962007bc440fa04d77  node-v16.17.1-linux-s390x.tar.xz
da5658693243b3ecf6a4cba6751a71df1eb9e9703ca93b42a9404aed85f58ad0  node-v16.17.1-linux-x64.tar.gz
06ba2eb34aa385967f5f58c87a44753f83212f6cccea892b33f80a2e7fda8384  node-v16.17.1-linux-x64.tar.xz
12d10476ea7483298364c810c037b9316d1a73dc8c81cfeff7d794aecadde498  node-v16.17.1.pkg
e423985f6019b2026f9a191adb56a96ae83ecd56cdf839cf94aa980168b7a90f  node-v16.17.1.tar.gz
6721feb4152d56d2c6b358ce397abd5a7f1daf09ee2e25c5021b9b4d3f86a330  node-v16.17.1.tar.xz
9777e8c4b2864c5b54a0e4e9400f14887db68560a09b94b4113b560a64d1e680  node-v16.17.1-win-x64.7z
ed290151efb417262b9808a70738d4ab79e9d53653a6a9f4b8dd97912e279dce  node-v16.17.1-win-x64.zip
0f8101648d5c9e49e89fee541da9e574f899716c32b7c51a732b1766b9fc4526  node-v16.17.1-win-x86.7z
189b5e8b23226403e7b07a46614de19b444d369e694901e3668e2f549799cbcd  node-v16.17.1-win-x86.zip
1bdff65fb7642425c0d6826084d63c4be43520316f0ea0b46e6a51999a0ed7fc  node-v16.17.1-x64.msi
b737eb23a2c67c253b9364b5284123faf5220d567615bebd4ec4b81070e4d177  node-v16.17.1-x86.msi
f518a70dcab7c3fac5b2e1ef100b4f628edfb160f4fafa9a94ef222da8a6e9ab  win-x64/node.exe
2f459a64647db493da63c790ce368ad54f59f086d9f22f59c5018680420197b3  win-x64/node.lib
23215ce7d1e9de9777c3407239e7cf18d29d60f757b772219421ab361ac67c74  win-x64/node_pdb.7z
8e32ec12028fd3e3147435be79a858ed9c870aaafa1fcb291362307ef3c47547  win-x64/node_pdb.zip
2393aff88be19dbe0205cbde4ff0c1d89911b15de5c99c80f6e5e29604eecd12  win-x86/node.exe
5018c3d42f3fbacbd06cb943b3f2696c8e67ca9bdf6864d0e263d6d6911dffd2  win-x86/node.lib
05a4db56444a60ee70b0d2642d7f2d82a33339894d2d73bd07b1a41d6c869e04  win-x86/node_pdb.7z
8f86eacb7f13a1bf6738cb0819d7854a2abca40fc2e9e1f91421e44ba52cad7e  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEEI9StI21e7DMQ5spl7AUGb2S+AoFAmMt2HIACgkQl7AUGb2S
+ArUiA/+L6DFxzecIzHsKCgbByVvl6H/fbyE5d5QJE8x9EdhNWkydDj9OptMGdyz
R/SX/YqMIGLaEj5TnQEx6QJqwr1Lh0j7wWdHQVwgS/T3ISH4BRRfF/me+MKknWGd
B7S0+AR2CQf7lpTohpAv86Ol3kQZ1uyUzIVWxx8zBXxI6u3vqyCh/w74m43x5Zg1
akDqyayW3IuLHCmwAnTzOJF2gPkhAh48fcygB1sVs1Lc+sMEHroqlst4JYxeAp+8
USsOtqFpQsXSZGl0jzwrsp305S1Nx96Jlw67N42VAHISwvSz4AyAYrlANP+ny2tK
GPxY/VgDS3IyeOZssEbuoasAfxWL2AtX0MwC/GmJwuhneAwxAvIys5Tu9Vy0LgAq
YlUA/Q9wPlh7fd7GN03VFU7qmg8Pa1MA/0psN48PJYW5bF6n3qevPO+pESPyIZxD
8OQgBEI0QRuGTW4lCtCPIt4lFohl4m2oBaR9koSRyUb5+PYjcM+DbSCS/ghc83ay
gvgBp5bzC+AXOPcasZ/AU7N2ripWVg1EKFQc/mJWT/330O5327H/hS34eKr7l0NS
riTdgQUqIO6jH4BS1L/2zFHl6mzdWNw2FFsERQIs3Rp/b9gSYxNgIMXeQKRx9M7a
M+8nKnLKi5g0qtJbuOlJS7UFuyMdhMhyku8bG0QH0Ps4vUZ7SOE=
=byYq
-----END PGP SIGNATURE-----

```
