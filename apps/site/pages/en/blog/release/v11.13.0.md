---
date: '2019-03-28T19:33:10.784Z'
category: release
title: Node v11.13.0 (Current)
layout: blog-post
author: <PERSON><PERSON><PERSON>
---

### Notable Changes

- **crypto**
  - Allow deriving public from private keys (<PERSON>) [#26278](https://github.com/nodejs/node/pull/26278).
- **events**
  - Added a `once` function to use `EventEmitter` with promises (<PERSON>) [#26078](https://github.com/nodejs/node/pull/26078).
- **tty**
  - Added a `hasColors` method to `WriteStream` (<PERSON><PERSON>) [#26247](https://github.com/nodejs/node/pull/26247).
  - Added NO_COLOR and FORCE_COLOR support (<PERSON><PERSON>) [#26485](https://github.com/nodejs/node/pull/26485).
- **v8**
  - Added `v8.getHeapSnapshot` and `v8.writeHeapSnapshot` to generate snapshots in the format used by tools such as Chrome DevTools (<PERSON>) [#26501](https://github.com/nodejs/node/pull/26501).
- **worker**
  - Added `worker.moveMessagePortToContext`. This enables using MessagePorts in different vm.Contexts, aiding with the isolation that the vm module seeks to provide (Anna Henningsen) [#26497](https://github.com/nodejs/node/pull/26497).
- **C++ API**
  - `AddPromiseHook` is now deprecated. This API was added to fill a use case that is served by `async_hooks`, since that has `Promise` support (Anna Henningsen) [#26529](https://github.com/nodejs/node/pull/26529).
  - Added a `Stop` API to shut down Node.js while it is running (Gireesh Punathil) [#21283](https://github.com/nodejs/node/pull/21283).
- **meta**
  - [Gireesh Punathil](https://github.com/gireeshpunathil) is now a member of the Technical Steering Committee [#26657](https://github.com/nodejs/node/pull/26657).
  - Added [Yongsheng Zhang](https://github.com/ZYSzys) to collaborators [#26730](https://github.com/nodejs/node/pull/26730).

### Commits

- [[`a2d2756792`](https://github.com/nodejs/node/commit/a2d2756792)] - **assert**: reduce internal usage of public require of util (toshi1127) [#26750](https://github.com/nodejs/node/pull/26750)
- [[`db7c4ac40b`](https://github.com/nodejs/node/commit/db7c4ac40b)] - **assert**: reduce internal usage of public require of util (Daiki Ihara) [#26762](https://github.com/nodejs/node/pull/26762)
- [[`3ab438aa17`](https://github.com/nodejs/node/commit/3ab438aa17)] - **benchmark**: replace deprecated and eliminate var in buffer-from.js (gengjiawen) [#26585](https://github.com/nodejs/node/pull/26585)
- [[`0e4ae00676`](https://github.com/nodejs/node/commit/0e4ae00676)] - **benchmark**: use gfm for clarity (gengjiawen) [#26710](https://github.com/nodejs/node/pull/26710)
- [[`509ad40348`](https://github.com/nodejs/node/commit/509ad40348)] - **build**: restore running tests on Travis (Richard Lau) [#26720](https://github.com/nodejs/node/pull/26720)
- [[`b480c792be`](https://github.com/nodejs/node/commit/b480c792be)] - **build**: temporarily don't run tests on Travis (Richard Lau) [#26720](https://github.com/nodejs/node/pull/26720)
- [[`4163864be5`](https://github.com/nodejs/node/commit/4163864be5)] - **build**: use Xenial and gcc 6 on Travis (Richard Lau) [#26720](https://github.com/nodejs/node/pull/26720)
- [[`e39a468cdc`](https://github.com/nodejs/node/commit/e39a468cdc)] - **child_process**: ensure message sanity at source (Gireesh Punathil) [#24787](https://github.com/nodejs/node/pull/24787)
- [[`f263f98d5a`](https://github.com/nodejs/node/commit/f263f98d5a)] - **console**: remove unreachable code (Rich Trott) [#26863](https://github.com/nodejs/node/pull/26863)
- [[`e49cd40789`](https://github.com/nodejs/node/commit/e49cd40789)] - **console**: fix trace function (Ruben Bridgewater) [#26764](https://github.com/nodejs/node/pull/26764)
- [[`f2a07df27f`](https://github.com/nodejs/node/commit/f2a07df27f)] - **crypto**: improve error handling in parseKeyEncoding (Tobias Nießen) [#26455](https://github.com/nodejs/node/pull/26455)
- [[`ed7599bf36`](https://github.com/nodejs/node/commit/ed7599bf36)] - **(SEMVER-MINOR)** **crypto**: allow deriving public from private keys (Tobias Nießen) [#26278](https://github.com/nodejs/node/pull/26278)
- [[`74c6f57aed`](https://github.com/nodejs/node/commit/74c6f57aed)] - **(SEMVER-MINOR)** **crypto**: expose KeyObject class (Filip Skokan) [#26438](https://github.com/nodejs/node/pull/26438)
- [[`54ffe61c56`](https://github.com/nodejs/node/commit/54ffe61c56)] - **deps**: upgrade to libuv 1.27.0 (cjihrig) [#26707](https://github.com/nodejs/node/pull/26707)
- [[`dae1e301c6`](https://github.com/nodejs/node/commit/dae1e301c6)] - **dgram**: remove usage of public require('util') (dnlup) [#26770](https://github.com/nodejs/node/pull/26770)
- [[`119f83bb44`](https://github.com/nodejs/node/commit/119f83bb44)] - **doc**: mark settings as optional and add callback (Ruben Bridgewater) [#26894](https://github.com/nodejs/node/pull/26894)
- [[`a545cfe293`](https://github.com/nodejs/node/commit/a545cfe293)] - **doc**: edit "How Can I Help?" in Collaborator Guide (Rich Trott) [#26895](https://github.com/nodejs/node/pull/26895)
- [[`14cc4f220c`](https://github.com/nodejs/node/commit/14cc4f220c)] - **doc**: add option to require 'process' to api docs (dkundel) [#26792](https://github.com/nodejs/node/pull/26792)
- [[`977f5acd04`](https://github.com/nodejs/node/commit/977f5acd04)] - **doc**: minor edit to worker_threads.md (Rich Trott) [#26870](https://github.com/nodejs/node/pull/26870)
- [[`78e6ec7dd5`](https://github.com/nodejs/node/commit/78e6ec7dd5)] - **doc**: edit LTS material in Collaborator Guide (Rich Trott) [#26845](https://github.com/nodejs/node/pull/26845)
- [[`7e072c816c`](https://github.com/nodejs/node/commit/7e072c816c)] - **doc**: change error message to 'not defined' (Mohammed Essehemy) [#26857](https://github.com/nodejs/node/pull/26857)
- [[`c7b34cd8ee`](https://github.com/nodejs/node/commit/c7b34cd8ee)] - **doc**: fix comma of the list in worker_threads.md (Hang Jiang) [#26838](https://github.com/nodejs/node/pull/26838)
- [[`560ff919b2`](https://github.com/nodejs/node/commit/560ff919b2)] - **doc**: remove discord community (Aymen Naghmouchi) [#26830](https://github.com/nodejs/node/pull/26830)
- [[`fc0aa50c3d`](https://github.com/nodejs/node/commit/fc0aa50c3d)] - **doc**: remove How Does LTS Work section from Collaborator Guide (Rich Trott) [#26723](https://github.com/nodejs/node/pull/26723)
- [[`bc9f6d877a`](https://github.com/nodejs/node/commit/bc9f6d877a)] - **doc**: condense LTS material in Collaborator Guide (Rich Trott) [#26722](https://github.com/nodejs/node/pull/26722)
- [[`8de9fe94a0`](https://github.com/nodejs/node/commit/8de9fe94a0)] - **doc**: document `error` event is optionally emitted after `.destroy()` (Sergey Zelenov) [#26589](https://github.com/nodejs/node/pull/26589)
- [[`148c2ca33d`](https://github.com/nodejs/node/commit/148c2ca33d)] - **doc**: add Note of options.stdio into child_process (kohta ito) [#26604](https://github.com/nodejs/node/pull/26604)
- [[`0303aba162`](https://github.com/nodejs/node/commit/0303aba162)] - **doc**: update spawnSync() status value possibilities (Rich Trott) [#26680](https://github.com/nodejs/node/pull/26680)
- [[`6744b8cb43`](https://github.com/nodejs/node/commit/6744b8cb43)] - **doc**: add ZYSzys to collaborators (ZYSzys) [#26730](https://github.com/nodejs/node/pull/26730)
- [[`0c06631a71`](https://github.com/nodejs/node/commit/0c06631a71)] - **doc**: simplify force-push guidelines (Rich Trott) [#26699](https://github.com/nodejs/node/pull/26699)
- [[`b38cf49094`](https://github.com/nodejs/node/commit/b38cf49094)] - **doc**: make RFC references consistent (Rich Trott) [#26727](https://github.com/nodejs/node/pull/26727)
- [[`1f0a2835f4`](https://github.com/nodejs/node/commit/1f0a2835f4)] - **doc**: note about DNS ANY queries / RFC 8482 (Thomas Hunter II) [#26695](https://github.com/nodejs/node/pull/26695)
- [[`cfa152b589`](https://github.com/nodejs/node/commit/cfa152b589)] - **doc**: simplify Troubleshooting text (Rich Trott) [#26652](https://github.com/nodejs/node/pull/26652)
- [[`e8e8eac96c`](https://github.com/nodejs/node/commit/e8e8eac96c)] - **doc**: update copy/paste error message in Troubleshooting (Rich Trott) [#26652](https://github.com/nodejs/node/pull/26652)
- [[`3b471db14a`](https://github.com/nodejs/node/commit/3b471db14a)] - **doc**: add Gireesh to TSC (Rich Trott) [#26657](https://github.com/nodejs/node/pull/26657)
- [[`058cf43a3c`](https://github.com/nodejs/node/commit/058cf43a3c)] - **doc**: edit "Technical How-To" section of guide (Rich Trott) [#26601](https://github.com/nodejs/node/pull/26601)
- [[`9a5c1495b1`](https://github.com/nodejs/node/commit/9a5c1495b1)] - **errors**: remove usage of require('util') (dnlup) [#26781](https://github.com/nodejs/node/pull/26781)
- [[`7022609dcc`](https://github.com/nodejs/node/commit/7022609dcc)] - **events**: load internal/errors eagerly (Joyee Cheung) [#26771](https://github.com/nodejs/node/pull/26771)
- [[`df55731918`](https://github.com/nodejs/node/commit/df55731918)] - **(SEMVER-MINOR)** **events**: add once method to use promises with EventEmitter (Matteo Collina) [#26078](https://github.com/nodejs/node/pull/26078)
- [[`c96946d5f3`](https://github.com/nodejs/node/commit/c96946d5f3)] - **http**: delay ret declaration in method \_flushOutput (gengjiawen) [#26562](https://github.com/nodejs/node/pull/26562)
- [[`15af5193af`](https://github.com/nodejs/node/commit/15af5193af)] - **http2**: reduce usage of require('util') (toshi1127) [#26784](https://github.com/nodejs/node/pull/26784)
- [[`1073e54ad6`](https://github.com/nodejs/node/commit/1073e54ad6)] - **http2**: delete unused enum in node_http2.h (gengjiawen) [#26704](https://github.com/nodejs/node/pull/26704)
- [[`3574b62717`](https://github.com/nodejs/node/commit/3574b62717)] - **inspector**: always set process.binding('inspector').callAndPauseOnStart (Joyee Cheung) [#26793](https://github.com/nodejs/node/pull/26793)
- [[`cc4a25a1a9`](https://github.com/nodejs/node/commit/cc4a25a1a9)] - **lib**: lazy load `v8` in error-serdes (Richard Lau) [#26689](https://github.com/nodejs/node/pull/26689)
- [[`5f3b850da5`](https://github.com/nodejs/node/commit/5f3b850da5)] - **lib**: reduce usage of require('util') (dnlup) [#26782](https://github.com/nodejs/node/pull/26782)
- [[`bf2b57e46f`](https://github.com/nodejs/node/commit/bf2b57e46f)] - **lib**: remove usage of require('util') (dnlup) [#26779](https://github.com/nodejs/node/pull/26779)
- [[`64a92290c0`](https://github.com/nodejs/node/commit/64a92290c0)] - **lib**: remove usage of require('util') (dnlup) [#26777](https://github.com/nodejs/node/pull/26777)
- [[`bff5d301bf`](https://github.com/nodejs/node/commit/bff5d301bf)] - **lib**: move extra properties into error creation (Ruben Bridgewater) [#26752](https://github.com/nodejs/node/pull/26752)
- [[`e916a2ad54`](https://github.com/nodejs/node/commit/e916a2ad54)] - **lib**: remove usage of require('util') (dnlup) [#26773](https://github.com/nodejs/node/pull/26773)
- [[`cc76f3f152`](https://github.com/nodejs/node/commit/cc76f3f152)] - **lib**: use Array#includes instead of Array#indexOf (Weijia Wang) [#26732](https://github.com/nodejs/node/pull/26732)
- [[`a44f98e333`](https://github.com/nodejs/node/commit/a44f98e333)] - **lib**: run prepareMainThreadExecution for third_party_main (Anna Henningsen) [#26677](https://github.com/nodejs/node/pull/26677)
- [[`1c1305dbc1`](https://github.com/nodejs/node/commit/1c1305dbc1)] - **lib**: make lowerProto scope more clear (gengjiawen) [#26562](https://github.com/nodejs/node/pull/26562)
- [[`9ce08c85e7`](https://github.com/nodejs/node/commit/9ce08c85e7)] - **lib**: explicitly initialize debuglog during bootstrap (Joyee Cheung) [#26468](https://github.com/nodejs/node/pull/26468)
- [[`b75af1537d`](https://github.com/nodejs/node/commit/b75af1537d)] - **lib**: move format and formatWithOptions into internal/util/inspect.js (Joyee Cheung) [#26468](https://github.com/nodejs/node/pull/26468)
- [[`235bb733a6`](https://github.com/nodejs/node/commit/235bb733a6)] - **module**: do not share the internal require function with public loaders (Joyee Cheung) [#26549](https://github.com/nodejs/node/pull/26549)
- [[`4cafd7419d`](https://github.com/nodejs/node/commit/4cafd7419d)] - **module**: remove usage of require('util') in `esm/translators.js` (dnlup) [#26806](https://github.com/nodejs/node/pull/26806)
- [[`037e3fddfa`](https://github.com/nodejs/node/commit/037e3fddfa)] - **module**: remove usage of require('util') in `esm/loader.js` (dnlup) [#26804](https://github.com/nodejs/node/pull/26804)
- [[`414d6f5e04`](https://github.com/nodejs/node/commit/414d6f5e04)] - **module**: remove usage of require('util') in `cjs/loader.js` (dnlup) [#26802](https://github.com/nodejs/node/pull/26802)
- [[`fbe6d30bcf`](https://github.com/nodejs/node/commit/fbe6d30bcf)] - **module**: remove usage of require('util') (dnlup) [#26805](https://github.com/nodejs/node/pull/26805)
- [[`a20bf75e06`](https://github.com/nodejs/node/commit/a20bf75e06)] - **_Revert_** "**net**: remove usage of require('util')" (Rich Trott) [#26896](https://github.com/nodejs/node/pull/26896)
- [[`5e06c3bc0b`](https://github.com/nodejs/node/commit/5e06c3bc0b)] - **net**: remove usage of require('util') (dnlup) [#26807](https://github.com/nodejs/node/pull/26807)
- [[`24e96b24cf`](https://github.com/nodejs/node/commit/24e96b24cf)] - **net**: some scattered cleanup (oyyd) [#24128](https://github.com/nodejs/node/pull/24128)
- [[`de353b75d5`](https://github.com/nodejs/node/commit/de353b75d5)] - **perf_hooks**: load internal/errors eagerly (Joyee Cheung) [#26771](https://github.com/nodejs/node/pull/26771)
- [[`0bd82c93c6`](https://github.com/nodejs/node/commit/0bd82c93c6)] - **perf_hooks**: reset prev\_ before starting ELD timer (Gerhard Stoebich) [#26693](https://github.com/nodejs/node/pull/26693)
- [[`c127bec4ab`](https://github.com/nodejs/node/commit/c127bec4ab)] - **policy**: reduce internal usage of public util for manifest.js (Jesse Katsumata) [#26833](https://github.com/nodejs/node/pull/26833)
- [[`899de0a7c7`](https://github.com/nodejs/node/commit/899de0a7c7)] - **process**: check no handle or request is active after bootstrap (Joyee Cheung) [#26593](https://github.com/nodejs/node/pull/26593)
- [[`57d302b563`](https://github.com/nodejs/node/commit/57d302b563)] - **process**: delay creation of process.env after bootstrap/node.js (Joyee Cheung) [#26515](https://github.com/nodejs/node/pull/26515)
- [[`255de69596`](https://github.com/nodejs/node/commit/255de69596)] - **process**: refactor global.queueMicrotask() (Joyee Cheung) [#26523](https://github.com/nodejs/node/pull/26523)
- [[`1481e5b5c1`](https://github.com/nodejs/node/commit/1481e5b5c1)] - **process**: set the trace category update handler during bootstrap (Joyee Cheung) [#26605](https://github.com/nodejs/node/pull/26605)
- [[`be3ea2a1eb`](https://github.com/nodejs/node/commit/be3ea2a1eb)] - **process**: handle node --debug deprecation in pre-execution (Joyee Cheung) [#26670](https://github.com/nodejs/node/pull/26670)
- [[`8b65aa73f6`](https://github.com/nodejs/node/commit/8b65aa73f6)] - **process**: make stdout and stderr emit 'close' on destroy (Matteo Collina) [#26691](https://github.com/nodejs/node/pull/26691)
- [[`dd2f2cca00`](https://github.com/nodejs/node/commit/dd2f2cca00)] - **process**: remove usage of require('util') in `per_thread.js` (dnlup) [#26817](https://github.com/nodejs/node/pull/26817)
- [[`41761cc4a6`](https://github.com/nodejs/node/commit/41761cc4a6)] - **process**: load internal/async_hooks before inspector hooks registration (Joyee Cheung) [#26866](https://github.com/nodejs/node/pull/26866)
- [[`b0afac2833`](https://github.com/nodejs/node/commit/b0afac2833)] - **process**: call prepareMainThreadExecution in all main thread scripts (Joyee Cheung) [#26468](https://github.com/nodejs/node/pull/26468)
- [[`cf1117a818`](https://github.com/nodejs/node/commit/cf1117a818)] - **process**: move deprecation warning setup for --debug\* args (Refael Ackermann) [#26662](https://github.com/nodejs/node/pull/26662)
- [[`4200fc30bd`](https://github.com/nodejs/node/commit/4200fc30bd)] - **process**: handle process.env.NODE_V8_COVERAGE in pre-execution (Joyee Cheung) [#26466](https://github.com/nodejs/node/pull/26466)
- [[`cc606e2dfc`](https://github.com/nodejs/node/commit/cc606e2dfc)] - **process**: set up process warning handler in pre-execution (Joyee Cheung) [#26466](https://github.com/nodejs/node/pull/26466)
- [[`03dba720da`](https://github.com/nodejs/node/commit/03dba720da)] - **process**: call `prepareMainThreadExecution` in `node inspect` (Joyee Cheung) [#26466](https://github.com/nodejs/node/pull/26466)
- [[`04e9d5a448`](https://github.com/nodejs/node/commit/04e9d5a448)] - **repl**: remove usage of require('util') in `repl/history` (dnlup) [#26819](https://github.com/nodejs/node/pull/26819)
- [[`e8412bc213`](https://github.com/nodejs/node/commit/e8412bc213)] - **repl**: remove redundant initialization (gengjiawen) [#26562](https://github.com/nodejs/node/pull/26562)
- [[`5b8eae4ea7`](https://github.com/nodejs/node/commit/5b8eae4ea7)] - **report**: remove duplicate TIME_TYPE (cjihrig) [#26708](https://github.com/nodejs/node/pull/26708)
- [[`01778f525b`](https://github.com/nodejs/node/commit/01778f525b)] - **report**: tidy up included headers (Richard Lau) [#26697](https://github.com/nodejs/node/pull/26697)
- [[`5c4187638c`](https://github.com/nodejs/node/commit/5c4187638c)] - **report**: use LocalTime from DiagnosticFilename (Richard Lau) [#26647](https://github.com/nodejs/node/pull/26647)
- [[`e3bae20941`](https://github.com/nodejs/node/commit/e3bae20941)] - **report**: use DiagnosticFilename for default filename (Richard Lau) [#26647](https://github.com/nodejs/node/pull/26647)
- [[`1b4553401c`](https://github.com/nodejs/node/commit/1b4553401c)] - **report**: remove unnecessary return in setters (Rich Trott) [#26614](https://github.com/nodejs/node/pull/26614)
- [[`f50c9c6ae2`](https://github.com/nodejs/node/commit/f50c9c6ae2)] - **src**: move ShouldNotAbortOnUncaughtScope out of Environment (Joyee Cheung) [#26824](https://github.com/nodejs/node/pull/26824)
- [[`7e7f07755c`](https://github.com/nodejs/node/commit/7e7f07755c)] - **src**: move TrackingTraceStateObserver out of Environment (Joyee Cheung) [#26824](https://github.com/nodejs/node/pull/26824)
- [[`bc69a81276`](https://github.com/nodejs/node/commit/bc69a81276)] - **src**: move TickInfo out of Environment (Joyee Cheung) [#26824](https://github.com/nodejs/node/pull/26824)
- [[`495e5e9e75`](https://github.com/nodejs/node/commit/495e5e9e75)] - **src**: move ImmediateInfo out of Environment (Joyee Cheung) [#26824](https://github.com/nodejs/node/pull/26824)
- [[`6de1220cc4`](https://github.com/nodejs/node/commit/6de1220cc4)] - **src**: move AsyncCallbackScope out of Environment (Joyee Cheung) [#26824](https://github.com/nodejs/node/pull/26824)
- [[`4af9ff00ff`](https://github.com/nodejs/node/commit/4af9ff00ff)] - **src**: move AsyncHooks out of Environment (Joyee Cheung) [#26824](https://github.com/nodejs/node/pull/26824)
- [[`3d9839ba3f`](https://github.com/nodejs/node/commit/3d9839ba3f)] - **src**: add include guard for trace_event_common.h (gengjiawen) [#26883](https://github.com/nodejs/node/pull/26883)
- [[`13eb1d8f8a`](https://github.com/nodejs/node/commit/13eb1d8f8a)] - **src**: store onread callback in internal field (Anna Henningsen) [#26837](https://github.com/nodejs/node/pull/26837)
- [[`220f67c6ce`](https://github.com/nodejs/node/commit/220f67c6ce)] - **src**: guard exit label when inspector disabled (Daniel Bevenius) [#26801](https://github.com/nodejs/node/pull/26801)
- [[`54753f2446`](https://github.com/nodejs/node/commit/54753f2446)] - **src**: micro-optimize ALPN negotiation (Ben Noordhuis) [#26836](https://github.com/nodejs/node/pull/26836)
- [[`6de2437c0f`](https://github.com/nodejs/node/commit/6de2437c0f)] - **src**: apply clang-tidy readability-delete-null-pointer (gengjiawen) [#26813](https://github.com/nodejs/node/pull/26813)
- [[`de5034643f`](https://github.com/nodejs/node/commit/de5034643f)] - **src**: apply clang-tidy performance-faster-string-find (gengjiawen) [#26812](https://github.com/nodejs/node/pull/26812)
- [[`79d6895484`](https://github.com/nodejs/node/commit/79d6895484)] - **src**: initialize worker's stack_base\_ field (cjihrig) [#26739](https://github.com/nodejs/node/pull/26739)
- [[`6911678f9e`](https://github.com/nodejs/node/commit/6911678f9e)] - **src**: use explicit casts to silence conversion warnings (Zach Bjornson) [#26766](https://github.com/nodejs/node/pull/26766)
- [[`26361d1a5f`](https://github.com/nodejs/node/commit/26361d1a5f)] - **src**: add fast path for equal size to `Reallocate()` (Anna Henningsen) [#26573](https://github.com/nodejs/node/pull/26573)
- [[`f597b37efb`](https://github.com/nodejs/node/commit/f597b37efb)] - **src**: do not make `Resize(0)`’d buffers base `nullptr` (Anna Henningsen) [#26731](https://github.com/nodejs/node/pull/26731)
- [[`14c3af7f3e`](https://github.com/nodejs/node/commit/14c3af7f3e)] - **src**: only open HandleScope when necessary (Anna Henningsen) [#26734](https://github.com/nodejs/node/pull/26734)
- [[`ad5d8e308c`](https://github.com/nodejs/node/commit/ad5d8e308c)] - **src**: refactor thread stopping mechanism (Anna Henningsen) [#26757](https://github.com/nodejs/node/pull/26757)
- [[`d075814149`](https://github.com/nodejs/node/commit/d075814149)] - **src**: replace heap_utils.createHeapSnapshot with v8.getHeapSnapshot (Joyee Cheung) [#26671](https://github.com/nodejs/node/pull/26671)
- [[`eafbfadec3`](https://github.com/nodejs/node/commit/eafbfadec3)] - **src**: elevate v8 namespaces for PropertyAttribute (gengjiawen) [#26681](https://github.com/nodejs/node/pull/26681)
- [[`15ec381944`](https://github.com/nodejs/node/commit/15ec381944)] - **src**: use EVPKeyPointer in more places (Ben Noordhuis) [#26632](https://github.com/nodejs/node/pull/26632)
- [[`2d2b6a8c23`](https://github.com/nodejs/node/commit/2d2b6a8c23)] - **src**: remove unused variable in class InspectorSocketServer (gengjiawen) [#26633](https://github.com/nodejs/node/pull/26633)
- [[`3637e71328`](https://github.com/nodejs/node/commit/3637e71328)] - **src**: use deleted function instead of private function in class AsyncWrap (gengjiawen) [#26634](https://github.com/nodejs/node/pull/26634)
- [[`51b8a891d8`](https://github.com/nodejs/node/commit/51b8a891d8)] - **src**: inline macro DISALLOW_COPY_AND_ASSIGN (gengjiawen) [#26634](https://github.com/nodejs/node/pull/26634)
- [[`6c90b7f259`](https://github.com/nodejs/node/commit/6c90b7f259)] - **(SEMVER-MINOR)** **src**: shutdown node in-flight (Gireesh Punathil) [#21283](https://github.com/nodejs/node/pull/21283)
- [[`925b645d60`](https://github.com/nodejs/node/commit/925b645d60)] - **src**: remove usage of deprecated IsNearDeath (Michaël Zasso) [#26630](https://github.com/nodejs/node/pull/26630)
- [[`d0801a1c4a`](https://github.com/nodejs/node/commit/d0801a1c4a)] - **(SEMVER-MINOR)** **src**: deprecate AddPromiseHook() (Anna Henningsen) [#26529](https://github.com/nodejs/node/pull/26529)
- [[`a13f0a6362`](https://github.com/nodejs/node/commit/a13f0a6362)] - **(SEMVER-MINOR)** **src**: add public API for linked bindings (Anna Henningsen) [#26457](https://github.com/nodejs/node/pull/26457)
- [[`1e669b2e2e`](https://github.com/nodejs/node/commit/1e669b2e2e)] - **(SEMVER-MINOR)** **src,lib**: make DOMException available in all Contexts (Anna Henningsen) [#26497](https://github.com/nodejs/node/pull/26497)
- [[`e044563bb0`](https://github.com/nodejs/node/commit/e044563bb0)] - **(SEMVER-MINOR)** **src,lib**: allow running multiple per-context files (Anna Henningsen) [#26497](https://github.com/nodejs/node/pull/26497)
- [[`8ba0da57a4`](https://github.com/nodejs/node/commit/8ba0da57a4)] - **src,win**: fix usage of deprecated v8::Object::Set (Michaël Zasso) [#26735](https://github.com/nodejs/node/pull/26735)
- [[`249bf509a3`](https://github.com/nodejs/node/commit/249bf509a3)] - **stream**: fix regression introduced in #26059 (Matteo Collina) [#26643](https://github.com/nodejs/node/pull/26643)
- [[`0b2f900c9a`](https://github.com/nodejs/node/commit/0b2f900c9a)] - **stream**: make sure 'readable' is emitted before ending the stream (Matteo Collina) [#26059](https://github.com/nodejs/node/pull/26059)
- [[`b552139554`](https://github.com/nodejs/node/commit/b552139554)] - **stream**: reduce internal usage of public require of util (Beni von Cheni) [#26698](https://github.com/nodejs/node/pull/26698)
- [[`9ef0a295cf`](https://github.com/nodejs/node/commit/9ef0a295cf)] - **test**: refactor trace event category tests (Joyee Cheung) [#26605](https://github.com/nodejs/node/pull/26605)
- [[`5d992f5ef7`](https://github.com/nodejs/node/commit/5d992f5ef7)] - **test**: delete pummel/test-dtrace-jsstack (Rich Trott) [#26869](https://github.com/nodejs/node/pull/26869)
- [[`3cae010ea0`](https://github.com/nodejs/node/commit/3cae010ea0)] - **test**: refactor test-https-connect-localport (Rich Trott) [#26881](https://github.com/nodejs/node/pull/26881)
- [[`838fb95059`](https://github.com/nodejs/node/commit/838fb95059)] - **test**: replace localhost IP with 'localhost' for TLS conformity (Rich Trott) [#26881](https://github.com/nodejs/node/pull/26881)
- [[`011c205787`](https://github.com/nodejs/node/commit/011c205787)] - **test**: use common.PORT instead of hardcoded number (Rich Trott) [#26881](https://github.com/nodejs/node/pull/26881)
- [[`4919e4b751`](https://github.com/nodejs/node/commit/4919e4b751)] - **test**: move test-https-connect-localport to sequential (Rich Trot) [#26881](https://github.com/nodejs/node/pull/26881)
- [[`57d3ba134a`](https://github.com/nodejs/node/commit/57d3ba134a)] - **test**: refactor test-dgram-broadcast-multi-process (Rich Trott) [#26846](https://github.com/nodejs/node/pull/26846)
- [[`352c31cd7e`](https://github.com/nodejs/node/commit/352c31cd7e)] - **test**: strengthen test-worker-prof (Gireesh Punathil) [#26608](https://github.com/nodejs/node/pull/26608)
- [[`963d7d1f4d`](https://github.com/nodejs/node/commit/963d7d1f4d)] - **test**: move pummel tls test to sequential (Rich Trott) [#26865](https://github.com/nodejs/node/pull/26865)
- [[`8ca7d56b2c`](https://github.com/nodejs/node/commit/8ca7d56b2c)] - **test**: fix pummel/test-tls-session-timeout (Rich Trott) [#26865](https://github.com/nodejs/node/pull/26865)
- [[`41bd7a62e9`](https://github.com/nodejs/node/commit/41bd7a62e9)] - **test**: complete console.assert() coverage (Rich Trott) [#26827](https://github.com/nodejs/node/pull/26827)
- [[`6874288f6e`](https://github.com/nodejs/node/commit/6874288f6e)] - **test**: fix test-console-stdio-setters to test setters (Rich Trott) [#26796](https://github.com/nodejs/node/pull/26796)
- [[`1458711846`](https://github.com/nodejs/node/commit/1458711846)] - **test**: remove internal error tests (Ruben Bridgewater) [#26752](https://github.com/nodejs/node/pull/26752)
- [[`c535e487d6`](https://github.com/nodejs/node/commit/c535e487d6)] - **test**: refresh tmpdir in child-process-server-close (Luigi Pinca) [#26729](https://github.com/nodejs/node/pull/26729)
- [[`7ebd6bdf87`](https://github.com/nodejs/node/commit/7ebd6bdf87)] - **test**: optimize test-http2-large-file (Rich Trott) [#26737](https://github.com/nodejs/node/pull/26737)
- [[`9c83002274`](https://github.com/nodejs/node/commit/9c83002274)] - **test**: use EC cert property now that it exists (Sam Roberts) [#26598](https://github.com/nodejs/node/pull/26598)
- [[`ea425140a1`](https://github.com/nodejs/node/commit/ea425140a1)] - **test**: add fs.watchFile() + worker.terminate() test (Anna Henningsen) [#21179](https://github.com/nodejs/node/pull/21179)
- [[`2d689888b8`](https://github.com/nodejs/node/commit/2d689888b8)] - **test**: update test for libuv update (cjihrig) [#26707](https://github.com/nodejs/node/pull/26707)
- [[`31995e4cd2`](https://github.com/nodejs/node/commit/31995e4cd2)] - **test**: fix intrinsics test (Ruben Bridgewater) [#26660](https://github.com/nodejs/node/pull/26660)
- [[`c65ff3df6d`](https://github.com/nodejs/node/commit/c65ff3df6d)] - **test**: fix test-heapdump-worker (Anna Henningsen) [#26713](https://github.com/nodejs/node/pull/26713)
- [[`875ddcbf10`](https://github.com/nodejs/node/commit/875ddcbf10)] - **test**: remove unnecessary semicolon after macro (Yang Guo) [#26618](https://github.com/nodejs/node/pull/26618)
- [[`892282ddb3`](https://github.com/nodejs/node/commit/892282ddb3)] - **test**: whitelist the expected modules in test-bootstrap-modules.js (Richard Lau) [#26531](https://github.com/nodejs/node/pull/26531)
- [[`e5312585c1`](https://github.com/nodejs/node/commit/e5312585c1)] - **(SEMVER-MINOR)** **test**: make cctest full Node.js environment (Anna Henningsen) [#26457](https://github.com/nodejs/node/pull/26457)
- [[`00a6f7686e`](https://github.com/nodejs/node/commit/00a6f7686e)] - **test,console**: add testing for monkeypatching of console stdio (Rich Trott) [#26561](https://github.com/nodejs/node/pull/26561)
- [[`a640834039`](https://github.com/nodejs/node/commit/a640834039)] - **timers**: move big impl comment to /internal/ (Jeremiah Senkpiel) [#26761](https://github.com/odejs/node/pull/26761)
- [[`3ec652ad38`](https://github.com/nodejs/node/commit/3ec652ad38)] - **timers**: fix refresh inside callback (Anatoli Papirovski) [#26721](https://github.com/nodejs/node/pull/26721)
- [[`1f4a5bcc98`](https://github.com/nodejs/node/commit/1f4a5bcc98)] - **timers**: refactor timer callback initialization (Joyee Cheung) [#26583](https://github.com/nodejs/node/pull/26583)
- [[`ebb0c2a44e`](https://github.com/nodejs/node/commit/ebb0c2a44e)] - **timers**: reduce usage of public util (Joyee Cheung) [#26583](https://github.com/nodejs/node/pull/26583)
- [[`e6367c2da5`](https://github.com/nodejs/node/commit/e6367c2da5)] - **timers**: refactor to use module.exports (Joyee Cheung) [#26583](https://github.com/nodejs/node/pull/26583)
- [[`92b666a6b7`](https://github.com/nodejs/node/commit/92b666a6b7)] - **tools**: windows_boxstarter "choco install python -y" for Python 3 (cclauss) [#26424](https://github.com/nodejs/node/pull/26424)
- [[`d80cd50dbc`](https://github.com/nodejs/node/commit/d80cd50dbc)] - **tools**: remove eslint rule no-let-in-for-declaration (gengjiawen) [#26715](https://github.com/nodejs/node/pull/26715)
- [[`fef2a54a4e`](https://github.com/nodejs/node/commit/fef2a54a4e)] - **tools**: enable getter-return lint rule (cjihrig) [#26615](https://github.com/nodejs/node/pull/26615)
- [[`08383a7bb6`](https://github.com/nodejs/node/commit/08383a7bb6)] - **tools**: update ESLint to 5.15.3 (cjihrig) [#26746](https://github.com/nodejs/node/pull/26746)
- [[`30d7f67e0f`](https://github.com/nodejs/node/commit/30d7f67e0f)] - **tools**: update ESLint to 5.15.2 (cjihrig) [#26687](https://github.com/nodejs/node/pull/26687)
- [[`1385b290ef`](https://github.com/nodejs/node/commit/1385b290ef)] - **tools**: update lint-md.js to lint rfc name format (Rich Trott) [#26727](https://github.com/nodejs/node/pull/26727)
- [[`72cda51440`](https://github.com/nodejs/node/commit/72cda51440)] - **tools**: tidy function arguments in eslint rules (Rich Trott) [#26668](https://github.com/nodejs/node/pull/26668)
- [[`0f9a779da8`](https://github.com/nodejs/node/commit/0f9a779da8)] - **trace_events**: remove usage of require('util') (dnlup) [#26822](https://github.com/nodejs/node/pull/26822)
- [[`83f6ec8876`](https://github.com/nodejs/node/commit/83f6ec8876)] - **tty**: remove util.inherits usage (nd-02110114) [#26797](https://github.com/nodejs/node/pull/26797)
- [[`8cafd83ba7`](https://github.com/nodejs/node/commit/8cafd83ba7)] - **(SEMVER-MINOR)** **tty**: add NO_COLOR and FORCE_COLOR support (Ruben Bridgewater) [#26485](https://github.com/nodejs/node/pull/26485)
- [[`070faf0bc1`](https://github.com/nodejs/node/commit/070faf0bc1)] - **(SEMVER-MINOR)** **tty**: add hasColors function (Ruben Bridgewater) [#26247](https://github.com/nodejs/node/pull/26247)
- [[`04c7db3638`](https://github.com/nodejs/node/commit/04c7db3638)] - **url**: remove usage of require('util') (toshi1127) [#26808](https://github.com/nodejs/node/pull/26808)
- [[`9092e12b82`](https://github.com/nodejs/node/commit/9092e12b82)] - **(SEMVER-MINOR)** **v8**: integrate node-heapdump into core (James M Snell) [#26501](https://github.com/nodejs/node/pull/26501)
- [[`4314dbfce9`](https://github.com/nodejs/node/commit/4314dbfce9)] - **worker**: create per-Environment message port after bootstrap (Joyee Cheung) [#26593](https://github.com/nodejs/node/pull/26593)
- [[`3c6f12c965`](https://github.com/nodejs/node/commit/3c6f12c965)] - **(SEMVER-MINOR)** **worker**: implement worker.moveMessagePortToContext() (Anna Henningsen) [#26497](https://github.com/nodejs/node/pull/26497)

Windows 32-bit Installer: https://nodejs.org/dist/v11.13.0/node-v11.13.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v11.13.0/node-v11.13.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v11.13.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v11.13.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v11.13.0/node-v11.13.0.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v11.13.0/node-v11.13.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v11.13.0/node-v11.13.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v11.13.0/node-v11.13.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v11.13.0/node-v11.13.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v11.13.0/node-v11.13.0-aix-ppc64.tar.gz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v11.13.0/node-v11.13.0-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v11.13.0/node-v11.13.0-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v11.13.0/node-v11.13.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v11.13.0/node-v11.13.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v11.13.0/node-v11.13.0.tar.gz \
Other release files: https://nodejs.org/dist/v11.13.0/ \
Documentation: https://nodejs.org/docs/v11.13.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

194cb9007c4db2a38a17ffabaa334166e6506274bcb23afa28a538838c180e35  node-v11.13.0-aix-ppc64.tar.gz
c1a8f94b4778dad2d9f513cfdf9091810dc733a2daf4fa7e03d49ad133415b5d  node-v11.13.0-darwin-x64.tar.gz
5b032d0c7f195866de8c657b451a5da85612be15bd82b45de5cc39a4ef503df9  node-v11.13.0-darwin-x64.tar.xz
3b1c39b02c0e539fdf5810c13a57188b6c093ed32b2a858793e47f39f1b99ee8  node-v11.13.0-headers.tar.gz
9b0562e54a90998b6951e28d582ca13d89696c551f0a715f3f6068ef9bdc8b52  node-v11.13.0-headers.tar.xz
f7ca6b4ad6944dc6b76356eb4d6116485445d78b294ea10e97e8ec7907436384  node-v11.13.0-linux-arm64.tar.gz
0edb29004fdbb5808331e765b4adde8b025a13d6619f49fec60a14582aba9ed7  node-v11.13.0-linux-arm64.tar.xz
2f855d5e8fa8a07832b1f53769331d9a68c5bec3268cddf82fe1b6d04461c547  node-v11.13.0-linux-armv6l.tar.gz
169791b83fdd3d58a1f552d124aa801254277dc545ebc01ed955dff27e1eb134  node-v11.13.0-linux-armv6l.tar.xz
8a9f647220c726ad9d7aa8e94198773f2eafadb4f3245fb07073bb1895a887aa  node-v11.13.0-linux-armv7l.tar.gz
2fe28b09d0c1de86f6d09e204fd59f87e475f784fb4aa77a7f0bb58ac6dfd98c  node-v11.13.0-linux-armv7l.tar.xz
b768f3d2f449de8547d6de5368953336ef9a0416fb56e3f0ff28a68e04527a53  node-v11.13.0-linux-ppc64le.tar.gz
cf0df9c619947b049632f10b209671fa678196e41ad23bb0067cf863b5a5f22d  node-v11.13.0-linux-ppc64le.tar.xz
cd08bf3af505bc683c4993da5e93c664c2f623274205174ad9388b0c52baf977  node-v11.13.0-linux-s390x.tar.gz
1e48381ce2916e28ed60553ad3afef60136fb247c1ab09887bae1b39d3c3966f  node-v11.13.0-linux-s390x.tar.xz
23cafb97f7e299c125b3c45cef3f7cb08ffd37e1aa9372367e715d89a1fa33ca  node-v11.13.0-linux-x64.tar.gz
c2ae3caced5f181df282a6974ab3f5e232b5decb821aa8ea8fe758b0e0528223  node-v11.13.0-linux-x64.tar.xz
255ef8b33f622bf2377cd3acc4197f232cd120b60143369cb6ff88dc939d0419  node-v11.13.0.pkg
34ea5059e23eadf1cb38d2e1064126e7d048765efa8ccffdf9e9141cc364f9f0  node-v11.13.0-sunos-x64.tar.gz
59f2c2d8e090c2ba1c2989d2aa07331357e8c6f4cb233a41a933c5f1da4cc217  node-v11.13.0-sunos-x64.tar.xz
4c29d24de0e6d2bdf7fbac6d37938696a124501d3710b7f6ecdadb0ef5925fb2  node-v11.13.0.tar.gz
7411952da11f6033d4ca5993d68f52b0d7eb90538099102c37d5221c445dc38a  node-v11.13.0.tar.xz
c0a1d95ebfc5c09ccbd83d9e091bb0597751a4c2aaa7e619deb2fcc6ce07949a  node-v11.13.0-win-x64.7z
f3dafd1f19841244f8f12952f9919c0a2857f6920b11f2a89554b061cd5a4525  node-v11.13.0-win-x64.zip
523a768efb90ebbd6a0273792c8d427504bb31c4aa98e7c4dbeae5ea24d409e7  node-v11.13.0-win-x86.7z
589d6456b527707c9af0bf23d0f70818e51fde1c3614b630cc4cd2ec2ad5d137  node-v11.13.0-win-x86.zip
8d0963f62059526146ffbd2a4fba36276399fcb5580ba7f1def8cc2b4a35fe76  node-v11.13.0-x64.msi
3ad0e7bb3206c4d2ce5abd2622c7e4d52add3e28eb65e0d07f60fcabf7e5e6af  node-v11.13.0-x86.msi
bed7788c39a86bf8095e3af0a3a1bf00408338d1003e7b5a7e77503e2dc6a097  win-x64/node.exe
d4703bae0cbed18c46ceb042b4d2d2d3728c09376ca5c9614e020293de46763e  win-x64/node.lib
446b14119f4bcd3dbf1235e8d0b5cd25e9004a227d9133e68ef62beb6ae303be  win-x64/node_pdb.7z
916411d3d6020aa9b3e24872a0e3dea1b8c70ea1cae254d12d2ae464fb883275  win-x64/node_pdb.zip
d61fedc1c8cb8f46609aa7edfa79b6e600f4e3df4576e1a57b0058adc2df196b  win-x86/node.exe
52a621aed1adca0f5fe62019e220abf6e3be2917a63f24ea17e5cb217c50be3b  win-x86/node.lib
cca5c871d0f3ad3f12907d4e12fafafe01d860bc2065ff187a66cf87dfe17917  win-x86/node_pdb.7z
4f50987a5eee917e2670709420b668afac3f8eb80271738b24268a7a1364c2b4  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEj8yhP+8dDC6RAI4Jdw96mlrhVgAFAlydH68ACgkQdw96mlrh
VgAeuhAAgRigIMGmPtK4ZmgIjHfL1BRiBa7YwdnKkEr4MI8dlk9d4Z6HgQURPymS
w2R0rcDB3aN7Iah7v+eIQV74gKaTBUQd7B4TLA9bFiKOETLlheG/+SzAZs5cupyS
nRK1bbFPXIIpxqawE9CPAHprlJZ615xNysDBFT3j7bfismqu/vT4bybpOhJFcjul
GQoocNG+RTgfYcpWmK3Rj9/rNx4HXQzntdPPmumCBCQG30b3eS0BurUNFpNfnOHj
7TQ8fx11Zpc1m0fDFif9gA5VjIwOFHYwmpn6VTSrMP1CLwmVtqRt4lnZ/MOI2Dvb
9q6FDlFRWPIUl93z1XawexUJhcwTtchUDmln4W7yEnOzoDRutt1IYvafeYLNU8pp
gw+QTgEVrJOqgvKXgec3PehN6Xn0LTXule3zT1oJJDDFABBLhc1pfpXztpypqqwx
9b4jeE6UCk9bOehIByDAbXW5Tq/7R7D8XrA5g0FOywPs6S9e1kzhrU0ZR2g7waw0
7YcIF+lo8MKFVgoDeTDupW91/Lxu6+Ja3zU8vfcx68Tz6NmtypwE0VYYbz74TjNQ
t5G8B2biKvrx7F2Tv2VFXazNT9P1z8GPgqT0/Te4G8z7Nip+vUw27tCr8D41Clhq
3OD44cwmoRyhDXsAQw3Yfr0TwH4339KSJ5s/ZeGfg+UAcchiOOY=
=zAEz
-----END PGP SIGNATURE-----

```
