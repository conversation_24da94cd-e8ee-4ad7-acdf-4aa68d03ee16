---
date: '2017-02-22T01:47:55.362Z'
category: release
title: Node v4.8.0 (LTS)
layout: blog-post
author: <PERSON><PERSON>
---

### Notable Changes

- **child_process**: add shell option to spawn() (cjihrig) [#4598](https://github.com/nodejs/node/pull/4598)
- **deps**:
  - **v8**: expose statistics about heap spaces (<PERSON>) [#4463](https://github.com/nodejs/node/pull/4463)
- **crypto**:
  - add ALPN Support (Shi<PERSON><PERSON>) [#2564](https://github.com/nodejs/node/pull/2564)
  - allow adding extra certs to well-known CAs (<PERSON>) [#9139](https://github.com/nodejs/node/pull/9139)
- **fs**: add the fs.mkdtemp() function. (Florian MARGAINE) [#5333](https://github.com/nodejs/node/pull/5333)
- **process**:
  - add `externalMemory` to `process` (<PERSON><PERSON>) [#9587](https://github.com/nodejs/node/pull/9587)
  - add process.cpuUsage() (<PERSON> Mueller) [#10796](https://github.com/nodejs/node/pull/10796)

### Commits

- [[`78010aa0cd`](https://github.com/nodejs/node/commit/78010aa0cd)] - **build**: add /opt/freeware/... to AIX library path (Stewart X Addison) [#10128](https://github.com/nodejs/node/pull/10128)
- [[`0bb77f24fa`](https://github.com/nodejs/node/commit/0bb77f24fa)] - **build**: add (not) cross-compiled configure flags (Jesús Leganés-Combarro 'piranna) [#10287](https://github.com/nodejs/node/pull/10287)
- [[`58245225ef`](https://github.com/nodejs/node/commit/58245225ef)] - **(SEMVER-MINOR)** **child_process**: add shell option to spawn() (cjihrig) [#4598](https://github.com/nodejs/node/pull/4598)
- [[`1595328b44`](https://github.com/nodejs/node/commit/1595328b44)] - **(SEMVER-MINOR)** **crypto**: allow adding extra certs to well-known CAs (Sam Roberts) [#9139](https://github.com/nodejs/node/pull/9139)
- [[`bf882fba35`](https://github.com/nodejs/node/commit/bf882fba35)] - **crypto**: Use reference count to manage cert_store (Adam Majer) [#9409](https://github.com/nodejs/node/pull/9409)
- [[`4cf7dcff99`](https://github.com/nodejs/node/commit/4cf7dcff99)] - **crypto**: remove unnecessary variables of alpn/npn (Shigeki Ohtsu) [#10831](https://github.com/nodejs/node/pull/10831)
- [[`d8b902f787`](https://github.com/nodejs/node/commit/d8b902f787)] - **debugger**: call `this.resume()` after `this.run()` (Lance Ball) [#10099](https://github.com/nodejs/node/pull/10099)
- [[`4e07bd45d6`](https://github.com/nodejs/node/commit/4e07bd45d6)] - **deps**: update patch level in V8 (Myles Borins) [#10668](https://github.com/nodejs/node/pull/10668)
- [[`a234d445c4`](https://github.com/nodejs/node/commit/a234d445c4)] - **deps**: backport a715957 from V8 upstream (Myles Borins) [#10668](https://github.com/nodejs/node/pull/10668)
- [[`ce66c8e424`](https://github.com/nodejs/node/commit/ce66c8e424)] - **deps**: backport 7a88ff3 from V8 upstream (Myles Borins) [#10668](https://github.com/nodejs/node/pull/10668)
- [[`8bd3d83e01`](https://github.com/nodejs/node/commit/8bd3d83e01)] - **deps**: backport d800a65 from V8 upstream (Myles Borins) [#10668](https://github.com/nodejs/node/pull/10668)
- [[`81e9a3bfcb`](https://github.com/nodejs/node/commit/81e9a3bfcb)] - **deps**: V8: fix debug backtrace for symbols (Ali Ijaz Sheikh) [#10732](https://github.com/nodejs/node/pull/10732)
- [[`d8961bdb3b`](https://github.com/nodejs/node/commit/d8961bdb3b)] - **doc**: correct vcbuild options for windows testing (Jonathan Boarman) [#10686](https://github.com/nodejs/node/pull/10686)
- [[`d3c5bc1c63`](https://github.com/nodejs/node/commit/d3c5bc1c63)] - **doc**: update BUILDING.md (rainabba) [#8704](https://github.com/nodejs/node/pull/8704)
- [[`d61c181085`](https://github.com/nodejs/node/commit/d61c181085)] - **doc**: unify dirname and filename description (Sam Roberts) [#10527](https://github.com/nodejs/node/pull/10527)
- [[`8eeccd82d2`](https://github.com/nodejs/node/commit/8eeccd82d2)] - **doc**: killSignal option accepts integer values (Sakthipriyan Vairamani (thefourtheye)) [#10424](https://github.com/nodejs/node/pull/10424)
- [[`7db7e47d7b`](https://github.com/nodejs/node/commit/7db7e47d7b)] - **doc**: change logical to bitwise OR in dns lookup (Sakthipriyan Vairamani (thefourtheye)) [#11037](https://github.com/nodejs/node/pull/11037)
- [[`28b707ba42`](https://github.com/nodejs/node/commit/28b707ba42)] - **doc**: replace newlines in deprecation with space (Sakthipriyan Vairamani (thefourtheye)) [#11074](https://github.com/nodejs/node/pull/11074)
- [[`79d49866f2`](https://github.com/nodejs/node/commit/79d49866f2)] - **doc**: update CONTRIBUTING.MD with link to V8 guide (sarahmeyer) [#10070](https://github.com/nodejs/node/pull/10070)
- [[`acbe4d3516`](https://github.com/nodejs/node/commit/acbe4d3516)] - **doc**: add joyeecheung to collaborators (Joyee Cheung) [#10603](https://github.com/nodejs/node/pull/10603)
- [[`c7378c4d5f`](https://github.com/nodejs/node/commit/c7378c4d5f)] - **doc**: warn about unvalidated input in child_process (Matthew Garrett) [#10466](https://github.com/nodejs/node/pull/10466)
- [[`08e924e45c`](https://github.com/nodejs/node/commit/08e924e45c)] - **doc**: require two-factor authentication (Rich Trott) [#10529](https://github.com/nodejs/node/pull/10529)
- [[`d260fb2e7e`](https://github.com/nodejs/node/commit/d260fb2e7e)] - **doc**: use "Node.js" in V8 guide (Rich Trott) [#10438](https://github.com/nodejs/node/pull/10438)
- [[`4f168a4a31`](https://github.com/nodejs/node/commit/4f168a4a31)] - **doc**: require() tries first core not native modules (Vicente Jimenez Aguilar) [#10324](https://github.com/nodejs/node/pull/10324)
- [[`5777c79c52`](https://github.com/nodejs/node/commit/5777c79c52)] - **doc**: clarify the review and landing process (Joyee Cheung) [#10202](https://github.com/nodejs/node/pull/10202)
- [[`d3a7fb8a9e`](https://github.com/nodejs/node/commit/d3a7fb8a9e)] - **doc**: redirect 'Start a Working Group' to TSC repo (William Kapke) [#9655](https://github.com/nodejs/node/pull/9655)
- [[`0e51cbb827`](https://github.com/nodejs/node/commit/0e51cbb827)] - **doc**: add Working Group dissolution text (William Kapke) [#9656](https://github.com/nodejs/node/pull/9656)
- [[`919e0cb8f2`](https://github.com/nodejs/node/commit/919e0cb8f2)] - **doc**: more efficient example in the console.md (Vse Mozhet Byt) [#10451](https://github.com/nodejs/node/pull/10451)
- [[`70ea38f2ee`](https://github.com/nodejs/node/commit/70ea38f2ee)] - **doc**: var -> const / let in the console.md (Vse Mozhet Byt) [#10451](https://github.com/nodejs/node/pull/10451)
- [[`dda777bf9e`](https://github.com/nodejs/node/commit/dda777bf9e)] - **doc**: consistent 'Returns:' part two (Myles Borins) [#10391](https://github.com/nodejs/node/pull/10391)
- [[`3b252a69a0`](https://github.com/nodejs/node/commit/3b252a69a0)] - **doc**: clarify macosx-firewall suggestion BUILDING (Chase Starr) [#10311](https://github.com/nodejs/node/pull/10311)
- [[`c4df02c815`](https://github.com/nodejs/node/commit/c4df02c815)] - **doc**: add Michaël Zasso to the CTC (Michaël Zasso)
- [[`2269d7db0f`](https://github.com/nodejs/node/commit/2269d7db0f)] - **(SEMVER-MINOR)** **fs**: add the fs.mkdtemp() function. (Florian MARGAINE) [#5333](https://github.com/nodejs/node/pull/5333)
- [[`2eda3c7c75`](https://github.com/nodejs/node/commit/2eda3c7c75)] - **lib,test**: use consistent operator linebreak style (Michaël Zasso) [#10178](https://github.com/nodejs/node/pull/10178)
- [[`7505b86d2f`](https://github.com/nodejs/node/commit/7505b86d2f)] - **os**: fix os.release() for aix and add test (jBarz) [#10245](https://github.com/nodejs/node/pull/10245)
- [[`7a9c8d8f10`](https://github.com/nodejs/node/commit/7a9c8d8f10)] - **(SEMVER-MINOR)** **process**: add process.cpuUsage() - implementation, doc, tests (Patrick Mueller) [#10796](https://github.com/nodejs/node/pull/10796)
- [[`23a573f7cb`](https://github.com/nodejs/node/commit/23a573f7cb)] - **(SEMVER-MINOR)** **process**: add `process.memoryUsage.external` (Fedor Indutny) [#9587](https://github.com/nodejs/node/pull/9587)
- [[`be6203715a`](https://github.com/nodejs/node/commit/be6203715a)] - **src**: describe what NODE_MODULE_VERSION is for (Sam Roberts) [#10414](https://github.com/nodejs/node/pull/10414)
- [[`3f29cbb5bc`](https://github.com/nodejs/node/commit/3f29cbb5bc)] - **src**: fix string format mistake for 32 bit node (Alex Newman) [#10082](https://github.com/nodejs/node/pull/10082)
- [[`271f5783fe`](https://github.com/nodejs/node/commit/271f5783fe)] - **stream, test**: test \_readableState.emittedReadable (Joyee Cheung) [#10249](https://github.com/nodejs/node/pull/10249)
- [[`c279cbe6a9`](https://github.com/nodejs/node/commit/c279cbe6a9)] - **test**: fix test.py command line options processing (Julien Gilli) [#11153](https://github.com/nodejs/node/pull/11153)
- [[`0f5d82e583`](https://github.com/nodejs/node/commit/0f5d82e583)] - **test**: add --abort-on-timeout option to test.py (Julien Gilli) [#11086](https://github.com/nodejs/node/pull/11086)
- [[`735119c6fb`](https://github.com/nodejs/node/commit/735119c6fb)] - **test**: cleanup stream tests (Italo A. Casas) [#8668](https://github.com/nodejs/node/pull/8668)
- [[`f9f8e4ee3e`](https://github.com/nodejs/node/commit/f9f8e4ee3e)] - **test**: refactor test-preload (Rich Trott) [#9803](https://github.com/nodejs/node/pull/9803)
- [[`e7c4dfb83b`](https://github.com/nodejs/node/commit/e7c4dfb83b)] - **test**: invalid package.json causes error when require()ing in directory (Sam Shull) [#10044](https://github.com/nodejs/node/pull/10044)
- [[`22226fa900`](https://github.com/nodejs/node/commit/22226fa900)] - **test**: refactoring test-pipe-head (Travis Bretton) [#10036](https://github.com/nodejs/node/pull/10036)
- [[`11115c0d85`](https://github.com/nodejs/node/commit/11115c0d85)] - **test**: add second argument to assert.throws() (Ken Russo) [#9987](https://github.com/nodejs/node/pull/9987)
- [[`96ca40bdd8`](https://github.com/nodejs/node/commit/96ca40bdd8)] - **test**: refactor test-tls-0-dns-altname (Richard Karmazin) [#9948](https://github.com/nodejs/node/pull/9948)
- [[`98496b6d3e`](https://github.com/nodejs/node/commit/98496b6d3e)] - **test**: test: refactor test-sync-fileread (Jason Wohlgemuth) [#9941](https://github.com/nodejs/node/pull/9941)
- [[`324c82b1c9`](https://github.com/nodejs/node/commit/324c82b1c9)] - **test**: use common.fixturesDir almost everywhere (Bryan English) [#6997](https://github.com/nodejs/node/pull/6997)
- [[`ce91bb21ba`](https://github.com/nodejs/node/commit/ce91bb21ba)] - **test**: refactor test-repl-mode.js (Cesar Hernandez) [#10061](https://github.com/nodejs/node/pull/10061)
- [[`61cbc202a1`](https://github.com/nodejs/node/commit/61cbc202a1)] - **test**: refactor test-net-dns-custom-lookup (Kent.Fan) [#10071](https://github.com/nodejs/node/pull/10071)
- [[`812c6361ff`](https://github.com/nodejs/node/commit/812c6361ff)] - **test**: refactor test-tls-server-verify (Hutson Betts) [#10076](https://github.com/nodejs/node/pull/10076)
- [[`19907c27a6`](https://github.com/nodejs/node/commit/19907c27a6)] - **test**: use mustCall() for simple flow tracking (cjihrig) [#7753](https://github.com/nodejs/node/pull/7753)
- [[`42da81e6cc`](https://github.com/nodejs/node/commit/42da81e6cc)] - **test**: set stdin too for pseudo-tty tests (Anna Henningsen) [#10149](https://github.com/nodejs/node/pull/10149)
- [[`53404dbc1f`](https://github.com/nodejs/node/commit/53404dbc1f)] - **test**: add stdin-setrawmode.out file (Jonathan Darling) [#10149](https://github.com/nodejs/node/pull/10149)
- [[`1fac431307`](https://github.com/nodejs/node/commit/1fac431307)] - **test**: add tests for clearBuffer state machine (Safia Abdalla) [#9922](https://github.com/nodejs/node/pull/9922)
- [[`37a362275e`](https://github.com/nodejs/node/commit/37a362275e)] - **test**: update test-cluster-shared-handle-bind-error (cjihrig) [#10547](https://github.com/nodejs/node/pull/10547)
- [[`f5e54f5d5f`](https://github.com/nodejs/node/commit/f5e54f5d5f)] - **test**: avoid assigning this to variables (cjihrig) [#10548](https://github.com/nodejs/node/pull/10548)
- [[`28a5ce10af`](https://github.com/nodejs/node/commit/28a5ce10af)] - **test**: improve test-http-allow-req-after-204-res (Adrian Estrada) [#10503](https://github.com/nodejs/node/pull/10503)
- [[`52edebc8f3`](https://github.com/nodejs/node/commit/52edebc8f3)] - **test**: improve test-fs-empty-readStream.js (Adrian Estrada) [#10479](https://github.com/nodejs/node/pull/10479)
- [[`b74bc517a6`](https://github.com/nodejs/node/commit/b74bc517a6)] - **test**: use strictEqual in test-http-server (Fabrice Tatieze) [#10478](https://github.com/nodejs/node/pull/10478)
- [[`a9cd1d1267`](https://github.com/nodejs/node/commit/a9cd1d1267)] - **test**: refactor test-stream2-unpipe-drain (Chris Story) [#10033](https://github.com/nodejs/node/pull/10033)
- [[`7020e9fd8b`](https://github.com/nodejs/node/commit/7020e9fd8b)] - **test**: add test for SIGWINCH handling by stdio.js (Sarah Meyer) [#10063](https://github.com/nodejs/node/pull/10063)
- [[`56b193a9c2`](https://github.com/nodejs/node/commit/56b193a9c2)] - **test**: improve code in test-vm-preserves-property (Adrian Estrada) [#10428](https://github.com/nodejs/node/pull/10428)
- [[`8a26ba142f`](https://github.com/nodejs/node/commit/8a26ba142f)] - **test**: fix flaky test-https-timeout (Rich Trott) [#10404](https://github.com/nodejs/node/pull/10404)
- [[`eeb2d7885a`](https://github.com/nodejs/node/commit/eeb2d7885a)] - **test**: improve test-cluster-worker-constructor.js (Adrian Estrada) [#10396](https://github.com/nodejs/node/pull/10396)
- [[`fd195b47d6`](https://github.com/nodejs/node/commit/fd195b47d6)] - **test**: stream readable resumeScheduled state (Italo A. Casas) [#10299](https://github.com/nodejs/node/pull/10299)
- [[`135a7c9e19`](https://github.com/nodejs/node/commit/135a7c9e19)] - **test**: stream readable needReadable state (Joyee Cheung) [#10241](https://github.com/nodejs/node/pull/10241)
- [[`f412b1fcfd`](https://github.com/nodejs/node/commit/f412b1fcfd)] - **test**: clean up domain-no-error-handler test (weyj4) [#10291](https://github.com/nodejs/node/pull/10291)
- [[`14c28ebcf1`](https://github.com/nodejs/node/commit/14c28ebcf1)] - **test**: update test-domain-uncaught-exception.js (Andy Chen) [#10193](https://github.com/nodejs/node/pull/10193)
- [[`928291c652`](https://github.com/nodejs/node/commit/928291c652)] - **test**: refactor test-domain.js (Siddhartha Sahai) [#10207](https://github.com/nodejs/node/pull/10207)
- [[`13c6cec433`](https://github.com/nodejs/node/commit/13c6cec433)] - **test**: fail for missing output files (Anna Henningsen) [#10150](https://github.com/nodejs/node/pull/10150)
- [[`544920f77b`](https://github.com/nodejs/node/commit/544920f77b)] - **test**: stream readableState readingMore state (Gregory) [#9868](https://github.com/nodejs/node/pull/9868)
- [[`2f8bc9a7bc`](https://github.com/nodejs/node/commit/2f8bc9a7bc)] - **test**: s/ASSERT/assert/ (cjihrig) [#10544](https://github.com/nodejs/node/pull/10544)
- [[`380a5d5e12`](https://github.com/nodejs/node/commit/380a5d5e12)] - **test**: fix flaky test-http-client-timeout-with-data (Rich Trott) [#10431](https://github.com/nodejs/node/pull/10431)
- [[`14e07c96e1`](https://github.com/nodejs/node/commit/14e07c96e1)] - **test**: refactor test-stdin-from-file (Rob Adelmann) [#10331](https://github.com/nodejs/node/pull/10331)
- [[`424c86139d`](https://github.com/nodejs/node/commit/424c86139d)] - **test**: refactor the code in test-fs-chmod (Adrian Estrada) [#10440](https://github.com/nodejs/node/pull/10440)
- [[`31aa877003`](https://github.com/nodejs/node/commit/31aa877003)] - **test**: improve the code in test-pipe.js (Adrian Estrada) [#10452](https://github.com/nodejs/node/pull/10452)
- [[`4bbd50ee07`](https://github.com/nodejs/node/commit/4bbd50ee07)] - **test**: improve code in test-fs-readfile-error (Adrian Estrada) [#10367](https://github.com/nodejs/node/pull/10367)
- [[`9840f505f0`](https://github.com/nodejs/node/commit/9840f505f0)] - **test**: improve code in test-vm-symbols (Adrian Estrada) [#10429](https://github.com/nodejs/node/pull/10429)
- [[`4efdbafeb3`](https://github.com/nodejs/node/commit/4efdbafeb3)] - **test**: refactor test-child-process-ipc (malen) [#9990](https://github.com/nodejs/node/pull/9990)
- [[`dbfec29663`](https://github.com/nodejs/node/commit/dbfec29663)] - **test**: fix and improve debug-break-on-uncaught (Sakthipriyan Vairamani (thefourtheye)) [#10370](https://github.com/nodejs/node/pull/10370)
- [[`80f4a37023`](https://github.com/nodejs/node/commit/80f4a37023)] - **test**: refactor test-pipe-file-to-http (Josh Mays) [#10054](https://github.com/nodejs/node/pull/10054)
- [[`a983400ac2`](https://github.com/nodejs/node/commit/a983400ac2)] - **test**: refactor test-tls-interleave (Brian Chirgwin) [#10017](https://github.com/nodejs/node/pull/10017)
- [[`6db76da2c8`](https://github.com/nodejs/node/commit/6db76da2c8)] - **test**: refactor test-cluster-send-handle-twice.js (Amar Zavery) [#10049](https://github.com/nodejs/node/pull/10049)
- [[`19b314e40a`](https://github.com/nodejs/node/commit/19b314e40a)] - **test**: update test-tls-check-server-identity.js (Kevin Cox) [#9986](https://github.com/nodejs/node/pull/9986)
- [[`ab3e4c6a9b`](https://github.com/nodejs/node/commit/ab3e4c6a9b)] - **test**: improve test-cluster-net-listen.js (Rico Cai) [#9953](https://github.com/nodejs/node/pull/9953)
- [[`fb9a0ad6c0`](https://github.com/nodejs/node/commit/fb9a0ad6c0)] - **test**: refactor test-child-process-stdin (Segu Riluvan) [#10420](https://github.com/nodejs/node/pull/10420)
- [[`122917df5a`](https://github.com/nodejs/node/commit/122917df5a)] - **test**: change var declarations, add mustCall check (Daniel Sims) [#9962](https://github.com/nodejs/node/pull/9962)
- [[`d5e911c51e`](https://github.com/nodejs/node/commit/d5e911c51e)] - **test**: refactoring test-cluster-worker-constructor (Christopher Rokita) [#9956](https://github.com/nodejs/node/pull/9956)
- [[`7d61bbf647`](https://github.com/nodejs/node/commit/7d61bbf647)] - **test**: refactor test-stdin-script-child (Emanuel Buholzer) [#10321](https://github.com/nodejs/node/pull/10321)
- [[`76bb3cbff9`](https://github.com/nodejs/node/commit/76bb3cbff9)] - **test**: refactor test-stream2-writable (Rich Trott) [#10353](https://github.com/nodejs/node/pull/10353)
- [[`b87ee26b96`](https://github.com/nodejs/node/commit/b87ee26b96)] - **test**: change assert.strict to assert.strictEqual() (Ashita Nagesh) [#9988](https://github.com/nodejs/node/pull/9988)
- [[`4514fd78f4`](https://github.com/nodejs/node/commit/4514fd78f4)] - **test**: refactor the code in test-http-keep-alive (Adrian Estrada) [#10350](https://github.com/nodejs/node/pull/10350)
- [[`f301df405a`](https://github.com/nodejs/node/commit/f301df405a)] - **test**: use strictEqual in test-cwd-enoent-repl.js (Neeraj Sharma) [#9952](https://github.com/nodejs/node/pull/9952)
- [[`3b67001c99`](https://github.com/nodejs/node/commit/3b67001c99)] - **test**: refactor test-net-reconnect-error (Duy Le) [#9903](https://github.com/nodejs/node/pull/9903)
- [[`34861efff6`](https://github.com/nodejs/node/commit/34861efff6)] - **test**: add test-require-invalid-package (Duy Le) [#9903](https://github.com/nodejs/node/pull/9903)
- [[`90a79b3967`](https://github.com/nodejs/node/commit/90a79b3967)] - **test**: refactor test-timers-this (Rich Trott) [#10315](https://github.com/nodejs/node/pull/10315)
- [[`5335b0a0d1`](https://github.com/nodejs/node/commit/5335b0a0d1)] - **test**: refactor test-tls-ecdh-disable (Aaron Williams) [#9989](https://github.com/nodejs/node/pull/9989)
- [[`0f8a323546`](https://github.com/nodejs/node/commit/0f8a323546)] - **test**: cleanup test-stdout-close-catch.js (Travis Bretton) [#10006](https://github.com/nodejs/node/pull/10006)
- [[`fc67a955e2`](https://github.com/nodejs/node/commit/fc67a955e2)] - **test**: use const/let and common.mustCall (Outsider) [#9959](https://github.com/nodejs/node/pull/9959)
- [[`2f44d7f367`](https://github.com/nodejs/node/commit/2f44d7f367)] - **test**: refactor test-crypto-random (Rich Trott) [#10232](https://github.com/nodejs/node/pull/10232)
- [[`730c3b29e8`](https://github.com/nodejs/node/commit/730c3b29e8)] - **test**: refactor test-fs-fsync (Rob Adelmann) [#10176](https://github.com/nodejs/node/pull/10176)
- [[`9c9d422433`](https://github.com/nodejs/node/commit/9c9d422433)] - **test**: refactor test-http-after-connect.js (larissayvette) [#10229](https://github.com/nodejs/node/pull/10229)
- [[`827bbe7985`](https://github.com/nodejs/node/commit/827bbe7985)] - **test**: refactor assert.equal, update syntax to ES6 (Prieto, Marcos)
- [[`121b68a283`](https://github.com/nodejs/node/commit/121b68a283)] - **test**: refactor http pipelined socket test (Rich Trott) [#10189](https://github.com/nodejs/node/pull/10189)
- [[`7ca31e38fb`](https://github.com/nodejs/node/commit/7ca31e38fb)] - **test**: fix alpn tests for openssl1.0.2h (Shigeki Ohtsu) [#6550](https://github.com/nodejs/node/pull/6550)
- [[`278d718a93`](https://github.com/nodejs/node/commit/278d718a93)] - **test**: refactor test-handle-wrap-close-abort (Rich Trott) [#10188](https://github.com/nodejs/node/pull/10188)
- [[`f12bab65b8`](https://github.com/nodejs/node/commit/f12bab65b8)] - **test**: stream readableListening internal state (Italo A. Casas) [#9864](https://github.com/nodejs/node/pull/9864)
- [[`210290dfba`](https://github.com/nodejs/node/commit/210290dfba)] - **test**: check for error on invalid signal (Matt Phillips) [#10026](https://github.com/nodejs/node/pull/10026)
- [[`4f5f0e4975`](https://github.com/nodejs/node/commit/4f5f0e4975)] - **test**: refactor test-net-keepalive.js (Kyle Corsi) [#9995](https://github.com/nodejs/node/pull/9995)
- [[`cfa2b87b5d`](https://github.com/nodejs/node/commit/cfa2b87b5d)] - **test,lib,benchmark**: match function names (Rich Trott) [#9113](https://github.com/nodejs/node/pull/9113)
- [[`a67ada7d32`](https://github.com/nodejs/node/commit/a67ada7d32)] - **tls**: copy the Buffer object before using (Sakthipriyan Vairamani) [#8055](https://github.com/nodejs/node/pull/8055)
- [[`e750f142ce`](https://github.com/nodejs/node/commit/e750f142ce)] - **(SEMVER-MINOR)** **tls, crypto**: add ALPN Support (Shigeki Ohtsu) [#2564](https://github.com/nodejs/node/pull/2564)
- [[`ef547f3325`](https://github.com/nodejs/node/commit/ef547f3325)] - **(SEMVER-MINOR)** **tls,crypto**: move NPN protcol data to hidden value (Shigeki Ohtsu) [#2564](https://github.com/nodejs/node/pull/2564)
- [[`31434a1202`](https://github.com/nodejs/node/commit/31434a1202)] - **tools**: enforce consistent operator linebreak style (Michaël Zasso) [#10178](https://github.com/nodejs/node/pull/10178)
- [[`9f13b5f7d5`](https://github.com/nodejs/node/commit/9f13b5f7d5)] - **tools**: forbid template literals in assert.throws (Michaël Zasso) [#10301](https://github.com/nodejs/node/pull/10301)
- [[`c801de9814`](https://github.com/nodejs/node/commit/c801de9814)] - **tools**: add ESLint rule for assert.throws arguments (Michaël Zasso) [#10089](https://github.com/nodejs/node/pull/10089)
- [[`b5e18f207f`](https://github.com/nodejs/node/commit/b5e18f207f)] - **tools**: add macosx-firwall script to avoid popups (Daniel Bevenius) [#10114](https://github.com/nodejs/node/pull/10114)
- [[`30d60cf81c`](https://github.com/nodejs/node/commit/30d60cf81c)] - **(SEMVER-MINOR)** **v8,src**: expose statistics about heap spaces (Ben Ripkens) [#4463](https://github.com/nodejs/node/pull/4463)
- [[`9556ef3241`](https://github.com/nodejs/node/commit/9556ef3241)] - **vm**: add error message if we abort (Franziska Hinkelmann) [#8634](https://github.com/nodejs/node/pull/8634)
- [[`fa11f4b1fc`](https://github.com/nodejs/node/commit/fa11f4b1fc)] - **win,msi**: add required UIRef for localized strings (Bill Ticehurst) [#8884](https://github.com/nodejs/node/pull/8884)

Windows 32-bit Installer: https://nodejs.org/dist/v4.8.0/node-v4.8.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v4.8.0/node-v4.8.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v4.8.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v4.8.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v4.8.0/node-v4.8.0.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v4.8.0/node-v4.8.0-darwin-x64.tar.gz \
Linux 32-bit Binary: https://nodejs.org/dist/v4.8.0/node-v4.8.0-linux-x86.tar.xz \
Linux 64-bit Binary: https://nodejs.org/dist/v4.8.0/node-v4.8.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v4.8.0/node-v4.8.0-linux-ppc64le.tar.xz \
Linux PPC BE 64-bit Binary: https://nodejs.org/dist/v4.8.0/node-v4.8.0-linux-ppc64.tar.xz \
SmartOS 32-bit Binary: https://nodejs.org/dist/v4.8.0/node-v4.8.0-sunos-x86.tar.xz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v4.8.0/node-v4.8.0-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v4.8.0/node-v4.8.0-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v4.8.0/node-v4.8.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v4.8.0/node-v4.8.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v4.8.0/node-v4.8.0.tar.gz \
Other release files: https://nodejs.org/dist/v4.8.0/ \
Documentation: https://nodejs.org/docs/v4.8.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

ea4d8dc0e77e744d78dbb5221f2711e77f90394a5a5296fc231757dd52afe9b0  node-v4.8.0-darwin-x64.tar.gz
73e23e85fa3e91c06156deddd52a037b0e9a94849fff6a91693737ee49094cc7  node-v4.8.0-darwin-x64.tar.xz
ba8bcb2e4187ee6b705605b4d55b0f5a0f9ad0ba3d24bd6f5d70dff26e9bcf97  node-v4.8.0-headers.tar.gz
a3a75cfee533ea41219796a89a4407608196e38f325ebf3127b12630d4f98b2a  node-v4.8.0-headers.tar.xz
f796af7ea3c2ac085ba4d24b367c4a9ec73a0d6f469628470a8b7ac14e05d0a4  node-v4.8.0-linux-arm64.tar.gz
e0f5f7a2458c715c60ecb3966ee29bcdae95acceb2a611bd920ce1c649174732  node-v4.8.0-linux-arm64.tar.xz
737c4bdaf68e4f347aaa90f6abb44f8822ee2234a862c2248fa800a58ac0600b  node-v4.8.0-linux-armv6l.tar.gz
1c828dbd59a2b5e226c5cce772cd6c4a01aaaa06f9c19ef06d9666b53fd54b5b  node-v4.8.0-linux-armv6l.tar.xz
a6b1e35a6f3fbf05fef76f2672b55f07ea7ebd179440f212d80605bd1ef548c0  node-v4.8.0-linux-armv7l.tar.gz
ad05bf930ecbcda934628b33fa4f8dda909cbd30a9577d0b70f18ecb3774900d  node-v4.8.0-linux-armv7l.tar.xz
cfe003b8984af329132e1027fd2c4a91f7e8aace0facde1789f55986f5671e63  node-v4.8.0-linux-ppc64le.tar.gz
86a43cb714d724a84b83954d548687b291abeeb7e8811e40a7633bd8e9532646  node-v4.8.0-linux-ppc64le.tar.xz
2aab5c30ff56be5811ec1181877014592bd301d5f4b60c7be6aabaa2e30b2639  node-v4.8.0-linux-ppc64.tar.gz
48535d3a1054faff37e49eedb757613b048edde4dcd92833d8e9c0db80545355  node-v4.8.0-linux-ppc64.tar.xz
43e50dfa950ccd0caf03ad71c65f8235aca6fe173596f2804b2f9dfaa45cabf1  node-v4.8.0-linux-x64.tar.gz
4597861fbbcfc120be608ec6ba18a61c5951d4093cf149bf98097972a4927a80  node-v4.8.0-linux-x64.tar.xz
20b3018ee2e0a5bedc5aa357607ceb9bfaeb215dc48f878a0a92b78288637eb7  node-v4.8.0-linux-x86.tar.gz
cd5e5e35398dc0fb347fbfefbf167b653d2b304bbd6d33b634bbe96b1c381f94  node-v4.8.0-linux-x86.tar.xz
e3ce14f5b249c888a3fdaae0360239f121f99dcd4d098df76f2a535e438e6cfd  node-v4.8.0.pkg
9fa8fd422985a4194d96161ac19b3b2cd1098bfd581483f6ea7c0bad6344d42b  node-v4.8.0-sunos-x64.tar.gz
817da2014e1a30c5ddc070340996e6ff2dd8790dc87c5de626e94027ad1f6177  node-v4.8.0-sunos-x64.tar.xz
b1472060cef54c54d516c20210648cc2836ee2fd56c35bd721b9c32d02d33308  node-v4.8.0-sunos-x86.tar.gz
b26d3630fcc2d72b8df9f8f821b4acf605edbe13a66d8c00c2fa2c800397812d  node-v4.8.0-sunos-x86.tar.xz
6e65d19e0135be474df53a350af766a8f1ba5ef1a5288a4da993a1a9b0e04942  node-v4.8.0.tar.gz
67ea720419f902bd47e5c45a16ed45f8083c38ccd307b206a1dc53e862fad965  node-v4.8.0.tar.xz
2c4ad7799609d284f54b004a1f9896890ba12796e5bb026d4c6f3e5b27312a76  node-v4.8.0-win-x64.7z
eb405edf1076b094fc3980ae8a83e91b15e05da057f4c5a96510de5b9636edf4  node-v4.8.0-win-x64.zip
58bcba2ce057a317bc7c3aff8e9ca6dfc3c9762bd51d91ddd4546ced7c598ef7  node-v4.8.0-win-x86.7z
bd5ac01b966de4936b6164d24f6d19ad89e79eea047aa7712b47e56106bee21e  node-v4.8.0-win-x86.zip
07a44c3289f5fe8082388928b9511a75106392d01b10cc24b5a19e376912d264  node-v4.8.0-x64.msi
f6af223f732953981b94d576f492f59c168713d7c1148fc512e42e1dadd9b5e0  node-v4.8.0-x86.msi
9a24558be019277a9693083fe0cb2f99e531b678bcdb86c3136a4d647c10a500  win-x64/node.exe
bba83a782c15baca3ef81126ff4489b9ab33094383c9587d66b1551cf60a2f10  win-x64/node.lib
080cda13b6574ac545f1f10daf9d2442520549d238e33a5d575f554a6c41b3ee  win-x64/node_pdb.7z
cd42e8b88d8816246ec08db2994ef13daf1ae599fb3aa1c5860c47d2e166bc0f  win-x64/node_pdb.zip
cb46fddbd1aa986af7b50f8291015c02a3457649cd1fa5bb753c214f0efd5ba5  win-x86/node.exe
96d43ace5b353fe1dae498326a863b5725216e8a417e70dfc067dca2c3df061c  win-x86/node.lib
b421d9c9f553e8fcdd5fd748a189ce4f7a45bf7d6302019d27ec22e53fcfb1f5  win-x86/node_pdb.7z
6ec54cb4666f39087257b32d0d11bf5db0465b68e849942f668d37dab54edf9e  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQEcBAEBCAAGBQJYrOwtAAoJEJM7AfQLXKlG16IH/Rz3M9YWZjYtNq9q2aQfrV/k
VMt6+WtAnqzDex3oBlNLGldp5xORT9Xl8+Lqh0R0iIt0Sfdj8w30X2DgksreATXM
PkguqF0/3LRTQXWYggNooU/DWrKpKxSCVI0jnZJJPGDge7DWYLW88IHWmqm8lDxo
sPNMeYhn5zG3nRvPTFPMp91duXrOebs8yIIQMWx3L8mlub+DPEiBmwBWLyA/lwMn
p/Ufkrxu9HeqYqdqiEo8CIxvp0gDDslqwRgFe97TB6IT2UexwXfnnZzbDP5CsnSO
SHbYiUYfKKhuiLWNeT4ntfiw2SrWj8zuaEOkaf+5UuMRdJvA8Q8teLQtz1EhSBU=
=mkXD
-----END PGP SIGNATURE-----

```
