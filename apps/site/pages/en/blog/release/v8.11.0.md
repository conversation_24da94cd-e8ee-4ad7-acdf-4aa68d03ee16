---
date: '2018-03-28T16:31:43.880Z'
category: release
title: Node v8.11.0 (LTS)
layout: blog-post
author: <PERSON><PERSON>
---

### Notable Changes

- **Upgrade to OpenSSL 1.0.2o**: Does not contain any security fixes that are known to impact Node.js.
- **Fix for inspector DNS rebinding vulnerability (CVE-2018-7160)**: A malicious website could use a DNS rebinding attack to trick a web browser to bypass same-origin-policy checks and allow HTTP connections to localhost or to hosts on the local network, potentially to an open inspector port as a debugger, therefore gaining full code execution access. The inspector now only allows connections that have a browser `Host` value that is either not subject to DNS resolution or matches `localhost` or `localhost6`.
- **Fix for `'path'` module regular expression denial of service (CVE-2018-7158)**: A regular expression used for parsing POSIX paths could be used to cause a denial of service if an attacker were able to have a specially crafted path string passed through one of the impacted `'path'` module functions.
- **Reject spaces in HTTP `Content-Length` header values (CVE-2018-7159)**: The Node.js HTTP parser allowed for spaces inside `Content-Length` header values. Such values now lead to rejected connections in the same way as non-numeric values.
- **Update root certificates**: 5 additional root certificates have been added to the Node.js binary and 30 have been removed.

### Commits

- [[`dc290562e9`](https://github.com/nodejs/node/commit/dc290562e9)] - **crypto**: update root certificates (Ben Noordhuis) [#19322](https://github.com/nodejs/node/pull/19322)
- [[`df92da3f3c`](https://github.com/nodejs/node/commit/df92da3f3c)] - **deps**: add -no_rand_screen to openssl s_client (Shigeki Ohtsu) [nodejs/io.js#1836](https://github.com/nodejs/io.js/pull/1836)
- [[`259156ea40`](https://github.com/nodejs/node/commit/259156ea40)] - **deps**: fix asm build error of openssl in x86_win32 (Shigeki Ohtsu) [iojs/io.js#1389](https://github.com/iojs/io.js/pull/1389)
- [[`d559d0eb25`](https://github.com/nodejs/node/commit/d559d0eb25)] - **deps**: fix openssl assembly error on ia32 win32 (Fedor Indutny) [iojs/io.js#1389](https://github.com/iojs/io.js/pull/1389)
- [[`cf8e8bcad2`](https://github.com/nodejs/node/commit/cf8e8bcad2)] - **deps**: copy all openssl header files to include dir (Shigeki Ohtsu) [#19638](https://github.com/nodejs/node/pull/19638)
- [[`987138e488`](https://github.com/nodejs/node/commit/987138e488)] - **deps**: upgrade openssl sources to 1.0.2o (Shigeki Ohtsu) [#19638](https://github.com/nodejs/node/pull/19638)
- [[`1b7f6d9072`](https://github.com/nodejs/node/commit/1b7f6d9072)] - **deps**: reject interior blanks in Content-Length (Ben Noordhuis) [nodejs-private/http-parser-private#1](https://github.com/nodejs-private/http-parser-private/pull/1)
- [[`86c9ec6c5c`](https://github.com/nodejs/node/commit/86c9ec6c5c)] - **deps**: upgrade http-parser to v2.8.0 (Ben Noordhuis) [nodejs-private/http-parser-private#1](https://github.com/nodejs-private/http-parser-private/pull/1)
- [[`de0c84889b`](https://github.com/nodejs/node/commit/de0c84889b)] - **inspector**: minor adjustments (Eugene Ostroukhov)
- [[`b7690655ef`](https://github.com/nodejs/node/commit/b7690655ef)] - **inspector**: check Host header (Ali Ijaz Sheikh)
- [[`0641f2dbf9`](https://github.com/nodejs/node/commit/0641f2dbf9)] - **openssl**: fix keypress requirement in apps on win32 (Shigeki Ohtsu) [iojs/io.js#1389](https://github.com/iojs/io.js/pull/1389)
- [[`6ee4228c1d`](https://github.com/nodejs/node/commit/6ee4228c1d)] - **src**: drop CNNIC+StartCom certificate whitelisting (Ben Noordhuis) [#19322](https://github.com/nodejs/node/pull/19322)
- [[`633e23a618`](https://github.com/nodejs/node/commit/633e23a618)] - **tools**: update certdata.txt (Ben Noordhuis) [#19322](https://github.com/nodejs/node/pull/19322)

Windows 32-bit Installer: https://nodejs.org/dist/v8.11.0/node-v8.11.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v8.11.0/node-v8.11.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v8.11.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v8.11.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v8.11.0/node-v8.11.0.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v8.11.0/node-v8.11.0-darwin-x64.tar.gz \
Linux 32-bit Binary: https://nodejs.org/dist/v8.11.0/node-v8.11.0-linux-x86.tar.xz \
Linux 64-bit Binary: https://nodejs.org/dist/v8.11.0/node-v8.11.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v8.11.0/node-v8.11.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v8.11.0/node-v8.11.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v8.11.0/node-v8.11.0-aix-ppc64.tar.gz \
SmartOS 32-bit Binary: https://nodejs.org/dist/v8.11.0/node-v8.11.0-sunos-x86.tar.xz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v8.11.0/node-v8.11.0-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v8.11.0/node-v8.11.0-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v8.11.0/node-v8.11.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v8.11.0/node-v8.11.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v8.11.0/node-v8.11.0.tar.gz \
Other release files: https://nodejs.org/dist/v8.11.0/ \
Documentation: https://nodejs.org/docs/v8.11.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

79cca390e53f6b63ea66b26f621e0253db065b005775aeec83a0d7762c911860  node-v8.11.0-aix-ppc64.tar.gz
408323335b8c691d75397c76ddd7b00490852652c78c813c586ba7eccc5c382b  node-v8.11.0-darwin-x64.tar.gz
06cbfbebd07ceda2197024a0e32ef91fbb3ff77a33c60e550fa85b5d1bf13481  node-v8.11.0-darwin-x64.tar.xz
ea0f11c29ab48c735d16ffe3237491125182355df4904ee3a9846871ed5ea7fe  node-v8.11.0-headers.tar.gz
eae49550a5a49a251b556e3aebf1e8e73dbc52ee80d8045cf7913b745219c126  node-v8.11.0-headers.tar.xz
2241f9eef968308fc4e25662ed49faf9fa1aa5dd513400197c2a15f3494b3388  node-v8.11.0-linux-arm64.tar.gz
a28f599a14ca9ef4062fa1e605b69cd046f81d3fc9a7d6dde1856fb593004b3a  node-v8.11.0-linux-arm64.tar.xz
81a11a2d1f4db0de6522e3b6fd739b4615e5df1438a9946ea9ddc428ee0660ff  node-v8.11.0-linux-armv6l.tar.gz
ac7075030e52cff0691b23673223e5a5104fc1e17e48944498c76836fae7d096  node-v8.11.0-linux-armv6l.tar.xz
fa41036876164d81191b73d687f40c795c009621daab0e7e91117eb6c355ca03  node-v8.11.0-linux-armv7l.tar.gz
032394421eac0a187ba9f1a1dda80511e02c0eaa8732420dccdebf2645685652  node-v8.11.0-linux-armv7l.tar.xz
9e645cc4fe0ae8f0b23858fdf47a679102674807dabbd1ca11439eaf6f3fee88  node-v8.11.0-linux-ppc64le.tar.gz
a55a00d1219a0988721d219321b7f1c01d2b1c76bcadb9a7383ae6b24637390f  node-v8.11.0-linux-ppc64le.tar.xz
3dc6f711fe02e38b95746cf0d9453f5e024bda549da3c43e52d3fdcebe6f3ef8  node-v8.11.0-linux-s390x.tar.gz
d2349f0354bdbf38f1be791e6ca0b4f8625984f686be05b38a3dd2d6252eacc3  node-v8.11.0-linux-s390x.tar.xz
93ab3ee41ac0731497e1c0fdd3de587dd7fa9e80b149d48c385b7756c9b3bb36  node-v8.11.0-linux-x64.tar.gz
180ef8c2a39c1696b9a05832883ed981ba11475ffa44ca77781a8d1c1954f944  node-v8.11.0-linux-x64.tar.xz
036eecc0a5cd5a7be38305a823743b94c7945f3c4b3cf540650ba4504abc5129  node-v8.11.0-linux-x86.tar.gz
e6bdd663706989b3bb004a84e3c45a227a3c7a00bc170c4fcf09f7ec8604a5c7  node-v8.11.0-linux-x86.tar.xz
1c90d137be7c5db449c4395d1e3c1eab5e8591098d287bc5acd790b21c36d9c5  node-v8.11.0.pkg
86feab1746044577d273a819cbb68b3f4e45406924c1ccca83f1c99b78f39674  node-v8.11.0-sunos-x64.tar.gz
bab8834d37325bc8971040087a0357b594be358bdf9baa95110e9f5ed48579e5  node-v8.11.0-sunos-x64.tar.xz
91d82daa8d49998eb8441b9182d3eb5e7d15dd3fb94d6c73781afdb1872c6c55  node-v8.11.0-sunos-x86.tar.gz
59aa9a7a98a6bbeeaf854f03838a1b28a298e8e13f1cf4866ecd7b86b1708623  node-v8.11.0-sunos-x86.tar.xz
7683a674259298fc53dd020e210d062aaf02698980fe293c64ed2daee13466a4  node-v8.11.0.tar.gz
1ad354cf4ac96a904007b907fc1fe7fa2fd3692036da0c2fb1790f7a0204ab3f  node-v8.11.0.tar.xz
09b04277ed01e7aefa584f16730a6a99944306266b206526ea7b085d095147e8  node-v8.11.0-win-x64.7z
55b9c8d48b59569117a63fdb26e1de05e792c37f563feb7d44b4cd59be96aff8  node-v8.11.0-win-x64.zip
5178075b2dc90d2142ea5daf894f011bb8bc7ddb8ce9d4e41a8da4f0740b16e8  node-v8.11.0-win-x86.7z
8bdbbb9cfe5993f456e4cc205dddf9b7f11195a7dbb3ad0a3b60900ec0bc9379  node-v8.11.0-win-x86.zip
2bf06d6dedcff62fa0e14fdf29c643a21ff2dd8767842a3766b31f9927223ac6  node-v8.11.0-x64.msi
2bdb0e408b49ab9f05bab119b50087f95f68d690623ddd5003ef2a9e1ce7cf29  node-v8.11.0-x86.msi
1c95e1fae11d6ef432b96479dcd5219d5d4e31f8b7b4f3b7cdcf54afa0b03874  win-x64/node.exe
e9aa974608b793495aeb30fbd41968c641aaa88ad03831dec0529a19fdb46308  win-x64/node.lib
cfe9af7bf8c75d27c5a1ded7f68f843fbe5d95a2ba3be64a129ba9a6e7c69615  win-x64/node_pdb.7z
f510ba6e287cd581e524e1769b1e4a49afcdd30cfbae679bb459018372d2486a  win-x64/node_pdb.zip
e1d4fda67c35f950768bc52155042fdc815191b3863e636396f5c608cf947918  win-x86/node.exe
a349826bda95618a553fb9a6efc4d3e7a46ab2be08714ceaa5a166b6ac0d8fc0  win-x86/node.lib
2d50b4dfdd44a51aa41cf16db46e46f90309ad6d3782785045fa1d2d9f2c7d1d  win-x86/node_pdb.7z
65dbbfc5fce3e2cbc581838e9a151d277ab4aad974ec111931692f76e295cf8a  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQEzBAEBCAAdFiEEDv/hvO/ZyE49CYFSkzsB9AtcqUYFAlq7wjQACgkQkzsB9Atc
qUbzlAf/YJ6mUL06/OGYiFYP4aXDDF0D6VbYk9NmpgsOptEJCCJbwmOwf+4q+hpn
wuWNvbd3qS0sNTFS3EHOC1R/ecpkCPdQpc8x90YetGY5V48AI2ukFmiAJWE8xAa8
pU3EpcqoqiwWPGgwr8DK7wCrOa/YAtsNDmW9WiDMcepxL/OAPn9D4386e3oLPuvX
ZOqKJpwrxVshS3CJ1gETcHgkE4zOnXd8+QLk8nLbc6le8BmQxI6Em88fjNNVVdAF
f6KAoN77jFqmZejRzQwE3hBYV2QcozGDgZ/hozXXs5KD5NcueLBZJ5/ZpOmZjZdY
cfODRtATP9dnw7hVAznjKNIfA0FVOQ==
=CA9D
-----END PGP SIGNATURE-----

```
