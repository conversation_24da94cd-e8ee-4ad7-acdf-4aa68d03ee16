---
date: '2016-08-16T16:13:31.973Z'
category: release
title: Node v4.5.0 (LTS)
layout: blog-post
author: <PERSON><PERSON>
---

### Notable Changes

Semver Minor:

- **buffer**:
  - backport new buffer constructor APIs to v4.x (Сковорода Никит<PERSON> Анд<PERSON><PERSON>евич) [#7562](https://github.com/nodejs/node/pull/7562)
  - backport --zero-fill-buffers cli option (<PERSON>) [#5745](https://github.com/nodejs/node/pull/5745)
- **build**:
  - add Intel Vtune profiling support (Chunyang Dai) [#5527](https://github.com/nodejs/node/pull/5527)
- **repl**:
  - copying tabs shouldn't trigger completion (<PERSON>) [#5958](https://github.com/nodejs/node/pull/5958)
- **src**:
  - add node::FreeEnvironment public API (<PERSON>) [#3098](https://github.com/nodejs/node/pull/3098)
- **test**:
  - run v8 tests from node tree (<PERSON><PERSON><PERSON>) [#4704](https://github.com/nodejs/node/pull/4704)
- **V8**:
  - Add post mortem data to improve object inspection and function's context variables inspection (Fedor Indutny) [#3779](https://github.com/nodejs/node/pull/3779)

Semver Patch:

- **buffer**:
  - ignore negative allocation lengths (Anna Henningsen) [#7562](https://github.com/nodejs/node/pull/7562)
- **crypto**:
  - update root certificates (Ben Noordhuis) [#7363](https://github.com/nodejs/node/pull/7363)
- **libuv**:
  - upgrade libuv to 1.9.1 (Saúl Ibarra Corretgé) [#6796](https://github.com/nodejs/node/pull/6796)
  - upgrade libuv to 1.9.0 (Saúl Ibarra Corretgé) [#5994](https://github.com/nodejs/node/pull/5994)
- **npm**:
  - upgrade to 2.15.9 (Kat Marchán) [#7692](https://github.com/nodejs/node/pull/7692)

### Commits

- [[`a4888926a2`](https://github.com/nodejs/node/commit/a4888926a2)] - **assert**: remove unneeded arguments special handling (Rich Trott) [#7413](https://github.com/nodejs/node/pull/7413)
- [[`39e24742f8`](https://github.com/nodejs/node/commit/39e24742f8)] - **assert**: allow circular references (Rich Trott) [#6432](https://github.com/nodejs/node/pull/6432)
- [[`271927f29e`](https://github.com/nodejs/node/commit/271927f29e)] - **async_wrap**: pass uid to JS as double (Trevor Norris) [#7096](https://github.com/nodejs/node/pull/7096)
- [[`747f107188`](https://github.com/nodejs/node/commit/747f107188)] - **async_wrap**: don't abort on callback exception (Trevor Norris) [#5756](https://github.com/nodejs/node/pull/5756)
- [[`c06e2b07b6`](https://github.com/nodejs/node/commit/c06e2b07b6)] - **async_wrap**: notify post if intercepted exception (Trevor Norris) [#5756](https://github.com/nodejs/node/pull/5756)
- [[`0642a146b3`](https://github.com/nodejs/node/commit/0642a146b3)] - **async_wrap**: setupHooks now accepts object (Trevor Norris) [#5756](https://github.com/nodejs/node/pull/5756)
- [[`75ecf8eb07`](https://github.com/nodejs/node/commit/75ecf8eb07)] - **async_wrap**: add parent uid to init hook (Andreas Madsen) [#4600](https://github.com/nodejs/node/pull/4600)
- [[`e10eebffa5`](https://github.com/nodejs/node/commit/e10eebffa5)] - **async_wrap**: make uid the first argument in init (Andreas Madsen) [#4600](https://github.com/nodejs/node/pull/4600)
- [[`13d465bcf6`](https://github.com/nodejs/node/commit/13d465bcf6)] - **async_wrap**: add uid to all asyncWrap hooks (Andreas Madsen) [#4600](https://github.com/nodejs/node/pull/4600)
- [[`046d651118`](https://github.com/nodejs/node/commit/046d651118)] - **benchmark**: fix child-process-exec-stdout on win (Bartosz Sosnowski) [#7178](https://github.com/nodejs/node/pull/7178)
- [[`4b464ce4bf`](https://github.com/nodejs/node/commit/4b464ce4bf)] - **benchmark**: remove unused variables (Rich Trott) [#7600](https://github.com/nodejs/node/pull/7600)
- [[`b95e5d7948`](https://github.com/nodejs/node/commit/b95e5d7948)] - **benchmark**: add benchmark for url.format() (Rich Trott) [#7250](https://github.com/nodejs/node/pull/7250)
- [[`1bd62c7c34`](https://github.com/nodejs/node/commit/1bd62c7c34)] - **benchmark**: add benchmark for Buffer.concat (Anna Henningsen) [#7054](https://github.com/nodejs/node/pull/7054)
- [[`08cd81b050`](https://github.com/nodejs/node/commit/08cd81b050)] - **benchmark**: add util.format benchmark (Evan Lucas) [#5360](https://github.com/nodejs/node/pull/5360)
- [[`7dbb0d0084`](https://github.com/nodejs/node/commit/7dbb0d0084)] - **buffer**: fix dataview-set benchmark (Ingvar Stepanyan) [#6922](https://github.com/nodejs/node/pull/6922)
- [[`200429e9e1`](https://github.com/nodejs/node/commit/200429e9e1)] - **buffer**: ignore negative allocation lengths (Anna Henningsen) [#7562](https://github.com/nodejs/node/pull/7562)
- [[`709048134c`](https://github.com/nodejs/node/commit/709048134c)] - **(SEMVER-MINOR)** **buffer**: backport new buffer constructor APIs to v4.x (Сковорода Никита Андреевич) [#7562](https://github.com/nodejs/node/pull/7562)
- [[`fb03e57de2`](https://github.com/nodejs/node/commit/fb03e57de2)] - **(SEMVER-MINOR)** **buffer**: backport --zero-fill-buffers cli option (James M Snell) [#5745](https://github.com/nodejs/node/pull/5745)
- [[`236491e698`](https://github.com/nodejs/node/commit/236491e698)] - **build**: update build-addons when node-gyp changes (Lance Ball) [#6787](https://github.com/nodejs/node/pull/6787)
- [[`8a7c5fdbd2`](https://github.com/nodejs/node/commit/8a7c5fdbd2)] - **build**: add REPLACEME tag for version info in docs (Ben Noordhuis) [#6864](https://github.com/nodejs/node/pull/6864)
- [[`da1e13fde7`](https://github.com/nodejs/node/commit/da1e13fde7)] - **build**: add Make `doc-only` target (Jesse McCarthy) [#3888](https://github.com/nodejs/node/pull/3888)
- [[`0db3aa9afa`](https://github.com/nodejs/node/commit/0db3aa9afa)] - **build**: remove unused files from CPPLINT_FILES (Ben Noordhuis) [#7462](https://github.com/nodejs/node/pull/7462)
- [[`5290c9d38c`](https://github.com/nodejs/node/commit/5290c9d38c)] - **build**: use BUILDTYPE when building V8 in Makefile (Michaël Zasso) [#7482](https://github.com/nodejs/node/pull/7482)
- [[`79bd39c202`](https://github.com/nodejs/node/commit/79bd39c202)] - **build**: add v8 requirement to test-v8\* in Makefile (Michaël Zasso) [#7482](https://github.com/nodejs/node/pull/7482)
- [[`65b75b51a6`](https://github.com/nodejs/node/commit/65b75b51a6)] - **build**: unbreak configure with python 2.6 (Ben Noordhuis) [#6874](https://github.com/nodejs/node/pull/6874)
- [[`8513232c82`](https://github.com/nodejs/node/commit/8513232c82)] - **build**: split CI rules in Makefile (João Reis) [#7317](https://github.com/nodejs/node/pull/7317)
- [[`13d0e463b0`](https://github.com/nodejs/node/commit/13d0e463b0)] - **build**: enable compilation for linuxOne (Michael Dawson) [#5941](https://github.com/nodejs/node/pull/5941)
- [[`834ea2c5c0`](https://github.com/nodejs/node/commit/834ea2c5c0)] - **(SEMVER-MINOR)** **build,src**: add Intel Vtune profiling support (Chunyang Dai) [#5527](https://github.com/nodejs/node/pull/5527)
- [[`ea20796e9d`](https://github.com/nodejs/node/commit/ea20796e9d)] - **build,test**: fix build-addons dependency chain (Ben Noordhuis) [#6652](https://github.com/nodejs/node/pull/6652)
- [[`6a08535dd1`](https://github.com/nodejs/node/commit/6a08535dd1)] - **child_process**: preserve argument type (Rich Trott) [#7391](https://github.com/nodejs/node/pull/7391)
- [[`fd05b0b289`](https://github.com/nodejs/node/commit/fd05b0b289)] - **_Revert_** "**child_process**: measure buffer length in bytes" (Rich Trott) [#7391](https://github.com/nodejs/node/pull/7391)
- [[`8eb18e4289`](https://github.com/nodejs/node/commit/8eb18e4289)] - **child_process**: measure buffer length in bytes (Rich Trott) [#6764](https://github.com/nodejs/node/pull/6764)
- [[`4ee863d956`](https://github.com/nodejs/node/commit/4ee863d956)] - **child_process**: allow buffer encoding in spawnSync (cjihrig) [#6939](https://github.com/nodejs/node/pull/6939)
- [[`0b8124f205`](https://github.com/nodejs/node/commit/0b8124f205)] - **child_process**: emit IPC messages on next tick (cjihrig) [#6909](https://github.com/nodejs/node/pull/6909)
- [[`20d3378969`](https://github.com/nodejs/node/commit/20d3378969)] - **cluster**: reset handle index on close (Santiago Gimeno) [#6981](https://github.com/nodejs/node/pull/6981)
- [[`09349a8b92`](https://github.com/nodejs/node/commit/09349a8b92)] - **cluster**: don't send messages if no IPC channel (Santiago Gimeno) [#7132](https://github.com/nodejs/node/pull/7132)
- [[`6ece2a0322`](https://github.com/nodejs/node/commit/6ece2a0322)] - **cluster**: rewrite debug ports consistently (cjihrig) [#7050](https://github.com/nodejs/node/pull/7050)
- [[`8cba3b2f72`](https://github.com/nodejs/node/commit/8cba3b2f72)] - **cluster**: guard against undefined message handlers (cjihrig) [#6902](https://github.com/nodejs/node/pull/6902)
- [[`f152adf5b7`](https://github.com/nodejs/node/commit/f152adf5b7)] - **cluster**: close ownerless handles on disconnect() (cjihrig) [#6909](https://github.com/nodejs/node/pull/6909)
- [[`65624440bf`](https://github.com/nodejs/node/commit/65624440bf)] - **crypto**: allow GCM ciphers to have longer IV length (Michael Wain) [#6376](https://github.com/nodejs/node/pull/6376)
- [[`1e0cede3a6`](https://github.com/nodejs/node/commit/1e0cede3a6)] - **crypto**: update root certificates (Ben Noordhuis) [#7363](https://github.com/nodejs/node/pull/7363)
- [[`3be5cdcd43`](https://github.com/nodejs/node/commit/3be5cdcd43)] - **debugger**: remove obsolete setTimeout (Rich Trott) [#7154](https://github.com/nodejs/node/pull/7154)
- [[`74a5e911c0`](https://github.com/nodejs/node/commit/74a5e911c0)] - **debugger**: propagate --debug-port= to debuggee (Ben Noordhuis) [#3470](https://github.com/nodejs/node/pull/3470)
- [[`af4940d63b`](https://github.com/nodejs/node/commit/af4940d63b)] - **deps**: upgrade npm in LTS to 2.15.9 (Kat Marchán) [#7692](https://github.com/nodejs/node/pull/7692)
- [[`da7b74b9bc`](https://github.com/nodejs/node/commit/da7b74b9bc)] - **deps**: upgrade libuv to 1.9.1 (Saúl Ibarra Corretgé) [#6796](https://github.com/nodejs/node/pull/6796)
- [[`94eb980ca5`](https://github.com/nodejs/node/commit/94eb980ca5)] - **deps**: upgrade libuv to 1.9.0 (Saúl Ibarra Corretgé) [#5994](https://github.com/nodejs/node/pull/5994)
- [[`4107b5d200`](https://github.com/nodejs/node/commit/4107b5d200)] - **deps**: backport 22c5e46 from V8 (Julien Gilli) [#7584](https://github.com/nodejs/node/pull/7584)
- [[`e06ab64705`](https://github.com/nodejs/node/commit/e06ab64705)] - **deps**: update to http-parser 2.7.0 (Fedor Indutny) [#6279](https://github.com/nodejs/node/pull/6279)
- [[`1164f542db`](https://github.com/nodejs/node/commit/1164f542db)] - **deps**: fix segfault during gc (Ali Ijaz Sheikh) [#7303](https://github.com/nodejs/node/pull/7303)
- [[`d9e9d9fb11`](https://github.com/nodejs/node/commit/d9e9d9fb11)] - **deps**: backport e7cc609 from upstream V8 (Ali Ijaz Sheikh) [#7303](https://github.com/nodejs/node/pull/7303)
- [[`9809992436`](https://github.com/nodejs/node/commit/9809992436)] - **(SEMVER-MINOR)** **deps**: backport 9c927d0f01 from V8 upstream (Myles Borins) [#7451](https://github.com/nodejs/node/pull/7451)
- [[`da9595fc47`](https://github.com/nodejs/node/commit/da9595fc47)] - **(SEMVER-MINOR)** **deps**: cherry-pick 68e89fb from v8's upstream (Fedor Indutny) [#3779](https://github.com/nodejs/node/pull/3779)
- [[`e9ff0f8fb2`](https://github.com/nodejs/node/commit/e9ff0f8fb2)] - **doc**: make doc-only -> fallback to user binary (Robert Jefe Lindstaedt) [#6906](https://github.com/nodejs/node/pull/6906)
- [[`b869cdb876`](https://github.com/nodejs/node/commit/b869cdb876)] - **doc**: fix deprecation warnings in addon examples (Ben Noordhuis) [#6652](https://github.com/nodejs/node/pull/6652)
- [[`ec25f38120`](https://github.com/nodejs/node/commit/ec25f38120)] - **doc**: add `added:` information for buffer (Anna Henningsen) [#6495](https://github.com/nodejs/node/pull/6495)
- [[`1e86d16812`](https://github.com/nodejs/node/commit/1e86d16812)] - **doc**: buffers are not sent over IPC with a socket (Tim Kuijsten) [#6951](https://github.com/nodejs/node/pull/6951)
- [[`5c1d8e1f0f`](https://github.com/nodejs/node/commit/5c1d8e1f0f)] - **doc**: add `added:` information for http (Anna Henningsen) [#7392](https://github.com/nodejs/node/pull/7392)
- [[`60c054bc11`](https://github.com/nodejs/node/commit/60c054bc11)] - **doc**: add information for IncomingMessage.destroy() (Rich Trott) [#7237](https://github.com/nodejs/node/pull/7237)
- [[`1a5c025f32`](https://github.com/nodejs/node/commit/1a5c025f32)] - **doc**: remove superfluos backticks in process.md (Anna Henningsen) [#7681](https://github.com/nodejs/node/pull/7681)
- [[`fcb4e410e4`](https://github.com/nodejs/node/commit/fcb4e410e4)] - **doc**: add `added:` information for process (Bryan English) [#6589](https://github.com/nodejs/node/pull/6589)
- [[`9b8565c42a`](https://github.com/nodejs/node/commit/9b8565c42a)] - **doc**: add `added:` information for tls (Italo A. Casas) [#7018](https://github.com/nodejs/node/pull/7018)
- [[`fd4aa6c16a`](https://github.com/nodejs/node/commit/fd4aa6c16a)] - **doc**: correct `added:` information for fs.access (Richard Lau) [#7299](https://github.com/nodejs/node/pull/7299)
- [[`1e9d27cbcc`](https://github.com/nodejs/node/commit/1e9d27cbcc)] - **doc**: add `added:` information for fs (Anna Henningsen) [#6717](https://github.com/nodejs/node/pull/6717)
- [[`2244a3c250`](https://github.com/nodejs/node/commit/2244a3c250)] - **doc**: adds 'close' events to fs.ReadStream and fs.WriteStream (Jenna Vuong) [#6499](https://github.com/nodejs/node/pull/6499)
- [[`88f46b886a`](https://github.com/nodejs/node/commit/88f46b886a)] - **doc**: add `added:` information for timers (Anna Henningsen) [#7493](https://github.com/nodejs/node/pull/7493)
- [[`a53253a232`](https://github.com/nodejs/node/commit/a53253a232)] - **doc**: add `added:` information for zlib (Anna Henningsen) [#6840](https://github.com/nodejs/node/pull/6840)
- [[`7abfb6e8dc`](https://github.com/nodejs/node/commit/7abfb6e8dc)] - **doc**: add `added:` information for vm (Anna Henningsen) [#7011](https://github.com/nodejs/node/pull/7011)
- [[`3e3471fb5f`](https://github.com/nodejs/node/commit/3e3471fb5f)] - **doc**: add `added:` information for v8 (Rich Trott) [#6684](https://github.com/nodejs/node/pull/6684)
- [[`1758f02ec1`](https://github.com/nodejs/node/commit/1758f02ec1)] - **doc**: add `added:` information for url (Bryan English) [#6593](https://github.com/nodejs/node/pull/6593)
- [[`3c8f19fcdf`](https://github.com/nodejs/node/commit/3c8f19fcdf)] - **doc**: add `added:` in for `tty` (Rich Trott) [#6783](https://github.com/nodejs/node/pull/6783)
- [[`5b50b1c255`](https://github.com/nodejs/node/commit/5b50b1c255)] - **doc**: add `added:` info for `string_decoder` (Rich Trott) [#6741](https://github.com/nodejs/node/pull/6741)
- [[`4474e83b78`](https://github.com/nodejs/node/commit/4474e83b78)] - **doc**: add `added:` information for repl (Anna Henningsen) [#7256](https://github.com/nodejs/node/pull/7256)
- [[`e6d7bfcbe7`](https://github.com/nodejs/node/commit/e6d7bfcbe7)] - **doc**: add `added:` information for readline (Julian Duque) [#6996](https://github.com/nodejs/node/pull/6996)
- [[`eec0c635ee`](https://github.com/nodejs/node/commit/eec0c635ee)] - **doc**: add `added:` information for querystring (Bryan English) [#6593](https://github.com/nodejs/node/pull/6593)
- [[`a870cdcd1f`](https://github.com/nodejs/node/commit/a870cdcd1f)] - **doc**: add `added:` information for punycode (Daniel Wang) [#6805](https://github.com/nodejs/node/pull/6805)
- [[`f1a37ad749`](https://github.com/nodejs/node/commit/f1a37ad749)] - **doc**: add `added:` information for path (Julian Duque) [#6985](https://github.com/nodejs/node/pull/6985)
- [[`8b53f4b27c`](https://github.com/nodejs/node/commit/8b53f4b27c)] - **doc**: add `added:` information for os (Bryan English) [#6609](https://github.com/nodejs/node/pull/6609)
- [[`78d361b22b`](https://github.com/nodejs/node/commit/78d361b22b)] - **doc**: add `added` information for net (Italo A. Casas) [#7038](https://github.com/nodejs/node/pull/7038)
- [[`b08ff33c01`](https://github.com/nodejs/node/commit/b08ff33c01)] - **doc**: add `added:` information for https (Anna Henningsen) [#7392](https://github.com/nodejs/node/pull/7392)
- [[`1d99059bb1`](https://github.com/nodejs/node/commit/1d99059bb1)] - **doc**: add `added:` information for dns (Julian Duque) [#7021](https://github.com/nodejs/node/pull/7021)
- [[`a0ca24b798`](https://github.com/nodejs/node/commit/a0ca24b798)] - **doc**: add `added:` information for console (Adrian Estrada) [#6995](https://github.com/nodejs/node/pull/6995)
- [[`eb08c17a20`](https://github.com/nodejs/node/commit/eb08c17a20)] - **doc**: add `added:` data for cli.md (Rich Trott) [#6960](https://github.com/nodejs/node/pull/6960)
- [[`ec9038478f`](https://github.com/nodejs/node/commit/ec9038478f)] - **doc**: add `added:` information for child_process (Anna Henningsen) [#6927](https://github.com/nodejs/node/pull/6927)
- [[`e52b2b07d7`](https://github.com/nodejs/node/commit/e52b2b07d7)] - **doc**: add `added:` information for assert (Rich Trott) [#6688](https://github.com/nodejs/node/pull/6688)
- [[`75e4f74c54`](https://github.com/nodejs/node/commit/75e4f74c54)] - **doc**: fix cluster worker 'message' event (cjihrig) [#7309](https://github.com/nodejs/node/pull/7309)
- [[`de5e2357fc`](https://github.com/nodejs/node/commit/de5e2357fc)] - **doc**: dns.resolve fix callback argument description (Quentin Headen) [#7532](https://github.com/nodejs/node/pull/7532)
- [[`0f903bb722`](https://github.com/nodejs/node/commit/0f903bb722)] - **doc**: add benchmark who-to-CC info (Rich Trott) [#7604](https://github.com/nodejs/node/pull/7604)
- [[`700c6d9be8`](https://github.com/nodejs/node/commit/700c6d9be8)] - **doc**: added information on how to run the linter. (Diosney Sarmiento) [#7534](https://github.com/nodejs/node/pull/7534)
- [[`537f33351e`](https://github.com/nodejs/node/commit/537f33351e)] - **doc**: fix minor style issues in http.md (Rich Trott) [#7528](https://github.com/nodejs/node/pull/7528)
- [[`33a08b0414`](https://github.com/nodejs/node/commit/33a08b0414)] - **doc**: add bartosz sosnowski to colaborators (Bartosz Sosnowski) [#7567](https://github.com/nodejs/node/pull/7567)
- [[`186af29298`](https://github.com/nodejs/node/commit/186af29298)] - **doc**: fix detached child stdio example (cjihrig) [#7540](https://github.com/nodejs/node/pull/7540)
- [[`066cefb6de`](https://github.com/nodejs/node/commit/066cefb6de)] - **doc**: improve usage of `zero`/`0` (Rich Trott) [#7466](https://github.com/nodejs/node/pull/7466)
- [[`6c94c67b73`](https://github.com/nodejs/node/commit/6c94c67b73)] - **doc**: fix "sign.verify" typo in crypto doc. (Ruslan Iusupov) [#7411](https://github.com/nodejs/node/pull/7411)
- [[`35ee35cba2`](https://github.com/nodejs/node/commit/35ee35cba2)] - **doc**: clarify child_process stdout/stderr types (sartrey) [#7361](https://github.com/nodejs/node/pull/7361)
- [[`71ef71cff8`](https://github.com/nodejs/node/commit/71ef71cff8)] - **doc**: add CTC meeting minutes 2016-06-15 (Josh Gavant) [#7320](https://github.com/nodejs/node/pull/7320)
- [[`13d60cab7c`](https://github.com/nodejs/node/commit/13d60cab7c)] - **doc**: add lance to collaborators (Lance Ball) [#7407](https://github.com/nodejs/node/pull/7407)
- [[`9122b3b665`](https://github.com/nodejs/node/commit/9122b3b665)] - **doc**: update "who to cc in issues" chart (Jeremiah Senkpiel) [#6694](https://github.com/nodejs/node/pull/6694)
- [[`ccb278d330`](https://github.com/nodejs/node/commit/ccb278d330)] - **doc**: mention http request "aborted" events (Kyle E. Mitchell) [#7270](https://github.com/nodejs/node/pull/7270)
- [[`868af29f2b`](https://github.com/nodejs/node/commit/868af29f2b)] - **doc**: add RReverser to collaborators (Ingvar Stepanyan) [#7370](https://github.com/nodejs/node/pull/7370)
- [[`f8fe474825`](https://github.com/nodejs/node/commit/f8fe474825)] - **doc**: fixing minor typo in AtExit hooks section (Daniel Bevenius) [#7485](https://github.com/nodejs/node/pull/7485)
- [[`4a7e333287`](https://github.com/nodejs/node/commit/4a7e333287)] - **doc**: use `Buffer.byteLength` for Content-Length (kimown) [#7274](https://github.com/nodejs/node/pull/7274)
- [[`85f70b36e4`](https://github.com/nodejs/node/commit/85f70b36e4)] - **doc**: clarify use of `0` port value (Rich Trott) [#7206](https://github.com/nodejs/node/pull/7206)
- [[`57ba51ec46`](https://github.com/nodejs/node/commit/57ba51ec46)] - **doc**: fix IRC link (Ilkka Myller) [#7210](https://github.com/nodejs/node/pull/7210)
- [[`ef37a2e80f`](https://github.com/nodejs/node/commit/ef37a2e80f)] - **doc**: add internal link in GOVERNANCE.md (Rich Trott) [#7279](https://github.com/nodejs/node/pull/7279)
- [[`c9ef04a1b2`](https://github.com/nodejs/node/commit/c9ef04a1b2)] - **doc**: fix events typo (Greyson Parrelli) [#7329](https://github.com/nodejs/node/pull/7329)
- [[`0013af61de`](https://github.com/nodejs/node/commit/0013af61de)] - **doc**: fix header depth of util.isSymbol (James M Snell) [#7138](https://github.com/nodejs/node/pull/7138)
- [[`96de3f8820`](https://github.com/nodejs/node/commit/96de3f8820)] - **doc**: Add CII Best Practices badge to README.md (David A. Wheeler) [#6819](https://github.com/nodejs/node/pull/6819)
- [[`146cba1f60`](https://github.com/nodejs/node/commit/146cba1f60)] - **doc**: improve debugger doc prose (Rich Trott) [#7007](https://github.com/nodejs/node/pull/7007)
- [[`694e34458b`](https://github.com/nodejs/node/commit/694e34458b)] - **doc**: fix typos in WORKING_GROUPS.md (Joao Andrade) [#7032](https://github.com/nodejs/node/pull/7032)
- [[`fbdc16a8a4`](https://github.com/nodejs/node/commit/fbdc16a8a4)] - **doc**: update labels and CI info in onboarding doc (Rich Trott) [#7006](https://github.com/nodejs/node/pull/7006)
- [[`1c65f1e3f6`](https://github.com/nodejs/node/commit/1c65f1e3f6)] - **doc**: add info on what's used for fswatch on AIX (Michael Dawson) [#6837](https://github.com/nodejs/node/pull/6837)
- [[`72e8ee570a`](https://github.com/nodejs/node/commit/72e8ee570a)] - **doc**: improve server.listen() documentation prose (Rich Trott) [#7000](https://github.com/nodejs/node/pull/7000)
- [[`649d201d63`](https://github.com/nodejs/node/commit/649d201d63)] - **doc**: improve `server.address()` doc text (Rich Trott) [#7001](https://github.com/nodejs/node/pull/7001)
- [[`e2e85ced1d`](https://github.com/nodejs/node/commit/e2e85ced1d)] - **doc**: clarified use of sexual language in the CoC (Bryan Hughes) [#6973](https://github.com/nodejs/node/pull/6973)
- [[`f395f6f5b2`](https://github.com/nodejs/node/commit/f395f6f5b2)] - **doc**: add yorkie to collaborators (Yazhong Liu) [#7004](https://github.com/nodejs/node/pull/7004)
- [[`c5051ef643`](https://github.com/nodejs/node/commit/c5051ef643)] - **doc**: add firedfox to collaborators (Daniel Wang) [#6961](https://github.com/nodejs/node/pull/6961)
- [[`2ef08323c6`](https://github.com/nodejs/node/commit/2ef08323c6)] - **doc**: add bmeck to collaborators (Bradley Meck) [#6962](https://github.com/nodejs/node/pull/6962)
- [[`d1a0a146b3`](https://github.com/nodejs/node/commit/d1a0a146b3)] - **doc**: Add CTC meeting minutes for 2016-05-04 (Michael Dawson) [#6579](https://github.com/nodejs/node/pull/6579)
- [[`0a85987899`](https://github.com/nodejs/node/commit/0a85987899)] - **doc**: update build instructions for Windows (João Reis) [#7285](https://github.com/nodejs/node/pull/7285)
- [[`629a76f9fb`](https://github.com/nodejs/node/commit/629a76f9fb)] - **doc**: remove cluster.setupMaster() myth (cjihrig) [#7179](https://github.com/nodejs/node/pull/7179)
- [[`5b807ac791`](https://github.com/nodejs/node/commit/5b807ac791)] - **doc**: specify how to link issues in commit log (Luigi Pinca) [#7161](https://github.com/nodejs/node/pull/7161)
- [[`350f4cf292`](https://github.com/nodejs/node/commit/350f4cf292)] - **doc**: server.listen truncates socket path on unix (Jean Regisser) [#6659](https://github.com/nodejs/node/pull/6659)
- [[`7813af7f16`](https://github.com/nodejs/node/commit/7813af7f16)] - **doc**: Add resolveNaptr and naptr rrtype docs (Doug Wade) [#6586](https://github.com/nodejs/node/pull/6586)
- [[`5380743208`](https://github.com/nodejs/node/commit/5380743208)] - **doc**: document socket.destroyed (Tushar Mathur) [#6128](https://github.com/nodejs/node/pull/6128)
- [[`f0edf87df1`](https://github.com/nodejs/node/commit/f0edf87df1)] - **doc**: add vm example, be able to require modules (Robert Jefe Lindstaedt) [#5323](https://github.com/nodejs/node/pull/5323)
- [[`9121e94e62`](https://github.com/nodejs/node/commit/9121e94e62)] - **doc**: note that process.config can and will be changed (James M Snell) [#6266](https://github.com/nodejs/node/pull/6266)
- [[`c237ac3d68`](https://github.com/nodejs/node/commit/c237ac3d68)] - **doc**: git mv to .md (Robert Jefe Lindstaedt) [#4747](https://github.com/nodejs/node/pull/4747)
- [[`6324723cc1`](https://github.com/nodejs/node/commit/6324723cc1)] - **doc,dgram**: fix addMembership documentation (Santiago Gimeno) [#7244](https://github.com/nodejs/node/pull/7244)
- [[`15bb0beab2`](https://github.com/nodejs/node/commit/15bb0beab2)] - **doc,test**: add `How to write a Node.js test` guide (Santiago Gimeno) [#6984](https://github.com/nodejs/node/pull/6984)
- [[`9d13337183`](https://github.com/nodejs/node/commit/9d13337183)] - **http**: wait for both prefinish/end to keepalive (Fedor Indutny) [#7149](https://github.com/nodejs/node/pull/7149)
- [[`ece428ea63`](https://github.com/nodejs/node/commit/ece428ea63)] - **http**: fix no dumping after `maybeReadMore` (Fedor Indutny) [#7211](https://github.com/nodejs/node/pull/7211)
- [[`07fd52e5aa`](https://github.com/nodejs/node/commit/07fd52e5aa)] - **http**: skip body and next message of CONNECT res (Fedor Indutny) [#6279](https://github.com/nodejs/node/pull/6279)
- [[`6f312b3a91`](https://github.com/nodejs/node/commit/6f312b3a91)] - **http_parser**: use `MakeCallback` (Trevor Norris) [#5419](https://github.com/nodejs/node/pull/5419)
- [[`373ffc5bad`](https://github.com/nodejs/node/commit/373ffc5bad)] - **installer**: don't install node_internals.h (Ben Noordhuis) [#6913](https://github.com/nodejs/node/pull/6913)
- [[`5782ec2427`](https://github.com/nodejs/node/commit/5782ec2427)] - **module**: don't cache uninitialized builtins (Anna Henningsen) [#6907](https://github.com/nodejs/node/pull/6907)
- [[`c8e9adb135`](https://github.com/nodejs/node/commit/c8e9adb135)] - **repl**: fix tab completion for defined commands (Prince J Wesley) [#7364](https://github.com/nodejs/node/pull/7364)
- [[`a3fa5db5ca`](https://github.com/nodejs/node/commit/a3fa5db5ca)] - **(SEMVER-MINOR)** **repl**: copying tabs shouldn't trigger completion (Eugene Obrezkov) [#5958](https://github.com/nodejs/node/pull/5958)
- [[`d86332799c`](https://github.com/nodejs/node/commit/d86332799c)] - **src**: clean up string_search (Brian White) [#7174](https://github.com/nodejs/node/pull/7174)
- [[`3eea55167d`](https://github.com/nodejs/node/commit/3eea55167d)] - **src**: fix memory leak in WriteBuffers() error path (Ben Noordhuis) [#7374](https://github.com/nodejs/node/pull/7374)
- [[`23797eb037`](https://github.com/nodejs/node/commit/23797eb037)] - **src**: remove obsolete NOLINT comments (Ben Noordhuis) [#7462](https://github.com/nodejs/node/pull/7462)
- [[`5aff60e832`](https://github.com/nodejs/node/commit/5aff60e832)] - **src**: lint v8abbr.h (Ben Noordhuis) [#7462](https://github.com/nodejs/node/pull/7462)
- [[`42e7c9d266`](https://github.com/nodejs/node/commit/42e7c9d266)] - **src**: lint node_lttng_tp.h (Ben Noordhuis) [#7462](https://github.com/nodejs/node/pull/7462)
- [[`27c2d25be6`](https://github.com/nodejs/node/commit/27c2d25be6)] - **src**: lint node_win32_perfctr_provider.cc (Ben Noordhuis) [#7462](https://github.com/nodejs/node/pull/7462)
- [[`4f4d3e77ef`](https://github.com/nodejs/node/commit/4f4d3e77ef)] - **src**: fix whitespace/indent cpplint warnings (Ben Noordhuis) [#7462](https://github.com/nodejs/node/pull/7462)
- [[`066064d65f`](https://github.com/nodejs/node/commit/066064d65f)] - **src**: fix whitespace/blank_line cpplint warnings (Ben Noordhuis) [#7462](https://github.com/nodejs/node/pull/7462)
- [[`44cbe0356d`](https://github.com/nodejs/node/commit/44cbe0356d)] - **src**: fix runtime/references cpplint warnings (Ben Noordhuis) [#7462](https://github.com/nodejs/node/pull/7462)
- [[`f530a36c65`](https://github.com/nodejs/node/commit/f530a36c65)] - **src**: fix runtime/int cpplint warnings (Ben Noordhuis) [#7462](https://github.com/nodejs/node/pull/7462)
- [[`d6595adcdb`](https://github.com/nodejs/node/commit/d6595adcdb)] - **src**: fix runtime/indentation_namespace warnings (Ben Noordhuis) [#7462](https://github.com/nodejs/node/pull/7462)
- [[`68db091aba`](https://github.com/nodejs/node/commit/68db091aba)] - **src**: fix readability/nolint cpplint warnings (Ben Noordhuis) [#7462](https://github.com/nodejs/node/pull/7462)
- [[`4748bed736`](https://github.com/nodejs/node/commit/4748bed736)] - **src**: fix readability/namespace cpplint warnings (Ben Noordhuis) [#7462](https://github.com/nodejs/node/pull/7462)
- [[`785211702a`](https://github.com/nodejs/node/commit/785211702a)] - **src**: fix readability/inheritance cpplint warnings (Ben Noordhuis) [#7462](https://github.com/nodejs/node/pull/7462)
- [[`c90ae7fb72`](https://github.com/nodejs/node/commit/c90ae7fb72)] - **src**: fix readability/constructors cpplint warnings (Ben Noordhuis) [#7462](https://github.com/nodejs/node/pull/7462)
- [[`16f2497994`](https://github.com/nodejs/node/commit/16f2497994)] - **src**: fix readability/braces cpplint warnings (Ben Noordhuis) [#7462](https://github.com/nodejs/node/pull/7462)
- [[`c8f78a2682`](https://github.com/nodejs/node/commit/c8f78a2682)] - **src**: fix build/header_guard cpplint warnings (Ben Noordhuis) [#7462](https://github.com/nodejs/node/pull/7462)
- [[`ccc701e1d5`](https://github.com/nodejs/node/commit/ccc701e1d5)] - **src**: fix build/c++tr1 cpplint warnings (Ben Noordhuis) [#7462](https://github.com/nodejs/node/pull/7462)
- [[`dda81b44b0`](https://github.com/nodejs/node/commit/dda81b44b0)] - **src**: unify implementations of Utf8Value etc. (Anna Henningsen) [#6357](https://github.com/nodejs/node/pull/6357)
- [[`db2b23f06f`](https://github.com/nodejs/node/commit/db2b23f06f)] - **src**: fix sporadic deadlock in SIGUSR1 handler (Ben Noordhuis) [#5904](https://github.com/nodejs/node/pull/5904)
- [[`53a67ed6d7`](https://github.com/nodejs/node/commit/53a67ed6d7)] - **src**: fix bad logic in uid/gid checks (Ben Noordhuis) [#7374](https://github.com/nodejs/node/pull/7374)
- [[`e6a27a70d8`](https://github.com/nodejs/node/commit/e6a27a70d8)] - **src**: fix use-after-return in zlib bindings (Ben Noordhuis) [#7374](https://github.com/nodejs/node/pull/7374)
- [[`61de6e9b47`](https://github.com/nodejs/node/commit/61de6e9b47)] - **src**: remove deprecated HMAC_Init, use HMAC_Init_ex (Ben Noordhuis) [#7374](https://github.com/nodejs/node/pull/7374)
- [[`7305e7b9d2`](https://github.com/nodejs/node/commit/7305e7b9d2)] - **src**: remove duplicate HMAC_Init calls (Ben Noordhuis) [#7374](https://github.com/nodejs/node/pull/7374)
- [[`38baf6a0b7`](https://github.com/nodejs/node/commit/38baf6a0b7)] - **src**: remove unused md\_ data members (Ben Noordhuis) [#7374](https://github.com/nodejs/node/pull/7374)
- [[`e103044b68`](https://github.com/nodejs/node/commit/e103044b68)] - **src**: remove unused data member write*queue_size* (Ben Noordhuis) [#7374](https://github.com/nodejs/node/pull/7374)
- [[`67937bca0a`](https://github.com/nodejs/node/commit/67937bca0a)] - **src**: guard against starting fs watcher twice (Ben Noordhuis) [#7374](https://github.com/nodejs/node/pull/7374)
- [[`c03bd57ac6`](https://github.com/nodejs/node/commit/c03bd57ac6)] - **src**: check uv_async_init() return value (Ben Noordhuis) [#7374](https://github.com/nodejs/node/pull/7374)
- [[`2b0dce5a5b`](https://github.com/nodejs/node/commit/2b0dce5a5b)] - **src**: don't use locale-sensitive strcasecmp() (Ben Noordhuis) [#6582](https://github.com/nodejs/node/pull/6582)
- [[`9c31c738fc`](https://github.com/nodejs/node/commit/9c31c738fc)] - **src**: remove unused #include statement (Ben Noordhuis) [#6582](https://github.com/nodejs/node/pull/6582)
- [[`426aa0a5e8`](https://github.com/nodejs/node/commit/426aa0a5e8)] - **src**: fix Windows segfault with `--eval` (Bryce Simonds) [#6938](https://github.com/nodejs/node/pull/6938)
- [[`b21d145c2a`](https://github.com/nodejs/node/commit/b21d145c2a)] - **(SEMVER-MINOR)** **src**: add node::FreeEnvironment public API (Cheng Zhao) [#3098](https://github.com/nodejs/node/pull/3098)
- [[`b9136c0c03`](https://github.com/nodejs/node/commit/b9136c0c03)] - **src**: add process.binding('config') (James M Snell) [#6266](https://github.com/nodejs/node/pull/6266)
- [[`c3d87eee49`](https://github.com/nodejs/node/commit/c3d87eee49)] - **src**: reword command and add ternary (Trevor Norris) [#5756](https://github.com/nodejs/node/pull/5756)
- [[`68f391bf3b`](https://github.com/nodejs/node/commit/68f391bf3b)] - **src**: remove unnecessary check (Brian White) [#5233](https://github.com/nodejs/node/pull/5233)
- [[`981bbcd925`](https://github.com/nodejs/node/commit/981bbcd925)] - **src**: remove TryCatch in MakeCallback (Trevor Norris) [#4507](https://github.com/nodejs/node/pull/4507)
- [[`48b7b71352`](https://github.com/nodejs/node/commit/48b7b71352)] - **src**: remove unused TickInfo::in_tick() (Trevor Norris) [#4507](https://github.com/nodejs/node/pull/4507)
- [[`d77b28c6b3`](https://github.com/nodejs/node/commit/d77b28c6b3)] - **src**: remove unused of TickInfo::last_threw() (Trevor Norris) [#4507](https://github.com/nodejs/node/pull/4507)
- [[`cb291d5c7f`](https://github.com/nodejs/node/commit/cb291d5c7f)] - **src**: add AsyncCallbackScope (Trevor Norris) [#4507](https://github.com/nodejs/node/pull/4507)
- [[`2eb097f212`](https://github.com/nodejs/node/commit/2eb097f212)] - **src**: fix MakeCallback error handling (Trevor Norris) [#4507](https://github.com/nodejs/node/pull/4507)
- [[`63356df39c`](https://github.com/nodejs/node/commit/63356df39c)] - **src,http**: fix uncaughtException miss in http (Trevor Norris) [#5591](https://github.com/nodejs/node/pull/5591)
- [[`ee7040568d`](https://github.com/nodejs/node/commit/ee7040568d)] - **src,http_parser**: remove KickNextTick call (Trevor Norris) [#5756](https://github.com/nodejs/node/pull/5756)
- [[`9a8acad6ff`](https://github.com/nodejs/node/commit/9a8acad6ff)] - **test**: use random ports where possible (Brian White) [#7045](https://github.com/nodejs/node/pull/7045)
- [[`223c0e2010`](https://github.com/nodejs/node/commit/223c0e2010)] - **test**: skip doctool tests when js-yaml is missing (Anna Henningsen) [#7218](https://github.com/nodejs/node/pull/7218)
- [[`3681b9b868`](https://github.com/nodejs/node/commit/3681b9b868)] - **test**: refactor doctool tests (Rich Trott) [#6719](https://github.com/nodejs/node/pull/6719)
- [[`686d7b329c`](https://github.com/nodejs/node/commit/686d7b329c)] - **test**: build addons with V8_DEPRECATION_WARNINGS=1 (Ben Noordhuis) [#6652](https://github.com/nodejs/node/pull/6652)
- [[`8404e34665`](https://github.com/nodejs/node/commit/8404e34665)] - **_Revert_** "**test**: mark test-vm-timeout flaky on windows" (Anna Henningsen) [#7373](https://github.com/nodejs/node/pull/7373)
- [[`eab9ced2ee`](https://github.com/nodejs/node/commit/eab9ced2ee)] - **test**: fix flaky test-vm-timeout (Anna Henningsen) [#7373](https://github.com/nodejs/node/pull/7373)
- [[`a31d3161f5`](https://github.com/nodejs/node/commit/a31d3161f5)] - **test**: add test for exec() known issue (Rich Trott) [#7375](https://github.com/nodejs/node/pull/7375)
- [[`1baa145a16`](https://github.com/nodejs/node/commit/1baa145a16)] - **test**: remove internet/test-tls-connnect-cnnic (Ben Noordhuis) [#7363](https://github.com/nodejs/node/pull/7363)
- [[`e3097b7cdf`](https://github.com/nodejs/node/commit/e3097b7cdf)] - **test**: test isFullWidthCodePoint with invalid input (Rich Trott) [#7422](https://github.com/nodejs/node/pull/7422)
- [[`f0b0fc49f9`](https://github.com/nodejs/node/commit/f0b0fc49f9)] - **test**: update weak module for gc tests (Rich Trott) [#7014](https://github.com/nodejs/node/pull/7014)
- [[`1d100f6853`](https://github.com/nodejs/node/commit/1d100f6853)] - **test**: remove unused vars from http/https tests (Rich Trott) [#7598](https://github.com/nodejs/node/pull/7598)
- [[`3241536d95`](https://github.com/nodejs/node/commit/3241536d95)] - **test**: remove unused var in net-server-try-ports (Rich Trott) [#7597](https://github.com/nodejs/node/pull/7597)
- [[`7bd7c235fa`](https://github.com/nodejs/node/commit/7bd7c235fa)] - **test**: remove unused var from stream2 test (Rich Trott) [#7596](https://github.com/nodejs/node/pull/7596)
- [[`4d36a67738`](https://github.com/nodejs/node/commit/4d36a67738)] - **test**: remove unused var from child-process-fork (Rich Trott) [#7599](https://github.com/nodejs/node/pull/7599)
- [[`b5e516a42c`](https://github.com/nodejs/node/commit/b5e516a42c)] - **test**: remove unused var in test-tls-server-verify (Rich Trott) [#7595](https://github.com/nodejs/node/pull/7595)
- [[`db35efa6c1`](https://github.com/nodejs/node/commit/db35efa6c1)] - **test**: fix flaky test-net-write-slow (Rich Trott) [#7555](https://github.com/nodejs/node/pull/7555)
- [[`8273824ca3`](https://github.com/nodejs/node/commit/8273824ca3)] - **test**: remove common.PORT from http tests (Rich Trott) [#7467](https://github.com/nodejs/node/pull/7467)
- [[`5129f3f2cd`](https://github.com/nodejs/node/commit/5129f3f2cd)] - **test**: mark test-vm-timeout flaky on windows (Rich Trott) [#7359](https://github.com/nodejs/node/pull/7359)
- [[`79b45886c1`](https://github.com/nodejs/node/commit/79b45886c1)] - **test**: add tests for some stream.Readable uses (Anna Henningsen) [#7260](https://github.com/nodejs/node/pull/7260)
- [[`65b5cccee9`](https://github.com/nodejs/node/commit/65b5cccee9)] - **test**: fix spawn on windows (Brian White) [#7049](https://github.com/nodejs/node/pull/7049)
- [[`96ed883d2f`](https://github.com/nodejs/node/commit/96ed883d2f)] - **test**: enable test-debug-brk-no-arg (Rich Trott) [#7143](https://github.com/nodejs/node/pull/7143)
- [[`8724c442f3`](https://github.com/nodejs/node/commit/8724c442f3)] - **test**: add test for uid/gid setting in spawn (Rich Trott) [#7084](https://github.com/nodejs/node/pull/7084)
- [[`042e858dfb`](https://github.com/nodejs/node/commit/042e858dfb)] - **test**: make test-child-process-fork-net more robust (Rich Trott) [#7033](https://github.com/nodejs/node/pull/7033)
- [[`2a59e4e73d`](https://github.com/nodejs/node/commit/2a59e4e73d)] - **test**: improve debug-break-on-uncaught reliability (Rich Trott) [#6793](https://github.com/nodejs/node/pull/6793)
- [[`77325d585e`](https://github.com/nodejs/node/commit/77325d585e)] - **test**: remove disabled eio race test (Rich Trott) [#7083](https://github.com/nodejs/node/pull/7083)
- [[`5b1f54678b`](https://github.com/nodejs/node/commit/5b1f54678b)] - **test**: remove non-incremental common.PORT changes (Rich Trott) [#7055](https://github.com/nodejs/node/pull/7055)
- [[`44228dfdef`](https://github.com/nodejs/node/commit/44228dfdef)] - **test**: remove `common.PORT` from gc tests (Rich Trott) [#7013](https://github.com/nodejs/node/pull/7013)
- [[`644bfe14a6`](https://github.com/nodejs/node/commit/644bfe14a6)] - **test**: fix test-debug-port-numbers on OS X (Santiago Gimeno) [#7046](https://github.com/nodejs/node/pull/7046)
- [[`cde3014f78`](https://github.com/nodejs/node/commit/cde3014f78)] - **test**: remove modifcation to common.PORT (Rich Trott) [#6990](https://github.com/nodejs/node/pull/6990)
- [[`8c412af7ac`](https://github.com/nodejs/node/commit/8c412af7ac)] - **test**: verify cluster worker exit (cjihrig) [#6993](https://github.com/nodejs/node/pull/6993)
- [[`7d6acefbcc`](https://github.com/nodejs/node/commit/7d6acefbcc)] - **test**: listen on and connect to 127.0.0.1 (Ben Noordhuis) [#7524](https://github.com/nodejs/node/pull/7524)
- [[`ecf5c1cb25`](https://github.com/nodejs/node/commit/ecf5c1cb25)] - **test**: refactor spawnSync() cwd test (cjihrig) [#6939](https://github.com/nodejs/node/pull/6939)
- [[`9cccaa3c80`](https://github.com/nodejs/node/commit/9cccaa3c80)] - **test**: fix component printing on windows (Ben Noordhuis) [#6915](https://github.com/nodejs/node/pull/6915)
- [[`af4b56d6be`](https://github.com/nodejs/node/commit/af4b56d6be)] - **test**: pass python path to node-gyp (hefangshi) [#6646](https://github.com/nodejs/node/pull/6646)
- [[`7c55f59214`](https://github.com/nodejs/node/commit/7c55f59214)] - **test**: make stdout buffer test more robust (Rich Trott) [#6633](https://github.com/nodejs/node/pull/6633)
- [[`3aef9b813f`](https://github.com/nodejs/node/commit/3aef9b813f)] - **test**: unmark test-http-regr-gh-2928 as flaky (Rich Trott) [#6540](https://github.com/nodejs/node/pull/6540)
- [[`2259e5db69`](https://github.com/nodejs/node/commit/2259e5db69)] - **test**: avoid test-cluster-master-\* flakiness (Stefan Budeanu) [#6531](https://github.com/nodejs/node/pull/6531)
- [[`5f444ed6a3`](https://github.com/nodejs/node/commit/5f444ed6a3)] - **test**: add tests for stream3 buffering using cork (Alex J Burke) [#6493](https://github.com/nodejs/node/pull/6493)
- [[`01b314d165`](https://github.com/nodejs/node/commit/01b314d165)] - **test**: test TTY problems by fakeing a TTY using openpty (Jeremiah Senkpiel) [#6895](https://github.com/nodejs/node/pull/6895)
- [[`55f8689711`](https://github.com/nodejs/node/commit/55f8689711)] - **test**: add test for responses to HTTP CONNECT req (Josh Leder) [#6279](https://github.com/nodejs/node/pull/6279)
- [[`9aec1ddb4f`](https://github.com/nodejs/node/commit/9aec1ddb4f)] - **test**: test cluster worker disconnection on error (Santiago Gimeno) [#6909](https://github.com/nodejs/node/pull/6909)
- [[`c0a42bc040`](https://github.com/nodejs/node/commit/c0a42bc040)] - **test**: verify IPC messages are emitted on next tick (Santiago Gimeno) [#6909](https://github.com/nodejs/node/pull/6909)
- [[`9606f768ea`](https://github.com/nodejs/node/commit/9606f768ea)] - **(SEMVER-MINOR)** **test**: run v8 tests from node tree (Bryon Leung) [#4704](https://github.com/nodejs/node/pull/4704)
- [[`efdeb69c9a`](https://github.com/nodejs/node/commit/efdeb69c9a)] - **test**: work around debugger not killing inferior (Ben Noordhuis) [#7037](https://github.com/nodejs/node/pull/7037)
- [[`e3f9bc893f`](https://github.com/nodejs/node/commit/e3f9bc893f)] - **test**: use strictEqual consistently in agent test (Ben Noordhuis) [#6654](https://github.com/nodejs/node/pull/6654)
- [[`1186b7a401`](https://github.com/nodejs/node/commit/1186b7a401)] - **test**: add addons test for MakeCallback (Trevor Norris) [#4507](https://github.com/nodejs/node/pull/4507)
- [[`8f76d7db03`](https://github.com/nodejs/node/commit/8f76d7db03)] - **test,tools**: test yaml parsing of doctool (Anna Henningsen) [#6495](https://github.com/nodejs/node/pull/6495)
- [[`e544b1c40c`](https://github.com/nodejs/node/commit/e544b1c40c)] - **test,win**: skip addons/load-long-path on WOW64 (Alexis Campailla) [#6675](https://github.com/nodejs/node/pull/6675)
- [[`b956635e41`](https://github.com/nodejs/node/commit/b956635e41)] - **tls**: catch `certCbDone` exceptions (Fedor Indutny) [#6887](https://github.com/nodejs/node/pull/6887)
- [[`06327e5eed`](https://github.com/nodejs/node/commit/06327e5eed)] - **tls**: use process.binding('config') to detect fips mode (James M Snell) [#7551](https://github.com/nodejs/node/pull/7551)
- [[`c807287e80`](https://github.com/nodejs/node/commit/c807287e80)] - **tls,https**: respect address family when connecting (Ben Noordhuis) [#6654](https://github.com/nodejs/node/pull/6654)
- [[`9ef6e23088`](https://github.com/nodejs/node/commit/9ef6e23088)] - **tools**: make sure doctool anchors respect includes (Anna Henningsen) [#6943](https://github.com/nodejs/node/pull/6943)
- [[`f9f85a006f`](https://github.com/nodejs/node/commit/f9f85a006f)] - **tools**: restore change of signatures to opts hashes (Jesse McCarthy) [#6690](https://github.com/nodejs/node/pull/6690)
- [[`607173bbac`](https://github.com/nodejs/node/commit/607173bbac)] - **tools**: fix regression in doctool (Myles Borins) [#6680](https://github.com/nodejs/node/pull/6680)
- [[`ed193ad8ae`](https://github.com/nodejs/node/commit/ed193ad8ae)] - **tools**: fix tools/doc/addon-verify.js regression (Anna Henningsen) [#6652](https://github.com/nodejs/node/pull/6652)
- [[`8b88c384f0`](https://github.com/nodejs/node/commit/8b88c384f0)] - **tools**: lint for object literal spacing (Rich Trott) [#6592](https://github.com/nodejs/node/pull/6592)
- [[`96b5aa8710`](https://github.com/nodejs/node/commit/96b5aa8710)] - **tools**: update marked dependency (Daniel Wang) [#6396](https://github.com/nodejs/node/pull/6396)
- [[`ea137637b7`](https://github.com/nodejs/node/commit/ea137637b7)] - **tools**: allow multiple added: version entries (Anna Henningsen) [#6495](https://github.com/nodejs/node/pull/6495)
- [[`2832a60426`](https://github.com/nodejs/node/commit/2832a60426)] - **tools**: parse documentation metadata (Tristian Flanagan) [#6495](https://github.com/nodejs/node/pull/6495)
- [[`0149cb0577`](https://github.com/nodejs/node/commit/0149cb0577)] - **tools**: add mock-y js-yaml dependency to doctool (Anna Henningsen) [#6495](https://github.com/nodejs/node/pull/6495)
- [[`68e9fd47c6`](https://github.com/nodejs/node/commit/68e9fd47c6)] - **tools**: fix -Wunused-variable warning (Ben Noordhuis) [#7462](https://github.com/nodejs/node/pull/7462)
- [[`4a2bd2d515`](https://github.com/nodejs/node/commit/4a2bd2d515)] - **tools**: allow cpplint to run outside git repo (Ben Noordhuis) [#7462](https://github.com/nodejs/node/pull/7462)
- [[`09e98a4457`](https://github.com/nodejs/node/commit/09e98a4457)] - **tools**: add back --mode=tap to cpplint (Ben Noordhuis) [#7462](https://github.com/nodejs/node/pull/7462)
- [[`e74f199fe2`](https://github.com/nodejs/node/commit/e74f199fe2)] - **tools**: disable unwanted cpplint rules again (Ben Noordhuis) [#7462](https://github.com/nodejs/node/pull/7462)
- [[`391fc80487`](https://github.com/nodejs/node/commit/391fc80487)] - **tools**: update cpplint to r456 (Ben Noordhuis) [#7462](https://github.com/nodejs/node/pull/7462)
- [[`efadf7639f`](https://github.com/nodejs/node/commit/efadf7639f)] - **tools**: update certdata.txt (Ben Noordhuis) [#7363](https://github.com/nodejs/node/pull/7363)
- [[`d7ce99214d`](https://github.com/nodejs/node/commit/d7ce99214d)] - **tools**: update ESLint, fix unused vars bug (Rich Trott) [#7601](https://github.com/nodejs/node/pull/7601)
- [[`242d6c7323`](https://github.com/nodejs/node/commit/242d6c7323)] - **tools**: remove unused variable (Rich Trott) [#7594](https://github.com/nodejs/node/pull/7594)
- [[`7182f5f876`](https://github.com/nodejs/node/commit/7182f5f876)] - **tools**: fix license builder to work with icu-small (Myles Borins) [#7119](https://github.com/nodejs/node/pull/7119)
- [[`140b84dd7d`](https://github.com/nodejs/node/commit/140b84dd7d)] - **tools**: print stderr on bad test.py `vmArch` check (Jeremiah Senkpiel) [#6786](https://github.com/nodejs/node/pull/6786)
- [[`4c423e649c`](https://github.com/nodejs/node/commit/4c423e649c)] - **tools**: explicit path for V8 test tap output (Myles Borins) [#7460](https://github.com/nodejs/node/pull/7460)
- [[`d50f16969d`](https://github.com/nodejs/node/commit/d50f16969d)] - **tools,doc**: add example usage for REPLACEME tag (Anna Henningsen) [#6864](https://github.com/nodejs/node/pull/6864)
- [[`b07c3a6ea6`](https://github.com/nodejs/node/commit/b07c3a6ea6)] - **tty**: use blocking mode on OS X (Jeremiah Senkpiel) [#6895](https://github.com/nodejs/node/pull/6895)
- [[`a1719a94e9`](https://github.com/nodejs/node/commit/a1719a94e9)] - **udp**: use libuv API to get file descriptor (Saúl Ibarra Corretgé) [#6908](https://github.com/nodejs/node/pull/6908)
- [[`7779639a11`](https://github.com/nodejs/node/commit/7779639a11)] - **unix,stream**: fix getting the correct fd for a handle (Saúl Ibarra Corretgé) [#6753](https://github.com/nodejs/node/pull/6753)
- [[`d0bf09d3ad`](https://github.com/nodejs/node/commit/d0bf09d3ad)] - **util**: improve format() performance further (Brian White) [#5360](https://github.com/nodejs/node/pull/5360)
- [[`72fb281961`](https://github.com/nodejs/node/commit/72fb281961)] - **util**: improve util.format performance (Evan Lucas) [#5360](https://github.com/nodejs/node/pull/5360)
- [[`855759757a`](https://github.com/nodejs/node/commit/855759757a)] - **vm**: don't print out arrow message for custom error (Anna Henningsen) [#7398](https://github.com/nodejs/node/pull/7398)
- [[`b9dfdfe1d3`](https://github.com/nodejs/node/commit/b9dfdfe1d3)] - **vm**: don't abort process when stack space runs out (Anna Henningsen) [#6907](https://github.com/nodejs/node/pull/6907)
- [[`0bfedd13a9`](https://github.com/nodejs/node/commit/0bfedd13a9)] - **win,build**: add creation of zip and 7z package (Bartosz Sosnowski) [#5995](https://github.com/nodejs/node/pull/5995)
- [[`7d66752f1f`](https://github.com/nodejs/node/commit/7d66752f1f)] - **zlib**: release callback and buffer after processing (Matt Lavin) [#6955](https://github.com/nodejs/node/pull/6955)

Windows 32-bit Installer: https://nodejs.org/dist/v4.5.0/node-v4.5.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v4.5.0/node-v4.5.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v4.5.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v4.5.0/win-x64/node.exe \
Mac OS X 64-bit Installer: https://nodejs.org/dist/v4.5.0/node-v4.5.0.pkg \
Mac OS X 64-bit Binary: https://nodejs.org/dist/v4.5.0/node-v4.5.0-darwin-x64.tar.gz \
Linux 32-bit Binary: https://nodejs.org/dist/v4.5.0/node-v4.5.0-linux-x86.tar.xz \
Linux 64-bit Binary: https://nodejs.org/dist/v4.5.0/node-v4.5.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v4.5.0/node-v4.5.0-linux-ppc64le.tar.xz \
SmartOS 32-bit Binary: https://nodejs.org/dist/v4.5.0/node-v4.5.0-sunos-x86.tar.xz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v4.5.0/node-v4.5.0-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v4.5.0/node-v4.5.0-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v4.5.0/node-v4.5.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v4.5.0/node-v4.5.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v4.5.0/node-v4.5.0.tar.gz \
Other release files: https://nodejs.org/dist/v4.5.0/ \
Documentation: https://nodejs.org/docs/v4.5.0/api/

Shasums (GPG signing hash: SHA512, file hash: SHA256):

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA1

d171f0c859e3895b2430c317001b817866c4de45211ad540c59658ee6a2f689f  node-v4.5.0-darwin-x64.tar.gz
61b570d0744bed2725a4d64ab28f641acb1206fee8760035df36f8ee63e54da3  node-v4.5.0-darwin-x64.tar.xz
12ee966eef2abc928f6d7fcf9cfcf2913ef0e59ae07e2dcc20726246ab174fd8  node-v4.5.0-headers.tar.gz
7a83d8e11089c16824dfcd82ca3f5cb9f727545a39f7138d034b67ed95fb922c  node-v4.5.0-headers.tar.xz
ecdbb3cb55d0a87aeb10334b47310f1823393abe6273f1ce7c97bcb509051e68  node-v4.5.0-linux-arm64.tar.gz
9034e67629e1912d6dc0632627528565d9e412c6eba410d171f9fd80f700f84f  node-v4.5.0-linux-arm64.tar.xz
87c6fb8269475d385a9b923c647890def45079f8e30b55c4737d70558fd36418  node-v4.5.0-linux-armv6l.tar.gz
bc3e33872b009e18f084efb9a595cc12cf8314df125ce80c2e8bde1c2213b5f1  node-v4.5.0-linux-armv6l.tar.xz
99d222ede2f38041c55a64da8e6a669e0128cfb40d40f968cae74d80e52ad566  node-v4.5.0-linux-armv7l.tar.gz
c01482d423b646542ba037cb5ce646cb26518aa2dc9f7728520e7ac1247932b3  node-v4.5.0-linux-armv7l.tar.xz
a517a56b5550567225c6820f99e44fc84ac9953e2fe9db67f3bab7a8d7661e9f  node-v4.5.0-linux-ppc64le.tar.gz
e01cb29c51f05dd56e15e0a25918fe4a2ad10cf4b83c4eee89fb0a3598728e50  node-v4.5.0-linux-ppc64le.tar.xz
46bebb806e4e8273e66e02faf5f25145f5351958bda75d9aa49bee9df7ccafec  node-v4.5.0-linux-ppc64.tar.gz
f910ffa6ff0dbe32d2e3dea7e73b4f83c37f8a7a0d4078d53f3d3b426e30a8cf  node-v4.5.0-linux-ppc64.tar.xz
5678ad94ee35e40fc3a2c545e136a0dc946ac4c039fca5898e1ea51ecf9e7c39  node-v4.5.0-linux-x64.tar.gz
c6ee1f4303353e3605ff70de180431417eb594fe08daf612e692216236750c55  node-v4.5.0-linux-x64.tar.xz
a11ef26fa700eff8ea7acfa491fd362dd362590da2967335d87932e7036a06fd  node-v4.5.0-linux-x86.tar.gz
0f5e51d913867eaf71332cd20fd9a98aeb3576b8a22a52e8e1cd97c89cf51feb  node-v4.5.0-linux-x86.tar.xz
370b65e6cd27943afc18e517f86c7fc801c0d256cb40ca75b2ff01051223cbb6  node-v4.5.0.pkg
47f0f3c9e33d02a49a6c467d8a12539b0bb3c5bf8e1916bd938eaa956afb47bb  node-v4.5.0-sunos-x64.tar.gz
0b83935c6e6decf11c347c1dcaeb1face825a6a103a04117c868239d226de492  node-v4.5.0-sunos-x64.tar.xz
190ff116905ce94291a7122779cbdf493af1ba415432a9a12a32263816b90ccd  node-v4.5.0-sunos-x86.tar.gz
2d49973241a67c1a2da747ad469ba43299a84e8e9f7e2d147b08d22084d73c15  node-v4.5.0-sunos-x86.tar.xz
74ced83b8d890d90e2a8b0d54b0d0e9b5e01d6fd6148cec6e9911ff6eaf0cf21  node-v4.5.0.tar.gz
97b99d378c56802444208409568e2e66c46332897f06aead74d1ffbe733bd488  node-v4.5.0.tar.xz
e80a7bf9afb91a422b2955c95a6dd35fec3a7019e5adc21763ed18c9fa6c67a6  node-v4.5.0-win-x64.7z
16aab15b29e79746d1bae708f6a5dbed8ef3c87426a9408f7261163d0cda0f56  node-v4.5.0-win-x64.zip
9d68e8f4c8650b60dfc0955185d867302a9e63b0bd4ee39b8296fc473b033879  node-v4.5.0-win-x86.7z
f79c11ecc0eb6ecb4e95e27744753eaa6db8c255b6c808bc660722c72d45a892  node-v4.5.0-win-x86.zip
367a1872bac36bfd398b89027df80c9c0e604a466185ad4631c167ba1a5d24c8  node-v4.5.0-x64.msi
489e84902243b1adeb7218322bc0e87091c5e758c669e088ac20b4267a8d165d  node-v4.5.0-x86.msi
ff1d2bbbc55b15fa3d23dc3d359224674b8d1f20b7f387c1e814099b9801288d  win-x64/node.exe
5e54f544fd8ea5cc0a76ccc8b3942e4783de7a174f7cdcd04664a990905395cb  win-x64/node.lib
b3c93cfa8155fc58ce86cdd64d28b2254d00b1d46bd3b69cdd965ffa0afa4493  win-x64/node_pdb.7z
973a47686ee5e3c30e986bcc5df5cecdd643d492ef689d33d801e35e912cd869  win-x64/node_pdb.zip
aa0458966999005a5df9d9d74a415022f92ba02ce641bd582cd46e93cd4b8a90  win-x86/node.exe
7e2ff7208dbe2612b55cc5f11be7e17c00b58b410580b3705cb86e7cba09cc97  win-x86/node.lib
a4e089ae072cf15ef247d16a4b7fb8177855e41bc595f395640dbb68d19a6738  win-x86/node_pdb.7z
355ea7df33f95a5d10377276cc7bf4e89b80457f16923b22fb6dad66ea77d3f5  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQEcBAEBAgAGBQJXszriAAoJEJM7AfQLXKlGQuEH/Rvw85bg3uDqCTgXxWaxlwLb
Ua/j3UOEJpqG+bdUn3Zvm0RvhyTWDyJkelb5NOdZhbzHd9/F4x8pPO1ZJa3uF7BF
vgBOxcpLIvL4UgpIelZXs/oLBfsD6RVHPIBNCGgeno0ryyEZp7tSrYbVmDO1zr8W
FqMNRN+E07fFbK+bTc9lMQVyclbdb2QHr4s4GmKrMg2i0GRWOndHfr+mQX3pcK9a
dP6dOKguLPAEOThFiPzks/dbMs++Zq/iWUAPXYzRkXZAvF7vVGOBYeAT7z49rlWT
J1qjH0RPlNzfLIHkTyhha9D6EV/4ZuPtDL+1wsXeCkHHWVa631IxhV1OMdFIivU=
=6paR
-----END PGP SIGNATURE-----

```
