---
date: '2021-02-23T13:01:31.647Z'
category: release
title: Node v15.10.0 (Current)
layout: blog-post
author: <PERSON>
---

### Notable changes

Vulnerabilities fixed:

- **CVE-2021-22883**: HTTP2 'unknownProtocol' cause Denial of Service by resource exhaustion
  - Affected Node.js versions are vulnerable to denial of service attacks when too many connection attempts with an 'unknownProtocol' are established. This leads to a leak of file descriptors. If a file descriptor limit is configured on the system, then the server is unable to accept new connections and prevent the process also from opening, e.g. a file. If no file descriptor limit is configured, then this lead to an excessive memory usage and cause the system to run out of memory.
- **CVE-2021-22884**: DNS rebinding in --inspect
  - Affected Node.js versions are vulnerable to denial of service attacks when the whitelist includes “localhost6”. When “localhost6” is not present in /etc/hosts, it is just an ordinary domain that is resolved via DNS, i.e., over network. If the attacker controls the victim's DNS server or can spoof its responses, the DNS rebinding protection can be bypassed by using the “localhost6” domain. As long as the attacker uses the “localhost6” domain, they can still apply the attack described in CVE-2018-7160.
- **CVE-2021-23840**: OpenSSL - Integer overflow in CipherUpdate
  - This is a vulnerability in OpenSSL which may be exploited through Node.js. You can read more about it in
    https://www.openssl.org/news/secadv/20210216.txt

### Commits

- [[`2a3ce5974b`](https://github.com/nodejs/node/commit/2a3ce5974b)] - **deps**: update archs files for OpenSSL-1.1.1j (Daniel Bevenius) [#37412](https://github.com/nodejs/node/pull/37412)
- [[`afbce66874`](https://github.com/nodejs/node/commit/afbce66874)] - **deps**: upgrade openssl sources to 1.1.1j (Daniel Bevenius) [#37412](https://github.com/nodejs/node/pull/37412)
- [[`4184806dee`](https://github.com/nodejs/node/commit/4184806dee)] - **(SEMVER-MINOR)** **http2**: add unknownProtocol timeout (Daniel Bevenius) [nodejs-private/node-private#246](https://github.com/nodejs-private/node-private/pull/246)
- [[`43ae9c46c3`](https://github.com/nodejs/node/commit/43ae9c46c3)] - **src**: drop localhost6 as allowed host for inspector (Matteo Collina) [nodejs-private/node-private#244](https://github.com/nodejs-private/node-private/pull/244)

Windows 32-bit Installer: https://nodejs.org/dist/v15.10.0/node-v15.10.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v15.10.0/node-v15.10.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v15.10.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v15.10.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v15.10.0/node-v15.10.0.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v15.10.0/node-v15.10.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v15.10.0/node-v15.10.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v15.10.0/node-v15.10.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v15.10.0/node-v15.10.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v15.10.0/node-v15.10.0-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v15.10.0/node-v15.10.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v15.10.0/node-v15.10.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v15.10.0/node-v15.10.0.tar.gz \
Other release files: https://nodejs.org/dist/v15.10.0/ \
Documentation: https://nodejs.org/docs/v15.10.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

351ef617dea3ec93819ebb92435b82febaa1de393bdb442dcc129ac76a03014c  node-v15.10.0-aix-ppc64.tar.gz
45ccf8dc5ac539a4f965313593510970a992e5f5dcf8cfacaebec0f99fd546be  node-v15.10.0-darwin-x64.tar.gz
60678b3995712ed96928c9bcce86d6847035d7853417b9862e8b2f7bd888aa7e  node-v15.10.0-darwin-x64.tar.xz
5ee67c683f7d5d060829aece2ec04609de9d500f5b6f56f584c0f1707650c9da  node-v15.10.0-headers.tar.gz
b8cb4c1f8cdfb19c3ecffa01a1a23754ae2075a19dfac3c33ababe9f3e5c9ad0  node-v15.10.0-headers.tar.xz
a9f8c807ec31ed24fe6e0b2d002ce2d1b9f20b7e23a0efe10679331a44b30dba  node-v15.10.0-linux-arm64.tar.gz
d3af39fb8d7bfc22c711aed4120a3fc4419cd70b3d2016b18c6a709fa0f0e845  node-v15.10.0-linux-arm64.tar.xz
ca27a48dd9233b00c49b37a07751395657d4e454ebfa5066a6371e5f6c9969cf  node-v15.10.0-linux-armv7l.tar.gz
200ca8de7f7d575dcf9221a40fe8754be40759b97f254d55b1f4679f368d5eee  node-v15.10.0-linux-armv7l.tar.xz
49977dac42124295bedff31b8409f2990b586aa673f439ed5e9636ac1485a08d  node-v15.10.0-linux-ppc64le.tar.gz
7a54dc554686d7fe7c45db57068a03c69dfd64afd7994873b77c1c7bc978db8a  node-v15.10.0-linux-ppc64le.tar.xz
078a8c2207f92040e0decdcfde0cd368973208876f01ff7d03de6bd0d5f09090  node-v15.10.0-linux-s390x.tar.gz
c2c8375d491e9f7c39e297587bcf99fe6be70ae38aaa9c1757405a5818fe5c73  node-v15.10.0-linux-s390x.tar.xz
31554d9b2de47948a364a007031c635d3943c303e50703b5f4c41a5eead07737  node-v15.10.0-linux-x64.tar.gz
a56793179965235e7c4f911f2cec08dd3f31dc1e09d68eddbb397107042a43fc  node-v15.10.0-linux-x64.tar.xz
aa188140cdc1f96a719bcc88904a37fdcd0745ed8344de9866a4e6869cc45466  node-v15.10.0.pkg
643f9bbd6ae4c424224c946cad52d11b9379aaab927673b2e3c6c4df9a1a7b0e  node-v15.10.0.tar.gz
7748d8f15eabb5543f26fec8ca08e755044512fab1f6bd98d5ba403f276deec4  node-v15.10.0.tar.xz
1e346ced805d316f9d288a2165e9273ed3e75d18bf02b55cda890af25e90919b  node-v15.10.0-win-x64.7z
2bf72da4e5ddc485599f2eca54ab7c59001d70baec7ff2bb050d9f4ed1b066a6  node-v15.10.0-win-x64.zip
3509ce1b7d26116e955c39eae2f60e2132b0b7db423283cac19ef643d682efc6  node-v15.10.0-win-x86.7z
aba943732f371eda2341a62de84c459b376b755917ea92ae587afcc4bfbbaf5d  node-v15.10.0-win-x86.zip
999ca54fb0b329dca36eb01975d6f4a2b3120707d078c594019e88cdda8f0a85  node-v15.10.0-x64.msi
ed40690ceee7339554f0d592453f864235e1e241a85f0e53d7e39a2c73554ab3  node-v15.10.0-x86.msi
a0db7bf0565841e88b85979efb9a640a702dd8ded3b5915342635b190b1413ca  win-x64/node.exe
0692711edaf626282aaeff0a28cce749488d03a5d1db6490c7bb19866ec4aedd  win-x64/node.lib
456373f6ed6145be5b01ce588590ec927c7c05f025704082a4986dc2eb4b25a6  win-x64/node_pdb.7z
b6de50aebb75696fca501a5bbf1914c7a01ebd929d2dc49707621410b8017276  win-x64/node_pdb.zip
5c0095f58e0d4e0bdb0598b5db5a5c4c31ba1cf090994fbd415ab4dc273506f6  win-x86/node.exe
6cec3adfe0cbf4f894d89fafe857fbda9dba14b61586d6abf145b3dd7775dad3  win-x86/node.lib
1ff43f5c0c77951cdfe21bb49973e800c8fdee6ea7565d6054bd2775a024ec47  win-x86/node_pdb.7z
59bb4f60277962be395513fa597126a44ce7264674a94ada6d32c0d2b6477f22  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQEzBAEBCAAdFiEETtd49TnjY0x3nIfG1wYoSKGrAFwFAmA0/JQACgkQ1wYoSKGr
AFzJvggAn5bMlAIhNEZUTZHEX9KiBMiSozCY+E+sZHFCuUwW5wCP2C1eZ6ImNcTi
QAjQEtHk2Pa2weHY2+TI0PZ+BNvXCYcgdTI5jsuhNEoIAvcNRKjdJzEWSwbMZfPv
LHbaGl4SyvNlAweDDsE7lskI1DpyS3p6h/X2+NmUZjcqceMVUPD2/udymIZHwW3P
/8hgntl3N19Qu18VXm1cUDR5VlCuDFrcC1Y/1Lt9Ju++08VV6zWxKz+asrUleQ3S
sPPHOmLVlJlh8tRCY0gGaGzbwhTT0/6fODPlBZzvGQUxUJaSXsQDnY8fcU6D84CX
btojDN2Lobjy1SINcPNh8/Pav0h8AQ==
=22VP
-----END PGP SIGNATURE-----

```
