---
date: '2012-07-25T17:21:16.176Z'
category: release
title: Version 0.8.4 (Stable)
layout: blog-post
author: The Node.js Project
---

2012.07.25, Version 0.8.4 (Stable)

- V8: Upgrade to 3.11.10.17

- npm: Upgrade to 1.1.45

- net: fix Socket({ fd: 42 }) api (<PERSON>)

- readline: Remove event listeners on close (isaacs)

- windows: correctly prep long path for fs.exists(Sync) (<PERSON>)

- debugger: wake up the event loop when a debugger command is dispatched (<PERSON>)

- tls: verify server's identity (Fedor Indutny)

- net: ignore socket.setTimeout(Infinity or NaN) (Fedor Indutny)

Source Code: https://nodejs.org/dist/v0.8.4/node-v0.8.4.tar.gz

Macintosh Installer (Universal): https://nodejs.org/dist/v0.8.4/node-v0.8.4.pkg

Windows Installer: https://nodejs.org/dist/v0.8.4/node-v0.8.4-x86.msi

Windows x64 Installer: https://nodejs.org/dist/v0.8.4/x64/node-v0.8.4-x64.msi

Windows x64 Files: https://nodejs.org/dist/v0.8.4/x64/

Other release files: https://nodejs.org/dist/v0.8.4/

Website: https://nodejs.org/docs/v0.8.4/

Documentation: https://nodejs.org/docs/v0.8.4/api/

Shasums:

```
387d7ac58d79dac301960510dfb7ee96fbf39072  node-v0.8.4-x86.msi
66687da0ef190e8c07d5499ae3c408a6cca7ac30  node-v0.8.4.pkg
8473e3dd48ab3734e2c92de0518cb80c2cdf750f  node-v0.8.4.tar.gz
724ec6dc5e86805bfac79c8a702638c353850ea3  node.exe
9bf483f72ad7884988d0678f7dc5bc000f4d21d2  node.exp
3b7bd3915b62896e5c2f5701fd5d1c7fb53cdd27  node.lib
9c3079ee727d471cef9fd91c7067d0a72349bfcb  node.pdb
236a693074f2c69157e2918d1342a7850fc939ca  npm-1.1.45.tgz
7126700cb321f688901e7773785eb88274906d50  npm-1.1.45.zip
e9f414ba043f1daa5eee65506ef33c5377d87a09  x64/node-v0.8.4-x64.msi
4cc86f21285b7f3d3cbd594ba604f06fb3434502  x64/node.exe
6bf207a6b587bf1b01f18e9d0f2eefb528d82c2f  x64/node.exp
e7b0d65992324b0688c61f6e534f3527e7e0b59f  x64/node.lib
9a0072f528f9055531dee2bfa991882836b235de  x64/node.pdb
```
