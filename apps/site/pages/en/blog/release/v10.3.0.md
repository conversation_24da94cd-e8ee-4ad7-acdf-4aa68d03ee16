---
date: '2018-05-29T17:52:33.571Z'
category: release
title: Node v10.3.0 (Current)
layout: blog-post
author: <PERSON><PERSON>
---

### Notable Changes

- **deps**:
  - upgrade npm to 6.1.0 (<PERSON>) [#20190](https://github.com/nodejs/node/pull/20190)
- **fs**:
  - fix reads with pos \> 4GB (<PERSON>) [#21003](https://github.com/nodejs/node/pull/21003)
- **net**:
  - new option to allow IPC servers to be readable and writable by all users (<PERSON><PERSON><PERSON>) [#19472](https://github.com/nodejs/node/pull/19472)
- **stream**:
  - fix removeAllListeners() for Stream.Readable to work as expected when no arguments are passed (<PERSON><PERSON>) [#20924](https://github.com/nodejs/node/pull/20924)
- **Added new collaborators**
  - [jdlaton](https://github.com/jdalton) John-<PERSON>

### Commits

- [[`ea702e2812`](https://github.com/nodejs/node/commit/ea702e2812)] - **assert**: handle undefined filename in getErrMessage (Tim Seckinger) [#20848](https://github.com/nodejs/node/pull/20848)
- [[`d7fed22511`](https://github.com/nodejs/node/commit/d7fed22511)] - **build,win**: disable DLL-interface warnings (Bert Belder) [#20958](https://github.com/nodejs/node/pull/20958)
- [[`efc7f91354`](https://github.com/nodejs/node/commit/efc7f91354)] - **deps**: cherry-pick 6989b3f6d7 from V8 upstream (Timothy Gu) [#20826](https://github.com/nodejs/node/pull/20826)
- [[`d0cdcb61fe`](https://github.com/nodejs/node/commit/d0cdcb61fe)] - **(SEMVER-MINOR)** **deps**: upgrade npm to 6.1.0 (Rebecca Turner) [#20190](https://github.com/nodejs/node/pull/20190)
- [[`ce13797dca`](https://github.com/nodejs/node/commit/ce13797dca)] - **doc**: fix doc for napi_get_typedarray_info (Michael Dawson) [#20747](https://github.com/nodejs/node/pull/20747)
- [[`babc9da6f9`](https://github.com/nodejs/node/commit/babc9da6f9)] - **doc**: add jdalton to collaborators (John-David Dalton) [#20968](https://github.com/nodejs/node/pull/20968)
- [[`f0704f2407`](https://github.com/nodejs/node/commit/f0704f2407)] - **doc**: mark Node 4 as EOL in changelog (Teddy Katz) [#20926](https://github.com/nodejs/node/pull/20926)
- [[`87ad9318bb`](https://github.com/nodejs/node/commit/87ad9318bb)] - **doc**: update the notable changes (Ruben Bridgewater) [#20316](https://github.com/nodejs/node/pull/20316)
- [[`c036cda1f5`](https://github.com/nodejs/node/commit/c036cda1f5)] - **doc**: fix outdated link FSEvents (amitbend) [#20949](https://github.com/nodejs/node/pull/20949)
- [[`1f3eb1cc1e`](https://github.com/nodejs/node/commit/1f3eb1cc1e)] - **doc**: fix filehandle.truncate() sample codes (Masashi Hirano) [#20913](https://github.com/nodejs/node/pull/20913)
- [[`819bba6d2b`](https://github.com/nodejs/node/commit/819bba6d2b)] - **doc**: removed LTS label from v4 in doc version picker (Chris Young) [#20904](https://github.com/nodejs/node/pull/20904)
- [[`be2a467395`](https://github.com/nodejs/node/commit/be2a467395)] - **doc**: fix incorrect fs.readFileSync example output (Teddy Katz) [#20902](https://github.com/nodejs/node/pull/20902)
- [[`bfe6dc369d`](https://github.com/nodejs/node/commit/bfe6dc369d)] - **fs**: fix reads with pos \> 4GB (Mathias Buus) [#21003](https://github.com/nodejs/node/pull/21003)
- [[`c2c3b6f434`](https://github.com/nodejs/node/commit/c2c3b6f434)] - **lib**: use object destructuring for ContextifyScript (Daniel Bevenius) [#20934](https://github.com/nodejs/node/pull/20934)
- [[`d2bcd55fb5`](https://github.com/nodejs/node/commit/d2bcd55fb5)] - **lib**: remove unnecessary string interpolation (Daniel Bevenius) [#20890](https://github.com/nodejs/node/pull/20890)
- [[`099c6b6c5d`](https://github.com/nodejs/node/commit/099c6b6c5d)] - **meta**: add link to unofficial discord (Gus Caplan) [#20508](https://github.com/nodejs/node/pull/20508)
- [[`45adec2616`](https://github.com/nodejs/node/commit/45adec2616)] - **module**: name anonymous function for debugging (Nicholas Dangles) [#20811](https://github.com/nodejs/node/pull/20811)
- [[`ba30d149ea`](https://github.com/nodejs/node/commit/ba30d149ea)] - **n-api**: throw when entry point is null (Gabriel Schulhof) [#20779](https://github.com/nodejs/node/pull/20779)
- [[`b242248188`](https://github.com/nodejs/node/commit/b242248188)] - **(SEMVER-MINOR)** **net**: allow IPC servers be accessible by all (Bartosz Sosnowski) [#19472](https://github.com/nodejs/node/pull/19472)
- [[`ed9e964357`](https://github.com/nodejs/node/commit/ed9e964357)] - **net**: remove unnecessary variables (chainhelen) [#20864](https://github.com/nodejs/node/pull/20864)
- [[`5f9c01b646`](https://github.com/nodejs/node/commit/5f9c01b646)] - **_Revert_** "**repl**: add friendly tips about how to exit repl" (cjihrig) [#20972](https://github.com/nodejs/node/pull/20972)
- [[`902120a927`](https://github.com/nodejs/node/commit/902120a927)] - **src**: add CHECK_NULL/CHECK_NOT_NULL macros (Tobias Nießen) [#20914](https://github.com/nodejs/node/pull/20914)
- [[`5e69e1a51e`](https://github.com/nodejs/node/commit/5e69e1a51e)] - **src**: add CHECK_IMPLIES macro (Tobias Nießen) [#20914](https://github.com/nodejs/node/pull/20914)
- [[`418739c021`](https://github.com/nodejs/node/commit/418739c021)] - **src**: fix MallocedBuffer move assignment operator (Anna Henningsen) [#20883](https://github.com/nodejs/node/pull/20883)
- [[`b4519cac20`](https://github.com/nodejs/node/commit/b4519cac20)] - **src**: move DeleteFnPtr into util.h (Anna Henningsen) [#20885](https://github.com/nodejs/node/pull/20885)
- [[`b0023d7bc9`](https://github.com/nodejs/node/commit/b0023d7bc9)] - **src,doc**: add doc of --prof flag to help command (ohbarye) [#20845](https://github.com/nodejs/node/pull/20845)
- [[`8f52c3fb6b`](https://github.com/nodejs/node/commit/8f52c3fb6b)] - **stream**: fix removeAllListeners() for Stream.Readable (Kael Zhang) [#20924](https://github.com/nodejs/node/pull/20924)
- [[`011235768c`](https://github.com/nodejs/node/commit/011235768c)] - **test**: improve assert test hygiene (Rich Trott) [#20861](https://github.com/nodejs/node/pull/20861)
- [[`88f9a399d6`](https://github.com/nodejs/node/commit/88f9a399d6)] - **test**: isolate unusual assert test in its own file (Rich Trott) [#20861](https://github.com/nodejs/node/pull/20861)
- [[`460a5025d0`](https://github.com/nodejs/node/commit/460a5025d0)] - **test**: fix test failure on aix (Ruben Bridgewater) [#20940](https://github.com/nodejs/node/pull/20940)
- [[`d09bec8a04`](https://github.com/nodejs/node/commit/d09bec8a04)] - **test**: improve error message in async-wrap test (Rich Trott) [#20948](https://github.com/nodejs/node/pull/20948)
- [[`460add98fb`](https://github.com/nodejs/node/commit/460add98fb)] - **test**: reduce runtime (Ruben Bridgewater) [#20688](https://github.com/nodejs/node/pull/20688)
- [[`82afb4cf7d`](https://github.com/nodejs/node/commit/82afb4cf7d)] - **test**: remove message argument from strictEqual() (sagirk) [#20912](https://github.com/nodejs/node/pull/20912)
- [[`40e57885d4`](https://github.com/nodejs/node/commit/40e57885d4)] - **test**: remove string literal from strictEqual (AbhimanyuVashisht) [#20920](https://github.com/nodejs/node/pull/20920)
- [[`9bbab91479`](https://github.com/nodejs/node/commit/9bbab91479)] - **test**: include port in assertion message (nam) [#20889](https://github.com/nodejs/node/pull/20889)
- [[`554ad478d4`](https://github.com/nodejs/node/commit/554ad478d4)] - **test**: improve coverage for readline.Interface (Masashi Hirano) [#20704](https://github.com/nodejs/node/pull/20704)
- [[`443d60afcc`](https://github.com/nodejs/node/commit/443d60afcc)] - **test**: use log only in test-child-process-fork-net (Rich Trott) [#20873](https://github.com/nodejs/node/pull/20873)
- [[`ed84b7d42f`](https://github.com/nodejs/node/commit/ed84b7d42f)] - **test**: changed assert message from string literal to template literal (CoreyGMartin) [#20870](https://github.com/nodejs/node/pull/20870)
- [[`b62cbe106c`](https://github.com/nodejs/node/commit/b62cbe106c)] - **tools**: update tools/doc/package-lock.json (Rich Trott) [#20970](https://github.com/nodejs/node/pull/20970)
- [[`46e7cec7a5`](https://github.com/nodejs/node/commit/46e7cec7a5)] - **tools**: fix sorting in doc/type-parser.js (Vse Mozhet Byt) [#20976](https://github.com/nodejs/node/pull/20976)

Windows 32-bit Installer: https://nodejs.org/dist/v10.3.0/node-v10.3.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v10.3.0/node-v10.3.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v10.3.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v10.3.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v10.3.0/node-v10.3.0.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v10.3.0/node-v10.3.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v10.3.0/node-v10.3.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v10.3.0/node-v10.3.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v10.3.0/node-v10.3.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v10.3.0/node-v10.3.0-aix-ppc64.tar.gz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v10.3.0/node-v10.3.0-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v10.3.0/node-v10.3.0-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v10.3.0/node-v10.3.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v10.3.0/node-v10.3.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v10.3.0/node-v10.3.0.tar.gz \
Other release files: https://nodejs.org/dist/v10.3.0/ \
Documentation: https://nodejs.org/docs/v10.3.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

53f5d2b14f4fe2f66a4c4063f7870af978a81559e7d0b608a9b483673e6d0b56  node-v10.3.0-aix-ppc64.tar.gz
0bb5b7e3fe8cccda2abda958d1eb0408f1518a8b0cb58b75ade5d507cd5d6053  node-v10.3.0-darwin-x64.tar.gz
efb546c0621e8baf1e430d63116c121b967d1dc92878af822c84a005a7eaca3d  node-v10.3.0-darwin-x64.tar.xz
2d16f5655335d7f3d1b8400aeb846168f7c2edd7c2a840de7bbcac5ce36a05e1  node-v10.3.0-headers.tar.gz
854c85bae992d17595eda6c79aaa9dcfc1978dd37aa66990069764db08065dfd  node-v10.3.0-headers.tar.xz
a4e8be9d186e6f0506088bf5121c1d0fb72b5d9eb5add6a75b466c140d6eb476  node-v10.3.0-linux-arm64.tar.gz
6811b7b9807135902990508143605c0c758d07f7726092ed1b2e27cc60111bd0  node-v10.3.0-linux-arm64.tar.xz
333156438a324cafd9f8475fa5dc8b052473756369af8e69b0a2b006573a1837  node-v10.3.0-linux-armv6l.tar.gz
00f42514b4b57013e10a4f50c37092c0889c812a89608eb3081b1bf071a6cc6d  node-v10.3.0-linux-armv6l.tar.xz
91bcb47837d3bfff5cb686f2273131f1718e92dce481d4741996a7d575b1cf83  node-v10.3.0-linux-armv7l.tar.gz
fe7a2774e55bbc41391465079b3ec41a2a25f4731d0b441b4a5bb3950774ebc7  node-v10.3.0-linux-armv7l.tar.xz
3aba69b71e35fcd359fe1e1824d125a50625f07f633024cff927e7c26798eb37  node-v10.3.0-linux-ppc64le.tar.gz
00fe6998e50acaee3da5f8198ec3db655dfa59f4733eae60d7ced61f89e92734  node-v10.3.0-linux-ppc64le.tar.xz
aac61178e5cc04ca9804f61c1119f39fa6df1dbd6e9e8455feee978a5d0ad338  node-v10.3.0-linux-s390x.tar.gz
21dddf20b7348d291e0569b8942de1dd1b879c33ae8ca93a9c8906c8bfb78f01  node-v10.3.0-linux-s390x.tar.xz
b9565d47f5cb95c9d01133b4266a3717f0ee7d3ccaff6d53275462eab40413f2  node-v10.3.0-linux-x64.tar.gz
eb3c3e2585494699716ad3197c8eedf4003d3f110829b30c5a0dc34414c47423  node-v10.3.0-linux-x64.tar.xz
efe481eb371bb42d4b1100d18e3a6d9b16b8e2e188e61204a2e437991d294d0e  node-v10.3.0.pkg
187945f9aaf59cc4fd97812bf2d07d58616963d54cdabdfd7499458a5a402072  node-v10.3.0-sunos-x64.tar.gz
42463333e634126433baf6617ba81406c5648f7c8f9d4fb54563b7cdbaceb643  node-v10.3.0-sunos-x64.tar.xz
e9f79ebe7181a63c39a986506e515cbea2a010c7ee7f5e1f108bb2203712aa0d  node-v10.3.0.tar.gz
cb90e9bde8e655eeb152ebdbe1b72e7d2443e3ae2d5f9ac4acd69e7c22671f31  node-v10.3.0.tar.xz
9cceb7cd738421847d53fe0978c33111d8b6d9e252f4f3a91efe2fb006551265  node-v10.3.0-win-x64.7z
65d586afb087406a2800d8e51f664c88b26d510f077b85a3b177a1bb79f73677  node-v10.3.0-win-x64.zip
c4ff55657317de866f82348cd56ab5df8976e205807169e10b9c1a4491e026f6  node-v10.3.0-win-x86.7z
09f4a2f3a7e5e3ab1c289e1283072af41f6a572feb85a801b97e97ff9af45a4f  node-v10.3.0-win-x86.zip
42224fc5c2536b892b762b1374422d27fac0b98ff8f99d9427ba12c834c787c3  node-v10.3.0-x64.msi
d668bc11780a5210b68939275d85a7c41161d2dd6f937b7e71d176a314095267  node-v10.3.0-x86.msi
8b41acd2f961266ec636b6d530261131a4e7afbf4ffe79296c0ed25a25abc958  win-x64/node.exe
b3f32e83e60f3d271e915224fd4a6af65c3e379fb4e8d8ba0edb626680093219  win-x64/node.lib
f90ad63c752d8f8ab689e4f60ac2e6896f1ad1af61415fbf69c30a9ecd21cec5  win-x64/node_pdb.7z
9110a8a3c65f600d61c90ee7aa267b413007ae710a73b7ce3a57a5626a0edbd8  win-x64/node_pdb.zip
b0b41a68837a8562f7d2d8ee793347cbb119fa9af0bd539ddca0d3e35a1f0e46  win-x86/node.exe
702c66fe5a71ab574df0f2dba5c5abbe6c2c1ccc79198f1c896f085bbe5fc439  win-x86/node.lib
40fee79f3733d0cd5615b3b0d9a2c665d490baa1ad2f5c41d49ac846faa3d9ed  win-x86/node_pdb.7z
ab7a775e28fa9407e83497cf95c0a94c330e15c943d2ae5cdeb805709fa82a31  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQEzBAEBCAAdFiEEDv/hvO/ZyE49CYFSkzsB9AtcqUYFAlsNkOAACgkQkzsB9Atc
qUaA1Qf9HxE2DTwHFqdKLxysvRwgzFVI6oqYiNwvT/RFHw3giFQlIL5gBNie+Uyz
vLaKZ+PcbZIWkwh5fqPBWG18oJ0yZ9LN57apXMrSvI8paHf9ijRvLS7y1SNXoLY8
M2f8CJy37apQO7SQdGg5yrjCreGABDHzunPuc13X3+Duh6ksyPUw9QSq88Bn1C2o
QxN2ehs4JYc7IsRekIygFXNrekT9OmQMWph+rGsaD6iaTWgM0drotpKjM1BrnwVl
qnigYdUllxnvnOrpC1OMiB6izdx6FBYL8vinN0JBYJMFncLCqil4H+u65BpLfjbN
XPv3cSGy4s36E1Jwm52o4GoBoZhX5g==
=0W4V
-----END PGP SIGNATURE-----

```
