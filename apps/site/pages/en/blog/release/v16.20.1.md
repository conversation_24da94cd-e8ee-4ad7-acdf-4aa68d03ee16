---
date: '2023-06-20T19:27:28.686Z'
category: release
title: Node v16.20.1 (LTS)
layout: blog-post
author: <PERSON>
---

### Notable Changes

The following CVEs are fixed in this release:

- [CVE-2023-30581](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2023-30581): `mainModule.__proto__` Bypass Experimental Policy Mechanism (High)
- [CVE-2023-30585](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2023-30585): Privilege escalation via Malicious Registry Key manipulation during Node.js installer repair process (Medium)
- [CVE-2023-30588](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2023-30588): Process interuption due to invalid Public Key information in x509 certificates (Medium)
- [CVE-2023-30589](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2023-30589): HTTP Request Smuggling via Empty headers separated by CR (Medium)
- [CVE-2023-30590](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2023-30590): DiffieHellman does not generate keys after setting a private key (Medium)
- OpenSSL Security Releases
  - [OpenSSL security advisory 28th March](https://www.openssl.org/news/secadv/20230328.txt).
  - [OpenSSL security advisory 20th April](https://www.openssl.org/news/secadv/20230420.txt).
  - [OpenSSL security advisory 30th May](https://www.openssl.org/news/secadv/20230530.txt)
- c-ares vulnerabilities:
  - [GHSA-9g78-jv2r-p7vc](https://github.com/c-ares/c-ares/security/advisories/GHSA-9g78-jv2r-p7vc)
  - [GHSA-8r8p-23f3-64c2](https://github.com/c-ares/c-ares/security/advisories/GHSA-8r8p-23f3-64c2)
  - [GHSA-54xr-f67r-4pc4](https://github.com/c-ares/c-ares/security/advisories/GHSA-54xr-f67r-4pc4)
  - [GHSA-x6mf-cxr9-8q6v](https://github.com/c-ares/c-ares/security/advisories/GHSA-x6mf-cxr9-8q6v)

More detailed information on each of the vulnerabilities can be found in [June 2023 Security Releases](/blog/vulnerability/june-2023-security-releases/) blog post.

### Commits

- \[[`5a92ea7a3b`](https://github.com/nodejs/node/commit/5a92ea7a3b)] - **crypto**: handle cert with invalid SPKI gracefully (Tobias Nießen)
- \[[`5df04e893a`](https://github.com/nodejs/node/commit/5df04e893a)] - **deps**: set `CARES_RANDOM_FILE` for c-ares (Richard Lau) [#48156](https://github.com/nodejs/node/pull/48156)
- \[[`c171cbd124`](https://github.com/nodejs/node/commit/c171cbd124)] - **deps**: update c-ares to 1.19.1 (RafaelGSS) [#48115](https://github.com/nodejs/node/pull/48115)
- \[[`155d3aac02`](https://github.com/nodejs/node/commit/155d3aac02)] - **deps**: update archs files for OpenSSL-1.1.1u+quic (RafaelGSS) [#48369](https://github.com/nodejs/node/pull/48369)
- \[[`8d4c8f8ebe`](https://github.com/nodejs/node/commit/8d4c8f8ebe)] - **deps**: upgrade openssl sources to OpenSSL_1_1_1u (RafaelGSS) [#48369](https://github.com/nodejs/node/pull/48369)
- \[[`1a5c9284eb`](https://github.com/nodejs/node/commit/1a5c9284eb)] - **doc,test**: clarify behavior of DH generateKeys (Tobias Nießen) [nodejs-private/node-private#426](https://github.com/nodejs-private/node-private/pull/426)
- \[[`e42ff4b018`](https://github.com/nodejs/node/commit/e42ff4b018)] - **http**: disable request smuggling via empty headers (Paolo Insogna) [nodejs-private/node-private#429](https://github.com/nodejs-private/node-private/pull/429)
- \[[`10042683c8`](https://github.com/nodejs/node/commit/10042683c8)] - **msi**: do not create AppData\Roaming\npm (Tobias Nießen) [nodejs-private/node-private#408](https://github.com/nodejs-private/node-private/pull/408)
- \[[`a6f4e87bc9`](https://github.com/nodejs/node/commit/a6f4e87bc9)] - **policy**: handle mainModule.\_\_proto\_\_ bypass (RafaelGSS) [nodejs-private/node-private#416](https://github.com/nodejs-private/node-private/pull/416)
- \[[`b77000f4d7`](https://github.com/nodejs/node/commit/b77000f4d7)] - **test**: allow SIGBUS in signal-handler abort test (Michaël Zasso) [#47851](https://github.com/nodejs/node/pull/47851)

Windows 32-bit Installer: https://nodejs.org/dist/v16.20.1/node-v16.20.1-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v16.20.1/node-v16.20.1-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v16.20.1/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v16.20.1/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v16.20.1/node-v16.20.1.pkg \
macOS Apple Silicon 64-bit Binary: https://nodejs.org/dist/v16.20.1/node-v16.20.1-darwin-arm64.tar.gz \
macOS Intel 64-bit Binary: https://nodejs.org/dist/v16.20.1/node-v16.20.1-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v16.20.1/node-v16.20.1-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v16.20.1/node-v16.20.1-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v16.20.1/node-v16.20.1-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v16.20.1/node-v16.20.1-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v16.20.1/node-v16.20.1-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v16.20.1/node-v16.20.1-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v16.20.1/node-v16.20.1.tar.gz \
Other release files: https://nodejs.org/dist/v16.20.1/ \
Documentation: https://nodejs.org/docs/v16.20.1/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

46a7761a7f4a5c836ee045004ff5ea14819ac7e97c45d6f8731f276c2ac4099d  node-v16.20.1-aix-ppc64.tar.gz
5f6b31c5a75567d382ba67220f3d7a2d9bb0c03d8af9307cd35a9cb32a6fde9d  node-v16.20.1-darwin-arm64.tar.gz
ba13b9adbb1367ace24a0ad458ca7a9524633e09d81aae6672065917c1bc4f07  node-v16.20.1-darwin-arm64.tar.xz
d1f9c2a7c3a0fe09860f701af5fb8ff9ac72d72faa7ebabfeb5794503e79f955  node-v16.20.1-darwin-x64.tar.gz
f16394bce38ff6f205b4ea5aa69cd8c53b45cd292a20d16f0746e82fc8d0ff22  node-v16.20.1-darwin-x64.tar.xz
d727d47efd1df8b2fb7a17d6716b89e8b1ecd2a4fc7093d8a0d8935dfdca5028  node-v16.20.1-headers.tar.gz
e783ae7305ebe5b9523b7651c1dadba7dc594f9278aa8960dd6eaba5474a2c96  node-v16.20.1-headers.tar.xz
e67a75da24bd72da5b60568774ee9814bf034959e3768fe6f16eb6cfb3fc4158  node-v16.20.1-linux-arm64.tar.gz
7fce19f3d1c2952599a0b47f9f5d8f497265ad577f37f256a8c6a03be6353234  node-v16.20.1-linux-arm64.tar.xz
89c983b8353d0fce0058d12876f399e4a6ddc524b51e90089df389e713404298  node-v16.20.1-linux-armv7l.tar.gz
85b2407d87945064efc099e85026ff3fcc13ef9f7cb90a714c3ed1e606d70de7  node-v16.20.1-linux-armv7l.tar.xz
67b1954b44f778565e065ed844ee9fe681baa069026c1b62677bf9784701f46f  node-v16.20.1-linux-ppc64le.tar.gz
c99360ecd3c05f4c4f3bf7d7c8517f9e581c160a379d71a71fb0b8006becf13c  node-v16.20.1-linux-ppc64le.tar.xz
a09b324b10f19335940cb9a59410465d25ca816658f67cb79bbb13f6f1da31f6  node-v16.20.1-linux-s390x.tar.gz
599e5fb4ecc8d83d528a60b28ce5b65d8c4b44c80090398782ab890a9bebe4df  node-v16.20.1-linux-s390x.tar.xz
c76d2aabd2d02542505fd24e18876fb8515e23638531249277272def42ab54e3  node-v16.20.1-linux-x64.tar.gz
b6c60e1e106ad7d8881e83945a5208c1b1d1b63e6901c04b9dafa607aff3a154  node-v16.20.1-linux-x64.tar.xz
a1cbfb71820b782dac79ddb09e624a822d00cd3aa14ab13ca7140538d3d86e99  node-v16.20.1.pkg
2bb075fc4e8d75ca7955bca33e39ce67c041d53bcfff779210b62af3ac1ba385  node-v16.20.1.tar.gz
83e03381e271f1a5619188e7aea9d85d9b7e12f5be2a28ceb78d7249ed22b7f1  node-v16.20.1.tar.xz
d4b931a0ebea8ad7b88a2c9afccb1265223b4e520ddbc45f250d78fcb10942fb  node-v16.20.1-win-x64.7z
2a7fde996c57a969f0498742f99385a520eb14aac864e0eff9c32e3f3633ff0a  node-v16.20.1-win-x64.zip
6e4ba1168a38eaa81d5c976ccd520a9c2ead70d38333319b9458c5c9e2693213  node-v16.20.1-win-x86.7z
07c75f53e6fafe20d399eca1393d4e42c7349d811170b03c15ffaaceb4594fc9  node-v16.20.1-win-x86.zip
90257b22178f09295cf54e99dc97b989e311059d1b15d693081f7e29b8538f81  node-v16.20.1-x64.msi
9f7e3aa09f083ce9f7f23489404b2a15b4d6749728b6baf227343f42209ced33  node-v16.20.1-x86.msi
b3472e2e85ee220a0405f76a08fcabcc1d69c38fa6c7f11d344329a7ca666b0e  win-x64/node.exe
e80291db5962cc9f7ddada615e685d6af3d33f7e7a688775807369de626bd6ff  win-x64/node.lib
0945a714e86a61de1c2362cda77a1101559d4d93c707652f1a9eb167b6658257  win-x64/node_pdb.7z
2f7def39587e0910a91c7a6d4e222596ff4f907ceb9aedf4719710570775d438  win-x64/node_pdb.zip
763824c95390d5def370194dff997c751b8f21bc78b9bd8ade29104257f5e2f2  win-x86/node.exe
8876bbef0392631001bdc034e6256d607972090ded068cb84f222752d3b828cf  win-x86/node.lib
93da9e7c0da80b4472f582b799e0226f106f683245462a33006ae1e7c2012d56  win-x86/node_pdb.7z
aac0c18cdf09b0a6486083b4f0719c42ad1f9040b0e61799ce700832a40d64e7  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQGzBAEBCAAdFiEEiQwI24V5Fi/uDfnbi+q0389VXvQFAmSR/UYACgkQi+q0389V
XvSL/Qv7BsHfWjvva8YpnFfTTrnyroWu2wY3nwiIS+rP3Cg0Vf9xULaiSqJnf6y3
/OjnxsVdLg4fFqWN1FTJTGdnmjsQYIl1SnePNt7pPOVXWrbKXVAc5a7gH5TJrGU8
E09j5GSCTlGvksD3lODvlCZxT9VDtL0winRkqWMuskZHdgTvZpYP/QDf7RNuJmgq
w5Ywc4ZGYC8d2mlM0+5+LN6g3RPF8Uqrj7gTcxIGZECX7wj9FqS24XoPVZPZj1IC
WM4M4hOxDYJKz/k0I/zlnoDS70Y4mWPWtY5Dg8zt2gNPZp7mpeQqS3eBVJH8OOKn
XIc04j9JuA9oFYmbH010XoKkLmUsz0c/urb5P8Wm5ofBsd2l10rsODfgeEFCHbe3
3AIAu7tkPJijwAteAgN36vJSMWOcTJMRO75+rd1/bowzp4hSAhEScUb1ukSwJaxo
L3ttA4k35mzUtDdSwtllyIE+kjBuqGJSVI6OTDX4y9i8Hcor+ucVi/kEeXc40CVV
qHXNVYon
=tu2q
-----END PGP SIGNATURE-----

```
