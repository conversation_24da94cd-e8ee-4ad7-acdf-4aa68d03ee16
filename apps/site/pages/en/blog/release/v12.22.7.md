---
date: '2021-10-12T15:35:54.557Z'
category: release
title: Node v12.22.7 (LTS)
layout: blog-post
author: <PERSON>
---

### Notable changes

- **CVE-2021-22959**: HTTP Request Smuggling due to spaced in headers (Medium)
  - The http parser accepts requests with a space (SP) right after the header name before the colon. This can lead to HTTP Request Smuggling (HRS). More details will be available at [CVE-2021-22959](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2021-22959) after publication.
- **CVE-2021-22960**: HTTP Request Smuggling when parsing the body (Medium)
  - The parse ignores chunk extensions when parsing the body of chunked requests. This leads to HTTP Request Smuggling (HRS) under certain conditions. More details will be available at [CVE-2021-22960](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2021-22960) after publication.

### Commits

- [[`21a2e554e3`](https://github.com/nodejs/node/commit/21a2e554e3)] - **deps**: update llhttp to 2.1.4 (Fedor Indutny) [nodejs-private/node-private#286](https://github.com/nodejs-private/node-private/pull/286)
- [[`d5d3a03246`](https://github.com/nodejs/node/commit/d5d3a03246)] - **http**: add regression test for smuggling content length (Matteo Collina) [nodejs-private/node-private#286](https://github.com/nodejs-private/node-private/pull/286)
- [[`0858587f21`](https://github.com/nodejs/node/commit/0858587f21)] - **http**: add regression test for chunked smuggling (Matteo Collina) [nodejs-private/node-private#286](https://github.com/nodejs-private/node-private/pull/286)

Windows 32-bit Installer: https://nodejs.org/dist/v12.22.7/node-v12.22.7-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v12.22.7/node-v12.22.7-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v12.22.7/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v12.22.7/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v12.22.7/node-v12.22.7.pkg \
macOS Intel 64-bit Binary: https://nodejs.org/dist/v12.22.7/node-v12.22.7-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v12.22.7/node-v12.22.7-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v12.22.7/node-v12.22.7-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v12.22.7/node-v12.22.7-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v12.22.7/node-v12.22.7-aix-ppc64.tar.gz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v12.22.7/node-v12.22.7-sunos-x64.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v12.22.7/node-v12.22.7-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v12.22.7/node-v12.22.7-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v12.22.7/node-v12.22.7.tar.gz \
Other release files: https://nodejs.org/dist/v12.22.7/ \
Documentation: https://nodejs.org/docs/v12.22.7/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

32a88bed33b22ac4c7c0a11a66857748f1d2ba8f409527d87d0045e5edb11ea9  node-v12.22.7-aix-ppc64.tar.gz
4fa5bdee2ac420f8043b800c4789929b09e4a5226dfd5fa7162e53939c594eae  node-v12.22.7-darwin-x64.tar.gz
a3c711b88aef33a074cb9ede0f70182eb496ba81b1148fd5c1891ec96b25f9d9  node-v12.22.7-darwin-x64.tar.xz
3e76d309d63e14e7fcace18e455fb4a5603c3b9419a41d38779f6554057a2167  node-v12.22.7-headers.tar.gz
26ed0f49d8017111abc58fd8c408b77fabd15a2fb708a46eafaefff84cbc3cf1  node-v12.22.7-headers.tar.xz
76fa99531cc57982e9a469babb03a7bd1c47d9392cb6d4b0d55f55858c4ed5a0  node-v12.22.7-linux-arm64.tar.gz
b10df2dc3642c20f96dc1328ebd48e6dc90e9d2a370eef22ccdee819d620b0e6  node-v12.22.7-linux-arm64.tar.xz
53cd2eeb5fbd41fa43620f06fe6340794498d434ac03894e7b4fd1633108775b  node-v12.22.7-linux-armv7l.tar.gz
4d9f2da504cda6715e6061196d23f771f64e0297312c501b3dab9361278a4262  node-v12.22.7-linux-armv7l.tar.xz
8b5db09688dd0966e4cf28332b36b07dd2ab1f611adc8b0770dc4a7ead624e2a  node-v12.22.7-linux-ppc64le.tar.gz
e0274a9aa3c003f0991646cf10be0d455b8b88e16bc42b652f124e95004b5a14  node-v12.22.7-linux-ppc64le.tar.xz
6c215740f2c017edc8d99b12f95a47623f4643acdc2047372a868a0287cc04a4  node-v12.22.7-linux-s390x.tar.gz
9b4e87c1b58982dac7682497cfe72bee06bb5e959367011f760b8aba93a40c03  node-v12.22.7-linux-s390x.tar.xz
0c650e494a0ce293fb1220cc81ab5b6b819c249439c392b5ee2e8b812eec5592  node-v12.22.7-linux-x64.tar.gz
2768bc01d2f97ab8135b8c03b275b9689573964b426b5dd9082334fd70dcc583  node-v12.22.7-linux-x64.tar.xz
072224552a7548e183ee3bfc5af0d67d451a8a11a885ff27ed262038903baa39  node-v12.22.7.pkg
dd11017cf3687ff89ee6e465b48bd0a1a21d0abdb1bd74c88160b31eae1b51e9  node-v12.22.7-sunos-x64.tar.gz
c3b2d4ddc637e2adbfce6f49249748205f4c897b2732aebd2ccb9ff60c5e68ed  node-v12.22.7-sunos-x64.tar.xz
f7eaea2f9fbd09b8fceec40d64a00132928eb8b55a587c2d0a563536e3da9273  node-v12.22.7.tar.gz
cc6a23b44870679a94bd8f3c8d4e1f4b77bb2712a36888ab87463459e6785f6b  node-v12.22.7.tar.xz
372adf19f7f3c3b2cea0a320523600ca8a16020e1fec561ebf87193ffc80c94a  node-v12.22.7-win-x64.7z
51cbf739cfb44a1cffb995ae1ba3e616418280736f4b40798676290f3fa421dc  node-v12.22.7-win-x64.zip
30b414b3944cd8f8d54b459d5fb87c04794a99d59dbbab53fcafe463c50bd0e2  node-v12.22.7-win-x86.7z
1604e00e83e72af2cfe4bfe87345b32069f8d96ed7e54398683151d2c9c477e5  node-v12.22.7-win-x86.zip
6d416829df54917a45e754723b685aa097ad2ede8a4d875faab0fafa264d8833  node-v12.22.7-x64.msi
6969d5fe47249ee8d89a12fb7396cf261565215751318cf246bf8c384d85545e  node-v12.22.7-x86.msi
1af4fcf2daba5a76c2159db1c8f10174e2a7710626774314ee09504ebc113a12  win-x64/node.exe
28e5c24831deedbf4fb8a9560f2c4f95205479c589f54a9a53ec346f6a5cf8bf  win-x64/node.lib
08d36eea4914be643b486a77c1707997c15d7779a705dfc129b94722fe32adf2  win-x64/node_pdb.7z
a9ea708a58ae5914729c583a39da05415fca9c40855e1d2ef9d14fae6fb76096  win-x64/node_pdb.zip
3b0b1763bda58e6c3343cd1e4f240980b15b76a6757ad89c319b5a82c8d94d92  win-x86/node.exe
dad0e6bef1c45f4f43fbf84c33df6b910ace8122eff3f8d39d5ebecd25320ba4  win-x86/node.lib
b00ac44eea98fd9efd8ee958e6a14475184fc3c81ecc3037775bf8fd3c4a5c95  win-x86/node_pdb.7z
0c2b8ff24d03c423b59f1e6cd01c7ba03a3a6566662a39ab393dc7a51c674a0c  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEdPEmArbxxOkT+qN606iWE2Q7YgEFAmFlqAoACgkQ06iWE2Q7
YgHgABAAsewOKgRQdB/3AEbrO/ySrWje8qdfc/6ym5z42+SerYmwywTSNZDIRcuu
i9I/vofLRsuwu7S3pgYRrmTIxqFNxQ7SNg20/fv7zTP7BRYQziNvkY9TlecmmmuT
kkrcCosDFFgZVtqTgKjqw7/+sMeV1aBrmbQ6mRcZy8qQUFMIreYQHZPlhMaW4YtG
hH63xy0QRs/PCMr3nNF1dN1oqhDY+oWdlvqzAXE81b9X42OkyEBwXMQrKYEdj/CP
dfHq88Abi6JZrIeJJHATlUswngR0hCsX7mui1A2VKkOLsyUdBL4tgI4oLNYP+dyA
8IDGLkaAkZ08IOg/fauQRD+BWJ2+NBR/mhKkMMmWKGRbErC48++NxA33508qPqjw
GhGHZkvuVmnGX9q1XszieNCrJGKOOck4tB2IZeEucoKWQIXZyt8W+0GzcYVcMQG0
ZgKF63NVmVvhgC/VqeDknBfvPIPTFq3u6aJpRtPQypfcmn2j+nQP7c0XwvQW6MoA
csjbcFwDHkvx5IZtIuWIoi19LvTg4TX6H63UUx3E2dWa6rD/Md4DcSVrpgSbnH3i
ZspDnJZBQwOAFr+ZCGkjrb1y6YcEy5ZoKF/uiPOs7/VlEn0KdV36Y1k3nAgmOdVi
uP8WtS4KRxyth5ed8Zo6JdFRGtEQatdm0y3ABDiT+rxP7lgRPr4=
=dWOu
-----END PGP SIGNATURE-----

```
