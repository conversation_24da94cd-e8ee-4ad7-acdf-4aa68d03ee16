---
date: '2025-01-21T17:05:27.542Z'
category: release
title: Node v18.20.6 (LTS)
layout: blog-post
author: <PERSON>
---

## 2025-01-21, Version 18.20.6 'Hydrogen' (LTS), @RafaelGSS

This is a security release.

### Notable Changes

- CVE-2025-23085 - src: fix HTTP2 mem leak on premature close and ERR_PROTO (Medium)
- CVE-2025-23084 - path: fix path traversal in normalize() on Windows (Medium)

Dependency update:

- CVE-2025-22150 - Use of Insufficiently Random Values in undici fetch() (Medium)

### Commits

- \[[`c03ad5ed63`](https://github.com/nodejs/node/commit/c03ad5ed63)] - **build**: use rclone instead of aws CLI (<PERSON><PERSON><PERSON>) [#55617](https://github.com/nodejs/node/pull/55617)
- \[[`8232463294`](https://github.com/nodejs/node/commit/8232463294)] - **build, tools**: drop leading `/` from `r2dir` (<PERSON>) [#53951](https://github.com/nodejs/node/pull/53951)
- \[[`b26bcd3394`](https://github.com/nodejs/node/commit/b26bcd3394)] - **build, tools**: copy release assets to staging R2 bucket once built (flakey5) [#51394](https://github.com/nodejs/node/pull/51394)
- \[[`56df127b7b`](https://github.com/nodejs/node/commit/56df127b7b)] - **build,tools**: simplify upload of shasum signatures (Michaël Zasso) [#53892](https://github.com/nodejs/node/pull/53892)
- \[[`a63e9372ed`](https://github.com/nodejs/node/commit/a63e9372ed)] - **(CVE-2025-22150)** **deps**: update undici to v5.28.5 (Matteo Collina) [nodejs-private/node-private#657](https://github.com/nodejs-private/node-private/pull/657)
- \[[`da2d177f91`](https://github.com/nodejs/node/commit/da2d177f91)] - **(CVE-2025-23084)** **path**: fix path traversal in normalize() on Windows (Tobias Nießen) [nodejs-private/node-private#555](https://github.com/nodejs-private/node-private/pull/555)
- \[[`6cc8d58e6f`](https://github.com/nodejs/node/commit/6cc8d58e6f)] - **(CVE-2025-23085)** **src**: fix HTTP2 mem leak on premature close and ERR_PROTO (RafaelGSS) [nodejs-private/node-private#650](https://github.com/nodejs-private/node-private/pull/650)

Windows 32-bit Installer: https://nodejs.org/dist/v18.20.6/node-v18.20.6-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v18.20.6/node-v18.20.6-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v18.20.6/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v18.20.6/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v18.20.6/node-v18.20.6.pkg \
macOS Apple Silicon 64-bit Binary: https://nodejs.org/dist/v18.20.6/node-v18.20.6-darwin-arm64.tar.gz \
macOS Intel 64-bit Binary: https://nodejs.org/dist/v18.20.6/node-v18.20.6-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v18.20.6/node-v18.20.6-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v18.20.6/node-v18.20.6-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v18.20.6/node-v18.20.6-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v18.20.6/node-v18.20.6-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v18.20.6/node-v18.20.6-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v18.20.6/node-v18.20.6-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v18.20.6/node-v18.20.6.tar.gz \
Other release files: https://nodejs.org/dist/v18.20.6/ \
Documentation: https://nodejs.org/docs/v18.20.6/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

25f9f8b8275186b632a9e0ff7f22bba85eebe3aeb7c4ed84822529ba917d2a04  node-v18.20.6-aix-ppc64.tar.gz
7105fb1ac42968010020db61edcc0ea0a366b37d792405d2959668e45b612753  node-v18.20.6-darwin-arm64.tar.gz
cc797a76aee995ebef16a446a445886287dab82d2e496a2ea58197e1065a88d4  node-v18.20.6-darwin-arm64.tar.xz
25a4040000e9838af28d0d168301c70d07dcc61294089dde5f5d9044dafda1e5  node-v18.20.6-darwin-x64.tar.gz
5f079ab410b3278e05cb367b4d2f4638bb93cdd19c8224f3da1068ef19e1f5ab  node-v18.20.6-darwin-x64.tar.xz
8f7f8f23d5f3265d09a556ba9aed207792ab90307140d04e55096dea7d63ac6e  node-v18.20.6-headers.tar.gz
68a846f73547b4074469eccc758a6700237d0e615e77766931cad6c40f1f111f  node-v18.20.6-headers.tar.xz
9580a8c3bb3e8014e15c6940595c45e831f5a878dec78f086824a8adf91a327f  node-v18.20.6-linux-arm64.tar.gz
169d317cc39ba5513c9588f7aded1bdff7f807b82c4dacb40ca03fd427d288b0  node-v18.20.6-linux-arm64.tar.xz
d55580014639304a9fc0567a5e5b772d8a19d6c02c40f3fd4006216f97c759c2  node-v18.20.6-linux-armv7l.tar.gz
7375b789338f01d4496e8febc16234cf3672efa57a16ca7bfc17d07ce034d9ac  node-v18.20.6-linux-armv7l.tar.xz
afa669b8c424f0f0d33ef461764194d126a1e5a07bad432252cfbefb7f9a52d0  node-v18.20.6-linux-ppc64le.tar.gz
3f431b1beb67141a43fea19b778e534357798bb20b4129de6360edae88a107bc  node-v18.20.6-linux-ppc64le.tar.xz
bb65d62cd88f45c91b60da975563ad1bffefce9337ba6e85997fb42496c19fd0  node-v18.20.6-linux-s390x.tar.gz
d58c4e6107b58b27b493c7a111b16b6f393e5af7ab69584b0548581fdca0a449  node-v18.20.6-linux-s390x.tar.xz
bff0672c5117e6f0809c456d9194630b5886442ddf298b2292529959c45b92c0  node-v18.20.6-linux-x64.tar.gz
abf47264a9a13b2233743ce8a966945388a1a10a56f841310a6d4dd12e18ca9a  node-v18.20.6-linux-x64.tar.xz
58e2e5a7cfe2a2d780a5246a114c644459dae8d71d4c2eabbe5b7ae91e21e5df  node-v18.20.6.pkg
e7ddfeabea3d1f7cc622cc9861d2fb0955b9e60940dbbedbed6f2f821ab3e4c7  node-v18.20.6.tar.gz
c669b48b632fa6797d4f5fa7bbd2b476ec961120957864402226cc9fd8ebbc0e  node-v18.20.6.tar.xz
6f289a5054bfec7a0a03d7feb09a58fd5661749dedfdb71bb1c3bfb854990faf  node-v18.20.6-win-x64.7z
611a152838d67e05ddde02fbf5abc7f40bccd80a7e68fdaeae8745f729acff59  node-v18.20.6-win-x64.zip
064c34005f30f89f86fbbafb90b4da02587f785d17c899026832f221637d1736  node-v18.20.6-win-x86.7z
cde3c32739d7f4e8c405819c66ca2e5c9462e43a31633f4185d4865bd60d4027  node-v18.20.6-win-x86.zip
df2828bfbbc5da31e70e840da365289c2578c9bfdca5d6b502e87e6675efba5a  node-v18.20.6-x64.msi
1942cff39580a9f15cb28418a391a5779d81b3384f2a8766cc4ec2d63c45f61f  node-v18.20.6-x86.msi
a64403d47ef2121e60eac8643f85fb3a682b7ffcc3b6ffe3fcacf10101cb7fbf  win-x64/node.exe
abfd50aed29f44e39725388fc60210b4a716b4fd759ebb9124ae01c30ccf81f9  win-x64/node.lib
5c99323f91d14803d5b9aff6474cc5d43bbb317589fc1ab099e3a4f31fcadd1f  win-x64/node_pdb.7z
52c63d593b3eeb9c6c27cd843111e613584749a2106ca4177dc057b2b906cd42  win-x64/node_pdb.zip
c3e2439a460ae24386694f03cae8b4105c2013aae6ae7cb1d5acef2a599346f3  win-x86/node.exe
3442ac0d3ad2c5e840f0f2ae84a1c7abe1736a315d83a161b843c5c31a986f83  win-x86/node.lib
1639c9101390cd0a0d2d516b6c8dfe091bd3d14f0d12195fee22ca3e82d8d510  win-x86/node_pdb.7z
af1e5136a75422ed6df75dae6dd419eb5a179e05c6f5b9aa1b42ea6fb8ea496e  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQGzBAEBCAAdFiEEiQwI24V5Fi/uDfnbi+q0389VXvQFAmeP0PoACgkQi+q0389V
XvQ3ugv/Qi7vH20YIkuxRGiMu2QtfSta4zxh3KT9Vty/j8uHLqEBF+VLFzK8wpkB
uBMkVgnsj8YrmuF5QSlIGZqhmzDpO4qiN9f+je7CZCIH7+y5t507mML/SRDaDUb4
njzBfHNoTGpIUqFc8L0bxCcuTTytLK5hbyiz4C7ISIYO3dtlqB3KBF70/cSNVYDw
uZSxZ2pOdA569XP/DVEbCIyP1zw3oxmjBP+2vgStnsJ8zBGI4aYVQkowtRmj5n3K
3dS3QtvdVySH+RJatNvf6trEk3DMAelQcJpHq9yRu+dXl0MFdCmWnXugnPFT9d/v
5f/AlKbMeXJPIhIrCZX9SuaHw7kQ31wklSsbyKZkXzgD1p9pf4jcwNUWTIvnzl+S
2k+YVNAFM7YeOxpsbgGebD8wp1KQkf8kIyVUFMhBJvCkaZmKEIQStNZtPfpyyWFS
It9EMDBPCP6oUDit+ZVHS3wncqW0nOsgguqZ5zl6k4BH7B/i8oIo+g8FAthar+J9
a0OF6HP9
=/PCS
-----END PGP SIGNATURE-----
```
