---
date: '2020-04-28T13:28:22.174Z'
category: release
title: Node v12.16.3 (LTS)
layout: blog-post
author: <PERSON><PERSON><PERSON>
---

### Notable Changes

- **Dependencies**:
  - Updated OpenSSL to 1.1.1g (<PERSON><PERSON><PERSON>) [#32971](https://github.com/nodejs/node/pull/32971).
  - Updated c-ares to 1.16.0 (<PERSON>) [#32246](https://github.com/nodejs/node/pull/32246).
  - Updated experimental uvwasi to 0.0.6 (<PERSON>) [#32309](https://github.com/nodejs/node/pull/32309).
- **ESM (experimental)**:
  - Additional warnings are no longer printed for modules that use conditional
    exports or package name self resolution (<PERSON>) [#31845](https://github.com/nodejs/node/pull/31845).

### Commits

- [[`2c5b0147fa`](https://github.com/nodejs/node/commit/2c5b0147fa)] - **async_hooks**: use hasHooks function internally (rickyes) [#32656](https://github.com/nodejs/node/pull/32656)
- [[`28abbfd594`](https://github.com/nodejs/node/commit/28abbfd594)] - **async_hooks**: move to lazy destroy hook registration in AsyncResource (Andrey Pechkurov) [#32429](https://github.com/nodejs/node/pull/32429)
- [[`146ad4eaae`](https://github.com/nodejs/node/commit/146ad4eaae)] - **async_hooks**: avoid resource reuse by FileHandle (Gerhard Stoebich) [#31972](https://github.com/nodejs/node/pull/31972)
- [[`39a3cc13dc`](https://github.com/nodejs/node/commit/39a3cc13dc)] - **buffer,n-api**: fix double ArrayBuffer::Detach() during cleanup (Anna Henningsen) [#33039](https://github.com/nodejs/node/pull/33039)
- [[`20f3e9d836`](https://github.com/nodejs/node/commit/20f3e9d836)] - **build**: output dots instead of tap in GitHub actions (Michaël Zasso) [#32714](https://github.com/nodejs/node/pull/32714)
- [[`c98aa9312e`](https://github.com/nodejs/node/commit/c98aa9312e)] - **build**: move doc versions JSON file out of out/doc (Richard Lau) [#32728](https://github.com/nodejs/node/pull/32728)
- [[`546a9ea998`](https://github.com/nodejs/node/commit/546a9ea998)] - **build**: fix LINT_MD_NEWER assignment (Rich Trott) [#32712](https://github.com/nodejs/node/pull/32712)
- [[`ae772b7c6a`](https://github.com/nodejs/node/commit/ae772b7c6a)] - **build**: log detected compilers in --verbose mode (Richard Lau) [#32715](https://github.com/nodejs/node/pull/32715)
- [[`43055519d3`](https://github.com/nodejs/node/commit/43055519d3)] - **build**: use tabs for indentation in Makefile (Luigi Pinca) [#32614](https://github.com/nodejs/node/pull/32614)
- [[`2e31ac96f3`](https://github.com/nodejs/node/commit/2e31ac96f3)] - **build**: remove make lint on lint-py (himself65) [#32599](https://github.com/nodejs/node/pull/32599)
- [[`d8a948f0fc`](https://github.com/nodejs/node/commit/d8a948f0fc)] - **build**: disable -Wattributes warnings on aix (Ben Noordhuis) [#32419](https://github.com/nodejs/node/pull/32419)
- [[`a3848e51aa`](https://github.com/nodejs/node/commit/a3848e51aa)] - **build**: expand ASAN acronym in configure help (Sam Roberts) [#32325](https://github.com/nodejs/node/pull/32325)
- [[`c8541a7d7a`](https://github.com/nodejs/node/commit/c8541a7d7a)] - **build**: disable libstdc++ debug containers globally (Ben Noordhuis) [#30147](https://github.com/nodejs/node/pull/30147)
- [[`d3c9a82a6e`](https://github.com/nodejs/node/commit/d3c9a82a6e)] - **build**: remove empty line on node.gyp file (Juan José Arboleda) [#31952](https://github.com/nodejs/node/pull/31952)
- [[`e65586985f`](https://github.com/nodejs/node/commit/e65586985f)] - **build**: support android build on ndk version equal or above 23 (forfun414) [#31521](https://github.com/nodejs/node/pull/31521)
- [[`790841597d`](https://github.com/nodejs/node/commit/790841597d)] - **console**: fixup error message (James M Snell) [#32475](https://github.com/nodejs/node/pull/32475)
- [[`d19251630e`](https://github.com/nodejs/node/commit/d19251630e)] - **crypto**: clear openssl error stack after en/decrypt (Ben Noordhuis) [#32248](https://github.com/nodejs/node/pull/32248)
- [[`51f05d2f3d`](https://github.com/nodejs/node/commit/51f05d2f3d)] - **deps**: update archs files for OpenSSL-1.1.1g (Hassaan Pasha) [#32971](https://github.com/nodejs/node/pull/32971)
- [[`a89744f4e0`](https://github.com/nodejs/node/commit/a89744f4e0)] - **deps**: upgrade openssl sources to 1.1.1g (Hassaan Pasha) [#32971](https://github.com/nodejs/node/pull/32971)
- [[`80c89d4ec7`](https://github.com/nodejs/node/commit/80c89d4ec7)] - **deps**: update archs files for OpenSSL-1.1.1f (Hassaan Pasha) [#32583](https://github.com/nodejs/node/pull/32583)
- [[`c9cc38114a`](https://github.com/nodejs/node/commit/c9cc38114a)] - **deps**: upgrade openssl sources to 1.1.1f (Hassaan Pasha) [#32583](https://github.com/nodejs/node/pull/32583)
- [[`fedcb16144`](https://github.com/nodejs/node/commit/fedcb16144)] - **deps**: update acorn to v7.1.1 (Ruben Bridgewater) [#32310](https://github.com/nodejs/node/pull/32310)
- [[`37476a339a`](https://github.com/nodejs/node/commit/37476a339a)] - **deps**: upgrade to c-ares v1.16.0 (Anna Henningsen) [#32246](https://github.com/nodejs/node/pull/32246)
- [[`fe0e1dbd13`](https://github.com/nodejs/node/commit/fe0e1dbd13)] - **deps**: update to uvwasi 0.0.6 (Colin Ihrig) [#32309](https://github.com/nodejs/node/pull/32309)
- [[`2e92cb476d`](https://github.com/nodejs/node/commit/2e92cb476d)] - **deps**: V8: cherry-pick f9257802c1c0 (Matheus Marchini) [#32180](https://github.com/nodejs/node/pull/32180)
- [[`0e922440d6`](https://github.com/nodejs/node/commit/0e922440d6)] - **deps,doc**: move openssl maintenance guide to doc (Sam Roberts) [#32209](https://github.com/nodejs/node/pull/32209)
- [[`06d16cf9ef`](https://github.com/nodejs/node/commit/06d16cf9ef)] - **dns**: remove duplicate code (rickyes) [#32664](https://github.com/nodejs/node/pull/32664)
- [[`af392a114b`](https://github.com/nodejs/node/commit/af392a114b)] - **doc**: add link to code ide configs (Robert Nagy) [#32767](https://github.com/nodejs/node/pull/32767)
- [[`b1790fbf4b`](https://github.com/nodejs/node/commit/b1790fbf4b)] - **doc**: replace node-test-pull-request-lite-pipeline from onboarding (Juan José Arboleda) [#32736](https://github.com/nodejs/node/pull/32736)
- [[`00ce6a3240`](https://github.com/nodejs/node/commit/00ce6a3240)] - **doc**: add useful v8 option section (Nimit) [#32262](https://github.com/nodejs/node/pull/32262)
- [[`c78019d792`](https://github.com/nodejs/node/commit/c78019d792)] - **doc**: add himself65 to collaborators (himself65) [#32734](https://github.com/nodejs/node/pull/32734)
- [[`16126328ac`](https://github.com/nodejs/node/commit/16126328ac)] - **doc**: clarify behavior of napi_get_typedarray_info (Michael Dawson) [#32603](https://github.com/nodejs/node/pull/32603)
- [[`a5fd29b024`](https://github.com/nodejs/node/commit/a5fd29b024)] - **doc**: remove optional parameter from markdown anchor link (Rich Trott) [#32671](https://github.com/nodejs/node/pull/32671)
- [[`d2c86a9dfc`](https://github.com/nodejs/node/commit/d2c86a9dfc)] - **doc**: clarify `listening` event (Harshitha KP) [#32581](https://github.com/nodejs/node/pull/32581)
- [[`9039c03967`](https://github.com/nodejs/node/commit/9039c03967)] - **doc**: update Ninja information in build guide (Adrian Estrada) [#32629](https://github.com/nodejs/node/pull/32629)
- [[`1d563a646e`](https://github.com/nodejs/node/commit/1d563a646e)] - **doc**: correct version metadata for Readable.from (Dave Vandyke) [#32639](https://github.com/nodejs/node/pull/32639)
- [[`5e2791ee84`](https://github.com/nodejs/node/commit/5e2791ee84)] - **doc**: adjust paths in openssl maintenance guide (Hassaan Pasha) [#32593](https://github.com/nodejs/node/pull/32593)
- [[`21c3685623`](https://github.com/nodejs/node/commit/21c3685623)] - **doc**: clarify docs fs.watch exception may be emitted (Juan José Arboleda) [#32513](https://github.com/nodejs/node/pull/32513)
- [[`c3d91eb94d`](https://github.com/nodejs/node/commit/c3d91eb94d)] - **doc**: add unreachable code on events example (himself65) [#32364](https://github.com/nodejs/node/pull/32364)
- [[`b4ba9b8bef`](https://github.com/nodejs/node/commit/b4ba9b8bef)] - **doc**: clarify `length` param in `buffer.write` (Harshitha KP) [#32119](https://github.com/nodejs/node/pull/32119)
- [[`5996df3c39`](https://github.com/nodejs/node/commit/5996df3c39)] - **doc**: document that server.address() can return null (Thomas Watson Steen) [#32519](https://github.com/nodejs/node/pull/32519)
- [[`a299e9cf28`](https://github.com/nodejs/node/commit/a299e9cf28)] - **doc**: return type of `crypto.getFips()` may change (Richard Lau) [#32580](https://github.com/nodejs/node/pull/32580)
- [[`4604127697`](https://github.com/nodejs/node/commit/4604127697)] - **doc**: fix return type of `crypto.getFips()` (Richard Lau) [#32580](https://github.com/nodejs/node/pull/32580)
- [[`f2235f68aa`](https://github.com/nodejs/node/commit/f2235f68aa)] - **doc**: clarify `requireManualDestroy` option (Harshitha KP) [#32514](https://github.com/nodejs/node/pull/32514)
- [[`7e952f2d38`](https://github.com/nodejs/node/commit/7e952f2d38)] - **doc**: fix wordy sentence (Moni) [#32567](https://github.com/nodejs/node/pull/32567)
- [[`f93b770bda`](https://github.com/nodejs/node/commit/f93b770bda)] - **doc**: fix more links (Alba Mendez) [#32586](https://github.com/nodejs/node/pull/32586)
- [[`d764414706`](https://github.com/nodejs/node/commit/d764414706)] - **doc**: improve markdown link checker (Alba Mendez) [#32586](https://github.com/nodejs/node/pull/32586)
- [[`3d36458cc6`](https://github.com/nodejs/node/commit/3d36458cc6)] - **doc**: add flarna to collaborators (Gerhard Stoebich) [#32620](https://github.com/nodejs/node/pull/32620)
- [[`4b417f87bd`](https://github.com/nodejs/node/commit/4b417f87bd)] - **doc**: improve fs.read documentation (Hachimi Aa (Sfeir)) [#29270](https://github.com/nodejs/node/pull/29270)
- [[`959055f225`](https://github.com/nodejs/node/commit/959055f225)] - **doc**: add ASAN build instructions (gengjiawen) [#32436](https://github.com/nodejs/node/pull/32436)
- [[`f1552f830f`](https://github.com/nodejs/node/commit/f1552f830f)] - **doc**: update context-aware section of addon doc (Gabriel Schulhof) [#28659](https://github.com/nodejs/node/pull/28659)
- [[`d0d414d98c`](https://github.com/nodejs/node/commit/d0d414d98c)] - **doc**: update AUTHORS list (Luigi Pinca) [#32222](https://github.com/nodejs/node/pull/32222)
- [[`e51c42dc52`](https://github.com/nodejs/node/commit/e51c42dc52)] - **doc**: tests local links in markdown documents (Antoine du HAMEL) [#32359](https://github.com/nodejs/node/pull/32359)
- [[`8b355eab57`](https://github.com/nodejs/node/commit/8b355eab57)] - **doc**: fix profile type of --heap-prof-name (Syohei YOSHIDA) [#32404](https://github.com/nodejs/node/pull/32404)
- [[`59a8dbebc2`](https://github.com/nodejs/node/commit/59a8dbebc2)] - **doc**: use uppercase on windows path (himself65) [#32294](https://github.com/nodejs/node/pull/32294)
- [[`fa9b10cebe`](https://github.com/nodejs/node/commit/fa9b10cebe)] - **doc**: rename cve_management_process.md to fit doc style guide (Ling Samuel) [#32456](https://github.com/nodejs/node/pull/32456)
- [[`3ed9fcd784`](https://github.com/nodejs/node/commit/3ed9fcd784)] - **doc**: add mildsunrise to collaborators (Alba Mendez) [#32525](https://github.com/nodejs/node/pull/32525)
- [[`5d15dd3fe3`](https://github.com/nodejs/node/commit/5d15dd3fe3)] - **doc**: add link to DNS definition (unknown) [#32228](https://github.com/nodejs/node/pull/32228)
- [[`8d27eb94d1`](https://github.com/nodejs/node/commit/8d27eb94d1)] - **doc**: remove extraneous sentence in events.md (Rich Trott) [#32457](https://github.com/nodejs/node/pull/32457)
- [[`1c84d85437`](https://github.com/nodejs/node/commit/1c84d85437)] - **doc**: trim wording in n-api.md text about exceptions (Rich Trott) [#32457](https://github.com/nodejs/node/pull/32457)
- [[`bba8dd3344`](https://github.com/nodejs/node/commit/bba8dd3344)] - **doc**: simplify and correct example descriptions in net.md (Rich Trott) [#32451](https://github.com/nodejs/node/pull/32451)
- [[`2976ac6c2e`](https://github.com/nodejs/node/commit/2976ac6c2e)] - **doc**: add new TSC members (Michael Dawson) [#32473](https://github.com/nodejs/node/pull/32473)
- [[`3d752cd3b9`](https://github.com/nodejs/node/commit/3d752cd3b9)] - **doc**: improve wording in vm.md (Rich Trott) [#32427](https://github.com/nodejs/node/pull/32427)
- [[`80a8e20826`](https://github.com/nodejs/node/commit/80a8e20826)] - **doc**: update security release process (Sam Roberts) [#31679](https://github.com/nodejs/node/pull/31679)
- [[`80493f54c8`](https://github.com/nodejs/node/commit/80493f54c8)] - **doc**: fix some 404 links (Thomas Watson Steen) [#32200](https://github.com/nodejs/node/pull/32200)
- [[`76e2455b06`](https://github.com/nodejs/node/commit/76e2455b06)] - **doc**: expand fs.watch caveats (Bartosz Sosnowski) [#32176](https://github.com/nodejs/node/pull/32176)
- [[`c1c3aa1b5f`](https://github.com/nodejs/node/commit/c1c3aa1b5f)] - **doc**: add Ruben to TSC (Michael Dawson) [#32213](https://github.com/nodejs/node/pull/32213)
- [[`385faf7721`](https://github.com/nodejs/node/commit/385faf7721)] - **doc**: include the error type in the request.resolve doc (Joe Pea) [#32152](https://github.com/nodejs/node/pull/32152)
- [[`11899f647a`](https://github.com/nodejs/node/commit/11899f647a)] - **doc**: clear up child_process command resolution (Denys Otrishko) [#32091](https://github.com/nodejs/node/pull/32091)
- [[`e33e989f20`](https://github.com/nodejs/node/commit/e33e989f20)] - **doc**: clarify windows specific behaviour (Sam Roberts) [#32079](https://github.com/nodejs/node/pull/32079)
- [[`860239255b`](https://github.com/nodejs/node/commit/860239255b)] - **doc**: improve Buffer documentation (Anna Henningsen) [#32086](https://github.com/nodejs/node/pull/32086)
- [[`ab1136a7ed`](https://github.com/nodejs/node/commit/ab1136a7ed)] - **doc**: add support encoding link on string_decoder.md (himself65) [#31911](https://github.com/nodejs/node/pull/31911)
- [[`c439d83dbf`](https://github.com/nodejs/node/commit/c439d83dbf)] - **doc**: add entry for `AsyncHook` class (Harshitha KP) [#31865](https://github.com/nodejs/node/pull/31865)
- [[`e6e38ecf64`](https://github.com/nodejs/node/commit/e6e38ecf64)] - **doc**: prevent tables from shrinking page (David Gilbertson) [#31859](https://github.com/nodejs/node/pull/31859)
- [[`6e68d9816d`](https://github.com/nodejs/node/commit/6e68d9816d)] - **doc**: fix anchor for ERR_TLS_INVALID_CONTEXT (Tobias Nießen) [#31915](https://github.com/nodejs/node/pull/31915)
- [[`d3b9a8810c`](https://github.com/nodejs/node/commit/d3b9a8810c)] - **doc,crypto**: clarify oaepHash option's impact (Filip Skokan) [#32340](https://github.com/nodejs/node/pull/32340)
- [[`b85bc0cc02`](https://github.com/nodejs/node/commit/b85bc0cc02)] - **fs**: fixup error message for invalid options.recursive (James M Snell) [#32472](https://github.com/nodejs/node/pull/32472)
- [[`010814856a`](https://github.com/nodejs/node/commit/010814856a)] - **fs**: fix writeFile\[Sync\] for non-seekable files (Alba Mendez) [#32006](https://github.com/nodejs/node/pull/32006)
- [[`225ddd5f42`](https://github.com/nodejs/node/commit/225ddd5f42)] - **http**: move free socket error handling to agent (Robert Nagy) [#32003](https://github.com/nodejs/node/pull/32003)
- [[`3b0204245d`](https://github.com/nodejs/node/commit/3b0204245d)] - **http**: don't emit 'readable' after 'close' (Robert Nagy) [#32277](https://github.com/nodejs/node/pull/32277)
- [[`52a52d2664`](https://github.com/nodejs/node/commit/52a52d2664)] - **http**: fixup options.method error message (James M Snell) [#32471](https://github.com/nodejs/node/pull/32471)
- [[`cf47bb9818`](https://github.com/nodejs/node/commit/cf47bb9818)] - **http**: don't emit 'finish' after 'error' (Robert Nagy) [#32276](https://github.com/nodejs/node/pull/32276)
- [[`f9123eb91b`](https://github.com/nodejs/node/commit/f9123eb91b)] - **http**: fix socket re-use races (Robert Nagy) [#32000](https://github.com/nodejs/node/pull/32000)
- [[`e54eb46cdb`](https://github.com/nodejs/node/commit/e54eb46cdb)] - **http2**: rename counter in `mapToHeaders` inner loop (Mateusz Krawczuk) [#32012](https://github.com/nodejs/node/pull/32012)
- [[`0db58753db`](https://github.com/nodejs/node/commit/0db58753db)] - **lib**: fix return type of setTimeout in net.Socket (龙腾道) [#32722](https://github.com/nodejs/node/pull/32722)
- [[`a152792590`](https://github.com/nodejs/node/commit/a152792590)] - **lib**: removes unnecessary params (Jesus Hernandez) [#32694](https://github.com/nodejs/node/pull/32694)
- [[`7dd001c1db`](https://github.com/nodejs/node/commit/7dd001c1db)] - **lib**: changed functional logic in cluster schedulers (Yash Ladha) [#32505](https://github.com/nodejs/node/pull/32505)
- [[`5a671772a2`](https://github.com/nodejs/node/commit/5a671772a2)] - **lib**: use spread operator on cluster (himself65) [#32125](https://github.com/nodejs/node/pull/32125)
- [[`4d0be3dce5`](https://github.com/nodejs/node/commit/4d0be3dce5)] - **meta**: move inactive collaborators to emeriti (Rich Trott) [#32151](https://github.com/nodejs/node/pull/32151)
- [[`ecddf6519f`](https://github.com/nodejs/node/commit/ecddf6519f)] - **module**: disable conditional exports, self resolve warnings (Guy Bedford) [#31845](https://github.com/nodejs/node/pull/31845)
- [[`717f9c5905`](https://github.com/nodejs/node/commit/717f9c5905)] - **module**: path-only CJS exports extension searching (Guy Bedford) [#32351](https://github.com/nodejs/node/pull/32351)
- [[`ff5ab6f925`](https://github.com/nodejs/node/commit/ff5ab6f925)] - **net**: fix crash if POLLHUP is received (Santiago Gimeno) [#32590](https://github.com/nodejs/node/pull/32590)
- [[`ed21d32a7c`](https://github.com/nodejs/node/commit/ed21d32a7c)] - **net**: wait for shutdown to complete before closing (Robert Nagy) [#32491](https://github.com/nodejs/node/pull/32491)
- [[`7d66ceadbb`](https://github.com/nodejs/node/commit/7d66ceadbb)] - **perf,src**: add HistogramBase and internal/histogram.js (James M Snell) [#31988](https://github.com/nodejs/node/pull/31988)
- [[`f302ac9ae4`](https://github.com/nodejs/node/commit/f302ac9ae4)] - **perf_hooks**: allow omitted parameters in 'performance.measure' (himself65) [#32651](https://github.com/nodejs/node/pull/32651)
- [[`7c0c4e9a7e`](https://github.com/nodejs/node/commit/7c0c4e9a7e)] - **repl**: fixup error message (James M Snell) [#32474](https://github.com/nodejs/node/pull/32474)
- [[`522101dbca`](https://github.com/nodejs/node/commit/522101dbca)] - **src**: removes unused v8::Integer and v8::Array namespace (Jesus Hernandez) [#32779](https://github.com/nodejs/node/pull/32779)
- [[`f9d94143fb`](https://github.com/nodejs/node/commit/f9d94143fb)] - **src**: remove unused v8::TryCatch namespace (Juan José Arboleda) [#32729](https://github.com/nodejs/node/pull/32729)
- [[`d0d7ebc2a6`](https://github.com/nodejs/node/commit/d0d7ebc2a6)] - **src**: remove duplicated code (himself65) [#32719](https://github.com/nodejs/node/pull/32719)
- [[`a50220955e`](https://github.com/nodejs/node/commit/a50220955e)] - **src**: refactor to avoid goto in node_file.cc (Tobias Nießen) [#32637](https://github.com/nodejs/node/pull/32637)
- [[`fabb53ed79`](https://github.com/nodejs/node/commit/fabb53ed79)] - **src**: fix warnings on SPrintF (himself65) [#32558](https://github.com/nodejs/node/pull/32558)
- [[`3605a9d67a`](https://github.com/nodejs/node/commit/3605a9d67a)] - **src**: replace goto with lambda in options parser (Tobias Nießen) [#32635](https://github.com/nodejs/node/pull/32635)
- [[`872f893e0f`](https://github.com/nodejs/node/commit/872f893e0f)] - **src**: align PerformanceState class name with conventions (Anna Henningsen) [#32539](https://github.com/nodejs/node/pull/32539)
- [[`191cde0e4d`](https://github.com/nodejs/node/commit/191cde0e4d)] - **src**: remove unnecessary 'Local.As' operation (himself65) [#32286](https://github.com/nodejs/node/pull/32286)
- [[`6d71eb5b5b`](https://github.com/nodejs/node/commit/6d71eb5b5b)] - **src**: add test/abort build tasks (Christian Niederer) [#31740](https://github.com/nodejs/node/pull/31740)
- [[`0dfb9514de`](https://github.com/nodejs/node/commit/0dfb9514de)] - **src**: add aliased-buffer-overflow abort test (Christian Niederer) [#31740](https://github.com/nodejs/node/pull/31740)
- [[`28cfaa837e`](https://github.com/nodejs/node/commit/28cfaa837e)] - **src**: check for overflow when extending AliasedBufferBase (Christian Niederer) [#31740](https://github.com/nodejs/node/pull/31740)
- [[`4155358031`](https://github.com/nodejs/node/commit/4155358031)] - **src**: replace handle dereference with ContainerOf (Harshitha KP) [#32298](https://github.com/nodejs/node/pull/32298)
- [[`c9b22c8d6d`](https://github.com/nodejs/node/commit/c9b22c8d6d)] - **src**: enhance template function 'MakeUtf8String' (himself65) [#32322](https://github.com/nodejs/node/pull/32322)
- [[`ad347f4cbb`](https://github.com/nodejs/node/commit/ad347f4cbb)] - **src**: remove excess v8 namespace (himself65) [#32191](https://github.com/nodejs/node/pull/32191)
- [[`12d83b3242`](https://github.com/nodejs/node/commit/12d83b3242)] - **src**: clean v8 namespaces in env.cc file (Juan José Arboleda) [#32374](https://github.com/nodejs/node/pull/32374)
- [[`13a7e0546f`](https://github.com/nodejs/node/commit/13a7e0546f)] - **src**: check for empty maybe local (Xavier Stouder) [#32339](https://github.com/nodejs/node/pull/32339)
- [[`aaf94fd6bb`](https://github.com/nodejs/node/commit/aaf94fd6bb)] - **src**: cleanup DestroyParam when Environment exits (Anna Henningsen) [#32421](https://github.com/nodejs/node/pull/32421)
- [[`4b5fd24855`](https://github.com/nodejs/node/commit/4b5fd24855)] - **src**: enhance C++ sprintf utility (himself65) [#32385](https://github.com/nodejs/node/pull/32385)
- [[`46e68bb445`](https://github.com/nodejs/node/commit/46e68bb445)] - **src**: simplify IsolateData shortcut accesses (Anna Henningsen) [#32407](https://github.com/nodejs/node/pull/32407)
- [[`7aa2ee2bd8`](https://github.com/nodejs/node/commit/7aa2ee2bd8)] - **src**: delete CallbackInfo when cleared from cleanup hook (Anna Henningsen) [#32405](https://github.com/nodejs/node/pull/32405)
- [[`7a346f63d6`](https://github.com/nodejs/node/commit/7a346f63d6)] - **src**: update comment for SetImmediate() (Anna Henningsen) [#32300](https://github.com/nodejs/node/pull/32300)
- [[`46c751e7f1`](https://github.com/nodejs/node/commit/46c751e7f1)] - **src**: handle NULL env scenario (himself65) [#32230](https://github.com/nodejs/node/pull/32230)
- [[`9b6f678751`](https://github.com/nodejs/node/commit/9b6f678751)] - **src**: fix warn_unused_result compiler warning (Colin Ihrig) [#32241](https://github.com/nodejs/node/pull/32241)
- [[`4e268314b5`](https://github.com/nodejs/node/commit/4e268314b5)] - **src**: refactor to more safe method (gengjiawen) [#32087](https://github.com/nodejs/node/pull/32087)
- [[`f223d2c7e4`](https://github.com/nodejs/node/commit/f223d2c7e4)] - **src**: fix spawnSync CHECK when SIGKILL fails (Ben Noordhuis) [#31768](https://github.com/nodejs/node/pull/31768)
- [[`5b2f698b32`](https://github.com/nodejs/node/commit/5b2f698b32)] - **src**: fix missing extra ca in tls.rootCertificates (Eric Bickle) [#32075](https://github.com/nodejs/node/pull/32075)
- [[`a53980d947`](https://github.com/nodejs/node/commit/a53980d947)] - **src**: fix -Wmaybe-uninitialized compiler warning (Ben Noordhuis) [#31809](https://github.com/nodejs/node/pull/31809)
- [[`a2d961da23`](https://github.com/nodejs/node/commit/a2d961da23)] - **src**: remove unused include from node_file.cc (Ben Noordhuis) [#31809](https://github.com/nodejs/node/pull/31809)
- [[`8fe70e88fe`](https://github.com/nodejs/node/commit/8fe70e88fe)] - **src**: elevate v8 namespace (RamanandPatil) [#32041](https://github.com/nodejs/node/pull/32041)
- [[`7e5e34d01e`](https://github.com/nodejs/node/commit/7e5e34d01e)] - **src**: simplify node_worker.cc using new KVStore API (Denys Otrishko) [#31773](https://github.com/nodejs/node/pull/31773)
- [[`7152fe3180`](https://github.com/nodejs/node/commit/7152fe3180)] - **src**: improve KVStore API (Denys Otrishko) [#31773](https://github.com/nodejs/node/pull/31773)
- [[`3bf21b096e`](https://github.com/nodejs/node/commit/3bf21b096e)] - **src**: fix minor typo in base_object.h (Daniel Bevenius) [#31535](https://github.com/nodejs/node/pull/31535)
- [[`8d1eeb1ae5`](https://github.com/nodejs/node/commit/8d1eeb1ae5)] - **stream**: combine properties using defineProperties (antsmartian) [#31187](https://github.com/nodejs/node/pull/31187)
- [[`d07dd313ae`](https://github.com/nodejs/node/commit/d07dd313ae)] - **stream**: add regression test for async iteration completion (Matteo Collina) [#31508](https://github.com/nodejs/node/pull/31508)
- [[`2f72054ec7`](https://github.com/nodejs/node/commit/2f72054ec7)] - **test**: replace console.log/error with debuglog (Agustin Daguerre) [#32695](https://github.com/nodejs/node/pull/32695)
- [[`bc9453a870`](https://github.com/nodejs/node/commit/bc9453a870)] - **test**: make sure that inspector tests finish (Anna Henningsen) [#32673](https://github.com/nodejs/node/pull/32673)
- [[`2cf7381a87`](https://github.com/nodejs/node/commit/2cf7381a87)] - **test**: fix check error name on error instance (himself65) [#32508](https://github.com/nodejs/node/pull/32508)
- [[`e4174165f3`](https://github.com/nodejs/node/commit/e4174165f3)] - **_Revert_** "**test**: mark empty udp tests flaky on OS X" (Luigi Pinca) [#32489](https://github.com/nodejs/node/pull/32489)
- [[`6feed98f33`](https://github.com/nodejs/node/commit/6feed98f33)] - **test**: remove unused variables on async hook test (Julian Duque) [#32630](https://github.com/nodejs/node/pull/32630)
- [[`b0386b4aaf`](https://github.com/nodejs/node/commit/b0386b4aaf)] - **test**: check that --expose-internals is disallowed in NODE_OPTIONS (Juan José Arboleda) [#32554](https://github.com/nodejs/node/pull/32554)
- [[`0adc867d59`](https://github.com/nodejs/node/commit/0adc867d59)] - **test**: add Worker initialization failure test case (Harshitha KP) [#31929](https://github.com/nodejs/node/pull/31929)
- [[`73221278d7`](https://github.com/nodejs/node/commit/73221278d7)] - **test**: fix tool path in test-doctool-versions.js (Richard Lau) [#32645](https://github.com/nodejs/node/pull/32645)
- [[`90a5b9d964`](https://github.com/nodejs/node/commit/90a5b9d964)] - **test**: copy addons .gitignore to test/abort/ (Anna Henningsen) [#32624](https://github.com/nodejs/node/pull/32624)
- [[`39be571a3f`](https://github.com/nodejs/node/commit/39be571a3f)] - **test**: refactor test-http2-buffersize (Rich Trott) [#32540](https://github.com/nodejs/node/pull/32540)
- [[`f71007ff39`](https://github.com/nodejs/node/commit/f71007ff39)] - **test**: skip crypto test on arm buildbots (Ben Noordhuis) [#32636](https://github.com/nodejs/node/pull/32636)
- [[`4e405ee899`](https://github.com/nodejs/node/commit/4e405ee899)] - **test**: replace console.error() with debuglog calls (Rich Trott) [#32588](https://github.com/nodejs/node/pull/32588)
- [[`8083d452e6`](https://github.com/nodejs/node/commit/8083d452e6)] - **test**: add a missing common.mustCall (Harshitha KP) [#32305](https://github.com/nodejs/node/pull/32305)
- [[`416531227e`](https://github.com/nodejs/node/commit/416531227e)] - **test**: remove unnecessary console.log() calls (Juan José Arboleda) [#32541](https://github.com/nodejs/node/pull/32541)
- [[`30d21fb6e6`](https://github.com/nodejs/node/commit/30d21fb6e6)] - **test**: replace console.log() with debuglog() (Juan José Arboleda) [#32550](https://github.com/nodejs/node/pull/32550)
- [[`fcf1123052`](https://github.com/nodejs/node/commit/fcf1123052)] - **test**: validate util.format when the value is 'Infinity' (Andrés M. Gómez) [#32573](https://github.com/nodejs/node/pull/32573)
- [[`e2174e4e3c`](https://github.com/nodejs/node/commit/e2174e4e3c)] - **test**: fix fs test-fs-utimes strictEqual arg order (Ben Noordhuis) [#32420](https://github.com/nodejs/node/pull/32420)
- [[`32ab30cc35`](https://github.com/nodejs/node/commit/32ab30cc35)] - **test**: use common.mustCall in test-worker-esm-exit (himself65) [#32544](https://github.com/nodejs/node/pull/32544)
- [[`a0552441fa`](https://github.com/nodejs/node/commit/a0552441fa)] - **test**: use template strings in parallel tests (Daniel Estiven Rico Posada) [#32549](https://github.com/nodejs/node/pull/32549)
- [[`d53d152da3`](https://github.com/nodejs/node/commit/d53d152da3)] - **test**: add known issues test for #31733 (Ben Noordhuis) [#31734](https://github.com/nodejs/node/pull/31734)
- [[`d6f6623243`](https://github.com/nodejs/node/commit/d6f6623243)] - **test**: refactor test-http-information-processing (Rich Trott) [#32547](https://github.com/nodejs/node/pull/32547)
- [[`b6e739a6b3`](https://github.com/nodejs/node/commit/b6e739a6b3)] - **test**: skip a wasi test on IBMi PASE (Xu Meng) [#32459](https://github.com/nodejs/node/pull/32459)
- [[`a40e7daf3c`](https://github.com/nodejs/node/commit/a40e7daf3c)] - **test**: harden the tick sampling logic (Harshitha KP) [#32190](https://github.com/nodejs/node/pull/32190)
- [[`9c84d7773a`](https://github.com/nodejs/node/commit/9c84d7773a)] - **test**: skip some binding tests on IBMi PASE (Xu Meng) [#31967](https://github.com/nodejs/node/pull/31967)
- [[`afc0c708a2`](https://github.com/nodejs/node/commit/afc0c708a2)] - **test**: revise test-http-response-multi-content-length (Rich Trott) [#32526](https://github.com/nodejs/node/pull/32526)
- [[`df890ad3d2`](https://github.com/nodejs/node/commit/df890ad3d2)] - **test**: remove a duplicated test (himself65) [#32453](https://github.com/nodejs/node/pull/32453)
- [[`fa4de53a3e`](https://github.com/nodejs/node/commit/fa4de53a3e)] - **test**: check bundled binaries are signed on macOS (Richard Lau) [#32522](https://github.com/nodejs/node/pull/32522)
- [[`d9abea5e3f`](https://github.com/nodejs/node/commit/d9abea5e3f)] - **test**: unflake async-hooks/test-statwatcher (Bartosz Sosnowski) [#32484](https://github.com/nodejs/node/pull/32484)
- [[`5cae1b7a53`](https://github.com/nodejs/node/commit/5cae1b7a53)] - **test**: use Promise.all() in test-cluster-net-listen-ipv6only-false (Rich Trott) [#32398](https://github.com/nodejs/node/pull/32398)
- [[`60db56ddba`](https://github.com/nodejs/node/commit/60db56ddba)] - **test**: replace Map with Array in test-cluster-net-listen-ipv6only-false (Rich Trott) [#32398](https://github.com/nodejs/node/pull/32398)
- [[`565f0f73e2`](https://github.com/nodejs/node/commit/565f0f73e2)] - **test**: revise test-http-client-default-headers-exist (Rich Trott) [#32493](https://github.com/nodejs/node/pull/32493)
- [[`7f5b89c307`](https://github.com/nodejs/node/commit/7f5b89c307)] - **test**: use mustCall in place of countdown in timers test (Rich Trott) [#32416](https://github.com/nodejs/node/pull/32416)
- [[`97e352d1a6`](https://github.com/nodejs/node/commit/97e352d1a6)] - **test**: replace countdown with Promise.all() in cluster-net-listen tests (Rich Trott) [#32381](https://github.com/nodejs/node/pull/32381)
- [[`1b79174203`](https://github.com/nodejs/node/commit/1b79174203)] - **test**: replace Map with Array in cluster-net-listen tests (Rich Trott) [#32381](https://github.com/nodejs/node/pull/32381)
- [[`85ae5661df`](https://github.com/nodejs/node/commit/85ae5661df)] - **test**: uv_tty_init returns EBADF on IBM i (Xu Meng) [#32338](https://github.com/nodejs/node/pull/32338)
- [[`8dbd7cf0e4`](https://github.com/nodejs/node/commit/8dbd7cf0e4)] - **test**: use Promise.all() in test-hash-seed (Rich Trott) [#32273](https://github.com/nodejs/node/pull/32273)
- [[`92a207cd2d`](https://github.com/nodejs/node/commit/92a207cd2d)] - **test**: workaround for V8 8.1 inspector pause issue (Matheus Marchini) [#32234](https://github.com/nodejs/node/pull/32234)
- [[`776905ef99`](https://github.com/nodejs/node/commit/776905ef99)] - **test**: use portable EOL (Harshitha KP) [#32104](https://github.com/nodejs/node/pull/32104)
- [[`914edddd79`](https://github.com/nodejs/node/commit/914edddd79)] - **test**: `buffer.write` with longer string scenario (Harshitha KP) [#32123](https://github.com/nodejs/node/pull/32123)
- [[`7060ed1176`](https://github.com/nodejs/node/commit/7060ed1176)] - **test**: fix test-tls-env-extra-ca-file-load (Eric Bickle) [#32073](https://github.com/nodejs/node/pull/32073)
- [[`bee009d271`](https://github.com/nodejs/node/commit/bee009d271)] - **test**: improve test-fs-existssync-false.js (himself65) [#31883](https://github.com/nodejs/node/pull/31883)
- [[`0403f00321`](https://github.com/nodejs/node/commit/0403f00321)] - **test**: mark test-timers-blocking-callback flaky on osx (Myles Borins) [#32189](https://github.com/nodejs/node/pull/32189)
- [[`fa7e975d2f`](https://github.com/nodejs/node/commit/fa7e975d2f)] - **test**: warn when inspector process crashes (Matheus Marchini) [#32133](https://github.com/nodejs/node/pull/32133)
- [[`4a94179a3c`](https://github.com/nodejs/node/commit/4a94179a3c)] - **tools**: update Boxstarter script and document (himself65) [#32299](https://github.com/nodejs/node/pull/32299)
- [[`8bc53d1298`](https://github.com/nodejs/node/commit/8bc53d1298)] - **tools**: update ESLint to 7.0.0-alpha.3 (Colin Ihrig) [#32533](https://github.com/nodejs/node/pull/32533)
- [[`baf56f8135`](https://github.com/nodejs/node/commit/baf56f8135)] - **tools**: fixup icutrim.py use of string and bytes objects (Jonathan MERCIER) [#31659](https://github.com/nodejs/node/pull/31659)
- [[`540a024057`](https://github.com/nodejs/node/commit/540a024057)] - **tools**: update to acorn@7.1.1 (Rich Trott) [#32259](https://github.com/nodejs/node/pull/32259)
- [[`ecf842ec27`](https://github.com/nodejs/node/commit/ecf842ec27)] - **tools**: enable no-useless-backreference lint rule (Colin Ihrig) [#31400](https://github.com/nodejs/node/pull/31400)
- [[`bcf152e2d0`](https://github.com/nodejs/node/commit/bcf152e2d0)] - **tools**: enable default-case-last lint rule (Colin Ihrig) [#31400](https://github.com/nodejs/node/pull/31400)
- [[`5dacfa76f2`](https://github.com/nodejs/node/commit/5dacfa76f2)] - **tools**: update ESLint to 7.0.0-alpha.2 (Colin Ihrig) [#31400](https://github.com/nodejs/node/pull/31400)
- [[`e641b3c6b6`](https://github.com/nodejs/node/commit/e641b3c6b6)] - **tools**: update ESLint to 7.0.0-alpha.1 (Colin Ihrig) [#31400](https://github.com/nodejs/node/pull/31400)
- [[`394fa1f356`](https://github.com/nodejs/node/commit/394fa1f356)] - **tools**: update ESLint to 7.0.0-alpha.0 (Colin Ihrig) [#31400](https://github.com/nodejs/node/pull/31400)
- [[`848df6f6cc`](https://github.com/nodejs/node/commit/848df6f6cc)] - **tracing**: do not attempt to call into JS when disallowed (Anna Henningsen) [#32548](https://github.com/nodejs/node/pull/32548)
- [[`12fe985154`](https://github.com/nodejs/node/commit/12fe985154)] - **util**: only inspect error properties that are not visible otherwise (Ruben Bridgewater) [#32327](https://github.com/nodejs/node/pull/32327)
- [[`eccd2a7740`](https://github.com/nodejs/node/commit/eccd2a7740)] - **util**: fix inspecting document.all (Gus Caplan) [#31938](https://github.com/nodejs/node/pull/31938)
- [[`58c6422f83`](https://github.com/nodejs/node/commit/58c6422f83)] - **util**: text decoding allows SharedArrayBuffer (Bradley Farias) [#32203](https://github.com/nodejs/node/pull/32203)
- [[`10c525f38d`](https://github.com/nodejs/node/commit/10c525f38d)] - **win,build**: set exit_code on configure failure (Bartlomiej Brzozowski) [#32205](https://github.com/nodejs/node/pull/32205)
- [[`aeea7d9c1f`](https://github.com/nodejs/node/commit/aeea7d9c1f)] - **worker**: do not emit 'exit' events during process.exit() (Anna Henningsen) [#32546](https://github.com/nodejs/node/pull/32546)
- [[`28cb7e78ff`](https://github.com/nodejs/node/commit/28cb7e78ff)] - **worker**: improve MessagePort performance (Anna Henningsen) [#31605](https://github.com/nodejs/node/pull/31605)

Windows 32-bit Installer: https://nodejs.org/dist/v12.16.3/node-v12.16.3-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v12.16.3/node-v12.16.3-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v12.16.3/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v12.16.3/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v12.16.3/node-v12.16.3.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v12.16.3/node-v12.16.3-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v12.16.3/node-v12.16.3-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v12.16.3/node-v12.16.3-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v12.16.3/node-v12.16.3-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v12.16.3/node-v12.16.3-aix-ppc64.tar.gz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v12.16.3/node-v12.16.3-sunos-x64.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v12.16.3/node-v12.16.3-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v12.16.3/node-v12.16.3-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v12.16.3/node-v12.16.3.tar.gz \
Other release files: https://nodejs.org/dist/v12.16.3/ \
Documentation: https://nodejs.org/docs/v12.16.3/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

7d16447943c66dda607420e798ddc9c7d59f8849aa739e061b6cab63e7c10dd1  node-v12.16.3-aix-ppc64.tar.gz
0718812b3ab8e77e8d1354f4d10428ae99d78f721bdcceee527c4b592ea7fed0  node-v12.16.3-darwin-x64.tar.gz
91c0bebdd3ca5e75c04a41291064f25e57163e763203bc0091f3f75a1def9597  node-v12.16.3-darwin-x64.tar.xz
24d10b7205f01326420dd577c7df697c4a9464d37f7c39eadba46924988aabb6  node-v12.16.3-headers.tar.gz
27169e5284a7bc2783bfb79bb80f69453cc59d2d5ca3d5d22265826370475cad  node-v12.16.3-headers.tar.xz
f91f92bd690f457ced9faa81bef8eeb8706abea33a349358299e30f1c2522f30  node-v12.16.3-linux-arm64.tar.gz
8311f513a7d1911200502dd3e00142cef56d600c851d5870f68d939802205b73  node-v12.16.3-linux-arm64.tar.xz
d1d1498e602c27fb907d45851046c882761c75bd9bcce746eacb37049d0f2d0b  node-v12.16.3-linux-armv7l.tar.gz
8fdf1751c985c4e8048b23bbe9e36aa0cad0011c755427694ea0fda9efad6d97  node-v12.16.3-linux-armv7l.tar.xz
d7ca87225e16da24d43c147b3081140b98b208c81e467f6c02b451e996fa42f3  node-v12.16.3-linux-ppc64le.tar.gz
624539ab6a1ce796dac8cd6474172e334f34e90797c377fe361aa1e7b56f4331  node-v12.16.3-linux-ppc64le.tar.xz
483972add1eb4d9a6d56067042fddd3ab558e5605b75f79ec8a290de027525d5  node-v12.16.3-linux-s390x.tar.gz
57bd769e8dbc07666597bf19f7146274e2cdbe737ac3b455de586d3ce3079e36  node-v12.16.3-linux-s390x.tar.xz
66518c31ea7735ae5a0bb8ea27edfee846702dbdc708fea6ad4a308d43ef5652  node-v12.16.3-linux-x64.tar.gz
1956e196e3c3c8ef5f0c45db76d7c1245af4ccdda2b7ab30a57ce91d6e165caa  node-v12.16.3-linux-x64.tar.xz
a78f8c22ac83f2f6ea2932422ae3b0b37659c443bf052d69450198c8250b5cd2  node-v12.16.3.pkg
4690380d482318aeb5214dbeb378440b345396f8429f0075ec84ef4173992b07  node-v12.16.3-sunos-x64.tar.gz
40eb9d0adf49dd0f77367de97e5c9822a4e85d579cb3674018fc6e8f1f1e757d  node-v12.16.3-sunos-x64.tar.xz
4694056576b6c48a70ad401cf061181eafbc3cbf5070907cdba4e6de1be567a5  node-v12.16.3.tar.gz
f0559b0829c7f2ac10bf5b3e157e498227e95e26cdb6af814969c49f367359a3  node-v12.16.3.tar.xz
743962e097d40383fac3c2e727251400e60185b294dad2a894dbce3687710f10  node-v12.16.3-win-x64.7z
d0bb0e0b1f1a948529ddd543e2cfe0bfe209eb843defc70217b3d2f84cbf3b78  node-v12.16.3-win-x64.zip
bbb46883746012693d6a4002281a62b5b401db0fb1961701e8e5b69f0b4c346b  node-v12.16.3-win-x86.7z
ee4431eb88ddbab45484f6cc538d82e371145d7a763e4a6c5b889303eace483b  node-v12.16.3-win-x86.zip
03450c12d04fc34c2d857a6a2b11ab1cfee7e0631546ab3f25eeb6452e83803f  node-v12.16.3-x64.msi
aaac0c3722c2349d0615214991e2bb05bb4f1d5819504705a6bd21c109ae768b  node-v12.16.3-x86.msi
3f118b24581cb898a4ba8c6eb42bd18daa701d5dcc73fd4492cd0cece5851ae5  win-x64/node.exe
7c2343076c27d6d175d7b614a80e3c6d3bfb61fc289c68edc0f26dfef20e7dd7  win-x64/node.lib
b3623cc6847b2376877ab9fdfb144802e23814a8dc03b141a018283fd7d7fb7d  win-x64/node_pdb.7z
d32e6725635cf8633446046fadfb0163e8dea8cae1264cd6469458e21a3d5bef  win-x64/node_pdb.zip
472f80eb80a492237960ffdb56d80d26e4ce5906f3a5f7445a0d6fecbcfd6c42  win-x86/node.exe
10c2e1817c7d032beca156417c5b0906b3081c5f6cc9caa27d18d46ccd916da6  win-x86/node.lib
c362793087c9714cb3e660ef113902fbd156071cd2cbb471ab4f52a313a9a623  win-x86/node_pdb.7z
b241af4718d011b9fae3b8eac84ea120ddce076b970b8e3c1b99e17a18a40599  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEj8yhP+8dDC6RAI4Jdw96mlrhVgAFAl6oLvkACgkQdw96mlrh
VgDKLBAAk3z4kOEeHReozVlqmf25fmglcklbwoHi/HZquKSGP9QosY5+NTl0lowq
odMfgPjeVJytmpgRgDmzbVYIxFcgPOvhPpFKYEbtbwUq/JklkCl9IegkBm+14PXu
nFNJs+fciqv3S1OSWvZ4aVM5u9/O/Cw9vrAO0Wbi+ODv2RRkzgXIS69WmsszmaxZ
HH199N1wglWn9LkrNFQtQaRpiZbVR0JwdC//NykAf+3peslot4Imrc4hziBx1qF5
Hz3n9pXbf5hp+RKle6f4SV7tVYWkbfZTv5X7a29e6ECAzQ4zCPZi6/5EIAtM+vT7
MoLtpaLWCLZkWYOCS2WS2kD0kv1e1AEBN+RekKaBmJv2YPMkNrjDaBCW4Tmd1SJF
cf1PRFslVP6oaKaiRua/HjNZHmCvg//W3KKIyMopmgcccZbMV0MkClE46QXoRuOF
7SaggaAj0YsfLHBByJ+NoHZtHnn1dbXG6MZu6cWom9RE1/DToiBTRjsJYfqXIArk
JowOIKHPOPQDnf4a1HQJkI5DH3MR053+fFraxL7WtBxYOi4EKUYTqGx7I+qhYJcV
x6Fd9diMAsUa2yGMD7Fi/IfYYar6+DyXMStnRz1lbjiX6iKoHYKXYzdLU/e9mb+n
UHnvkQWNrHB/KEtfTvgd+e+8Gz+p19Fq0cw8OhtUYNWl7rTva2s=
=AHbE
-----END PGP SIGNATURE-----

```
