---
date: '2018-12-18T19:02:27.599Z'
category: release
title: Node v11.5.0 (Current)
layout: blog-post
author: <PERSON>
---

### Notable Changes

- **tls**:
  - support "BEGIN TRUSTED CERTIFICATE" for ca: (<PERSON>) [#24733](https://github.com/nodejs/node/pull/24733)
- **util**:
  - add inspection getter option (<PERSON><PERSON>) [#24852](https://github.com/nodejs/node/pull/24852)

### Commits

- [[`bf4faf3ffc`](https://github.com/nodejs/node/commit/bf4faf3ffc)] - **assert,util**: harden comparison (<PERSON><PERSON>) [#24831](https://github.com/nodejs/node/pull/24831)
- [[`302081bafc`](https://github.com/nodejs/node/commit/302081bafc)] - **build**: make lint-addon-docs run only if needed (<PERSON>) [#24993](https://github.com/nodejs/node/pull/24993)
- [[`cc8a805e31`](https://github.com/nodejs/node/commit/cc8a805e31)] - **build**: fix compiler version detection (Richard Lau) [#24879](https://github.com/nodejs/node/pull/24879)
- [[`bde5df20d6`](https://github.com/nodejs/node/commit/bde5df20d6)] - **doc**: fix node.1 --http-parser sort order (cjihrig) [#25045](https://github.com/nodejs/node/pull/25045)
- [[`a9f239fb60`](https://github.com/nodejs/node/commit/a9f239fb60)] - **doc**: add EventTarget link to worker_threads (Azard) [#25058](https://github.com/nodejs/node/pull/25058)
- [[`00ce972305`](https://github.com/nodejs/node/commit/00ce972305)] - **doc**: make README formatting more consistent (wenjun ye) [#25003](https://github.com/nodejs/node/pull/25003)
- [[`dbdea36190`](https://github.com/nodejs/node/commit/dbdea36190)] - **doc**: add codebytere's info to release team (Shelley Vohr) [#25022](https://github.com/nodejs/node/pull/25022)
- [[`877f8a0094`](https://github.com/nodejs/node/commit/877f8a0094)] - **doc**: revise internal vs. public API in Collaborator Guide (Rich Trott) [#24975](https://github.com/nodejs/node/pull/24975)
- [[`f0bcacdcc6`](https://github.com/nodejs/node/commit/f0bcacdcc6)] - **doc**: update a link of npm repository (Daijiro Wachi) [#24969](https://github.com/nodejs/node/pull/24969)
- [[`1e096291d6`](https://github.com/nodejs/node/commit/1e096291d6)] - **doc**: fix author-ready conflict (Ruben Bridgewater) [#25015](https://github.com/nodejs/node/pull/25015)
- [[`b2e6cbddd8`](https://github.com/nodejs/node/commit/b2e6cbddd8)] - **doc**: update Useful CI Jobs section of Collaborator Guide (Rich Trott) [#24916](https://github.com/nodejs/node/pull/24916)
- [[`9bfbb6822b`](https://github.com/nodejs/node/commit/9bfbb6822b)] - **doc**: add class worker documentation (yoshimoto koki) [#24849](https://github.com/nodejs/node/pull/24849)
- [[`0220cd3260`](https://github.com/nodejs/node/commit/0220cd3260)] - **doc**: remove bad link to irc info (Richard Lau) [#24967](https://github.com/nodejs/node/pull/24967)
- [[`a6a3829962`](https://github.com/nodejs/node/commit/a6a3829962)] - **doc**: simplify author ready (Ruben Bridgewater) [#24893](https://github.com/nodejs/node/pull/24893)
- [[`cda1da9200`](https://github.com/nodejs/node/commit/cda1da9200)] - **doc**: update "Testing and CI" in Collaborator Guide (Rich Trott) [#24884](https://github.com/nodejs/node/pull/24884)
- [[`81dce68a9d`](https://github.com/nodejs/node/commit/81dce68a9d)] - **doc**: update http doc for new Agent()/support options in socket.connect() (Beni von Cheni) [#24846](https://github.com/nodejs/node/pull/24846)
- [[`643ca14d2c`](https://github.com/nodejs/node/commit/643ca14d2c)] - **doc**: fix order of events when request is aborted (Luigi Pinca) [#24779](https://github.com/nodejs/node/pull/24779)
- [[`c300aaa208`](https://github.com/nodejs/node/commit/c300aaa208)] - **doc**: update LICENSE file (Anna Henningsen) [#24898](https://github.com/nodejs/node/pull/24898)
- [[`c4f3cf9759`](https://github.com/nodejs/node/commit/c4f3cf9759)] - **doc**: revise Waiting for Approvals documentation (Rich Trott) [#24845](https://github.com/nodejs/node/pull/24845)
- [[`56b2a7274c`](https://github.com/nodejs/node/commit/56b2a7274c)] - **inspector**: split the HostPort being used and the one parsed from CLI (Joyee Cheung) [#24772](https://github.com/nodejs/node/pull/24772)
- [[`2456a545a6`](https://github.com/nodejs/node/commit/2456a545a6)] - **lib**: ensure readable stream flows to end (Mikko Rantanen) [#24918](https://github.com/nodejs/node/pull/24918)
- [[`79c52a9f88`](https://github.com/nodejs/node/commit/79c52a9f88)] - **lib**: improve error creation performance (Ruben Bridgewater) [#24747](https://github.com/nodejs/node/pull/24747)
- [[`25dae6cffd`](https://github.com/nodejs/node/commit/25dae6cffd)] - **module**: use validateString in modules/esm (ZYSzys) [#24868](https://github.com/nodejs/node/pull/24868)
- [[`2a11e6aaf3`](https://github.com/nodejs/node/commit/2a11e6aaf3)] - **module**: use validateString in modules/cjs (ZYSzys) [#24863](https://github.com/nodejs/node/pull/24863)
- [[`f4d5c358d9`](https://github.com/nodejs/node/commit/f4d5c358d9)] - **net**: use strict comparisons for fd (cjihrig) [#25014](https://github.com/nodejs/node/pull/25014)
- [[`5f60ed7647`](https://github.com/nodejs/node/commit/5f60ed7647)] - **path**: replace assertPath() with validator (cjihrig) [#24840](https://github.com/nodejs/node/pull/24840)
- [[`f43f45a26c`](https://github.com/nodejs/node/commit/f43f45a26c)] - **process**: properly close file descriptor on exit (Ruben Bridgewater) [#24972](https://github.com/nodejs/node/pull/24972)
- [[`8b109f05d9`](https://github.com/nodejs/node/commit/8b109f05d9)] - **process**: simplify check in previousValueIsValid() (cjihrig) [#24836](https://github.com/nodejs/node/pull/24836)
- [[`2e94f3b798`](https://github.com/nodejs/node/commit/2e94f3b798)] - **querystring**: remove eslint-disable (cjihrig) [#24995](https://github.com/nodejs/node/pull/24995)
- [[`5f8950b652`](https://github.com/nodejs/node/commit/5f8950b652)] - **src**: emit 'params' instead of 'data' for NodeTracing.dataCollected (Kelvin Jin) [#24949](https://github.com/nodejs/node/pull/24949)
- [[`d0270f3a5c`](https://github.com/nodejs/node/commit/d0270f3a5c)] - **src**: add GetLoadedLibraries routine (Gireesh Punathil) [#24825](https://github.com/nodejs/node/pull/24825)
- [[`f8547019c7`](https://github.com/nodejs/node/commit/f8547019c7)] - **src**: include node_internals.h in node_metadata.cc (Daniel Bevenius) [#24933](https://github.com/nodejs/node/pull/24933)
- [[`5a1289d128`](https://github.com/nodejs/node/commit/5a1289d128)] - **src**: create env-\>inspector_console_api_object earlier (Joyee Cheung) [#24906](https://github.com/nodejs/node/pull/24906)
- [[`d7605725df`](https://github.com/nodejs/node/commit/d7605725df)] - **src**: remove use of CallOnForegroundThread() (cjihrig) [#24925](https://github.com/nodejs/node/pull/24925)
- [[`08c6b2126c`](https://github.com/nodejs/node/commit/08c6b2126c)] - **src**: use Local version of ToBoolean() (cjihrig) [#24924](https://github.com/nodejs/node/pull/24924)
- [[`5206f3add5`](https://github.com/nodejs/node/commit/5206f3add5)] - **src**: do not alias new and old signal masks (Sam Roberts) [#24810](https://github.com/nodejs/node/pull/24810)
- [[`94d02cabb9`](https://github.com/nodejs/node/commit/94d02cabb9)] - **src**: fix warning for potential snprintf truncation (Sam Roberts) [#24810](https://github.com/nodejs/node/pull/24810)
- [[`9b000e5088`](https://github.com/nodejs/node/commit/9b000e5088)] - **src**: remove finalized\_ member from Hash class (Daniel Bevenius) [#24822](https://github.com/nodejs/node/pull/24822)
- [[`90d481ea45`](https://github.com/nodejs/node/commit/90d481ea45)] - **src**: remove unused env variables in node_util (Daniel Bevenius) [#24820](https://github.com/nodejs/node/pull/24820)
- [[`d449c36500`](https://github.com/nodejs/node/commit/d449c36500)] - **stream**: re-use existing `once()` implementation (Anna Henningsen) [#24991](https://github.com/nodejs/node/pull/24991)
- [[`39af61faa2`](https://github.com/nodejs/node/commit/39af61faa2)] - **stream**: fix end-of-stream for HTTP/2 (Anna Henningsen) [#24926](https://github.com/nodejs/node/pull/24926)
- [[`4f0d17b019`](https://github.com/nodejs/node/commit/4f0d17b019)] - **test**: remove unnecessary linter comment (cjihrig) [#25013](https://github.com/nodejs/node/pull/25013)
- [[`ab1801b8ad`](https://github.com/nodejs/node/commit/ab1801b8ad)] - **test**: use global.gc() instead of gc() (cjihrig) [#25012](https://github.com/nodejs/node/pull/25012)
- [[`ddff644172`](https://github.com/nodejs/node/commit/ddff644172)] - **test**: run eslint on test file and fix errors (Ruben Bridgewater) [#25009](https://github.com/nodejs/node/pull/25009)
- [[`110fd39dfe`](https://github.com/nodejs/node/commit/110fd39dfe)] - **test**: remove dead code (Ruben Bridgewater) [#25009](https://github.com/nodejs/node/pull/25009)
- [[`e04e85460f`](https://github.com/nodejs/node/commit/e04e85460f)] - **test**: use blocks instead of async IIFE (Anna Henningsen) [#24989](https://github.com/nodejs/node/pull/24989)
- [[`eb9e6e6576`](https://github.com/nodejs/node/commit/eb9e6e6576)] - **test**: adding history regression test case (Anto Aravinth) [#24843](https://github.com/nodejs/node/pull/24843)
- [[`ac919efbaf`](https://github.com/nodejs/node/commit/ac919efbaf)] - **test**: mark test-child-process-execfile flaky (Rich Trott) [#25051](https://github.com/nodejs/node/pull/25051)
- [[`1e3fb0ae03`](https://github.com/nodejs/node/commit/1e3fb0ae03)] - **test**: mark test-child-process-exit-code flaky (Rich Trott) [#25050](https://github.com/nodejs/node/pull/25050)
- [[`7e0dbc6e01`](https://github.com/nodejs/node/commit/7e0dbc6e01)] - **test**: improve WPT runner name matching (Joyee Cheung) [#24826](https://github.com/nodejs/node/pull/24826)
- [[`da984be0a3`](https://github.com/nodejs/node/commit/da984be0a3)] - **test**: remove reference to whatwg in file names under test/wpt (Joyee Cheung) [#24826](https://github.com/nodejs/node/pull/24826)
- [[`282589456c`](https://github.com/nodejs/node/commit/282589456c)] - **test**: mark test-worker-memory flaky on Windows CI (Rich Trott) [#25042](https://github.com/nodejs/node/pull/25042)
- [[`9bd42671c9`](https://github.com/nodejs/node/commit/9bd42671c9)] - **test**: mark test-cli-node-options flaky on arm (Rich Trott) [#25032](https://github.com/nodejs/node/pull/25032)
- [[`a4ef54a0a6`](https://github.com/nodejs/node/commit/a4ef54a0a6)] - **test**: mark test-child-process-execsync flaky on AIX (Rich Trott) [#25031](https://github.com/nodejs/node/pull/25031)
- [[`900a412f3f`](https://github.com/nodejs/node/commit/900a412f3f)] - **test**: increase error information in test-cli-syntax-\* (Rich Trott) [#25021](https://github.com/nodejs/node/pull/25021)
- [[`d5b0ce15d3`](https://github.com/nodejs/node/commit/d5b0ce15d3)] - **test**: refactor test-enable-in-init (Mitch Hankins) [#24976](https://github.com/nodejs/node/pull/24976)
- [[`649a7289dc`](https://github.com/nodejs/node/commit/649a7289dc)] - **test**: from functools import reduce in test/testpy/\_\_init\_\_.py (cclauss) [#24954](https://github.com/nodejs/node/pull/24954)
- [[`d366676cc5`](https://github.com/nodejs/node/commit/d366676cc5)] - **test**: split test-cli-syntax into multiple tests (Rich Trott) [#24922](https://github.com/nodejs/node/pull/24922)
- [[`e61bbda85d`](https://github.com/nodejs/node/commit/e61bbda85d)] - **test**: improve internet/test-dns (Ilarion Halushka) [#24927](https://github.com/nodejs/node/pull/24927)
- [[`016e35210c`](https://github.com/nodejs/node/commit/016e35210c)] - **(SEMVER-MINOR)** **test**: test TLS client authentication (Sam Roberts) [#24733](https://github.com/nodejs/node/pull/24733)
- [[`e050a5756f`](https://github.com/nodejs/node/commit/e050a5756f)] - **test**: replace callback with arrows (Shubham Urkade) [#24866](https://github.com/nodejs/node/pull/24866)
- [[`22b6befa14`](https://github.com/nodejs/node/commit/22b6befa14)] - **test**: mark test-cli-syntax as flaky/unreliable (Rich Trott) [#24957](https://github.com/nodejs/node/pull/24957)
- [[`56fd127ef0`](https://github.com/nodejs/node/commit/56fd127ef0)] - **test**: do not lint macros files (again) (cclauss) [#24886](https://github.com/nodejs/node/pull/24886)
- [[`bc71e9e0d6`](https://github.com/nodejs/node/commit/bc71e9e0d6)] - **test**: prepare test/pseudo-tty/testcfg.py Python 3 (cclauss) [#24887](https://github.com/nodejs/node/pull/24887)
- [[`f41443cc5c`](https://github.com/nodejs/node/commit/f41443cc5c)] - **test**: move test-cli-syntax to sequential (Rich Trott) [#24907](https://github.com/nodejs/node/pull/24907)
- [[`592bad1b0b`](https://github.com/nodejs/node/commit/592bad1b0b)] - **test**: move http2 test to parallel (Rich Trott) [#24877](https://github.com/nodejs/node/pull/24877)
- [[`91ce957037`](https://github.com/nodejs/node/commit/91ce957037)] - **test**: make http2 timeout test robust (Rich Trott) [#24877](https://github.com/nodejs/node/pull/24877)
- [[`3d87688fba`](https://github.com/nodejs/node/commit/3d87688fba)] - **test**: fix wrong parameter (zhmushan) [#24844](https://github.com/nodejs/node/pull/24844)
- [[`6db760c231`](https://github.com/nodejs/node/commit/6db760c231)] - **test**: improve test-net-socket-timeout (Rich Trott) [#24859](https://github.com/nodejs/node/pull/24859)
- [[`526ff1d1d2`](https://github.com/nodejs/node/commit/526ff1d1d2)] - **test**: prepare test/pseudo-tty/testcfg.py for Python 3 (cclauss) [#24791](https://github.com/nodejs/node/pull/24791)
- [[`a5c57861a9`](https://github.com/nodejs/node/commit/a5c57861a9)] - **test**: refactor test-fs-write-file-sync.js (cjihrig) [#24834](https://github.com/nodejs/node/pull/24834)
- [[`a5c8af7af4`](https://github.com/nodejs/node/commit/a5c8af7af4)] - **test**: prepare test/message/testcfg.py for Python 3 (cclauss) [#24793](https://github.com/nodejs/node/pull/24793)
- [[`390e050ae0`](https://github.com/nodejs/node/commit/390e050ae0)] - **(SEMVER-MINOR)** **tls**: support "BEGIN TRUSTED CERTIFICATE" for ca: (Sam Roberts) [#24733](https://github.com/nodejs/node/pull/24733)
- [[`16a75beffc`](https://github.com/nodejs/node/commit/16a75beffc)] - **tools**: prepare ./tools/compress_json.py for Python 3 (cclauss) [#24889](https://github.com/nodejs/node/pull/24889)
- [[`b60808a2da`](https://github.com/nodejs/node/commit/b60808a2da)] - **tools**: prepare tools/testp.py for Python 3 (cclauss) [#24890](https://github.com/nodejs/node/pull/24890)
- [[`1f61c89a7f`](https://github.com/nodejs/node/commit/1f61c89a7f)] - **tools**: prepare tools/icu/icutrim.py for Python 3 (cclauss) [#24888](https://github.com/nodejs/node/pull/24888)
- [[`e140d41789`](https://github.com/nodejs/node/commit/e140d41789)] - **tools**: capitalize sentences (Ruben Bridgewater) [#24808](https://github.com/nodejs/node/pull/24808)
- [[`ad6104dbac`](https://github.com/nodejs/node/commit/ad6104dbac)] - **tools**: update ESLint to 5.10.0 (cjihrig) [#24903](https://github.com/nodejs/node/pull/24903)
- [[`ac46e27714`](https://github.com/nodejs/node/commit/ac46e27714)] - **tools**: do not lint tools/inspector_protocol or tools/markupsafe (cclauss) [#24882](https://github.com/nodejs/node/pull/24882)
- [[`c3dda00e48`](https://github.com/nodejs/node/commit/c3dda00e48)] - **tools**: prepare tools/js2c.py for Python 3 (cclauss) [#24798](https://github.com/nodejs/node/pull/24798)
- [[`7cac76cdd5`](https://github.com/nodejs/node/commit/7cac76cdd5)] - **tools**: prepare tools/specialize_node_d.py for Python 3 (cclauss) [#24797](https://github.com/nodejs/node/pull/24797)
- [[`15632c3867`](https://github.com/nodejs/node/commit/15632c3867)] - **tools**: prepare tools/test.py for Python 3 (cclauss) [#24799](https://github.com/nodejs/node/pull/24799)
- [[`022599c0e1`](https://github.com/nodejs/node/commit/022599c0e1)] - **tools**: prepare tools/genv8constants.py for Python 3 (cclauss) [#24801](https://github.com/nodejs/node/pull/24801)
- [[`e7b77ead74`](https://github.com/nodejs/node/commit/e7b77ead74)] - **url**: remove an eslint-disable comment (cjihrig) [#24995](https://github.com/nodejs/node/pull/24995)
- [[`59317470e3`](https://github.com/nodejs/node/commit/59317470e3)] - **util**: inspect all prototypes (Ruben Bridgewater) [#24974](https://github.com/nodejs/node/pull/24974)
- [[`a1f0da1d40`](https://github.com/nodejs/node/commit/a1f0da1d40)] - **util**: remove todo (Ruben Bridgewater) [#24982](https://github.com/nodejs/node/pull/24982)
- [[`117e99121c`](https://github.com/nodejs/node/commit/117e99121c)] - **(SEMVER-MINOR)** **util**: add inspection getter option (Ruben Bridgewater) [#24852](https://github.com/nodejs/node/pull/24852)
- [[`331f6044b9`](https://github.com/nodejs/node/commit/331f6044b9)] - **worker**: drain messages from internal message port (Yael Hermon) [#24932](https://github.com/nodejs/node/pull/24932)

Windows 32-bit Installer: https://nodejs.org/dist/v11.5.0/node-v11.5.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v11.5.0/node-v11.5.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v11.5.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v11.5.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v11.5.0/node-v11.5.0.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v11.5.0/node-v11.5.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v11.5.0/node-v11.5.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v11.5.0/node-v11.5.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v11.5.0/node-v11.5.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v11.5.0/node-v11.5.0-aix-ppc64.tar.gz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v11.5.0/node-v11.5.0-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v11.5.0/node-v11.5.0-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v11.5.0/node-v11.5.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v11.5.0/node-v11.5.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v11.5.0/node-v11.5.0.tar.gz \
Other release files: https://nodejs.org/dist/v11.5.0/ \
Documentation: https://nodejs.org/docs/v11.5.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

f1eaef8d20fd107ee716cca51d35c3588400cd97f6d67ef5d1075a1cbd5344fd  node-v11.5.0-aix-ppc64.tar.gz
741abd58ac67b4bf8d2ef991a7487ea17e421b2248688b93be0d2f34886c6aa2  node-v11.5.0-darwin-x64.tar.gz
4b7b63a4b5e0a9d87df72ba8f5403feac3165db2d84a5d510f0529060f48d361  node-v11.5.0-darwin-x64.tar.xz
fa6b4f1529f52fc96a20f51b0de9901733a08450d85ec205e7b6601f0707c459  node-v11.5.0-headers.tar.gz
4f73abb9889fd0d90320be6d58975013ea3ed8e2ae612c0ea2c1a37312d2ff62  node-v11.5.0-headers.tar.xz
fb4fe801f1e69ddde351a72ae8c6bfe61f017c607e69db0a2464264181142b5e  node-v11.5.0-linux-arm64.tar.gz
3f1436c1de70b2e34f7a63a5af7e6b106a8ebe9289b3f7d045ac4a4856570164  node-v11.5.0-linux-arm64.tar.xz
2284b18fd27382ec3a653f161edb3a9d25cbad0777e52855f6be7f9e4a38652c  node-v11.5.0-linux-armv6l.tar.gz
8e689b78530d66f550647b2d8aea18a81f8f5d1a80d0f66a8327720c4f6077bb  node-v11.5.0-linux-armv6l.tar.xz
3373f23701d3095b2024252fb1400ba3498984fd3b534c9df7e1f76a596071d9  node-v11.5.0-linux-armv7l.tar.gz
0760a90bba3bab692891e678f8dc0ae30439b0553222e7a5dd14ce232aa8f68b  node-v11.5.0-linux-armv7l.tar.xz
f4b65c7b821ef6861ee5abd98d24efe17fec0db2b79e0533c4ddaf6b7c0ac438  node-v11.5.0-linux-ppc64le.tar.gz
478fcd530aed46b574ea00dd97aa5340550e6486ad2cb737377822cd4cd8404e  node-v11.5.0-linux-ppc64le.tar.xz
653bd3a7bdaf2ecea91bfd1a98eec230113eadba94b33d39c2fc39c82257079c  node-v11.5.0-linux-s390x.tar.gz
4b80c04e8e49052ef866fcbf9dd699f0fd12b706fe493ce5e295d20a2df4aba6  node-v11.5.0-linux-s390x.tar.xz
17775508531fd1d47db6e905c039cb563a65943f9d7c847f70380f87d8ae9675  node-v11.5.0-linux-x64.tar.gz
ada54407b505b7e6f516c753f0e49220917dd11efa5ee892d3252bdd65d4a54c  node-v11.5.0-linux-x64.tar.xz
76f9c5b5eac1d1cf52cdac8b156e5aa6c798b32f324b959808e61bf029f95a57  node-v11.5.0.pkg
45137fbef7f0ac926cdbed3432427c8d39a2fb3558d1c18841e483d4f2335cb3  node-v11.5.0-sunos-x64.tar.gz
79c6236e46daf642f7d6d52f48f534c1823379ef464d6d91413a776f8eb1fc75  node-v11.5.0-sunos-x64.tar.xz
9dc66232aa6584508afe744dc2908dc1d49e41105fc8aac1547460f7e5467683  node-v11.5.0.tar.gz
dd254333bdfc3d85e2380fd00cc5843abe1ca0128e47e5236a8dcdcf11bdcd1d  node-v11.5.0.tar.xz
b11459745e420b3147fd51be6d026ba431c7f3c5b6bbcc0261fdff5bf547565c  node-v11.5.0-win-x64.7z
3692939b1bbd7ee8b0a967429eef6b1d45078c2f4fc289aae261ca5bde5a0607  node-v11.5.0-win-x64.zip
838b75c304a24296d14be9585dc216e7f67f63856c364514a53264d8a4a9c6aa  node-v11.5.0-win-x86.7z
3a885a0d21c2952f079ad3da332dbb130708d68ebf76b4b644c3e3d4ebfdbbe5  node-v11.5.0-win-x86.zip
4681f1e66c8afdf1452f1810ee6d6bd7b5b08aa80b66604cd1eeca79f0e94839  node-v11.5.0-x64.msi
ffb48dbb6f6f8baba3f2bb5d78e9976b4a6199b13f580d63a643dca7665fd76d  node-v11.5.0-x86.msi
90a52fe6c0305d4ed86f9b2ad04f3d44fe2bfb45140eaab5ce4cdfd34f7e49da  win-x64/node.exe
81350bbc5a0a9c5ea414ab908eb47d48f46cb709134873d929e7278628e7398f  win-x64/node.lib
b111033af5d9ffb7439d8b2253451f0b898882e0cfe5e023ab6bd6cd91ec17a1  win-x64/node_pdb.7z
8c84293c2e6eb9115e14a1a95b1bd374f6f1db1ad379bf00aa8b130f15a251e5  win-x64/node_pdb.zip
e6c4cd6afd370d84e9bf09b00c706948fb99f5a005ba6b1d19ad13df7bd0b099  win-x86/node.exe
eb7e61ed7fac0af85327f05e47e088ed71aff83941ea5816d0196f34702b7c0c  win-x86/node.lib
150b10da65e1981fcb340566bd552bbf0f71c4721015c897290d16a3ce663ca7  win-x86/node_pdb.7z
40afe32a48cbd210c4c59e8228a00f10bbc6c5d04f20867ecea491ee67fcd234  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQEzBAEBCAAdFiEETtd49TnjY0x3nIfG1wYoSKGrAFwFAlwZQ30ACgkQ1wYoSKGr
AFyjRwf9HfdM5jQMHaQ7MyMZ1NYmDMB169d+zkhKZKE2IHq0aqO2sq2E4mV2WQ+B
6Pr0Y+gET+R0PNuL4qYk2GEDyMrdvmvXuWfnJubp7uI8BFwjJVwJaeKOaLjhWJUe
6RqGLsg2UtJfIuNB96+rRh03or+m+TGTyCu3wjQVWlqnIMcCS0cpEiYuAyJzii7v
wOz+i1Ye1wHM971YITTWj9Imxix/xbaIcmfXzWgKeQJBce05lMN1sgmx1J3sOgWH
+WLxfITjCPGsBYV6d5LO+zH3yL4YgS+s4sVnKCLISB6qN5yQRQCFnc6UEyGdr8ra
JsAKynxo4VZbkD7O+fAr9HxcvviaDA==
=ArL3
-----END PGP SIGNATURE-----

```
