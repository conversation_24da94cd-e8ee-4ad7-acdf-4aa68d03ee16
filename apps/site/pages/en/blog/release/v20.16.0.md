---
date: '2024-07-24T12:29:52.613Z'
category: release
title: Node v20.16.0 (LTS)
layout: blog-post
author: <PERSON>
---

## 2024-07-24, Version 20.16.0 'Iron' (LTS), @marco-ippolito

### process: add process.getBuiltinModule(id)

`process.getBuiltinModule(id)` provides a way to load built-in modules
in a globally available function. ES Modules that need to support
other environments can use it to conditionally load a Node.js built-in
when it is run in Node.js, without having to deal with the resolution
error that can be thrown by `import` in a non-Node.js environment or
having to use dynamic `import()` which either turns the module into
an asynchronous module, or turns a synchronous API into an asynchronous one.

```mjs
if (globalThis.process?.getBuiltinModule) {
  // Run in Node.js, use the Node.js fs module.
  const fs = globalThis.process.getBuiltinModule('fs');
  // If `require()` is needed to load user-modules, use createRequire()
  const module = globalThis.process.getBuiltinModule('module');
  const require = module.createRequire(import.meta.url);
  const foo = require('foo');
}
```

If `id` specifies a built-in module available in the current Node.js process,
`process.getBuiltinModule(id)` method returns the corresponding built-in
module. If `id` does not correspond to any built-in module, `undefined`
is returned.

`process.getBuiltinModule(id)` accepts built-in module IDs that are recognized
by `module.isBuiltin(id)`.

The references returned by `process.getBuiltinModule(id)` always point to
the built-in module corresponding to `id` even if users modify
`require.cache` so that `require(id)` returns something else.

Contributed by Joyee Cheung in [#52762](https://github.com/nodejs/node/pull/52762)

### doc: doc-only deprecate OpenSSL engine-based APIs

OpenSSL 3 deprecated support for custom engines with a recommendation to switch to its new provider model.
The `clientCertEngine` option for `https.request()`, `tls.createSecureContext()`, and `tls.createServer()`; the `privateKeyEngine` and `privateKeyIdentifier` for `tls.createSecureContext();` and `crypto.setEngine()` all depend on this functionality from OpenSSL.

Contributed by Richard Lau in [#53329](https://github.com/nodejs/node/pull/53329)

### inspector: fix disable async hooks on Debugger.setAsyncCallStackDepth

`Debugger.setAsyncCallStackDepth` was previously calling the enable function by mistake. As a result, when profiling using Chrome DevTools, the async hooks won't be turned off properly after receiving `Debugger.setAsyncCallStackDepth` with depth 0.

Contributed by Joyee Cheung in [#53473](https://github.com/nodejs/node/pull/53473)

### Other Notable Changes

- \[[`09e2191432`](https://github.com/nodejs/node/commit/09e2191432)] - **(SEMVER-MINOR)** **buffer**: add .bytes() method to Blob (Matthew Aitken) [#53221](https://github.com/nodejs/node/pull/53221)
- \[[`394e00f41c`](https://github.com/nodejs/node/commit/394e00f41c)] - **(SEMVER-MINOR)** **doc**: add context.assert docs (Colin Ihrig) [#53169](https://github.com/nodejs/node/pull/53169)
- \[[`a8601efa5e`](https://github.com/nodejs/node/commit/a8601efa5e)] - **(SEMVER-MINOR)** **doc**: improve explanation about built-in modules (Joyee Cheung) [#52762](https://github.com/nodejs/node/pull/52762)
- \[[`5e76c258f7`](https://github.com/nodejs/node/commit/5e76c258f7)] - **doc**: add StefanStojanovic to collaborators (StefanStojanovic) [#53118](https://github.com/nodejs/node/pull/53118)
- \[[`5e694026f1`](https://github.com/nodejs/node/commit/5e694026f1)] - **doc**: add Marco Ippolito to TSC (Rafael Gonzaga) [#53008](https://github.com/nodejs/node/pull/53008)
- \[[`f3ba1eb72f`](https://github.com/nodejs/node/commit/f3ba1eb72f)] - **(SEMVER-MINOR)** **net**: add new net.server.listen tracing channel (Paolo Insogna) [#53136](https://github.com/nodejs/node/pull/53136)
- \[[`2bcce3255b`](https://github.com/nodejs/node/commit/2bcce3255b)] - **(SEMVER-MINOR)** **src,permission**: --allow-wasi & prevent WASI exec (Rafael Gonzaga) [#53124](https://github.com/nodejs/node/pull/53124)
- \[[`a03a4c7bdd`](https://github.com/nodejs/node/commit/a03a4c7bdd)] - **(SEMVER-MINOR)** **test_runner**: add context.fullName (Colin Ihrig) [#53169](https://github.com/nodejs/node/pull/53169)
- \[[`69b828f5a5`](https://github.com/nodejs/node/commit/69b828f5a5)] - **(SEMVER-MINOR)** **util**: support `--no-` for argument with boolean type for parseArgs (Zhenwei Jin) [#53107](https://github.com/nodejs/node/pull/53107)

### Commits

- \[[`76fd0ea92e`](https://github.com/nodejs/node/commit/76fd0ea92e)] - **assert,util**: correct comparison when both contain same reference (Daniel Lemire) [#53431](https://github.com/nodejs/node/pull/53431)
- \[[`65308b6692`](https://github.com/nodejs/node/commit/65308b6692)] - **benchmark**: fix api restriction for the permission category (Ryan Tsien) [#51528](https://github.com/nodejs/node/pull/51528)
- \[[`1e2bc2c2d0`](https://github.com/nodejs/node/commit/1e2bc2c2d0)] - **benchmark**: fix napi/ref addon (Michaël Zasso) [#53233](https://github.com/nodejs/node/pull/53233)
- \[[`09e2191432`](https://github.com/nodejs/node/commit/09e2191432)] - **(SEMVER-MINOR)** **buffer**: add .bytes() method to Blob (Matthew Aitken) [#53221](https://github.com/nodejs/node/pull/53221)
- \[[`e1951a4804`](https://github.com/nodejs/node/commit/e1951a4804)] - **build**: fix spacing before NINJA_ARGS (jakecastelli) [#53181](https://github.com/nodejs/node/pull/53181)
- \[[`76f3bb3460`](https://github.com/nodejs/node/commit/76f3bb3460)] - **build**: generate binlog in out directories (Chengzhong Wu) [#53325](https://github.com/nodejs/node/pull/53325)
- \[[`eded0c187b`](https://github.com/nodejs/node/commit/eded0c187b)] - **build**: support python 3.13 (Chengzhong Wu) [#53190](https://github.com/nodejs/node/pull/53190)
- \[[`1e57c67fdb`](https://github.com/nodejs/node/commit/1e57c67fdb)] - **build**: update ruff to v0.4.5 (Yagiz Nizipli) [#53180](https://github.com/nodejs/node/pull/53180)
- \[[`28e71ede63`](https://github.com/nodejs/node/commit/28e71ede63)] - **build**: add `--skip-tests` to `test-ci-js` target (Antoine du Hamel) [#53105](https://github.com/nodejs/node/pull/53105)
- \[[`bb06778a65`](https://github.com/nodejs/node/commit/bb06778a65)] - **build**: fix building embedtest in GN build (Cheng) [#53145](https://github.com/nodejs/node/pull/53145)
- \[[`117ff5f139`](https://github.com/nodejs/node/commit/117ff5f139)] - **build**: use broader detection for 'help' (Aviv Keller) [#53045](https://github.com/nodejs/node/pull/53045)
- \[[`9aa896e7f5`](https://github.com/nodejs/node/commit/9aa896e7f5)] - **build**: fix -j propagation to ninja (Tobias Nießen) [#53088](https://github.com/nodejs/node/pull/53088)
- \[[`acdbc78955`](https://github.com/nodejs/node/commit/acdbc78955)] - **build**: exit on unsupported host OS for Android (Mohammed Keyvanzadeh) [#52882](https://github.com/nodejs/node/pull/52882)
- \[[`bf3d94478e`](https://github.com/nodejs/node/commit/bf3d94478e)] - **build**: fix `--enable-d8` builds (Richard Lau) [#53106](https://github.com/nodejs/node/pull/53106)
- \[[`99da7d7237`](https://github.com/nodejs/node/commit/99da7d7237)] - **build**: set "clang" in config.gypi in GN build (Cheng) [#53004](https://github.com/nodejs/node/pull/53004)
- \[[`9446278f03`](https://github.com/nodejs/node/commit/9446278f03)] - **crypto**: improve GetECGroupBits signature (Tobias Nießen) [#53364](https://github.com/nodejs/node/pull/53364)
- \[[`dc2a4af68d`](https://github.com/nodejs/node/commit/dc2a4af68d)] - **crypto**: fix propagation of "memory limit exceeded" (Tobias Nießen) [#53300](https://github.com/nodejs/node/pull/53300)
- \[[`c5174f5e60`](https://github.com/nodejs/node/commit/c5174f5e60)] - **deps**: update c-ares to v1.31.0 (Node.js GitHub Bot) [#53554](https://github.com/nodejs/node/pull/53554)
- \[[`28e932dc7a`](https://github.com/nodejs/node/commit/28e932dc7a)] - **deps**: update undici to 6.19.2 (Node.js GitHub Bot) [#53468](https://github.com/nodejs/node/pull/53468)
- \[[`e4f9c663c4`](https://github.com/nodejs/node/commit/e4f9c663c4)] - **deps**: update undici to 6.19.1 (Node.js GitHub Bot) [#53468](https://github.com/nodejs/node/pull/53468)
- \[[`171dc50fdc`](https://github.com/nodejs/node/commit/171dc50fdc)] - **deps**: update undici to 6.19.1 (Node.js GitHub Bot) [#53468](https://github.com/nodejs/node/pull/53468)
- \[[`6bb6a9100d`](https://github.com/nodejs/node/commit/6bb6a9100d)] - **deps**: update undici to 6.19.0 (Node.js GitHub Bot) [#53468](https://github.com/nodejs/node/pull/53468)
- \[[`815d71b4cd`](https://github.com/nodejs/node/commit/815d71b4cd)] - **deps**: update acorn-walk to 8.3.3 (Node.js GitHub Bot) [#53466](https://github.com/nodejs/node/pull/53466)
- \[[`8b5f1d765a`](https://github.com/nodejs/node/commit/8b5f1d765a)] - **deps**: update zlib to 1.3.0.1-motley-209717d (Node.js GitHub Bot) [#53156](https://github.com/nodejs/node/pull/53156)
- \[[`fc73da6f50`](https://github.com/nodejs/node/commit/fc73da6f50)] - **deps**: update c-ares to v1.30.0 (Node.js GitHub Bot) [#53416](https://github.com/nodejs/node/pull/53416)
- \[[`a6b803abd6`](https://github.com/nodejs/node/commit/a6b803abd6)] - **deps**: update undici to 6.18.2 (Node.js GitHub Bot) [#53255](https://github.com/nodejs/node/pull/53255)
- \[[`0f235535bb`](https://github.com/nodejs/node/commit/0f235535bb)] - **deps**: update ada to 2.8.0 (Node.js GitHub Bot) [#53254](https://github.com/nodejs/node/pull/53254)
- \[[`63407269a8`](https://github.com/nodejs/node/commit/63407269a8)] - **deps**: update corepack to 0.28.2 (Node.js GitHub Bot) [#53253](https://github.com/nodejs/node/pull/53253)
- \[[`7a126e8773`](https://github.com/nodejs/node/commit/7a126e8773)] - **deps**: update c-ares to 1.29.0 (Node.js GitHub Bot) [#53155](https://github.com/nodejs/node/pull/53155)
- \[[`0c8fcceefa`](https://github.com/nodejs/node/commit/0c8fcceefa)] - **deps**: upgrade npm to 10.8.1 (npm team) [#53207](https://github.com/nodejs/node/pull/53207)
- \[[`23866979f2`](https://github.com/nodejs/node/commit/23866979f2)] - **deps**: update undici to 6.18.1 (Node.js GitHub Bot) [#53073](https://github.com/nodejs/node/pull/53073)
- \[[`4987a00142`](https://github.com/nodejs/node/commit/4987a00142)] - **deps**: update undici to 6.18.0 (Node.js GitHub Bot) [#53073](https://github.com/nodejs/node/pull/53073)
- \[[`af226d0d9c`](https://github.com/nodejs/node/commit/af226d0d9c)] - **deps**: update undici to 6.17.0 (Node.js GitHub Bot) [#53034](https://github.com/nodejs/node/pull/53034)
- \[[`c9c6bf8bfb`](https://github.com/nodejs/node/commit/c9c6bf8bfb)] - **deps**: update undici to 6.16.1 (Node.js GitHub Bot) [#52948](https://github.com/nodejs/node/pull/52948)
- \[[`b32b62d590`](https://github.com/nodejs/node/commit/b32b62d590)] - **deps**: update undici to 6.15.0 (Matthew Aitken) [#52763](https://github.com/nodejs/node/pull/52763)
- \[[`6e6641bea2`](https://github.com/nodejs/node/commit/6e6641bea2)] - **deps**: update googletest to 33af80a (Node.js GitHub Bot) [#53053](https://github.com/nodejs/node/pull/53053)
- \[[`aa96fbe03e`](https://github.com/nodejs/node/commit/aa96fbe03e)] - **deps**: update zlib to 1.3.0.1-motley-4f653ff (Node.js GitHub Bot) [#53052](https://github.com/nodejs/node/pull/53052)
- \[[`ba3310ded5`](https://github.com/nodejs/node/commit/ba3310ded5)] - **deps**: upgrade npm to 10.8.0 (npm team) [#53014](https://github.com/nodejs/node/pull/53014)
- \[[`8537a2aecf`](https://github.com/nodejs/node/commit/8537a2aecf)] - **doc**: recommend not using libuv node-api function (Michael Dawson) [#53521](https://github.com/nodejs/node/pull/53521)
- \[[`c13600f0db`](https://github.com/nodejs/node/commit/c13600f0db)] - **doc**: add additional guidance for PRs to deps (Michael Dawson) [#53499](https://github.com/nodejs/node/pull/53499)
- \[[`7c3edd952e`](https://github.com/nodejs/node/commit/7c3edd952e)] - **doc**: only apply content-visibility on all.html (Filip Skokan) [#53510](https://github.com/nodejs/node/pull/53510)
- \[[`ac5be14ed8`](https://github.com/nodejs/node/commit/ac5be14ed8)] - **doc**: update the description of the return type for options.filter (Zhenwei Jin) [#52742](https://github.com/nodejs/node/pull/52742)
- \[[`cac300e351`](https://github.com/nodejs/node/commit/cac300e351)] - **doc**: remove first timer badge (Aviv Keller) [#53338](https://github.com/nodejs/node/pull/53338)
- \[[`feb61459fd`](https://github.com/nodejs/node/commit/feb61459fd)] - **doc**: add Buffer.from(string) to functions that use buffer pool (Christian Bates-White) [#52801](https://github.com/nodejs/node/pull/52801)
- \[[`9e0a6e938b`](https://github.com/nodejs/node/commit/9e0a6e938b)] - **doc**: add initial text for ambassadors program (Michael Dawson) [#52857](https://github.com/nodejs/node/pull/52857)
- \[[`55ac53cb0b`](https://github.com/nodejs/node/commit/55ac53cb0b)] - **doc**: define more cases for stream event emissions (Aviv Keller) [#53317](https://github.com/nodejs/node/pull/53317)
- \[[`7128e0f9c9`](https://github.com/nodejs/node/commit/7128e0f9c9)] - **doc**: remove mentions of policy model from security info (Aviv Keller) [#53249](https://github.com/nodejs/node/pull/53249)
- \[[`3e290433df`](https://github.com/nodejs/node/commit/3e290433df)] - **doc**: fix mistakes in the module `load` hook api (István Donkó) [#53349](https://github.com/nodejs/node/pull/53349)
- \[[`3445c08144`](https://github.com/nodejs/node/commit/3445c08144)] - **doc**: doc-only deprecate OpenSSL engine-based APIs (Richard Lau) [#53329](https://github.com/nodejs/node/pull/53329)
- \[[`a3e8cda019`](https://github.com/nodejs/node/commit/a3e8cda019)] - **doc**: mark --heap-prof and related flags stable (Joyee Cheung) [#53343](https://github.com/nodejs/node/pull/53343)
- \[[`0b9daaae4d`](https://github.com/nodejs/node/commit/0b9daaae4d)] - **doc**: mark --cpu-prof and related flags stable (Joyee Cheung) [#53343](https://github.com/nodejs/node/pull/53343)
- \[[`daf91834f6`](https://github.com/nodejs/node/commit/daf91834f6)] - **doc**: remove IRC from man page (Tobias Nießen) [#53344](https://github.com/nodejs/node/pull/53344)
- \[[`4246c8fa31`](https://github.com/nodejs/node/commit/4246c8fa31)] - **doc**: fix broken link in `static-analysis.md` (Richard Lau) [#53345](https://github.com/nodejs/node/pull/53345)
- \[[`955b98a0e4`](https://github.com/nodejs/node/commit/955b98a0e4)] - **doc**: remove cases for keys not containing "\*" in PATTERN_KEY_COMPARE (Maarten Zuidhoorn) [#53215](https://github.com/nodejs/node/pull/53215)
- \[[`7832b1815f`](https://github.com/nodejs/node/commit/7832b1815f)] - **doc**: add err param to fs.cp callback (Feng Yu) [#53234](https://github.com/nodejs/node/pull/53234)
- \[[`01533df87f`](https://github.com/nodejs/node/commit/01533df87f)] - **doc**: add `err` param to fs.copyFile callback (Feng Yu) [#53234](https://github.com/nodejs/node/pull/53234)
- \[[`b081bc7d5e`](https://github.com/nodejs/node/commit/b081bc7d5e)] - **doc**: reserve 128 for Electron 32 (Keeley Hammond) [#53203](https://github.com/nodejs/node/pull/53203)
- \[[`6b8460b560`](https://github.com/nodejs/node/commit/6b8460b560)] - **doc**: add note to ninjia build for macOS using -jn flag (jakecastelli) [#53187](https://github.com/nodejs/node/pull/53187)
- \[[`394e00f41c`](https://github.com/nodejs/node/commit/394e00f41c)] - **(SEMVER-MINOR)** **doc**: add context.assert docs (Colin Ihrig) [#53169](https://github.com/nodejs/node/pull/53169)
- \[[`c143d61d0e`](https://github.com/nodejs/node/commit/c143d61d0e)] - **doc**: include ESM import for HTTP (Aviv Keller) [#53165](https://github.com/nodejs/node/pull/53165)
- \[[`a8601efa5e`](https://github.com/nodejs/node/commit/a8601efa5e)] - **(SEMVER-MINOR)** **doc**: improve explanation about built-in modules (Joyee Cheung) [#52762](https://github.com/nodejs/node/pull/52762)
- \[[`560392de3d`](https://github.com/nodejs/node/commit/560392de3d)] - **doc**: fix minor grammar and style issues in SECURITY.md (Rich Trott) [#53168](https://github.com/nodejs/node/pull/53168)
- \[[`9f8e34323d`](https://github.com/nodejs/node/commit/9f8e34323d)] - **doc**: mention pm is not enforced when using fd (Rafael Gonzaga) [#53125](https://github.com/nodejs/node/pull/53125)
- \[[`3ac775b015`](https://github.com/nodejs/node/commit/3ac775b015)] - **doc**: fix format in `esm.md` (Pop Moore) [#53170](https://github.com/nodejs/node/pull/53170)
- \[[`41b08bdcf7`](https://github.com/nodejs/node/commit/41b08bdcf7)] - **doc**: fix wrong variable name in example of `timers.tick()` (Deokjin Kim) [#53147](https://github.com/nodejs/node/pull/53147)
- \[[`698ea7aa5a`](https://github.com/nodejs/node/commit/698ea7aa5a)] - **doc**: fix wrong function name in example of `context.plan()` (Deokjin Kim) [#53140](https://github.com/nodejs/node/pull/53140)
- \[[`a99359d79d`](https://github.com/nodejs/node/commit/a99359d79d)] - **doc**: add note for windows users and symlinks (Aviv Keller) [#53117](https://github.com/nodejs/node/pull/53117)
- \[[`61ec2af292`](https://github.com/nodejs/node/commit/61ec2af292)] - **doc**: move all TLS-PSK documentation to its section (Alba Mendez) [#35717](https://github.com/nodejs/node/pull/35717)
- \[[`5e76c258f7`](https://github.com/nodejs/node/commit/5e76c258f7)] - **doc**: add StefanStojanovic to collaborators (StefanStojanovic) [#53118](https://github.com/nodejs/node/pull/53118)
- \[[`1dc406ba62`](https://github.com/nodejs/node/commit/1dc406ba62)] - **doc**: improve ninja build for --built-in-modules-path (jakecastelli) [#53007](https://github.com/nodejs/node/pull/53007)
- \[[`2854585662`](https://github.com/nodejs/node/commit/2854585662)] - **doc**: avoid hiding by navigation bar in anchor jumping (Cloyd Lau) [#45131](https://github.com/nodejs/node/pull/45131)
- \[[`3f432f829f`](https://github.com/nodejs/node/commit/3f432f829f)] - **doc**: remove unavailable youtube link in pull requests (Deokjin Kim) [#52982](https://github.com/nodejs/node/pull/52982)
- \[[`5e694026f1`](https://github.com/nodejs/node/commit/5e694026f1)] - **doc**: add Marco Ippolito to TSC (Rafael Gonzaga) [#53008](https://github.com/nodejs/node/pull/53008)
- \[[`231e44043e`](https://github.com/nodejs/node/commit/231e44043e)] - **doc**: add missing supported timer values in `timers.enable()` (Deokjin Kim) [#52969](https://github.com/nodejs/node/pull/52969)
- \[[`b8944f6938`](https://github.com/nodejs/node/commit/b8944f6938)] - **doc, http**: add `rejectNonStandardBodyWrites` option, clear its behaviour (jakecastelli) [#53396](https://github.com/nodejs/node/pull/53396)
- \[[`0354584738`](https://github.com/nodejs/node/commit/0354584738)] - **doc, meta**: organize contributing to Node-API guide (Aviv Keller) [#53243](https://github.com/nodejs/node/pull/53243)
- \[[`9ae3719c4e`](https://github.com/nodejs/node/commit/9ae3719c4e)] - **doc, meta**: use markdown rather than HTML in CONTRIBUTING.md (Aviv Keller) [#53235](https://github.com/nodejs/node/pull/53235)
- \[[`621e073c96`](https://github.com/nodejs/node/commit/621e073c96)] - **fs**: do not crash if the watched file is removed while setting up watch (Matteo Collina) [#53452](https://github.com/nodejs/node/pull/53452)
- \[[`f00ee1c377`](https://github.com/nodejs/node/commit/f00ee1c377)] - **fs**: fix cp dir/non-dir mismatch error messages (Mathis Wiehl) [#53150](https://github.com/nodejs/node/pull/53150)
- \[[`655b960418`](https://github.com/nodejs/node/commit/655b960418)] - **http2**: reject failed http2.connect when used with promisify (ehsankhfr) [#53475](https://github.com/nodejs/node/pull/53475)
- \[[`eb0b68bb29`](https://github.com/nodejs/node/commit/eb0b68bb29)] - **inspector**: fix disable async hooks on Debugger.setAsyncCallStackDepth (Joyee Cheung) [#53473](https://github.com/nodejs/node/pull/53473)
- \[[`1c0b89be4c`](https://github.com/nodejs/node/commit/1c0b89be4c)] - **lib**: fix typo in comment (codediverdev) [#53543](https://github.com/nodejs/node/pull/53543)
- \[[`55922d9cb0`](https://github.com/nodejs/node/commit/55922d9cb0)] - **lib**: remove the unused code (theanarkh) [#53463](https://github.com/nodejs/node/pull/53463)
- \[[`06374ef96b`](https://github.com/nodejs/node/commit/06374ef96b)] - **lib**: fix naming convention of `Symbol` (Deokjin Kim) [#53387](https://github.com/nodejs/node/pull/53387)
- \[[`d1a780039a`](https://github.com/nodejs/node/commit/d1a780039a)] - **lib**: fix timer leak (theanarkh) [#53337](https://github.com/nodejs/node/pull/53337)
- \[[`8689ce4b41`](https://github.com/nodejs/node/commit/8689ce4b41)] - **lib**: fix misleading argument of validateUint32 (Tobias Nießen) [#53307](https://github.com/nodejs/node/pull/53307)
- \[[`57d7bbf624`](https://github.com/nodejs/node/commit/57d7bbf624)] - **lib**: fix the name of the fetch global function (Gabriel Bota) [#53227](https://github.com/nodejs/node/pull/53227)
- \[[`23f086c363`](https://github.com/nodejs/node/commit/23f086c363)] - **lib**: do not call callback if socket is closed (theanarkh) [#52829](https://github.com/nodejs/node/pull/52829)
- \[[`f325c54c80`](https://github.com/nodejs/node/commit/f325c54c80)] - **meta**: use correct source for workflow in PR (Aviv Keller) [#53490](https://github.com/nodejs/node/pull/53490)
- \[[`8172412dbe`](https://github.com/nodejs/node/commit/8172412dbe)] - **meta**: move one or more collaborators to emeritus (Node.js GitHub Bot) [#53480](https://github.com/nodejs/node/pull/53480)
- \[[`01b61d65d3`](https://github.com/nodejs/node/commit/01b61d65d3)] - **meta**: fix typo in dependency updates (Aviv Keller) [#53471](https://github.com/nodejs/node/pull/53471)
- \[[`12f5737cd3`](https://github.com/nodejs/node/commit/12f5737cd3)] - **meta**: bump step-security/harden-runner from 2.7.1 to 2.8.0 (dependabot\[bot]) [#53245](https://github.com/nodejs/node/pull/53245)
- \[[`102e4eee3c`](https://github.com/nodejs/node/commit/102e4eee3c)] - **meta**: bump ossf/scorecard-action from 2.3.1 to 2.3.3 (dependabot\[bot]) [#53248](https://github.com/nodejs/node/pull/53248)
- \[[`5ba185580d`](https://github.com/nodejs/node/commit/5ba185580d)] - **meta**: bump actions/checkout from 4.1.4 to 4.1.6 (dependabot\[bot]) [#53247](https://github.com/nodejs/node/pull/53247)
- \[[`9d186cce2b`](https://github.com/nodejs/node/commit/9d186cce2b)] - **meta**: bump github/codeql-action from 3.25.3 to 3.25.7 (dependabot\[bot]) [#53246](https://github.com/nodejs/node/pull/53246)
- \[[`29ab74009e`](https://github.com/nodejs/node/commit/29ab74009e)] - **meta**: bump codecov/codecov-action from 4.3.1 to 4.4.1 (dependabot\[bot]) [#53244](https://github.com/nodejs/node/pull/53244)
- \[[`bd4b593f30`](https://github.com/nodejs/node/commit/bd4b593f30)] - **meta**: remove `initializeCommand` from devcontainer (Aviv Keller) [#53137](https://github.com/nodejs/node/pull/53137)
- \[[`61b1f573cf`](https://github.com/nodejs/node/commit/61b1f573cf)] - **meta**: move one or more collaborators to emeritus (Node.js GitHub Bot) [#53065](https://github.com/nodejs/node/pull/53065)
- \[[`f3ba1eb72f`](https://github.com/nodejs/node/commit/f3ba1eb72f)] - **(SEMVER-MINOR)** **net**: add new net.server.listen tracing channel (Paolo Insogna) [#53136](https://github.com/nodejs/node/pull/53136)
- \[[`67333a5796`](https://github.com/nodejs/node/commit/67333a5796)] - **(SEMVER-MINOR)** **process**: add process.getBuiltinModule(id) (Joyee Cheung) [#52762](https://github.com/nodejs/node/pull/52762)
- \[[`092aa09eb3`](https://github.com/nodejs/node/commit/092aa09eb3)] - **repl**: fix await object patterns without values (Luke Haas) [#53331](https://github.com/nodejs/node/pull/53331)
- \[[`554d25f526`](https://github.com/nodejs/node/commit/554d25f526)] - **src**: reset `process.versions` during pre-execution (Richard Lau) [#53444](https://github.com/nodejs/node/pull/53444)
- \[[`a0879ad628`](https://github.com/nodejs/node/commit/a0879ad628)] - **src**: fix dynamically linked OpenSSL version (Richard Lau) [#53456](https://github.com/nodejs/node/pull/53456)
- \[[`91c05f34de`](https://github.com/nodejs/node/commit/91c05f34de)] - **src**: remove `SetEncoding` from StringEncoder (Yagiz Nizipli) [#53441](https://github.com/nodejs/node/pull/53441)
- \[[`4f49384be5`](https://github.com/nodejs/node/commit/4f49384be5)] - **src**: fix typo in env.cc (EhsanKhaki) [#53418](https://github.com/nodejs/node/pull/53418)
- \[[`9730d1e186`](https://github.com/nodejs/node/commit/9730d1e186)] - **src**: avoid strcmp in favor of operator== (Tobias Nießen) [#53439](https://github.com/nodejs/node/pull/53439)
- \[[`436ad8ceb9`](https://github.com/nodejs/node/commit/436ad8ceb9)] - **src**: print v8::OOMDetails::detail when it's available (Joyee Cheung) [#53360](https://github.com/nodejs/node/pull/53360)
- \[[`f773b289eb`](https://github.com/nodejs/node/commit/f773b289eb)] - **src**: fix IsIPAddress for IPv6 (Hüseyin Açacak) [#53400](https://github.com/nodejs/node/pull/53400)
- \[[`7705efd860`](https://github.com/nodejs/node/commit/7705efd860)] - **src**: fix permission inspector crash (theanarkh) [#53389](https://github.com/nodejs/node/pull/53389)
- \[[`260d8d9ae1`](https://github.com/nodejs/node/commit/260d8d9ae1)] - **src**: use \_\_FUNCSIG\_\_ on Windows in backtrace (Joyee Cheung) [#53135](https://github.com/nodejs/node/pull/53135)
- \[[`3b79e9c24e`](https://github.com/nodejs/node/commit/3b79e9c24e)] - **src**: fix external module env and kDisableNodeOptionsEnv (Rafael Gonzaga) [#52905](https://github.com/nodejs/node/pull/52905)
- \[[`32839c63cb`](https://github.com/nodejs/node/commit/32839c63cb)] - **src**: reduce unnecessary `GetCwd` calls (Yagiz Nizipli) [#53064](https://github.com/nodejs/node/pull/53064)
- \[[`840dd092ce`](https://github.com/nodejs/node/commit/840dd092ce)] - **src**: improve node::Dotenv declarations (Tobias Nießen) [#52973](https://github.com/nodejs/node/pull/52973)
- \[[`2bcce3255b`](https://github.com/nodejs/node/commit/2bcce3255b)] - **(SEMVER-MINOR)** **src,permission**: --allow-wasi & prevent WASI exec (Rafael Gonzaga) [#53124](https://github.com/nodejs/node/pull/53124)
- \[[`e092c62a22`](https://github.com/nodejs/node/commit/e092c62a22)] - **stream**: update outdated highwatermark doc (Jay Kim) [#53494](https://github.com/nodejs/node/pull/53494)
- \[[`71af3e8172`](https://github.com/nodejs/node/commit/71af3e8172)] - **stream**: support dispose in writable (Benjamin Gruenbaum) [#48547](https://github.com/nodejs/node/pull/48547)
- \[[`33a15be32f`](https://github.com/nodejs/node/commit/33a15be32f)] - **stream**: callback should be called when pendingcb is 0 (jakecastelli) [#53438](https://github.com/nodejs/node/pull/53438)
- \[[`1b46ebbf69`](https://github.com/nodejs/node/commit/1b46ebbf69)] - **stream**: make sure \_destroy is called (jakecastelli) [#53213](https://github.com/nodejs/node/pull/53213)
- \[[`9f95d41947`](https://github.com/nodejs/node/commit/9f95d41947)] - **stream**: prevent stream unexpected pause when highWaterMark set to 0 (jakecastelli) [#53261](https://github.com/nodejs/node/pull/53261)
- \[[`d02651c9d6`](https://github.com/nodejs/node/commit/d02651c9d6)] - **stream**: micro-optimize writable condition (Orgad Shaneh) [#53189](https://github.com/nodejs/node/pull/53189)
- \[[`324070c410`](https://github.com/nodejs/node/commit/324070c410)] - **stream**: fix memory usage regression in writable (Orgad Shaneh) [#53188](https://github.com/nodejs/node/pull/53188)
- \[[`48138afd35`](https://github.com/nodejs/node/commit/48138afd35)] - **stream**: fixes for webstreams (Mattias Buelens) [#51168](https://github.com/nodejs/node/pull/51168)
- \[[`24f078a22b`](https://github.com/nodejs/node/commit/24f078a22b)] - **test**: mark `test-benchmark-crypto` as flaky (Antoine du Hamel) [#52955](https://github.com/nodejs/node/pull/52955)
- \[[`0d69ce3474`](https://github.com/nodejs/node/commit/0d69ce3474)] - **test**: extend env for `test-node-output-errors` (Richard Lau) [#53535](https://github.com/nodejs/node/pull/53535)
- \[[`1aaaad8518`](https://github.com/nodejs/node/commit/1aaaad8518)] - **test**: update encoding web-platform tests (Yagiz Nizipli) [#53477](https://github.com/nodejs/node/pull/53477)
- \[[`54e0ba8771`](https://github.com/nodejs/node/commit/54e0ba8771)] - **test**: check against run-time OpenSSL version (Richard Lau) [#53456](https://github.com/nodejs/node/pull/53456)
- \[[`059e47c320`](https://github.com/nodejs/node/commit/059e47c320)] - **test**: update tests for OpenSSL 3.0.14 (Richard Lau) [#53373](https://github.com/nodejs/node/pull/53373)
- \[[`49e6f33021`](https://github.com/nodejs/node/commit/49e6f33021)] - **test**: fix test-http-server-keepalive-req-gc (Etienne Pierre-doray) [#53292](https://github.com/nodejs/node/pull/53292)
- \[[`292d13a289`](https://github.com/nodejs/node/commit/292d13a289)] - **test**: update TLS tests for OpenSSL 3.2 (Richard Lau) [#53384](https://github.com/nodejs/node/pull/53384)
- \[[`82017c90bb`](https://github.com/nodejs/node/commit/82017c90bb)] - **test**: fix test when compiled without engine support (Richard Lau) [#53232](https://github.com/nodejs/node/pull/53232)
- \[[`a54090b385`](https://github.com/nodejs/node/commit/a54090b385)] - **test**: update TLS trace tests for OpenSSL >= 3.2 (Richard Lau) [#53229](https://github.com/nodejs/node/pull/53229)
- \[[`3a1693421d`](https://github.com/nodejs/node/commit/3a1693421d)] - **test**: fix Windows native test suites (Stefan Stojanovic) [#53173](https://github.com/nodejs/node/pull/53173)
- \[[`2b07d01272`](https://github.com/nodejs/node/commit/2b07d01272)] - **test**: skip `test-setproctitle` when `ps` is not available (Antoine du Hamel) [#53104](https://github.com/nodejs/node/pull/53104)
- \[[`0051d1c83d`](https://github.com/nodejs/node/commit/0051d1c83d)] - **test**: increase allocation so it fails for the test (Adam Majer) [#53099](https://github.com/nodejs/node/pull/53099)
- \[[`048cbe3304`](https://github.com/nodejs/node/commit/048cbe3304)] - **test**: remove timers from test-tls-socket-close (Luigi Pinca) [#53019](https://github.com/nodejs/node/pull/53019)
- \[[`8653d9223e`](https://github.com/nodejs/node/commit/8653d9223e)] - **test**: replace `.substr` with `.slice` (Antoine du Hamel) [#53070](https://github.com/nodejs/node/pull/53070)
- \[[`d74bda4241`](https://github.com/nodejs/node/commit/d74bda4241)] - **test**: add AbortController to knownGlobals (Luigi Pinca) [#53020](https://github.com/nodejs/node/pull/53020)
- \[[`f29e1e9838`](https://github.com/nodejs/node/commit/f29e1e9838)] - **test**: skip unstable shadow realm gc tests (Chengzhong Wu) [#52855](https://github.com/nodejs/node/pull/52855)
- \[[`dfa498697e`](https://github.com/nodejs/node/commit/dfa498697e)] - **test,doc**: enable running embedtest for Windows (Vladimir Morozov) [#52646](https://github.com/nodejs/node/pull/52646)
- \[[`0381817f1d`](https://github.com/nodejs/node/commit/0381817f1d)] - **test_runner**: calculate executed lines using source map (Moshe Atlow) [#53315](https://github.com/nodejs/node/pull/53315)
- \[[`9d3699b5b0`](https://github.com/nodejs/node/commit/9d3699b5b0)] - **test_runner**: handle file rename and deletion under watch mode (jakecastelli) [#53114](https://github.com/nodejs/node/pull/53114)
- \[[`9a36258ca0`](https://github.com/nodejs/node/commit/9a36258ca0)] - **test_runner**: refactor to use min/max of `validateInteger` (Deokjin Kim) [#53148](https://github.com/nodejs/node/pull/53148)
- \[[`a03a4c7bdd`](https://github.com/nodejs/node/commit/a03a4c7bdd)] - **(SEMVER-MINOR)** **test_runner**: add context.fullName (Colin Ihrig) [#53169](https://github.com/nodejs/node/pull/53169)
- \[[`a72157077a`](https://github.com/nodejs/node/commit/a72157077a)] - **test_runner**: fix t.assert methods (Colin Ihrig) [#53049](https://github.com/nodejs/node/pull/53049)
- \[[`ba764db9ab`](https://github.com/nodejs/node/commit/ba764db9ab)] - **test_runner**: avoid error when coverage line not found (Moshe Atlow) [#53000](https://github.com/nodejs/node/pull/53000)
- \[[`3a4a0ebd06`](https://github.com/nodejs/node/commit/3a4a0ebd06)] - **test_runner,doc**: align documentation with actual stdout/stderr behavior (Moshe Atlow) [#53131](https://github.com/nodejs/node/pull/53131)
- \[[`6e6646bdd5`](https://github.com/nodejs/node/commit/6e6646bdd5)] - **tls**: check result of SSL_CTX_set\_\*\_proto_version (Tobias Nießen) [#53459](https://github.com/nodejs/node/pull/53459)
- \[[`2aceed4297`](https://github.com/nodejs/node/commit/2aceed4297)] - **tls**: avoid taking ownership of OpenSSL objects (Tobias Nießen) [#53436](https://github.com/nodejs/node/pull/53436)
- \[[`faa5cac18c`](https://github.com/nodejs/node/commit/faa5cac18c)] - **tls**: use SSL_get_peer_tmp_key (Tobias Nießen) [#53366](https://github.com/nodejs/node/pull/53366)
- \[[`68fcbb635e`](https://github.com/nodejs/node/commit/68fcbb635e)] - **tls**: fix negative sessionTimeout handling (Tobias Nießen) [#53002](https://github.com/nodejs/node/pull/53002)
- \[[`61a1c43ef1`](https://github.com/nodejs/node/commit/61a1c43ef1)] - **tools**: fix skip detection of test runner output (Richard Lau) [#53545](https://github.com/nodejs/node/pull/53545)
- \[[`53a7b6e1c0`](https://github.com/nodejs/node/commit/53a7b6e1c0)] - **tools**: fix c-ares update script (Marco Ippolito) [#53414](https://github.com/nodejs/node/pull/53414)
- \[[`3bd5f46a15`](https://github.com/nodejs/node/commit/3bd5f46a15)] - **tools**: update lint-md-dependencies (Node.js GitHub Bot) [#53158](https://github.com/nodejs/node/pull/53158)
- \[[`daab9e170f`](https://github.com/nodejs/node/commit/daab9e170f)] - **tools**: do not run Corepack code before it's reviewed (Antoine du Hamel) [#53405](https://github.com/nodejs/node/pull/53405)
- \[[`d18a67f937`](https://github.com/nodejs/node/commit/d18a67f937)] - **tools**: use Ubuntu 24.04 and Clang on GitHub actions (Michaël Zasso) [#53212](https://github.com/nodejs/node/pull/53212)
- \[[`e9b7a52848`](https://github.com/nodejs/node/commit/e9b7a52848)] - **tools**: add stream label on PR when related files being changed in lib (jakecastelli) [#53269](https://github.com/nodejs/node/pull/53269)
- \[[`04d78dd56d`](https://github.com/nodejs/node/commit/04d78dd56d)] - **tools**: remove no-goma arg from make-v8 script (Michaël Zasso) [#53336](https://github.com/nodejs/node/pull/53336)
- \[[`37e725a500`](https://github.com/nodejs/node/commit/37e725a500)] - **tools**: use sccache Github action (Moshe Atlow) [#53316](https://github.com/nodejs/node/pull/53316)
- \[[`2a1fde7e32`](https://github.com/nodejs/node/commit/2a1fde7e32)] - **tools**: update error message for Type Error (Aviv Keller) [#53047](https://github.com/nodejs/node/pull/53047)
- \[[`8f5fb4192d`](https://github.com/nodejs/node/commit/8f5fb4192d)] - _**Revert**_ "**tools**: add --certify-safe to nci-ci" (Antoine du Hamel) [#53098](https://github.com/nodejs/node/pull/53098)
- \[[`69b828f5a5`](https://github.com/nodejs/node/commit/69b828f5a5)] - **(SEMVER-MINOR)** **util**: support `--no-` for argument with boolean type for parseArgs (Zhenwei Jin) [#53107](https://github.com/nodejs/node/pull/53107)
- \[[`1a2f3ab4f5`](https://github.com/nodejs/node/commit/1a2f3ab4f5)] - **watch**: fix variable naming (jakecastelli) [#53101](https://github.com/nodejs/node/pull/53101)

Windows 32-bit Installer: https://nodejs.org/dist/v20.16.0/node-v20.16.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v20.16.0/node-v20.16.0-x64.msi \
Windows ARM 64-bit Installer: https://nodejs.org/dist/v20.16.0/node-v20.16.0-arm64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v20.16.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v20.16.0/win-x64/node.exe \
Windows ARM 64-bit Binary: https://nodejs.org/dist/v20.16.0/win-arm64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v20.16.0/node-v20.16.0.pkg \
macOS Apple Silicon 64-bit Binary: https://nodejs.org/dist/v20.16.0/node-v20.16.0-darwin-arm64.tar.gz \
macOS Intel 64-bit Binary: https://nodejs.org/dist/v20.16.0/node-v20.16.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v20.16.0/node-v20.16.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v20.16.0/node-v20.16.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v20.16.0/node-v20.16.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v20.16.0/node-v20.16.0-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v20.16.0/node-v20.16.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v20.16.0/node-v20.16.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v20.16.0/node-v20.16.0.tar.gz \
Other release files: https://nodejs.org/dist/v20.16.0/ \
Documentation: https://nodejs.org/docs/v20.16.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

5eb1b7ea405c86be0a21ec3850997c89df238d6e4659a0b990aa793a8cbfd9cf  node-v20.16.0-aix-ppc64.tar.gz
f366fe5903dcb3b6cd495c8add77c87a32772085718a672d52ad17d9d91d2018  node-v20.16.0-arm64.msi
fc7355e778b181575153b7dea4879e8021776eeb376c43c50f65893d2ea70aa3  node-v20.16.0-darwin-arm64.tar.gz
5043e98cdf859963b1a0aff54c1f1813a2a8059e4179403171860d664ca090f2  node-v20.16.0-darwin-arm64.tar.xz
e18942cd706e4d69a4845ddacee2f1c17a72e853a229e3d2623d2edeb7efde72  node-v20.16.0-darwin-x64.tar.gz
9df751ac5edbb2181335200060dff14de25f828eaed70d8b48459d2c203aeedc  node-v20.16.0-darwin-x64.tar.xz
6cc5690a67b9b1e1fa8cedaeca41f1bdb5e1af1f7948761c798d33d99f789a5c  node-v20.16.0-headers.tar.gz
a1464c304980d3ab41922cda7025ebc2ec0dc2a0b89d9b9183c589560810feaa  node-v20.16.0-headers.tar.xz
551588f8f5ca05c04efb53f1b2bb7d9834603327bdc82d60a944d385569866e1  node-v20.16.0-linux-arm64.tar.gz
1d9929e72f692179f884cd676b2dfabd879cb77defa7869dc8cfc802619277fb  node-v20.16.0-linux-arm64.tar.xz
1c77c52ab507ddee479012f0b4bf523dd8400df4504447d623632353076e2e27  node-v20.16.0-linux-armv7l.tar.gz
a23a49029e8c7788c701eb3ace553260b7676a5a2ea9965ba92e4817008fbefe  node-v20.16.0-linux-armv7l.tar.xz
80b515595e46afb9bae77f61083a4ca7c21bbdb627f69ff53fd5dca3a26773fb  node-v20.16.0-linux-ppc64le.tar.gz
86cf6e8c93a9e517bfcfdfb4ad2774105312679ad21e03da75ab516ebc10e2dc  node-v20.16.0-linux-ppc64le.tar.xz
ae7a9f6e631a0bede76a501d8b1d806f56b97acfa5a1d6833bab5ce90a404e5e  node-v20.16.0-linux-s390x.tar.gz
6c38ac5c516a6a36ee6e0426975e6466795db30b9ced04e59f0f33fe6b3d657e  node-v20.16.0-linux-s390x.tar.xz
b3f874ea84e440d69ed02ca92429d0eccd17737fde86db69c1c153d16ec654f2  node-v20.16.0-linux-x64.tar.gz
c30af7dfea46de7d8b9b370fa33b8b15440bc93f0a686af8601bbb48b82f16c0  node-v20.16.0-linux-x64.tar.xz
55852a420ca41db9f128f97e0dd8751199c23d63f5a7978432fd7c9e0c74c323  node-v20.16.0.pkg
8f24bf9abe455a09ab30f9ae8edda1e945ed678a4b1c3b07ee0f901fdc0ff4fd  node-v20.16.0.tar.gz
cd6c8fc3ff2606aadbc7155db6f7e77247d2d0065ac18e2f7f049095584b8b46  node-v20.16.0.tar.xz
52e5666a379acd8533d9ccab66c2321a6ffc83766248419bfbd41ba8bc071244  node-v20.16.0-win-arm64.7z
af5a85ea299fcebd34c3c726a47a926e73171f9b657a6eaa796c011597241bf8  node-v20.16.0-win-arm64.zip
1b3961054a484476872715d9ca04bc491d797fde6336db514b6e6fcbb71fae9d  node-v20.16.0-win-x64.7z
4e88373ac5ae859ad4d50cc3c5fa86eb3178d089b72e64c4dbe6eeac5d7b5979  node-v20.16.0-win-x64.zip
76f1806fde0b09ed4044f29ea140fb2bea9bce745b9892ec4aeb6537344db6f1  node-v20.16.0-win-x86.7z
1adc1f086595ecbc98da40eccb42fa1691b6c6c0658ff875dda19e4e02b1d5f0  node-v20.16.0-win-x86.zip
813306c94e6f5f061a5789f037d48f57d52240284a679e5ace4a0f73f8f2feeb  node-v20.16.0-x64.msi
2bb8c3084384c95c47c4191c38098d5ecf55c0f02c1de5e0968730dec957ea15  node-v20.16.0-x86.msi
7e773fba3a19eac5ccbe85c1f87a05d7b112ecf41440076e6b6de1c7bffa0fdf  win-arm64/node.exe
a4f01329c1c211082ac3ed387ff6651530040bbf7250ec419ce8f95b10d7804a  win-arm64/node.lib
e1bec70ae9529cc637a21de850c070125f8016070451094d72f96408001200a2  win-arm64/node_pdb.7z
bc5b60eecd3b6c92b35755adef2e4aad01e021a3c434d46c2555a49056c5bcf7  win-arm64/node_pdb.zip
ba221658a3b68bd583e3068903eb675b5206d86a883c084ed95502e8f634b82a  win-x64/node.exe
87056190b7cd06f40058f8e059efd328cdcc7600b825afa102c0aa5039865af5  win-x64/node.lib
bf2ad1e1f4e7c3853d5209fe9ef24ad7117edafc71f6401ec0121d8b681b8c3c  win-x64/node_pdb.7z
5386f3c3af1af1b325b43b574043c5a7e830b3e9e7df0370ae0797ce4f39b375  win-x64/node_pdb.zip
b7b8d6b5fdd1c073b6f5f6d15bc849f4b5f92c4a66f23e77294f4bdf5f51e9f6  win-x86/node.exe
fa02ae7feca7eb6c4a0f1b929126df400719f5d18a2ec4b7d12c52fbe0b13814  win-x86/node.lib
328b2dcc91255c1c75faa8ce7eb687a8960ae09555d3bca0ae8e0dac4238c873  win-x86/node_pdb.7z
71b1e6b75c61227342ba6f1edb9014445dbee857d6cb14dce3d9b8d94c694d55  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEzGj1oxBv9EgyLkjtJ/XjjVsKIV8FAmag7tcACgkQJ/XjjVsK
IV/hlRAAjvdBRTWPfjXzTqxQODXLZps1HREXRZAa8C8bbAoagCJ1jfm6d8yVUegH
Bl5FqDAutGfTlEhXtQqBmnbQPv4Ahj156cVYtp3dFrxPF15bP+o9q+Un5+R1zcfX
kH9W26G7IvfrtFJkDClpBlPKYE5HcDrrJBNfvALf4th17bkVHMpr04oJz5IwGV2M
petLMwqFxcqQ+15tzRW42Z6EhWHvNaMveab6SM4JEqBxvqB8K+m4nsw2ER7ycU5b
Isa0bUsxtRICtSX0yzzdzEYrFXZmb9eXZRVfJ4sBpUhw0xtBmHn3c1MZH0qez+Nm
tbc6pcgGv9cUSXauBeD8rrYMzQHcrhihd51i9a3Cen3RDy/dtuNx9jEXnxfkY+n9
wkwKb4Lask962L+yTHQCfJ+JQxgouADxqzMxhcup1iiHXCd7pSBSoeAvd5Z1AeGX
qBYrLU9mcyIuLrbtADSfnWmXWs2k1hgnP3UXMBhu/GuobQf9kJ2Gwwx5Gp0aB8z9
4EA+oUXnkM2kJF0MYVMXL+z8VcQpHgyVPujglhNn/a4WdCVTr1jKptNqqnriH9zl
bHMZuiKbAt8RL9rQ3XuFD1sN9k1z/mj8bCHES2WVta+3kCmY9u+eKXNdXJYt+5Xh
bGxwXP5T+Z8Yzc9FVmgPzZzVddCX74Yug0j8BUyE3vPaDq32H6M=
=cZH5
-----END PGP SIGNATURE-----
```
