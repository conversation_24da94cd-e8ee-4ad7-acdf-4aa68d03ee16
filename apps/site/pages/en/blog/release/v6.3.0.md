---
date: '2016-07-06T18:02:20.335Z'
category: release
title: Node v6.3.0 (Current)
layout: blog-post
author: <PERSON>
---

### Notable changes

- **buffer**: Added `buffer.swap64()` to complement `swap16()` & `swap32()`. (<PERSON>) [#7157](https://github.com/nodejs/node/pull/7157)
- **build**: New `configure` options have been added for building Node.js as a shared library. (<PERSON>) [#6994](https://github.com/nodejs/node/pull/6994)
  - The options are: `--shared`, `--without-v8-platform` & `--without-bundled-v8`.
- **crypto**: Root certificates have been updated. (<PERSON>) [#7363](https://github.com/nodejs/node/pull/7363)
- **debugger**: The server address is now configurable via `--debug=<address>:<port>`. (<PERSON>) [#3316](https://github.com/nodejs/node/pull/3316)
- **npm**: Upgraded npm to v3.10.3 (<PERSON>) [#7515](https://github.com/nodejs/node/pull/7515) & (Rebecca <PERSON>) [#7410](https://github.com/nodejs/node/pull/7410)
- **readline**: Added the `prompt` option to the readline constructor. (Evan Lucas) [#7125](https://github.com/nodejs/node/pull/7125)
- **repl / vm**: `sigint`/`ctrl+c` will now break out of infinite loops without stopping the Node.js instance. (Anna Henningsen) [#6635](https://github.com/nodejs/node/pull/6635)
- **src**:
  - Added a `node::FreeEnvironment` public C++ API. (Cheng Zhao) [#3098](https://github.com/nodejs/node/pull/3098)
  - Refactored `require('constants')`, constants are now available directly from their respective modules. (James M Snell) [#6534](https://github.com/nodejs/node/pull/6534)
- **stream**: Improved `readable.read()` performance by up to 70%. (Brian White) [#7077](https://github.com/nodejs/node/pull/7077)
- **timers**: `setImmediate()` is now up to 150% faster in some situations. (Andras) [#6436](https://github.com/nodejs/node/pull/6436)
- **util**: Added a `breakLength` option to `util.inspect()` to control how objects are formatted across lines. (cjihrig) [#7499](https://github.com/nodejs/node/pull/7499)
- **v8-inspector**: Experimental support has been added for debugging Node.js over the inspector protocol. (Ali Ijaz Sheikh) [#6792](https://github.com/nodejs/node/pull/6792)
  - **Note: This feature is _experimental_, and it could be altered or removed.**
  - You can try this feature by running Node.js with the `--inspect` flag.

### Commits

- [[`40211e80f2`](https://github.com/nodejs/node/commit/40211e80f2)] - **assert**: remove unneeded arguments special handling (Rich Trott) [#7413](https://github.com/nodejs/node/pull/7413)
- [[`44f0f940c8`](https://github.com/nodejs/node/commit/44f0f940c8)] - **benchmark**: add `setImmediate()` benchmarks (Andras) [#6436](https://github.com/nodejs/node/pull/6436)
- [[`35c70b5668`](https://github.com/nodejs/node/commit/35c70b5668)] - **benchmark**: `util._extend` vs `object.assign` (surya panikkal) [#7255](https://github.com/nodejs/node/pull/7255)
- [[`4014ecbfb4`](https://github.com/nodejs/node/commit/4014ecbfb4)] - **(SEMVER-MINOR)** **buffer**: speed up swap16/32, add swap64 (Zach Bjornson) [#7157](https://github.com/nodejs/node/pull/7157)
- [[`ac8e1bf609`](https://github.com/nodejs/node/commit/ac8e1bf609)] - **buffer**: improve creation performance. (Ingvar Stepanyan) [#7349](https://github.com/nodejs/node/pull/7349)
- [[`5e4113e58d`](https://github.com/nodejs/node/commit/5e4113e58d)] - **build**: Fix compile failure in `backtrace_posix.cc` (Michael Dawson) [#7544](https://github.com/nodejs/node/pull/7544)
- [[`15a32dd42b`](https://github.com/nodejs/node/commit/15a32dd42b)] - **build**: export openssl symbols on windows (Ben Noordhuis) [#6274](https://github.com/nodejs/node/pull/6274)
- [[`b829a4969f`](https://github.com/nodejs/node/commit/b829a4969f)] - **build**: enable big toc for release builds in AIX (Gireesh Punathil) [#7508](https://github.com/nodejs/node/pull/7508)
- [[`9b5be44b01`](https://github.com/nodejs/node/commit/9b5be44b01)] - **build**: split CI rules in Makefile (João Reis) [#7317](https://github.com/nodejs/node/pull/7317)
- [[`1bd6a623a0`](https://github.com/nodejs/node/commit/1bd6a623a0)] - **build**: drop unconditional openssl dep from cctest (Ben Noordhuis) [#7486](https://github.com/nodejs/node/pull/7486)
- [[`8a31b234ee`](https://github.com/nodejs/node/commit/8a31b234ee)] - **build**: remove unused files from `CPPLINT_FILES` (Ben Noordhuis) [#7462](https://github.com/nodejs/node/pull/7462)
- [[`91744aaf00`](https://github.com/nodejs/node/commit/91744aaf00)] - **build**: configure `--shared` (Stefan Budeanu) [#6994](https://github.com/nodejs/node/pull/6994)
- [[`2aa06b9fa0`](https://github.com/nodejs/node/commit/2aa06b9fa0)] - **child_process**: preserve argument type (Rich Trott) [#7391](https://github.com/nodejs/node/pull/7391)
- [[`4a0fb6fcb8`](https://github.com/nodejs/node/commit/4a0fb6fcb8)] - **_Revert_** "**child_process**: measure buffer length in bytes" (Rich Trott) [#7391](https://github.com/nodejs/node/pull/7391)
- [[`c1bd3fe14c`](https://github.com/nodejs/node/commit/c1bd3fe14c)] - **(SEMVER-MINOR)** **cluster**: work with `v8_inspector` (cjihrig) [#6792](https://github.com/nodejs/node/pull/6792)
- [[`fa9e6f7463`](https://github.com/nodejs/node/commit/fa9e6f7463)] - **crypto**: Allow GCM ciphers to have a longer IV length (Michael Wain) [#6376](https://github.com/nodejs/node/pull/6376)
- [[`ef41c8bd8e`](https://github.com/nodejs/node/commit/ef41c8bd8e)] - **crypto**: update root certificates (Ben Noordhuis) [#7363](https://github.com/nodejs/node/pull/7363)
- [[`2c7804ad9e`](https://github.com/nodejs/node/commit/2c7804ad9e)] - **crypto,tls**: perf improvements for crypto and tls getCiphers (James M Snell) [#7225](https://github.com/nodejs/node/pull/7225)
- [[`4891001d7e`](https://github.com/nodejs/node/commit/4891001d7e)] - **(SEMVER-MINOR)** **debugger**: make listen address configurable (Ben Noordhuis) [#3316](https://github.com/nodejs/node/pull/3316)
- [[`5bb63e13d1`](https://github.com/nodejs/node/commit/5bb63e13d1)] - **deps**: upgrade npm to 3.10.3 (Kat Marchán) [#7515](https://github.com/nodejs/node/pull/7515)
- [[`581e6deeda`](https://github.com/nodejs/node/commit/581e6deeda)] - **deps**: upgrade npm to 3.10.2 (Rebecca Turner) [#7410](https://github.com/nodejs/node/pull/7410)
- [[`12b199369d`](https://github.com/nodejs/node/commit/12b199369d)] - **deps**: update `icu-small` to include punycode datafiles (James M Snell) [#7355](https://github.com/nodejs/node/pull/7355)
- [[`225f3b9f34`](https://github.com/nodejs/node/commit/225f3b9f34)] - **deps**: update `v8_inspector` (Michaël Zasso) [#7385](https://github.com/nodejs/node/pull/7385)
- [[`a4880b5b10`](https://github.com/nodejs/node/commit/a4880b5b10)] - **deps**: `MASM.UseSafeExceptionHandlers` for OpenSSL (Fedor Indutny) [#7427](https://github.com/nodejs/node/pull/7427)
- [[`cbe57479c4`](https://github.com/nodejs/node/commit/cbe57479c4)] - **deps**: switch to upstream `v8_inspector` (Ali Ijaz Sheikh) [#7302](https://github.com/nodejs/node/pull/7302)
- [[`f4777c77eb`](https://github.com/nodejs/node/commit/f4777c77eb)] - **deps**: update `v8_inspector` (Ali Ijaz Sheikh) [#7118](https://github.com/nodejs/node/pull/7118)
- [[`62105288d3`](https://github.com/nodejs/node/commit/62105288d3)] - **(SEMVER-MINOR)** **deps**: import `v8_inspector` (Ali Ijaz Sheikh) [#6792](https://github.com/nodejs/node/pull/6792)
- [[`c544213717`](https://github.com/nodejs/node/commit/c544213717)] - **deps**: backport 7dfb5beeec from V8 upstream (Myles Borins) [#7348](https://github.com/nodejs/node/pull/7348)
- [[`b0da07a788`](https://github.com/nodejs/node/commit/b0da07a788)] - **doc**: add `added:` information for timers (Anna Henningsen) [#7493](https://github.com/nodejs/node/pull/7493)
- [[`63d361b531`](https://github.com/nodejs/node/commit/63d361b531)] - **doc**: fix documentation of process.argv (Tarun Garg) [#7449](https://github.com/nodejs/node/pull/7449)
- [[`45f83e59c4`](https://github.com/nodejs/node/commit/45f83e59c4)] - **doc**: add guide for Node.js Timers (Ryan Lewis) [#6825](https://github.com/nodejs/node/pull/6825)
- [[`7d07a0b68e`](https://github.com/nodejs/node/commit/7d07a0b68e)] - **doc**: improve usage of `zero`/`0` (Rich Trott) [#7466](https://github.com/nodejs/node/pull/7466)
- [[`8d18aed59e`](https://github.com/nodejs/node/commit/8d18aed59e)] - **doc**: fixing minor typo in `AtExit` hooks section (Daniel Bevenius) [#7485](https://github.com/nodejs/node/pull/7485)
- [[`58ae35c34d`](https://github.com/nodejs/node/commit/58ae35c34d)] - **doc**: fix broken refs to `url.parse()` in http docs (Anna Henningsen) [#7392](https://github.com/nodejs/node/pull/7392)
- [[`f269c008f2`](https://github.com/nodejs/node/commit/f269c008f2)] - **doc**: add `added:` information for https (Anna Henningsen) [#7392](https://github.com/nodejs/node/pull/7392)
- [[`1c7b622cfc`](https://github.com/nodejs/node/commit/1c7b622cfc)] - **doc**: add `added:` information for http (Anna Henningsen) [#7392](https://github.com/nodejs/node/pull/7392)
- [[`bc37e6a22d`](https://github.com/nodejs/node/commit/bc37e6a22d)] - **doc**: general improvements to timers.md (James M Snell) [#6937](https://github.com/nodejs/node/pull/6937)
- [[`5f766ad6d0`](https://github.com/nodejs/node/commit/5f766ad6d0)] - **doc**: fix typographic error in process doc (Rich Trott) [#7431](https://github.com/nodejs/node/pull/7431)
- [[`3475591d1c`](https://github.com/nodejs/node/commit/3475591d1c)] - **doc**: fix "sign.verify" typo in crypto doc. (Ruslan Iusupov) [#7411](https://github.com/nodejs/node/pull/7411)
- [[`44bc638cdb`](https://github.com/nodejs/node/commit/44bc638cdb)] - **doc**: clarify `child_process` stdout/stderr types (sartrey) [#7361](https://github.com/nodejs/node/pull/7361)
- [[`efce335e63`](https://github.com/nodejs/node/commit/efce335e63)] - **doc**: add CTC meeting minutes 2016-06-15 (Josh Gavant) [#7320](https://github.com/nodejs/node/pull/7320)
- [[`b725437c81`](https://github.com/nodejs/node/commit/b725437c81)] - **doc**: minor rewording to the GitHub issue/pr templates (Jeremiah Senkpiel) [#7403](https://github.com/nodejs/node/pull/7403)
- [[`4486ba9ee1`](https://github.com/nodejs/node/commit/4486ba9ee1)] - **doc**: add lance to collaborators (Lance Ball) [#7407](https://github.com/nodejs/node/pull/7407)
- [[`09a7c91baa`](https://github.com/nodejs/node/commit/09a7c91baa)] - **doc**: update "who to cc in issues" chart (Jeremiah Senkpiel) [#6694](https://github.com/nodejs/node/pull/6694)
- [[`eed65973d5`](https://github.com/nodejs/node/commit/eed65973d5)] - **doc**: fix link in the stream doc (Italo A. Casas) [#7347](https://github.com/nodejs/node/pull/7347)
- [[`a04cd85667`](https://github.com/nodejs/node/commit/a04cd85667)] - **doc**: fix repl defineCommand example (akki) [#7365](https://github.com/nodejs/node/pull/7365)
- [[`029af2c1f6`](https://github.com/nodejs/node/commit/029af2c1f6)] - **doc**: update build instructions for Windows (João Reis) [#7285](https://github.com/nodejs/node/pull/7285)
- [[`7a0718bdc6`](https://github.com/nodejs/node/commit/7a0718bdc6)] - **doc**: add `added:` information for tls (Italo A. Casas) [#7018](https://github.com/nodejs/node/pull/7018)
- [[`ec515c5d3b`](https://github.com/nodejs/node/commit/ec515c5d3b)] - **doc**: mention http request "aborted" events (Kyle E. Mitchell) [#7270](https://github.com/nodejs/node/pull/7270)
- [[`0f434fee6e`](https://github.com/nodejs/node/commit/0f434fee6e)] - **doc**: add RReverser to collaborators (Ingvar Stepanyan) [#7370](https://github.com/nodejs/node/pull/7370)
- [[`7aa2125fae`](https://github.com/nodejs/node/commit/7aa2125fae)] - **doc**: add argument information for `socket.destroy()` (Rich Trott) [#7238](https://github.com/nodejs/node/pull/7238)
- [[`9e9d7b8fba`](https://github.com/nodejs/node/commit/9e9d7b8fba)] - **doc**: general improvements to os.md copy (James M Snell) [#7124](https://github.com/nodejs/node/pull/7124)
- [[`cd439465cc`](https://github.com/nodejs/node/commit/cd439465cc)] - **doc**: fix typos in the stream doc (vsemozhetbyt) [#7336](https://github.com/nodejs/node/pull/7336)
- [[`dddfed24db`](https://github.com/nodejs/node/commit/dddfed24db)] - **doc**: document `socket.destroyed` (Tushar Mathur) [#6128](https://github.com/nodejs/node/pull/6128)
- [[`cd7c29e471`](https://github.com/nodejs/node/commit/cd7c29e471)] - **doc**: correct `added:` information for fs.access (Richard Lau) [#7299](https://github.com/nodejs/node/pull/7299)
- [[`6aa179b4a6`](https://github.com/nodejs/node/commit/6aa179b4a6)] - **doc**: add `added:` information for repl (Anna Henningsen) [#7256](https://github.com/nodejs/node/pull/7256)
- [[`08a9aa31e1`](https://github.com/nodejs/node/commit/08a9aa31e1)] - **doc**: fix broken link in vm.md (Luigi Pinca) [#7304](https://github.com/nodejs/node/pull/7304)
- [[`12fbac102b`](https://github.com/nodejs/node/commit/12fbac102b)] - **doc**: fix cluster worker `'message'` event (cjihrig) [#7309](https://github.com/nodejs/node/pull/7309)
- [[`1a0ed26883`](https://github.com/nodejs/node/commit/1a0ed26883)] - **doc**: fix events typo (Greyson Parrelli) [#7329](https://github.com/nodejs/node/pull/7329)
- [[`1e7a7be1ad`](https://github.com/nodejs/node/commit/1e7a7be1ad)] - **doc**: clarify `fs.access()` works on directories too. (Lance Ball) [#7321](https://github.com/nodejs/node/pull/7321)
- [[`e7b84007be`](https://github.com/nodejs/node/commit/e7b84007be)] - **http**: replace finish() callback with arrow function (Guy Fraser) [#7378](https://github.com/nodejs/node/pull/7378)
- [[`c4aaf47f4d`](https://github.com/nodejs/node/commit/c4aaf47f4d)] - **inspector**: Do cleanups before notifying callback (Eugene Ostroukhov) [#7450](https://github.com/nodejs/node/pull/7450)
- [[`fe580eb578`](https://github.com/nodejs/node/commit/fe580eb578)] - **inspector**: print warning when used (Evan Lucas) [#7383](https://github.com/nodejs/node/pull/7383)
- [[`8dd48c9251`](https://github.com/nodejs/node/commit/8dd48c9251)] - **inspector**: fix inspector connection cleanup (Eugene Ostroukhov) [#7268](https://github.com/nodejs/node/pull/7268)
- [[`09ecd1fb58`](https://github.com/nodejs/node/commit/09ecd1fb58)] - **inspector**: fix coverity scan errors (Eugene Ostroukhov) [#7324](https://github.com/nodejs/node/pull/7324)
- [[`88b2aa3ce6`](https://github.com/nodejs/node/commit/88b2aa3ce6)] - **inspector**: `process.exit()` should wait for inspector (Eugene Ostroukhov) [#7252](https://github.com/nodejs/node/pull/7252)
- [[`7da8a413f6`](https://github.com/nodejs/node/commit/7da8a413f6)] - **inspector**: reduce implementation in header (Eugene Ostroukhov) [#7228](https://github.com/nodejs/node/pull/7228)
- [[`ec90a7a92e`](https://github.com/nodejs/node/commit/ec90a7a92e)] - **inspector**: change default port (Ali Ijaz Sheikh) [#7212](https://github.com/nodejs/node/pull/7212)
- [[`d0e24923a6`](https://github.com/nodejs/node/commit/d0e24923a6)] - **net**: use icu's punycode implementation (James M Snell) [#7355](https://github.com/nodejs/node/pull/7355)
- [[`fb39025e31`](https://github.com/nodejs/node/commit/fb39025e31)] - **punycode**: update to v2.0.0 (Mathias Bynens) [#7267](https://github.com/nodejs/node/pull/7267)
- [[`6b1fc63dcb`](https://github.com/nodejs/node/commit/6b1fc63dcb)] - **(SEMVER-MINOR)** **readline**: allow passing prompt to constructor (Evan Lucas) [#7125](https://github.com/nodejs/node/pull/7125)
- [[`72d659a000`](https://github.com/nodejs/node/commit/72d659a000)] - **(SEMVER-MINOR)** **readline**: return old status from `_setRawMode()` (Anna Henningsen) [#6635](https://github.com/nodejs/node/pull/6635)
- [[`7a7b8f7e67`](https://github.com/nodejs/node/commit/7a7b8f7e67)] - **repl**: Default `useGlobal` to false in CLI REPL. (Lance Ball) [#5703](https://github.com/nodejs/node/pull/5703)
- [[`c39f6c0204`](https://github.com/nodejs/node/commit/c39f6c0204)] - **repl**: Enable tab completion for global properties (Lance Ball) [#7369](https://github.com/nodejs/node/pull/7369)
- [[`ca95a84bc4`](https://github.com/nodejs/node/commit/ca95a84bc4)] - **repl**: fix tab completion for defined commands (Prince J Wesley) [#7364](https://github.com/nodejs/node/pull/7364)
- [[`da8e510ee0`](https://github.com/nodejs/node/commit/da8e510ee0)] - **(SEMVER-MINOR)** **repl**: break on sigint/ctrl+c (Anna Henningsen) [#6635](https://github.com/nodejs/node/pull/6635)
- [[`3cba8acc15`](https://github.com/nodejs/node/commit/3cba8acc15)] - **src**: remove obsolete `NOLINT` comments (Ben Noordhuis) [#7462](https://github.com/nodejs/node/pull/7462)
- [[`57cc4e3071`](https://github.com/nodejs/node/commit/57cc4e3071)] - **src**: print backtrace on failed `CHECK`/`ASSERT` (Ben Noordhuis) [#6734](https://github.com/nodejs/node/pull/6734)
- [[`b8919b1d23`](https://github.com/nodejs/node/commit/b8919b1d23)] - **src**: move `ABORT()` logic into `node::Abort()` (Ben Noordhuis) [#6734](https://github.com/nodejs/node/pull/6734)
- [[`c96d701769`](https://github.com/nodejs/node/commit/c96d701769)] - **src**: print backtrace on abort/unreachable code (Ben Noordhuis) [#6734](https://github.com/nodejs/node/pull/6734)
- [[`6cec90a611`](https://github.com/nodejs/node/commit/6cec90a611)] - **src**: print backtrace on fatal error (Ben Noordhuis) [#6734](https://github.com/nodejs/node/pull/6734)
- [[`8f7baffee4`](https://github.com/nodejs/node/commit/8f7baffee4)] - **src**: fix bad logic in uid/gid checks (Ben Noordhuis) [#7374](https://github.com/nodejs/node/pull/7374)
- [[`6fa560dce9`](https://github.com/nodejs/node/commit/6fa560dce9)] - **src**: fix memory leak in `WriteBuffers()` error path (Ben Noordhuis) [#7374](https://github.com/nodejs/node/pull/7374)
- [[`ce039c3240`](https://github.com/nodejs/node/commit/ce039c3240)] - **src**: fix use-after-return in zlib bindings (Ben Noordhuis) [#7374](https://github.com/nodejs/node/pull/7374)
- [[`2816418c04`](https://github.com/nodejs/node/commit/2816418c04)] - **src**: remove deprecated `HMAC_Init`, use `HMAC_Init_ex` (Ben Noordhuis) [#7374](https://github.com/nodejs/node/pull/7374)
- [[`b7e661b12c`](https://github.com/nodejs/node/commit/b7e661b12c)] - **src**: remove duplicate `HMAC_Init` calls (Ben Noordhuis) [#7374](https://github.com/nodejs/node/pull/7374)
- [[`25bc7fee34`](https://github.com/nodejs/node/commit/25bc7fee34)] - **src**: remove unused `md_` data members (Ben Noordhuis) [#7374](https://github.com/nodejs/node/pull/7374)
- [[`2228a656b0`](https://github.com/nodejs/node/commit/2228a656b0)] - **src**: remove unused data member `write_queue_size_` (Ben Noordhuis) [#7374](https://github.com/nodejs/node/pull/7374)
- [[`9945b4ecd6`](https://github.com/nodejs/node/commit/9945b4ecd6)] - **src**: guard against starting fs watcher twice (Ben Noordhuis) [#7374](https://github.com/nodejs/node/pull/7374)
- [[`3b1c19f90a`](https://github.com/nodejs/node/commit/3b1c19f90a)] - **src**: initialize `encoding_` data member (Ben Noordhuis) [#7374](https://github.com/nodejs/node/pull/7374)
- [[`c795d1ed9b`](https://github.com/nodejs/node/commit/c795d1ed9b)] - **src**: check `uv_async_init()` return value (Ben Noordhuis) [#7374](https://github.com/nodejs/node/pull/7374)
- [[`001aa06bc0`](https://github.com/nodejs/node/commit/001aa06bc0)] - **src**: lint v8abbr.h (Ben Noordhuis) [#7462](https://github.com/nodejs/node/pull/7462)
- [[`ca4fb084f6`](https://github.com/nodejs/node/commit/ca4fb084f6)] - **src**: lint `node_lttng_tp.h` (Ben Noordhuis) [#7462](https://github.com/nodejs/node/pull/7462)
- [[`da0ebf62c7`](https://github.com/nodejs/node/commit/da0ebf62c7)] - **src**: lint `node_win32_perfctr_provider.cc` (Ben Noordhuis) [#7462](https://github.com/nodejs/node/pull/7462)
- [[`3fa643b069`](https://github.com/nodejs/node/commit/3fa643b069)] - **src**: fix whitespace/indent cpplint warnings (Ben Noordhuis) [#7462](https://github.com/nodejs/node/pull/7462)
- [[`f72259aa89`](https://github.com/nodejs/node/commit/f72259aa89)] - **src**: fix whitespace/blank_line cpplint warnings (Ben Noordhuis) [#7462](https://github.com/nodejs/node/pull/7462)
- [[`1b3c1b08a8`](https://github.com/nodejs/node/commit/1b3c1b08a8)] - **src**: fix runtime/references cpplint warnings (Ben Noordhuis) [#7462](https://github.com/nodejs/node/pull/7462)
- [[`be0c575ab4`](https://github.com/nodejs/node/commit/be0c575ab4)] - **src**: fix runtime/int cpplint warnings (Ben Noordhuis) [#7462](https://github.com/nodejs/node/pull/7462)
- [[`88c5183147`](https://github.com/nodejs/node/commit/88c5183147)] - **src**: fix runtime/indentation_namespace warnings (Ben Noordhuis) [#7462](https://github.com/nodejs/node/pull/7462)
- [[`1fa6dba8f2`](https://github.com/nodejs/node/commit/1fa6dba8f2)] - **src**: fix readability/nolint cpplint warnings (Ben Noordhuis) [#7462](https://github.com/nodejs/node/pull/7462)
- [[`43e83576bd`](https://github.com/nodejs/node/commit/43e83576bd)] - **src**: fix readability/namespace cpplint warnings (Ben Noordhuis) [#7462](https://github.com/nodejs/node/pull/7462)
- [[`5fd158568f`](https://github.com/nodejs/node/commit/5fd158568f)] - **src**: fix readability/inheritance cpplint warnings (Ben Noordhuis) [#7462](https://github.com/nodejs/node/pull/7462)
- [[`b7e006b489`](https://github.com/nodejs/node/commit/b7e006b489)] - **src**: fix readability/constructors cpplint warnings (Ben Noordhuis) [#7462](https://github.com/nodejs/node/pull/7462)
- [[`7fe758de85`](https://github.com/nodejs/node/commit/7fe758de85)] - **src**: fix readability/braces cpplint warnings (Ben Noordhuis) [#7462](https://github.com/nodejs/node/pull/7462)
- [[`6280ccdaaa`](https://github.com/nodejs/node/commit/6280ccdaaa)] - **src**: fix build/header_guard cpplint warnings (Ben Noordhuis) [#7462](https://github.com/nodejs/node/pull/7462)
- [[`5dfa234bae`](https://github.com/nodejs/node/commit/5dfa234bae)] - **src**: fix build/c++tr1 cpplint warnings (Ben Noordhuis) [#7462](https://github.com/nodejs/node/pull/7462)
- [[`84dd526f51`](https://github.com/nodejs/node/commit/84dd526f51)] - **src**: renaming `ares_task` struct to `node_ares_task` (Daniel Bevenius) [#7345](https://github.com/nodejs/node/pull/7345)
- [[`059335180d`](https://github.com/nodejs/node/commit/059335180d)] - **src**: use RAII for mutexes and condition variables (Ben Noordhuis) [#7334](https://github.com/nodejs/node/pull/7334)
- [[`3d69ad1cf3`](https://github.com/nodejs/node/commit/3d69ad1cf3)] - **src**: make Sec-WebSocket-Key check case-insensitive (Myles Borins) [#7248](https://github.com/nodejs/node/pull/7248)
- [[`38d36e3285`](https://github.com/nodejs/node/commit/38d36e3285)] - **src**: fix `--without-inspector` build (Anna Henningsen) [#7258](https://github.com/nodejs/node/pull/7258)
- [[`2fd140b949`](https://github.com/nodejs/node/commit/2fd140b949)] - **src**: fix json payload from inspector (Myles Borins) [#7232](https://github.com/nodejs/node/pull/7232)
- [[`643b33b497`](https://github.com/nodejs/node/commit/643b33b497)] - **src**: add linebreak to inspector message (Nicolas Romer) [#7070](https://github.com/nodejs/node/pull/7070)
- [[`ea2d661447`](https://github.com/nodejs/node/commit/ea2d661447)] - **src**: fix `--without-inspector` build (Anna Henningsen) [#7078](https://github.com/nodejs/node/pull/7078)
- [[`2a8bd35bac`](https://github.com/nodejs/node/commit/2a8bd35bac)] - **(SEMVER-MINOR)** **src**: add `node::FreeEnvironment` public API (Cheng Zhao) [#3098](https://github.com/nodejs/node/pull/3098)
- [[`929b6c29d8`](https://github.com/nodejs/node/commit/929b6c29d8)] - **(SEMVER-MINOR)** **src**: refactor `require('constants')` (James M Snell) [#6534](https://github.com/nodejs/node/pull/6534)
- [[`cd38401724`](https://github.com/nodejs/node/commit/cd38401724)] - **src**: fix Windows segfault with `--eval` (Bryce Simonds) [#6938](https://github.com/nodejs/node/pull/6938)
- [[`6dc0dae830`](https://github.com/nodejs/node/commit/6dc0dae830)] - **src**: rename "node" script to "bootstrap_node" (Daniel Bevenius) [#7277](https://github.com/nodejs/node/pull/7277)
- [[`7d4f038a78`](https://github.com/nodejs/node/commit/7d4f038a78)] - **(SEMVER-MINOR)** **src,lib**: v8-inspector support (Pavel Feldman) [#6792](https://github.com/nodejs/node/pull/6792)
- [[`e1d6bd9e30`](https://github.com/nodejs/node/commit/e1d6bd9e30)] - **(SEMVER-MINOR)** **stream**: improve `Readable.read()` performance (Brian White) [#7077](https://github.com/nodejs/node/pull/7077)
- [[`962ac37e1f`](https://github.com/nodejs/node/commit/962ac37e1f)] - **string_decoder**: fix bad utf8 character handling (Brian White) [#7310](https://github.com/nodejs/node/pull/7310)
- [[`f6ba5f6380`](https://github.com/nodejs/node/commit/f6ba5f6380)] - **test**: really run addon tests on `make test` (Anna Henningsen) [#7542](https://github.com/nodejs/node/pull/7542)
- [[`c132e9cc24`](https://github.com/nodejs/node/commit/c132e9cc24)] - **test**: listen on and connect to `127.0.0.1` (Ben Noordhuis) [#7524](https://github.com/nodejs/node/pull/7524)
- [[`6da49ac1fd`](https://github.com/nodejs/node/commit/6da49ac1fd)] - **test**: handle SmartOS bug in test-tls-session-cache (Rich Trott) [#7505](https://github.com/nodejs/node/pull/7505)
- [[`b383fdde79`](https://github.com/nodejs/node/commit/b383fdde79)] - **test**: remove `common.PORT` from http tests (Rich Trott) [#7467](https://github.com/nodejs/node/pull/7467)
- [[`658ab3d1e6`](https://github.com/nodejs/node/commit/658ab3d1e6)] - **test**: check types for http request and response (Ben Noordhuis) [#7003](https://github.com/nodejs/node/pull/7003)
- [[`517e71508e`](https://github.com/nodejs/node/commit/517e71508e)] - **test**: add abort test for backtrace validation (cjihrig) [#6734](https://github.com/nodejs/node/pull/6734)
- [[`6de80fcaea`](https://github.com/nodejs/node/commit/6de80fcaea)] - **test**: don't use internal headers in add-on tests (Ben Noordhuis) [#6734](https://github.com/nodejs/node/pull/6734)
- [[`c7ab7a31d7`](https://github.com/nodejs/node/commit/c7ab7a31d7)] - **test**: fix abort/test-abort-uncaught-exception (Ben Noordhuis) [#6734](https://github.com/nodejs/node/pull/6734)
- [[`4b0ab5b308`](https://github.com/nodejs/node/commit/4b0ab5b308)] - **test**: add `testcfg.py` to test/abort/ (Ben Noordhuis) [#6734](https://github.com/nodejs/node/pull/6734)
- [[`365f5207b3`](https://github.com/nodejs/node/commit/365f5207b3)] - **test**: test `isFullWidthCodePoint()` with invalid input (Rich Trott) [#7422](https://github.com/nodejs/node/pull/7422)
- [[`e30f32f003`](https://github.com/nodejs/node/commit/e30f32f003)] - **_Revert_** "**test**: mark test-vm-timeout flaky on windows" (Anna Henningsen) [#7373](https://github.com/nodejs/node/pull/7373)
- [[`457d244170`](https://github.com/nodejs/node/commit/457d244170)] - **test**: fix flaky test-vm-timeout (Anna Henningsen) [#7373](https://github.com/nodejs/node/pull/7373)
- [[`16aff79dee`](https://github.com/nodejs/node/commit/16aff79dee)] - **test**: add test for `exec()` known issue (Rich Trott) [#7375](https://github.com/nodejs/node/pull/7375)
- [[`8f1733c4e7`](https://github.com/nodejs/node/commit/8f1733c4e7)] - **test**: add more `UTF-8` `StringDecoder` tests (Martin von Gagern) [#7310](https://github.com/nodejs/node/pull/7310)
- [[`0bbf2ef6ea`](https://github.com/nodejs/node/commit/0bbf2ef6ea)] - **test**: fix flaky test-fs-watch-encoding on OS X (Rich Trott) [#7356](https://github.com/nodejs/node/pull/7356)
- [[`009858bd0a`](https://github.com/nodejs/node/commit/009858bd0a)] - **test**: remove internet/test-tls-connnect-cnnic (Ben Noordhuis) [#7363](https://github.com/nodejs/node/pull/7363)
- [[`c236a13341`](https://github.com/nodejs/node/commit/c236a13341)] - **test**: mark test-vm-timeout flaky on windows (Rich Trott) [#7359](https://github.com/nodejs/node/pull/7359)
- [[`580e11026e`](https://github.com/nodejs/node/commit/580e11026e)] - **test**: refresh the tmpdir before using (Rich Trott) [#7327](https://github.com/nodejs/node/pull/7327)
- [[`e2bf250a29`](https://github.com/nodejs/node/commit/e2bf250a29)] - **test**: add tests for some `stream.Readable` uses (Anna Henningsen) [#7260](https://github.com/nodejs/node/pull/7260)
- [[`efb7a90fa9`](https://github.com/nodejs/node/commit/efb7a90fa9)] - **timers**: optimize `setImmediate()` (Andras) [#6436](https://github.com/nodejs/node/pull/6436)
- [[`a5d894590d`](https://github.com/nodejs/node/commit/a5d894590d)] - **timers**: optimize linkedlist (Andras) [#6436](https://github.com/nodejs/node/pull/6436)
- [[`77331a7c01`](https://github.com/nodejs/node/commit/77331a7c01)] - **tls**: avoid calling `Buffer.byteLength` multiple times (James M Snell) [#7236](https://github.com/nodejs/node/pull/7236)
- [[`d857e9a3e9`](https://github.com/nodejs/node/commit/d857e9a3e9)] - **tools**: explicit path for V8 test tap output (Myles Borins) [#7460](https://github.com/nodejs/node/pull/7460)
- [[`e727cb5021`](https://github.com/nodejs/node/commit/e727cb5021)] - **tools**: fix `-Wunused-variable` warning (Ben Noordhuis) [#7462](https://github.com/nodejs/node/pull/7462)
- [[`9d0641e20d`](https://github.com/nodejs/node/commit/9d0641e20d)] - **tools**: allow cpplint to run outside git repo (Ben Noordhuis) [#7462](https://github.com/nodejs/node/pull/7462)
- [[`01b7b9a2bc`](https://github.com/nodejs/node/commit/01b7b9a2bc)] - **tools**: add back `--mode=tap` to cpplint (Ben Noordhuis) [#7462](https://github.com/nodejs/node/pull/7462)
- [[`e3662a4386`](https://github.com/nodejs/node/commit/e3662a4386)] - **tools**: disable unwanted cpplint rules again (Ben Noordhuis) [#7462](https://github.com/nodejs/node/pull/7462)
- [[`5830ec5d41`](https://github.com/nodejs/node/commit/5830ec5d41)] - **tools**: update cpplint to r456 (Ben Noordhuis) [#7462](https://github.com/nodejs/node/pull/7462)
- [[`3eb02b6743`](https://github.com/nodejs/node/commit/3eb02b6743)] - **tools**: output include guards in mk-ca-bundle.pl (Ben Noordhuis) [#7363](https://github.com/nodejs/node/pull/7363)
- [[`32e068a5d0`](https://github.com/nodejs/node/commit/32e068a5d0)] - **tools**: update certdata.txt (Ben Noordhuis) [#7363](https://github.com/nodejs/node/pull/7363)
- [[`bd8c951757`](https://github.com/nodejs/node/commit/bd8c951757)] - **tools**: disable readability/function cpplint rule (Ben Noordhuis) [#7334](https://github.com/nodejs/node/pull/7334)
- [[`3b8914d5ce`](https://github.com/nodejs/node/commit/3b8914d5ce)] - **(SEMVER-MINOR)** **util**: add an option for configuring break length (cjihrig) [#7499](https://github.com/nodejs/node/pull/7499)
- [[`6151544751`](https://github.com/nodejs/node/commit/6151544751)] - **vm**: don't print out arrow message for custom error (Anna Henningsen) [#7398](https://github.com/nodejs/node/pull/7398)
- [[`55b87c0238`](https://github.com/nodejs/node/commit/55b87c0238)] - **vm**: test for abort condition of current invocation (Anna Henningsen) [#7373](https://github.com/nodejs/node/pull/7373)
- [[`d049919e7d`](https://github.com/nodejs/node/commit/d049919e7d)] - **(SEMVER-MINOR)** **vm**: add ability to break on sigint/ctrl+c (Anna Henningsen) [#6635](https://github.com/nodejs/node/pull/6635)

Windows 32-bit Installer: https://nodejs.org/dist/v6.3.0/node-v6.3.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v6.3.0/node-v6.3.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v6.3.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v6.3.0/win-x64/node.exe \
Mac OS X 64-bit Installer: https://nodejs.org/dist/v6.3.0/node-v6.3.0.pkg \
Mac OS X 64-bit Binary: https://nodejs.org/dist/v6.3.0/node-v6.3.0-darwin-x64.tar.gz \
Linux 32-bit Binary: https://nodejs.org/dist/v6.3.0/node-v6.3.0-linux-x86.tar.xz \
Linux 64-bit Binary: https://nodejs.org/dist/v6.3.0/node-v6.3.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v6.3.0/node-v6.3.0-linux-ppc64le.tar.xz \
SmartOS 32-bit Binary: https://nodejs.org/dist/v6.3.0/node-v6.3.0-sunos-x86.tar.xz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v6.3.0/node-v6.3.0-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v6.3.0/node-v6.3.0-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v6.3.0/node-v6.3.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v6.3.0/node-v6.3.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v6.3.0/node-v6.3.0.tar.gz \
Other release files: https://nodejs.org/dist/v6.3.0/ \
Documentation: https://nodejs.org/docs/v6.3.0/api/

Shasums (GPG signing hash: SHA512, file hash: SHA256):

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA512

5c711a62e464f1d455c31afe2a62e9866eeaf1c23d977b57a60285d0bd040ba5  node-v6.3.0-darwin-x64.tar.gz
4f9a19b5688092d5652e8748093cb9c90fa503ea13b976e51dbd53c2fa07c116  node-v6.3.0-darwin-x64.tar.xz
9275894c3ed6373068cddeeb968e4d2ceba76368b6cd3b01aca79f0b592badd6  node-v6.3.0-headers.tar.gz
85358983586760b0a9c0d36294ca5266dbd4e5d0df5390dfbf7aba684799e8db  node-v6.3.0-headers.tar.xz
58995c3f91962fc4383696f9c64763b3cd27d9b5903b4cf2a5ccfe86c8258e9f  node-v6.3.0-linux-arm64.tar.gz
18000ebe5208a2ddb17ab6d301a79d6ffa29287d579299480c62859c42c6c51c  node-v6.3.0-linux-arm64.tar.xz
de3554545e2d04719ebcd990984ff1eb5d6edbbbb9d24893cb998e2eb15d8bf5  node-v6.3.0-linux-armv7l.tar.gz
dce3e835d17a4febfb234f37593eb1b1dc02c87c16f49504c1800de7a8ccb0f2  node-v6.3.0-linux-armv7l.tar.xz
4665fa5c521e7c39bec441c66c41017a0a430944eb49fd0adec6e1c3b72f992b  node-v6.3.0-linux-ppc64le.tar.gz
7fd30dc537135180604139ac59db5673230ea6e95163046f08302968230951f0  node-v6.3.0-linux-ppc64le.tar.xz
72d6b834e88acc7d805f5bc7b90bce7f418b5b7270efa5126b614ae81a6c8b6c  node-v6.3.0-linux-ppc64.tar.gz
fb90a4ecde4feb7b70cb2af5297e560b03a3a3c8a201e05eaeb259c294f0b70e  node-v6.3.0-linux-ppc64.tar.xz
d26c09fc95ebb457b79fcb0a2890fe8417b2c04f4016dadf2d165c07af762764  node-v6.3.0-linux-x64.tar.gz
eeee9611daaa80ddd277dba66586f3580ce5cb6309838f854e557e780a68a2c9  node-v6.3.0-linux-x64.tar.xz
9302affb9844ec9a51e774169072bff3421c0232f746b3d504ba6c13c749ff9f  node-v6.3.0-linux-x86.tar.gz
504dc0ad16f8f5d9147df757eb47838a4ae159d50f3efe73539d3b761267f213  node-v6.3.0-linux-x86.tar.xz
c752cab4c2519e513626dacdab93e7c0b8ef25de7d34e46813bf5035d6c81bfe  node-v6.3.0.pkg
7d5f8b37ea3809db6bd633cb1702d1f942319de0c606883ffd300b36728283b7  node-v6.3.0-sunos-x64.tar.gz
6f7bd008f9d6aff1ee69d26b7215dedcfbf12108c22d4259059233067f29eae6  node-v6.3.0-sunos-x64.tar.xz
a494375cce067250bf8729b007a913a9b06b7b3a0ca1629dd263a84da3d172e2  node-v6.3.0-sunos-x86.tar.gz
654e0ed32e76ec5e4707d5d4af5aeab25dd824e638b70bf43a764ee78bf1530a  node-v6.3.0-sunos-x86.tar.xz
4ed7a99985f8afee337cc22d5fef61b495ab4238dfff3750ac9019e87fc6aae6  node-v6.3.0.tar.gz
66c2015bfedfff219f6026b1271a7a94942eb66856929cca1f40f895fbbef62c  node-v6.3.0.tar.xz
3aec8f0e2a74f6fe02f16e6af0be21a5a6f5003ddd03d24d4b1884a2b01d1ead  node-v6.3.0-win-x64.7z
723b2e9c8eee51aded69b08ae74cee495859799ee2e029ef495ee8cf2e9b68e8  node-v6.3.0-win-x64.zip
d53e30f1c561356f0bc2cdb68d93eacb31ba63ba6c02b467304502cc0b6bc0b7  node-v6.3.0-win-x86.7z
38a0160eac3ee8c2c2497ffdd7fcb7c644a73cbb9532a78fc11f2efbae1a74ea  node-v6.3.0-win-x86.zip
ec3fdc5e383948c68a2fa762be548ea18e83710c1b4af013b1b687faacfab274  node-v6.3.0-x64.msi
d838594fe4d3de0ec8265d742e7d3400a0e7c0c1d29df490f1f9c3af4639469d  node-v6.3.0-x86.msi
83cd7d82f5d7b2be3298fb44d218d38879f564e21a3fcab21c51138f032495c4  win-x64/node.exe
a98e6d7d10bcc85495da8f3f72c51ff7e506e248440c681f1ca8fc6644836143  win-x64/node.lib
94f4c037001787c1cb0e05057f339da19c59cf832f514a5ed592c04a4d367112  win-x64/node_pdb.7z
81591b06a70c6880b9acaa1642643d9f9734d36b660c888bf5e97ac65d88cb7e  win-x64/node_pdb.zip
f789e1b0040fb0f86ba567df42f7759a5b16c9976a45cf2c73693456e8b52e0a  win-x86/node.exe
39de1eda0901e6cd16155abb7bf0db28aad8bff938c2857f08cd1a12cbdd58c9  win-x86/node.lib
a1ef9df3736a29a4394ecb158b20e3192bbe5355028b0b41fb60c31d986c6456  win-x86/node_pdb.7z
7b6c9ad8ae08e70e3f5439c632377e6c31058187268d640c8980eeb22623f50c  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----
Comment: GPGTools - https://gpgtools.org

iQIcBAEBCgAGBQJXfUO5AAoJEEX17r2BPa6Ob0cQAIsV4/DC/961LkhIV2RJUXUO
YhFC6nchaTM3MSr3RQvDQQNC3TmEt9JagPPyMC1oMiy9DT7rzq52UCquIMh6nS8j
NjBs+L55Y0BaXrQbuqGs+isiqqUVvuLZkLU9IBbKs8hID4egqrHyY7tkxidoO3kL
g+z/A43rcSOPZzrGl6muwIe9OF/81o1sJHTETfn939SXfM9zOXQmbo71EfUnVNHT
ZfEIk4o4v24SR7+OW444ziJpJY4robJcFnUlA0WvBkf34ByAKIWnXm5yEEaNIIHa
R3E84JrXM0HgDfCVQ089pAyY1ImLT4XosK3j1pmjND/nOHHblH/TfcF73pBqtC70
n3eXjilVaTzZeMnRFHi5Ofopc9VPgAhAd/+Ya1aVDzUb0IA5mR3uMR8KTYx1UDZN
5yuBpHLhj4TFYmjHVvPRsJ4VT7qSUtHZXzHJyEG+mqhjJQtjkbF81fHbO0NYzr3G
uY0iUdc+cKmGuWq059qFJFo/6mlPdIlIydCIs31peVwRpI4JuXapGuVwRvlyD/kH
cFmElIXdq7S+Kn9jxIaokx2bo1hoZ1Q8Tb74s0kxhh+1bUlQohuUScvS3h1PG7cM
nn8OdMAVg9tNs2YrCndYzWqup80l+23boLTX9qyxEWeQo2Nb9Ddja2oZXERLy4hT
+v4vjY1hcMdVt/eL5tZ+
=DrfW
-----END PGP SIGNATURE-----

```
