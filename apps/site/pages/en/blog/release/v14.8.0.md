---
date: '2020-08-11T16:45:12.441Z'
category: release
title: Node v14.8.0 (Current)
layout: blog-post
author: <PERSON>
---

### Notable Changes

- [[`16aa927216`](https://github.com/nodejs/node/commit/16aa927216)] - **(SEMVER-MINOR)** **async_hooks**: add AsyncResource.bind utility (<PERSON>) [#34574](https://github.com/nodejs/node/pull/34574)
- [[`dc49561e8d`](https://github.com/nodejs/node/commit/dc49561e8d)] - **deps**: update to uvwasi 0.0.10 (<PERSON>) [#34623](https://github.com/nodejs/node/pull/34623)
- [[`6cd1c41604`](https://github.com/nodejs/node/commit/6cd1c41604)] - **doc**: add <PERSON> to collaborators (rickyes) [#34676](https://github.com/nodejs/node/pull/34676)
- [[`f0a41b2530`](https://github.com/nodejs/node/commit/f0a41b2530)] - **doc**: add release key for Ruy Adorno (Ruy Adorno) [#34628](https://github.com/nodejs/node/pull/34628)
- [[`10dd7a0eda`](https://github.com/nodejs/node/commit/10dd7a0eda)] - **doc**: add DerekNonGeneric to collaborators (Derek Lewis) [#34602](https://github.com/nodejs/node/pull/34602)
- [[`62bb2e757f`](https://github.com/nodejs/node/commit/62bb2e757f)] - **(SEMVER-MINOR)** **module**: unflag Top-Level Await (Myles Borins) [#34558](https://github.com/nodejs/node/pull/34558)
- [[`8cc9e5eb52`](https://github.com/nodejs/node/commit/8cc9e5eb52)] - **(SEMVER-MINOR)** **n-api**: support type-tagging objects (Gabriel Schulhof) [#28237](https://github.com/nodejs/node/pull/28237)
- [[`e89ec46ba9`](https://github.com/nodejs/node/commit/e89ec46ba9)] - **(SEMVER-MINOR)** **n-api,src**: provide asynchronous cleanup hooks (Anna Henningsen) [#34572](https://github.com/nodejs/node/pull/34572)

### Commits

- [[`650248922b`](https://github.com/nodejs/node/commit/650248922b)] - **async_hooks**: avoid GC tracking of AsyncResource in ALS (Gerhard Stoebich) [#34653](https://github.com/nodejs/node/pull/34653)
- [[`0a51aa8fdb`](https://github.com/nodejs/node/commit/0a51aa8fdb)] - **async_hooks**: avoid unneeded AsyncResource creation (Gerhard Stoebich) [#34616](https://github.com/nodejs/node/pull/34616)
- [[`0af9bee4c3`](https://github.com/nodejs/node/commit/0af9bee4c3)] - **async_hooks**: improve property descriptors in als.bind (Gerhard Stoebich) [#34620](https://github.com/nodejs/node/pull/34620)
- [[`16aa927216`](https://github.com/nodejs/node/commit/16aa927216)] - **(SEMVER-MINOR)** **async_hooks**: add AsyncResource.bind utility (James M Snell) [#34574](https://github.com/nodejs/node/pull/34574)
- [[`e45c68af27`](https://github.com/nodejs/node/commit/e45c68af27)] - **async_hooks**: don't read resource if ALS is disabled (Gerhard Stoebich) [#34617](https://github.com/nodejs/node/pull/34617)
- [[`e9aebc3a8f`](https://github.com/nodejs/node/commit/e9aebc3a8f)] - **async_hooks**: fix id assignment in fast-path promise hook (Andrey Pechkurov) [#34548](https://github.com/nodejs/node/pull/34548)
- [[`5aed83c77f`](https://github.com/nodejs/node/commit/5aed83c77f)] - **async_hooks**: fix resource stack for deep stacks (Anna Henningsen) [#34573](https://github.com/nodejs/node/pull/34573)
- [[`9af62641c6`](https://github.com/nodejs/node/commit/9af62641c6)] - **async_hooks**: execute destroy hooks earlier (Gerhard Stoebich) [#34342](https://github.com/nodejs/node/pull/34342)
- [[`14656e1703`](https://github.com/nodejs/node/commit/14656e1703)] - **async_hooks**: don't reuse resource in HttpAgent when queued (Andrey Pechkurov) [#34439](https://github.com/nodejs/node/pull/34439)
- [[`c4457d873f`](https://github.com/nodejs/node/commit/c4457d873f)] - **benchmark**: always throw the same Error instance (Anna Henningsen) [#34523](https://github.com/nodejs/node/pull/34523)
- [[`6a129d0cf5`](https://github.com/nodejs/node/commit/6a129d0cf5)] - **build**: do not run auto-start-ci on forks (Evan Lucas) [#34650](https://github.com/nodejs/node/pull/34650)
- [[`2cd299b217`](https://github.com/nodejs/node/commit/2cd299b217)] - **build**: run CI on release branches (Shelley Vohr) [#34649](https://github.com/nodejs/node/pull/34649)
- [[`9ed9ccc5b3`](https://github.com/nodejs/node/commit/9ed9ccc5b3)] - **build**: enable build for node-v8 push (gengjiawen) [#34634](https://github.com/nodejs/node/pull/34634)
- [[`10f29e7550`](https://github.com/nodejs/node/commit/10f29e7550)] - **build**: increase startCI verbosity and fix job name (Mary Marchini) [#34635](https://github.com/nodejs/node/pull/34635)
- [[`befbaf384e`](https://github.com/nodejs/node/commit/befbaf384e)] - **build**: don't run auto-start-ci on push (Mary Marchini) [#34588](https://github.com/nodejs/node/pull/34588)
- [[`4af5dbd3bf`](https://github.com/nodejs/node/commit/4af5dbd3bf)] - **build**: fix auto-start-ci script path (Mary Marchini) [#34588](https://github.com/nodejs/node/pull/34588)
- [[`70cf3cbdfa`](https://github.com/nodejs/node/commit/70cf3cbdfa)] - **build**: auto start Jenkins CI via PR labels (Mary Marchini) [#34089](https://github.com/nodejs/node/pull/34089)
- [[`70e9eceeee`](https://github.com/nodejs/node/commit/70e9eceeee)] - **build**: toolchain.gypi and node_gyp.py cleanup (iandrc) [#34268](https://github.com/nodejs/node/pull/34268)
- [[`465968c5f8`](https://github.com/nodejs/node/commit/465968c5f8)] - **console**: document the behavior of console.assert() (iandrc) [#34501](https://github.com/nodejs/node/pull/34501)
- [[`a7b4318df9`](https://github.com/nodejs/node/commit/a7b4318df9)] - **crypto**: add OP flag constants added in OpenSSL v1.1.1 (Mateusz Krawczuk) [#33929](https://github.com/nodejs/node/pull/33929)
- [[`dc49561e8d`](https://github.com/nodejs/node/commit/dc49561e8d)] - **deps**: update to uvwasi 0.0.10 (Colin Ihrig) [#34623](https://github.com/nodejs/node/pull/34623)
- [[`8b1ec43da4`](https://github.com/nodejs/node/commit/8b1ec43da4)] - **doc**: use \_Static method\_ instead of \_Class Method\_ (Rich Trott) [#34659](https://github.com/nodejs/node/pull/34659)
- [[`a1b9d7f42e`](https://github.com/nodejs/node/commit/a1b9d7f42e)] - **doc**: tidy some addons.md text (Rich Trott) [#34654](https://github.com/nodejs/node/pull/34654)
- [[`b78278b922`](https://github.com/nodejs/node/commit/b78278b922)] - **doc**: use \_Class Method\_ in async_hooks.md (Rich Trott) [#34626](https://github.com/nodejs/node/pull/34626)
- [[`6cd1c41604`](https://github.com/nodejs/node/commit/6cd1c41604)] - **doc**: add Ricky Zhou to collaborators (rickyes) [#34676](https://github.com/nodejs/node/pull/34676)
- [[`d8e0deaa7c`](https://github.com/nodejs/node/commit/d8e0deaa7c)] - **doc**: edit process.title note for brevity and clarity (Rich Trott) [#34627](https://github.com/nodejs/node/pull/34627)
- [[`dd6bf20e8f`](https://github.com/nodejs/node/commit/dd6bf20e8f)] - **doc**: update fs.watch() availability for IBM i (iandrc) [#34611](https://github.com/nodejs/node/pull/34611)
- [[`f260bdd57b`](https://github.com/nodejs/node/commit/f260bdd57b)] - **doc**: fix typo in path.md (aetheryx) [#34550](https://github.com/nodejs/node/pull/34550)
- [[`f0a41b2530`](https://github.com/nodejs/node/commit/f0a41b2530)] - **doc**: add release key for Ruy Adorno (Ruy Adorno) [#34628](https://github.com/nodejs/node/pull/34628)
- [[`3f55dcd723`](https://github.com/nodejs/node/commit/3f55dcd723)] - **doc**: clarify process.title inconsistencies (Corey Butler) [#34557](https://github.com/nodejs/node/pull/34557)
- [[`6cd9ea82f6`](https://github.com/nodejs/node/commit/6cd9ea82f6)] - **doc**: document the connection event for HTTP2 & TLS servers (Tim Perry) [#34531](https://github.com/nodejs/node/pull/34531)
- [[`0a9389bb1a`](https://github.com/nodejs/node/commit/0a9389bb1a)] - **doc**: mention null special-case for `napi\_typeof` (Renée Kooi) [#34577](https://github.com/nodejs/node/pull/34577)
- [[`10dd7a0eda`](https://github.com/nodejs/node/commit/10dd7a0eda)] - **doc**: add DerekNonGeneric to collaborators (Derek Lewis) [#34602](https://github.com/nodejs/node/pull/34602)
- [[`d7eaf3a027`](https://github.com/nodejs/node/commit/d7eaf3a027)] - **doc**: revise N-API versions matrix text (Rich Trott) [#34566](https://github.com/nodejs/node/pull/34566)
- [[`e2bea73b03`](https://github.com/nodejs/node/commit/e2bea73b03)] - **doc**: clarify N-API version 1 (Michael Dawson) [#34344](https://github.com/nodejs/node/pull/34344)
- [[`be23e23361`](https://github.com/nodejs/node/commit/be23e23361)] - **doc**: use consistent spelling for "falsy" (Rich Trott) [#34545](https://github.com/nodejs/node/pull/34545)
- [[`f393ae9296`](https://github.com/nodejs/node/commit/f393ae9296)] - **doc**: simplify and clarify console.assert() documentation (Rich Trott) [#34544](https://github.com/nodejs/node/pull/34544)
- [[`b69ff2ff60`](https://github.com/nodejs/node/commit/b69ff2ff60)] - **doc**: use consistent capitalization for addons (Rich Trott) [#34536](https://github.com/nodejs/node/pull/34536)
- [[`212d17fa06`](https://github.com/nodejs/node/commit/212d17fa06)] - **doc**: add mmarchini pronouns (Mary Marchini) [#34586](https://github.com/nodejs/node/pull/34586)
- [[`7a28c3d543`](https://github.com/nodejs/node/commit/7a28c3d543)] - **doc**: update mmarchini contact info (Mary Marchini) [#34586](https://github.com/nodejs/node/pull/34586)
- [[`c8104f3d10`](https://github.com/nodejs/node/commit/c8104f3d10)] - **doc**: update .mailmap for mmarchini (Mary Marchini) [#34586](https://github.com/nodejs/node/pull/34586)
- [[`692a735881`](https://github.com/nodejs/node/commit/692a735881)] - **doc**: use sentence-case for headers in SECURITY.md (Rich Trott) [#34525](https://github.com/nodejs/node/pull/34525)
- [[`44e6c010b4`](https://github.com/nodejs/node/commit/44e6c010b4)] - **esm**: fix hook mistypes and links to types (Derek Lewis) [#34240](https://github.com/nodejs/node/pull/34240)
- [[`7322e58d11`](https://github.com/nodejs/node/commit/7322e58d11)] - **http**: reset headers timeout on headers complete (Robert Nagy) [#34578](https://github.com/nodejs/node/pull/34578)
- [[`36fd3daae6`](https://github.com/nodejs/node/commit/36fd3daae6)] - **http**: provide keep-alive timeout response header (Robert Nagy) [#34561](https://github.com/nodejs/node/pull/34561)
- [[`d0efaf2fe3`](https://github.com/nodejs/node/commit/d0efaf2fe3)] - **lib**: use non-symbols in isURLInstance check (Shelley Vohr) [#34622](https://github.com/nodejs/node/pull/34622)
- [[`335cb0d1d1`](https://github.com/nodejs/node/commit/335cb0d1d1)] - **lib**: absorb `path` error cases (Gireesh Punathil) [#34519](https://github.com/nodejs/node/pull/34519)
- [[`521e620533`](https://github.com/nodejs/node/commit/521e620533)] - **meta**: uncomment all codeowners (Mary Marchini) [#34670](https://github.com/nodejs/node/pull/34670)
- [[`650adeca22`](https://github.com/nodejs/node/commit/650adeca22)] - **meta**: enable http2 team for CODEOWNERS (Rich Trott) [#34534](https://github.com/nodejs/node/pull/34534)
- [[`35ef9907aa`](https://github.com/nodejs/node/commit/35ef9907aa)] - **module**: handle Top-Level Await non-fulfills better (Anna Henningsen) [#34640](https://github.com/nodejs/node/pull/34640)
- [[`62bb2e757f`](https://github.com/nodejs/node/commit/62bb2e757f)] - **(SEMVER-MINOR)** **module**: unflag Top-Level Await (Myles Borins) [#34558](https://github.com/nodejs/node/pull/34558)
- [[`fbd411d28a`](https://github.com/nodejs/node/commit/fbd411d28a)] - **n-api**: fix use-after-free with napi_remove_async_cleanup_hook (Anna Henningsen) [#34662](https://github.com/nodejs/node/pull/34662)
- [[`8cc9e5eb52`](https://github.com/nodejs/node/commit/8cc9e5eb52)] - **(SEMVER-MINOR)** **n-api**: support type-tagging objects (Gabriel Schulhof) [#28237](https://github.com/nodejs/node/pull/28237)
- [[`2703fe498e`](https://github.com/nodejs/node/commit/2703fe498e)] - **n-api**: simplify bigint-from-word creation (Gabriel Schulhof) [#34554](https://github.com/nodejs/node/pull/34554)
- [[`e89ec46ba9`](https://github.com/nodejs/node/commit/e89ec46ba9)] - **(SEMVER-MINOR)** **n-api,src**: provide asynchronous cleanup hooks (Anna Henningsen) [#34572](https://github.com/nodejs/node/pull/34572)
- [[`b1890e0866`](https://github.com/nodejs/node/commit/b1890e0866)] - **net**: don't return the stream object from onStreamRead (Robey Pointer) [#34375](https://github.com/nodejs/node/pull/34375)
- [[`35fdfb44a2`](https://github.com/nodejs/node/commit/35fdfb44a2)] - **policy**: increase tests via permutation matrix (Bradley Meck) [#34404](https://github.com/nodejs/node/pull/34404)
- [[`ddd339ff45`](https://github.com/nodejs/node/commit/ddd339ff45)] - **repl**: use \_Node.js\_ in user-facing REPL text (Rich Trott) [#34644](https://github.com/nodejs/node/pull/34644)
- [[`276e2980e2`](https://github.com/nodejs/node/commit/276e2980e2)] - **repl**: use \_REPL\_ in user-facing text (Rich Trott) [#34643](https://github.com/nodejs/node/pull/34643)
- [[`465c262ac6`](https://github.com/nodejs/node/commit/465c262ac6)] - **repl**: improve static import error message in repl (Myles Borins) [#33588](https://github.com/nodejs/node/pull/33588)
- [[`12cb0fb8a0`](https://github.com/nodejs/node/commit/12cb0fb8a0)] - **repl**: give repl entries unique names (Bradley Meck) [#34372](https://github.com/nodejs/node/pull/34372)
- [[`2dbd15a075`](https://github.com/nodejs/node/commit/2dbd15a075)] - **src**: fix linter failures (Anna Henningsen) [#34582](https://github.com/nodejs/node/pull/34582)
- [[`2761f349ec`](https://github.com/nodejs/node/commit/2761f349ec)] - **src**: spin shutdown loop while immediates are pending (Anna Henningsen) [#34662](https://github.com/nodejs/node/pull/34662)
- [[`39ca48c840`](https://github.com/nodejs/node/commit/39ca48c840)] - **src**: fix `size` underflow in CallbackQueue (Anna Henningsen) [#34662](https://github.com/nodejs/node/pull/34662)
- [[`c1abc8d3e5`](https://github.com/nodejs/node/commit/c1abc8d3e5)] - **src**: fix unused namespace member in node_util (Andrey Pechkurov) [#34565](https://github.com/nodejs/node/pull/34565)
- [[`e146686972`](https://github.com/nodejs/node/commit/e146686972)] - **test**: fix wrong method call (gengjiawen) [#34629](https://github.com/nodejs/node/pull/34629)
- [[`ca89c375f7`](https://github.com/nodejs/node/commit/ca89c375f7)] - **test**: add debugging for callbacks in test-https-foafssl.js (Rich Trott) [#34603](https://github.com/nodejs/node/pull/34603)
- [[`2133b18bee`](https://github.com/nodejs/node/commit/2133b18bee)] - **test**: add debugging for test-https-foafssl.js (Rich Trott) [#34603](https://github.com/nodejs/node/pull/34603)
- [[`b9fb0c63b3`](https://github.com/nodejs/node/commit/b9fb0c63b3)] - **test**: convert most N-API tests from C++ to C (Gabriel Schulhof) [#34615](https://github.com/nodejs/node/pull/34615)
- [[`54a4c6a39c`](https://github.com/nodejs/node/commit/54a4c6a39c)] - **test**: replace flaky pummel regression tests (Anna Henningsen) [#34530](https://github.com/nodejs/node/pull/34530)
- [[`bd55236788`](https://github.com/nodejs/node/commit/bd55236788)] - **test**: change Fixes: to Refs: (Rich Trott) [#34568](https://github.com/nodejs/node/pull/34568)
- [[`a340587cfd`](https://github.com/nodejs/node/commit/a340587cfd)] - **test**: fix flaky http-parser-timeout-reset (Robert Nagy) [#34609](https://github.com/nodejs/node/pull/34609)
- [[`9c442f9786`](https://github.com/nodejs/node/commit/9c442f9786)] - **test**: remove unneeded flag check in test-vm-memleak (Rich Trott) [#34528](https://github.com/nodejs/node/pull/34528)
- [[`05100e1eec`](https://github.com/nodejs/node/commit/05100e1eec)] - **tools**: fix C++ import checker argument expansion (Anna Henningsen) [#34582](https://github.com/nodejs/node/pull/34582)
- [[`bf6c8aaae3`](https://github.com/nodejs/node/commit/bf6c8aaae3)] - **tools**: update ESLint to 7.6.0 (Colin Ihrig) [#34589](https://github.com/nodejs/node/pull/34589)
- [[`0b1616c2f0`](https://github.com/nodejs/node/commit/0b1616c2f0)] - **tools**: add meta.fixable to fixable lint rules (Colin Ihrig) [#34589](https://github.com/nodejs/node/pull/34589)
- [[`f46649bc5b`](https://github.com/nodejs/node/commit/f46649bc5b)] - **util**: print External address from inspect (unknown) [#34398](https://github.com/nodejs/node/pull/34398)
- [[`2fa24c0ccc`](https://github.com/nodejs/node/commit/2fa24c0ccc)] - **wasi**: add \_\_wasi_fd_filestat_set_times() test (Colin Ihrig) [#34623](https://github.com/nodejs/node/pull/34623)

Windows 32-bit Installer: https://nodejs.org/dist/v14.8.0/node-v14.8.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v14.8.0/node-v14.8.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v14.8.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v14.8.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v14.8.0/node-v14.8.0.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v14.8.0/node-v14.8.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v14.8.0/node-v14.8.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v14.8.0/node-v14.8.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v14.8.0/node-v14.8.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v14.8.0/node-v14.8.0-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v14.8.0/node-v14.8.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v14.8.0/node-v14.8.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v14.8.0/node-v14.8.0.tar.gz \
Other release files: https://nodejs.org/dist/v14.8.0/ \
Documentation: https://nodejs.org/docs/v14.8.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

104da9a7138aad8d825a4ced0197529956d5bac11f0c6f443a49360d7cf5556f  node-v14.8.0-aix-ppc64.tar.gz
b6db32f2ff37475ae68502c76fc777a604cbc589bf57158fb4eed4db9ac5f62d  node-v14.8.0-darwin-x64.tar.gz
9b3df5ac8a3b47881b619722ab1b4f44cc3b7f46cdd0c51010258d68b74e9f14  node-v14.8.0-darwin-x64.tar.xz
e5d23078e24e7d45dad035565b3449b8b67a17717374eaf9070f4fcd36062ebd  node-v14.8.0-headers.tar.gz
32eea1339ebbe5c8eb1fc09e89baee158ac44b7eab4668d3b303d54b9c97412e  node-v14.8.0-headers.tar.xz
ab2e44354f7032a9b3f2e02d078596afb6d9822df8a1e672634d66126d17df7a  node-v14.8.0-linux-arm64.tar.gz
0c66a6468c36552c00d45cff0eaa924240f3d2e625be0306f33f8b0d81af4224  node-v14.8.0-linux-arm64.tar.xz
3ec34c75338f608ce9034395c03d06306d53938576172a7d769495ebf8ff512b  node-v14.8.0-linux-armv7l.tar.gz
95e5ce77f1db780c19d2f11f1dc2792062128b5b2bb84d08a0648b5a8a379a9b  node-v14.8.0-linux-armv7l.tar.xz
f01ccc5f957a520374a7ab2c72d755256d583a22f3266fb79c646d7ce9d23ec9  node-v14.8.0-linux-ppc64le.tar.gz
ae63a8fa02f1a75b7be43795391256c6f94ed255a6f543884d67480ecb535d21  node-v14.8.0-linux-ppc64le.tar.xz
a80e2cc9d04bf4f617ee220897e1c31af40bd61aa051b154c7d032940d3313a3  node-v14.8.0-linux-s390x.tar.gz
cab9fa8eb01d40e8078459f6463ed1f655cbab6a1f346273c6ab4cabf9d85245  node-v14.8.0-linux-s390x.tar.xz
4bc595057f51ce04fdb25a5ef0cee2b7a567e7380806c281294727a4d9bfcfb0  node-v14.8.0-linux-x64.tar.gz
c7761fe5d56d045d1540b1f0bc8a20d7edf03e6fd695ee5fbffc1dd9416ccc75  node-v14.8.0-linux-x64.tar.xz
cc4f577eff7705b0554c712a7ff6c0d2bfeab0483123a8f211c89c5a0fc752b7  node-v14.8.0.pkg
6552a640a8b1a34f4ad00b4147eed6432386b304dcb2f5d73a659258ed2a8cf2  node-v14.8.0.tar.gz
9b9e68e4e641ab099b3fe2d49308c65820eebe60ed733b5f8b07c67adef9f06d  node-v14.8.0.tar.xz
4ea463f37e748fbd3a6423c7ff5a59b24ee1318226e76286816bc1094b23e311  node-v14.8.0-win-x64.7z
848ca582bdf8b7fdc21a38d9f3887a45bdf6381b04549fc0f918852889157c9a  node-v14.8.0-win-x64.zip
b56aec1b4133fd16b77cd15ffad6539ba38964e5a36efcdbb29e7c79722dc94b  node-v14.8.0-win-x86.7z
85c1c81027d995b2e7d365bb9ddc1982b3b14d2d94841850fb725264076fe487  node-v14.8.0-win-x86.zip
2c7e2d179f2e5d91fadcb8adbc93c33b2b8c0401c7c2097ab8f63734caa6f99a  node-v14.8.0-x64.msi
5990b0d1d1e97a1e2d7d57d5556c0aa926e3ba5ea116c8b854f6b808c26088b7  node-v14.8.0-x86.msi
4868ac5e1f1d564343686139e4937ce11b3fe3117f1725e2de6b2d678003e730  win-x64/node.exe
2a0fe0b9b07c3b6a35d9b188ce165e41868225683617cd1d910cb7aaa5405972  win-x64/node.lib
ce1dc40d69acf4703b498e8c99bab9cf6a69fb0ef8624c95f4b25026ee7e386e  win-x64/node_pdb.7z
20c68f40451cb9a860ff3e81df2d531e5f3aed6df9ccfab2aa68e41b00299dd0  win-x64/node_pdb.zip
58763376e6ad9f1254f0e12223126a2f418c38c24c79c5a6531c4c4a721dcf77  win-x86/node.exe
0821162e484e64aac30126cbc3def68b44cf6830e4622bc9eb6587230685afdc  win-x86/node.lib
e2c0628df272bdda1c717d55067ce121b028fd829bdef02caa72ab4ef3aee141  win-x86/node_pdb.7z
f17f4f74d26fc2dc658c6c488c863780635726780ea9776b4d9486c4191aad9c  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEueL1mBqm4M0oFg2f8TmTp1WZZTwFAl8yysMACgkQ8TmTp1WZ
ZTwyqw//SaEm5nggcwnzGYTMZQ7cXyo3aiNMKOjr2VtqxBcOF8zJm/UgrMMYdUYN
Q4DUg2mOgTufPwGHFAutRonXjX0FYytITtxbpJ794Ko1agoegNBN8d2oYJlKRpEi
mJ+ELNB9MgyJ+RJ4KWlV6UmfuPPluDnEWpZZ2Izjw6kpoKY4OSG2b7OQ4rxlKstq
dpUvX1HC8Dbjr2/mDM7f1xk2LuY7W9jibVSPRD38DebsmIrcgipQy6bE4s8iLYk/
LX+eK7vCvJADvwzkXf5M6Y+4Hxz7datPkrC9ARrIQs2nVHqn2AuU3c2R7hQz460E
cOYJTzyl7f1Tu3eahtNWF3iKnyWma6w2r6r89h3SBrm7qUxChBVXiucxpf90W/MZ
/H+eAg906U7YHR+ipiGpIPmROtrJ0g1jtMnmXWmywa09BjHdrfsBbDoKF1clBViA
CGXIu9b5JTxhIp8qPFKYiGXx9qupE0gb4G1AsAPeIuW9fX9nODwjhJ/4wpsoiSse
xriTZ9fulQXRteO+OfNFj1QJh3afZKDiV2hcZz3ao5HDGkHSu6NtnwIx3+Hvtj7M
bo/DGBfHYkSNZjAT0XOF6n7TkO6pK7JvdfwBiUOlb9ZYsfSJiSrWX9oory6hGZR3
mcPSQAED3Pla3XkxXGXQBDzWeDug2phZzHTrhf+POQsnEppSdMw=
=a2vm
-----END PGP SIGNATURE-----

```
