---
date: '2021-09-28T11:34:52.834Z'
category: release
title: Node v14.18.0 (LTS)
layout: blog-post
author: <PERSON><PERSON><PERSON>
---

### Notable Changes

- [[`3a60de0135`](https://github.com/nodejs/node/commit/3a60de0135)] - **assert**: change status of legacy asserts (<PERSON>) [#38113](https://github.com/nodejs/node/pull/38113)
- [[`df37c106a7`](https://github.com/nodejs/node/commit/df37c106a7)] - **(SEMVER-MINOR)** **buffer**: introduce Blob (<PERSON>) [#36811](https://github.com/nodejs/node/pull/36811)
- [[`223494c548`](https://github.com/nodejs/node/commit/223494c548)] - **(SEMVER-MINOR)** **buffer**: add base64url encoding option (<PERSON><PERSON>) [#36952](https://github.com/nodejs/node/pull/36952)
- [[`14fc4ddabc`](https://github.com/nodejs/node/commit/14fc4ddabc)] - **(SEMVER-MINOR)** **child_process**: allow `options.cwd` receive a URL (Khaidi Chu) [#38862](https://github.com/nodejs/node/pull/38862)
- [[`b68b13acb3`](https://github.com/nodejs/node/commit/b68b13acb3)] - **(SEMVER-MINOR)** **child_process**: add timeout to spawn and fork (Nitzan Uziely) [#37256](https://github.com/nodejs/node/pull/37256)
- [[`da98c9f99b`](https://github.com/nodejs/node/commit/da98c9f99b)] - **(SEMVER-MINOR)** **child_process**: allow promisified exec to be cancel (Carlos Fuentes) [#34249](https://github.com/nodejs/node/pull/34249)
- [[`779310ac87`](https://github.com/nodejs/node/commit/779310ac87)] - **(SEMVER-MINOR)** **child_process**: add 'overlapped' stdio flag (Thiago Padilha) [#29412](https://github.com/nodejs/node/pull/29412)
- [[`40eb3b79f1`](https://github.com/nodejs/node/commit/40eb3b79f1)] - **(SEMVER-MINOR)** **cli**: add -C alias for --conditions flag (Guy Bedford) [#38755](https://github.com/nodejs/node/pull/38755)
- [[`39eba0a2e1`](https://github.com/nodejs/node/commit/39eba0a2e1)] - **(SEMVER-MINOR)** **cli**: add --node-memory-debug option (Anna Henningsen) [#35537](https://github.com/nodejs/node/pull/35537)
- [[`d8d9a9628a`](https://github.com/nodejs/node/commit/d8d9a9628a)] - **(SEMVER-MINOR)** **dns**: add "tries" option to Resolve options (Luan Devecchi) [#39610](https://github.com/nodejs/node/pull/39610)
- [[`15ba19b020`](https://github.com/nodejs/node/commit/15ba19b020)] - **(SEMVER-MINOR)** **dns**: allow `--dns-result-order` to change default dns verbatim (Ouyang Yadong) [#38099](https://github.com/nodejs/node/pull/38099)
- [[`307c1d817f`](https://github.com/nodejs/node/commit/307c1d817f)] - **doc**: refactor fs docs structure (James M Snell) [#37170](https://github.com/nodejs/node/pull/37170)
- [[`9ee3f77e32`](https://github.com/nodejs/node/commit/9ee3f77e32)] - **(SEMVER-MINOR)** **errors**: remove experimental from --enable-source-maps (Benjamin Coe) [#37362](https://github.com/nodejs/node/pull/37362)
- [[`e73bfed2f4`](https://github.com/nodejs/node/commit/e73bfed2f4)] - **esm**: deprecate legacy main lookup for modules (Guy Bedford) [#36918](https://github.com/nodejs/node/pull/36918)
- [[`989c204a58`](https://github.com/nodejs/node/commit/989c204a58)] - **(SEMVER-MINOR)** **fs**: allow empty string for temp directory prefix (Voltrex) [#39028](https://github.com/nodejs/node/pull/39028)
- [[`ef72490cde`](https://github.com/nodejs/node/commit/ef72490cde)] - **(SEMVER-MINOR)** **fs**: allow no-params fsPromises fileHandle read (Nitzan Uziely) [#38287](https://github.com/nodejs/node/pull/38287)
- [[`cad9d20f64`](https://github.com/nodejs/node/commit/cad9d20f64)] - **(SEMVER-MINOR)** **fs**: add support for async iterators to `fsPromises.writeFile` (HiroyukiYagihashi) [#37490](https://github.com/nodejs/node/pull/37490)
- [[`2b0e2706c0`](https://github.com/nodejs/node/commit/2b0e2706c0)] - **fs**: improve fsPromises readFile performance (Nitzan Uziely) [#37608](https://github.com/nodejs/node/pull/37608)
- [[`fe12cc07b3`](https://github.com/nodejs/node/commit/fe12cc07b3)] - **(SEMVER-MINOR)** **fs**: add fsPromises.watch() (James M Snell) [#37179](https://github.com/nodejs/node/pull/37179)
- [[`2459c115a8`](https://github.com/nodejs/node/commit/2459c115a8)] - **(SEMVER-MINOR)** **fs**: allow `position` parameter to be a `BigInt` in read and readSync (Darshan Sen) [#36190](https://github.com/nodejs/node/pull/36190)
- [[`6544cfb4b9`](https://github.com/nodejs/node/commit/6544cfb4b9)] - **(SEMVER-MINOR)** **http2**: add support for sensitive headers (Anna Henningsen) [#34145](https://github.com/nodejs/node/pull/34145)
- [[`a6c6cbb4e6`](https://github.com/nodejs/node/commit/a6c6cbb4e6)] - **(SEMVER-MINOR)** **http2**: allow setting the local window size of a session (Yongsheng Zhang) [#35978](https://github.com/nodejs/node/pull/35978)
- [[`1e5aca550c`](https://github.com/nodejs/node/commit/1e5aca550c)] - **inspector**: mark as stable (Gireesh Punathil) [#37748](https://github.com/nodejs/node/pull/37748)
- [[`93af04afbb`](https://github.com/nodejs/node/commit/93af04afbb)] - **(SEMVER-MINOR)** **module**: add support for `URL` to `import.meta.resolve` (Antoine du Hamel) [#38587](https://github.com/nodejs/node/pull/38587)
- [[`f9f9389d83`](https://github.com/nodejs/node/commit/f9f9389d83)] - **(SEMVER-MINOR)** **module**: add support for `node:`‑prefixed `require(…)` calls (ExE Boss) [#37246](https://github.com/nodejs/node/pull/37246)
- [[`87c71065eb`](https://github.com/nodejs/node/commit/87c71065eb)] - **(SEMVER-MINOR)** **net**: introduce net.BlockList (James M Snell) [#34625](https://github.com/nodejs/node/pull/34625)
- [[`b421d99a48`](https://github.com/nodejs/node/commit/b421d99a48)] - **(SEMVER-MINOR)** **node-api**: allow retrieval of add-on file name (Gabriel Schulhof) [#37195](https://github.com/nodejs/node/pull/37195)
- [[`6a4811df8a`](https://github.com/nodejs/node/commit/6a4811df8a)] - **(SEMVER-MINOR)** **os**: add os.devNull (Luigi Pinca) [#38569](https://github.com/nodejs/node/pull/38569)
- [[`4a88ddeeca`](https://github.com/nodejs/node/commit/4a88ddeeca)] - **(SEMVER-MINOR)** **perf_hooks**: introduce createHistogram (James M Snell) [#37155](https://github.com/nodejs/node/pull/37155)
- [[`1a6bf1c4a3`](https://github.com/nodejs/node/commit/1a6bf1c4a3)] - **(SEMVER-MINOR)** **process**: add api to enable source-maps programmatically (legendecas) [#39085](https://github.com/nodejs/node/pull/39085)
- [[`99735a6fe8`](https://github.com/nodejs/node/commit/99735a6fe8)] - **(SEMVER-MINOR)** **process**: add `'worker'` event (James M Snell) [#38659](https://github.com/nodejs/node/pull/38659)
- [[`3982919317`](https://github.com/nodejs/node/commit/3982919317)] - **(SEMVER-MINOR)** **process**: add direct access to rss without iterating pages (Adrien Maret) [#34291](https://github.com/nodejs/node/pull/34291)
- [[`526e6c7bde`](https://github.com/nodejs/node/commit/526e6c7bde)] - **(SEMVER-MINOR)** **readline**: add AbortSignal support to interface (Nitzan Uziely) [#37932](https://github.com/nodejs/node/pull/37932)
- [[`e6eee08692`](https://github.com/nodejs/node/commit/e6eee08692)] - **(SEMVER-MINOR)** **readline**: add support for the AbortController to the question method (Mattias Runge-Broberg) [#33676](https://github.com/nodejs/node/pull/33676)
- [[`32de361d70`](https://github.com/nodejs/node/commit/32de361d70)] - **(SEMVER-MINOR)** **readline**: add history event and option to set initial history (Mattias Runge-Broberg) [#33662](https://github.com/nodejs/node/pull/33662)
- [[`797f7f8a38`](https://github.com/nodejs/node/commit/797f7f8a38)] - **(SEMVER-MINOR)** **repl**: add auto‑completion for `node:`‑prefixed `require(…)` calls (ExE Boss) [#37246](https://github.com/nodejs/node/pull/37246)
- [[`abfd71b64c`](https://github.com/nodejs/node/commit/abfd71b64c)] - **(SEMVER-MINOR)** **src**: call overload ctor from the original ctor (Darshan Sen) [#39768](https://github.com/nodejs/node/pull/39768)
- [[`1efae01b18`](https://github.com/nodejs/node/commit/1efae01b18)] - **(SEMVER-MINOR)** **src**: add a constructor overload for CallbackScope (Darshan Sen) [#39768](https://github.com/nodejs/node/pull/39768)
- [[`f7933804ba`](https://github.com/nodejs/node/commit/f7933804ba)] - **(SEMVER-MINOR)** **src**: allow to negate boolean CLI flags (Michaël Zasso) [#39023](https://github.com/nodejs/node/pull/39023)
- [[`6d06ac2202`](https://github.com/nodejs/node/commit/6d06ac2202)] - **(SEMVER-MINOR)** **src**: add --heapsnapshot-near-heap-limit option (Joyee Cheung) [#33010](https://github.com/nodejs/node/pull/33010)
- [[`577d228ca0`](https://github.com/nodejs/node/commit/577d228ca0)] - **(SEMVER-MINOR)** **src**: add way to get IsolateData and allocator from Environment (Anna Henningsen) [#36441](https://github.com/nodejs/node/pull/36441)
- [[`658a266cd4`](https://github.com/nodejs/node/commit/658a266cd4)] - **(SEMVER-MINOR)** **src**: allow preventing SetPrepareStackTraceCallback (Shelley Vohr) [#36447](https://github.com/nodejs/node/pull/36447)
- [[`f421422ea4`](https://github.com/nodejs/node/commit/f421422ea4)] - **(SEMVER-MINOR)** **src**: add maybe versions of EmitExit and EmitBeforeExit (Anna Henningsen) [#35486](https://github.com/nodejs/node/pull/35486)
- [[`a62d4d60f4`](https://github.com/nodejs/node/commit/a62d4d60f4)] - **(SEMVER-MINOR)** **stream**: add readableDidRead if has been read from (Robert Nagy) [#39589](https://github.com/nodejs/node/pull/39589)
- [[`63502131a3`](https://github.com/nodejs/node/commit/63502131a3)] - **(SEMVER-MINOR)** **stream**: pipeline accept Buffer as a valid first argument (Nitzan Uziely) [#37739](https://github.com/nodejs/node/pull/37739)
- [[`68bbebd42c`](https://github.com/nodejs/node/commit/68bbebd42c)] - **(SEMVER-MINOR)** **tls**: allow reading data into a static buffer (Andrey Pechkurov) [#35753](https://github.com/nodejs/node/pull/35753)
- [[`1cbb74d63d`](https://github.com/nodejs/node/commit/1cbb74d63d)] - **(SEMVER-MINOR)** **url**: expose urlToHttpOptions utility (Yongsheng Zhang) [#35960](https://github.com/nodejs/node/pull/35960)
- [[`8eb11356dd`](https://github.com/nodejs/node/commit/8eb11356dd)] - **(SEMVER-MINOR)** **util**: expose toUSVString (Robert Nagy) [#39814](https://github.com/nodejs/node/pull/39814)
- [[`84fcdc3074`](https://github.com/nodejs/node/commit/84fcdc3074)] - **(SEMVER-MINOR)** **v8**: implement v8.stopCoverage() (Joyee Cheung) [#33807](https://github.com/nodejs/node/pull/33807)
- [[`b238b6bf17`](https://github.com/nodejs/node/commit/b238b6bf17)] - **(SEMVER-MINOR)** **v8**: implement v8.takeCoverage() (Joyee Cheung) [#33807](https://github.com/nodejs/node/pull/33807)
- [[`9f6bc58da8`](https://github.com/nodejs/node/commit/9f6bc58da8)] - **(SEMVER-MINOR)** **worker**: add setEnvironmentData/getEnvironmentData (James M Snell) [#37486](https://github.com/nodejs/node/pull/37486)

### Commits

#### Semver-minor commits

- [[`f3563d3197`](https://github.com/nodejs/node/commit/f3563d3197)] - **(SEMVER-MINOR)** **async_hooks**: use new v8::Context PromiseHook API (Stephen Belanger) [#36394](https://github.com/nodejs/node/pull/36394)
- [[`df37c106a7`](https://github.com/nodejs/node/commit/df37c106a7)] - **(SEMVER-MINOR)** **buffer**: introduce Blob (James M Snell) [#36811](https://github.com/nodejs/node/pull/36811)
- [[`223494c548`](https://github.com/nodejs/node/commit/223494c548)] - **(SEMVER-MINOR)** **buffer**: add base64url encoding option (Filip Skokan) [#36952](https://github.com/nodejs/node/pull/36952)
- [[`14fc4ddabc`](https://github.com/nodejs/node/commit/14fc4ddabc)] - **(SEMVER-MINOR)** **child_process**: allow `options.cwd` receive a URL (Khaidi Chu) [#38862](https://github.com/nodejs/node/pull/38862)
- [[`b68b13acb3`](https://github.com/nodejs/node/commit/b68b13acb3)] - **(SEMVER-MINOR)** **child_process**: add timeout to spawn and fork (Nitzan Uziely) [#37256](https://github.com/nodejs/node/pull/37256)
- [[`da98c9f99b`](https://github.com/nodejs/node/commit/da98c9f99b)] - **(SEMVER-MINOR)** **child_process**: allow promisified exec to be cancel (Carlos Fuentes) [#34249](https://github.com/nodejs/node/pull/34249)
- [[`779310ac87`](https://github.com/nodejs/node/commit/779310ac87)] - **(SEMVER-MINOR)** **child_process**: add 'overlapped' stdio flag (Thiago Padilha) [#29412](https://github.com/nodejs/node/pull/29412)
- [[`40eb3b79f1`](https://github.com/nodejs/node/commit/40eb3b79f1)] - **(SEMVER-MINOR)** **cli**: add -C alias for --conditions flag (Guy Bedford) [#38755](https://github.com/nodejs/node/pull/38755)
- [[`39eba0a2e1`](https://github.com/nodejs/node/commit/39eba0a2e1)] - **(SEMVER-MINOR)** **cli**: add --node-memory-debug option (Anna Henningsen) [#35537](https://github.com/nodejs/node/pull/35537)
- [[`d9b58a0262`](https://github.com/nodejs/node/commit/d9b58a0262)] - **(SEMVER-MINOR)** **deps**: V8: cherry-pick fa4cb172cde2 (Stephen Belanger) [#38577](https://github.com/nodejs/node/pull/38577)
- [[`9d7177c152`](https://github.com/nodejs/node/commit/9d7177c152)] - **(SEMVER-MINOR)** **deps**: V8: cherry-pick 4c074516397b (Stephen Belanger) [#36394](https://github.com/nodejs/node/pull/36394)
- [[`ec0f0ef8ef`](https://github.com/nodejs/node/commit/ec0f0ef8ef)] - **(SEMVER-MINOR)** **deps**: V8: cherry-pick 5f4413194480 (Stephen Belanger) [#36394](https://github.com/nodejs/node/pull/36394)
- [[`3e7238e45a`](https://github.com/nodejs/node/commit/3e7238e45a)] - **(SEMVER-MINOR)** **deps**: V8: cherry-pick 272445f10927 (Stephen Belanger) [#36394](https://github.com/nodejs/node/pull/36394)
- [[`214e568597`](https://github.com/nodejs/node/commit/214e568597)] - **(SEMVER-MINOR)** **deps**: V8: backport c0fceaa0669b (Stephen Belanger) [#36394](https://github.com/nodejs/node/pull/36394)
- [[`d8d9a9628a`](https://github.com/nodejs/node/commit/d8d9a9628a)] - **(SEMVER-MINOR)** **dns**: add "tries" option to Resolve options (Luan Devecchi) [#39610](https://github.com/nodejs/node/pull/39610)
- [[`15ba19b020`](https://github.com/nodejs/node/commit/15ba19b020)] - **(SEMVER-MINOR)** **dns**: allow `--dns-result-order` to change default dns verbatim (Ouyang Yadong) [#38099](https://github.com/nodejs/node/pull/38099)
- [[`defb77cac9`](https://github.com/nodejs/node/commit/defb77cac9)] - **(SEMVER-MINOR)** **doc**: add missing change to resolver ctor (Luan Devecchi) [#39610](https://github.com/nodejs/node/pull/39610)
- [[`9ee3f77e32`](https://github.com/nodejs/node/commit/9ee3f77e32)] - **(SEMVER-MINOR)** **errors**: remove experimental from --enable-source-maps (Benjamin Coe) [#37362](https://github.com/nodejs/node/pull/37362)
- [[`989c204a58`](https://github.com/nodejs/node/commit/989c204a58)] - **(SEMVER-MINOR)** **fs**: allow empty string for temp directory prefix (Voltrex) [#39028](https://github.com/nodejs/node/pull/39028)
- [[`ef72490cde`](https://github.com/nodejs/node/commit/ef72490cde)] - **(SEMVER-MINOR)** **fs**: allow no-params fsPromises fileHandle read (Nitzan Uziely) [#38287](https://github.com/nodejs/node/pull/38287)
- [[`cad9d20f64`](https://github.com/nodejs/node/commit/cad9d20f64)] - **(SEMVER-MINOR)** **fs**: add support for async iterators to `fsPromises.writeFile` (HiroyukiYagihashi) [#37490](https://github.com/nodejs/node/pull/37490)
- [[`fe12cc07b3`](https://github.com/nodejs/node/commit/fe12cc07b3)] - **(SEMVER-MINOR)** **fs**: add fsPromises.watch() (James M Snell) [#37179](https://github.com/nodejs/node/pull/37179)
- [[`2459c115a8`](https://github.com/nodejs/node/commit/2459c115a8)] - **(SEMVER-MINOR)** **fs**: allow `position` parameter to be a `BigInt` in read and readSync (Darshan Sen) [#36190](https://github.com/nodejs/node/pull/36190)
- [[`6544cfb4b9`](https://github.com/nodejs/node/commit/6544cfb4b9)] - **(SEMVER-MINOR)** **http2**: add support for sensitive headers (Anna Henningsen) [#34145](https://github.com/nodejs/node/pull/34145)
- [[`a6c6cbb4e6`](https://github.com/nodejs/node/commit/a6c6cbb4e6)] - **(SEMVER-MINOR)** **http2**: allow setting the local window size of a session (Yongsheng Zhang) [#35978](https://github.com/nodejs/node/pull/35978)
- [[`93af04afbb`](https://github.com/nodejs/node/commit/93af04afbb)] - **(SEMVER-MINOR)** **module**: add support for `URL` to `import.meta.resolve` (Antoine du Hamel) [#38587](https://github.com/nodejs/node/pull/38587)
- [[`f9f9389d83`](https://github.com/nodejs/node/commit/f9f9389d83)] - **(SEMVER-MINOR)** **module**: add support for `node:`‑prefixed `require(…)` calls (ExE Boss) [#37246](https://github.com/nodejs/node/pull/37246)
- [[`76d4f22bab`](https://github.com/nodejs/node/commit/76d4f22bab)] - **(SEMVER-MINOR)** **net**: allow net.BlockList to use net.SocketAddress objects (James M Snell) [#37917](https://github.com/nodejs/node/pull/37917)
- [[`82363d864d`](https://github.com/nodejs/node/commit/82363d864d)] - **(SEMVER-MINOR)** **net**: add SocketAddress class (James M Snell) [#37917](https://github.com/nodejs/node/pull/37917)
- [[`0202ba46b8`](https://github.com/nodejs/node/commit/0202ba46b8)] - **(SEMVER-MINOR)** **net**: make net.BlockList cloneable (James M Snell) [#37917](https://github.com/nodejs/node/pull/37917)
- [[`a41a3e3b3f`](https://github.com/nodejs/node/commit/a41a3e3b3f)] - **(SEMVER-MINOR)** **net**: make blocklist family case insensitive (James M Snell) [#34864](https://github.com/nodejs/node/pull/34864)
- [[`87c71065eb`](https://github.com/nodejs/node/commit/87c71065eb)] - **(SEMVER-MINOR)** **net**: introduce net.BlockList (James M Snell) [#34625](https://github.com/nodejs/node/pull/34625)
- [[`b421d99a48`](https://github.com/nodejs/node/commit/b421d99a48)] - **(SEMVER-MINOR)** **node-api**: allow retrieval of add-on file name (Gabriel Schulhof) [#37195](https://github.com/nodejs/node/pull/37195)
- [[`6a4811df8a`](https://github.com/nodejs/node/commit/6a4811df8a)] - **(SEMVER-MINOR)** **os**: add os.devNull (Luigi Pinca) [#38569](https://github.com/nodejs/node/pull/38569)
- [[`4a88ddeeca`](https://github.com/nodejs/node/commit/4a88ddeeca)] - **(SEMVER-MINOR)** **perf_hooks**: introduce createHistogram (James M Snell) [#37155](https://github.com/nodejs/node/pull/37155)
- [[`1a6bf1c4a3`](https://github.com/nodejs/node/commit/1a6bf1c4a3)] - **(SEMVER-MINOR)** **process**: add api to enable source-maps programmatically (legendecas) [#39085](https://github.com/nodejs/node/pull/39085)
- [[`99735a6fe8`](https://github.com/nodejs/node/commit/99735a6fe8)] - **(SEMVER-MINOR)** **process**: add `'worker'` event (James M Snell) [#38659](https://github.com/nodejs/node/pull/38659)
- [[`3982919317`](https://github.com/nodejs/node/commit/3982919317)] - **(SEMVER-MINOR)** **process**: add direct access to rss without iterating pages (Adrien Maret) [#34291](https://github.com/nodejs/node/pull/34291)
- [[`526e6c7bde`](https://github.com/nodejs/node/commit/526e6c7bde)] - **(SEMVER-MINOR)** **readline**: add AbortSignal support to interface (Nitzan Uziely) [#37932](https://github.com/nodejs/node/pull/37932)
- [[`e6eee08692`](https://github.com/nodejs/node/commit/e6eee08692)] - **(SEMVER-MINOR)** **readline**: add support for the AbortController to the question method (Mattias Runge-Broberg) [#33676](https://github.com/nodejs/node/pull/33676)
- [[`32de361d70`](https://github.com/nodejs/node/commit/32de361d70)] - **(SEMVER-MINOR)** **readline**: add history event and option to set initial history (Mattias Runge-Broberg) [#33662](https://github.com/nodejs/node/pull/33662)
- [[`797f7f8a38`](https://github.com/nodejs/node/commit/797f7f8a38)] - **(SEMVER-MINOR)** **repl**: add auto‑completion for `node:`‑prefixed `require(…)` calls (ExE Boss) [#37246](https://github.com/nodejs/node/pull/37246)
- [[`abfd71b64c`](https://github.com/nodejs/node/commit/abfd71b64c)] - **(SEMVER-MINOR)** **src**: call overload ctor from the original ctor (Darshan Sen) [#39768](https://github.com/nodejs/node/pull/39768)
- [[`1efae01b18`](https://github.com/nodejs/node/commit/1efae01b18)] - **(SEMVER-MINOR)** **src**: add a constructor overload for CallbackScope (Darshan Sen) [#39768](https://github.com/nodejs/node/pull/39768)
- [[`1aa2080d29`](https://github.com/nodejs/node/commit/1aa2080d29)] - **(SEMVER-MINOR)** **src**: fix align in cares_wrap.h (Luan) [#39610](https://github.com/nodejs/node/pull/39610)
- [[`f7933804ba`](https://github.com/nodejs/node/commit/f7933804ba)] - **(SEMVER-MINOR)** **src**: allow to negate boolean CLI flags (Michaël Zasso) [#39023](https://github.com/nodejs/node/pull/39023)
- [[`6d06ac2202`](https://github.com/nodejs/node/commit/6d06ac2202)] - **(SEMVER-MINOR)** **src**: add --heapsnapshot-near-heap-limit option (Joyee Cheung) [#33010](https://github.com/nodejs/node/pull/33010)
- [[`4091eb9db7`](https://github.com/nodejs/node/commit/4091eb9db7)] - **(SEMVER-MINOR)** **src**: move node_binding to modern THROW_ERR\* (James M Snell) [#35469](https://github.com/nodejs/node/pull/35469)
- [[`577d228ca0`](https://github.com/nodejs/node/commit/577d228ca0)] - **(SEMVER-MINOR)** **src**: add way to get IsolateData and allocator from Environment (Anna Henningsen) [#36441](https://github.com/nodejs/node/pull/36441)
- [[`658a266cd4`](https://github.com/nodejs/node/commit/658a266cd4)] - **(SEMVER-MINOR)** **src**: allow preventing SetPrepareStackTraceCallback (Shelley Vohr) [#36447](https://github.com/nodejs/node/pull/36447)
- [[`f421422ea4`](https://github.com/nodejs/node/commit/f421422ea4)] - **(SEMVER-MINOR)** **src**: add maybe versions of EmitExit and EmitBeforeExit (Anna Henningsen) [#35486](https://github.com/nodejs/node/pull/35486)
- [[`a62d4d60f4`](https://github.com/nodejs/node/commit/a62d4d60f4)] - **(SEMVER-MINOR)** **stream**: add readableDidRead if has been read from (Robert Nagy) [#39589](https://github.com/nodejs/node/pull/39589)
- [[`63502131a3`](https://github.com/nodejs/node/commit/63502131a3)] - **(SEMVER-MINOR)** **stream**: pipeline accept Buffer as a valid first argument (Nitzan Uziely) [#37739](https://github.com/nodejs/node/pull/37739)
- [[`72ef41c72b`](https://github.com/nodejs/node/commit/72ef41c72b)] - **(SEMVER-MINOR)** **test**: add wpt tests for Blob (Michaël Zasso) [#36811](https://github.com/nodejs/node/pull/36811)
- [[`68bbebd42c`](https://github.com/nodejs/node/commit/68bbebd42c)] - **(SEMVER-MINOR)** **tls**: allow reading data into a static buffer (Andrey Pechkurov) [#35753](https://github.com/nodejs/node/pull/35753)
- [[`587deacad9`](https://github.com/nodejs/node/commit/587deacad9)] - **(SEMVER-MINOR)** **tools**: add `Worker` to type-parser (James M Snell) [#38659](https://github.com/nodejs/node/pull/38659)
- [[`1cbb74d63d`](https://github.com/nodejs/node/commit/1cbb74d63d)] - **(SEMVER-MINOR)** **url**: expose urlToHttpOptions utility (Yongsheng Zhang) [#35960](https://github.com/nodejs/node/pull/35960)
- [[`8eb11356dd`](https://github.com/nodejs/node/commit/8eb11356dd)] - **(SEMVER-MINOR)** **util**: expose toUSVString (Robert Nagy) [#39814](https://github.com/nodejs/node/pull/39814)
- [[`84fcdc3074`](https://github.com/nodejs/node/commit/84fcdc3074)] - **(SEMVER-MINOR)** **v8**: implement v8.stopCoverage() (Joyee Cheung) [#33807](https://github.com/nodejs/node/pull/33807)
- [[`b238b6bf17`](https://github.com/nodejs/node/commit/b238b6bf17)] - **(SEMVER-MINOR)** **v8**: implement v8.takeCoverage() (Joyee Cheung) [#33807](https://github.com/nodejs/node/pull/33807)
- [[`9f6bc58da8`](https://github.com/nodejs/node/commit/9f6bc58da8)] - **(SEMVER-MINOR)** **worker**: add setEnvironmentData/getEnvironmentData (James M Snell) [#37486](https://github.com/nodejs/node/pull/37486)

#### Semver-patch commits

- [[`3a60de0135`](https://github.com/nodejs/node/commit/3a60de0135)] - **assert**: change status of legacy asserts (James M Snell) [#38113](https://github.com/nodejs/node/pull/38113)
- [[`5a42be9719`](https://github.com/nodejs/node/commit/5a42be9719)] - **async_hooks**: use resource stack for AsyncLocalStorage run (Stephen Belanger) [#39890](https://github.com/nodejs/node/pull/39890)
- [[`fc29ddb38e`](https://github.com/nodejs/node/commit/fc29ddb38e)] - **async_hooks**: emit promise trace events from JS (Stephen Belanger) [#39135](https://github.com/nodejs/node/pull/39135)
- [[`13296d1abf`](https://github.com/nodejs/node/commit/13296d1abf)] - **async_hooks**: eliminate native PromiseHook (Stephen Belanger) [#39135](https://github.com/nodejs/node/pull/39135)
- [[`48e5971e51`](https://github.com/nodejs/node/commit/48e5971e51)] - **async_hooks**: check for empty contexts before removing (Bryan English) [#39095](https://github.com/nodejs/node/pull/39095)
- [[`691c00c48b`](https://github.com/nodejs/node/commit/691c00c48b)] - **async_hooks**: switch between native and context hooks correctly (Stephen Belanger) [#38912](https://github.com/nodejs/node/pull/38912)
- [[`8484ab2a6c`](https://github.com/nodejs/node/commit/8484ab2a6c)] - **buffer**: avoid creating the backing store in the thread (James M Snell) [#37052](https://github.com/nodejs/node/pull/37052)
- [[`c8d039a872`](https://github.com/nodejs/node/commit/c8d039a872)] - **buffer**: make Blob's constructor more spec-compliant (Michaël Zasso) [#37361](https://github.com/nodejs/node/pull/37361)
- [[`05d73ac286`](https://github.com/nodejs/node/commit/05d73ac286)] - **buffer**: make Blob's slice method more spec-compliant (Michaël Zasso) [#37361](https://github.com/nodejs/node/pull/37361)
- [[`e7cf2efc60`](https://github.com/nodejs/node/commit/e7cf2efc60)] - **buffer**: add @@toStringTag to Blob (Colin Ihrig) [#37336](https://github.com/nodejs/node/pull/37336)
- [[`d99deeaf97`](https://github.com/nodejs/node/commit/d99deeaf97)] - **build**: fix update authors commit (Mestery) [#39858](https://github.com/nodejs/node/pull/39858)
- [[`5e1cba81bf`](https://github.com/nodejs/node/commit/5e1cba81bf)] - **build**: add authors.yml (Tierney Cyren) [#35831](https://github.com/nodejs/node/pull/35831)
- [[`ed3c332089`](https://github.com/nodejs/node/commit/ed3c332089)] - **build**: add option to hide console window (Cheng Zhao) [#39712](https://github.com/nodejs/node/pull/39712)
- [[`c696f97c5e`](https://github.com/nodejs/node/commit/c696f97c5e)] - **build**: exclude markdown files from some GitHub Actions (Rich Trott) [#39565](https://github.com/nodejs/node/pull/39565)
- [[`0bd6dd1ee2`](https://github.com/nodejs/node/commit/0bd6dd1ee2)] - **build**: use lts shorthand in GitHub Actions (Rich Trott) [#39538](https://github.com/nodejs/node/pull/39538)
- [[`3482bca643`](https://github.com/nodejs/node/commit/3482bca643)] - **build**: override python executable path on configure (legendecas) [#39465](https://github.com/nodejs/node/pull/39465)
- [[`61261cdb8e`](https://github.com/nodejs/node/commit/61261cdb8e)] - **build**: use Node.js 14 in commit-lint.yml (Rich Trott) [#39506](https://github.com/nodejs/node/pull/39506)
- [[`719f1563c1`](https://github.com/nodejs/node/commit/719f1563c1)] - **build**: fix `host_arch_cc()` for AIX/IBM i (Richard Lau) [#39481](https://github.com/nodejs/node/pull/39481)
- [[`6e06b2ff9d`](https://github.com/nodejs/node/commit/6e06b2ff9d)] - **build**: update coverage Makefile target comments (Richard Lau) [#39365](https://github.com/nodejs/node/pull/39365)
- [[`4e28d2b2c0`](https://github.com/nodejs/node/commit/4e28d2b2c0)] - **build**: run workflows when a PR is ready for review (Michaël Zasso) [#39405](https://github.com/nodejs/node/pull/39405)
- [[`0da5d74da4`](https://github.com/nodejs/node/commit/0da5d74da4)] - **build**: update to setup-node@v2 (Rich Trott) [#39366](https://github.com/nodejs/node/pull/39366)
- [[`f2e1c2267e`](https://github.com/nodejs/node/commit/f2e1c2267e)] - **build**: update gcovr for gcc 8 compatibility (Richard Lau) [#39326](https://github.com/nodejs/node/pull/39326)
- [[`131dd6ec4d`](https://github.com/nodejs/node/commit/131dd6ec4d)] - **build**: remove unused comment in Makefile (LitoMore) [#39171](https://github.com/nodejs/node/pull/39171)
- [[`40e46321b0`](https://github.com/nodejs/node/commit/40e46321b0)] - **build**: uvwasi honours node_shared_libuv (Jérémy Lal) [#39260](https://github.com/nodejs/node/pull/39260)
- [[`5c6ab719f2`](https://github.com/nodejs/node/commit/5c6ab719f2)] - **build**: shorten path used in tarball build workflow (Richard Lau) [#39192](https://github.com/nodejs/node/pull/39192)
- [[`870526374c`](https://github.com/nodejs/node/commit/870526374c)] - **build**: add `library_files` to gyp variables (himself65) [#39293](https://github.com/nodejs/node/pull/39293)
- [[`0e221156aa`](https://github.com/nodejs/node/commit/0e221156aa)] - **build**: pass directory instead of list of files to js2c.py (Joyee Cheung) [#39069](https://github.com/nodejs/node/pull/39069)
- [[`8d8415415b`](https://github.com/nodejs/node/commit/8d8415415b)] - **build**: don't pass `--mode` argument to V8 test-runner (Richard Lau) [#39055](https://github.com/nodejs/node/pull/39055)
- [[`2d50217634`](https://github.com/nodejs/node/commit/2d50217634)] - **build**: fix commit linter on unrebased PRs (Mary Marchini) [#39121](https://github.com/nodejs/node/pull/39121)
- [[`c93d5e006e`](https://github.com/nodejs/node/commit/c93d5e006e)] - **build**: use Actions to validate commit message (Mary Marchini) [#32417](https://github.com/nodejs/node/pull/32417)
- [[`0bcaf9c4d1`](https://github.com/nodejs/node/commit/0bcaf9c4d1)] - **child_process**: fix spawn and fork abort behavior (Nitzan Uziely) [#37325](https://github.com/nodejs/node/pull/37325)
- [[`8010c83180`](https://github.com/nodejs/node/commit/8010c83180)] - **child_process**: fix bad abort signal leak (Nitzan Uziely) [#37257](https://github.com/nodejs/node/pull/37257)
- [[`32aff2f5a0`](https://github.com/nodejs/node/commit/32aff2f5a0)] - **console**: refactor to avoid unsafe array iteration (Antoine du Hamel) [#36753](https://github.com/nodejs/node/pull/36753)
- [[`f46e8cdf79`](https://github.com/nodejs/node/commit/f46e8cdf79)] - **debugger**: remove undefined parameter (Rich Trott) [#39570](https://github.com/nodejs/node/pull/39570)
- [[`482459edd4`](https://github.com/nodejs/node/commit/482459edd4)] - **debugger**: validate sec-websocket-accept response header (Chris Opperwall) [#39357](https://github.com/nodejs/node/pull/39357)
- [[`e9c46107d7`](https://github.com/nodejs/node/commit/e9c46107d7)] - **debugger**: rename internal module (Rich Trott) [#39378](https://github.com/nodejs/node/pull/39378)
- [[`49e0883c75`](https://github.com/nodejs/node/commit/49e0883c75)] - **debugger**: indicate server is ending (Rich Trott) [#39334](https://github.com/nodejs/node/pull/39334)
- [[`72a3419510`](https://github.com/nodejs/node/commit/72a3419510)] - **debugger**: rename inspector-cli test module to debugger (Rich Trott) [#38530](https://github.com/nodejs/node/pull/38530)
- [[`b3352cfba4`](https://github.com/nodejs/node/commit/b3352cfba4)] - **debugger**: prevent simultaneous heap snapshots (Rich Trott) [#39638](https://github.com/nodejs/node/pull/39638)
- [[`e5826ab1c2`](https://github.com/nodejs/node/commit/e5826ab1c2)] - **debugger**: remove final lint exceptions in inspect_repl.js (Rich Trott) [#39078](https://github.com/nodejs/node/pull/39078)
- [[`34c0701952`](https://github.com/nodejs/node/commit/34c0701952)] - **deps**: V8: cherry-pick 00bb1a77c03e (Darshan Sen) [#39829](https://github.com/nodejs/node/pull/39829)
- [[`42359ab582`](https://github.com/nodejs/node/commit/42359ab582)] - **deps**: upgrade to libuv 1.42.0 (Luigi Pinca) [#39525](https://github.com/nodejs/node/pull/39525)
- [[`d863a9db68`](https://github.com/nodejs/node/commit/d863a9db68)] - **deps**: bump HdrHistogram_C to 0.11.2 (Matteo Collina) [#39462](https://github.com/nodejs/node/pull/39462)
- [[`4c93968a62`](https://github.com/nodejs/node/commit/4c93968a62)] - **deps**: extract gtest source files to deps/googletest (legendecas) [#39386](https://github.com/nodejs/node/pull/39386)
- [[`fcae391fed`](https://github.com/nodejs/node/commit/fcae391fed)] - **deps**: update Acorn to v8.4.1 (Michaël Zasso) [#39166](https://github.com/nodejs/node/pull/39166)
- [[`327838dd96`](https://github.com/nodejs/node/commit/327838dd96)] - **deps**: V8: backport c9224589cf53 (Stephen Belanger) [#39743](https://github.com/nodejs/node/pull/39743)
- [[`89c1bbd7b2`](https://github.com/nodejs/node/commit/89c1bbd7b2)] - **deps**: V8: cherry-pick 81814ed44574 (Stephen Belanger) [#39719](https://github.com/nodejs/node/pull/39719)
- [[`8b9215d07c`](https://github.com/nodejs/node/commit/8b9215d07c)] - **deps**: update to cjs-module-lexer@1.2.2 (Guy Bedford) [#39402](https://github.com/nodejs/node/pull/39402)
- [[`e201293ddb`](https://github.com/nodejs/node/commit/e201293ddb)] - **dgram**: use simplified validator (Voltrex) [#39753](https://github.com/nodejs/node/pull/39753)
- [[`6fdac38f91`](https://github.com/nodejs/node/commit/6fdac38f91)] - **doc,fs**: remove experimental status for WHATWG URL as path (Antoine du Hamel) [#38870](https://github.com/nodejs/node/pull/38870)
- [[`d56e8268f9`](https://github.com/nodejs/node/commit/d56e8268f9)] - **doc,lib**: prepare for stricter multi-line array linting (Rich Trott) [#37088](https://github.com/nodejs/node/pull/37088)
- [[`5500ae9236`](https://github.com/nodejs/node/commit/5500ae9236)] - **domain**: do not add domain to promise from other context (Stephen Belanger) [#39135](https://github.com/nodejs/node/pull/39135)
- [[`dc855af18e`](https://github.com/nodejs/node/commit/dc855af18e)] - **errors**: don't throw TypeError on missing export (Benjamin Coe) [#39017](https://github.com/nodejs/node/pull/39017)
- [[`c13eadc218`](https://github.com/nodejs/node/commit/c13eadc218)] - **errors**: eliminate all overhead for hidden calls (Momtchil Momtchev) [#35644](https://github.com/nodejs/node/pull/35644)
- [[`d42bbe48c5`](https://github.com/nodejs/node/commit/d42bbe48c5)] - **esm**: use correct URL for error decoration (Bradley Farias) [#37854](https://github.com/nodejs/node/pull/37854)
- [[`9db3304368`](https://github.com/nodejs/node/commit/9db3304368)] - **esm**: update to correct deprecation code (Colin Ihrig) [#37147](https://github.com/nodejs/node/pull/37147)
- [[`e73bfed2f4`](https://github.com/nodejs/node/commit/e73bfed2f4)] - **esm**: deprecate legacy main lookup for modules (Guy Bedford) [#36918](https://github.com/nodejs/node/pull/36918)
- [[`c1782ea1f5`](https://github.com/nodejs/node/commit/c1782ea1f5)] - **events**: allow the options argument to be null (Luigi Pinca) [#39486](https://github.com/nodejs/node/pull/39486)
- [[`d2834fb97f`](https://github.com/nodejs/node/commit/d2834fb97f)] - **fs**: improve fsPromises writeFile performance (Nitzan Uziely) [#37610](https://github.com/nodejs/node/pull/37610)
- [[`ee1d13c90d`](https://github.com/nodejs/node/commit/ee1d13c90d)] - **fs**: use byteLength to handle ArrayBuffer views (Michaël Zasso) [#38187](https://github.com/nodejs/node/pull/38187)
- [[`b38d6b475b`](https://github.com/nodejs/node/commit/b38d6b475b)] - **fs**: fixup negative length in fs.truncate (James M Snell) [#37483](https://github.com/nodejs/node/pull/37483)
- [[`fe28128f3c`](https://github.com/nodejs/node/commit/fe28128f3c)] - **fs**: add docs and tests for `AsyncIterable` support in `fh.writeFile` (Antoine du Hamel) [#39836](https://github.com/nodejs/node/pull/39836)
- [[`2b0e2706c0`](https://github.com/nodejs/node/commit/2b0e2706c0)] - **fs**: improve fsPromises readFile performance (Nitzan Uziely) [#37608](https://github.com/nodejs/node/pull/37608)
- [[`a4d6f78619`](https://github.com/nodejs/node/commit/a4d6f78619)] - **fs**: move constants to internal/fs/utils.js (Darshan Sen) [#38061](https://github.com/nodejs/node/pull/38061)
- [[`402f7722ce`](https://github.com/nodejs/node/commit/402f7722ce)] - **fs**: add validatePosition and use in read and readSync (Darshan Sen) [#37051](https://github.com/nodejs/node/pull/37051)
- [[`2bc301dcff`](https://github.com/nodejs/node/commit/2bc301dcff)] - **http**: decodes url.username and url.password for authorization header (Lew Gordon) [#39310](https://github.com/nodejs/node/pull/39310)
- [[`5459f4af33`](https://github.com/nodejs/node/commit/5459f4af33)] - **http**: clean up HttpParser correctly (Tobias Koppers) [#39292](https://github.com/nodejs/node/pull/39292)
- [[`8b3feee148`](https://github.com/nodejs/node/commit/8b3feee148)] - **http,https**: align server option of https with http (Qingyu Deng) [#38992](https://github.com/nodejs/node/pull/38992)
- [[`cf59e87c8b`](https://github.com/nodejs/node/commit/cf59e87c8b)] - **inspector**: update inspector_protocol to 89c4adf (Rich Trott) [#39650](https://github.com/nodejs/node/pull/39650)
- [[`ea5f2047a2`](https://github.com/nodejs/node/commit/ea5f2047a2)] - **inspector**: update inspector_protocol to 8ec18cf (Rich Trott) [#39614](https://github.com/nodejs/node/pull/39614)
- [[`1e5aca550c`](https://github.com/nodejs/node/commit/1e5aca550c)] - **inspector**: mark as stable (Gireesh Punathil) [#37748](https://github.com/nodejs/node/pull/37748)
- [[`8a2ce5dae6`](https://github.com/nodejs/node/commit/8a2ce5dae6)] - **inspector**: move inspector async hooks to environment (Joyee Cheung) [#39112](https://github.com/nodejs/node/pull/39112)
- [[`338189ff6f`](https://github.com/nodejs/node/commit/338189ff6f)] - **lib**: simplify validators (Voltrex) [#39753](https://github.com/nodejs/node/pull/39753)
- [[`e1019351e8`](https://github.com/nodejs/node/commit/e1019351e8)] - **lib**: cleanup validation (Voltrex) [#39652](https://github.com/nodejs/node/pull/39652)
- [[`dbaf4988bc`](https://github.com/nodejs/node/commit/dbaf4988bc)] - **lib**: use validators (Voltrex) [#39663](https://github.com/nodejs/node/pull/39663)
- [[`9c33e4bfb2`](https://github.com/nodejs/node/commit/9c33e4bfb2)] - **lib**: use validator (Voltrex) [#39547](https://github.com/nodejs/node/pull/39547)
- [[`5b1104291d`](https://github.com/nodejs/node/commit/5b1104291d)] - **lib**: use `validateObject` (Voltrex) [#39605](https://github.com/nodejs/node/pull/39605)
- [[`1ce81079df`](https://github.com/nodejs/node/commit/1ce81079df)] - **lib**: remove use of array destructuring (Antoine du Hamel) [#36818](https://github.com/nodejs/node/pull/36818)
- [[`b24b34effd`](https://github.com/nodejs/node/commit/b24b34effd)] - **lib**: add `bound apply` variants of varargs `primordials` (ExE Boss) [#37005](https://github.com/nodejs/node/pull/37005)
- [[`7cdff9a6a8`](https://github.com/nodejs/node/commit/7cdff9a6a8)] - **lib**: refactor `primordials.makeSafe` to use more primordials (ExE Boss) [#36865](https://github.com/nodejs/node/pull/36865)
- [[`1737352580`](https://github.com/nodejs/node/commit/1737352580)] - **lib**: comment explaining special-case handling of promises (Stephen Belanger) [#39135](https://github.com/nodejs/node/pull/39135)
- [[`7f54cccb6c`](https://github.com/nodejs/node/commit/7f54cccb6c)] - **lib**: refactor to use validateString (ZiJian Liu) [#37006](https://github.com/nodejs/node/pull/37006)
- [[`98259dc527`](https://github.com/nodejs/node/commit/98259dc527)] - **module**: improve support of data: URLs (Antoine du Hamel) [#37392](https://github.com/nodejs/node/pull/37392)
- [[`9aba2888a1`](https://github.com/nodejs/node/commit/9aba2888a1)] - **net**: throw ERR_OUT_OF_RANGE if blockList.addSubnet prefix is NaN (ZiJian Liu) [#36732](https://github.com/nodejs/node/pull/36732)
- [[`2ca12c83b4`](https://github.com/nodejs/node/commit/2ca12c83b4)] - **node-api**: handle pending exception in cb wrapper (Michael Dawson) [#39476](https://github.com/nodejs/node/pull/39476)
- [[`9e5edf2158`](https://github.com/nodejs/node/commit/9e5edf2158)] - **node-api**: cctest on v8impl::Reference (legendecas) [#38970](https://github.com/nodejs/node/pull/38970)
- [[`a74032a490`](https://github.com/nodejs/node/commit/a74032a490)] - **node-api**: rtn pending excep on napi_new_instance (legendecas) [#38798](https://github.com/nodejs/node/pull/38798)
- [[`bcb85adee6`](https://github.com/nodejs/node/commit/bcb85adee6)] - **policy**: canonicalize before resolving specifiers (Bradley Farias) [#37863](https://github.com/nodejs/node/pull/37863)
- [[`0ff520cf02`](https://github.com/nodejs/node/commit/0ff520cf02)] - **policy**: fix integrity when DEFAULT_ENCODING is set (Tobias Nießen) [#39750](https://github.com/nodejs/node/pull/39750)
- [[`6c87b591d9`](https://github.com/nodejs/node/commit/6c87b591d9)] - **readline**: allow completer to rewrite existing input (Anna Henningsen) [#39178](https://github.com/nodejs/node/pull/39178)
- [[`37b4708b19`](https://github.com/nodejs/node/commit/37b4708b19)] - **repl**: fix tla function hoisting (Don Jayamanne) [#39745](https://github.com/nodejs/node/pull/39745)
- [[`9264caeafe`](https://github.com/nodejs/node/commit/9264caeafe)] - **repl**: do not include legacy getter/setter methods in completion (Anna Henningsen) [#39576](https://github.com/nodejs/node/pull/39576)
- [[`50c5e71e22`](https://github.com/nodejs/node/commit/50c5e71e22)] - **repl**: correctly hoist top level await declarations (ejose19) [#39265](https://github.com/nodejs/node/pull/39265)
- [[`1e065a0a43`](https://github.com/nodejs/node/commit/1e065a0a43)] - **repl**: processTopLevelAwait fallback error handling (ejose19) [#39290](https://github.com/nodejs/node/pull/39290)
- [[`99664494ff`](https://github.com/nodejs/node/commit/99664494ff)] - **repl**: ensure correct syntax err for await parsing (Guy Bedford) [#39154](https://github.com/nodejs/node/pull/39154)
- [[`761dafafde`](https://github.com/nodejs/node/commit/761dafafde)] - **repl**: fix Ctrl+C on top level await (Antoine du Hamel) [#38656](https://github.com/nodejs/node/pull/38656)
- [[`88b02cbb08`](https://github.com/nodejs/node/commit/88b02cbb08)] - **repl**: add auto‑completion for dynamic import calls (ExE Boss) [#37178](https://github.com/nodejs/node/pull/37178)
- [[`8f3a8830ba`](https://github.com/nodejs/node/commit/8f3a8830ba)] - **repl**: refactor to avoid unsafe array iteration (Antoine du Hamel) [#37188](https://github.com/nodejs/node/pull/37188)
- [[`a48e2d6ec7`](https://github.com/nodejs/node/commit/a48e2d6ec7)] - **repl**: refactor to avoid unsafe array iteration (Darshan Sen) [#36663](https://github.com/nodejs/node/pull/36663)
- [[`20ffadf437`](https://github.com/nodejs/node/commit/20ffadf437)] - **repl**: refactor to use more primordials (Antoine du Hamel) [#36264](https://github.com/nodejs/node/pull/36264)
- [[`f69c934ad4`](https://github.com/nodejs/node/commit/f69c934ad4)] - **report**: generates report on threads with no isolates (legendecas) [#38994](https://github.com/nodejs/node/pull/38994)
- [[`c4686fa5a7`](https://github.com/nodejs/node/commit/c4686fa5a7)] - **src**: fix TextDecoder final flush size calculation (James M Snell) [#39737](https://github.com/nodejs/node/pull/39737)
- [[`495cd02c20`](https://github.com/nodejs/node/commit/495cd02c20)] - **src**: add cosmetic space character to `async_wrap.h` file (Juan José Arboleda) [#39459](https://github.com/nodejs/node/pull/39459)
- [[`985ec48975`](https://github.com/nodejs/node/commit/985ec48975)] - **src**: print native module id on native module not found (legendecas) [#39460](https://github.com/nodejs/node/pull/39460)
- [[`e6ff7e648e`](https://github.com/nodejs/node/commit/e6ff7e648e)] - **src**: close HandleWraps instead of deleting them in OnGCCollect() (Anna Henningsen) [#39441](https://github.com/nodejs/node/pull/39441)
- [[`5c473bdc12`](https://github.com/nodejs/node/commit/5c473bdc12)] - **src**: remove unused guards around node-api reference (legendecas) [#38334](https://github.com/nodejs/node/pull/38334)
- [[`41213bd507`](https://github.com/nodejs/node/commit/41213bd507)] - **src**: add JSDoc typings for v8 (Voltrex) [#38944](https://github.com/nodejs/node/pull/38944)
- [[`02b1df9fac`](https://github.com/nodejs/node/commit/02b1df9fac)] - **src**: fix crash in AfterGetAddrInfo (Anna Henningsen) [#39735](https://github.com/nodejs/node/pull/39735)
- [[`99493b07d4`](https://github.com/nodejs/node/commit/99493b07d4)] - **src**: fix fatal errors when a current isolate not exist (legendecas) [#38624](https://github.com/nodejs/node/pull/38624)
- [[`9433c28c14`](https://github.com/nodejs/node/commit/9433c28c14)] - **src**: remove more extra semis from member fns (Shelley Vohr) [#38744](https://github.com/nodejs/node/pull/38744)
- [[`bad990c934`](https://github.com/nodejs/node/commit/bad990c934)] - **src**: use BaseObject::kInteralFieldCount in Blob (Joyee Cheung) [#36991](https://github.com/nodejs/node/pull/36991)
- [[`0a759dff52`](https://github.com/nodejs/node/commit/0a759dff52)] - **src**: compare IPv4 addresses in host byte order (Colin Ihrig) [#39096](https://github.com/nodejs/node/pull/39096)
- [[`d73181f243`](https://github.com/nodejs/node/commit/d73181f243)] - **src**: reduce duplicated boilerplate with new env utility fn (James M Snell) [#36536](https://github.com/nodejs/node/pull/36536)
- [[`85af15a8b6`](https://github.com/nodejs/node/commit/85af15a8b6)] - **src**: allow instances of net.BlockList to be created internally (James M Snell) [#34741](https://github.com/nodejs/node/pull/34741)
- [[`1008c80176`](https://github.com/nodejs/node/commit/1008c80176)] - **src**: add SocketAddressLRU Utility (James M Snell) [#34618](https://github.com/nodejs/node/pull/34618)
- [[`e404841a9c`](https://github.com/nodejs/node/commit/e404841a9c)] - **src**: set PromiseHooks by Environment (Bryan English) [#38821](https://github.com/nodejs/node/pull/38821)
- [[`c8c290ae8f`](https://github.com/nodejs/node/commit/c8c290ae8f)] - **src,zlib**: tighten up Z\_\*\_WINDOWBITS macros (Khaidi Chu) [#39115](https://github.com/nodejs/node/pull/39115)
- [[`de171177b4`](https://github.com/nodejs/node/commit/de171177b4)] - **stream**: clean `endWritableNT` (Mestery) [#39645](https://github.com/nodejs/node/pull/39645)
- [[`32a5b8f59b`](https://github.com/nodejs/node/commit/32a5b8f59b)] - **stream**: move duplicated code to an internal module (Rich Trott) [#37508](https://github.com/nodejs/node/pull/37508)
- [[`f90b22d351`](https://github.com/nodejs/node/commit/f90b22d351)] - **util**: add internal createDeferredPromise() (Colin Ihrig) [#37095](https://github.com/nodejs/node/pull/37095)
- [[`61b4a98480`](https://github.com/nodejs/node/commit/61b4a98480)] - **zlib**: avoid converting `Uint8Array` instances to `Buffer` (Antoine du Hamel) [#39492](https://github.com/nodejs/node/pull/39492)

#### Documentation commits

- [[`8efd559347`](https://github.com/nodejs/node/commit/8efd559347)] - **doc**: add duplicate CVE check in sec. release doc (Daniel Bevenius) [#39845](https://github.com/nodejs/node/pull/39845)
- [[`7b123ec78d`](https://github.com/nodejs/node/commit/7b123ec78d)] - **doc**: improve description of the triagers team (Michaël Zasso) [#39833](https://github.com/nodejs/node/pull/39833)
- [[`615477f67b`](https://github.com/nodejs/node/commit/615477f67b)] - **doc**: update instructions for cc (Michael Dawson) [#39674](https://github.com/nodejs/node/pull/39674)
- [[`1a8a26d92e`](https://github.com/nodejs/node/commit/1a8a26d92e)] - **doc**: fix malformed changelog entries (Rich Trott) [#39791](https://github.com/nodejs/node/pull/39791)
- [[`9e772ca9a1`](https://github.com/nodejs/node/commit/9e772ca9a1)] - **doc**: fix lint errors in packages.md (Rich Trott) [#39792](https://github.com/nodejs/node/pull/39792)
- [[`2624c98207`](https://github.com/nodejs/node/commit/2624c98207)] - **doc**: add example of self-reference in scoped packages (Jesús Leganés-Combarro 'piranna) [#37630](https://github.com/nodejs/node/pull/37630)
- [[`00f2cee26c`](https://github.com/nodejs/node/commit/00f2cee26c)] - **doc**: add himadriganguly as a triager (Himadri Ganguly) [#39757](https://github.com/nodejs/node/pull/39757)
- [[`95b9cc78d2`](https://github.com/nodejs/node/commit/95b9cc78d2)] - **doc**: fix YAML comment opening tags (Jayden Seric) [#38324](https://github.com/nodejs/node/pull/38324)
- [[`49a7962d58`](https://github.com/nodejs/node/commit/49a7962d58)] - **doc**: fix `fs.rmdir` `recursive` option deprecation history (Antoine du Hamel) [#39728](https://github.com/nodejs/node/pull/39728)
- [[`53300d33c7`](https://github.com/nodejs/node/commit/53300d33c7)] - **doc**: fixed variable names in queueMicrotask example (ashish maurya) [#39634](https://github.com/nodejs/node/pull/39634)
- [[`df1e20aaf1`](https://github.com/nodejs/node/commit/df1e20aaf1)] - **doc**: update debugger.md description and examples (Rich Trott) [#39661](https://github.com/nodejs/node/pull/39661)
- [[`9672bbf01c`](https://github.com/nodejs/node/commit/9672bbf01c)] - **doc**: fix color contrast issue in light mode (Rich Trott) [#39660](https://github.com/nodejs/node/pull/39660)
- [[`48281ecfcd`](https://github.com/nodejs/node/commit/48281ecfcd)] - **doc**: add code examples to `Writable.destroy()` and `Writable.destroyed` (Juan José Arboleda) [#39491](https://github.com/nodejs/node/pull/39491)
- [[`8799a134e4`](https://github.com/nodejs/node/commit/8799a134e4)] - **doc**: move `NODE_MODULE_VERSION` in release guide (Richard Lau) [#39544](https://github.com/nodejs/node/pull/39544)
- [[`89c8afcf48`](https://github.com/nodejs/node/commit/89c8afcf48)] - **doc**: remove outdated ARM information from release guide (Richard Lau) [#39544](https://github.com/nodejs/node/pull/39544)
- [[`a718b26f28`](https://github.com/nodejs/node/commit/a718b26f28)] - **doc**: fence command examples in release guide (Richard Lau) [#39544](https://github.com/nodejs/node/pull/39544)
- [[`42669bb049`](https://github.com/nodejs/node/commit/42669bb049)] - **doc**: update backport labels in release guide (Richard Lau) [#39544](https://github.com/nodejs/node/pull/39544)
- [[`a437de3c5f`](https://github.com/nodejs/node/commit/a437de3c5f)] - **doc**: add code example to `http.createServer` method (Juan José Arboleda) [#39455](https://github.com/nodejs/node/pull/39455)
- [[`695569fc17`](https://github.com/nodejs/node/commit/695569fc17)] - **doc**: move <EMAIL> to emeritus (Lance Ball) [#39501](https://github.com/nodejs/node/pull/39501)
- [[`c7523da86c`](https://github.com/nodejs/node/commit/c7523da86c)] - **doc**: update AUTHORS (Rich Trott) [#39488](https://github.com/nodejs/node/pull/39488)
- [[`e826109d5c`](https://github.com/nodejs/node/commit/e826109d5c)] - **doc**: update strategic initiative champion (Rich Trott) [#39487](https://github.com/nodejs/node/pull/39487)
- [[`39da842051`](https://github.com/nodejs/node/commit/39da842051)] - **doc**: simplify unnecessarily specific .mailmap entries (Rich Trott) [#39430](https://github.com/nodejs/node/pull/39430)
- [[`6a4c6ce4d7`](https://github.com/nodejs/node/commit/6a4c6ce4d7)] - **doc**: update checkbox label in backporting guide (Darshan Sen) [#39420](https://github.com/nodejs/node/pull/39420)
- [[`d17afa08bd`](https://github.com/nodejs/node/commit/d17afa08bd)] - **doc**: remove \_Addenda\_ from headers (Rich Trott) [#39427](https://github.com/nodejs/node/pull/39427)
- [[`ae97a96d9e`](https://github.com/nodejs/node/commit/ae97a96d9e)] - **doc**: simplify .mailmap file (Rich Trott) [#39418](https://github.com/nodejs/node/pull/39418)
- [[`a3dee70f66`](https://github.com/nodejs/node/commit/a3dee70f66)] - **doc**: fix broken internal link in http.md (Rich Trott) [#39425](https://github.com/nodejs/node/pull/39425)
- [[`ca947ac524`](https://github.com/nodejs/node/commit/ca947ac524)] - **doc**: remove outdated step in onboarding exercise (Rich Trott) [#39410](https://github.com/nodejs/node/pull/39410)
- [[`86e12607f0`](https://github.com/nodejs/node/commit/86e12607f0)] - **doc**: revise strategic initiatives text (Rich Trott) [#39417](https://github.com/nodejs/node/pull/39417)
- [[`cd8e773d28`](https://github.com/nodejs/node/commit/cd8e773d28)] - **doc**: update mailmap and AUTHORS (Rich Trott) [#39393](https://github.com/nodejs/node/pull/39393)
- [[`8376b07ae8`](https://github.com/nodejs/node/commit/8376b07ae8)] - **doc**: use a details tag for completed initiatves (Rich Trott) [#39416](https://github.com/nodejs/node/pull/39416)
- [[`43d28f5f00`](https://github.com/nodejs/node/commit/43d28f5f00)] - **doc**: update commit-queue.md to indicate GitHub Actions are checked (Rich Trott) [#39411](https://github.com/nodejs/node/pull/39411)
- [[`63b0603e95`](https://github.com/nodejs/node/commit/63b0603e95)] - **doc**: use \_pull request\_ instead of \_PR\_ in onboarding doc (Rich Trott) [#39409](https://github.com/nodejs/node/pull/39409)
- [[`73f784f764`](https://github.com/nodejs/node/commit/73f784f764)] - **doc**: add strategic initiatives from TSC repo (Rich Trott) [#39394](https://github.com/nodejs/node/pull/39394)
- [[`1a494d51dc`](https://github.com/nodejs/node/commit/1a494d51dc)] - **doc**: standardize on \_pull request\_ (Rich Trott) [#39384](https://github.com/nodejs/node/pull/39384)
- [[`eb12e4ccfb`](https://github.com/nodejs/node/commit/eb12e4ccfb)] - **doc**: make minor edits to pull request text (Rich Trott) [#39383](https://github.com/nodejs/node/pull/39383)
- [[`ab0bf4fa1a`](https://github.com/nodejs/node/commit/ab0bf4fa1a)] - **doc**: add docker-node and build-wg issue contents (Daniel Bevenius) [#39215](https://github.com/nodejs/node/pull/39215)
- [[`8438e8bf33`](https://github.com/nodejs/node/commit/8438e8bf33)] - **doc**: add instructions for core vuln files (Daniel Bevenius) [#39220](https://github.com/nodejs/node/pull/39220)
- [[`c3cfefc2d3`](https://github.com/nodejs/node/commit/c3cfefc2d3)] - **doc**: standardize on not capitalizing \_collaborator\_ (Rich Trott) [#39379](https://github.com/nodejs/node/pull/39379)
- [[`672023f9f2`](https://github.com/nodejs/node/commit/672023f9f2)] - **doc**: update mailmap and deduplicate AUTHORS entry (Rich Trott) [#39391](https://github.com/nodejs/node/pull/39391)
- [[`baaa397e39`](https://github.com/nodejs/node/commit/baaa397e39)] - **doc**: update AUTHORS (Rich Trott) [#39367](https://github.com/nodejs/node/pull/39367)
- [[`f39d93a428`](https://github.com/nodejs/node/commit/f39d93a428)] - **doc**: move jdalton to emeritus (Rich Trott) [#39380](https://github.com/nodejs/node/pull/39380)
- [[`0b1ce72d64`](https://github.com/nodejs/node/commit/0b1ce72d64)] - **doc**: edit guide on pull requests (Rich Trott) [#39359](https://github.com/nodejs/node/pull/39359)
- [[`6f0b3a20d1`](https://github.com/nodejs/node/commit/6f0b3a20d1)] - **doc**: add text about moving long commit lists out of PR description (Danielle Adams) [#39186](https://github.com/nodejs/node/pull/39186)
- [[`9d43ce3b80`](https://github.com/nodejs/node/commit/9d43ce3b80)] - **doc**: do not use & for "and" in text (Rich Trott) [#39345](https://github.com/nodejs/node/pull/39345)
- [[`25c104f21f`](https://github.com/nodejs/node/commit/25c104f21f)] - **doc**: update AUTHORS (Rich Trott) [#39277](https://github.com/nodejs/node/pull/39277)
- [[`b47b47930c`](https://github.com/nodejs/node/commit/b47b47930c)] - **doc**: put information about the past in details tags (Rich Trott) [#39321](https://github.com/nodejs/node/pull/39321)
- [[`5eafc3afa8`](https://github.com/nodejs/node/commit/5eafc3afa8)] - **doc**: move AndreasMadsen to emeritus (Rich Trott) [#39315](https://github.com/nodejs/node/pull/39315)
- [[`fbf658f1d5`](https://github.com/nodejs/node/commit/fbf658f1d5)] - **doc**: move ofrobots to collaborator emeritus (Rich Trott) [#39307](https://github.com/nodejs/node/pull/39307)
- [[`fc7d714149`](https://github.com/nodejs/node/commit/fc7d714149)] - **doc**: simplify CRAN mirror text in benchmark guide (Rich Trott) [#39287](https://github.com/nodejs/node/pull/39287)
- [[`22f0b7e0d0`](https://github.com/nodejs/node/commit/22f0b7e0d0)] - **doc**: use "repository" instead of "repo" in onboarding.md (Rich Trott) [#39286](https://github.com/nodejs/node/pull/39286)
- [[`f46ae3ffb6`](https://github.com/nodejs/node/commit/f46ae3ffb6)] - **doc**: update collaborator email address (Rich Trott) [#39263](https://github.com/nodejs/node/pull/39263)
- [[`8c569cef88`](https://github.com/nodejs/node/commit/8c569cef88)] - **doc**: remove GitHub mark (Rich Trott) [#39251](https://github.com/nodejs/node/pull/39251)
- [[`b4a0c5a384`](https://github.com/nodejs/node/commit/b4a0c5a384)] - **doc**: remove emailing the TSC from offboarding doc (Rich Trott) [#39280](https://github.com/nodejs/node/pull/39280)
- [[`a4d70ff0cc`](https://github.com/nodejs/node/commit/a4d70ff0cc)] - **doc**: use "repository" in guides versus repo (Michael Dawson) [#39198](https://github.com/nodejs/node/pull/39198)
- [[`31163ed9ee`](https://github.com/nodejs/node/commit/31163ed9ee)] - **doc**: update Node-api version matrix (Michael Dawson) [#39197](https://github.com/nodejs/node/pull/39197)
- [[`9357547519`](https://github.com/nodejs/node/commit/9357547519)] - **doc**: update node-api support matrix (Michael Dawson) [#38424](https://github.com/nodejs/node/pull/38424)
- [[`f08e9d5230`](https://github.com/nodejs/node/commit/f08e9d5230)] - **doc**: remove onboarding-extras (Rich Trott) [#39252](https://github.com/nodejs/node/pull/39252)
- [[`6466faf26d`](https://github.com/nodejs/node/commit/6466faf26d)] - **doc**: move Sam Ruby to emeritus (Rich Trott) [#39264](https://github.com/nodejs/node/pull/39264)
- [[`06acbf6453`](https://github.com/nodejs/node/commit/06acbf6453)] - **doc**: update AUTHORS file (Rich Trott) [#39250](https://github.com/nodejs/node/pull/39250)
- [[`9178805653`](https://github.com/nodejs/node/commit/9178805653)] - **doc**: fix color contrast for anchor marks in dark mode (Rich Trott) [#39168](https://github.com/nodejs/node/pull/39168)
- [[`c6118b23f7`](https://github.com/nodejs/node/commit/c6118b23f7)] - **doc**: rename datatypes to data types (FrankEntriken) [#39209](https://github.com/nodejs/node/pull/39209)
- [[`fdd315918f`](https://github.com/nodejs/node/commit/fdd315918f)] - **doc**: normalize CSS variable names and indentation (Rich Trott) [#39199](https://github.com/nodejs/node/pull/39199)
- [[`9c7c44781c`](https://github.com/nodejs/node/commit/9c7c44781c)] - **doc**: use more consistent formatting for deprecations (Rich Trott) [#39218](https://github.com/nodejs/node/pull/39218)
- [[`c97ebd7905`](https://github.com/nodejs/node/commit/c97ebd7905)] - **doc**: update AUTHORS (Rich Trott) [#39217](https://github.com/nodejs/node/pull/39217)
- [[`c4a3a24848`](https://github.com/nodejs/node/commit/c4a3a24848)] - **doc**: use "pull request" instead of "PR" in packages.md (Rich Trott) [#39213](https://github.com/nodejs/node/pull/39213)
- [[`0d098bfaf0`](https://github.com/nodejs/node/commit/0d098bfaf0)] - **doc**: move v8.stopCoverage() to expected location in doc (Rich Trott) [#39212](https://github.com/nodejs/node/pull/39212)
- [[`bd6af78749`](https://github.com/nodejs/node/commit/bd6af78749)] - **doc**: move vm.measureMemory() to expected location in doc (Rich Trott) [#39211](https://github.com/nodejs/node/pull/39211)
- [[`7378b84bb8`](https://github.com/nodejs/node/commit/7378b84bb8)] - **doc**: add missing deprecation code (Colin Ihrig) [#37147](https://github.com/nodejs/node/pull/37147)
- [[`2f6861ca51`](https://github.com/nodejs/node/commit/2f6861ca51)] - **doc**: use ASCII order for md refs (Antoine du Hamel) [#39170](https://github.com/nodejs/node/pull/39170)
- [[`fa3909504f`](https://github.com/nodejs/node/commit/fa3909504f)] - **doc**: <NAME_EMAIL> (Daniel Bevenius) [#39191](https://github.com/nodejs/node/pull/39191)
- [[`52105acd5f`](https://github.com/nodejs/node/commit/52105acd5f)] - **doc**: remove instructions for unsupported Node.js versions (Rich Trott) [#39185](https://github.com/nodejs/node/pull/39185)
- [[`eb2d75da16`](https://github.com/nodejs/node/commit/eb2d75da16)] - **doc**: remove obsolete cc recommendations (Rich Trott) [#39181](https://github.com/nodejs/node/pull/39181)
- [[`4cf17edd03`](https://github.com/nodejs/node/commit/4cf17edd03)] - **doc**: use "repository" in maintaining-V8 doc (Rich Trott) [#39179](https://github.com/nodejs/node/pull/39179)
- [[`d6a4f8aac9`](https://github.com/nodejs/node/commit/d6a4f8aac9)] - **doc**: fix broken link in errors.md (Rich Trott) [#39200](https://github.com/nodejs/node/pull/39200)
- [[`82458b30fe`](https://github.com/nodejs/node/commit/82458b30fe)] - **doc**: correct JavaScript primitive value names in n-api.md (legendecas) [#39129](https://github.com/nodejs/node/pull/39129)
- [[`2629979fd0`](https://github.com/nodejs/node/commit/2629979fd0)] - **doc**: apply logical ordering to CSS variables (Rich Trott) [#39169](https://github.com/nodejs/node/pull/39169)
- [[`1996580b06`](https://github.com/nodejs/node/commit/1996580b06)] - **doc**: use repository instead of repo (Rich Trott) [#39157](https://github.com/nodejs/node/pull/39157)
- [[`74ba115ab6`](https://github.com/nodejs/node/commit/74ba115ab6)] - **doc**: fix `EventTarget.dispatchEvent` docs (Rohan Sharma) [#39127](https://github.com/nodejs/node/pull/39127)
- [[`2884d9094d`](https://github.com/nodejs/node/commit/2884d9094d)] - **doc**: update AUTHORS file (Rich Trott) [#39082](https://github.com/nodejs/node/pull/39082)
- [[`d069c725b1`](https://github.com/nodejs/node/commit/d069c725b1)] - **doc**: fix napi_default_property name (Davidson Francis) [#39104](https://github.com/nodejs/node/pull/39104)
- [[`1b74d3f775`](https://github.com/nodejs/node/commit/1b74d3f775)] - **doc**: fix dead links in packages.md (Michaël Zasso) [#39113](https://github.com/nodejs/node/pull/39113)
- [[`0c2b5a048d`](https://github.com/nodejs/node/commit/0c2b5a048d)] - **doc**: clearify that http does chunked encoding itself (Mao Wtm) [#28379](https://github.com/nodejs/node/pull/28379)
- [[`d0d731e271`](https://github.com/nodejs/node/commit/d0d731e271)] - **doc**: add descriptions about when `options.mode` is ignored (Ray) [#39881](https://github.com/nodejs/node/pull/39881)
- [[`898db5a570`](https://github.com/nodejs/node/commit/898db5a570)] - **doc**: add code example to `fs.truncate` method (Juan José Arboleda) [#39454](https://github.com/nodejs/node/pull/39454)
- [[`05d7460747`](https://github.com/nodejs/node/commit/05d7460747)] - **doc**: add annotation to writeFile `data` as `Object` (Jacob) [#39167](https://github.com/nodejs/node/pull/39167)
- [[`2ef61b987d`](https://github.com/nodejs/node/commit/2ef61b987d)] - **doc**: fix constants usage in fs.access example (Cyrille Bourgois) [#39289](https://github.com/nodejs/node/pull/39289)
- [[`b2c533ea1d`](https://github.com/nodejs/node/commit/b2c533ea1d)] - **doc**: remove unnecessary module format comments (Rich Trott) [#39219](https://github.com/nodejs/node/pull/39219)
- [[`e8355c47d2`](https://github.com/nodejs/node/commit/e8355c47d2)] - **doc**: remove file name from self-reference links (Antoine du Hamel) [#39165](https://github.com/nodejs/node/pull/39165)
- [[`f799c4617e`](https://github.com/nodejs/node/commit/f799c4617e)] - **doc**: use `await` in filehandle.truncate() snippet (RA80533) [#38939](https://github.com/nodejs/node/pull/38939)
- [[`e7f3f0d778`](https://github.com/nodejs/node/commit/e7f3f0d778)] - **doc**: update abort signal in fs promise api example (Moritz Kneilmann) [#38669](https://github.com/nodejs/node/pull/38669)
- [[`a44219d979`](https://github.com/nodejs/node/commit/a44219d979)] - **doc**: add documentation for fs.WriteStream.close() (Hitesh Sharma) [#38610](https://github.com/nodejs/node/pull/38610)
- [[`c3ae1cfbab`](https://github.com/nodejs/node/commit/c3ae1cfbab)] - **doc**: fix fs.openSync() signature (Luigi Pinca) [#38591](https://github.com/nodejs/node/pull/38591)
- [[`23a8aed3f9`](https://github.com/nodejs/node/commit/23a8aed3f9)] - **doc**: typo stats() should be stat(); clarity (Bryan Field) [#38541](https://github.com/nodejs/node/pull/38541)
- [[`9fe46ea0fd`](https://github.com/nodejs/node/commit/9fe46ea0fd)] - **doc**: fix broken AHAFS link in fs doc (Rich Trott) [#38534](https://github.com/nodejs/node/pull/38534)
- [[`7a92c3cfd4`](https://github.com/nodejs/node/commit/7a92c3cfd4)] - **doc**: clarify that fs.Dir async iterator closes automatically (James M Snell) [#38438](https://github.com/nodejs/node/pull/38438)
- [[`b819848487`](https://github.com/nodejs/node/commit/b819848487)] - **doc**: remove superfluous await from fsPromises.readdir example (Michael Rommel) [#38293](https://github.com/nodejs/node/pull/38293)
- [[`9fa7dcf9df`](https://github.com/nodejs/node/commit/9fa7dcf9df)] - **doc**: fix missing backtick in fs.md (Siddharth) [#38260](https://github.com/nodejs/node/pull/38260)
- [[`4cf4ee99dc`](https://github.com/nodejs/node/commit/4cf4ee99dc)] - **doc**: fix typo in fs.md (Antoine du Hamel) [#38100](https://github.com/nodejs/node/pull/38100)
- [[`f9d36cbf42`](https://github.com/nodejs/node/commit/f9d36cbf42)] - **doc**: fix typos in /doc/api/fs.md (Merlin Luntke) [#37557](https://github.com/nodejs/node/pull/37557)
- [[`bbcc2171c5`](https://github.com/nodejs/node/commit/bbcc2171c5)] - **doc**: fix typo "director" instead of "directory" (humanwebpl) [#37523](https://github.com/nodejs/node/pull/37523)
- [[`67ac6b3b66`](https://github.com/nodejs/node/commit/67ac6b3b66)] - **doc**: fix "referred to" in fs docs (Tobias Nießen) [#37388](https://github.com/nodejs/node/pull/37388)
- [[`3b9fa2412f`](https://github.com/nodejs/node/commit/3b9fa2412f)] - **doc**: change "Version 4 UUID" to "version 4 UUID" (Tobias Nießen) [#39682](https://github.com/nodejs/node/pull/39682)
- [[`3d26572773`](https://github.com/nodejs/node/commit/3d26572773)] - **doc**: add point to ask H1 reporter about credit (Daniel Bevenius) [#39585](https://github.com/nodejs/node/pull/39585)
- [[`469190d13c`](https://github.com/nodejs/node/commit/469190d13c)] - **doc**: move util.toUSVString() outside of deprecated group (Luigi Pinca) [#39840](https://github.com/nodejs/node/pull/39840)
- [[`f509788850`](https://github.com/nodejs/node/commit/f509788850)] - **doc**: fix lint error in modules.md (Rich Trott) [#37811](https://github.com/nodejs/node/pull/37811)
- [[`a7833c7ce6`](https://github.com/nodejs/node/commit/a7833c7ce6)] - **doc**: refactor signal info in child_process.md (Darshan Sen) [#37528](https://github.com/nodejs/node/pull/37528)
- [[`f5b2fe1204`](https://github.com/nodejs/node/commit/f5b2fe1204)] - **doc**: change lang info string in fs JS snippets (Antoine du Hamel) [#37605](https://github.com/nodejs/node/pull/37605)
- [[`307c1d817f`](https://github.com/nodejs/node/commit/307c1d817f)] - **doc**: refactor fs docs structure (Michaël Zasso) [#37170](https://github.com/nodejs/node/pull/37170)
- [[`298a16a2e7`](https://github.com/nodejs/node/commit/298a16a2e7)] - **doc**: update emitClose default for fs streams (Kevin Locke) [#36653](https://github.com/nodejs/node/pull/36653)
- [[`0c469b3f77`](https://github.com/nodejs/node/commit/0c469b3f77)] - **doc**: revise process.memoryUsage() text (Rich Trott) [#36757](https://github.com/nodejs/node/pull/36757)
- [[`1ebe7d70ea`](https://github.com/nodejs/node/commit/1ebe7d70ea)] - **doc**: fix punctuation in v8.md (Rich Trott) [#36192](https://github.com/nodejs/node/pull/36192)
- [[`591a05b637`](https://github.com/nodejs/node/commit/591a05b637)] - **doc**: add link for v8.takeCoverage() (Rich Trott) [#36135](https://github.com/nodejs/node/pull/36135)
- [[`e5fe3164f3`](https://github.com/nodejs/node/commit/e5fe3164f3)] - **doc**: add YAML metadata for process.memoryUsage.rss (Gerhard Stoebich) [#36781](https://github.com/nodejs/node/pull/36781)

#### Other commits

- [[`ab66dabbf2`](https://github.com/nodejs/node/commit/ab66dabbf2)] - **doc,meta**: update email addresses for misterdjules (Rich Trott) [#39433](https://github.com/nodejs/node/pull/39433)
- [[`c6ccd97fe2`](https://github.com/nodejs/node/commit/c6ccd97fe2)] - **doc,tools**: remove `checkLinks.mjs` (Antoine du Hamel) [#39206](https://github.com/nodejs/node/pull/39206)
- [[`8f8f528f08`](https://github.com/nodejs/node/commit/8f8f528f08)] - **meta**: add gyp as owner of gyp files and tools/gyp (Mary Marchini) [#34847](https://github.com/nodejs/node/pull/34847)
- [[`4b2eee5232`](https://github.com/nodejs/node/commit/4b2eee5232)] - **meta**: consolidate AUTHORS entries for ooHmartY (Rich Trott) [#39705](https://github.com/nodejs/node/pull/39705)
- [[`6916a6c2b0`](https://github.com/nodejs/node/commit/6916a6c2b0)] - **meta**: consolidate AUTHORS entries for homosaur (Rich Trott) [#39705](https://github.com/nodejs/node/pull/39705)
- [[`b65a635c8a`](https://github.com/nodejs/node/commit/b65a635c8a)] - **meta**: consolidate AUTHORS entries for Ayase-252 (Rich Trott) [#39705](https://github.com/nodejs/node/pull/39705)
- [[`e86b59cf4c`](https://github.com/nodejs/node/commit/e86b59cf4c)] - **meta**: consolidate AUTHORS entries for robin-drexler (Rich Trott) [#39705](https://github.com/nodejs/node/pull/39705)
- [[`1eda8442bd`](https://github.com/nodejs/node/commit/1eda8442bd)] - **meta**: consolidate AUTHORS entries for samshull (Rich Trott) [#39705](https://github.com/nodejs/node/pull/39705)
- [[`cd67d86572`](https://github.com/nodejs/node/commit/cd67d86572)] - **meta**: update AUTHORS (Rich Trott) [#39705](https://github.com/nodejs/node/pull/39705)
- [[`bb06282a9e`](https://github.com/nodejs/node/commit/bb06282a9e)] - **meta**: consolidate email addresses for MarshallOfSound (Rich Trott) [#39651](https://github.com/nodejs/node/pull/39651)
- [[`12fe34eae4`](https://github.com/nodejs/node/commit/12fe34eae4)] - **meta**: consolidate email addresses for tadjik1 (Rich Trott) [#39651](https://github.com/nodejs/node/pull/39651)
- [[`4301e252b4`](https://github.com/nodejs/node/commit/4301e252b4)] - **meta**: consolidate email addresses for szmarczak (Rich Trott) [#39651](https://github.com/nodejs/node/pull/39651)
- [[`3e8fc49730`](https://github.com/nodejs/node/commit/3e8fc49730)] - **meta**: update AUTHORS (Rich Trott) [#39636](https://github.com/nodejs/node/pull/39636)
- [[`60f41c34dd`](https://github.com/nodejs/node/commit/60f41c34dd)] - **meta**: simplify mailmap (Rich Trott) [#39612](https://github.com/nodejs/node/pull/39612)
- [[`fc9c680260`](https://github.com/nodejs/node/commit/fc9c680260)] - **meta**: consolidate emails for tadhgcreedon (Rich Trott) [#39611](https://github.com/nodejs/node/pull/39611)
- [[`d87fcf9959`](https://github.com/nodejs/node/commit/d87fcf9959)] - **meta**: consolidate emails for timcosta (Rich Trott) [#39611](https://github.com/nodejs/node/pull/39611)
- [[`fdbe97849b`](https://github.com/nodejs/node/commit/fdbe97849b)] - **meta**: consolidate emails for timruffles (Rich Trott) [#39611](https://github.com/nodejs/node/pull/39611)
- [[`b9f2ea92e9`](https://github.com/nodejs/node/commit/b9f2ea92e9)] - **meta**: update AUTHORS (Rich Trott) [#39629](https://github.com/nodejs/node/pull/39629)
- [[`472cf1520e`](https://github.com/nodejs/node/commit/472cf1520e)] - **meta**: add mailmap entry for ryzokuken (Rich Trott) [#39596](https://github.com/nodejs/node/pull/39596)
- [[`ae3f8b1eda`](https://github.com/nodejs/node/commit/ae3f8b1eda)] - **meta**: add mailmap entry for uttampawar (Rich Trott) [#39596](https://github.com/nodejs/node/pull/39596)
- [[`2a2d8ebd90`](https://github.com/nodejs/node/commit/2a2d8ebd90)] - **meta**: add mailmap entry for dmabupt (Rich Trott) [#39596](https://github.com/nodejs/node/pull/39596)
- [[`030036ec92`](https://github.com/nodejs/node/commit/030036ec92)] - **meta**: align README/.mailmap/AUTHORS email entries (Rich Trott) [#39505](https://github.com/nodejs/node/pull/39505)
- [[`fd2146be91`](https://github.com/nodejs/node/commit/fd2146be91)] - **meta**: add mailmap entry for garygsc (Rich Trott) [#39588](https://github.com/nodejs/node/pull/39588)
- [[`0833e2d9cb`](https://github.com/nodejs/node/commit/0833e2d9cb)] - **meta**: add mailmap entry for ttzztztz (Rich Trott) [#39588](https://github.com/nodejs/node/pull/39588)
- [[`1fbc19ee32`](https://github.com/nodejs/node/commit/1fbc19ee32)] - **meta**: update AUTHORS (Rich Trott) [#39587](https://github.com/nodejs/node/pull/39587)
- [[`2d6428665d`](https://github.com/nodejs/node/commit/2d6428665d)] - **meta**: update .mailmap to remove duplication in AUTHORS (Rich Trott) [#39561](https://github.com/nodejs/node/pull/39561)
- [[`6c4febd701`](https://github.com/nodejs/node/commit/6c4febd701)] - **meta**: add .mailmap entries to remove AUTHORS duplicates (Rich Trott) [#39560](https://github.com/nodejs/node/pull/39560)
- [[`1755f49a20`](https://github.com/nodejs/node/commit/1755f49a20)] - **meta**: add .mailmap entry to remove duplication in AUTHORS (Rich Trott) [#39559](https://github.com/nodejs/node/pull/39559)
- [[`fdcc5729d9`](https://github.com/nodejs/node/commit/fdcc5729d9)] - **meta**: update collaborator email in AUTHORS/.mailmap (Rich Trott) [#39521](https://github.com/nodejs/node/pull/39521)
- [[`27e9a44852`](https://github.com/nodejs/node/commit/27e9a44852)] - **meta**: update collaborator email in README (Rich Trott) [#39521](https://github.com/nodejs/node/pull/39521)
- [[`5e1c49ff0f`](https://github.com/nodejs/node/commit/5e1c49ff0f)] - **meta**: update collaborator email in AUTHORS/.mailmap (Rich Trott) [#39521](https://github.com/nodejs/node/pull/39521)
- [[`fbecae169e`](https://github.com/nodejs/node/commit/fbecae169e)] - **meta**: move gdams to emeritus (Rich Trott) [#39539](https://github.com/nodejs/node/pull/39539)
- [[`48ec33f1b8`](https://github.com/nodejs/node/commit/48ec33f1b8)] - **meta**: update collaborator email in README (Rich Trott) [#39510](https://github.com/nodejs/node/pull/39510)
- [[`f269df31ea`](https://github.com/nodejs/node/commit/f269df31ea)] - **meta**: remove unneeded .mailmap entry (Rich Trott) [#39512](https://github.com/nodejs/node/pull/39512)
- [[`b0c1aab28d`](https://github.com/nodejs/node/commit/b0c1aab28d)] - **meta**: update email address for collaborator (Rich Trott) [#39511](https://github.com/nodejs/node/pull/39511)
- [[`5f4935292a`](https://github.com/nodejs/node/commit/5f4935292a)] - **meta**: align collaborator name in .mailmap/AUTHORS with README (Rich Trott) [#39489](https://github.com/nodejs/node/pull/39489)
- [[`1b2078c912`](https://github.com/nodejs/node/commit/1b2078c912)] - **meta**: align email address in README/.mailmap/AUTHORS (Rich Trott) [#39503](https://github.com/nodejs/node/pull/39503)
- [[`2f816bf24b`](https://github.com/nodejs/node/commit/2f816bf24b)] - **meta**: revise .mailmap for README consistency (Rich Trott) [#39457](https://github.com/nodejs/node/pull/39457)
- [[`1302a911f5`](https://github.com/nodejs/node/commit/1302a911f5)] - **meta**: alphabetize .mailmap file (Rich Trott) [#39434](https://github.com/nodejs/node/pull/39434)
- [[`55322c0260`](https://github.com/nodejs/node/commit/55322c0260)] - **meta**: align collaborator email in .mailmap/AUTHORS with README (Rich Trott) [#39478](https://github.com/nodejs/node/pull/39478)
- [[`83f5cc0bd4`](https://github.com/nodejs/node/commit/83f5cc0bd4)] - **meta**: update AUTHORS (Rich Trott) [#39461](https://github.com/nodejs/node/pull/39461)
- [[`69b56a3fe9`](https://github.com/nodejs/node/commit/69b56a3fe9)] - **meta**: add .mailmap entry for new email for existing contributor (Rich Trott) [#39431](https://github.com/nodejs/node/pull/39431)
- [[`2f325c946f`](https://github.com/nodejs/node/commit/2f325c946f)] - **meta**: use form schema for bug report template (Michaël Zasso) [#39194](https://github.com/nodejs/node/pull/39194)
- [[`9766a99dd2`](https://github.com/nodejs/node/commit/9766a99dd2)] - **meta**: add @nodejs/actions as CODEOWNERS (Mary Marchini) [#39119](https://github.com/nodejs/node/pull/39119)
- [[`007f9a0e36`](https://github.com/nodejs/node/commit/007f9a0e36)] - **test**: fix test-vm-memleak for high baseline platforms (Rich Trott) [#38062](https://github.com/nodejs/node/pull/38062)
- [[`0fabd8e755`](https://github.com/nodejs/node/commit/0fabd8e755)] - **test**: fix flaky test-vm-memleak (Rich Trott) [#38054](https://github.com/nodejs/node/pull/38054)
- [[`64fb928ec7`](https://github.com/nodejs/node/commit/64fb928ec7)] - **test**: fix flaky test-child-process-exec-abortcontroller-promisified (Antoine du Hamel) [#37572](https://github.com/nodejs/node/pull/37572)
- [[`e660892f1a`](https://github.com/nodejs/node/commit/e660892f1a)] - **test**: use simplfied validator (voltrexmaster) [#39753](https://github.com/nodejs/node/pull/39753)
- [[`779417f97e`](https://github.com/nodejs/node/commit/779417f97e)] - **test**: use template to concatenate string (Himadri Ganguly) [#39621](https://github.com/nodejs/node/pull/39621)
- [[`a61076042d`](https://github.com/nodejs/node/commit/a61076042d)] - **test**: deflake test-http2-buffersize (Luigi Pinca) [#39591](https://github.com/nodejs/node/pull/39591)
- [[`68ef265c39`](https://github.com/nodejs/node/commit/68ef265c39)] - **test**: convert anonymous function to arrow function (Himadri Ganguly) [#39604](https://github.com/nodejs/node/pull/39604)
- [[`78db43c9e7`](https://github.com/nodejs/node/commit/78db43c9e7)] - **test**: add test-debugger-breakpoint-exists (Rich Trott) [#39570](https://github.com/nodejs/node/pull/39570)
- [[`5696bcf715`](https://github.com/nodejs/node/commit/5696bcf715)] - **test**: fix WASI link test (Richard Lau) [#39485](https://github.com/nodejs/node/pull/39485)
- [[`0b564a6d40`](https://github.com/nodejs/node/commit/0b564a6d40)] - **test**: add test for WebSocket secret verification in debugger (Rich Trott) [#39357](https://github.com/nodejs/node/pull/39357)
- [[`831f266d6f`](https://github.com/nodejs/node/commit/831f266d6f)] - **test**: put common lint exceptions into config file (Rich Trott) [#39358](https://github.com/nodejs/node/pull/39358)
- [[`d8066f5325`](https://github.com/nodejs/node/commit/d8066f5325)] - **test**: mark test-domain-error-types flaky (James M Snell) [#39369](https://github.com/nodejs/node/pull/39369)
- [[`c915a1bd04`](https://github.com/nodejs/node/commit/c915a1bd04)] - **test**: remove eslint-disable comment from fixture file (Rich Trott) [#39320](https://github.com/nodejs/node/pull/39320)
- [[`1eb8307cc5`](https://github.com/nodejs/node/commit/1eb8307cc5)] - **test**: move debugger test case to parallel (Rich Trott) [#39300](https://github.com/nodejs/node/pull/39300)
- [[`546202364c`](https://github.com/nodejs/node/commit/546202364c)] - **test**: remove debugger workaround for AIX (Rich Trott) [#39296](https://github.com/nodejs/node/pull/39296)
- [[`e12164e88d`](https://github.com/nodejs/node/commit/e12164e88d)] - **test**: fix test-debugger-heap-profiler for workers (Richard Lau) [#39687](https://github.com/nodejs/node/pull/39687)
- [[`a45bf2f1a0`](https://github.com/nodejs/node/commit/a45bf2f1a0)] - **test**: use common.PORT instead of hardcoded port number (Rich Trott) [#39298](https://github.com/nodejs/node/pull/39298)
- [[`9b737ebd4b`](https://github.com/nodejs/node/commit/9b737ebd4b)] - **test**: add test for debugger restart message issue (Rich Trott) [#39273](https://github.com/nodejs/node/pull/39273)
- [[`68523894ab`](https://github.com/nodejs/node/commit/68523894ab)] - **test**: remove workaround code in debugger test (Rich Trott) [#39238](https://github.com/nodejs/node/pull/39238)
- [[`2cd414147b`](https://github.com/nodejs/node/commit/2cd414147b)] - **test**: move test-debugger-address to parallel (Rich Trott) [#39236](https://github.com/nodejs/node/pull/39236)
- [[`a2e4020e4b`](https://github.com/nodejs/node/commit/a2e4020e4b)] - **test**: prepare for consistent comma-dangle lint rule (Rich Trott) [#37930](https://github.com/nodejs/node/pull/37930)
- [[`62b439e04d`](https://github.com/nodejs/node/commit/62b439e04d)] - **test**: replace "inspector-cli" with "debugger" (Rich Trott) [#39156](https://github.com/nodejs/node/pull/39156)
- [[`f13a302d23`](https://github.com/nodejs/node/commit/f13a302d23)] - **test**: improve coverage of stream.Readable (Rongjian Zhang) [#38702](https://github.com/nodejs/node/pull/38702)
- [[`f3d2e6ac29`](https://github.com/nodejs/node/commit/f3d2e6ac29)] - **test**: add tests for `bound apply` variants of varargs `primordials` (ExE Boss) [#37005](https://github.com/nodejs/node/pull/37005)
- [[`f70fd00fb3`](https://github.com/nodejs/node/commit/f70fd00fb3)] - **test**: use localhost test instead of connecting to remote (Adam Majer) [#39011](https://github.com/nodejs/node/pull/39011)
- [[`c4ff5e4a7e`](https://github.com/nodejs/node/commit/c4ff5e4a7e)] - **test**: update error message keywords (leeight) [#39826](https://github.com/nodejs/node/pull/39826)
- [[`922dacebfb`](https://github.com/nodejs/node/commit/922dacebfb)] - **test**: increase coverage for Blob (ZiJian Liu) [#38515](https://github.com/nodejs/node/pull/38515)
- [[`c6ab19895d`](https://github.com/nodejs/node/commit/c6ab19895d)] - **test**: account for OOM risks in heapsnapshot-near-heap-limit tests (Joyee Cheung) [#37761](https://github.com/nodejs/node/pull/37761)
- [[`971d5be57c`](https://github.com/nodejs/node/commit/971d5be57c)] - **test**: split heap snapshot limit tests (Rich Trott) [#37189](https://github.com/nodejs/node/pull/37189)
- [[`815d59a7b3`](https://github.com/nodejs/node/commit/815d59a7b3)] - **test**: fix test-memory-usage.js for IBMi (Rich Trott) [#36758](https://github.com/nodejs/node/pull/36758)
- [[`aa5309c33f`](https://github.com/nodejs/node/commit/aa5309c33f)] - **test**: increase coverage for net/blocklist (Zijian Liu) [#36405](https://github.com/nodejs/node/pull/36405)
- [[`f3be3ec417`](https://github.com/nodejs/node/commit/f3be3ec417)] - **test**: check mustCall errors in test-fs-read-type (Tobias Nießen) [#36914](https://github.com/nodejs/node/pull/36914)
- [[`b643fe7edf`](https://github.com/nodejs/node/commit/b643fe7edf)] - **test**: use faster variant for rss (Pooja D P) [#36839](https://github.com/nodejs/node/pull/36839)
- [[`d4362db111`](https://github.com/nodejs/node/commit/d4362db111)] - **test**: use faster variant for rss in test-crypto-dh-leak (Pooja D P) [#36766](https://github.com/nodejs/node/pull/36766)
- [[`3094ef967a`](https://github.com/nodejs/node/commit/3094ef967a)] - **test**: use faster variant for rss in test-vm-memleak.js (Pooja D P) [#36769](https://github.com/nodejs/node/pull/36769)
- [[`ff7879b41e`](https://github.com/nodejs/node/commit/ff7879b41e)] - **test**: use faster variant for rss test-memoryusage-emfile (Pooja D P) [#36768](https://github.com/nodejs/node/pull/36768)
- [[`d39200c7f4`](https://github.com/nodejs/node/commit/d39200c7f4)] - **tools**: make utils.SearchFiles Python2-compatible (Michaël Zasso) [#40020](https://github.com/nodejs/node/pull/40020)
- [[`55493f2011`](https://github.com/nodejs/node/commit/55493f2011)] - **tools**: update workflow to open a pull request (Rich Trott) [#39825](https://github.com/nodejs/node/pull/39825)
- [[`417a3ac474`](https://github.com/nodejs/node/commit/417a3ac474)] - **tools**: use find-inactive-collaborators to modify README.md (Rich Trott) [#39825](https://github.com/nodejs/node/pull/39825)
- [[`e9b1a006a1`](https://github.com/nodejs/node/commit/e9b1a006a1)] - **tools**: fix markdown linting (Rich Trott) [#39832](https://github.com/nodejs/node/pull/39832)
- [[`67f1bff657`](https://github.com/nodejs/node/commit/67f1bff657)] - **tools**: update markdown linter dependencies and move to ESM (Antoine du Hamel) [#39801](https://github.com/nodejs/node/pull/39801)
- [[`67c5921e8a`](https://github.com/nodejs/node/commit/67c5921e8a)] - **tools**: update rollup to latest version in markdown linter (Rich Trott) [#39797](https://github.com/nodejs/node/pull/39797)
- [[`64714b429a`](https://github.com/nodejs/node/commit/64714b429a)] - **tools**: update markdown lint dependencies (Rich Trott) [#39770](https://github.com/nodejs/node/pull/39770)
- [[`de9461168a`](https://github.com/nodejs/node/commit/de9461168a)] - **tools**: bump remark-preset-lint-node to 3.0.0 (Rich Trott) [#39755](https://github.com/nodejs/node/pull/39755)
- [[`dfdf6c7317`](https://github.com/nodejs/node/commit/dfdf6c7317)] - **tools**: update markdown linter rules (Rich Trott) [#38384](https://github.com/nodejs/node/pull/38384)
- [[`f8fee449f7`](https://github.com/nodejs/node/commit/f8fee449f7)] - **tools**: update path-parse in markdown linter package-lock file (Rich Trott) [#39729](https://github.com/nodejs/node/pull/39729)
- [[`a338c0e07b`](https://github.com/nodejs/node/commit/a338c0e07b)] - **tools**: fix more build warnings in inspector_protocol (Richard Lau) [#39725](https://github.com/nodejs/node/pull/39725)
- [[`09630cf199`](https://github.com/nodejs/node/commit/09630cf199)] - **tools**: cherry-pick ffb34b6d5dbf0 (Darshan Sen) [#39725](https://github.com/nodejs/node/pull/39725)
- [[`26a067e33e`](https://github.com/nodejs/node/commit/26a067e33e)] - **tools**: update inspector_protocol to e8ba1a7 (Rich Trott) [#39694](https://github.com/nodejs/node/pull/39694)
- [[`9847d58feb`](https://github.com/nodejs/node/commit/9847d58feb)] - **tools**: update inspector_protocol to 39ca567 (Rich Trott) [#39694](https://github.com/nodejs/node/pull/39694)
- [[`6870bb7505`](https://github.com/nodejs/node/commit/6870bb7505)] - **tools**: update inspector_protocol to 97d3146 (Rich Trott) [#39694](https://github.com/nodejs/node/pull/39694)
- [[`383fa01e97`](https://github.com/nodejs/node/commit/383fa01e97)] - **_Revert_** "**tools**: fix compiler warning in inspector_protocol" (Rich Trott) [#39694](https://github.com/nodejs/node/pull/39694)
- [[`b95a759c86`](https://github.com/nodejs/node/commit/b95a759c86)] - **tools**: update inspector_protocol to a53e96d31a2755eb16ca37 (Rich Trott) [#39694](https://github.com/nodejs/node/pull/39694)
- [[`ad39687422`](https://github.com/nodejs/node/commit/ad39687422)] - **tools**: update inspector_protocol to fe0467fd105a (Rich Trott) [#39694](https://github.com/nodejs/node/pull/39694)
- [[`78de83cc74`](https://github.com/nodejs/node/commit/78de83cc74)] - **tools**: improve error detection in find-inactive-collaborators (Rich Trott) [#39617](https://github.com/nodejs/node/pull/39617)
- [[`a5152a0875`](https://github.com/nodejs/node/commit/a5152a0875)] - **tools**: flag README/mailmap mismatches in find-inactive-collaborators (Rich Trott) [#39477](https://github.com/nodejs/node/pull/39477)
- [[`87c5332f89`](https://github.com/nodejs/node/commit/87c5332f89)] - **tools**: use mailmap for find-inactive-collaborators (Rich Trott) [#39432](https://github.com/nodejs/node/pull/39432)
- [[`f75224f1ce`](https://github.com/nodejs/node/commit/f75224f1ce)] - **tools**: email matchin is case insensitive for .mailmap (Rich Trott) [#39430](https://github.com/nodejs/node/pull/39430)
- [[`dfb77a581f`](https://github.com/nodejs/node/commit/dfb77a581f)] - **tools**: make internal link checker more robust (Rich Trott) [#39429](https://github.com/nodejs/node/pull/39429)
- [[`d2c0da20a0`](https://github.com/nodejs/node/commit/d2c0da20a0)] - **tools**: added remark-frontmatter (Ben Halverson) [#38717](https://github.com/nodejs/node/pull/38717)
- [[`cec04821aa`](https://github.com/nodejs/node/commit/cec04821aa)] - **tools**: change commit fetch limiting in find-inactive-collaborators (Rich Trott) [#39362](https://github.com/nodejs/node/pull/39362)
- [[`d948148498`](https://github.com/nodejs/node/commit/d948148498)] - **tools**: use Node.js 16.x for GitHub workflow (Rich Trott) [#39362](https://github.com/nodejs/node/pull/39362)
- [[`edc5791b5a`](https://github.com/nodejs/node/commit/edc5791b5a)] - **tools**: add GitHub Action to run find-inactive-collaborators.mjs (Rich Trott) [#39335](https://github.com/nodejs/node/pull/39335)
- [[`d86d37bc9e`](https://github.com/nodejs/node/commit/d86d37bc9e)] - **tools**: relax max-len lint rule for template strings (Rich Trott) [#38097](https://github.com/nodejs/node/pull/38097)
- [[`f467e2a0c5`](https://github.com/nodejs/node/commit/f467e2a0c5)] - **tools**: pass bot token to node-pr-labeler (Michaël Zasso) [#39271](https://github.com/nodejs/node/pull/39271)
- [[`61ec594609`](https://github.com/nodejs/node/commit/61ec594609)] - **tools**: add find-inactive-collaborators.js (Rich Trott) [#39262](https://github.com/nodejs/node/pull/39262)
- [[`ff0ca11521`](https://github.com/nodejs/node/commit/ff0ca11521)] - **tools**: update path-parse to 1.0.7 (Rich Trott) [#39232](https://github.com/nodejs/node/pull/39232)
- [[`b8fb75121b`](https://github.com/nodejs/node/commit/b8fb75121b)] - **tools**: remove unused `lint-pr-commit-message.sh` (Richard Lau) [#39120](https://github.com/nodejs/node/pull/39120)
- [[`e7761b627f`](https://github.com/nodejs/node/commit/e7761b627f)] - **tools**: apply consistent comma-dangle lint rule (Rich Trott) [#37930](https://github.com/nodejs/node/pull/37930)
- [[`315eba7789`](https://github.com/nodejs/node/commit/315eba7789)] - **tools**: make comma-dangle ESLint rule more stringent … (Rich Trott) [#37088](https://github.com/nodejs/node/pull/37088)
- [[`3ecfe9d7ee`](https://github.com/nodejs/node/commit/3ecfe9d7ee)] - **tools**: update remark-preset-lint-node to 2.4.1 (Rich Trott) [#39201](https://github.com/nodejs/node/pull/39201)
- [[`70e527c0c7`](https://github.com/nodejs/node/commit/70e527c0c7)] - **tools**: upgrade `highlight.js` to version 11.0.1 (Antoine du Hamel) [#39032](https://github.com/nodejs/node/pull/39032)
- [[`7b2bebba7a`](https://github.com/nodejs/node/commit/7b2bebba7a)] - **tools**: add support for import assertions in linter (Antoine du Hamel) [#39924](https://github.com/nodejs/node/pull/39924)
- [[`1353a6e22f`](https://github.com/nodejs/node/commit/1353a6e22f)] - **tools**: update ESLint to 7.32.0 (Luigi Pinca) [#39602](https://github.com/nodejs/node/pull/39602)
- [[`509f26549c`](https://github.com/nodejs/node/commit/509f26549c)] - **tools**: update ESLint to 7.31.0 (Colin Ihrig) [#39424](https://github.com/nodejs/node/pull/39424)
- [[`f0e0c8f720`](https://github.com/nodejs/node/commit/f0e0c8f720)] - **tools**: update ESLint to 7.30.0 (Colin Ihrig) [#39242](https://github.com/nodejs/node/pull/39242)
- [[`6540c271e4`](https://github.com/nodejs/node/commit/6540c271e4)] - **tools**: update @babel/eslint-parser to 7.14.7 (Rich Trott) [#39160](https://github.com/nodejs/node/pull/39160)
- [[`d7e2318e74`](https://github.com/nodejs/node/commit/d7e2318e74)] - **tools**: add ESLint rule no-array-destructuring (Antoine du Hamel) [#36818](https://github.com/nodejs/node/pull/36818)
- [[`87e5429334`](https://github.com/nodejs/node/commit/87e5429334)] - **tools,doc**: fix error message for unrecognized type (Antoine du Hamel) [#39221](https://github.com/nodejs/node/pull/39221)
- [[`f206af679c`](https://github.com/nodejs/node/commit/f206af679c)] - **typings**: add a few JSDoc typings for the net lib module (nerdthatnoonelikes) [#38953](https://github.com/nodejs/node/pull/38953)
- [[`d458cd7e2b`](https://github.com/nodejs/node/commit/d458cd7e2b)] - **typings**: add JSDoc typings for timers (Voltrex) [#38834](https://github.com/nodejs/node/pull/38834)

Windows 32-bit Installer: https://nodejs.org/dist/v14.18.0/node-v14.18.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v14.18.0/node-v14.18.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v14.18.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v14.18.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v14.18.0/node-v14.18.0.pkg \
macOS Intel 64-bit Binary: https://nodejs.org/dist/v14.18.0/node-v14.18.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v14.18.0/node-v14.18.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v14.18.0/node-v14.18.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v14.18.0/node-v14.18.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v14.18.0/node-v14.18.0-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v14.18.0/node-v14.18.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v14.18.0/node-v14.18.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v14.18.0/node-v14.18.0.tar.gz \
Other release files: https://nodejs.org/dist/v14.18.0/ \
Documentation: https://nodejs.org/docs/v14.18.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

887bdbc61250431cf2fe062f55124833c02a9386fdd234d11aa868049ac1858e  node-v14.18.0-aix-ppc64.tar.gz
6b9b4d60bcb4eba95488380be8c4da4af98fce3f4a01c9a76db881cbb736656d  node-v14.18.0-darwin-x64.tar.gz
967e74229ba12141487b38bc4911125efd01397a35ec149db264b277792be8b1  node-v14.18.0-darwin-x64.tar.xz
a812312e2b82eee186d14cf78f08e1f3b5a397ecfaada3d7e574a070c50586b6  node-v14.18.0-headers.tar.gz
e06bf25f5f7b8cfcf0e6713b19e44f80011527e90de067e38740b6036eccbd5e  node-v14.18.0-headers.tar.xz
6261a87bf25d08e7b39017a1486b04c65be3ea0ea8442c090e1e4ec4d4cc6ebd  node-v14.18.0-linux-arm64.tar.gz
572cb0d673e0d67f141a64cbe27aeceef41d421e9c763966289d9816d7931711  node-v14.18.0-linux-arm64.tar.xz
3e1ef643adf658a7a27335a2f8efadba85ef9e5bdfffe121e18870537782691b  node-v14.18.0-linux-armv7l.tar.gz
7d6cc474230524f32a87f9c5eea24a2f53d7cac59d6d4db28a2e62a1eda10407  node-v14.18.0-linux-armv7l.tar.xz
67941bfa506372f0e82b6c75a88e9af2407e2b51da67665b6ccaef0bfb3fe14e  node-v14.18.0-linux-ppc64le.tar.gz
35fbf2fbab586ae06cb2440c8169bff6573991e81e95628e3d8af777e6c17c7f  node-v14.18.0-linux-ppc64le.tar.xz
976a57b21162cf731028a756da565ad68a39b87314b6d2afb2903d1dcc43b3f0  node-v14.18.0-linux-s390x.tar.gz
d6efddd341d77612186aa847dd4a7ae5905dc303506c227f2e9a25b94b4a7622  node-v14.18.0-linux-s390x.tar.xz
f411b8aee36d6dc6a5435906f42bd4ea59d6f678894cf562beaf115b58a318ee  node-v14.18.0-linux-x64.tar.gz
5c0bc18b19fd09ff80beb16772e69cb033ee4992a4ccd35bd884fd8f02e6d1ec  node-v14.18.0-linux-x64.tar.xz
81c3bcf76ddf5c7c1fbdf587c8fcdbd765c1533019bfe4ed2310eb0eeabf77ab  node-v14.18.0.pkg
2272312d7eb48a28e982af395142d916385b0572380d07c89f9abd9c97810189  node-v14.18.0.tar.gz
6b485158a0ae4e936346b45da6fdd2ee96cecfef82fce86f281e6bfa14d85859  node-v14.18.0.tar.xz
7a6681ec8df968421abec28d6fa957fdddf1f7708e52ac0d069e0108a4baa0e5  node-v14.18.0-win-x64.7z
2883e83ac3b1e1cb9a9bf65554043640849b39e86761e7c7ac50b664f42f20ff  node-v14.18.0-win-x64.zip
aa2da586d71437468f36c7e5f7143a2f5f8589e2e2d47c4303b2e221a498ecc6  node-v14.18.0-win-x86.7z
56973b1a9a7cd800e5dbf3cc14a73a99012f52402df9eaded081014f2dfd209e  node-v14.18.0-win-x86.zip
35aafc1b10f7041b1f361fb042f32a6484482ff8633cb9c5ffca36c10ef97536  node-v14.18.0-x64.msi
bf9a25b6f57e1e00bc0571068537c6f8da81f8d8c12b511090fe3b85ca7343a0  node-v14.18.0-x86.msi
262ec7222031430cb25fee4c3e932a94afe65a3614ecc8b68e47cfac4f76e7b3  win-x64/node.exe
6b6ed13aca8d29bfea176b171ef8630ce5bfcc528dca43d985452b9a9948f4bf  win-x64/node.lib
5b0773c40b17e4a3927b2755dbae2920cfdb070e0e100642a351034085b37e5f  win-x64/node_pdb.7z
a8628bf0e2c7ada4712887293017b7bff69348ddb5bd68fbd2aeac8e738c125f  win-x64/node_pdb.zip
b242b6ff988a6bb79cb0bd231b918e380fb77ad8085b1bcf4ef1f49751720533  win-x86/node.exe
2fa2858c3c462b6e1d94ba57ad0adc0b68dd6fa5693e7b8dc33b43c191c8a15f  win-x86/node.lib
927902b874a97d2c3e8b0ff1b85e16c50ed812bfef1a71d477a9e2f9da4449e1  win-x86/node_pdb.7z
9bad7e0bd46bf1db3ce1c48147018cd7ce7a712872cb9795c591bd8319d75678  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEj8yhP+8dDC6RAI4Jdw96mlrhVgAFAmFS/WQACgkQdw96mlrh
VgA1IQ/9Hy1WMliGwlUd/VWxbVSBsj1XeCnZSGjTRJTVQR9BS33ZsUJlD9okgrCi
YGF/512BcZkm2406Jpi7FlZUR+RNjv4JKgylL6WWWtp148H+IbQ7nMl9z6DRGsCU
DNGdDt6FVAiX+zBm44LLAQ/nUNCGp+GzXfsT3LQFGXJ/7ShgBoNjQnoPKKdYks8e
7B3rN6m4/q+bnZfDGxgjk4dQRmTr9L+Yb4zc9NS9QLD0zd/evhyaS+3ZCiWf5mnR
+eBKcmGAh2IkXHiPARe4qCGop0vqmjDw4HRFce40wp7t0RlOeUDxg0rFgwnwUxwj
0bTKhqQH6BGlesAMghvMMGTNZSO+29d0P3EwrbFPySmGnJrJOlD+CK3wYjO2Ub2T
hIXfKiJKPtawGJISCawsNN+D75Crp/bDtLVkK1HbZnJQmVOcWhsjf2L5kFveVdX7
VZQD6XwQn1s6VF1ZZDSnLXncM7yGpPQfrJLJ8DNFOCLzJiZhiQIQd+RnpxCFpRs6
ahUWHtfQ4VeP5TXqnd647EhLIq6+fsMcHcvNcrb1VEL0nxctk9aHff/KXdF8gEWs
965vZCyfBIDaql/1fdcATKh9GwTky5b+Ysq3hOnyx6G3FAiC/dp8tYUrEC2z1jSs
bwobLsvIs9tgHdh5iocffUxg7CcoxcnRPNRTqm8lfDKYk+rDMr4=
=QWM8
-----END PGP SIGNATURE-----

```
