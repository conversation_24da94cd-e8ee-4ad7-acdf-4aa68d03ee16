---
date: '2020-09-29T19:51:28.044Z'
category: release
title: Node v14.13.0 (Current)
layout: blog-post
author: <PERSON><PERSON>
---

### Notable Changes

- [[`19b95a7fa9`](https://github.com/nodejs/node/commit/19b95a7fa9)] - **(SEMVER-MINOR)** **deps**: upgrade to libuv 1.40.0 (<PERSON>) [#35333](https://github.com/nodejs/node/pull/35333)
- [[`f551f52f83`](https://github.com/nodejs/node/commit/f551f52f83)] - **(SEMVER-MINOR)** **module**: named exports for CJS via static analysis (Guy Bedford) [#35249](https://github.com/nodejs/node/pull/35249)
- [[`505731871e`](https://github.com/nodejs/node/commit/505731871e)] - **(SEMVER-MINOR)** **module**: exports pattern support (<PERSON>) [#34718](https://github.com/nodejs/node/pull/34718)
- [[`0d8eaa3942`](https://github.com/nodejs/node/commit/0d8eaa3942)] - **(SEMVER-MINOR)** **src**: allow N-API addon in `AddLinkedBinding()` (Anna Henningsen) [#35301](https://github.com/nodejs/node/pull/35301)

### Commits

- [[`19b95a7fa9`](https://github.com/nodejs/node/commit/19b95a7fa9)] - **(SEMVER-MINOR)** **deps**: upgrade to libuv 1.40.0 (Colin Ihrig) [#35333](https://github.com/nodejs/node/pull/35333)
- [[`353a567235`](https://github.com/nodejs/node/commit/353a567235)] - **deps**: upgrade to c-ares v1.16.1 (Shelley Vohr) [#35324](https://github.com/nodejs/node/pull/35324)
- [[`2e10616d48`](https://github.com/nodejs/node/commit/2e10616d48)] - **doc**: remove http2 non-link anchor tags (Rich Trott) [#35161](https://github.com/nodejs/node/pull/35161)
- [[`02db136c49`](https://github.com/nodejs/node/commit/02db136c49)] - **doc**: alphabetize error list (Rich Trott) [#35219](https://github.com/nodejs/node/pull/35219)
- [[`46a4154cab`](https://github.com/nodejs/node/commit/46a4154cab)] - **doc**: packages docs feedback (Guy Bedford) [#35370](https://github.com/nodejs/node/pull/35370)
- [[`70ad69ba46`](https://github.com/nodejs/node/commit/70ad69ba46)] - **doc**: outline when origin is set to unhandledRejection (Matthieu Larcher) [#35294](https://github.com/nodejs/node/pull/35294)
- [[`010173a4b7`](https://github.com/nodejs/node/commit/010173a4b7)] - **doc**: edit n-api.md for minor improvements (Rich Trott) [#35361](https://github.com/nodejs/node/pull/35361)
- [[`86ac7497e0`](https://github.com/nodejs/node/commit/86ac7497e0)] - **doc**: add history entry for breaking destroy() change (Gil Pedersen) [#35326](https://github.com/nodejs/node/pull/35326)
- [[`857e321baf`](https://github.com/nodejs/node/commit/857e321baf)] - **doc**: set encoding to hex before piping hash (Victor Antonio Barzana Crespo) [#35338](https://github.com/nodejs/node/pull/35338)
- [[`87dfed012c`](https://github.com/nodejs/node/commit/87dfed012c)] - **doc**: add gpg key export directions to releases doc (Danielle Adams) [#35298](https://github.com/nodejs/node/pull/35298)
- [[`1758ac8237`](https://github.com/nodejs/node/commit/1758ac8237)] - **doc**: added version 7 to N-API version matrix (NickNaso) [#35319](https://github.com/nodejs/node/pull/35319)
- [[`5da5d41b1c`](https://github.com/nodejs/node/commit/5da5d41b1c)] - **doc**: refine require/import conditions constraints (Guy Bedford) [#35311](https://github.com/nodejs/node/pull/35311)
- [[`482ce6ce1d`](https://github.com/nodejs/node/commit/482ce6ce1d)] - **doc**: improve N-API string-to-native doc (Gabriel Schulhof) [#35322](https://github.com/nodejs/node/pull/35322)
- [[`6dc6dadfc6`](https://github.com/nodejs/node/commit/6dc6dadfc6)] - **doc**: avoid referring to C array size (Tobias Nießen) [#35300](https://github.com/nodejs/node/pull/35300)
- [[`0a847ca729`](https://github.com/nodejs/node/commit/0a847ca729)] - **doc**: update napi_make_callback documentation (Gerhard Stoebich) [#35321](https://github.com/nodejs/node/pull/35321)
- [[`a8d3a7f742`](https://github.com/nodejs/node/commit/a8d3a7f742)] - **doc**: put landing specifics in details tag (Rich Trott) [#35296](https://github.com/nodejs/node/pull/35296)
- [[`dd530364d0`](https://github.com/nodejs/node/commit/dd530364d0)] - **doc**: fixup lutimes metadata (Anna Henningsen) [#35328](https://github.com/nodejs/node/pull/35328)
- [[`d7282c0ae3`](https://github.com/nodejs/node/commit/d7282c0ae3)] - **doc**: edit subpath export patterns introduction (Rich Trott) [#35254](https://github.com/nodejs/node/pull/35254)
- [[`1d1ce1fc2c`](https://github.com/nodejs/node/commit/1d1ce1fc2c)] - **doc**: document support for package.json fields (Antoine du HAMEL) [#34970](https://github.com/nodejs/node/pull/34970)
- [[`ef0d2ef5a2`](https://github.com/nodejs/node/commit/ef0d2ef5a2)] - **doc**: move package config docs to separate page (Antoine du HAMEL) [#34748](https://github.com/nodejs/node/pull/34748)
- [[`b9d767c4d5`](https://github.com/nodejs/node/commit/b9d767c4d5)] - **doc**: change type of child_process.signalCode to string (Linn Dahlgren) [#35223](https://github.com/nodejs/node/pull/35223)
- [[`b4514d464d`](https://github.com/nodejs/node/commit/b4514d464d)] - **doc**: replace "this guide" link text with guide title (Rich Trott) [#35283](https://github.com/nodejs/node/pull/35283)
- [[`1893449724`](https://github.com/nodejs/node/commit/1893449724)] - **doc**: revise dependency redirection text in policy.md (Rich Trott) [#35276](https://github.com/nodejs/node/pull/35276)
- [[`0c4540b050`](https://github.com/nodejs/node/commit/0c4540b050)] - **doc**: fix heading space bug in assert.md (Thomas Hunter II) [#35310](https://github.com/nodejs/node/pull/35310)
- [[`ec6b78ae73`](https://github.com/nodejs/node/commit/ec6b78ae73)] - **doc**: add `socket.readyState` (Clark Kozak) [#35262](https://github.com/nodejs/node/pull/35262)
- [[`2a4ae0926d`](https://github.com/nodejs/node/commit/2a4ae0926d)] - **doc**: update crypto.createSecretKey accepted types (Filip Skokan) [#35246](https://github.com/nodejs/node/pull/35246)
- [[`c09f3dc2f3`](https://github.com/nodejs/node/commit/c09f3dc2f3)] - **doc**: put release script specifics in details (Myles Borins) [#35260](https://github.com/nodejs/node/pull/35260)
- [[`99a79e32a6`](https://github.com/nodejs/node/commit/99a79e32a6)] - **fs**: fix fs.promises.writeFile with typed arrays (Michaël Zasso) [#35376](https://github.com/nodejs/node/pull/35376)
- [[`ab7d0e92b1`](https://github.com/nodejs/node/commit/ab7d0e92b1)] - **meta**: update module pages in CODEOWNERS (Antoine du Hamel) [#34932](https://github.com/nodejs/node/pull/34932)
- [[`f551f52f83`](https://github.com/nodejs/node/commit/f551f52f83)] - **(SEMVER-MINOR)** **module**: named exports for CJS via static analysis (Guy Bedford) [#35249](https://github.com/nodejs/node/pull/35249)
- [[`505731871e`](https://github.com/nodejs/node/commit/505731871e)] - **(SEMVER-MINOR)** **module**: exports pattern support (Guy Bedford) [#34718](https://github.com/nodejs/node/pull/34718)
- [[`68ea7f5560`](https://github.com/nodejs/node/commit/68ea7f5560)] - **module**: fix crash on multiline named cjs imports (Christoph Tavan) [#35275](https://github.com/nodejs/node/pull/35275)
- [[`0f4ecaa741`](https://github.com/nodejs/node/commit/0f4ecaa741)] - **repl**: standardize Control key indications (Rich Trott) [#35270](https://github.com/nodejs/node/pull/35270)
- [[`1e1cb94e69`](https://github.com/nodejs/node/commit/1e1cb94e69)] - **src**: fix incorrect SIGSEGV handling in NODE_USE_V8_WASM_TRAP_HANDLER (Anatoly Korniltsev) [#35282](https://github.com/nodejs/node/pull/35282)
- [[`0d8eaa3942`](https://github.com/nodejs/node/commit/0d8eaa3942)] - **(SEMVER-MINOR)** **src**: allow N-API addon in `AddLinkedBinding()` (Anna Henningsen) [#35301](https://github.com/nodejs/node/pull/35301)
- [[`f2635b317e`](https://github.com/nodejs/node/commit/f2635b317e)] - **test**: replace annonymous functions with arrow (Pooja D.P) [#34921](https://github.com/nodejs/node/pull/34921)
- [[`d7c28c9243`](https://github.com/nodejs/node/commit/d7c28c9243)] - **test,child_process**: add tests for signalCode value (Rich Trott) [#35327](https://github.com/nodejs/node/pull/35327)
- [[`80eb22185e`](https://github.com/nodejs/node/commit/80eb22185e)] - **tools**: update ESLint to 7.10.0 (Colin Ihrig) [#35366](https://github.com/nodejs/node/pull/35366)
- [[`7f355735df`](https://github.com/nodejs/node/commit/7f355735df)] - **tools**: ignore build folder when checking links (Ash Cripps) [#35315](https://github.com/nodejs/node/pull/35315)
- [[`c5d27e1e29`](https://github.com/nodejs/node/commit/c5d27e1e29)] - **tools,doc**: enforce alphabetical order for md refs (Antoine du Hamel) [#35244](https://github.com/nodejs/node/pull/35244)
- [[`9d91842a9d`](https://github.com/nodejs/node/commit/9d91842a9d)] - **tools,doc**: upgrade dependencies (Antoine du Hamel) [#35244](https://github.com/nodejs/node/pull/35244)

Windows 32-bit Installer: https://nodejs.org/dist/v14.13.0/node-v14.13.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v14.13.0/node-v14.13.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v14.13.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v14.13.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v14.13.0/node-v14.13.0.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v14.13.0/node-v14.13.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v14.13.0/node-v14.13.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v14.13.0/node-v14.13.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v14.13.0/node-v14.13.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v14.13.0/node-v14.13.0-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v14.13.0/node-v14.13.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v14.13.0/node-v14.13.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v14.13.0/node-v14.13.0.tar.gz \
Other release files: https://nodejs.org/dist/v14.13.0/ \
Documentation: https://nodejs.org/docs/v14.13.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

73f7fb1b63c7ffa2ebfddceca64a0bde23cee735398db31eb9006a9272cdfa0e  node-v14.13.0-aix-ppc64.tar.gz
e9fd3fcc5adf3266881a36f72238e65041e2d318509edcbd7e6b2f902b7a7514  node-v14.13.0-darwin-x64.tar.gz
8a35817d21f8a203381c34b039f3c65ccceebd2e3859950c55c52b3d179417c6  node-v14.13.0-darwin-x64.tar.xz
a75416c602b1b6e33cedd34723d0a27543e6a204f48dc19fdb6255a499ea9cc9  node-v14.13.0-headers.tar.gz
5e0df673df77becbb38c2061fd20703654a3798bcd6aa5f22b155cd7039bca0e  node-v14.13.0-headers.tar.xz
a9a98ef518c9e75d0a33d8f344f76b037b54e4ad8f8051fbf1506dbfccfb3f25  node-v14.13.0-linux-arm64.tar.gz
38b4ed6f5afdb3fd963b67f3f2bf6e43a3a6dfc4358e195c20981f184fa6494b  node-v14.13.0-linux-arm64.tar.xz
8bdb438c9962054c75261a597fdfa196712e1c11f91b64b8ca701aa62355832e  node-v14.13.0-linux-armv7l.tar.gz
04267a5e919d280212ea10b7dab90c4fabcb8e82234726d4fdb2fec3342caea9  node-v14.13.0-linux-armv7l.tar.xz
6af5661d4a043be77d9113b5164c8f647228e2342cce7882a8994751a591d361  node-v14.13.0-linux-ppc64le.tar.gz
8dad59e781ac1bea3cb042ca31be3ea88edf6b9eeab7d146a73baf43e4e6163f  node-v14.13.0-linux-ppc64le.tar.xz
024fb80d2c383d5f2d99e34fa65f139045511436ca7d9becf01b246f18eec1f0  node-v14.13.0-linux-s390x.tar.gz
4e785fb51659ad93443646242f2e4e892b30430b3a5e1f5b28a664d547264359  node-v14.13.0-linux-s390x.tar.xz
f7b4001e7172a2af32743607b457844adafcdeff555685876ddabaad43b5d71a  node-v14.13.0-linux-x64.tar.gz
8dbf2869033b315de8369405bc3cd5a19fb11afc824fe616640e2743a9a84cc4  node-v14.13.0-linux-x64.tar.xz
56bf5b46d0e62f44463d0381682d0c0b540f51319425f1554bd9791e4871d1b4  node-v14.13.0.pkg
8538b2e76aa06ee0e6eb1c118426c3c5ca53b2e49d66591738eacf76e89edd61  node-v14.13.0.tar.gz
e0b9bed440b41005f840c8ab1747ec079d3aa12fc227f62ec27395489ced06e4  node-v14.13.0.tar.xz
eacfcd04aebd3b6b5f57de32f21fa39b342dfe5d169747fec4257c6b0a4c2136  node-v14.13.0-win-x64.7z
9652ca2c47c08eec440035b3594d7927e567246ea1217ccd73dcb5a41cf89c3d  node-v14.13.0-win-x64.zip
3e024acbbc8d635637a505224ceda8dd469450c4cc4a513d6b73141fb4669216  node-v14.13.0-win-x86.7z
f72841327d07632f030ec29a99d4ffe28bcc1a1582ffcb54c8c0f9592d61b2f8  node-v14.13.0-win-x86.zip
3bea7e997088674237b9de7771a375d794ee55ae0200d3d49ef5d4e5868a6ee9  node-v14.13.0-x64.msi
9fe6f272a4ff0cbf6e6be2b804b7c6c17fa5e7a207df5fd72e7d764f4671cac5  node-v14.13.0-x86.msi
3a8be36999f30c21e32b1ec84140e9d2ca39243738dfcea3dff81193b7c301e7  win-x64/node.exe
a30524920387674e522728cdb0f34db589889da98ef9bfd769235dd73552cc9c  win-x64/node.lib
5ef65b6a6d6bfed66892a697081a8245bb6dc76166243a3a206dc8e68d67b225  win-x64/node_pdb.7z
cebe9246a20cf29574405d9579dc06f93600e2feeed34f7e14dd3f4709dedc7b  win-x64/node_pdb.zip
1e5856bb92c04f4506181a4d89c38eb51743f410cd208ea9d770e1793b2a829a  win-x86/node.exe
831a9165fe3d63afc1d5b5ac2238235f3227cf1bc19b46b5f45e8c4d670c29e7  win-x86/node.lib
896a383a5c690d6100629cde7f165f48381a48689131bf82f7ba74d8bd9d07d9  win-x86/node_pdb.7z
772aa63616e15278b146b12d9b6f0d8cc4fa29a1c09f48beb6ce38922960cea9  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQEzBAEBCAAdFiEEDv/hvO/ZyE49CYFSkzsB9AtcqUYFAl9zjwwACgkQkzsB9Atc
qUZVsAf+IhNXe9cHyYSZ7jLeFjKAyQ/mp/KRqFt78Mq9HbRLGNrF0zZpK1tRvY6E
yB2uEy+OF1tW/xdWqTxgQH7kiQvYeyuAHPddDM+rZLRt0NimrvXFT02HfvwqB6mz
XglnJWEVVm1xLLfLAXsLMA7Hh+x+zAmL3FI3VXNAPoylwfgsCu6cCrMIjQOqiMhk
tgNBWz/OC1SwLcHNdQp92MTRT2E1R6OtTx+7rgepb5wznBd6y3t3o8sFn3z8yRZE
zyOi7q1uDuZ2Tv/bWk/grS4B81/WCP+FclfF45d+8S5oGPMS84HhgwLd2i8LKqei
gF2SGCpluA3dWGGoPZK1Ju573mXXeA==
=KkvE
-----END PGP SIGNATURE-----

```
