---
date: '2020-01-09T21:57:08.665Z'
category: release
title: Node v10.18.1 (LTS)
layout: blog-post
author: <PERSON>
---

### Notable changes

- **http2**: fix session memory accounting after pausing (<PERSON>) [#30684](https://github.com/nodejs/node/pull/30684)
- **n-api**: correct bug in napi_get_last_error (Octavian <PERSON>) [#28702](https://github.com/nodejs/node/pull/28702)
- **tools**: update tzdata to 2019c (<PERSON><PERSON>) [#30479](https://github.com/nodejs/node/pull/30479)

### Commits

- [[`a80c59130e`](https://github.com/nodejs/node/commit/a80c59130e)] - **build**: fix configure script to work with Apple Clang 11 (Saagar Jha) [#28071](https://github.com/nodejs/node/pull/28071)
- [[`68b2b5cc51`](https://github.com/nodejs/node/commit/68b2b5cc51)] - **build,win**: propagate error codes in vcbuild (<PERSON>is) [#30724](https://github.com/nodejs/node/pull/30724)
- [[`3e0709cf5e`](https://github.com/nodejs/node/commit/3e0709cf5e)] - **deps**: V8: backport fb63e5cf55e9 (Michaël Zasso)
- [[`25b8fbda35`](https://github.com/nodejs/node/commit/25b8fbda35)] - **doc**: allow \<code\> in header elements (Rich Trott) [#31086](https://github.com/nodejs/node/pull/31086)
- [[`a1b095dd46`](https://github.com/nodejs/node/commit/a1b095dd46)] - **doc,dns**: use code markup/markdown in headers (Rich Trott) [#31086](https://github.com/nodejs/node/pull/31086)
- [[`8f3b8ca515`](https://github.com/nodejs/node/commit/8f3b8ca515)] - **http2**: fix session memory accounting after pausing (Michael Lehenbauer) [#30684](https://github.com/nodejs/node/pull/30684)
- [[`20f64a96de`](https://github.com/nodejs/node/commit/20f64a96de)] - **http2**: use the latest settings (ZYSzys) [#29780](https://github.com/nodejs/node/pull/29780)
- [[`81c31005fd`](https://github.com/nodejs/node/commit/81c31005fd)] - **lib**: fix comment nits in bootstrap\loaders.js (Vse Mozhet Byt) [#24641](https://github.com/nodejs/node/pull/24641)
- [[`88e8b7cf83`](https://github.com/nodejs/node/commit/88e8b7cf83)] - **n-api**: correct bug in napi_get_last_error (Octavian Soldea) [#28702](https://github.com/nodejs/node/pull/28702)
- [[`77e0318849`](https://github.com/nodejs/node/commit/77e0318849)] - **stream**: increase MAX_HWM (Robert Nagy) [#29938](https://github.com/nodejs/node/pull/29938)
- [[`894aaa2040`](https://github.com/nodejs/node/commit/894aaa2040)] - **stream**: extract Readable.from in its own file (Matteo Collina) [#30140](https://github.com/nodejs/node/pull/30140)
- [[`7e941eb17d`](https://github.com/nodejs/node/commit/7e941eb17d)] - **test**: do not fail SLOW tests if they are not slow (Yang Guo) [#25868](https://github.com/nodejs/node/pull/25868)
- [[`0f3ae77aaf`](https://github.com/nodejs/node/commit/0f3ae77aaf)] - **tools**: update tzdata to 2019c (Myles Borins) [#30479](https://github.com/nodejs/node/pull/30479)
- [[`4ae8d204cb`](https://github.com/nodejs/node/commit/4ae8d204cb)] - **tools**: move python code out of jenkins shell (Sam Roberts) [#28458](https://github.com/nodejs/node/pull/28458)
- [[`4879b80d87`](https://github.com/nodejs/node/commit/4879b80d87)] - **tools**: fix v8 testing with devtoolset on ppcle (Sam Roberts) [#28458](https://github.com/nodejs/node/pull/28458)

Windows 32-bit Installer: https://nodejs.org/dist/v10.18.1/node-v10.18.1-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v10.18.1/node-v10.18.1-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v10.18.1/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v10.18.1/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v10.18.1/node-v10.18.1.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v10.18.1/node-v10.18.1-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v10.18.1/node-v10.18.1-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v10.18.1/node-v10.18.1-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v10.18.1/node-v10.18.1-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v10.18.1/node-v10.18.1-aix-ppc64.tar.gz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v10.18.1/node-v10.18.1-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v10.18.1/node-v10.18.1-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v10.18.1/node-v10.18.1-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v10.18.1/node-v10.18.1-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v10.18.1/node-v10.18.1.tar.gz \
Other release files: https://nodejs.org/dist/v10.18.1/ \
Documentation: https://nodejs.org/docs/v10.18.1/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

07d8baf00e4456d678c4828bc597808f0789abfbc91e81172bed650f1b72477d  node-v10.18.1-aix-ppc64.tar.gz
2b2d3379420e626eee393cabf1c90bc55957ff5bb067b82a74eb2f92147d6757  node-v10.18.1-darwin-x64.tar.gz
ea344da9fc5e07f1bdf5b192813d22b0e94d78e50bd7965711c01d99f094d9b0  node-v10.18.1-darwin-x64.tar.xz
4bfe41402c1573d20e03bee0533d1bcb3a36b031348f840f48278987550022af  node-v10.18.1-headers.tar.gz
280c09fd7b03479d538929a8d4479c44d6d70bde429c398c21a823bc51cf214c  node-v10.18.1-headers.tar.xz
554b42da76877a9c5ab0054b492fef0d5847b06217e466728b1e73547e55c7da  node-v10.18.1-linux-arm64.tar.gz
9a6203697e0087a1c909961481d579c76777df1df622921e4ce16198851f30fe  node-v10.18.1-linux-arm64.tar.xz
9f50007efdacdd9abd81b6b67548b2318fbbaddcdb27639f6b2837831f112aa3  node-v10.18.1-linux-armv6l.tar.gz
3b0fe9858b9235fd3de1893fa1872b5c1516a07733ebfd9d46ae37a071c920aa  node-v10.18.1-linux-armv6l.tar.xz
a1d03512da3fd602f3ed28c0d36e41f9b532e1e6451784bd4499f8982400c7cc  node-v10.18.1-linux-armv7l.tar.gz
df15d99279a5d367d3f13c70b5008b800919ffa4ca445dba0e85f95183ce7fc6  node-v10.18.1-linux-armv7l.tar.xz
a64a9367167bcb9052d4d0c47ea2d642fba0819b2a67ef0c85db19df19c14069  node-v10.18.1-linux-ppc64le.tar.gz
53263a462f5f38064f58c17c5d880e6e905abccc65cc0b986ac29715303bd455  node-v10.18.1-linux-ppc64le.tar.xz
09d2433f50e1acda684a55d887613510ebaf8ed89ce5481ca85d4f869623b71d  node-v10.18.1-linux-s390x.tar.gz
ad2c4e65667e470aac69d54ef0261cc878fbe7d58b73f273492566f6aaab5e54  node-v10.18.1-linux-s390x.tar.xz
812fe7d421894b792027d19c78c919faad3bf32d8bc16bde67f5c7eea2469eac  node-v10.18.1-linux-x64.tar.gz
8cc40f45c2c62529b15e83a6bbe0ac1febf57af3c5720df68067c96c0fddbbdf  node-v10.18.1-linux-x64.tar.xz
3bc5ff247263ad94b48c539b4100733f28449a887ae70ece16c4a7da4dcaabd0  node-v10.18.1.pkg
59f7471ed55d57346bc9a12d1666f770766bb07d871c21502e13e64b14e117b6  node-v10.18.1-sunos-x64.tar.gz
c9c885da49cb3dc5d020fb2410ad392bbbdbf341c969a64e6444bf63c893912f  node-v10.18.1-sunos-x64.tar.xz
80a61ffbe6d156458ed54120eb0e9fff7b626502e0986e861d91b365f7e876db  node-v10.18.1.tar.gz
39af1837f439af7b4dc40ec18a64221c688c3982858168ae535bbe4911e8ea35  node-v10.18.1.tar.xz
ecd4923e2014fb6c05b10ddd9eb498511efadc3c6da93bd7a0406623e9b6c22c  node-v10.18.1-win-x64.7z
fb27bb95c27c72f2e25d0c41309b606b2ae48ba0d6094a19f206ad1df9dc5e19  node-v10.18.1-win-x64.zip
ff3cb3727f4f175fd015535b93dbffff5b4ba6ea09f18ade6fd311c70574b994  node-v10.18.1-win-x86.7z
ffe874d6edfc56c88b85de118e14a2e999fa344e8814cc1e1d9cd4048dd75461  node-v10.18.1-win-x86.zip
fbd9d0406a26d385ab40150cc269fcc8f9f73448d08619dd45362075d3c45525  node-v10.18.1-x64.msi
a9f1970812902a302f04d09aecdb4dba0f1da4225815c7830679ee91c0cca655  node-v10.18.1-x86.msi
63ba1415044a12b9437d7116d1ce4e409eddd1bc96b3b59c17c9da85ce261c20  win-x64/node.exe
43e8403c4b125618e82df4f2f8142d3e05b74a4a0a6b60e53222bf27de2c6bf4  win-x64/node.lib
552086b3a80c518eae8555af30a1c1aec8e4414f3237bd03fbf68bcd1034fe75  win-x64/node_pdb.7z
eba8aad2fe8628b8d2a79726e4ae4c229259d92342b689985e28617fd8961ced  win-x64/node_pdb.zip
c3504f950976657eeebf61c536675eac022a97647ac7591a73185b76b2a5bf97  win-x86/node.exe
4de7aa6902e7b17660e6a1f43b8fab8c7bf082aeed9a21833f474fe4a170596a  win-x86/node.lib
0394e3df203c2c3afa5faa177e2d5a5e9fc840997321f0e9afdca24d755f2568  win-x86/node_pdb.7z
22e29260c95806245dd46217ab78e0bb2c8c12b339d65c60582110e38d1d344a  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQEzBAEBCAAdFiEETtd49TnjY0x3nIfG1wYoSKGrAFwFAl4XoW0ACgkQ1wYoSKGr
AFyQlwgAmstrJIUL+jvo9WBZVdWrYMwQ26G0kFOJYMuqcu7nlPsZTGn8zg6/EK4S
i1gjwYDRj9DcLs+XBlHLpuFj9NypD5dVXKdk9pGbVRC4FW847v+ki7FFlmNWP9C5
zNa9kxERFeXW8wFRxR+vyf306YO9+bMEQAMONr2Asy/hBp/a8h7soMzHHju+M1cg
c7eoWRG+rMVIc3zfZTlaq/m6JcXCsa7vVT1sWe72RN+oXeICm1Tn84qpdZY83CAl
wTaNoseTp8Y3FN0rk7HjwkzPBu4GLAcGWeYAfOPY/1xeawaQJQGRG+k9iKt+eivk
wg/AXuArMv8LSZQPhKGTapgm/4SQcQ==
=HyP3
-----END PGP SIGNATURE-----

```
