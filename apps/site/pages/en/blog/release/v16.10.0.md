---
date: '2021-09-22T20:50:23.403Z'
category: release
title: Node v16.10.0 (Current)
layout: blog-post
author: <PERSON>
---

### Notable Changes

- [[`fb226ff2ee`](https://github.com/nodejs/node/commit/fb226ff2ee)] - **(SEMVER-MINOR)** **crypto**: add rsa-pss keygen parameters (<PERSON><PERSON>) [#39927](https://github.com/nodejs/node/pull/39927)
- [[`85206b7311`](https://github.com/nodejs/node/commit/85206b7311)] - **deps**: upgrade npm to 7.24.0 (npm team) [#40167](https://github.com/nodejs/node/pull/40167)
- [[`98f56d179c`](https://github.com/nodejs/node/commit/98f56d179c)] - **deps**: update <PERSON>corn to v8.5.0 (<PERSON><PERSON><PERSON>) [#40015](https://github.com/nodejs/node/pull/40015)
- [[`9655329772`](https://github.com/nodejs/node/commit/9655329772)] - **doc**: add Ayase-252 to collaborators (Qingyu Deng) [#40078](https://github.com/nodejs/node/pull/40078)
- [[`59fff925be`](https://github.com/nodejs/node/commit/59fff925be)] - **(SEMVER-MINOR)** **fs**: make `open` and `close` stream override optional when unused (Antoine du Hamel) [#40013](https://github.com/nodejs/node/pull/40013)
- [[`a63a4bce90`](https://github.com/nodejs/node/commit/a63a4bce90)] - **(SEMVER-MINOR)** **http**: limit requests per connection (Artur K) [#40082](https://github.com/nodejs/node/pull/40082)
  - The maximum number of requests a socket can handle before closing keep alive connection can be set with `server.maxRequestsPerSocket`.
- [[`9a672961fa`](https://github.com/nodejs/node/commit/9a672961fa)] - **(SEMVER-MINOR)** **src**: add --no-global-search-paths cli option (Cheng Zhao) [#39754](https://github.com/nodejs/node/pull/39754)
  - Adds the `--no-global-search-paths` command-line option to not search modules from global paths like `$HOME/.node_modules` and `$NODE_PATH`.
- [[`fe920b6cbf`](https://github.com/nodejs/node/commit/fe920b6cbf)] - **(SEMVER-MINOR)** **src**: make napi_create_reference accept symbol (JckXia) [#39926](https://github.com/nodejs/node/pull/39926)
- [[`97f3072ceb`](https://github.com/nodejs/node/commit/97f3072ceb)] - **(SEMVER-MINOR)** **stream**: add signal support to pipeline generators (Robert Nagy) [#39067](https://github.com/nodejs/node/pull/39067)

### Commits

- [[`b7dc651884`](https://github.com/nodejs/node/commit/b7dc651884)] - **build**: run modified internet tests on GitHub Actions (Rich Trott) [#40100](https://github.com/nodejs/node/pull/40100)
- [[`8d5787a043`](https://github.com/nodejs/node/commit/8d5787a043)] - **build**: add .mailmap/AUTHORS to paths-ignore for test-macos (Rich Trott) [#40109](https://github.com/nodejs/node/pull/40109)
- [[`9793e7ff08`](https://github.com/nodejs/node/commit/9793e7ff08)] - **build**: add .mailmap/AUTHORS to path-ignore for test-asan (Rich Trott) [#40109](https://github.com/nodejs/node/pull/40109)
- [[`886921de38`](https://github.com/nodejs/node/commit/886921de38)] - **build**: add paths-ignore for build-tarball workflow (Rich Trott) [#40109](https://github.com/nodejs/node/pull/40109)
- [[`01b1946b38`](https://github.com/nodejs/node/commit/01b1946b38)] - **build**: only lint version numbers for pull requests (Michaël Zasso) [#40027](https://github.com/nodejs/node/pull/40027)
- [[`c804d070a6`](https://github.com/nodejs/node/commit/c804d070a6)] - **build**: add daily/on-demand internet test workflow (Rich Trott) [#40086](https://github.com/nodejs/node/pull/40086)
- [[`7bddaecbf4`](https://github.com/nodejs/node/commit/7bddaecbf4)] - **build**: add YAML linting to GitHub Actions (Rich Trott) [#40007](https://github.com/nodejs/node/pull/40007)
- [[`5a20f9055c`](https://github.com/nodejs/node/commit/5a20f9055c)] - **build**: add YAML linting (Rich Trott) [#40007](https://github.com/nodejs/node/pull/40007)
- [[`0b30867c08`](https://github.com/nodejs/node/commit/0b30867c08)] - **build**: run AUTHORS update weekly (Rich Trott) [#40004](https://github.com/nodejs/node/pull/40004)
- [[`22a78a75ee`](https://github.com/nodejs/node/commit/22a78a75ee)] - **build**: preserves symbols during LTO with macOS linker (Jesse Chan) [#39839](https://github.com/nodejs/node/pull/39839)
- [[`f0dec58d43`](https://github.com/nodejs/node/commit/f0dec58d43)] - **crypto**: fix webcrypto ed(25519|448) spki/pkcs8 import (Filip Skokan) [#40131](https://github.com/nodejs/node/pull/40131)
- [[`d80082f3eb`](https://github.com/nodejs/node/commit/d80082f3eb)] - **crypto**: use `validateObject` (Voltrex) [#39872](https://github.com/nodejs/node/pull/39872)
- [[`d657ae6f8a`](https://github.com/nodejs/node/commit/d657ae6f8a)] - **crypto**: fix RSA-PSS default saltLength (Tobias Nießen) [#39999](https://github.com/nodejs/node/pull/39999)
- [[`fc45cbe7a8`](https://github.com/nodejs/node/commit/fc45cbe7a8)] - **crypto**: fix default MGF1 hash for OpenSSL 3 (Tobias Nießen) [#40031](https://github.com/nodejs/node/pull/40031)
- [[`105c9e6d3b`](https://github.com/nodejs/node/commit/105c9e6d3b)] - **crypto**: check webcrypto asymmetric key types during importKey (Filip Skokan) [#39962](https://github.com/nodejs/node/pull/39962)
- [[`fb226ff2ee`](https://github.com/nodejs/node/commit/fb226ff2ee)] - **(SEMVER-MINOR)** **crypto**: add rsa-pss keygen parameters (Filip Skokan) [#39927](https://github.com/nodejs/node/pull/39927)
- [[`85206b7311`](https://github.com/nodejs/node/commit/85206b7311)] - **deps**: upgrade npm to 7.24.0 (npm team) [#40167](https://github.com/nodejs/node/pull/40167)
- [[`06f6e01f37`](https://github.com/nodejs/node/commit/06f6e01f37)] - **deps**: add riscv64 into openssl Makefile and gen openssl-riscv64 (Lu Yahan) [#40063](https://github.com/nodejs/node/pull/40063)
- [[`9c76c69972`](https://github.com/nodejs/node/commit/9c76c69972)] - **deps**: patch V8 to 9.3.345.19 (Michaël Zasso) [#40108](https://github.com/nodejs/node/pull/40108)
- [[`0df47d5843`](https://github.com/nodejs/node/commit/0df47d5843)] - **deps**: upgrade npm to 7.23.0 (npm team) [#40055](https://github.com/nodejs/node/pull/40055)
- [[`b3843bf417`](https://github.com/nodejs/node/commit/b3843bf417)] - **deps**: patch v8 for vs2019 in std17 (Jiawen Geng) [#40060](https://github.com/nodejs/node/pull/40060)
- [[`67759585a0`](https://github.com/nodejs/node/commit/67759585a0)] - **deps**: patch for v8 on windows (Jiawen Geng) [#40010](https://github.com/nodejs/node/pull/40010)
- [[`98f56d179c`](https://github.com/nodejs/node/commit/98f56d179c)] - **deps**: update Acorn to v8.5.0 (Michaël Zasso) [#40015](https://github.com/nodejs/node/pull/40015)
- [[`5c6708582e`](https://github.com/nodejs/node/commit/5c6708582e)] - **dns**: cleanup validation (Voltrex) [#40061](https://github.com/nodejs/node/pull/40061)
- [[`e4825dcfd5`](https://github.com/nodejs/node/commit/e4825dcfd5)] - **doc**: changes default values for fs.read fns (RISHABH BUDHIRAJA) [#39163](https://github.com/nodejs/node/pull/39163)
- [[`0254b4b0d3`](https://github.com/nodejs/node/commit/0254b4b0d3)] - **doc**: fix markdown indentation in lists (Michaël Zasso) [#40142](https://github.com/nodejs/node/pull/40142)
- [[`b6939a3419`](https://github.com/nodejs/node/commit/b6939a3419)] - **doc**: prepare README.md for stricter linting (Rich Trott) [#40137](https://github.com/nodejs/node/pull/40137)
- [[`a07d8444f9`](https://github.com/nodejs/node/commit/a07d8444f9)] - **doc**: fix comma splice (Rich Trott) [#40133](https://github.com/nodejs/node/pull/40133)
- [[`2488bc0c4f`](https://github.com/nodejs/node/commit/2488bc0c4f)] - **doc**: clean up weird notes about reentrancy (Anna Henningsen) [#40107](https://github.com/nodejs/node/pull/40107)
- [[`8b80dcbc30`](https://github.com/nodejs/node/commit/8b80dcbc30)] - **doc**: correct parameters in fs and stream documentation (vipul kumar) [#39984](https://github.com/nodejs/node/pull/39984)
- [[`1ced732078`](https://github.com/nodejs/node/commit/1ced732078)] - **doc**: fix CJS-ESM selector in Safari (Bradley Farias) [#40135](https://github.com/nodejs/node/pull/40135)
- [[`7fdb12739d`](https://github.com/nodejs/node/commit/7fdb12739d)] - **doc**: add timeout.close (Nikita Galkin) [#40036](https://github.com/nodejs/node/pull/40036)
- [[`81cb14bb58`](https://github.com/nodejs/node/commit/81cb14bb58)] - **doc**: clarify that ObjectWrap requires manual cleanup on shutdown (Gerhard Stöbich) [#40074](https://github.com/nodejs/node/pull/40074)
- [[`8aad81dd99`](https://github.com/nodejs/node/commit/8aad81dd99)] - **doc**: add full list of subsystems (FrankQiu) [#39971](https://github.com/nodejs/node/pull/39971)
- [[`9655329772`](https://github.com/nodejs/node/commit/9655329772)] - **doc**: add Ayase-252 to collaborators (Qingyu Deng) [#40078](https://github.com/nodejs/node/pull/40078)
- [[`6d399e11e9`](https://github.com/nodejs/node/commit/6d399e11e9)] - **doc**: fix CCM cipher example in MJS (Tobias Nießen) [#39949](https://github.com/nodejs/node/pull/39949)
- [[`d426ee9b17`](https://github.com/nodejs/node/commit/d426ee9b17)] - **doc**: fix property name 'detail' of performanceEntry (Christian Boehlke) [#40019](https://github.com/nodejs/node/pull/40019)
- [[`846e7e880e`](https://github.com/nodejs/node/commit/846e7e880e)] - **doc**: fix list indentation in corepack.md (Alexey Ten) [#40029](https://github.com/nodejs/node/pull/40029)
- [[`b6dd2ea930`](https://github.com/nodejs/node/commit/b6dd2ea930)] - **doc**: fix missing history version in `fs.md` (Antoine du Hamel) [#39972](https://github.com/nodejs/node/pull/39972)
- [[`f666f5a8d1`](https://github.com/nodejs/node/commit/f666f5a8d1)] - **events**: fix duplicate require which cause performance penalty (wwwzbwcom) [#39892](https://github.com/nodejs/node/pull/39892)
- [[`59fff925be`](https://github.com/nodejs/node/commit/59fff925be)] - **(SEMVER-MINOR)** **fs**: make `open` and `close` stream override optional when unused (Antoine du Hamel) [#40013](https://github.com/nodejs/node/pull/40013)
- [[`a63a4bce90`](https://github.com/nodejs/node/commit/a63a4bce90)] - **(SEMVER-MINOR)** **http**: limit requests per connection (Artur K) [#40082](https://github.com/nodejs/node/pull/40082)
- [[`bc9c2ca6af`](https://github.com/nodejs/node/commit/bc9c2ca6af)] - **http**: remove CRLF variable (shfshanyue) [#40101](https://github.com/nodejs/node/pull/40101)
- [[`dd50b91f77`](https://github.com/nodejs/node/commit/dd50b91f77)] - **lib**: remove useless statement (Maledong) [#39983](https://github.com/nodejs/node/pull/39983)
- [[`608528028c`](https://github.com/nodejs/node/commit/608528028c)] - **lib**: avoid creating a throw away object in `validateObject` (Antoine du Hamel) [#39807](https://github.com/nodejs/node/pull/39807)
- [[`edcfffeaea`](https://github.com/nodejs/node/commit/edcfffeaea)] - **lib**: use standard property names (null) [#39981](https://github.com/nodejs/node/pull/39981)
- [[`640353af86`](https://github.com/nodejs/node/commit/640353af86)] - **lib,repl**: ignore non-canBeRequiredByUsers built-in (Khaidi Chu) [#39942](https://github.com/nodejs/node/pull/39942)
- [[`4444b5c938`](https://github.com/nodejs/node/commit/4444b5c938)] - **meta**: update AUTHORS (Node.js GitHub Bot) [#40148](https://github.com/nodejs/node/pull/40148)
- [[`4993318862`](https://github.com/nodejs/node/commit/4993318862)] - **meta**: update GeoffreyBooth email addresses in AUTHORS and .mailmap (Rich Trott) [#40132](https://github.com/nodejs/node/pull/40132)
- [[`98d42fa1f4`](https://github.com/nodejs/node/commit/98d42fa1f4)] - **meta**: add mailmap entry for LPardue (Rich Trott) [#40129](https://github.com/nodejs/node/pull/40129)
- [[`effdfa91be`](https://github.com/nodejs/node/commit/effdfa91be)] - **meta**: update GeoffreyBooth email address (Geoffrey Booth) [#40102](https://github.com/nodejs/node/pull/40102)
- [[`588257c00a`](https://github.com/nodejs/node/commit/588257c00a)] - **meta**: add .mailmap entry for arcanis (Rich Trott) [#40103](https://github.com/nodejs/node/pull/40103)
- [[`7ee3fbd1e0`](https://github.com/nodejs/node/commit/7ee3fbd1e0)] - **meta**: update AUTHORS (Node.js GitHub Bot) [#40087](https://github.com/nodejs/node/pull/40087)
- [[`2a41530a5e`](https://github.com/nodejs/node/commit/2a41530a5e)] - **meta**: consolidate AUTHORS entry for mikemaccana (Rich Trott) [#40051](https://github.com/nodejs/node/pull/40051)
- [[`a71579b05e`](https://github.com/nodejs/node/commit/a71579b05e)] - **meta**: add more mailmap entries for bajtos (Rich Trott) [#40023](https://github.com/nodejs/node/pull/40023)
- [[`29104f5e64`](https://github.com/nodejs/node/commit/29104f5e64)] - **meta**: consolidate AUTHORS entries for mithunsasidharan (Rich Trott) [#40003](https://github.com/nodejs/node/pull/40003)
- [[`381293f54a`](https://github.com/nodejs/node/commit/381293f54a)] - **meta**: update AUTHORS (Node.js GitHub Bot) [#39957](https://github.com/nodejs/node/pull/39957)
- [[`1eca9bc5b2`](https://github.com/nodejs/node/commit/1eca9bc5b2)] - **module**: support pattern trailers for imports field (Guy Bedford) [#40041](https://github.com/nodejs/node/pull/40041)
- [[`7376edca6d`](https://github.com/nodejs/node/commit/7376edca6d)] - **module**: deprecate trailing slash pattern mappings (Guy Bedford) [#40039](https://github.com/nodejs/node/pull/40039)
- [[`92f182b23d`](https://github.com/nodejs/node/commit/92f182b23d)] - **module**: fix $ pattern replacements (Guy Bedford) [#40044](https://github.com/nodejs/node/pull/40044)
- [[`d6124d8259`](https://github.com/nodejs/node/commit/d6124d8259)] - **repl**: fix top level await with surrogate characters (Mestery) [#39931](https://github.com/nodejs/node/pull/39931)
- [[`9a672961fa`](https://github.com/nodejs/node/commit/9a672961fa)] - **(SEMVER-MINOR)** **src**: add --no-global-search-paths cli option (Cheng Zhao) [#39754](https://github.com/nodejs/node/pull/39754)
- [[`51f9ad4897`](https://github.com/nodejs/node/commit/51f9ad4897)] - **(SEMVER-MINOR)** **src**: add option to disable global search paths (Cheng Zhao) [#39754](https://github.com/nodejs/node/pull/39754)
- [[`95528b284d`](https://github.com/nodejs/node/commit/95528b284d)] - **src**: remove unnecessary comment and add a CHECK in crypto_tls.cc (Darshan Sen) [#39991](https://github.com/nodejs/node/pull/39991)
- [[`31994fbf8e`](https://github.com/nodejs/node/commit/31994fbf8e)] - **src**: register zlib external references for snapshot (Joyee Cheung) [#40050](https://github.com/nodejs/node/pull/40050)
- [[`cfcd57182b`](https://github.com/nodejs/node/commit/cfcd57182b)] - **src**: fix -Wunreachable-code-return error (Shelley Vohr) [#40034](https://github.com/nodejs/node/pull/40034)
- [[`9f3a015b60`](https://github.com/nodejs/node/commit/9f3a015b60)] - **src**: add option to disable loading native addons (Dominic Elm) [#39977](https://github.com/nodejs/node/pull/39977)
- [[`570bef1710`](https://github.com/nodejs/node/commit/570bef1710)] - **_Revert_** "**src**: skip test_fatal/test_threads for Debug builds" (Anna Henningsen) [#39954](https://github.com/nodejs/node/pull/39954)
- [[`842f936e04`](https://github.com/nodejs/node/commit/842f936e04)] - **src**: use Isolate::TryGetCurrent where appropriate (Anna Henningsen) [#39954](https://github.com/nodejs/node/pull/39954)
- [[`fe920b6cbf`](https://github.com/nodejs/node/commit/fe920b6cbf)] - **(SEMVER-MINOR)** **src**: make napi_create_reference accept symbol (JckXia) [#39926](https://github.com/nodejs/node/pull/39926)
- [[`73aa4e34ff`](https://github.com/nodejs/node/commit/73aa4e34ff)] - **src**: fix C4805 MSVC warning (Michaël Zasso) [#39998](https://github.com/nodejs/node/pull/39998)
- [[`826eee363c`](https://github.com/nodejs/node/commit/826eee363c)] - **src**: register external references of PipeWrap for snapshot (Joyee Cheung) [#39961](https://github.com/nodejs/node/pull/39961)
- [[`7a17cbfdea`](https://github.com/nodejs/node/commit/7a17cbfdea)] - **src**: register external references of TTYWrap for snapshot (Joyee Cheung) [#39961](https://github.com/nodejs/node/pull/39961)
- [[`00cca48081`](https://github.com/nodejs/node/commit/00cca48081)] - **src**: register external references of TCPWrap for snapshot (Joyee Cheung) [#39961](https://github.com/nodejs/node/pull/39961)
- [[`6095fb07b6`](https://github.com/nodejs/node/commit/6095fb07b6)] - **src**: register external references of SignalWrap for snapshot (Joyee Cheung) [#39961](https://github.com/nodejs/node/pull/39961)
- [[`db75711c5c`](https://github.com/nodejs/node/commit/db75711c5c)] - **src**: register missing process methods external references (Joyee Cheung) [#39961](https://github.com/nodejs/node/pull/39961)
- [[`b4e074c295`](https://github.com/nodejs/node/commit/b4e074c295)] - **src**: register missing stream wrap external references (Joyee Cheung) [#39961](https://github.com/nodejs/node/pull/39961)
- [[`a2c1c3ef64`](https://github.com/nodejs/node/commit/a2c1c3ef64)] - **src**: register external references of BaseObject for snapshot (Joyee Cheung) [#39961](https://github.com/nodejs/node/pull/39961)
- [[`6fdf02523e`](https://github.com/nodejs/node/commit/6fdf02523e)] - **src**: register external references of node-report for snapshot (Joyee Cheung) [#39961](https://github.com/nodejs/node/pull/39961)
- [[`bef78a2f88`](https://github.com/nodejs/node/commit/bef78a2f88)] - **src**: register external references of dtrace for snapshot (Joyee Cheung) [#39961](https://github.com/nodejs/node/pull/39961)
- [[`97f3072ceb`](https://github.com/nodejs/node/commit/97f3072ceb)] - **(SEMVER-MINOR)** **stream**: add signal support to pipeline generators (Robert Nagy) [#39067](https://github.com/nodejs/node/pull/39067)
- [[`6be405bd7b`](https://github.com/nodejs/node/commit/6be405bd7b)] - **test**: fix test-dgram-udp6-link-local-address on Windows (Michaël Zasso) [#40005](https://github.com/nodejs/node/pull/40005)
- [[`ec94bec9a3`](https://github.com/nodejs/node/commit/ec94bec9a3)] - **test**: do not run `test-corepack-yarn-install` with no internet (Antoine du Hamel) [#40090](https://github.com/nodejs/node/pull/40090)
- [[`4aa2610252`](https://github.com/nodejs/node/commit/4aa2610252)] - **test**: update OpenSSL3 error messages for 3.0.0+quic (Daniel Bevenius) [#40093](https://github.com/nodejs/node/pull/40093)
- [[`4367a61a9b`](https://github.com/nodejs/node/commit/4367a61a9b)] - **test**: mark test-crypto-timing-safe-equal-benchmarks flaky (Richard Lau) [#40065](https://github.com/nodejs/node/pull/40065)
- [[`5b5e27281c`](https://github.com/nodejs/node/commit/5b5e27281c)] - **test**: fix internet/test-dns (Rich Trott) [#40083](https://github.com/nodejs/node/pull/40083)
- [[`67bbfeb7e1`](https://github.com/nodejs/node/commit/67bbfeb7e1)] - **test**: make tests pass on Windows with Unix EOL (Michaël Zasso) [#40002](https://github.com/nodejs/node/pull/40002)
- [[`a8c99d9f09`](https://github.com/nodejs/node/commit/a8c99d9f09)] - **tools**: update doc generator dependencies (Michaël Zasso) [#40042](https://github.com/nodejs/node/pull/40042)
- [[`ec6de1195a`](https://github.com/nodejs/node/commit/ec6de1195a)] - **tools**: update ansi-regex in lint-md rollup (Rich Trott) [#40112](https://github.com/nodejs/node/pull/40112)
- [[`d55804ca4e`](https://github.com/nodejs/node/commit/d55804ca4e)] - **tools**: update all dependencies of markdown linter (Michaël Zasso) [#40035](https://github.com/nodejs/node/pull/40035)
- [[`f03bae7c82`](https://github.com/nodejs/node/commit/f03bae7c82)] - **tools**: update remark-html to v13.0.2 (Michaël Zasso) [#40043](https://github.com/nodejs/node/pull/40043)
- [[`99af21292f`](https://github.com/nodejs/node/commit/99af21292f)] - **tools,build**: update YAML files in preparation for linting (Rich Trott) [#40007](https://github.com/nodejs/node/pull/40007)
- [[`590ace418d`](https://github.com/nodejs/node/commit/590ace418d)] - **tools,doc**: fix misrendering of consecutive JS blocks (Rich Trott) [#40146](https://github.com/nodejs/node/pull/40146)
- [[`5983568204`](https://github.com/nodejs/node/commit/5983568204)] - **worker**: avoid potential deadlock on NearHeapLimit (Santiago Gimeno) [#38403](https://github.com/nodejs/node/pull/38403)

Windows 32-bit Installer: https://nodejs.org/dist/v16.10.0/node-v16.10.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v16.10.0/node-v16.10.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v16.10.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v16.10.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v16.10.0/node-v16.10.0.pkg \
macOS Apple Silicon 64-bit Binary: https://nodejs.org/dist/v16.10.0/node-v16.10.0-darwin-arm64.tar.gz \
macOS Intel 64-bit Binary: https://nodejs.org/dist/v16.10.0/node-v16.10.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v16.10.0/node-v16.10.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v16.10.0/node-v16.10.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v16.10.0/node-v16.10.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v16.10.0/node-v16.10.0-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v16.10.0/node-v16.10.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v16.10.0/node-v16.10.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v16.10.0/node-v16.10.0.tar.gz \
Other release files: https://nodejs.org/dist/v16.10.0/ \
Documentation: https://nodejs.org/docs/v16.10.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

2142ee042206c5064b19fc034d8d03c7291d0177341a3b55f80d0a3b44ee32de  node-v16.10.0-aix-ppc64.tar.gz
dfdaf4149365e170929b99692520388e89f618e8d64ddd3ded7126bccf4583ed  node-v16.10.0-darwin-arm64.tar.gz
c6eaaf06ed1b18150794a68d7a211cf8e9dd794be4def36da6a5ebb136c702c8  node-v16.10.0-darwin-arm64.tar.xz
66a42483908aabd6d5fb19e9f3cebd6927dc84206b75b8801d9e010815083906  node-v16.10.0-darwin-x64.tar.gz
0635879c2ae0ddff5dcd6dcce9649c0119d005a16f376b8331979a0709fc97e4  node-v16.10.0-darwin-x64.tar.xz
efb4b0133ab578f99cb9abb990817cafafea15bae1c07716565cece513070621  node-v16.10.0-headers.tar.gz
8f90343307892774cd26d0cbc48743f04667a2f450852b5b01d4da4ac2295195  node-v16.10.0-headers.tar.xz
2675ca64c50badd609907b2149f7c021cce52248e08f984a4102d4f390794f57  node-v16.10.0-linux-arm64.tar.gz
a9b477ea5c376729d59b39ecbb9bc5597b792a00ec11afbdf1e502b9b2557fb2  node-v16.10.0-linux-arm64.tar.xz
2d2c63fc7f974419437194f854204a2728095dbe124d1a6f782f3ac6201d8e02  node-v16.10.0-linux-armv7l.tar.gz
b52d3be99a05a4975ce492f4e010274f66ff6449824accd57a87fd29ab5d054a  node-v16.10.0-linux-armv7l.tar.xz
3fa8d29fec4db4097968e1023e9c82aad866a09e96d3201efc213902d5b85059  node-v16.10.0-linux-ppc64le.tar.gz
dbaef9f49547a0ce396d7fadb4863978f2c38989801af3d2ebaa55270722ea6a  node-v16.10.0-linux-ppc64le.tar.xz
a7197fd1e3fad30ea9f40fccddce16cc39a155a48c7f41f0ea04024ec793683d  node-v16.10.0-linux-s390x.tar.gz
ca6bcb2098622880433b14323d4789ce7034eb94cd0a77134feed975ab5483ed  node-v16.10.0-linux-s390x.tar.xz
bca7f42ea3e61938cc28868614bb37908111b9ff190fe8022fa9954651b5665d  node-v16.10.0-linux-x64.tar.gz
00c4de617038fe7bd60efd9303b83abe5a5df830a9221687e20408404e307c4e  node-v16.10.0-linux-x64.tar.xz
6fa8b76a7c0e3e5c4b0ccb48ddbf0d37fb625ba74dec7c21df40ca80fea164d5  node-v16.10.0.pkg
69b09bb0c4cd9add7eb501b1625a405b15d6ce53596e98b863c8d066e0b6b202  node-v16.10.0.tar.gz
97dc1aca232b4911e0b9e5a23a03200ab8ef05157e03c732315b579481bf7912  node-v16.10.0.tar.xz
c8ed55a1e9285ec261c95457226cdcbbed12cff3cdb715f464de185d9c5f2f0b  node-v16.10.0-win-x64.7z
269ef35a91e7e8ce107ed09ca17e70d2feb448cf46ed6dbda86ac95cb90c5344  node-v16.10.0-win-x64.zip
d1fe8079361f9e524e471173f92de4b47a8c7ecd20fee557283fb524b3e31c9a  node-v16.10.0-win-x86.7z
4f00121f999d681a20a83f51683a5acd736d983d866bef86689c54865b34b861  node-v16.10.0-win-x86.zip
eaa59fd94899078f118130bf31448a4d307cce3f3687cff99fa71e1a89c0b1b6  node-v16.10.0-x64.msi
d4a800849ab7f0ae701a5c29a3c0f79e491efb0124bdc3e695cc5dc2faef2088  node-v16.10.0-x86.msi
3a1b4319a963655d7c72b32ea942f4e17db2d37f625039581eb8b1996c3fff85  win-x64/node.exe
774524a2b7c2806a8141a430ce9c6bffb9e8f78ce6bc095ec8fa26a333ff7cef  win-x64/node.lib
19d5c41f19e5358fb6ba08e45373a5cfb9248b894471c594d4ba0f7309d2de02  win-x64/node_pdb.7z
14f8057e9daefb6e034a6f6d1e259aa301c99067d95ca15c57b03ffdb5258bac  win-x64/node_pdb.zip
2f13f5ef2a4ce07eee5d938b2ad4e6bd629decfcb45d8d8d594b9f2a8f30805d  win-x86/node.exe
1827162a03517e9a4897bd0195e601216d4ffb3680b11db4599166d0e76b6e98  win-x86/node.lib
fb6b5b947acdacba035933befca57ec5e7c24bf7907579416f0244261a023f60  win-x86/node_pdb.7z
3f2f144955744ae2030fd870ed45df5f0adbd63f26489a21cd37257de52f272c  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQEzBAEBCAAdFiEETtd49TnjY0x3nIfG1wYoSKGrAFwFAmFLlhkACgkQ1wYoSKGr
AFwHywgAmdIfGcRBxpvBa+KqmWoQTP9tg2hhEA05vbE0u7hWY5fIdYkcXpYSIIb9
X6goqvtbQyzsJMOLuaHv9/Xl9LCKMb0a5+TRUwWT9OiZc1VCvGT5LKOBLNSjzOj0
W9/Gv3/xQ2rPksLhDX85E/1wgibNyIslaZ2VxHuXucthKFqOicjklOvNXFzL+9XK
W7X9h/o7KNiqa6Im93b9yoL8QSy3en9ykTdHRmuzgKUj2vSh/3iO8RA5L323JJwR
6AtfR6wgNn/xd010zCxME1/zi3v7MtgNqUym+HwvxMf351IZwR46YblQpEiSJJpc
FmwyNjgVvOeHDlbt5GbZXobcn33SgA==
=jd25
-----END PGP SIGNATURE-----

```
