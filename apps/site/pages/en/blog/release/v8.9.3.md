---
date: '2017-12-08T16:14:14.605Z'
category: release
title: Node v8.9.3 (LTS)
layout: blog-post
author: <PERSON><PERSON>
---

### Notable Changes

- **buffer**:
  - buffer allocated with an invalid content will now be zero filled (<PERSON>) [#17428](https://github.com/nodejs/node/pull/17428)
- **deps**:
  - openssl updated to 1.0.2n (<PERSON><PERSON><PERSON>) [#17526](https://github.com/nodejs/node/pull/17526)

### Commits

- [[`b05ef978d3`](https://github.com/nodejs/node/commit/b05ef978d3)] - **buffer**: zero-fill buffer allocated with invalid content (<PERSON>) [#17428](https://github.com/nodejs/node/pull/17428)
- [[`18652b6860`](https://github.com/nodejs/node/commit/18652b6860)] - **deps**: update openssl asm and asm_obsolete files (<PERSON><PERSON><PERSON>) [#17526](https://github.com/nodejs/node/pull/17526)
- [[`e6c308e237`](https://github.com/nodejs/node/commit/e6c308e237)] - **deps**: add -no_rand_screen to openssl s_client (Shigeki Ohtsu) [nodejs/io.js#1836](https://github.com/nodejs/io.js/pull/1836)
- [[`a85f94bd59`](https://github.com/nodejs/node/commit/a85f94bd59)] - **deps**: fix asm build error of openssl in x86_win32 (Shigeki Ohtsu) [iojs/io.js#1389](https://github.com/iojs/io.js/pull/1389)
- [[`b5552c854c`](https://github.com/nodejs/node/commit/b5552c854c)] - **deps**: fix openssl assembly error on ia32 win32 (Fedor Indutny) [iojs/io.js#1389](https://github.com/iojs/io.js/pull/1389)
- [[`afad1f23a2`](https://github.com/nodejs/node/commit/afad1f23a2)] - **deps**: copy all openssl header files to include dir (Shigeki Ohtsu) [#17526](https://github.com/nodejs/node/pull/17526)
- [[`9fdd3bddf5`](https://github.com/nodejs/node/commit/9fdd3bddf5)] - **deps**: upgrade openssl sources to 1.0.2n (Shigeki Ohtsu) [#17526](https://github.com/nodejs/node/pull/17526)
- [[`db09f245bf`](https://github.com/nodejs/node/commit/db09f245bf)] - **doc**: warn against filling buffer with invalid data (Anna Henningsen) [#17428](https://github.com/nodejs/node/pull/17428)
- [[`42f09ed461`](https://github.com/nodejs/node/commit/42f09ed461)] - **http2**: use correct connect event for TLS Socket (James M Snell) [#17328](https://github.com/nodejs/node/pull/17328)
- [[`aba3544b50`](https://github.com/nodejs/node/commit/aba3544b50)] - **http2**: use 'close' event instead of 'streamClosed' (James M Snell) [#17328](https://github.com/nodejs/node/pull/17328)
- [[`bd035d75bd`](https://github.com/nodejs/node/commit/bd035d75bd)] - **http2**: general cleanups in core.js (James M Snell) [#17209](https://github.com/nodejs/node/pull/17209)
- [[`a5e3ba2cb3`](https://github.com/nodejs/node/commit/a5e3ba2cb3)] - **http2**: major update to internals (James M Snell) [#17105](https://github.com/nodejs/node/pull/17105)
- [[`d7f37cebed`](https://github.com/nodejs/node/commit/d7f37cebed)] - **http2**: simplify subsequent rstStream calls (Anatoli Papirovski) [#16753](https://github.com/nodejs/node/pull/16753)
- [[`22ee960775`](https://github.com/nodejs/node/commit/22ee960775)] - **http2**: refactor multiple internals (James M Snell) [#16676](https://github.com/nodejs/node/pull/16676)
- [[`319beaf45b`](https://github.com/nodejs/node/commit/319beaf45b)] - **http2**: allocate on every chunk send (James M Snell) [#16669](https://github.com/nodejs/node/pull/16669)
- [[`7d68488524`](https://github.com/nodejs/node/commit/7d68488524)] - **openssl**: fix keypress requirement in apps on win32 (Shigeki Ohtsu) [iojs/io.js#1389](https://github.com/iojs/io.js/pull/1389)
- [[`8e8fac29de`](https://github.com/nodejs/node/commit/8e8fac29de)] - **src**: fix -Winconsistent-missing-override warning (Ben Noordhuis) [#16726](https://github.com/nodejs/node/pull/16726)
- [[`26b43c87ee`](https://github.com/nodejs/node/commit/26b43c87ee)] - **src**: add method to compute storage in WriteWrap (Anna Henningsen) [#16727](https://github.com/nodejs/node/pull/16727)
- [[`99d775ca07`](https://github.com/nodejs/node/commit/99d775ca07)] - **test**: fix flaky test-http2-create-client-connect (David Benjamin) [#16130](https://github.com/nodejs/node/pull/16130)

Windows 32-bit Installer: https://nodejs.org/dist/v8.9.3/node-v8.9.3-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v8.9.3/node-v8.9.3-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v8.9.3/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v8.9.3/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v8.9.3/node-v8.9.3.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v8.9.3/node-v8.9.3-darwin-x64.tar.gz \
Linux 32-bit Binary: https://nodejs.org/dist/v8.9.3/node-v8.9.3-linux-x86.tar.xz \
Linux 64-bit Binary: https://nodejs.org/dist/v8.9.3/node-v8.9.3-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v8.9.3/node-v8.9.3-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v8.9.3/node-v8.9.3-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v8.9.3/node-v8.9.3-aix-ppc64.tar.gz \
SmartOS 32-bit Binary: https://nodejs.org/dist/v8.9.3/node-v8.9.3-sunos-x86.tar.xz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v8.9.3/node-v8.9.3-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v8.9.3/node-v8.9.3-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v8.9.3/node-v8.9.3-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v8.9.3/node-v8.9.3-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v8.9.3/node-v8.9.3.tar.gz \
Other release files: https://nodejs.org/dist/v8.9.3/ \
Documentation: https://nodejs.org/docs/v8.9.3/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

92e662ae3e7fa84a304a4be2e547a38d4f4dc421201a44a5f833683278484198  node-v8.9.3-aix-ppc64.tar.gz
fa7962f25db420a374e9e60d8a410188bd690a2f0ce8d403aa9b09d9b7ae8c1f  node-v8.9.3-darwin-x64.tar.gz
ff2a425e6c87cd51be6935fe7e3cf3979f9dd53fc7307b66b74358822780ab05  node-v8.9.3-darwin-x64.tar.xz
f8a5add5cc635ac6981a52ac777d9e7ff03c072078f2ec1f33e099e9f57e3fca  node-v8.9.3-headers.tar.gz
c0aa9ccfc9445b912367e695285cab6d153904c75a67b9cf7ce1b5fc8620ba99  node-v8.9.3-headers.tar.xz
df32e87060f5426fc6c6b1af8e3e130ae08ee36f570ac3728442c7833e53d7c3  node-v8.9.3-linux-arm64.tar.gz
8860678ad0c24059380af254574e5a12371a2d5c92ca5e1ac7267314af7df04f  node-v8.9.3-linux-arm64.tar.xz
c1303857ab808dfd72de748e359ef767df6411404f57d05805ca9bfffd0ab3a8  node-v8.9.3-linux-armv6l.tar.gz
b3b588de37e4a6e227b540b3f85eac37fe7fd6e78ee92be9556eb17077f734ed  node-v8.9.3-linux-armv6l.tar.xz
4288690edfb0ec8f20f34cbcb24580fad227b9323589a3f437d8fc474b19677d  node-v8.9.3-linux-armv7l.tar.gz
4ca2b9a5b46c22353233365944c2df7f1566a298bf25f57be8c863bf1bd4e0f8  node-v8.9.3-linux-armv7l.tar.xz
0ddb29995a279c3ce630aee38dc5a5745e99283d440a9381e8ca209ccb860352  node-v8.9.3-linux-ppc64le.tar.gz
a5568b0098096db65c50c7968206c6cab4ea67f069e1a8627e18f33cec188095  node-v8.9.3-linux-ppc64le.tar.xz
020839f4dfd462ce7cc97e01c228e751511bc503387a094f580df1ef85cbf680  node-v8.9.3-linux-s390x.tar.gz
ca854e0f21bfbc86182134a1dc6197cf09ad4a5e0f640923d51674cc36aad9e2  node-v8.9.3-linux-s390x.tar.xz
df3f1480dddb27ba5ca72bcaae48cb1a4446f341648c87338979fff35eb9fb27  node-v8.9.3-linux-x64.tar.gz
86f3aa593315f0503d069e3f4805019583ab8d86c0244a83c795d1942e3f99b7  node-v8.9.3-linux-x64.tar.xz
2414b66425fb79d60f2ec6ba054de17b5253c1ee00fc25e491f8f02dbaae03a8  node-v8.9.3-linux-x86.tar.gz
04dbc27bfd4ef06a0b364f4c15ab28268f90bf1310d8035481106397c108f600  node-v8.9.3-linux-x86.tar.xz
05b95b18cd170cbf397b83f9cf098c7132a816354b45a9db8454347215df91fb  node-v8.9.3.pkg
2159b641ed66fa9a20fa0092575d14f7175898704b66d4406583da6b52db7cae  node-v8.9.3-sunos-x64.tar.gz
67bb2c6231fe621812caac11ee7c0b08be7260f8f00572a4a24f1d8f89f345aa  node-v8.9.3-sunos-x64.tar.xz
19fbec782aad6524f6417e44d2ddc176b1aaa16f5a9ccd6586b543f2428c8ae3  node-v8.9.3-sunos-x86.tar.gz
0a300d8875e7fcbefb082e3ff79b62a39b63ed348a632d074315a18f1108ad4e  node-v8.9.3-sunos-x86.tar.xz
9efec12ae408f4e92738d9e5ab3c3fe68a407b7830228b0548ce9eef54cf63f9  node-v8.9.3.tar.gz
748ddb3baa6b85e6a56e38aacd066586e7581952f84a92bc8152248a9be6b2da  node-v8.9.3.tar.xz
d67bea0d8e27e66b55bdc32de600a11611e73b3a2322401a487513e56304559c  node-v8.9.3-win-x64.7z
17dee0c06d088269123a27db3905a39a17a51cc0ea65435ae942c718f0f94403  node-v8.9.3-win-x64.zip
e23102d8555eace48b9976a490f3a5b702a74eff7184cdd99b7dc2e1b8d22fcb  node-v8.9.3-win-x86.7z
c85bfc5443b9e9265493b286029ad0edda5b2aeed2abd6d314703fd21d5fd82a  node-v8.9.3-win-x86.zip
68cdd5a021101a208d830da016eebb3a9de5ba75e76d8d8d6e9332038f10f56a  node-v8.9.3-x64.msi
5f8e0e01879f273751dd1d0da7de44b4f32e65f62b5d3c373f54abd8c6db3f5a  node-v8.9.3-x86.msi
c8231cd0fa8be30891fabf53092c2916d1cac024a7ae425b9082d3d13251d3b2  win-x64/node.exe
1cefa86f4992cd65af19c86113fe9fcd034f67ddcb85dc17fe8a10720b76480e  win-x64/node.lib
a4bf203e3f1516020a913b560a468ad77796f8d2c2ae3e9f1884ca4312ee0d39  win-x64/node_pdb.7z
af14e1458134a9d4b37e66b0fea635b827e603694368f1feed4ff35a1f010867  win-x64/node_pdb.zip
2cd11403c9976e45be4f7f4785b96b0cb8126d7dd87dee8b1f1fe9fa680126f1  win-x86/node.exe
f2f21617e46cbb2e899d012774fdeed5cba4057b6829f231f429b28f07f1a86a  win-x86/node.lib
f9a60e58680f720e4ca63833aa500586d4cc6366763a727b80f3f3153eeb1e7f  win-x86/node_pdb.7z
834f09cc6c78ebf8b0d210aa55a4fdbecaab0f30a12f8b467a2de04b5db0d78b  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQEzBAEBCAAdFiEEDv/hvO/ZyE49CYFSkzsB9AtcqUYFAlorI/YACgkQkzsB9Atc
qUbatAgAnFGQj8EF/sT/Ffk2Q2atHoDMT/ip9e3a8YRrMXX98n9ECgYsnCU+jPpU
vOohwp7Seoj/dCSoCot6kE/OM8qfpzdIpTdQzYAOrn5cKIxIaI3q9im9GpCc4MHF
0vpMIWD6BjJU8fshGqqaQv7qhRu0VNNaRanzmh45r0P9rBooe8gszrmUW8g22Jul
k6FaOONCMjyqrmjgl9bIqdR4h8ic+zX0LyU44rRz0YaMHheNyD/zo67cqPJAgdXF
7fdefc1j7Bv+lGjzWcm2AHGm6Y5p33VLaxtQ1KVwyAUyoxLfB6jdRjwTh6X47rxa
k74izQxS7uOKBUHSy7E0FjNzLRYIGw==
=jUNr
-----END PGP SIGNATURE-----

```
