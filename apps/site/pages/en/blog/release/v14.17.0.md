---
date: '2021-05-11T23:29:38.815Z'
category: release
title: Node v14.17.0 (LTS)
layout: blog-post
author: <PERSON>
---

### Notable Changes

#### Diagnostics channel (experimental module)

`diagnostics_channel` is a new experimental module that provides an API to create named channels to report arbitrary message data for diagnostics purposes.

The module was initially introduced in Node.js v15.1.0 and is backported to v14.17.0
to enable testing it at a larger scale.

With `diagnostics_channel`, Node.js core and module authors can publish contextual data about what they are doing at a given time. This could be the hostname and query string of a mysql query, for example. Just create a named channel with `dc.channel(name)` and call `channel.publish(data)` to send the data to any listeners to that channel.

```js
const dc = require('diagnostics_channel');
const channel = dc.channel('mysql.query');

MySQL.prototype.query = function query(queryString, values, callback) {
  // Broadcast query information whenever a query is made
  channel.publish({
    query: queryString,
    host: this.hostname,
  });

  this.doQuery(queryString, values, callback);
};
```

Channels are like one big global event emitter but are split into separate objects to ensure they get the best performance. If nothing is listening to the channel, the publishing overhead should be as close to zero as possible. Consuming channel data is as easy as using `channel.subscribe(listener)` to run a function whenever a message is published to that channel.

```js
const dc = require('diagnostics_channel');
const channel = dc.channel('mysql.query');

channel.subscribe(({ query, host }) => {
  console.log(`mysql query to ${host}: ${query}`);
});
```

The data captured can be used to provide context for what an app is doing at a given time. This can be used for things like augmenting tracing data, tracking network and filesystem activity, logging queries, and many other things. It's also a very useful data source for diagnostics tools to provide a clearer picture of exactly what the application is doing at a given point in the data they are presenting.

Contributed by Stephen Belanger [#34895](https://github.com/nodejs/node/pull/34895).

#### UUID support in the crypto module

The new `crypto.randomUUID()` method now allows to generate random
[RFC 4122](https://www.rfc-editor.org/rfc/rfc4122.txt) Version 4 UUID strings:

```js
const { randomUUID } = require('crypto');

console.log(randomUUID());
// 'aa7c91a1-f8fc-4339-b9db-f93fc7233429'
```

Contributed by James M Snell [#36729](https://github.com/nodejs/node/pull/36729).

#### Experimental support for `AbortController` and `AbortSignal`

Node.js 14.17.0 adds experimental partial support for `AbortController` and `AbortSignal`.

Both constructors can be enabled globally using the `--experimental-abortcontroller` flag.

Additionally, several Node.js APIs have been updated to support `AbortSignal` for cancellation.
It is not mandatory to use the built-in constructors with them. Any spec-compliant third-party alternatives
should be compatible.

`AbortSignal` support was added to the following methods:

- `child_process.exec`
- `child_process.execFile`
- `child_process.fork`
- `child_process.spawn`
- `dgram.createSocket`
- `events.on`
- `events.once`
- `fs.readFile`
- `fs.watch`
- `fs.writeFile`
- `http.request`
- `https.request`
- `http2Session.request`
- The promisified variants of `setImmediate` and `setTimeout`

#### Other notable changes

- **doc**:
  - revoke deprecation of legacy url, change status to legacy (James M Snell) [#37784](https://github.com/nodejs/node/pull/37784)
  - add legacy status to stability index (James M Snell) [#37784](https://github.com/nodejs/node/pull/37784)
  - upgrade stability status of report API (Gireesh Punathil) [#35654](https://github.com/nodejs/node/pull/35654)
- **deps**:
  - V8: Backport various patches for Apple Silicon support (BoHong Li) [#38051](https://github.com/nodejs/node/pull/38051)
  - update ICU to 68.1 (Michaël Zasso) [#36187](https://github.com/nodejs/node/pull/36187)
  - upgrade to libuv 1.41.0 (Colin Ihrig) [#37360](https://github.com/nodejs/node/pull/37360)
- **http**:
  - add http.ClientRequest.getRawHeaderNames() (simov) [#37660](https://github.com/nodejs/node/pull/37660)
  - report request start and end with diagnostics_channel (Stephen Belanger) [#34895](https://github.com/nodejs/node/pull/34895)
- **util**:
  - add getSystemErrorMap() impl (eladkeyshawn) [#38101](https://github.com/nodejs/node/pull/38101)

### Commits

- [[`9fb10dc4e7`](https://github.com/nodejs/node/commit/9fb10dc4e7)] - **assert,util**: fix commutativity edge case (Ruben Bridgewater) [#37711](https://github.com/nodejs/node/pull/37711)
- [[`2bbf253b00`](https://github.com/nodejs/node/commit/2bbf253b00)] - **benchmark**: changed `fstat` to `fstatSync` (Narasimha Prasanna HN) [#36206](https://github.com/nodejs/node/pull/36206)
- [[`c00c31c3c5`](https://github.com/nodejs/node/commit/c00c31c3c5)] - **benchmark**: improve compare.R output (Brian White) [#38118](https://github.com/nodejs/node/pull/38118)
- [[`a191bc7761`](https://github.com/nodejs/node/commit/a191bc7761)] - **benchmark**: add benchmark for fsPromises.writeFile (Nitzan Uziely) [#37610](https://github.com/nodejs/node/pull/37610)
- [[`d2770a5608`](https://github.com/nodejs/node/commit/d2770a5608)] - **benchmark**: add benchmark for NODE_V8_COVERAGE (Benjamin Coe) [#36972](https://github.com/nodejs/node/pull/36972)
- [[`4318e708b8`](https://github.com/nodejs/node/commit/4318e708b8)] - **benchmark**: make output RFC 4180 compliant (Tobias Nießen) [#37038](https://github.com/nodejs/node/pull/37038)
- [[`0fbeab7a95`](https://github.com/nodejs/node/commit/0fbeab7a95)] - **benchmark**: improve explanations in R script (Tobias Nießen) [#36995](https://github.com/nodejs/node/pull/36995)
- [[`c22efc5191`](https://github.com/nodejs/node/commit/c22efc5191)] - **benchmark**: fix http2 benchmarks (Rich Trott) [#36871](https://github.com/nodejs/node/pull/36871)
- [[`682d0a92db`](https://github.com/nodejs/node/commit/682d0a92db)] - **benchmark**: fix http/headers.js with test-double (Rich Trott) [#36794](https://github.com/nodejs/node/pull/36794)
- [[`3a11ee88a2`](https://github.com/nodejs/node/commit/3a11ee88a2)] - **benchmark**: add simple https benchmark (Andrey Pechkurov) [#36612](https://github.com/nodejs/node/pull/36612)
- [[`681c4afc51`](https://github.com/nodejs/node/commit/681c4afc51)] - **benchmark**: reduce code duplication (Rich Trott) [#36568](https://github.com/nodejs/node/pull/36568)
- [[`f28eea0896`](https://github.com/nodejs/node/commit/f28eea0896)] - **benchmark,child_process**: remove failing benchmark parameter (Antoine du Hamel) [#36295](https://github.com/nodejs/node/pull/36295)
- [[`bf2d9f25d4`](https://github.com/nodejs/node/commit/bf2d9f25d4)] - **(SEMVER-MINOR)** **buffer**: implement btoa and atob (James M Snell) [#37529](https://github.com/nodejs/node/pull/37529)
- [[`0544410328`](https://github.com/nodejs/node/commit/0544410328)] - **buffer,errors**: add missing n literal in range error string (Cactysman) [#37750](https://github.com/nodejs/node/pull/37750)
- [[`5667d0a540`](https://github.com/nodejs/node/commit/5667d0a540)] - **build**: don't run test workflow on doc dir on macOS (ycjcl868) [#37999](https://github.com/nodejs/node/pull/37999)
- [[`079d90b9f3`](https://github.com/nodejs/node/commit/079d90b9f3)] - **build**: package release changelog for releases (Richard Lau) [#38033](https://github.com/nodejs/node/pull/38033)
- [[`5c74dc7227`](https://github.com/nodejs/node/commit/5c74dc7227)] - **build**: refactor Makefile (raisinten) [#36759](https://github.com/nodejs/node/pull/36759)
- [[`38921b3805`](https://github.com/nodejs/node/commit/38921b3805)] - **build**: do not "exit" a script meant to be "source"d (François-Denis Gonthier) [#35520](https://github.com/nodejs/node/pull/35520)
- [[`dcbcd9e045`](https://github.com/nodejs/node/commit/dcbcd9e045)] - **build**: run some workflows only on nodejs/node (Michaël Zasso) [#36507](https://github.com/nodejs/node/pull/36507)
- [[`cda0a80713`](https://github.com/nodejs/node/commit/cda0a80713)] - **build**: fix typo in Makefile (raisinten) [#36176](https://github.com/nodejs/node/pull/36176)
- [[`d8f8719415`](https://github.com/nodejs/node/commit/d8f8719415)] - **build**: do not pass mode option to test-v8 command (Michaël Zasso) [#38275](https://github.com/nodejs/node/pull/38275)
- [[`f62b138278`](https://github.com/nodejs/node/commit/f62b138278)] - **build**: fix label-pr workflow (Michaël Zasso) [#38399](https://github.com/nodejs/node/pull/38399)
- [[`1250db9206`](https://github.com/nodejs/node/commit/1250db9206)] - **build**: label PRs with GitHub Action instead of nodejs-github-bot (Phillip Johnsen) [#38301](https://github.com/nodejs/node/pull/38301)
- [[`9ccf7dbe2d`](https://github.com/nodejs/node/commit/9ccf7dbe2d)] - **build,lib,test**: change whitelist to allowlist (Michaël Zasso) [#36406](https://github.com/nodejs/node/pull/36406)
- [[`385e8e8d7b`](https://github.com/nodejs/node/commit/385e8e8d7b)] - **(SEMVER-MINOR)** **child_process**: support AbortSignal in fork (Benjamin Gruenbaum) [#36603](https://github.com/nodejs/node/pull/36603)
- [[`0b691ce57e`](https://github.com/nodejs/node/commit/0b691ce57e)] - **(SEMVER-MINOR)** **child_process**: add signal support to spawn (Benjamin Gruenbaum) [#36432](https://github.com/nodejs/node/pull/36432)
- [[`6c08c9de4a`](https://github.com/nodejs/node/commit/6c08c9de4a)] - **child_process**: clean event listener correctly (Benjamin Gruenbaum) [#36424](https://github.com/nodejs/node/pull/36424)
- [[`a5c0f39197`](https://github.com/nodejs/node/commit/a5c0f39197)] - **(SEMVER-MINOR)** **child_process**: add AbortSignal support (Benjamin Gruenbaum) [#36308](https://github.com/nodejs/node/pull/36308)
- [[`aa5b726f83`](https://github.com/nodejs/node/commit/aa5b726f83)] - **(SEMVER-MINOR)** **child_process**: add ChildProcess 'spawn' event (Matthew Francis Brunetti) [#35369](https://github.com/nodejs/node/pull/35369)
- [[`723977feaa`](https://github.com/nodejs/node/commit/723977feaa)] - **crypto**: reduce range of size to int max (Qingyu Deng) [#38096](https://github.com/nodejs/node/pull/38096)
- [[`46ece20fe3`](https://github.com/nodejs/node/commit/46ece20fe3)] - **crypto**: fix DiffieHellman argument validation (Antoine du Hamel) [#37810](https://github.com/nodejs/node/pull/37810)
- [[`00659a9218`](https://github.com/nodejs/node/commit/00659a9218)] - **crypto**: fix randomInt bias (Tobias Nießen) [#36894](https://github.com/nodejs/node/pull/36894)
- [[`08f9130888`](https://github.com/nodejs/node/commit/08f9130888)] - **(SEMVER-MINOR)** **crypto**: implement randomuuid (James M Snell) [#36729](https://github.com/nodejs/node/pull/36729)
- [[`8951c19e72`](https://github.com/nodejs/node/commit/8951c19e72)] - **deps**: V8: cherry-pick 501482cbc704 (Colin Ihrig) [#38121](https://github.com/nodejs/node/pull/38121)
- [[`ea0b0697c3`](https://github.com/nodejs/node/commit/ea0b0697c3)] - **deps**: update nghttp2 to 1.42.0 (Michaël Zasso) [#36842](https://github.com/nodejs/node/pull/36842)
- [[`5747dff04e`](https://github.com/nodejs/node/commit/5747dff04e)] - **deps**: update to c-ares 1.17.1 (Danny Sonnenschein) [#36207](https://github.com/nodejs/node/pull/36207)
- [[`329ee8bbc3`](https://github.com/nodejs/node/commit/329ee8bbc3)] - **deps**: V8: cherry-pick bbc59d124ef3 (Michaël Zasso) [#38275](https://github.com/nodejs/node/pull/38275)
- [[`bda15149f8`](https://github.com/nodejs/node/commit/bda15149f8)] - **deps**: V8: cherry-pick be91c6c50818 (Michaël Zasso) [#38275](https://github.com/nodejs/node/pull/38275)
- [[`16a005cfa0`](https://github.com/nodejs/node/commit/16a005cfa0)] - **deps**: V8: cherry-pick 4e24c353d812 (Michaël Zasso) [#38275](https://github.com/nodejs/node/pull/38275)
- [[`42140a12f2`](https://github.com/nodejs/node/commit/42140a12f2)] - **deps**: V8: cherry-pick eddb82330975 (Michaël Zasso) [#38275](https://github.com/nodejs/node/pull/38275)
- [[`4d0bc3839a`](https://github.com/nodejs/node/commit/4d0bc3839a)] - **deps**: V8: cherry-pick 6771d3e31883 (Michaël Zasso) [#38275](https://github.com/nodejs/node/pull/38275)
- [[`982937893e`](https://github.com/nodejs/node/commit/982937893e)] - **deps**: V8: cherry-pick f44fcbf803ac (Michaël Zasso) [#38275](https://github.com/nodejs/node/pull/38275)
- [[`fa45d6a358`](https://github.com/nodejs/node/commit/fa45d6a358)] - **deps**: V8: cherry-pick 93b2105fbe44 (Michaël Zasso) [#38275](https://github.com/nodejs/node/pull/38275)
- [[`c5fe3a226a`](https://github.com/nodejs/node/commit/c5fe3a226a)] - **deps**: V8: cherry-pick 1a7d55a9a427 (Michaël Zasso) [#38275](https://github.com/nodejs/node/pull/38275)
- [[`7dd68ac5b6`](https://github.com/nodejs/node/commit/7dd68ac5b6)] - **deps**: V8: cherry-pick 8ebd894186ed (Michaël Zasso) [#38275](https://github.com/nodejs/node/pull/38275)
- [[`a4a9246ea1`](https://github.com/nodejs/node/commit/a4a9246ea1)] - **deps**: V8: cherry-pick 1e35f6472510 (Michaël Zasso) [#38275](https://github.com/nodejs/node/pull/38275)
- [[`9bfb0f33e9`](https://github.com/nodejs/node/commit/9bfb0f33e9)] - **deps**: V8: cherry-pick 3066b7b2fcb3 (Michaël Zasso) [#38275](https://github.com/nodejs/node/pull/38275)
- [[`5dc82469d5`](https://github.com/nodejs/node/commit/5dc82469d5)] - **deps**: V8: cherry-pick 254c7945eea2 (Michaël Zasso) [#38275](https://github.com/nodejs/node/pull/38275)
- [[`77b7a3b710`](https://github.com/nodejs/node/commit/77b7a3b710)] - **deps**: V8: cherry-pick 5678ebe8f6c4 (Michaël Zasso) [#38275](https://github.com/nodejs/node/pull/38275)
- [[`0bd8e14501`](https://github.com/nodejs/node/commit/0bd8e14501)] - **deps**: V8: cherry-pick 813066946968 (Michaël Zasso) [#38275](https://github.com/nodejs/node/pull/38275)
- [[`d221cdc97c`](https://github.com/nodejs/node/commit/d221cdc97c)] - **deps**: V8: cherry-pick d2283ba066ba (Michaël Zasso) [#38275](https://github.com/nodejs/node/pull/38275)
- [[`26cc160565`](https://github.com/nodejs/node/commit/26cc160565)] - **deps**: V8: cherry-pick 53c4d057974a (Michaël Zasso) [#38275](https://github.com/nodejs/node/pull/38275)
- [[`05530e8333`](https://github.com/nodejs/node/commit/05530e8333)] - **deps**: V8: cherry-pick e527ba4bf8af (Michaël Zasso) [#38275](https://github.com/nodejs/node/pull/38275)
- [[`fdb4a0c170`](https://github.com/nodejs/node/commit/fdb4a0c170)] - **deps**: V8: cherry-pick 5c6c99a8dc72 (Michaël Zasso) [#38275](https://github.com/nodejs/node/pull/38275)
- [[`42552a7eda`](https://github.com/nodejs/node/commit/42552a7eda)] - **deps**: V8: cherry-pick ad2c5dae4688 (Michaël Zasso) [#38275](https://github.com/nodejs/node/pull/38275)
- [[`aff53dd8b3`](https://github.com/nodejs/node/commit/aff53dd8b3)] - **deps**: V8: cherry-pick 482e5c7750b3 (Michaël Zasso) [#38275](https://github.com/nodejs/node/pull/38275)
- [[`931d31a2cb`](https://github.com/nodejs/node/commit/931d31a2cb)] - **deps**: V8: cherry-pick 412ac52d8246 (Michaël Zasso) [#38275](https://github.com/nodejs/node/pull/38275)
- [[`e99e456757`](https://github.com/nodejs/node/commit/e99e456757)] - **deps**: V8: cherry-pick c449afa1953b (Michaël Zasso) [#38275](https://github.com/nodejs/node/pull/38275)
- [[`18a4cbfb52`](https://github.com/nodejs/node/commit/18a4cbfb52)] - **deps**: V8: cherry-pick 3ba21a17ce2f (Michaël Zasso) [#38275](https://github.com/nodejs/node/pull/38275)
- [[`70f622b542`](https://github.com/nodejs/node/commit/70f622b542)] - **deps**: V8: cherry-pick 8c725f7b5bbf (Michaël Zasso) [#38275](https://github.com/nodejs/node/pull/38275)
- [[`0e6976f5ee`](https://github.com/nodejs/node/commit/0e6976f5ee)] - **deps**: V8: cherry-pick ed3eedae33d0 (Michaël Zasso) [#38275](https://github.com/nodejs/node/pull/38275)
- [[`86c7c0ae4e`](https://github.com/nodejs/node/commit/86c7c0ae4e)] - **deps**: V8: cherry-pick 6a4cd97d6691 (Michaël Zasso) [#38275](https://github.com/nodejs/node/pull/38275)
- [[`b10cce1b87`](https://github.com/nodejs/node/commit/b10cce1b87)] - **deps**: V8: cherry-pick d724820c1d5d (Michaël Zasso) [#38275](https://github.com/nodejs/node/pull/38275)
- [[`aaeeb75a99`](https://github.com/nodejs/node/commit/aaeeb75a99)] - **deps**: V8: cherry-pick 33f4064dbad3 (Michaël Zasso) [#38275](https://github.com/nodejs/node/pull/38275)
- [[`b0d1a060e2`](https://github.com/nodejs/node/commit/b0d1a060e2)] - **deps**: V8: cherry-pick abb4d0a431c0 (Michaël Zasso) [#38275](https://github.com/nodejs/node/pull/38275)
- [[`5372f1ff5b`](https://github.com/nodejs/node/commit/5372f1ff5b)] - **deps**: V8: cherry-pick a59e3ac1d7fa (Michaël Zasso) [#38275](https://github.com/nodejs/node/pull/38275)
- [[`31154a5611`](https://github.com/nodejs/node/commit/31154a5611)] - **deps**: V8: cherry-pick 516b5d3f9cfe (Michaël Zasso) [#38275](https://github.com/nodejs/node/pull/38275)
- [[`fc8f1b7f0a`](https://github.com/nodejs/node/commit/fc8f1b7f0a)] - **deps**: upgrade npm to 6.14.13 (Ruy Adorno) [#38214](https://github.com/nodejs/node/pull/38214)
- [[`0c1e878c4c`](https://github.com/nodejs/node/commit/0c1e878c4c)] - **deps**: backport v8 f19142e6 (Guy Bedford) [#37864](https://github.com/nodejs/node/pull/37864)
- [[`dd5da301c8`](https://github.com/nodejs/node/commit/dd5da301c8)] - **deps**: backport v8 5f90cfd7 (Guy Bedford) [#37973](https://github.com/nodejs/node/pull/37973)
- [[`d56079ab9b`](https://github.com/nodejs/node/commit/d56079ab9b)] - **deps**: update to cjs-module-lexer@1.1.1 (Guy Bedford) [#38002](https://github.com/nodejs/node/pull/38002)
- [[`866e3244da`](https://github.com/nodejs/node/commit/866e3244da)] - **deps**: V8: Backport various patches for Apple Silicon support (BoHong Li) [#38051](https://github.com/nodejs/node/pull/38051)
- [[`16b59c62ff`](https://github.com/nodejs/node/commit/16b59c62ff)] - **deps**: cherry-pick 8957d4677aa794c230577f234071af0 from V8 upstream (Antoine du Hamel) [#37471](https://github.com/nodejs/node/pull/37471)
- [[`5707adaf33`](https://github.com/nodejs/node/commit/5707adaf33)] - **deps**: V8: cherry-pick 0c8b6e415c30 (Matin Zadehdolatabad) [#37276](https://github.com/nodejs/node/pull/37276)
- [[`7d247f1691`](https://github.com/nodejs/node/commit/7d247f1691)] - **deps**: V8: cherry-pick 1d0f426311d4 (Ole André Vadla Ravnås) [#35986](https://github.com/nodejs/node/pull/35986)
- [[`14a87a5a01`](https://github.com/nodejs/node/commit/14a87a5a01)] - **deps**: V8: cherry-pick 4e077ff0444a (Ole André Vadla Ravnås) [#35986](https://github.com/nodejs/node/pull/35986)
- [[`507c2f2101`](https://github.com/nodejs/node/commit/507c2f2101)] - **deps**: V8: cherry-pick 086eecbd96b6 (Ole André Vadla Ravnås) [#35986](https://github.com/nodejs/node/pull/35986)
- [[`31f8610a02`](https://github.com/nodejs/node/commit/31f8610a02)] - **deps**: V8: cherry-pick 27e1ac1a79ff (Ole André Vadla Ravnås) [#35986](https://github.com/nodejs/node/pull/35986)
- [[`6b115d762d`](https://github.com/nodejs/node/commit/6b115d762d)] - **deps**: patch V8 to 8.4.371.23 (Michaël Zasso) [#38001](https://github.com/nodejs/node/pull/38001)
- [[`a92ecf0081`](https://github.com/nodejs/node/commit/a92ecf0081)] - **deps**: v8 backport 9689b17687b (Guy Bedford) [#37865](https://github.com/nodejs/node/pull/37865)
- [[`3e8ceed0eb`](https://github.com/nodejs/node/commit/3e8ceed0eb)] - **deps**: update ICU to 68.2 (Michaël Zasso) [#36980](https://github.com/nodejs/node/pull/36980)
- [[`2d7e0b6912`](https://github.com/nodejs/node/commit/2d7e0b6912)] - **deps**: update ICU to 68.1 (Michaël Zasso) [#36187](https://github.com/nodejs/node/pull/36187)
- [[`bfba66dbd6`](https://github.com/nodejs/node/commit/bfba66dbd6)] - **deps**: upgrade to libuv 1.41.0 (Colin Ihrig) [#37360](https://github.com/nodejs/node/pull/37360)
- [[`e446d82394`](https://github.com/nodejs/node/commit/e446d82394)] - **deps**: V8: cherry-pick beebee4f80ff (Peter Marshall) [#37293](https://github.com/nodejs/node/pull/37293)
- [[`ae1fa98496`](https://github.com/nodejs/node/commit/ae1fa98496)] - **deps**: cherry-pick f4376ec801e1ded from V8 upstream (Daniel Bevenius) [#37225](https://github.com/nodejs/node/pull/37225)
- [[`81cd06b3c6`](https://github.com/nodejs/node/commit/81cd06b3c6)] - **(SEMVER-MINOR)** **dgram**: support AbortSignal in createSocket (Nitzan Uziely) [#37026](https://github.com/nodejs/node/pull/37026)
- [[`46651b63c1`](https://github.com/nodejs/node/commit/46651b63c1)] - **dns**: refactor cares_wrap internals (James M Snell) [#38172](https://github.com/nodejs/node/pull/38172)
- [[`8715462f47`](https://github.com/nodejs/node/commit/8715462f47)] - **(SEMVER-MINOR)** **dns**: add a cancel() method to the promise Resolver (Szymon Marczak) [#33099](https://github.com/nodejs/node/pull/33099)
- [[`0f126d0e05`](https://github.com/nodejs/node/commit/0f126d0e05)] - **dns**: fix trace_events name for resolveCaa() (Rich Trott) [#35979](https://github.com/nodejs/node/pull/35979)
- [[`ed79c98683`](https://github.com/nodejs/node/commit/ed79c98683)] - **(SEMVER-MINOR)** **dns**: add setLocalAddress to Resolver (Josh Dague) [#34824](https://github.com/nodejs/node/pull/34824)
- [[`2e7f74c8a5`](https://github.com/nodejs/node/commit/2e7f74c8a5)] - **doc**: harmonize changes list ordering (Antoine du Hamel) [#35454](https://github.com/nodejs/node/pull/35454)
- [[`885ed96540`](https://github.com/nodejs/node/commit/885ed96540)] - **doc**: fix typo in repl.md (Arkerone) [#38244](https://github.com/nodejs/node/pull/38244)
- [[`92650278eb`](https://github.com/nodejs/node/commit/92650278eb)] - **doc**: change "oject" to "object" (Arkerone) [#38256](https://github.com/nodejs/node/pull/38256)
- [[`5dfe5af155`](https://github.com/nodejs/node/commit/5dfe5af155)] - **doc**: revise TLS minVersion/maxVersion text (Rich Trott) [#38202](https://github.com/nodejs/node/pull/38202)
- [[`e6c599b680`](https://github.com/nodejs/node/commit/e6c599b680)] - **doc**: standardize command flag notes (Ferdi) [#38199](https://github.com/nodejs/node/pull/38199)
- [[`bb8db846b3`](https://github.com/nodejs/node/commit/bb8db846b3)] - **doc**: clarify child_process close event (Nitzan Uziely) [#38181](https://github.com/nodejs/node/pull/38181)
- [[`be28376140`](https://github.com/nodejs/node/commit/be28376140)] - **doc**: add command flag to import.meta.resolve (Ferdi) [#38171](https://github.com/nodejs/node/pull/38171)
- [[`c7c8722ba3`](https://github.com/nodejs/node/commit/c7c8722ba3)] - **doc**: update links in ICU guide (Michaël Zasso) [#38177](https://github.com/nodejs/node/pull/38177)
- [[`4350bf5a0b`](https://github.com/nodejs/node/commit/4350bf5a0b)] - **doc**: mention cryptographic prng in description of randomUUID (Serkan Özel) [#38074](https://github.com/nodejs/node/pull/38074)
- [[`424c8e1eb9`](https://github.com/nodejs/node/commit/424c8e1eb9)] - **doc**: add link to V8 (Voltrex) [#38144](https://github.com/nodejs/node/pull/38144)
- [[`ecc85516cf`](https://github.com/nodejs/node/commit/ecc85516cf)] - **doc**: improve security text in collaborators guide (Rich Trott) [#38107](https://github.com/nodejs/node/pull/38107)
- [[`6c970ba2d4`](https://github.com/nodejs/node/commit/6c970ba2d4)] - **doc**: apply consistent punctuation to header contributing guide (Akhil Marsonya) [#38047](https://github.com/nodejs/node/pull/38047)
- [[`aff0cd3ea6`](https://github.com/nodejs/node/commit/aff0cd3ea6)] - **doc**: sending http request to localhost to avoid https redirect (Hassaan Pasha) [#38036](https://github.com/nodejs/node/pull/38036)
- [[`56aaf7010c`](https://github.com/nodejs/node/commit/56aaf7010c)] - **doc**: apply sentence case to backporting-to-release-lines.md headers (marsonya) [#37617](https://github.com/nodejs/node/pull/37617)
- [[`8615fa1983`](https://github.com/nodejs/node/commit/8615fa1983)] - **doc**: add parentheses to function and move reference (Rich Trott) [#38066](https://github.com/nodejs/node/pull/38066)
- [[`5d2f0d0c4e`](https://github.com/nodejs/node/commit/5d2f0d0c4e)] - **doc**: change wording in doc/api/domain.md comment (Akhil Marsonya) [#38044](https://github.com/nodejs/node/pull/38044)
- [[`ac59022106`](https://github.com/nodejs/node/commit/ac59022106)] - **doc**: fix asyncLocalStorage.run() description (Darkripper214) [#38023](https://github.com/nodejs/node/pull/38023)
- [[`df54edc668`](https://github.com/nodejs/node/commit/df54edc668)] - **doc**: document how to unref stdin when using readline.Interface (Anu Pasumarthy) [#38019](https://github.com/nodejs/node/pull/38019)
- [[`21bc5d4bd4`](https://github.com/nodejs/node/commit/21bc5d4bd4)] - **doc**: move psmarshall to collaborators emeriti (Peter Marshall) [#37994](https://github.com/nodejs/node/pull/37994)
- [[`69c4bfd750`](https://github.com/nodejs/node/commit/69c4bfd750)] - **doc**: add distinctive color for code elements inside links (Antoine du Hamel) [#37950](https://github.com/nodejs/node/pull/37950)
- [[`35a382e814`](https://github.com/nodejs/node/commit/35a382e814)] - **doc**: add Windows-specific info to subprocess.kill() (João Lucas Lucchetta) [#34867](https://github.com/nodejs/node/pull/34867)
- [[`2a5f21f9cd`](https://github.com/nodejs/node/commit/2a5f21f9cd)] - **doc**: fix typos in lib/internal/bootstrap/pre_execution.js (marsonya) [#37658](https://github.com/nodejs/node/pull/37658)
- [[`9f1f2153e9`](https://github.com/nodejs/node/commit/9f1f2153e9)] - **doc**: add more commands for cherry-picking and changelog to release docs (Danielle Adams) [#37785](https://github.com/nodejs/node/pull/37785)
- [[`dd1c47bbf3`](https://github.com/nodejs/node/commit/dd1c47bbf3)] - **doc**: spell out ICU acronym on first occurrence (Rich Trott) [#37942](https://github.com/nodejs/node/pull/37942)
- [[`585f1119a3`](https://github.com/nodejs/node/commit/585f1119a3)] - **doc**: update GOVERNANCE.md for TSC Charter changes (Rich Trott) [#37888](https://github.com/nodejs/node/pull/37888)
- [[`b51651cfc5`](https://github.com/nodejs/node/commit/b51651cfc5)] - **doc**: reduce header nesting in async_hooks.md (Rich Trott) [#37839](https://github.com/nodejs/node/pull/37839)
- [[`7789159009`](https://github.com/nodejs/node/commit/7789159009)] - **doc**: add examples for WHATWG URL objects (James M Snell) [#37822](https://github.com/nodejs/node/pull/37822)
- [[`b31bb72c10`](https://github.com/nodejs/node/commit/b31bb72c10)] - **doc**: clarify when child process 'spawn' event is \*not\* emitted (Matthew Francis Brunetti) [#37833](https://github.com/nodejs/node/pull/37833)
- [[`9166653aef`](https://github.com/nodejs/node/commit/9166653aef)] - **doc**: fix legacy stability indicator display (Rich Trott) [#37838](https://github.com/nodejs/node/pull/37838)
- [[`2e0266de5b`](https://github.com/nodejs/node/commit/2e0266de5b)] - **doc**: use sentence-style capitlaztion in template header (Rich Trott) [#37837](https://github.com/nodejs/node/pull/37837)
- [[`1b83242772`](https://github.com/nodejs/node/commit/1b83242772)] - **doc**: add Ayase-252 to triagers (Qingyu Deng) [#37781](https://github.com/nodejs/node/pull/37781)
- [[`89418e8758`](https://github.com/nodejs/node/commit/89418e8758)] - **doc**: use sentence case in issues.md headers (marsonya) [#37537](https://github.com/nodejs/node/pull/37537)
- [[`66502fc186`](https://github.com/nodejs/node/commit/66502fc186)] - **doc**: move Derek Lewis back to collaborators (Derek Lewis) [#37726](https://github.com/nodejs/node/pull/37726)
- [[`0d720a4a5c`](https://github.com/nodejs/node/commit/0d720a4a5c)] - **doc**: apply style for legacy status (James M Snell) [#37784](https://github.com/nodejs/node/pull/37784)
- [[`c8383dd99f`](https://github.com/nodejs/node/commit/c8383dd99f)] - **doc**: revoke deprecation of legacy url, change status to legacy (James M Snell) [#37784](https://github.com/nodejs/node/pull/37784)
- [[`e34aace62b`](https://github.com/nodejs/node/commit/e34aace62b)] - **doc**: add legacy status to stability index (James M Snell) [#37784](https://github.com/nodejs/node/pull/37784)
- [[`b2d3ac835c`](https://github.com/nodejs/node/commit/b2d3ac835c)] - **doc**: add @linkgoron to collaborators (Nitzan Uziely) [#37817](https://github.com/nodejs/node/pull/37817)
- [[`a27534e883`](https://github.com/nodejs/node/commit/a27534e883)] - **doc**: fix AbortError example for timers (dbachko) [#37738](https://github.com/nodejs/node/pull/37738)
- [[`14a160ae04`](https://github.com/nodejs/node/commit/14a160ae04)] - **doc**: fix typo in stream docs (Ian Kerins) [#37716](https://github.com/nodejs/node/pull/37716)
- [[`fe0f6a53a6`](https://github.com/nodejs/node/commit/fe0f6a53a6)] - **doc**: add gyp maintain info (Jiawen Geng) [#37765](https://github.com/nodejs/node/pull/37765)
- [[`6d7c7bc8d9`](https://github.com/nodejs/node/commit/6d7c7bc8d9)] - **doc**: add marsonya as a triager (marsonya) [#37667](https://github.com/nodejs/node/pull/37667)
- [[`5f2da5af42`](https://github.com/nodejs/node/commit/5f2da5af42)] - **doc**: add hints to http.request() options (Luigi Pinca) [#37745](https://github.com/nodejs/node/pull/37745)
- [[`02cd4044da`](https://github.com/nodejs/node/commit/02cd4044da)] - **doc**: fix link to googletest fixtures (Tobias Nießen) [#37698](https://github.com/nodejs/node/pull/37698)
- [[`85a293bdfc`](https://github.com/nodejs/node/commit/85a293bdfc)] - **doc**: fix typo in description of close event (Tobias Nießen) [#37662](https://github.com/nodejs/node/pull/37662)
- [[`3a6e40530c`](https://github.com/nodejs/node/commit/3a6e40530c)] - **doc**: use sentence case in README.md headers (marsonya) [#37645](https://github.com/nodejs/node/pull/37645)
- [[`c51a60cd05`](https://github.com/nodejs/node/commit/c51a60cd05)] - **doc**: add localPort to http.request() options (Luigi Pinca) [#37586](https://github.com/nodejs/node/pull/37586)
- [[`b0840ac680`](https://github.com/nodejs/node/commit/b0840ac680)] - **doc**: fix typo in doc/guides/collaborator-guide.md (marsonya) [#37643](https://github.com/nodejs/node/pull/37643)
- [[`5ef2a8de25`](https://github.com/nodejs/node/commit/5ef2a8de25)] - **doc**: document that module.evaluate fulfills as undefined (James M Snell) [#37663](https://github.com/nodejs/node/pull/37663)
- [[`b192227a95`](https://github.com/nodejs/node/commit/b192227a95)] - **doc**: add return type of readline.createInterface (Darshan Sen) [#37600](https://github.com/nodejs/node/pull/37600)
- [[`68d5cb80de`](https://github.com/nodejs/node/commit/68d5cb80de)] - **doc**: apply sentence case to headers in pull-requests.md (marsonya) [#37602](https://github.com/nodejs/node/pull/37602)
- [[`183dba0dd8`](https://github.com/nodejs/node/commit/183dba0dd8)] - **doc**: add top-level await syntax in vm.md (Antoine du Hamel) [#37077](https://github.com/nodejs/node/pull/37077)
- [[`1dc7f426aa`](https://github.com/nodejs/node/commit/1dc7f426aa)] - **doc**: clarify that columnOffset applies only to the first line (James M Snell) [#37563](https://github.com/nodejs/node/pull/37563)
- [[`c21731b39f`](https://github.com/nodejs/node/commit/c21731b39f)] - **doc**: document that NODE_EXTRA_CA_CERTS is read only once (James M Snell) [#37562](https://github.com/nodejs/node/pull/37562)
- [[`0255ed7e8e`](https://github.com/nodejs/node/commit/0255ed7e8e)] - **doc**: fix typo in doc/api/packages.md (marsonya) [#37536](https://github.com/nodejs/node/pull/37536)
- [[`52c0f0bf0f`](https://github.com/nodejs/node/commit/52c0f0bf0f)] - **doc**: revise LTS text in collaborator guide (Rich Trott) [#37527](https://github.com/nodejs/node/pull/37527)
- [[`fdc6a96d49`](https://github.com/nodejs/node/commit/fdc6a96d49)] - **doc**: revise CI text in collaborator guide (Rich Trott) [#37526](https://github.com/nodejs/node/pull/37526)
- [[`c62a1345bb`](https://github.com/nodejs/node/commit/c62a1345bb)] - **doc**: revise objections section of collaborator guide (Rich Trott) [#37525](https://github.com/nodejs/node/pull/37525)
- [[`adc75368ba`](https://github.com/nodejs/node/commit/adc75368ba)] - **doc**: revise premature disclosure text in collaborator guide (Rich Trott) [#37524](https://github.com/nodejs/node/pull/37524)
- [[`1b851461b1`](https://github.com/nodejs/node/commit/1b851461b1)] - **doc**: change links to use HEAD in top level docs (Michael Dawson) [#37494](https://github.com/nodejs/node/pull/37494)
- [[`64ed65ecc4`](https://github.com/nodejs/node/commit/64ed65ecc4)] - **doc**: apply sentence case to headers in doc/guides (marsonya) [#37506](https://github.com/nodejs/node/pull/37506)
- [[`ff1990c409`](https://github.com/nodejs/node/commit/ff1990c409)] - **doc**: add url.resolve replacement example (Antoine du Hamel) [#37501](https://github.com/nodejs/node/pull/37501)
- [[`52b3b54c14`](https://github.com/nodejs/node/commit/52b3b54c14)] - **doc**: apply sentence case to guides headers (marsonya) [#37497](https://github.com/nodejs/node/pull/37497)
- [[`da2cd4a48a`](https://github.com/nodejs/node/commit/da2cd4a48a)] - **doc**: update CI requirements for landing pull requests (Antoine du Hamel) [#37308](https://github.com/nodejs/node/pull/37308)
- [[`2082f5bd68`](https://github.com/nodejs/node/commit/2082f5bd68)] - **doc**: recommend queueMicrotask over process.nextTick (James M Snell) [#37484](https://github.com/nodejs/node/pull/37484)
- [[`099eef6a84`](https://github.com/nodejs/node/commit/099eef6a84)] - **doc**: apply sentence case to headers in doc/guides (marsonya) [#37478](https://github.com/nodejs/node/pull/37478)
- [[`a0bab6915e`](https://github.com/nodejs/node/commit/a0bab6915e)] - **doc**: fix typo in doc/api/http2/md (marsonya) [#37479](https://github.com/nodejs/node/pull/37479)
- [[`3e82263877`](https://github.com/nodejs/node/commit/3e82263877)] - **doc**: alphabetize vm Module class properties (Rich Trott) [#37451](https://github.com/nodejs/node/pull/37451)
- [[`e6f804b0af`](https://github.com/nodejs/node/commit/e6f804b0af)] - **doc**: alphabetize crypto Cipher class entries (Rich Trott) [#37450](https://github.com/nodejs/node/pull/37450)
- [[`bb434a983c`](https://github.com/nodejs/node/commit/bb434a983c)] - **doc**: use HEAD for links in api docs (Michael Dawson) [#37437](https://github.com/nodejs/node/pull/37437)
- [[`39ef3bd155`](https://github.com/nodejs/node/commit/39ef3bd155)] - **doc**: fix alignment of parameters (Michael Dawson) [#37422](https://github.com/nodejs/node/pull/37422)
- [[`8b60e66982`](https://github.com/nodejs/node/commit/8b60e66982)] - **doc**: fix typo in doc/api/esm.md (marsonya) [#37400](https://github.com/nodejs/node/pull/37400)
- [[`605cb4cd4c`](https://github.com/nodejs/node/commit/605cb4cd4c)] - **doc**: fix typo in esm.md (Jay Tailor) [#37417](https://github.com/nodejs/node/pull/37417)
- [[`74f0760a9b`](https://github.com/nodejs/node/commit/74f0760a9b)] - **doc**: use HEAD in links where possible (Michael Dawson) [#37421](https://github.com/nodejs/node/pull/37421)
- [[`4785755014`](https://github.com/nodejs/node/commit/4785755014)] - **doc**: clarify that async_hook callbacks cannot be async (James M Snell) [#37384](https://github.com/nodejs/node/pull/37384)
- [[`07130c038f`](https://github.com/nodejs/node/commit/07130c038f)] - **doc**: add dmabupt to collaborators (Xu Meng) [#37377](https://github.com/nodejs/node/pull/37377)
- [[`2a3feff2f0`](https://github.com/nodejs/node/commit/2a3feff2f0)] - **doc**: optimize HTML rendering (Antoine du Hamel) [#37301](https://github.com/nodejs/node/pull/37301)
- [[`8b5e42e031`](https://github.com/nodejs/node/commit/8b5e42e031)] - **doc**: fix quotes in stream docs (Tobias Nießen) [#37269](https://github.com/nodejs/node/pull/37269)
- [[`d426143f54`](https://github.com/nodejs/node/commit/d426143f54)] - **doc**: link PACKAGE_EXPORTS_RESOLVE to ESM section (Utku Gultopu) [#37135](https://github.com/nodejs/node/pull/37135)
- [[`debffd9b41`](https://github.com/nodejs/node/commit/debffd9b41)] - **doc**: use sentence case in benchmark doc (Rich Trott) [#37351](https://github.com/nodejs/node/pull/37351)
- [[`f28a5c6e1e`](https://github.com/nodejs/node/commit/f28a5c6e1e)] - **doc**: apply sentence-consistently in C++ style guide (Rich Trott) [#37350](https://github.com/nodejs/node/pull/37350)
- [[`569ad98b9a`](https://github.com/nodejs/node/commit/569ad98b9a)] - **doc**: apply sentence case to release doc headers (Rich Trott) [#37349](https://github.com/nodejs/node/pull/37349)
- [[`7cf4a4b2b8`](https://github.com/nodejs/node/commit/7cf4a4b2b8)] - **doc**: fix performanceEntry.flags style format (Cheng Liu) [#37274](https://github.com/nodejs/node/pull/37274)
- [[`5ade2fd207`](https://github.com/nodejs/node/commit/5ade2fd207)] - **doc**: fix typo in deprecations.md (marsonya) [#37282](https://github.com/nodejs/node/pull/37282)
- [[`5bc0a0d9f7`](https://github.com/nodejs/node/commit/5bc0a0d9f7)] - **doc**: add version metadata for packages features (Antoine du Hamel) [#37289](https://github.com/nodejs/node/pull/37289)
- [[`b485a3e2d2`](https://github.com/nodejs/node/commit/b485a3e2d2)] - **doc**: fix typo in /api/dns.md (marsonya) [#37312](https://github.com/nodejs/node/pull/37312)
- [[`a99456ce69`](https://github.com/nodejs/node/commit/a99456ce69)] - **doc**: fix description of hasSubscribers (Tobias Nießen) [#37324](https://github.com/nodejs/node/pull/37324)
- [[`b7c9366979`](https://github.com/nodejs/node/commit/b7c9366979)] - **doc**: discourage error event (Benjamin Gruenbaum) [#37264](https://github.com/nodejs/node/pull/37264)
- [[`8c41bc953e`](https://github.com/nodejs/node/commit/8c41bc953e)] - **doc**: fix misnamed SHASUMS256.txt name in README.md (marsonya) [#37260](https://github.com/nodejs/node/pull/37260)
- [[`b2ee1afb2e`](https://github.com/nodejs/node/commit/b2ee1afb2e)] - **doc**: fix typo in console.md (marsonya) [#37279](https://github.com/nodejs/node/pull/37279)
- [[`281d75cebb`](https://github.com/nodejs/node/commit/281d75cebb)] - **doc**: use sentence case in README headers (Rich Trott) [#37251](https://github.com/nodejs/node/pull/37251)
- [[`8cffab6571`](https://github.com/nodejs/node/commit/8cffab6571)] - **doc**: use sentence case for headers in BUILDING.md (Rich Trott) [#37250](https://github.com/nodejs/node/pull/37250)
- [[`0eaeaea454`](https://github.com/nodejs/node/commit/0eaeaea454)] - **doc**: rename N-API to Node-API (Gabriel Schulhof) [#37259](https://github.com/nodejs/node/pull/37259)
- [[`cb632e4040`](https://github.com/nodejs/node/commit/cb632e4040)] - **doc**: fix version number for DEP006 (Antoine du Hamel) [#37231](https://github.com/nodejs/node/pull/37231)
- [[`e7415c374b`](https://github.com/nodejs/node/commit/e7415c374b)] - **doc**: fix CHANGELOG_ARCHIVE table of contents (Antoine du Hamel) [#37232](https://github.com/nodejs/node/pull/37232)
- [[`2959c65632`](https://github.com/nodejs/node/commit/2959c65632)] - **doc**: fix typo in globals.md (Darshan Sen) [#37228](https://github.com/nodejs/node/pull/37228)
- [[`ad80e3de1e`](https://github.com/nodejs/node/commit/ad80e3de1e)] - **doc**: fix 404 links in module.md (Antoine du Hamel) [#37202](https://github.com/nodejs/node/pull/37202)
- [[`e7ca9b6d71`](https://github.com/nodejs/node/commit/e7ca9b6d71)] - **doc**: fix color contrast on \<kbd\> elements (Antoine du Hamel) [#37185](https://github.com/nodejs/node/pull/37185)
- [[`11d3e71f80`](https://github.com/nodejs/node/commit/11d3e71f80)] - **doc**: improve promise terminology (Benjamin Gruenbaum) [#37181](https://github.com/nodejs/node/pull/37181)
- [[`35cf86c83b`](https://github.com/nodejs/node/commit/35cf86c83b)] - **doc**: fix list format in Developer's Certificate of Origin (Akash Negi) [#37138](https://github.com/nodejs/node/pull/37138)
- [[`6264ac187a`](https://github.com/nodejs/node/commit/6264ac187a)] - **doc**: clarify ERR_INVALID_REPL_INPUT usage (Rich Trott) [#37143](https://github.com/nodejs/node/pull/37143)
- [[`d340dca940`](https://github.com/nodejs/node/commit/d340dca940)] - **doc**: clarify repl exception conditions (Rich Trott) [#37142](https://github.com/nodejs/node/pull/37142)
- [[`26ec20a9b6`](https://github.com/nodejs/node/commit/26ec20a9b6)] - **doc**: add example for test structure (Turner Jabbour) [#35046](https://github.com/nodejs/node/pull/35046)
- [[`8099bfb35c`](https://github.com/nodejs/node/commit/8099bfb35c)] - **doc**: remove TOC summary for pages with no TOC (Rich Trott) [#37043](https://github.com/nodejs/node/pull/37043)
- [[`b0c9b1fdfb`](https://github.com/nodejs/node/commit/b0c9b1fdfb)] - **doc**: update Buffer encoding option count (Dave Cardwell) [#37102](https://github.com/nodejs/node/pull/37102)
- [[`af313a8495`](https://github.com/nodejs/node/commit/af313a8495)] - **doc**: update BUILDING.md previous versions links (Richard Lau) [#37082](https://github.com/nodejs/node/pull/37082)
- [[`b353549f7c`](https://github.com/nodejs/node/commit/b353549f7c)] - **doc**: mention adding Fixes to collaborator onboarding PR (Joyee Cheung) [#37097](https://github.com/nodejs/node/pull/37097)
- [[`8ed0c17bee`](https://github.com/nodejs/node/commit/8ed0c17bee)] - **doc**: add Zijian Liu to collaborators (ZiJian Liu) [#37075](https://github.com/nodejs/node/pull/37075)
- [[`87fcda8d3e`](https://github.com/nodejs/node/commit/87fcda8d3e)] - **doc**: add tooltip for light/dark mode toggle (Rich Trott) [#37044](https://github.com/nodejs/node/pull/37044)
- [[`49f13743e5`](https://github.com/nodejs/node/commit/49f13743e5)] - **doc**: improve AsyncLocalStorage introduction (Romuald Brillout) [#36946](https://github.com/nodejs/node/pull/36946)
- [[`b8080134a2`](https://github.com/nodejs/node/commit/b8080134a2)] - **doc**: add missing comma in tty (Matthew Mario Di Pasquale) [#37039](https://github.com/nodejs/node/pull/37039)
- [[`1e2beeea99`](https://github.com/nodejs/node/commit/1e2beeea99)] - **doc**: list Unsupported Directory Import resolve err (Guy Bedford) [#37032](https://github.com/nodejs/node/pull/37032)
- [[`fb4eee132e`](https://github.com/nodejs/node/commit/fb4eee132e)] - **doc**: add missing ARIA label for button (Rich Trott) [#37031](https://github.com/nodejs/node/pull/37031)
- [[`f260f4a12f`](https://github.com/nodejs/node/commit/f260f4a12f)] - **doc**: add @RaisinTen to collaborators (Darshan Sen) [#36998](https://github.com/nodejs/node/pull/36998)
- [[`00075986f0`](https://github.com/nodejs/node/commit/00075986f0)] - **doc**: fix typo in http.server.requestTimout docs (alexbs) [#36987](https://github.com/nodejs/node/pull/36987)
- [[`b25b69418a`](https://github.com/nodejs/node/commit/b25b69418a)] - **doc**: add performance notes for fs.readFile (James M Snell) [#36880](https://github.com/nodejs/node/pull/36880)
- [[`385d0df02d`](https://github.com/nodejs/node/commit/385d0df02d)] - **doc**: clarify maxSockets option of http.Agent (Pooja D P) [#36941](https://github.com/nodejs/node/pull/36941)
- [[`792bea4e78`](https://github.com/nodejs/node/commit/792bea4e78)] - **doc**: remove pull-requests.md preamble (Rich Trott) [#36960](https://github.com/nodejs/node/pull/36960)
- [[`d43492ee42`](https://github.com/nodejs/node/commit/d43492ee42)] - **doc**: fix percentile range in perf_hooks.md (raisinten) [#36938](https://github.com/nodejs/node/pull/36938)
- [[`f39ee90c94`](https://github.com/nodejs/node/commit/f39ee90c94)] - **doc**: improve perf_hooks docs (Juan José Arboleda) [#36909](https://github.com/nodejs/node/pull/36909)
- [[`e990b11672`](https://github.com/nodejs/node/commit/e990b11672)] - **doc**: fix invalid HTML in doc template (Rich Trott) [#36930](https://github.com/nodejs/node/pull/36930)
- [[`e1c62dd977`](https://github.com/nodejs/node/commit/e1c62dd977)] - **doc**: remove issue template duplication from contributing docs (Rich Trott) [#36908](https://github.com/nodejs/node/pull/36908)
- [[`3c70f6842d`](https://github.com/nodejs/node/commit/3c70f6842d)] - **doc**: remove resolving-a-bug-report from contributing docs (Rich Trott) [#36905](https://github.com/nodejs/node/pull/36905)
- [[`308d361a79`](https://github.com/nodejs/node/commit/308d361a79)] - **doc**: use ESM syntax for WASI example (Antoine du Hamel) [#36848](https://github.com/nodejs/node/pull/36848)
- [[`189fae8618`](https://github.com/nodejs/node/commit/189fae8618)] - **doc**: add iansu to collaborators (Ian Sutherland) [#36951](https://github.com/nodejs/node/pull/36951)
- [[`28c80b514f`](https://github.com/nodejs/node/commit/28c80b514f)] - **doc**: add alternative version links to the packages page (Filip Skokan) [#36915](https://github.com/nodejs/node/pull/36915)
- [[`ed5bb7673f`](https://github.com/nodejs/node/commit/ed5bb7673f)] - **doc**: add miladfarca to collaborators (Milad Fa) [#36934](https://github.com/nodejs/node/pull/36934)
- [[`580b647ee4`](https://github.com/nodejs/node/commit/580b647ee4)] - **doc**: update tls test to use better terminology (Michael Dawson) [#36851](https://github.com/nodejs/node/pull/36851)
- [[`fa82cbc4f7`](https://github.com/nodejs/node/commit/fa82cbc4f7)] - **doc**: remove unnecessary contributing.md section (Rich Trott) [#36891](https://github.com/nodejs/node/pull/36891)
- [[`583b2192d9`](https://github.com/nodejs/node/commit/583b2192d9)] - **doc**: wrap TOC in a \<details\> tag (Mattia Pontonio) [#36896](https://github.com/nodejs/node/pull/36896)
- [[`9a3cfa7069`](https://github.com/nodejs/node/commit/9a3cfa7069)] - **doc**: fix indentation on http2 doc entry (Rich Trott) [#36869](https://github.com/nodejs/node/pull/36869)
- [[`7fbbdb831a`](https://github.com/nodejs/node/commit/7fbbdb831a)] - **doc**: define "browser", "production", "development" (Guy Bedford) [#36856](https://github.com/nodejs/node/pull/36856)
- [[`5770ae057e`](https://github.com/nodejs/node/commit/5770ae057e)] - **doc**: fix module syncBuiltinESMExports example (Bruce A. MacNaughton) [#34284](https://github.com/nodejs/node/pull/34284)
- [[`099d776e29`](https://github.com/nodejs/node/commit/099d776e29)] - **doc**: update release key for Danielle Adams (Danielle Adams) [#36793](https://github.com/nodejs/node/pull/36793)
- [[`57e27a3824`](https://github.com/nodejs/node/commit/57e27a3824)] - **doc**: clarify child_process.exec inherits cwd (ugultopu) [#36809](https://github.com/nodejs/node/pull/36809)
- [[`f605bc00ae`](https://github.com/nodejs/node/commit/f605bc00ae)] - **doc**: clarify descriptions of \_writev chunks argument (James M Snell) [#36822](https://github.com/nodejs/node/pull/36822)
- [[`cf0fa7fa2f`](https://github.com/nodejs/node/commit/cf0fa7fa2f)] - **doc**: document buffer's "Uint" aliases clearly (Michaël Zasso) [#36796](https://github.com/nodejs/node/pull/36796)
- [[`5f36ff80b9`](https://github.com/nodejs/node/commit/5f36ff80b9)] - **doc**: add dnlup to collaborators (Daniele Belardi) [#36849](https://github.com/nodejs/node/pull/36849)
- [[`c1ea23c55d`](https://github.com/nodejs/node/commit/c1ea23c55d)] - **doc**: clarify subprocess.stdout/in/err/io properties (James M Snell) [#36784](https://github.com/nodejs/node/pull/36784)
- [[`19e61206f2`](https://github.com/nodejs/node/commit/19e61206f2)] - **doc**: add dark mode (Ajay Poshak) [#36313](https://github.com/nodejs/node/pull/36313)
- [[`8620458966`](https://github.com/nodejs/node/commit/8620458966)] - **doc**: revise method text in async_hooks.md (Rich Trott) [#36736](https://github.com/nodejs/node/pull/36736)
- [[`a800b5b698`](https://github.com/nodejs/node/commit/a800b5b698)] - **doc**: clarify when messageerror is emitted (James M Snell) [#36780](https://github.com/nodejs/node/pull/36780)
- [[`e973bdc14e`](https://github.com/nodejs/node/commit/e973bdc14e)] - **doc**: avoid memory leak warning in async_hooks example (James M Snell) [#36783](https://github.com/nodejs/node/pull/36783)
- [[`1521a59002`](https://github.com/nodejs/node/commit/1521a59002)] - **doc**: clarify that --require only supports cjs (James M Snell) [#36806](https://github.com/nodejs/node/pull/36806)
- [[`dc15608661`](https://github.com/nodejs/node/commit/dc15608661)] - **doc**: clarify Buffer.from when using ArrayBuffer (James M Snell) [#36785](https://github.com/nodejs/node/pull/36785)
- [[`67a6e9c516`](https://github.com/nodejs/node/commit/67a6e9c516)] - **doc**: fix broken link for ChildProcess (James M Snell) [#36788](https://github.com/nodejs/node/pull/36788)
- [[`31039dbf63`](https://github.com/nodejs/node/commit/31039dbf63)] - **doc**: revise exit() and run() text in async_hooks.md (Rich Trott) [#36738](https://github.com/nodejs/node/pull/36738)
- [[`844a15622f`](https://github.com/nodejs/node/commit/844a15622f)] - **doc**: clarify that N-API addons are context-aware (Alba Mendez) [#36640](https://github.com/nodejs/node/pull/36640)
- [[`8f48e77217`](https://github.com/nodejs/node/commit/8f48e77217)] - **doc**: fix typo in esm documentation (Mohamed Kamagate) [#36800](https://github.com/nodejs/node/pull/36800)
- [[`095c69dce3`](https://github.com/nodejs/node/commit/095c69dce3)] - **doc**: add panva to collaborators (Filip Skokan) [#36802](https://github.com/nodejs/node/pull/36802)
- [[`8bda9f4dce`](https://github.com/nodejs/node/commit/8bda9f4dce)] - **doc**: reduce abbreviations in async_hooks.md (Rich Trott) [#36737](https://github.com/nodejs/node/pull/36737)
- [[`1a65b442f0`](https://github.com/nodejs/node/commit/1a65b442f0)] - **doc**: simplify pull request template (Rich Trott) [#36739](https://github.com/nodejs/node/pull/36739)
- [[`71b94e1ff7`](https://github.com/nodejs/node/commit/71b94e1ff7)] - **doc**: clarify undocumented stream properties (James M Snell) [#36715](https://github.com/nodejs/node/pull/36715)
- [[`bcca52c11a`](https://github.com/nodejs/node/commit/bcca52c11a)] - **doc**: document common warning types (James M Snell) [#36713](https://github.com/nodejs/node/pull/36713)
- [[`97faeeb115`](https://github.com/nodejs/node/commit/97faeeb115)] - **doc**: improve ALS.enterWith and exit descriptions (Andrey Pechkurov) [#36705](https://github.com/nodejs/node/pull/36705)
- [[`535bbc294a`](https://github.com/nodejs/node/commit/535bbc294a)] - **doc**: add yashLadha to collaborator (Yash Ladha) [#36666](https://github.com/nodejs/node/pull/36666)
- [[`6293aea1d1`](https://github.com/nodejs/node/commit/6293aea1d1)] - **doc**: alphabetize http response properties (Rich Trott) [#36631](https://github.com/nodejs/node/pull/36631)
- [[`afb9534d65`](https://github.com/nodejs/node/commit/afb9534d65)] - **doc**: correct callback parameter type for createPushResponse() (Rich Trott) [#36631](https://github.com/nodejs/node/pull/36631)
- [[`f65b842ee1`](https://github.com/nodejs/node/commit/f65b842ee1)] - **doc**: use \_code name\_ rather than \_codename\_ (Rich Trott) [#36611](https://github.com/nodejs/node/pull/36611)
- [[`12dc0e6a28`](https://github.com/nodejs/node/commit/12dc0e6a28)] - **doc**: document return value of https.request (Michael Chen) [#36370](https://github.com/nodejs/node/pull/36370)
- [[`317a1d7a0d`](https://github.com/nodejs/node/commit/317a1d7a0d)] - **doc**: remove replication of GitHub template (Rich Trott) [#36590](https://github.com/nodejs/node/pull/36590)
- [[`653492f2d4`](https://github.com/nodejs/node/commit/653492f2d4)] - **doc**: remove "Related Issues" from pull request template (Rich Trott) [#36590](https://github.com/nodejs/node/pull/36590)
- [[`b57785d89b`](https://github.com/nodejs/node/commit/b57785d89b)] - **doc**: update and run license-builder for Babel (Michaël Zasso) [#36504](https://github.com/nodejs/node/pull/36504)
- [[`e3c2d112eb`](https://github.com/nodejs/node/commit/e3c2d112eb)] - **doc**: add remark about Collaborators discussion page (FrankQiu) [#36420](https://github.com/nodejs/node/pull/36420)
- [[`8b349187b8`](https://github.com/nodejs/node/commit/8b349187b8)] - **doc**: add two tips for speeding the dev builds (Momtchil Momtchev) [#36452](https://github.com/nodejs/node/pull/36452)
- [[`6198d74cd3`](https://github.com/nodejs/node/commit/6198d74cd3)] - **doc**: add note about timingSafeEqual for TypedArray (Tobias Nießen) [#36323](https://github.com/nodejs/node/pull/36323)
- [[`d27028040e`](https://github.com/nodejs/node/commit/d27028040e)] - **doc**: move Derek Lewis to emeritus (Rich Trott) [#36514](https://github.com/nodejs/node/pull/36514)
- [[`cf9d476948`](https://github.com/nodejs/node/commit/cf9d476948)] - **doc**: add issue reference to github pr template (Chinmoy Chakraborty) [#36440](https://github.com/nodejs/node/pull/36440)
- [[`760e593968`](https://github.com/nodejs/node/commit/760e593968)] - **doc**: update url.md (Rock) [#36147](https://github.com/nodejs/node/pull/36147)
- [[`c32c109450`](https://github.com/nodejs/node/commit/c32c109450)] - **doc**: make explicit reverting node_version.h changes (Richard Lau) [#36461](https://github.com/nodejs/node/pull/36461)
- [[`647ec70419`](https://github.com/nodejs/node/commit/647ec70419)] - **doc**: add license info to the README (FrankQiu) [#36278](https://github.com/nodejs/node/pull/36278)
- [[`2ec839d974`](https://github.com/nodejs/node/commit/2ec839d974)] - **doc**: revise addon mulitple initializations text (Rich Trott) [#36457](https://github.com/nodejs/node/pull/36457)
- [[`050b52f16b`](https://github.com/nodejs/node/commit/050b52f16b)] - **doc**: add PoojaDurgad to collaborators (Pooja D P) [#36511](https://github.com/nodejs/node/pull/36511)
- [[`681d2a7176`](https://github.com/nodejs/node/commit/681d2a7176)] - **doc**: edit addon text about event loop blocking (Rich Trott) [#36448](https://github.com/nodejs/node/pull/36448)
- [[`afad5e6be6`](https://github.com/nodejs/node/commit/afad5e6be6)] - **doc**: update terminology (Michael Dawson) [#36475](https://github.com/nodejs/node/pull/36475)
- [[`615a0aaf86`](https://github.com/nodejs/node/commit/615a0aaf86)] - **doc**: reword POSIX threads text in addons.md (Rich Trott) [#36436](https://github.com/nodejs/node/pull/36436)
- [[`94d41f22f0`](https://github.com/nodejs/node/commit/94d41f22f0)] - **doc**: add RaisinTen as a triager (raisinten) [#36404](https://github.com/nodejs/node/pull/36404)
- [[`cd065210a5`](https://github.com/nodejs/node/commit/cd065210a5)] - **doc**: provide more context on techinical values (Michael Dawson) [#36201](https://github.com/nodejs/node/pull/36201)
- [[`f515156fab`](https://github.com/nodejs/node/commit/f515156fab)] - **doc**: add Powershell oneliner to get Windows version (Michael Bashurov) [#30289](https://github.com/nodejs/node/pull/30289)
- [[`280d1b09be`](https://github.com/nodejs/node/commit/280d1b09be)] - **doc**: add process for handling premature disclosure (Michael Dawson) [#36155](https://github.com/nodejs/node/pull/36155)
- [[`9269352e45`](https://github.com/nodejs/node/commit/9269352e45)] - **doc**: add table header in intl.md (Rich Trott) [#36261](https://github.com/nodejs/node/pull/36261)
- [[`6201f23e12`](https://github.com/nodejs/node/commit/6201f23e12)] - **doc**: adding example to Buffer.isBuffer method (naortedgi) [#36233](https://github.com/nodejs/node/pull/36233)
- [[`1d78d50127`](https://github.com/nodejs/node/commit/1d78d50127)] - **doc**: fix typo in events.md (Luigi Pinca) [#36231](https://github.com/nodejs/node/pull/36231)
- [[`9b32c9c575`](https://github.com/nodejs/node/commit/9b32c9c575)] - **doc**: fix --experimental-wasm-modules text location (Colin Ihrig) [#36220](https://github.com/nodejs/node/pull/36220)
- [[`28149cff23`](https://github.com/nodejs/node/commit/28149cff23)] - **doc**: add missing version to update cmd (Ruy Adorno) [#36204](https://github.com/nodejs/node/pull/36204)
- [[`2d6494775b`](https://github.com/nodejs/node/commit/2d6494775b)] - **doc**: fix invalid link in worker_threads.md (Rich Trott) [#36109](https://github.com/nodejs/node/pull/36109)
- [[`ec140d7160`](https://github.com/nodejs/node/commit/ec140d7160)] - **doc**: fix `events.getEventListeners` example (Dmitry Semigradsky) [#36085](https://github.com/nodejs/node/pull/36085)
- [[`34d8a64c56`](https://github.com/nodejs/node/commit/34d8a64c56)] - **doc**: fix incorrect heading level (Bryan Field) [#35965](https://github.com/nodejs/node/pull/35965)
- [[`d9d7ebd0b4`](https://github.com/nodejs/node/commit/d9d7ebd0b4)] - **doc**: make globals Extends usage consistent (Colin Ihrig) [#33777](https://github.com/nodejs/node/pull/33777)
- [[`2f7afd11a4`](https://github.com/nodejs/node/commit/2f7afd11a4)] - **doc**: make perf_hooks Extends usage consistent (Colin Ihrig) [#33777](https://github.com/nodejs/node/pull/33777)
- [[`810b1d0cbb`](https://github.com/nodejs/node/commit/810b1d0cbb)] - **doc**: make events Extends usage consistent (Colin Ihrig) [#33777](https://github.com/nodejs/node/pull/33777)
- [[`cf4fa79f17`](https://github.com/nodejs/node/commit/cf4fa79f17)] - **doc**: recommend checking abortSignal.aborted first (James M Snell) [#37714](https://github.com/nodejs/node/pull/37714)
- [[`e93615c5e3`](https://github.com/nodejs/node/commit/e93615c5e3)] - **doc**: make AbortSignal text consistent in events.md (Rich Trott) [#35005](https://github.com/nodejs/node/pull/35005)
- [[`1a362d8d4b`](https://github.com/nodejs/node/commit/1a362d8d4b)] - **doc**: revise AbortSignal text and example using events.once() (Rich Trott) [#35005](https://github.com/nodejs/node/pull/35005)
- [[`a7da993cff`](https://github.com/nodejs/node/commit/a7da993cff)] - **doc**: stabilize packages features (Myles Borins) [#35742](https://github.com/nodejs/node/pull/35742)
- [[`b133034735`](https://github.com/nodejs/node/commit/b133034735)] - **doc**: stabilize subpath patterns (Guy Bedford) [#36177](https://github.com/nodejs/node/pull/36177)
- [[`5a3e12b1ee`](https://github.com/nodejs/node/commit/5a3e12b1ee)] - **doc**: update fs.l/statSync API history for throwIfNoEntry (Andrew Casey) [#36882](https://github.com/nodejs/node/pull/36882)
- [[`7c9f3a9d0c`](https://github.com/nodejs/node/commit/7c9f3a9d0c)] - **doc**: fix module.isPreloading documentation (Antoine du Hamel) [#36944](https://github.com/nodejs/node/pull/36944)
- [[`c1af59384d`](https://github.com/nodejs/node/commit/c1af59384d)] - **doc**: mark modules implementation as stable (Guy Bedford) [#35781](https://github.com/nodejs/node/pull/35781)
- [[`9930b6b825`](https://github.com/nodejs/node/commit/9930b6b825)] - **doc**: update `buffer.constants.MAX\_LENGTH` (Qingyu Deng) [#38109](https://github.com/nodejs/node/pull/38109)
- [[`c975a8ded1`](https://github.com/nodejs/node/commit/c975a8ded1)] - **doc**: fix maintaining ICU guide (Michaël Zasso) [#36980](https://github.com/nodejs/node/pull/36980)
- [[`d1004d26e4`](https://github.com/nodejs/node/commit/d1004d26e4)] - **doc**: fix typo in BUILDING.md (raisinten) [#35807](https://github.com/nodejs/node/pull/35807)
- [[`f41c28cf4b`](https://github.com/nodejs/node/commit/f41c28cf4b)] - **doc**: fix YAML lint error on master (Rich Trott) [#35709](https://github.com/nodejs/node/pull/35709)
- [[`4a768bc13b`](https://github.com/nodejs/node/commit/4a768bc13b)] - **doc**: upgrade stability status of report API (Gireesh Punathil) [#35654](https://github.com/nodejs/node/pull/35654)
- [[`a3c564bead`](https://github.com/nodejs/node/commit/a3c564bead)] - **doc,child_process**: `pid` can be `undefined` when `ENOENT` (dr-js) [#37014](https://github.com/nodejs/node/pull/37014)
- [[`404327563a`](https://github.com/nodejs/node/commit/404327563a)] - **doc,tools**: allow stability table to be updated (Richard Lau) [#38048](https://github.com/nodejs/node/pull/38048)
- [[`eb6ea8501b`](https://github.com/nodejs/node/commit/eb6ea8501b)] - **doc,tools**: use only one level 1 header per page (Rich Trott) [#37839](https://github.com/nodejs/node/pull/37839)
- [[`73c1999d0c`](https://github.com/nodejs/node/commit/73c1999d0c)] - **docs**: add references to punycode.md (Isaac Levy) [#36761](https://github.com/nodejs/node/pull/36761)
- [[`bd38dfbfcc`](https://github.com/nodejs/node/commit/bd38dfbfcc)] - **domain**: add name to monkey-patched emit function (Colin Ihrig) [#37550](https://github.com/nodejs/node/pull/37550)
- [[`13d972dd86`](https://github.com/nodejs/node/commit/13d972dd86)] - **domain**: show falsy names as anonymous for DEP0097 (Colin Ihrig) [#37550](https://github.com/nodejs/node/pull/37550)
- [[`3f43743c9d`](https://github.com/nodejs/node/commit/3f43743c9d)] - **domain**: make node resilient to Array prototype tempering (Antoine du Hamel) [#36676](https://github.com/nodejs/node/pull/36676)
- [[`4807499d21`](https://github.com/nodejs/node/commit/4807499d21)] - **domain**: improve deprecation warning text for DEP0097 (Anna Henningsen) [#36136](https://github.com/nodejs/node/pull/36136)
- [[`b01c496e34`](https://github.com/nodejs/node/commit/b01c496e34)] - **errors**: do not call resolve on URLs with schemes (Benjamin Coe) [#35903](https://github.com/nodejs/node/pull/35903)
- [[`3c37f896a4`](https://github.com/nodejs/node/commit/3c37f896a4)] - **errors**: print original exception context (Benjamin Coe) [#33491](https://github.com/nodejs/node/pull/33491)
- [[`3006302372`](https://github.com/nodejs/node/commit/3006302372)] - **(SEMVER-MINOR)** **events**: add max listener warning for EventTarget (James M Snell) [#36001](https://github.com/nodejs/node/pull/36001)
- [[`6e734a8a61`](https://github.com/nodejs/node/commit/6e734a8a61)] - **(SEMVER-MINOR)** **events**: getEventListeners static (Benjamin Gruenbaum) [#35991](https://github.com/nodejs/node/pull/35991)
- [[`64cd54be57`](https://github.com/nodejs/node/commit/64cd54be57)] - **events**: disabled manual construction AbortSignal (raisinten) [#36094](https://github.com/nodejs/node/pull/36094)
- [[`daad5214c4`](https://github.com/nodejs/node/commit/daad5214c4)] - **events**: fire handlers in correct oder (Benjamin Gruenbaum) [#35931](https://github.com/nodejs/node/pull/35931)
- [[`7c95cfc164`](https://github.com/nodejs/node/commit/7c95cfc164)] - **events**: define abort on prototype (Benjamin Gruenbaum) [#35931](https://github.com/nodejs/node/pull/35931)
- [[`bdad1bc4fc`](https://github.com/nodejs/node/commit/bdad1bc4fc)] - **events**: support event handlers on prototypes (Benjamin Gruenbaum) [#35931](https://github.com/nodejs/node/pull/35931)
- [[`6e21e8283f`](https://github.com/nodejs/node/commit/6e21e8283f)] - **events**: define event handler as enumerable (Benjamin Gruenbaum) [#35931](https://github.com/nodejs/node/pull/35931)
- [[`e51d7c535f`](https://github.com/nodejs/node/commit/e51d7c535f)] - **events**: support emit on nodeeventtarget (Benjamin Gruenbaum) [#35851](https://github.com/nodejs/node/pull/35851)
- [[`6558ffa76b`](https://github.com/nodejs/node/commit/6558ffa76b)] - **events**: add a few tests (Benjamin Gruenbaum) [#35806](https://github.com/nodejs/node/pull/35806)
- [[`bf728b5439`](https://github.com/nodejs/node/commit/bf728b5439)] - **events**: make abort_controller event trusted (Benjamin Gruenbaum) [#35811](https://github.com/nodejs/node/pull/35811)
- [[`3f33b5a89e`](https://github.com/nodejs/node/commit/3f33b5a89e)] - **(SEMVER-MINOR)** **events**: allow use of AbortController with on (James M Snell) [#34912](https://github.com/nodejs/node/pull/34912)
- [[`1fefb5cb75`](https://github.com/nodejs/node/commit/1fefb5cb75)] - **(SEMVER-MINOR)** **events**: allow use of AbortController with once (James M Snell) [#34911](https://github.com/nodejs/node/pull/34911)
- [[`85987450df`](https://github.com/nodejs/node/commit/85987450df)] - **fs**: fix chown abort (Darshan Sen) [#38004](https://github.com/nodejs/node/pull/38004)
- [[`dd1fe6d8ba`](https://github.com/nodejs/node/commit/dd1fe6d8ba)] - **fs**: add promisified readFile benchmark (Nitzan Uziely) [#37608](https://github.com/nodejs/node/pull/37608)
- [[`f2279f856e`](https://github.com/nodejs/node/commit/f2279f856e)] - **fs**: fix writeFile signal does not close file (Nitzan Uziely) [#37402](https://github.com/nodejs/node/pull/37402)
- [[`92348a9216`](https://github.com/nodejs/node/commit/92348a9216)] - **(SEMVER-MINOR)** **fs**: use a default callback for fs.close() (James M Snell) [#37174](https://github.com/nodejs/node/pull/37174)
- [[`e582832643`](https://github.com/nodejs/node/commit/e582832643)] - **fs**: only use Buffer.concat in promises.readFile when necessary (Anna Henningsen) [#37127](https://github.com/nodejs/node/pull/37127)
- [[`a1ee9b3680`](https://github.com/nodejs/node/commit/a1ee9b3680)] - **fs**: add explicit note about undefined path when recursive (Sebastian Silbermann) [#37010](https://github.com/nodejs/node/pull/37010)
- [[`fe6e5e4c0e`](https://github.com/nodejs/node/commit/fe6e5e4c0e)] - **fs**: accept non-32-bit length in writeBuffer (raisinten) [#36667](https://github.com/nodejs/node/pull/36667)
- [[`455243667a`](https://github.com/nodejs/node/commit/455243667a)] - **fs**: move method definition from header (Yash Ladha) [#36256](https://github.com/nodejs/node/pull/36256)
- [[`1bffd8d7bb`](https://github.com/nodejs/node/commit/1bffd8d7bb)] - **fs**: pass ERR_DIR_CLOSED asynchronously to dir.close (Zijian Liu) [#36243](https://github.com/nodejs/node/pull/36243)
- [[`9b8596a67f`](https://github.com/nodejs/node/commit/9b8596a67f)] - **fs**: fix when path is buffer on fs.symlinkSync (himself65) [#34540](https://github.com/nodejs/node/pull/34540)
- [[`026941381b`](https://github.com/nodejs/node/commit/026941381b)] - **fs**: fix pre-aborted writeFile AbortSignal file leak (Nitzan Uziely) [#37393](https://github.com/nodejs/node/pull/37393)
- [[`7aa2e5d8dc`](https://github.com/nodejs/node/commit/7aa2e5d8dc)] - **(SEMVER-MINOR)** **fs**: add AbortSignal support to watch (Benjamin Gruenbaum) [#37190](https://github.com/nodejs/node/pull/37190)
- [[`219cd000c0`](https://github.com/nodejs/node/commit/219cd000c0)] - **(SEMVER-MINOR)** **fs**: support abortsignal in writeFile (Benjamin Gruenbaum) [#35993](https://github.com/nodejs/node/pull/35993)
- [[`5f88b644f8`](https://github.com/nodejs/node/commit/5f88b644f8)] - **(SEMVER-MINOR)** **fs**: add support for AbortSignal in readFile (Benjamin Gruenbaum) [#35911](https://github.com/nodejs/node/pull/35911)
- [[`443cacee5f`](https://github.com/nodejs/node/commit/443cacee5f)] - **fs**: remove custom Buffer pool for streams (Robert Nagy) [#33981](https://github.com/nodejs/node/pull/33981)
- [[`d0115f14f6`](https://github.com/nodejs/node/commit/d0115f14f6)] - **(SEMVER-MINOR)** **http**: add http.ClientRequest.getRawHeaderNames() (simov) [#37660](https://github.com/nodejs/node/pull/37660)
- [[`105b8630b9`](https://github.com/nodejs/node/commit/105b8630b9)] - **http**: explain the possibilty of refactor unused argument (Qingyu Deng) [#37275](https://github.com/nodejs/node/pull/37275)
- [[`926bb4fd17`](https://github.com/nodejs/node/commit/926bb4fd17)] - **http**: explain the unused argument in IncomingMessage.\_read (Qingyu Deng) [#37275](https://github.com/nodejs/node/pull/37275)
- [[`e7bc37909c`](https://github.com/nodejs/node/commit/e7bc37909c)] - **http**: cleanup ClientRequest oncreate (Robert Nagy) [#36862](https://github.com/nodejs/node/pull/36862)
- [[`5064822f24`](https://github.com/nodejs/node/commit/5064822f24)] - **http**: make HEAD method to work with keep-alive (Joseph Hackman) [#34231](https://github.com/nodejs/node/pull/34231)
- [[`8163f3179b`](https://github.com/nodejs/node/commit/8163f3179b)] - **http**: remove dead code from internal/http.js (ZiJian Liu) [#36630](https://github.com/nodejs/node/pull/36630)
- [[`ad39b37974`](https://github.com/nodejs/node/commit/ad39b37974)] - **(SEMVER-MINOR)** **http**: enable call chaining with setHeader() (pooja d.p) [#35924](https://github.com/nodejs/node/pull/35924)
- [[`623099db4b`](https://github.com/nodejs/node/commit/623099db4b)] - **(SEMVER-MINOR)** **http**: report request start and end with diagnostics_channel (Stephen Belanger) [#34895](https://github.com/nodejs/node/pull/34895)
- [[`524b7134e8`](https://github.com/nodejs/node/commit/524b7134e8)] - **(SEMVER-MINOR)** **http**: add support for abortsignal to http.request (Benjamin Gruenbaum) [#36048](https://github.com/nodejs/node/pull/36048)
- [[`f70aee03ab`](https://github.com/nodejs/node/commit/f70aee03ab)] - **(SEMVER-MINOR)** **http**: set lifo as the default scheduling strategy in Agent (Matteo Collina) [#36685](https://github.com/nodejs/node/pull/36685)
- [[`16a16508c4`](https://github.com/nodejs/node/commit/16a16508c4)] - **http2**: fix typos in core.js (Pranshu Jethmalani) [#36719](https://github.com/nodejs/node/pull/36719)
- [[`2e259220cb`](https://github.com/nodejs/node/commit/2e259220cb)] - **(SEMVER-MINOR)** **http2**: add updateSettings to both http2 servers (Vincent Boivin) [#35383](https://github.com/nodejs/node/pull/35383)
- [[`336fb18b44`](https://github.com/nodejs/node/commit/336fb18b44)] - **http2**: add support for AbortSignal to http2Session.request (Madara Uchiha) [#36070](https://github.com/nodejs/node/pull/36070)
- [[`f44b3c12e5`](https://github.com/nodejs/node/commit/f44b3c12e5)] - **https**: add abortcontroller test (Benjamin Gruenbaum) [#36307](https://github.com/nodejs/node/pull/36307)
- [[`b5ad655c6b`](https://github.com/nodejs/node/commit/b5ad655c6b)] - **lib**: properly process JavaScript exceptions on async_hooks fatal error (legendecas) [#38106](https://github.com/nodejs/node/pull/38106)
- [[`1d8269302e`](https://github.com/nodejs/node/commit/1d8269302e)] - **lib**: change wording in lib/domain.js comment (Akhil Marsonya) [#37933](https://github.com/nodejs/node/pull/37933)
- [[`a61fd37786`](https://github.com/nodejs/node/commit/a61fd37786)] - **lib**: change wording in lib/internal/child_process comment (Akhil Marsonya) [#37903](https://github.com/nodejs/node/pull/37903)
- [[`13ac680cdb`](https://github.com/nodejs/node/commit/13ac680cdb)] - **lib**: fix typo in internal/modules/esm/module_job.js (marsonya) [#37773](https://github.com/nodejs/node/pull/37773)
- [[`eea4f3bf82`](https://github.com/nodejs/node/commit/eea4f3bf82)] - **lib**: fix typo in lib/internal/bootstrap/loaders.js (marsonya) [#37644](https://github.com/nodejs/node/pull/37644)
- [[`fe47563bed`](https://github.com/nodejs/node/commit/fe47563bed)] - **lib**: simplify check in child_process (Darshan Sen) [#37367](https://github.com/nodejs/node/pull/37367)
- [[`d78e2ed82c`](https://github.com/nodejs/node/commit/d78e2ed82c)] - **lib**: remove non used getter in `lib/perf\_hooks.js` (Juan José Arboleda) [#36907](https://github.com/nodejs/node/pull/36907)
- [[`08b69fb1e8`](https://github.com/nodejs/node/commit/08b69fb1e8)] - **lib**: fix diagnostics_channel hasSubscribers error (ZiJian Liu) [#36599](https://github.com/nodejs/node/pull/36599)
- [[`3ee0423dfa`](https://github.com/nodejs/node/commit/3ee0423dfa)] - **(SEMVER-MINOR)** **lib**: support BigInt in querystring.stringify (raisinten) [#36499](https://github.com/nodejs/node/pull/36499)
- [[`e71eed620a`](https://github.com/nodejs/node/commit/e71eed620a)] - **lib**: fix typo in internal/errors.js (raisinten) [#36426](https://github.com/nodejs/node/pull/36426)
- [[`be537fe877`](https://github.com/nodejs/node/commit/be537fe877)] - **lib**: remove primordials.SafePromise (Antoine du Hamel) [#36149](https://github.com/nodejs/node/pull/36149)
- [[`60ef53cc14`](https://github.com/nodejs/node/commit/60ef53cc14)] - **(SEMVER-MINOR)** **lib**: create diagnostics_channel module (Stephen Belanger) [#34895](https://github.com/nodejs/node/pull/34895)
- [[`b1507c41e7`](https://github.com/nodejs/node/commit/b1507c41e7)] - **lib**: add brand checks to AbortController and AbortSignal (Mattias Buelens) [#37720](https://github.com/nodejs/node/pull/37720)
- [[`448a6a22cf`](https://github.com/nodejs/node/commit/448a6a22cf)] - **(SEMVER-MINOR)** **lib**: implement AbortSignal.abort() (James M Snell) [#37693](https://github.com/nodejs/node/pull/37693)
- [[`4cd9f39066`](https://github.com/nodejs/node/commit/4cd9f39066)] - **lib**: set abort-controller toStringTag (Benjamin Gruenbaum) [#36115](https://github.com/nodejs/node/pull/36115)
- [[`b2f8e8dd28`](https://github.com/nodejs/node/commit/b2f8e8dd28)] - **lib**: let abort_controller target be EventTarget (Daijiro Wachi) [#35869](https://github.com/nodejs/node/pull/35869)
- [[`f30f9314c8`](https://github.com/nodejs/node/commit/f30f9314c8)] - **(SEMVER-MINOR)** **lib**: initial experimental AbortController implementation (James M Snell) [#33527](https://github.com/nodejs/node/pull/33527)
- [[`5e77bcd3dc`](https://github.com/nodejs/node/commit/5e77bcd3dc)] - **(SEMVER-MINOR)** **lib**: add throws option to fs.f/l/statSync (Andrew Casey) [#33716](https://github.com/nodejs/node/pull/33716)
- [[`e0df3bc751`](https://github.com/nodejs/node/commit/e0df3bc751)] - **meta**: notify slack when someone force pushes (Mary Marchini) [#35131](https://github.com/nodejs/node/pull/35131)
- [[`079671d3c1`](https://github.com/nodejs/node/commit/079671d3c1)] - **module**: improve error message for invalid data URL (Antoine du Hamel) [#37701](https://github.com/nodejs/node/pull/37701)
- [[`4cdd9b63b4`](https://github.com/nodejs/node/commit/4cdd9b63b4)] - **module**: make synthetic module evaluation steps return a Promise to support top level await (Daniel Clark) [#37300](https://github.com/nodejs/node/pull/37300)
- [[`2413907a12`](https://github.com/nodejs/node/commit/2413907a12)] - **(SEMVER-MINOR)** **module**: add isPreloading indicator (James M Snell) [#36263](https://github.com/nodejs/node/pull/36263)
- [[`fd08c37d98`](https://github.com/nodejs/node/commit/fd08c37d98)] - **(SEMVER-MINOR)** **net**: add support for resolving DNS CAA records (Danny Sonnenschein) [#35466](https://github.com/nodejs/node/pull/35466)
- [[`8413759d84`](https://github.com/nodejs/node/commit/8413759d84)] - **node-api**: fix crash in finalization (Michael Dawson) [#37876](https://github.com/nodejs/node/pull/37876)
- [[`6e018559db`](https://github.com/nodejs/node/commit/6e018559db)] - **node-api**: stop ref gc during environment teardown (Gabriel Schulhof) [#37616](https://github.com/nodejs/node/pull/37616)
- [[`b5b1ad39dd`](https://github.com/nodejs/node/commit/b5b1ad39dd)] - **node-api**: force env shutdown deferring behavior (Gabriel Schulhof) [#37303](https://github.com/nodejs/node/pull/37303)
- [[`36abc1802c`](https://github.com/nodejs/node/commit/36abc1802c)] - **(SEMVER-MINOR)** **node-api**: define version 8 (Gabriel Schulhof) [#37652](https://github.com/nodejs/node/pull/37652)
- [[`991251fbb2`](https://github.com/nodejs/node/commit/991251fbb2)] - **os**: performance improvement in vector allocation (Yash Ladha) [#36748](https://github.com/nodejs/node/pull/36748)
- [[`395b9a69a1`](https://github.com/nodejs/node/commit/395b9a69a1)] - **perf_hooks**: make nodeTiming a first-class object (Momtchil Momtchev) [#35977](https://github.com/nodejs/node/pull/35977)
- [[`506f1d4044`](https://github.com/nodejs/node/commit/506f1d4044)] - **policy**: fix cascade getting scope (Bradley Meck) [#37298](https://github.com/nodejs/node/pull/37298)
- [[`e9110d56d2`](https://github.com/nodejs/node/commit/e9110d56d2)] - **process**: do not lazily load AsyncResource (Michaël Zasso) [#38041](https://github.com/nodejs/node/pull/38041)
- [[`43057595e2`](https://github.com/nodejs/node/commit/43057595e2)] - **process**: passing -1 to setuid/setgid should not abort (James M Snell) [#36786](https://github.com/nodejs/node/pull/36786)
- [[`70cbe4a565`](https://github.com/nodejs/node/commit/70cbe4a565)] - **readline**: fix behaviour of Interface plugged to a non-terminal output (Antoine du Hamel) [#36774](https://github.com/nodejs/node/pull/36774)
- [[`ffaa1149e1`](https://github.com/nodejs/node/commit/ffaa1149e1)] - **(SEMVER-MINOR)** **readline**: add getPrompt to get the current prompt (Mattias Runge-Broberg) [#33675](https://github.com/nodejs/node/pull/33675)
- [[`8d4936da2c`](https://github.com/nodejs/node/commit/8d4936da2c)] - **repl**: fix error message printing (Anna Henningsen) [#38209](https://github.com/nodejs/node/pull/38209)
- [[`1ac07b2aee`](https://github.com/nodejs/node/commit/1ac07b2aee)] - **repl**: disable blocking completions by default (Anna Henningsen) [#36564](https://github.com/nodejs/node/pull/36564)
- [[`5ed770e1b7`](https://github.com/nodejs/node/commit/5ed770e1b7)] - **src**: cache some context in locals (Khaidi Chu) [#37473](https://github.com/nodejs/node/pull/37473)
- [[`ec4be2d7e1`](https://github.com/nodejs/node/commit/ec4be2d7e1)] - **src**: fix finalization crash (James M Snell) [#38250](https://github.com/nodejs/node/pull/38250)
- [[`854a2a9c8a`](https://github.com/nodejs/node/commit/854a2a9c8a)] - **src**: fix typo for initialization (Yash Ladha) [#37974](https://github.com/nodejs/node/pull/37974)
- [[`b0f8f8d637`](https://github.com/nodejs/node/commit/b0f8f8d637)] - **src**: fix typo in node_mutex (Tobias Nießen) [#38011](https://github.com/nodejs/node/pull/38011)
- [[`e4f614100f`](https://github.com/nodejs/node/commit/e4f614100f)] - **src**: document newer values for --unhandled-rejections flag (David Glasser) [#37899](https://github.com/nodejs/node/pull/37899)
- [[`d0cb129cdb`](https://github.com/nodejs/node/commit/d0cb129cdb)] - **src**: fix typo in src code guide (Tobias Nießen) [#37956](https://github.com/nodejs/node/pull/37956)
- [[`6aa1ed20fa`](https://github.com/nodejs/node/commit/6aa1ed20fa)] - **src**: report idle time correctly (Stephen Belanger) [#37868](https://github.com/nodejs/node/pull/37868)
- [[`eb0faa12df`](https://github.com/nodejs/node/commit/eb0faa12df)] - **src**: add .note.GNU-stack section (James Addison) [#37688](https://github.com/nodejs/node/pull/37688)
- [[`ec5d7b1ec0`](https://github.com/nodejs/node/commit/ec5d7b1ec0)] - **src**: fix variable name of OnCloseReceived callback (Tobias Nießen) [#37521](https://github.com/nodejs/node/pull/37521)
- [[`fc0d6e4008`](https://github.com/nodejs/node/commit/fc0d6e4008)] - **src**: add error formatting support (Gus Caplan) [#37598](https://github.com/nodejs/node/pull/37598)
- [[`512ae7e125`](https://github.com/nodejs/node/commit/512ae7e125)] - **src**: adjust THP sysfs config token retrieval and file closure (James Addison) [#37187](https://github.com/nodejs/node/pull/37187)
- [[`bf16d288e2`](https://github.com/nodejs/node/commit/bf16d288e2)] - **src**: fix return type of method in string_search.h (Darshan Sen) [#37167](https://github.com/nodejs/node/pull/37167)
- [[`f476c6dc0c`](https://github.com/nodejs/node/commit/f476c6dc0c)] - **src**: refactor v8 binding (Joyee Cheung) [#37112](https://github.com/nodejs/node/pull/37112)
- [[`33ebf5d9ef`](https://github.com/nodejs/node/commit/33ebf5d9ef)] - **src**: rename binding_data_name to type_name in BindingData (Joyee Cheung) [#37112](https://github.com/nodejs/node/pull/37112)
- [[`88d9676e74`](https://github.com/nodejs/node/commit/88d9676e74)] - **src**: use make_shared for safe allocation (Yash Ladha) [#37139](https://github.com/nodejs/node/pull/37139)
- [[`ec7e5bc786`](https://github.com/nodejs/node/commit/ec7e5bc786)] - **src**: fix warning in string_search.h (Darshan Sen) [#37146](https://github.com/nodejs/node/pull/37146)
- [[`00309eea27`](https://github.com/nodejs/node/commit/00309eea27)] - **src**: read exactly two tokens from Linux THP sysfs config (James Addison) [#37065](https://github.com/nodejs/node/pull/37065)
- [[`1ecdb66299`](https://github.com/nodejs/node/commit/1ecdb66299)] - **src**: expose BaseObject::kInternalFieldCount in post-mortem metadata (Joyee Cheung) [#37111](https://github.com/nodejs/node/pull/37111)
- [[`93517dce78`](https://github.com/nodejs/node/commit/93517dce78)] - **src**: replace push_back with emplace_back in debug_utils (raisinten) [#36897](https://github.com/nodejs/node/pull/36897)
- [[`dcba374604`](https://github.com/nodejs/node/commit/dcba374604)] - **src**: fix leading backslash bug in URL (raisinten) [#36613](https://github.com/nodejs/node/pull/36613)
- [[`c308b06483`](https://github.com/nodejs/node/commit/c308b06483)] - **src**: remove unnecessary ToLocalChecked node_errors (Daniel Bevenius) [#36547](https://github.com/nodejs/node/pull/36547)
- [[`e8f8c70911`](https://github.com/nodejs/node/commit/e8f8c70911)] - **src**: remove unnecessary ToLocalChecked call (Daniel Bevenius) [#36523](https://github.com/nodejs/node/pull/36523)
- [[`a356f32e66`](https://github.com/nodejs/node/commit/a356f32e66)] - **src**: remove empty name check in node_env_var.cc (raisinten) [#36133](https://github.com/nodejs/node/pull/36133)
- [[`308cb93304`](https://github.com/nodejs/node/commit/308cb93304)] - **src**: remove duplicate V macros in node_v8.cc (Daniel Bevenius) [#36454](https://github.com/nodejs/node/pull/36454)
- [[`6df1132c85`](https://github.com/nodejs/node/commit/6df1132c85)] - **src**: guard against env != null in node_errors.cc (Anna Henningsen) [#36414](https://github.com/nodejs/node/pull/36414)
- [[`3701e5d14f`](https://github.com/nodejs/node/commit/3701e5d14f)] - **src**: add typedef for CleanupHookCallback callback (Daniel Bevenius) [#36442](https://github.com/nodejs/node/pull/36442)
- [[`bd90752a7e`](https://github.com/nodejs/node/commit/bd90752a7e)] - **src**: fix indentation in memory_tracker-inl.h (Daniel Bevenius) [#36425](https://github.com/nodejs/node/pull/36425)
- [[`05bb3e14cf`](https://github.com/nodejs/node/commit/05bb3e14cf)] - **src**: remove identical V macro (Daniel Bevenius) [#36427](https://github.com/nodejs/node/pull/36427)
- [[`4b386e3dbe`](https://github.com/nodejs/node/commit/4b386e3dbe)] - **src**: add missing context scopes (Anna Henningsen) [#36413](https://github.com/nodejs/node/pull/36413)
- [[`685ed2c275`](https://github.com/nodejs/node/commit/685ed2c275)] - **src**: use ToLocal in DeserializeProperties (Daniel Bevenius) [#36279](https://github.com/nodejs/node/pull/36279)
- [[`d8cb34158b`](https://github.com/nodejs/node/commit/d8cb34158b)] - **src**: update node.rc file description (devsnek) [#36197](https://github.com/nodejs/node/pull/36197)
- [[`bacd432f34`](https://github.com/nodejs/node/commit/bacd432f34)] - **src**: move all base64.h inline methods into -inl.h header file (Anna Henningsen) [#35432](https://github.com/nodejs/node/pull/35432)
- [[`b1d5160828`](https://github.com/nodejs/node/commit/b1d5160828)] - **src**: guard against nullptr deref in TimerWrapHandle::Stop (Anna Henningsen) [#34460](https://github.com/nodejs/node/pull/34460)
- [[`cf2475ca09`](https://github.com/nodejs/node/commit/cf2475ca09)] - **src**: refactor TimerWrap lifetime management (Anna Henningsen) [#34252](https://github.com/nodejs/node/pull/34252)
- [[`b8bccc3132`](https://github.com/nodejs/node/commit/b8bccc3132)] - **src**: remove user_data from TimerWrap (Anna Henningsen) [#34252](https://github.com/nodejs/node/pull/34252)
- [[`8e1e3b563f`](https://github.com/nodejs/node/commit/8e1e3b563f)] - **src**: replace InspectorTimer with TimerWrap utility (James M Snell) [#34186](https://github.com/nodejs/node/pull/34186)
- [[`a3a4d2c283`](https://github.com/nodejs/node/commit/a3a4d2c283)] - **src**: add TimerWrap utility (James M Snell) [#34186](https://github.com/nodejs/node/pull/34186)
- [[`ae9bd89329`](https://github.com/nodejs/node/commit/ae9bd89329)] - **src**: perform bounds checking on error source line (Anna Henningsen) [#33645](https://github.com/nodejs/node/pull/33645)
- [[`44868010f7`](https://github.com/nodejs/node/commit/44868010f7)] - **(SEMVER-MINOR)** **src**: add loop idle time in diagnostic report (Gireesh Punathil) [#35940](https://github.com/nodejs/node/pull/35940)
- [[`4793f165dc`](https://github.com/nodejs/node/commit/4793f165dc)] - **stream**: fix pipe deadlock when starting with needDrain (Robert Nagy) [#36563](https://github.com/nodejs/node/pull/36563)
- [[`617f2dc0cf`](https://github.com/nodejs/node/commit/617f2dc0cf)] - **(SEMVER-MINOR)** **stream**: writableNeedDrain (Robert Nagy) [#35348](https://github.com/nodejs/node/pull/35348)
- [[`26e9c39cea`](https://github.com/nodejs/node/commit/26e9c39cea)] - **stream**: remove isPromise utility function (Antoine du Hamel) [#35925](https://github.com/nodejs/node/pull/35925)
- [[`7858de4f63`](https://github.com/nodejs/node/commit/7858de4f63)] - **stream,util**: fix "the the" typo in comments (Luigi Pinca) [#37674](https://github.com/nodejs/node/pull/37674)
- [[`93c917c1ce`](https://github.com/nodejs/node/commit/93c917c1ce)] - **test**: move buffer-as-path symlink test to its own test file (Rich Trott) [#34569](https://github.com/nodejs/node/pull/34569)
- [[`cd95bf3dd7`](https://github.com/nodejs/node/commit/cd95bf3dd7)] - **test**: change Fixes: to Refs: (Rich Trott) [#34568](https://github.com/nodejs/node/pull/34568)
- [[`2e81ded71e`](https://github.com/nodejs/node/commit/2e81ded71e)] - **test**: fix flaky test-dns and test-dns-lookup (Rich Trott) [#38282](https://github.com/nodejs/node/pull/38282)
- [[`ea1183c6ec`](https://github.com/nodejs/node/commit/ea1183c6ec)] - **test**: fixup failing test/internet/test-dns.js (James M Snell) [#38241](https://github.com/nodejs/node/pull/38241)
- [[`ae44e5f5b5`](https://github.com/nodejs/node/commit/ae44e5f5b5)] - **test**: add tests for missing https agent options (Rich Trott) [#38202](https://github.com/nodejs/node/pull/38202)
- [[`796007ba07`](https://github.com/nodejs/node/commit/796007ba07)] - **test**: fix test-https-agent-additional-options.js (Rich Trott) [#38202](https://github.com/nodejs/node/pull/38202)
- [[`a4275eec79`](https://github.com/nodejs/node/commit/a4275eec79)] - **test**: fix typo in comment in binding.c (Tobias Nießen) [#38220](https://github.com/nodejs/node/pull/38220)
- [[`95c7dabbb4`](https://github.com/nodejs/node/commit/95c7dabbb4)] - **test**: fix typo in gtest-all.cc (Ikko Ashimine) [#38224](https://github.com/nodejs/node/pull/38224)
- [[`605f830672`](https://github.com/nodejs/node/commit/605f830672)] - **test**: add undefined fatalException exit code test (Nitzan Uziely) [#38119](https://github.com/nodejs/node/pull/38119)
- [[`ce70ea8a85`](https://github.com/nodejs/node/commit/ce70ea8a85)] - **test**: skip fs.watch() test on IBMi (Rich Trott) [#38192](https://github.com/nodejs/node/pull/38192)
- [[`058abbece1`](https://github.com/nodejs/node/commit/058abbece1)] - **test**: skip test-vm-memleak in ASAN (Rich Trott) [#34289](https://github.com/nodejs/node/pull/34289)
- [[`c7981daf09`](https://github.com/nodejs/node/commit/c7981daf09)] - **test**: skip test-hash-seed on armv6 and armv7 (Rich Trott) [#34289](https://github.com/nodejs/node/pull/34289)
- [[`552c945c7c`](https://github.com/nodejs/node/commit/552c945c7c)] - **test**: remove unneeded m flag on regular expressions (Rich Trott) [#38124](https://github.com/nodejs/node/pull/38124)
- [[`e999da7004`](https://github.com/nodejs/node/commit/e999da7004)] - **test**: fix flaky test-zlib-unused-weak.js (Ouyang Yadong) [#38149](https://github.com/nodejs/node/pull/38149)
- [[`aa0d8146e9`](https://github.com/nodejs/node/commit/aa0d8146e9)] - **test**: add regression test for serdes readDouble() (Colin Ihrig) [#38121](https://github.com/nodejs/node/pull/38121)
- [[`dd8c9ad65b`](https://github.com/nodejs/node/commit/dd8c9ad65b)] - **test**: skip test-crypto-dh-keys on armv6 and armv7 (Rich Trott) [#38076](https://github.com/nodejs/node/pull/38076)
- [[`7d85617484`](https://github.com/nodejs/node/commit/7d85617484)] - **test**: fix skip message for test-macos-app-sandbox (Tobias Nießen) [#38114](https://github.com/nodejs/node/pull/38114)
- [[`f1aae43349`](https://github.com/nodejs/node/commit/f1aae43349)] - **test**: correct test comment (Evan Lucas) [#38095](https://github.com/nodejs/node/pull/38095)
- [[`d6ab9bf1ad`](https://github.com/nodejs/node/commit/d6ab9bf1ad)] - **test**: fix flaky test-net-timeout (Rich Trott) [#38060](https://github.com/nodejs/node/pull/38060)
- [[`1cb3d89bf1`](https://github.com/nodejs/node/commit/1cb3d89bf1)] - **test**: fix flaky timeout-delayed-body and headers tests (Nitzan Uziely) [#38045](https://github.com/nodejs/node/pull/38045)
- [[`8590720489`](https://github.com/nodejs/node/commit/8590720489)] - **test**: add extra space in test failure output (Qingyu Deng) [#37957](https://github.com/nodejs/node/pull/37957)
- [[`d6346e1486`](https://github.com/nodejs/node/commit/d6346e1486)] - **test**: deflake test-fs-read-optional-params (Luigi Pinca) [#37991](https://github.com/nodejs/node/pull/37991)
- [[`abec939c58`](https://github.com/nodejs/node/commit/abec939c58)] - **test**: improve clarity of ALS-enable-disable.js (Darkripper214) [#38008](https://github.com/nodejs/node/pull/38008)
- [[`2e3305d1b3`](https://github.com/nodejs/node/commit/2e3305d1b3)] - **test**: add DataView test case for v8 serdes (Rich Trott) [#37955](https://github.com/nodejs/node/pull/37955)
- [[`7b0e4e23f2`](https://github.com/nodejs/node/commit/7b0e4e23f2)] - **test**: fix typeof comparison (Rich Trott) [#37924](https://github.com/nodejs/node/pull/37924)
- [[`4c5eff91ef`](https://github.com/nodejs/node/commit/4c5eff91ef)] - **test**: increase wiggle room for memory in test-worker-resource-limits (Rich Trott) [#37901](https://github.com/nodejs/node/pull/37901)
- [[`939f5541fa`](https://github.com/nodejs/node/commit/939f5541fa)] - **test**: fix deprecation warning in test-doctool-html (Antoine du Hamel) [#37858](https://github.com/nodejs/node/pull/37858)
- [[`f5334881f2`](https://github.com/nodejs/node/commit/f5334881f2)] - **test**: fix ibmi skip message (Tobias Nießen) [#37821](https://github.com/nodejs/node/pull/37821)
- [[`cd71199cdb`](https://github.com/nodejs/node/commit/cd71199cdb)] - **test**: fix flaky test-vm-timeout-escape-promise-module-2 (Rich Trott) [#37842](https://github.com/nodejs/node/pull/37842)
- [[`b858e12fca`](https://github.com/nodejs/node/commit/b858e12fca)] - **test**: remove duplicated test for eventtarget (himself65) [#37853](https://github.com/nodejs/node/pull/37853)
- [[`e57e532d28`](https://github.com/nodejs/node/commit/e57e532d28)] - **test**: relax Y2K38 check in test-fs-utimes-y2K38 (Richard Lau) [#37825](https://github.com/nodejs/node/pull/37825)
- [[`08d32e18b7`](https://github.com/nodejs/node/commit/08d32e18b7)] - **test**: remove skip for fixed test-benchmark-fs (Rich Trott) [#37803](https://github.com/nodejs/node/pull/37803)
- [[`3f86dc1d9b`](https://github.com/nodejs/node/commit/3f86dc1d9b)] - **test**: improve test-arm-math-illegal-instruction (marsonya) [#37670](https://github.com/nodejs/node/pull/37670)
- [[`5c60087132`](https://github.com/nodejs/node/commit/5c60087132)] - **(SEMVER-MINOR)** **test**: app atob web platform tests (James M Snell) [#37529](https://github.com/nodejs/node/pull/37529)
- [[`02916edbdd`](https://github.com/nodejs/node/commit/02916edbdd)] - **test**: add known_issues test for #13683 (Rich Trott) [#37744](https://github.com/nodejs/node/pull/37744)
- [[`62292031a8`](https://github.com/nodejs/node/commit/62292031a8)] - **test**: fix test-fs-utimes on non-Y2K38 file systems (Rich Trott) [#37707](https://github.com/nodejs/node/pull/37707)
- [[`e29905441e`](https://github.com/nodejs/node/commit/e29905441e)] - **test**: remove unnecessary V8 flag (Antoine du Hamel) [#37671](https://github.com/nodejs/node/pull/37671)
- [[`27d4fedca3`](https://github.com/nodejs/node/commit/27d4fedca3)] - **test**: improve error reporting in test-child-process-pipe-dataflow (Rich Trott) [#37632](https://github.com/nodejs/node/pull/37632)
- [[`376fcc75c6`](https://github.com/nodejs/node/commit/376fcc75c6)] - **test**: remove FLAKY status for test-async-hooks-http-parser-destroy (Rich Trott) [#37636](https://github.com/nodejs/node/pull/37636)
- [[`a91a200be0`](https://github.com/nodejs/node/commit/a91a200be0)] - **test**: remove FLAKY status for fixed test (Rich Trott) [#37633](https://github.com/nodejs/node/pull/37633)
- [[`102d12f308`](https://github.com/nodejs/node/commit/102d12f308)] - **test**: clear flaky designation for test-stream-pipeline-http2 (Rich Trott) [#37631](https://github.com/nodejs/node/pull/37631)
- [[`ed30e52ee8`](https://github.com/nodejs/node/commit/ed30e52ee8)] - **test**: clear FLAKY designation for test-http2-pipe (Rich Trott) [#37631](https://github.com/nodejs/node/pull/37631)
- [[`df93cb47da`](https://github.com/nodejs/node/commit/df93cb47da)] - **test**: fix wasi/test-return-on-exit on 32-bit systems (Colin Ihrig) [#37615](https://github.com/nodejs/node/pull/37615)
- [[`097f638fde`](https://github.com/nodejs/node/commit/097f638fde)] - **test**: remove FLAKY status for test-http2-multistream-destroy-on-read-tls (Rich Trott) [#37533](https://github.com/nodejs/node/pull/37533)
- [[`0726192c94`](https://github.com/nodejs/node/commit/0726192c94)] - **test**: make status file names consistent (Rich Trott) [#37532](https://github.com/nodejs/node/pull/37532)
- [[`bcc4f1773c`](https://github.com/nodejs/node/commit/bcc4f1773c)] - **test**: remove FLAKY for test-http2-compat-client-upload-reject (Rich Trott) [#37462](https://github.com/nodejs/node/pull/37462)
- [[`d168cdea50`](https://github.com/nodejs/node/commit/d168cdea50)] - **test**: validate no debug info for http2 (Michael Dawson) [#37447](https://github.com/nodejs/node/pull/37447)
- [[`2f2f83fc4c`](https://github.com/nodejs/node/commit/2f2f83fc4c)] - **test**: remove FLAKY designation for test-http2-client-upload-reject (Rich Trott) [#37461](https://github.com/nodejs/node/pull/37461)
- [[`776ef11732`](https://github.com/nodejs/node/commit/776ef11732)] - **test**: clarify usage of tmpdir.refresh() (Darshan Sen) [#37383](https://github.com/nodejs/node/pull/37383)
- [[`8386b88c7f`](https://github.com/nodejs/node/commit/8386b88c7f)] - **test**: fix test-doctool-html (Antoine du Hamel) [#37397](https://github.com/nodejs/node/pull/37397)
- [[`03752c0412`](https://github.com/nodejs/node/commit/03752c0412)] - **test**: remove flaky designation for test-http2-large-file (Rich Trott) [#37156](https://github.com/nodejs/node/pull/37156)
- [[`db44b92c58`](https://github.com/nodejs/node/commit/db44b92c58)] - **test**: increase inspect coverage (Emil Sivervik) [#36755](https://github.com/nodejs/node/pull/36755)
- [[`21e7b021a0`](https://github.com/nodejs/node/commit/21e7b021a0)] - **test**: skip tests consistently in parallel.status (Rich Trott) [#37035](https://github.com/nodejs/node/pull/37035)
- [[`8f580df5ac`](https://github.com/nodejs/node/commit/8f580df5ac)] - **test**: increase read file abort coverage (Moshe vilner) [#36716](https://github.com/nodejs/node/pull/36716)
- [[`55a7b0c2e1`](https://github.com/nodejs/node/commit/55a7b0c2e1)] - **test**: increase coverage for assert/calltracker (ZiJian Liu) [#36728](https://github.com/nodejs/node/pull/36728)
- [[`ec7ee61af0`](https://github.com/nodejs/node/commit/ec7ee61af0)] - **test**: improve assertion message for test-vm-memleak (Rich Trott) [#37034](https://github.com/nodejs/node/pull/37034)
- [[`456fd758f3`](https://github.com/nodejs/node/commit/456fd758f3)] - **test**: process.nextTick for before exit (ttzztztz) [#37012](https://github.com/nodejs/node/pull/37012)
- [[`d99f1755d3`](https://github.com/nodejs/node/commit/d99f1755d3)] - **test**: log error in test-fs-realpath-pipe (Joyee Cheung) [#36996](https://github.com/nodejs/node/pull/36996)
- [[`0e1963c486`](https://github.com/nodejs/node/commit/0e1963c486)] - **test**: test mode passed as an options object in mkdir/mkdirSync (Darshan Sen) [#37008](https://github.com/nodejs/node/pull/37008)
- [[`5408f51684`](https://github.com/nodejs/node/commit/5408f51684)] - **test**: mark flaky tests on IBM i (Richard Lau) [#36986](https://github.com/nodejs/node/pull/36986)
- [[`e72b2b4639`](https://github.com/nodejs/node/commit/e72b2b4639)] - **test**: increase buffer list coverage (Emil Sivervik) [#36688](https://github.com/nodejs/node/pull/36688)
- [[`90122d87c9`](https://github.com/nodejs/node/commit/90122d87c9)] - **test**: fix warning in test_environment.cc (raisinten) [#36846](https://github.com/nodejs/node/pull/36846)
- [[`2cbd72e17d`](https://github.com/nodejs/node/commit/2cbd72e17d)] - **test**: skip internet for test-npm-install (Ruy Adorno) [#36933](https://github.com/nodejs/node/pull/36933)
- [[`2896219613`](https://github.com/nodejs/node/commit/2896219613)] - **test**: add coverage for breakLength one-column array (Rich Trott) [#36657](https://github.com/nodejs/node/pull/36657)
- [[`ff212f44b7`](https://github.com/nodejs/node/commit/ff212f44b7)] - **test**: increase coverage for diagnostics_channel (ZiJian Liu) [#36602](https://github.com/nodejs/node/pull/36602)
- [[`35c388ab1f`](https://github.com/nodejs/node/commit/35c388ab1f)] - **test**: increase coverage for internal/error_serdes.js (ZiJian Liu) [#36628](https://github.com/nodejs/node/pull/36628)
- [[`581a95f0ea`](https://github.com/nodejs/node/commit/581a95f0ea)] - **test**: improve coverage for util.inspect() with classes (Rich Trott) [#36625](https://github.com/nodejs/node/pull/36625)
- [[`e82a2e8ad5`](https://github.com/nodejs/node/commit/e82a2e8ad5)] - **test**: increase runInAsyncScope() coverage (Rich Trott) [#36624](https://github.com/nodejs/node/pull/36624)
- [[`064144db89`](https://github.com/nodejs/node/commit/064144db89)] - **test**: redirect stderr EnvironmentWithNoESMLoader (Daniel Bevenius) [#36548](https://github.com/nodejs/node/pull/36548)
- [[`cf8d025207`](https://github.com/nodejs/node/commit/cf8d025207)] - **test**: increase abort logic coverage (Moshe vilner) [#36586](https://github.com/nodejs/node/pull/36586)
- [[`eba2dc5330`](https://github.com/nodejs/node/commit/eba2dc5330)] - **test**: increase coverage for worker (ZiJian Liu) [#36491](https://github.com/nodejs/node/pull/36491)
- [[`5ef609af3e`](https://github.com/nodejs/node/commit/5ef609af3e)] - **test**: specify global object for globals (Rich Trott) [#36498](https://github.com/nodejs/node/pull/36498)
- [[`829213f624`](https://github.com/nodejs/node/commit/829213f624)] - **test**: increase coverage for fs/dir read (Zijian Liu) [#36388](https://github.com/nodejs/node/pull/36388)
- [[`c0604c9958`](https://github.com/nodejs/node/commit/c0604c9958)] - **test**: remove test-http2-client-upload as flaky (Rich Trott) [#36496](https://github.com/nodejs/node/pull/36496)
- [[`dabbd6d8ad`](https://github.com/nodejs/node/commit/dabbd6d8ad)] - **test**: make executable name more general (Shelley Vohr) [#36489](https://github.com/nodejs/node/pull/36489)
- [[`44603b7535`](https://github.com/nodejs/node/commit/44603b7535)] - **test**: increased externalized string length (Shelley Vohr) [#36451](https://github.com/nodejs/node/pull/36451)
- [[`2d0b6ca1c6`](https://github.com/nodejs/node/commit/2d0b6ca1c6)] - **test**: fix flaky test-repl (Rich Trott) [#36415](https://github.com/nodejs/node/pull/36415)
- [[`fee0389c12`](https://github.com/nodejs/node/commit/fee0389c12)] - **test**: check null proto-of-proto in util.inspect() (Rich Trott) [#36399](https://github.com/nodejs/node/pull/36399)
- [[`0e821ff337`](https://github.com/nodejs/node/commit/0e821ff337)] - **test**: fix child-process-pipe-dataflow (Santiago Gimeno) [#36366](https://github.com/nodejs/node/pull/36366)
- [[`736b575b7e`](https://github.com/nodejs/node/commit/736b575b7e)] - **test**: fix comment misspellings of transferred (Rich Trott) [#36360](https://github.com/nodejs/node/pull/36360)
- [[`5fc0f5eabb`](https://github.com/nodejs/node/commit/5fc0f5eabb)] - **test**: increase coverage for readline (Zijian Liu) [#36389](https://github.com/nodejs/node/pull/36389)
- [[`f5e8f12c1e`](https://github.com/nodejs/node/commit/f5e8f12c1e)] - **test**: fix typo in comment (inokawa) [#36312](https://github.com/nodejs/node/pull/36312)
- [[`72b3e5a6d9`](https://github.com/nodejs/node/commit/72b3e5a6d9)] - **test**: replace anonymous functions by arrows (Aleksandr Krutko) [#36125](https://github.com/nodejs/node/pull/36125)
- [[`b11e3ea1e6`](https://github.com/nodejs/node/commit/b11e3ea1e6)] - **test**: fix flaky sequential/test-fs-watch (Rich Trott) [#36249](https://github.com/nodejs/node/pull/36249)
- [[`cb530d2372`](https://github.com/nodejs/node/commit/cb530d2372)] - **test**: increase coverage for util.inspect() (Rich Trott) [#36228](https://github.com/nodejs/node/pull/36228)
- [[`94a877b0a4`](https://github.com/nodejs/node/commit/94a877b0a4)] - **test**: improve test coverage SourceMap API (Juan José Arboleda) [#36089](https://github.com/nodejs/node/pull/36089)
- [[`6c62e15c36`](https://github.com/nodejs/node/commit/6c62e15c36)] - **test**: move test-worker-eventlooputil to sequential (Rich Trott) [#35996](https://github.com/nodejs/node/pull/35996)
- [[`16f6f1a0c2`](https://github.com/nodejs/node/commit/16f6f1a0c2)] - **test**: add missing test coverage for setLocalAddress() (Rich Trott) [#36039](https://github.com/nodejs/node/pull/36039)
- [[`23835012e0`](https://github.com/nodejs/node/commit/23835012e0)] - **test**: fix races in test-performance-eventlooputil (Gerhard Stoebich) [#36028](https://github.com/nodejs/node/pull/36028)
- [[`d63747de5a`](https://github.com/nodejs/node/commit/d63747de5a)] - **test**: fix error in test/internet/test-dns.js (Rich Trott) [#35969](https://github.com/nodejs/node/pull/35969)
- [[`00b9d955e9`](https://github.com/nodejs/node/commit/00b9d955e9)] - **test**: run test-benchmark-napi on arm (Rich Trott) [#34502](https://github.com/nodejs/node/pull/34502)
- [[`de654bf5b4`](https://github.com/nodejs/node/commit/de654bf5b4)] - **test**: fix unreliable test-fs-write-file.js (Rich Trott) [#36102](https://github.com/nodejs/node/pull/36102)
- [[`397d9372c2`](https://github.com/nodejs/node/commit/397d9372c2)] - **(SEMVER-MINOR)** **test**: update dom/abort tests (James M Snell) [#37693](https://github.com/nodejs/node/pull/37693)
- [[`dc63ca686e`](https://github.com/nodejs/node/commit/dc63ca686e)] - **test**: increase execFile abort coverage (Moshe vilner) [#36429](https://github.com/nodejs/node/pull/36429)
- [[`88f42617dd`](https://github.com/nodejs/node/commit/88f42617dd)] - **test**: integrate abort_controller tests from wpt (Daijiro Wachi) [#35869](https://github.com/nodejs/node/pull/35869)
- [[`cd1da67295`](https://github.com/nodejs/node/commit/cd1da67295)] - **test**: fix flaky test-http2-respond-file-error-pipe-offset (Rich Trott) [#36305](https://github.com/nodejs/node/pull/36305)
- [[`db04ae6b02`](https://github.com/nodejs/node/commit/db04ae6b02)] - **test**: add SIGTRAP to test-signal-handler (Ash Cripps) [#36368](https://github.com/nodejs/node/pull/36368)
- [[`48e3f592a0`](https://github.com/nodejs/node/commit/48e3f592a0)] - **test**: refactor coverage logic (Benjamin Coe) [#35767](https://github.com/nodejs/node/pull/35767)
- [[`01ba1c9a9c`](https://github.com/nodejs/node/commit/01ba1c9a9c)] - **test**: correct test-worker-eventlooputil (Gerhard Stoebich) [#35891](https://github.com/nodejs/node/pull/35891)
- [[`89046d7840`](https://github.com/nodejs/node/commit/89046d7840)] - **test**: add cpu-profiler-crash test (Santiago Gimeno) [#37293](https://github.com/nodejs/node/pull/37293)
- [[`6961be5ed1`](https://github.com/nodejs/node/commit/6961be5ed1)] - **test**: add arm64 arch to test-worker-prof status (Daniel Bevenius) [#37225](https://github.com/nodejs/node/pull/37225)
- [[`98f08c06e4`](https://github.com/nodejs/node/commit/98f08c06e4)] - **test,benchmark**: stop requiring URL and URLSearchParams (raisinten) [#36927](https://github.com/nodejs/node/pull/36927)
- [[`51ef745975`](https://github.com/nodejs/node/commit/51ef745975)] - **test,child_process**: add check for `subProcess.pid` (dr-js) [#37014](https://github.com/nodejs/node/pull/37014)
- [[`8476537aa4`](https://github.com/nodejs/node/commit/8476537aa4)] - **test,http**: check that http server is robust from handler abuse (Rich Trott) [#37958](https://github.com/nodejs/node/pull/37958)
- [[`e4d10426c4`](https://github.com/nodejs/node/commit/e4d10426c4)] - **timers**: fix arbitrary object clearImmediate errors (Nitzan Uziely) [#37824](https://github.com/nodejs/node/pull/37824)
- [[`1b74a08eba`](https://github.com/nodejs/node/commit/1b74a08eba)] - **timers**: refactor to use validateAbortSignal (ZiJian Liu) [#36604](https://github.com/nodejs/node/pull/36604)
- [[`4b04bb87f6`](https://github.com/nodejs/node/commit/4b04bb87f6)] - **timers**: use AbortController with correct name/message (Anna Henningsen) [#34763](https://github.com/nodejs/node/pull/34763)
- [[`9660a78278`](https://github.com/nodejs/node/commit/9660a78278)] - **(SEMVER-MINOR)** **timers**: move promisified timers implementations (James M Snell) [#33950](https://github.com/nodejs/node/pull/33950)
- [[`59a8425d04`](https://github.com/nodejs/node/commit/59a8425d04)] - **timers**: fix multipleResolves in promisified timeouts/immediates (Denys Otrishko) [#33949](https://github.com/nodejs/node/pull/33949)
- [[`84b2863f00`](https://github.com/nodejs/node/commit/84b2863f00)] - **(SEMVER-MINOR)** **timers**: allow promisified timeouts/immediates to be canceled (James M Snell) [#33833](https://github.com/nodejs/node/pull/33833)
- [[`08ed2337ea`](https://github.com/nodejs/node/commit/08ed2337ea)] - **tls**: forward new SecureContext options (Alba Mendez) [#36416](https://github.com/nodejs/node/pull/36416)
- [[`2c49953fc9`](https://github.com/nodejs/node/commit/2c49953fc9)] - **tools**: update glob-parent to 5.1.2 (Rich Trott) [#37646](https://github.com/nodejs/node/pull/37646)
- [[`b76aa10aa8`](https://github.com/nodejs/node/commit/b76aa10aa8)] - **tools**: update remark-preset-lint-node to 2.1.1 (Rich Trott) [#37604](https://github.com/nodejs/node/pull/37604)
- [[`5afaeafabe`](https://github.com/nodejs/node/commit/5afaeafabe)] - **tools**: bump remark-present-lint-node from 2.0.0 to 2.0.1 (Rich Trott) [#37270](https://github.com/nodejs/node/pull/37270)
- [[`abd45d3355`](https://github.com/nodejs/node/commit/abd45d3355)] - **tools**: update all lint-md rollup dependencies (Michaël Zasso) [#36843](https://github.com/nodejs/node/pull/36843)
- [[`c8f77f2e1d`](https://github.com/nodejs/node/commit/c8f77f2e1d)] - **tools**: update ini in tools/node-lint-md-cli-rollup (Myles Borins) [#36474](https://github.com/nodejs/node/pull/36474)
- [[`ac70a59056`](https://github.com/nodejs/node/commit/ac70a59056)] - **tools**: bump remark-lint-preset-node to 2.0.0 (Rich Trott) [#35905](https://github.com/nodejs/node/pull/35905)
- [[`cbb0a458b5`](https://github.com/nodejs/node/commit/cbb0a458b5)] - **tools**: bump remark-lint-preset-node to 1.17.1 (Rich Trott) [#35668](https://github.com/nodejs/node/pull/35668)
- [[`ed7c0d7c1b`](https://github.com/nodejs/node/commit/ed7c0d7c1b)] - **tools**: skip macOS GitHub Actions test on doc-only changes (Rich Trott) [#38296](https://github.com/nodejs/node/pull/38296)
- [[`4254315ebe`](https://github.com/nodejs/node/commit/4254315ebe)] - **tools**: improve valid-typeof lint rule (Rich Trott) [#37924](https://github.com/nodejs/node/pull/37924)
- [[`f055faf647`](https://github.com/nodejs/node/commit/f055faf647)] - **tools**: improve macos-firewall.sh output (Rich Trott) [#37846](https://github.com/nodejs/node/pull/37846)
- [[`2551bb1a61`](https://github.com/nodejs/node/commit/2551bb1a61)] - **tools**: make genv8constants.py Python3-compatible (Michaël Zasso) [#37835](https://github.com/nodejs/node/pull/37835)
- [[`e6a79802d3`](https://github.com/nodejs/node/commit/e6a79802d3)] - **tools**: update gitignore for CMake (Jiawen Geng) [#37793](https://github.com/nodejs/node/pull/37793)
- [[`e4975d9057`](https://github.com/nodejs/node/commit/e4975d9057)] - **tools**: fix object name in prefer-assert-methods.js (Tobias Nießen) [#37544](https://github.com/nodejs/node/pull/37544)
- [[`77eb45aad4`](https://github.com/nodejs/node/commit/77eb45aad4)] - **tools**: fix compiler warning in inspector_protocol (Darshan Sen) [#37573](https://github.com/nodejs/node/pull/37573)
- [[`af8b3852a8`](https://github.com/nodejs/node/commit/af8b3852a8)] - **tools**: fix lint-pr-url message (Antoine du Hamel) [#37304](https://github.com/nodejs/node/pull/37304)
- [[`2ba6db3c3e`](https://github.com/nodejs/node/commit/2ba6db3c3e)] - **tools**: avoid pending deprecation in doc generator (Michaël Zasso) [#37267](https://github.com/nodejs/node/pull/37267)
- [[`981659c7b9`](https://github.com/nodejs/node/commit/981659c7b9)] - **tools**: add GitHub Action linter for pr-url (Antoine du Hamel) [#37221](https://github.com/nodejs/node/pull/37221)
- [[`2e5994dcd8`](https://github.com/nodejs/node/commit/2e5994dcd8)] - **tools**: remove commented code from stability.js (Colin Ihrig) [#37092](https://github.com/nodejs/node/pull/37092)
- [[`ef1aab1239`](https://github.com/nodejs/node/commit/ef1aab1239)] - **tools**: add support for top-level await syntax in linter (Antoine du Hamel) [#36911](https://github.com/nodejs/node/pull/36911)
- [[`aef769745b`](https://github.com/nodejs/node/commit/aef769745b)] - **tools**: update doc tool dependencies (Michaël Zasso) [#36844](https://github.com/nodejs/node/pull/36844)
- [[`dd10afc45a`](https://github.com/nodejs/node/commit/dd10afc45a)] - **tools**: revise install.py for minor improvements (Rich Trott) [#36626](https://github.com/nodejs/node/pull/36626)
- [[`d85ea61c7e`](https://github.com/nodejs/node/commit/d85ea61c7e)] - **tools**: correct usage message for genv8constants.py (Rich Trott) [#36606](https://github.com/nodejs/node/pull/36606)
- [[`9990ec7423`](https://github.com/nodejs/node/commit/9990ec7423)] - **tools**: call close() explicitly in genv8constants.py (Rich Trott) [#36606](https://github.com/nodejs/node/pull/36606)
- [[`26d5965c77`](https://github.com/nodejs/node/commit/26d5965c77)] - **tools**: use `is None` consistently in Python (Rich Trott) [#36606](https://github.com/nodejs/node/pull/36606)
- [[`bb07a767c0`](https://github.com/nodejs/node/commit/bb07a767c0)] - **tools**: revise line in configure.py for clarity (Rich Trott) [#36551](https://github.com/nodejs/node/pull/36551)
- [[`5ac21bceba`](https://github.com/nodejs/node/commit/5ac21bceba)] - **tools**: fix make-v8.sh (Richard Lau) [#36594](https://github.com/nodejs/node/pull/36594)
- [[`3cac041b97`](https://github.com/nodejs/node/commit/3cac041b97)] - **tools**: fix release script sign function (Antoine du Hamel) [#36556](https://github.com/nodejs/node/pull/36556)
- [[`b398e4044c`](https://github.com/nodejs/node/commit/b398e4044c)] - **tools**: fix update-eslint.sh (Yongsheng Zhang) [#36579](https://github.com/nodejs/node/pull/36579)
- [[`471da7dc88`](https://github.com/nodejs/node/commit/471da7dc88)] - **tools**: fix release script (Antoine du Hamel) [#36540](https://github.com/nodejs/node/pull/36540)
- [[`1f3c129f05`](https://github.com/nodejs/node/commit/1f3c129f05)] - **tools**: remove unused variable in configure.py (Rich Trott) [#36525](https://github.com/nodejs/node/pull/36525)
- [[`9b13ddc6a6`](https://github.com/nodejs/node/commit/9b13ddc6a6)] - **tools**: lint shell scripts (Antoine du Hamel) [#36099](https://github.com/nodejs/node/pull/36099)
- [[`7ac8ab8e26`](https://github.com/nodejs/node/commit/7ac8ab8e26)] - **tools**: update doc tool dependencies (Michaël Zasso) [#36407](https://github.com/nodejs/node/pull/36407)
- [[`e8b2c776f7`](https://github.com/nodejs/node/commit/e8b2c776f7)] - **tools**: upgrade to @babel/eslint-parser 7.12.1 (Antoine du Hamel) [#36321](https://github.com/nodejs/node/pull/36321)
- [[`a0339101ba`](https://github.com/nodejs/node/commit/a0339101ba)] - **tools**: remove bashisms from macOS release scripts (Antoine du Hamel) [#36121](https://github.com/nodejs/node/pull/36121)
- [[`9434bb3cfd`](https://github.com/nodejs/node/commit/9434bb3cfd)] - **tools**: remove bashisms from release script (Antoine du Hamel) [#36123](https://github.com/nodejs/node/pull/36123)
- [[`2183a2be51`](https://github.com/nodejs/node/commit/2183a2be51)] - **tools**: update stability index linking logic (Rich Trott) [#36280](https://github.com/nodejs/node/pull/36280)
- [[`2677fe9dcb`](https://github.com/nodejs/node/commit/2677fe9dcb)] - **tools**: update highlight.js to 10.1.2 (Myles Borins) [#36309](https://github.com/nodejs/node/pull/36309)
- [[`17f942ffc0`](https://github.com/nodejs/node/commit/17f942ffc0)] - **tools**: fix undeclared identifier FALSE (Antoine du Hamel) [#36276](https://github.com/nodejs/node/pull/36276)
- [[`4bb5c10915`](https://github.com/nodejs/node/commit/4bb5c10915)] - **tools**: cleanup old ICU version-specific fixes (Michaël Zasso) [#36980](https://github.com/nodejs/node/pull/36980)
- [[`77f32ee8f7`](https://github.com/nodejs/node/commit/77f32ee8f7)] - **tools**: fix md5 hash for ICU 68.1 src (Richard Lau) [#36777](https://github.com/nodejs/node/pull/36777)
- [[`5d29781491`](https://github.com/nodejs/node/commit/5d29781491)] - **tools**: add msvc /P output to .gitignore (Jiawen Geng) [#35735](https://github.com/nodejs/node/pull/35735)
- [[`571afd3c30`](https://github.com/nodejs/node/commit/571afd3c30)] - **tools,doc**: add "legacy" badge in the TOC (Antoine du Hamel) [#37949](https://github.com/nodejs/node/pull/37949)
- [[`15d66fbc0c`](https://github.com/nodejs/node/commit/15d66fbc0c)] - **tools,doc**: list the stability status of each API (Zijian Liu) [#36223](https://github.com/nodejs/node/pull/36223)
- [[`d89d55ab36`](https://github.com/nodejs/node/commit/d89d55ab36)] - **tty**: validate file descriptor to avoid int32 overflow (Antoine du Hamel) [#37809](https://github.com/nodejs/node/pull/37809)
- [[`face4b86dd`](https://github.com/nodejs/node/commit/face4b86dd)] - **typings**: add JSDoc to os module functions (David Brownman) [#38197](https://github.com/nodejs/node/pull/38197)
- [[`599434a61d`](https://github.com/nodejs/node/commit/599434a61d)] - **typings**: add JSDoc Types to lib/querystring (Simon Knott) [#38185](https://github.com/nodejs/node/pull/38185)
- [[`60c7591af2`](https://github.com/nodejs/node/commit/60c7591af2)] - **typings**: add JSDoc typings for http (Voltrex) [#38191](https://github.com/nodejs/node/pull/38191)
- [[`530e69e145`](https://github.com/nodejs/node/commit/530e69e145)] - **typings**: add JSDoc typings for assert (Voltrex) [#38188](https://github.com/nodejs/node/pull/38188)
- [[`e2c2f2b7a5`](https://github.com/nodejs/node/commit/e2c2f2b7a5)] - **typings**: add JSDoc types to lib/path (Simon Knott) [#38186](https://github.com/nodejs/node/pull/38186)
- [[`d13fc6ed68`](https://github.com/nodejs/node/commit/d13fc6ed68)] - **url**: align url format behavior with browsers (ZiJian Liu) [#36903](https://github.com/nodejs/node/pull/36903)
- [[`2aff77f358`](https://github.com/nodejs/node/commit/2aff77f358)] - **url**: fix url.format with ipv6 hostname (ZiJian Liu) [#36665](https://github.com/nodejs/node/pull/36665)
- [[`08a6d9effd`](https://github.com/nodejs/node/commit/08a6d9effd)] - **(SEMVER-MINOR)** **util**: add getSystemErrorMap() impl (eladkeyshawn) [#38101](https://github.com/nodejs/node/pull/38101)
- [[`e6c64bf0c7`](https://github.com/nodejs/node/commit/e6c64bf0c7)] - **util**: remove unreachable inspect code (Rich Trott) [#37941](https://github.com/nodejs/node/pull/37941)
- [[`b5c5bd1f51`](https://github.com/nodejs/node/commit/b5c5bd1f51)] - **util**: inspect \_\_proto\_\_ key as written in object literal (Anna Henningsen) [#37713](https://github.com/nodejs/node/pull/37713)
- [[`2bfe185c3f`](https://github.com/nodejs/node/commit/2bfe185c3f)] - **util**: use assert for unreachable code (Rich Trott) [#37249](https://github.com/nodejs/node/pull/37249)
- [[`ba4788dd9f`](https://github.com/nodejs/node/commit/ba4788dd9f)] - **(SEMVER-MINOR)** **v8**: fix native `serdes` constructors (ExE Boss) [#36549](https://github.com/nodejs/node/pull/36549)
- [[`16606d05b7`](https://github.com/nodejs/node/commit/16606d05b7)] - **vm**: add `SafeForTerminationScope`s for SIGINT interruptions (Anna Henningsen) [#36344](https://github.com/nodejs/node/pull/36344)
- [[`b6f4d790d3`](https://github.com/nodejs/node/commit/b6f4d790d3)] - **worker**: fix exit code for error thrown in handler (Nitzan Uziely) [#38012](https://github.com/nodejs/node/pull/38012)
- [[`9460f2cd83`](https://github.com/nodejs/node/commit/9460f2cd83)] - **(SEMVER-MINOR)** **worker**: add eventLoopUtilization() (Trevor Norris) [#35664](https://github.com/nodejs/node/pull/35664)
- [[`78ad8b4c44`](https://github.com/nodejs/node/commit/78ad8b4c44)] - **workers**: fix spawning from preload scripts (James M Snell) [#37481](https://github.com/nodejs/node/pull/37481)

Windows 32-bit Installer: https://nodejs.org/dist/v14.17.0/node-v14.17.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v14.17.0/node-v14.17.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v14.17.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v14.17.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v14.17.0/node-v14.17.0.pkg \
macOS Intel 64-bit Binary: https://nodejs.org/dist/v14.17.0/node-v14.17.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v14.17.0/node-v14.17.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v14.17.0/node-v14.17.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v14.17.0/node-v14.17.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v14.17.0/node-v14.17.0-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v14.17.0/node-v14.17.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v14.17.0/node-v14.17.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v14.17.0/node-v14.17.0.tar.gz \
Other release files: https://nodejs.org/dist/v14.17.0/ \
Documentation: https://nodejs.org/docs/v14.17.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

fb33407ea6518e5a839875d33a10948c01e36bbdba968831c861a2f5c1d5c3c2  node-v14.17.0-aix-ppc64.tar.gz
7b210652e11d1ee25650c164cf32381895e1dcb3e0ff1d0841d8abc1f47ac73e  node-v14.17.0-darwin-x64.tar.gz
5866a8b115a2b9deee5bf4114aa9d81f0f6077ca5d4258b7b07fb334e5ec8d03  node-v14.17.0-darwin-x64.tar.xz
cc143d20f827a9a307ee8c1a9c5c403d3d254690bf9329094cf679064990e456  node-v14.17.0-headers.tar.gz
3ac387f9ffa78cc845f38a1bf620a70a2e25fbd385093153eb88acdd7302298d  node-v14.17.0-headers.tar.xz
9d5948d4397815ce7a746618338f79ce5e7e91efec9c165140ba62fd6c17c07a  node-v14.17.0-linux-arm64.tar.gz
712e5575cee20570a0a56f4d4b4572cb0f2ee2f4bce49433de18be0393e7df22  node-v14.17.0-linux-arm64.tar.xz
66d629b911279d9223fb7c2afa153fe42c84998164d54ec6be42a60aa40705ca  node-v14.17.0-linux-armv7l.tar.gz
9f96c567e32acc1a9055e2881fdb7b8f42b909b87ada91ebc6b2f1acd6d451da  node-v14.17.0-linux-armv7l.tar.xz
7fdbc70cb3ef04886fb70c303c3b42efc572a9101b46a2b8377e8db005341213  node-v14.17.0-linux-ppc64le.tar.gz
b20a9d557b513a375d348ca84b8341e08c6d8d5936c0e1527160ecaa11794fab  node-v14.17.0-linux-ppc64le.tar.xz
6aacb480a8ba34af3de0e410ea6a3fd445b3945274da81a1f7551ca22f3d4afc  node-v14.17.0-linux-s390x.tar.gz
6419372b9e9ad37e0bce188dc5740f2f060aaa44454418e462b4088a310a1c0b  node-v14.17.0-linux-s390x.tar.xz
3d06eabc73ec8626337bff370474306eac1c3c21122f677720d154c556ceafaf  node-v14.17.0-linux-x64.tar.gz
494b161759a3d19c70e3172d33ce1918dd8df9ad20d29d1652a8387a84e2d308  node-v14.17.0-linux-x64.tar.xz
bce130481d66fe9e13cc7e04dd18963f0d78148347b8232ae7533eb09418477f  node-v14.17.0.pkg
6114e82d3256136dc85a509d835442fbdf2f8430dcd8bfa7c304097344d06fb7  node-v14.17.0.tar.gz
56e05bff9331039317db417f772e635e0cd1c55f733f7b1b079d71ab5842c9ed  node-v14.17.0.tar.xz
bfd45ff5065def2aa85e5da3c7e88035078c956286d9ea0220752ad98d6d28de  node-v14.17.0-win-x64.7z
6582a7259c433e9f667dcc4ed3e5d68bc514caba2eed40e4626c8b4c7e5ecd5c  node-v14.17.0-win-x64.zip
ce5f166bab925f9c6ae99c5f8f7b105bd98e20b3554a9016bf3dcb3c2746fa2d  node-v14.17.0-win-x86.7z
952f4fb7ed9e3e3a5eb5a53a94f78f3dd2d6ee809e61056fcbf39285ca0066d4  node-v14.17.0-win-x86.zip
6d4c7457a210b867ca5b1c4a4465b4065ae6708dfd917383fe61f6860dba2128  node-v14.17.0-x64.msi
c3d7bb267ead2548746092a7e31fb59cb7828d1fe9f97d9628ed6ea4977b4c35  node-v14.17.0-x86.msi
8ac595f56ab1db228db115ce69798b31151cbf169b889330ca0c5f10bd3b8839  win-x64/node.exe
f399504245cb54ce8717408fedb10417a0066f1532c37511bc32a4380a56da7b  win-x64/node.lib
10d06d2eed7f486316ba90b7e5f5d4e6c6def64fd29147685f98bdfaca11f992  win-x64/node_pdb.7z
070da7f1cafbd85e0a8008cd058e13b9f111f3858716094f74e5bcaf936889f0  win-x64/node_pdb.zip
e1afb960b95a31c4f1016718ea1d17f4d31a5c9fcc8474575dad41b7afecea1a  win-x86/node.exe
ccfea07933b62822ee8d1572b9e80bbfbbf664da7b4a18184c152ea98e6c32ae  win-x86/node.lib
8c266b2d93bd08690ed219ee3c9ad4fcd6c81a35ba9c803de9f27fc948f62766  win-x86/node_pdb.7z
f54d9181dac21835e00a64d8b530243c3a5db26c3267bb0b09744a94e515dc9a  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEdPEmArbxxOkT+qN606iWE2Q7YgEFAmCbD5kACgkQ06iWE2Q7
YgGHkhAAmAO+IHG8kKPyaBoKD9PpHnvdCzNIRFHgzeVNiO+uIYJD3h6qdGncUwd9
cxh303gy2VBUxXb6baKneNLOLF10hs6ETeemUWXUrfxDiNhC1iqc3izhojyBoSEv
6h/RpbPgSLDCKTH2unDa7FDW4niIeou7WCQNo/Y+7FDIXNBVZKxoByv03TSAjugU
JJROwUBTgGIEV7X4m1aF4YTii8sklNOU3UPLy6FuKX6I7Cf//AXuZYiiD8PN5/rL
gYOe++tExlj5Q3veESf5k6GAnMPtMkgVI51pj+de1JWhWNuAHzdJUhgmLvoXF4Pn
ILCRi8PuBsSD15CixodskeYoga+SP+MDAmOt23Qn5fNx5zAyADAk/cq2OpI7zFKH
eIcM4eBc6fbdZgRs9BMp55Mq2MVlgmcR10Z8RWMPT8251bK9m0nXhx73xUKuUg4O
LBvRn5RkUsFdh6WLod+F4udsp+wljKt5rbVsLPT9fH/QA9lbUcg4i5FoLjxwa9eP
Y2UscqyPN5rMhmMzmkpagKmRhWwPnLV497u6QdY1qefWqi0RoTeehuxX2JMCbAK5
Ehgtyns+NsY9Oqu2W192eonoUx73N6S+6OjK8enhtOZrROAGF8aioTtKcbo1qFZo
BZWMpsQ6UaCMSGP4+bkIo6WJgWxfcqRybsm3dJPCIy4OuWyZgko=
=7SvM
-----END PGP SIGNATURE-----

```
