---
date: '2020-04-08T16:11:48.600Z'
category: release
title: Node v10.20.0 (LTS)
layout: blog-post
author: <PERSON>
---

### macOS package notarization and a change in builder configuration

The macOS binaries for this release, and future 10.x releases, are now
being compiled on macOS 10.15 (Catalina) with Xcode 11 to support
package notarization, a requirement for installing .pkg files on macOS
10.15 and later. Previous builds of Node.js 10.x were compiled on macOS
10.10 (Yosemite) with a minimum deployment target of macOS 10.7 (Lion).
As binaries are still being compiled to support a minimum of macOS 10.7
(Lion) we do not anticipate this having a negative impact on Node.js
10.x users with older versions of macOS.

### Notable changes

- **buffer**: add {read|write}Big\[U\]Int64{BE|LE} methods (garygsc) [#19691](https://github.com/nodejs/node/pull/19691)
- **build**: macOS package notarization (<PERSON>) [#31459](https://github.com/nodejs/node/pull/31459)
- **deps**:
  - update npm to 6.14.3 (<PERSON><PERSON>) [#32368](https://github.com/nodejs/node/pull/32368)
  - upgrade openssl sources to 1.1.1e (Hassaan Pasha) [#32328](https://github.com/nodejs/node/pull/32328)
  - upgrade to libuv 1.34.2 (cjihrig) [#31477](https://github.com/nodejs/node/pull/31477)
- **n-api**:
  - add napi_get_all_property_names (himself65) [#30006](https://github.com/nodejs/node/pull/30006)
  - add APIs for per-instance state management (Gabriel Schulhof) [#28682](https://github.com/nodejs/node/pull/28682)
  - define release 6 [#32058](https://github.com/nodejs/node/pull/32058)
  - turn NAPI_CALL_INTO_MODULE into a function (Anna Henningsen) [#26128](https://github.com/nodejs/node/pull/26128)
- **tls**:
  - expose keylog event on TLSSocket (Alba Mendez) [#27654](https://github.com/nodejs/node/pull/27654)
  - support TLS min/max protocol defaults in CLI (Sam Roberts) [#27946](https://github.com/nodejs/node/pull/27946)
- **url**: handle quasi-WHATWG URLs in urlToOptions() (cjihrig) [#26226](https://github.com/nodejs/node/pull/26226)

### Commits

- [[`64744a282e`](https://github.com/nodejs/node/commit/64744a282e)] - **(SEMVER-MINOR)** **buffer**: add {read|write}Big\[U\]Int64{BE|LE} methods (garygsc) [#19691](https://github.com/nodejs/node/pull/19691)
- [[`8a0ed8f1ff`](https://github.com/nodejs/node/commit/8a0ed8f1ff)] - **build**: macOS package notarization (Rod Vagg) [#31459](https://github.com/nodejs/node/pull/31459)
- [[`42af3b861a`](https://github.com/nodejs/node/commit/42af3b861a)] - **build,win**: fix goto exit in vcbuild (João Reis) [#30931](https://github.com/nodejs/node/pull/30931)
- [[`b164a2e3bf`](https://github.com/nodejs/node/commit/b164a2e3bf)] - **console**: add trace-events for time and count (James M Snell) [#23703](https://github.com/nodejs/node/pull/23703)
- [[`04cd67f85e`](https://github.com/nodejs/node/commit/04cd67f85e)] - **deps**: upgrade npm to 6.14.4 (Ruy Adorno) [#32495](https://github.com/nodejs/node/pull/32495)
- [[`8d85a43d99`](https://github.com/nodejs/node/commit/8d85a43d99)] - **deps**: update term-size with signed version (Rod Vagg) [#31459](https://github.com/nodejs/node/pull/31459)
- [[`76033c5495`](https://github.com/nodejs/node/commit/76033c5495)] - **deps**: update archs files for OpenSSL-1.1.1e (Hassaan Pasha) [#32328](https://github.com/nodejs/node/pull/32328)
- [[`64c184836b`](https://github.com/nodejs/node/commit/64c184836b)] - **deps**: adjust openssl configuration for 1.1.1e (Hassaan Pasha) [#32328](https://github.com/nodejs/node/pull/32328)
- [[`c8f5ab2089`](https://github.com/nodejs/node/commit/c8f5ab2089)] - **deps**: upgrade openssl sources to 1.1.1e (Hassaan Pasha) [#32328](https://github.com/nodejs/node/pull/32328)
- [[`bf26c44c92`](https://github.com/nodejs/node/commit/bf26c44c92)] - **deps**: remove \*.pyc files from deps/npm (Ben Noordhuis) [#32387](https://github.com/nodejs/node/pull/32387)
- [[`c2b3cf61ce`](https://github.com/nodejs/node/commit/c2b3cf61ce)] - **deps**: update npm to 6.14.3 (Myles Borins) [#32368](https://github.com/nodejs/node/pull/32368)
- [[`8cae4dde91`](https://github.com/nodejs/node/commit/8cae4dde91)] - **deps**: upgrade npm to 6.14.1 (Isaac Z. Schlueter) [#31977](https://github.com/nodejs/node/pull/31977)
- [[`47046aa5a9`](https://github.com/nodejs/node/commit/47046aa5a9)] - **deps**: openssl: cherry-pick 4dcb150ea30f (Adam Majer) [#32002](https://github.com/nodejs/node/pull/32002)
- [[`098704c85d`](https://github.com/nodejs/node/commit/098704c85d)] - **deps**: upgrade to libuv 1.34.2 (Colin Ihrig) [#31477](https://github.com/nodejs/node/pull/31477)
- [[`4b1cccc4ce`](https://github.com/nodejs/node/commit/4b1cccc4ce)] - **deps**: upgrade to libuv 1.34.1 (Colin Ihrig) [#31332](https://github.com/nodejs/node/pull/31332)
- [[`fff6162693`](https://github.com/nodejs/node/commit/fff6162693)] - **(SEMVER-MINOR)** **deps**: upgrade to libuv 1.34.0 (Colin Ihrig) [#30783](https://github.com/nodejs/node/pull/30783)
- [[`6826ef0568`](https://github.com/nodejs/node/commit/6826ef0568)] - **deps**: upgrade to libuv 1.33.1 (Colin Ihrig) [#29996](https://github.com/nodejs/node/pull/29996)
- [[`aed7ca4fb0`](https://github.com/nodejs/node/commit/aed7ca4fb0)] - **deps**: upgrade to libuv 1.32.0 (Colin Ihrig) [#29508](https://github.com/nodejs/node/pull/29508)
- [[`794abbc758`](https://github.com/nodejs/node/commit/794abbc758)] - **deps**: upgrade to libuv 1.31.0 (Colin Ihrig) [#29070](https://github.com/nodejs/node/pull/29070)
- [[`ed71f55a54`](https://github.com/nodejs/node/commit/ed71f55a54)] - **deps**: upgrade to libuv 1.30.1 (Colin Ihrig) [#28511](https://github.com/nodejs/node/pull/28511)
- [[`7cde563235`](https://github.com/nodejs/node/commit/7cde563235)] - **deps**: upgrade to libuv 1.30.0 (Colin Ihrig) [#28449](https://github.com/nodejs/node/pull/28449)
- [[`b53ce6e6c5`](https://github.com/nodejs/node/commit/b53ce6e6c5)] - **deps**: upgrade to libuv 1.29.1 (Colin Ihrig) [#27718](https://github.com/nodejs/node/pull/27718)
- [[`9b2b66b7d8`](https://github.com/nodejs/node/commit/9b2b66b7d8)] - **deps**: V8: cherry-pick d89f4ef1cd62 (Milad Farazmand) [#31753](https://github.com/nodejs/node/pull/31753)
- [[`7eac95981e`](https://github.com/nodejs/node/commit/7eac95981e)] - **deps**: upgrade npm to 6.13.7 (Michael Perrotte) [#31558](https://github.com/nodejs/node/pull/31558)
- [[`db24641fbe`](https://github.com/nodejs/node/commit/db24641fbe)] - **deps**: upgrade npm to 6.13.6 (Ruy Adorno) [#31304](https://github.com/nodejs/node/pull/31304)
- [[`2e3d511cff`](https://github.com/nodejs/node/commit/2e3d511cff)] - **doc**: correct version metadata for Readable.from (Dave Vandyke) [#32639](https://github.com/nodejs/node/pull/32639)
- [[`34c1c2a82b`](https://github.com/nodejs/node/commit/34c1c2a82b)] - **doc**: add missing version metadata for Readable.from (Anna Henningsen) [#28695](https://github.com/nodejs/node/pull/28695)
- [[`aa7d369c72`](https://github.com/nodejs/node/commit/aa7d369c72)] - **doc**: update releaser list in README.md (Myles Borins) [#32577](https://github.com/nodejs/node/pull/32577)
- [[`05f5b3ecc4`](https://github.com/nodejs/node/commit/05f5b3ecc4)] - **doc**: remove em dashes (Rich Trott) [#32080](https://github.com/nodejs/node/pull/32080)
- [[`ffa9f9bd1b`](https://github.com/nodejs/node/commit/ffa9f9bd1b)] - **doc**: fix changelog for v10.18.1 (Andrew Hughes) [#31358](https://github.com/nodejs/node/pull/31358)
- [[`0177464b0e`](https://github.com/nodejs/node/commit/0177464b0e)] - **doc,tools**: get altDocs versions from CHANGELOG.md (Richard Lau) [#27661](https://github.com/nodejs/node/pull/27661)
- [[`e9c590ea00`](https://github.com/nodejs/node/commit/e9c590ea00)] - **(SEMVER-MINOR)** **n-api**: define release 6 (Gabriel Schulhof) [#32058](https://github.com/nodejs/node/pull/32058)
- [[`239377b654`](https://github.com/nodejs/node/commit/239377b654)] - **(SEMVER-MINOR)** **n-api**: correct instance data tests (Gabriel Schulhof) [#32488](https://github.com/nodejs/node/pull/32488)
- [[`ecbb331be0`](https://github.com/nodejs/node/commit/ecbb331be0)] - **(SEMVER-MINOR)** **n-api**: add napi_get_all_property_names (himself65) [#30006](https://github.com/nodejs/node/pull/30006)
- [[`f29fb14cf6`](https://github.com/nodejs/node/commit/f29fb14cf6)] - **(SEMVER-MINOR)** **n-api**: add APIs for per-instance state management (Gabriel Schulhof) [#28682](https://github.com/nodejs/node/pull/28682)
- [[`20177b9946`](https://github.com/nodejs/node/commit/20177b9946)] - **n-api**: turn NAPI_CALL_INTO_MODULE into a function (Anna Henningsen) [#26128](https://github.com/nodejs/node/pull/26128)
- [[`017909b847`](https://github.com/nodejs/node/commit/017909b847)] - **test**: fix tool path in test-doctool-versions.js (Richard Lau) [#32645](https://github.com/nodejs/node/pull/32645)
- [[`1ea70d641d`](https://github.com/nodejs/node/commit/1ea70d641d)] - **test**: fix flaky doctool and test (Rich Trott) [#29979](https://github.com/nodejs/node/pull/29979)
- [[`89692ff19b`](https://github.com/nodejs/node/commit/89692ff19b)] - **test**: end tls connection with some data (Sam Roberts) [#32328](https://github.com/nodejs/node/pull/32328)
- [[`9bd1317764`](https://github.com/nodejs/node/commit/9bd1317764)] - **test**: mark empty udp tests flaky on OS X (Sam Roberts) [#31936](https://github.com/nodejs/node/pull/31936)
- [[`5484e061b5`](https://github.com/nodejs/node/commit/5484e061b5)] - **test**: scale keepalive timeouts for slow machines (Ben Noordhuis) [#30834](https://github.com/nodejs/node/pull/30834)
- [[`3f9cec3f51`](https://github.com/nodejs/node/commit/3f9cec3f51)] - **test**: add debugging output to test-net-listen-after-destroy-stdin (Rich Trott) [#31698](https://github.com/nodejs/node/pull/31698)
- [[`f1a8791316`](https://github.com/nodejs/node/commit/f1a8791316)] - **test**: allow EAI_FAIL in test-http-dns-error.js (Colin Ihrig) [#27500](https://github.com/nodejs/node/pull/27500)
- [[`4b9a77909b`](https://github.com/nodejs/node/commit/4b9a77909b)] - **test**: mark tests as flaky (João Reis) [#30848](https://github.com/nodejs/node/pull/30848)
- [[`a8fd8a1a61`](https://github.com/nodejs/node/commit/a8fd8a1a61)] - **test**: mark http2 tests as flaky on 10.x (AshCripps) [#31887](https://github.com/nodejs/node/pull/31887)
- [[`2315270cb6`](https://github.com/nodejs/node/commit/2315270cb6)] - **test**: try to stabalize test-child-process-fork-exec-path.js (Refael Ackermann) [#27277](https://github.com/nodejs/node/pull/27277)
- [[`a2b0e9ef6a`](https://github.com/nodejs/node/commit/a2b0e9ef6a)] - **(SEMVER-MINOR)** **tls**: expose keylog event on TLSSocket (Alba Mendez) [#27654](https://github.com/nodejs/node/pull/27654)
- [[`1cfb45732a`](https://github.com/nodejs/node/commit/1cfb45732a)] - **(SEMVER-MINOR)** **tls**: support TLS min/max protocol defaults in CLI (Sam Roberts) [#27946](https://github.com/nodejs/node/pull/27946)
- [[`a175b8d3a7`](https://github.com/nodejs/node/commit/a175b8d3a7)] - **tools**: only fetch previous versions when necessary (Richard Lau) [#32518](https://github.com/nodejs/node/pull/32518)
- [[`3756be8511`](https://github.com/nodejs/node/commit/3756be8511)] - **tools**: add NODE_TEST_NO_INTERNET to the doc builder (Joyee Cheung) [#31849](https://github.com/nodejs/node/pull/31849)
- [[`ac1ea7312a`](https://github.com/nodejs/node/commit/ac1ea7312a)] - **tools**: make doctool work if no internet available (Richard Lau) [#30214](https://github.com/nodejs/node/pull/30214)
- [[`f235eea8b3`](https://github.com/nodejs/node/commit/f235eea8b3)] - **tools**: unify make-v8.sh for ppc64le and s390x (Richard Lau) [#31628](https://github.com/nodejs/node/pull/31628)
- [[`61e2d4856d`](https://github.com/nodejs/node/commit/61e2d4856d)] - **tools**: use CC instead of CXX when pointing to gcc (Milad Farazmand) [#30817](https://github.com/nodejs/node/pull/30817)
- [[`4390674624`](https://github.com/nodejs/node/commit/4390674624)] - **url**: handle quasi-WHATWG URLs in urlToOptions() (Colin Ihrig) [#26226](https://github.com/nodejs/node/pull/26226)
- [[`dc61e09feb`](https://github.com/nodejs/node/commit/dc61e09feb)] - **v8**: fix load elimination liveness checks (Ben Noordhuis) [#31613](https://github.com/nodejs/node/pull/31613)

Windows 32-bit Installer: https://nodejs.org/dist/v10.20.0/node-v10.20.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v10.20.0/node-v10.20.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v10.20.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v10.20.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v10.20.0/node-v10.20.0.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v10.20.0/node-v10.20.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v10.20.0/node-v10.20.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v10.20.0/node-v10.20.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v10.20.0/node-v10.20.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v10.20.0/node-v10.20.0-aix-ppc64.tar.gz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v10.20.0/node-v10.20.0-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v10.20.0/node-v10.20.0-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v10.20.0/node-v10.20.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v10.20.0/node-v10.20.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v10.20.0/node-v10.20.0.tar.gz \
Other release files: https://nodejs.org/dist/v10.20.0/ \
Documentation: https://nodejs.org/docs/v10.20.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

392432f73ec56ad420bd505d9d4e0e15435138aef45c27106d0f15de4975790b  node-v10.20.0-aix-ppc64.tar.gz
c153832774afcae89a82efb55ed80557d1a41e1880638ad57128a9a3762d212f  node-v10.20.0-darwin-x64.tar.gz
45de728515f0e7cac4ecf7741b0fa09d4e9f2048162fa3b680ea4e1f8ee24588  node-v10.20.0-darwin-x64.tar.xz
88f17346b68594ddcd88e069150d360569b84d23f5ae7e8c08d9300d1057da26  node-v10.20.0-headers.tar.gz
2324f2eaa947d80d59adaeb8d803e6873178b7a6ea25b56d9990e5407678b381  node-v10.20.0-headers.tar.xz
96a26b897d120806c80115bb484160daae3e86944d0c1ffecf1b4be0a8e09501  node-v10.20.0-linux-arm64.tar.gz
f3567924d6b7f0fa55c4ee0a7330ec0dcaeec557982794796d6b312e7053c674  node-v10.20.0-linux-arm64.tar.xz
6d58340726f21450376299c474c192815a9ed751cd359e6bc3d13028c22af7df  node-v10.20.0-linux-armv6l.tar.gz
619b27eae1474aae15762427a8ca061f3a1dc3d080dc7b04bb6cbc8a00f8c7ed  node-v10.20.0-linux-armv6l.tar.xz
1661af5e9ef0f7af92b714349113f51931b3a41b858fb4a17d407a383f748068  node-v10.20.0-linux-armv7l.tar.gz
1923ab9d2e1ff20d3ce09b0c836974a3f508713aa839e75e9cff0a0bc7d01da7  node-v10.20.0-linux-armv7l.tar.xz
8a9c813ba4e5493b8fffaa0f2682ff32c1cfb167707520aa25803cfa997eb160  node-v10.20.0-linux-ppc64le.tar.gz
bbf52916737c10f04b5286219a694a0621b47b71a83755d0670dbcf9d66e3f35  node-v10.20.0-linux-ppc64le.tar.xz
e2b5eb09979040ff40a5707bfda344cfc1af3d383ec366d01b65b6045bb9c399  node-v10.20.0-linux-s390x.tar.gz
4051f510299d2f90b203420435bdbb9486052ea6d1e2b4ac7605b3d8c1c2dd9c  node-v10.20.0-linux-s390x.tar.xz
63f7fe148dece366c79a4daf06d38ab06e979cf6d7c3ea7153887e4d65a5f85e  node-v10.20.0-linux-x64.tar.gz
c5721a89feecc0e98d42386e171cb763c077f782033ddc998819edcf9d93b691  node-v10.20.0-linux-x64.tar.xz
b902776612234df2bf901728eddac8222101acf533f675dbcf8c317af5ccd9da  node-v10.20.0.pkg
b4a1a4f52cd6a093181f649db0d64c88269a8155c455230a40fdd37f374ce148  node-v10.20.0-sunos-x64.tar.gz
e3810a12ccb20d99e0eb90e9b116df573f97d3bd326e4f64f060bddd8615b3b6  node-v10.20.0-sunos-x64.tar.xz
5b7166da554743989c657918a0246b09770bea9706df3b531d7a08e53f77981f  node-v10.20.0.tar.gz
d14116ef2ba9cbcfb5d1c286706de665081dc06ecb5a3507f79a4d0ea8e57233  node-v10.20.0.tar.xz
8ecb7a4a2d9e419c1a326d20e6d4412e0f77b6d14e2f8f9b98a61e747458859c  node-v10.20.0-win-x64.7z
d266313fa22885a6ec76eea521fb8a1131b4d9fb3a57afb045a98301aeb7d24e  node-v10.20.0-win-x64.zip
5c85e119b22cff5210a1d5d19b73d1fe7d3600051e1927ea8514ba42e4acd117  node-v10.20.0-win-x86.7z
22011eb1d4a5fb885ef68c09a9b14391810d23e0c3cf74be611486a28cdfc124  node-v10.20.0-win-x86.zip
129d2c5cc05c5fa74aab254bdbacacb37b5818a89d0b088dbef90d36f3de9c60  node-v10.20.0-x64.msi
ce39f71c7f6472b4d2038e86a5e90672dabbd8b976bb65eed917c1b33fd7a893  node-v10.20.0-x86.msi
d4c6bcf938e1ef55f8105e7e944fe56e43dabd42447eded0b70f3b81dcb5bf75  win-x64/node.exe
213d72051a68c37afc37a5d185a4c5bfacc985bc35331024e5f28635f084da70  win-x64/node.lib
e43384f31dd6849f47d49bebe68517ad800040a0c188883ce190f20ac7c3b2e5  win-x64/node_pdb.7z
bd2d6046f225c8cf26b08971e3453ba257c2aa7e6a2d046ecad4d8a1b6760a3b  win-x64/node_pdb.zip
0da94c857055c1368ea4e83eb67db3dd8e289148d89a9452edb817c73e7ab077  win-x86/node.exe
e5224f36cf5ae52ee8532e383142073fd6c28866745e9967992de28cfbf5b63a  win-x86/node.lib
d8d0bee9f9e288bb37cf8e2103a666f6d7c11fbf152b9e565ac2e67a2fb0962f  win-x86/node_pdb.7z
1345bb2a28f43e3db496f9ce7054142bae523358bbee8a9937dbf729689cd563  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQEzBAEBCAAdFiEETtd49TnjY0x3nIfG1wYoSKGrAFwFAl6N9jgACgkQ1wYoSKGr
AFx5+Qf/fvo2I5Mig7oc4t5nhOJsM8kAbCpXKNmkJYRyNiXn+5pBGO7NUapfQwTx
9+aYoTsz2OZ7X45nA0FtHPbbBRuvdB+XXApX8i2ORruhtA+E4+KeuAGjvAecVrH7
K14O29j2CEAw8OGEYDnaO9E0dRDOeJ+wm4WddeGCxd03EVQB88zjaoYfgtMAufz+
VTYXCwaFGMfumYn1PaZS35pUQra2IsVQmpDUSTd7avCZZnKw6iunvmjHQcxoQfMz
W74Z+3KGNZuYNOz0gLxd5eitkNgL2ZIxY73qUPEMezzaBtUxudvUwxLCPR4kWZ81
S50aw8My98ZVS3fpYb6RPv8aYmw2ug==
=LYjs
-----END PGP SIGNATURE-----

```
