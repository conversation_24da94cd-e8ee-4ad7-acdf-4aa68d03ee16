---
date: '2019-08-15T22:41:03.400Z'
category: release
title: Node v10.16.3 (LTS)
layout: blog-post
author: <PERSON>
---

### Notable changes

This is a security release.

Node.js, as well as many other implementations of HTTP/2, have been found
vulnerable to Denial of Service attacks.
See https://github.com/Netflix/security-bulletins/blob/master/advisories/third-party/2019-002.md
for more information.

Vulnerabilities fixed:

- **CVE-2019-9511 “Data Dribble”**: The attacker requests a large amount of data from a specified resource over multiple streams. They manipulate window size and stream priority to force the server to queue the data in 1-byte chunks. Depending on how efficiently this data is queued, this can consume excess CPU, memory, or both, potentially leading to a denial of service.
- **CVE-2019-9512 “Ping Flood”**: The attacker sends continual pings to an HTTP/2 peer, causing the peer to build an internal queue of responses. Depending on how efficiently this data is queued, this can consume excess CPU, memory, or both, potentially leading to a denial of service.
- **CVE-2019-9513 “Resource Loop”**: The attacker creates multiple request streams and continually shuffles the priority of the streams in a way that causes substantial churn to the priority tree. This can consume excess CPU, potentially leading to a denial of service.
- **CVE-2019-9514 “Reset Flood”**: The attacker opens a number of streams and sends an invalid request over each stream that should solicit a stream of RST_STREAM frames from the peer. Depending on how the peer queues the RST_STREAM frames, this can consume excess memory, CPU, or both, potentially leading to a denial of service.
- **CVE-2019-9515 “Settings Flood”**: The attacker sends a stream of SETTINGS frames to the peer. Since the RFC requires that the peer reply with one acknowledgement per SETTINGS frame, an empty SETTINGS frame is almost equivalent in behavior to a ping. Depending on how efficiently this data is queued, this can consume excess CPU, memory, or both, potentially leading to a denial of service.
- **CVE-2019-9516 “0-Length Headers Leak”**: The attacker sends a stream of headers with a 0-length header name and 0-length header value, optionally Huffman encoded into 1-byte or greater headers. Some implementations allocate memory for these headers and keep the allocation alive until the session dies. This can consume excess memory, potentially leading to a denial of service.
- **CVE-2019-9517 “Internal Data Buffering”**: The attacker opens the HTTP/2 window so the peer can send without constraint; however, they leave the TCP window closed so the peer cannot actually write (many of) the bytes on the wire. The attacker then sends a stream of requests for a large response object. Depending on how the servers queue the responses, this can consume excess memory, CPU, or both, potentially leading to a denial of service.
- **CVE-2019-9518 “Empty Frames Flood”**: The attacker sends a stream of frames with an empty payload and without the end-of-stream flag. These frames can be DATA, HEADERS, CONTINUATION and/or PUSH_PROMISE. The peer spends time processing each frame disproportionate to attack bandwidth. This can consume excess CPU, potentially leading to a denial of service. (Discovered by Piotr Sikora of Google)

### Commits

- [[`74507fae34`](https://github.com/nodejs/node/commit/74507fae34)] - **deps**: update nghttp2 to 1.39.2 (Anna Henningsen) [#29122](https://github.com/nodejs/node/pull/29122)
- [[`a397c881ec`](https://github.com/nodejs/node/commit/a397c881ec)] - **deps**: update nghttp2 to 1.39.1 (gengjiawen) [#28448](https://github.com/nodejs/node/pull/28448)
- [[`fedfa12a33`](https://github.com/nodejs/node/commit/fedfa12a33)] - **deps**: update nghttp2 to 1.38.0 (gengjiawen) [#27295](https://github.com/nodejs/node/pull/27295)
- [[`ab0f2ace36`](https://github.com/nodejs/node/commit/ab0f2ace36)] - **deps**: update nghttp2 to 1.37.0 (gengjiawen) [#26990](https://github.com/nodejs/node/pull/26990)
- [[`0acbe05ee2`](https://github.com/nodejs/node/commit/0acbe05ee2)] - **http2**: allow security revert for Ping/Settings Flood (Anna Henningsen) [#29122](https://github.com/nodejs/node/pull/29122)
- [[`c152449012`](https://github.com/nodejs/node/commit/c152449012)] - **http2**: pause input processing if sending output (Anna Henningsen) [#29122](https://github.com/nodejs/node/pull/29122)
- [[`0ce699c7b1`](https://github.com/nodejs/node/commit/0ce699c7b1)] - **http2**: stop reading from socket if writes are in progress (Anna Henningsen) [#29122](https://github.com/nodejs/node/pull/29122)
- [[`17357d37a9`](https://github.com/nodejs/node/commit/17357d37a9)] - **http2**: consider 0-length non-end DATA frames an error (Anna Henningsen) [#29122](https://github.com/nodejs/node/pull/29122)
- [[`460f896c63`](https://github.com/nodejs/node/commit/460f896c63)] - **http2**: shrink default `vector::reserve()` allocations (Anna Henningsen) [#29122](https://github.com/nodejs/node/pull/29122)
- [[`f4242e24f9`](https://github.com/nodejs/node/commit/f4242e24f9)] - **http2**: handle 0-length headers better (Anna Henningsen) [#29122](https://github.com/nodejs/node/pull/29122)
- [[`477461a51f`](https://github.com/nodejs/node/commit/477461a51f)] - **http2**: limit number of invalid incoming frames (Anna Henningsen) [#29122](https://github.com/nodejs/node/pull/29122)
- [[`05dada46ee`](https://github.com/nodejs/node/commit/05dada46ee)] - **http2**: limit number of rejected stream openings (Anna Henningsen) [#29122](https://github.com/nodejs/node/pull/29122)
- [[`7f11465572`](https://github.com/nodejs/node/commit/7f11465572)] - **http2**: do not create ArrayBuffers when no DATA received (Anna Henningsen) [#29122](https://github.com/nodejs/node/pull/29122)
- [[`2eb914ff5f`](https://github.com/nodejs/node/commit/2eb914ff5f)] - **http2**: only call into JS when necessary for session events (Anna Henningsen) [#29122](https://github.com/nodejs/node/pull/29122)
- [[`76a7ada15d`](https://github.com/nodejs/node/commit/76a7ada15d)] - **http2**: improve JS-side debug logging (Anna Henningsen) [#29122](https://github.com/nodejs/node/pull/29122)
- [[`00f153da13`](https://github.com/nodejs/node/commit/00f153da13)] - **http2**: improve http2 code a bit (James M Snell) [#23984](https://github.com/nodejs/node/pull/23984)
- [[`a0a14c809f`](https://github.com/nodejs/node/commit/a0a14c809f)] - **src**: pass along errors from http2 object creation (Anna Henningsen) [#25822](https://github.com/nodejs/node/pull/25822)
- [[`d85e4006ab`](https://github.com/nodejs/node/commit/d85e4006ab)] - **test**: apply test-http2-max-session-memory-leak from v12.x (Anna Henningsen) [#29122](https://github.com/nodejs/node/pull/29122)

Windows 32-bit Installer: https://nodejs.org/dist/v10.16.3/node-v10.16.3-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v10.16.3/node-v10.16.3-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v10.16.3/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v10.16.3/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v10.16.3/node-v10.16.3.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v10.16.3/node-v10.16.3-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v10.16.3/node-v10.16.3-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v10.16.3/node-v10.16.3-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v10.16.3/node-v10.16.3-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v10.16.3/node-v10.16.3-aix-ppc64.tar.gz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v10.16.3/node-v10.16.3-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v10.16.3/node-v10.16.3-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v10.16.3/node-v10.16.3-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v10.16.3/node-v10.16.3-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v10.16.3/node-v10.16.3.tar.gz \
Other release files: https://nodejs.org/dist/v10.16.3/ \
Documentation: https://nodejs.org/docs/v10.16.3/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

1e246436e177d69354b5e3ea47579a03b68d7b61a2565972c18c3a4cb5d8ee33  node-v10.16.3-aix-ppc64.tar.gz
6febc571e1543c2845fa919c6d06b36a24e4e142c91aedbe28b6ff7d296119e4  node-v10.16.3-darwin-x64.tar.gz
1d5ce05abf39ef482c2c3eaf16c1f4edb01314308066871d3dfc99e95701b19b  node-v10.16.3-darwin-x64.tar.xz
0901d1f1bfe0f5d922f9d79c5822a3624c11eb0f2de9fd552425bc98c7b95e54  node-v10.16.3-headers.tar.gz
6ceb3249ac26a098c943de8104c19d7ea5a540b02db8652b80ab54deed1e5848  node-v10.16.3-headers.tar.xz
3bab16e7107092e43426e082ee9fd88ef0a43a35816f662f14563bcc5152600d  node-v10.16.3-linux-arm64.tar.gz
8ee77bad022bd460bf2867a97bf56ce7ddc4aa2ace067e45995fb1721a958428  node-v10.16.3-linux-arm64.tar.xz
c4ce8f8627c4d0d71647987924e5369cd1adeef2865bf1bdbb4387b75fba804b  node-v10.16.3-linux-armv6l.tar.gz
17b6ea87cde13c330a72c44be8a30ebebf83c7c71d1a1b92e1d629f273fa0743  node-v10.16.3-linux-armv6l.tar.xz
f267710728ca56ca3b522076fff808540ea27d6aa5fd586bc0ad39c389530c3b  node-v10.16.3-linux-armv7l.tar.gz
4a6f27308cd0cea136be6f5f7c194b0faabea016efe844fb1452c846d2eca7b1  node-v10.16.3-linux-armv7l.tar.xz
3e0f41b3cb991f062e1c93a297675af34d1a2f29052306cd585f1f5e25ea9159  node-v10.16.3-linux-ppc64le.tar.gz
b51731b49f6816f5e3e670c399611eb9386f558cc2d597301fb73e585640a642  node-v10.16.3-linux-ppc64le.tar.xz
ae87fa499fd5fd9d86d4f788cb4e54bd3eb4fdb735b20235537e9d38ae5da73b  node-v10.16.3-linux-s390x.tar.gz
90cc57506c98cd5561f5dda9e103fa70083242fff2b1a39a5351fc15ff63db46  node-v10.16.3-linux-s390x.tar.xz
2f0397bb81c1d0c9901b9aff82a933257bf60f3992227b86107111a75b9030d9  node-v10.16.3-linux-x64.tar.gz
d2271fd8cf997fa7447d638dfa92749ff18ca4b0d796bf89f2a82bf7800d5506  node-v10.16.3-linux-x64.tar.xz
1c820559780c7a5b90ceebd819afce58f3c14805191141fbd59feb8485c2fc73  node-v10.16.3.pkg
d8f2418b748c4c9b1d8c1dc008b42384ca87c4c4c134df3a3009e6b7546f8b23  node-v10.16.3-sunos-x64.tar.gz
2952da9d127c3f72fe5b3e466fbfe8a45ab3ef63263d881da7e9b586559f3693  node-v10.16.3-sunos-x64.tar.xz
db5a5e03a815b84a1266a4b48bb6a6d887175705f84fd2472f0d28e5e305a1f8  node-v10.16.3.tar.gz
7bf1123d7415964775b8f81fe6ec6dd5c3c08abb42bb71dfe4409dbeeba26bbd  node-v10.16.3.tar.xz
c2cc3b95d6e8504b20623cfe74f848d1ebc2f43a262e7bae14bee64db2b13889  node-v10.16.3-win-x64.7z
19aa47de7c5950d7bd71a1e878013b98d93871cc311d7185f5472e6d3f633146  node-v10.16.3-win-x64.zip
fc1ccb1e6f12fe68e816c552e6b1df289a8bfe064c025eb62c360bd041da4a5f  node-v10.16.3-win-x86.7z
02a30a17c25c354747ac76fd130ff5495dde2f60f0a21ad675526951c2d7a52a  node-v10.16.3-win-x86.zip
f68b75eea46232adb8fd38126c977dc244166d29e7c6cd2df930b460c38590a9  node-v10.16.3-x64.msi
b3cf18ad447d177cf858dbe45751100846cad96ba7497cd7c1850a616758e67f  node-v10.16.3-x86.msi
cb8f5c1981e11a3d438b6867df5fb8e4f77cd29ddc0856d99b62060ef991c649  win-x64/node.exe
9f10cd25d27fc31f8fe8f5fcc5e9f27a8ba5bb79dc0791dc0df102f063c0066e  win-x64/node.lib
654d350ee101cbb4a0246e5226169f99af763a4d4c14546370ec6dfb09cf5a47  win-x64/node_pdb.7z
426f326d88b5a184cc74ba50f5106eca933cb3386f872ffeb5844f3916296c09  win-x64/node_pdb.zip
78b71fd2fb792c5f24848a05119d50d02407675a54a72caf0e40b039a43fefdd  win-x86/node.exe
459095d9bd9dc4e2d12c4284e6fc5bdc78eec8181c8327c57876499b002a59fd  win-x86/node.lib
1d0bb57a74e836835a1f9b8c1e0bf2493d4bd095c81a8e12d05cbfbf8c363534  win-x86/node_pdb.7z
e2b5ea28cbe965e074e61035b559efda9312044ac5d0346243c041bbb62cea73  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQEzBAEBCAAdFiEETtd49TnjY0x3nIfG1wYoSKGrAFwFAl1V2SsACgkQ1wYoSKGr
AFx2zAgAvucy6XmXR4ErdiTw0EJQjp19NHknk5ovcGVFJKMqwe4V2UqYgru7aFJL
paEEyS8j7pKeQ8d9AqmPiUontMVGFgJg9bD7qZIwiZnFoLeoe8CjY9jpDd+USDH7
NuKRCC7JLcjEXiMM9oQm4TzdmKLjopPvKWkLcrZka7SO1WQHdAz9N7LqBRt/WWre
VRxtC7pmEpR9SxXy+MVKw4aveO7rkPSDQ99XBPBccn2u2PUjSws/muyYN5cNQ0O5
USF86KoJGqlYcEEFXxk76OWJxeaik2eUv7Sjy/QOtbb9UvQQdrzzLKp0xK6+zGXw
dmNx9HVSkLfw++f4DOjvDofqlsnxOQ==
=Ie/z
-----END PGP SIGNATURE-----

```
