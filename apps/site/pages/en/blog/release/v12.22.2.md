---
date: '2021-07-01T15:42:17.938Z'
category: release
title: Node v12.22.2 (LTS)
layout: blog-post
author: <PERSON>
---

### Notable Changes

Vulnerabilities fixed:

- **CVE-2021-22918**: libuv upgrade - Out of bounds read (Medium)
  - Node.js is vulnerable to out-of-bounds read in libuv's uv\_\_idna_toascii() function which is used to convert strings to ASCII. This is called by Node's dns module's lookup() function and can lead to information disclosures or crashes. You can read more about it in https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2021-22918
- **CVE-2021-22921**: Windows installer - Node Installer Local Privilege Escalation (Medium)
  - Node.js is vulnerable to local privilege escalation attacks under certain conditions on Windows platforms. More specifically, improper configuration of permissions in the installation directory allows an attacker to perform two different escalation attacks: PATH and DLL hijacking. You can read more about it in https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2021-22921
- **CVE-2021-27290**: npm upgrade - ssri Regular Expression Denial of Service (ReDoS) (High)
  - This is a vulnerability in the ssri npm mudule which may be vulnerable to denial of service attacks. You can read more about it in https://github.com/advisories/GHSA-vx3p-948g-6vhq
- **CVE-2021-23362**: npm upgrade - hosted-git-info Regular Expression Denial of Service (ReDoS) (Medium)
  - This is a vulnerability in the hosted-git-info npm mudule which may be vulnerable to denial of service attacks. You can read more about it in https://nvd.nist.gov/vuln/detail/CVE-2021-23362

### Commits

- [[`623fd1fcb5`](https://github.com/nodejs/node/commit/623fd1fcb5)] - **deps**: uv: cherry-pick 99c29c9c2c9b (Ben Noordhuis) [nodejs-private/node-private#267](https://github.com/nodejs-private/node-private/pull/267)
- [[`923b3760f8`](https://github.com/nodejs/node/commit/923b3760f8)] - **deps**: upgrade npm to 6.14.13 (Ruy Adorno) [#38214](https://github.com/nodejs/node/pull/38214)
- [[`a52790cba0`](https://github.com/nodejs/node/commit/a52790cba0)] - **win,msi**: set install directory permission (AkshayK) [nodejs-private/node-private#269](https://github.com/nodejs-private/node-private/pull/269)

Windows 32-bit Installer: https://nodejs.org/dist/v12.22.2/node-v12.22.2-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v12.22.2/node-v12.22.2-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v12.22.2/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v12.22.2/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v12.22.2/node-v12.22.2.pkg \
macOS Intel 64-bit Binary: https://nodejs.org/dist/v12.22.2/node-v12.22.2-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v12.22.2/node-v12.22.2-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v12.22.2/node-v12.22.2-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v12.22.2/node-v12.22.2-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v12.22.2/node-v12.22.2-aix-ppc64.tar.gz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v12.22.2/node-v12.22.2-sunos-x64.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v12.22.2/node-v12.22.2-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v12.22.2/node-v12.22.2-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v12.22.2/node-v12.22.2.tar.gz \
Other release files: https://nodejs.org/dist/v12.22.2/ \
Documentation: https://nodejs.org/docs/v12.22.2/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

6e55248464241a1a2e025872c02becf49ab4a989576a5a40fdabc7c11c2cb449  node-v12.22.2-aix-ppc64.tar.gz
293156b4ab199ed2775e4b4c69dbd6d3730fe0a099be8c354d5628652ff0ec3a  node-v12.22.2-darwin-x64.tar.gz
99fc13a8e1592be75183abeea8479c49c20a28e34003d3b47ddbbb3bf0825d74  node-v12.22.2-darwin-x64.tar.xz
737fef9dd325c7154670e1846a09c16377fc2a6245031465d1e388cf23c481bc  node-v12.22.2-headers.tar.gz
af16ebb7e35ab2274bd2a6685c20ccb7a331046e9eed4b432e9e287be6fc0fe1  node-v12.22.2-headers.tar.xz
40f4a6a887e3ab8675e71bdc544353e078775074ec9f7911cfe3827ad68007fb  node-v12.22.2-linux-arm64.tar.gz
ab9b950b80bad8e9070129438cabd548a31dc9c292d41341453d22920f49cdbb  node-v12.22.2-linux-arm64.tar.xz
b96aa9b7cefda490c0cc5f91ef9dcd1a1d59cd7e9891bebc1827a99bdc3dea76  node-v12.22.2-linux-armv7l.tar.gz
dee507935ecfd2b3e01a27a0d2307d8b20e7e105bd82560859919c09e717eef9  node-v12.22.2-linux-armv7l.tar.xz
a3012c6eda500322f300cfc22afbe7b3b4bfb7c9165b735670221d7f6beceef2  node-v12.22.2-linux-ppc64le.tar.gz
ee9a11f1fbbd29879c5ea90ac8e93420f2d05042802a89f70470e60cda7d382c  node-v12.22.2-linux-ppc64le.tar.xz
ed4594abf3a572ef4f247310a6534f874d2f3de5ac49012dc0314136eb190c2c  node-v12.22.2-linux-s390x.tar.gz
38ef8f8db5c4eaba9cc919811a65933dfe0844564569e1cf8acb4a96b27d77ec  node-v12.22.2-linux-s390x.tar.xz
2898ac962602443fedeaaccf61b33f127c97f7c9d7b23fbe0d78a4d20b69db0b  node-v12.22.2-linux-x64.tar.gz
c17f838cec2eb111048342543d23a74bcab7459a4374615ff4124940a717377a  node-v12.22.2-linux-x64.tar.xz
461b3637a576a4030a369fa116d8376d72b7b84c9d40e21650b2f6423c965269  node-v12.22.2.pkg
735e1d050790470683125e02d4f085313fc11e8aaa696ca09f4dc3b1bf7b6f2f  node-v12.22.2-sunos-x64.tar.gz
752507716a94731b59a581a69e746bc04ec3f006b2f29277ff85a77b1224c675  node-v12.22.2-sunos-x64.tar.xz
210a550c47056f29537e1b5b73cb78a88c44609c3b92aa003cf7862d3904ef99  node-v12.22.2.tar.gz
7fd805571df106f086f4c45e131efed98bfd62628d9dec96bd62f8c11b0c48dc  node-v12.22.2.tar.xz
d5d04f560ce321b5fd803f9676b7d430d7218d270a5ce561a11f1e3c1b6ebdd7  node-v12.22.2-win-x64.7z
c42cd8c8d8c10faeff8ed02d2d3c5214feb519873ced78c47d0374847843110b  node-v12.22.2-win-x64.zip
730dc503eb1b6704dc7d97001c33d5f12a374a5e52ba8ad53bb3bc53b23739b8  node-v12.22.2-win-x86.7z
179a7837e2bf1478ea36515942efbcbfec5e45fea5eb526074016cee310cfa8a  node-v12.22.2-win-x86.zip
5f5416f96c015278ab9a3c5d1e53c82293e0b1fa91f440d4159a4d4ba54a7805  node-v12.22.2-x64.msi
de6fad6d89dc0ed1749dc2d15ba26267961732c569cd79904d903868c2680973  node-v12.22.2-x86.msi
baa4a8ee5fdd7c98ff2cccc59523f10e5ca21df518e2fc4d8ff628cc974c9982  win-x64/node.exe
28e5c24831deedbf4fb8a9560f2c4f95205479c589f54a9a53ec346f6a5cf8bf  win-x64/node.lib
d27fa6c9f89ff31193ed99664f34250305e137900e3d2bc07e3ff7d0393ea644  win-x64/node_pdb.7z
5403fdbd09880f02754f749fe59a0ce225a3ef4f2fe0372c23ac0b9c233717f4  win-x64/node_pdb.zip
31857477fb99710529b0c89f2635629ecffca166ddea633f6926a8ee419eb4d1  win-x86/node.exe
8bbcf3b9305b83f54bd80f8ec19d4e237841bde5bfaeb2aec708c36daa6435f6  win-x86/node.lib
efe340a69f091eaa9c2def41152259559171f603ba7d3ef5e3b7dffc589efff5  win-x86/node_pdb.7z
e2bbb4249e5a18e6b7efac1f0faa2625888e66f6517e419ea18fa9c9e04acc22  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEyC+jrhy+3Gvka5NgxDzsRcF6uTwFAmDd4SAACgkQxDzsRcF6
uTzSPg/7BaFYxhFQs73xJ9y3CucOK9dVPVXtr/8/tXil1pXPtVxEMxghtT1Rbr0W
E4m9F8qiu8mFaNXqu99wywnH8RzAK8yce7uxlvcvcTizLA0tPmgyQUTRRjIrjl7t
e6lbBi0swhdNaVBLMdVs9Ln40op+sBwN27bn43DtrYIlih8hA2P2UoSTf/+tWKG/
/10/LZ+UFB2ddwV6NJdJJY36dSHppNK/2gJZ9w963vEb5DqN987ydJuLnwOBmdkc
sKfvj1WsR7ycUH+P6/1dnRzLBXhhZo+nlMtdVpVhiUh9kwNbl1UwALTG/Qmp76Z9
1AiTodFftb471cpv7I7HnBKqEoVK/qsHJ82+uDuIaf/qnBvKHTpMOe4dwMajnQzf
YAhHaS/Os/52lQEY3LoYIMTPzvby15U7LaEp3phupUKOjtpSBnYiSNOoCMy5foZS
3uvOvbXMAlkpYq7fRHn8WUSC6CBM/Et6GZD0h4YnlvJiRbB2wXOKHR2lNIr9wf/w
4fTZZZjbs8JpTYKWifypstPqwfm9wrAtXre7dkH146hKl7qFIro3P0nC6U4vsQiZ
O2xLI5ekVkWwe2zeqAxcoHN+ZcsBxn5z/sDQTP5Een2yOaz0BMXy2+m5mcC5YTJo
DiPrmaWX+Juo8KynBxbjZF5EC9is78QVgOYUtYcqgSBm3lDUYy8=
=j1ft
-----END PGP SIGNATURE-----

```
