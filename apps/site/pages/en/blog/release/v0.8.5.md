---
date: '2012-08-02T22:00:45.000Z'
category: release
title: Version 0.8.5 (stable)
layout: blog-post
author: The Node.js Project
---

2012.08.02, Version 0.8.5 (Stable)

- node: tag Encode and friends NODE_EXTERN (<PERSON>)

- fs: fix ReadStream / WriteStream missing callback (<PERSON>)

- fs: fix readFileSync("/proc/cpuinfo") regression (<PERSON>)

- installer: don't assume bash is installed (<PERSON>)

- Report errors properly from --eval and stdin (isaacs)

- assert: fix throws() throws an error without message property (koichik)

- cluster: fix libuv assert in net.listen() (<PERSON>)

- build: always link sunos builds with libumem (<PERSON>)

- build: improve armv7 / hard-float detection (<PERSON>)

- https: Use host header as effective servername (isaacs)

- sunos: work around OS bug to prevent fs.watch() from spinning (<PERSON>)

- linux: fix 'two watchers, one path' segfault (<PERSON>)

- windows: fix memory leaks in many fs functions (<PERSON>)

- windows: don't allow directories to be opened for writing/appending (<PERSON>)

- windows: make fork() work even when not all stdio handles are valid (<PERSON>der)

- windows: make unlink() not remove mount points, and improve performance (Bert Belder)

- build: Sign pkg installer for OS X (isaacs)

Source Code: https://nodejs.org/dist/v0.8.5/node-v0.8.5.tar.gz

Macintosh Installer (Universal): https://nodejs.org/dist/v0.8.5/node-v0.8.5.pkg

Windows Installer: https://nodejs.org/dist/v0.8.5/node-v0.8.5-x86.msi

Windows x64 Installer: https://nodejs.org/dist/v0.8.5/x64/node-v0.8.5-x64.msi

Windows x64 Files: https://nodejs.org/dist/v0.8.5/x64/

Other release files: https://nodejs.org/dist/v0.8.5/

Website: https://nodejs.org/docs/v0.8.5/

Documentation: https://nodejs.org/docs/v0.8.5/api/

Shasums:

```
74b470d04c3dac9f5838d4ed61e2fb50e394114a  node-v0.8.5-x86.msi
28280575b717306b34440c83aace720dccc3047f  node-v0.8.5.pkg
835ba5ca429e56f65aeb1a5d9730fff105e86337  node-v0.8.5.tar.gz
c8ce66eefd6d75b44cdf29bb49aeffe4ea534b7f  node.exe
d3832fb5a7a45d739c9d3faa7e13115bdaaa9cc7  node.exp
5dee595f12dbfdee14b6e8aebc07a77ee96e5e45  node.lib
ac62ec2d2508dfcd0c2f5cfcf725286ad876fb7a  node.pdb
12876d80668b066b460833d096998525d8eceec7  npm-1.1.46.tgz
4e820e5a99512e194ec30878ede08488211c8391  npm-1.1.46.zip
95882b6aeab74725197b10d31ff4ee3a0723574c  x64/node-v0.8.5-x64.msi
9d9c0652e57e05708b59bba78a55bc1237fcfb73  x64/node.exe
f13f377c432c5a8fb4661abaae3a07c46ac74a66  x64/node.exp
a17e3e8742ac084e1f7f99f721470e9d6182e75f  x64/node.lib
4026170fed5a55fad6ee569daa9666cbb9d5ae3d  x64/node.pdb
```
