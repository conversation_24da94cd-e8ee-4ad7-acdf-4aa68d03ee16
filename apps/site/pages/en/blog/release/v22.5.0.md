---
date: '2024-07-17T16:08:19.116Z'
category: release
title: Node v22.5.0 (Current)
layout: blog-post
author: <PERSON>
---

## 2024-07-17, Version 22.5.0 (Current), @RafaelGSS prepared by @aduh95

### Notable Changes

- \[[`1367c5558e`](https://github.com/nodejs/node/commit/1367c5558e)] - **(SEMVER-MINOR)** **http**: expose websockets (<PERSON>) [#53721](https://github.com/nodejs/node/pull/53721)
- \[[`b31394920d`](https://github.com/nodejs/node/commit/b31394920d)] - **(SEMVER-MINOR)** **lib**: add `node:sqlite` module (<PERSON>) [#53752](https://github.com/nodejs/node/pull/53752)
- \[[`aa7df9551d`](https://github.com/nodejs/node/commit/aa7df9551d)] - **module**: add `__esModule` to `require()`'d ESM (<PERSON><PERSON>) [#52166](https://github.com/nodejs/node/pull/52166)
- \[[`8743c4d65a`](https://github.com/nodejs/node/commit/8743c4d65a)] - **(SEMVER-MINOR)** **path**: add `matchesGlob` method (Aviv Keller) [#52881](https://github.com/nodejs/node/pull/52881)
- \[[`77936c3d24`](https://github.com/nodejs/node/commit/77936c3d24)] - **(SEMVER-MINOR)** **process**: port on-exit-leak-free to core (Vinicius Lourenço) [#53239](https://github.com/nodejs/node/pull/53239)
- \[[`82d88a83f8`](https://github.com/nodejs/node/commit/82d88a83f8)] - **(SEMVER-MINOR)** **stream**: pipeline wait for close before calling the callback (jakecastelli) [#53462](https://github.com/nodejs/node/pull/53462)
- \[[`3a0fcbb17a`](https://github.com/nodejs/node/commit/3a0fcbb17a)] - **test_runner**: support glob matching coverage files (Aviv Keller) [#53553](https://github.com/nodejs/node/pull/53553)
- \[[`22ca334090`](https://github.com/nodejs/node/commit/22ca334090)] - **(SEMVER-MINOR)** **worker**: add `postMessageToThread` (Paolo Insogna) [#53682](https://github.com/nodejs/node/pull/53682)

### Commits

- \[[`eb4e370933`](https://github.com/nodejs/node/commit/eb4e370933)] - **benchmark**: add require-esm benchmark (Joyee Cheung) [#52166](https://github.com/nodejs/node/pull/52166)
- \[[`4d4a8338db`](https://github.com/nodejs/node/commit/4d4a8338db)] - **benchmark**: add cpSync benchmark (Yagiz Nizipli) [#53612](https://github.com/nodejs/node/pull/53612)
- \[[`3d60b38afa`](https://github.com/nodejs/node/commit/3d60b38afa)] - **build**: fix build warning of c-ares under GN build (Cheng) [#53750](https://github.com/nodejs/node/pull/53750)
- \[[`a45c801048`](https://github.com/nodejs/node/commit/a45c801048)] - **build**: fix build error in sqlite under GN build (Cheng) [#53686](https://github.com/nodejs/node/pull/53686)
- \[[`40032eb623`](https://github.com/nodejs/node/commit/40032eb623)] - **build**: add gn files for deps/nbytes (Cheng) [#53685](https://github.com/nodejs/node/pull/53685)
- \[[`082799debb`](https://github.com/nodejs/node/commit/082799debb)] - **build**: fix mac build error of c-ares under GN (Cheng) [#53687](https://github.com/nodejs/node/pull/53687)
- \[[`b05394ea6a`](https://github.com/nodejs/node/commit/b05394ea6a)] - **build**: add version-specific library path for AIX (Richard Lau) [#53585](https://github.com/nodejs/node/pull/53585)
- \[[`6237172eaf`](https://github.com/nodejs/node/commit/6237172eaf)] - **cli**: update `node.1` to reflect Atom's sunset (Aviv Keller) [#53734](https://github.com/nodejs/node/pull/53734)
- \[[`5697938cb7`](https://github.com/nodejs/node/commit/5697938cb7)] - **crypto**: avoid std::function (Tobias Nießen) [#53683](https://github.com/nodejs/node/pull/53683)
- \[[`3cc01aa314`](https://github.com/nodejs/node/commit/3cc01aa314)] - **crypto**: make deriveBits length parameter optional and nullable (Filip Skokan) [#53601](https://github.com/nodejs/node/pull/53601)
- \[[`f82e20fdea`](https://github.com/nodejs/node/commit/f82e20fdea)] - **crypto**: avoid taking ownership of OpenSSL objects (Tobias Nießen) [#53460](https://github.com/nodejs/node/pull/53460)
- \[[`ad1e5610ec`](https://github.com/nodejs/node/commit/ad1e5610ec)] - **deps**: update googletest to 4b21f1a (Node.js GitHub Bot) [#53842](https://github.com/nodejs/node/pull/53842)
- \[[`d285d610a0`](https://github.com/nodejs/node/commit/d285d610a0)] - **deps**: update minimatch to 10.0.1 (Node.js GitHub Bot) [#53841](https://github.com/nodejs/node/pull/53841)
- \[[`70f5209c9f`](https://github.com/nodejs/node/commit/70f5209c9f)] - **deps**: update corepack to 0.29.2 (Node.js GitHub Bot) [#53838](https://github.com/nodejs/node/pull/53838)
- \[[`4930e12a45`](https://github.com/nodejs/node/commit/4930e12a45)] - **deps**: update simdutf to 5.3.0 (Node.js GitHub Bot) [#53837](https://github.com/nodejs/node/pull/53837)
- \[[`d346833364`](https://github.com/nodejs/node/commit/d346833364)] - **deps**: update ada to 2.9.0 (Node.js GitHub Bot) [#53748](https://github.com/nodejs/node/pull/53748)
- \[[`ab8abb5367`](https://github.com/nodejs/node/commit/ab8abb5367)] - **deps**: upgrade npm to 10.8.2 (npm team) [#53799](https://github.com/nodejs/node/pull/53799)
- \[[`1ad664905a`](https://github.com/nodejs/node/commit/1ad664905a)] - **deps**: update nbytes and add update script (Yagiz Nizipli) [#53790](https://github.com/nodejs/node/pull/53790)
- \[[`a66f11e798`](https://github.com/nodejs/node/commit/a66f11e798)] - **deps**: update googletest to 34ad51b (Node.js GitHub Bot) [#53157](https://github.com/nodejs/node/pull/53157)
- \[[`9bf61d6a0d`](https://github.com/nodejs/node/commit/9bf61d6a0d)] - **deps**: update googletest to 305e5a2 (Node.js GitHub Bot) [#53157](https://github.com/nodejs/node/pull/53157)
- \[[`8542ace488`](https://github.com/nodejs/node/commit/8542ace488)] - **deps**: V8: cherry-pick 9ebca66a5740 (Chengzhong Wu) [#53755](https://github.com/nodejs/node/pull/53755)
- \[[`29a734c21d`](https://github.com/nodejs/node/commit/29a734c21d)] - **deps**: V8: cherry-pick e061cf9970d9 (Joyee Cheung) [#53755](https://github.com/nodejs/node/pull/53755)
- \[[`c7624af44a`](https://github.com/nodejs/node/commit/c7624af44a)] - **deps**: update c-ares to v1.32.1 (Node.js GitHub Bot) [#53753](https://github.com/nodejs/node/pull/53753)
- \[[`bbcec9e129`](https://github.com/nodejs/node/commit/bbcec9e129)] - **deps**: update minimatch to 9.0.5 (Node.js GitHub Bot) [#53646](https://github.com/nodejs/node/pull/53646)
- \[[`76032fd980`](https://github.com/nodejs/node/commit/76032fd980)] - **deps**: update c-ares to v1.32.0 (Node.js GitHub Bot) [#53722](https://github.com/nodejs/node/pull/53722)
- \[[`26386046ad`](https://github.com/nodejs/node/commit/26386046ad)] - **doc**: move MylesBorins to emeritus (Myles Borins) [#53760](https://github.com/nodejs/node/pull/53760)
- \[[`362875bda0`](https://github.com/nodejs/node/commit/362875bda0)] - **doc**: add Rafael to the last security release (Rafael Gonzaga) [#53769](https://github.com/nodejs/node/pull/53769)
- \[[`a1a5ad848d`](https://github.com/nodejs/node/commit/a1a5ad848d)] - **doc**: use mock.callCount() in examples (Sébastien Règne) [#53754](https://github.com/nodejs/node/pull/53754)
- \[[`bb960c5471`](https://github.com/nodejs/node/commit/bb960c5471)] - **doc**: clarify authenticity of plaintexts in update (Tobias Nießen) [#53784](https://github.com/nodejs/node/pull/53784)
- \[[`5dd3018eb4`](https://github.com/nodejs/node/commit/5dd3018eb4)] - **doc**: add option to have support me link (Michael Dawson) [#53312](https://github.com/nodejs/node/pull/53312)
- \[[`0f95ad3d7d`](https://github.com/nodejs/node/commit/0f95ad3d7d)] - **doc**: add OpenSSL security level to TLS docs (Afanasii Kurakin) [#53647](https://github.com/nodejs/node/pull/53647)
- \[[`2d92ec2831`](https://github.com/nodejs/node/commit/2d92ec2831)] - **doc**: update `scroll-padding-top` to 4rem (Cloyd Lau) [#53662](https://github.com/nodejs/node/pull/53662)
- \[[`933359a786`](https://github.com/nodejs/node/commit/933359a786)] - **doc**: mention v8.setFlagsFromString to pm (Rafael Gonzaga) [#53731](https://github.com/nodejs/node/pull/53731)
- \[[`e17c2618e3`](https://github.com/nodejs/node/commit/e17c2618e3)] - **doc**: remove the last \<pre> tag (Claudio W) [#53741](https://github.com/nodejs/node/pull/53741)
- \[[`7f18a5f47a`](https://github.com/nodejs/node/commit/7f18a5f47a)] - **doc**: exclude voting and regular TSC from spotlight (Michael Dawson) [#53694](https://github.com/nodejs/node/pull/53694)
- \[[`df3dcd1bd1`](https://github.com/nodejs/node/commit/df3dcd1bd1)] - **doc**: fix releases guide for recent Git versions (Michaël Zasso) [#53709](https://github.com/nodejs/node/pull/53709)
- \[[`50987ea833`](https://github.com/nodejs/node/commit/50987ea833)] - **doc**: require `node:process` in assert doc examples (Alfredo González) [#53702](https://github.com/nodejs/node/pull/53702)
- \[[`fa58d01497`](https://github.com/nodejs/node/commit/fa58d01497)] - **doc**: add additional explanation to the wildcard section in permissions (jakecastelli) [#53664](https://github.com/nodejs/node/pull/53664)
- \[[`28bf1e48ef`](https://github.com/nodejs/node/commit/28bf1e48ef)] - **doc**: mark NODE_MODULE_VERSION for Node.js 22.0.0 (Michaël Zasso) [#53650](https://github.com/nodejs/node/pull/53650)
- \[[`1cc0b41f00`](https://github.com/nodejs/node/commit/1cc0b41f00)] - **doc**: include node.module_timer on available categories (Vinicius Lourenço) [#53638](https://github.com/nodejs/node/pull/53638)
- \[[`d224e9eab5`](https://github.com/nodejs/node/commit/d224e9eab5)] - **doc**: fix module customization hook examples (Elliot Goodrich) [#53637](https://github.com/nodejs/node/pull/53637)
- \[[`2cf60964e6`](https://github.com/nodejs/node/commit/2cf60964e6)] - **doc**: fix doc for correct usage with plan & TestContext (Emil Tayeb) [#53615](https://github.com/nodejs/node/pull/53615)
- \[[`6df86ae056`](https://github.com/nodejs/node/commit/6df86ae056)] - **doc**: remove some news issues that are no longer (Michael Dawson) [#53608](https://github.com/nodejs/node/pull/53608)
- \[[`42b9408f3e`](https://github.com/nodejs/node/commit/42b9408f3e)] - **doc**: add issue for news from ambassadors (Michael Dawson) [#53607](https://github.com/nodejs/node/pull/53607)
- \[[`2d1ff91953`](https://github.com/nodejs/node/commit/2d1ff91953)] - **doc**: add esm example for os (Leonardo Peixoto) [#53604](https://github.com/nodejs/node/pull/53604)
- \[[`de99d69d75`](https://github.com/nodejs/node/commit/de99d69d75)] - **doc**: clarify usage of coverage reporters (Eliphaz Bouye) [#53523](https://github.com/nodejs/node/pull/53523)
- \[[`519c328dcf`](https://github.com/nodejs/node/commit/519c328dcf)] - **doc**: document addition testing options (Aviv Keller) [#53569](https://github.com/nodejs/node/pull/53569)
- \[[`c6166cdfe4`](https://github.com/nodejs/node/commit/c6166cdfe4)] - **doc**: clarify that fs.exists() may return false for existing symlink (Tobias Nießen) [#53566](https://github.com/nodejs/node/pull/53566)
- \[[`9139ab2848`](https://github.com/nodejs/node/commit/9139ab2848)] - **doc**: note http.closeAllConnections excludes upgraded sockets (Rob Hogan) [#53560](https://github.com/nodejs/node/pull/53560)
- \[[`19b3718ee1`](https://github.com/nodejs/node/commit/19b3718ee1)] - **doc, meta**: add PTAL to glossary (Aviv Keller) [#53770](https://github.com/nodejs/node/pull/53770)
- \[[`80c1f5ce8a`](https://github.com/nodejs/node/commit/80c1f5ce8a)] - **doc, typings**: events.once accepts symbol event type (René) [#53542](https://github.com/nodejs/node/pull/53542)
- \[[`1a21e0f61e`](https://github.com/nodejs/node/commit/1a21e0f61e)] - **esm**: improve `defaultResolve` performance (Yagiz Nizipli) [#53711](https://github.com/nodejs/node/pull/53711)
- \[[`262f2cb3b6`](https://github.com/nodejs/node/commit/262f2cb3b6)] - **esm**: remove unnecessary toNamespacedPath calls (Yagiz Nizipli) [#53656](https://github.com/nodejs/node/pull/53656)
- \[[`e29c9453a9`](https://github.com/nodejs/node/commit/e29c9453a9)] - **esm**: move hooks test with others (Geoffrey Booth) [#53558](https://github.com/nodejs/node/pull/53558)
- \[[`8368555289`](https://github.com/nodejs/node/commit/8368555289)] - **fs**: add v8 fast api to closeSync (Yagiz Nizipli) [#53627](https://github.com/nodejs/node/pull/53627)
- \[[`628a539810`](https://github.com/nodejs/node/commit/628a539810)] - **fs**: reduce throwing unnecessary errors on glob (Yagiz Nizipli) [#53632](https://github.com/nodejs/node/pull/53632)
- \[[`076e82ca40`](https://github.com/nodejs/node/commit/076e82ca40)] - **fs**: move `ToNamespacedPath` dir calls to c++ (Yagiz Nizipli) [#53630](https://github.com/nodejs/node/pull/53630)
- \[[`128e514d81`](https://github.com/nodejs/node/commit/128e514d81)] - **fs**: improve error performance of `fs.dir` (Yagiz Nizipli) [#53667](https://github.com/nodejs/node/pull/53667)
- \[[`603c2c5c08`](https://github.com/nodejs/node/commit/603c2c5c08)] - **fs**: fix typings (Yagiz Nizipli) [#53626](https://github.com/nodejs/node/pull/53626)
- \[[`1367c5558e`](https://github.com/nodejs/node/commit/1367c5558e)] - **(SEMVER-MINOR)** **http**: expose websockets (Natalia Venditto) [#53721](https://github.com/nodejs/node/pull/53721)
- \[[`7debb6c36e`](https://github.com/nodejs/node/commit/7debb6c36e)] - **http**: remove prototype primordials (Antoine du Hamel) [#53698](https://github.com/nodejs/node/pull/53698)
- \[[`b13aea5698`](https://github.com/nodejs/node/commit/b13aea5698)] - **http, readline**: replace sort with toSorted (Benjamin Gruenbaum) [#53623](https://github.com/nodejs/node/pull/53623)
- \[[`1397f5d9f4`](https://github.com/nodejs/node/commit/1397f5d9f4)] - **http2**: remove prototype primordials (Antoine du Hamel) [#53696](https://github.com/nodejs/node/pull/53696)
- \[[`f57d3cee2c`](https://github.com/nodejs/node/commit/f57d3cee2c)] - **lib**: make navigator not runtime-lookup process.version/arch/platform (Jordan Harband) [#53765](https://github.com/nodejs/node/pull/53765)
- \[[`0a01abbd45`](https://github.com/nodejs/node/commit/0a01abbd45)] - **lib**: refactor `platform` utility methods (Daniel Bayley) [#53817](https://github.com/nodejs/node/pull/53817)
- \[[`afe7f4f819`](https://github.com/nodejs/node/commit/afe7f4f819)] - **lib**: remove path.resolve from permissions.js (Rafael Gonzaga) [#53729](https://github.com/nodejs/node/pull/53729)
- \[[`cbe77b30ca`](https://github.com/nodejs/node/commit/cbe77b30ca)] - **lib**: move `ToNamespacedPath` call to c++ (Yagiz Nizipli) [#53654](https://github.com/nodejs/node/pull/53654)
- \[[`0f146aac2c`](https://github.com/nodejs/node/commit/0f146aac2c)] - **lib**: make navigator properties lazy (James M Snell) [#53649](https://github.com/nodejs/node/pull/53649)
- \[[`0540308bd7`](https://github.com/nodejs/node/commit/0540308bd7)] - **lib**: add toJSON to PerformanceMeasure (theanarkh) [#53603](https://github.com/nodejs/node/pull/53603)
- \[[`b31394920d`](https://github.com/nodejs/node/commit/b31394920d)] - **(SEMVER-MINOR)** **lib,src,test,doc**: add node:sqlite module (Colin Ihrig) [#53752](https://github.com/nodejs/node/pull/53752)
- \[[`1a7c2dc5ea`](https://github.com/nodejs/node/commit/1a7c2dc5ea)] - **meta**: remove redudant logging from dep updaters (Aviv Keller) [#53783](https://github.com/nodejs/node/pull/53783)
- \[[`ac5d7b709d`](https://github.com/nodejs/node/commit/ac5d7b709d)] - **meta**: change email address of anonrig (Yagiz Nizipli) [#53829](https://github.com/nodejs/node/pull/53829)
- \[[`085ec5533c`](https://github.com/nodejs/node/commit/085ec5533c)] - **meta**: add `node_sqlite.c` to PR label config (Aviv Keller) [#53797](https://github.com/nodejs/node/pull/53797)
- \[[`c68d873e99`](https://github.com/nodejs/node/commit/c68d873e99)] - **meta**: move one or more collaborators to emeritus (Node.js GitHub Bot) [#53758](https://github.com/nodejs/node/pull/53758)
- \[[`5ae8ea489d`](https://github.com/nodejs/node/commit/5ae8ea489d)] - **meta**: use HTML entities in commit-queue comment (Aviv Keller) [#53744](https://github.com/nodejs/node/pull/53744)
- \[[`ecd8fceb68`](https://github.com/nodejs/node/commit/ecd8fceb68)] - **meta**: move regular TSC member to emeritus (Michael Dawson) [#53693](https://github.com/nodejs/node/pull/53693)
- \[[`05058f9809`](https://github.com/nodejs/node/commit/05058f9809)] - **meta**: bump codecov/codecov-action from 4.4.1 to 4.5.0 (dependabot\[bot]) [#53675](https://github.com/nodejs/node/pull/53675)
- \[[`e272ffa3d6`](https://github.com/nodejs/node/commit/e272ffa3d6)] - **meta**: bump mozilla-actions/sccache-action from 0.0.4 to 0.0.5 (dependabot\[bot]) [#53674](https://github.com/nodejs/node/pull/53674)
- \[[`a39407560c`](https://github.com/nodejs/node/commit/a39407560c)] - **meta**: bump github/codeql-action from 3.25.7 to 3.25.11 (dependabot\[bot]) [#53673](https://github.com/nodejs/node/pull/53673)
- \[[`e4ce92ee31`](https://github.com/nodejs/node/commit/e4ce92ee31)] - **meta**: bump actions/checkout from 4.1.6 to 4.1.7 (dependabot\[bot]) [#53672](https://github.com/nodejs/node/pull/53672)
- \[[`4cf98febe7`](https://github.com/nodejs/node/commit/4cf98febe7)] - **meta**: bump peter-evans/create-pull-request from 6.0.5 to 6.1.0 (dependabot\[bot]) [#53671](https://github.com/nodejs/node/pull/53671)
- \[[`c28af95bf5`](https://github.com/nodejs/node/commit/c28af95bf5)] - **meta**: bump step-security/harden-runner from 2.8.0 to 2.8.1 (dependabot\[bot]) [#53670](https://github.com/nodejs/node/pull/53670)
- \[[`dd2157bc83`](https://github.com/nodejs/node/commit/dd2157bc83)] - **meta**: move member from TSC regular to emeriti (Michael Dawson) [#53599](https://github.com/nodejs/node/pull/53599)
- \[[`508abfe178`](https://github.com/nodejs/node/commit/508abfe178)] - **meta**: warnings bypass deprecation cycle (Benjamin Gruenbaum) [#53513](https://github.com/nodejs/node/pull/53513)
- \[[`3c5ec839e3`](https://github.com/nodejs/node/commit/3c5ec839e3)] - **meta**: prevent constant references to issues in versioning (Aviv Keller) [#53564](https://github.com/nodejs/node/pull/53564)
- \[[`aa7df9551d`](https://github.com/nodejs/node/commit/aa7df9551d)] - **module**: add \_\_esModule to require()'d ESM (Joyee Cheung) [#52166](https://github.com/nodejs/node/pull/52166)
- \[[`8743c4d65a`](https://github.com/nodejs/node/commit/8743c4d65a)] - **(SEMVER-MINOR)** **path**: add `matchesGlob` method (Aviv Keller) [#52881](https://github.com/nodejs/node/pull/52881)
- \[[`77936c3d24`](https://github.com/nodejs/node/commit/77936c3d24)] - **(SEMVER-MINOR)** **process**: port on-exit-leak-free to core (Vinicius Lourenço) [#53239](https://github.com/nodejs/node/pull/53239)
- \[[`5e4ca9fbb6`](https://github.com/nodejs/node/commit/5e4ca9fbb6)] - **src**: update outdated references to spec sections (Tobias Nießen) [#53832](https://github.com/nodejs/node/pull/53832)
- \[[`c22d9d5167`](https://github.com/nodejs/node/commit/c22d9d5167)] - **src**: use Maybe\<void> in ManagedEVPPKey (Tobias Nießen) [#53811](https://github.com/nodejs/node/pull/53811)
- \[[`d41ed44f49`](https://github.com/nodejs/node/commit/d41ed44f49)] - **src**: move `loadEnvFile` toNamespacedPath call (Yagiz Nizipli) [#53658](https://github.com/nodejs/node/pull/53658)
- \[[`dc99dd391f`](https://github.com/nodejs/node/commit/dc99dd391f)] - **src**: fix error handling in ExportJWKAsymmetricKey (Tobias Nießen) [#53767](https://github.com/nodejs/node/pull/53767)
- \[[`ab1e03e8cd`](https://github.com/nodejs/node/commit/ab1e03e8cd)] - **src**: use Maybe\<void> in node::crypto::error (Tobias Nießen) [#53766](https://github.com/nodejs/node/pull/53766)
- \[[`9bde9b254d`](https://github.com/nodejs/node/commit/9bde9b254d)] - **src**: fix implementation of `PropertySetterCallback` (Igor Sheludko) [#53576](https://github.com/nodejs/node/pull/53576)
- \[[`021e2cf40f`](https://github.com/nodejs/node/commit/021e2cf40f)] - **src**: remove unused ContextifyContext::WeakCallback (Chengzhong Wu) [#53517](https://github.com/nodejs/node/pull/53517)
- \[[`87121a17c4`](https://github.com/nodejs/node/commit/87121a17c4)] - **src**: fix typo in node.h (Daeyeon Jeong) [#53759](https://github.com/nodejs/node/pull/53759)
- \[[`94c7054c8d`](https://github.com/nodejs/node/commit/94c7054c8d)] - **src**: document the Node.js context embedder data (Joyee Cheung) [#53611](https://github.com/nodejs/node/pull/53611)
- \[[`c181940e83`](https://github.com/nodejs/node/commit/c181940e83)] - **src**: zero-initialize data that are copied into the snapshot (Joyee Cheung) [#53563](https://github.com/nodejs/node/pull/53563)
- \[[`8cda2db64c`](https://github.com/nodejs/node/commit/8cda2db64c)] - _**Revert**_ "**src**: make sure that memcpy-ed structs in snapshot have no padding" (Joyee Cheung) [#53563](https://github.com/nodejs/node/pull/53563)
- \[[`81767f6089`](https://github.com/nodejs/node/commit/81767f6089)] - **src**: fix Worker termination when '--inspect-brk' is passed (Daeyeon Jeong) [#53724](https://github.com/nodejs/node/pull/53724)
- \[[`a9db553935`](https://github.com/nodejs/node/commit/a9db553935)] - **src**: refactor embedded entrypoint loading (Joyee Cheung) [#53573](https://github.com/nodejs/node/pull/53573)
- \[[`3ab8aba478`](https://github.com/nodejs/node/commit/3ab8aba478)] - **src**: do not get string_view from temp string (Cheng) [#53688](https://github.com/nodejs/node/pull/53688)
- \[[`664bf6c28f`](https://github.com/nodejs/node/commit/664bf6c28f)] - **src**: replace `kPathSeparator` with std::filesystem (Yagiz Nizipli) [#53063](https://github.com/nodejs/node/pull/53063)
- \[[`cc1f49751a`](https://github.com/nodejs/node/commit/cc1f49751a)] - **src**: move `FromNamespacedPath` to path.cc (Yagiz Nizipli) [#53540](https://github.com/nodejs/node/pull/53540)
- \[[`e43a4e07ec`](https://github.com/nodejs/node/commit/e43a4e07ec)] - **src**: use `starts_with` in node_dotenv.cc (Yagiz Nizipli) [#53539](https://github.com/nodejs/node/pull/53539)
- \[[`19488fd4ce`](https://github.com/nodejs/node/commit/19488fd4ce)] - **src,test**: further cleanup references to osx (Daniel Bayley) [#53820](https://github.com/nodejs/node/pull/53820)
- \[[`4bf62f6cbd`](https://github.com/nodejs/node/commit/4bf62f6cbd)] - **stream**: improve inspector ergonomics (Benjamin Gruenbaum) [#53800](https://github.com/nodejs/node/pull/53800)
- \[[`82d88a83f8`](https://github.com/nodejs/node/commit/82d88a83f8)] - **(SEMVER-MINOR)** **stream**: pipeline wait for close before calling the callback (jakecastelli) [#53462](https://github.com/nodejs/node/pull/53462)
- \[[`53a7dd7790`](https://github.com/nodejs/node/commit/53a7dd7790)] - **test**: update wpt test (Mert Can Altin) [#53814](https://github.com/nodejs/node/pull/53814)
- \[[`bc480902ab`](https://github.com/nodejs/node/commit/bc480902ab)] - **test**: update WPT WebIDL interfaces (Filip Skokan) [#53720](https://github.com/nodejs/node/pull/53720)
- \[[`d13153d90f`](https://github.com/nodejs/node/commit/d13153d90f)] - **test**: un-set inspector-async-hook-setup-at-inspect-brk as flaky (Abdirahim Musse) [#53692](https://github.com/nodejs/node/pull/53692)
- \[[`ac9c2e6bf2`](https://github.com/nodejs/node/commit/ac9c2e6bf2)] - **test**: use python3 instead of python in pummel test (Mathis Wiehl) [#53057](https://github.com/nodejs/node/pull/53057)
- \[[`bac28678e6`](https://github.com/nodejs/node/commit/bac28678e6)] - **test**: do not assume cwd in snapshot tests (Antoine du Hamel) [#53146](https://github.com/nodejs/node/pull/53146)
- \[[`41e106c0c6`](https://github.com/nodejs/node/commit/41e106c0c6)] - **test**: use `Set.difference()` (Richard Lau) [#53597](https://github.com/nodejs/node/pull/53597)
- \[[`8aab680f66`](https://github.com/nodejs/node/commit/8aab680f66)] - **test**: fix OpenSSL version checks (Richard Lau) [#53503](https://github.com/nodejs/node/pull/53503)
- \[[`6aa4f0f266`](https://github.com/nodejs/node/commit/6aa4f0f266)] - **test**: refactor, add assertion to http-request-end (jakecastelli) [#53411](https://github.com/nodejs/node/pull/53411)
- \[[`fbc5cbb617`](https://github.com/nodejs/node/commit/fbc5cbb617)] - **test_runner**: remove plan option from run() (Colin Ihrig) [#53834](https://github.com/nodejs/node/pull/53834)
- \[[`c590828ad8`](https://github.com/nodejs/node/commit/c590828ad8)] - **test_runner**: fix escaping in snapshot tests (Julian Kniephoff) [#53833](https://github.com/nodejs/node/pull/53833)
- \[[`3a0fcbb17a`](https://github.com/nodejs/node/commit/3a0fcbb17a)] - **test_runner**: support glob matching coverage files (Aviv Keller) [#53553](https://github.com/nodejs/node/pull/53553)
- \[[`e6a1eeb73d`](https://github.com/nodejs/node/commit/e6a1eeb73d)] - **test_runner**: support module detection in module mocks (Geoffrey Booth) [#53642](https://github.com/nodejs/node/pull/53642)
- \[[`4d777de7d4`](https://github.com/nodejs/node/commit/4d777de7d4)] - **tls**: add setKeyCert() to tls.Socket (Brian White) [#53636](https://github.com/nodejs/node/pull/53636)
- \[[`ab9adfc42a`](https://github.com/nodejs/node/commit/ab9adfc42a)] - **tls**: remove prototype primordials (Antoine du Hamel) [#53699](https://github.com/nodejs/node/pull/53699)
- \[[`03d378ffb9`](https://github.com/nodejs/node/commit/03d378ffb9)] - **tools**: update lint-md-dependencies (Node.js GitHub Bot) [#53840](https://github.com/nodejs/node/pull/53840)
- \[[`06377b1b11`](https://github.com/nodejs/node/commit/06377b1b11)] - **tools**: update eslint to 9.7.0 (Node.js GitHub Bot) [#53839](https://github.com/nodejs/node/pull/53839)
- \[[`d6629a2d84`](https://github.com/nodejs/node/commit/d6629a2d84)] - **tools**: use v8_features.json to populate config.gypi (Cheng) [#53749](https://github.com/nodejs/node/pull/53749)
- \[[`d3653fe8ac`](https://github.com/nodejs/node/commit/d3653fe8ac)] - **tools**: update eslint to 9.6.0 (Node.js GitHub Bot) [#53645](https://github.com/nodejs/node/pull/53645)
- \[[`1e930e93d4`](https://github.com/nodejs/node/commit/1e930e93d4)] - **tools**: update lint-md-dependencies to unified\@11.0.5 (Node.js GitHub Bot) [#53555](https://github.com/nodejs/node/pull/53555)
- \[[`317a13b30f`](https://github.com/nodejs/node/commit/317a13b30f)] - **tools**: replace reference to NodeMainInstance with SnapshotBuilder (codediverdev) [#53544](https://github.com/nodejs/node/pull/53544)
- \[[`0e25faea0a`](https://github.com/nodejs/node/commit/0e25faea0a)] - **typings**: add `fs_dir` types (Yagiz Nizipli) [#53631](https://github.com/nodejs/node/pull/53631)
- \[[`7637f291be`](https://github.com/nodejs/node/commit/7637f291be)] - **url**: fix typo (KAYYY) [#53827](https://github.com/nodejs/node/pull/53827)
- \[[`2c6548afd1`](https://github.com/nodejs/node/commit/2c6548afd1)] - **url**: reduce unnecessary string copies (Yagiz Nizipli) [#53628](https://github.com/nodejs/node/pull/53628)
- \[[`0f2b57d1bc`](https://github.com/nodejs/node/commit/0f2b57d1bc)] - **url**: make URL.parse enumerable (Filip Skokan) [#53720](https://github.com/nodejs/node/pull/53720)
- \[[`1300169f80`](https://github.com/nodejs/node/commit/1300169f80)] - **url**: add missing documentation for `URL.parse()` (Yagiz Nizipli) [#53733](https://github.com/nodejs/node/pull/53733)
- \[[`c55e72ed8b`](https://github.com/nodejs/node/commit/c55e72ed8b)] - **util**: fix crashing when emitting new Buffer() deprecation warning #53075 (Aras Abbasi) [#53089](https://github.com/nodejs/node/pull/53089)
- \[[`5aa216320e`](https://github.com/nodejs/node/commit/5aa216320e)] - **v8**: move `ToNamespacedPath` to c++ (Yagiz Nizipli) [#53655](https://github.com/nodejs/node/pull/53655)
- \[[`9fd976b09d`](https://github.com/nodejs/node/commit/9fd976b09d)] - **vm,src**: add property query interceptors (Chengzhong Wu) [#53517](https://github.com/nodejs/node/pull/53517)
- \[[`22ca334090`](https://github.com/nodejs/node/commit/22ca334090)] - **(SEMVER-MINOR)** **worker**: add postMessageToThread (Paolo Insogna) [#53682](https://github.com/nodejs/node/pull/53682)
- \[[`5aecbefbd5`](https://github.com/nodejs/node/commit/5aecbefbd5)] - **worker**: allow copied NODE_OPTIONS in the env setting (Joyee Cheung) [#53596](https://github.com/nodejs/node/pull/53596)

Windows 32-bit Installer: https://nodejs.org/dist/v22.5.0/node-v22.5.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v22.5.0/node-v22.5.0-x64.msi \
Windows ARM 64-bit Installer: https://nodejs.org/dist/v22.5.0/node-v22.5.0-arm64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v22.5.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v22.5.0/win-x64/node.exe \
Windows ARM 64-bit Binary: https://nodejs.org/dist/v22.5.0/win-arm64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v22.5.0/node-v22.5.0.pkg \
macOS Apple Silicon 64-bit Binary: https://nodejs.org/dist/v22.5.0/node-v22.5.0-darwin-arm64.tar.gz \
macOS Intel 64-bit Binary: https://nodejs.org/dist/v22.5.0/node-v22.5.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v22.5.0/node-v22.5.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v22.5.0/node-v22.5.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v22.5.0/node-v22.5.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v22.5.0/node-v22.5.0-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v22.5.0/node-v22.5.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v22.5.0/node-v22.5.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v22.5.0/node-v22.5.0.tar.gz \
Other release files: https://nodejs.org/dist/v22.5.0/ \
Documentation: https://nodejs.org/docs/v22.5.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

e55511ccce9e25789d80135882fe10301f5f4cf018c3ab48bd3b6b7dcfb80a71  node-v22.5.0-aix-ppc64.tar.gz
173bc126eb7ce24c4a1b2fdafd32221894358a854fa73f94fcf772094d156ef9  node-v22.5.0-arm64.msi
dd484dc9b461aa78cfea3f48d540b4ae600efb07c7482893ee9d260dd3686614  node-v22.5.0-darwin-arm64.tar.gz
2550d7feb00e7e0a569d43a43c4827b038f5eeff3645e88660023fa177a763e2  node-v22.5.0-darwin-arm64.tar.xz
a4409511fd1008a75f16b42a2d5694f3622f361eafa59bec5bab1928389754a2  node-v22.5.0-darwin-x64.tar.gz
64841cfd0a8e9dfb4425442d1336c076b4d5248de974dfa762bdb932c81d421c  node-v22.5.0-darwin-x64.tar.xz
713f6a77ada0577a2ae6aff3ff7986874706efbc0d4e6c1c3082208eafac1479  node-v22.5.0-headers.tar.gz
29c11bd809890de264b1100264ecf6f2022952553fed5420f0a2c6a7ece9259b  node-v22.5.0-headers.tar.xz
f64000dcdb279f056dc574db13494d61fd85f2928c99ffa2e6dd2e5843ce6299  node-v22.5.0-linux-arm64.tar.gz
67c8c3b243e90a8cb8a47fe87bdc8f787fd842df33dc6132e484893bdb635e75  node-v22.5.0-linux-arm64.tar.xz
73b4d63ca3fc958910a6a7155ad7f584c60d26998ef36ebadcde7f0fae5c97fc  node-v22.5.0-linux-armv7l.tar.gz
1d7245aff4942f47cd949858362ea1b5f138900d86989308c510b957e5d065eb  node-v22.5.0-linux-armv7l.tar.xz
8e0bde5839c81cd2c6e536fe5e2f4de58ee644c92e1cddd4f5e29365b716d798  node-v22.5.0-linux-ppc64le.tar.gz
c7978108c925acfd420a4f67dcc856b43fcabc4b1d0b9f1ddbe7f9746f8488f7  node-v22.5.0-linux-ppc64le.tar.xz
73af14a5da0eda93abe1beb8b30278ca88f49ba3699bacb218a062d19a6de0aa  node-v22.5.0-linux-s390x.tar.gz
1022bd1d761da80444d173653e1e6576a7d46a48c031c69b3444f01629b277d5  node-v22.5.0-linux-s390x.tar.xz
f1a2eadec1f7ae92a98252eda0579b581d9a592792a1a3c391a073b38d5f036c  node-v22.5.0-linux-x64.tar.gz
1b8b7db45ff5ef9ac82a9d92c276408b0fcfdb44f322360bdb02d156b5442b46  node-v22.5.0-linux-x64.tar.xz
63ebc337186168a1474838efeaa1bbe3ed5ca2d8a6d64dc6b2f433a5614686e8  node-v22.5.0-win-arm64.7z
a2cb4dd4cb49a8bcddc9c51574b82615816205a3e074f254c2e3bfa5045aca4d  node-v22.5.0-win-arm64.zip
00a8043372ed1a8565f3772337690421b64904ecee7347d9c31d7cf3663fbde8  node-v22.5.0-win-x64.7z
9523ca091931a50c4fb93e001c9d11033e5440e92122c93008e6e16ff15660e5  node-v22.5.0-win-x64.zip
72756432d58cfa863f5dfe7f92d180c7fee1608859af436948e4c2c4738163cd  node-v22.5.0-win-x86.7z
43b1b6afc62ecb6fbd9518b3173aa92348c1aa3c8ec059ca505c00da7ea5bac2  node-v22.5.0-win-x86.zip
f273c7e2e013ebd1e9f3498ca5608af857eb385426e6179e9a889b065b63686c  node-v22.5.0-x64.msi
4fa9542aba7098c33c2244b728ec0fa67d71e52dee2a7a4bc9e10218f8cf5cea  node-v22.5.0-x86.msi
71c80417cdffc34ec4a5b8f2814513d7cf0ff6eceaee5d3ecd5470ceb0d55194  node-v22.5.0.pkg
8327234c161abd9d8768537404258389adb4c7da02cda7d055d457ceb3ff9bc1  node-v22.5.0.tar.gz
5a86238f00393716a03cd304f7c63f4c27144889d05a3938bb0f242c8d29d62f  node-v22.5.0.tar.xz
076e3728466fe9275f2169ecd434561a83625181f51517f345661158e853d754  win-arm64/node.exe
30e63a6726cda6539eeb37c311adf915bccd5c1462723b97a6c07ac91e8ae728  win-arm64/node.lib
f0e6767bce59e516c4bbb47b8cfec7afe78de9ac32a386f368b9f5aed1ca05f1  win-arm64/node_pdb.7z
3e65d7873316d209c3bb0d50d6cc770cbec305382293b8a65bd8c76351e888d9  win-arm64/node_pdb.zip
26e7ac93a231cc04a2c93821d99455bd5ab5682ce0db3930ee4e33228b4750b5  win-x64/node.exe
c4d08d45267da3625a30730bf5c8e41518f25d9809179feb267f1b393f5c5f05  win-x64/node.lib
4d4f62c9a811a0f35c507fd447432e020f03a951055aec331e7fbf7fd80204ef  win-x64/node_pdb.7z
3d86182574d020069b0ad5576c9974364d37365505ca1128cbfd764c24a5aa73  win-x64/node_pdb.zip
c572a54a3acf1bee443744b917ed7bab34664325083a4204cbbf98e2feb35160  win-x86/node.exe
fc3bf3c1e561da1e1c152be9aa5ed1bce8d263a5124841a4ba41ebc37c727f3e  win-x86/node.lib
700a57520d462dc03c1399e1b6912805fbc6d54678853dca1f725c5c59bfab6e  win-x86/node_pdb.7z
0750c7973c03513fc7658f9fc784ac80472853e24a48582558d170a0ae37cc7b  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQGzBAEBCAAdFiEEiQwI24V5Fi/uDfnbi+q0389VXvQFAmaX67UACgkQi+q0389V
XvTKGAv/fY/yKxULLdeRomBN3KmU1WwGFLtxdx5RhAWMMcFBZWtfrkpbAdNszvP2
Ig3I7vFE4i9bWkEExm6sJEz+8eTdmGsi0P8oQxG9GZcV6PfkH9GAP1XK+QxWIbmz
dNR9IYkQzl3k9zDfTLGBGb5JbPvUYW1iIYPtO77UEm6Yv253u8oAMUcObPGfAaRK
Ho8S+o5CtI0dztISUxOKQn9tn+UBwJBhekCRZLz/0aZzQd7Orktl4AIwUXul6xpf
R/2H8ofHz6RtZ0Ne1MIizradcw8pGdC4bLEeIHEMT64L+bQXmV0VDtV9ehefAkvz
cP8hGnrPuLuDNnhdbplnM7W9+OuRbrJYg5oxymmsrV8oJHMUi4LuxEutXoIXBOCb
z8JNEg4fd2tD0xX7tybLiJIg8uWM1CJutzt+vHs5HtWHvjL+OiL3X5VDLZQdHB88
xq6mns91saFtQI4P4zmzxwL5K+0o0KCQhfmBpEoDnQfDkXjCI0cP+GocLonx2Lz1
JJHsOztu
=9PDN
-----END PGP SIGNATURE-----
```
