---
date: '2024-02-14T17:36:06.168Z'
category: release
title: Node v21.6.2 (Current)
layout: blog-post
author: <PERSON>
---

## 2024-02-14, Version 21.6.2 (Current), @RafaelGSS

### Notable changes

This is a security release.

### Notable changes

- CVE-2024-21892 - Code injection and privilege escalation through Linux capabilities- (High)
- CVE-2024-22019 - http: Reading unprocessed HTTP request with unbounded chunk extension allows DoS attacks- (High)
- CVE-2024-21896 - Path traversal by monkey-patching Buffer internals- (High)
- CVE-2024-22017 - setuid() does not drop all privileges due to io_uring - (High)
- CVE-2023-46809 - Node.js is vulnerable to the Marvin Attack (timing variant of the <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> attack against PKCS#1 v1.5 padding) - (Medium)
- CVE-2024-21891 - Multiple permission model bypasses due to improper path traversal sequence sanitization - (Medium)
- CVE-2024-21890 - Improper handling of wildcards in --allow-fs-read and --allow-fs-write (Medium)
- CVE-2024-22025 - Denial of Service by resource exhaustion in fetch() brotli decoding - (Medium)
- undici version 5.28.3
- libuv version 1.48.0
- OpenSSL version 3.0.13+quic1

### Commits

- \[[`8344719369`](https://github.com/nodejs/node/commit/8344719369)] - **crypto**: disable PKCS#1 padding for privateDecrypt (Michael Dawson) [nodejs-private/node-private#525](https://github.com/nodejs-private/node-private/pull/525)
- \[[`d093600ac4`](https://github.com/nodejs/node/commit/d093600ac4)] - **deps**: update archs files for openssl-3.0.13+quic1 (Node.js GitHub Bot) [#51614](https://github.com/nodejs/node/pull/51614)
- \[[`6cd930e5e8`](https://github.com/nodejs/node/commit/6cd930e5e8)] - **deps**: upgrade openssl sources to quictls/openssl-3.0.13+quic1 (Node.js GitHub Bot) [#51614](https://github.com/nodejs/node/pull/51614)
- \[[`9590c15d3d`](https://github.com/nodejs/node/commit/9590c15d3d)] - **deps**: upgrade libuv to 1.48.0 (Santiago Gimeno) [#51698](https://github.com/nodejs/node/pull/51698)
- \[[`666096298c`](https://github.com/nodejs/node/commit/666096298c)] - **deps**: disable io_uring support in libuv by default (Tobias Nießen) [nodejs-private/node-private#528](https://github.com/nodejs-private/node-private/pull/528)
- \[[`a4edd22e30`](https://github.com/nodejs/node/commit/a4edd22e30)] - **fs**: protect against modified Buffer internals in possiblyTransformPath (Tobias Nießen) [nodejs-private/node-private#497](https://github.com/nodejs-private/node-private/pull/497)
- \[[`6155a1ffaf`](https://github.com/nodejs/node/commit/6155a1ffaf)] - **http**: add maximum chunk extension size (Paolo Insogna) [nodejs-private/node-private#518](https://github.com/nodejs-private/node-private/pull/518)
- \[[`777509495e`](https://github.com/nodejs/node/commit/777509495e)] - **lib**: use cache fs internals against path traversal (RafaelGSS) [nodejs-private/node-private#516](https://github.com/nodejs-private/node-private/pull/516)
- \[[`9d2ac2b3fc`](https://github.com/nodejs/node/commit/9d2ac2b3fc)] - **lib**: update undici to v5.28.3 (Matteo Collina) [nodejs-private/node-private#538](https://github.com/nodejs-private/node-private/pull/538)
- \[[`208b3940c7`](https://github.com/nodejs/node/commit/208b3940c7)] - **src**: fix HasOnly(capability) in node::credentials (Tobias Nießen) [nodejs-private/node-private#505](https://github.com/nodejs-private/node-private/pull/505)
- \[[`fc2454f29c`](https://github.com/nodejs/node/commit/fc2454f29c)] - **src,deps**: disable setuid() etc if io_uring enabled (Tobias Nießen) [nodejs-private/node-private#528](https://github.com/nodejs-private/node-private/pull/528)
- \[[`ef3eea20be`](https://github.com/nodejs/node/commit/ef3eea20be)] - **test,doc**: clarify wildcard usage (RafaelGSS) [nodejs-private/node-private#517](https://github.com/nodejs-private/node-private/pull/517)
- \[[`8547196964`](https://github.com/nodejs/node/commit/8547196964)] - **zlib**: pause stream if outgoing buffer is full (Matteo Collina) [nodejs-private/node-private#540](https://github.com/nodejs-private/node-private/pull/540)

Windows 32-bit Installer: https://nodejs.org/dist/v21.6.2/node-v21.6.2-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v21.6.2/node-v21.6.2-x64.msi \
Windows ARM 64-bit Installer: https://nodejs.org/dist/v21.6.2/node-v21.6.2-arm64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v21.6.2/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v21.6.2/win-x64/node.exe \
Windows ARM 64-bit Binary: https://nodejs.org/dist/v21.6.2/win-arm64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v21.6.2/node-v21.6.2.pkg \
macOS Apple Silicon 64-bit Binary: https://nodejs.org/dist/v21.6.2/node-v21.6.2-darwin-arm64.tar.gz \
macOS Intel 64-bit Binary: https://nodejs.org/dist/v21.6.2/node-v21.6.2-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v21.6.2/node-v21.6.2-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v21.6.2/node-v21.6.2-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v21.6.2/node-v21.6.2-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v21.6.2/node-v21.6.2-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v21.6.2/node-v21.6.2-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v21.6.2/node-v21.6.2-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v21.6.2/node-v21.6.2.tar.gz \
Other release files: https://nodejs.org/dist/v21.6.2/ \
Documentation: https://nodejs.org/docs/v21.6.2/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

e06dff53a5e2a88caff9735c076165a6a53f4c45960a8887410684e1fea6c7cb  node-v21.6.2-aix-ppc64.tar.gz
c7fa8788001eaac4bb250a84f6b3a918ebaa8016111ece95d59b513cf4a394dc  node-v21.6.2-arm64.msi
120c8205654c640865864dc464389b3ffe6d7ebe310dffdbe3fd8718a512e14f  node-v21.6.2-darwin-arm64.tar.gz
f8aa996b4e7700069892bc9ff28ddef3b3b3c8c952b929d1b148c943995970e3  node-v21.6.2-darwin-arm64.tar.xz
0f75d9b46b986100c6faeec040ee46adf4981eb6abb5dd63e7a6ca4868d280f4  node-v21.6.2-darwin-x64.tar.gz
5944de39bc7b8af229b0024d583ced7c76cee194ee9068a07d67372a606c5105  node-v21.6.2-darwin-x64.tar.xz
55d9a03dcfce682583eb5e7eec15f32ae95b28b6e805f31688b22a7bd71581b1  node-v21.6.2-headers.tar.gz
976500ffa659108fa2eb30daae2f1b96a34a97b2caa1db30802ac56edc2b237a  node-v21.6.2-headers.tar.xz
b8431985c53cc14e02cddf4c128d043c62af19023f908ebcdc1c6a683ee995f3  node-v21.6.2-linux-arm64.tar.gz
2606765f95262bcebb323e56a39b3be8db89863fbd83e06d2b5a08e41dc78f29  node-v21.6.2-linux-arm64.tar.xz
d6127be538ae57447fd40bac6ea124ad71cfd5a50b9343b781830cc92bc1a0c2  node-v21.6.2-linux-armv7l.tar.gz
ae33085c3d635f9488f47c56ee90fdf0dc9c1d0a520cfbe281c5b08d69e64da0  node-v21.6.2-linux-armv7l.tar.xz
2e265d86f9d20ba223d65ceadc0589b156439a5521cd9da6e34de5460a0d2195  node-v21.6.2-linux-ppc64le.tar.gz
b951f52db17b75a7bff0a2da2cefca3ba1e4dd7368b2b1280f39fcbecde0555c  node-v21.6.2-linux-ppc64le.tar.xz
7cba8c2b2338aaa05f5dec5d953d61cdf5219881a7c8d420f215e920a33c06fa  node-v21.6.2-linux-s390x.tar.gz
167bb0595478bae4c46b2248cae16890d24c2a9c92de7d0e27f9d1cafcad21ba  node-v21.6.2-linux-s390x.tar.xz
d4504dcbcd1a9ded42d86bc20a7e72d6d631e49dcf3f9c849c3b51b12f3f4544  node-v21.6.2-linux-x64.tar.gz
593dd28f5c78d797e76b730937b95fcdfc594f053a8756b1d0860a4555bed58e  node-v21.6.2-linux-x64.tar.xz
a0cdada31786f6ff1f82e8fd91bda23cd4f615a56acd3c9605cd468b60b8437a  node-v21.6.2-win-arm64.7z
a201948e5f0df6de6c4b42dbcb42d7a10d3cb5b6dbb7a40e3f4244644d3b3d1a  node-v21.6.2-win-arm64.zip
d450d170009d272c98765af3abf2bbc2903c1c08856f9e3730be03cc9d9b2bc5  node-v21.6.2-win-x64.7z
99bac3a930bd487e53c5a35b3e2f5ec102053316d7eb89f93273d916d57353a2  node-v21.6.2-win-x64.zip
44dee171378d7ac9967e772a8f114be5fdf59a163f65ec5faa7411c8be3bc961  node-v21.6.2-win-x86.7z
1701b32ba5315c794c2a64ef4a71e93ad2a6c109acf5b577d628413a7dc5cd04  node-v21.6.2-win-x86.zip
e081647df79c833e9d62e7edff5e9e01dbd5b78417dff6ef149e6384e8327bcf  node-v21.6.2-x64.msi
12960661f83a1618adf57e84eddcd1886edec452d74f27318efde8b92a25c91a  node-v21.6.2-x86.msi
6b5d7153dffec20487cbcb81d5ebaf97e6678eb463337e8429ba4e7b60754505  node-v21.6.2.pkg
9020fb36ec7e04f5032944c8422c2004350e9bfcd5e835ac3c90b74981c1f3e0  node-v21.6.2.tar.gz
191294d445d1e6800359acc8174529b1e18e102147dc5f596030d3dce96931e5  node-v21.6.2.tar.xz
ae7ccd1298e8871e61c1223a929ff482fa43d29aa284118798f01a73e40b2b29  win-arm64/node.exe
cf6082f3ffb45335d41566805c7b844082f36042fa7dc2dc6aecdc3ce0e7c79a  win-arm64/node.lib
8cb47e9ebda0efb8578382db82001308bcb2de95e0275e0bca3ff4f03de140b1  win-arm64/node_pdb.7z
cdb3d760f9aca9578135c25f299b382e074bfcb56b2d321acf278a0a76ec2eb4  win-arm64/node_pdb.zip
3f06d98986b4ddf7e9d258936bb7b8907c44c6e9a29c645a5aca04e5a26c0b53  win-x64/node.exe
cacf06da3d7f04d0a0a5a901bfbdbaf0950cc5f73febf7b2f451b27c7f6265b1  win-x64/node.lib
077739fded97d02d026db839aca840622c34b7e584efe294d40fc8d1bd9dc19a  win-x64/node_pdb.7z
54c3a6fea6f832716bfe5d918a56ca1767ae89163729c34d866c2623c0a90edf  win-x64/node_pdb.zip
0aaa0f4635253afe9660f64a862786db555961ecc7217b68fa120817c0c56a00  win-x86/node.exe
ca6545afa230d2abdbf8ba19065f77b727bde72dfa253c466d876d0b3a7ea4ee  win-x86/node.lib
cf24de1e7157ac4bac77fccb255fe295e8639a349d5c9f8726fe3e6b5ccf8afc  win-x86/node_pdb.7z
99f2345e0e737fbd7f8d94b4118d9dc820ebc198823de29324e1f1754f84c9e0  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQGzBAEBCAAdFiEEiQwI24V5Fi/uDfnbi+q0389VXvQFAmXM+XcACgkQi+q0389V
XvRIBQv+J+9C/N2aHF7mJJajSMJ67YZ1DjCwVt91loxcjnmt5aVx7LvluXkI3fBd
qzjx31Mvb45XeduyQaWgSzRgzanOJbXoQEQSqeeL4ubQV1Rq8UtTWl7/NZQQt6lX
ZY9Icg1BS577NwayCyKhoRjViq7waWkh07EfLZvMvl5Y85x/rIN6EF7YiscwhN10
oUEgNBS1txHy9grCHU42iNqKzI2jguPYLauvcj6QebZxFg1OhWp5q2lVDGckW5Sh
2pLKkvIRru+6oFZ848fu8sxxWxLZSbWSumVBbxmmoWGjWMK8zdCf3PbKBXkgm66y
yn4jnk5iDquottcf3BMcDBivcXcNv+x0DdWMq27YjVHfgxwhYYcDOBXlDtSY2zhd
njX2t3/96GrDIgZMNoBgGre7WWvInLYr7r5ejPoXVGbRd9AtOd65TbzsoKsJzrwG
LcVZ7ppfIGaV8T76q2tdMmfbvmGDOyJ6sXF2U4VzcqRVnfyIuIkM74S06BJIywBI
BvL6HeBI
=loKR
-----END PGP SIGNATURE-----
```
