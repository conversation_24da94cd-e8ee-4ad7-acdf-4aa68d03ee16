---
date: '2018-06-12T23:51:00.986Z'
category: release
title: Node v10.4.1 (Current)
layout: blog-post
author: <PERSON>
---

### Notable Changes

- **Fixes memory exhaustion DoS** (CVE-2018-7164): Fixes a bug introduced in 9.7.0 that increases the memory consumed when reading from the network into JavaScript using the net.Socket object directly as a stream.
- **http2**
  - (CVE-2018-7161): Fixes Denial of Service vulnerability by updating the http2 implementation to not crash under certain circumstances during cleanup
  - (CVE-2018-1000168): Fixes Denial of Service vulnerability by upgrading nghttp2 to 1.32.0
- **tls** (CVE-2018-7162): Fixes Denial of Service vulnerability by updating the TLS implementation to not crash upon receiving
- **n-api**: Prevent use-after-free in napi_delete_async_work

### Commits

- [[`1bbfe9a72b`](https://github.com/nodejs/node/commit/1bbfe9a72b)] - **build**: fix configure script for double-digits (<PERSON>) [#21183](https://github.com/nodejs/node/pull/21183)
- [[`4c90ee8fc6`](https://github.com/nodejs/node/commit/4c90ee8fc6)] - **deps**: update to nghttp2 1.32.0 (James M Snell) [nodejs-private/node-private#117](https://github.com/nodejs-private/node-private/pull/117)
- [[`e5c2f575b1`](https://github.com/nodejs/node/commit/e5c2f575b1)] - **deps**: patch V8 to 6.7.288.45 (Michaël Zasso) [#21192](https://github.com/nodejs/node/pull/21192)
- [[`03ded94ffe`](https://github.com/nodejs/node/commit/03ded94ffe)] - **deps**: patch V8 to 6.7.288.44 (Michaël Zasso) [#21146](https://github.com/nodejs/node/pull/21146)
- [[`4de7e0c96c`](https://github.com/nodejs/node/commit/4de7e0c96c)] - **deps,npm**: float node-gyp patch on npm (Rich Trott) [#21239](https://github.com/nodejs/node/pull/21239)
- [[`92d7b6c9a0`](https://github.com/nodejs/node/commit/92d7b6c9a0)] - **fs**: fix promises reads with pos \> 4GB (cjihrig) [#21148](https://github.com/nodejs/node/pull/21148)
- [[`8681402228`](https://github.com/nodejs/node/commit/8681402228)] - **http2**: fixup http2stream cleanup and other nits (James M Snell) [nodejs-private/node-private#115](https://github.com/nodejs-private/node-private/pull/115)
- [[`53f8563353`](https://github.com/nodejs/node/commit/53f8563353)] - **n-api**: back up env before async work finalize (Gabriel Schulhof) [#21129](https://github.com/nodejs/node/pull/21129)
- [[`9ba8ed1371`](https://github.com/nodejs/node/commit/9ba8ed1371)] - **src**: re-add `Realloc()` shrink after reading stream data (Anna Henningsen) [nodejs-private/node-private#128](https://github.com/nodejs-private/node-private/pull/128)
- [[`8e979482fa`](https://github.com/nodejs/node/commit/8e979482fa)] - **_Revert_** "**src**: restore stdio on program exit" (Evan Lucas) [#21257](https://github.com/nodejs/node/pull/21257)
- [[`cb5ec64956`](https://github.com/nodejs/node/commit/cb5ec64956)] - **src**: reset TTY mode before cleaning up resources (Anna Henningsen) [#21257](https://github.com/nodejs/node/pull/21257)
- [[`ae5567eaea`](https://github.com/nodejs/node/commit/ae5567eaea)] - **test**: add regression test for nghttp2 CVE-2018-1000168 (James M Snell) [nodejs-private/node-private#117](https://github.com/nodejs-private/node-private/pull/117)
- [[`e87bf625dd`](https://github.com/nodejs/node/commit/e87bf625dd)] - **test**: add tls write error regression test (Shigeki Ohtsu) [nodejs-private/node-private#127](https://github.com/nodejs-private/node-private/pull/127)
- [[`eea2bce58d`](https://github.com/nodejs/node/commit/eea2bce58d)] - **tls**: fix SSL write error handling (Anna Henningsen) [nodejs-private/node-private#127](https://github.com/nodejs-private/node-private/pull/127)
- [[`1e49eadd68`](https://github.com/nodejs/node/commit/1e49eadd68)] - **tools,gyp**: fix regex for version matching (Rich Trott) [#21216](https://github.com/nodejs/node/pull/21216)

Windows 32-bit Installer: https://nodejs.org/dist/v10.4.1/node-v10.4.1-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v10.4.1/node-v10.4.1-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v10.4.1/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v10.4.1/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v10.4.1/node-v10.4.1.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v10.4.1/node-v10.4.1-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v10.4.1/node-v10.4.1-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v10.4.1/node-v10.4.1-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v10.4.1/node-v10.4.1-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v10.4.1/node-v10.4.1-aix-ppc64.tar.gz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v10.4.1/node-v10.4.1-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v10.4.1/node-v10.4.1-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v10.4.1/node-v10.4.1-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v10.4.1/node-v10.4.1-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v10.4.1/node-v10.4.1.tar.gz \
Other release files: https://nodejs.org/dist/v10.4.1/ \
Documentation: https://nodejs.org/docs/v10.4.1/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

7b35df9310cf11c2c9b2ff27de5acb19ac2f75fc0f8d670da0d05d2a83b84bb2  node-v10.4.1-aix-ppc64.tar.gz
c232241c97e1f4659186205d50b44132e62b61cdc517f1fb86905a21d03e9189  node-v10.4.1-darwin-x64.tar.gz
a5f0148e5aca077295d7c065f1f99adbc0bd4bb31c2c683a134725ecf7d632d3  node-v10.4.1-darwin-x64.tar.xz
f672862be1d9c406a47a38520e9fbf329f48bc9db56cffa62fca76098857725d  node-v10.4.1-headers.tar.gz
862f9470cd8aeebe965a3feffd4d97fbe99a47f60494633bdccd152a9c140b80  node-v10.4.1-headers.tar.xz
f61110447544b5ada4b5523b4ccd8a2f5000709e2f9dc6f1f3594f556a068627  node-v10.4.1-linux-arm64.tar.gz
c00b75a28eb69e4238c9d560f50da3652395ba7bfa6e325d5a2b0cd0926070f7  node-v10.4.1-linux-arm64.tar.xz
18093075b38b026a93d4560487d7ce853091835dd58137f04b685ccd9a564746  node-v10.4.1-linux-armv6l.tar.gz
1260aef50e5eb3889429692c55555c4ccd4c49c647ebf2d3be3d19e71e355c0d  node-v10.4.1-linux-armv6l.tar.xz
54a3014e2b5baf2d32b99fcd8d5f320457c2f28da79ae7284494df87da042864  node-v10.4.1-linux-armv7l.tar.gz
b10d01061d41e6813e1fa9c3fb52401d67a1fe645ce351fe0c5ad097049eb5b5  node-v10.4.1-linux-armv7l.tar.xz
ebadd3b950a5b4a49827dbd2be1d16e7e21fae98630eaa0372efdae870343fb1  node-v10.4.1-linux-ppc64le.tar.gz
1607290a43a64fb0d02b85b1987e7232b8c95737deec57de82bae6757e9f4bc2  node-v10.4.1-linux-ppc64le.tar.xz
22486e1dd914c3769964d44d192ad46ca474247eb56fbb7f50a3c7efebf8c8a5  node-v10.4.1-linux-s390x.tar.gz
4d3f689fe22ca247cfdca4c73ac3eff87cccf1d88d5d6549fc391d90a94992f9  node-v10.4.1-linux-s390x.tar.xz
1271aa1d889ffe5b9d0ccdb51faabeb60bf27859a5e9401d47f9eead4644991c  node-v10.4.1-linux-x64.tar.gz
6196daea2b291cdb865b3597e6b819b13068cb2c9dbf27cb150256c557a81082  node-v10.4.1-linux-x64.tar.xz
2641d8615e82661e12d93b64085928073e04eb0cc7807594b429e732c9e3fd5d  node-v10.4.1.pkg
e594895cec32bd62730095d6735fc34d34e05dd3269883d62441dea68db754fd  node-v10.4.1-sunos-x64.tar.gz
26c96034ae8f941bf2eb7d8e442fc3e797c01f49f739bf97587bb238bf32cd7b  node-v10.4.1-sunos-x64.tar.xz
37f0c44399a0955dafb2162308064382883bbf2e4e8ff2e50aa062b081aad87f  node-v10.4.1.tar.gz
a5bf584b52f992b6ce31d8afd8c468945a772431575ad868e4e787f390ad8044  node-v10.4.1.tar.xz
1983061a75ee23c2bd00f817ddf43d0cffca951f5404901527ddfc0bf93f55c6  node-v10.4.1-win-x64.7z
70e0b9f0036f878884fdfc585c1001a439508d1d4e6448c4aced60274a2dc191  node-v10.4.1-win-x64.zip
fcf138fd11f435cfa49d24940365392090251ad04268dbfeb7c2b6dc87579a12  node-v10.4.1-win-x86.7z
19f0739fb72cd029b30ab29e7e7e19849fd936dac41915223ba22d6801f911e1  node-v10.4.1-win-x86.zip
e7aecc7f6682550f988f2363dac908fd42ecd1fba7daa5ab2e7ea054cbb1b9e2  node-v10.4.1-x64.msi
cb4082d7c435bdc17cdbf7ba4af04b7fd88e64978cd872d28486662246b7c949  node-v10.4.1-x86.msi
093244fb6d4c464c710e58ca0a1d1011945b7bf8337c4c45e34a49231e86d69d  win-x64/node.exe
8a716197eb364e6fb82e27ef60cbd7464ee33761292e02701b46b5a191a8a42b  win-x64/node.lib
75bbbfba8ccb8157f9d3624b4beb032a39017faaca18d6156f11e71d5e2b1ec7  win-x64/node_pdb.7z
cc5cda46cb63dacb91b52aa5d518fdf0690c834ebf635401b1a1048195d9a97b  win-x64/node_pdb.zip
02f6df2e1299d1a9369e41e4ca7dc01d0219ad7f7b0dbbea627770a23458843c  win-x86/node.exe
03afed5670805f78a70ac9b65edc19b9bf7b7e70132b837a88a2c26979a9f9a1  win-x86/node.lib
92f7dc828d05b28987e0e18aeda0ae8335342eba91a4e69b3cb29a22cc140d91  win-x86/node_pdb.7z
b6681fd9f50b9c66773fa3bcf11560c99d6d9f9c7c95d502b05f5671276e16d9  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEua6ZBf/XgD8lcUZhtjtTWkwgbKkFAlsgVnMACgkQtjtTWkwg
bKktFhAA2P8ksvqtA5aI48/4KDV+WWIHjif+zVfD1GCz+wEfw61+6HV8qRacLOyR
Ql65gNA7V8cz6IMsE5rNpp3/K6GoBRyOXFH8R5KFcc73/3Yt/kyfhS3UrsGHJYXx
QEa8cNWBxnKzKRaGddqE6LHy6IS04PxzbDL9XTZxoH01x/ppMlS2aBGKfgLTBrRR
aMFygQCvEGRODDkQ13PdyYESo+dJFzCgIu1Pva80kCVSlqejGQr03Rq7ormICLR5
OVabVzQqED0U1mgLTZSaUNPVtpCYUvuy0N2V7DKABnaum2TNPlc/KjEjTA7JW7PP
WW/26S2XAdA9hv/XJD7vXuHuCY0zeUHrH3g5zxlgDVcnzfb1gNIjTJ+GpkGrqscV
MqdASLIXvJUld84AkqvrRZOHGrvZXidA8Tg6R8HDE1nX4N/y8g0vAHOfhaCwdFJO
QBtlsfuiELo7zn9dSSMsUFP8S63LRmhtdnVlC1780HPIgU6nStHt1I2CXnqeXc5m
efC4BS8aMrk1aTcDViG8wG5G1tf1v8YAexSwoHaI/JbUwBz9dXKBC4gv/M6nBf1A
uvNNtBxc3AVWe+nwlaGJNI5ydkSW0bDS7OgS64tuOTJZZpQK4QbSnzwk+fW1ICNW
709tw1+l92Ys9Ze5iUekABsD1Pww2jbJ2SzOuQ3nbTl/cmup/uc=
=rLAS
-----END PGP SIGNATURE-----

```
