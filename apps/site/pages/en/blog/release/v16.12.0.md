---
date: '2021-10-20T14:44:30.011Z'
category: release
title: Node v16.12.0 (Current)
layout: blog-post
author: <PERSON>
---

### Notable Changes

#### Experimental ESM Loader Hooks API

Node.js ESM Loader hooks have been consolidated to represent the steps involved needed to facilitate future loader chaining:

1. `resolve`: `resolve` \[+ `getFormat`\]
1. `load`: `getFormat` + `getSource` + `transformSource`

For consistency, `getGlobalPreloadCode` has been renamed to `globalPreload`.

A loader exporting obsolete hook(s) will trigger a single deprecation warning (per loader) listing the errant hooks.

Contributed by <PERSON>, <PERSON>, and <PERSON> - https://github.com/nodejs/node/pull/37468

#### Other Notable Changes

- \[[`8fdabcb918`](https://github.com/nodejs/node/commit/8fdabcb918)] - **deps**: upgrade npm to 8.1.0 (npm team) [#40463](https://github.com/nodejs/node/pull/40463)
- \[[`d1d9f2de30`](https://github.com/nodejs/node/commit/d1d9f2de30)] - **doc**: deprecate (doc-only) http abort related (dr-js) [#36670](https://github.com/nodejs/node/pull/36670)
- \[[`4116b6c907`](https://github.com/nodejs/node/commit/4116b6c907)] - **(SEMVER-MINOR)** **vm**: add support for import assertions in dynamic imports (Antoine du Hamel) [#40249](https://github.com/nodejs/node/pull/40249)

### Commits

- \[[`8bb3951e41`](https://github.com/nodejs/node/commit/8bb3951e41)] - **build**: remove duplicate check for authors.yml (Rich Trott) [#40393](https://github.com/nodejs/node/pull/40393)
- \[[`2de57edced`](https://github.com/nodejs/node/commit/2de57edced)] - **build**: make scripts in gyp run with right python (Cheng Zhao) [#39730](https://github.com/nodejs/node/pull/39730)
- \[[`a8926d199d`](https://github.com/nodejs/node/commit/a8926d199d)] - **crypto**: remove incorrect constructor invocation (gc) [#40300](https://github.com/nodejs/node/pull/40300)
- \[[`8fdabcb918`](https://github.com/nodejs/node/commit/8fdabcb918)] - **deps**: upgrade npm to 8.1.0 (npm team) [#40463](https://github.com/nodejs/node/pull/40463)
- \[[`dca5ac1539`](https://github.com/nodejs/node/commit/dca5ac1539)] - **deps**: suppress zlib compiler warnings (Daniel Bevenius) [#40343](https://github.com/nodejs/node/pull/40343)
- \[[`91c3bf6a7f`](https://github.com/nodejs/node/commit/91c3bf6a7f)] - **deps**: upgrade Corepack to 0.10 (Maël Nison) [#40374](https://github.com/nodejs/node/pull/40374)
- \[[`7e02124a06`](https://github.com/nodejs/node/commit/7e02124a06)] - **dgram**: add `nread` assertion to `UDPWrap::OnRecv` (Darshan Sen) [#40295](https://github.com/nodejs/node/pull/40295)
- \[[`2d409ed29e`](https://github.com/nodejs/node/commit/2d409ed29e)] - **dns**: refactor and use validators (Voltrex) [#40022](https://github.com/nodejs/node/pull/40022)
- \[[`dc7291dab8`](https://github.com/nodejs/node/commit/dc7291dab8)] - **doc**: remove ESLint comments which were breaking the CJS/ESM toggles (Mark Skelton) [#40408](https://github.com/nodejs/node/pull/40408)
- \[[`85b7385115`](https://github.com/nodejs/node/commit/85b7385115)] - **doc**: add pronouns for tniessen to README (Tobias Nießen) [#40412](https://github.com/nodejs/node/pull/40412)
- \[[`1d5857c9f4`](https://github.com/nodejs/node/commit/1d5857c9f4)] - **doc**: format changelogs (Rich Trott) [#40388](https://github.com/nodejs/node/pull/40388)
- \[[`5eb9402b50`](https://github.com/nodejs/node/commit/5eb9402b50)] - **doc**: fix missing variable in deepStrictEqual example (OliverOdo) [#40396](https://github.com/nodejs/node/pull/40396)
- \[[`6f77d1a1d5`](https://github.com/nodejs/node/commit/6f77d1a1d5)] - **doc**: fix asyncLocalStorage.run() description (Constantine Kim) [#40381](https://github.com/nodejs/node/pull/40381)
- \[[`93a48e02dc`](https://github.com/nodejs/node/commit/93a48e02dc)] - **doc**: fix typos in n-api docs (Ignacio Carbajo) [#40402](https://github.com/nodejs/node/pull/40402)
- \[[`fb7afb91c2`](https://github.com/nodejs/node/commit/fb7afb91c2)] - **doc**: format doc/guides using format-md task (Rich Trott) [#40358](https://github.com/nodejs/node/pull/40358)
- \[[`6c091c7878`](https://github.com/nodejs/node/commit/6c091c7878)] - **doc**: improve phrasing in fs.md (Arslan Ali) [#40255](https://github.com/nodejs/node/pull/40255)
- \[[`38d81380ac`](https://github.com/nodejs/node/commit/38d81380ac)] - **doc**: add link to core promises tracking issue (Michael Dawson) [#40355](https://github.com/nodejs/node/pull/40355)
- \[[`71a94aa82a`](https://github.com/nodejs/node/commit/71a94aa82a)] - **doc**: correct ESM load hook table header (Jacob Smith) [#40234](https://github.com/nodejs/node/pull/40234)
- \[[`5b074affb4`](https://github.com/nodejs/node/commit/5b074affb4)] - **doc**: fix typo in esm.md (Mason Malone) [#40273](https://github.com/nodejs/node/pull/40273)
- \[[`3b3aaa0a37`](https://github.com/nodejs/node/commit/3b3aaa0a37)] - **doc**: fix typo in ESM example (Tobias Nießen) [#40275](https://github.com/nodejs/node/pull/40275)
- \[[`f848553fb8`](https://github.com/nodejs/node/commit/f848553fb8)] - **doc**: assign missing deprecation number (Michaël Zasso) [#40324](https://github.com/nodejs/node/pull/40324)
- \[[`d1d9f2de30`](https://github.com/nodejs/node/commit/d1d9f2de30)] - **doc**: deprecate (doc-only) http abort related (dr-js) [#36670](https://github.com/nodejs/node/pull/36670)
- \[[`1ef2cf8413`](https://github.com/nodejs/node/commit/1ef2cf8413)] - **doc**: anchor link parity between markdown and html-generated docs (foxxyz) [#39304](https://github.com/nodejs/node/pull/39304)
- \[[`3743406b0a`](https://github.com/nodejs/node/commit/3743406b0a)] - **(SEMVER-MINOR)** **esm**: consolidate ESM loader hooks (Jacob Smith) [#37468](https://github.com/nodejs/node/pull/37468)
- \[[`168020e1c8`](https://github.com/nodejs/node/commit/168020e1c8)] - **lib**: refactor to use let (gdccwxx) [#40364](https://github.com/nodejs/node/pull/40364)
- \[[`bcd59d70bb`](https://github.com/nodejs/node/commit/bcd59d70bb)] - **meta**: consolidate AUTHORS entries for gabrielschulhof (Rich Trott) [#40420](https://github.com/nodejs/node/pull/40420)
- \[[`80b4245db8`](https://github.com/nodejs/node/commit/80b4245db8)] - **meta**: consolidate AUTHORS information for geirha (Rich Trott) [#40406](https://github.com/nodejs/node/pull/40406)
- \[[`93cecb4700`](https://github.com/nodejs/node/commit/93cecb4700)] - **meta**: consolidate duplicate AUTHORS entries for hassaanp (Rich Trott) [#40391](https://github.com/nodejs/node/pull/40391)
- \[[`fff3135909`](https://github.com/nodejs/node/commit/fff3135909)] - **meta**: update AUTHORS (Node.js GitHub Bot) [#40392](https://github.com/nodejs/node/pull/40392)
- \[[`122481713d`](https://github.com/nodejs/node/commit/122481713d)] - **meta**: consolidate AUTHORS entry for thw0rted (Rich Trott) [#40387](https://github.com/nodejs/node/pull/40387)
- \[[`7f50313fcc`](https://github.com/nodejs/node/commit/7f50313fcc)] - **meta**: update label-pr-config (Mestery) [#40199](https://github.com/nodejs/node/pull/40199)
- \[[`5668182665`](https://github.com/nodejs/node/commit/5668182665)] - **meta**: use .mailmap to consolidate AUTHORS entries for ide (Rich Trott) [#40367](https://github.com/nodejs/node/pull/40367)
- \[[`bc86084a3e`](https://github.com/nodejs/node/commit/bc86084a3e)] - **net**: check if option is undefined (Daijiro Wachi) [#40344](https://github.com/nodejs/node/pull/40344)
- \[[`4564a93e5e`](https://github.com/nodejs/node/commit/4564a93e5e)] - **net**: remove unused ObjectKeys (Daijiro Wachi) [#40344](https://github.com/nodejs/node/pull/40344)
- \[[`dbb2e6f429`](https://github.com/nodejs/node/commit/dbb2e6f429)] - **net**: check objectMode first and then readble || writable (Daijiro Wachi) [#40344](https://github.com/nodejs/node/pull/40344)
- \[[`a672be57c8`](https://github.com/nodejs/node/commit/a672be57c8)] - **net**: throw error to object mode in Socket (Daijiro Wachi) [#40344](https://github.com/nodejs/node/pull/40344)
- \[[`faf9e28c36`](https://github.com/nodejs/node/commit/faf9e28c36)] - **src**: remove usage of `AllocatedBuffer` from `stream_*` (Darshan Sen) [#40293](https://github.com/nodejs/node/pull/40293)
- \[[`857af2ba99`](https://github.com/nodejs/node/commit/857af2ba99)] - **src**: add missing initialization (Michael Dawson) [#40370](https://github.com/nodejs/node/pull/40370)
- \[[`2bfa87edbc`](https://github.com/nodejs/node/commit/2bfa87edbc)] - **stream**: fix fromAsyncGen (Robert Nagy) [#40499](https://github.com/nodejs/node/pull/40499)
- \[[`1e15137e71`](https://github.com/nodejs/node/commit/1e15137e71)] - **test**: replace common port with specific number (Daijiro Wachi) [#40344](https://github.com/nodejs/node/pull/40344)
- \[[`6f6b99c302`](https://github.com/nodejs/node/commit/6f6b99c302)] - **test**: fix typos in whatwg-webstreams explanations (Tobias Nießen) [#40389](https://github.com/nodejs/node/pull/40389)
- \[[`641b1bb052`](https://github.com/nodejs/node/commit/641b1bb052)] - **test**: add test for readStream.path when fd is specified (Qingyu Deng) [#40359](https://github.com/nodejs/node/pull/40359)
- \[[`07dae7ff50`](https://github.com/nodejs/node/commit/07dae7ff50)] - **test**: replace .then chains with await (gdccwxx) [#40348](https://github.com/nodejs/node/pull/40348)
- \[[`d8a36ee1de`](https://github.com/nodejs/node/commit/d8a36ee1de)] - **test**: fix "test/common/debugger" identify async function (gdccwxx) [#40348](https://github.com/nodejs/node/pull/40348)
- \[[`13d6a56c7d`](https://github.com/nodejs/node/commit/13d6a56c7d)] - **test**: improve test coverage of `fs.ReadStream` with `FileHandle` (Antoine du Hamel) [#40018](https://github.com/nodejs/node/pull/40018)
- \[[`50f91ab059`](https://github.com/nodejs/node/commit/50f91ab059)] - **tools**: udpate @babel/eslint-parser (Rich Trott) [#40394](https://github.com/nodejs/node/pull/40394)
- \[[`3611073145`](https://github.com/nodejs/node/commit/3611073145)] - **tools**: remove @babel/plugin-syntax-import-assertions (Rich Trott) [#40394](https://github.com/nodejs/node/pull/40394)
- \[[`b72d693a3a`](https://github.com/nodejs/node/commit/b72d693a3a)] - **tools**: remove @bable/plugin-syntax-class-properties (Rich Trott) [#40394](https://github.com/nodejs/node/pull/40394)
- \[[`d6a99b77da`](https://github.com/nodejs/node/commit/d6a99b77da)] - **tools**: remove @babel/plugin-syntax-top-level-await (Rich Trott) [#40394](https://github.com/nodejs/node/pull/40394)
- \[[`d9157aa5fe`](https://github.com/nodejs/node/commit/d9157aa5fe)] - **tools**: update ESLint to 8.0.0 (Rich Trott) [#40394](https://github.com/nodejs/node/pull/40394)
- \[[`43b97c7984`](https://github.com/nodejs/node/commit/43b97c7984)] - **tools**: prepare ESLint rules for 8.0.0 requirements (Rich Trott) [#40394](https://github.com/nodejs/node/pull/40394)
- \[[`282b6eb4b0`](https://github.com/nodejs/node/commit/282b6eb4b0)] - **tools**: fix ESLint update scripts (Rich Trott) [#40394](https://github.com/nodejs/node/pull/40394)
- \[[`c3a744f7bf`](https://github.com/nodejs/node/commit/c3a744f7bf)] - **tools**: warn about duplicates when generating AUTHORS file (Rich Trott) [#40304](https://github.com/nodejs/node/pull/40304)
- \[[`7733b5e55d`](https://github.com/nodejs/node/commit/7733b5e55d)] - **typings**: define types for os binding (Michaël Zasso) [#40222](https://github.com/nodejs/node/pull/40222)
- \[[`ca9a854877`](https://github.com/nodejs/node/commit/ca9a854877)] - **typings**: add missing types to options and util bindings (Michaël Zasso) [#40222](https://github.com/nodejs/node/pull/40222)
- \[[`c3a7a0bd59`](https://github.com/nodejs/node/commit/c3a7a0bd59)] - **typings**: define types for timers binding (Michaël Zasso) [#40222](https://github.com/nodejs/node/pull/40222)
- \[[`65b51d05fa`](https://github.com/nodejs/node/commit/65b51d05fa)] - **typings**: fix declaration of primordials (Michaël Zasso) [#40222](https://github.com/nodejs/node/pull/40222)
- \[[`5f3f3a5128`](https://github.com/nodejs/node/commit/5f3f3a5128)] - **v8**: remove --harmony-top-level-await (Geoffrey Booth) [#40226](https://github.com/nodejs/node/pull/40226)
- \[[`4116b6c907`](https://github.com/nodejs/node/commit/4116b6c907)] - **(SEMVER-MINOR)** **vm**: add support for import assertions in dynamic imports (Antoine du Hamel) [#40249](https://github.com/nodejs/node/pull/40249)

Windows 32-bit Installer: https://nodejs.org/dist/v16.12.0/node-v16.12.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v16.12.0/node-v16.12.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v16.12.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v16.12.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v16.12.0/node-v16.12.0.pkg \
macOS Apple Silicon 64-bit Binary: https://nodejs.org/dist/v16.12.0/node-v16.12.0-darwin-arm64.tar.gz \
macOS Intel 64-bit Binary: https://nodejs.org/dist/v16.12.0/node-v16.12.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v16.12.0/node-v16.12.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v16.12.0/node-v16.12.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v16.12.0/node-v16.12.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v16.12.0/node-v16.12.0-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v16.12.0/node-v16.12.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v16.12.0/node-v16.12.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v16.12.0/node-v16.12.0.tar.gz \
Other release files: https://nodejs.org/dist/v16.12.0/ \
Documentation: https://nodejs.org/docs/v16.12.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

d45cc55607bdc730aa6b930be4a067bc56568ff92162608f0f5983879024cd5b  node-v16.12.0-aix-ppc64.tar.gz
1d681c528205f56531084a09e9648586f91e68726ee73851c8e4b1098df2f603  node-v16.12.0-darwin-arm64.tar.gz
16446b03ce381d396ae14fb821084f4a253545667f233a8857a58195b540b49a  node-v16.12.0-darwin-arm64.tar.xz
35ee05c9392742f934a3058fa64837b14e184b26aa9bd621ec499a83f9fdfe67  node-v16.12.0-darwin-x64.tar.gz
260db12cb00b8daf845577e9ba66b9f7f191e9038f2688689d9b188dfdabfc61  node-v16.12.0-darwin-x64.tar.xz
8e42d46d4382cbc1b2eb11bdbc609667569b55da3690b8dbeb64caa95d2a2b5a  node-v16.12.0-headers.tar.gz
af34cd2110b02a2709882d6196a4e96eedd64a3d6049bc5751d755c8d742a605  node-v16.12.0-headers.tar.xz
2abb224a6d9880d459ed64a02876b5843ca891978b072e7516431b15142a472c  node-v16.12.0-linux-arm64.tar.gz
b5d6b2e70e4556866e4015997d0915d0eca19576f011e6e8463711da151ed7bd  node-v16.12.0-linux-arm64.tar.xz
18dc901dc9c585360fc0dd1ac023eaac61eeb46464a59e821b2171c487a46a0e  node-v16.12.0-linux-armv7l.tar.gz
a9409df88c328726842f523308014371b603cfa7afdd446a6ad02d464f40c790  node-v16.12.0-linux-armv7l.tar.xz
48c5f2fdaf9938d49e24248727249fbcd4faedbdc9d5e3948cfd00ffb14ba47d  node-v16.12.0-linux-ppc64le.tar.gz
9ec2b8d44827dbd1f4f7542c61f169ee4288da1c5a2150249bf38a0e9d330bd3  node-v16.12.0-linux-ppc64le.tar.xz
5313dd3023d53405fcb272af8dba24893d2102ec928bcc817e2a4c571be62fe2  node-v16.12.0-linux-s390x.tar.gz
0b0dc48f1e160006dfd56bf32333102044b1548f0b7a86e5a9378764f574c67c  node-v16.12.0-linux-s390x.tar.xz
1f41d5b68ca39eb2e76dad4e5beb8de8b0a1498773e3e53b80c03e42202969fb  node-v16.12.0-linux-x64.tar.gz
e0e1a54b34db95ca49bb8561ad736750222f54a41aad2c876b9355cacff42ba0  node-v16.12.0-linux-x64.tar.xz
0f777eda63df1f6bc176edd98f3b25252f096a4cf8d52001c1ee1b06d0c6f3b9  node-v16.12.0.pkg
bceb8098e276c6bb6b3e88223ed378a6c786491b6a3f6b22ae3fd8774724a9cb  node-v16.12.0.tar.gz
5f620a6a400901a6565aa0c07309cde3aab3dbaa765cecb934241de520d36bac  node-v16.12.0.tar.xz
3f19469ea095173d998d8a11b3726b04629b02e3e608790475515175d29573b4  node-v16.12.0-win-x64.7z
433206b42522e65ed6c0f30f1d665f336c2bdcfd200286da639c4e91ea602870  node-v16.12.0-win-x64.zip
b45d0f934bc6afd4716cf048538df2550de39024ac6e522a15af36bf2114105f  node-v16.12.0-win-x86.7z
ef2ff38ce68acf8d930bc8cbbc1be82405fa1ac85490a7f92a1ddeba00fa67a6  node-v16.12.0-win-x86.zip
d269299d5feb29bc9d5713e9b87f0154ff857bbe6510c76a553568a1bc22badc  node-v16.12.0-x64.msi
adc0b407f5b856fad4e36ac7b22def93cf01b6f7576404da7c5b6a3992a82236  node-v16.12.0-x86.msi
75e88894b274d91f98bbd2109dd1acfea6f658cfd2045f066dca7145ad745427  win-x64/node.exe
7c94657df6918a77dc8edefaf3b5415dbfb9eb83a88c17d216a48c8c36fcc58d  win-x64/node.lib
1edef4e0323fbe13b4c0b97d20fac39a3fcf6b3137933edea0d16caf78c68cec  win-x64/node_pdb.7z
ee65351b06218b3390e639ff9a5ea51048bcd97d719c52806455d635f77ea10e  win-x64/node_pdb.zip
d6fd4b983a0f61bd2297d07881241c2629f8326c7445e5d151a09a3fb960f039  win-x86/node.exe
8090f51a19ff2d5e765920262a4367203be2e69e64ac3725e4e14dd034c98443  win-x86/node.lib
188d915991ab21e101a9e321dad7628e6b9acf2be0b1874ae6450225e9ad2d24  win-x86/node_pdb.7z
eab8b47522fdeb6ab3a46e26eda7ae5c51729875b0b42f11e0d24d632626a779  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEyC+jrhy+3Gvka5NgxDzsRcF6uTwFAmFwKrgACgkQxDzsRcF6
uTyvHw/8D4tGIxl7mccgF17epMxwULXWQVq43A0oonFXPb9LzWXAVzMkJ70YCGzZ
EkP2YMjIW/mf/jcE3UoI14loHMR8Y2vk10x8/Y2I5o5D3naWjWEOLyAXb7W8476J
LZEWZacS0vQOoCT+qEiRQSDjbJeY+4cBsNsslh5UjdvMs9j1zpU8mPkDLSRMPc/L
l9i/lXKnSx+Surb3taRMphgkTGf8EreBoTjg73u283CR3pRlRj0LVE+a+umXcgFf
EMDWm+hwN7j8zv6Rm3W0Rw9RCTTZUT7jyuV5bjqoBgkCbyy+V/Za8X8yCOGX9kK4
BV8opcAq1fIatfCUQsHjWGbIN8T1NjtNfjND9sjEfV9SWT7EFue3DaPnbS4Wh4P2
pfERePBMyHI47Ltug6XbNvFmr7huzpe/FUj0pvsxaL27XnQNPmiBQnWNDevPG0vg
IKZ2op/4KtnLKDi5cWfNyvs0FL1XxV5jeB3aFDilOCMhUEt+KCrT7C0DTrgDrotT
VjgkkNmLtUZR93hMrSkfhqn2VEUui3YyeGtazerSetTfbKII19cvWsOEBSQL9Vsa
WD29bsanXhEHL04c8qwb0Wct5tjBbV1Ol7jQewigOJoOAYSDtH4hx/1MZ00MVad3
mfZx8vFjmzg87i26ZV/bTyxjCyaz0IFc4ghuum4OWHrod7cOPAg=
=NJOg
-----END PGP SIGNATURE-----

```
