---
date: '2021-05-19T11:00:42.622Z'
category: release
title: Node v16.2.0 (Current)
layout: blog-post
author: <PERSON><PERSON><PERSON>
---

### Notable Changes

- [[`36b948560c`](https://github.com/nodejs/node/commit/36b948560c)] - **(SEMVER-MINOR)** **async_hooks**: use new v8::Context PromiseHook API (<PERSON>) [#36394](https://github.com/nodejs/node/pull/36394)
- [[`c0deeeacb2`](https://github.com/nodejs/node/commit/c0deeeacb2)] - **lib**: support setting process.env.TZ on windows (James M <PERSON>nell) [#38642](https://github.com/nodejs/node/pull/38642)
- [[`4c4902748c`](https://github.com/nodejs/node/commit/4c4902748c)] - **(SEMVER-MINOR)** **module**: add support for `URL` to `import.meta.resolve` (<PERSON>) [#38587](https://github.com/nodejs/node/pull/38587)
- [[`c182198c44`](https://github.com/nodejs/node/commit/c182198c44)] - **(SEMVER-MINOR)** **process**: add `'worker'` event (James M Snell) [#38659](https://github.com/nodejs/node/pull/38659)
- [[`fbf02e3198`](https://github.com/nodejs/node/commit/fbf02e3198)] - **(SEMVER-MINOR)** **util**: add util.types.isKeyObject and util.types.isCryptoKey (Filip Skokan) [#38619](https://github.com/nodejs/node/pull/38619)

### Commits

- [[`36b948560c`](https://github.com/nodejs/node/commit/36b948560c)] - **(SEMVER-MINOR)** **async_hooks**: use new v8::Context PromiseHook API (Stephen Belanger) [#36394](https://github.com/nodejs/node/pull/36394)
- [[`dcae03203e`](https://github.com/nodejs/node/commit/dcae03203e)] - **buffer**: remove TODOs in `atob` / `btoa` (Khaidi Chu) [#38548](https://github.com/nodejs/node/pull/38548)
- [[`48b557e904`](https://github.com/nodejs/node/commit/48b557e904)] - **buffer**: remove unreachable code (Rongjian Zhang) [#38537](https://github.com/nodejs/node/pull/38537)
- [[`b0df28dea5`](https://github.com/nodejs/node/commit/b0df28dea5)] - **build**: add workaround for V8 builds (Richard Lau) [#38632](https://github.com/nodejs/node/pull/38632)
- [[`3bb12db255`](https://github.com/nodejs/node/commit/3bb12db255)] - **build**: remove dependency on `distutils.spawn` (Richard Lau) [#38600](https://github.com/nodejs/node/pull/38600)
- [[`10aaf30da1`](https://github.com/nodejs/node/commit/10aaf30da1)] - **build**: add missing torque output sources (Richard Lau) [#38576](https://github.com/nodejs/node/pull/38576)
- [[`03b4a3a5bf`](https://github.com/nodejs/node/commit/03b4a3a5bf)] - **build**: compile with -std=gnu++14 (Darshan Sen) [#38504](https://github.com/nodejs/node/pull/38504)
- [[`4296591154`](https://github.com/nodejs/node/commit/4296591154)] - **build,src,test,doc**: enable FIPS for OpenSSL 3.0 (Daniel Bevenius) [#38633](https://github.com/nodejs/node/pull/38633)
- [[`36bb8daba5`](https://github.com/nodejs/node/commit/36bb8daba5)] - **crypto**: forbid NODE-ED25519 and NODE-ED448 "raw" key export (Filip Skokan) [#38668](https://github.com/nodejs/node/pull/38668)
- [[`36bb7243ff`](https://github.com/nodejs/node/commit/36bb7243ff)] - **debugger**: refactor `inspect_repl` to use primordials (Antoine du Hamel) [#38551](https://github.com/nodejs/node/pull/38551)
- [[`16a6c8d5a6`](https://github.com/nodejs/node/commit/16a6c8d5a6)] - **debugger**: refactor to use internal modules (Antoine du Hamel) [#38550](https://github.com/nodejs/node/pull/38550)
- [[`11dd9a6838`](https://github.com/nodejs/node/commit/11dd9a6838)] - **debugger**: disable only the lint rules required by current file state (Rich Trott) [#38529](https://github.com/nodejs/node/pull/38529)
- [[`e79f540fa0`](https://github.com/nodejs/node/commit/e79f540fa0)] - **debugger**: avoid non-ASCII char in code file (Rich Trott) [#38529](https://github.com/nodejs/node/pull/38529)
- [[`d9867b9358`](https://github.com/nodejs/node/commit/d9867b9358)] - **debugger**: wrap lines longer than 80 chars (Rich Trott) [#38529](https://github.com/nodejs/node/pull/38529)
- [[`352a600142`](https://github.com/nodejs/node/commit/352a600142)] - **debugger**: rename inspector-cli test module to debugger (Rich Trott) [#38530](https://github.com/nodejs/node/pull/38530)
- [[`608d0e11f3`](https://github.com/nodejs/node/commit/608d0e11f3)] - **deps**: upgrade npm to 7.13.0 (Ruy Adorno) [#38682](https://github.com/nodejs/node/pull/38682)
- [[`5c71f49d3f`](https://github.com/nodejs/node/commit/5c71f49d3f)] - **deps**: upgrade npm to 7.12.1 (Ruy Adorno) [#38628](https://github.com/nodejs/node/pull/38628)
- [[`ec2dbfb200`](https://github.com/nodejs/node/commit/ec2dbfb200)] - **deps**: patch V8 to 9.0.257.25 (Michaël Zasso) [#38556](https://github.com/nodejs/node/pull/38556)
- [[`ab298723b5`](https://github.com/nodejs/node/commit/ab298723b5)] - **(SEMVER-MINOR)** **deps**: V8: cherry-pick fa4cb172cde2 (Stephen Belanger) [#36394](https://github.com/nodejs/node/pull/36394)
- [[`a84e9b3e7d`](https://github.com/nodejs/node/commit/a84e9b3e7d)] - **(SEMVER-MINOR)** **deps**: V8: cherry-pick 4c074516397b (Stephen Belanger) [#36394](https://github.com/nodejs/node/pull/36394)
- [[`043b1aaa3f`](https://github.com/nodejs/node/commit/043b1aaa3f)] - **(SEMVER-MINOR)** **deps**: V8: cherry-pick 5f4413194480 (Stephen Belanger) [#36394](https://github.com/nodejs/node/pull/36394)
- [[`1a104bac74`](https://github.com/nodejs/node/commit/1a104bac74)] - **(SEMVER-MINOR)** **deps**: V8: cherry-pick 272445f10927 (Stephen Belanger) [#36394](https://github.com/nodejs/node/pull/36394)
- [[`827ae05538`](https://github.com/nodejs/node/commit/827ae05538)] - **(SEMVER-MINOR)** **deps**: V8: backport c0fceaa0669b (Stephen Belanger) [#36394](https://github.com/nodejs/node/pull/36394)
- [[`f31a6114a4`](https://github.com/nodejs/node/commit/f31a6114a4)] - **deps**: V8: cherry-pick 530080c44af2 (Milad Fa) [#38489](https://github.com/nodejs/node/pull/38489)
- [[`4001dd28ba`](https://github.com/nodejs/node/commit/4001dd28ba)] - **dgram**: extract cluster lazy loading method to make it testable (Rongjian Zhang) [#38563](https://github.com/nodejs/node/pull/38563)
- [[`a0dc194e31`](https://github.com/nodejs/node/commit/a0dc194e31)] - **doc**: document buffer.kStringMaxLength (Tobias Nießen) [#38688](https://github.com/nodejs/node/pull/38688)
- [[`8590c151cd`](https://github.com/nodejs/node/commit/8590c151cd)] - **doc**: update abort signal in fs promise api example (Moritz Kneilmann) [#38669](https://github.com/nodejs/node/pull/38669)
- [[`0100a3b026`](https://github.com/nodejs/node/commit/0100a3b026)] - **doc**: add documentation for fs.WriteStream.close() (Hitesh Sharma) [#38610](https://github.com/nodejs/node/pull/38610)
- [[`5c38a554ec`](https://github.com/nodejs/node/commit/5c38a554ec)] - **doc**: clarify synchronous blocking of Worker stdio (James M Snell) [#38658](https://github.com/nodejs/node/pull/38658)
- [[`1765e32c45`](https://github.com/nodejs/node/commit/1765e32c45)] - **doc**: update contact info (Gabriel Schulhof) [#38689](https://github.com/nodejs/node/pull/38689)
- [[`c4b161cb89`](https://github.com/nodejs/node/commit/c4b161cb89)] - **doc**: change color of doctag on night mode (Qingyu Deng) [#38652](https://github.com/nodejs/node/pull/38652)
- [[`6620a3182e`](https://github.com/nodejs/node/commit/6620a3182e)] - **doc**: add ESM code examples in url.md (Antoine du Hamel) [#38651](https://github.com/nodejs/node/pull/38651)
- [[`d3de0ef5d4`](https://github.com/nodejs/node/commit/d3de0ef5d4)] - **doc**: fix fs.openSync() signature (Luigi Pinca) [#38591](https://github.com/nodejs/node/pull/38591)
- [[`56bf6c1bcd`](https://github.com/nodejs/node/commit/56bf6c1bcd)] - **doc**: typo stats() should be stat(); clarity (Bryan Field) [#38541](https://github.com/nodejs/node/pull/38541)
- [[`1d9fd49f41`](https://github.com/nodejs/node/commit/1d9fd49f41)] - **doc**: fix code example in ecdh.setPublicKey() (Jordan Baczuk) [#38542](https://github.com/nodejs/node/pull/38542)
- [[`4c70e42928`](https://github.com/nodejs/node/commit/4c70e42928)] - **doc**: use `HEAD` instead of `master` for links (Antoine du Hamel) [#38518](https://github.com/nodejs/node/pull/38518)
- [[`ae9128ec61`](https://github.com/nodejs/node/commit/ae9128ec61)] - **doc**: clarify DiffieHellmanGroup class docs (Nitzan Uziely) [#38363](https://github.com/nodejs/node/pull/38363)
- [[`e59131d97f`](https://github.com/nodejs/node/commit/e59131d97f)] - **doc**: fix broken AHAFS link in fs doc (Rich Trott) [#38534](https://github.com/nodejs/node/pull/38534)
- [[`e9d4c8587a`](https://github.com/nodejs/node/commit/e9d4c8587a)] - **doc**: use AIX instead of Aix in fs.md (Rich Trott) [#38535](https://github.com/nodejs/node/pull/38535)
- [[`e0118f347a`](https://github.com/nodejs/node/commit/e0118f347a)] - **doc**: remove extraneous dash from flag prefix (Rodolfo Carvalho) [#38532](https://github.com/nodejs/node/pull/38532)
- [[`9e10e1a76f`](https://github.com/nodejs/node/commit/9e10e1a76f)] - **doc**: corrected workload name as per the latest VS Installer (MrJithil) [#38500](https://github.com/nodejs/node/pull/38500)
- [[`38644d6f96`](https://github.com/nodejs/node/commit/38644d6f96)] - **doc**: use sentence case in headers in src/crypto/README.md (Rich Trott) [#38524](https://github.com/nodejs/node/pull/38524)
- [[`347b9f2304`](https://github.com/nodejs/node/commit/347b9f2304)] - **errors**: remove input from ERR_INVALID_URL message (moander) [#38614](https://github.com/nodejs/node/pull/38614)
- [[`5b40e2f596`](https://github.com/nodejs/node/commit/5b40e2f596)] - **events**: use nullish coalencing operator (Voltrex) [#38328](https://github.com/nodejs/node/pull/38328)
- [[`3a5856cbc3`](https://github.com/nodejs/node/commit/3a5856cbc3)] - **fs**: fix async iterator partial writes (Nitzan Uziely) [#38615](https://github.com/nodejs/node/pull/38615)
- [[`e8761186a5`](https://github.com/nodejs/node/commit/e8761186a5)] - **fs**: fix error when writing buffers \> INT32_MAX (Zach Bjornson) [#38546](https://github.com/nodejs/node/pull/38546)
- [[`47080bcfc8`](https://github.com/nodejs/node/commit/47080bcfc8)] - **fs**: use `assert` in `fsCall` argument checking (Rongjian Zhang) [#38519](https://github.com/nodejs/node/pull/38519)
- [[`3d8b8e133f`](https://github.com/nodejs/node/commit/3d8b8e133f)] - **http**: refactor to remove redundant argument of \_deferToConnect (Rongjian Zhang) [#38598](https://github.com/nodejs/node/pull/38598)
- [[`c0deeeacb2`](https://github.com/nodejs/node/commit/c0deeeacb2)] - **lib**: support setting process.env.TZ on windows (James M Snell) [#38642](https://github.com/nodejs/node/pull/38642)
- [[`cf4dc80d5f`](https://github.com/nodejs/node/commit/cf4dc80d5f)] - **lib**: make `IterableWeakMap` safe to iterate (Antoine du Hamel) [#38523](https://github.com/nodejs/node/pull/38523)
- [[`90b640efb1`](https://github.com/nodejs/node/commit/90b640efb1)] - **meta**: add v8 team (Jiawen Geng) [#38566](https://github.com/nodejs/node/pull/38566)
- [[`4c4902748c`](https://github.com/nodejs/node/commit/4c4902748c)] - **(SEMVER-MINOR)** **module**: add support for `URL` to `import.meta.resolve` (Antoine du Hamel) [#38587](https://github.com/nodejs/node/pull/38587)
- [[`14a2a00cda`](https://github.com/nodejs/node/commit/14a2a00cda)] - **node-api**: faster threadsafe_function (Fedor Indutny) [#38506](https://github.com/nodejs/node/pull/38506)
- [[`be4b3a4164`](https://github.com/nodejs/node/commit/be4b3a4164)] - **path**: inline conditions (Voltrex) [#38613](https://github.com/nodejs/node/pull/38613)
- [[`c182198c44`](https://github.com/nodejs/node/commit/c182198c44)] - **(SEMVER-MINOR)** **process**: add `'worker'` event (James M Snell) [#38659](https://github.com/nodejs/node/pull/38659)
- [[`e2b8454582`](https://github.com/nodejs/node/commit/e2b8454582)] - **repl**: fix Ctrl+C on top level await (Antoine du Hamel) [#38656](https://github.com/nodejs/node/pull/38656)
- [[`718ad105e5`](https://github.com/nodejs/node/commit/718ad105e5)] - **src**: fix fatal errors when a current isolate not exist (legendecas) [#38624](https://github.com/nodejs/node/pull/38624)
- [[`524a9d6fcd`](https://github.com/nodejs/node/commit/524a9d6fcd)] - **src**: update cares_wrap OpenBSD defines (Anna Henningsen) [#38670](https://github.com/nodejs/node/pull/38670)
- [[`6b409cf664`](https://github.com/nodejs/node/commit/6b409cf664)] - **src**: remove extra semi after member fn (Shelley Vohr) [#38686](https://github.com/nodejs/node/pull/38686)
- [[`bfec80fd66`](https://github.com/nodejs/node/commit/bfec80fd66)] - **src**: make workers messaging more resilient (Juan José Arboleda) [#38510](https://github.com/nodejs/node/pull/38510)
- [[`ff1b4322f5`](https://github.com/nodejs/node/commit/ff1b4322f5)] - **test**: refactor `test-readline-interface` to be shorter (Juan José Arboleda) [#38691](https://github.com/nodejs/node/pull/38691)
- [[`8eea317227`](https://github.com/nodejs/node/commit/8eea317227)] - **test**: stream.finished detects a destroyed IncomingMessage (Nitzan Uziely) [#38661](https://github.com/nodejs/node/pull/38661)
- [[`5b25fbe266`](https://github.com/nodejs/node/commit/5b25fbe266)] - **test**: set common.bits to 64 for riscv64 (Andreas Schwab) [#38626](https://github.com/nodejs/node/pull/38626)
- [[`5a0b52120a`](https://github.com/nodejs/node/commit/5a0b52120a)] - **test**: improve coverage of lib/\_http_client.js (Rongjian Zhang) [#38599](https://github.com/nodejs/node/pull/38599)
- [[`3d0fad3840`](https://github.com/nodejs/node/commit/3d0fad3840)] - **test**: improve coverage of lib/os.js (Rongjian Zhang) [#38653](https://github.com/nodejs/node/pull/38653)
- [[`16b2fb4e0c`](https://github.com/nodejs/node/commit/16b2fb4e0c)] - **test**: increase coverage for repl (ZiJian Liu) [#38559](https://github.com/nodejs/node/pull/38559)
- [[`8f78c6646e`](https://github.com/nodejs/node/commit/8f78c6646e)] - **test**: call functions internally (Voltrex) [#38560](https://github.com/nodejs/node/pull/38560)
- [[`178fe215a4`](https://github.com/nodejs/node/commit/178fe215a4)] - **test**: increase coverage for Histogram (ZiJian Liu) [#38555](https://github.com/nodejs/node/pull/38555)
- [[`95db7d5afc`](https://github.com/nodejs/node/commit/95db7d5afc)] - **test**: improve fs coverage (Rongjian Zhang) [#38517](https://github.com/nodejs/node/pull/38517)
- [[`f2f768f261`](https://github.com/nodejs/node/commit/f2f768f261)] - **test**: complete coverage of querystring (Rongjian Zhang) [#38520](https://github.com/nodejs/node/pull/38520)
- [[`5b44107ae9`](https://github.com/nodejs/node/commit/5b44107ae9)] - **test**: increase coverage for AbortController (ZiJian Liu) [#38514](https://github.com/nodejs/node/pull/38514)
- [[`662265074c`](https://github.com/nodejs/node/commit/662265074c)] - **test**: increase coverage for Blob (ZiJian Liu) [#38515](https://github.com/nodejs/node/pull/38515)
- [[`89e1daccf3`](https://github.com/nodejs/node/commit/89e1daccf3)] - **test**: run message and pseudo-tty tests in parallel (Richard Lau) [#38502](https://github.com/nodejs/node/pull/38502)
- [[`727c2bcc24`](https://github.com/nodejs/node/commit/727c2bcc24)] - **test**: move test-net-connect-econnrefused from pummel to sequential (Rich Trott) [#38462](https://github.com/nodejs/node/pull/38462)
- [[`e64ebac2da`](https://github.com/nodejs/node/commit/e64ebac2da)] - **test**: fix flaky inspector-cli tests when breakpionts are restored (Rich Trott) [#38431](https://github.com/nodejs/node/pull/38431)
- [[`b51b4feece`](https://github.com/nodejs/node/commit/b51b4feece)] - **test**: skip tests for openssl-3.0.0-alpha15 (Daniel Bevenius) [#38451](https://github.com/nodejs/node/pull/38451)
- [[`db5ee23edf`](https://github.com/nodejs/node/commit/db5ee23edf)] - **test**: update OpenSSL 3.0.0-alpha15 error messages (Daniel Bevenius) [#38451](https://github.com/nodejs/node/pull/38451)
- [[`24472d9e0c`](https://github.com/nodejs/node/commit/24472d9e0c)] - **test,repl**: fix tests when inspector is disabled (Antoine du Hamel) [#38564](https://github.com/nodejs/node/pull/38564)
- [[`267a84f5e1`](https://github.com/nodejs/node/commit/267a84f5e1)] - **tools**: remove redundant v8 config (Jiawen Geng) [#38565](https://github.com/nodejs/node/pull/38565)
- [[`a028805f1b`](https://github.com/nodejs/node/commit/a028805f1b)] - **tools**: update ESLint to 7.26.0 (Colin Ihrig) [#38605](https://github.com/nodejs/node/pull/38605)
- [[`ec8ab22ce6`](https://github.com/nodejs/node/commit/ec8ab22ce6)] - **(SEMVER-MINOR)** **tools**: add `Worker` to type-parser (James M Snell) [#38659](https://github.com/nodejs/node/pull/38659)
- [[`151488539b`](https://github.com/nodejs/node/commit/151488539b)] - **tools**: make GH Actions workflows work if default branch is not master (Antoine du Hamel) [#38516](https://github.com/nodejs/node/pull/38516)
- [[`c0f0c9a92d`](https://github.com/nodejs/node/commit/c0f0c9a92d)] - **typings**: add JSDoc typings for readline (Voltrex) [#38253](https://github.com/nodejs/node/pull/38253)
- [[`fbf02e3198`](https://github.com/nodejs/node/commit/fbf02e3198)] - **(SEMVER-MINOR)** **util**: add util.types.isKeyObject and util.types.isCryptoKey (Filip Skokan) [#38619](https://github.com/nodejs/node/pull/38619)
- [[`070ee4bb94`](https://github.com/nodejs/node/commit/070ee4bb94)] - **_Revert_** "**worker**: remove `ERR_CLOSED_MESSAGE_PORT`" (Juan José Arboleda) [#38510](https://github.com/nodejs/node/pull/38510)

Windows 32-bit Installer: https://nodejs.org/dist/v16.2.0/node-v16.2.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v16.2.0/node-v16.2.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v16.2.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v16.2.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v16.2.0/node-v16.2.0.pkg \
macOS Apple Silicon 64-bit Binary: https://nodejs.org/dist/v16.2.0/node-v16.2.0-darwin-arm64.tar.gz \
macOS Intel 64-bit Binary: https://nodejs.org/dist/v16.2.0/node-v16.2.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v16.2.0/node-v16.2.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v16.2.0/node-v16.2.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v16.2.0/node-v16.2.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v16.2.0/node-v16.2.0-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v16.2.0/node-v16.2.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v16.2.0/node-v16.2.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v16.2.0/node-v16.2.0.tar.gz \
Other release files: https://nodejs.org/dist/v16.2.0/ \
Documentation: https://nodejs.org/docs/v16.2.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

149f3f7be5996d6f26a86f97daeaa24bf585e9517ea283cd60676a0c81943a95  node-v16.2.0-aix-ppc64.tar.gz
451d87c07c522e24152a584b2d5461d4e3a7c690bd8882bef9ae8bf6b19d1dfd  node-v16.2.0-darwin-arm64.tar.gz
ee6eed2877b8fe18cd169e00b87459c60d98e4f195ae00f3462f024de1b48554  node-v16.2.0-darwin-arm64.tar.xz
3fc49b69de9491b45491f880217f8220d489b28ba3c1fff53e849dcf3ad77343  node-v16.2.0-darwin-x64.tar.gz
213f24511fd3aa55ff6f315b23c8fa9fdc7fe765b30a446f24ac77cf009a0c7f  node-v16.2.0-darwin-x64.tar.xz
4f6a1e877c65ce31217b1c67f4fd90b9f9f4d3051e5ffabf151f43dd6edf7a34  node-v16.2.0-headers.tar.gz
683fc213a12e4612e6f30eff0b5bdc3ec383b7d80c2b1f04c8700fda858c7408  node-v16.2.0-headers.tar.xz
2880b393d5950a1fb934a7e36fcb3434d788ded6669c586c2346708a7f72368b  node-v16.2.0-linux-arm64.tar.gz
364ceac78235a052f17f3c0a8e11819b8a04ec5ec6b2f09e21647a7b33cbf97b  node-v16.2.0-linux-arm64.tar.xz
947f91232031e28e41f71609bd88153b9c527e5482a3d948fa7a197bd7270fc2  node-v16.2.0-linux-armv7l.tar.gz
88082503133421be92520d5538e628d7b40307903de90035553a5ce28af1e22b  node-v16.2.0-linux-armv7l.tar.xz
e29a247becf4f496070a33752c1a2ebe45185bbb844e2b331067e1bb4c965b4f  node-v16.2.0-linux-ppc64le.tar.gz
0ea72611d7a11c88bdad34d5ae92b928e36e94d43177648e8803ad3badaa73a3  node-v16.2.0-linux-ppc64le.tar.xz
ac7cc3c48abe862d94411a8477c01acecb9f7767882e8d5870498804c03508b2  node-v16.2.0-linux-s390x.tar.gz
4727a501fc17b3c4402120cd64bd3e46d9f0b24bf9eefd6c12724c06712ebfe7  node-v16.2.0-linux-s390x.tar.xz
c3fd89a768e40a2fd8008919100bd283e6e9aec742eddeb1d494eb2a626466dc  node-v16.2.0-linux-x64.tar.gz
e134c8738761ab908aac3b62efa4865d8a7c73e40cc7758c6e99244921696216  node-v16.2.0-linux-x64.tar.xz
ad76bd2e7d15b1ad5528683f9c937df7979adf78b2c20d0f00e687fa202a76b8  node-v16.2.0.pkg
5d093d6b203159aa3288a1d51495d2faf11550f0755d04015161a529a065b12a  node-v16.2.0.tar.gz
d0f93b9842afb8f23c07862e9cd48226e7104547f7b2415d250fdb752d1b35cf  node-v16.2.0.tar.xz
89a0f59d6c0b9be9498b62c40b4f5b5cd25325ee27dae91d4daf45b9ae019806  node-v16.2.0-win-x64.7z
8cf1fb277154b82d2774143be2575e1690af1b5c05e2a1b9a54b50b4efc73e8e  node-v16.2.0-win-x64.zip
5a847e85778f5ff7b928dd5ecc5d3f44f2ba82db52fc456709a4d93c5cd0d5e0  node-v16.2.0-win-x86.7z
bbcaeb6f3043d385ae085f8a11c92467a3c33c7023070f42ad08b7ba2d2b978d  node-v16.2.0-win-x86.zip
ec7403bba0b6d55dd9bc963043973d269f93c34d00d169fae4c0986cd408e93c  node-v16.2.0-x64.msi
0da39e156480385920e256a51fc94a77211d07a37b216080651a5835c3994b8a  node-v16.2.0-x86.msi
c35384c71bf5399cef92f8d1baa5a594160f8a94962757844b1b636c751073ee  win-x64/node.exe
c1f38a0c578c1e7b062b10cd6c122795dd5e8d5d7252d22a05ecc01d0a581fd9  win-x64/node.lib
3c77a03e5119ab26add58d84af3f87a884602346a77b3b040fcee0160ea96f6d  win-x64/node_pdb.7z
1a83b7e9fc5bbbff8480ce8b0e5b63bf6d536c92df061f68634945a4f0dd496e  win-x64/node_pdb.zip
baf09fafb14cbbbe115e721eeb9ede72fb799e711e6534194cfa13a3e83584eb  win-x86/node.exe
a50f3c57671e7a07e7541f493e98cbbf006535598c93465ed14bb6f9cf253317  win-x86/node.lib
f0446b9911804e3075db56c0655012c1ae94adb8fa6df100e23359eb5f510c66  win-x86/node_pdb.7z
aae4d7c5bd8d3337a11398a60132d1a43c1b947b6a21f59a76a8e1c2e1e527b1  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEj8yhP+8dDC6RAI4Jdw96mlrhVgAFAmCk74MACgkQdw96mlrh
VgDGYQ/9G1lFH5R3AE1FCVrkspMzl3yTEXH7yJ6UDhlQxjJ+yii949wG/niGlLja
PpMxjHTMGAMaLgJ/sLP2ANvegKFJMjMhwHjHXa/+h2rGM0iYs6AXQ2momsi7JEtD
Z97hlgdPX7jrLKIJgWa4Qzb7FkOmycsYqWRDcLH2zjtLWSyH3D5h0irgc3jfpQ69
A+uWKMF0XYnrlQAc6sK/w/Es/s1Csqye2zJrOjjGsTx6lqE9QH7qayxCc/SNaLF8
pMoYTH951lqHkpJyr+H2BjC4YokNUq0gJrwuqzX2yGXT1j+95Zw9XadywQmEG61R
wMir6Ngu9ilav5Or1kh6lL3ZGA9QVYgBfIVSLmiiLHcgFpqCZ6rZ4zag85NGx5TH
1TC35LYfVw0VYdD+TB0SuAfsswzCvsGoBTI7lTIH//qVlaPrXMlNQKzUWRViMwM4
nU9SCrZh8d9bIDQws6nZC2LIuLMYCbOa/nc84NKsjvDq+t/DAId5h2UoGkZxUcEP
CRLShen0/9WowD85JOvehcmJhFhcYkFal9UyFUchFBL/9JsdZJimB3Zs0hbMy6R4
y5DrzBtxTVT5Ckojpk3Hs3otFocefYwXv8f5aLlY1y27b//QMCS6/MIacRJlJlEW
1xWW+i/Sb1lJPfAJ40Z+gOFTGgedVs0kQWW1amUwT24YB/Jy/xY=
=8amJ
-----END PGP SIGNATURE-----

```
