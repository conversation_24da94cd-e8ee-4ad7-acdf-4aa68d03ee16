---
date: '2021-11-09T19:09:12.694Z'
category: release
title: Node v17.1.0 (Current)
layout: blog-post
author: <PERSON><PERSON><PERSON>
---

### Notable Changes

- \[[`89b34ecffb`](https://github.com/nodejs/node/commit/89b34ecffb)] - **doc**: add VoltrexMaster to collaborators (voltrexmaster) [#40566](https://github.com/nodejs/node/pull/40566)
- \[[`95e4d29eb4`](https://github.com/nodejs/node/commit/95e4d29eb4)] - **(SEMVER-MINOR)** **esm**: add support for JSON import assertion (<PERSON>) [#40250](https://github.com/nodejs/node/pull/40250)
- \[[`1ddbae2d76`](https://github.com/nodejs/node/commit/1ddbae2d76)] - **(SEMVER-MINOR)** **lib**: add unsubscribe method to non-active DC channels (simon-id) [#40433](https://github.com/nodejs/node/pull/40433)
- \[[`aa61551b49`](https://github.com/nodejs/node/commit/aa61551b49)] - **(SEMVER-MINOR)** **lib**: add return value for DC channel.unsubscribe (simon-id) [#40433](https://github.com/nodejs/node/pull/40433)
- \[[`fbeb895ca6`](https://github.com/nodejs/node/commit/fbeb895ca6)] - **(SEMVER-MINOR)** **v8**: multi-tenant promise hook api (Stephen Belanger) [#39283](https://github.com/nodejs/node/pull/39283)

### Commits

- \[[`8a00dc5add`](https://github.com/nodejs/node/commit/8a00dc5add)] - **build**: skip long-running Actions for README-only modifications (Rich Trott) [#40571](https://github.com/nodejs/node/pull/40571)
- \[[`9f46fca124`](https://github.com/nodejs/node/commit/9f46fca124)] - **build**: disable v8 pointer compression on 32bit archs (Cheng Zhao) [#40418](https://github.com/nodejs/node/pull/40418)
- \[[`5bef74395d`](https://github.com/nodejs/node/commit/5bef74395d)] - **deps**: patch V8 to ********** (Michaël Zasso) [#40604](https://github.com/nodejs/node/pull/40604)
- \[[`3805b806ee`](https://github.com/nodejs/node/commit/3805b806ee)] - **deps**: upgrade npm to 8.1.2 (npm team) [#40643](https://github.com/nodejs/node/pull/40643)
- \[[`c003ba131b`](https://github.com/nodejs/node/commit/c003ba131b)] - **deps**: update c-ares to 1.18.1 (Richard Lau) [#40660](https://github.com/nodejs/node/pull/40660)
- \[[`841f35cc52`](https://github.com/nodejs/node/commit/841f35cc52)] - **deps**: upgrade npm to 8.1.1 (npm team) [#40554](https://github.com/nodejs/node/pull/40554)
- \[[`8d16f0d2d3`](https://github.com/nodejs/node/commit/8d16f0d2d3)] - **deps**: V8: cherry-pick 422dc378a1da (Ray Wang) [#40450](https://github.com/nodejs/node/pull/40450)
- \[[`cdf5c44d62`](https://github.com/nodejs/node/commit/cdf5c44d62)] - **deps**: add riscv64 config into openssl gypi (Lu Yahan) [#40473](https://github.com/nodejs/node/pull/40473)
- \[[`2b9fcdfe26`](https://github.com/nodejs/node/commit/2b9fcdfe26)] - **deps**: attempt to suppress macro-redefined warning (Daniel Bevenius) [#40518](https://github.com/nodejs/node/pull/40518)
- \[[`d2839bfaa9`](https://github.com/nodejs/node/commit/d2839bfaa9)] - **deps**: regenerate OpenSSL arch files (Daniel Bevenius) [#40518](https://github.com/nodejs/node/pull/40518)
- \[[`5df8ce5cbe`](https://github.com/nodejs/node/commit/5df8ce5cbe)] - **deps,build,tools**: fix openssl-is-fips for ninja builds (Daniel Bevenius) [#40518](https://github.com/nodejs/node/pull/40518)
- \[[`79bf429405`](https://github.com/nodejs/node/commit/79bf429405)] - **dgram**: fix send with out of bounds offset + length (Nitzan Uziely) [#40568](https://github.com/nodejs/node/pull/40568)
- \[[`c29658fda7`](https://github.com/nodejs/node/commit/c29658fda7)] - **doc**: update cjs-module-lexer repo link (Guy Bedford) [#40707](https://github.com/nodejs/node/pull/40707)
- \[[`e374f3ddd9`](https://github.com/nodejs/node/commit/e374f3ddd9)] - **doc**: fix lint re-enabling comment in README.md (Rich Trott) [#40647](https://github.com/nodejs/node/pull/40647)
- \[[`ecccf48106`](https://github.com/nodejs/node/commit/ecccf48106)] - **doc**: format v8.md in preparation for stricter linting (Rich Trott) [#40647](https://github.com/nodejs/node/pull/40647)
- \[[`95a7117037`](https://github.com/nodejs/node/commit/95a7117037)] - **doc**: final round of markdown format changes (Rich Trott) [#40645](https://github.com/nodejs/node/pull/40645)
- \[[`c104f5a9ab`](https://github.com/nodejs/node/commit/c104f5a9ab)] - **doc**: remove `--experimental-modules` documentation (FrankQiu) [#38974](https://github.com/nodejs/node/pull/38974)
- \[[`ac81f89bbf`](https://github.com/nodejs/node/commit/ac81f89bbf)] - **doc**: update tracking issues of startup performance (Joyee Cheung) [#40629](https://github.com/nodejs/node/pull/40629)
- \[[`65effa11fc`](https://github.com/nodejs/node/commit/65effa11fc)] - **doc**: fix markdown syntax and HTML tag misses (ryan) [#40608](https://github.com/nodejs/node/pull/40608)
- \[[`c78d708a16`](https://github.com/nodejs/node/commit/c78d708a16)] - **doc**: use 'GitHub Actions workflow' instead (Mestery) [#40586](https://github.com/nodejs/node/pull/40586)
- \[[`71bac70bf2`](https://github.com/nodejs/node/commit/71bac70bf2)] - **doc**: ref OpenSSL legacy provider from crypto docs (Tobias Nießen) [#40593](https://github.com/nodejs/node/pull/40593)
- \[[`8f410229ac`](https://github.com/nodejs/node/commit/8f410229ac)] - **doc**: add node: url scheme (Daniel Nalborczyk) [#40573](https://github.com/nodejs/node/pull/40573)
- \[[`35dbed21f2`](https://github.com/nodejs/node/commit/35dbed21f2)] - **doc**: call cwd function (Daniel Nalborczyk) [#40573](https://github.com/nodejs/node/pull/40573)
- \[[`4870a23ccc`](https://github.com/nodejs/node/commit/4870a23ccc)] - **doc**: remove unused imports (Daniel Nalborczyk) [#40573](https://github.com/nodejs/node/pull/40573)
- \[[`5951ccc12e`](https://github.com/nodejs/node/commit/5951ccc12e)] - **doc**: simplify CHANGELOG.md (Rich Trott) [#40475](https://github.com/nodejs/node/pull/40475)
- \[[`6ae134ecb7`](https://github.com/nodejs/node/commit/6ae134ecb7)] - **doc**: correct esm spec scope lookup definition (Guy Bedford) [#40592](https://github.com/nodejs/node/pull/40592)
- \[[`09239216f6`](https://github.com/nodejs/node/commit/09239216f6)] - **doc**: update CHANGELOG.md for Node.js 16.13.0 (Richard Lau) [#40617](https://github.com/nodejs/node/pull/40617)
- \[[`46ec5ac4df`](https://github.com/nodejs/node/commit/46ec5ac4df)] - **doc**: add info on project's usage of coverity (Michael Dawson) [#40506](https://github.com/nodejs/node/pull/40506)
- \[[`7eb1a44410`](https://github.com/nodejs/node/commit/7eb1a44410)] - **doc**: fix typo in changelogs (Luigi Pinca) [#40585](https://github.com/nodejs/node/pull/40585)
- \[[`132f6cba05`](https://github.com/nodejs/node/commit/132f6cba05)] - **doc**: update onboarding task (Rich Trott) [#40570](https://github.com/nodejs/node/pull/40570)
- \[[`5e2d0ed61e`](https://github.com/nodejs/node/commit/5e2d0ed61e)] - **doc**: simplify ccache instructions (Rich Trott) [#40550](https://github.com/nodejs/node/pull/40550)
- \[[`c1c1738bfc`](https://github.com/nodejs/node/commit/c1c1738bfc)] - **doc**: fix macOS environment variables for ccache (Rich Trott) [#40550](https://github.com/nodejs/node/pull/40550)
- \[[`6e3e50f2ab`](https://github.com/nodejs/node/commit/6e3e50f2ab)] - **doc**: improve async_context introduction (Michaël Zasso) [#40560](https://github.com/nodejs/node/pull/40560)
- \[[`1587fe62d4`](https://github.com/nodejs/node/commit/1587fe62d4)] - **doc**: use GFM footnotes in webcrypto.md (Rich Trott) [#40477](https://github.com/nodejs/node/pull/40477)
- \[[`305c022db4`](https://github.com/nodejs/node/commit/305c022db4)] - **doc**: describe buffer limit of v8.serialize (Ray Wang) [#40243](https://github.com/nodejs/node/pull/40243)
- \[[`6e39e0e10a`](https://github.com/nodejs/node/commit/6e39e0e10a)] - **doc**: run license-builder (Rich Trott) [#40540](https://github.com/nodejs/node/pull/40540)
- \[[`556e49ccb5`](https://github.com/nodejs/node/commit/556e49ccb5)] - **doc**: use GFM footnotes in maintaining-V8.md (#40476) (Rich Trott) [#40476](https://github.com/nodejs/node/pull/40476)
- \[[`9c6a9fd5b1`](https://github.com/nodejs/node/commit/9c6a9fd5b1)] - **doc**: use GFM footnotes in BUILDING.md (Rich Trott) [#40474](https://github.com/nodejs/node/pull/40474)
- \[[`fd946215cc`](https://github.com/nodejs/node/commit/fd946215cc)] - **doc**: fix `fs.symlink` code example (Juan José Arboleda) [#40414](https://github.com/nodejs/node/pull/40414)
- \[[`404730ac1b`](https://github.com/nodejs/node/commit/404730ac1b)] - **doc**: update for changed `--dns-result-order` default (Richard Lau) [#40538](https://github.com/nodejs/node/pull/40538)
- \[[`acc22c7c4a`](https://github.com/nodejs/node/commit/acc22c7c4a)] - **doc**: add missing entry in `globals.md` (Antoine du Hamel) [#40531](https://github.com/nodejs/node/pull/40531)
- \[[`0375d958ef`](https://github.com/nodejs/node/commit/0375d958ef)] - **doc**: explain backport labels (Stephen Belanger) [#40520](https://github.com/nodejs/node/pull/40520)
- \[[`4993d87c49`](https://github.com/nodejs/node/commit/4993d87c49)] - **doc**: fix entry for Slack channel in onboarding.md (Rich Trott) [#40563](https://github.com/nodejs/node/pull/40563)
- \[[`89b34ecffb`](https://github.com/nodejs/node/commit/89b34ecffb)] - **doc**: add VoltrexMaster to collaborators (voltrexmaster) [#40566](https://github.com/nodejs/node/pull/40566)
- \[[`6357ef15d0`](https://github.com/nodejs/node/commit/6357ef15d0)] - **doc**: document considerations for inclusion in core (Rich Trott) [#40338](https://github.com/nodejs/node/pull/40338)
- \[[`ed04827373`](https://github.com/nodejs/node/commit/ed04827373)] - **doc**: update link in onboarding doc (Rich Trott) [#40539](https://github.com/nodejs/node/pull/40539)
- \[[`34e244b8e9`](https://github.com/nodejs/node/commit/34e244b8e9)] - **doc**: clarify behavior of napi_extended_error_info (Michael Dawson) [#40458](https://github.com/nodejs/node/pull/40458)
- \[[`5a588ff047`](https://github.com/nodejs/node/commit/5a588ff047)] - **doc**: add updating expected assets to release guide (Richard Lau) [#40470](https://github.com/nodejs/node/pull/40470)
- \[[`95e4d29eb4`](https://github.com/nodejs/node/commit/95e4d29eb4)] - **(SEMVER-MINOR)** **esm**: add support for JSON import assertion (Antoine du Hamel) [#40250](https://github.com/nodejs/node/pull/40250)
- \[[`825a683423`](https://github.com/nodejs/node/commit/825a683423)] - **http**: response should always emit 'close' (Robert Nagy) [#40543](https://github.com/nodejs/node/pull/40543)
- \[[`81cd7f3751`](https://github.com/nodejs/node/commit/81cd7f3751)] - **lib**: fix regular expression to detect \`/\` and \`\\\` (Francesco Trotta) [#40325](https://github.com/nodejs/node/pull/40325)
- \[[`1ddbae2d76`](https://github.com/nodejs/node/commit/1ddbae2d76)] - **(SEMVER-MINOR)** **lib**: add unsubscribe method to non-active DC channels (simon-id) [#40433](https://github.com/nodejs/node/pull/40433)
- \[[`aa61551b49`](https://github.com/nodejs/node/commit/aa61551b49)] - **(SEMVER-MINOR)** **lib**: add return value for DC channel.unsubscribe (simon-id) [#40433](https://github.com/nodejs/node/pull/40433)
- \[[`d97872dd98`](https://github.com/nodejs/node/commit/d97872dd98)] - **meta**: use form schema for flaky test template (Michaël Zasso) [#40737](https://github.com/nodejs/node/pull/40737)
- \[[`c2fabdbce8`](https://github.com/nodejs/node/commit/c2fabdbce8)] - **meta**: update AUTHORS (Node.js GitHub Bot) [#40668](https://github.com/nodejs/node/pull/40668)
- \[[`aa98c6bdce`](https://github.com/nodejs/node/commit/aa98c6bdce)] - **meta**: consolidate AUTHORS entries for brettkiefer (Rich Trott) [#40599](https://github.com/nodejs/node/pull/40599)
- \[[`18296c3d8e`](https://github.com/nodejs/node/commit/18296c3d8e)] - **meta**: consolidate AUTHORS entries for alexzherdev (Rich Trott) [#40620](https://github.com/nodejs/node/pull/40620)
- \[[`88d812793d`](https://github.com/nodejs/node/commit/88d812793d)] - **meta**: consolidate AUTHORS entries for Azard (Rich Trott) [#40619](https://github.com/nodejs/node/pull/40619)
- \[[`d81b65ca0e`](https://github.com/nodejs/node/commit/d81b65ca0e)] - **meta**: move Fishrock123 to emeritus (Jeremiah Senkpiel) [#40596](https://github.com/nodejs/node/pull/40596)
- \[[`ec02e7b68a`](https://github.com/nodejs/node/commit/ec02e7b68a)] - **meta**: consolidate AUTHORS entries for clakech (Rich Trott) [#40589](https://github.com/nodejs/node/pull/40589)
- \[[`08e7a2ff24`](https://github.com/nodejs/node/commit/08e7a2ff24)] - **meta**: consolidate AUTHORS entries for darai0512 (Rich Trott) [#40569](https://github.com/nodejs/node/pull/40569)
- \[[`488ee51f90`](https://github.com/nodejs/node/commit/488ee51f90)] - **meta**: update AUTHORS (Node.js GitHub Bot) [#40580](https://github.com/nodejs/node/pull/40580)
- \[[`ba9a6c5d06`](https://github.com/nodejs/node/commit/ba9a6c5d06)] - **meta**: consolidate AUTHORS entries for dfabulich (Rich Trott) [#40527](https://github.com/nodejs/node/pull/40527)
- \[[`bd06e9945e`](https://github.com/nodejs/node/commit/bd06e9945e)] - **meta**: move one or more collaborators to emeritus (Node.js GitHub Bot) [#40464](https://github.com/nodejs/node/pull/40464)
- \[[`7cee125dcf`](https://github.com/nodejs/node/commit/7cee125dcf)] - **meta**: add Richard Lau to TSC list in README.md (Rich Trott) [#40523](https://github.com/nodejs/node/pull/40523)
- \[[`6a771f8bcb`](https://github.com/nodejs/node/commit/6a771f8bcb)] - **meta**: consolidate AUTHORS entries for dguo (Rich Trott) [#40517](https://github.com/nodejs/node/pull/40517)
- \[[`e4c740646d`](https://github.com/nodejs/node/commit/e4c740646d)] - **meta**: consolidate AUTHORS entries for cxreg (Rich Trott) [#40490](https://github.com/nodejs/node/pull/40490)
- \[[`075cfbf941`](https://github.com/nodejs/node/commit/075cfbf941)] - **module**: resolver & spec hardening /w refactoring (Guy Bedford) [#40510](https://github.com/nodejs/node/pull/40510)
- \[[`b320387c83`](https://github.com/nodejs/node/commit/b320387c83)] - **policy**: fix message for invalid manifest specifier (Rich Trott) [#40574](https://github.com/nodejs/node/pull/40574)
- \[[`ea968d54c5`](https://github.com/nodejs/node/commit/ea968d54c5)] - **process**: refactor execution (Voltrex) [#40664](https://github.com/nodejs/node/pull/40664)
- \[[`fb7c437b0b`](https://github.com/nodejs/node/commit/fb7c437b0b)] - **src**: make LoadEnvironment with string work with builtin modules path (Michaël Zasso) [#40607](https://github.com/nodejs/node/pull/40607)
- \[[`e9388c87bf`](https://github.com/nodejs/node/commit/e9388c87bf)] - **src**: remove usage of `AllocatedBuffer` from `node_http2` (Darshan Sen) [#40584](https://github.com/nodejs/node/pull/40584)
- \[[`7a22f913b0`](https://github.com/nodejs/node/commit/7a22f913b0)] - **src**: fix #endif description in crypto_keygen.h (Tobias Nießen) [#40639](https://github.com/nodejs/node/pull/40639)
- \[[`396342e26d`](https://github.com/nodejs/node/commit/396342e26d)] - **src**: throw error instead of assertion (Ray Wang) [#40243](https://github.com/nodejs/node/pull/40243)
- \[[`accab383a1`](https://github.com/nodejs/node/commit/accab383a1)] - **src**: register external references in os bindings (Joyee Cheung) [#40239](https://github.com/nodejs/node/pull/40239)
- \[[`a11f9ea4f0`](https://github.com/nodejs/node/commit/a11f9ea4f0)] - **src**: register external references in crypto bindings (Joyee Cheung) [#40239](https://github.com/nodejs/node/pull/40239)
- \[[`ef1ace7e88`](https://github.com/nodejs/node/commit/ef1ace7e88)] - **src,crypto**: use `std::variant` in DH params (Darshan Sen) [#40457](https://github.com/nodejs/node/pull/40457)
- \[[`4433852f62`](https://github.com/nodejs/node/commit/4433852f62)] - **src,crypto**: remove `AllocatedBuffer` from `crypto_cipher.cc` (Darshan Sen) [#40400](https://github.com/nodejs/node/pull/40400)
- \[[`814126c3ed`](https://github.com/nodejs/node/commit/814126c3ed)] - **src,fs**: remove `ToLocalChecked()` call from `fs::AfterMkdirp()` (Darshan Sen) [#40386](https://github.com/nodejs/node/pull/40386)
- \[[`d4b45cc249`](https://github.com/nodejs/node/commit/d4b45cc249)] - **src,stream**: remove `*Check*()` calls from non-`Initialize()` functions (Darshan Sen) [#40425](https://github.com/nodejs/node/pull/40425)
- \[[`bac7fe0797`](https://github.com/nodejs/node/commit/bac7fe0797)] - **stream**: remove no longer necessary ComposeDuplex (Robert Nagy) [#40545](https://github.com/nodejs/node/pull/40545)
- \[[`e58cce49fd`](https://github.com/nodejs/node/commit/e58cce49fd)] - **test**: disable warnings to fix flaky test (Antoine du Hamel) [#40739](https://github.com/nodejs/node/pull/40739)
- \[[`8c103ab2ff`](https://github.com/nodejs/node/commit/8c103ab2ff)] - **test**: skip macos sandbox test with builtin modules path (Michaël Zasso) [#40607](https://github.com/nodejs/node/pull/40607)
- \[[`ac3bc6eed0`](https://github.com/nodejs/node/commit/ac3bc6eed0)] - **test**: add semicolon after chunk size (Luigi Pinca) [#40487](https://github.com/nodejs/node/pull/40487)
- \[[`95fe9bb922`](https://github.com/nodejs/node/commit/95fe9bb922)] - **test**: deflake http2-cancel-while-client-reading (Luigi Pinca) [#40659](https://github.com/nodejs/node/pull/40659)
- \[[`dfd0215266`](https://github.com/nodejs/node/commit/dfd0215266)] - **test**: avoid deep comparisons with literals (Tobias Nießen) [#40634](https://github.com/nodejs/node/pull/40634)
- \[[`5020f634b8`](https://github.com/nodejs/node/commit/5020f634b8)] - **test**: mark test-policy-integrity flaky on Windows (Rich Trott) [#40684](https://github.com/nodejs/node/pull/40684)
- \[[`8fa1c61e40`](https://github.com/nodejs/node/commit/8fa1c61e40)] - **test**: fix test-datetime-change-notify after daylight change (Piotr Rybak) [#40684](https://github.com/nodejs/node/pull/40684)
- \[[`179a5c5436`](https://github.com/nodejs/node/commit/179a5c5436)] - **test**: test `crypto.setEngine()` using an actual engine (Darshan Sen) [#40481](https://github.com/nodejs/node/pull/40481)
- \[[`cf6ded4db5`](https://github.com/nodejs/node/commit/cf6ded4db5)] - **test**: use conventional argument order in assertion (Tobias Nießen) [#40591](https://github.com/nodejs/node/pull/40591)
- \[[`aefb097d6a`](https://github.com/nodejs/node/commit/aefb097d6a)] - **test**: fix test description (Luigi Pinca) [#40486](https://github.com/nodejs/node/pull/40486)
- \[[`126e669b84`](https://github.com/nodejs/node/commit/126e669b84)] - **test,doc**: correct documentation for runBenchmark() (Rich Trott) [#40683](https://github.com/nodejs/node/pull/40683)
- \[[`1844463ce2`](https://github.com/nodejs/node/commit/1844463ce2)] - **test,tools**: increase pummel/benchmark test timeout from 4x to 6x (Rich Trott) [#40684](https://github.com/nodejs/node/pull/40684)
- \[[`f731f5ffb5`](https://github.com/nodejs/node/commit/f731f5ffb5)] - **test,tools**: increase timeout for benchmark tests (Rich Trott) [#40684](https://github.com/nodejs/node/pull/40684)
- \[[`bbc10f1849`](https://github.com/nodejs/node/commit/bbc10f1849)] - **tools**: simplify and fix commit queue (Michaël Zasso) [#40742](https://github.com/nodejs/node/pull/40742)
- \[[`a3df50d810`](https://github.com/nodejs/node/commit/a3df50d810)] - **tools**: ensure the PR was not pushed before merging (Antoine du Hamel) [#40747](https://github.com/nodejs/node/pull/40747)
- \[[`306d953c15`](https://github.com/nodejs/node/commit/306d953c15)] - **tools**: update ESLint to 8.2.0 (Luigi Pinca) [#40734](https://github.com/nodejs/node/pull/40734)
- \[[`b7e736843c`](https://github.com/nodejs/node/commit/b7e736843c)] - **tools**: use GitHub Squash and Merge feature when using CQ (Antoine du Hamel) [#40666](https://github.com/nodejs/node/pull/40666)
- \[[`50d102ec08`](https://github.com/nodejs/node/commit/50d102ec08)] - **tools**: fix bug in `prefer-primordials` ESLint rule (Antoine du Hamel) [#40628](https://github.com/nodejs/node/pull/40628)
- \[[`ec2cadef85`](https://github.com/nodejs/node/commit/ec2cadef85)] - **tools**: add script to update c-ares (Richard Lau) [#40660](https://github.com/nodejs/node/pull/40660)
- \[[`5daa313215`](https://github.com/nodejs/node/commit/5daa313215)] - **tools**: notify user if format-md needs to be run (Rich Trott) [#40647](https://github.com/nodejs/node/pull/40647)
- \[[`0787c781ce`](https://github.com/nodejs/node/commit/0787c781ce)] - **tools**: abort CQ session when landing several commits (Antoine du Hamel) [#40577](https://github.com/nodejs/node/pull/40577)
- \[[`ddc44ddfd9`](https://github.com/nodejs/node/commit/ddc44ddfd9)] - **tools**: fix commit-lint workflow (Antoine du Hamel) [#40673](https://github.com/nodejs/node/pull/40673)
- \[[`47eddd7076`](https://github.com/nodejs/node/commit/47eddd7076)] - **tools**: avoid unnecessary escaping in markdown formatter (Rich Trott) [#40645](https://github.com/nodejs/node/pull/40645)
- \[[`c700de3705`](https://github.com/nodejs/node/commit/c700de3705)] - **tools**: avoid fetch extra commits when validating commit messages (Antoine du Hamel) [#39128](https://github.com/nodejs/node/pull/39128)
- \[[`716963484b`](https://github.com/nodejs/node/commit/716963484b)] - **tools**: update ESLint to 8.1.0 (Luigi Pinca) [#40582](https://github.com/nodejs/node/pull/40582)
- \[[`9cb2116608`](https://github.com/nodejs/node/commit/9cb2116608)] - **tools**: fix formatting of warning message in update-authors.js (Rich Trott) [#40600](https://github.com/nodejs/node/pull/40600)
- \[[`507f1dbc8d`](https://github.com/nodejs/node/commit/507f1dbc8d)] - **tools**: udpate doc tools to accommodate GFM footnotes (Rich Trott) [#40477](https://github.com/nodejs/node/pull/40477)
- \[[`c2265a92c3`](https://github.com/nodejs/node/commit/c2265a92c3)] - **tools**: update license-builder.sh for OpenSSL (Rich Trott) [#40540](https://github.com/nodejs/node/pull/40540)
- \[[`16624b404c`](https://github.com/nodejs/node/commit/16624b404c)] - **tools,meta**: remove exclusions from AUTHORS (Rich Trott) [#40648](https://github.com/nodejs/node/pull/40648)
- \[[`a95e344fe5`](https://github.com/nodejs/node/commit/a95e344fe5)] - **tty**: support more CI services in `getColorDepth` (Richie Bendall) [#40385](https://github.com/nodejs/node/pull/40385)
- \[[`b4194ff349`](https://github.com/nodejs/node/commit/b4194ff349)] - **typings**: add more bindings typings (Mestery) [#40415](https://github.com/nodejs/node/pull/40415)
- \[[`da859b56cb`](https://github.com/nodejs/node/commit/da859b56cb)] - **typings**: add JSDoc typings for inspector (Voltrex) [#38390](https://github.com/nodejs/node/pull/38390)
- \[[`90aa96dc44`](https://github.com/nodejs/node/commit/90aa96dc44)] - **typings**: improve internal bindings typings (Mestery) [#40411](https://github.com/nodejs/node/pull/40411)
- \[[`1e9f3cc522`](https://github.com/nodejs/node/commit/1e9f3cc522)] - **typings**: separate `internalBinding` typings (Mestery) [#40409](https://github.com/nodejs/node/pull/40409)
- \[[`fbeb895ca6`](https://github.com/nodejs/node/commit/fbeb895ca6)] - **(SEMVER-MINOR)** **v8**: multi-tenant promise hook api (Stephen Belanger) [#39283](https://github.com/nodejs/node/pull/39283)

Windows 32-bit Installer: https://nodejs.org/dist/v17.1.0/node-v17.1.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v17.1.0/node-v17.1.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v17.1.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v17.1.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v17.1.0/node-v17.1.0.pkg \
macOS Apple Silicon 64-bit Binary: https://nodejs.org/dist/v17.1.0/node-v17.1.0-darwin-arm64.tar.gz \
macOS Intel 64-bit Binary: https://nodejs.org/dist/v17.1.0/node-v17.1.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v17.1.0/node-v17.1.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v17.1.0/node-v17.1.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v17.1.0/node-v17.1.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v17.1.0/node-v17.1.0-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v17.1.0/node-v17.1.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v17.1.0/node-v17.1.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v17.1.0/node-v17.1.0.tar.gz \
Other release files: https://nodejs.org/dist/v17.1.0/ \
Documentation: https://nodejs.org/docs/v17.1.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

dfeb027e2dd37b099bf326238a24959bee2818a4544a63d8a3f7ff88e026b050  node-v17.1.0-aix-ppc64.tar.gz
5e89e1cd17c58ef795d3bb420dd2a473c72a642422328c66dec59eb6d9243408  node-v17.1.0-darwin-arm64.tar.gz
489f945a806f84866210f67dc2890377a3632f449ca77bdcf4b619a4489855d3  node-v17.1.0-darwin-arm64.tar.xz
5255978096ea249a8b155f6cc7f8f81e3bc2f7e9371c9f263bdb484359d740b2  node-v17.1.0-darwin-x64.tar.gz
ae55f234128ed99efa7984520895cdc7a78cc4eb3ef6666062219a959eed30e0  node-v17.1.0-darwin-x64.tar.xz
e0fd9ee50e9bc1e5e69be6afcd1b2c51787ed1dffd0d11c72ff2c9f548c7b8c5  node-v17.1.0-headers.tar.gz
9f28f927939d6f54af737810337cc65b054c3abf53913bdc6886c345c6aceb71  node-v17.1.0-headers.tar.xz
738e785768b1a266c60cf9147d8b0c8fbca2a98a7b72b00ad63b39f94d0577c7  node-v17.1.0-linux-arm64.tar.gz
0620262d87bc3567a25a40585e8065a5298ea213560d4774dcd52ce070252f39  node-v17.1.0-linux-arm64.tar.xz
c1cee85cee2f1e964ef915088098cf6332f07d0edc7eac03b21b24e2fbefe52b  node-v17.1.0-linux-armv7l.tar.gz
aab48ff2882b8956679d57b6c630899a55f3228590da36f620e8bbb25f9c3d23  node-v17.1.0-linux-armv7l.tar.xz
90cd3ae95a98a928553f708291635ca368f95029af181a34b2c64bb1d509fbea  node-v17.1.0-linux-ppc64le.tar.gz
28f268c6598977b82424c558113b54a6c63a341e0e3dd7b632008ac4d36f5419  node-v17.1.0-linux-ppc64le.tar.xz
34d77f33f2367b03e5d2bd1dc5f6a8b3854dd33dbdb4523a1d11dc9410a387d4  node-v17.1.0-linux-s390x.tar.gz
4a5181be0f67a56e91180bc27d14fa60db7f353e955c0c1b51d9f4c90adcbdc0  node-v17.1.0-linux-s390x.tar.xz
54b387bb1b6faa436d73777343a1a2147e67ff5e33a80971df2fd030dde4fd24  node-v17.1.0-linux-x64.tar.gz
f0db80870a2a4da6361b2e7779d43d6b163f1a0cb80250f3a8471150a0e4dbe4  node-v17.1.0-linux-x64.tar.xz
bf29185eb1e374657847704c819af2923e033e7308eca42ee9a83a11623ac170  node-v17.1.0.pkg
a5676b96974926fdf4b0e3040e09a4ab4e91980d2d2091fa62b77e2f74be5d6e  node-v17.1.0.tar.gz
6b803f37eb92b009f9162a489d647611aa95393e488fff0fc4fd8efd2effcac7  node-v17.1.0.tar.xz
d6ca33fa49c75346cef82306b70612bca7b2d65205d55d3e8f6c550ef98be6ed  node-v17.1.0-win-x64.7z
de8d90d82af888efa0e7942204539d2fca731013a2d715da954db410df58be8f  node-v17.1.0-win-x64.zip
c640c290f6e99b646f98e095c7bf92971345c386adf33e98688dd575c136a3f4  node-v17.1.0-win-x86.7z
54b1716910bb039533ce09480fe8db8cd3d4d556beee9ca1759e7a60f0465d0c  node-v17.1.0-win-x86.zip
8cd46fefbf7989a3a8145cd5c63ded6bfa7b89983e3969626d9930e060401b61  node-v17.1.0-x64.msi
5925efabdad8c7c084b4b3a8441b7bbbfe05bba7d83ad4b25b5bc9fc1b6647d4  node-v17.1.0-x86.msi
6bd725180c81586746922a236cdcd893cbc2740012ab47f10bfdac34d2d53f14  win-x64/node.exe
fc0564954ca50dc4c974791fa7dc8b283f60966e5b373cbd3945c05326dc0dd3  win-x64/node.lib
d67f7a0be06409069ac77a4a11fd1d1c77cacc984539ce0346f986a5a6e30218  win-x64/node_pdb.7z
f33493bef2224da50bcdc6fb9e1413d4072ced3fa1c2ba8693c627f57679d7f7  win-x64/node_pdb.zip
a6c2a28b4d2f0985b3d624b73f5ffc2866b7e17705752feace84e54b3a5d7f03  win-x86/node.exe
c304c1787bc8fe6925bec996fe67987219e8b035d0fdbb4ca766fba46db7853a  win-x86/node.lib
c26cf68939e1b0286ee2b8c1c7931289f309b1f8f8aa5b0cd775ac7660aafbb5  win-x86/node_pdb.7z
1305c28e4fff88fec779cc1a9190840c8cba28545adb2909e09563ab3aac9453  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEj8yhP+8dDC6RAI4Jdw96mlrhVgAFAmGKxu8ACgkQdw96mlrh
VgDHAA//elGilbGowLS5UzJXles3R0Q00LJuAalKItBowSU8gibi0HpstHYgtHNq
/qzHY4/Lszgc8T4AiZuidTn5V7Y/CY+Dn70/Cbm7ymgron1bWwLNJ1TzmM5+z+sP
u4BYsj8q3ZD82thaXkUlv2TW78xdXsrO0SW2hcyZi9VHZMDZyZK2M3j3pb9Z9t3z
EZuc/rDwTbKQ9RsY5snwBZdptqFQjrdELHzPv1oumnlolZJJBDHAKe9+MOX/v6GP
mK5Z16//KMgZyjIEsbvawSz+gN6wt8PwpuhusXdFsJnw+PQf2ffHg1I2L/JHZyD0
NvZyBXtWiZRxLxXvS+dPx48CyJ1CYQVQrmvKjxRr2jdgbsNndlCug8/V5uw+v+gI
L6yAaUH/T5n5BJCO7OcCfkD58T3AZYPuTxyyiGBcgYXIRKTeZ4BUkn+YbmNlZzUe
j7EUf/oM0xGiZoa9HhPXUGUAGGNvReJzveUEd95yyYTITMQl+HF6pY+NVHsRV/3Y
WIfxFknEPMoZp3YNRup7lXOMoKxslz3pn4RTeOCyqfXLOIeFLwC7V/0Swihy+zWz
Vzrj3iV/f/uhFOXnA7KEMFwPgR2ydTuXQxA4zGsZYXsaKpBYIB8Uv3EBxg2WVr5I
sGWioen2GlNo4M9U36TeXsvcTF5O2FvcwfZDRsbXYiaZS1YSvKA=
=24P7
-----END PGP SIGNATURE-----

```
