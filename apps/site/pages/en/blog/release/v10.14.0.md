---
date: '2018-11-28T00:42:23.395Z'
category: release
title: Node v10.14.0 (LTS)
layout: blog-post
author: <PERSON>
---

**This is a security release.** All Node.js users should consult the security release summary at /blog/vulnerability/november-2018-security-releases/ for details on patched vulnerabilities.

Fixes for the following CVEs are included in this release:

- Node.js: Denial of Service with large HTTP headers (CVE-2018-12121)
- Node.js: Slowloris HTTP Denial of Service (CVE-2018-12122 / Node.js)
- Node.js: Hostname spoofing in URL parser for javascript protocol (CVE-2018-12123)
- OpenSSL: Timing vulnerability in DSA signature generation (CVE-2018-0734)
- OpenSSL: Timing vulnerability in ECDSA signature generation (CVE-2019-0735)

### Notable Changes

- **deps**: Upgrade to OpenSSL 1.1.0j, fixing CVE-2018-0734 and CVE-2019-0735
- **http**:
  - Headers received by HTTP servers must not exceed 8192 bytes in total to prevent possible Denial of Service attacks. Reported by <PERSON>. (CVE-2018-12121 / <PERSON>)
  - A timeout of 40 seconds now applies to servers receiving HTTP headers. This value can be adjusted with `server.headersTimeout`. Where headers are not completely received within this period, the socket is destroyed on the next received chunk. In conjunction with `server.setTimeout()`, this aids in protecting against excessive resource retention and possible Denial of Service. Reported by Jan Maybach ([liebdich.com](https://liebdich.com)). (CVE-2018-12122 / Matteo Collina)
- **url**: Fix a bug that would allow a hostname being spoofed when parsing URLs with `url.parse()` with the `'javascript:'` protocol. Reported by [Martin Bajanik](https://twitter.com/_bayotop) ([Kentico](https://kenticocloud.com/)). (CVE-2018-12123 / Matteo Collina)

### Commits

- [[`38ca8baf81`](https://github.com/nodejs/node/commit/38ca8baf81)] - **deps**: update openssl 1.1.0 upgrade docs (Sam Roberts) [#24523](https://github.com/nodejs/node/pull/24523)
- [[`241ba81a5b`](https://github.com/nodejs/node/commit/241ba81a5b)] - **deps**: update archs files for OpenSSL-1.1.0 (Sam Roberts) [#24523](https://github.com/nodejs/node/pull/24523)
- [[`acc40efa90`](https://github.com/nodejs/node/commit/acc40efa90)] - **deps**: add s390 asm rules for OpenSSL-1.1.0 (Shigeki Ohtsu) [#24523](https://github.com/nodejs/node/pull/24523)
- [[`7efd184bb1`](https://github.com/nodejs/node/commit/7efd184bb1)] - **deps**: upgrade openssl sources to 1.1.0j (Sam Roberts) [#24523](https://github.com/nodejs/node/pull/24523)
- [[`a8532d4d23`](https://github.com/nodejs/node/commit/a8532d4d23)] - **deps,http**: http_parser set max header size to 8KB (Matteo Collina) [nodejs-private/node-private#143](https://github.com/nodejs-private/node-private/pull/143)
- [[`eb43bc04b1`](https://github.com/nodejs/node/commit/eb43bc04b1)] - **(SEMVER-MINOR)** **http,https**: protect against slow headers attack (Matteo Collina) [nodejs-private/node-private#150](https://github.com/nodejs-private/node-private/pull/150)
- [[`8b1405ee01`](https://github.com/nodejs/node/commit/8b1405ee01)] - **url**: avoid hostname spoofing w/ javascript protocol (Matteo Collina) [nodejs-private/node-private#145](https://github.com/nodejs-private/node-private/pull/145)

Windows 32-bit Installer: https://nodejs.org/dist/v10.14.0/node-v10.14.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v10.14.0/node-v10.14.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v10.14.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v10.14.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v10.14.0/node-v10.14.0.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v10.14.0/node-v10.14.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v10.14.0/node-v10.14.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v10.14.0/node-v10.14.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v10.14.0/node-v10.14.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v10.14.0/node-v10.14.0-aix-ppc64.tar.gz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v10.14.0/node-v10.14.0-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v10.14.0/node-v10.14.0-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v10.14.0/node-v10.14.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v10.14.0/node-v10.14.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v10.14.0/node-v10.14.0.tar.gz \
Other release files: https://nodejs.org/dist/v10.14.0/ \
Documentation: https://nodejs.org/docs/v10.14.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

d089875efec9b79a248a23ed7da86250b639fbca1572dba19323eb16321e58e1  node-v10.14.0-aix-ppc64.tar.gz
dd044aa0ddeb5e32fefa80a13b33bafe3f7e0536e15fe93c1e81b052c2f1965c  node-v10.14.0-darwin-x64.tar.gz
79b03f4e2db1dbb80d940a0550b175185c6509d7b798ed3a7521f38f11fa2a76  node-v10.14.0-darwin-x64.tar.xz
c09d53ab8f32683abaed06528eca5a40a0376ef2916c0e00cf510bdfc7eb7ddc  node-v10.14.0-headers.tar.gz
6a2fc03a2ff7b34332d805edfe95d20eff55b6ca34bf0eb29324b10784bed451  node-v10.14.0-headers.tar.xz
848868d24f1b237afd2d71b1749a21bdabafda2346bf404b1d4fa941d3d35982  node-v10.14.0-linux-arm64.tar.gz
4d80efe675c40d6d3af697e17e33cad8af1caf50655276ca99d0c4ca8e2f2cf2  node-v10.14.0-linux-arm64.tar.xz
f763009b48e51ed103afb8eea96c938f058165711559bac9bbe5d20d99f7c1a8  node-v10.14.0-linux-armv6l.tar.gz
686b87f2716c158e1aca84e462b3bcf1a33a99b140d11889bd59c7aa79293000  node-v10.14.0-linux-armv6l.tar.xz
7e441bf926b25717df84652c882221d3bfee63525a3d67a245dbd8e6e9f0ebd4  node-v10.14.0-linux-armv7l.tar.gz
a9a49fc81cf7b5b7db8a3ae91845ea5809b5964d00df5c222cd16a91e2f3aaf2  node-v10.14.0-linux-armv7l.tar.xz
ce658f47c24c0a150fb00d7bce1583cb9dc3f36d22e35efd993a3ce7ecc1c273  node-v10.14.0-linux-ppc64le.tar.gz
b6a15ef2061ac320ed0fa9ac352f56db3b30f0fa4962234ed5d73de4b75f8958  node-v10.14.0-linux-ppc64le.tar.xz
34942f6df4414de2566a97bc511026a6ed5db9e40ad513545a2025b252cec555  node-v10.14.0-linux-s390x.tar.gz
fa6ecbd6e688cd8389ae6db6a5c47061b81c63e97baa54ef6f89097de2b8df94  node-v10.14.0-linux-s390x.tar.xz
2f10d1a5d211a150d6813bdca8f3b1fe673a68fd534b1f547befec1314244596  node-v10.14.0-linux-x64.tar.gz
5f576f9893e0335f0c1b071a42fdf8b3e302577ad6ea38237aaef08ad0ca898e  node-v10.14.0-linux-x64.tar.xz
5d6dd4ff707c467db619452fe09c0dd6f99e3b935ddfeae9e0136595856565a2  node-v10.14.0.pkg
b124cda2f2c343c9cb4cf30b5403d7a5afa9ed5ecb5e28be0f91d307b1cb45e2  node-v10.14.0-sunos-x64.tar.gz
2165548cc079e74993440d0bc152a584d597101218205a0e711ae103290907b1  node-v10.14.0-sunos-x64.tar.xz
2d5a203249c89917c3002e6cb9c8870ceb2a10350c675bd085a4cd8ca4fa3a2e  node-v10.14.0.tar.gz
b05a52e556df813eb3f36c795784956b1c3c3b078cef930af6c1f2fb93642929  node-v10.14.0.tar.xz
74324dc5242c8f630a61118c185d092d6345907b3bda9e63a015ff9611be9e6e  node-v10.14.0-win-x64.7z
a3acbbdbbdb6ff6d5ae6e6f5ccea55aef83cfa54f52c080538edc3ac6326797b  node-v10.14.0-win-x64.zip
0b8d4082dfdaaf6e19c586111ee5f755d2d0639024ec65c91747e30c06f63dbb  node-v10.14.0-win-x86.7z
45a6f1f5c274db9727828649094a2f068705f65c6b651edbcd206c6656ce4213  node-v10.14.0-win-x86.zip
4f691619b9230a774c89c926f5e406825b70789a2fd33621e2cb8232848a70cd  node-v10.14.0-x64.msi
ba68e4a8cdf73e9fef9a3171940d2aeea49e2fc10d69a7b765215e570b2c45d4  node-v10.14.0-x86.msi
8f741de13240d1fc185a293c3ac71e87b9b755d14c4b68b60e21128cd4af426f  win-x64/node.exe
19e703f4a64fea62425f18d96073bd2703b3869c8c7844c5b92bb823f34aafc4  win-x64/node.lib
6e0fa9017d0d8b2e6842c8e3dce84925dbd2c13750f1ad69d8f11b2d28b86fbb  win-x64/node_pdb.7z
de5a279bf1bb0157598c469c5f9156b86de477d9ca75ef5c7a00b172b2a6cea0  win-x64/node_pdb.zip
b0b0505658061a75de1de1a5df9cd6e71a7336d17f51da4e46fc8b107aa14323  win-x86/node.exe
2478c2eb074cab1bf0f3d6001eaf554c0b86b40460696308c12f31fafeed7077  win-x86/node.lib
9015c07c06c3972d20e83d78769285a4c6757495b0f46dd1cb986a82868708ce  win-x86/node_pdb.7z
4d0dcea36768fd2e93de069c29b444fb2779e8df0561bb4ec8089652a1271416  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQEzBAEBCAAdFiEE3Y8jOLrnUB491ax4wnN5L32DVF0FAlv95AUACgkQwnN5L32D
VF2JhAgAlGbTVz0eqLEU1nIt0KgCiKRO1Fb9x3M0xQOU0nQaZCDf7fnS64jIDWEp
yQgm2JL9zdr8HA6jMcnzBZfjoh3hZZ0YjwR/mk32+0RXExqNT0pKfAQQR6dV9NQN
xJSX77dkt8KFnDBkg3oeEEyXG9WmeoxY0OlZxoIQfNNSpBGlcH7Oqs0XXpnZsDUv
6BtjygrmqPeYkD4hlHyNn50Ra47wAmq02PaEgPkJzQ3gYXtbNvgbmwmLplX/WH7y
ZVAyJ7k4G+y32Oyo+872eqseJUpzmg4K+wtteruGAxa2zusuEP0PYb0zgm0egate
JXV30reBvQ2KAYMOjCq52qfOczNsEw==
=KWLK
-----END PGP SIGNATURE-----

```
