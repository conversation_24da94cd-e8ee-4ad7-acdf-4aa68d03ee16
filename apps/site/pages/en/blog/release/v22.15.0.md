---
date: '2025-04-23T07:27:50.679Z'
category: release
title: Node v22.15.0 (LTS)
layout: blog-post
author: <PERSON><PERSON><PERSON>
---

## 2025-04-23, Version 22.15.0 'Jod' (LTS), @UlisesGascon prepared by @RafaelGSS

### Notable Changes

- \[[`3c88f3938b`](https://github.com/nodejs/node/commit/3c88f3938b)] - **(SEMVER-MINOR)** **assert**: implement partial error comparison (<PERSON><PERSON>) [#57370](https://github.com/nodejs/node/pull/57370)
- \[[`db19a3f9fc`](https://github.com/nodejs/node/commit/db19a3f9fc)] - **(SEMVER-MINOR)** **assert**: improve partialDeepStrictEqual (<PERSON><PERSON>) [#57370](https://github.com/nodejs/node/pull/57370)
- \[[`1ee5f840b4`](https://github.com/nodejs/node/commit/1ee5f840b4)] - **(SEMVER-MINOR)** **cli**: allow --cpu-prof\* in NODE_OPTIONS (Carlos Espa) [#57018](https://github.com/nodejs/node/pull/57018)
- \[[`872ee0f2ac`](https://github.com/nodejs/node/commit/872ee0f2ac)] - **crypto**: update root certificates to NSS 3.108 (Node.js GitHub Bot) [#57381](https://github.com/nodejs/node/pull/57381)
- \[[`03a0f3a56b`](https://github.com/nodejs/node/commit/03a0f3a56b)] - **(SEMVER-MINOR)** **crypto**: support --use-system-ca on Windows (Joyee Cheung) [#56833](https://github.com/nodejs/node/pull/56833)
- \[[`94647bbdb2`](https://github.com/nodejs/node/commit/94647bbdb2)] - **(SEMVER-MINOR)** **crypto**: added support for reading certificates from macOS system store (Tim Jacomb) [#56599](https://github.com/nodejs/node/pull/56599)
- \[[`8f7b86a6e7`](https://github.com/nodejs/node/commit/8f7b86a6e7)] - **deps**: update timezone to 2025a (Node.js GitHub Bot) [#56876](https://github.com/nodejs/node/pull/56876)
- \[[`f9f611fb58`](https://github.com/nodejs/node/commit/f9f611fb58)] - **(SEMVER-MINOR)** **deps,tools**: add zstd 1.5.6 (Jan Martin) [#52100](https://github.com/nodejs/node/pull/52100)
- \[[`07a6d5f8cf`](https://github.com/nodejs/node/commit/07a6d5f8cf)] - **(SEMVER-MINOR)** **dns**: add TLSA record query and parsing (Rithvik Vibhu) [#52983](https://github.com/nodejs/node/pull/52983)
- \[[`d8a83ef2f3`](https://github.com/nodejs/node/commit/d8a83ef2f3)] - **doc**: add @geeksilva97 to collaborators (Edy Silva) [#57241](https://github.com/nodejs/node/pull/57241)
- \[[`6b93ba723b`](https://github.com/nodejs/node/commit/6b93ba723b)] - **(SEMVER-MINOR)** **module**: use synchronous hooks for preparsing in import(cjs) (Joyee Cheung) [#55698](https://github.com/nodejs/node/pull/55698)
- \[[`b2e44a8079`](https://github.com/nodejs/node/commit/b2e44a8079)] - **(SEMVER-MINOR)** **module**: implement module.registerHooks() (Joyee Cheung) [#55698](https://github.com/nodejs/node/pull/55698)
- \[[`dc91ae7471`](https://github.com/nodejs/node/commit/dc91ae7471)] - **(SEMVER-MINOR)** **process**: add execve (Paolo Insogna) [#56496](https://github.com/nodejs/node/pull/56496)
- \[[`bc672fcfdd`](https://github.com/nodejs/node/commit/bc672fcfdd)] - **(SEMVER-MINOR)** **sqlite**: allow returning `ArrayBufferView`s from user-defined functions (René) [#56790](https://github.com/nodejs/node/pull/56790)
- \[[`5edee197ab`](https://github.com/nodejs/node/commit/5edee197ab)] - **(SEMVER-MINOR)** **tls**: implement tls.getCACertificates() (Joyee Cheung) [#57107](https://github.com/nodejs/node/pull/57107)
- \[[`f9fe0e09ee`](https://github.com/nodejs/node/commit/f9fe0e09ee)] - **(SEMVER-MINOR)** **util**: expose diff function used by the assertion errors (Giovanni Bucci) [#57462](https://github.com/nodejs/node/pull/57462)
- \[[`673a424180`](https://github.com/nodejs/node/commit/673a424180)] - **(SEMVER-MINOR)** **v8**: add v8.getCppHeapStatistics() method (Aditi) [#57146](https://github.com/nodejs/node/pull/57146)
- \[[`4991e5d826`](https://github.com/nodejs/node/commit/4991e5d826)] - **(SEMVER-MINOR)** **zlib**: add zstd support (Jan Martin) [#52100](https://github.com/nodejs/node/pull/52100)

### Commits

- \[[`ea70a379c3`](https://github.com/nodejs/node/commit/ea70a379c3)] - **assert**: improve partialDeepStrictEqual performance (Ruben Bridgewater) [#57509](https://github.com/nodejs/node/pull/57509)
- \[[`2b419d7e79`](https://github.com/nodejs/node/commit/2b419d7e79)] - **(SEMVER-MINOR)** **assert**: implement partial error comparison (Ruben Bridgewater) [#57370](https://github.com/nodejs/node/pull/57370)
- \[[`d817c17fd7`](https://github.com/nodejs/node/commit/d817c17fd7)] - **(SEMVER-MINOR)** **assert**: improve partialDeepStrictEqual (Ruben Bridgewater) [#57370](https://github.com/nodejs/node/pull/57370)
- \[[`7af0440073`](https://github.com/nodejs/node/commit/7af0440073)] - **assert**: improve myers diff performance (Giovanni Bucci) [#57279](https://github.com/nodejs/node/pull/57279)
- \[[`01cf5fb871`](https://github.com/nodejs/node/commit/01cf5fb871)] - **(SEMVER-MINOR)** **assert,util**: improve performance (Ruben Bridgewater) [#57370](https://github.com/nodejs/node/pull/57370)
- \[[`a58842cee4`](https://github.com/nodejs/node/commit/a58842cee4)] - **(SEMVER-MINOR)** **benchmark**: adjust assert runtimes (Ruben Bridgewater) [#57370](https://github.com/nodejs/node/pull/57370)
- \[[`b20b3697aa`](https://github.com/nodejs/node/commit/b20b3697aa)] - **(SEMVER-MINOR)** **benchmark**: skip running some assert benchmarks by default (Ruben Bridgewater) [#57370](https://github.com/nodejs/node/pull/57370)
- \[[`ec5570fd1e`](https://github.com/nodejs/node/commit/ec5570fd1e)] - **(SEMVER-MINOR)** **benchmark**: add assert partialDeepStrictEqual benchmark (Ruben Bridgewater) [#57370](https://github.com/nodejs/node/pull/57370)
- \[[`b991bf4ca6`](https://github.com/nodejs/node/commit/b991bf4ca6)] - **benchmark**: add a warmup on bench-openSync (Elves Vieira) [#57051](https://github.com/nodejs/node/pull/57051)
- \[[`4a455bc806`](https://github.com/nodejs/node/commit/4a455bc806)] - **build**: fix update-wpt workflow (Jonas) [#57468](https://github.com/nodejs/node/pull/57468)
- \[[`6ec397e61c`](https://github.com/nodejs/node/commit/6ec397e61c)] - **build**: fix compatibility with V8's `depot_tools` (Richard Lau) [#57330](https://github.com/nodejs/node/pull/57330)
- \[[`475aaca336`](https://github.com/nodejs/node/commit/475aaca336)] - **build**: print 'Formatting Markdown...' for long task markdown formatting (1ilsang) [#57108](https://github.com/nodejs/node/pull/57108)
- \[[`73fced7a97`](https://github.com/nodejs/node/commit/73fced7a97)] - **build**: fix GN build failure (Cheng) [#57013](https://github.com/nodejs/node/pull/57013)
- \[[`af05f91425`](https://github.com/nodejs/node/commit/af05f91425)] - **build**: fix GN build of uv (Cheng) [#56955](https://github.com/nodejs/node/pull/56955)
- \[[`fd3053e947`](https://github.com/nodejs/node/commit/fd3053e947)] - **build**: gyp exclude libm linking on macOS (deepak1556) [#56901](https://github.com/nodejs/node/pull/56901)
- \[[`5ec6b9a50f`](https://github.com/nodejs/node/commit/5ec6b9a50f)] - **build**: remove explicit linker call to libm on macOS (deepak1556) [#56901](https://github.com/nodejs/node/pull/56901)
- \[[`a893da9be7`](https://github.com/nodejs/node/commit/a893da9be7)] - **build**: link with Security.framework in GN build (Cheng) [#56895](https://github.com/nodejs/node/pull/56895)
- \[[`02cd8e0a50`](https://github.com/nodejs/node/commit/02cd8e0a50)] - **build**: do not put commands in sources variables (Cheng) [#56885](https://github.com/nodejs/node/pull/56885)
- \[[`73dc8c2140`](https://github.com/nodejs/node/commit/73dc8c2140)] - **build**: add double quotes around <(python) (Luigi Pinca) [#56826](https://github.com/nodejs/node/pull/56826)
- \[[`65a3b5f73c`](https://github.com/nodejs/node/commit/65a3b5f73c)] - **build**: add build option suppress_all_error_on_warn (Michael Dawson) [#56647](https://github.com/nodejs/node/pull/56647)
- \[[`424aacc942`](https://github.com/nodejs/node/commit/424aacc942)] - **build,win**: disable node pch with ccache (Stefan Stojanovic) [#57224](https://github.com/nodejs/node/pull/57224)
- \[[`901685c723`](https://github.com/nodejs/node/commit/901685c723)] - **build,win**: enable ccache (Stefan Stojanovic) [#56847](https://github.com/nodejs/node/pull/56847)
- \[[`79987676c1`](https://github.com/nodejs/node/commit/79987676c1)] - **cli**: clarify --cpu-prof-name allowed values (Eugenio Ceschia) [#57433](https://github.com/nodejs/node/pull/57433)
- \[[`503d4237aa`](https://github.com/nodejs/node/commit/503d4237aa)] - **(SEMVER-MINOR)** **cli**: allow --cpu-prof\* in NODE_OPTIONS (Carlos Espa) [#57018](https://github.com/nodejs/node/pull/57018)
- \[[`ada572b733`](https://github.com/nodejs/node/commit/ada572b733)] - **crypto**: ensure expected JWK alg in SubtleCrypto.importKey RSA imports (Filip Skokan) [#57450](https://github.com/nodejs/node/pull/57450)
- \[[`7e5aabde55`](https://github.com/nodejs/node/commit/7e5aabde55)] - **crypto**: update root certificates to NSS 3.108 (Node.js GitHub Bot) [#57381](https://github.com/nodejs/node/pull/57381)
- \[[`7ea6ac1e09`](https://github.com/nodejs/node/commit/7ea6ac1e09)] - **crypto**: add support for intermediate certs in --use-system-ca (Tim Jacomb) [#57164](https://github.com/nodejs/node/pull/57164)
- \[[`44b19ec534`](https://github.com/nodejs/node/commit/44b19ec534)] - **crypto**: support --use-system-ca on non-Windows and non-macOS (Joyee Cheung) [#57009](https://github.com/nodejs/node/pull/57009)
- \[[`e21d126438`](https://github.com/nodejs/node/commit/e21d126438)] - **crypto**: fix missing OPENSSL_NO_ENGINE guard (Shelley Vohr) [#57012](https://github.com/nodejs/node/pull/57012)
- \[[`2fdf82b357`](https://github.com/nodejs/node/commit/2fdf82b357)] - **crypto**: cleanup root certificates and skip PEM deserialization (Joyee Cheung) [#56999](https://github.com/nodejs/node/pull/56999)
- \[[`03a0f3a56b`](https://github.com/nodejs/node/commit/03a0f3a56b)] - **(SEMVER-MINOR)** **crypto**: support --use-system-ca on Windows (Joyee Cheung) [#56833](https://github.com/nodejs/node/pull/56833)
- \[[`bbdb10bc2c`](https://github.com/nodejs/node/commit/bbdb10bc2c)] - **crypto**: fix X509\* leak in --use-system-ca (Joyee Cheung) [#56832](https://github.com/nodejs/node/pull/56832)
- \[[`5470cab6d3`](https://github.com/nodejs/node/commit/5470cab6d3)] - **crypto**: add api to get openssl security level (Michael Dawson) [#56601](https://github.com/nodejs/node/pull/56601)
- \[[`94647bbdb2`](https://github.com/nodejs/node/commit/94647bbdb2)] - **(SEMVER-MINOR)** **crypto**: added support for reading certificates from macOS system store (Tim Jacomb) [#56599](https://github.com/nodejs/node/pull/56599)
- \[[`caf81ca549`](https://github.com/nodejs/node/commit/caf81ca549)] - **debugger**: fix behavior of plain object exec in debugger repl (Dario Piotrowicz) [#57498](https://github.com/nodejs/node/pull/57498)
- \[[`1d703fe220`](https://github.com/nodejs/node/commit/1d703fe220)] - **deps**: update c-ares to v1.34.5 (Node.js GitHub Bot) [#57792](https://github.com/nodejs/node/pull/57792)
- \[[`98457dfea3`](https://github.com/nodejs/node/commit/98457dfea3)] - **deps**: update undici to 6.21.2 (Matteo Collina) [#57442](https://github.com/nodejs/node/pull/57442)
- \[[`4a852ba11b`](https://github.com/nodejs/node/commit/4a852ba11b)] - **deps**: V8: cherry-pick c172ffc5bf54 (Choongwoo Han) [#57437](https://github.com/nodejs/node/pull/57437)
- \[[`54a12e0bcc`](https://github.com/nodejs/node/commit/54a12e0bcc)] - **deps**: update googletest to 0bdccf4 (Node.js GitHub Bot) [#57380](https://github.com/nodejs/node/pull/57380)
- \[[`2e350963e5`](https://github.com/nodejs/node/commit/2e350963e5)] - **deps**: update acorn to 8.14.1 (Node.js GitHub Bot) [#57382](https://github.com/nodejs/node/pull/57382)
- \[[`95e5d01c25`](https://github.com/nodejs/node/commit/95e5d01c25)] - **deps**: update amaro to 0.4.1 (marco-ippolito) [#57121](https://github.com/nodejs/node/pull/57121)
- \[[`ef216deb05`](https://github.com/nodejs/node/commit/ef216deb05)] - **deps**: update amaro to 0.3.2 (marco-ippolito) [#56916](https://github.com/nodejs/node/pull/56916)
- \[[`4ef4d6ecf6`](https://github.com/nodejs/node/commit/4ef4d6ecf6)] - **deps**: update amaro to 0.3.1 (Node.js GitHub Bot) [#56785](https://github.com/nodejs/node/pull/56785)
- \[[`a8bf5ef4a7`](https://github.com/nodejs/node/commit/a8bf5ef4a7)] - **deps**: update simdjson to 3.12.2 (Node.js GitHub Bot) [#57084](https://github.com/nodejs/node/pull/57084)
- \[[`0bd612bb32`](https://github.com/nodejs/node/commit/0bd612bb32)] - **deps**: update archs files for openssl-3.0.16 (Node.js GitHub Bot) [#57335](https://github.com/nodejs/node/pull/57335)
- \[[`7d65f79306`](https://github.com/nodejs/node/commit/7d65f79306)] - **deps**: upgrade openssl sources to quictls/openssl-3.0.16 (Node.js GitHub Bot) [#57335](https://github.com/nodejs/node/pull/57335)
- \[[`5c88c52491`](https://github.com/nodejs/node/commit/5c88c52491)] - **deps**: update corepack to 0.32.0 (Node.js GitHub Bot) [#57265](https://github.com/nodejs/node/pull/57265)
- \[[`fa04bf4999`](https://github.com/nodejs/node/commit/fa04bf4999)] - **deps**: update gyp file for ngtcp2 1.11.0 (Richard Lau) [#57225](https://github.com/nodejs/node/pull/57225)
- \[[`ca6b07258d`](https://github.com/nodejs/node/commit/ca6b07258d)] - **deps**: update cjs-module-lexer to 2.1.0 (Node.js GitHub Bot) [#57180](https://github.com/nodejs/node/pull/57180)
- \[[`0a72b16fe1`](https://github.com/nodejs/node/commit/0a72b16fe1)] - **deps**: update ngtcp2 to 1.11.0 (Node.js GitHub Bot) [#57179](https://github.com/nodejs/node/pull/57179)
- \[[`600fb41f54`](https://github.com/nodejs/node/commit/600fb41f54)] - **deps**: update sqlite to 3.49.1 (Node.js GitHub Bot) [#57178](https://github.com/nodejs/node/pull/57178)
- \[[`7eb3b44010`](https://github.com/nodejs/node/commit/7eb3b44010)] - **deps**: update zlib to 1.3.0.1-motley-788cb3c (Node.js GitHub Bot) [#56655](https://github.com/nodejs/node/pull/56655)
- \[[`257d22e181`](https://github.com/nodejs/node/commit/257d22e181)] - **deps**: update sqlite to 3.49.0 (Node.js GitHub Bot) [#56654](https://github.com/nodejs/node/pull/56654)
- \[[`53a7bfce01`](https://github.com/nodejs/node/commit/53a7bfce01)] - **deps**: V8: cherry-pick 9ab40592f697 (Levi Zim) [#56781](https://github.com/nodejs/node/pull/56781)
- \[[`636f65cb1a`](https://github.com/nodejs/node/commit/636f65cb1a)] - **deps**: update cjs-module-lexer to 2.0.0 (Michael Dawson) [#56855](https://github.com/nodejs/node/pull/56855)
- \[[`8f7b86a6e7`](https://github.com/nodejs/node/commit/8f7b86a6e7)] - **deps**: update timezone to 2025a (Node.js GitHub Bot) [#56876](https://github.com/nodejs/node/pull/56876)
- \[[`db31276bfa`](https://github.com/nodejs/node/commit/db31276bfa)] - **deps**: update simdjson to 3.12.0 (Node.js GitHub Bot) [#56874](https://github.com/nodejs/node/pull/56874)
- \[[`d1d58d6198`](https://github.com/nodejs/node/commit/d1d58d6198)] - **deps**: update googletest to e235eb3 (Node.js GitHub Bot) [#56873](https://github.com/nodejs/node/pull/56873)
- \[[`05b3dff275`](https://github.com/nodejs/node/commit/05b3dff275)] - **deps**: update simdjson to 3.11.6 (Node.js GitHub Bot) [#56250](https://github.com/nodejs/node/pull/56250)
- \[[`f9f611fb58`](https://github.com/nodejs/node/commit/f9f611fb58)] - **(SEMVER-MINOR)** **deps,tools**: add zstd 1.5.6 (Jan Martin) [#52100](https://github.com/nodejs/node/pull/52100)
- \[[`ef212a41a7`](https://github.com/nodejs/node/commit/ef212a41a7)] - **dns**: restore dns query cache ttl (Ethan Arrowood) [#57640](https://github.com/nodejs/node/pull/57640)
- \[[`7a10b01e97`](https://github.com/nodejs/node/commit/7a10b01e97)] - **dns**: remove redundant code using common variable (Deokjin Kim) [#57386](https://github.com/nodejs/node/pull/57386)
- \[[`bc2603f086`](https://github.com/nodejs/node/commit/bc2603f086)] - **(SEMVER-MINOR)** **dns**: add TLSA record query and parsing (Rithvik Vibhu) [#52983](https://github.com/nodejs/node/pull/52983)
- \[[`38a2e5d60b`](https://github.com/nodejs/node/commit/38a2e5d60b)] - **doc**: add gurgunday as triager (Gürgün Dayıoğlu) [#57594](https://github.com/nodejs/node/pull/57594)
- \[[`b7ac0bd129`](https://github.com/nodejs/node/commit/b7ac0bd129)] - **doc**: clarify behaviour of node-api adjust function (Michael Dawson) [#57463](https://github.com/nodejs/node/pull/57463)
- \[[`fa834896c8`](https://github.com/nodejs/node/commit/fa834896c8)] - **doc**: remove Corepack documentation (Antoine du Hamel) [#57635](https://github.com/nodejs/node/pull/57635)
- \[[`8988173286`](https://github.com/nodejs/node/commit/8988173286)] - **doc**: remove mention of `--require` not supporting ES modules (Huáng Jùnliàng) [#57620](https://github.com/nodejs/node/pull/57620)
- \[[`3a7d179dbd`](https://github.com/nodejs/node/commit/3a7d179dbd)] - **doc**: mention reports should align with Node.js CoC (Rafael Gonzaga) [#57607](https://github.com/nodejs/node/pull/57607)
- \[[`983c5087f6`](https://github.com/nodejs/node/commit/983c5087f6)] - **doc**: add section stating that very stale PRs should be closed (Dario Piotrowicz) [#57541](https://github.com/nodejs/node/pull/57541)
- \[[`f4e1f702d4`](https://github.com/nodejs/node/commit/f4e1f702d4)] - **doc**: add bjohansebas as triager (Sebastian Beltran) [#57564](https://github.com/nodejs/node/pull/57564)
- \[[`9b7fd6b076`](https://github.com/nodejs/node/commit/9b7fd6b076)] - **doc**: update support channels (Claudio W.) [#57538](https://github.com/nodejs/node/pull/57538)
- \[[`ef624aff55`](https://github.com/nodejs/node/commit/ef624aff55)] - **doc**: remove cryptoStream API reference (Jonas) [#57579](https://github.com/nodejs/node/pull/57579)
- \[[`4a2afc255a`](https://github.com/nodejs/node/commit/4a2afc255a)] - **doc**: module resolution pseudocode corrections (Marcel Laverdet) [#57080](https://github.com/nodejs/node/pull/57080)
- \[[`ee5059426d`](https://github.com/nodejs/node/commit/ee5059426d)] - **doc**: add history entry for DEP0190 in `child_process.md` (Antoine du Hamel) [#57544](https://github.com/nodejs/node/pull/57544)
- \[[`4deebb4fca`](https://github.com/nodejs/node/commit/4deebb4fca)] - **doc**: remove deprecated pattern in `child_process.md` (Antoine du Hamel) [#57568](https://github.com/nodejs/node/pull/57568)
- \[[`6cd7b37d9c`](https://github.com/nodejs/node/commit/6cd7b37d9c)] - **doc**: mark multiple experimental APIS as stable (James M Snell) [#57510](https://github.com/nodejs/node/pull/57510)
- \[[`c2f1fa0928`](https://github.com/nodejs/node/commit/c2f1fa0928)] - **doc**: remove mertcanaltin from Triagers (Mert Can Altin) [#57531](https://github.com/nodejs/node/pull/57531)
- \[[`9b6047e520`](https://github.com/nodejs/node/commit/9b6047e520)] - **doc**: recommend watching the collaborators repo in the onboarding doc (Darshan Sen) [#57527](https://github.com/nodejs/node/pull/57527)
- \[[`bf1e297079`](https://github.com/nodejs/node/commit/bf1e297079)] - **doc**: remove mention of visa fees from onboarding doc (Darshan Sen) [#57526](https://github.com/nodejs/node/pull/57526)
- \[[`**********`](https://github.com/nodejs/node/commit/**********)] - **doc**: deprecate passing `args` to `spawn` and `execFile` (Antoine du Hamel) [#57389](https://github.com/nodejs/node/pull/57389)
- \[[`06994d5a75`](https://github.com/nodejs/node/commit/06994d5a75)] - **doc**: remove some inconsistencies in `deprecations.md` (Antoine du Hamel) [#57512](https://github.com/nodejs/node/pull/57512)
- \[[`707f851ba3`](https://github.com/nodejs/node/commit/707f851ba3)] - **doc**: run license-builder (github-actions\[bot]) [#57511](https://github.com/nodejs/node/pull/57511)
- \[[`a7793195d6`](https://github.com/nodejs/node/commit/a7793195d6)] - **doc**: add new writing-docs contributing md (Dario Piotrowicz) [#57502](https://github.com/nodejs/node/pull/57502)
- \[[`30d4a43b3d`](https://github.com/nodejs/node/commit/30d4a43b3d)] - **doc**: add node.js streams references to Web Streams doc (Dario Piotrowicz) [#57393](https://github.com/nodejs/node/pull/57393)
- \[[`e08365980b`](https://github.com/nodejs/node/commit/e08365980b)] - **doc**: prefer to sign commits under nodejs repository (Rafael Gonzaga) [#57311](https://github.com/nodejs/node/pull/57311)
- \[[`c35e1f9048`](https://github.com/nodejs/node/commit/c35e1f9048)] - **doc**: fixed the incorrect splitting of multiple words (letianpailove) [#57454](https://github.com/nodejs/node/pull/57454)
- \[[`3e1f3bc2bb`](https://github.com/nodejs/node/commit/3e1f3bc2bb)] - **doc**: add review guidelines for collaborator nominations (Antoine du Hamel) [#57449](https://github.com/nodejs/node/pull/57449)
- \[[`fef3f82a41`](https://github.com/nodejs/node/commit/fef3f82a41)] - **doc**: add history info for --use-system-ca (Darshan Sen) [#57432](https://github.com/nodejs/node/pull/57432)
- \[[`96afdf949d`](https://github.com/nodejs/node/commit/96afdf949d)] - **doc**: remove typo YAML snippet from tls.getCACertificates doc (Darshan Sen) [#57459](https://github.com/nodejs/node/pull/57459)
- \[[`800d61d47e`](https://github.com/nodejs/node/commit/800d61d47e)] - **doc**: fix typo in sqlite.md (Tobias Nießen) [#57473](https://github.com/nodejs/node/pull/57473)
- \[[`4876aee775`](https://github.com/nodejs/node/commit/4876aee775)] - **doc**: explicit mention arbitrary code execution as a vuln (Rafael Gonzaga) [#57426](https://github.com/nodejs/node/pull/57426)
- \[[`2dd72c658f`](https://github.com/nodejs/node/commit/2dd72c658f)] - **doc**: update maintaining-openssl.md for openssl (Richard Lau) [#57413](https://github.com/nodejs/node/pull/57413)
- \[[`a49fd31f04`](https://github.com/nodejs/node/commit/a49fd31f04)] - **doc**: add missing `deprecated` badges in `fs.md` (Yukihiro Hasegawa) [#57384](https://github.com/nodejs/node/pull/57384)
- \[[`3a4ed77674`](https://github.com/nodejs/node/commit/3a4ed77674)] - **doc**: add note about sync nodejs-private branches (Rafael Gonzaga) [#57404](https://github.com/nodejs/node/pull/57404)
- \[[`1025e6dc7c`](https://github.com/nodejs/node/commit/1025e6dc7c)] - **doc**: update Xcode version used for arm64 and pkg (Michaël Zasso) [#57104](https://github.com/nodejs/node/pull/57104)
- \[[`77b9e04a70`](https://github.com/nodejs/node/commit/77b9e04a70)] - **doc**: improve type stripping documentation (Marco Ippolito) [#56916](https://github.com/nodejs/node/pull/56916)
- \[[`3a75e8410d`](https://github.com/nodejs/node/commit/3a75e8410d)] - **doc**: specificy support for erasable ts syntax (Marco Ippolito) [#56916](https://github.com/nodejs/node/pull/56916)
- \[[`69f12f9686`](https://github.com/nodejs/node/commit/69f12f9686)] - **doc**: make first parameter optional in `util.getCallSites` (Deokjin Kim) [#57387](https://github.com/nodejs/node/pull/57387)
- \[[`2b4e737ffb`](https://github.com/nodejs/node/commit/2b4e737ffb)] - **doc**: fix usage of module.registerSync in comment (Timo Kössler) [#57328](https://github.com/nodejs/node/pull/57328)
- \[[`f320593958`](https://github.com/nodejs/node/commit/f320593958)] - **doc**: add Darshan back as voting TSC member (Michael Dawson) [#57402](https://github.com/nodejs/node/pull/57402)
- \[[`2b7765469a`](https://github.com/nodejs/node/commit/2b7765469a)] - **doc**: revise webcrypto.md types, interfaces, and added versions (Filip Skokan) [#57376](https://github.com/nodejs/node/pull/57376)
- \[[`649828c74a`](https://github.com/nodejs/node/commit/649828c74a)] - **doc**: add info on how project manages social media (Michael Dawson) [#57318](https://github.com/nodejs/node/pull/57318)
- \[[`2a2e1cfd71`](https://github.com/nodejs/node/commit/2a2e1cfd71)] - **doc**: revise `tsconfig.json` note (Steven) [#57353](https://github.com/nodejs/node/pull/57353)
- \[[`17883b1d46`](https://github.com/nodejs/node/commit/17883b1d46)] - **doc**: use more clear name in getSystemErrorMessage's example (ikuma-t) [#57310](https://github.com/nodejs/node/pull/57310)
- \[[`7feed9989b`](https://github.com/nodejs/node/commit/7feed9989b)] - **doc**: recommend setting `noEmit: true` in `tsconfig.json` (Steven) [#57320](https://github.com/nodejs/node/pull/57320)
- \[[`fe707ab162`](https://github.com/nodejs/node/commit/fe707ab162)] - **doc**: ping nodejs/tsc for each security pull request (Rafael Gonzaga) [#57309](https://github.com/nodejs/node/pull/57309)
- \[[`f3c58ab693`](https://github.com/nodejs/node/commit/f3c58ab693)] - **doc**: fix Windows ccache section position (Stefan Stojanovic) [#57326](https://github.com/nodejs/node/pull/57326)
- \[[`e69170bacd`](https://github.com/nodejs/node/commit/e69170bacd)] - **doc**: update node-api version matrix (Chengzhong Wu) [#57287](https://github.com/nodejs/node/pull/57287)
- \[[`0bc1fd2245`](https://github.com/nodejs/node/commit/0bc1fd2245)] - **doc**: recommend `erasableSyntaxOnly` in ts docs (Rob Palmer) [#57271](https://github.com/nodejs/node/pull/57271)
- \[[`068013744e`](https://github.com/nodejs/node/commit/068013744e)] - **doc**: clarify `path.isAbsolute` is not path traversal mitigation (Eric Fortis) [#57073](https://github.com/nodejs/node/pull/57073)
- \[[`238b0e856e`](https://github.com/nodejs/node/commit/238b0e856e)] - **doc**: fix rendering of DEP0174 description (David Sanders) [#56835](https://github.com/nodejs/node/pull/56835)
- \[[`db0bcefd14`](https://github.com/nodejs/node/commit/db0bcefd14)] - **doc**: add 1ilsang to triage team (1ilsang) [#57183](https://github.com/nodejs/node/pull/57183)
- \[[`52a593feab`](https://github.com/nodejs/node/commit/52a593feab)] - **doc**: add @geeksilva97 to collaborators (Edy Silva) [#57241](https://github.com/nodejs/node/pull/57241)
- \[[`89f4475e32`](https://github.com/nodejs/node/commit/89f4475e32)] - **doc**: add missing assert return types (Colin Ihrig) [#57219](https://github.com/nodejs/node/pull/57219)
- \[[`62b6d94c03`](https://github.com/nodejs/node/commit/62b6d94c03)] - **doc**: add streamResetBurst and streamResetRate (Sujal Raj) [#57195](https://github.com/nodejs/node/pull/57195)
- \[[`f150017e70`](https://github.com/nodejs/node/commit/f150017e70)] - **doc**: add esm examples to node:util (Alfredo González) [#56793](https://github.com/nodejs/node/pull/56793)
- \[[`99465ffa9c`](https://github.com/nodejs/node/commit/99465ffa9c)] - **doc**: update options to filehandle.appendFile() (Hasegawa-Yukihiro) [#56972](https://github.com/nodejs/node/pull/56972)
- \[[`6242520a90`](https://github.com/nodejs/node/commit/6242520a90)] - **doc**: add additional caveat for fs.watch (Michael Dawson) [#57150](https://github.com/nodejs/node/pull/57150)
- \[[`19cda4791a`](https://github.com/nodejs/node/commit/19cda4791a)] - **doc**: fix typo in Windows building instructions (Tim Jacomb) [#57158](https://github.com/nodejs/node/pull/57158)
- \[[`ef206add59`](https://github.com/nodejs/node/commit/ef206add59)] - **doc**: fix web.libera.chat link in pull-requests.md (Samuel Bronson) [#57076](https://github.com/nodejs/node/pull/57076)
- \[[`7243c1713d`](https://github.com/nodejs/node/commit/7243c1713d)] - **doc**: remove buffered flag from performance hooks examples (Pavel Romanov) [#52607](https://github.com/nodejs/node/pull/52607)
- \[[`617fe71f67`](https://github.com/nodejs/node/commit/617fe71f67)] - **doc**: fix 'introduced_in' version in typescript module (1ilsang) [#57109](https://github.com/nodejs/node/pull/57109)
- \[[`6cc15b8dc9`](https://github.com/nodejs/node/commit/6cc15b8dc9)] - **doc**: fix link and history of `SourceMap` sections (Antoine du Hamel) [#57098](https://github.com/nodejs/node/pull/57098)
- \[[`6be8189041`](https://github.com/nodejs/node/commit/6be8189041)] - **doc**: add `module namespace object` links (Dario Piotrowicz) [#57093](https://github.com/nodejs/node/pull/57093)
- \[[`8611c4a3ea`](https://github.com/nodejs/node/commit/8611c4a3ea)] - **doc**: disambiguate pseudo-code statement (Dario Piotrowicz) [#57092](https://github.com/nodejs/node/pull/57092)
- \[[`79da145a55`](https://github.com/nodejs/node/commit/79da145a55)] - **doc**: update clang-cl on Windows building guide (Joyee Cheung) [#57087](https://github.com/nodejs/node/pull/57087)
- \[[`845eaf91be`](https://github.com/nodejs/node/commit/845eaf91be)] - **doc**: fix wrong articles used to address modules (Dario Piotrowicz) [#57090](https://github.com/nodejs/node/pull/57090)
- \[[`42c5e23eb1`](https://github.com/nodejs/node/commit/42c5e23eb1)] - **doc**: `modules.md`: fix `distance` definition (Alexander “weej” Jones) [#57046](https://github.com/nodejs/node/pull/57046)
- \[[`bda851aaa3`](https://github.com/nodejs/node/commit/bda851aaa3)] - **doc**: fix wrong verb form (Dario Piotrowicz) [#57091](https://github.com/nodejs/node/pull/57091)
- \[[`64e13fd36e`](https://github.com/nodejs/node/commit/64e13fd36e)] - **doc**: fix transpiler loader hooks documentation (Joyee Cheung) [#57037](https://github.com/nodejs/node/pull/57037)
- \[[`51494d8b78`](https://github.com/nodejs/node/commit/51494d8b78)] - **doc**: add a note about `require('../common')` in testing documentation (Aditi) [#56953](https://github.com/nodejs/node/pull/56953)
- \[[`053b128e9c`](https://github.com/nodejs/node/commit/053b128e9c)] - **doc**: recommend writing tests in new files and including comments (Joyee Cheung) [#57028](https://github.com/nodejs/node/pull/57028)
- \[[`a20c62a00c`](https://github.com/nodejs/node/commit/a20c62a00c)] - **doc**: improve documentation on argument validation (Aditi) [#56954](https://github.com/nodejs/node/pull/56954)
- \[[`2921658813`](https://github.com/nodejs/node/commit/2921658813)] - **doc**: buffer: fix typo on `Buffer.copyBytesFrom(` `offset` option (tpoisseau) [#57015](https://github.com/nodejs/node/pull/57015)
- \[[`6f4ab1c9b2`](https://github.com/nodejs/node/commit/6f4ab1c9b2)] - **doc**: update cleanup to trust on vuln db automation (Rafael Gonzaga) [#57004](https://github.com/nodejs/node/pull/57004)
- \[[`5285facb3e`](https://github.com/nodejs/node/commit/5285facb3e)] - **doc**: move stability index after history section for consistency (Antoine du Hamel) [#56997](https://github.com/nodejs/node/pull/56997)
- \[[`a7646e17ff`](https://github.com/nodejs/node/commit/a7646e17ff)] - **doc**: add `signal` to `filehandle.writeFile()` options (Yukihiro Hasegawa) [#56804](https://github.com/nodejs/node/pull/56804)
- \[[`ba031089e6`](https://github.com/nodejs/node/commit/ba031089e6)] - **doc**: run license-builder (github-actions\[bot]) [#56985](https://github.com/nodejs/node/pull/56985)
- \[[`afa6f93a32`](https://github.com/nodejs/node/commit/afa6f93a32)] - **doc**: update history of stream.Readable.toWeb() (Jimmy Leung) [#56928](https://github.com/nodejs/node/pull/56928)
- \[[`cc644de126`](https://github.com/nodejs/node/commit/cc644de126)] - **doc**: make MDN links to global classes more consistent (Antoine du Hamel) [#56924](https://github.com/nodejs/node/pull/56924)
- \[[`93bba4eee1`](https://github.com/nodejs/node/commit/93bba4eee1)] - **doc**: make MDN links to global classes more consistent in `assert.md` (Antoine du Hamel) [#56920](https://github.com/nodejs/node/pull/56920)
- \[[`ad03c85f98`](https://github.com/nodejs/node/commit/ad03c85f98)] - **doc**: make MDN links to global classes more consistent (Antoine du Hamel) [#56923](https://github.com/nodejs/node/pull/56923)
- \[[`96c2a90dee`](https://github.com/nodejs/node/commit/96c2a90dee)] - **doc**: make MDN links to global classes more consistent in `util.md` (Antoine du Hamel) [#56922](https://github.com/nodejs/node/pull/56922)
- \[[`6bb73c0745`](https://github.com/nodejs/node/commit/6bb73c0745)] - **doc**: make MDN links to global classes more consistent in `buffer.md` (Antoine du Hamel) [#56921](https://github.com/nodejs/node/pull/56921)
- \[[`824cf35475`](https://github.com/nodejs/node/commit/824cf35475)] - **doc**: update post sec release process (Rafael Gonzaga) [#56907](https://github.com/nodejs/node/pull/56907)
- \[[`027749eb17`](https://github.com/nodejs/node/commit/027749eb17)] - **doc**: update websocket link to avoid linking to self (Chengzhong Wu) [#56897](https://github.com/nodejs/node/pull/56897)
- \[[`5dcb9d632b`](https://github.com/nodejs/node/commit/5dcb9d632b)] - **doc**: mark `--env-file-if-exists` flag as experimental (Juan José) [#56893](https://github.com/nodejs/node/pull/56893)
- \[[`4f6d751bf5`](https://github.com/nodejs/node/commit/4f6d751bf5)] - **doc**: fix typo in cjs example of `util.styleText` (Deokjin Kim) [#56769](https://github.com/nodejs/node/pull/56769)
- \[[`313d9db7a5`](https://github.com/nodejs/node/commit/313d9db7a5)] - **doc**: clarify sqlite user-defined function behaviour (René) [#56786](https://github.com/nodejs/node/pull/56786)
- \[[`eff42956c4`](https://github.com/nodejs/node/commit/eff42956c4)] - **doc**: correct customization hook types & clarify descriptions (Jacob Smith) [#56454](https://github.com/nodejs/node/pull/56454)
- \[[`64180421c2`](https://github.com/nodejs/node/commit/64180421c2)] - **events**: getMaxListeners detects 0 listeners (Matthew Aitken) [#56807](https://github.com/nodejs/node/pull/56807)
- \[[`2de27787b4`](https://github.com/nodejs/node/commit/2de27787b4)] - **fs**: apply exclude function to root path (Rich Trott) [#57420](https://github.com/nodejs/node/pull/57420)
- \[[`b6df9e350a`](https://github.com/nodejs/node/commit/b6df9e350a)] - **fs**: handle UV_ENOTDIR in `fs.statSync` with `throwIfNoEntry` provided (Juan José Arboleda) [#56996](https://github.com/nodejs/node/pull/56996)
- \[[`14b2d496a0`](https://github.com/nodejs/node/commit/14b2d496a0)] - **fs**: make `FileHandle.readableWebStream` always create byte streams (Ian Kerins) [#55461](https://github.com/nodejs/node/pull/55461)
- \[[`10d2f1d898`](https://github.com/nodejs/node/commit/10d2f1d898)] - **http**: coerce content-length to number (Marco Ippolito) [#57458](https://github.com/nodejs/node/pull/57458)
- \[[`9192b7fa25`](https://github.com/nodejs/node/commit/9192b7fa25)] - **http**: be more generational GC friendly (ywave620) [#56767](https://github.com/nodejs/node/pull/56767)
- \[[`1cf98a8788`](https://github.com/nodejs/node/commit/1cf98a8788)] - **inspector**: convert event params to protocol without json (Chengzhong Wu) [#57027](https://github.com/nodejs/node/pull/57027)
- \[[`6dcad868bb`](https://github.com/nodejs/node/commit/6dcad868bb)] - **inspector**: skip promise hook in the inspector async hook (Joyee Cheung) [#57148](https://github.com/nodejs/node/pull/57148)
- \[[`787e93f75a`](https://github.com/nodejs/node/commit/787e93f75a)] - **inspector**: add Network.Initiator in inspector protocol (Chengzhong Wu) [#56805](https://github.com/nodejs/node/pull/56805)
- \[[`c7c04d0dc8`](https://github.com/nodejs/node/commit/c7c04d0dc8)] - **inspector**: fix GN build (Cheng) [#56798](https://github.com/nodejs/node/pull/56798)
- \[[`177da9c3c3`](https://github.com/nodejs/node/commit/177da9c3c3)] - **inspector**: fix StringUtil::CharacterCount for unicodes (Chengzhong Wu) [#56788](https://github.com/nodejs/node/pull/56788)
- \[[`1b5418eeea`](https://github.com/nodejs/node/commit/1b5418eeea)] - **lib**: add warning when binding inspector to public IP (Demian Parkhomenko) [#55736](https://github.com/nodejs/node/pull/55736)
- \[[`cc4d33842b`](https://github.com/nodejs/node/commit/cc4d33842b)] - **lib**: limit split function calls to prevent excessive array length (Gürgün Dayıoğlu) [#57501](https://github.com/nodejs/node/pull/57501)
- \[[`0546612d1d`](https://github.com/nodejs/node/commit/0546612d1d)] - **lib**: make getCallSites sourceMap option truly optional (James M Snell) [#57388](https://github.com/nodejs/node/pull/57388)
- \[[`d7d54e6bf3`](https://github.com/nodejs/node/commit/d7d54e6bf3)] - **lib**: optimize priority queue (Gürgün Dayıoğlu) [#57100](https://github.com/nodejs/node/pull/57100)
- \[[`62761c73a1`](https://github.com/nodejs/node/commit/62761c73a1)] - **lib**: fixup incorrect argument order in assertEncoding (James M Snell) [#57177](https://github.com/nodejs/node/pull/57177)
- \[[`5dce55c376`](https://github.com/nodejs/node/commit/5dce55c376)] - **meta**: add some clarification to the nomination process (James M Snell) [#57503](https://github.com/nodejs/node/pull/57503)
- \[[`a2a4cf1d95`](https://github.com/nodejs/node/commit/a2a4cf1d95)] - **meta**: remove collaborator self-nomination (Rich Trott) [#57537](https://github.com/nodejs/node/pull/57537)
- \[[`244f74b844`](https://github.com/nodejs/node/commit/244f74b844)] - **meta**: edit collaborator nomination process (Antoine du Hamel) [#57483](https://github.com/nodejs/node/pull/57483)
- \[[`dec204bb3f`](https://github.com/nodejs/node/commit/dec204bb3f)] - **meta**: move ovflowd to emeritus (Claudio W.) [#57443](https://github.com/nodejs/node/pull/57443)
- \[[`c0b8b84384`](https://github.com/nodejs/node/commit/c0b8b84384)] - **meta**: bump codecov/codecov-action from 5.3.1 to 5.4.0 (dependabot\[bot]) [#57257](https://github.com/nodejs/node/pull/57257)
- \[[`14cbe292da`](https://github.com/nodejs/node/commit/14cbe292da)] - **meta**: bump github/codeql-action from 3.28.8 to 3.28.10 (dependabot\[bot]) [#57254](https://github.com/nodejs/node/pull/57254)
- \[[`69d2dd69e2`](https://github.com/nodejs/node/commit/69d2dd69e2)] - **meta**: bump ossf/scorecard-action from 2.4.0 to 2.4.1 (dependabot\[bot]) [#57253](https://github.com/nodejs/node/pull/57253)
- \[[`5f3428ded6`](https://github.com/nodejs/node/commit/5f3428ded6)] - **meta**: move RaisinTen back to collaborators, triagers and SEA champion (Darshan Sen) [#57292](https://github.com/nodejs/node/pull/57292)
- \[[`3eea8c72fc`](https://github.com/nodejs/node/commit/3eea8c72fc)] - **meta**: bump actions/download-artifact from 4.1.8 to 4.1.9 (dependabot\[bot]) [#57260](https://github.com/nodejs/node/pull/57260)
- \[[`2508893edb`](https://github.com/nodejs/node/commit/2508893edb)] - **meta**: bump peter-evans/create-pull-request from 7.0.6 to 7.0.7 (dependabot\[bot]) [#57259](https://github.com/nodejs/node/pull/57259)
- \[[`fc09523f44`](https://github.com/nodejs/node/commit/fc09523f44)] - **meta**: bump step-security/harden-runner from 2.10.4 to 2.11.0 (dependabot\[bot]) [#57258](https://github.com/nodejs/node/pull/57258)
- \[[`b162402440`](https://github.com/nodejs/node/commit/b162402440)] - **meta**: bump actions/cache from 4.2.0 to 4.2.2 (dependabot\[bot]) [#57256](https://github.com/nodejs/node/pull/57256)
- \[[`f781be1332`](https://github.com/nodejs/node/commit/f781be1332)] - **meta**: bump actions/upload-artifact from 4.6.0 to 4.6.1 (dependabot\[bot]) [#57255](https://github.com/nodejs/node/pull/57255)
- \[[`7934ad9fc0`](https://github.com/nodejs/node/commit/7934ad9fc0)] - **meta**: bump `actions/setup-python` from 5.3.0 to 5.4.0 (dependabot\[bot]) [#56867](https://github.com/nodejs/node/pull/56867)
- \[[`eb4fb9ce90`](https://github.com/nodejs/node/commit/eb4fb9ce90)] - **meta**: bump `peter-evans/create-pull-request` from 7.0.5 to 7.0.6 (dependabot\[bot]) [#56866](https://github.com/nodejs/node/pull/56866)
- \[[`a14e7f1cc4`](https://github.com/nodejs/node/commit/a14e7f1cc4)] - **meta**: bump `mozilla-actions/sccache-action` from 0.0.6 to 0.0.7 (dependabot\[bot]) [#56865](https://github.com/nodejs/node/pull/56865)
- \[[`6c8a9e3d0d`](https://github.com/nodejs/node/commit/6c8a9e3d0d)] - **meta**: bump `codecov/codecov-action` from 5.0.7 to 5.3.1 (dependabot\[bot]) [#56864](https://github.com/nodejs/node/pull/56864)
- \[[`f438c27cbf`](https://github.com/nodejs/node/commit/f438c27cbf)] - **meta**: bump `step-security/harden-runner` from 2.10.2 to 2.10.4 (dependabot\[bot]) [#56863](https://github.com/nodejs/node/pull/56863)
- \[[`24b7fcb153`](https://github.com/nodejs/node/commit/24b7fcb153)] - **meta**: bump `actions/cache` from 4.1.2 to 4.2.0 (dependabot\[bot]) [#56862](https://github.com/nodejs/node/pull/56862)
- \[[`a0afc47988`](https://github.com/nodejs/node/commit/a0afc47988)] - **meta**: bump `actions/stale` from 9.0.0 to 9.1.0 (dependabot\[bot]) [#56860](https://github.com/nodejs/node/pull/56860)
- \[[`8abf4e5d7d`](https://github.com/nodejs/node/commit/8abf4e5d7d)] - **meta**: bump `github/codeql-action` from 3.27.5 to 3.28.8 (dependabot\[bot]) [#56859](https://github.com/nodejs/node/pull/56859)
- \[[`c5bff736e9`](https://github.com/nodejs/node/commit/c5bff736e9)] - **meta**: add CODEOWNERS for SQLite (Colin Ihrig) [#57147](https://github.com/nodejs/node/pull/57147)
- \[[`fd2abaa088`](https://github.com/nodejs/node/commit/fd2abaa088)] - **meta**: update last name for jkrems (Jan Martin) [#57006](https://github.com/nodejs/node/pull/57006)
- \[[`2383f00aae`](https://github.com/nodejs/node/commit/2383f00aae)] - **meta**: bump `actions/upload-artifact` from 4.4.3 to 4.6.0 (dependabot\[bot]) [#56861](https://github.com/nodejs/node/pull/56861)
- \[[`35b3140d03`](https://github.com/nodejs/node/commit/35b3140d03)] - **meta**: bump `actions/setup-node` from 4.1.0 to 4.2.0 (dependabot\[bot]) [#56868](https://github.com/nodejs/node/pull/56868)
- \[[`815fcef73d`](https://github.com/nodejs/node/commit/815fcef73d)] - **meta**: move one or more collaborators to emeritus (Node.js GitHub Bot) [#56889](https://github.com/nodejs/node/pull/56889)
- \[[`08001127a2`](https://github.com/nodejs/node/commit/08001127a2)] - **meta**: add @nodejs/url as codeowner (Chengzhong Wu) [#56783](https://github.com/nodejs/node/pull/56783)
- \[[`3ceda2a035`](https://github.com/nodejs/node/commit/3ceda2a035)] - **module**: handle cached linked async jobs in require(esm) (Joyee Cheung) [#57187](https://github.com/nodejs/node/pull/57187)
- \[[`4c29cc7e6b`](https://github.com/nodejs/node/commit/4c29cc7e6b)] - **module**: add dynamic file-specific ESM warnings (Mert Can Altin) [#56628](https://github.com/nodejs/node/pull/56628)
- \[[`d1845edd21`](https://github.com/nodejs/node/commit/d1845edd21)] - **module**: improve error message from asynchronicity in require(esm) (Joyee Cheung) [#57126](https://github.com/nodejs/node/pull/57126)
- \[[`41fa7d3c21`](https://github.com/nodejs/node/commit/41fa7d3c21)] - **module**: allow omitting context in synchronous next hooks (Joyee Cheung) [#57056](https://github.com/nodejs/node/pull/57056)
- \[[`deddecce3a`](https://github.com/nodejs/node/commit/deddecce3a)] - **module**: fix require.resolve() crash on non-string paths (Aditi) [#56942](https://github.com/nodejs/node/pull/56942)
- \[[`926b887534`](https://github.com/nodejs/node/commit/926b887534)] - **module**: fixing url change in load sync hook chain (Vitalii Akimov) [#56402](https://github.com/nodejs/node/pull/56402)
- \[[`6b93ba723b`](https://github.com/nodejs/node/commit/6b93ba723b)] - **(SEMVER-MINOR)** **module**: use synchronous hooks for preparsing in import(cjs) (Joyee Cheung) [#55698](https://github.com/nodejs/node/pull/55698)
- \[[`b2e44a8079`](https://github.com/nodejs/node/commit/b2e44a8079)] - **(SEMVER-MINOR)** **module**: implement module.registerHooks() (Joyee Cheung) [#55698](https://github.com/nodejs/node/pull/55698)
- \[[`e79e67f6dc`](https://github.com/nodejs/node/commit/e79e67f6dc)] - **net**: validate non-string host for socket.connect (Daeyeon Jeong) [#57198](https://github.com/nodejs/node/pull/57198)
- \[[`e23056212e`](https://github.com/nodejs/node/commit/e23056212e)] - **net**: replace brand checks with identity checks (Yagiz Nizipli) [#57341](https://github.com/nodejs/node/pull/57341)
- \[[`9c0d5e140b`](https://github.com/nodejs/node/commit/9c0d5e140b)] - **net**: emit an error when custom lookup resolves to a non-string address (Edy Silva) [#57192](https://github.com/nodejs/node/pull/57192)
- \[[`2ce79787de`](https://github.com/nodejs/node/commit/2ce79787de)] - **(SEMVER-MINOR)** **process**: add execve (Paolo Insogna) [#56496](https://github.com/nodejs/node/pull/56496)
- \[[`712db2232c`](https://github.com/nodejs/node/commit/712db2232c)] - **readline**: add support for `Symbol.dispose` (Antoine du Hamel) [#57276](https://github.com/nodejs/node/pull/57276)
- \[[`55fb81c0f1`](https://github.com/nodejs/node/commit/55fb81c0f1)] - **readline**: fix unresolved promise on abortion (Daniel Venable) [#54030](https://github.com/nodejs/node/pull/54030)
- \[[`dfcd9b1ac2`](https://github.com/nodejs/node/commit/dfcd9b1ac2)] - **sea**: suppress builtin warning with disableExperimentalSEAWarning option (koooge) [#57086](https://github.com/nodejs/node/pull/57086)
- \[[`bd5c90654a`](https://github.com/nodejs/node/commit/bd5c90654a)] - **sqlite**: add support for unknown named parameters (Colin Ihrig) [#57552](https://github.com/nodejs/node/pull/57552)
- \[[`ec571382a4`](https://github.com/nodejs/node/commit/ec571382a4)] - **sqlite**: add DatabaseSync.prototype.isOpen (Colin Ihrig) [#57522](https://github.com/nodejs/node/pull/57522)
- \[[`bb3bbed126`](https://github.com/nodejs/node/commit/bb3bbed126)] - **sqlite**: add DatabaseSync.prototype\[Symbol.dispose]\() (Colin Ihrig) [#57506](https://github.com/nodejs/node/pull/57506)
- \[[`6067bea027`](https://github.com/nodejs/node/commit/6067bea027)] - **sqlite**: restore changes from #55373 (Colin Ihrig) [#56908](https://github.com/nodejs/node/pull/56908)
- \[[`bc672fcfdd`](https://github.com/nodejs/node/commit/bc672fcfdd)] - **(SEMVER-MINOR)** **sqlite**: allow returning `ArrayBufferView`s from user-defined functions (René) [#56790](https://github.com/nodejs/node/pull/56790)
- \[[`227603dc30`](https://github.com/nodejs/node/commit/227603dc30)] - **sqlite,test,doc**: allow Buffer and URL as database location (Edy Silva) [#56991](https://github.com/nodejs/node/pull/56991)
- \[[`9dd324467a`](https://github.com/nodejs/node/commit/9dd324467a)] - **src**: cleanup aliased_buffer.h (Mohammed Keyvanzadeh) [#57395](https://github.com/nodejs/node/pull/57395)
- \[[`45a2b8532b`](https://github.com/nodejs/node/commit/45a2b8532b)] - **src**: do not pass nullptr to std::string ctor (Charles Kerr) [#57354](https://github.com/nodejs/node/pull/57354)
- \[[`854370a06c`](https://github.com/nodejs/node/commit/854370a06c)] - **src**: fix process exit listeners not receiving unsettled tla codes (Dario Piotrowicz) [#56872](https://github.com/nodejs/node/pull/56872)
- \[[`f7fb259193`](https://github.com/nodejs/node/commit/f7fb259193)] - **src**: refactor SubtleCrypto algorithm and length validations (Filip Skokan) [#57319](https://github.com/nodejs/node/pull/57319)
- \[[`c7bcc2d6c8`](https://github.com/nodejs/node/commit/c7bcc2d6c8)] - **src**: allow embedder customization of OOMErrorHandler (Shelley Vohr) [#57325](https://github.com/nodejs/node/pull/57325)
- \[[`fbd8862156`](https://github.com/nodejs/node/commit/fbd8862156)] - **src**: use Maybe\<void> in ProcessEmitWarningSync (Daeyeon Jeong) [#57250](https://github.com/nodejs/node/pull/57250)
- \[[`04de550289`](https://github.com/nodejs/node/commit/04de550289)] - **src**: make even more improvements to error handling (James M Snell) [#57264](https://github.com/nodejs/node/pull/57264)
- \[[`f1c5e46f89`](https://github.com/nodejs/node/commit/f1c5e46f89)] - **src**: use cached `emit` v8::String (Daeyeon Jeong) [#57249](https://github.com/nodejs/node/pull/57249)
- \[[`65b8e12689`](https://github.com/nodejs/node/commit/65b8e12689)] - **src**: refactor SubtleCrypto algorithm and length validations (Filip Skokan) [#57273](https://github.com/nodejs/node/pull/57273)
- \[[`b6091a8b21`](https://github.com/nodejs/node/commit/b6091a8b21)] - **src**: make more error handling improvements (James M Snell) [#57262](https://github.com/nodejs/node/pull/57262)
- \[[`3bd8a6c76e`](https://github.com/nodejs/node/commit/3bd8a6c76e)] - **src**: fix typo in comment (Antoine du Hamel) [#57291](https://github.com/nodejs/node/pull/57291)
- \[[`f7e39385ae`](https://github.com/nodejs/node/commit/f7e39385ae)] - **src**: improve error handling in `node_messaging.cc` (James M Snell) [#57211](https://github.com/nodejs/node/pull/57211)
- \[[`1bb561bede`](https://github.com/nodejs/node/commit/1bb561bede)] - **src**: improve error handling in `tty_wrap.cc` (James M Snell) [#57211](https://github.com/nodejs/node/pull/57211)
- \[[`567d321a40`](https://github.com/nodejs/node/commit/567d321a40)] - **src**: improve error handling in `tcp_wrap.cc` (James M Snell) [#57211](https://github.com/nodejs/node/pull/57211)
- \[[`f8bee871f7`](https://github.com/nodejs/node/commit/f8bee871f7)] - **src**: fix ThrowInvalidURL call in PathToFileURL (Daniel M Brasil) [#57141](https://github.com/nodejs/node/pull/57141)
- \[[`817f7d0e2e`](https://github.com/nodejs/node/commit/817f7d0e2e)] - **src**: improve error handling in buffer and dotenv (James M Snell) [#57189](https://github.com/nodejs/node/pull/57189)
- \[[`11ef7f9d9c`](https://github.com/nodejs/node/commit/11ef7f9d9c)] - **src**: improve error handling in module_wrap (James M Snell) [#57188](https://github.com/nodejs/node/pull/57188)
- \[[`3b08d718b1`](https://github.com/nodejs/node/commit/3b08d718b1)] - **src**: improve error handling in spawn_sync (James M Snell) [#57185](https://github.com/nodejs/node/pull/57185)
- \[[`9221c2ad87`](https://github.com/nodejs/node/commit/9221c2ad87)] - **src**: detect whether the string is one byte representation or not (theweipeng) [#56147](https://github.com/nodejs/node/pull/56147)
- \[[`e323694772`](https://github.com/nodejs/node/commit/e323694772)] - **src**: fix crash when lazy getter is invoked in a vm context (Chengzhong Wu) [#57168](https://github.com/nodejs/node/pull/57168)
- \[[`9363b05a91`](https://github.com/nodejs/node/commit/9363b05a91)] - **src**: do not format single string argument for THROW_ERR\_\* (Joyee Cheung) [#57126](https://github.com/nodejs/node/pull/57126)
- \[[`5d6a1bc35b`](https://github.com/nodejs/node/commit/5d6a1bc35b)] - **src**: move instead of copy shared pointer in node_blob (Michaël Zasso) [#57120](https://github.com/nodejs/node/pull/57120)
- \[[`5dab48fd9f`](https://github.com/nodejs/node/commit/5dab48fd9f)] - **src**: replace NewFromUtf8 with OneByteString where appropriate (James M Snell) [#57096](https://github.com/nodejs/node/pull/57096)
- \[[`0fe60b478d`](https://github.com/nodejs/node/commit/0fe60b478d)] - **src**: port `defineLazyProperties` to native code (Antoine du Hamel) [#57081](https://github.com/nodejs/node/pull/57081)
- \[[`792959db1d`](https://github.com/nodejs/node/commit/792959db1d)] - **src**: improve error handling in node_blob (James M Snell) [#57078](https://github.com/nodejs/node/pull/57078)
- \[[`e05e2cfb1e`](https://github.com/nodejs/node/commit/e05e2cfb1e)] - **src**: fix accessing empty string (Cheng) [#57014](https://github.com/nodejs/node/pull/57014)
- \[[`619e52ce8d`](https://github.com/nodejs/node/commit/619e52ce8d)] - **src**: lock the isolate properly in IsolateData destructor (Joyee Cheung) [#57031](https://github.com/nodejs/node/pull/57031)
- \[[`844a4a884d`](https://github.com/nodejs/node/commit/844a4a884d)] - **src**: add self-assigment memcpy checks (Burkov Egor) [#56986](https://github.com/nodejs/node/pull/56986)
- \[[`0d1e79740f`](https://github.com/nodejs/node/commit/0d1e79740f)] - **src**: improve node::Dotenv trimming (Dario Piotrowicz) [#56983](https://github.com/nodejs/node/pull/56983)
- \[[`50f164e23b`](https://github.com/nodejs/node/commit/50f164e23b)] - **src**: improve error handling in string_bytes/decoder (James M Snell) [#56978](https://github.com/nodejs/node/pull/56978)
- \[[`93aa4393a4`](https://github.com/nodejs/node/commit/93aa4393a4)] - **src**: improve error handling in process_wrap (James M Snell) [#56977](https://github.com/nodejs/node/pull/56977)
- \[[`c1c824e38d`](https://github.com/nodejs/node/commit/c1c824e38d)] - **src**: use `args.This()` in zlib (Michaël Zasso) [#56988](https://github.com/nodejs/node/pull/56988)
- \[[`0a8e474bdc`](https://github.com/nodejs/node/commit/0a8e474bdc)] - **src**: add nullptr handling for `NativeKeyObject` (Burkov Egor) [#56900](https://github.com/nodejs/node/pull/56900)
- \[[`1ea6198a5a`](https://github.com/nodejs/node/commit/1ea6198a5a)] - **src**: disallow copy/move fns/constructors (Yagiz Nizipli) [#56811](https://github.com/nodejs/node/pull/56811)
- \[[`e4100853cb`](https://github.com/nodejs/node/commit/e4100853cb)] - **src**: add a hard dependency v8_inspector_headers (Chengzhong Wu) [#56805](https://github.com/nodejs/node/pull/56805)
- \[[`a1f92898c0`](https://github.com/nodejs/node/commit/a1f92898c0)] - **src**: improve error handling in encoding_binding.cc (James M Snell) [#56915](https://github.com/nodejs/node/pull/56915)
- \[[`dee8793d94`](https://github.com/nodejs/node/commit/dee8793d94)] - **src**: improve error handling in permission.cc (James M Snell) [#56904](https://github.com/nodejs/node/pull/56904)
- \[[`f41bc4cfd7`](https://github.com/nodejs/node/commit/f41bc4cfd7)] - **src**: improve error handling in node_sqlite (James M Snell) [#56891](https://github.com/nodejs/node/pull/56891)
- \[[`e4df6181bf`](https://github.com/nodejs/node/commit/e4df6181bf)] - **src**: improve error handling in node_os by removing ToLocalChecked (James M Snell) [#56888](https://github.com/nodejs/node/pull/56888)
- \[[`2c96e7a32c`](https://github.com/nodejs/node/commit/2c96e7a32c)] - **src**: improve error handling in node_url (James M Snell) [#56886](https://github.com/nodejs/node/pull/56886)
- \[[`36926ae8d8`](https://github.com/nodejs/node/commit/36926ae8d8)] - **src**: add check for Bignum in GroupOrderSize (Burkov Egor) [#56702](https://github.com/nodejs/node/pull/56702)
- \[[`a68f127a30`](https://github.com/nodejs/node/commit/a68f127a30)] - **src**: reduce string allocations on sqlite (Yagiz Nizipli) [#57227](https://github.com/nodejs/node/pull/57227)
- \[[`e41b1735f1`](https://github.com/nodejs/node/commit/e41b1735f1)] - **stream**: fix sizeAlgorithm validation in WritableStream (Daeyeon Jeong) [#57280](https://github.com/nodejs/node/pull/57280)
- \[[`3bc877dc5c`](https://github.com/nodejs/node/commit/3bc877dc5c)] - **test**: add more number cases for buffer.indexOf (Meghan Denny) [#57200](https://github.com/nodejs/node/pull/57200)
- \[[`cac9a4e832`](https://github.com/nodejs/node/commit/cac9a4e832)] - **test**: update parallel/test-tls-dhe for OpenSSL 3.5 (Richard Lau) [#57477](https://github.com/nodejs/node/pull/57477)
- \[[`3082ab3a64`](https://github.com/nodejs/node/commit/3082ab3a64)] - **test**: module syntax should throw (Marco Ippolito) [#57121](https://github.com/nodejs/node/pull/57121)
- \[[`9b0dfc9a44`](https://github.com/nodejs/node/commit/9b0dfc9a44)] - **test**: update snapshots for amaro v0.3.2 (Marco Ippolito) [#56916](https://github.com/nodejs/node/pull/56916)
- \[[`2defc35ea8`](https://github.com/nodejs/node/commit/2defc35ea8)] - **test**: test runner run plan (Pietro Marchini) [#57304](https://github.com/nodejs/node/pull/57304)
- \[[`ccb3df70be`](https://github.com/nodejs/node/commit/ccb3df70be)] - **test**: update WPT for WebCryptoAPI to edd42c005c (Node.js GitHub Bot) [#57365](https://github.com/nodejs/node/pull/57365)
- \[[`528103c5d0`](https://github.com/nodejs/node/commit/528103c5d0)] - **test**: simplify test-tls-connect-abort-controller.js (Yagiz Nizipli) [#57338](https://github.com/nodejs/node/pull/57338)
- \[[`17e21e6eb5`](https://github.com/nodejs/node/commit/17e21e6eb5)] - **test**: use `assert.match` in `test-esm-import-meta` (Antoine du Hamel) [#57290](https://github.com/nodejs/node/pull/57290)
- \[[`77bbee5184`](https://github.com/nodejs/node/commit/77bbee5184)] - **test**: update compression wpt (Yagiz Nizipli) [#56960](https://github.com/nodejs/node/pull/56960)
- \[[`4fe88f8f53`](https://github.com/nodejs/node/commit/4fe88f8f53)] - _**Revert**_ "**test**: temporary remove resource check from fs read-write" (Rafael Gonzaga) [#56906](https://github.com/nodejs/node/pull/56906)
- \[[`766efc7758`](https://github.com/nodejs/node/commit/766efc7758)] - **test**: more common.mustNotCall in net, tls (Meghan Denny) [#57246](https://github.com/nodejs/node/pull/57246)
- \[[`562e635e11`](https://github.com/nodejs/node/commit/562e635e11)] - **test**: swap assert.strictEqual() parameters (Luigi Pinca) [#57217](https://github.com/nodejs/node/pull/57217)
- \[[`64fdfd5622`](https://github.com/nodejs/node/commit/64fdfd5622)] - **test**: assert write return values in buffer-bigint64 (Meghan Denny) [#57212](https://github.com/nodejs/node/pull/57212)
- \[[`dd538e7cf1`](https://github.com/nodejs/node/commit/dd538e7cf1)] - **test**: allow embedder running async context frame test (Shelley Vohr) [#57193](https://github.com/nodejs/node/pull/57193)
- \[[`937bbeb2b6`](https://github.com/nodejs/node/commit/937bbeb2b6)] - **test**: resolve race condition in test-net-write-fully-async-\* (Matteo Collina) [#57022](https://github.com/nodejs/node/pull/57022)
- \[[`32df9f27d8`](https://github.com/nodejs/node/commit/32df9f27d8)] - **test**: add doAppendAndCancel test (Hasegawa-Yukihiro) [#56972](https://github.com/nodejs/node/pull/56972)
- \[[`90c98df258`](https://github.com/nodejs/node/commit/90c98df258)] - **test**: fix test-without-async-context-frame.mjs in debug mode (Joyee Cheung) [#57034](https://github.com/nodejs/node/pull/57034)
- \[[`974817c9fc`](https://github.com/nodejs/node/commit/974817c9fc)] - **test**: make eval snapshot comparison more flexible (Shelley Vohr) [#57020](https://github.com/nodejs/node/pull/57020)
- \[[`09741cd129`](https://github.com/nodejs/node/commit/09741cd129)] - **test**: simplify test-http2-client-promisify-connect-error (Luigi Pinca) [#57144](https://github.com/nodejs/node/pull/57144)
- \[[`89f3feb364`](https://github.com/nodejs/node/commit/89f3feb364)] - **test**: improve error output of test-http2-client-promisify-connect-error (Antoine du Hamel) [#57135](https://github.com/nodejs/node/pull/57135)
- \[[`25751eba4d`](https://github.com/nodejs/node/commit/25751eba4d)] - **test**: add case for unrecognised fields within pjson "exports" (Jacob Smith) [#57026](https://github.com/nodejs/node/pull/57026)
- \[[`bf0b9fa7c0`](https://github.com/nodejs/node/commit/bf0b9fa7c0)] - **test**: remove unnecessary assert requiring from tests (Dario Piotrowicz) [#57008](https://github.com/nodejs/node/pull/57008)
- \[[`8cfb2df466`](https://github.com/nodejs/node/commit/8cfb2df466)] - **test**: reduce flakiness on test-net-write-fully-async-buffer (Yagiz Nizipli) [#56971](https://github.com/nodejs/node/pull/56971)
- \[[`43c8c101da`](https://github.com/nodejs/node/commit/43c8c101da)] - **test**: remove flakiness on macOS test (Yagiz Nizipli) [#56971](https://github.com/nodejs/node/pull/56971)
- \[[`bd47178f7f`](https://github.com/nodejs/node/commit/bd47178f7f)] - **test**: improve timeout duration for debugger events (Yagiz Nizipli) [#56970](https://github.com/nodejs/node/pull/56970)
- \[[`65694aa2fd`](https://github.com/nodejs/node/commit/65694aa2fd)] - **test**: remove unnecessary syscall to cpuinfo (Yagiz Nizipli) [#56968](https://github.com/nodejs/node/pull/56968)
- \[[`5633c4b2df`](https://github.com/nodejs/node/commit/5633c4b2df)] - **test**: update webstorage wpt (Yagiz Nizipli) [#56963](https://github.com/nodejs/node/pull/56963)
- \[[`2244a2776a`](https://github.com/nodejs/node/commit/2244a2776a)] - **test**: execute shell directly for refresh() (Yagiz Nizipli) [#55051](https://github.com/nodejs/node/pull/55051)
- \[[`afae4b1216`](https://github.com/nodejs/node/commit/afae4b1216)] - **test**: change jenkins reporter (Carlos Espa) [#56808](https://github.com/nodejs/node/pull/56808)
- \[[`b26592a7c4`](https://github.com/nodejs/node/commit/b26592a7c4)] - **test**: fix race condition in test-child-process-bad-stdio (Colin Ihrig) [#56845](https://github.com/nodejs/node/pull/56845)
- \[[`72c2279649`](https://github.com/nodejs/node/commit/72c2279649)] - **test**: adjust check to use OpenSSL sec level (Michael Dawson) [#56819](https://github.com/nodejs/node/pull/56819)
- \[[`9551b27651`](https://github.com/nodejs/node/commit/9551b27651)] - **test**: test-crypto-scrypt.js doesn't need internals (Meghan Denny) [#56673](https://github.com/nodejs/node/pull/56673)
- \[[`3095db84be`](https://github.com/nodejs/node/commit/3095db84be)] - **test**: set `test-fs-cp` as flaky (Stefan Stojanovic) [#56799](https://github.com/nodejs/node/pull/56799)
- \[[`31f98d7ccd`](https://github.com/nodejs/node/commit/31f98d7ccd)] - **test**: search cctest files (Chengzhong Wu) [#56791](https://github.com/nodejs/node/pull/56791)
- \[[`267f17d5f6`](https://github.com/nodejs/node/commit/267f17d5f6)] - **test**: convert test_encoding_binding.cc to a JS test (Chengzhong Wu) [#56791](https://github.com/nodejs/node/pull/56791)
- \[[`a875d7bdd1`](https://github.com/nodejs/node/commit/a875d7bdd1)] - **test**: test-crypto-prime.js doesn't need internals (Meghan Denny) [#56675](https://github.com/nodejs/node/pull/56675)
- \[[`85482d69c6`](https://github.com/nodejs/node/commit/85482d69c6)] - **test**: temporary remove resource check from fs read-write (Rafael Gonzaga) [#56789](https://github.com/nodejs/node/pull/56789)
- \[[`ec63d72f16`](https://github.com/nodejs/node/commit/ec63d72f16)] - **test**: mark test-without-async-context-frame flaky on windows (James M Snell) [#56753](https://github.com/nodejs/node/pull/56753)
- \[[`f16acc8521`](https://github.com/nodejs/node/commit/f16acc8521)] - **test**: remove unnecessary code (Luigi Pinca) [#56784](https://github.com/nodejs/node/pull/56784)
- \[[`0573c19a97`](https://github.com/nodejs/node/commit/0573c19a97)] - **test**: mark `test-esm-loader-hooks-inspect-wait` flaky (Richard Lau) [#56803](https://github.com/nodejs/node/pull/56803)
- \[[`48e0fd3f13`](https://github.com/nodejs/node/commit/48e0fd3f13)] - **test**: update WPT for url to a23788b77a (Node.js GitHub Bot) [#56779](https://github.com/nodejs/node/pull/56779)
- \[[`642959b87f`](https://github.com/nodejs/node/commit/642959b87f)] - **test**: remove duplicate error reporter from ci (Carlos Espa) [#56739](https://github.com/nodejs/node/pull/56739)
- \[[`2023237b4e`](https://github.com/nodejs/node/commit/2023237b4e)] - **test,crypto**: make tests work for BoringSSL (Shelley Vohr) [#57021](https://github.com/nodejs/node/pull/57021)
- \[[`1b33b976ec`](https://github.com/nodejs/node/commit/1b33b976ec)] - **test_runner**: refactor testPlan counter increse (Pietro Marchini) [#56765](https://github.com/nodejs/node/pull/56765)
- \[[`d860f2bf42`](https://github.com/nodejs/node/commit/d860f2bf42)] - **test_runner**: differentiate test types in enqueue dequeue events (Eddie Abbondanzio) [#54049](https://github.com/nodejs/node/pull/54049)
- \[[`993bab646c`](https://github.com/nodejs/node/commit/993bab646c)] - **test_runner**: print formatted errors on summary (Pietro Marchini) [#56911](https://github.com/nodejs/node/pull/56911)
- \[[`3ed3ba438f`](https://github.com/nodejs/node/commit/3ed3ba438f)] - **test_runner**: allow special characters in snapshot keys (Carlos Espa) [#57017](https://github.com/nodejs/node/pull/57017)
- \[[`d1da9a3a2f`](https://github.com/nodejs/node/commit/d1da9a3a2f)] - **timers**: optimize timer functions with improved argument handling (Gürgün Dayıoğlu) [#57072](https://github.com/nodejs/node/pull/57072)
- \[[`44aa13990a`](https://github.com/nodejs/node/commit/44aa13990a)] - **timers**: remove unnecessary allocation of \_onTimeout (Gürgün Dayıoğlu) [#57497](https://github.com/nodejs/node/pull/57497)
- \[[`401b965977`](https://github.com/nodejs/node/commit/401b965977)] - **timers**: remove unused parameter from insertGuarded (Gürgün Dayıoğlu) [#57251](https://github.com/nodejs/node/pull/57251)
- \[[`9eac9c02c9`](https://github.com/nodejs/node/commit/9eac9c02c9)] - **timers**: simplify the compareTimersLists function (Gürgün Dayıoğlu) [#57110](https://github.com/nodejs/node/pull/57110)
- \[[`01215af350`](https://github.com/nodejs/node/commit/01215af350)] - **tls**: remove unnecessary type check on normalize (Yagiz Nizipli) [#57336](https://github.com/nodejs/node/pull/57336)
- \[[`f5e2b12a60`](https://github.com/nodejs/node/commit/f5e2b12a60)] - **(SEMVER-MINOR)** **tls**: implement tls.getCACertificates() (Joyee Cheung) [#57107](https://github.com/nodejs/node/pull/57107)
- \[[`7a777cdb58`](https://github.com/nodejs/node/commit/7a777cdb58)] - **tools**: fix WPT update cron string (Antoine du Hamel) [#57665](https://github.com/nodejs/node/pull/57665)
- \[[`c6d90dbf9b`](https://github.com/nodejs/node/commit/c6d90dbf9b)] - **tools**: remove stalled label on unstalled issues and PRs (Rich Trott) [#57630](https://github.com/nodejs/node/pull/57630)
- \[[`96f7f64602`](https://github.com/nodejs/node/commit/96f7f64602)] - **tools**: update sccache to support GH cache changes (Michaël Zasso) [#57573](https://github.com/nodejs/node/pull/57573)
- \[[`0b87027520`](https://github.com/nodejs/node/commit/0b87027520)] - **tools**: bump @babel/helpers from 7.26.9 to 7.26.10 in /tools/eslint (dependabot\[bot]) [#57444](https://github.com/nodejs/node/pull/57444)
- \[[`7d561eb90c`](https://github.com/nodejs/node/commit/7d561eb90c)] - **tools**: add config subspace (Marco Ippolito) [#57239](https://github.com/nodejs/node/pull/57239)
- \[[`46efdbf59f`](https://github.com/nodejs/node/commit/46efdbf59f)] - **tools**: import rather than require ESLint plugins (Michaël Zasso) [#57315](https://github.com/nodejs/node/pull/57315)
- \[[`502bfaf876`](https://github.com/nodejs/node/commit/502bfaf876)] - **tools**: switch back to official OpenSSL (Richard Lau) [#57301](https://github.com/nodejs/node/pull/57301)
- \[[`ea821f419d`](https://github.com/nodejs/node/commit/ea821f419d)] - **tools**: revert to use @stylistic/eslint-plugin-js v3 (Joyee Cheung) [#57314](https://github.com/nodejs/node/pull/57314)
- \[[`bb857615d3`](https://github.com/nodejs/node/commit/bb857615d3)] - **tools**: add more details about rolling inspector_protocol (Chengzhong Wu) [#57167](https://github.com/nodejs/node/pull/57167)
- \[[`3f29d39c1b`](https://github.com/nodejs/node/commit/3f29d39c1b)] - **tools**: bump the eslint group in /tools/eslint with 5 updates (dependabot\[bot]) [#57261](https://github.com/nodejs/node/pull/57261)
- \[[`b3caac83d4`](https://github.com/nodejs/node/commit/b3caac83d4)] - **tools**: remove deps/zlib/GN-scraper.py (Chengzhong Wu) [#57238](https://github.com/nodejs/node/pull/57238)
- \[[`ace99ffe79`](https://github.com/nodejs/node/commit/ace99ffe79)] - **tools**: run Linux tests on GitHub arm64 runners as well (Dennis Ameling) [#57162](https://github.com/nodejs/node/pull/57162)
- \[[`e65e6269b7`](https://github.com/nodejs/node/commit/e65e6269b7)] - **tools**: consolidate 'introduced_in' check for docs (1ilsang) [#57109](https://github.com/nodejs/node/pull/57109)
- \[[`890841e64b`](https://github.com/nodejs/node/commit/890841e64b)] - **tools**: do not run major-release workflow on forks (Rich Trott) [#57064](https://github.com/nodejs/node/pull/57064)
- \[[`e3f86c5a0c`](https://github.com/nodejs/node/commit/e3f86c5a0c)] - **tools**: fix release URL computation in update-root-certs.mjs (Joyee Cheung) [#56843](https://github.com/nodejs/node/pull/56843)
- \[[`280316f773`](https://github.com/nodejs/node/commit/280316f773)] - **tools**: add support for `import source` syntax in linter (Antoine du Hamel) [#56992](https://github.com/nodejs/node/pull/56992)
- \[[`998b2ae3cd`](https://github.com/nodejs/node/commit/998b2ae3cd)] - **tools**: bump eslint version (dependabot\[bot]) [#56869](https://github.com/nodejs/node/pull/56869)
- \[[`ca4121b95a`](https://github.com/nodejs/node/commit/ca4121b95a)] - **tools**: remove test-asan/ubsan workflows (Michaël Zasso) [#56823](https://github.com/nodejs/node/pull/56823)
- \[[`866ac37255`](https://github.com/nodejs/node/commit/866ac37255)] - **tools**: run macOS test workflow with Xcode 16.1 (Michaël Zasso) [#56831](https://github.com/nodejs/node/pull/56831)
- \[[`55ca46ad8e`](https://github.com/nodejs/node/commit/55ca46ad8e)] - **tools**: update sccache and sccache-action (Michaël Zasso) [#56815](https://github.com/nodejs/node/pull/56815)
- \[[`be9c1c93a8`](https://github.com/nodejs/node/commit/be9c1c93a8)] - **tools**: fix license-builder for inspector_protocol (Michaël Zasso) [#56814](https://github.com/nodejs/node/pull/56814)
- \[[`6dab980fab`](https://github.com/nodejs/node/commit/6dab980fab)] - **typings**: fix `ImportModuleDynamicallyCallback` return type (Chengzhong Wu) [#57160](https://github.com/nodejs/node/pull/57160)
- \[[`e301098854`](https://github.com/nodejs/node/commit/e301098854)] - **util**: avoid run debug when enabled is false (fengmk2) [#57494](https://github.com/nodejs/node/pull/57494)
- \[[`17016d7722`](https://github.com/nodejs/node/commit/17016d7722)] - **(SEMVER-MINOR)** **util**: expose diff function used by the assertion errors (Giovanni Bucci) [#57462](https://github.com/nodejs/node/pull/57462)
- \[[`42b9e19f6b`](https://github.com/nodejs/node/commit/42b9e19f6b)] - **util**: enforce shouldColorize in styleText array arg (Marco Ippolito) [#56722](https://github.com/nodejs/node/pull/56722)
- \[[`5ed6d8be40`](https://github.com/nodejs/node/commit/5ed6d8be40)] - **(SEMVER-MINOR)** **v8**: add v8.getCppHeapStatistics() method (Aditi) [#57146](https://github.com/nodejs/node/pull/57146)
- \[[`c06d218b23`](https://github.com/nodejs/node/commit/c06d218b23)] - **win,build**: add option to enable Control Flow Guard (Hüseyin Açacak) [#56605](https://github.com/nodejs/node/pull/56605)
- \[[`8202211140`](https://github.com/nodejs/node/commit/8202211140)] - **win,test**: disable test case failing with ClangCL (Stefan Stojanovic) [#57397](https://github.com/nodejs/node/pull/57397)
- \[[`1a12b4c119`](https://github.com/nodejs/node/commit/1a12b4c119)] - **zlib**: use modern class syntax for zstd classes (Yagiz Nizipli) [#56965](https://github.com/nodejs/node/pull/56965)
- \[[`f9b3680268`](https://github.com/nodejs/node/commit/f9b3680268)] - **zlib**: make all zstd functions experimental (Yagiz Nizipli) [#56964](https://github.com/nodejs/node/pull/56964)
- \[[`4991e5d826`](https://github.com/nodejs/node/commit/4991e5d826)] - **(SEMVER-MINOR)** **zlib**: add zstd support (Jan Martin) [#52100](https://github.com/nodejs/node/pull/52100)

Windows 32-bit Installer: https://nodejs.org/dist/v22.15.0/node-v22.15.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v22.15.0/node-v22.15.0-x64.msi \
Windows ARM 64-bit Installer: https://nodejs.org/dist/v22.15.0/node-v22.15.0-arm64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v22.15.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v22.15.0/win-x64/node.exe \
Windows ARM 64-bit Binary: https://nodejs.org/dist/v22.15.0/win-arm64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v22.15.0/node-v22.15.0.pkg \
macOS Apple Silicon 64-bit Binary: https://nodejs.org/dist/v22.15.0/node-v22.15.0-darwin-arm64.tar.gz \
macOS Intel 64-bit Binary: https://nodejs.org/dist/v22.15.0/node-v22.15.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v22.15.0/node-v22.15.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v22.15.0/node-v22.15.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v22.15.0/node-v22.15.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v22.15.0/node-v22.15.0-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v22.15.0/node-v22.15.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v22.15.0/node-v22.15.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v22.15.0/node-v22.15.0.tar.gz \
Other release files: https://nodejs.org/dist/v22.15.0/ \
Documentation: https://nodejs.org/docs/v22.15.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

049718cc0ce4f58de90f7b752b82a78868a21fe7685af1c603ba1d22ae8a4d67  node-v22.15.0-aix-ppc64.tar.gz
1013d381e8a4ebe64484eed07db2637a570090fdef87924bf0beb92d9ba7cc92  node-v22.15.0-arm64.msi
92eb58f54d172ed9dee320b8450f1390db629d4262c936d5c074b25a110fed02  node-v22.15.0-darwin-arm64.tar.gz
6e278a107d50da24b644dd26810a639a5f8ca67b55086e6b693caabcbb759912  node-v22.15.0-darwin-arm64.tar.xz
f7f42bee60d602783d3a842f0a02a2ecd9cb9d7f6f3088686c79295b0222facf  node-v22.15.0-darwin-x64.tar.gz
7dab3f93551d88f1e63db6b32bae6d4858e16740e9849ebbeac1d43f5055d8f0  node-v22.15.0-darwin-x64.tar.xz
cda8bbbfb4f7fb19b65efd5faabc97ccea1e94422e7066b9a1e70280c1ca6453  node-v22.15.0-headers.tar.gz
1f0377a9f3dbb85959d38a6ebc6e7e189f520f509488c401be4970d8718a14e9  node-v22.15.0-headers.tar.xz
c3582722db988ed1eaefd590b877b86aaace65f68746726c1f8c79d26e5cc7de  node-v22.15.0-linux-arm64.tar.gz
d68adf72c531f1118bee75b20ffbc5911accfda5e73454a798625464b40a4adf  node-v22.15.0-linux-arm64.tar.xz
639a3ee3217049ba20e6f05651c7281a9d007111196387eb177081af8851e52f  node-v22.15.0-linux-armv7l.tar.gz
998da599863b8104992472ca4e738842b5e9b76b3665ea8bc13bd1ca52019426  node-v22.15.0-linux-armv7l.tar.xz
a744107732546a3b112630cde1cc2db681559668cea36bee851348ed036c101c  node-v22.15.0-linux-ppc64le.tar.gz
e12384dd6d8e80ee1cdb8cf0f38545d3cc2a5b99f7dd2c6ae05067222d39ff7a  node-v22.15.0-linux-ppc64le.tar.xz
600afa22128e5d1b7d472109b8f9f93ace161dab3c1a8a642895675471916d86  node-v22.15.0-linux-s390x.tar.gz
308e14b2b20445e03fbec920921b6b7f579832516b3e826b0d67be8acf0ac6f6  node-v22.15.0-linux-s390x.tar.xz
29d1c60c5b64ccdb0bc4e5495135e68e08a872e0ae91f45d9ec34fc135a17981  node-v22.15.0-linux-x64.tar.gz
dafe2e8f82cb97de1bd10db9e2ec4c07bbf53389b0799b1e095a918951e78fd4  node-v22.15.0-linux-x64.tar.xz
0bc096a279cd7cbc57bf3a6c6570f3ca03b3ab684d1878a1425919e9b6b76317  node-v22.15.0.pkg
8e64edbc70a7188873e0f4f07c812fa747c27c477d558b23dcb135bdf5e32298  node-v22.15.0.tar.gz
e7c4226d1d92f33ad854d6da4f7e519e77690b8e73f93496881f8c539174d9df  node-v22.15.0.tar.xz
d0615f16a280f4660a6c83d472a0b2e72122e47f4c82754a5ed950d54d0cbe3d  node-v22.15.0-win-arm64.7z
737cd7046d96575c2d6cd36e0355afba54b79296b0f403ef4b3b1b5852b10ab6  node-v22.15.0-win-arm64.zip
45471a4f77cdaacce971ab7a6698659ed872606f7b4b0aecdfe5689627134cb2  node-v22.15.0-win-x64.7z
06067d4f0d463f90ed803d5eca5b039a05dec5d70fc7b7cc254803a59bd0e27c  node-v22.15.0-win-x64.zip
f9534de57244052cb245ca3af29ac40e947b5735b05b37a28efac7bc82809a8a  node-v22.15.0-win-x86.7z
961e362568b91340b0229d27a64e3925ce5d807898a86074d61cab578f151843  node-v22.15.0-win-x86.zip
499d4a47b9db6db47114a31ceab69c69a7265900a29a5da53746c00d13dbad88  node-v22.15.0-x64.msi
1d884cdc5517126c2da00e23b445050a2f1e8b7bcaa57e0feb2c2481d347fb0a  node-v22.15.0-x86.msi
ebdab2adc9ff5fb09f852b665875af35138b9ee5725a3927bb85d7cfd36e059f  win-arm64/node.exe
6c43922c747e0c07fcc4ceb64f08e98854a5c1b31665764076f5783556973d16  win-arm64/node.lib
fb437ae33e9e5fff6792ba7a1e81d1a1dab5e346b57328e9a0d2ec7b9bdb309d  win-arm64/node_pdb.7z
7785c2aec7a86efce7707a1abc7dee58bd11e610f748f74e243789f9daff1e13  win-arm64/node_pdb.zip
77bdff912b1c569b3e693fe126f619337c3e9d73dafbc4d0bf1d4f1f6a145761  win-x64/node.exe
e731ad9af05eabe6bd941442b19dd6037f559a19a2fa8df2a8297ccb46ed7577  win-x64/node.lib
8c886b364ac8c65bef429d9d127f457e5c5755d19c4df294e9d187763dc6c3ea  win-x64/node_pdb.7z
132d18a1f522d20bc63d66b0eabe8089a2643ac61e1585d0269013f01ef1baef  win-x64/node_pdb.zip
0c20613ca23c8b8e7ca3d1ee5da9173dd9289f9833cff18649c192e4ce42c9b4  win-x86/node.exe
5f66397542e27adb288e1742720cedbeaba8a2034ebd58397306b2905f4acd2d  win-x86/node.lib
93d5377d08c9c5edf71f0c52e801c1ae55ace3e45e107fdac61ec7e04e0e5ff5  win-x86/node_pdb.7z
7601c2ca6c1a64b834af03ac2cb78b8485dc501e8b5b980ac4702ff8d6ad0cfb  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEpgI1MPxTRh/skfmcBM0/L94HlXgFAmgIldcACgkQBM0/L94H
lXiqeA//TdW4CLwlfmlA2l/5o0QcJLxWjtS/D+Wyn3t+yyRqBUdTTCBpvKUwen1D
mLlK5uyedcF6D5nazBn7aBB21l+RglQ/1a4l2aA4vlwP2wKUjwHdsU4qWxdiX4hm
C1VBqmerLw+UyzEy7V9/WQLEsocEqBrfw8j0g44cWqJW278iRMc4OFvX+WEn34N+
wxQI+aKQt7RrqlRUNKe83w8PkXP2wBMGiHwCQVN8GvS0IlKmEXUa/er9VHV5HXie
bCtmSlZvC4mPyUA3R4TYr1iAkoRrk0YNcoZ/eN45Zdd/IEyejoy0yddUNd3ow/6R
sHySf9b0YG0CZmJmLfHhAE9M+gUtz1QCHvGePs5+GScjytzIqBnNskfh/rSdjX1g
gWcU0vfOW4jz/enDmPXlLhMw18nz4UEVxmJMPFSd/yBhszcdds+aBR0mo9pkVZA2
dsoAlLZWl16DpdmYLJhK6P2vs9LjWV0Pn43HcXxWgxSq+hFiRc/3m9BWWN2HEr3/
uB/mE5kxb+h3qwlvygvzclnZ7PcITg8rpTWA3CIlnojPoS0p+23gSpYeGB1tVqz5
qZcdnDN1iMnj0HzPKQoi12YTVdklPpDvGucNJya5+eVydzkrvPBHxxVczRUp8SWN
bJA5eOBajrS087L8bHJJ6kefur9bmJoAVqbVxl72Jef7s/O7gvk=
=MzJb
-----END PGP SIGNATURE-----
```
