---
date: '2017-07-11T16:53:50.440Z'
category: release
title: Node v6.11.1 (LTS)
layout: blog-post
author: <PERSON><PERSON>
---

### Notable Changes

- **build**:
  - Disable V8 snapshots - The hashseed embedded in the snapshot is currently the same for all runs of the binary. This opens node up to collision attacks which could result in a Denial of Service. We have temporarily disabled snapshots until a more robust solution is found (<PERSON>)
- **deps**:
  - CVE-2017-1000381 - The c-ares function ares_parse_naptr_reply(), which is used for parsing NAPTR responses, could be triggered to read memory outside of the given input buffer if the passed in DNS response packet was crafted in a particular way. This patch checks that there is enough data for the required elements of an NAPTR record (2 int16, 3 bytes for string lengths) before processing a record. (<PERSON>)

### Commits

- [[`86c0eae524`](https://github.com/nodejs/node/commit/86c0eae524)] - **build**: disable V8 snapshots (<PERSON>) [nodejs/node-private#84](https://github.com/nodejs/node-private/pull/84)
- [[`75bc33d16f`](https://github.com/nodejs/node/commit/75bc33d16f)] - **deps**: cherry-pick 9478908a49 from cares upstream (David Drysdale) [nodejs/node-private#88](https://github.com/nodejs/node-private/pull/88)
- [[`a92d4ca460`](https://github.com/nodejs/node/commit/a92d4ca460)] - **deps**: Debug code requires bigger buffer on s390 (Michael Dawson) [nodejs/node-private#93](https://github.com/nodejs/node-private/pull/93)
- [[`6e247b8a4e`](https://github.com/nodejs/node/commit/6e247b8a4e)] - **test**: verify hash seed uniqueness (Ali Ijaz Sheikh) [nodejs/node-private#84](https://github.com/nodejs/node-private/pull/84)

Windows 32-bit Installer: https://nodejs.org/dist/v6.11.1/node-v6.11.1-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v6.11.1/node-v6.11.1-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v6.11.1/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v6.11.1/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v6.11.1/node-v6.11.1.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v6.11.1/node-v6.11.1-darwin-x64.tar.gz \
Linux 32-bit Binary: https://nodejs.org/dist/v6.11.1/node-v6.11.1-linux-x86.tar.xz \
Linux 64-bit Binary: https://nodejs.org/dist/v6.11.1/node-v6.11.1-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v6.11.1/node-v6.11.1-linux-ppc64le.tar.xz \
Linux PPC BE 64-bit Binary: https://nodejs.org/dist/v6.11.1/node-v6.11.1-linux-ppc64.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v6.11.1/node-v6.11.1-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v6.11.1/node-v6.11.1-aix-ppc64.tar.gz \
SmartOS 32-bit Binary: https://nodejs.org/dist/v6.11.1/node-v6.11.1-sunos-x86.tar.xz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v6.11.1/node-v6.11.1-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v6.11.1/node-v6.11.1-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v6.11.1/node-v6.11.1-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v6.11.1/node-v6.11.1-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v6.11.1/node-v6.11.1.tar.gz \
Other release files: https://nodejs.org/dist/v6.11.1/ \
Documentation: https://nodejs.org/docs/v6.11.1/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

903c39914631c25509467516f22d2b8b320d08f17d1a4d2a7de740dffc7a1c1b  node-v6.11.1-aix-ppc64.tar.gz
a2b839259089ef26f20c17864ff5ce9cd1a67e841be3d129b38d288b45fe375b  node-v6.11.1-darwin-x64.tar.gz
60521df5c436552bc09ae68e8399e7e32cf34c36b21f1dd93c4ddfa421ed7ef0  node-v6.11.1-darwin-x64.tar.xz
333fd57e7ffa419655980b462ece5efc1b2f09c50c5d702ce2a24cb763f19885  node-v6.11.1-headers.tar.gz
cef0b18c510fc396e571576d6479072ae2895387335532dd6811ef337a0bfcb5  node-v6.11.1-headers.tar.xz
f8c898c39ecc9806fd6b5a3b49f037fee3cfe823238b8c119b4f6f8b7869168e  node-v6.11.1-linux-arm64.tar.gz
da1188f66aefab601c419b18d658b2fb22dbfcd8dd9db1efe033454182302a06  node-v6.11.1-linux-arm64.tar.xz
f725f0b55f1ddf8b297325e7d34e7dd2acb940c380adadae0489df438f7b5976  node-v6.11.1-linux-armv6l.tar.gz
9111c08c27553afe39bf75b5c70d1090e99eab79874a349d2475372447751171  node-v6.11.1-linux-armv6l.tar.xz
f48a17b7781052a57414f4c4f9b8cda61886ac82dbcc72cb054151b8ddae7f13  node-v6.11.1-linux-armv7l.tar.gz
81d5cfeca28134e1c4011ef473b836f17b9833ce35477ce3d5d5244a574cb2d7  node-v6.11.1-linux-armv7l.tar.xz
79d64e0265d29eb2ce2b592657ef36ef8f263db85bd81a01ddc6e20c9602117a  node-v6.11.1-linux-ppc64le.tar.gz
2ce2f559d7b3e165638659c4bd8190f0b944376f6af2613af08b7dca390d9ca3  node-v6.11.1-linux-ppc64le.tar.xz
af73c5213a986c3cbe152ea06fd02e9bf9ae534f01f0f9aebf976aa1aecf3ce8  node-v6.11.1-linux-ppc64.tar.gz
47a51a7a92ca4dfd24e3ea545d3ecb85de028fe0213472a041c7244d05dd0557  node-v6.11.1-linux-ppc64.tar.xz
afb203218defb33189e9205ea3aaed8b16d7f8e8ba1c754225d931919f42566a  node-v6.11.1-linux-s390x.tar.gz
8b3ab3eb08245ee5fa381952d5b2e6a12c29dc8216687adc8a0f775ae56a9fd1  node-v6.11.1-linux-s390x.tar.xz
175e00ad504f0dca5a4d2af0f941e27ea0bd3178529fd1a9c3d67f3d75afd864  node-v6.11.1-linux-x64.tar.gz
e68cc956f0ca5c54e7f3016d639baf987f6f9de688bb7b31339ab7561af88f41  node-v6.11.1-linux-x64.tar.xz
6471fbb402e2e26086e125abb6dfe51d1f1aaee4c7cc769539490de6bca635c3  node-v6.11.1-linux-x86.tar.gz
5bab3ae4e437b94dfe6116fab5e0ad30eb9ef91d728f8daa9518ced28910f2af  node-v6.11.1-linux-x86.tar.xz
b74310f4de51f60de8377686d8c150c737a3f41bb2bda353f4b170f9db58aa5f  node-v6.11.1.pkg
9d7826c823df9edc51967005d80ba93ba962000c99e9a32b8f2c380275698b82  node-v6.11.1-sunos-x64.tar.gz
a0312c958f07b54ec2be0123a3fd04490a0bd85f5a63ed52a0f5b572b1e5a822  node-v6.11.1-sunos-x64.tar.xz
f400f12a1c7e954247e48d858cac4282b821858ac1d40760cfd30544c63a6752  node-v6.11.1-sunos-x86.tar.gz
beda0888e07856c5c1ae5a27bdb83af63706a78d402d0dd742a26927d0b0c4ad  node-v6.11.1-sunos-x86.tar.xz
0187d4e4ef00cee2b70b0ad0689100050654f26629775d097b145d0d8727f9a0  node-v6.11.1.tar.gz
6f6655b85919aa54cb045a6d69a226849802fcc26491d0db4ce59873e41cc2b8  node-v6.11.1.tar.xz
22adcce6cbf2e40a879906be662f1818073f4668964d3b4ce544da56c17051f9  node-v6.11.1-win-x64.7z
9ffdaf75546fdc39b1a4f49ed816618c4669e34e2ee3b7a810ad89c8287f1293  node-v6.11.1-win-x64.zip
117bdeec7008492e2b313b637773375f07bf9582d13083566d3ad5e089f30875  node-v6.11.1-win-x86.7z
39be718eab141a38edc8abd6328362e8bb63c13c0d6114f8f59df95e0eb36952  node-v6.11.1-win-x86.zip
a3cd79655e4bff8c33884923eb0ccdb7290729e5f3352e8644d63e2849ae445d  node-v6.11.1-x64.msi
6d10c47ad968bee5bf8d467a375eb93c148a780e3c34ee8d656055035f1f90c8  node-v6.11.1-x86.msi
2b740a411bd8d6d63749452169fdeb662d7bbec35a5108c19753fe993e0df141  win-x64/node.exe
392f789336568f3c4d5f8f24ddfe62e8084f33ef10d0fb186cd508055e579e6e  win-x64/node.lib
db8ce8256c5281139de57cadd19910125123ba16184027f58f269f82d9e370e2  win-x64/node_pdb.7z
2fd21217fba052f49ce175f16398163eb7616f8052403d0c5a8264e3ff3bd583  win-x64/node_pdb.zip
781f24b0426f30fa4ac0baf2d22710a938360df4f8c4ae3285ca8921937d985c  win-x86/node.exe
5b0285b9e03214d384e7a72cf45bf0eb047481dcab85ab93932271020b78b342  win-x86/node.lib
95eb4925543f143da527ecfabf454c3e6f2845e76afa8cac49a2c66d5ec06727  win-x86/node_pdb.7z
37a7dc654c03fea7f98428ecb81a1b575de5f19efa0f382f81302fdac7bdf0d1  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQEcBAEBCAAGBQJZZP/7AAoJEJM7AfQLXKlGBXUIALt3QRkrrOOEx+2O3C787Cw1
VnIIzElVgsUXAI4HFUeIYyfq3vgysQFdzHpKI0vjcgCHPP1fOEQrA4jIW8ZIdDlu
FsFjf8nRJSTTneMGyvX6xU8Q4Y5rA/+Htwg/CDIWU2T+pc+HSggk3D7TyOW48XKk
5N5F1L8tAx/wuebgntyXhjV0RT8ZFLAlgKFLvpzLmaZ0zya3wbkTv9jMwqt5TEGk
px7MxqSZmISHAGzHcVGRVwy3Znic8ZzSvthyGki+Z/8GLWzQeIBMIOS5wqhwl1kC
EWybzffifUDJfukFcKnHjDmV/BCS+/lkCSMcsH4yJzqLAcUxDxpRFstUP8K1VNs=
=qvw/
-----END PGP SIGNATURE-----

```
