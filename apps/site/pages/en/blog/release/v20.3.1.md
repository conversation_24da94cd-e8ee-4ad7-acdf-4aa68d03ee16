---
date: '2023-06-20T20:13:40.780Z'
category: release
title: Node v20.3.1 (Current)
layout: blog-post
author: <PERSON>
---

### Notable Changes

The following CVEs are fixed in this release:

- [CVE-2023-30581](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2023-30581): `mainModule.__proto__` Bypass Experimental Policy Mechanism (High)
- [CVE-2023-30584](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2023-30584): Path Traversal Bypass in Experimental Permission Model (High)
- [CVE-2023-30587](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2023-30587): Bypass of Experimental Permission Model via Node.js Inspector (High)
- [CVE-2023-30582](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2023-30582): Inadequate Permission Model Allows Unauthorized File Watching (Medium)
- [CVE-2023-30583](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2023-30583): Bypass of Experimental Permission Model via fs.openAsBlob() (Medium)
- [CVE-2023-30585](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2023-30585): Privilege escalation via Malicious Registry Key manipulation during Node.js installer repair process (Medium)
- [CVE-2023-30586](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2023-30586): Bypass of Experimental Permission Model via Arbitrary OpenSSL Engines (Medium)
- [CVE-2023-30588](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2023-30588): Process interuption due to invalid Public Key information in x509 certificates (Medium)
- [CVE-2023-30589](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2023-30589): HTTP Request Smuggling via Empty headers separated by CR (Medium)
- [CVE-2023-30590](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2023-30590): DiffieHellman does not generate keys after setting a private key (Medium)
- OpenSSL Security Releases
  - [OpenSSL security advisory 28th March](https://www.openssl.org/news/secadv/20230328.txt).
  - [OpenSSL security advisory 20th April](https://www.openssl.org/news/secadv/20230420.txt).
  - [OpenSSL security advisory 30th May](https://www.openssl.org/news/secadv/20230530.txt)

More detailed information on each of the vulnerabilities can be found in [June 2023 Security Releases](/blog/vulnerability/june-2023-security-releases/) blog post.

### Commits

- \[[`dac08dafc9`](https://github.com/nodejs/node/commit/dac08dafc9)] - **crypto**: handle cert with invalid SPKI gracefully (Tobias Nießen) [nodejs-private/node-private#393](https://github.com/nodejs-private/node-private/pull/393)
- \[[`d274c3babc`](https://github.com/nodejs/node/commit/d274c3babc)] - **crypto,https,tls**: disable engines if perms enabled (Tobias Nießen) [nodejs-private/node-private#409](https://github.com/nodejs-private/node-private/pull/409)
- \[[`5621c1de38`](https://github.com/nodejs/node/commit/5621c1de38)] - **deps**: update archs files for openssl-3.0.9-quic1 (Node.js GitHub Bot) [#48402](https://github.com/nodejs/node/pull/48402)
- \[[`771caa9f1c`](https://github.com/nodejs/node/commit/771caa9f1c)] - **deps**: upgrade openssl sources to quictls/openssl-3.0.9-quic1 (Node.js GitHub Bot) [#48402](https://github.com/nodejs/node/pull/48402)
- \[[`0459bf9c99`](https://github.com/nodejs/node/commit/0459bf9c99)] - **doc,test**: clarify behavior of DH generateKeys (Tobias Nießen) [nodejs-private/node-private#426](https://github.com/nodejs-private/node-private/pull/426)
- \[[`27e20501aa`](https://github.com/nodejs/node/commit/27e20501aa)] - **http**: disable request smuggling via empty headers (Paolo Insogna) [nodejs-private/node-private#427](https://github.com/nodejs-private/node-private/pull/427)
- \[[`9c17e335f1`](https://github.com/nodejs/node/commit/9c17e335f1)] - **msi**: do not create AppData\Roaming\npm (Tobias Nießen) [nodejs-private/node-private#408](https://github.com/nodejs-private/node-private/pull/408)
- \[[`b51124c637`](https://github.com/nodejs/node/commit/b51124c637)] - **permission**: handle fs path traversal (RafaelGSS) [nodejs-private/node-private#403](https://github.com/nodejs-private/node-private/pull/403)
- \[[`ebc5927adc`](https://github.com/nodejs/node/commit/ebc5927adc)] - **permission**: handle fs.openAsBlob (RafaelGSS) [nodejs-private/node-private#405](https://github.com/nodejs-private/node-private/pull/405)
- \[[`c39a43bff5`](https://github.com/nodejs/node/commit/c39a43bff5)] - **permission**: handle fs.watchFile (RafaelGSS) [nodejs-private/node-private#404](https://github.com/nodejs-private/node-private/pull/404)
- \[[`d0a8264ec9`](https://github.com/nodejs/node/commit/d0a8264ec9)] - **policy**: handle mainModule.\_\_proto\_\_ bypass (RafaelGSS) [nodejs-private/node-private#416](https://github.com/nodejs-private/node-private/pull/416)
- \[[`3df13d5a79`](https://github.com/nodejs/node/commit/3df13d5a79)] - **src,permission**: restrict inspector when pm enabled (RafaelGSS) [nodejs-private/node-private#410](https://github.com/nodejs-private/node-private/pull/410)

Windows 32-bit Installer: https://nodejs.org/dist/v20.3.1/node-v20.3.1-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v20.3.1/node-v20.3.1-x64.msi \
Windows ARM 64-bit Installer: https://nodejs.org/dist/v20.3.1/node-v20.3.1-arm64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v20.3.1/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v20.3.1/win-x64/node.exe \
Windows ARM 64-bit Binary: https://nodejs.org/dist/v20.3.1/win-arm64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v20.3.1/node-v20.3.1.pkg \
macOS Apple Silicon 64-bit Binary: https://nodejs.org/dist/v20.3.1/node-v20.3.1-darwin-arm64.tar.gz \
macOS Intel 64-bit Binary: https://nodejs.org/dist/v20.3.1/node-v20.3.1-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v20.3.1/node-v20.3.1-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v20.3.1/node-v20.3.1-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v20.3.1/node-v20.3.1-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v20.3.1/node-v20.3.1-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v20.3.1/node-v20.3.1-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v20.3.1/node-v20.3.1-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v20.3.1/node-v20.3.1.tar.gz \
Other release files: https://nodejs.org/dist/v20.3.1/ \
Documentation: https://nodejs.org/docs/v20.3.1/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

74408b56e0a20601cd073e52f90ffcafeb8d63a4e8deb8583fc3f0d26b502081  node-v20.3.1-aix-ppc64.tar.gz
fe48243bff912a26a76a7d5553c6f848a8b738c05ca976738d3a49af5621a584  node-v20.3.1-arm64.msi
fabf0d5bde4e1c16b6b96c310115425508c3750cd2b1d2992fa03d52b0050cf1  node-v20.3.1-darwin-arm64.tar.gz
5b9ba231a2502f9369295454a80e85468225f2695289ed163870a675eb5ed29f  node-v20.3.1-darwin-arm64.tar.xz
fd2be29c8e17ef1460a3c67b5fd36ead27159367a8958fae8fe8f3945465e0db  node-v20.3.1-darwin-x64.tar.gz
37684d83976612774f553e428a1f2610fb7efb270cca32657950c6e2542b250b  node-v20.3.1-darwin-x64.tar.xz
0e5265e18039399e4e4e013622d5cd5878f3aa9e7d96fc551a74713e0ea447c2  node-v20.3.1-headers.tar.gz
c5d8b256a6acd91178342cb4634d0f1652a6aceb32565a72f99c71d69dd22550  node-v20.3.1-headers.tar.xz
4785061286dccf43cef673d8f9fec637f7a27d7e4c5b075f393e99ae13089f17  node-v20.3.1-linux-arm64.tar.gz
75f820e7e0c460d902eb2c35716d158c06a4692e69f9a6cf2be30a721d7e0b42  node-v20.3.1-linux-arm64.tar.xz
f9f8fa6e90e341b2e334589fea5247dfffada4b8ac1eecfb1577b3bbc538f2de  node-v20.3.1-linux-armv7l.tar.gz
55d405b0ce92fe85a2604c56e92757ba255fff698f7e7d1bc5c9a3f5efcc966c  node-v20.3.1-linux-armv7l.tar.xz
6e786adcc4bce1d790af579829d29d11a59221c608b760fe5ba2557ee8e3c2c4  node-v20.3.1-linux-ppc64le.tar.gz
8463ced01d4aa008be5c699ac4c0f75edac341d6da3bb4c34d5e708bc164e660  node-v20.3.1-linux-ppc64le.tar.xz
4fe866b2e8f001d0861a3042d3778189ccbc494dfe7fb1c0b6af9ff8e5086ff4  node-v20.3.1-linux-s390x.tar.gz
62737d306d1a3c25b794a362a354092cbce5f04f22f9e8f5cfd61e95aecd487e  node-v20.3.1-linux-s390x.tar.xz
100507c0c4b4cf2f0661ab8ca79b21790c20a4aae24859e9ab60b7d95fbfd740  node-v20.3.1-linux-x64.tar.gz
a9f94435763f9c0128a8b6282ccbeefd0413a96e78e4427cfb7831d150c50334  node-v20.3.1-linux-x64.tar.xz
44ef9d3ceac5b7903948923bebc737bd062116a9768e2f620a0ccf034a0fcdf4  node-v20.3.1.pkg
785cfbf77998a96949d626f7bc63bc0b8e4737eb5dd2054b2e58d876904f5443  node-v20.3.1.tar.gz
12a82db306697959b4389b351a5f97848986b1313f9901b0e0b3d8cf4f3f9991  node-v20.3.1.tar.xz
569cb80351832ec3c1e38d61dabbe18d49f18cf20d658845d129c75d67e6e664  node-v20.3.1-win-arm64.7z
3ded6baf40440d762928d44df7d05d7c3f0c210a0240b8e5bb65ef3d9ad10edd  node-v20.3.1-win-arm64.zip
79a85bb3758bfaba66df3abda9e2f29c6ceb65ceedd19d5a5e589c92835b24d5  node-v20.3.1-win-x64.7z
b9660cf19136d6cfce9d5ec1bd7b8b7dcc5642fe5fb8c5ddde78dc0aba216dd5  node-v20.3.1-win-x64.zip
72657860e268ca1b778a249531cda920800851c252d9f3680201796f45dc76da  node-v20.3.1-win-x86.7z
69dce73312904b19b4a9b011bccfc47d05b8ebf05b07dcb58246f8d9c7f91e5b  node-v20.3.1-win-x86.zip
4afe2af88b327173910085d25645e84bf7986d5849db2872b1e304ffd96e295a  node-v20.3.1-x64.msi
6371129462ec79c6463be74bbd792bbb8071bf333d43fd8aa510d747da670fb4  node-v20.3.1-x86.msi
2b1965d736fcdf590c07751948c84b5936d9e09ae2a00d4cd0d307b965437cd9  win-arm64/node.exe
7d27551370e78fdf44e4e515458ad33d0c39983985853649fb01368999172662  win-arm64/node.lib
44589d9a922591ea1ec4c4a98f77f99cbeae9928ad26c10ec21d6012afc5a48a  win-arm64/node_pdb.7z
172e8df848141efa31a92ebbb8a557b314b6d97ada7f73cfd49b5397a2dc978d  win-arm64/node_pdb.zip
016669a5da40b058c02e4a61a990dec2b4ad4d6fc071eeebdd8d07db4d2601db  win-x64/node.exe
68a3ed4ccb2780dda353f609ec83ff6d6dc02a399f1dfed6621ae8c1f39a5788  win-x64/node.lib
7c3a5f02900047ef8451ac4cc134803960f80e2373fe187a8567d505f9b8415b  win-x64/node_pdb.7z
44c07769d543b300f3a59f34e5deb428fa2454da9a55114ed945133cc9a4e702  win-x64/node_pdb.zip
38022d2ed2a9cb86998dfbd0e3bb2983e4eabaedbec81949223c2e27a5d4ecc4  win-x86/node.exe
13fb4b75d9e6fdf5a708b4dc8ea4ca60b565e4a3235514870e4b369e26fade5f  win-x86/node.lib
2c5750f21ca050a0a7d179970b11d2c54458492166905e7748bd2307c19b2771  win-x86/node_pdb.7z
834cb5cd72a281f01dc5273bd47818f12d00a5efacc315dc3addaeb91920a2de  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQGzBAEBCAAdFiEEiQwI24V5Fi/uDfnbi+q0389VXvQFAmSSCGMACgkQi+q0389V
XvTfQgv/W6MXQFlsuetzmgBe2qxf6+OHZq5sAswD7spM9wArZy/Loh1VXuLMXpIO
d8ON2JxzWY74mYVrGLi5i0kfnV09PuxQfnOXcOJwncfgV60qVdnxQb50y1N2zHbc
uXUOcJKTq0HVN7u2zdRV65kG/cKDJoQacp1AilDGNPwQywpnaV3aZ+7RCEemKKd4
egx8fLW4+jPftke218EMCtLKkNoBPoIU29oGLcXL0uWNFSzEb3tiK0b+Nyz2brBe
qn9AY9OuzNjMT9G8z9WeK8+qDWDXjBJwnPq6lGFcPF1c7BnyDBSSVrcY0gFk4BzO
Mi92pdN1outi9ZiOW+49jI/1FPo9sxnqgiiP0GQyEB38JzCYsQ8OeASBYPoVlmu7
/hw0kxMgu6h0hxq48IemM4Nj/Fe9CP/OkFZ4WeS2VLHNv6LLZbF7FdbGIuCK5lw/
Jan0nFcx+LIdrE1zQwgj8KBVh7DfLOoSC5t6qMZYDoOu4Q3/3B/YAqquO4uM+wUK
6eJaCXMg
=jM7F
-----END PGP SIGNATURE-----

```
