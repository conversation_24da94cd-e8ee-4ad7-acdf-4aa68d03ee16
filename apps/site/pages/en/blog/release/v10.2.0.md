---
date: '2018-05-23T23:08:56.277Z'
category: release
title: Node v10.2.0 (Current)
layout: blog-post
author: <PERSON><PERSON>
---

### Notable Changes

- **addons**:
  - Fixed a memory leak for users of `AsyncResource` and N-API. (<PERSON>) [#20668](https://github.com/nodejs/node/pull/20668)
- **assert**:
  - The `error` parameter of `assert.throws()` can be an object containing regular expressions now. (<PERSON><PERSON>) [#20485](https://github.com/nodejs/node/pull/20485)
- **crypto**:
  - The `authTagLength` option has been made more flexible. (<PERSON>) [#20235](https://github.com/nodejs/node/pull/20235), [#20039](https://github.com/nodejs/node/pull/20039)
- **esm**:
  - Builtin modules (e.g. `fs`) now provide named exports in ES6 modules. (<PERSON>) [#20403](https://github.com/nodejs/node/pull/20403)
- **http**:
  - Handling of `close` and `aborted` events has been made more consistent. (<PERSON>) [#20075](https://github.com/nodejs/node/pull/20075), [#20611](https://github.com/nodejs/node/pull/20611)
- **module**:
  - add --preserve-symlinks-main (David Goldstein) [#19911](https://github.com/nodejs/node/pull/19911)
- **timers**:
  - `timeout.refresh()` has been added to the public API. (Jeremiah Senkpiel) [#20298](https://github.com/nodejs/node/pull/20298)
- Embedder support:
  - Functions for creating V8 `Isolate` and `Context` objects with Node.js-specific behaviour have been added to the API. (Allen Yonghuang Wang) [#20639](https://github.com/nodejs/node/pull/20639)
  - Node.js `Environment`s clean up resources before exiting now. (Anna Henningsen) [#19377](https://github.com/nodejs/node/pull/19377)
  - Support for multi-threaded embedding has been improved. (Anna Henningsen) [#20542](https://github.com/nodejs/node/pull/20542), [#20539](https://github.com/nodejs/node/pull/20539), [#20541](https://github.com/nodejs/node/pull/20541)

### Commits

- [[`8f8a0e3483`](https://github.com/nodejs/node/commit/8f8a0e3483)] - **assert**: fix wrong message indentation (Ruben Bridgewater) [#20791](https://github.com/nodejs/node/pull/20791)
- [[`338e663860`](https://github.com/nodejs/node/commit/338e663860)] - **assert**: fix EOL issue in messages on Windows (Ruben Bridgewater) [#20754](https://github.com/nodejs/node/pull/20754)
- [[`1160d61cd9`](https://github.com/nodejs/node/commit/1160d61cd9)] - **assert**: support symbols as assertion messages (cjihrig) [#20693](https://github.com/nodejs/node/pull/20693)
- [[`bb857d9e71`](https://github.com/nodejs/node/commit/bb857d9e71)] - **assert**: make sure throws is able to handle primitives (Ruben Bridgewater) [#20482](https://github.com/nodejs/node/pull/20482)
- [[`5d06c1e1ae`](https://github.com/nodejs/node/commit/5d06c1e1ae)] - **assert**: move AssertionError into own file (Ruben Bridgewater) [#20486](https://github.com/nodejs/node/pull/20486)
- [[`a5ee31bba1`](https://github.com/nodejs/node/commit/a5ee31bba1)] - **(SEMVER-MINOR)** **assert**: accept regular expressions to validate (Ruben Bridgewater) [#20485](https://github.com/nodejs/node/pull/20485)
- [[`74db9f43ba`](https://github.com/nodejs/node/commit/74db9f43ba)] - **assert,util**: lazy load comparison functions (Ruben Bridgewater) [#20567](https://github.com/nodejs/node/pull/20567)
- [[`9feca3ea32`](https://github.com/nodejs/node/commit/9feca3ea32)] - **async_hooks**: lazy loading for startup performance (Ruben Bridgewater) [#20567](https://github.com/nodejs/node/pull/20567)
- [[`e61337d36d`](https://github.com/nodejs/node/commit/e61337d36d)] - **async_wrap**: fix memory leak in AsyncResource (Michael Dawson) [#20668](https://github.com/nodejs/node/pull/20668)
- [[`56de3bfb70`](https://github.com/nodejs/node/commit/56de3bfb70)] - **benchmark**: add tls benchmark for legacy SecurePair (Alex Fernández) [#20344](https://github.com/nodejs/node/pull/20344)
- [[`3b516177c8`](https://github.com/nodejs/node/commit/3b516177c8)] - **build**: use nyc's merge command (Benjamin Coe) [#20760](https://github.com/nodejs/node/pull/20760)
- [[`aaf1df59be`](https://github.com/nodejs/node/commit/aaf1df59be)] - **build**: export openssl TLSv1 methods again (Ben Noordhuis) [#20712](https://github.com/nodejs/node/pull/20712)
- [[`7a980086c8`](https://github.com/nodejs/node/commit/7a980086c8)] - **build**: always use BUILDTYPE binary to run JS tests (Joyee Cheung) [#20362](https://github.com/nodejs/node/pull/20362)
- [[`47103493f7`](https://github.com/nodejs/node/commit/47103493f7)] - **child_process**: fix exec set stdout.setEncoding (killagu) [#18976](https://github.com/nodejs/node/pull/18976)
- [[`4a872b98a0`](https://github.com/nodejs/node/commit/4a872b98a0)] - **cluster**: remove obsolete array allocation (Ruben Bridgewater) [#20567](https://github.com/nodejs/node/pull/20567)
- [[`a41c44a307`](https://github.com/nodejs/node/commit/a41c44a307)] - **codeowners**: add rule for \*.gypi files (Ben Noordhuis) [#20740](https://github.com/nodejs/node/pull/20740)
- [[`b701f5af18`](https://github.com/nodejs/node/commit/b701f5af18)] - **codeowners**: fix typo in v8-inspector team name (Ben Noordhuis) [#20740](https://github.com/nodejs/node/pull/20740)
- [[`3fd67249df`](https://github.com/nodejs/node/commit/3fd67249df)] - **console**: .table fall back to logging for function too (ohbarye) [#20681](https://github.com/nodejs/node/pull/20681)
- [[`bc6dbc3bfc`](https://github.com/nodejs/node/commit/bc6dbc3bfc)] - **console**: lazy load cli (Ruben Bridgewater) [#20567](https://github.com/nodejs/node/pull/20567)
- [[`cecec46204`](https://github.com/nodejs/node/commit/cecec46204)] - **crypto**: add test case for AES key wrapping (Yihong Wang) [#20587](https://github.com/nodejs/node/pull/20587)
- [[`34d67085d5`](https://github.com/nodejs/node/commit/34d67085d5)] - **crypto**: allocate more memory for cipher.update() (Yihong Wang) [#20370](https://github.com/nodejs/node/pull/20370)
- [[`2b2ccae390`](https://github.com/nodejs/node/commit/2b2ccae390)] - **(SEMVER-MINOR)** **crypto**: support authTagLength in GCM encryption (Tobias Nießen) [#20235](https://github.com/nodejs/node/pull/20235)
- [[`1e5de6fe97`](https://github.com/nodejs/node/commit/1e5de6fe97)] - **crypto**: add using directives for v8::Int32, Uint32 (Tobias Nießen) [#20225](https://github.com/nodejs/node/pull/20225)
- [[`f5e7010eb9`](https://github.com/nodejs/node/commit/f5e7010eb9)] - **crypto**: use kNoAuthTagLength in InitAuthenticated (Tobias Nießen) [#20225](https://github.com/nodejs/node/pull/20225)
- [[`5ea1a58db9`](https://github.com/nodejs/node/commit/5ea1a58db9)] - **crypto**: remove rsaPrivate and rename rsaPublic (Daniel Bevenius) [#20164](https://github.com/nodejs/node/pull/20164)
- [[`503844eb73`](https://github.com/nodejs/node/commit/503844eb73)] - **crypto**: add addCipherPrototypeFunctions function (Daniel Bevenius) [#20164](https://github.com/nodejs/node/pull/20164)
- [[`72029b8cc7`](https://github.com/nodejs/node/commit/72029b8cc7)] - **crypto**: add createCipher/WithIV functions (Daniel Bevenius) [#20164](https://github.com/nodejs/node/pull/20164)
- [[`bdd2856152`](https://github.com/nodejs/node/commit/bdd2856152)] - **(SEMVER-MINOR)** **crypto**: allow to restrict valid GCM tag length (Tobias Nießen) [#20039](https://github.com/nodejs/node/pull/20039)
- [[`e56716e396`](https://github.com/nodejs/node/commit/e56716e396)] - **deps**: cherry-pick ff0a9793334 from upstream V8 (Anna Henningsen) [#20719](https://github.com/nodejs/node/pull/20719)
- [[`8e058d5f94`](https://github.com/nodejs/node/commit/8e058d5f94)] - **deps**: patch V8 to 6.6.346.32 (Myles Borins) [#20748](https://github.com/nodejs/node/pull/20748)
- [[`cb94601cf2`](https://github.com/nodejs/node/commit/cb94601cf2)] - **deps**: cherry-pick 23652c5f from upstream V8 (Eugene Ostroukhov) [#20608](https://github.com/nodejs/node/pull/20608)
- [[`91e60b0d33`](https://github.com/nodejs/node/commit/91e60b0d33)] - **deps**: V8: cherry-pick b49206d from upstream (Ali Ijaz Sheikh) [#20727](https://github.com/nodejs/node/pull/20727)
- [[`6ce589f7f4`](https://github.com/nodejs/node/commit/6ce589f7f4)] - **deps**: patch V8 to 6.6.346.31 (Myles Borins) [#20603](https://github.com/nodejs/node/pull/20603)
- [[`f69a823f8e`](https://github.com/nodejs/node/commit/f69a823f8e)] - **deps**: upgrade to libuv 1.20.3 (cjihrig) [#20585](https://github.com/nodejs/node/pull/20585)
- [[`60eab9100f`](https://github.com/nodejs/node/commit/60eab9100f)] - **dns**: lazy loaded (Ruben Bridgewater) [#20567](https://github.com/nodejs/node/pull/20567)
- [[`c1fe9b29b1`](https://github.com/nodejs/node/commit/c1fe9b29b1)] - **doc**: add note about autocrlf required for tests (Bartosz Sosnowski) [#20752](https://github.com/nodejs/node/pull/20752)
- [[`8a17a259f3`](https://github.com/nodejs/node/commit/8a17a259f3)] - **doc**: fix some nits in hardcoded manpage links (Vse Mozhet Byt) [#20854](https://github.com/nodejs/node/pull/20854)
- [[`8317a468db`](https://github.com/nodejs/node/commit/8317a468db)] - **doc**: fix fs.promises sample codes (Keita Akutsu) [#20838](https://github.com/nodejs/node/pull/20838)
- [[`37b9fe1e68`](https://github.com/nodejs/node/commit/37b9fe1e68)] - **doc**: fix typo in http2.md (Keita Akutsu) [#20843](https://github.com/nodejs/node/pull/20843)
- [[`88aee8a65c`](https://github.com/nodejs/node/commit/88aee8a65c)] - **doc**: improve \_Deprecation\_ definition (Rich Trott) [#20788](https://github.com/nodejs/node/pull/20788)
- [[`7b1c035218`](https://github.com/nodejs/node/commit/7b1c035218)] - **doc**: describe using multiple link-module on win (Bartosz Sosnowski) [#20774](https://github.com/nodejs/node/pull/20774)
- [[`9a8cdc93ff`](https://github.com/nodejs/node/commit/9a8cdc93ff)] - **doc**: fix typo in COLLABORATOR_GUIDE.md (Vse Mozhet Byt) [#20742](https://github.com/nodejs/node/pull/20742)
- [[`657f8cbe41`](https://github.com/nodejs/node/commit/657f8cbe41)] - **doc**: fix linter warnings and typos in manpage (Alhadis) [#20741](https://github.com/nodejs/node/pull/20741)
- [[`165971d35b`](https://github.com/nodejs/node/commit/165971d35b)] - **doc**: sort references in ASCII order (Rich Trott) [#20790](https://github.com/nodejs/node/pull/20790)
- [[`8f489a2447`](https://github.com/nodejs/node/commit/8f489a2447)] - **doc**: add .github to CODEOWNERS (Rich Trott) [#20733](https://github.com/nodejs/node/pull/20733)
- [[`7943449305`](https://github.com/nodejs/node/commit/7943449305)] - **doc**: improve specificity in CODEOWNERS (Rich Trott) [#20729](https://github.com/nodejs/node/pull/20729)
- [[`7d28f5bb1b`](https://github.com/nodejs/node/commit/7d28f5bb1b)] - **doc**: reorder CODEOWNERS file (Rich Trott) [#20732](https://github.com/nodejs/node/pull/20732)
- [[`fd14ec1101`](https://github.com/nodejs/node/commit/fd14ec1101)] - **doc**: add missing `changes:` entry for assert.throws (Anna Henningsen) [#20723](https://github.com/nodejs/node/pull/20723)
- [[`a66aad4a50`](https://github.com/nodejs/node/commit/a66aad4a50)] - **doc**: fixup NODE_EXTERN -\> NAPI_EXTERN (Michael Dawson) [#20641](https://github.com/nodejs/node/pull/20641)
- [[`f263340731`](https://github.com/nodejs/node/commit/f263340731)] - **doc**: fix signature for napi_create_range_error (Michael Dawson) [#20641](https://github.com/nodejs/node/pull/20641)
- [[`d11a435875`](https://github.com/nodejs/node/commit/d11a435875)] - **doc**: fix typo in dns docs (Anna Henningsen) [#20711](https://github.com/nodejs/node/pull/20711)
- [[`512982c0ff`](https://github.com/nodejs/node/commit/512982c0ff)] - **doc**: update AUTHORS list (Michaël Zasso) [#20658](https://github.com/nodejs/node/pull/20658)
- [[`e06c5874f6`](https://github.com/nodejs/node/commit/e06c5874f6)] - **doc**: add global node_modules to require.resolve() (musgravejw) [#20534](https://github.com/nodejs/node/pull/20534)
- [[`1d7379d641`](https://github.com/nodejs/node/commit/1d7379d641)] - **doc**: fix stability text for n-api (Michael Dawson) [#20659](https://github.com/nodejs/node/pull/20659)
- [[`73492233c3`](https://github.com/nodejs/node/commit/73492233c3)] - **doc**: add util.types.isModuleNamespaceObject() (Gus Caplan) [#20616](https://github.com/nodejs/node/pull/20616)
- [[`3929516a6f`](https://github.com/nodejs/node/commit/3929516a6f)] - **doc**: fix nits in doc/api_assets/style.css (Vse Mozhet Byt) [#20601](https://github.com/nodejs/node/pull/20601)
- [[`01abed1c36`](https://github.com/nodejs/node/commit/01abed1c36)] - **doc**: update assert documentation (Ruben Bridgewater) [#20486](https://github.com/nodejs/node/pull/20486)
- [[`c546746396`](https://github.com/nodejs/node/commit/c546746396)] - **doc**: add util.types.isBig{Int,Uint}64Array() (cjihrig) [#20615](https://github.com/nodejs/node/pull/20615)
- [[`d568952b8c`](https://github.com/nodejs/node/commit/d568952b8c)] - **doc**: fix missing napi_get_typedarray_info() param (Gabriel Schulhof) [#20631](https://github.com/nodejs/node/pull/20631)
- [[`9177f734e3`](https://github.com/nodejs/node/commit/9177f734e3)] - **doc**: update VM section text (Daniel Bevenius) [#20595](https://github.com/nodejs/node/pull/20595)
- [[`88bc6da6e9`](https://github.com/nodejs/node/commit/88bc6da6e9)] - **doc**: add parameters for Http2Stream:error event (Ujjwal Sharma) [#20610](https://github.com/nodejs/node/pull/20610)
- [[`b3b267a87c`](https://github.com/nodejs/node/commit/b3b267a87c)] - **doc**: add params for ClientHttp2Session:altsvc (Ujjwal Sharma) [#20598](https://github.com/nodejs/node/pull/20598)
- [[`d327893193`](https://github.com/nodejs/node/commit/d327893193)] - **doc**: refactor mode constants parts in fs.md (Shobhit Chittora) [#20558](https://github.com/nodejs/node/pull/20558)
- [[`4a7bb406fe`](https://github.com/nodejs/node/commit/4a7bb406fe)] - **doc, tools**: unify stability signatures (Vse Mozhet Byt) [#20552](https://github.com/nodejs/node/pull/20552)
- [[`c244436707`](https://github.com/nodejs/node/commit/c244436707)] - **errors**: move functions to error code (Ruben Bridgewater) [#20486](https://github.com/nodejs/node/pull/20486)
- [[`104c3bc443`](https://github.com/nodejs/node/commit/104c3bc443)] - **(SEMVER-MINOR)** **esm**: provide named exports for builtin libs (Gus Caplan) [#20403](https://github.com/nodejs/node/pull/20403)
- [[`9b43af3703`](https://github.com/nodejs/node/commit/9b43af3703)] - **fs**: lazy load createPromise/promiseResolve (James M Snell) [#20766](https://github.com/nodejs/node/pull/20766)
- [[`2d2897855f`](https://github.com/nodejs/node/commit/2d2897855f)] - **fs**: lazy load the promises impl (James M Snell) [#20766](https://github.com/nodejs/node/pull/20766)
- [[`dc30d36467`](https://github.com/nodejs/node/commit/dc30d36467)] - **fs**: consistent constants use and cleanup (James M Snell) [#20765](https://github.com/nodejs/node/pull/20765)
- [[`e5a0c197bd`](https://github.com/nodejs/node/commit/e5a0c197bd)] - **fs**: refactor promises version of lchown and lchmod (cjihrig) [#20551](https://github.com/nodejs/node/pull/20551)
- [[`39caa6ddaf`](https://github.com/nodejs/node/commit/39caa6ddaf)] - **fs**: use \_final() for fs.WriteStream (Jackson Tian) [#20562](https://github.com/nodejs/node/pull/20562)
- [[`de06115d18`](https://github.com/nodejs/node/commit/de06115d18)] - **fs**: make fs.promises non-enumerable (cjihrig) [#20632](https://github.com/nodejs/node/pull/20632)
- [[`fe7e8d6a3e`](https://github.com/nodejs/node/commit/fe7e8d6a3e)] - **http**: fix capitalization of 418 status message (я котик пур-пур) [#20700](https://github.com/nodejs/node/pull/20700)
- [[`75e4415c40`](https://github.com/nodejs/node/commit/75e4415c40)] - **http**: do not rely on the 'agentRemove' event (Luigi Pinca) [#20786](https://github.com/nodejs/node/pull/20786)
- [[`4c6bfbdbb4`](https://github.com/nodejs/node/commit/4c6bfbdbb4)] - **http**: fix client response close & aborted (Robert Nagy) [#20075](https://github.com/nodejs/node/pull/20075)
- [[`8029a2473e`](https://github.com/nodejs/node/commit/8029a2473e)] - **http**: always emit close on req and res (Robert Nagy) [#20611](https://github.com/nodejs/node/pull/20611)
- [[`2687d44739`](https://github.com/nodejs/node/commit/2687d44739)] - **http2**: fix several serious bugs (Anatoli Papirovski) [#20772](https://github.com/nodejs/node/pull/20772)
- [[`b2fb1d70bb`](https://github.com/nodejs/node/commit/b2fb1d70bb)] - **http2**: fix end without read (Anatoli Papirovski) [#20621](https://github.com/nodejs/node/pull/20621)
- [[`de2b04772b`](https://github.com/nodejs/node/commit/de2b04772b)] - **http2**: avoid bind and properly clean up in compat (Robert Nagy) [#20374](https://github.com/nodejs/node/pull/20374)
- [[`28ecf93dc5`](https://github.com/nodejs/node/commit/28ecf93dc5)] - **http2**: destroy the socket properly and add tests (Mathias Buus) [#19852](https://github.com/nodejs/node/pull/19852)
- [[`92dd9b59eb`](https://github.com/nodejs/node/commit/92dd9b59eb)] - **inspector**: get rid of the make_unique (Eugene Ostroukhov) [#20895](https://github.com/nodejs/node/pull/20895)
- [[`04f7678edb`](https://github.com/nodejs/node/commit/04f7678edb)] - **inspector**: add a "NodeTracing" domain support (Eugene Ostroukhov) [#20608](https://github.com/nodejs/node/pull/20608)
- [[`ccf69dd3b6`](https://github.com/nodejs/node/commit/ccf69dd3b6)] - **inspector**: fix inspector::Agent::HasConnectedSessions (helloshuangzi) [#20614](https://github.com/nodejs/node/pull/20614)
- [[`e0fd80c641`](https://github.com/nodejs/node/commit/e0fd80c641)] - **lib**: do not call performance hooks (Ruben Bridgewater) [#20567](https://github.com/nodejs/node/pull/20567)
- [[`bd13193979`](https://github.com/nodejs/node/commit/bd13193979)] - **lib**: remove unnecessary require (Ruben Bridgewater) [#20567](https://github.com/nodejs/node/pull/20567)
- [[`07537749db`](https://github.com/nodejs/node/commit/07537749db)] - **lib**: use capital letters in comments (Ruben Bridgewater) [#20567](https://github.com/nodejs/node/pull/20567)
- [[`72f3228203`](https://github.com/nodejs/node/commit/72f3228203)] - **lib**: lazy loaded (Ruben Bridgewater) [#20567](https://github.com/nodejs/node/pull/20567)
- [[`3aab6ce39d`](https://github.com/nodejs/node/commit/3aab6ce39d)] - **lib**: lazy load necessary loaders (Ruben Bridgewater) [#20567](https://github.com/nodejs/node/pull/20567)
- [[`486ac23cb0`](https://github.com/nodejs/node/commit/486ac23cb0)] - **lib**: only load inspector stuff if necessary (Ruben Bridgewater) [#20567](https://github.com/nodejs/node/pull/20567)
- [[`61415dccc4`](https://github.com/nodejs/node/commit/61415dccc4)] - **(SEMVER-MINOR)** **lib**: defer pausing stdin to the next tick (Anna Henningsen) [#19377](https://github.com/nodejs/node/pull/19377)
- [[`7c13e54ca7`](https://github.com/nodejs/node/commit/7c13e54ca7)] - **lib**: return directly from packageMainCache (Daniel Bevenius) [#20591](https://github.com/nodejs/node/pull/20591)
- [[`fb7a775242`](https://github.com/nodejs/node/commit/fb7a775242)] - **lib,src**: use V8 API for collection inspection (Anna Henningsen) [#20719](https://github.com/nodejs/node/pull/20719)
- [[`8d8b0bdf38`](https://github.com/nodejs/node/commit/8d8b0bdf38)] - **lib,src,test**: fix comments (Tobias Nießen) [#20846](https://github.com/nodejs/node/pull/20846)
- [[`b10823506d`](https://github.com/nodejs/node/commit/b10823506d)] - **meta**: add initial CODEOWNERS file (James M Snell) [#20554](https://github.com/nodejs/node/pull/20554)
- [[`678b7544df`](https://github.com/nodejs/node/commit/678b7544df)] - **module**: introduce defaultModuleName in module.js (Daniel Bevenius) [#20709](https://github.com/nodejs/node/pull/20709)
- [[`b6ea5df08a`](https://github.com/nodejs/node/commit/b6ea5df08a)] - **(SEMVER-MINOR)** **module**: add --preserve-symlinks-main (David Goldstein) [#19911](https://github.com/nodejs/node/pull/19911)
- [[`eac7aad55e`](https://github.com/nodejs/node/commit/eac7aad55e)] - **net**: lazy load dns (Ruben Bridgewater) [#20567](https://github.com/nodejs/node/pull/20567)
- [[`1f34c04bd0`](https://github.com/nodejs/node/commit/1f34c04bd0)] - **net**: remove typo in setTimeout comment (Daniel Bevenius) [#20576](https://github.com/nodejs/node/pull/20576)
- [[`d614511b9f`](https://github.com/nodejs/node/commit/d614511b9f)] - **net,http2**: refactor \_write and \_writev (Ujjwal Sharma) [#20643](https://github.com/nodejs/node/pull/20643)
- [[`28d00a18c8`](https://github.com/nodejs/node/commit/28d00a18c8)] - **os**: lazy loaded (Ruben Bridgewater) [#20567](https://github.com/nodejs/node/pull/20567)
- [[`2e9957641e`](https://github.com/nodejs/node/commit/2e9957641e)] - **perf_hooks**: always set bootstrapComplete (James M Snell) [#20768](https://github.com/nodejs/node/pull/20768)
- [[`c8fe8e8f5d`](https://github.com/nodejs/node/commit/c8fe8e8f5d)] - **(SEMVER-MINOR)** **process**: create stdin with `manualStart: true` (Anna Henningsen) [#19377](https://github.com/nodejs/node/pull/19377)
- [[`4a92da15dc`](https://github.com/nodejs/node/commit/4a92da15dc)] - **querystring**: lazy loaded (Ruben Bridgewater) [#20567](https://github.com/nodejs/node/pull/20567)
- [[`3eb38debb4`](https://github.com/nodejs/node/commit/3eb38debb4)] - **readline**: lazy loaded (Ruben Bridgewater) [#20567](https://github.com/nodejs/node/pull/20567)
- [[`ada41b02c5`](https://github.com/nodejs/node/commit/ada41b02c5)] - **repl**: make console, module and require non-enumerable (Ruben Bridgewater) [#20717](https://github.com/nodejs/node/pull/20717)
- [[`83119db45e`](https://github.com/nodejs/node/commit/83119db45e)] - **repl**: add friendly tips about how to exit repl (monkingxue) [#20617](https://github.com/nodejs/node/pull/20617)
- [[`c4f0e81dd0`](https://github.com/nodejs/node/commit/c4f0e81dd0)] - **src**: trace_events: background thread events (Ali Ijaz Sheikh) [#20823](https://github.com/nodejs/node/pull/20823)
- [[`3110d15f2b`](https://github.com/nodejs/node/commit/3110d15f2b)] - **src**: make pointers lean left in node_crypto.cc (Daniel Bevenius) [#20799](https://github.com/nodejs/node/pull/20799)
- [[`b6225349f4`](https://github.com/nodejs/node/commit/b6225349f4)] - **src**: use unqualified names in node_crypto.cc (Daniel Bevenius) [#20799](https://github.com/nodejs/node/pull/20799)
- [[`010ad8c26c`](https://github.com/nodejs/node/commit/010ad8c26c)] - **src**: move \*Exceptions out to separate cc/h (James M Snell) [#20789](https://github.com/nodejs/node/pull/20789)
- [[`08b98d17f1`](https://github.com/nodejs/node/commit/08b98d17f1)] - **src**: fix odd linting issue (James M Snell) [#20789](https://github.com/nodejs/node/pull/20789)
- [[`36d4a42e35`](https://github.com/nodejs/node/commit/36d4a42e35)] - **src**: move CallbackScope to separate cc/h (James M Snell) [#20789](https://github.com/nodejs/node/pull/20789)
- [[`4b64c847f1`](https://github.com/nodejs/node/commit/4b64c847f1)] - **src**: trace_events: support for metadata events (Ali Ijaz Sheikh) [#20757](https://github.com/nodejs/node/pull/20757)
- [[`3edb04d065`](https://github.com/nodejs/node/commit/3edb04d065)] - **src**: remove 2nd `undefined` argument in node_file.cc (Dan Kang) [#20629](https://github.com/nodejs/node/pull/20629)
- [[`d6805c15a5`](https://github.com/nodejs/node/commit/d6805c15a5)] - **src**: add override to ThreadPool methods in zlib (Daniel Bevenius) [#20769](https://github.com/nodejs/node/pull/20769)
- [[`01aa0581fe`](https://github.com/nodejs/node/commit/01aa0581fe)] - **src**: order C++ error list alphabetically (Anna Henningsen) [#20707](https://github.com/nodejs/node/pull/20707)
- [[`5eb0765fc9`](https://github.com/nodejs/node/commit/5eb0765fc9)] - **src**: handle TryCatch with empty message (Ben Noordhuis) [#20708](https://github.com/nodejs/node/pull/20708)
- [[`e0b438a641`](https://github.com/nodejs/node/commit/e0b438a641)] - **(SEMVER-MINOR)** **src**: add public API to create isolate and context (helloshuangzi) [#20639](https://github.com/nodejs/node/pull/20639)
- [[`d223e3ca41`](https://github.com/nodejs/node/commit/d223e3ca41)] - **src**: make `AsyncResource` destructor virtual (Anna Henningsen) [#20633](https://github.com/nodejs/node/pull/20633)
- [[`28b58b56a8`](https://github.com/nodejs/node/commit/28b58b56a8)] - **src**: replace `template<` → `template <` (Anna Henningsen) [#20675](https://github.com/nodejs/node/pull/20675)
- [[`30aceedba6`](https://github.com/nodejs/node/commit/30aceedba6)] - **src**: make env\_ and context\_ private (Daniel Bevenius) [#20671](https://github.com/nodejs/node/pull/20671)
- [[`9422909e07`](https://github.com/nodejs/node/commit/9422909e07)] - **src**: remove unused includes from node_contextify.h (Daniel Bevenius) [#20670](https://github.com/nodejs/node/pull/20670)
- [[`e732b4ce5c`](https://github.com/nodejs/node/commit/e732b4ce5c)] - **src**: use unqualified names in node_contextify.cc (Daniel Bevenius) [#20669](https://github.com/nodejs/node/pull/20669)
- [[`57dfd64f8f`](https://github.com/nodejs/node/commit/57dfd64f8f)] - **src**: add missing override to ThreadPoolWork funcs (Daniel Bevenius) [#20663](https://github.com/nodejs/node/pull/20663)
- [[`2347ce8870`](https://github.com/nodejs/node/commit/2347ce8870)] - **(SEMVER-MINOR)** **src**: unify thread pool work (Anna Henningsen) [#19377](https://github.com/nodejs/node/pull/19377)
- [[`7153bec955`](https://github.com/nodejs/node/commit/7153bec955)] - **(SEMVER-MINOR)** **src**: always call ReadStop() before Close() (Anna Henningsen) [#19377](https://github.com/nodejs/node/pull/19377)
- [[`9e1dcdc5bd`](https://github.com/nodejs/node/commit/9e1dcdc5bd)] - **(SEMVER-MINOR)** **src**: remove NodeCategorySet destructor (Anna Henningsen) [#19377](https://github.com/nodejs/node/pull/19377)
- [[`97d939a5f0`](https://github.com/nodejs/node/commit/97d939a5f0)] - **(SEMVER-MINOR)** **src**: store fd for libuv streams on Windows (Anna Henningsen) [#19377](https://github.com/nodejs/node/pull/19377)
- [[`5b0d2e7b19`](https://github.com/nodejs/node/commit/5b0d2e7b19)] - **(SEMVER-MINOR)** **src**: add can_call_into_js flag (Anna Henningsen) [#19377](https://github.com/nodejs/node/pull/19377)
- [[`9e2554ce98`](https://github.com/nodejs/node/commit/9e2554ce98)] - **(SEMVER-MINOR)** **src**: use cleanup hooks to tear down BaseObjects (Anna Henningsen) [#19377](https://github.com/nodejs/node/pull/19377)
- [[`8995408748`](https://github.com/nodejs/node/commit/8995408748)] - **(SEMVER-MINOR)** **src**: keep track of open requests (Anna Henningsen) [#19377](https://github.com/nodejs/node/pull/19377)
- [[`75aad9069b`](https://github.com/nodejs/node/commit/75aad9069b)] - **(SEMVER-MINOR)** **src**: unify ReqWrap libuv calling (Anna Henningsen) [#19377](https://github.com/nodejs/node/pull/19377)
- [[`e253edb48a`](https://github.com/nodejs/node/commit/e253edb48a)] - **(SEMVER-MINOR)** **src**: make CleanupHandles() tear down handles/reqs (Anna Henningsen) [#19377](https://github.com/nodejs/node/pull/19377)
- [[`ba269585ed`](https://github.com/nodejs/node/commit/ba269585ed)] - **(SEMVER-MINOR)** **src**: add environment cleanup hooks (Anna Henningsen) [#19377](https://github.com/nodejs/node/pull/19377)
- [[`40fb885ecf`](https://github.com/nodejs/node/commit/40fb885ecf)] - **src**: more automatic memory management in node_crypto.cc (Anna Henningsen) [#20238](https://github.com/nodejs/node/pull/20238)
- [[`fd5adbc9c3`](https://github.com/nodejs/node/commit/fd5adbc9c3)] - **src**: fix node_crypto.cc compiler warnings (Daniel Bevenius) [#20216](https://github.com/nodejs/node/pull/20216)
- [[`db457cb6a0`](https://github.com/nodejs/node/commit/db457cb6a0)] - **src**: fix typo in util.h comment (Anna Henningsen) [#20656](https://github.com/nodejs/node/pull/20656)
- [[`e93726ad10`](https://github.com/nodejs/node/commit/e93726ad10)] - **src**: fix nullptr dereference for signal during startup (Anna Henningsen) [#20637](https://github.com/nodejs/node/pull/20637)
- [[`0824ea9d7b`](https://github.com/nodejs/node/commit/0824ea9d7b)] - **src**: use unqualified names in module_wrap.cc (Daniel Bevenius) [#20594](https://github.com/nodejs/node/pull/20594)
- [[`43ec938634`](https://github.com/nodejs/node/commit/43ec938634)] - **src**: remove static variables from string_search (Anna Henningsen) [#20541](https://github.com/nodejs/node/pull/20541)
- [[`4873fbaf63`](https://github.com/nodejs/node/commit/4873fbaf63)] - **src**: remove unused freelist.h header (Anna Henningsen) [#20544](https://github.com/nodejs/node/pull/20544)
- [[`a89cc2886e`](https://github.com/nodejs/node/commit/a89cc2886e)] - **src**: protect global state with mutexes (Anna Henningsen) [#20542](https://github.com/nodejs/node/pull/20542)
- [[`2df99ac095`](https://github.com/nodejs/node/commit/2df99ac095)] - **src**: use lock for c-ares library init/cleanup (Anna Henningsen) [#20539](https://github.com/nodejs/node/pull/20539)
- [[`5803973206`](https://github.com/nodejs/node/commit/5803973206)] - **src**: minor refactor to string_search.h (Anna Henningsen) [#20546](https://github.com/nodejs/node/pull/20546)
- [[`983cb269e0`](https://github.com/nodejs/node/commit/983cb269e0)] - **src**: don't create Undefined if not needed (Daniel Bevenius) [#20573](https://github.com/nodejs/node/pull/20573)
- [[`e01e060763`](https://github.com/nodejs/node/commit/e01e060763)] - **src**: rename handle parameter object (Daniel Bevenius) [#20570](https://github.com/nodejs/node/pull/20570)
- [[`328a2c7c28`](https://github.com/nodejs/node/commit/328a2c7c28)] - **stream**: lazy load end-of-stream (Ruben Bridgewater) [#20567](https://github.com/nodejs/node/pull/20567)
- [[`94d217f877`](https://github.com/nodejs/node/commit/94d217f877)] - **stream**: lazy load ReadableAsyncIterator (Ruben Bridgewater) [#20567](https://github.com/nodejs/node/pull/20567)
- [[`ed5f253cfa`](https://github.com/nodejs/node/commit/ed5f253cfa)] - **stream**: refactor getHighWaterMark in state.js (Daniel Bevenius) [#20415](https://github.com/nodejs/node/pull/20415)
- [[`39a41120d4`](https://github.com/nodejs/node/commit/39a41120d4)] - **stream**: simplify writable's validChunk() (cjihrig) [#20696](https://github.com/nodejs/node/pull/20696)
- [[`981a2f7b16`](https://github.com/nodejs/node/commit/981a2f7b16)] - **stream**: simplify Writable.prototype.cork() (cjihrig) [#20697](https://github.com/nodejs/node/pull/20697)
- [[`ebc1b77e5a`](https://github.com/nodejs/node/commit/ebc1b77e5a)] - **stream**: no need to initial er with false (Jackson Tian) [#20607](https://github.com/nodejs/node/pull/20607)
- [[`0ace8f9835`](https://github.com/nodejs/node/commit/0ace8f9835)] - **string_decoder**: lazy loaded (Ruben Bridgewater) [#20567](https://github.com/nodejs/node/pull/20567)
- [[`5886b7826c`](https://github.com/nodejs/node/commit/5886b7826c)] - **test**: test about:blank against invalid WHATWG URL (Joyee Cheung) [#20796](https://github.com/nodejs/node/pull/20796)
- [[`b6d678b018`](https://github.com/nodejs/node/commit/b6d678b018)] - **test**: fix tests that fail under coverage (Benjamin Coe) [#20794](https://github.com/nodejs/node/pull/20794)
- [[`dc29a3b386`](https://github.com/nodejs/node/commit/dc29a3b386)] - **test**: add promise API test for appendFile() (Rich Trott) [#20842](https://github.com/nodejs/node/pull/20842)
- [[`d9aecc0c07`](https://github.com/nodejs/node/commit/d9aecc0c07)] - **test**: improve coverage for internal/readline (Masashi Hirano) [#20840](https://github.com/nodejs/node/pull/20840)
- [[`9c560ca907`](https://github.com/nodejs/node/commit/9c560ca907)] - **test**: rename and document tls test (Anna Henningsen) [#20820](https://github.com/nodejs/node/pull/20820)
- [[`dd32a7a0d4`](https://github.com/nodejs/node/commit/dd32a7a0d4)] - **test**: fix flaky http2-session-unref (Anatoli Papirovski) [#20772](https://github.com/nodejs/node/pull/20772)
- [[`a8c74e89ae`](https://github.com/nodejs/node/commit/a8c74e89ae)] - **test**: use error code rather than message in test (Rich Trott) [#20859](https://github.com/nodejs/node/pull/20859)
- [[`f5f9cdc110`](https://github.com/nodejs/node/commit/f5f9cdc110)] - **test**: define SharedArrayBuffer as a known global (cjihrig) [#20849](https://github.com/nodejs/node/pull/20849)
- [[`22f46e7766`](https://github.com/nodejs/node/commit/22f46e7766)] - **test**: remove common.globalCheck (Ruben Bridgewater) [#20717](https://github.com/nodejs/node/pull/20717)
- [[`5ffce3ef06`](https://github.com/nodejs/node/commit/5ffce3ef06)] - **test**: remove untested knownGlobals (Ruben Bridgewater) [#20717](https://github.com/nodejs/node/pull/20717)
- [[`e7c2616d10`](https://github.com/nodejs/node/commit/e7c2616d10)] - **test**: mark tests as flaky as intermediate step (Ruben Bridgewater) [#20835](https://github.com/nodejs/node/pull/20835)
- [[`b664a848fa`](https://github.com/nodejs/node/commit/b664a848fa)] - **test**: improve assertion in test-performance (Anna Henningsen) [#20809](https://github.com/nodejs/node/pull/20809)
- [[`045b37b32d`](https://github.com/nodejs/node/commit/045b37b32d)] - **test**: add eslint rule to verify assertion input (Ruben Bridgewater) [#20718](https://github.com/nodejs/node/pull/20718)
- [[`1ae076b30e`](https://github.com/nodejs/node/commit/1ae076b30e)] - **test**: add loaded modules test (Ruben Bridgewater) [#20567](https://github.com/nodejs/node/pull/20567)
- [[`9e432ca79c`](https://github.com/nodejs/node/commit/9e432ca79c)] - **test**: add promise API test for appendFile() (Rich Trott) [#20739](https://github.com/nodejs/node/pull/20739)
- [[`a6667d68f3`](https://github.com/nodejs/node/commit/a6667d68f3)] - **test**: slightly improve test-util-inspect assertions (Anna Henningsen) [#20721](https://github.com/nodejs/node/pull/20721)
- [[`a4cbe30791`](https://github.com/nodejs/node/commit/a4cbe30791)] - **test**: improve reliability of http2-session-timeout (Rich Trott) [#20692](https://github.com/nodejs/node/pull/20692)
- [[`0d28b4b6ba`](https://github.com/nodejs/node/commit/0d28b4b6ba)] - **test**: disable colors in test-assert-checktag.js (cjihrig) [#20695](https://github.com/nodejs/node/pull/20695)
- [[`dccbc3a153`](https://github.com/nodejs/node/commit/dccbc3a153)] - **test**: disable colors in test-assert-deep.js (cjihrig) [#20695](https://github.com/nodejs/node/pull/20695)
- [[`90c77bcc18`](https://github.com/nodejs/node/commit/90c77bcc18)] - **test**: disable colors in test-assert.js (cjihrig) [#20695](https://github.com/nodejs/node/pull/20695)
- [[`2b6e8ccfd4`](https://github.com/nodejs/node/commit/2b6e8ccfd4)] - **test**: increase test coverage for fs/promises.js (David Humphrey) [#19811](https://github.com/nodejs/node/pull/19811)
- [[`e6c0bbe185`](https://github.com/nodejs/node/commit/e6c0bbe185)] - **test**: display values in AssertionErrors (RakshithNM) [#20545](https://github.com/nodejs/node/pull/20545)
- [[`886116f837`](https://github.com/nodejs/node/commit/886116f837)] - **test**: apply test-fs-access to promises API (Rich Trott) [#20667](https://github.com/nodejs/node/pull/20667)
- [[`2a7c863d3d`](https://github.com/nodejs/node/commit/2a7c863d3d)] - **test**: modernize and correct test-doctool-html.js (Vse Mozhet Byt) [#20676](https://github.com/nodejs/node/pull/20676)
- [[`9c1c03e5d4`](https://github.com/nodejs/node/commit/9c1c03e5d4)] - **test**: better error message in trace events test (Anna Henningsen) [#20655](https://github.com/nodejs/node/pull/20655)
- [[`0aab92f6b2`](https://github.com/nodejs/node/commit/0aab92f6b2)] - **test**: add test for async hooks parity for async/await (Maya Lekova) [#20626](https://github.com/nodejs/node/pull/20626)
- [[`2db83fdc0c`](https://github.com/nodejs/node/commit/2db83fdc0c)] - **test**: remove deepStrictEqual() third argument (Francesco Falanga) [#20702](https://github.com/nodejs/node/pull/20702)
- [[`87f3f5af2e`](https://github.com/nodejs/node/commit/87f3f5af2e)] - **test**: plug AliasedBuffer cctest memory leak (Anna Henningsen) [#20665](https://github.com/nodejs/node/pull/20665)
- [[`eb21a6b7f6`](https://github.com/nodejs/node/commit/eb21a6b7f6)] - **test**: remove crypto.DEFAULT_ENCODING usage (Daniel Bevenius) [#20221](https://github.com/nodejs/node/pull/20221)
- [[`de34cfad58`](https://github.com/nodejs/node/commit/de34cfad58)] - **test**: make sure linked lists are inspectable with defaults (Anna Henningsen) [#20017](https://github.com/nodejs/node/pull/20017)
- [[`41e1dc09de`](https://github.com/nodejs/node/commit/41e1dc09de)] - **test**: add regression test for #11257 (Benjamin Coe) [#20391](https://github.com/nodejs/node/pull/20391)
- [[`56530f0844`](https://github.com/nodejs/node/commit/56530f0844)] - **(SEMVER-MINOR)** **timers**: make timer.refresh() a public API (Jeremiah Senkpiel) [#20298](https://github.com/nodejs/node/pull/20298)
- [[`bd500af2ff`](https://github.com/nodejs/node/commit/bd500af2ff)] - **tools**: update prohibited-strings md linting (Rich Trott) [#20742](https://github.com/nodejs/node/pull/20742)
- [[`2361f6454c`](https://github.com/nodejs/node/commit/2361f6454c)] - **tools**: stricter eslint rule for globals (Ruben Bridgewater) [#20567](https://github.com/nodejs/node/pull/20567)
- [[`38fc741c36`](https://github.com/nodejs/node/commit/38fc741c36)] - **tools**: eliminate intermediate module in doctools (Vse Mozhet Byt) [#20701](https://github.com/nodejs/node/pull/20701)
- [[`6f4e9ffb7b`](https://github.com/nodejs/node/commit/6f4e9ffb7b)] - **tools**: fix "the the" typos in comments (Masashi Hirano) [#20716](https://github.com/nodejs/node/pull/20716)
- [[`b795953b5f`](https://github.com/nodejs/node/commit/b795953b5f)] - **tools**: hide symbols for builtin JS files in binary (Anna Henningsen) [#20634](https://github.com/nodejs/node/pull/20634)
- [[`44960a0d5a`](https://github.com/nodejs/node/commit/44960a0d5a)] - **tools**: make C++ linter reject `template<` (Anna Henningsen) [#20675](https://github.com/nodejs/node/pull/20675)
- [[`7bff6d15b2`](https://github.com/nodejs/node/commit/7bff6d15b2)] - **tools**: overhaul tools/doc/html.js (Vse Mozhet Byt) [#20613](https://github.com/nodejs/node/pull/20613)
- [[`f2ad1d5d22`](https://github.com/nodejs/node/commit/f2ad1d5d22)] - **(SEMVER-MINOR)** **tools**: remove `--quiet` from run-valgrind.py (Anna Henningsen) [#19377](https://github.com/nodejs/node/pull/19377)
- [[`ebd102e473`](https://github.com/nodejs/node/commit/ebd102e473)] - **tools**: use macOS as operating system name (Rich Trott) [#20579](https://github.com/nodejs/node/pull/20579)
- [[`08097ccf84`](https://github.com/nodejs/node/commit/08097ccf84)] - **tools**: ignore VS compiler output (Yulong Wang) [#20527](https://github.com/nodejs/node/pull/20527)
- [[`8781bcb1ee`](https://github.com/nodejs/node/commit/8781bcb1ee)] - **tools, doc**: wrap manpage links in code elements (Vse Mozhet Byt) [#20785](https://github.com/nodejs/node/pull/20785)
- [[`e1ff587a26`](https://github.com/nodejs/node/commit/e1ff587a26)] - **tools, doc**: fix stability index isssues (Vse Mozhet Byt) [#20731](https://github.com/nodejs/node/pull/20731)
- [[`526163cff9`](https://github.com/nodejs/node/commit/526163cff9)] - **url**: introduce `URL_FLAGS_IS_DEFAULT_SCHEME_PORT` flag (Ayush Gupta) [#20479](https://github.com/nodejs/node/pull/20479)
- [[`c8c9211fa6`](https://github.com/nodejs/node/commit/c8c9211fa6)] - **util**: improve error inspection (Ruben Bridgewater) [#20802](https://github.com/nodejs/node/pull/20802)
- [[`f0d6a37c5c`](https://github.com/nodejs/node/commit/f0d6a37c5c)] - **util**: fix inspected stack indentation (Ruben Bridgewater) [#20802](https://github.com/nodejs/node/pull/20802)
- [[`38bc5fbd6b`](https://github.com/nodejs/node/commit/38bc5fbd6b)] - **util**: remove erroneous whitespace (Ruben Bridgewater) [#20802](https://github.com/nodejs/node/pull/20802)
- [[`5ce85a72cb`](https://github.com/nodejs/node/commit/5ce85a72cb)] - **util**: wrap error in brackets without stack (Ruben Bridgewater) [#20802](https://github.com/nodejs/node/pull/20802)
- [[`b308a07301`](https://github.com/nodejs/node/commit/b308a07301)] - **util**: support inspecting namespaces of unevaluated modules (Gus Caplan) [#20782](https://github.com/nodejs/node/pull/20782)
- [[`105f606202`](https://github.com/nodejs/node/commit/105f606202)] - **v8**: backport 9fb02b526f1cd3b859a530a01adb08bc0d089f4f (Gus Caplan) [#20575](https://github.com/nodejs/node/pull/20575)
- [[`8604481b2e`](https://github.com/nodejs/node/commit/8604481b2e)] - **vm**: move emitExperimentalWarning (Daniel Bevenius) [#20593](https://github.com/nodejs/node/pull/20593)
- [[`740bf783e5`](https://github.com/nodejs/node/commit/740bf783e5)] - **vm,trace_events**: add node.vm.script trace events category (James M Snell) [#20728](https://github.com/nodejs/node/pull/20728)
- [[`d5db576d15`](https://github.com/nodejs/node/commit/d5db576d15)] - **zlib**: reduce number of static internal methods (Anna Henningsen) [#20674](https://github.com/nodejs/node/pull/20674)

Windows 32-bit Installer: https://nodejs.org/dist/v10.2.0/node-v10.2.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v10.2.0/node-v10.2.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v10.2.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v10.2.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v10.2.0/node-v10.2.0.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v10.2.0/node-v10.2.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v10.2.0/node-v10.2.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v10.2.0/node-v10.2.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v10.2.0/node-v10.2.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v10.2.0/node-v10.2.0-aix-ppc64.tar.gz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v10.2.0/node-v10.2.0-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v10.2.0/node-v10.2.0-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v10.2.0/node-v10.2.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v10.2.0/node-v10.2.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v10.2.0/node-v10.2.0.tar.gz \
Other release files: https://nodejs.org/dist/v10.2.0/ \
Documentation: https://nodejs.org/docs/v10.2.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

bc1e5b26ddd18f494036682fda957199210b4c88b382f61eb45ec55a70601546  node-v10.2.0-aix-ppc64.tar.gz
35fcc482d07218119ce5fde62620994324f03f8c4426dd680886c6844b62232a  node-v10.2.0-darwin-x64.tar.gz
c6dd7ed69f1b546d22716ae6e21a30eefe1ba876a83cc42951ccf3eb9bf1d3f1  node-v10.2.0-darwin-x64.tar.xz
5ce883e56a3732436b4fe2dc9e7513f0f3bf6b80ac532c750a326a6cce942e89  node-v10.2.0-headers.tar.gz
fb8c8dd185ca71c509fa6096af5c4ab854c003503bdc390110acdd49e8e58841  node-v10.2.0-headers.tar.xz
77a9e159c303faa12c85a0cffd3cf8a3a1134ef781a7bc52787f49e97116540f  node-v10.2.0-linux-arm64.tar.gz
8f970be59a6a11f511a04ae1bd303746f0c5409498e062fb1d263d3c3c1093bd  node-v10.2.0-linux-arm64.tar.xz
98ba7a6ac4e2d98ca59035f5544416632f776108e1f279c3ffd928a44a3f5c7e  node-v10.2.0-linux-armv6l.tar.gz
58d4ed9a75cecd831f6daf898cfdc19be79a48b4383eab3e137661fc50750fdc  node-v10.2.0-linux-armv6l.tar.xz
5ecb0ff548b5b5b049aeaf4a9741986ec3915859876a30f174675b81bdf774de  node-v10.2.0-linux-armv7l.tar.gz
8f87651aac8ddcf4b837ea8bfd8b7275d252006d69bb534614412338e9432800  node-v10.2.0-linux-armv7l.tar.xz
734be9510319dac1b26646ac482d47e16906285a392791258de8a640ff932c5b  node-v10.2.0-linux-ppc64le.tar.gz
1d8be5b5e929980600b2236816b8823a5ee9bb92ab6623d8cafe691a6b45a6c0  node-v10.2.0-linux-ppc64le.tar.xz
56fecc27a9e757f11c4c11ee671f682311419d47d7dc78d5997e66d38c606cc7  node-v10.2.0-linux-s390x.tar.gz
580884f650c3b2008aa5962b5d30a3076c58bc9a51aac559e3acf2ae612bb3e7  node-v10.2.0-linux-s390x.tar.xz
75195a61d029819ad9ce77cbb13d3a29362c07cf73f2dc52da8a3f14839554cb  node-v10.2.0-linux-x64.tar.gz
a6ef9adc824db795b36f81ad0856adc5c878395c4ce2af20f5ba7b76a1ca9982  node-v10.2.0-linux-x64.tar.xz
2ef7dd2c8b33faa9693dafc10c0e0beba2deb5b466bb35cb51c941b7b73e5d6b  node-v10.2.0.pkg
c64ac195892bde08acca7a6cc2345d5ae976d2626eeb427f891f6eccc72b6c5a  node-v10.2.0-sunos-x64.tar.gz
d58476f8b4a35ec4c3ad3537111f7cdcbd93008595ba6250d9ae23d382eb43da  node-v10.2.0-sunos-x64.tar.xz
0f6c3dfb991c515e61c38f3a475a6f74d2b0b2ac14628816f9eecf5ecb179238  node-v10.2.0.tar.gz
7e5f11b785412e94394e31793296c37ebe1aa32f95d9fe56b7a055169aa512c3  node-v10.2.0.tar.xz
67a96d149d6b329fdc19ee9bd0f46bd3682312296337830e628fdc0f48351d56  node-v10.2.0-win-x64.7z
7d7144d57b1b910d10f51d5445ae4306f10d3d9f45ca08b49e8777472993db51  node-v10.2.0-win-x64.zip
147784d304dc64927e4125baac2373264dd4007971d8e31be098f2b1cb9ef397  node-v10.2.0-win-x86.7z
fee964206da7351bb1e1284b0043c205398dc767e3d66a1e2f67dd5b4412e403  node-v10.2.0-win-x86.zip
37d73c20e95b0bf0d86d670e416062e3bdc9e6f22ae0592e5bfa73f9363b3aef  node-v10.2.0-x64.msi
6d6f8f3ca510f48dba1129ffe4e427dae5ce709fd4ae250e01d1b6733062b4c9  node-v10.2.0-x86.msi
cb37295d941d20abd86af2e37bba9bbec1c36d5a7fd444a783a03e95cadfddd4  win-x64/node.exe
3f56450f08017fe210a01336470557b1054460e87ea42112fd5d0974e1369403  win-x64/node.lib
401410f537b2573df137cf94e768cc2ca9b5bf524d4533e8bddcead4ed70878a  win-x64/node_pdb.7z
37d2a1a6865b26467a267925a461e7a9d943bb9647253b29c153c91513a3ee03  win-x64/node_pdb.zip
e4aedcbf030ebab6f5912071f79dbb5d77451b6d2924bd0c761bedb46dcab418  win-x86/node.exe
702c66fe5a71ab574df0f2dba5c5abbe6c2c1ccc79198f1c896f085bbe5fc439  win-x86/node.lib
bc48d7028c5aff5150a5cd72f2327293a5136ef024de2499dbe962c19370a720  win-x86/node_pdb.7z
530c5fdf1c5983c99c5b704c10be1dbbf7c01fd2975277e7863c1d6dae9ab74a  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQEzBAEBCAAdFiEEDv/hvO/ZyE49CYFSkzsB9AtcqUYFAlsF88cACgkQkzsB9Atc
qUYaWQf/TaTxIyvAFeDv2lv00aj6sVa67qVys7v6bzucoi9PmeAD3QPqKwsXExUb
vEWG+cqoLDvixl4qZ7/iM08wBE5Pa8Xm4Kn6YQGIeNlrgPZhyzP2I3LX8spVeRAW
F63Se58CbdeSLCRDlqshV7EbeUlGFeTtFgiv9LIhh7i4ghdo+xdukaeBsWHLsD27
Y99GCxgNOLy0OjH5x18lvG//v59TUk/zodHaPbipqVJ6d8gO4bVaO0PAj+R3CVGU
RJ4yPhAgettOtriJAHXZzImE8JeK9yTSsvJmYHEPz3cy9A6jd0FTbpaGttAJKULM
nbAkNJqxWInsGmuQdMhZPrKan1I1OA==
=kIQm
-----END PGP SIGNATURE-----

```
