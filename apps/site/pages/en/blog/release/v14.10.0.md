---
date: '2020-09-08T15:54:55.953Z'
category: release
title: Node v14.10.0 (Current)
layout: blog-post
author: <PERSON>
---

### Notable Changes

- [[`2ab33c58ae`](https://github.com/nodejs/node/commit/2ab33c58ae)] - **(SEMVER-MINOR)** **buffer**: also alias BigUInt methods (<PERSON>) [#34960](https://github.com/nodejs/node/pull/34960)
- [[`44d89a9faa`](https://github.com/nodejs/node/commit/44d89a9faa)] - **(SEMVER-MINOR)** **crypto**: add randomInt function (<PERSON><PERSON>) [#34600](https://github.com/nodejs/node/pull/34600)
- [[`8aac42caf2`](https://github.com/nodejs/node/commit/8aac42caf2)] - **(SEMVER-MINOR)** **perf_hooks**: add idleTime and event loop util (<PERSON>) [#34938](https://github.com/nodejs/node/pull/34938)
- [[`4bb40078da`](https://github.com/nodejs/node/commit/4bb40078da)] - **(SEMVER-MINOR)** **stream**: simpler and faster Readable async iterator (Robert Nagy) [#34035](https://github.com/nodejs/node/pull/34035)
- [[`ffae5f3809`](https://github.com/nodejs/node/commit/ffae5f3809)] - **(SEMVER-MINOR)** **stream**: save error in state (Robert Nagy) [#34103](https://github.com/nodejs/node/pull/34103)

### Commits

- [[`1fdfaa578f`](https://github.com/nodejs/node/commit/1fdfaa578f)] - **bootstrap**: correct --frozen-intrinsics override fix (Guy Bedford) [#35041](https://github.com/nodejs/node/pull/35041)
- [[`2ab33c58ae`](https://github.com/nodejs/node/commit/2ab33c58ae)] - **(SEMVER-MINOR)** **buffer**: also alias BigUInt methods (Anna Henningsen) [#34960](https://github.com/nodejs/node/pull/34960)
- [[`1be6956ee0`](https://github.com/nodejs/node/commit/1be6956ee0)] - **build**: require "allow edits" to be checked (Jordan Harband) [#35002](https://github.com/nodejs/node/pull/35002)
- [[`7b7299012e`](https://github.com/nodejs/node/commit/7b7299012e)] - **build**: comment about auto close when stalled via with github action (Phillip Johnsen) [#34555](https://github.com/nodejs/node/pull/34555)
- [[`d6c796b4ab`](https://github.com/nodejs/node/commit/d6c796b4ab)] - **build**: close stalled issues and PRs with github action (Phillip Johnsen) [#34555](https://github.com/nodejs/node/pull/34555)
- [[`46766a10df`](https://github.com/nodejs/node/commit/46766a10df)] - **build**: use autorebase option for git node land (Denys Otrishko) [#34969](https://github.com/nodejs/node/pull/34969)
- [[`7afb67f491`](https://github.com/nodejs/node/commit/7afb67f491)] - **build**: use latest node-core-utils from npm (Denys Otrishko) [#34969](https://github.com/nodejs/node/pull/34969)
- [[`d06e158253`](https://github.com/nodejs/node/commit/d06e158253)] - **build**: add support for build on arm64 (Evan Lucas) [#34238](https://github.com/nodejs/node/pull/34238)
- [[`755f9e4bc8`](https://github.com/nodejs/node/commit/755f9e4bc8)] - **build,deps**: add gen-openssl target (Evan Lucas) [#34642](https://github.com/nodejs/node/pull/34642)
- [[`178a740caf`](https://github.com/nodejs/node/commit/178a740caf)] - **crypto**: simplify KeyObject constructor (Rich Trott) [#35064](https://github.com/nodejs/node/pull/35064)
- [[`a12d92c97b`](https://github.com/nodejs/node/commit/a12d92c97b)] - **crypto**: fix randomInt range check (Tobias Nießen) [#35052](https://github.com/nodejs/node/pull/35052)
- [[`6d0d5b2ec2`](https://github.com/nodejs/node/commit/6d0d5b2ec2)] - **crypto**: align parameter names with documentation (Rich Trott) [#35054](https://github.com/nodejs/node/pull/35054)
- [[`44d89a9faa`](https://github.com/nodejs/node/commit/44d89a9faa)] - **(SEMVER-MINOR)** **crypto**: add randomInt function (Oli Lalonde) [#34600](https://github.com/nodejs/node/pull/34600)
- [[`791a85b880`](https://github.com/nodejs/node/commit/791a85b880)] - **deps**: V8: cherry-pick 6be2f6e26e8d (Benjamin Coe) [#35055](https://github.com/nodejs/node/pull/35055)
- [[`96ae05a770`](https://github.com/nodejs/node/commit/96ae05a770)] - **deps**: V8: backport 3f071e3e7e15 (Milad Farazmand) [#35036](https://github.com/nodejs/node/pull/35036)
- [[`90f9348297`](https://github.com/nodejs/node/commit/90f9348297)] - **deps**: update brotli to v1.0.9 (Anna Henningsen) [#34937](https://github.com/nodejs/node/pull/34937)
- [[`f1fcd6646d`](https://github.com/nodejs/node/commit/f1fcd6646d)] - **deps**: add openssl support for arm64 (Evan Lucas) [#34238](https://github.com/nodejs/node/pull/34238)
- [[`bbf7b925a2`](https://github.com/nodejs/node/commit/bbf7b925a2)] - **doc**: use present tense in events.md (Rich Trott) [#35068](https://github.com/nodejs/node/pull/35068)
- [[`f6b2286e12`](https://github.com/nodejs/node/commit/f6b2286e12)] - **doc**: change stablility-2 color for accessibility (Rich Trott) [#35061](https://github.com/nodejs/node/pull/35061)
- [[`8044533e87`](https://github.com/nodejs/node/commit/8044533e87)] - **doc**: add link to safe integer definition (Tobias Nießen) [#35049](https://github.com/nodejs/node/pull/35049)
- [[`f03a4d78a2`](https://github.com/nodejs/node/commit/f03a4d78a2)] - **doc**: format exponents better (Tobias Nießen) [#35050](https://github.com/nodejs/node/pull/35050)
- [[`1a9ca52716`](https://github.com/nodejs/node/commit/1a9ca52716)] - **doc**: add ESM examples in `module` API doc page (Antoine du HAMEL) [#34875](https://github.com/nodejs/node/pull/34875)
- [[`0ac7d5423f`](https://github.com/nodejs/node/commit/0ac7d5423f)] - **doc**: add deprecated badge to legacy URL methods (Antoine du HAMEL) [#34931](https://github.com/nodejs/node/pull/34931)
- [[`a08e853edc`](https://github.com/nodejs/node/commit/a08e853edc)] - **doc**: spruce up user journey to local docs browsing (Derek Lewis) [#34986](https://github.com/nodejs/node/pull/34986)
- [[`83a3e3b681`](https://github.com/nodejs/node/commit/83a3e3b681)] - **doc**: update syntax highlighting color for accessibility (Rich Trott) [#35063](https://github.com/nodejs/node/pull/35063)
- [[`5bd0e0803d`](https://github.com/nodejs/node/commit/5bd0e0803d)] - **doc**: fix incorrect URL in cli.md (Rich Trott) [#35043](https://github.com/nodejs/node/pull/35043)
- [[`28e89f6766`](https://github.com/nodejs/node/commit/28e89f6766)] - **doc**: remove style for empty links (Antoine du HAMEL) [#35034](https://github.com/nodejs/node/pull/35034)
- [[`cdc1198a62`](https://github.com/nodejs/node/commit/cdc1198a62)] - **doc**: fix certificate display in tls doc (Rich Trott) [#35032](https://github.com/nodejs/node/pull/35032)
- [[`72d03cd802`](https://github.com/nodejs/node/commit/72d03cd802)] - **doc**: remove duplicate error code entry (Rich Trott) [#35031](https://github.com/nodejs/node/pull/35031)
- [[`680782ea64`](https://github.com/nodejs/node/commit/680782ea64)] - **doc**: use consistent header typography (Rich Trott) [#35030](https://github.com/nodejs/node/pull/35030)
- [[`1ae674c67a`](https://github.com/nodejs/node/commit/1ae674c67a)] - **doc**: fix malformed hashes in assert.md (Rich Trott) [#35028](https://github.com/nodejs/node/pull/35028)
- [[`c3a3cb69aa`](https://github.com/nodejs/node/commit/c3a3cb69aa)] - **doc**: fix a typo of microtaskMode (Shigma) [#34980](https://github.com/nodejs/node/pull/34980)
- [[`a846a9f116`](https://github.com/nodejs/node/commit/a846a9f116)] - **doc**: change 'be will' to 'will be' (Victory Osikwemhe) [#34999](https://github.com/nodejs/node/pull/34999)
- [[`593236ad33`](https://github.com/nodejs/node/commit/593236ad33)] - **doc**: change color contrast for accessibility (Rich Trott) [#35047](https://github.com/nodejs/node/pull/35047)
- [[`8c207c67d1`](https://github.com/nodejs/node/commit/8c207c67d1)] - **doc**: refactor deprecation anchors (Antoine du HAMEL) [#34955](https://github.com/nodejs/node/pull/34955)
- [[`cc0aaf2384`](https://github.com/nodejs/node/commit/cc0aaf2384)] - **doc**: error code fix in resolver spec (Guy Bedford) [#34998](https://github.com/nodejs/node/pull/34998)
- [[`a4201843e7`](https://github.com/nodejs/node/commit/a4201843e7)] - **doc**: use period consistently in man page (Rich Trott) [#34939](https://github.com/nodejs/node/pull/34939)
- [[`f1217d6d8b`](https://github.com/nodejs/node/commit/f1217d6d8b)] - **doc**: revise commit-queue.md (Rich Trott) [#35006](https://github.com/nodejs/node/pull/35006)
- [[`9aba579acb`](https://github.com/nodejs/node/commit/9aba579acb)] - **doc**: change effected to affected (Turner Jabbour) [#34989](https://github.com/nodejs/node/pull/34989)
- [[`2598527112`](https://github.com/nodejs/node/commit/2598527112)] - **doc**: drop the --production flag for installing windows-build-tools (DeeDeeG) [#34979](https://github.com/nodejs/node/pull/34979)
- [[`287ce7b810`](https://github.com/nodejs/node/commit/287ce7b810)] - **doc**: fix broken link to response.writableFinished in deprecations doc (Rich Trott) [#34983](https://github.com/nodejs/node/pull/34983)
- [[`a0656ff863`](https://github.com/nodejs/node/commit/a0656ff863)] - **doc**: fix broken link to response.finished in deprecations doc (Rich Trott) [#34982](https://github.com/nodejs/node/pull/34982)
- [[`f4524b8936`](https://github.com/nodejs/node/commit/f4524b8936)] - **doc**: fix broken link to writableEnded in deprecations doc (Rich Trott) [#34984](https://github.com/nodejs/node/pull/34984)
- [[`514a538f64`](https://github.com/nodejs/node/commit/514a538f64)] - **doc**: fix typos in buffer doc (Robert Williams) [#34981](https://github.com/nodejs/node/pull/34981)
- [[`df76c89b78`](https://github.com/nodejs/node/commit/df76c89b78)] - **doc**: recommend URL() over url.parse() in http2 doc (Rich Trott) [#34978](https://github.com/nodejs/node/pull/34978)
- [[`ca0302e4f1`](https://github.com/nodejs/node/commit/ca0302e4f1)] - **doc**: arrange perf_hooks entries alphabetically (Rich Trott) [#34973](https://github.com/nodejs/node/pull/34973)
- [[`94c6e09367`](https://github.com/nodejs/node/commit/94c6e09367)] - **doc**: replace require() with reference links in http2.md (Rich Trott) [#34956](https://github.com/nodejs/node/pull/34956)
- [[`2407a7a671`](https://github.com/nodejs/node/commit/2407a7a671)] - **doc**: add a note about possible missing lines to readline.asyncIterator (Igor Mikhalev) [#34675](https://github.com/nodejs/node/pull/34675)
- [[`31098a4c0e`](https://github.com/nodejs/node/commit/31098a4c0e)] - **doc**: make minor improvements to query string sentence in http2.md (Rich Trott) [#34929](https://github.com/nodejs/node/pull/34929)
- [[`1589f0e6f4`](https://github.com/nodejs/node/commit/1589f0e6f4)] - **doc**: make general copy-edit changes to policy.md (Rich Trott) [#34943](https://github.com/nodejs/node/pull/34943)
- [[`aee3b8510b`](https://github.com/nodejs/node/commit/aee3b8510b)] - **doc**: simplify "make use of" to "use" (Rich Trott) [#34861](https://github.com/nodejs/node/pull/34861)
- [[`0e09ff8ab1`](https://github.com/nodejs/node/commit/0e09ff8ab1)] - **doc**: make minor fixes to maintaining-openssl.md (Rich Trott) [#34926](https://github.com/nodejs/node/pull/34926)
- [[`b091681d25`](https://github.com/nodejs/node/commit/b091681d25)] - **doc**: fix CHANGELOG.md parsing issue (Juan José Arboleda) [#34923](https://github.com/nodejs/node/pull/34923)
- [[`fbd18be459`](https://github.com/nodejs/node/commit/fbd18be459)] - **doc**: provide more guidance about process.version (Rich Trott) [#34909](https://github.com/nodejs/node/pull/34909)
- [[`4782ec7b3b`](https://github.com/nodejs/node/commit/4782ec7b3b)] - **doc**: use consistent typography for node-addon-api (Rich Trott) [#34910](https://github.com/nodejs/node/pull/34910)
- [[`2fe95094fd`](https://github.com/nodejs/node/commit/2fe95094fd)] - **doc**: improve link-local text in dgram.md (Rich Trott) [#34868](https://github.com/nodejs/node/pull/34868)
- [[`657292e2dd`](https://github.com/nodejs/node/commit/657292e2dd)] - **doc**: fix broken markdown/display in cli.html (Rich Trott) [#34892](https://github.com/nodejs/node/pull/34892)
- [[`4cf93bb3cf`](https://github.com/nodejs/node/commit/4cf93bb3cf)] - **doc**: use "previous"/"preceding" instead of "above" as modifier (Rich Trott) [#34877](https://github.com/nodejs/node/pull/34877)
- [[`29b048b06b`](https://github.com/nodejs/node/commit/29b048b06b)] - **doc**: use links to MS guide in style guide (Rich Trott) [#34871](https://github.com/nodejs/node/pull/34871)
- [[`52be37cf39`](https://github.com/nodejs/node/commit/52be37cf39)] - **doc,tools**: remove malfunctioning Linux manpage linker (Rich Trott) [#34985](https://github.com/nodejs/node/pull/34985)
- [[`fffba3a270`](https://github.com/nodejs/node/commit/fffba3a270)] - **errors**: use `ErrorPrototypeToString` from `primordials` object (ExE Boss) [#34891](https://github.com/nodejs/node/pull/34891)
- [[`db8c66b8c2`](https://github.com/nodejs/node/commit/db8c66b8c2)] - **esm**: shorten ERR_UNSUPPORTED_ESM_URL_SCHEME message (Rich Trott) [#34836](https://github.com/nodejs/node/pull/34836)
- [[`be71e717c5`](https://github.com/nodejs/node/commit/be71e717c5)] - **meta**: enable wasi for CODEOWNERS (gengjiawen) [#34889](https://github.com/nodejs/node/pull/34889)
- [[`a43b7ff72e`](https://github.com/nodejs/node/commit/a43b7ff72e)] - **meta**: remove non-existent quic from CODEOWNERS (Richard Lau) [#34947](https://github.com/nodejs/node/pull/34947)
- [[`3c32fe09e9`](https://github.com/nodejs/node/commit/3c32fe09e9)] - **n-api**: re-implement async env cleanup hooks (Gabriel Schulhof) [#34819](https://github.com/nodejs/node/pull/34819)
- [[`fcb211f38a`](https://github.com/nodejs/node/commit/fcb211f38a)] - **net**: replace usage of internal stream state with public api (Denys Otrishko) [#34885](https://github.com/nodejs/node/pull/34885)
- [[`8aac42caf2`](https://github.com/nodejs/node/commit/8aac42caf2)] - **(SEMVER-MINOR)** **perf_hooks**: add idleTime and event loop util (Trevor Norris) [#34938](https://github.com/nodejs/node/pull/34938)
- [[`18b04ab4c8`](https://github.com/nodejs/node/commit/18b04ab4c8)] - **policy**: implement scopes field (Bradley Farias) [#34552](https://github.com/nodejs/node/pull/34552)
- [[`1bf5d1a39b`](https://github.com/nodejs/node/commit/1bf5d1a39b)] - **querystring**: manage percent character at unescape (Daijiro Wachi) [#35013](https://github.com/nodejs/node/pull/35013)
- [[`f21d78d537`](https://github.com/nodejs/node/commit/f21d78d537)] - **src**: shutdown libuv before exit() (Anna Henningsen) [#35021](https://github.com/nodejs/node/pull/35021)
- [[`789798bedf`](https://github.com/nodejs/node/commit/789798bedf)] - **src**: add get/set pair for env context awareness (Shelley Vohr) [#35024](https://github.com/nodejs/node/pull/35024)
- [[`73ef3f2f05`](https://github.com/nodejs/node/commit/73ef3f2f05)] - **src**: disallow JS execution during exit() (Anna Henningsen) [#35020](https://github.com/nodejs/node/pull/35020)
- [[`f6a5999a9d`](https://github.com/nodejs/node/commit/f6a5999a9d)] - **src,doc**: fix wording to refer to context, not environment (Turner Jabbour) [#34880](https://github.com/nodejs/node/pull/34880)
- [[`bcc1d431f8`](https://github.com/nodejs/node/commit/bcc1d431f8)] - **src,doc**: fix grammar due to missing 'is' (Turner Jabbour) [#34897](https://github.com/nodejs/node/pull/34897)
- [[`044297ff10`](https://github.com/nodejs/node/commit/044297ff10)] - **src,doc**: rephrase for clarity (Turner Jabbour) [#34879](https://github.com/nodejs/node/pull/34879)
- [[`4bb40078da`](https://github.com/nodejs/node/commit/4bb40078da)] - **(SEMVER-MINOR)** **stream**: simpler and faster Readable async iterator (Robert Nagy) [#34035](https://github.com/nodejs/node/pull/34035)
- [[`ffae5f3809`](https://github.com/nodejs/node/commit/ffae5f3809)] - **(SEMVER-MINOR)** **stream**: save error in state (Robert Nagy) [#34103](https://github.com/nodejs/node/pull/34103)
- [[`5f24cea11a`](https://github.com/nodejs/node/commit/5f24cea11a)] - **stream**: fix Readable stream state properties (Denys Otrishko) [#34886](https://github.com/nodejs/node/pull/34886)
- [[`f537c868b9`](https://github.com/nodejs/node/commit/f537c868b9)] - **stream**: allow using `.push()`/`.unshift()` during `once('data')` (Anna Henningsen) [#34957](https://github.com/nodejs/node/pull/34957)
- [[`4d533858cf`](https://github.com/nodejs/node/commit/4d533858cf)] - **test**: make .out checks embedder-friendly (Shelley Vohr) [#35040](https://github.com/nodejs/node/pull/35040)
- [[`a756b92c4a`](https://github.com/nodejs/node/commit/a756b92c4a)] - **test**: use mustCall() in test-http-timeout (Pooja D.P) [#34996](https://github.com/nodejs/node/pull/34996)
- [[`9011c87c1c`](https://github.com/nodejs/node/commit/9011c87c1c)] - **test**: change var to let (Pooja D.P) [#34902](https://github.com/nodejs/node/pull/34902)
- [[`b698d2ec81`](https://github.com/nodejs/node/commit/b698d2ec81)] - **test**: remove incorrect debug() in test-policy-integrity (Rich Trott) [#34961](https://github.com/nodejs/node/pull/34961)
- [[`ee6a583b9f`](https://github.com/nodejs/node/commit/ee6a583b9f)] - **test**: fix typo in test/parallel/test-icu-punycode.js (Daijiro Wachi) [#34934](https://github.com/nodejs/node/pull/34934)
- [[`9057a1644d`](https://github.com/nodejs/node/commit/9057a1644d)] - **test**: add readline test for escape sequence (Rich Trott) [#34952](https://github.com/nodejs/node/pull/34952)
- [[`75d16125e1`](https://github.com/nodejs/node/commit/75d16125e1)] - **test**: make test-tls-reuse-host-from-socket pass without internet (Rich Trott) [#34953](https://github.com/nodejs/node/pull/34953)
- [[`971b7ac087`](https://github.com/nodejs/node/commit/971b7ac087)] - **test**: simplify test-vm-memleak (Rich Trott) [#34881](https://github.com/nodejs/node/pull/34881)
- [[`577978a96c`](https://github.com/nodejs/node/commit/577978a96c)] - **tools**: fix docopen target (Antoine du HAMEL) [#35062](https://github.com/nodejs/node/pull/35062)
- [[`2b445bb3ee`](https://github.com/nodejs/node/commit/2b445bb3ee)] - **tools**: fix doc build targets (Antoine du HAMEL) [#35060](https://github.com/nodejs/node/pull/35060)
- [[`3d41ff25b7`](https://github.com/nodejs/node/commit/3d41ff25b7)] - **tools**: add banner to lint-md.js by rollup.config.js (KuthorX) [#34233](https://github.com/nodejs/node/pull/34233)
- [[`62cc3b8249`](https://github.com/nodejs/node/commit/62cc3b8249)] - **tools**: update ESLint to 7.8.1 (cjihrig) [#35004](https://github.com/nodejs/node/pull/35004)
- [[`c47d319ac6`](https://github.com/nodejs/node/commit/c47d319ac6)] - **tools**: update ESLint to 7.8.0 (cjihrig) [#35004](https://github.com/nodejs/node/pull/35004)
- [[`b6f3ae8ffc`](https://github.com/nodejs/node/commit/b6f3ae8ffc)] - **tools,doc**: allow page titles to contain inline code (Antoine du HAMEL) [#35003](https://github.com/nodejs/node/pull/35003)
- [[`fb2111e300`](https://github.com/nodejs/node/commit/fb2111e300)] - **tools,doc**: fix global table of content active element (Antoine du Hamel) [#34976](https://github.com/nodejs/node/pull/34976)
- [[`7ad629e4e4`](https://github.com/nodejs/node/commit/7ad629e4e4)] - **tools,doc**: remove "toc" anchor name (Rich Trott) [#34893](https://github.com/nodejs/node/pull/34893)
- [[`94528f510e`](https://github.com/nodejs/node/commit/94528f510e)] - **zlib**: replace usage of internal stream state with public api (Denys Otrishko) [#34884](https://github.com/nodejs/node/pull/34884)

Windows 32-bit Installer: https://nodejs.org/dist/v14.10.0/node-v14.10.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v14.10.0/node-v14.10.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v14.10.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v14.10.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v14.10.0/node-v14.10.0.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v14.10.0/node-v14.10.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v14.10.0/node-v14.10.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v14.10.0/node-v14.10.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v14.10.0/node-v14.10.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v14.10.0/node-v14.10.0-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v14.10.0/node-v14.10.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v14.10.0/node-v14.10.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v14.10.0/node-v14.10.0.tar.gz \
Other release files: https://nodejs.org/dist/v14.10.0/ \
Documentation: https://nodejs.org/docs/v14.10.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

95947c76445850e23d47a50b2934f03736d88fc7b82d4c99d038500c0e29d8e5  node-v14.10.0-aix-ppc64.tar.gz
8cf72422fb268ecf3bb72c66a61ccf2afb7b8ff358d09b3568d762ea281c86ed  node-v14.10.0-darwin-x64.tar.gz
b1307110602a5b53e5ad7f788797a799f122a0145b6078f86a410f93973e01f7  node-v14.10.0-darwin-x64.tar.xz
d165225fc881dfb70b54806b03913ae282da1ee33003de2fec7b235dbfa7fff8  node-v14.10.0-headers.tar.gz
418cf2d3c98d05f018dfd658a7a369c1e18a0a2c204ebdb550e8b975ad7787d2  node-v14.10.0-headers.tar.xz
842811feed3177bef73b16b24e2b2d2b27f6223ea65da6a397d86b670fd35766  node-v14.10.0-linux-arm64.tar.gz
3fc62b875246f9b88b0c4f1cf066a737bb1ae9fd48728d1c038c52d9ff8a84bd  node-v14.10.0-linux-arm64.tar.xz
9972c3ec4782abd0da263b59e63d513d3783286251fc01160dbfe183d3b2d2f7  node-v14.10.0-linux-armv7l.tar.gz
021a1f62185f3439a22dfa1b97e2dd2bedf9a8f313e59a93ed01fa8235b5d1ce  node-v14.10.0-linux-armv7l.tar.xz
5d9e99c90c6bd296bf8853afa469b5b5e67c1b5a4a3075412daf5b15f46e36cd  node-v14.10.0-linux-ppc64le.tar.gz
ea765592c5a3b07aaeff3a244d29c9728d0491ab51f13a775c814fcdf0ddabc7  node-v14.10.0-linux-ppc64le.tar.xz
a8d4f9b4e75882a43b071eb8fb375fe99920b26fcd492e7873d1c3e69deea6cb  node-v14.10.0-linux-s390x.tar.gz
18fe5b64c88324fd1a291ee9bfcf88859ff051fcb9eaf65c7ca0034d95b4169f  node-v14.10.0-linux-s390x.tar.xz
4a91b15ae7e8bdb7594211ef0e6a4733f06e97fe9a54711805321b21142f02fc  node-v14.10.0-linux-x64.tar.gz
5cadd646f6cfa153b6091454047779314c1ef6c2a0e46bbb705cd78a16589482  node-v14.10.0-linux-x64.tar.xz
322e3c58c64e76c376a00ab5fea2c724ea7e8421dbab9c10298eebf3d91734a7  node-v14.10.0.pkg
7e0d7a1aa23697415e3588a1ca4f1c47496e6c88b9cf37c66be90353d3e4ac3e  node-v14.10.0.tar.gz
431b78c598017d30e7cffd534c6c3494df383b33f4fcbe4c16415e3ea8b8c701  node-v14.10.0.tar.xz
a06cdebcb9c0b5cf87600b6fcd2384ddee402ea7d7f9b921f4924c2fc94a9d89  node-v14.10.0-win-x64.7z
302a8f3336d16ef365a203f476f6e4c80f39e47988c4b80b1d76291d16bcb7b5  node-v14.10.0-win-x64.zip
d4e4f30c7d96f9fe0ad18ea2abd3e8108386a2c300b3726ad76b0869846217c8  node-v14.10.0-win-x86.7z
e93a3bb9805a11d745ed01ecaa9f65222904a6e5273f3248e6f5f9e227474943  node-v14.10.0-win-x86.zip
c1b9bad7e1029de8b1f05167760d4f22b2105f828aeb3e763dfa4618ae0eb882  node-v14.10.0-x64.msi
17b8290524752b512d88f285047335e8bf8685f3e338f6addf3051a1f0c73196  node-v14.10.0-x86.msi
1540ab6593d2ad6029e9b0c1c981e70282250b292f9603a565be70a97207951f  win-x64/node.exe
a1968370c38c54d5eb66996c077c6b450cb8c3abde37b75148fe5f0ca632c27e  win-x64/node.lib
61cf65542b9e425c296b49da33121b5d19ba4bb341d14934b539738c746695a9  win-x64/node_pdb.7z
4d77c5f49cc3bbcb24e45014fc7180cf09669b057b5d04584931992d0cc894dd  win-x64/node_pdb.zip
268a1e094b764962d41b875cde52ee7ee2b74a3b4fac080e967f3d885d0c0824  win-x86/node.exe
96d8a893d60b725a41477f9d5448adaf84d1be56ab8c05ca32605ffcf6c65c48  win-x86/node.lib
97ab01e62a48d6244a4a7970aacbc65ac6282d62d69a54036162a1b0be41d334  win-x86/node_pdb.7z
93710120572fcdbad990e3f2d2a8c9af4bab6d5495bd5a6458da5295ca661095  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----
Version: GnuPG v2.0.22 (GNU/Linux)

iQIcBAEBCAAGBQJfV6aLAAoJEMQ87EXBerk8+ywP/3Nb8lOFd2FoyG1txoMir/wH
cW/sLPQLVaJFm2Aqbb19wwbR1xnAJEm/s3e2h9ENSyyy7YEQoYB6fHn47SnDvBvu
iY2mr8Pbqu08ywJcx+wriOLQSGcYFNrDaCAcRyEn4c5GEI6kx1P0dhUi+2lFiDud
HN6qatYCbMy1Voe+eaSnHbi911QLFrrvK+Ir0QaAwDLJGrdu36fD0kT2NGyMl7Lm
tkI01LenbghyGqKLUjcM8bb8iv6lqtd01NxnFzzwuMoW8gsH+4l8ehcLmQyeTidT
Eu448WhG9KXYQt2DIJAjlzYInoWNovlTNY6KydhTQExUHs1pmAU0L8Ir0lzH+qZv
Fvck5vyA97Y9mpTZXJHsH3hmcqxeLOUnjTpk7T8M7PCjDN6QD3Ca+EoKEM0Gtq0s
YUcKEH6XXa8km+PeEJkymoRl+B4InBaqDvS0C6MTHGFDdcwL25zLpuOgaF3iPXDS
ghdnWGahNbVDVyqZxymAZO9r2AA+RdpMzlGmQk77IP5XPdarVFbJjA3MXeE4rQfy
VFPLA22AgklbqduNGj96CcnBr0d9dKTc7P37AgEof02Kt5pMNmVvXwSMEFrQKs5l
14bn1cbLAnnDUHazYUeIq2SRzbQUErDEH3X7zAarCkjabFKjWpdKGHvP/V+ULzCg
aSCKRwgXwwnqeUjntQ4l
=Jn6b
-----END PGP SIGNATURE-----

```
