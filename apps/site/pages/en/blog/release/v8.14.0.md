---
date: '2018-11-28T00:42:12.777Z'
category: release
title: Node v8.14.0 (LTS)
layout: blog-post
author: <PERSON>
---

**This is a security release.** All Node.js users should consult the security release summary at /blog/vulnerability/november-2018-security-releases/ for details on patched vulnerabilities.

Fixes for the following CVEs are included in this release:

- Node.js: Denial of Service with large HTTP headers (CVE-2018-12121)
- Node.js: Slowloris HTTP Denial of Service (CVE-2018-12122 / Node.js)
- Node.js: Hostname spoofing in URL parser for javascript protocol (CVE-2018-12123)
- Node.js: HTTP request splitting (CVE-2018-12116)
- OpenSSL: Timing vulnerability in DSA signature generation (CVE-2018-0734)
- OpenSSL: Microarchitecture timing vulnerability in ECC scalar multiplication (CVE-2018-5407)

### Notable Changes

- **deps**: Upgrade to OpenSSL 1.0.2q, fixing CVE-2018-0734 and CVE-2018-5407
- **http**:
  - Headers received by HTTP servers must not exceed 8192 bytes in total to prevent possible Denial of Service attacks. Reported by <PERSON>. (CVE-2018-12121 / <PERSON>)
  - A timeout of 40 seconds now applies to servers receiving HTTP headers. This value can be adjusted with `server.headersTimeout`. Where headers are not completely received within this period, the socket is destroyed on the next received chunk. In conjunction with `server.setTimeout()`, this aids in protecting against excessive resource retention and possible Denial of Service. Reported by Jan Maybach ([liebdich.com](https://liebdich.com)). (CVE-2018-12122 / Matteo Collina)
  - Two-byte characters are now strictly disallowed for the `path` option in HTTP client requests. Paths containing characters outside of the range `\u0021` - `\u00ff` will now be rejected with a `TypeError`. This behavior can be reverted if necessary by supplying the `--security-revert=CVE-2018-12116` command line argument (this is not recommended). Reported as security concern for Node.js 6 and 8 by [Arkadiy Tetelman](https://twitter.com/arkadiyt) ([Lob](https://lob.com)), fixed by backporting a change by Benno Fünfstück applied to Node.js 10 and later. (CVE-2018-12116 / Matteo Collina)
- **url**: Fix a bug that would allow a hostname being spoofed when parsing URLs with `url.parse()` with the `'javascript:'` protocol. Reported by [Martin Bajanik](https://twitter.com/_bayotop) ([Kentico](https://kenticocloud.com/)). (CVE-2018-12123 / Matteo Collina)

### Commits

- [[`add20f373c`](https://github.com/nodejs/node/commit/add20f373c)] - **deps**: add -no_rand_screen to openssl s_client (Shigeki Ohtsu) [nodejs/node#1836](https://github.com/nodejs/node/pull/1836)
- [[`c4e382cce3`](https://github.com/nodejs/node/commit/c4e382cce3)] - **deps**: fix asm build error of openssl in x86_win32 (Shigeki Ohtsu) [nodejs/node#1389](https://github.com/nodejs/node/pull/1389)
- [[`f1d1f12519`](https://github.com/nodejs/node/commit/f1d1f12519)] - **deps**: fix openssl assembly error on ia32 win32 (Fedor Indutny) [nodejs/node#1389](https://github.com/nodejs/node/pull/1389)
- [[`69037ad5c4`](https://github.com/nodejs/node/commit/69037ad5c4)] - **deps**: copy all openssl header files to include dir (Sam Roberts) [#24530](https://github.com/nodejs/node/pull/24530)
- [[`f5b34336bb`](https://github.com/nodejs/node/commit/f5b34336bb)] - **deps**: upgrade openssl sources to 1.0.2q (Sam Roberts) [#24530](https://github.com/nodejs/node/pull/24530)
- [[`93dba83fb0`](https://github.com/nodejs/node/commit/93dba83fb0)] - **deps,http**: http_parser set max header size to 8KB (Matteo Collina) [nodejs-private/node-private#143](https://github.com/nodejs-private/node-private/pull/143)
- [[`576038fb61`](https://github.com/nodejs/node/commit/576038fb61)] - **(SEMVER-MINOR)** **http**: add --security-revert for CVE-2018-12116 (Matteo Collina) [nodejs-private/node-private#146](https://github.com/nodejs-private/node-private/pull/146)
- [[`513e9747a2`](https://github.com/nodejs/node/commit/513e9747a2)] - **(SEMVER-MINOR)** **http**: disallow two-byte characters in URL path (Benno Fünfstück) [nodejs-private/node-private#146](https://github.com/nodejs-private/node-private/pull/146)
- [[`696f063c5e`](https://github.com/nodejs/node/commit/696f063c5e)] - **(SEMVER-MINOR)** **http,https**: protect against slow headers attack (Matteo Collina) [nodejs-private/node-private#151](https://github.com/nodejs-private/node-private/pull/151)
- [[`7f362a11ee`](https://github.com/nodejs/node/commit/7f362a11ee)] - **openssl**: fix keypress requirement in apps on win32 (Shigeki Ohtsu) [nodejs/node#1389](https://github.com/nodejs/node/pull/1389)
- [[`53a6e4eb20`](https://github.com/nodejs/node/commit/53a6e4eb20)] - **url**: avoid hostname spoofing w/ javascript protocol (Matteo Collina) [nodejs-private/node-private#145](https://github.com/nodejs-private/node-private/pull/145)

Windows 32-bit Installer: https://nodejs.org/dist/v8.14.0/node-v8.14.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v8.14.0/node-v8.14.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v8.14.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v8.14.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v8.14.0/node-v8.14.0.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v8.14.0/node-v8.14.0-darwin-x64.tar.gz \
Linux 32-bit Binary: https://nodejs.org/dist/v8.14.0/node-v8.14.0-linux-x86.tar.xz \
Linux 64-bit Binary: https://nodejs.org/dist/v8.14.0/node-v8.14.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v8.14.0/node-v8.14.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v8.14.0/node-v8.14.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v8.14.0/node-v8.14.0-aix-ppc64.tar.gz \
SmartOS 32-bit Binary: https://nodejs.org/dist/v8.14.0/node-v8.14.0-sunos-x86.tar.xz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v8.14.0/node-v8.14.0-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v8.14.0/node-v8.14.0-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v8.14.0/node-v8.14.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v8.14.0/node-v8.14.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v8.14.0/node-v8.14.0.tar.gz \
Other release files: https://nodejs.org/dist/v8.14.0/ \
Documentation: https://nodejs.org/docs/v8.14.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

3897b7ad086751be72eebeafab5c5f84f361135748dd2cfb542046e990561adf  node-v8.14.0-aix-ppc64.tar.gz
1e9bb233bb3c3b01826f9d7e1b3ecf1047840ce96a3a7d1921ddcb569c467329  node-v8.14.0-darwin-x64.tar.gz
d005440bde4d0336f6649153f4ea41a7b8e0e2d0faa4be39f7bd214d40d0cd15  node-v8.14.0-darwin-x64.tar.xz
2b850583b121a7a9bb0c8c65e7b90e83e8e51fa2b10f795c51a38fccb915b021  node-v8.14.0-headers.tar.gz
5c96a0f614777b20cf6b3d31d8a948b264fd05e5fb36a01a6d427443fef95ca5  node-v8.14.0-headers.tar.xz
ce522ad9331428195899ff3f94d23592aadc7d7752eaee0bf35607fa6df24501  node-v8.14.0-linux-arm64.tar.gz
fbcef89a60f1f40699589850f861fc84354a6f240610e2726c3743455dd82525  node-v8.14.0-linux-arm64.tar.xz
e0a65e56cb241a503c4eae8eb29943d8f949fafb7fcdc152a08caff0b5541ecd  node-v8.14.0-linux-armv6l.tar.gz
b7b42d0c74588f6edb1f1001ee14643c7577ab3fbfc0bbdd121a16629396a63b  node-v8.14.0-linux-armv6l.tar.xz
f935630482134b2414a5dec8b51f2d429b73a9cf3a961760488153b83f9b0b9d  node-v8.14.0-linux-armv7l.tar.gz
11275cb156c24bc76596c3360d87c0738edd6f14dc0f84daae701b80186781d2  node-v8.14.0-linux-armv7l.tar.xz
eeed73ddadaa7f63ac4e24bd14267d09b8615bab665eeae84ca2f1282f452a70  node-v8.14.0-linux-ppc64le.tar.gz
0e4c8cfd0f8584ad099adee80e371597791acf343ebf19c27c9d9f92495b58aa  node-v8.14.0-linux-ppc64le.tar.xz
31c8a9da5169c281bef000d9124e98c8ad1a5d6dfea8eb8a636a92a7e90f0c86  node-v8.14.0-linux-s390x.tar.gz
e24e9b98fd98b081f511dd465835650fda338a1a08b42f2e81e2e3fd1864bac3  node-v8.14.0-linux-s390x.tar.xz
bbf81603a924bf86c64da520f6b2a923e6f78e987bb36a58bdb8ff2606d7f995  node-v8.14.0-linux-x64.tar.gz
a56d1af4d7da81504338b09809cf10b3144808d47d4117b9bd9a5a4ec4d5d9b9  node-v8.14.0-linux-x64.tar.xz
2b867f310a7ef2ac082eb920276f710499c106db3aa90f8ef2ceb66489152182  node-v8.14.0-linux-x86.tar.gz
1e73f218dcedb50d602a2c59ba1976073505f44e23ec72360be68c1a2587f672  node-v8.14.0-linux-x86.tar.xz
9899a365e2534e60b518e5feb0ff918fbea9953e789cbb2ecd7e58c95600ec5c  node-v8.14.0.pkg
6f529d9ec2d9ed3e6df472f73f16a096f2c4a1329d716f61baf3b2a1be622a12  node-v8.14.0-sunos-x64.tar.gz
2d1daf3b6b83cae4a39637e0782462b226781c74c3edfdb25d759b9ec5e33886  node-v8.14.0-sunos-x64.tar.xz
67579cf81eff5fcd75331a7c040add6283b90f35e35bfce85c1276dca0f07810  node-v8.14.0-sunos-x86.tar.gz
186c79f4a9df6adeb7a316e226bedecfe14f09f00d1fd5118d7fe4a4626c370d  node-v8.14.0-sunos-x86.tar.xz
c49f4d2223be9f2d2d73a131e9a25d9668b7ec2c1319d28c3c3658ff503b720c  node-v8.14.0.tar.gz
8ce252913c9f6aaa9871f2d9661b6e54858dae2f0064bd3c624676edb09083c4  node-v8.14.0.tar.xz
fc4770b0b53e8d8abe5b1c5cb86a21f5009eb812965894728a79c5bb81bce268  node-v8.14.0-win-x64.7z
d0be7c96a25c5d2b69f8a3510e9f4414643d5fe361b4509d455249e57f9a50af  node-v8.14.0-win-x64.zip
b169de9eca5240599ac74aaa2a8046fc87602126f006057eace9ba3d810590d5  node-v8.14.0-win-x86.7z
011e4c762d637561515cb527dc3bc1cf211c416af940c7c624466d28ccbec843  node-v8.14.0-win-x86.zip
a6ff914df561dd0bc45b6e6ca0165a4890c342a7db6897e1f40cd50e0f71c043  node-v8.14.0-x64.msi
8aa6db66c83aeb978f9edf8cbff2948062679578c6bb0c0c42b4db419358d91c  node-v8.14.0-x86.msi
2e96ef23b65b4a50ce14c723848ba3f499d667498c0ddf92f83bf7a6a16d3b09  win-x64/node.exe
95cff2087b729a783ec464f6397cc28cc50bc25482e97ecde78f7c4785c7db64  win-x64/node.lib
8900c9ddfa2b0bd564436658e19b1a753a31e1fb84b64510b7f165b5f8b1ade4  win-x64/node_pdb.7z
ad5d4a8f93fc27fee7a63d7292e1d30696f376279cfeb435e89b1f71fb041d9f  win-x64/node_pdb.zip
54ffc91c4cd37bc3e864293b5ddae0c1f48658ac9d715e626c7b85cd1eeea1d9  win-x86/node.exe
c453249389bca50c7572f59e67e6e675cc91fdec8ab637cafdbfdd92207a7d21  win-x86/node.lib
9667660fe5dcdc989e8af4e8a6dedaeb4f3c39750a838d3da2e3e82bd66d4d51  win-x86/node_pdb.7z
c7d14f4fc8fa46fc2cd0b34160ef37570b3b1c6f49e23acaa4d5564abf9aa2a8  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQEzBAEBCAAdFiEE3Y8jOLrnUB491ax4wnN5L32DVF0FAlv9490ACgkQwnN5L32D
VF1mVwgA1WyvpkAkUlO3dxRzTWGNZS21f2mzDlH1sjnqY015g9TkFDxbPqZbLd3C
3G7IuWhiGNAAXVeGN5j9QsouaRkDJqRsu/Xg+/PFUxq5jZwJp3j7MN9so3Q33d77
qRaUpkKATIMVvgm1xbDT1LOQDycVJ7fiynrbcKzdC8gZsjgwDu7UHQfab3o++hJR
E5OhHyuPiXSuf5sX8rpBeN0jNnFFze110F8ef/k9h5Wvq82GEleCBgzrW93H3pBp
tycxYW2PIEVEuNrgUsIK1UwgHy/NYRaJTP9sfABwYcrINLXPVYCl9NVKOCHeCs5n
8vtSbfxlD9FY8DhIjJ0rsNPjhPrtGQ==
=XGLg
-----END PGP SIGNATURE-----

```
