---
date: '2021-02-09T16:54:12.127Z'
category: release
title: Node v14.15.5 (LTS)
layout: blog-post
author: <PERSON>
---

### Notable Changes

- **deps**:
  - upgrade npm to 6.14.11 (<PERSON><PERSON>) [#37173](https://github.com/nodejs/node/pull/37173)
  - V8: backport dfcf1e86fac0 (<PERSON><PERSON><PERSON>) [#37245](https://github.com/nodejs/node/pull/37245)
    - **Note**: Node.js is not believed to be vulnerable to CVE-2021-21148.
- **stream,zlib**: do not use \_stream\_\* anymore (<PERSON>) [#36618](https://github.com/nodejs/node/pull/36618)

### Commits

- [[`20b1e6c802`](https://github.com/nodejs/node/commit/20b1e6c802)] - **deps**: V8: backport dfcf1e86fac0 (<PERSON><PERSON><PERSON>) [#37245](https://github.com/nodejs/node/pull/37245)
- [[`408c7a65f3`](https://github.com/nodejs/node/commit/408c7a65f3)] - **deps**: upgrade npm to 6.14.11 (Ruy Adorno) [#37173](https://github.com/nodejs/node/pull/37173)
- [[`017eed665b`](https://github.com/nodejs/node/commit/017eed665b)] - **http**: do not loop over prototype in Agent (Michaël Zasso) [#36410](https://github.com/nodejs/node/pull/36410)
- [[`25a3204fe2`](https://github.com/nodejs/node/commit/25a3204fe2)] - **http**: don't cork .end when not needed (Dimitris Halatsis) [#36633](https://github.com/nodejs/node/pull/36633)
- [[`2a1e4e9244`](https://github.com/nodejs/node/commit/2a1e4e9244)] - **stream**: accept iterable as a valid first argument (ZiJian Liu) [#36479](https://github.com/nodejs/node/pull/36479)
- [[`9ff73fcdbe`](https://github.com/nodejs/node/commit/9ff73fcdbe)] - **stream,zlib**: do not use \_stream\_\* anymore (Matteo Collina) [#36618](https://github.com/nodejs/node/pull/36618)
- [[`c03cddb46f`](https://github.com/nodejs/node/commit/c03cddb46f)] - **test**: http complete response after socket double end (Dimitris Halatsis) [#36633](https://github.com/nodejs/node/pull/36633)
- [[`f206505e9d`](https://github.com/nodejs/node/commit/f206505e9d)] - **util**: fix instanceof checks with null prototypes during inspection (Ruben Bridgewater) [#36178](https://github.com/nodejs/node/pull/36178)
- [[`2f7944b18b`](https://github.com/nodejs/node/commit/2f7944b18b)] - **util**: fix module prefixes during inspection (Ruben Bridgewater) [#36178](https://github.com/nodejs/node/pull/36178)

Windows 32-bit Installer: https://nodejs.org/dist/v14.15.5/node-v14.15.5-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v14.15.5/node-v14.15.5-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v14.15.5/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v14.15.5/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v14.15.5/node-v14.15.5.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v14.15.5/node-v14.15.5-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v14.15.5/node-v14.15.5-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v14.15.5/node-v14.15.5-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v14.15.5/node-v14.15.5-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v14.15.5/node-v14.15.5-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v14.15.5/node-v14.15.5-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v14.15.5/node-v14.15.5-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v14.15.5/node-v14.15.5.tar.gz \
Other release files: https://nodejs.org/dist/v14.15.5/ \
Documentation: https://nodejs.org/docs/v14.15.5/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

cc7dc8f702ab9fe143e4d9cfce33331f630b5ce5df9b76e4353b91fe7d5075ee  node-v14.15.5-aix-ppc64.tar.gz
78e2a63c54f0d3e22f0b3d29a832d0379406a619f1107d6e74679a1e76a132b0  node-v14.15.5-darwin-x64.tar.gz
278d4827988c3d32e39666279b133faa74eaf3f264440b62c28b1c798c6a83f2  node-v14.15.5-darwin-x64.tar.xz
adaa32ed32106f7a354ab6ad32bf7552df6ee99a148f3975bf71d6a10bb0c006  node-v14.15.5-headers.tar.gz
8d479b58252ad765f0972a4a73b9a12e9fa7399d5261b4599f76dbbd1adc6ffa  node-v14.15.5-headers.tar.xz
a1fb6f28041198971fd95e58638c30565de4ee74bd13db7f30ffd1d1c6820599  node-v14.15.5-linux-arm64.tar.gz
765204443777ebab13df6491f3fbd14cb64b7741dd7a697f4a74e4c2cb8bac0f  node-v14.15.5-linux-arm64.tar.xz
c2d5429b6d3c6e887d9fa72cb07485e6180177747b6bbb1074f3d3789694c378  node-v14.15.5-linux-armv7l.tar.gz
9524a54026e6b6bf111e0dfbb6e8f15903dd8517b7380fb22ed259ae759042d8  node-v14.15.5-linux-armv7l.tar.xz
0f5b26fb3913f62aacf4827c46b409fcec4c91479292ab094a319ccc975778e4  node-v14.15.5-linux-ppc64le.tar.gz
414660201f8c3653ee4df04ee69f88b2e2edb9ea9128e4efb53fdb300e24e4ab  node-v14.15.5-linux-ppc64le.tar.xz
13ea1f04870b2c1f7f6fbf5ecc31a972f79c016b8fef59e1233393d460293a3a  node-v14.15.5-linux-s390x.tar.gz
b1a79cd66a0653e4756fca76355869b32cc3f436f734d0fa42c31b2da0d274d3  node-v14.15.5-linux-s390x.tar.xz
e30c1fd4807fba052c209d7577bb6b63b5096d67c1b9ac753b9d502fda43ded9  node-v14.15.5-linux-x64.tar.gz
fa198afa9a2872cde991c3aa71796894bf7b5310d6eb178c3eafcf66e3ae79a7  node-v14.15.5-linux-x64.tar.xz
58ad4a08da1b7fc633a12ccd03efe3cc62b4bbba906dc418a70b0e3fe08e4b13  node-v14.15.5.pkg
043f39bb43ecaca699a21a8bd65d064ef53b48bf2cc6acaddb5ee0f4227e4d2a  node-v14.15.5.tar.gz
e19b1d40e958fe30c224f5a67af4ee4081e7f9d6fb586fb4bbc8d94aab39655b  node-v14.15.5.tar.xz
a78954324217009b42b09a2b0c7c512ef6dbb068cc61c822d2519c5cf7a3df8a  node-v14.15.5-win-x64.7z
e691461adf7cac8b8b21da6e3a80d1bfe69d57e51ddc1377504302fcef4d7a69  node-v14.15.5-win-x64.zip
f52153563487716b0a9a22d192378a446432e86b76d06aaa9797df9c3c029912  node-v14.15.5-win-x86.7z
50b3a92691dce913e16d8a486e7f328f5e2c625c90d041c83fa5569b8b23e148  node-v14.15.5-win-x86.zip
b53cbf58663d31fefaee7f6439287ef07d55a9ab4dac6f2ac7182cd85d090170  node-v14.15.5-x64.msi
29475fa79b845e700bde0d1a2704b79e10bc40c7c22b587bdc174215a748afe9  node-v14.15.5-x86.msi
89fbfa6627d40fac95410e2386634ebd8aafc069c3396f4ff0cb84d5a4ae400c  win-x64/node.exe
340890391521b66afabeddc9aba73bfa44b5c21f38614f2613a56c681415e903  win-x64/node.lib
a6329b004b5023aab2cf8d4f0f3e503e9fd221c4ebe3300acdfcdf0bee18c076  win-x64/node_pdb.7z
a4ec3525ca81585f909df594d02bb223c82590e149f689a0ee98dd71eed5fb2a  win-x64/node_pdb.zip
f1801dbcde563d2e88084dc2f6264b9b47bbce8a76b4bb6eb05e92156b301ee7  win-x86/node.exe
79478c3af7538b3585c201d565d5de8b2361f7e2482354b25f9b5449f302798a  win-x86/node.lib
c94c8eef210a69818ea156b993b772994eb1c6bb1aa2b7393f11e17f18e55cd2  win-x86/node_pdb.7z
74272d1e71af0635ab592077c986fa51df85b40959c7b9bc84b4e082df573690  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQEzBAEBCAAdFiEETtd49TnjY0x3nIfG1wYoSKGrAFwFAmAivgYACgkQ1wYoSKGr
AFwupQf9H68qjEF02U6upnfNOl8HxZRINwayd7DrbGJ3cyhfxAUU5zrJFyv0eRXo
PiRpH/1BHQH0/fZC5WJ9ChXKn5SN+Ga+MYUz4n0gsbOG/nW0IPf7tbSssrbAgthY
k9G0GDCcFY63FCrPliub2xtQU9hFfMWVvOfNNqP292UWuTCnw2tT7uFS5WdeahUd
pJrztP+z1p0ZyOybBs9fZQNb/WZDjYenALF8V9ITU5TcXPys4yt2tnnNS/btIYey
3GsYLOd1u2IsZuhPo4NYxQlT8VT7Tagwh7wtBRhtw+jwvTOkIZ6on/GHyRJXmIi4
q2kvyBUaTEQFX3QDR8jTqzCQS2X+CA==
=LglY
-----END PGP SIGNATURE-----

```
