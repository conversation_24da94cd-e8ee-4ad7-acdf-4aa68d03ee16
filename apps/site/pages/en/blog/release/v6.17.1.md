---
date: '2019-04-03T19:48:27.606Z'
category: release
title: Node v6.17.1 (LTS)
layout: blog-post
author: <PERSON>
---

Node 6 is due to go End-of-Life on 2019-04-30 - see [Release Schedule](https://github.com/nodejs/release#release-schedule).

### Notable Changes

- **http**:
  - fix error check in `Execute()` (<PERSON>) [#25939](https://github.com/nodejs/node/pull/25939)

### Commits

- [[`c9d21a0c10`](https://github.com/nodejs/node/commit/c9d21a0c10)] - **build**: set `-blibpath:` for AIX (<PERSON>) [#25447](https://github.com/nodejs/node/pull/25447)
- [[`9ba5fd6bad`](https://github.com/nodejs/node/commit/9ba5fd6bad)] - **build**: only check REPLACEME & DEP...X for releases (<PERSON>) [#24575](https://github.com/nodejs/node/pull/24575)
- [[`1371a6f88b`](https://github.com/nodejs/node/commit/1371a6f88b)] - **doc**: simplify CODE_OF_CONDUCT.md (Rich Trott) [#23989](https://github.com/nodejs/node/pull/23989)
- [[`ad62971573`](https://github.com/nodejs/node/commit/ad62971573)] - **doc**: document that addMembership must be called once in a cluster (James M Snell) [#23746](https://github.com/nodejs/node/pull/23746)
- [[`8080a9bf40`](https://github.com/nodejs/node/commit/8080a9bf40)] - **http**: fix error check in `Execute()` (Brian White) [#25939](https://github.com/nodejs/node/pull/25939)
- [[`aedc7120ea`](https://github.com/nodejs/node/commit/aedc7120ea)] - **src**: fix bootstrap_node on bsd (sylkat) [#22663](https://github.com/nodejs/node/pull/22663)
- [[`b5d464955a`](https://github.com/nodejs/node/commit/b5d464955a)] - **test**: fix test-repl-envvars (Anna Henningsen) [#25226](https://github.com/nodejs/node/pull/25226)

Windows 32-bit Installer: https://nodejs.org/dist/v6.17.1/node-v6.17.1-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v6.17.1/node-v6.17.1-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v6.17.1/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v6.17.1/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v6.17.1/node-v6.17.1.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v6.17.1/node-v6.17.1-darwin-x64.tar.gz \
Linux 32-bit Binary: https://nodejs.org/dist/v6.17.1/node-v6.17.1-linux-x86.tar.xz \
Linux 64-bit Binary: https://nodejs.org/dist/v6.17.1/node-v6.17.1-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v6.17.1/node-v6.17.1-linux-ppc64le.tar.xz \
Linux PPC BE 64-bit Binary: https://nodejs.org/dist/v6.17.1/node-v6.17.1-linux-ppc64.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v6.17.1/node-v6.17.1-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v6.17.1/node-v6.17.1-aix-ppc64.tar.gz \
SmartOS 32-bit Binary: https://nodejs.org/dist/v6.17.1/node-v6.17.1-sunos-x86.tar.xz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v6.17.1/node-v6.17.1-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v6.17.1/node-v6.17.1-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v6.17.1/node-v6.17.1-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v6.17.1/node-v6.17.1-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v6.17.1/node-v6.17.1.tar.gz \
Other release files: https://nodejs.org/dist/v6.17.1/ \
Documentation: https://nodejs.org/docs/v6.17.1/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

8e01f15308312487d7f2f0f86185e768f0ee61bcc8f2d52e8dbe9b6cab241539  node-v6.17.1-aix-ppc64.tar.gz
8033e07a5da759af00db7364c2344f11eeff73b51647d39926bfa36f2131b990  node-v6.17.1-darwin-x64.tar.gz
7ced1cbebf4dbe4ba267f21777264b9a1b6adf577b185dd7f04dce1fcbbd4789  node-v6.17.1-darwin-x64.tar.xz
e7e4e22c782fe25540a0a74f64e8aadffb45fc1d93ed66de8afd8fb94896fd72  node-v6.17.1-headers.tar.gz
396981dd46ca7602ed8f9c71b6ba10614dc5af554e0aeccc73304c3c3ed1a793  node-v6.17.1-headers.tar.xz
50b2d01026afc361925836ed4fa7ddd948d3102f1c2872a078fbce5ec7cceb8b  node-v6.17.1-linux-arm64.tar.gz
9c7fdb4208fe0d63ac4280d0894a076d11873d49b79648c448f059443c666fbe  node-v6.17.1-linux-arm64.tar.xz
dfc9b0e47bb471aaf90496019e5bf01efe79fb2d94958f948410b664fb57f536  node-v6.17.1-linux-armv6l.tar.gz
541ae894fa7001c31e8a7992ef2abbba2defc1cf13b6e58894f5fe7341e26247  node-v6.17.1-linux-armv6l.tar.xz
7919c565532ccabe57e13d2d4eecdc43ae81abda187be9a87dc14dc81281fa05  node-v6.17.1-linux-armv7l.tar.gz
4a659adfa3191c0f1246eef5386af9105fc2774997aad4809db53dbf3623e3da  node-v6.17.1-linux-armv7l.tar.xz
0c1d63258b80ce20aa2739a2b510c8f630912409a410080d064941a8f8bd1856  node-v6.17.1-linux-ppc64le.tar.gz
0e1a31cff61d84f14a69e035be6cfd3bf044ed8371124ed14ab8b1b83d949438  node-v6.17.1-linux-ppc64le.tar.xz
f340efb57bb6d9a3468d14a412384bd52832d5300292f992737565f0d1509dfc  node-v6.17.1-linux-ppc64.tar.gz
d2668a48bac1f855420c2a621a5ce9ecccce345a2495d437d451bf20fd80a8f7  node-v6.17.1-linux-ppc64.tar.xz
6e84f14ac2a0273ce009f51698813375e1633465d1df14dd80e88c0901347869  node-v6.17.1-linux-s390x.tar.gz
0eb09f079c44350bac3a586a413848689a27ae3d3076161a826ccd928fc368ba  node-v6.17.1-linux-s390x.tar.xz
6b2b0613f56e1630edcfc8e7800f87d5538cef09fce6a08f1cc35983181d96b2  node-v6.17.1-linux-x64.tar.gz
0f88dacefc4be4709e0a9f9fe685efdfe1582a724d8f42614179c2f604c36165  node-v6.17.1-linux-x64.tar.xz
f506e24f741a14a64f3d6b660a0faecac1001fef98509382ef231e716efc49a1  node-v6.17.1-linux-x86.tar.gz
45bdc0ae20c8d2a4a140f5cb6c0bc7b9812ccf19c95d72bc89a40bb3403c5954  node-v6.17.1-linux-x86.tar.xz
372ddb024919f0ee9293b222adcb284c5ad56e914b8f844f4d4bd236f42f39d0  node-v6.17.1.pkg
f74d887f7b2164201f39ac99c623e30e23a646f4bb6096e5c174d7e95b51dc93  node-v6.17.1-sunos-x64.tar.gz
01c5bace398229a61bae32e98156d7b6d9a9d966074f232d03dbe3bb34fe1344  node-v6.17.1-sunos-x64.tar.xz
6ea4704e64a6228da533453a424ff095e67b196bb85614a999c66cab3689b26d  node-v6.17.1-sunos-x86.tar.gz
b6658ffc366c467ca8412b76171684b93e66297db0ce15f8c7123a5df1e9bd49  node-v6.17.1-sunos-x86.tar.xz
6fa487c5c6770cc7ca0567c0e532d6bcdf0f5d1aebdceb169ecfb66984459dea  node-v6.17.1.tar.gz
6f6dc9624656a008513b7608bfc105dd92ceea5d7b4516edeca7e6b19d2edd94  node-v6.17.1.tar.xz
76fd386179ebae42e1f9278c0df332e3fd7e74913154b4b10ff18e4200aea499  node-v6.17.1-win-x64.7z
85a7110c2e2cdaa76c5cad4512395eb13034fffd2c6aea3eca7e61797959dad7  node-v6.17.1-win-x64.zip
dd61684aee2dcc9805454b19e85cec9ef7a7462a53c202bb6c8c7ac08e5bdd59  node-v6.17.1-win-x86.7z
3705f26c83c261a480115cc2165ad680adb94d99dfc2f4a3d9c4d72b0a8e1ef8  node-v6.17.1-win-x86.zip
e3a3816d7184eb90162b0fc7b0f2b7f6f2ba5c11c4c6d8dc1bd8b7da40a7c514  node-v6.17.1-x64.msi
db11f92d99e1b2289bf62f6cb8e9173fea680c0371d74de0f61802519dc07613  node-v6.17.1-x86.msi
6639e047318e21dc1c5077325d8be24a9ca7465e40f4d9fc568859516352f047  win-x64/node.exe
906ce519680be90b67e75f12af9630f3ded4972b880337077996e2d80a7be984  win-x64/node.lib
412267948bfc0f05a99af525f1339e4c1c72eadc697eaaf6eb828d1b2b09c5e9  win-x64/node_pdb.7z
a5fd65519f48d9a0d0a2d477f276557e1858902f442fc35164c76b5348aa5868  win-x64/node_pdb.zip
8fcacb69695883e610f6e6385779d382db141e5cf7c474e1c6106e0521940471  win-x86/node.exe
9c8d1eda4f4c0d233f9c9fae730ede7f41d0ab7aa2b5646acc05d9d6709e32d1  win-x86/node.lib
753d7b5841f15b6acd1319376023caef644f02161d84baf7189cf97e116b25b8  win-x86/node_pdb.7z
43bbb5a3f27bbe4e2a8e1909070074c9521cc75061b05ec5aec37d5cc3a53ef1  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQEzBAEBCAAdFiEETtd49TnjY0x3nIfG1wYoSKGrAFwFAlylDUcACgkQ1wYoSKGr
AFzCBgf/fIvPDHHUsbanQYqQI588xhJjC3IhNjZ6GoVymRqUCNm6Dr28WIyphyU1
UZSor5BGo5OA3p0gVl7avNa98I9uYBLqx5hwQQjtx6lA70qsydmwKWjtTxYIdv2f
V4OBUGPnfwyLZyxPt1X/tfpbpjYgh+93dW8WfFRDsBwWkkZlv8sUfOgfDpyCHlIP
+ixiUIGjKHE4aLrQJtiUXyC7WL4EINpcyCoEIWF9xgLJrHrs5KKLoXOgQln3rQsH
oDyqHH6+8DLKk998qB322OMQ6stL04Qt2QSeI18iKzr8DGq2Y6D/xvcFwyf5Ewvd
M84EFfeFn83Z4bwPZGUXzzbNT55c6g==
=+rIQ
-----END PGP SIGNATURE-----

```
