---
date: '2016-01-21T02:26:55.519Z'
category: release
title: Node v5.5.0 (Current)
layout: blog-post
author: <PERSON>
---

### Notable Changes

- **events**: make sure console functions exist (<PERSON>) [#4479](https://github.com/nodejs/node/pull/4479)
- **fs**: add autoClose option to fs.createWriteStream (Saquib) [#3679](https://github.com/nodejs/node/pull/3679)
- **http**: improves expect header handling (<PERSON>) [#4501](https://github.com/nodejs/node/pull/4501)
- **node**: allow preload modules with -i (<PERSON>) [#4696](https://github.com/nodejs/node/pull/4696)
- **v8,src**: expose statistics about heap spaces (`v8.getHeapSpaceStatistics()`) (<PERSON>) [#4463](https://github.com/nodejs/node/pull/4463)
- Minor performance improvements:
  - **lib**: Use arrow functions instead of bind where possible (<PERSON><PERSON><PERSON>) [#3622](https://github.com/nodejs/node/pull/3622).
    - (Mistakenly missing from v5.4.0)
  - **module**: cache stat() results more aggressively (Ben Noordhuis) [#4575](https://github.com/nodejs/node/pull/4575)
  - **querystring**: improve parse() performance (Brian White) [#4675](https://github.com/nodejs/node/pull/4675)

### Known issues

- Surrogate pair in REPL can freeze terminal. [#690](https://github.com/nodejs/node/issues/690)
- Calling `dns.setServers()` while a DNS query is in progress can cause the process to crash on a failed assertion. [#894](https://github.com/nodejs/node/issues/894)
- `url.resolve` may transfer the auth portion of the url when resolving between two full hosts, see [#1435](https://github.com/nodejs/node/issues/1435).
- Unicode characters in filesystem paths are not handled consistently across platforms or Node.js APIs. See [#2088](https://github.com/nodejs/node/issues/2088), [#3401](https://github.com/nodejs/node/issues/3401) and [#3519](https://github.com/nodejs/node/issues/3519).

### Commits

- [[`8d0ca10752`](https://github.com/nodejs/node/commit/8d0ca10752)] - **buffer**: make byteLength work with Buffer correctly (Jackson Tian) [#4738](https://github.com/nodejs/node/pull/4738)
- [[`83d2b7707e`](https://github.com/nodejs/node/commit/83d2b7707e)] - **buffer**: remove unnecessary TODO comments (Peter Geiss) [#4719](https://github.com/nodejs/node/pull/4719)
- [[`8182ec094d`](https://github.com/nodejs/node/commit/8182ec094d)] - **build**: add option to select VS version (julien.waechter) [#4645](https://github.com/nodejs/node/pull/4645)
- [[`4383acd9f4`](https://github.com/nodejs/node/commit/4383acd9f4)] - **build**: fix and refactor VTune config in vcbuild.bat (Rod Vagg) [#4192](https://github.com/nodejs/node/pull/4192)
- [[`be0b0b8cb9`](https://github.com/nodejs/node/commit/be0b0b8cb9)] - **build**: minor corrections in VTune configure text (Rod Vagg) [#4192](https://github.com/nodejs/node/pull/4192)
- [[`9571be12f6`](https://github.com/nodejs/node/commit/9571be12f6)] - **cluster**: fix race condition setting suicide prop (Santiago Gimeno) [#4349](https://github.com/nodejs/node/pull/4349)
- [[`ebd9addcd1`](https://github.com/nodejs/node/commit/ebd9addcd1)] - **crypto**: clear error stack in ECDH::Initialize (Fedor Indutny) [#4689](https://github.com/nodejs/node/pull/4689)
- [[`66b9c0d8bd`](https://github.com/nodejs/node/commit/66b9c0d8bd)] - **debugger**: remove variable redeclarations (Rich Trott) [#4633](https://github.com/nodejs/node/pull/4633)
- [[`88b2889679`](https://github.com/nodejs/node/commit/88b2889679)] - **dgram**: prevent disabled optimization of bind() (Brian White) [#4613](https://github.com/nodejs/node/pull/4613)
- [[`d56e3f8b67`](https://github.com/nodejs/node/commit/d56e3f8b67)] - **doc**: restore ICU third-party software licenses (Richard Lau) [#4762](https://github.com/nodejs/node/pull/4762)
- [[`212a44df03`](https://github.com/nodejs/node/commit/212a44df03)] - **doc**: clarify protocol default in http.request() (cjihrig) [#4714](https://github.com/nodejs/node/pull/4714)
- [[`**********`](https://github.com/nodejs/node/commit/**********)] - **doc**: update branch-diff arguments in release doc (Rod Vagg) [#4691](https://github.com/nodejs/node/pull/4691)
- [[`666c089e68`](https://github.com/nodejs/node/commit/666c089e68)] - **doc**: fix named anchors in addons.markdown and http.markdown (Michael Theriot) [#4708](https://github.com/nodejs/node/pull/4708)
- [[`310530b7ec`](https://github.com/nodejs/node/commit/310530b7ec)] - **doc**: add path property to Write/ReadStream in fs.markdown (Claudio Rodriguez) [#4368](https://github.com/nodejs/node/pull/4368)
- [[`3470574cb6`](https://github.com/nodejs/node/commit/3470574cb6)] - **doc**: clarify explanation of first stream section (Vitor Cortez) [#4234](https://github.com/nodejs/node/pull/4234)
- [[`d91646b9c7`](https://github.com/nodejs/node/commit/d91646b9c7)] - **doc**: rebuild LICENSE using tools/license-builder.sh (Rod Vagg) [#4194](https://github.com/nodejs/node/pull/4194)
- [[`265e2f557b`](https://github.com/nodejs/node/commit/265e2f557b)] - **doc**: fix typo in doc/node.1 (Jérémy Lal) [#4680](https://github.com/nodejs/node/pull/4680)
- [[`4c132fe61e`](https://github.com/nodejs/node/commit/4c132fe61e)] - **doc**: make references clickable (Roman Klauke) [#4654](https://github.com/nodejs/node/pull/4654)
- [[`d139704ff7`](https://github.com/nodejs/node/commit/d139704ff7)] - **doc**: improve child_process.execFile() code example (Ryan Sobol) [#4504](https://github.com/nodejs/node/pull/4504)
- [[`eeb6fdcd0f`](https://github.com/nodejs/node/commit/eeb6fdcd0f)] - **doc**: add docs for more stream options (zoubin) [#4639](https://github.com/nodejs/node/pull/4639)
- [[`b6ab6d2de5`](https://github.com/nodejs/node/commit/b6ab6d2de5)] - **doc**: add branch-diff example to releases.md (Myles Borins) [#4636](https://github.com/nodejs/node/pull/4636)
- [[`287325c5e8`](https://github.com/nodejs/node/commit/287325c5e8)] - **docs**: update gpg key for Myles Borins (Myles Borins) [#4657](https://github.com/nodejs/node/pull/4657)
- [[`65825b79aa`](https://github.com/nodejs/node/commit/65825b79aa)] - **docs**: fix npm command in releases.md (Myles Borins) [#4656](https://github.com/nodejs/node/pull/4656)
- [[`f9a59c1d3b`](https://github.com/nodejs/node/commit/f9a59c1d3b)] - **(SEMVER-MINOR)** **events**: make sure console functions exist (Dave) [#4479](https://github.com/nodejs/node/pull/4479)
- [[`6039a7c1b5`](https://github.com/nodejs/node/commit/6039a7c1b5)] - **(SEMVER-MINOR)** **fs**: add autoClose option to fs.createWriteStream (Saquib) [#3679](https://github.com/nodejs/node/pull/3679)
- [[`ed55169834`](https://github.com/nodejs/node/commit/ed55169834)] - **gitignore**: never ignore debug module (Michaël Zasso) [#2286](https://github.com/nodejs/node/pull/2286)
- [[`d755432fa9`](https://github.com/nodejs/node/commit/d755432fa9)] - **(SEMVER-MINOR)** **http**: improves expect header handling (Daniel Sellers) [#4501](https://github.com/nodejs/node/pull/4501)
- [[`7ce0e04f44`](https://github.com/nodejs/node/commit/7ce0e04f44)] - **lib**: fix style issues after eslint update (Michaël Zasso) [nodejs/io.js#2286](https://github.com/nodejs/io.js/pull/2286)
- [[`ae5bcf9528`](https://github.com/nodejs/node/commit/ae5bcf9528)] - **lib**: use arrow functions instead of bind (Minwoo Jung) [#3622](https://github.com/nodejs/node/pull/3622)
- [[`0ec093cd41`](https://github.com/nodejs/node/commit/0ec093cd41)] - **lib,test**: remove extra semicolons (Michaël Zasso) [#2205](https://github.com/nodejs/node/pull/2205)
- [[`d8f5bd4fe1`](https://github.com/nodejs/node/commit/d8f5bd4fe1)] - **module**: avoid ArgumentsAdaptorTrampoline frame (Ben Noordhuis) [#4575](https://github.com/nodejs/node/pull/4575)
- [[`83f8d98806`](https://github.com/nodejs/node/commit/83f8d98806)] - **module**: cache stat() results more aggressively (Ben Noordhuis) [#4575](https://github.com/nodejs/node/pull/4575)
- [[`ff64a4c395`](https://github.com/nodejs/node/commit/ff64a4c395)] - **(SEMVER-MINOR)** **node**: allow preload modules with -i (Evan Lucas) [#4696](https://github.com/nodejs/node/pull/4696)
- [[`4bc1a47761`](https://github.com/nodejs/node/commit/4bc1a47761)] - **querystring**: improve parse() performance (Brian White) [#4675](https://github.com/nodejs/node/pull/4675)
- [[`ad63d350d4`](https://github.com/nodejs/node/commit/ad63d350d4)] - **readline**: Remove XXX and output debuglog (Kohei TAKATA) [#4690](https://github.com/nodejs/node/pull/4690)
- [[`da550aa063`](https://github.com/nodejs/node/commit/da550aa063)] - **repl**: make sure historyPath is trimmed (Evan Lucas) [#4539](https://github.com/nodejs/node/pull/4539)
- [[`1a6e7d1b52`](https://github.com/nodejs/node/commit/1a6e7d1b52)] - **src**: fix negative values in process.hrtime() (Ben Noordhuis) [#4757](https://github.com/nodejs/node/pull/4757)
- [[`8bad51977a`](https://github.com/nodejs/node/commit/8bad51977a)] - **src**: return UV_EAI_NODATA on empty lookup (cjihrig) [#4715](https://github.com/nodejs/node/pull/4715)
- [[`761cf2bf6a`](https://github.com/nodejs/node/commit/761cf2bf6a)] - **src**: don't check failure with ERR_peek_error() (Ben Noordhuis) [#4731](https://github.com/nodejs/node/pull/4731)
- [[`953f4a3999`](https://github.com/nodejs/node/commit/953f4a3999)] - **stream**: prevent object map change in ReadableState (Evan Lucas) [#4761](https://github.com/nodejs/node/pull/4761)
- [[`e65f1f7954`](https://github.com/nodejs/node/commit/e65f1f7954)] - **test**: fix tls-multi-key race condition (Santiago Gimeno) [#3966](https://github.com/nodejs/node/pull/3966)
- [[`3727ae0d7d`](https://github.com/nodejs/node/commit/3727ae0d7d)] - **test**: use addon.md block headings as test dir names (Rod Vagg) [#4412](https://github.com/nodejs/node/pull/4412)
- [[`47960a07c0`](https://github.com/nodejs/node/commit/47960a07c0)] - **test**: make test-cluster-disconnect-leak reliable (Rich Trott) [#4736](https://github.com/nodejs/node/pull/4736)
- [[`9926b5a25f`](https://github.com/nodejs/node/commit/9926b5a25f)] - **test**: fix issues for space-in-parens ESLint rule (Roman Reiss) [#4753](https://github.com/nodejs/node/pull/4753)
- [[`d1aabd6264`](https://github.com/nodejs/node/commit/d1aabd6264)] - **test**: fix style issues after eslint update (Michaël Zasso) [nodejs/io.js#2286](https://github.com/nodejs/io.js/pull/2286)
- [[`e98bcfa2cb`](https://github.com/nodejs/node/commit/e98bcfa2cb)] - **test**: remove 1 second delay from test (Rich Trott) [#4616](https://github.com/nodejs/node/pull/4616)
- [[`6cfd0b5a32`](https://github.com/nodejs/node/commit/6cfd0b5a32)] - **test**: fix flaky test-net-socket-local-address (cjihrig) [#4650](https://github.com/nodejs/node/pull/4650)
- [[`e22cc6c2eb`](https://github.com/nodejs/node/commit/e22cc6c2eb)] - **test**: fix race in test-net-server-pause-on-connect (Rich Trott) [#4637](https://github.com/nodejs/node/pull/4637)
- [[`9164c00bdb`](https://github.com/nodejs/node/commit/9164c00bdb)] - **test**: move resource intensive tests to sequential (Rich Trott) [#4615](https://github.com/nodejs/node/pull/4615)
- [[`d8ba2c0de4`](https://github.com/nodejs/node/commit/d8ba2c0de4)] - **test**: fix `http-upgrade-client` flakiness (Santiago Gimeno) [#4602](https://github.com/nodejs/node/pull/4602)
- [[`6018fa1f57`](https://github.com/nodejs/node/commit/6018fa1f57)] - **test**: fix `http-upgrade-agent` flakiness (Santiago Gimeno) [#4520](https://github.com/nodejs/node/pull/4520)
- [[`c33f6a87d0`](https://github.com/nodejs/node/commit/c33f6a87d0)] - **tools**: enable space-in-parens ESLint rule (Roman Reiss) [#4753](https://github.com/nodejs/node/pull/4753)
- [[`162e16afdb`](https://github.com/nodejs/node/commit/162e16afdb)] - **tools**: enable no-extra-semi rule in eslint (Michaël Zasso) [#2205](https://github.com/nodejs/node/pull/2205)
- [[`031b87d42d`](https://github.com/nodejs/node/commit/031b87d42d)] - **tools**: add license-builder.sh to construct LICENSE (Rod Vagg) [#4194](https://github.com/nodejs/node/pull/4194)
- [[`ec8e0ae697`](https://github.com/nodejs/node/commit/ec8e0ae697)] - **tools**: fix style issue after eslint update (Michaël Zasso) [nodejs/io.js#2286](https://github.com/nodejs/io.js/pull/2286)
- [[`4d5ee7a512`](https://github.com/nodejs/node/commit/4d5ee7a512)] - **tools**: update eslint config (Michaël Zasso) [nodejs/io.js#2286](https://github.com/nodejs/io.js/pull/2286)
- [[`2d441493a4`](https://github.com/nodejs/node/commit/2d441493a4)] - **tools**: update eslint to v1.10.3 (Michaël Zasso) [nodejs/io.js#2286](https://github.com/nodejs/io.js/pull/2286)
- [[`aba3cc834e`](https://github.com/nodejs/node/commit/aba3cc834e)] - **tools**: fix license-builder.sh for ICU (Richard Lau) [#4762](https://github.com/nodejs/node/pull/4762)
- [[`5f57005ec9`](https://github.com/nodejs/node/commit/5f57005ec9)] - **(SEMVER-MINOR)** **v8,src**: expose statistics about heap spaces (Ben Ripkens) [#4463](https://github.com/nodejs/node/pull/4463)

Windows 32-bit Installer: https://nodejs.org/dist/v5.5.0/node-v5.5.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v5.5.0/node-v5.5.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v5.5.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v5.5.0/win-x64/node.exe \
Mac OS X 64-bit Installer: https://nodejs.org/dist/v5.5.0/node-v5.5.0.pkg \
Mac OS X 64-bit Binary: https://nodejs.org/dist/v5.5.0/node-v5.5.0-darwin-x64.tar.gz \
Linux 32-bit Binary: https://nodejs.org/dist/v5.5.0/node-v5.5.0-linux-x86.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v5.5.0/node-v5.5.0-linux-x64.tar.gz \
SmartOS 32-bit Binary: https://nodejs.org/dist/v5.5.0/node-v5.5.0-sunos-x86.tar.gz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v5.5.0/node-v5.5.0-sunos-x64.tar.gz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v5.5.0/node-v5.5.0-linux-armv6l.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v5.5.0/node-v5.5.0-linux-armv7l.tar.gz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v5.5.0/node-v5.5.0-linux-arm64.tar.gz \
Source Code: https://nodejs.org/dist/v5.5.0/node-v5.5.0.tar.gz \
Other release files: https://nodejs.org/dist/v5.5.0/ \
Documentation: https://nodejs.org/docs/v5.5.0/api/

Shasums (GPG signing hash: SHA512, file hash: SHA256):

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA512

d4fd29e2d501963235104fc715fb0b55b302a40b605f432c456069606b939a46  node-v5.5.0-darwin-x64.tar.gz
d993c26c6a3127911345f09a7b41a6d124bb894c819e30333ea110c16fe54f7f  node-v5.5.0-darwin-x64.tar.xz
67ebc3989b98980c13ef17c1336b98ed2975aa093d9b69f399dfb93de5ae74b4  node-v5.5.0-headers.tar.gz
f98d73e8e466faa526308e57a4f912a9eea0704a9b4cda8db89fee48062b1514  node-v5.5.0-headers.tar.xz
a9ebfce36675cc8d5e1bea6fa57de7fd80e8016f5957340831fcd03560e59845  node-v5.5.0-linux-arm64.tar.gz
81e51ee6dafc19bdb3b81fd136010212ee08ed6f58f44788923a2a56a9be86b6  node-v5.5.0-linux-arm64.tar.xz
a156dfda7fa00ac7ea86ac3cff8d445c44bcec3c677db375776a0489ad7155bd  node-v5.5.0-linux-armv6l.tar.gz
9cbbcd4ec70b2f2ff2a478c1d83ccda47af3dd96e66d47fee5869212f0d35c5a  node-v5.5.0-linux-armv6l.tar.xz
cf9832efa0cac6365b4fec83df5dee8ecb67d39931c4e7cdaaa4cb933fb4b78b  node-v5.5.0-linux-armv7l.tar.gz
cb8a11d3dd154c834373b6243edbd353e637b701ed05db0f3015577efbc66444  node-v5.5.0-linux-armv7l.tar.xz
3e593d91b6d2ad871efaaf8e9a17b3608ca98904959bcfb7c42e6acce89e80f4  node-v5.5.0-linux-x64.tar.gz
33b4d062f5ba5c8db86119b693273f0ca764fe0d076558bb8e645c37ea0b22fc  node-v5.5.0-linux-x64.tar.xz
e384f8beb392cd5df882cd3401e449371190bc139010d391888c2f3346fb5107  node-v5.5.0-linux-x86.tar.gz
7e4bd588e165aede4df22ea743548c61cee79faed4fd034b4e08e6b429576dfb  node-v5.5.0-linux-x86.tar.xz
c192270ecc0e1c15513c27cab48811f52f37b3216b20d3bcfdd2172f0a06d7cb  node-v5.5.0.pkg
e8abb364fc8db51e3a6c8dc14f24ec2c289940386c18da4ecb33462021c1bcef  node-v5.5.0-sunos-x64.tar.gz
84737dc228b203077d0fe0a55247e7d0f2f68f2e9700bade8eb8cdd1ce1c2872  node-v5.5.0-sunos-x64.tar.xz
c49c8ce8c997da2cf1ee1cccb55ed457d31aa85d4ec6f58edd868daaf29ddc45  node-v5.5.0-sunos-x86.tar.gz
cd034dcac4018aa0c8c01142d3d16238ac73d643b7a9b4294542b2ee93e07d09  node-v5.5.0-sunos-x86.tar.xz
d69b18cc20699a35434858fb853997616762280610a510ec4b4ff1a94798b432  node-v5.5.0.tar.gz
9c46b4dc9548e43826f71f6571f56e39783c456b9516045b496ea73321731e22  node-v5.5.0.tar.xz
ba1dcd3035b045fb4ecb254bab1e0dda24934f2a773e6e05d0a54ac4adc4ee3b  node-v5.5.0-x64.msi
40b667ca279927aacbf65f32f0a3400ff2aba15db46fd3eb1a1d8b94b182bb12  node-v5.5.0-x86.msi
74abfa53909b3011d67d0b76115d134642b946d19a17293f3421ee5d5d00f20a  win-x64/node.exe
0cb30f8ab20fc91d72067e7812216b166a71b8de94a16ca79d3b316a76f476ed  win-x64/node.lib
142156b0b895f4f4f0df80816bf4acde05a6b5166580e8124cfedc8e1a408783  win-x86/node.exe
ff0f93aff2caaef5c45d1ca261a940ff6ad27148da4ded01d76dfe868a0904db  win-x86/node.lib
-----BEGIN PGP SIGNATURE-----
Comment: GPGTools - https://gpgtools.org

iQIcBAEBCgAGBQJWoEGjAAoJELY7U1pMIGypci0QALw8WgMkTd4rqEqsE6QKoEdB
iLXXCWf90IDa/4qJHYUX9roxyEV+XEaZjrIkd3HnUYZrjdqTl5Z1Rhe+wD7zH8Jk
n31JOKaD+btxmhV6Exg2SBLZt38J3eoOwdnrSN0B3bEbC6FEKDdWDk3KPOu9Eh0j
uIH+chSAlGJqYwC6NO6bxHKh3vlz7klD7KUMzVj2bas32UmdNKNT6r2LRFZMVknd
+GsM5LuHnD/nwh/ENnA2QjcHJEjmxxL1CmJFSMX0ITTMLzBDjcj8FCoDPdYQp0YQ
ZKCJrvrBUAW+rSWxrDuKP0DqO2/47LmOcssGO3eR/w+q3lXvdj3f0EeLT2tdaiQ5
eHn6RaJpzk0BTWrYh21C3BkAuZs3+nNG3i7rYxHBtCkYdjx4sNkwxCjblEEJtzJK
39ygB0zNIrAFFNeqytSzkqjzkx+XD9CwzXVcC+s+zw0fCVOzvwh91jaNaG2haszD
fDS02nW4VEy+7UQJ3Hf2ojIjetqZTWmGdFH/Y6016s+b9VEObgtEtGU38phatRuQ
1k34Fk82u2LGAyaTEXuMILDPrONSXOtkaFlM3hkCV/V/1EgSRHTEfQetrrR7fW4R
LYoyc03CsrjL3hm3gePbOjHqu/vDejqD8zttBwvGE1uPZlCOPiM8bb0IFuAZUzEo
CtOuU8IYfoBYo8fOVLvl
=xfMB
-----END PGP SIGNATURE-----

```
