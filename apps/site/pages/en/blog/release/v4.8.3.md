---
date: '2017-05-02T18:11:20.259Z'
category: release
title: Node v4.8.3 (Maintenance)
layout: blog-post
author: <PERSON><PERSON>
---

### Notable Changes

- **module**:
  - The [module loading global fallback](https://nodejs.org/dist/latest-v4.x/docs/api/modules.html#modules_loading_from_the_global_folders) to the Node executable's directory now works correctly on Windows. (<PERSON>) [#9283](https://github.com/nodejs/node/pull/9283)
- **src**:
  - fix base64 decoding in rare edgecase (<PERSON>) [#11995](https://github.com/nodejs/node/pull/11995)
- **tls**:
  - fix rare segmentation faults when using TLS
    - (<PERSON>) [#11947](https://github.com/nodejs/node/pull/11947)
    - (<PERSON>) [#11898](https://github.com/nodejs/node/pull/11898)
    - (jBarz) [#11776](https://github.com/nodejs/node/pull/11776)

### Commits

- [[`44260806a6`](https://github.com/nodejs/node/commit/44260806a6)] - Partial revert "tls: keep track of stream that is closed" (Trevor Norris) [#11947](https://github.com/nodejs/node/pull/11947)
- [[`ab3fdf531f`](https://github.com/nodejs/node/commit/ab3fdf531f)] - **deps**: cherry-pick ca0f9573 from V8 upstream (Ali Ijaz Sheikh) [#11940](https://github.com/nodejs/node/pull/11940)
- [[`07b92a3c0b`](https://github.com/nodejs/node/commit/07b92a3c0b)] - **doc**: add supported platforms list for v4.x (Michael Dawson) [#12091](https://github.com/nodejs/node/pull/12091)
- [[`ba91c41478`](https://github.com/nodejs/node/commit/ba91c41478)] - **module**: fix loading from global folders on Windows (Richard Lau) [#9283](https://github.com/nodejs/node/pull/9283)
- [[`b5b78b12b8`](https://github.com/nodejs/node/commit/b5b78b12b8)] - **src**: add fcntl.h include to node.cc (Bartosz Sosnowski) [#12540](https://github.com/nodejs/node/pull/12540)
- [[`eb393f9ae1`](https://github.com/nodejs/node/commit/eb393f9ae1)] - **src**: fix base64 decoding (Nikolai Vavilov) [#11995](https://github.com/nodejs/node/pull/11995)
- [[`8ed18a1429`](https://github.com/nodejs/node/commit/8ed18a1429)] - **src**: ensure that fd 0-2 are valid on windows (Bartosz Sosnowski) [#11863](https://github.com/nodejs/node/pull/11863)
- [[`ff1d61c11b`](https://github.com/nodejs/node/commit/ff1d61c11b)] - **stream_base,tls_wrap**: notify on destruct (Trevor Norris) [#11947](https://github.com/nodejs/node/pull/11947)
- [[`6040efd7dc`](https://github.com/nodejs/node/commit/6040efd7dc)] - **test**: fix flaky test-tls-wrap-timeout (Rich Trott) [#7857](https://github.com/nodejs/node/pull/7857)
- [[`7a1920dc84`](https://github.com/nodejs/node/commit/7a1920dc84)] - **test**: add hasCrypto check to tls-socket-close (Daniel Bevenius) [#11911](https://github.com/nodejs/node/pull/11911)
- [[`1dc6b38dcf`](https://github.com/nodejs/node/commit/1dc6b38dcf)] - **test**: add test for loading from global folders (Richard Lau) [#9283](https://github.com/nodejs/node/pull/9283)
- [[`54f5258582`](https://github.com/nodejs/node/commit/54f5258582)] - **tls**: fix segfault on destroy after partial read (Ben Noordhuis) [#11898](https://github.com/nodejs/node/pull/11898)
- [[`99749dccfe`](https://github.com/nodejs/node/commit/99749dccfe)] - **tls**: keep track of stream that is closed (jBarz) [#11776](https://github.com/nodejs/node/pull/11776)
- [[`6d3aaa72a8`](https://github.com/nodejs/node/commit/6d3aaa72a8)] - **tls**: TLSSocket emits 'error' on handshake failure (Mariusz 'koder' Chwalba) [#8805](https://github.com/nodejs/node/pull/8805)

Windows 32-bit Installer: https://nodejs.org/dist/v4.8.3/node-v4.8.3-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v4.8.3/node-v4.8.3-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v4.8.3/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v4.8.3/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v4.8.3/node-v4.8.3.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v4.8.3/node-v4.8.3-darwin-x64.tar.gz \
Linux 32-bit Binary: https://nodejs.org/dist/v4.8.3/node-v4.8.3-linux-x86.tar.xz \
Linux 64-bit Binary: https://nodejs.org/dist/v4.8.3/node-v4.8.3-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v4.8.3/node-v4.8.3-linux-ppc64le.tar.xz \
Linux PPC BE 64-bit Binary: https://nodejs.org/dist/v4.8.3/node-v4.8.3-linux-ppc64.tar.xz \
SmartOS 32-bit Binary: https://nodejs.org/dist/v4.8.3/node-v4.8.3-sunos-x86.tar.xz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v4.8.3/node-v4.8.3-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v4.8.3/node-v4.8.3-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v4.8.3/node-v4.8.3-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v4.8.3/node-v4.8.3-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v4.8.3/node-v4.8.3.tar.gz \
Other release files: https://nodejs.org/dist/v4.8.3/ \
Documentation: https://nodejs.org/docs/v4.8.3/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

5aa0fe69a61b56a806b7176ef53974e61ae4643b3684a1b098bc2f31a21f87c4  node-v4.8.3-darwin-x64.tar.gz
b53f2b6dbbb7da5a0938c3d8dbe34be4ad794ffd61692cfbc07fc095bf6170cb  node-v4.8.3-darwin-x64.tar.xz
586beed84d30a46e90995331538a86bb39ee22c6e71e9181cd448edd5df9e23b  node-v4.8.3-headers.tar.gz
63d6a24ce83f884648a2c40c58ba3c29a725aba0145631d46670993f194b54c2  node-v4.8.3-headers.tar.xz
6bcc2285ea06fd6096027367a6bea6b9c44f1f7ce7cf0700172663217ef1423f  node-v4.8.3-linux-arm64.tar.gz
fb3154197f9a83c64f09643ea73fb8b18002cc1270b8dd6a572ed1e5f1633edd  node-v4.8.3-linux-arm64.tar.xz
af669f5fc7cb5269978144a8b6469923657119b3ee712d6bf70167e36b6812c2  node-v4.8.3-linux-armv6l.tar.gz
dd1b6a31ae77a639b6758c00bc5fc76316233672487330041f8e3366f271821f  node-v4.8.3-linux-armv6l.tar.xz
36813c749289f547c9068a6cd2837ceb95a3d36da8e31de597d07cd899dcb952  node-v4.8.3-linux-armv7l.tar.gz
3e2f4c8e7c0a6fe2578f42b7cf5e67d6be6a24bc564dbecde3e0ecb49fb11a2e  node-v4.8.3-linux-armv7l.tar.xz
40489a73e1bb4d6eaf45280c032afa154adf2f898edd6b8ccbd16df83a78eb6a  node-v4.8.3-linux-ppc64le.tar.gz
f93307bc817c728dc44344b21ce3ae24afe13f20a59e9e2ef9cb4773646b3652  node-v4.8.3-linux-ppc64le.tar.xz
36b07f2496fa286c6879f04b550f23a528dd645d5fdaddd6a5370f468c5a0c86  node-v4.8.3-linux-ppc64.tar.gz
b47e6d47b2801145dc0b1b752393eab904ad1bcdf12409e5aff02fd6678d3176  node-v4.8.3-linux-ppc64.tar.xz
52382b93865a5edd834db10e8f60822680d26dc2b8cadccafc351b0082a9052a  node-v4.8.3-linux-x64.tar.gz
ca26fee56642f01f6b678d498cbe9b4bd611d8ea07ea5ef63d91f9768c8c6243  node-v4.8.3-linux-x64.tar.xz
0138e4199863a482611d0df517a318b17f1ca9df21bd8c1136030947fd8155b5  node-v4.8.3-linux-x86.tar.gz
f4db97d419791ccc03b3430f94027db25fe0893d55a9faa8565f4426db226438  node-v4.8.3-linux-x86.tar.xz
369aeccb89552c79d804eebb88d61d520986a200ef935d3c39519975ad7f4ea7  node-v4.8.3.pkg
1a19b5c2184547a38515cfe44e29f237d557bc1332b6b7712f33b965f6c24e9e  node-v4.8.3-sunos-x64.tar.gz
4428d6e81d4d05050776c46ed7d25dc30e3888efbfab0bd84410a02c187de19e  node-v4.8.3-sunos-x64.tar.xz
70c1a0bd6deeb32941fc197cf47bed41296bfc4037e43ab06a49af6bdbaf0286  node-v4.8.3-sunos-x86.tar.gz
46b21e86a520e36c5d456580eaa823c5812076883945d6ff1db0c506a19285c8  node-v4.8.3-sunos-x86.tar.xz
f8a5b7fe246f8358121e057f26411d3be9657a328b146d74abf483a956702868  node-v4.8.3.tar.gz
d84e7544c2e31a2d0825b4f8b093d169bf8bdb1881ee8cf75ff937918e59e9cb  node-v4.8.3.tar.xz
cccf63332b8e2b45c1366735a2f9a42073a34cc91b1bfbe5e2c2c2dcde3b71f1  node-v4.8.3-win-x64.7z
a8bb12240576ecc1a8640db47e4bd38cc21ac7262d8456dc988b873e07a38dd5  node-v4.8.3-win-x64.zip
f3b9ba365f82f0aa146a830f08d275481aa308fe2d7f6dfce4f1e7240dcf6fa2  node-v4.8.3-win-x86.7z
a17a924e6bb91f07b6d08a8383084a5e74e42621dc3675ab111fb7c0da060f26  node-v4.8.3-win-x86.zip
4113f98b9133687335f633be86aa91892992b884476a66e290d2650f85df8fd4  node-v4.8.3-x64.msi
493ae603e8a423a1b98ff048ac7051c6aabab6340011b3dceaeffdd13d4d350f  node-v4.8.3-x86.msi
a793a6ea651399a78ee6a4316dd81323d3644f4626171c73390d06b1133a29c3  win-x64/node.exe
cafd50bc53154efff221aba08b3e0beaf2dcd92d578dded52a2e7791cd03b027  win-x64/node.lib
e6c56a0a753adba34d8bf0ed38d22efda9a5dcae35d9fbb6693f318482bc76d1  win-x64/node_pdb.7z
27b8ebf64c91257dc843cd0d418c58c1adec3bbeab60d67ead39361738a94016  win-x64/node_pdb.zip
774f32e53db439ad8c1270e4de5ab79bc4482b10c418dd14bf24216f51586312  win-x86/node.exe
2f580bc0996e4f1d9b90fa066de7b071b0557b6df9435a59c36260ea2de931d8  win-x86/node.lib
ac6675bbb43d3f49ce109d0a7dee768217741a5b0f0722872dbabcccb5ba2be5  win-x86/node_pdb.7z
b76b77e9db0a6a8d6823525c016d3f4415da799d4e73d3eaf889552350f24e2f  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQEcBAEBCAAGBQJZCMi7AAoJEJM7AfQLXKlGXCEIAJuraxQ1R28nmDi6orty1iuv
yi+ddsxhS8LodM4Rn/cTKULtNbRi5ca90sSPPNI1QUjXuqQBpsabUC+7mVla1ZL3
1KuP9VCrRqdSVGhLi2Y1ynccjNxx+UU8E4giagnNPLrzn3L6Hn18RYPHaz//ZVPi
dnTgVhh6wka5LpspwnKvKH7ROMYSmslptQbipfVphmGDIqaua2m39Eppwrtnxaxb
olP/ntClGwr9an/neGSnxEKn/NblTrQtisEh7gACtORLhlyjSTgQSU7iLpmTtQTh
sqG8ZWqFwNLyUvg/AmIpzr2p66A7RxxMWSql+EaMnS4K9H2+S698SEGXNk0wvMw=
=mf3f
-----END PGP SIGNATURE-----

```
