---
date: '2020-11-16T16:21:17.478Z'
category: release
title: Node v15.2.1 (Current)
layout: blog-post
author: <PERSON><PERSON><PERSON>
---

### Notable changes

This is a security release.

Vulnerabilities fixed:

- **CVE-2020-8277**: Denial of Service through DNS request (High). A Node.js application that allows an attacker to trigger a DNS request for a host of their choice could trigger a Denial of service by getting the application to resolve a DNS record with a larger number of responses.

### Commits

- [[`2a44836eeb`](https://github.com/nodejs/node/commit/2a44836eeb)] - **deps**: cherry-pick 0d252eb from upstream c-ares (<PERSON>) [nodejs-private/node-private#231](https://github.com/nodejs-private/node-private/pull/231)
- [[`b1f5518a0a`](https://github.com/nodejs/node/commit/b1f5518a0a)] - **doc**: fix `events.getEventListeners` example (<PERSON>) [#36085](https://github.com/nodejs/node/pull/36085)
- [[`b477447a55`](https://github.com/nodejs/node/commit/b477447a55)] - **doc**: fix `added:` info for `stream.\_construct()` (Luigi Pinca) [#36067](https://github.com/nodejs/node/pull/36067)
- [[`df211208c0`](https://github.com/nodejs/node/commit/df211208c0)] - **test**: add missing test coverage for setLocalAddress() (Rich Trott) [#36039](https://github.com/nodejs/node/pull/36039)
- [[`f5191f5bd2`](https://github.com/nodejs/node/commit/f5191f5bd2)] - **test**: remove flaky designation for fixed test (Rich Trott) [#35961](https://github.com/nodejs/node/pull/35961)
- [[`a2f652f7c5`](https://github.com/nodejs/node/commit/a2f652f7c5)] - **test**: move test-worker-eventlooputil to sequential (Rich Trott) [#35996](https://github.com/nodejs/node/pull/35996)
- [[`b0b43b27d6`](https://github.com/nodejs/node/commit/b0b43b27d6)] - **test**: fix unreliable test-fs-write-file.js (Rich Trott) [#36102](https://github.com/nodejs/node/pull/36102)

Windows 32-bit Installer: https://nodejs.org/dist/v15.2.1/node-v15.2.1-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v15.2.1/node-v15.2.1-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v15.2.1/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v15.2.1/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v15.2.1/node-v15.2.1.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v15.2.1/node-v15.2.1-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v15.2.1/node-v15.2.1-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v15.2.1/node-v15.2.1-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v15.2.1/node-v15.2.1-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v15.2.1/node-v15.2.1-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v15.2.1/node-v15.2.1-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v15.2.1/node-v15.2.1-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v15.2.1/node-v15.2.1.tar.gz \
Other release files: https://nodejs.org/dist/v15.2.1/ \
Documentation: https://nodejs.org/docs/v15.2.1/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

7f8276074adb699696b1a159bc2a21b939ab9d6a2997c49a80d967ae460f6c3d  node-v15.2.1-aix-ppc64.tar.gz
2cca29de17ab2d047ca3a793fe15be43e251985dd3b186942b593fa2f0d9e47a  node-v15.2.1-darwin-x64.tar.gz
72bef01a6fe209847136a71713085b45356d0c87f1ae3f85666dafd82ae455a2  node-v15.2.1-darwin-x64.tar.xz
74b0f1ac57b4ed0b325b17e53bfbfa56af4bd98b90f9f310a074cc594b674624  node-v15.2.1-headers.tar.gz
d2146fc62f398df78b4039c90994bcf7a452cf8a8e5b696732f338c22d5c20b7  node-v15.2.1-headers.tar.xz
1b7c9a5a484e4c1dc3e104d79627e65cd0e39fa84f8115e239355b5bf3b0b16d  node-v15.2.1-linux-arm64.tar.gz
77983ab655fd90d64efb37149a45fbb79e4998c5952c5b4696a6fb1b87245311  node-v15.2.1-linux-arm64.tar.xz
56a2d53aa2a1d29b9c369b431fffe2e09a1ea7b847746d08ef56975c4fd58872  node-v15.2.1-linux-armv7l.tar.gz
5650682f27643b4436f992a7c224a3f8c1c34f9aa7c87d8375b05f1bb9190868  node-v15.2.1-linux-armv7l.tar.xz
725827d64c8e7e272bb72a4e02fc3ed4ac7158c24cdd0ee6608727c10f8f771f  node-v15.2.1-linux-ppc64le.tar.gz
565cbdb06808f2aad480a49099ef841b6d277f1be5f1fe06198e3e65d64cd872  node-v15.2.1-linux-ppc64le.tar.xz
4414594d82d0eb0a6d09fec1ca8d995c124f8aeb0732495248cb75597cf19add  node-v15.2.1-linux-s390x.tar.gz
5dcae04fa61959f28e5a9ef45d99ff06703c067f814d4f3a5d90d168c58d59b7  node-v15.2.1-linux-s390x.tar.xz
70802c27ca9f9db9a4acc2a849fb572f4cd971749f9d9a8d36bd4c37a0a64f71  node-v15.2.1-linux-x64.tar.gz
a13dc3282312f8e862b02e5aacd93a7dffe5b01d55f60f3a6042b10523b5d7b3  node-v15.2.1-linux-x64.tar.xz
c3b3e2a8d7a0d1e54a8f0a32061f5b4e8248a95927d8aee08b926285835f2457  node-v15.2.1.pkg
a64aa96e01c097d5bc16c191a647154315e12c5421e38ae985c197c6a20a69f9  node-v15.2.1.tar.gz
566231e02a30b6bfa6a572b152cea3c58079f463312a6228161d210491f9e83e  node-v15.2.1.tar.xz
e6aef3dfa43b8e09d9819476551ecace73a3e5f6f5729854a704fc0d36f108ff  node-v15.2.1-win-x64.7z
ac3a647f18d0c81d5da4094f00526462c2ca38f4866c46489a6cd8ee402a417f  node-v15.2.1-win-x64.zip
5b4c550e53531969dc5cb6ac0eeb637c2214eb9014fa6a058d738167827097e2  node-v15.2.1-win-x86.7z
615e9d59ee26044da16641f44dc7505ed46629334004e038a39648dfbc0e0659  node-v15.2.1-win-x86.zip
637f455ba284067b94c484e28d917859f3197418c3c53cf305c3014c6e2d29b3  node-v15.2.1-x64.msi
88964ce69dbd6dd86dd9c0c305a38588f001ef7b63c9f5586433b4b721f6e0a6  node-v15.2.1-x86.msi
99a99b6cc1fd19429da783b70e14c67bde4a8da17ec7ff7a84ca7700f239f55e  win-x64/node.exe
cd72b0b6e92a7d81c22d287f7863338a7e3248be7aa83a20c5c575917c6b101e  win-x64/node.lib
78cdf253b08ad43ea0a02abf80516c7f6d9999c72307d9e31a505484479edddc  win-x64/node_pdb.7z
8dfa27874715ca9317101bc3c53637fb2e388d310e64ced1209f2c118108ddd7  win-x64/node_pdb.zip
380d5027f89ce12d33320a3aceb6a9aaf1815f14451cebfb625283040fdd9847  win-x86/node.exe
211fd9f4906a694c7d5e8d02b0bd2d27e295f31798fc97a3bd74bac3f057ab35  win-x86/node.lib
0cb4e2dd52719872de7ac927927b9181cacf77e3b4465ae0ab545f7a714133f4  win-x86/node_pdb.7z
ef3cd550b445567dedb5782ac7c00127e015072f5bd8bb64dca4e21df4b94ce4  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEj8yhP+8dDC6RAI4Jdw96mlrhVgAFAl+ypoMACgkQdw96mlrh
VgBeiw/+PRDj42sPcX5ApKz/rRRJejR/ifiOX4zngR0bLKADhfe7B4fu56X8crg+
aUcATBxxEgqU4AKn3qU+K/RlHdm4GYs48z2BnjRN+mU04haeP4e+104suCAYvhH5
xj/c6i7vYLW5w58w7Tv0tiSHzsRSaglqmBuK0i/wkEz8B02bsbhCI2FJ+yh+HAGC
bVTwufA+WAnLCaq5M3TTTPMrAhgE7eOwNQJI8b3bYIUz2GiTj8oVQUdcvnsphObZ
+ZBclK56sWzs4/x3y+HYYhNWKKA5yvkpVxraEI/hp6/VISlXp0dq4CewC+SHpcJn
aaw4tLyvaYsLjOQriSUMBeRje+LsgGCRMFWalbYqYK1MQWtleFt+wce0rI4K4R1Z
WEwfzz+t1G0e0PmTns5aHKkPSGEknwE9pDeAHBbQvNgE0nT556BMXy3764F5mowN
Nw3BlURHS9t6WZsREFf3xT69mxPCv6UPAkzFu4jC4GCoegY6As2E+dyAlQ8OGgOc
RJJddzoOKB0YYND28VJTaThpOjO/7751NTxU1LnsiDAPcCSUfWUHXBRK37d+oLO5
7u9SmCjXb8mj2CY1gFoRak5oDThXJPVP/ena8WRRMjcU33obtO9c8DfJTMjv9eO1
MoJRH92JmToXTRNIg3G1DQbDDfUqKJVzyy6Wp297lDW8btfDa4M=
=GDce
-----END PGP SIGNATURE-----

```
