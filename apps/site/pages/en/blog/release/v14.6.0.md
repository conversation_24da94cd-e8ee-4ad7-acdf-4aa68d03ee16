---
date: '2020-07-21T21:47:34.209Z'
category: release
title: Node v14.6.0 (Current)
layout: blog-post
author: <PERSON><PERSON>
---

### Notable Changes

- **deps**:
  - upgrade to libuv 1.38.1 (<PERSON>) [#34187](https://github.com/nodejs/node/pull/34187)
  - upgrade npm to 6.14.6 (claudiahdz) [#34246](https://github.com/nodejs/node/pull/34246)
  - **(SEMVER-MINOR)** update V8 to 8.4.371.19 (<PERSON><PERSON><PERSON>) [#33579](https://github.com/nodejs/node/pull/33579)
- **module**:
  - **(SEMVER-MINOR)** doc only deprecation of module.parent (<PERSON>) [#32217](https://github.com/nodejs/node/pull/32217)
  - **(SEMVER-MINOR)** package "imports" field (<PERSON>) [#34117](https://github.com/nodejs/node/pull/34117)
- **src**:
  - **(SEMVER-MINOR)** allow embedders to disable esm loader (<PERSON>) [#34060](https://github.com/nodejs/node/pull/34060)
- **tls**:
  - **(SEMVER-MINOR)** make 'createSecureContext' honor more options (Mateusz Krawczuk) [#33974](https://github.com/nodejs/node/pull/33974)
- **vm**:
  - **(SEMVER-MINOR)** add run-after-evaluate microtask mode (Anna Henningsen) [#34023](https://github.com/nodejs/node/pull/34023)
- **worker**:
  - **(SEMVER-MINOR)** add option to track unmanaged file descriptors (Anna Henningsen) [#34303](https://github.com/nodejs/node/pull/34303)
- **New Collaborators**:
  - add danielleadams to collaborators (Danielle Adams) [#34360](https://github.com/nodejs/node/pull/34360)
  - add ruyadorno to collaborators (Ruy Adorno) [#34297](https://github.com/nodejs/node/pull/34297)
  - add sxa as collaborator (Stewart X Addison) [#34338](https://github.com/nodejs/node/pull/34338)

### Commits

- [[`afec0d7f51`](https://github.com/nodejs/node/commit/afec0d7f51)] - **async_hooks**: improve resource stack performance (Anna Henningsen) [#34319](https://github.com/nodejs/node/pull/34319)
- [[`f340571301`](https://github.com/nodejs/node/commit/f340571301)] - **(SEMVER-MINOR)** **build**: reset embedder string to "-node.0" (Michaël Zasso) [#33579](https://github.com/nodejs/node/pull/33579)
- [[`de250c136c`](https://github.com/nodejs/node/commit/de250c136c)] - **build**: recommend Python 3.8 to build on Windows (Michaël Zasso) [#34182](https://github.com/nodejs/node/pull/34182)
- [[`a130771d4f`](https://github.com/nodejs/node/commit/a130771d4f)] - **build,tools**: fix cmd_regen_makefile (Daniel Bevenius) [#34255](https://github.com/nodejs/node/pull/34255)
- [[`cfd4c8012d`](https://github.com/nodejs/node/commit/cfd4c8012d)] - **crypto**: move typechecking for timingSafeEqual into C++ (Anna Henningsen) [#34141](https://github.com/nodejs/node/pull/34141)
- [[`95afc2e50e`](https://github.com/nodejs/node/commit/95afc2e50e)] - **deps**: V8: update headers for ABI compatibility (Anna Henningsen) [#34356](https://github.com/nodejs/node/pull/34356)
- [[`2c9fd6ebd4`](https://github.com/nodejs/node/commit/2c9fd6ebd4)] - **deps**: V8: revert de4c0042cbe6 from upstream V8 (Anna Henningsen) [#34356](https://github.com/nodejs/node/pull/34356)
- [[`447b1e86a5`](https://github.com/nodejs/node/commit/447b1e86a5)] - **deps**: V8: re-add dummy Isolate::CheckMemoryPressure (Anna Henningsen) [#34356](https://github.com/nodejs/node/pull/34356)
- [[`2079fefacf`](https://github.com/nodejs/node/commit/2079fefacf)] - **deps**: V8: undo header change of 9dbab9bbdb979 (Anna Henningsen) [#34356](https://github.com/nodejs/node/pull/34356)
- [[`9f886c968c`](https://github.com/nodejs/node/commit/9f886c968c)] - **(SEMVER-MINOR)** **deps**: bump minimum icu version to 67 (Michaël Zasso) [#33579](https://github.com/nodejs/node/pull/33579)
- [[`3fa7ad3375`](https://github.com/nodejs/node/commit/3fa7ad3375)] - **(SEMVER-MINOR)** **deps**: update V8 postmortem metadata script (Colin Ihrig) [#33579](https://github.com/nodejs/node/pull/33579)
- [[`4c37837424`](https://github.com/nodejs/node/commit/4c37837424)] - **deps**: V8: cherry-pick eec10a2fd8fa (Stephen Belanger) [#33778](https://github.com/nodejs/node/pull/33778)
- [[`fb180ac110`](https://github.com/nodejs/node/commit/fb180ac110)] - **deps**: V8: backport 22014de00115 (Joyee Cheung) [#33300](https://github.com/nodejs/node/pull/33300)
- [[`01e788622c`](https://github.com/nodejs/node/commit/01e788622c)] - **(SEMVER-MINOR)** **deps**: V8: fix compilation on VS2017 (Jiawen Geng) [#33579](https://github.com/nodejs/node/pull/33579)
- [[`f269dff06e`](https://github.com/nodejs/node/commit/f269dff06e)] - **(SEMVER-MINOR)** **deps**: V8: cherry-pick 9868b2aefa1a (Michaël Zasso) [#33579](https://github.com/nodejs/node/pull/33579)
- [[`335e3861c3`](https://github.com/nodejs/node/commit/335e3861c3)] - **(SEMVER-MINOR)** **deps**: patch V8 to run on Xcode 8 (Matheus Marchini) [#32116](https://github.com/nodejs/node/pull/32116)
- [[`355e2f2b6a`](https://github.com/nodejs/node/commit/355e2f2b6a)] - **(SEMVER-MINOR)** **deps**: V8: silence irrelevant warnings (Michaël Zasso) [#32116](https://github.com/nodejs/node/pull/32116)
- [[`eb6ded61b7`](https://github.com/nodejs/node/commit/eb6ded61b7)] - **(SEMVER-MINOR)** **deps**: make v8.h compatible with VS2015 (Joao Reis) [#32116](https://github.com/nodejs/node/pull/32116)
- [[`a4b71e02ca`](https://github.com/nodejs/node/commit/a4b71e02ca)] - **(SEMVER-MINOR)** **deps**: V8: forward declaration of `Rtl\*FunctionTable` (Refael Ackermann) [#32116](https://github.com/nodejs/node/pull/32116)
- [[`1e37442fdd`](https://github.com/nodejs/node/commit/1e37442fdd)] - **(SEMVER-MINOR)** **deps**: V8: patch register-arm64.h (Refael Ackermann) [#32116](https://github.com/nodejs/node/pull/32116)
- [[`eac35c6061`](https://github.com/nodejs/node/commit/eac35c6061)] - **(SEMVER-MINOR)** **deps**: patch V8 to run on older XCode versions (Ujjwal Sharma) [#32116](https://github.com/nodejs/node/pull/32116)
- [[`51d86f4b59`](https://github.com/nodejs/node/commit/51d86f4b59)] - **(SEMVER-MINOR)** **deps**: V8: un-cherry-pick bd019bd (Refael Ackermann) [#32116](https://github.com/nodejs/node/pull/32116)
- [[`9cd523d148`](https://github.com/nodejs/node/commit/9cd523d148)] - **(SEMVER-MINOR)** **deps**: update V8 to 8.4.371.19 (Michaël Zasso) [#33579](https://github.com/nodejs/node/pull/33579)
- [[`24f76cf004`](https://github.com/nodejs/node/commit/24f76cf004)] - **deps**: upgrade npm to 6.14.6 (claudiahdz) [#34246](https://github.com/nodejs/node/pull/34246)
- [[`a9ca4204e0`](https://github.com/nodejs/node/commit/a9ca4204e0)] - **deps**: upgrade to libuv 1.38.1 (Colin Ihrig) [#34187](https://github.com/nodejs/node/pull/34187)
- [[`601ed8ef7e`](https://github.com/nodejs/node/commit/601ed8ef7e)] - **deps**: V8: backport 2d5017a0fc02 (Benjamin Coe) [#34272](https://github.com/nodejs/node/pull/34272)
- [[`17174e69ce`](https://github.com/nodejs/node/commit/17174e69ce)] - **doc**: clarify conditional exports guidance (Guy Bedford) [#34306](https://github.com/nodejs/node/pull/34306)
- [[`1dd265384b`](https://github.com/nodejs/node/commit/1dd265384b)] - **doc**: reword warnings about sockets passed to subprocesses (Rich Trott) [#34273](https://github.com/nodejs/node/pull/34273)
- [[`ef31f179e0`](https://github.com/nodejs/node/commit/ef31f179e0)] - **doc**: sync deprecation numbers with v14.x (Myles Borins) [#34368](https://github.com/nodejs/node/pull/34368)
- [[`0b42e5d205`](https://github.com/nodejs/node/commit/0b42e5d205)] - **doc**: add danielleadams to collaborators (Danielle Adams) [#34360](https://github.com/nodejs/node/pull/34360)
- [[`1cc65332b0`](https://github.com/nodejs/node/commit/1cc65332b0)] - **doc**: buffer documentation improvements (James M Snell) [#34230](https://github.com/nodejs/node/pull/34230)
- [[`d11496174d`](https://github.com/nodejs/node/commit/d11496174d)] - **doc**: improve text in fs docs about omitting callbacks (Rich Trott) [#34307](https://github.com/nodejs/node/pull/34307)
- [[`d2c58948e9`](https://github.com/nodejs/node/commit/d2c58948e9)] - **doc**: add sxa as collaborator (Stewart X Addison) [#34338](https://github.com/nodejs/node/pull/34338)
- [[`d865be4cab`](https://github.com/nodejs/node/commit/d865be4cab)] - **doc**: move sebdeckers to emeritus (Rich Trott) [#34298](https://github.com/nodejs/node/pull/34298)
- [[`24fe55872f`](https://github.com/nodejs/node/commit/24fe55872f)] - **doc**: add ruyadorno to collaborators (Ruy Adorno) [#34297](https://github.com/nodejs/node/pull/34297)
- [[`e6776fe194`](https://github.com/nodejs/node/commit/e6776fe194)] - **doc**: move kfarnung to collaborator emeriti list (Rich Trott) [#34258](https://github.com/nodejs/node/pull/34258)
- [[`7416028f99`](https://github.com/nodejs/node/commit/7416028f99)] - **doc**: specify encoding in text/html examples (James M Snell) [#34222](https://github.com/nodejs/node/pull/34222)
- [[`9339f9f602`](https://github.com/nodejs/node/commit/9339f9f602)] - **doc**: document the ready event for Http2Stream (James M Snell) [#34221](https://github.com/nodejs/node/pull/34221)
- [[`25ac669be9`](https://github.com/nodejs/node/commit/25ac669be9)] - **doc**: add comment to example about 2xx status codes (James M Snell) [#34223](https://github.com/nodejs/node/pull/34223)
- [[`6f014d0b13`](https://github.com/nodejs/node/commit/6f014d0b13)] - **doc**: document that whitespace is ignored in base64 decoding (James M Snell) [#34227](https://github.com/nodejs/node/pull/34227)
- [[`431bfe177f`](https://github.com/nodejs/node/commit/431bfe177f)] - **doc**: add note about multiple sync events and once (James M Snell) [#34220](https://github.com/nodejs/node/pull/34220)
- [[`ffe6886de9`](https://github.com/nodejs/node/commit/ffe6886de9)] - **doc**: document behavior for once(ee, 'error') (James M Snell) [#34225](https://github.com/nodejs/node/pull/34225)
- [[`a6a656abaa`](https://github.com/nodejs/node/commit/a6a656abaa)] - **doc**: document security issues with url.parse() (James M Snell) [#34226](https://github.com/nodejs/node/pull/34226)
- [[`abfab9892b`](https://github.com/nodejs/node/commit/abfab9892b)] - **doc**: replace http to https of link urls (sapics) [#34158](https://github.com/nodejs/node/pull/34158)
- [[`2e20cd4fde`](https://github.com/nodejs/node/commit/2e20cd4fde)] - **doc**: remove errors that were never released (Rich Trott) [#34197](https://github.com/nodejs/node/pull/34197)
- [[`c83d98619d`](https://github.com/nodejs/node/commit/c83d98619d)] - **doc**: move ERR_FEATURE_UNAVAILABLE_ON_PLATFORM to current errors (Rich Trott) [#34196](https://github.com/nodejs/node/pull/34196)
- [[`59bb6d6663`](https://github.com/nodejs/node/commit/59bb6d6663)] - **doc**: move digitalinfinity to emeritus (Rich Trott) [#34191](https://github.com/nodejs/node/pull/34191)
- [[`39d6ecdea9`](https://github.com/nodejs/node/commit/39d6ecdea9)] - **doc**: move gibfahn to emeritus (Rich Trott) [#34190](https://github.com/nodejs/node/pull/34190)
- [[`938de338ef`](https://github.com/nodejs/node/commit/938de338ef)] - **doc**: specify how fs.WriteStream/ReadStreams are created (James M Snell) [#34188](https://github.com/nodejs/node/pull/34188)
- [[`326b854e6e`](https://github.com/nodejs/node/commit/326b854e6e)] - **doc**: remove parenthetical \\r\\n comment in http and http2 docs (Rich Trott) [#34178](https://github.com/nodejs/node/pull/34178)
- [[`a2dd2589c1`](https://github.com/nodejs/node/commit/a2dd2589c1)] - **doc**: remove stability from unreleased errors (Rich Trott) [#33764](https://github.com/nodejs/node/pull/33764)
- [[`8dd8b1a8be`](https://github.com/nodejs/node/commit/8dd8b1a8be)] - **doc**: util.debuglog callback (Bradley Meck) [#33856](https://github.com/nodejs/node/pull/33856)
- [[`aaba1c08dc`](https://github.com/nodejs/node/commit/aaba1c08dc)] - **doc**: update wording in "Two reading modes" (Julien Poissonnier) [#34119](https://github.com/nodejs/node/pull/34119)
- [[`6aa0dac362`](https://github.com/nodejs/node/commit/6aa0dac362)] - **doc**: clarify that the ctx argument is optional (Luigi Pinca) [#34097](https://github.com/nodejs/node/pull/34097)
- [[`1558800217`](https://github.com/nodejs/node/commit/1558800217)] - **doc**: add a reference to the list of OpenSSL flags. (Mateusz Krawczuk) [#34050](https://github.com/nodejs/node/pull/34050)
- [[`25d310b631`](https://github.com/nodejs/node/commit/25d310b631)] - **doc**: no longer maintain a CNA structure (Sam Roberts) [#33639](https://github.com/nodejs/node/pull/33639)
- [[`5ae2b74350`](https://github.com/nodejs/node/commit/5ae2b74350)] - **doc**: use consistent naming in stream doc (Saleem) [#30506](https://github.com/nodejs/node/pull/30506)
- [[`a0cfa62338`](https://github.com/nodejs/node/commit/a0cfa62338)] - **doc**: clarify how to read process.stdin (Anentropic) [#27350](https://github.com/nodejs/node/pull/27350)
- [[`e8184554ba`](https://github.com/nodejs/node/commit/e8184554ba)] - **doc**: fix entry for `napi\_create\_external\_buffer` (Gabriel Schulhof) [#34125](https://github.com/nodejs/node/pull/34125)
- [[`167a21a66a`](https://github.com/nodejs/node/commit/167a21a66a)] - **doc**: fix source link margin to sub-header mark (Rodion Abdurakhimov) [#33664](https://github.com/nodejs/node/pull/33664)
- [[`146538de65`](https://github.com/nodejs/node/commit/146538de65)] - **doc**: improve async_hooks asynchronous context example (Denys Otrishko) [#33730](https://github.com/nodejs/node/pull/33730)
- [[`e386188775`](https://github.com/nodejs/node/commit/e386188775)] - **doc**: clarify esm conditional exports prose (Derek Lewis) [#33886](https://github.com/nodejs/node/pull/33886)
- [[`e273edf943`](https://github.com/nodejs/node/commit/e273edf943)] - **doc**: Add maxTotalSockets option to agent constructor (rickyes) [#34013](https://github.com/nodejs/node/pull/34013)
- [[`ab6b786e9d`](https://github.com/nodejs/node/commit/ab6b786e9d)] - **doc**: add streams to the pipeline function signature (rickyes) [#34153](https://github.com/nodejs/node/pull/34153)
- [[`9f0bf5c9e1`](https://github.com/nodejs/node/commit/9f0bf5c9e1)] - **doc**: improve triaging text in issues.md (Rich Trott) [#34164](https://github.com/nodejs/node/pull/34164)
- [[`22c1fbf4cb`](https://github.com/nodejs/node/commit/22c1fbf4cb)] - **doc**: simply dns.ADDRCONFIG language (Rich Trott) [#34155](https://github.com/nodejs/node/pull/34155)
- [[`7fc56ebd0d`](https://github.com/nodejs/node/commit/7fc56ebd0d)] - **doc**: remove "considered" in errors.md (Rich Trott) [#34152](https://github.com/nodejs/node/pull/34152)
- [[`e33c09cb3a`](https://github.com/nodejs/node/commit/e33c09cb3a)] - **doc**: simplify and clarify ReferenceError material in errors.md (Rich Trott) [#34151](https://github.com/nodejs/node/pull/34151)
- [[`af9e6f6e1b`](https://github.com/nodejs/node/commit/af9e6f6e1b)] - **doc**: add http highlight grammar (Derek Lewis) [#33785](https://github.com/nodejs/node/pull/33785)
- [[`26ecdf8ade`](https://github.com/nodejs/node/commit/26ecdf8ade)] - **doc**: move sam-github to TSC Emeriti (Sam Roberts) [#34095](https://github.com/nodejs/node/pull/34095)
- [[`78a4d97b82`](https://github.com/nodejs/node/commit/78a4d97b82)] - **doc**: change "considered experimental" to "experimental" in n-api.md (Rich Trott) [#34129](https://github.com/nodejs/node/pull/34129)
- [[`da5fde6594`](https://github.com/nodejs/node/commit/da5fde6594)] - **doc**: changed "considered experimental" to "experimental" in cli.md (Rich Trott) [#34128](https://github.com/nodejs/node/pull/34128)
- [[`49d2d49336`](https://github.com/nodejs/node/commit/49d2d49336)] - **doc**: improve text in issues.md (falguniraina) [#33973](https://github.com/nodejs/node/pull/33973)
- [[`9d30f0542c`](https://github.com/nodejs/node/commit/9d30f0542c)] - **doc**: change "currently not considered public" to "not supported" (Rich Trott) [#34114](https://github.com/nodejs/node/pull/34114)
- [[`64bd518f26`](https://github.com/nodejs/node/commit/64bd518f26)] - **doc**: clarify that APIs are no longer experimental (Rich Trott) [#34113](https://github.com/nodejs/node/pull/34113)
- [[`ee6ccef091`](https://github.com/nodejs/node/commit/ee6ccef091)] - **doc**: clarify O_EXCL text in fs.md (Rich Trott) [#34096](https://github.com/nodejs/node/pull/34096)
- [[`05a69e2e88`](https://github.com/nodejs/node/commit/05a69e2e88)] - **doc**: clarify ambiguous rdev description (Rich Trott) [#34094](https://github.com/nodejs/node/pull/34094)
- [[`4927fed9ea`](https://github.com/nodejs/node/commit/4927fed9ea)] - **doc**: make minor improvements to paragraph in child_process.md (Rich Trott) [#34063](https://github.com/nodejs/node/pull/34063)
- [[`585f3a5f84`](https://github.com/nodejs/node/commit/585f3a5f84)] - **doc**: improve paragraph in esm.md (Rich Trott) [#34064](https://github.com/nodejs/node/pull/34064)
- [[`556e55db72`](https://github.com/nodejs/node/commit/556e55db72)] - **doc**: clarify require/import mutual exclusivity (Guy Bedford) [#33832](https://github.com/nodejs/node/pull/33832)
- [[`eb04ba3080`](https://github.com/nodejs/node/commit/eb04ba3080)] - **doc**: add dynamic source code links (Alec Davidson) [#33996](https://github.com/nodejs/node/pull/33996)
- [[`2ca6a45ba9`](https://github.com/nodejs/node/commit/2ca6a45ba9)] - **doc**: mention errors thrown by methods called on an unbound dgram.Socket (Mateusz Krawczuk) [#33983](https://github.com/nodejs/node/pull/33983)
- [[`b8a17ccc9a`](https://github.com/nodejs/node/commit/b8a17ccc9a)] - **doc**: document n-api callback scope usage (Gabriel Schulhof) [#33915](https://github.com/nodejs/node/pull/33915)
- [[`3b268094cc`](https://github.com/nodejs/node/commit/3b268094cc)] - **doc**: use sentence-case for headings in docs (Rich Trott) [#33889](https://github.com/nodejs/node/pull/33889)
- [[`280cd967d3`](https://github.com/nodejs/node/commit/280cd967d3)] - **domain**: fix unintentional deprecation warning (Anna Henningsen) [#34245](https://github.com/nodejs/node/pull/34245)
- [[`96ebd5f352`](https://github.com/nodejs/node/commit/96ebd5f352)] - **http**: add note about timer unref (Robert Nagy) [#34143](https://github.com/nodejs/node/pull/34143)
- [[`16160e654f`](https://github.com/nodejs/node/commit/16160e654f)] - **_Revert_** "**http2**: streamline OnStreamRead streamline memory accounting" (Rich Trott) [#34315](https://github.com/nodejs/node/pull/34315)
- [[`8bafba2e56`](https://github.com/nodejs/node/commit/8bafba2e56)] - **lib**: always initialize esm loader callbackMap (Shelley Vohr) [#34127](https://github.com/nodejs/node/pull/34127)
- [[`daf2abf393`](https://github.com/nodejs/node/commit/daf2abf393)] - **lib**: replace http to https of comment link urls (sapics) [#34158](https://github.com/nodejs/node/pull/34158)
- [[`8f8d16849c`](https://github.com/nodejs/node/commit/8f8d16849c)] - **meta**: make issue template mobile friendly and address nits (Derek Lewis) [#34243](https://github.com/nodejs/node/pull/34243)
- [[`de58eb6286`](https://github.com/nodejs/node/commit/de58eb6286)] - **meta**: add N-API to codeowners coverage (Michael Dawson) [#34039](https://github.com/nodejs/node/pull/34039)
- [[`4dc89c6d30`](https://github.com/nodejs/node/commit/4dc89c6d30)] - **meta**: fixup CODEOWNERS so it hopefully works (James M Snell) [#34147](https://github.com/nodejs/node/pull/34147)
- [[`8d7330be0e`](https://github.com/nodejs/node/commit/8d7330be0e)] - **(SEMVER-MINOR)** **module**: deprecate module.parent (Antoine du HAMEL) [#32217](https://github.com/nodejs/node/pull/32217)
- [[`1ae76bd075`](https://github.com/nodejs/node/commit/1ae76bd075)] - **(SEMVER-MINOR)** **module**: package "imports" field (Guy Bedford) [#34117](https://github.com/nodejs/node/pull/34117)
- [[`0e1361cb8b`](https://github.com/nodejs/node/commit/0e1361cb8b)] - **net**: doc deprecate bufferSize (Robert Nagy) [#34088](https://github.com/nodejs/node/pull/34088)
- [[`b7e9b43b2f`](https://github.com/nodejs/node/commit/b7e9b43b2f)] - **net**: fix bufferSize (Robert Nagy) [#34088](https://github.com/nodejs/node/pull/34088)
- [[`02ea320e0c`](https://github.com/nodejs/node/commit/02ea320e0c)] - **policy**: add startup benchmark and make SRI lazier (Bradley Farias) [#29527](https://github.com/nodejs/node/pull/29527)
- [[`73d6792a05`](https://github.com/nodejs/node/commit/73d6792a05)] - **repl**: support --loader option in builtin REPL (Michaël Zasso) [#33437](https://github.com/nodejs/node/pull/33437)
- [[`b20e6ed94e`](https://github.com/nodejs/node/commit/b20e6ed94e)] - **repl**: fix verb conjugation in deprecation message (Rich Trott) [#34198](https://github.com/nodejs/node/pull/34198)
- [[`b878e3223e`](https://github.com/nodejs/node/commit/b878e3223e)] - **src**: add callback scope for native immediates (Anna Henningsen) [#34366](https://github.com/nodejs/node/pull/34366)
- [[`0f6805d507`](https://github.com/nodejs/node/commit/0f6805d507)] - **(SEMVER-MINOR)** **src**: add option to track unmanaged file descriptors (Anna Henningsen) [#34303](https://github.com/nodejs/node/pull/34303)
- [[`e4c7b59665`](https://github.com/nodejs/node/commit/e4c7b59665)] - **(SEMVER-MINOR)** **src**: allow embedders to disable esm loader (Shelley Vohr) [#34060](https://github.com/nodejs/node/pull/34060)
- [[`9c12e53d47`](https://github.com/nodejs/node/commit/9c12e53d47)] - **src**: remove redundant snprintf (Anna Henningsen) [#34282](https://github.com/nodejs/node/pull/34282)
- [[`844bf770f8`](https://github.com/nodejs/node/commit/844bf770f8)] - **src**: use FromMaybe instead of ToLocal in GetCert (Daniel Bevenius) [#34276](https://github.com/nodejs/node/pull/34276)
- [[`ec876eecc0`](https://github.com/nodejs/node/commit/ec876eecc0)] - **src**: add GetCipherValue function (Daniel Bevenius) [#34287](https://github.com/nodejs/node/pull/34287)
- [[`9c98af71db`](https://github.com/nodejs/node/commit/9c98af71db)] - **src**: exit explicitly after printing V8 help (Anna Henningsen) [#34136](https://github.com/nodejs/node/pull/34136)
- [[`3e3d908c81`](https://github.com/nodejs/node/commit/3e3d908c81)] - **src**: add encoding_type variable in WritePrivateKey (Daniel Bevenius) [#34181](https://github.com/nodejs/node/pull/34181)
- [[`ed0f5697d8`](https://github.com/nodejs/node/commit/ed0f5697d8)] - **src**: fix minor comment typo in KeyObjectData (Daniel Bevenius) [#34167](https://github.com/nodejs/node/pull/34167)
- [[`8f7ed40fc4`](https://github.com/nodejs/node/commit/8f7ed40fc4)] - **src**: fix unused namespace member (Nikola Glavina) [#34212](https://github.com/nodejs/node/pull/34212)
- [[`e378b681d0`](https://github.com/nodejs/node/commit/e378b681d0)] - **src**: remove unused fields from IsolateData (Anna Henningsen) [#34139](https://github.com/nodejs/node/pull/34139)
- [[`b2cd87e611`](https://github.com/nodejs/node/commit/b2cd87e611)] - **src,doc,test**: remove String::New default parameter (Anna Henningsen) [#34248](https://github.com/nodejs/node/pull/34248)
- [[`41c80f6abe`](https://github.com/nodejs/node/commit/41c80f6abe)] - **stream**: destroy wrapped streams on error (Robert Nagy) [#34102](https://github.com/nodejs/node/pull/34102)
- [[`1af8943622`](https://github.com/nodejs/node/commit/1af8943622)] - **(SEMVER-MINOR)** **test**: remove test/v8-updates/test-postmortem-metadata.js (Colin Ihrig) [#33579](https://github.com/nodejs/node/pull/33579)
- [[`58dfeac133`](https://github.com/nodejs/node/commit/58dfeac133)] - **test**: use mustCall() in pummel test (Rich Trott) [#34327](https://github.com/nodejs/node/pull/34327)
- [[`28ce378e17`](https://github.com/nodejs/node/commit/28ce378e17)] - **test**: fix flaky test-http2-reset-flood (Rich Trott) [#34318](https://github.com/nodejs/node/pull/34318)
- [[`060c95a3b1`](https://github.com/nodejs/node/commit/060c95a3b1)] - **test**: add n-api null checks for conversions (Gabriel Schulhof) [#34142](https://github.com/nodejs/node/pull/34142)
- [[`3ee8f5342c`](https://github.com/nodejs/node/commit/3ee8f5342c)] - **test**: add regression tests for HTTP parser crash (Anna Henningsen) [#34250](https://github.com/nodejs/node/pull/34250)
- [[`6925ef3b1c`](https://github.com/nodejs/node/commit/6925ef3b1c)] - **test**: add WASI test for file resizing (Colin Ihrig) [#31617](https://github.com/nodejs/node/pull/31617)
- [[`1aad61eeec`](https://github.com/nodejs/node/commit/1aad61eeec)] - **test**: add issue ref for known_issues test (Rich Trott) [#34267](https://github.com/nodejs/node/pull/34267)
- [[`ec9b49a9b9`](https://github.com/nodejs/node/commit/ec9b49a9b9)] - **test**: add known issue for fs.open() keeping event loop open (Rich Trott) [#34228](https://github.com/nodejs/node/pull/34228)
- [[`38b3c2a300`](https://github.com/nodejs/node/commit/38b3c2a300)] - **test**: add arrayOfStreams to pipeline (rickyes) [#34156](https://github.com/nodejs/node/pull/34156)
- [[`0f9bafd03d`](https://github.com/nodejs/node/commit/0f9bafd03d)] - **test**: skip an ipv6 test on IBM i (Xu Meng) [#34209](https://github.com/nodejs/node/pull/34209)
- [[`a38219f962`](https://github.com/nodejs/node/commit/a38219f962)] - **test**: add regression test for C++-created Buffer transfer (Anna Henningsen) [#34140](https://github.com/nodejs/node/pull/34140)
- [[`09faebd9ad`](https://github.com/nodejs/node/commit/09faebd9ad)] - **test**: replace deprecated function call from test-repl-history-navigation (Rich Trott) [#34199](https://github.com/nodejs/node/pull/34199)
- [[`bddc99ec7f`](https://github.com/nodejs/node/commit/bddc99ec7f)] - **test**: skip some IBM i unsupported test cases (Xu Meng) [#34118](https://github.com/nodejs/node/pull/34118)
- [[`f5691fa6b6`](https://github.com/nodejs/node/commit/f5691fa6b6)] - **test**: report actual error code on failure (Richard Lau) [#34134](https://github.com/nodejs/node/pull/34134)
- [[`46d183c86e`](https://github.com/nodejs/node/commit/46d183c86e)] - **test**: update test-child-process-spawn-loop for Python 3 (Richard Lau) [#34071](https://github.com/nodejs/node/pull/34071)
- [[`a89bcf72fb`](https://github.com/nodejs/node/commit/a89bcf72fb)] - **(SEMVER-MINOR)** **tls**: make 'createSecureContext' honor more options (Mateusz Krawczuk) [#33974](https://github.com/nodejs/node/pull/33974)
- [[`fbcd1fa0f4`](https://github.com/nodejs/node/commit/fbcd1fa0f4)] - **tls**: remove unnecessary close listener (Robert Nagy) [#34105](https://github.com/nodejs/node/pull/34105)
- [[`4e2fa439c9`](https://github.com/nodejs/node/commit/4e2fa439c9)] - **(SEMVER-MINOR)** **tools**: update V8 gypfiles for 8.4 (Ujjwal Sharma) [#33579](https://github.com/nodejs/node/pull/33579)
- [[`440642d00b`](https://github.com/nodejs/node/commit/440642d00b)] - **tools**: remove lint-js.js (Rich Trott) [#30955](https://github.com/nodejs/node/pull/30955)
- [[`e0206bafe6`](https://github.com/nodejs/node/commit/e0206bafe6)] - **util**: restrict custom inspect function + vm.Context interaction (Anna Henningsen) [#33690](https://github.com/nodejs/node/pull/33690)
- [[`70c4045aa5`](https://github.com/nodejs/node/commit/70c4045aa5)] - **(SEMVER-MINOR)** **vm**: add run-after-evaluate microtask mode (Anna Henningsen) [#34023](https://github.com/nodejs/node/pull/34023)
- [[`6be685a99d`](https://github.com/nodejs/node/commit/6be685a99d)] - **wasi**: add reactor support (Gus Caplan) [#34046](https://github.com/nodejs/node/pull/34046)
- [[`1bc4def18f`](https://github.com/nodejs/node/commit/1bc4def18f)] - **worker**: fix nested uncaught exception handling (Anna Henningsen) [#34310](https://github.com/nodejs/node/pull/34310)
- [[`9e04070d3c`](https://github.com/nodejs/node/commit/9e04070d3c)] - **(SEMVER-MINOR)** **worker**: add option to track unmanaged file descriptors (Anna Henningsen) [#34303](https://github.com/nodejs/node/pull/34303)
- [[`105d5607a8`](https://github.com/nodejs/node/commit/105d5607a8)] - **zlib**: remove redundant variable in zlibBufferOnEnd (Andrey Pechkurov) [#34072](https://github.com/nodejs/node/pull/34072)

Windows 32-bit Installer: https://nodejs.org/dist/v14.6.0/node-v14.6.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v14.6.0/node-v14.6.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v14.6.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v14.6.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v14.6.0/node-v14.6.0.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v14.6.0/node-v14.6.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v14.6.0/node-v14.6.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v14.6.0/node-v14.6.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v14.6.0/node-v14.6.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v14.6.0/node-v14.6.0-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v14.6.0/node-v14.6.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v14.6.0/node-v14.6.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v14.6.0/node-v14.6.0.tar.gz \
Other release files: https://nodejs.org/dist/v14.6.0/ \
Documentation: https://nodejs.org/docs/v14.6.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

5a42400906c4e748930a90ee1137639876839394b5ebab4dbea96eac2fa32be7  node-v14.6.0-aix-ppc64.tar.gz
7907a18605b900ce977ff4c7e67f7507f937f85738659865d31779c3b2990756  node-v14.6.0-darwin-x64.tar.gz
a5a9c0acaa0fb6b24c1f112b647515abf15fb6d6403d5d7e16a7c3ec35dc8a7c  node-v14.6.0-darwin-x64.tar.xz
22f398697efbe077c00791a03ec462a0169f6b2452b5dbfadda35135e1af162b  node-v14.6.0-headers.tar.gz
3d8f8f092935718e9b1883c9b869ab1f3d41b79e62ff00382b766fa4fcaf1b97  node-v14.6.0-headers.tar.xz
eb4f98efe22057a831415c2367416330878f0e1ad9a9bb5c25a6631031588075  node-v14.6.0-linux-arm64.tar.gz
853f2c99f0e5fa65e6f222821e29c9d1a49f2d358a97c1de080ddf5c74e4ec70  node-v14.6.0-linux-arm64.tar.xz
9e4ff9421102e61381133f71b536305a5f6befa6de62a44c4805f23fc3cb7b0c  node-v14.6.0-linux-armv7l.tar.gz
385f1adeef5c3c101d0c6bd6ba53e2dc82b37e87d5a44a57526b777251166e7a  node-v14.6.0-linux-armv7l.tar.xz
2cfac862890b8c70a0b74aa5f31a5d51571ec1cbe91de46fc82b079b7bde9bec  node-v14.6.0-linux-ppc64le.tar.gz
2299703301b3b69d386cd4c3c2721c102798ae2e9ea1debc2218452883c08955  node-v14.6.0-linux-ppc64le.tar.xz
b1114f7360babb5eca8e27fbc2556e9e73f0d423281dbe292f8d521b21359faf  node-v14.6.0-linux-s390x.tar.gz
0ff3193e5bd1e4101f64507a743faed7630140e2118fb986a89c50ed37777c34  node-v14.6.0-linux-s390x.tar.xz
5e2c59200c86c37a0c800fe2cd2cfabc459f8a3ae3f83c3611483c485ad32e4f  node-v14.6.0-linux-x64.tar.gz
b8a39b2dac8e200e96586356c5525d20b0b43dba8bf9f7eb4e8c2d5366be2bb2  node-v14.6.0-linux-x64.tar.xz
1db15bee99fa98574f42564c3aa821561816f116b4274b442a288cbcd0e309d4  node-v14.6.0.pkg
bc978486f602dc99f0075514f1cd860b286077d140b2d7de308343ff07907f51  node-v14.6.0.tar.gz
5af300c736088ce8ecabc64ea036ccbf56cf6d072c7374985e7cd7ecdc016a94  node-v14.6.0.tar.xz
078407a38f747e7d2a1ff2765f44ce2b8bc9aa040c28e35b28b0ae0dffbde50f  node-v14.6.0-win-x64.7z
57ea75a7ec70cc8158e6f9774f9728fb9e3d08212b1af3e206db2de46ca304ca  node-v14.6.0-win-x64.zip
79d213d67028e3dfb9a1d3db26d1f0c6ad0967276fc794b8a4544c23061c9fc7  node-v14.6.0-win-x86.7z
d7244b00e19404e74e33b2df1f066ec8260efc2bf312b9dfb3470878ac0f6af6  node-v14.6.0-win-x86.zip
fe8e714b0fc5260e07185655845d21c4fff3e8bdd09512b041834ee2aea13c06  node-v14.6.0-x64.msi
4c539834431cadaed92f5daae2f7a16551754e55e9129939db2aba8a5e7ad28d  node-v14.6.0-x86.msi
bd2b3e7f2c3506b972127c26c6a9d692443c316c759c8eaa5a5b4a73a6d6f175  win-x64/node.exe
203ffb5c4e9351e762af1cbaca1e08cef784784c665a60657d26d10ad200a65c  win-x64/node.lib
31ae200486e50b572b2b20b60cc05b6652e2f992cd46b7f2bd732a5fb1bd88a4  win-x64/node_pdb.7z
6c245a0ab6c4dd77bb8f43d7484885706532ec4123567fd55be4b5a03c484b6c  win-x64/node_pdb.zip
6c946d449e8f488c9e61abc4e9ac92c24beb80b76abff8d86bb9aa5ddac31a32  win-x86/node.exe
49b13b05e148d6593269174efc0e537b4bc57290b2703658a26f037a398307b4  win-x86/node.lib
248ba0c3dbff14bbcf3ac3290c019d6211547efde0948554b07bef30fafa8ca9  win-x86/node_pdb.7z
458aee171c6cfdc569264f56d77e68f4d2068a28a9de6c921022357072766d97  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQEzBAEBCAAdFiEEDv/hvO/ZyE49CYFSkzsB9AtcqUYFAl8XYh8ACgkQkzsB9Atc
qUZdswgAhGcIvV5JdUxDvl0YIcWScLaj66DHef1pp1hifa0n+vD3znL5bBFQ2D6O
jxXZ8tIOpunB8is/s44eMRacml77b7OAzTXv1TE6mR2LZG6mP8Edw4nKJbAsCaTP
ySJC/ag9FNPS5V84yjaFXgQCy9+HhNahQabruZG8rJEx16cLIGfE5pKOYeOgbMhY
E0lZkgBERhS6tnBRXPOGdSCKb4nOdhuKZ1cCdj81DMI5UcN7miKY0Vb18aNYrv9w
o1edNeX6rzvfETElINIYCO9QQ5OnrLCqpujZ4QFHIh+z2nzOfUgh22W5o6wGOyOV
bsljthaA0nFMcsXQLxCM7bWtq1KxvQ==
=TfA7
-----END PGP SIGNATURE-----

```
