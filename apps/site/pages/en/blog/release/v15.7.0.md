---
date: '2021-01-26T16:19:24.433Z'
category: release
title: Node v15.7.0 (Current)
layout: blog-post
author: <PERSON><PERSON>
---

### Notable changes

- **buffer**:
  - introduce <PERSON><PERSON>b (<PERSON>) [#36811](https://github.com/nodejs/node/pull/36811)
  - add base64url encoding option (<PERSON><PERSON>) [#36952](https://github.com/nodejs/node/pull/36952)
- **doc**:
  - add @iansu to collaborators (<PERSON>) [#36951](https://github.com/nodejs/node/pull/36951)
  - add @<PERSON><PERSON><PERSON><PERSON> to collaborators (<PERSON><PERSON> Sen) [#36998](https://github.com/nodejs/node/pull/36998)
  - add @milad<PERSON><PERSON> to collaborators (Milad Fa) [#36934](https://github.com/nodejs/node/pull/36934)
- **fs**:
  - allow `position` parameter to be a `BigInt` in read and readSync (raisinten) [#36190](https://github.com/nodejs/node/pull/36190)
- **http**:
  - attach request as res.req (<PERSON>) [#36505](https://github.com/nodejs/node/pull/36505)
  - expose urlToHttpOptions utility (Yongsheng Zhang) [#35960](https://github.com/nodejs/node/pull/35960)

### Commits

- [[`775b34b822`](https://github.com/nodejs/node/commit/775b34b822)] - **(SEMVER-MINOR)** **buffer**: introduce Blob (James M Snell) [#36811](https://github.com/nodejs/node/pull/36811)
- [[`832cd015d5`](https://github.com/nodejs/node/commit/832cd015d5)] - **(SEMVER-MINOR)** **buffer**: add base64url encoding option (Filip Skokan) [#36952](https://github.com/nodejs/node/pull/36952)
- [[`7ce7404f79`](https://github.com/nodejs/node/commit/7ce7404f79)] - **build**: fix compiling against openssl with no-psk (Caleb ツ Everett) [#36881](https://github.com/nodejs/node/pull/36881)
- [[`b7d8e61ef1`](https://github.com/nodejs/node/commit/b7d8e61ef1)] - **crypto**: fix randomInt bias (Tobias Nießen) [#36894](https://github.com/nodejs/node/pull/36894)
- [[`1149af6265`](https://github.com/nodejs/node/commit/1149af6265)] - **(SEMVER-MINOR)** **crypto**: add keyObject.asymmetricKeyDetails for asymmetric keys (Filip Skokan) [#36188](https://github.com/nodejs/node/pull/36188)
- [[`0398167b35`](https://github.com/nodejs/node/commit/0398167b35)] - **crypto**: fix WebCrypto import of RSA-PSS keys (Tobias Nießen) [#36877](https://github.com/nodejs/node/pull/36877)
- [[`e52e860172`](https://github.com/nodejs/node/commit/e52e860172)] - **deps**: upgrade npm to 7.4.3 (Ruy Adorno) [#37018](https://github.com/nodejs/node/pull/37018)
- [[`ef3a5f6958`](https://github.com/nodejs/node/commit/ef3a5f6958)] - **deps**: update ICU to 68.2 (Michaël Zasso) [#36980](https://github.com/nodejs/node/pull/36980)
- [[`ca479b9e9d`](https://github.com/nodejs/node/commit/ca479b9e9d)] - **deps**: V8: cherry-pick fe191e8d05cc (Benjamin Coe) [#36956](https://github.com/nodejs/node/pull/36956)
- [[`6f773fbe84`](https://github.com/nodejs/node/commit/6f773fbe84)] - **deps**: upgrade npm to 7.4.2 (Ruy Adorno) [#36953](https://github.com/nodejs/node/pull/36953)
- [[`4b952d8d3e`](https://github.com/nodejs/node/commit/4b952d8d3e)] - **doc**: fix maintaining ICU guide (Michaël Zasso) [#36980](https://github.com/nodejs/node/pull/36980)
- [[`a2559b9044`](https://github.com/nodejs/node/commit/a2559b9044)] - **doc**: add @RaisinTen to collaborators (Darshan Sen) [#36998](https://github.com/nodejs/node/pull/36998)
- [[`4d5273b156`](https://github.com/nodejs/node/commit/4d5273b156)] - **doc**: fix typo in http.server.requestTimout docs (alexbs) [#36987](https://github.com/nodejs/node/pull/36987)
- [[`93fc295b75`](https://github.com/nodejs/node/commit/93fc295b75)] - **doc**: add performance notes for fs.readFile (James M Snell) [#36880](https://github.com/nodejs/node/pull/36880)
- [[`7ea374b159`](https://github.com/nodejs/node/commit/7ea374b159)] - **doc**: clarify maxSockets option of http.Agent (Pooja D P) [#36941](https://github.com/nodejs/node/pull/36941)
- [[`f3637d5328`](https://github.com/nodejs/node/commit/f3637d5328)] - **doc**: remove pull-requests.md preamble (Rich Trott) [#36960](https://github.com/nodejs/node/pull/36960)
- [[`d2d9ad7477`](https://github.com/nodejs/node/commit/d2d9ad7477)] - **doc**: fix module.isPreloading documentation (Antoine du Hamel) [#36944](https://github.com/nodejs/node/pull/36944)
- [[`48b6781151`](https://github.com/nodejs/node/commit/48b6781151)] - **doc**: fix crypto.generateKeySync aes allowed length list (Filip Skokan) [#36928](https://github.com/nodejs/node/pull/36928)
- [[`120db2c169`](https://github.com/nodejs/node/commit/120db2c169)] - **doc**: fix grammar and link to QUIC in changelog (Dan Dascalescu) [#36959](https://github.com/nodejs/node/pull/36959)
- [[`af0f0a0f65`](https://github.com/nodejs/node/commit/af0f0a0f65)] - **doc**: fix percentile range in perf_hooks.md (raisinten) [#36938](https://github.com/nodejs/node/pull/36938)
- [[`8cf280d9ab`](https://github.com/nodejs/node/commit/8cf280d9ab)] - **doc**: improve perf_hooks docs (Juan José Arboleda) [#36909](https://github.com/nodejs/node/pull/36909)
- [[`3ea37c2d67`](https://github.com/nodejs/node/commit/3ea37c2d67)] - **doc**: fix invalid HTML in doc template (Rich Trott) [#36930](https://github.com/nodejs/node/pull/36930)
- [[`eaf378aa46`](https://github.com/nodejs/node/commit/eaf378aa46)] - **doc**: remove issue template duplication from contributing docs (Rich Trott) [#36908](https://github.com/nodejs/node/pull/36908)
- [[`7a794417f3`](https://github.com/nodejs/node/commit/7a794417f3)] - **doc**: remove resolving-a-bug-report from contributing docs (Rich Trott) [#36905](https://github.com/nodejs/node/pull/36905)
- [[`707b97307d`](https://github.com/nodejs/node/commit/707b97307d)] - **doc**: use ESM syntax for WASI example (Antoine du Hamel) [#36848](https://github.com/nodejs/node/pull/36848)
- [[`5a9a07e7cd`](https://github.com/nodejs/node/commit/5a9a07e7cd)] - **doc**: add iansu to collaborators (Ian Sutherland) [#36951](https://github.com/nodejs/node/pull/36951)
- [[`aa3bc74cd6`](https://github.com/nodejs/node/commit/aa3bc74cd6)] - **doc**: fixup typo in metadata entry (James M Snell) [#36947](https://github.com/nodejs/node/pull/36947)
- [[`22e29ccfa3`](https://github.com/nodejs/node/commit/22e29ccfa3)] - **doc**: add alternative version links to the packages page (Filip Skokan) [#36915](https://github.com/nodejs/node/pull/36915)
- [[`80c84a1136`](https://github.com/nodejs/node/commit/80c84a1136)] - **doc**: add miladfarca to collaborators (Milad Fa) [#36934](https://github.com/nodejs/node/pull/36934)
- [[`e73b1072f3`](https://github.com/nodejs/node/commit/e73b1072f3)] - **doc**: update tls test to use better terminology (Michael Dawson) [#36851](https://github.com/nodejs/node/pull/36851)
- [[`5cbf638c06`](https://github.com/nodejs/node/commit/5cbf638c06)] - **doc**: remove unnecessary contributing.md section (Rich Trott) [#36891](https://github.com/nodejs/node/pull/36891)
- [[`f99b38fedd`](https://github.com/nodejs/node/commit/f99b38fedd)] - **doc**: wrap TOC in a \<details\> tag (Mattia Pontonio) [#36896](https://github.com/nodejs/node/pull/36896)
- [[`82eccddf1e`](https://github.com/nodejs/node/commit/82eccddf1e)] - **doc**: update fs.l/statSync API history for throwIfNoEntry (Andrew Casey) [#36882](https://github.com/nodejs/node/pull/36882)
- [[`70cd43c32e`](https://github.com/nodejs/node/commit/70cd43c32e)] - **doc**: change "it's" to "its" where necessary (Tobias Nießen) [#36913](https://github.com/nodejs/node/pull/36913)
- [[`02a8f52040`](https://github.com/nodejs/node/commit/02a8f52040)] - **doc**: fix indentation on http2 doc entry (Rich Trott) [#36869](https://github.com/nodejs/node/pull/36869)
- [[`dc596d0607`](https://github.com/nodejs/node/commit/dc596d0607)] - **events**: remove error listener on signal abort (ZiJian Liu) [#36969](https://github.com/nodejs/node/pull/36969)
- [[`c4cdf1d830`](https://github.com/nodejs/node/commit/c4cdf1d830)] - **(SEMVER-MINOR)** **fs**: allow `position` parameter to be a `BigInt` in read and readSync (raisinten) [#36190](https://github.com/nodejs/node/pull/36190)
- [[`70ee7dce62`](https://github.com/nodejs/node/commit/70ee7dce62)] - **(SEMVER-MINOR)** **http**: attach request as res.req (Ian Storm Taylor) [#36505](https://github.com/nodejs/node/pull/36505)
- [[`f07e1c9d03`](https://github.com/nodejs/node/commit/f07e1c9d03)] - **http**: abortIncoming only on socket close (Robert Nagy) [#36821](https://github.com/nodejs/node/pull/36821)
- [[`aa7243e3d4`](https://github.com/nodejs/node/commit/aa7243e3d4)] - **http**: refactor ClientRequest destroy (Robert Nagy) [#36863](https://github.com/nodejs/node/pull/36863)
- [[`80051abfcb`](https://github.com/nodejs/node/commit/80051abfcb)] - **http**: cleanup ClientRequest oncreate (Robert Nagy) [#36862](https://github.com/nodejs/node/pull/36862)
- [[`f5b8e7b068`](https://github.com/nodejs/node/commit/f5b8e7b068)] - **http2**: refactor to avoid unsafe array iteration (Antoine du Hamel) [#36700](https://github.com/nodejs/node/pull/36700)
- [[`8aeba3cb92`](https://github.com/nodejs/node/commit/8aeba3cb92)] - **lib**: refactor to use validateArray (ZiJian Liu) [#36982](https://github.com/nodejs/node/pull/36982)
- [[`743dd8f89d`](https://github.com/nodejs/node/commit/743dd8f89d)] - **lib**: remove non used getter in `lib/perf\_hooks.js` (Juan José Arboleda) [#36907](https://github.com/nodejs/node/pull/36907)
- [[`f2ac4bb8e2`](https://github.com/nodejs/node/commit/f2ac4bb8e2)] - **lib**: expose primordials object (Antoine du Hamel) [#36872](https://github.com/nodejs/node/pull/36872)
- [[`850d3578b6`](https://github.com/nodejs/node/commit/850d3578b6)] - **lib**: refactor `primordials.makeSafe` to use more primordials (ExE Boss) [#36865](https://github.com/nodejs/node/pull/36865)
- [[`b86c48cc91`](https://github.com/nodejs/node/commit/b86c48cc91)] - **lib**: refactor source_map to use more primordials (Antoine du Hamel) [#36733](https://github.com/nodejs/node/pull/36733)
- [[`1ef92f61fa`](https://github.com/nodejs/node/commit/1ef92f61fa)] - **lib**: refactor source_map to avoid unsafe array iteration (Antoine du Hamel) [#36734](https://github.com/nodejs/node/pull/36734)
- [[`5290d63e7f`](https://github.com/nodejs/node/commit/5290d63e7f)] - **module**: simplify tryStatSync with throwIfNoEntry option (Antoine du Hamel) [#36971](https://github.com/nodejs/node/pull/36971)
- [[`89a7941425`](https://github.com/nodejs/node/commit/89a7941425)] - **os**: performance improvement in vector allocation (Yash Ladha) [#36748](https://github.com/nodejs/node/pull/36748)
- [[`3f75a60b51`](https://github.com/nodejs/node/commit/3f75a60b51)] - **perf_hooks**: throw ERR_INVALID_ARG_VALUE if histogram.percentile param is NaN (ZiJian Liu) [#36937](https://github.com/nodejs/node/pull/36937)
- [[`9951daefbd`](https://github.com/nodejs/node/commit/9951daefbd)] - **repl**: refactor to avoid unsafe array iteration (raisinten) [#36663](https://github.com/nodejs/node/pull/36663)
- [[`868d3b2ff6`](https://github.com/nodejs/node/commit/868d3b2ff6)] - **src**: use BaseObject::kInteralFieldCount in Blob (Joyee Cheung) [#36991](https://github.com/nodejs/node/pull/36991)
- [[`a5ffdaee1c`](https://github.com/nodejs/node/commit/a5ffdaee1c)] - **src**: replace push_back with emplace_back in debug_utils (raisinten) [#36897](https://github.com/nodejs/node/pull/36897)
- [[`d54998538e`](https://github.com/nodejs/node/commit/d54998538e)] - **src**: use BaseObject::kInternalFieldCount in X509Certificate constructor (Joyee Cheung) [#36892](https://github.com/nodejs/node/pull/36892)
- [[`7acea78493`](https://github.com/nodejs/node/commit/7acea78493)] - **test**: mark flaky tests on IBM i (Richard Lau) [#36986](https://github.com/nodejs/node/pull/36986)
- [[`e69c4a941d`](https://github.com/nodejs/node/commit/e69c4a941d)] - **(SEMVER-MINOR)** **test**: add wpt tests for Blob (Michaël Zasso) [#36811](https://github.com/nodejs/node/pull/36811)
- [[`2f1f1dadaa`](https://github.com/nodejs/node/commit/2f1f1dadaa)] - **test**: increase buffer list coverage (Emil Sivervik) [#36688](https://github.com/nodejs/node/pull/36688)
- [[`8d49ce9d75`](https://github.com/nodejs/node/commit/8d49ce9d75)] - **test**: fix warning in test_environment.cc (raisinten) [#36846](https://github.com/nodejs/node/pull/36846)
- [[`98369aaf7b`](https://github.com/nodejs/node/commit/98369aaf7b)] - **test**: remove unused ecdhPeerKey (Daniel Bevenius) [#36942](https://github.com/nodejs/node/pull/36942)
- [[`ba87be0b0e`](https://github.com/nodejs/node/commit/ba87be0b0e)] - **test**: improve coverage for `Module` getters (Juan José Arboleda) [#36950](https://github.com/nodejs/node/pull/36950)
- [[`c7dd9c8c69`](https://github.com/nodejs/node/commit/c7dd9c8c69)] - **test**: skip internet for test-npm-install (Ruy Adorno) [#36933](https://github.com/nodejs/node/pull/36933)
- [[`3bbe9a5588`](https://github.com/nodejs/node/commit/3bbe9a5588)] - **test**: improve coverage on worker threads (Juan José Arboleda) [#36910](https://github.com/nodejs/node/pull/36910)
- [[`f589bb2052`](https://github.com/nodejs/node/commit/f589bb2052)] - **test**: improve coverage at `lib/internal/vm/module.js` (Juan José Arboleda) [#36898](https://github.com/nodejs/node/pull/36898)
- [[`8a8241529e`](https://github.com/nodejs/node/commit/8a8241529e)] - **_Revert_** "**test**: mark test-cluster-bind-privileged-port flaky on arm" (Rod Vagg) [#36884](https://github.com/nodejs/node/pull/36884)
- [[`99c15909ad`](https://github.com/nodejs/node/commit/99c15909ad)] - **test**: fixup flaky test-crypto-x509 on windows (James M Snell) [#36966](https://github.com/nodejs/node/pull/36966)
- [[`c2ec15aff6`](https://github.com/nodejs/node/commit/c2ec15aff6)] - **test**: check mustCall errors in test-fs-read-type (Tobias Nießen) [#36914](https://github.com/nodejs/node/pull/36914)
- [[`30b2aac98a`](https://github.com/nodejs/node/commit/30b2aac98a)] - **test**: fix variable name for non-RSA keys (Tobias Nießen) [#36912](https://github.com/nodejs/node/pull/36912)
- [[`fada6b0087`](https://github.com/nodejs/node/commit/fada6b0087)] - **test,benchmark**: stop requiring URL and URLSearchParams (raisinten) [#36927](https://github.com/nodejs/node/pull/36927)
- [[`864b97b24d`](https://github.com/nodejs/node/commit/864b97b24d)] - **tls**: use recently added matching SecureContext in default SNICallback (Mateusz Krawczuk) [#36072](https://github.com/nodejs/node/pull/36072)
- [[`6ef54bb9ca`](https://github.com/nodejs/node/commit/6ef54bb9ca)] - **tools**: cleanup old ICU version-specific fixes (Michaël Zasso) [#36980](https://github.com/nodejs/node/pull/36980)
- [[`8e02b53b09`](https://github.com/nodejs/node/commit/8e02b53b09)] - **tools**: update ESLint to 7.18.0 (Colin Ihrig) [#36955](https://github.com/nodejs/node/pull/36955)
- [[`8dc8adc782`](https://github.com/nodejs/node/commit/8dc8adc782)] - **tools**: add support for top-level await syntax in linter (Antoine du Hamel) [#36911](https://github.com/nodejs/node/pull/36911)
- [[`17bdcd9d18`](https://github.com/nodejs/node/commit/17bdcd9d18)] - **tools,doc**: list the stability status of each API (Zijian Liu) [#36223](https://github.com/nodejs/node/pull/36223)
- [[`889654d36c`](https://github.com/nodejs/node/commit/889654d36c)] - **url**: align url format behavior with browsers (ZiJian Liu) [#36903](https://github.com/nodejs/node/pull/36903)
- [[`64fed319ef`](https://github.com/nodejs/node/commit/64fed319ef)] - **(SEMVER-MINOR)** **url**: expose urlToHttpOptions utility (Yongsheng Zhang) [#35960](https://github.com/nodejs/node/pull/35960)
- [[`f2704170a3`](https://github.com/nodejs/node/commit/f2704170a3)] - **util**: prefer `Reflect.ownKeys(…)` (ExE Boss) [#36740](https://github.com/nodejs/node/pull/36740)
- [[`0d719476e0`](https://github.com/nodejs/node/commit/0d719476e0)] - **vm**: refactor to avoid unsafe array iteration (Antoine du Hamel) [#36752](https://github.com/nodejs/node/pull/36752)
- [[`bf695ebdb1`](https://github.com/nodejs/node/commit/bf695ebdb1)] - **worker**: refactor to avoid unsafe array iteration (Antoine du Hamel) [#36735](https://github.com/nodejs/node/pull/36735)
- [[`403b595ef5`](https://github.com/nodejs/node/commit/403b595ef5)] - **zlib**: refactor to avoid unsafe array iteration (Antoine du Hamel) [#36722](https://github.com/nodejs/node/pull/36722)

Windows 32-bit Installer: https://nodejs.org/dist/v15.7.0/node-v15.7.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v15.7.0/node-v15.7.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v15.7.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v15.7.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v15.7.0/node-v15.7.0.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v15.7.0/node-v15.7.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v15.7.0/node-v15.7.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v15.7.0/node-v15.7.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v15.7.0/node-v15.7.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v15.7.0/node-v15.7.0-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v15.7.0/node-v15.7.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v15.7.0/node-v15.7.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v15.7.0/node-v15.7.0.tar.gz \
Other release files: https://nodejs.org/dist/v15.7.0/ \
Documentation: https://nodejs.org/docs/v15.7.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

b091732d90fe6882850dc5d0973a774030c4d6c32fb303a49f7e7be1918d168e  node-v15.7.0-aix-ppc64.tar.gz
b0f7728bbf428cdd8343e1fae910882d19a3889a04d09afc722c653cf8ab3ef2  node-v15.7.0-darwin-x64.tar.gz
67c134ad7e319a283852a410336a67e679b7c15031e2d5f2956ad8a105dd2252  node-v15.7.0-darwin-x64.tar.xz
d1d223ed8bbb12ad96587ac2b6566482958508e081f43c2ad615bf116ff179bd  node-v15.7.0-headers.tar.gz
2b4648c2b5aac304d99c15740d033be3550fcb501dddb3c069bb04d1bdbb4b51  node-v15.7.0-headers.tar.xz
72853eb858a93d53b0758b86eea0d466296ab275fbb73f2f4d40fad6cd1a0ff9  node-v15.7.0-linux-arm64.tar.gz
14a3d219ff2341327ec45319c6b29cd74ffa347b3f538fa36b0009bf7939b01c  node-v15.7.0-linux-arm64.tar.xz
aa65f287bfe060321ee5e0b4f7134bd17690abb911c6fc1173ddbedddbf2c060  node-v15.7.0-linux-armv7l.tar.gz
86b0b286342713d07ffd64e7417022fea7cf33de3082d4b83a5e16e8aa2365f0  node-v15.7.0-linux-armv7l.tar.xz
78c8a4e9ed8a51cac421d2017f9eec61a318cc30b6e4af96cb28200816a4d49a  node-v15.7.0-linux-ppc64le.tar.gz
626519a223990e34e154da0161372f2787ea1a9f707e1db9ddb897c1c7a65f03  node-v15.7.0-linux-ppc64le.tar.xz
c497139882e4cc569df7fa5c305fafcd5c1f33485c7b44615fad976f622e8410  node-v15.7.0-linux-s390x.tar.gz
bc9d3125deb7ede2603cd3975c68abeaa3402a819d8d510d86120cbb3f71398b  node-v15.7.0-linux-s390x.tar.xz
8081794dc8a6a1dd46045ce5a921e227407dcf7c17ee9d1ad39e354b37526f5c  node-v15.7.0-linux-x64.tar.gz
8bcbc3a47ea8563519ab8c447412e941d160a79531c959b5f9c2306611a95223  node-v15.7.0-linux-x64.tar.xz
2dbfa3cc5f4cfc78b9ffe492b9debe85219c154f50ec4a7aee2a00ef10f9ed37  node-v15.7.0.pkg
cda67d79fe69b7a977a5b7fcb64721a68daca1fc6890368813525c8b5d349ddb  node-v15.7.0.tar.gz
ef5eceabed802356859e3340d42f88ca3ded7d39fb7da1c18575887bc628dbda  node-v15.7.0.tar.xz
52f49f158f7ed2ece2bc28b30b205af1bb212c867a3172e885e3ad63e4d7a4f9  node-v15.7.0-win-x64.7z
737701d33bfef3140482b7f94f119b1a33ebb871a4039382178c72bbedcf9b32  node-v15.7.0-win-x64.zip
a8d65db87ddb046e2fd335acd6d8f9d52ae5469e70c8f56c074c4630c43a6c25  node-v15.7.0-win-x86.7z
cd2553a6355a48194e19bee1b09d57b69698f40de243c9aa5025cccc6bd8d9d3  node-v15.7.0-win-x86.zip
98420c4d3559ea5400056c4c85925eaa4402dc061c80b458f2e9f066ed69e828  node-v15.7.0-x64.msi
b361f89b49b252c9d2df1085c73f19676bed445b1b3df661c590018c1828fb2a  node-v15.7.0-x86.msi
cf32e3c1c39762d4b14669ec593af7961b78b64a517e8a9dbbe970cb32729cd6  win-x64/node.exe
74c5baf6049f2f9f6712ad06cbf544c25803f7a01c6f63b8fe18488a68ef4720  win-x64/node.lib
31a12b8a3062e35f6b1a33431df1b3ddfba9b3b504e2adeddbb84e94696d2e0c  win-x64/node_pdb.7z
ceceb6f268fe358e02bd4f24a8e183c0933b3e76c5f7005fc9f8679bff06e12b  win-x64/node_pdb.zip
9f5f782bf1f4a50b318efd702482ac9b2cd887e4a4faf065cb8376db1a6075d6  win-x86/node.exe
bff86968328f105b6d4a491806863ef9f2b51dc55f02580600c0b2ca5faef870  win-x86/node.lib
07eb162255e52d246afce11b5a7c0015483adadf26a3e2869fa6c52a434a4507  win-x86/node_pdb.7z
67a5be6d772ab890a9529e5649b794e6b44e85f651a7375c62104ce37ca80847  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEEI9StI21e7DMQ5spl7AUGb2S+AoFAmAQP+kACgkQl7AUGb2S
+Ar+Dw/8C4Ea3pD/e6gyxJxfKxKkEE37VEvmwUKSXZAiqQGECGngVF3yPVJ2+tDr
ezj2bPmNp3joUs+QLpYltJiCWb3I9fyv3Vu+DhgZlldOEIvO3GyGd3pFuttPIak+
jQnxCwH2dW9AbgrZFIjazFTMmHuom9LCaxW2BeQM/gE9jxz/gKwQnA84beQG5Fo+
FII3I51QuGxvVsH6rYh22WgfU9MkICLu+v/OMnpZEYQ/Z3ytP5/MI36YG2GQ7hQ+
/EdgnQbFwnOlqUMz6yUnDKuPX9cvkxOXQLTFmShbQYvLHyTjU2OrQ27k7NOKFio7
RimVhUVf94BB6iuLUdYVdminzdoQ3FU1c8AsTC6jJj2suzu/Ta88l7WMvcw5MKLs
YC1ah5l3OllAaWtLQEBttcJ2la5PcunDewz0bVHJ1bwLz1U+JpOJaF8LM86qrSfo
wzN9IUV4ppOy9yjdz5XpY5iDc/ZegTLbjHA9gUZn3XqHyT3qXQVOQB/nEBTyZLVd
+l+PkL6jMgQ5JidDX0eNEPvUllefuTafH56EEChKYohp1a7xLtnIeHSI9jT4F7tS
8w2HX0WA5StNZKrcEmC+RrH+ovanGHdx00VBqOP1o8K6VCo1xk7aL9bZ+eTOsPe4
LCxqAMHPRAGL5FIPbZAEsz7BDKUN+ssNOxHV2wSBumjqIVD2tcw=
=DNgP
-----END PGP SIGNATURE-----

```
