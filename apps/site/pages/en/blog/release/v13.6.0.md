---
date: '2020-01-07T23:19:07.727Z'
category: release
title: Node v13.6.0 (Current)
layout: blog-post
author: <PERSON><PERSON>
---

### Notable Changes

- **assert**:
  - Implement `assert.match()` and `assert.doesNotMatch()` (<PERSON><PERSON>) [#30929](https://github.com/nodejs/node/pull/30929)
- **events**:
  - Add `EventEmitter.on` to async iterate over events (<PERSON>) [#27994](https://github.com/nodejs/node/pull/27994)
  - Allow monitoring error events (<PERSON>) [#30932](https://github.com/nodejs/node/pull/30932)
- **fs**:
  - Allow overriding `fs` for streams (<PERSON>) [#29083](https://github.com/nodejs/node/pull/29083)
- **perf_hooks**:
  - Move `perf_hooks` out of experimental (legendecas) [#31101](https://github.com/nodejs/node/pull/31101)
- **repl**:
  - Implement ZSH-like reverse-i-search (<PERSON><PERSON>) [#31006](https://github.com/nodejs/node/pull/31006)
- **tls**:
  - Add PSK (pre-shared key) support (Denys Otrishko) [#23188](https://github.com/nodejs/node/pull/23188)

### Commits

- [[`d831dc1b77`](https://github.com/nodejs/node/commit/d831dc1b77)] - **(SEMVER-MINOR)** **assert**: implement `assert.match()` and `assert.doesNotMatch()` (Ruben Bridgewater) [#30929](https://github.com/nodejs/node/pull/30929)
- [[`f8aa365508`](https://github.com/nodejs/node/commit/f8aa365508)] - **assert**: use for...of (Soar) [#30983](https://github.com/nodejs/node/pull/30983)
- [[`5fccb508e9`](https://github.com/nodejs/node/commit/5fccb508e9)] - **benchmark**: use let instead of var in dgram (dnlup) [#31175](https://github.com/nodejs/node/pull/31175)
- [[`827d3fea0e`](https://github.com/nodejs/node/commit/827d3fea0e)] - **benchmark**: add benchmark on async_hooks enabled http server (legendecas) [#31100](https://github.com/nodejs/node/pull/31100)
- [[`b193142e0a`](https://github.com/nodejs/node/commit/b193142e0a)] - **benchmark**: use let instead of var in crypto (dnlup) [#31135](https://github.com/nodejs/node/pull/31135)
- [[`b8ccf30ac1`](https://github.com/nodejs/node/commit/b8ccf30ac1)] - **benchmark**: replace var with let/const in cluster benchmark (dnlup) [#31042](https://github.com/nodejs/node/pull/31042)
- [[`01fd3be84a`](https://github.com/nodejs/node/commit/01fd3be84a)] - **benchmark**: include writev in benchmark (Robert Nagy) [#31066](https://github.com/nodejs/node/pull/31066)
- [[`ca53f02767`](https://github.com/nodejs/node/commit/ca53f02767)] - **benchmark**: use let instead of var in child_process (dnlup) [#31043](https://github.com/nodejs/node/pull/31043)
- [[`625744d292`](https://github.com/nodejs/node/commit/625744d292)] - **benchmark**: add clear connections to secure-pair (Diego Lafuente) [#27971](https://github.com/nodejs/node/pull/27971)
- [[`0e864a383c`](https://github.com/nodejs/node/commit/0e864a383c)] - **benchmark**: update manywrites back pressure (Robert Nagy) [#30977](https://github.com/nodejs/node/pull/30977)
- [[`37ffa8c2ae`](https://github.com/nodejs/node/commit/37ffa8c2ae)] - **bootstrap**: use different scripts to setup different configurations (Joyee Cheung) [#30862](https://github.com/nodejs/node/pull/30862)
- [[`4df365256f`](https://github.com/nodejs/node/commit/4df365256f)] - **buffer**: improve .from() error details (Ruben Bridgewater) [#29675](https://github.com/nodejs/node/pull/29675)
- [[`9b7cf090c7`](https://github.com/nodejs/node/commit/9b7cf090c7)] - **build**: don't use -latomic on macOS (Ryan Schmidt) [#30099](https://github.com/nodejs/node/pull/30099)
- [[`d2ab877b72`](https://github.com/nodejs/node/commit/d2ab877b72)] - **build**: warn upon --use-largepages config option (Gabriel Schulhof) [#31103](https://github.com/nodejs/node/pull/31103)
- [[`ca05a5bb64`](https://github.com/nodejs/node/commit/ca05a5bb64)] - **build**: switch realpath to pwd (bcoe) [#31095](https://github.com/nodejs/node/pull/31095)
- [[`d131877398`](https://github.com/nodejs/node/commit/d131877398)] - **build**: fixes build for some os versions (David Carlier)
- [[`baf8730a47`](https://github.com/nodejs/node/commit/baf8730a47)] - **build**: re-introduce --use-largepages as no-op (Gabriel Schulhof)
- [[`ca235112ae`](https://github.com/nodejs/node/commit/ca235112ae)] - **deps**: V8: backport a4545db (David Carlier) [#31127](https://github.com/nodejs/node/pull/31127)
- [[`e2ef1a9e63`](https://github.com/nodejs/node/commit/e2ef1a9e63)] - **deps**: V8: bump v8_embedder_string for 0e21c1e637bf (Сковорода Никита Андреевич) [#31096](https://github.com/nodejs/node/pull/31096)
- [[`2ec817e02d`](https://github.com/nodejs/node/commit/2ec817e02d)] - **deps**: uvwasi: cherry-pick 75b389c (cjihrig) [#31076](https://github.com/nodejs/node/pull/31076)
- [[`a5937c7b6c`](https://github.com/nodejs/node/commit/a5937c7b6c)] - **deps**: uvwasi: cherry-pick 64e59d5 (cjihrig) [#31076](https://github.com/nodejs/node/pull/31076)
- [[`647f3c7639`](https://github.com/nodejs/node/commit/647f3c7639)] - **deps**: V8: cherry-pick 687d865fe251 (Сковорода Никита Андреевич) [#31007](https://github.com/nodejs/node/pull/31007)
- [[`7fe8399e08`](https://github.com/nodejs/node/commit/7fe8399e08)] - **deps**: V8: cherry-pick d406bfd64653 (Sam Roberts) [#30819](https://github.com/nodejs/node/pull/30819)
- [[`7e13ae7757`](https://github.com/nodejs/node/commit/7e13ae7757)] - **deps**: V8: cherry-pick d3a1a5b6c491 (Michaël Zasso) [#31005](https://github.com/nodejs/node/pull/31005)
- [[`32805a9525`](https://github.com/nodejs/node/commit/32805a9525)] - **deps,src,test**: update to uvwasi 0.0.3 (cjihrig) [#30980](https://github.com/nodejs/node/pull/30980)
- [[`44d03e81d4`](https://github.com/nodejs/node/commit/44d03e81d4)] - **dgram**: test to add and to drop specific membership (A. Volgin) [#31047](https://github.com/nodejs/node/pull/31047)
- [[`21ef3d615e`](https://github.com/nodejs/node/commit/21ef3d615e)] - **dgram**: use for...of (Trivikram Kamat) [#30999](https://github.com/nodejs/node/pull/30999)
- [[`7b696fe9f4`](https://github.com/nodejs/node/commit/7b696fe9f4)] - **doc**: remove extra backtick (cjihrig) [#31186](https://github.com/nodejs/node/pull/31186)
- [[`dba2ab75d9`](https://github.com/nodejs/node/commit/dba2ab75d9)] - **doc**: use code markup/markdown in headers (Ruben Bridgewater) [#31149](https://github.com/nodejs/node/pull/31149)
- [[`cc44325eed`](https://github.com/nodejs/node/commit/cc44325eed)] - **doc**: update REPL documentation to instantiate the REPL (Ruben Bridgewater) [#30928](https://github.com/nodejs/node/pull/30928)
- [[`d3a8088cd5`](https://github.com/nodejs/node/commit/d3a8088cd5)] - **doc**: improve explanation of package.json "type" field (Ronald J Kimball) [#27516](https://github.com/nodejs/node/pull/27516)
- [[`33352c2433`](https://github.com/nodejs/node/commit/33352c2433)] - **doc**: clarify role of writable.cork() (Colin Grant) [#30442](https://github.com/nodejs/node/pull/30442)
- [[`b657a64b77`](https://github.com/nodejs/node/commit/b657a64b77)] - **doc**: de-duplicate security release processes (Sam Roberts) [#30996](https://github.com/nodejs/node/pull/30996)
- [[`18b34def41`](https://github.com/nodejs/node/commit/18b34def41)] - **doc**: fix createDiffieHellman generator type (Tobias Nießen) [#31121](https://github.com/nodejs/node/pull/31121)
- [[`1fa8e49f7e`](https://github.com/nodejs/node/commit/1fa8e49f7e)] - **doc**: update mode type for mkdir() functions (cjihrig) [#31115](https://github.com/nodejs/node/pull/31115)
- [[`a37a88f40d`](https://github.com/nodejs/node/commit/a37a88f40d)] - **doc**: update mode type for process.umask() (cjihrig) [#31115](https://github.com/nodejs/node/pull/31115)
- [[`2313b9e33b`](https://github.com/nodejs/node/commit/2313b9e33b)] - **doc**: update mode type for fs open() functions (cjihrig) [#31115](https://github.com/nodejs/node/pull/31115)
- [[`53c6a1ee34`](https://github.com/nodejs/node/commit/53c6a1ee34)] - **doc**: update mode type for fchmod() functions (cjihrig) [#31115](https://github.com/nodejs/node/pull/31115)
- [[`68557889d3`](https://github.com/nodejs/node/commit/68557889d3)] - **doc**: update parameter type for fsPromises.chmod() (cjihrig) [#31115](https://github.com/nodejs/node/pull/31115)
- [[`72d70d5102`](https://github.com/nodejs/node/commit/72d70d5102)] - **doc**: improve dns introduction (Rich Trott) [#31090](https://github.com/nodejs/node/pull/31090)
- [[`4c29a6ee15`](https://github.com/nodejs/node/commit/4c29a6ee15)] - **doc**: update parameter type for fs.chmod() (Santosh Yadav) [#31085](https://github.com/nodejs/node/pull/31085)
- [[`dcce8b68b2`](https://github.com/nodejs/node/commit/dcce8b68b2)] - **doc**: use code markup/markdown in headers in globals documentation (Rich Trott) [#31086](https://github.com/nodejs/node/pull/31086)
- [[`7afe69cee0`](https://github.com/nodejs/node/commit/7afe69cee0)] - **doc**: use code markup/markdown in headers in deprecations documentation (Rich Trott) [#31086](https://github.com/nodejs/node/pull/31086)
- [[`ff828900f6`](https://github.com/nodejs/node/commit/ff828900f6)] - **doc**: use code markup/markdown in headers in addons documentation (Rich Trott) [#31086](https://github.com/nodejs/node/pull/31086)
- [[`ce60a80944`](https://github.com/nodejs/node/commit/ce60a80944)] - **doc**: allow \<code\> in header elements (Rich Trott) [#31086](https://github.com/nodejs/node/pull/31086)
- [[`1033760874`](https://github.com/nodejs/node/commit/1033760874)] - **doc**: add --inspect-publish-uid man page entry (cjihrig) [#31077](https://github.com/nodejs/node/pull/31077)
- [[`23013e3e31`](https://github.com/nodejs/node/commit/23013e3e31)] - **doc**: add --force-context-aware man page entry (cjihrig) [#31077](https://github.com/nodejs/node/pull/31077)
- [[`efc97fd927`](https://github.com/nodejs/node/commit/efc97fd927)] - **doc**: add --enable-source-maps man page entry (cjihrig) [#31077](https://github.com/nodejs/node/pull/31077)
- [[`4292f64c27`](https://github.com/nodejs/node/commit/4292f64c27)] - **doc**: fix anchors and subtitle in BUILDING.md (sutangu) [#30296](https://github.com/nodejs/node/pull/30296)
- [[`1357c97a70`](https://github.com/nodejs/node/commit/1357c97a70)] - **doc**: standardize usage of hostname vs. host name (Rich Trott) [#31073](https://github.com/nodejs/node/pull/31073)
- [[`4caf4578fe`](https://github.com/nodejs/node/commit/4caf4578fe)] - **doc**: add unrepresented flags docs for configure (Pranshu Srivastava) [#28069](https://github.com/nodejs/node/pull/28069)
- [[`9141366e09`](https://github.com/nodejs/node/commit/9141366e09)] - **doc**: improve doc net:server.listen (dev-313) [#31064](https://github.com/nodejs/node/pull/31064)
- [[`69d6e9732b`](https://github.com/nodejs/node/commit/69d6e9732b)] - **doc**: implement minor improvements to BUILDING.md text (Rich Trott) [#31070](https://github.com/nodejs/node/pull/31070)
- [[`a7988ab0fa`](https://github.com/nodejs/node/commit/a7988ab0fa)] - **doc**: avoid using v8::Persistent in addon docs (Anna Henningsen) [#31018](https://github.com/nodejs/node/pull/31018)
- [[`a3861147e5`](https://github.com/nodejs/node/commit/a3861147e5)] - **doc**: clarify required flag for extensionless esm (Lucas Azzola) [#30657](https://github.com/nodejs/node/pull/30657)
- [[`cc8c0b4cde`](https://github.com/nodejs/node/commit/cc8c0b4cde)] - **doc**: reference worker threads on signal events (legendecas) [#30990](https://github.com/nodejs/node/pull/30990)
- [[`7815d5f2cb`](https://github.com/nodejs/node/commit/7815d5f2cb)] - **doc**: update message.url example in http.IncomingMessage (Tadao Iseki) [#30830](https://github.com/nodejs/node/pull/30830)
- [[`118df63d9f`](https://github.com/nodejs/node/commit/118df63d9f)] - **doc,assert**: use code markup/markdown in headers (Rich Trott) [#31086](https://github.com/nodejs/node/pull/31086)
- [[`32e5895a2f`](https://github.com/nodejs/node/commit/32e5895a2f)] - **doc,async_hooks**: use code markup/markdown in headers (Rich Trott) [#31086](https://github.com/nodejs/node/pull/31086)
- [[`0e0d45b02f`](https://github.com/nodejs/node/commit/0e0d45b02f)] - **doc,buffer**: use code markup/markdown in headers (Rich Trott) [#31086](https://github.com/nodejs/node/pull/31086)
- [[`405bf8c8bb`](https://github.com/nodejs/node/commit/405bf8c8bb)] - **doc,child_process**: use code markup/markdown in headers (Rich Trott) [#31086](https://github.com/nodejs/node/pull/31086)
- [[`27790fc76e`](https://github.com/nodejs/node/commit/27790fc76e)] - **doc,cluster**: use code markup/markdown in headers (Rich Trott) [#31086](https://github.com/nodejs/node/pull/31086)
- [[`f8a6edaac6`](https://github.com/nodejs/node/commit/f8a6edaac6)] - **doc,console**: use code markup/markdown in headers (Rich Trott) [#31086](https://github.com/nodejs/node/pull/31086)
- [[`df5ec4e7b1`](https://github.com/nodejs/node/commit/df5ec4e7b1)] - **doc,crypto**: use code markup/markdown in headers (Rich Trott) [#31086](https://github.com/nodejs/node/pull/31086)
- [[`4a42230fd7`](https://github.com/nodejs/node/commit/4a42230fd7)] - **doc,dgram**: use code markup/markdown in headers (Rich Trott) [#31086](https://github.com/nodejs/node/pull/31086)
- [[`9979f82716`](https://github.com/nodejs/node/commit/9979f82716)] - **doc,dns**: use code markup/markdown in headers (Rich Trott) [#31086](https://github.com/nodejs/node/pull/31086)
- [[`decfcaf89e`](https://github.com/nodejs/node/commit/decfcaf89e)] - **doc,domain**: use code markup/markdown in headers (Rich Trott) [#31086](https://github.com/nodejs/node/pull/31086)
- [[`665a662ad1`](https://github.com/nodejs/node/commit/665a662ad1)] - **doc,errors**: use code markup/markdown in headers (Rich Trott) [#31086](https://github.com/nodejs/node/pull/31086)
- [[`fbb217a29d`](https://github.com/nodejs/node/commit/fbb217a29d)] - **doc,esm**: use code markup/markdown in headers (Rich Trott) [#31086](https://github.com/nodejs/node/pull/31086)
- [[`db01d0f947`](https://github.com/nodejs/node/commit/db01d0f947)] - **doc,events**: use code markup/markdown in headers (Rich Trott) [#31086](https://github.com/nodejs/node/pull/31086)
- [[`e7f7e45ddb`](https://github.com/nodejs/node/commit/e7f7e45ddb)] - **doc,fs**: use code markup/markdown in headers (Rich Trott) [#31086](https://github.com/nodejs/node/pull/31086)
- [[`cdb79fc106`](https://github.com/nodejs/node/commit/cdb79fc106)] - **doc,http**: use code markup/markdown in headers (Rich Trott) [#31086](https://github.com/nodejs/node/pull/31086)
- [[`3062bcb13c`](https://github.com/nodejs/node/commit/3062bcb13c)] - **doc,http2**: use code markup/markdown in headers (Rich Trott) [#31086](https://github.com/nodejs/node/pull/31086)
- [[`3571df3115`](https://github.com/nodejs/node/commit/3571df3115)] - **doc,https**: use code markup/markdown in headers (Rich Trott) [#31086](https://github.com/nodejs/node/pull/31086)
- [[`699b31f8fe`](https://github.com/nodejs/node/commit/699b31f8fe)] - **doc,inspector**: use code markup/markdown in headers (Rich Trott) [#31086](https://github.com/nodejs/node/pull/31086)
- [[`d6f942003b`](https://github.com/nodejs/node/commit/d6f942003b)] - **doc,lib,src,test**: rename WASI CLI flag (cjihrig) [#30980](https://github.com/nodejs/node/pull/30980)
- [[`7d25e44bc1`](https://github.com/nodejs/node/commit/7d25e44bc1)] - **doc,module**: use code markup/markdown in headers (Rich Trott) [#31086](https://github.com/nodejs/node/pull/31086)
- [[`927b37f5a3`](https://github.com/nodejs/node/commit/927b37f5a3)] - **doc,net**: use code markup/markdown in headers (Rich Trott) [#31086](https://github.com/nodejs/node/pull/31086)
- [[`9de914687d`](https://github.com/nodejs/node/commit/9de914687d)] - **doc,os**: use code markup/markdown in headers (Rich Trott) [#31086](https://github.com/nodejs/node/pull/31086)
- [[`5921654eca`](https://github.com/nodejs/node/commit/5921654eca)] - **doc,path**: use code markup/markdown in headers (Rich Trott) [#31086](https://github.com/nodejs/node/pull/31086)
- [[`3ee3e6f5ff`](https://github.com/nodejs/node/commit/3ee3e6f5ff)] - **doc,perf_hooks**: use code markup/markdown in headers (Rich Trott) [#31086](https://github.com/nodejs/node/pull/31086)
- [[`8c126527d9`](https://github.com/nodejs/node/commit/8c126527d9)] - **doc,process**: use code markup/markdown in headers (Rich Trott) [#31086](https://github.com/nodejs/node/pull/31086)
- [[`f0bc62896a`](https://github.com/nodejs/node/commit/f0bc62896a)] - **doc,punycode**: use code markup/markdown in headers (Rich Trott) [#31086](https://github.com/nodejs/node/pull/31086)
- [[`125a59a0b0`](https://github.com/nodejs/node/commit/125a59a0b0)] - **doc,querystring**: use code markup/markdown in headers (Rich Trott) [#31086](https://github.com/nodejs/node/pull/31086)
- [[`128a69dde3`](https://github.com/nodejs/node/commit/128a69dde3)] - **doc,readline**: use code markup/markdown in headers (Rich Trott) [#31086](https://github.com/nodejs/node/pull/31086)
- [[`38e09f8d17`](https://github.com/nodejs/node/commit/38e09f8d17)] - **doc,repl**: use code markup/markdown in headers (Rich Trott) [#31086](https://github.com/nodejs/node/pull/31086)
- [[`4c5a9854ec`](https://github.com/nodejs/node/commit/4c5a9854ec)] - **doc,stream**: use code markup/markdown in headers (Rich Trott) [#31086](https://github.com/nodejs/node/pull/31086)
- [[`cf563bbd7f`](https://github.com/nodejs/node/commit/cf563bbd7f)] - **doc,string_decoder**: use code markup/markdown in headers (Rich Trott) [#31086](https://github.com/nodejs/node/pull/31086)
- [[`450d9a27bf`](https://github.com/nodejs/node/commit/450d9a27bf)] - **doc,timers**: use code markup/markdown in headers (Rich Trott) [#31086](https://github.com/nodejs/node/pull/31086)
- [[`d6d507aa6c`](https://github.com/nodejs/node/commit/d6d507aa6c)] - **doc,tls**: use code markup/markdown in headers (Rich Trott) [#31086](https://github.com/nodejs/node/pull/31086)
- [[`9d2082be94`](https://github.com/nodejs/node/commit/9d2082be94)] - **doc,tty**: use code markup/markdown in headers (Rich Trott) [#31086](https://github.com/nodejs/node/pull/31086)
- [[`73c598a905`](https://github.com/nodejs/node/commit/73c598a905)] - **doc,url**: use code markup/markdown in headers (Rich Trott) [#31086](https://github.com/nodejs/node/pull/31086)
- [[`4672e106c1`](https://github.com/nodejs/node/commit/4672e106c1)] - **doc,util**: use code markup/markdown in headers (Rich Trott) [#31086](https://github.com/nodejs/node/pull/31086)
- [[`342d3372ef`](https://github.com/nodejs/node/commit/342d3372ef)] - **doc,v8**: use code markup/markdown in headers (Rich Trott) [#31086](https://github.com/nodejs/node/pull/31086)
- [[`e6fbde53b3`](https://github.com/nodejs/node/commit/e6fbde53b3)] - **doc,vm**: use code markup/markdown in headers (Rich Trott) [#31086](https://github.com/nodejs/node/pull/31086)
- [[`796a9c0f43`](https://github.com/nodejs/node/commit/796a9c0f43)] - **doc,vm,test**: remove \_sandbox\_ from vm documentation (Rich Trott) [#31057](https://github.com/nodejs/node/pull/31057)
- [[`1bcc07b758`](https://github.com/nodejs/node/commit/1bcc07b758)] - **doc,wasi**: use code markup/markdown in headers (Rich Trott) [#31086](https://github.com/nodejs/node/pull/31086)
- [[`cb3c3fcb3f`](https://github.com/nodejs/node/commit/cb3c3fcb3f)] - **doc,worker**: use code markup/markdown in headers (Rich Trott) [#31086](https://github.com/nodejs/node/pull/31086)
- [[`a6f16b3e78`](https://github.com/nodejs/node/commit/a6f16b3e78)] - **doc,zlib**: use code markup/markdown in headers (Rich Trott) [#31086](https://github.com/nodejs/node/pull/31086)
- [[`1057a4cdf2`](https://github.com/nodejs/node/commit/1057a4cdf2)] - **errors**: support prepareSourceMap with source-maps (bcoe) [#31143](https://github.com/nodejs/node/pull/31143)
- [[`33c5dbe197`](https://github.com/nodejs/node/commit/33c5dbe197)] - **errors**: improve ERR_INVALID_ARG_TYPE (Ruben Bridgewater) [#29675](https://github.com/nodejs/node/pull/29675)
- [[`a6c2502686`](https://github.com/nodejs/node/commit/a6c2502686)] - **esm**: better error message for unsupported URL (Thomas) [#31129](https://github.com/nodejs/node/pull/31129)
- [[`24a021216d`](https://github.com/nodejs/node/commit/24a021216d)] - **esm**: empty ext from pkg type/main doesnt affect format (Bradley Farias) [#31021](https://github.com/nodejs/node/pull/31021)
- [[`afecc973d5`](https://github.com/nodejs/node/commit/afecc973d5)] - **(SEMVER-MINOR)** **events**: add EventEmitter.on to async iterate over events (Matteo Collina) [#27994](https://github.com/nodejs/node/pull/27994)
- [[`f570de8ea9`](https://github.com/nodejs/node/commit/f570de8ea9)] - **(SEMVER-MINOR)** **events**: allow monitoring error events (Gerhard Stoebich) [#30932](https://github.com/nodejs/node/pull/30932)
- [[`4f32bbb816`](https://github.com/nodejs/node/commit/4f32bbb816)] - **fs**: use consistent defaults in sync stat functions (cjihrig) [#31097](https://github.com/nodejs/node/pull/31097)
- [[`7f6a0ed548`](https://github.com/nodejs/node/commit/7f6a0ed548)] - **(SEMVER-MINOR)** **fs**: allow overriding fs for streams (Robert Nagy) [#29083](https://github.com/nodejs/node/pull/29083)
- [[`4a54f304a7`](https://github.com/nodejs/node/commit/4a54f304a7)] - **http**: http_outgoing rename var to let and const (telenord) [#30284](https://github.com/nodejs/node/pull/30284)
- [[`1b720aa802`](https://github.com/nodejs/node/commit/1b720aa802)] - **http**: free listeners on free sockets (Robert Nagy) [#29259](https://github.com/nodejs/node/pull/29259)
- [[`b5a71a439d`](https://github.com/nodejs/node/commit/b5a71a439d)] - **http2**: set default enableConnectProtocol to 0 (ZYSzys) [#31174](https://github.com/nodejs/node/pull/31174)
- [[`b9160351ec`](https://github.com/nodejs/node/commit/b9160351ec)] - **http2**: make HTTP2ServerResponse more streams compliant (Robert Nagy) [#30964](https://github.com/nodejs/node/pull/30964)
- [[`ba0682e91c`](https://github.com/nodejs/node/commit/ba0682e91c)] - **http2**: wait for session socket writable end on close/destroy (Denys Otrishko) [#30854](https://github.com/nodejs/node/pull/30854)
- [[`86f2e869dc`](https://github.com/nodejs/node/commit/86f2e869dc)] - **http2**: wait for session to finish writing before destroy (Denys Otrishko) [#30854](https://github.com/nodejs/node/pull/30854)
- [[`18acaccf0a`](https://github.com/nodejs/node/commit/18acaccf0a)] - **https**: prevent options object from being mutated (Vighnesh Raut) [#31151](https://github.com/nodejs/node/pull/31151)
- [[`42d36dca90`](https://github.com/nodejs/node/commit/42d36dca90)] - **lib**: move initialization of APIs for changing process state (Anna Henningsen) [#31172](https://github.com/nodejs/node/pull/31172)
- [[`20ecb5dcfb`](https://github.com/nodejs/node/commit/20ecb5dcfb)] - **lib**: replace Map global by the primordials (Sebastien Ahkrin) [#31155](https://github.com/nodejs/node/pull/31155)
- [[`f268621ffa`](https://github.com/nodejs/node/commit/f268621ffa)] - **lib**: replace use of Error with primordials (Sebastien Ahkrin) [#31163](https://github.com/nodejs/node/pull/31163)
- [[`3f21ad67f8`](https://github.com/nodejs/node/commit/3f21ad67f8)] - **lib**: replace Set global by the primordials (Sebastien Ahkrin) [#31154](https://github.com/nodejs/node/pull/31154)
- [[`542aae4bf0`](https://github.com/nodejs/node/commit/542aae4bf0)] - **lib**: replace WeakSet global by the primordials (Sebastien Ahkrin) [#31157](https://github.com/nodejs/node/pull/31157)
- [[`0b8eaf2e5c`](https://github.com/nodejs/node/commit/0b8eaf2e5c)] - **lib**: replace WeakMap global by the primordials (Sebastien Ahkrin) [#31158](https://github.com/nodejs/node/pull/31158)
- [[`1527796661`](https://github.com/nodejs/node/commit/1527796661)] - **lib**: replace Set.prototype with SetPrototype primordial (Sebastien Ahkrin) [#31161](https://github.com/nodejs/node/pull/31161)
- [[`4b2d8df5b5`](https://github.com/nodejs/node/commit/4b2d8df5b5)] - **lib**: do not catch user errors (Ruben Bridgewater) [#31159](https://github.com/nodejs/node/pull/31159)
- [[`97ce0a3b47`](https://github.com/nodejs/node/commit/97ce0a3b47)] - **lib**: replace var with let/const (kresimirfranin) [#30394](https://github.com/nodejs/node/pull/30394)
- [[`614b2c58f0`](https://github.com/nodejs/node/commit/614b2c58f0)] - **lib**: further simplify assertions in vm/module (Anna Henningsen) [#30815](https://github.com/nodejs/node/pull/30815)
- [[`a83d338102`](https://github.com/nodejs/node/commit/a83d338102)] - **lib**: improve spelling and grammar in comment (David Newman) [#31026](https://github.com/nodejs/node/pull/31026)
- [[`799b50934b`](https://github.com/nodejs/node/commit/799b50934b)] - **meta**: clarify scope of new nodejs.org issue choice (Derek Lewis) [#31123](https://github.com/nodejs/node/pull/31123)
- [[`72c64605c9`](https://github.com/nodejs/node/commit/72c64605c9)] - **module**: unflag resolve self (Guy Bedford) [#31002](https://github.com/nodejs/node/pull/31002)
- [[`bd047e8277`](https://github.com/nodejs/node/commit/bd047e8277)] - **module**: self resolve bug fix and esm ordering (Guy Bedford) [#31009](https://github.com/nodejs/node/pull/31009)
- [[`d7712213a4`](https://github.com/nodejs/node/commit/d7712213a4)] - **n-api**: keep napi_env alive while it has finalizers (Anna Henningsen) [#31140](https://github.com/nodejs/node/pull/31140)
- [[`ae58c9709b`](https://github.com/nodejs/node/commit/ae58c9709b)] - **perf_hooks**: use for...of (Kamat, Trivikram) [#31049](https://github.com/nodejs/node/pull/31049)
- [[`dcbb97e2c3`](https://github.com/nodejs/node/commit/dcbb97e2c3)] - **(SEMVER-MINOR)** **perf_hooks**: move perf_hooks out of experimental (legendecas) [#31101](https://github.com/nodejs/node/pull/31101)
- [[`ffbf790358`](https://github.com/nodejs/node/commit/ffbf790358)] - **(SEMVER-MINOR)** **readline**: set null as callback return in case there's no error (Ruben Bridgewater) [#31006](https://github.com/nodejs/node/pull/31006)
- [[`92dcf3e4ae`](https://github.com/nodejs/node/commit/92dcf3e4ae)] - **(SEMVER-MINOR)** **readline**: small refactoring (Ruben Bridgewater) [#31006](https://github.com/nodejs/node/pull/31006)
- [[`0999d53df0`](https://github.com/nodejs/node/commit/0999d53df0)] - **repl**: use public getCursorPos() (cjihrig) [#31091](https://github.com/nodejs/node/pull/31091)
- [[`09ca8be1f2`](https://github.com/nodejs/node/commit/09ca8be1f2)] - **(SEMVER-MINOR)** **repl**: implement reverse search (Ruben Bridgewater) [#31006](https://github.com/nodejs/node/pull/31006)
- [[`925dd8e7f9`](https://github.com/nodejs/node/commit/925dd8e7f9)] - **(SEMVER-MINOR)** **repl**: fix preview of lines that exceed the terminal columns (Ruben Bridgewater) [#31006](https://github.com/nodejs/node/pull/31006)
- [[`892e7b0d7f`](https://github.com/nodejs/node/commit/892e7b0d7f)] - **src**: suppress warning in src/node_env_var.cc (Harshitha KP) [#31136](https://github.com/nodejs/node/pull/31136)
- [[`2c6f81730b`](https://github.com/nodejs/node/commit/2c6f81730b)] - **src**: make large_pages node.cc include conditional (Denys Otrishko) [#31078](https://github.com/nodejs/node/pull/31078)
- [[`54caadc6ef`](https://github.com/nodejs/node/commit/54caadc6ef)] - **src**: enable stack trace printing for V8 check failures (Anna Henningsen) [#31079](https://github.com/nodejs/node/pull/31079)
- [[`60dd1838e9`](https://github.com/nodejs/node/commit/60dd1838e9)] - **src**: prevent hard coding stack trace limit (legendecas) [#30752](https://github.com/nodejs/node/pull/30752)
- [[`80732cdf9c`](https://github.com/nodejs/node/commit/80732cdf9c)] - **src**: port --bash-completion to C++ (Joyee Cheung) [#25901](https://github.com/nodejs/node/pull/25901)
- [[`49a7e73898`](https://github.com/nodejs/node/commit/49a7e73898)] - **src**: make --use-largepages a runtime option (Gabriel Schulhof) [#30954](https://github.com/nodejs/node/pull/30954)
- [[`6b65cafacf`](https://github.com/nodejs/node/commit/6b65cafacf)] - **src**: list used functions on headers (Juan José Arboleda) [#30827](https://github.com/nodejs/node/pull/30827)
- [[`e5a41552e6`](https://github.com/nodejs/node/commit/e5a41552e6)] - **src**: fix compiler warning in env.cc (Anna Henningsen) [#31020](https://github.com/nodejs/node/pull/31020)
- [[`a27edd8335`](https://github.com/nodejs/node/commit/a27edd8335)] - **src,test**: use v8::Global instead of v8::Persistent (Anna Henningsen) [#31018](https://github.com/nodejs/node/pull/31018)
- [[`5bf27729dd`](https://github.com/nodejs/node/commit/5bf27729dd)] - **stream**: group all properties using defineProperties (antsmartian) [#31144](https://github.com/nodejs/node/pull/31144)
- [[`ca22ce2698`](https://github.com/nodejs/node/commit/ca22ce2698)] - **stream**: pipeline should use req.abort() to destroy response (Robert Nagy) [#31054](https://github.com/nodejs/node/pull/31054)
- [[`bca23b9e16`](https://github.com/nodejs/node/commit/bca23b9e16)] - **stream**: reset flowing state if no 'readable' or 'data' listeners (Robert Nagy) [#31036](https://github.com/nodejs/node/pull/31036)
- [[`146321410c`](https://github.com/nodejs/node/commit/146321410c)] - **stream**: simplify isBuf (Robert Nagy) [#31067](https://github.com/nodejs/node/pull/31067)
- [[`21d96645db`](https://github.com/nodejs/node/commit/21d96645db)] - **test**: change buffer offset to accommodate V8 BackingStore (Thang Tran) [#31171](https://github.com/nodejs/node/pull/31171)
- [[`bd6a29c60b`](https://github.com/nodejs/node/commit/bd6a29c60b)] - **test**: use spread object (Fran Herrero) [#30423](https://github.com/nodejs/node/pull/30423)
- [[`efa0bd8e25`](https://github.com/nodejs/node/commit/efa0bd8e25)] - **test**: refactor common.expectsError (Ruben Bridgewater) [#31092](https://github.com/nodejs/node/pull/31092)
- [[`16f60cedb3`](https://github.com/nodejs/node/commit/16f60cedb3)] - **test**: increase coverage for \_http_incoming.js (Rich Trott) [#31093](https://github.com/nodejs/node/pull/31093)
- [[`990760e57f`](https://github.com/nodejs/node/commit/990760e57f)] - **test**: log errors in test-http2-propagate-session-destroy-code (Denys Otrishko) [#31072](https://github.com/nodejs/node/pull/31072)
- [[`e28e873fb6`](https://github.com/nodejs/node/commit/e28e873fb6)] - **test**: skip the unsupported test cases for IBM i (Xu Meng) [#30819](https://github.com/nodejs/node/pull/30819)
- [[`07e82db764`](https://github.com/nodejs/node/commit/07e82db764)] - **test**: get lib/wasi.js coverage to 100% (cjihrig) [#31039](https://github.com/nodejs/node/pull/31039)
- [[`e5980a106c`](https://github.com/nodejs/node/commit/e5980a106c)] - **test**: cover vm with negative tests (Andrew Kuzmenko) [#31028](https://github.com/nodejs/node/pull/31028)
- [[`3c9e435f56`](https://github.com/nodejs/node/commit/3c9e435f56)] - **test**: unflake async hooks statwatcher test (Denys Otrishko) [#30362](https://github.com/nodejs/node/pull/30362)
- [[`dadccb7761`](https://github.com/nodejs/node/commit/dadccb7761)] - **test**: fix common.enoughTestMem (Rich Trott) [#31035](https://github.com/nodejs/node/pull/31035)
- [[`93cf1231db`](https://github.com/nodejs/node/commit/93cf1231db)] - **test**: fix long lines (cjihrig) [#31014](https://github.com/nodejs/node/pull/31014)
- [[`54c471a3bf`](https://github.com/nodejs/node/commit/54c471a3bf)] - **test**: fix flaky test-http2-client-upload (Gerhard Stoebich) [#29889](https://github.com/nodejs/node/pull/29889)
- [[`3753f47677`](https://github.com/nodejs/node/commit/3753f47677)] - **test**: use tmpdir.refresh() in test-esm-windows.js (Richard Lau) [#30997](https://github.com/nodejs/node/pull/30997)
- [[`d36ae62bd7`](https://github.com/nodejs/node/commit/d36ae62bd7)] - **test**: remove obsolete WASI test (cjihrig) [#30980](https://github.com/nodejs/node/pull/30980)
- [[`fe4f55ee13`](https://github.com/nodejs/node/commit/fe4f55ee13)] - **timers**: fix refresh for expired timers (Anatoli Papirovski) [#27345](https://github.com/nodejs/node/pull/27345)
- [[`83330a00a0`](https://github.com/nodejs/node/commit/83330a00a0)] - **timers**: do less work in insert (Anatoli Papirovski) [#27345](https://github.com/nodejs/node/pull/27345)
- [[`7b2bf20f7e`](https://github.com/nodejs/node/commit/7b2bf20f7e)] - **(SEMVER-MINOR)** **tls**: add PSK support (Denys Otrishko) [#23188](https://github.com/nodejs/node/pull/23188)
- [[`c23bbc6fe2`](https://github.com/nodejs/node/commit/c23bbc6fe2)] - **tools**: remove prefer-common-expectserror lint rule (cjihrig) [#31147](https://github.com/nodejs/node/pull/31147)
- [[`85d152fccf`](https://github.com/nodejs/node/commit/85d152fccf)] - **tools**: allow the travis commit message job to fail (Ruben Bridgewater) [#31116](https://github.com/nodejs/node/pull/31116)
- [[`048b7f469c`](https://github.com/nodejs/node/commit/048b7f469c)] - **tools**: fix Raspbian armv7 build (Andrey Hohutkin) [#31041](https://github.com/nodejs/node/pull/31041)
- [[`c779421f41`](https://github.com/nodejs/node/commit/c779421f41)] - **tools**: update ESLint to 6.8.0 (cjihrig) [#31044](https://github.com/nodejs/node/pull/31044)
- [[`28a62c30be`](https://github.com/nodejs/node/commit/28a62c30be)] - **tools,src**: forbid usage of v8::Persistent (Anna Henningsen) [#31018](https://github.com/nodejs/node/pull/31018)
- [[`697908e8d9`](https://github.com/nodejs/node/commit/697908e8d9)] - **util**: improve prototype inspection using `inspect()` and `showHidden` (Ruben Bridgewater) [#31113](https://github.com/nodejs/node/pull/31113)
- [[`a6998085d2`](https://github.com/nodejs/node/commit/a6998085d2)] - **util**: add (typed) array length to the default output (Ruben Bridgewater) [#31027](https://github.com/nodejs/node/pull/31027)
- [[`7611d5b47b`](https://github.com/nodejs/node/commit/7611d5b47b)] - **util**: add colors to debuglog() (Ruben Bridgewater) [#30930](https://github.com/nodejs/node/pull/30930)
- [[`614b074f3b`](https://github.com/nodejs/node/commit/614b074f3b)] - **wasi**: refactor destructuring object on constructor (himself65) [#31185](https://github.com/nodejs/node/pull/31185)
- [[`8491e1c3c6`](https://github.com/nodejs/node/commit/8491e1c3c6)] - **wasi**: fix serdes bugs from snapshot1 migration (cjihrig) [#31122](https://github.com/nodejs/node/pull/31122)
- [[`87f15c03bc`](https://github.com/nodejs/node/commit/87f15c03bc)] - **wasi**: throw on failed uvwasi_init() (cjihrig) [#31076](https://github.com/nodejs/node/pull/31076)
- [[`10f7169d58`](https://github.com/nodejs/node/commit/10f7169d58)] - **zlib**: use for...of (Kamat, Trivikram) [#31051](https://github.com/nodejs/node/pull/31051)
- [[`31bbae7c92`](https://github.com/nodejs/node/commit/31bbae7c92)] - **zlib**: allow writes after readable 'end' to finish (Anna Henningsen) [#31082](https://github.com/nodejs/node/pull/31082)

Windows 32-bit Installer: https://nodejs.org/dist/v13.6.0/node-v13.6.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v13.6.0/node-v13.6.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v13.6.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v13.6.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v13.6.0/node-v13.6.0.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v13.6.0/node-v13.6.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v13.6.0/node-v13.6.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v13.6.0/node-v13.6.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v13.6.0/node-v13.6.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v13.6.0/node-v13.6.0-aix-ppc64.tar.gz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v13.6.0/node-v13.6.0-sunos-x64.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v13.6.0/node-v13.6.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v13.6.0/node-v13.6.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v13.6.0/node-v13.6.0.tar.gz \
Other release files: https://nodejs.org/dist/v13.6.0/ \
Documentation: https://nodejs.org/docs/v13.6.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

2a23aa5541378896ae92b729c279e27f2153904189ec3f641e7c96b53681acc0  node-v13.6.0-aix-ppc64.tar.gz
da13adb864777b322dda7af20410a9b0c63aa69de4b5574008d1e6910768bf69  node-v13.6.0-darwin-x64.tar.gz
ca473e894ca6d89ec12a3afc98e9e4b4a60a50d1be8befc4f30274c4cdd9c47c  node-v13.6.0-darwin-x64.tar.xz
8e66fad806b86676bc28c562fbb84fbd7186d9f70554c9734efe763e5f4f15e6  node-v13.6.0-headers.tar.gz
60ce58aae4baca4e04e01638fac23456183f21a4521be1722e6c4a12e993d22e  node-v13.6.0-headers.tar.xz
65c648bdcb0efa5d2be4a660903a535a1ffb959079276152d076d89906242d87  node-v13.6.0-linux-arm64.tar.gz
1f34ed8668207e03cb832763319a82b4c753839a3655207adc5bbe52f65e154a  node-v13.6.0-linux-arm64.tar.xz
ccc50bf8b6fa2a8a9b58048d30203da6c0d9d1931492d89e961f4329e689ac15  node-v13.6.0-linux-armv7l.tar.gz
c04ff33e2c6aa42e40c22137ebbfa511bd5abf8e4e9438d003b14f311c48e4e6  node-v13.6.0-linux-armv7l.tar.xz
f2a43a1d2bf8d27dba485b53347f1ed97010d1917f2d0f13a7632522fbf35184  node-v13.6.0-linux-ppc64le.tar.gz
603aa3b8763fb4e228a2c6c69b9108a02997ba75e5e9df45c3d9fdf69717154c  node-v13.6.0-linux-ppc64le.tar.xz
4f49536b096406f8e31fa1c457d8387c7887d55425d2ee2f325ea52225279dc1  node-v13.6.0-linux-s390x.tar.gz
bcf62476267e9b8530187fdae8d249f38d2380352d6edd0109151cce0cf4c7c6  node-v13.6.0-linux-s390x.tar.xz
89ab4fe0db3309592924194133901b7340607d77cb5f12592325746fcdba1568  node-v13.6.0-linux-x64.tar.gz
00f01315a867da16d1638f7a02966c608e344ac6c5b7d04d1fdae3138fa9d798  node-v13.6.0-linux-x64.tar.xz
5305792221f5a5faeb2dd9dd871f74d4d8e3a841e619fa61bf00184886dbf707  node-v13.6.0-sunos-x64.tar.gz
62cf7c9996fe75ffe886c3cfcf79ac750addf8b1967beb113019b509cea109d6  node-v13.6.0-sunos-x64.tar.xz
4f4960e88edc5c8873c19dcb504295b9f068651cfd6459aae69c612ddeb0a33c  node-v13.6.0-win-x64.7z
7fe37b34a4673a071bea52fcaf913ec422cf6fd79fd025bfb22de42ccc77f386  node-v13.6.0-win-x64.zip
2608a9aca6fee6d6a32f1cfc27be2cf04d55cd36c17c7e8b7e717655f37a37a3  node-v13.6.0-win-x86.7z
e0d156738035d198fb580fee6faf1095a3b7e0cbf8b4fd7cefa52b33a77c3244  node-v13.6.0-win-x86.zip
f72acf011bcbf2eb47b74fc089c62aaa52a0ef49391e248e96dca5d0c14b0d35  node-v13.6.0-x64.msi
513e1e3adca71a0e53996ffa7382cf987a1e966d8cbd77ef55165ca8667ac21f  node-v13.6.0-x86.msi
81705fcdfacfcccaf7399c7f9281f41e6de7d47b8d038ff57837058df39d8059  node-v13.6.0.pkg
c1db19b4cf23b3903ac3048dad44a05397a63854b2a159fedc454eb15650369a  node-v13.6.0.tar.gz
553f6b7ac3826f616fecf640d92be77498efdd19776ef958ae32ae108bb5c949  node-v13.6.0.tar.xz
11a3439a9e60ddd238c94abeb2b4de903c923f7fd9161dc42006605f5856caad  win-x64/node.exe
b0c8c1d206042e7f60037571a38854c1fa6c6efdb17e1d9535befdb2572e5c04  win-x64/node.lib
312232f2df4012c1c4650f60b983c6b6febc3f1536d3d150bc0b384f433df0f0  win-x64/node_pdb.7z
00ee6f64fa97f11be7e530c570c95518a772b7ab5decc5bb89f10e3005a75f10  win-x64/node_pdb.zip
250d418b116968e709b99f97c8a5b768bc34715a7a3a889102e28374fc80f305  win-x86/node.exe
9b25c73b36ce4a8abfd7aa63a6e1893266d1dfe5bae53ea85acd5422bb719bb7  win-x86/node.lib
50dbf72f4f82ab1759a001057c5906806ca4f56ba9e2bfe6d4fe2d1cc2e468fb  win-x86/node_pdb.7z
64ba73a0434ee46a66c854da11f8f345aa5b67f34ff94fa209b0d646d04b5779  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEpIwr7mgOhBYyzU5E8HSWs+s8F2IFAl4VEH4ACgkQ8HSWs+s8
F2Lgng//aJ/NDU6wfiiDWdntuQ5WruptIuJveJIfvk84pVUstV0ztKH3hgIrZFqH
eMZ9PVVoxHrVBeHo1gB8zhvbvHPFiJjtYuQAvQBG9U9CZs6+fJ23Y8Oig3ko/lQd
0XpKTOdv/pxKi8ljOVuUlIGaezcEHCelrUjPs1Gumu3Bp+zbSxhupYWuStqCdzwC
wkhrICfQogX6BhHR6tlmVYRMTezOz4R65zR98bBuW82I4YTy0X+kN4togT/DWZTn
+6eLzjiV1qFWVH7RAHca6TqjjEqe+BPzgZ1pU7/p3KIXXdoNwtoUDPMn/x4Y0Nar
tr6CKYGiDUphPMGC4uLln0/VSxqPzzgkPZChsoXQICNYTIwkuTyp+0vOn0v62UiA
HeHIRpP6DjbHJf2bAfv6tbsiDLA3Ro6o899g1ReHpj5VJgNVvKqE50Wp8j97PsCe
fG5fIP+9WhQUt/yz/THwSdv0oPhAXao3XJptQgFmTxYjG5ub1zwHPRk4rjyaGSkE
8TpeCaKAAr13neKsYTHmx5s8MQ5IKEIA/FjVjtOOORhKLA7JkHRAbZDgkuxqV7H0
EFQit1vA6uHWhmIp9PYcjGaXy4qS48EIzI8ppu5rG5wTOMGIpLv6F7QSdyl81ci9
CSsOEPyGmktj98CYmUXyfib0LrQts2KlCzbhXG5eLEXaKpRiD2M=
=Tw2u
-----END PGP SIGNATURE-----

```
