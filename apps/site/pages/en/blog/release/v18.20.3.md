---
date: '2024-05-21T12:13:37.812Z'
category: release
title: Node v18.20.3 (LTS)
layout: blog-post
author: <PERSON>
---

## 2024-05-21, Version 18.20.3 'Hydrogen' (LTS), @richardlau

### Notable Changes

This release fixes a regression introduced in Node.js 18.19.0 where `http.server.close()` was incorrectly closing idle connections.

A fix has also been included for compiling Node.js from source with newer versions of Clang.

The list of keys used to sign releases has been synchronized with the current list from the `main` branch.

#### Updated dependencies

- acorn updated to 8.11.3.
- acorn-walk updated to 8.3.2.
- ada updated to 2.7.8.
- c-ares updated to 1.28.1.
- corepack updated to 0.28.0.
- nghttp2 updated to 1.61.0.
- ngtcp2 updated to 1.3.0.
- npm updated to 10.7.0. Includes a fix from npm\@10.5.1 to limit the number of open connections [npm/cli#7324](https://github.com/npm/cli/pull/7324).
- simdutf updated to 5.2.4.
- zlib updated to 1.3.0.1-motley-7d77fb7.

### Commits

- \[[`0c260e10e7`](https://github.com/nodejs/node/commit/0c260e10e7)] - **deps**: update zlib to 1.3.0.1-motley-7d77fb7 (Node.js GitHub Bot) [#52516](https://github.com/nodejs/node/pull/52516)
- \[[`1152d7f919`](https://github.com/nodejs/node/commit/1152d7f919)] - **deps**: update zlib to 1.3.0.1-motley-24c07df (Node.js GitHub Bot) [#52199](https://github.com/nodejs/node/pull/52199)
- \[[`755399db9d`](https://github.com/nodejs/node/commit/755399db9d)] - **deps**: update zlib to 1.3.0.1-motley-24342f6 (Node.js GitHub Bot) [#52123](https://github.com/nodejs/node/pull/52123)
- \[[`af3e32073b`](https://github.com/nodejs/node/commit/af3e32073b)] - **deps**: update ada to 2.7.8 (Node.js GitHub Bot) [#52517](https://github.com/nodejs/node/pull/52517)
- \[[`e4ea2db58b`](https://github.com/nodejs/node/commit/e4ea2db58b)] - **deps**: update c-ares to 1.28.1 (Node.js GitHub Bot) [#52285](https://github.com/nodejs/node/pull/52285)
- \[[`14e857bea2`](https://github.com/nodejs/node/commit/14e857bea2)] - **deps**: update corepack to 0.28.0 (Node.js GitHub Bot) [#52616](https://github.com/nodejs/node/pull/52616)
- \[[`7f5dd44ca6`](https://github.com/nodejs/node/commit/7f5dd44ca6)] - **deps**: upgrade npm to 10.7.0 (npm team) [#52767](https://github.com/nodejs/node/pull/52767)
- \[[`78f84ebb09`](https://github.com/nodejs/node/commit/78f84ebb09)] - **deps**: update ngtcp2 to 1.3.0 (Node.js GitHub Bot) [#51796](https://github.com/nodejs/node/pull/51796)
- \[[`1f489a3753`](https://github.com/nodejs/node/commit/1f489a3753)] - **deps**: update ngtcp2 to 1.2.0 (Node.js GitHub Bot) [#51584](https://github.com/nodejs/node/pull/51584)
- \[[`3034968225`](https://github.com/nodejs/node/commit/3034968225)] - **deps**: update ngtcp2 to 1.1.0 (Node.js GitHub Bot) [#51319](https://github.com/nodejs/node/pull/51319)
- \[[`1aa9da467f`](https://github.com/nodejs/node/commit/1aa9da467f)] - **deps**: add nghttp3/\*\*/.deps to .gitignore (Luigi Pinca) [#51400](https://github.com/nodejs/node/pull/51400)
- \[[`28c0c78c9a`](https://github.com/nodejs/node/commit/28c0c78c9a)] - **deps**: update ngtcp2 and nghttp3 (James M Snell) [#51291](https://github.com/nodejs/node/pull/51291)
- \[[`8fd5a35364`](https://github.com/nodejs/node/commit/8fd5a35364)] - **deps**: upgrade npm to 10.5.2 (npm team) [#52458](https://github.com/nodejs/node/pull/52458)
- \[[`2c53ff31c9`](https://github.com/nodejs/node/commit/2c53ff31c9)] - **deps**: update acorn-walk to 8.3.2 (Node.js GitHub Bot) [#51457](https://github.com/nodejs/node/pull/51457)
- \[[`12f28f33c2`](https://github.com/nodejs/node/commit/12f28f33c2)] - **deps**: update acorn to 8.11.3 (Node.js GitHub Bot) [#51317](https://github.com/nodejs/node/pull/51317)
- \[[`dddb7eb3e0`](https://github.com/nodejs/node/commit/dddb7eb3e0)] - **deps**: update acorn-walk to 8.3.1 (Node.js GitHub Bot) [#50457](https://github.com/nodejs/node/pull/50457)
- \[[`c86550e607`](https://github.com/nodejs/node/commit/c86550e607)] - **deps**: update acorn-walk to 8.3.0 (Node.js GitHub Bot) [#50457](https://github.com/nodejs/node/pull/50457)
- \[[`9500817f66`](https://github.com/nodejs/node/commit/9500817f66)] - **deps**: update acorn to 8.11.2 (Node.js GitHub Bot) [#50460](https://github.com/nodejs/node/pull/50460)
- \[[`7a8c7b6275`](https://github.com/nodejs/node/commit/7a8c7b6275)] - **deps**: update ada to 2.7.7 (Node.js GitHub Bot) [#52028](https://github.com/nodejs/node/pull/52028)
- \[[`b199889943`](https://github.com/nodejs/node/commit/b199889943)] - **deps**: update corepack to 0.26.0 (Node.js GitHub Bot) [#52027](https://github.com/nodejs/node/pull/52027)
- \[[`052b0ba0c6`](https://github.com/nodejs/node/commit/052b0ba0c6)] - **deps**: upgrade npm to 10.5.1 (npm team) [#52351](https://github.com/nodejs/node/pull/52351)
- \[[`209823d3af`](https://github.com/nodejs/node/commit/209823d3af)] - **deps**: update simdutf to 5.2.4 (Node.js GitHub Bot) [#52473](https://github.com/nodejs/node/pull/52473)
- \[[`5114cbe18a`](https://github.com/nodejs/node/commit/5114cbe18a)] - **deps**: update simdutf to 5.2.3 (Yagiz Nizipli) [#52381](https://github.com/nodejs/node/pull/52381)
- \[[`be30309ea0`](https://github.com/nodejs/node/commit/be30309ea0)] - **deps**: update simdutf to 5.0.0 (Daniel Lemire) [#52138](https://github.com/nodejs/node/pull/52138)
- \[[`b56f66e250`](https://github.com/nodejs/node/commit/b56f66e250)] - **deps**: update simdutf to 4.0.9 (Node.js GitHub Bot) [#51655](https://github.com/nodejs/node/pull/51655)
- \[[`a9f3b9d9d1`](https://github.com/nodejs/node/commit/a9f3b9d9d1)] - **deps**: update nghttp2 to 1.61.0 (Node.js GitHub Bot) [#52395](https://github.com/nodejs/node/pull/52395)
- \[[`1b6fa70620`](https://github.com/nodejs/node/commit/1b6fa70620)] - **deps**: update nghttp2 to 1.60.0 (Node.js GitHub Bot) [#51948](https://github.com/nodejs/node/pull/51948)
- \[[`3c9dbbf4d4`](https://github.com/nodejs/node/commit/3c9dbbf4d4)] - **deps**: update nghttp2 to 1.59.0 (Node.js GitHub Bot) [#51581](https://github.com/nodejs/node/pull/51581)
- \[[`e28316da54`](https://github.com/nodejs/node/commit/e28316da54)] - **deps**: update nghttp2 to 1.58.0 (Node.js GitHub Bot) [#50441](https://github.com/nodejs/node/pull/50441)
- \[[`678641f470`](https://github.com/nodejs/node/commit/678641f470)] - **deps**: V8: cherry-pick d15d49b09dc7 (Bo Anderson) [#52337](https://github.com/nodejs/node/pull/52337)
- \[[`1147fee7d9`](https://github.com/nodejs/node/commit/1147fee7d9)] - **doc**: remove ableist language from crypto (Jamie King) [#52063](https://github.com/nodejs/node/pull/52063)
- \[[`5e93eae972`](https://github.com/nodejs/node/commit/5e93eae972)] - **doc**: add release key for marco-ippolito (marco-ippolito) [#52257](https://github.com/nodejs/node/pull/52257)
- \[[`6689a98488`](https://github.com/nodejs/node/commit/6689a98488)] - **http**: remove closeIdleConnections function while calling server close (Kumar Rishav) [#52336](https://github.com/nodejs/node/pull/52336)
- \[[`71616e8a8a`](https://github.com/nodejs/node/commit/71616e8a8a)] - **node-api**: make tsfn accept napi_finalize once more (Gabriel Schulhof) [#51801](https://github.com/nodejs/node/pull/51801)
- \[[`d9d9e62474`](https://github.com/nodejs/node/commit/d9d9e62474)] - **src**: avoid draining platform tasks at FreeEnvironment (Chengzhong Wu) [#51290](https://github.com/nodejs/node/pull/51290)
- \[[`e5fc8ec9fc`](https://github.com/nodejs/node/commit/e5fc8ec9fc)] - **test**: skip v8-updates/test-linux-perf (Michaël Zasso) [#49639](https://github.com/nodejs/node/pull/49639)
- \[[`351ef189ca`](https://github.com/nodejs/node/commit/351ef189ca)] - **test**: v8: Add test-linux-perf-logger test suite (Luke Albao) [#50352](https://github.com/nodejs/node/pull/50352)
- \[[`5cec2efc31`](https://github.com/nodejs/node/commit/5cec2efc31)] - **test**: reduce the number of requests and parsers (Luigi Pinca) [#50240](https://github.com/nodejs/node/pull/50240)
- \[[`5186e453d9`](https://github.com/nodejs/node/commit/5186e453d9)] - **test**: deflake test-http-regr-gh-2928 (Luigi Pinca) [#49574](https://github.com/nodejs/node/pull/49574)
- \[[`c60cd67e1c`](https://github.com/nodejs/node/commit/c60cd67e1c)] - **test**: skip test for dynamically linked OpenSSL (Richard Lau) [#52542](https://github.com/nodejs/node/pull/52542)

Windows 32-bit Installer: https://nodejs.org/dist/v18.20.3/node-v18.20.3-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v18.20.3/node-v18.20.3-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v18.20.3/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v18.20.3/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v18.20.3/node-v18.20.3.pkg \
macOS Apple Silicon 64-bit Binary: https://nodejs.org/dist/v18.20.3/node-v18.20.3-darwin-arm64.tar.gz \
macOS Intel 64-bit Binary: https://nodejs.org/dist/v18.20.3/node-v18.20.3-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v18.20.3/node-v18.20.3-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v18.20.3/node-v18.20.3-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v18.20.3/node-v18.20.3-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v18.20.3/node-v18.20.3-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v18.20.3/node-v18.20.3-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v18.20.3/node-v18.20.3-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v18.20.3/node-v18.20.3.tar.gz \
Other release files: https://nodejs.org/dist/v18.20.3/ \
Documentation: https://nodejs.org/docs/v18.20.3/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

6a1a5c5b1c2c0aec48b8070d50403ef5dbe9ecb9d69c687849183d190d597ac8  node-v18.20.3-aix-ppc64.tar.gz
99328b985f7336a8fcfb62fda382155d210979fcca928e2dd75b7148d9bba636  node-v18.20.3-darwin-arm64.tar.gz
e37a21a764ac97077dd06f9aff273e27e997fed7d2b4f9ed4c5bf023e3446d37  node-v18.20.3-darwin-arm64.tar.xz
317a4607390c923610303e8583972e23fb656e9d348d3740bde0f1a94cdb7e0c  node-v18.20.3-darwin-x64.tar.gz
04df598874fa15c968a1123ce8fabc26d7a2270a11cec6e5859aca67341004dd  node-v18.20.3-darwin-x64.tar.xz
a76c43a7829493e710d7aaaf335c1b4884a6356f8ae4497e26389255095b84af  node-v18.20.3-headers.tar.gz
75a5ce13c010e31fa0ff72338678e1c8c46ae8ef7e97acb8fa1c406c55d2affa  node-v18.20.3-headers.tar.xz
7aab1e72b5f214ae08895ca0cfbf68e1731d53a6ebf945b929446564f2cc80d8  node-v18.20.3-linux-arm64.tar.gz
3c497c19ddbf75bab7fecb36ddf1738622f0ba244aa1e0aebc70e46daf1a0794  node-v18.20.3-linux-arm64.tar.xz
eb51ef4a1e1a4c33bfeb658547498c676bf467f660169ac0453961e54fd60285  node-v18.20.3-linux-armv7l.tar.gz
d5c6efda2f88718556cd0d6df4cc5adadc4f24aef80c63c3dbd54b1ecf34f52c  node-v18.20.3-linux-armv7l.tar.xz
7d338eb66e3cd4a89a0682ddc5135a5037fbe2ce20327d467f01ef07ec74b3bf  node-v18.20.3-linux-ppc64le.tar.gz
41c3e3d36d0ba6654612c424ad8aba63b6e92f451332eaad9e92286ed8a5a434  node-v18.20.3-linux-ppc64le.tar.xz
1e5aedfcb010581ba1527ae159079d7845ef9c0598bf7f915e2c3f8d1d473c11  node-v18.20.3-linux-s390x.tar.gz
6c71cb6f491217755e9703a7c7d950476ca99d30aca20c97c5bced432732957c  node-v18.20.3-linux-s390x.tar.xz
262bdd5d59608360cb872cdb3d2aa089867b46963ec7e6a000c1afc7d183db5c  node-v18.20.3-linux-x64.tar.gz
ffd6147c263b81016742dc1e72dc68885a3ca9b441d9744f9b76cad362d0cc5f  node-v18.20.3-linux-x64.tar.xz
924cbd6ac35512b26180ee18f4a926ef3cf9adf8cf981a39882ba1dc19e82fcf  node-v18.20.3.pkg
f35c9b9923c7b2e9243e7e2d10cd9ae61fbd5b925df3debbb72d5a70dbff555d  node-v18.20.3.tar.gz
4b144f9fd6ae4b1d62b732c5b3160e7b9e84be4af0ae062c7b484e89ea41ae8d  node-v18.20.3.tar.xz
61fd506729f6f0754f16c94b6d497b79f8f444892248ecefd3c5b6a20ae3db07  node-v18.20.3-win-x64.7z
8e91df18904f9cc2950dca17b737e779dbd780c7b7a2192458d42af901c8f2d3  node-v18.20.3-win-x64.zip
0ebd20313ddfa478b8d5e1510283133cfd1ba55694964da8beae7a2dbc278d4b  node-v18.20.3-win-x86.7z
5db6c8a093cc8fa9ed6060a35a735c6cf811d3d5ed6b1e3fb7856708dda515d4  node-v18.20.3-win-x86.zip
073a9a13c5b29c5a5b2cc4ddb0b52a53c569e9871fd377bb706a0a7b608168e4  node-v18.20.3-x64.msi
dcbc42e359ca40093ffc960267cc45e2f73dadbd6065ceb396f9eeeacd0f2d8d  node-v18.20.3-x86.msi
c0f2ecac65797fe24e50252edf718fea2942cb1e1b0a835c72f7b1bb1ff1d2b1  win-x64/node.exe
64d93225aaece04e3cd45177d6dea2b22df49e127281fefa3ade43ac46a36cc6  win-x64/node.lib
ca82b6df70c106f3d481b4a122c272f8c7530d848c08508a8e82a892d2ef7da7  win-x64/node_pdb.7z
ac40c2f3398e221774283e81e982a51de87019a54c790c4dadb8d4361e671c25  win-x64/node_pdb.zip
4e8adabd5d59a52696cbccb3eb0e583056ad59419981e072374a7972bc733ac3  win-x86/node.exe
df34047e8ae646e6f43d76ecbec9709a185f29e01f49b377c4c46070cacc2859  win-x86/node.lib
cd6b2a6f63e59dcfb05a09572be4180934104b000ecff4bb5850f19ba50f2b2e  win-x86/node_pdb.7z
57c5cb6076555ff8d3f65b0a8ea63935a415832a91f6d69d091fe35d75f6034d  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEyC+jrhy+3Gvka5NgxDzsRcF6uTwFAmZMja0ACgkQxDzsRcF6
uTxcWRAAnhHAP6BDQtkHViQJgs0cirIE+SSznnAql2YOGmzxYyK25pEwcOj0od7n
GbhSX2GdVElR/MeVTa1GbDozAWAzRkurKBDyHHjdRrRbBbXLlqOUCy6EQoMmVA9c
j5TIvkVhi4UFQcyH4JnM2ZKPbnd0TvOoz4S5svmmQVd9UMe9MhZoHlEe3Uw5QdVO
5xk9UpzPWLhVS0YQFwaITj5xe/3j/zaEWDqSB/JMrg6M7D2ZjGpTGw0bmNo8DCdc
x7RSDobmIAj7CkevOUTTj4KIJk2ha/37xIT+j7aZCtAk4ORZ1xhrNTigH63Uw9et
YK6GWsSLTItGEpaf3KAtFB+nUn2nAWXEJG5kJ8ko1p5oslfCrfE7s7VFn4x5C81n
b750hJZSiZTDOAn8iA8MzcTHuHYTZyRrIeC0WErqep8ba5kbDss4rGJuFi3IOP5U
RSST62zTTPrA3o9wtsNQ7fuBE5kIMNFks34VVJ/uqn2Auz+YuLDaVmMvjnAzE/Wd
7d7RU/tt/fYP7gfVeJPBmai+4FUs/YDKykNVX6+TDmYimbgYrFj3JpypK8NoUKzW
JPXUkAvVsZvQ0xz0E/WAyLwEOmHzIZQ+StKs45q0+dHHS3K5hftjSoKdxwf+JGD9
iRpVTN1QBnblXmrlwHN0tVwcKDv5TCKRXkgk91P56cnExY6rd8A=
=rBjG
-----END PGP SIGNATURE-----
```
