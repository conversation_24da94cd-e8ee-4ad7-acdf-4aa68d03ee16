---
date: '2019-01-18T13:43:52.227Z'
category: release
title: Node v11.7.0 (Current)
layout: blog-post
author: <PERSON><PERSON>
---

### Notable Changes

- **compression / zlib**:
  - Added brotli support (<PERSON> and <PERSON>) [#24938](https://github.com/nodejs/node/pull/24938)
- **console**:
  - Added `inspectOptions` option (<PERSON><PERSON>) [#24978](https://github.com/nodejs/node/pull/24978)
- **crypto**:
  - Always accept private keys as public keys (<PERSON>) [#25217](https://github.com/nodejs/node/pull/25217)
- **deps**:
  - Upgrade npm to v6.5.0 (<PERSON>) [#25234](https://github.com/nodejs/node/pull/25234)
- **fs**:
  - Use internalBinding('fs') internally instead of process.binding('fs') (<PERSON><PERSON><PERSON>) [#22478](https://github.com/nodejs/node/pull/22478)
- **http(s)**:
  - Support overriding http\\s.globalAgent (<PERSON>) [#25170](https://github.com/nodejs/node/pull/25170)
- **util**:
  - Inspect ArrayBuffers contents closely (Ruben Bridgewater) [#25006](https://github.com/nodejs/node/pull/25006)
- **worker**:
  - Expose workers by default and remove `--experimental-worker` flag (Anna Henningsen) [#25361](https://github.com/nodejs/node/pull/25361)

### Commits

- [[`e09dd0c5f0`](https://github.com/nodejs/node/commit/e09dd0c5f0)] - **assert**: make `actual` and `expected` getters (Ruben Bridgewater) [#25250](https://github.com/nodejs/node/pull/25250)
- [[`516f75fda8`](https://github.com/nodejs/node/commit/516f75fda8)] - **benchmark**: fix net-wrap-js-stream-passthrough (Rich Trott) [#25273](https://github.com/nodejs/node/pull/25273)
- [[`9a627a4694`](https://github.com/nodejs/node/commit/9a627a4694)] - **(SEMVER-MINOR)** **benchmark,test**: add brotli (Anna Henningsen) [#24938](https://github.com/nodejs/node/pull/24938)
- [[`8e84ccb502`](https://github.com/nodejs/node/commit/8e84ccb502)] - **buffer**: move Buffer prototype wiring into internal/buffer.js (Joyee Cheung) [#25292](https://github.com/nodejs/node/pull/25292)
- [[`042d20ab47`](https://github.com/nodejs/node/commit/042d20ab47)] - **buffer**: move initialization of buffer prototype into node.js (Joyee Cheung) [#25292](https://github.com/nodejs/node/pull/25292)
- [[`68014fbc22`](https://github.com/nodejs/node/commit/68014fbc22)] - **buffer**: inspect extra properties (Ruben Bridgewater) [#25150](https://github.com/nodejs/node/pull/25150)
- [[`1d4940888d`](https://github.com/nodejs/node/commit/1d4940888d)] - **buffer**: refactor checks for SlowBuffer creation (P. Mike) [#25266](https://github.com/nodejs/node/pull/25266)
- [[`7dc4c3be03`](https://github.com/nodejs/node/commit/7dc4c3be03)] - **buffer**: fix crash for invalid index types (Anna Henningsen) [#25154](https://github.com/nodejs/node/pull/25154)
- [[`a4f50a62d5`](https://github.com/nodejs/node/commit/a4f50a62d5)] - **build**: set `-blibpath:` for AIX (Richard Lau) [#25447](https://github.com/nodejs/node/pull/25447)
- [[`07ffa3f189`](https://github.com/nodejs/node/commit/07ffa3f189)] - **build**: add check for empty openssl-fips flag (Daniel Bevenius) [#25391](https://github.com/nodejs/node/pull/25391)
- [[`a2cc4bad0e`](https://github.com/nodejs/node/commit/a2cc4bad0e)] - **build**: fix Windows shared lib build (Richard Lau) [#25166](https://github.com/nodejs/node/pull/25166)
- [[`56e7e4f0cd`](https://github.com/nodejs/node/commit/56e7e4f0cd)] - **child_process**: simplify argument handling (cjihrig) [#25194](https://github.com/nodejs/node/pull/25194)
- [[`272ddb1765`](https://github.com/nodejs/node/commit/272ddb1765)] - **console**: improve inspectOptions validation (cjihrig) [#25090](https://github.com/nodejs/node/pull/25090)
- [[`65d485b880`](https://github.com/nodejs/node/commit/65d485b880)] - **(SEMVER-MINOR)** **console**: add `inspectOptions` option (Ruben Bridgewater) [#24978](https://github.com/nodejs/node/pull/24978)
- [[`57323e8048`](https://github.com/nodejs/node/commit/57323e8048)] - **console**: move the inspector console wrapping in a separate file (Joyee Cheung) [#24709](https://github.com/nodejs/node/pull/24709)
- [[`b549058cc4`](https://github.com/nodejs/node/commit/b549058cc4)] - **console**: split console into global.js and constructor.js (Joyee Cheung) [#24709](https://github.com/nodejs/node/pull/24709)
- [[`4052aec321`](https://github.com/nodejs/node/commit/4052aec321)] - **console**: lazy load process.stderr and process.stdout (Joyee Cheung) [#24534](https://github.com/nodejs/node/pull/24534)
- [[`7f5bb9d3bf`](https://github.com/nodejs/node/commit/7f5bb9d3bf)] - **console**: bind methods from the prototype chain in Console (Joyee Cheung) [#24047](https://github.com/nodejs/node/pull/24047)
- [[`b2b0645805`](https://github.com/nodejs/node/commit/b2b0645805)] - **console**: create the global console from Console constructor (Joyee Cheung) [#25420](https://github.com/nodejs/node/pull/25420)
- [[`561c2689ef`](https://github.com/nodejs/node/commit/561c2689ef)] - **console**: use spread notation instead of Object.assign (Ruben Bridgewater) [#25149](https://github.com/nodejs/node/pull/25149)
- [[`63fbd00834`](https://github.com/nodejs/node/commit/63fbd00834)] - **coverage**: pass cwd to path.resolve() in setup (cjihrig) [#25289](https://github.com/nodejs/node/pull/25289)
- [[`daca3188af`](https://github.com/nodejs/node/commit/daca3188af)] - **coverage**: use process.\_rawDebug() during setup (cjihrig) [#25289](https://github.com/nodejs/node/pull/25289)
- [[`eaaaa0d479`](https://github.com/nodejs/node/commit/eaaaa0d479)] - **(SEMVER-MINOR)** **crypto**: always accept private keys as public keys (Tobias Nießen) [#25217](https://github.com/nodejs/node/pull/25217)
- [[`32e45b20da`](https://github.com/nodejs/node/commit/32e45b20da)] - **crypto**: fix key object wrapping in sync keygen (Tobias Nießen) [#25326](https://github.com/nodejs/node/pull/25326)
- [[`bc6f4bc0c5`](https://github.com/nodejs/node/commit/bc6f4bc0c5)] - **crypto**: add crypto/keys to cannotUseCache (Daniel Bevenius) [#25237](https://github.com/nodejs/node/pull/25237)
- [[`f3ebc391a3`](https://github.com/nodejs/node/commit/f3ebc391a3)] - **crypto**: fix zero byte allocation assertion failure (Tobias Nießen) [#25248](https://github.com/nodejs/node/pull/25248)
- [[`e1d4f4384a`](https://github.com/nodejs/node/commit/e1d4f4384a)] - **deps**: cherry-pick d9fbfeb from upstream V8 (Alexey Kozyatinskiy) [#25331](https://github.com/nodejs/node/pull/25331)
- [[`91015918d8`](https://github.com/nodejs/node/commit/91015918d8)] - **deps**: upgrade npm to v6.5.0 (Jordan Harband) [#25234](https://github.com/nodejs/node/pull/25234)
- [[`11c01a6a69`](https://github.com/nodejs/node/commit/11c01a6a69)] - **(SEMVER-MINOR)** **deps**: add brotli (Hackzzila) [#24938](https://github.com/nodejs/node/pull/24938)
- [[`f2abe7bf76`](https://github.com/nodejs/node/commit/f2abe7bf76)] - **deps**: V8: backport 3e010af (Ruben Bridgewater) [#25101](https://github.com/nodejs/node/pull/25101)
- [[`201cf97fcb`](https://github.com/nodejs/node/commit/201cf97fcb)] - **deps**: V8: backport bf84766 (Ruben Bridgewater) [#25101](https://github.com/nodejs/node/pull/25101)
- [[`ec87b6c994`](https://github.com/nodejs/node/commit/ec87b6c994)] - **(SEMVER-MINOR)** **deps,tools**: update license-builder.sh and LICENSE (Hackzzila) [#24938](https://github.com/nodejs/node/pull/24938)
- [[`5b4fab1a40`](https://github.com/nodejs/node/commit/5b4fab1a40)] - **dns**: fix TTL value for AAAA replies to `resolveAny()` (Anna Henningsen) [#25187](https://github.com/nodejs/node/pull/25187)
- [[`edab2d61fd`](https://github.com/nodejs/node/commit/edab2d61fd)] - **doc**: revert incorrect change on readable.\_read (Matteo Collina) [#25442](https://github.com/nodejs/node/pull/25442)
- [[`2172dbfce4`](https://github.com/nodejs/node/commit/2172dbfce4)] - **doc**: add TLSSocket.isSessionReused() docs (Sam Roberts) [#25423](https://github.com/nodejs/node/pull/25423)
- [[`7123167e31`](https://github.com/nodejs/node/commit/7123167e31)] - **doc**: improve Sign/Verify examples and docs (Sam Roberts) [#25452](https://github.com/nodejs/node/pull/25452)
- [[`9a61a7abb3`](https://github.com/nodejs/node/commit/9a61a7abb3)] - **doc**: fix section order in vm.md (Vse Mozhet Byt) [#25374](https://github.com/nodejs/node/pull/25374)
- [[`2b0c8538ef`](https://github.com/nodejs/node/commit/2b0c8538ef)] - **doc**: fix sorting in buffer.md (Vse Mozhet Byt) [#25477](https://github.com/nodejs/node/pull/25477)
- [[`f8bb544bfb`](https://github.com/nodejs/node/commit/f8bb544bfb)] - **doc**: fix `napi_open_callback_scope` description (Philipp Renoth) [#25366](https://github.com/nodejs/node/pull/25366)
- [[`b67c4b4f99`](https://github.com/nodejs/node/commit/b67c4b4f99)] - **doc**: document that stream.on('close') was changed in Node 10 (Matteo Collina) [#25413](https://github.com/nodejs/node/pull/25413)
- [[`3db7a9ffba`](https://github.com/nodejs/node/commit/3db7a9ffba)] - **doc**: fix, unify, formalize, and amplify vm.md (Vse Mozhet Byt) [#25422](https://github.com/nodejs/node/pull/25422)
- [[`ebd202736c`](https://github.com/nodejs/node/commit/ebd202736c)] - **doc**: fix the path to postMessage() (Mitar) [#25332](https://github.com/nodejs/node/pull/25332)
- [[`177635b320`](https://github.com/nodejs/node/commit/177635b320)] - **doc**: update `os.networkInterfaces()` example (jvelezpo) [#25417](https://github.com/nodejs/node/pull/25417)
- [[`67782613bb`](https://github.com/nodejs/node/commit/67782613bb)] - **doc**: make sure that calls to .read() are looped (Matteo Collina) [#25375](https://github.com/nodejs/node/pull/25375)
- [[`f58b5300cd`](https://github.com/nodejs/node/commit/f58b5300cd)] - **doc**: wrap and punctuate YAML description text (Sam Roberts) [#25419](https://github.com/nodejs/node/pull/25419)
- [[`8380bd46a0`](https://github.com/nodejs/node/commit/8380bd46a0)] - **doc**: add history to http.request.setTimeout() (James Bunton) [#25121](https://github.com/nodejs/node/pull/25121)
- [[`8bc1651249`](https://github.com/nodejs/node/commit/8bc1651249)] - **doc**: add clarification for exception behaviour (Michael Dawson) [#25339](https://github.com/nodejs/node/pull/25339)
- [[`f3d86391d9`](https://github.com/nodejs/node/commit/f3d86391d9)] - **doc**: clarify timing of socket.connecting (Sam Roberts) [#25333](https://github.com/nodejs/node/pull/25333)
- [[`7d46437c45`](https://github.com/nodejs/node/commit/7d46437c45)] - **doc**: update benchmark doc (Kazushi Kitaya) [#25367](https://github.com/nodejs/node/pull/25367)
- [[`071f84e80a`](https://github.com/nodejs/node/commit/071f84e80a)] - **doc**: use lowercase for zlib (Rich Trott) [#25371](https://github.com/nodejs/node/pull/25371)
- [[`7d1d26191d`](https://github.com/nodejs/node/commit/7d1d26191d)] - **doc**: fix heading in cpp style guide (Kazushi Kitaya) [#25303](https://github.com/nodejs/node/pull/25303)
- [[`354fba1b26`](https://github.com/nodejs/node/commit/354fba1b26)] - **doc**: fix process.stdin example (Anna Henningsen) [#25344](https://github.com/nodejs/node/pull/25344)
- [[`1e20c5e440`](https://github.com/nodejs/node/commit/1e20c5e440)] - **doc**: make modules.md more accurate (Vse Mozhet Byt) [#25357](https://github.com/nodejs/node/pull/25357)
- [[`f8dcbba563`](https://github.com/nodejs/node/commit/f8dcbba563)] - **doc**: fs.mkdir('/') throws EPERM on Windows (Corey Farrell) [#25340](https://github.com/nodejs/node/pull/25340)
- [[`b9b2ba22ec`](https://github.com/nodejs/node/commit/b9b2ba22ec)] - **doc**: document key encryption options (Tobias Nießen) [#23632](https://github.com/nodejs/node/pull/23632)
- [[`f5008fd1ef`](https://github.com/nodejs/node/commit/f5008fd1ef)] - **doc**: simplify DEP0119 wording (cjihrig) [#25276](https://github.com/nodejs/node/pull/25276)
- [[`1c5a99797b`](https://github.com/nodejs/node/commit/1c5a99797b)] - **(SEMVER-MINOR)** **doc**: add documentation for brotli support (Anna Henningsen) [#24938](https://github.com/nodejs/node/pull/24938)
- [[`be45469744`](https://github.com/nodejs/node/commit/be45469744)] - **doc**: edit and simplify util.inspect() docs (cjihrig) [#25195](https://github.com/nodejs/node/pull/25195)
- [[`8a701c3fce`](https://github.com/nodejs/node/commit/8a701c3fce)] - **doc**: include license for src/large_pages in LICENSE (Ujjwal Sharma) [#25246](https://github.com/nodejs/node/pull/25246)
- [[`e6da77b12c`](https://github.com/nodejs/node/commit/e6da77b12c)] - **doc**: describe TLS session resumption (Sam Roberts) [#25174](https://github.com/nodejs/node/pull/25174)
- [[`3af173df00`](https://github.com/nodejs/node/commit/3af173df00)] - **doc**: link and expand --tls-cipher-list docs (Sam Roberts) [#25174](https://github.com/nodejs/node/pull/25174)
- [[`39b3fd1b61`](https://github.com/nodejs/node/commit/39b3fd1b61)] - **doc**: revise "Breaking Changes to Internal Elements" (Rich Trott) [#25190](https://github.com/nodejs/node/pull/25190)
- [[`2c50bcda8a`](https://github.com/nodejs/node/commit/2c50bcda8a)] - **doc**: fix NAPI typo (Philipp Renoth) [#25216](https://github.com/nodejs/node/pull/25216)
- [[`1697604ae0`](https://github.com/nodejs/node/commit/1697604ae0)] - **doc,worker**: revise worker_threads.md (Rich Trott) [#25402](https://github.com/nodejs/node/pull/25402)
- [[`dd0381fe4e`](https://github.com/nodejs/node/commit/dd0381fe4e)] - **(SEMVER-MAJOR)** **fs**: make process.binding('fs') internal (Masashi Hirano) [#22478](https://github.com/nodejs/node/pull/22478)
- [[`ca7adcafda`](https://github.com/nodejs/node/commit/ca7adcafda)] - **fs**: extract start and end check into checkPosition (ZYSzys) [#25264](https://github.com/nodejs/node/pull/25264)
- [[`26f2eb8b12`](https://github.com/nodejs/node/commit/26f2eb8b12)] - **http2**: add test case for goaway (Anto Aravinth) [#24054](https://github.com/nodejs/node/pull/24054)
- [[`445ba9f283`](https://github.com/nodejs/node/commit/445ba9f283)] - **inspector**: move process.binding to internalBinding (Beni von Cheni) [#24931](https://github.com/nodejs/node/pull/24931)
- [[`8cc97571a4`](https://github.com/nodejs/node/commit/8cc97571a4)] - **_Revert_** "**inspector**: move process.binding to internalBinding" (Joyee Cheung) [#25446](https://github.com/nodejs/node/pull/25446)
- [[`4794cf601e`](https://github.com/nodejs/node/commit/4794cf601e)] - **inspector**: move process.binding to internalBinding (Beni von Cheni) [#24931](https://github.com/nodejs/node/pull/24931)
- [[`cb73fed430`](https://github.com/nodejs/node/commit/cb73fed430)] - **inspector, test**: verify reported console message (Eugene Ostroukhov) [#25455](https://github.com/nodejs/node/pull/25455)
- [[`6528ce6176`](https://github.com/nodejs/node/commit/6528ce6176)] - **lib**: expose all type checks from the internal types module (Ruben Bridgewater) [#25149](https://github.com/nodejs/node/pull/25149)
- [[`207612c723`](https://github.com/nodejs/node/commit/207612c723)] - **lib**: remove internalBinding('config').pendingDeprecation (Joyee Cheung) [#24962](https://github.com/nodejs/node/pull/24962)
- [[`d8ba520622`](https://github.com/nodejs/node/commit/d8ba520622)] - **lib**: remove unused NativeModule/NativeModule wraps (Joyee Cheung) [#24904](https://github.com/nodejs/node/pull/24904)
- [[`87a58beed7`](https://github.com/nodejs/node/commit/87a58beed7)] - **lib**: remove duplicated noop function (ZYSzys) [#24770](https://github.com/nodejs/node/pull/24770)
- [[`d7d772b2f8`](https://github.com/nodejs/node/commit/d7d772b2f8)] - **_Revert_** "**lib**: remove duplicated noop function" (Joyee Cheung) [#25446](https://github.com/nodejs/node/pull/25446)
- [[`42a7eaf9d4`](https://github.com/nodejs/node/commit/42a7eaf9d4)] - **_Revert_** "**lib**: remove unused NativeModule/NativeModule wraps" (Joyee Cheung) [#25446](https://github.com/nodejs/node/pull/25446)
- [[`b48865f03f`](https://github.com/nodejs/node/commit/b48865f03f)] - **lib**: move lib/console.js to lib/internal/console/constructor.js (Joyee Cheung) [#24709](https://github.com/nodejs/node/pull/24709)
- [[`3350230e20`](https://github.com/nodejs/node/commit/3350230e20)] - **lib**: remove internal `util._extends()` usage (Ruben Bridgewater) [#25105](https://github.com/nodejs/node/pull/25105)
- [[`73c3a3d5ed`](https://github.com/nodejs/node/commit/73c3a3d5ed)] - **(SEMVER-MAJOR)** **lib**: make the global console \[\[Prototype\]\] an empty object (Joyee Cheung) [#23509](https://github.com/nodejs/node/pull/23509)
- [[`8d0c638583`](https://github.com/nodejs/node/commit/8d0c638583)] - **(SEMVER-MINOR)** **lib**: support overriding http\\s.globalAgent (Roy Sommer) [#25170](https://github.com/nodejs/node/pull/25170)
- [[`217bb0e5f0`](https://github.com/nodejs/node/commit/217bb0e5f0)] - **lib**: simplify several debug() calls (cjihrig) [#25241](https://github.com/nodejs/node/pull/25241)
- [[`e14f8646e2`](https://github.com/nodejs/node/commit/e14f8646e2)] - **lib,test**: remove lib/internal/test/unicode.js (Rich Trott) [#25298](https://github.com/nodejs/node/pull/25298)
- [[`c13e5be740`](https://github.com/nodejs/node/commit/c13e5be740)] - **net**: use decodeStrings public API for writable stream (Rich Trott) [#25201](https://github.com/nodejs/node/pull/25201)
- [[`9ac8d41925`](https://github.com/nodejs/node/commit/9ac8d41925)] - **net**: check for close on stream, not parent (David Halls) [#25026](https://github.com/nodejs/node/pull/25026)
- [[`3bd8e4b6a3`](https://github.com/nodejs/node/commit/3bd8e4b6a3)] - **os**: add fallback for undefined CPUs (Minwoo Jung) [#25493](https://github.com/nodejs/node/pull/25493)
- [[`840ec230f1`](https://github.com/nodejs/node/commit/840ec230f1)] - **os**: improve networkInterfaces() performance (Brian White) [#25410](https://github.com/nodejs/node/pull/25410)
- [[`d197105476`](https://github.com/nodejs/node/commit/d197105476)] - **os**: move process.binding('os') to internalBinding (briete) [#25087](https://github.com/nodejs/node/pull/25087)
- [[`f64e5ec148`](https://github.com/nodejs/node/commit/f64e5ec148)] - **_Revert_** "**os**: move process.binding('os') to internalBinding" (Joyee Cheung) [#25446](https://github.com/nodejs/node/pull/25446)
- [[`55d185f0dd`](https://github.com/nodejs/node/commit/55d185f0dd)] - **os**: move process.binding('os') to internalBinding (briete) [#25087](https://github.com/nodejs/node/pull/25087)
- [[`c718592147`](https://github.com/nodejs/node/commit/c718592147)] - **process**: register the inspector async hooks in bootstrap/node.js (Joyee Cheung) [#25443](https://github.com/nodejs/node/pull/25443)
- [[`b524a7bed0`](https://github.com/nodejs/node/commit/b524a7bed0)] - **process**: refactor coverage setup during bootstrap (Joyee Cheung) [#25398](https://github.com/nodejs/node/pull/25398)
- [[`83900148e6`](https://github.com/nodejs/node/commit/83900148e6)] - **process**: allow StartExecution() to take a main script ID (Joyee Cheung) [#25474](https://github.com/nodejs/node/pull/25474)
- [[`28baf266c7`](https://github.com/nodejs/node/commit/28baf266c7)] - **process**: move C++ process events into node_process_events.cc (Joyee Cheung) [#25397](https://github.com/nodejs/node/pull/25397)
- [[`5eada9dce4`](https://github.com/nodejs/node/commit/5eada9dce4)] - **process**: move --help and --bash-completeion handling to startExecution (Joyee Cheung) [#25262](https://github.com/nodejs/node/pull/25262)
- [[`743056e3af`](https://github.com/nodejs/node/commit/743056e3af)] - **process**: move process.features initialization into node.js (Joyee Cheung) [#25239](https://github.com/nodejs/node/pull/25239)
- [[`c07b12da42`](https://github.com/nodejs/node/commit/c07b12da42)] - **process**: make tick callback and promise rejection callback more robust (Joyee Cheung) [#25200](https://github.com/nodejs/node/pull/25200)
- [[`655c1c9232`](https://github.com/nodejs/node/commit/655c1c9232)] - **process**: move worker bootstrap code into worker_thread_only.js (Joyee Cheung) [#25199](https://github.com/nodejs/node/pull/25199)
- [[`9480e1b795`](https://github.com/nodejs/node/commit/9480e1b795)] - **process**: split worker IO into internal/worker/io.js (Joyee Cheung) [#25199](https://github.com/nodejs/node/pull/25199)
- [[`456b1b55b1`](https://github.com/nodejs/node/commit/456b1b55b1)] - **process**: move eval and exception bootstrap ito process/execution.js (Joyee Cheung) [#25199](https://github.com/nodejs/node/pull/25199)
- [[`f32e6a81a6`](https://github.com/nodejs/node/commit/f32e6a81a6)] - **process**: make internal/queue_microtask.js more self-contained (Joyee Cheung) [#25189](https://github.com/nodejs/node/pull/25189)
- [[`6b5c962a0a`](https://github.com/nodejs/node/commit/6b5c962a0a)] - **process**: move child process IPC setup condition into node.js (Joyee Cheung) [#25130](https://github.com/nodejs/node/pull/25130)
- [[`e93dd4dad6`](https://github.com/nodejs/node/commit/e93dd4dad6)] - **process**: move POSIX credential accessors into node_credentials.cc (Joyee Cheung) [#25066](https://github.com/nodejs/node/pull/25066)
- [[`0e2fbe4ff4`](https://github.com/nodejs/node/commit/0e2fbe4ff4)] - **process**: specialize building and storage of process.config (Joyee Cheung) [#24816](https://github.com/nodejs/node/pull/24816)
- [[`18052364ce`](https://github.com/nodejs/node/commit/18052364ce)] - **process**: provide dummy stdio for non-console Windows apps (Anna Henningsen) [#20640](https://github.com/nodejs/node/pull/20640)
- [[`1ccaf9a8f1`](https://github.com/nodejs/node/commit/1ccaf9a8f1)] - **repl**: indicate if errors are thrown or not (Ruben Bridgewater) [#25253](https://github.com/nodejs/node/pull/25253)
- [[`2ed3fa187e`](https://github.com/nodejs/node/commit/2ed3fa187e)] - **src**: declare process-related C++ methods in node_process.h (Joyee Cheung) [#25397](https://github.com/nodejs/node/pull/25397)
- [[`49ac9688f3`](https://github.com/nodejs/node/commit/49ac9688f3)] - **src**: move process object creation into node_process_object.cc (Joyee Cheung) [#25397](https://github.com/nodejs/node/pull/25397)
- [[`299aefd81a`](https://github.com/nodejs/node/commit/299aefd81a)] - **src**: clean up `node::Init()` wrt embedder scenarios (Anna Henningsen) [#25370](https://github.com/nodejs/node/pull/25370)
- [[`dca6741b9b`](https://github.com/nodejs/node/commit/dca6741b9b)] - **src**: move InternalMakeCallback and MakeCallback (Joyee Cheung) [#25299](https://github.com/nodejs/node/pull/25299)
- [[`81924ffa4f`](https://github.com/nodejs/node/commit/81924ffa4f)] - **src**: remove unused isolate variable (Daniel Bevenius) [#25368](https://github.com/nodejs/node/pull/25368)
- [[`8e6175e001`](https://github.com/nodejs/node/commit/8e6175e001)] - **src**: use generic helper for splitting strings (Anna Henningsen) [#25363](https://github.com/nodejs/node/pull/25363)
- [[`6cdaf038ce`](https://github.com/nodejs/node/commit/6cdaf038ce)] - **src**: split `LoadEnvironment()` at `startExecution()` (Anna Henningsen) [#25320](https://github.com/nodejs/node/pull/25320)
- [[`c6adf4b44f`](https://github.com/nodejs/node/commit/c6adf4b44f)] - **src**: move per-process global variables into node::per_process (Joyee Cheung) [#25302](https://github.com/nodejs/node/pull/25302)
- [[`69d8e60596`](https://github.com/nodejs/node/commit/69d8e60596)] - **src**: use `internalBinding('config').hasInspector` in JS land (Joyee Cheung) [#25291](https://github.com/nodejs/node/pull/25291)
- [[`c5ab3408b1`](https://github.com/nodejs/node/commit/c5ab3408b1)] - **src**: refactor tickInfo access (Joyee Cheung) [#25200](https://github.com/nodejs/node/pull/25200)
- [[`2e33ad1caa`](https://github.com/nodejs/node/commit/2e33ad1caa)] - **src**: move process.nextTick and promise setup into node_task_queue.cc (Joyee Cheung) [#25163](https://github.com/nodejs/node/pull/25163)
- [[`fa74cd352f`](https://github.com/nodejs/node/commit/fa74cd352f)] - **src**: move symbols binding into node_symbols.cc (Joyee Cheung) [#25163](https://github.com/nodejs/node/pull/25163)
- [[`57a0cd4d48`](https://github.com/nodejs/node/commit/57a0cd4d48)] - **src**: move node::errno_string into node_errors.h/cc (Joyee Cheung) [#25396](https://github.com/nodejs/node/pull/25396)
- [[`f8ba4880ab`](https://github.com/nodejs/node/commit/f8ba4880ab)] - **src**: fix compiler warnings (cjihrig) [#25165](https://github.com/nodejs/node/pull/25165)
- [[`dde71520ba`](https://github.com/nodejs/node/commit/dde71520ba)] - **src**: move more process methods initialization in bootstrap/node.js (Joyee Cheung) [#25127](https://github.com/nodejs/node/pull/25127)
- [[`5fe774104f`](https://github.com/nodejs/node/commit/5fe774104f)] - **src**: dispose of V8 platform in `process.exit()` (Anna Henningsen) [#25061](https://github.com/nodejs/node/pull/25061)
- [[`e9b4d24eda`](https://github.com/nodejs/node/commit/e9b4d24eda)] - **src**: move arch, platform and release into node_metadata.cc (Joyee Cheung) [#25293](https://github.com/nodejs/node/pull/25293)
- [[`43535f56fd`](https://github.com/nodejs/node/commit/43535f56fd)] - **src**: simplify JS Array creation (Anna Henningsen) [#25288](https://github.com/nodejs/node/pull/25288)
- [[`de6f1f5e4d`](https://github.com/nodejs/node/commit/de6f1f5e4d)] - **src**: initialize ICU version in per_process::metadata.versions (Joyee Cheung) [#25115](https://github.com/nodejs/node/pull/25115)
- [[`e5b4af43fd`](https://github.com/nodejs/node/commit/e5b4af43fd)] - **src**: move the declaration of http parser versions into node_metadata.h (Joyee Cheung) [#25115](https://github.com/nodejs/node/pull/25115)
- [[`64c713a2e7`](https://github.com/nodejs/node/commit/64c713a2e7)] - **src**: move GetOpenSSLVersion into node_metadata.cc (Joyee Cheung) [#25115](https://github.com/nodejs/node/pull/25115)
- [[`b1500d9a7f`](https://github.com/nodejs/node/commit/b1500d9a7f)] - **src**: pass isMainThread into bootstrap/node.js directly (Joyee Cheung) [#25017](https://github.com/nodejs/node/pull/25017)
- [[`ee461feaee`](https://github.com/nodejs/node/commit/ee461feaee)] - **src**: always compile and store code cache for native modules (Joyee Cheung) [#24950](https://github.com/nodejs/node/pull/24950)
- [[`fd913fe365`](https://github.com/nodejs/node/commit/fd913fe365)] - **src**: remove code cache integrity check (Joyee Cheung) [#24950](https://github.com/nodejs/node/pull/24950)
- [[`d245c4cd50`](https://github.com/nodejs/node/commit/d245c4cd50)] - **src**: use NativeModuleLoader to compile all the bootstrappers (Joyee Cheung) [#24775](https://github.com/nodejs/node/pull/24775)
- [[`d1ff107b51`](https://github.com/nodejs/node/commit/d1ff107b51)] - **src**: initialize `Environment` members in class definition (Anna Henningsen) [#25369](https://github.com/nodejs/node/pull/25369)
- [[`5b933565ac`](https://github.com/nodejs/node/commit/5b933565ac)] - **src**: check curve ID existence instead of asn flags (Sam Roberts) [#25345](https://github.com/nodejs/node/pull/25345)
- [[`807e732832`](https://github.com/nodejs/node/commit/807e732832)] - **src**: trace_events: fix race with metadata events (Ali Ijaz Sheikh) [#25235](https://github.com/nodejs/node/pull/25235)
- [[`1e60e0afcb`](https://github.com/nodejs/node/commit/1e60e0afcb)] - **src**: remove unused method declaration (Ben Noordhuis) [#25329](https://github.com/nodejs/node/pull/25329)
- [[`f6e341a546`](https://github.com/nodejs/node/commit/f6e341a546)] - **src**: improve ToV8Value() functions (Anna Henningsen) [#25288](https://github.com/nodejs/node/pull/25288)
- [[`465d02b817`](https://github.com/nodejs/node/commit/465d02b817)] - **src**: add NAPI_VERSION_EXPERIMENTAL (Michael Dawson) [#25319](https://github.com/nodejs/node/pull/25319)
- [[`d7186252df`](https://github.com/nodejs/node/commit/d7186252df)] - **src**: unload addons when environment quits (Gabriel Schulhof) [#24861](https://github.com/nodejs/node/pull/24861)
- [[`f62e35fd05`](https://github.com/nodejs/node/commit/f62e35fd05)] - **src**: fix warning in cares_wrap.cc (cjihrig) [#25230](https://github.com/nodejs/node/pull/25230)
- [[`2f5c8b5041`](https://github.com/nodejs/node/commit/2f5c8b5041)] - **src**: remove unused variable from string_search.h (Anna Henningsen) [#25139](https://github.com/nodejs/node/pull/25139)
- [[`e00b326f33`](https://github.com/nodejs/node/commit/e00b326f33)] - **src**: pass along MaybeLocal\<\> state from `URL::ToObject()` (Anna Henningsen) [#25141](https://github.com/nodejs/node/pull/25141)
- [[`ae86192732`](https://github.com/nodejs/node/commit/ae86192732)] - **src**: ignore termination exceptions in fatal TryCatch (Anna Henningsen) [#25141](https://github.com/nodejs/node/pull/25141)
- [[`c9d49d65a4`](https://github.com/nodejs/node/commit/c9d49d65a4)] - **src**: fulfill Maybe contract in InlineDecoder (Anna Henningsen) [#25140](https://github.com/nodejs/node/pull/25140)
- [[`dd6667d05e`](https://github.com/nodejs/node/commit/dd6667d05e)] - **src**: lazily load internalBinding('uv') and build the errmap lazily (Joyee Cheung) [#25143](https://github.com/nodejs/node/pull/25143)
- [[`bc66356093`](https://github.com/nodejs/node/commit/bc66356093)] - **src**: use consistent names for JSStream (Sam Roberts) [#25153](https://github.com/nodejs/node/pull/25153)
- [[`99a5af65df`](https://github.com/nodejs/node/commit/99a5af65df)] - **src**: introduce DCHECK macro (cjihrig) [#25207](https://github.com/nodejs/node/pull/25207)
- [[`e2a01ca061`](https://github.com/nodejs/node/commit/e2a01ca061)] - **src**: use DCHECK\_\* macros where possible (cjihrig) [#25207](https://github.com/nodejs/node/pull/25207)
- [[`73ccfc81c9`](https://github.com/nodejs/node/commit/73ccfc81c9)] - **src**: fix compiler warnings in node_crypto.cc (cjihrig) [#25205](https://github.com/nodejs/node/pull/25205)
- [[`7365b00929`](https://github.com/nodejs/node/commit/7365b00929)] - **src**: do not leak NodeTraceStateObserver (Anna Henningsen) [#25180](https://github.com/nodejs/node/pull/25180)
- [[`37ba20112a`](https://github.com/nodejs/node/commit/37ba20112a)] - **src,lib**: prefer internal/options over process.\_foo (Anna Henningsen) [#25063](https://github.com/nodejs/node/pull/25063)
- [[`7480864c51`](https://github.com/nodejs/node/commit/7480864c51)] - **src,lib**: make process.binding('config') internal (Masashi Hirano) [#23400](https://github.com/nodejs/node/pull/23400)
- [[`577da835d2`](https://github.com/nodejs/node/commit/577da835d2)] - **_Revert_** "**src,lib**: make process.binding('config') internal" (Joyee Cheung) [#25446](https://github.com/nodejs/node/pull/25446)
- [[`d7bc03e2ca`](https://github.com/nodejs/node/commit/d7bc03e2ca)] - **test**: improve known_issues/test-vm-timeout-escape-queuemicrotask (Rich Trott) [#25503](https://github.com/nodejs/node/pull/25503)
- [[`3afb4813c8`](https://github.com/nodejs/node/commit/3afb4813c8)] - **test**: add test for fs.lchmod (ZYSzys) [#25439](https://github.com/nodejs/node/pull/25439)
- [[`067d38fb07`](https://github.com/nodejs/node/commit/067d38fb07)] - **test**: make test-v8-coverage.js more strict (cjihrig) [#25289](https://github.com/nodejs/node/pull/25289)
- [[`f6c14bd1e2`](https://github.com/nodejs/node/commit/f6c14bd1e2)] - **test**: rework ephemeralkeyinfo to run in parallel (Sam Roberts) [#25409](https://github.com/nodejs/node/pull/25409)
- [[`29b89badb5`](https://github.com/nodejs/node/commit/29b89badb5)] - **test**: check for tls renegotiation errors (Sam Roberts) [#25437](https://github.com/nodejs/node/pull/25437)
- [[`23d41fbf01`](https://github.com/nodejs/node/commit/23d41fbf01)] - **test**: fix test-net-connect-econnrefused (again) (Rich Trott) [#25438](https://github.com/nodejs/node/pull/25438)
- [[`d86a3e8245`](https://github.com/nodejs/node/commit/d86a3e8245)] - **test**: remove unnecessary skipIfWorker() (Rich Trott) [#25427](https://github.com/nodejs/node/pull/25427)
- [[`82fc9a8889`](https://github.com/nodejs/node/commit/82fc9a8889)] - **test**: fix module loading error for AIX 7.1 (Richard Lau) [#25418](https://github.com/nodejs/node/pull/25418)
- [[`3f661097d1`](https://github.com/nodejs/node/commit/3f661097d1)] - **test**: improve test coverage of native crypto code (Tobias Nießen) [#25400](https://github.com/nodejs/node/pull/25400)
- [[`fe9b6ee88b`](https://github.com/nodejs/node/commit/fe9b6ee88b)] - **test**: move require('https') to after crypto check (Daniel Bevenius) [#25388](https://github.com/nodejs/node/pull/25388)
- [[`b545b4c1e9`](https://github.com/nodejs/node/commit/b545b4c1e9)] - **test**: fix test-net-connect-econnrefused (Rich Trott) [#25389](https://github.com/nodejs/node/pull/25389)
- [[`0f290e8f62`](https://github.com/nodejs/node/commit/0f290e8f62)] - **test**: remove test/pummel/test-http-client-reconnect-bug.js (Rich Trott) [#25387](https://github.com/nodejs/node/pull/25387)
- [[`58de81faa7`](https://github.com/nodejs/node/commit/58de81faa7)] - **test**: remove duplicate encoding tests in favor of WPT (Joyee Cheung) [#25321](https://github.com/nodejs/node/pull/25321)
- [[`da34c6c575`](https://github.com/nodejs/node/commit/da34c6c575)] - **test**: use WPT runner to run encoding tests (Joyee Cheung) [#25321](https://github.com/nodejs/node/pull/25321)
- [[`8d8c30599a`](https://github.com/nodejs/node/commit/8d8c30599a)] - **test**: support more icu requirements in the WPT status file (Joyee Cheung) [#25321](https://github.com/nodejs/node/pull/25321)
- [[`d9adceecb6`](https://github.com/nodejs/node/commit/d9adceecb6)] - **test**: pull enconding WPT test fixtures (Joyee Cheung) [#25321](https://github.com/nodejs/node/pull/25321)
- [[`837ca76a0d`](https://github.com/nodejs/node/commit/837ca76a0d)] - **test**: refactor test-fs-watch-non-recursive (Rich Trott) [#25386](https://github.com/nodejs/node/pull/25386)
- [[`65dfeeb9a9`](https://github.com/nodejs/node/commit/65dfeeb9a9)] - **test**: fix test/pummel/test-fs-watch-non-recursive.js (Rich Trott) [#25386](https://github.com/nodejs/node/pull/25386)
- [[`bdcf8f4784`](https://github.com/nodejs/node/commit/bdcf8f4784)] - **test**: fix test/pummel/test-fs-watch-file.js (Rich Trott) [#25384](https://github.com/nodejs/node/pull/25384)
- [[`be16cc9fd6`](https://github.com/nodejs/node/commit/be16cc9fd6)] - **test**: set umask for tests (Rich Trott) [#25229](https://github.com/nodejs/node/pull/25229)
- [[`3bebcf0180`](https://github.com/nodejs/node/commit/3bebcf0180)] - **test**: fix failing assertion (Ruben Bridgewater) [#25250](https://github.com/nodejs/node/pull/25250)
- [[`201a8d9dc2`](https://github.com/nodejs/node/commit/201a8d9dc2)] - **test**: refactor `common.expectWarning()` (Ruben Bridgewater) [#25251](https://github.com/nodejs/node/pull/25251)
- [[`f0202a7604`](https://github.com/nodejs/node/commit/f0202a7604)] - **test**: fix test/pummel/test-fs-largefile.js (Rich Trott) [#25372](https://github.com/nodejs/node/pull/25372)
- [[`fc22df9552`](https://github.com/nodejs/node/commit/fc22df9552)] - **test**: more tests for internal/util/types (ZYSzys) [#25225](https://github.com/nodejs/node/pull/25225)
- [[`c826af781f`](https://github.com/nodejs/node/commit/c826af781f)] - **test**: clean up wasm fixtures (Gus Caplan) [#25360](https://github.com/nodejs/node/pull/25360)
- [[`c1aa5f0dae`](https://github.com/nodejs/node/commit/c1aa5f0dae)] - **test**: tune test-uv-threadpool-schedule (Rich Trott) [#25358](https://github.com/nodejs/node/pull/25358)
- [[`f80fbd2c16`](https://github.com/nodejs/node/commit/f80fbd2c16)] - **test**: remove redundant fchmod test (ZYSzys) [#25282](https://github.com/nodejs/node/pull/25282)
- [[`ce7bbd2ad9`](https://github.com/nodejs/node/commit/ce7bbd2ad9)] - **test**: move test-tls-securepair-client out of pummel (Rich Trott) [#25222](https://github.com/nodejs/node/pull/25222)
- [[`7ac1db2c31`](https://github.com/nodejs/node/commit/7ac1db2c31)] - **test**: fix test-tls-securepair-client (Rich Trott) [#25222](https://github.com/nodejs/node/pull/25222)
- [[`239d5ec92c`](https://github.com/nodejs/node/commit/239d5ec92c)] - **test**: http2 origin length ERR_HTTP2_ORIGIN_LENGTH (Furqan Shaikh) [#25296](https://github.com/nodejs/node/pull/25296)
- [[`456f76a48b`](https://github.com/nodejs/node/commit/456f76a48b)] - **test**: remove flag for test-addon-uv-handle-leak (Rich Trott) [#25327](https://github.com/nodejs/node/pull/25327)
- [[`523872b37f`](https://github.com/nodejs/node/commit/523872b37f)] - **test**: fix test-benchmark-zlib (Rich Trott) [#25365](https://github.com/nodejs/node/pull/25365)
- [[`379260e4bd`](https://github.com/nodejs/node/commit/379260e4bd)] - **test**: replace internals with public API (Rich Trott) [#25309](https://github.com/nodejs/node/pull/25309)
- [[`973b32d3c3`](https://github.com/nodejs/node/commit/973b32d3c3)] - **test**: set umask explicitly (Thomas Chung) [#25213](https://github.com/nodejs/node/pull/25213)
- [[`c10b131ec9`](https://github.com/nodejs/node/commit/c10b131ec9)] - **test**: make sure tmpdir is created before using it (Joyee Cheung) [#25224](https://github.com/nodejs/node/pull/25224)
- [[`5a5bc58b4f`](https://github.com/nodejs/node/commit/5a5bc58b4f)] - **test**: remove unused --expose-native-as V8 flag (peterwmwong) [#25275](https://github.com/nodejs/node/pull/25275)
- [[`61fc3bfd8e`](https://github.com/nodejs/node/commit/61fc3bfd8e)] - **test**: mark test-util-callbackify flaky on AIX (Rich Trott) [#25284](https://github.com/nodejs/node/pull/25284)
- [[`ee8a4a291d`](https://github.com/nodejs/node/commit/ee8a4a291d)] - **test**: remove unnecessary test flags (cjihrig) [#25277](https://github.com/nodejs/node/pull/25277)
- [[`4ca4b546ab`](https://github.com/nodejs/node/commit/4ca4b546ab)] - **test**: remove `util.inherits()` usage (ZYSzys) [#25245](https://github.com/nodejs/node/pull/25245)
- [[`11c9a82f0f`](https://github.com/nodejs/node/commit/11c9a82f0f)] - **test**: slightly refactor test-child-process-execsync (Denys Otrishko) [#25227](https://github.com/nodejs/node/pull/25227)
- [[`05d1a536cc`](https://github.com/nodejs/node/commit/05d1a536cc)] - **test**: remove try/catch in common.isMainThread (Rich Trott) [#25249](https://github.com/nodejs/node/pull/25249)
- [[`b0b1414ad7`](https://github.com/nodejs/node/commit/b0b1414ad7)] - **test**: regression test for uv threadpool congestion (Gireesh Punathil) [#23099](https://github.com/nodejs/node/pull/23099)
- [[`c7d2dbd5da`](https://github.com/nodejs/node/commit/c7d2dbd5da)] - **test**: add TODO to encoding tests that can be replaced with WPT (Joyee Cheung) [#25155](https://github.com/nodejs/node/pull/25155)
- [[`b45be671db`](https://github.com/nodejs/node/commit/b45be671db)] - **test**: rename custom encoding tests that cannot be replaced by WPT (Joyee Cheung) [#25155](https://github.com/nodejs/node/pull/25155)
- [[`be421823e5`](https://github.com/nodejs/node/commit/be421823e5)] - **test**: split encoding tests where some cases can be run without ICU (Joyee Cheung) [#25155](https://github.com/nodejs/node/pull/25155)
- [[`deceb26238`](https://github.com/nodejs/node/commit/deceb26238)] - **test**: split test-whatwg-encoding-textdecoder-fatal.js (Joyee Cheung) [#25155](https://github.com/nodejs/node/pull/25155)
- [[`a8f5191eb9`](https://github.com/nodejs/node/commit/a8f5191eb9)] - **test**: split test-whatwg-encoding-textdecoder.js (Joyee Cheung) [#25155](https://github.com/nodejs/node/pull/25155)
- [[`7e2ae75a6b`](https://github.com/nodejs/node/commit/7e2ae75a6b)] - **test**: mark two tests as flaky in AIX (Gireesh Punathil) [#25126](https://github.com/nodejs/node/pull/25126)
- [[`e182ca9bdc`](https://github.com/nodejs/node/commit/e182ca9bdc)] - **test**: add more inspect subclassing tests (Ruben Bridgewater) [#25192](https://github.com/nodejs/node/pull/25192)
- [[`58af085d9f`](https://github.com/nodejs/node/commit/58af085d9f)] - **test**: refactor stdio handling in test-esm-cjs-main (Richard Lau) [#25169](https://github.com/nodejs/node/pull/25169)
- [[`91d1aea311`](https://github.com/nodejs/node/commit/91d1aea311)] - **test**: refactor test-esm-namespace.mjs (Rich Trott) [#25117](https://github.com/nodejs/node/pull/25117)
- [[`b7b1d7eb88`](https://github.com/nodejs/node/commit/b7b1d7eb88)] - **test**: fix test-repl-envvars (Anna Henningsen) [#25226](https://github.com/nodejs/node/pull/25226)
- [[`95353c7c20`](https://github.com/nodejs/node/commit/95353c7c20)] - **test,doc**: add tests and docs for addon unloading (Anna Henningsen) [#24861](https://github.com/nodejs/node/pull/24861)
- [[`a29adef252`](https://github.com/nodejs/node/commit/a29adef252)] - **test,worker**: simplify common.isMainThread (Rich Trott) [#25426](https://github.com/nodejs/node/pull/25426)
- [[`a6df7278d8`](https://github.com/nodejs/node/commit/a6df7278d8)] - **test,worker**: refactor test-worker-cleanup-handles (Rich Trott) [#25401](https://github.com/nodejs/node/pull/25401)
- [[`453bd18969`](https://github.com/nodejs/node/commit/453bd18969)] - **tls**: do not confuse TLSSocket and Socket (Sam Roberts) [#25153](https://github.com/nodejs/node/pull/25153)
- [[`f6b2ea8bb9`](https://github.com/nodejs/node/commit/f6b2ea8bb9)] - **tls**: do not confuse session and session ID (Sam Roberts) [#25153](https://github.com/nodejs/node/pull/25153)
- [[`d5ba121e74`](https://github.com/nodejs/node/commit/d5ba121e74)] - **tls**: fix initRead socket argument name (Sam Roberts) [#25153](https://github.com/nodejs/node/pull/25153)
- [[`acf7802fe3`](https://github.com/nodejs/node/commit/acf7802fe3)] - **tls**: remove unused ocsp extension parsing (Sam Roberts) [#25153](https://github.com/nodejs/node/pull/25153)
- [[`f0409be2a7`](https://github.com/nodejs/node/commit/f0409be2a7)] - **tools**: lint for use of internalBinding() (cjihrig) [#25395](https://github.com/nodejs/node/pull/25395)
- [[`2a85cc7cae`](https://github.com/nodejs/node/commit/2a85cc7cae)] - **tools**: update crypo check rule (cjihrig) [#25399](https://github.com/nodejs/node/pull/25399)
- [[`dcbf1d9da4`](https://github.com/nodejs/node/commit/dcbf1d9da4)] - **tools**: add openssl-cli to macos-firewall.sh (Daniel Bevenius) [#25385](https://github.com/nodejs/node/pull/25385)
- [[`ee4c46c72f`](https://github.com/nodejs/node/commit/ee4c46c72f)] - **tools**: update ESLint to 5.12.0 (cjihrig) [#25347](https://github.com/nodejs/node/pull/25347)
- [[`1be566bd2f`](https://github.com/nodejs/node/commit/1be566bd2f)] - **tools**: replace NULL with nullptr (Juan José Arboleda) [#25179](https://github.com/nodejs/node/pull/25179)
- [[`fee8a11634`](https://github.com/nodejs/node/commit/fee8a11634)] - **tools**: remove custom buffer-constructor lint rule (cjihrig) [#25261](https://github.com/nodejs/node/pull/25261)
- [[`ee43540aa7`](https://github.com/nodejs/node/commit/ee43540aa7)] - **tools**: enable no-buffer-constructor lint rule (cjihrig) [#25261](https://github.com/nodejs/node/pull/25261)
- [[`e6b5232381`](https://github.com/nodejs/node/commit/e6b5232381)] - **tools**: enable no-useless-catch lint rule (cjihrig) [#25236](https://github.com/nodejs/node/pull/25236)
- [[`f944a75336`](https://github.com/nodejs/node/commit/f944a75336)] - **tools**: update ESLint to 5.11.1 (cjihrig) [#25236](https://github.com/nodejs/node/pull/25236)
- [[`19f1a506ee`](https://github.com/nodejs/node/commit/19f1a506ee)] - **trace_events**: move SetupTraceCategoryState into node_trace_events.cc (Joyee Cheung) [#25128](https://github.com/nodejs/node/pull/25128)
- [[`6e716ed1d6`](https://github.com/nodejs/node/commit/6e716ed1d6)] - **url**: return backslashes from fileURLToPath on win (Kevin Smith) [#25349](https://github.com/nodejs/node/pull/25349)
- [[`71432c3d06`](https://github.com/nodejs/node/commit/71432c3d06)] - **util**: fixes type in argument type validation error (Ankur Oberoi) [#25103](https://github.com/nodejs/node/pull/25103)
- [[`46ec26f8aa`](https://github.com/nodejs/node/commit/46ec26f8aa)] - **util**: remove eslint comments and rename variables (Ruben Bridgewater) [#25255](https://github.com/nodejs/node/pull/25255)
- [[`7ff44105be`](https://github.com/nodejs/node/commit/7ff44105be)] - **util**: remove outdated comment (Ruben Bridgewater) [#25255](https://github.com/nodejs/node/pull/25255)
- [[`45a8eb6ed3`](https://github.com/nodejs/node/commit/45a8eb6ed3)] - **util**: simpler module namespace code (Ruben Bridgewater) [#25255](https://github.com/nodejs/node/pull/25255)
- [[`a333272fb0`](https://github.com/nodejs/node/commit/a333272fb0)] - **util**: code cleanup (Ruben Bridgewater) [#25255](https://github.com/nodejs/node/pull/25255)
- [[`7696d1fe84`](https://github.com/nodejs/node/commit/7696d1fe84)] - **util**: switch recurseTimes counter (Ruben Bridgewater) [#25255](https://github.com/nodejs/node/pull/25255)
- [[`2e6e4cfaf5`](https://github.com/nodejs/node/commit/2e6e4cfaf5)] - **util**: add null prototype support for date (Anto Aravinth) [#25144](https://github.com/nodejs/node/pull/25144)
- [[`901d3d0959`](https://github.com/nodejs/node/commit/901d3d0959)] - **(SEMVER-MINOR)** **util**: inspect ArrayBuffers contents as well (Ruben Bridgewater) [#25006](https://github.com/nodejs/node/pull/25006)
- [[`4ca0968918`](https://github.com/nodejs/node/commit/4ca0968918)] - **util**: update comment in util.promisify (Kazushi Kitaya) [#25323](https://github.com/nodejs/node/pull/25323)
- [[`37976251b5`](https://github.com/nodejs/node/commit/37976251b5)] - **util**: fix util.inspect with proxied function (Weijia Wang) [#25244](https://github.com/nodejs/node/pull/25244)
- [[`88e73862ca`](https://github.com/nodejs/node/commit/88e73862ca)] - **util**: simplify code (Kazushi Kitaya) [#25162](https://github.com/nodejs/node/pull/25162)
- [[`73f3a1c4e6`](https://github.com/nodejs/node/commit/73f3a1c4e6)] - **util**: make inspect aware of RegExp subclasses and null prototype (Ruben Bridgewater) [#25192](https://github.com/nodejs/node/pull/25192)
- [[`7f78137c37`](https://github.com/nodejs/node/commit/7f78137c37)] - **v8**: enable inline WASM in serialization API (Anna Henningsen) [#25313](https://github.com/nodejs/node/pull/25313)
- [[`2df0d14e18`](https://github.com/nodejs/node/commit/2df0d14e18)] - **win, build**: fix building addons on Windows (Bartosz Sosnowski) [#25108](https://github.com/nodejs/node/pull/25108)
- [[`243f90283c`](https://github.com/nodejs/node/commit/243f90283c)] - **worker**: remove `--experimental-worker` flag (Anna Henningsen) [#25361](https://github.com/nodejs/node/pull/25361)
- [[`e8a6cc8802`](https://github.com/nodejs/node/commit/e8a6cc8802)] - **worker**: improve JS-side debugging (Anna Henningsen) [#25312](https://github.com/nodejs/node/pull/25312)
- [[`65c136f3de`](https://github.com/nodejs/node/commit/65c136f3de)] - **worker**: partially remove `--experimental-worker` flag (Anna Henningsen) [#25404](https://github.com/nodejs/node/pull/25404)
- [[`7bb7b9a61f`](https://github.com/nodejs/node/commit/7bb7b9a61f)] - **worker**: set `--experimental-worker` always (Anna Henningsen) [#25404](https://github.com/nodejs/node/pull/25404)
- [[`dd8795f4a0`](https://github.com/nodejs/node/commit/dd8795f4a0)] - **worker**: enable transferring WASM modules (Anna Henningsen) [#25314](https://github.com/nodejs/node/pull/25314)
- [[`2014eba782`](https://github.com/nodejs/node/commit/2014eba782)] - **worker**: use engine-provided deleter for `SharedArrayBuffer`s (Anna Henningsen) [#25307](https://github.com/nodejs/node/pull/25307)
- [[`7edf8c7e74`](https://github.com/nodejs/node/commit/7edf8c7e74)] - **(SEMVER-MINOR)** **zlib**: add brotli support (Anna Henningsen) [#24938](https://github.com/nodejs/node/pull/24938)
- [[`e534dcd75e`](https://github.com/nodejs/node/commit/e534dcd75e)] - **zlib**: split JS code as prep for non-zlib-backed streams (Anna Henningsen) [#24939](https://github.com/nodejs/node/pull/24939)

Windows 32-bit Installer: https://nodejs.org/dist/v11.7.0/node-v11.7.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v11.7.0/node-v11.7.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v11.7.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v11.7.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v11.7.0/node-v11.7.0.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v11.7.0/node-v11.7.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v11.7.0/node-v11.7.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v11.7.0/node-v11.7.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v11.7.0/node-v11.7.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v11.7.0/node-v11.7.0-aix-ppc64.tar.gz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v11.7.0/node-v11.7.0-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v11.7.0/node-v11.7.0-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v11.7.0/node-v11.7.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v11.7.0/node-v11.7.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v11.7.0/node-v11.7.0.tar.gz \
Other release files: https://nodejs.org/dist/v11.7.0/ \
Documentation: https://nodejs.org/docs/v11.7.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

081b4c5359dbf320fd167f2b8c075a91980a49a5d95f3d4ecdd075dfb6d92132  node-v11.7.0-aix-ppc64.tar.gz
873b47e6fc97aef2f73b0e8dde641967b2a84a3b63d1697b9d8813e14fb3b01c  node-v11.7.0-darwin-x64.tar.gz
0af0843eebeb574d11b94435a4162a8fc1797eea890153214770c6f0153d32ef  node-v11.7.0-darwin-x64.tar.xz
cd9d705ba27ba009b79652f1cbdef13ee99a916b72100ab749ea831c060b39a7  node-v11.7.0-headers.tar.gz
b1372341748de7d9e8e5c764d3d4ec88bd097121ff2ae2b50f46fc43253ac5aa  node-v11.7.0-headers.tar.xz
cbbc6f68d74433e1b8975606d2f83f3c3fff264466db1c14082d2e16a1951abf  node-v11.7.0-linux-arm64.tar.gz
82b9726c6b367a43530f868f61b1b2a05c942d3decdf9d2e18b4a9bdf353c223  node-v11.7.0-linux-arm64.tar.xz
87c956b17d4f529f703c0f1b519888f7986d8d5e81c1992e06c9f96dae27dc0b  node-v11.7.0-linux-armv6l.tar.gz
20ded7f501e7375e87d345cc4248e7d40eecc963b721b929a8d8676a1ebc8226  node-v11.7.0-linux-armv6l.tar.xz
6dadf13ef470ff90bef5bc595043da2077e33ce6bbf3ef24793b96d95109a6ae  node-v11.7.0-linux-armv7l.tar.gz
524e2538db9685d8a2973eeb09537d8b991bdc487346741ee82f9a21bd2690ec  node-v11.7.0-linux-armv7l.tar.xz
48de0f6b42226374cfc76d912f9d569d0b9f81a5646909f847892b2be78759ed  node-v11.7.0-linux-ppc64le.tar.gz
aee397fedf82ead2fb8a41e42567395cde874ec63baa6c7fe50f42fb2e8883f4  node-v11.7.0-linux-ppc64le.tar.xz
e42b01bce12ad893987e7d6775517f72f704dab3b7c49087d6e866e9eba886d8  node-v11.7.0-linux-s390x.tar.gz
f13565ab58e8cc308fcbc941af67a17e842dbe6f6bc6d6d1799bbca904edf997  node-v11.7.0-linux-s390x.tar.xz
d2ab60b497b59c415dc1d1c0ab3f64da3083a746fb15ec82d917ca7f4743028d  node-v11.7.0-linux-x64.tar.gz
b2a0a7dde17fe0da5f86ce5fea3da2861d652a5e702e2177ee62813ff86451c0  node-v11.7.0-linux-x64.tar.xz
d4e12059afeca85a6ff8035d3ffd073b010f650a8524836864c136d226246c81  node-v11.7.0-sunos-x64.tar.gz
7067103800d94c383a97ea7ca696005849d041d59344182bcffa2635fb6ef2ef  node-v11.7.0-sunos-x64.tar.xz
6008a40f944ac9daba492f2404aef82cfc4a878f36ee106d935c1a34f7bd86ea  node-v11.7.0-win-x64.7z
00834d16532f24583bf56d2baab03904b0220154cc712344be95e4d05c6234dd  node-v11.7.0-win-x64.zip
155ae76195bd04a4e38d1d3a95acaa700d4cc9bc295eae5e176c2a6de4a8b69e  node-v11.7.0-win-x86.7z
4d861d4311e515a352fca7f5a4ec8fc5d5069d75c03950bfec45952cb1c8f26d  node-v11.7.0-win-x86.zip
56644938be5d053123a4766755b7b06b5428117133b9e57bd945c6053b9c58f6  node-v11.7.0-x64.msi
901cf55bfecdd5048c3184c45087bbefc136be11a5cb224b280d65c95c0ccdbf  node-v11.7.0-x86.msi
a58f9331c8a944c0d3f70142ef2ea40853c058b5ef890187bd3bfa0ddb82b53b  node-v11.7.0.pkg
4d95471083735c275ed3fb4d61458940887edbaae663d0c96ea5395c087ab9dc  node-v11.7.0.tar.gz
07febdd8722f6e24ad0cb9360e183baa75a07cad390c14896e3d97027e0fada2  node-v11.7.0.tar.xz
d1060afc554bf9c858abab68a1ea5a7845e05c6123477659cc1bebb582ae5830  win-x64/node.exe
b1af21cbb79322a09d34af26bf964a5f560883fa57cabc1c987d5f861f6cf131  win-x64/node.lib
497a0983680a333e66c4a4583563f2a6807485a6e45875e1b1ab2d321f3dee8b  win-x64/node_pdb.7z
f7a7a52091c1ce9a7925e20cdc3c15696d3b00465b978e6824f3336834dbd721  win-x64/node_pdb.zip
e908e51873a98520e7e8d54f4afdb1ad0ff4466373ddb511f9d562b7cdbd5a9c  win-x86/node.exe
2eb31627eea9007d677fc04fdd69caf72daff96f41fa332a3e850f2ee9c088fd  win-x86/node.lib
fcc896a301b77d0862913effcaf0bd40c8f172c611a339a61d329d11da057bcc  win-x86/node_pdb.7z
1cce183862133641f8ee56569343e8ec8575ea08f1557f45c6f8edd9bf79893b  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEpIwr7mgOhBYyzU5E8HSWs+s8F2IFAlxB06cACgkQ8HSWs+s8
F2LqNQ/+PLHeygvuo7zCPmwhun+yCE2i/GiDYFOffiG22ZfDhPXc9qxfUlUrLlv7
+93Ro22hBPgveSaZuVzXvJiTqXPjEs3IN4apE1GqmWoz+1n0DzsE+5egXr/C/0TY
ntOmbKArnLacGrT5TqRhG31CQCmlL+Lh2A3FQUWlPDIpTGFXq+OfjMxW8ddmYoVM
2o2WAvfVZdHezv/kq14sKIDAsfFE1uw1pMx5J+l0icOPxDv082kd0mRjgL3vdcWa
kO11Zg1SFHXjHU3XuE+yuTXSDVV8xERDdA1u3ATKQEHCD8hxHpVpNTKk5ih6Jvyf
s6WhVG0ygapofYy6GzZ+NNdFVRtQwm0UqZQCWFjssF1HRZVv4vIBORDQWdkXvgQc
4PaQBemSV2V8ytRELxGcmDbNtoumaUzWmKbHAgZ11VD5TCGBBSTpzuMlNjn6MXyB
QCMSKvMX/Y+hBByaVZD+8Nh2ilJx9/IzI+XaL+nWdS1Qvrb2rMfI6q1EgpRY7J+k
aVyzfOt2qV5qFYXR3B8BZe4dxf2BmmUW/CJxT3O1I9EhAenbuU9S6uQ+bEERDnCZ
6tm/cduAKIlKEMqoTNzewvVcq2IN4BN8+ocZhXf2Um4pXmOHaBoN3MXGz9l2Bhp6
RHk8LuyRfYaicIwu6bWsneb6F33xSJz5WdewCHveujQaV+uj2GE=
=NJaJ
-----END PGP SIGNATURE-----

```
