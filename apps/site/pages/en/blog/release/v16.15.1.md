---
date: '2022-06-01T23:22:01.560Z'
category: release
title: Node v16.15.1 (LTS)
layout: blog-post
author: <PERSON> and <PERSON>
---

### Notable Changes

- **deps**:

  - upgrade npm to 8.11.0 (<<EMAIL>>) [#43210](https://github.com/nodejs/node/pull/43210)

- **docs**:
  - add release key for RafaelGS<PERSON> (<PERSON>) [#43131](https://github.com/nodejs/node/pull/43131)
  - add release key for <PERSON> (<PERSON>) [#42961](https://github.com/nodejs/node/pull/42961)

### Commits

- \[[`f7c4ce2255`](https://github.com/nodejs/node/commit/f7c4ce2255)] - **deps**: V8: cherry-pick 3ebf2052a1b2 (<PERSON>) [#43147](https://github.com/nodejs/node/pull/43147)
- \[[`447f9a0e2e`](https://github.com/nodejs/node/commit/447f9a0e2e)] - **deps**: upgrade npm to 8.11.0 (<<EMAIL>>) [#43210](https://github.com/nodejs/node/pull/43210)
- \[[`68572bdea2`](https://github.com/nodejs/node/commit/68572bdea2)] - **deps**: upgrade npm to 8.10.0 (<<EMAIL>>) [#43061](https://github.com/nodejs/node/pull/43061)
- \[[`3dfc632f12`](https://github.com/nodejs/node/commit/3dfc632f12)] - **deps**: upgrade npm to 8.9.0 (npm-robot) [#42968](https://github.com/nodejs/node/pull/42968)
- \[[`a746943fcb`](https://github.com/nodejs/node/commit/a746943fcb)] - **deps**: update archs files for OpenSSL-1.1.1o (RafaelGSS) [#42957](https://github.com/nodejs/node/pull/42957)
- \[[`6efc844922`](https://github.com/nodejs/node/commit/6efc844922)] - **deps**: upgrade openssl sources to OpenSSL_1_1_1o (RafaelGSS) [#42957](https://github.com/nodejs/node/pull/42957)
- \[[`3ceeb574c3`](https://github.com/nodejs/node/commit/3ceeb574c3)] - **deps**: upgrade npm to 8.8.0 (npm-robot) [#42886](https://github.com/nodejs/node/pull/42886)
- \[[`f8b2156e07`](https://github.com/nodejs/node/commit/f8b2156e07)] - **deps**: upgrade npm to 8.7.0 (<<EMAIL>>) [#42744](https://github.com/nodejs/node/pull/42744)
- \[[`898b4751e9`](https://github.com/nodejs/node/commit/898b4751e9)] - **deps**: upgrade npm to 8.6.0 (npm team) [#42550](https://github.com/nodejs/node/pull/42550)
- \[[`617f64a1cd`](https://github.com/nodejs/node/commit/617f64a1cd)] - **doc**: add release key for RafaelGSS (Rafael Gonzaga) [#43131](https://github.com/nodejs/node/pull/43131)
- \[[`e1b47e6e3d`](https://github.com/nodejs/node/commit/e1b47e6e3d)] - **doc**: add release key for Juan Arboleda (Juan José) [#42961](https://github.com/nodejs/node/pull/42961)
- \[[`4185f1f466`](https://github.com/nodejs/node/commit/4185f1f466)] - **src,inspector**: fix empty MaybeLocal crash (Darshan Sen) [#42409](https://github.com/nodejs/node/pull/42409)
- \[[`f11b3cfc6a`](https://github.com/nodejs/node/commit/f11b3cfc6a)] - **test**: delete test/pummel/test-repl-empty-maybelocal-crash.js (Darshan Sen) [#42720](https://github.com/nodejs/node/pull/42720)
- \[[`feac215e4e`](https://github.com/nodejs/node/commit/feac215e4e)] - **tools**: disable trap handler for Windows cross-compiler (Michaël Zasso) [#40488](https://github.com/nodejs/node/pull/40488)
- \[[`47cdddf59b`](https://github.com/nodejs/node/commit/47cdddf59b)] - **tools**: update V8 gypfiles for 9.6 (Michaël Zasso) [#40488](https://github.com/nodejs/node/pull/40488)

Windows 32-bit Installer: https://nodejs.org/dist/v16.15.1/node-v16.15.1-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v16.15.1/node-v16.15.1-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v16.15.1/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v16.15.1/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v16.15.1/node-v16.15.1.pkg \
macOS Apple Silicon 64-bit Binary: https://nodejs.org/dist/v16.15.1/node-v16.15.1-darwin-arm64.tar.gz \
macOS Intel 64-bit Binary: https://nodejs.org/dist/v16.15.1/node-v16.15.1-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v16.15.1/node-v16.15.1-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v16.15.1/node-v16.15.1-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v16.15.1/node-v16.15.1-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v16.15.1/node-v16.15.1-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v16.15.1/node-v16.15.1-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v16.15.1/node-v16.15.1-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v16.15.1/node-v16.15.1.tar.gz \
Other release files: https://nodejs.org/dist/v16.15.1/ \
Documentation: https://nodejs.org/docs/v16.15.1/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

31dec4d81fb154120055132bf21b0664c34ca6e1f66def9438bd19e2c5aff0ca  node-v16.15.1-aix-ppc64.tar.gz
7f492d01bed05c982fd55d6ba68711fe29bd7a1bb97b528909ac5aa9e3ab951d  node-v16.15.1-darwin-arm64.tar.gz
efd7d2f6eab59d05955c8c4cbdd4d5612a894f3c6d9283f34340a4b93ccf9375  node-v16.15.1-darwin-arm64.tar.xz
965f4c44d53be8c7fd718ecb8ca3889c49e9877e68382851e8bf3b9b26eb3b69  node-v16.15.1-darwin-x64.tar.gz
bdad616d4d388364a7734cf61d078912c468809ee54c2869edcd813c472ebe3a  node-v16.15.1-darwin-x64.tar.xz
535d0eddb5cde8445f198476aa240ccb7b59f1251f235408e84e940f84fb1c81  node-v16.15.1-headers.tar.gz
3553651e631ee52c717cc95cf045683eab807418cac7d55bb618d0a7f1cfe2e1  node-v16.15.1-headers.tar.xz
84db3f261a02c3d92558fb80a3b597b58175d713b8aa928f6b66e963340f1faf  node-v16.15.1-linux-arm64.tar.gz
ddd18ba09cfa937f75123a9d69256d44386ff1b9c0d12fa4b3f9ce0b0d121d9d  node-v16.15.1-linux-arm64.tar.xz
b868e7d757acca8a1df5394f2381365c006e491aac5e9979b4a933ca66698f8c  node-v16.15.1-linux-armv7l.tar.gz
adb74fbb539c48f3fa6aca86c72fdcaa97f19f8f8a4db34cef5b2f3177c2958b  node-v16.15.1-linux-armv7l.tar.xz
f0f088eac8423b4c27b41d73a21056c7152fe3c79004ebec28f27bf2a2abac14  node-v16.15.1-linux-ppc64le.tar.gz
bf30678aa2170ac6894ba1c58184c1301ed62fe22057426db9482a2df6cd7d8b  node-v16.15.1-linux-ppc64le.tar.xz
0c9fac94a37ff9f59bad86c86a8660309978a0106785b5e99440e97caef44fd4  node-v16.15.1-linux-s390x.tar.gz
7bfb51543d06f04b60654e5034df4f1f7ca1d1aa2170f778d7020f37ae447a37  node-v16.15.1-linux-s390x.tar.xz
f78a49c0c9c2f546c3a44eb434c49a852125441422a1bcfc433dedc58d6a241c  node-v16.15.1-linux-x64.tar.gz
b749f7a76e56dfd6dfb8a910b8a2a871159661557680aa95acf13c9514422c12  node-v16.15.1-linux-x64.tar.xz
359b618d6b8b3e3e2af1b1a6da9be464114616cac23f709d81a223d72eaf13dd  node-v16.15.1.pkg
308aee7149c4092a53c87c28ef49e23a8d1606119e79ae68333062e2a1f94208  node-v16.15.1.tar.gz
d4e99d3c1f69711109a67525571058e6009cddbc228e7d723b8fb4a454169b7d  node-v16.15.1.tar.xz
378a9202374ac65377e13966dc80d435538db5a5cb128d629fa69f638c157a83  node-v16.15.1-win-x64.7z
e160591f562b575eb18b9b234b738f21cb7fbec5a73df1bd8062f8d0c6cef431  node-v16.15.1-win-x64.zip
d03c7d1dbe397a6095d0119c2a48105949c12e212fb7cda5a8df986a1af69a9b  node-v16.15.1-win-x86.7z
45eb18d8e4ba9b040dc43445ddaf16e51bc9af52aac863a4f734ecc0dfafbdc4  node-v16.15.1-win-x86.zip
ad2733ed364afbd9758690f1ca8f142b3cfa186c754af8b9f6fb6befae9941ff  node-v16.15.1-x64.msi
729a3211d43966d196ed5359e38164a6962c2c4af9d1d7aeebe6842d1da74b33  node-v16.15.1-x86.msi
13751aebac5bd85d73a7e18c37b1c6d33e33e65b924552b65ecbb182db463c67  win-x64/node.exe
a676e936f019206ecfb19a878802c085841ac34f8f83080d4261e5ca138b011c  win-x64/node.lib
cce83e7d5897bb385e58e028b07e17c4b303f2ef0f5d3ba74a77ac76e064b7c8  win-x64/node_pdb.7z
0362eb23d8459c97623db44df7c1a1834e6cf9be2e2b650df3b25ee91941910d  win-x64/node_pdb.zip
9bc93f97eccf3ac6b94c4a7e170e5b2c62cbad8abf4af65566c989d151008f9e  win-x86/node.exe
8574cc9643d304df424d038f37b2a78cc3c2a7dd58c2f1b9ff5d9a07da73d80d  win-x86/node.lib
1f5b95582d6a091b145212263be8ba7da6c1677607f65aa28df4b844a1329050  win-x86/node_pdb.7z
4a99ae9b0d27d9402dc331fdcf8d22677af7a10eb309ac05ff2d6d1b24867334  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQEzBAEBCAAdFiEETtd49TnjY0x3nIfG1wYoSKGrAFwFAmKX8+YACgkQ1wYoSKGr
AFxzyQf/V8V9tIWoFTy5wAEh4rmpER8E/clsNsr/Xnvy8SacE8NQoG9L9xA0f5qV
n20UwMlPMJBoE6e+ZK2wmQ3KG/XxX9tuD0zCpKb5nzzveOadIIzghb+QjDbZEqvg
8ctiVh4kOslXbQBmX3ARPaId3DS4+LzeLFiZYFEESlle2r7Y/Il+NJ40A+IIloCm
MKUCTrtIcdIhIJdDqu2gTqH/c/JQ3JQGj6M65SbZZuH3iq9AEGMKSQRBfon8Cgqi
6nZ8KZqjkPTSzdkk0XnWM8VmqzvWWzn0YFxw8wW/vA+vCzATLDRxLwWc7U23rztR
vwwxLkn5zVNM+/LuD7JG29X9HA4Rlw==
=F0P1
-----END PGP SIGNATURE-----

```
