---
date: '2015-10-13T17:42:03.184Z'
category: release
title: Node v4.2.1 (LTS)
layout: blog-post
author: The Node.js Project
---

<!--lint disable prohibited-strings-->
<!--lint disable maximum-line-length-->
<!--lint disable no-literal-urls-->
<!--lint disable no-shortcut-reference-link-->

### Notable changes

- Includes fixes for two regressions
  - Assertion error in WeakCallback - see [#3329](https://github.com/nodejs/node/pull/3329)
  - Undefined timeout regression - see [#3331](https://github.com/nodejs/node/pull/3331)

### Known issues

- When a server queues a large amount of data to send to a client over a pipelined HTTP connection, the underlying socket may be destroyed. See [#3332](https://github.com/nodejs/node/issues/3332) and [#3342](https://github.com/nodejs/node/pull/3342).
- Some problems with unreferenced timers running during `beforeExit` are still to be resolved. See [#1264](https://github.com/nodejs/node/issues/1264).
- Surrogate pair in REPL can freeze terminal. [#690](https://github.com/nodejs/node/issues/690)
- Calling `dns.setServers()` while a DNS query is in progress can cause the process to crash on a failed assertion. [#894](https://github.com/nodejs/node/issues/894)
- `url.resolve` may transfer the auth portion of the url when resolving between two full hosts, see [#1435](https://github.com/nodejs/node/issues/1435).

### Commits

- [[`b3cbd13340`](https://github.com/nodejs/node/commit/b3cbd13340)] - **buffer**: fix assertion error in WeakCallback (Fedor Indutny) [#3329](https://github.com/nodejs/node/pull/3329)
- [[`102cb7288c`](https://github.com/nodejs/node/commit/102cb7288c)] - **doc**: label v4.2.0 as LTS in changelog heading (Rod Vagg) [#3343](https://github.com/nodejs/node/pull/3343)
- [[`c245a199a7`](https://github.com/nodejs/node/commit/c245a199a7)] - **lib**: fix undefined timeout regression (Ryan Graham) [#3331](https://github.com/nodejs/node/pull/3331

Windows 32-bit Installer: https://nodejs.org/dist/v4.2.1/node-v4.2.1-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v4.2.1/node-v4.2.1-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v4.2.1/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v4.2.1/win-x64/node.exe \
Mac OS X 64-bit Installer: https://nodejs.org/dist/v4.2.1/node-v4.2.1.pkg \
Mac OS X 64-bit Binary: https://nodejs.org/dist/v4.2.1/node-v4.2.1-darwin-x64.tar.gz \
Linux 32-bit Binary: https://nodejs.org/dist/v4.2.1/node-v4.2.1-linux-x86.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v4.2.1/node-v4.2.1-linux-x64.tar.gz \
SmartOS 32-bit Binary: https://nodejs.org/dist/v4.2.1/node-v4.2.1-sunos-x86.tar.gz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v4.2.1/node-v4.2.1-sunos-x64.tar.gz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v4.2.1/node-v4.2.1-linux-armv6l.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v4.2.1/node-v4.2.1-linux-armv7l.tar.gz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v4.2.1/node-v4.2.1-linux-arm64.tar.gz \
Source Code: https://nodejs.org/dist/v4.2.1/node-v4.2.1.tar.gz \
Other release files: https://nodejs.org/dist/v4.2.1/ \
Documentation: https://nodejs.org/docs/v4.2.1/api/

Shasums (GPG signing hash: SHA512, file hash: SHA256):

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA512

658686795fac9669d411ac5c5be2da8159058e386767322d8d8151dcdb4810b9  node-v4.2.1-darwin-x64.tar.gz
d2c1ffb5c9f1d24f1f49652600bb2056b13afe068ba7d80bfd1009423ead8941  node-v4.2.1-darwin-x64.tar.xz
b242fd0fa83748d95cc3e68d14579b629960b0436d65da9d86907eef27b96fc1  node-v4.2.1-headers.tar.gz
b1416eca3f1c56e049d924d31b343d57a735d4e422c4288bf4c2ca453cb6bcf2  node-v4.2.1-headers.tar.xz
05df4aeb8a53798f8b10074600518040fc317f2919f9755aeab57b0aaf7227b0  node-v4.2.1-linux-arm64.tar.gz
ba5c0705f0d69306d978faac14ed37725b71b4e2c41192019e8e4aae239b2683  node-v4.2.1-linux-arm64.tar.xz
6f29286464efda4a362d9464d82733398b98882051c20c0f190004480b6e506a  node-v4.2.1-linux-armv6l.tar.gz
5a99151840df5dc2f9ec4562f6174ad06d0d52949256610e89ee8272e643cf95  node-v4.2.1-linux-armv6l.tar.xz
fb4fbef9306962e800804ab5ba615c06bba28deb4e7a3e945a291dba986ef816  node-v4.2.1-linux-armv7l.tar.gz
7699ee39c9bc92208ef38ab6d61ddee46cad2978b06095b2ca54a7a116c47d9a  node-v4.2.1-linux-armv7l.tar.xz
e766e387934e17daaad92d0460ed76f756655da62b627a5c9cc07faea4a0b824  node-v4.2.1-linux-x64.tar.gz
346f6c1b96bd5fc0a2a100e78deceff9e1045f5ccf0ba66401cd8d37d78d1b23  node-v4.2.1-linux-x64.tar.xz
97b5ccea7044073c87a21bcc4b0762f4a6bd77db9cc958206f684ecdfeb89b1f  node-v4.2.1-linux-x86.tar.gz
2a7a16e6066ddd724400634ba80266d6994036d0772545bcedc81c80d2e84e2b  node-v4.2.1-linux-x86.tar.xz
688df0a12461f378b296f69e8ae2c1cbb974b0216278624815bce7dcfea080c2  node-v4.2.1.pkg
acec44790ff0069620c0fd03945d14b9f97c7ccb0a0450f766a5cbe4a906510b  node-v4.2.1-sunos-x64.tar.gz
72d1cb21347af731f594f85fa177140b2c3e015b8c1cb65a63a082999cd390e8  node-v4.2.1-sunos-x64.tar.xz
d7bf8dcf353115b5e55bf64d25c2c34fa2d237e201e46dab97ae3d8e3f051583  node-v4.2.1-sunos-x86.tar.gz
fb047d9b3db08fba2c3021d574a3ffd6f0f3e0208735035ed245926f0977ab8a  node-v4.2.1-sunos-x86.tar.xz
8861b9f4c3b4db380fcda19a710c0430c3d62d03ee176c64db63eef95a672663  node-v4.2.1.tar.gz
0528c60cf75371314ad0e7b19f9fd586d98a220d307d03e163b323b67458b7be  node-v4.2.1.tar.xz
e460a71ea9aa4d743387a20319042de203de837cb613be0737b6ca368480302d  node-v4.2.1-x64.msi
df70335dc34d48cf03c9074d84ff330f4d1fa0eb12b5225da91ad4f7c1835318  node-v4.2.1-x86.msi
92744bfa525cd51ea3bf5ac810a9e3e9306f6fe4ac44847a8aefdd674701e8e6  win-x64/node.exe
d5369e749dc6181226f8a91c6c7d590596175943301fd92455dbea5de7c6b6f6  win-x64/node.lib
c22fcbf2f1db7ff3cd71b4c771f277fe8d75caff8689a3be50aeee1a7682820e  win-x86/node.exe
98f0b03cae561cb59fd602c51a35b326e561fe9bc8ad6cc9d5ec32aa2fe2acbd  win-x86/node.lib
-----BEGIN PGP SIGNATURE-----
Comment: GPGTools - https://gpgtools.org

iQEcBAEBCgAGBQJWHmkUAAoJEHNBsVwHCHesq4oH/1ghNOtL/4uVfHPeQtmAR/lW
+IHOHno346EpZDR0afOCTDwB+GyY5gbDdmb1CEwmFN5IstRPM4Jow6W2JwDNwOYD
QAIzzx/+sABFJZk3iuppIUbSA6ziP/L0B8dGxel96Iq02Zf9pv3NY5sxI744SMMF
Mr5l7/FIyb4dd9H3QiX3mxvbVj8q7Oj5e2ROlqn5mKm5HszFVhDwu1PjR5/mjazC
EB2rRJuIwzUO3VEe6ApmQCO26yLZA/CPwl41z9oQvF+j4lHHb6DkDDX7MQu3EWd7
hqsK2xQ6oOAzcVOP5edwYgGVH4N8hlHaHTVfFezgcri83ruMxqmRA4cYdLb3yOk=
=dRAV
-----END PGP SIGNATURE-----

```
