---
date: '2021-12-01T16:14:42.578Z'
category: release
title: Node v16.13.1 (LTS)
layout: blog-post
author: <PERSON>
---

### Notable Changes

- \[[`c14eb2325d`](https://github.com/nodejs/node/commit/c14eb2325d)] - **deps**: upgrade npm to 8.1.2 (npm team) [#40643](https://github.com/nodejs/node/pull/40643)
- \[[`a901b6c53c`](https://github.com/nodejs/node/commit/a901b6c53c)] - **deps**: update c-ares to 1.18.1 (<PERSON>) [#40660](https://github.com/nodejs/node/pull/40660)
  - This release contains a c-ares update to fix a regression introduced in Node.js v16.6.2 resolving CNAME records containing underscores ([#39780](https://github.com/nodejs/node/issues/39780)).
- \[[`755c08573f`](https://github.com/nodejs/node/commit/755c08573f)] - **doc**: add VoltrexMaster to collaborators (voltrexmaster) [#40566](https://github.com/nodejs/node/pull/40566)
- \[[`881dd7ba2a`](https://github.com/nodejs/node/commit/881dd7ba2a)] - **lib**: fix regular expression to detect \`/\` and \`\\\` (Francesco Trotta) [#40325](https://github.com/nodejs/node/pull/40325)

### Commits

- \[[`996bc6e840`](https://github.com/nodejs/node/commit/996bc6e840)] - **benchmark**: increase crypto DSA keygen params (Brian White) [#40416](https://github.com/nodejs/node/pull/40416)
- \[[`27009092c8`](https://github.com/nodejs/node/commit/27009092c8)] - **build**: skip long-running Actions for README-only modifications (Rich Trott) [#40571](https://github.com/nodejs/node/pull/40571)
- \[[`4581997ed0`](https://github.com/nodejs/node/commit/4581997ed0)] - **build**: disable v8 pointer compression on 32bit archs (Cheng Zhao) [#40418](https://github.com/nodejs/node/pull/40418)
- \[[`17433060d4`](https://github.com/nodejs/node/commit/17433060d4)] - **build**: fix actions pull request's branch (Mestery) [#40494](https://github.com/nodejs/node/pull/40494)
- \[[`bfdd32fa62`](https://github.com/nodejs/node/commit/bfdd32fa62)] - **build**: avoid run find inactive authors on forked repo (Jiawen Geng) [#40465](https://github.com/nodejs/node/pull/40465)
- \[[`134e8afc59`](https://github.com/nodejs/node/commit/134e8afc59)] - **build**: update codeowners-validator to 0.6 (FrankQiu) [#40307](https://github.com/nodejs/node/pull/40307)
- \[[`de125a556c`](https://github.com/nodejs/node/commit/de125a556c)] - **crypto**: avoid double free (Michael Dawson) [#40380](https://github.com/nodejs/node/pull/40380)
- \[[`c14eb2325d`](https://github.com/nodejs/node/commit/c14eb2325d)] - **deps**: upgrade npm to 8.1.2 (npm team) [#40643](https://github.com/nodejs/node/pull/40643)
- \[[`a901b6c53c`](https://github.com/nodejs/node/commit/a901b6c53c)] - **deps**: update c-ares to 1.18.1 (Richard Lau) [#40660](https://github.com/nodejs/node/pull/40660)
- \[[`76e2c3769e`](https://github.com/nodejs/node/commit/76e2c3769e)] - **deps**: upgrade npm to 8.1.1 (npm team) [#40554](https://github.com/nodejs/node/pull/40554)
- \[[`91c3cf5d0a`](https://github.com/nodejs/node/commit/91c3cf5d0a)] - **deps**: V8: cherry-pick 422dc378a1da (Ray Wang) [#40450](https://github.com/nodejs/node/pull/40450)
- \[[`769336ab8c`](https://github.com/nodejs/node/commit/769336ab8c)] - **deps**: add riscv64 config into openssl gypi (Lu Yahan) [#40473](https://github.com/nodejs/node/pull/40473)
- \[[`76d1b5d868`](https://github.com/nodejs/node/commit/76d1b5d868)] - **deps**: patch V8 to 9.4.146.24 (Michaël Zasso) [#40616](https://github.com/nodejs/node/pull/40616)
- \[[`23d11a1dd9`](https://github.com/nodejs/node/commit/23d11a1dd9)] - **dgram**: fix send with out of bounds offset + length (Nitzan Uziely) [#40568](https://github.com/nodejs/node/pull/40568)
- \[[`45bdc77dc0`](https://github.com/nodejs/node/commit/45bdc77dc0)] - **doc**: update cjs-module-lexer repo link (Guy Bedford) [#40707](https://github.com/nodejs/node/pull/40707)
- \[[`de5c5c8509`](https://github.com/nodejs/node/commit/de5c5c8509)] - **doc**: remove `--experimental-modules` documentation (FrankQiu) [#38974](https://github.com/nodejs/node/pull/38974)
- \[[`befac5ddd9`](https://github.com/nodejs/node/commit/befac5ddd9)] - **doc**: update tracking issues of startup performance (Joyee Cheung) [#40629](https://github.com/nodejs/node/pull/40629)
- \[[`3cb74d72f8`](https://github.com/nodejs/node/commit/3cb74d72f8)] - **doc**: fix markdown syntax and HTML tag misses (ryan) [#40608](https://github.com/nodejs/node/pull/40608)
- \[[`eea061f8f1`](https://github.com/nodejs/node/commit/eea061f8f1)] - **doc**: use 'GitHub Actions workflow' instead (Mestery) [#40586](https://github.com/nodejs/node/pull/40586)
- \[[`7a6e833677`](https://github.com/nodejs/node/commit/7a6e833677)] - **doc**: add node: url scheme (Daniel Nalborczyk) [#40573](https://github.com/nodejs/node/pull/40573)
- \[[`d72fb7df4a`](https://github.com/nodejs/node/commit/d72fb7df4a)] - **doc**: call cwd function (Daniel Nalborczyk) [#40573](https://github.com/nodejs/node/pull/40573)
- \[[`d732ff4614`](https://github.com/nodejs/node/commit/d732ff4614)] - **doc**: remove unused imports (Daniel Nalborczyk) [#40573](https://github.com/nodejs/node/pull/40573)
- \[[`e2114e21f4`](https://github.com/nodejs/node/commit/e2114e21f4)] - **doc**: add info on project's usage of coverity (Michael Dawson) [#40506](https://github.com/nodejs/node/pull/40506)
- \[[`d38077babe`](https://github.com/nodejs/node/commit/d38077babe)] - **doc**: fix typo in changelogs (Luigi Pinca) [#40585](https://github.com/nodejs/node/pull/40585)
- \[[`7c7f8791c6`](https://github.com/nodejs/node/commit/7c7f8791c6)] - **doc**: update onboarding task (Rich Trott) [#40570](https://github.com/nodejs/node/pull/40570)
- \[[`0a7c4ff248`](https://github.com/nodejs/node/commit/0a7c4ff248)] - **doc**: simplify ccache instructions (Rich Trott) [#40550](https://github.com/nodejs/node/pull/40550)
- \[[`5593dd1b25`](https://github.com/nodejs/node/commit/5593dd1b25)] - **doc**: fix macOS environment variables for ccache (Rich Trott) [#40550](https://github.com/nodejs/node/pull/40550)
- \[[`2d4a042675`](https://github.com/nodejs/node/commit/2d4a042675)] - **doc**: improve async_context introduction (Michaël Zasso) [#40560](https://github.com/nodejs/node/pull/40560)
- \[[`9fcfef09ac`](https://github.com/nodejs/node/commit/9fcfef09ac)] - **doc**: use GFM footnotes in webcrypto.md (Rich Trott) [#40477](https://github.com/nodejs/node/pull/40477)
- \[[`579f01c0a3`](https://github.com/nodejs/node/commit/579f01c0a3)] - **doc**: describe buffer limit of v8.serialize (Ray Wang) [#40243](https://github.com/nodejs/node/pull/40243)
- \[[`3b6cf090a0`](https://github.com/nodejs/node/commit/3b6cf090a0)] - **doc**: use GFM footnotes in maintaining-V8.md (#40476) (Rich Trott) [#40476](https://github.com/nodejs/node/pull/40476)
- \[[`dea701004e`](https://github.com/nodejs/node/commit/dea701004e)] - **doc**: fix `fs.symlink` code example (Juan José Arboleda) [#40414](https://github.com/nodejs/node/pull/40414)
- \[[`595117ff0b`](https://github.com/nodejs/node/commit/595117ff0b)] - **doc**: explain backport labels (Stephen Belanger) [#40520](https://github.com/nodejs/node/pull/40520)
- \[[`042f01e3ed`](https://github.com/nodejs/node/commit/042f01e3ed)] - **doc**: fix entry for Slack channel in onboarding.md (Rich Trott) [#40563](https://github.com/nodejs/node/pull/40563)
- \[[`755c08573f`](https://github.com/nodejs/node/commit/755c08573f)] - **doc**: add VoltrexMaster to collaborators (voltrexmaster) [#40566](https://github.com/nodejs/node/pull/40566)
- \[[`c029d0b61f`](https://github.com/nodejs/node/commit/c029d0b61f)] - **doc**: document considerations for inclusion in core (Rich Trott) [#40338](https://github.com/nodejs/node/pull/40338)
- \[[`836fc274e4`](https://github.com/nodejs/node/commit/836fc274e4)] - _**Revert**_ "**doc**: fix typo in stream docs" (Luigi Pinca) [#40819](https://github.com/nodejs/node/pull/40819)
- \[[`b3a12767a4`](https://github.com/nodejs/node/commit/b3a12767a4)] - **doc**: update link in onboarding doc (Rich Trott) [#40539](https://github.com/nodejs/node/pull/40539)
- \[[`aa47c9f38f`](https://github.com/nodejs/node/commit/aa47c9f38f)] - **doc**: clarify behavior of napi_extended_error_info (Michael Dawson) [#40458](https://github.com/nodejs/node/pull/40458)
- \[[`bf88328bdc`](https://github.com/nodejs/node/commit/bf88328bdc)] - **doc**: add updating expected assets to release guide (Richard Lau) [#40470](https://github.com/nodejs/node/pull/40470)
- \[[`621266afc7`](https://github.com/nodejs/node/commit/621266afc7)] - **doc**: format doc/api/\*.md with markdown formatter (Rich Trott) [#40403](https://github.com/nodejs/node/pull/40403)
- \[[`7b746381ce`](https://github.com/nodejs/node/commit/7b746381ce)] - **doc**: specify that maxFreeSockets is per host (Luigi Pinca) [#40483](https://github.com/nodejs/node/pull/40483)
- \[[`934dcc85c3`](https://github.com/nodejs/node/commit/934dcc85c3)] - **doc**: update Collaborator guide to reflect GitHub web UI update (Antoine du Hamel) [#40456](https://github.com/nodejs/node/pull/40456)
- \[[`4724e07476`](https://github.com/nodejs/node/commit/4724e07476)] - **doc**: indicate n-api out params that may be NULL (Isaac Brodsky) [#40371](https://github.com/nodejs/node/pull/40371)
- \[[`3b1499c971`](https://github.com/nodejs/node/commit/3b1499c971)] - **doc**: update CHANGELOG.md for Node.js 16.13.0 (Richard Lau) [#40617](https://github.com/nodejs/node/pull/40617)
- \[[`881dd7ba2a`](https://github.com/nodejs/node/commit/881dd7ba2a)] - **lib**: fix regular expression to detect \`/\` and \`\\\` (Francesco Trotta) [#40325](https://github.com/nodejs/node/pull/40325)
- \[[`0a8c33123e`](https://github.com/nodejs/node/commit/0a8c33123e)] - **lib,url**: correct URL's argument to pass idlharness (Khaidi Chu) [#39848](https://github.com/nodejs/node/pull/39848)
- \[[`480f0e1d20`](https://github.com/nodejs/node/commit/480f0e1d20)] - **meta**: use form schema for flaky test template (Michaël Zasso) [#40737](https://github.com/nodejs/node/pull/40737)
- \[[`55ff97342d`](https://github.com/nodejs/node/commit/55ff97342d)] - **meta**: update AUTHORS (Node.js GitHub Bot) [#40668](https://github.com/nodejs/node/pull/40668)
- \[[`ef46cb428d`](https://github.com/nodejs/node/commit/ef46cb428d)] - **meta**: consolidate AUTHORS entries for brettkiefer (Rich Trott) [#40599](https://github.com/nodejs/node/pull/40599)
- \[[`7230b6d33d`](https://github.com/nodejs/node/commit/7230b6d33d)] - **meta**: consolidate AUTHORS entries for alexzherdev (Rich Trott) [#40620](https://github.com/nodejs/node/pull/40620)
- \[[`9e12ed4f68`](https://github.com/nodejs/node/commit/9e12ed4f68)] - **meta**: consolidate AUTHORS entries for Azard (Rich Trott) [#40619](https://github.com/nodejs/node/pull/40619)
- \[[`97aa8e42b8`](https://github.com/nodejs/node/commit/97aa8e42b8)] - **meta**: move Fishrock123 to emeritus (Jeremiah Senkpiel) [#40596](https://github.com/nodejs/node/pull/40596)
- \[[`7b1c89f357`](https://github.com/nodejs/node/commit/7b1c89f357)] - **meta**: consolidate AUTHORS entries for clakech (Rich Trott) [#40589](https://github.com/nodejs/node/pull/40589)
- \[[`0003cb6b3b`](https://github.com/nodejs/node/commit/0003cb6b3b)] - **meta**: consolidate AUTHORS entries for darai0512 (Rich Trott) [#40569](https://github.com/nodejs/node/pull/40569)
- \[[`7590bacec1`](https://github.com/nodejs/node/commit/7590bacec1)] - **meta**: update AUTHORS (Node.js GitHub Bot) [#40580](https://github.com/nodejs/node/pull/40580)
- \[[`a5475df083`](https://github.com/nodejs/node/commit/a5475df083)] - **meta**: consolidate AUTHORS entries for dfabulich (Rich Trott) [#40527](https://github.com/nodejs/node/pull/40527)
- \[[`c021a7f169`](https://github.com/nodejs/node/commit/c021a7f169)] - **meta**: move one or more collaborators to emeritus (Node.js GitHub Bot) [#40464](https://github.com/nodejs/node/pull/40464)
- \[[`d64cf1706c`](https://github.com/nodejs/node/commit/d64cf1706c)] - **meta**: add Richard Lau to TSC list in README.md (Rich Trott) [#40523](https://github.com/nodejs/node/pull/40523)
- \[[`d09b8239bf`](https://github.com/nodejs/node/commit/d09b8239bf)] - **meta**: consolidate AUTHORS entries for dguo (Rich Trott) [#40517](https://github.com/nodejs/node/pull/40517)
- \[[`66192060e7`](https://github.com/nodejs/node/commit/66192060e7)] - **meta**: consolidate AUTHORS entries for cxreg (Rich Trott) [#40490](https://github.com/nodejs/node/pull/40490)
- \[[`b4f51276cb`](https://github.com/nodejs/node/commit/b4f51276cb)] - **meta**: update AUTHORS (Node.js GitHub Bot) [#40485](https://github.com/nodejs/node/pull/40485)
- \[[`2a2b549a28`](https://github.com/nodejs/node/commit/2a2b549a28)] - **meta**: consolidate AUTHORS entries for emanuelbuholzer (Rich Trott) [#40469](https://github.com/nodejs/node/pull/40469)
- \[[`618bbbf2f4`](https://github.com/nodejs/node/commit/618bbbf2f4)] - **meta**: consolidate AUTHORS entries for ebickle (Rich Trott) [#40447](https://github.com/nodejs/node/pull/40447)
- \[[`06706e8dd2`](https://github.com/nodejs/node/commit/06706e8dd2)] - **meta**: add `typings` to label-pr-config (Mestery) [#40401](https://github.com/nodejs/node/pull/40401)
- \[[`e2c9e1ccdd`](https://github.com/nodejs/node/commit/e2c9e1ccdd)] - **meta**: consolidate AUTHORS entries for evantorrie (Rich Trott) [#40430](https://github.com/nodejs/node/pull/40430)
- \[[`dab574e937`](https://github.com/nodejs/node/commit/dab574e937)] - **policy**: fix message for invalid manifest specifier (Rich Trott) [#40574](https://github.com/nodejs/node/pull/40574)
- \[[`58de6cebb6`](https://github.com/nodejs/node/commit/58de6cebb6)] - **process**: refactor execution (Voltrex) [#40664](https://github.com/nodejs/node/pull/40664)
- \[[`bc0eb0a3ea`](https://github.com/nodejs/node/commit/bc0eb0a3ea)] - **src**: make LoadEnvironment with string work with builtin modules path (Michaël Zasso) [#40607](https://github.com/nodejs/node/pull/40607)
- \[[`2c8a6ec28e`](https://github.com/nodejs/node/commit/2c8a6ec28e)] - **src**: remove usage of `AllocatedBuffer` from `node_http2` (Darshan Sen) [#40584](https://github.com/nodejs/node/pull/40584)
- \[[`59c26a2b2c`](https://github.com/nodejs/node/commit/59c26a2b2c)] - **src**: fix #endif description in crypto_keygen.h (Tobias Nießen) [#40639](https://github.com/nodejs/node/pull/40639)
- \[[`789fef1309`](https://github.com/nodejs/node/commit/789fef1309)] - **src**: throw error instead of assertion (Ray Wang) [#40243](https://github.com/nodejs/node/pull/40243)
- \[[`7a8a6deee7`](https://github.com/nodejs/node/commit/7a8a6deee7)] - **src**: register external references in os bindings (Joyee Cheung) [#40239](https://github.com/nodejs/node/pull/40239)
- \[[`7bb3d43432`](https://github.com/nodejs/node/commit/7bb3d43432)] - **src**: register external references in crypto bindings (Joyee Cheung) [#40239](https://github.com/nodejs/node/pull/40239)
- \[[`143c881ccb`](https://github.com/nodejs/node/commit/143c881ccb)] - **src**: add missing inialization in agent.h (Michael Dawson) [#40379](https://github.com/nodejs/node/pull/40379)
- \[[`c15afda79f`](https://github.com/nodejs/node/commit/c15afda79f)] - **src**: get embedder options on-demand (Joyee Cheung) [#40357](https://github.com/nodejs/node/pull/40357)
- \[[`ff3b7d228e`](https://github.com/nodejs/node/commit/ff3b7d228e)] - **src**: ensure V8 initialized before marking milestone (Shelley Vohr) [#40405](https://github.com/nodejs/node/pull/40405)
- \[[`774bc46327`](https://github.com/nodejs/node/commit/774bc46327)] - **src,crypto**: remove `AllocatedBuffer` from `crypto_cipher.cc` (Darshan Sen) [#40400](https://github.com/nodejs/node/pull/40400)
- \[[`4030eff3d6`](https://github.com/nodejs/node/commit/4030eff3d6)] - **src,fs**: remove `ToLocalChecked()` call from `fs::AfterMkdirp()` (Darshan Sen) [#40386](https://github.com/nodejs/node/pull/40386)
- \[[`3ac99a2417`](https://github.com/nodejs/node/commit/3ac99a2417)] - **src,stream**: remove `*Check*()` calls from non-`Initialize()` functions (Darshan Sen) [#40425](https://github.com/nodejs/node/pull/40425)
- \[[`36d3b123a0`](https://github.com/nodejs/node/commit/36d3b123a0)] - **stream**: support array of streams in promises pipeline (Mestery) [#40193](https://github.com/nodejs/node/pull/40193)
- \[[`01ffe0316c`](https://github.com/nodejs/node/commit/01ffe0316c)] - **test**: deflake child-process-pipe-dataflow (Luigi Pinca) [#40838](https://github.com/nodejs/node/pull/40838)
- \[[`63b44fc429`](https://github.com/nodejs/node/commit/63b44fc429)] - **test**: skip macos sandbox test with builtin modules path (Michaël Zasso) [#40607](https://github.com/nodejs/node/pull/40607)
- \[[`3d50997ccb`](https://github.com/nodejs/node/commit/3d50997ccb)] - **test**: add semicolon after chunk size (Luigi Pinca) [#40487](https://github.com/nodejs/node/pull/40487)
- \[[`f114e35115`](https://github.com/nodejs/node/commit/f114e35115)] - **test**: deflake http2-cancel-while-client-reading (Luigi Pinca) [#40659](https://github.com/nodejs/node/pull/40659)
- \[[`f778fa230b`](https://github.com/nodejs/node/commit/f778fa230b)] - **test**: test `crypto.setEngine()` using an actual engine (Darshan Sen) [#40481](https://github.com/nodejs/node/pull/40481)
- \[[`b9533c592a`](https://github.com/nodejs/node/commit/b9533c592a)] - **test**: use conventional argument order in assertion (Tobias Nießen) [#40591](https://github.com/nodejs/node/pull/40591)
- \[[`e72c95c580`](https://github.com/nodejs/node/commit/e72c95c580)] - **test**: fix test description (Luigi Pinca) [#40486](https://github.com/nodejs/node/pull/40486)
- \[[`af4e682758`](https://github.com/nodejs/node/commit/af4e682758)] - **test**: pass URL's toascii.window\.js WPT (Khaidi Chu) [#39910](https://github.com/nodejs/node/pull/39910)
- \[[`6de88bc5ed`](https://github.com/nodejs/node/commit/6de88bc5ed)] - **test**: adjust CLI flags test to ignore blank lines in doc (Rich Trott) [#40403](https://github.com/nodejs/node/pull/40403)
- \[[`8226690097`](https://github.com/nodejs/node/commit/8226690097)] - **test**: mark test-policy-integrity flaky on Windows (Rich Trott) [#40684](https://github.com/nodejs/node/pull/40684)
- \[[`50c6666b37`](https://github.com/nodejs/node/commit/50c6666b37)] - **test**: fix test-datetime-change-notify after daylight change (Piotr Rybak) [#40684](https://github.com/nodejs/node/pull/40684)
- \[[`9227f2af79`](https://github.com/nodejs/node/commit/9227f2af79)] - **test**: split test-crypto-dh.js (Joyee Cheung) [#40451](https://github.com/nodejs/node/pull/40451)
- \[[`c593cff0af`](https://github.com/nodejs/node/commit/c593cff0af)] - **test,doc**: correct documentation for runBenchmark() (Rich Trott) [#40683](https://github.com/nodejs/node/pull/40683)
- \[[`aef809f5c8`](https://github.com/nodejs/node/commit/aef809f5c8)] - **test,tools**: increase pummel/benchmark test timeout from 4x to 6x (Rich Trott) [#40684](https://github.com/nodejs/node/pull/40684)
- \[[`908f6447cd`](https://github.com/nodejs/node/commit/908f6447cd)] - **test,tools**: increase timeout for benchmark tests (Rich Trott) [#40684](https://github.com/nodejs/node/pull/40684)
- \[[`64c6575f44`](https://github.com/nodejs/node/commit/64c6575f44)] - **tools**: simplify and fix commit queue (Michaël Zasso) [#40742](https://github.com/nodejs/node/pull/40742)
- \[[`cba8eaf264`](https://github.com/nodejs/node/commit/cba8eaf264)] - **tools**: ensure the PR was not pushed before merging (Antoine du Hamel) [#40747](https://github.com/nodejs/node/pull/40747)
- \[[`1c8590e1fe`](https://github.com/nodejs/node/commit/1c8590e1fe)] - **tools**: update ESLint to 8.2.0 (Luigi Pinca) [#40734](https://github.com/nodejs/node/pull/40734)
- \[[`18800dee0a`](https://github.com/nodejs/node/commit/18800dee0a)] - **tools**: use GitHub Squash and Merge feature when using CQ (Antoine du Hamel) [#40666](https://github.com/nodejs/node/pull/40666)
- \[[`48a785edb7`](https://github.com/nodejs/node/commit/48a785edb7)] - **tools**: fix bug in `prefer-primordials` ESLint rule (Antoine du Hamel) [#40628](https://github.com/nodejs/node/pull/40628)
- \[[`adde2a7a8c`](https://github.com/nodejs/node/commit/adde2a7a8c)] - **tools**: add script to update c-ares (Richard Lau) [#40660](https://github.com/nodejs/node/pull/40660)
- \[[`c12ce898e8`](https://github.com/nodejs/node/commit/c12ce898e8)] - **tools**: abort CQ session when landing several commits (Antoine du Hamel) [#40577](https://github.com/nodejs/node/pull/40577)
- \[[`dd08e532a2`](https://github.com/nodejs/node/commit/dd08e532a2)] - **tools**: fix commit-lint workflow (Antoine du Hamel) [#40673](https://github.com/nodejs/node/pull/40673)
- \[[`b4a80dba79`](https://github.com/nodejs/node/commit/b4a80dba79)] - **tools**: avoid fetch extra commits when validating commit messages (Antoine du Hamel) [#39128](https://github.com/nodejs/node/pull/39128)
- \[[`2a53995442`](https://github.com/nodejs/node/commit/2a53995442)] - **tools**: update ESLint to 8.1.0 (Luigi Pinca) [#40582](https://github.com/nodejs/node/pull/40582)
- \[[`8648e50183`](https://github.com/nodejs/node/commit/8648e50183)] - **tools**: fix formatting of warning message in update-authors.js (Rich Trott) [#40600](https://github.com/nodejs/node/pull/40600)
- \[[`59de0f703f`](https://github.com/nodejs/node/commit/59de0f703f)] - **tools**: udpate doc tools to accommodate GFM footnotes (Rich Trott) [#40477](https://github.com/nodejs/node/pull/40477)
- \[[`abf3b84d77`](https://github.com/nodejs/node/commit/abf3b84d77)] - **tools**: add support for import assertions in linter (Antoine du Hamel) [#39924](https://github.com/nodejs/node/pull/39924)
- \[[`04c2cbecb9`](https://github.com/nodejs/node/commit/04c2cbecb9)] - **tools**: update tools/lint-md dependencies to support GFM footnotes (Rich Trott) [#40445](https://github.com/nodejs/node/pull/40445)
- \[[`a9990876f7`](https://github.com/nodejs/node/commit/a9990876f7)] - **tools**: update lint-md dependencies (Rich Trott) [#40404](https://github.com/nodejs/node/pull/40404)
- \[[`f45814bad1`](https://github.com/nodejs/node/commit/f45814bad1)] - **tools,meta**: remove exclusions from AUTHORS (Rich Trott) [#40648](https://github.com/nodejs/node/pull/40648)
- \[[`7d550ad966`](https://github.com/nodejs/node/commit/7d550ad966)] - **tty**: support more CI services in `getColorDepth` (Richie Bendall) [#40385](https://github.com/nodejs/node/pull/40385)
- \[[`cdea5b671b`](https://github.com/nodejs/node/commit/cdea5b671b)] - **typings**: add more bindings typings (Mestery) [#40415](https://github.com/nodejs/node/pull/40415)
- \[[`67c7d11f1a`](https://github.com/nodejs/node/commit/67c7d11f1a)] - **typings**: add JSDoc typings for inspector (Voltrex) [#38390](https://github.com/nodejs/node/pull/38390)
- \[[`fbe0323ebf`](https://github.com/nodejs/node/commit/fbe0323ebf)] - **typings**: improve internal bindings typings (Mestery) [#40411](https://github.com/nodejs/node/pull/40411)
- \[[`63ab0031c3`](https://github.com/nodejs/node/commit/63ab0031c3)] - **typings**: separate `internalBinding` typings (Mestery) [#40409](https://github.com/nodejs/node/pull/40409)

Windows 32-bit Installer: https://nodejs.org/dist/v16.13.1/node-v16.13.1-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v16.13.1/node-v16.13.1-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v16.13.1/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v16.13.1/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v16.13.1/node-v16.13.1.pkg \
macOS Apple Silicon 64-bit Binary: https://nodejs.org/dist/v16.13.1/node-v16.13.1-darwin-arm64.tar.gz \
macOS Intel 64-bit Binary: https://nodejs.org/dist/v16.13.1/node-v16.13.1-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v16.13.1/node-v16.13.1-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v16.13.1/node-v16.13.1-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v16.13.1/node-v16.13.1-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v16.13.1/node-v16.13.1-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v16.13.1/node-v16.13.1-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v16.13.1/node-v16.13.1-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v16.13.1/node-v16.13.1.tar.gz \
Other release files: https://nodejs.org/dist/v16.13.1/ \
Documentation: https://nodejs.org/docs/v16.13.1/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

b1986cc40595c615fa5c51e29148c41c9e2446c479cdb99575a033f30c5f419c  node-v16.13.1-aix-ppc64.tar.gz
2d27c10c49af87a8d87bce4d32ca0e37afbc8dcc73d524ec7de3506c6309d4fc  node-v16.13.1-darwin-arm64.tar.gz
8e1c244ada393734a2fd8b6ea0356ce6569b3c05d834ee4cab57e4c42456d8bd  node-v16.13.1-darwin-arm64.tar.xz
a139fc6a4c8daf160989420535378d69b53a0d9f5ae43871e9befeb2b8a44187  node-v16.13.1-darwin-x64.tar.gz
e4683ade1b198fb54e95a7ac8064105a9696bc358f2693365485f13126387ca5  node-v16.13.1-darwin-x64.tar.xz
f2101cdf05dd040397000596ed0f285dff74a926637cfeabab8e98bb7ba67327  node-v16.13.1-headers.tar.gz
9274cb504c58585e4cf90999ac678daa7c90ff21063ac77a1f7ce7c0e7535eb6  node-v16.13.1-headers.tar.xz
c2f2a0a5adbfc267dbe41ef9fbd83af157a64997bc7546c12717ff55ea6b57d8  node-v16.13.1-linux-arm64.tar.gz
af1127594d6dae96d3f1d307174daa5084d9c9027eb6fc02548022257f4b0a6a  node-v16.13.1-linux-arm64.tar.xz
749bc9191f1ba3b2c9d79d74675a3c19a69a3e7da5f17d5a1ce3d05a6cbef88e  node-v16.13.1-linux-armv7l.tar.gz
0816ba8750651a49a5b1bf4fa82d0e080bddc00c01c4316948b82a146b2ec18a  node-v16.13.1-linux-armv7l.tar.xz
58810743fbfe782e7dbeb1153769bc8aeb3d1b6dcb470c82eca58ab0bc840332  node-v16.13.1-linux-ppc64le.tar.gz
c19affa95c8a3e52ae99fc3d08c713328de921a3e71c9c5ddb844886e3caa038  node-v16.13.1-linux-ppc64le.tar.xz
27ac1da92065d04916cd6abe8c6b305bb2a358d0fa3f45417feecdc8641abf88  node-v16.13.1-linux-s390x.tar.gz
36abceeb29fb4ddd71d6ea15cdf16ee1507702ef44a33998998eeea97e8e7e88  node-v16.13.1-linux-s390x.tar.xz
5f80197d654fd0b749cdeddf1f07a5eac1fcf6b423a00ffc8f2d3bea9c6dc8d1  node-v16.13.1-linux-x64.tar.gz
a3721f87cecc0b52b0be8587c20776ac7305db413751db02c55aa2bffac15198  node-v16.13.1-linux-x64.tar.xz
ec614451f343c28309ed0e7566752ee775cf800565e63df5fbb46cc5eeab3ca7  node-v16.13.1.pkg
34b23965457fb08a8c62f81e8faf74ea60587cda6fa898e5d030211f5f374cb6  node-v16.13.1.tar.gz
4c23004fd75eaf799ad8e76fe34f53e0327f433d4acbfc883396f72e96cc63ad  node-v16.13.1.tar.xz
e4e06ca94b46522096cbf3baa836dbb8af8b44d6c0db0ad577881dc9edf1d252  node-v16.13.1-win-x64.7z
a9147e9a86f8420893bafc4ef041e578795dc5874b9dccdd731699613b8a60ab  node-v16.13.1-win-x64.zip
439728a9c2d708e24669897d40b64b02e8d4e55c2e8c5e6f90552e1f20cf534e  node-v16.13.1-win-x86.7z
d0421f0bd08641a2c9cef44f75c73b53843cace4f1a60cbd60f88a506c822253  node-v16.13.1-win-x86.zip
8c0434b1735110ef8fbd012c46dfa867fea360b29fbbf76f0995361d17243103  node-v16.13.1-x64.msi
133d0d96bfa4825f9beca6c8e0603d77a8616f71c2a22222a868524cce4e1b3b  node-v16.13.1-x86.msi
9ee6bfb71095b215e0c724fe52830ecdc435d57e288418daeaa4fc80baa3fd76  win-x64/node.exe
034607ba97ebe59b00d4128aeb4bbb12c79550832a546d83b25666902b8ffab1  win-x64/node.lib
866342f7d8ad5ea2aa4d07ab044f9a005c36c1e99b16188e8a2afd29744df4ea  win-x64/node_pdb.7z
457d32c4b193d10e37719d84f33b2fcb7bf68f94fe6b04d32d2d26d1358d33fd  win-x64/node_pdb.zip
79742a53e555909aac020403e0d3a46d51fa70f410da80f6561ef185107529d7  win-x86/node.exe
6b774d921793b32c4ca2f629972c702ae91a14fb4f391639c913a259cd714844  win-x86/node.lib
193dfd7538f5f2f16a3a4730cbae60573b613cb548f7d639dc5c524dad41c5ab  win-x86/node_pdb.7z
579c45886f53d4de4437885043ad76e38a4b62cc9d416404768c519bc7b9adb8  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQEzBAEBCAAdFiEETtd49TnjY0x3nIfG1wYoSKGrAFwFAmGnnxMACgkQ1wYoSKGr
AFwlmwf+KD8Zh8B1S4xslKV0OhN6jSa2ez3sppfQQSH9CMnjcKJwjJZLQYcc7KoQ
6zXRzIhLfa1gH5nRkH6jPtiWNxK3TyUeuXlBAYL235RunE/ZinwdLv+Zsv/MRcCM
NyhOaDujYu0RnFDxxkpZCeNn2v7/2jYcSi3Sliute7l5+51QJgWxh0Ie2wRzHUZ2
IbzA6+vgGA64WDWpXF9RVNqHENCdRZlOsWDNv1fSagwqmqTOsYcDgHbnSSD3Uwac
KrHBWhym9QWIhLBbb7VqiHbqmC1nLwR1eBkwjE/rmq4ZHpPdQKtgPSa7QmkGlCdA
xXBmLz6nwB7AkZpCRynQFeA+PxJABw==
=8gUE
-----END PGP SIGNATURE-----

```
