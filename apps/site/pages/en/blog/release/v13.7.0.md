---
date: '2020-01-21T18:43:13.626Z'
category: release
title: Node v13.7.0 (Current)
layout: blog-post
author: <PERSON>
---

### Notable Changes

- **deps**:
  - upgrade to libuv 1.34.1 (c<PERSON><PERSON><PERSON>) [#31332](https://github.com/nodejs/node/pull/31332)
  - upgrade npm to 6.13.6 (<PERSON><PERSON>) [#31304](https://github.com/nodejs/node/pull/31304)
- **module**
  - add API for interacting with source maps (bcoe) [#31132](https://github.com/nodejs/node/pull/31132)
  - loader getSource, getFormat, transform hooks (<PERSON>) [#30986](https://github.com/nodejs/node/pull/30986)
  - logical conditional exports ordering (<PERSON>) [#31008](https://github.com/nodejs/node/pull/31008)
  - unflag conditional exports (<PERSON>) [#31001](https://github.com/nodejs/node/pull/31001)
- **process**:
  - allow monitoring uncaughtException (<PERSON>) [#31257](https://github.com/nodejs/node/pull/31257)
- **Added new collaborators**:
  - [<PERSON>Booth](https://github.com/<PERSON>Booth) - Geoffrey Booth. [#31306](https://github.com/nodejs/node/pull/31306)

### Commits

- [[`9d26358cfe`](https://github.com/nodejs/node/commit/9d26358cfe)] - **async_hooks**: remove internal only error checking (Anatoli Papirovski) [#30967](https://github.com/nodejs/node/pull/30967)
- [[`6e978f7d73`](https://github.com/nodejs/node/commit/6e978f7d73)] - **benchmark**: add default type in getstringwidth.js (Rich Trott) [#31377](https://github.com/nodejs/node/pull/31377)
- [[`317d2dba45`](https://github.com/nodejs/node/commit/317d2dba45)] - **benchmark**: benchmarking impacts of async hooks on promises (legendecas) [#31188](https://github.com/nodejs/node/pull/31188)
- [[`55e2b4ee3b`](https://github.com/nodejs/node/commit/55e2b4ee3b)] - **build**: remove enable_vtune from vcbuild.bat (Richard Lau) [#31338](https://github.com/nodejs/node/pull/31338)
- [[`f11406de43`](https://github.com/nodejs/node/commit/f11406de43)] - **build**: add vs2019 to vcbuild.bat help (Richard Lau) [#31338](https://github.com/nodejs/node/pull/31338)
- [[`b2180d932a`](https://github.com/nodejs/node/commit/b2180d932a)] - **build**: fix macos runner type in GitHub Action (扩散性百万甜面包) [#31327](https://github.com/nodejs/node/pull/31327)
- [[`a6e1e9c6c3`](https://github.com/nodejs/node/commit/a6e1e9c6c3)] - **build**: fix step name in GitHub Actions workflow (Richard Lau) [#31323](https://github.com/nodejs/node/pull/31323)
- [[`0379c319fd`](https://github.com/nodejs/node/commit/0379c319fd)] - **build**: add GitHub actions to run linters (Richard Lau) [#31323](https://github.com/nodejs/node/pull/31323)
- [[`a31eed0214`](https://github.com/nodejs/node/commit/a31eed0214)] - **build**: silence OpenSSL Windows compiler warnings (Richard Lau) [#31311](https://github.com/nodejs/node/pull/31311)
- [[`6b118b44e8`](https://github.com/nodejs/node/commit/6b118b44e8)] - **build**: silence c-ares Windows compiler warnings (Richard Lau) [#31311](https://github.com/nodejs/node/pull/31311)
- [[`7a5d4fad84`](https://github.com/nodejs/node/commit/7a5d4fad84)] - **build**: test Python 3 using GitHub Actions-based CI (cclauss) [#29474](https://github.com/nodejs/node/pull/29474)
- [[`964371824c`](https://github.com/nodejs/node/commit/964371824c)] - **build**: avoid using CMP for BZ2File (SpaceRacet5w2A6l0I) [#31198](https://github.com/nodejs/node/pull/31198)
- [[`22859367ca`](https://github.com/nodejs/node/commit/22859367ca)] - **child_process**: remove unnecessary use of inner state (Chetan Karande) [#29358](https://github.com/nodejs/node/pull/29358)
- [[`6d6a3e4551`](https://github.com/nodejs/node/commit/6d6a3e4551)] - **deps**: V8: cherry-pick d89f4ef1cd62 (Milad Farazmand) [#31354](https://github.com/nodejs/node/pull/31354)
- [[`d62d06b3b7`](https://github.com/nodejs/node/commit/d62d06b3b7)] - **deps**: V8: cherry-pick b9d33036e9a8 (Benjamin Coe) [#31335](https://github.com/nodejs/node/pull/31335)
- [[`51e4a5618b`](https://github.com/nodejs/node/commit/51e4a5618b)] - **deps**: upgrade to libuv 1.34.1 (cjihrig) [#31332](https://github.com/nodejs/node/pull/31332)
- [[`985f980053`](https://github.com/nodejs/node/commit/985f980053)] - **deps**: upgrade npm to 6.13.6 (Ruy Adorno) [#31304](https://github.com/nodejs/node/pull/31304)
- [[`6f95f01f95`](https://github.com/nodejs/node/commit/6f95f01f95)] - **deps**: deactivate failing tests corresponding to experimental features (Ruben Bridgewater) [#31289](https://github.com/nodejs/node/pull/31289)
- [[`aed00d7d68`](https://github.com/nodejs/node/commit/aed00d7d68)] - **doc**: add missing code formatting in vm.md (cjihrig) [#31350](https://github.com/nodejs/node/pull/31350)
- [[`aedbfdb33a`](https://github.com/nodejs/node/commit/aedbfdb33a)] - **doc**: standardize on "host name" in url.md (Rich Trott) [#31326](https://github.com/nodejs/node/pull/31326)
- [[`28245277d7`](https://github.com/nodejs/node/commit/28245277d7)] - **doc**: standardize on "host name" in tls.md (Rich Trott) [#31326](https://github.com/nodejs/node/pull/31326)
- [[`baeabff896`](https://github.com/nodejs/node/commit/baeabff896)] - **doc**: standardize on "host name" in os.md (Rich Trott) [#31326](https://github.com/nodejs/node/pull/31326)
- [[`94122f611a`](https://github.com/nodejs/node/commit/94122f611a)] - **doc**: standardize on "host name" in net.md (Rich Trott) [#31326](https://github.com/nodejs/node/pull/31326)
- [[`bac588e076`](https://github.com/nodejs/node/commit/bac588e076)] - **doc**: standardize on "host name" in https.md (Rich Trott) [#31326](https://github.com/nodejs/node/pull/31326)
- [[`600eb8b67c`](https://github.com/nodejs/node/commit/600eb8b67c)] - **doc**: standardize on "host name" in http2.md (Rich Trott) [#31326](https://github.com/nodejs/node/pull/31326)
- [[`47f71de786`](https://github.com/nodejs/node/commit/47f71de786)] - **doc**: standardize on "host name" in fs.md (Rich Trott) [#31326](https://github.com/nodejs/node/pull/31326)
- [[`ece70a8288`](https://github.com/nodejs/node/commit/ece70a8288)] - **doc**: standardize on "host name" in errors.md (Rich Trott) [#31326](https://github.com/nodejs/node/pull/31326)
- [[`b8dee4540a`](https://github.com/nodejs/node/commit/b8dee4540a)] - **doc**: standardize on "host name" in dgram.md (Rich Trott) [#31326](https://github.com/nodejs/node/pull/31326)
- [[`8055f78995`](https://github.com/nodejs/node/commit/8055f78995)] - **doc**: standardize on "host name" in deprecations.md (Rich Trott) [#31326](https://github.com/nodejs/node/pull/31326)
- [[`6e9f0daad1`](https://github.com/nodejs/node/commit/6e9f0daad1)] - **doc**: standardize on "host name" in async_hooks.md (Rich Trott) [#31326](https://github.com/nodejs/node/pull/31326)
- [[`e1fd6ae4fa`](https://github.com/nodejs/node/commit/e1fd6ae4fa)] - **doc**: fix a code example in crypto.md (himself65) [#31313](https://github.com/nodejs/node/pull/31313)
- [[`bb9622ba5a`](https://github.com/nodejs/node/commit/bb9622ba5a)] - **doc**: add an example for util.types.isExternal (Harshitha KP) [#31173](https://github.com/nodejs/node/pull/31173)
- [[`0608873052`](https://github.com/nodejs/node/commit/0608873052)] - **doc**: fix example of parsing request.url (Egor Pavlov) [#31302](https://github.com/nodejs/node/pull/31302)
- [[`b9aca7849d`](https://github.com/nodejs/node/commit/b9aca7849d)] - **doc**: document readline key bindings (Harshitha KP) [#31256](https://github.com/nodejs/node/pull/31256)
- [[`6184f1ab70`](https://github.com/nodejs/node/commit/6184f1ab70)] - **doc**: improve doc v8.getHeapSpaceStatistics() 'GetHeapSpaceStatistics' (dev-313) [#31274](https://github.com/nodejs/node/pull/31274)
- [[`deff60024a`](https://github.com/nodejs/node/commit/deff60024a)] - **doc**: update README to make Node.js description clearer (carterbancroft) [#31266](https://github.com/nodejs/node/pull/31266)
- [[`8e14066578`](https://github.com/nodejs/node/commit/8e14066578)] - **doc**: fix a code example in zlib.md (Alexander Wang) [#31264](https://github.com/nodejs/node/pull/31264)
- [[`9c58aa4c75`](https://github.com/nodejs/node/commit/9c58aa4c75)] - **doc**: add GeoffreyBooth to collaborators (Geoffrey Booth) [#31306](https://github.com/nodejs/node/pull/31306)
- [[`de6f2be0d0`](https://github.com/nodejs/node/commit/de6f2be0d0)] - **doc**: update description of `External` (Anna Henningsen) [#31255](https://github.com/nodejs/node/pull/31255)
- [[`0e48d8d855`](https://github.com/nodejs/node/commit/0e48d8d855)] - **doc**: rename iterator to iterable in examples (Robert Nagy) [#31252](https://github.com/nodejs/node/pull/31252)
- [[`d51de787d9`](https://github.com/nodejs/node/commit/d51de787d9)] - **doc**: fix stream async iterator sample (Robert Nagy) [#31252](https://github.com/nodejs/node/pull/31252)
- [[`3e7b3e3c18`](https://github.com/nodejs/node/commit/3e7b3e3c18)] - **doc**: correct filehandle.\[read|write|append\]File() (Bryan English) [#31235](https://github.com/nodejs/node/pull/31235)
- [[`220ea0c12e`](https://github.com/nodejs/node/commit/220ea0c12e)] - **doc**: prefer server vs srv and client vs clt (Andrew Hughes) [#31224](https://github.com/nodejs/node/pull/31224)
- [[`c1333ea113`](https://github.com/nodejs/node/commit/c1333ea113)] - **doc**: explain native external types (Harshitha KP) [#31214](https://github.com/nodejs/node/pull/31214)
- [[`82b447c399`](https://github.com/nodejs/node/commit/82b447c399)] - **doc,src**: clarify that one napi_env is per-module (legendecas) [#31102](https://github.com/nodejs/node/pull/31102)
- [[`4981f9721a`](https://github.com/nodejs/node/commit/4981f9721a)] - **errors**: remove dead code in ERR_INVALID_ARG_TYPE (Gerhard Stoebich) [#31322](https://github.com/nodejs/node/pull/31322)
- [[`b55fba2028`](https://github.com/nodejs/node/commit/b55fba2028)] - **fs**: add missing HandleScope to FileHandle.close (Anna Henningsen) [#31276](https://github.com/nodejs/node/pull/31276)
- [[`57016b9e66`](https://github.com/nodejs/node/commit/57016b9e66)] - **fs**: use async writeFile in FileHandle#appendFile (Bryan English) [#31235](https://github.com/nodejs/node/pull/31235)
- [[`52504fb91e`](https://github.com/nodejs/node/commit/52504fb91e)] - **http2**: skip creating native ShutdownWrap (Anna Henningsen) [#31283](https://github.com/nodejs/node/pull/31283)
- [[`108046d910`](https://github.com/nodejs/node/commit/108046d910)] - **lib**: replace BigInt64Array global by the primordials (Sebastien Ahkrin) [#31193](https://github.com/nodejs/node/pull/31193)
- [[`02714573ee`](https://github.com/nodejs/node/commit/02714573ee)] - **lib**: add Uint16Array primordials (Sebastien Ahkrin) [#31210](https://github.com/nodejs/node/pull/31210)
- [[`53e73fc60e`](https://github.com/nodejs/node/commit/53e73fc60e)] - **lib**: add RegExp primordials (Sebastien Ahkrin) [#31208](https://github.com/nodejs/node/pull/31208)
- [[`f7833ac934`](https://github.com/nodejs/node/commit/f7833ac934)] - **lib**: replace Float32Array global by the primordials (Sebastien Ahkrin) [#31195](https://github.com/nodejs/node/pull/31195)
- [[`aafeab8cdb`](https://github.com/nodejs/node/commit/aafeab8cdb)] - **lib**: replace BigUInt64Array global by the primordials (Sebastien Ahkrin) [#31194](https://github.com/nodejs/node/pull/31194)
- [[`ac904f9e65`](https://github.com/nodejs/node/commit/ac904f9e65)] - **lib,tools,test**: remove custom number-isnan rule (cjihrig) [#31211](https://github.com/nodejs/node/pull/31211)
- [[`cb27c2bd3e`](https://github.com/nodejs/node/commit/cb27c2bd3e)] - **module**: fix check exports issue in cjs module loading (Guy Bedford) [#31427](https://github.com/nodejs/node/pull/31427)
- [[`ea27e16fc2`](https://github.com/nodejs/node/commit/ea27e16fc2)] - **(SEMVER-MINOR)** **module**: unflag conditional exports (Guy Bedford) [#31001](https://github.com/nodejs/node/pull/31001)
- [[`4dced024fd`](https://github.com/nodejs/node/commit/4dced024fd)] - **(SEMVER-MINOR)** **module**: add API for interacting with source maps (bcoe) [#31132](https://github.com/nodejs/node/pull/31132)
- [[`f62fb7603a`](https://github.com/nodejs/node/commit/f62fb7603a)] - **module**: logical conditional exports ordering (Guy Bedford) [#31008](https://github.com/nodejs/node/pull/31008)
- [[`94af94ae73`](https://github.com/nodejs/node/commit/94af94ae73)] - **module**: loader getSource, getFormat, transform hooks (Geoffrey Booth) [#30986](https://github.com/nodejs/node/pull/30986)
- [[`c8aa08ed27`](https://github.com/nodejs/node/commit/c8aa08ed27)] - **n-api**: return napi_invalid_arg on napi_create_bigint_words (legendecas) [#31312](https://github.com/nodejs/node/pull/31312)
- [[`0911813862`](https://github.com/nodejs/node/commit/0911813862)] - **(SEMVER-MINOR)** **n-api**: add napi_get_all_property_names (himself65) [#30006](https://github.com/nodejs/node/pull/30006)
- [[`79eba6afa3`](https://github.com/nodejs/node/commit/79eba6afa3)] - **(SEMVER-MINOR)** **process**: allow monitoring uncaughtException (Gerhard Stoebich) [#31257](https://github.com/nodejs/node/pull/31257)
- [[`38811897c0`](https://github.com/nodejs/node/commit/38811897c0)] - **readline**: improve unicode support and tab completion (Ruben Bridgewater) [#31288](https://github.com/nodejs/node/pull/31288)
- [[`f0506c3dd2`](https://github.com/nodejs/node/commit/f0506c3dd2)] - **readline**: move charLengthLeft() and charLengthAt() (Ruben Bridgewater) [#31112](https://github.com/nodejs/node/pull/31112)
- [[`7ba21d0e15`](https://github.com/nodejs/node/commit/7ba21d0e15)] - **readline**: improve getStringWidth() (Ruben Bridgewater) [#31112](https://github.com/nodejs/node/pull/31112)
- [[`686a3bcf92`](https://github.com/nodejs/node/commit/686a3bcf92)] - **readline,repl**: support tabs properly (Ruben Bridgewater) [#31112](https://github.com/nodejs/node/pull/31112)
- [[`2e54a9922e`](https://github.com/nodejs/node/commit/2e54a9922e)] - **readline,repl**: improve history up/previous (Ruben Bridgewater) [#31112](https://github.com/nodejs/node/pull/31112)
- [[`cecd25693f`](https://github.com/nodejs/node/commit/cecd25693f)] - **readline,repl**: skip history entries identical to the current line (Ruben Bridgewater) [#31112](https://github.com/nodejs/node/pull/31112)
- [[`b6f4e01a0e`](https://github.com/nodejs/node/commit/b6f4e01a0e)] - **readline,repl**: add substring based history search (Ruben Bridgewater) [#31112](https://github.com/nodejs/node/pull/31112)
- [[`85926d4038`](https://github.com/nodejs/node/commit/85926d4038)] - **repl**: do not preview while pasting code (Ruben Bridgewater) [#31315](https://github.com/nodejs/node/pull/31315)
- [[`c252356d38`](https://github.com/nodejs/node/commit/c252356d38)] - **repl**: fix preview cursor position (Ruben Bridgewater) [#31293](https://github.com/nodejs/node/pull/31293)
- [[`b9b044b98e`](https://github.com/nodejs/node/commit/b9b044b98e)] - **repl**: change preview default in case of custom eval functions (Ruben Bridgewater) [#31259](https://github.com/nodejs/node/pull/31259)
- [[`b92d65dbe7`](https://github.com/nodejs/node/commit/b92d65dbe7)] - **repl**: activate previews for lines exceeding the terminal columns (Ruben Bridgewater) [#31112](https://github.com/nodejs/node/pull/31112)
- [[`d84c394541`](https://github.com/nodejs/node/commit/d84c394541)] - **repl**: improve preview length calculation (Ruben Bridgewater) [#31112](https://github.com/nodejs/node/pull/31112)
- [[`f8e805985e`](https://github.com/nodejs/node/commit/f8e805985e)] - **repl,readline**: clean up code (Ruben Bridgewater) [#31288](https://github.com/nodejs/node/pull/31288)
- [[`83f7b5a8a9`](https://github.com/nodejs/node/commit/83f7b5a8a9)] - **src**: fix performance regression in node_file.cc (Ben Noordhuis) [#31343](https://github.com/nodejs/node/pull/31343)
- [[`6534c6c7bd`](https://github.com/nodejs/node/commit/6534c6c7bd)] - **src**: use uv_guess_handle() to detect TTYs (cjihrig) [#31333](https://github.com/nodejs/node/pull/31333)
- [[`06fbc03cbd`](https://github.com/nodejs/node/commit/06fbc03cbd)] - **src**: include uv.h in node_binding header (Shelley Vohr) [#31265](https://github.com/nodejs/node/pull/31265)
- [[`e3491d7dd6`](https://github.com/nodejs/node/commit/e3491d7dd6)] - **src**: change GetStringWidth's expand_emoji_sequence option default (Ruben Bridgewater) [#31112](https://github.com/nodejs/node/pull/31112)
- [[`d2a10ad847`](https://github.com/nodejs/node/commit/d2a10ad847)] - **src**: improve GetColumnWidth performance (Ruben Bridgewater) [#31112](https://github.com/nodejs/node/pull/31112)
- [[`d0a96ab700`](https://github.com/nodejs/node/commit/d0a96ab700)] - **src**: fix -Wbraced-scalar-init warning (cjihrig) [#31254](https://github.com/nodejs/node/pull/31254)
- [[`60942cc2a7`](https://github.com/nodejs/node/commit/60942cc2a7)] - **src**: add build Github Action (gengjiawen) [#31153](https://github.com/nodejs/node/pull/31153)
- [[`4259afe583`](https://github.com/nodejs/node/commit/4259afe583)] - **src**: remove node::InitializeV8Platform() (Ben Noordhuis) [#31245](https://github.com/nodejs/node/pull/31245)
- [[`6050236c3d`](https://github.com/nodejs/node/commit/6050236c3d)] - **src**: remove uses of node::InitializeV8Platform() (Ben Noordhuis) [#31245](https://github.com/nodejs/node/pull/31245)
- [[`1ad907039d`](https://github.com/nodejs/node/commit/1ad907039d)] - **src**: clean up large_pages code (Denys Otrishko) [#31196](https://github.com/nodejs/node/pull/31196)
- [[`499c41d78a`](https://github.com/nodejs/node/commit/499c41d78a)] - **stream**: fix async iterator destroyed error propagation (Robert Nagy) [#31314](https://github.com/nodejs/node/pull/31314)
- [[`d04118f125`](https://github.com/nodejs/node/commit/d04118f125)] - **stream**: simplify push (Robert Nagy) [#31150](https://github.com/nodejs/node/pull/31150)
- [[`ff60a0e2b1`](https://github.com/nodejs/node/commit/ff60a0e2b1)] - **stream**: clean up definition using defineProperties (antsmartian) [#31236](https://github.com/nodejs/node/pull/31236)
- [[`9c98d25506`](https://github.com/nodejs/node/commit/9c98d25506)] - **stream**: replace Function.prototype with primordial (Sebastien Ahkrin) [#31204](https://github.com/nodejs/node/pull/31204)
- [[`256289fe83`](https://github.com/nodejs/node/commit/256289fe83)] - **stream**: sync stream unpipe resume (Robert Nagy) [#31191](https://github.com/nodejs/node/pull/31191)
- [[`424408005f`](https://github.com/nodejs/node/commit/424408005f)] - **test**: stricten readline keypress failure test condition (Ruben Bridgewater) [#31300](https://github.com/nodejs/node/pull/31300)
- [[`1df7961b28`](https://github.com/nodejs/node/commit/1df7961b28)] - **test**: allow disabling crypto tests (Shelley Vohr) [#31268](https://github.com/nodejs/node/pull/31268)
- [[`3c82d5bed2`](https://github.com/nodejs/node/commit/3c82d5bed2)] - **test**: add repl tests to verify unicode support in previews (Ruben Bridgewater) [#31112](https://github.com/nodejs/node/pull/31112)
- [[`ca51ff8981`](https://github.com/nodejs/node/commit/ca51ff8981)] - **test**: fix recursive rm test to actually use tmpdir (Denys Otrishko) [#31250](https://github.com/nodejs/node/pull/31250)
- [[`0b88c3d8ed`](https://github.com/nodejs/node/commit/0b88c3d8ed)] - **test**: check that --insecure-http-parser works (Sam Roberts) [#31253](https://github.com/nodejs/node/pull/31253)
- [[`69c4f229cb`](https://github.com/nodejs/node/commit/69c4f229cb)] - **test**: remove unused symlink loop (cjihrig) [#31267](https://github.com/nodejs/node/pull/31267)
- [[`d76deca9cf`](https://github.com/nodejs/node/commit/d76deca9cf)] - **test**: prefer server over srv (Andrew Hughes) [#31224](https://github.com/nodejs/node/pull/31224)
- [[`f93095de6f`](https://github.com/nodejs/node/commit/f93095de6f)] - **test**: fix unit test logging with python3 (Adam Majer) [#31156](https://github.com/nodejs/node/pull/31156)
- [[`cbd84c5ee1`](https://github.com/nodejs/node/commit/cbd84c5ee1)] - **test,module**: add test for exports cjs loader check (Rich Trott) [#31427](https://github.com/nodejs/node/pull/31427)
- [[`5dd6fb1529`](https://github.com/nodejs/node/commit/5dd6fb1529)] - **tools**: remove obsolete dependencies (Rich Trott) [#31359](https://github.com/nodejs/node/pull/31359)
- [[`29e0465104`](https://github.com/nodejs/node/commit/29e0465104)] - **tools**: update remark-preset-lint-node to 1.12.0 (Rich Trott) [#31359](https://github.com/nodejs/node/pull/31359)
- [[`49364b0835`](https://github.com/nodejs/node/commit/49364b0835)] - **tools**: update JSON header parsing for backticks (Rich Trott) [#31294](https://github.com/nodejs/node/pull/31294)
- [[`d48f59224b`](https://github.com/nodejs/node/commit/d48f59224b)] - **tools**: ensure consistent perms of signed release files (Rod Vagg) [#29350](https://github.com/nodejs/node/pull/29350)
- [[`a5311bd757`](https://github.com/nodejs/node/commit/a5311bd757)] - **tools**: add clang-tidy rule in src (gengjiawen) [#26840](https://github.com/nodejs/node/pull/26840)
- [[`63f4eaefee`](https://github.com/nodejs/node/commit/63f4eaefee)] - **util**: add todo comments for inspect to add unicode support (Ruben Bridgewater) [#31112](https://github.com/nodejs/node/pull/31112)
- [[`27564a4837`](https://github.com/nodejs/node/commit/27564a4837)] - **(SEMVER-MINOR)** **vm**: add code cache support for SourceTextModule (Gus Caplan) [#31278](https://github.com/nodejs/node/pull/31278)
- [[`bdaac04c10`](https://github.com/nodejs/node/commit/bdaac04c10)] - **wasi**: improve use of primordials (cjihrig) [#31212](https://github.com/nodejs/node/pull/31212)
- [[`66fe92353b`](https://github.com/nodejs/node/commit/66fe92353b)] - **win**: change to use Python in install tool (gengjiawen) [#31221](https://github.com/nodejs/node/pull/31221)

Windows 32-bit Installer: https://nodejs.org/dist/v13.7.0/node-v13.7.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v13.7.0/node-v13.7.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v13.7.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v13.7.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v13.7.0/node-v13.7.0.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v13.7.0/node-v13.7.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v13.7.0/node-v13.7.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v13.7.0/node-v13.7.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v13.7.0/node-v13.7.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v13.7.0/node-v13.7.0-aix-ppc64.tar.gz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v13.7.0/node-v13.7.0-sunos-x64.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v13.7.0/node-v13.7.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v13.7.0/node-v13.7.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v13.7.0/node-v13.7.0.tar.gz \
Other release files: https://nodejs.org/dist/v13.7.0/ \
Documentation: https://nodejs.org/docs/v13.7.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

50cf1dfcf1cfbc32def91e43f28ea5886135776c7dee5b3f7a4818487028f03b  node-v13.7.0-aix-ppc64.tar.gz
866ea9bdbd7b734c593af96b946397d9c7cb9c291aa8ea52a6a2af271b972169  node-v13.7.0-darwin-x64.tar.gz
b8f531808b3b51f9e6c2ca07634f618d2e6e6a052abe865e9e98a8254ea76b5f  node-v13.7.0-darwin-x64.tar.xz
4b042d9a36c115224f360d79e64a8e7be15a343f5171b4abfba8859589c48ddd  node-v13.7.0-headers.tar.gz
76883f980f46661af2f44f1927e0e427996af9c0ec60ad8b606e97da0034a5d6  node-v13.7.0-headers.tar.xz
fb492b493e13ddad73533f5b06318b7f46120ff4289475e0e91445370be1b13c  node-v13.7.0-linux-arm64.tar.gz
2823b199c7c4e6c547caf95139e28341444a5ffc52481a5ba704067291771579  node-v13.7.0-linux-arm64.tar.xz
dd31f9b0cc351b4f46e25670a0c41737fc7815b0069da15948ac38cb976a0987  node-v13.7.0-linux-armv7l.tar.gz
1a45eb16e92ac20109cc99f16ffa37821a80b9a5f41737ab9f9aff3b08ba64aa  node-v13.7.0-linux-armv7l.tar.xz
3f445104065bf9be4d33b103fe45ef7edea7d0e338bc902f73fc446f8bf812cd  node-v13.7.0-linux-ppc64le.tar.gz
a527b53765697e1d0884e7889dbcf64039057bb79ad3d5fb2cb1241c22b4a9d8  node-v13.7.0-linux-ppc64le.tar.xz
87d54ef48ad8b56032992824e40cfca1ea6a766fc745aab8f6ac239af76d7de6  node-v13.7.0-linux-s390x.tar.gz
4630a8c87861c1b4fbb95322fb7038a199f2d3f8253836e713b52d68d2d8a9f6  node-v13.7.0-linux-s390x.tar.xz
49ecb710e29c3ea0617803f450e2dc9b229688f1576190826ffdd5a9eaae7869  node-v13.7.0-linux-x64.tar.gz
02578025b82de24f4cfb3ffeb3824990431d739d09220f2db9ef9f454f475470  node-v13.7.0-linux-x64.tar.xz
a5ba11babf0cbae2fccb9b6ad9eaddd59576d1a17755a41eb9dcab324b54de05  node-v13.7.0.pkg
8b4bc9cabc3b1fdda0498f15e011e58c01d1ff768aee2ac28118b24256daf632  node-v13.7.0-sunos-x64.tar.gz
3ef92be6021abf7457c906f89bffaa408bcac603c64bd21143bfa195296e236e  node-v13.7.0-sunos-x64.tar.xz
441a1b2b6031f359597fb68b82fd27dfbbe5933f9e1242f6a03062c2abd88368  node-v13.7.0.tar.gz
70e26e628868d7a946b53c6e2ac1ee4845374fbaed659cf344de62850a2a14bb  node-v13.7.0.tar.xz
be8ed2aef726e92af6e187079099e2c2df8fff2a1733553cd5b55cc3f99976b8  node-v13.7.0-win-x64.7z
26b41de81ead8f51de2964d7c7526533f46387ff436b61596e09d678bcd7503f  node-v13.7.0-win-x64.zip
7ae3639723d4bb5610841aed396d1f285f965a3133b8e354cc4f0c2dee457da7  node-v13.7.0-win-x86.7z
cf42fb3ecfbe4e5111da23a2f5ee617e326d786a596cbb7dfb9fc1e62fe41bf4  node-v13.7.0-win-x86.zip
7d0fe36f612291ccf12abb9607e6759ff591d3f409384612fab7dd797b053949  node-v13.7.0-x64.msi
c578401253fa8302b6f0fdf39d209839576f5ad269e326d0f827283121cc7c8e  node-v13.7.0-x86.msi
b412e8be916b4472430d715fac8a10d2b18a3a2f8ace14d272c42a71ab9990f0  win-x64/node.exe
b5b4c2306b5abdd5e786e58b04ac800ab802e5f34a8b421f7e46b5f336b0c644  win-x64/node.lib
94c841a3bb9186a8170be7407c2f9897a4d0f9bfedf89ba5942379a92b765360  win-x64/node_pdb.7z
073f773d38010e13bbc588d0afd592e4da0bdd5b69b207439e446d18ecb26972  win-x64/node_pdb.zip
5b7f3f0607689f0a1144d5069a3c38f752d4eceeb639a685c1a4e1ea9941ca4d  win-x86/node.exe
05e233e73af7c3b2f7c2b40a6a01f6ef70c7d26690f97f5814b38cf680d7b417  win-x86/node.lib
ea515a12c139b6538cd59544e6fbe0dfb4a4fa1250cfda7c5b5beff7da5882aa  win-x86/node_pdb.7z
8b5477e5d268f3b2cc2ef546c8bc6c7a4e1815c5a1410daeb97358dd24e14ccf  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEueL1mBqm4M0oFg2f8TmTp1WZZTwFAl4nQbEACgkQ8TmTp1WZ
ZTwYdw/+N4gJJ2b60K9837XjiV7mR9mPWAcuaqNpJpB2hns7BxM2at/jBjEkHOOS
yoxIsLJoY6CFQ55dEuyoPqqkKQL1EKTIy7FsB6FkJW9mVpcy9IivbnDQmVKElM0U
gk+ZeGGeOKNq7xQ4sfgxA1NnxGLeG0VNbWkH6iV9uB2iY4LjCZ8FEOsqTsk/wHD1
DM8b0tOhp12ri5KWqCtKJdQ76ZmJG3CLqCAfYPEsRIuYdmbcEJTgfqdWs3Y448R2
IMsXIFnPkFTT1aw4viATTpYo0XhbV7ob4ro+TVkzS8UOMVFyV4l2B1k9eLscZUTX
VLKi3QUP644h59BcAYQunUTYYbT1rYiN4BPvWDQIusJvrpqbLB3zds1Q88qJ3wzt
GDyAM0jxZpOQ4taZypk28gOLQQ22D1ERmb66bp+9Mb8gs7PttPiaqj5KN8hYZWG/
LUfMyQ+9ZI1lQlGgvMijo963af36buWfS/3fw01zng06hoObT1szWvsJSgVod4T7
W0SalDfodX5IH2//0vvra7JzrLEpvu7Tm5xdRVcihmTh/UxghlQSfv47+Rh6MCB+
UImw18NRU9kZGwBBF4IsWDz3+V0HWh4i0FWltTgAYyY+opxVGzSgn1n56KI9XeGX
oSBnJVAvskYE5KajsQLw5/hl/DMdid5ba4m9yc5vLo9zp77jhI4=
=bQbX
-----END PGP SIGNATURE-----

```
