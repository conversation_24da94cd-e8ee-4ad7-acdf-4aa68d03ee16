---
date: '2017-10-31T19:37:47.051Z'
category: release
title: Node v8.9.0 (LTS)
layout: blog-post
author: <PERSON>
---

### A New LTS Release Line

Node.js 8.x is now in LTS!

Node v8.9.0 marks the transition of Node v8.x into Long Term Support (LTS), with
the codename **"Carbon"**. The v8.x line now moves into "Active LTS", and will
remain so until April 2019, at which point it will move into "Maintenance LTS"
until December 2019. For more info see the
[Release Schedule](https://github.com/nodejs/Release#release-schedule).

### Notable Changes between 6.x (Boron) and 8.x (Carbon)

For a full list of notable changes, see the blog posts for
[Node v7.0.0](/blog/release/v7.0.0/#commits) and
[Node v8.0.0](/blog/release/v8.0.0/#semver-major-commits).

The [Node v8.0.0 post](/blog/release/v8.0.0/) also contains
an in-depth look at the significant changes in the Node 8.x release line.

### Notable Changes in v8.9.0

- **doc**:
  - add <PERSON> to Release team (<PERSON>) [#16620](https://github.com/nodejs/node/pull/16620)
- **deps**:
  - update npm to 5.5.1 (Myles Borins) [#16509](https://github.com/nodejs/node/pull/16509)
- **http2**:
  - The exposed http2 socket is no longer manipulatable (Anatoli Papirovski) [#16330](https://github.com/nodejs/node/pull/16330)
- **module**:
  - support custom paths to require.resolve() (cjihrig) [#16397](https://github.com/nodejs/node/pull/16397)
- **util**:
  - util.TextEncoder and util.TextDecoder are no longer experimental. There will no longer be a warning when they are used (James M Snell) [#15743](https://github.com/nodejs/node/pull/15743)

### Commits

- [[`d576e17691`](https://github.com/nodejs/node/commit/d576e17691)] - **build**: make test-doc and lint addon docs (Joyee Cheung) [#16377](https://github.com/nodejs/node/pull/16377)
- [[`63e33ac327`](https://github.com/nodejs/node/commit/63e33ac327)] - **build**: make doc target quiet (Daniel Bevenius) [#16516](https://github.com/nodejs/node/pull/16516)
- [[`528edb2ea8`](https://github.com/nodejs/node/commit/528edb2ea8)] - **build**: ignore empty folders in test-addons-napi (Anna Henningsen) [#16380](https://github.com/nodejs/node/pull/16380)
- [[`c8a3b2d171`](https://github.com/nodejs/node/commit/c8a3b2d171)] - **build**: run linter before running tests (Joyee Cheung) [#16284](https://github.com/nodejs/node/pull/16284)
- [[`a763fcd7a7`](https://github.com/nodejs/node/commit/a763fcd7a7)] - **build**: improve `make clean` (Refael Ackermann) [#16372](https://github.com/nodejs/node/pull/16372)
- [[`0a1f7fefc6`](https://github.com/nodejs/node/commit/0a1f7fefc6)] - **build**: skip bin override on windows (Hitesh Kanwathirtha) [#16460](https://github.com/nodejs/node/pull/16460)
- [[`3b64fa451e`](https://github.com/nodejs/node/commit/3b64fa451e)] - **build**: fix npm install with --shared (Ben Noordhuis) [#16438](https://github.com/nodejs/node/pull/16438)
- [[`9185cfef9c`](https://github.com/nodejs/node/commit/9185cfef9c)] - **build**: add lint-md-build (Daijiro Wachi) [#12756](https://github.com/nodejs/node/pull/12756)
- [[`22ec800b20`](https://github.com/nodejs/node/commit/22ec800b20)] - **build**: do not include deleted directory (Jon Moss) [#16384](https://github.com/nodejs/node/pull/16384)
- [[`b5c6d596ca`](https://github.com/nodejs/node/commit/b5c6d596ca)] - **build,win**: set /MP separately in Debug and Release (Nikolai Vavilov) [#16415](https://github.com/nodejs/node/pull/16415)
- [[`9bea207e83`](https://github.com/nodejs/node/commit/9bea207e83)] - **child_process**: fix memory leak in .fork() (Ben Noordhuis) [#15679](https://github.com/nodejs/node/pull/15679)
- [[`bf2564df0d`](https://github.com/nodejs/node/commit/bf2564df0d)] - **deps**: V8: backport b1cd96e from upstream (Ali Ijaz Sheikh) [#16308](https://github.com/nodejs/node/pull/16308)
- [[`ad692074a4`](https://github.com/nodejs/node/commit/ad692074a4)] - **deps**: cherry-pick e0d64dc from upstream V8 (Michaël Zasso) [#16490](https://github.com/nodejs/node/pull/16490)
- [[`7bdb8db440`](https://github.com/nodejs/node/commit/7bdb8db440)] - **deps**: cherry-pick 676c413 from upstream V8 (Michaël Zasso) [#16490](https://github.com/nodejs/node/pull/16490)
- [[`5787f5331f`](https://github.com/nodejs/node/commit/5787f5331f)] - **deps**: cherry-pick 2c75616 from upstream V8 (Michaël Zasso) [#16490](https://github.com/nodejs/node/pull/16490)
- [[`0d7e4d2bdc`](https://github.com/nodejs/node/commit/0d7e4d2bdc)] - **deps**: update npm to 5.5.1 (Myles Borins) [#16509](https://github.com/nodejs/node/pull/16509)
- [[`4d9c1bedbd`](https://github.com/nodejs/node/commit/4d9c1bedbd)] - **doc**: add Gibson Fahnestock to Release team (Gibson Fahnestock) [#16620](https://github.com/nodejs/node/pull/16620)
- [[`d6619b9ad4`](https://github.com/nodejs/node/commit/d6619b9ad4)] - **doc**: document missing error codes (George Bezerra) [#15160](https://github.com/nodejs/node/pull/15160)
- [[`fdc072bd9c`](https://github.com/nodejs/node/commit/fdc072bd9c)] - **doc**: fix inconsistent server.listen documentation (Martin Michaelis) [#16020](https://github.com/nodejs/node/pull/16020)
- [[`a6b3cd8166`](https://github.com/nodejs/node/commit/a6b3cd8166)] - **doc**: more accurate zlib windowBits information (Anna Henningsen) [#16511](https://github.com/nodejs/node/pull/16511)
- [[`e5c2059f88`](https://github.com/nodejs/node/commit/e5c2059f88)] - **doc**: make default values and periods consistent (Matej Krajčovič) [#16563](https://github.com/nodejs/node/pull/16563)
- [[`b93275e1a3`](https://github.com/nodejs/node/commit/b93275e1a3)] - **doc**: slightly relax 50 character rule (James M Snell) [#16523](https://github.com/nodejs/node/pull/16523)
- [[`1b08ae853e`](https://github.com/nodejs/node/commit/1b08ae853e)] - **doc**: http2.connect accepts net & tls options (Anatoli Papirovski) [#16576](https://github.com/nodejs/node/pull/16576)
- [[`04602109e8`](https://github.com/nodejs/node/commit/04602109e8)] - **doc**: add note to releases.md (Jon Moss) [#16507](https://github.com/nodejs/node/pull/16507)
- [[`03b233ed40`](https://github.com/nodejs/node/commit/03b233ed40)] - **doc**: fix CHANGELOG_V8 indentation (Jon Moss) [#16507](https://github.com/nodejs/node/pull/16507)
- [[`a7540d59e8`](https://github.com/nodejs/node/commit/a7540d59e8)] - **doc**: remove http2 pushStream weight option (Sebastiaan Deckers) [#16451](https://github.com/nodejs/node/pull/16451)
- [[`4ba06d07cc`](https://github.com/nodejs/node/commit/4ba06d07cc)] - **doc**: add dot in documentations (erwinwahyura) [#16542](https://github.com/nodejs/node/pull/16542)
- [[`83902e6e02`](https://github.com/nodejs/node/commit/83902e6e02)] - **doc**: add multiple build guide to benchmarking doc (Peter Marton) [#16142](https://github.com/nodejs/node/pull/16142)
- [[`04fac61fdd`](https://github.com/nodejs/node/commit/04fac61fdd)] - **doc**: improve http2 documentation (Jacob Hoffman-Andrews) [#16366](https://github.com/nodejs/node/pull/16366)
- [[`fe5d4535c9`](https://github.com/nodejs/node/commit/fe5d4535c9)] - **doc, win**: remove note about resize (Bartosz Sosnowski) [#16320](https://github.com/nodejs/node/pull/16320)
- [[`54ebf91394`](https://github.com/nodejs/node/commit/54ebf91394)] - **http2**: make sessions garbage-collectible (Anna Henningsen) [#16461](https://github.com/nodejs/node/pull/16461)
- [[`bfc1ad07a3`](https://github.com/nodejs/node/commit/bfc1ad07a3)] - **http2**: remove unused assignment (Anna Henningsen) [#16461](https://github.com/nodejs/node/pull/16461)
- [[`56dd734a6a`](https://github.com/nodejs/node/commit/56dd734a6a)] - **http2**: track async state for sending (Anna Henningsen) [#16461](https://github.com/nodejs/node/pull/16461)
- [[`5390d7e374`](https://github.com/nodejs/node/commit/5390d7e374)] - **http2**: move uv_prepare handle to `Http2Session` (Anna Henningsen) [#16461](https://github.com/nodejs/node/pull/16461)
- [[`95a61cbb1e`](https://github.com/nodejs/node/commit/95a61cbb1e)] - **http2**: fix stream reading resumption (Anatoli Papirovski) [#16580](https://github.com/nodejs/node/pull/16580)
- [[`98b9705cb8`](https://github.com/nodejs/node/commit/98b9705cb8)] - **http2**: simplify mapToHeaders, stricter validation (Anatoli Papirovski) [#16575](https://github.com/nodejs/node/pull/16575)
- [[`e592c320ce`](https://github.com/nodejs/node/commit/e592c320ce)] - **http2**: fix several timeout related issues (Anatoli Papirovski) [#16525](https://github.com/nodejs/node/pull/16525)
- [[`24fd8ff32a`](https://github.com/nodejs/node/commit/24fd8ff32a)] - **http2**: adjust stream buffer size (Anatoli Papirovski) [#16445](https://github.com/nodejs/node/pull/16445)
- [[`2c2b6586e7`](https://github.com/nodejs/node/commit/2c2b6586e7)] - **http2**: fix mapToHeaders() with single string value (Jinwoo Lee) [#16458](https://github.com/nodejs/node/pull/16458)
- [[`e6e99eb447`](https://github.com/nodejs/node/commit/e6e99eb447)] - **http2**: do not allow socket manipulation (Anatoli Papirovski) [#16330](https://github.com/nodejs/node/pull/16330)
- [[`3638694d8b`](https://github.com/nodejs/node/commit/3638694d8b)] - **http2**: fix errors in debug statements (Anatoli Papirovski) [#16373](https://github.com/nodejs/node/pull/16373)
- [[`754df71a79`](https://github.com/nodejs/node/commit/754df71a79)] - **https**: refactor to use http internals (Bryan English) [#16395](https://github.com/nodejs/node/pull/16395)
- [[`2ea25ad3e2`](https://github.com/nodejs/node/commit/2ea25ad3e2)] - **inspector**: track async stacks when necessary (Ali Ijaz Sheikh) [#16308](https://github.com/nodejs/node/pull/16308)
- [[`ab0d7a64aa`](https://github.com/nodejs/node/commit/ab0d7a64aa)] - **lib**: refactor wrap_js_stream for ES6/readability (Anna Henningsen) [#16158](https://github.com/nodejs/node/pull/16158)
- [[`87fd5b798f`](https://github.com/nodejs/node/commit/87fd5b798f)] - **lib**: move \_stream_wrap into internals (Anna Henningsen) [#16158](https://github.com/nodejs/node/pull/16158)
- [[`96e82509b0`](https://github.com/nodejs/node/commit/96e82509b0)] - **lib**: use destructuring for some constants (Weijia Wang) [#16063](https://github.com/nodejs/node/pull/16063)
- [[`6be494251b`](https://github.com/nodejs/node/commit/6be494251b)] - **lib**: move duplicate spliceOne into internal/util (Weijia Wang) [#16221](https://github.com/nodejs/node/pull/16221)
- [[`8c0c456c73`](https://github.com/nodejs/node/commit/8c0c456c73)] - **lib**: setup IPC channel before console (Nikolai Vavilov) [#16562](https://github.com/nodejs/node/pull/16562)
- [[`3a230b42f3`](https://github.com/nodejs/node/commit/3a230b42f3)] - **lib**: internal/errors should not be executable (Jon Moss) [#16369](https://github.com/nodejs/node/pull/16369)
- [[`415fb56735`](https://github.com/nodejs/node/commit/415fb56735)] - **module**: fix extension lookups for top-level main (Guy Bedford) [#16526](https://github.com/nodejs/node/pull/16526)
- [[`e68307737c`](https://github.com/nodejs/node/commit/e68307737c)] - **module**: fix hook module CJS dependency loading (guybedford) [#16381](https://github.com/nodejs/node/pull/16381)
- [[`ac02a0be28`](https://github.com/nodejs/node/commit/ac02a0be28)] - **(SEMVER-MINOR)** **module**: support custom paths to require.resolve() (cjihrig) [#16397](https://github.com/nodejs/node/pull/16397)
- [[`e578268ef9`](https://github.com/nodejs/node/commit/e578268ef9)] - **src**: add `InternalCallbackScope` util constructor (Anna Henningsen) [#16461](https://github.com/nodejs/node/pull/16461)
- [[`6ac7eefe41`](https://github.com/nodejs/node/commit/6ac7eefe41)] - **src**: move handle properties to prototype (Ben Noordhuis) [#16482](https://github.com/nodejs/node/pull/16482)
- [[`3a54bb5c2c`](https://github.com/nodejs/node/commit/3a54bb5c2c)] - **src**: remove superfluous HandleScope (Ben Noordhuis) [#16482](https://github.com/nodejs/node/pull/16482)
- [[`77c02aab5b`](https://github.com/nodejs/node/commit/77c02aab5b)] - **src**: use uv_stream_t, not uv_stream_s (Ben Noordhuis) [#16512](https://github.com/nodejs/node/pull/16512)
- [[`a194d831fe`](https://github.com/nodejs/node/commit/a194d831fe)] - **src**: use maybe versions of methods (Tom Boutell) [#16005](https://github.com/nodejs/node/pull/16005)
- [[`ec5168813b`](https://github.com/nodejs/node/commit/ec5168813b)] - **src**: use V8 function to get Module Namespace (Bradley Farias) [#16261](https://github.com/nodejs/node/pull/16261)
- [[`0a5a2c43b1`](https://github.com/nodejs/node/commit/0a5a2c43b1)] - **src**: fix vm module for strict mode (Franziska Hinkelmann) [#16487](https://github.com/nodejs/node/pull/16487)
- [[`ee40368c87`](https://github.com/nodejs/node/commit/ee40368c87)] - **src**: make header file self-contained (Joyee Cheung) [#16518](https://github.com/nodejs/node/pull/16518)
- [[`465540cc98`](https://github.com/nodejs/node/commit/465540cc98)] - **src**: destroy inspector agent before context (Ali Ijaz Sheikh) [#16472](https://github.com/nodejs/node/pull/16472)
- [[`9bca6200ad`](https://github.com/nodejs/node/commit/9bca6200ad)] - **src**: remove empty comment in node_http2.h (Daniel Bevenius) [#16400](https://github.com/nodejs/node/pull/16400)
- [[`f0576c5ee0`](https://github.com/nodejs/node/commit/f0576c5ee0)] - **src**: remove unused include in tty_wrap.h (Daniel Bevenius) [#16379](https://github.com/nodejs/node/pull/16379)
- [[`f00ba6b142`](https://github.com/nodejs/node/commit/f00ba6b142)] - **src**: fix http2 debug build errors (Daniel Bevenius) [#16432](https://github.com/nodejs/node/pull/16432)
- [[`889f42a924`](https://github.com/nodejs/node/commit/889f42a924)] - **test**: remove empty comments in http2 tests (Gibson Fahnestock) [#16631](https://github.com/nodejs/node/pull/16631)
- [[`a62845565e`](https://github.com/nodejs/node/commit/a62845565e)] - **test**: add block scoping to test-assert-deep (Rich Trott) [#16532](https://github.com/nodejs/node/pull/16532)
- [[`0510f42efc`](https://github.com/nodejs/node/commit/0510f42efc)] - **test**: update test-timers-block-eventloop.js (zhangzifa) [#16314](https://github.com/nodejs/node/pull/16314)
- [[`bac2aff23d`](https://github.com/nodejs/node/commit/bac2aff23d)] - **test**: include actual value in assertion message (Matthew Cantelon) [#15935](https://github.com/nodejs/node/pull/15935)
- [[`d185bfdcef`](https://github.com/nodejs/node/commit/d185bfdcef)] - **test**: replace fixturesDir in test-tls-connect (Casie Lynch) [#15849](https://github.com/nodejs/node/pull/15849)
- [[`ce07cbeacb`](https://github.com/nodejs/node/commit/ce07cbeacb)] - **test**: use fixtures module (Iryna Yaremtso) [#15901](https://github.com/nodejs/node/pull/15901)
- [[`2e1b45db07`](https://github.com/nodejs/node/commit/2e1b45db07)] - **test**: add details in assertions in test-vm-context (Vladimir Ilic) [#16116](https://github.com/nodejs/node/pull/16116)
- [[`4f47e2df44`](https://github.com/nodejs/node/commit/4f47e2df44)] - **test**: increase fs.exists coverage (Nigel Kibodeaux) [#15963](https://github.com/nodejs/node/pull/15963)
- [[`01058c412e`](https://github.com/nodejs/node/commit/01058c412e)] - **test**: use fixtures module in test-fs-realpath.js (Raphael Rheault) [#15904](https://github.com/nodejs/node/pull/15904)
- [[`fe9cca2e0f`](https://github.com/nodejs/node/commit/fe9cca2e0f)] - **test**: use fixtures module (Scott J Beck) [#15843](https://github.com/nodejs/node/pull/15843)
- [[`b4af92280b`](https://github.com/nodejs/node/commit/b4af92280b)] - **test**: imporove assert messages (Hadis-Fard) [#16021](https://github.com/nodejs/node/pull/16021)
- [[`604ab12447`](https://github.com/nodejs/node/commit/604ab12447)] - **test**: show values instead of assertion message (Cheyenne Arrowsmith) [#15979](https://github.com/nodejs/node/pull/15979)
- [[`aea09da6f9`](https://github.com/nodejs/node/commit/aea09da6f9)] - **test**: include values in assertion messages (nhoel) [#15996](https://github.com/nodejs/node/pull/15996)
- [[`ea32d0ec4e`](https://github.com/nodejs/node/commit/ea32d0ec4e)] - **test**: use process.features.debug in common module (Rich Trott) [#16537](https://github.com/nodejs/node/pull/16537)
- [[`a0fcd4d219`](https://github.com/nodejs/node/commit/a0fcd4d219)] - **test**: use common.buildType in repl-domain-abort (Rich Trott) [#16538](https://github.com/nodejs/node/pull/16538)
- [[`525700b3d5`](https://github.com/nodejs/node/commit/525700b3d5)] - **test**: skip test-process-config if no config.gypi (Gibson Fahnestock) [#16436](https://github.com/nodejs/node/pull/16436)
- [[`aaacddbcbf`](https://github.com/nodejs/node/commit/aaacddbcbf)] - **test**: use fixtures module in tls-handshake-error (Mark Walker) [#15939](https://github.com/nodejs/node/pull/15939)
- [[`977fe22290`](https://github.com/nodejs/node/commit/977fe22290)] - **test**: add failing vm tests to known_issues (Michaël Zasso) [#16410](https://github.com/nodejs/node/pull/16410)
- [[`02ac8bae91`](https://github.com/nodejs/node/commit/02ac8bae91)] - **test**: change tmp directories prefix (Refael Ackermann) [#16372](https://github.com/nodejs/node/pull/16372)
- [[`568d614dc3`](https://github.com/nodejs/node/commit/568d614dc3)] - **test**: allow tests to pass without internet (Daniel Bevenius) [#16255](https://github.com/nodejs/node/pull/16255)
- [[`3d8a7e97fe`](https://github.com/nodejs/node/commit/3d8a7e97fe)] - **tools**: modernize license2rtf (Tobias Nießen) [#16220](https://github.com/nodejs/node/pull/16220)
- [[`d5891b5330`](https://github.com/nodejs/node/commit/d5891b5330)] - **tools**: replace loop with padStart (Tobias Nießen) [#16220](https://github.com/nodejs/node/pull/16220)
- [[`b1e2a38d90`](https://github.com/nodejs/node/commit/b1e2a38d90)] - **tools**: fix cpplint.py when path contains non-ascii (sharkfisher) [#16047](https://github.com/nodejs/node/pull/16047)
- [[`e80d878b3f`](https://github.com/nodejs/node/commit/e80d878b3f)] - **tools**: no trailing slash in ignore patterns (Refael Ackermann) [#16372](https://github.com/nodejs/node/pull/16372)
- [[`31f54e5fb9`](https://github.com/nodejs/node/commit/31f54e5fb9)] - **tools**: add make lint-md-clean (Daijiro Wachi) [#12756](https://github.com/nodejs/node/pull/12756)
- [[`ea415a663e`](https://github.com/nodejs/node/commit/ea415a663e)] - **tools**: add lint-md command in Makefile (Daijiro Wachi) [#12756](https://github.com/nodejs/node/pull/12756)
- [[`3522e765be`](https://github.com/nodejs/node/commit/3522e765be)] - **tools**: use remark-preset-lint-node in .remarkrc (Daijiro Wachi) [#12756](https://github.com/nodejs/node/pull/12756)
- [[`8d62116cf0`](https://github.com/nodejs/node/commit/8d62116cf0)] - **tools**: introduce remark-preset-lint-node (Daijiro Wachi) [#12756](https://github.com/nodejs/node/pull/12756)
- [[`3d499b3712`](https://github.com/nodejs/node/commit/3d499b3712)] - **tools**: introduce remark-cli@3.0.1 (Daijiro Wachi) [#12756](https://github.com/nodejs/node/pull/12756)
- [[`55ba1d4115`](https://github.com/nodejs/node/commit/55ba1d4115)] - **util**: expand test coverage for util.deprecate (Ashish Kaila) [#16305](https://github.com/nodejs/node/pull/16305)
- [[`8fd75fb9b5`](https://github.com/nodejs/node/commit/8fd75fb9b5)] - **(SEMVER-MINOR)** **util**: graduate TextEncoder/TextDecoder, tests (James M Snell) [#15743](https://github.com/nodejs/node/pull/15743)

Windows 32-bit Installer: https://nodejs.org/dist/v8.9.0/node-v8.9.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v8.9.0/node-v8.9.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v8.9.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v8.9.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v8.9.0/node-v8.9.0.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v8.9.0/node-v8.9.0-darwin-x64.tar.gz \
Linux 32-bit Binary: https://nodejs.org/dist/v8.9.0/node-v8.9.0-linux-x86.tar.xz \
Linux 64-bit Binary: https://nodejs.org/dist/v8.9.0/node-v8.9.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v8.9.0/node-v8.9.0-linux-ppc64le.tar.xz \
Linux PPC BE 64-bit Binary: https://nodejs.org/dist/v8.9.0/node-v8.9.0-linux-ppc64.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v8.9.0/node-v8.9.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v8.9.0/node-v8.9.0-aix-ppc64.tar.gz \
SmartOS 32-bit Binary: https://nodejs.org/dist/v8.9.0/node-v8.9.0-sunos-x86.tar.xz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v8.9.0/node-v8.9.0-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v8.9.0/node-v8.9.0-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v8.9.0/node-v8.9.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v8.9.0/node-v8.9.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v8.9.0/node-v8.9.0.tar.gz \
Other release files: https://nodejs.org/dist/v8.9.0/ \
Documentation: https://nodejs.org/docs/v8.9.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

7c0d705d29130357bef83ce062031dd2d8e3756deee444865ec23f8e5bf83635  node-v8.9.0-aix-ppc64.tar.gz
aaf165348bc6d20012b048a88a8f3a35cba6799496e8f4c1246d85c524a84dbc  node-v8.9.0-darwin-x64.tar.gz
9c38c3778f60532cec93a49330a945fcf10b252ffbf1b842b4a9b1d64a99d3cd  node-v8.9.0-darwin-x64.tar.xz
04ebc71075df3b2cf34af9f00c7ecace7d11c5316c165b2f1e2e64d02c564ed1  node-v8.9.0-headers.tar.gz
c36655b5594dd85932bb7c8f7fd55ac1eb9ffe5ab112a1cc61cfc85c9b4a013c  node-v8.9.0-headers.tar.xz
468af2d1936cc9daca02949774680a0d1fd24b6169561598bae71a0bc90c5c3d  node-v8.9.0-linux-arm64.tar.gz
30cb00ac1cf6b466b1f27e7ce41363a67a66dbb64227c2dc5e33d221b09fc579  node-v8.9.0-linux-arm64.tar.xz
139ddc93a2777ec105b2b8989157ab348dfe8cdf537f249b34e38b36f3d311bd  node-v8.9.0-linux-armv6l.tar.gz
87ed863762b394d77b9cac36a444b655bda3b69d808aca126f862cfaccd22fc3  node-v8.9.0-linux-armv6l.tar.xz
8a80e010b801a1f105828c3cd01636cf5ebf39669b9120138672f43e63023e85  node-v8.9.0-linux-armv7l.tar.gz
36edb836120a68ab9a660e869e5ca3073f5cee880621d9ea4233d671632c33f5  node-v8.9.0-linux-armv7l.tar.xz
969617525970e8eed07a86925fd8cded2dde54ca0f880889934806ed6a0256ee  node-v8.9.0-linux-ppc64le.tar.gz
b510def21209dd49ca7aa01bb7e9bc2a6e0038889161d424079a60ecc14d8a94  node-v8.9.0-linux-ppc64le.tar.xz
188acc64afdd371a2106e29e654c3bca40d738a279497a6d1d9ca1dbc2105512  node-v8.9.0-linux-ppc64.tar.gz
6b8f479b3df571457836cd2c0cbced824ad40109b7553aef3daebc8562d7307c  node-v8.9.0-linux-ppc64.tar.xz
440ca9936c9852d9f8a20c53f00058619af8656d8fe95f41f229e0eff7c19763  node-v8.9.0-linux-s390x.tar.gz
30df7e252e9030bf4575d39774fd9a24d7418814ef4025d0b18f1034d5484cb0  node-v8.9.0-linux-s390x.tar.xz
34b544cdda86bcc201568822fd20c1eaf8dadc53227f928cbfc45865677db7f6  node-v8.9.0-linux-x64.tar.gz
e92b91fa473f9ad805a1241907b6f1bd3f8ceac8426a8b4cb05428e62e243bdd  node-v8.9.0-linux-x64.tar.xz
b4dfc4d93808280b1bb9946a89c1cca5dd242d68bd774994d7fb1de6bd91958b  node-v8.9.0-linux-x86.tar.gz
7bd958d48222b04f65fbb109334b75bec927ada0a223b6aa64c44c3e5d6d6d2b  node-v8.9.0-linux-x86.tar.xz
f5d47ba3acc7de5d8449e5e07d077ae77d9590e0197c6a6dedfdab75ba0c7a82  node-v8.9.0.pkg
d3eb1fd387bc33301307c5555d63bf20be9ce7dcafa3faaa6baddb4d7602c5f0  node-v8.9.0-sunos-x64.tar.gz
514171bec3b906a88bbf3b94582ad09553f3a56e83b0f5252bb0237b8261ba8a  node-v8.9.0-sunos-x64.tar.xz
a50a3379a4264eee23d4ff780c2dfe81aa8f98b2041c128c736a99af7f5bd617  node-v8.9.0-sunos-x86.tar.gz
0b51be6dc86c364acdfc9e4c3f83d00c86fd82d7a0e7036fa5c92cbca24d486d  node-v8.9.0-sunos-x86.tar.xz
00b422827f37913576f8e5059c84acab364375cfbfcc083652191165f709de6c  node-v8.9.0.tar.gz
ae8258f89e127a76d4b4aff6fdb8dc395b7da0069cba054b913dfc36b3c91189  node-v8.9.0.tar.xz
46ce25a96592b3eec86093f44c4031a701c8678a4d62f6ea74378d1bfec26975  node-v8.9.0-win-x64.7z
dd971e43ff003213b0be31c1a8ce3421f72e0db2a703bc254ac685be4f7f609e  node-v8.9.0-win-x64.zip
b903cdfa53421582685e84766de6beaa5d1e82caf6e197e4809a96c8bef31c4d  node-v8.9.0-win-x86.7z
687970cdda845ed1128f8c8533863bd470e10d8f1f4540a7ec1d85184cebfff9  node-v8.9.0-win-x86.zip
cd37ff1fb455f7e6e6fe566cffcea06bbde392501fd7b5be5ec4174b762af523  node-v8.9.0-x64.msi
6b33268464ebbd60c6c8b271cbbbe4f52d87edde97df5b165c1bcc1339d70045  node-v8.9.0-x86.msi
6e63ec4c7faca6141617eecd5076cbb38d82fe05711a066f928f0326bef57e70  win-x64/node.exe
45b91a9be65ae631bf4bebecfaaca5a0329f366b924d75ad5c67a88f38ddfd7d  win-x64/node.lib
84629ab3ee01ca25ceb06806ed15160a026820d7abf8d63f3c813ebff9f8a864  win-x64/node_pdb.7z
2408b6ca0fcb51857d2547bd92c845ff5e7b9481af954805a787255541f7c21e  win-x64/node_pdb.zip
a4edbd1b645b515889af267f4b509d66526ff88f3c7b1bc8123979b4d4087a7f  win-x86/node.exe
c04a8ac2661eed2bc31b9e2ae14965848c989d2dda5240980ffa52f3ee25502b  win-x86/node.lib
f7b7b32ed6eb8f7b0f6956c71228b39848f69638ecf2a38bb8b65855e57fce6a  win-x86/node_pdb.7z
45645f74e83258959bdc3b8b0e24072760d73354f3a9377f987e1d776be2d5f4  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----
Version: GnuPG v2

iQIcBAEBCAAGBQJZ+gVkAAoJELAfu5KCHFh6LtgP+wT/bE4PIEtZNc6h3TIHNDeA
qUV1pAz3xdvScfZra51G6f9HnRRqqDzLTUOZZ2pwgM+d2U2m8zkPO7zAIhPYf596
lH8JV4sJCusWxVcAAKq0b9dUvFUwXyZIZHqJMwH90PpgFbdeDJ3liVJfR3lFRd+D
Gtw572wHybgg5YtVbScVGb2fOm8ICNVkJgnZtZJwz5lHOzgQQJpPMKj3B+kSih1w
EjNdp9C/HviZWM0IsH7xWrgWMC7HBmqZRA0yw5qn3AGf4QxTd7SewT7DnPwGFviy
yVLj7PIXkmAwRd8Exrw3wHMk9vwQDCFT7r/u/Mdl4/XUNqSneDrdT7Pe6nSSJOfP
1Wrkd15zxhaxaNnx0HAHpr2EpAviaI7Q6/8tF3axsKxhQ6RfYkxLvye4JFdZH1sQ
fIA7kW41C05LoGInGyIRoBLFLLxsqVw5setj6qw0oP9kHy45g0jr3Jv+rdBaEVIA
aTdsviJ/ah7NSrpzJWElein5r5dmTe+OZXRX4nU2XU/UHTMAo3Q281oEubnREYlG
O7VmJim8R2ufyT1cfCPl+sPA53Dtqe/FhF68A4DvuV728x8W+Bn6u3Je8yPOj0SX
BnJZVhgXgoVd1m8oSA4pctyXga/eAd6xSTQTQk7/S0BW47QxSDbuu5gRDbUrcZC/
WJYTKzyHASR87jfdrCft
=ED8p
-----END PGP SIGNATURE-----

```
