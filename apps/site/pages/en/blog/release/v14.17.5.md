---
date: '2021-08-11T16:41:40.767Z'
category: release
title: Node v14.17.5 (LTS)
layout: blog-post
author: <PERSON>
---

### Notable Changes

- **CVE-2021-3672/CVE-2021-22931**: Improper handling of untypical characters in domain names (High)
  - Node.js was vulnerable to Remote Code Execution, XSS, application crashes due to missing input validation of hostnames returned by Domain Name Servers in the Node.js DNS library which can lead to the output of wrong hostnames (leading to Domain Hijacking) and injection vulnerabilities in applications using the library. You can read more about it at https://nvd.nist.gov/vuln/detail/CVE-2021-22931.
- **CVE-2021-22940**: Use after free on close http2 on stream canceling (High)
  - Node.js was vulnerable to a use after free attack where an attacker might be able to exploit memory corruption to change process behavior. This release includes a follow-up fix for CVE-2021-22930 as the issue was not completely resolved by the previous fix. You can read more about it at https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2021-22940.
- **CVE-2021-22939**: Incomplete validation of rejectUnauthorized parameter (Low)
  - If the Node.js HTTPS API was used incorrectly and "undefined" was in passed for the "rejectUnauthorized" parameter, no error was returned and connections to servers with an expired certificate would have been accepted. You can read more about it at https://nvd.nist.gov/vuln/detail/CVE-2021-22939.

### Commits

- [[`4923b59e0b`](https://github.com/nodejs/node/commit/4923b59e0b)] - **deps**: update c-ares to 1.17.2 (Beth Griggs) [#39724](https://github.com/nodejs/node/pull/39724)
- [[`847a4c6a8a`](https://github.com/nodejs/node/commit/847a4c6a8a)] - **deps**: reflect c-ares source tree (Beth Griggs) [#39653](https://github.com/nodejs/node/pull/39653)
- [[`33208e2f89`](https://github.com/nodejs/node/commit/33208e2f89)] - **deps**: apply missed updates from c-ares 1.17.1 (Beth Griggs) [#39653](https://github.com/nodejs/node/pull/39653)
- [[`af5c1af9a4`](https://github.com/nodejs/node/commit/af5c1af9a4)] - **http2**: add tests for cancel event while client is paused reading (Akshay K) [#39622](https://github.com/nodejs/node/pull/39622)
- [[`434872e838`](https://github.com/nodejs/node/commit/434872e838)] - **http2**: update handling of rst_stream with error code NGHTTP2_CANCEL (Akshay K) [#39622](https://github.com/nodejs/node/pull/39622)
- [[`35b86110e4`](https://github.com/nodejs/node/commit/35b86110e4)] - **tls**: validate "rejectUnauthorized: undefined" (Matteo Collina) [nodejs-private/node-private#276](https://github.com/nodejs-private/node-private/pull/276)

Windows 32-bit Installer: https://nodejs.org/dist/v14.17.5/node-v14.17.5-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v14.17.5/node-v14.17.5-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v14.17.5/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v14.17.5/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v14.17.5/node-v14.17.5.pkg \
macOS Intel 64-bit Binary: https://nodejs.org/dist/v14.17.5/node-v14.17.5-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v14.17.5/node-v14.17.5-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v14.17.5/node-v14.17.5-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v14.17.5/node-v14.17.5-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v14.17.5/node-v14.17.5-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v14.17.5/node-v14.17.5-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v14.17.5/node-v14.17.5-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v14.17.5/node-v14.17.5.tar.gz \
Other release files: https://nodejs.org/dist/v14.17.5/ \
Documentation: https://nodejs.org/docs/v14.17.5/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

9085e04afb5ac85f70d9ac1080a486022ad14a2e36c0f668cb8a8c23e7f413cf  node-v14.17.5-aix-ppc64.tar.gz
2e40ab625b45b9bdfcb963ddd4d65d87ddf1dd37a86b6f8b075cf3d77fe9dc09  node-v14.17.5-darwin-x64.tar.gz
6e1d9e6091b14f5accb03dad7617492442dad83778cc34de45f1818a75b6368b  node-v14.17.5-darwin-x64.tar.xz
c8e264312804785020b68ad7afe1c41f3bf2a6aea4bba62f9b8e825cadfdfe49  node-v14.17.5-headers.tar.gz
cc70abea8fdd52c82b45dd9187ee555f6bbe931b33739aa7d8a3c5f25a9f5cac  node-v14.17.5-headers.tar.xz
bee6d7fb5dbdd2931e688b33defd449afdfd9cd6e716975864406cda18daca66  node-v14.17.5-linux-arm64.tar.gz
3a2e674b6db50dfde767c427e8f077235bbf6f9236e1b12a4cc3496b12f94bae  node-v14.17.5-linux-arm64.tar.xz
c79cc802f6034e9a9583ccbf8ccd17a0d8e0942c136011c0f4c3a475d46614be  node-v14.17.5-linux-armv7l.tar.gz
496f7bf14e360026421db1b803575c4f1d3614646ecdaca98d607553579f8904  node-v14.17.5-linux-armv7l.tar.xz
bbcd0142e65614670ac88017290a1b875bfecbe46b35d1a330360b8f2679b85e  node-v14.17.5-linux-ppc64le.tar.gz
29f2e1f0e1c13ceb8225bfbfb6ab0a236398eca5ceff602c4f3bc1e7eff88927  node-v14.17.5-linux-ppc64le.tar.xz
469d5a39a81baba588768404b58eb369c14a1667fcb0569c2247461476b6cc0e  node-v14.17.5-linux-s390x.tar.gz
7d40eee3d54241403db12fb3bc420cd776e2b02e89100c45cf5e74a73942e7f6  node-v14.17.5-linux-s390x.tar.xz
dc04c7e60235ff73536ba0d9e50638090f60cacabfd83184082dce3b330afc6e  node-v14.17.5-linux-x64.tar.gz
2d759de07a50cd7f75bd73d67e97b0d0e095ee3c413efac7d1b3d1e84ed76fff  node-v14.17.5-linux-x64.tar.xz
508dca638ee5807601bbaae72307d2413b16cf7f73f92a79b8b070e4c5a83c0b  node-v14.17.5.pkg
bd1bb74eae48c7aa7d4519736385e99477d954c6915adefdc3c373be461d1aaa  node-v14.17.5.tar.gz
42b1a7ff87580c6058063e943d7d53efa8c236145e6e9c8264ee425b40911fa8  node-v14.17.5.tar.xz
f6e651a76e0158e07ac12753c40739554455f42eec355a651242a28ed979e483  node-v14.17.5-win-x64.7z
a99b7ee08e846e5d1f4e70c4396265542819d79ed9cebcc27760b89571f03cbf  node-v14.17.5-win-x64.zip
2f2933bfb42d407b064f83c6047f71b75c55389b4bebf82b7ff5a97f4635ce54  node-v14.17.5-win-x86.7z
122ff403f4fadf9997ff2ceac5e07b6b0efd23294da369a1cf907ec98bf69db7  node-v14.17.5-win-x86.zip
f40926f33c4517a64978050fb8e88110d279a3a15adc962de4002f65ed34e821  node-v14.17.5-x64.msi
53763a06ff498d9deabff2cc6f4041d0a3c3f38f940acb6c8a8339df8d8b3827  node-v14.17.5-x86.msi
a6f14eab020e5b96214b9e08c1add28787526bfc47fa6f449c0e8059908bcb75  win-x64/node.exe
859ceb82ba9af9df5831bb67f45427bfb774aae22e7c0ee52623a3196ec0e1eb  win-x64/node.lib
9ccd696aa53573b3882524cf75bcf50ce0e04aca46ac85c11f2705920df9d38b  win-x64/node_pdb.7z
113e591eb4c433733ac5ad78764698f230877d441ae28d62faf90a48c7698630  win-x64/node_pdb.zip
23e8f9db8908051153b305b8d1b474d75d6503a1208604d19c39d15799d097c4  win-x86/node.exe
1078be47b9315c81aa3bb989c4bba8ee23e0da9e4854a44006decf45d578833e  win-x86/node.lib
facb6feaab3b11bac3b3e571898ebdc9ad0e0049a4d0b14cce0930eb3d8437ac  win-x86/node_pdb.7z
795e518e063abdda5866ecf5b4c29683115a6ef83bffb401d4066ada2067d144  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQEzBAEBCAAdFiEETtd49TnjY0x3nIfG1wYoSKGrAFwFAmET+1QACgkQ1wYoSKGr
AFwnXwf/ZTHqyRLj/EzS06OGDdwFOaqY9tU9Cgbqxns98pKV1BbhDAvDKoxzzo+H
9At0g/Sz4t3/p6ElCoh6hhL2DxRHejkktq39ofupz2NP3Ukc/ABomYyPz/EPVJjL
P/ZCJYNB1mwjRDhRpjcAXT3bF53T0+O1rx6T9Xexnp3K3B47dYB+0JSYx7Jh7/0O
JKhBIVOoNXGodNCyW7QLnj4ETg2TywGe2cil2zYeUTO/EOQaWzOxbcI7FFhoBLXw
MRlcOhrmjqJ94rriI9EQzEzIuqvcbnBd/rYe0cWcKfIdjAo+Iqz3p3HD3kzACBML
8C8A/5gTD2hEXplT2mjwUlFK3t8qTg==
=yMp/
-----END PGP SIGNATURE-----

```
