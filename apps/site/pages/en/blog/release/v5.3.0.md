---
date: '2015-12-16T20:23:07.363Z'
category: release
title: Node v5.3.0 (Current)
layout: blog-post
author: <PERSON>
---

### Notable changes

- **buffer**:
  - `Buffer.prototype.includes()` has been added to keep parity with TypedArrays. (<PERSON>) [#3567](https://github.com/nodejs/node/pull/3567).
- **domains**:
  - Fix handling of uncaught exceptions. (<PERSON>) [#3654](https://github.com/nodejs/node/pull/3654).
- **https**:
  - Added support for disabling session caching. (<PERSON><PERSON>) [#4252](https://github.com/nodejs/node/pull/4252).
- **repl**:
  - Allow third party modules to be imported using `require()`. This corrects a regression from 5.2.0. (<PERSON>) [#4215](https://github.com/nodejs/node/pull/4215).
- **deps**:
  - Upgrade libuv to 1.8.0. (<PERSON><PERSON>) [#4276](https://github.com/nodejs/node/pull/4276).

### Known issues

- Surrogate pair in REPL can freeze terminal. [#690](https://github.com/nodejs/node/issues/690)
- Calling `dns.setServers()` while a DNS query is in progress can cause the process to crash on a failed assertion. [#894](https://github.com/nodejs/node/issues/894)
- `url.resolve` may transfer the auth portion of the url when resolving between two full hosts, see [#1435](https://github.com/nodejs/node/issues/1435).
- Unicode characters in filesystem paths are not handled consistently across platforms or Node.js APIs. See [#2088](https://github.com/nodejs/node/issues/2088), [#3401](https://github.com/nodejs/node/issues/3401) and [#3519](https://github.com/nodejs/node/issues/3519).

### Commits

- [[`6ca5ea3860`](https://github.com/nodejs/node/commit/6ca5ea3860)] - 2015-12-09, Version 5.2.0 (Current) (Rod Vagg) [#4181](https://github.com/nodejs/node/pull/4181)
- [[`da5cdc2207`](https://github.com/nodejs/node/commit/da5cdc2207)] - **assert**: accommodate ES6 classes that extend Error (Rich Trott) [#4166](https://github.com/nodejs/node/pull/4166)
- [[`67e181986a`](https://github.com/nodejs/node/commit/67e181986a)] - **(SEMVER-MINOR)** **buffer**: add includes() for parity with TypedArray (Alexander Martin) [#3567](https://github.com/nodejs/node/pull/3567)
- [[`84dea1bd0c`](https://github.com/nodejs/node/commit/84dea1bd0c)] - **configure**: fix arm vfpv2 (Jörg Krause) [#4203](https://github.com/nodejs/node/pull/4203)
- [[`a7f5dfd14c`](https://github.com/nodejs/node/commit/a7f5dfd14c)] - **configure**: use \_\_ARM_ARCH to determine arm version (João Reis) [#4123](https://github.com/nodejs/node/pull/4123)
- [[`0e3912be0b`](https://github.com/nodejs/node/commit/0e3912be0b)] - **configure**: respect CC_host in host arch detection (João Reis) [#4117](https://github.com/nodejs/node/pull/4117)
- [[`69b94ec55c`](https://github.com/nodejs/node/commit/69b94ec55c)] - **deps**: upgrade libuv to 1.8.0 (Saúl Ibarra Corretgé) [#4276](https://github.com/nodejs/node/pull/4276)
- [[`a8854e5b59`](https://github.com/nodejs/node/commit/a8854e5b59)] - **doc**: document the cache parameter for fs.realpathSync (Jackson Tian) [#4285](https://github.com/nodejs/node/pull/4285)
- [[`9e1b7aa874`](https://github.com/nodejs/node/commit/9e1b7aa874)] - **doc**: document backlog for server.listen() variants (Jan Schär) [#4025](https://github.com/nodejs/node/pull/4025)
- [[`435d571f22`](https://github.com/nodejs/node/commit/435d571f22)] - **doc**: update AUTHORS list (Rod Vagg) [#4183](https://github.com/nodejs/node/pull/4183)
- [[`3b3061365a`](https://github.com/nodejs/node/commit/3b3061365a)] - **doc**: update irc channels: #node.js and #node-dev (Nelson Pecora) [#2743](https://github.com/nodejs/node/pull/2743)
- [[`9538fd02e5`](https://github.com/nodejs/node/commit/9538fd02e5)] - **doc**: clarify error events in HTTP module documentation (Lenny Markus) [#4275](https://github.com/nodejs/node/pull/4275)
- [[`c6efd535e4`](https://github.com/nodejs/node/commit/c6efd535e4)] - **doc**: fix improper http.get sample code (Hideki Yamamura) [#4263](https://github.com/nodejs/node/pull/4263)
- [[`498c9adb08`](https://github.com/nodejs/node/commit/498c9adb08)] - **doc**: add CTC meeting minutes 2015-10-28 (Rod Vagg) [#3661](https://github.com/nodejs/node/pull/3661)
- [[`671347cf13`](https://github.com/nodejs/node/commit/671347cf13)] - **doc**: fixup socket.remoteAddress (Arthur Gautier) [#4198](https://github.com/nodejs/node/pull/4198)
- [[`f050cab3d8`](https://github.com/nodejs/node/commit/f050cab3d8)] - **doc**: copyedit console doc (Rich Trott) [#4225](https://github.com/nodejs/node/pull/4225)
- [[`1a21a5368b`](https://github.com/nodejs/node/commit/1a21a5368b)] - **doc**: move description of 'equals' method to right place (janriemer) [#4227](https://github.com/nodejs/node/pull/4227)
- [[`9a9c5259bf`](https://github.com/nodejs/node/commit/9a9c5259bf)] - **doc**: Fixing broken links to the v8 wiki (Tom Gallacher) [#4241](https://github.com/nodejs/node/pull/4241)
- [[`37ed05b8c1`](https://github.com/nodejs/node/commit/37ed05b8c1)] - **doc**: copyedit child_process doc (Rich Trott) [#4188](https://github.com/nodejs/node/pull/4188)
- [[`e47ae5808b`](https://github.com/nodejs/node/commit/e47ae5808b)] - **doc**: copyedit buffer doc (Rich Trott) [#4187](https://github.com/nodejs/node/pull/4187)
- [[`70fb06a90b`](https://github.com/nodejs/node/commit/70fb06a90b)] - **doc**: clarify assert.fail doc (Rich Trott) [#4186](https://github.com/nodejs/node/pull/4186)
- [[`e3187cc81e`](https://github.com/nodejs/node/commit/e3187cc81e)] - **doc**: copyedit addons doc (Rich Trott) [#4185](https://github.com/nodejs/node/pull/4185)
- [[`931ab967ff`](https://github.com/nodejs/node/commit/931ab967ff)] - **doc**: add calvinmetcalf to collaborators (Calvin Metcalf) [#4218](https://github.com/nodejs/node/pull/4218)
- [[`01ce23148b`](https://github.com/nodejs/node/commit/01ce23148b)] - **doc**: add mcollina to collaborators (Matteo Collina) [#4220](https://github.com/nodejs/node/pull/4220)
- [[`bd8753aabf`](https://github.com/nodejs/node/commit/bd8753aabf)] - **doc**: add rmg to collaborators (Ryan Graham) [#4219](https://github.com/nodejs/node/pull/4219)
- [[`73a9a6fc92`](https://github.com/nodejs/node/commit/73a9a6fc92)] - **doc**: harmonize description of `ca` argument (Ben Noordhuis) [#4213](https://github.com/nodejs/node/pull/4213)
- [[`dfc8bedbc5`](https://github.com/nodejs/node/commit/dfc8bedbc5)] - **doc**: change references from node to Node.js (Roman Klauke) [#4177](https://github.com/nodejs/node/pull/4177)
- [[`7a518788e9`](https://github.com/nodejs/node/commit/7a518788e9)] - **doc, test**: symbols as event names (Bryan English) [#4151](https://github.com/nodejs/node/pull/4151)
- [[`425a3545d2`](https://github.com/nodejs/node/commit/425a3545d2)] - **(SEMVER-MINOR)** **domains**: fix handling of uncaught exceptions (Julien Gilli) [#3654](https://github.com/nodejs/node/pull/3654)
- [[`acef181fde`](https://github.com/nodejs/node/commit/acef181fde)] - **(SEMVER-MINOR)** **https**: support disabling session caching (Fedor Indutny) [#4252](https://github.com/nodejs/node/pull/4252)
- [[`2a60e2ad71`](https://github.com/nodejs/node/commit/2a60e2ad71)] - **module,src**: do not wrap modules with -1 lineOffset (cjihrig) [#4298](https://github.com/nodejs/node/pull/4298)
- [[`d3c498b1b7`](https://github.com/nodejs/node/commit/d3c498b1b7)] - **node**: remove unused variables in AppendExceptionLine (Yazhong Liu) [#4264](https://github.com/nodejs/node/pull/4264)
- [[`aad6b9f0eb`](https://github.com/nodejs/node/commit/aad6b9f0eb)] - **repl**: display error message when loading directory (Prince J Wesley) [#4170](https://github.com/nodejs/node/pull/4170)
- [[`213ede6cee`](https://github.com/nodejs/node/commit/213ede6cee)] - **repl**: fix require('3rdparty') regression (Ben Noordhuis) [#4215](https://github.com/nodejs/node/pull/4215)
- [[`f176b31e74`](https://github.com/nodejs/node/commit/f176b31e74)] - **src**: remove \_\_builtin_bswap16 call (Ben Noordhuis) [#4290](https://github.com/nodejs/node/pull/4290)
- [[`ce2471673f`](https://github.com/nodejs/node/commit/ce2471673f)] - **src**: remove unused BITS_PER_LONG macro (Ben Noordhuis) [#4290](https://github.com/nodejs/node/pull/4290)
- [[`b799a74709`](https://github.com/nodejs/node/commit/b799a74709)] - **src**: fix line numbers on core errors (cjihrig) [#4254](https://github.com/nodejs/node/pull/4254)
- [[`c311b61430`](https://github.com/nodejs/node/commit/c311b61430)] - **src**: fix deprecation message for ErrnoException (Martin von Gagern) [#4269](https://github.com/nodejs/node/pull/4269)
- [[`2859f9ef92`](https://github.com/nodejs/node/commit/2859f9ef92)] - **test**: fix debug-port-cluster flakiness (Ben Noordhuis) [#4310](https://github.com/nodejs/node/pull/4310)
- [[`cb0b4a6bc0`](https://github.com/nodejs/node/commit/cb0b4a6bc0)] - **test**: add test for debugging one line files (cjihrig) [#4298](https://github.com/nodejs/node/pull/4298)
- [[`0b9c3a30d6`](https://github.com/nodejs/node/commit/0b9c3a30d6)] - **test**: add test for tls.parseCertString (Evan Lucas) [#4283](https://github.com/nodejs/node/pull/4283)
- [[`7598ed6cc0`](https://github.com/nodejs/node/commit/7598ed6cc0)] - **test**: parallelize test-repl-persistent-history (Jeremiah Senkpiel) [#4247](https://github.com/nodejs/node/pull/4247)
- [[`668449ad14`](https://github.com/nodejs/node/commit/668449ad14)] - **test**: use regular timeout times for ARMv8 (Jeremiah Senkpiel) [#4248](https://github.com/nodejs/node/pull/4248)
- [[`23e7703c85`](https://github.com/nodejs/node/commit/23e7703c85)] - **test**: fix http-many-ended-pipelines flakiness (Santiago Gimeno) [#4041](https://github.com/nodejs/node/pull/4041)
- [[`3b94991bda`](https://github.com/nodejs/node/commit/3b94991bda)] - **test**: fix tls-inception flakiness (Santiago Gimeno) [#4195](https://github.com/nodejs/node/pull/4195)
- [[`86a3bd09b0`](https://github.com/nodejs/node/commit/86a3bd09b0)] - **test**: fix tls-inception (Santiago Gimeno) [#4195](https://github.com/nodejs/node/pull/4195)
- [[`1e89830a11`](https://github.com/nodejs/node/commit/1e89830a11)] - **test**: don't assume openssl s_client supports -ssl3 (Ben Noordhuis) [#4204](https://github.com/nodejs/node/pull/4204)
- [[`c5b4f6bc99`](https://github.com/nodejs/node/commit/c5b4f6bc99)] - **(SEMVER-MINOR)** **tls**: introduce `secureContext` for `tls.connect` (Fedor Indutny) [#4246](https://github.com/nodejs/node/pull/4246)
- [[`e0bb118a1d`](https://github.com/nodejs/node/commit/e0bb118a1d)] - **tls_wrap**: inherit from the `AsyncWrap` first (Fedor Indutny) [#4268](https://github.com/nodejs/node/pull/4268)
- [[`d63cceeb10`](https://github.com/nodejs/node/commit/d63cceeb10)] - **tools**: add .editorconfig (ronkorving) [#2993](https://github.com/nodejs/node/pull/2993)
- [[`4b267df93e`](https://github.com/nodejs/node/commit/4b267df93e)] - **udp**: remove a needless instanceof Buffer check (ronkorving) [#4301](https://github.com/nodejs/node/pull/4301)

Windows 32-bit Installer: https://nodejs.org/dist/v5.3.0/node-v5.3.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v5.3.0/node-v5.3.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v5.3.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v5.3.0/win-x64/node.exe \
Mac OS X 64-bit Installer: https://nodejs.org/dist/v5.3.0/node-v5.3.0.pkg \
Mac OS X 64-bit Binary: https://nodejs.org/dist/v5.3.0/node-v5.3.0-darwin-x64.tar.gz \
Linux 32-bit Binary: https://nodejs.org/dist/v5.3.0/node-v5.3.0-linux-x86.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v5.3.0/node-v5.3.0-linux-x64.tar.gz \
SmartOS 32-bit Binary: https://nodejs.org/dist/v5.3.0/node-v5.3.0-sunos-x86.tar.gz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v5.3.0/node-v5.3.0-sunos-x64.tar.gz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v5.3.0/node-v5.3.0-linux-armv6l.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v5.3.0/node-v5.3.0-linux-armv7l.tar.gz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v5.3.0/node-v5.3.0-linux-arm64.tar.gz \
Source Code: https://nodejs.org/dist/v5.3.0/node-v5.3.0.tar.gz \
Other release files: https://nodejs.org/dist/v5.3.0/ \
Documentation: https://nodejs.org/docs/v5.3.0/api/

Shasums (GPG signing hash: SHA512, file hash: SHA256):

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA512

bfb28ff6a02a6bcb3a77afcb66054dcf44b50e1ccdbeca807865c6220c380b6b  node-v5.3.0-darwin-x64.tar.gz
c88af62d50f19803831a0ab0c1f208154d8bb0e022fe9e374e068183c05ed262  node-v5.3.0-darwin-x64.tar.xz
40d7d7982865719af6723e4796d0d773f2d47a64e43133b7a7f89bbac1a52e97  node-v5.3.0-headers.tar.gz
3e92e7cd02d2571455f7791d582ee76c5292c54ac97d57c229a05f85fd180287  node-v5.3.0-headers.tar.xz
0a37c919cb2e2511ee7ff60e4fc80266afa3dad7cffa9204dc73da244c3a308a  node-v5.3.0-linux-arm64.tar.gz
1e9f4f7650fdef2bbd030cec7de59e225030e05e0e1298f5f84b103fcd1900e8  node-v5.3.0-linux-arm64.tar.xz
266bedf9f8777c98abd0f28b41b0bb2b0b3d5be888bcfb899bf46fd986188172  node-v5.3.0-linux-armv6l.tar.gz
76dbb7960dfaab99c017e568e3f2b4b7c7bd4bdf2c341b38cf1053ad8d159eea  node-v5.3.0-linux-armv6l.tar.xz
67205437fd74f7e63219cbe5932f83b064884204eea4503faa6d93f5db2719e1  node-v5.3.0-linux-armv7l.tar.gz
2b84201ec54279b3d0e148c04111ac8aa004698bbb131753f6f86233f32b64aa  node-v5.3.0-linux-armv7l.tar.xz
75b029b30d4a4147d67cf75bf6e034291fb5919c6935ec23f8365cee2d463f12  node-v5.3.0-linux-x64.tar.gz
8c03fed1c6b11dc6604282a595cd695eab5b80cd9e025bd6ced4fa78c2ade396  node-v5.3.0-linux-x64.tar.xz
5e1b2e0aa5277e6dded7bc3cba452035a9695b696e974b02eadcba76070ca7eb  node-v5.3.0-linux-x86.tar.gz
6f46d13e0c6e94f1b31fece79eef880804de04b2316e04ba9fd6a4dcfd9ae666  node-v5.3.0-linux-x86.tar.xz
755e0abee3b2d50f28a906d292fb201676661e38135916622f7ce5dbe6f39bea  node-v5.3.0.pkg
96be7d379e284012c2da8e08df304896ba7b533fd84be4585b1405bcafbe8c96  node-v5.3.0-sunos-x64.tar.gz
06fdb8871ccd4a86d893cecf06c8dbe367eeef95ee79cca5e737cb66f3a95340  node-v5.3.0-sunos-x64.tar.xz
0c28263d118a1819e48d633d5dc62fbc88035cff007246588505eb836eb041af  node-v5.3.0-sunos-x86.tar.gz
6530edeb7e09f5d1b6083579ed0438c2a15a7e2a8f931ebdd401b05eb5a27d77  node-v5.3.0-sunos-x86.tar.xz
cc05ff06149c638345835788f448471d264a7e011bf083394f86d5be51975c7e  node-v5.3.0.tar.gz
e5115ee78c31ac3af0f2b3553aa84fff2f49ae59bd6e01e11d78ce86c544569e  node-v5.3.0.tar.xz
1c2a516cac40488f0d305c8aeed2fc61075dd882427830c12593f652d68f65e0  node-v5.3.0-x64.msi
156065db0018e3058e2fb41753eb27d160e6ba3ec5fad8f394f1c6f2992661af  node-v5.3.0-x86.msi
c665410c624b1a6833d7460e25ec8cfa21f4fb215b92f5c684ad26e64888554d  win-x64/node.exe
796fab4b8bf369ba3d21cf932e8a3110b3b458c4d1c5cee87e8ab1d594d57ced  win-x64/node.lib
2a1815c7f12ffa1ec62b827816657330a686180804c8482bff10c12433f1f49c  win-x86/node.exe
4325dcfd627d6286907a9c2378bce93b7690152bd08da187d7c3e917b932f9b4  win-x86/node.lib
-----BEGIN PGP SIGNATURE-----
Comment: GPGTools - https://gpgtools.org

iQIcBAEBCgAGBQJWccPxAAoJEHQ0OQvb6bnFCsEP/ROhGerba34tL4lzNRpRtMWP
HwmvKX9VVFhZwYWpPrqkowl3F292Jo5oxNNxT8vkElWjBhLeRa55dDNJzv1f7xs1
2/3U5vRmOv3bpcp32d/8+GIjDsSfy3KrzjgFJh9Nw4fW0QrIzsgLFIZKCnAI/Tx2
1KTRVefFUbMlQxRP3cH4SaLmDACyD5KuDFJjWMLbtuUUql+x4UcfoEL9iM2kN/VX
1vA+0SMBGcEftAnnM2xjeZNPXPUWOiCtONnngfcAU+TT5kPW54bvpPNb0E3qy7H4
onliYYuDK5171BodmLnQTsXn5u9QhmngHll+nTVDjMJHY7RPCB2+6GOdyXRcZbNx
t6/DEiBEs38qM+NOhEegzNCJsbMvHRd6KW+RVbX81u/X0ncXplOGwsfnjX4ndl4H
piL3CnQts/EI5DHqm22YvBJj6NbxosLM8OOkJSrt9JZTwXyI4TWJ+zW9+OCrFZbw
nDGeAokNSvA9nGXLgunSUVnIHHQdEUnIjYlnwFsJuE6qHuyNHerMuFdUPeAkWnr6
21pwPOwSGbP1oll3vYB8CREu3rRskW7mk1m0amZc5dxsSs/w28XcwYacvz5Hpf4Q
8Y2JkHAWZDKVyKyGY4j8uyRhHGJ6h5FFfa8LOPar/rwDM6IufcW4PKsqVvqM2glx
5p6R3ZmeTxU1HDkQHWOk
=IJpS
-----END PGP SIGNATURE-----

```
