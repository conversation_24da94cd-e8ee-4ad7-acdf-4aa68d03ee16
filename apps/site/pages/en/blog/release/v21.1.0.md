---
date: '2023-10-24T13:54:53.218Z'
category: release
title: Node v21.1.0 (Current)
layout: blog-post
author: <PERSON><PERSON><PERSON>
---

## 2023-10-24, Version 21.1.0 (Current), @targos

### Notable Changes

#### Automatically detect and run ESM syntax

The new flag `--experimental-detect-module` can be used to automatically run
ES modules when their syntax can be detected. For “ambiguous” files, which are
`.js` or extensionless files with no `package.json` with a `type` field, Node.js
will parse the file to detect ES module syntax; if found, it will run the file
as an ES module, otherwise it will run the file as a CommonJS module.
The same applies to string input via `--eval` or `STDIN`.

We hope to make detection enabled by default in a future version of Node.js.
Detection increases startup time, so we encourage everyone — especially package
authors — to add a `type` field to `package.json`, even for the default
`"type": "commonjs"`. The presence of a `type` field, or explicit extensions
such as `.mjs` or `.cjs`, will opt out of detection.

Contributed by <PERSON> in [#50096](https://github.com/nodejs/node/pull/50096).

### vm: fix V8 compilation cache support for vm.Script

Previously repeated compilation of the same source code using `vm.Script` stopped hitting the V8 compilation cache after v16.x when support for `importModuleDynamically` was added to `vm.Script`, resulting in a performance regression that blocked users (in particular Jest users) from upgrading from v16.x.

The recent fixes landed in v21.1.0 allow the compilation cache to be hit again for `vm.Script` when `--experimental-vm-modules` is not used even in the presence of the `importModuleDynamically` option, so that users affected by the performance regression can now upgrade. Ongoing work is also being done to enable compilation cache support for `vm.CompileFunction`.

Contributed by Joyee Cheung in https://github.com/nodejs/node/pull/50137

#### Other Notable Changes

- \[[`3729e33358`](https://github.com/nodejs/node/commit/3729e33358)] - **doc**: add H4ad to collaborators (Vinícius Lourenço) [#50217](https://github.com/nodejs/node/pull/50217)
- \[[`18862e4d5d`](https://github.com/nodejs/node/commit/18862e4d5d)] - **(SEMVER-MINOR)** **fs**: add `flush` option to `appendFile()` functions (Colin Ihrig) [#50095](https://github.com/nodejs/node/pull/50095)
- \[[`5a52c518ef`](https://github.com/nodejs/node/commit/5a52c518ef)] - **(SEMVER-MINOR)** **lib**: add `navigator.userAgent` (Yagiz Nizipli) [#50200](https://github.com/nodejs/node/pull/50200)
- \[[`789372a072`](https://github.com/nodejs/node/commit/789372a072)] - **(SEMVER-MINOR)** **stream**: allow pass stream class to `stream.compose` (Alex Yang) [#50187](https://github.com/nodejs/node/pull/50187)
- \[[`f3a9ea0bc4`](https://github.com/nodejs/node/commit/f3a9ea0bc4)] - **stream**: improve performance of readable stream reads (Raz Luvaton) [#50173](https://github.com/nodejs/node/pull/50173)

### Commits

- \[[`9cd68b9083`](https://github.com/nodejs/node/commit/9cd68b9083)] - **buffer**: remove unnecessary assignment in fromString (Tobias Nießen) [#50199](https://github.com/nodejs/node/pull/50199)
- \[[`a362c276ec`](https://github.com/nodejs/node/commit/a362c276ec)] - **crypto**: ensure valid point on elliptic curve in SubtleCrypto.importKey (Filip Skokan) [#50234](https://github.com/nodejs/node/pull/50234)
- \[[`f4da308f8d`](https://github.com/nodejs/node/commit/f4da308f8d)] - **deps**: V8: cherry-pick f7d000a7ae7b (Luke Albao) [#50302](https://github.com/nodejs/node/pull/50302)
- \[[`269e268c38`](https://github.com/nodejs/node/commit/269e268c38)] - **deps**: update ada to 2.7.2 (Node.js GitHub Bot) [#50338](https://github.com/nodejs/node/pull/50338)
- \[[`03a31ce41e`](https://github.com/nodejs/node/commit/03a31ce41e)] - **deps**: update corepack to 0.22.0 (Node.js GitHub Bot) [#50325](https://github.com/nodejs/node/pull/50325)
- \[[`000531781b`](https://github.com/nodejs/node/commit/000531781b)] - **deps**: update undici to 5.26.4 (Node.js GitHub Bot) [#50274](https://github.com/nodejs/node/pull/50274)
- \[[`f050668c14`](https://github.com/nodejs/node/commit/f050668c14)] - **deps**: update c-ares to 1.20.1 (Node.js GitHub Bot) [#50082](https://github.com/nodejs/node/pull/50082)
- \[[`ba258b682b`](https://github.com/nodejs/node/commit/ba258b682b)] - **deps**: update c-ares to 1.20.0 (Node.js GitHub Bot) [#50082](https://github.com/nodejs/node/pull/50082)
- \[[`571f7ef1fa`](https://github.com/nodejs/node/commit/571f7ef1fa)] - **deps**: patch V8 to 11.8.172.15 (Michaël Zasso) [#50114](https://github.com/nodejs/node/pull/50114)
- \[[`943047e800`](https://github.com/nodejs/node/commit/943047e800)] - **deps**: V8: cherry-pick 25902244ad1a (Joyee Cheung) [#50156](https://github.com/nodejs/node/pull/50156)
- \[[`db2a1cf1cb`](https://github.com/nodejs/node/commit/db2a1cf1cb)] - **doc**: fix `navigator.hardwareConcurrency` example (Tobias Nießen) [#50278](https://github.com/nodejs/node/pull/50278)
- \[[`6e537aeb44`](https://github.com/nodejs/node/commit/6e537aeb44)] - **doc**: explain how to disable navigator (Geoffrey Booth) [#50310](https://github.com/nodejs/node/pull/50310)
- \[[`c40de82d62`](https://github.com/nodejs/node/commit/c40de82d62)] - **doc**: add loong64 info into platform list (Shi Pujin) [#50086](https://github.com/nodejs/node/pull/50086)
- \[[`1c21a1880b`](https://github.com/nodejs/node/commit/1c21a1880b)] - **doc**: update release process LTS step (Richard Lau) [#50299](https://github.com/nodejs/node/pull/50299)
- \[[`2473aa3672`](https://github.com/nodejs/node/commit/2473aa3672)] - **doc**: fix release process table of contents (Richard Lau) [#50216](https://github.com/nodejs/node/pull/50216)
- \[[`ce9d84eae3`](https://github.com/nodejs/node/commit/ce9d84eae3)] - **doc**: update api `stream.compose` (Alex Yang) [#50206](https://github.com/nodejs/node/pull/50206)
- \[[`dacee4d9b5`](https://github.com/nodejs/node/commit/dacee4d9b5)] - **doc**: add ReflectConstruct to known perf issues (Vinicius Lourenço) [#50111](https://github.com/nodejs/node/pull/50111)
- \[[`82363be2ac`](https://github.com/nodejs/node/commit/82363be2ac)] - **doc**: fix typo in dgram docs (Peter Johnson) [#50211](https://github.com/nodejs/node/pull/50211)
- \[[`8c1a46c751`](https://github.com/nodejs/node/commit/8c1a46c751)] - **doc**: fix H4ad collaborator sort (Vinicius Lourenço) [#50218](https://github.com/nodejs/node/pull/50218)
- \[[`3729e33358`](https://github.com/nodejs/node/commit/3729e33358)] - **doc**: add H4ad to collaborators (Vinícius Lourenço) [#50217](https://github.com/nodejs/node/pull/50217)
- \[[`bac872cbd0`](https://github.com/nodejs/node/commit/bac872cbd0)] - **doc**: update release-stewards with last sec-release (Rafael Gonzaga) [#50179](https://github.com/nodejs/node/pull/50179)
- \[[`06b7724f14`](https://github.com/nodejs/node/commit/06b7724f14)] - **doc**: add command to keep major branch sync (Rafael Gonzaga) [#50102](https://github.com/nodejs/node/pull/50102)
- \[[`47633ab086`](https://github.com/nodejs/node/commit/47633ab086)] - **doc**: add loong64 to list of architectures (Shi Pujin) [#50172](https://github.com/nodejs/node/pull/50172)
- \[[`1f40ca1b91`](https://github.com/nodejs/node/commit/1f40ca1b91)] - **doc**: update security release process (Michael Dawson) [#50166](https://github.com/nodejs/node/pull/50166)
- \[[`998feda118`](https://github.com/nodejs/node/commit/998feda118)] - **esm**: do not give wrong hints when detecting file format (Antoine du Hamel) [#50314](https://github.com/nodejs/node/pull/50314)
- \[[`e375063e01`](https://github.com/nodejs/node/commit/e375063e01)] - **(SEMVER-MINOR)** **esm**: detect ESM syntax in ambiguous JavaScript (Geoffrey Booth) [#50096](https://github.com/nodejs/node/pull/50096)
- \[[`c76eb27971`](https://github.com/nodejs/node/commit/c76eb27971)] - **esm**: improve check for ESM syntax (Geoffrey Booth) [#50127](https://github.com/nodejs/node/pull/50127)
- \[[`7740bf820c`](https://github.com/nodejs/node/commit/7740bf820c)] - **esm**: rename error code related to import attributes (Antoine du Hamel) [#50181](https://github.com/nodejs/node/pull/50181)
- \[[`0cc176ef25`](https://github.com/nodejs/node/commit/0cc176ef25)] - **fs**: improve error performance for `readSync` (Jungku Lee) [#50033](https://github.com/nodejs/node/pull/50033)
- \[[`5942edb774`](https://github.com/nodejs/node/commit/5942edb774)] - **fs**: improve error performance for `fsyncSync` (Jungku Lee) [#49880](https://github.com/nodejs/node/pull/49880)
- \[[`6ec5abadc0`](https://github.com/nodejs/node/commit/6ec5abadc0)] - **fs**: improve error performance for `mkdirSync` (CanadaHonk) [#49847](https://github.com/nodejs/node/pull/49847)
- \[[`c5ff000cb1`](https://github.com/nodejs/node/commit/c5ff000cb1)] - **fs**: improve error performance of `realpathSync` (Yagiz Nizipli) [#49962](https://github.com/nodejs/node/pull/49962)
- \[[`6eeaa02f5c`](https://github.com/nodejs/node/commit/6eeaa02f5c)] - **fs**: improve error performance of `lchownSync` (Yagiz Nizipli) [#49962](https://github.com/nodejs/node/pull/49962)
- \[[`dc9ac8d41c`](https://github.com/nodejs/node/commit/dc9ac8d41c)] - **fs**: improve error performance of `symlinkSync` (Yagiz Nizipli) [#49962](https://github.com/nodejs/node/pull/49962)
- \[[`bc6f279261`](https://github.com/nodejs/node/commit/bc6f279261)] - **fs**: improve error performance of `readlinkSync` (Yagiz Nizipli) [#49962](https://github.com/nodejs/node/pull/49962)
- \[[`275987841e`](https://github.com/nodejs/node/commit/275987841e)] - **fs**: improve error performance of `mkdtempSync` (Yagiz Nizipli) [#49962](https://github.com/nodejs/node/pull/49962)
- \[[`81f15274e2`](https://github.com/nodejs/node/commit/81f15274e2)] - **fs**: improve error performance of `linkSync` (Yagiz Nizipli) [#49962](https://github.com/nodejs/node/pull/49962)
- \[[`f766c04856`](https://github.com/nodejs/node/commit/f766c04856)] - **fs**: improve error performance of `chownSync` (Yagiz Nizipli) [#49962](https://github.com/nodejs/node/pull/49962)
- \[[`610036c67d`](https://github.com/nodejs/node/commit/610036c67d)] - **fs**: improve error performance of `renameSync` (Yagiz Nizipli) [#49962](https://github.com/nodejs/node/pull/49962)
- \[[`18862e4d5d`](https://github.com/nodejs/node/commit/18862e4d5d)] - **(SEMVER-MINOR)** **fs**: add flush option to appendFile() functions (Colin Ihrig) [#50095](https://github.com/nodejs/node/pull/50095)
- \[[`3f8cbb15cb`](https://github.com/nodejs/node/commit/3f8cbb15cb)] - **http2**: allow streams to complete gracefully after goaway (Michael Lumish) [#50202](https://github.com/nodejs/node/pull/50202)
- \[[`1464eba1a0`](https://github.com/nodejs/node/commit/1464eba1a0)] - **lib**: improve performance of validateStringArray and validateBooleanArray (Aras Abbasi) [#49756](https://github.com/nodejs/node/pull/49756)
- \[[`5a52c518ef`](https://github.com/nodejs/node/commit/5a52c518ef)] - **(SEMVER-MINOR)** **lib**: add `navigator.userAgent` (Yagiz Nizipli) [#50200](https://github.com/nodejs/node/pull/50200)
- \[[`b6021ab8f6`](https://github.com/nodejs/node/commit/b6021ab8f6)] - **lib**: reduce overhead of blob clone (Vinicius Lourenço) [#50110](https://github.com/nodejs/node/pull/50110)
- \[[`be19d9baa1`](https://github.com/nodejs/node/commit/be19d9baa1)] - **meta**: move Trott to TSC regular member (Rich Trott) [#50297](https://github.com/nodejs/node/pull/50297)
- \[[`91e373f8e9`](https://github.com/nodejs/node/commit/91e373f8e9)] - **node-api**: return napi_exception_pending on proxy handlers (Chengzhong Wu) [#48607](https://github.com/nodejs/node/pull/48607)
- \[[`531a3ae4b5`](https://github.com/nodejs/node/commit/531a3ae4b5)] - **stream**: simplify prefinish (Robert Nagy) [#50204](https://github.com/nodejs/node/pull/50204)
- \[[`514ac86579`](https://github.com/nodejs/node/commit/514ac86579)] - **stream**: reduce scope of readable bitmap details (Robert Nagy) [#49963](https://github.com/nodejs/node/pull/49963)
- \[[`789372a072`](https://github.com/nodejs/node/commit/789372a072)] - **(SEMVER-MINOR)** **stream**: allow pass stream class to `stream.compose` (Alex Yang) [#50187](https://github.com/nodejs/node/pull/50187)
- \[[`f3a9ea0bc4`](https://github.com/nodejs/node/commit/f3a9ea0bc4)] - **stream**: call helper function from push and unshift (Raz Luvaton) [#50173](https://github.com/nodejs/node/pull/50173)
- \[[`a9ca7b32e7`](https://github.com/nodejs/node/commit/a9ca7b32e7)] - **test**: improve watch mode test (Moshe Atlow) [#50319](https://github.com/nodejs/node/pull/50319)
- \[[`63b7059efd`](https://github.com/nodejs/node/commit/63b7059efd)] - **test**: set `test-watch-mode-inspect` as flaky (Yagiz Nizipli) [#50259](https://github.com/nodejs/node/pull/50259)
- \[[`7f87084b05`](https://github.com/nodejs/node/commit/7f87084b05)] - _**Revert**_ "**test**: set `test-esm-loader-resolve-type` as flaky" (Antoine du Hamel) [#50315](https://github.com/nodejs/node/pull/50315)
- \[[`4d390e2de4`](https://github.com/nodejs/node/commit/4d390e2de4)] - **test**: replace forEach with for..of in test-http-perf_hooks.js (Niya Shiyas) [#49818](https://github.com/nodejs/node/pull/49818)
- \[[`67c599ec39`](https://github.com/nodejs/node/commit/67c599ec39)] - **test**: replace forEach with for..of in test-net-isipv4.js (Niya Shiyas) [#49822](https://github.com/nodejs/node/pull/49822)
- \[[`19d3ce2494`](https://github.com/nodejs/node/commit/19d3ce2494)] - **test**: deflake `test-esm-loader-resolve-type` (Antoine du Hamel) [#50273](https://github.com/nodejs/node/pull/50273)
- \[[`2d8d6c5701`](https://github.com/nodejs/node/commit/2d8d6c5701)] - **test**: replace forEach with for..of in test-http2-server (Niya Shiyas) [#49819](https://github.com/nodejs/node/pull/49819)
- \[[`af31d51e5a`](https://github.com/nodejs/node/commit/af31d51e5a)] - **test**: replace forEach with for..of in test-http2-client-destroy.js (Niya Shiyas) [#49820](https://github.com/nodejs/node/pull/49820)
- \[[`465ad2a5ce`](https://github.com/nodejs/node/commit/465ad2a5ce)] - **test**: update `url` web platform tests (Yagiz Nizipli) [#50264](https://github.com/nodejs/node/pull/50264)
- \[[`3b80a6894c`](https://github.com/nodejs/node/commit/3b80a6894c)] - **test**: set `test-emit-after-on-destroyed` as flaky (Yagiz Nizipli) [#50246](https://github.com/nodejs/node/pull/50246)
- \[[`57adbdd156`](https://github.com/nodejs/node/commit/57adbdd156)] - **test**: set inspector async stack test as flaky (Yagiz Nizipli) [#50244](https://github.com/nodejs/node/pull/50244)
- \[[`6507f66404`](https://github.com/nodejs/node/commit/6507f66404)] - **test**: set test-worker-nearheaplimit-deadlock flaky (StefanStojanovic) [#50277](https://github.com/nodejs/node/pull/50277)
- \[[`21a6ba548d`](https://github.com/nodejs/node/commit/21a6ba548d)] - **test**: set `test-cli-node-options` as flaky (Yagiz Nizipli) [#50296](https://github.com/nodejs/node/pull/50296)
- \[[`c55f8f30cb`](https://github.com/nodejs/node/commit/c55f8f30cb)] - **test**: reduce the number of requests and parsers (Luigi Pinca) [#50240](https://github.com/nodejs/node/pull/50240)
- \[[`5129bedfa2`](https://github.com/nodejs/node/commit/5129bedfa2)] - **test**: set crypto-timing test as flaky (Yagiz Nizipli) [#50232](https://github.com/nodejs/node/pull/50232)
- \[[`9bc5ab5e07`](https://github.com/nodejs/node/commit/9bc5ab5e07)] - **test**: set `test-structuredclone-*` as flaky (Yagiz Nizipli) [#50261](https://github.com/nodejs/node/pull/50261)
- \[[`317e447ddc`](https://github.com/nodejs/node/commit/317e447ddc)] - **test**: deflake `test-loaders-workers-spawned` (Antoine du Hamel) [#50251](https://github.com/nodejs/node/pull/50251)
- \[[`0c710daae2`](https://github.com/nodejs/node/commit/0c710daae2)] - **test**: improve code coverage of diagnostics_channel (Jithil P Ponnan) [#50053](https://github.com/nodejs/node/pull/50053)
- \[[`7c6e4d7ec3`](https://github.com/nodejs/node/commit/7c6e4d7ec3)] - **test**: set `test-esm-loader-resolve-type` as flaky (Yagiz Nizipli) [#50226](https://github.com/nodejs/node/pull/50226)
- \[[`c8744909b0`](https://github.com/nodejs/node/commit/c8744909b0)] - **test**: set inspector async hook test as flaky (Yagiz Nizipli) [#50252](https://github.com/nodejs/node/pull/50252)
- \[[`3e38001739`](https://github.com/nodejs/node/commit/3e38001739)] - **test**: skip test-benchmark-os.js on IBM i (Abdirahim Musse) [#50208](https://github.com/nodejs/node/pull/50208)
- \[[`dd66fdfb7b`](https://github.com/nodejs/node/commit/dd66fdfb7b)] - **test**: set parallel http server test as flaky (Yagiz Nizipli) [#50227](https://github.com/nodejs/node/pull/50227)
- \[[`a38d1311bf`](https://github.com/nodejs/node/commit/a38d1311bf)] - **test**: set test-worker-nearheaplimit-deadlock flaky (Stefan Stojanovic) [#50238](https://github.com/nodejs/node/pull/50238)
- \[[`8efb75fd80`](https://github.com/nodejs/node/commit/8efb75fd80)] - **test**: set `test-runner-watch-mode` as flaky (Yagiz Nizipli) [#50221](https://github.com/nodejs/node/pull/50221)
- \[[`143ddded74`](https://github.com/nodejs/node/commit/143ddded74)] - **test**: set sea snapshot tests as flaky (Yagiz Nizipli) [#50223](https://github.com/nodejs/node/pull/50223)
- \[[`ae905a8f35`](https://github.com/nodejs/node/commit/ae905a8f35)] - **test**: fix defect path traversal tests (Tobias Nießen) [#50124](https://github.com/nodejs/node/pull/50124)
- \[[`ce27ee701b`](https://github.com/nodejs/node/commit/ce27ee701b)] - **tls**: reduce TLS 'close' event listener warnings (Tim Perry) [#50136](https://github.com/nodejs/node/pull/50136)
- \[[`ab4bae8e1f`](https://github.com/nodejs/node/commit/ab4bae8e1f)] - **tools**: drop support for osx notarization with gon (Ulises Gascón) [#50291](https://github.com/nodejs/node/pull/50291)
- \[[`5df3d5abcc`](https://github.com/nodejs/node/commit/5df3d5abcc)] - **tools**: update comment in `update-uncidi.sh` and `acorn_version.h` (Jungku Lee) [#50175](https://github.com/nodejs/node/pull/50175)
- \[[`bf7b94f0b3`](https://github.com/nodejs/node/commit/bf7b94f0b3)] - **tools**: refactor checkimports.py (Mohammed Keyvanzadeh) [#50011](https://github.com/nodejs/node/pull/50011)
- \[[`5dc454a837`](https://github.com/nodejs/node/commit/5dc454a837)] - **util**: remove internal mime fns from benchmarks (Aras Abbasi) [#50201](https://github.com/nodejs/node/pull/50201)
- \[[`8f7eb15603`](https://github.com/nodejs/node/commit/8f7eb15603)] - **vm**: use import attributes instead of import assertions (Antoine du Hamel) [#50141](https://github.com/nodejs/node/pull/50141)
- \[[`dda33c2bf1`](https://github.com/nodejs/node/commit/dda33c2bf1)] - **vm**: reject in importModuleDynamically without --experimental-vm-modules (Joyee Cheung) [#50137](https://github.com/nodejs/node/pull/50137)
- \[[`3999362c59`](https://github.com/nodejs/node/commit/3999362c59)] - **vm**: use internal versions of compileFunction and Script (Joyee Cheung) [#50137](https://github.com/nodejs/node/pull/50137)
- \[[`a54179f0e0`](https://github.com/nodejs/node/commit/a54179f0e0)] - **vm**: unify host-defined option generation in vm.compileFunction (Joyee Cheung) [#50137](https://github.com/nodejs/node/pull/50137)
- \[[`87be790fa9`](https://github.com/nodejs/node/commit/87be790fa9)] - **worker**: handle detached `MessagePort` from a different context (Juan José) [#49150](https://github.com/nodejs/node/pull/49150)

Windows 32-bit Installer: https://nodejs.org/dist/v21.1.0/node-v21.1.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v21.1.0/node-v21.1.0-x64.msi \
Windows ARM 64-bit Installer: https://nodejs.org/dist/v21.1.0/node-v21.1.0-arm64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v21.1.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v21.1.0/win-x64/node.exe \
Windows ARM 64-bit Binary: https://nodejs.org/dist/v21.1.0/win-arm64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v21.1.0/node-v21.1.0.pkg \
macOS Apple Silicon 64-bit Binary: https://nodejs.org/dist/v21.1.0/node-v21.1.0-darwin-arm64.tar.gz \
macOS Intel 64-bit Binary: https://nodejs.org/dist/v21.1.0/node-v21.1.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v21.1.0/node-v21.1.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v21.1.0/node-v21.1.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v21.1.0/node-v21.1.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v21.1.0/node-v21.1.0-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v21.1.0/node-v21.1.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v21.1.0/node-v21.1.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v21.1.0/node-v21.1.0.tar.gz \
Other release files: https://nodejs.org/dist/v21.1.0/ \
Documentation: https://nodejs.org/docs/v21.1.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

691672195af440401e4957d9d5e148d361e686aa26c9e529505edf89bab4b089  node-v21.1.0-aix-ppc64.tar.gz
75f28378c09fba7c5ecd6add07ac64f4983f23a847987e0a2097d753b0dcb30c  node-v21.1.0-arm64.msi
4872463830381785b91d13a7fbb9b6f4a9c7658e10d964f6c421951cec8833ad  node-v21.1.0-darwin-arm64.tar.gz
ecd3f51874892a8a56abf4b96424236647f26f64da6139a930557c044c48f73b  node-v21.1.0-darwin-arm64.tar.xz
6b526c08320fcf41ced0ceee7588828ea2cb07ba826af4ff82b0ec53958fd8a4  node-v21.1.0-darwin-x64.tar.gz
f33382175ab49d0c9bd176401e746a041db1c7c31c85b7b91344e188945c3ba1  node-v21.1.0-darwin-x64.tar.xz
ceb2ecd7ed6049778403c0baa49844ba9495c1218b55bb73dea42a87e6a57253  node-v21.1.0-headers.tar.gz
c1ab3d18209f1331b9f14cdea0aeb2f7fdce4583aceee001856a42e44cfe1b35  node-v21.1.0-headers.tar.xz
5480f438703049f55f19fc3247f6aa1e8059b2f47cf08e9adfdcb7ce7aedff70  node-v21.1.0-linux-arm64.tar.gz
ff487ad68d898a6eebbf02e15fe5c5a885991828732163501fdf21af7a7bef36  node-v21.1.0-linux-arm64.tar.xz
0d51fe055599c17b2e3389d15868d4fd5ca07b408f8c15c325874595310a9382  node-v21.1.0-linux-armv7l.tar.gz
a8de516f50e6f4a7325d115a7cf398ac15440d495672f5d0118296b5d593fee9  node-v21.1.0-linux-armv7l.tar.xz
ed90f74545cd44474bbb947544679d3dfb44403200f7d2faa49402bebf26e52f  node-v21.1.0-linux-ppc64le.tar.gz
8c932a1259291e8db9874981886d17993ab82ecf1ecaeda2c102b5f44ef94593  node-v21.1.0-linux-ppc64le.tar.xz
b45b87d21dada54999ef8d80957a6e700d0d29dd1b7f23502223c4a67d9d0a10  node-v21.1.0-linux-s390x.tar.gz
a8d04ae7a2b401e469a2c7e1e579d985126d3ec1a024bcc5220a059fcf861c17  node-v21.1.0-linux-s390x.tar.xz
b919cad4e8a5abbd7e6a4433c4f8a7cdc1a78c1e526c6c1aa4a5fcf74011ad2b  node-v21.1.0-linux-x64.tar.gz
6a1020b5605ba4b4a89b08147e5fd124bd9d1fd5bfe5301fe0567af67bf0e335  node-v21.1.0-linux-x64.tar.xz
5fcb23725fa760ebe059cc5d957e9214f22ba623b20ee20301089a89d33bc06f  node-v21.1.0.pkg
86e2fbfa8a9977656a82bb1a15208e7d45bd7a05dec331d0abf6fcf938a97cc3  node-v21.1.0.tar.gz
91ac72e4444c5e5ab4b448030a61ffa95acd35d34a9d31d2d220ee2bed01b925  node-v21.1.0.tar.xz
8a0f2c3abea32eb1706772d741ed8ea05de8f0b170561b37088cb2412de5cb2b  node-v21.1.0-win-arm64.7z
fe660ef5ff0a1cf09207a1d83dbac5cfa0495870717aa397b584ce0c3e5cfcfb  node-v21.1.0-win-arm64.zip
20ad766d8576e0c651dcbd6cbafaf803a51110613304224820c38936858af774  node-v21.1.0-win-x64.7z
6ee3e74ecceb27f388d75a94d6782df670bad37a4d10ff2d28a7c7bcb75bdb49  node-v21.1.0-win-x64.zip
3247272d574287ea798a34bd5a393671d44d0f608434f06eb97696754ee0257f  node-v21.1.0-win-x86.7z
d4c3834a00cf0d74ca80d5e9992acb98f7d703293a18eb1716d596148ff855a8  node-v21.1.0-win-x86.zip
9890aaf7b9387a6d15890735b8facfe9eb072aebfd30e38cbeaf3426a1eabdc0  node-v21.1.0-x64.msi
53f8248e82282552f9fe5709a771801c4adb174f6dc35e2610077edc758d2bcf  node-v21.1.0-x86.msi
69d61ca95c2a81f2a8f535446a2bfdd8b905d1b1783d18ae82b92017a637ab61  win-arm64/node.exe
aa149ab68ccab472beea201d39283390547c8e0a12b099797a0cfb6d9ffefb7b  win-arm64/node.lib
085701532872fa03f7c29148b6b880245a765ee5f2c51593fc97a848730f9a57  win-arm64/node_pdb.7z
97580ce603887840a19f314a7eeaede4e18b72de0dfda060ea5ac4e747fc6465  win-arm64/node_pdb.zip
1cbbd083f79c51c6ca4d3ccffc944895d4e04e182849fd49855d93b66c0bb411  win-x64/node.exe
2f88106acb54a105e7beda077ab9eda04d98c202db9c1477ea48d7dd9e58151a  win-x64/node.lib
f100227a3a7351c7340ef440a6b7dc2a4c2ab9786d0d3f8f603fb5658c423946  win-x64/node_pdb.7z
7b29f3242e0eaffb8990d8cd375a6a9d48079708e3dbc676c7a30d67895e052f  win-x64/node_pdb.zip
2374196fac3d6b3611a411a6c413f1a78f47e47f7d5a86e861e774bf8203bc10  win-x86/node.exe
6825637ae988424a72f049ba92264be2956881875ee264cb71441bfbff5bcd85  win-x86/node.lib
842afb550c8821b82a1136dc29a3d0a0c862a045bea2c9940148569305a867c3  win-x86/node_pdb.7z
3e5357d128e79924866b1ed039458f43af377676c2d3b0fbba751986be14a833  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEj8yhP+8dDC6RAI4Jdw96mlrhVgAFAmU3ydgACgkQdw96mlrh
VgDz7g//R9ykv7dH9NgA9l5ej0uHKkhaLeP9cy6Njx5iymNl7UuSCNOBLdVmRJQT
bQrF+FRFflUdjiZoo0BEYC9cZuLABUj0peBLM5OTkx942L0V+16845WOrcWxkjZw
cD0FXzl+63IIy+7SutA9E7R7B4LT3S8wci9sLiqN4seciQG3eECfLy4/V2+sXYar
0N9MpHwegwuiw7xtP61voXybiuoNmuLmaVp2x9TQJBK+Y/xMMyL7L4MuNjQfGepy
fGukAAFeLPdA+Z1GtbG8+sv/QSpsTjgdIyGyE2BTbiI51kh7133PoY7Tj2+hIqYI
DKVh3vcNz7X4nz5tn/47M5fp931vuHi71MhWkoRmn/YE/DoQp8Aj2cRSqI/N3/vl
JY02CyrBYFO6XOGJjNrKj/Njw0q0IM0FNVdvT8bG/gRetMJMtJqj7begZdSvx5Ou
IIluWZH+EfCWu8RT7wrHOu4x9RWYori1xQbKzVChcS4xY9eq/Atet8yjinTR+cd1
In+b2wwImm2sQIntnwqMmAPu6OFINS666zAw1d1yXdcFxaL8W8xMYAq9t+fJwiSc
kcexT26IKxxBmgsSH6YOBOwlwY4h2n89gkigwm4vxoPIMfGPdxewLz9E8p0f9eix
LFgaO2Hmt1enQbNXk281Sd/9WghyRq/DlAC1cX8txYisRA1wVjQ=
=EaCJ
-----END PGP SIGNATURE-----
```
