---
date: '2023-10-13T21:09:24.659Z'
category: release
title: Node v18.18.2 (LTS)
layout: blog-post
author: <PERSON>
---

### Notable Changes

The following CVEs are fixed in this release:

- [CVE-2023-44487](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2023-44487): `nghttp2` Security Release (High)
- [CVE-2023-45143](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2023-45143): `undici` Security Release (High)
- [CVE-2023-38552](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2023-38552): Integrity checks according to policies can be circumvented (Medium)
- [CVE-2023-39333](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2023-39333): Code injection via WebAssembly export names (Low)

More detailed information on each of the vulnerabilities can be found in [October 2023 Security Releases](/blog/vulnerability/october-2023-security-releases/) blog post.

### Commits

- \[[`55028468db`](https://github.com/nodejs/node/commit/55028468db)] - **deps**: update undici to v5.26.3 (Matteo Collina) [#50153](https://github.com/nodejs/node/pull/50153)
- \[[`a792bbc515`](https://github.com/nodejs/node/commit/a792bbc515)] - **deps**: update nghttp2 to 1.57.0 (James M Snell) [#50121](https://github.com/nodejs/node/pull/50121)
- \[[`f6444defa4`](https://github.com/nodejs/node/commit/f6444defa4)] - **deps**: update nghttp2 to 1.56.0 (Node.js GitHub Bot) [#49582](https://github.com/nodejs/node/pull/49582)
- \[[`7e9b08dfd4`](https://github.com/nodejs/node/commit/7e9b08dfd4)] - **deps**: update nghttp2 to 1.55.1 (Node.js GitHub Bot) [#48790](https://github.com/nodejs/node/pull/48790)
- \[[`85672c153f`](https://github.com/nodejs/node/commit/85672c153f)] - **deps**: update nghttp2 to 1.55.0 (Node.js GitHub Bot) [#48746](https://github.com/nodejs/node/pull/48746)
- \[[`300a902422`](https://github.com/nodejs/node/commit/300a902422)] - **deps**: update nghttp2 to 1.53.0 (Node.js GitHub Bot) [#47997](https://github.com/nodejs/node/pull/47997)
- \[[`7d83ed0bf6`](https://github.com/nodejs/node/commit/7d83ed0bf6)] - _**Revert**_ "**deps**: update nghttp2 to 1.55.0" (Richard Lau) [#50151](https://github.com/nodejs/node/pull/50151)
- \[[`1193ca5fdb`](https://github.com/nodejs/node/commit/1193ca5fdb)] - **lib**: let deps require `node` prefixed modules (Matthew Aitken) [#50047](https://github.com/nodejs/node/pull/50047)
- \[[`eaf9083cf1`](https://github.com/nodejs/node/commit/eaf9083cf1)] - **module**: fix code injection through export names (Tobias Nießen) [nodejs-private/node-private#461](https://github.com/nodejs-private/node-private/pull/461)
- \[[`1c538938cc`](https://github.com/nodejs/node/commit/1c538938cc)] - **policy**: use tamper-proof integrity check function (Tobias Nießen) [nodejs-private/node-private#462](https://github.com/nodejs-private/node-private/pull/462)

Windows 32-bit Installer: https://nodejs.org/dist/v18.18.2/node-v18.18.2-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v18.18.2/node-v18.18.2-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v18.18.2/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v18.18.2/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v18.18.2/node-v18.18.2.pkg \
macOS Apple Silicon 64-bit Binary: https://nodejs.org/dist/v18.18.2/node-v18.18.2-darwin-arm64.tar.gz \
macOS Intel 64-bit Binary: https://nodejs.org/dist/v18.18.2/node-v18.18.2-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v18.18.2/node-v18.18.2-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v18.18.2/node-v18.18.2-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v18.18.2/node-v18.18.2-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v18.18.2/node-v18.18.2-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v18.18.2/node-v18.18.2-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v18.18.2/node-v18.18.2-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v18.18.2/node-v18.18.2.tar.gz \
Other release files: https://nodejs.org/dist/v18.18.2/ \
Documentation: https://nodejs.org/docs/v18.18.2/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

8911c72a1395cdf88b826da49e1dc0af9700c9db880a69da24a9e061769c94f2  node-v18.18.2-aix-ppc64.tar.gz
9f982cc91b28778dd8638e4f94563b0c2a1da7aba62beb72bd427721035ab553  node-v18.18.2-darwin-arm64.tar.gz
890bc7a53375ba53f0860796c88f3e8ada0d307df25ac25cf85140ffacbfb0bf  node-v18.18.2-darwin-arm64.tar.xz
5bb8da908ed590e256a69bf2862238c8a67bc4600119f2f7721ca18a7c810c0f  node-v18.18.2-darwin-x64.tar.gz
6492bc80eb736f2694e9e78f624c0a34c404d19b844a4f23b0e9a81bd515ebcc  node-v18.18.2-darwin-x64.tar.xz
70c9164c1952a77ce06bd80e3507a11124cf60b6c82409e2c3ad1a6d5bc5a910  node-v18.18.2-headers.tar.gz
c2c1298a1ccd1bfb2cb76725fb87715cf88db2a2515d28d8a66376300de96d67  node-v18.18.2-headers.tar.xz
0c9a6502b66310cb26e12615b57304e91d92ac03d4adcb91c1906351d7928f0d  node-v18.18.2-linux-arm64.tar.gz
2e630e18548627f61eaf573233da7949dc0a1df5eef3f486fa9820c5f6c121aa  node-v18.18.2-linux-arm64.tar.xz
7a3b34a6fdb9514bc2374114ec6df3c36113dc5075c38b22763aa8f106783737  node-v18.18.2-linux-armv7l.tar.gz
663160a8a2bbc42c9d2b2591d7dc556d245254f7c5e5aafb4adcad791ed39875  node-v18.18.2-linux-armv7l.tar.xz
dbf0939c2ad50b74f2aaa005473b2e14c1cbe68318b69f4c1b4e6dda8e5aa43a  node-v18.18.2-linux-ppc64le.tar.gz
b0adff5cf5938266b711d6c724fb134d802e0dee40b3a3f73d162de1b3d11880  node-v18.18.2-linux-ppc64le.tar.xz
c5ed8da4272740190ce2a477096cb8486ade0d15e2b830f298d7d599c2e6cd97  node-v18.18.2-linux-s390x.tar.gz
c70ec2074b5e2b42c55bb4b8105418b67bf8a61c500d9376a07430dfcc341fdb  node-v18.18.2-linux-s390x.tar.xz
a44c3e7f8bf91e852c928e5d8bd67ca316b35e27eec1d8acbe3b9dbe03688dab  node-v18.18.2-linux-x64.tar.gz
75aba25ae76999309fc6c598efe56ce53fbfc221381a44a840864276264ab8ac  node-v18.18.2-linux-x64.tar.xz
01eba2502b9c60f20df0cd0a027d2eb6ea80c265e3cb05b2e14f14e5aedc025c  node-v18.18.2-win-x64.7z
3bb0e51e579a41a22b3bf6cb2f3e79c03801aa17acbe0ca00fc555d1282e7acd  node-v18.18.2-win-x64.zip
28422e9ebb45715e9744916299596519e80fc37680c06155b8e0108c7eb17974  node-v18.18.2-win-x86.7z
1ba446246ac47e25f165b8a00d5245d68980d747bc5feffbc421d003dd186f14  node-v18.18.2-win-x86.zip
221f2a904f13105122a108fb735a8f89615864c7bcba3842fa1ef684f136abe3  node-v18.18.2-x64.msi
77684d746d977404ee1832505ae64fb4cb8355eb12e4bf37c3036f7cf3b84e5b  node-v18.18.2-x86.msi
a7b27f8dbe7c73f357875a0e983ccab5ccc26f3e1e02cfc5c70404f3909d49dc  node-v18.18.2.pkg
509cd2cfc3a515bf2257ed3886b9fac64aeaac2a70ea59c0a6e02e2dbb722132  node-v18.18.2.tar.gz
7249e2f0af943ec38599504f4b2a2bd31fb938787291b6ccca6c8badf01e3b56  node-v18.18.2.tar.xz
54884183ff5108874c091746465e8156ae0acc68af589cc10bc41b3927db0f4a  win-x64/node.exe
57f0bf8dbc68fe3468105a7f9d8fe773220f135a171dde2682f30e118a6fc7d7  win-x64/node.lib
f1fbe0be82f279f281336a9e8b29a5b41363d6f1d4aef88937d57c9fce985515  win-x64/node_pdb.7z
07ce9bdc39f1a5dfd970837d1fc72c8461a010ed359f4dc12065ab917855d0cb  win-x64/node_pdb.zip
a5226bb0dd7bbd7c33dad6456579a7da3e05a8f8bb0a2019296c556acae411e8  win-x86/node.exe
62ee700c739ed89bfea9a5151be27c0ae035aa6dfb3efd7433076de1df389e35  win-x86/node.lib
289e324c58377c086b4abf83096f4f2690427af20b1343d186808b6e4b2a356b  win-x86/node_pdb.7z
021042696076e19e17ed67dc11bd2eee6eeccc7a3091bd070772b8b0630e8b34  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQGzBAEBCAAdFiEEiQwI24V5Fi/uDfnbi+q0389VXvQFAmUpr0EACgkQi+q0389V
XvR76Qv/e24PfcP9s+k6yc8NMMn2uyOBMLaxr7kA4/hE1GiK5TWXM+tEcrhmWQgK
Q9+z8NHpIgtDg4W8FAiwGAkz1ZE5XpekfBuYwcRV7qQVReeHYmTiwOflXfAhCrBs
kFELDS/5iduqspnkdZOQS+XJ4HwjHtrnNlI/Dzp27BbsdoSmZGYvPeYxfffeUtA4
zHnU8ckXEHjd7ThJuZkTV5r6Z7+RRkgT7bC6MVI2AHIxzFlL9xuMSXEkmLDricYB
aSrmFX/hJNB3RSgFRfA9qinLi/I4+i3zmupT7DylgpuE3O7CZ9NjkvXlg32NYDB1
/hMUsFxJF7fDVANiIbnCMzACIigu/MRScJ6eQ9TBsi5TWSV6E/xAshr2NkxC/KNa
Zy/YiSEVkBTLo+fWJcP4T/k7IOKDlrKYMaFDexzZI4DTSQesncOygqKtWyuimP0n
razlhNKfhwwijdkjQlZ/IM2Xf4zhSIgh4mTdNvWybMG8axXNpYr4fV1VIQjPA1jG
bnOwiDcW
=1sm8
-----END PGP SIGNATURE-----

```
