---
date: '2021-04-06T20:09:51.664Z'
category: release
title: Node v14.16.1 (LTS)
layout: blog-post
author: <PERSON><PERSON>
---

### Notable Changes

Vulnerabilities fixed:

- **CVE-2021-3450**: OpenSSL - CA certificate check bypass with X509_V_FLAG_X509_STRICT (High)
  - This is a vulnerability in OpenSSL which may be exploited through Node.js. You can read more about it in https://www.openssl.org/news/secadv/20210325.txt
  - Impacts:
    - All versions of the 15.x, 14.x, 12.x and 10.x releases lines
- **CVE-2021-3449**: OpenSSL - NULL pointer deref in signature_algorithms processing (High)
  - This is a vulnerability in OpenSSL which may be exploited through Node.js. You can read more about it in https://www.openssl.org/news/secadv/20210325.txt
  - Impacts:
    - All versions of the 15.x, 14.x, 12.x and 10.x releases lines
- **CVE-2020-7774**: npm upgrade - Update y18n to fix Prototype-Pollution (High)
  - This is a vulnerability in the y18n npm module which may be exploited by prototype pollution. You can read more about it in https://github.com/advisories/GHSA-c4w7-xm78-47vh
  - Impacts:
    - All versions of the 14.x, 12.x and 10.x releases lines

### Commits

- [[`467be7a950`](https://github.com/nodejs/node/commit/467be7a950)] - **deps**: upgrade npm to 6.14.12 (Ruy Adorno) [#37918](https://github.com/nodejs/node/pull/37918)
- [[`6bc8f58182`](https://github.com/nodejs/node/commit/6bc8f58182)] - **deps**: update archs files for OpenSSL-1.1.1k (Tobias Nießen) [#37938](https://github.com/nodejs/node/pull/37938)
- [[`403a014ef6`](https://github.com/nodejs/node/commit/403a014ef6)] - **deps**: upgrade openssl sources to 1.1.1k (Tobias Nießen) [#37938](https://github.com/nodejs/node/pull/37938)

Windows 32-bit Installer: https://nodejs.org/dist/v14.16.1/node-v14.16.1-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v14.16.1/node-v14.16.1-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v14.16.1/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v14.16.1/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v14.16.1/node-v14.16.1.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v14.16.1/node-v14.16.1-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v14.16.1/node-v14.16.1-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v14.16.1/node-v14.16.1-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v14.16.1/node-v14.16.1-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v14.16.1/node-v14.16.1-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v14.16.1/node-v14.16.1-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v14.16.1/node-v14.16.1-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v14.16.1/node-v14.16.1.tar.gz \
Other release files: https://nodejs.org/dist/v14.16.1/ \
Documentation: https://nodejs.org/docs/v14.16.1/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

79d7201c1cc0765248f6b96d6377aa1f02c27c12814275a93cb3eaa2a25eec2c  node-v14.16.1-aix-ppc64.tar.gz
b762b72fc149629b7e394ea9b75a093cad709a9f2f71480942945d8da0fc1218  node-v14.16.1-darwin-x64.tar.gz
72d890963c4497272eef3df1a1d46a8c64c4c356ed3f8c0ede5cb1ba8d8dd0f7  node-v14.16.1-darwin-x64.tar.xz
8aff317d131fd4959614953ccb819639c68c284d7c8203be23cb4659e3b5f52f  node-v14.16.1-headers.tar.gz
c5ec269e9a4d05b89013f4b7d1585d249d61809ea6f65591845dd426f00c15f0  node-v14.16.1-headers.tar.xz
58cb307666ed4aa751757577a563b8a1e5d4ee73a9fac2b495e5c463682a07d1  node-v14.16.1-linux-arm64.tar.gz
b4d474e79f7d33b3b4430fad25c3f836b82ce2d5bb30d4a2c9fa20df027e40da  node-v14.16.1-linux-arm64.tar.xz
54efe997dbeff971b1e39c8eb910566ecb68cfd6140a6b5c738265d4b5842d24  node-v14.16.1-linux-armv7l.tar.gz
1bc47a47f4415c64b6adb478857b7a1f6bc586ed61e946f03c25ea648867665e  node-v14.16.1-linux-armv7l.tar.xz
de6ccb9bf08520939cc2ae0507634015981604b5eb6912d031d4b7fe146f0de4  node-v14.16.1-linux-ppc64le.tar.gz
349e415bb7a95c0183ba474654c42f41ac0ceb02acca1609ff111f91e6d32189  node-v14.16.1-linux-ppc64le.tar.xz
4a78bb87682f55106d68e38f996adc0f5d7089ce62b8222150828582aabdf3eb  node-v14.16.1-linux-s390x.tar.gz
af9982fef32e4a3e4a5d66741dcf30ac9c27613bd73582fa1dae1fb25003047a  node-v14.16.1-linux-s390x.tar.xz
068400cb9f53d195444b9260fd106f7be83af62bb187932656b68166a2f87f44  node-v14.16.1-linux-x64.tar.gz
85a89d2f68855282c87851c882d4c4bbea4cd7f888f603722f0240a6e53d89df  node-v14.16.1-linux-x64.tar.xz
3dc7987ec419767afd07dfebb2f0dcd5ae27419939d49174e5b9efc1e3b4d45b  node-v14.16.1.pkg
5f5080427abddde7f22fd2ba77cd2b8a1f86253277a1eec54bc98a202728ce80  node-v14.16.1.tar.gz
e44adbbed6756c2c1a01258383e9f00df30c147b36e438f6369b5ef1069abac3  node-v14.16.1.tar.xz
3797bbe3c9ff6a0052eb550afcbf2af1ba7374743e54ce89039bbbcd3e988944  node-v14.16.1-win-x64.7z
e469db37b4df74627842d809566c651042d86f0e6006688f0f5fe3532c6dfa41  node-v14.16.1-win-x64.zip
005a8c367608d96eb13a79d78cf28df161ee8aa63766e6d130c7ae3977dd241a  node-v14.16.1-win-x86.7z
cfb3535a172fb792a63814deffde405466902359bedfbd884188f6fc56f97d64  node-v14.16.1-win-x86.zip
c90d32952154eb1ef3ebef5a5d6ec4b752e5d0f1520f9d2ebdef6685a2d3a4ec  node-v14.16.1-x64.msi
d9cad1fbbe479f39949b36ff10df9c9afe3498621d50426e7dc8eff1c6740636  node-v14.16.1-x86.msi
a2d5d5d4c68faf9ca14429c30d9b61cdf23e0a6f76dc3269aa2af34b3e72a1bd  win-x64/node.exe
c8a28cc134d7455d168de698b0cfc930870c9e3fd345bbd1f496d31b530fd41e  win-x64/node.lib
2a7a090edbcc97e0d1c40606b4c4549424d07971c7c5fcd5cb874ce472a1fe99  win-x64/node_pdb.7z
9de7516f6c91daa1d0466b23c592c0a4ce4f1e59f796b024b7a14798bd1afb2e  win-x64/node_pdb.zip
56d7208ca2d42a16292057d0452a958cb503beab6fc00922c85ecb9169c10f95  win-x86/node.exe
d0ab95fdb156a779340288cc9fe342d9976922edbc7d60992f9d7ebc572c52a3  win-x86/node.lib
bc3dbf099e1e2b4e041efab4ad0fe9584fcbb17ab0c6ea8919faafed626ba4f3  win-x86/node_pdb.7z
e89aee0d4cea68186b9c23a503a17fcbcdfd37e0e27e9ec08cd2bc9ebb68c9a2  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQEzBAEBCAAdFiEEDv/hvO/ZyE49CYFSkzsB9AtcqUYFAmBsvX8ACgkQkzsB9Atc
qUau0gf+LXe1QZmLbMS1LSwgd6db9yiGdaQfgAdvd952ZLXAHUOKFfbwlR8GchLr
rdZe0w/6BIpC9JfCZihQ/uRBkQnwHNx135FWQpaDHlEpAfJ3r9qlGORXtOskLdPI
Q2mYJgdgxSqMC2pxwJYZq36aPd+pu8cDBgJPKSiE7pnJltUrHj5M0axXTRRk1izP
f2BCK4YMQlr6PZgBHbpBBHaU29HlKKP89OCPpr2KLyNZ2OVOpEQltn1TIfJQc4/C
448jhTXAnBlFrHmbV2xVamD0K8GHgZcvLTySlf58GN9FHUuiPGcbI5dfi1nNleoh
mpPtZ4kD6y9W3kRh1nU/sV9ijGeYDQ==
=g8kB
-----END PGP SIGNATURE-----

```
