---
date: '2016-10-12T21:04:45.260Z'
category: release
title: Node v6.8.0 (Current)
layout: blog-post
author: <PERSON>
---

### Notable changes

- **fs**:
  - `SyncWriteStream` now inherits from `Stream.Writable`. (<PERSON>) [#8830](https://github.com/nodejs/node/pull/8830) \Practically, this means that when stdio is piped to a file, stdout and stderr will still be `Writable` streams.
  - `fs.existsSync()` has been undeprecated. `fs.exists()` remains deprecated. (<PERSON>) [#8364](https://github.com/nodejs/node/pull/8364)
- **http**: `http.request()` now accepts a `timeout` option. (<PERSON>) [#8101](https://github.com/nodejs/node/pull/8101)
- **module**: The module loader now maintains its own realpath cache. (<PERSON>) [#8100](https://github.com/nodejs/node/pull/8100)
- **npm**: Upgraded to 3.10.8 (<PERSON>) [#8706](https://github.com/nodejs/node/pull/8706)
- **stream**: `Duplex` streams now show proper `instanceof Stream.Writable`. (Anna Henningsen) [#8834](https://github.com/nodejs/node/pull/8834)
- **timers**: Improved `setTimeout`/`Interval` performance by up to 22%. (Brian White) [#8661](https://github.com/nodejs/node/pull/8661)

### Commits

- [[`1a6e8983a6`](https://github.com/nodejs/node/commit/1a6e8983a6)] - **benchmark**: add benchmarks for `Buffer.from()` (Anna Henningsen) [#8738](https://github.com/nodejs/node/pull/8738)
- [[`882f8b3d6c`](https://github.com/nodejs/node/commit/882f8b3d6c)] - **benchmark**: use 'yes' instead of echo in a loop (Bartosz Sosnowski) [#8721](https://github.com/nodejs/node/pull/8721)
- [[`999f727bb5`](https://github.com/nodejs/node/commit/999f727bb5)] - **benchmark**: add benchmark for destructuring object (Fangdun Cai (Fundon)) [#8680](https://github.com/nodejs/node/pull/8680)
- [[`5c10898e31`](https://github.com/nodejs/node/commit/5c10898e31)] - **buffer**: fix check for `.buffer` property (Ojas Shirekar) [#8739](https://github.com/nodejs/node/pull/8739)
- [[`b9c2270502`](https://github.com/nodejs/node/commit/b9c2270502)] - **buffer**: fix performance regression (Michaël Zasso) [#8754](https://github.com/nodejs/node/pull/8754)
- [[`3fcdf4e80a`](https://github.com/nodejs/node/commit/3fcdf4e80a)] - **buffer**: remove unnecessary argument check (Michaël Zasso) [#8552](https://github.com/nodejs/node/pull/8552)
- [[`ee319b739c`](https://github.com/nodejs/node/commit/ee319b739c)] - **buffer**: add isSharedArrayBuffer checks (Ojas Shirekar) [#8510](https://github.com/nodejs/node/pull/8510)
- [[`38fdbcd7f7`](https://github.com/nodejs/node/commit/38fdbcd7f7)] - **build**: make addons build dep. on node_version.h (Anna Henningsen) [#8861](https://github.com/nodejs/node/pull/8861)
- [[`f057d193ec`](https://github.com/nodejs/node/commit/f057d193ec)] - **build**: run cctests as part of test-ci target (Ben Noordhuis) [#8034](https://github.com/nodejs/node/pull/8034)
- [[`a202be6690`](https://github.com/nodejs/node/commit/a202be6690)] - **build**: don't build icu with -fno-rtti (Ben Noordhuis) [#8886](https://github.com/nodejs/node/pull/8886)
- [[`296254f141`](https://github.com/nodejs/node/commit/296254f141)] - **build**: remove VS 2013 switch from vcbuild.bat (Ben Noordhuis) [#8067](https://github.com/nodejs/node/pull/8067)
- [[`a425c4da98`](https://github.com/nodejs/node/commit/a425c4da98)] - **build**: run `npm install` for doc builds in tarball (Anna Henningsen) [#8413](https://github.com/nodejs/node/pull/8413)
- [[`3d3bce6ca1`](https://github.com/nodejs/node/commit/3d3bce6ca1)] - **cluster**: remove unused backlog argument (Brian White) [#8877](https://github.com/nodejs/node/pull/8877)
- [[`23a851dfe6`](https://github.com/nodejs/node/commit/23a851dfe6)] - **deps**: avoid single-byte buffer overwrite in ares_create_query (Daniel Stenberg) [#8849](https://github.com/nodejs/node/pull/8849)
- [[`46af58898c`](https://github.com/nodejs/node/commit/46af58898c)] - **deps**: make gtest output tap (Ben Noordhuis) [#8034](https://github.com/nodejs/node/pull/8034)
- [[`9d41e8913f`](https://github.com/nodejs/node/commit/9d41e8913f)] - **deps**: bump V8 patch to 84 (Myles Borins) [#8851](https://github.com/nodejs/node/pull/8851)
- [[`4d41bd9c68`](https://github.com/nodejs/node/commit/4d41bd9c68)] - **deps**: hotfix upgrade npm tap version for tests (Kat Marchán) [#8706](https://github.com/nodejs/node/pull/8706)
- [[`9ecfc32fde`](https://github.com/nodejs/node/commit/9ecfc32fde)] - **deps**: upgrade npm to 3.10.8 (Kat Marchán) [#8706](https://github.com/nodejs/node/pull/8706)
- [[`c4d9b54f75`](https://github.com/nodejs/node/commit/c4d9b54f75)] - **dgram**: use Buffer.alloc(0) for zero-size buffers (Сковорода Никита Андреевич) [#8751](https://github.com/nodejs/node/pull/8751)
- [[`e1a774d314`](https://github.com/nodejs/node/commit/e1a774d314)] - **dns**: remove internal variable from makeAsync (yorkie) [#8800](https://github.com/nodejs/node/pull/8800)
- [[`787558935c`](https://github.com/nodejs/node/commit/787558935c)] - **dns**: tweak regex for IPv6 addresses (Luigi Pinca) [#8665](https://github.com/nodejs/node/pull/8665)
- [[`4e8c03707a`](https://github.com/nodejs/node/commit/4e8c03707a)] - **dns**: handle array holes in setServers() (cjihrig) [#8567](https://github.com/nodejs/node/pull/8567)
- [[`5e6be7b4c2`](https://github.com/nodejs/node/commit/5e6be7b4c2)] - **doc**: fix typo in email address in README (Rich Trott) [#8941](https://github.com/nodejs/node/pull/8941)
- [[`72ed6aa205`](https://github.com/nodejs/node/commit/72ed6aa205)] - **doc**: make node(1) more consistent with tradition (Alex Jordan) [#8902](https://github.com/nodejs/node/pull/8902)
- [[`2fad4d29b0`](https://github.com/nodejs/node/commit/2fad4d29b0)] - **doc**: add example for file existence with fs.stat (Christopn Noelke) [#8585](https://github.com/nodejs/node/pull/8585)
- [[`0bdc363205`](https://github.com/nodejs/node/commit/0bdc363205)] - **doc**: add `added:` information for globals (Luigi Pinca) [#8901](https://github.com/nodejs/node/pull/8901)
- [[`bc2dd3e467`](https://github.com/nodejs/node/commit/bc2dd3e467)] - **doc**: change ./node to node in debugger.md (AnnaMag) [#8943](https://github.com/nodejs/node/pull/8943)
- [[`b212b9b44b`](https://github.com/nodejs/node/commit/b212b9b44b)] - **doc**: add CTC meeting minutes 2016-09-07 (Josh Gavant) [#8499](https://github.com/nodejs/node/pull/8499)
- [[`c31a3178e8`](https://github.com/nodejs/node/commit/c31a3178e8)] - **doc**: fixup link in fs.md (ss22ever) [#8940](https://github.com/nodejs/node/pull/8940)
- [[`e7039cdbb8`](https://github.com/nodejs/node/commit/e7039cdbb8)] - **doc**: fix markdown formatting in url.md (Bryan Bess) [#8933](https://github.com/nodejs/node/pull/8933)
- [[`ff9a84d324`](https://github.com/nodejs/node/commit/ff9a84d324)] - **doc**: update punctuation in README.md (Abner Chou) [#8892](https://github.com/nodejs/node/pull/8892)
- [[`c36c8dc3cc`](https://github.com/nodejs/node/commit/c36c8dc3cc)] - **doc**: add documentation for test common module (Paul Grock) [#8840](https://github.com/nodejs/node/pull/8840)
- [[`a604a82186`](https://github.com/nodejs/node/commit/a604a82186)] - **doc**: recommend using port 0 instead of common.PORT (Niklas Ingholt) [#8694](https://github.com/nodejs/node/pull/8694)
- [[`77fc5caceb`](https://github.com/nodejs/node/commit/77fc5caceb)] - **doc**: add CTC meeting minutes 2016-09-14 (Josh Gavant) [#8726](https://github.com/nodejs/node/pull/8726)
- [[`505b88d3c3`](https://github.com/nodejs/node/commit/505b88d3c3)] - **doc**: add CTC meeting minutes 2016-09-21 (Josh Gavant) [#8727](https://github.com/nodejs/node/pull/8727)
- [[`3e8c8f90bc`](https://github.com/nodejs/node/commit/3e8c8f90bc)] - **doc**: fix typo in repl doc (Franziska Hinkelmann) [#8826](https://github.com/nodejs/node/pull/8826)
- [[`a00ae75805`](https://github.com/nodejs/node/commit/a00ae75805)] - **doc**: improve documentation for commit subject line (Luigi Pinca) [#8546](https://github.com/nodejs/node/pull/8546)
- [[`aaebbf9708`](https://github.com/nodejs/node/commit/aaebbf9708)] - **doc**: encourage 2FA before onboarding (Rich Trott) [#8776](https://github.com/nodejs/node/pull/8776)
- [[`f07054dd49`](https://github.com/nodejs/node/commit/f07054dd49)] - **doc**: add optional step to onboarding doc (Rich Trott) [#8774](https://github.com/nodejs/node/pull/8774)
- [[`dceaa0ba4a`](https://github.com/nodejs/node/commit/dceaa0ba4a)] - **doc**: remove failing workaround in BUILDING.md (Christopher Fujino) [#8763](https://github.com/nodejs/node/pull/8763)
- [[`0522aa0dc0`](https://github.com/nodejs/node/commit/0522aa0dc0)] - **doc**: add commit formats for release blog posts (fen) [#8631](https://github.com/nodejs/node/pull/8631)
- [[`98e425eed4`](https://github.com/nodejs/node/commit/98e425eed4)] - **doc**: fix title level at tls.md (yorkie) [#8782](https://github.com/nodejs/node/pull/8782)
- [[`e7c0f34f20`](https://github.com/nodejs/node/commit/e7c0f34f20)] - **doc**: add added: info for crypto.timingSafeEqual() (Marc-Aurèle DARCHE) [#8796](https://github.com/nodejs/node/pull/8796)
- [[`4fb051426c`](https://github.com/nodejs/node/commit/4fb051426c)] - **doc**: enable no-file-name-articles remark-lint rule (Сковорода Никита Андреевич) [#8713](https://github.com/nodejs/node/pull/8713)
- [[`4699e3022b`](https://github.com/nodejs/node/commit/4699e3022b)] - **doc**: enable first-heading-level remark-lint rule (Сковорода Никита Андреевич) [#8716](https://github.com/nodejs/node/pull/8716)
- [[`3aec6a68bf`](https://github.com/nodejs/node/commit/3aec6a68bf)] - **doc**: improve child_process doc types (yorkie) [#8741](https://github.com/nodejs/node/pull/8741)
- [[`6fab334a73`](https://github.com/nodejs/node/commit/6fab334a73)] - **doc**: fix example in stream doc (Luigi Pinca) [#8378](https://github.com/nodejs/node/pull/8378)
- [[`f13089b834`](https://github.com/nodejs/node/commit/f13089b834)] - **doc**: update BUILDING.md (rainabba) [#8704](https://github.com/nodejs/node/pull/8704)
- [[`5c014bb532`](https://github.com/nodejs/node/commit/5c014bb532)] - **doc**: standardize on `make -j8` (Rich Trott) [#8678](https://github.com/nodejs/node/pull/8678)
- [[`98ca442cae`](https://github.com/nodejs/node/commit/98ca442cae)] - **doc**: add CTC meeting minutes 2016-08-24 (Josh Gavant) [#8423](https://github.com/nodejs/node/pull/8423)
- [[`28264f8da4`](https://github.com/nodejs/node/commit/28264f8da4)] - **doc**: add eugeneo to collaborators (Eugene Ostroukhov) [#8696](https://github.com/nodejs/node/pull/8696)
- [[`85ee89edd6`](https://github.com/nodejs/node/commit/85ee89edd6)] - **doc**: add ak239 to collaborators (Aleksey Kozyatinskiy) [#8676](https://github.com/nodejs/node/pull/8676)
- [[`5dd6229aeb`](https://github.com/nodejs/node/commit/5dd6229aeb)] - **doc**: clarify fs.utimes() arguments (Danny Guo) [#8651](https://github.com/nodejs/node/pull/8651)
- [[`42386d7229`](https://github.com/nodejs/node/commit/42386d7229)] - **doc**: add link to help repo in README (Rich Trott) [#8570](https://github.com/nodejs/node/pull/8570)
- [[`2f6101ed10`](https://github.com/nodejs/node/commit/2f6101ed10)] - **doc**: fix broken link in timers doc (Ltrlg) [#8562](https://github.com/nodejs/node/pull/8562)
- [[`e16f95db4d`](https://github.com/nodejs/node/commit/e16f95db4d)] - **doc**: update exercise portion of onboarding doc (Rich Trott) [#8559](https://github.com/nodejs/node/pull/8559)
- [[`a91050c57b`](https://github.com/nodejs/node/commit/a91050c57b)] - **doc**: fix typo in path doc (Kalman Hazins) [#8527](https://github.com/nodejs/node/pull/8527)
- [[`64b79eba42`](https://github.com/nodejs/node/commit/64b79eba42)] - **doc**: remove extra comma in cluster docs (Teddy Katz) [#8557](https://github.com/nodejs/node/pull/8557)
- [[`2d23227060`](https://github.com/nodejs/node/commit/2d23227060)] - **doc**: fix a formatting error in buffer.md (Сковорода Никита Андреевич) [#8553](https://github.com/nodejs/node/pull/8553)
- [[`eebecef9a4`](https://github.com/nodejs/node/commit/eebecef9a4)] - **doc**: link onboarding to contributing guide (Rich Trott) [#8529](https://github.com/nodejs/node/pull/8529)
- [[`8a1f74338c`](https://github.com/nodejs/node/commit/8a1f74338c)] - **doc**: remove duplicate content from readline doc (Italo A. Casas) [#8497](https://github.com/nodejs/node/pull/8497)
- [[`71dd4c193a`](https://github.com/nodejs/node/commit/71dd4c193a)] - **doc**: capitalize arguments' type names in url doc (yorkie) [#8489](https://github.com/nodejs/node/pull/8489)
- [[`8e28e03fb9`](https://github.com/nodejs/node/commit/8e28e03fb9)] - **doc**: add gibfahn to collaborators (Gibson Fahnestock) [#8533](https://github.com/nodejs/node/pull/8533)
- [[`c96618f241`](https://github.com/nodejs/node/commit/c96618f241)] - **doc**: add imyller to collaborators (Ilkka Myller) [#8530](https://github.com/nodejs/node/pull/8530)
- [[`929d9bb222`](https://github.com/nodejs/node/commit/929d9bb222)] - **doc**: standardize rest parameters (Timothy Gu) [#8485](https://github.com/nodejs/node/pull/8485)
- [[`125df45b80`](https://github.com/nodejs/node/commit/125df45b80)] - **doc**: add not-an-aardvark to collaborators (not-an-aardvark) [#8525](https://github.com/nodejs/node/pull/8525)
- [[`3eb93e1fe8`](https://github.com/nodejs/node/commit/3eb93e1fe8)] - **doc**: add example for running with v8-inspector (Franziska Hinkelmann) [#8845](https://github.com/nodejs/node/pull/8845)
- [[`d6dcbc212b`](https://github.com/nodejs/node/commit/d6dcbc212b)] - **doc,tool**: add tls.TLSSocket to typeMap (yorkie) [#8742](https://github.com/nodejs/node/pull/8742)
- [[`d69570d5fc`](https://github.com/nodejs/node/commit/d69570d5fc)] - **doc,tool**: add ref to Integer (yorkie) [#8740](https://github.com/nodejs/node/pull/8740)
- [[`688abac7b2`](https://github.com/nodejs/node/commit/688abac7b2)] - **(SEMVER-MINOR)** **fs**: make `SyncWriteStream` inherit from `Writable` (Anna Henningsen) [#8830](https://github.com/nodejs/node/pull/8830)
- [[`07d97f4f3e`](https://github.com/nodejs/node/commit/07d97f4f3e)] - **fs**: fix handling of `uv_stat_t` fields (Anna Henningsen) [#8515](https://github.com/nodejs/node/pull/8515)
- [[`14e2d67776`](https://github.com/nodejs/node/commit/14e2d67776)] - **(SEMVER-MINOR)** **fs,doc**: undeprecate existsSync (Dan Fabulich) [#8364](https://github.com/nodejs/node/pull/8364)
- [[`980c1edf63`](https://github.com/nodejs/node/commit/980c1edf63)] - **(SEMVER-MINOR)** **fs,module**: add module-loader-only realpath cache (Anna Henningsen) [#8100](https://github.com/nodejs/node/pull/8100)
- [[`ee7af01b93`](https://github.com/nodejs/node/commit/ee7af01b93)] - **(SEMVER-MINOR)** **http**: socket connection timeout for http request (Rene Weber) [#8101](https://github.com/nodejs/node/pull/8101)
- [[`7a59449478`](https://github.com/nodejs/node/commit/7a59449478)] - **https**: fix memory leak with https.request() (Ilkka Myller) [#8647](https://github.com/nodejs/node/pull/8647)
- [[`573d8bcee4`](https://github.com/nodejs/node/commit/573d8bcee4)] - **inspector**: fix minor issues (Brian White) [#8890](https://github.com/nodejs/node/pull/8890)
- [[`f4f9cf779f`](https://github.com/nodejs/node/commit/f4f9cf779f)] - **inspector**: build file cleanup (Eugene Ostroukhov) [#8753](https://github.com/nodejs/node/pull/8753)
- [[`e80ae1350c`](https://github.com/nodejs/node/commit/e80ae1350c)] - **inspector**: address race conditions (Eugene Ostroukhov) [#8672](https://github.com/nodejs/node/pull/8672)
- [[`f817875235`](https://github.com/nodejs/node/commit/f817875235)] - **inspector**: wait for both sides closing (Eugene Ostroukhov) [#8505](https://github.com/nodejs/node/pull/8505)
- [[`4ed46b47a1`](https://github.com/nodejs/node/commit/4ed46b47a1)] - **inspector**: report default context (Eugene Ostroukhov) [#8502](https://github.com/nodejs/node/pull/8502)
- [[`b05ce842ce`](https://github.com/nodejs/node/commit/b05ce842ce)] - **inspector**: zero out structure members (Eugene Ostroukhov) [#8536](https://github.com/nodejs/node/pull/8536)
- [[`0b90ff7a8d`](https://github.com/nodejs/node/commit/0b90ff7a8d)] - **inspector**: introduce a smoke test (Eugene Ostroukhov) [#8429](https://github.com/nodejs/node/pull/8429)
- [[`3222b66abe`](https://github.com/nodejs/node/commit/3222b66abe)] - **inspector**: fix tests on Windows (Eugene Ostroukhov) [#8528](https://github.com/nodejs/node/pull/8528)
- [[`a1925a7955`](https://github.com/nodejs/node/commit/a1925a7955)] - **lib**: minor improvements to bootstrap_node.js (Rémy MEJA) [#8906](https://github.com/nodejs/node/pull/8906)
- [[`313a45da24`](https://github.com/nodejs/node/commit/313a45da24)] - **lib**: changed var to const in linkedlist (Adri Van Houdt) [#8609](https://github.com/nodejs/node/pull/8609)
- [[`6cd5588a67`](https://github.com/nodejs/node/commit/6cd5588a67)] - **lib**: fix TypeError in v8-polyfill (Wyatt Preul) [#8863](https://github.com/nodejs/node/pull/8863)
- [[`ba361a2aa0`](https://github.com/nodejs/node/commit/ba361a2aa0)] - **lib**: remove let from for loops (Myles Borins) [#8873](https://github.com/nodejs/node/pull/8873)
- [[`beb288b639`](https://github.com/nodejs/node/commit/beb288b639)] - **lib**: changed var to const in internal/v8_polyfill (Adri Van Houdt) [#8615](https://github.com/nodejs/node/pull/8615)
- [[`858a7bbacf`](https://github.com/nodejs/node/commit/858a7bbacf)] - **lib**: changed var to const in bootstrap_node.js (Adri Van Houdt) [#8588](https://github.com/nodejs/node/pull/8588)
- [[`31232adebb`](https://github.com/nodejs/node/commit/31232adebb)] - **module**: fix comment from "read-only" to "shallow" (Bryan English) [#8887](https://github.com/nodejs/node/pull/8887)
- [[`0eaf3ff53c`](https://github.com/nodejs/node/commit/0eaf3ff53c)] - **path**: fallback to process cwd when resolving drive cwd (Jason Ginchereau) [#8541](https://github.com/nodejs/node/pull/8541)
- [[`d72a7b3d0c`](https://github.com/nodejs/node/commit/d72a7b3d0c)] - **path**: fix path.relative UNC path result (Jason Ginchereau) [#8523](https://github.com/nodejs/node/pull/8523)
- [[`e0c10f63b0`](https://github.com/nodejs/node/commit/e0c10f63b0)] - **process**: changed var to const in internal/process.js (Adri Van Houdt) [#8614](https://github.com/nodejs/node/pull/8614)
- [[`37ce6da59a`](https://github.com/nodejs/node/commit/37ce6da59a)] - **process**: changed var to const in internal/v8_prof_processor (Adri Van Houdt) [#8619](https://github.com/nodejs/node/pull/8619)
- [[`e8f1cf1bd8`](https://github.com/nodejs/node/commit/e8f1cf1bd8)] - **process**: changed var to const in internal/process/promises (Adri Van Houdt) [#8620](https://github.com/nodejs/node/pull/8620)
- [[`4c194ee7bd`](https://github.com/nodejs/node/commit/4c194ee7bd)] - **readline**: fix `concievably` typo in readline.js (Miguel Angel Asencio Hurtado) [#8953](https://github.com/nodejs/node/pull/8953)
- [[`8c91a9b848`](https://github.com/nodejs/node/commit/8c91a9b848)] - **repl**: improve .help message (Roman Reiss) [#8519](https://github.com/nodejs/node/pull/8519)
- [[`443bedeb68`](https://github.com/nodejs/node/commit/443bedeb68)] - **src**: remove out-of-date TODO comment (Daniel Bevenius) [#9000](https://github.com/nodejs/node/pull/9000)
- [[`59aa103df2`](https://github.com/nodejs/node/commit/59aa103df2)] - **src**: fix typo in #endif comment (Juan Andres Andrango) [#8989](https://github.com/nodejs/node/pull/8989)
- [[`8a2ba6fe83`](https://github.com/nodejs/node/commit/8a2ba6fe83)] - **src**: fix build for older clang (Zach Bjornson) [#7645](https://github.com/nodejs/node/pull/7645)
- [[`d8df78c573`](https://github.com/nodejs/node/commit/d8df78c573)] - **src**: remove unused function declaration (Brian White) [#8878](https://github.com/nodejs/node/pull/8878)
- [[`a6b9ffbf5b`](https://github.com/nodejs/node/commit/a6b9ffbf5b)] - **src**: refactor reading of options in contextify (Franziska Hinkelmann) [#8850](https://github.com/nodejs/node/pull/8850)
- [[`324c8b5f7e`](https://github.com/nodejs/node/commit/324c8b5f7e)] - **src**: fixes misplaced comment (Madhav Gharmalkar) [#8860](https://github.com/nodejs/node/pull/8860)
- [[`86b9db601d`](https://github.com/nodejs/node/commit/86b9db601d)] - **src**: add missing length argument to send comment (Daniel Bevenius) [#8816](https://github.com/nodejs/node/pull/8816)
- [[`aa11205f71`](https://github.com/nodejs/node/commit/aa11205f71)] - **src**: rename CHECK_NOT_OOB() macro (Ben Noordhuis) [#8784](https://github.com/nodejs/node/pull/8784)
- [[`8be818eb07`](https://github.com/nodejs/node/commit/8be818eb07)] - **src**: fix minor typo in comments (Daniel Bevenius) [#8736](https://github.com/nodejs/node/pull/8736)
- [[`41ad6e3965`](https://github.com/nodejs/node/commit/41ad6e3965)] - **src**: rename handle\_\_ to handle\_ in HandleWrap (Daniel Bevenius) [#8712](https://github.com/nodejs/node/pull/8712)
- [[`9205edc35c`](https://github.com/nodejs/node/commit/9205edc35c)] - **src**: don't abort when c-ares initialization fails (Ben Noordhuis) [#8710](https://github.com/nodejs/node/pull/8710)
- [[`6ddfe89fdf`](https://github.com/nodejs/node/commit/6ddfe89fdf)] - **src**: remove VS 2013 compatibility hacks (Ben Noordhuis) [#8067](https://github.com/nodejs/node/pull/8067)
- [[`a9491f1604`](https://github.com/nodejs/node/commit/a9491f1604)] - **src**: make ReqWrap req\_ member private (Daniel Bevenius) [#8532](https://github.com/nodejs/node/pull/8532)
- [[`5ebce30468`](https://github.com/nodejs/node/commit/5ebce30468)] - **src**: remove unneeded ABORT after CHECK (yorkie) [#8593](https://github.com/nodejs/node/pull/8593)
- [[`2dbef79ca7`](https://github.com/nodejs/node/commit/2dbef79ca7)] - **src**: handle thrown errors in CopyProperties() (cjihrig) [#8649](https://github.com/nodejs/node/pull/8649)
- [[`52f0f64e79`](https://github.com/nodejs/node/commit/52f0f64e79)] - **src**: use MaybeStackBuffer on DoSend/Writev (Paul Kiddie) [#8626](https://github.com/nodejs/node/pull/8626)
- [[`a62999ac70`](https://github.com/nodejs/node/commit/a62999ac70)] - **src**: add /json/protocol endpoint to inspector (Ben Noordhuis) [#7491](https://github.com/nodejs/node/pull/7491)
- [[`4e7c67cf55`](https://github.com/nodejs/node/commit/4e7c67cf55)] - **(SEMVER-MINOR)** **stream**: proper `instanceof` for `Writable`s (Anna Henningsen) [#8834](https://github.com/nodejs/node/pull/8834)
- [[`56951b4367`](https://github.com/nodejs/node/commit/56951b4367)] - **test**: add coverage for spawnSync() killSignal (cjihrig) [#8960](https://github.com/nodejs/node/pull/8960)
- [[`05f74120e8`](https://github.com/nodejs/node/commit/05f74120e8)] - **test**: refactor `assert` in internet test-dns.js (Junshu Okamoto) [#8980](https://github.com/nodejs/node/pull/8980)
- [[`1a4207d91d`](https://github.com/nodejs/node/commit/1a4207d91d)] - **test**: various test improvements (James M Snell) [#8468](https://github.com/nodejs/node/pull/8468)
- [[`c4f0bb237a`](https://github.com/nodejs/node/commit/c4f0bb237a)] - **test**: expand test coverage for url.js (Junshu Okamoto) [#8976](https://github.com/nodejs/node/pull/8976)
- [[`4e9b6a0022`](https://github.com/nodejs/node/commit/4e9b6a0022)] - **test**: fix test-child-process-fork-regr-gh-2847 (Santiago Gimeno) [#8954](https://github.com/nodejs/node/pull/8954)
- [[`b579fcab45`](https://github.com/nodejs/node/commit/b579fcab45)] - **test**: remove FIXME pummel/test-tls-securepair-client (Alfred Cepeda) [#8757](https://github.com/nodejs/node/pull/8757)
- [[`9b0733fd49`](https://github.com/nodejs/node/commit/9b0733fd49)] - **test**: run faster and cleanup after run (Wyatt Preul) [#8848](https://github.com/nodejs/node/pull/8848)
- [[`df0211d95e`](https://github.com/nodejs/node/commit/df0211d95e)] - **test**: refactor test-net-server-max-connections (Rich Trott) [#8931](https://github.com/nodejs/node/pull/8931)
- [[`147a06d4a5`](https://github.com/nodejs/node/commit/147a06d4a5)] - **test**: enable addons test to pass with debug build (Daniel Bevenius) [#8836](https://github.com/nodejs/node/pull/8836)
- [[`636026a22d`](https://github.com/nodejs/node/commit/636026a22d)] - **test**: remove blank lines at end of files (Rich Trott) [#8920](https://github.com/nodejs/node/pull/8920)
- [[`93c48743f1`](https://github.com/nodejs/node/commit/93c48743f1)] - **test**: refactor test-file-write-stream (Sudaraka Wijesinghe) [#8894](https://github.com/nodejs/node/pull/8894)
- [[`516486526f`](https://github.com/nodejs/node/commit/516486526f)] - **test**: cleanup/update test-dgram-empty-packet.js (Michael Macherey) [#8896](https://github.com/nodejs/node/pull/8896)
- [[`2f0f596e07`](https://github.com/nodejs/node/commit/2f0f596e07)] - **test**: fix child-process-uid-gid on Windows (Michaël Zasso) [#8924](https://github.com/nodejs/node/pull/8924)
- [[`52d0424c56`](https://github.com/nodejs/node/commit/52d0424c56)] - **test**: mark test-tick-processor-unknown flaky (Rich Trott) [#8900](https://github.com/nodejs/node/pull/8900)
- [[`424c126742`](https://github.com/nodejs/node/commit/424c126742)] - **test**: fix -Wformat warnings in inspector cctest (Ben Noordhuis) [#8034](https://github.com/nodejs/node/pull/8034)
- [[`76f80a3f74`](https://github.com/nodejs/node/commit/76f80a3f74)] - **test**: fix running child-process-uid-gid as root (Wyatt Preul) [#8864](https://github.com/nodejs/node/pull/8864)
- [[`11ba56c83d`](https://github.com/nodejs/node/commit/11ba56c83d)] - **test**: expand test coverage for url.js (Junshu Okamoto) [#8859](https://github.com/nodejs/node/pull/8859)
- [[`c9450cef1b`](https://github.com/nodejs/node/commit/c9450cef1b)] - **test**: clean up test-timers-immediate (Rich Trott) [#8857](https://github.com/nodejs/node/pull/8857)
- [[`17922de555`](https://github.com/nodejs/node/commit/17922de555)] - **test**: add and assert readable/writable arguments (Daniel Bevenius) [#8815](https://github.com/nodejs/node/pull/8815)
- [[`bc710ef461`](https://github.com/nodejs/node/commit/bc710ef461)] - **test**: cleanup/update test-os.js (Mike Woods) [#8761](https://github.com/nodejs/node/pull/8761)
- [[`fc42825530`](https://github.com/nodejs/node/commit/fc42825530)] - **test**: modernize syntax, add strict checks (Lydia Kats) [#8841](https://github.com/nodejs/node/pull/8841)
- [[`72de8594fe`](https://github.com/nodejs/node/commit/72de8594fe)] - **test**: use common.skip for tap skip output (Lydia Kats) [#8841](https://github.com/nodejs/node/pull/8841)
- [[`4fa0fc59cd`](https://github.com/nodejs/node/commit/4fa0fc59cd)] - **test**: stream writable ended state (Italo A. Casas) [#8778](https://github.com/nodejs/node/pull/8778)
- [[`6dbda6aa86`](https://github.com/nodejs/node/commit/6dbda6aa86)] - **test**: clean up test-buffer-badhex (Jeremiah Senkpiel) [#7773](https://github.com/nodejs/node/pull/7773)
- [[`af092f1fc0`](https://github.com/nodejs/node/commit/af092f1fc0)] - **test**: cleanup test-net-server-address.js (Akito Ito) [#8586](https://github.com/nodejs/node/pull/8586)
- [[`af84528d41`](https://github.com/nodejs/node/commit/af84528d41)] - **test**: replace indexOf, assert.equal, add mustCall() (Richard Hong) [#8766](https://github.com/nodejs/node/pull/8766)
- [[`2e95b0e24b`](https://github.com/nodejs/node/commit/2e95b0e24b)] - **test**: fixed FIXME in test-repl-persistent-history (Alfred Cepeda) [#8756](https://github.com/nodejs/node/pull/8756)
- [[`76fd7db521`](https://github.com/nodejs/node/commit/76fd7db521)] - **test**: update var to const, use arrow functions (Matt Lang) [#8595](https://github.com/nodejs/node/pull/8595)
- [[`5bd13a3d6c`](https://github.com/nodejs/node/commit/5bd13a3d6c)] - **test**: cleanup parallel/test-fs-readfile-unlink.js (nohmapp) [#8764](https://github.com/nodejs/node/pull/8764)
- [[`f523b82c7b`](https://github.com/nodejs/node/commit/f523b82c7b)] - **test**: cleanup parallel/test-file-write-stream2.js (Jenna Vuong) [#8770](https://github.com/nodejs/node/pull/8770)
- [[`9252e7a52d`](https://github.com/nodejs/node/commit/9252e7a52d)] - **test**: cleanup parallel/test-fs-realpath.js (mpmckenna8) [#8769](https://github.com/nodejs/node/pull/8769)
- [[`6ba8aa963e`](https://github.com/nodejs/node/commit/6ba8aa963e)] - **test**: changed var to const, added strict equal checks (Lydia Katsamberis) [#8762](https://github.com/nodejs/node/pull/8762)
- [[`81ed50c5da`](https://github.com/nodejs/node/commit/81ed50c5da)] - **test**: add assertions to zero length buffer test (Lauren Spiegel) [#8729](https://github.com/nodejs/node/pull/8729)
- [[`b2aea505df`](https://github.com/nodejs/node/commit/b2aea505df)] - **test**: use Buffer.alloc (Eugene Ostroukhov) [#8748](https://github.com/nodejs/node/pull/8748)
- [[`5e4d8984b4`](https://github.com/nodejs/node/commit/5e4d8984b4)] - **test**: accept expected AIX result test-stdio-closed (Rich Trott) [#8755](https://github.com/nodejs/node/pull/8755)
- [[`906283837f`](https://github.com/nodejs/node/commit/906283837f)] - **test**: skip cpu-intensive tests on slow hosts (Rich Trott) [#8652](https://github.com/nodejs/node/pull/8652)
- [[`aa5a16a8ae`](https://github.com/nodejs/node/commit/aa5a16a8ae)] - **test**: add expectWarning to common (Michaël Zasso) [#8662](https://github.com/nodejs/node/pull/8662)
- [[`b46d8cdcde`](https://github.com/nodejs/node/commit/b46d8cdcde)] - **test**: cleanup vars to const and '==' to '===' (oogz) [#8705](https://github.com/nodejs/node/pull/8705)
- [[`5540e3d488`](https://github.com/nodejs/node/commit/5540e3d488)] - **test**: fix test-cluster-worker-init.js flakyness (Ilkka Myller) [#8703](https://github.com/nodejs/node/pull/8703)
- [[`ed625fefd2`](https://github.com/nodejs/node/commit/ed625fefd2)] - **test**: add tests for add/remove header after sent (Niklas Ingholt) [#8682](https://github.com/nodejs/node/pull/8682)
- [[`e9d1426080`](https://github.com/nodejs/node/commit/e9d1426080)] - **test**: enable cyrillic punycode test case (Ben Noordhuis) [#8695](https://github.com/nodejs/node/pull/8695)
- [[`b62735a302`](https://github.com/nodejs/node/commit/b62735a302)] - **test**: remove call to `net.Socket.resume()` (Alfred Cepeda) [#8679](https://github.com/nodejs/node/pull/8679)
- [[`9ca8722203`](https://github.com/nodejs/node/commit/9ca8722203)] - **test**: cleanup stream tests (Italo A. Casas) [#8668](https://github.com/nodejs/node/pull/8668)
- [[`dfd022ff9e`](https://github.com/nodejs/node/commit/dfd022ff9e)] - **test**: update test/parallel/test-child-process-stdio.js (matzavinos) [#8584](https://github.com/nodejs/node/pull/8584)
- [[`fef4341f46`](https://github.com/nodejs/node/commit/fef4341f46)] - **test**: update test/parallel/test-eval.js (Pavol Otcenas) [#8590](https://github.com/nodejs/node/pull/8590)
- [[`43d6212257`](https://github.com/nodejs/node/commit/43d6212257)] - **test**: update test/parallel/test-child-process-send-utf8.js (Jonathan Prince) [#8594](https://github.com/nodejs/node/pull/8594)
- [[`6924a4d237`](https://github.com/nodejs/node/commit/6924a4d237)] - **test**: update test/parallel/test-fs-read.js (Richard Walker) [#8596](https://github.com/nodejs/node/pull/8596)
- [[`1b494d3b96`](https://github.com/nodejs/node/commit/1b494d3b96)] - **test**: fixup parallel/test-async-wrap-post-did-throw.js (Jermaine Oppong) [#8625](https://github.com/nodejs/node/pull/8625)
- [[`edf9242f56`](https://github.com/nodejs/node/commit/edf9242f56)] - **test**: cleanup test-os.js (delvedor) [#8606](https://github.com/nodejs/node/pull/8606)
- [[`d4ad8d9619`](https://github.com/nodejs/node/commit/d4ad8d9619)] - **test**: refactor test-dgram-bind-shared-ports.js (Fikret Burak Gazioglu) [#8582](https://github.com/nodejs/node/pull/8582)
- [[`714cbfd46c`](https://github.com/nodejs/node/commit/714cbfd46c)] - **test**: update test-child-process-recv-handle (Jonathan Prince) [#8648](https://github.com/nodejs/node/pull/8648)
- [[`c664109c72`](https://github.com/nodejs/node/commit/c664109c72)] - **test**: improve test-child-process-stdout-flush (Wietse Venema) [#8581](https://github.com/nodejs/node/pull/8581)
- [[`c98d0c984d`](https://github.com/nodejs/node/commit/c98d0c984d)] - **test**: cleanup test-child-process-exec-env.js (Yevgen Safronov) [#8600](https://github.com/nodejs/node/pull/8600)
- [[`3269a7d6f5`](https://github.com/nodejs/node/commit/3269a7d6f5)] - **test**: cleanup test-tls-connect-given-socket.js (Thomas van Lankveld) [#8616](https://github.com/nodejs/node/pull/8616)
- [[`5e5a1c0e3c`](https://github.com/nodejs/node/commit/5e5a1c0e3c)] - **test**: cleanup test-child-process-buffering.js (Adri Van Houdt) [#8578](https://github.com/nodejs/node/pull/8578)
- [[`bcba27e8c6`](https://github.com/nodejs/node/commit/bcba27e8c6)] - **test**: update test-cluster-disconnect-unshared-udp (matt-in-a-hat) [#8599](https://github.com/nodejs/node/pull/8599)
- [[`5a59ca6168`](https://github.com/nodejs/node/commit/5a59ca6168)] - **test**: changing equal to strictEqual in path (lrlna) [#8628](https://github.com/nodejs/node/pull/8628)
- [[`3ddf77fc24`](https://github.com/nodejs/node/commit/3ddf77fc24)] - **test**: modernize js and tighten equality checking (Pavol Otcenas) [#8618](https://github.com/nodejs/node/pull/8618)
- [[`34f24e559d`](https://github.com/nodejs/node/commit/34f24e559d)] - **test**: refactor parallel/test-tcp-wrap-listen (scott stern) [#8640](https://github.com/nodejs/node/pull/8640)
- [[`6fb8ebd98c`](https://github.com/nodejs/node/commit/6fb8ebd98c)] - **test**: cleanup cluster-disconnect-unshared-tcp test (Rachel) [#8598](https://github.com/nodejs/node/pull/8598)
- [[`fd7f87ef40`](https://github.com/nodejs/node/commit/fd7f87ef40)] - **test**: var variables to const in zlib (Ishan Aditya) [#8627](https://github.com/nodejs/node/pull/8627)
- [[`fc6b4970b5`](https://github.com/nodejs/node/commit/fc6b4970b5)] - **test**: modernize JS and tighten equality checking (Pavol Otcenas) [#8580](https://github.com/nodejs/node/pull/8580)
- [[`f2f6353bc4`](https://github.com/nodejs/node/commit/f2f6353bc4)] - **test**: cleanup test-intl.js (Alessandro Metta) [#8641](https://github.com/nodejs/node/pull/8641)
- [[`14025db8c5`](https://github.com/nodejs/node/commit/14025db8c5)] - **test**: cleanup test-child-process-disconnect.js (Pavol Otcenas) [#8602](https://github.com/nodejs/node/pull/8602)
- [[`9032ba60a5`](https://github.com/nodejs/node/commit/9032ba60a5)] - **test**: replace var by const test-tls-zero-clear-in (Sébastien Barbieri) [#8621](https://github.com/nodejs/node/pull/8621)
- [[`1aa1740f12`](https://github.com/nodejs/node/commit/1aa1740f12)] - **test**: improve coverage of the util module (Michaël Zasso) [#8633](https://github.com/nodejs/node/pull/8633)
- [[`28d009be76`](https://github.com/nodejs/node/commit/28d009be76)] - **test**: refactored test-crypto-random.js (Tobias Kahse) [#8632](https://github.com/nodejs/node/pull/8632)
- [[`a89deb9c59`](https://github.com/nodejs/node/commit/a89deb9c59)] - **test**: cleanup test-c-ares.js (Yevgen Safronov) [#8577](https://github.com/nodejs/node/pull/8577)
- [[`9c3d521d90`](https://github.com/nodejs/node/commit/9c3d521d90)] - **test**: improve child_process tests (Dennis Schwartz) [#8617](https://github.com/nodejs/node/pull/8617)
- [[`ba88f5b8f8`](https://github.com/nodejs/node/commit/ba88f5b8f8)] - **test**: improve coverage of the buffer module (Michaël Zasso) [#8552](https://github.com/nodejs/node/pull/8552)
- [[`b10467cde2`](https://github.com/nodejs/node/commit/b10467cde2)] - **test**: improve test-https-agent.js (Dan.Williams) [#8517](https://github.com/nodejs/node/pull/8517)
- [[`82b7894d3b`](https://github.com/nodejs/node/commit/82b7894d3b)] - **test**: make test-tick-processor.js non-flaky (Fedor Indutny) [#8542](https://github.com/nodejs/node/pull/8542)
- [[`30e995f714`](https://github.com/nodejs/node/commit/30e995f714)] - **test**: add coverage for `client._addHandle()` (Rich Trott) [#8518](https://github.com/nodejs/node/pull/8518)
- [[`ff238c8d15`](https://github.com/nodejs/node/commit/ff238c8d15)] - **test**: fix flaky test-force-repl (Rich Trott) [#8484](https://github.com/nodejs/node/pull/8484)
- [[`83ed5085f4`](https://github.com/nodejs/node/commit/83ed5085f4)] - **test**: mark test-inspector flaky on windows (Rich Trott) [#8835](https://github.com/nodejs/node/pull/8835)
- [[`5a7dd18699`](https://github.com/nodejs/node/commit/5a7dd18699)] - **test,benchmark**: fix lint errors on v6.x (Jeremiah Senkpiel) [#9029](https://github.com/nodejs/node/pull/9029)
- [[`4492cc3e25`](https://github.com/nodejs/node/commit/4492cc3e25)] - **test,lib**: align arguments in multiline calls (Rich Trott) [#8642](https://github.com/nodejs/node/pull/8642)
- [[`3a72a606cb`](https://github.com/nodejs/node/commit/3a72a606cb)] - **timers**: improve setImmediate() performance (Brian White) [#8655](https://github.com/nodejs/node/pull/8655)
- [[`06c411753e`](https://github.com/nodejs/node/commit/06c411753e)] - **timers**: improve setTimeout/Interval performance (Brian White) [#8661](https://github.com/nodejs/node/pull/8661)
- [[`02da155e4b`](https://github.com/nodejs/node/commit/02da155e4b)] - **timers**: remove unreachable code (yorkie) [#8487](https://github.com/nodejs/node/pull/8487)
- [[`cf92be6939`](https://github.com/nodejs/node/commit/cf92be6939)] - **tls**: TLSSocket emits 'error' on handshake failure (Mariusz 'koder' Chwalba) [#8805](https://github.com/nodejs/node/pull/8805)
- [[`bee1955f4e`](https://github.com/nodejs/node/commit/bee1955f4e)] - **tls**: handle `error` events with `_tlsError` (Fedor Indutny) [#8889](https://github.com/nodejs/node/pull/8889)
- [[`d2eaa12a23`](https://github.com/nodejs/node/commit/d2eaa12a23)] - **tls**: improve createSecureContext in `_tls_common` (yorkie) [#8781](https://github.com/nodejs/node/pull/8781)
- [[`b0234e7968`](https://github.com/nodejs/node/commit/b0234e7968)] - **tls**: add 'new' keyword for Array constructor call (Mike Ralphson) [#8514](https://github.com/nodejs/node/pull/8514)
- [[`58c55108d2`](https://github.com/nodejs/node/commit/58c55108d2)] - **tools**: disallow extra blank lines at EOF/BOF (Rich Trott) [#8920](https://github.com/nodejs/node/pull/8920)
- [[`336d505dd9`](https://github.com/nodejs/node/commit/336d505dd9)] - **tools**: enable more remark-lint rules (Сковорода Никита Андреевич) [#8708](https://github.com/nodejs/node/pull/8708)
- [[`0310655cda`](https://github.com/nodejs/node/commit/0310655cda)] - **tools**: update remark configuration (Сковорода Никита Андреевич) [#8666](https://github.com/nodejs/node/pull/8666)
- [[`5c6284a417`](https://github.com/nodejs/node/commit/5c6284a417)] - **tools**: add additional ESLint rules (Teddy Katz) [#8643](https://github.com/nodejs/node/pull/8643)
- [[`73d54a6fc7`](https://github.com/nodejs/node/commit/73d54a6fc7)] - **tools**: add eslint rule prefer-assert-methods (Dany Shaanan) [#8622](https://github.com/nodejs/node/pull/8622)
- [[`ac6927f549`](https://github.com/nodejs/node/commit/ac6927f549)] - **tools**: make argument alignment linting more strict (Rich Trott) [#8642](https://github.com/nodejs/node/pull/8642)
- [[`8684cea9b7`](https://github.com/nodejs/node/commit/8684cea9b7)] - **tools**: make sure links are correctly passed to marked (Timothy Gu) [#8494](https://github.com/nodejs/node/pull/8494)
- [[`a12ff5cc5d`](https://github.com/nodejs/node/commit/a12ff5cc5d)] - **tools**: clean up icu/README.md formatting (Сковорода Никита Андреевич) [#8660](https://github.com/nodejs/node/pull/8660)
- [[`fc68b12bc3`](https://github.com/nodejs/node/commit/fc68b12bc3)] - **(SEMVER-MINOR)** **util**: Add format for SharedArrayBuffer (Yosuke Furukawa) [#8587](https://github.com/nodejs/node/pull/8587)
- [[`38be15549e`](https://github.com/nodejs/node/commit/38be15549e)] - **util**: don't init Debug if it's not needed yet (Bryan English) [#8452](https://github.com/nodejs/node/pull/8452)
- [[`7728f95967`](https://github.com/nodejs/node/commit/7728f95967)] - **util**: simplify SIMD setup (Dany Shaanan) [#8579](https://github.com/nodejs/node/pull/8579)
- [[`8282d6fc60`](https://github.com/nodejs/node/commit/8282d6fc60)] - **vm**: add error message if we abort (Franziska Hinkelmann) [#8634](https://github.com/nodejs/node/pull/8634)
- [[`b83f51a326`](https://github.com/nodejs/node/commit/b83f51a326)] - **win,msi**: mark INSTALLDIR property as secure (João Reis) [#8795](https://github.com/nodejs/node/pull/8795)
- [[`623d3c6eb5`](https://github.com/nodejs/node/commit/623d3c6eb5)] - **win,tools**: ignore linting for line breaks (João Reis) [#8785](https://github.com/nodejs/node/pull/8785)
- [[`7403aaa13f`](https://github.com/nodejs/node/commit/7403aaa13f)] - **zlib**: tighten up dictionary tests (Tarjei Husøy) [#8512](https://github.com/nodejs/node/pull/8512)
- [[`15474951a5`](https://github.com/nodejs/node/commit/15474951a5)] - **zlib**: fix raw inflate with custom dictionary (Tarjei Husøy) [#8512](https://github.com/nodejs/node/pull/8512)

Windows 32-bit Installer: https://nodejs.org/dist/v6.8.0/node-v6.8.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v6.8.0/node-v6.8.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v6.8.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v6.8.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v6.8.0/node-v6.8.0.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v6.8.0/node-v6.8.0-darwin-x64.tar.gz \
Linux 32-bit Binary: https://nodejs.org/dist/v6.8.0/node-v6.8.0-linux-x86.tar.xz \
Linux 64-bit Binary: https://nodejs.org/dist/v6.8.0/node-v6.8.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v6.8.0/node-v6.8.0-linux-ppc64le.tar.xz \
SmartOS 32-bit Binary: https://nodejs.org/dist/v6.8.0/node-v6.8.0-sunos-x86.tar.xz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v6.8.0/node-v6.8.0-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v6.8.0/node-v6.8.0-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v6.8.0/node-v6.8.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v6.8.0/node-v6.8.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v6.8.0/node-v6.8.0.tar.gz \
Other release files: https://nodejs.org/dist/v6.8.0/ \
Documentation: https://nodejs.org/docs/v6.8.0/api/

Shasums (GPG signing hash: SHA512, file hash: SHA256):

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA512

6ec44c9dd6e9e7ed5145089aeb4437893873dd4de7cd8dfd7afb875f9b8eeb77  node-v6.8.0-aix-ppc64.tar.gz
4ff13860f6bda86ce1053c673548c7f5d301a42ceb2f103c781024c3b88d2083  node-v6.8.0-darwin-x64.tar.gz
f76d896e9e48d7a7544463492f65b9cfaf15a4ccccf7aff16de5f59f5ea5971b  node-v6.8.0-darwin-x64.tar.xz
1cac1f6816e26982cb652314f84fec76c0494c1fbd080fe86a400f841ef215f1  node-v6.8.0-headers.tar.gz
d6f386113c75d360b36bb70d7a008b00ecd531fe3f8602998412d82969b9de0d  node-v6.8.0-headers.tar.xz
c7524e35bfc1fd4961dfac6bd6d602f474e909d63e3e48f892906a183bd9aa04  node-v6.8.0-linux-arm64.tar.gz
46c3a34f57ebd2f9988223d5c552fdc6f3cd96bd8611cd94f5bc5ddab762e2c6  node-v6.8.0-linux-arm64.tar.xz
2ae978777979c4af49945ce46dd014eba18d50a94520a8555e60d10113308b2f  node-v6.8.0-linux-armv7l.tar.gz
636fb4e5cd2d499cb3feb473f62c1033b5c3621256209aa13952a15c96a4b65d  node-v6.8.0-linux-armv7l.tar.xz
d955c56f404287054bb7433b934636305d8f9788294da1b34e7942288497485c  node-v6.8.0-linux-ppc64le.tar.gz
20609c3ab0c7d4429aace56d9dad43bd79d7cfc45a2134908fc6a5259be6a039  node-v6.8.0-linux-ppc64le.tar.xz
c8bfd55be7f47efa7c6d2d051eef0b99c70d222a2c70c3a013cffb4b171e1808  node-v6.8.0-linux-ppc64.tar.gz
6ef8f001dad55337321b871965816dca880b20d0c6d05a88de93afbfbde256b8  node-v6.8.0-linux-ppc64.tar.xz
76225db134036a6f2426e5c8e854ee62e8e2d4b522d916874e995f88a22ddb14  node-v6.8.0-linux-s390x.tar.gz
854830d5638dcbf448cc87eecec51aed63bafadebe42ce6eb9f1237e6e241896  node-v6.8.0-linux-s390x.tar.xz
9ddd118262cbe27fd668bc17c9c786cdd27c6a291cc712b4937013e4665f6e6d  node-v6.8.0-linux-x64.tar.gz
1db91adb194a2fa65d48311502a13aa2ab9787212364a6925544b9f20b92a97b  node-v6.8.0-linux-x64.tar.xz
d79eccb95911d3d3cf742d059e2d2f7669db33ef8f7f1f38f023c0e553f26d30  node-v6.8.0-linux-x86.tar.gz
30eecdcd99eaa8c2a4fc4c5d2c979878aeaa480b2f937c2b7c34efd1ec1b5de4  node-v6.8.0-linux-x86.tar.xz
05d4ba510fe8e9cd0cacb276979d7327ca613543fe988c668baddcadc60cb2b1  node-v6.8.0.pkg
9346655f3724e81d5fb49140d36097957f5ede725cc5070a22b3be4ce423bf48  node-v6.8.0-sunos-x64.tar.gz
9182c4ce9c9c2084efb1c90be746c87ea0ea4349a37bf326cb1798c84f60bdd7  node-v6.8.0-sunos-x64.tar.xz
7ae4a80269e254791a57b1759d4e5eeb2127a44a431444a36c02d3ff8727ac88  node-v6.8.0-sunos-x86.tar.gz
a86c7a8afcb8df73b9b22f25e5ab7bdaecb5ad566f9bd7fb0cf29f42598645da  node-v6.8.0-sunos-x86.tar.xz
a8a2d4b74734991dd45e5b2b0b13617864f1283f6d6bc262e1e4d30741114352  node-v6.8.0.tar.gz
f832ce5a90a86615c2ab5a4d485e0a60666a800f07411a3244c88c1027ff598d  node-v6.8.0.tar.xz
65f579915f01cbf361f8071e9561cbd151d7a128b1cd7171a12e5d129bf049bb  node-v6.8.0-win-x64.7z
f8ebc521d109bcc9d5b94ffef2922beb05e2e19fe8d7572333acf1b509082c3e  node-v6.8.0-win-x64.zip
b90e68d9e22e11655a9d4ce86327628f31f484108555143c838f46fc7cd57930  node-v6.8.0-win-x86.7z
e94c33550a80c1a92caba5951bfc596bf40e33fcab2fd857d795a88d163829b7  node-v6.8.0-win-x86.zip
e4ebd121116fa2d22cae6a56962b3d8a9b002c775d7db6adcdff3ba6d30ee6b0  node-v6.8.0-x64.msi
2bf4bb535f926138393766ea163af1032d469cfb704f892f5b73a98da67d131a  node-v6.8.0-x86.msi
f052f2093d0d788950a0706a21aa9252e3db19393b33588592dbd6f7052b7cbd  win-x64/node.exe
1fe7ae8725f2d172d54a9d4ae3461686f05144986905f495d6def2ace38c7203  win-x64/node.lib
fdedf887b7ace4596b9ff764a741123a7b783abce3f174a83d458cdc9bfe1d13  win-x64/node_pdb.7z
2208cb79a7c1c8ebb0d4e20cbfa2da0ef1b18da3a303715292e11602ea2eac7f  win-x64/node_pdb.zip
bbb88d6eab73ca0b8d4d5d8c3f1816820c2b3470debfb8bd8ed5b5bd98ea3d4b  win-x86/node.exe
f859a599260092e1022ed9163c2da2a5e0b644d988a7532bd013d14218e5f25e  win-x86/node.lib
de29e3e862c47541ce9bb1d00143f4e6d1fd3e283eaf0472634c7ea45a062e12  win-x86/node_pdb.7z
0d3ffdbb43a7b5b35a10df7a2d9ae76fe10b7fd7728370e61a10a9de0abc36b9  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----
Comment: GPGTools - https://gpgtools.org

iQIcBAEBCgAGBQJX/pl4AAoJEEX17r2BPa6O5QAP/0OxoqpBq0v1IsbAkQomN+ti
Y8As8LymZzVvQvM/4Cfg/ZaQPqr6McBErJWet7KVTUArNKspqmuIu93Gq/DwcBu3
a+f0POBhZYYyVJQHo3+ZRO0jNY+hZLB/VK5TA3P1uKPHGbhSOoTpGxBYYTQ3FVY7
4Ei3j44VTiRpcYOammEcgFfTQpES7mDki/XEMbtDyk7WEwvIkfgr/hN9cSnHpafs
FRs8tkxhb+5e11+IdoSSvLEncAGt55t0awm/ctFoAEk7CSRhLnQE7ATa0mi2Lb/j
HfB3c2x3lb6QPJVOQwqdKDtJcmHLR8oonjljHBw8HAfKvEO0xLitFv5TxygbjFCT
3Nfpgmn/Ia3KQKwNeYkST7R2bMU7WU2mThvIMsmCA4OwCJSRrTrGlXMT9G1FwicD
cJ3WI4+lwIqGxSudrnVtZaBVQ3T9ArcUiELvyeqDcPm9sw8QVWkMA9F9J0Sl9yw4
aPPFSBkkStELHjkOqp0hs0Xg8/p/GfcBcizWu3zQb4Scep56H6JOeOLBeHhooEWF
WaySL0FaPfNNQwhxMOMHSkYdPR3eEoYd6k2Ea+S8kOe7BWBghCRVkou582cxZZbt
1RczdWirVL5/AI6aGoe4bZtZwRdpqFjm3wpYkNrT0KIkrRino2Y1eoNzrjB6ZvpQ
Ld4lKpKmIEH1mWDE7q7k
=oguF
-----END PGP SIGNATURE-----

```
