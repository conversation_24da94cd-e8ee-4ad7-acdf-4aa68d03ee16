---
date: '2017-10-24T22:08:05.068Z'
category: release
title: Node v8.8.0 (Current)
layout: blog-post
author: <PERSON><PERSON>
---

### Notable Changes

- **crypto**:
  - expose ECDH class [#8188](https://github.com/nodejs/node/pull/8188)
- **http2**:
  - http2 is now exposed by default without the need for a flag [#15685](https://github.com/nodejs/node/pull/15685)
  - a new environment variable NODE_NO_HTTP2 has been added to allow userland http2 to be required [#15685](https://github.com/nodejs/node/pull/15685)
  - support has been added for generic `Duplex` streams [#16269](https://github.com/nodejs/node/pull/16269)
- **module**:
  - resolve and instantiate loader pipeline hooks have been added to the ESM lifecycle [#15445](https://github.com/nodejs/node/pull/15445)
- **zlib**:
  - CVE-2017-14919 - In zlib v1.2.9, a change was made that causes an error to be raised when a raw deflate stream is initialized with windowBits set to 8. On some versions this crashes Node and you cannot recover from it, while on some versions it throws an exception. Node.js will now gracefully set windowBits to 9 replicating the legacy behavior to avoid a DOS vector. [nodejs-private/node-private#95](https://github.com/nodejs-private/node-private/pull/95)

### Commits

- [[`c62f2e21c0`](https://github.com/nodejs/node/commit/c62f2e21c0)] - **assert**: fix actual and expected order (Steve Jenkins) [#15866](https://github.com/nodejs/node/pull/15866)
- [[`c9715bb9c2`](https://github.com/nodejs/node/commit/c9715bb9c2)] - **async_hooks**: skip runtime checks when disabled (Andreas Madsen) [#15454](https://github.com/nodejs/node/pull/15454)
- [[`11b775beb6`](https://github.com/nodejs/node/commit/11b775beb6)] - **async_hooks**: replace concat w template literals (Rob Adelmann) [#15968](https://github.com/nodejs/node/pull/15968)
- [[`5d34f2f5a7`](https://github.com/nodejs/node/commit/5d34f2f5a7)] - **benchmark**: improve http2 benchmark configs (James M Snell) [#16239](https://github.com/nodejs/node/pull/16239)
- [[`b1a68f52e2`](https://github.com/nodejs/node/commit/b1a68f52e2)] - **benchmark**: add util/type-check (Timothy Gu) [#15663](https://github.com/nodejs/node/pull/15663)
- [[`6f64a4456f`](https://github.com/nodejs/node/commit/6f64a4456f)] - **benchmark**: remove writing to benchmark directory (Rich Trott) [#16144](https://github.com/nodejs/node/pull/16144)
- [[`dc48e9cdaf`](https://github.com/nodejs/node/commit/dc48e9cdaf)] - **benchmark**: remove misc/v8-bench.js (Joyee Cheung) [#16126](https://github.com/nodejs/node/pull/16126)
- [[`a16d314214`](https://github.com/nodejs/node/commit/a16d314214)] - **build**: use bin override if no `python` in PATH (Bradley T. Hughes) [#16241](https://github.com/nodejs/node/pull/16241)
- [[`6ce04d94f9`](https://github.com/nodejs/node/commit/6ce04d94f9)] - **build**: revert "call setlocal in vcbuild.bat" (Refael Ackermann) [#16270](https://github.com/nodejs/node/pull/16270)
- [[`dfaa05722b`](https://github.com/nodejs/node/commit/dfaa05722b)] - **build**: use doc-only instead of doc (Rich Trott) [#16309](https://github.com/nodejs/node/pull/16309)
- [[`2cfcc45ae1`](https://github.com/nodejs/node/commit/2cfcc45ae1)] - **build**: add c++ coverage support on macOS (Evan Lucas) [#16163](https://github.com/nodejs/node/pull/16163)
- [[`e32b10f469`](https://github.com/nodejs/node/commit/e32b10f469)] - **build**: set disable_glibcxx_debug flag (Anna Henningsen) [#16159](https://github.com/nodejs/node/pull/16159)
- [[`ea0fec25fe`](https://github.com/nodejs/node/commit/ea0fec25fe)] - **build**: lint benchmark addon (Ben Noordhuis) [#16160](https://github.com/nodejs/node/pull/16160)
- [[`c032b5ffe7`](https://github.com/nodejs/node/commit/c032b5ffe7)] - **build**: use local node-gyp for benchmark addon (Ben Noordhuis) [#16160](https://github.com/nodejs/node/pull/16160)
- [[`ce2eeb9a0b`](https://github.com/nodejs/node/commit/ce2eeb9a0b)] - **build**: restore mistakenly dropped suites (Refael Ackermann) [#16132](https://github.com/nodejs/node/pull/16132)
- [[`37ab447942`](https://github.com/nodejs/node/commit/37ab447942)] - **build**: correct minor typo in lttng help message (Daniel Bevenius) [#16101](https://github.com/nodejs/node/pull/16101)
- [[`f5c0d74eda`](https://github.com/nodejs/node/commit/f5c0d74eda)] - **build**: ignore empty folders in test-addons (Gregor) [#16031](https://github.com/nodejs/node/pull/16031)
- [[`152ca1e49b`](https://github.com/nodejs/node/commit/152ca1e49b)] - **build, windows**: use /bigobj for debug builds (Nikolai Vavilov) [#16289](https://github.com/nodejs/node/pull/16289)
- [[`3951c15212`](https://github.com/nodejs/node/commit/3951c15212)] - **build,win**: use /MP for debug builds (Nikolai Vavilov) [#16333](https://github.com/nodejs/node/pull/16333)
- [[`c9ec12d3e6`](https://github.com/nodejs/node/commit/c9ec12d3e6)] - **build,win**: enable lint option to run "standalone" (Daniel Bevenius) [#16176](https://github.com/nodejs/node/pull/16176)
- [[`6e2a6b2c7e`](https://github.com/nodejs/node/commit/6e2a6b2c7e)] - **build,win**: include addons-napi in linter (Daniel Bevenius) [#16181](https://github.com/nodejs/node/pull/16181)
- [[`81d01bccd1`](https://github.com/nodejs/node/commit/81d01bccd1)] - **child_process**: add windowsHide option (cjihrig) [#15380](https://github.com/nodejs/node/pull/15380)
- [[`a5c3143539`](https://github.com/nodejs/node/commit/a5c3143539)] - **(SEMVER-MINOR)** **crypto**: expose ECDH class (Bryan English) [#8188](https://github.com/nodejs/node/pull/8188)
- [[`ff25ca70b2`](https://github.com/nodejs/node/commit/ff25ca70b2)] - **doc**: replace undocumented encoding aliases (Vse Mozhet Byt) [#16368](https://github.com/nodejs/node/pull/16368)
- [[`8f08d6653d`](https://github.com/nodejs/node/commit/8f08d6653d)] - **doc**: async_hooks grammar nits (Jon Moss) [#16361](https://github.com/nodejs/node/pull/16361)
- [[`b96d76f3a7`](https://github.com/nodejs/node/commit/b96d76f3a7)] - **doc**: link to async_hooks destroy callback properly (Jon Moss) [#16351](https://github.com/nodejs/node/pull/16351)
- [[`71e6c6ea9c`](https://github.com/nodejs/node/commit/71e6c6ea9c)] - **doc**: fix some links (Vse Mozhet Byt) [#16202](https://github.com/nodejs/node/pull/16202)
- [[`fbd3c80316`](https://github.com/nodejs/node/commit/fbd3c80316)] - **doc**: replace methods used in the example code (Damian) [#16416](https://github.com/nodejs/node/pull/16416)
- [[`73915d3ff8`](https://github.com/nodejs/node/commit/73915d3ff8)] - **doc**: fix http2 example with rstWithCancel (c0b) [#16365](https://github.com/nodejs/node/pull/16365)
- [[`ce159f4e10`](https://github.com/nodejs/node/commit/ce159f4e10)] - **doc**: improve {readable,writable}.\_destroy() docs (Luigi Pinca) [#16313](https://github.com/nodejs/node/pull/16313)
- [[`d57f358225`](https://github.com/nodejs/node/commit/d57f358225)] - **doc**: remove duplicate options (aayush.a) [#16339](https://github.com/nodejs/node/pull/16339)
- [[`77e76141ee`](https://github.com/nodejs/node/commit/77e76141ee)] - **doc**: fix comment in assert.md (umatoma) [#16335](https://github.com/nodejs/node/pull/16335)
- [[`d0fc7ab4d2`](https://github.com/nodejs/node/commit/d0fc7ab4d2)] - **doc**: add space after period (Diego Rodríguez Baquero) [#16334](https://github.com/nodejs/node/pull/16334)
- [[`6c809e0125`](https://github.com/nodejs/node/commit/6c809e0125)] - **doc**: minor correction to note on process section (Daniel Bevenius) [#16311](https://github.com/nodejs/node/pull/16311)
- [[`49a41d9739`](https://github.com/nodejs/node/commit/49a41d9739)] - **doc**: fix links in http2.md (Vse Mozhet Byt) [#16307](https://github.com/nodejs/node/pull/16307)
- [[`8b4f1229ac`](https://github.com/nodejs/node/commit/8b4f1229ac)] - **doc**: document opening hidden files on Windows (Bartosz Sosnowski) [#15409](https://github.com/nodejs/node/pull/15409)
- [[`0585148c34`](https://github.com/nodejs/node/commit/0585148c34)] - **doc**: add return value to util.promisify (Supamic) [#16040](https://github.com/nodejs/node/pull/16040)
- [[`497bfff7b2`](https://github.com/nodejs/node/commit/497bfff7b2)] - **doc**: clarify using crlfDelay with fs streams (Vse Mozhet Byt) [#16259](https://github.com/nodejs/node/pull/16259)
- [[`7bf9878568`](https://github.com/nodejs/node/commit/7bf9878568)] - **doc**: add apapirovski to collaborators (Anatoli Papirovski) [#16302](https://github.com/nodejs/node/pull/16302)
- [[`97b8271aa4`](https://github.com/nodejs/node/commit/97b8271aa4)] - **doc**: add @nodejs/build to onboarding-extras.md (Lance Ball) [#16298](https://github.com/nodejs/node/pull/16298)
- [[`d92b45ec55`](https://github.com/nodejs/node/commit/d92b45ec55)] - **doc**: update test/inspector reference (Jon Moss) [#16277](https://github.com/nodejs/node/pull/16277)
- [[`e34509e8ed`](https://github.com/nodejs/node/commit/e34509e8ed)] - **doc**: public keys don't accept passphrases (Ben Noordhuis) [#16087](https://github.com/nodejs/node/pull/16087)
- [[`720ea94894`](https://github.com/nodejs/node/commit/720ea94894)] - **doc**: clarify os.cpus() returns logical CPU cores (Luke Childs) [#16282](https://github.com/nodejs/node/pull/16282)
- [[`8a8e5c775b`](https://github.com/nodejs/node/commit/8a8e5c775b)] - **doc**: add return values in crypto documentation (Jeremy Huang) [#16229](https://github.com/nodejs/node/pull/16229)
- [[`9eccb8479b`](https://github.com/nodejs/node/commit/9eccb8479b)] - **doc**: reduce keylen in pbkdf2 examples (Lukas) [#16203](https://github.com/nodejs/node/pull/16203)
- [[`0776c80606`](https://github.com/nodejs/node/commit/0776c80606)] - **doc**: support multidimensional arrays in type link (Vse Mozhet Byt) [#16207](https://github.com/nodejs/node/pull/16207)
- [[`3e7945ad3a`](https://github.com/nodejs/node/commit/3e7945ad3a)] - **doc**: update to use NAPI_AUTO_LENGTH (Michael Dawson) [#16187](https://github.com/nodejs/node/pull/16187)
- [[`7218bba916`](https://github.com/nodejs/node/commit/7218bba916)] - **doc**: move Shigeki to TSC Emeritus (Rich Trott) [#16195](https://github.com/nodejs/node/pull/16195)
- [[`c23bf96dbe`](https://github.com/nodejs/node/commit/c23bf96dbe)] - **doc**: fix paragraph line-height issue (silverwind) [#16200](https://github.com/nodejs/node/pull/16200)
- [[`d75a403add`](https://github.com/nodejs/node/commit/d75a403add)] - **doc**: Update a typo in module.js' comments (Orta) [#16205](https://github.com/nodejs/node/pull/16205)
- [[`4ecc2880a4`](https://github.com/nodejs/node/commit/4ecc2880a4)] - **doc**: add missing comma (Jon Moss) [#16204](https://github.com/nodejs/node/pull/16204)
- [[`57b7f401ee`](https://github.com/nodejs/node/commit/57b7f401ee)] - **doc**: added note to fs.watchFile on previousStat (NiveditN) [#16099](https://github.com/nodejs/node/pull/16099)
- [[`0bb1415b2d`](https://github.com/nodejs/node/commit/0bb1415b2d)] - **doc**: add UV_THREADPOOL_SIZE link definition (Jon Moss) [#16193](https://github.com/nodejs/node/pull/16193)
- [[`3266e1fe81`](https://github.com/nodejs/node/commit/3266e1fe81)] - **doc**: add basic C++ style guide (Anna Henningsen) [#16090](https://github.com/nodejs/node/pull/16090)
- [[`c9be3a29de`](https://github.com/nodejs/node/commit/c9be3a29de)] - **doc**: fix dgram.md typos (Alessandro Vergani) [#16183](https://github.com/nodejs/node/pull/16183)
- [[`55fa067a6d`](https://github.com/nodejs/node/commit/55fa067a6d)] - **doc**: clarify usage of util.promisify.custom (Shiya Luo) [#16134](https://github.com/nodejs/node/pull/16134)
- [[`4ed78e267a`](https://github.com/nodejs/node/commit/4ed78e267a)] - **doc**: ensure collaborators validate commits (Bradley Farias) [#16162](https://github.com/nodejs/node/pull/16162)
- [[`04b6ae6648`](https://github.com/nodejs/node/commit/04b6ae6648)] - **doc**: move 8 collaborators to emeriti (Rich Trott) [#16173](https://github.com/nodejs/node/pull/16173)
- [[`d09e489ec9`](https://github.com/nodejs/node/commit/d09e489ec9)] - **doc**: include V8 commit URL in V8 backport guide (Gibson Fahnestock) [#16054](https://github.com/nodejs/node/pull/16054)
- [[`80539d83d2`](https://github.com/nodejs/node/commit/80539d83d2)] - **doc**: add parentheses to refreshTmpDir() (Rich Trott) [#16168](https://github.com/nodejs/node/pull/16168)
- [[`a45ac4f77d`](https://github.com/nodejs/node/commit/a45ac4f77d)] - **doc**: fix changelog anchor to v8.7.0 (Lewis Zhang) [#16165](https://github.com/nodejs/node/pull/16165)
- [[`2b4f3bb39e`](https://github.com/nodejs/node/commit/2b4f3bb39e)] - **doc**: add pronoun for fhinkel (F. Hinkelmann) [#16069](https://github.com/nodejs/node/pull/16069)
- [[`4fdb9f5024`](https://github.com/nodejs/node/commit/4fdb9f5024)] - **doc**: document windows shell support (Tim Ermilov) [#16104](https://github.com/nodejs/node/pull/16104)
- [[`6670b35f23`](https://github.com/nodejs/node/commit/6670b35f23)] - **doc**: remove undefined reference variable (Adarsh Honawad) [#16106](https://github.com/nodejs/node/pull/16106)
- [[`0d23412e12`](https://github.com/nodejs/node/commit/0d23412e12)] - **doc**: exempt test/doc only changes from 48-hr rule (Anna Henningsen) [#16135](https://github.com/nodejs/node/pull/16135)
- [[`e375b8cbae`](https://github.com/nodejs/node/commit/e375b8cbae)] - **doc**: rename good first contrib label (Jeremiah Senkpiel) [#16150](https://github.com/nodejs/node/pull/16150)
- [[`3b7abe1e0d`](https://github.com/nodejs/node/commit/3b7abe1e0d)] - **doc**: fix the description of 'close' event (James M. Greene) [#15800](https://github.com/nodejs/node/pull/15800)
- [[`698f358956`](https://github.com/nodejs/node/commit/698f358956)] - **doc**: document the `test/common/dns` module (Cameron Burwell) [#15772](https://github.com/nodejs/node/pull/15772)
- [[`cfa1ef51b4`](https://github.com/nodejs/node/commit/cfa1ef51b4)] - **doc**: remove bold typography from STYLE_GUIDE.md (Rich Trott) [#16085](https://github.com/nodejs/node/pull/16085)
- [[`c2f0700769`](https://github.com/nodejs/node/commit/c2f0700769)] - **doc**: add history for readline `crlfDelay` option (Vse Mozhet Byt) [#16075](https://github.com/nodejs/node/pull/16075)
- [[`8a151bedfb`](https://github.com/nodejs/node/commit/8a151bedfb)] - **doc**: responsive docs, rewrite font sizes (silverwind) [#15660](https://github.com/nodejs/node/pull/15660)
- [[`939a580406`](https://github.com/nodejs/node/commit/939a580406)] - **errors**: make `code` and `name` properties settable (John-David Dalton) [#15694](https://github.com/nodejs/node/pull/15694)
- [[`0bd2fd5627`](https://github.com/nodejs/node/commit/0bd2fd5627)] - **errors**: make properties mutable (Rich Trott) [#15694](https://github.com/nodejs/node/pull/15694)
- [[`62f802aee0`](https://github.com/nodejs/node/commit/62f802aee0)] - **fs**: account for buffer alloc failure in readFile (Anna Henningsen) [#16219](https://github.com/nodejs/node/pull/16219)
- [[`f501062363`](https://github.com/nodejs/node/commit/f501062363)] - **fs**: remove no-longer-relevant comment (Bryan English) [#16285](https://github.com/nodejs/node/pull/16285)
- [[`ce73ee5f36`](https://github.com/nodejs/node/commit/ce73ee5f36)] - **http**: do not blindly destroy UNIX domain sockets (Luigi Pinca) [#15650](https://github.com/nodejs/node/pull/15650)
- [[`0d50c7061c`](https://github.com/nodejs/node/commit/0d50c7061c)] - **http2**: multiple style and performance updates (James M Snell) [#16239](https://github.com/nodejs/node/pull/16239)
- [[`9aa5d494e6`](https://github.com/nodejs/node/commit/9aa5d494e6)] - **http2**: allow port 80 in http2.connect (Anatoli Papirovski) [#16337](https://github.com/nodejs/node/pull/16337)
- [[`63036a8d6d`](https://github.com/nodejs/node/commit/63036a8d6d)] - **(SEMVER-MINOR)** **http2**: support generic `Duplex` streams (Anna Henningsen) [#16269](https://github.com/nodejs/node/pull/16269)
- [[`bc1ad81b4c`](https://github.com/nodejs/node/commit/bc1ad81b4c)] - **http2**: small fixes to http2 core (Anatoli Papirovski) [#16327](https://github.com/nodejs/node/pull/16327)
- [[`bcb83f70ec`](https://github.com/nodejs/node/commit/bcb83f70ec)] - **http2**: remove unnecessary asserts in core (Anatoli Papirovski) [#16327](https://github.com/nodejs/node/pull/16327)
- [[`098cd90560`](https://github.com/nodejs/node/commit/098cd90560)] - **http2**: cleanup access via Symbols in core (Anatoli Papirovski) [#16327](https://github.com/nodejs/node/pull/16327)
- [[`bf7c2a3b65`](https://github.com/nodejs/node/commit/bf7c2a3b65)] - **http2**: slightly simplify stream destroy (Anatoli Papirovski) [#16327](https://github.com/nodejs/node/pull/16327)
- [[`25ef372235`](https://github.com/nodejs/node/commit/25ef372235)] - **http2**: simplify te header validation, add test (Trivikram Kamat) [#16246](https://github.com/nodejs/node/pull/16246)
- [[`e01daec4e1`](https://github.com/nodejs/node/commit/e01daec4e1)] - **(SEMVER-MINOR)** **http2**: remove --expose-http2 flag from tests, etc (James M Snell) [#15685](https://github.com/nodejs/node/pull/15685)
- [[`57ab6e2a10`](https://github.com/nodejs/node/commit/57ab6e2a10)] - **(SEMVER-MINOR)** **http2**: expose http2 by default, add NODE_NO_HTTP2 (James M Snell) [#15685](https://github.com/nodejs/node/pull/15685)
- [[`b0b224c823`](https://github.com/nodejs/node/commit/b0b224c823)] - **http2**: replace fixturesDir with common.fixtures (ekulnivek) [#15839](https://github.com/nodejs/node/pull/15839)
- [[`1956ae7fc1`](https://github.com/nodejs/node/commit/1956ae7fc1)] - **inspector**: Move JS API code to separate file (Eugene Ostroukhov) [#16056](https://github.com/nodejs/node/pull/16056)
- [[`b431a4969b`](https://github.com/nodejs/node/commit/b431a4969b)] - **inspector**: reimplement JS binding (Timothy Gu) [#15643](https://github.com/nodejs/node/pull/15643)
- [[`ec93a53933`](https://github.com/nodejs/node/commit/ec93a53933)] - **lib**: fix extraneous space in module.js (Anatoli Papirovski) [#16199](https://github.com/nodejs/node/pull/16199)
- [[`67aed7972d`](https://github.com/nodejs/node/commit/67aed7972d)] - **lib**: faster type checks for some types (Timothy Gu) [#15663](https://github.com/nodejs/node/pull/15663)
- [[`303c6c287f`](https://github.com/nodejs/node/commit/303c6c287f)] - **meta**: update comments for module version (Myles Borins) [#16303](https://github.com/nodejs/node/pull/16303)
- [[`fd54d8a438`](https://github.com/nodejs/node/commit/fd54d8a438)] - **meta**: add note about email sync to CONTRIBUTING.md (Vse Mozhet Byt) [#16340](https://github.com/nodejs/node/pull/16340)
- [[`598787d854`](https://github.com/nodejs/node/commit/598787d854)] - **module**: fix main resolution and not found updates (Guy Bedford) [#16147](https://github.com/nodejs/node/pull/16147)
- [[`25c980332f`](https://github.com/nodejs/node/commit/25c980332f)] - **module**: add inspector to builtinLibs (Timothy Gu) [#15643](https://github.com/nodejs/node/pull/15643)
- [[`a4bc6ff546`](https://github.com/nodejs/node/commit/a4bc6ff546)] - **module**: minor performance improvement (243083df) [#16125](https://github.com/nodejs/node/pull/16125)
- [[`86082a4a74`](https://github.com/nodejs/node/commit/86082a4a74)] - **module**: allow loading files with % in the name (Guy Bedford) [#16128](https://github.com/nodejs/node/pull/16128)
- [[`7337a40491`](https://github.com/nodejs/node/commit/7337a40491)] - **module**: resolve and instantiate loader pipeline hooks (guybedford) [#15445](https://github.com/nodejs/node/pull/15445)
- [[`43c92cd850`](https://github.com/nodejs/node/commit/43c92cd850)] - **n-api**: unexpose symbols and remove EXTERNAL_NAPI (Gabriel Schulhof) [#16234](https://github.com/nodejs/node/pull/16234)
- [[`0a52479805`](https://github.com/nodejs/node/commit/0a52479805)] - **n-api**: check against invalid handle scope usage (Anna Henningsen) [#16201](https://github.com/nodejs/node/pull/16201)
- [[`06aa09661d`](https://github.com/nodejs/node/commit/06aa09661d)] - **n-api**: reimplement instanceof using V8 API (Yang Guo) [#16143](https://github.com/nodejs/node/pull/16143)
- [[`170d9abe03`](https://github.com/nodejs/node/commit/170d9abe03)] - **n-api**: use module name macro (Michael Dawson) [#16185](https://github.com/nodejs/node/pull/16185)
- [[`5c8cd0ef3e`](https://github.com/nodejs/node/commit/5c8cd0ef3e)] - **n-api**: make changes for source compatibility (Gabriel Schulhof) [#16102](https://github.com/nodejs/node/pull/16102)
- [[`8e7ca5db8d`](https://github.com/nodejs/node/commit/8e7ca5db8d)] - **n-api,test**: use module name macro (Gabriel Schulhof) [#16146](https://github.com/nodejs/node/pull/16146)
- [[`f6a725ea09`](https://github.com/nodejs/node/commit/f6a725ea09)] - **net**: fix timeouts during long writes (Anatoli Papirovski) [#15791](https://github.com/nodejs/node/pull/15791)
- [[`5f486cd5b6`](https://github.com/nodejs/node/commit/5f486cd5b6)] - **net**: change assert to conform to other files (James Hodgskiss) [#15861](https://github.com/nodejs/node/pull/15861)
- [[`1168e01455`](https://github.com/nodejs/node/commit/1168e01455)] - **repl**: remove unused err argument (Jon Moss) [#16152](https://github.com/nodejs/node/pull/16152)
- [[`78a6ef46a9`](https://github.com/nodejs/node/commit/78a6ef46a9)] - **src**: combine loops in CopyJsStringArray() (cjihrig) [#16247](https://github.com/nodejs/node/pull/16247)
- [[`5dbefe12e6`](https://github.com/nodejs/node/commit/5dbefe12e6)] - **src**: increase usage of context in spawn_sync.cc (cjihrig) [#16247](https://github.com/nodejs/node/pull/16247)
- [[`91dc6c7c40`](https://github.com/nodejs/node/commit/91dc6c7c40)] - **src**: increase usage of context in process_wrap.cc (cjihrig) [#16247](https://github.com/nodejs/node/pull/16247)
- [[`d356022087`](https://github.com/nodejs/node/commit/d356022087)] - **(SEMVER-MINOR)** **src**: turn JS stream into a full duplex (Anna Henningsen) [#16269](https://github.com/nodejs/node/pull/16269)
- [[`2788cb7524`](https://github.com/nodejs/node/commit/2788cb7524)] - **(SEMVER-MINOR)** **src**: allow top-level calls into JSStream (Anna Henningsen) [#16269](https://github.com/nodejs/node/pull/16269)
- [[`26bd574b32`](https://github.com/nodejs/node/commit/26bd574b32)] - **src**: operator\[\] checks bounds in debug mode (gitHubTracey) [#16002](https://github.com/nodejs/node/pull/16002)
- [[`0ddb00d3c2`](https://github.com/nodejs/node/commit/0ddb00d3c2)] - **src**: check uv_prepare_stop return value (Anna Henningsen) [#16268](https://github.com/nodejs/node/pull/16268)
- [[`0b04869860`](https://github.com/nodejs/node/commit/0b04869860)] - **src**: do not add .domain to promises in VM contexts (Timothy Gu) [#15695](https://github.com/nodejs/node/pull/15695)
- [[`659dd8ae4f`](https://github.com/nodejs/node/commit/659dd8ae4f)] - **src**: remove unused includes from node_wrap.h (Daniel Bevenius) [#16179](https://github.com/nodejs/node/pull/16179)
- [[`e8f59f755b`](https://github.com/nodejs/node/commit/e8f59f755b)] - **src**: rename StreamWrap to LibuvStreamWrap (Anna Henningsen) [#16157](https://github.com/nodejs/node/pull/16157)
- [[`7307e1d2cd`](https://github.com/nodejs/node/commit/7307e1d2cd)] - **src**: add internalBindings for binding isolation (Bradley Farias) [#15759](https://github.com/nodejs/node/pull/15759)
- [[`c8db0230c9`](https://github.com/nodejs/node/commit/c8db0230c9)] - **src**: use more appropriate context-entered check (Anna Henningsen) [#15691](https://github.com/nodejs/node/pull/15691)
- [[`419876aabb`](https://github.com/nodejs/node/commit/419876aabb)] - **src**: fixup node_platform blocking task drain (Anna Henningsen) [#15639](https://github.com/nodejs/node/pull/15639)
- [[`33d5262c23`](https://github.com/nodejs/node/commit/33d5262c23)] - **src**: prepare platform for upstream V8 changes (Anna Henningsen) [#15428](https://github.com/nodejs/node/pull/15428)
- [[`824be6dc1d`](https://github.com/nodejs/node/commit/824be6dc1d)] - **src**: node_dtrace line continuations clean up (Daniel Bevenius) [#15777](https://github.com/nodejs/node/pull/15777)
- [[`09660c8511`](https://github.com/nodejs/node/commit/09660c8511)] - **src**: rename perfctr_macros.py-\>noperfctr_macros.py (Daniel Bevenius) [#16100](https://github.com/nodejs/node/pull/16100)
- [[`2c469e8e29`](https://github.com/nodejs/node/commit/2c469e8e29)] - **test**: fix flaky test-make-doc (Rich Trott) [#16301](https://github.com/nodejs/node/pull/16301)
- [[`6d1602244a`](https://github.com/nodejs/node/commit/6d1602244a)] - **test**: http2 add timeout no callback test case (Trivikram Kamat) [#16082](https://github.com/nodejs/node/pull/16082)
- [[`c07d757508`](https://github.com/nodejs/node/commit/c07d757508)] - **test**: http2 session operations after destroy (Trivikram Kamat) [#15758](https://github.com/nodejs/node/pull/15758)
- [[`65c5d0e27a`](https://github.com/nodejs/node/commit/65c5d0e27a)] - **test**: consolidate http2 tests in one file (Trivikram Kamat) [#15624](https://github.com/nodejs/node/pull/15624)
- [[`6ad5b90730`](https://github.com/nodejs/node/commit/6ad5b90730)] - **test**: add tests for ERR_HTTP2_FRAME_ERROR (Ruxandra Fediuc) [#16107](https://github.com/nodejs/node/pull/16107)
- [[`d1080f858d`](https://github.com/nodejs/node/commit/d1080f858d)] - **test**: improve coverage for 'internal/errors' (Ruxandra Fediuc) [#16055](https://github.com/nodejs/node/pull/16055)
- [[`80b0dcfd2b`](https://github.com/nodejs/node/commit/80b0dcfd2b)] - **(SEMVER-MINOR)** **test**: add `makeDuplexPair()` helper (Anna Henningsen) [#16269](https://github.com/nodejs/node/pull/16269)
- [[`7e1a187df1`](https://github.com/nodejs/node/commit/7e1a187df1)] - **test**: handle blank shells in test-os.js (Gibson Fahnestock) [#16287](https://github.com/nodejs/node/pull/16287)
- [[`3c41f3f0e3`](https://github.com/nodejs/node/commit/3c41f3f0e3)] - **test**: increase enoughTestMem to 1.75 Gb (Rich Trott) [#16374](https://github.com/nodejs/node/pull/16374)
- [[`0d5ee95e36`](https://github.com/nodejs/node/commit/0d5ee95e36)] - **test**: use fixtures.readKey in https-timeout-server (Nicolas 'Pixel' Noble) [#15871](https://github.com/nodejs/node/pull/15871)
- [[`d7b4ad89fb`](https://github.com/nodejs/node/commit/d7b4ad89fb)] - **test**: improve message for assert.strictEqual() (Jayson D. Henkel) [#16013](https://github.com/nodejs/node/pull/16013)
- [[`b1c7889ad7`](https://github.com/nodejs/node/commit/b1c7889ad7)] - **test**: fix common.PIPE path bug (Rich Trott) [#16364](https://github.com/nodejs/node/pull/16364)
- [[`fad763789d`](https://github.com/nodejs/node/commit/fad763789d)] - **test**: use fixtures.readKey instead of fixturesDir (Paul Marion Camantigue) [#15976](https://github.com/nodejs/node/pull/15976)
- [[`f9321bb2f6`](https://github.com/nodejs/node/commit/f9321bb2f6)] - **test**: replace fixturesDir with fixtures module (tpurcell) [#16262](https://github.com/nodejs/node/pull/16262)
- [[`87804a6c0e`](https://github.com/nodejs/node/commit/87804a6c0e)] - **test**: replace fixturesDir with fixtures module (André Føyn Berge) [#15947](https://github.com/nodejs/node/pull/15947)
- [[`dae022e17e`](https://github.com/nodejs/node/commit/dae022e17e)] - **test**: replace fixturesDir with fixtures module (elisa lee) [#16095](https://github.com/nodejs/node/pull/16095)
- [[`0a0fa2bc42`](https://github.com/nodejs/node/commit/0a0fa2bc42)] - **test**: skip test due to file size limit (jBarz) [#16273](https://github.com/nodejs/node/pull/16273)
- [[`6b1f75d26f`](https://github.com/nodejs/node/commit/6b1f75d26f)] - **test**: update test-npm to use test-npm-package.js (Gibson Fahnestock) [#11540](https://github.com/nodejs/node/pull/11540)
- [[`e11c8fbc9f`](https://github.com/nodejs/node/commit/e11c8fbc9f)] - **test**: remove error msg in test-vm-symbols.js (Daniel Abrão) [#15873](https://github.com/nodejs/node/pull/15873)
- [[`dacef99bfc`](https://github.com/nodejs/node/commit/dacef99bfc)] - **test**: remove error messages in test-buffer-alloc (Braden Whitten) [#15867](https://github.com/nodejs/node/pull/15867)
- [[`bbc93edee9`](https://github.com/nodejs/node/commit/bbc93edee9)] - **test**: update assert error messages (Omar Gonzalez) [#16035](https://github.com/nodejs/node/pull/16035)
- [[`2e2c09e5de`](https://github.com/nodejs/node/commit/2e2c09e5de)] - **test**: more AsyncWrap constructor validation tests (Braden Whitten) [#16025](https://github.com/nodejs/node/pull/16025)
- [[`6f5edf4894`](https://github.com/nodejs/node/commit/6f5edf4894)] - **test**: remove error message (Jack Wang) [#15983](https://github.com/nodejs/node/pull/15983)
- [[`0ef63352b3`](https://github.com/nodejs/node/commit/0ef63352b3)] - **test**: expand error message (Stefania Sharp) [#15991](https://github.com/nodejs/node/pull/15991)
- [[`489168d87f`](https://github.com/nodejs/node/commit/489168d87f)] - **test**: use fixtures module (Kanika Shah) [#15959](https://github.com/nodejs/node/pull/15959)
- [[`d472922e35`](https://github.com/nodejs/node/commit/d472922e35)] - **test**: remove literal messages (Oscar Funes) [#15938](https://github.com/nodejs/node/pull/15938)
- [[`7634652e71`](https://github.com/nodejs/node/commit/7634652e71)] - **test**: fix stderr reference (Oscar Funes) [#15938](https://github.com/nodejs/node/pull/15938)
- [[`2b46049e16`](https://github.com/nodejs/node/commit/2b46049e16)] - **test**: fix typo (Oscar Funes) [#15938](https://github.com/nodejs/node/pull/15938)
- [[`acba6d0cb0`](https://github.com/nodejs/node/commit/acba6d0cb0)] - **test**: improve coverage of ModuleMap.js (Jean-Philippe Blais) [#15924](https://github.com/nodejs/node/pull/15924)
- [[`fe84f3ca26`](https://github.com/nodejs/node/commit/fe84f3ca26)] - **test**: replace concatenation with literals (Jean-Philippe Blais) [#15924](https://github.com/nodejs/node/pull/15924)
- [[`a87b202530`](https://github.com/nodejs/node/commit/a87b202530)] - **test**: use fixtures module in test-https-truncate (Gene Wu) [#15875](https://github.com/nodejs/node/pull/15875)
- [[`48ae4e0e5f`](https://github.com/nodejs/node/commit/48ae4e0e5f)] - **test**: use fixtures module (Alvaro Cruz) [#15874](https://github.com/nodejs/node/pull/15874)
- [[`b4fa45d641`](https://github.com/nodejs/node/commit/b4fa45d641)] - **test**: use fixtures module (Lance Barlaan) [#15872](https://github.com/nodejs/node/pull/15872)
- [[`737c9cfff0`](https://github.com/nodejs/node/commit/737c9cfff0)] - **test**: replace fixturesDir with fixtures module (Thomas Karsten) [#15840](https://github.com/nodejs/node/pull/15840)
- [[`f4a5abac56`](https://github.com/nodejs/node/commit/f4a5abac56)] - **test**: use default message for assert.strictEqual (hwaisiu) [#15970](https://github.com/nodejs/node/pull/15970)
- [[`70dab19c18`](https://github.com/nodejs/node/commit/70dab19c18)] - **test**: fix flaky test-benchmark-buffer (Rich Trott) [#16296](https://github.com/nodejs/node/pull/16296)
- [[`c6b2fa904e`](https://github.com/nodejs/node/commit/c6b2fa904e)] - **test**: improve assert message in internet test (Nikki St Onge) [#15998](https://github.com/nodejs/node/pull/15998)
- [[`e5cf833453`](https://github.com/nodejs/node/commit/e5cf833453)] - **test**: http2 priority stream depends on itself (Trivikram Kamat) [#16224](https://github.com/nodejs/node/pull/16224)
- [[`50b49656cb`](https://github.com/nodejs/node/commit/50b49656cb)] - **test**: http2 client settings errors (Trivikram Kamat) [#16096](https://github.com/nodejs/node/pull/16096)
- [[`327be77749`](https://github.com/nodejs/node/commit/327be77749)] - **test**: http2 emitGoAway post shutdown pre destroy (Trivikram Kamat) [#16215](https://github.com/nodejs/node/pull/16215)
- [[`811118e112`](https://github.com/nodejs/node/commit/811118e112)] - **test**: http2-respond-file-errors to fixtures module (David8472) [#16004](https://github.com/nodejs/node/pull/16004)
- [[`2a5e7ec634`](https://github.com/nodejs/node/commit/2a5e7ec634)] - **test**: http2-respond-file-range to use fixtures (Michael Rueppel) [#15852](https://github.com/nodejs/node/pull/15852)
- [[`1c14ad96a5`](https://github.com/nodejs/node/commit/1c14ad96a5)] - **test**: http2 ERR_INVALID_ARG_TYPE tests (Trivikram Kamat) [#15766](https://github.com/nodejs/node/pull/15766)
- [[`63a2b55747`](https://github.com/nodejs/node/commit/63a2b55747)] - **test**: replace common.fixturesDir (Shawn McGinty) [#15834](https://github.com/nodejs/node/pull/15834)
- [[`e0e5c890da`](https://github.com/nodejs/node/commit/e0e5c890da)] - **test**: fix flaky test-crypto-classes.js (Bryan English) [#15662](https://github.com/nodejs/node/pull/15662)
- [[`8f778d2506`](https://github.com/nodejs/node/commit/8f778d2506)] - **(SEMVER-MINOR)** **test**: crypto createClass instanceof Class (Bryan English) [#8188](https://github.com/nodejs/node/pull/8188)
- [[`a601596718`](https://github.com/nodejs/node/commit/a601596718)] - **test**: refactor test-process-kill-null (Luigi Pinca) [#16236](https://github.com/nodejs/node/pull/16236)
- [[`fefcd82c18`](https://github.com/nodejs/node/commit/fefcd82c18)] - **test**: test make doc and verify toc (Joyee Cheung) [#16208](https://github.com/nodejs/node/pull/16208)
- [[`5829b8fbf9`](https://github.com/nodejs/node/commit/5829b8fbf9)] - **test**: add common.projectDir (Joyee Cheung) [#16208](https://github.com/nodejs/node/pull/16208)
- [[`1daf5179dc`](https://github.com/nodejs/node/commit/1daf5179dc)] - **test**: fix inspector tests (Rich Trott) [#16281](https://github.com/nodejs/node/pull/16281)
- [[`1fe5308753`](https://github.com/nodejs/node/commit/1fe5308753)] - **test**: move inspector tests to parallel/sequential (Jon Moss) [#16197](https://github.com/nodejs/node/pull/16197)
- [[`cef125bfca`](https://github.com/nodejs/node/commit/cef125bfca)] - **test**: add missing spaces in concatenations (Vse Mozhet Byt) [#16244](https://github.com/nodejs/node/pull/16244)
- [[`06bae4b34a`](https://github.com/nodejs/node/commit/06bae4b34a)] - **test**: replace fixturesDir with fixtures module (hschwalm) [#15989](https://github.com/nodejs/node/pull/15989)
- [[`fa8e4186fb`](https://github.com/nodejs/node/commit/fa8e4186fb)] - **test**: update output to include exit code & signal (Jenna Zeigen) [#15945](https://github.com/nodejs/node/pull/15945)
- [[`edb9e1119f`](https://github.com/nodejs/node/commit/edb9e1119f)] - **test**: change common.fixturesDir to fixtures.path (tejbirsingh) [#15860](https://github.com/nodejs/node/pull/15860)
- [[`cca51f4b0a`](https://github.com/nodejs/node/commit/cca51f4b0a)] - **test**: split up and refactor test-domain (Anna Henningsen) [#13614](https://github.com/nodejs/node/pull/13614)
- [[`191e39011b`](https://github.com/nodejs/node/commit/191e39011b)] - **test**: fixtures in repl persistent history test (Jenna Zeigen) [#15999](https://github.com/nodejs/node/pull/15999)
- [[`62c2c78929`](https://github.com/nodejs/node/commit/62c2c78929)] - **test**: replace fixturesDir with common.fixtures (Kasim Doctor) [#15810](https://github.com/nodejs/node/pull/15810)
- [[`8a172cb085`](https://github.com/nodejs/node/commit/8a172cb085)] - **test**: http2 client operations after destroy (Trivikram Kamat) [#16094](https://github.com/nodejs/node/pull/16094)
- [[`b49df0bf87`](https://github.com/nodejs/node/commit/b49df0bf87)] - **test**: replaced fs.readSync with fixtures.readSync (Lam Chan) [#15882](https://github.com/nodejs/node/pull/15882)
- [[`ae3836a58f`](https://github.com/nodejs/node/commit/ae3836a58f)] - **test**: http2 connectionListener reject client (Trivikram Kamat) [#16080](https://github.com/nodejs/node/pull/16080)
- [[`45ccf5cc35`](https://github.com/nodejs/node/commit/45ccf5cc35)] - **test**: improve coverage for process.umask (Evan Lucas) [#16188](https://github.com/nodejs/node/pull/16188)
- [[`f9c1bcb13c`](https://github.com/nodejs/node/commit/f9c1bcb13c)] - **test**: remove message from notStrictEqual (twk-b) [#16048](https://github.com/nodejs/node/pull/16048)
- [[`5629351c66`](https://github.com/nodejs/node/commit/5629351c66)] - **test**: use fixtures module (Ben Hallion) [#15808](https://github.com/nodejs/node/pull/15808)
- [[`3f9b0a985c`](https://github.com/nodejs/node/commit/3f9b0a985c)] - **test**: refactor test-cluster-setup-master (Jean-Baptiste Brossard) [#16065](https://github.com/nodejs/node/pull/16065)
- [[`8173b0c363`](https://github.com/nodejs/node/commit/8173b0c363)] - **test**: use relative path in pipePrefix (Randal Hanford) [#15988](https://github.com/nodejs/node/pull/15988)
- [[`fa836fc21b`](https://github.com/nodejs/node/commit/fa836fc21b)] - **test**: replace fixtureDir with fixtures methods (Vladimir Ilic) [#16114](https://github.com/nodejs/node/pull/16114)
- [[`9e8df31e8d`](https://github.com/nodejs/node/commit/9e8df31e8d)] - **test**: remove error messages in crypto-binary test (Kim Gentes) [#15981](https://github.com/nodejs/node/pull/15981)
- [[`438728a680`](https://github.com/nodejs/node/commit/438728a680)] - **test**: use fixtures module over fixturesDir (JamesNimlos) [#15847](https://github.com/nodejs/node/pull/15847)
- [[`3a77a2d0d2`](https://github.com/nodejs/node/commit/3a77a2d0d2)] - **test**: use common.fixtures module (Shaun Sweet) [#15992](https://github.com/nodejs/node/pull/15992)
- [[`6ebdd989f8`](https://github.com/nodejs/node/commit/6ebdd989f8)] - **test**: replace fixturesDir with fixtures.path (Bear Trickey) [#15994](https://github.com/nodejs/node/pull/15994)
- [[`dbbb36ecb2`](https://github.com/nodejs/node/commit/dbbb36ecb2)] - **test**: update fixturesDir import (Tyler Seabrook) [#15887](https://github.com/nodejs/node/pull/15887)
- [[`40d2da60d9`](https://github.com/nodejs/node/commit/40d2da60d9)] - **test**: replace fixturesDir with fixtures methods (Komivi Agbakpem) [#15967](https://github.com/nodejs/node/pull/15967)
- [[`816acc92a0`](https://github.com/nodejs/node/commit/816acc92a0)] - **test**: fix regression in test-require-resolver.js (Tobias Nießen) [#16192](https://github.com/nodejs/node/pull/16192)
- [[`8452af2469`](https://github.com/nodejs/node/commit/8452af2469)] - **test**: replace fixturesDir with the fixtures module (WeiPlanet) [#16027](https://github.com/nodejs/node/pull/16027)
- [[`f753aedb1e`](https://github.com/nodejs/node/commit/f753aedb1e)] - **test**: change crypto decipheriv assertion messages (Daniel Kostro) [#16007](https://github.com/nodejs/node/pull/16007)
- [[`ab87520b30`](https://github.com/nodejs/node/commit/ab87520b30)] - **test**: replaces fixturesDir with fixtures (Mike Fleming) [#15835](https://github.com/nodejs/node/pull/15835)
- [[`65eca5499f`](https://github.com/nodejs/node/commit/65eca5499f)] - **test**: remove test messages for assert.strictEqual (Ali Groening) [#15995](https://github.com/nodejs/node/pull/15995)
- [[`8dd217cf9e`](https://github.com/nodejs/node/commit/8dd217cf9e)] - **test**: move to common.fixtures (Justin Beckwith) [#15987](https://github.com/nodejs/node/pull/15987)
- [[`24a0761e0a`](https://github.com/nodejs/node/commit/24a0761e0a)] - **test**: remove redundant error messages (Christina Chan) [#16043](https://github.com/nodejs/node/pull/16043)
- [[`d818173044`](https://github.com/nodejs/node/commit/d818173044)] - **test**: remove error messages for readability (Fadi Asfour) [#16022](https://github.com/nodejs/node/pull/16022)
- [[`734a2d39b0`](https://github.com/nodejs/node/commit/734a2d39b0)] - **test**: added fixtures module (Michael Pal) [#15980](https://github.com/nodejs/node/pull/15980)
- [[`f1c30aa2c5`](https://github.com/nodejs/node/commit/f1c30aa2c5)] - **test**: use fixtures in test-tls-multi-key.js (Cheyenne Arrowsmith) [#15844](https://github.com/nodejs/node/pull/15844)
- [[`61b0af0fe4`](https://github.com/nodejs/node/commit/61b0af0fe4)] - **test**: switch to use common.fixtures.fixturesDir (Roger Jiang) [#15814](https://github.com/nodejs/node/pull/15814)
- [[`ae0a00c8d4`](https://github.com/nodejs/node/commit/ae0a00c8d4)] - **test**: use common.fixtures module (Chi-chi Wang) [#16012](https://github.com/nodejs/node/pull/16012)
- [[`e3ac7b14b7`](https://github.com/nodejs/node/commit/e3ac7b14b7)] - **test**: escape script filename on Windows (Bartosz Sosnowski) [#16124](https://github.com/nodejs/node/pull/16124)
- [[`f7c087040d`](https://github.com/nodejs/node/commit/f7c087040d)] - **test**: replace common.fixtureDir with fixtures (<EMAIL>) [#15816](https://github.com/nodejs/node/pull/15816)
- [[`81f16a988b`](https://github.com/nodejs/node/commit/81f16a988b)] - **test**: add env to failure message (<EMAIL>) [#15816](https://github.com/nodejs/node/pull/15816)
- [[`26ca2ae266`](https://github.com/nodejs/node/commit/26ca2ae266)] - **test**: improve assert message in test-dh-regr (Mabry Cervin) [#15912](https://github.com/nodejs/node/pull/15912)
- [[`6abb1f44f6`](https://github.com/nodejs/node/commit/6abb1f44f6)] - **test**: fixtures in test-net-pipe-connect-errors (Eric Freiberg) [#15922](https://github.com/nodejs/node/pull/15922)
- [[`969c87e456`](https://github.com/nodejs/node/commit/969c87e456)] - **test**: fixtures in test-process-redirect-warnings-env (Kat Rosario) [#15930](https://github.com/nodejs/node/pull/15930)
- [[`8e2064f093`](https://github.com/nodejs/node/commit/8e2064f093)] - **test**: fix ordering of strictEqual actual/expected (Chad Zezula) [#16008](https://github.com/nodejs/node/pull/16008)
- [[`b054a4e138`](https://github.com/nodejs/node/commit/b054a4e138)] - **test**: use fixtures.path instead of fixturesDir (Matthew Meyer) [#15984](https://github.com/nodejs/node/pull/15984)
- [[`68bfde9fb9`](https://github.com/nodejs/node/commit/68bfde9fb9)] - **test**: fix test-esm-addons (Rich Trott) [#16174](https://github.com/nodejs/node/pull/16174)
- [[`562d445999`](https://github.com/nodejs/node/commit/562d445999)] - **test**: use fixtures.readSync (szhang351)
- [[`1469eff8f6`](https://github.com/nodejs/node/commit/1469eff8f6)] - **test**: replaced fixturesDir with common.fixtures (Dolapo Toki) [#15836](https://github.com/nodejs/node/pull/15836)
- [[`b463259efb`](https://github.com/nodejs/node/commit/b463259efb)] - **test**: use fixtures.fixturesDir (Gene Wu) [#15822](https://github.com/nodejs/node/pull/15822)
- [[`1e0a55529f`](https://github.com/nodejs/node/commit/1e0a55529f)] - **test**: replaces fixturesDir with fixtures methods (Christian Murphy) [#15817](https://github.com/nodejs/node/pull/15817)
- [[`6f6b2cb7b7`](https://github.com/nodejs/node/commit/6f6b2cb7b7)] - **test**: replace fixturesDir with fixtures methods (Cristian Peñarrieta) [#15833](https://github.com/nodejs/node/pull/15833)
- [[`0418a4170b`](https://github.com/nodejs/node/commit/0418a4170b)] - **test**: update test-https-server-keep-alive-timeout (Tom Boutell) [#15805](https://github.com/nodejs/node/pull/15805)
- [[`1cfff97a2b`](https://github.com/nodejs/node/commit/1cfff97a2b)] - **test**: fixtures in test-process-redirect-warnings (Nicolas Chaulet) [#15917](https://github.com/nodejs/node/pull/15917)
- [[`ba2261a450`](https://github.com/nodejs/node/commit/ba2261a450)] - **test**: update test-crypto-from-binary (Raj Parekh) [#16011](https://github.com/nodejs/node/pull/16011)
- [[`abfb6b1262`](https://github.com/nodejs/node/commit/abfb6b1262)] - **test**: fixtures in test-http2-respond-file-fd-range (kysnm) [#15878](https://github.com/nodejs/node/pull/15878)
- [[`db92df9f67`](https://github.com/nodejs/node/commit/db92df9f67)] - **test**: use fixtures in test-https-set-timeout-server (Bob Clewell) [#15886](https://github.com/nodejs/node/pull/15886)
- [[`529e6851d9`](https://github.com/nodejs/node/commit/529e6851d9)] - **test**: make use of common/fixtures.fixturesDir (Jem Bezooyen) [#15815](https://github.com/nodejs/node/pull/15815)
- [[`7339620466`](https://github.com/nodejs/node/commit/7339620466)] - **test**: use common/fixtures in test-https-close (Alberto Lopez de Lara) [#15870](https://github.com/nodejs/node/pull/15870)
- [[`7a210cd467`](https://github.com/nodejs/node/commit/7a210cd467)] - **test**: use fixtures in test-process-warnings (Suresh Srinivas) [#15869](https://github.com/nodejs/node/pull/15869)
- [[`56cf077e0e`](https://github.com/nodejs/node/commit/56cf077e0e)] - **test**: use fixtures in tls-friendly-error-message (tobyfarley) [#15905](https://github.com/nodejs/node/pull/15905)
- [[`f1b4e7c001`](https://github.com/nodejs/node/commit/f1b4e7c001)] - **test**: add benchmark tests for es (Ethan Arrowood) [#16076](https://github.com/nodejs/node/pull/16076)
- [[`68b59eaaf0`](https://github.com/nodejs/node/commit/68b59eaaf0)] - **test**: use common/fixtures in tls-connect-no-host (Donovan Buck) [#15986](https://github.com/nodejs/node/pull/15986)
- [[`36fa169e01`](https://github.com/nodejs/node/commit/36fa169e01)] - **test**: use common/fixtures in test-https-agent (jpaulptr) [#15941](https://github.com/nodejs/node/pull/15941)
- [[`7a18e6441e`](https://github.com/nodejs/node/commit/7a18e6441e)] - **test**: use common fixtures module (Kat Rosario) [#15856](https://github.com/nodejs/node/pull/15856)
- [[`08fa0040a7`](https://github.com/nodejs/node/commit/08fa0040a7)] - **test**: fs.readFileSync -\> fixtures.readKey (Ethan Brown) [#16030](https://github.com/nodejs/node/pull/16030)
- [[`268ce8cad4`](https://github.com/nodejs/node/commit/268ce8cad4)] - **test**: reduce test-benchmark-http iterations (Rich Trott) [#16137](https://github.com/nodejs/node/pull/16137)
- [[`9808c59b4e`](https://github.com/nodejs/node/commit/9808c59b4e)] - **test**: reduce run time for misc benchmark tests (Rich Trott) [#16120](https://github.com/nodejs/node/pull/16120)
- [[`5f0686ab07`](https://github.com/nodejs/node/commit/5f0686ab07)] - **test**: improve assertion message in dgram test (Shakeel Mohamed) [#16121](https://github.com/nodejs/node/pull/16121)
- [[`5a1867067c`](https://github.com/nodejs/node/commit/5a1867067c)] - **test**: use of fixtures in test-pipe-head (Nicolas Chaulet) [#15868](https://github.com/nodejs/node/pull/15868)
- [[`48d862b0d5`](https://github.com/nodejs/node/commit/48d862b0d5)] - **test**: use fixtures in test-https-localaddress.js (Charles T Wall III) [#15811](https://github.com/nodejs/node/pull/15811)
- [[`ce88af1729`](https://github.com/nodejs/node/commit/ce88af1729)] - **test**: use common/fixtures in fs-symlink test (AlexeyM) [#15830](https://github.com/nodejs/node/pull/15830)
- [[`c89ef9d9aa`](https://github.com/nodejs/node/commit/c89ef9d9aa)] - **test**: replace common.fixtures with fixtures module (Jonathan Eskew) [#15877](https://github.com/nodejs/node/pull/15877)
- [[`181ee5d1ef`](https://github.com/nodejs/node/commit/181ee5d1ef)] - **test**: use fixtures.readKey() (Kasim Doctor) [#15862](https://github.com/nodejs/node/pull/15862)
- [[`859ce10c4d`](https://github.com/nodejs/node/commit/859ce10c4d)] - **test**: added a test comment (Edward Andrew Robinson) [#16003](https://github.com/nodejs/node/pull/16003)
- [[`78ffdb85d0`](https://github.com/nodejs/node/commit/78ffdb85d0)] - **test**: improve assert message (Tri Nguyen) [#15909](https://github.com/nodejs/node/pull/15909)
- [[`6f605b8b7e`](https://github.com/nodejs/node/commit/6f605b8b7e)] - **test**: replace fixturesDir with fixtures method (suraiyah) [#15894](https://github.com/nodejs/node/pull/15894)
- [[`e47366e66a`](https://github.com/nodejs/node/commit/e47366e66a)] - **test**: replace fixturesDir with fixtures module (Joel Dart) [#15848](https://github.com/nodejs/node/pull/15848)
- [[`1a884cd9e8`](https://github.com/nodejs/node/commit/1a884cd9e8)] - **test**: common.fixturesDir -\> common.fixtures (Peter) [#16028](https://github.com/nodejs/node/pull/16028)
- [[`64a1c1f1d4`](https://github.com/nodejs/node/commit/64a1c1f1d4)] - **test**: normalize fixtures use (Ruxandra Fediuc) [#15855](https://github.com/nodejs/node/pull/15855)
- [[`d3bf46982f`](https://github.com/nodejs/node/commit/d3bf46982f)] - **test**: cleaned up assert messages (mrgorbo) [#16032](https://github.com/nodejs/node/pull/16032)
- [[`270dd22d4e`](https://github.com/nodejs/node/commit/270dd22d4e)] - **test**: replace common.fixturesDir w/common.fixtures (Jason Walton) [#15853](https://github.com/nodejs/node/pull/15853)
- [[`0e2db0e64e`](https://github.com/nodejs/node/commit/0e2db0e64e)] - **test**: fix lint error (Rich Trott) [#16145](https://github.com/nodejs/node/pull/16145)
- [[`ceca43ba39`](https://github.com/nodejs/node/commit/ceca43ba39)] - **test**: replace common.fixturesDir with commonfixtures (Feon Sua) [#15832](https://github.com/nodejs/node/pull/15832)
- [[`ae75af5292`](https://github.com/nodejs/node/commit/ae75af5292)] - **test**: replace fixturesDir to use fixtures module (Josh Lim) [#15831](https://github.com/nodejs/node/pull/15831)
- [[`d96b2a7077`](https://github.com/nodejs/node/commit/d96b2a7077)] - **test**: switch to use common.fixtures module for fixturesDir (r1cebank) [#15821](https://github.com/nodejs/node/pull/15821)
- [[`c2345391cd`](https://github.com/nodejs/node/commit/c2345391cd)] - **test**: replace common.fixturesDir with fixtures (Steven Scott) [#15845](https://github.com/nodejs/node/pull/15845)
- [[`d4b454dd3c`](https://github.com/nodejs/node/commit/d4b454dd3c)] - **test**: fixturesDir replaced to fixtures module (Pawel Golda) [#15809](https://github.com/nodejs/node/pull/15809)
- [[`d7ec89ce34`](https://github.com/nodejs/node/commit/d7ec89ce34)] - **test**: replace common.fixturesDir with fixtures (Stefania Sharp) [#16015](https://github.com/nodejs/node/pull/16015)
- [[`25335ec513`](https://github.com/nodejs/node/commit/25335ec513)] - **test**: replaces common.fixturesDir usage (Ruy Adorno) [#15818](https://github.com/nodejs/node/pull/15818)
- [[`a79b982155`](https://github.com/nodejs/node/commit/a79b982155)] - **test**: added fs benchmark test (Charlie Duong) [#16049](https://github.com/nodejs/node/pull/16049)
- [[`92b5cf1d49`](https://github.com/nodejs/node/commit/92b5cf1d49)] - **test**: use common.fixtures.path() (Tobias Kieslich) [#16112](https://github.com/nodejs/node/pull/16112)
- [[`b8f6d33f52`](https://github.com/nodejs/node/commit/b8f6d33f52)] - **test**: replace common.fixturesDir with fixtures (Shakeel Mohamed) [#15857](https://github.com/nodejs/node/pull/15857)
- [[`7788207a91`](https://github.com/nodejs/node/commit/7788207a91)] - **test**: use fixtures module in test (Nigel Kibodeaux) [#16117](https://github.com/nodejs/node/pull/16117)
- [[`37235a392c`](https://github.com/nodejs/node/commit/37235a392c)] - **test**: use template literals in test-string-decoder (Edward Andrew Robinson) [#15884](https://github.com/nodejs/node/pull/15884)
- [[`c7b0c9b2cd`](https://github.com/nodejs/node/commit/c7b0c9b2cd)] - **test**: switch to fixtures module (Christopher Sidebottom) [#15880](https://github.com/nodejs/node/pull/15880)
- [[`14b3f3af6a`](https://github.com/nodejs/node/commit/14b3f3af6a)] - **test**: rewrite assert message (Martin Michaelis) [#15879](https://github.com/nodejs/node/pull/15879)
- [[`d20f8d3d1f`](https://github.com/nodejs/node/commit/d20f8d3d1f)] - **test**: change fixturesDir to fixtures.path (Guilherme Akio Sakae) [#15863](https://github.com/nodejs/node/pull/15863)
- [[`228940d6ca`](https://github.com/nodejs/node/commit/228940d6ca)] - **test**: replace common.fixturesDir with fixtures (Chris Jimenez) [#15806](https://github.com/nodejs/node/pull/15806)
- [[`80b276c910`](https://github.com/nodejs/node/commit/80b276c910)] - **test**: replace fixturesDir with common.fixtures (Oliver Luebeck) [#15907](https://github.com/nodejs/node/pull/15907)
- [[`6dc3c116f0`](https://github.com/nodejs/node/commit/6dc3c116f0)] - **test**: allow short benchmarks for tests (Rich Trott) [#16097](https://github.com/nodejs/node/pull/16097)
- [[`cbbb229c81`](https://github.com/nodejs/node/commit/cbbb229c81)] - **test**: update http test client function signatures (Jakub Mrowiec - Alkagar) [#15807](https://github.com/nodejs/node/pull/15807)
- [[`0eb2826688`](https://github.com/nodejs/node/commit/0eb2826688)] - **test**: reduce run time for string_decoder benchmark (Rich Trott) [#16118](https://github.com/nodejs/node/pull/16118)
- [[`497bcebf82`](https://github.com/nodejs/node/commit/497bcebf82)] - **test**: increase test coverage of readline-interface (Daniel Kostro) [#16062](https://github.com/nodejs/node/pull/16062)
- [[`1be3f50d24`](https://github.com/nodejs/node/commit/1be3f50d24)] - **test**: improve crypto HMAC test assertions (Seth Holladay) [#16026](https://github.com/nodejs/node/pull/16026)
- [[`2c44072ea1`](https://github.com/nodejs/node/commit/2c44072ea1)] - **test**: add tests of querystring benchmark (Jonathan Eskew) [#16052](https://github.com/nodejs/node/pull/16052)
- [[`a8d1a56bec`](https://github.com/nodejs/node/commit/a8d1a56bec)] - **test**: make it easier to run tests for subsystems (Benjamin Coe) [#15450](https://github.com/nodejs/node/pull/15450)
- [[`3c98a2e67e`](https://github.com/nodejs/node/commit/3c98a2e67e)] - **timers**: fix eventloop block (zhangzifa) [#15072](https://github.com/nodejs/node/pull/15072)
- [[`eeef06a4bb`](https://github.com/nodejs/node/commit/eeef06a4bb)] - **tls**: properly track writeQueueSize during writes (Anatoli Papirovski) [#15791](https://github.com/nodejs/node/pull/15791)
- [[`509b3b81d6`](https://github.com/nodejs/node/commit/509b3b81d6)] - **tools**: enable additional eslint rules (Anatoli Papirovski) [#16243](https://github.com/nodejs/node/pull/16243)
- [[`37b50357c7`](https://github.com/nodejs/node/commit/37b50357c7)] - **tools**: update to ESLint 4.8.0 (Anatoli Papirovski) [#16199](https://github.com/nodejs/node/pull/16199)
- [[`118724f88e`](https://github.com/nodejs/node/commit/118724f88e)] - **tools**: rename unused variale in more pythonic way (Nikhil Komawar) [#16171](https://github.com/nodejs/node/pull/16171)
- [[`f89cc5d279`](https://github.com/nodejs/node/commit/f89cc5d279)] - **tools**: minor performance improvement (Anna Henningsen) [#16125](https://github.com/nodejs/node/pull/16125)
- [[`917ccc94b8`](https://github.com/nodejs/node/commit/917ccc94b8)] - **tools**: use template literal in error message (Tim Chon) [#15846](https://github.com/nodejs/node/pull/15846)
- [[`94a76162ef`](https://github.com/nodejs/node/commit/94a76162ef)] - **tools**: replace string concat (Kai Cataldo) [#15850](https://github.com/nodejs/node/pull/15850)
- [[`397ad0936d`](https://github.com/nodejs/node/commit/397ad0936d)] - **tools**: replace string concatenation with template (Nicola Del Gobbo) [#15827](https://github.com/nodejs/node/pull/15827)
- [[`e1cff102f4`](https://github.com/nodejs/node/commit/e1cff102f4)] - **tty,doc**: add type-check to isatty (Bryan English) [#15567](https://github.com/nodejs/node/pull/15567)
- [[`6ff397db89`](https://github.com/nodejs/node/commit/6ff397db89)] - **url**: using util.\_extend for improving profermace (Weijia Wang) [#16081](https://github.com/nodejs/node/pull/16081)
- [[`f5e56aac0c`](https://github.com/nodejs/node/commit/f5e56aac0c)] - **url**: fix port overflow checking (Rimas Misevičius) [#15794](https://github.com/nodejs/node/pull/15794)
- [[`c2b1435b55`](https://github.com/nodejs/node/commit/e82e2745af)] - **zlib**: gracefully set windowBits from 8 to 9 (Myles Borins) [nodejs-private/node-private#95](https://github.com/nodejs-private/node-private/pull/95)

Windows 32-bit Installer: https://nodejs.org/dist/v8.8.0/node-v8.8.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v8.8.0/node-v8.8.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v8.8.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v8.8.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v8.8.0/node-v8.8.0.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v8.8.0/node-v8.8.0-darwin-x64.tar.gz \
Linux 32-bit Binary: https://nodejs.org/dist/v8.8.0/node-v8.8.0-linux-x86.tar.xz \
Linux 64-bit Binary: https://nodejs.org/dist/v8.8.0/node-v8.8.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v8.8.0/node-v8.8.0-linux-ppc64le.tar.xz \
Linux PPC BE 64-bit Binary: https://nodejs.org/dist/v8.8.0/node-v8.8.0-linux-ppc64.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v8.8.0/node-v8.8.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v8.8.0/node-v8.8.0-aix-ppc64.tar.gz \
SmartOS 32-bit Binary: https://nodejs.org/dist/v8.8.0/node-v8.8.0-sunos-x86.tar.xz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v8.8.0/node-v8.8.0-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v8.8.0/node-v8.8.0-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v8.8.0/node-v8.8.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v8.8.0/node-v8.8.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v8.8.0/node-v8.8.0.tar.gz \
Other release files: https://nodejs.org/dist/v8.8.0/ \
Documentation: https://nodejs.org/docs/v8.8.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

4524fccb73aae20d6ebd1900c0dd131fab7d3807c5597eb847293562513e43bf  node-v8.8.0-aix-ppc64.tar.gz
69a37e240fac48289a2a5bb75ab96091f8d6457eeaf23c0a5125250abe418176  node-v8.8.0-darwin-x64.tar.gz
b2f1440a7669af1e76be7ee7b275c97cbe79ee41a7e6278518d00b5a142d8d12  node-v8.8.0-darwin-x64.tar.xz
272888d6830c0a8c881c947cd5108a51f657f8e050f51b73c6eed1479dec55a3  node-v8.8.0-headers.tar.gz
85a4b3b5ad9d95e0e7df1c00c6496601cd1b4b9844c0bc57310741589bc7dc48  node-v8.8.0-headers.tar.xz
58bb50e792f60651f48b26a9e7e68bd10f51ecc254a2be6ea759f26ebe6b1a21  node-v8.8.0-linux-arm64.tar.gz
454617ff94882d9a81dca0840065fb5a928ff68e8e8efe7c1748d996ab757b2e  node-v8.8.0-linux-arm64.tar.xz
15145f5b3c8d05abb786b7877e66ad70459cd5d949bcc63ed5a307a2e1cc300f  node-v8.8.0-linux-armv6l.tar.gz
acd924e0bd5fe95e7fad2a97d1e5ffd43e67e6f880e1947dc805a9fe4546b31e  node-v8.8.0-linux-armv6l.tar.xz
c6e30d71dba7d735aeb969205c5aa5f9d7574e19fee5e0aa3434bcc8c4d74683  node-v8.8.0-linux-armv7l.tar.gz
68b6b2393f0d379a8c3e2f7416e4577996047839fae8076e479a19ae0d90e053  node-v8.8.0-linux-armv7l.tar.xz
a0e9e51e4b47cacda44db1b9e6bd47d5bc0445b08784aaf4f7181a71a5dfcffa  node-v8.8.0-linux-ppc64le.tar.gz
942aade9651a425b171d2699d8c849eb065e49885d6e346c3bf23a5bc9f0b503  node-v8.8.0-linux-ppc64le.tar.xz
d5878ed00864b724339d3752be8d830e2eb7c08c4ce90a9d0ea80a7e78e03dcc  node-v8.8.0-linux-ppc64.tar.gz
9eccec7aa1c983ebdb2688f1788b432c5c8908d98c593fd989f0755249f90866  node-v8.8.0-linux-ppc64.tar.xz
aae9ea4e9b213bf40bf5305bf3ac82fa82efd33a4216fea01d430300a9757320  node-v8.8.0-linux-s390x.tar.gz
5405e9f28db36d798253e74d6a52764a91170bf8578fc793620b598c958c5522  node-v8.8.0-linux-s390x.tar.xz
3d988ec9d7e50a030b5aa3f36840a6d37219ff0f5bcd7da255dc9475e7d99813  node-v8.8.0-linux-x64.tar.gz
4304c297f41085ac1f7a6a8e68496d9fd5aa3b92590e7c3c6015a23939767e72  node-v8.8.0-linux-x64.tar.xz
6fcb04e49725378a109388610ac14142adaa001bcf292982077968406fb04a54  node-v8.8.0-linux-x86.tar.gz
6257c617a12eb3a4c5381629c5a8a6166b20efd853ae09fdb1ac10c2c259cdd1  node-v8.8.0-linux-x86.tar.xz
9968e00b7530203d26baa6aa2895717df5a5c433dc9a8bad032c8d2cfb6a425a  node-v8.8.0.pkg
bf8d32f0e8c59c60b0de386b557057618476261ce4d0668fc493b3774f7af7f3  node-v8.8.0-sunos-x64.tar.gz
3233c927cc9fb99d17a2bab8f0fdf510b1e3b3b07a0eafbb0690da585324ab28  node-v8.8.0-sunos-x64.tar.xz
9c9b535fab082990689360d7857ffc8aea5f0e371e28be132f98b9f07ff5be02  node-v8.8.0-sunos-x86.tar.gz
a143ba487b1aba035634eec41ad03ba7681b7a1ed78d70f551b9471f3df01dc8  node-v8.8.0-sunos-x86.tar.xz
669e0054a25c7f21e6d8bef182da566de35fb3b3441e8f90b6da9585c1f82caf  node-v8.8.0.tar.gz
854f32d27cd62895a13a6af43b87e0542f512f40af960244d15cf16da0914ecf  node-v8.8.0.tar.xz
a4bbee86af96c01159178ace42643942bc55ce3907e49f01dd1810f19cfef822  node-v8.8.0-win-x64.7z
a7a1fd2c5f5c967d7df70fdec1a8b2d2d2b1b411fcdd4f23bcf8c9c837a7c3cb  node-v8.8.0-win-x64.zip
2136c4c0a489074ad317603605ae03ada260b21e6d0a40ef0c7282b6130f50bd  node-v8.8.0-win-x86.7z
abec6b1cec0b768e57121d98d1546741bf33e29399e348bbd26d6c3fd4b359ee  node-v8.8.0-win-x86.zip
fcdca3cd23c9e7a7dd277e53dc912a0d587cb59e70d754060ccc1ea10e40b0ce  node-v8.8.0-x64.msi
2b33f8ab6273bf5462ae8253e141a468fdffea07b50402534422108491c36bd9  node-v8.8.0-x86.msi
e4bccfc5c35aace727b6b3c95a04065e5193c3cd413ec7ab62bbd7bed3e4a24b  win-x64/node.exe
fb5dc64464c1d1a28235a579f09c17053f2c791b5af0af5308ac385c9796747b  win-x64/node.lib
eaa509f60be69d0683b92695fec5cb42c233c0e7b9a96dc47e1fe8f1f7af6a69  win-x64/node_pdb.7z
3b6073e505ff15afe5e8e8c99dbe190c16cfaeb44dec680730264c42a7e54e55  win-x64/node_pdb.zip
4433dec0d966f01e2e72ba178acf1f9ef141e73dc0310856981582c3111c42ff  win-x86/node.exe
3bacf59ee9027c21ea8789d95769048d2ff036507c51d2dfd9623543915b6d91  win-x86/node.lib
cc0bbd17f16224f74f22d660f26263497a34d178f4d7d2cb9e328a80f4ca3f90  win-x86/node_pdb.7z
2f78c77b7a158b945264783de9b2cccbbf5be98ca6750faef9519aa07d7c64a5  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQEzBAEBCAAdFiEEDv/hvO/ZyE49CYFSkzsB9AtcqUYFAlnvuDkACgkQkzsB9Atc
qUat1wf/e2B+pccmrp7jrdpl+1b7CjDQugzgvJuJd6W+D6Dyq47gy/Xw5cvRx2/2
77WSUd1j6nGghRC2kgr/6iHyf6vg2e7IxJ7+nD9NplWbmF1T1ZtpYx1ojbFa5muD
1/JKQM6Dv/sh/LeL8zw7JSR4Mqtya7/3KlFVBD47Lx02o4fyAiGXy7mcWddZwIMd
FgOYFhmGQre3aBroRvZ06gqRGHXZNezNzQ4joIt3Ro1eSf0gJ83MgLTJ8F5ajdM0
nqi6rKS97eaMdK/DFE/5e/AmUZYwSa8ky/RZGohldAqVDg7oWwWN+KScvFTB5hC1
lqsirns3wFGsWgsly+bq+6P8yt+zFA==
=bc6R
-----END PGP SIGNATURE-----

```
