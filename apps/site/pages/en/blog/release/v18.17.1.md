---
date: '2023-08-09T17:09:37.538Z'
category: release
title: Node v18.17.1 (LTS)
layout: blog-post
author: <PERSON>
---

### Notable Changes

The following CVEs are fixed in this release:

- [CVE-2023-32002](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2023-32002): Policies can be bypassed via Module.\_load (High)
- [CVE-2023-32006](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2023-32006): Policies can be bypassed by module.constructor.createRequire (Medium)
- [CVE-2023-32559](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2023-32559): Policies can be bypassed via process.binding (Medium)
- OpenSSL Security Releases
  - [OpenSSL security advisory 14th July](https://mta.openssl.org/pipermail/openssl-announce/2023-July/000264.html).
  - [OpenSSL security advisory 19th July](https://mta.openssl.org/pipermail/openssl-announce/2023-July/000265.html).
  - [OpenSSL security advisory 31st July](https://mta.openssl.org/pipermail/openssl-announce/2023-July/000267.html)

More detailed information on each of the vulnerabilities can be found in [August 2023 Security Releases](/blog/vulnerability/august-2023-security-releases/) blog post.

### Commits

- \[[`fe3abdf82e`](https://github.com/nodejs/node/commit/fe3abdf82e)] - **deps**: update archs files for openssl-3.0.10+quic1 (Node.js GitHub Bot) [#49036](https://github.com/nodejs/node/pull/49036)
- \[[`2c5a522d9c`](https://github.com/nodejs/node/commit/2c5a522d9c)] - **deps**: upgrade openssl sources to quictls/openssl-3.0.10+quic1 (Node.js GitHub Bot) [#49036](https://github.com/nodejs/node/pull/49036)
- \[[`15bced0bde`](https://github.com/nodejs/node/commit/15bced0bde)] - **policy**: handle Module.constructor and main.extensions bypass (RafaelGSS) [nodejs-private/node-private#417](https://github.com/nodejs-private/node-private/pull/417)
- \[[`d4570fae35`](https://github.com/nodejs/node/commit/d4570fae35)] - **policy**: disable process.binding() when enabled (Tobias Nießen) [nodejs-private/node-private#460](https://github.com/nodejs-private/node-private/pull/460)

Windows 32-bit Installer: https://nodejs.org/dist/v18.17.1/node-v18.17.1-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v18.17.1/node-v18.17.1-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v18.17.1/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v18.17.1/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v18.17.1/node-v18.17.1.pkg \
macOS Apple Silicon 64-bit Binary: https://nodejs.org/dist/v18.17.1/node-v18.17.1-darwin-arm64.tar.gz \
macOS Intel 64-bit Binary: https://nodejs.org/dist/v18.17.1/node-v18.17.1-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v18.17.1/node-v18.17.1-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v18.17.1/node-v18.17.1-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v18.17.1/node-v18.17.1-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v18.17.1/node-v18.17.1-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v18.17.1/node-v18.17.1-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v18.17.1/node-v18.17.1-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v18.17.1/node-v18.17.1.tar.gz \
Other release files: https://nodejs.org/dist/v18.17.1/ \
Documentation: https://nodejs.org/docs/v18.17.1/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

1467acda7bc502fad6150ec12d8d3ee8bdf26e9217f2b61eeb675a4f26202cb5  node-v18.17.1-aix-ppc64.tar.gz
18ca716ea57522b90473777cb9f878467f77fdf826d37beb15a0889fdd74533e  node-v18.17.1-darwin-arm64.tar.gz
e33c6391a33187c4eccf62661c9da3a67aa50752abae8fe75214e7e57b9292cc  node-v18.17.1-darwin-arm64.tar.xz
b3e083d2715f07ec3f00438401fb58faa1e0bdf3c7bde9f38b75ed17809d92fa  node-v18.17.1-darwin-x64.tar.gz
bb15810944a6f77dcc79c8f8da01a605473e806c4ab6289d0a497f45a200543b  node-v18.17.1-darwin-x64.tar.xz
04ee43f5b27200379cf531f954d4de289b4870c36161c48d427fa741be3e67fb  node-v18.17.1-headers.tar.gz
af91a6f383986481eb00681a83fa6dba02fe69bee731f27503208012ec7b82a7  node-v18.17.1-headers.tar.xz
8f5203f5c6dc44ea50ac918b7ecbdb1c418e4f3d9376d8232a1ef9ff38f9c480  node-v18.17.1-linux-arm64.tar.gz
3f933716a468524acb68c2514d819b532131eb50399ee946954d4a511303e1bb  node-v18.17.1-linux-arm64.tar.xz
1ab79868859b2d37148c6d8ecee3abb5ee55b88731ab5df01928ed4f6f9bfbad  node-v18.17.1-linux-armv7l.tar.gz
bf7fb6c7009726e594c9e23192c644787b4637eac503e229d750e665d143ef0e  node-v18.17.1-linux-armv7l.tar.xz
d04ff995044121a8493f6ebc976999d519dd06f03359da62c7538ebd37d79907  node-v18.17.1-linux-ppc64le.tar.gz
8046fa0eb23c8bb57c52a62fc84315712f280d482b6f0358dcedc7d26bfb4065  node-v18.17.1-linux-ppc64le.tar.xz
814f511534121d9e3a378cb8f39c940813f46c8c5c9ea5305c651fa16986ab98  node-v18.17.1-linux-s390x.tar.gz
6f30e71d5f6d58f9bef88adf527232cbd82305472f20cc3d1fe8a984f549be0d  node-v18.17.1-linux-s390x.tar.xz
2cb75f2bc04b0a3498733fbee779b2f76fe3f655188b4ac69ef2887b6721da2d  node-v18.17.1-linux-x64.tar.gz
07e76408ddb0300a6f46fcc9abc61f841acde49b45020ec4e86bb9b25df4dced  node-v18.17.1-linux-x64.tar.xz
5cdb6e55dbcd3c874814f4110404249861abbb94614fb51a412576ff2bd89dde  node-v18.17.1.pkg
1157525a819c395020795ff8c49eee7472b8666cc256b45558b9cbe2e0864c35  node-v18.17.1.tar.gz
f215cf03d0f00f07ac0b674c6819f804c1542e16f152da04980022aeccf5e65a  node-v18.17.1.tar.xz
5541a746ffe15beb0f96673f29e9d610bbd30fe1f94291716e7170745bfa4757  node-v18.17.1-win-x64.7z
afc83f5cf6e8b45a4d3fb842904f604dcd271fefada31ad6654f8302f8da28c9  node-v18.17.1-win-x64.zip
99b7965415753170a0eafaa20741262e43ebd62a6b37fef8ce4e44f5fdd915a3  node-v18.17.1-win-x86.7z
462c533c2797e410bb62c25ee148e8c5ce0c4eead973d1a52ac405fe3d3b6054  node-v18.17.1-win-x86.zip
245b718dc1c12715c52fa6812bc95b0e577a0760e89c26e528e1d59f002d0ab8  node-v18.17.1-x64.msi
e497443428f546bc5ef9878b3632052747715a599c09d683824db5179a31f613  node-v18.17.1-x86.msi
afb45186ad4f4217c2fc1dfc2239ff5ab016ef0ba5fc329bc6aa8fd10c7ecc88  win-x64/node.exe
fce36923d974ac8f9cea4bd68e56ab055797fd5b5ede5041b228d642a0d9bb40  win-x64/node.lib
60c0f7547df22f55e491ff2b58d5ad82231e68f3dfb5ab6a8680f66cbe9b196a  win-x64/node_pdb.7z
3225ad25d5895956013bf982a33a064b8c4a1988cac768b99e7af8465687b77b  win-x64/node_pdb.zip
1040b46f2c23ff232582a34fab210297688b3302b60f18204c40f46f53973d4e  win-x86/node.exe
7982f49aac6ff5b4cfaa236793f62a8c18b0bc9f1f2903617b9fdafee377bb2b  win-x86/node.lib
641bd49ee71d938e55fd0a1746b233bd807c23578c73a90ac4811e9c40e92984  win-x86/node_pdb.7z
3fdd11b9cc07b756d551376d1523b0002532a8c4496361f5fbab2c1f2b739498  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQGzBAEBCAAdFiEEiQwI24V5Fi/uDfnbi+q0389VXvQFAmTTx6UACgkQi+q0389V
XvRD3gwAoJ3GYb9Q7RV5lGWQi/08mnY8YIXr5YD62hw/qGnW7n56OqzOXitXlMoc
7U/HrGH0IdoIOBeUFAzdWiSXU8Fzb47YlUxF0X5Bz33mPkB28lamCPXWYlKk6ZGp
xTiEpxw2Z9pWVYSrW1SZWGxDbEpEvn1nqGBTxz8inklRV/KtA9g75g26dnJNziIc
f+XcAlwyJnsZurbfX/3TkRuonNK6HGUAzbMQHmfkcLB3yvuFc7a/guFDjWGDURD9
BTET8fdvj7i8Koww0xeRCCRzA5Z4VYp6ICOZUO2qTnIHDsmij3x8zVMfgPu80S4s
cm/5TBwSqpal5WF1gY/qgZxNuh7muPoApU5M60a5oadfQzrfxFJ3lDnWftdp6tNO
x6uFy7aOut1EXcOdxx6bIcpxNPBs9RObT7Lq7vwo7FF07w0C3lfVVuK9rUn7N57k
9WhBuRS+rKI7CI4pDzrJySLk6GkBNBg3+hB3n81UnfAZViyF06iMU/g17Mez9gOA
3MdJgOAp
=Un1l
-----END PGP SIGNATURE-----

```
