---
date: '2021-07-29T16:51:01.576Z'
category: release
title: Node v12.22.4 (LTS)
layout: blog-post
author: <PERSON>
---

### Notable Changes

- **CVE-2021-22930**: Use after free on close http2 on stream canceling (High)
  - Node.js is vulnerable to a use after free attack where an attacker might be able to exploit the memory corruption, to change process behavior. You can read more about it in https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2021-22930

### Commits

- [[`499e56babe`](https://github.com/nodejs/node/commit/499e56babe)] - **build**: fix label-pr workflow (<PERSON><PERSON><PERSON>) [#38399](https://github.com/nodejs/node/pull/38399)
- [[`98ac3c4108`](https://github.com/nodejs/node/commit/98ac3c4108)] - **build**: label PRs with GitHub Action instead of nodejs-github-bot (<PERSON>) [#38301](https://github.com/nodejs/node/pull/38301)
- [[`ddc8dde150`](https://github.com/nodejs/node/commit/ddc8dde150)] - **deps**: upgrade npm to 6.14.14 (Darcy Clarke) [#39553](https://github.com/nodejs/node/pull/39553)
- [[`e11a862eed`](https://github.com/nodejs/node/commit/e11a862eed)] - **deps**: update to c-ares 1.17.1 (Danny Sonnenschein) [#36207](https://github.com/nodejs/node/pull/36207)
- [[`39e9cd540f`](https://github.com/nodejs/node/commit/39e9cd540f)] - **deps**: restore minimum ICU version to 65 (Richard Lau) [#39068](https://github.com/nodejs/node/pull/39068)
- [[`e459c79b02`](https://github.com/nodejs/node/commit/e459c79b02)] - **deps**: V8: cherry-pick 035c305ce776 (Michaël Zasso) [#38497](https://github.com/nodejs/node/pull/38497)
- [[`b3c698a5d8`](https://github.com/nodejs/node/commit/b3c698a5d8)] - **deps**: update to cjs-module-lexer@1.2.1 (Guy Bedford) [#38450](https://github.com/nodejs/node/pull/38450)
- [[`7d5a2f9588`](https://github.com/nodejs/node/commit/7d5a2f9588)] - **deps**: update to cjs-module-lexer@1.1.1 (Guy Bedford) [#37992](https://github.com/nodejs/node/pull/37992)
- [[`906b43e586`](https://github.com/nodejs/node/commit/906b43e586)] - **deps**: V8: update build dependencies (Michaël Zasso) [#39245](https://github.com/nodejs/node/pull/39245)
- [[`15b91fa3fa`](https://github.com/nodejs/node/commit/15b91fa3fa)] - **deps**: V8: backport 895949419186 (Michaël Zasso) [#39245](https://github.com/nodejs/node/pull/39245)
- [[`8046daf09f`](https://github.com/nodejs/node/commit/8046daf09f)] - **deps**: V8: cherry-pick 0b3a4ecf7083 (Michaël Zasso) [#39245](https://github.com/nodejs/node/pull/39245)
- [[`f4377b13a6`](https://github.com/nodejs/node/commit/f4377b13a6)] - **deps**: V8: cherry-pick 7c182bd65f42 (Michaël Zasso) [#39245](https://github.com/nodejs/node/pull/39245)
- [[`add7b5b4c2`](https://github.com/nodejs/node/commit/add7b5b4c2)] - **deps**: V8: cherry-pick cc641f6be756 (Michaël Zasso) [#39245](https://github.com/nodejs/node/pull/39245)
- [[`a73275f056`](https://github.com/nodejs/node/commit/a73275f056)] - **deps**: V8: cherry-pick 7b3332844212 (Michaël Zasso) [#39245](https://github.com/nodejs/node/pull/39245)
- [[`492b0d6b37`](https://github.com/nodejs/node/commit/492b0d6b37)] - **deps**: V8: cherry-pick e6f62a41f5ee (Michaël Zasso) [#39245](https://github.com/nodejs/node/pull/39245)
- [[`2b54156260`](https://github.com/nodejs/node/commit/2b54156260)] - **deps**: V8: cherry-pick 92e6d3317082 (Michaël Zasso) [#39245](https://github.com/nodejs/node/pull/39245)
- [[`bbceab4d91`](https://github.com/nodejs/node/commit/bbceab4d91)] - **deps**: V8: backport 1b1eda0876aa (Michaël Zasso) [#39245](https://github.com/nodejs/node/pull/39245)
- [[`93a1a3c5ae`](https://github.com/nodejs/node/commit/93a1a3c5ae)] - **deps**: V8: cherry-pick 530080c44af2 (Milad Fa) [#38509](https://github.com/nodejs/node/pull/38509)
- [[`b263f2585a`](https://github.com/nodejs/node/commit/b263f2585a)] - **http2**: on receiving rst_stream with cancel code add it to pending list (Akshay K) [#39423](https://github.com/nodejs/node/pull/39423)
- [[`3e4bc1b0d3`](https://github.com/nodejs/node/commit/3e4bc1b0d3)] - **module**: fix legacy `node` specifier resolution to resolve `"main"` field (Antoine du Hamel) [#38979](https://github.com/nodejs/node/pull/38979)
- [[`f552c45676`](https://github.com/nodejs/node/commit/f552c45676)] - **src**: move CHECK in AddIsolateFinishedCallback (Fedor Indutny) [#38010](https://github.com/nodejs/node/pull/38010)
- [[`30ce0e66ae`](https://github.com/nodejs/node/commit/30ce0e66ae)] - **src**: update cares_wrap OpenBSD defines (Anna Henningsen) [#38670](https://github.com/nodejs/node/pull/38670)

Windows 32-bit Installer: https://nodejs.org/dist/v12.22.4/node-v12.22.4-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v12.22.4/node-v12.22.4-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v12.22.4/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v12.22.4/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v12.22.4/node-v12.22.4.pkg \
macOS Intel 64-bit Binary: https://nodejs.org/dist/v12.22.4/node-v12.22.4-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v12.22.4/node-v12.22.4-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v12.22.4/node-v12.22.4-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v12.22.4/node-v12.22.4-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v12.22.4/node-v12.22.4-aix-ppc64.tar.gz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v12.22.4/node-v12.22.4-sunos-x64.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v12.22.4/node-v12.22.4-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v12.22.4/node-v12.22.4-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v12.22.4/node-v12.22.4.tar.gz \
Other release files: https://nodejs.org/dist/v12.22.4/ \
Documentation: https://nodejs.org/docs/v12.22.4/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

efc0252457e8bbc4d703a672e2f58a4da9ebc14eb07e7a16c9dab2fbc732f6a5  node-v12.22.4-aix-ppc64.tar.gz
6e6842468ff8b50562098e41f9fb6af7c3acbefbd018696f4ac7c2d9b7faac48  node-v12.22.4-darwin-x64.tar.gz
0c8fe8b0c6c8d34eda207d81431dfe7669de62e26b9ba900f81281b12e0d87aa  node-v12.22.4-darwin-x64.tar.xz
a13fded3cc808fe8396b97af065450db8e458bdcb7171cfc4e19e6ff0006eee4  node-v12.22.4-headers.tar.gz
8c66ac12087638124967bfc91f55722119018f9ff8fc3be79171f75935d811f1  node-v12.22.4-headers.tar.xz
c104dff52409d27836f7c4529c7f3cce6c76a521b8b834e338bcbf6eed4abc18  node-v12.22.4-linux-arm64.tar.gz
db84c7a5a34ffdaabcc3edd715f0ce71158306b10e3d85c8a3d4f894eeab8cf8  node-v12.22.4-linux-arm64.tar.xz
8d5164221a89c3ae4e7cbb5a5bb5b22fd3bf87d058295fe97e1b1e3376fafe33  node-v12.22.4-linux-armv7l.tar.gz
2484a2f7161eda044e0b83c421a734ff615d869eea9fc501b687edb6efa889eb  node-v12.22.4-linux-armv7l.tar.xz
2f8243adeb44aba9f1ff7ca62f79c923fd08093787e3b045e40c96d56311f733  node-v12.22.4-linux-ppc64le.tar.gz
cf7c930124a62bc7255977643fdc902b4edcdc084d4b062f6c5b0ef8efd09234  node-v12.22.4-linux-ppc64le.tar.xz
a26088c02efa4cf07c5b8aaf9bf1678c83991bc8ef36683cd5846563d67e2730  node-v12.22.4-linux-s390x.tar.gz
d080306bb610e73e4d36303773927551b781254e2960d6886c6878933a3ef3f4  node-v12.22.4-linux-s390x.tar.xz
2dde8a22ad15b8e270fee42ab40de71aed7bd97c10e7d04cd826430400fff601  node-v12.22.4-linux-x64.tar.gz
b699789d93a8d1435bf0b90a859423c9595148830a9304a78b4795d9104a128b  node-v12.22.4-linux-x64.tar.xz
4f1894cd9e8ded74b72cad2686b01bf8fa50cbe2154a6f0f3735800868c1c7f2  node-v12.22.4.pkg
466efc26a1fd5ed4881e943ed90d62eff307cac85802b4aacaa468b72e4e1fdb  node-v12.22.4-sunos-x64.tar.gz
91db23b05a146166a5093eb30ee22a379a61a23eca492824b3192887b912d23c  node-v12.22.4-sunos-x64.tar.xz
613b5a895d85d72b4aa495bdf0ffa483ad8b33635a173c4beb94d2842db740f5  node-v12.22.4.tar.gz
44cd4eab131e5282fc923e9e720d983a0b44c12e4aa4f6c3598dc97ae1e4cd4c  node-v12.22.4.tar.xz
ddf59d18b64fdd4c3d1376195ad474dab50805e1c29be49368f01970d561066b  node-v12.22.4-win-x64.7z
b0f0981a417fad6eca2e012958fd2597ce51f441cc8615ca121752ea1c29de0c  node-v12.22.4-win-x64.zip
0001277cb16722efde2ce3ed9314432a63a4919b79e0d40253775468c3238f30  node-v12.22.4-win-x86.7z
dabb1adb657c941d61dde77d7b703d50c8b93ceba98f4b2064bcdff334bbfbb8  node-v12.22.4-win-x86.zip
5caf61f114e46f04fdb57fc1607b62156d80afc6cf52e39854938ba09f6e9476  node-v12.22.4-x64.msi
687f43f366451ad1a8c2b1fbd19ebb816722ed86e68554be207de9ce47a030c6  node-v12.22.4-x86.msi
29398272a82800e055dbca73c976afe4ee0030b42b6436b5b993eb96774372c7  win-x64/node.exe
28e5c24831deedbf4fb8a9560f2c4f95205479c589f54a9a53ec346f6a5cf8bf  win-x64/node.lib
3940b0fc29b083bb042b96a22349b80e16c1622acb69de7bb8788aaadc93e9bb  win-x64/node_pdb.7z
6d0f10e5399955c92857404fbce668366615e2ddc4f31d40a91ff31a8b10aa6f  win-x64/node_pdb.zip
9f9265a24ea3dc71e2df7951d7b0978ff54e43c4ed20b9935fd44e40f3086bcb  win-x86/node.exe
dad0e6bef1c45f4f43fbf84c33df6b910ace8122eff3f8d39d5ebecd25320ba4  win-x86/node.lib
5e54a615f013ed6c89c6b569c570c5089901edf328152ac806382dfb69e7ba4c  win-x86/node_pdb.7z
69b88e655b4b8eff5975105d25566a71ddecfbf1f1c6f0f08210eca5ce35a040  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEyC+jrhy+3Gvka5NgxDzsRcF6uTwFAmEC2vsACgkQxDzsRcF6
uTw/tw/+MnzYxvM25CUEAEwTe9USs2IC6ro0e6+LL2F/XkEaECt8eOtfpZhF+4Yj
SzL+A82RxctYZEn6nOzTyrRV0PGH8sce4zV7KlK/fwD7xBQ2WmL5V2pffgTEPe7G
brWtdRHBsQg3Y3MfwhZdxldhNbcsrfD1L+rkcybTtYYJHfV2QgwjftBk3/hTs7Ma
EKZFlPapAXW1EeXEg+q7dMtrjxq/McIfoud+gsb5oouJ9bZ2ie2hVR6JS0xkLpix
FVf/v1uqBqQiFaQNOhQawajI8UN6hcOA2OwHO6kCCHUSOdy9aCMYQajaq0RmSD/t
VaElU98+35emRwbPyBTbfoXfDwqAhlHTUgn/7iExBCqIJU37FrkPghXJPQBgK7Rs
x8upnGl/239iyyw7/ATkO9Ibta4fKLMQc3ZF5Go+0M6omCfLPoMLAt3jrUyRKJvF
k+oOhKInTgPD0xm+n2S8rl5NbjsBUOW1WVONrvp1KcQBM4rvh/AZT8U09ejP1pZq
vFddPRYF1pyNnGtyTdpi1jcPWgcEnSSror51rYeUlE5UJtshICq7bALXNHiusEFe
iihjzTi4ef/eLyzxTYSNvGfBBO+Tt61O5r7r6xmRWG7HPZ+QxZd/rBsUk6zdvpFR
PQIAIOzSYDpp+pfULl78La9hY/1KEVbxvg+TWm+VZiyrc2aajaE=
=GiEb
-----END PGP SIGNATURE-----

```
