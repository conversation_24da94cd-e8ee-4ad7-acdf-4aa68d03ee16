---
date: '2023-10-10T16:52:25.133<PERSON>'
category: release
title: Node v18.18.1 (LTS)
layout: blog-post
author: <PERSON>
---

### Notable Changes

This release addresses some regressions that appeared in Node.js 18.18.0:

- (Windows) FS can not handle certain characters in file name [#48673](https://github.com/nodejs/node/issues/48673)
- 18 and 20 node images give error - Text file busy (after re-build images) [nodejs/docker-node#1968](https://github.com/nodejs/docker-node/issues/1968)
- libuv update in 18.18.0 breaks webpack's thread-loader [#49911](https://github.com/nodejs/node/issues/49911)

The libuv 1.45.0 and 1.46.0 updates that were released in Node.js 18.18.0 have been temporarily reverted.

### Commits

- \[[`3e3a75cc46`](https://github.com/nodejs/node/commit/3e3a75cc46)] - _**Revert**_ "**build**: sync libuv header change" (<PERSON>) [#50036](https://github.com/nodejs/node/pull/50036)
- \[[`14ece2c479`](https://github.com/nodejs/node/commit/14ece2c479)] - _**Revert**_ "**deps**: upgrade to libuv 1.45.0" (Richard Lau) [#50036](https://github.com/nodejs/node/pull/50036)
- \[[`022352acbe`](https://github.com/nodejs/node/commit/022352acbe)] - _**Revert**_ "**deps**: upgrade to libuv 1.46.0" (Richard Lau) [#50036](https://github.com/nodejs/node/pull/50036)
- \[[`d9f138189c`](https://github.com/nodejs/node/commit/d9f138189c)] - _**Revert**_ "**deps**: add missing thread-common.c in uv.gyp" (Richard Lau) [#50036](https://github.com/nodejs/node/pull/50036)
- \[[`7a3e1ffbb8`](https://github.com/nodejs/node/commit/7a3e1ffbb8)] - **fs**: make sure to write entire buffer (Robert Nagy) [#49211](https://github.com/nodejs/node/pull/49211)
- \[[`04cba95a67`](https://github.com/nodejs/node/commit/04cba95a67)] - **test**: add `tmpdir.resolve()` (Livia Medeiros) [#49079](https://github.com/nodejs/node/pull/49079)

Windows 32-bit Installer: https://nodejs.org/dist/v18.18.1/node-v18.18.1-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v18.18.1/node-v18.18.1-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v18.18.1/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v18.18.1/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v18.18.1/node-v18.18.1.pkg \
macOS Apple Silicon 64-bit Binary: https://nodejs.org/dist/v18.18.1/node-v18.18.1-darwin-arm64.tar.gz \
macOS Intel 64-bit Binary: https://nodejs.org/dist/v18.18.1/node-v18.18.1-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v18.18.1/node-v18.18.1-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v18.18.1/node-v18.18.1-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v18.18.1/node-v18.18.1-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v18.18.1/node-v18.18.1-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v18.18.1/node-v18.18.1-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v18.18.1/node-v18.18.1-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v18.18.1/node-v18.18.1.tar.gz \
Other release files: https://nodejs.org/dist/v18.18.1/ \
Documentation: https://nodejs.org/docs/v18.18.1/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

88e9b3fda25cfcca569f022681e1fcade677c7ca197b9778f20edba52748a936  node-v18.18.1-aix-ppc64.tar.gz
e2b276e7a62cb4bd487211a185f84a94f012220f9e09d2ecd1f24de482c8f023  node-v18.18.1-darwin-arm64.tar.gz
cb32e86302c0af73543a6e8fedcbcff2979df9695e9d031fd1d484e554975b4b  node-v18.18.1-darwin-arm64.tar.xz
b7237c9c8e6f7ec3a2c61158e9e5e96f1e19d54a1d1d0932b79fede24aa6de86  node-v18.18.1-darwin-x64.tar.gz
8bb169caf8eab395393f4a7e86380e9ad40abbfe09f5b647b1ff592170734940  node-v18.18.1-darwin-x64.tar.xz
7e96d38cca734754e711af9bc43b6b5f2f279951849c1089bdc8825312fd7ddb  node-v18.18.1-headers.tar.gz
35719fc5f74d1abe238dec3b863603b79d1e504edaafbcb26cb6524b874f4a20  node-v18.18.1-headers.tar.xz
1d3ef02cfefc9c87a010ec9ac9a21cafc71096e8159b9f35b419d6e5f71e2f92  node-v18.18.1-linux-arm64.tar.gz
753f90c57173948d06e750a01c49466c3af532e915abead90fd07507daa98ff1  node-v18.18.1-linux-arm64.tar.xz
28f2a8e9e469317b211096a15982f84ebfa6e8cafd72f2980cb8c2e5841837e1  node-v18.18.1-linux-armv7l.tar.gz
60af337de365b58766d6b6e69dac9fba7fbfc8e6142ef58e26220bf5f67cdfcd  node-v18.18.1-linux-armv7l.tar.xz
56e305aedb859131835d1da06ae0956a4b366726a7358cfb42727945c141a98d  node-v18.18.1-linux-ppc64le.tar.gz
627d3c3c8abc757ed7dd5f119fe814587f6150682bd72afd338067f59f7ccd07  node-v18.18.1-linux-ppc64le.tar.xz
edc1eb3371e7442721b48f0a7b8a9d77e553e9ca206c47d6a90b82771af71082  node-v18.18.1-linux-s390x.tar.gz
7d09fa2988870de52f10648e73e1e89a27ad3a807a8a53d09aa21270fc50565d  node-v18.18.1-linux-s390x.tar.xz
9ce4db11f1d8399f6b58aab6858a688b2e09405127b47ebc4594dc8262a5e29f  node-v18.18.1-linux-x64.tar.gz
1db684d7da5fec4ae335ac5d8049a10a8bf30bad9e1d0aa9dcd92baa1f90c6e5  node-v18.18.1-linux-x64.tar.xz
e73818888634e8bf1a9d482e1bd219ca3811c3f194764311e98e3a7b7b8566ab  node-v18.18.1.pkg
f882080b1a5ee2fd5078521ec3016cb8472740d671d19465d377207d671af372  node-v18.18.1.tar.gz
c3c95047ec0c2b2063a5ea4b4f71ee807f6075d1dbeae4f3207cda4b9ae782f6  node-v18.18.1.tar.xz
1fdd4cc029fcd96d7019b8acea4598bc37e31f1ca1413e87f6f7deb762516c8c  node-v18.18.1-win-x64.7z
4c99a7851b16ab4e80fb1203e465688d575f908fda9503d6e7b5c51d1371bfcb  node-v18.18.1-win-x64.zip
f1a4546f6c96872b687d7ad0f2e10cbfc109d30714118ed9b10dad211d5a434a  node-v18.18.1-win-x86.7z
8d2379ba6c95a69ca6387c01b67393c72908d023689d2d519fb6d1ddcd3ca31f  node-v18.18.1-win-x86.zip
3aee7b478867a0efb75781b6790ab649fff7bf887f78d9a64f7e80adf35c305c  node-v18.18.1-x64.msi
6be4816df97a204ea5e4984e439cb5d6a847da0d37ad8cb62a24ddc80e310376  node-v18.18.1-x86.msi
60bd421f615ea4100467255f70ad6dc75ea1cdf5d903173ab068f0d4951af77f  win-x64/node.exe
57f0bf8dbc68fe3468105a7f9d8fe773220f135a171dde2682f30e118a6fc7d7  win-x64/node.lib
ee403afa0ec689fdebb27008fc6c0d37b06af6514448e59689c4608d8666c1c0  win-x64/node_pdb.7z
50b661b6d05cb4f26143a9bd7954ccbe7b4173522d1ef375c10f5646bfc29609  win-x64/node_pdb.zip
0ed68dda91423b4af62856064ca89933ec327ce58248071c4ec860e825dd61d5  win-x86/node.exe
62ee700c739ed89bfea9a5151be27c0ae035aa6dfb3efd7433076de1df389e35  win-x86/node.lib
b21bdaf0edcf37817af4ffd8c91f5bc65dbfbf63f9e74330980d5ac0965e4c83  win-x86/node_pdb.7z
2f66a6d37e85ebb258a4d6fb6e8513de07e05e3fae95fae2f4cc6cfc0c7b7523  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEyC+jrhy+3Gvka5NgxDzsRcF6uTwFAmUlfVgACgkQxDzsRcF6
uTw8nBAAq3KXA9Yf/bPbu9AJI2SN0uMhsCZIyvqKUXuQBdy6uz13nY8+VUahnzIC
mZA5oe6411OP0jw3wIie2h3xK6ww0NeYcU99P7TDy6SpI6lb8yQpnzZEYfHumStz
wU+3K/K05FBUVaEjJlhcjWFRs57GiKUAMd8IzO1eznu1NZwAClLYdJ466OOAacQJ
AUeFInM95ox2ITZMe9+fcgBkeQEKmVyELTKgqF0GlQBySfpst7iCCQWBe0IgKzm/
GSib5L1V9qvWvKVQmLdMqcYa5VkhC8AW8chaUOns13MW47mByKL6ZU7DMQC3sxEv
abxrNTOUyRJyt9cGzIt3jf3boM48FwK8uqVNkjkOlw173mkaBIudlPmXFe8NsLc0
dTTqDNtUeLXVnRq76H2LQLXXKCB/MTg6zQRtNO4Bjw/+uuN6kU9w5P0DcgOnX9Ny
9MepRrPxMr+k3c2GfDS7vWCUP7V4+RvhHQ6T26ibc10hdbEa7/sHXm3g12fxMb11
j9AA8AOLGQmS4+JYLFvpdFhOKs1iIMIUUOMUt6khsZbWAhZAkyEBBZRi5fvgDWlq
5o3RBGg03i4hQWOWoLnJzC7yhTtxOuOyty84pEs8zSH8QyvprGmFcGRtZo52UW9K
WB2SFSw3UnMKwKY1YY6AOaofFMLqCNkwRRRjD1edQ7mjAFOKPl8=
=ls1E
-----END PGP SIGNATURE-----

```
