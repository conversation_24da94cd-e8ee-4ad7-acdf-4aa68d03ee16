---
date: '2015-10-05T20:05:01.925Z'
category: release
title: Node v4.1.2 (Current)
layout: blog-post
author: The Node.js Project
---

## 2015-10-05, Version 4.1.2 (Current), @rvagg

This release includes a fix for [CVE-2015-7384](https://github.com/nodejs/node/issues/3138), a Denial of Service (DoS) bug. Details of the bug can be found on the [nodejs-sec](https://groups.google.com/forum/#!topic/nodejs-sec/fSNEQiuof6I) group. Please subscribe to [nodejs-sec](https://groups.google.com/forum/#!forum/nodejs-sec) to receive future notifications of security releases.

All users of Node.js v4.x should upgrade to v4.1.2, this is a critical update.

### Notable changes

- **http**:
  - Fix out-of-order 'finish' event bug in pipelining that can abort execution, fixes DoS vulnerability [CVE-2015-7384](https://github.com/nodejs/node/issues/3138) (Fedor <PERSON>) [#3128](https://github.com/nodejs/node/pull/3128)
  - Account for pending response data instead of just the data on the current request to decide whether pause the socket or not (Fedor Indutny) [#3128](https://github.com/nodejs/node/pull/3128)
- **libuv**: Upgraded from v1.7.4 to v1.7.5, see [release notes](https://github.com/libuv/libuv/releases/tag/v1.7.5) for details (Saúl Ibarra Corretgé) [#3010](https://github.com/nodejs/node/pull/3010)
  - A better rwlock implementation for all Windows versions
  - Improved AIX support
- **v8**:
  - Upgraded from v4.5.103.33 to v********** (Ali Ijaz Sheikh) [#3117](https://github.com/nodejs/node/pull/3117)
  - Backported [f782159](https://codereview.chromium.org/**********) from v8's upstream to help speed up Promise introspection (Ben Noordhuis) [#3130](https://github.com/nodejs/node/pull/3130)
  - Backported [c281c15](https://codereview.chromium.org/**********) from v8's upstream to add JSTypedArray length in post-mortem metadata (Julien Gilli) [#3031](https://github.com/nodejs/node/pull/3031)

### Known issues

See https://github.com/nodejs/node/labels/confirmed-bug for complete and current list of known issues.

- Some problems with unreferenced timers running during `beforeExit` are still to be resolved. See [#1264](https://github.com/nodejs/node/issues/1264).
- Surrogate pair in REPL can freeze terminal. [#690](https://github.com/nodejs/node/issues/690)
- Calling `dns.setServers()` while a DNS query is in progress can cause the process to crash on a failed assertion. [#894](https://github.com/nodejs/node/issues/894)
- `url.resolve` may transfer the auth portion of the url when resolving between two full hosts, see [#1435](https://github.com/nodejs/node/issues/1435).

### Commits

- [[`39b8730e8b`](https://github.com/nodejs/node/commit/39b8730e8b)] - **async_wrap**: ensure all objects have internal field (Trevor Norris) [#3139](https://github.com/nodejs/node/pull/3139)
- [[`99e66074d7`](https://github.com/nodejs/node/commit/99e66074d7)] - **async_wrap**: update providers and add test (Trevor Norris) [#3139](https://github.com/nodejs/node/pull/3139)
- [[`7a58157d4e`](https://github.com/nodejs/node/commit/7a58157d4e)] - **benchmark**: update comment in common.js (Minwoo Jung) [#2399](https://github.com/nodejs/node/pull/2399)
- [[`9e9bfa4dc0`](https://github.com/nodejs/node/commit/9e9bfa4dc0)] - **build**: iojs -> nodejs of release-urlbase (P.S.V.R) [#3015](https://github.com/nodejs/node/pull/3015)
- [[`8335ec7191`](https://github.com/nodejs/node/commit/8335ec7191)] - **build**: fix some typos inside the configure script (P.S.V.R) [#3016](https://github.com/nodejs/node/pull/3016)
- [[`d6ac547d5d`](https://github.com/nodejs/node/commit/d6ac547d5d)] - **build,win**: fix node.exe resource version (João Reis) [#3053](https://github.com/nodejs/node/pull/3053)
- [[`798dad24f4`](https://github.com/nodejs/node/commit/798dad24f4)] - **child_process**: `null` channel handle on close (Fedor Indutny) [#3041](https://github.com/nodejs/node/pull/3041)
- [[`e5615854ea`](https://github.com/nodejs/node/commit/e5615854ea)] - **contextify**: use CHECK instead of `if` (Oguz Bastemur) [#3125](https://github.com/nodejs/node/pull/3125)
- [[`f055a66a38`](https://github.com/nodejs/node/commit/f055a66a38)] - **crypto**: enable FIPS only when configured with it (Fedor Indutny) [#3153](https://github.com/nodejs/node/pull/3153)
- [[`4c8d96bc30`](https://github.com/nodejs/node/commit/4c8d96bc30)] - **crypto**: add more keylen sanity checks in pbkdf2 (Johann) [#3029](https://github.com/nodejs/node/pull/3029)
- [[`4c5940776c`](https://github.com/nodejs/node/commit/4c5940776c)] - **deps**: upgrade libuv to 1.7.5 (Saúl Ibarra Corretgé) [#3010](https://github.com/nodejs/node/pull/3010)
- [[`5a9e795577`](https://github.com/nodejs/node/commit/5a9e795577)] - **deps**: upgrade V8 to ********** (Ali Ijaz Sheikh) [#3117](https://github.com/nodejs/node/pull/3117)
- [[`925b29f959`](https://github.com/nodejs/node/commit/925b29f959)] - **deps**: backport f782159 from v8's upstream (Ben Noordhuis) [#3130](https://github.com/nodejs/node/pull/3130)
- [[`039f73fa83`](https://github.com/nodejs/node/commit/039f73fa83)] - **deps**: remove and gitignore .bin directory (Ben Noordhuis) [#3004](https://github.com/nodejs/node/pull/3004)
- [[`5fbb24812d`](https://github.com/nodejs/node/commit/5fbb24812d)] - **deps**: backport c281c15 from V8's upstream (Julien Gilli) [#3031](https://github.com/nodejs/node/pull/3031)
- [[`6ee5d0f69f`](https://github.com/nodejs/node/commit/6ee5d0f69f)] - **dns**: add missing exports.BADNAME (Roman Reiss) [#3051](https://github.com/nodejs/node/pull/3051)
- [[`f92aee7170`](https://github.com/nodejs/node/commit/f92aee7170)] - **doc**: fix outdated 'try/catch' statement in sync (Minwoo Jung) [#3087](https://github.com/nodejs/node/pull/3087)
- [[`c7161f39e8`](https://github.com/nodejs/node/commit/c7161f39e8)] - **doc**: add TSC meeting minutes 2015-09-16 (Rod Vagg) [#3023](https://github.com/nodejs/node/pull/3023)
- [[`928166c4a8`](https://github.com/nodejs/node/commit/928166c4a8)] - **doc**: copyedit fs.watch() information (Rich Trott) [#3097](https://github.com/nodejs/node/pull/3097)
- [[`75d5dcea76`](https://github.com/nodejs/node/commit/75d5dcea76)] - **doc**: jenkins-iojs.nodesource.com -> ci.nodejs.org (Michał Gołębiowski) [#2886](https://github.com/nodejs/node/pull/2886)
- [[`5c3f50b21d`](https://github.com/nodejs/node/commit/5c3f50b21d)] - **doc**: rearrange execSync and execFileSync (Laurent Fortin) [#2940](https://github.com/nodejs/node/pull/2940)
- [[`4fc33ac11a`](https://github.com/nodejs/node/commit/4fc33ac11a)] - **doc**: make execFileSync in line with execFile (Laurent Fortin) [#2940](https://github.com/nodejs/node/pull/2940)
- [[`a366e84b17`](https://github.com/nodejs/node/commit/a366e84b17)] - **doc**: fix typos in cluster & errors (reggi) [#3011](https://github.com/nodejs/node/pull/3011)
- [[`52031e1bf1`](https://github.com/nodejs/node/commit/52031e1bf1)] - **doc**: switch LICENSE from closure-linter to eslint (P.S.V.R) [#3018](https://github.com/nodejs/node/pull/3018)
- [[`b28f6a53bc`](https://github.com/nodejs/node/commit/b28f6a53bc)] - **docs**: Clarify assert.doesNotThrow behavior (Fabio Oliveira) [#2807](https://github.com/nodejs/node/pull/2807)
- [[`99943e189d`](https://github.com/nodejs/node/commit/99943e189d)] - **http**: fix out-of-order 'finish' bug in pipelining (Fedor Indutny) [#3128](https://github.com/nodejs/node/pull/3128)
- [[`fb7a491d1c`](https://github.com/nodejs/node/commit/fb7a491d1c)] - **http_server**: pause socket properly (Fedor Indutny) [#3128](https://github.com/nodejs/node/pull/3128)
- [[`a0b35bfcf3`](https://github.com/nodejs/node/commit/a0b35bfcf3)] - **i18n**: add caller to removal list for bidi in ICU55 (Michael Dawson) [#3115](https://github.com/nodejs/node/pull/3115)
- [[`ac2bce0b0c`](https://github.com/nodejs/node/commit/ac2bce0b0c)] - **path**: improve posixSplitPath performance (Evan Lucas) [#3034](https://github.com/nodejs/node/pull/3034)
- [[`37cdeafa2f`](https://github.com/nodejs/node/commit/37cdeafa2f)] - **smalloc**: remove module (Brendan Ashworth) [#3099](https://github.com/nodejs/node/pull/3099)
- [[`5ec5d0aa8b`](https://github.com/nodejs/node/commit/5ec5d0aa8b)] - **src**: internalize binding function property names (Ben Noordhuis) [#3060](https://github.com/nodejs/node/pull/3060)
- [[`c8175fc2af`](https://github.com/nodejs/node/commit/c8175fc2af)] - **src**: internalize per-isolate string properties (Ben Noordhuis) [#3060](https://github.com/nodejs/node/pull/3060)
- [[`9a593abc47`](https://github.com/nodejs/node/commit/9a593abc47)] - **src**: include signal.h in util.h (Cheng Zhao) [#3058](https://github.com/nodejs/node/pull/3058)
- [[`fde0c6f321`](https://github.com/nodejs/node/commit/fde0c6f321)] - **src**: fix function and variable names in comments (Sakthipriyan Vairamani) [#3039](https://github.com/nodejs/node/pull/3039)
- [[`1cc7b41ba4`](https://github.com/nodejs/node/commit/1cc7b41ba4)] - **stream_wrap**: support empty `TryWrite`s (Fedor Indutny) [#3128](https://github.com/nodejs/node/pull/3128)
- [[`9faf4c6fcf`](https://github.com/nodejs/node/commit/9faf4c6fcf)] - **test**: load common.js to test for global leaks (Rich Trott) [#3095](https://github.com/nodejs/node/pull/3095)
- [[`0858c86374`](https://github.com/nodejs/node/commit/0858c86374)] - **test**: fix invalid variable name (Sakthipriyan Vairamani) [#3150](https://github.com/nodejs/node/pull/3150)
- [[`1167171004`](https://github.com/nodejs/node/commit/1167171004)] - **test**: change calls to deprecated util.print() (Rich Trott) [#3083](https://github.com/nodejs/node/pull/3083)
- [[`5ada45bf28`](https://github.com/nodejs/node/commit/5ada45bf28)] - **test**: replace deprecated util.debug() calls (Rich Trott) [#3082](https://github.com/nodejs/node/pull/3082)
- [[`d8ab4e185d`](https://github.com/nodejs/node/commit/d8ab4e185d)] - **util**: optimize promise introspection (Ben Noordhuis) [#3130](https://github.com/nodejs/node/pull/3130)

Windows 32-bit Installer: https://nodejs.org/dist/v4.1.2/node-v4.1.2-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v4.1.2/node-v4.1.2-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v4.1.2/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v4.1.2/win-x64/node.exe \
Mac OS X 64-bit Installer: https://nodejs.org/dist/v4.1.2/node-v4.1.2.pkg \
Mac OS X 64-bit Binary: https://nodejs.org/dist/v4.1.2/node-v4.1.2-darwin-x64.tar.gz \
Linux 32-bit Binary: https://nodejs.org/dist/v4.1.2/node-v4.1.2-linux-x86.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v4.1.2/node-v4.1.2-linux-x64.tar.gz \
SmartOS 32-bit Binary: https://nodejs.org/dist/v4.1.2/node-v4.1.2-sunos-x86.tar.gz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v4.1.2/node-v4.1.2-sunos-x64.tar.gz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v4.1.2/node-v4.1.2-linux-armv6l.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v4.1.2/node-v4.1.2-linux-armv7l.tar.gz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v4.1.2/node-v4.1.2-linux-arm64.tar.gz \
Source Code: https://nodejs.org/dist/v4.1.2/node-v4.1.2.tar.gz \
Other release files: https://nodejs.org/dist/v4.1.2/ \
Documentation: https://nodejs.org/docs/v4.1.2/api/

Shasums (GPG signing hash: SHA512, file hash: SHA256):

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA1

058a5b07c9bda34074f4acad75ce03319402592a0482221f574e2fc4aed60131  node-v4.1.2-darwin-x64.tar.gz
ea5495dab4139c291ecec2cfd618adea14e7df6794ce60873db3783d5897f7fd  node-v4.1.2-darwin-x64.tar.xz
e8e17c8dc5afbf42d9533b37d32d7b44f105c425f13be4840c9e20cfcd251e78  node-v4.1.2-headers.tar.gz
33651643226584cc83753633a61ad83e02ba399537e428468b793e06f1f3a188  node-v4.1.2-headers.tar.xz
ae74c245b9592d52f8632a249a0bdd2eb664dcf7aaf5089d061f9c5b051f101a  node-v4.1.2-linux-arm64.tar.gz
5a805fb6e3f3650c2048ac4eafb7654df12b34f1bcc94bd58ad93fc015ded9da  node-v4.1.2-linux-arm64.tar.xz
834b36fa7e397f27f63f8fd56b77c15de4d9297c23f70679d0ceabd83662d950  node-v4.1.2-linux-armv6l.tar.gz
32f0958afef2ebeb390a10e9e5e12a99ddba41a8cd17ad26038a8b27fd2e26d1  node-v4.1.2-linux-armv6l.tar.xz
a8e38b1fae35c9b13f0c8cc57991d20a0267603c820028290236508930a522b0  node-v4.1.2-linux-armv7l.tar.gz
d1fccdbedd27ab77aed20861f2dc0dcefb91512f198957ef9574bfe8d9329a0e  node-v4.1.2-linux-armv7l.tar.xz
c39aefac81a2a4b0ae12df495e7dcdf6a8b75cbfe3a6efb649c8a4daa3aebdb6  node-v4.1.2-linux-x64.tar.gz
b2d072cf2c87e368ae41931e8ef855d825d66a39f3092cd61e7e60123ad1b471  node-v4.1.2-linux-x64.tar.xz
843599c40d3aabb1f60e33144647e6ead92bcfcefa7efca8396651ab92b5f3b9  node-v4.1.2-linux-x86.tar.gz
4429573a7d26b4103c92f06a4a1384a0ad9476baffdfb081434f95248e1cfe41  node-v4.1.2-linux-x86.tar.xz
d1e6e59bea645b4de42ca0b45f302eea32b0af55bccd7fee8da112eaebf6f221  node-v4.1.2.pkg
5a783eb6b4feaee29f447fe6ea4bfd1b745bee2f00b48d5bbb78f6a2a6488e59  node-v4.1.2-sunos-x64.tar.gz
9c6a3361deddf0712a87a751f679425451692453c407dba75618cf3c789ef637  node-v4.1.2-sunos-x64.tar.xz
33719d820fca0a0b44f55ecb9d9de0ce8359211a98d802a3191dfb4e0e5f2c5c  node-v4.1.2-sunos-x86.tar.gz
a09199e3383d855b84033c7441b19dae93172dbd1dd49505df8464f8aefa0005  node-v4.1.2-sunos-x86.tar.xz
febec3703a3fea9b64c46100a7b6a4408250027aa8259a2a815fd2275d502c49  node-v4.1.2.tar.gz
443c8251e812b1f2c4b3e9152a47df23c55567ade739e017e2c0ca0869b71e74  node-v4.1.2.tar.xz
9dce96b418ee81f4552601e936312a503d8f17a6ef34936fb562f6653a933071  node-v4.1.2-x64.msi
18f003ffb82b832c39eb94d5e167c4b0e965e4a28fefda909871375b43a6d94b  node-v4.1.2-x86.msi
f80b66e561125ec54f3ac3ad4098847581493ae1be850eceadc3255b5f5ed843  win-x64/node.exe
64f2738aef37a64356c290dd23b72a4171eafb9bec2c2f57208fa7638a06120b  win-x64/node.lib
e1fddb067d39edcbe187b3dfd276bb89cb5282d21f2eda7331b46005561ffa0f  win-x86/node.exe
80ba09d65813acebf005102ed5cd52a020ec0fbdef09928f3a3667c25cdfab47  win-x86/node.lib
-----BEGIN PGP SIGNATURE-----
Version: GnuPG v1

iQEcBAEBAgAGBQJWEu1KAAoJEMJzeS99g1RdSpsIALZ4L9a6jqUmpfig5tNGd8++
IlzCDYIqKkrN570V12rMpmFLSRKnE8kl08t0fcy4XbLCBpBYVoIlF5aztUHEjgSz
O2NijWuvygN2rpQWNMKoa0/a3enN5WeduRTm7/0pJvThy1k79tYNA2ti/7aMt554
zlnXxTWYoimz/trfl9ZSBOGKY3Qv8EecKhMw4D2PuTXrUYsOR1GdJikj4nwVgGpo
X+T+h2/yoCcxgCVcruckcmJOYMqKRuu+p5EvtRxycgC5ja7/2XrM/ywgJ33VcJXL
ETnnmI3fSDhXKo9JdAA4mtHMLeIBHUjMu253Dyq1NmbUrEcONF6TBMhuPTM4PwI=
=SCKn
-----END PGP SIGNATURE-----
```
