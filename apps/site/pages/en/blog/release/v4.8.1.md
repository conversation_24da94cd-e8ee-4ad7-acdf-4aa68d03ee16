---
date: '2017-03-21T21:32:25.011Z'
category: release
title: Node v4.8.1 (LTS)
layout: blog-post
author: <PERSON><PERSON>
---

### Notable Changes

- **buffer**: The performance of `.toJSON()` is now up to 2859% faster on average. (<PERSON>) [#10895](https://github.com/nodejs/node/pull/10895)
- **IPC**: Batched writes have been enabled for process IPC on platforms that support Unix Domain Sockets. (<PERSON><PERSON>) [#10677](https://github.com/nodejs/node/pull/10677)
  - Performance gains may be up to 40% for some workloads.
- **http**:
  - Control characters are now always rejected when using `http.request()`. (<PERSON>) [#8923](https://github.com/nodejs/node/pull/8923)
- **node**: Heap statistics now support values larger than 4GB. (<PERSON>) [#10186](https://github.com/nodejs/node/pull/10186)

### Commits

- [[`77f23ec5af`](https://github.com/nodejs/node/commit/77f23ec5af)] - **assert**: unlock the assert API (<PERSON>) [#11304](https://github.com/nodejs/node/pull/11304)
- [[`090037a41a`](https://github.com/nodejs/node/commit/090037a41a)] - **assert**: remove unneeded condition (Rich Trott) [#11314](https://github.com/nodejs/node/pull/11314)
- [[`75af859af7`](https://github.com/nodejs/node/commit/75af859af7)] - **assert**: apply minor refactoring (Rich Trott) [#11511](https://github.com/nodejs/node/pull/11511)
- [[`994f562858`](https://github.com/nodejs/node/commit/994f562858)] - **assert**: update comments (Kai Cataldo) [#10579](https://github.com/nodejs/node/pull/10579)
- [[`14e57c1102`](https://github.com/nodejs/node/commit/14e57c1102)] - **benchmark**: add more thorough timers benchmarks (Jeremiah Senkpiel) [#10925](https://github.com/nodejs/node/pull/10925)
- [[`850f85d96e`](https://github.com/nodejs/node/commit/850f85d96e)] - **benchmark**: add benchmark for object properties (Michaël Zasso) [#10949](https://github.com/nodejs/node/pull/10949)
- [[`626875f2e4`](https://github.com/nodejs/node/commit/626875f2e4)] - **benchmark**: don't lint autogenerated modules (Brian White) [#10756](https://github.com/nodejs/node/pull/10756)
- [[`9da6ebd73f`](https://github.com/nodejs/node/commit/9da6ebd73f)] - **benchmark**: add dgram bind(+/- params) benchmark (Vse Mozhet Byt) [#11313](https://github.com/nodejs/node/pull/11313)
- [[`a597c11ba4`](https://github.com/nodejs/node/commit/a597c11ba4)] - **benchmark**: improve readability of net benchmarks (Brian White) [#10446](https://github.com/nodejs/node/pull/10446)
- [[`22c25dee92`](https://github.com/nodejs/node/commit/22c25dee92)] - **buffer**: improve toJSON() performance (Brian White) [#10895](https://github.com/nodejs/node/pull/10895)
- [[`af3c21197d`](https://github.com/nodejs/node/commit/af3c21197d)] - **build**: move source files from headers section (Daniel Bevenius) [#10850](https://github.com/nodejs/node/pull/10850)
- [[`4bb61553f0`](https://github.com/nodejs/node/commit/4bb61553f0)] - **build**: disable C4267 conversion compiler warning (Ben Noordhuis) [#11205](https://github.com/nodejs/node/pull/11205)
- [[`6a45ac0ea9`](https://github.com/nodejs/node/commit/6a45ac0ea9)] - **build**: fix newlines in addon build output (Brian White) [#11466](https://github.com/nodejs/node/pull/11466)
- [[`bfc553d55d`](https://github.com/nodejs/node/commit/bfc553d55d)] - **build**: fail on CI if leftover processes (Rich Trott) [#11269](https://github.com/nodejs/node/pull/11269)
- [[`094bfe66aa`](https://github.com/nodejs/node/commit/094bfe66aa)] - **build**: fix node_g target (Daniel Bevenius) [#10153](https://github.com/nodejs/node/pull/10153)
- [[`87db4f7225`](https://github.com/nodejs/node/commit/87db4f7225)] - **build**: Don't regenerate node symlink (sxa555) [#9827](https://github.com/nodejs/node/pull/9827)
- [[`e0dc0ceb37`](https://github.com/nodejs/node/commit/e0dc0ceb37)] - **build**: don't squash signal handlers with --shared (Stewart X Addison) [#10539](https://github.com/nodejs/node/pull/10539)
- [[`4676eec382`](https://github.com/nodejs/node/commit/4676eec382)] - **child_process**: remove empty if condition (cjihrig) [#11427](https://github.com/nodejs/node/pull/11427)
- [[`2b867d2ae5`](https://github.com/nodejs/node/commit/2b867d2ae5)] - **child_process**: refactor internal/child_process.js (Arseniy Maximov) [#11366](https://github.com/nodejs/node/pull/11366)
- [[`c9a92ff494`](https://github.com/nodejs/node/commit/c9a92ff494)] - **crypto**: return the retval of HMAC_Update (Travis Meisenheimer) [#10891](https://github.com/nodejs/node/pull/10891)
- [[`9c53e402d7`](https://github.com/nodejs/node/commit/9c53e402d7)] - **crypto**: freelist_max_len is gone in OpenSSL 1.1.0 (Adam Langley) [#10859](https://github.com/nodejs/node/pull/10859)
- [[`c6f6b029a1`](https://github.com/nodejs/node/commit/c6f6b029a1)] - **crypto**: add cert check issued by StartCom/WoSign (Shigeki Ohtsu) [#9469](https://github.com/nodejs/node/pull/9469)
- [[`c56719f47a`](https://github.com/nodejs/node/commit/c56719f47a)] - **crypto**: Remove expired certs from CNNIC whitelist (Shigeki Ohtsu) [#9469](https://github.com/nodejs/node/pull/9469)
- [[`b48f6ffc63`](https://github.com/nodejs/node/commit/b48f6ffc63)] - **crypto**: use CHECK_NE instead of ABORT or abort (Sam Roberts) [#10413](https://github.com/nodejs/node/pull/10413)
- [[`35a660ee70`](https://github.com/nodejs/node/commit/35a660ee70)] - **crypto**: fix handling of root_cert_store. (Adam Langley) [#9409](https://github.com/nodejs/node/pull/9409)
- [[`3516f35b77`](https://github.com/nodejs/node/commit/3516f35b77)] - **deps**: backport 7c3748a from upstream V8 (Cristian Cavalli) [#10873](https://github.com/nodejs/node/pull/10873)
- [[`f9e121ead8`](https://github.com/nodejs/node/commit/f9e121ead8)] - **dgram**: fix possibly deoptimizing use of arguments (Vse Mozhet Byt)
- [[`fc2bb2c8ef`](https://github.com/nodejs/node/commit/fc2bb2c8ef)] - **doc**: remove Chris Dickinson from active releasers (Ben Noordhuis) [#11011](https://github.com/nodejs/node/pull/11011)
- [[`725a89606b`](https://github.com/nodejs/node/commit/725a89606b)] - **doc**: remove duplicate properties bullet in readme (Javis Sullivan) [#10741](https://github.com/nodejs/node/pull/10741)
- [[`db03294c41`](https://github.com/nodejs/node/commit/db03294c41)] - **doc**: fix typo in http.md (Peter Mescalchin) [#10975](https://github.com/nodejs/node/pull/10975)
- [[`15188900b8`](https://github.com/nodejs/node/commit/15188900b8)] - **doc**: add who to CC list for dgram (cjihrig) [#11035](https://github.com/nodejs/node/pull/11035)
- [[`a0742902bd`](https://github.com/nodejs/node/commit/a0742902bd)] - **doc**: correct and complete dgram's Socket.bind docs (Alex Jordan) [#11025](https://github.com/nodejs/node/pull/11025)
- [[`f464dd837f`](https://github.com/nodejs/node/commit/f464dd837f)] - **doc**: edit CONTRIBUTING.md for clarity (Rich Trott) [#11045](https://github.com/nodejs/node/pull/11045)
- [[`07dfed8f45`](https://github.com/nodejs/node/commit/07dfed8f45)] - **doc**: fix confusing example in dns.md (Vse Mozhet Byt) [#11022](https://github.com/nodejs/node/pull/11022)
- [[`d55d760086`](https://github.com/nodejs/node/commit/d55d760086)] - **doc**: add personal pronouns option (Rich Trott) [#11089](https://github.com/nodejs/node/pull/11089)
- [[`b86843a463`](https://github.com/nodejs/node/commit/b86843a463)] - **doc**: clarify msg when doc/api/cli.md not updated (Stewart X Addison) [#10872](https://github.com/nodejs/node/pull/10872)
- [[`c2d70908e6`](https://github.com/nodejs/node/commit/c2d70908e6)] - **doc**: edit stability text for clarity and style (Rich Trott) [#11112](https://github.com/nodejs/node/pull/11112)
- [[`115448ec94`](https://github.com/nodejs/node/commit/115448ec94)] - **doc**: remove assertions about assert (Rich Trott) [#11113](https://github.com/nodejs/node/pull/11113)
- [[`e90317d739`](https://github.com/nodejs/node/commit/e90317d739)] - **doc**: fix "initial delay" link in http.md (Timo Tijhof) [#11108](https://github.com/nodejs/node/pull/11108)
- [[`788d736ab6`](https://github.com/nodejs/node/commit/788d736ab6)] - **doc**: typographical fixes in COLLABORATOR_GUIDE.md (Anna Henningsen) [#11163](https://github.com/nodejs/node/pull/11163)
- [[`2016aa4e07`](https://github.com/nodejs/node/commit/2016aa4e07)] - **doc**: add not-an-aardvark as ESLint contact (Rich Trott) [#11169](https://github.com/nodejs/node/pull/11169)
- [[`2b6ee39264`](https://github.com/nodejs/node/commit/2b6ee39264)] - **doc**: improve testing guide (Joyee Cheung) [#11150](https://github.com/nodejs/node/pull/11150)
- [[`aae768c599`](https://github.com/nodejs/node/commit/aae768c599)] - **doc**: remove extraneous paragraph from assert doc (Rich Trott) [#11174](https://github.com/nodejs/node/pull/11174)
- [[`ca4b2f6154`](https://github.com/nodejs/node/commit/ca4b2f6154)] - **doc**: fix typo in dgram doc (Rich Trott) [#11186](https://github.com/nodejs/node/pull/11186)
- [[`bb1e97c31a`](https://github.com/nodejs/node/commit/bb1e97c31a)] - **doc**: add and fix System Error properties (Daiki Arai) [#10986](https://github.com/nodejs/node/pull/10986)
- [[`e1e02efac5`](https://github.com/nodejs/node/commit/e1e02efac5)] - **doc**: clarify the behavior of Buffer.byteLength (Nikolai Vavilov) [#11238](https://github.com/nodejs/node/pull/11238)
- [[`30d9202f54`](https://github.com/nodejs/node/commit/30d9202f54)] - **doc**: improve consistency in documentation titles (Vse Mozhet Byt) [#11230](https://github.com/nodejs/node/pull/11230)
- [[`10afa8befc`](https://github.com/nodejs/node/commit/10afa8befc)] - **doc**: drop "and io.js" from release section (Ben Noordhuis) [#11054](https://github.com/nodejs/node/pull/11054)
- [[`6f1db35e27`](https://github.com/nodejs/node/commit/6f1db35e27)] - **doc**: update email and add personal pronoun (JungMinu) [#11318](https://github.com/nodejs/node/pull/11318)
- [[`61ac3346ba`](https://github.com/nodejs/node/commit/61ac3346ba)] - **doc**: update code examples in domain.md (Vse Mozhet Byt) [#11110](https://github.com/nodejs/node/pull/11110)
- [[`0c9ea4fe8b`](https://github.com/nodejs/node/commit/0c9ea4fe8b)] - **doc**: dns examples implied string args were arrays (Sam Roberts) [#11350](https://github.com/nodejs/node/pull/11350)
- [[`485ec6c180`](https://github.com/nodejs/node/commit/485ec6c180)] - **doc**: change STYLE-GUIDE to STYLE_GUIDE (Dean Coakley) [#11460](https://github.com/nodejs/node/pull/11460)
- [[`41bf266b0a`](https://github.com/nodejs/node/commit/41bf266b0a)] - **doc**: add STYLE_GUIDE (moved from nodejs/docs) (Gibson Fahnestock) [#11321](https://github.com/nodejs/node/pull/11321)
- [[`6abfcd560b`](https://github.com/nodejs/node/commit/6abfcd560b)] - **doc**: add comment for net.Server's error event (QianJin2013) [#11136](https://github.com/nodejs/node/pull/11136)
- [[`f4bc12dd11`](https://github.com/nodejs/node/commit/f4bc12dd11)] - **doc**: note message event listeners ref IPC channels (Diego Rodríguez Baquero) [#11494](https://github.com/nodejs/node/pull/11494)
- [[`09c9105a79`](https://github.com/nodejs/node/commit/09c9105a79)] - **doc**: argument types for assert methods (Amelia Clarke) [#11548](https://github.com/nodejs/node/pull/11548)
- [[`d622b67302`](https://github.com/nodejs/node/commit/d622b67302)] - **doc**: document clientRequest.aborted (Zach Bjornson) [#11544](https://github.com/nodejs/node/pull/11544)
- [[`d0dbf12884`](https://github.com/nodejs/node/commit/d0dbf12884)] - **doc**: update TheAlphaNerd to MylesBorins (Myles Borins) [#10586](https://github.com/nodejs/node/pull/10586)
- [[`05273c5a4e`](https://github.com/nodejs/node/commit/05273c5a4e)] - **doc**: update AUTHORS list to fix name (Noah Rose Ledesma) [#10945](https://github.com/nodejs/node/pull/10945)
- [[`79f700c891`](https://github.com/nodejs/node/commit/79f700c891)] - **doc**: add TimothyGu to collaborators (Timothy Gu) [#10954](https://github.com/nodejs/node/pull/10954)
- [[`e656a4244a`](https://github.com/nodejs/node/commit/e656a4244a)] - **doc**: add edsadr to collaborators (Adrian Estrada) [#10883](https://github.com/nodejs/node/pull/10883)
- [[`6d0e1621e5`](https://github.com/nodejs/node/commit/6d0e1621e5)] - **doc**: clarifying variables in fs.write() (Jessica Quynh Tran) [#9792](https://github.com/nodejs/node/pull/9792)
- [[`7287dddd69`](https://github.com/nodejs/node/commit/7287dddd69)] - **doc**: add links for zlib convenience methods (Anna Henningsen) [#10829](https://github.com/nodejs/node/pull/10829)
- [[`b10842ac77`](https://github.com/nodejs/node/commit/b10842ac77)] - **doc**: sort require statements in tests (Sam Roberts) [#10616](https://github.com/nodejs/node/pull/10616)
- [[`8f0e31b2d9`](https://github.com/nodejs/node/commit/8f0e31b2d9)] - **doc**: add test naming information to guide (Rich Trott) [#10584](https://github.com/nodejs/node/pull/10584)
- [[`56b779db93`](https://github.com/nodejs/node/commit/56b779db93)] - **doc**: "s/git apply/git am -3" in V8 guide (Myles Borins) [#10665](https://github.com/nodejs/node/pull/10665)
- [[`3be7a7adb5`](https://github.com/nodejs/node/commit/3be7a7adb5)] - **doc**: update LTS info for current releases (Evan Lucas) [#10720](https://github.com/nodejs/node/pull/10720)
- [[`530adfdb2a`](https://github.com/nodejs/node/commit/530adfdb2a)] - **doc**: improve rinfo object documentation (Matt Crummey) [#10050](https://github.com/nodejs/node/pull/10050)
- [[`48b5097ea8`](https://github.com/nodejs/node/commit/48b5097ea8)] - **http**: make request.abort() destroy the socket (Luigi Pinca) [#10818](https://github.com/nodejs/node/pull/10818)
- [[`15231aa6e5`](https://github.com/nodejs/node/commit/15231aa6e5)] - **http**: reject control characters in http.request() (Ben Noordhuis) [#8923](https://github.com/nodejs/node/pull/8923)
- [[`fc2cd63998`](https://github.com/nodejs/node/commit/fc2cd63998)] - **lib,src**: support values \> 4GB in heap statistics (Ben Noordhuis) [#10186](https://github.com/nodejs/node/pull/10186)
- [[`533d2bf0a9`](https://github.com/nodejs/node/commit/533d2bf0a9)] - **meta**: add explicit deprecation and semver-major policy (James M Snell) [#7964](https://github.com/nodejs/node/pull/7964)
- [[`923309adef`](https://github.com/nodejs/node/commit/923309adef)] - **meta**: remove Chris Dickinson from CTC (Chris Dickinson) [#11267](https://github.com/nodejs/node/pull/11267)
- [[`342c3e2bb4`](https://github.com/nodejs/node/commit/342c3e2bb4)] - **meta**: adding Italo A. Casas PGP Fingerprint (Italo A. Casas) [#11202](https://github.com/nodejs/node/pull/11202)
- [[`434b00be8a`](https://github.com/nodejs/node/commit/434b00be8a)] - **meta**: decharter the http working group (James M Snell) [#10604](https://github.com/nodejs/node/pull/10604)
- [[`a7df345921`](https://github.com/nodejs/node/commit/a7df345921)] - **net**: prefer === to == (Arseniy Maximov) [#11513](https://github.com/nodejs/node/pull/11513)
- [[`396688f075`](https://github.com/nodejs/node/commit/396688f075)] - **readline**: refactor construct Interface (Jackson Tian) [#4740](https://github.com/nodejs/node/pull/4740)
- [[`a40f8429e6`](https://github.com/nodejs/node/commit/a40f8429e6)] - **readline**: update 6 comparions to strict (Umair Ishaq) [#11078](https://github.com/nodejs/node/pull/11078)
- [[`90d8e118fb`](https://github.com/nodejs/node/commit/90d8e118fb)] - **src**: add a missing space in node_os.cc (Alexey Orlenko) [#10931](https://github.com/nodejs/node/pull/10931)
- [[`279cb09cc3`](https://github.com/nodejs/node/commit/279cb09cc3)] - **src**: enable writev for pipe handles on Unix (Alexey Orlenko) [#10677](https://github.com/nodejs/node/pull/10677)
- [[`a557d6ce1d`](https://github.com/nodejs/node/commit/a557d6ce1d)] - **src**: unconsume stream fix in internal http impl (Roee Kasher) [#11015](https://github.com/nodejs/node/pull/11015)
- [[`c4e1af712e`](https://github.com/nodejs/node/commit/c4e1af712e)] - **src**: remove unused typedef (Ben Noordhuis) [#11322](https://github.com/nodejs/node/pull/11322)
- [[`da2adb7133`](https://github.com/nodejs/node/commit/da2adb7133)] - **src**: update http-parser link (Daniel Bevenius) [#11477](https://github.com/nodejs/node/pull/11477)
- [[`2f48001574`](https://github.com/nodejs/node/commit/2f48001574)] - **src**: use ABORT() macro instead of abort() (Evan Lucas) [#9613](https://github.com/nodejs/node/pull/9613)
- [[`a9eb093ce3`](https://github.com/nodejs/node/commit/a9eb093ce3)] - **src**: fix memory leak introduced in 34febfbf4 (Ben Noordhuis) [#9604](https://github.com/nodejs/node/pull/9604)
- [[`f854d8c789`](https://github.com/nodejs/node/commit/f854d8c789)] - **test**: increase setMulticastLoopback() coverage (cjihrig) [#11277](https://github.com/nodejs/node/pull/11277)
- [[`1df09f9d37`](https://github.com/nodejs/node/commit/1df09f9d37)] - **test**: add known_issues test for #10223 (AnnaMag) [#11024](https://github.com/nodejs/node/pull/11024)
- [[`be34b629de`](https://github.com/nodejs/node/commit/be34b629de)] - **test**: increase coverage for stream's duplex (abouthiroppy) [#10963](https://github.com/nodejs/node/pull/10963)
- [[`dc24127e5c`](https://github.com/nodejs/node/commit/dc24127e5c)] - **test**: allow for slow hosts in spawnSync() test (Rich Trott) [#10998](https://github.com/nodejs/node/pull/10998)
- [[`2f4b6bda97`](https://github.com/nodejs/node/commit/2f4b6bda97)] - **test**: expand test coverage of fs.js (Vinícius do Carmo) [#10947](https://github.com/nodejs/node/pull/10947)
- [[`3f6a2dbc2f`](https://github.com/nodejs/node/commit/3f6a2dbc2f)] - **test**: enhance test-timers (Rich Trott) [#10960](https://github.com/nodejs/node/pull/10960)
- [[`6ca9901d8b`](https://github.com/nodejs/node/commit/6ca9901d8b)] - **test**: add process.assert's test (abouthiroppy) [#10911](https://github.com/nodejs/node/pull/10911)
- [[`d8af5a7431`](https://github.com/nodejs/node/commit/d8af5a7431)] - **test**: improve code in test-crypto-verify (Adrian Estrada) [#10845](https://github.com/nodejs/node/pull/10845)
- [[`4d1f7b1df8`](https://github.com/nodejs/node/commit/4d1f7b1df8)] - **test**: add dgram.Socket.prototype.bind's test (abouthiroppy) [#10894](https://github.com/nodejs/node/pull/10894)
- [[`6c1d82c68a`](https://github.com/nodejs/node/commit/6c1d82c68a)] - **test**: improving coverage for dgram (abouthiroppy) [#10783](https://github.com/nodejs/node/pull/10783)
- [[`017afd48fd`](https://github.com/nodejs/node/commit/017afd48fd)] - **test**: improve code in test-console-instance (Adrian Estrada) [#10813](https://github.com/nodejs/node/pull/10813)
- [[`1b1ba741c3`](https://github.com/nodejs/node/commit/1b1ba741c3)] - **test**: improve code in test-domain-multi (Adrian Estrada) [#10798](https://github.com/nodejs/node/pull/10798)
- [[`ee27917a65`](https://github.com/nodejs/node/commit/ee27917a65)] - **test**: improve test-stream2-large-read-stall (stefan judis) [#10725](https://github.com/nodejs/node/pull/10725)
- [[`9ac2316595`](https://github.com/nodejs/node/commit/9ac2316595)] - **test**: improve code in test-http-host-headers (Adrian Estrada) [#10830](https://github.com/nodejs/node/pull/10830)
- [[`a9278a063f`](https://github.com/nodejs/node/commit/a9278a063f)] - **test**: refactor cluster-preload.js (abouthiroppy) [#10701](https://github.com/nodejs/node/pull/10701)
- [[`db60d92e15`](https://github.com/nodejs/node/commit/db60d92e15)] - **test**: test hmac binding robustness (Sam Roberts) [#10923](https://github.com/nodejs/node/pull/10923)
- [[`a1a850f066`](https://github.com/nodejs/node/commit/a1a850f066)] - **test**: don't connect to :: (use localhost instead) (Gibson Fahnestock)
- [[`b3a8e95af3`](https://github.com/nodejs/node/commit/b3a8e95af3)] - **test**: improve test-assert (richnologies) [#10916](https://github.com/nodejs/node/pull/10916)
- [[`56970efe51`](https://github.com/nodejs/node/commit/56970efe51)] - **test**: increase coverage for punycode's decode (abouthiroppy) [#10940](https://github.com/nodejs/node/pull/10940)
- [[`df69c2148a`](https://github.com/nodejs/node/commit/df69c2148a)] - **test**: check fd 0,1,2 are used, not access mode (John Barboza) [#10339](https://github.com/nodejs/node/pull/10339)
- [[`7bceb4fb48`](https://github.com/nodejs/node/commit/7bceb4fb48)] - **test**: add message verification on assert.throws (Travis Meisenheimer) [#10890](https://github.com/nodejs/node/pull/10890)
- [[`1c223ecc70`](https://github.com/nodejs/node/commit/1c223ecc70)] - **test**: add http-common's test (abouthiroppy) [#10832](https://github.com/nodejs/node/pull/10832)
- [[`89e9da6b6d`](https://github.com/nodejs/node/commit/89e9da6b6d)] - **test**: tests for \_readableStream.awaitDrain (Mark) [#8914](https://github.com/nodejs/node/pull/8914)
- [[`53b0f413cd`](https://github.com/nodejs/node/commit/53b0f413cd)] - **test**: improve the code in test-process-cpuUsage (Adrian Estrada) [#10714](https://github.com/nodejs/node/pull/10714)
- [[`b3d1700d1f`](https://github.com/nodejs/node/commit/b3d1700d1f)] - **test**: improve tests in pummel/test-exec (Chase Starr) [#10757](https://github.com/nodejs/node/pull/10757)
- [[`6e7dfb1f45`](https://github.com/nodejs/node/commit/6e7dfb1f45)] - **test**: fix temp-dir option in tools/test.py (Gibson Fahnestock) [#10723](https://github.com/nodejs/node/pull/10723)
- [[`9abde3ac6e`](https://github.com/nodejs/node/commit/9abde3ac6e)] - **test**: use realpath for NODE_TEST_DIR in common.js (Gibson Fahnestock) [#10723](https://github.com/nodejs/node/pull/10723)
- [[`f86c64a13a`](https://github.com/nodejs/node/commit/f86c64a13a)] - **test**: refactor the code of test-keep-alive.js (sivaprasanna) [#10684](https://github.com/nodejs/node/pull/10684)
- [[`4d51db87dc`](https://github.com/nodejs/node/commit/4d51db87dc)] - **test**: refactor test-doctool-html.js (abouthiroppy) [#10696](https://github.com/nodejs/node/pull/10696)
- [[`ab65429e44`](https://github.com/nodejs/node/commit/ab65429e44)] - **test**: refactor test-watch-file.js (sivaprasanna) [#10679](https://github.com/nodejs/node/pull/10679)
- [[`4453c0c1dc`](https://github.com/nodejs/node/commit/4453c0c1dc)] - **test**: refactor the code in test-child-process-spawn-loop.js (sivaprasanna) [#10605](https://github.com/nodejs/node/pull/10605)
- [[`42b86ea968`](https://github.com/nodejs/node/commit/42b86ea968)] - **test**: improve test-http-chunked-304 (Adrian Estrada) [#10462](https://github.com/nodejs/node/pull/10462)
- [[`1ae95e64ee`](https://github.com/nodejs/node/commit/1ae95e64ee)] - **test**: improve test-fs-readfile-zero-byte-liar (Adrian Estrada) [#10570](https://github.com/nodejs/node/pull/10570)
- [[`3f3c78d785`](https://github.com/nodejs/node/commit/3f3c78d785)] - **test**: refactor test-fs-utimes (Junshu Okamoto) [#9290](https://github.com/nodejs/node/pull/9290)
- [[`50a868b3f7`](https://github.com/nodejs/node/commit/50a868b3f7)] - **test**: require handler to be run in sigwinch test (Rich Trott) [#11068](https://github.com/nodejs/node/pull/11068)
- [[`c1f45ec2d0`](https://github.com/nodejs/node/commit/c1f45ec2d0)] - **test**: add 2nd argument to throws in test-assert (Marlena Compton) [#11061](https://github.com/nodejs/node/pull/11061)
- [[`f24aa7e071`](https://github.com/nodejs/node/commit/f24aa7e071)] - **test**: improve error messages in test-npm-install (Gonen Dukas) [#11027](https://github.com/nodejs/node/pull/11027)
- [[`1db89d4009`](https://github.com/nodejs/node/commit/1db89d4009)] - **test**: improve coverage on removeListeners functions (matsuda-koushi) [#11140](https://github.com/nodejs/node/pull/11140)
- [[`c532c16e53`](https://github.com/nodejs/node/commit/c532c16e53)] - **test**: increase specificity in dgram test (Rich Trott) [#11187](https://github.com/nodejs/node/pull/11187)
- [[`cb81ae8eea`](https://github.com/nodejs/node/commit/cb81ae8eea)] - **test**: add vm module edge cases (Franziska Hinkelmann) [#11265](https://github.com/nodejs/node/pull/11265)
- [[`8629c956c3`](https://github.com/nodejs/node/commit/8629c956c3)] - **test**: improve punycode test coverage (Sebastian Van Sande) [#11144](https://github.com/nodejs/node/pull/11144)
- [[`caf1ba15f9`](https://github.com/nodejs/node/commit/caf1ba15f9)] - **test**: add coverage for dgram \_createSocketHandle() (cjihrig) [#11291](https://github.com/nodejs/node/pull/11291)
- [[`d729e52ef3`](https://github.com/nodejs/node/commit/d729e52ef3)] - **test**: improve crypto coverage (Akito Ito) [#11280](https://github.com/nodejs/node/pull/11280)
- [[`d1a8588cab`](https://github.com/nodejs/node/commit/d1a8588cab)] - **test**: improve message in net-connect-local-error (Rich Trott) [#11393](https://github.com/nodejs/node/pull/11393)
- [[`f2fb4143b4`](https://github.com/nodejs/node/commit/f2fb4143b4)] - **test**: refactor test-dgram-membership (Rich Trott) [#11388](https://github.com/nodejs/node/pull/11388)
- [[`bf4703d66f`](https://github.com/nodejs/node/commit/bf4703d66f)] - **test**: remove unused args and comparison fix (Alexander) [#11396](https://github.com/nodejs/node/pull/11396)
- [[`28471c23ff`](https://github.com/nodejs/node/commit/28471c23ff)] - **test**: refactor test-http-response-splitting (Arseniy Maximov) [#11429](https://github.com/nodejs/node/pull/11429)
- [[`cd3e17e248`](https://github.com/nodejs/node/commit/cd3e17e248)] - **test**: improve coverage in test-crypto.dh (Eric Christie) [#11253](https://github.com/nodejs/node/pull/11253)
- [[`fa681ea55a`](https://github.com/nodejs/node/commit/fa681ea55a)] - **test**: add regex check to test-module-loading (Tarang Hirani) [#11413](https://github.com/nodejs/node/pull/11413)
- [[`f0eee61a93`](https://github.com/nodejs/node/commit/f0eee61a93)] - **test**: throw check in test-zlib-write-after-close (Jason Wilson) [#11482](https://github.com/nodejs/node/pull/11482)
- [[`f0c7c7fad4`](https://github.com/nodejs/node/commit/f0c7c7fad4)] - **test**: fix flaky test-vm-timeout-rethrow (Kunal Pathak) [#11530](https://github.com/nodejs/node/pull/11530)
- [[`53f2848dc8`](https://github.com/nodejs/node/commit/53f2848dc8)] - **test**: favor assertions over console logging (Rich Trott) [#11547](https://github.com/nodejs/node/pull/11547)
- [[`0109321fd8`](https://github.com/nodejs/node/commit/0109321fd8)] - **test**: refactor test-https-truncate (Rich Trott) [#10225](https://github.com/nodejs/node/pull/10225)
- [[`536733697c`](https://github.com/nodejs/node/commit/536733697c)] - **test**: simplify test-http-client-unescaped-path (Rod Vagg) [#9649](https://github.com/nodejs/node/pull/9649)
- [[`4ce9bfb4e7`](https://github.com/nodejs/node/commit/4ce9bfb4e7)] - **test**: exclude pseudo-tty test pertinent to #11541 (Gireesh Punathil) [#11602](https://github.com/nodejs/node/pull/11602)
- [[`53dd1a8539`](https://github.com/nodejs/node/commit/53dd1a8539)] - **tls**: do not crash on STARTTLS when OCSP requested (Fedor Indutny) [#10706](https://github.com/nodejs/node/pull/10706)
- [[`e607ff52fa`](https://github.com/nodejs/node/commit/e607ff52fa)] - **tools**: rename eslintrc to an undeprecated format (Sakthipriyan Vairamani) [#7699](https://github.com/nodejs/node/pull/7699)
- [[`6648b729b7`](https://github.com/nodejs/node/commit/6648b729b7)] - **tools**: add compile_commands.json gyp generator (Ben Noordhuis) [#7986](https://github.com/nodejs/node/pull/7986)
- [[`8f49962f47`](https://github.com/nodejs/node/commit/8f49962f47)] - **tools**: suggest python2 command in configure (Roman Reiss) [#11375](https://github.com/nodejs/node/pull/11375)
- [[`4b83a83c06`](https://github.com/nodejs/node/commit/4b83a83c06)] - **tools,doc**: add Google Analytics tracking. (Phillip Johnsen) [#6601](https://github.com/nodejs/node/pull/6601)
- [[`ef63af6006`](https://github.com/nodejs/node/commit/ef63af6006)] - **tty**: avoid oob warning in TTYWrap::GetWindowSize() (Dmitry Tsvettsikh) [#11454](https://github.com/nodejs/node/pull/11454)
- [[`2c84601062`](https://github.com/nodejs/node/commit/2c84601062)] - **util**: don't init Debug if it's not needed yet (Bryan English) [#8452](https://github.com/nodejs/node/pull/8452)

Windows 32-bit Installer: https://nodejs.org/dist/v4.8.1/node-v4.8.1-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v4.8.1/node-v4.8.1-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v4.8.1/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v4.8.1/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v4.8.1/node-v4.8.1.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v4.8.1/node-v4.8.1-darwin-x64.tar.gz \
Linux 32-bit Binary: https://nodejs.org/dist/v4.8.1/node-v4.8.1-linux-x86.tar.xz \
Linux 64-bit Binary: https://nodejs.org/dist/v4.8.1/node-v4.8.1-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v4.8.1/node-v4.8.1-linux-ppc64le.tar.xz \
Linux PPC BE 64-bit Binary: https://nodejs.org/dist/v4.8.1/node-v4.8.1-linux-ppc64.tar.xz \
SmartOS 32-bit Binary: https://nodejs.org/dist/v4.8.1/node-v4.8.1-sunos-x86.tar.xz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v4.8.1/node-v4.8.1-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v4.8.1/node-v4.8.1-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v4.8.1/node-v4.8.1-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v4.8.1/node-v4.8.1-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v4.8.1/node-v4.8.1.tar.gz \
Other release files: https://nodejs.org/dist/v4.8.1/ \
Documentation: https://nodejs.org/docs/v4.8.1/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

ec6a6c41d8001abf0a954bec4cec3b2b1e063b2b13af11b177c33f5c965d6b52  node-v4.8.1-darwin-x64.tar.gz
33bf7fd5df5faa292079c18d65ae72c442df8cb650aca80b98201cfbd4bd30d6  node-v4.8.1-darwin-x64.tar.xz
08204166f31e71dcd575c23ecb7b42f475065bc44822f02bd7293db8283dedc7  node-v4.8.1-headers.tar.gz
fe210afad84c7fa369d8a8178a2012796f44acc256e264ae4e6215313a1fe878  node-v4.8.1-headers.tar.xz
40a29ae4f59de7b195ba0cc0334c179f834968af0e7e57e7c685cd02d9cdb5be  node-v4.8.1-linux-arm64.tar.gz
3f3dad40de2c16ce373df7ae46a89d229fc191613ddb7fe22883583f4d5223b0  node-v4.8.1-linux-arm64.tar.xz
f2779ddcd4fbbb5ff43bc84c28dc9b0ad0398ac992329243d63df6df4fd8a7e4  node-v4.8.1-linux-armv6l.tar.gz
0ae8578a953d52d8d71423b7c7e44c5b99cce62828f8c75d5c862729c41f8ff9  node-v4.8.1-linux-armv6l.tar.xz
cdd193c2b4116966a75064e583f90cb8f4a28246e340c1c5541b9498019c5aed  node-v4.8.1-linux-armv7l.tar.gz
73fed31848ca6e9832c0172dfe2ef99d434695d8c3d139d3bf58ae79347ff31d  node-v4.8.1-linux-armv7l.tar.xz
4ff900003cb61995ff20cb7bd34773b4597a9166127071e914bd4cbb46977246  node-v4.8.1-linux-ppc64le.tar.gz
f343d6df7d83f5f10efe4cfe6da6764d09d13bf3971532be7c0e9b4e37972b3b  node-v4.8.1-linux-ppc64le.tar.xz
4c099039b574b0705be6eef4df9f6a2bd453a09b836350b3ed72d1574be8a685  node-v4.8.1-linux-ppc64.tar.gz
60be4c0e5e33f87849a212f021cfb6855e68aae6cbc84555b7eae7e38238785f  node-v4.8.1-linux-ppc64.tar.xz
8eed258da756c77618c03b780eb15743726aedd260b3696e98ced313c57155b9  node-v4.8.1-linux-x64.tar.gz
908375af21b27efa271b0c622fdbf9dce617b35969e69cc4f3c2e70cec2ed374  node-v4.8.1-linux-x64.tar.xz
771e13b17e3d4f5406dae9ff20d91b30357fac1f7a2344460f03af1b220cfc18  node-v4.8.1-linux-x86.tar.gz
a4723cdd00a34ffbf54827ee66f6c668caf1840aa2b32f20509ed59366e2f29c  node-v4.8.1-linux-x86.tar.xz
6dbfbce62f49da70d0da3486d3cdacf3334b28268174cee3196230a11a09b883  node-v4.8.1.pkg
12b3a1fd6725eb296ab2d083ee61b88f502337fcd199cf49a9febb06546a3990  node-v4.8.1-sunos-x64.tar.gz
3bc80207308fdfe4e0402671bb176378fb81bcafe7eb9cca00a6c30233493765  node-v4.8.1-sunos-x64.tar.xz
94106f95bb83bc0aed7de2520684f02900796206a3a25ffafb0ca4a51d590bb6  node-v4.8.1-sunos-x86.tar.gz
755275d62f67b49cf4780a3e32098637bd5fb0d40be1205083c9e1f8e56602b7  node-v4.8.1-sunos-x86.tar.xz
0cdc67fb2df091414447e2e1750b371be99a7b61c11ade0261ce5bab845e9dd1  node-v4.8.1.tar.gz
bae63f7fb43890047e3d9b0d5d51b839302890f12fec4af233030427afa78a4d  node-v4.8.1.tar.xz
bda930dba184fb69fbd94cd95e8911cc60a41ce87daff265b0e63bc3c818a2d2  node-v4.8.1-win-x64.7z
edb47c31de7891ddb58d5e1024e31c91b49b4f2226cf6c3e0c41e715ee6111e4  node-v4.8.1-win-x64.zip
43988e531fc6d77717903819f28884be88e17ab1d3cad074e2bdf93e8747864c  node-v4.8.1-win-x86.7z
d7659c2de8e9787bd234b644ceb006f7b131c56b480cc14c8061444b7104f074  node-v4.8.1-win-x86.zip
4f086a8c5338bd259ac1fe452c6b5ccbdc9f86df36ede9cbc9361bb651e69264  node-v4.8.1-x64.msi
872214e3b791e256bbdded8df114e3dae294c164ff0a42b578defd2a84b8f51f  node-v4.8.1-x86.msi
a37086eb408b2ab5c477a543f257ee10d2b7946b56e801e501529d7e15ca2b1e  win-x64/node.exe
8626f6758318e88e8a8641d4f7b75560fa207c35ea9d80e5e4f43066b5a7f714  win-x64/node.lib
d2da86f91b0a58b04df274df8084ad58c46b022658e80842bc4635dbbe21631c  win-x64/node_pdb.7z
e7d1211e89588cee4400c57d2525fb0342492f5b11f98080efb9a089a6655071  win-x64/node_pdb.zip
affdc9b673eacba18bb22d86480bd7a7a56ba8d144630fbd2f121b8b5c57ffd6  win-x86/node.exe
b72002fa8301f60d4b7a18f5b59b6bb7ec8eced7c59f7703a0cdb1c2a375dd8f  win-x86/node.lib
3591ce9f562fe05f2684e6ee9c9979779f3437b8cae68213ea5cc91e33f9ee8b  win-x86/node_pdb.7z
ff42a2d42eb7b292446285f98e493b70508c77b0bdc7a27725b37ef8a42dac9f  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQEcBAEBCAAGBQJY0ZhjAAoJEJM7AfQLXKlGMUsH/09r1GkLblWSljtjAj4XO7x1
1OFm+P8WmcttdmQAyy84iZDW2euUOYk8RnPYR3XD6raUEbTRLoq95WO1X9ZhVUhx
6QU483rkWZbSjiWK4DthonpxWLyqirI+MM3MoHForxXZiaPAWtM0cwrK9sunvjiP
CD5c5jEp+9uJE9dD+SqEWbsxYSxWY7GFFZNsZx+nYPH2BlQqayAzH4Zaefx7j0a7
VC6wkyjgbNQFKILDNA+IZFvLNzkn3iMGOgflyQV86UPgw5ybzE2n54YjdcmnXr2n
DFMk9aGaLdqxQDPihfKSUwToZe9gBwXZNM4toNHBRaNeW8jrtKx/QEX/mt6w/Qs=
=R1cp
-----END PGP SIGNATURE-----

```
