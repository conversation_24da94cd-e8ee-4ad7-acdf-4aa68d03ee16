---
date: '2022-02-01T13:33:41.310Z'
category: release
title: Node v14.19.0 (LTS)
layout: blog-post
author: <PERSON>
---

### Notable Changes

#### Corepack

Node.js now includes Corepack, a script that acts as a bridge between Node.js projects and the package managers they are intended to be used with during development.
In practical terms, **Corepack will let you use Yarn and pnpm without having to install them** - just like what currently happens with npm, which is shipped in Node.js by default.
Please head over to the [Corepack documentation page](https://nodejs.org/dist/latest-v14.x/docs/api/corepack.html) for more information on how to use it.

Contributed by <PERSON><PERSON> - [#39608](https://github.com/nodejs/node/pull/39608)

#### ICU updated

ICU has been updated to 70.1. This updates timezone database to 2021a3, including bringing forward the start for DST for Jordan from March to February.

Contributed by <PERSON><PERSON><PERSON> - [#40658](https://github.com/nodejs/node/pull/40658)

#### New option to disable loading of native addons

A new command line option `--no-addons` has been added to disallow loading of native addons.

Contributed by <PERSON> - [#39977](https://github.com/nodejs/node/pull/39977)

#### Updated Root Certificates

Root certificates have been updated to those from Mozilla's Network Security Services 3.71.

Contributed by Richard Lau - [#40280](https://github.com/nodejs/node/pull/40280)

#### Other Notable Changes

- \[[`0d448eaab5`](https://github.com/nodejs/node/commit/0d448eaab5)] - **(SEMVER-MINOR)** **crypto**: make FIPS related options always available (Vít Ondruch) [#36341](https://github.com/nodejs/node/pull/36341)
- \[[`004eafbebf`](https://github.com/nodejs/node/commit/004eafbebf)] - **(SEMVER-MINOR)** **lib**: add unsubscribe method to non-active DC channels (simon-id) [#40433](https://github.com/nodejs/node/pull/40433)
- \[[`625be7585d`](https://github.com/nodejs/node/commit/625be7585d)] - **(SEMVER-MINOR)** **lib**: add return value for DC channel.unsubscribe (simon-id) [#40433](https://github.com/nodejs/node/pull/40433)
- \[[`607bc74eae`](https://github.com/nodejs/node/commit/607bc74eae)] - **(SEMVER-MINOR)** **module**: support pattern trailers (Guy Bedford) [#39635](https://github.com/nodejs/node/pull/39635)
- \[[`f74fe2a59c`](https://github.com/nodejs/node/commit/f74fe2a59c)] - **(SEMVER-MINOR)** **src**: make napi_create_reference accept symbol (JckXia) [#39926](https://github.com/nodejs/node/pull/39926)

### Commits

- \[[`0231ffa501`](https://github.com/nodejs/node/commit/0231ffa501)] - **build**: add `--without-corepack` (Jonah Snider) [#41060](https://github.com/nodejs/node/pull/41060)
- \[[`5389b8ab05`](https://github.com/nodejs/node/commit/5389b8ab05)] - **crypto**: update root certificates (Richard Lau) [#40280](https://github.com/nodejs/node/pull/40280)
- \[[`0d448eaab5`](https://github.com/nodejs/node/commit/0d448eaab5)] - **(SEMVER-MINOR)** **crypto**: make FIPS related options always available (Vít Ondruch) [#36341](https://github.com/nodejs/node/pull/36341)
- \[[`cd20ecc7cb`](https://github.com/nodejs/node/commit/cd20ecc7cb)] - **deps**: upgrade Corepack to 0.10 (Maël Nison) [#40374](https://github.com/nodejs/node/pull/40374)
- \[[`737df75e17`](https://github.com/nodejs/node/commit/737df75e17)] - **(SEMVER-MINOR)** **deps**: add corepack (Maël Nison) [#39608](https://github.com/nodejs/node/pull/39608)
- \[[`b85aa5a143`](https://github.com/nodejs/node/commit/b85aa5a143)] - **deps**: upgrade npm to 6.14.16 (Ruy Adorno) [#41603](https://github.com/nodejs/node/pull/41603)
- \[[`2755d391a5`](https://github.com/nodejs/node/commit/2755d391a5)] - **deps**: update ICU to 70.1 (Michaël Zasso) [#40658](https://github.com/nodejs/node/pull/40658)
- \[[`3089326d89`](https://github.com/nodejs/node/commit/3089326d89)] - **deps**: update archs files for OpenSSL-1.1.1m (Richard Lau) [#41173](https://github.com/nodejs/node/pull/41173)
- \[[`59da7c12aa`](https://github.com/nodejs/node/commit/59da7c12aa)] - **deps**: upgrade openssl sources to 1.1.1m (Richard Lau) [#41173](https://github.com/nodejs/node/pull/41173)
- \[[`cede1f26f6`](https://github.com/nodejs/node/commit/cede1f26f6)] - **deps**: add -fno-strict-aliasing flag to libuv (Daniel Bevenius) [#40631](https://github.com/nodejs/node/pull/40631)
- \[[`4477da858f`](https://github.com/nodejs/node/commit/4477da858f)] - **doc**: fix corepack grammar for `--force` flag (Steven) [#40762](https://github.com/nodejs/node/pull/40762)
- \[[`5971d58600`](https://github.com/nodejs/node/commit/5971d58600)] - **doc**: add missing YAML tag in `esm.md` (Antoine du Hamel) [#41516](https://github.com/nodejs/node/pull/41516)
- \[[`e903798ae1`](https://github.com/nodejs/node/commit/e903798ae1)] - **doc**: add note regarding unfinished TLA (Antoine du Hamel) [#41434](https://github.com/nodejs/node/pull/41434)
- \[[`a90defebcf`](https://github.com/nodejs/node/commit/a90defebcf)] - **esm**: make `process.exit()` default to exit code 0 (Gang Chen) [#41388](https://github.com/nodejs/node/pull/41388)
- \[[`fc328f1ab0`](https://github.com/nodejs/node/commit/fc328f1ab0)] - **fs**: nullish coalescing to respect zero positional reads (Omar El-Mihilmy) [#40716](https://github.com/nodejs/node/pull/40716)
- \[[`004eafbebf`](https://github.com/nodejs/node/commit/004eafbebf)] - **(SEMVER-MINOR)** **lib**: add unsubscribe method to non-active DC channels (simon-id) [#40433](https://github.com/nodejs/node/pull/40433)
- \[[`625be7585d`](https://github.com/nodejs/node/commit/625be7585d)] - **(SEMVER-MINOR)** **lib**: add return value for DC channel.unsubscribe (simon-id) [#40433](https://github.com/nodejs/node/pull/40433)
- \[[`2c365961d0`](https://github.com/nodejs/node/commit/2c365961d0)] - **module**: support pattern trailers for imports field (Guy Bedford) [#40041](https://github.com/nodejs/node/pull/40041)
- \[[`607bc74eae`](https://github.com/nodejs/node/commit/607bc74eae)] - **(SEMVER-MINOR)** **module**: support pattern trailers (Guy Bedford) [#39635](https://github.com/nodejs/node/pull/39635)
- \[[`f74fe2a59c`](https://github.com/nodejs/node/commit/f74fe2a59c)] - **(SEMVER-MINOR)** **src**: make napi_create_reference accept symbol (JckXia) [#39926](https://github.com/nodejs/node/pull/39926)
- \[[`b050c65885`](https://github.com/nodejs/node/commit/b050c65885)] - **src**: add option to disable loading native addons (Dominic Elm) [#39977](https://github.com/nodejs/node/pull/39977)
- \[[`c1695ac68a`](https://github.com/nodejs/node/commit/c1695ac68a)] - **tools**: update certdata.txt (Richard Lau) [#40280](https://github.com/nodejs/node/pull/40280)

Windows 32-bit Installer: https://nodejs.org/dist/v14.19.0/node-v14.19.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v14.19.0/node-v14.19.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v14.19.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v14.19.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v14.19.0/node-v14.19.0.pkg \
macOS Intel 64-bit Binary: https://nodejs.org/dist/v14.19.0/node-v14.19.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v14.19.0/node-v14.19.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v14.19.0/node-v14.19.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v14.19.0/node-v14.19.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v14.19.0/node-v14.19.0-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v14.19.0/node-v14.19.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v14.19.0/node-v14.19.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v14.19.0/node-v14.19.0.tar.gz \
Other release files: https://nodejs.org/dist/v14.19.0/ \
Documentation: https://nodejs.org/docs/v14.19.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

bad96e53e403a8bbc3c5b92bf451327f90ddaf37923ef3a91f3f7e267ff68930  node-v14.19.0-aix-ppc64.tar.gz
e4ece4481b948c95f28662e74fc738ad03e07e877d9c9a47e59b4eb099aa1449  node-v14.19.0-darwin-x64.tar.gz
fe344d2184303c6ca79ac4c9cb1391f15b9c00ad9c82366737f35037301e15f8  node-v14.19.0-darwin-x64.tar.xz
d83912c709207b2c315dda6e9432734e0492fe9034a80b7931bcc40596f20e3d  node-v14.19.0-headers.tar.gz
2341b02f456c98951ff25d0121b112529670f12e78154942df97a50099662baa  node-v14.19.0-headers.tar.xz
89c03d1c156c0161c891924d4a309df3308bbf245641413d72affae9b62e97e0  node-v14.19.0-linux-arm64.tar.gz
73224b331d2bb0f05fd2ff62d1c736460f12f8e8757e8afbbcdf11a7f1464da5  node-v14.19.0-linux-arm64.tar.xz
3d2e25613ec215e461d790a90e57c51e9d1866ebf57d49b34cd2c5a1b0473da3  node-v14.19.0-linux-armv7l.tar.gz
39114862eb7a68ece50a6410015b9ef5cdb9944b4c6e1fb90afdacfac758759f  node-v14.19.0-linux-armv7l.tar.xz
de2000321b03aaa7f45efd1eaa4ff1a3ffcdc094d82f01db68d5edf6cd92fc9c  node-v14.19.0-linux-ppc64le.tar.gz
bc35e7776e08a7312015cd52fd567891d981afb1cf07769727c7b9d681d3fa7d  node-v14.19.0-linux-ppc64le.tar.xz
c4c6c3af00b3574013f96b2575f3756605c26bb07978ce5cbd98322de2872437  node-v14.19.0-linux-s390x.tar.gz
e3ebd74744ff5c6efd77cd882d38d87a83cd74d343cc35fe995f37cd732de95e  node-v14.19.0-linux-s390x.tar.xz
223ca31e3440b79a3fe6828161b1843743eaa7610a88c0e1ac1d8e1d815b44cd  node-v14.19.0-linux-x64.tar.gz
daac84812f9815c5040561cb3a4c73ced26a490019d503432c7da17847389c55  node-v14.19.0-linux-x64.tar.xz
04fefffdf2f388ec738f57ef19674b069db55920d50216c0d0f13389890ec35f  node-v14.19.0.pkg
284cf4824d0c3e79177899519f90c160491c0c642bbf459b72a97679cad8fe87  node-v14.19.0.tar.gz
e92e846300e6117547d37ea8d5bd32244c19b2fcefcb39e1420a47637f45030c  node-v14.19.0.tar.xz
e4446bf06e7a15fd302413697d053871bdd73170dca2e762d103b59076241375  node-v14.19.0-win-x64.7z
8b572958ee6f0b9b29fa90695d6345bd561c930bb84f21423fb036b4eef8043e  node-v14.19.0-win-x64.zip
706532101b2f664c6a2b0c7a2c7b85a9b74913388fba88fbdce7bdee191ad4ed  node-v14.19.0-win-x86.7z
a46a9e694d3cc35c589554b33b86220093d088fd5d2b199169d04c7944590ab7  node-v14.19.0-win-x86.zip
f1b1d4e00b36bc3be0025a409a1e44d4375220407239202d8627becd7fd359f0  node-v14.19.0-x64.msi
54a965894b0fc133b85aef0a5b25ee5368f87d859ad1aa6a49efa67a39f1d469  node-v14.19.0-x86.msi
cefdec18fcc383d2abfc401a34f25afab134b03a45b491446be9e628cb0516a0  win-x64/node.exe
0da8c631a4d5c3d140e274b2567ac53cee4696fd15f1fe777116120f69bdb66d  win-x64/node.lib
c3aafbf20b9303e657820acf1c6a1614a55f22ca3fd46e945b33281c2b3f28c9  win-x64/node_pdb.7z
106e75d7cbf312692556b05fb24bbc98fbead73189d2f060ea6636eb003eb559  win-x64/node_pdb.zip
5f74930986e1e3def3f1f666ac79b3d3e3a959accb285aabf452a010cbaa65e2  win-x86/node.exe
0bac1efa4d2a090a12bccc0aac52353028b34b9a9bde8def668e26ce134c0d11  win-x86/node.lib
50fa77a34d011f2e179b9bb46097fd6a61aed6595227ad4b79c59a2be7ce41ec  win-x86/node_pdb.7z
647e7a0099f5ff79e901f9aa05cb91d18ef9f20193d3208c90764f156a86241d  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEyC+jrhy+3Gvka5NgxDzsRcF6uTwFAmH5NogACgkQxDzsRcF6
uTxDvw//bTZXkakxzpCY0LcnaQALrSvMBs2eVM3T8EhaM6hzI07FpL593i1CUvMa
ed3igKfmA9v49nPYVFTwiTCyDNrisPojz9YkCZAvJfPbrnVkRnuILll6YOv6QNnA
HctzWFkmFTALSZH6krVzsPZv35kMJvV6IE1RXgwSLpBCBsZmY2FhzNbUbZu/gdZE
k/IJDWPYu75im831vlO2yBKjfNkGK6SiggBrJsmlSHORstOL9bOHkUipzGBr7Y/M
F1uI+xFkEJaRVpWZDu3r1dyK7shmFe6muGv5wXRnx3labiyMP+nN75MuTDyg5crS
Yq26sqJMWt+WR1Q1xZDB815LKFeO3MHjkxLyjIN5pN428O9jNJ/nqU53EFCDmRGJ
IixhHlAPUCqBzMtoZTybcfK30ZnmWDvj0sUCyDVK9EknStllAVAL9RrAuGBhcgEj
7QH01ZVxTX0V5wr7lqlLuBuOcMG2K/sD7Mk1om/DqbGT0GpeB+lJQJoaKLNREz8T
h33ftR8fJE6sW5m34PgRSNxfB/jJp5d2NIb4snOEU227wVi/x7D4ENicbX8t+ZC0
+p8S+ZJSiMyNPVhHO/qgv3Yf5/OoHCC+6+PQxvY71kLE1pUJQHNMKrl389VZ/VVG
aDk6s23iVai493kjIfnakGnjRMrur8g2RuZFaCExuqV2eZm0Fdc=
=A82j
-----END PGP SIGNATURE-----

```
