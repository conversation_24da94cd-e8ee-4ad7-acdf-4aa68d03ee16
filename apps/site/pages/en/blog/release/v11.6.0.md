---
date: '2018-12-26T16:30:43.116Z'
category: release
title: Node v11.6.0 (Current)
layout: blog-post
author: <PERSON><PERSON>
---

### Notable Changes

- **cli**:
  - add --max-http-header-size flag (cjihrig) [#24811](https://github.com/nodejs/node/pull/24811)
- **crypto**:
  - always accept certificates as public keys (<PERSON>) [#24234](https://github.com/nodejs/node/pull/24234)
  - add key object API (<PERSON>) [#24234](https://github.com/nodejs/node/pull/24234)
  - update root certificates (<PERSON>) [#25113](https://github.com/nodejs/node/pull/25113)
- **deps**:
  - upgrade to libuv 1.24.1 (cjihrig) [#25078](https://github.com/nodejs/node/pull/25078)
  - upgrade npm to 6.5.0 (<PERSON>) [#24734](https://github.com/nodejs/node/pull/24734)
- **http**:
  - add maxHeaderSize property (cjihrig) [#24860](https://github.com/nodejs/node/pull/24860)

### Commits

- [[`a9ab28df2c`](https://github.com/nodejs/node/commit/a9ab28df2c)] - **assert**: inspect getters (Ruben Bridgewater) [#25004](https://github.com/nodejs/node/pull/25004)
- [[`c6bfa66b2e`](https://github.com/nodejs/node/commit/c6bfa66b2e)] - **buffer**: simplify code (Ruben Bridgewater) [#25151](https://github.com/nodejs/node/pull/25151)
- [[`9b38bbff7f`](https://github.com/nodejs/node/commit/9b38bbff7f)] - **build**: correct fi indentation in Makefile (Daniel Bevenius) [#25107](https://github.com/nodejs/node/pull/25107)
- [[`4513516f5e`](https://github.com/nodejs/node/commit/4513516f5e)] - **build**: add a space to clarify skipping crypto msg (Daniel Bevenius) [#25011](https://github.com/nodejs/node/pull/25011)
- [[`7b2eefc103`](https://github.com/nodejs/node/commit/7b2eefc103)] - **child_process**: spawn ignores options in case args is undefined (Eduard Bondarenko) [#24913](https://github.com/nodejs/node/pull/24913)
- [[`edd8bd0ee0`](https://github.com/nodejs/node/commit/edd8bd0ee0)] - **(SEMVER-MINOR)** **cli**: add --max-http-header-size flag (cjihrig) [#24811](https://github.com/nodejs/node/pull/24811)
- [[`e6c1e8de95`](https://github.com/nodejs/node/commit/e6c1e8de95)] - **(SEMVER-MINOR)** **crypto**: always accept certificates as public keys (Tobias Nießen) [#24234](https://github.com/nodejs/node/pull/24234)
- [[`3b53df0748`](https://github.com/nodejs/node/commit/3b53df0748)] - **(SEMVER-MINOR)** **crypto**: add key object API (Tobias Nießen) [#24234](https://github.com/nodejs/node/pull/24234)
- [[`6f6f339ef0`](https://github.com/nodejs/node/commit/6f6f339ef0)] - **crypto**: update root certificates (Sam Roberts) [#25113](https://github.com/nodejs/node/pull/25113)
- [[`e855018968`](https://github.com/nodejs/node/commit/e855018968)] - **(SEMVER-MINOR)** **deps**: upgrade npm to 6.5.0 (Audrey Eschright) [#24734](https://github.com/nodejs/node/pull/24734)
- [[`155d1d54bf`](https://github.com/nodejs/node/commit/155d1d54bf)] - **deps**: upgrade to libuv 1.24.1 (cjihrig) [#25078](https://github.com/nodejs/node/pull/25078)
- [[`0057af293a`](https://github.com/nodejs/node/commit/0057af293a)] - **(SEMVER-MINOR)** **deps**: cherry-pick http_parser_set_max_header_size (cjihrig) [#24811](https://github.com/nodejs/node/pull/24811)
- [[`b78d48749a`](https://github.com/nodejs/node/commit/b78d48749a)] - **doc**: fix links in test/common/README.md (Vse Mozhet Byt) [#25172](https://github.com/nodejs/node/pull/25172)
- [[`6a690ee51b`](https://github.com/nodejs/node/commit/6a690ee51b)] - **doc**: revise "Breaking Changes and Deprecations" (Rich Trott) [#25116](https://github.com/nodejs/node/pull/25116)
- [[`4ca09517c2`](https://github.com/nodejs/node/commit/4ca09517c2)] - **doc**: describe root cert update process (Sam Roberts) [#25113](https://github.com/nodejs/node/pull/25113)
- [[`4561e2c984`](https://github.com/nodejs/node/commit/4561e2c984)] - **doc**: revise "Breaking Changes" section of Collaborator Guide (Rich Trott) [#25071](https://github.com/nodejs/node/pull/25071)
- [[`2516e9cfd0`](https://github.com/nodejs/node/commit/2516e9cfd0)] - **doc,lib,test**: capitalize comment sentences (Ruben Bridgewater) [#24996](https://github.com/nodejs/node/pull/24996)
- [[`d1a98a8d0a`](https://github.com/nodejs/node/commit/d1a98a8d0a)] - **events**: simplify stack compare function (Ruben Bridgewater) [#24744](https://github.com/nodejs/node/pull/24744)
- [[`ae50f480d2`](https://github.com/nodejs/node/commit/ae50f480d2)] - **(SEMVER-MINOR)** **http**: add maxHeaderSize property (cjihrig) [#24860](https://github.com/nodejs/node/pull/24860)
- [[`b3f45daf7b`](https://github.com/nodejs/node/commit/b3f45daf7b)] - **lib**: make internal API warning more direct (Rich Trott) [#25125](https://github.com/nodejs/node/pull/25125)
- [[`2fc43fbe43`](https://github.com/nodejs/node/commit/2fc43fbe43)] - **lib**: switch to object spread where possible (Ruben Bridgewater) [#25104](https://github.com/nodejs/node/pull/25104)
- [[`96bdd47734`](https://github.com/nodejs/node/commit/96bdd47734)] - **lib**: refactor argument validation using validateString (ZYSzys) [#24960](https://github.com/nodejs/node/pull/24960)
- [[`0cde1a4fdc`](https://github.com/nodejs/node/commit/0cde1a4fdc)] - **lib**: remove unused NativeModule/NativeModule wraps (Joyee Cheung) [#24904](https://github.com/nodejs/node/pull/24904)
- [[`add566eee5`](https://github.com/nodejs/node/commit/add566eee5)] - **os**: use uv_os_gethostname() in hostname() (cjihrig) [#25111](https://github.com/nodejs/node/pull/25111)
- [[`85a136974e`](https://github.com/nodejs/node/commit/85a136974e)] - **perf_hooks**: make GC tracking state per-Environment (Anna Henningsen) [#25053](https://github.com/nodejs/node/pull/25053)
- [[`3f82144c98`](https://github.com/nodejs/node/commit/3f82144c98)] - **process**: move environment variable proxy code into node_env_var.cc (Joyee Cheung) [#25067](https://github.com/nodejs/node/pull/25067)
- [[`c9f809e36f`](https://github.com/nodejs/node/commit/c9f809e36f)] - **src**: add DCHECK macros (kiyomizumia) [#24359](https://github.com/nodejs/node/pull/24359)
- [[`b801b0372a`](https://github.com/nodejs/node/commit/b801b0372a)] - **src**: use std::vector for setting up process.execPath (Anna Henningsen) [#25069](https://github.com/nodejs/node/pull/25069)
- [[`54e42f04a7`](https://github.com/nodejs/node/commit/54e42f04a7)] - **src**: port GetLoadedLibraries for freebsd (Gireesh Punathil) [#25106](https://github.com/nodejs/node/pull/25106)
- [[`fd0361bff0`](https://github.com/nodejs/node/commit/fd0361bff0)] - **src**: mark options parsers as const (Anna Henningsen) [#25065](https://github.com/nodejs/node/pull/25065)
- [[`c6388edf34`](https://github.com/nodejs/node/commit/c6388edf34)] - **src**: handle empty Maybe in uv binding initialize (Anna Henningsen) [#25079](https://github.com/nodejs/node/pull/25079)
- [[`6f3b421dd5`](https://github.com/nodejs/node/commit/6f3b421dd5)] - **src**: schedule destroy hooks in BeforeExit early during bootstrap (Joyee Cheung) [#25020](https://github.com/nodejs/node/pull/25020)
- [[`a4505c698f`](https://github.com/nodejs/node/commit/a4505c698f)] - **src**: extract common Bind method (Jon Moss) [#22315](https://github.com/nodejs/node/pull/22315)
- [[`09a99c6834`](https://github.com/nodejs/node/commit/09a99c6834)] - **src**: mark some global state as const (Anna Henningsen) [#25052](https://github.com/nodejs/node/pull/25052)
- [[`7f34c768da`](https://github.com/nodejs/node/commit/7f34c768da)] - **src**: remove internalBinding('config').warningFile (Joyee Cheung) [#24959](https://github.com/nodejs/node/pull/24959)
- [[`c80ac7fae3`](https://github.com/nodejs/node/commit/c80ac7fae3)] - **(SEMVER-MINOR)** **src**: add kUInteger parsing (Matteo Collina) [#24811](https://github.com/nodejs/node/pull/24811)
- [[`45d48510bd`](https://github.com/nodejs/node/commit/45d48510bd)] - **test**: fix test-tls-session-timeout (Rich Trott) [#25188](https://github.com/nodejs/node/pull/25188)
- [[`6557ea180c`](https://github.com/nodejs/node/commit/6557ea180c)] - **test**: mark test-trace-events-api-worker-disabled flaky (Rich Trott) [#25197](https://github.com/nodejs/node/pull/25197)
- [[`db54531c8d`](https://github.com/nodejs/node/commit/db54531c8d)] - **test**: remove Files: comment processing from Python test runner (Rich Trott) [#25183](https://github.com/nodejs/node/pull/25183)
- [[`a28cae0e55`](https://github.com/nodejs/node/commit/a28cae0e55)] - **test**: add hasCrypto check to common flags check (Daniel Bevenius) [#25147](https://github.com/nodejs/node/pull/25147)
- [[`175f7b60c2`](https://github.com/nodejs/node/commit/175f7b60c2)] - **test**: remove unnecessary eslint-disable comments (Rich Trott) [#25119](https://github.com/nodejs/node/pull/25119)
- [[`d09e3335a6`](https://github.com/nodejs/node/commit/d09e3335a6)] - **test**: remove obsolete eslint comments (cjihrig) [#25088](https://github.com/nodejs/node/pull/25088)
- [[`8279826ce6`](https://github.com/nodejs/node/commit/8279826ce6)] - **test**: verify input flags (Ruben Bridgewater) [#24876](https://github.com/nodejs/node/pull/24876)
- [[`1f45b2370d`](https://github.com/nodejs/node/commit/1f45b2370d)] - **test**: add signal check to test-esm-cjs-main (Rich Trott) [#25073](https://github.com/nodejs/node/pull/25073)
- [[`3e1fe19194`](https://github.com/nodejs/node/commit/3e1fe19194)] - **test**: add missing tmpdir.refresh() in recently-added test (Rich Trott) [#25098](https://github.com/nodejs/node/pull/25098)
- [[`5eb5d1d7b1`](https://github.com/nodejs/node/commit/5eb5d1d7b1)] - **test**: test internal/util/types in vm (ZYSzys) [#25056](https://github.com/nodejs/node/pull/25056)
- [[`9ad6bc2e6e`](https://github.com/nodejs/node/commit/9ad6bc2e6e)] - **test**: remove magic numbers in test-gc-http-client-onerror (Rich Trott) [#24943](https://github.com/nodejs/node/pull/24943)
- [[`30b61554f6`](https://github.com/nodejs/node/commit/30b61554f6)] - **test**: merge test with unnecessary child process (Sam Roberts) [#25025](https://github.com/nodejs/node/pull/25025)
- [[`e340b8f1ff`](https://github.com/nodejs/node/commit/e340b8f1ff)] - **tls**: re-define max supported version as 1.2 (Sam Roberts) [#25024](https://github.com/nodejs/node/pull/25024)
- [[`8ab0a48928`](https://github.com/nodejs/node/commit/8ab0a48928)] - **tools**: update ESLint to 5.11.0 (cjihrig) [#25191](https://github.com/nodejs/node/pull/25191)
- [[`c7fa132aea`](https://github.com/nodejs/node/commit/c7fa132aea)] - **tools**: alphabetize IGNORED_SUITES in tools/test.py (Rich Trott) [#25182](https://github.com/nodejs/node/pull/25182)
- [[`073a51220e`](https://github.com/nodejs/node/commit/073a51220e)] - **tools**: report unused disable-directives for ESLint (Rich Trott) [#25119](https://github.com/nodejs/node/pull/25119)
- [[`9b941da78d`](https://github.com/nodejs/node/commit/9b941da78d)] - **tools**: update certdata.txt (Sam Roberts) [#25113](https://github.com/nodejs/node/pull/25113)
- [[`a5bccc2919`](https://github.com/nodejs/node/commit/a5bccc2919)] - **tools**: make apilinks building more robust (Joyee Cheung) [#25019](https://github.com/nodejs/node/pull/25019)
- [[`ed3303ba99`](https://github.com/nodejs/node/commit/ed3303ba99)] - **tools**: enable no-useless-constructor lint rule (cjihrig) [#25055](https://github.com/nodejs/node/pull/25055)
- [[`7df59f824b`](https://github.com/nodejs/node/commit/7df59f824b)] - **vm**: reuse validateString of internal/validators (ZYSzys) [#25074](https://github.com/nodejs/node/pull/25074)
- [[`74e08c0458`](https://github.com/nodejs/node/commit/74e08c0458)] - **vm**: simplify Script constructor options validation (cjihrig) [#25054](https://github.com/nodejs/node/pull/25054)
- [[`4f28da883f`](https://github.com/nodejs/node/commit/4f28da883f)] - **worker**: fix nullptr deref after MessagePort deser failure (Anna Henningsen) [#25076](https://github.com/nodejs/node/pull/25076)

Windows 32-bit Installer: https://nodejs.org/dist/v11.6.0/node-v11.6.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v11.6.0/node-v11.6.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v11.6.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v11.6.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v11.6.0/node-v11.6.0.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v11.6.0/node-v11.6.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v11.6.0/node-v11.6.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v11.6.0/node-v11.6.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v11.6.0/node-v11.6.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v11.6.0/node-v11.6.0-aix-ppc64.tar.gz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v11.6.0/node-v11.6.0-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v11.6.0/node-v11.6.0-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v11.6.0/node-v11.6.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v11.6.0/node-v11.6.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v11.6.0/node-v11.6.0.tar.gz \
Other release files: https://nodejs.org/dist/v11.6.0/ \
Documentation: https://nodejs.org/docs/v11.6.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

b015a390703cf829c169016991c891c91807db50c76be8a73f39f1415efed462  node-v11.6.0-aix-ppc64.tar.gz
c880063b112c48130dba8f7b058de61959ae46fddcfa363715571b22c1cbeb26  node-v11.6.0-darwin-x64.tar.gz
55e0ac7a97711e91b720cb51733ac5f4739b66c0dd7daa727728656ca73928e3  node-v11.6.0-darwin-x64.tar.xz
9c4e877b9f85ec6780656fa82adc53e50cbefdefa9c23d2248e8ac8b31630628  node-v11.6.0-headers.tar.gz
b232ce0abb7f5d92e19441694cc3c65b81a8934ca23b7621b9a5e3a72cb2794f  node-v11.6.0-headers.tar.xz
a112c89390965356036597e712ec3939c37090bbafd513b90ab2a524bd29190a  node-v11.6.0-linux-arm64.tar.gz
956016db41f4f96f8e005d36c738d7c833442d09a970462552eb214027e0268a  node-v11.6.0-linux-arm64.tar.xz
36e77daee70c7caf77977fafd5adaeb66b109f547e1e25c51f861f1528632297  node-v11.6.0-linux-armv6l.tar.gz
d49e5544b22e41e615c04bf1e366800781f80abf9f04eda69a7ed16116729ab1  node-v11.6.0-linux-armv6l.tar.xz
8386c32569e13fd7953736b20bfff9e9e1c1d1905bfea5bc317d34105c1e78c6  node-v11.6.0-linux-armv7l.tar.gz
b6c3b4834b900c5152a8030f5d383669df4c2cc203064e69ae1b8189de6f0d0d  node-v11.6.0-linux-armv7l.tar.xz
e09b92294ea7e39d0f188626bc75e47a5a52f01ca2c3e6f27a194f207a12ec0e  node-v11.6.0-linux-ppc64le.tar.gz
304cd210686e4d743c2c132f4fda897bb36a8d5d1ee5591aa43c0cd21560a7ac  node-v11.6.0-linux-ppc64le.tar.xz
20b6469d7d554bc80911450f3395f2bac2eb615c81d963dbffcd4c6b2bced22d  node-v11.6.0-linux-s390x.tar.gz
4eb5cb5823d324819f32872be41e66d676c7792e05c0c4d4411b297a87cb6cab  node-v11.6.0-linux-s390x.tar.xz
ee5b070caa8e812ee763b65e75c6f4f120a65e40fdef807b075e39dc8916fa9c  node-v11.6.0-linux-x64.tar.gz
2251a6c5b332e7ea69bbefba11950cb6c27ba50fa700468711f729da6a6f5324  node-v11.6.0-linux-x64.tar.xz
de17fb9f6fa41a2b35f46fbc9cb090db203d4d828d9c6004bb4063902377737c  node-v11.6.0.pkg
10e380e751e6208d201b01dd1cc11a9b816c8013371f16362070c06d3d061444  node-v11.6.0-sunos-x64.tar.gz
43e0753cb5dca2c3d874599e7ae029715d7645aeb912980d1e857e9a1bc37da3  node-v11.6.0-sunos-x64.tar.xz
39ef4f1866f75786baff5959439483fafdc99d3ee3a0568a13cc635d64cf5e0b  node-v11.6.0.tar.gz
94f2be389c80ab939114f67c824db7ebd12df602358b7481c55431336bbff9b3  node-v11.6.0.tar.xz
92b25af01f6b6b9a5fd12142779fb9b3b3974f4506e11dd4a6b0c2ca022db954  node-v11.6.0-win-x64.7z
d230828c1cc9863c9768106ffee0320ba42049b594bd2689e430b872e8f0b2dd  node-v11.6.0-win-x64.zip
d6670c8d26a78e3b834da6efacd37dceff841645c2d475c642b4ddad84291427  node-v11.6.0-win-x86.7z
176e7ced367cad8858fd62c858e23129556842c28ce1cb3d17729f51488fab55  node-v11.6.0-win-x86.zip
b5a896741c8cc1f5ccf353d24577c5b78b11af9e52cb9969a70d27d311e3873c  node-v11.6.0-x64.msi
5cdd2c590acc8d5d5c93c09bd61ab485c289168bbf732efeda7f83422f8b9691  node-v11.6.0-x86.msi
862ab8a5e1a7b666b6462defa40dd7de16a5ea0afc407707fb7dd9aa804bf44b  win-x64/node.exe
8a7f7821e9dd89600815e11df3906c1257ba9e465d1a9ba978d097038bb20b89  win-x64/node.lib
80167ea916b04fb90fc9deebe5d77b3a15e4953cfa31eed2fa1541b6ee242e05  win-x64/node_pdb.7z
613932345f90cedda29716b0798434734d7bc13230da4d2ae80f5e0a287b0ba9  win-x64/node_pdb.zip
7318f417499fcfbe719957bd8be1257268d6e139563d3adc9e62f3677e99a3ec  win-x86/node.exe
f4f8ebeccc3cb61ceea668bc22d9a03d7939c52a686d43441f82317dd6d10a5a  win-x86/node.lib
504a17ab4e845a972c54d518d460c4fee82229b28596fd9f7b6b01f9dd706339  win-x86/node_pdb.7z
331d5ff93cb83552e83082c9fbe0960c02bdcd3c2359223f997b905be637ff61  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQEzBAEBCAAdFiEEDv/hvO/ZyE49CYFSkzsB9AtcqUYFAlwjq/QACgkQkzsB9Atc
qUbstAf/dsLme1aAetyNZz7nHm/tqB9PtSQdVllaupWnINi5vFRq/ANLqzmOuXGc
GoHFOM2FaZJZX/Af3nUu3sBT9RN9IoLInWC42Eov0WQR0ydzaP7amunJAKvnF/ZR
0Qlx2753RhK5FRWyvl55Frrp3+GwQ4aNqG7Pmm+cgMq1KWE0ok4W/B5JOdvWFdF1
k4H6Ggh7P4kZqbz43RkeUOFbybVQRggIk7W/Tg2/SdIBYShmqElR3mOstE3vC8vK
6ahIHG0UUbUXbZniN/zNYQsRO0WQQlGGhmjuPex/BPfvlZ4OFwQihS2WEACUtXzR
ptODnm3/TZ9xSbotuRngpCLVNUVB0g==
=5QU2
-----END PGP SIGNATURE-----

```
