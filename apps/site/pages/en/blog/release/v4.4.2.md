---
date: '2016-04-01T01:47:06.225Z'
category: release
title: Node v4.4.2 (LTS)
layout: blog-post
author: <PERSON><PERSON>
---

This release includes a security update for npm. For more details you can read [this post on our blog](/blog/vulnerability/npm-tokens-leak-march-2016/) written by [<PERSON>](https://github.com/othiym23) from npm.

_Update_: The version of npm included in this release did not have the correct version string. As such executing `npm -v` will report `2.15.0` rather than `2.15.1`, which is incorrect. The source code included in this release is in fact the source for `2.15.1`, including the security fix.

### Notable Changes

- **https**:
  - Under certain conditions ssl sockets may have been causing a memory leak when keepalive is enabled. This is no longer the case. (<PERSON>) [#5713](https://github.com/nodejs/node/pull/5713)
- **lib**:
  - The way that we were internally passing arguments was causing a potential leak. By copying the arguments into an array we can avoid this. (<PERSON>) [#4361](https://github.com/nodejs/node/pull/4361)
- **npm**:
  - Upgrade to v2.15.1. Fixes a security flaw in the use of authentication tokens in HTTP requests that would allow an attacker to set up a server that could collect tokens from users of the command-line interface. Authentication tokens have previously been sent with every request made by the CLI for logged-in users, regardless of the destination of the request. This update fixes this by only including those tokens for requests made against the registry or registries used for the current install. (Forrest L Norvell)
- **repl**:
  - Previously if you were using the repl in strict mode the column number would be wrong in a stack trace. This is no longer an issue. (Prince J Wesley) [#5416](https://github.com/nodejs/node/pull/5416)

### Commits

- [[`96e163a79f`](https://github.com/nodejs/node/commit/96e163a79f)] - **buffer**: changing let in for loops back to var (Gareth Ellis) [#5819](https://github.com/nodejs/node/pull/5819)
- [[`0c6f6742f2`](https://github.com/nodejs/node/commit/0c6f6742f2)] - **console**: check that stderr is writable (Rich Trott) [#5635](https://github.com/nodejs/node/pull/5635)
- [[`55c3f804c4`](https://github.com/nodejs/node/commit/55c3f804c4)] - **deps**: upgrade npm in LTS to 2.15.1 (Forrest L Norvell)
- [[`1d0e4a987d`](https://github.com/nodejs/node/commit/1d0e4a987d)] - **deps**: remove unused openssl files (Ben Noordhuis) [#5619](https://github.com/nodejs/node/pull/5619)
- [[`d55599f4d8`](https://github.com/nodejs/node/commit/d55599f4d8)] - **dns**: use template literals (Benjamin Gruenbaum) [#5809](https://github.com/nodejs/node/pull/5809)
- [[`42bbdc9dd1`](https://github.com/nodejs/node/commit/42bbdc9dd1)] - **doc** Add @mhdawson back to the CTC (James M Snell) [#5633](https://github.com/nodejs/node/pull/5633)
- [[`8d86d232e7`](https://github.com/nodejs/node/commit/8d86d232e7)] - **doc**: typo: interal->internal. (Corey Kosak) [#5849](https://github.com/nodejs/node/pull/5849)
- [[`60ddab841e`](https://github.com/nodejs/node/commit/60ddab841e)] - **doc**: add instructions to only sign a release (Jeremiah Senkpiel) [#5876](https://github.com/nodejs/node/pull/5876)
- [[`040263e0f3`](https://github.com/nodejs/node/commit/040263e0f3)] - **doc**: grammar, clarity and links in timers doc (Bryan English) [#5792](https://github.com/nodejs/node/pull/5792)
- [[`8c24bd25a6`](https://github.com/nodejs/node/commit/8c24bd25a6)] - **doc**: fix order of end tags of list after heading (firedfox) [#5874](https://github.com/nodejs/node/pull/5874)
- [[`7c837028da`](https://github.com/nodejs/node/commit/7c837028da)] - **doc**: use consistent event name parameter (Benjamin Gruenbaum) [#5850](https://github.com/nodejs/node/pull/5850)
- [[`20faf9097d`](https://github.com/nodejs/node/commit/20faf9097d)] - **doc**: explain error message on missing main file (Wolfgang Steiner) [#5812](https://github.com/nodejs/node/pull/5812)
- [[`79d26ae196`](https://github.com/nodejs/node/commit/79d26ae196)] - **doc**: explain path.format expected properties (John Eversole) [#5801](https://github.com/nodejs/node/pull/5801)
- [[`e43e8e3a31`](https://github.com/nodejs/node/commit/e43e8e3a31)] - **doc**: add a cli options doc page (Jeremiah Senkpiel) [#5787](https://github.com/nodejs/node/pull/5787)
- [[`c0a24e4a1d`](https://github.com/nodejs/node/commit/c0a24e4a1d)] - **doc**: fix multiline return comments in querystring (Claudio Rodriguez) [#5705](https://github.com/nodejs/node/pull/5705)
- [[`bf1fe4693c`](https://github.com/nodejs/node/commit/bf1fe4693c)] - **doc**: Add windows example for Path.format (Mithun Patel) [#5700](https://github.com/nodejs/node/pull/5700)
- [[`3b8fc4fddc`](https://github.com/nodejs/node/commit/3b8fc4fddc)] - **doc**: update crypto docs to use good defaults (Bill Automata) [#5505](https://github.com/nodejs/node/pull/5505)
- [[`a6ec8a6cb7`](https://github.com/nodejs/node/commit/a6ec8a6cb7)] - **doc**: fix crypto update() signatures (Brian White) [#5500](https://github.com/nodejs/node/pull/5500)
- [[`eb0ed46665`](https://github.com/nodejs/node/commit/eb0ed46665)] - **doc**: reformat & improve node.1 manual page (Jeremiah Senkpiel) [#5497](https://github.com/nodejs/node/pull/5497)
- [[`b70ca4a4b4`](https://github.com/nodejs/node/commit/b70ca4a4b4)] - **doc**: updated fs #5862 removed irrelevant data in fs.markdown (topal) [#5877](https://github.com/nodejs/node/pull/5877)
- [[`81876612f7`](https://github.com/nodejs/node/commit/81876612f7)] - **https**: fix ssl socket leak when keepalive is used (Alexander Penev) [#5713](https://github.com/nodejs/node/pull/5713)
- [[`6daebdbd9b`](https://github.com/nodejs/node/commit/6daebdbd9b)] - **lib**: simplify code with String.prototype.repeat() (Jackson Tian) [#5359](https://github.com/nodejs/node/pull/5359)
- [[`108fc90dd7`](https://github.com/nodejs/node/commit/108fc90dd7)] - **lib**: reduce usage of `self = this` (Jackson Tian) [#5231](https://github.com/nodejs/node/pull/5231)
- [[`3c8e59c396`](https://github.com/nodejs/node/commit/3c8e59c396)] - **lib**: copy arguments object instead of leaking it (Nathan Woltman) [#4361](https://github.com/nodejs/node/pull/4361)
- [[`8648420586`](https://github.com/nodejs/node/commit/8648420586)] - **net**: make `isIPv4` and `isIPv6` more efficient (Vladimir Kurchatkin) [#5478](https://github.com/nodejs/node/pull/5478)
- [[`07b7172d76`](https://github.com/nodejs/node/commit/07b7172d76)] - **net**: remove unused `var self = this` from old code (Benjamin Gruenbaum) [#5224](https://github.com/nodejs/node/pull/5224)
- [[`acbce4b72b`](https://github.com/nodejs/node/commit/acbce4b72b)] - **repl**: fix stack trace column number in strict mode (Prince J Wesley) [#5416](https://github.com/nodejs/node/pull/5416)
- [[`0a1eb168e0`](https://github.com/nodejs/node/commit/0a1eb168e0)] - **test**: fix `test-cluster-worker-kill` (Santiago Gimeno) [#5814](https://github.com/nodejs/node/pull/5814)
- [[`86b876fe7b`](https://github.com/nodejs/node/commit/86b876fe7b)] - **test**: smaller chunk size for smaller person.jpg (Jérémy Lal) [#5813](https://github.com/nodejs/node/pull/5813)
- [[`1135ee97e7`](https://github.com/nodejs/node/commit/1135ee97e7)] - **test**: strip non-free icc profile from person.jpg (Jérémy Lal) [#5813](https://github.com/nodejs/node/pull/5813)
- [[`0836d7e2fb`](https://github.com/nodejs/node/commit/0836d7e2fb)] - **test**: fix flaky test-cluster-shared-leak (Claudio Rodriguez) [#5802](https://github.com/nodejs/node/pull/5802)
- [[`e57355c2f4`](https://github.com/nodejs/node/commit/e57355c2f4)] - **test**: make test-net-connect-options-ipv6.js better (Michael Dawson) [#5791](https://github.com/nodejs/node/pull/5791)
- [[`1b266fc15c`](https://github.com/nodejs/node/commit/1b266fc15c)] - **test**: remove the use of curl in the test suite (Santiago Gimeno) [#5750](https://github.com/nodejs/node/pull/5750)
- [[`7e45d4f076`](https://github.com/nodejs/node/commit/7e45d4f076)] - **test**: minimize test-http-get-pipeline-problem (Rich Trott) [#5728](https://github.com/nodejs/node/pull/5728)
- [[`78effc3484`](https://github.com/nodejs/node/commit/78effc3484)] - **test**: add batch of known issue tests (cjihrig) [#5653](https://github.com/nodejs/node/pull/5653)
- [[`d506eea4b7`](https://github.com/nodejs/node/commit/d506eea4b7)] - **test**: improve test-npm-install (Santiago Gimeno) [#5613](https://github.com/nodejs/node/pull/5613)
- [[`7520100e8b`](https://github.com/nodejs/node/commit/7520100e8b)] - **test**: add test-npm-install to parallel tests suite (Myles Borins) [#5166](https://github.com/nodejs/node/pull/5166)
- [[`b258dddb8c`](https://github.com/nodejs/node/commit/b258dddb8c)] - **test**: repl tab completion test (Santiago Gimeno) [#5534](https://github.com/nodejs/node/pull/5534)
- [[`f209effe8b`](https://github.com/nodejs/node/commit/f209effe8b)] - **test**: remove timer from test-http-1.0 (Santiago Gimeno) [#5129](https://github.com/nodejs/node/pull/5129)
- [[`3a901b0e3e`](https://github.com/nodejs/node/commit/3a901b0e3e)] - **tools**: remove unused imports (Sakthipriyan Vairamani) [#5765](https://github.com/nodejs/node/pull/5765)

Windows 32-bit Installer: https://nodejs.org/dist/v4.4.2/node-v4.4.2-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v4.4.2/node-v4.4.2-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v4.4.2/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v4.4.2/win-x64/node.exe \
Mac OS X 64-bit Installer: https://nodejs.org/dist/v4.4.2/node-v4.4.2.pkg \
Mac OS X 64-bit Binary: https://nodejs.org/dist/v4.4.2/node-v4.4.2-darwin-x64.tar.gz \
Linux 32-bit Binary: https://nodejs.org/dist/v4.4.2/node-v4.4.2-linux-x86.tar.xz \
Linux 64-bit Binary: https://nodejs.org/dist/v4.4.2/node-v4.4.2-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v4.4.2/node-v4.4.2-linux-ppc64le.tar.xz \
SmartOS 32-bit Binary: https://nodejs.org/dist/v4.4.2/node-v4.4.2-sunos-x86.tar.xz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v4.4.2/node-v4.4.2-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v4.4.2/node-v4.4.2-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v4.4.2/node-v4.4.2-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v4.4.2/node-v4.4.2-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v4.4.2/node-v4.4.2.tar.gz \
Other release files: https://nodejs.org/dist/v4.4.2/ \
Documentation: https://nodejs.org/docs/v4.4.2/api/

Shasums (GPG signing hash: SHA512, file hash: SHA256):

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA1

a143d6c71c9f1d1827d4cde1086611fb41d4909247377b8539a7129e1bf8266b  node-v4.4.2-darwin-x64.tar.gz
2407261b3829b83b06ad285af1aaa5198b9c69f6efb93ca493ffb42344f219fe  node-v4.4.2-darwin-x64.tar.xz
6b2ec4064900a8c39357ca7bdd7863587d749dcad1e4735df0de402400142cbb  node-v4.4.2-headers.tar.gz
8dc001e4b78073c567eb4c345a5d50bb3d9f9dfe6a4b5f99ba4ca9cadd0a7e52  node-v4.4.2-headers.tar.xz
be881df65ff29ffbec47a14e082800c150d4a9238d1c137ff18cf7c28fafa987  node-v4.4.2-linux-arm64.tar.gz
a15d77d084b954c96d0fbb12e97afc988bfd1f9c0e386ad52e0805f6422db783  node-v4.4.2-linux-arm64.tar.xz
c574a381e5955c724bf39ff93814882e8f5218293b8c4210ccfafbda06fc4200  node-v4.4.2-linux-armv6l.tar.gz
d139f9ba6c18a55a9549b75d5b384ee4a7e7b379bf36e8077d33e27d5ee6e7fd  node-v4.4.2-linux-armv6l.tar.xz
f243f42bbab4757b3881a26e7ac3af4a1ec6d0f5edc1e4415e69768e94cd1389  node-v4.4.2-linux-armv7l.tar.gz
03bcb820c7f1fea145675a9b632e35b58211cb3dae202f9347eae007386ca1e7  node-v4.4.2-linux-armv7l.tar.xz
035d202d02ac965618d99ec5c8680aba1ea41f6114caf720a160fe23405809b5  node-v4.4.2-linux-ppc64le.tar.gz
cd6e96898fcc1b8e98177d6de3e58904431db5c9ca75a68b08558d5c8f166ab9  node-v4.4.2-linux-ppc64le.tar.xz
b4a44dbe528520397621aad76168bdfd50cdb96fb1f15e99358263f6400c33d2  node-v4.4.2-linux-x64.tar.gz
003a8dcb3c267b9f268e9443ee2ae381bceaebee1cb438688cd52122591c9b56  node-v4.4.2-linux-x64.tar.xz
0c2bdf6fed204c6b9bddd7f839f49010cf9b484986f820e20d4f7abb01d8ee9d  node-v4.4.2-linux-x86.tar.gz
3787dc1a9dd6f65a32f9cc435c6c7d04bc3580ac03ce38246734bfdcdef94ea9  node-v4.4.2-linux-x86.tar.xz
5d115f4d561f92c6b26f40e7d0217e99e4255e2051337bca15489c39597d05ba  node-v4.4.2.pkg
006ef87af910bb5b0d78d65d2b979125a6b1049d56dc26088c9cb9deb6a16986  node-v4.4.2-sunos-x64.tar.gz
c66c35ea9b3594475eac67b213bb87f3ac867a953b5202c70433bc318de1369d  node-v4.4.2-sunos-x64.tar.xz
13da507b8ecd88598080c63a63c77efc83e04c8454f4070389c4472d512ced48  node-v4.4.2-sunos-x86.tar.gz
6e9e382fca253b6032881cad42a7e7380f7efd7a169b4df56aba990613a6404f  node-v4.4.2-sunos-x86.tar.xz
ae91cb0a31f87b5b13bb2665178845d99abf1037dc3636fa88f442b4a4c65297  node-v4.4.2.tar.gz
49051fd930e36181509dc8fcf584fc01971083b6bf9e14f2bbec49cc9c4224e9  node-v4.4.2.tar.xz
70bab3f8412f341d8ed2f2fd98904270aba650262f947084dbc5b1e7e6d98692  node-v4.4.2-x64.msi
3f16ed3b83d893b8d594e252fbe2512806141d078afd66440afd63cd00f562f8  node-v4.4.2-x86.msi
133e73a2b7a231e78cfa5802ae301de64e9b1bcfbe7e46e49a930b6b6f08c222  win-x64/node.exe
c258cd572a3120e940671c4728041bc1e34fe1e2287e73c358dccb41f959191b  win-x64/node.lib
79f633a85f6c06b923b2283d7f6b8a869d4cf0f66ef95464c19e67d575d204bc  win-x86/node.exe
b2a4e0057b5bca1a5b089d1faa92f23718359442f1bd072dac2c9d7b0bd71199  win-x86/node.lib
-----BEGIN PGP SIGNATURE-----

iQEcBAEBAgAGBQJW/dEwAAoJEJM7AfQLXKlGMW8H/Aj8PhN/66SYqG3LH2UCffmh
rlsc9R265TU5m9WJA6AuXOa5cY5rIdWiucp9wzc5nX/y8imSmy7bCNui2Ar0McDN
VKIlzcUhsa4TG0b1LihF5NUbNDwpFjandbMVHMPoo4KPYYd5IbxEIDjGj6ysktRl
D7w/cUXihLTWm8Ucda1IKGBDt28e4zSSz1bYUBPcw0AKc9KJzTWth0ikqRWLNxXB
aSJWflrDatBvghtTOtXNrGmnlcn2dLDOD44a9gqTkFBdQg1CfHchFCHpG7+TIAHZ
kUMFGcEvH3tj27YFn7iyfUIlGUezwI+fuS52HN6BU3alO0JWCmQmGNr/Z6uL4Ew=
=+gcT
-----END PGP SIGNATURE-----

```
