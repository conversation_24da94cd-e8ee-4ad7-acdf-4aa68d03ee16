---
date: '2020-04-08T19:27:15.304Z'
category: release
title: Node v12.16.2 (LTS)
layout: blog-post
author: <PERSON>
---

This release includes macOS package notarization and a change in builder configuration.

The macOS binaries for this release, and future 12.x releases, are now being compiled on macOS 10.15 (Catalina) with Xcode 11 to support package notarization, a requirement for installing .pkg files on macOS 10.15 and later. Previous builds of Node.js 12.x were compiled on macOS 10.11 (El Capitan) with a minimum deployment target of macOS 10.10 (Yosemite). As binaries are still being compiled to support a minimum of macOS 10.10 (Yosemite) we do not anticipate this having a negative impact on Node.js 12.x users with older versions of macOS.

### Notable Changes

- **doc**:
  - add ronag to collaborators (<PERSON>) [#31498](https://github.com/nodejs/node/pull/31498)
  - add <PERSON><PERSON><PERSON><PERSON> to collaborators (<PERSON>) [#31306](https://github.com/nodejs/node/pull/31306)
- **deps**:
  - upgrade npm to 6.13.6 (<PERSON><PERSON>) [#31304](https://github.com/nodejs/node/pull/31304)
  - update openssl to 1.1.1e (<PERSON>saan <PERSON>) [#32328](https://github.com/nodejs/node/pull/32328)

### Commits

- [[`f756c809e0`](https://github.com/nodejs/node/commit/f756c809e0)] - **assert**: align character indicators properly (Ruben Bridgewater) [#31429](https://github.com/nodejs/node/pull/31429)
- [[`597431b1f2`](https://github.com/nodejs/node/commit/597431b1f2)] - **async_hooks**: remove internal only error checking (Anatoli Papirovski) [#30967](https://github.com/nodejs/node/pull/30967)
- [[`35f107da53`](https://github.com/nodejs/node/commit/35f107da53)] - **benchmark**: remove problematic tls params (Brian White) [#31816](https://github.com/nodejs/node/pull/31816)
- [[`0b7579022c`](https://github.com/nodejs/node/commit/0b7579022c)] - **benchmark**: use let instead of var (Daniele Belardi) [#31592](https://github.com/nodejs/node/pull/31592)
- [[`7173b285e7`](https://github.com/nodejs/node/commit/7173b285e7)] - **benchmark**: swap var for let in benchmarks (Alex Ramirez) [#28958](https://github.com/nodejs/node/pull/28958)
- [[`c18cec7593`](https://github.com/nodejs/node/commit/c18cec7593)] - **benchmark**: remove special test entries (Ruben Bridgewater) [#31396](https://github.com/nodejs/node/pull/31396)
- [[`19fbe55451`](https://github.com/nodejs/node/commit/19fbe55451)] - **benchmark**: refactor helper into a class (Ruben Bridgewater) [#31396](https://github.com/nodejs/node/pull/31396)
- [[`a305ae2308`](https://github.com/nodejs/node/commit/a305ae2308)] - **benchmark**: check for and fix multiple end() (Brian White) [#31624](https://github.com/nodejs/node/pull/31624)
- [[`7f828a4dd0`](https://github.com/nodejs/node/commit/7f828a4dd0)] - **benchmark**: clean up config resolution in multiple benchmarks (Denys Otrishko) [#31581](https://github.com/nodejs/node/pull/31581)
- [[`4e40c77dc7`](https://github.com/nodejs/node/commit/4e40c77dc7)] - **benchmark**: add MessagePort benchmark (Anna Henningsen) [#31568](https://github.com/nodejs/node/pull/31568)
- [[`2973c1fabf`](https://github.com/nodejs/node/commit/2973c1fabf)] - **benchmark**: use let and const instead of var (Daniele Belardi) [#31518](https://github.com/nodejs/node/pull/31518)
- [[`393b48e744`](https://github.com/nodejs/node/commit/393b48e744)] - **benchmark**: fix getStringWidth() benchmark (Rich Trott) [#31476](https://github.com/nodejs/node/pull/31476)
- [[`267a01f4eb`](https://github.com/nodejs/node/commit/267a01f4eb)] - **benchmark**: add default type in getstringwidth.js (Rich Trott) [#31377](https://github.com/nodejs/node/pull/31377)
- [[`4f9d57d01e`](https://github.com/nodejs/node/commit/4f9d57d01e)] - **benchmark**: benchmarking impacts of async hooks on promises (legendecas) [#31188](https://github.com/nodejs/node/pull/31188)
- [[`06bc2ded77`](https://github.com/nodejs/node/commit/06bc2ded77)] - **buffer**: improve from() performance (Brian White) [#31615](https://github.com/nodejs/node/pull/31615)
- [[`22a37d6847`](https://github.com/nodejs/node/commit/22a37d6847)] - **buffer**: improve concat() performance (Brian White) [#31522](https://github.com/nodejs/node/pull/31522)
- [[`1849c2cc99`](https://github.com/nodejs/node/commit/1849c2cc99)] - **buffer**: improve fill(number) performance (Brian White) [#31489](https://github.com/nodejs/node/pull/31489)
- [[`45d05e1cf6`](https://github.com/nodejs/node/commit/45d05e1cf6)] - **build**: add mjs extension to lint-js (Nick Schonning) [#32145](https://github.com/nodejs/node/pull/32145)
- [[`fae680f740`](https://github.com/nodejs/node/commit/fae680f740)] - **build**: drop Travis in favor of Actions (Christian Clauss) [#32450](https://github.com/nodejs/node/pull/32450)
- [[`a50648975d`](https://github.com/nodejs/node/commit/a50648975d)] - **build**: annotate markdown lint failures in pull requests (Richard Lau) [#32391](https://github.com/nodejs/node/pull/32391)
- [[`c42cb79bb7`](https://github.com/nodejs/node/commit/c42cb79bb7)] - **build**: build docs in GitHub Actions CI workflow (Richard Lau) [#31504](https://github.com/nodejs/node/pull/31504)
- [[`46f83df2fd`](https://github.com/nodejs/node/commit/46f83df2fd)] - **build**: do not use setup-node in build workflows (Richard Lau) [#31349](https://github.com/nodejs/node/pull/31349)
- [[`ad12c82e37`](https://github.com/nodejs/node/commit/ad12c82e37)] - **build**: fix macos runner type in GitHub Action (扩散性百万甜面包) [#31327](https://github.com/nodejs/node/pull/31327)
- [[`df701f3b12`](https://github.com/nodejs/node/commit/df701f3b12)] - **build**: fix step name in GitHub Actions workflow (Richard Lau) [#31323](https://github.com/nodejs/node/pull/31323)
- [[`d190ee06b4`](https://github.com/nodejs/node/commit/d190ee06b4)] - **build**: add GitHub actions to run linters (Richard Lau) [#31323](https://github.com/nodejs/node/pull/31323)
- [[`7d3910d078`](https://github.com/nodejs/node/commit/7d3910d078)] - **build**: macOS package notarization (Rod Vagg) [#31459](https://github.com/nodejs/node/pull/31459)
- [[`36ae81a52a`](https://github.com/nodejs/node/commit/36ae81a52a)] - **build**: allow use of system-installed brotli (André Draszik) [#32046](https://github.com/nodejs/node/pull/32046)
- [[`6605bba0b8`](https://github.com/nodejs/node/commit/6605bba0b8)] - **build**: allow passing multiple libs to pkg_config (André Draszik) [#32046](https://github.com/nodejs/node/pull/32046)
- [[`8a0b0a76c0`](https://github.com/nodejs/node/commit/8a0b0a76c0)] - **build**: enable backtrace when V8 is built for PPC and S390x (Michaël Zasso) [#32113](https://github.com/nodejs/node/pull/32113)
- [[`4dddb56178`](https://github.com/nodejs/node/commit/4dddb56178)] - **build**: only lint markdown files that have changed (POSIX-only) (Rich Trott) [#31923](https://github.com/nodejs/node/pull/31923)
- [[`4f36bf78ea`](https://github.com/nodejs/node/commit/4f36bf78ea)] - **build**: add configure option to debug only Node.js part of the binary (Anna Henningsen) [#31644](https://github.com/nodejs/node/pull/31644)
- [[`a53500e26b`](https://github.com/nodejs/node/commit/a53500e26b)] - **build**: ignore all the "Debug","Release" folders (ConorDavenport) [#31565](https://github.com/nodejs/node/pull/31565)
- [[`038fd25ef8`](https://github.com/nodejs/node/commit/038fd25ef8)] - **build**: fix zlib tarball generation (Shelley Vohr) [#32094](https://github.com/nodejs/node/pull/32094)
- [[`e79f368783`](https://github.com/nodejs/node/commit/e79f368783)] - **build**: remove enable_vtune from vcbuild.bat (Richard Lau) [#31338](https://github.com/nodejs/node/pull/31338)
- [[`8296224e41`](https://github.com/nodejs/node/commit/8296224e41)] - **build**: add vs2019 to vcbuild.bat help (Richard Lau) [#31338](https://github.com/nodejs/node/pull/31338)
- [[`93a7f91b52`](https://github.com/nodejs/node/commit/93a7f91b52)] - **build**: silence OpenSSL Windows compiler warnings (Richard Lau) [#31311](https://github.com/nodejs/node/pull/31311)
- [[`a89893f3df`](https://github.com/nodejs/node/commit/a89893f3df)] - **build**: silence c-ares Windows compiler warnings (Richard Lau) [#31311](https://github.com/nodejs/node/pull/31311)
- [[`f6a6638d0c`](https://github.com/nodejs/node/commit/f6a6638d0c)] - **build**: test Python 3 using GitHub Actions-based CI (Christian Clauss) [#29474](https://github.com/nodejs/node/pull/29474)
- [[`aec22268af`](https://github.com/nodejs/node/commit/aec22268af)] - **cli**: allow --jitless V8 flag in NODE_OPTIONS (Andrew Neitsch) [#32100](https://github.com/nodejs/node/pull/32100)
- [[`70dc1cefea`](https://github.com/nodejs/node/commit/70dc1cefea)] - **cli**: --perf-prof only works on Linux (Shelley Vohr) [#31892](https://github.com/nodejs/node/pull/31892)
- [[`f9113fd7c2`](https://github.com/nodejs/node/commit/f9113fd7c2)] - **crypto**: turn impossible DH errors into assertions (Tobias Nießen) [#31934](https://github.com/nodejs/node/pull/31934)
- [[`c6bbae44a9`](https://github.com/nodejs/node/commit/c6bbae44a9)] - **crypto**: fix ieee-p1363 for createVerify (Tobias Nießen) [#31876](https://github.com/nodejs/node/pull/31876)
- [[`b84fd947d2`](https://github.com/nodejs/node/commit/b84fd947d2)] - **crypto**: fix performance regression (Robert Nagy) [#31742](https://github.com/nodejs/node/pull/31742)
- [[`9a3760d2fa`](https://github.com/nodejs/node/commit/9a3760d2fa)] - **crypto**: improve randomBytes() performance (Brian White) [#31519](https://github.com/nodejs/node/pull/31519)
- [[`a1be32752c`](https://github.com/nodejs/node/commit/a1be32752c)] - **deps**: V8: backport 07ee86a5a28b (Milad Farazmand) [#32619](https://github.com/nodejs/node/pull/32619)
- [[`a83fc49717`](https://github.com/nodejs/node/commit/a83fc49717)] - **deps**: V8: cherry-pick cb1c2b0fbfe7 (Gerhard Stoebich) [#31939](https://github.com/nodejs/node/pull/31939)
- [[`784df12494`](https://github.com/nodejs/node/commit/784df12494)] - **deps**: revert whitespace changes on V8 (Matheus Marchini) [#32605](https://github.com/nodejs/node/pull/32605)
- [[`6db190bb1c`](https://github.com/nodejs/node/commit/6db190bb1c)] - **deps**: upgrade npm to 6.14.4 (Ruy Adorno) [#32495](https://github.com/nodejs/node/pull/32495)
- [[`652ffa5470`](https://github.com/nodejs/node/commit/652ffa5470)] - **deps**: update term-size with signed version (Rod Vagg) [#31459](https://github.com/nodejs/node/pull/31459)
- [[`f55d071bfd`](https://github.com/nodejs/node/commit/f55d071bfd)] - **deps**: remove \*.pyc files from deps/npm (Ben Noordhuis) [#32387](https://github.com/nodejs/node/pull/32387)
- [[`c419cd21e3`](https://github.com/nodejs/node/commit/c419cd21e3)] - **deps**: update npm to 6.14.3 (Myles Borins) [#32368](https://github.com/nodejs/node/pull/32368)
- [[`17217a5275`](https://github.com/nodejs/node/commit/17217a5275)] - **deps**: upgrade npm to 6.14.1 (Isaac Z. Schlueter) [#31977](https://github.com/nodejs/node/pull/31977)
- [[`260bd810e9`](https://github.com/nodejs/node/commit/260bd810e9)] - **deps**: update archs files for OpenSSL-1.1.1e (Hassaan Pasha) [#32328](https://github.com/nodejs/node/pull/32328)
- [[`e96e8afead`](https://github.com/nodejs/node/commit/e96e8afead)] - **deps**: adjust openssl configuration for 1.1.1e (Hassaan Pasha) [#32328](https://github.com/nodejs/node/pull/32328)
- [[`4de1afd32d`](https://github.com/nodejs/node/commit/4de1afd32d)] - **deps**: upgrade openssl sources to 1.1.1e (Hassaan Pasha) [#32328](https://github.com/nodejs/node/pull/32328)
- [[`e2c40ccb78`](https://github.com/nodejs/node/commit/e2c40ccb78)] - **deps**: V8: backport f7771e5b0cc4 (Matheus Marchini) [#31957](https://github.com/nodejs/node/pull/31957)
- [[`78d9a50c83`](https://github.com/nodejs/node/commit/78d9a50c83)] - **deps**: openssl: cherry-pick 4dcb150ea30f (Adam Majer) [#32002](https://github.com/nodejs/node/pull/32002)
- [[`ff1e5e01f4`](https://github.com/nodejs/node/commit/ff1e5e01f4)] - **deps**: upgrade npm to 6.13.7 (Michael Perrotte) [#31558](https://github.com/nodejs/node/pull/31558)
- [[`48e4d45cca`](https://github.com/nodejs/node/commit/48e4d45cca)] - **deps**: uvwasi: cherry-pick 7b5b6f9 (Colin Ihrig) [#31495](https://github.com/nodejs/node/pull/31495)
- [[`604ce0aa5a`](https://github.com/nodejs/node/commit/604ce0aa5a)] - **deps**: upgrade to libuv 1.34.2 (Colin Ihrig) [#31477](https://github.com/nodejs/node/pull/31477)
- [[`3fb3079337`](https://github.com/nodejs/node/commit/3fb3079337)] - **deps**: uvwasi: cherry-pick eea4508 (Colin Ihrig) [#31432](https://github.com/nodejs/node/pull/31432)
- [[`3bd1c02941`](https://github.com/nodejs/node/commit/3bd1c02941)] - **deps**: uvwasi: cherry-pick c3bef8e (Colin Ihrig) [#31432](https://github.com/nodejs/node/pull/31432)
- [[`12949019de`](https://github.com/nodejs/node/commit/12949019de)] - **deps**: uvwasi: cherry-pick ea73af5 (Colin Ihrig) [#31432](https://github.com/nodejs/node/pull/31432)
- [[`ada070eed4`](https://github.com/nodejs/node/commit/ada070eed4)] - **deps**: update to uvwasi 0.0.5 (Colin Ihrig) [#31432](https://github.com/nodejs/node/pull/31432)
- [[`8cf2666248`](https://github.com/nodejs/node/commit/8cf2666248)] - **deps**: uvwasi: cherry-pick 941bedf (Colin Ihrig) [#31363](https://github.com/nodejs/node/pull/31363)
- [[`ef5517aa0c`](https://github.com/nodejs/node/commit/ef5517aa0c)] - **deps**: port uvwasi@676ba9a to gyp (Colin Ihrig) [#31363](https://github.com/nodejs/node/pull/31363)
- [[`bbb8ae7bd0`](https://github.com/nodejs/node/commit/bbb8ae7bd0)] - **deps**: upgrade to libuv 1.34.1 (Colin Ihrig) [#31332](https://github.com/nodejs/node/pull/31332)
- [[`7a8963bc75`](https://github.com/nodejs/node/commit/7a8963bc75)] - **deps**: upgrade npm to 6.13.6 (Ruy Adorno) [#31304](https://github.com/nodejs/node/pull/31304)
- [[`676e3c3c38`](https://github.com/nodejs/node/commit/676e3c3c38)] - **deps,test**: update to uvwasi 0.0.4 (Colin Ihrig) [#31363](https://github.com/nodejs/node/pull/31363)
- [[`e88960d1f4`](https://github.com/nodejs/node/commit/e88960d1f4)] - **doc**: esm spec bug (Myles Borins) [#32610](https://github.com/nodejs/node/pull/32610)
- [[`155a6c397d`](https://github.com/nodejs/node/commit/155a6c397d)] - **doc**: update conditional exports recommendations (Guy Bedford) [#32098](https://github.com/nodejs/node/pull/32098)
- [[`7e56e3dee3`](https://github.com/nodejs/node/commit/7e56e3dee3)] - **doc**: remove unnecessary "obvious(ly)" modifiers in esm.md (Rich Trott) [#32457](https://github.com/nodejs/node/pull/32457)
- [[`61f44c94ae`](https://github.com/nodejs/node/commit/61f44c94ae)] - **doc**: fix lint warning in doc/api/esm.md (Richard Lau) [#32462](https://github.com/nodejs/node/pull/32462)
- [[`d8e17bf12a`](https://github.com/nodejs/node/commit/d8e17bf12a)] - **doc**: improve wording in esm.md (Rich Trott) [#32427](https://github.com/nodejs/node/pull/32427)
- [[`8b961347fe`](https://github.com/nodejs/node/commit/8b961347fe)] - **doc**: import clarifications with links to MDN (Eric Dobbertin) [#31479](https://github.com/nodejs/node/pull/31479)
- [[`f58594b8ec`](https://github.com/nodejs/node/commit/f58594b8ec)] - **doc**: update releaser list in README.md (Myles Borins) [#32577](https://github.com/nodejs/node/pull/32577)
- [[`885c88ee5b`](https://github.com/nodejs/node/commit/885c88ee5b)] - **doc**: official macOS builds now on 10.15 + Xcode 11 (Rod Vagg) [#31459](https://github.com/nodejs/node/pull/31459)
- [[`efd20f08e8`](https://github.com/nodejs/node/commit/efd20f08e8)] - **doc**: link setRawMode() from signal docs (Anna Henningsen) [#32088](https://github.com/nodejs/node/pull/32088)
- [[`3f1b9162f6`](https://github.com/nodejs/node/commit/3f1b9162f6)] - **doc**: document self-referencing a package name (Gil Tayar) [#31680](https://github.com/nodejs/node/pull/31680)
- [[`f820ce6e50`](https://github.com/nodejs/node/commit/f820ce6e50)] - **doc**: add AsyncResource + Worker pool example (Anna Henningsen) [#31601](https://github.com/nodejs/node/pull/31601)
- [[`df8d51b411`](https://github.com/nodejs/node/commit/df8d51b411)] - **doc**: document fs.watchFile() bigint option (Colin Ihrig) [#32128](https://github.com/nodejs/node/pull/32128)
- [[`eaf615709f`](https://github.com/nodejs/node/commit/eaf615709f)] - **doc**: fix broken links in benchmark README (Rich Trott) [#32121](https://github.com/nodejs/node/pull/32121)
- [[`047bd9d38e`](https://github.com/nodejs/node/commit/047bd9d38e)] - **doc**: update email address in authors (Yael Hermon) [#32026](https://github.com/nodejs/node/pull/32026)
- [[`c20f2cd41d`](https://github.com/nodejs/node/commit/c20f2cd41d)] - **doc**: update maintaining-V8.md (kenzo-spaulding) [#31503](https://github.com/nodejs/node/pull/31503)
- [[`05fbc80f45`](https://github.com/nodejs/node/commit/05fbc80f45)] - **doc**: visibility of Worker threads cli options (Harshitha KP) [#31380](https://github.com/nodejs/node/pull/31380)
- [[`224a17e202`](https://github.com/nodejs/node/commit/224a17e202)] - **doc**: improve doc/markdown file organization coherence (ConorDavenport) [#31792](https://github.com/nodejs/node/pull/31792)
- [[`df54a1932a`](https://github.com/nodejs/node/commit/df54a1932a)] - **doc**: revise --zero-fill-buffers text in buffer.md (Rich Trott) [#32019](https://github.com/nodejs/node/pull/32019)
- [[`9161b7e5c3`](https://github.com/nodejs/node/commit/9161b7e5c3)] - **doc**: add link to sem-ver info (unknown) [#31985](https://github.com/nodejs/node/pull/31985)
- [[`1630320781`](https://github.com/nodejs/node/commit/1630320781)] - **doc**: update zlib doc (James M Snell) [#31665](https://github.com/nodejs/node/pull/31665)
- [[`a1c0d467ef`](https://github.com/nodejs/node/commit/a1c0d467ef)] - **doc**: clarify http2.connect authority details (James M Snell) [#31828](https://github.com/nodejs/node/pull/31828)
- [[`ed86854025`](https://github.com/nodejs/node/commit/ed86854025)] - **doc**: updated YAML version representation in readline.md (Rich Trott) [#31924](https://github.com/nodejs/node/pull/31924)
- [[`370653f1a0`](https://github.com/nodejs/node/commit/370653f1a0)] - **doc**: update releases guide re pushing tags (Myles Borins) [#31855](https://github.com/nodejs/node/pull/31855)
- [[`ab735d0144`](https://github.com/nodejs/node/commit/ab735d0144)] - **doc**: update assert.rejects() docs with a validation function example (Eric Eastwood) [#31271](https://github.com/nodejs/node/pull/31271)
- [[`911dfc5099`](https://github.com/nodejs/node/commit/911dfc5099)] - **doc**: add note about ssh key to releases (Shelley Vohr) [#31856](https://github.com/nodejs/node/pull/31856)
- [[`e83af20b70`](https://github.com/nodejs/node/commit/e83af20b70)] - **doc**: add note in BUILDING.md about running `make distclean` (Swagat Konchada) [#31542](https://github.com/nodejs/node/pull/31542)
- [[`17882ac83d`](https://github.com/nodejs/node/commit/17882ac83d)] - **doc**: reword possessive form of Node.js in adding-new-napi-api.md (Rich Trott) [#31748](https://github.com/nodejs/node/pull/31748)
- [[`648f83135e`](https://github.com/nodejs/node/commit/648f83135e)] - **doc**: reword possessive form of Node.js in http.md (Rich Trott) [#31748](https://github.com/nodejs/node/pull/31748)
- [[`d034eb41f2`](https://github.com/nodejs/node/commit/d034eb41f2)] - **doc**: reword possessive form of Node.js in process.md (Rich Trott) [#31748](https://github.com/nodejs/node/pull/31748)
- [[`b8d2997950`](https://github.com/nodejs/node/commit/b8d2997950)] - **doc**: reword possessive form of Node.js in debugger.md (Rich Trott) [#31748](https://github.com/nodejs/node/pull/31748)
- [[`aebaeadf05`](https://github.com/nodejs/node/commit/aebaeadf05)] - **doc**: move gireeshpunathil to TSC emeritus (Gireesh Punathil) [#31770](https://github.com/nodejs/node/pull/31770)
- [[`88a6d9b077`](https://github.com/nodejs/node/commit/88a6d9b077)] - **doc**: pronouns for @Fishrock123 (Jeremiah Senkpiel) [#31725](https://github.com/nodejs/node/pull/31725)
- [[`b3d0795a4a`](https://github.com/nodejs/node/commit/b3d0795a4a)] - **doc**: move @Fishrock123 to TSC Emeriti (Jeremiah Senkpiel) [#31725](https://github.com/nodejs/node/pull/31725)
- [[`e65c1c25fc`](https://github.com/nodejs/node/commit/e65c1c25fc)] - **doc**: move @Fishrock123 to a previous releaser (Jeremiah Senkpiel) [#31725](https://github.com/nodejs/node/pull/31725)
- [[`38d3f56ea3`](https://github.com/nodejs/node/commit/38d3f56ea3)] - **doc**: fix typos in doc/api/https.md (Jeff) [#31793](https://github.com/nodejs/node/pull/31793)
- [[`de275d0e0b`](https://github.com/nodejs/node/commit/de275d0e0b)] - **doc**: guide - using valgrind to debug memory leaks (Michael Dawson) [#31501](https://github.com/nodejs/node/pull/31501)
- [[`82defc0d15`](https://github.com/nodejs/node/commit/82defc0d15)] - **doc**: add glossary.md (gengjiawen) [#27517](https://github.com/nodejs/node/pull/27517)
- [[`01ec7221e6`](https://github.com/nodejs/node/commit/01ec7221e6)] - **doc**: add prerequisites information for Arch (Ujjwal Sharma) [#31669](https://github.com/nodejs/node/pull/31669)
- [[`a7a6261fa4`](https://github.com/nodejs/node/commit/a7a6261fa4)] - **doc**: fix typo on fs docs (Juan José Arboleda) [#31620](https://github.com/nodejs/node/pull/31620)
- [[`d4c1a8cc7b`](https://github.com/nodejs/node/commit/d4c1a8cc7b)] - **doc**: update contact email for @ryzokuken (Ujjwal Sharma) [#31670](https://github.com/nodejs/node/pull/31670)
- [[`86686ccbab`](https://github.com/nodejs/node/commit/86686ccbab)] - **doc**: fix default server timeout description for https (Andrey Pechkurov) [#31692](https://github.com/nodejs/node/pull/31692)
- [[`448fe39c35`](https://github.com/nodejs/node/commit/448fe39c35)] - **doc**: add directions to mark a release line as lts (Danielle Adams) [#31724](https://github.com/nodejs/node/pull/31724)
- [[`dbe2da65c9`](https://github.com/nodejs/node/commit/dbe2da65c9)] - **doc**: expand C++ README with information about exception handling (Anna Henningsen) [#31720](https://github.com/nodejs/node/pull/31720)
- [[`236943ac5b`](https://github.com/nodejs/node/commit/236943ac5b)] - **doc**: update foundation name in onboarding (Tobias Nießen) [#31719](https://github.com/nodejs/node/pull/31719)
- [[`165047a787`](https://github.com/nodejs/node/commit/165047a787)] - **doc**: reword possessive form of Node.js in zlib.md (Rich Trott) [#31713](https://github.com/nodejs/node/pull/31713)
- [[`d3201d7933`](https://github.com/nodejs/node/commit/d3201d7933)] - **doc**: reword possessive form of Node.js in modules.md (Rich Trott) [#31713](https://github.com/nodejs/node/pull/31713)
- [[`4c65d0a3d3`](https://github.com/nodejs/node/commit/4c65d0a3d3)] - **doc**: reword possessive form of Node.js in repl.md (Rich Trott) [#31713](https://github.com/nodejs/node/pull/31713)
- [[`4c5c340d28`](https://github.com/nodejs/node/commit/4c5c340d28)] - **doc**: reword section title in addons.md (Rich Trott) [#31713](https://github.com/nodejs/node/pull/31713)
- [[`1db85aa71c`](https://github.com/nodejs/node/commit/1db85aa71c)] - **doc**: revise deepEqual() legacy assertion mode text (Rich Trott) [#31704](https://github.com/nodejs/node/pull/31704)
- [[`aadd8cac4b`](https://github.com/nodejs/node/commit/aadd8cac4b)] - **doc**: improve strict assertion mode color text (Rich Trott) [#31703](https://github.com/nodejs/node/pull/31703)
- [[`708aff953a`](https://github.com/nodejs/node/commit/708aff953a)] - **doc**: consolidate introductory text (Rich Trott) [#31667](https://github.com/nodejs/node/pull/31667)
- [[`959fa8ff9d`](https://github.com/nodejs/node/commit/959fa8ff9d)] - **doc**: simplify async_hooks overview (Rich Trott) [#31660](https://github.com/nodejs/node/pull/31660)
- [[`28657cc614`](https://github.com/nodejs/node/commit/28657cc614)] - **doc**: clarify Worker exit/message event ordering (Anna Henningsen) [#31642](https://github.com/nodejs/node/pull/31642)
- [[`cb75ca1a51`](https://github.com/nodejs/node/commit/cb75ca1a51)] - **doc**: update TSC name in "Release Process" (Tobias Nießen) [#31652](https://github.com/nodejs/node/pull/31652)
- [[`76b500218b`](https://github.com/nodejs/node/commit/76b500218b)] - **doc**: remove .github/ISSUE_TEMPLATE.md in favor of the template folder (Joyee Cheung) [#31656](https://github.com/nodejs/node/pull/31656)
- [[`55c7b9f94a`](https://github.com/nodejs/node/commit/55c7b9f94a)] - **doc**: correct getting an ArrayBuffer's length (tsabolov) [#31632](https://github.com/nodejs/node/pull/31632)
- [[`afeaec7d6f`](https://github.com/nodejs/node/commit/afeaec7d6f)] - **doc**: ask more questions in the bug report template (Joyee Cheung) [#31611](https://github.com/nodejs/node/pull/31611)
- [[`06d5a9c0a0`](https://github.com/nodejs/node/commit/06d5a9c0a0)] - **doc**: add example to fs.promises.readdir (Conor ONeill) [#31552](https://github.com/nodejs/node/pull/31552)
- [[`351c86310b`](https://github.com/nodejs/node/commit/351c86310b)] - **doc**: fix numbering (Steffen) [#31575](https://github.com/nodejs/node/pull/31575)
- [[`356e505ab0`](https://github.com/nodejs/node/commit/356e505ab0)] - **doc**: clarify socket.setNoDelay() explanation (Rusty Conover) [#31541](https://github.com/nodejs/node/pull/31541)
- [[`b2e571ea65`](https://github.com/nodejs/node/commit/b2e571ea65)] - **doc**: clarify require() OS independence (Denys Otrishko) [#31571](https://github.com/nodejs/node/pull/31571)
- [[`1759f0ab52`](https://github.com/nodejs/node/commit/1759f0ab52)] - **doc**: add protocol option in http2.connect() (Michael Lumish) [#31560](https://github.com/nodejs/node/pull/31560)
- [[`f5663d92b8`](https://github.com/nodejs/node/commit/f5663d92b8)] - **doc**: clarify that `v8.serialize()` is not deterministic (Anna Henningsen) [#31548](https://github.com/nodejs/node/pull/31548)
- [[`af61c5d1b2`](https://github.com/nodejs/node/commit/af61c5d1b2)] - **doc**: update job reference in COLLABORATOR_GUIDE.md (Richard Lau) [#31557](https://github.com/nodejs/node/pull/31557)
- [[`f4bdcf86ce`](https://github.com/nodejs/node/commit/f4bdcf86ce)] - **doc**: simultaneous blog and email of sec announce (Sam Roberts) [#31483](https://github.com/nodejs/node/pull/31483)
- [[`5286ccc1dc`](https://github.com/nodejs/node/commit/5286ccc1dc)] - **doc**: update collaborator guide citgm instructions (Robert Nagy) [#31549](https://github.com/nodejs/node/pull/31549)
- [[`1cf450c51f`](https://github.com/nodejs/node/commit/1cf450c51f)] - **doc**: change error message testing policy (Tobias Nießen) [#31421](https://github.com/nodejs/node/pull/31421)
- [[`d978bb56dd`](https://github.com/nodejs/node/commit/d978bb56dd)] - **doc**: remove redundant properties from headers (XhmikosR) [#31492](https://github.com/nodejs/node/pull/31492)
- [[`e48f874afd`](https://github.com/nodejs/node/commit/e48f874afd)] - **doc**: enable visual code indication in headers (Rich Trott) [#31493](https://github.com/nodejs/node/pull/31493)
- [[`8c78b87d97`](https://github.com/nodejs/node/commit/8c78b87d97)] - **doc**: clean up and streamline vm.md examples (Denys Otrishko) [#31474](https://github.com/nodejs/node/pull/31474)
- [[`821b9ac007`](https://github.com/nodejs/node/commit/821b9ac007)] - **doc**: further fix async iterator example (Robert Nagy) [#31367](https://github.com/nodejs/node/pull/31367)
- [[`f0b5f9fb94`](https://github.com/nodejs/node/commit/f0b5f9fb94)] - **doc**: add ronag to collaborators (Robert Nagy) [#31498](https://github.com/nodejs/node/pull/31498)
- [[`37754bab2d`](https://github.com/nodejs/node/commit/37754bab2d)] - **doc**: fix code display in header glitch (Rich Trott) [#31460](https://github.com/nodejs/node/pull/31460)
- [[`40480e0c0d`](https://github.com/nodejs/node/commit/40480e0c0d)] - **doc**: fix syntax in N-API documentation (Tobias Nießen) [#31466](https://github.com/nodejs/node/pull/31466)
- [[`11dbdcb839`](https://github.com/nodejs/node/commit/11dbdcb839)] - **doc**: add explanatory to path.resolve description (Yakov Litvin) [#31430](https://github.com/nodejs/node/pull/31430)
- [[`5e8f8b8320`](https://github.com/nodejs/node/commit/5e8f8b8320)] - **doc**: document process.std\*.fd (Harshitha KP) [#31395](https://github.com/nodejs/node/pull/31395)
- [[`c7f03ad8ca`](https://github.com/nodejs/node/commit/c7f03ad8ca)] - **doc**: fix several child_process doc typos (Colin Ihrig) [#31393](https://github.com/nodejs/node/pull/31393)
- [[`2d9f111011`](https://github.com/nodejs/node/commit/2d9f111011)] - **doc**: correct added version for --abort-on-uncaught-exception (Anna Henningsen) [#31360](https://github.com/nodejs/node/pull/31360)
- [[`d944fa71dd`](https://github.com/nodejs/node/commit/d944fa71dd)] - **doc**: explain `hex` encoding in Buffer API (Harshitha KP) [#31352](https://github.com/nodejs/node/pull/31352)
- [[`ff8f0bc3cc`](https://github.com/nodejs/node/commit/ff8f0bc3cc)] - **doc**: explain \_writev() API (Harshitha KP) [#31356](https://github.com/nodejs/node/pull/31356)
- [[`b4d15a9adc`](https://github.com/nodejs/node/commit/b4d15a9adc)] - **doc**: document missing properties in child_process (Harshitha KP) [#31342](https://github.com/nodejs/node/pull/31342)
- [[`9aa4fcc052`](https://github.com/nodejs/node/commit/9aa4fcc052)] - **doc**: standardize on "host name" in deprecations.md (Rich Trott) [#31326](https://github.com/nodejs/node/pull/31326)
- [[`175a5ec795`](https://github.com/nodejs/node/commit/175a5ec795)] - **doc**: standardize on "host name" in url.md (Rich Trott) [#31326](https://github.com/nodejs/node/pull/31326)
- [[`5f45eaf390`](https://github.com/nodejs/node/commit/5f45eaf390)] - **doc**: standardize on "host name" in tls.md (Rich Trott) [#31326](https://github.com/nodejs/node/pull/31326)
- [[`54b5346392`](https://github.com/nodejs/node/commit/54b5346392)] - **doc**: standardize on "host name" in os.md (Rich Trott) [#31326](https://github.com/nodejs/node/pull/31326)
- [[`ac3d0c90f5`](https://github.com/nodejs/node/commit/ac3d0c90f5)] - **doc**: standardize on "host name" in net.md (Rich Trott) [#31326](https://github.com/nodejs/node/pull/31326)
- [[`9217b7a639`](https://github.com/nodejs/node/commit/9217b7a639)] - **doc**: standardize on "host name" in https.md (Rich Trott) [#31326](https://github.com/nodejs/node/pull/31326)
- [[`9bca4514bf`](https://github.com/nodejs/node/commit/9bca4514bf)] - **doc**: standardize on "host name" in http2.md (Rich Trott) [#31326](https://github.com/nodejs/node/pull/31326)
- [[`a419698b18`](https://github.com/nodejs/node/commit/a419698b18)] - **doc**: standardize on "host name" in fs.md (Rich Trott) [#31326](https://github.com/nodejs/node/pull/31326)
- [[`d4a0300424`](https://github.com/nodejs/node/commit/d4a0300424)] - **doc**: standardize on "host name" in errors.md (Rich Trott) [#31326](https://github.com/nodejs/node/pull/31326)
- [[`ad701329d6`](https://github.com/nodejs/node/commit/ad701329d6)] - **doc**: standardize on "host name" in dgram.md (Rich Trott) [#31326](https://github.com/nodejs/node/pull/31326)
- [[`0eba07b267`](https://github.com/nodejs/node/commit/0eba07b267)] - **doc**: standardize on "host name" in async_hooks.md (Rich Trott) [#31326](https://github.com/nodejs/node/pull/31326)
- [[`52a4a44b76`](https://github.com/nodejs/node/commit/52a4a44b76)] - **doc**: fix a code example in crypto.md (himself65) [#31313](https://github.com/nodejs/node/pull/31313)
- [[`6598a08308`](https://github.com/nodejs/node/commit/6598a08308)] - **doc**: add an example for util.types.isExternal (Harshitha KP) [#31173](https://github.com/nodejs/node/pull/31173)
- [[`760bedee44`](https://github.com/nodejs/node/commit/760bedee44)] - **doc**: fix example of parsing request.url (Egor Pavlov) [#31302](https://github.com/nodejs/node/pull/31302)
- [[`fa0762d663`](https://github.com/nodejs/node/commit/fa0762d663)] - **doc**: improve doc v8.getHeapSpaceStatistics() 'GetHeapSpaceStatistics' (dev-313) [#31274](https://github.com/nodejs/node/pull/31274)
- [[`cb40a1a90f`](https://github.com/nodejs/node/commit/cb40a1a90f)] - **doc**: update README to make Node.js description clearer (carterbancroft) [#31266](https://github.com/nodejs/node/pull/31266)
- [[`dd9a6c6c22`](https://github.com/nodejs/node/commit/dd9a6c6c22)] - **doc**: fix a code example in zlib.md (Alexander Wang) [#31264](https://github.com/nodejs/node/pull/31264)
- [[`97c12f120e`](https://github.com/nodejs/node/commit/97c12f120e)] - **doc**: add GeoffreyBooth to collaborators (Geoffrey Booth) [#31306](https://github.com/nodejs/node/pull/31306)
- [[`63af1ab60f`](https://github.com/nodejs/node/commit/63af1ab60f)] - **doc**: update description of `External` (Anna Henningsen) [#31255](https://github.com/nodejs/node/pull/31255)
- [[`e398137020`](https://github.com/nodejs/node/commit/e398137020)] - **doc**: rename iterator to iterable in examples (Robert Nagy) [#31252](https://github.com/nodejs/node/pull/31252)
- [[`4922184310`](https://github.com/nodejs/node/commit/4922184310)] - **doc**: fix stream async iterator sample (Robert Nagy) [#31252](https://github.com/nodejs/node/pull/31252)
- [[`623e1118a0`](https://github.com/nodejs/node/commit/623e1118a0)] - **doc**: correct filehandle.\[read|write|append\]File() (Bryan English) [#31235](https://github.com/nodejs/node/pull/31235)
- [[`60e35d454c`](https://github.com/nodejs/node/commit/60e35d454c)] - **doc**: prefer server vs srv and client vs clt (Andrew Hughes) [#31224](https://github.com/nodejs/node/pull/31224)
- [[`6cfc4dcfb4`](https://github.com/nodejs/node/commit/6cfc4dcfb4)] - **doc**: explain native external types (Harshitha KP) [#31214](https://github.com/nodejs/node/pull/31214)
- [[`ebc47f8b52`](https://github.com/nodejs/node/commit/ebc47f8b52)] - **doc**: remove em dashes (Rich Trott) [#32146](https://github.com/nodejs/node/pull/32146)
- [[`db125c5618`](https://github.com/nodejs/node/commit/db125c5618)] - **doc**: fix missing changelog corrections (Myles Borins) [#31854](https://github.com/nodejs/node/pull/31854)
- [[`8f75c7497e`](https://github.com/nodejs/node/commit/8f75c7497e)] - **doc,assert**: rename "mode" to "assertion mode" (Rich Trott) [#31635](https://github.com/nodejs/node/pull/31635)
- [[`fd5aa41178`](https://github.com/nodejs/node/commit/fd5aa41178)] - **doc,crypto**: re-document oaepLabel option (Ben Noordhuis) [#31825](https://github.com/nodejs/node/pull/31825)
- [[`8f9f92fb33`](https://github.com/nodejs/node/commit/8f9f92fb33)] - **doc,net**: reword Unix domain path paragraph in net.md (Rich Trott) [#31684](https://github.com/nodejs/node/pull/31684)
- [[`073b4f2750`](https://github.com/nodejs/node/commit/073b4f2750)] - **doc,src**: clarify that one napi_env is per-module (legendecas) [#31102](https://github.com/nodejs/node/pull/31102)
- [[`844f893e4e`](https://github.com/nodejs/node/commit/844f893e4e)] - **doc,util**: revise util.md introductory paragraph (Rich Trott) [#31685](https://github.com/nodejs/node/pull/31685)
- [[`b1517a4f6c`](https://github.com/nodejs/node/commit/b1517a4f6c)] - **errors**: make use of "cannot" consistent (Tobias Nießen) [#31420](https://github.com/nodejs/node/pull/31420)
- [[`7231090a5d`](https://github.com/nodejs/node/commit/7231090a5d)] - **errors**: remove dead code in ERR_INVALID_ARG_TYPE (Gerhard Stoebich) [#31322](https://github.com/nodejs/node/pull/31322)
- [[`0e513b2ae7`](https://github.com/nodejs/node/commit/0e513b2ae7)] - **esm**: remove unused parameter on module.instantiate (himself65) [#32147](https://github.com/nodejs/node/pull/32147)
- [[`05091d48e3`](https://github.com/nodejs/node/commit/05091d48e3)] - **esm**: import.meta.resolve with nodejs: builtins (Guy Bedford) [#31032](https://github.com/nodejs/node/pull/31032)
- [[`400083b9f5`](https://github.com/nodejs/node/commit/400083b9f5)] - **events**: fix removeListener for Symbols (zfx) [#31847](https://github.com/nodejs/node/pull/31847)
- [[`de5d162c60`](https://github.com/nodejs/node/commit/de5d162c60)] - **fs**: fix valid id range on chown, lchown, fchown (himself65) [#31694](https://github.com/nodejs/node/pull/31694)
- [[`d36699662f`](https://github.com/nodejs/node/commit/d36699662f)] - **fs**: set path when mkdir recursive called on file (Benjamin Coe) [#31607](https://github.com/nodejs/node/pull/31607)
- [[`3d8e850d31`](https://github.com/nodejs/node/commit/3d8e850d31)] - **fs**: bail on permission error in recursive directory creation (Benjamin Coe) [#31505](https://github.com/nodejs/node/pull/31505)
- [[`fc9c6c3227`](https://github.com/nodejs/node/commit/fc9c6c3227)] - **fs**: do not emit 'close' twice if emitClose enabled (Robert Nagy) [#31383](https://github.com/nodejs/node/pull/31383)
- [[`ca951e182e`](https://github.com/nodejs/node/commit/ca951e182e)] - **fs**: unset FileHandle fd after close (Anna Henningsen) [#31389](https://github.com/nodejs/node/pull/31389)
- [[`1fe0065a51`](https://github.com/nodejs/node/commit/1fe0065a51)] - **fs**: add missing HandleScope to FileHandle.close (Anna Henningsen) [#31276](https://github.com/nodejs/node/pull/31276)
- [[`73c4729652`](https://github.com/nodejs/node/commit/73c4729652)] - **fs**: use async writeFile in FileHandle#appendFile (Bryan English) [#31235](https://github.com/nodejs/node/pull/31235)
- [[`4745ac4fd7`](https://github.com/nodejs/node/commit/4745ac4fd7)] - **http2**: use custom BaseObject smart pointers (Anna Henningsen) [#30374](https://github.com/nodejs/node/pull/30374)
- [[`76a0ba689a`](https://github.com/nodejs/node/commit/76a0ba689a)] - **http2**: make compat finished match http/1 (Robert Nagy) [#24347](https://github.com/nodejs/node/pull/24347)
- [[`f910f645b9`](https://github.com/nodejs/node/commit/f910f645b9)] - **http2**: skip creating native ShutdownWrap (Anna Henningsen) [#31283](https://github.com/nodejs/node/pull/31283)
- [[`d00a1b9ad2`](https://github.com/nodejs/node/commit/d00a1b9ad2)] - **lib**: replace BigInt64Array global by the primordials (Sebastien Ahkrin) [#31193](https://github.com/nodejs/node/pull/31193)
- [[`2147c29de0`](https://github.com/nodejs/node/commit/2147c29de0)] - **lib**: add Uint16Array primordials (Sebastien Ahkrin) [#31210](https://github.com/nodejs/node/pull/31210)
- [[`bc4cbe3f50`](https://github.com/nodejs/node/commit/bc4cbe3f50)] - **lib**: add RegExp primordials (Sebastien Ahkrin) [#31208](https://github.com/nodejs/node/pull/31208)
- [[`41f0fa742e`](https://github.com/nodejs/node/commit/41f0fa742e)] - **lib**: replace Float32Array global by the primordials (Sebastien Ahkrin) [#31195](https://github.com/nodejs/node/pull/31195)
- [[`68d48fead3`](https://github.com/nodejs/node/commit/68d48fead3)] - **lib**: replace BigUInt64Array global by the primordials (Sebastien Ahkrin) [#31194](https://github.com/nodejs/node/pull/31194)
- [[`a0ad12bd7d`](https://github.com/nodejs/node/commit/a0ad12bd7d)] - **lib,tools,test**: remove custom number-isnan rule (Colin Ihrig) [#31211](https://github.com/nodejs/node/pull/31211)
- [[`a6f56bb11e`](https://github.com/nodejs/node/commit/a6f56bb11e)] - **meta**: move thefourtheye to TSC Emeritus (Rich Trott) [#32059](https://github.com/nodejs/node/pull/32059)
- [[`ae9f58cbdd`](https://github.com/nodejs/node/commit/ae9f58cbdd)] - **meta**: move not-an-aardvark to emeritus (Rich Trott) [#31928](https://github.com/nodejs/node/pull/31928)
- [[`553d62c26d`](https://github.com/nodejs/node/commit/553d62c26d)] - **meta**: move aqrln to emeritus (Rich Trott) [#31997](https://github.com/nodejs/node/pull/31997)
- [[`a44fb3fabf`](https://github.com/nodejs/node/commit/a44fb3fabf)] - **meta**: move jbergstroem to emeritus (Rich Trott) [#31996](https://github.com/nodejs/node/pull/31996)
- [[`a75aa93b2d`](https://github.com/nodejs/node/commit/a75aa93b2d)] - **meta**: move maclover7 to Emeritus (Rich Trott) [#31994](https://github.com/nodejs/node/pull/31994)
- [[`fd5c3a749a`](https://github.com/nodejs/node/commit/fd5c3a749a)] - **meta**: move Glen Keane to Collaborator Emeritus (Rich Trott) [#31993](https://github.com/nodejs/node/pull/31993)
- [[`9251307570`](https://github.com/nodejs/node/commit/9251307570)] - **meta**: move julianduque to emeritus (Rich Trott) [#31863](https://github.com/nodejs/node/pull/31863)
- [[`2a4d31ae23`](https://github.com/nodejs/node/commit/2a4d31ae23)] - **meta**: move eljefedelrodeodeljefe to emeritus (Rich Trott) [#31735](https://github.com/nodejs/node/pull/31735)
- [[`c222d561c6`](https://github.com/nodejs/node/commit/c222d561c6)] - **meta**: move princejwesley to emeritus (Rich Trott) [#31730](https://github.com/nodejs/node/pull/31730)
- [[`3e7e9fdca9`](https://github.com/nodejs/node/commit/3e7e9fdca9)] - **meta**: move vkurchatkin to emeritus (Rich Trott) [#31729](https://github.com/nodejs/node/pull/31729)
- [[`ca52b5b1e3`](https://github.com/nodejs/node/commit/ca52b5b1e3)] - **meta**: move calvinmetcalf to emeritus (Rich Trott) [#31736](https://github.com/nodejs/node/pull/31736)
- [[`c892d410bb`](https://github.com/nodejs/node/commit/c892d410bb)] - **meta**: fix collaborator list errors in README.md (James M Snell) [#31655](https://github.com/nodejs/node/pull/31655)
- [[`62b5bd4ca0`](https://github.com/nodejs/node/commit/62b5bd4ca0)] - **module**: add hook for global preload code (Jan Krems) [#32068](https://github.com/nodejs/node/pull/32068)
- [[`c537afb18c`](https://github.com/nodejs/node/commit/c537afb18c)] - **module**: package "exports" error refinements (Guy Bedford) [#31625](https://github.com/nodejs/node/pull/31625)
- [[`4ee41c572c`](https://github.com/nodejs/node/commit/4ee41c572c)] - **module**: drop support for extensionless main entry points in esm (Geoffrey Booth) [#31415](https://github.com/nodejs/node/pull/31415)
- [[`08e09eca34`](https://github.com/nodejs/node/commit/08e09eca34)] - **n-api**: free instance data as reference (Gabriel Schulhof) [#31638](https://github.com/nodejs/node/pull/31638)
- [[`16c690373a`](https://github.com/nodejs/node/commit/16c690373a)] - **n-api**: rename 'promise' parameter to 'value' (Tobias Nießen) [#31544](https://github.com/nodejs/node/pull/31544)
- [[`3a84634cc1`](https://github.com/nodejs/node/commit/3a84634cc1)] - **n-api**: return napi_invalid_arg on napi_create_bigint_words (legendecas) [#31312](https://github.com/nodejs/node/pull/31312)
- [[`0d30546329`](https://github.com/nodejs/node/commit/0d30546329)] - **net**: track state of setNoDelay() and prevent unnecessary system calls (Rusty Conover) [#31543](https://github.com/nodejs/node/pull/31543)
- [[`87cfbb2da1`](https://github.com/nodejs/node/commit/87cfbb2da1)] - **report**: add support for Workers (Anna Henningsen) [#31386](https://github.com/nodejs/node/pull/31386)
- [[`782f5dbddd`](https://github.com/nodejs/node/commit/782f5dbddd)] - **src**: add build Github Action (gengjiawen) [#31153](https://github.com/nodejs/node/pull/31153)
- [[`fbd5be6734`](https://github.com/nodejs/node/commit/fbd5be6734)] - **src**: delete BaseObjectWeakPtr data when pointee is gone (Anna Henningsen) [#32393](https://github.com/nodejs/node/pull/32393)
- [[`56a45095b7`](https://github.com/nodejs/node/commit/56a45095b7)] - **src**: harden running native `SetImmediate()`s slightly (Anna Henningsen) [#31468](https://github.com/nodejs/node/pull/31468)
- [[`cb16aabd15`](https://github.com/nodejs/node/commit/cb16aabd15)] - **src**: simplify native immediate queue running (Anna Henningsen) [#31502](https://github.com/nodejs/node/pull/31502)
- [[`c2176e15ea`](https://github.com/nodejs/node/commit/c2176e15ea)] - **src**: move MemoryInfo() for worker code to .cc files (Anna Henningsen) [#31386](https://github.com/nodejs/node/pull/31386)
- [[`22bf867149`](https://github.com/nodejs/node/commit/22bf867149)] - **src**: add interrupts to Environments/Workers (Anna Henningsen) [#31386](https://github.com/nodejs/node/pull/31386)
- [[`7c2c068aeb`](https://github.com/nodejs/node/commit/7c2c068aeb)] - **src**: remove AsyncRequest (Anna Henningsen) [#31386](https://github.com/nodejs/node/pull/31386)
- [[`748a530046`](https://github.com/nodejs/node/commit/748a530046)] - **src**: add a threadsafe variant of SetImmediate() (Anna Henningsen) [#31386](https://github.com/nodejs/node/pull/31386)
- [[`aafb224147`](https://github.com/nodejs/node/commit/aafb224147)] - **src**: exclude C++ SetImmediate() from count (Anna Henningsen) [#31386](https://github.com/nodejs/node/pull/31386)
- [[`5df969826d`](https://github.com/nodejs/node/commit/5df969826d)] - **src**: better encapsulate native immediate list (Anna Henningsen) [#31386](https://github.com/nodejs/node/pull/31386)
- [[`2625244111`](https://github.com/nodejs/node/commit/2625244111)] - **src**: run native immediates during Environment cleanup (Anna Henningsen) [#30666](https://github.com/nodejs/node/pull/30666)
- [[`5b65348fed`](https://github.com/nodejs/node/commit/5b65348fed)] - **src**: no SetImmediate from destructor in stream_pipe code (Anna Henningsen) [#30666](https://github.com/nodejs/node/pull/30666)
- [[`51230f71ff`](https://github.com/nodejs/node/commit/51230f71ff)] - **src**: add more `can\_call\_into\_js()` guards (Anna Henningsen) [#30666](https://github.com/nodejs/node/pull/30666)
- [[`7647bfe3fc`](https://github.com/nodejs/node/commit/7647bfe3fc)] - **src**: keep object alive in stream_pipe code (Anna Henningsen) [#30666](https://github.com/nodejs/node/pull/30666)
- [[`5f95e69f4d`](https://github.com/nodejs/node/commit/5f95e69f4d)] - **src**: remove HandleWrap instances from list once closed (Anna Henningsen) [#30374](https://github.com/nodejs/node/pull/30374)
- [[`e17d314a21`](https://github.com/nodejs/node/commit/e17d314a21)] - **src**: remove keep alive option from SetImmediate() (Anna Henningsen) [#30374](https://github.com/nodejs/node/pull/30374)
- [[`6db84d3e50`](https://github.com/nodejs/node/commit/6db84d3e50)] - **src**: use BaseObjectPtr for keeping channel alive in dns bindings (Anna Henningsen) [#30374](https://github.com/nodejs/node/pull/30374)
- [[`c60780ff52`](https://github.com/nodejs/node/commit/c60780ff52)] - **src**: introduce custom smart pointers for `BaseObject`s (Anna Henningsen) [#30374](https://github.com/nodejs/node/pull/30374)
- [[`17e10dd3cb`](https://github.com/nodejs/node/commit/17e10dd3cb)] - **src**: use C++ style for struct with initializers (Sam Roberts) [#32134](https://github.com/nodejs/node/pull/32134)
- [[`b5c6230258`](https://github.com/nodejs/node/commit/b5c6230258)] - **src**: implement per-process native Debug() printer (Joyee Cheung) [#31884](https://github.com/nodejs/node/pull/31884)
- [[`b95e8eafa5`](https://github.com/nodejs/node/commit/b95e8eafa5)] - **src**: refactor debug category parsing (Joyee Cheung) [#31884](https://github.com/nodejs/node/pull/31884)
- [[`19f3c0adc2`](https://github.com/nodejs/node/commit/19f3c0adc2)] - **src**: make aliased_buffer.h self-contained (Joyee Cheung) [#31884](https://github.com/nodejs/node/pull/31884)
- [[`908f634110`](https://github.com/nodejs/node/commit/908f634110)] - **src**: discard tasks posted to platform TaskRunner during shutdown (Anna Henningsen) [#31853](https://github.com/nodejs/node/pull/31853)
- [[`808379c379`](https://github.com/nodejs/node/commit/808379c379)] - **src**: Handle bad callback in asyc_wrap (Harshitha KP) [#31946](https://github.com/nodejs/node/pull/31946)
- [[`a6a41f4c77`](https://github.com/nodejs/node/commit/a6a41f4c77)] - **src**: add node_crypto_common and refactor (James M Snell) [#32016](https://github.com/nodejs/node/pull/32016)
- [[`0b327bd81d`](https://github.com/nodejs/node/commit/0b327bd81d)] - **src**: enable `StreamPipe` for generic `StreamBase`s (Anna Henningsen) [#31869](https://github.com/nodejs/node/pull/31869)
- [[`bd92243ddf`](https://github.com/nodejs/node/commit/bd92243ddf)] - **src**: elevate v8 namespaces (Harshitha KP) [#31901](https://github.com/nodejs/node/pull/31901)
- [[`3b2bbbdeca`](https://github.com/nodejs/node/commit/3b2bbbdeca)] - **src**: allow unique_ptrs with custom deleter in memory tracker (Anna Henningsen) [#31870](https://github.com/nodejs/node/pull/31870)
- [[`9ab4a7e5ce`](https://github.com/nodejs/node/commit/9ab4a7e5ce)] - **src**: move BaseObject subclass dtors/ctors out of node_crypto.h (Anna Henningsen) [#31872](https://github.com/nodejs/node/pull/31872)
- [[`041408d513`](https://github.com/nodejs/node/commit/041408d513)] - **src**: don't run bootstrapper in CreateEnvironment (Shelley Vohr) [#31910](https://github.com/nodejs/node/pull/31910)
- [[`e6debf5c25`](https://github.com/nodejs/node/commit/e6debf5c25)] - **src**: prefer 3-argument Array::New() (Anna Henningsen) [#31775](https://github.com/nodejs/node/pull/31775)
- [[`98640f7a6d`](https://github.com/nodejs/node/commit/98640f7a6d)] - **src**: use hex not decimal in IsArrayIndex (Shelley Vohr) [#31758](https://github.com/nodejs/node/pull/31758)
- [[`75971009d0`](https://github.com/nodejs/node/commit/75971009d0)] - **src**: wrap HostPort in ExclusiveAccess (Ben Noordhuis) [#31717](https://github.com/nodejs/node/pull/31717)
- [[`01da65644e`](https://github.com/nodejs/node/commit/01da65644e)] - **src**: add ExclusiveAccess class (Ben Noordhuis) [#31717](https://github.com/nodejs/node/pull/31717)
- [[`28289eaeb8`](https://github.com/nodejs/node/commit/28289eaeb8)] - **src**: allow to reuse env options handling (Denys Otrishko) [#31711](https://github.com/nodejs/node/pull/31711)
- [[`249a0fe61d`](https://github.com/nodejs/node/commit/249a0fe61d)] - **src**: fix compile warnings in node_url.cc (Anna Henningsen) [#31689](https://github.com/nodejs/node/pull/31689)
- [[`bf729d02b7`](https://github.com/nodejs/node/commit/bf729d02b7)] - **src**: modernized unique_ptr construction (Yuhanun Citgez) [#31654](https://github.com/nodejs/node/pull/31654)
- [[`6e3e158f51`](https://github.com/nodejs/node/commit/6e3e158f51)] - **src**: remove dead code in InternalMakeCallback (Gerhard Stoebich) [#31622](https://github.com/nodejs/node/pull/31622)
- [[`c34672a3b0`](https://github.com/nodejs/node/commit/c34672a3b0)] - **src**: remove fixed-size GetHumanReadableProcessName (Ben Noordhuis) [#31633](https://github.com/nodejs/node/pull/31633)
- [[`57d1d73b47`](https://github.com/nodejs/node/commit/57d1d73b47)] - **src**: fix OOB reads in process.title getter (Ben Noordhuis) [#31633](https://github.com/nodejs/node/pull/31633)
- [[`5e68a13d53`](https://github.com/nodejs/node/commit/5e68a13d53)] - **src**: various minor improvements to node_url (James M Snell) [#31651](https://github.com/nodejs/node/pull/31651)
- [[`2cdd57ab67`](https://github.com/nodejs/node/commit/2cdd57ab67)] - **src**: fix inspecting `MessagePort` from `init` async hook (Anna Henningsen) [#31600](https://github.com/nodejs/node/pull/31600)
- [[`753db6aee2`](https://github.com/nodejs/node/commit/753db6aee2)] - **src**: remove unused `Worker::child\_port\_` member (Anna Henningsen) [#31599](https://github.com/nodejs/node/pull/31599)
- [[`7e52e39385`](https://github.com/nodejs/node/commit/7e52e39385)] - **src**: change Fill() to use ParseArrayIndex() (ConorDavenport) [#31591](https://github.com/nodejs/node/pull/31591)
- [[`79a6872809`](https://github.com/nodejs/node/commit/79a6872809)] - **src**: remove duplicate field env in CryptoJob class (ConorDavenport) [#31554](https://github.com/nodejs/node/pull/31554)
- [[`5e19c4a9d4`](https://github.com/nodejs/node/commit/5e19c4a9d4)] - **src**: fix console debug output on Windows (Denys Otrishko) [#31580](https://github.com/nodejs/node/pull/31580)
- [[`9c9dc4b184`](https://github.com/nodejs/node/commit/9c9dc4b184)] - **src**: remove preview for heap dump utilities (Anna Henningsen) [#31570](https://github.com/nodejs/node/pull/31570)
- [[`91dd1018ac`](https://github.com/nodejs/node/commit/91dd1018ac)] - **src**: fix debug crash handling null strings (Rusty Conover) [#31523](https://github.com/nodejs/node/pull/31523)
- [[`fb32043e6a`](https://github.com/nodejs/node/commit/fb32043e6a)] - **src**: define noreturn attribute for windows (Alexander Smarus) [#31467](https://github.com/nodejs/node/pull/31467)
- [[`ce6b9d15d2`](https://github.com/nodejs/node/commit/ce6b9d15d2)] - **src**: reduce code duplication in BootstrapNode (Denys Otrishko) [#31465](https://github.com/nodejs/node/pull/31465)
- [[`a309af0f52`](https://github.com/nodejs/node/commit/a309af0f52)] - **src**: use custom fprintf alike to write errors to stderr (Anna Henningsen) [#31446](https://github.com/nodejs/node/pull/31446)
- [[`7bdd29fa21`](https://github.com/nodejs/node/commit/7bdd29fa21)] - **src**: add C++-style sprintf utility (Anna Henningsen) [#31446](https://github.com/nodejs/node/pull/31446)
- [[`8f88d62a31`](https://github.com/nodejs/node/commit/8f88d62a31)] - **src**: reduce large pages code duplication (Gabriel Schulhof) [#31385](https://github.com/nodejs/node/pull/31385)
- [[`de6d5523a1`](https://github.com/nodejs/node/commit/de6d5523a1)] - **src**: fix ignore GCC -Wcast-function-type for older compilers (Denys Otrishko) [#31524](https://github.com/nodejs/node/pull/31524)
- [[`a8d9c0f8b6`](https://github.com/nodejs/node/commit/a8d9c0f8b6)] - **src**: ignore GCC -Wcast-function-type for v8.h (Daniel Bevenius) [#31475](https://github.com/nodejs/node/pull/31475)
- [[`a2f1825cb5`](https://github.com/nodejs/node/commit/a2f1825cb5)] - **src**: fix performance regression in node_file.cc (Ben Noordhuis) [#31343](https://github.com/nodejs/node/pull/31343)
- [[`1d075cfd7f`](https://github.com/nodejs/node/commit/1d075cfd7f)] - **src**: use uv_guess_handle() to detect TTYs (Colin Ihrig) [#31333](https://github.com/nodejs/node/pull/31333)
- [[`21bcc96f92`](https://github.com/nodejs/node/commit/21bcc96f92)] - **src**: include uv.h in node_binding header (Shelley Vohr) [#31265](https://github.com/nodejs/node/pull/31265)
- [[`d77a1b088f`](https://github.com/nodejs/node/commit/d77a1b088f)] - **src**: remove node::InitializeV8Platform() (Ben Noordhuis) [#31245](https://github.com/nodejs/node/pull/31245)
- [[`fe1ac496f7`](https://github.com/nodejs/node/commit/fe1ac496f7)] - **src**: remove uses of node::InitializeV8Platform() (Ben Noordhuis) [#31245](https://github.com/nodejs/node/pull/31245)
- [[`8aa7bf2d23`](https://github.com/nodejs/node/commit/8aa7bf2d23)] - **src**: clean up large_pages code (Denys Otrishko) [#31196](https://github.com/nodejs/node/pull/31196)
- [[`12253f8c74`](https://github.com/nodejs/node/commit/12253f8c74)] - **stream**: sync stream unpipe resume (Robert Nagy) [#31191](https://github.com/nodejs/node/pull/31191)
- [[`6e76752a7b`](https://github.com/nodejs/node/commit/6e76752a7b)] - **stream**: simplify push (Robert Nagy) [#31150](https://github.com/nodejs/node/pull/31150)
- [[`8973209ad0`](https://github.com/nodejs/node/commit/8973209ad0)] - **stream**: clean up definition using defineProperties (antsmartian) [#31236](https://github.com/nodejs/node/pull/31236)
- [[`a987972bde`](https://github.com/nodejs/node/commit/a987972bde)] - **stream**: replace Function.prototype with primordial (Sebastien Ahkrin) [#31204](https://github.com/nodejs/node/pull/31204)
- [[`e685f12ee6`](https://github.com/nodejs/node/commit/e685f12ee6)] - **test**: restore --jitless test on AIX (Richard Lau) [#32619](https://github.com/nodejs/node/pull/32619)
- [[`eee587b847`](https://github.com/nodejs/node/commit/eee587b847)] - **test**: fix test-http2-reset-flood flakiness (Anna Henningsen) [#32607](https://github.com/nodejs/node/pull/32607)
- [[`d568efcd22`](https://github.com/nodejs/node/commit/d568efcd22)] - **test**: refactor common.expectsError (Ruben Bridgewater) [#31092](https://github.com/nodejs/node/pull/31092)
- [[`e4f9360287`](https://github.com/nodejs/node/commit/e4f9360287)] - **test**: mark test-http2-reset-flood flaky on bsd (Myles Borins) [#32595](https://github.com/nodejs/node/pull/32595)
- [[`6f50b60018`](https://github.com/nodejs/node/commit/6f50b60018)] - **test**: add test-worker-prof to the SLOW list for debug (Myles Borins) [#32589](https://github.com/nodejs/node/pull/32589)
- [[`7123c0f042`](https://github.com/nodejs/node/commit/7123c0f042)] - **test**: always skip vm-timeout-escape-queuemicrotask (Denys Otrishko) [#31980](https://github.com/nodejs/node/pull/31980)
- [[`bb947ce3c2`](https://github.com/nodejs/node/commit/bb947ce3c2)] - **test**: improve test-debug-usage (Rich Trott) [#32141](https://github.com/nodejs/node/pull/32141)
- [[`7c8a7b4c7d`](https://github.com/nodejs/node/commit/7c8a7b4c7d)] - **test**: end tls connection with some data (Sam Roberts) [#32328](https://github.com/nodejs/node/pull/32328)
- [[`f4bd01c816`](https://github.com/nodejs/node/commit/f4bd01c816)] - **test**: discard data received by client (Hassaan Pasha) [#32328](https://github.com/nodejs/node/pull/32328)
- [[`7a14ddf104`](https://github.com/nodejs/node/commit/7a14ddf104)] - **test**: increase test timeout to prevent flakiness (Ruben Bridgewater) [#31716](https://github.com/nodejs/node/pull/31716)
- [[`147045716b`](https://github.com/nodejs/node/commit/147045716b)] - **test**: use index.js if package.json "main" is empty (Ben Noordhuis) [#32040](https://github.com/nodejs/node/pull/32040)
- [[`03aa2e1b7b`](https://github.com/nodejs/node/commit/03aa2e1b7b)] - **test**: changed function to arrow function (ProdipRoy89) [#32045](https://github.com/nodejs/node/pull/32045)
- [[`b4c407fecc`](https://github.com/nodejs/node/commit/b4c407fecc)] - **test**: allow EAI_FAIL in test-net-dns-error.js (Vita Batrla) [#31780](https://github.com/nodejs/node/pull/31780)
- [[`2582083f63`](https://github.com/nodejs/node/commit/2582083f63)] - **test**: remove superfluous checks in test-net-reconnect-error (Rich Trott) [#32120](https://github.com/nodejs/node/pull/32120)
- [[`f365e5c262`](https://github.com/nodejs/node/commit/f365e5c262)] - **test**: apply camelCase in test-net-reconnect-error (Rich Trott) [#32120](https://github.com/nodejs/node/pull/32120)
- [[`256bc4412c`](https://github.com/nodejs/node/commit/256bc4412c)] - **test**: update tests for larger Buffers (Jakob Kummerow) [#32114](https://github.com/nodejs/node/pull/32114)
- [[`96c7226897`](https://github.com/nodejs/node/commit/96c7226897)] - **test**: remove common.port from test-tls-securepair-client (Rich Trott) [#32024](https://github.com/nodejs/node/pull/32024)
- [[`1318662ff7`](https://github.com/nodejs/node/commit/1318662ff7)] - **test**: add WASI test for path_link() (Colin Ihrig) [#32132](https://github.com/nodejs/node/pull/32132)
- [[`55214628af`](https://github.com/nodejs/node/commit/55214628af)] - **test**: move test-inspector-module to parallel (Rich Trott) [#32025](https://github.com/nodejs/node/pull/32025)
- [[`3574116887`](https://github.com/nodejs/node/commit/3574116887)] - **test**: fix flaky test-dns-any.js (Rich Trott) [#32017](https://github.com/nodejs/node/pull/32017)
- [[`d62538404e`](https://github.com/nodejs/node/commit/d62538404e)] - **test**: fix flaky test-gc-net-timeout (Robert Nagy) [#31918](https://github.com/nodejs/node/pull/31918)
- [[`2bf9a2d84c`](https://github.com/nodejs/node/commit/2bf9a2d84c)] - **test**: change test to not be sensitive to buffer send size (Rusty Conover) [#31499](https://github.com/nodejs/node/pull/31499)
- [[`b1cf56f5db`](https://github.com/nodejs/node/commit/b1cf56f5db)] - **test**: remove sequential/test-https-keep-alive-large-write.js (Rusty Conover) [#31499](https://github.com/nodejs/node/pull/31499)
- [[`67c3a95f7d`](https://github.com/nodejs/node/commit/67c3a95f7d)] - **test**: validate common property usage (Denys Otrishko) [#31933](https://github.com/nodejs/node/pull/31933)
- [[`26d9f4c160`](https://github.com/nodejs/node/commit/26d9f4c160)] - **test**: fix usage of invalid common properties (Denys Otrishko) [#31933](https://github.com/nodejs/node/pull/31933)
- [[`086e14d251`](https://github.com/nodejs/node/commit/086e14d251)] - **test**: increase timeout in vm-timeout-escape-queuemicrotask (Denys Otrishko) [#31966](https://github.com/nodejs/node/pull/31966)
- [[`c2ffef8678`](https://github.com/nodejs/node/commit/c2ffef8678)] - **test**: add documentation for common.enoughTestCpu (Rich Trott) [#31931](https://github.com/nodejs/node/pull/31931)
- [[`0c6fdfc4ac`](https://github.com/nodejs/node/commit/0c6fdfc4ac)] - **test**: fix typo in common/index.js (Rich Trott) [#31931](https://github.com/nodejs/node/pull/31931)
- [[`3deee057b3`](https://github.com/nodejs/node/commit/3deee057b3)] - **test**: remove common.PORT from assorted pummel tests (Rich Trott) [#31897](https://github.com/nodejs/node/pull/31897)
- [[`bde5a9bda8`](https://github.com/nodejs/node/commit/bde5a9bda8)] - **test**: remove flaky designation for test-net-connect-options-port (Rich Trott) [#31841](https://github.com/nodejs/node/pull/31841)
- [[`c386f7568c`](https://github.com/nodejs/node/commit/c386f7568c)] - **test**: remove common.PORT from test-net-write-callbacks.js (Rich Trott) [#31839](https://github.com/nodejs/node/pull/31839)
- [[`709256346c`](https://github.com/nodejs/node/commit/709256346c)] - **test**: remove common.PORT from test-net-pause (Rich Trott) [#31749](https://github.com/nodejs/node/pull/31749)
- [[`61de609ac8`](https://github.com/nodejs/node/commit/61de609ac8)] - **test**: remove common.PORT from test-tls-server-large-request (Rich Trott) [#31749](https://github.com/nodejs/node/pull/31749)
- [[`33d3cccb98`](https://github.com/nodejs/node/commit/33d3cccb98)] - **test**: remove common.PORT from test-net-throttle (Rich Trott) [#31749](https://github.com/nodejs/node/pull/31749)
- [[`d172cc1474`](https://github.com/nodejs/node/commit/d172cc1474)] - **test**: remove common.PORT from test-net-timeout (Rich Trott) [#31749](https://github.com/nodejs/node/pull/31749)
- [[`1109124313`](https://github.com/nodejs/node/commit/1109124313)] - **test**: add known issue test for sync writable callback (James M Snell) [#31756](https://github.com/nodejs/node/pull/31756)
- [[`aa5afd013b`](https://github.com/nodejs/node/commit/aa5afd013b)] - **test**: mark test-fs-stat-bigint flaky on FreeBSD (Rich Trott) [#31728](https://github.com/nodejs/node/pull/31728)
- [[`3f43c5f508`](https://github.com/nodejs/node/commit/3f43c5f508)] - **test**: improve test-fs-stat-bigint (Rich Trott) [#31726](https://github.com/nodejs/node/pull/31726)
- [[`3f6805f0e7`](https://github.com/nodejs/node/commit/3f6805f0e7)] - **test**: remove flaky designation for test-fs-stat-bigint (Rich Trott) [#30437](https://github.com/nodejs/node/pull/30437)
- [[`7d71465194`](https://github.com/nodejs/node/commit/7d71465194)] - **test**: fix flaky test-fs-stat-bigint (Duncan Healy) [#30437](https://github.com/nodejs/node/pull/30437)
- [[`ca6fce0cbb`](https://github.com/nodejs/node/commit/ca6fce0cbb)] - **test**: add debugging output to test-net-listen-after-destroy-stdin (Rich Trott) [#31698](https://github.com/nodejs/node/pull/31698)
- [[`59eba1177b`](https://github.com/nodejs/node/commit/59eba1177b)] - **test**: improve assertion message in test-dns-any (Rich Trott) [#31697](https://github.com/nodejs/node/pull/31697)
- [[`61e534baa0`](https://github.com/nodejs/node/commit/61e534baa0)] - **test**: stricter assert color test (Ruben Bridgewater) [#31429](https://github.com/nodejs/node/pull/31429)
- [[`bdd1133451`](https://github.com/nodejs/node/commit/bdd1133451)] - **test**: fix test-benchmark-http (Rich Trott) [#31686](https://github.com/nodejs/node/pull/31686)
- [[`795a21d53a`](https://github.com/nodejs/node/commit/795a21d53a)] - **test**: fix flaky test-inspector-connect-main-thread (Anna Henningsen) [#31637](https://github.com/nodejs/node/pull/31637)
- [[`297fb67304`](https://github.com/nodejs/node/commit/297fb67304)] - **test**: add test-dns-promises-lookupService (Rich Trott) [#31640](https://github.com/nodejs/node/pull/31640)
- [[`02c2396976`](https://github.com/nodejs/node/commit/02c2396976)] - **test**: fix flaky test-http2-stream-destroy-event-order (Anna Henningsen) [#31610](https://github.com/nodejs/node/pull/31610)
- [[`d2fbe80a4a`](https://github.com/nodejs/node/commit/d2fbe80a4a)] - **test**: unset NODE_OPTIONS for cctest (Anna Henningsen) [#31594](https://github.com/nodejs/node/pull/31594)
- [[`944f1a345a`](https://github.com/nodejs/node/commit/944f1a345a)] - **test**: simplify test-https-simple.js (Sam Roberts) [#31584](https://github.com/nodejs/node/pull/31584)
- [[`0eb2dbb24e`](https://github.com/nodejs/node/commit/0eb2dbb24e)] - **test**: mark additional tests as flaky on Windows (Anna Henningsen) [#31606](https://github.com/nodejs/node/pull/31606)
- [[`0bc3bd7c11`](https://github.com/nodejs/node/commit/0bc3bd7c11)] - **test**: remove --experimental-worker flag comment (Harshitha KP) [#31563](https://github.com/nodejs/node/pull/31563)
- [[`baa14c9e39`](https://github.com/nodejs/node/commit/baa14c9e39)] - **test**: make test-http2-buffersize more correct (Anna Henningsen) [#31502](https://github.com/nodejs/node/pull/31502)
- [[`e3e056d5cd`](https://github.com/nodejs/node/commit/e3e056d5cd)] - **test**: fix test-heapdump-worker (Anna Henningsen) [#31494](https://github.com/nodejs/node/pull/31494)
- [[`48f4212286`](https://github.com/nodejs/node/commit/48f4212286)] - **test**: add tests for main() argument handling (Colin Ihrig) [#31426](https://github.com/nodejs/node/pull/31426)
- [[`dbe2d85f66`](https://github.com/nodejs/node/commit/dbe2d85f66)] - **test**: add wasi test for freopen() (Colin Ihrig) [#31432](https://github.com/nodejs/node/pull/31432)
- [[`a8e2f405f2`](https://github.com/nodejs/node/commit/a8e2f405f2)] - **test**: remove bluebird remnants from test fixture (Rich Trott) [#31435](https://github.com/nodejs/node/pull/31435)
- [[`8438d1498d`](https://github.com/nodejs/node/commit/8438d1498d)] - **test**: improve wasi stat test (Colin Ihrig) [#31413](https://github.com/nodejs/node/pull/31413)
- [[`596920dbf4`](https://github.com/nodejs/node/commit/596920dbf4)] - **test**: add wasi test for symlink() and readlink() (Colin Ihrig) [#31403](https://github.com/nodejs/node/pull/31403)
- [[`2750e65f5c`](https://github.com/nodejs/node/commit/2750e65f5c)] - **test**: update postmortem test with v12 constants (Matheus Marchini) [#31391](https://github.com/nodejs/node/pull/31391)
- [[`642f8c0eb9`](https://github.com/nodejs/node/commit/642f8c0eb9)] - **test**: export public symbols in addons tests (Ben Noordhuis) [#28717](https://github.com/nodejs/node/pull/28717)
- [[`20167fec5f`](https://github.com/nodejs/node/commit/20167fec5f)] - **test**: stricten readline keypress failure test condition (Ruben Bridgewater) [#31300](https://github.com/nodejs/node/pull/31300)
- [[`c719f7ab36`](https://github.com/nodejs/node/commit/c719f7ab36)] - **test**: allow disabling crypto tests (Shelley Vohr) [#31268](https://github.com/nodejs/node/pull/31268)
- [[`31a13dc3a4`](https://github.com/nodejs/node/commit/31a13dc3a4)] - **test**: fix recursive rm test to actually use tmpdir (Denys Otrishko) [#31250](https://github.com/nodejs/node/pull/31250)
- [[`320ac13452`](https://github.com/nodejs/node/commit/320ac13452)] - **test**: remove unused symlink loop (Colin Ihrig) [#31267](https://github.com/nodejs/node/pull/31267)
- [[`f3af68ea80`](https://github.com/nodejs/node/commit/f3af68ea80)] - **test**: prefer server over srv (Andrew Hughes) [#31224](https://github.com/nodejs/node/pull/31224)
- [[`04e2f41783`](https://github.com/nodejs/node/commit/04e2f41783)] - **test**: fix unit test logging with python3 (Adam Majer) [#31156](https://github.com/nodejs/node/pull/31156)
- [[`5a537babe1`](https://github.com/nodejs/node/commit/5a537babe1)] - **test**: mark empty udp tests flaky on OS X (Sam Roberts) [#32146](https://github.com/nodejs/node/pull/32146)
- [[`99cfab2594`](https://github.com/nodejs/node/commit/99cfab2594)] - **test,dns**: add coverage for dns exception (Rich Trott) [#31678](https://github.com/nodejs/node/pull/31678)
- [[`54395c60eb`](https://github.com/nodejs/node/commit/54395c60eb)] - **tls**: reduce memory copying and number of BIO buffer allocations (Rusty Conover) [#31499](https://github.com/nodejs/node/pull/31499)
- [[`4f177c4f63`](https://github.com/nodejs/node/commit/4f177c4f63)] - **tls**: simplify errors using ThrowCryptoError (Tobias Nießen) [#31436](https://github.com/nodejs/node/pull/31436)
- [[`c0e6e60cb1`](https://github.com/nodejs/node/commit/c0e6e60cb1)] - **tools**: update minimist@1.2.5 (Rich Trott) [#32274](https://github.com/nodejs/node/pull/32274)
- [[`dca3d298dd`](https://github.com/nodejs/node/commit/dca3d298dd)] - **tools**: update icu to 65.1 (Albert Wang) [#30232](https://github.com/nodejs/node/pull/30232)
- [[`d57719098c`](https://github.com/nodejs/node/commit/d57719098c)] - **tools**: only fetch previous versions when necessary (Richard Lau) [#32518](https://github.com/nodejs/node/pull/32518)
- [[`61d54e7716`](https://github.com/nodejs/node/commit/61d54e7716)] - **tools**: use per-process native Debug() printer in mkcodecache (Joyee Cheung) [#31884](https://github.com/nodejs/node/pull/31884)
- [[`1060a2bba9`](https://github.com/nodejs/node/commit/1060a2bba9)] - **tools**: add NODE_TEST_NO_INTERNET to the doc builder (Joyee Cheung) [#31849](https://github.com/nodejs/node/pull/31849)
- [[`aa8a435e17`](https://github.com/nodejs/node/commit/aa8a435e17)] - **tools**: sync gyp code base with node-gyp repo (Michaël Zasso) [#30563](https://github.com/nodejs/node/pull/30563)
- [[`6b1a5518e0`](https://github.com/nodejs/node/commit/6b1a5518e0)] - **tools**: update lint-md task to lint for possessives of Node.js (Rich Trott) [#31862](https://github.com/nodejs/node/pull/31862)
- [[`b657df4759`](https://github.com/nodejs/node/commit/b657df4759)] - **tools**: update Markdown linter to be cross-platform (Derek Lewis) [#31239](https://github.com/nodejs/node/pull/31239)
- [[`289f3dc538`](https://github.com/nodejs/node/commit/289f3dc538)] - **tools**: replace deprecated iteritems() for items() (Giovanny Andres Gongora Granada (Gioyik)) [#31528](https://github.com/nodejs/node/pull/31528)
- [[`77e6700b03`](https://github.com/nodejs/node/commit/77e6700b03)] - **tools**: remove obsolete dependencies (Rich Trott) [#31359](https://github.com/nodejs/node/pull/31359)
- [[`c7b1f1df3b`](https://github.com/nodejs/node/commit/c7b1f1df3b)] - **tools**: update remark-preset-lint-node to 1.12.0 (Rich Trott) [#31359](https://github.com/nodejs/node/pull/31359)
- [[`20f857fa01`](https://github.com/nodejs/node/commit/20f857fa01)] - **tools**: update JSON header parsing for backticks (Rich Trott) [#31294](https://github.com/nodejs/node/pull/31294)
- [[`0f4a9e26ef`](https://github.com/nodejs/node/commit/0f4a9e26ef)] - **tools**: ensure consistent perms of signed release files (Rod Vagg) [#29350](https://github.com/nodejs/node/pull/29350)
- [[`6f71efa0ed`](https://github.com/nodejs/node/commit/6f71efa0ed)] - **tools**: add clang-tidy rule in src (gengjiawen) [#26840](https://github.com/nodejs/node/pull/26840)
- [[`3a1566a267`](https://github.com/nodejs/node/commit/3a1566a267)] - **tools**: unify make-v8.sh for ppc64le and s390x (Richard Lau) [#31628](https://github.com/nodejs/node/pull/31628)
- [[`fbc0bd95ec`](https://github.com/nodejs/node/commit/fbc0bd95ec)] - **tty**: do not end in an infinite warning recursion (Ruben Bridgewater) [#31429](https://github.com/nodejs/node/pull/31429)
- [[`32c0449141`](https://github.com/nodejs/node/commit/32c0449141)] - **(SEMVER-MINOR)** **util**: use a global symbol for `util.promisify.custom` (ExE Boss) [#31672](https://github.com/nodejs/node/pull/31672)
- [[`f4e5404b5d`](https://github.com/nodejs/node/commit/f4e5404b5d)] - **util**: throw if unreachable TypedArray checking code is reached (Rich Trott) [#31737](https://github.com/nodejs/node/pull/31737)
- [[`785417aeda`](https://github.com/nodejs/node/commit/785417aeda)] - **util**: add coverage for util.inspect.colors alias setter (Rich Trott) [#31743](https://github.com/nodejs/node/pull/31743)
- [[`c9fa2d1fbf`](https://github.com/nodejs/node/commit/c9fa2d1fbf)] - **util**: throw if unreachable code is reached (Rich Trott) [#31712](https://github.com/nodejs/node/pull/31712)
- [[`51d8fbf31f`](https://github.com/nodejs/node/commit/51d8fbf31f)] - **util**: fix inspection of typed arrays with unusual length (Ruben Bridgewater) [#31458](https://github.com/nodejs/node/pull/31458)
- [[`f068788f59`](https://github.com/nodejs/node/commit/f068788f59)] - **util**: add colors to debuglog() (Ruben Bridgewater) [#30930](https://github.com/nodejs/node/pull/30930)
- [[`a91a824108`](https://github.com/nodejs/node/commit/a91a824108)] - **wasi**: improve use of primordials (Colin Ihrig) [#31212](https://github.com/nodejs/node/pull/31212)
- [[`2029c10196`](https://github.com/nodejs/node/commit/2029c10196)] - **win**: change to use Python in install tool (gengjiawen) [#31221](https://github.com/nodejs/node/pull/31221)
- [[`c5de212039`](https://github.com/nodejs/node/commit/c5de212039)] - **worker**: move JoinThread() back into exit callback (Anna Henningsen) [#31468](https://github.com/nodejs/node/pull/31468)
- [[`65729f966e`](https://github.com/nodejs/node/commit/65729f966e)] - **worker**: emit runtime error on loop creation failure (Harshitha KP) [#31621](https://github.com/nodejs/node/pull/31621)
- [[`ea989e160e`](https://github.com/nodejs/node/commit/ea989e160e)] - **worker**: unroll file extension regexp (Anna Henningsen) [#31779](https://github.com/nodejs/node/pull/31779)
- [[`9f8d315a09`](https://github.com/nodejs/node/commit/9f8d315a09)] - **worker**: add support for .cjs extension (Antoine du HAMEL) [#31662](https://github.com/nodejs/node/pull/31662)
- [[`30dbc84642`](https://github.com/nodejs/node/commit/30dbc84642)] - **worker**: properly handle env and NODE_OPTIONS in workers (Denys Otrishko) [#31711](https://github.com/nodejs/node/pull/31711)
- [[`0697f65f70`](https://github.com/nodejs/node/commit/0697f65f70)] - **worker**: reset `Isolate` stack limit after entering `Locker` (Anna Henningsen) [#31593](https://github.com/nodejs/node/pull/31593)
- [[`5500521804`](https://github.com/nodejs/node/commit/5500521804)] - **worker**: remove redundant closing of child port (aaccttrr) [#31555](https://github.com/nodejs/node/pull/31555)

Windows 32-bit Installer: https://nodejs.org/dist/v12.16.2/node-v12.16.2-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v12.16.2/node-v12.16.2-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v12.16.2/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v12.16.2/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v12.16.2/node-v12.16.2.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v12.16.2/node-v12.16.2-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v12.16.2/node-v12.16.2-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v12.16.2/node-v12.16.2-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v12.16.2/node-v12.16.2-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v12.16.2/node-v12.16.2-aix-ppc64.tar.gz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v12.16.2/node-v12.16.2-sunos-x64.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v12.16.2/node-v12.16.2-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v12.16.2/node-v12.16.2-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v12.16.2/node-v12.16.2.tar.gz \
Other release files: https://nodejs.org/dist/v12.16.2/ \
Documentation: https://nodejs.org/docs/v12.16.2/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

6beb0d9583b33d6ff7cab78cb89f61c861fe57d2fff6474317aa2613eaf1634f  node-v12.16.2-aix-ppc64.tar.gz
483954e311a5ff649ddf32b473f635a58890790d284b5788bdd8d7ff850c6db2  node-v12.16.2-darwin-x64.tar.gz
abdf114ad49ecc83715e868627804f22e5c32f9f654d0b1eae37ca01f71e1416  node-v12.16.2-darwin-x64.tar.xz
9c3c1b826222a7fa1693a53b45e67063061f3ac72d461251a67616c8be08753b  node-v12.16.2-headers.tar.gz
1e017798ab427897b74ba8ea9754e58b030f2e6bdb00ee31180f2ec740344bb0  node-v12.16.2-headers.tar.xz
0beb78161a02eed9fc2a97e9cf95e1aecfdff61da6a695a26a66880528f1f53f  node-v12.16.2-linux-arm64.tar.gz
c88005467f6dcdb621d454b35ec39bb42e165c6a2e1cee74cabcdc01af307e81  node-v12.16.2-linux-arm64.tar.xz
542175f19369b99484bbac7d8ad41a4bbd6e642affd97afc78f236472c3afe75  node-v12.16.2-linux-armv7l.tar.gz
7210712073cc47aef684e8968e18d7a47cce7ba57e80e4cc10f328a8bbbc9aef  node-v12.16.2-linux-armv7l.tar.xz
b27c8946f9aeb867f98d5e5e6445a2d9ef5d92097411ff17e9e6efddb56c7b15  node-v12.16.2-linux-ppc64le.tar.gz
ac430e64fcac892ad6ee29fe2a6c856361bf131630e40e603a7ca3a63627df6e  node-v12.16.2-linux-ppc64le.tar.xz
3d189b860176e753bea65fc522795427ab1865fbcacc295a1eeb2886cfb4942c  node-v12.16.2-linux-s390x.tar.gz
61a283a110f57c06a8b4446b271b0d0075466ced0a8752a3e5170cf9968b4efe  node-v12.16.2-linux-s390x.tar.xz
ffc92b8d9f53a10a2a734c93e78a3be74af7b0034bddd6f034a700c430eb94c1  node-v12.16.2-linux-x64.tar.gz
f94a6eb06e80ef2794ebf51a2baed0b89ed307d3196ab5579f16c0fa7cc62901  node-v12.16.2-linux-x64.tar.xz
2d6bd4a100e38d18f48a37cf5cf4ae7226d7574dcca61990fe8439834fc07504  node-v12.16.2.pkg
1d483fc65a72a74ad33c78d3fd18e4691f2b61ceb89f009abc4fba396185588d  node-v12.16.2-sunos-x64.tar.gz
bb4374ddc136fd6b36df662b1cab5515e5198c0228ed3db723585fe50f659c6f  node-v12.16.2-sunos-x64.tar.xz
48b21340eff95bfc6e7297e4a027f5db74d6d64257c476972a1b2f2fdccc9582  node-v12.16.2.tar.gz
555c47ca0a40e5526d9ab7b2e9c18f9dbd1d956cbdc013fd2223bb11a069be78  node-v12.16.2.tar.xz
25a134821a5f45b7ef6f7eaf0196c60de90132cb280eea5497666f0b42740f37  node-v12.16.2-win-x64.7z
f34303a49384aee2e5daab5f248c5a719e850a9ebb19e7098fe2bfffb26efe03  node-v12.16.2-win-x64.zip
3e047cd9d738d358a7e66c7f96874ab81199693ee3d7ed92d3e475becb5454a3  node-v12.16.2-win-x86.7z
2f30dd518421450fc58a67074cb99983d0a1903311e6b23443eda5486e2ef182  node-v12.16.2-win-x86.zip
7d305c61032a2392900c9435b4d6e4656d6387fef1f8a6706d9fabde35022652  node-v12.16.2-x64.msi
6729debf3dab8c25f2a5d466fc697a7392f612d6ad9416e54c82d735d6f0a11a  node-v12.16.2-x86.msi
37145b237d99ac81299584ca74105a9e06e5f4662c14edee5bf468b369dc2c46  win-x64/node.exe
e17dce3a62f0b7e8d3c6f12c7d675da8ff25960ac0c938641e9dc0975387025e  win-x64/node.lib
bb0226289a5e9ebb28b29e3f2e1ec7bd7efd503018299605e378da9d3290dcf1  win-x64/node_pdb.7z
e5f3db8b41133cbc3b5c56ccc6bd9bfff57a53b60c9fd02dd46adf9348d0f770  win-x64/node_pdb.zip
0095ecf38be0175cd7bfb1c5afc67801a2f73192c302ddc55a87c342dd62bc62  win-x86/node.exe
8dd3436d74aa48ac7f25bc24dc497f7abc304bf0844c72ee31ad4cff9fa55088  win-x86/node.lib
751085bf76a8bc8d989098ac80454c7d626e7b6fe1f52597876f22205f8b866b  win-x86/node_pdb.7z
c14d675300d1c40173d18d127edb2a018574c9374a1c5f0268b6b54a28fde9ea  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEueL1mBqm4M0oFg2f8TmTp1WZZTwFAl6OJG4ACgkQ8TmTp1WZ
ZTxLUQ//WokAAOUyCWnmWu+4AdpRa57k93ibkl5QmoDKROYAsSk54SmRpxw8iLW7
N/VlfKAec01PMKV8SesyIQJhKQmGiwdcAttnQcNP8wuDvHt/vudDF9jArOaDJANH
12yYg99NQ2vESsF/OtCzDPi1Wm7BvlZZt9fczSYHO99tU2fp+xkJw/dHsHiRyGs4
1O/YMaERILOUGoAHe5pOTmwOObyEtxmKoA6cIvJdP1+tixuqed/6t0+AgK3ZFdVi
jnwZn+Jcm8Gg0mnRR6MhHCQQNZBqJHPmq8INMnZWKOPjHSV8e62ITD3KarkeDn6D
9jqesclS3jYzOSjEDDOMlcDbJbUh2647CWxF/KfDsyItq9EVXYIC0Iw0NqBnohsF
i85JX2JFwoIyuEsEIJn3wshv/2+bGdLUGbo+fX5yHdb2yS9CVxBJcu/44E1zLAov
auKzKeeruh+x0okPZ87L0q2GChr22MdG8zdF8RGnv0ZCBrxgmI0rLFFazuL/0z/Z
mPWbM8H2lRhCeSmnO2FI/tR7HBErZymcXHEftSEHHODf2DAMWDyFM76OfUpxO5Nl
IutKc5noXxaiIxlwKz1O59Sf2Fro2pKgYvyZv1AnrZBcQGrM+gGgKFmztPQ/UDNh
itL8CaRNmkAYM82MMt0NsMoi3VLlDAz3Hn5p1GNcyEREOCruxT4=
=ZL6p
-----END PGP SIGNATURE-----

```
