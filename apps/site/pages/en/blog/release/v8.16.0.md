---
date: '2019-04-16T22:06:08.455Z'
category: release
title: Node v8.16.0 (LTS)
layout: blog-post
author: <PERSON><PERSON>
---

### Notable Changes

- **n-api**:
  - add API for asynchronous functions (<PERSON>) [#17887](https://github.com/nodejs/node/pull/17887)
  - mark thread-safe function as stable (<PERSON>) [#25556](https://github.com/nodejs/node/pull/25556)

### Commits

- [[`705935d620`](https://github.com/nodejs/node/commit/705935d620)] - **assert**: fix backport regression (<PERSON><PERSON>) [#27202](https://github.com/nodejs/node/pull/27202)
- [[`c07ba9681f`](https://github.com/nodejs/node/commit/c07ba9681f)] - **build**: skip cctest on Windows shared lib build (<PERSON><PERSON>) [#21228](https://github.com/nodejs/node/pull/21228)
- [[`63522886ea`](https://github.com/nodejs/node/commit/63522886ea)] - **build**: add loader path to rpath for cctest (Sam Ruby) [#23168](https://github.com/nodejs/node/pull/23168)
- [[`e9369073d9`](https://github.com/nodejs/node/commit/e9369073d9)] - **build**: set `-blibpath:` for AIX (Richard Lau) [#25447](https://github.com/nodejs/node/pull/25447)
- [[`97cc0fc51d`](https://github.com/nodejs/node/commit/97cc0fc51d)] - **deps**: V8: cherry-pick 3cc6919 (Farazmand) [#25874](https://github.com/nodejs/node/pull/25874)
- [[`a1aff28fba`](https://github.com/nodejs/node/commit/a1aff28fba)] - **deps**: cherry-pick 525b396 from V8 upstream (Peter Marshall) [#25041](https://github.com/nodejs/node/pull/25041)
- [[`6b7cccc88a`](https://github.com/nodejs/node/commit/6b7cccc88a)] - **doc**: fix optional parameters in n-api.md (Lars-Magnus Skog) [#22998](https://github.com/nodejs/node/pull/22998)
- [[`b17819db3d`](https://github.com/nodejs/node/commit/b17819db3d)] - **doc**: update the http.request.setTimeout docs to be accurate (James Bunton) [#25123](https://github.com/nodejs/node/pull/25123)
- [[`ac9b8f7645`](https://github.com/nodejs/node/commit/ac9b8f7645)] - **http**: fix error check in `Execute()` (Brian White) [#24738](https://github.com/nodejs/node/pull/24738)
- [[`1d862610f8`](https://github.com/nodejs/node/commit/1d862610f8)] - **http**: attach reused parser to correct domain (Julien Gilli) [#25459](https://github.com/nodejs/node/pull/25459)
- [[`d3de1ed653`](https://github.com/nodejs/node/commit/d3de1ed653)] - **n-api**: improve performance creating strings (Anthony Tuininga) [#26439](https://github.com/nodejs/node/pull/26439)
- [[`2b2ad96ef2`](https://github.com/nodejs/node/commit/2b2ad96ef2)] - **n-api**: finalize during second-pass callback (Gabriel Schulhof) [#25992](https://github.com/nodejs/node/pull/25992)
- [[`d6ffabc37f`](https://github.com/nodejs/node/commit/d6ffabc37f)] - **(SEMVER-MINOR)** **n-api**: mark thread-safe function as stable (Gabriel Schulhof) [#25556](https://github.com/nodejs/node/pull/25556)
- [[`44609d1274`](https://github.com/nodejs/node/commit/44609d1274)] - **n-api**: restrict exports by version (Kyle Farnung) [#19962](https://github.com/nodejs/node/pull/19962)
- [[`fe4328252a`](https://github.com/nodejs/node/commit/fe4328252a)] - **n-api**: add missing handle scopes (Daniel Bevenius) [#24011](https://github.com/nodejs/node/pull/24011)
- [[`902b07959f`](https://github.com/nodejs/node/commit/902b07959f)] - **n-api**: clean up thread-safe function (Gabriel Schulhof) [#22259](https://github.com/nodejs/node/pull/22259)
- [[`09b88aabb3`](https://github.com/nodejs/node/commit/09b88aabb3)] - **n-api**: remove idle_running from TsFn (Lars-Magnus Skog) [#22520](https://github.com/nodejs/node/pull/22520)
- [[`367505940a`](https://github.com/nodejs/node/commit/367505940a)] - **n-api**: guard against cond null dereference (Gabriel Schulhof) [#21871](https://github.com/nodejs/node/pull/21871)
- [[`c5a11dc58e`](https://github.com/nodejs/node/commit/c5a11dc58e)] - **n-api**: fix compiler warning (cjihrig) [#21597](https://github.com/nodejs/node/pull/21597)
- [[`759a0180b5`](https://github.com/nodejs/node/commit/759a0180b5)] - **(SEMVER-MINOR)** **n-api**: add API for asynchronous functions (Gabriel Schulhof) [#17887](https://github.com/nodejs/node/pull/17887)
- [[`ea5628e77a`](https://github.com/nodejs/node/commit/ea5628e77a)] - **process**: allow reading from stdout/stderr sockets (Anna Henningsen) [#23053](https://github.com/nodejs/node/pull/23053)
- [[`67b6e0d19c`](https://github.com/nodejs/node/commit/67b6e0d19c)] - **src**: fix may be uninitialized warning in n-api (Michael Dawson) [#21898](https://github.com/nodejs/node/pull/21898)
- [[`eaf474cc5d`](https://github.com/nodejs/node/commit/eaf474cc5d)] - **test**: shared lib build doesn't handle SIGPIPE (Yihong Wang) [#19211](https://github.com/nodejs/node/pull/19211)
- [[`3128cb7da6`](https://github.com/nodejs/node/commit/3128cb7da6)] - **test**: avoid running fsync on directory on AIX (John Barboza) [#21298](https://github.com/nodejs/node/pull/21298)
- [[`b4c5435a46`](https://github.com/nodejs/node/commit/b4c5435a46)] - **test**: add process.stdin.end() TTY regression test (Matteo Collina) [#23051](https://github.com/nodejs/node/pull/23051)
- [[`c56f3edb10`](https://github.com/nodejs/node/commit/c56f3edb10)] - **test**: add stdin writable regression test (Anna Henningsen) [#23053](https://github.com/nodejs/node/pull/23053)
- [[`f6ff8c51bc`](https://github.com/nodejs/node/commit/f6ff8c51bc)] - **test**: fix module loading error for AIX 7.1 (Richard Lau) [#25418](https://github.com/nodejs/node/pull/25418)
- [[`d4b6643ac3`](https://github.com/nodejs/node/commit/d4b6643ac3)] - **test**: mark test-cli-node-options flaky on arm (Rich Trott) [#25032](https://github.com/nodejs/node/pull/25032)
- [[`60db455961`](https://github.com/nodejs/node/commit/60db455961)] - **test**: mark test_threadsafe_function/test as flaky (Gireesh Punathil) [#24714](https://github.com/nodejs/node/pull/24714)
- [[`fbafe8d311`](https://github.com/nodejs/node/commit/fbafe8d311)] - **test**: fix test-repl-envvars (Anna Henningsen) [#25226](https://github.com/nodejs/node/pull/25226)
- [[`7573b55a15`](https://github.com/nodejs/node/commit/7573b55a15)] - **tls**: fix legacy SecurePair clienthello race window (Ben Noordhuis) [#26452](https://github.com/nodejs/node/pull/26452)
- [[`91620b8bd6`](https://github.com/nodejs/node/commit/91620b8bd6)] - **tls**: fix legacy SecurePair session resumption (Ben Noordhuis) [#26452](https://github.com/nodejs/node/pull/26452)
- [[`1a9582b7a6`](https://github.com/nodejs/node/commit/1a9582b7a6)] - **tools**: allow input for TTY tests (Anna Henningsen) [#23053](https://github.com/nodejs/node/pull/23053)

Windows 32-bit Installer: https://nodejs.org/dist/v8.16.0/node-v8.16.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v8.16.0/node-v8.16.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v8.16.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v8.16.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v8.16.0/node-v8.16.0.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v8.16.0/node-v8.16.0-darwin-x64.tar.gz \
Linux 32-bit Binary: https://nodejs.org/dist/v8.16.0/node-v8.16.0-linux-x86.tar.xz \
Linux 64-bit Binary: https://nodejs.org/dist/v8.16.0/node-v8.16.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v8.16.0/node-v8.16.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v8.16.0/node-v8.16.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v8.16.0/node-v8.16.0-aix-ppc64.tar.gz \
SmartOS 32-bit Binary: https://nodejs.org/dist/v8.16.0/node-v8.16.0-sunos-x86.tar.xz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v8.16.0/node-v8.16.0-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v8.16.0/node-v8.16.0-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v8.16.0/node-v8.16.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v8.16.0/node-v8.16.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v8.16.0/node-v8.16.0.tar.gz \
Other release files: https://nodejs.org/dist/v8.16.0/ \
Documentation: https://nodejs.org/docs/v8.16.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

fde4775ae5b8b2a19f1343629b6892da60eca0aebf517f840e345522a5c386b2  node-v8.16.0-aix-ppc64.tar.gz
a6710b8af0862fab0ccdba0549dbcdad76b5f99070652e64f6a85158038fc9a6  node-v8.16.0-darwin-x64.tar.gz
35e35fa23d5527aa1a9ec076864a5987288dc0c65318b2d8b714ff20beb303bf  node-v8.16.0-darwin-x64.tar.xz
83ebc7c97d0c6b9b26f4bc19ebdc3d29231c2a8c5462efeb51b14e5843079376  node-v8.16.0-headers.tar.gz
1ba3a82389d4c830e03985215354222fece040809cd65255a1256dccc84612b8  node-v8.16.0-headers.tar.xz
c0678bc942efe04805e229b557a5b4f82671f05f3325cc33d7c6ea2531d3ce96  node-v8.16.0-linux-arm64.tar.gz
4583d1cb44ff8b51cbf0402a78f2fe086c13a6c900c20c8be14e3b0e28e34335  node-v8.16.0-linux-arm64.tar.xz
b75990523df52e353cf6c14810531e6eb17286602335befc921ec20f4efee21a  node-v8.16.0-linux-armv6l.tar.gz
3f2387eacce66fca46d43860ed8be3a27c2b44d3e921064b340311ca3e1eef4a  node-v8.16.0-linux-armv6l.tar.xz
1be646c6b5b84034c4e4a20254044c5d3f1dc258860f99c2893d00fe965486ac  node-v8.16.0-linux-armv7l.tar.gz
e5e1d69c3a974f2346663bca73cbd3a4a8b93246e3e149241947d3736a1dd88a  node-v8.16.0-linux-armv7l.tar.xz
52b8989c81f7d2cf1250e682a0132e17d08536d458c2e6058e9d5e525032879d  node-v8.16.0-linux-ppc64le.tar.gz
a68f169eaf40166888c12578f99c1524bcfd4e5223e7156a5658c8d093cc23a7  node-v8.16.0-linux-ppc64le.tar.xz
d41bf13aa5bc845ef0a14af811c3b1ecaf3edc03a341eea4e9af28fc9093d174  node-v8.16.0-linux-s390x.tar.gz
521c02098b23bf86958bc8ba428ebc52ecf5e0fd1f69d88562f2700e3b9df164  node-v8.16.0-linux-s390x.tar.xz
b391450e0fead11f61f119ed26c713180cfe64b363cd945bac229130dfab64fa  node-v8.16.0-linux-x64.tar.gz
e538ffaaf2f808c084e70f1a1d2ff5559cff892cfd56e0bb67d00b0a95fc3a7a  node-v8.16.0-linux-x64.tar.xz
b616582589101df7fae465e052f016980c2a733ca1b2ee1a1d46ff2fe594a0e8  node-v8.16.0-linux-x86.tar.gz
b0f6de54151ab2c8ca64af15012dbf5f1902293e4d6efb8aac98de1eda1cb8af  node-v8.16.0-linux-x86.tar.xz
6ae83b06031b375330f869ae77f522e5a629a4f3dbf8235e299b4e50ea18cce9  node-v8.16.0.pkg
618387ed83339d2fec57500f249e2a1425aaba03f63875b17654bd954bd73f17  node-v8.16.0-sunos-x64.tar.gz
c2ebe8159651f598339072e9fd20414802bbffb707ee13689101aecb40248901  node-v8.16.0-sunos-x64.tar.xz
cf069297ca7d7dc5f6776fb2a3e39c5c8fa7b02f54a2f1daa7139163682db384  node-v8.16.0-sunos-x86.tar.gz
2198fc874019778feb4da43bfdf8a89739269e734797d597400f6bd8e4bbe5a2  node-v8.16.0-sunos-x86.tar.xz
a1a885add3e511177f05676f0834df710886b68cb559b893169f8674a23adfcf  node-v8.16.0.tar.gz
3515e8e01568a5dc4dff3d91a76ebc6724f5fa2fbb58b4b0c5da7b178a2f7340  node-v8.16.0.tar.xz
0dd9dc3c0713d59a6a45c1eb39198efb38d2ccc8be1946710d43163a6f12626c  node-v8.16.0-win-x64.7z
d6baa929bacb78b347b29ebb0263220ea649ce82f3cdfd3f0b41ac725d1bbba5  node-v8.16.0-win-x64.zip
e3036ab4155535eff798c4ca9a21534c9954ecc9ab9e14de45c74bc386b6b162  node-v8.16.0-win-x86.7z
e91beb197a3c0f6da0711eb821018214de39c60e4561b376eceaf966a3c23d18  node-v8.16.0-win-x86.zip
ed1ca34dc2458b72a96c638405b8833bc9ab1daee99accec498cf395b70eaf55  node-v8.16.0-x64.msi
082154c71992355384be8e8d402771ccce72027e07098d99fd56945fba3fa036  node-v8.16.0-x86.msi
e8971ac21e30f3eff08a019314989c3cf19d54f383ec1394064b16dc3b332982  win-x64/node.exe
9fcf52fbc2386a6250df1cd45a6d1bf498936f35c92b31459fa88fe11df254b6  win-x64/node.lib
6977635bff6b5025c3a7145f41a4f166c676757bfde234662564dfcbd2ff1771  win-x64/node_pdb.7z
871c2667f5ed002264a6e737979652000608b3030d3d65f2aa19033bdef1af66  win-x64/node_pdb.zip
d9f634f6dc7dbf4ad142ab5271907cefe816cee23c5eaf4df739db4c6593cd8d  win-x86/node.exe
0bdc3313fb9281f9ef5e0a125ee44f480db1409cc3c6472fceaba5eddd818321  win-x86/node.lib
09f580b780d615a4b8d70490aafa757b459087a1290d4e10ac98c1c16731623c  win-x86/node_pdb.7z
4a4a25424d35f17abe7825b0ddcea29f713b8ee65ee5c4073757781b2ca9a62e  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQEzBAEBCAAdFiEEDv/hvO/ZyE49CYFSkzsB9AtcqUYFAly2Ub0ACgkQkzsB9Atc
qUZzIAf/VpJwTMchNRQaHKoyYXOwYTOvth3Nyc4NZtfNS/FTbFoUKAlaE3gDq2xr
V2tcGj9tPOeuMbv1WiGjlY4bsHhZZ3H3vRfqU/VtsA+okOrRm+eXn7sE3HLyh8XX
7ZAkvjTGYwC95GahH3VKRFEh9pKJ5K5Z2kJrten6a/rWvF3HOtoqNCWVd3hEqMMN
wkWGma/pMmOFrP6agUUBrCronSNBHTonnlwwLXC+fA8u2VNYAX85SSNE8iX/MOSO
PqobJkPU0vXpMANlrEOBRCBBGWIUAOyZTP6fFMLpWoPWANR9l7S9/UqLk+h9/XRv
1+Rxd1GVMjSdrW1dPYcL64++eeRp+w==
=V1no
-----END PGP SIGNATURE-----

```
