---
date: '2017-03-02T14:23:17.071Z'
category: release
title: Node v7.7.1 (Current)
layout: blog-post
author: <PERSON><PERSON> <PERSON><PERSON>
---

### Notable changes

Node.js 7.7.0 contains a bug that will prevent all native modules from building, this patch should fix the issue. Apologies to everyone who was affected by 7.7.0.

### Commits

- [[`c8e34b61f6`](https://github.com/nodejs/node/commit/c8e34b61f6)] - **build**: add missing src/tracing header files (<PERSON>) [#10851](https://github.com/nodejs/node/pull/10851)
- [[`96f55f9e59`](https://github.com/nodejs/node/commit/96f55f9e59)] - **src**: move trace_event.h include to internal header (<PERSON>) [#10959](https://github.com/nodejs/node/pull/10959)
- [[`30c80cbe6f`](https://github.com/nodejs/node/commit/30c80cbe6f)] - **src**: fix TracingController cleanup (<PERSON>) [#10623](https://github.com/nodejs/node/pull/10623)
- [[`b89b2a7d36`](https://github.com/nodejs/node/commit/b89b2a7d36)] - **src**: always initialize tracing controller in agent (Matt Loring) [#10507](https://github.com/nodejs/node/pull/10507)
- [[`54e55e05ca`](https://github.com/nodejs/node/commit/54e55e05ca)] - **test**: make test-intl-no-icu-data more robust (Michaël Zasso) [#10992](https://github.com/nodejs/node/pull/10992)
- [[`7b253eb3ed`](https://github.com/nodejs/node/commit/7b253eb3ed)] - **test**: increase strictness for test-trace-event (Rich Trott) [#11065](https://github.com/nodejs/node/pull/11065)
- [[`3dc4a5f1f4`](https://github.com/nodejs/node/commit/3dc4a5f1f4)] - **tracing**: fix -Wunused-private-field warning (Santiago Gimeno) [#10416](https://github.com/nodejs/node/pull/10416)
- [[`8a918bf411`](https://github.com/nodejs/node/commit/8a918bf411)] - **tracing**: fix -Wreorder warning (Santiago Gimeno) [#10416](https://github.com/nodejs/node/pull/10416)

Windows 32-bit Installer: https://nodejs.org/dist/v7.7.1/node-v7.7.1-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v7.7.1/node-v7.7.1-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v7.7.1/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v7.7.1/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v7.7.1/node-v7.7.1.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v7.7.1/node-v7.7.1-darwin-x64.tar.gz \
Linux 32-bit Binary: https://nodejs.org/dist/v7.7.1/node-v7.7.1-linux-x86.tar.xz \
Linux 64-bit Binary: https://nodejs.org/dist/v7.7.1/node-v7.7.1-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v7.7.1/node-v7.7.1-linux-ppc64le.tar.xz \
Linux PPC BE 64-bit Binary: https://nodejs.org/dist/v7.7.1/node-v7.7.1-linux-ppc64.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v7.7.1/node-v7.7.1-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v7.7.1/node-v7.7.1-aix-ppc64.tar.gz \
SmartOS 32-bit Binary: https://nodejs.org/dist/v7.7.1/node-v7.7.1-sunos-x86.tar.xz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v7.7.1/node-v7.7.1-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v7.7.1/node-v7.7.1-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v7.7.1/node-v7.7.1-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v7.7.1/node-v7.7.1-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v7.7.1/node-v7.7.1.tar.gz \
Other release files: https://nodejs.org/dist/v7.7.1/ \
Documentation: https://nodejs.org/docs/v7.7.1/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

c6ae32c741a007b443092a85f5dfd5b244a1c9eaf3f0cc27052d3d2017019953  node-v7.7.1-aix-ppc64.tar.gz
b554032d488fdabb6ec9753fa2841bdf074808203595454c1fe480a9ce7dc068  node-v7.7.1-darwin-x64.tar.gz
0e9fbc473e3deb1274f979dc8447519b00f673611d4ee9d78c3c04ab08ded34d  node-v7.7.1-darwin-x64.tar.xz
65c6173d4e3a94a22f1bbbe3b69104d9c6134289d2417d857f958ef635159805  node-v7.7.1-headers.tar.gz
277c32f5f45c15a4f1f1aa08007dcf333413f3a1dc39cf989226a1c0420395d2  node-v7.7.1-headers.tar.xz
3eb8d507eaa4ea1e01b863d95c268e0d55b780563696867d682adbad968cc18e  node-v7.7.1-linux-arm64.tar.gz
a130783c14d8487b5905d49bcffba9396e19276cd1cd86b376ba2a9a55c385e2  node-v7.7.1-linux-arm64.tar.xz
e1ce01664fe628d24176ddc63204cb2b703015d0f70dadedc39fcc734245a345  node-v7.7.1-linux-armv6l.tar.gz
81ace4feb565b3e6f2df5e57adbb6d3595d47f337ae9bbd8d9e53c5f2e48b252  node-v7.7.1-linux-armv6l.tar.xz
6521ac2d6188256a92ffe6be8af37ae4c1dc8f3e155dec7c932d6c09d752c87d  node-v7.7.1-linux-armv7l.tar.gz
343b105f3b1f9fb63dfb3272b1018f6f13f3ff5194b09bf454985cd4b0011bad  node-v7.7.1-linux-armv7l.tar.xz
f1e6fc224349aee3715b603a3fbe9f3ff1a386cede4def0590a4935e26880674  node-v7.7.1-linux-ppc64le.tar.gz
42d1c80a5f6dc057c50bbc01ef60588d6d4f320683f985367674de362f0be88e  node-v7.7.1-linux-ppc64le.tar.xz
23416b1c1eb23c8c7dd8698d5e537d1f88bb1b7c1dd90563359b6e59de20b2a0  node-v7.7.1-linux-ppc64.tar.gz
203bfe4533c3210872416babc915ed913ea4eef7fb83cd5ff315e3221eedb0ac  node-v7.7.1-linux-ppc64.tar.xz
949530ca9051e0e009914a14b675634a87908fbaa74cba36df7a7b937eeefe7b  node-v7.7.1-linux-s390x.tar.gz
f06cd6f8d4d87d74af62747c67a62a900724d198440371d4687ab462915dc6fd  node-v7.7.1-linux-s390x.tar.xz
26524c315f20062d625410357415e1b5069cabedfe51ebb3a0c7ad5d01420068  node-v7.7.1-linux-x64.tar.gz
b5a88d7b2dd0d116fdcad5767521cb1a17672405ccb1c341c351d8315c1f9db2  node-v7.7.1-linux-x64.tar.xz
9a61259bfb85b5a1a9cb12ae607af94d00bee170de58f84d751993eb9728fda2  node-v7.7.1-linux-x86.tar.gz
6cf12d0c8e1da5882f8efcf7f8f6b72cd101fc54e34f18c357e0681fdd2c25f7  node-v7.7.1-linux-x86.tar.xz
f90287cc460ec5db85a645e7e2d93c054e0a5c8a142384e42b3d81a2c875374e  node-v7.7.1.pkg
06924e708a4349a3d440d111f5d76985ad232332f64f72bcb0538e3e5a0780b8  node-v7.7.1-sunos-x64.tar.gz
b75c9a6b07883f07b95a3cfa91d8f15d53aee66d4c1d7be788dc03939e109485  node-v7.7.1-sunos-x64.tar.xz
2090d64748defed4c5d67272b496311a7ddff5517098d51404847a408a43f1e6  node-v7.7.1-sunos-x86.tar.gz
847ae98b01aab5d4a3a9dff96d01c24c7c6aac4d1435cb840d79e15339674781  node-v7.7.1-sunos-x86.tar.xz
9e87ec5420d558ca9651d13b10dd4a1be954fea0fc7a909016a1cc4aedfa651c  node-v7.7.1.tar.gz
965fc82aa767223be574e41d7f78ec4bd2ab3da619cef1256e46c30d053b7611  node-v7.7.1.tar.xz
b17cae9092d3c8e23d5977b9740059c14076ca8defda4dabe7242156a3466126  node-v7.7.1-win-x64.7z
d907f7cd20aff2e9ae087b0293b9c85ad3a61ea6ae6b1bdaa8f40696a6347878  node-v7.7.1-win-x64.zip
9f307a3a975eeedb7fb930e474e0e4c8350569bb3e4f96208572aae2bd0add2a  node-v7.7.1-win-x86.7z
2bd6d27c2fb4b708e88d976eeb4c15bb059de468e943d08ddec10ccf432b6122  node-v7.7.1-win-x86.zip
283a71889531f942664967e91e554036deb53effbbca663b8e5c079045cd6be6  node-v7.7.1-x64.msi
9947a4b357f9d0300bbc4310c74d2444b5cb31f95fff7706386a67417ade00d8  node-v7.7.1-x86.msi
f6ca56c1d659354d8a41fa3d5e13ae1be2be950bc890b6ee79274949bef1c33c  win-x64/node.exe
823dc13ecd98fe5aa3ccf27ffd7eaf015c1244f005d2d755d7126c50426bee6b  win-x64/node.lib
eb94364ad62c661ef62a306fa0eb9789f57d5cbaa5f2cd25ec5c395f1bf1a892  win-x64/node_pdb.7z
0b634161a40048d89fae2276828358426d8a4541394a5cea0a1a1ce97d8865ff  win-x64/node_pdb.zip
95e2ac059dd687a9c1079bda0b38761c6e8078edfdf6fe84d12477f2319d75a7  win-x86/node.exe
0445a2d3ecf702f6ddbe5fc05c307109cf4630537f1daa3e56efb549ef233a01  win-x86/node.lib
1b17d0b8e5dbf0e8eeb14b0f3d0bc11f98bfa9f241f3eee8536434231ab35bc1  win-x86/node_pdb.7z
505c0a62435610c94722ce9364d3571186a5e1ffb0d254793951d66ccf8bbd2c  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----
Version: GnuPG v2

iQIcBAEBCAAGBQJYt5BvAAoJECPv7+k8TP/+l+MQAItgLj4sNiQ2aNXTXVqPkUO4
fY6/w207txcfh8N8e7TjDfXAj0Lz5mhDg496uc6J8yFFAeX0FmBQ9Hrc1BdFGQyN
cfBpx+EvMTYrnEtL0923Ujkdxm/zRuvqrOUryUraY7LvZX35+gcBOClfUFUMLx+w
ppPSZKdJPwm4o4TQ1/u32zrp8IPO9Ri3EjavTK41HR+k79BtFZ2L2wWGr58BnDKu
iUV4D1f6nzR2vwxVvK9f3uf++xDkYHDx49munWiYwdUPFR+WHJAkQKpBAlfcnkSl
khgiBlYrvXP4QmjTYeNW4gUXtPPkm22m7fx7MMstSMKht34dDV/r2QkLsGZLyZb2
MRA09zqeJpIbwBdZHcMJzNkrl5xMtQHPMJ0SIHR+mhOqhVJbYjIYst/s0vuERLPq
2JLQFvlxhwMkAdz48i3IFtHk6wg4N+N6JgN0bncukuA8YICA68zXVQP2/KfQwS44
hL9EYPvES/K7IV/QY8k46vNdamj/5Ya3+dm2+guoWZfe9n/K5yPoYbxBPCzUd5ph
fwIN5+1UwUUhkLq4hutjOTKxfVzA1dFFxEaZa5bl4RTfrKs84BSaGW/xc0rReU5+
wHIxaKNEpDoJHaAsYfbTkprwG6Fz7uzvqumyYGwEBHHy6UeIM+eYLN1z2fB19Ach
RcA2ACuDibdUHN2OI1Nz
=5VJx
-----END PGP SIGNATURE-----

```
