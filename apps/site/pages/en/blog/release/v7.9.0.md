---
date: '2017-04-11T19:22:46.487Z'
category: release
title: Node v7.9.0 (Current)
layout: blog-post
author: <PERSON><PERSON> <PERSON><PERSON>
---

### Notable Changes

- **util**: console is now closer to what is supported in all major browsers (<PERSON>) [#10308](https://github.com/nodejs/node/pull/10308)

### Commits

- [[`9f73df5910`](https://github.com/nodejs/node/commit/9f73df5910)] - **deps**: cherry-pick 22858cb from V8 upstream (<PERSON>) [#11998](https://github.com/nodejs/node/pull/11998)
- [[`b997e62692`](https://github.com/nodejs/node/commit/b997e62692)] - **test**: add internal/socket_list tests (DavidCai) [#12109](https://github.com/nodejs/node/pull/12109)
- [[`c11c23b22b`](https://github.com/nodejs/node/commit/c11c23b22b)] - **doc**: make the heading consistent (<PERSON><PERSON><PERSON><PERSON><PERSON> (thefourtheye)) [#11569](https://github.com/nodejs/node/pull/11569)
- [[`67d21149a2`](https://github.com/nodejs/node/commit/67d21149a2)] - **crypto**: handle exceptions in hmac/hash.digest (Tobias Nießen) [#12164](https://github.com/nodejs/node/pull/12164)
- [[`3b765f5366`](https://github.com/nodejs/node/commit/3b765f5366)] - **doc**: fix confusing example in process.md (Vse Mozhet Byt) [#12282](https://github.com/nodejs/node/pull/12282)
- [[`37568c093a`](https://github.com/nodejs/node/commit/37568c093a)] - **src**: use std::list for at_exit_functions (Daniel Bevenius) [#12255](https://github.com/nodejs/node/pull/12255)
- [[`2f9e2fcf3e`](https://github.com/nodejs/node/commit/2f9e2fcf3e)] - **doc**: update information on test/known_issues (Jan Krems) [#12262](https://github.com/nodejs/node/pull/12262)
- [[`0f4319a14a`](https://github.com/nodejs/node/commit/0f4319a14a)] - **src**: use std::string for trace enabled_categories (Sam Roberts) [#12242](https://github.com/nodejs/node/pull/12242)
- [[`6826637f11`](https://github.com/nodejs/node/commit/6826637f11)] - **doc**: fix missing argument for dns.resolvePtr() (Uppinder Chugh) [#12256](https://github.com/nodejs/node/pull/12256)
- [[`4a6bb378d4`](https://github.com/nodejs/node/commit/4a6bb378d4)] - **doc**: fix confusing reference in net.md (Vse Mozhet Byt) [#12247](https://github.com/nodejs/node/pull/12247)
- [[`3e8991cc56`](https://github.com/nodejs/node/commit/3e8991cc56)] - **doc**: modernize and fix code examples in modules.md (Vse Mozhet Byt) [#12224](https://github.com/nodejs/node/pull/12224)
- [[`376f5ef1ee`](https://github.com/nodejs/node/commit/376f5ef1ee)] - **doc**: document the performance team (Gibson Fahnestock) [#12213](https://github.com/nodejs/node/pull/12213)
- [[`c0b7c075da`](https://github.com/nodejs/node/commit/c0b7c075da)] - **doc**: add refack to collaborators (Refael Ackermann) [#12277](https://github.com/nodejs/node/pull/12277)
- [[`83f855d505`](https://github.com/nodejs/node/commit/83f855d505)] - **doc**: add aqrln to collaborators (Alexey Orlenko) [#12273](https://github.com/nodejs/node/pull/12273)
- [[`2fb2289177`](https://github.com/nodejs/node/commit/2fb2289177)] - **doc**: add sub domain to host in url (Steven) [#12233](https://github.com/nodejs/node/pull/12233)
- [[`ac200a6122`](https://github.com/nodejs/node/commit/ac200a6122)] - **test**: add a second argument to assert.throws() (dave-k) [#12139](https://github.com/nodejs/node/pull/12139)
- [[`3cdd04b1c0`](https://github.com/nodejs/node/commit/3cdd04b1c0)] - **test**: skip irrelevant test on Windows (Rich Trott) [#12261](https://github.com/nodejs/node/pull/12261)
- [[`d4d6986551`](https://github.com/nodejs/node/commit/d4d6986551)] - **build**: fix path voodoo in icu-generic.gyp (Refael Ackermann) [#11217](https://github.com/nodejs/node/pull/11217)
- [[`a735c16d52`](https://github.com/nodejs/node/commit/a735c16d52)] - **deps**: backport ec1ffe3 from upstream V8 (Daniel Bevenius) [#12061](https://github.com/nodejs/node/pull/12061)
- [[`d641164d09`](https://github.com/nodejs/node/commit/d641164d09)] - **doc**: update pull request template URL layout (Rich Trott) [#12216](https://github.com/nodejs/node/pull/12216)
- [[`6feea08587`](https://github.com/nodejs/node/commit/6feea08587)] - **buffer**: preallocate array with buffer length (alejandro) [#11733](https://github.com/nodejs/node/pull/11733)
- [[`a703bdecc4`](https://github.com/nodejs/node/commit/a703bdecc4)] - **build**: add checks for openssl configure options (Daniel Bevenius) [#12175](https://github.com/nodejs/node/pull/12175)
- [[`b495b6acdf`](https://github.com/nodejs/node/commit/b495b6acdf)] - **build**: make configure print statements consistent (Daniel Bevenius) [#12176](https://github.com/nodejs/node/pull/12176)
- [[`f60b4553f3`](https://github.com/nodejs/node/commit/f60b4553f3)] - **doc**: modernize and fix code examples in https.md (Vse Mozhet Byt) [#12171](https://github.com/nodejs/node/pull/12171)
- [[`74d0266694`](https://github.com/nodejs/node/commit/74d0266694)] - **doc**: fix string interpolation in Stream 'finish' (Vinay Hiremath) [#12221](https://github.com/nodejs/node/pull/12221)
- [[`4b54520a4a`](https://github.com/nodejs/node/commit/4b54520a4a)] - **test**: refactor mkdtemp test and added async (Luca Maraschi) [#12080](https://github.com/nodejs/node/pull/12080)
- [[`8caf6fd58a`](https://github.com/nodejs/node/commit/8caf6fd58a)] - **test**: add Unicode characters regression test (Alexey Orlenko) [#11423](https://github.com/nodejs/node/pull/11423)
- [[`961c89cc61`](https://github.com/nodejs/node/commit/961c89cc61)] - **doc**: add table of contents to README.md (Jason Marsh) [#11635](https://github.com/nodejs/node/pull/11635)
- [[`a11ed6a0b3`](https://github.com/nodejs/node/commit/a11ed6a0b3)] - **test**: more robust check for location of `node.exe` (Refael Ackermann) [#12120](https://github.com/nodejs/node/pull/12120)
- [[`6083e7aa7b`](https://github.com/nodejs/node/commit/6083e7aa7b)] - **benchmark**: avoid TurboFan deopt in arrays bench (Michaël Zasso) [#11894](https://github.com/nodejs/node/pull/11894)
- [[`cf1117bc13`](https://github.com/nodejs/node/commit/cf1117bc13)] - **doc**: fix the timing of setImmediate's execution (Daiki Arai) [#12034](https://github.com/nodejs/node/pull/12034)
- [[`806c4f3c0c`](https://github.com/nodejs/node/commit/806c4f3c0c)] - **doc**: fix fs.read arg type (Daiki Arai) [#12034](https://github.com/nodejs/node/pull/12034)
- [[`c814c7e9ea`](https://github.com/nodejs/node/commit/c814c7e9ea)] - **events**: do not keep arrays with a single listener (Luigi Pinca) [#12043](https://github.com/nodejs/node/pull/12043)
- [[`36617fd5b8`](https://github.com/nodejs/node/commit/36617fd5b8)] - **doc**: add notes to http.get options (Raphael Okon) [#12124](https://github.com/nodejs/node/pull/12124)
- [[`9e6b0a4604`](https://github.com/nodejs/node/commit/9e6b0a4604)] - **test**: performance, remove Popen(shell=True) on Win (Refael Ackermann) [#12138](https://github.com/nodejs/node/pull/12138)
- [[`805ebef8b1`](https://github.com/nodejs/node/commit/805ebef8b1)] - **buffer**: optimize decoding wrapped base64 data (Alexey Orlenko) [#12146](https://github.com/nodejs/node/pull/12146)
- [[`fb34d9c210`](https://github.com/nodejs/node/commit/fb34d9c210)] - **test**: increase querystring coverage (DavidCai) [#12163](https://github.com/nodejs/node/pull/12163)
- [[`d6e9cf7c22`](https://github.com/nodejs/node/commit/d6e9cf7c22)] - **doc**: fix and update examples in http.md (Vse Mozhet Byt) [#12169](https://github.com/nodejs/node/pull/12169)
- [[`f057cc3d84`](https://github.com/nodejs/node/commit/f057cc3d84)] - **benchmark**: replace \[\].join() with ''.repeat() (Vse Mozhet Byt) [#12170](https://github.com/nodejs/node/pull/12170)
- [[`b15dc95848`](https://github.com/nodejs/node/commit/b15dc95848)] - **test**: fix flaky test-child-process-exec-timeout (Santiago Gimeno) [#12159](https://github.com/nodejs/node/pull/12159)
- [[`72a27b3eb5`](https://github.com/nodejs/node/commit/72a27b3eb5)] - **build**: use $(RM) in Makefile for consistency (Gibson Fahnestock) [#12157](https://github.com/nodejs/node/pull/12157)
- [[`3af9101d20`](https://github.com/nodejs/node/commit/3af9101d20)] - **doc, inspector**: note that the host is optional (Gibson Fahnestock) [#12149](https://github.com/nodejs/node/pull/12149)
- [[`b52b3f6710`](https://github.com/nodejs/node/commit/b52b3f6710)] - **test**: reduce buffer size in buffer-creation test (Sakthipriyan Vairamani (thefourtheye)) [#11177](https://github.com/nodejs/node/pull/11177)
- [[`b5283f9d4b`](https://github.com/nodejs/node/commit/b5283f9d4b)] - **doc**: add logo to README (Roman Reiss) [#12148](https://github.com/nodejs/node/pull/12148)
- [[`305f822a36`](https://github.com/nodejs/node/commit/305f822a36)] - **net**: rename internal functions for readability (Joyee Cheung) [#11796](https://github.com/nodejs/node/pull/11796)
- [[`2f88de1ce3`](https://github.com/nodejs/node/commit/2f88de1ce3)] - **vm**: use SetterCallback to set func declarations (AnnaMag) [#12051](https://github.com/nodejs/node/pull/12051)
- [[`ffbcfdfe32`](https://github.com/nodejs/node/commit/ffbcfdfe32)] - **src**: fix base64 decoding (Nikolai Vavilov) [#11995](https://github.com/nodejs/node/pull/11995)
- [[`8823861d9d`](https://github.com/nodejs/node/commit/8823861d9d)] - **tools**: update dotfile whitelist in .gitignore (Michaël Zasso) [#12116](https://github.com/nodejs/node/pull/12116)
- [[`87ca9a6ffe`](https://github.com/nodejs/node/commit/87ca9a6ffe)] - **test**: fix flaky child-process-exec-kill-throws (Rich Trott) [#12111](https://github.com/nodejs/node/pull/12111)
- [[`fdf76d5aa0`](https://github.com/nodejs/node/commit/fdf76d5aa0)] - **tools**: add missing #include "unicode/putil.h" (Steven R. Loomis) [#12078](https://github.com/nodejs/node/pull/12078)
- [[`6130d547a0`](https://github.com/nodejs/node/commit/6130d547a0)] - **deps**: backport 8dde6ac from upstream V8 (Daniel Bevenius) [#12060](https://github.com/nodejs/node/pull/12060)
- [[`1ee38eb874`](https://github.com/nodejs/node/commit/1ee38eb874)] - **(SEMVER-MINOR)** **util**: add %i and %f formatting specifiers (Roman Reiss) [#10308](https://github.com/nodejs/node/pull/10308)
- [[`5ac719d0d2`](https://github.com/nodejs/node/commit/5ac719d0d2)] - **doc**: add deprecations page to docs toc (Michaël Zasso) [#12268](https://github.com/nodejs/node/pull/12268)

Windows 32-bit Installer: https://nodejs.org/dist/v7.9.0/node-v7.9.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v7.9.0/node-v7.9.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v7.9.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v7.9.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v7.9.0/node-v7.9.0.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v7.9.0/node-v7.9.0-darwin-x64.tar.gz \
Linux 32-bit Binary: https://nodejs.org/dist/v7.9.0/node-v7.9.0-linux-x86.tar.xz \
Linux 64-bit Binary: https://nodejs.org/dist/v7.9.0/node-v7.9.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v7.9.0/node-v7.9.0-linux-ppc64le.tar.xz \
Linux PPC BE 64-bit Binary: https://nodejs.org/dist/v7.9.0/node-v7.9.0-linux-ppc64.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v7.9.0/node-v7.9.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v7.9.0/node-v7.9.0-aix-ppc64.tar.gz \
SmartOS 32-bit Binary: https://nodejs.org/dist/v7.9.0/node-v7.9.0-sunos-x86.tar.xz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v7.9.0/node-v7.9.0-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v7.9.0/node-v7.9.0-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v7.9.0/node-v7.9.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v7.9.0/node-v7.9.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v7.9.0/node-v7.9.0.tar.gz \
Other release files: https://nodejs.org/dist/v7.9.0/ \
Documentation: https://nodejs.org/docs/v7.9.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

e2ea01c130677d5ac5ecea9b1591d285779a142d372000bda15655058864534f  node-v7.9.0-aix-ppc64.tar.gz
fbeff9aa20adfb69e0a2977578713ac0a33d20d895816af4c9eb9f75aa2b0c31  node-v7.9.0-darwin-x64.tar.gz
a6ca33b34163591e53baad876607ca12ebf03768dcb3e661b60269ff21b1b938  node-v7.9.0-darwin-x64.tar.xz
7954edd9650bb9caad67de26295968fb4aa6451606106632b2ba6c41d9180877  node-v7.9.0-headers.tar.gz
b1ca3fd5e01d4d195f8630ede09fcc0e1e1d4197c99dcc97cadbbcacea1dfbb8  node-v7.9.0-headers.tar.xz
97a4fdf9bdce13e6ff42109ac75564b480586203dbd9c621fa801f53db94484d  node-v7.9.0-linux-arm64.tar.gz
2dff3ee0c0a5e9b3adc6b8af8fb5a9f1fb47917a7e8538497a7125f50fc685d3  node-v7.9.0-linux-arm64.tar.xz
c3058ba99f18bf8db7798a171a0709fc3e4b1c1f6d80f0673496ac325c9a1911  node-v7.9.0-linux-armv6l.tar.gz
42adda34a8de3d0bde4307e18cbcab905e44d3a4d577bf5665ff84a83b2135b1  node-v7.9.0-linux-armv6l.tar.xz
18489a7cb44ddf91fdf7ec802a9cd2153fd00af22ed8124ae907e15613c1f592  node-v7.9.0-linux-armv7l.tar.gz
f68072767fea47171eb4bb660daaa5e8fee1c06596b3dd28b1743287923fcf2f  node-v7.9.0-linux-armv7l.tar.xz
8e80dd4f2fea8864d724d1ab61fca8476a05ae4c4423fa813d3d8b1ae72f6593  node-v7.9.0-linux-ppc64le.tar.gz
c55aa59ac82db8a5d1a9ae1028dd49b8d53831e77575a2afc519a4ab512e00af  node-v7.9.0-linux-ppc64le.tar.xz
68e634269c5872f58338f812dcdb5e879acf15dfd2ed7a970a102d932dc7e620  node-v7.9.0-linux-ppc64.tar.gz
a867ea2c5cfc2c954002c226ea041a8888e81cfe5dab68ae7fb69b8a7c62ef70  node-v7.9.0-linux-ppc64.tar.xz
7ad687ca378bfbd693bd4dc0f6bd81bd85318d97b28ad9316b4e30d4e98f9211  node-v7.9.0-linux-s390x.tar.gz
882986f4aaae24b531da482914d1e177253ef0f33dbb386a156f1f38585e4986  node-v7.9.0-linux-s390x.tar.xz
62b35635c648befde8bf534b6086f7416b8c1a3ac0ff8a99c2d6773722829a0e  node-v7.9.0-linux-x64.tar.gz
d8910cf0dd90be84c07df179512cf2e36659a92726e67e8dc8bc8b457fe6e5ee  node-v7.9.0-linux-x64.tar.xz
cf0413a5119c8460215b932dd69449b33238538b0a86e4a7805747073a2be9aa  node-v7.9.0-linux-x86.tar.gz
a1178015504b81d1a42d72feebf80e3e7c96f3ff64ef17157e22f886c7d29e64  node-v7.9.0-linux-x86.tar.xz
cfd3edb13a3ff4020e75cc942da67aefdbae11944ecfc306cc255b7dfbf05373  node-v7.9.0.pkg
7ad260f909c8fd919917693b0cb9f493236a6bb21537feedcaa44c0941cc61a7  node-v7.9.0-sunos-x64.tar.gz
b6b4531111a940c279e65a9e17ef2851f787c486c17b6668e2f86314e35a2987  node-v7.9.0-sunos-x64.tar.xz
6ae38c43ea91858974077d733d298548b3bfe15cf004ec280035a3e618ed313a  node-v7.9.0-sunos-x86.tar.gz
671e8be01b40515b8aa8061d8c4a317403571fb51863661f76cf30810476958d  node-v7.9.0-sunos-x86.tar.xz
5cc131bb16f7ca688c29634a18b3c6a7816609a431a509f31baedf7caf25d626  node-v7.9.0.tar.gz
a569764b884929f31a0772600a5cf36048ad42aa8c54ba4cabea6c067ef96a29  node-v7.9.0.tar.xz
1611306e59558afd6502db2e39177429eb064b4c3ca0334c5d4a31204dd0a7ee  node-v7.9.0-win-x64.7z
67050bfa8fcb7679230078c14f291035640725a0edc564169cb6118bd62ccc70  node-v7.9.0-win-x64.zip
05778b0a58cb2b95c5864bc69dccc52f7238691079f264c9c04cecfa0e00d30f  node-v7.9.0-win-x86.7z
57f4f8919388019c95cf514203ee98daa761435d31406560b37a94d9cad0508c  node-v7.9.0-win-x86.zip
9722d751e9d5dd54e2f4384d4259486971b3f073f6cabd32066668d6abef4b17  node-v7.9.0-x64.msi
9fcf1dba6d6b07f7d969f001e4d0563fa8ac3b5487db8e0d61a71fb5c9b4a158  node-v7.9.0-x86.msi
aff221d258fc07f56cfea3235551be95d97ab2fb19905ec7e3afe10977b1bb7f  win-x64/node.exe
a4b2abc082d7a5d9d8855d460d403ded32e45b61d69745265aa1db168314415b  win-x64/node.lib
5b4e6d7c86d806530c467613c8a3a02528acf261a0d77fb696d2bfaca049d0e4  win-x64/node_pdb.7z
1f706cf0a7e958fa49231db94f0281824a7a784ee0e4ccdffe3c899bcb00edb4  win-x64/node_pdb.zip
c095d7863463a12d6b1db17a7eaa64379c880843c79370a6f0cb2c8ecd334f3d  win-x86/node.exe
0343725dc98d66d66adcbc435e726fec3797900caead18bae230da553416816b  win-x86/node.lib
cf97d513cfa55829d0f7195e5dec47f209ed10da1b7a7cf373f570a32f400d5c  win-x86/node_pdb.7z
df25d948663b253823b76abc2f8dc47d33898407898a83319b97097aa4145cd6  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIcBAEBCAAGBQJY7SsqAAoJECPv7+k8TP/++4wP/2FJURNAxxB4hM7aes0klkES
HO+YsEmqv4mVPoUHRrhQwtbvu1muTU3CYJWwIyVdqL5zNgIkU7C7KNI48Xn8NYaZ
mC9WGcf1guOtVeOXlJ19PZrwo2esJCFUel4z9x53e4KF6LoYHPx4F1Ea4tZYhohd
6R71xW/K8e/WP+M/ppBtt1qh2VRuhRlvlMzcbNEalQu43chg8Wg9t+8NxI5EED/8
7b4tpddCBRW3JmeSR9kq/jDvjH3WoiXVummWv8SsnhjYoB7S7O8h4dhYDDXEu3pr
JSbalruGhCB/xnRX8ToUnSop8yDTIoFPMJVLUlDalOJGWtT/4SOvNrMmymTX1OEA
naC75Ji3ACT35BJn0WRH8JVB24S7S7kGfOjjE5khKn4SVzV+gig9QlwLwkDmtTkH
kA7EMvZMRIELyS+jS5MwmArB0P+8NKB34GoU94lr7fmvgmZulbDiazi9aGvQ5miI
qCTnIrPNwt9F9NG/VplwkcdvEAo/y0EG2+mspYGN36ZHXy6tFA74k90p1nfEjLNu
oCLzGBzDjMmU6lCCXscHTrNjUNn61exjej8F9/QwjyARKJ+z7J47gyo9zT4KRtNF
2fOfG/CD3yb0sxo8DqZZlw5kgWwhPpJLVTqCI3QtJGJin8AJvngCVVx0X4gu5etQ
s5V0b4G1O7xkvlhfr+1/
=k4lg
-----END PGP SIGNATURE-----

```
