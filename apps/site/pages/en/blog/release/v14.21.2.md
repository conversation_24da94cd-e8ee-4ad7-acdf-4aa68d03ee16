---
date: '2022-12-13T13:05:04.156Z'
category: release
title: Node v14.21.2 (LTS)
layout: blog-post
author: <PERSON>
---

### Notable Changes

#### OpenSSL 1.1.1s

This OpenSSL version does not address any security vulnerabilities.

#### Root certificates updated to NSS 3.85

Certificates added:

- Autoridad de Certificacion Firmaprofesional CIF A62634068
- Certainly Root E1
- Certainly Root R1
- D-TRUST BR Root CA 1 2020
- D-TRUST EV Root CA 1 2020
- DigiCert TLS ECC P384 Root G5
- DigiCert TLS RSA4096 Root G5
- E-Tugra Global Root CA ECC v3
- E-Tugra Global Root CA RSA v3
- HiPKI Root CA - G1
- ISRG Root X2
- Security Communication ECC RootCA1
- Security Communication RootCA3
- Telia Root CA v2
- vTrus ECC Root CA
- vTrus Root CA

Certificates removed:

- Cybertrust Global Root
- DST Root CA X3
- GlobalSign Root CA - R2
- Hellenic Academic and Research Institutions RootCA 2011

#### Time zone update to 2022f

Time zone data has been updated to 2022f. This includes changes to Daylight
Savings Time (DST) for Fiji and Mexico. For more information, see
<https://mm.icann.org/pipermail/tz-announce/2022-October/000075.html>.

### Commits

- \[[`436a596e99`](https://github.com/nodejs/node/commit/436a596e99)] - **crypto**: update root certificates (Luigi Pinca) [#45490](https://github.com/nodejs/node/pull/45490)
- \[[`4b422d34af`](https://github.com/nodejs/node/commit/4b422d34af)] - **deps**: V8: cherry-pick d2db7fa7f786 (Richard Lau) [#45785](https://github.com/nodejs/node/pull/45785)
- \[[`625f4bf3a9`](https://github.com/nodejs/node/commit/625f4bf3a9)] - **deps**: update corepack to 0.15.1 (Node.js GitHub Bot) [#45331](https://github.com/nodejs/node/pull/45331)
- \[[`48a9810de8`](https://github.com/nodejs/node/commit/48a9810de8)] - **deps**: update corepack to 0.15.0 (Node.js GitHub Bot) [#45235](https://github.com/nodejs/node/pull/45235)
- \[[`9f4e64b603`](https://github.com/nodejs/node/commit/9f4e64b603)] - **deps**: update timezone to 2022f (Richard Lau) [#45521](https://github.com/nodejs/node/pull/45521)
- \[[`f297b6bd21`](https://github.com/nodejs/node/commit/f297b6bd21)] - **deps**: update archs files for OpenSSL-1.1.1s (RafaelGSS) [#45272](https://github.com/nodejs/node/pull/45272)
- \[[`11629fef15`](https://github.com/nodejs/node/commit/11629fef15)] - **deps**: upgrade openssl sources to 1.1.1s (RafaelGSS) [#45272](https://github.com/nodejs/node/pull/45272)
- \[[`c3a90c4b44`](https://github.com/nodejs/node/commit/c3a90c4b44)] - **http2**: fix memory leak when nghttp2 hd threshold is reached (rogertyang) [#41502](https://github.com/nodejs/node/pull/41502)
- \[[`785dc3efee`](https://github.com/nodejs/node/commit/785dc3efee)] - **module**: cjs-module-lexer WebAssembly fallback (Guy Bedford) [#43612](https://github.com/nodejs/node/pull/43612)
- \[[`2dbeb889f6`](https://github.com/nodejs/node/commit/2dbeb889f6)] - **node-api**: handle no support for external buffers (Michael Dawson) [#45181](https://github.com/nodejs/node/pull/45181)
- \[[`5b2ea124f3`](https://github.com/nodejs/node/commit/5b2ea124f3)] - **test**: add test to validate changelogs for releases (Richard Lau) [#45325](https://github.com/nodejs/node/pull/45325)
- \[[`f13f889956`](https://github.com/nodejs/node/commit/f13f889956)] - **test**: add a test to ensure the correctness of timezone upgrades (Darshan Sen) [#45299](https://github.com/nodejs/node/pull/45299)
- \[[`5608e6fa72`](https://github.com/nodejs/node/commit/5608e6fa72)] - **tools**: update certdata.txt (Luigi Pinca) [#45490](https://github.com/nodejs/node/pull/45490)
- \[[`d6f1d7107b`](https://github.com/nodejs/node/commit/d6f1d7107b)] - **tools**: have test-asan use ubuntu-20.04 (Filip Skokan) [#45581](https://github.com/nodejs/node/pull/45581)
- \[[`370a00f737`](https://github.com/nodejs/node/commit/370a00f737)] - **tools**: make license-builder.sh comply with shellcheck 0.8.0 (Rich Trott) [#41258](https://github.com/nodejs/node/pull/41258)

Windows 32-bit Installer: https://nodejs.org/dist/v14.21.2/node-v14.21.2-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v14.21.2/node-v14.21.2-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v14.21.2/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v14.21.2/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v14.21.2/node-v14.21.2.pkg \
macOS Intel 64-bit Binary: https://nodejs.org/dist/v14.21.2/node-v14.21.2-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v14.21.2/node-v14.21.2-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v14.21.2/node-v14.21.2-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v14.21.2/node-v14.21.2-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v14.21.2/node-v14.21.2-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v14.21.2/node-v14.21.2-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v14.21.2/node-v14.21.2-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v14.21.2/node-v14.21.2.tar.gz \
Other release files: https://nodejs.org/dist/v14.21.2/ \
Documentation: https://nodejs.org/docs/v14.21.2/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

bbcd546fd41dadb4d0278c7b522282c97c406388628245dcca1a667bb69ca05d  node-v14.21.2-aix-ppc64.tar.gz
4a3e7b12cc24b97331c8424ac9b508c61cc97ed327dafda5f3d785e8230cd7e0  node-v14.21.2-darwin-x64.tar.gz
f32dc21f9bde31563d9c8fd28e07a7bc1189d1a094cc842125ae3067128f9492  node-v14.21.2-darwin-x64.tar.xz
144e88d651cbecc2a87db2b20756d800f6b3f49e99cd48c5008a432ef7217ce5  node-v14.21.2-headers.tar.gz
c841694a7593b18705e2232b71e2e9d3a66b523346cbafc39aa715f77d00f5d2  node-v14.21.2-headers.tar.xz
bb7ac11ee8ff3a06773028d53443769c4b0c0f0e85ece68921645882b181cf80  node-v14.21.2-linux-arm64.tar.gz
38c541682791b39b5a4b8b215c81c6cb2c3f745fcfc7e545bd006df04abc9854  node-v14.21.2-linux-arm64.tar.xz
02d41e1fafcd2fa090a9f4e7184a9c85b011612478ad2ee35f6d0219da28b1f6  node-v14.21.2-linux-armv7l.tar.gz
b6c2f2b19a9e0ab45ed9e9defaeb3558cf1c27739c5a6cb4e4340fd4c120e9c4  node-v14.21.2-linux-armv7l.tar.xz
dc322aafa2525f114b2fa2a3144c805c8b80341fe64e8d66b5b11c8c9e9fad45  node-v14.21.2-linux-ppc64le.tar.gz
c88cd00247779b199494f95b471f37811e3bdfa32923c6716c0d98ade97fb17e  node-v14.21.2-linux-ppc64le.tar.xz
3cfb3c3793b8fa3928bf42693939ce531a0a77184f43a2611d981be7f246943c  node-v14.21.2-linux-s390x.tar.gz
2717ee94d51a0f95204064b7419a8ee993d1e1102e35aa4f238ce185038c4f21  node-v14.21.2-linux-s390x.tar.xz
951d64432d1c7e026a585d1c6ec8822a268faa3c9b71e1b8d36dc812c51b661e  node-v14.21.2-linux-x64.tar.gz
c73b52b6f2ae5a07e8c8eb626915557065b7f02b7e7c2faff293a71101461f86  node-v14.21.2-linux-x64.tar.xz
33b367f26d9059c8eea1fcd1c038b8999ccafabc3887682090d532843141156d  node-v14.21.2.pkg
aad2b8ac4808a069648caf0dcc938a7a01265c1efcdeb7329a7cc9474f2b87eb  node-v14.21.2.tar.gz
d8f09a0f16773a77613c3817606f6d455624992d9c43443aca15e91807a1ff03  node-v14.21.2.tar.xz
821ae7c25b7caf7fcaefeab15ff1b9943e1e3856fca0dec15cd465c1f1ad6c1b  node-v14.21.2-win-x64.7z
3a53cd029a050b979fd1a112e2ada1a5ba56cfa90ae5e135752bf29d44f1a8eb  node-v14.21.2-win-x64.zip
1d467fcd51fd13df0d118013bb02f131c4f6469f2bf6617cb303286ecef8ab87  node-v14.21.2-win-x86.7z
e322916b6cf0b4d173669fde8a3860658f641d6fe15ea3d303f5772590a8b034  node-v14.21.2-win-x86.zip
8d5c718923aef83e27bc09ed29f923638028df2fa652cde13faabbfb215260c3  node-v14.21.2-x64.msi
77db9927220a675f8d8e15e7a58348f4731dadff4e5c4490b62747a6212e4370  node-v14.21.2-x86.msi
af2ad71c29231f3532995fd97834288d256f39708af2b2884c130ead787890d0  win-x64/node.exe
5226634dfd3f9a99dd40ca374fe654b6ed4fdb1890c629a3426e34e212fb011c  win-x64/node.lib
f25ce6b1c59f22eb20c3ccfe970e05a71ed2ed11bf19292fd90b0f4a7c996e5e  win-x64/node_pdb.7z
f00d14f24df5add00dfde7a86dedd7fceebf4bf86de8002a1d87b1d97eb3980e  win-x64/node_pdb.zip
1a9c1229e8a1e91a0a1566ee1cf64028f086623ca2b0036c0eab74d7ff808191  win-x86/node.exe
e1e44fce63711f68656b246488b63e2ebbfd3e3ea8253d9a8fa264d526c80508  win-x86/node.lib
57bf9b5d3d383ec88685498bf343b5d3a42d7c8d9a26c5409c49a4b2f929fc42  win-x86/node_pdb.7z
5b642441ed2d508733e75e35e910b5c0f17edc2909874dd4bf02f75afa657dde  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEyC+jrhy+3Gvka5NgxDzsRcF6uTwFAmOYc/cACgkQxDzsRcF6
uTxeIw/+NNfoCO5Pq/byMPKMVGujp8zIHgSlSvYMvTFwv09sU84gdFG0phxcZ/lF
QK5oxqMuQzpPa4PzWuQFhdJ7D7rQJAo5KR/1Gd6dK74z22hIf8DrAlQd1b0gr9Ch
mfB83tJ0ujQwUdJm1VmyXx1MKS9e8VS+bFC0XSLQtitrUAQKUR0kQSXmWIrg19vk
2cNEkkImvy4CoNrxahZg/V++JRhVtAoxSug88mE/HLNrtSOkkJv0u+GVAEK5mk+k
5k/AeYDWWR65VfQqojkTzFet8MyP+soCObHiu3kI2uBb7jZCnrOKk5wYUssDVMUG
db7UucQC05xrmGMvUkKu4EmFlSMnzsFPRML11Gl+LSOeVydYCd7F1XtCIaLPH1/V
wzs7Vvr0XYu43Qykc1KnFGMWKW9EvPidM8Cz4dpjasigP6wiSDiGfq6pQCcoGxe4
q6zRm5g1CJ+VIO4x0KraAlR/BqGLqoKMeOkdWC3gXY7lJVTJmfnAeBwvkZBD++RC
MR9T+DXAsgKXr/6EC1Kwvx/qzlhixQdByLMI6JwJ0DBhr9ZDCaAF0S/dIxWkcEZ7
mGpo4FRzm+RmZ7t7VLAOJMLWlvhy32OCvSmSD3u4UlOm1uvDwx6Ztw349VvKVo+5
+PgeKQKgI+2WbY5EepEixP9L5jdrvAW7EtZUnMEKCWGOVxEfBOU=
=wiOg
-----END PGP SIGNATURE-----

```
