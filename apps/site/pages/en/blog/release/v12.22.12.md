---
date: '2022-04-05T12:19:10.471Z'
category: release
title: Node v12.22.12 (LTS)
layout: blog-post
author: <PERSON>
---

### Notable Changes

This is planned to be the final Node.js 12 release. Node.js 12 will
reach End-of-Life status on 30 April 2022, after which it will no
receive updates. You are strongly advised to migrate your applications
to Node.js 16 or 14 (both of which are Long Term Support (LTS) releases)
to continue to receive future security updates beyond 30 April 2022.

This release fixes a shutdown crash in Node-API (formerly N-API) and a
potential stack overflow when using `vm.runInNewContext()`.

The list of GPG keys used to sign releases and instructions on how to
fetch the keys for verifying binaries has been synchronized with the
main branch.

### Commits

- \[[`1193290f3f`](https://github.com/nodejs/node/commit/1193290f3f)] - **deps**: V8: cherry-pick cc9a8a37445e (devsnek) [#42065](https://github.com/nodejs/node/pull/42065)
- \[[`333eda8d03`](https://github.com/nodejs/node/commit/333eda8d03)] - **doc**: add a note about possible missing lines to readline.asyncIterator (Igor Mikhalev) [#34675](https://github.com/nodejs/node/pull/34675)
- \[[`518a49c0c6`](https://github.com/nodejs/node/commit/518a49c0c6)] - **doc**: use openpgp.org for keyserver examples (Nick Schonning) [#39227](https://github.com/nodejs/node/pull/39227)
- \[[`11aef2ad03`](https://github.com/nodejs/node/commit/11aef2ad03)] - **doc**: update release key for Danielle Adams (Danielle Adams) [#36793](https://github.com/nodejs/node/pull/36793)
- \[[`a9c38f1003`](https://github.com/nodejs/node/commit/a9c38f1003)] - **doc**: add release key for Danielle Adams (Danielle Adams) [#35545](https://github.com/nodejs/node/pull/35545)
- \[[`a35f553889`](https://github.com/nodejs/node/commit/a35f553889)] - **doc**: add release key for Bryan English (Bryan English) [#42102](https://github.com/nodejs/node/pull/42102)
- \[[`5f104e3218`](https://github.com/nodejs/node/commit/5f104e3218)] - **node-api**: cctest on v8impl::Reference (legendecas) [#38970](https://github.com/nodejs/node/pull/38970)
- \[[`e23c04f0dc`](https://github.com/nodejs/node/commit/e23c04f0dc)] - **node-api**: avoid SecondPassCallback crash (Michael Dawson) [#38899](https://github.com/nodejs/node/pull/38899)
- \[[`a7224c9559`](https://github.com/nodejs/node/commit/a7224c9559)] - **node-api**: fix shutdown crashes (Michael Dawson) [#38492](https://github.com/nodejs/node/pull/38492)
- \[[`81b4dc88f1`](https://github.com/nodejs/node/commit/81b4dc88f1)] - **node-api**: make reference weak parameter an indirect link to references (Chengzhong Wu) [#38000](https://github.com/nodejs/node/pull/38000)
- \[[`2aa9ca1ea9`](https://github.com/nodejs/node/commit/2aa9ca1ea9)] - **node-api**: fix crash in finalization (Michael Dawson) [#37876](https://github.com/nodejs/node/pull/37876)
- \[[`a2f4206415`](https://github.com/nodejs/node/commit/a2f4206415)] - **node-api**: stop ref gc during environment teardown (Gabriel Schulhof) [#37616](https://github.com/nodejs/node/pull/37616)
- \[[`171bb66ccc`](https://github.com/nodejs/node/commit/171bb66ccc)] - **node-api**: force env shutdown deferring behavior (Gabriel Schulhof) [#37303](https://github.com/nodejs/node/pull/37303)
- \[[`e707514c80`](https://github.com/nodejs/node/commit/e707514c80)] - **src**: fix finalization crash (James M Snell) [#38250](https://github.com/nodejs/node/pull/38250)

Windows 32-bit Installer: https://nodejs.org/dist/v12.22.12/node-v12.22.12-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v12.22.12/node-v12.22.12-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v12.22.12/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v12.22.12/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v12.22.12/node-v12.22.12.pkg \
macOS Intel 64-bit Binary: https://nodejs.org/dist/v12.22.12/node-v12.22.12-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v12.22.12/node-v12.22.12-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v12.22.12/node-v12.22.12-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v12.22.12/node-v12.22.12-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v12.22.12/node-v12.22.12-aix-ppc64.tar.gz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v12.22.12/node-v12.22.12-sunos-x64.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v12.22.12/node-v12.22.12-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v12.22.12/node-v12.22.12-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v12.22.12/node-v12.22.12.tar.gz \
Other release files: https://nodejs.org/dist/v12.22.12/ \
Documentation: https://nodejs.org/docs/v12.22.12/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

c360131f03422105d59c84d164e3cbb8fa23d6c1a7d7b80fc594ec331e13a466  node-v12.22.12-aix-ppc64.tar.gz
32927913ed549ce01685a6f9f4697567a64592c7fd1e9a845ac8a10efa1475e6  node-v12.22.12-darwin-x64.tar.gz
95799e1276d65b599635f839248d3b3f7d3986873da8f01902a541b9588a6c71  node-v12.22.12-darwin-x64.tar.xz
495643d22049308bc3b3ab9fbe5c518df2abda51fe1013c509a80d0aebe1f425  node-v12.22.12-headers.tar.gz
7a9bb004d3f5061d2b01a66371aaf29041a1b246707dd04416d9d0326a2bd579  node-v12.22.12-headers.tar.xz
91aefa690914b7f24250f3c0b560b42c6d306315d40009c96b5a6940115895fe  node-v12.22.12-linux-arm64.tar.gz
eebdeb528df727271635196188ed7d04a5dba2c61ccaded22f693ae501cf17f5  node-v12.22.12-linux-arm64.tar.xz
bb68e804ecba00b30a135e505b51509e8ffb666b3d4872dd53037ad11699174c  node-v12.22.12-linux-armv7l.tar.gz
31d071da9b365d4acd4c91eaa36bef386834f67e8e0beb1348b6decd8c497df3  node-v12.22.12-linux-armv7l.tar.xz
a32f0e934f99b68a30738354880fd61bcf651f8d9012e1f0d439205bc918a83b  node-v12.22.12-linux-ppc64le.tar.gz
37df710a3577655b410b1300491c5d1e49662ab14b88d29a3be74fc063964203  node-v12.22.12-linux-ppc64le.tar.xz
d73166dc687ddaab926e4aa2df6b26ca7c050fd326aa0104a833d0176978bc5b  node-v12.22.12-linux-s390x.tar.gz
55f9bb6ed3a590b3cffcaabbde2c24a46aafbc9687105cfac514271b1cda5dc2  node-v12.22.12-linux-s390x.tar.xz
ff92a45c4d03e8e270bec1ab337b8fff6e9de293dabfe7e8936a41f2fb0b202e  node-v12.22.12-linux-x64.tar.gz
e6d052364bfa2c17da92cf31794100cfd709ba147415ddaeed2222eec9ca1469  node-v12.22.12-linux-x64.tar.xz
a7c5d6b56a1bb468d04c1156fe1696234c55cb9a71c0d9c0f6e8346baf86a53c  node-v12.22.12.pkg
feef54c248cdac9ba983face48dcce1a5cfd6355b1746861dfeaed76e472992b  node-v12.22.12-sunos-x64.tar.gz
4dfaf2f0ff9a645f2169e722720ffbf5fed661275c55ea2e4e2966e358cd8af0  node-v12.22.12-sunos-x64.tar.xz
1a5c52c50185f7c23318e7e8001cc58054736acb98cb8c523d33b136da9e54be  node-v12.22.12.tar.gz
bc42b7f8495b9bfc7f7850dd180bb02a5bdf139cc232b8c6f02a6967e20714f2  node-v12.22.12.tar.xz
95f969cafbe02eb91e9d375899518b8e517f9f16300d040ac89fdaf4b881ba8d  node-v12.22.12-win-x64.7z
09639bac66d4dc4dd52179968209413ad4b7360e917dcbe8834052a4b936a087  node-v12.22.12-win-x64.zip
172df18129f96dd44636fc750f538e530f2a2db765a4bea136ee189566a13c33  node-v12.22.12-win-x86.7z
2f7fa563c9477d5e9fddc5c22451b21b8a963c9b5004c80dd0140c3a3675a4e8  node-v12.22.12-win-x86.zip
76102997f9084e1faa544693ad1daeef68394d46ae7e363ad8df1fa86896133f  node-v12.22.12-x64.msi
68a5f1198cf76b85ca9eb8819c1e3fa670af6440534f59d920d267ea3cc0f402  node-v12.22.12-x86.msi
b014e4ec5ca810b2fb54cdbf6ab8d6acc488285c98469606efb8b412472bec2a  win-x64/node.exe
28e5c24831deedbf4fb8a9560f2c4f95205479c589f54a9a53ec346f6a5cf8bf  win-x64/node.lib
f53de068ed884b0bf767d3e98aca14957d48dc3ef4067ba400378c6a60ff207c  win-x64/node_pdb.7z
eb2ce31b514ee6b130c22248464b9e456aa17f930976a4aaf61938d7a063e9a7  win-x64/node_pdb.zip
d8e21b6590e2542949e8abac1903665871ca3bc426da59c3d3fa37476e18439f  win-x86/node.exe
dad0e6bef1c45f4f43fbf84c33df6b910ace8122eff3f8d39d5ebecd25320ba4  win-x86/node.lib
b5ab85386155182f0b570f1a0fd30092a5e30ef62e2ed2f5376162efea92e2e9  win-x86/node_pdb.7z
4ab50237a9b8334b51105c1405148f16d8ace10a0bcede53b6e7bdda27bb18f3  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEyC+jrhy+****************************************
uTy3uxAAr0IBR5P6cggphIRfYRFCfkUfZUYcnyaXb3zIW+sIF83T5K4u2wHtSGRD
gAbesC0d+PCm5jslXZIHgfOfMmq1LwYOg1Pz91Ls2jK84jANj2zZ3AmfzzatN1Fs
qzGzb+8hQASqO7XD1fdmlAD4TesXQoH7ab69NoBRiP5MPWLBnS8nVWFPfJUkCxO+
zJ/aGnJmm4tuAX78QPOY8Du0Jl5YethDcA5dnR9ECmIZfcBQ4w5Yf6gEe+Jgdzna
MAG+d/Z77fA3cM2jjZSCihA8iSUiIQkRLu+K4A5WGvbcSa9/s2ICkvalmTjf05g8
WOqHWCwX0MlrVi8yyKfF4LG2yv4QvTbYje8Jtj32d6O431Q5DjN6xPXj5KYnSvv0
qMqMvs3T2Aihn9sqINyaNrp7yEK43+Wiez569sBiixtu5/b3YPDKmYjAmVMpkyp5
zoeVO8aqP5YMjqfGYGwqGDBsw0pr6O4b1qqEwq7V+O5+OxBrHEauVTeaDl5zeFh0
RrlgvaUrVb6EWa8tPsJjVyAFA6zXnXEw6nc2GyPdYLmzOYZVT3ZXqB13YSIMUQ7F
0R+zTJPHHKbNq7HgFxfvvZdtegg8NNnVxtS3Z9O0RdbbCJea7lC372C/Re7qWVEJ
50NSkwc1rlHSt9LTlgQ9CLlAgTLrBhgo9TXGwewAB1VvRQ0mdg4=
=v5oK
-----END PGP SIGNATURE-----

```
