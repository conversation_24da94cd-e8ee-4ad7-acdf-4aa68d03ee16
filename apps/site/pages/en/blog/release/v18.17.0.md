---
date: '2023-07-18T19:53:21.503Z'
category: release
title: Node v18.17.0 (LTS)
layout: blog-post
author: <PERSON>
---

### Notable Changes

#### Ada 2.0

Node.js v18.17.0 comes with the latest version of the URL parser, Ada. This update brings significant performance improvements
to URL parsing, including enhancements to the url.domainToASCII and url.domainToUnicode functions in node:url.

Ada 2.0 has been integrated into the Node.js codebase, ensuring that all parts of the application can benefit from the
improved performance. Additionally, Ada 2.0 features a significant performance boost over its predecessor, Ada 1.0.4,
while also eliminating the need for the ICU requirement for URL hostname parsing.

Contributed by <PERSON><PERSON><PERSON> and <PERSON> in [#47339](https://github.com/nodejs/node/pull/47339)

#### Web Crypto API

Web Crypto API functions' arguments are now coerced and validated as per their WebIDL definitions like in other Web Crypto API implementations.
This further improves interoperability with other implementations of Web Crypto API.

Contributed by <PERSON><PERSON> in [#46067](https://github.com/nodejs/node/pull/46067)

- **crypto**:
  - update root certificates to NSS 3.89 (Node.js GitHub Bot) [#47659](https://github.com/nodejs/node/pull/47659)
- **dns**:
  - **(SEMVER-MINOR)** expose getDefaultResultOrder (btea) [#46973](https://github.com/nodejs/node/pull/46973)
- **doc**:
  - add ovflowd to collaborators (Claudio Wunder) [#47844](https://github.com/nodejs/node/pull/47844)
  - add KhafraDev to collaborators (Matthew Aitken) [#47510](https://github.com/nodejs/node/pull/47510)
- **events**:
  - **(SEMVER-MINOR)** add getMaxListeners method (Matthew Aitken) [#47039](https://github.com/nodejs/node/pull/47039)
- **fs**:
  - **(SEMVER-MINOR)** add support for mode flag to specify the copy behavior (Tetsuharu Ohzeki) [#47084](https://github.com/nodejs/node/pull/47084)
  - **(SEMVER-MINOR)** add recursive option to readdir and opendir (Ethan Arrowood) [#41439](https://github.com/nodejs/node/pull/41439)
  - **(SEMVER-MINOR)** add support for mode flag to specify the copy behavior (Tetsuharu Ohzeki) [#47084](https://github.com/nodejs/node/pull/47084)
  - **(SEMVER-MINOR)** implement byob mode for readableWebStream() (Debadree Chatterjee) [#46933](https://github.com/nodejs/node/pull/46933)
- **http**:
  - **(SEMVER-MINOR)** prevent writing to the body when not allowed by HTTP spec (Gerrard Lindsay) [#47732](https://github.com/nodejs/node/pull/47732)
  - **(SEMVER-MINOR)** remove internal error in assignSocket (Matteo Collina) [#47723](https://github.com/nodejs/node/pull/47723)
  - **(SEMVER-MINOR)** add highWaterMark opt in http.createServer (HinataKah0) [#47405](https://github.com/nodejs/node/pull/47405)
- **lib**:
  - **(SEMVER-MINOR)** add webstreams to Duplex.from() (Debadree Chatterjee) [#46190](https://github.com/nodejs/node/pull/46190)
  - **(SEMVER-MINOR)** implement AbortSignal.any() (Chemi Atlow) [#47821](https://github.com/nodejs/node/pull/47821)
- **module**:
  - change default resolver to not throw on unknown scheme (Gil Tayar) [#47824](https://github.com/nodejs/node/pull/47824)
- **node-api**:
  - **(SEMVER-MINOR)** define version 9 (Chengzhong Wu) [#48151](https://github.com/nodejs/node/pull/48151)
  - **(SEMVER-MINOR)** deprecate napi_module_register (Vladimir Morozov) [#46319](https://github.com/nodejs/node/pull/46319)
- **stream**:
  - **(SEMVER-MINOR)** preserve object mode in compose (Raz Luvaton) [#47413](https://github.com/nodejs/node/pull/47413)
  - **(SEMVER-MINOR)** add setter & getter for default highWaterMark (#46929) (Robert Nagy) [#46929](https://github.com/nodejs/node/pull/46929)
- **test**:
  - unflake test-vm-timeout-escape-nexttick (Santiago Gimeno) [#48078](https://github.com/nodejs/node/pull/48078)
- **test_runner**:
  - **(SEMVER-MINOR)** add shorthands to `test` (Chemi Atlow) [#47909](https://github.com/nodejs/node/pull/47909)
  - **(SEMVER-MINOR)** support combining coverage reports (Colin Ihrig) [#47686](https://github.com/nodejs/node/pull/47686)
  - **(SEMVER-MINOR)** execute before hook on test (Chemi Atlow) [#47586](https://github.com/nodejs/node/pull/47586)
  - **(SEMVER-MINOR)** expose reporter for use in run api (Chemi Atlow) [#47238](https://github.com/nodejs/node/pull/47238)
- **tools**:
  - update LICENSE and license-builder.sh (Santiago Gimeno) [#48078](https://github.com/nodejs/node/pull/48078)
- **url**:
  - **(SEMVER-MINOR)** implement URL.canParse (Matthew Aitken) [#47179](https://github.com/nodejs/node/pull/47179)
- **wasi**:
  - **(SEMVER-MINOR)** no longer require flag to enable wasi (Michael Dawson) [#47286](https://github.com/nodejs/node/pull/47286)

### Commits

- \[[`2ba08ac002`](https://github.com/nodejs/node/commit/2ba08ac002)] - **benchmark**: use `cluster.isPrimary` instead of `cluster.isMaster` (Deokjin Kim) [#48002](https://github.com/nodejs/node/pull/48002)
- \[[`60ca69d96c`](https://github.com/nodejs/node/commit/60ca69d96c)] - **benchmark**: add eventtarget creation bench (Rafael Gonzaga) [#47774](https://github.com/nodejs/node/pull/47774)
- \[[`d8233d96bb`](https://github.com/nodejs/node/commit/d8233d96bb)] - **benchmark**: add a benchmark for `defaultResolve` (Antoine du Hamel) [#47543](https://github.com/nodejs/node/pull/47543)
- \[[`a1aabb6912`](https://github.com/nodejs/node/commit/a1aabb6912)] - **benchmark**: fix invalid requirementsURL (Deokjin Kim) [#47378](https://github.com/nodejs/node/pull/47378)
- \[[`394c61caf9`](https://github.com/nodejs/node/commit/394c61caf9)] - **bootstrap**: support namespaced builtins in snapshot scripts (Joyee Cheung) [#47467](https://github.com/nodejs/node/pull/47467)
- \[[`0165a765a0`](https://github.com/nodejs/node/commit/0165a765a0)] - **bootstrap**: do not expand process.argv\[1] for snapshot entry points (Joyee Cheung) [#47466](https://github.com/nodejs/node/pull/47466)
- \[[`cca557cdd9`](https://github.com/nodejs/node/commit/cca557cdd9)] - **buffer**: combine checking range of sourceStart in `buf.copy` (Deokjin Kim) [#47758](https://github.com/nodejs/node/pull/47758)
- \[[`4c69be467c`](https://github.com/nodejs/node/commit/4c69be467c)] - **buffer**: use private properties for brand checks in File (Matthew Aitken) [#47154](https://github.com/nodejs/node/pull/47154)
- \[[`d002f9b6e2`](https://github.com/nodejs/node/commit/d002f9b6e2)] - **build**: revert unkonwn ruff selector (Moshe Atlow) [#48753](https://github.com/nodejs/node/pull/48753)
- \[[`93f77cb762`](https://github.com/nodejs/node/commit/93f77cb762)] - **build**: set v8_enable_webassembly=false when lite mode is enabled (Cheng Shao) [#48248](https://github.com/nodejs/node/pull/48248)
- \[[`1662e894f3`](https://github.com/nodejs/node/commit/1662e894f3)] - **build**: add action to close stale PRs (Michael Dawson) [#48051](https://github.com/nodejs/node/pull/48051)
- \[[`5ca437b288`](https://github.com/nodejs/node/commit/5ca437b288)] - **build**: use pathlib for paths (Mohammed Keyvanzadeh) [#47581](https://github.com/nodejs/node/pull/47581)
- \[[`72443bc54b`](https://github.com/nodejs/node/commit/72443bc54b)] - **build**: refactor configure.py (Mohammed Keyvanzadeh) [#47667](https://github.com/nodejs/node/pull/47667)
- \[[`d4eecb5be9`](https://github.com/nodejs/node/commit/d4eecb5be9)] - **build**: add devcontainer configuration (Tierney Cyren) [#40825](https://github.com/nodejs/node/pull/40825)
- \[[`803ed41144`](https://github.com/nodejs/node/commit/803ed41144)] - **build**: bump ossf/scorecard-action from 2.1.2 to 2.1.3 (dependabot\[bot]) [#47367](https://github.com/nodejs/node/pull/47367)
- \[[`48468c4413`](https://github.com/nodejs/node/commit/48468c4413)] - **build**: replace Python linter flake8 with ruff (Christian Clauss) [#47519](https://github.com/nodejs/node/pull/47519)
- \[[`3ceb2c4387`](https://github.com/nodejs/node/commit/3ceb2c4387)] - **build**: add node-core-utils to setup (Jiawen Geng) [#47442](https://github.com/nodejs/node/pull/47442)
- \[[`fdc59b8e14`](https://github.com/nodejs/node/commit/fdc59b8e14)] - **build**: bump github/codeql-action from 2.2.6 to 2.2.9 (dependabot\[bot]) [#47366](https://github.com/nodejs/node/pull/47366)
- \[[`3924893023`](https://github.com/nodejs/node/commit/3924893023)] - **build**: update stale action from v7 to v8 (Rich Trott) [#47357](https://github.com/nodejs/node/pull/47357)
- \[[`753185c5b0`](https://github.com/nodejs/node/commit/753185c5b0)] - **build**: remove Python pip `--no-user` option (Christian Clauss) [#47372](https://github.com/nodejs/node/pull/47372)
- \[[`67af0a6a2b`](https://github.com/nodejs/node/commit/67af0a6a2b)] - **build**: avoid usage of pipes library (Mohammed Keyvanzadeh) [#47271](https://github.com/nodejs/node/pull/47271)
- \[[`db910dd6b2`](https://github.com/nodejs/node/commit/db910dd6b2)] - **build, deps, tools**: avoid excessive LTO (Konstantin Demin) [#47313](https://github.com/nodejs/node/pull/47313)
- \[[`35d1def891`](https://github.com/nodejs/node/commit/35d1def891)] - **child_process**: use signal.reason in child process abort (Debadree Chatterjee) [#47817](https://github.com/nodejs/node/pull/47817)
- \[[`7692d2e7b9`](https://github.com/nodejs/node/commit/7692d2e7b9)] - **cluster**: use ObjectPrototypeHasOwnProperty (Daeyeon Jeong) [#48141](https://github.com/nodejs/node/pull/48141)
- \[[`7617772762`](https://github.com/nodejs/node/commit/7617772762)] - **crypto**: use openssl's own memory BIOs in crypto_context.cc (GauriSpears) [#47160](https://github.com/nodejs/node/pull/47160)
- \[[`8cabfe7c6e`](https://github.com/nodejs/node/commit/8cabfe7c6e)] - **crypto**: fix setEngine() when OPENSSL_NO_ENGINE set (Tobias Nießen) [#47977](https://github.com/nodejs/node/pull/47977)
- \[[`de1338da05`](https://github.com/nodejs/node/commit/de1338da05)] - **crypto**: fix webcrypto private/secret import with empty usages (Filip Skokan) [#47877](https://github.com/nodejs/node/pull/47877)
- \[[`27a696fda9`](https://github.com/nodejs/node/commit/27a696fda9)] - **crypto**: update root certificates to NSS 3.89 (Node.js GitHub Bot) [#47659](https://github.com/nodejs/node/pull/47659)
- \[[`e2292f936e`](https://github.com/nodejs/node/commit/e2292f936e)] - **crypto**: remove INT_MAX restriction in randomBytes (Tobias Nießen) [#47559](https://github.com/nodejs/node/pull/47559)
- \[[`a5f214c00c`](https://github.com/nodejs/node/commit/a5f214c00c)] - **crypto**: replace THROW with CHECK for scrypt keylen (Tobias Nießen) [#47407](https://github.com/nodejs/node/pull/47407)
- \[[`dd42214fd4`](https://github.com/nodejs/node/commit/dd42214fd4)] - **crypto**: unify validation of checkPrime checks (Tobias Nießen) [#47165](https://github.com/nodejs/node/pull/47165)
- \[[`76e4d12fb3`](https://github.com/nodejs/node/commit/76e4d12fb3)] - **crypto**: re-add padding for AES-KW wrapped JWKs (Filip Skokan) [#46563](https://github.com/nodejs/node/pull/46563)
- \[[`9d894c17dd`](https://github.com/nodejs/node/commit/9d894c17dd)] - **crypto**: use WebIDL converters in WebCryptoAPI (Filip Skokan) [#46067](https://github.com/nodejs/node/pull/46067)
- \[[`6f3a8b45a5`](https://github.com/nodejs/node/commit/6f3a8b45a5)] - **deps**: update ada to 2.5.0 (Node.js GitHub Bot) [#48223](https://github.com/nodejs/node/pull/48223)
- \[[`075b6db919`](https://github.com/nodejs/node/commit/075b6db919)] - **deps**: update ada to 2.4.2 (Node.js GitHub Bot) [#48092](https://github.com/nodejs/node/pull/48092)
- \[[`a4ee1f652c`](https://github.com/nodejs/node/commit/a4ee1f652c)] - **deps**: update ada to 2.4.1 (Node.js GitHub Bot) [#48036](https://github.com/nodejs/node/pull/48036)
- \[[`81b514d3f0`](https://github.com/nodejs/node/commit/81b514d3f0)] - **deps**: update ada to 2.4.0 (Node.js GitHub Bot) [#47922](https://github.com/nodejs/node/pull/47922)
- \[[`575ddf694f`](https://github.com/nodejs/node/commit/575ddf694f)] - **deps**: update ada to 2.3.1 (Node.js GitHub Bot) [#47893](https://github.com/nodejs/node/pull/47893)
- \[[`2d03d5f458`](https://github.com/nodejs/node/commit/2d03d5f458)] - **deps**: update ada to 2.3.0 (Node.js GitHub Bot) [#47737](https://github.com/nodejs/node/pull/47737)
- \[[`42e690f2d5`](https://github.com/nodejs/node/commit/42e690f2d5)] - **deps**: update ada to 2.2.0 (Node.js GitHub Bot) [#47678](https://github.com/nodejs/node/pull/47678)
- \[[`08dd271521`](https://github.com/nodejs/node/commit/08dd271521)] - **deps**: update ada to 2.1.0 (Node.js GitHub Bot) [#47598](https://github.com/nodejs/node/pull/47598)
- \[[`96c50ba71f`](https://github.com/nodejs/node/commit/96c50ba71f)] - **deps**: update ada to 2.0.0 (Node.js GitHub Bot) [#47339](https://github.com/nodejs/node/pull/47339)
- \[[`4d1c38b758`](https://github.com/nodejs/node/commit/4d1c38b758)] - **deps**: update zlib to 337322d (Node.js GitHub Bot) [#48218](https://github.com/nodejs/node/pull/48218)
- \[[`74206b2549`](https://github.com/nodejs/node/commit/74206b2549)] - **deps**: update histogram 0.11.8 (Marco Ippolito) [#47742](https://github.com/nodejs/node/pull/47742)
- \[[`fbb4b3775d`](https://github.com/nodejs/node/commit/fbb4b3775d)] - **deps**: update histogram to 0.11.7 (Marco Ippolito) [#47742](https://github.com/nodejs/node/pull/47742)
- \[[`e88c079022`](https://github.com/nodejs/node/commit/e88c079022)] - **deps**: update simdutf to 3.2.12 (Node.js GitHub Bot) [#48118](https://github.com/nodejs/node/pull/48118)
- \[[`48bd1248b9`](https://github.com/nodejs/node/commit/48bd1248b9)] - **deps**: update minimatch to 9.0.1 (Node.js GitHub Bot) [#48094](https://github.com/nodejs/node/pull/48094)
- \[[`d4572d31fa`](https://github.com/nodejs/node/commit/d4572d31fa)] - **deps**: update corepack to 0.18.0 (Node.js GitHub Bot) [#48091](https://github.com/nodejs/node/pull/48091)
- \[[`8090d29dc4`](https://github.com/nodejs/node/commit/8090d29dc4)] - **deps**: update uvwasi to 0.0.18 (Node.js GitHub Bot) [#47866](https://github.com/nodejs/node/pull/47866)
- \[[`169c8eea2e`](https://github.com/nodejs/node/commit/169c8eea2e)] - **deps**: update uvwasi to 0.0.17 (Node.js GitHub Bot) [#47866](https://github.com/nodejs/node/pull/47866)
- \[[`6acbb23380`](https://github.com/nodejs/node/commit/6acbb23380)] - **deps**: upgrade npm to 9.6.7 (npm team) [#48062](https://github.com/nodejs/node/pull/48062)
- \[[`e8f2c0a58b`](https://github.com/nodejs/node/commit/e8f2c0a58b)] - **deps**: update undici to 5.22.1 (Node.js GitHub Bot) [#47994](https://github.com/nodejs/node/pull/47994)
- \[[`9309fd3120`](https://github.com/nodejs/node/commit/9309fd3120)] - **deps**: update simdutf to 3.2.9 (Node.js GitHub Bot) [#47983](https://github.com/nodejs/node/pull/47983)
- \[[`b796d3560a`](https://github.com/nodejs/node/commit/b796d3560a)] - **deps**: upgrade npm to 9.6.6 (npm team) [#47862](https://github.com/nodejs/node/pull/47862)
- \[[`cce372e14e`](https://github.com/nodejs/node/commit/cce372e14e)] - **deps**: V8: cherry-pick c5ab3e4f0c5a (Richard Lau) [#47736](https://github.com/nodejs/node/pull/47736)
- \[[`7283486adb`](https://github.com/nodejs/node/commit/7283486adb)] - **deps**: update undici to 5.22.0 (Node.js GitHub Bot) [#47679](https://github.com/nodejs/node/pull/47679)
- \[[`2ea6e03003`](https://github.com/nodejs/node/commit/2ea6e03003)] - **deps**: add minimatch as a dependency (Moshe Atlow) [#47499](https://github.com/nodejs/node/pull/47499)
- \[[`261e1d23d1`](https://github.com/nodejs/node/commit/261e1d23d1)] - **deps**: update ICU to 73.1 release (Steven R. Loomis) [#47456](https://github.com/nodejs/node/pull/47456)
- \[[`f532f9df5f`](https://github.com/nodejs/node/commit/f532f9df5f)] - **deps**: update undici to 5.21.2 (Node.js GitHub Bot) [#47508](https://github.com/nodejs/node/pull/47508)
- \[[`dcb8c038b9`](https://github.com/nodejs/node/commit/dcb8c038b9)] - **deps**: update simdutf to 3.2.8 (Node.js GitHub Bot) [#47507](https://github.com/nodejs/node/pull/47507)
- \[[`6c8456d61f`](https://github.com/nodejs/node/commit/6c8456d61f)] - **deps**: update undici to 5.21.1 (Node.js GitHub Bot) [#47488](https://github.com/nodejs/node/pull/47488)
- \[[`d3b2e8a438`](https://github.com/nodejs/node/commit/d3b2e8a438)] - **deps**: update simdutf to 3.2.7 (Node.js GitHub Bot) [#47473](https://github.com/nodejs/node/pull/47473)
- \[[`64a5fe0499`](https://github.com/nodejs/node/commit/64a5fe0499)] - **deps**: update corepack to 0.17.2 (Node.js GitHub Bot) [#47474](https://github.com/nodejs/node/pull/47474)
- \[[`6f0f61a7d3`](https://github.com/nodejs/node/commit/6f0f61a7d3)] - **deps**: upgrade npm to 9.6.4 (npm team) [#47432](https://github.com/nodejs/node/pull/47432)
- \[[`443a72e207`](https://github.com/nodejs/node/commit/443a72e207)] - **deps**: update zlib to upstream 5edb52d4 (Luigi Pinca) [#47151](https://github.com/nodejs/node/pull/47151)
- \[[`dc3bc46914`](https://github.com/nodejs/node/commit/dc3bc46914)] - **deps**: update simdutf to 3.2.3 (Node.js GitHub Bot) [#47331](https://github.com/nodejs/node/pull/47331)
- \[[`b2f2bebbc2`](https://github.com/nodejs/node/commit/b2f2bebbc2)] - **deps**: update timezone to 2023c (Node.js GitHub Bot) [#47302](https://github.com/nodejs/node/pull/47302)
- \[[`c10729ffa7`](https://github.com/nodejs/node/commit/c10729ffa7)] - **deps**: upgrade npm to 9.6.3 (npm team) [#47325](https://github.com/nodejs/node/pull/47325)
- \[[`420deac1de`](https://github.com/nodejs/node/commit/420deac1de)] - **deps**: update corepack to 0.17.1 (Node.js GitHub Bot) [#47156](https://github.com/nodejs/node/pull/47156)
- \[[`966ba28491`](https://github.com/nodejs/node/commit/966ba28491)] - **deps**: V8: cherry-pick 3e4952cb2a59 (Richard Lau) [#47236](https://github.com/nodejs/node/pull/47236)
- \[[`fc6ab26824`](https://github.com/nodejs/node/commit/fc6ab26824)] - **deps**: update timezone to 2023b (Node.js GitHub Bot) [#47256](https://github.com/nodejs/node/pull/47256)
- \[[`2700e70215`](https://github.com/nodejs/node/commit/2700e70215)] - **deps**: upgrade npm to 9.6.2 (npm team) [#47108](https://github.com/nodejs/node/pull/47108)
- \[[`29ba98a0a5`](https://github.com/nodejs/node/commit/29ba98a0a5)] - **deps**: V8: cherry-pick 975ff4dbfd1b (Debadree Chatterjee) [#47209](https://github.com/nodejs/node/pull/47209)
- \[[`be34777be8`](https://github.com/nodejs/node/commit/be34777be8)] - **deps**: cherry-pick win/arm64/clang fixes (Cheng Zhao) [#47011](https://github.com/nodejs/node/pull/47011)
- \[[`b52aacb614`](https://github.com/nodejs/node/commit/b52aacb614)] - **deps**: update uvwasi to v0.0.16 (Michael Dawson) [#46434](https://github.com/nodejs/node/pull/46434)
- \[[`27a76cf5e0`](https://github.com/nodejs/node/commit/27a76cf5e0)] - **deps,test**: update postject to 1.0.0-alpha.6 (Node.js GitHub Bot) [#48072](https://github.com/nodejs/node/pull/48072)
- \[[`b171c1a3a4`](https://github.com/nodejs/node/commit/b171c1a3a4)] - **dgram**: convert macro to template (Tobias Nießen) [#47891](https://github.com/nodejs/node/pull/47891)
- \[[`709bf1c758`](https://github.com/nodejs/node/commit/709bf1c758)] - **(SEMVER-MINOR)** **dns**: expose getDefaultResultOrder (btea) [#46973](https://github.com/nodejs/node/pull/46973)
- \[[`2f202c93e7`](https://github.com/nodejs/node/commit/2f202c93e7)] - **doc**: clarify array args to Buffer.from() (Bryan English) [#48274](https://github.com/nodejs/node/pull/48274)
- \[[`27f195f8d8`](https://github.com/nodejs/node/commit/27f195f8d8)] - **doc**: document watch option for node:test run() (Moshe Atlow) [#48256](https://github.com/nodejs/node/pull/48256)
- \[[`7558ef350a`](https://github.com/nodejs/node/commit/7558ef350a)] - **doc**: update documentation for FIPS support (Richard Lau) [#48194](https://github.com/nodejs/node/pull/48194)
- \[[`f2bb1919e5`](https://github.com/nodejs/node/commit/f2bb1919e5)] - **doc**: improve the documentation of the stdio option (Kumar Arnav) [#48110](https://github.com/nodejs/node/pull/48110)
- \[[`a2aa52ba92`](https://github.com/nodejs/node/commit/a2aa52ba92)] - **doc**: update Buffer.allocUnsafe description (sinkhaha) [#48183](https://github.com/nodejs/node/pull/48183)
- \[[`19ad471d52`](https://github.com/nodejs/node/commit/19ad471d52)] - **doc**: update codeowners with website team (Claudio Wunder) [#48197](https://github.com/nodejs/node/pull/48197)
- \[[`67b2c2a98f`](https://github.com/nodejs/node/commit/67b2c2a98f)] - **doc**: fix broken link to new folder doc/contributing/maintaining (Andrea Fassina) [#48205](https://github.com/nodejs/node/pull/48205)
- \[[`795ca70815`](https://github.com/nodejs/node/commit/795ca70815)] - **doc**: add atlowChemi to triagers (Chemi Atlow) [#48104](https://github.com/nodejs/node/pull/48104)
- \[[`e437a0aff1`](https://github.com/nodejs/node/commit/e437a0aff1)] - **doc**: fix typo in readline completer function section (Vadym) [#48188](https://github.com/nodejs/node/pull/48188)
- \[[`92e0ea496d`](https://github.com/nodejs/node/commit/92e0ea496d)] - **doc**: remove broken link for keygen (Rich Trott) [#48176](https://github.com/nodejs/node/pull/48176)
- \[[`fe15dae8e6`](https://github.com/nodejs/node/commit/fe15dae8e6)] - **doc**: add `auto` intrinsic height to prevent jitter/flicker (Daniel Holbert) [#48195](https://github.com/nodejs/node/pull/48195)
- \[[`230335e21f`](https://github.com/nodejs/node/commit/230335e21f)] - **doc**: add version info on the SEA docs (Antoine du Hamel) [#48173](https://github.com/nodejs/node/pull/48173)
- \[[`e6f37d1b80`](https://github.com/nodejs/node/commit/e6f37d1b80)] - **doc**: add Ruy to list of TSC members (Michael Dawson) [#48172](https://github.com/nodejs/node/pull/48172)
- \[[`69205a250c`](https://github.com/nodejs/node/commit/69205a250c)] - **doc**: update socket.remote\* properties documentation (Saba Kharanauli) [#48139](https://github.com/nodejs/node/pull/48139)
- \[[`e4a5d6298c`](https://github.com/nodejs/node/commit/e4a5d6298c)] - **doc**: update outdated section on TLSv1.3-PSK (Tobias Nießen) [#48123](https://github.com/nodejs/node/pull/48123)
- \[[`d14018ed99`](https://github.com/nodejs/node/commit/d14018ed99)] - **doc**: improve HMAC key recommendations (Tobias Nießen) [#48121](https://github.com/nodejs/node/pull/48121)
- \[[`e9d4baf770`](https://github.com/nodejs/node/commit/e9d4baf770)] - **doc**: clarify mkdir() recursive behavior (Stephen Odogwu) [#48109](https://github.com/nodejs/node/pull/48109)
- \[[`3e4a469139`](https://github.com/nodejs/node/commit/3e4a469139)] - **doc**: fix typo in crypto legacy streams API section (Tobias Nießen) [#48122](https://github.com/nodejs/node/pull/48122)
- \[[`bdf366ab88`](https://github.com/nodejs/node/commit/bdf366ab88)] - **doc**: update SEA source link (Rich Trott) [#48080](https://github.com/nodejs/node/pull/48080)
- \[[`2a4f79a75f`](https://github.com/nodejs/node/commit/2a4f79a75f)] - **doc**: clarify tty.isRaw (Roberto Vidal) [#48055](https://github.com/nodejs/node/pull/48055)
- \[[`98c6e4be03`](https://github.com/nodejs/node/commit/98c6e4be03)] - **doc**: use secure key length for HMAC generateKey (Tobias Nießen) [#48052](https://github.com/nodejs/node/pull/48052)
- \[[`8ae5c8cf9d`](https://github.com/nodejs/node/commit/8ae5c8cf9d)] - **doc**: update broken EVP_BytesToKey link (Rich Trott) [#48064](https://github.com/nodejs/node/pull/48064)
- \[[`3c713e7caa`](https://github.com/nodejs/node/commit/3c713e7caa)] - **doc**: update broken spkac link (Rich Trott) [#48063](https://github.com/nodejs/node/pull/48063)
- \[[`c22f739e94`](https://github.com/nodejs/node/commit/c22f739e94)] - **doc**: document node-api version process (Chengzhong Wu) [#47972](https://github.com/nodejs/node/pull/47972)
- \[[`ce859f9f9f`](https://github.com/nodejs/node/commit/ce859f9f9f)] - **doc**: fix typo in binding functions (Deokjin Kim) [#48003](https://github.com/nodejs/node/pull/48003)
- \[[`070c3457b7`](https://github.com/nodejs/node/commit/070c3457b7)] - **doc**: mark Node.js 14 as End-of-Life (Richard Lau) [#48023](https://github.com/nodejs/node/pull/48023)
- \[[`3611027d8e`](https://github.com/nodejs/node/commit/3611027d8e)] - **doc**: clarify CRYPTO_CUSTOM_ENGINE_NOT_SUPPORTED (Tobias Nießen) [#47976](https://github.com/nodejs/node/pull/47976)
- \[[`dbffad958c`](https://github.com/nodejs/node/commit/dbffad958c)] - **doc**: add missing deprecated blocks to cluster (Tobias Nießen) [#47981](https://github.com/nodejs/node/pull/47981)
- \[[`035356f711`](https://github.com/nodejs/node/commit/035356f711)] - **doc**: update description of global (Tobias Nießen) [#47969](https://github.com/nodejs/node/pull/47969)
- \[[`081a6ffaea`](https://github.com/nodejs/node/commit/081a6ffaea)] - **doc**: update measure memory rejection information (Yash Ladha) [#41639](https://github.com/nodejs/node/pull/41639)
- \[[`3460cf9c23`](https://github.com/nodejs/node/commit/3460cf9c23)] - **doc**: fix broken link to TC39 import attributes proposal (Rich Trott) [#47954](https://github.com/nodejs/node/pull/47954)
- \[[`3b018c8aa9`](https://github.com/nodejs/node/commit/3b018c8aa9)] - **doc**: fix broken link (Rich Trott) [#47953](https://github.com/nodejs/node/pull/47953)
- \[[`244db960a9`](https://github.com/nodejs/node/commit/244db960a9)] - **doc**: remove broken link (Rich Trott) [#47942](https://github.com/nodejs/node/pull/47942)
- \[[`2cc8715bb9`](https://github.com/nodejs/node/commit/2cc8715bb9)] - **doc**: document make lint-md-clean (Matteo Collina) [#47926](https://github.com/nodejs/node/pull/47926)
- \[[`b80e006c17`](https://github.com/nodejs/node/commit/b80e006c17)] - **doc**: mark global object as legacy (Mert Can Altın) [#47819](https://github.com/nodejs/node/pull/47819)
- \[[`bf4eb058f3`](https://github.com/nodejs/node/commit/bf4eb058f3)] - **doc**: ntfs junction points must link to directories (Ben Noordhuis) [#47907](https://github.com/nodejs/node/pull/47907)
- \[[`49875f0d69`](https://github.com/nodejs/node/commit/49875f0d69)] - **doc**: fix params names (Dmitry Semigradsky) [#47853](https://github.com/nodejs/node/pull/47853)
- \[[`94b5eaaf17`](https://github.com/nodejs/node/commit/94b5eaaf17)] - **doc**: update supported version of FreeBSD to 12.4 (Michaël Zasso) [#47838](https://github.com/nodejs/node/pull/47838)
- \[[`0114201825`](https://github.com/nodejs/node/commit/0114201825)] - **doc**: swap Matteo with Rafael in the stewards (Rafael Gonzaga) [#47841](https://github.com/nodejs/node/pull/47841)
- \[[`8bcfcc0af9`](https://github.com/nodejs/node/commit/8bcfcc0af9)] - **doc**: add valgrind suppression details (Kevin Eady) [#47760](https://github.com/nodejs/node/pull/47760)
- \[[`75d397ecab`](https://github.com/nodejs/node/commit/75d397ecab)] - **doc**: replace EOL versions in README (Tobias Nießen) [#47833](https://github.com/nodejs/node/pull/47833)
- \[[`2b0c57cb80`](https://github.com/nodejs/node/commit/2b0c57cb80)] - **doc**: add ovflowd to collaborators (Claudio Wunder) [#47844](https://github.com/nodejs/node/pull/47844)
- \[[`be4966977c`](https://github.com/nodejs/node/commit/be4966977c)] - **doc**: update BUILDING.md previous versions links (Tobias Nießen) [#47835](https://github.com/nodejs/node/pull/47835)
- \[[`a9e8a20fb8`](https://github.com/nodejs/node/commit/a9e8a20fb8)] - **doc**: create maintaining folder for deps (Marco Ippolito) [#47589](https://github.com/nodejs/node/pull/47589)
- \[[`fd0f362d7c`](https://github.com/nodejs/node/commit/fd0f362d7c)] - **doc**: remove MoLow from Triagers (Moshe Atlow) [#47792](https://github.com/nodejs/node/pull/47792)
- \[[`0927c67ab6`](https://github.com/nodejs/node/commit/0927c67ab6)] - **doc**: fix typo in webstreams.md (Christian Takle) [#47766](https://github.com/nodejs/node/pull/47766)
- \[[`994be578da`](https://github.com/nodejs/node/commit/994be578da)] - **doc**: move BethGriggs to regular member (Rich Trott) [#47776](https://github.com/nodejs/node/pull/47776)
- \[[`64d19f4678`](https://github.com/nodejs/node/commit/64d19f4678)] - **doc**: move addaleax to TSC emeriti (Anna Henningsen) [#47752](https://github.com/nodejs/node/pull/47752)
- \[[`33ec10e6b8`](https://github.com/nodejs/node/commit/33ec10e6b8)] - **doc**: add link to news for Node.js core (Michael Dawson) [#47704](https://github.com/nodejs/node/pull/47704)
- \[[`2a682b5efe`](https://github.com/nodejs/node/commit/2a682b5efe)] - **doc**: async_hooks asynchronous content example add mjs code (btea) [#47401](https://github.com/nodejs/node/pull/47401)
- \[[`4f541c3ca3`](https://github.com/nodejs/node/commit/4f541c3ca3)] - **doc**: clarify concurrency model of test runner (Tobias Nießen) [#47642](https://github.com/nodejs/node/pull/47642)
- \[[`ffcff68f0d`](https://github.com/nodejs/node/commit/ffcff68f0d)] - **doc**: fix typos (Mohammed Keyvanzadeh) [#47685](https://github.com/nodejs/node/pull/47685)
- \[[`290b2b7afc`](https://github.com/nodejs/node/commit/290b2b7afc)] - **doc**: fix capitalization of ASan (Mohammed Keyvanzadeh) [#47676](https://github.com/nodejs/node/pull/47676)
- \[[`b4ca788878`](https://github.com/nodejs/node/commit/b4ca788878)] - **doc**: fix typos in SECURITY.md (Mohammed Keyvanzadeh) [#47677](https://github.com/nodejs/node/pull/47677)
- \[[`971c545a47`](https://github.com/nodejs/node/commit/971c545a47)] - **doc**: update error code of buffer (Deokjin Kim) [#47617](https://github.com/nodejs/node/pull/47617)
- \[[`ec5c919928`](https://github.com/nodejs/node/commit/ec5c919928)] - **doc**: change offset of example in `Buffer.copyBytesFrom` (Deokjin Kim) [#47606](https://github.com/nodejs/node/pull/47606)
- \[[`980bf052c7`](https://github.com/nodejs/node/commit/980bf052c7)] - **doc**: remove markdown link from heading (Tobias Nießen) [#47585](https://github.com/nodejs/node/pull/47585)
- \[[`e96451ec5e`](https://github.com/nodejs/node/commit/e96451ec5e)] - **doc**: fix release-post script location (Rafael Gonzaga) [#47517](https://github.com/nodejs/node/pull/47517)
- \[[`61ea15339c`](https://github.com/nodejs/node/commit/61ea15339c)] - **doc**: add link for news from uvwasi team (Michael Dawson) [#47531](https://github.com/nodejs/node/pull/47531)
- \[[`d40bcdd73e`](https://github.com/nodejs/node/commit/d40bcdd73e)] - **doc**: add missing setEncoding call in ESM example (Anna Henningsen) [#47558](https://github.com/nodejs/node/pull/47558)
- \[[`924dc909b3`](https://github.com/nodejs/node/commit/924dc909b3)] - **doc**: fix typo in util.types.isNativeError() (Julian Dax) [#47532](https://github.com/nodejs/node/pull/47532)
- \[[`a24d72a6fb`](https://github.com/nodejs/node/commit/a24d72a6fb)] - **doc**: add KhafraDev to collaborators (Matthew Aitken) [#47510](https://github.com/nodejs/node/pull/47510)
- \[[`b0196378b6`](https://github.com/nodejs/node/commit/b0196378b6)] - **doc**: create maintaining-brotli.md (Marco Ippolito) [#47380](https://github.com/nodejs/node/pull/47380)
- \[[`3902be8fe8`](https://github.com/nodejs/node/commit/3902be8fe8)] - **doc**: info on handling unintended breaking changes (Michael Dawson) [#47426](https://github.com/nodejs/node/pull/47426)
- \[[`670f9a591d`](https://github.com/nodejs/node/commit/670f9a591d)] - **doc**: add performance initiative (Yagiz Nizipli) [#47424](https://github.com/nodejs/node/pull/47424)
- \[[`89a5d04a8e`](https://github.com/nodejs/node/commit/89a5d04a8e)] - **doc**: do not create a backup file (Luigi Pinca) [#47151](https://github.com/nodejs/node/pull/47151)
- \[[`7f2bccc5d6`](https://github.com/nodejs/node/commit/7f2bccc5d6)] - **doc**: add MoLow to the TSC (Colin Ihrig) [#47436](https://github.com/nodejs/node/pull/47436)
- \[[`7db2e889ac`](https://github.com/nodejs/node/commit/7db2e889ac)] - **doc**: add a note about os.cpus() returning an empty list (codedokode) [#47363](https://github.com/nodejs/node/pull/47363)
- \[[`289a8e30d6`](https://github.com/nodejs/node/commit/289a8e30d6)] - **doc**: clarify reports are only evaluated on active versions (Rafael Gonzaga) [#47341](https://github.com/nodejs/node/pull/47341)
- \[[`dc22edb4d2`](https://github.com/nodejs/node/commit/dc22edb4d2)] - **doc**: remove Vladimir de Turckheim from Security release stewards (Vladimir de Turckheim) [#47318](https://github.com/nodejs/node/pull/47318)
- \[[`3e74a74da3`](https://github.com/nodejs/node/commit/3e74a74da3)] - **doc**: add importing util to example of \`process.report.getReport' (Deokjin Kim) [#47298](https://github.com/nodejs/node/pull/47298)
- \[[`ece029f64e`](https://github.com/nodejs/node/commit/ece029f64e)] - **doc**: vm.SourceTextModule() without context option (Axel Kittenberger) [#47295](https://github.com/nodejs/node/pull/47295)
- \[[`c7227204cc`](https://github.com/nodejs/node/commit/c7227204cc)] - **doc**: document process for sharing project news (Michael Dawson) [#47189](https://github.com/nodejs/node/pull/47189)
- \[[`2865cbb4bd`](https://github.com/nodejs/node/commit/2865cbb4bd)] - **doc**: revise example of assert.CallTracker (Deokjin Kim) [#47252](https://github.com/nodejs/node/pull/47252)
- \[[`81ebaf2670`](https://github.com/nodejs/node/commit/81ebaf2670)] - **doc**: fix typo in SECURITY.md (Rich Trott) [#47282](https://github.com/nodejs/node/pull/47282)
- \[[`faabd48f11`](https://github.com/nodejs/node/commit/faabd48f11)] - **doc**: use serial comma in cli docs (Tobias Nießen) [#47262](https://github.com/nodejs/node/pull/47262)
- \[[`3a85794089`](https://github.com/nodejs/node/commit/3a85794089)] - **doc**: improve example for Error.captureStackTrace() (Julian Dax) [#46886](https://github.com/nodejs/node/pull/46886)
- \[[`2114fa472b`](https://github.com/nodejs/node/commit/2114fa472b)] - **doc**: clarify http error events after calling destroy() (Zach Bjornson) [#46903](https://github.com/nodejs/node/pull/46903)
- \[[`d15f522d1f`](https://github.com/nodejs/node/commit/d15f522d1f)] - **doc**: update output of example in AbortController (Deokjin Kim) [#47227](https://github.com/nodejs/node/pull/47227)
- \[[`ab6588e343`](https://github.com/nodejs/node/commit/ab6588e343)] - **doc**: drop one-week branch sync on major releases (Rafael Gonzaga) [#47149](https://github.com/nodejs/node/pull/47149)
- \[[`6ac52e3061`](https://github.com/nodejs/node/commit/6ac52e3061)] - **doc**: fix grammar in the collaborator guide (Mohammed Keyvanzadeh) [#47245](https://github.com/nodejs/node/pull/47245)
- \[[`13175774a6`](https://github.com/nodejs/node/commit/13175774a6)] - **doc**: update stream.reduce concurrency note (Raz Luvaton) [#47166](https://github.com/nodejs/node/pull/47166)
- \[[`1e97ccd6d4`](https://github.com/nodejs/node/commit/1e97ccd6d4)] - **doc**: remove use of DEFAULT_ENCODING in PBKDF2 docs (Tobias Nießen) [#47181](https://github.com/nodejs/node/pull/47181)
- \[[`d562a7c461`](https://github.com/nodejs/node/commit/d562a7c461)] - **doc**: fix typos in async_context.md (Shubham Sharma) [#47155](https://github.com/nodejs/node/pull/47155)
- \[[`9a11788cdf`](https://github.com/nodejs/node/commit/9a11788cdf)] - **doc**: update collaborator guide to reflect TSC changes (Rich Trott) [#47126](https://github.com/nodejs/node/pull/47126)
- \[[`5fc2bb763f`](https://github.com/nodejs/node/commit/5fc2bb763f)] - **doc**: clarify that `fs.create{Read,Write}Stream` support `AbortSignal` (Antoine du Hamel) [#47122](https://github.com/nodejs/node/pull/47122)
- \[[`2dd3b0213e`](https://github.com/nodejs/node/commit/2dd3b0213e)] - **doc**: improve documentation for util.types.isNativeError() (Julian Dax) [#46840](https://github.com/nodejs/node/pull/46840)
- \[[`ce4636e36b`](https://github.com/nodejs/node/commit/ce4636e36b)] - **doc**: rename the startup performance initiative to startup snapshot (#47111) (Joyee Cheung)
- \[[`309d017f15`](https://github.com/nodejs/node/commit/309d017f15)] - **doc**: fix "maintaining dependencies" heading typos (Keyhan Vakil) [#47082](https://github.com/nodejs/node/pull/47082)
- \[[`230a984eb3`](https://github.com/nodejs/node/commit/230a984eb3)] - **doc**: include webstreams in finished() and Duplex.from() parameters (Debadree Chatterjee) [#46312](https://github.com/nodejs/node/pull/46312)
- \[[`8651ea822e`](https://github.com/nodejs/node/commit/8651ea822e)] - **doc,fs**: update description of fs.stat() method (Mert Can Altın) [#47654](https://github.com/nodejs/node/pull/47654)
- \[[`e4539e1f19`](https://github.com/nodejs/node/commit/e4539e1f19)] - **doc,test**: update the v8.startupSnapshot doc and test the example (Joyee Cheung) [#47468](https://github.com/nodejs/node/pull/47468)
- \[[`3dddc0175f`](https://github.com/nodejs/node/commit/3dddc0175f)] - **doc,test**: fix concurrency option of test() (Tobias Nießen) [#47734](https://github.com/nodejs/node/pull/47734)
- \[[`563f9fe06a`](https://github.com/nodejs/node/commit/563f9fe06a)] - **doc,vm**: clarify usage of cachedData in vm.compileFunction() (Darshan Sen) [#48193](https://github.com/nodejs/node/pull/48193)
- \[[`316016ffac`](https://github.com/nodejs/node/commit/316016ffac)] - **esm**: avoid accessing lazy getters for urls (Yagiz Nizipli) [#47542](https://github.com/nodejs/node/pull/47542)
- \[[`e5e385d2b2`](https://github.com/nodejs/node/commit/e5e385d2b2)] - **esm**: increase test coverage of edge cases (Antoine du Hamel) [#47033](https://github.com/nodejs/node/pull/47033)
- \[[`061fb20660`](https://github.com/nodejs/node/commit/061fb20660)] - **(SEMVER-MINOR)** **events**: add getMaxListeners method (Matthew Aitken) [#47039](https://github.com/nodejs/node/pull/47039)
- \[[`ed0b62cc01`](https://github.com/nodejs/node/commit/ed0b62cc01)] - **(SEMVER-MINOR)** **fs**: add support for mode flag to specify the copy behavior (Tetsuharu Ohzeki) [#47084](https://github.com/nodejs/node/pull/47084)
- \[[`9b44c56c9a`](https://github.com/nodejs/node/commit/9b44c56c9a)] - **fs**: make readdir recursive algorithm iterative (Ethan Arrowood) [#47650](https://github.com/nodejs/node/pull/47650)
- \[[`7273ef53b3`](https://github.com/nodejs/node/commit/7273ef53b3)] - **(SEMVER-MINOR)** **fs**: add recursive option to readdir and opendir (Ethan Arrowood) [#41439](https://github.com/nodejs/node/pull/41439)
- \[[`3f0636d2c1`](https://github.com/nodejs/node/commit/3f0636d2c1)] - **(SEMVER-MINOR)** **fs**: add support for mode flag to specify the copy behavior (Tetsuharu Ohzeki) [#47084](https://github.com/nodejs/node/pull/47084)
- \[[`a0b9853251`](https://github.com/nodejs/node/commit/a0b9853251)] - **(SEMVER-MINOR)** **fs**: implement byob mode for readableWebStream() (Debadree Chatterjee) [#46933](https://github.com/nodejs/node/pull/46933)
- \[[`709e368708`](https://github.com/nodejs/node/commit/709e368708)] - **http**: send implicit headers on HEAD with no body (Matteo Collina) [#48108](https://github.com/nodejs/node/pull/48108)
- \[[`dc318f26c0`](https://github.com/nodejs/node/commit/dc318f26c0)] - **(SEMVER-MINOR)** **http**: prevent writing to the body when not allowed by HTTP spec (Gerrard Lindsay) [#47732](https://github.com/nodejs/node/pull/47732)
- \[[`4b2a015642`](https://github.com/nodejs/node/commit/4b2a015642)] - **(SEMVER-MINOR)** **http**: remove internal error in assignSocket (Matteo Collina) [#47723](https://github.com/nodejs/node/pull/47723)
- \[[`7cef6aa721`](https://github.com/nodejs/node/commit/7cef6aa721)] - **(SEMVER-MINOR)** **http**: add highWaterMark opt in http.createServer (HinataKah0) [#47405](https://github.com/nodejs/node/pull/47405)
- \[[`9186f3a0ef`](https://github.com/nodejs/node/commit/9186f3a0ef)] - **http2**: improve nghttp2 error callback (Tobias Nießen) [#47840](https://github.com/nodejs/node/pull/47840)
- \[[`cc7e5dd4cd`](https://github.com/nodejs/node/commit/cc7e5dd4cd)] - **inspector**: add tips for Session (theanarkh) [#47195](https://github.com/nodejs/node/pull/47195)
- \[[`70c0e882d3`](https://github.com/nodejs/node/commit/70c0e882d3)] - **inspector**: log response and requests in the inspector for debugging (Joyee Cheung) [#46941](https://github.com/nodejs/node/pull/46941)
- \[[`6099d2d08b`](https://github.com/nodejs/node/commit/6099d2d08b)] - **inspector**: fix session.disconnect crash (theanarkh) [#46942](https://github.com/nodejs/node/pull/46942)
- \[[`156292d44a`](https://github.com/nodejs/node/commit/156292d44a)] - **lib**: create weakRef only if any signals provided (Chemi Atlow) [#48448](https://github.com/nodejs/node/pull/48448)
- \[[`efaa073303`](https://github.com/nodejs/node/commit/efaa073303)] - **(SEMVER-MINOR)** **lib**: implement AbortSignal.any() (Chemi Atlow) [#47821](https://github.com/nodejs/node/pull/47821)
- \[[`c46b31f3bf`](https://github.com/nodejs/node/commit/c46b31f3bf)] - **lib**: support FORCE_COLOR for non TTY streams (Moshe Atlow) [#48034](https://github.com/nodejs/node/pull/48034)
- \[[`286c358832`](https://github.com/nodejs/node/commit/286c358832)] - **lib**: do not disable linter for entire files (Antoine du Hamel) [#48299](https://github.com/nodejs/node/pull/48299)
- \[[`a2552ab7c0`](https://github.com/nodejs/node/commit/a2552ab7c0)] - **lib**: use existing `isWindows` variable (sinkhaha) [#48134](https://github.com/nodejs/node/pull/48134)
- \[[`2b65625281`](https://github.com/nodejs/node/commit/2b65625281)] - **lib**: update comment (sinkhaha) [#47884](https://github.com/nodejs/node/pull/47884)
- \[[`fa447b5120`](https://github.com/nodejs/node/commit/fa447b5120)] - **lib**: use webidl DOMString converter in EventTarget (Matthew Aitken) [#47514](https://github.com/nodejs/node/pull/47514)
- \[[`33f32dc318`](https://github.com/nodejs/node/commit/33f32dc318)] - **lib**: define Event.isTrusted in the prototype (Santiago Gimeno) [#46974](https://github.com/nodejs/node/pull/46974)
- \[[`50789a5e4a`](https://github.com/nodejs/node/commit/50789a5e4a)] - **lib**: refactor to use `validateBuffer` (Deokjin Kim) [#46489](https://github.com/nodejs/node/pull/46489)
- \[[`3658abea26`](https://github.com/nodejs/node/commit/3658abea26)] - **(SEMVER-MINOR)** **lib**: add webstreams to Duplex.from() (Debadree Chatterjee) [#46190](https://github.com/nodejs/node/pull/46190)
- \[[`fcf3781d22`](https://github.com/nodejs/node/commit/fcf3781d22)] - **lib,src,test**: lint codebase according new rules for v18.x (Juan José Arboleda) [#48697](https://github.com/nodejs/node/pull/48697)
- \[[`b55dc53422`](https://github.com/nodejs/node/commit/b55dc53422)] - **meta**: bump github/codeql-action from 2.3.3 to 2.3.6 (dependabot\[bot]) [#48287](https://github.com/nodejs/node/pull/48287)
- \[[`8ac4579d85`](https://github.com/nodejs/node/commit/8ac4579d85)] - **meta**: bump actions/setup-python from 4.6.0 to 4.6.1 (dependabot\[bot]) [#48286](https://github.com/nodejs/node/pull/48286)
- \[[`b1854fe9c1`](https://github.com/nodejs/node/commit/b1854fe9c1)] - **meta**: bump codecov/codecov-action from 3.1.3 to 3.1.4 (dependabot\[bot]) [#48285](https://github.com/nodejs/node/pull/48285)
- \[[`d9448b8d93`](https://github.com/nodejs/node/commit/d9448b8d93)] - **meta**: remove dont-land-on-v14 auto labeling (Shrujal Shah) [#48031](https://github.com/nodejs/node/pull/48031)
- \[[`bb859768b6`](https://github.com/nodejs/node/commit/bb859768b6)] - **meta**: move one or more collaborators to emeritus (Node.js GitHub Bot) [#48010](https://github.com/nodejs/node/pull/48010)
- \[[`af90fb939b`](https://github.com/nodejs/node/commit/af90fb939b)] - **meta**: bump step-security/harden-runner from 2.3.1 to 2.4.0 (Rich Trott) [#47980](https://github.com/nodejs/node/pull/47980)
- \[[`4dcf5e2052`](https://github.com/nodejs/node/commit/4dcf5e2052)] - **meta**: bump github/codeql-action from 2.3.2 to 2.3.3 (Rich Trott) [#47979](https://github.com/nodejs/node/pull/47979)
- \[[`dab3186ea2`](https://github.com/nodejs/node/commit/dab3186ea2)] - **meta**: bump actions/setup-python from 4.5.0 to 4.6.0 (Rich Trott) [#47968](https://github.com/nodejs/node/pull/47968)
- \[[`546224c13c`](https://github.com/nodejs/node/commit/546224c13c)] - **meta**: add security-wg ping to permission.js (Rafael Gonzaga) [#47941](https://github.com/nodejs/node/pull/47941)
- \[[`353dfbd2d6`](https://github.com/nodejs/node/commit/353dfbd2d6)] - **meta**: bump step-security/harden-runner from 2.2.1 to 2.3.1 (dependabot\[bot]) [#47808](https://github.com/nodejs/node/pull/47808)
- \[[`20a5cc27ec`](https://github.com/nodejs/node/commit/20a5cc27ec)] - **meta**: bump actions/setup-python from 4.5.0 to 4.6.0 (dependabot\[bot]) [#47806](https://github.com/nodejs/node/pull/47806)
- \[[`eef6442d8d`](https://github.com/nodejs/node/commit/eef6442d8d)] - **meta**: bump actions/checkout from 3.3.0 to 3.5.2 (dependabot\[bot]) [#47805](https://github.com/nodejs/node/pull/47805)
- \[[`e30e6a718a`](https://github.com/nodejs/node/commit/e30e6a718a)] - **meta**: remove extra space in scorecard workflow (Mestery) [#47805](https://github.com/nodejs/node/pull/47805)
- \[[`2d13cdebc4`](https://github.com/nodejs/node/commit/2d13cdebc4)] - **meta**: bump github/codeql-action from 2.2.9 to 2.3.2 (dependabot\[bot]) [#47809](https://github.com/nodejs/node/pull/47809)
- \[[`f0d8352ed8`](https://github.com/nodejs/node/commit/f0d8352ed8)] - **meta**: bump codecov/codecov-action from 3.1.1 to 3.1.3 (dependabot\[bot]) [#47807](https://github.com/nodejs/node/pull/47807)
- \[[`7e95fba0ff`](https://github.com/nodejs/node/commit/7e95fba0ff)] - **meta**: fix dependabot commit message (Mestery) [#47810](https://github.com/nodejs/node/pull/47810)
- \[[`d31d9c7c28`](https://github.com/nodejs/node/commit/d31d9c7c28)] - **meta**: ping nodejs/startup for startup test changes (Joyee Cheung) [#47771](https://github.com/nodejs/node/pull/47771)
- \[[`077686055b`](https://github.com/nodejs/node/commit/077686055b)] - **meta**: add mailmap entry for KhafraDev (Rich Trott) [#47512](https://github.com/nodejs/node/pull/47512)
- \[[`e50eb6570a`](https://github.com/nodejs/node/commit/e50eb6570a)] - **meta**: ping security-wg team on permission model changes (Rafael Gonzaga) [#47483](https://github.com/nodejs/node/pull/47483)
- \[[`2df1a36214`](https://github.com/nodejs/node/commit/2df1a36214)] - **meta**: ping startup and realm team on src/node_realm\* changes (Joyee Cheung) [#47448](https://github.com/nodejs/node/pull/47448)
- \[[`c7dc6e321b`](https://github.com/nodejs/node/commit/c7dc6e321b)] - **meta**: fix notable-change comment label url (Filip Skokan) [#47300](https://github.com/nodejs/node/pull/47300)
- \[[`e859ca44d5`](https://github.com/nodejs/node/commit/e859ca44d5)] - **meta**: clarify the threat model to explain the JSON.parse case (Matteo Collina) [#47276](https://github.com/nodejs/node/pull/47276)
- \[[`1f08f4848d`](https://github.com/nodejs/node/commit/1f08f4848d)] - **meta**: update link to collaborators discussion page (Michaël Zasso) [#47211](https://github.com/nodejs/node/pull/47211)
- \[[`3b524cbf86`](https://github.com/nodejs/node/commit/3b524cbf86)] - **meta**: automate description requests when notable change label is added (Danielle Adams) [#47078](https://github.com/nodejs/node/pull/47078)
- \[[`da16ca7c59`](https://github.com/nodejs/node/commit/da16ca7c59)] - **meta**: move TSC voting member(s) to regular member(s) (Node.js GitHub Bot) [#47180](https://github.com/nodejs/node/pull/47180)
- \[[`0c80e60d7e`](https://github.com/nodejs/node/commit/0c80e60d7e)] - **meta**: move TSC voting member to regular membership (Node.js GitHub Bot) [#46985](https://github.com/nodejs/node/pull/46985)
- \[[`0edcfed895`](https://github.com/nodejs/node/commit/0edcfed895)] - **meta**: update GOVERNANCE.md to reflect TSC charter changes (Rich Trott) [#47126](https://github.com/nodejs/node/pull/47126)
- \[[`baada5d035`](https://github.com/nodejs/node/commit/baada5d035)] - **meta**: ask expected behavior reason in bug template (Ben Noordhuis) [#47049](https://github.com/nodejs/node/pull/47049)
- \[[`5d75ec402e`](https://github.com/nodejs/node/commit/5d75ec402e)] - **module**: reduce the number of URL initializations (Yagiz Nizipli) [#48272](https://github.com/nodejs/node/pull/48272)
- \[[`c5af5a4f4f`](https://github.com/nodejs/node/commit/c5af5a4f4f)] - **module**: change default resolver to not throw on unknown scheme (Gil Tayar) [#47824](https://github.com/nodejs/node/pull/47824)
- \[[`cf8845d001`](https://github.com/nodejs/node/commit/cf8845d001)] - **module**: block requiring `test/reporters` without scheme (Moshe Atlow) [#47831](https://github.com/nodejs/node/pull/47831)
- \[[`ce7e6c6765`](https://github.com/nodejs/node/commit/ce7e6c6765)] - **(SEMVER-MINOR)** **node-api**: define version 9 (Chengzhong Wu) [#48151](https://github.com/nodejs/node/pull/48151)
- \[[`53c02b20b8`](https://github.com/nodejs/node/commit/53c02b20b8)] - **node-api**: add status napi_cannot_run_js (Gabriel Schulhof) [#47986](https://github.com/nodejs/node/pull/47986)
- \[[`4b280d5361`](https://github.com/nodejs/node/commit/4b280d5361)] - **node-api**: napi_ref on all types is experimental (Vladimir Morozov) [#47975](https://github.com/nodejs/node/pull/47975)
- \[[`e2553b12e7`](https://github.com/nodejs/node/commit/e2553b12e7)] - **(NODE-API-SEMVER-MAJOR)** **node-api**: get Node API version used by addon (Vladimir Morozov) [#45715](https://github.com/nodejs/node/pull/45715)
- \[[`beaad7f692`](https://github.com/nodejs/node/commit/beaad7f692)] - **node-api**: test passing NULL to napi_define_class (Gabriel Schulhof) [#47567](https://github.com/nodejs/node/pull/47567)
- \[[`6ab892780c`](https://github.com/nodejs/node/commit/6ab892780c)] - **node-api**: test passing NULL to number APIs (Gabriel Schulhof) [#47549](https://github.com/nodejs/node/pull/47549)
- \[[`a67e5ea89c`](https://github.com/nodejs/node/commit/a67e5ea89c)] - **node-api**: remove unused mark_arraybuffer_as_untransferable (Chengzhong Wu) [#47557](https://github.com/nodejs/node/pull/47557)
- \[[`7019d48ba1`](https://github.com/nodejs/node/commit/7019d48ba1)] - **(SEMVER-MINOR)** **node-api**: deprecate napi_module_register (Vladimir Morozov) [#46319](https://github.com/nodejs/node/pull/46319)
- \[[`395c56bd7c`](https://github.com/nodejs/node/commit/395c56bd7c)] - **node-api**: extend type-tagging to externals (Gabriel Schulhof) [#47141](https://github.com/nodejs/node/pull/47141)
- \[[`6e66371eee`](https://github.com/nodejs/node/commit/6e66371eee)] - **node-api**: document node-api shutdown finalization (Chengzhong Wu) [#45903](https://github.com/nodejs/node/pull/45903)
- \[[`d8d2d33efb`](https://github.com/nodejs/node/commit/d8d2d33efb)] - **node-api**: verify cleanup hooks order (Chengzhong Wu) [#46692](https://github.com/nodejs/node/pull/46692)
- \[[`b34eaf393e`](https://github.com/nodejs/node/commit/b34eaf393e)] - **path**: indicate index of wrong resolve() parameter (sosoba) [#47660](https://github.com/nodejs/node/pull/47660)
- \[[`13bc5488a1`](https://github.com/nodejs/node/commit/13bc5488a1)] - **permission**: remove unused function declaration (Deokjin Kim) [#47957](https://github.com/nodejs/node/pull/47957)
- \[[`5f8aef477e`](https://github.com/nodejs/node/commit/5f8aef477e)] - **quic**: address recent coverity warning (Michael Dawson) [#47753](https://github.com/nodejs/node/pull/47753)
- \[[`75d7024ecd`](https://github.com/nodejs/node/commit/75d7024ecd)] - **quic**: add more QUIC implementation (James M Snell) [#47494](https://github.com/nodejs/node/pull/47494)
- \[[`24840832bd`](https://github.com/nodejs/node/commit/24840832bd)] - **quic**: add more QUIC impl (James M Snell) [#47348](https://github.com/nodejs/node/pull/47348)
- \[[`114b8479b4`](https://github.com/nodejs/node/commit/114b8479b4)] - **readline**: fix issue with newline-less last line (Ian Harris) [#47317](https://github.com/nodejs/node/pull/47317)
- \[[`e668efac6b`](https://github.com/nodejs/node/commit/e668efac6b)] - **repl**: preserve preview on ESCAPE key press (Xuguang Mei) [#46878](https://github.com/nodejs/node/pull/46878)
- \[[`7306b0f733`](https://github.com/nodejs/node/commit/7306b0f733)] - **sea**: fix memory leak detected by asan (Darshan Sen) [#47309](https://github.com/nodejs/node/pull/47309)
- \[[`1f2c91f98a`](https://github.com/nodejs/node/commit/1f2c91f98a)] - **src**: use std::array for passing argv in node::url (Anna Henningsen) [#47035](https://github.com/nodejs/node/pull/47035)
- \[[`36bf06904f`](https://github.com/nodejs/node/commit/36bf06904f)] - **src**: add Realm document in the src README.md (Chengzhong Wu) [#47932](https://github.com/nodejs/node/pull/47932)
- \[[`5445835671`](https://github.com/nodejs/node/commit/5445835671)] - **src**: check node_extra_ca_certs after openssl cfg (Raghu Saxena) [#48159](https://github.com/nodejs/node/pull/48159)
- \[[`eb96856555`](https://github.com/nodejs/node/commit/eb96856555)] - **src**: include missing header in node_sea.h (Joyee Cheung) [#48152](https://github.com/nodejs/node/pull/48152)
- \[[`2a35462045`](https://github.com/nodejs/node/commit/2a35462045)] - **src**: deduplicate X509Certificate::Fingerprint\* (Tobias Nießen) [#47978](https://github.com/nodejs/node/pull/47978)
- \[[`4c556816bd`](https://github.com/nodejs/node/commit/4c556816bd)] - **src**: move BlobSerializerDeserializer to a separate header file (Darshan Sen) [#47933](https://github.com/nodejs/node/pull/47933)
- \[[`4bad757012`](https://github.com/nodejs/node/commit/4bad757012)] - **src**: rename SKIP_CHECK_SIZE to SKIP_CHECK_STRLEN (Tobias Nießen) [#47845](https://github.com/nodejs/node/pull/47845)
- \[[`33daa89dac`](https://github.com/nodejs/node/commit/33daa89dac)] - **src**: register ext reference for Fingerprint512 (Tobias Nießen) [#47892](https://github.com/nodejs/node/pull/47892)
- \[[`30b7133008`](https://github.com/nodejs/node/commit/30b7133008)] - **src**: clarify the parameter name in `Permission::Apply` (Daeyeon Jeong) [#47874](https://github.com/nodejs/node/pull/47874)
- \[[`274c0f2e0a`](https://github.com/nodejs/node/commit/274c0f2e0a)] - **src**: avoid strcmp() with Utf8Value (Tobias Nießen) [#47827](https://github.com/nodejs/node/pull/47827)
- \[[`559c98f468`](https://github.com/nodejs/node/commit/559c98f468)] - **src**: prefer data accessor of string and vector (Mohammed Keyvanzadeh) [#47750](https://github.com/nodejs/node/pull/47750)
- \[[`933673de61`](https://github.com/nodejs/node/commit/933673de61)] - **src**: avoid copying string in fs_permission (Yagiz Nizipli) [#47746](https://github.com/nodejs/node/pull/47746)
- \[[`77f2b97197`](https://github.com/nodejs/node/commit/77f2b97197)] - **src**: fix typo in comment in quic/sessionticket.cc (Tobias Nießen) [#47754](https://github.com/nodejs/node/pull/47754)
- \[[`8e6af9fcf4`](https://github.com/nodejs/node/commit/8e6af9fcf4)] - **src**: mark fatal error functions as noreturn (Chengzhong Wu) [#47695](https://github.com/nodejs/node/pull/47695)
- \[[`d0ad873b0e`](https://github.com/nodejs/node/commit/d0ad873b0e)] - **src**: prevent changing FunctionTemplateInfo after publish (Shelley Vohr) [#46979](https://github.com/nodejs/node/pull/46979)
- \[[`71fb476781`](https://github.com/nodejs/node/commit/71fb476781)] - **src**: use v8::Boolean(b) over b ? True() : False() (Tobias Nießen) [#47554](https://github.com/nodejs/node/pull/47554)
- \[[`175b78bc02`](https://github.com/nodejs/node/commit/175b78bc02)] - **src**: fix typo in process.env accessor error message (Moritz Raho) [#47014](https://github.com/nodejs/node/pull/47014)
- \[[`2c2b6d1661`](https://github.com/nodejs/node/commit/2c2b6d1661)] - **src**: replace static const string_view by static constexpr (Daniel Lemire) [#47524](https://github.com/nodejs/node/pull/47524)
- \[[`3840bb586e`](https://github.com/nodejs/node/commit/3840bb586e)] - **src**: fix CSPRNG when length exceeds INT_MAX (Tobias Nießen) [#47515](https://github.com/nodejs/node/pull/47515)
- \[[`f6aa38dc5f`](https://github.com/nodejs/node/commit/f6aa38dc5f)] - **src**: use correct variable in node_builtins.cc (Michaël Zasso) [#47343](https://github.com/nodejs/node/pull/47343)
- \[[`e88e249838`](https://github.com/nodejs/node/commit/e88e249838)] - **src**: slim down stream_base-inl.h (lilsweetcaligula) [#46972](https://github.com/nodejs/node/pull/46972)
- \[[`b34de64442`](https://github.com/nodejs/node/commit/b34de64442)] - **src**: allow simdutf::convert\_\* functions to return zero (Daniel Lemire) [#47471](https://github.com/nodejs/node/pull/47471)
- \[[`ded4a5eb8f`](https://github.com/nodejs/node/commit/ded4a5eb8f)] - **src**: remove usage of `std::shared_ptr<T>::unique()` (Darshan Sen) [#47315](https://github.com/nodejs/node/pull/47315)
- \[[`0fbfb28cf5`](https://github.com/nodejs/node/commit/0fbfb28cf5)] - **src**: use stricter compile-time guidance (Tobias Nießen) [#46509](https://github.com/nodejs/node/pull/46509)
- \[[`a8430ad211`](https://github.com/nodejs/node/commit/a8430ad211)] - **src**: remove unused variable in crypto_x509.cc (Michaël Zasso) [#47344](https://github.com/nodejs/node/pull/47344)
- \[[`24dabf8965`](https://github.com/nodejs/node/commit/24dabf8965)] - **src**: don't reset embeder signal handlers (Dmitry Vyukov) [#47188](https://github.com/nodejs/node/pull/47188)
- \[[`4add36872c`](https://github.com/nodejs/node/commit/4add36872c)] - **src**: replace impossible THROW with CHECK (Tobias Nießen) [#47168](https://github.com/nodejs/node/pull/47168)
- \[[`e1007ff6a8`](https://github.com/nodejs/node/commit/e1007ff6a8)] - **src**: remove dead comments about return_code_cache (Keyhan Vakil) [#47083](https://github.com/nodejs/node/pull/47083)
- \[[`5501d12713`](https://github.com/nodejs/node/commit/5501d12713)] - **src**: remove SSL_CTX_get_tlsext_ticket_keys guards (Tobias Nießen) [#47068](https://github.com/nodejs/node/pull/47068)
- \[[`716d289874`](https://github.com/nodejs/node/commit/716d289874)] - **src**: fix clang 14 linker error (Keyhan Vakil) [#47057](https://github.com/nodejs/node/pull/47057)
- \[[`8809adfdb6`](https://github.com/nodejs/node/commit/8809adfdb6)] - **src**: clarify OptionEnvvarSettings member names (Chengzhong Wu) [#45057](https://github.com/nodejs/node/pull/45057)
- \[[`05f5c79574`](https://github.com/nodejs/node/commit/05f5c79574)] - **src**: per-realm binding data (Chengzhong Wu) [#46556](https://github.com/nodejs/node/pull/46556)
- \[[`a7620d19c8`](https://github.com/nodejs/node/commit/a7620d19c8)] - **src,http2**: ensure cleanup if a frame is not sent (ywave620) [#47244](https://github.com/nodejs/node/pull/47244)
- \[[`585d62848e`](https://github.com/nodejs/node/commit/585d62848e)] - **stream**: deprecate asIndexedPairs (Chemi Atlow) [#48102](https://github.com/nodejs/node/pull/48102)
- \[[`d3449ca010`](https://github.com/nodejs/node/commit/d3449ca010)] - **stream**: prevent pipeline hang with generator functions (Debadree Chatterjee) [#47712](https://github.com/nodejs/node/pull/47712)
- \[[`5e4b2434a6`](https://github.com/nodejs/node/commit/5e4b2434a6)] - **(SEMVER-MINOR)** **stream**: preserve object mode in compose (Raz Luvaton) [#47413](https://github.com/nodejs/node/pull/47413)
- \[[`912eb308ab`](https://github.com/nodejs/node/commit/912eb308ab)] - **(SEMVER-MINOR)** **stream**: add setter & getter for default highWaterMark (#46929) (Robert Nagy) [#46929](https://github.com/nodejs/node/pull/46929)
- \[[`c59887744a`](https://github.com/nodejs/node/commit/c59887744a)] - **stream**: expose stream symbols (Robert Nagy) [#45671](https://github.com/nodejs/node/pull/45671)
- \[[`4edc1abf0b`](https://github.com/nodejs/node/commit/4edc1abf0b)] - **stream**: dont wait for next item in take when finished (Raz Luvaton) [#47132](https://github.com/nodejs/node/pull/47132)
- \[[`cfb18d816d`](https://github.com/nodejs/node/commit/cfb18d816d)] - **stream**: remove brandchecks from stream duplexify (Debadree Chatterjee) [#46315](https://github.com/nodejs/node/pull/46315)
- \[[`9d4025c411`](https://github.com/nodejs/node/commit/9d4025c411)] - **test**: mark test-child-process-pipe-dataflow as flaky (Moshe Atlow) [#48334](https://github.com/nodejs/node/pull/48334)
- \[[`c29b6874d4`](https://github.com/nodejs/node/commit/c29b6874d4)] - **test**: unflake test-vm-timeout-escape-nexttick (Santiago Gimeno) [#48078](https://github.com/nodejs/node/pull/48078)
- \[[`0e3312b01b`](https://github.com/nodejs/node/commit/0e3312b01b)] - **test**: fix zlib version regex (Luigi Pinca) [#48227](https://github.com/nodejs/node/pull/48227)
- \[[`a9d0b8d005`](https://github.com/nodejs/node/commit/a9d0b8d005)] - **test**: use lower security level in s_client (Luigi Pinca) [#48192](https://github.com/nodejs/node/pull/48192)
- \[[`7250d8c2f1`](https://github.com/nodejs/node/commit/7250d8c2f1)] - _**Revert**_ "**test**: unskip negative-settimeout.any.js WPT" (Filip Skokan) [#48182](https://github.com/nodejs/node/pull/48182)
- \[[`18476697bd`](https://github.com/nodejs/node/commit/18476697bd)] - **test**: mark test_cannot_run_js as flaky (Keyhan Vakil) [#48181](https://github.com/nodejs/node/pull/48181)
- \[[`446f6118ff`](https://github.com/nodejs/node/commit/446f6118ff)] - **test**: fix flaky test-runner-watch-mode (Moshe Atlow) [#48144](https://github.com/nodejs/node/pull/48144)
- \[[`7fa144e3fe`](https://github.com/nodejs/node/commit/7fa144e3fe)] - **test**: skip test-http-pipeline-flood on IBM i (Abdirahim Musse) [#48048](https://github.com/nodejs/node/pull/48048)
- \[[`5c5b1d2867`](https://github.com/nodejs/node/commit/5c5b1d2867)] - **test**: ignore helper files in WPTs (Filip Skokan) [#48079](https://github.com/nodejs/node/pull/48079)
- \[[`3b2cee071b`](https://github.com/nodejs/node/commit/3b2cee071b)] - **test**: move `test-cluster-primary-error` flaky test (Yagiz Nizipli) [#48039](https://github.com/nodejs/node/pull/48039)
- \[[`7b816b4922`](https://github.com/nodejs/node/commit/7b816b4922)] - **test**: fix suite signal (Benjamin Gruenbaum) [#47800](https://github.com/nodejs/node/pull/47800)
- \[[`ca4a0e3717`](https://github.com/nodejs/node/commit/ca4a0e3717)] - **test**: fix parsing test flags (Daeyeon Jeong) [#48012](https://github.com/nodejs/node/pull/48012)
- \[[`a3f0504556`](https://github.com/nodejs/node/commit/a3f0504556)] - **test**: mark test-esm-loader-http-imports as flaky (Tobias Nießen) [#47987](https://github.com/nodejs/node/pull/47987)
- \[[`ab36a30143`](https://github.com/nodejs/node/commit/ab36a30143)] - **test**: unskip negative-settimeout.any.js WPT (Filip Skokan) [#47946](https://github.com/nodejs/node/pull/47946)
- \[[`7c80439b21`](https://github.com/nodejs/node/commit/7c80439b21)] - **test**: use appropriate usages for a negative import test (Filip Skokan) [#47878](https://github.com/nodejs/node/pull/47878)
- \[[`81d8d95ba0`](https://github.com/nodejs/node/commit/81d8d95ba0)] - **test**: fix webcrypto wrap unwrap tests (Filip Skokan) [#47876](https://github.com/nodejs/node/pull/47876)
- \[[`1b84e85576`](https://github.com/nodejs/node/commit/1b84e85576)] - **test**: fix output tests when path includes node version (Moshe Atlow) [#47843](https://github.com/nodejs/node/pull/47843)
- \[[`95972aac8d`](https://github.com/nodejs/node/commit/95972aac8d)] - **test**: migrate a pseudo_tty test to use assertSnapshot (Moshe Atlow) [#47803](https://github.com/nodejs/node/pull/47803)
- \[[`f1e131283d`](https://github.com/nodejs/node/commit/f1e131283d)] - **test**: fix WPT state when process exits but workers are still running (Filip Skokan) [#47826](https://github.com/nodejs/node/pull/47826)
- \[[`03dcf7bc94`](https://github.com/nodejs/node/commit/03dcf7bc94)] - **test**: migrate message tests to use assertSnapshot (Moshe Atlow) [#47498](https://github.com/nodejs/node/pull/47498)
- \[[`dedbeee336`](https://github.com/nodejs/node/commit/dedbeee336)] - **test**: refactor to use `getEventListeners` in timers (Deokjin Kim) [#47759](https://github.com/nodejs/node/pull/47759)
- \[[`11a2d1c4e4`](https://github.com/nodejs/node/commit/11a2d1c4e4)] - **test**: add and use tmpdir.hasEnoughSpace() (Tobias Nießen) [#47767](https://github.com/nodejs/node/pull/47767)
- \[[`d669714e57`](https://github.com/nodejs/node/commit/d669714e57)] - **test**: remove spaces from test runner test names (Tobias Nießen) [#47733](https://github.com/nodejs/node/pull/47733)
- \[[`3a9c43a6d7`](https://github.com/nodejs/node/commit/3a9c43a6d7)] - **test**: mark test-cluster-primary-error flaky on asan (Yagiz Nizipli) [#47422](https://github.com/nodejs/node/pull/47422)
- \[[`bd1eb14cb0`](https://github.com/nodejs/node/commit/bd1eb14cb0)] - **test**: remove unnecessary status check on test-release-npm (RafaelGSS) [#47516](https://github.com/nodejs/node/pull/47516)
- \[[`914f68d953`](https://github.com/nodejs/node/commit/914f68d953)] - **test**: mark test/parallel/test-file-write-stream4 as flaky (Yagiz Nizipli) [#47423](https://github.com/nodejs/node/pull/47423)
- \[[`7c4178cb11`](https://github.com/nodejs/node/commit/7c4178cb11)] - **test**: remove unused callback variables (angellovc) [#47167](https://github.com/nodejs/node/pull/47167)
- \[[`d0bda902dc`](https://github.com/nodejs/node/commit/d0bda902dc)] - **test**: migrate test runner message tests to snapshot (Moshe Atlow) [#47392](https://github.com/nodejs/node/pull/47392)
- \[[`095ca5ccf2`](https://github.com/nodejs/node/commit/095ca5ccf2)] - **test**: remove stale entry from known_issues.status (Richard Lau) [#47454](https://github.com/nodejs/node/pull/47454)
- \[[`8820d5415b`](https://github.com/nodejs/node/commit/8820d5415b)] - **test**: move more inspector sequential tests to parallel (Joyee Cheung) [#47412](https://github.com/nodejs/node/pull/47412)
- \[[`f6ef5c4ad3`](https://github.com/nodejs/node/commit/f6ef5c4ad3)] - **test**: use random port in test-inspector-enabled (Joyee Cheung) [#47412](https://github.com/nodejs/node/pull/47412)
- \[[`e97ceeca69`](https://github.com/nodejs/node/commit/e97ceeca69)] - **test**: use random port in test-inspector-debug-brk-flag (Joyee Cheung) [#47412](https://github.com/nodejs/node/pull/47412)
- \[[`a2e4643981`](https://github.com/nodejs/node/commit/a2e4643981)] - **test**: use random port in NodeInstance.startViaSignal() (Joyee Cheung) [#47412](https://github.com/nodejs/node/pull/47412)
- \[[`a779261732`](https://github.com/nodejs/node/commit/a779261732)] - **test**: fix flaky test-watch-mode-inspect (Moshe Atlow) [#47403](https://github.com/nodejs/node/pull/47403)
- \[[`116df2ad3e`](https://github.com/nodejs/node/commit/116df2ad3e)] - **test**: move debugger tests with --port=0 to parallel (Joyee Cheung) [#47274](https://github.com/nodejs/node/pull/47274)
- \[[`016b8bd27d`](https://github.com/nodejs/node/commit/016b8bd27d)] - **test**: use --port=0 in debugger tests that do not have to work on 9229 (Joyee Cheung) [#47274](https://github.com/nodejs/node/pull/47274)
- \[[`3c157cb7a3`](https://github.com/nodejs/node/commit/3c157cb7a3)] - **test**: run doctool tests in parallel (Joyee Cheung) [#47273](https://github.com/nodejs/node/pull/47273)
- \[[`44f08ed941`](https://github.com/nodejs/node/commit/44f08ed941)] - **test**: move test-shadow-realm-gc.js to known_issues (Joyee Cheung) [#47355](https://github.com/nodejs/node/pull/47355)
- \[[`e2b5d968c3`](https://github.com/nodejs/node/commit/e2b5d968c3)] - **test**: update wasm/jsapi WPT (Michaël Zasso) [#47210](https://github.com/nodejs/node/pull/47210)
- \[[`53d9eb5950`](https://github.com/nodejs/node/commit/53d9eb5950)] - **test**: skip test-wasm-web-api on ARM (Michaël Zasso) [#47299](https://github.com/nodejs/node/pull/47299)
- \[[`b620f5fcd6`](https://github.com/nodejs/node/commit/b620f5fcd6)] - **test**: skip instantiateStreaming-bad-imports WPT (Michaël Zasso) [#47292](https://github.com/nodejs/node/pull/47292)
- \[[`1e6fe56333`](https://github.com/nodejs/node/commit/1e6fe56333)] - **test**: fix test-child-process-exec-cwd (Stefan Stojanovic) [#47235](https://github.com/nodejs/node/pull/47235)
- \[[`0dc4971aab`](https://github.com/nodejs/node/commit/0dc4971aab)] - **test**: skip broken tests win arm64 (Stefan Stojanovic) [#47020](https://github.com/nodejs/node/pull/47020)
- \[[`6fee6050e3`](https://github.com/nodejs/node/commit/6fee6050e3)] - **test**: fix 'checks' validation test for checkPrime (Tobias Nießen) [#47139](https://github.com/nodejs/node/pull/47139)
- \[[`6b85472f01`](https://github.com/nodejs/node/commit/6b85472f01)] - **test**: update URL web-platform-tests (Yagiz Nizipli) [#47135](https://github.com/nodejs/node/pull/47135)
- \[[`732a98e1ba`](https://github.com/nodejs/node/commit/732a98e1ba)] - **test**: reduce flakiness of test-http-remove-header-stays-removed.js (Debadree Chatterjee) [#46855](https://github.com/nodejs/node/pull/46855)
- \[[`713b412ee9`](https://github.com/nodejs/node/commit/713b412ee9)] - **test**: mark test-http-max-sockets as flaky on win32 (Tobias Nießen) [#47134](https://github.com/nodejs/node/pull/47134)
- \[[`b3b7dbc395`](https://github.com/nodejs/node/commit/b3b7dbc395)] - **test**: update web-platform tests for url (Xuguang Mei) [#46860](https://github.com/nodejs/node/pull/46860)
- \[[`2edd0fdb5b`](https://github.com/nodejs/node/commit/2edd0fdb5b)] - **test**: mark test-child-process-stdio-reuse-readable-stdio flaky (Luigi Pinca) [#48537](https://github.com/nodejs/node/pull/48537)
- \[[`fae240e492`](https://github.com/nodejs/node/commit/fae240e492)] - **test**: mark test-child-process-pipe-dataflow as flaky (Moshe Atlow) [#48334](https://github.com/nodejs/node/pull/48334)
- \[[`9ca3cc0f2a`](https://github.com/nodejs/node/commit/9ca3cc0f2a)] - **test**: remove useless require('../common') from WPTs (Filip Skokan) [#46796](https://github.com/nodejs/node/pull/46796)
- \[[`4bece055a5`](https://github.com/nodejs/node/commit/4bece055a5)] - **test,crypto**: update WebCryptoAPI WPT (Filip Skokan) [#47921](https://github.com/nodejs/node/pull/47921)
- \[[`7ef169b706`](https://github.com/nodejs/node/commit/7ef169b706)] - **test,crypto**: update WebCryptoAPI WPT (Filip Skokan) [#47222](https://github.com/nodejs/node/pull/47222)
- \[[`873606b355`](https://github.com/nodejs/node/commit/873606b355)] - **test,crypto**: update WebCryptoAPI WPT (Filip Skokan) [#47131](https://github.com/nodejs/node/pull/47131)
- \[[`8f1fc0bc99`](https://github.com/nodejs/node/commit/8f1fc0bc99)] - **test,doc,sea**: run SEA tests on ppc64 (Darshan Sen) [#48111](https://github.com/nodejs/node/pull/48111)
- \[[`5d910ca389`](https://github.com/nodejs/node/commit/5d910ca389)] - **test_runner**: add enqueue and dequeue events (Moshe Atlow) [#48428](https://github.com/nodejs/node/pull/48428)
- \[[`d795c0ab0c`](https://github.com/nodejs/node/commit/d795c0ab0c)] - **test_runner**: dont split lines on `test:stdout` (Moshe Atlow) [#48057](https://github.com/nodejs/node/pull/48057)
- \[[`04eb1f85da`](https://github.com/nodejs/node/commit/04eb1f85da)] - **test_runner**: pass FORCE_COLOR to child process (Moshe Atlow) [#48057](https://github.com/nodejs/node/pull/48057)
- \[[`2262653148`](https://github.com/nodejs/node/commit/2262653148)] - **test_runner**: apply `runOnly` on suites (Moshe Atlow) [#48279](https://github.com/nodejs/node/pull/48279)
- \[[`033d0bb3e1`](https://github.com/nodejs/node/commit/033d0bb3e1)] - **test_runner**: emit `test:watch:drained` event (Moshe Atlow) [#48259](https://github.com/nodejs/node/pull/48259)
- \[[`618a9e1c09`](https://github.com/nodejs/node/commit/618a9e1c09)] - **test_runner**: stop watch mode when abortSignal aborted (Moshe Atlow) [#48259](https://github.com/nodejs/node/pull/48259)
- \[[`6a82fbd006`](https://github.com/nodejs/node/commit/6a82fbd006)] - **test_runner**: fix global after hook (Moshe Atlow) [#48231](https://github.com/nodejs/node/pull/48231)
- \[[`d1295d7b10`](https://github.com/nodejs/node/commit/d1295d7b10)] - **test_runner**: remove redundant check from coverage (Colin Ihrig) [#48070](https://github.com/nodejs/node/pull/48070)
- \[[`47602fe73b`](https://github.com/nodejs/node/commit/47602fe73b)] - **test_runner**: fix test deserialize edge cases (Moshe Atlow) [#48106](https://github.com/nodejs/node/pull/48106)
- \[[`b18a78cd0b`](https://github.com/nodejs/node/commit/b18a78cd0b)] - **test_runner**: delegate stderr and stdout formatting to reporter (Shiba) [#48045](https://github.com/nodejs/node/pull/48045)
- \[[`e0d0b19c30`](https://github.com/nodejs/node/commit/e0d0b19c30)] - **test_runner**: display dot report as wide as the terminal width (Raz Luvaton) [#48038](https://github.com/nodejs/node/pull/48038)
- \[[`bdca468a79`](https://github.com/nodejs/node/commit/bdca468a79)] - **test_runner**: use v8.serialize instead of TAP (Moshe Atlow) [#47867](https://github.com/nodejs/node/pull/47867)
- \[[`866ed6a887`](https://github.com/nodejs/node/commit/866ed6a887)] - **(SEMVER-MINOR)** **test_runner**: add shorthands to `test` (Chemi Atlow) [#47909](https://github.com/nodejs/node/pull/47909)
- \[[`4737314865`](https://github.com/nodejs/node/commit/4737314865)] - **test_runner**: fix ordering of test hooks (Phil Nash) [#47931](https://github.com/nodejs/node/pull/47931)
- \[[`ea543d95f6`](https://github.com/nodejs/node/commit/ea543d95f6)] - **test_runner**: omit inaccessible files from coverage (Colin Ihrig) [#47850](https://github.com/nodejs/node/pull/47850)
- \[[`8398bca842`](https://github.com/nodejs/node/commit/8398bca842)] - **test_runner**: fix --require with --experimental-loader (Moshe Atlow) [#47751](https://github.com/nodejs/node/pull/47751)
- \[[`4c0036ba1b`](https://github.com/nodejs/node/commit/4c0036ba1b)] - **(SEMVER-MINOR)** **test_runner**: support combining coverage reports (Colin Ihrig) [#47686](https://github.com/nodejs/node/pull/47686)
- \[[`cb3abda9aa`](https://github.com/nodejs/node/commit/cb3abda9aa)] - **test_runner**: remove no-op validation (Colin Ihrig) [#47687](https://github.com/nodejs/node/pull/47687)
- \[[`323881f60b`](https://github.com/nodejs/node/commit/323881f60b)] - **test_runner**: fix test runner concurrency (Moshe Atlow) [#47675](https://github.com/nodejs/node/pull/47675)
- \[[`3bbb1fc990`](https://github.com/nodejs/node/commit/3bbb1fc990)] - **test_runner**: fix test counting (Moshe Atlow) [#47675](https://github.com/nodejs/node/pull/47675)
- \[[`0ea63717ba`](https://github.com/nodejs/node/commit/0ea63717ba)] - **test_runner**: fix nested hooks (Moshe Atlow) [#47648](https://github.com/nodejs/node/pull/47648)
- \[[`fa18b17d88`](https://github.com/nodejs/node/commit/fa18b17d88)] - **test_runner**: add testNamePatterns to run api (atlowChemi) [#47648](https://github.com/nodejs/node/pull/47648)
- \[[`2033691bfc`](https://github.com/nodejs/node/commit/2033691bfc)] - **test_runner**: support coverage of unnamed functions (Colin Ihrig) [#47652](https://github.com/nodejs/node/pull/47652)
- \[[`882c6127ae`](https://github.com/nodejs/node/commit/882c6127ae)] - **test_runner**: move coverage collection to root.postRun() (Colin Ihrig) [#47651](https://github.com/nodejs/node/pull/47651)
- \[[`e97eefa538`](https://github.com/nodejs/node/commit/e97eefa538)] - **(SEMVER-MINOR)** **test_runner**: execute before hook on test (Chemi Atlow) [#47586](https://github.com/nodejs/node/pull/47586)
- \[[`4bce39108c`](https://github.com/nodejs/node/commit/4bce39108c)] - **test_runner**: avoid reporting parents of failing tests in summary (Moshe Atlow) [#47579](https://github.com/nodejs/node/pull/47579)
- \[[`688078b93a`](https://github.com/nodejs/node/commit/688078b93a)] - **test_runner**: fix spec skip detection (Moshe Atlow) [#47537](https://github.com/nodejs/node/pull/47537)
- \[[`0b32a8c8a3`](https://github.com/nodejs/node/commit/0b32a8c8a3)] - **test_runner**: color errors only when colors are available (Moshe Atlow) [#47394](https://github.com/nodejs/node/pull/47394)
- \[[`d5fc8236bf`](https://github.com/nodejs/node/commit/d5fc8236bf)] - **test_runner**: hide failing tests title when all tests pass (Moshe Atlow) [#47370](https://github.com/nodejs/node/pull/47370)
- \[[`1d453e4d31`](https://github.com/nodejs/node/commit/1d453e4d31)] - **test_runner**: stringify AssertError expected and actual (Moshe Atlow) [#47088](https://github.com/nodejs/node/pull/47088)
- \[[`99312a55f2`](https://github.com/nodejs/node/commit/99312a55f2)] - **test_runner**: add code coverage support to spec reporter (Pulkit Gupta) [#46674](https://github.com/nodejs/node/pull/46674)
- \[[`2091b4718f`](https://github.com/nodejs/node/commit/2091b4718f)] - **(SEMVER-MINOR)** **test_runner**: expose reporter for use in run api (Chemi Atlow) [#47238](https://github.com/nodejs/node/pull/47238)
- \[[`9cbf89717e`](https://github.com/nodejs/node/commit/9cbf89717e)] - **test_runner**: report failing tests after summary (HinataKah0) [#47164](https://github.com/nodejs/node/pull/47164)
- \[[`460bcc042e`](https://github.com/nodejs/node/commit/460bcc042e)] - **test_runner**: count nested tests (Moshe Atlow) [#47094](https://github.com/nodejs/node/pull/47094)
- \[[`c62e6b2e54`](https://github.com/nodejs/node/commit/c62e6b2e54)] - **test_runner**: accept \x1b as a escape symbol (Debadree Chatterjee) [#47050](https://github.com/nodejs/node/pull/47050)
- \[[`ddf819f810`](https://github.com/nodejs/node/commit/ddf819f810)] - **test_runner**: support defining test reporter in NODE_OPTIONS (Steve Herzog) [#46688](https://github.com/nodejs/node/pull/46688)
- \[[`e049ce296a`](https://github.com/nodejs/node/commit/e049ce296a)] - **tls**: reapply servername on happy eyeballs connect (Fedor Indutny) [#48255](https://github.com/nodejs/node/pull/48255)
- \[[`19b0f244b2`](https://github.com/nodejs/node/commit/19b0f244b2)] - **tls**: accept SecureContext object in server.addContext() (HinataKah0) [#47570](https://github.com/nodejs/node/pull/47570)
- \[[`7786d7cece`](https://github.com/nodejs/node/commit/7786d7cece)] - **tools**: pin ruff version number (Rich Trott) [#48505](https://github.com/nodejs/node/pull/48505)
- \[[`a2bfe02289`](https://github.com/nodejs/node/commit/a2bfe02289)] - **tools**: remove non-existing file from CODEOWNERS file (Juan José Arboleda) [#48697](https://github.com/nodejs/node/pull/48697)
- \[[`87562c9af0`](https://github.com/nodejs/node/commit/87562c9af0)] - **tools**: update rollup lint-md-dependencies (Node.js GitHub Bot) [#48329](https://github.com/nodejs/node/pull/48329)
- \[[`1cde4a4299`](https://github.com/nodejs/node/commit/1cde4a4299)] - _**Revert**_ "**tools**: open issue when update workflow fails" (Marco Ippolito) [#48312](https://github.com/nodejs/node/pull/48312)
- \[[`361cf8cffc`](https://github.com/nodejs/node/commit/361cf8cffc)] - **tools**: don't gitignore base64 config.h (Ben Noordhuis) [#48174](https://github.com/nodejs/node/pull/48174)
- \[[`0cfdb3affa`](https://github.com/nodejs/node/commit/0cfdb3affa)] - **tools**: update LICENSE and license-builder.sh (Santiago Gimeno) [#48078](https://github.com/nodejs/node/pull/48078)
- \[[`715cf81b4a`](https://github.com/nodejs/node/commit/715cf81b4a)] - **tools**: automate histogram update (Marco Ippolito) [#48171](https://github.com/nodejs/node/pull/48171)
- \[[`d9afffab03`](https://github.com/nodejs/node/commit/d9afffab03)] - **tools**: use shasum instead of sha256sum (Luigi Pinca) [#48229](https://github.com/nodejs/node/pull/48229)
- \[[`1a5cddfc1f`](https://github.com/nodejs/node/commit/1a5cddfc1f)] - **tools**: harmonize `dep_updaters` scripts (Antoine du Hamel) [#48201](https://github.com/nodejs/node/pull/48201)
- \[[`24abe07dda`](https://github.com/nodejs/node/commit/24abe07dda)] - **tools**: log and verify sha256sum (Andrea Fassina) [#48088](https://github.com/nodejs/node/pull/48088)
- \[[`9aed8683aa`](https://github.com/nodejs/node/commit/9aed8683aa)] - **tools**: open issue when update workflow fails (Marco Ippolito) [#48018](https://github.com/nodejs/node/pull/48018)
- \[[`4c5c63fd82`](https://github.com/nodejs/node/commit/4c5c63fd82)] - **tools**: alphabetize CODEOWNERS (Rich Trott) [#48124](https://github.com/nodejs/node/pull/48124)
- \[[`9b849a7270`](https://github.com/nodejs/node/commit/9b849a7270)] - **tools**: use latest upstream commit for zlib updates (Andrea Fassina) [#48054](https://github.com/nodejs/node/pull/48054)
- \[[`e18c1258ae`](https://github.com/nodejs/node/commit/e18c1258ae)] - **tools**: add security-wg as dep updaters owner (Marco Ippolito) [#48113](https://github.com/nodejs/node/pull/48113)
- \[[`999a289dd9`](https://github.com/nodejs/node/commit/999a289dd9)] - **tools**: fix race condition when npm installing (Tobias Nießen) [#48101](https://github.com/nodejs/node/pull/48101)
- \[[`25b0033b86`](https://github.com/nodejs/node/commit/25b0033b86)] - **tools**: refloat 7 Node.js patches to cpplint.py (Rich Trott) [#48098](https://github.com/nodejs/node/pull/48098)
- \[[`87b36b3a8a`](https://github.com/nodejs/node/commit/87b36b3a8a)] - **tools**: update cpplint to 1.6.1 (Yagiz Nizipli) [#48098](https://github.com/nodejs/node/pull/48098)
- \[[`64ff6fe443`](https://github.com/nodejs/node/commit/64ff6fe443)] - **tools**: update eslint to 8.41.0 (Node.js GitHub Bot) [#48097](https://github.com/nodejs/node/pull/48097)
- \[[`739c314851`](https://github.com/nodejs/node/commit/739c314851)] - **tools**: update lint-md-dependencies (Node.js GitHub Bot) [#48096](https://github.com/nodejs/node/pull/48096)
- \[[`6674ed1901`](https://github.com/nodejs/node/commit/6674ed1901)] - **tools**: update doc to remark-parse\@10.0.2 (Node.js GitHub Bot) [#48095](https://github.com/nodejs/node/pull/48095)
- \[[`0b0818caab`](https://github.com/nodejs/node/commit/0b0818caab)] - **tools**: add debug logs (Marco Ippolito) [#48060](https://github.com/nodejs/node/pull/48060)
- \[[`168d080f40`](https://github.com/nodejs/node/commit/168d080f40)] - **tools**: fix zconf.h path (Luigi Pinca) [#48089](https://github.com/nodejs/node/pull/48089)
- \[[`ccd2795f42`](https://github.com/nodejs/node/commit/ccd2795f42)] - **tools**: update remark-preset-lint-node to 4.0.0 (Node.js GitHub Bot) [#47995](https://github.com/nodejs/node/pull/47995)
- \[[`89e068a550`](https://github.com/nodejs/node/commit/89e068a550)] - **tools**: debug log for nghttp3 (Marco Ippolito) [#47992](https://github.com/nodejs/node/pull/47992)
- \[[`3bf2bd4ec6`](https://github.com/nodejs/node/commit/3bf2bd4ec6)] - **tools**: automate icu-small update (Marco Ippolito) [#47727](https://github.com/nodejs/node/pull/47727)
- \[[`fdf9681dc0`](https://github.com/nodejs/node/commit/fdf9681dc0)] - **tools**: update lint-md-dependencies to rollup\@3.21.5 (Node.js GitHub Bot) [#47903](https://github.com/nodejs/node/pull/47903)
- \[[`52532c2cb1`](https://github.com/nodejs/node/commit/52532c2cb1)] - **tools**: update eslint to 8.40.0 (Node.js GitHub Bot) [#47906](https://github.com/nodejs/node/pull/47906)
- \[[`951cc7b624`](https://github.com/nodejs/node/commit/951cc7b624)] - **tools**: update eslint to 8.39.0 (Node.js GitHub Bot) [#47789](https://github.com/nodejs/node/pull/47789)
- \[[`706c87db50`](https://github.com/nodejs/node/commit/706c87db50)] - **tools**: fix jsdoc lint (Moshe Atlow) [#47789](https://github.com/nodejs/node/pull/47789)
- \[[`8eef5f5f5f`](https://github.com/nodejs/node/commit/8eef5f5f5f)] - **tools**: update doc to highlight.js\@11.8.0 (Node.js GitHub Bot) [#47786](https://github.com/nodejs/node/pull/47786)
- \[[`899eea6237`](https://github.com/nodejs/node/commit/899eea6237)] - **tools**: update lint-md-dependencies to rollup\@3.21.1 (Node.js GitHub Bot) [#47787](https://github.com/nodejs/node/pull/47787)
- \[[`8ca50b6ac9`](https://github.com/nodejs/node/commit/8ca50b6ac9)] - **tools**: move update-npm to dep updaters (Marco Ippolito) [#47619](https://github.com/nodejs/node/pull/47619)
- \[[`5ee554397d`](https://github.com/nodejs/node/commit/5ee554397d)] - **tools**: fix update-v8-patch cache (Marco Ippolito) [#47725](https://github.com/nodejs/node/pull/47725)
- \[[`4e0d19e1bd`](https://github.com/nodejs/node/commit/4e0d19e1bd)] - **tools**: automate v8 patch update (Marco Ippolito) [#47594](https://github.com/nodejs/node/pull/47594)
- \[[`64c3154bdf`](https://github.com/nodejs/node/commit/64c3154bdf)] - **tools**: fix skip message in update-cjs-module-lexer (Tobias Nießen) [#47701](https://github.com/nodejs/node/pull/47701)
- \[[`6535ab90d9`](https://github.com/nodejs/node/commit/6535ab90d9)] - **tools**: update lint-md-dependencies to @rollup/plugin-commonjs\@24.1.0 (Node.js GitHub Bot) [#47577](https://github.com/nodejs/node/pull/47577)
- \[[`7cfc00d072`](https://github.com/nodejs/node/commit/7cfc00d072)] - **tools**: keep PR titles/description up-to-date (Tobias Nießen) [#47621](https://github.com/nodejs/node/pull/47621)
- \[[`d6b555e2c8`](https://github.com/nodejs/node/commit/d6b555e2c8)] - **tools**: fix updating root certificates (Richard Lau) [#47607](https://github.com/nodejs/node/pull/47607)
- \[[`0d3e3ddea6`](https://github.com/nodejs/node/commit/0d3e3ddea6)] - **tools**: update PR label config (Mohammed Keyvanzadeh) [#47593](https://github.com/nodejs/node/pull/47593)
- \[[`fa52c472b8`](https://github.com/nodejs/node/commit/fa52c472b8)] - **tools**: add execution permission to uvwasi script (Mert Can Altın) [#47600](https://github.com/nodejs/node/pull/47600)
- \[[`f6955a6b0c`](https://github.com/nodejs/node/commit/f6955a6b0c)] - **tools**: add update script for googletest (Tobias Nießen) [#47482](https://github.com/nodejs/node/pull/47482)
- \[[`4e1d87e752`](https://github.com/nodejs/node/commit/4e1d87e752)] - **tools**: add option to run workflow with specific tool id (Michaël Zasso) [#47591](https://github.com/nodejs/node/pull/47591)
- \[[`e605402590`](https://github.com/nodejs/node/commit/e605402590)] - **tools**: automate zlib update (Marco Ippolito) [#47417](https://github.com/nodejs/node/pull/47417)
- \[[`b6ca57e8d0`](https://github.com/nodejs/node/commit/b6ca57e8d0)] - **tools**: add url and whatwg-url labels automatically (Yagiz Nizipli) [#47545](https://github.com/nodejs/node/pull/47545)
- \[[`d5c9bc4f8e`](https://github.com/nodejs/node/commit/d5c9bc4f8e)] - **tools**: add performance label to benchmark changes (Yagiz Nizipli) [#47545](https://github.com/nodejs/node/pull/47545)
- \[[`c5227628a9`](https://github.com/nodejs/node/commit/c5227628a9)] - **tools**: automate uvwasi dependency update (Ranieri Innocenti Spada) [#47509](https://github.com/nodejs/node/pull/47509)
- \[[`5ffdb57302`](https://github.com/nodejs/node/commit/5ffdb57302)] - **tools**: add missing pinned dependencies (Mateo Nunez) [#47346](https://github.com/nodejs/node/pull/47346)
- \[[`c7b898d4e4`](https://github.com/nodejs/node/commit/c7b898d4e4)] - **tools**: automate ngtcp2 and nghttp3 update (Marco Ippolito) [#47402](https://github.com/nodejs/node/pull/47402)
- \[[`e696a48225`](https://github.com/nodejs/node/commit/e696a48225)] - **tools**: move update-undici.sh to dep_updaters and create maintain md (Marco Ippolito) [#47380](https://github.com/nodejs/node/pull/47380)
- \[[`056067286a`](https://github.com/nodejs/node/commit/056067286a)] - **tools**: make `js2c.py` usable for other build systems (Cheng Zhao) [#46930](https://github.com/nodejs/node/pull/46930)
- \[[`d11c6ba2eb`](https://github.com/nodejs/node/commit/d11c6ba2eb)] - **tools**: move update-acorn.sh to dep_updaters and create maintaining md (Marco Ippolito) [#47382](https://github.com/nodejs/node/pull/47382)
- \[[`0a65c7c300`](https://github.com/nodejs/node/commit/0a65c7c300)] - **tools**: update eslint to 8.38.0 (Node.js GitHub Bot) [#47475](https://github.com/nodejs/node/pull/47475)
- \[[`60dc249cd5`](https://github.com/nodejs/node/commit/60dc249cd5)] - **tools**: update eslint to 8.38.0 (Node.js GitHub Bot) [#47475](https://github.com/nodejs/node/pull/47475)
- \[[`1271b0eded`](https://github.com/nodejs/node/commit/1271b0eded)] - **tools**: automate cjs-module-lexer dependency update (Marco Ippolito) [#47446](https://github.com/nodejs/node/pull/47446)
- \[[`26905572d4`](https://github.com/nodejs/node/commit/26905572d4)] - **tools**: fix notify-on-push Slack messages (Antoine du Hamel) [#47453](https://github.com/nodejs/node/pull/47453)
- \[[`3c62663797`](https://github.com/nodejs/node/commit/3c62663797)] - **tools**: update lint-md-dependencies to @rollup/plugin-node-resolve\@15.0.2 (Node.js GitHub Bot) [#47431](https://github.com/nodejs/node/pull/47431)
- \[[`c57dabe360`](https://github.com/nodejs/node/commit/c57dabe360)] - **tools**: add root certificate update script (Richard Lau) [#47425](https://github.com/nodejs/node/pull/47425)
- \[[`f27680e37c`](https://github.com/nodejs/node/commit/f27680e37c)] - **tools**: fix update-openssl.yml compare version (Marco Ippolito) [#47384](https://github.com/nodejs/node/pull/47384)
- \[[`35e6cf2944`](https://github.com/nodejs/node/commit/35e6cf2944)] - **tools**: use ref_name to get branch pushed on (Debadree Chatterjee) [#47358](https://github.com/nodejs/node/pull/47358)
- \[[`28935a86f8`](https://github.com/nodejs/node/commit/28935a86f8)] - **tools**: add a at here tag for slack messages (Debadree Chatterjee) [#47358](https://github.com/nodejs/node/pull/47358)
- \[[`e1846ee4f1`](https://github.com/nodejs/node/commit/e1846ee4f1)] - **tools**: disable Codecov commit statuses (Michaël Zasso) [#47306](https://github.com/nodejs/node/pull/47306)
- \[[`d1c8229da4`](https://github.com/nodejs/node/commit/d1c8229da4)] - **tools**: update eslint to 8.37.0 (Node.js GitHub Bot) [#47333](https://github.com/nodejs/node/pull/47333)
- \[[`6ab22151c4`](https://github.com/nodejs/node/commit/6ab22151c4)] - **tools**: fix duration_ms to be milliseconds (Moshe Atlow) [#44490](https://github.com/nodejs/node/pull/44490)
- \[[`5d46c594a7`](https://github.com/nodejs/node/commit/5d46c594a7)] - **tools**: automate brotli update (Marco Ippolito) [#47205](https://github.com/nodejs/node/pull/47205)
- \[[`d324c15227`](https://github.com/nodejs/node/commit/d324c15227)] - **tools**: fix typo in nghttp2 path (Marco Ippolito) [#47330](https://github.com/nodejs/node/pull/47330)
- \[[`b1bcdceef5`](https://github.com/nodejs/node/commit/b1bcdceef5)] - **tools**: add scorecard workflow (Mateo Nunez) [#47254](https://github.com/nodejs/node/pull/47254)
- \[[`281362bcc2`](https://github.com/nodejs/node/commit/281362bcc2)] - **tools**: pin actions by hash for auto-start-ci.yml (Gabriela Gutierrez) [#46820](https://github.com/nodejs/node/pull/46820)
- \[[`6cae1bd377`](https://github.com/nodejs/node/commit/6cae1bd377)] - **tools**: standardize base64 update (Marco Ippolito) [#47201](https://github.com/nodejs/node/pull/47201)
- \[[`a682bd0714`](https://github.com/nodejs/node/commit/a682bd0714)] - **tools**: update codecov branch (Rich Trott) [#47285](https://github.com/nodejs/node/pull/47285)
- \[[`1061e17c66`](https://github.com/nodejs/node/commit/1061e17c66)] - **tools**: standardize update-llhttp.sh (Marco Ippolito) [#47198](https://github.com/nodejs/node/pull/47198)
- \[[`9781185943`](https://github.com/nodejs/node/commit/9781185943)] - **tools**: upgrade Windows digital signature to SHA256 (Tobias Nießen) [#47206](https://github.com/nodejs/node/pull/47206)
- \[[`37638e43c5`](https://github.com/nodejs/node/commit/37638e43c5)] - **tools**: add button to copy code example to clipboard (jakecastelli) [#46928](https://github.com/nodejs/node/pull/46928)
- \[[`05cb503f02`](https://github.com/nodejs/node/commit/05cb503f02)] - **tools**: standardize update-nghttp2.sh (Marco Ippolito) [#47197](https://github.com/nodejs/node/pull/47197)
- \[[`816e215701`](https://github.com/nodejs/node/commit/816e215701)] - **tools**: fix Slack notification action (Antoine du Hamel) [#47237](https://github.com/nodejs/node/pull/47237)
- \[[`9ac01ecc59`](https://github.com/nodejs/node/commit/9ac01ecc59)] - **tools**: notify on Slack when invalid commit lands (Antoine du Hamel) [#47178](https://github.com/nodejs/node/pull/47178)
- \[[`13eb029b4f`](https://github.com/nodejs/node/commit/13eb029b4f)] - **tools**: update daily wpt actions summary (Filip Skokan) [#47138](https://github.com/nodejs/node/pull/47138)
- \[[`e0a00ebfc5`](https://github.com/nodejs/node/commit/e0a00ebfc5)] - **tools**: allow test tap output to include unicode characters (Moshe Atlow) [#47175](https://github.com/nodejs/node/pull/47175)
- \[[`fca3391d0b`](https://github.com/nodejs/node/commit/fca3391d0b)] - **tools**: update lint-md-dependencies to rollup\@3.19.1 (Node.js GitHub Bot) [#47045](https://github.com/nodejs/node/pull/47045)
- \[[`c0fd6a3721`](https://github.com/nodejs/node/commit/c0fd6a3721)] - **tools**: update eslint to 8.36.0 (Node.js GitHub Bot) [#47046](https://github.com/nodejs/node/pull/47046)
- \[[`7d971daf29`](https://github.com/nodejs/node/commit/7d971daf29)] - **tools,meta**: update README and tools to reflect changes in TSC charter (Rich Trott) [#47126](https://github.com/nodejs/node/pull/47126)
- \[[`d078d66bdc`](https://github.com/nodejs/node/commit/d078d66bdc)] - **typings**: fix syntax error in tsconfig (Mohammed Keyvanzadeh) [#47584](https://github.com/nodejs/node/pull/47584)
- \[[`889730512c`](https://github.com/nodejs/node/commit/889730512c)] - **url**: handle URL.canParse without base parameter (Yagiz Nizipli) [#47547](https://github.com/nodejs/node/pull/47547)
- \[[`0dc485eb28`](https://github.com/nodejs/node/commit/0dc485eb28)] - **url**: drop ICU requirement for parsing hostnames (Yagiz Nizipli) [#47339](https://github.com/nodejs/node/pull/47339)
- \[[`b395b16c40`](https://github.com/nodejs/node/commit/b395b16c40)] - **url**: use ada::url_aggregator for parsing urls (Yagiz Nizipli) [#47339](https://github.com/nodejs/node/pull/47339)
- \[[`11f48e02a8`](https://github.com/nodejs/node/commit/11f48e02a8)] - **(SEMVER-MINOR)** **url**: implement URL.canParse (Matthew Aitken) [#47179](https://github.com/nodejs/node/pull/47179)
- \[[`977a8bad35`](https://github.com/nodejs/node/commit/977a8bad35)] - **url**: fix array overrun in node:url::SetArgs() (Yagiz Nizipli) [#47001](https://github.com/nodejs/node/pull/47001)
- \[[`4784e64850`](https://github.com/nodejs/node/commit/4784e64850)] - **url**: allow extension of user provided URL objects (Antoine du Hamel) [#46989](https://github.com/nodejs/node/pull/46989)
- \[[`f495cb6bf4`](https://github.com/nodejs/node/commit/f495cb6bf4)] - **url**: backport non-major changes from #46904 (Yagiz Nizipli) [#46904](https://github.com/nodejs/node/pull/46904)
- \[[`aa4f485388`](https://github.com/nodejs/node/commit/aa4f485388)] - **url**: set `formatUrl` method as no side effect (Yagiz Nizipli) [#46884](https://github.com/nodejs/node/pull/46884)
- \[[`c79e1b72f2`](https://github.com/nodejs/node/commit/c79e1b72f2)] - **url**: offload `URLSearchParams` initialization (Yagiz Nizipli) [#46867](https://github.com/nodejs/node/pull/46867)
- \[[`3db235b822`](https://github.com/nodejs/node/commit/3db235b822)] - **url**: remove unused `kFormat` from url (Yagiz Nizipli) [#46867](https://github.com/nodejs/node/pull/46867)
- \[[`b9df1a9668`](https://github.com/nodejs/node/commit/b9df1a9668)] - **url**: clean vertical alignment of docs (Robin Ury) [#48037](https://github.com/nodejs/node/pull/48037)
- \[[`9a2354d4a9`](https://github.com/nodejs/node/commit/9a2354d4a9)] - **url**: do not use object as hashmap (Timothy Gu) [#47415](https://github.com/nodejs/node/pull/47415)
- \[[`4850ba4bd4`](https://github.com/nodejs/node/commit/4850ba4bd4)] - **url**: improve URLSearchParams creation performance (Yagiz Nizipli) [#47190](https://github.com/nodejs/node/pull/47190)
- \[[`7fb1980fd9`](https://github.com/nodejs/node/commit/7fb1980fd9)] - **url**: add pending-deprecation to `url.parse()` (Yagiz Nizipli) [#47203](https://github.com/nodejs/node/pull/47203)
- \[[`b04ea5aa9b`](https://github.com/nodejs/node/commit/b04ea5aa9b)] - **url**: allow extension of user provided URL objects (Antoine du Hamel) [#46989](https://github.com/nodejs/node/pull/46989)
- \[[`972c851918`](https://github.com/nodejs/node/commit/972c851918)] - **url**: remove unnecessary call to `FunctionPrototypeBind` (Antoine du Hamel) [#46870](https://github.com/nodejs/node/pull/46870)
- \[[`87ef1b2859`](https://github.com/nodejs/node/commit/87ef1b2859)] - **util**: fix inspecting error with a throwing getter for `cause` (Antoine du Hamel) [#47163](https://github.com/nodejs/node/pull/47163)
- \[[`4729d30c1e`](https://github.com/nodejs/node/commit/4729d30c1e)] - **v8**: fix ERR_NOT_BUILDING_SNAPSHOT is not a constructor (Chengzhong Wu) [#47721](https://github.com/nodejs/node/pull/47721)
- \[[`d9a68b821e`](https://github.com/nodejs/node/commit/d9a68b821e)] - **vm**: properly handle defining symbol props (Nicolas DUBIEN) [#47572](https://github.com/nodejs/node/pull/47572)
- \[[`0d0fad8f0a`](https://github.com/nodejs/node/commit/0d0fad8f0a)] - **vm**: fix crash when setting \_\_proto\_\_ on context's globalThis (Feng Yu) [#47939](https://github.com/nodejs/node/pull/47939)
- \[[`fb90b6b3fb`](https://github.com/nodejs/node/commit/fb90b6b3fb)] - **vm**: properly handle defining props on any value (Nicolas DUBIEN) [#46615](https://github.com/nodejs/node/pull/46615)
- \[[`4b2aa3d27c`](https://github.com/nodejs/node/commit/4b2aa3d27c)] - **vm,lib**: refactor microtaskQueue assignment logic (Khaidi Chu) [#47765](https://github.com/nodejs/node/pull/47765)
- \[[`58afcc27f6`](https://github.com/nodejs/node/commit/58afcc27f6)] - **(SEMVER-MINOR)** **wasi**: no longer require flag to enable wasi (Michael Dawson) [#47286](https://github.com/nodejs/node/pull/47286)
- \[[`407af51cf5`](https://github.com/nodejs/node/commit/407af51cf5)] - **wasi**: add wasi sock_accept stub (Michael Dawson) [#46434](https://github.com/nodejs/node/pull/46434)
- \[[`d3e0229948`](https://github.com/nodejs/node/commit/d3e0229948)] - **watch**: fix watch path with equals (Moshe Atlow) [#47369](https://github.com/nodejs/node/pull/47369)
- \[[`78972d4696`](https://github.com/nodejs/node/commit/78972d4696)] - **worker**: support more cases when (de)serializing errors (Moshe Atlow) [#47925](https://github.com/nodejs/node/pull/47925)

Windows 32-bit Installer: https://nodejs.org/dist/v18.17.0/node-v18.17.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v18.17.0/node-v18.17.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v18.17.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v18.17.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v18.17.0/node-v18.17.0.pkg \
macOS Apple Silicon 64-bit Binary: https://nodejs.org/dist/v18.17.0/node-v18.17.0-darwin-arm64.tar.gz \
macOS Intel 64-bit Binary: https://nodejs.org/dist/v18.17.0/node-v18.17.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v18.17.0/node-v18.17.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v18.17.0/node-v18.17.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v18.17.0/node-v18.17.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v18.17.0/node-v18.17.0-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v18.17.0/node-v18.17.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v18.17.0/node-v18.17.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v18.17.0/node-v18.17.0.tar.gz \
Other release files: https://nodejs.org/dist/v18.17.0/ \
Documentation: https://nodejs.org/docs/v18.17.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

b61952f8468fe50b24b46039ad795af4bbea3751ded7cb488ec52e5a332f91ce  node-v18.17.0-aix-ppc64.tar.gz
19731ef427e77ad9c5f476eb62bfb02a7f179d3012feed0bbded62e45f23e679  node-v18.17.0-darwin-arm64.tar.gz
621cf884c4c27ddc595ff23f35ccd6fa1e827470581c30d31d779ba3cd6a162e  node-v18.17.0-darwin-arm64.tar.xz
2f381442381f7fbde2ca644c3275bec9c9c2a8d361f467b40e39428acdd6ccff  node-v18.17.0-darwin-x64.tar.gz
774734231d484d72e14f3327db6d7915abd3e2164ba577dd78affa5eade48a11  node-v18.17.0-darwin-x64.tar.xz
7c47c07521f8be7693f3b0436d2ef0cb5a490d070d1d9a3606decc55c39c41c4  node-v18.17.0-headers.tar.gz
1a7a3bbb7299f69e16a8ee2b327dd1c4811a9376bcafe41f8310467a9a9e3307  node-v18.17.0-headers.tar.xz
9d586f9d8b73a121b8a5438079c7106379aaae868f945a9f1755565607607944  node-v18.17.0-linux-arm64.tar.gz
fbd2904178ee47da6e0386bc9704a12b1f613da6ad194878a517d4a69ba56544  node-v18.17.0-linux-arm64.tar.xz
a9c3a56b3a407eca1fef7c688e6e7774cc492c9a5ae74f0baff2d8fe9b7b74c3  node-v18.17.0-linux-armv7l.tar.gz
a2f12295b2da1db23d6f48c5bfb5969e7bf26f663ca8256e1c246ca0ee44a71f  node-v18.17.0-linux-armv7l.tar.xz
6da314c8ef1b6239290d9237ba14bd44cfd4382a6a59696a5f506c6c8dd353ad  node-v18.17.0-linux-ppc64le.tar.gz
9fd9c215be8e54dcf8bbc42ba9e1342b05136be4bc12594ee40a29cedfaeaffd  node-v18.17.0-linux-ppc64le.tar.xz
c39f8386ee916915949651cad3a735f988ec825c60fe591c0791b4883749bc8b  node-v18.17.0-linux-s390x.tar.gz
876ca54c246d24e346d0c740fbb72c9fb7353369127f20492bc923ee6d0121db  node-v18.17.0-linux-s390x.tar.xz
5c4a7fd9262c0c47bafab3442de6c3fed1602be3d243cb8cf11309a201955e75  node-v18.17.0-linux-x64.tar.gz
f36facda28c4d5ce76b3a1b4344e688d29d9254943a47f2f1909b1a10acb1959  node-v18.17.0-linux-x64.tar.xz
a4710e7da19464707582216557edcc5a15e29fca4a80599f89484cd867a66a73  node-v18.17.0.pkg
7aaa157c0219b5895a4bd51d9e9731e0d0eb0ab9424fdbdcf30d01b3e5fb74e9  node-v18.17.0.tar.gz
80c0faadf5ea39c213ccb9aa5c2432977a0f1b5a0b766852abd0de06f2770406  node-v18.17.0.tar.xz
daf06430be6380cd7416f0e9060b99bea821ceb72dacd55242ad84a5440cf45b  node-v18.17.0-win-x64.7z
06e30b4e70b18d794651ef132c39080e5eaaa1187f938721d57edae2824f4e96  node-v18.17.0-win-x64.zip
d02327ddb3a6895a79b641fa4a456590bd7c8f0070a1915f2f76a70fd4ea7cc9  node-v18.17.0-win-x86.7z
6aef2e2ca6e0edf3cc85b5d8ae640caaab46e98da378c749d79a34fd78e0416e  node-v18.17.0-win-x86.zip
dabdc4e683f0d0a835952ea5df61139fa6b679e25121d1941a2ae77d29e989f4  node-v18.17.0-x64.msi
724c4258aa878fd6286a41dc62a1128a10a918b5b3028de8d276126f3d1c5b1d  node-v18.17.0-x86.msi
da74bcc1606026f05523c33e7882cc0ca4e0786a532bb80f932873eca9bdb431  win-x64/node.exe
fce36923d974ac8f9cea4bd68e56ab055797fd5b5ede5041b228d642a0d9bb40  win-x64/node.lib
221209e2d1defb5a59900e6a05e1c4b3d5bebca80048df4649436346a7944e59  win-x64/node_pdb.7z
792a2f132f5753a21c241629956ac4b0251baafbbf1afb0fcd96cf6c672727a8  win-x64/node_pdb.zip
da188adf811f97ba7da777dca949456936cc2ce1a540175f5c9e4f64169bddaf  win-x86/node.exe
7982f49aac6ff5b4cfaa236793f62a8c18b0bc9f1f2903617b9fdafee377bb2b  win-x86/node.lib
38c62a693d89e4b38863268f7286fc3fab8d5ce24bd4b7b3ed5a151b9f1b53b7  win-x86/node_pdb.7z
2da36082fae828d4faf45226775a7bee90f3d6e7e92cbf7a69c89b287a762f89  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEdPEmArbxxOkT+qN606iWE2Q7YgEFAmS26kQACgkQ06iWE2Q7
YgGjxRAAnK6iw0ZQI6CvfuJgJsaV7bj3Y8nj/CvMaj7o2CzgMLpY4r8aj5Mlt60U
K2exJOY+h3Z6CQExZITp+k2v8j07R+xiCEYt9K6UkZwxbCI4UJNePGPjphHwq6/R
dQk9i6GRd1lhhSYxqEqRSSptY9vFKfEczJgyUqWQGtNCX1hW15osvK6B/tZN4/bs
sYnTEImOkoFjz3GRj8PEMv42tF7aRUhC2E8Xsl0jZlt/n9GWe7LX3f2OJfczafJo
Q7bj670pUCHxpHxvjHsg/lWDuunWDvNIZSMFvR71VI3PIe45s2WoRO/jDNxJ9wyt
CBlfb08LEOnBHxV8au1GZQqvRaLedD51j9N7IwvQb3DDdq7T3msuIDzlZiByj5aZ
E2wGiVYetevuSogOf49CO99MexciDcCdwRSY79cg5Ar92Zl3xzrcErmXWsJd72p9
AAdTZQKtTKih0k3YKtYBe0c4gMm8U8bfQ12RiVQPUJMnwYbOIZ3l/6omuKN458Hm
poxXU/aUN6KCNR/5UfGivKr8EVjjO8EvHj/txHCmiEz/YhM7dbHFBQdfCY/EWKap
IYUAiR4jIWhGYFS3dbwxhCZHTIPfIK3DtqvQBtU07wa1WSAkKKSMCgBHAKOlDwap
ZCVKLyezajjko28SugXGjegEjcY4o7v23XghhW6RAbEB6R8TZDo=
=Y5D8
-----END PGP SIGNATURE-----

```
