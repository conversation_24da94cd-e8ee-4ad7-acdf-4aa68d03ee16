---
date: '2021-09-07T10:08:25.175Z'
category: release
title: Node v16.9.0 (Current)
layout: blog-post
author: <PERSON><PERSON><PERSON>
---

### Notable Changes

#### Corepack

Node.js now includes Corepack, a script that acts as a bridge between Node.js projects and the package managers they are intended to be used with during development.
In practical terms, **Corepack will let you use Yarn and pnpm without having to install them** - just like what currently happens with npm, which is shipped in Node.js by default.
Please head over to the [Corepack documentation page](https://nodejs.org/dist/latest-v16.x/docs/api/corepack.html) for more information on how to use it.

Contributed by <PERSON><PERSON> - [#39608](https://github.com/nodejs/node/pull/39608)

#### V8 9.3

V8 is updated to version [9.3](https://v8.dev/blog/v8-release-93), which includes performance improvements and new JavaScript features.

#### [`Object.hasOwn`](https://v8.dev/features/object-has-own)

`Object.hasOwn` is a static alias for `Object.prototype.hasOwnProperty.call`:

```js
Object.hasOwn({ value: 42 }, 'value'); // Returns `true`.
```

#### [Error cause](https://v8.dev/features/error-cause)

Errors can now be optionally constructed with a `cause` option, pointing to another error.
This adds a `cause` property on the new error:

```js
const error1 = new Error('Error one');
const error2 = new Error('Error two', { cause: error1 });
// error2.cause === error1
```

Contributed by Michaël Zasso - [#39947](https://github.com/nodejs/node/pull/39947)

#### Other Notable Changes

- [[`34c627e4bc`](https://github.com/nodejs/node/commit/34c627e4bc)] - **(SEMVER-MINOR)** **crypto**: add RSA-PSS params to asymmetricKeyDetails (Tobias Nießen) [#39851](https://github.com/nodejs/node/pull/39851)
- [[`20da0a5379`](https://github.com/nodejs/node/commit/20da0a5379)] - **(SEMVER-MINOR)** **module**: support pattern trailers (Guy Bedford) [#39635](https://github.com/nodejs/node/pull/39635)
- [[`cb44781371`](https://github.com/nodejs/node/commit/cb44781371)] - **(SEMVER-MINOR)** **stream**: add stream.compose (Robert Nagy) [#39029](https://github.com/nodejs/node/pull/39029)

### Commits

- [[`2343c394fb`](https://github.com/nodejs/node/commit/2343c394fb)] - **async_hooks**: use resource stack for AsyncLocalStorage run (Stephen Belanger) [#39890](https://github.com/nodejs/node/pull/39890)
- [[`00951827cd`](https://github.com/nodejs/node/commit/00951827cd)] - **_Revert_** "**build**: add windows-2022 to GitHub test matrix" (Michaël Zasso) [#39982](https://github.com/nodejs/node/pull/39982)
- [[`e7834535b3`](https://github.com/nodejs/node/commit/e7834535b3)] - **build**: add windows-2022 to GitHub test matrix (Michaël Zasso) [#39857](https://github.com/nodejs/node/pull/39857)
- [[`c49b0c0dd4`](https://github.com/nodejs/node/commit/c49b0c0dd4)] - **build**: add support for Visual Studio 2022 (Michaël Zasso) [#39857](https://github.com/nodejs/node/pull/39857)
- [[`afdb665e57`](https://github.com/nodejs/node/commit/afdb665e57)] - **build**: fix find-inactive-collaborators workflow token (Rich Trott) [#39909](https://github.com/nodejs/node/pull/39909)
- [[`0ff88f362f`](https://github.com/nodejs/node/commit/0ff88f362f)] - **build**: update token used for pull requests (Rich Trott) [#39907](https://github.com/nodejs/node/pull/39907)
- [[`beca890330`](https://github.com/nodejs/node/commit/beca890330)] - **build**: adapt v8_pch.h to V8 9.3 (Michaël Zasso) [#39469](https://github.com/nodejs/node/pull/39469)
- [[`2170346aa3`](https://github.com/nodejs/node/commit/2170346aa3)] - **build**: reset embedder string to "-node.0" (Michaël Zasso) [#39947](https://github.com/nodejs/node/pull/39947)
- [[`d33ab968ab`](https://github.com/nodejs/node/commit/d33ab968ab)] - **cluster**: fix comment regarding child_process file (Yash Ladha) [#39308](https://github.com/nodejs/node/pull/39308)
- [[`585199497f`](https://github.com/nodejs/node/commit/585199497f)] - **crypto**: fix regression in RSA-PSS keygen (Tobias Nießen) [#39937](https://github.com/nodejs/node/pull/39937)
- [[`34c627e4bc`](https://github.com/nodejs/node/commit/34c627e4bc)] - **(SEMVER-MINOR)** **crypto**: add RSA-PSS params to asymmetricKeyDetails (Tobias Nießen) [#39851](https://github.com/nodejs/node/pull/39851)
- [[`1dd91582da`](https://github.com/nodejs/node/commit/1dd91582da)] - **crypto**: fix rsa-pss one-shot sign/verify error handling (Filip Skokan) [#39830](https://github.com/nodejs/node/pull/39830)
- [[`20cf47004e`](https://github.com/nodejs/node/commit/20cf47004e)] - **crypto**: fix JWK RSA-PSS SubtleCrypto.exportKey (Filip Skokan) [#39828](https://github.com/nodejs/node/pull/39828)
- [[`e25dc8e470`](https://github.com/nodejs/node/commit/e25dc8e470)] - **deps**: upgrade npm to 7.21.1 (npm team) [#39904](https://github.com/nodejs/node/pull/39904)
- [[`9270684837`](https://github.com/nodejs/node/commit/9270684837)] - **deps**: update archs files for OpenSSL-1.1.1l+quic (Richard Lau) [#39867](https://github.com/nodejs/node/pull/39867)
- [[`4b5bbec6cc`](https://github.com/nodejs/node/commit/4b5bbec6cc)] - **deps**: upgrade openssl sources to OpenSSL_1_1_1l+quic (Richard Lau) [#39867](https://github.com/nodejs/node/pull/39867)
- [[`71659fd4ba`](https://github.com/nodejs/node/commit/71659fd4ba)] - **(SEMVER-MINOR)** **deps**: add corepack (Maël Nison) [#39608](https://github.com/nodejs/node/pull/39608)
- [[`7470db0dfb`](https://github.com/nodejs/node/commit/7470db0dfb)] - **deps**: restore minimum ICU version to 68 (Michaël Zasso) [#39470](https://github.com/nodejs/node/pull/39470)
- [[`92d83d18d2`](https://github.com/nodejs/node/commit/92d83d18d2)] - **deps**: make V8 9.3 abi-compatible with 9.0 (Michaël Zasso) [#39947](https://github.com/nodejs/node/pull/39947)
- [[`0140face81`](https://github.com/nodejs/node/commit/0140face81)] - **deps**: V8: cherry-pick 00bb1a77c03e (Darshan Sen) [#39829](https://github.com/nodejs/node/pull/39829)
- [[`3e1053e755`](https://github.com/nodejs/node/commit/3e1053e755)] - **deps**: V8: cherry-pick 81814ed44574 (Stephen Belanger) [#39719](https://github.com/nodejs/node/pull/39719)
- [[`d9d0104878`](https://github.com/nodejs/node/commit/d9d0104878)] - **deps**: silence irrelevant V8 warning (Michaël Zasso) [#38990](https://github.com/nodejs/node/pull/38990)
- [[`cd9b03ea40`](https://github.com/nodejs/node/commit/cd9b03ea40)] - **deps**: silence irrelevant V8 warnings (Michaël Zasso) [#37587](https://github.com/nodejs/node/pull/37587)
- [[`b83cab712f`](https://github.com/nodejs/node/commit/b83cab712f)] - **deps**: fix V8 build issue with inline methods (Jiawen Geng) [#35415](https://github.com/nodejs/node/pull/35415)
- [[`068824d754`](https://github.com/nodejs/node/commit/068824d754)] - **deps**: make v8.h compatible with VS2015 (Joao Reis) [#32116](https://github.com/nodejs/node/pull/32116)
- [[`54f4f1af50`](https://github.com/nodejs/node/commit/54f4f1af50)] - **deps**: V8: forward declaration of `Rtl*FunctionTable` (Refael Ackermann) [#32116](https://github.com/nodejs/node/pull/32116)
- [[`10ba1cb8b2`](https://github.com/nodejs/node/commit/10ba1cb8b2)] - **deps**: V8: patch register-arm64.h (Refael Ackermann) [#32116](https://github.com/nodejs/node/pull/32116)
- [[`3ce6f72124`](https://github.com/nodejs/node/commit/3ce6f72124)] - **deps**: V8: un-cherry-pick bd019bd (Refael Ackermann) [#32116](https://github.com/nodejs/node/pull/32116)
- [[`f43c292520`](https://github.com/nodejs/node/commit/f43c292520)] - **(SEMVER-MINOR)** **deps**: update V8 to 9.3.345.16 (Michaël Zasso) [#39947](https://github.com/nodejs/node/pull/39947)
- [[`9e782eb758`](https://github.com/nodejs/node/commit/9e782eb758)] - **doc**: remove {C,Dec}ompressionStream documentation (Luigi Pinca) [#39899](https://github.com/nodejs/node/pull/39899)
- [[`7857e9cc77`](https://github.com/nodejs/node/commit/7857e9cc77)] - **doc**: add descriptions about when `options.mode` is ignored (Ray) [#39881](https://github.com/nodejs/node/pull/39881)
- [[`d43b555047`](https://github.com/nodejs/node/commit/d43b555047)] - **doc**: remove danbev from TSC member list (Daniel Bevenius) [#39978](https://github.com/nodejs/node/pull/39978)
- [[`fc01dd916e`](https://github.com/nodejs/node/commit/fc01dd916e)] - **doc**: add missing changes to generateKeyPair(Sync) (Tobias Nießen) [#39963](https://github.com/nodejs/node/pull/39963)
- [[`953f2e9f88`](https://github.com/nodejs/node/commit/953f2e9f88)] - **doc**: add nodejs/tweet issue creation to sec. doc (Daniel Bevenius) [#39940](https://github.com/nodejs/node/pull/39940)
- [[`29c4b07716`](https://github.com/nodejs/node/commit/29c4b07716)] - **doc**: update WASI example to use import.meta.url (Guy Bedford) [#39925](https://github.com/nodejs/node/pull/39925)
- [[`9eb4a70c14`](https://github.com/nodejs/node/commit/9eb4a70c14)] - **doc**: move reference to OpenSSL flags SSL_OP\_\* (Tobias Nießen) [#39935](https://github.com/nodejs/node/pull/39935)
- [[`8ea4befc82`](https://github.com/nodejs/node/commit/8ea4befc82)] - **doc**: add docs for duplex.allowHalfOpen property (Tim Perry) [#39126](https://github.com/nodejs/node/pull/39126)
- [[`bc2b73ec9b`](https://github.com/nodejs/node/commit/bc2b73ec9b)] - **doc**: add FrankQiu to a triager (FrankQiu) [#39922](https://github.com/nodejs/node/pull/39922)
- [[`8b68f8ec38`](https://github.com/nodejs/node/commit/8b68f8ec38)] - **doc**: add VoltrexMaster to triagers (voltrexmaster) [#39920](https://github.com/nodejs/node/pull/39920)
- [[`3a8f77ac0d`](https://github.com/nodejs/node/commit/3a8f77ac0d)] - **doc**: document JavaScript tool for benchmark comparison (Michaël Zasso) [#39835](https://github.com/nodejs/node/pull/39835)
- [[`4ac703ca8e`](https://github.com/nodejs/node/commit/4ac703ca8e)] - **doc**: add Mesteery to triagers (Mestery) [#39887](https://github.com/nodejs/node/pull/39887)
- [[`d059a5186b`](https://github.com/nodejs/node/commit/d059a5186b)] - **doc**: update maintaining openssl guide (Richard Lau) [#39878](https://github.com/nodejs/node/pull/39878)
- [[`486150580c`](https://github.com/nodejs/node/commit/486150580c)] - **doc**: move ERR_WORKER_UNSPPORTED_EXTENSION to legacy (Qingyu Deng) [#39788](https://github.com/nodejs/node/pull/39788)
- [[`a4b8c13798`](https://github.com/nodejs/node/commit/a4b8c13798)] - **events**: protect property defs against prototype polution (James M Snell) [#39773](https://github.com/nodejs/node/pull/39773)
- [[`cfbe9065ae`](https://github.com/nodejs/node/commit/cfbe9065ae)] - **events**: add brand checks for detached accessors (James M Snell) [#39773](https://github.com/nodejs/node/pull/39773)
- [[`112af69194`](https://github.com/nodejs/node/commit/112af69194)] - **fs**: add docs and tests for `AsyncIterable` support in `fh.writeFile` (Antoine du Hamel) [#39836](https://github.com/nodejs/node/pull/39836)
- [[`402071bc45`](https://github.com/nodejs/node/commit/402071bc45)] - **meta**: remove duplicate AUTHORS entry for NigelKibodeaux (Rich Trott) [#39967](https://github.com/nodejs/node/pull/39967)
- [[`3588f07603`](https://github.com/nodejs/node/commit/3588f07603)] - **meta**: add mailmap entry for Ethan-Arrowood (Rich Trott) [#39930](https://github.com/nodejs/node/pull/39930)
- [[`259e0cf4d7`](https://github.com/nodejs/node/commit/259e0cf4d7)] - **meta**: add mailmap entry for branisha (Rich Trott) [#39889](https://github.com/nodejs/node/pull/39889)
- [[`bc236a6714`](https://github.com/nodejs/node/commit/bc236a6714)] - **meta**: update .mailmap to remove duplicate AUTHORS entry for addaleax (Rich Trott) [#39880](https://github.com/nodejs/node/pull/39880)
- [[`20da0a5379`](https://github.com/nodejs/node/commit/20da0a5379)] - **(SEMVER-MINOR)** **module**: support pattern trailers (Guy Bedford) [#39635](https://github.com/nodejs/node/pull/39635)
- [[`879dc4658e`](https://github.com/nodejs/node/commit/879dc4658e)] - **src**: remove usage of AllocatedBuffer from src/node_buffer.cc (Darshan Sen) [#39941](https://github.com/nodejs/node/pull/39941)
- [[`79ce096470`](https://github.com/nodejs/node/commit/79ce096470)] - **src**: remove extra semicolons outside fns (Shelley Vohr) [#39800](https://github.com/nodejs/node/pull/39800)
- [[`cb44781371`](https://github.com/nodejs/node/commit/cb44781371)] - **(SEMVER-MINOR)** **stream**: add stream.compose (Robert Nagy) [#39029](https://github.com/nodejs/node/pull/39029)
- [[`ca9b781d20`](https://github.com/nodejs/node/commit/ca9b781d20)] - **test**: use `assert.match` instead of `regexp.test` (Michaël Zasso) [#39928](https://github.com/nodejs/node/pull/39928)
- [[`007e2855af`](https://github.com/nodejs/node/commit/007e2855af)] - **test**: use error code mapping in place of raw errno (Darshan Sen) [#38675](https://github.com/nodejs/node/pull/38675)
- [[`00529b0ef2`](https://github.com/nodejs/node/commit/00529b0ef2)] - **test**: add test to verify other extension can be loaded by worker (Qingyu Deng) [#39788](https://github.com/nodejs/node/pull/39788)
- [[`4e7212c88c`](https://github.com/nodejs/node/commit/4e7212c88c)] - **test**: update error message keywords (leeight) [#39826](https://github.com/nodejs/node/pull/39826)
- [[`549d717722`](https://github.com/nodejs/node/commit/549d717722)] - **test**: adapt test-fs-read to V8 9.3 (Michaël Zasso) [#39469](https://github.com/nodejs/node/pull/39469)
- [[`644b25e068`](https://github.com/nodejs/node/commit/644b25e068)] - **test**: adapt test-util-inspect to V8 9.3 (Michaël Zasso) [#39469](https://github.com/nodejs/node/pull/39469)
- [[`105bff9ea5`](https://github.com/nodejs/node/commit/105bff9ea5)] - **test**: adapt test-v8-flags to V8 9.3 (Michaël Zasso) [#39469](https://github.com/nodejs/node/pull/39469)
- [[`d92bd9a982`](https://github.com/nodejs/node/commit/d92bd9a982)] - **tools**: add support for import assertions in linter (Antoine du Hamel) [#39924](https://github.com/nodejs/node/pull/39924)
- [[`9763561e30`](https://github.com/nodejs/node/commit/9763561e30)] - **tools**: update gyp-next to v0.10.0 (Michaël Zasso) [#39857](https://github.com/nodejs/node/pull/39857)
- [[`9f105c73fc`](https://github.com/nodejs/node/commit/9f105c73fc)] - **tools**: update V8 gypfiles for 9.3 (Michaël Zasso) [#39469](https://github.com/nodejs/node/pull/39469)
- [[`16271d2f50`](https://github.com/nodejs/node/commit/16271d2f50)] - **worker**: remove file extension check (Qingyu Deng) [#39788](https://github.com/nodejs/node/pull/39788)
- [[`3b1ce93e03`](https://github.com/nodejs/node/commit/3b1ce93e03)] - **worker**: add brand checks for detached MessageEvent accessors (James M Snell) [#39773](https://github.com/nodejs/node/pull/39773)

Windows 32-bit Installer: https://nodejs.org/dist/v16.9.0/node-v16.9.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v16.9.0/node-v16.9.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v16.9.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v16.9.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v16.9.0/node-v16.9.0.pkg \
macOS Apple Silicon 64-bit Binary: https://nodejs.org/dist/v16.9.0/node-v16.9.0-darwin-arm64.tar.gz \
macOS Intel 64-bit Binary: https://nodejs.org/dist/v16.9.0/node-v16.9.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v16.9.0/node-v16.9.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v16.9.0/node-v16.9.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v16.9.0/node-v16.9.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v16.9.0/node-v16.9.0-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v16.9.0/node-v16.9.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v16.9.0/node-v16.9.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v16.9.0/node-v16.9.0.tar.gz \
Other release files: https://nodejs.org/dist/v16.9.0/ \
Documentation: https://nodejs.org/docs/v16.9.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

d4c91b7321877945403162bb330aa5c30323773aece0bbc65fefb1efd2be8a53  node-v16.9.0-aix-ppc64.tar.gz
13105eb6623f474e4596c725bd4d6fcd500c68155f9f954bd3332c46f5df7378  node-v16.9.0-darwin-arm64.tar.gz
4fc309ad4e663aae2ded6cfca008513bceae9f108637fce3cee89c43b839423f  node-v16.9.0-darwin-arm64.tar.xz
37cea8ce6f88c501ed2ed191fc4335e5c4ecbeb0e85247c07b35825f07a60351  node-v16.9.0-darwin-x64.tar.gz
3a4e899c6195c3579d377b3f9fbb27d018ee74211731009b458fd9ccd11b46a7  node-v16.9.0-darwin-x64.tar.xz
a1f38a6e3800bded2fb89d9cb3b3f1ebea33bf4469c725880b58dc692179ee0e  node-v16.9.0-headers.tar.gz
7ac337077ddb1fbe3c2a0a9dad8c8a307bb48cd604ebd50037bc0fdc34bd3d4c  node-v16.9.0-headers.tar.xz
a5e838ab842f75a9e20275b13cdb67a8a9a10a6feec9b59097903c1a3e16351a  node-v16.9.0-linux-arm64.tar.gz
6f5c9f6c95e9b80630d9e280379dab3d442dc4109e324bf9c310d438c0c735c4  node-v16.9.0-linux-arm64.tar.xz
505525dd2e59c6718ee3b49b72c291685ee26e3630d309d116829d77d285629a  node-v16.9.0-linux-armv7l.tar.gz
b68a726c56af8cfdc0d98bf13c0f9dfc5011aba7bc803eadc0f0afa6f4221928  node-v16.9.0-linux-armv7l.tar.xz
7316b5c9d7c6c2fc7ca3648792500dfacd8eea68f5f7d4b50b965c49779483c9  node-v16.9.0-linux-ppc64le.tar.gz
85684fb0aaefc367cff1bc8810f9d39db425bc3958b75a5f2e49784f333fd2ec  node-v16.9.0-linux-ppc64le.tar.xz
716b5fea0ab13e6f5bb395cbc8a381739eaf57ba7a0c2d4cddd2c2a09216f7c3  node-v16.9.0-linux-s390x.tar.gz
744612d24fbf6bcecc56caf511bde659b2e5eafac8d17e50c0529d934a0723b7  node-v16.9.0-linux-s390x.tar.xz
f7389d3bc9efdf5ce95ff52ea880efcbf2c9de662ef7b143aedf141aeb74ab95  node-v16.9.0-linux-x64.tar.gz
891ecae3826e6dd9b7ed6f484383bcb0803edcb3dc5956e42f6e64d2c3cc269b  node-v16.9.0-linux-x64.tar.xz
9dfeaa736129def9eb6520fc4a140649b2f7c09211b21b1bda47c26ce006e6ed  node-v16.9.0.pkg
d04e044c498438e0f0de4729529c64345a3db97e412fe59604a6ce4970019c86  node-v16.9.0.tar.gz
1ae764200dfc23a8a6a68387e3ef6c7eb38705effdb38722c9d3916feb8b666f  node-v16.9.0.tar.xz
f526cc79568193b5ea66ce8297dbdf48dbd1601005e48952e83fb84fba415687  node-v16.9.0-win-x64.7z
c08c1dff1d43f149a3161f84881a73e9dd3c1772138645ce198b8126e641e313  node-v16.9.0-win-x64.zip
b2b6c83958321784f55cbfe179d8f2907c2d75547f18324a02b9e690cfd69612  node-v16.9.0-win-x86.7z
47230270ffda66b80382f0a12fb43806aac41c813661b6a23b52fa0b02669c5f  node-v16.9.0-win-x86.zip
72601300585a6cf6cc8ef86374452158b9e48e619e4810d0b06c432b01377c2f  node-v16.9.0-x64.msi
d81d721f97054adfb31b4daaa84f7e2b097be3c0a95a5711af3f2f67fc5767cc  node-v16.9.0-x86.msi
42cb10f2733ff5ed80bdf1f595b47de761c06dd3f1ef49d178b189b08aa8518e  win-x64/node.exe
774524a2b7c2806a8141a430ce9c6bffb9e8f78ce6bc095ec8fa26a333ff7cef  win-x64/node.lib
c22ef2f25123893cac924e3b8cd84a51ae3fcdde8c6f2d7e96c4a1ffc7ad6ed9  win-x64/node_pdb.7z
6bb557600e7e276a03f01b078f14b24f1ada73956df26d814d669e04012f4246  win-x64/node_pdb.zip
26e19ece9c55243066c2a11de00f6476f94e566a7cd7e0d790067dae7e17fb53  win-x86/node.exe
1827162a03517e9a4897bd0195e601216d4ffb3680b11db4599166d0e76b6e98  win-x86/node.lib
1871bbc5ecf783970ea36a2e7dd8899b9d76b1c599d494ea7be2979bf6b3ecbc  win-x86/node_pdb.7z
c6fd5e3b3d37b15dc49acfead04b240350d5c548a9e4ff28eb1bf6a48c620f5f  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEj8yhP+8dDC6RAI4Jdw96mlrhVgAFAmE3OX4ACgkQdw96mlrh
VgCNRRAAi4qxY30gwmCg9fmYAHfa/b0Kd8bjB7vGmgd1p+jxFeazBXv6Vk/cSg+M
p2i1ZZHUTapelBKXeJYcGA8dcdw/20ELwcY8W5v8tKr2LOCViu/2QRgW3S4+I17z
Uclk1amOhaexrXj9/nP0qCEG0cHx22Nr4GQO4T8qbLIH4/nZKWtoXxPBACJgOLZj
XryhHWwOjxJXF3saQxlaOfC1LYcWdihJR62fqsSRnTTvDq8VYiRMKsUstqrnMFMP
uWu+ZnF9PqM5r11zKv7ecV5Tlx26UBb6UCFlnSBYaEdM8srG8CnAFT6Mx8lGPuPq
9MAAbd94UU8zxn6XROCas/2ii0IB9xnwYbOVibs06sK2KcEtlPMoa+3Ur9LDqS3Q
0sDTN8yffEe5AF7JHDAGNzDRbKfMk38Tra0gG/k+6OGlp21M5TwXXpDQWwzrNwfy
+kotMKHEkwWzL4hiKbCMXjntUXiBjvGD706kX6r8/iIOKCj8U0qn44z8v9VOwWSd
rV3fmJhwV66Zz53gRMIMtorIR8hXmeMtLuv6h2j1+vnHAT9j3lOFaY0bZ2kaVK1Q
05yYPwBZG7B/Ebi8WK05VJNjnJXNBwOouFwHy+jGsDigNKE0jMD0ZvYyrIT2NnS1
sYJsLTjRHv/1Ww9bGIbp8VkZhGO9ONrp+RWKvEGtc9rgOlQykhw=
=cmND
-----END PGP SIGNATURE-----

```
