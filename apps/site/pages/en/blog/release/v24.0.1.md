---
date: '2025-05-08T20:05:05.636Z'
category: release
title: Node v24.0.1 (Current)
layout: blog-post
author: <PERSON>
---

## 2025-05-08, Version 24.0.1 (Current), @aduh95

### Notable Changes

- \[[`2e1d9581e0`](https://github.com/nodejs/node/commit/2e1d9581e0)] - _**Revert**_ "**buffer**: move SlowBuffer to EOL" (<PERSON><PERSON>) [#58211](https://github.com/nodejs/node/pull/58211)

### Commits

- \[[`d38e811c5b`](https://github.com/nodejs/node/commit/d38e811c5b)] - **benchmark**: fix typo in method name for error-stack (<PERSON>) [#58128](https://github.com/nodejs/node/pull/58128)
- \[[`2e1d9581e0`](https://github.com/nodejs/node/commit/2e1d9581e0)] - _**Revert**_ "**buffer**: move <PERSON>B<PERSON>er to EOL" (<PERSON><PERSON>) [#58211](https://github.com/nodejs/node/pull/58211)
- \[[`a883b0c979`](https://github.com/nodejs/node/commit/a883b0c979)] - **build**: use //third_party/simdutf by default in GN (Shelley Vohr) [#58115](https://github.com/nodejs/node/pull/58115)
- \[[`3d84b5c7a4`](https://github.com/nodejs/node/commit/3d84b5c7a4)] - **doc**: add HBSPS as triager (Wiyeong Seo) [#57980](https://github.com/nodejs/node/pull/57980)
- \[[`1e57cb686e`](https://github.com/nodejs/node/commit/1e57cb686e)] - **doc**: add history entries to `--input-type` section (Antoine du Hamel) [#58175](https://github.com/nodejs/node/pull/58175)
- \[[`0b54f06b6f`](https://github.com/nodejs/node/commit/0b54f06b6f)] - **doc**: add ambassaor message (Brian Muenzenmeyer) [#57600](https://github.com/nodejs/node/pull/57600)
- \[[`46bee52d57`](https://github.com/nodejs/node/commit/46bee52d57)] - **doc**: fix misaligned options in vm.compileFunction() (Jimmy Leung) [#58145](https://github.com/nodejs/node/pull/58145)
- \[[`e732a8bfdd`](https://github.com/nodejs/node/commit/e732a8bfdd)] - **doc**: fix typo in benchmark script path (Miguel Marcondes Filho) [#58129](https://github.com/nodejs/node/pull/58129)
- \[[`d49ff34adb`](https://github.com/nodejs/node/commit/d49ff34adb)] - **doc**: add missing options.signal to readlinePromises.createInterface() (Jimmy Leung) [#55456](https://github.com/nodejs/node/pull/55456)
- \[[`bc9f5a2e79`](https://github.com/nodejs/node/commit/bc9f5a2e79)] - **doc**: fix typo of file `zlib.md` (yusheng chen) [#58093](https://github.com/nodejs/node/pull/58093)
- \[[`c8e8558958`](https://github.com/nodejs/node/commit/c8e8558958)] - **doc**: clarify future Corepack removal in v25+ (Trivikram Kamat) [#57825](https://github.com/nodejs/node/pull/57825)
- \[[`b13d5d14bd`](https://github.com/nodejs/node/commit/b13d5d14bd)] - **meta**: bump actions/setup-node from 4.3.0 to 4.4.0 (dependabot\[bot]) [#58111](https://github.com/nodejs/node/pull/58111)
- \[[`0ebb17a300`](https://github.com/nodejs/node/commit/0ebb17a300)] - **meta**: bump actions/setup-python from 5.5.0 to 5.6.0 (dependabot\[bot]) [#58107](https://github.com/nodejs/node/pull/58107)
- \[[`5946785bf4`](https://github.com/nodejs/node/commit/5946785bf4)] - **tools**: exclude deps/v8/tools from CodeQL scans (Rich Trott) [#58132](https://github.com/nodejs/node/pull/58132)
- \[[`0708497c7f`](https://github.com/nodejs/node/commit/0708497c7f)] - **tools**: bump the eslint group in /tools/eslint with 6 updates (dependabot\[bot]) [#58105](https://github.com/nodejs/node/pull/58105)

Windows 64-bit Installer: https://nodejs.org/dist/v24.0.1/node-v24.0.1-x64.msi \
Windows ARM 64-bit Installer: https://nodejs.org/dist/v24.0.1/node-v24.0.1-arm64.msi \
Windows 64-bit Binary: https://nodejs.org/dist/v24.0.1/win-x64/node.exe \
Windows ARM 64-bit Binary: https://nodejs.org/dist/v24.0.1/win-arm64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v24.0.1/node-v24.0.1.pkg \
macOS Apple Silicon 64-bit Binary: https://nodejs.org/dist/v24.0.1/node-v24.0.1-darwin-arm64.tar.gz \
macOS Intel 64-bit Binary: https://nodejs.org/dist/v24.0.1/node-v24.0.1-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v24.0.1/node-v24.0.1-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v24.0.1/node-v24.0.1-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v24.0.1/node-v24.0.1-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v24.0.1/node-v24.0.1-aix-ppc64.tar.gz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v24.0.1/node-v24.0.1-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v24.0.1/node-v24.0.1.tar.gz \
Other release files: https://nodejs.org/dist/v24.0.1/ \
Documentation: https://nodejs.org/docs/v24.0.1/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

264d0432fe5820e74e66a0f94fb9f51959728149985746021a9aae57ebe46761  node-v24.0.1-aix-ppc64.tar.gz
e3c89f37088167a5eb656c6e1d912f06fbc446103f632b18b3149e2baf0b7ec2  node-v24.0.1-arm64.msi
f38faa6ea209aa31f56e5d82ee021ba5b8f149137734d81a31fda65c327f0be4  node-v24.0.1-darwin-arm64.tar.gz
60644415ed97d17a74250b182ae0b634f649db401f36c79c6aace60d0913dd80  node-v24.0.1-darwin-arm64.tar.xz
f0be72400208ff369b1dd4e2b6c0aa09713ad42e8cd99e7abf2a9b1575e2fef5  node-v24.0.1-darwin-x64.tar.gz
b81734d1f82097274f95f1986fcc72c42a7c52a0343eaef0fdc7ffc5e0d9b08f  node-v24.0.1-darwin-x64.tar.xz
2205f0b4749dcd2feab66293953c9f6dbab3c509a7771e7821762ebba5ec1235  node-v24.0.1-headers.tar.gz
1891351c25607f4b8770b36174a6092d4bb8acf0a030bc4b05fddfd1d7c8992f  node-v24.0.1-headers.tar.xz
45a5ffeff5eae91e64f19575072f0241ff71da49c0cdf69fad360323c587a5bd  node-v24.0.1-linux-arm64.tar.gz
9e61592d31480c6835c56d1d605bda0fbfd7311fb4e0e69a9441343ca9b3feb5  node-v24.0.1-linux-arm64.tar.xz
66d74261b98d0aec8bec6b980e0f49c5b95543e4b2d365394cf96c7294b9d5a6  node-v24.0.1-linux-ppc64le.tar.gz
96866845a35098c36ecd391c9e210c59ea482332a2b2cf9933438d91efd3e4e7  node-v24.0.1-linux-ppc64le.tar.xz
a65aac6ec6324c0b81a68bf8a701cb2cb8654be1422866908b42dba680c9368d  node-v24.0.1-linux-s390x.tar.gz
1db1bb96fceb0d94105e3c0ebed6001f44bdc70a82d73b92fbc7e5d91e8dd6b3  node-v24.0.1-linux-s390x.tar.xz
58239e217440acffb3889954f1dc0977f46048c54f226e446280fc8feb8c5ab9  node-v24.0.1-linux-x64.tar.gz
12d8b7c7dd9191bd4f3afe872c7d4908ac75d2a6ef06d2ae59c0b4aa384bc875  node-v24.0.1-linux-x64.tar.xz
ddb291b75560600b4ad763b0d1f556a913805ffe301006b5cb637eb728b4e62a  node-v24.0.1-win-arm64.7z
64df0c762fd866687f88b059cb4a1da3a2ad50a80f9666a505412868edf61948  node-v24.0.1-win-arm64.zip
88f541641996127887052fe059a96e7e83ae7f0e19aabf04fdd60c9e0da4f7ad  node-v24.0.1-win-x64.7z
8bbbc4860ce03f8a6cc7369039f6497be3157c4bde3ad54651cc22f5f3af0b40  node-v24.0.1-win-x64.zip
d2896e3630c14fea0c9f8dd4bc23a6e4974f292cc242714612d4f8a53896f8c1  node-v24.0.1-x64.msi
e1580db46b085b3ebfc7f87489f7340e3ab4d26bb58be516be2a19e92bea73fa  node-v24.0.1.pkg
e02f2ad3fe56f74674657f2e76019c99f74a7172b4e5e02db0687e60c4c99508  node-v24.0.1.tar.gz
70271026971808409a7ed6444360d5fe3ef4146c1ca53f2ca290c60d214be84e  node-v24.0.1.tar.xz
7a4ca50fa4556c55d0bc6136e278d43f2d9809756906ea68bfe5ae5076ba4ce5  win-arm64/node.exe
6809fc156673d10cca7dff0e54f28309bb111d235c54493a4a0ca401c2113be7  win-arm64/node.lib
afec8ef9e518b93a8f3713693892e50c6984b0f599e5ca5849e191b4437ead9c  win-arm64/node_pdb.7z
d15a58efde7cc6d97c2cc56aaab603a6f24bbc6013614de4d14787d6b6daf625  win-arm64/node_pdb.zip
5303ddb655aa510d131bdfd07aeba0e598b03896effe2edc235b2a58369fc4a5  win-x64/node.exe
34882ca2bb450431047f2dd3bae1c3b8c1cd2b4cffd5c1a0bf079948846d2b83  win-x64/node.lib
e0cd0eae609960285f52f98c48a3216c6ec4aeb58b26e9eb316f1eeaeebda841  win-x64/node_pdb.7z
2e6aa8b476595ad1cf1b2b432b59d090e11af4b762af562156faeb4ba118ef2d  win-x64/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEwNYkhDnx1WBKr/tAIdkA/9sjN1YFAmgdDNsACgkQIdkA/9sj
N1Zs6w//R+vvhpQfYp9JYBvi8eNWciqx4236XlKZc3tWTdoYwAigsdoAQGmvAbRc
MdI2C7Rx9FJSkhRYa76rNo1j40Jw/2wNQjC/+DdI3yHthJl4nUKS/dxkZxw99M4k
0QQMHktq5gANu782AosVwtGYPbmf/1xz4BGoW+NTxqyoNeGFlHgxoQA6d1wvnCWI
NdFcZqzTWFg0ZazCkWVRs0YNS57eVstD+iyJTO8geq84TC1j7DJQjIx0HZI1jrmt
y6ZQP3zdov0aDq9qUJVfPxG9JA3nxLOaIL9tuDcJLJJ9AJBl30p+AD7PrvIfRUye
UqVlVMA675E7IRdep2M3Q0jNpSVy2VsrdPGna//BvK2yMJ6MM0qSpvrMc7RJPrYj
sigsThhkSitqmbRApqWJW+Mf37+jHplGg0k110kfGoI7F10aJJz1QlmOGdWAwyVU
j3xRuqannoRSFjKYmtlpcyowrAePV83n2D8N3bnwoBefHwCxnLZN87L+wB8luS0f
ovX3UNUAx+u/ZP26UOh4qYNVBWLeJXjR1GNcGFChhfc9721blvm5gCqvf/PjMmrx
1BB/ximegO3CRWzo4sMWyq2rLDHEPJMImcYbnP4J2J7MjfLA9iUYKLDfe/5duwWU
gfMRH/uqaxigBOMqsY0VOo4WopvOaT0vr5f3hGUkWSuytG9U8YU=
=5wpg
-----END PGP SIGNATURE-----
```
