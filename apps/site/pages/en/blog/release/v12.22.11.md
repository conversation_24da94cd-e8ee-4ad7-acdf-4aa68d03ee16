---
date: '2022-03-18T01:10:50.815Z'
category: release
title: Node v12.22.11 (LTS)
layout: blog-post
author: <PERSON>
---

### Notable changes

Update to OpenSSL 1.1.1n, which addresses the following vulnerability:

- Infinite loop in `BN_mod_sqrt()` reachable when parsing certificates (High)(CVE-2022-0778)
  More details are available at <https://www.openssl.org/news/secadv/20220315.txt>

Fix for building Node.js 12.x with Visual Studio 2019 to allow us to continue to
run CI tests.

### Commits

- \[[`e3e5bf11ba`](https://github.com/nodejs/node/commit/e3e5bf11ba)] - **build**: pin Windows GitHub runner to windows-2019 (<PERSON>) [#42349](https://github.com/nodejs/node/pull/42349)
- \[[`f41e7771bf`](https://github.com/nodejs/node/commit/f41e7771bf)] - **build**: fix detection of Visual Studio 2019 (<PERSON>) [#42349](https://github.com/nodejs/node/pull/42349)
- \[[`c372ec207d`](https://github.com/nodejs/node/commit/c372ec207d)] - **deps**: update archs files for OpenSSL-1.1.n (Richard Lau) [#42348](https://github.com/nodejs/node/pull/42348)
- \[[`d574a1dccb`](https://github.com/nodejs/node/commit/d574a1dccb)] - **deps**: upgrade openssl sources to 1.1.1n (Richard Lau) [#42348](https://github.com/nodejs/node/pull/42348)

Windows 32-bit Installer: https://nodejs.org/dist/v12.22.11/node-v12.22.11-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v12.22.11/node-v12.22.11-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v12.22.11/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v12.22.11/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v12.22.11/node-v12.22.11.pkg \
macOS Intel 64-bit Binary: https://nodejs.org/dist/v12.22.11/node-v12.22.11-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v12.22.11/node-v12.22.11-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v12.22.11/node-v12.22.11-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v12.22.11/node-v12.22.11-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v12.22.11/node-v12.22.11-aix-ppc64.tar.gz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v12.22.11/node-v12.22.11-sunos-x64.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v12.22.11/node-v12.22.11-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v12.22.11/node-v12.22.11-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v12.22.11/node-v12.22.11.tar.gz \
Other release files: https://nodejs.org/dist/v12.22.11/ \
Documentation: https://nodejs.org/docs/v12.22.11/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

66d54fbdc98ed93cc54524d8e515301a6954daabcfb1507c57c258e20095a12f  node-v12.22.11-aix-ppc64.tar.gz
858fd52204fa48799105019e73347dfd6b0371621b01beecfe2264f2d64d1b30  node-v12.22.11-darwin-x64.tar.gz
185b854f27db8a7fcce90569e0b1a0373bb58e148494f1cc4f29b26fd322caf3  node-v12.22.11-darwin-x64.tar.xz
6f1429dd6815cf53977c3767eba9a6557130a454b94187f65bfae2e109d676eb  node-v12.22.11-headers.tar.gz
2b909276bdc796517985813a060cbfe7441c2a59b10a89f82f1f66b6cc8f723f  node-v12.22.11-headers.tar.xz
6efd2770a6d73ef631d1b7a8aecf50361c5cf1858080dbc29e56c8ddf0a981af  node-v12.22.11-linux-arm64.tar.gz
86664fe446cb9c76c8135da2fce8b0ecece18339a4f9d84d167ad4b2046701f9  node-v12.22.11-linux-arm64.tar.xz
2b94245e9079e868a762523296089c5d193a9cd0942b12d3c42de0b22bec3d2e  node-v12.22.11-linux-armv7l.tar.gz
45538ad6872a91fa22d1ccd77e8e70c4536797de7109a7544c9bfa24be2928ac  node-v12.22.11-linux-armv7l.tar.xz
ddbdf6546bbba854f0715b875f803fbae9d96b528f2721a5819d61523535c33c  node-v12.22.11-linux-ppc64le.tar.gz
c2b1df30152abcb9caa569c5a38e0490efc274d4c12a5c845c9947a8c19b6401  node-v12.22.11-linux-ppc64le.tar.xz
c2aa9d69e02135e1f38362a456df27bab7efe0f504b1967ccb5c0f40bb6886ef  node-v12.22.11-linux-s390x.tar.gz
052a652d8f52d23eabd708db78c4156a806a99157e3b73d9502770c55aeee4ad  node-v12.22.11-linux-s390x.tar.xz
d98da55241ad2a1359e4785e24be0788a331782f5d14a2ba40284eb2153bbb7f  node-v12.22.11-linux-x64.tar.gz
53cde07f114fb13cd39dbb09e9bd8bb25e87f367e8ff0f3786422c6a58bac78d  node-v12.22.11-linux-x64.tar.xz
980f9a35b96ddc96615d7e2862fc5ea0d757d0e4f5fe516215ec1ecfa310ddc3  node-v12.22.11.pkg
e27506be7f7b3e24bac5c1225ccd269b221c72d8a9781c0281c88993d8a62584  node-v12.22.11-sunos-x64.tar.gz
ab1ed6f63fc5989225a6b152aa3f4f3140ed80fcf462f61b5f5790f2a7c82d7c  node-v12.22.11-sunos-x64.tar.xz
784785071df0c2f756a5dc5206a37f585ee1017898131a707183b8764269fefa  node-v12.22.11.tar.gz
5e81da26fd5b1f89714483abaa68e3da304523e4334c78c49bfa7a03ee78d6f1  node-v12.22.11.tar.xz
3cd8fbfe0f5159a684656686d35c13434d205c0dd543aba238d032ba4eb9129c  node-v12.22.11-win-x64.7z
26e083c0e1d694de449a67bd57d27b777e09529550cf5e4dc6a5967b8cfa70e6  node-v12.22.11-win-x64.zip
f7ed0c291c258c7cb2967f8d45aac3a7ed2ea38ec45cfe7078f600b65c16dc67  node-v12.22.11-win-x86.7z
d35db1f48eaf661b71f87d1ba96024cd884777fa89aecd16acf4a1455f1f5bd8  node-v12.22.11-win-x86.zip
690f5c2506d9f01c24163f588b1f27b5bfa0caed80002077d8754e84432278c4  node-v12.22.11-x64.msi
080e0f17d9c9bb4b6d04eadc54d2dbd50ebb820457aafdb8880df97a324bbde5  node-v12.22.11-x86.msi
fd5e2a06b2b3c0eca232b7e869673ee8a179c0e8aaf9d4c8260fd4fe1df7e386  win-x64/node.exe
28e5c24831deedbf4fb8a9560f2c4f95205479c589f54a9a53ec346f6a5cf8bf  win-x64/node.lib
a8fdc7bd722c8c439c0294716b5adb426dd685019a369082f78385cf036d44bd  win-x64/node_pdb.7z
140a606bd7a46c009ed1cbb500afd91a2bf0b7f1f8d76b4483d94efbc565f416  win-x64/node_pdb.zip
619cc59058ce0c89658f033557c837330b1daaefb3d8857397b4a44a224022cb  win-x86/node.exe
dad0e6bef1c45f4f43fbf84c33df6b910ace8122eff3f8d39d5ebecd25320ba4  win-x86/node.lib
e78d03b6d5cd83c61293b9da8b3e50ca68ec026b20572e14790ff92ec3779ee1  win-x86/node_pdb.7z
6c4249a78dc6a116f1d527bc9239904bd03d2e8fe4a2ffab52bb32e7d0afbe57  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEyC+jrhy+3Gvka5NgxDzsRcF6uTwFAmIzw5YACgkQxDzsRcF6
uTwyOBAAmYo/p2Q/UfTH0B+bUDh1+35cEyT2rHSUUQsQHaak8tvQfuFYZiCec6Wv
uXo9PKT5dmaaiC6AqUkvYK7JbtYa7DztRVmP0IaJFTn5kFLANQaaIElkHR7KFUne
u6MIjv9gKw6U349BGdbkXpOqsCCeODAxM/IHrFNpCW4Q6t+pzLxybM4EH5QigFPS
POyhnVG6kPUMy2oY9b+B0Y2QZRyEeEkxkdLWcNnpu+f1fpTHumNSNOebAqnWVJLE
WK9UAEVu5I7BO6Xz5QQPmz02eW8IPseshWji6HWv+K4JkAKOPmcYk8rIlH6qwT4t
o6nlPSSbNj96KFhNQEXUjB1WY1GSHtKSnDSjBihZKdEO4jd1rwZtj9puW0C+Dr7r
m7HKEGa1LXJpO8ITyfB8KCosyzbH7huiFd/DaKUJedu10vutcP0NIda6ffdHTRrU
8ei7d+6cMYwUvtTfw+3d+YRdeAvGbG6n4pM59PL5HvZ4asHdqMXOUIZnLqM7SWge
0G+fuaPD/OVRPs41vzXmE+fw+zNCbQToRUXEaOZFVT4rLn2vLidb1vhxRVFhwu3M
TKrZPEtKdJ0lPU9g5Ns45NjMKXC3oLoaUDneECYDgJYa+N9FpwiE5IB19qyBFZ8K
LNcuI2hzY8Iyji6dJka6pi351SzN7BfkVZJnAUUhSzxX8sqtR+4=
=4a/W
-----END PGP SIGNATURE-----

```
