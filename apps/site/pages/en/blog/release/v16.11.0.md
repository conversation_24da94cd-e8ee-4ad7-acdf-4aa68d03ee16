---
date: '2021-10-08T17:13:45.589Z'
category: release
title: Node v16.11.0 (Current)
layout: blog-post
author: <PERSON>
---

### Notable Changes

- **crypto**
  - update root certificates (<PERSON>) [#40280](https://github.com/nodejs/node/pull/40280)
- **deps**
  - upgrade npm to 8.0.0 (npm team) [#40369](https://github.com/nodejs/node/pull/40369)
  - update `nghttp2` to v1.45.1 (thunder-coding) [#40206](https://github.com/nodejs/node/pull/40206)
  - update V8 to 9.4.146.19 (<PERSON><PERSON><PERSON>) [#40285](https://github.com/nodejs/node/pull/40285)
- **tools**
  - update certdata.txt (<PERSON>) [#40280](https://github.com/nodejs/node/pull/40280)

### Commits

- [[`34f3021ca3`](https://github.com/nodejs/node/commit/34f3021ca3)] - **benchmark**: add `util.toUSVString()`'s benchmark (<PERSON><PERSON><PERSON>) [#40203](https://github.com/nodejs/node/pull/40203)
- [[`f83b9bcb6f`](https://github.com/nodejs/node/commit/f83b9bcb6f)] - **build**: support Python 3.10.0 (FrankQiu) [#40296](https://github.com/nodejs/node/pull/40296)
- [[`3148f9b64e`](https://github.com/nodejs/node/commit/3148f9b64e)] - **build**: check for duplicates in new AUTHORS entries (Rich Trott) [#40264](https://github.com/nodejs/node/pull/40264)
- [[`48c162d457`](https://github.com/nodejs/node/commit/48c162d457)] - **build**: set DESTCPU correctly for 'make binary' on Apple Silicon (Chris Heisterkamp) [#40147](https://github.com/nodejs/node/pull/40147)
- [[`7fbfb66d41`](https://github.com/nodejs/node/commit/7fbfb66d41)] - **build**: limit update authors CI scope (Jiawen Geng) [#40219](https://github.com/nodejs/node/pull/40219)
- [[`a1bee94502`](https://github.com/nodejs/node/commit/a1bee94502)] - **build**: pass a tuple of alternatives to str.endswith() (Christian Clauss) [#40017](https://github.com/nodejs/node/pull/40017)
- [[`eaf9d08332`](https://github.com/nodejs/node/commit/eaf9d08332)] - **build**: add --no-user for pip commands in Makefile (Rich Trott) [#40169](https://github.com/nodejs/node/pull/40169)
- [[`e22ca06ac4`](https://github.com/nodejs/node/commit/e22ca06ac4)] - **build**: fix "test-internet.yml" workflows (SURYAPRATAP SINGH SURYAVANSHI) [#40177](https://github.com/nodejs/node/pull/40177)
- [[`4da73d09bf`](https://github.com/nodejs/node/commit/4da73d09bf)] - **(SEMVER-MINOR)** **build**: reset embedder string to "-node.0" (Michaël Zasso) [#40285](https://github.com/nodejs/node/pull/40285)
- [[`4b117fbc81`](https://github.com/nodejs/node/commit/4b117fbc81)] - **console**: use validators for consistency (Voltrex) [#39812](https://github.com/nodejs/node/pull/39812)
- [[`6489423187`](https://github.com/nodejs/node/commit/6489423187)] - **console**: avoid unnecessary variables (Pancake) [#40183](https://github.com/nodejs/node/pull/40183)
- [[`9af2592e69`](https://github.com/nodejs/node/commit/9af2592e69)] - **crypto**: update root certificates (Richard Lau) [#40280](https://github.com/nodejs/node/pull/40280)
- [[`2fa5e5011f`](https://github.com/nodejs/node/commit/2fa5e5011f)] - **crypto**: handle initEDRaw pkey failure (Shelley Vohr) [#40188](https://github.com/nodejs/node/pull/40188)
- [[`7968c79301`](https://github.com/nodejs/node/commit/7968c79301)] - **crypto**: don't call callback twice in case crypto.randomBytes fails (Guilherme Bernal) [#40157](https://github.com/nodejs/node/pull/40157)
- [[`b89c7ae297`](https://github.com/nodejs/node/commit/b89c7ae297)] - **deps**: upgrade npm to 8.0.0 (npm team) [#40369](https://github.com/nodejs/node/pull/40369)
- [[`947f3dc9af`](https://github.com/nodejs/node/commit/947f3dc9af)] - **deps**: V8: patch jinja2 for Python 3.10 compat (Michaël Zasso) [#40296](https://github.com/nodejs/node/pull/40296)
- [[`685c7d43a5`](https://github.com/nodejs/node/commit/685c7d43a5)] - **(SEMVER-MINOR)** **deps**: update `nghttp2` to v1.45.1 (thunder-coding) [#40206](https://github.com/nodejs/node/pull/40206)
- [[`e7046e0ff1`](https://github.com/nodejs/node/commit/e7046e0ff1)] - **deps**: restore minimum ICU version to 68 (Michaël Zasso) [#39470](https://github.com/nodejs/node/pull/39470)
- [[`a3db2033d4`](https://github.com/nodejs/node/commit/a3db2033d4)] - **(SEMVER-MINOR)** **deps**: make V8 9.4 abi-compatible with 9.0 (Michaël Zasso) [#40285](https://github.com/nodejs/node/pull/40285)
- [[`5cc24e6d76`](https://github.com/nodejs/node/commit/5cc24e6d76)] - **deps**: V8: cherry-pick 9a607043cb31 (Jiawen Geng) [#40046](https://github.com/nodejs/node/pull/40046)
- [[`8de5eb88d3`](https://github.com/nodejs/node/commit/8de5eb88d3)] - **deps**: V8: cherry-pick 5681a6565828 (Michaël Zasso) [#39945](https://github.com/nodejs/node/pull/39945)
- [[`150d816edb`](https://github.com/nodejs/node/commit/150d816edb)] - **deps**: V8: cherry-pick bdcda72cd1d8 (Michaël Zasso) [#39945](https://github.com/nodejs/node/pull/39945)
- [[`807b68b430`](https://github.com/nodejs/node/commit/807b68b430)] - **deps**: V8: cherry-pick 00bb1a77c03e (Darshan Sen) [#39829](https://github.com/nodejs/node/pull/39829)
- [[`be016948df`](https://github.com/nodejs/node/commit/be016948df)] - **deps**: silence irrelevant V8 warning (Michaël Zasso) [#38990](https://github.com/nodejs/node/pull/38990)
- [[`22dcd3e4dc`](https://github.com/nodejs/node/commit/22dcd3e4dc)] - **deps**: silence irrelevant V8 warnings (Michaël Zasso) [#37587](https://github.com/nodejs/node/pull/37587)
- [[`1aea6a771b`](https://github.com/nodejs/node/commit/1aea6a771b)] - **deps**: fix V8 build issue with inline methods (Jiawen Geng) [#40060](https://github.com/nodejs/node/pull/40060)
- [[`e9812157f0`](https://github.com/nodejs/node/commit/e9812157f0)] - **deps**: make v8.h compatible with VS2015 (Joao Reis) [#32116](https://github.com/nodejs/node/pull/32116)
- [[`88ae710057`](https://github.com/nodejs/node/commit/88ae710057)] - **deps**: V8: forward declaration of `Rtl*FunctionTable` (Refael Ackermann) [#32116](https://github.com/nodejs/node/pull/32116)
- [[`e810f0766f`](https://github.com/nodejs/node/commit/e810f0766f)] - **deps**: V8: patch register-arm64.h (Refael Ackermann) [#32116](https://github.com/nodejs/node/pull/32116)
- [[`b8aabd5622`](https://github.com/nodejs/node/commit/b8aabd5622)] - **deps**: V8: un-cherry-pick bd019bd (Refael Ackermann) [#32116](https://github.com/nodejs/node/pull/32116)
- [[`309c4f05df`](https://github.com/nodejs/node/commit/309c4f05df)] - **(SEMVER-MINOR)** **deps**: update V8 to 9.4.146.19 (Michaël Zasso) [#40285](https://github.com/nodejs/node/pull/40285)
- [[`69eaaf6321`](https://github.com/nodejs/node/commit/69eaaf6321)] - **doc**: format general markdown files (Rich Trott) [#40322](https://github.com/nodejs/node/pull/40322)
- [[`dc9c31985c`](https://github.com/nodejs/node/commit/dc9c31985c)] - **doc**: fix the inline code-block at the NodeDhKeyGenParams class (Justin) [#40341](https://github.com/nodejs/node/pull/40341)
- [[`8d0546db39`](https://github.com/nodejs/node/commit/8d0546db39)] - **doc**: correct the codeblock for `hmacImportParams.hash` (Justin) [#40340](https://github.com/nodejs/node/pull/40340)
- [[`1db2ffd008`](https://github.com/nodejs/node/commit/1db2ffd008)] - **doc**: fix typo in stream docs (Juan José Arboleda) [#40337](https://github.com/nodejs/node/pull/40337)
- [[`abfcbcd14c`](https://github.com/nodejs/node/commit/abfcbcd14c)] - **doc**: update fast-track approval comment request (voltrexmaster) [#40316](https://github.com/nodejs/node/pull/40316)
- [[`e2cd2f44f2`](https://github.com/nodejs/node/commit/e2cd2f44f2)] - **doc**: fix CVE-2021-22940 references (Michaël Zasso) [#40308](https://github.com/nodejs/node/pull/40308)
- [[`88bdbf1e29`](https://github.com/nodejs/node/commit/88bdbf1e29)] - **doc**: format markdown files in test directory (Rich Trott) [#40290](https://github.com/nodejs/node/pull/40290)
- [[`f71ac57a86`](https://github.com/nodejs/node/commit/f71ac57a86)] - **doc**: add triagers to the table of contents (FrankQiu) [#39969](https://github.com/nodejs/node/pull/39969)
- [[`a5218b5313`](https://github.com/nodejs/node/commit/a5218b5313)] - **doc**: update Forrest Norvell's pronouns (Forrest L Norvell) [#40292](https://github.com/nodejs/node/pull/40292)
- [[`d2e54e5d0c`](https://github.com/nodejs/node/commit/d2e54e5d0c)] - **doc**: reorder stream 'readable' paragraphs (Vincent Weevers) [#40212](https://github.com/nodejs/node/pull/40212)
- [[`1d0a3e1a0c`](https://github.com/nodejs/node/commit/1d0a3e1a0c)] - **doc**: fix typo in fs (Brian White) [#40257](https://github.com/nodejs/node/pull/40257)
- [[`66edb7bfe1`](https://github.com/nodejs/node/commit/66edb7bfe1)] - **doc**: fix typo in fs.md (Arslan Ali) [#40254](https://github.com/nodejs/node/pull/40254)
- [[`614a7c21f8`](https://github.com/nodejs/node/commit/614a7c21f8)] - **doc**: fix typo in packages.md (Arslan Ali) [#40230](https://github.com/nodejs/node/pull/40230)
- [[`9fa6dfbe76`](https://github.com/nodejs/node/commit/9fa6dfbe76)] - **doc**: fix example of crypto.generateKeySync (Gary Ho) [#40225](https://github.com/nodejs/node/pull/40225)
- [[`9a2b94a142`](https://github.com/nodejs/node/commit/9a2b94a142)] - **doc**: update fs.watchFile doc (Clément Nardi) [#40134](https://github.com/nodejs/node/pull/40134)
- [[`a68f91c884`](https://github.com/nodejs/node/commit/a68f91c884)] - **doc**: add version when diagnostics_channel APIs were added (Gerhard Stöbich) [#40208](https://github.com/nodejs/node/pull/40208)
- [[`6bf67909ad`](https://github.com/nodejs/node/commit/6bf67909ad)] - **doc**: fix typo in 'maxHeaderSize' (Rebhi Alfa) [#40164](https://github.com/nodejs/node/pull/40164)
- [[`73a127ba7b`](https://github.com/nodejs/node/commit/73a127ba7b)] - **doc**: fix buffer api example code's token error (m3m0ry) [#40125](https://github.com/nodejs/node/pull/40125)
- [[`59db8293f4`](https://github.com/nodejs/node/commit/59db8293f4)] - **doc**: fix typo in `async_hooks.md` (xuchaobei) [#40187](https://github.com/nodejs/node/pull/40187)
- [[`779dfd199b`](https://github.com/nodejs/node/commit/779dfd199b)] - **doc**: make version picker usable on mobile (Evan Lucas) [#39958](https://github.com/nodejs/node/pull/39958)
- [[`7bd62f4809`](https://github.com/nodejs/node/commit/7bd62f4809)] - **doc**: fix typos in http.md (Luigi Pinca) [#40161](https://github.com/nodejs/node/pull/40161)
- [[`94b415b980`](https://github.com/nodejs/node/commit/94b415b980)] - **doc**: add blank line between comments (Rich Trott) [#40160](https://github.com/nodejs/node/pull/40160)
- [[`847b451d88`](https://github.com/nodejs/node/commit/847b451d88)] - **doc**: update markdown files in src for upcoming linting/formatting (Rich Trott) [#40159](https://github.com/nodejs/node/pull/40159)
- [[`cea7395858`](https://github.com/nodejs/node/commit/cea7395858)] - **doc**: update benchmarks README.md for upcoming linting/formatting (Rich Trott) [#40158](https://github.com/nodejs/node/pull/40158)
- [[`c231745837`](https://github.com/nodejs/node/commit/c231745837)] - **doc**: prepare markdown file for upcoming formatting/linting (Rich Trott) [#40156](https://github.com/nodejs/node/pull/40156)
- [[`7e58cda6e0`](https://github.com/nodejs/node/commit/7e58cda6e0)] - **doc**: update tools .md files for upcoming lint/formatting (Rich Trott) [#40155](https://github.com/nodejs/node/pull/40155)
- [[`02a87b096c`](https://github.com/nodejs/node/commit/02a87b096c)] - **doc**: update markdown formatting for \*.md files (Rich Trott) [#40154](https://github.com/nodejs/node/pull/40154)
- [[`9b0e61a67f`](https://github.com/nodejs/node/commit/9b0e61a67f)] - **doc,src**: update crypto/README.md (Tobias Nießen) [#40332](https://github.com/nodejs/node/pull/40332)
- [[`88e7bd073a`](https://github.com/nodejs/node/commit/88e7bd073a)] - **events**: allow dispatch many times without listener (MrBBot) [#39772](https://github.com/nodejs/node/pull/39772)
- [[`c7f3294d02`](https://github.com/nodejs/node/commit/c7f3294d02)] - **(SEMVER-MINOR)** **fs**: add stream utilities to `FileHandle` (Antoine du Hamel) [#40009](https://github.com/nodejs/node/pull/40009)
- [[`555af5b808`](https://github.com/nodejs/node/commit/555af5b808)] - **http**: remove 'data' and 'end' listener if client parser error (Matteo Collina) [#40244](https://github.com/nodejs/node/pull/40244)
- [[`22725f5bdd`](https://github.com/nodejs/node/commit/22725f5bdd)] - **http**: use 0 as default for requests limit (Artur K) [#40192](https://github.com/nodejs/node/pull/40192)
- [[`3d5eba8042`](https://github.com/nodejs/node/commit/3d5eba8042)] - **lib**: refactor to avoid unsafe array iteration (Antoine du Hamel) [#40271](https://github.com/nodejs/node/pull/40271)
- [[`547fc86371`](https://github.com/nodejs/node/commit/547fc86371)] - **lib**: use `validateArray` (Voltrex) [#39774](https://github.com/nodejs/node/pull/39774)
- [[`a37527ce8f`](https://github.com/nodejs/node/commit/a37527ce8f)] - **meta**: add mailmap entry for ratracegrad (Rich Trott) [#40291](https://github.com/nodejs/node/pull/40291)
- [[`a75a8f2ca0`](https://github.com/nodejs/node/commit/a75a8f2ca0)] - **meta**: update AUTHORS (Node.js GitHub Bot) [#40289](https://github.com/nodejs/node/pull/40289)
- [[`66ab278bae`](https://github.com/nodejs/node/commit/66ab278bae)] - **meta**: add .mailmap entry for Jimbly (Rich Trott) [#40267](https://github.com/nodejs/node/pull/40267)
- [[`e040f2cf0d`](https://github.com/nodejs/node/commit/e040f2cf0d)] - **meta**: add .mailmap entry for daguej (Rich Trott) [#40223](https://github.com/nodejs/node/pull/40223)
- [[`d64740fbb3`](https://github.com/nodejs/node/commit/d64740fbb3)] - **meta**: update AUTHORS (Node.js GitHub Bot) [#40217](https://github.com/nodejs/node/pull/40217)
- [[`9ee9e41f5c`](https://github.com/nodejs/node/commit/9ee9e41f5c)] - **meta**: move one or more collaborators to emeritus (Node.js GitHub Bot) [#40115](https://github.com/nodejs/node/pull/40115)
- [[`da6c82b425`](https://github.com/nodejs/node/commit/da6c82b425)] - **meta**: update gdams contact information (Rich Trott) [#40233](https://github.com/nodejs/node/pull/40233)
- [[`a660017915`](https://github.com/nodejs/node/commit/a660017915)] - **meta**: add .mailmap entry for kunalspathak (Rich Trott) [#40202](https://github.com/nodejs/node/pull/40202)
- [[`4d46bde22e`](https://github.com/nodejs/node/commit/4d46bde22e)] - **meta**: add mailmap entry for ralphtheninja (Rich Trott) [#40153](https://github.com/nodejs/node/pull/40153)
- [[`b856886d00`](https://github.com/nodejs/node/commit/b856886d00)] - **meta**: update mailmap for LakshmiSwethaG (Rich Trott) [#40172](https://github.com/nodejs/node/pull/40172)
- [[`972d921855`](https://github.com/nodejs/node/commit/972d921855)] - **module**: fix ERR_REQUIRE_ESM for parentPath null (Guy Bedford) [#40145](https://github.com/nodejs/node/pull/40145)
- [[`344c03b2e6`](https://github.com/nodejs/node/commit/344c03b2e6)] - **repl**: skip EmptyStatements and return result with TLA (Mestery) [#40194](https://github.com/nodejs/node/pull/40194)
- [[`b694b0ca52`](https://github.com/nodejs/node/commit/b694b0ca52)] - **src**: use `As()` instead of `Cast()` for conversions (Darshan Sen) [#40287](https://github.com/nodejs/node/pull/40287)
- [[`383dbe940d`](https://github.com/nodejs/node/commit/383dbe940d)] - **src**: implement changes suggested by @addaleax (kokke) [#40128](https://github.com/nodejs/node/pull/40128)
- [[`a6112dd1de`](https://github.com/nodejs/node/commit/a6112dd1de)] - **src**: fix time-of-use vs time-of-check "bugs" (kokke) [#40128](https://github.com/nodejs/node/pull/40128)
- [[`bbf1ed7c78`](https://github.com/nodejs/node/commit/bbf1ed7c78)] - **src**: remove AllocatedBuffer from crypto_common.cc (Darshan Sen) [#40213](https://github.com/nodejs/node/pull/40213)
- [[`528f9228fd`](https://github.com/nodejs/node/commit/528f9228fd)] - **src**: remove usage of AllocatedBuffer from udp_wrap.cc (Darshan Sen) [#40151](https://github.com/nodejs/node/pull/40151)
- [[`d36127d862`](https://github.com/nodejs/node/commit/d36127d862)] - **src**: move `ToUSVString()` to node_util.cc (Khaidi Chu) [#40204](https://github.com/nodejs/node/pull/40204)
- [[`bddf8c28d9`](https://github.com/nodejs/node/commit/bddf8c28d9)] - **src,crypto**: eliminate code duplication between StatelessDiffieHellman\* (Darshan Sen) [#40084](https://github.com/nodejs/node/pull/40084)
- [[`6a8689f1f9`](https://github.com/nodejs/node/commit/6a8689f1f9)] - **test**: fix typo in test/common/index.js (Tobias Nießen) [#40297](https://github.com/nodejs/node/pull/40297)
- [[`dc0c2744cf`](https://github.com/nodejs/node/commit/dc0c2744cf)] - **test**: suppress compiler warning in test_bigint (Daniel Bevenius) [#40253](https://github.com/nodejs/node/pull/40253)
- [[`18820bfa58`](https://github.com/nodejs/node/commit/18820bfa58)] - **tools**: patch jinja2 for Python 3.10 compat (Michaël Zasso) [#40296](https://github.com/nodejs/node/pull/40296)
- [[`8d7710e6c3`](https://github.com/nodejs/node/commit/8d7710e6c3)] - **tools**: update rollup entry in lint-md package.json (FrankQiu) [#40281](https://github.com/nodejs/node/pull/40281)
- [[`7bb4dc2406`](https://github.com/nodejs/node/commit/7bb4dc2406)] - **tools**: update certdata.txt (Richard Lau) [#40280](https://github.com/nodejs/node/pull/40280)
- [[`f31b0c9700`](https://github.com/nodejs/node/commit/f31b0c9700)] - **tools**: update remark-preset-lint-node to 3.2.0 (Rich Trott) [#40278](https://github.com/nodejs/node/pull/40278)
- [[`9c4e7a5158`](https://github.com/nodejs/node/commit/9c4e7a5158)] - **tools**: fix lint-md autolinking (Rich Trott) [#40181](https://github.com/nodejs/node/pull/40181)
- [[`26db6db87f`](https://github.com/nodejs/node/commit/26db6db87f)] - **tools**: implement markdown formatting (Rich Trott) [#40181](https://github.com/nodejs/node/pull/40181)
- [[`67812e8c65`](https://github.com/nodejs/node/commit/67812e8c65)] - **tools**: re-implement lint-md without unified-args (Rich Trott) [#40180](https://github.com/nodejs/node/pull/40180)
- [[`0232f94175`](https://github.com/nodejs/node/commit/0232f94175)] - **tools**: update remark-preset-lint-node to 3.1.0 (Rich Trott) [#40166](https://github.com/nodejs/node/pull/40166)
- [[`80fdedd184`](https://github.com/nodejs/node/commit/80fdedd184)] - **tools**: fix find-inactive-collaborators for recent README change (Rich Trott) [#40163](https://github.com/nodejs/node/pull/40163)
- [[`ebf17102d1`](https://github.com/nodejs/node/commit/ebf17102d1)] - **tools**: extend default yamllint config (Michaël Zasso) [#40150](https://github.com/nodejs/node/pull/40150)
- [[`f7c82749a7`](https://github.com/nodejs/node/commit/f7c82749a7)] - **tools**: update V8 gypfiles for 9.4 (Michaël Zasso) [#39945](https://github.com/nodejs/node/pull/39945)
- [[`dd39422b8b`](https://github.com/nodejs/node/commit/dd39422b8b)] - **typings**: define types for symbols binding (Michaël Zasso) [#40143](https://github.com/nodejs/node/pull/40143)
- [[`ced8467e20`](https://github.com/nodejs/node/commit/ced8467e20)] - **typings**: define types for worker and messaging bindings (Michaël Zasso) [#40143](https://github.com/nodejs/node/pull/40143)
- [[`66d3101677`](https://github.com/nodejs/node/commit/66d3101677)] - **(SEMVER-MINOR)** **util**: improve ansi escape code regex (Colin Ihrig) [#40214](https://github.com/nodejs/node/pull/40214)
- [[`f4164fa4c3`](https://github.com/nodejs/node/commit/f4164fa4c3)] - **(SEMVER-MINOR)** **util**: expose stripVTControlCharacters() (Colin Ihrig) [#40214](https://github.com/nodejs/node/pull/40214)

Windows 32-bit Installer: https://nodejs.org/dist/v16.11.0/node-v16.11.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v16.11.0/node-v16.11.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v16.11.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v16.11.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v16.11.0/node-v16.11.0.pkg \
macOS Apple Silicon 64-bit Binary: https://nodejs.org/dist/v16.11.0/node-v16.11.0-darwin-arm64.tar.gz \
macOS Intel 64-bit Binary: https://nodejs.org/dist/v16.11.0/node-v16.11.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v16.11.0/node-v16.11.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v16.11.0/node-v16.11.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v16.11.0/node-v16.11.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v16.11.0/node-v16.11.0-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v16.11.0/node-v16.11.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v16.11.0/node-v16.11.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v16.11.0/node-v16.11.0.tar.gz \
Other release files: https://nodejs.org/dist/v16.11.0/ \
Documentation: https://nodejs.org/docs/v16.11.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

c83419e179b82019daea529c1da187e91b857184adfbf315e267490a47aad957  node-v16.11.0-aix-ppc64.tar.gz
b8f75887d6e94f8f38df9b6016258ea742677ffb45e6d513d55dc09bcd9c1fd3  node-v16.11.0-darwin-arm64.tar.gz
3fc491462cf9cb4332137578ca19f4c356dd285a07f72350b1bc36d915072454  node-v16.11.0-darwin-arm64.tar.xz
abcf083d1c5f83c6d12fbe0f0ff2b3ff61fc0d3e06b43ebbbd0761804c62c468  node-v16.11.0-darwin-x64.tar.gz
cf8a5db0b04f12996142b7f0beca07fc194e3d5b86ef2abae0ea9b974dc9f3bf  node-v16.11.0-darwin-x64.tar.xz
d4c2eaf9e3571146cbfb59728539e7ae559363c3ce903b235d0a5d6fdc5cab5d  node-v16.11.0-headers.tar.gz
88cfc11248dbe21c5f3491651824114215ddc2e22cba7b5df1cd17996c5235a0  node-v16.11.0-headers.tar.xz
c923289edd9b251dd37bd6bb53c4fbf0476ae91d55b8703aeb95b0da39642c45  node-v16.11.0-linux-arm64.tar.gz
3149673ce793d117e0e7392025af32540d5a0d27db4aedb71b88b0b6ffd5ece9  node-v16.11.0-linux-arm64.tar.xz
944501148597e695e508ed2d2988253cc87c0e6012d2215daf2dd2267b5ea0ed  node-v16.11.0-linux-armv7l.tar.gz
0cb620756d8add8da5b0735f6b1eaaf7624d5126c9cf13f2a300060a008ce235  node-v16.11.0-linux-armv7l.tar.xz
484f1a6a44415841d23c2c743b2a2e6816f211eaab3a1f2c9fe2f90215573662  node-v16.11.0-linux-ppc64le.tar.gz
60fd05f49f5975335497875432e6d19c2a7eb62a42a668404bb322583960345e  node-v16.11.0-linux-ppc64le.tar.xz
30a1e216b6a802d4cc81b24c4ee6f02c4750e4e0a6f45f33cefacb8717c4ea54  node-v16.11.0-linux-s390x.tar.gz
bcdbda2b1cccad13f13ce539cdc0cf6c1caf1be283d2ab13b557c5ed0f9f5297  node-v16.11.0-linux-s390x.tar.xz
bfc84faaa07864398edbe8bfb9d7d0e64fa20649b8c498cd299e0ff44657d9a3  node-v16.11.0-linux-x64.tar.gz
29cf360ef3dea364f01d9ada9917b5e49f0beaf9927fbdb57fcefb800966e5c6  node-v16.11.0-linux-x64.tar.xz
77cc4d58976b7af93f1d58bc4669bab42774297288f812a9ef18602f12d4ec98  node-v16.11.0.pkg
a070b0f57cf0851d959d6d8caa4e3fc24a34998ccf1f4ad68abfaa6103fbb6fd  node-v16.11.0.tar.gz
d3f631bd0d215ded26b49b2eae42c84de2ba1b46f00cc2930809900a0f7165ae  node-v16.11.0.tar.xz
ef2c8049e2a6e34b72fa7f2b2618e5e5f91c1a5f4c816ee54cf462dc31b9b584  node-v16.11.0-win-x64.7z
44343d779b6a72c6bfd708f8927aa54014b223530568509cd0e294b0d6660e83  node-v16.11.0-win-x64.zip
b6869590c9709a739d01ac08543ee901598357a75d51674872190d463dc44ba9  node-v16.11.0-win-x86.7z
59afe55ed8ac256794d27f4db53a594019be8260ed7663b8d216af88e90756e2  node-v16.11.0-win-x86.zip
ec495faffe733e998fd78efa7c6c8de047690bad2a6cd4ca088fbed72ff68358  node-v16.11.0-x64.msi
8be850c1384b5e96437ff2b884cb5add60bd44960863406dfddfbc08dc9c96a9  node-v16.11.0-x86.msi
74802d05a5a80d3050d505267a79fb5c66ada02ffc17946601f3997ce4d845b1  win-x64/node.exe
7c94657df6918a77dc8edefaf3b5415dbfb9eb83a88c17d216a48c8c36fcc58d  win-x64/node.lib
009f33196edf594a122baf0adec4f3f84874c59b23bc9e50ccf20675a2ec80c4  win-x64/node_pdb.7z
c94b27e403bafa5edaa29f545768b81333ec6e34058b4c14029e48be5d7f5f12  win-x64/node_pdb.zip
7214748424c802c38d6b0de348207c6bdce9d39a31a8bc5ae86b82ba936839ae  win-x86/node.exe
006af4382187d5cec9bd2b3781a017e221af8ed19de379c4a6f99375dea1d307  win-x86/node.lib
27e8c8187d39a3192c243da080926b4995b61455e2cf79c17271213e994129c7  win-x86/node_pdb.7z
dfa29528cd6e20a1b0e29f40453310119d8149aa864fa3904317feb5a5b9af28  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEdPEmArbxxOkT+qN606iWE2Q7YgEFAmFge4YACgkQ06iWE2Q7
YgGQzA/+KDfKmFpA+0JqiEahoBcaoNefx0B8Y0PWnihQ1WtcnwljhweT9+QcN+nP
Kb2RUhAMNhGGJmHhDszw/MUpI5Q6jf+0WndUXdH8rHHZJuzOjOEFlIwsDP1ggjqG
zUs7SQP6QXZLtFnJiBrmhpFIlg39oAI0IGbz9QqKJS9yzkJF7g+zkSg0Z/1ipzaU
Fb7T0CpLj6kgz5HCTh6feqxlyF/MoWeWNtXPC70/KfflXs2mEa5kmrYvw6X8RETb
XnB1EDq5F7zi1miaGYFhY4fE0LqKpZ7IMmD3V0VckXDdHQoafx2rqDCDbi7cq9In
1+WpNe01VfrvrYr+/G5KoYMcmBWHkVHkoYijLyvqX1eaIibx5D4Ag/E2eHq3ZJuj
24XBKCpE+lRqIM40YiXvqREoiWmxC4+pw3kmlqA8Q99HqZ3pyAh/qGiLN1ppp5ay
Vf98qoHD6L5itDMCI+DorawBML1d8sw7WAP0IHTIhRevrhFZViSJP6hQxZqg/0Jn
tAdyteD9A45UcgFpBhD1U1OsADAuhvvVd2ytxmhxcnDjNeydqF9mWJho8r24V7ON
hBGmBzbXJaq50kx6rFVvtOeAqaIjrkhD3BtS+KgMrJP2aqvyteLInDHrJDVuNGn/
R0uIxs1+AAj96wHsdX6bEdvicLQCYgysocRdN8a4utw4MzBMRSg=
=smSS
-----END PGP SIGNATURE-----

```
