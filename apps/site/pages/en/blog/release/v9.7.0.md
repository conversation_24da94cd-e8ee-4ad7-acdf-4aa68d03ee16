---
date: '2018-03-01T09:53:02.271Z'
category: release
title: Node v9.7.0 (Current)
layout: blog-post
author: <PERSON>
---

**2018-03-01, Version 9.7.0 (Current), @rvagg, prepared by @addaleax**

### Notable Changes

- **libuv**:

  - Updated to libuv 1.19.2 (<PERSON>) [#18918](https://github.com/nodejs/node/pull/18918)

- **src**:

  - Add initial support for Node.js-specific post-mortem metadata (<PERSON><PERSON>) [#14901](https://github.com/nodejs/node/pull/14901)

- **timers**:

  - The return value of `setImmediate()` now has `ref()` and `unref()` methods (<PERSON><PERSON><PERSON>) [#18139](https://github.com/nodejs/node/pull/18139)

- **util**:
  - It is now possible to get the name for a numerical platform-specific error code as a string (<PERSON><PERSON>) [#18186](https://github.com/nodejs/node/pull/18186)

### Commits

- [[`5ddef2988b`](https://github.com/nodejs/node/commit/5ddef2988b)] - **async_wrap**: schedule destroy hook as unref (Anatoli Papirovski) [#18241](https://github.com/nodejs/node/pull/18241)
- [[`be9777c5f6`](https://github.com/nodejs/node/commit/be9777c5f6)] - **benchmark**: add stream.pipe benchmarks (Mathias Buus) [#18617](https://github.com/nodejs/node/pull/18617)
- [[`4012ae8885`](https://github.com/nodejs/node/commit/4012ae8885)] - **build**: fix coverage build (Yihong Wang) [#18409](https://github.com/nodejs/node/pull/18409)
- [[`8c934990ef`](https://github.com/nodejs/node/commit/8c934990ef)] - **build**: add node_lib_target_name to cctest deps (Daniel Bevenius) [#18576](https://github.com/nodejs/node/pull/18576)
- [[`f7e1402923`](https://github.com/nodejs/node/commit/f7e1402923)] - **build**: include the libuv and zlib into node (Yihong Wang) [#18383](https://github.com/nodejs/node/pull/18383)
- [[`237a363dc7`](https://github.com/nodejs/node/commit/237a363dc7)] - **build**: make gyp user defined variables lowercase (Daniel Bevenius) [#16238](https://github.com/nodejs/node/pull/16238)
- [[`16ef386507`](https://github.com/nodejs/node/commit/16ef386507)] - **build, win**: vcbuild improvements (Bartosz Sosnowski) [#17015](https://github.com/nodejs/node/pull/17015)
- [[`4fa1f3197f`](https://github.com/nodejs/node/commit/4fa1f3197f)] - **cluster**: fix inspector port assignment (Santiago Gimeno) [#18696](https://github.com/nodejs/node/pull/18696)
- [[`ec55965501`](https://github.com/nodejs/node/commit/ec55965501)] - **deps**: upgrade libuv to 1.19.2 (cjihrig) [#18918](https://github.com/nodejs/node/pull/18918)
- [[`7fb72a5fa3`](https://github.com/nodejs/node/commit/7fb72a5fa3)] - **deps,src**: align ssize_t ABI between Node & nghttp2 (Anna Henningsen) [#18565](https://github.com/nodejs/node/pull/18565)
- [[`dd917eb946`](https://github.com/nodejs/node/commit/dd917eb946)] - **doc**: add pending-deprecation to deprecations list (Сковорода Никита Андреевич) [#18433](https://github.com/nodejs/node/pull/18433)
- [[`287946ddff`](https://github.com/nodejs/node/commit/287946ddff)] - **doc**: remove `Returns: {undefined}` (Sho Miyamoto) [#18951](https://github.com/nodejs/node/pull/18951)
- [[`4f454bde74`](https://github.com/nodejs/node/commit/4f454bde74)] - **doc**: mention git-node in the collaborator guide (Joyee Cheung) [#18960](https://github.com/nodejs/node/pull/18960)
- [[`4bc54238b2`](https://github.com/nodejs/node/commit/4bc54238b2)] - **doc**: update 2fa information in onboarding.md (Rich Trott) [#18968](https://github.com/nodejs/node/pull/18968)
- [[`b456e31964`](https://github.com/nodejs/node/commit/b456e31964)] - **doc**: add process.debugPort to doc/api/process.md (flickz) [#18716](https://github.com/nodejs/node/pull/18716)
- [[`6f177e7b5d`](https://github.com/nodejs/node/commit/6f177e7b5d)] - **doc**: `readable.push(undefined)` in non-object mode (陈刚) [#18283](https://github.com/nodejs/node/pull/18283)
- [[`85322518ca`](https://github.com/nodejs/node/commit/85322518ca)] - **doc**: remove extraneous "for example" text (Rich Trott) [#18890](https://github.com/nodejs/node/pull/18890)
- [[`38cf3cf494`](https://github.com/nodejs/node/commit/38cf3cf494)] - **doc**: update description of 'clientError' event (Luigi Pinca) [#18885](https://github.com/nodejs/node/pull/18885)
- [[`e447580872`](https://github.com/nodejs/node/commit/e447580872)] - **doc**: fix link in onboarding.md (Justin Lee) [#18878](https://github.com/nodejs/node/pull/18878)
- [[`205a84cf09`](https://github.com/nodejs/node/commit/205a84cf09)] - **doc**: remove CII badge in README (Roman Reiss) [#18908](https://github.com/nodejs/node/pull/18908)
- [[`1246902bae`](https://github.com/nodejs/node/commit/1246902bae)] - **errors**: move error creation helpers to errors.js (Joyee Cheung) [#18546](https://github.com/nodejs/node/pull/18546)
- [[`b3fe55aada`](https://github.com/nodejs/node/commit/b3fe55aada)] - **errors**: improve the description of ERR_INVALID_ARG_VALUE (Joyee Cheung) [#18358](https://github.com/nodejs/node/pull/18358)
- [[`112c9a3a19`](https://github.com/nodejs/node/commit/112c9a3a19)] - **http**: remove default 'drain' listener on upgrade (Luigi Pinca) [#18866](https://github.com/nodejs/node/pull/18866)
- [[`c7f9608626`](https://github.com/nodejs/node/commit/c7f9608626)] - **http**: allow \_httpMessage to be GC'ed (Luigi Pinca) [#18865](https://github.com/nodejs/node/pull/18865)
- [[`738b0a1f2e`](https://github.com/nodejs/node/commit/738b0a1f2e)] - **lib**: add `process` to internal module wrapper (Anna Henningsen) [#17198](https://github.com/nodejs/node/pull/17198)
- [[`cfb78bc1df`](https://github.com/nodejs/node/commit/cfb78bc1df)] - **process**: use linked reusable queue for ticks (Mathias Buus) [#18617](https://github.com/nodejs/node/pull/18617)
- [[`4acea14197`](https://github.com/nodejs/node/commit/4acea14197)] - **process**: do not directly schedule \_tickCallback in \_fatalException (Anatoli Papirovski) [#17841](https://github.com/nodejs/node/pull/17841)
- [[`d348496345`](https://github.com/nodejs/node/commit/d348496345)] - **process**: refactor nextTick for clarity (Anatoli Papirovski) [#17738](https://github.com/nodejs/node/pull/17738)
- [[`cf0b95c4b1`](https://github.com/nodejs/node/commit/cf0b95c4b1)] - **process**: use more direct sync I/O for stdio (Anna Henningsen) [#18019](https://github.com/nodejs/node/pull/18019)
- [[`b4c933dd44`](https://github.com/nodejs/node/commit/b4c933dd44)] - **promises**: refactor rejection handling (Anatoli Papirovski) [#18207](https://github.com/nodejs/node/pull/18207)
- [[`01398b29e9`](https://github.com/nodejs/node/commit/01398b29e9)] - **repl**: fix tab-complete warning (killagu) [#18881](https://github.com/nodejs/node/pull/18881)
- [[`e33b9fa7b5`](https://github.com/nodejs/node/commit/e33b9fa7b5)] - **src**: fix GetCpuProfiler() deprecation warning (Ben Noordhuis) [#18534](https://github.com/nodejs/node/pull/18534)
- [[`91694497ba`](https://github.com/nodejs/node/commit/91694497ba)] - **src**: refactor WriteWrap and ShutdownWraps (Anna Henningsen) [#18676](https://github.com/nodejs/node/pull/18676)
- [[`fa691f7d95`](https://github.com/nodejs/node/commit/fa691f7d95)] - **src**: only set JSStreamWrap write req after `write()` (Anna Henningsen) [#18676](https://github.com/nodejs/node/pull/18676)
- [[`296523a698`](https://github.com/nodejs/node/commit/296523a698)] - **src**: remove unnecessary async hooks check (Anatoli Papirovski) [#18291](https://github.com/nodejs/node/pull/18291)
- [[`4de4c54069`](https://github.com/nodejs/node/commit/4de4c54069)] - **src**: expose uv.errmap to binding (Joyee Cheung) [#17338](https://github.com/nodejs/node/pull/17338)
- [[`189e566076`](https://github.com/nodejs/node/commit/189e566076)] - **src**: do not redefine private for GenDebugSymbols (Joyee Cheung) [#18653](https://github.com/nodejs/node/pull/18653)
- [[`07c6fb983b`](https://github.com/nodejs/node/commit/07c6fb983b)] - **src**: use AliasedBuffer for TickInfo (Anatoli Papirovski) [#17881](https://github.com/nodejs/node/pull/17881)
- [[`684684e567`](https://github.com/nodejs/node/commit/684684e567)] - **src**: simplify handles for libuv streams (Anna Henningsen) [#18334](https://github.com/nodejs/node/pull/18334)
- [[`cb5ed45603`](https://github.com/nodejs/node/commit/cb5ed45603)] - **src**: refactor stream callbacks and ownership (Anna Henningsen) [#18334](https://github.com/nodejs/node/pull/18334)
- [[`f60757796b`](https://github.com/nodejs/node/commit/f60757796b)] - **src**: use `DoTryWrite()` for not-all-Buffer writev()s too (Anna Henningsen) [#18019](https://github.com/nodejs/node/pull/18019)
- [[`f17987ba16`](https://github.com/nodejs/node/commit/f17987ba16)] - **src**: remove `HasWriteQueue()` (Anna Henningsen) [#18019](https://github.com/nodejs/node/pull/18019)
- [[`2282dceb29`](https://github.com/nodejs/node/commit/2282dceb29)] - **src**: remove node namespace qualifiers (Daniel Bevenius) [#18962](https://github.com/nodejs/node/pull/18962)
- [[`6e7aa3d8f4`](https://github.com/nodejs/node/commit/6e7aa3d8f4)] - **src**: fix abort when taking a heap snapshot (Ben Noordhuis) [#18898](https://github.com/nodejs/node/pull/18898)
- [[`a17d6840e1`](https://github.com/nodejs/node/commit/a17d6840e1)] - **src**: fix deprecation warning in node_perf.cc (Daniel Bevenius) [#18877](https://github.com/nodejs/node/pull/18877)
- [[`46fc507054`](https://github.com/nodejs/node/commit/46fc507054)] - **(SEMVER-MINOR)** **src, test**: node internals' postmortem metadata (Matheus Marchini) [#14901](https://github.com/nodejs/node/pull/14901)
- [[`7853a7fd2a`](https://github.com/nodejs/node/commit/7853a7fd2a)] - **test**: add test for stream unpipe with 'data' listeners (Anna Henningsen) [#18516](https://github.com/nodejs/node/pull/18516)
- [[`3543c5543b`](https://github.com/nodejs/node/commit/3543c5543b)] - **test**: make sure WriteWrap tests are actually async (Anna Henningsen) [#18676](https://github.com/nodejs/node/pull/18676)
- [[`7dd3c8af88`](https://github.com/nodejs/node/commit/7dd3c8af88)] - **test**: add url type check in Module options (JiaHerr Tee) [#18664](https://github.com/nodejs/node/pull/18664)
- [[`1be5e33f03`](https://github.com/nodejs/node/commit/1be5e33f03)] - **test**: replace assert.throws with expectsError (sreepurnajasti) [#17997](https://github.com/nodejs/node/pull/17997)
- [[`df0d78a7e9`](https://github.com/nodejs/node/commit/df0d78a7e9)] - **test**: stdio pipe behavior tests (Bartosz Sosnowski) [#18614](https://github.com/nodejs/node/pull/18614)
- [[`35cddae18f`](https://github.com/nodejs/node/commit/35cddae18f)] - **test**: fix cctest -Wunused-variable warning (Ben Noordhuis) [#18530](https://github.com/nodejs/node/pull/18530)
- [[`743cf33616`](https://github.com/nodejs/node/commit/743cf33616)] - **test**: introduce SetUpTestCase/TearDownTestCase (Daniel Bevenius) [#18558](https://github.com/nodejs/node/pull/18558)
- [[`edba129df3`](https://github.com/nodejs/node/commit/edba129df3)] - **test**: http2 compat response.write() error checks (Trivikram) [#18859](https://github.com/nodejs/node/pull/18859)
- [[`f2dd17bde9`](https://github.com/nodejs/node/commit/f2dd17bde9)] - **(SEMVER-MINOR)** **timers**: allow Immediates to be unrefed (Anatoli Papirovski) [#18139](https://github.com/nodejs/node/pull/18139)
- [[`37f253e88f`](https://github.com/nodejs/node/commit/37f253e88f)] - **timers**: refactor setImmediate error handling (Anatoli Papirovski) [#17879](https://github.com/nodejs/node/pull/17879)
- [[`8474f86e9f`](https://github.com/nodejs/node/commit/8474f86e9f)] - **timers**: make setImmediate() immune to tampering (Ben Noordhuis) [#17736](https://github.com/nodejs/node/pull/17736)
- [[`484e06d89a`](https://github.com/nodejs/node/commit/484e06d89a)] - **tls**: use after free in tls_wrap (Kyle Farnung) [#18860](https://github.com/nodejs/node/pull/18860)
- [[`efb4646539`](https://github.com/nodejs/node/commit/efb4646539)] - **tls_wrap**: use DoTryWrite() (Anna Henningsen) [#18676](https://github.com/nodejs/node/pull/18676)
- [[`d255db3ae7`](https://github.com/nodejs/node/commit/d255db3ae7)] - **tools**: ignore VS compiler output in deps/v8 (Michaël Zasso) [#18952](https://github.com/nodejs/node/pull/18952)
- [[`fc6ee39ea6`](https://github.com/nodejs/node/commit/fc6ee39ea6)] - **tools**: fix custom eslint rule errors (Ruben Bridgewater) [#18853](https://github.com/nodejs/node/pull/18853)
- [[`f8691398e4`](https://github.com/nodejs/node/commit/f8691398e4)] - **tools, test**: fix prof polyfill readline (killagu) [#18641](https://github.com/nodejs/node/pull/18641)
- [[`38fd7902ef`](https://github.com/nodejs/node/commit/38fd7902ef)] - **tty**: fix console printing on Windows (Anna Henningsen) [#18214](https://github.com/nodejs/node/pull/18214)
- [[`def51bafbd`](https://github.com/nodejs/node/commit/def51bafbd)] - **url**: reduce deplicated codes in `autoEscapeStr` (Weijia Wang) [#18613](https://github.com/nodejs/node/pull/18613)
- [[`8e31bf42cf`](https://github.com/nodejs/node/commit/8e31bf42cf)] - **util**: skip type checks in internal getSystemErrorName (Joyee Cheung) [#18546](https://github.com/nodejs/node/pull/18546)
- [[`28fa906ec1`](https://github.com/nodejs/node/commit/28fa906ec1)] - **(SEMVER-MINOR)** **util**: implement util.getSystemErrorName() (Joyee Cheung) [#18186](https://github.com/nodejs/node/pull/18186)
- [[`38797b5804`](https://github.com/nodejs/node/commit/38797b5804)] - **vm**: consolidate validation (Timothy O. Peters) [#18816](https://github.com/nodejs/node/pull/18816)

Windows 32-bit Installer: https://nodejs.org/dist/v9.7.0/node-v9.7.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v9.7.0/node-v9.7.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v9.7.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v9.7.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v9.7.0/node-v9.7.0.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v9.7.0/node-v9.7.0-darwin-x64.tar.gz \
Linux 32-bit Binary: https://nodejs.org/dist/v9.7.0/node-v9.7.0-linux-x86.tar.xz \
Linux 64-bit Binary: https://nodejs.org/dist/v9.7.0/node-v9.7.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v9.7.0/node-v9.7.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v9.7.0/node-v9.7.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v9.7.0/node-v9.7.0-aix-ppc64.tar.gz \
SmartOS 32-bit Binary: https://nodejs.org/dist/v9.7.0/node-v9.7.0-sunos-x86.tar.xz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v9.7.0/node-v9.7.0-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v9.7.0/node-v9.7.0-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v9.7.0/node-v9.7.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v9.7.0/node-v9.7.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v9.7.0/node-v9.7.0.tar.gz \
Other release files: https://nodejs.org/dist/v9.7.0/ \
Documentation: https://nodejs.org/docs/v9.7.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

b49936b7b28849890a8c255550a89c2bd3569abf0abaccbaf584f80a718cfa2f  node-v9.7.0-aix-ppc64.tar.gz
9730cf3c4c5e228d4855c3362b63dbe59041202d89ae30d7f5ca42f60f742c5d  node-v9.7.0-darwin-x64.tar.gz
3c7494cd32297b4c8deb21713ff3d45e9b772b3b5ffc37d44d0763153007b008  node-v9.7.0-darwin-x64.tar.xz
24441951bb0b10273086fa3d0955cf18e101c7213e2ab37614dd2442315eda3d  node-v9.7.0-headers.tar.gz
360e45f6cf1b03d90667e470d5ea9b2687fa0ec156013ac55f1c40dc243220af  node-v9.7.0-headers.tar.xz
036d7113d7cab9cfc7543bf675676bf2144b477d52f6b1d9c38bf1696d54e49c  node-v9.7.0-linux-arm64.tar.gz
18bec2bec5e2496f33b4d9d7f3bea7411cdb5d108f390b5c1949ddeda0e61125  node-v9.7.0-linux-arm64.tar.xz
778bc3b9bff91bb0200b49547f690ec68cc93fb7a1026153d2b29d54e730b07a  node-v9.7.0-linux-armv6l.tar.gz
8b0574d318499224ca4c6c3460976fef5c6fdf0faa616cb7f3c21c06bdc82839  node-v9.7.0-linux-armv6l.tar.xz
678a13d94e93f59a3f9384e72ce1717f79f7fcc37cbc0cdf4a5aafaa69eb418d  node-v9.7.0-linux-armv7l.tar.gz
b944374e523c10cea85877dd96fbe83e7319f80c6664a209a9604063b9b78409  node-v9.7.0-linux-armv7l.tar.xz
06a7294043d94304ded39a2a3c199f62246269606faa647f290b8526e93cb179  node-v9.7.0-linux-ppc64le.tar.gz
b5b6f0e290b555d33909051efdaa07fa6fd31a9b613ad92107dc0ff065167dc4  node-v9.7.0-linux-ppc64le.tar.xz
424e1cadc6e957253b7bd71872c0daa7b03b55de9d6a68dfc45924082bfa20fc  node-v9.7.0-linux-s390x.tar.gz
fe3eb7b0a0e46055fb35e764f01746e127ab3c0bb1c48e8c88916fa78bcc5bb9  node-v9.7.0-linux-s390x.tar.xz
c490ef08cfcf048229ccd99b17fe7fcd2b3d6e063ac9de4f14b603914ad6dbe0  node-v9.7.0-linux-x64.tar.gz
5c7549ceeebdc7ac5acdcae5fffbeb9585e3b9e2003e70e116d9f19c768f73d1  node-v9.7.0-linux-x64.tar.xz
cfcfc7aa24286abb110e195596737c160a295538ec68d3411158ea4ed2c56afa  node-v9.7.0-linux-x86.tar.gz
d55ed20036040148195f44fd9704b55f57f6ff87b2d0ef5e608baf03981ec889  node-v9.7.0-linux-x86.tar.xz
5247a2467c4722bd8697ede7e4aa2b26be3b77a92c51243451252753f61a6482  node-v9.7.0.pkg
0064b75d82cc866e81b9ac65fac4376febe782d85c5ff1c96715a8172dd54aa1  node-v9.7.0-sunos-x64.tar.gz
d401a1444cefc85f77605c0040fad93f6d0c28006ed239b0e93d7029b44c38bb  node-v9.7.0-sunos-x64.tar.xz
ae475e292397fbdd9d1d51035fb56f651acecb8d84efcda9201ae7402ccc75a6  node-v9.7.0-sunos-x86.tar.gz
2e6265a38f07fd0735ffe23c17cbb8603b98ef4a2fc1bdc6541e4dbb6713896a  node-v9.7.0-sunos-x86.tar.xz
9ef89425bfc3f14a5a27b5443997c237afe4fe00602f0abb1d5f42aa6426e5f3  node-v9.7.0.tar.gz
a136db69769fadbfc31b5577284faf644589519fb3c51e3dc7403566635956c9  node-v9.7.0.tar.xz
8e49a8bada1159ed94b50a669557a832bb9c79de0120c740aea09f7267a31d80  node-v9.7.0-win-x64.7z
a17796c9c888e88a9a086dc1a22cdd872d8e948fbf1e988a8132fef716990530  node-v9.7.0-win-x64.zip
2e375789e735fc6bc792814e49e6049d290294ed362847e8e10c30accffa02f3  node-v9.7.0-win-x86.7z
995737b0ba5fde383dadce08952d865f517ebe5d94852977fa079a68baac7e5a  node-v9.7.0-win-x86.zip
2a37c917c6cbb3a3aa04ec3bf82a2ed703bf44de3d0c13af9a6c33c04ea060ba  node-v9.7.0-x64.msi
fb7e4f5e84acaa06821313e11d09a76893fbe602ecb9d567379c4e75e9d9f376  node-v9.7.0-x86.msi
6040ad65b49d541948de3465e7e8bbec07eed8387da9ad64a392ef4cfa9dac5a  win-x64/node.exe
86a0809f202cca44c0065523be1fa7ae36904efaa381292ace83f30a0cc79688  win-x64/node.lib
69b20c210aa6d47610e49640a3fceecfc38b13df6b37b34cc6bc77445ac17ab0  win-x64/node_pdb.7z
42063e57d9818ffa842b000f3a92daf00fcda5cf34c72fdc4135403957999574  win-x64/node_pdb.zip
a2a9683d5aea1830e8ee7387769e42ee906105899cc27f9af6c791fdf62f3eea  win-x86/node.exe
5cf1724b56f985336b07a414def026db050dc72951280d814d42c694f82e73b4  win-x86/node.lib
2a393b9ed33877d0a759bf8a98ac421b77fd7438b5e79a6844b8156643c6b29c  win-x86/node_pdb.7z
f384b0fa58876334fb488ee59b606caee029190d44e9afccb1823884cc439cdc  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQEcBAEBCAAGBQJal8zqAAoJEMJzeS99g1RdIkAH/1Mu7WsUYf0nDbEa25EgHFYt
JWiyzQ/fUgXGJE8rgem/p/ndVQRmWGTLGRaDxtUrgUgMWfyBOvwn2160YmXVg16H
gFQLwxxfl1QAU7JuXvgUZnYfy65BJJX+S4hDp01Uy6B3Xp5+T7B2/wpjqw3pvBIn
c57v1Hl44PSJM87wZpFWOP3gnWvU+aeEL55X5nFgs11jnUYDq44vj6nDNg7VMivl
FsdqIjin2mLKn/PGXgyYClLf0hbpDWv9+8oDh4xoMM/2mygOdJIYS6u/NMwzZoKk
Ad3+Pk/XQ9ag65Du/CJdRPHDOKidASDuRXlScnmAtEDb/TrnZiPpT6pWULY5E7M=
=5gWb
-----END PGP SIGNATURE-----

```
