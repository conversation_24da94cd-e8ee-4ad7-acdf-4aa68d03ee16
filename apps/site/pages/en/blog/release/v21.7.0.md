---
date: '2024-03-06T18:38:37.085Z'
category: release
title: Node v21.7.0 (Current)
layout: blog-post
author: <PERSON> & <PERSON>
---

## 2024-03-06, Version 21.7.0 (Current), @RafaelGSS prepared by @marco-ippolito

### Text Styling

- `util.styleText(format, text)`: This function returns a formatted text considering the `format` passed.

A new API has been created to format text based on `util.inspect.colors`, enabling you to style text in different colors (such as red, blue, ...) and emphasis (italic, bold, ...).

```cjs
const { styleText } = require('node:util');
const errorMessage = styleText('red', 'Error! Error!');
console.log(errorMessage);
```

Contributed by <PERSON> and <PERSON><PERSON><PERSON> in [#51850](https://github.com/nodejs/node/pull/51850).

### Loading and parsing environment variables

- `process.loadEnvFile(path)`:

  - Use this function to load the `.env` file. If no path is specified, it automatically loads the .env file in the current directory. Example: `process.loadEnvFile()`.
  - Load a specific .env file by specifying its path. Example: `process.loadEnvFile('./development.env')`.

- `util.parseEnv(content)`:
  - Use this function to parse an existing string containing environment variable assignments.
  - Example usage: `require('node:util').parseEnv('HELLO=world')`.

Contributed by Yagiz Nizipli in [#51476](https://github.com/nodejs/node/pull/51476)

### Support for multi-line values for `.env` file

Node.js 21.7.0 will now support multi-line values in the .env file:

```text
MULTI_LINE="HELLO
WORLD"
```

Contributed by Ilyas Shabi [#51289](https://github.com/nodejs/node/pull/51289)

### sea: support embedding assets

Users can now include assets by adding a key-path dictionary
to the configuration as the `assets` field. At build time, Node.js
would read the assets from the specified paths and bundle them into
the preparation blob. In the generated executable, users can retrieve
the assets using the `sea.getAsset()` and `sea.getAssetAsBlob()` API.

```json
{
  "main": "/path/to/bundled/script.js",
  "output": "/path/to/write/the/generated/blob.blob",
  "assets": {
    "a.jpg": "/path/to/a.jpg",
    "b.txt": "/path/to/b.txt"
  }
}
```

The single-executable application can access the assets as follows:

```cjs
const { getAsset } = require('node:sea');
// Returns a copy of the data in an ArrayBuffer
const image = getAsset('a.jpg');
// Returns a string decoded from the asset as UTF8.
const text = getAsset('b.txt', 'utf8');
// Returns a Blob containing the asset without copying.
const blob = getAssetAsBlob('a.jpg');
```

Contributed by Joyee Cheung in [#50960](https://github.com/nodejs/node/pull/50960)

### vm: support using the default loader to handle dynamic import()

This patch adds support for using `vm.constants.USE_MAIN_CONTEXT_DEFAULT_LOADER` as the
`importModuleDynamically` option in all vm APIs that take this option except `vm.SourceTextModule`. This allows users to have a shortcut to support dynamic `import()` in the compiled code without missing the compilation cache if they don't need customization of the loading process. We emit an experimental warning when the `import()` is actually handled by the default loader through this option instead of requiring `--experimental-vm-modules`.

```js
const { Script, constants } = require('node:vm');
const { resolve } = require('node:path');
const { writeFileSync } = require('node:fs');

// Write test.js and test.txt to the directory where the current script
// being run is located.
writeFileSync(
  resolve(__dirname, 'test.mjs'),
  'export const filename = "./test.json";'
);
writeFileSync(resolve(__dirname, 'test.json'), '{"hello": "world"}');

// Compile a script that loads test.mjs and then test.json
// as if the script is placed in the same directory.
const script = new Script(
  `(async function() {
    const { filename } = await import('./test.mjs');
    return import(filename, { with: { type: 'json' } })
  })();`,
  {
    filename: resolve(__dirname, 'test-with-default.js'),
    importModuleDynamically: constants.USE_MAIN_CONTEXT_DEFAULT_LOADER,
  }
);

// { default: { hello: 'world' } }
script.runInThisContext().then(console.log);
```

Contributed by Joyee Cheung in [#51244](https://github.com/nodejs/node/pull/51244)

### crypto: implement crypto.hash()

This patch introduces a helper crypto.hash() that computes
a digest from the input at one shot. This can be 1.2-2x faster
than the object-based createHash() for smaller inputs (<= 5MB)
that are readily available (not streamed) and incur less memory
overhead since no intermediate objects will be created.

```js
const crypto = require('node:crypto');

// Hashing a string and return the result as a hex-encoded string.
const string = 'Node.js';
// 10b3493287f831e81a438811a1ffba01f8cec4b7
console.log(crypto.hash('sha1', string));
```

Contributed by Joyee Cheung in [#51044](https://github.com/nodejs/node/pull/51044)

### Other Notable Changes

- \[[`8ae0eeb7f4`](https://github.com/nodejs/node/commit/8ae0eeb7f4)] - **(SEMVER-MINOR)** **build**: build opt to set local location of headers (Michael Dawson) [#51525](https://github.com/nodejs/node/pull/51525)
- \[[`496776cc78`](https://github.com/nodejs/node/commit/496776cc78)] - **crypto**: update root certificates to NSS 3.98 (Node.js GitHub Bot) [#51794](https://github.com/nodejs/node/pull/51794)
- \[[`a8c9e6f7e9`](https://github.com/nodejs/node/commit/a8c9e6f7e9)] - **doc**: add zcbenz to collaborators (Cheng Zhao) [#51812](https://github.com/nodejs/node/pull/51812)
- \[[`adbf2d3837`](https://github.com/nodejs/node/commit/adbf2d3837)] - **doc**: add lemire to collaborators (Daniel Lemire) [#51572](https://github.com/nodejs/node/pull/51572)
- \[[`4b1c6839f4`](https://github.com/nodejs/node/commit/4b1c6839f4)] - **(SEMVER-MINOR)** **http2**: add h2 compat support for appendHeader (Tim Perry) [#51412](https://github.com/nodejs/node/pull/51412)
- \[[`d8aa2bac0b`](https://github.com/nodejs/node/commit/d8aa2bac0b)] - **(SEMVER-MINOR)** **http2**: add server handshake utility (snek) [#51172](https://github.com/nodejs/node/pull/51172)
- \[[`b9275d9039`](https://github.com/nodejs/node/commit/b9275d9039)] - **(SEMVER-MINOR)** **http2**: receive customsettings (Marten Richter) [#51323](https://github.com/nodejs/node/pull/51323)
- \[[`5a2d2daad5`](https://github.com/nodejs/node/commit/5a2d2daad5)] - **(SEMVER-MINOR)** **lib**: move encodingsMap to internal/util (Joyee Cheung) [#51044](https://github.com/nodejs/node/pull/51044)
- \[[`e8d9065262`](https://github.com/nodejs/node/commit/e8d9065262)] - **(SEMVER-MINOR)** **sea**: support sea.getRawAsset() (Joyee Cheung) [#50960](https://github.com/nodejs/node/pull/50960)
- \[[`47186fbad5`](https://github.com/nodejs/node/commit/47186fbad5)] - **(SEMVER-MINOR)** **src**: print string content better in BlobDeserializer (Joyee Cheung) [#50960](https://github.com/nodejs/node/pull/50960)
- \[[`119e045053`](https://github.com/nodejs/node/commit/119e045053)] - **(SEMVER-MINOR)** **src**: do not coerce dotenv paths (Tobias Nießen) [#51425](https://github.com/nodejs/node/pull/51425)
- \[[`9ab353af00`](https://github.com/nodejs/node/commit/9ab353af00)] - **(SEMVER-MINOR)** **stream**: implement `min` option for `ReadableStreamBYOBReader.read` (Mattias Buelens) [#50888](https://github.com/nodejs/node/pull/50888)

### Commits

- \[[`4ddb9b33d5`](https://github.com/nodejs/node/commit/4ddb9b33d5)] - **async_hooks,inspector**: implement inspector api without async_wrap (Gabriel Bota) [#51501](https://github.com/nodejs/node/pull/51501)
- \[[`7e06c11f55`](https://github.com/nodejs/node/commit/7e06c11f55)] - **benchmark**: update iterations of assert/deepequal-typedarrays.js (Lei Shi) [#51419](https://github.com/nodejs/node/pull/51419)
- \[[`72be232006`](https://github.com/nodejs/node/commit/72be232006)] - **benchmark**: update iterations of benchmark/assert/deepequal-map.js (Lei Shi) [#51416](https://github.com/nodejs/node/pull/51416)
- \[[`92e7c310cb`](https://github.com/nodejs/node/commit/92e7c310cb)] - **benchmark**: rename startup.js to startup-core.js (Joyee Cheung) [#51669](https://github.com/nodejs/node/pull/51669)
- \[[`c9ada533a2`](https://github.com/nodejs/node/commit/c9ada533a2)] - **build**: remove `librt` libs link for Android compatibility (BuShe Pie) [#51632](https://github.com/nodejs/node/pull/51632)
- \[[`86ac787889`](https://github.com/nodejs/node/commit/86ac787889)] - **build**: do not rely on gn_helpers in GN build (Cheng Zhao) [#51439](https://github.com/nodejs/node/pull/51439)
- \[[`9be6b7ccf0`](https://github.com/nodejs/node/commit/9be6b7ccf0)] - **build**: fix warning in cares under GN build (Cheng Zhao) [#51687](https://github.com/nodejs/node/pull/51687)
- \[[`d1a8c2e989`](https://github.com/nodejs/node/commit/d1a8c2e989)] - **build**: fix building js2c with GN (Cheng Zhao) [#51818](https://github.com/nodejs/node/pull/51818)
- \[[`9840715dc0`](https://github.com/nodejs/node/commit/9840715dc0)] - **build**: encode non-ASCII Latin1 characters as one byte in JS2C (Joyee Cheung) [#51605](https://github.com/nodejs/node/pull/51605)
- \[[`8ae0eeb7f4`](https://github.com/nodejs/node/commit/8ae0eeb7f4)] - **(SEMVER-MINOR)** **build**: build opt to set local location of headers (Michael Dawson) [#51525](https://github.com/nodejs/node/pull/51525)
- \[[`1999719877`](https://github.com/nodejs/node/commit/1999719877)] - **build**: use macOS m1 machines for testing (Yagiz Nizipli) [#51620](https://github.com/nodejs/node/pull/51620)
- \[[`85f63f3d7d`](https://github.com/nodejs/node/commit/85f63f3d7d)] - **build**: check before removing %config% link (liudonghua) [#51437](https://github.com/nodejs/node/pull/51437)
- \[[`cc37959232`](https://github.com/nodejs/node/commit/cc37959232)] - **build**: increase parallel executions in github (Yagiz Nizipli) [#51554](https://github.com/nodejs/node/pull/51554)
- \[[`2921d55121`](https://github.com/nodejs/node/commit/2921d55121)] - **build**: remove copyright header in node.gni (Cheng Zhao) [#51535](https://github.com/nodejs/node/pull/51535)
- \[[`9da0926396`](https://github.com/nodejs/node/commit/9da0926396)] - **build**: update GN build files for ngtcp2 (Cheng Zhao) [#51313](https://github.com/nodejs/node/pull/51313)
- \[[`59117317f3`](https://github.com/nodejs/node/commit/59117317f3)] - **build,tools**: make addons tests work with GN (Cheng Zhao) [#50737](https://github.com/nodejs/node/pull/50737)
- \[[`78c226281c`](https://github.com/nodejs/node/commit/78c226281c)] - **(SEMVER-MINOR)** **crypto**: implement crypto.hash() (Joyee Cheung) [#51044](https://github.com/nodejs/node/pull/51044)
- \[[`496776cc78`](https://github.com/nodejs/node/commit/496776cc78)] - **crypto**: update root certificates to NSS 3.98 (Node.js GitHub Bot) [#51794](https://github.com/nodejs/node/pull/51794)
- \[[`17c554f1ca`](https://github.com/nodejs/node/commit/17c554f1ca)] - **crypto**: use EVP_MD_fetch and cache EVP_MD for hashes (Joyee Cheung) [#51034](https://github.com/nodejs/node/pull/51034)
- \[[`014cc53541`](https://github.com/nodejs/node/commit/014cc53541)] - **deps**: upgrade npm to 10.5.0 (npm team) [#51913](https://github.com/nodejs/node/pull/51913)
- \[[`4ebb944800`](https://github.com/nodejs/node/commit/4ebb944800)] - **deps**: update undici to 6.6.2 (Michaël Zasso) [#51667](https://github.com/nodejs/node/pull/51667)
- \[[`3b29dff0ed`](https://github.com/nodejs/node/commit/3b29dff0ed)] - **deps**: update ngtcp2 to 1.3.0 (Node.js GitHub Bot) [#51796](https://github.com/nodejs/node/pull/51796)
- \[[`28c0ffb363`](https://github.com/nodejs/node/commit/28c0ffb363)] - **deps**: update simdjson to 3.7.0 (Daniel Lemire) [#51859](https://github.com/nodejs/node/pull/51859)
- \[[`58b1403693`](https://github.com/nodejs/node/commit/58b1403693)] - **deps**: update corepack to 0.25.2 (Node.js GitHub Bot) [#51810](https://github.com/nodejs/node/pull/51810)
- \[[`c7083720cc`](https://github.com/nodejs/node/commit/c7083720cc)] - **deps**: update c-ares to 1.27.0 (Node.js GitHub Bot) [#51846](https://github.com/nodejs/node/pull/51846)
- \[[`6d2699d40b`](https://github.com/nodejs/node/commit/6d2699d40b)] - **deps**: update timezone to 2024a (Michaël Zasso) [#51723](https://github.com/nodejs/node/pull/51723)
- \[[`8d2222714d`](https://github.com/nodejs/node/commit/8d2222714d)] - **deps**: update icu to 74.2 (Michaël Zasso) [#51723](https://github.com/nodejs/node/pull/51723)
- \[[`c3dbd7cccd`](https://github.com/nodejs/node/commit/c3dbd7cccd)] - **deps**: update c-ares to 1.26.0 (Node.js GitHub Bot) [#51582](https://github.com/nodejs/node/pull/51582)
- \[[`dfc3811056`](https://github.com/nodejs/node/commit/dfc3811056)] - **deps**: update googletest to 6a59382 (Node.js GitHub Bot) [#51580](https://github.com/nodejs/node/pull/51580)
- \[[`8235c2676e`](https://github.com/nodejs/node/commit/8235c2676e)] - **deps**: update nghttp2 to 1.59.0 (Node.js GitHub Bot) [#51581](https://github.com/nodejs/node/pull/51581)
- \[[`2ad665e24f`](https://github.com/nodejs/node/commit/2ad665e24f)] - **deps**: V8: cherry-pick efb1133eb894 (Joyee Cheung) [#51551](https://github.com/nodejs/node/pull/51551)
- \[[`e5db8d416f`](https://github.com/nodejs/node/commit/e5db8d416f)] - **deps**: update corepack to 0.24.1 (Node.js GitHub Bot) [#51459](https://github.com/nodejs/node/pull/51459)
- \[[`fe597de72e`](https://github.com/nodejs/node/commit/fe597de72e)] - **deps**: update ada to 2.7.6 (Node.js GitHub Bot) [#51542](https://github.com/nodejs/node/pull/51542)
- \[[`5aca6f54f9`](https://github.com/nodejs/node/commit/5aca6f54f9)] - **deps**: update ada to 2.7.5 (Node.js GitHub Bot) [#51542](https://github.com/nodejs/node/pull/51542)
- \[[`8f63f6ff57`](https://github.com/nodejs/node/commit/8f63f6ff57)] - **deps**: update ngtcp2 to 1.2.0 (Node.js GitHub Bot) [#51584](https://github.com/nodejs/node/pull/51584)
- \[[`a04aa36ce8`](https://github.com/nodejs/node/commit/a04aa36ce8)] - **deps**: update googletest to 7c07a86 (Node.js GitHub Bot) [#51458](https://github.com/nodejs/node/pull/51458)
- \[[`4b1d25b68d`](https://github.com/nodejs/node/commit/4b1d25b68d)] - **deps**: update ngtcp2 to 1.1.0 (Node.js GitHub Bot) [#51319](https://github.com/nodejs/node/pull/51319)
- \[[`682f4f67b0`](https://github.com/nodejs/node/commit/682f4f67b0)] - **deps**: update acorn-walk to 8.3.2 (Node.js GitHub Bot) [#51457](https://github.com/nodejs/node/pull/51457)
- \[[`365a9dc2cb`](https://github.com/nodejs/node/commit/365a9dc2cb)] - **deps**: update timezone to 2023d (Node.js GitHub Bot) [#51461](https://github.com/nodejs/node/pull/51461)
- \[[`40e8b362a2`](https://github.com/nodejs/node/commit/40e8b362a2)] - **deps**: update base64 to 0.5.2 (Node.js GitHub Bot) [#51455](https://github.com/nodejs/node/pull/51455)
- \[[`139a626264`](https://github.com/nodejs/node/commit/139a626264)] - **deps**: compile c-ares with C11 support (Michaël Zasso) [#51410](https://github.com/nodejs/node/pull/51410)
- \[[`1cc37c4355`](https://github.com/nodejs/node/commit/1cc37c4355)] - **deps**: upgrade npm to 10.3.0 (npm team) [#51431](https://github.com/nodejs/node/pull/51431)
- \[[`942e10f5b5`](https://github.com/nodejs/node/commit/942e10f5b5)] - **deps**: update c-ares to 1.25.0 (Node.js GitHub Bot) [#51385](https://github.com/nodejs/node/pull/51385)
- \[[`17cb4af5a9`](https://github.com/nodejs/node/commit/17cb4af5a9)] - **deps**: update uvwasi to 0.0.20 and fixup tests (Michael Dawson) [#51355](https://github.com/nodejs/node/pull/51355)
- \[[`76582e434c`](https://github.com/nodejs/node/commit/76582e434c)] - **deps**: add nghttp3/\*\*/.deps to .gitignore (Luigi Pinca) [#51400](https://github.com/nodejs/node/pull/51400)
- \[[`4a889e8ea3`](https://github.com/nodejs/node/commit/4a889e8ea3)] - **doc**: add stability index to crypto.hash() (Joyee Cheung) [#51978](https://github.com/nodejs/node/pull/51978)
- \[[`3fdaeba9e6`](https://github.com/nodejs/node/commit/3fdaeba9e6)] - **doc**: remove redundant backquote which breaks sentence (JounQin) [#51904](https://github.com/nodejs/node/pull/51904)
- \[[`58747734a2`](https://github.com/nodejs/node/commit/58747734a2)] - **doc**: update node-api/node-addon-api team link to sharing project news (Ulises Gascón) [#51877](https://github.com/nodejs/node/pull/51877)
- \[[`2cdfe35437`](https://github.com/nodejs/node/commit/2cdfe35437)] - **doc**: add website team to sharing project news (Ulises Gascón) [#49002](https://github.com/nodejs/node/pull/49002)
- \[[`db30428f06`](https://github.com/nodejs/node/commit/db30428f06)] - **doc**: update guide link for Event Loop (Shrujal Shah) [#51874](https://github.com/nodejs/node/pull/51874)
- \[[`a5a17a18e3`](https://github.com/nodejs/node/commit/a5a17a18e3)] - **doc**: change `ExperimentalWarnings` to `ExperimentalWarning` (Ameet Kaustav) [#51741](https://github.com/nodejs/node/pull/51741)
- \[[`32d92aca1f`](https://github.com/nodejs/node/commit/32d92aca1f)] - **doc**: add Paolo to TSC members (Michael Dawson) [#51825](https://github.com/nodejs/node/pull/51825)
- \[[`2de1e85268`](https://github.com/nodejs/node/commit/2de1e85268)] - **doc**: reserve 123 for Electron 30 (Keeley Hammond) [#51803](https://github.com/nodejs/node/pull/51803)
- \[[`ad8cefcbf1`](https://github.com/nodejs/node/commit/ad8cefcbf1)] - **doc**: add mention to GPG_TTY (Rafael Gonzaga) [#51806](https://github.com/nodejs/node/pull/51806)
- \[[`a8c9e6f7e9`](https://github.com/nodejs/node/commit/a8c9e6f7e9)] - **doc**: add zcbenz to collaborators (Cheng Zhao) [#51812](https://github.com/nodejs/node/pull/51812)
- \[[`4e0c79bea9`](https://github.com/nodejs/node/commit/4e0c79bea9)] - **doc**: add entry to stewards (Rafael Gonzaga) [#51760](https://github.com/nodejs/node/pull/51760)
- \[[`7fa30812ea`](https://github.com/nodejs/node/commit/7fa30812ea)] - **doc**: update technical priorities for 2023 (Jean Burellier) [#47523](https://github.com/nodejs/node/pull/47523)
- \[[`af6b5b1722`](https://github.com/nodejs/node/commit/af6b5b1722)] - **doc**: mark isWebAssemblyCompiledModule eol (Marco Ippolito) [#51442](https://github.com/nodejs/node/pull/51442)
- \[[`a62f69ecad`](https://github.com/nodejs/node/commit/a62f69ecad)] - **doc**: fix `globals.md` introduction (Antoine du Hamel) [#51742](https://github.com/nodejs/node/pull/51742)
- \[[`519dc8aad6`](https://github.com/nodejs/node/commit/519dc8aad6)] - **doc**: updates for better json generating (Dmitry Semigradsky) [#51592](https://github.com/nodejs/node/pull/51592)
- \[[`1b45ca4e38`](https://github.com/nodejs/node/commit/1b45ca4e38)] - **doc**: document the GN build (Cheng Zhao) [#51676](https://github.com/nodejs/node/pull/51676)
- \[[`37182c4c1f`](https://github.com/nodejs/node/commit/37182c4c1f)] - **doc**: fix uncaught exception example (Gabriel Schulhof) [#51638](https://github.com/nodejs/node/pull/51638)
- \[[`c9be260b7d`](https://github.com/nodejs/node/commit/c9be260b7d)] - **doc**: clarify execution of `after` hook on test suite completion (Ognjen Jevremović) [#51523](https://github.com/nodejs/node/pull/51523)
- \[[`8c0a257021`](https://github.com/nodejs/node/commit/8c0a257021)] - **doc**: fix `dns.lookup` and `dnsPromises.lookup` description (Duncan Chiu) [#51517](https://github.com/nodejs/node/pull/51517)
- \[[`177e13cb0d`](https://github.com/nodejs/node/commit/177e13cb0d)] - **doc**: note that path.normalize deviates from POSIX (Tobias Nießen) [#51513](https://github.com/nodejs/node/pull/51513)
- \[[`adbf2d3837`](https://github.com/nodejs/node/commit/adbf2d3837)] - **doc**: add lemire to collaborators (Daniel Lemire) [#51572](https://github.com/nodejs/node/pull/51572)
- \[[`fd2a3cef57`](https://github.com/nodejs/node/commit/fd2a3cef57)] - **doc**: fix historical experimental fetch flag (Kenrick) [#51506](https://github.com/nodejs/node/pull/51506)
- \[[`1d40a0067a`](https://github.com/nodejs/node/commit/1d40a0067a)] - **doc**: fix type of connectionAttempt parameter (Rafael Gonzaga) [#51490](https://github.com/nodejs/node/pull/51490)
- \[[`d3b78051ce`](https://github.com/nodejs/node/commit/d3b78051ce)] - **doc**: remove reference to resolved child_process v8 issue (Ian Kerins) [#51467](https://github.com/nodejs/node/pull/51467)
- \[[`2bf371886a`](https://github.com/nodejs/node/commit/2bf371886a)] - **doc**: update typos (Aranđel Šarenac) [#51475](https://github.com/nodejs/node/pull/51475)
- \[[`10f95283c6`](https://github.com/nodejs/node/commit/10f95283c6)] - **doc**: add notes on inspector breakpoints (Chengzhong Wu) [#51417](https://github.com/nodejs/node/pull/51417)
- \[[`4ea194ab33`](https://github.com/nodejs/node/commit/4ea194ab33)] - **doc**: add links in `offboarding.md` (Antoine du Hamel) [#51440](https://github.com/nodejs/node/pull/51440)
- \[[`fc5629616b`](https://github.com/nodejs/node/commit/fc5629616b)] - **doc**: fix spelling mistake (u9g) [#51454](https://github.com/nodejs/node/pull/51454)
- \[[`70e88cf159`](https://github.com/nodejs/node/commit/70e88cf159)] - **doc**: add check for security reverts (Michael Dawson) [#51376](https://github.com/nodejs/node/pull/51376)
- \[[`74d4e382a7`](https://github.com/nodejs/node/commit/74d4e382a7)] - **doc**: fix some policy scope typos (Tim Kuijsten) [#51234](https://github.com/nodejs/node/pull/51234)
- \[[`d7658ca6a2`](https://github.com/nodejs/node/commit/d7658ca6a2)] - **doc**: improve subtests documentation (Marco Ippolito) [#51379](https://github.com/nodejs/node/pull/51379)
- \[[`c18a813638`](https://github.com/nodejs/node/commit/c18a813638)] - **doc**: add missing word in `child_process.md` (Joseph Joy) [#50370](https://github.com/nodejs/node/pull/50370)
- \[[`fabd5c4b21`](https://github.com/nodejs/node/commit/fabd5c4b21)] - **doc**: fixup alignment of warning subsection (James M Snell) [#51374](https://github.com/nodejs/node/pull/51374)
- \[[`80c750c8c1`](https://github.com/nodejs/node/commit/80c750c8c1)] - **doc,crypto**: further clarify RSA_PKCS1_PADDING support (Tobias Nießen) [#51799](https://github.com/nodejs/node/pull/51799)
- \[[`b53919d988`](https://github.com/nodejs/node/commit/b53919d988)] - **doc,crypto**: add changelog and note about disabled RSA_PKCS1_PADDING (Filip Skokan) [#51782](https://github.com/nodejs/node/pull/51782)
- \[[`08832e76d3`](https://github.com/nodejs/node/commit/08832e76d3)] - **esm**: improve error when calling `import.meta.resolve` from `data:` URL (Antoine du Hamel) [#49516](https://github.com/nodejs/node/pull/49516)
- \[[`78f818069a`](https://github.com/nodejs/node/commit/78f818069a)] - **events**: no stopPropagation call in cancelBubble (mert.altin) [#50405](https://github.com/nodejs/node/pull/50405)
- \[[`f130a33438`](https://github.com/nodejs/node/commit/f130a33438)] - _**Revert**_ "**fs**: remove workaround for `esm` package" (Jeremy Meng) [#50907](https://github.com/nodejs/node/pull/50907)
- \[[`5a8af3d362`](https://github.com/nodejs/node/commit/5a8af3d362)] - **fs**: load rimraf lazily in fs/promises (Joyee Cheung) [#51617](https://github.com/nodejs/node/pull/51617)
- \[[`15a7f2103b`](https://github.com/nodejs/node/commit/15a7f2103b)] - **fs**: remove race condition for recursive watch on Linux (Matteo Collina) [#51406](https://github.com/nodejs/node/pull/51406)
- \[[`d8bb4b2c1e`](https://github.com/nodejs/node/commit/d8bb4b2c1e)] - **fs**: update jsdoc for `filehandle.createWriteStream` and `appendFile` (Jungku Lee) [#51494](https://github.com/nodejs/node/pull/51494)
- \[[`e8fffebdd3`](https://github.com/nodejs/node/commit/e8fffebdd3)] - **fs,test**: add URL to string to fs.watch (Rafael Gonzaga) [#51346](https://github.com/nodejs/node/pull/51346)
- \[[`ec17fd73cc`](https://github.com/nodejs/node/commit/ec17fd73cc)] - **http**: fix `close` return value mismatch between doc and implementation (kylo5aby) [#51797](https://github.com/nodejs/node/pull/51797)
- \[[`b8e7a87aa9`](https://github.com/nodejs/node/commit/b8e7a87aa9)] - **http**: split set-cookie when using setHeaders (Marco Ippolito) [#51649](https://github.com/nodejs/node/pull/51649)
- \[[`682951af60`](https://github.com/nodejs/node/commit/682951af60)] - **http2**: close idle connections when allowHTTP1 is true (xsbchen) [#51569](https://github.com/nodejs/node/pull/51569)
- \[[`4b1c6839f4`](https://github.com/nodejs/node/commit/4b1c6839f4)] - **(SEMVER-MINOR)** **http2**: add h2 compat support for appendHeader (Tim Perry) [#51412](https://github.com/nodejs/node/pull/51412)
- \[[`d8aa2bac0b`](https://github.com/nodejs/node/commit/d8aa2bac0b)] - **(SEMVER-MINOR)** **http2**: add server handshake utility (snek) [#51172](https://github.com/nodejs/node/pull/51172)
- \[[`b9275d9039`](https://github.com/nodejs/node/commit/b9275d9039)] - **(SEMVER-MINOR)** **http2**: receive customsettings (Marten Richter) [#51323](https://github.com/nodejs/node/pull/51323)
- \[[`58e2015a03`](https://github.com/nodejs/node/commit/58e2015a03)] - **inspector**: add NodeRuntime.waitingForDebugger event (mary marchini) [#51560](https://github.com/nodejs/node/pull/51560)
- \[[`af32d433ee`](https://github.com/nodejs/node/commit/af32d433ee)] - **lib**: account for cwd access from snapshot serialization cb (Anna Henningsen) [#51901](https://github.com/nodejs/node/pull/51901)
- \[[`1edbc7d353`](https://github.com/nodejs/node/commit/1edbc7d353)] - **lib**: fix http client socket path (theanarkh) [#51900](https://github.com/nodejs/node/pull/51900)
- \[[`4dfc9e092e`](https://github.com/nodejs/node/commit/4dfc9e092e)] - **lib**: only build the ESM facade for builtins when they are needed (Joyee Cheung) [#51669](https://github.com/nodejs/node/pull/51669)
- \[[`5a2d2daad5`](https://github.com/nodejs/node/commit/5a2d2daad5)] - **(SEMVER-MINOR)** **lib**: move encodingsMap to internal/util (Joyee Cheung) [#51044](https://github.com/nodejs/node/pull/51044)
- \[[`eb1089ab17`](https://github.com/nodejs/node/commit/eb1089ab17)] - **lib**: do not access process.noDeprecation at build time (Joyee Cheung) [#51447](https://github.com/nodejs/node/pull/51447)
- \[[`614ca327c8`](https://github.com/nodejs/node/commit/614ca327c8)] - **lib**: add assertion for user ESM execution (Joyee Cheung) [#51748](https://github.com/nodejs/node/pull/51748)
- \[[`77ae03f723`](https://github.com/nodejs/node/commit/77ae03f723)] - **lib**: create global console properties at snapshot build time (Joyee Cheung) [#51700](https://github.com/nodejs/node/pull/51700)
- \[[`7f698f064e`](https://github.com/nodejs/node/commit/7f698f064e)] - **lib**: define FormData and fetch etc. in the built-in snapshot (Joyee Cheung) [#51598](https://github.com/nodejs/node/pull/51598)
- \[[`4b583bfcc5`](https://github.com/nodejs/node/commit/4b583bfcc5)] - **lib**: allow checking the test result from afterEach (Tim Stableford) [#51485](https://github.com/nodejs/node/pull/51485)
- \[[`ec60639cc0`](https://github.com/nodejs/node/commit/ec60639cc0)] - **lib**: remove unnecessary refreshHrtimeBuffer() (Joyee Cheung) [#51446](https://github.com/nodejs/node/pull/51446)
- \[[`8dc3f91eb4`](https://github.com/nodejs/node/commit/8dc3f91eb4)] - **lib,src**: extract sourceMappingURL from module (unbyte) [#51690](https://github.com/nodejs/node/pull/51690)
- \[[`84c71fa895`](https://github.com/nodejs/node/commit/84c71fa895)] - **meta**: move one or more collaborators to emeritus (Node.js GitHub Bot) [#51726](https://github.com/nodejs/node/pull/51726)
- \[[`e5c52a2a6b`](https://github.com/nodejs/node/commit/e5c52a2a6b)] - **meta**: bump codecov/codecov-action from 3.1.4 to 4.0.1 (dependabot\[bot]) [#51648](https://github.com/nodejs/node/pull/51648)
- \[[`16aa6e5341`](https://github.com/nodejs/node/commit/16aa6e5341)] - **meta**: bump actions/download-artifact from 4.1.0 to 4.1.1 (dependabot\[bot]) [#51644](https://github.com/nodejs/node/pull/51644)
- \[[`97825603ae`](https://github.com/nodejs/node/commit/97825603ae)] - **meta**: bump actions/upload-artifact from 4.0.0 to 4.3.0 (dependabot\[bot]) [#51643](https://github.com/nodejs/node/pull/51643)
- \[[`51f0d80876`](https://github.com/nodejs/node/commit/51f0d80876)] - **meta**: bump step-security/harden-runner from 2.6.1 to 2.7.0 (dependabot\[bot]) [#51641](https://github.com/nodejs/node/pull/51641)
- \[[`97e3cb5844`](https://github.com/nodejs/node/commit/97e3cb5844)] - **meta**: bump actions/cache from 3.3.2 to 4.0.0 (dependabot\[bot]) [#51640](https://github.com/nodejs/node/pull/51640)
- \[[`dcf5f28d68`](https://github.com/nodejs/node/commit/dcf5f28d68)] - **meta**: bump github/codeql-action from 3.22.12 to 3.23.2 (dependabot\[bot]) [#51639](https://github.com/nodejs/node/pull/51639)
- \[[`c4a28b2211`](https://github.com/nodejs/node/commit/c4a28b2211)] - **meta**: add .mailmap entry for lemire (Daniel Lemire) [#51600](https://github.com/nodejs/node/pull/51600)
- \[[`dbf44744ba`](https://github.com/nodejs/node/commit/dbf44744ba)] - **meta**: mark security-wg codeowner for deps folder (Marco Ippolito) [#51529](https://github.com/nodejs/node/pull/51529)
- \[[`16fea71d08`](https://github.com/nodejs/node/commit/16fea71d08)] - **meta**: move one or more collaborators to emeritus (Node.js GitHub Bot) [#51468](https://github.com/nodejs/node/pull/51468)
- \[[`015c4dcacf`](https://github.com/nodejs/node/commit/015c4dcacf)] - **meta**: move RaisinTen to emeritus and remove from strategic initiatives (Darshan Sen) [#51411](https://github.com/nodejs/node/pull/51411)
- \[[`e942dc1d0c`](https://github.com/nodejs/node/commit/e942dc1d0c)] - **meta**: add .temp and .lock tags to ignore (Rafael Gonzaga) [#51343](https://github.com/nodejs/node/pull/51343)
- \[[`595542e330`](https://github.com/nodejs/node/commit/595542e330)] - **meta**: bump actions/setup-python from 4.7.1 to 5.0.0 (dependabot\[bot]) [#51335](https://github.com/nodejs/node/pull/51335)
- \[[`6c3ba73d03`](https://github.com/nodejs/node/commit/6c3ba73d03)] - **meta**: bump actions/setup-node from 4.0.0 to 4.0.1 (dependabot\[bot]) [#51334](https://github.com/nodejs/node/pull/51334)
- \[[`e4f9f0a260`](https://github.com/nodejs/node/commit/e4f9f0a260)] - **meta**: bump github/codeql-action from 2.22.8 to 3.22.12 (dependabot\[bot]) [#51333](https://github.com/nodejs/node/pull/51333)
- \[[`77598c3a8e`](https://github.com/nodejs/node/commit/77598c3a8e)] - **meta**: bump actions/stale from 8.0.0 to 9.0.0 (dependabot\[bot]) [#51332](https://github.com/nodejs/node/pull/51332)
- \[[`22a11c32c0`](https://github.com/nodejs/node/commit/22a11c32c0)] - **meta**: move one or more collaborators to emeritus (Node.js GitHub Bot) [#51329](https://github.com/nodejs/node/pull/51329)
- \[[`391aeb1996`](https://github.com/nodejs/node/commit/391aeb1996)] - **module**: fix crash when built-in module export a `default` key (Antoine du Hamel) [#51481](https://github.com/nodejs/node/pull/51481)
- \[[`615b0ae307`](https://github.com/nodejs/node/commit/615b0ae307)] - **module**: fix `--preserve-symlinks-main` (per4uk) [#51312](https://github.com/nodejs/node/pull/51312)
- \[[`c6cc3ed3b4`](https://github.com/nodejs/node/commit/c6cc3ed3b4)] - **net**: fix connect crash when call destroy in lookup handler (theanarkh) [#51826](https://github.com/nodejs/node/pull/51826)
- \[[`63e0ceb48f`](https://github.com/nodejs/node/commit/63e0ceb48f)] - **net**: fix example IPv4 in dns docs (Aras Abbasi) [#51377](https://github.com/nodejs/node/pull/51377)
- \[[`bc6f33d8d1`](https://github.com/nodejs/node/commit/bc6f33d8d1)] - **node-api**: make napi_get_buffer_info check if passed buffer is valid (Janrupf) [#51571](https://github.com/nodejs/node/pull/51571)
- \[[`5b94ff44ec`](https://github.com/nodejs/node/commit/5b94ff44ec)] - **node-api**: move NAPI_EXPERIMENTAL definition to gyp file (Gabriel Schulhof) [#51254](https://github.com/nodejs/node/pull/51254)
- \[[`66c11f31c3`](https://github.com/nodejs/node/commit/66c11f31c3)] - **node-api**: optimize napi_set_property for perf (Mert Can Altın) [#50282](https://github.com/nodejs/node/pull/50282)
- \[[`cb621863c6`](https://github.com/nodejs/node/commit/cb621863c6)] - **perf_hooks**: performance milestone time origin timestamp improvement (IlyasShabi) [#51713](https://github.com/nodejs/node/pull/51713)
- \[[`4d06d80675`](https://github.com/nodejs/node/commit/4d06d80675)] - **quic**: various additional cleanups, fixes in Endpoint (James M Snell) [#51310](https://github.com/nodejs/node/pull/51310)
- \[[`3e579ab2fd`](https://github.com/nodejs/node/commit/3e579ab2fd)] - **repl**: fix `NO_COLORS` env var is ignored (Moshe Atlow) [#51568](https://github.com/nodejs/node/pull/51568)
- \[[`7ceb6d6700`](https://github.com/nodejs/node/commit/7ceb6d6700)] - **sea**: update stability index (Joyee Cheung) [#51774](https://github.com/nodejs/node/pull/51774)
- \[[`e8d9065262`](https://github.com/nodejs/node/commit/e8d9065262)] - **(SEMVER-MINOR)** **sea**: support sea.getRawAsset() (Joyee Cheung) [#50960](https://github.com/nodejs/node/pull/50960)
- \[[`cea5295c16`](https://github.com/nodejs/node/commit/cea5295c16)] - **(SEMVER-MINOR)** **sea**: support embedding assets (Joyee Cheung) [#50960](https://github.com/nodejs/node/pull/50960)
- \[[`7543e774bd`](https://github.com/nodejs/node/commit/7543e774bd)] - **src**: simplify direct queries of env vars in C++ land (Joyee Cheung) [#51829](https://github.com/nodejs/node/pull/51829)
- \[[`8f10543c58`](https://github.com/nodejs/node/commit/8f10543c58)] - **src**: stop the profiler and the inspector before snapshot serialization (Joyee Cheung) [#51815](https://github.com/nodejs/node/pull/51815)
- \[[`ccc76bbfd7`](https://github.com/nodejs/node/commit/ccc76bbfd7)] - **src**: simplify embedder entry point execution (Joyee Cheung) [#51557](https://github.com/nodejs/node/pull/51557)
- \[[`0c41210865`](https://github.com/nodejs/node/commit/0c41210865)] - **src**: compile code eagerly in snapshot builder (Joyee Cheung) [#51672](https://github.com/nodejs/node/pull/51672)
- \[[`2a46dc7b86`](https://github.com/nodejs/node/commit/2a46dc7b86)] - **src**: check empty before accessing string (Cheng Zhao) [#51665](https://github.com/nodejs/node/pull/51665)
- \[[`47186fbad5`](https://github.com/nodejs/node/commit/47186fbad5)] - **(SEMVER-MINOR)** **src**: print string content better in BlobDeserializer (Joyee Cheung) [#50960](https://github.com/nodejs/node/pull/50960)
- \[[`6603d32ce3`](https://github.com/nodejs/node/commit/6603d32ce3)] - **src**: fix vm bug for configurable globalThis (F. Hinkelmann) [#51602](https://github.com/nodejs/node/pull/51602)
- \[[`c7912c3d5a`](https://github.com/nodejs/node/commit/c7912c3d5a)] - **(SEMVER-MINOR)** **src**: support multi-line values for .env file (IlyasShabi) [#51289](https://github.com/nodejs/node/pull/51289)
- \[[`b8ae5c27c6`](https://github.com/nodejs/node/commit/b8ae5c27c6)] - **(SEMVER-MINOR)** **src**: add `process.loadEnvFile` and `util.parseEnv` (Yagiz Nizipli) [#51476](https://github.com/nodejs/node/pull/51476)
- \[[`e3a63843f2`](https://github.com/nodejs/node/commit/e3a63843f2)] - **src**: terminate correctly double-quote in env variable (Marco Ippolito) [#51510](https://github.com/nodejs/node/pull/51510)
- \[[`119e045053`](https://github.com/nodejs/node/commit/119e045053)] - **(SEMVER-MINOR)** **src**: do not coerce dotenv paths (Tobias Nießen) [#51425](https://github.com/nodejs/node/pull/51425)
- \[[`b271cc5b16`](https://github.com/nodejs/node/commit/b271cc5b16)] - **src**: refactor `GetCreationContext` calls (Jungku Lee) [#51367](https://github.com/nodejs/node/pull/51367)
- \[[`36e42aa570`](https://github.com/nodejs/node/commit/36e42aa570)] - **src**: do not read string out of bounds (Cheng Zhao) [#51358](https://github.com/nodejs/node/pull/51358)
- \[[`8ea7d79082`](https://github.com/nodejs/node/commit/8ea7d79082)] - **src**: avoid shadowed string in fs_permission (Shelley Vohr) [#51123](https://github.com/nodejs/node/pull/51123)
- \[[`5b06af7814`](https://github.com/nodejs/node/commit/5b06af7814)] - **stream**: fix eventNames() to not return not defined events (IlyasShabi) [#51331](https://github.com/nodejs/node/pull/51331)
- \[[`438b7fd049`](https://github.com/nodejs/node/commit/438b7fd049)] - **stream**: fix cloned webstreams not being unref correctly (tsctx) [#51526](https://github.com/nodejs/node/pull/51526)
- \[[`9ab353af00`](https://github.com/nodejs/node/commit/9ab353af00)] - **(SEMVER-MINOR)** **stream**: implement `min` option for `ReadableStreamBYOBReader.read` (Mattias Buelens) [#50888](https://github.com/nodejs/node/pull/50888)
- \[[`17ab5ae570`](https://github.com/nodejs/node/commit/17ab5ae570)] - **test**: fix unreliable assumption in js-native-api/test_cannot_run_js (Joyee Cheung) [#51898](https://github.com/nodejs/node/pull/51898)
- \[[`e2c51385c7`](https://github.com/nodejs/node/commit/e2c51385c7)] - **test**: test surrogate pair filenames on windows (Mert Can Altın) [#51800](https://github.com/nodejs/node/pull/51800)
- \[[`049e5f5e8c`](https://github.com/nodejs/node/commit/049e5f5e8c)] - **test**: deflake test-http2-large-write-multiple-requests (Joyee Cheung) [#51863](https://github.com/nodejs/node/pull/51863)
- \[[`2bf03ee678`](https://github.com/nodejs/node/commit/2bf03ee678)] - **test**: fix test-debugger-profile for coverage generation (Joyee Cheung) [#51816](https://github.com/nodejs/node/pull/51816)
- \[[`d47a95f3b1`](https://github.com/nodejs/node/commit/d47a95f3b1)] - **test**: fix test-bootstrap-modules for coverage generation (Joyee Cheung) [#51816](https://github.com/nodejs/node/pull/51816)
- \[[`c0918f082f`](https://github.com/nodejs/node/commit/c0918f082f)] - **test**: ensure delay in recursive fs watch tests (Joyee Cheung) [#51842](https://github.com/nodejs/node/pull/51842)
- \[[`1f6551dda2`](https://github.com/nodejs/node/commit/1f6551dda2)] - **test**: fix test-child-process-fork-net (Joyee Cheung) [#51841](https://github.com/nodejs/node/pull/51841)
- \[[`f845a16c58`](https://github.com/nodejs/node/commit/f845a16c58)] - **test**: split wasi tests (Joyee Cheung) [#51836](https://github.com/nodejs/node/pull/51836)
- \[[`275cea0fdb`](https://github.com/nodejs/node/commit/275cea0fdb)] - **test**: mark test-wasi as flaky on Windows on ARM (Joyee Cheung) [#51834](https://github.com/nodejs/node/pull/51834)
- \[[`2fb620cd00`](https://github.com/nodejs/node/commit/2fb620cd00)] - **test**: remove test-fs-stat-bigint flaky designation (Luigi Pinca) [#51735](https://github.com/nodejs/node/pull/51735)
- \[[`b046712e86`](https://github.com/nodejs/node/commit/b046712e86)] - **test**: skip test-http-correct-hostname on loong64 (Shi Pujin) [#51663](https://github.com/nodejs/node/pull/51663)
- \[[`83f581d4c1`](https://github.com/nodejs/node/commit/83f581d4c1)] - **test**: increase platform timeout zlib-brotli-16gb (Rafael Gonzaga) [#51792](https://github.com/nodejs/node/pull/51792)
- \[[`ea08350c83`](https://github.com/nodejs/node/commit/ea08350c83)] - **test**: remove test-cli-node-options flaky designation (Luigi Pinca) [#51716](https://github.com/nodejs/node/pull/51716)
- \[[`9d3a014f67`](https://github.com/nodejs/node/commit/9d3a014f67)] - **test**: remove test-domain-error-types flaky designation (Luigi Pinca) [#51717](https://github.com/nodejs/node/pull/51717)
- \[[`d7563a5448`](https://github.com/nodejs/node/commit/d7563a5448)] - **test**: fix `internet/test-inspector-help-page` (Richard Lau) [#51693](https://github.com/nodejs/node/pull/51693)
- \[[`e9299255ca`](https://github.com/nodejs/node/commit/e9299255ca)] - **test**: remove duplicate entry for flaky test (Luigi Pinca) [#51654](https://github.com/nodejs/node/pull/51654)
- \[[`a8ac337250`](https://github.com/nodejs/node/commit/a8ac337250)] - **test**: remove test-crypto-keygen flaky designation (Luigi Pinca) [#51567](https://github.com/nodejs/node/pull/51567)
- \[[`c820166e4b`](https://github.com/nodejs/node/commit/c820166e4b)] - **test**: remove test-fs-rmdir-recursive flaky designation (Luigi Pinca) [#51566](https://github.com/nodejs/node/pull/51566)
- \[[`db88bf185f`](https://github.com/nodejs/node/commit/db88bf185f)] - **test**: remove common.expectsError calls for asserts (Paulo Chaves) [#51504](https://github.com/nodejs/node/pull/51504)
- \[[`fc0c1309b2`](https://github.com/nodejs/node/commit/fc0c1309b2)] - **test**: mark test-http2-large-file as flaky (Michaël Zasso) [#51549](https://github.com/nodejs/node/pull/51549)
- \[[`c88f0b6db9`](https://github.com/nodejs/node/commit/c88f0b6db9)] - **test**: use checkIfCollectableByCounting in SourceTextModule leak test (Joyee Cheung) [#51512](https://github.com/nodejs/node/pull/51512)
- \[[`d4d07f4a44`](https://github.com/nodejs/node/commit/d4d07f4a44)] - **test**: remove test-file-write-stream4 flaky designation (Luigi Pinca) [#51472](https://github.com/nodejs/node/pull/51472)
- \[[`7420a7d2f8`](https://github.com/nodejs/node/commit/7420a7d2f8)] - **test**: add URL tests to fs-write (Rafael Gonzaga) [#51352](https://github.com/nodejs/node/pull/51352)
- \[[`28c2bf3e42`](https://github.com/nodejs/node/commit/28c2bf3e42)] - **test**: remove unneeded common.expectsError for asserts (Andrés Morelos) [#51353](https://github.com/nodejs/node/pull/51353)
- \[[`9dfb36fbe5`](https://github.com/nodejs/node/commit/9dfb36fbe5)] - **test,crypto**: update WebCryptoAPI WPT (Filip Skokan) [#51533](https://github.com/nodejs/node/pull/51533)
- \[[`e4d4bc6f9a`](https://github.com/nodejs/node/commit/e4d4bc6f9a)] - **test_runner**: serialize 'expected' and 'actual' in isolation (Malthe Borch) [#51851](https://github.com/nodejs/node/pull/51851)
- \[[`5f9491237c`](https://github.com/nodejs/node/commit/5f9491237c)] - **test_runner**: add ref methods to mocked timers (Marco Ippolito) [#51809](https://github.com/nodejs/node/pull/51809)
- \[[`af5875c6e8`](https://github.com/nodejs/node/commit/af5875c6e8)] - **test_runner**: check if timeout was cleared by own callback (Ben Richeson) [#51673](https://github.com/nodejs/node/pull/51673)
- \[[`e0789fbc8a`](https://github.com/nodejs/node/commit/e0789fbc8a)] - **test_runner**: do not invoke after hook when test is empty (Marco Ippolito) [#51389](https://github.com/nodejs/node/pull/51389)
- \[[`27f8549903`](https://github.com/nodejs/node/commit/27f8549903)] - **tools**: fix installing node with shared mode (Cheng Zhao) [#51910](https://github.com/nodejs/node/pull/51910)
- \[[`71a809bd43`](https://github.com/nodejs/node/commit/71a809bd43)] - **tools**: update eslint to 8.57.0 (Node.js GitHub Bot) [#51867](https://github.com/nodejs/node/pull/51867)
- \[[`20effe638a`](https://github.com/nodejs/node/commit/20effe638a)] - **tools**: update lint-md-dependencies to rollup\@4.12.0 (Node.js GitHub Bot) [#51795](https://github.com/nodejs/node/pull/51795)
- \[[`6c0a3b9c0d`](https://github.com/nodejs/node/commit/6c0a3b9c0d)] - **tools**: update lint-md-dependencies to rollup\@4.12.0 (Node.js GitHub Bot) [#51795](https://github.com/nodejs/node/pull/51795)
- \[[`03f926ddca`](https://github.com/nodejs/node/commit/03f926ddca)] - **tools**: fix missing \[\[fallthrough]] in js2c (Cheng Zhao) [#51845](https://github.com/nodejs/node/pull/51845)
- \[[`b502be1d09`](https://github.com/nodejs/node/commit/b502be1d09)] - **tools**: disable automated libuv updates (Rafael Gonzaga) [#51775](https://github.com/nodejs/node/pull/51775)
- \[[`787fb32557`](https://github.com/nodejs/node/commit/787fb32557)] - **tools**: fix update-icu.sh (Michaël Zasso) [#51723](https://github.com/nodejs/node/pull/51723)
- \[[`ba22c614c1`](https://github.com/nodejs/node/commit/ba22c614c1)] - **tools**: update lint-md-dependencies to rollup\@4.10.0 (Node.js GitHub Bot) [#51720](https://github.com/nodejs/node/pull/51720)
- \[[`751821fa21`](https://github.com/nodejs/node/commit/751821fa21)] - **tools**: update github_reporter to 1.6.0 (Node.js GitHub Bot) [#51658](https://github.com/nodejs/node/pull/51658)
- \[[`5fe493d0e4`](https://github.com/nodejs/node/commit/5fe493d0e4)] - **tools**: run `build-windows` workflow only on source changes (Antoine du Hamel) [#51596](https://github.com/nodejs/node/pull/51596)
- \[[`e1b9655bdc`](https://github.com/nodejs/node/commit/e1b9655bdc)] - **tools**: update lint-md-dependencies to rollup\@4.9.6 (Node.js GitHub Bot) [#51583](https://github.com/nodejs/node/pull/51583)
- \[[`d8e1058f18`](https://github.com/nodejs/node/commit/d8e1058f18)] - **tools**: fix loong64 build (Shi Pujin) [#51401](https://github.com/nodejs/node/pull/51401)
- \[[`e0eeebc960`](https://github.com/nodejs/node/commit/e0eeebc960)] - **tools**: set normalizeTD text default to empty string (Marco Ippolito) [#51543](https://github.com/nodejs/node/pull/51543)
- \[[`81fd7d1ca9`](https://github.com/nodejs/node/commit/81fd7d1ca9)] - **tools**: limit parallelism with ninja in V8 builds (Richard Lau) [#51473](https://github.com/nodejs/node/pull/51473)
- \[[`e88a301e98`](https://github.com/nodejs/node/commit/e88a301e98)] - **tools**: do not pass invalid flag to C compiler (Michaël Zasso) [#51409](https://github.com/nodejs/node/pull/51409)
- \[[`129d3b3293`](https://github.com/nodejs/node/commit/129d3b3293)] - **tools**: update lint-md-dependencies to rollup\@4.9.5 (Node.js GitHub Bot) [#51460](https://github.com/nodejs/node/pull/51460)
- \[[`f3845de204`](https://github.com/nodejs/node/commit/f3845de204)] - **tools**: update inspector_protocol to 83b1154 (Kohei Ueno) [#51309](https://github.com/nodejs/node/pull/51309)
- \[[`58901d08fd`](https://github.com/nodejs/node/commit/58901d08fd)] - **tools**: update github_reporter to 1.5.4 (Node.js GitHub Bot) [#51395](https://github.com/nodejs/node/pull/51395)
- \[[`29492e2c88`](https://github.com/nodejs/node/commit/29492e2c88)] - **tools**: fix version parsing in brotli update script (Richard Lau) [#51373](https://github.com/nodejs/node/pull/51373)
- \[[`17593d95ba`](https://github.com/nodejs/node/commit/17593d95ba)] - **tools**: update lint-md-dependencies to rollup\@4.9.4 (Node.js GitHub Bot) [#51396](https://github.com/nodejs/node/pull/51396)
- \[[`35f33d3a31`](https://github.com/nodejs/node/commit/35f33d3a31)] - **tools**: remove openssl v1 update script (Marco Ippolito) [#51378](https://github.com/nodejs/node/pull/51378)
- \[[`83b3aa838b`](https://github.com/nodejs/node/commit/83b3aa838b)] - **tools**: remove deprecated python api (Alex Yang) [#49731](https://github.com/nodejs/node/pull/49731)
- \[[`adb2c36f0f`](https://github.com/nodejs/node/commit/adb2c36f0f)] - **typings**: lib/internal/vm.js (Geoffrey Booth) [#50112](https://github.com/nodejs/node/pull/50112)
- \[[`407341e25c`](https://github.com/nodejs/node/commit/407341e25c)] - **url**: don't update URL immediately on update to URLSearchParams (Matt Cowley) [#51520](https://github.com/nodejs/node/pull/51520)
- \[[`88e08bbe80`](https://github.com/nodejs/node/commit/88e08bbe80)] - **(SEMVER-MINOR)** **util**: add styleText API to text formatting (Rafael Gonzaga) [#51850](https://github.com/nodejs/node/pull/51850)
- \[[`ba444a949d`](https://github.com/nodejs/node/commit/ba444a949d)] - **vm**: implement isContext() directly in JS land with private symbol (Joyee Cheung) [#51685](https://github.com/nodejs/node/pull/51685)
- \[[`4c508269cd`](https://github.com/nodejs/node/commit/4c508269cd)] - **(SEMVER-MINOR)** **vm**: support using the default loader to handle dynamic import() (Joyee Cheung) [#51244](https://github.com/nodejs/node/pull/51244)

Windows 32-bit Installer: https://nodejs.org/dist/v21.7.0/node-v21.7.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v21.7.0/node-v21.7.0-x64.msi \
Windows ARM 64-bit Installer: https://nodejs.org/dist/v21.7.0/node-v21.7.0-arm64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v21.7.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v21.7.0/win-x64/node.exe \
Windows ARM 64-bit Binary: https://nodejs.org/dist/v21.7.0/win-arm64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v21.7.0/node-v21.7.0.pkg \
macOS Apple Silicon 64-bit Binary: https://nodejs.org/dist/v21.7.0/node-v21.7.0-darwin-arm64.tar.gz \
macOS Intel 64-bit Binary: https://nodejs.org/dist/v21.7.0/node-v21.7.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v21.7.0/node-v21.7.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v21.7.0/node-v21.7.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v21.7.0/node-v21.7.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v21.7.0/node-v21.7.0-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v21.7.0/node-v21.7.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v21.7.0/node-v21.7.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v21.7.0/node-v21.7.0.tar.gz \
Other release files: https://nodejs.org/dist/v21.7.0/ \
Documentation: https://nodejs.org/docs/v21.7.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

c31d5b60c4a284c6855bd468d4aae4436a16b351362b2971d3c0db2a471d3f24  node-v21.7.0-aix-ppc64.tar.gz
7d7dc37aa30b6dbb52d698d01cfed1d99056c82396eadd41a49fc55a873c423d  node-v21.7.0-arm64.msi
f48ad51cf3c2814bbf61c8c26efd810e5e22dcac980786fd7ac5b54365233d2c  node-v21.7.0-darwin-arm64.tar.gz
0805239d8a7402dae49e0033b7ad8208fed498dbeee9a3194863e52f6f3c6d7f  node-v21.7.0-darwin-arm64.tar.xz
3f81adca80b523b413e26f03f855f4a2ae52d9af20f0cda2e259dd26a0608607  node-v21.7.0-darwin-x64.tar.gz
6a755416292854f2be38e74704ccf09edeba247718e9f047f5e1939b0dba17bd  node-v21.7.0-darwin-x64.tar.xz
628d9e4f866e3828b77ce812dc99f33d3e7c673d0c499f13eadff6fa6ccb4383  node-v21.7.0-headers.tar.gz
627d6552d2120660a51f74fff0d40573f4a35d8545462250d30592ce0ba4eec7  node-v21.7.0-headers.tar.xz
520a3e5c83a05a782b1f4959f150c2fdc03e2ea056e855ef6bbb74f6ccf7aa7d  node-v21.7.0-linux-arm64.tar.gz
73ce1e4e956532e0916fc7014f5b649573bd2b5870fef5cfc26cc42f58358ae7  node-v21.7.0-linux-arm64.tar.xz
723abb32135ad4baa6e9671447a72f5c9a5bfc681fc540b0e4864e965171b6ed  node-v21.7.0-linux-armv7l.tar.gz
8a367a3bf667f0bb3abb9e8121326911d47a31886419ad052d5a52d8c6531d9d  node-v21.7.0-linux-armv7l.tar.xz
c2290cb35b11ee2b0f0ae34ad3c8372652688ff2dc3d9a89ada46c2b84ea5dda  node-v21.7.0-linux-ppc64le.tar.gz
b85348211a4d195de2f850a17cdec77aedc8fc1c402864b2bc3501608e6c9c47  node-v21.7.0-linux-ppc64le.tar.xz
90b8678ed113950613edeae5eaf298cf795c72005fac6ffd9b7fbb90ddd86738  node-v21.7.0-linux-s390x.tar.gz
99a09f4c790f3210a6d26032bf69713ba199cf2e73af43e04b1b1d9bd1c8db76  node-v21.7.0-linux-s390x.tar.xz
0fce039e2b6af00766492127a49f959ae92ed22fede4c49e9a8c2543aadbd6e2  node-v21.7.0-linux-x64.tar.gz
68510c3851133a21c6a6f9940e58c5bc8fed39f1d91a08e34c5070dd0615fef1  node-v21.7.0-linux-x64.tar.xz
d680d5c3d0b2476a97d11b30cbbdaf1d7f92ffd1cc89e5c640782a6b52480666  node-v21.7.0-win-arm64.7z
11b11b9a3f2db7b5076cf16655e05cd63dc3d8843cd4836ecb12e11315f03441  node-v21.7.0-win-arm64.zip
31c8b4721f37e30ca8e2131a4cb848fc7347f67bf87618e82959b58481f17bc4  node-v21.7.0-win-x64.7z
204de88f4073b08ae3dbe4c412b071eee565fc681e163be205d5cc88065f0322  node-v21.7.0-win-x64.zip
b17ef0c5557e61610774cae5beb0f877699ab419c4672e9c6e3bb3da3d571ed1  node-v21.7.0-win-x86.7z
6aba3fe2258d5c0c40a89e81dfe90113a67489f2a67fd05b7f216b63b4c7bb02  node-v21.7.0-win-x86.zip
512945cf8816e1e906143ea2ee6816f8744a3d114ea38f3540c3ebe685fe3e3a  node-v21.7.0-x64.msi
4bedb6069c94a71fd6f0b8fbea280468d5ecdcf209eef6da1a45808e8b15cba6  node-v21.7.0-x86.msi
ccac99782e587c6090b6ad82979210fa0c352322636a6cf290d37eb41152d0b5  node-v21.7.0.pkg
26d6b600e1076f132d4175a90ddc1a709263e75d81967300aa1ffbd86103b991  node-v21.7.0.tar.gz
e41eefe1e59624ee7f312c38f8f7dfc11595641acb2293d21176f03d2763e9d4  node-v21.7.0.tar.xz
25511d1e05d7d0b049945c5ef1cf2a4daa5d6ad16692ccd2c1399142a1c57a65  win-arm64/node.exe
7920932f7be355dbf4568492ab7d104fc059f689ba1a46ac0d6568884c8d201a  win-arm64/node.lib
40c423a2b126fc5b6858f8617f0f8537fd8f8d2fa73a5c918607f3ccd386f8c9  win-arm64/node_pdb.7z
dec9eaa91a431ea0f3c243605f0556dbe6459df5c04c10df7935d678a6b3fca4  win-arm64/node_pdb.zip
c486fe72a3663379105538e886ef9d2deacad1deaa64b338e570cb086be592d3  win-x64/node.exe
96d09c2055c2f252122c86b65d2aabd5f90b1a075844f24bf8bcdbab05baf53e  win-x64/node.lib
08990dd6bcce80710d59ef76cd74ab98b5bed36b0d2584ca3acbc029f92db4fc  win-x64/node_pdb.7z
1a27a25c92f6339b3aa77588063cca537af389aee26bfdf1d0ef505d790e63a3  win-x64/node_pdb.zip
4aaa5b3a95ee4ab932a80b9708c31662a9c4a99d19fea7cb1f7b0ff79d8399ed  win-x86/node.exe
6e2502e84c3a0e2da643f6399b59381ade5b525f544a5bcabae923188b8f9998  win-x86/node.lib
d0cd5494364039f558c76d4fc7a1db69739149873e10a5200fb9e2a0ab12fe10  win-x86/node_pdb.7z
354031f3f9576733ebeeccbcafcc691c8326427153a48978ff5cd6f2c8ef5d36  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQGzBAEBCAAdFiEEiQwI24V5Fi/uDfnbi+q0389VXvQFAmXouAIACgkQi+q0389V
XvRp+wv+IPHjBUmVC6YzAxFhRD4GHVUgjckfSbP2jH/acre1mYgm9LJ//7l2GaJy
oEOO85WaHgaKCHCdv9GBc3dDbbt1n9J2IGmBqcdE8e9cRko5qhBoVUvW7p7Ki7ci
nAq5DS3YkpWAocsY/k+LyR0Ky8mW466ARAucTp9kuZmxB2FW53B0bYK57++1qGuo
tr9kJPoGYQB0cUiTSwTaMbOIdl/4CL+a9J7mIrpaDVW5g3PnNy5y1vgDvtuU7Qcn
uEucciBlOn0Ib4mBnky+NX1ThL9WNwLjaivxdioFgc0E4sMwf0CjF3vMUuEvI8qi
PJ5lYndsHI4fdh1SbcgoFNZzTkMZbTr9xcZIGLzLkMX8r+ztLTiFtiLIUSQq0jgm
fqKQghuDN2SVi7WW4KAa7K1285zmV7L27N9mnNWH4ujTqCW73Wdo2XkG/TwM3yEC
5o+YookAV6RHT1X6RPJ8rQaC0BrBgpm/MQH1kvH4vUyF2HRVZ2ZgEYorvKtOwf9D
f7v3IC9J
=/YNz
-----END PGP SIGNATURE-----
```
