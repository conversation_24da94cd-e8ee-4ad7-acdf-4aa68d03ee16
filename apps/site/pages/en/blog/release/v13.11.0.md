---
date: '2020-03-12T03:58:16.427Z'
category: release
title: Node v13.11.0 (Current)
layout: blog-post
author: <PERSON><PERSON>
---

### Notable Changes

- **async_hooks**: add sync enterWith to ALS (<PERSON>) [#31945](https://github.com/nodejs/node/pull/31945)
- **cli**: allow --jitless V8 flag in NODE_OPTIONS (<PERSON>) [#32100](https://github.com/nodejs/node/pull/32100)
- **fs**: return first folder made by mkdir recursive (<PERSON>) [#31530](https://github.com/nodejs/node/pull/31530)
- **n-api**: define release 6 (<PERSON>) [#32058](https://github.com/nodejs/node/pull/32058)
- **os**: create a getter for kernel version (<PERSON>) [#31732](https://github.com/nodejs/node/pull/31732)
- **wasi**: add returnOnExit option (<PERSON>) [#32101](https://github.com/nodejs/node/pull/32101)

### Commits

- [[`478f1e7e13`](https://github.com/nodejs/node/commit/478f1e7e13)] - **async_hooks**: avoid resource reuse by FileHandle (Gerhard Stoebich) [#31972](https://github.com/nodejs/node/pull/31972)
- [[`4d5981be96`](https://github.com/nodejs/node/commit/4d5981be96)] - **(SEMVER-MINOR)** **async_hooks**: add sync enterWith to ALS (Stephen Belanger) [#31945](https://github.com/nodejs/node/pull/31945)
- [[`3befe80c4f`](https://github.com/nodejs/node/commit/3befe80c4f)] - **async_hooks**: fix ctx loss after nested ALS calls (Andrey Pechkurov) [#32085](https://github.com/nodejs/node/pull/32085)
- [[`ddb882439f`](https://github.com/nodejs/node/commit/ddb882439f)] - **benchmark**: remove special test entries (Ruben Bridgewater) [#31755](https://github.com/nodejs/node/pull/31755)
- [[`5d92cec12d`](https://github.com/nodejs/node/commit/5d92cec12d)] - **benchmark**: add `test` and `all` options and improve errors" (Ruben Bridgewater) [#31755](https://github.com/nodejs/node/pull/31755)
- [[`e11f38cbab`](https://github.com/nodejs/node/commit/e11f38cbab)] - **benchmark**: refactor helper into a class (Ruben Bridgewater) [#31755](https://github.com/nodejs/node/pull/31755)
- [[`31ec44302a`](https://github.com/nodejs/node/commit/31ec44302a)] - **benchmark**: remove problematic tls params (Brian White) [#31816](https://github.com/nodejs/node/pull/31816)
- [[`079bb31b29`](https://github.com/nodejs/node/commit/079bb31b29)] - **build**: remove empty line on node.gyp file (Juan José Arboleda) [#31952](https://github.com/nodejs/node/pull/31952)
- [[`fe34da84de`](https://github.com/nodejs/node/commit/fe34da84de)] - **build**: add mjs extension to lint-js (Nick Schonning) [#32145](https://github.com/nodejs/node/pull/32145)
- [[`d66daa5661`](https://github.com/nodejs/node/commit/d66daa5661)] - **build**: support android build on ndk version equal or above 23 (forfun414) [#31521](https://github.com/nodejs/node/pull/31521)
- [[`3c06316679`](https://github.com/nodejs/node/commit/3c06316679)] - **build**: workaround for gclient python3 issues (Matheus Marchini) [#32140](https://github.com/nodejs/node/pull/32140)
- [[`64135249e5`](https://github.com/nodejs/node/commit/64135249e5)] - **build**: allow use of system-installed brotli (André Draszik) [#32046](https://github.com/nodejs/node/pull/32046)
- [[`f07d423d16`](https://github.com/nodejs/node/commit/f07d423d16)] - **build**: allow passing multiple libs to pkg_config (André Draszik) [#32046](https://github.com/nodejs/node/pull/32046)
- [[`7c739aa386`](https://github.com/nodejs/node/commit/7c739aa386)] - **build**: enable backtrace when V8 is built for PPC and S390x (Michaël Zasso) [#32113](https://github.com/nodejs/node/pull/32113)
- [[`e1347b411a`](https://github.com/nodejs/node/commit/e1347b411a)] - **cli**: allow --jitless V8 flag in NODE_OPTIONS (Andrew Neitsch) [#32100](https://github.com/nodejs/node/pull/32100)
- [[`ce686c03ae`](https://github.com/nodejs/node/commit/ce686c03ae)] - **crypto**: optimize sign.update() and verify.update() (Ben Noordhuis) [#31767](https://github.com/nodejs/node/pull/31767)
- [[`a727b13343`](https://github.com/nodejs/node/commit/a727b13343)] - **crypto**: make update(buf, enc) ignore encoding (Ben Noordhuis) [#31766](https://github.com/nodejs/node/pull/31766)
- [[`893e9183b5`](https://github.com/nodejs/node/commit/893e9183b5)] - **doc**: include the error type in the request.resolve doc (Joe Pea) [#32152](https://github.com/nodejs/node/pull/32152)
- [[`af73ed632c`](https://github.com/nodejs/node/commit/af73ed632c)] - **doc**: clear up child_process command resolution (Denys Otrishko) [#32091](https://github.com/nodejs/node/pull/32091)
- [[`fa78aa4a60`](https://github.com/nodejs/node/commit/fa78aa4a60)] - **doc**: clarify windows specific behaviour (Sam Roberts) [#32079](https://github.com/nodejs/node/pull/32079)
- [[`5bc51612b9`](https://github.com/nodejs/node/commit/5bc51612b9)] - **doc**: improve Buffer documentation (Anna Henningsen) [#32086](https://github.com/nodejs/node/pull/32086)
- [[`35bea0798e`](https://github.com/nodejs/node/commit/35bea0798e)] - **doc**: add support encoding link on string_decoder.md (himself65) [#31911](https://github.com/nodejs/node/pull/31911)
- [[`3fa57ee0c2`](https://github.com/nodejs/node/commit/3fa57ee0c2)] - **doc**: add entry for `AsyncHook` class (Harshitha KP) [#31865](https://github.com/nodejs/node/pull/31865)
- [[`38329bd438`](https://github.com/nodejs/node/commit/38329bd438)] - **doc**: prevent tables from shrinking page (David Gilbertson) [#31859](https://github.com/nodejs/node/pull/31859)
- [[`bc1e3575d4`](https://github.com/nodejs/node/commit/bc1e3575d4)] - **doc**: change worker.takeHeapSnapshot to getHeapSnapshot (Gerhard Stoebich) [#32061](https://github.com/nodejs/node/pull/32061)
- [[`7de4dfba79`](https://github.com/nodejs/node/commit/7de4dfba79)] - **doc**: remove personal pronoun usage in policy.md (Rich Trott) [#32142](https://github.com/nodejs/node/pull/32142)
- [[`618b389b6a`](https://github.com/nodejs/node/commit/618b389b6a)] - **doc**: remove personal pronoun usage in fs.md (Rich Trott) [#32142](https://github.com/nodejs/node/pull/32142)
- [[`fa99fb2eac`](https://github.com/nodejs/node/commit/fa99fb2eac)] - **doc**: remove personal pronoun usage in errors.md (Rich Trott) [#32142](https://github.com/nodejs/node/pull/32142)
- [[`2d39369ee5`](https://github.com/nodejs/node/commit/2d39369ee5)] - **doc**: remove personal pronoun usage in addons.md (Rich Trott) [#32142](https://github.com/nodejs/node/pull/32142)
- [[`02ebc81e94`](https://github.com/nodejs/node/commit/02ebc81e94)] - **doc**: revise tools/icu/README.md (Rich Trott) [#32136](https://github.com/nodejs/node/pull/32136)
- [[`50c5eb49ab`](https://github.com/nodejs/node/commit/50c5eb49ab)] - **doc**: link setRawMode() from signal docs (Anna Henningsen) [#32088](https://github.com/nodejs/node/pull/32088)
- [[`97965f518c`](https://github.com/nodejs/node/commit/97965f518c)] - **doc**: document self-referencing a package name (Gil Tayar) [#31680](https://github.com/nodejs/node/pull/31680)
- [[`a79b8fa6f8`](https://github.com/nodejs/node/commit/a79b8fa6f8)] - **doc**: document fs.watchFile() bigint option (Colin Ihrig) [#32128](https://github.com/nodejs/node/pull/32128)
- [[`2e5f81f69c`](https://github.com/nodejs/node/commit/2e5f81f69c)] - **doc**: fix broken links in benchmark README (Rich Trott) [#32121](https://github.com/nodejs/node/pull/32121)
- [[`50094de274`](https://github.com/nodejs/node/commit/50094de274)] - **doc**: remove em dashes (Rich Trott) [#32080](https://github.com/nodejs/node/pull/32080)
- [[`5f12595e00`](https://github.com/nodejs/node/commit/5f12595e00)] - **doc**: update email address in authors (Yael Hermon) [#32026](https://github.com/nodejs/node/pull/32026)
- [[`77e5b509a9`](https://github.com/nodejs/node/commit/77e5b509a9)] - **doc,test**: add server.timeout property to http2 public API (Andrey Pechkurov) [#31693](https://github.com/nodejs/node/pull/31693)
- [[`4c2e4d1747`](https://github.com/nodejs/node/commit/4c2e4d1747)] - **esm**: remove unused parameter on module.instantiate (himself65) [#32147](https://github.com/nodejs/node/pull/32147)
- [[`55486bceb9`](https://github.com/nodejs/node/commit/55486bceb9)] - **events**: fix removeListener for Symbols (zfx) [#31847](https://github.com/nodejs/node/pull/31847)
- [[`94f3eed229`](https://github.com/nodejs/node/commit/94f3eed229)] - **(SEMVER-MINOR)** **fs**: make fs.read params optional (Lucas Holmquist) [#31402](https://github.com/nodejs/node/pull/31402)
- [[`7eed9d6bcc`](https://github.com/nodejs/node/commit/7eed9d6bcc)] - **fs**: fix WriteStream autoClose order (Robert Nagy) [#31790](https://github.com/nodejs/node/pull/31790)
- [[`ff58854dbe`](https://github.com/nodejs/node/commit/ff58854dbe)] - **(SEMVER-MINOR)** **fs**: return first folder made by mkdir recursive (Benjamin Coe) [#31530](https://github.com/nodejs/node/pull/31530)
- [[`1c4f4cc436`](https://github.com/nodejs/node/commit/1c4f4cc436)] - **fs**: fix writeFile\[Sync\] for non-seekable files (Alba Mendez) [#32006](https://github.com/nodejs/node/pull/32006)
- [[`c106a857a9`](https://github.com/nodejs/node/commit/c106a857a9)] - **fs**: fix valid id range on chown, lchown, fchown (himself65) [#31694](https://github.com/nodejs/node/pull/31694)
- [[`1ffa9f388f`](https://github.com/nodejs/node/commit/1ffa9f388f)] - **http**: fix socket re-use races (Robert Nagy) [#32000](https://github.com/nodejs/node/pull/32000)
- [[`49a07f7932`](https://github.com/nodejs/node/commit/49a07f7932)] - **http, async_hooks**: remove unneeded reference to wrapping resource (Gerhard Stoebich) [#32054](https://github.com/nodejs/node/pull/32054)
- [[`897b1d2e5e`](https://github.com/nodejs/node/commit/897b1d2e5e)] - **lib**: move isLegalPort to validators, refactor (James M Snell) [#31851](https://github.com/nodejs/node/pull/31851)
- [[`607ac90906`](https://github.com/nodejs/node/commit/607ac90906)] - **lib**: improve value validation utils (Denys Otrishko) [#31480](https://github.com/nodejs/node/pull/31480)
- [[`c0ba6ec560`](https://github.com/nodejs/node/commit/c0ba6ec560)] - **meta**: move thefourtheye to TSC Emeritus (Rich Trott) [#32059](https://github.com/nodejs/node/pull/32059)
- [[`710c9051e3`](https://github.com/nodejs/node/commit/710c9051e3)] - **n-api**: define release 6 (Gabriel Schulhof) [#32058](https://github.com/nodejs/node/pull/32058)
- [[`e83671c3c4`](https://github.com/nodejs/node/commit/e83671c3c4)] - **src**: DRY crypto Update() methods (Ben Noordhuis) [#31767](https://github.com/nodejs/node/pull/31767)
- [[`025f658fa6`](https://github.com/nodejs/node/commit/025f658fa6)] - **src**: fix spawnSync CHECK when SIGKILL fails (Ben Noordhuis) [#31768](https://github.com/nodejs/node/pull/31768)
- [[`2248ba760b`](https://github.com/nodejs/node/commit/2248ba760b)] - **src**: fix missing extra ca in tls.rootCertificates (Eric Bickle) [#32075](https://github.com/nodejs/node/pull/32075)
- [[`fa376f420c`](https://github.com/nodejs/node/commit/fa376f420c)] - **src**: fix -Wmaybe-uninitialized compiler warning (Ben Noordhuis) [#31809](https://github.com/nodejs/node/pull/31809)
- [[`c3aa3e70f0`](https://github.com/nodejs/node/commit/c3aa3e70f0)] - **src**: remove unused include from node_file.cc (Ben Noordhuis) [#31809](https://github.com/nodejs/node/pull/31809)
- [[`d8c927b5f1`](https://github.com/nodejs/node/commit/d8c927b5f1)] - **_Revert_** "**src**: keep main-thread Isolate attached to platform during Dispose" (Anna Henningsen) [#31853](https://github.com/nodejs/node/pull/31853)
- [[`625d8f7007`](https://github.com/nodejs/node/commit/625d8f7007)] - **src**: discard tasks posted to platform TaskRunner during shutdown (Anna Henningsen) [#31853](https://github.com/nodejs/node/pull/31853)
- [[`55a8ca8ee4`](https://github.com/nodejs/node/commit/55a8ca8ee4)] - **src**: elevate v8 namespace (RamanandPatil) [#32041](https://github.com/nodejs/node/pull/32041)
- [[`1e9a2516df`](https://github.com/nodejs/node/commit/1e9a2516df)] - **src**: use C++ style for struct with initializers (Sam Roberts) [#32134](https://github.com/nodejs/node/pull/32134)
- [[`6aa797b546`](https://github.com/nodejs/node/commit/6aa797b546)] - **src**: implement per-process native Debug() printer (Joyee Cheung) [#31884](https://github.com/nodejs/node/pull/31884)
- [[`5127c700d0`](https://github.com/nodejs/node/commit/5127c700d0)] - **src**: refactor debug category parsing (Joyee Cheung) [#31884](https://github.com/nodejs/node/pull/31884)
- [[`2388a40f56`](https://github.com/nodejs/node/commit/2388a40f56)] - **src**: make aliased_buffer.h self-contained (Joyee Cheung) [#31884](https://github.com/nodejs/node/pull/31884)
- [[`258a80d3cc`](https://github.com/nodejs/node/commit/258a80d3cc)] - **(SEMVER-MINOR)** **src**: create a getter for kernel version (Juan José Arboleda) [#31732](https://github.com/nodejs/node/pull/31732)
- [[`cba75c5cf4`](https://github.com/nodejs/node/commit/cba75c5cf4)] - **src**: handle NULL env scenario (Harshitha KP) [#31899](https://github.com/nodejs/node/pull/31899)
- [[`cc27846fb9`](https://github.com/nodejs/node/commit/cc27846fb9)] - **src**: simplify node_worker.cc using new KVStore API (Denys Otrishko) [#31773](https://github.com/nodejs/node/pull/31773)
- [[`296f35b888`](https://github.com/nodejs/node/commit/296f35b888)] - **src**: improve KVStore API (Denys Otrishko) [#31773](https://github.com/nodejs/node/pull/31773)
- [[`bd756883a7`](https://github.com/nodejs/node/commit/bd756883a7)] - **src**: add missing namespace using statements in node_watchdog.h (legendecas) [#32117](https://github.com/nodejs/node/pull/32117)
- [[`e9f9d076e9`](https://github.com/nodejs/node/commit/e9f9d076e9)] - **src**: fix -Wreorder compiler warning (Colin Ihrig) [#32126](https://github.com/nodejs/node/pull/32126)
- [[`7b9b578652`](https://github.com/nodejs/node/commit/7b9b578652)] - **src**: fix -Winconsistent-missing-override warning (Colin Ihrig) [#32126](https://github.com/nodejs/node/pull/32126)
- [[`4ac1ce1071`](https://github.com/nodejs/node/commit/4ac1ce1071)] - **src**: introduce node_sockaddr (James M Snell) [#32070](https://github.com/nodejs/node/pull/32070)
- [[`31e4a0d7ac`](https://github.com/nodejs/node/commit/31e4a0d7ac)] - **src**: Handle bad callback in asyc_wrap (Harshitha KP) [#31946](https://github.com/nodejs/node/pull/31946)
- [[`a03777096e`](https://github.com/nodejs/node/commit/a03777096e)] - **src,http2**: introduce node_http_common (James M Snell) [#32069](https://github.com/nodejs/node/pull/32069)
- [[`fab8c83253`](https://github.com/nodejs/node/commit/fab8c83253)] - **stream**: avoid destroying writable source (Robert Nagy) [#32198](https://github.com/nodejs/node/pull/32198)
- [[`66fe2d90ff`](https://github.com/nodejs/node/commit/66fe2d90ff)] - **stream**: avoid destroying http1 objects (Robert Nagy) [#32197](https://github.com/nodejs/node/pull/32197)
- [[`0a00552122`](https://github.com/nodejs/node/commit/0a00552122)] - **stream**: do not swallow errors with async iterators and pipeline (Matteo Collina) [#32051](https://github.com/nodejs/node/pull/32051)
- [[`f2636598e8`](https://github.com/nodejs/node/commit/f2636598e8)] - **stream**: eos make const state const (Robert Nagy) [#32031](https://github.com/nodejs/node/pull/32031)
- [[`4b04bf89ad`](https://github.com/nodejs/node/commit/4b04bf89ad)] - **stream**: re-use legacy destroyer (Robert Nagy) [#31316](https://github.com/nodejs/node/pull/31316)
- [[`7ce1cc93ce`](https://github.com/nodejs/node/commit/7ce1cc93ce)] - **stream**: simplify pipeline (Robert Nagy) [#31316](https://github.com/nodejs/node/pull/31316)
- [[`9d1b1a3fbd`](https://github.com/nodejs/node/commit/9d1b1a3fbd)] - **stream**: simplify Writable.write (Robert Nagy) [#31146](https://github.com/nodejs/node/pull/31146)
- [[`1e05ddf406`](https://github.com/nodejs/node/commit/1e05ddf406)] - **stream**: improve writable.write() performance (Brian White) [#31624](https://github.com/nodejs/node/pull/31624)
- [[`90a4d438cb`](https://github.com/nodejs/node/commit/90a4d438cb)] - **stream**: combine properties using defineProperties (antsmartian) [#31187](https://github.com/nodejs/node/pull/31187)
- [[`4640ea24bd`](https://github.com/nodejs/node/commit/4640ea24bd)] - **stream**: don't destroy final readable stream in pipeline (Robert Nagy) [#32110](https://github.com/nodejs/node/pull/32110)
- [[`2585b814b0`](https://github.com/nodejs/node/commit/2585b814b0)] - **stream**: add comments to pipeline implementation (Robert Nagy) [#32042](https://github.com/nodejs/node/pull/32042)
- [[`ceca1c3a4f`](https://github.com/nodejs/node/commit/ceca1c3a4f)] - **test**: improve test-fs-existssync-false.js (himself65) [#31883](https://github.com/nodejs/node/pull/31883)
- [[`84197eaae0`](https://github.com/nodejs/node/commit/84197eaae0)] - **test**: mark test-timers-blocking-callback flaky on osx (Myles Borins) [#32189](https://github.com/nodejs/node/pull/32189)
- [[`4589863518`](https://github.com/nodejs/node/commit/4589863518)] - **test**: always skip vm-timeout-escape-queuemicrotask (Denys Otrishko) [#31980](https://github.com/nodejs/node/pull/31980)
- [[`188f1d275f`](https://github.com/nodejs/node/commit/188f1d275f)] - **test**: improve test-debug-usage (Rich Trott) [#32141](https://github.com/nodejs/node/pull/32141)
- [[`92cc406baf`](https://github.com/nodejs/node/commit/92cc406baf)] - **test**: refactor all benchmark tests to use the new test option (Ruben Bridgewater) [#31755](https://github.com/nodejs/node/pull/31755)
- [[`6f9f2c5de4`](https://github.com/nodejs/node/commit/6f9f2c5de4)] - **test**: warn when inspector process crashes (Matheus Marchini) [#32133](https://github.com/nodejs/node/pull/32133)
- [[`6a9654a7a9`](https://github.com/nodejs/node/commit/6a9654a7a9)] - **test**: increase test timeout to prevent flakiness (Ruben Bridgewater) [#31716](https://github.com/nodejs/node/pull/31716)
- [[`862cd2b49d`](https://github.com/nodejs/node/commit/862cd2b49d)] - **test**: use index.js if package.json "main" is empty (Ben Noordhuis) [#32040](https://github.com/nodejs/node/pull/32040)
- [[`3d64c9eba6`](https://github.com/nodejs/node/commit/3d64c9eba6)] - **test**: changed function to arrow function (ProdipRoy89) [#32045](https://github.com/nodejs/node/pull/32045)
- [[`6545d1a55d`](https://github.com/nodejs/node/commit/6545d1a55d)] - **test**: allow EAI_FAIL in test-net-dns-error.js (Vita Batrla) [#31780](https://github.com/nodejs/node/pull/31780)
- [[`1428de8ee6`](https://github.com/nodejs/node/commit/1428de8ee6)] - **test**: add WASI test for path_link() (Colin Ihrig) [#32132](https://github.com/nodejs/node/pull/32132)
- [[`da7349d908`](https://github.com/nodejs/node/commit/da7349d908)] - **test**: remove superfluous checks in test-net-reconnect-error (Rich Trott) [#32120](https://github.com/nodejs/node/pull/32120)
- [[`74edcc5dd9`](https://github.com/nodejs/node/commit/74edcc5dd9)] - **test**: apply camelCase in test-net-reconnect-error (Rich Trott) [#32120](https://github.com/nodejs/node/pull/32120)
- [[`8e435687bb`](https://github.com/nodejs/node/commit/8e435687bb)] - **test**: update tests for larger Buffers (Jakob Kummerow) [#32114](https://github.com/nodejs/node/pull/32114)
- [[`83e9a3ea59`](https://github.com/nodejs/node/commit/83e9a3ea59)] - **test**: add coverage for FSWatcher exception (Rich Trott) [#32057](https://github.com/nodejs/node/pull/32057)
- [[`89987b3a9f`](https://github.com/nodejs/node/commit/89987b3a9f)] - **test**: remove common.expectsInternalAssertion (Rich Trott) [#32057](https://github.com/nodejs/node/pull/32057)
- [[`35d0569356`](https://github.com/nodejs/node/commit/35d0569356)] - **tools**: enable no-useless-backreference lint rule (Colin Ihrig) [#31400](https://github.com/nodejs/node/pull/31400)
- [[`d3c4210ea0`](https://github.com/nodejs/node/commit/d3c4210ea0)] - **tools**: enable default-case-last lint rule (Colin Ihrig) [#31400](https://github.com/nodejs/node/pull/31400)
- [[`814bb4a35d`](https://github.com/nodejs/node/commit/814bb4a35d)] - **tools**: update ESLint to 7.0.0-alpha.2 (Colin Ihrig) [#31400](https://github.com/nodejs/node/pull/31400)
- [[`cac1d01cad`](https://github.com/nodejs/node/commit/cac1d01cad)] - **tools**: update ESLint to 7.0.0-alpha.1 (Colin Ihrig) [#31400](https://github.com/nodejs/node/pull/31400)
- [[`c70cfd2ba6`](https://github.com/nodejs/node/commit/c70cfd2ba6)] - **tools**: update ESLint to 7.0.0-alpha.0 (Colin Ihrig) [#31400](https://github.com/nodejs/node/pull/31400)
- [[`bb41383bdc`](https://github.com/nodejs/node/commit/bb41383bdc)] - **tools**: use per-process native Debug() printer in mkcodecache (Joyee Cheung) [#31884](https://github.com/nodejs/node/pull/31884)
- [[`eaf6723804`](https://github.com/nodejs/node/commit/eaf6723804)] - **vm**: refactor value validation with internal/validators.js (Denys Otrishko) [#31480](https://github.com/nodejs/node/pull/31480)
- [[`dd83bd266d`](https://github.com/nodejs/node/commit/dd83bd266d)] - **(SEMVER-MINOR)** **wasi**: add returnOnExit option (Colin Ihrig) [#32101](https://github.com/nodejs/node/pull/32101)

Windows 32-bit Installer: https://nodejs.org/dist/v13.11.0/node-v13.11.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v13.11.0/node-v13.11.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v13.11.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v13.11.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v13.11.0/node-v13.11.0.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v13.11.0/node-v13.11.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v13.11.0/node-v13.11.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v13.11.0/node-v13.11.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v13.11.0/node-v13.11.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v13.11.0/node-v13.11.0-aix-ppc64.tar.gz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v13.11.0/node-v13.11.0-sunos-x64.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v13.11.0/node-v13.11.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v13.11.0/node-v13.11.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v13.11.0/node-v13.11.0.tar.gz \
Other release files: https://nodejs.org/dist/v13.11.0/ \
Documentation: https://nodejs.org/docs/v13.11.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

35ef7b13b8c5cee625d14a82a896401ccccc60f2299c63827c52e3aff27a7b5a  node-v13.11.0-aix-ppc64.tar.gz
2d87989fb1e0d425667c5ca9893cb3ecfb30cd3344d543870246d65f8d9b892f  node-v13.11.0-darwin-x64.tar.gz
10e77257bd8a43930c221295e9c08bf38f6adb3b60c5c4d60db27b95b63ce953  node-v13.11.0-darwin-x64.tar.xz
82e0e8f7506bec1759331eeeecc8b09f9a3e7fa1d70d1154f884c4c6be887d7d  node-v13.11.0-headers.tar.gz
ab533f78130f8bd4683a57bfa56054a7fb79d3eb6a32fe871a5096fc6302ee6e  node-v13.11.0-headers.tar.xz
c20c89664b5f06559f0aa2f0ad334d6d8157599b01101719e455b2b500a13c1a  node-v13.11.0-linux-arm64.tar.gz
63ce9871b9802a1f021c84f282c63e1890524a32dc97d3e7a7ab8d52b4bde19e  node-v13.11.0-linux-arm64.tar.xz
98c59faf01ddb868f3238c802ce420cd4d46f04b6181525e92fd4d728469a7cf  node-v13.11.0-linux-armv7l.tar.gz
f80ea1c16d9027773c8aeb04ed8debe21896caa6f24256eb9b0aa7099b94e3d1  node-v13.11.0-linux-armv7l.tar.xz
103f759336fa74505d949c4cd0307e958b1eaf0ef71c36f0c147e3718af74322  node-v13.11.0-linux-ppc64le.tar.gz
8818d17d6d7f6eb5dfab15002cc07bc0d68f75b6bebff7f0aec76d37dc5e0354  node-v13.11.0-linux-ppc64le.tar.xz
6a6f659c32dcbf5b86b97b0d330125b4a89344a0244b626b1274d4f839868128  node-v13.11.0-linux-s390x.tar.gz
954c41fbc569ae8dcd7308861d4dcb497a605d15770f189ac4633e346cacf88b  node-v13.11.0-linux-s390x.tar.xz
db9592a3e54c34fcf2252e6cf49780dda93cc175d7a27654a8971e1eb5f1f989  node-v13.11.0-linux-x64.tar.gz
c127cf38f9a56d97646eb1fedb93712f304950c7143705de7180a701becc0fbb  node-v13.11.0-linux-x64.tar.xz
2e367005f9b01f9899518d4df7f4e75d0a919d04392a9563f8cd732476e3924b  node-v13.11.0.pkg
cef81650f8b76b28cea3155b69af49ae87d64acfa5ce48d759925a04172b8d7a  node-v13.11.0-sunos-x64.tar.gz
914ff8896b6f8deb00839151e3c166ba5db171b9b58a7067f40aad2cc05c8c7b  node-v13.11.0-sunos-x64.tar.xz
a47e0a2ce346c7d5b25a5d4cb3c9efa82068a2a32a2885332d7d354f9e120706  node-v13.11.0.tar.gz
e5402183e68806785b3c40c2cb0a6b6aa43bb61aee1cec5efde9c65825ef291f  node-v13.11.0.tar.xz
a3efebb357576449561d5f58a74e7d13dda7212a0d05319281ba8961bc7c2d9f  node-v13.11.0-win-x64.7z
dc93aa5a82988f741dca6f1869c7b1aeda6f339293d13a968279a6dc9fcc8dd0  node-v13.11.0-win-x64.zip
bf9e94e637e26e9a4548f5c198dc763b2f3a1775334b4b53f31327038afe372c  node-v13.11.0-win-x86.7z
38214897726f645799eee08ff537ea1c95daa967ead07b0bc61f0f30a247e1ce  node-v13.11.0-win-x86.zip
7ecb19e7fbc690ecc81ef62383c651314dbc466fd614abd6e81a05f82d80f4e8  node-v13.11.0-x64.msi
311a1035db172f3ae7fc573dfb2f23cbb988a6bd2f852d64285c8cb416c707b2  node-v13.11.0-x86.msi
5aefe14973a3ed72ada7cfc5e8f358c77b1b1e084d25bd52767c83d3a59af082  win-x64/node.exe
1f21d29b063cd16bb401331f0f62efae7682a70147074ef0f316750241ed5cba  win-x64/node.lib
6cc67690c87f02e1c2cc11f352607d373299a208de2fd4e35488438ac493bb90  win-x64/node_pdb.7z
a2548bc70a75c806ee626768f4b2ebbc59535841500f58d309b94b1e0a832eac  win-x64/node_pdb.zip
b3b2211643029ee9e7b43a04cc143b75b9bd0e6dfa917b1829bcc65d840493c1  win-x86/node.exe
2d07645620eaa61500d5e06603a5dd0595321950b4c5fd9e8e3fd5bf82891483  win-x86/node.lib
6466501d5b6df24a0cc365e81922f18efc2766320f0fc7ba3b408df9b94d33b3  win-x86/node_pdb.7z
9181e6089f3ec9bcd2def9611575ad978318e3800552b588248a5ca0a4b04c71  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQEzBAEBCAAdFiEEDv/hvO/ZyE49CYFSkzsB9AtcqUYFAl5qn1IACgkQkzsB9Atc
qUazxQgAnAb+e1R0PDnGo6pqj/DI6HBx0x36gV2CPaQL3b+PW1xp1QOcmCaif7LS
aarombpnCkpaUhIxLf9dVRpfnUPLOgdYO9o5Z4vykkN/C9sKH2judLiWTZfkNKVL
eeFzOgo9xiDdZPBnN1bi1vU6zF5oIIBUyxhU45FXZrPgSmtZSLKrnQhremPxE39q
SzTKbgqxN1YWUZjQZna5arGvXgw6OWtoUdgOrW9VuiwnwRvbGjAg8Yags4N67olm
mC+cQ0ru6YPY+FbeXVD88nrWvoi5nfep8v6hVGyfMEFI6rG1stTcE3yARy0gfEup
WpiS1Im6N8mQ8TIHGKPS2kg0dKHMAg==
=6gQf
-----END PGP SIGNATURE-----

```
