---
date: '2022-07-07T16:08:02.138Z'
category: release
title: Node v16.16.0 (LTS)
layout: blog-post
author: <PERSON>
---

### Notable changes

- **deps**:
  - upgrade openssl sources to OpenSSL_1_1_1q (RafaelGSS) [#43692](https://github.com/nodejs/node/pull/43692)
- **src**:
  - add OpenSSL config appname (<PERSON>) [#43124](https://github.com/nodejs/node/pull/43124)

### Commits

- \[[`2303fd3fe5`](https://github.com/nodejs/node/commit/2303fd3fe5)] - **deps**: update archs files for OpenSSL-1.1.1q (RafaelGSS) [#43692](https://github.com/nodejs/node/pull/43692)
- \[[`b219a63c28`](https://github.com/nodejs/node/commit/b219a63c28)] - **deps**: upgrade openssl sources to OpenSSL_1_1_1q (RafaelGSS) [#43692](https://github.com/nodejs/node/pull/43692)
- \[[`c6553a4ef5`](https://github.com/nodejs/node/commit/c6553a4ef5)] - **deps**: update archs files for OpenSSL-1.1.1p+quic (RafaelGSS) [#43535](https://github.com/nodejs/node/pull/43535)
- \[[`e9084a3e90`](https://github.com/nodejs/node/commit/e9084a3e90)] - **deps**: upgrade openssl sources to OpenSSL_1_1_1p+quic (RafaelGSS) [#43535](https://github.com/nodejs/node/pull/43535)
- \[[`1da22eb482`](https://github.com/nodejs/node/commit/1da22eb482)] - **http**: stricter Transfer-Encoding and header separator parsing (Paolo Insogna) [nodejs-private/node-private#315](https://github.com/nodejs-private/node-private/pull/315)
- \[[`754c9bfde0`](https://github.com/nodejs/node/commit/754c9bfde0)] - **src**: fix IPv4 validation in inspector_socket (Tobias Nießen) [nodejs-private/node-private#320](https://github.com/nodejs-private/node-private/pull/320)
- \[[`447cf680b0`](https://github.com/nodejs/node/commit/447cf680b0)] - **(SEMVER-MAJOR)** **src,deps,build,test**: add OpenSSL config appname (Daniel Bevenius) [#43124](https://github.com/nodejs/node/pull/43124)

Windows 32-bit Installer: https://nodejs.org/dist/v16.16.0/node-v16.16.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v16.16.0/node-v16.16.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v16.16.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v16.16.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v16.16.0/node-v16.16.0.pkg \
macOS Apple Silicon 64-bit Binary: https://nodejs.org/dist/v16.16.0/node-v16.16.0-darwin-arm64.tar.gz \
macOS Intel 64-bit Binary: https://nodejs.org/dist/v16.16.0/node-v16.16.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v16.16.0/node-v16.16.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v16.16.0/node-v16.16.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v16.16.0/node-v16.16.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v16.16.0/node-v16.16.0-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v16.16.0/node-v16.16.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v16.16.0/node-v16.16.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v16.16.0/node-v16.16.0.tar.gz \
Other release files: https://nodejs.org/dist/v16.16.0/ \
Documentation: https://nodejs.org/docs/v16.16.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

6050ce2c71ef0acca9bc09a0dbbc19b243d1915839b3a349f08ac92672c09ff7  node-v16.16.0-aix-ppc64.tar.gz
167721c2d72402e54adc0f8c87ca840796216c4d98946509d73221b771ad3e4c  node-v16.16.0-darwin-arm64.tar.gz
e4468368bf25ee1c504f08ecfd0d9fec17a9e72ef27c4495406b958cd7b4bafd  node-v16.16.0-darwin-arm64.tar.xz
982edd0fad364ad6e2d72161671544ab9399bd0ca8c726bde3cd07775c4c709a  node-v16.16.0-darwin-x64.tar.gz
a65b2b92ede6be9004eae1ca9da26da2178bc696f35e8cca1840c33b777a5421  node-v16.16.0-darwin-x64.tar.xz
89373d20e381b7f1ddd773f7e9c5bd3795876c0eab09696f7fc233c5d8db2e85  node-v16.16.0-headers.tar.gz
d463bae761d7fde776564d25012190df1e2b85e2b93480eaf413126331973fbc  node-v16.16.0-headers.tar.xz
378a3998e7c4dabd0cbd96b05a1b08e834c4b607f09c0745072de9423626fca4  node-v16.16.0-linux-arm64.tar.gz
6cb8f1353480646c1cd8ab9911995e5591e1a97811f43ea4ab3e946a57e7c80e  node-v16.16.0-linux-arm64.tar.xz
cd061554c3a9f1bac0a0bcafbbf50e10c9e5f6efbf98ee3450bb0920194ec06d  node-v16.16.0-linux-armv7l.tar.gz
b8f499e38792e7b24d38da4169ab999cbbdfbf597ef86447aeaa661679ef0604  node-v16.16.0-linux-armv7l.tar.xz
3f1c57af5994e4f524d33e0173e5b60a76ad2347bc4b838719bc06cc0a1ef1c3  node-v16.16.0-linux-ppc64le.tar.gz
32c665437a17cb5ad273ed7abe1a5711935f2b86d36dce315b5655d524fbc041  node-v16.16.0-linux-ppc64le.tar.xz
b71b5dd31f398c5467cc3b93a79d5757e7ad286e5ad2bd79d5fda6b775b481c7  node-v16.16.0-linux-s390x.tar.gz
bd779749163d3b26d2b54fc6f9731fd6dba903c7b54caa40afbbb9476637cdff  node-v16.16.0-linux-s390x.tar.xz
c85b16d1a4c259d01be7111ecb0361260627e4fc245004a920521eacb28e50df  node-v16.16.0-linux-x64.tar.gz
edcb6e9bb049ae365611aa209fc03c4bfc7e0295dbcc5b2f1e710ac70384a8ec  node-v16.16.0-linux-x64.tar.xz
ee42133ad6de3cd61954c2abf4a03d123f9e596d2f658247c59b8ad43fd2f147  node-v16.16.0.pkg
e07c30b0498f143c08793e34bda1adeaad32f485a4f79f4d67a82879f4c0bbe3  node-v16.16.0.tar.gz
145151eff3b2aa5ebe73384009c52271a83740ae687a93c98c628cd7d52736eb  node-v16.16.0.tar.xz
cfe9ba47f32dd2c667078ed175e76cad1d00bd4ea9f915219ead81d2cfca3c34  node-v16.16.0-win-x64.7z
c657acc98af55018c8fd6113c7e08d67c8083af75ba0306f9561b0117abc39d4  node-v16.16.0-win-x64.zip
a933c0f78bc653dfba11df49c476e90e7ed2492f34319d74bf7de2b21f8943d8  node-v16.16.0-win-x86.7z
957783527844fcd7da1d0b6dd311b64f92e2c5185112ada2d92e52c1a71fb373  node-v16.16.0-win-x86.zip
c94f794d56ab6b5ba817a7a2efcc3ea9e81893126f6a925963542aa8ad34f798  node-v16.16.0-x64.msi
1117bbde79025da055c87d35aa230f3c9a8855bf8e92bd126c30ec51b8dc8afd  node-v16.16.0-x86.msi
a7da2a7db76991bbb6a058b034969d2464cbd1b4c9af35c39def8a79cbbb01cc  win-x64/node.exe
a676e936f019206ecfb19a878802c085841ac34f8f83080d4261e5ca138b011c  win-x64/node.lib
811b52184ff4c7e104ac752088c2dae6fcd91d22e4d0361809e6d66fa4d68575  win-x64/node_pdb.7z
595b784a4fdd1c682a020c2f5742ab302abb018ca28e25db46666e0061b2f7f5  win-x64/node_pdb.zip
b928c03c1985e8fa1664fc2e795daecac612b0a8a75491f72ccbc09f5a9c65e6  win-x86/node.exe
8574cc9643d304df424d038f37b2a78cc3c2a7dd58c2f1b9ff5d9a07da73d80d  win-x86/node.lib
bd79146dfe7984b82d32b69985bde595c30c86bbbd1c6fe7620381872c3006e1  win-x86/node_pdb.7z
a004a56c407d1f35e91141a5b86981a2d1cc1049a5f4092a6bde562076135d88  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEdPEmArbxxOkT+qN606iWE2Q7YgEFAmLG9psACgkQ06iWE2Q7
YgG7IRAAgDqQDf7abPjTYW+j/YNR9h/n+a3ukl0PKNHVpLExTrkvKv5aLZIaxV2Q
vIkYHHrSjK6TcrdyCB/QeA6SW37r274PrKJMpD5bSr4nwmN9vX5Vs9d0GVg7Bq9p
fY9qkBq2w7Rg/21YO9vOIM8fTvruEgDiRB9/UPpB1MFIWZRYr0HhdlTbt3LNHYUZ
D2ZpN3pom5BCthoxCEunve6sU5XYnjdiXyGwX9LSAjg6YijTPEsXSG3ZIbrTGrtQ
L5wlHVRlVSr/6N9q6AF8vjKDtVgHkZ3VTXx0oCS19DoZSa+gz+ZyUa1o957PSpt1
CuQe/G5U5VJLyeZbzJrUnH7ACoWMShYglcGrVoKTgkdD0t7Wtfg8ZhNFHM7e+NLU
DOmSjHs1oGkkyUQpAFSWdOxxchlcgOZS4N9BV4KeWPNYjIFmm4/+WTHGB29gzFz4
qmqnFbccFFxJ4oresep7B395oEJMXIufRd+W0kDUs5cC6nVI8qvgNAYF+unyMe6M
VFQ1oAARHQYPOsBtuO4AKl7fwk5AxPy6SJ3PmwHFWWB8f/jXvPrGledFmwEhjAux
DcV4vEE41NmoieOoDLPhoBPH5wojeNK5Fb2aHMxOQakn99oVMdjHqTbCUAhqGAnC
HEwCAXSKh8In8dj3q4VPLJQERHB/cjiXWxV0Lg5n4kMrshHQ9xw=
=Y+EN
-----END PGP SIGNATURE-----

```
