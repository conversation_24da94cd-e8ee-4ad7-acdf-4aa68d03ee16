---
date: '2016-12-20T21:54:50.539Z'
category: release
title: Node v7.3.0 (Current)
layout: blog-post
author: <PERSON>
---

### Notable changes

- **buffer**:
  - buffer.fill() now works properly for the UCS2 encoding on Big-Endian machines. (<PERSON>) [#9837](https://github.com/nodejs/node/pull/9837)
- **cluster**:
  - disconnect() now returns a reference to the disconnected worker. (<PERSON>) [#10019](https://github.com/nodejs/node/pull/10019)
- **crypto**:
  - The built-in list of Well-Known CAs (Certificate Authorities) can now be extended via a NODE_EXTRA_CA_CERTS environment variable. (<PERSON>) [#9139](https://github.com/nodejs/node/pull/9139)
- **http**:
  - Remove stale timeout listeners in order to prevent a memory leak when using keep alive. (<PERSON>) [#9440](https://github.com/nodejs/node/pull/9440)
- **tls**:
  - Allow obvious key/passphrase combinations. (<PERSON>) [#10294](https://github.com/nodejs/node/pull/10294)
- **url**:
  - Including base argument in URL.originFor() to meet specification compliance. (joyeecheung) [#10021](https://github.com/nodejs/node/pull/10021)
  - Improve URLSearchParams to meet specification compliance. (Timothy Gu) [#9484](https://github.com/nodejs/node/pull/9484)

### Commits

- [[`c2cc11b3c6`](https://github.com/nodejs/node/commit/c2cc11b3c6)] - Working on v7.2.2 (Jeremiah Senkpiel) [#10127](https://github.com/nodejs/node/pull/10127)
- [[`b99a372e91`](https://github.com/nodejs/node/commit/b99a372e91)] - **buffer**: fix single-character string filling (Anna Henningsen) [#9837](https://github.com/nodejs/node/pull/9837)
- [[`d8b6723096`](https://github.com/nodejs/node/commit/d8b6723096)] - **buffer**: handle UCS2 `.fill()` properly on BE (Anna Henningsen) [#9837](https://github.com/nodejs/node/pull/9837)
- [[`e61331ee9b`](https://github.com/nodejs/node/commit/e61331ee9b)] - **build**: fix node_g target (Daniel Bevenius) [#10153](https://github.com/nodejs/node/pull/10153)
- [[`9d04152e15`](https://github.com/nodejs/node/commit/9d04152e15)] - **build**: Don't regenerate node symlink (sxa555) [#9827](https://github.com/nodejs/node/pull/9827)
- [[`5d14602181`](https://github.com/nodejs/node/commit/5d14602181)] - **(SEMVER-MINOR)** **cluster**: return worker reference from disconnect() (Sean Villars) [#10019](https://github.com/nodejs/node/pull/10019)
- [[`6963e8aa9d`](https://github.com/nodejs/node/commit/6963e8aa9d)] - **(SEMVER-MINOR)** **crypto**: allow adding extra certs to well-known CAs (Sam Roberts) [#9139](https://github.com/nodejs/node/pull/9139)
- [[`a308a2fae4`](https://github.com/nodejs/node/commit/a308a2fae4)] - **deps**: cherry-pick 081fce3 from V8 upstream (Matt Loring) [#10342](https://github.com/nodejs/node/pull/10342)
- [[`7c3d280bf0`](https://github.com/nodejs/node/commit/7c3d280bf0)] - **doc**: rework tls for accuracy and clarity (Sam Roberts) [#9800](https://github.com/nodejs/node/pull/9800)
- [[`6b98906a08`](https://github.com/nodejs/node/commit/6b98906a08)] - **doc**: document R CRAN mirror process (Lucas Holmquist) [#10211](https://github.com/nodejs/node/pull/10211)
- [[`7e8c5e3490`](https://github.com/nodejs/node/commit/7e8c5e3490)] - **doc**: expand common module material in test guide (Rich Trott) [#10251](https://github.com/nodejs/node/pull/10251)
- [[`ee736b276c`](https://github.com/nodejs/node/commit/ee736b276c)] - **doc**: fix broken link in COLLABORATOR_GUIDE.md (Michael Dawson) [#10267](https://github.com/nodejs/node/pull/10267)
- [[`40b0ca1329`](https://github.com/nodejs/node/commit/40b0ca1329)] - **doc**: fix typo in code example of 'path' module (pallxk) [#10136](https://github.com/nodejs/node/pull/10136)
- [[`b44e7891d0`](https://github.com/nodejs/node/commit/b44e7891d0)] - **doc**: standardizing on make -j4 (Jonathan Darling) [#9961](https://github.com/nodejs/node/pull/9961)
- [[`ff8fdb14fb`](https://github.com/nodejs/node/commit/ff8fdb14fb)] - **doc**: add note to parallelize make (Jonathan Darling) [#9961](https://github.com/nodejs/node/pull/9961)
- [[`5a64187bed`](https://github.com/nodejs/node/commit/5a64187bed)] - **doc**: buffer allocation throws for negative size (joyeecheung) [#10151](https://github.com/nodejs/node/pull/10151)
- [[`20fdf3aec6`](https://github.com/nodejs/node/commit/20fdf3aec6)] - **doc**: add some info on `tty#setRawMode()` (Jeremiah Senkpiel) [#10147](https://github.com/nodejs/node/pull/10147)
- [[`ae53a6e12b`](https://github.com/nodejs/node/commit/ae53a6e12b)] - **doc**: update `path.format` description and examples (anoff) [#10046](https://github.com/nodejs/node/pull/10046)
- [[`30340388f1`](https://github.com/nodejs/node/commit/30340388f1)] - **doc**: add a variable declaration in the buffer.md (Vse Mozhet Byt) [#9795](https://github.com/nodejs/node/pull/9795)
- [[`d64e52c68d`](https://github.com/nodejs/node/commit/d64e52c68d)] - **doc**: adding missing - in README (Italo A. Casas) [#10170](https://github.com/nodejs/node/pull/10170)
- [[`39bf5bfaf1`](https://github.com/nodejs/node/commit/39bf5bfaf1)] - **doc**: removing extra space in README (Italo A. Casas) [#10168](https://github.com/nodejs/node/pull/10168)
- [[`bc64a63440`](https://github.com/nodejs/node/commit/bc64a63440)] - **doc**: fix a wrong note in the buffer.md (Vse Mozhet Byt) [#9795](https://github.com/nodejs/node/pull/9795)
- [[`d4c73d4823`](https://github.com/nodejs/node/commit/d4c73d4823)] - **doc**: remove an extraneous word in the buffer.md (Vse Mozhet Byt) [#9795](https://github.com/nodejs/node/pull/9795)
- [[`d373b2f2fb`](https://github.com/nodejs/node/commit/d373b2f2fb)] - **doc**: fix examples in buffer.md to avoid confusion (Vse Mozhet Byt) [#9795](https://github.com/nodejs/node/pull/9795)
- [[`7a39a44dbc`](https://github.com/nodejs/node/commit/7a39a44dbc)] - **doc**: remove a wrong remark in the buffer.md (Vse Mozhet Byt) [#9795](https://github.com/nodejs/node/pull/9795)
- [[`39b083eb51`](https://github.com/nodejs/node/commit/39b083eb51)] - **doc**: repeat a remark as needed in the buffer.md (Vse Mozhet Byt) [#9795](https://github.com/nodejs/node/pull/9795)
- [[`622690f242`](https://github.com/nodejs/node/commit/622690f242)] - **doc**: fix copy-paste artifacts in the buffer.md (Vse Mozhet Byt) [#9795](https://github.com/nodejs/node/pull/9795)
- [[`3b848a279b`](https://github.com/nodejs/node/commit/3b848a279b)] - **doc**: fix wrong function arguments in the buffer.md (Vse Mozhet Byt) [#9795](https://github.com/nodejs/node/pull/9795)
- [[`9e47b943a7`](https://github.com/nodejs/node/commit/9e47b943a7)] - **doc**: fix a syntax error in the buffer.md (Vse Mozhet Byt) [#9795](https://github.com/nodejs/node/pull/9795)
- [[`1864222d50`](https://github.com/nodejs/node/commit/1864222d50)] - **doc**: var => const/let in the buffer.md (Vse Mozhet Byt) [#9795](https://github.com/nodejs/node/pull/9795)
- [[`7b924f1713`](https://github.com/nodejs/node/commit/7b924f1713)] - **doc**: fix typo in ecdhCurve, a tls property name (Sam Roberts) [#10345](https://github.com/nodejs/node/pull/10345)
- [[`2673be676a`](https://github.com/nodejs/node/commit/2673be676a)] - **fs**: remove unused argument from copyObject() (Ethan Arrowood) [#10041](https://github.com/nodejs/node/pull/10041)
- [[`1081f0f33d`](https://github.com/nodejs/node/commit/1081f0f33d)] - **fs**: remove needless assignment of null (Francis Gulotta) [#10260](https://github.com/nodejs/node/pull/10260)
- [[`dded482bb8`](https://github.com/nodejs/node/commit/dded482bb8)] - **http**: remove stale timeout listeners (Karl Böhlmark) [#9440](https://github.com/nodejs/node/pull/9440)
- [[`b41db3396b`](https://github.com/nodejs/node/commit/b41db3396b)] - **inspector**: check if connected before waiting (Eugene Ostroukhov) [#10094](https://github.com/nodejs/node/pull/10094)
- [[`b6a8bc6ac3`](https://github.com/nodejs/node/commit/b6a8bc6ac3)] - **lib,test**: use consistent operator linebreak style (Michaël Zasso) [#10178](https://github.com/nodejs/node/pull/10178)
- [[`ef2fa56314`](https://github.com/nodejs/node/commit/ef2fa56314)] - **src**: fix string format mistake for 32 bit node (Alex Newman) [#10082](https://github.com/nodejs/node/pull/10082)
- [[`d4e160c946`](https://github.com/nodejs/node/commit/d4e160c946)] - **(SEMVER-MINOR)** **src**: add wrapper for process.emitWarning() (Sam Roberts) [#9139](https://github.com/nodejs/node/pull/9139)
- [[`ec2f13fe66`](https://github.com/nodejs/node/commit/ec2f13fe66)] - **src**: don't overwrite non-writable vm globals (Ben Noordhuis) [#10227](https://github.com/nodejs/node/pull/10227)
- [[`28ffd593e2`](https://github.com/nodejs/node/commit/28ffd593e2)] - **stream, test**: test \_readableState.emittedReadable (Joyee Cheung) [#10249](https://github.com/nodejs/node/pull/10249)
- [[`729fecf390`](https://github.com/nodejs/node/commit/729fecf390)] - **stream_base**: homogenize req_wrap_obj use (Fedor Indutny) [#10184](https://github.com/nodejs/node/pull/10184)
- [[`8b9131c1f8`](https://github.com/nodejs/node/commit/8b9131c1f8)] - **test**: tls key/cert ordering not necessary (Sam Roberts) [#9800](https://github.com/nodejs/node/pull/9800)
- [[`8a34e60b41`](https://github.com/nodejs/node/commit/8a34e60b41)] - **test**: var to const in tls-no-cert-required (Sam Roberts) [#9800](https://github.com/nodejs/node/pull/9800)
- [[`ea16a2ab52`](https://github.com/nodejs/node/commit/ea16a2ab52)] - **test**: stream readable needReadable state (Joyee Cheung) [#10241](https://github.com/nodejs/node/pull/10241)
- [[`e4b29a57f9`](https://github.com/nodejs/node/commit/e4b29a57f9)] - **test**: refactor test-fs-read-stream-inherit (Rich Trott) [#10246](https://github.com/nodejs/node/pull/10246)
- [[`fb297cba8f`](https://github.com/nodejs/node/commit/fb297cba8f)] - **test**: refactor test-dgram-send-callback-multi-buffer (mfrance) [#9999](https://github.com/nodejs/node/pull/9999)
- [[`16fbd4f6bf`](https://github.com/nodejs/node/commit/16fbd4f6bf)] - **test**: refactor test-tls-ecdh-disable (Aaron Williams) [#9989](https://github.com/nodejs/node/pull/9989)
- [[`46c55a6454`](https://github.com/nodejs/node/commit/46c55a6454)] - **test**: cleanup test-stdout-close-catch.js (Travis Bretton) [#10006](https://github.com/nodejs/node/pull/10006)
- [[`8c8b1230da`](https://github.com/nodejs/node/commit/8c8b1230da)] - **test**: use const/let and common.mustCall (Outsider) [#9959](https://github.com/nodejs/node/pull/9959)
- [[`74563f07e9`](https://github.com/nodejs/node/commit/74563f07e9)] - **test**: refactor domain test (Adao Junior) [#10269](https://github.com/nodejs/node/pull/10269)
- [[`d9cfd5484f`](https://github.com/nodejs/node/commit/d9cfd5484f)] - **test**: clean up domain-no-error-handler test (weyj4) [#10291](https://github.com/nodejs/node/pull/10291)
- [[`553a32674a`](https://github.com/nodejs/node/commit/553a32674a)] - **test**: fix http-client-timeout-option-listeners (Rich Trott) [#10224](https://github.com/nodejs/node/pull/10224)
- [[`308cead66e`](https://github.com/nodejs/node/commit/308cead66e)] - **test**: update test-domain-uncaught-exception.js (Andy Chen) [#10193](https://github.com/nodejs/node/pull/10193)
- [[`60542cb98b`](https://github.com/nodejs/node/commit/60542cb98b)] - **test**: refactor test-domain.js (Siddhartha Sahai) [#10207](https://github.com/nodejs/node/pull/10207)
- [[`c0800d9449`](https://github.com/nodejs/node/commit/c0800d9449)] - **test**: refactor test-stream-big-push (Rich Trott) [#10226](https://github.com/nodejs/node/pull/10226)
- [[`b9361cae6e`](https://github.com/nodejs/node/commit/b9361cae6e)] - **test**: refactor test-http-dns-fail (Adrian Estrada) [#10243](https://github.com/nodejs/node/pull/10243)
- [[`a97f26476d`](https://github.com/nodejs/node/commit/a97f26476d)] - **test**: refactor test-crypto-random (Rich Trott) [#10232](https://github.com/nodejs/node/pull/10232)
- [[`2f9c8d977f`](https://github.com/nodejs/node/commit/2f9c8d977f)] - **test**: refactor test-http-pause-resume-one-end (Rich Trott) [#10210](https://github.com/nodejs/node/pull/10210)
- [[`90659bc95c`](https://github.com/nodejs/node/commit/90659bc95c)] - **test**: fix flaky test-dgram-exclusive-implicit-bind (Rich Trott) [#10212](https://github.com/nodejs/node/pull/10212)
- [[`a4f3080595`](https://github.com/nodejs/node/commit/a4f3080595)] - **test**: improvements in test fixtures symlinked (Adrian Estrada) [#10182](https://github.com/nodejs/node/pull/10182)
- [[`d5e30a69e2`](https://github.com/nodejs/node/commit/d5e30a69e2)] - **test**: refactor test-fs-fsync (Rob Adelmann) [#10176](https://github.com/nodejs/node/pull/10176)
- [[`be87441463`](https://github.com/nodejs/node/commit/be87441463)] - **test**: refactor test-http-after-connect.js (larissayvette) [#10229](https://github.com/nodejs/node/pull/10229)
- [[`2b78212445`](https://github.com/nodejs/node/commit/2b78212445)] - **test**: use strictEqual in test-debug-break (Adrian Estrada) [#10181](https://github.com/nodejs/node/pull/10181)
- [[`8b698d89ac`](https://github.com/nodejs/node/commit/8b698d89ac)] - **test**: refactor assert.equal, update syntax to ES6 (Prieto, Marcos) [#10190](https://github.com/nodejs/node/pull/10190)
- [[`3749dc6ce7`](https://github.com/nodejs/node/commit/3749dc6ce7)] - **test**: refactor http pipelined socket test (Rich Trott) [#10189](https://github.com/nodejs/node/pull/10189)
- [[`e1d813f3f8`](https://github.com/nodejs/node/commit/e1d813f3f8)] - **test**: refactor test-handle-wrap-close-abort (Rich Trott) [#10188](https://github.com/nodejs/node/pull/10188)
- [[`7f01484a7a`](https://github.com/nodejs/node/commit/7f01484a7a)] - **test**: add ES6 and strictEqual to test-fs-truncate (Adrian Estrada) [#10167](https://github.com/nodejs/node/pull/10167)
- [[`88839cf204`](https://github.com/nodejs/node/commit/88839cf204)] - **test**: replace var with const in test-require-dot (Amar Zavery) [#9916](https://github.com/nodejs/node/pull/9916)
- [[`09ec5db10b`](https://github.com/nodejs/node/commit/09ec5db10b)] - **test**: fail for missing output files (Anna Henningsen) [#10150](https://github.com/nodejs/node/pull/10150)
- [[`3f269cc760`](https://github.com/nodejs/node/commit/3f269cc760)] - **test**: use ES6 in test-debugger-client.js (Adrian Estrada) [#10183](https://github.com/nodejs/node/pull/10183)
- [[`1f11deb58f`](https://github.com/nodejs/node/commit/1f11deb58f)] - **test**: improve buffer transcode (Johnny Reading) [#10043](https://github.com/nodejs/node/pull/10043)
- [[`3e8df733e8`](https://github.com/nodejs/node/commit/3e8df733e8)] - **test**: improving crypto fips (James Tenenbaum) [#10002](https://github.com/nodejs/node/pull/10002)
- [[`6780c0e572`](https://github.com/nodejs/node/commit/6780c0e572)] - **test**: stream readableState readingMore state (Gregory) [#9868](https://github.com/nodejs/node/pull/9868)
- [[`c792e2ac49`](https://github.com/nodejs/node/commit/c792e2ac49)] - **test**: stream readableListening internal state (Italo A. Casas) [#9864](https://github.com/nodejs/node/pull/9864)
- [[`28c6df2604`](https://github.com/nodejs/node/commit/28c6df2604)] - **test**: add stdin-setrawmode.out file (Jonathan Darling) [#10149](https://github.com/nodejs/node/pull/10149)
- [[`f5347abac8`](https://github.com/nodejs/node/commit/f5347abac8)] - **test**: set stdin too for pseudo-tty tests (Anna Henningsen) [#10149](https://github.com/nodejs/node/pull/10149)
- [[`3a460d5469`](https://github.com/nodejs/node/commit/3a460d5469)] - **test**: check for error on invalid signal (Matt Phillips) [#10026](https://github.com/nodejs/node/pull/10026)
- [[`1ebb5b9adb`](https://github.com/nodejs/node/commit/1ebb5b9adb)] - **test**: refactor test-http-unix-socket (davidmarkclements) [#10072](https://github.com/nodejs/node/pull/10072)
- [[`8b7c97bc59`](https://github.com/nodejs/node/commit/8b7c97bc59)] - **test**: increase test coverage of BufferList (joyeecheung) [#10171](https://github.com/nodejs/node/pull/10171)
- [[`53e8e962d4`](https://github.com/nodejs/node/commit/53e8e962d4)] - **test**: fix flaky test-net-socket-timeout (Rich Trott) [#10172](https://github.com/nodejs/node/pull/10172)
- [[`ca38f70dea`](https://github.com/nodejs/node/commit/ca38f70dea)] - **test**: refactor test-net-keepalive.js (Kyle Corsi) [#9995](https://github.com/nodejs/node/pull/9995)
- [[`a9d4bd7a34`](https://github.com/nodejs/node/commit/a9d4bd7a34)] - **test**: refactor test-crypto-hmac (eudaimos) [#9958](https://github.com/nodejs/node/pull/9958)
- [[`778e5f7d0c`](https://github.com/nodejs/node/commit/778e5f7d0c)] - **test**: fix error in test-cluster-worker-death.js (Bruce Lai) [#9981](https://github.com/nodejs/node/pull/9981)
- [[`b67cad1174`](https://github.com/nodejs/node/commit/b67cad1174)] - **test**: use `assert.strictEqual` (anoff) [#9975](https://github.com/nodejs/node/pull/9975)
- [[`72fb05d062`](https://github.com/nodejs/node/commit/72fb05d062)] - **test**: change assert.equal to assert.strictEqual (Aileen) [#9946](https://github.com/nodejs/node/pull/9946)
- [[`dac757e502`](https://github.com/nodejs/node/commit/dac757e502)] - **test**: changed assert.equal to assert.strictEqual (vazina robertson) [#10015](https://github.com/nodejs/node/pull/10015)
- [[`d7988e0355`](https://github.com/nodejs/node/commit/d7988e0355)] - **test**: renamed assert.Equal to assert.strictEqual (Jared Young)
- [[`9d037cfa44`](https://github.com/nodejs/node/commit/9d037cfa44)] - **test**: improves test-tls-client-verify (Paul Graham) [#10051](https://github.com/nodejs/node/pull/10051)
- [[`2565e48445`](https://github.com/nodejs/node/commit/2565e48445)] - **test**: refactor test-https-agent-session-reuse (Diego Paez) [#10105](https://github.com/nodejs/node/pull/10105)
- [[`11140802f4`](https://github.com/nodejs/node/commit/11140802f4)] - **test**: refactor test-beforeexit-event (Rob Adelmann) [#10121](https://github.com/nodejs/node/pull/10121)
- [[`e695862531`](https://github.com/nodejs/node/commit/e695862531)] - **test**: improve test-fs-read-stream.js (Jenna Vuong) [#9629](https://github.com/nodejs/node/pull/9629)
- [[`be90638487`](https://github.com/nodejs/node/commit/be90638487)] - **test**: refactor test-domain-from-timer (Daniel Sims) [#9889](https://github.com/nodejs/node/pull/9889)
- [[`2c5d5629de`](https://github.com/nodejs/node/commit/2c5d5629de)] - **test**: refactor test-domain-exit-dispose-again (Ethan Arrowood) [#10003](https://github.com/nodejs/node/pull/10003)
- [[`6d4f270f2f`](https://github.com/nodejs/node/commit/6d4f270f2f)] - **test**: use const and strictEqual in test-os-homedir-no-envvar (CodeVana) [#9899](https://github.com/nodejs/node/pull/9899)
- [[`62f5a0bf59`](https://github.com/nodejs/node/commit/62f5a0bf59)] - **test**: check result of uv_loop_init and uv_write (Ben Noordhuis) [#10126](https://github.com/nodejs/node/pull/10126)
- [[`19432f05ff`](https://github.com/nodejs/node/commit/19432f05ff)] - **test**: refactor test-dgram-bind-default-address (Michael-Bryant Choa) [#9947](https://github.com/nodejs/node/pull/9947)
- [[`01509bc67e`](https://github.com/nodejs/node/commit/01509bc67e)] - **test**: move long-running test to sequential (Rich Trott) [#10161](https://github.com/nodejs/node/pull/10161)
- [[`d8dc890352`](https://github.com/nodejs/node/commit/d8dc890352)] - **test**: assert.throws() should include a RegExp (Chris Bystrek) [#9976](https://github.com/nodejs/node/pull/9976)
- [[`6f2f02d5ad`](https://github.com/nodejs/node/commit/6f2f02d5ad)] - **test**: invalid package.json causes error when require()ing in directory (Sam Shull) [#10044](https://github.com/nodejs/node/pull/10044)
- [[`6489a91027`](https://github.com/nodejs/node/commit/6489a91027)] - **test**: refactor test-listen-fd-ebadf (Richard Karmazin) [#10034](https://github.com/nodejs/node/pull/10034)
- [[`eb1664bed9`](https://github.com/nodejs/node/commit/eb1664bed9)] - **test**: refactor test-event-emitter-method-names (Rodrigo Palma) [#10027](https://github.com/nodejs/node/pull/10027)
- [[`c66cf2c1cf`](https://github.com/nodejs/node/commit/c66cf2c1cf)] - **test**: refactor tls-ticket-cluster (Yojan Shrestha) [#10023](https://github.com/nodejs/node/pull/10023)
- [[`de9972678e`](https://github.com/nodejs/node/commit/de9972678e)] - **test**: refactor test-domain-exit-dispose (Chris Henney) [#9938](https://github.com/nodejs/node/pull/9938)
- [[`5ca90777e6`](https://github.com/nodejs/node/commit/5ca90777e6)] - **test**: refactor test-stdin-from-file.js (amrios) [#10012](https://github.com/nodejs/node/pull/10012)
- [[`4d66578997`](https://github.com/nodejs/node/commit/4d66578997)] - **test**: use ES6 to update let & const (Jason Humphrey) [#9917](https://github.com/nodejs/node/pull/9917)
- [[`bb9174745b`](https://github.com/nodejs/node/commit/bb9174745b)] - **test**: fix test for buffer regression #649 (joyeecheung) [#9924](https://github.com/nodejs/node/pull/9924)
- [[`613798335c`](https://github.com/nodejs/node/commit/613798335c)] - **test**: stream readable resumeScheduled state (Italo A. Casas) [#10299](https://github.com/nodejs/node/pull/10299)
- [[`15c71f6c66`](https://github.com/nodejs/node/commit/15c71f6c66)] - **test**: improve code in test-fs-open.js (Adrian Estrada) [#10312](https://github.com/nodejs/node/pull/10312)
- [[`793d8719eb`](https://github.com/nodejs/node/commit/793d8719eb)] - **test**: fix flaky test-debug-port (Santiago Gimeno) [#10316](https://github.com/nodejs/node/pull/10316)
- [[`5e781a3883`](https://github.com/nodejs/node/commit/5e781a3883)] - **test**: refactor the code in test-dns-ipv6 (Adrian Estrada) [#10219](https://github.com/nodejs/node/pull/10219)
- [[`8b367c5ddd`](https://github.com/nodejs/node/commit/8b367c5ddd)] - **test**: improve test-child-process-fork-and-spawn (Adrian Estrada) [#10273](https://github.com/nodejs/node/pull/10273)
- [[`348e69c89d`](https://github.com/nodejs/node/commit/348e69c89d)] - **test**: fix flaky test-http-client-timeout-event (Rich Trott) [#10293](https://github.com/nodejs/node/pull/10293)
- [[`0d3ac89ff7`](https://github.com/nodejs/node/commit/0d3ac89ff7)] - **test**: add known_issues test for #6287 (AnnaMag) [#10272](https://github.com/nodejs/node/pull/10272)
- [[`f7f662cad5`](https://github.com/nodejs/node/commit/f7f662cad5)] - **test**: improve test-child-process-exec-buffer (Adrian Estrada) [#10275](https://github.com/nodejs/node/pull/10275)
- [[`f66461382c`](https://github.com/nodejs/node/commit/f66461382c)] - **timers**: fix handling of cleared immediates (hveldstra) [#9759](https://github.com/nodejs/node/pull/9759)
- [[`8e4b9fa487`](https://github.com/nodejs/node/commit/8e4b9fa487)] - **tls**: fix/annotate connect arg comments (Sam Roberts) [#9800](https://github.com/nodejs/node/pull/9800)
- [[`980acb4b95`](https://github.com/nodejs/node/commit/980acb4b95)] - **tls**: document and test option-less createServer (Sam Roberts) [#9800](https://github.com/nodejs/node/pull/9800)
- [[`41e1e6eb35`](https://github.com/nodejs/node/commit/41e1e6eb35)] - **tls**: do not refer to secureOptions as flags (Sam Roberts) [#9800](https://github.com/nodejs/node/pull/9800)
- [[`0b44384561`](https://github.com/nodejs/node/commit/0b44384561)] - **(SEMVER-MINOR)** **tls**: allow obvious key/passphrase combinations (Sam Roberts) [#10294](https://github.com/nodejs/node/pull/10294)
- [[`a92f2ad19c`](https://github.com/nodejs/node/commit/a92f2ad19c)] - **tools**: enforce consistent operator linebreak style (Michaël Zasso) [#10178](https://github.com/nodejs/node/pull/10178)
- [[`cc5bd9a0cf`](https://github.com/nodejs/node/commit/cc5bd9a0cf)] - **tools**: add macosx-firwall script to avoid popups (Daniel Bevenius) [#10114](https://github.com/nodejs/node/pull/10114)
- [[`7cb98138a9`](https://github.com/nodejs/node/commit/7cb98138a9)] - **tools**: forbid template literals in assert.throws (Michaël Zasso) [#10301](https://github.com/nodejs/node/pull/10301)
- [[`24482d08ce`](https://github.com/nodejs/node/commit/24482d08ce)] - **(SEMVER-MINOR)** **url**: add inspect function to TupleOrigin (Safia Abdalla) [#10039](https://github.com/nodejs/node/pull/10039)
- [[`f08d8a6c6f`](https://github.com/nodejs/node/commit/f08d8a6c6f)] - **url**: improve URLSearchParams spec compliance (Timothy Gu) [#9484](https://github.com/nodejs/node/pull/9484)
- [[`19d7197177`](https://github.com/nodejs/node/commit/19d7197177)] - **url**: add a got host pattern in url.js (Axel Monroy) [#9653](https://github.com/nodejs/node/pull/9653)
- [[`2da71f24de`](https://github.com/nodejs/node/commit/2da71f24de)] - **url, test**: fix typo in inspect output, add test (Jay Brownlee) [#10231](https://github.com/nodejs/node/pull/10231)
- [[`80cccce218`](https://github.com/nodejs/node/commit/80cccce218)] - **url, test**: including base argument in originFor (joyeecheung) [#10021](https://github.com/nodejs/node/pull/10021)
- [[`7a0fe9f471`](https://github.com/nodejs/node/commit/7a0fe9f471)] - **win,msi**: add required UIRef for localized strings (Bill Ticehurst) [#8884](https://github.com/nodejs/node/pull/8884)

Windows 32-bit Installer: https://nodejs.org/dist/v7.3.0/node-v7.3.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v7.3.0/node-v7.3.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v7.3.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v7.3.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v7.3.0/node-v7.3.0.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v7.3.0/node-v7.3.0-darwin-x64.tar.gz \
Linux 32-bit Binary: https://nodejs.org/dist/v7.3.0/node-v7.3.0-linux-x86.tar.xz \
Linux 64-bit Binary: https://nodejs.org/dist/v7.3.0/node-v7.3.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v7.3.0/node-v7.3.0-linux-ppc64le.tar.xz \
Linux PPC BE 64-bit Binary: https://nodejs.org/dist/v7.3.0/node-v7.3.0-linux-ppc64.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v7.3.0/node-v7.3.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v7.3.0/node-v7.3.0-aix-ppc64.tar.gz \
SmartOS 32-bit Binary: https://nodejs.org/dist/v7.3.0/node-v7.3.0-sunos-x86.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v7.3.0/node-v7.3.0-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v7.3.0/node-v7.3.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v7.3.0/node-v7.3.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v7.3.0/node-v7.3.0.tar.gz \
Other release files: https://nodejs.org/dist/v7.3.0/ \
Documentation: https://nodejs.org/docs/v7.3.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

301bf56472af47863ce78a6b371c6fc36f9cd25a74f9d098fd18d7dd080d2551  node-v7.3.0-aix-ppc64.tar.gz
c16622a44c19cfb03ced81ca463ccecbedfb1e438b3d3c2bc5a75df7857973c6  node-v7.3.0-darwin-x64.tar.gz
1c495c2e7af7ce5266d4bb64ef1b6fb114e0a662efdd7682e268a805f8a35946  node-v7.3.0-darwin-x64.tar.xz
c1b344959cf033fd6e3b0bc34ce899caf23a0202e97e19b4aebef9d29e4dc8e2  node-v7.3.0-headers.tar.gz
07f096f032d8ad06bf5d607646df9afc0587f17f3266401f975ac0ce49c8fdcf  node-v7.3.0-headers.tar.xz
b7187533653eafbbb791fd911afecf0e025a9c62d5a8433a929fb0ebe420ca97  node-v7.3.0-linux-arm64.tar.gz
cb5c4cc98272393582dbc751901927acbdbe752382e8def7c1cba1d33121434c  node-v7.3.0-linux-arm64.tar.xz
4c231e9eaec4af5397353528f15c2aeb3c0f5948b789cf837f54e53617632609  node-v7.3.0-linux-armv6l.tar.gz
c0b574604635b83a1506adf7388b6549ba66816a92d58a2982fa447ab63d33ea  node-v7.3.0-linux-armv6l.tar.xz
ffbe219fbd41cc5910b1ec1651958f4fca802efb0506df11a1a63ff1573858fb  node-v7.3.0-linux-armv7l.tar.gz
1ef7d988670f04eb38c393edc8399c2446f429846c0af574c429c8ba5cad89ad  node-v7.3.0-linux-armv7l.tar.xz
5e78014865c30ee00d47f8f3b95c264c9020808af543e35a7de2bf34c1163a2b  node-v7.3.0-linux-ppc64le.tar.gz
9750aadf4719ce97feb75cf919cf6df4ecdf1ec71f4b5b4d56cb4ae59d84919b  node-v7.3.0-linux-ppc64le.tar.xz
11f2a278e63e076d45ae024a8795cfeb63d54fb0377d63b6573a91c9e3f85f0e  node-v7.3.0-linux-ppc64.tar.gz
73eed381f1dea7238266976dfa553f3702e6be49c8664054a40c261124f367c5  node-v7.3.0-linux-ppc64.tar.xz
f1bc9766425c07a6a781062dc55fb643a681677fbf8e74f150711a3d26d7f37c  node-v7.3.0-linux-s390x.tar.gz
f57caf70225e56bc614abdaf285a7f9594c6127002e5bd1c3814748e8f137f88  node-v7.3.0-linux-s390x.tar.xz
0c1bb08c574c8d0e7e9ecd6b43d3ae2c069eaea45a5dea54a31d87cc1c0a1eee  node-v7.3.0-linux-x64.tar.gz
e5caa2040084015198cab97181aa450c687a19cbf76a09cc81e8368dad81fd8c  node-v7.3.0-linux-x64.tar.xz
a552e4aa49b3f9bd938e4cc6607b41106462e365a375ae3777da5375d58ea0dd  node-v7.3.0-linux-x86.tar.gz
90edc1e88c07ed63c1fb5acc86f0c9280821f2371d64d73c05cb6e3b41df10a8  node-v7.3.0-linux-x86.tar.xz
8c8485544dd10e99a0b5069fbe6b7a1fb8dfd555cd51ab7ba78a412ce3011498  node-v7.3.0.pkg
534b8b9b5be7c97f7cd9eb6fc396a0dc5c456df227956fb342ec96bf4c8b5e46  node-v7.3.0-sunos-x86.tar.gz
51384f0c5e0df8750ba39d6e3ef06eee75ccf5212648ee32f072dc6ad3f6aac4  node-v7.3.0-sunos-x86.tar.xz
5ba8a26e5341ec738ee6f1aacdd82be4002aef64716d069e060d901ade13c5b0  node-v7.3.0.tar.gz
dde8f6c800ac8049016d86b91ba5aeb6ddb35cb747cd74756dc05cf9fbe16b81  node-v7.3.0.tar.xz
0abf2d8ada935ec5c7a6d81c2c2cc9103d6220fd623f2dbdb5791c6396a7766f  node-v7.3.0-win-x64.7z
5ef8c99df1617fe0d4505c9766b451b5f48993ae70d5e807f80a2ad1a2521df3  node-v7.3.0-win-x64.zip
1fb12e70eee0e256cbedaf988044062f7a2030f766da351cc6234aaef84379b6  node-v7.3.0-win-x86.7z
4c27b69ee711bfcc196eda70395392c7145da7970bbef6ea9ba98fa895a1bf02  node-v7.3.0-win-x86.zip
4a08a27f816140f31cd826d14c31c84634e3c4e05f3cf71143496dbe96c241a9  node-v7.3.0-x64.msi
0451c0350a6d8feb78e8e81ca3dcb37183a3fd30c790055a8d1932b1eab0c5e4  node-v7.3.0-x86.msi
dbd723242d3faad3087d98979b600eac7fd97fac67a41bb9e87292514490bf63  win-x64/node.exe
94021b73bebd5b86caa091c99c7fea8f74f968b85004eafd7c8772282f5fba54  win-x64/node.lib
f7200e3ca5189d5469f4e87ce8c7a5760681aad731e7ecf7d8581774d5c50f4d  win-x64/node_pdb.7z
33c76d9d1739ec9e61a7dd6a3e74585bf6eeed6f9cc63a4757bc8361735808ea  win-x64/node_pdb.zip
fa083fd0e5655fdfcad61d056f14099626a83164b45cf11e267e590dd1c04a5d  win-x86/node.exe
a59e18364313b4d5b6bd67c588ebe68f73650b0c7e9e8382f0492d939d77cb2e  win-x86/node.lib
93e6468aa9613865049ae2159bac0705f13566057399427a5ea85a498d115189  win-x86/node_pdb.7z
9c106a4ef4ab6f28226c968c81cd50baf25aaf6ea4cb24d4c39ca2490e603812  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----
Comment: GPGTools - https://gpgtools.org

iQIcBAEBCAAGBQJYWac5AAoJEHQ0OQvb6bnFIywP/21aKxgyVRvXk4B2A5Au0opy
r5LCFuy0rQKo+K0Qf/8Hsg2fuSAPfakslerZf6MEmwQwbbcExGwbJWv+t/HbbIwM
xbJQjYKveG0b1CDaZwgphjKIZAB9GV1kbiwNnv/EFb4+GtX8VX/LeLANs3K8+zR8
7j8HVGKRujfpGgFW725rmRmomob2GXCq/Oi2aukANXQqCOakYIZs2Qt6y59+FFln
k4tusct+nTpE4Ztu1Yqh0ta2fZeEssjGxBfpuIiV6xR5rdvH9ru5NzbAkRSlFMZe
z0QVhFTeiCwYAWyYkhYy5jMcBFvNphDRjL0yPBfcQRQ1L3sbTGq0EnCbjYJ5CVZ5
fIt5J6Ws7h0hafojtI4uIqYyKE+Gf+7twK+BRWQJs0/XGphg8VcMPfI9GgrLGAl2
KrX7R5oHRAnSeixtjYU4fBxubG16gJMVi9QMK2TRkNxiM2/YdYiyc4W/a5T23y1W
rs8bkcQzb7LzbwNxpW+Pb8qTHQnWFZj5172hwnAKoYaz5dcs10Rn7SCqHrqtgUQG
vdCVf0a8zK/7oHtgSE4JWKiDZZTyh/di0joXKjv6IiI1j9RX1+QtUSWsl6918Kih
BhISnM7eAH6wVa1XYI58qO1r8txdHVOHXUA22T2Nozq4UYHPy2bTBViEHzLoTYBs
lfYatpjymxb3CuLdG25u
=7bEV
-----END PGP SIGNATURE-----

```
