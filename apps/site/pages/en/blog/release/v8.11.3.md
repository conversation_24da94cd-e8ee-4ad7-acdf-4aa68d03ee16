---
date: '2018-06-12T23:57:27.053Z'
category: release
title: Node v8.11.3 (LTS)
layout: blog-post
author: <PERSON>
---

### Notable Changes

- **buffer** (CVE-2018-7167): Fixes Denial of Service vulnerability where calling Buffer.fill() could hang
- **http2**
  - (CVE-2018-7161): Fixes Denial of Service vulnerability by updating the http2 implementation to not crash under certain circumstances during cleanup
  - (CVE-2018-1000168): Fixes Denial of Service vulnerability by upgrading nghttp2 to 1.32.0

### Commits

- [[`e1ff7c3cbc`](https://github.com/nodejs/node/commit/e1ff7c3cbc)] - **deps**: update to nghttp2 1.32.0 (<PERSON>) [nodejs-private/node-private#125](https://github.com/nodejs-private/node-private/pull/125)
- [[`c5a2748d8f`](https://github.com/nodejs/node/commit/c5a2748d8f)] - **doc**: buffer.fill() can zero-fill on invalid input (Сковорода Никита Андреевич) [nodejs-private/node-private#119](https://github.com/nodejs-private/node-private/pull/119)
- [[`354f2d97ff`](https://github.com/nodejs/node/commit/354f2d97ff)] - **http2**: fixup http2stream cleanup and other nits (James M Snell) [nodejs-private/node-private#123](https://github.com/nodejs-private/node-private/pull/123)
- [[`25c5111ca4`](https://github.com/nodejs/node/commit/25c5111ca4)] - **src**: avoid hanging on Buffer#fill 0-length input (Сковорода Никита Андреевич) [nodejs-private/node-private#119](https://github.com/nodejs-private/node-private/pull/119)
- [[`10c5adf19b`](https://github.com/nodejs/node/commit/10c5adf19b)] - **test**: add `Realloc()` shrink after reading stream data test (Anna Henningsen) [nodejs-private/node-private#132](https://github.com/nodejs-private/node-private/pull/132)
- [[`bc91220ca2`](https://github.com/nodejs/node/commit/bc91220ca2)] - **test**: add tls write error regression test (Shigeki Ohtsu) [nodejs-private/node-private#131](https://github.com/nodejs-private/node-private/pull/131)
- [[`acd11b01c4`](https://github.com/nodejs/node/commit/acd11b01c4)] - **test**: add regression test for nghttp2 CVE-2018-1000168 (James M Snell) [nodejs-private/node-private#125](https://github.com/nodejs-private/node-private/pull/125)

Windows 32-bit Installer: https://nodejs.org/dist/v8.11.3/node-v8.11.3-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v8.11.3/node-v8.11.3-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v8.11.3/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v8.11.3/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v8.11.3/node-v8.11.3.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v8.11.3/node-v8.11.3-darwin-x64.tar.gz \
Linux 32-bit Binary: https://nodejs.org/dist/v8.11.3/node-v8.11.3-linux-x86.tar.xz \
Linux 64-bit Binary: https://nodejs.org/dist/v8.11.3/node-v8.11.3-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v8.11.3/node-v8.11.3-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v8.11.3/node-v8.11.3-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v8.11.3/node-v8.11.3-aix-ppc64.tar.gz \
SmartOS 32-bit Binary: https://nodejs.org/dist/v8.11.3/node-v8.11.3-sunos-x86.tar.xz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v8.11.3/node-v8.11.3-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v8.11.3/node-v8.11.3-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v8.11.3/node-v8.11.3-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v8.11.3/node-v8.11.3-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v8.11.3/node-v8.11.3.tar.gz \
Other release files: https://nodejs.org/dist/v8.11.3/ \
Documentation: https://nodejs.org/docs/v8.11.3/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

3933bbdbc678cf237072b0429f0552e01c77fe0a6118da907bff81769d2c801e  node-v8.11.3-aix-ppc64.tar.gz
77fa26b4c2fc34bdf5a5dd1cd39c93b12087fbd25148c6f04bf409698ee48b86  node-v8.11.3-darwin-x64.tar.gz
7eac0bf398cb6ecf9f84dfc577ee84eee3d930f7a54b7e50f56d1a358b528792  node-v8.11.3-darwin-x64.tar.xz
36bf155f2c42bb3628f146a35fcf2ce812ba29f5d57bbec4780787fe282f9b80  node-v8.11.3-headers.tar.gz
cf42ee988b81b5fd0744f121caa16c7e3fc689137aad66c12eff4d8ac3ebc158  node-v8.11.3-headers.tar.xz
27bbee0710a798f61fab945dc22d4680926d0a679e293f285ff06bb86142b086  node-v8.11.3-linux-arm64.tar.gz
b8fddec18f20533929a07bc1d38ae63b1999a0252740094f0974b2cbea76eaa4  node-v8.11.3-linux-arm64.tar.xz
4e44edd4830159fe026ae2a240308d590e4e3930d794370c2cfcb4c6c1b6db30  node-v8.11.3-linux-armv6l.tar.gz
7eec60b638843f0336759dd6cc6fa236c103ce9a10e137e6a0b3a82bb7e59f9b  node-v8.11.3-linux-armv6l.tar.xz
3ab2037f7de2be2021e9b2eccad4a4480f6ec66cad8d7b344ac6a8aeba6908ca  node-v8.11.3-linux-armv7l.tar.gz
7a2bb6e37615fa45926ac0ad4e5ecda4a98e2956e468dedc337117bfbae0ac68  node-v8.11.3-linux-armv7l.tar.xz
5c73e55c748c3176746f4e3c278646b44a39e3526c72767a74ce9444165e9e80  node-v8.11.3-linux-ppc64le.tar.gz
6a29ea871e2288dc83d79473bd7b6702a1da126c1a5c900247344252970cb87e  node-v8.11.3-linux-ppc64le.tar.xz
3bdb471bbf28478ea82a184193d2fd20d9a5fa5bfe962ecd87a6b4d06a20bd9a  node-v8.11.3-linux-s390x.tar.gz
4ba85841c7c31e2846bfaf4346e3c6419e30318bc42677f60bb0d1d5dedebcfd  node-v8.11.3-linux-s390x.tar.xz
1ea408e9a467ed4571730e160993f67a100e8c347f6f9891c9a83350df2bf2be  node-v8.11.3-linux-x64.tar.gz
08e2fcfea66746bd966ea3a89f26851f1238d96f86c33eaf6274f67fce58421a  node-v8.11.3-linux-x64.tar.xz
7fe0d547baaa6334c1b14cfad8f4dc707b58b3568659b7466894ff26030944e5  node-v8.11.3-linux-x86.tar.gz
79fc372d8cb2d9d352bad00ae6630994fde802767fafff2dc411f37a3e84a5d3  node-v8.11.3-linux-x86.tar.xz
e4c21b34356cc734034d6b19431e8c4d191b3eac698cad23b75dc99bf34398f9  node-v8.11.3.pkg
a49a53d12385eb841e49091c6b94bc38c1bf9696ddc5f41ae30f5b4c829b0892  node-v8.11.3-sunos-x64.tar.gz
04f0fe764f6b6ca6a2dfa9d868ca6df4986fd215c344f2e06d06a005516eb11e  node-v8.11.3-sunos-x64.tar.xz
93bf636a0bcbfd4d95c3e9686a8e16b3c99e4fe8f6dda13e746e7745ea6d5bab  node-v8.11.3-sunos-x86.tar.gz
b78288b650e025916087847158185cee41cbbd644fe110b97197870f87873b50  node-v8.11.3-sunos-x86.tar.xz
0d7e795c0579226c8b197353bbb9392cae802f4fefa4787a2c0e678beaf85cce  node-v8.11.3.tar.gz
577c751fdca91c46c60ffd8352e5b465881373bfdde212c17c3a3c1bd2616ee0  node-v8.11.3.tar.xz
87ea95cef11a0c840556a164a664917ddaebde76e2dac6607c519d56ea68d5f8  node-v8.11.3-win-x64.7z
91b779def1b21dcd1def7fc9671a869a1e2f989952e76fdc08a5d73570075f31  node-v8.11.3-win-x64.zip
df3649ce8fed94c474491d378f1385b949a6208d2f6a6b4097e081ab55501c4e  node-v8.11.3-win-x86.7z
9482a0ad7aa5cd964cbeb11a605377b5c5aae4eae952c838aecf079de6088dc6  node-v8.11.3-win-x86.zip
d7e3057e921803159266ff1340283420a3e5053bad0c3ff951d8946a8f9aa66b  node-v8.11.3-x64.msi
66d1750f03f9319312843e8e80764625c8157600c1add99cba92c783cf9ff02a  node-v8.11.3-x86.msi
8ec35e8b61e08e3c09547ab52101e480285761baa9d35d707866540942ae0b0d  win-x64/node.exe
dd212906d408a23a1e7f32ab67c962aac33ed911b591d482cb37df390d536d48  win-x64/node.lib
ee8480f5c41973b41abc95fdd8cd2034a7484f5979d174a217d041da86464a12  win-x64/node_pdb.7z
80279ed44f5e4a332d36060078632af8723d0935eddda4b1a7a1e97a121d53fa  win-x64/node_pdb.zip
e7a5aa4d5b06aba4ab68a4c57d899800550cd05428ab1d74336170b98c4fe96a  win-x86/node.exe
244daecad206b29215dfaab70bb73eb56f48f43c780ba4b70d0bf0046f9514f2  win-x86/node.lib
bac3cfe70b1c4802bc2b751ba7d1e860769c2899018ed81c9f47fc045a7d5a16  win-x86/node_pdb.7z
aba100bac88199c457dec75571a2078b1ec3ee57670587cd0c12ce5a3fed9c4c  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEua6ZBf/XgD8lcUZhtjtTWkwgbKkFAlsgVqwACgkQtjtTWkwg
bKmXIA//fZJ3l7tdF4jZyRNWZ4Gk6+aZsOMfXBD/YM3wD4owpg9Y2LRP27MDlpkf
/o+zCzGdJeOTWC8yA9RM2GPomm4grHgnexZOWhjrAhV1xKWw/S2CFlIA4272H61u
jBZnVVNe/30l/SH6vdf9VUVbGZfEvEOTQO9SMJcDLLqG+A+Vy+iUmTK/Y5Kbrpjd
5w64AeMKdKUv3FWVrXgGGsnV2VHem6dDj/ABOXGENvvMSucSWSFXNytV9v4dBHcH
whTl0kt+xwH6ycrNJk6PJJBguMArOno/Fb9+uKZ1sdc1G2vPoRTJuRztY7UAch0e
kYYs3lpJIMk85CFyAacLnrDXIz7sC6ws6orn9ZH6fwbEc+KrNEPKJtKHUJYq8mr3
e8MwXvojurHOQmcyFLaROoMgYizCbA8vMuBc5GJj3SHgAaXd1hTQ1rNGxHLRZI7u
e8IprOCZUaTPvlu6Ch9DE1NJrQBeP/7wAu76his8rO9elYcb2w4glOQLE0wILTIN
MFo5YqiEBp5zdhERU5mcilT67S1lUS3FqCdeGQHHZ/Y+jZDBYcW7S6aM/NkvFQ8g
b8x+TYPoZlwXB8PTyz+2BdxzFUy7+2QoRVNYGySyxp7f8d6TNZ7v04rXqWA1ZHTq
hiIM6vhk/suhIpu0nfoRVMDozWLsy8wJqJVuBNSLEhKlPy9op+k=
=Lz/E
-----END PGP SIGNATURE-----

```
