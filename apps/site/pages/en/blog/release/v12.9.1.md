---
date: '2019-08-26T19:44:51.589Z'
category: release
title: Node v12.9.1 (Current)
layout: blog-post
author: <PERSON><PERSON><PERSON>
---

### Notable changes

This release fixes two regressions in the **http** module:

- Fixes an event listener leak in the HTTP client. This resulted in lots of
  warnings during npm/yarn installs (<PERSON>) [#29245](https://github.com/nodejs/node/pull/29245).
- Fixes a regression preventing the `'end'` event from being emitted for
  keepalive requests in case the full body was not parsed (<PERSON>) [#29263](https://github.com/nodejs/node/pull/29263).

### Commits

- [[`3cc8fca299`](https://github.com/nodejs/node/commit/3cc8fca299)] - **crypto**: handle i2d_SSL_SESSION() error return (<PERSON>) [#29225](https://github.com/nodejs/node/pull/29225)
- [[`ae0a0e97ba`](https://github.com/nodejs/node/commit/ae0a0e97ba)] - **http**: reset parser.incoming when server request is finished (<PERSON>) [#29297](https://github.com/nodejs/node/pull/29297)
- [[`dedbd119c5`](https://github.com/nodejs/node/commit/dedbd119c5)] - **http**: fix event listener leak (<PERSON> Nagy) [#29245](https://github.com/nodejs/node/pull/29245)
- [[`f8f8754d43`](https://github.com/nodejs/node/commit/f8f8754d43)] - **_Revert_** "**http**: reset parser.incoming when server response is finished" (Matteo Collina) [#29263](https://github.com/nodejs/node/pull/29263)
- [[`a6abfcb423`](https://github.com/nodejs/node/commit/a6abfcb423)] - **src**: remove unused using declarations (Daniel Bevenius) [#29222](https://github.com/nodejs/node/pull/29222)
- [[`ff6330a6ac`](https://github.com/nodejs/node/commit/ff6330a6ac)] - **test**: fix 'timeout' typos (cjihrig) [#29234](https://github.com/nodejs/node/pull/29234)
- [[`3c7a1a9090`](https://github.com/nodejs/node/commit/3c7a1a9090)] - **test, http**: add regression test for keepalive 'end' event (Matteo Collina) [#29263](https://github.com/nodejs/node/pull/29263)

Windows 32-bit Installer: https://nodejs.org/dist/v12.9.1/node-v12.9.1-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v12.9.1/node-v12.9.1-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v12.9.1/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v12.9.1/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v12.9.1/node-v12.9.1.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v12.9.1/node-v12.9.1-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v12.9.1/node-v12.9.1-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v12.9.1/node-v12.9.1-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v12.9.1/node-v12.9.1-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v12.9.1/node-v12.9.1-aix-ppc64.tar.gz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v12.9.1/node-v12.9.1-sunos-x64.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v12.9.1/node-v12.9.1-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v12.9.1/node-v12.9.1-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v12.9.1/node-v12.9.1.tar.gz \
Other release files: https://nodejs.org/dist/v12.9.1/ \
Documentation: https://nodejs.org/docs/v12.9.1/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

76c66b55bd2a923579333c2d24429e10d50f4e259170cdd6e413214febb9df55  node-v12.9.1-aix-ppc64.tar.gz
9aaf29d30056e2233fd15dfac56eec12e8342d91bb6c13d54fb5e599383dddb9  node-v12.9.1-darwin-x64.tar.gz
84b8e3684ff8e30c0735cddc94b3820e0dca4a683aa46834fd711098a0f6a8f3  node-v12.9.1-darwin-x64.tar.xz
8f68a9a53a5c3351c90d295258599737de8afca66e3fe41991576efb6a35b4c5  node-v12.9.1-headers.tar.gz
9fe1461acef13aa3c02afaae05984ca77e1de9be7348a1fc7a2d550072b81816  node-v12.9.1-headers.tar.xz
a09e7c54f05036ddf260f9a6762d72669e428810f814ad189519fe5adad0bd2d  node-v12.9.1-linux-arm64.tar.gz
b6d986faf3a77b6c353c344645c93f2a0e0436c43865e6c215a24301a076a11f  node-v12.9.1-linux-arm64.tar.xz
b81af82e2c0cfd0ec20d1ca4622d7ce602435458459b056527d46f1a35ba9050  node-v12.9.1-linux-armv7l.tar.gz
11a10a59e1099296d28e24e5b200774d5196932369e5be09ee3d0cc1c7d276c2  node-v12.9.1-linux-armv7l.tar.xz
1febb5758d96f6b05c6e86d2520d9d658065fae98da9273248caca0ddde27878  node-v12.9.1-linux-ppc64le.tar.gz
ad6d9058f25cbe5edf1a12e6eeb527cb17ba2eadcbb406996ad82d1c27fe29e2  node-v12.9.1-linux-ppc64le.tar.xz
df3d69a65bc9efbfb36187522061c6fb67c59362e46693c719b88f1b882b0b00  node-v12.9.1-linux-s390x.tar.gz
77bb8e58ed7d196f934159e952d1598984b900094f5948e0a5a4a67489a9e711  node-v12.9.1-linux-s390x.tar.xz
5488e9d9e860eb344726aabdc8f90d09e36602da38da3d16a7ee852fd9fbd91f  node-v12.9.1-linux-x64.tar.gz
680a1263c9f5f91adadcada549f0a9c29f1b26d09658d2b501c334c3f63719e5  node-v12.9.1-linux-x64.tar.xz
06eeede83ff403f1af18acf16b007e652d14ff1c12ef19f6866722f091cacccf  node-v12.9.1.pkg
51bfbfc7755cf7901de9acc4b3f3baf38900af5a4f38e25a61427d6c3772ebbe  node-v12.9.1-sunos-x64.tar.gz
0277eaf191b8887983957d8fff3292512c5a057329384a9aa39b5c00070b66a7  node-v12.9.1-sunos-x64.tar.xz
9150b2b3c728e022c596ab86188c685facc9f30f4c8e60c1a0f9d5a3e8e3653f  node-v12.9.1.tar.gz
c245822a9d5204e821646508244c835e386e11c3eda4cb5261cde83517ff545e  node-v12.9.1.tar.xz
631af2dc0e0a5966dbd736e27ea01b186f6d3f22fa2e569e7e3407ef3d96b689  node-v12.9.1-win-x64.7z
6a4e54bda091bd02dbd8ff1b9f6671e036297da012a53891e3834d4bf4bed297  node-v12.9.1-win-x64.zip
8ffd55204b9d00d58456c4ae22e8ea9db440a9755a02f40ea1ee8321c50dd5cd  node-v12.9.1-win-x86.7z
346dc4ac0ad07783dafd8f9a0f55538cf7e9cdf62e14df1a960a6acb5f39d795  node-v12.9.1-win-x86.zip
e1c25dd10b9e1d68768842ba8892ba18ca6e1e14d5be7ade86c035c7f02a02fa  node-v12.9.1-x64.msi
9a1d1fa6e71f5f9860b1c09275c7d38255029864b489aded09dfaa4fbe9bd8b5  node-v12.9.1-x86.msi
a3219cdb26c895d8b396ec438f7dadfbb909f8b6e4069bcbcfc41192af8e3682  win-x64/node.exe
654cb8682984cbed2ca698e5b3ebd05edaa60174d964432143bdfc0c38254aa7  win-x64/node.lib
f54cfea1bc2b3872275ffe6f517ff08c17ee3f62d756ed45f909bddc4fc32cc3  win-x64/node_pdb.7z
3896c478a8c6d0091c859a0afa234ce39d2d54e0734dfd4a05e3d111cda4e9ac  win-x64/node_pdb.zip
4b8e1f7d1ca6ce6669674a32a3274042867d24a795779b7c32d9c27f967aee82  win-x86/node.exe
df075eff064bd3e081d848484b4b7c99b3a022a8a8e5988b2d17dbf4d0489a56  win-x86/node.lib
05c2fc8c19770f98b7da8ede070e7b247dcb671db6207e030b8a8d962249679d  win-x86/node_pdb.7z
c4c7c649ba6a88361c94d087581e2ed2f097eabfee3a737d33e3d57337274eea  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEj8yhP+8dDC6RAI4Jdw96mlrhVgAFAl1kJuIACgkQdw96mlrh
VgDvchAAhy+O22DXHUgiADuiKb3/yWx2oCb52MzLBc7Gi2lCm9IIe6Xx2aJCq2XQ
SDoE6apEIsBGe0hPzkRLQb5PUaAkXwyE7L2OUqmxZNqf3aIlMOqY7RKTyIRCmA3M
PyVPwIMChbiiODTfG7jA1Rop2Y2NAAUC8IfxGGQNGTm5SAMdiZ1KLYl+YRIKDnv5
nnf27gmpA6DEIbL1D/8CSEcTXMVrporpCkuoRQ6eK6s96Jy+3ps4x0TbkbVUVSOn
Fnp8IgUMtQBHh4vc4vYhWOG8G7ZGjksbIW6q+IjBEs8XeKXXEtAVNHUTXnwflo+m
u4dZl8JrLoG8qKe8S7Be5ps1OmZnjbDCxraGtpCnQP9OADUHEC9DRDYTvM/a5teX
wH9sUjh/10nfJfNeWiaTCXr6P9GyuYPTYKXkNOihvVIlGI+vV27L16qawGvpdwwW
4QeX5lVLHj8MQE1ZzQEzs/F1ThlhoEaixXb7D9fEbpesENeaoSEF2/ZOQ1w1VwFr
9Ecl+/uC/KPlaEcQOm5xVy9dSrRyTPAI3vpNyGzpKZrlFcfn6oUOTLa4gvGTozB6
kMvTGobNtQzypUeNSZvY9AamJZL2ZgmIO2rGNK10eyMqjmjGZXkVrNkasuc5Ew6O
ErlBVaythMxuJ+sVohlNf033rGS7U8Fu812VZo0Q2kg/g3IzcNE=
=5MCI
-----END PGP SIGNATURE-----

```
