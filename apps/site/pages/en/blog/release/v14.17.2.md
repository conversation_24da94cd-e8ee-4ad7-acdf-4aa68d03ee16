---
date: '2021-07-01T15:44:53.509Z'
category: release
title: Node v14.17.2 (LTS)
layout: blog-post
author: <PERSON>
---

### Notable Changes

Vulnerabilities fixed:

- **CVE-2021-22918**: libuv upgrade - Out of bounds read (Medium)
  - Node.js is vulnerable to out-of-bounds read in libuv's uv\_\_idna_toascii() function which is used to convert strings to ASCII. This is called by <PERSON>de's dns module's lookup() function and can lead to information disclosures or crashes. You can read more about it in https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2021-22918
- **CVE-2021-22921**: Windows installer - Node Installer Local Privilege Escalation (Medium)
  - Node.js is vulnerable to local privilege escalation attacks under certain conditions on Windows platforms. More specifically, improper configuration of permissions in the installation directory allows an attacker to perform two different escalation attacks: PATH and DLL hijacking. You can read more about it in https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2021-22921

### Commits

- [[`a7496aba0a`](https://github.com/nodejs/node/commit/a7496aba0a)] - **deps**: uv: cherry-pick 99c29c9c2c9b (Ben Noordhuis) [nodejs-private/node-private#267](https://github.com/nodejs-private/node-private/pull/267)
- [[`d0b449da1d`](https://github.com/nodejs/node/commit/d0b449da1d)] - **win,msi**: set install directory permission (AkshayK) [nodejs-private/node-private#269](https://github.com/nodejs-private/node-private/pull/269)

Windows 32-bit Installer: https://nodejs.org/dist/v14.17.2/node-v14.17.2-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v14.17.2/node-v14.17.2-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v14.17.2/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v14.17.2/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v14.17.2/node-v14.17.2.pkg \
macOS Intel 64-bit Binary: https://nodejs.org/dist/v14.17.2/node-v14.17.2-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v14.17.2/node-v14.17.2-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v14.17.2/node-v14.17.2-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v14.17.2/node-v14.17.2-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v14.17.2/node-v14.17.2-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v14.17.2/node-v14.17.2-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v14.17.2/node-v14.17.2-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v14.17.2/node-v14.17.2.tar.gz \
Other release files: https://nodejs.org/dist/v14.17.2/ \
Documentation: https://nodejs.org/docs/v14.17.2/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

fb4348515d67085153c58d7b5114ca71690e3938d6c6000a02a7977cf154290a  node-v14.17.2-aix-ppc64.tar.gz
e45db91fc2136202868a5eb7c6d08b0a2b75394fafdf8538f650fa945b7dee16  node-v14.17.2-darwin-x64.tar.gz
1b5227ec537a456ce4f3af9631db652e7fd3ab8ac7b9a38be117e0bc2f54d1d5  node-v14.17.2-darwin-x64.tar.xz
ccda5c3ce6dd51e6901cd227c1e6b39b17efa1dfe17fcc5ae1cde15d88cbb05c  node-v14.17.2-headers.tar.gz
935e8661db59ab51e4079fbcafd70185ad65ac0924cc48209d976faf69a3b286  node-v14.17.2-headers.tar.xz
05117e74f424fd4ab744c3013c77906c5fe4a19fa22ce624a21986ce152fd258  node-v14.17.2-linux-arm64.tar.gz
3aff08c49b8c0c3443e7a9ea9bfe607867d79e6e5ccf204a5c8f13fb92a48abd  node-v14.17.2-linux-arm64.tar.xz
c5b3447eda84a402e604a3ca317747f8f3661b1c9cb68f73a2e2f5a39ed95533  node-v14.17.2-linux-armv7l.tar.gz
0ef956711d6f984b2f7a2e8a3c5d1f274668fd50ba73a4f4580dc72e4d7dff87  node-v14.17.2-linux-armv7l.tar.xz
d94681c9953a68cfbc3903edbe680d9068581c140a5d24c265e557c2e0a57a51  node-v14.17.2-linux-ppc64le.tar.gz
64364f96ff04083bf91ee82a2e27ae6d45a4b8512d38e70da976909db5400d2a  node-v14.17.2-linux-ppc64le.tar.xz
9b60ec8bb3675f3648b1474a4d911838ca11630e07ec99230a078937e73be158  node-v14.17.2-linux-s390x.tar.gz
76f955856626a3e596b438855fdfe438937623dc71af9a25a8466409be470877  node-v14.17.2-linux-s390x.tar.xz
48cc87b7adb13f479643166a16514861556d0936761b317a3b65f4fbbb265b4d  node-v14.17.2-linux-x64.tar.gz
6cf9db7349407c177b36205feec949729d0ee9db485e19b10b0b1ffca65a3a46  node-v14.17.2-linux-x64.tar.xz
16286b9d41238dc85af791aafc71523cdc8456cd7eccebdf92f8368ce879e363  node-v14.17.2.pkg
03bea54a68c6fa8b520c273a1dc80f2fdbf80ddc9c303200d2eeaf25bd7e62fa  node-v14.17.2.tar.gz
4f5fb2b87e2255da3b200ed73ab076002e4b088df0027ec85e25541a6830583e  node-v14.17.2.tar.xz
6f59257b20861efb506f9b7f5b8587a310c6df6de4d719324ae8f3dcda68bcbc  node-v14.17.2-win-x64.7z
0e27897578752865fa61546d75b20f7cd62957726caab3c03f82c57a4aef5636  node-v14.17.2-win-x64.zip
b5b2f6d2523b0c60399111b4c60ee3066d6e7e51fe22ce045c5181d52dd2dd20  node-v14.17.2-win-x86.7z
308e3e2228cd6b0e991fd9d23dcd0c57f913926355adcb702f9660a1574a79fa  node-v14.17.2-win-x86.zip
452a94f6db29ea6ef298cb731765c615624eed2c8bd8746c827d5abbed89a5de  node-v14.17.2-x64.msi
92443d36339a76f39102e785ecf663a072d26c9c45a2b4b68ddb82ae54345682  node-v14.17.2-x86.msi
140fd660d63eab02812ec583a4c958c9252f041a92d76047c63ac34d773aab66  win-x64/node.exe
2166a3ae8728f872727ced97eaccdc8e463ae3c00b7dcb94c3b9da80feedb735  win-x64/node.lib
a79264dcce6c4f6aba147aee4bfd9e6b69b4134f07e487d381efecf771f7e275  win-x64/node_pdb.7z
2a57321f0767c3a89a8dfd5355ac2c0ccdec728da97a8f5bd3bd53ea4252eb0e  win-x64/node_pdb.zip
2695c86b7000623119abfd7b9aed48c04d5ae4247ea7398ff2c77f61907d531f  win-x86/node.exe
bab3fdbacbd6621e27762c559041723dc35d3122ddfd153f81315aaab78646ae  win-x86/node.lib
dc5a8c41b0ceaea49dadd0ff53b25837809a90e75222b3f34c5c01325a52bd26  win-x86/node_pdb.7z
f5e6ae2fcc35f94749e8404f909734649e5617ac3e41ce326fb2601cbf72c862  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEyC+jrhy+3Gvka5NgxDzsRcF6uTwFAmDd4UIACgkQxDzsRcF6
uTwCdQ//ar0hKWBOIKQUFpAuAFGvrlZRz6jPdRVb+4VK6NahRDNf0fbyQjepOcI0
sDTRptjlgPOYpvNTKUHr7BaMCPsPnpw6rVbdVpcXhYf3ZqCd2xyVBte8zqTPbHkp
RHoEwG+Qw/qVt6BKEFfONfw437eTySMBPFzMjKbyTBrU/LgY4esMTSlsnwPy1R6M
CblH3M6kIvMadA1DN4yKH751IXX3/nvW+rwo/eNIiAfEzwFkXMcL73u5+rhq9SZG
SLT7jyM+6qRAGPGF1uxG/nnj4tjCSkAWF9v5WKyElrJfJxga1rJECMr8KaBXcQW0
KPoeKIqEhtDgxWwTRCkghXYSAl1uLqnx7AU3XPrT557kGCPr2FcV0V3+McQ6T0Cc
8AXA9gNjlEgU2BGe8p3VUyCwjfFXfnNdPcD3OaZvoiMGWIB9Ty1sJeGmvse783ID
KIoc+qnmB966Qh1WvTdl3S5V9sVVXgk9xUOToXpbfFoUDXMmsQe5lwr4VB6YHVGt
8f0iM8dVfkjA2qc1tgfmTyPMMlPIGB1iNVwMuSm4WCpe924py0boI+j+dp6NGhrn
YymRG1cER45uNmFLN8Q30mDAFG5pWAsC2UMgZYzqFXBL0PdTrtYSL56vQkD6RLgT
aD3s8r/vtxQ0gZZQW8s7Zg9OXHSbPZRXjg31OpTQPrk2BsTAkfM=
=08xu
-----END PGP SIGNATURE-----

```
