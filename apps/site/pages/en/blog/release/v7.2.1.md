---
date: '2016-12-06T22:58:15.249Z'
category: release
title: Node v7.2.1 (Current)
layout: blog-post
author: <PERSON>
---

### Notable changes

- **buffer**:
  - Reverted the runtime deprecation of calling `<PERSON><PERSON><PERSON>()` without `new`. (<PERSON>) [#9529](https://github.com/nodejs/node/pull/9529)
  - Fixed `buffer.transcode()` for single-byte character
    encodings to `UCS2`. (<PERSON>) [#9838](https://github.com/nodejs/node/pull/9838)
- **promise**: `--trace-warnings` now produces useful stacktraces for Promise warnings. (<PERSON>) [#9525](https://github.com/nodejs/node/pull/9525)
- **repl**: Fixed a bug preventing correct parsing of generator functions. (<PERSON>) [#9852](https://github.com/nodejs/node/pull/9852)
- **V8**: Fixed a significant `instanceof` performance regression. (<PERSON><PERSON><PERSON>) [#9730](https://github.com/nodejs/node/pull/9730)

### Commits

- [[`f55a63c86f`](https://github.com/nodejs/node/commit/f55a63c86f)] - internal/util: move the case 'latin1' (Jackson Tian) [#9646](https://github.com/nodejs/node/pull/9646)
- [[`5379b9da11`](https://github.com/nodejs/node/commit/5379b9da11)] - **async_wrap**: call destroy() callback in `uv_idle_t` (Trevor Norris) [#9753](https://github.com/nodejs/node/pull/9753)
- [[`5157a5cee9`](https://github.com/nodejs/node/commit/5157a5cee9)] - **async_wrap**: make Initialize a static class member (Trevor Norris) [#9753](https://github.com/nodejs/node/pull/9753)
- [[`3e5be7fc8b`](https://github.com/nodejs/node/commit/3e5be7fc8b)] - **async_wrap**: mode constructor/destructor to .cc (Trevor Norris) [#9753](https://github.com/nodejs/node/pull/9753)
- [[`88464ac6ac`](https://github.com/nodejs/node/commit/88464ac6ac)] - **benchmark**: reformat code for clarity (Rich Trott) [#9790](https://github.com/nodejs/node/pull/9790)
- [[`573f9db6c9`](https://github.com/nodejs/node/commit/573f9db6c9)] - **buffer**: fix transcode for single-byte enc to ucs2 (Anna Henningsen) [#9838](https://github.com/nodejs/node/pull/9838)
- [[`0c745e3a3a`](https://github.com/nodejs/node/commit/0c745e3a3a)] - **buffer**: convert offset & length to int properly (Sakthipriyan Vairamani (thefourtheye)) [#9815](https://github.com/nodejs/node/pull/9815)
- [[`e0e62d1113`](https://github.com/nodejs/node/commit/e0e62d1113)] - **_Revert_** "**buffer**: runtime deprecation of calling Buffer without new" (Anna Henningsen) [#9529](https://github.com/nodejs/node/pull/9529)
- [[`371090d817`](https://github.com/nodejs/node/commit/371090d817)] - **build**: Make configure file parseable on python3 (kalrover) [#9657](https://github.com/nodejs/node/pull/9657)
- [[`16af467146`](https://github.com/nodejs/node/commit/16af467146)] - **build**: add shared library support to AIX build (Stewart Addison) [#9675](https://github.com/nodejs/node/pull/9675)
- [[`fa38032148`](https://github.com/nodejs/node/commit/fa38032148)] - **child_process**: name anonymous functions (brad-decker) [#9880](https://github.com/nodejs/node/pull/9880)
- [[`5c9aa18484`](https://github.com/nodejs/node/commit/5c9aa18484)] - **constants**: errors -> errno (Bryan English) [#9349](https://github.com/nodejs/node/pull/9349)
- [[`dfa35d66f5`](https://github.com/nodejs/node/commit/dfa35d66f5)] - **debugger**: call `this.resume()` after `this.run()` (Lance Ball) [#10099](https://github.com/nodejs/node/pull/10099)
- [[`ac8d212428`](https://github.com/nodejs/node/commit/ac8d212428)] - **debugger**: refactor `_debugger.js` (Rich Trott) [#9860](https://github.com/nodejs/node/pull/9860)
- [[`4bcda633c0`](https://github.com/nodejs/node/commit/4bcda633c0)] - **deps**: upgrade npm to 3.10.10 (Rebecca Turner) [#9847](https://github.com/nodejs/node/pull/9847)
- [[`03b1c314cd`](https://github.com/nodejs/node/commit/03b1c314cd)] - **deps**: cherry-pick 08377af from v8 upstream (Franziska Hinkelmann) [#9730](https://github.com/nodejs/node/pull/9730)
- [[`e9c2ffd20c`](https://github.com/nodejs/node/commit/e9c2ffd20c)] - **deps**: backport GYP fix to fix AIX shared suffix (Stewart Addison)
- [[`3bc40ce725`](https://github.com/nodejs/node/commit/3bc40ce725)] - **doc**: remove repeated info onboarding.md (BethGriggs) [#9635](https://github.com/nodejs/node/pull/9635)
- [[`446bcbea4e`](https://github.com/nodejs/node/commit/446bcbea4e)] - **doc**: correct it's vs. its usage (Rich Trott) [#10098](https://github.com/nodejs/node/pull/10098)
- [[`b9bd9a2fcb`](https://github.com/nodejs/node/commit/b9bd9a2fcb)] - **doc**: remove Sam Roberts from release team (Sam Roberts) [#9862](https://github.com/nodejs/node/pull/9862)
- [[`51b77aa44a`](https://github.com/nodejs/node/commit/51b77aa44a)] - **doc**: add people to cc for async_wrap (Anna Henningsen) [#9471](https://github.com/nodejs/node/pull/9471)
- [[`346204d77e`](https://github.com/nodejs/node/commit/346204d77e)] - **doc**: add link to `net.Server` in tls.md (Devon Rifkin) [#10109](https://github.com/nodejs/node/pull/10109)
- [[`c4fbdfa785`](https://github.com/nodejs/node/commit/c4fbdfa785)] - **doc**: fix typo for `decipher.final`. (iamchenxin) [#10086](https://github.com/nodejs/node/pull/10086)
- [[`d226418b87`](https://github.com/nodejs/node/commit/d226418b87)] - **doc**: suggest Buffer.alloc instead of Buffer#fill (Teddy Katz) [#10000](https://github.com/nodejs/node/pull/10000)
- [[`78e188d929`](https://github.com/nodejs/node/commit/78e188d929)] - **doc**: clarify fs.createReadStream options (Wes Tyler) [#10078](https://github.com/nodejs/node/pull/10078)
- [[`cdec174d4d`](https://github.com/nodejs/node/commit/cdec174d4d)] - **doc**: var => const in js code examples of addons.md (Vse Mozhet Byt) [#10092](https://github.com/nodejs/node/pull/10092)
- [[`13eea40d6f`](https://github.com/nodejs/node/commit/13eea40d6f)] - **doc**: rename writing_tests.md to writing-tests.md (Safia Abdalla) [#9867](https://github.com/nodejs/node/pull/9867)
- [[`c948d9051b`](https://github.com/nodejs/node/commit/c948d9051b)] - **doc**: it’s -> its in api/child_process.md (Devon Rifkin) [#10090](https://github.com/nodejs/node/pull/10090)
- [[`f6c1f24068`](https://github.com/nodejs/node/commit/f6c1f24068)] - **doc**: update Collaborators list in README (Rich Trott) [#9846](https://github.com/nodejs/node/pull/9846)
- [[`a0e25b2544`](https://github.com/nodejs/node/commit/a0e25b2544)] - **doc**: remove minor contradiction in debugger doc (Rich Trott) [#9832](https://github.com/nodejs/node/pull/9832)
- [[`8c70f79249`](https://github.com/nodejs/node/commit/8c70f79249)] - **doc**: clarify introductory module material (Rich Trott) [#9816](https://github.com/nodejs/node/pull/9816)
- [[`2e22fa043d`](https://github.com/nodejs/node/commit/2e22fa043d)] - **doc**: improve description of module `exports` (Sam Roberts) [#9622](https://github.com/nodejs/node/pull/9622)
- [[`6ab920a3fc`](https://github.com/nodejs/node/commit/6ab920a3fc)] - **doc**: add guide for maintaining V8 (Ali Ijaz Sheikh) [#9777](https://github.com/nodejs/node/pull/9777)
- [[`4fa84c9589`](https://github.com/nodejs/node/commit/4fa84c9589)] - **doc**: fix crypto Verify cut-n-paste from Sign (子丶言) [#9796](https://github.com/nodejs/node/pull/9796)
- [[`6297b9afc5`](https://github.com/nodejs/node/commit/6297b9afc5)] - **doc**: minor fixes event-loop-timers-and-nexttick.md (Dan Koster) [#9126](https://github.com/nodejs/node/pull/9126)
- [[`a8d84d5b50`](https://github.com/nodejs/node/commit/a8d84d5b50)] - **doc**: changed order of invocations in https.request() example. (atrioom) [#9614](https://github.com/nodejs/node/pull/9614)
- [[`c7cd400fcb`](https://github.com/nodejs/node/commit/c7cd400fcb)] - **doc**: fix crypto "decipher.setAAD()" typo (子丶言) [#9782](https://github.com/nodejs/node/pull/9782)
- [[`77e145a00e`](https://github.com/nodejs/node/commit/77e145a00e)] - **doc**: clarify slashes-appending in url module (Rich Trott) [#9731](https://github.com/nodejs/node/pull/9731)
- [[`65af114267`](https://github.com/nodejs/node/commit/65af114267)] - **doc**: "util" is not needed to extend ES6 classes (Adam Brunner) [#9737](https://github.com/nodejs/node/pull/9737)
- [[`44ae0283af`](https://github.com/nodejs/node/commit/44ae0283af)] - **doc**: fix `<code>` inside stability boxes (Roman Reiss) [#9723](https://github.com/nodejs/node/pull/9723)
- [[`9554a974d1`](https://github.com/nodejs/node/commit/9554a974d1)] - **https**: name anonymous functions in https (Pedro Lima) [#9217](https://github.com/nodejs/node/pull/9217)
- [[`80a3934cd7`](https://github.com/nodejs/node/commit/80a3934cd7)] - **inspector**: /json/version returns object, not array (Ben Noordhuis) [#9762](https://github.com/nodejs/node/pull/9762)
- [[`65cda7f265`](https://github.com/nodejs/node/commit/65cda7f265)] - **lib**: use === in `_http_server` and `_tls_wrap` (Walter Beller-Morales) [#9849](https://github.com/nodejs/node/pull/9849)
- [[`a673d44d68`](https://github.com/nodejs/node/commit/a673d44d68)] - **lib,tools**: remove unneeded escaping of / (Prince J Wesley) [#9591](https://github.com/nodejs/node/pull/9591)
- [[`3253954e62`](https://github.com/nodejs/node/commit/3253954e62)] - **meta**: whitelist dotfiles in .gitignore (Claudio Rodriguez) [#8016](https://github.com/nodejs/node/pull/8016)
- [[`cef3a04f62`](https://github.com/nodejs/node/commit/cef3a04f62)] - **promise**: better stack traces for --trace-warnings (Anna Henningsen) [#9525](https://github.com/nodejs/node/pull/9525)
- [[`a0f6cc718a`](https://github.com/nodejs/node/commit/a0f6cc718a)] - **repl**: avoid parsing division operator as regex (Teddy Katz) [#10103](https://github.com/nodejs/node/pull/10103)
- [[`6087e361e5`](https://github.com/nodejs/node/commit/6087e361e5)] - **repl**: preprocess only for defaultEval (Prince J Wesley) [#9752](https://github.com/nodejs/node/pull/9752)
- [[`9099664959`](https://github.com/nodejs/node/commit/9099664959)] - **repl**: fix generator function preprocessing (Teddy Katz) [#9852](https://github.com/nodejs/node/pull/9852)
- [[`9726c8271e`](https://github.com/nodejs/node/commit/9726c8271e)] - **test**: update parallel/test-crypto-hash.js (Deepti Agrawal) [#10009](https://github.com/nodejs/node/pull/10009)
- [[`7144f811a6`](https://github.com/nodejs/node/commit/7144f811a6)] - **test**: add test for url module domainToAscii and domainToUnicode (Daryl Thayil) [#10031](https://github.com/nodejs/node/pull/10031)
- [[`2f6d0c7e61`](https://github.com/nodejs/node/commit/2f6d0c7e61)] - **test**: refactor test-require-extensions-main (Daryl Thayil) [#9912](https://github.com/nodejs/node/pull/9912)
- [[`e718f2051c`](https://github.com/nodejs/node/commit/e718f2051c)] - **test**: refactor test-tls-ocsp-callback (k3kathy) [#9970](https://github.com/nodejs/node/pull/9970)
- [[`f5e622ea53`](https://github.com/nodejs/node/commit/f5e622ea53)] - **test**: use assert.strictEqual and fix setTimeout (Matt Phillips) [#9957](https://github.com/nodejs/node/pull/9957)
- [[`0a4fc64c3f`](https://github.com/nodejs/node/commit/0a4fc64c3f)] - **test**: clean up tls junk test (Danny Guo) [#9940](https://github.com/nodejs/node/pull/9940)
- [[`a3a664a321`](https://github.com/nodejs/node/commit/a3a664a321)] - **test**: update test-stdout-to-file (scalkpdev) [#9939](https://github.com/nodejs/node/pull/9939)
- [[`f531c96846`](https://github.com/nodejs/node/commit/f531c96846)] - **test**: changed assert.Equal to asset.strictEqual (Paul Chin) [#9973](https://github.com/nodejs/node/pull/9973)
- [[`843b8c1658`](https://github.com/nodejs/node/commit/843b8c1658)] - **test**: refactor test-domain-multi (Wes Tyler) [#9963](https://github.com/nodejs/node/pull/9963)
- [[`8936d835c1`](https://github.com/nodejs/node/commit/8936d835c1)] - **test**: refactor test-fs-write.js (hirabhullar) [#9982](https://github.com/nodejs/node/pull/9982)
- [[`2f731e5b5d`](https://github.com/nodejs/node/commit/2f731e5b5d)] - **test**: refactor test-child-fork-exec-path.js (hirabhullar) [#9982](https://github.com/nodejs/node/pull/9982)
- [[`d697ac404f`](https://github.com/nodejs/node/commit/d697ac404f)] - **test**: use assert.strictEqual in test-cli-eval (Nigel Kibodeaux) [#9919](https://github.com/nodejs/node/pull/9919)
- [[`0a07bccc5c`](https://github.com/nodejs/node/commit/0a07bccc5c)] - **test**: refactor test-tls-connect-simple (Russell Sherman) [#9934](https://github.com/nodejs/node/pull/9934)
- [[`371a785f6d`](https://github.com/nodejs/node/commit/371a785f6d)] - **test**: refactor test-signal-unregister (mark hughes) [#9920](https://github.com/nodejs/node/pull/9920)
- [[`79b36e927c`](https://github.com/nodejs/node/commit/79b36e927c)] - **test**: update test-net-connect-handle-econnrefused (Punit Buch) [#9932](https://github.com/nodejs/node/pull/9932)
- [[`ba7d1cf4bc`](https://github.com/nodejs/node/commit/ba7d1cf4bc)] - **test**: refactor test-require-resolve (blugavere) [#10120](https://github.com/nodejs/node/pull/10120)
- [[`1877ba3384`](https://github.com/nodejs/node/commit/1877ba3384)] - **test**: refactor test-fs-symlink-dir-junction (Walter Beller-Morales) [#9928](https://github.com/nodejs/node/pull/9928)
- [[`84813fdaf8`](https://github.com/nodejs/node/commit/84813fdaf8)] - **test**: refactor test-fs-read-stream-resume (Matt Webb) [#9927](https://github.com/nodejs/node/pull/9927)
- [[`f68bfc5bde`](https://github.com/nodejs/node/commit/f68bfc5bde)] - **test**: replace equal with strictEqual (Tracy Hinds) [#10011](https://github.com/nodejs/node/pull/10011)
- [[`c0eb08adbe`](https://github.com/nodejs/node/commit/c0eb08adbe)] - **test**: use strictEqual instead of equal (Uttam Pawar) [#9921](https://github.com/nodejs/node/pull/9921)
- [[`2e36b2ef49`](https://github.com/nodejs/node/commit/2e36b2ef49)] - **test**: using const and strictEqual (Fabrice Tatieze) [#9926](https://github.com/nodejs/node/pull/9926)
- [[`8e27254594`](https://github.com/nodejs/node/commit/8e27254594)] - **test**: convert assert.equal to assert.strictEqual (Jonathan Darling) [#9925](https://github.com/nodejs/node/pull/9925)
- [[`328cd93036`](https://github.com/nodejs/node/commit/328cd93036)] - **test**: changed assert.equal to assert.strictEqual (Scott Smereka) [#9936](https://github.com/nodejs/node/pull/9936)
- [[`cbdc64e026`](https://github.com/nodejs/node/commit/cbdc64e026)] - **test**: test-file-write-stream3.js refactor (Richard Karmazin) [#10035](https://github.com/nodejs/node/pull/10035)
- [[`7c90244677`](https://github.com/nodejs/node/commit/7c90244677)] - **test**: implemented es6 conventions (Erez Weiss) [#9669](https://github.com/nodejs/node/pull/9669)
- [[`bb677d41ce`](https://github.com/nodejs/node/commit/bb677d41ce)] - **test**: strictEqual() and RegExp in test-buffer-fill.js (J Scott Chapman) [#9895](https://github.com/nodejs/node/pull/9895)
- [[`34b8c86895`](https://github.com/nodejs/node/commit/34b8c86895)] - **test**: Modernize test-tls-peer-certificate.js (Ilya Potuzhnov) [#10014](https://github.com/nodejs/node/pull/10014)
- [[`5ad7e04280`](https://github.com/nodejs/node/commit/5ad7e04280)] - **test**: strictCompare and explcit inputs mprovement to test-buffer-slice (Michael Alexander) [#10048](https://github.com/nodejs/node/pull/10048)
- [[`256de35c98`](https://github.com/nodejs/node/commit/256de35c98)] - **test**: add test for process.stdin.setRawMode() (Jonathan Darling) [#10037](https://github.com/nodejs/node/pull/10037)
- [[`990a19fc7e`](https://github.com/nodejs/node/commit/990a19fc7e)] - **test**: refactor test for net listen on fd0 (Julian Duque) [#10025](https://github.com/nodejs/node/pull/10025)
- [[`7fd8833fa9`](https://github.com/nodejs/node/commit/7fd8833fa9)] - **test**: update assert.equal() to assert.strictEqual() (Peter Diaz) [#10024](https://github.com/nodejs/node/pull/10024)
- [[`fdc55ef02c`](https://github.com/nodejs/node/commit/fdc55ef02c)] - **test**: use const or let and assert.strictEqual (Christopher Rokita) [#10001](https://github.com/nodejs/node/pull/10001)
- [[`ae1ef5336d`](https://github.com/nodejs/node/commit/ae1ef5336d)] - **test**: fix buffer alloc tests (levsoroka) [#9998](https://github.com/nodejs/node/pull/9998)
- [[`e8fc7fcef7`](https://github.com/nodejs/node/commit/e8fc7fcef7)] - **test**: Added more validations to setEncoding (Paul Lucas) [#9997](https://github.com/nodejs/node/pull/9997)
- [[`79e6068d5c`](https://github.com/nodejs/node/commit/79e6068d5c)] - **test**: use strictEqual() domain-http (cdnadmin) [#9996](https://github.com/nodejs/node/pull/9996)
- [[`7428d80879`](https://github.com/nodejs/node/commit/7428d80879)] - **test**: refactor test-cluster-worker-events (fmizzell) [#9994](https://github.com/nodejs/node/pull/9994)
- [[`6df3b7babc`](https://github.com/nodejs/node/commit/6df3b7babc)] - **test**: update repl tests (makenova) [#9991](https://github.com/nodejs/node/pull/9991)
- [[`47b5f9e710`](https://github.com/nodejs/node/commit/47b5f9e710)] - **test**: modernize test-fs-truncate-fd (Nigel Kibodeaux) [#9978](https://github.com/nodejs/node/pull/9978)
- [[`8b6c45f4b4`](https://github.com/nodejs/node/commit/8b6c45f4b4)] - **test**: update tls test to use const/let and common.mustCall (rgoodwin) [#9968](https://github.com/nodejs/node/pull/9968)
- [[`c05909b3e8`](https://github.com/nodejs/node/commit/c05909b3e8)] - **test**: adding strictEqual to test-buffer-indexof.js (Eric Gonzalez) [#9955](https://github.com/nodejs/node/pull/9955)
- [[`d0852459d5`](https://github.com/nodejs/node/commit/d0852459d5)] - **test**: strictEqual in test-beforeexit-event.js (CodeTheInternet) [#10004](https://github.com/nodejs/node/pull/10004)
- [[`2beba9e025`](https://github.com/nodejs/node/commit/2beba9e025)] - **test**: refactor test-child-process-double-pipe (Dan Villa) [#9930](https://github.com/nodejs/node/pull/9930)
- [[`64b2494e90`](https://github.com/nodejs/node/commit/64b2494e90)] - **test**: updated tls-getcipher test (Ethan Arrowood) [#9923](https://github.com/nodejs/node/pull/9923)
- [[`e502262687`](https://github.com/nodejs/node/commit/e502262687)] - **test**: replace equal with strictEqual in test-freelist.js (Adrian Estrada) [#9910](https://github.com/nodejs/node/pull/9910)
- [[`5a2b68896c`](https://github.com/nodejs/node/commit/5a2b68896c)] - **test**: updated test-stream-pipe-unpipe-stream (Raja Panidepu) [#10100](https://github.com/nodejs/node/pull/10100)
- [[`f900753eeb`](https://github.com/nodejs/node/commit/f900753eeb)] - **test**: refactor test-crypto-ecb (michael6) [#10029](https://github.com/nodejs/node/pull/10029)
- [[`**********`](https://github.com/nodejs/node/commit/**********)] - **test**: refactor test-require-exceptions (Oscar Martinez) [#9882](https://github.com/nodejs/node/pull/9882)
- [[`a801ffb1ee`](https://github.com/nodejs/node/commit/a801ffb1ee)] - **test**: refactor test-console (Matt Crummey) [#9873](https://github.com/nodejs/node/pull/9873)
- [[`bca587bdb3`](https://github.com/nodejs/node/commit/bca587bdb3)] - **test**: refactor test-crypto-certificate (Josh Mays) [#9911](https://github.com/nodejs/node/pull/9911)
- [[`278772a5df`](https://github.com/nodejs/node/commit/278772a5df)] - **test**: refactor dgram-send-multi-buffer-copy (Konstantin Likhter) [#9909](https://github.com/nodejs/node/pull/9909)
- [[`6d5ded508e`](https://github.com/nodejs/node/commit/6d5ded508e)] - **test**: refactor test-domain (Johnny Reading) [#9890](https://github.com/nodejs/node/pull/9890)
- [[`318a2dbea4`](https://github.com/nodejs/node/commit/318a2dbea4)] - **test**: refactor test-cli-syntax (Exlipse7) [#10057](https://github.com/nodejs/node/pull/10057)
- [[`da8e3d946a`](https://github.com/nodejs/node/commit/da8e3d946a)] - **test**: refactor test-child-process-constructor (k3kathy) [#10060](https://github.com/nodejs/node/pull/10060)
- [[`9fddf29f53`](https://github.com/nodejs/node/commit/9fddf29f53)] - **test**: refactor test-repl-mode.js (Cesar Hernandez) [#10061](https://github.com/nodejs/node/pull/10061)
- [[`65c44830c2`](https://github.com/nodejs/node/commit/65c44830c2)] - **test**: var to const, assert.equal to assert.strictEqual in net (Sean Villars) [#9907](https://github.com/nodejs/node/pull/9907)
- [[`ef7cbde0a2`](https://github.com/nodejs/node/commit/ef7cbde0a2)] - **test**: changed vars to const in test-net-better-error-messages-listen-path.js (anoff) [#9905](https://github.com/nodejs/node/pull/9905)
- [[`f62567b7f8`](https://github.com/nodejs/node/commit/f62567b7f8)] - **test**: use const instead of var in test-require-json.js (Sarah Meyer) [#9904](https://github.com/nodejs/node/pull/9904)
- [[`5f3f54d4bb`](https://github.com/nodejs/node/commit/5f3f54d4bb)] - **test**: refactor test-http-dns-error (Outsider) [#10062](https://github.com/nodejs/node/pull/10062)
- [[`ae2bf0a761`](https://github.com/nodejs/node/commit/ae2bf0a761)] - **test**: Changed assert.equal to assert.strictEqual (Daniel Pittman) [#9902](https://github.com/nodejs/node/pull/9902)
- [[`1eb581779d`](https://github.com/nodejs/node/commit/1eb581779d)] - **test**: refactor test-vm-syntax-error-stderr.js (Jay Brownlee) [#9900](https://github.com/nodejs/node/pull/9900)
- [[`c456ca3601`](https://github.com/nodejs/node/commit/c456ca3601)] - **test**: refactor test-tls-destroy-whilst-write (Chris Bystrek) [#10064](https://github.com/nodejs/node/pull/10064)
- [[`fd17ca7710`](https://github.com/nodejs/node/commit/fd17ca7710)] - **test**: refactor test-net-dns-custom-lookup (Kent.Fan) [#10071](https://github.com/nodejs/node/pull/10071)
- [[`cf3c635dba`](https://github.com/nodejs/node/commit/cf3c635dba)] - **test**: refactor test-https-truncate (davidmarkclements) [#10074](https://github.com/nodejs/node/pull/10074)
- [[`14c0388945`](https://github.com/nodejs/node/commit/14c0388945)] - **test**: refactor test-tls-server-verify (Hutson Betts) [#10076](https://github.com/nodejs/node/pull/10076)
- [[`36b8dd3b07`](https://github.com/nodejs/node/commit/36b8dd3b07)] - **test**: refactor test-crypto-padding.js (Konstantin Likhter) [#9971](https://github.com/nodejs/node/pull/9971)
- [[`38ec8e44fa`](https://github.com/nodejs/node/commit/38ec8e44fa)] - **test**: improve test for crypto padding (Julian Duque) [#9906](https://github.com/nodejs/node/pull/9906)
- [[`a771f2181c`](https://github.com/nodejs/node/commit/a771f2181c)] - **test**: use strictEqual in test-cli-eval-event.js (Richard Karmazin) [#9964](https://github.com/nodejs/node/pull/9964)
- [[`e1394eeb16`](https://github.com/nodejs/node/commit/e1394eeb16)] - **test**: refactor test-tls-friendly-error-message.js (Adrian Estrada) [#9967](https://github.com/nodejs/node/pull/9967)
- [[`69077a13bf`](https://github.com/nodejs/node/commit/69077a13bf)] - **test**: refactor test-fs-append-file.js (adelmann) [#10110](https://github.com/nodejs/node/pull/10110)
- [[`baa1accdb1`](https://github.com/nodejs/node/commit/baa1accdb1)] - **test**: assert.equal -> assert.strictEqual (davidmarkclements) [#10065](https://github.com/nodejs/node/pull/10065)
- [[`a34e19532c`](https://github.com/nodejs/node/commit/a34e19532c)] - **test**: refactor test-dgram-exclusive-implicit-bind (Cesar Hernandez) [#10066](https://github.com/nodejs/node/pull/10066)
- [[`d87926ae34`](https://github.com/nodejs/node/commit/d87926ae34)] - **test**: assert.equal -> assert.strictEqual (davidmarkclements) [#10067](https://github.com/nodejs/node/pull/10067)
- [[`c4902e44ad`](https://github.com/nodejs/node/commit/c4902e44ad)] - **test**: polish test-net-better-error-messages-listen (Hitesh Kanwathirtha) [#10087](https://github.com/nodejs/node/pull/10087)
- [[`9b9fe8c5ac`](https://github.com/nodejs/node/commit/9b9fe8c5ac)] - **test**: change var to const in test-tls-key-mismatch.js (bjdelro) [#9897](https://github.com/nodejs/node/pull/9897)
- [[`7697aee7da`](https://github.com/nodejs/node/commit/7697aee7da)] - **test**: use strictEqual in cwd-enoent (JDHarmon) [#10077](https://github.com/nodejs/node/pull/10077)
- [[`cdc2909882`](https://github.com/nodejs/node/commit/cdc2909882)] - **test**: refactor test-fs-read-stream-inherit.js (Jonathan Darling) [#9894](https://github.com/nodejs/node/pull/9894)
- [[`55b58baed1`](https://github.com/nodejs/node/commit/55b58baed1)] - **test**: use assert.strictEqual in test-crypto-ecb (Daniel Pittman) [#9980](https://github.com/nodejs/node/pull/9980)
- [[`e070588a8a`](https://github.com/nodejs/node/commit/e070588a8a)] - **test**: refactor test-child-process-stdio-inherit (Wes Tyler) [#9893](https://github.com/nodejs/node/pull/9893)
- [[`22b15f2ab6`](https://github.com/nodejs/node/commit/22b15f2ab6)] - **test**: change var to const for require and strict equality checks (Harish Tejwani) [#9892](https://github.com/nodejs/node/pull/9892)
- [[`2a8d29339d`](https://github.com/nodejs/node/commit/2a8d29339d)] - **test**: Update to const and use regex for assertions (Daniel Flores) [#9891](https://github.com/nodejs/node/pull/9891)
- [[`295eb5a3b6`](https://github.com/nodejs/node/commit/295eb5a3b6)] - **test**: swap var->const/let and equal->strictEqual (Peter Masucci) [#9888](https://github.com/nodejs/node/pull/9888)
- [[`57f060c495`](https://github.com/nodejs/node/commit/57f060c495)] - **test**: replace equal with strictEqual in crypto (Julian Duque) [#9886](https://github.com/nodejs/node/pull/9886)
- [[`3d35930b2c`](https://github.com/nodejs/node/commit/3d35930b2c)] - **test**: replace equal with strictEqual (Julian Duque) [#9879](https://github.com/nodejs/node/pull/9879)
- [[`13cc6a005b`](https://github.com/nodejs/node/commit/13cc6a005b)] - **test**: var to const/let in test-tls-set-ciphers (rajatk) [#9877](https://github.com/nodejs/node/pull/9877)
- [[`f3eb8b1bea`](https://github.com/nodejs/node/commit/f3eb8b1bea)] - **test**: refactor test-tls-timeout-server-2 (Devon Rifkin) [#9876](https://github.com/nodejs/node/pull/9876)
- [[`dc76a20474`](https://github.com/nodejs/node/commit/dc76a20474)] - **test**: Updating vars to const and tsl server test (Matt Webb) [#9874](https://github.com/nodejs/node/pull/9874)
- [[`63fafb8aca`](https://github.com/nodejs/node/commit/63fafb8aca)] - **test**: refactor test-crypto-hash-stream-pipe (Matt Wilson) [#10055](https://github.com/nodejs/node/pull/10055)
- [[`fb4b650159`](https://github.com/nodejs/node/commit/fb4b650159)] - **test**: crypto-hash-stream-pipe use strict equal (Mitchell Stoutin) [#9935](https://github.com/nodejs/node/pull/9935)
- [[`8f550df252`](https://github.com/nodejs/node/commit/8f550df252)] - **test**: refactor child-process-spawn-error (Johnny Reading) [#9951](https://github.com/nodejs/node/pull/9951)
- [[`b73f6b760f`](https://github.com/nodejs/node/commit/b73f6b760f)] - **test**: refactor test-child-process-spawn-error (stokingerl) [#9937](https://github.com/nodejs/node/pull/9937)
- [[`371ca03568`](https://github.com/nodejs/node/commit/371ca03568)] - **test**: refactor test-vm-static-this.js (David Bradford) [#9887](https://github.com/nodejs/node/pull/9887)
- [[`3e37673d5c`](https://github.com/nodejs/node/commit/3e37673d5c)] - **test**: refactor test-crypto-cipheriv-decipheriv (Aileen) [#10018](https://github.com/nodejs/node/pull/10018)
- [[`f76bb2adf8`](https://github.com/nodejs/node/commit/f76bb2adf8)] - **test**: refactor test for crypto cipher/decipher iv (Julian Duque) [#9943](https://github.com/nodejs/node/pull/9943)
- [[`4cc813d8b9`](https://github.com/nodejs/node/commit/4cc813d8b9)] - **test**: refactor test-cluster-setup-master-argv (Oscar Martinez) [#9960](https://github.com/nodejs/node/pull/9960)
- [[`eb0c1cd412`](https://github.com/nodejs/node/commit/eb0c1cd412)] - **test**: refactor test-cluster-setup-master-argv (Christine Hong) [#9993](https://github.com/nodejs/node/pull/9993)
- [[`d2e89272d2`](https://github.com/nodejs/node/commit/d2e89272d2)] - **test**: refactor test-fs-append-file-sync (Chris Bystrek) [#10056](https://github.com/nodejs/node/pull/10056)
- [[`070370fd0a`](https://github.com/nodejs/node/commit/070370fd0a)] - **test**: refactor test-fs-append-file-sync (Ian White) [#9977](https://github.com/nodejs/node/pull/9977)
- [[`87038bb628`](https://github.com/nodejs/node/commit/87038bb628)] - **test**: refactor test-fs-write-file (adelmann) [#10030](https://github.com/nodejs/node/pull/10030)
- [[`1f6f411234`](https://github.com/nodejs/node/commit/1f6f411234)] - **test**: refactor test/parallel/test-fs-write-file.js (Kyle Carter) [#9992](https://github.com/nodejs/node/pull/9992)
- [[`4cb52ee827`](https://github.com/nodejs/node/commit/4cb52ee827)] - **test**: update to const iin cluster test (Greg Valdez) [#10007](https://github.com/nodejs/node/pull/10007)
- [[`f9d79ef597`](https://github.com/nodejs/node/commit/f9d79ef597)] - **test**: use assert.strictEqual() cluster test (Bidur Adhikari) [#10042](https://github.com/nodejs/node/pull/10042)
- [[`b4ec7d6c50`](https://github.com/nodejs/node/commit/b4ec7d6c50)] - **test**: use const in test-crypto-pbkdf2 (Greg Valdez) [#9974](https://github.com/nodejs/node/pull/9974)
- [[`2e889cf056`](https://github.com/nodejs/node/commit/2e889cf056)] - **test**: improve test for crypto pbkdf2 (joyeecheung) [#9883](https://github.com/nodejs/node/pull/9883)
- [[`c0a28622ce`](https://github.com/nodejs/node/commit/c0a28622ce)] - **test**: var -> let/const, .equal -> .strictEqual (shiya) [#9913](https://github.com/nodejs/node/pull/9913)
- [[`d1da89906d`](https://github.com/nodejs/node/commit/d1da89906d)] - **test**: increase coverage for timers (lrlna) [#10068](https://github.com/nodejs/node/pull/10068)
- [[`44d9bc8b90`](https://github.com/nodejs/node/commit/44d9bc8b90)] - **test**: change equal to strictEqual (Kevin Zurawel) [#9872](https://github.com/nodejs/node/pull/9872)
- [[`0cab6eb6ca`](https://github.com/nodejs/node/commit/0cab6eb6ca)] - **test**: test for http.request() invalid method error (Ashton Kinslow) [#10080](https://github.com/nodejs/node/pull/10080)
- [[`f9386f2846`](https://github.com/nodejs/node/commit/f9386f2846)] - **test**: update net-local-address-port (scalkpdev) [#9885](https://github.com/nodejs/node/pull/9885)
- [[`66554c75d5`](https://github.com/nodejs/node/commit/66554c75d5)] - **test**: refactor test-tls-ecdh (Adriana Rios) [#9878](https://github.com/nodejs/node/pull/9878)
- [[`a857c9a74c`](https://github.com/nodejs/node/commit/a857c9a74c)] - **test**: refactor test-vm-debug-context (makenova) [#9875](https://github.com/nodejs/node/pull/9875)
- [[`a6377a96dd`](https://github.com/nodejs/node/commit/a6377a96dd)] - **test**: increase coverage for lib/events.js (Safia Abdalla) [#9865](https://github.com/nodejs/node/pull/9865)
- [[`eb369f6d48`](https://github.com/nodejs/node/commit/eb369f6d48)] - **test**: use strictEqual in test-zlib-truncated (ben_cripps) [#9858](https://github.com/nodejs/node/pull/9858)
- [[`3af4ef4642`](https://github.com/nodejs/node/commit/3af4ef4642)] - **test**: use strictEqual in test-debugger-client.js (ben_cripps) [#9857](https://github.com/nodejs/node/pull/9857)
- [[`5c15a68091`](https://github.com/nodejs/node/commit/5c15a68091)] - **test**: refactor test-debug-args (Rich Trott) [#9833](https://github.com/nodejs/node/pull/9833)
- [[`0e36becd39`](https://github.com/nodejs/node/commit/0e36becd39)] - **test**: refactor test-fs-non-number-arguments-throw (Michaël Zasso) [#9844](https://github.com/nodejs/node/pull/9844)
- [[`c286312ef5`](https://github.com/nodejs/node/commit/c286312ef5)] - **test**: replace assert.equal with assert.strictEqual (brad-decker) [#9842](https://github.com/nodejs/node/pull/9842)
- [[`0ccb2c3992`](https://github.com/nodejs/node/commit/0ccb2c3992)] - **test**: refactor test-crypto-timing-safe-equal (Michaël Zasso) [#9843](https://github.com/nodejs/node/pull/9843)
- [[`0bdd5ca0f7`](https://github.com/nodejs/node/commit/0bdd5ca0f7)] - **test**: run cpplint on files in test/cctest (Ben Noordhuis) [#9787](https://github.com/nodejs/node/pull/9787)
- [[`956239124d`](https://github.com/nodejs/node/commit/956239124d)] - **test**: add toASCII and toUnicode punycode tests (Claudio Rodriguez) [#9741](https://github.com/nodejs/node/pull/9741)
- [[`70633f965d`](https://github.com/nodejs/node/commit/70633f965d)] - **test**: refactor test-util-inspect (Rich Trott) [#9804](https://github.com/nodejs/node/pull/9804)
- [[`4c2ad8c89f`](https://github.com/nodejs/node/commit/4c2ad8c89f)] - **test**: refactor test-preload (Rich Trott) [#9803](https://github.com/nodejs/node/pull/9803)
- [[`59aec82f88`](https://github.com/nodejs/node/commit/59aec82f88)] - **test**: refine test-http-status-reason-invalid-chars (Rich Trott) [#9802](https://github.com/nodejs/node/pull/9802)
- [[`c35bf44f60`](https://github.com/nodejs/node/commit/c35bf44f60)] - **test**: refactor test-crypto-binary-default (Michaël Zasso) [#9810](https://github.com/nodejs/node/pull/9810)
- [[`4d1e11243b`](https://github.com/nodejs/node/commit/4d1e11243b)] - **test**: refactor and fix test-crypto (Michaël Zasso) [#9807](https://github.com/nodejs/node/pull/9807)
- [[`74c3283cfa`](https://github.com/nodejs/node/commit/74c3283cfa)] - **test**: fix test-buffer-slow (Michaël Zasso) [#9809](https://github.com/nodejs/node/pull/9809)
- [[`e2db5c8e7a`](https://github.com/nodejs/node/commit/e2db5c8e7a)] - **test**: refactor test-net-pingpong (Michaël Zasso) [#9812](https://github.com/nodejs/node/pull/9812)
- [[`cd10e1ae4a`](https://github.com/nodejs/node/commit/cd10e1ae4a)] - **test**: refactor and fix test-dns (Michaël Zasso) [#9811](https://github.com/nodejs/node/pull/9811)
- [[`dcba25082f`](https://github.com/nodejs/node/commit/dcba25082f)] - **test**: refactor and fix test-buffer-bytelength (Michaël Zasso) [#9808](https://github.com/nodejs/node/pull/9808)
- [[`d06f010482`](https://github.com/nodejs/node/commit/d06f010482)] - **test**: cleanup test-dgram-error-message-address (Michael Macherey) [#8938](https://github.com/nodejs/node/pull/8938)
- [[`3b193defb2`](https://github.com/nodejs/node/commit/3b193defb2)] - **test**: fix flaky test-cluster-dgram-2 (Rich Trott) [#9791](https://github.com/nodejs/node/pull/9791)
- [[`3f1b068644`](https://github.com/nodejs/node/commit/3f1b068644)] - **test**: refactor common.js (Rich Trott) [#9732](https://github.com/nodejs/node/pull/9732)
- [[`d31a41149d`](https://github.com/nodejs/node/commit/d31a41149d)] - **test**: fix test-tls-connect-address-family (mkamakura) [#9573](https://github.com/nodejs/node/pull/9573)
- [[`d51c856f11`](https://github.com/nodejs/node/commit/d51c856f11)] - **test**: fix test-http-status-reason-invalid-chars (Yosuke Saito) [#9572](https://github.com/nodejs/node/pull/9572)
- [[`b763a31af0`](https://github.com/nodejs/node/commit/b763a31af0)] - **test**: refactor test-child-process-exec-error (Rich Trott) [#9780](https://github.com/nodejs/node/pull/9780)
- [[`2b7ecb5012`](https://github.com/nodejs/node/commit/2b7ecb5012)] - **test**: exclude no_interleaved_stdio test for AIX (Michael Dawson) [#9772](https://github.com/nodejs/node/pull/9772)
- [[`4971c3bb79`](https://github.com/nodejs/node/commit/4971c3bb79)] - **test**: fix flaky test-dgram-empty-packet & friends (Rich Trott) [#9724](https://github.com/nodejs/node/pull/9724)
- [[`2fb825750d`](https://github.com/nodejs/node/commit/2fb825750d)] - **test**: fix flaky test-inspector (Rich Trott) [#9727](https://github.com/nodejs/node/pull/9727)
- [[`fc13cc6a12`](https://github.com/nodejs/node/commit/fc13cc6a12)] - **test**: refactor test-tls-hello-parser-failure (Rich Trott) [#9715](https://github.com/nodejs/node/pull/9715)
- [[`ea1c4e1212`](https://github.com/nodejs/node/commit/ea1c4e1212)] - **test,url**: improve escaping in url.parse (joyeecheung) [#10083](https://github.com/nodejs/node/pull/10083)
- [[`64854f625b`](https://github.com/nodejs/node/commit/64854f625b)] - **tools**: add ESLint rule for assert.throws arguments (Michaël Zasso) [#10089](https://github.com/nodejs/node/pull/10089)
- [[`2ee3543e04`](https://github.com/nodejs/node/commit/2ee3543e04)] - **tools**: remove unneeded escaping in generate.js (Rich Trott) [#9781](https://github.com/nodejs/node/pull/9781)
- [[`53d175267c`](https://github.com/nodejs/node/commit/53d175267c)] - **tools**: Add no useless regex char class rule (Prince J Wesley) [#9591](https://github.com/nodejs/node/pull/9591)
- [[`561b1494bc`](https://github.com/nodejs/node/commit/561b1494bc)] - **tools**: allow test.py to use full paths of tests (Francis Gulotta) [#9694](https://github.com/nodejs/node/pull/9694)
- [[`5ae549c3aa`](https://github.com/nodejs/node/commit/5ae549c3aa)] - **url**: fix -Warray-bounds warning (Santiago Gimeno) [#9751](https://github.com/nodejs/node/pull/9751)

Windows 32-bit Installer: https://nodejs.org/dist/v7.2.1/node-v7.2.1-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v7.2.1/node-v7.2.1-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v7.2.1/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v7.2.1/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v7.2.1/node-v7.2.1.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v7.2.1/node-v7.2.1-darwin-x64.tar.gz \
Linux 32-bit Binary: https://nodejs.org/dist/v7.2.1/node-v7.2.1-linux-x86.tar.xz \
Linux 64-bit Binary: https://nodejs.org/dist/v7.2.1/node-v7.2.1-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v7.2.1/node-v7.2.1-linux-ppc64le.tar.xz \
Linux PPC BE 64-bit Binary: https://nodejs.org/dist/v7.2.1/node-v7.2.1-linux-ppc64.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v7.2.1/node-v7.2.1-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v7.2.1/node-v7.2.1-aix-ppc64.tar.gz \
SmartOS 32-bit Binary: https://nodejs.org/dist/v7.2.1/node-v7.2.1-sunos-x86.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v7.2.1/node-v7.2.1-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v7.2.1/node-v7.2.1-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v7.2.1/node-v7.2.1-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v7.2.1/node-v7.2.1.tar.gz \
Other release files: https://nodejs.org/dist/v7.2.1/ \
Documentation: https://nodejs.org/docs/v7.2.1/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

93c809d64e28f6cf0e62f51f0e385764f615c90112ed6220371ad756332b796f  node-v7.2.1-aix-ppc64.tar.gz
c4956b58bae8bb73860f2f13f7641ae873ff95fabf955be35a329be9aed08f27  node-v7.2.1-darwin-x64.tar.gz
38c4a18fbf80c5c0e632e510dd2e8bcc06fab1478ea44c1665371a3eb2ec840a  node-v7.2.1-darwin-x64.tar.xz
088a59f8756f90f35f93c9c8354d19652f48fa303d58f84cf41b357edc0a9752  node-v7.2.1-headers.tar.gz
494d29c21847a78265e16768e362081cb2ee78292316f61567268ca147397f31  node-v7.2.1-headers.tar.xz
bd9639fd159ae8c93b9ecfb50412f35aaefd46fa9111fd661f80a0e0203edcdc  node-v7.2.1-linux-arm64.tar.gz
8154e0606f69144089ecdb15682f5512c63b92e92057d38a3eb3177eb5108981  node-v7.2.1-linux-arm64.tar.xz
4eb1e2c66db89d4f4215c65231a184db8c663a1f26084d1fe81925ddacc6e09b  node-v7.2.1-linux-armv6l.tar.gz
3445db6e1bb46f1a2dba070cbc68ee1b044e968deb840a2bfbae3c8b6df525cf  node-v7.2.1-linux-armv6l.tar.xz
87af4deae903616c5181394880335a6ec7930463d2244b7a20fb904f84d6b30b  node-v7.2.1-linux-armv7l.tar.gz
18c74fa2c7dfff2f3d38f5eb44accb64efef60475d399eedf4a8d39f712c0053  node-v7.2.1-linux-armv7l.tar.xz
beaaa9a911d6e92029853e7e96442b11d2de6cb22ae4551ab5e6ceb42bfe82bf  node-v7.2.1-linux-ppc64le.tar.gz
da6afca0a7d2e2620af8332ffd4d6b8183b61f7a8bf011853728854d0c4599a3  node-v7.2.1-linux-ppc64le.tar.xz
22983a319bb3528d8c22e0ca974bbed657ee9517df9c0ddc1fe5a09617de6f64  node-v7.2.1-linux-ppc64.tar.gz
ad8ebb27b27068b1e989cb139fe9618ade08e9ba4151f7b32d4ef851eea051a4  node-v7.2.1-linux-ppc64.tar.xz
b61a5f9b724bae912103cfb3223316ab63f8bf8b911f3aef195e04e946202d43  node-v7.2.1-linux-s390x.tar.gz
b550166b2011ec07c6947ce583f1df0b7e1dc7b175a521b857d4341290cbfa2a  node-v7.2.1-linux-s390x.tar.xz
661b26736bd5a63df632d47a54c7ca9004f7e7a0603e635571335c54529d0241  node-v7.2.1-linux-x64.tar.gz
1907fff2698444e8fac0d6b1f0f8fddeb355fd4924e284af0fd71089f365b187  node-v7.2.1-linux-x64.tar.xz
2f8b8730d39cb75f34ee065d9999a58b77e756b38833d0152fda49b57f697fd9  node-v7.2.1-linux-x86.tar.gz
02763ac0ac1eeb777c95e2202beaf3d7de3cd2e541255ce0afcf75651c55abec  node-v7.2.1-linux-x86.tar.xz
b7740d814f01b295b8504badeaa4378a233a5ba0deb7cb50c1504fafda6e5874  node-v7.2.1.pkg
0b2c088f0250a7c3fe0b605f3d312d836188a2182e380d2190a53382ae4b07d4  node-v7.2.1-sunos-x86.tar.gz
1931b6c7c30f4177b9539b3e9554a6c9e12c5e28f2bd3aff9e72751ecd9d9b76  node-v7.2.1-sunos-x86.tar.xz
fd08b8ba43b0596a7160b09a37113ac03b4b0976ec7e48980b7b8c078aa51b02  node-v7.2.1.tar.gz
c08b03aa5d2cc61c5f04042658d247dc376e1e873946f2b36d54108fa6d9d80d  node-v7.2.1.tar.xz
133b311121dc9c1a0325e4c86c7ab6a08fa22cace5d7068a5f6f0f0eb417a2a2  node-v7.2.1-win-x64.7z
960ce0e9da98650c0cb86f9550d558996b2e13cff4c376991e74a852babe76ac  node-v7.2.1-win-x64.zip
5ed4f99f4b1610754762657541c1b03642fa820243a7adba104374013181b85a  node-v7.2.1-win-x86.7z
6d6fb4bfabd62e4a5ce7d4599fc969ecdc770e6ef3b8c55160b3a2be6c144270  node-v7.2.1-win-x86.zip
789af29eba3a43213dfab7a71ada7e2c513a9fa023f0987b2076b10754da907e  node-v7.2.1-x64.msi
8302c95d26d343c131f403c088f8812540f4bebc5a01a98972599c03658e547b  node-v7.2.1-x86.msi
a043241c90a866b1afa362dac407356999db1025548563e4c92539cef9a02500  win-x64/node.exe
85575437bb07d678e881794674cf44b0e8e04d5248c76546dc6baea0a644bfae  win-x64/node.lib
2982dd47e7b5f8653c02704091804cd485957b44a949483391729bf012ea3345  win-x64/node_pdb.7z
a1ef7d0cd6d90cec3ff43a9aa22d43be8bb5f6d4e5774ff359333edf4802e48b  win-x64/node_pdb.zip
fe4b6b5e881ad186fd2777512fe9c91949473415d88f9a5e2a71d34dac3442bd  win-x86/node.exe
1de1a84b21f43425baddc4cbff9040c383613caa2fad0a2306d0e067347f27fe  win-x86/node.lib
8a43fa2e978c70fb358b8bfe0c9ff369e0c848519d5b07b845ad2619c586287b  win-x86/node_pdb.7z
d9e66ce24d0272f619838adeb7d2728557b628eff7a8307e33c09c55b4f1da3d  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----
Comment: GPGTools - https://gpgtools.org

iQIcBAEBCAAGBQJYR0HtAAoJEEX17r2BPa6OHw4P/jsf1vWyWPzhWoNjmSHIWAn+
Zpeau1xr2CBvY8HsSo2wPzekVfdmFpaiqVvI8WrpQ5O8Uc68OzH+8hB0rB5UvmCc
D0wcJWYVsuNhEw5HgUQVdTfG/BBJJQw01oCwYLKekWT9SjYfExuSGSqQQEcEcHjn
cqs/46rNwyTcJXeOfkwL2wmoiEx+KBApdE99MTW+8YmpPP1vQBendM5zSTYMg71Z
vTf2Cb1Twi+06to8ICtWAY0hcBtESh8rfwnaC4CSs3SPt/HrAYVwkTLN3cp0miS2
LD3iiy0BwsKcmEwuP069hQ9OyIn0jviju5Jr4YGZAC7QkXn3x7nEaeWW4Vum6NeY
WJkITUAhtr2Jr3IfTGWbVm4E0qCazMu9RSA3ogv3XXSlb+GyvpwrX8tATM4kPnF9
B81GFTNG67ClhFPnLHzmiOoU8PnYf3mp1uJoAkMVPiHRxcgqBdo4eD9bSAPP7J1M
VC6LP76Df+PZ3HCHsQm+YwugJoaW+iWmruJi0CmOD4UV2BiSj57uKpSYxoDHvzre
v6c/MSwiI9b9onC3S2b4HNO4wRIYTFt4j7z07MVda4Me+JfJ/WtReLy7PDS1liO/
DOvYnbYN27zpRGaL7TSR4GdpnUwUu4L2qSmsX4OOkr87QEjVaSAEFUBNXQ4I0pqf
uegbx0d6vyr3082ZtoBN
=Knhx
-----END PGP SIGNATURE-----

```
