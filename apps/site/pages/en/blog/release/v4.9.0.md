---
date: '2018-03-28T16:31:32.882Z'
category: release
title: Node v4.9.0 (Maintenance)
layout: blog-post
author: <PERSON><PERSON>
---

### Notable Changes

- **Upgrade to OpenSSL 1.0.2o**: Does not contain any security fixes that are known to impact Node.js.
- **Fix for `'path'` module regular expression denial of service (CVE-2018-7158)**: A regular expression used for parsing POSIX an Windows paths could be used to cause a denial of service if an attacker were able to have a specially crafted path string passed through one of the impacted `'path'` module functions.
- **Reject spaces in HTTP `Content-Length` header values (CVE-2018-7159)**: The Node.js HTTP parser allowed for spaces inside `Content-Length` header values. Such values now lead to rejected connections in the same way as non-numeric values.
- **Update root certificates**: 5 additional root certificates have been added to the Node.js binary and 30 have been removed.

### Commits

- [[`497ff3cd4f`](https://github.com/nodejs/node/commit/497ff3cd4f)] - **crypto**: update root certificates (<PERSON>) [#19322](https://github.com/nodejs/node/pull/19322)
- [[`514709e41f`](https://github.com/nodejs/node/commit/514709e41f)] - **deps**: add -no_rand_screen to openssl s_client (Shigeki Ohtsu) [nodejs/io.js#1836](https://github.com/nodejs/io.js/pull/1836)
- [[`5108108606`](https://github.com/nodejs/node/commit/5108108606)] - **deps**: fix asm build error of openssl in x86_win32 (Shigeki Ohtsu) [iojs/io.js#1389](https://github.com/iojs/io.js/pull/1389)
- [[`d67d0a63d9`](https://github.com/nodejs/node/commit/d67d0a63d9)] - **deps**: fix openssl assembly error on ia32 win32 (Fedor Indutny) [iojs/io.js#1389](https://github.com/iojs/io.js/pull/1389)
- [[`6af057ecc8`](https://github.com/nodejs/node/commit/6af057ecc8)] - **deps**: copy all openssl header files to include dir (Shigeki Ohtsu) [#19638](https://github.com/nodejs/node/pull/19638)
- [[`b50cd3359d`](https://github.com/nodejs/node/commit/b50cd3359d)] - **deps**: upgrade openssl sources to 1.0.2o (Shigeki Ohtsu) [#19638](https://github.com/nodejs/node/pull/19638)
- [[`da6e24c8d6`](https://github.com/nodejs/node/commit/da6e24c8d6)] - **deps**: reject interior blanks in Content-Length (Ben Noordhuis) [nodejs-private/http-parser-private#1](https://github.com/nodejs-private/http-parser-private/pull/1)
- [[`7ebc9981e0`](https://github.com/nodejs/node/commit/7ebc9981e0)] - **deps**: upgrade http-parser to v2.8.0 (Ben Noordhuis) [nodejs-private/http-parser-private#1](https://github.com/nodejs-private/http-parser-private/pull/1)
- [[`6fd2cc93a6`](https://github.com/nodejs/node/commit/6fd2cc93a6)] - **openssl**: fix keypress requirement in apps on win32 (Shigeki Ohtsu) [iojs/io.js#1389](https://github.com/iojs/io.js/pull/1389)
- [[`bf00665af6`](https://github.com/nodejs/node/commit/bf00665af6)] - **path**: unwind regular expressions in Windows (Myles Borins)
- [[`4196fcf23e`](https://github.com/nodejs/node/commit/4196fcf23e)] - **path**: unwind regular expressions in POSIX (Myles Borins)
- [[`625986b699`](https://github.com/nodejs/node/commit/625986b699)] - **src**: drop CNNIC+StartCom certificate whitelisting (Ben Noordhuis) [#19322](https://github.com/nodejs/node/pull/19322)
- [[`ebc46448a4`](https://github.com/nodejs/node/commit/ebc46448a4)] - **tools**: update certdata.txt (Ben Noordhuis) [#19322](https://github.com/nodejs/node/pull/19322)

Windows 32-bit Installer: https://nodejs.org/dist/v4.9.0/node-v4.9.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v4.9.0/node-v4.9.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v4.9.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v4.9.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v4.9.0/node-v4.9.0.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v4.9.0/node-v4.9.0-darwin-x64.tar.gz \
Linux 32-bit Binary: https://nodejs.org/dist/v4.9.0/node-v4.9.0-linux-x86.tar.xz \
Linux 64-bit Binary: https://nodejs.org/dist/v4.9.0/node-v4.9.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v4.9.0/node-v4.9.0-linux-ppc64le.tar.xz \
Linux PPC BE 64-bit Binary: https://nodejs.org/dist/v4.9.0/node-v4.9.0-linux-ppc64.tar.xz \
SmartOS 32-bit Binary: https://nodejs.org/dist/v4.9.0/node-v4.9.0-sunos-x86.tar.xz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v4.9.0/node-v4.9.0-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v4.9.0/node-v4.9.0-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v4.9.0/node-v4.9.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v4.9.0/node-v4.9.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v4.9.0/node-v4.9.0.tar.gz \
Other release files: https://nodejs.org/dist/v4.9.0/ \
Documentation: https://nodejs.org/docs/v4.9.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

9baa27ff50189db2f8de4b3dff58bd1c6e83ba98f8ecc128215c007f0de0a3d7  node-v4.9.0-darwin-x64.tar.gz
ea833892f563282bd9c4699bf7445f3dee17dd22b8a7556ea034e8a4ec771d20  node-v4.9.0-darwin-x64.tar.xz
cb5cebf97f06da566aab26c994b9324e5bdf49e77b67366a96601884860eba32  node-v4.9.0-headers.tar.gz
3f3a60fc351b9307604d94db9df6590979f148c3c7391dee18c302c5f0a08963  node-v4.9.0-headers.tar.xz
58c47ff94cb79ec8dd3c2c5d21a1836df00914e6306201503baffbf584012171  node-v4.9.0-linux-arm64.tar.gz
89b3462cb184404ca154f70e9fd169ef4a234c2c1b65fa7a60ecdf91f67adbe4  node-v4.9.0-linux-arm64.tar.xz
44fd2c83ba525e89bcf9ced36f9413dab52de5066942d6dbdac2f4f01354fbe6  node-v4.9.0-linux-armv6l.tar.gz
6f1ca87bbbec3746f437e9b437c8775419e49265a827bc8bf22748712b2f11aa  node-v4.9.0-linux-armv6l.tar.xz
f67808a2ae7c39e51732df15aa33b12f1ec1375c83c8b483d28452c9b748b8ae  node-v4.9.0-linux-armv7l.tar.gz
db73d54719c44eabf98296d269f9a1d2e10730ac28dc1cd7c82638f27f73402b  node-v4.9.0-linux-armv7l.tar.xz
2b71b7acf75f51babd2057a86a44afc1f8b4999201e7d083a0fa8ed07f95a9a6  node-v4.9.0-linux-ppc64le.tar.gz
1b8bc5882d2e6e5787f03f10b435e4ec6b323b61e4be038487f9f890d8f5fef6  node-v4.9.0-linux-ppc64le.tar.xz
13bc8f0fe36aeb3b94ee9d11a8249f5535beb76e136c28efbc9849768ccc02e6  node-v4.9.0-linux-ppc64.tar.gz
e91817e69db722bf3218a32abe6304e810680c29e663bebb028fd73950e5669d  node-v4.9.0-linux-ppc64.tar.xz
31967377cece1bfb30a16f7d6b2535434e3e2c56d894ed60de7a9fab7930f767  node-v4.9.0-linux-x64.tar.gz
7bcbaf32d4b16bb232aae3cf1e6e0b6fa24db4dcbbd164d5180ff3f295e0d08f  node-v4.9.0-linux-x64.tar.xz
2a2aa1df78b150c034db237873d47e5f539276d2fc676360cc73003dd7c27fb8  node-v4.9.0-linux-x86.tar.gz
854058c44845273d858560238b579ab06ede35b0d3c938ffce87b9037d7a0ba6  node-v4.9.0-linux-x86.tar.xz
e238bd3bcc60b8d29e4c9928bfc2df152fca0d11a12938893eba5900da1cf27c  node-v4.9.0.pkg
2274d99d49d2b01d76a3ebbe98f8dc26e2e7a38ab46641a8505213f4fb92d944  node-v4.9.0-sunos-x64.tar.gz
c84401b777e4b932ae540325b95c0a29baec1210ca95f6bde7cdb47580196875  node-v4.9.0-sunos-x64.tar.xz
67e40a890c3084dbf5ffc59ea9aaf03d229410475b40bc4698254ab914264131  node-v4.9.0-sunos-x86.tar.gz
fb8cf066bb7b4fdb202d15a5c85f16e728af6dadb6b70bc82a7e31222694df7e  node-v4.9.0-sunos-x86.tar.xz
55683e98b39513735dedddcdd3331c64ddc7edd5744d2c4317b44a1c54e82f9a  node-v4.9.0.tar.gz
a1b271c585f45e2f17c6a211bc87028c448d54f4418a494d5ae62d114ecbb69f  node-v4.9.0.tar.xz
f618460880dc5d8f9d07820071b9cf253c0ad161f3d0ef7c84ccf348344d531f  node-v4.9.0-win-x64.7z
ceaf785dbb29a0432aa31e861dbe7ab7ecf2edff0e03cc875f91e95ec2f4fba6  node-v4.9.0-win-x64.zip
6a027337023ee140094fcaa37e8a731f1af74e33b5695eb66d576a0fa73041e7  node-v4.9.0-win-x86.7z
d38b158d04a1a8454d17cc1cf11f576464e833017e31fb0b0f49ffc0e66ae2a0  node-v4.9.0-win-x86.zip
6e360cd2f18ebdf70fee3adce67dc84f7ef5d8affa9c15b193c50578dda2585a  node-v4.9.0-x64.msi
be4a46a7e464c58fbdf70f2c9f01fe0d80652f9180194b9cf716691673c5533e  node-v4.9.0-x86.msi
ea96035c7348702053c500ff6707c836de5dbca22f77cd3e62bb6fdfe2c57714  win-x64/node.exe
024df4c7969ce604b521d2c1cc450ac2b2459d66ebdfa494c0a5f207a2e4f6b9  win-x64/node.lib
286b2f1a73600d3ab59391b4eeed058929d1beb9424f12aac70edc9ab1059068  win-x64/node_pdb.7z
5f2dbb1b477fa36cb6cd13b2c217ae05351bbaaa363bd30e6a4b01ec36a492b1  win-x64/node_pdb.zip
f8e921807c07567a220e571c14838971dbc619f874db40b7dd545dde38ae32fd  win-x86/node.exe
0379be01a2b9077a311d6db233c0af2d49855573f34672354ff168c02e1d09d6  win-x86/node.lib
9e05ff71fc9e2487f770660ebbcdbe890cf72be63ce7fa9c9306df7b31cf121e  win-x86/node_pdb.7z
48e537e1af08bc3897e8cac64789303538afa16c61e093b1dfbb241ec5748d75  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQEzBAEBCAAdFiEEDv/hvO/ZyE49CYFSkzsB9AtcqUYFAlq8NmsACgkQkzsB9Atc
qUZMVQgAiGlbWpgatCiM7vImDIZec0/aWgDSDSLmB5lVr1RlM+B2Hs8Bpc3ffcSz
g9B8MJERcqqU8PL6r3QTQlK79FIJVd3ut1sphQJYa+Qtw3mDYGywN+B/u2/yKjvN
hQUJ+bsalcixGnvZ11dE1zw2czm9pKD6KhqmkwaUzYOLl/oVNuvQKHd47eRt2ZQs
fa01v3fq4Da9EaVbvDHP4rdidRW6b1hVDck9J/QcnE7LfWZd8AGZ3mFwkSyklp37
dJ38PCnJneefkdIZY5e8a8LOvv9FjUCuKPF23KLiLfNk/HSgpuvlXxWgjXQzcHGs
bjzfAYIaKmvoFXIG0bCm9UVp6u6sGA==
=s5Ym
-----END PGP SIGNATURE-----

```
