---
date: '2016-03-02T21:53:44.939Z'
category: release
title: Node v4.3.2 (LTS)
layout: blog-post
author: <PERSON><PERSON>
---

### Notable changes

- **openssl**: Upgrade from 1.0.2f to 1.0.2g (<PERSON>) [#5507](https://github.com/nodejs/node/pull/5507)
  - Fix a double-free defect in parsing malformed DSA keys that may potentially be used for DoS or memory corruption attacks. It is likely to be very difficult to use this defect for a practical attack and is therefore considered low severity for Node.js users. More info is available at [CVE-2016-0705](https://www.openssl.org/news/vulnerabilities.html#2016-0705).
  - Fix a defect that can cause memory corruption in certain very rare cases relating to the internal `BN_hex2bn()` and `BN_dec2bn()` functions. It is believed that Node.js is not invoking the code paths that use these functions so practical attacks via Node.js using this defect are _unlikely_ to be possible. More info is available at [CVE-2016-0797](https://www.openssl.org/news/vulnerabilities.html#2016-0797).
  - Fix a defect that makes the _[CacheBleed Attack](https://ssrg.nicta.com.au/projects/TS/cachebleed/)_ possible. This defect enables attackers to execute side-channel attacks leading to the potential recovery of entire RSA private keys. It only affects the Intel Sandy Bridge (and possibly older) microarchitecture when using hyper-threading. Newer microarchitectures, including Haswell, are unaffected. More info is available at [CVE-2016-0702](https://www.openssl.org/news/vulnerabilities.html#2016-0702).

## Commits

- [[`c133797d09`](https://github.com/nodejs/node/commit/c133797d09)] - **deps**: upgrade openssl to 1.0.2g (Ben Noordhuis) [#5507](https://github.com/nodejs/node/pull/5507)

Windows 32-bit Installer: https://nodejs.org/dist/v4.3.2/node-v4.3.2-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v4.3.2/node-v4.3.2-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v4.3.2/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v4.3.2/win-x64/node.exe \
Mac OS X 64-bit Installer: https://nodejs.org/dist/v4.3.2/node-v4.3.2.pkg \
Mac OS X 64-bit Binary: https://nodejs.org/dist/v4.3.2/node-v4.3.2-darwin-x64.tar.gz \
Linux 32-bit Binary: https://nodejs.org/dist/v4.3.2/node-v4.3.2-linux-x86.tar.xz \
Linux 64-bit Binary: https://nodejs.org/dist/v4.3.2/node-v4.3.2-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v4.3.2/node-v4.3.2-linux-ppc64le.tar.xz \
SmartOS 32-bit Binary: https://nodejs.org/dist/v4.3.2/node-v4.3.2-sunos-x86.tar.xz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v4.3.2/node-v4.3.2-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v4.3.2/node-v4.3.2-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v4.3.2/node-v4.3.2-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v4.3.2/node-v4.3.2-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v4.3.2/node-v4.3.2.tar.gz \
Other release files: https://nodejs.org/dist/v4.3.2/ \
Documentation: https://nodejs.org/docs/v4.3.2/api/

Shasums (GPG signing hash: SHA512, file hash: SHA256):

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA1

6380a36500de02a48758f204c1c5931af986d93609cfa06db1f91216bec9a5eb  node-v4.3.2-darwin-x64.tar.gz
47109a00cac344d80296c195451bb5eee7c21727fcef1594384ddfe1f852957a  node-v4.3.2-darwin-x64.tar.xz
f532e7cd3886ac9fdc774cc2add0db21483052c966a23aace7c84ec69b91981f  node-v4.3.2-headers.tar.gz
1f7c9a25c224ff3ce2da0cc858d813ba4359c3b6114e125b5c0abfcc6388a130  node-v4.3.2-headers.tar.xz
5d86c21d47cad54e3e5d7f36c1323b1e2416efc75e4615dafe35b202b59f26c8  node-v4.3.2-linux-arm64.tar.gz
606e85821fc88f389a20d4ed4e8e483d1e7627e808dada59931b282768041fb7  node-v4.3.2-linux-arm64.tar.xz
f8767c0bacc0ed516bfa22802dda573082b5bf463e5ea79a74087272ccb2d1e2  node-v4.3.2-linux-armv6l.tar.gz
3ea0b359efdb58ba693fdd67382ff0c086ee03899cde78c8aa2f29630c4d79fd  node-v4.3.2-linux-armv6l.tar.xz
f4d5b9ae277aaec50cdbb735480ec1a04e6e02bd820e2a1fcfa913c0c25f2e3d  node-v4.3.2-linux-armv7l.tar.gz
4e8cae9f7ad94edcc4eb29c9cfcf9e1e1f4cdd5a40156573d77ab352465a74c7  node-v4.3.2-linux-armv7l.tar.xz
568b13ae13588853cbf49b7a0772698bf50b6de1168f76cd7a7cd7d7d7ada7c7  node-v4.3.2-linux-ppc64le.tar.gz
a648fab221101408e5a2a740674686816c5ef26c9d3884c1c444fc14c3040b65  node-v4.3.2-linux-ppc64le.tar.xz
f307f173a96dff6652bc70d835af0c732864bb09875cf32a0b6ce7d70cebf77d  node-v4.3.2-linux-x64.tar.gz
4350d0431b49697517c6cca5d66adf5f74eb9101c52f52ae959fa94225822d44  node-v4.3.2-linux-x64.tar.xz
3c668a2128db3982987c596c028f9ec1d08c6abf63502e24826d40e552f80a8c  node-v4.3.2-linux-x86.tar.gz
ea45ccb7cf334789727dc5a1d1dfb0fa325e88de0b4a06d211dad6cb57c36e33  node-v4.3.2-linux-x86.tar.xz
f27eda062619f624d198012735cd996bbe7999715dbed1d6e47f3dbc4529b7a6  node-v4.3.2.pkg
80fdaa0c54b187d43933336b334b70bcbb79953350510006b07dbaa019929188  node-v4.3.2-sunos-x64.tar.gz
632d45df9172e54feeba5d5400220f946daf2023c282fb2527a3ff0b1e93be8f  node-v4.3.2-sunos-x64.tar.xz
04705681dde835c383028bc5bab3cab9d14dd7977e062ac824c2cfaf999f7c43  node-v4.3.2-sunos-x86.tar.gz
1ad766e22773f3ad9ac2274fbf2f277e24d1234957a358e64bea2ea3ce6fe66a  node-v4.3.2-sunos-x86.tar.xz
1f92f6d31f7292ce56db57d6703efccf3e6c945948f5901610cefa69e78d3498  node-v4.3.2.tar.gz
f0c5761a7e4f520ab8793cb2dcabbd7dfd7f5839ce4f2cf79d9d52d1c2da5bca  node-v4.3.2.tar.xz
49abd7e2855b4d124ad87f48162f6c0f0b26048bef17e1ab8f4ec8144d6914f3  node-v4.3.2-x64.msi
206ef690925231c9a527aeffc5b2c3d77d2497d91f581a21ba9689bfff9556a1  node-v4.3.2-x86.msi
606c44c42d17866c017c50c0afadad411d9492ac4281d2431b937f881911614e  win-x64/node.exe
451a40570099a95488d6438f175813629e0430f87f23c8659bc18dc42494820a  win-x64/node.lib
e41d283a6e3999dcf90742e97dd5d858a6b9d2b767087a4ef0087c0ed82f9bd9  win-x86/node.exe
1a6f27f9cee7dc117c51eb1a9c2b1ab336020fd46673b87c4bf2719ef4d2d00c  win-x86/node.lib
-----BEGIN PGP SIGNATURE-----

iQEcBAEBAgAGBQJW11/yAAoJEJM7AfQLXKlGYDYH/11wCIjqnoJq/mrx++DkwTPH
/xcS6GPg+M6okP/+12BDxzJ3veMBCXFEe21wKcX68B7vzc9QyfcWR/FDbuu3Jpwl
nhY0GwA8KqKQwKKl8ecchORWvVCX+b6D6bSSuC1gnO3w0C88usV0MLvdje2WjKdM
Rg4fy/lzwpNp63yxghwuEwqemxoAt/B/KlJhRqDktcHnsJC4RL8gRr5CXa5E0l81
ph71mN4nLnES9kYSwb7+XoXsbTXGwRMiWHjjT8s2DP3Tn+WeE1JJzT7JLRRN89KF
rUnjnEW/76Rb4mk+9W8S30wdBBNEUL0JXAqlEQGANcp5Kdpr1SwwkDoaWSZOprc=
=hltr
-----END PGP SIGNATURE-----

```
