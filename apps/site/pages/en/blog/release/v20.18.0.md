---
date: '2024-10-03T17:57:41.630Z'
category: release
title: Node v20.18.0 (LTS)
layout: blog-post
author: <PERSON><PERSON><PERSON>
---

## 2024-10-03, Version 20.18.0 'Iron' (LTS), @targos

### Notable Changes

### Experimental Network Inspection Support in Node.js

This update introduces the initial support for network inspection in Node.js.
Currently, this is an experimental feature, so you need to enable it using the `--experimental-network-inspection` flag.
With this feature enabled, you can inspect network activities occurring within a JavaScript application.

To use network inspection, start your Node.js application with the following command:

```console
$ node --inspect-wait --experimental-network-inspection index.js
```

Please note that the network inspection capabilities are in active development.
We are actively working on enhancing this feature and will continue to expand its functionality in future updates.

- Network inspection is limited to the `http` and `https` modules only.
- The Network tab in Chrome DevTools will not be available until the
  [feature request on the Chrome DevTools side](https://issues.chromium.org/issues/353924015) is addressed.

Contributed by <PERSON><PERSON><PERSON> in [#53593](https://github.com/nodejs/node/pull/53593) and [#54246](https://github.com/nodejs/node/pull/54246)

#### Exposes X509_V_FLAG_PARTIAL_CHAIN to tls.createSecureContext

This releases introduces a new option to the API `tls.createSecureContext`. From
now on, `tls.createSecureContext({ allowPartialTrustChain: true })` can be used
to treat intermediate (non-self-signed) certificates in the trust CA certificate
list as trusted.

Contributed by Anna Henningsen in [#54790](https://github.com/nodejs/node/pull/54790)

#### New option for vm.createContext() to create a context with a freezable globalThis

Node.js implements a flavor of `vm.createContext()` and friends that creates a context without contextifying its global
object when vm.constants.DONT_CONTEXTIFY is used. This is suitable when users want to freeze the context
(impossible when the global is contextified i.e. has interceptors installed) or speed up the global access if they
don't need the interceptor behavior.

Contributed by Joyee Cheung in [#54394](https://github.com/nodejs/node/pull/54394)

#### Deprecations

- \[[`64aa31f6e5`](https://github.com/nodejs/node/commit/64aa31f6e5)] - **repl**: doc-deprecate instantiating `node:repl` classes without `new` (Aviv Keller) [#54842](https://github.com/nodejs/node/pull/54842)
- \[[`4c52ee3d7f`](https://github.com/nodejs/node/commit/4c52ee3d7f)] - **zlib**: deprecate instantiating classes without new (Yagiz Nizipli) [#54708](https://github.com/nodejs/node/pull/54708)

#### Other Notable Changes

- \[[`b80da2f964`](https://github.com/nodejs/node/commit/b80da2f964)] - **buffer**: optimize createFromString (Robert Nagy) [#54324](https://github.com/nodejs/node/pull/54324)
- \[[`02b36cbd2d`](https://github.com/nodejs/node/commit/02b36cbd2d)] - **(SEMVER-MINOR)** **lib**: add EventSource Client (Aras Abbasi) [#51575](https://github.com/nodejs/node/pull/51575)
- \[[`879546a9bf`](https://github.com/nodejs/node/commit/879546a9bf)] - **(SEMVER-MINOR)** **src,lib**: add performance.uvMetricsInfo (Rafael Gonzaga) [#54413](https://github.com/nodejs/node/pull/54413)
- \[[`f789f4c92d`](https://github.com/nodejs/node/commit/f789f4c92d)] - **(SEMVER-MINOR)** **test_runner**: support module mocking (Colin Ihrig) [#52848](https://github.com/nodejs/node/pull/52848)
- \[[`4eb0749b6c`](https://github.com/nodejs/node/commit/4eb0749b6c)] - **(SEMVER-MINOR)** **url**: implement parse method for safer URL parsing (Ali Hassan) [#52280](https://github.com/nodejs/node/pull/52280)

### Commits

- \[[`013c48f0e9`](https://github.com/nodejs/node/commit/013c48f0e9)] - **benchmark**: --no-warnings to avoid DEP/ExpWarn log (Rafael Gonzaga) [#54928](https://github.com/nodejs/node/pull/54928)
- \[[`194fc113ac`](https://github.com/nodejs/node/commit/194fc113ac)] - **benchmark**: add buffer.isAscii benchmark (RafaelGSS) [#54740](https://github.com/nodejs/node/pull/54740)
- \[[`7410d51cb9`](https://github.com/nodejs/node/commit/7410d51cb9)] - **benchmark**: add buffer.isUtf8 bench (RafaelGSS) [#54740](https://github.com/nodejs/node/pull/54740)
- \[[`2393f21e8a`](https://github.com/nodejs/node/commit/2393f21e8a)] - **benchmark**: add access async version to bench (Rafael Gonzaga) [#54747](https://github.com/nodejs/node/pull/54747)
- \[[`b8779721f0`](https://github.com/nodejs/node/commit/b8779721f0)] - **benchmark**: enhance dc publish benchmark (Rafael Gonzaga) [#54745](https://github.com/nodejs/node/pull/54745)
- \[[`4078aa83ff`](https://github.com/nodejs/node/commit/4078aa83ff)] - **benchmark**: add match and doesNotMatch bench (RafaelGSS) [#54734](https://github.com/nodejs/node/pull/54734)
- \[[`66acab9976`](https://github.com/nodejs/node/commit/66acab9976)] - **benchmark**: add rejects and doesNotReject bench (RafaelGSS) [#54734](https://github.com/nodejs/node/pull/54734)
- \[[`6db777fb3a`](https://github.com/nodejs/node/commit/6db777fb3a)] - **benchmark**: add throws and doesNotThrow bench (RafaelGSS) [#54734](https://github.com/nodejs/node/pull/54734)
- \[[`8f101560ce`](https://github.com/nodejs/node/commit/8f101560ce)] - **benchmark**: add strictEqual and notStrictEqual bench (RafaelGSS) [#54734](https://github.com/nodejs/node/pull/54734)
- \[[`2c9e4c936e`](https://github.com/nodejs/node/commit/2c9e4c936e)] - **benchmark**: adds groups to better separate benchmarks (Giovanni Bucci) [#54393](https://github.com/nodejs/node/pull/54393)
- \[[`671c3ac633`](https://github.com/nodejs/node/commit/671c3ac633)] - **benchmark**: fix benchmark for file path and URL conversion (Early Riser) [#54190](https://github.com/nodejs/node/pull/54190)
- \[[`8c8708cb5b`](https://github.com/nodejs/node/commit/8c8708cb5b)] - **benchmark**: use assert.ok searchparams (Rafael Gonzaga) [#54334](https://github.com/nodejs/node/pull/54334)
- \[[`8b71fa79e2`](https://github.com/nodejs/node/commit/8b71fa79e2)] - **benchmark**: add stream.compose benchmark (jakecastelli) [#54308](https://github.com/nodejs/node/pull/54308)
- \[[`93ee36e3a0`](https://github.com/nodejs/node/commit/93ee36e3a0)] - **benchmark**: rename count to n (Rafael Gonzaga) [#54271](https://github.com/nodejs/node/pull/54271)
- \[[`f2971b6f0b`](https://github.com/nodejs/node/commit/f2971b6f0b)] - **benchmark**: change assert() to assert.ok() (Rafael Gonzaga) [#54254](https://github.com/nodejs/node/pull/54254)
- \[[`f48f2c212c`](https://github.com/nodejs/node/commit/f48f2c212c)] - **benchmark**: support --help in CLI (Aviv Keller) [#53358](https://github.com/nodejs/node/pull/53358)
- \[[`0309b0520b`](https://github.com/nodejs/node/commit/0309b0520b)] - **benchmark**: remove force option as force defaults to true (Yelim Koo) [#54203](https://github.com/nodejs/node/pull/54203)
- \[[`b6e8305b2d`](https://github.com/nodejs/node/commit/b6e8305b2d)] - **benchmark**: use assert.ok instead of assert (Rafael Gonzaga) [#54176](https://github.com/nodejs/node/pull/54176)
- \[[`90c660d26a`](https://github.com/nodejs/node/commit/90c660d26a)] - **benchmark**: add require-esm benchmark (Joyee Cheung) [#52166](https://github.com/nodejs/node/pull/52166)
- \[[`1b8584b52e`](https://github.com/nodejs/node/commit/1b8584b52e)] - **benchmark,doc**: add CPU scaling governor to perf (Rafael Gonzaga) [#54723](https://github.com/nodejs/node/pull/54723)
- \[[`0b9161b330`](https://github.com/nodejs/node/commit/0b9161b330)] - **benchmark,doc**: mention bar.R to the list of scripts (Rafael Gonzaga) [#54722](https://github.com/nodejs/node/pull/54722)
- \[[`84bf93b7ea`](https://github.com/nodejs/node/commit/84bf93b7ea)] - **buffer**: allow invalid encoding in from (Robert Nagy) [#54533](https://github.com/nodejs/node/pull/54533)
- \[[`d04246a0d7`](https://github.com/nodejs/node/commit/d04246a0d7)] - **buffer**: optimize byteLength for common encodings (Robert Nagy) [#54342](https://github.com/nodejs/node/pull/54342)
- \[[`f36831f694`](https://github.com/nodejs/node/commit/f36831f694)] - **buffer**: optimize createFromString (Robert Nagy) [#54324](https://github.com/nodejs/node/pull/54324)
- \[[`f5f40c8088`](https://github.com/nodejs/node/commit/f5f40c8088)] - **buffer**: optimize for common encodings (Robert Nagy) [#54319](https://github.com/nodejs/node/pull/54319)
- \[[`76c37703be`](https://github.com/nodejs/node/commit/76c37703be)] - **buffer**: add JSDoc to blob bytes method (Roberto Simonini) [#54117](https://github.com/nodejs/node/pull/54117)
- \[[`3012d31404`](https://github.com/nodejs/node/commit/3012d31404)] - **buffer**: use faster integer argument check (Robert Nagy) [#54089](https://github.com/nodejs/node/pull/54089)
- \[[`3505782801`](https://github.com/nodejs/node/commit/3505782801)] - **buffer**: make indexOf(byte) faster (Tobias Nießen) [#53455](https://github.com/nodejs/node/pull/53455)
- \[[`d285fc1f68`](https://github.com/nodejs/node/commit/d285fc1f68)] - **build**: upgrade clang-format to v18 (Aviv Keller) [#53957](https://github.com/nodejs/node/pull/53957)
- \[[`d288ec3b0a`](https://github.com/nodejs/node/commit/d288ec3b0a)] - **build**: fix conflicting V8 object print flags (Daeyeon Jeong) [#54785](https://github.com/nodejs/node/pull/54785)
- \[[`e862eecac9`](https://github.com/nodejs/node/commit/e862eecac9)] - **build**: do not build with code cache for core coverage collection (Joyee Cheung) [#54633](https://github.com/nodejs/node/pull/54633)
- \[[`f7a606eb96`](https://github.com/nodejs/node/commit/f7a606eb96)] - **build**: turn off `-Wrestrict` (Richard Lau) [#54737](https://github.com/nodejs/node/pull/54737)
- \[[`71ca2665e4`](https://github.com/nodejs/node/commit/71ca2665e4)] - **build**: reclaim disk space on macOS GHA runner (jakecastelli) [#54658](https://github.com/nodejs/node/pull/54658)
- \[[`82d8051c39`](https://github.com/nodejs/node/commit/82d8051c39)] - **build**: don't clean obj.target directory if it doesn't exist (Joyee Cheung) [#54337](https://github.com/nodejs/node/pull/54337)
- \[[`6e550b1f26`](https://github.com/nodejs/node/commit/6e550b1f26)] - **build**: update `ruff` to `0.5.2` (Aviv Keller) [#53909](https://github.com/nodejs/node/pull/53909)
- \[[`e2ea7b26d7`](https://github.com/nodejs/node/commit/e2ea7b26d7)] - **build**: fix ./configure --help format error (Zhenwei Jin) [#53066](https://github.com/nodejs/node/pull/53066)
- \[[`eb2402d569`](https://github.com/nodejs/node/commit/eb2402d569)] - **build**: enable building with shared uvwasi lib (Pooja D P) [#43987](https://github.com/nodejs/node/pull/43987)
- \[[`45732314d4`](https://github.com/nodejs/node/commit/45732314d4)] - **build**: sync V8 warning cflags with BUILD.gn (Michaël Zasso) [#52873](https://github.com/nodejs/node/pull/52873)
- \[[`6e0a2bb54c`](https://github.com/nodejs/node/commit/6e0a2bb54c)] - **build**: harmonize Clang checks (Michaël Zasso) [#52873](https://github.com/nodejs/node/pull/52873)
- \[[`3f78d4eb28`](https://github.com/nodejs/node/commit/3f78d4eb28)] - **cli**: add `--expose-gc` flag available to `NODE_OPTIONS` (Juan José) [#53078](https://github.com/nodejs/node/pull/53078)
- \[[`a110409b2a`](https://github.com/nodejs/node/commit/a110409b2a)] - **console**: use validateOneOf for colorMode validation (HEESEUNG) [#54245](https://github.com/nodejs/node/pull/54245)
- \[[`231ab788ea`](https://github.com/nodejs/node/commit/231ab788ea)] - **crypto**: reject dh,x25519,x448 in {Sign,Verify}Final (Huáng Jùnliàng) [#53774](https://github.com/nodejs/node/pull/53774)
- \[[`a5984e4570`](https://github.com/nodejs/node/commit/a5984e4570)] - **crypto**: return a clearer error when loading an unsupported pkcs12 (Tim Perry) [#54485](https://github.com/nodejs/node/pull/54485)
- \[[`f287cd77bd`](https://github.com/nodejs/node/commit/f287cd77bd)] - **crypto**: remove unused `kHashTypes` internal (Antoine du Hamel) [#54627](https://github.com/nodejs/node/pull/54627)
- \[[`1fc904f8c4`](https://github.com/nodejs/node/commit/1fc904f8c4)] - **deps**: update cjs-module-lexer to 1.4.1 (Node.js GitHub Bot) [#54846](https://github.com/nodejs/node/pull/54846)
- \[[`95b55c39b1`](https://github.com/nodejs/node/commit/95b55c39b1)] - **deps**: update simdutf to 5.5.0 (Node.js GitHub Bot) [#54434](https://github.com/nodejs/node/pull/54434)
- \[[`cf6ded5dd3`](https://github.com/nodejs/node/commit/cf6ded5dd3)] - **deps**: update cjs-module-lexer to 1.4.0 (Node.js GitHub Bot) [#54713](https://github.com/nodejs/node/pull/54713)
- \[[`7f8edce3f1`](https://github.com/nodejs/node/commit/7f8edce3f1)] - **deps**: update c-ares to v1.33.1 (Node.js GitHub Bot) [#54549](https://github.com/nodejs/node/pull/54549)
- \[[`9a4a7b7ecc`](https://github.com/nodejs/node/commit/9a4a7b7ecc)] - **deps**: update undici to 6.19.8 (Node.js GitHub Bot) [#54456](https://github.com/nodejs/node/pull/54456)
- \[[`87ca1d7fee`](https://github.com/nodejs/node/commit/87ca1d7fee)] - **deps**: update simdutf to 5.3.4 (Node.js GitHub Bot) [#54312](https://github.com/nodejs/node/pull/54312)
- \[[`d3a743f182`](https://github.com/nodejs/node/commit/d3a743f182)] - **deps**: update zlib to 1.3.0.1-motley-71660e1 (Node.js GitHub Bot) [#53464](https://github.com/nodejs/node/pull/53464)
- \[[`926981aa9f`](https://github.com/nodejs/node/commit/926981aa9f)] - **deps**: update zlib to 1.3.0.1-motley-c2469fd (Node.js GitHub Bot) [#53464](https://github.com/nodejs/node/pull/53464)
- \[[`654c8d1fdc`](https://github.com/nodejs/node/commit/654c8d1fdc)] - **deps**: update zlib to 1.3.0.1-motley-68e57e6 (Node.js GitHub Bot) [#53464](https://github.com/nodejs/node/pull/53464)
- \[[`2477e79172`](https://github.com/nodejs/node/commit/2477e79172)] - **deps**: update zlib to 1.3.0.1-motley-8b7eff8 (Node.js GitHub Bot) [#53464](https://github.com/nodejs/node/pull/53464)
- \[[`3d8113faf5`](https://github.com/nodejs/node/commit/3d8113faf5)] - **deps**: update zlib to 1.3.0.1-motley-e432200 (Node.js GitHub Bot) [#53464](https://github.com/nodejs/node/pull/53464)
- \[[`ac294e3db4`](https://github.com/nodejs/node/commit/ac294e3db4)] - **deps**: update zlib to 1.3.0.1-motley-887bb57 (Node.js GitHub Bot) [#53464](https://github.com/nodejs/node/pull/53464)
- \[[`239588b968`](https://github.com/nodejs/node/commit/239588b968)] - **deps**: update c-ares to v1.33.0 (Node.js GitHub Bot) [#54198](https://github.com/nodejs/node/pull/54198)
- \[[`6e7de37ed3`](https://github.com/nodejs/node/commit/6e7de37ed3)] - **deps**: update undici to 6.19.7 (Node.js GitHub Bot) [#54286](https://github.com/nodejs/node/pull/54286)
- \[[`38aa9d6ea9`](https://github.com/nodejs/node/commit/38aa9d6ea9)] - **deps**: update acorn to 8.12.1 (Node.js GitHub Bot) [#53465](https://github.com/nodejs/node/pull/53465)
- \[[`d30145f663`](https://github.com/nodejs/node/commit/d30145f663)] - **deps**: update undici to 6.19.5 (Node.js GitHub Bot) [#54076](https://github.com/nodejs/node/pull/54076)
- \[[`c169d9c12b`](https://github.com/nodejs/node/commit/c169d9c12b)] - **deps**: update simdutf to 5.3.1 (Node.js GitHub Bot) [#54196](https://github.com/nodejs/node/pull/54196)
- \[[`92f3447957`](https://github.com/nodejs/node/commit/92f3447957)] - **doc**: add missing EventSource docs to globals (Matthew Aitken) [#55022](https://github.com/nodejs/node/pull/55022)
- \[[`2879ce9681`](https://github.com/nodejs/node/commit/2879ce9681)] - **doc**: fix broken Android building link (Niklas Wenzel) [#54922](https://github.com/nodejs/node/pull/54922)
- \[[`096623b59a`](https://github.com/nodejs/node/commit/096623b59a)] - **doc**: add support link for aduh95 (Antoine du Hamel) [#54866](https://github.com/nodejs/node/pull/54866)
- \[[`1dfd238781`](https://github.com/nodejs/node/commit/1dfd238781)] - **doc**: run license-builder (github-actions\[bot]) [#54854](https://github.com/nodejs/node/pull/54854)
- \[[`a6c748fffb`](https://github.com/nodejs/node/commit/a6c748fffb)] - **doc**: experimental flag for global accessible APIs (Chengzhong Wu) [#54330](https://github.com/nodejs/node/pull/54330)
- \[[`d48a22fa14`](https://github.com/nodejs/node/commit/d48a22fa14)] - **doc**: add `ERR_INVALID_ADDRESS` to `errors.md` (Aviv Keller) [#54661](https://github.com/nodejs/node/pull/54661)
- \[[`4a840cecfa`](https://github.com/nodejs/node/commit/4a840cecfa)] - **doc**: add support link for mcollina (Matteo Collina) [#54786](https://github.com/nodejs/node/pull/54786)
- \[[`ec22d86512`](https://github.com/nodejs/node/commit/ec22d86512)] - **doc**: mark `--conditions` CLI flag as stable (Guy Bedford) [#54209](https://github.com/nodejs/node/pull/54209)
- \[[`77c702ca07`](https://github.com/nodejs/node/commit/77c702ca07)] - **doc**: fix typo in recognizing-contributors (Tobias Nießen) [#54822](https://github.com/nodejs/node/pull/54822)
- \[[`62953ef9fb`](https://github.com/nodejs/node/commit/62953ef9fb)] - **doc**: clarify `--max-old-space-size` and `--max-semi-space-size` units (Alexandre ABRIOUX) [#54477](https://github.com/nodejs/node/pull/54477)
- \[[`e2bab0f2b2`](https://github.com/nodejs/node/commit/e2bab0f2b2)] - **doc**: replace --allow-fs-read by --allow-fs-write in related section (M1CK431) [#54427](https://github.com/nodejs/node/pull/54427)
- \[[`9cbfd5b33a`](https://github.com/nodejs/node/commit/9cbfd5b33a)] - **doc**: add support link for marco-ippolito (Marco Ippolito) [#54789](https://github.com/nodejs/node/pull/54789)
- \[[`53167b29ef`](https://github.com/nodejs/node/commit/53167b29ef)] - **doc**: fix typo (Michael Dawson) [#54640](https://github.com/nodejs/node/pull/54640)
- \[[`87f78a35f7`](https://github.com/nodejs/node/commit/87f78a35f7)] - **doc**: fix webcrypto.md AES-GCM backticks (Filip Skokan) [#54621](https://github.com/nodejs/node/pull/54621)
- \[[`7c83c15221`](https://github.com/nodejs/node/commit/7c83c15221)] - **doc**: add documentation about os.tmpdir() overrides (Joyee Cheung) [#54613](https://github.com/nodejs/node/pull/54613)
- \[[`4bfd832d70`](https://github.com/nodejs/node/commit/4bfd832d70)] - **doc**: add support me link for anonrig (Yagiz Nizipli) [#54611](https://github.com/nodejs/node/pull/54611)
- \[[`22a103e5ec`](https://github.com/nodejs/node/commit/22a103e5ec)] - **doc**: add alert on REPL from TCP socket (Rafael Gonzaga) [#54594](https://github.com/nodejs/node/pull/54594)
- \[[`b6374c24e1`](https://github.com/nodejs/node/commit/b6374c24e1)] - **doc**: fix typo in styleText description (Rafael Gonzaga) [#54616](https://github.com/nodejs/node/pull/54616)
- \[[`2f5b98ee1f`](https://github.com/nodejs/node/commit/2f5b98ee1f)] - **doc**: add getHeapStatistics() property descriptions (Benji Marinacci) [#54584](https://github.com/nodejs/node/pull/54584)
- \[[`482302b99b`](https://github.com/nodejs/node/commit/482302b99b)] - **doc**: fix information about including coverage files (Aviv Keller) [#54527](https://github.com/nodejs/node/pull/54527)
- \[[`b3708e7df4`](https://github.com/nodejs/node/commit/b3708e7df4)] - **doc**: support collaborators - talk amplification (Michael Dawson) [#54508](https://github.com/nodejs/node/pull/54508)
- \[[`c86fe23012`](https://github.com/nodejs/node/commit/c86fe23012)] - **doc**: add note about shasum generation failure (Marco Ippolito) [#54487](https://github.com/nodejs/node/pull/54487)
- \[[`d53e6cf755`](https://github.com/nodejs/node/commit/d53e6cf755)] - **doc**: fix capitalization in module.md (shallow-beach) [#54488](https://github.com/nodejs/node/pull/54488)
- \[[`cdc6713f18`](https://github.com/nodejs/node/commit/cdc6713f18)] - **doc**: add esm examples to node:https (Alfredo González) [#54399](https://github.com/nodejs/node/pull/54399)
- \[[`1ac1fe4e65`](https://github.com/nodejs/node/commit/1ac1fe4e65)] - **doc**: fix error description of the max header size (Egawa Ryo) [#54125](https://github.com/nodejs/node/pull/54125)
- \[[`244542b720`](https://github.com/nodejs/node/commit/244542b720)] - **doc**: add git node security --cleanup (Rafael Gonzaga) [#54381](https://github.com/nodejs/node/pull/54381)
- \[[`69fb71f54c`](https://github.com/nodejs/node/commit/69fb71f54c)] - **doc**: add note on weakness of permission model (Tobias Nießen) [#54268](https://github.com/nodejs/node/pull/54268)
- \[[`83b2cb908b`](https://github.com/nodejs/node/commit/83b2cb908b)] - **doc**: add versions when `--watch-preserve-output` was added (Théo LUDWIG) [#54328](https://github.com/nodejs/node/pull/54328)
- \[[`460fb49483`](https://github.com/nodejs/node/commit/460fb49483)] - **doc**: replace v19 mention in Current release (Rafael Gonzaga) [#54361](https://github.com/nodejs/node/pull/54361)
- \[[`994b46a160`](https://github.com/nodejs/node/commit/994b46a160)] - **doc**: correct peformance entry types (Jason Zhang) [#54263](https://github.com/nodejs/node/pull/54263)
- \[[`f142e668cb`](https://github.com/nodejs/node/commit/f142e668cb)] - **doc**: fix typo in method name in the sea doc (Eliyah Sundström) [#54027](https://github.com/nodejs/node/pull/54027)
- \[[`9529a30dba`](https://github.com/nodejs/node/commit/9529a30dba)] - **doc**: mark process.nextTick legacy (Marco Ippolito) [#51280](https://github.com/nodejs/node/pull/51280)
- \[[`7e25fabb91`](https://github.com/nodejs/node/commit/7e25fabb91)] - **doc**: add esm examples to node:http2 (Alfredo González) [#54292](https://github.com/nodejs/node/pull/54292)
- \[[`6a4f05e384`](https://github.com/nodejs/node/commit/6a4f05e384)] - **doc**: explicitly mention node:fs module restriction (Rafael Gonzaga) [#54269](https://github.com/nodejs/node/pull/54269)
- \[[`53f5c54997`](https://github.com/nodejs/node/commit/53f5c54997)] - **doc**: warn for windows build bug (Jason Zhang) [#54217](https://github.com/nodejs/node/pull/54217)
- \[[`07bde054f3`](https://github.com/nodejs/node/commit/07bde054f3)] - **doc**: make some parameters optional in `tracingChannel.traceCallback` (Deokjin Kim) [#54068](https://github.com/nodejs/node/pull/54068)
- \[[`62bf03b5f1`](https://github.com/nodejs/node/commit/62bf03b5f1)] - **doc**: add esm examples to node:dns (Alfredo González) [#54172](https://github.com/nodejs/node/pull/54172)
- \[[`fb2b19184b`](https://github.com/nodejs/node/commit/fb2b19184b)] - **doc**: add KevinEady as a triager (Chengzhong Wu) [#54179](https://github.com/nodejs/node/pull/54179)
- \[[`24976bfba0`](https://github.com/nodejs/node/commit/24976bfba0)] - **doc**: add esm examples to node:console (Alfredo González) [#54108](https://github.com/nodejs/node/pull/54108)
- \[[`4e7edc40f7`](https://github.com/nodejs/node/commit/4e7edc40f7)] - **doc**: fix sea assets example (Sadzurami) [#54192](https://github.com/nodejs/node/pull/54192)
- \[[`322b5d91e1`](https://github.com/nodejs/node/commit/322b5d91e1)] - **doc**: add links to security steward companies (Aviv Keller) [#52981](https://github.com/nodejs/node/pull/52981)
- \[[`6ab271510e`](https://github.com/nodejs/node/commit/6ab271510e)] - **doc**: move `onread` option from `socket.connect()` to `new net.socket()` (sendoru) [#54194](https://github.com/nodejs/node/pull/54194)
- \[[`39c30ea08f`](https://github.com/nodejs/node/commit/39c30ea08f)] - **doc**: move release key for Myles Borins (Richard Lau) [#54059](https://github.com/nodejs/node/pull/54059)
- \[[`e9fc54804a`](https://github.com/nodejs/node/commit/e9fc54804a)] - **doc**: refresh instructions for building node from source (Liran Tal) [#53768](https://github.com/nodejs/node/pull/53768)
- \[[`f131dc625a`](https://github.com/nodejs/node/commit/f131dc625a)] - **doc**: add documentation for blob.bytes() method (jaexxin) [#54114](https://github.com/nodejs/node/pull/54114)
- \[[`8d41bb900b`](https://github.com/nodejs/node/commit/8d41bb900b)] - **doc**: add missing new lines to custom test reporter examples (Eddie Abbondanzio) [#54152](https://github.com/nodejs/node/pull/54152)
- \[[`2acaeaba77`](https://github.com/nodejs/node/commit/2acaeaba77)] - **doc**: update list of Triagers on the `README.md` (Antoine du Hamel) [#54138](https://github.com/nodejs/node/pull/54138)
- \[[`fff8eb2792`](https://github.com/nodejs/node/commit/fff8eb2792)] - **doc**: expand troubleshooting section (Liran Tal) [#53808](https://github.com/nodejs/node/pull/53808)
- \[[`402121520f`](https://github.com/nodejs/node/commit/402121520f)] - **doc**: clarify `useCodeCache` setting for cross-platform SEA generation (Yelim Koo) [#53994](https://github.com/nodejs/node/pull/53994)
- \[[`272484b8b2`](https://github.com/nodejs/node/commit/272484b8b2)] - **doc**: test for cli options (Aras Abbasi) [#51623](https://github.com/nodejs/node/pull/51623)
- \[[`c4d0ca4710`](https://github.com/nodejs/node/commit/c4d0ca4710)] - **doc, build**: fixup build docs (Aviv Keller) [#54899](https://github.com/nodejs/node/pull/54899)
- \[[`2e3e17748b`](https://github.com/nodejs/node/commit/2e3e17748b)] - **doc, child_process**: add esm snippets (Aviv Keller) [#53616](https://github.com/nodejs/node/pull/53616)
- \[[`c40b4b4f27`](https://github.com/nodejs/node/commit/c40b4b4f27)] - **doc, meta**: fix broken link in `onboarding.md` (Aviv Keller) [#54886](https://github.com/nodejs/node/pull/54886)
- \[[`beff587b94`](https://github.com/nodejs/node/commit/beff587b94)] - **doc, meta**: add missing `,` to `BUILDING.md` (Aviv Keller) [#54409](https://github.com/nodejs/node/pull/54409)
- \[[`c114585430`](https://github.com/nodejs/node/commit/c114585430)] - **doc, meta**: replace command with link to keys (Aviv Keller) [#53745](https://github.com/nodejs/node/pull/53745)
- \[[`0843077a99`](https://github.com/nodejs/node/commit/0843077a99)] - **doc, test**: simplify test README table (Aviv Keller) [#53971](https://github.com/nodejs/node/pull/53971)
- \[[`2df7bc0e32`](https://github.com/nodejs/node/commit/2df7bc0e32)] - **doc,tools**: enforce use of `node:` prefix (Antoine du Hamel) [#53950](https://github.com/nodejs/node/pull/53950)
- \[[`0dd4639391`](https://github.com/nodejs/node/commit/0dd4639391)] - **esm**: fix support for `URL` instances in `import.meta.resolve` (Antoine du Hamel) [#54690](https://github.com/nodejs/node/pull/54690)
- \[[`f0c55e206d`](https://github.com/nodejs/node/commit/f0c55e206d)] - **fs**: refactor rimraf to avoid using primordials (Yagiz Nizipli) [#54834](https://github.com/nodejs/node/pull/54834)
- \[[`f568384bbd`](https://github.com/nodejs/node/commit/f568384bbd)] - **fs**: refactor handleTimestampsAndMode to remove redundant call (HEESEUNG) [#54369](https://github.com/nodejs/node/pull/54369)
- \[[`2fb7cc9715`](https://github.com/nodejs/node/commit/2fb7cc9715)] - **fs**: fix typings (Yagiz Nizipli) [#53626](https://github.com/nodejs/node/pull/53626)
- \[[`596940cfa0`](https://github.com/nodejs/node/commit/596940cfa0)] - **http**: reduce likelihood of race conditions on keep-alive timeout (jazelly) [#54863](https://github.com/nodejs/node/pull/54863)
- \[[`6e13a7ba02`](https://github.com/nodejs/node/commit/6e13a7ba02)] - **http**: remove prototype primordials (Antoine du Hamel) [#53698](https://github.com/nodejs/node/pull/53698)
- \[[`99f96eb3f7`](https://github.com/nodejs/node/commit/99f96eb3f7)] - **http2**: remove prototype primordials (Antoine du Hamel) [#53696](https://github.com/nodejs/node/pull/53696)
- \[[`41f5eacc1a`](https://github.com/nodejs/node/commit/41f5eacc1a)] - **https**: only use default ALPNProtocols when appropriate (Brian White) [#54411](https://github.com/nodejs/node/pull/54411)
- \[[`59a39520e1`](https://github.com/nodejs/node/commit/59a39520e1)] - **(SEMVER-MINOR)** **inspector**: support `Network.loadingFailed` event (Kohei Ueno) [#54246](https://github.com/nodejs/node/pull/54246)
- \[[`d1007fb1a9`](https://github.com/nodejs/node/commit/d1007fb1a9)] - **inspector**: provide detailed info to fix DevTools frontend errors (Kohei Ueno) [#54156](https://github.com/nodejs/node/pull/54156)
- \[[`3b93507949`](https://github.com/nodejs/node/commit/3b93507949)] - **(SEMVER-MINOR)** **inspector**: add initial support for network inspection (Kohei Ueno) [#53593](https://github.com/nodejs/node/pull/53593)
- \[[`fc37b801c8`](https://github.com/nodejs/node/commit/fc37b801c8)] - **lib**: remove unnecessary async (jakecastelli) [#54829](https://github.com/nodejs/node/pull/54829)
- \[[`d86f24787b`](https://github.com/nodejs/node/commit/d86f24787b)] - **lib**: make WeakRef safe in abort_controller (jazelly) [#54791](https://github.com/nodejs/node/pull/54791)
- \[[`77c59224e5`](https://github.com/nodejs/node/commit/77c59224e5)] - **lib**: add note about removing `node:sys` module (Rafael Gonzaga) [#54743](https://github.com/nodejs/node/pull/54743)
- \[[`b8c06dce02`](https://github.com/nodejs/node/commit/b8c06dce02)] - **lib**: ensure no holey array in fixed_queue (Jason Zhang) [#54537](https://github.com/nodejs/node/pull/54537)
- \[[`b85c8ce1fc`](https://github.com/nodejs/node/commit/b85c8ce1fc)] - **lib**: refactor SubtleCrypto experimental warnings (Filip Skokan) [#54620](https://github.com/nodejs/node/pull/54620)
- \[[`e84812c1b5`](https://github.com/nodejs/node/commit/e84812c1b5)] - **lib**: respect terminal capabilities on styleText (Rafael Gonzaga) [#54389](https://github.com/nodejs/node/pull/54389)
- \[[`c004abaf17`](https://github.com/nodejs/node/commit/c004abaf17)] - **lib**: replace spread operator with primordials function (YoonSoo_Shin) [#54053](https://github.com/nodejs/node/pull/54053)
- \[[`b79aeabc4d`](https://github.com/nodejs/node/commit/b79aeabc4d)] - **lib**: avoid for of loop and remove unnecessary variable in zlib (YoonSoo_Shin) [#54258](https://github.com/nodejs/node/pull/54258)
- \[[`f4085363c6`](https://github.com/nodejs/node/commit/f4085363c6)] - **lib**: fix unhandled errors in webstream adapters (Fedor Indutny) [#54206](https://github.com/nodejs/node/pull/54206)
- \[[`1ad857e748`](https://github.com/nodejs/node/commit/1ad857e748)] - **lib**: fix typos in comments within internal/streams (YoonSoo_Shin) [#54093](https://github.com/nodejs/node/pull/54093)
- \[[`02b36cbd2d`](https://github.com/nodejs/node/commit/02b36cbd2d)] - **(SEMVER-MINOR)** **lib**: add EventSource Client (Aras Abbasi) [#51575](https://github.com/nodejs/node/pull/51575)
- \[[`afbf2c0530`](https://github.com/nodejs/node/commit/afbf2c0530)] - **lib,permission**: support Buffer to permission.has (Rafael Gonzaga) [#54104](https://github.com/nodejs/node/pull/54104)
- \[[`54af47395d`](https://github.com/nodejs/node/commit/54af47395d)] - **meta**: bump peter-evans/create-pull-request from 6.1.0 to 7.0.1 (dependabot\[bot]) [#54820](https://github.com/nodejs/node/pull/54820)
- \[[`a0c10f2ed9`](https://github.com/nodejs/node/commit/a0c10f2ed9)] - **meta**: add `Windows ARM64` to flaky-tests list (Aviv Keller) [#54693](https://github.com/nodejs/node/pull/54693)
- \[[`27b06880e1`](https://github.com/nodejs/node/commit/27b06880e1)] - **meta**: bump actions/setup-python from 5.1.1 to 5.2.0 (Rich Trott) [#54691](https://github.com/nodejs/node/pull/54691)
- \[[`8747af1037`](https://github.com/nodejs/node/commit/8747af1037)] - **meta**: update sccache to v0.8.1 (Aviv Keller) [#54720](https://github.com/nodejs/node/pull/54720)
- \[[`3f753d87a6`](https://github.com/nodejs/node/commit/3f753d87a6)] - **meta**: bump step-security/harden-runner from 2.9.0 to 2.9.1 (dependabot\[bot]) [#54704](https://github.com/nodejs/node/pull/54704)
- \[[`6f103ae25d`](https://github.com/nodejs/node/commit/6f103ae25d)] - **meta**: bump actions/upload-artifact from 4.3.4 to 4.4.0 (dependabot\[bot]) [#54703](https://github.com/nodejs/node/pull/54703)
- \[[`3e6a9bb04e`](https://github.com/nodejs/node/commit/3e6a9bb04e)] - **meta**: bump github/codeql-action from 3.25.15 to 3.26.6 (dependabot\[bot]) [#54702](https://github.com/nodejs/node/pull/54702)
- \[[`c666ebc4e4`](https://github.com/nodejs/node/commit/c666ebc4e4)] - **meta**: fix links in `SECURITY.md` (Aviv Keller) [#54696](https://github.com/nodejs/node/pull/54696)
- \[[`4d361b3bed`](https://github.com/nodejs/node/commit/4d361b3bed)] - **meta**: fix `contributing` codeowners (Aviv Keller) [#54641](https://github.com/nodejs/node/pull/54641)
- \[[`36931aa183`](https://github.com/nodejs/node/commit/36931aa183)] - **meta**: remind users to use a supported version in bug reports (Aviv Keller) [#54481](https://github.com/nodejs/node/pull/54481)
- \[[`cf283d9ca7`](https://github.com/nodejs/node/commit/cf283d9ca7)] - **meta**: run coverage-windows when `vcbuild.bat` updated (Aviv Keller) [#54412](https://github.com/nodejs/node/pull/54412)
- \[[`67ca397c9f`](https://github.com/nodejs/node/commit/67ca397c9f)] - **meta**: add test-permission-\* CODEOWNERS (Rafael Gonzaga) [#54267](https://github.com/nodejs/node/pull/54267)
- \[[`b61a2f5b79`](https://github.com/nodejs/node/commit/b61a2f5b79)] - **meta**: move one or more collaborators to emeritus (Node.js GitHub Bot) [#54210](https://github.com/nodejs/node/pull/54210)
- \[[`dd8ab83667`](https://github.com/nodejs/node/commit/dd8ab83667)] - **meta**: add module label for the lib/internal/modules folder (Aviv Keller) [#52858](https://github.com/nodejs/node/pull/52858)
- \[[`db78978d17`](https://github.com/nodejs/node/commit/db78978d17)] - **meta**: bump `actions/upload-artifact` from 4.3.3 to 4.3.4 (dependabot\[bot]) [#54166](https://github.com/nodejs/node/pull/54166)
- \[[`ca808dd9e5`](https://github.com/nodejs/node/commit/ca808dd9e5)] - **meta**: bump `actions/download-artifact` from 4.1.7 to 4.1.8 (dependabot\[bot]) [#54167](https://github.com/nodejs/node/pull/54167)
- \[[`a35d980146`](https://github.com/nodejs/node/commit/a35d980146)] - **meta**: bump actions/setup-python from 5.1.0 to 5.1.1 (dependabot\[bot]) [#54165](https://github.com/nodejs/node/pull/54165)
- \[[`3a103c3a17`](https://github.com/nodejs/node/commit/3a103c3a17)] - **meta**: bump `step-security/harden-runner` from 2.8.1 to 2.9.0 (dependabot\[bot]) [#54169](https://github.com/nodejs/node/pull/54169)
- \[[`775ebbe0e8`](https://github.com/nodejs/node/commit/775ebbe0e8)] - **meta**: bump `actions/setup-node` from 4.0.2 to 4.0.3 (dependabot\[bot]) [#54170](https://github.com/nodejs/node/pull/54170)
- \[[`7d5dd6f1d1`](https://github.com/nodejs/node/commit/7d5dd6f1d1)] - **meta**: bump `github/codeql-action` from 3.25.11 to 3.25.15 (dependabot\[bot]) [#54168](https://github.com/nodejs/node/pull/54168)
- \[[`80dd38dde3`](https://github.com/nodejs/node/commit/80dd38dde3)] - **meta**: bump `ossf/scorecard-action` from 2.3.3 to 2.4.0 (dependabot\[bot]) [#54171](https://github.com/nodejs/node/pull/54171)
- \[[`90b632ee02`](https://github.com/nodejs/node/commit/90b632ee02)] - **module**: warn on detection in typeless package (Geoffrey Booth) [#52168](https://github.com/nodejs/node/pull/52168)
- \[[`3011927aab`](https://github.com/nodejs/node/commit/3011927aab)] - **node-api**: add external buffer creation benchmark (Chengzhong Wu) [#54877](https://github.com/nodejs/node/pull/54877)
- \[[`7611093e11`](https://github.com/nodejs/node/commit/7611093e11)] - **node-api**: add support for UTF-8 and Latin-1 property keys (Mert Can Altin) [#52984](https://github.com/nodejs/node/pull/52984)
- \[[`d65a8f377c`](https://github.com/nodejs/node/commit/d65a8f377c)] - **node-api**: remove RefBase and CallbackWrapper (Vladimir Morozov) [#53590](https://github.com/nodejs/node/pull/53590)
- \[[`309cb1cbd2`](https://github.com/nodejs/node/commit/309cb1cbd2)] - **path**: remove `StringPrototypeCharCodeAt` from `posix.extname` (Aviv Keller) [#54546](https://github.com/nodejs/node/pull/54546)
- \[[`2859b4ba9a`](https://github.com/nodejs/node/commit/2859b4ba9a)] - **path**: change `posix.join` to use array (Wiyeong Seo) [#54331](https://github.com/nodejs/node/pull/54331)
- \[[`c61cee2138`](https://github.com/nodejs/node/commit/c61cee2138)] - **path**: fix relative on Windows (Hüseyin Açacak) [#53991](https://github.com/nodejs/node/pull/53991)
- \[[`329be5cc35`](https://github.com/nodejs/node/commit/329be5cc35)] - **path**: use the correct name in `validateString` (Benjamin Pasero) [#53669](https://github.com/nodejs/node/pull/53669)
- \[[`a9837267cb`](https://github.com/nodejs/node/commit/a9837267cb)] - **repl**: avoid interpreting 'npm' as a command when errors are recoverable (Shima Ryuhei) [#54848](https://github.com/nodejs/node/pull/54848)
- \[[`d6a2317961`](https://github.com/nodejs/node/commit/d6a2317961)] - **repl**: doc-deprecate instantiating `node:repl` classes without `new` (Aviv Keller) [#54842](https://github.com/nodejs/node/pull/54842)
- \[[`7f09d983f3`](https://github.com/nodejs/node/commit/7f09d983f3)] - **sea**: don't set code cache flags when snapshot is used (Joyee Cheung) [#54120](https://github.com/nodejs/node/pull/54120)
- \[[`85542b094c`](https://github.com/nodejs/node/commit/85542b094c)] - **src**: add Cleanable class to Environment (Gabriel Schulhof) [#54880](https://github.com/nodejs/node/pull/54880)
- \[[`8422064127`](https://github.com/nodejs/node/commit/8422064127)] - **src**: remove redundant AESCipherMode (Tobias Nießen) [#54438](https://github.com/nodejs/node/pull/54438)
- \[[`342c32483a`](https://github.com/nodejs/node/commit/342c32483a)] - **src**: handle errors correctly in `permission.cc` (Michaël Zasso) [#54541](https://github.com/nodejs/node/pull/54541)
- \[[`90ff714699`](https://github.com/nodejs/node/commit/90ff714699)] - **src**: return `v8::Object` from error constructors (Michaël Zasso) [#54541](https://github.com/nodejs/node/pull/54541)
- \[[`872856cfcb`](https://github.com/nodejs/node/commit/872856cfcb)] - **src**: improve `buffer.transcode` performance (Yagiz Nizipli) [#54153](https://github.com/nodejs/node/pull/54153)
- \[[`91936ebd12`](https://github.com/nodejs/node/commit/91936ebd12)] - **src**: skip inspector wait in internal workers (Chengzhong Wu) [#54219](https://github.com/nodejs/node/pull/54219)
- \[[`**********`](https://github.com/nodejs/node/commit/**********)] - **src**: account for OpenSSL unexpected version (Shelley Vohr) [#54038](https://github.com/nodejs/node/pull/54038)
- \[[`87167fa248`](https://github.com/nodejs/node/commit/87167fa248)] - **src**: use `args.This()` instead of `Holder` (Michaël Zasso) [#53474](https://github.com/nodejs/node/pull/53474)
- \[[`b05c56e4be`](https://github.com/nodejs/node/commit/b05c56e4be)] - **src**: simplify `size() == 0` checks (Yagiz Nizipli) [#53440](https://github.com/nodejs/node/pull/53440)
- \[[`d53e53699c`](https://github.com/nodejs/node/commit/d53e53699c)] - **src**: fix execArgv in worker (theanarkh) [#53029](https://github.com/nodejs/node/pull/53029)
- \[[`21776a34b5`](https://github.com/nodejs/node/commit/21776a34b5)] - **src**: make sure pass the `argv` to worker threads (theanarkh) [#52827](https://github.com/nodejs/node/pull/52827)
- \[[`3aaae68ec8`](https://github.com/nodejs/node/commit/3aaae68ec8)] - **(SEMVER-MINOR)** **src,lib**: add performance.uvMetricsInfo (Rafael Gonzaga) [#54413](https://github.com/nodejs/node/pull/54413)
- \[[`ef1c0d7def`](https://github.com/nodejs/node/commit/ef1c0d7def)] - **src,permission**: handle process.chdir on pm (Rafael Gonzaga) [#53175](https://github.com/nodejs/node/pull/53175)
- \[[`0c32918eef`](https://github.com/nodejs/node/commit/0c32918eef)] - **stream**: change stream to use index instead of `for...of` (Wiyeong Seo) [#54474](https://github.com/nodejs/node/pull/54474)
- \[[`337cd412b5`](https://github.com/nodejs/node/commit/337cd412b5)] - **stream**: make checking pendingcb on WritableStream backward compatible (jakecastelli) [#54142](https://github.com/nodejs/node/pull/54142)
- \[[`713fc0c9eb`](https://github.com/nodejs/node/commit/713fc0c9eb)] - **stream**: throw TypeError when criteria fulfilled in getIterator (jakecastelli) [#53825](https://github.com/nodejs/node/pull/53825)
- \[[`9686153616`](https://github.com/nodejs/node/commit/9686153616)] - **stream**: fix util.inspect for compression/decompressionStream (Mert Can Altin) [#52283](https://github.com/nodejs/node/pull/52283)
- \[[`76110b0b43`](https://github.com/nodejs/node/commit/76110b0b43)] - **test**: adjust test-tls-junk-server for OpenSSL32 (Michael Dawson) [#54926](https://github.com/nodejs/node/pull/54926)
- \[[`4092889371`](https://github.com/nodejs/node/commit/4092889371)] - **test**: adjust tls test for OpenSSL32 (Michael Dawson) [#54909](https://github.com/nodejs/node/pull/54909)
- \[[`5d48543a16`](https://github.com/nodejs/node/commit/5d48543a16)] - **test**: fix test-http2-socket-close.js (Hüseyin Açacak) [#54900](https://github.com/nodejs/node/pull/54900)
- \[[`8048c2eaed`](https://github.com/nodejs/node/commit/8048c2eaed)] - **test**: improve test-internal-fs-syncwritestream (Sunghoon) [#54671](https://github.com/nodejs/node/pull/54671)
- \[[`597bc14c90`](https://github.com/nodejs/node/commit/597bc14c90)] - **test**: deflake test-dns (Luigi Pinca) [#54902](https://github.com/nodejs/node/pull/54902)
- \[[`a9fc8d9cfa`](https://github.com/nodejs/node/commit/a9fc8d9cfa)] - **test**: fix test test-tls-dhe for OpenSSL32 (Michael Dawson) [#54903](https://github.com/nodejs/node/pull/54903)
- \[[`1b3b4f4a9f`](https://github.com/nodejs/node/commit/1b3b4f4a9f)] - **test**: use correct file naming syntax for `util-parse-env` (Aviv Keller) [#53705](https://github.com/nodejs/node/pull/53705)
- \[[`9db46b5ea3`](https://github.com/nodejs/node/commit/9db46b5ea3)] - **test**: add missing await (Luigi Pinca) [#54828](https://github.com/nodejs/node/pull/54828)
- \[[`124f715679`](https://github.com/nodejs/node/commit/124f715679)] - **test**: move more url tests to `node:test` (Yagiz Nizipli) [#54636](https://github.com/nodejs/node/pull/54636)
- \[[`d2ec96150a`](https://github.com/nodejs/node/commit/d2ec96150a)] - **test**: strip color chars in `test-runner-run` (Giovanni Bucci) [#54552](https://github.com/nodejs/node/pull/54552)
- \[[`747d9ae72e`](https://github.com/nodejs/node/commit/747d9ae72e)] - **test**: deflake test-http2-misbehaving-multiplex (Luigi Pinca) [#54872](https://github.com/nodejs/node/pull/54872)
- \[[`7b7687eadc`](https://github.com/nodejs/node/commit/7b7687eadc)] - **test**: remove dead code in test-http2-misbehaving-multiplex (Luigi Pinca) [#54860](https://github.com/nodejs/node/pull/54860)
- \[[`60f5f5426d`](https://github.com/nodejs/node/commit/60f5f5426d)] - **test**: reduce test-esm-loader-hooks-inspect-wait flakiness (Luigi Pinca) [#54827](https://github.com/nodejs/node/pull/54827)
- \[[`f5e77385c5`](https://github.com/nodejs/node/commit/f5e77385c5)] - **test**: reduce the allocation size in test-worker-arraybuffer-zerofill (James M Snell) [#54839](https://github.com/nodejs/node/pull/54839)
- \[[`f26cf09d6b`](https://github.com/nodejs/node/commit/f26cf09d6b)] - **test**: fix test-tls-client-mindhsize for OpenSSL32 (Michael Dawson) [#54739](https://github.com/nodejs/node/pull/54739)
- \[[`c6f9afec94`](https://github.com/nodejs/node/commit/c6f9afec94)] - **test**: use platform timeout (jakecastelli) [#54591](https://github.com/nodejs/node/pull/54591)
- \[[`8f49b7c3ee`](https://github.com/nodejs/node/commit/8f49b7c3ee)] - **test**: reduce fs calls in test-fs-existssync-false (Yagiz Nizipli) [#54815](https://github.com/nodejs/node/pull/54815)
- \[[`e2c69c9844`](https://github.com/nodejs/node/commit/e2c69c9844)] - **test**: move test-http-server-request-timeouts-mixed (James M Snell) [#54841](https://github.com/nodejs/node/pull/54841)
- \[[`f7af8ca021`](https://github.com/nodejs/node/commit/f7af8ca021)] - **test**: fix volatile for CauseSegfault with clang (Ivan Trubach) [#54325](https://github.com/nodejs/node/pull/54325)
- \[[`d1bae5ede5`](https://github.com/nodejs/node/commit/d1bae5ede5)] - **test**: set `test-worker-arraybuffer-zerofill` as flaky (Yagiz Nizipli) [#54802](https://github.com/nodejs/node/pull/54802)
- \[[`b5b5cc811f`](https://github.com/nodejs/node/commit/b5b5cc811f)] - **test**: set `test-http-server-request-timeouts-mixed` as flaky (Yagiz Nizipli) [#54802](https://github.com/nodejs/node/pull/54802)
- \[[`9808feecac`](https://github.com/nodejs/node/commit/9808feecac)] - **test**: set `test-single-executable-application-empty` as flaky (Yagiz Nizipli) [#54802](https://github.com/nodejs/node/pull/54802)
- \[[`97d41c62e3`](https://github.com/nodejs/node/commit/97d41c62e3)] - **test**: set `test-macos-app-sandbox` as flaky (Yagiz Nizipli) [#54802](https://github.com/nodejs/node/pull/54802)
- \[[`57ae68001c`](https://github.com/nodejs/node/commit/57ae68001c)] - **test**: set `test-fs-utimes` as flaky (Yagiz Nizipli) [#54802](https://github.com/nodejs/node/pull/54802)
- \[[`38afc4da03`](https://github.com/nodejs/node/commit/38afc4da03)] - **test**: set `test-runner-run-watch` as flaky (Yagiz Nizipli) [#54802](https://github.com/nodejs/node/pull/54802)
- \[[`68e19748a6`](https://github.com/nodejs/node/commit/68e19748a6)] - **test**: set `test-writewrap` as flaky (Yagiz Nizipli) [#54802](https://github.com/nodejs/node/pull/54802)
- \[[`e8cb03d530`](https://github.com/nodejs/node/commit/e8cb03d530)] - **test**: set `test-async-context-frame` as flaky (Yagiz Nizipli) [#54802](https://github.com/nodejs/node/pull/54802)
- \[[`3a56517220`](https://github.com/nodejs/node/commit/3a56517220)] - **test**: set `test-esm-loader-hooks-inspect-wait` as flaky (Yagiz Nizipli) [#54802](https://github.com/nodejs/node/pull/54802)
- \[[`c98cd1227d`](https://github.com/nodejs/node/commit/c98cd1227d)] - **test**: set `test-http2-large-file` as flaky (Yagiz Nizipli) [#54802](https://github.com/nodejs/node/pull/54802)
- \[[`16176a6323`](https://github.com/nodejs/node/commit/16176a6323)] - **test**: set `test-runner-watch-mode-complex` as flaky (Yagiz Nizipli) [#54802](https://github.com/nodejs/node/pull/54802)
- \[[`eed0537533`](https://github.com/nodejs/node/commit/eed0537533)] - **test**: set `test-performance-function` as flaky (Yagiz Nizipli) [#54802](https://github.com/nodejs/node/pull/54802)
- \[[`d0f208d2e9`](https://github.com/nodejs/node/commit/d0f208d2e9)] - **test**: set `test-debugger-heap-profiler` as flaky (Yagiz Nizipli) [#54802](https://github.com/nodejs/node/pull/54802)
- \[[`68891a6363`](https://github.com/nodejs/node/commit/68891a6363)] - **test**: fix `test-process-load-env-file` when path contains `'` (Antoine du Hamel) [#54511](https://github.com/nodejs/node/pull/54511)
- \[[`4f82673139`](https://github.com/nodejs/node/commit/4f82673139)] - **test**: refactor fs-watch tests due to macOS issue (Santiago Gimeno) [#54498](https://github.com/nodejs/node/pull/54498)
- \[[`3606c53fdc`](https://github.com/nodejs/node/commit/3606c53fdc)] - **test**: refactor `test-esm-type-field-errors` (Giovanni Bucci) [#54368](https://github.com/nodejs/node/pull/54368)
- \[[`99566aea97`](https://github.com/nodejs/node/commit/99566aea97)] - **test**: improve output of child process utilities (Joyee Cheung) [#54622](https://github.com/nodejs/node/pull/54622)
- \[[`ed2377c1a1`](https://github.com/nodejs/node/commit/ed2377c1a1)] - **test**: fix test-tls-client-auth test for OpenSSL32 (Michael Dawson) [#54610](https://github.com/nodejs/node/pull/54610)
- \[[`d2a7e45946`](https://github.com/nodejs/node/commit/d2a7e45946)] - **test**: update TLS test for OpenSSL 3.2 (Richard Lau) [#54612](https://github.com/nodejs/node/pull/54612)
- \[[`a50bbca78a`](https://github.com/nodejs/node/commit/a50bbca78a)] - **test**: increase key size for ca2-cert.pem (Michael Dawson) [#54599](https://github.com/nodejs/node/pull/54599)
- \[[`d7ac3262de`](https://github.com/nodejs/node/commit/d7ac3262de)] - **test**: update test-assert-typedarray-deepequal to use node:test (James M Snell) [#54585](https://github.com/nodejs/node/pull/54585)
- \[[`916a73cd8f`](https://github.com/nodejs/node/commit/916a73cd8f)] - **test**: update test-assert to use node:test (James M Snell) [#54585](https://github.com/nodejs/node/pull/54585)
- \[[`10bea1cef5`](https://github.com/nodejs/node/commit/10bea1cef5)] - **test**: merge ongc and gcutil into gc.js (tannal) [#54355](https://github.com/nodejs/node/pull/54355)
- \[[`f145982436`](https://github.com/nodejs/node/commit/f145982436)] - **test**: move a couple of tests over to using node:test (James M Snell) [#54582](https://github.com/nodejs/node/pull/54582)
- \[[`229e102d20`](https://github.com/nodejs/node/commit/229e102d20)] - **test**: fix embedding test for Windows (Vladimir Morozov) [#53659](https://github.com/nodejs/node/pull/53659)
- \[[`fcf82adef0`](https://github.com/nodejs/node/commit/fcf82adef0)] - **test**: use relative paths in test-cli-permission tests (sendoru) [#54188](https://github.com/nodejs/node/pull/54188)
- \[[`4c219b0235`](https://github.com/nodejs/node/commit/4c219b0235)] - **test**: fix timeout not being cleared (Isaac-yz-Liu) [#54242](https://github.com/nodejs/node/pull/54242)
- \[[`e446517a41`](https://github.com/nodejs/node/commit/e446517a41)] - **test**: refactor `test-runner-module-mocking` (Antoine du Hamel) [#54233](https://github.com/nodejs/node/pull/54233)
- \[[`782a6a05ef`](https://github.com/nodejs/node/commit/782a6a05ef)] - **test**: use assert.{s,deepS}trictEqual() (Luigi Pinca) [#54208](https://github.com/nodejs/node/pull/54208)
- \[[`d478db7adc`](https://github.com/nodejs/node/commit/d478db7adc)] - **test**: set test-structuredclone-jstransferable non-flaky (Stefan Stojanovic) [#54115](https://github.com/nodejs/node/pull/54115)
- \[[`c8587ec90d`](https://github.com/nodejs/node/commit/c8587ec90d)] - **test**: update wpt test for streams (devstone) [#54129](https://github.com/nodejs/node/pull/54129)
- \[[`dbc26c2971`](https://github.com/nodejs/node/commit/dbc26c2971)] - **test**: fix typo in test (Sonny) [#54137](https://github.com/nodejs/node/pull/54137)
- \[[`17b7ec4df3`](https://github.com/nodejs/node/commit/17b7ec4df3)] - **test**: add initial pull delay and prototype pollution prevention tests (Sonny) [#54061](https://github.com/nodejs/node/pull/54061)
- \[[`931ff4367a`](https://github.com/nodejs/node/commit/931ff4367a)] - **test**: update wpt test (Mert Can Altin) [#53814](https://github.com/nodejs/node/pull/53814)
- \[[`1c1bd7ce52`](https://github.com/nodejs/node/commit/1c1bd7ce52)] - **test**: update `url` web-platform tests (Yagiz Nizipli) [#53472](https://github.com/nodejs/node/pull/53472)
- \[[`b048eaea5c`](https://github.com/nodejs/node/commit/b048eaea5c)] - **test_runner**: reimplement `assert.ok` to allow stack parsing (Aviv Keller) [#54776](https://github.com/nodejs/node/pull/54776)
- \[[`c981e61155`](https://github.com/nodejs/node/commit/c981e61155)] - **test_runner**: improve code coverage cleanup (Colin Ihrig) [#54856](https://github.com/nodejs/node/pull/54856)
- \[[`4f421b37da`](https://github.com/nodejs/node/commit/4f421b37da)] - **test_runner**: use validateStringArray for `timers.enable()` (Deokjin Kim) [#49534](https://github.com/nodejs/node/pull/49534)
- \[[`27da75ae22`](https://github.com/nodejs/node/commit/27da75ae22)] - **test_runner**: do not expose internal loader (Antoine du Hamel) [#54106](https://github.com/nodejs/node/pull/54106)
- \[[`56cbc80d28`](https://github.com/nodejs/node/commit/56cbc80d28)] - **test_runner**: make mock_loader not confuse CJS and ESM resolution (Sung Ye In) [#53846](https://github.com/nodejs/node/pull/53846)
- \[[`8fd951f7c7`](https://github.com/nodejs/node/commit/8fd951f7c7)] - **test_runner**: remove outdated comment (Colin Ihrig) [#54146](https://github.com/nodejs/node/pull/54146)
- \[[`65b6fec3ba`](https://github.com/nodejs/node/commit/65b6fec3ba)] - **test_runner**: run after hooks even if test is aborted (Colin Ihrig) [#54151](https://github.com/nodejs/node/pull/54151)
- \[[`c0b4c8284c`](https://github.com/nodejs/node/commit/c0b4c8284c)] - **test_runner**: added colors to dot reporter (Giovanni) [#53450](https://github.com/nodejs/node/pull/53450)
- \[[`3000e5df91`](https://github.com/nodejs/node/commit/3000e5df91)] - **test_runner**: support module detection in module mocks (Geoffrey Booth) [#53642](https://github.com/nodejs/node/pull/53642)
- \[[`f789f4c92d`](https://github.com/nodejs/node/commit/f789f4c92d)] - **(SEMVER-MINOR)** **test_runner**: support module mocking (Colin Ihrig) [#52848](https://github.com/nodejs/node/pull/52848)
- \[[`82d1c36f51`](https://github.com/nodejs/node/commit/82d1c36f51)] - **test_runner**: display failed test stack trace with dot reporter (Mihir Bhansali) [#52655](https://github.com/nodejs/node/pull/52655)
- \[[`5358601e31`](https://github.com/nodejs/node/commit/5358601e31)] - **timers**: avoid generating holey internal arrays (Gürgün Dayıoğlu) [#54771](https://github.com/nodejs/node/pull/54771)
- \[[`b6ed97c66d`](https://github.com/nodejs/node/commit/b6ed97c66d)] - **timers**: document ref option for scheduler.wait (Paolo Insogna) [#54605](https://github.com/nodejs/node/pull/54605)
- \[[`f524b8a28b`](https://github.com/nodejs/node/commit/f524b8a28b)] - **timers**: fix validation (Paolo Insogna) [#54404](https://github.com/nodejs/node/pull/54404)
- \[[`bc020f7cb3`](https://github.com/nodejs/node/commit/bc020f7cb3)] - **(SEMVER-MINOR)** **tls**: add `allowPartialTrustChain` flag (Anna Henningsen) [#54790](https://github.com/nodejs/node/pull/54790)
- \[[`d0e6f9168e`](https://github.com/nodejs/node/commit/d0e6f9168e)] - **tls**: remove prototype primordials (Antoine du Hamel) [#53699](https://github.com/nodejs/node/pull/53699)
- \[[`f5c65d0be6`](https://github.com/nodejs/node/commit/f5c65d0be6)] - **tools**: add readability/fn_size to filter (Rafael Gonzaga) [#54744](https://github.com/nodejs/node/pull/54744)
- \[[`a47bb9b2c2`](https://github.com/nodejs/node/commit/a47bb9b2c2)] - **tools**: add util scripts to land and rebase PRs (Antoine du Hamel) [#54656](https://github.com/nodejs/node/pull/54656)
- \[[`fe3155cefa`](https://github.com/nodejs/node/commit/fe3155cefa)] - **tools**: remove readability/fn_size rule (Rafael Gonzaga) [#54663](https://github.com/nodejs/node/pull/54663)
- \[[`d6b9cc3acd`](https://github.com/nodejs/node/commit/d6b9cc3acd)] - **tools**: remove unused python files (Aviv Keller) [#53928](https://github.com/nodejs/node/pull/53928)
- \[[`b5fbe9609c`](https://github.com/nodejs/node/commit/b5fbe9609c)] - **tools**: remove header from c-ares license (Aviv Keller) [#54335](https://github.com/nodejs/node/pull/54335)
- \[[`a7fdc608c6`](https://github.com/nodejs/node/commit/a7fdc608c6)] - **tools**: add find pyenv path on windows (Marco Ippolito) [#54314](https://github.com/nodejs/node/pull/54314)
- \[[`f90688cd5b`](https://github.com/nodejs/node/commit/f90688cd5b)] - **tools**: make undici updater build wasm from src (Michael Dawson) [#54128](https://github.com/nodejs/node/pull/54128)
- \[[`a033dff2f2`](https://github.com/nodejs/node/commit/a033dff2f2)] - **tty**: initialize winSize array with values (Michaël Zasso) [#54281](https://github.com/nodejs/node/pull/54281)
- \[[`e635e0956c`](https://github.com/nodejs/node/commit/e635e0956c)] - **typings**: fix TypedArray to a global type (1ilsang) [#54063](https://github.com/nodejs/node/pull/54063)
- \[[`b5bf08f31e`](https://github.com/nodejs/node/commit/b5bf08f31e)] - **typings**: correct param type of `SafePromisePrototypeFinally` (Wuli) [#54727](https://github.com/nodejs/node/pull/54727)
- \[[`628ae4bde5`](https://github.com/nodejs/node/commit/628ae4bde5)] - **typings**: add util.styleText type definition (Rafael Gonzaga) [#54252](https://github.com/nodejs/node/pull/54252)
- \[[`cc37401ea5`](https://github.com/nodejs/node/commit/cc37401ea5)] - **typings**: add missing binding function `writeFileUtf8()` (Jungku Lee) [#54110](https://github.com/nodejs/node/pull/54110)
- \[[`728c3fd6f1`](https://github.com/nodejs/node/commit/728c3fd6f1)] - **url**: modify pathToFileURL to handle extended UNC path (Early Riser) [#54262](https://github.com/nodejs/node/pull/54262)
- \[[`b25563dfcb`](https://github.com/nodejs/node/commit/b25563dfcb)] - **url**: improve resolveObject with ObjectAssign (Early Riser) [#54092](https://github.com/nodejs/node/pull/54092)
- \[[`eededd1ca8`](https://github.com/nodejs/node/commit/eededd1ca8)] - **url**: make URL.parse enumerable (Filip Skokan) [#53720](https://github.com/nodejs/node/pull/53720)
- \[[`4eb0749b6c`](https://github.com/nodejs/node/commit/4eb0749b6c)] - **(SEMVER-MINOR)** **url**: implement parse method for safer URL parsing (Ali Hassan) [#52280](https://github.com/nodejs/node/pull/52280)
- \[[`9e1c2293bf`](https://github.com/nodejs/node/commit/9e1c2293bf)] - **vm**: harden module type checks (Chengzhong Wu) [#52162](https://github.com/nodejs/node/pull/52162)
- \[[`2d90340cb3`](https://github.com/nodejs/node/commit/2d90340cb3)] - **(SEMVER-MINOR)** **vm**: introduce vanilla contexts via vm.constants.DONT_CONTEXTIFY (Joyee Cheung) [#54394](https://github.com/nodejs/node/pull/54394)
- \[[`4644d05ab5`](https://github.com/nodejs/node/commit/4644d05ab5)] - **zlib**: deprecate instantiating classes without new (Yagiz Nizipli) [#54708](https://github.com/nodejs/node/pull/54708)
- \[[`ecdf6dd444`](https://github.com/nodejs/node/commit/ecdf6dd444)] - **zlib**: simplify validators (Yagiz Nizipli) [#54442](https://github.com/nodejs/node/pull/54442)

Windows 32-bit Installer: https://nodejs.org/dist/v20.18.0/node-v20.18.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v20.18.0/node-v20.18.0-x64.msi \
Windows ARM 64-bit Installer: https://nodejs.org/dist/v20.18.0/node-v20.18.0-arm64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v20.18.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v20.18.0/win-x64/node.exe \
Windows ARM 64-bit Binary: https://nodejs.org/dist/v20.18.0/win-arm64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v20.18.0/node-v20.18.0.pkg \
macOS Apple Silicon 64-bit Binary: https://nodejs.org/dist/v20.18.0/node-v20.18.0-darwin-arm64.tar.gz \
macOS Intel 64-bit Binary: https://nodejs.org/dist/v20.18.0/node-v20.18.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v20.18.0/node-v20.18.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v20.18.0/node-v20.18.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v20.18.0/node-v20.18.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v20.18.0/node-v20.18.0-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v20.18.0/node-v20.18.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v20.18.0/node-v20.18.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v20.18.0/node-v20.18.0.tar.gz \
Other release files: https://nodejs.org/dist/v20.18.0/ \
Documentation: https://nodejs.org/docs/v20.18.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

612c1cbb613f7af05f9de71767c3ba791b1972abe07fe37dbc721e837f2df9bc  node-v20.18.0-aix-ppc64.tar.gz
6a8615293b4920fdd232185810a77583ae5301350c0d025654452d1f1f655436  node-v20.18.0-arm64.msi
92e180624259d082562592bb12548037c6a417069be29e452ec5d158d657b4be  node-v20.18.0-darwin-arm64.tar.gz
678e062bdae3824aa997bd469580a4dda48fd51f61d3679b6ba06352e6cef38f  node-v20.18.0-darwin-arm64.tar.xz
c02aa7560612a4e2cc359fd89fae7aedde370c06db621f2040a4a9f830a125dc  node-v20.18.0-darwin-x64.tar.gz
63e150a3bb4f31743257d8597262c6b5f0a2356e7c42002e29d5f7d1bf161f08  node-v20.18.0-darwin-x64.tar.xz
fbae87a8bc00969c7e89e15e51392362e2c1e59a7eaba3e2ccba1601745e77a5  node-v20.18.0-headers.tar.gz
b9984e1fb92e30437cf41c1b9ae3361a87022aa2528f9b77d5e415d772d28d96  node-v20.18.0-headers.tar.xz
38bccb35c06ee4edbcd00c77976e3fad1d69d2e57c3c0c363d1700a2a2493278  node-v20.18.0-linux-arm64.tar.gz
a9ce85675ba33f00527f6234d90000946c0936fb4fca605f1891bb5f4fe6fb0a  node-v20.18.0-linux-arm64.tar.xz
9a522daa837d4d32dc700bf9b18dea9e21a229b113a22cfcf38f1f2240bbbc47  node-v20.18.0-linux-armv7l.tar.gz
964110a031c467a555aa889a6f2f13f7a1d5d4b834403aaa6d9148de050e1563  node-v20.18.0-linux-armv7l.tar.xz
627ead0c0dbb6db57db415dd440506764ecb7238a8b6ecb65720a43787ccfead  node-v20.18.0-linux-ppc64le.tar.gz
b3753b739c5a53694477f45b25970f9b3daf83c88844de9fb837c6593e99edf6  node-v20.18.0-linux-ppc64le.tar.xz
41d3e8dc9fc23e0ae1a313fac27f57fc3358c790af11632943566600fc2ae2d3  node-v20.18.0-linux-s390x.tar.gz
8a9a0142055f85b0f75a8e80195c8e413a40ccf926cb4b983accf01f33f83118  node-v20.18.0-linux-s390x.tar.xz
24a5d58a1d4c2903478f4b7c3cfd2eeb5cea2cae3baee11a4dc6a1fed25fec6c  node-v20.18.0-linux-x64.tar.gz
4543670b589593f8fa5f106111fd5139081da42bb165a9239f05195e405f240a  node-v20.18.0-linux-x64.tar.xz
f3abed6434fc5f378547516f118c9a3696099ebc670c3ea349bac122b3973770  node-v20.18.0.pkg
c0819f8fc5038584d24c22002aeffd23f2d4a6fd6b337b30c502cbe4a659720c  node-v20.18.0.tar.gz
7d9433e91fd88d82ba8de86e711ec41907638e227993d22e95126b02f6cd714a  node-v20.18.0.tar.xz
295682b03995d0887af37e2781da6a405dbac39b29c801a45b8bff19c7df3b9b  node-v20.18.0-win-arm64.7z
9da99126416e25fa0351c39daf984120e3a24ae8f3ad35a4f4f117ae52db7184  node-v20.18.0-win-arm64.zip
e990cb43a1bf130335b4ff913190e20b36e27f37fe7929ef1b6fc79c64cd78bc  node-v20.18.0-win-x64.7z
f5cea43414cc33024bbe5867f208d1c9c915d6a38e92abeee07ed9e563662297  node-v20.18.0-win-x64.zip
d2392f96262b392b3122a0271e8d98d8dda56e345a39a93096758fb47c1abfb3  node-v20.18.0-win-x86.7z
74335c6f67c68f33e751ece1a3c834fc17dba2a872a73ef3921ea575cb57d4fe  node-v20.18.0-win-x86.zip
93d1d30341d7d38b7a8f3ab0fa3be1f9e6436b90338b2bd8b8af4e80d00bd036  node-v20.18.0-x64.msi
9ae3aaeced931fbd874c6488cc4960611691baa9c11e0b84c0ed9226ccc2d0cb  node-v20.18.0-x86.msi
e66327f3d1de938059bedda660de2eff1a508b61d777ff247615a019eb153d46  win-arm64/node.exe
58795bcd44e8023ff443dedabf7f9af928732a51befc5324082aafe56e0f5eb0  win-arm64/node.lib
12d1614f57c02726066586f00b00cf570965ddcfaaa42e6418a26355a68de3fa  win-arm64/node_pdb.7z
0ed107a5ca32327a131c0c02b573fbba7c9549a8b16ea2828850cb87e15f97eb  win-arm64/node_pdb.zip
35b7c95a379beb606f5798ed83081690df13190077630b234163c6607aa4cc94  win-x64/node.exe
afeae4c806b82987ee073c9c75a0e5c5530213145c64f2824d0ed220a6acf962  win-x64/node.lib
7b5adad6a9278e1f507add8d2c49465053f92cef8aed43aa88f0131df817bb0d  win-x64/node_pdb.7z
b1887992ff66f841aaf89db8a26909355253f7a28e8245b5500436a0a4e41204  win-x64/node_pdb.zip
4364bfd5cc485ce2bf9b89878fddc09f4c7abdedc680cd5ea5065b0516754106  win-x86/node.exe
a2ccacb78f399dceb60f5ad275c9380c5cf498202ef1cb7f7bd789d844ab0daa  win-x86/node.lib
180103650f8efc5726826e6c07d0d775b6ae063d85778c600f040bb54d17129f  win-x86/node_pdb.7z
0a0cae066699d13891179fc167aab64eda1d477f241ed22b09233b05c56f87d8  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEj8yhP+8dDC6RAI4Jdw96mlrhVgAFAmb+2tQACgkQdw96mlrh
VgDD3hAAjUuhsYitdzrnesSmmMldEcIWRS+1Wrb6TmJLTsosgFnEvEboTqYtcASF
LCj6oAGauRVGhn49IWxJH6d0QtSIMkTe3wBQPQIridsR++RNCmoYy0tqVPQrU111
EymcSfISwnvC40xGmyGw1PQz33dD9IPwBlz9SnmvpQGYNxpb3EA9bjhbldQ1BbjU
xbV9mdC6KFYAvwYzAfyEfuf4PgkN/1Q9//p+nPSYIFrW0uMpxJQg47DhiSfFKlxA
WuifCYdNaMv7QE5QMgPElSH/4/yoo7owRxeMj77Dq33OzvY0mcLauWsItR2Hjjla
1KsvOdFMk6x0wb4Gwb6S245zbbFq+Ja5/7oWput9nmfxxbY16c24ieKKOlFTx74s
sOSml10O5PkpE60CXnakDJdrAr7zDvyvPgmMNyWs69hLdKkWUKnDlembuie2fmTA
QK/Tu/sjFTney8BIAIBmJSgXrTLNesppmaXQ2nvTpK2J2sSV+8DVMvzK0SqCXJzW
nBCV+z+beLo2YbDyQWfMgYwz6vLXir0pEHlD3VZrVR7gsWArYFbztCkf1h3V7njT
txr2//xDGfRvh92/UmuRhSnKoi59gEfTb6zIwifTUWQXOSPYkIKK3tMSDcn+ChYO
44tjDUK4sksFP6OAW8PVNTTtgobAXFdwWJ+OGq9WnPb/fpjysjk=
=xl8D
-----END PGP SIGNATURE-----
```
