---
date: '2012-07-20T18:37:15.000Z'
category: release
title: Version 0.9.0 (Unstable)
layout: blog-post
author: The Node.js Project
---

2012.07.20, Version 0.9.0 (Unstable)

- punycode: update to v1.1.1 (<PERSON>)

- c-ares: upgrade to 1.9.0 (<PERSON><PERSON>)

- dns: ignore rogue DNS servers reported by windows (<PERSON><PERSON>)

- unix: speed up uv_async_send() (<PERSON>)

- dar<PERSON>: get cpu model correctly on mac (Xidorn Quan)

- nextTick: Handle tick callbacks before any other I/O (isaacs)

- Enable color customization of `util.inspect` (<PERSON>)

- tls: Speed and memory improvements (<PERSON>or <PERSON>)

- readline: Use one history item for reentered line (Vladimir Beloborodov)

- Fix #3521 Make process.env more like a regular Object (isaacs)

Source Code: https://nodejs.org/dist/v0.9.0/node-v0.9.0.tar.gz

Macintosh Installer (Universal): https://nodejs.org/dist/v0.9.0/node-v0.9.0.pkg

Windows Installer: https://nodejs.org/dist/v0.9.0/node-v0.9.0-x86.msi

Windows x64 Installer: https://nodejs.org/dist/v0.9.0/x64/node-v0.9.0-x64.msi

Windows x64 Files: https://nodejs.org/dist/v0.9.0/x64/

Other release files: https://nodejs.org/dist/v0.9.0/

Website: https://nodejs.org/docs/v0.9.0/

Documentation: https://nodejs.org/docs/v0.9.0/api/

Shasums:

```
4d6881934f5e41da651b478f914f71543d21d3cc  node-v0.9.0-x86.msi
ec00cc6f0830f64cd9e8246a299abf9a2a6ed73e  node-v0.9.0.pkg
912d0eb3139b8f6f99199dae5ec1ecb300ed9c9b  node-v0.9.0.tar.gz
7e56dddbb1d3e243549db7182f1bf2dd4518eaae  node.exe
31718ad3e0de9b0ea6c207966a13d4bafaf9ef64  node.exp
0738b4d91de4c87cdee2547d83144668cb232c24  node.lib
baca86ec3c12a5261abb940cd8107bdeb40713cc  node.pdb
cf3892596a7d2a27a63672b537b06b8828125fe9  x64/node-v0.9.0-x64.msi
cad4bf9b2be85476d1a897ea17b3f927ec49c96a  x64/node.exe
29ea3654f7728efef6fa046943ded57af42b91f3  x64/node.exp
1dfa57b111d5942b4fe701b625176eae73c82d82  x64/node.lib
9093b2120f8adf0236f965982e4a32697af2af5d  x64/node.pdb
```
