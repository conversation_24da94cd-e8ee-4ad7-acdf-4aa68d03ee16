---
date: '2024-05-09T10:38:21.154Z'
category: release
title: Node v20.13.1 (LTS)
layout: blog-post
author: <PERSON>
---

## 2024-05-09, Version 20.13.1 'Iron' (LTS), @marco-ippolito

### Revert "tools: install npm PowerShell scripts on Windows"

Due to a regression in the npm installation on Windows, this commit reverts the change that installed npm PowerShell scripts on Windows.

### Commits

- \[[`b7d80802cc`](https://github.com/nodejs/node/commit/b7d80802cc)] - _**Revert**_ "**tools**: install npm PowerShell scripts on Windows" (marco-ippolito) [#52897](https://github.com/nodejs/node/pull/52897)

Windows 32-bit Installer: https://nodejs.org/dist/v20.13.1/node-v20.13.1-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v20.13.1/node-v20.13.1-x64.msi \
Windows ARM 64-bit Installer: https://nodejs.org/dist/v20.13.1/node-v20.13.1-arm64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v20.13.1/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v20.13.1/win-x64/node.exe \
Windows ARM 64-bit Binary: https://nodejs.org/dist/v20.13.1/win-arm64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v20.13.1/node-v20.13.1.pkg \
macOS Apple Silicon 64-bit Binary: https://nodejs.org/dist/v20.13.1/node-v20.13.1-darwin-arm64.tar.gz \
macOS Intel 64-bit Binary: https://nodejs.org/dist/v20.13.1/node-v20.13.1-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v20.13.1/node-v20.13.1-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v20.13.1/node-v20.13.1-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v20.13.1/node-v20.13.1-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v20.13.1/node-v20.13.1-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v20.13.1/node-v20.13.1-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v20.13.1/node-v20.13.1-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v20.13.1/node-v20.13.1.tar.gz \
Other release files: https://nodejs.org/dist/v20.13.1/ \
Documentation: https://nodejs.org/docs/v20.13.1/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

941d645cc804274934a06813c87b0b7cee7a6c3f5a3c82e62e1f6f8dc7f0030c  node-v20.13.1-aix-ppc64.tar.gz
adee910ad7603adfc198bbcf114fbe38635e305d29b0ea36435d8c1c2f2da275  node-v20.13.1-arm64.msi
c30fe595f59dcd2c5158da6bf8bc251ffc85ca37304afa89db150fb3c62c4507  node-v20.13.1-darwin-arm64.tar.gz
e8a8e78b91485bc95d20f2aa86201485593685c828ee609245ce21c5680d07ce  node-v20.13.1-darwin-arm64.tar.xz
80bde95dc976b84db5ca566738c07305087ae578f5f3b05191e0e6ff54aaeaf2  node-v20.13.1-darwin-x64.tar.gz
c83bffeb4eb793da6cb61a44c422b399048a73d7a9c5eb735d9c7f5b0e8659b6  node-v20.13.1-darwin-x64.tar.xz
f81607966d511276eec291231a2329e2b626d5ed73e8cb5a13616a6a320f60b2  node-v20.13.1-headers.tar.gz
3a9b24eb11175c71e50f1eea40156df622ddb7f93b9616f639b4e6cffb2ba1a9  node-v20.13.1-headers.tar.xz
4f9c2ffb116855d6fa4b6654e453f403e31b53284b81c789b73d1d2e20c6f710  node-v20.13.1-linux-arm64.tar.gz
d251cda3ee0a539d8aea4ea2327e98998cb23487569073902e35efb0526d574b  node-v20.13.1-linux-arm64.tar.xz
0f65dddc797ce7f76976e3720b7a1bca67d1465b113daf82878b66c541ae649f  node-v20.13.1-linux-armv7l.tar.gz
af16f17ce87a0d51d0003ea60437d6c323c6514d187d5c3bd52a887bf0b4adf4  node-v20.13.1-linux-armv7l.tar.xz
d964b4ebc4f6fb2255dfb259f2e2489318c712a3b12ec2f5a1cb36a9ca208180  node-v20.13.1-linux-ppc64le.tar.gz
05f85b05d2aa1c0a2e0e7531fa54664fe47622cb346d18983908f100de8805fb  node-v20.13.1-linux-ppc64le.tar.xz
efabff9fc928821325139b853c8edcd25a828b8f2fdc1e227cc282655424cf90  node-v20.13.1-linux-s390x.tar.gz
0bbe63d4ba09527e40ba59e606ea31fdc44f9552b9e726bf2817ac7fe986961e  node-v20.13.1-linux-s390x.tar.xz
80b978a9fe544b1892e73a4bf89e0b3792b1d459b621874efdc2ddd2270c03fe  node-v20.13.1-linux-x64.tar.gz
efc0f295dd878e510ab12ea36bbadc3db03c687ab30c07e86c7cdba7eed879a9  node-v20.13.1-linux-x64.tar.xz
e22c5087b64cb6e4e8ea43aa68147b0b12025f2d53fe8f8da826c874348a268c  node-v20.13.1.pkg
a85ee53aa0a5c2f5ca94fa414cdbceb91eb7d18a77fc498358512c14cc6c6991  node-v20.13.1.tar.gz
791786a09023241cb7e4f7d65ec90aa924bb39141ff7bb6d5a1dedf7def4b4e7  node-v20.13.1.tar.xz
acd05435cba201ed43dace61750d2ce65ef3fab0a0752cedd614085e93806992  node-v20.13.1-win-arm64.7z
d6d3019fb03c9918dfb4ba9dbdadb701b781a661283689f95053849c3afc70c8  node-v20.13.1-win-arm64.zip
a3aacbadf432c03b13428b55d33e5a6af238c30c340d97d47cc9ce0b57bfa3b8  node-v20.13.1-win-x64.7z
555a55c0c7441cb90e441115c81f610fca712dd5b192034d5eaafd7c29924425  node-v20.13.1-win-x64.zip
20df3d7b90876aff5a952d6613c629707b0d04e7d03e0a86ff2d1524f0906969  node-v20.13.1-win-x86.7z
d9e6a4c0b72d63ae2c6e71fab868392bc394238f2027f9603500eb2edf571495  node-v20.13.1-win-x86.zip
695eb534992f0d4aa10ab024aef596664493e19e0e1581c41eefe33050811c52  node-v20.13.1-x64.msi
7d14bdc418e77f74b75ae424047039e73e004d2ebf917f491e481d4cb1c4676a  node-v20.13.1-x86.msi
6e7ec3dc7b8874eec281e8ea1a44c9a1f1034784853aacba47531ebc4bed6ceb  win-arm64/node.exe
4935c9a662de9421bc69e3e8a2146f6dabed40a7fd486ca46e697df30378b89f  win-arm64/node.lib
8264792d2db82c78dcc2d52bcbbb37ce199f7b1730dfe6998bf6ef6d5c0d4a18  win-arm64/node_pdb.7z
204c4fd540f44ab4e52b80d2277c97092af8a67a38ee883abf1e44dc4fa86d34  win-arm64/node_pdb.zip
11dfd8204bf09a95574ae69c524792f47bc61c693a53cca8a0185f831130740f  win-x64/node.exe
bd5890fac4571d1cdfaca31d74ae791feef1edad3b70421969de2aa83f330797  win-x64/node.lib
a77be6d0f8142f9c01a0d3312289b949703f262759ce5bd1843fb2f2773c7eed  win-x64/node_pdb.7z
688602fd42def373fb4bf4f9f5ae2badd846b1faec6c77eea0bd0e1b5ed0b379  win-x64/node_pdb.zip
7d3f344bb628d331563723dbb5855a118ba26484c96f9d5cc007d1d2aa74f3f0  win-x86/node.exe
f82b299d4498b58e549a11cdcb5ce9efa2162f7efa20f527903f5ac5a0387ca0  win-x86/node.lib
df200297b0a6b860a7fae8ee6b0eea1e0ca83bae168dd51cc93372454353bdcd  win-x86/node_pdb.7z
f48664ea50da26bc2f42c157be2b89412d105f5124d5fcc847eb91c4f14cfd00  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEzGj1oxBv9EgyLkjtJ/XjjVsKIV8FAmY8pv0ACgkQJ/XjjVsK
IV+Bfw//ahWJYxt6xsIqCmOzKiXyPnSHJNutRp4pbGpXBVYwAEOCMcztOC4z/AOu
8K2WElhDHHf0/LwhCgNIcg5J3Gh3059EW1RAdwx9MuYkt8wFO4TCoK/mrdN1XqjP
lRXp45YD01O9igebgILHd70vSz3UG9msdhndp1FuHC3THEMiQ7FRJRJ9Mz9nA3TQ
UV3zvAXdrOTKqFWLkoFAFp5DSutdplkzUnQ9oW69Jlw0X0Wr2wOFyHOSVRtQwjBT
v2dg5/FoPZoEDWBhyIlARD5UXF2ruy93KucBUza8SjuHPocG1QQzOcfphUc5biPE
XFz77Gh1M9raxn/guQLfpj0scFwm7nvVo7QOGDlEjQkWyyoTHEe/GdU9fBquQRhX
HcFRYMucFv87cHR8Rem1Mb59oM/GA9k8frpFKgVa+4oT8Yp+6VQukHbiK5SGqU1D
GXJwqwYoIA7eeAllHA3Jh5WBeJxm9JlbV7fYEWIBK62E5Wayj0OYTBaag3uSYJt/
aPBJzYFFCT259Pe8Q5CfThajVuiCUOUa+Wwx+4Wd60oKF3aFlURZ7zckzjLgX4ne
yDwOW+5FXG/I8qCtQ6onCdlhir0/hiVI6dZ92GtXpQJ3kPYcUIAq5XoGZ7PhYwwJ
LDTd9SRji+EDqq9uk2z0hGZ5SbbcSyY8MWmMqCYeiD0AopWkLOI=
=d095
-----END PGP SIGNATURE-----
```
