---
date: '2019-08-15T13:15:32.128Z'
category: release
title: Node v12.8.1 (Current)
layout: blog-post
author: <PERSON><PERSON><PERSON>
---

### Notable changes

This is a security release.

Node.js, as well as many other implementations of HTTP/2, have been found
vulnerable to Denial of Service attacks.
See https://github.com/Netflix/security-bulletins/blob/master/advisories/third-party/2019-002.md
for more information.

Vulnerabilities fixed:

- **CVE-2019-9511 “Data Dribble”**: The attacker requests a large amount of data from a specified resource over multiple streams. They manipulate window size and stream priority to force the server to queue the data in 1-byte chunks. Depending on how efficiently this data is queued, this can consume excess CPU, memory, or both, potentially leading to a denial of service.
- **CVE-2019-9512 “Ping Flood”**: The attacker sends continual pings to an HTTP/2 peer, causing the peer to build an internal queue of responses. Depending on how efficiently this data is queued, this can consume excess CPU, memory, or both, potentially leading to a denial of service.
- **CVE-2019-9513 “Resource Loop”**: The attacker creates multiple request streams and continually shuffles the priority of the streams in a way that causes substantial churn to the priority tree. This can consume excess CPU, potentially leading to a denial of service.
- **CVE-2019-9514 “Reset Flood”**: The attacker opens a number of streams and sends an invalid request over each stream that should solicit a stream of RST_STREAM frames from the peer. Depending on how the peer queues the RST_STREAM frames, this can consume excess memory, CPU, or both, potentially leading to a denial of service.
- **CVE-2019-9515 “Settings Flood”**: The attacker sends a stream of SETTINGS frames to the peer. Since the RFC requires that the peer reply with one acknowledgement per SETTINGS frame, an empty SETTINGS frame is almost equivalent in behavior to a ping. Depending on how efficiently this data is queued, this can consume excess CPU, memory, or both, potentially leading to a denial of service.
- **CVE-2019-9516 “0-Length Headers Leak”**: The attacker sends a stream of headers with a 0-length header name and 0-length header value, optionally Huffman encoded into 1-byte or greater headers. Some implementations allocate memory for these headers and keep the allocation alive until the session dies. This can consume excess memory, potentially leading to a denial of service.
- **CVE-2019-9517 “Internal Data Buffering”**: The attacker opens the HTTP/2 window so the peer can send without constraint; however, they leave the TCP window closed so the peer cannot actually write (many of) the bytes on the wire. The attacker then sends a stream of requests for a large response object. Depending on how the servers queue the responses, this can consume excess memory, CPU, or both, potentially leading to a denial of service.
- **CVE-2019-9518 “Empty Frames Flood”**: The attacker sends a stream of frames with an empty payload and without the end-of-stream flag. These frames can be DATA, HEADERS, CONTINUATION and/or PUSH_PROMISE. The peer spends time processing each frame disproportionate to attack bandwidth. This can consume excess CPU, potentially leading to a denial of service. (Discovered by Piotr Sikora of Google)

### Commits

- [[`bfeb5fc07f`](https://github.com/nodejs/node/commit/bfeb5fc07f)] - **deps**: update nghttp2 to 1.39.2 (Anna Henningsen) [#29122](https://github.com/nodejs/node/pull/29122)
- [[`08021fac59`](https://github.com/nodejs/node/commit/08021fac59)] - **http2**: allow security revert for Ping/Settings Flood (Anna Henningsen) [#29122](https://github.com/nodejs/node/pull/29122)
- [[`bbb4769cc1`](https://github.com/nodejs/node/commit/bbb4769cc1)] - **http2**: pause input processing if sending output (Anna Henningsen) [#29122](https://github.com/nodejs/node/pull/29122)
- [[`f64515b05e`](https://github.com/nodejs/node/commit/f64515b05e)] - **http2**: stop reading from socket if writes are in progress (Anna Henningsen) [#29122](https://github.com/nodejs/node/pull/29122)
- [[`ba332df5d2`](https://github.com/nodejs/node/commit/ba332df5d2)] - **http2**: consider 0-length non-end DATA frames an error (Anna Henningsen) [#29122](https://github.com/nodejs/node/pull/29122)
- [[`23b0db58ca`](https://github.com/nodejs/node/commit/23b0db58ca)] - **http2**: shrink default `vector::reserve()` allocations (Anna Henningsen) [#29122](https://github.com/nodejs/node/pull/29122)
- [[`4f10ac3623`](https://github.com/nodejs/node/commit/4f10ac3623)] - **http2**: handle 0-length headers better (Anna Henningsen) [#29122](https://github.com/nodejs/node/pull/29122)
- [[`a21a1c007b`](https://github.com/nodejs/node/commit/a21a1c007b)] - **http2**: limit number of invalid incoming frames (Anna Henningsen) [#29122](https://github.com/nodejs/node/pull/29122)
- [[`4570ed10d7`](https://github.com/nodejs/node/commit/4570ed10d7)] - **http2**: limit number of rejected stream openings (Anna Henningsen) [#29122](https://github.com/nodejs/node/pull/29122)
- [[`88726f2384`](https://github.com/nodejs/node/commit/88726f2384)] - **http2**: do not create ArrayBuffers when no DATA received (Anna Henningsen) [#29122](https://github.com/nodejs/node/pull/29122)
- [[`530004ef32`](https://github.com/nodejs/node/commit/530004ef32)] - **http2**: only call into JS when necessary for session events (Anna Henningsen) [#29122](https://github.com/nodejs/node/pull/29122)
- [[`58d8c9ef48`](https://github.com/nodejs/node/commit/58d8c9ef48)] - **http2**: improve JS-side debug logging (Anna Henningsen) [#29122](https://github.com/nodejs/node/pull/29122)

Windows 32-bit Installer: https://nodejs.org/dist/v12.8.1/node-v12.8.1-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v12.8.1/node-v12.8.1-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v12.8.1/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v12.8.1/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v12.8.1/node-v12.8.1.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v12.8.1/node-v12.8.1-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v12.8.1/node-v12.8.1-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v12.8.1/node-v12.8.1-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v12.8.1/node-v12.8.1-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v12.8.1/node-v12.8.1-aix-ppc64.tar.gz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v12.8.1/node-v12.8.1-sunos-x64.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v12.8.1/node-v12.8.1-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v12.8.1/node-v12.8.1-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v12.8.1/node-v12.8.1.tar.gz \
Other release files: https://nodejs.org/dist/v12.8.1/ \
Documentation: https://nodejs.org/docs/v12.8.1/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

a140e9c84fdb5c40328dc1ea6425954e08cec722ab4e1ab4521b0523bd7b2fa0  node-v12.8.1-aix-ppc64.tar.gz
caccf8b409af342e35672cc766430587664f88d01dab622a5de44c8be1336e44  node-v12.8.1-darwin-x64.tar.gz
c4789916ef5fcb61f8d6b10e15f9137d043ee5dbd004638c88ea4dde2841b2c2  node-v12.8.1-darwin-x64.tar.xz
06c011a5601fca1107d4a29428a051416dffb592391b47e23d6150abc98e4fef  node-v12.8.1-headers.tar.gz
b39eb6675c88b62c7c0d5103d7098f98b43b841a2d0065692760c9513b2bb6c4  node-v12.8.1-headers.tar.xz
3ad6e53bcf8a3b92b10504fa70d83f69eab85a8603c2fbe210121278dd19920b  node-v12.8.1-linux-arm64.tar.gz
3707240c402aae0b5879ce1ba17b32ebd224d47f5147639c6881a1b71ce65383  node-v12.8.1-linux-arm64.tar.xz
44e2f0dda0ae2ebabecfefe9a33995939d8e362a5bb3fcc579df98c107db2272  node-v12.8.1-linux-armv7l.tar.gz
cbe09af1f41de4cdc917a8874a1cda6bf877495dee159752d3f8745cf6a1e9a3  node-v12.8.1-linux-armv7l.tar.xz
1ddabc01c81dc8056daf831db9815439aa2dea9f93e653c6a185ec859836cf93  node-v12.8.1-linux-ppc64le.tar.gz
2002c80e3cd8c3c4816bdb96e9fc496510a5d2edf273a5cda91625a48a87dedd  node-v12.8.1-linux-ppc64le.tar.xz
a4b73e4c847ebc33d05d7f0d2efb7720ebbb643d17f3848ff3fe4266cc32f0ff  node-v12.8.1-linux-s390x.tar.gz
f8712268de942b2c05a58eb7291b8ed7cdd9b6413f82bc1aa99e23fae42375f9  node-v12.8.1-linux-s390x.tar.xz
8758a131cac2e8a373153b9eb53696d2e823b910dbf175af7ec4a0592b065c22  node-v12.8.1-linux-x64.tar.gz
1c7cf61cb8fa98a76d92445823d6dd1649e985899e16900b39eacdd8cd4094f3  node-v12.8.1-linux-x64.tar.xz
79c92434d5e9cf7b77122b679aa4b61e12dffa9feb1caee0134446ff8b7ecf05  node-v12.8.1.pkg
9e6fbd7d4346ddb0e86e14dbeff5f1cb23a69e09096dde6c0ad2785dc5643742  node-v12.8.1-sunos-x64.tar.gz
9f0ac2d6f98efeb54bb3a6f52ea58e545bc542b354061f79c1f2804e52a406a0  node-v12.8.1-sunos-x64.tar.xz
fbd4de6657f6301cdcd0c60a0e5a413c490670d8c21b501c380b96cc3b9b5b95  node-v12.8.1.tar.gz
3e63b2e7979b51d79b6f2f89d2869de85222be8edc549f08e10dc8d039de5b88  node-v12.8.1.tar.xz
52ece730f0d4d80657eb8d5c62120c2a91f435b8ef3bea672e1bfdaf9f7eae70  node-v12.8.1-win-x64.7z
c81ecaa03dc882dde36087c854e4831c13d3eb237f262f9ce74ccc6f156684da  node-v12.8.1-win-x64.zip
73e50beac826d2a68ad7bfa9c69d02881651a84d8284b609eb6de1f816422ce1  node-v12.8.1-win-x86.7z
71257bcb20caeb22320f4316549424329af888d4fb49f9beb6ea660a85c5848b  node-v12.8.1-win-x86.zip
cf7766ff0ffb30a6b1d88832b059c151cd84a43b9e80891bde0b456a730034ed  node-v12.8.1-x64.msi
b861512e89b07266b90422c5a48ba5a718f5377e0a3fe2e64e13ac69b87f6d29  node-v12.8.1-x86.msi
f54c49a9652b59f10b7961f6b3891636946b68e404e69d83e32762135c1fa862  win-x64/node.exe
508026e08ee3d579c17f766167c5c69ce31280a4a5607ac853099cd2b84e0426  win-x64/node.lib
dd9308e9161a5ff3bc549cf017d498d6bb05c7a9940c9e6d4d34f95cbce55e2d  win-x64/node_pdb.7z
46240f4ddb0d8ce81bff744b14376b702f37c2b43022602b002464622b7e8395  win-x64/node_pdb.zip
bf42f24a980db702b51939fdcfdf38a95580b00dd57a78d3d9d9c8bc558ab8c9  win-x86/node.exe
26e54309d4e8a8ac8f10d460962c474f1ae3f83ffa1bf420ec773b53db36a90a  win-x86/node.lib
297a842f38c8bb2bb1082ca763c40ca9ab104bcab3d57ea85fa62bb6682f2f43  win-x86/node_pdb.7z
07e4a6cdc87b1e5bbd2970ef7b35412c50393d9a74199f7babe6c432cc7db299  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEj8yhP+8dDC6RAI4Jdw96mlrhVgAFAl1VWqEACgkQdw96mlrh
VgB40w//Z/qkA9OpKmcu8VE4g2tWIU4utQTh2HR5ueAyy/LjJQ2f2mcC5Xar5WCq
w8K5q9loPkdxdQlteb55/p4VGEGAam5tWhy3juS5HbboiOlt5QNfswe9JQfcQGvu
UdPSukHoY+DLHeiaAdhf1bFhWTnCNuXRZdrWqukTjQhApu3JkLMa+gwex1TrEb7R
4mlpDtEXi1sTudOgBD71VGo3t7//nD6oV5NegH5AJZwXaGeaeWVhW9jscyy9GLLq
e0mzbZTUQnECw7fUujsGL0UbRRwUiVgeoBCV5WwatF/ofVXJ+VU0z3L18the+z27
DidOXQQHU/9EFkkXjGAmob2iS7imZMPHTiEhCCsfyHT7ldVFJS2UXdfetkfcMauO
D1jt0OVLRaJ78zYCO2Ct6CWDIqbYERvjuvIkWk3wLGvWZCMxkVnsNQH4Yrr9cYKq
P5PU8wGZyueOWj4TN9JAzj9h6BIrPmEJMgJHFiIDQnP84mq+tSqQSNwTiFUW6jZN
1WHg8zdq4kEJuYusKnZF13Z5FCnisXVXDThO9p7dwrJ1xta2t6LWmWF7uTwiate5
AXGnXz+zoZiGll831QOuIMCMZZyZPh6JHZOMO6AGj+58d4OQcVMyLVH2bsRQhK7B
Q8g229JTL7XjOvd5MwKuvoZAi+HG35hqPstICTu/ZsSZEiWgV2c=
=9JPr
-----END PGP SIGNATURE-----

```
