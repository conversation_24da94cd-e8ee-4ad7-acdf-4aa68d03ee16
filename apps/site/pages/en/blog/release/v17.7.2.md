---
date: '2022-03-18T01:13:10.035Z'
category: release
title: Node v17.7.2 (Current)
layout: blog-post
author: <PERSON>
---

### Notable Changes

Update to OpenSSL 3.0.2, which addresses the following vulnerability:

- Infinite loop in `BN_mod_sqrt()` reachable when parsing certificates (High)(CVE-2022-0778)
  More details are available at <https://www.openssl.org/news/secadv/20220315.txt>

### Commits

- \[[`55e293e05f`](https://github.com/nodejs/node/commit/55e293e05f)] - **deps**: update archs files for quictls/openssl-3.0.2+quic (<PERSON><PERSON><PERSON>) [#42356](https://github.com/nodejs/node/pull/42356)
- \[[`b8d090603d`](https://github.com/nodejs/node/commit/b8d090603d)] - **deps**: upgrade openssl sources to quictls/openssl-3.0.2+quic (<PERSON><PERSON><PERSON>) [#42356](https://github.com/nodejs/node/pull/42356)
- \[[`c8b6d92af0`](https://github.com/nodejs/node/commit/c8b6d92af0)] - **test**: fix tests affected by OpenSSL update (Michael Dawson) [#42356](https://github.com/nodejs/node/pull/42356)
- \[[`457e31ea09`](https://github.com/nodejs/node/commit/457e31ea09)] - **test**: renew certificates for specific test (Luigi Pinca) [#42342](https://github.com/nodejs/node/pull/42342)

Windows 32-bit Installer: https://nodejs.org/dist/v17.7.2/node-v17.7.2-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v17.7.2/node-v17.7.2-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v17.7.2/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v17.7.2/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v17.7.2/node-v17.7.2.pkg \
macOS Apple Silicon 64-bit Binary: https://nodejs.org/dist/v17.7.2/node-v17.7.2-darwin-arm64.tar.gz \
macOS Intel 64-bit Binary: https://nodejs.org/dist/v17.7.2/node-v17.7.2-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v17.7.2/node-v17.7.2-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v17.7.2/node-v17.7.2-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v17.7.2/node-v17.7.2-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v17.7.2/node-v17.7.2-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v17.7.2/node-v17.7.2-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v17.7.2/node-v17.7.2-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v17.7.2/node-v17.7.2.tar.gz \
Other release files: https://nodejs.org/dist/v17.7.2/ \
Documentation: https://nodejs.org/docs/v17.7.2/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

a70a82872ce0c37c47356fbb216b434aed578a730ca6dd359f2f21f74a2b8c20  node-v17.7.2-aix-ppc64.tar.gz
9b078739799239adb3a0aea051e3cf60be886f28bb39d34d75780297dddd7af1  node-v17.7.2-darwin-arm64.tar.gz
0cb7399bcd108c5d77f625f0951a3b18bc844383a5238685f8e3951066bd2b60  node-v17.7.2-darwin-arm64.tar.xz
57be6ba9e505c6b4b3b59c2878a1679fa11c1160773ec2d082cff74ed79e5ea1  node-v17.7.2-darwin-x64.tar.gz
601af5ad60d0cae815ed2df9a875b06f6d62d537bf1c74b5273385c90865c0aa  node-v17.7.2-darwin-x64.tar.xz
4e5a713c8e55ae2eae4ce2e877ad52f4cd1fe96952e1b206b141633432d886de  node-v17.7.2-headers.tar.gz
b331291a98b965a2f8c055d1b5c1702b34a4f264970dc159d3bf65700d73b125  node-v17.7.2-headers.tar.xz
577d89823ecfe5fc2008b1f2d3c8677c27385f1969ca0d1b8ba7a371a770ce54  node-v17.7.2-linux-arm64.tar.gz
8205f326850a33f581aba96a0028dfca0695f075f8d9889a098b2350168f2304  node-v17.7.2-linux-arm64.tar.xz
e722b09201fb2254123dddbdf93a1665ffeaaaa8d5da1bbd6e16d024fa663546  node-v17.7.2-linux-armv7l.tar.gz
2567faa68a3c9eb2e4f5efd8cdb4c42792ff2df82b45366904bcd5c38ab7706a  node-v17.7.2-linux-armv7l.tar.xz
2d1fdfb967dbb0395c7723ff62324f76dd06bcb2ecf2757cd7ec3875eceae029  node-v17.7.2-linux-ppc64le.tar.gz
7ba69a78bea5407d028b68adbc948a0c43d83732766f6625817a286691119e9a  node-v17.7.2-linux-ppc64le.tar.xz
b21ea41f26b7750ab5d3045b1d0c33d9133526d37fea0353163c9f4f663395b2  node-v17.7.2-linux-s390x.tar.gz
d287013b6d4d807750dfec3c9f2e8ca8cf24de4830238d796b28a3497d2d0382  node-v17.7.2-linux-s390x.tar.xz
7865d88b7a07ec407ceb9a3a9aa92a1c5a07469885834a5ee56661de369a9e40  node-v17.7.2-linux-x64.tar.gz
e706d33e40f3f7db08e07f7e707cbb5035831288eb161774d22a240a396926f7  node-v17.7.2-linux-x64.tar.xz
195d10bd7c1b4f034dbe7ef3f1b0413939b6a7e1bfb2739e6e7ab02bbd84e6d5  node-v17.7.2.pkg
50e4cf1c1afbf75076587dd818ed80a99308e009ada52e1a024d109c41599838  node-v17.7.2.tar.gz
3ae5e74e0b16228cf7edf6885359a9d2e019ac843906c5e01948d746faba4aaf  node-v17.7.2.tar.xz
b6b7afc9a998d4960767622674df89c56ce52e8966ee7852abefa3f2a637884d  node-v17.7.2-win-x64.7z
65128536b0b2a957c9e75d75a1b7272ecc0e0a2d99a4f7f7835fe57fa609cabd  node-v17.7.2-win-x64.zip
88a4747577d06f52b4fe0e92a674a1844abd0a62f06cd97c0a95e731ae66f5c9  node-v17.7.2-win-x86.7z
5bb50f78dec52b5b462cb3129c2688a14c78ba339aca5ad8db1c5588fdf79baa  node-v17.7.2-win-x86.zip
1db0ea1cce473fe4d2908aca305e2ee8e746420aafe1fd58e4dc47e0b9443e17  node-v17.7.2-x64.msi
156d937aa6de512e1beb2357474e4a5f7f6999d936ae5fb1f75a4b45740e8605  node-v17.7.2-x86.msi
f6b3f78ea23e1cbcf169907e09b7250881f3deb7289e7072eeb11844f5342ed8  win-x64/node.exe
998cc6721961278e2baa41598ea68ab7c04e8ab032bd3b36ed94e1d60d462c42  win-x64/node.lib
33dc4dd38691b3b57df4b506ed8f23d70a857429dc5809aaf8b62d39a39dac1c  win-x64/node_pdb.7z
fa4a9b05931e88c4ceba7f07a43232a48b33019c4c13268d19f8cb11db2f1538  win-x64/node_pdb.zip
2f5ebf12c976c3279bde2a4f4f4ea470d45033165b72f2cee8dfd989f2be8178  win-x86/node.exe
4dd1b950bfa0bf82923b12fa5b67912aa4d931e774593755aeb7ec1191d6579b  win-x86/node.lib
b3179c7d964a442490a6ffaf6f4fbb81f8543f090fd42196ce00a290ae393769  win-x86/node_pdb.7z
88bb5b1130e6ccde1887a967e0285a1bac3015c48f213cb6b36e9acce676390c  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEyC+jrhy+3Gvka5NgxDzsRcF6uTwFAmIz2tgACgkQxDzsRcF6
uTxlKw//XMb3kuCA08D+QF4uHRm6ZpcjErCSl2NRFdauARCqbk5F7O1HxAQQr6yc
0RTNIVV9J5UC7zgn6zbgudE+oWAGptQeZy3+5QiS15OadFLWqC+h1tZZmWjkKx4t
J/tFW6r8HLHPPf6s9nv3uq92f8EzYaPF9PsvUp5EUbFQjgVN8ZpAgm2LJMAmoXSt
ygtb+UpuCo5W0zqS6kVrjPtqpEc6qxszl1B/MP4pLgu8HakwnGHH5OUCzcnYxcJx
BKbj3FJT2HPmm8K3drm0hDpDioMrQ0zl9q/FcznotclKXID8ExSd6V3Y5KJaFxYN
dc5rxFb3P2cX6wbKjA/OnkMV4z9M4ujBXk1U+nFCS079HQXkd6+13bH90mHb8r8d
qKBSb0lzoPOl9qcH9zSaDRYzVkLoltdxOHyXPhNA8m8TIrZYI9ttpMlo9TJNK2Uq
apT0fsfbykqPxIZw9TzIc2KU3PumGV2E2HWgjR0ewMHFdy4YZGfjUDnmrFKgpgEU
gM5yNs7oeG0wmiBR4hlt7lAyHqqZ82NFfxmfLcszbnMnoY1x0SBgRD9Q0KRXxtcF
c6JPXyizPsebudLJZyyzWbnOBqWn8gIAP2iIdqiTOjoXze4pwsA6xCnjW6mgBzNp
HV3oxvfD8pfgqKWWFrQtMX3qGLNR49ZmZB2pmTL+8CB7NQTcsJA=
=B2pR
-----END PGP SIGNATURE-----

```
