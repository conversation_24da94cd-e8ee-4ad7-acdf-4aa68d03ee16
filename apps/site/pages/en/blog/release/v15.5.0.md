---
date: '2020-12-22T18:59:40.303Z'
category: release
title: Node v15.5.0 (Current)
layout: blog-post
author: <PERSON><PERSON><PERSON>
---

### Notable Changes

#### OpenSSL-1.1.1i

OpenSSL-1.1.1i contains a fix for CVE-2020-1971: OpenSSL - EDIPARTYNAME NULL pointer de-reference (High). This is a vulnerability in OpenSSL which may be exploited through Node.js. You can read more about it in https://www.openssl.org/news/secadv/20201208.txt

Contributed by <PERSON><PERSON> [#36520](https://github.com/nodejs/node/pull/36520).

#### Extended support for `AbortSignal` in child_process and stream

The following APIs now support an `AbortSignal` in their options object:

- `child_process.spawn()`

Calling `.abort()` on the corresponding `AbortController` is similar to calling `.kill()` on the child process except the error passed to the callback will be an `AbortError`:

```js
const controller = new AbortController();
const { signal } = controller;
const grep = spawn('grep', ['ssh'], { signal });
grep.on('error', err => {
  // This will be called with err being an AbortError if the controller aborts
});
controller.abort(); // stops the process
```

- `new stream.Writable()` and `new stream.Readable()`

Calling `.abort()` on the corresponding `AbortController` will behave the same way as calling `.destroy(new AbortError())` on the stream:

```js
const { Readable } = require('stream');
const controller = new AbortController();
const read = new Readable({
  read(size) {
    // ...
  },
  signal: controller.signal,
});
// Later, abort the operation closing the stream
controller.abort();
```

Contributed by Benjamin Gruenbaum [#36431](https://github.com/nodejs/node/pull/36431), [#36432](https://github.com/nodejs/node/pull/36432).

#### BigInt support in `querystring.stringify()`

If `querystring.stringify()` is called with an object that contains `BigInt` values, they will now be serialized to their decimal representation instead of the empty string:

```js
const querystring = require('querystring');
console.log(querystring.stringify({ bigint: 2n ** 64n }));
// Prints: bigint=18446744073709551616
```

Contributed by Darshan Sen [#36499](https://github.com/nodejs/node/pull/36499).

#### Additions to the C++ embedder APIs

A new `IsolateSettingsFlag` is available for those calling `SetIsolateUpForNode()`: `SHOULD_NOT_SET_PREPARE_STACK_TRACE_CALLBACK` can be used to prevent Node.js from setting a custom callback to prepare stack traces.

Contributed by Shelley Vohr [#36447](https://github.com/nodejs/node/pull/36447).

---

Added `node::GetEnvironmentIsolateData()` and `node::GetArrayBufferAllocator()` to respectively get the current `IsolateData*` and, from it, the current Node.js `ArrayBufferAllocator` if there is one.

Contributed by Anna Henningsen [#36441](https://github.com/nodejs/node/pull/36441).

#### New core collaborator

With this release, we welcome a new Node.js core collaborator:

- Pooja D P [@PoojaDurgad](https://github.com/PoojaDurgad) [#36511](https://github.com/nodejs/node/pull/36511)

### Commits

#### Semver-minor commits

- [[`e449571230`](https://github.com/nodejs/node/commit/e449571230)] - **(SEMVER-MINOR)** **child_process**: add signal support to spawn (Benjamin Gruenbaum) [#36432](https://github.com/nodejs/node/pull/36432)
- [[`25d7e90386`](https://github.com/nodejs/node/commit/25d7e90386)] - **(SEMVER-MINOR)** **http**: use `autoDestroy: true` in incoming message (Daniele Belardi) [#33035](https://github.com/nodejs/node/pull/33035)
- [[`5481be8cbd`](https://github.com/nodejs/node/commit/5481be8cbd)] - **(SEMVER-MINOR)** **lib**: support BigInt in querystring.stringify (raisinten) [#36499](https://github.com/nodejs/node/pull/36499)
- [[`036ed1fafc`](https://github.com/nodejs/node/commit/036ed1fafc)] - **(SEMVER-MINOR)** **src**: add way to get IsolateData and allocator from Environment (Anna Henningsen) [#36441](https://github.com/nodejs/node/pull/36441)
- [[`e23309486b`](https://github.com/nodejs/node/commit/e23309486b)] - **(SEMVER-MINOR)** **src**: allow preventing SetPrepareStackTraceCallback (Shelley Vohr) [#36447](https://github.com/nodejs/node/pull/36447)
- [[`6ecbc1dcb3`](https://github.com/nodejs/node/commit/6ecbc1dcb3)] - **(SEMVER-MINOR)** **stream**: support abortsignal in constructor (Benjamin Gruenbaum) [#36431](https://github.com/nodejs/node/pull/36431)

#### Semver-patch commits

- [[`1330995b80`](https://github.com/nodejs/node/commit/1330995b80)] - **build,lib,test**: change whitelist to allowlist (Michaël Zasso) [#36406](https://github.com/nodejs/node/pull/36406)
- [[`dc8d1a74a6`](https://github.com/nodejs/node/commit/dc8d1a74a6)] - **deps**: upgrade npm to 7.3.0 (Ruy Adorno) [#36572](https://github.com/nodejs/node/pull/36572)
- [[`b6a31f0a70`](https://github.com/nodejs/node/commit/b6a31f0a70)] - **deps**: update archs files for OpenSSL-1.1.1i (Myles Borins) [#36520](https://github.com/nodejs/node/pull/36520)
- [[`5b49807c3f`](https://github.com/nodejs/node/commit/5b49807c3f)] - **deps**: re-enable OPENSSL_NO_QUIC guards (James M Snell) [#36520](https://github.com/nodejs/node/pull/36520)
- [[`309e2971a2`](https://github.com/nodejs/node/commit/309e2971a2)] - **deps**: various quic patches from akamai/openssl (Todd Short) [#36520](https://github.com/nodejs/node/pull/36520)
- [[`27fb651cbc`](https://github.com/nodejs/node/commit/27fb651cbc)] - **deps**: upgrade openssl sources to 1.1.1i (Myles Borins) [#36520](https://github.com/nodejs/node/pull/36520)
- [[`1f43aadf90`](https://github.com/nodejs/node/commit/1f43aadf90)] - **deps**: update patch and docs for openssl update (Myles Borins) [#36520](https://github.com/nodejs/node/pull/36520)
- [[`752c94d202`](https://github.com/nodejs/node/commit/752c94d202)] - **deps**: fix npm doctor tests for pre-release node (nlf) [#36543](https://github.com/nodejs/node/pull/36543)
- [[`b0393fa2ed`](https://github.com/nodejs/node/commit/b0393fa2ed)] - **deps**: upgrade npm to 7.2.0 (Myles Borins) [#36543](https://github.com/nodejs/node/pull/36543)
- [[`cb4652e91d`](https://github.com/nodejs/node/commit/cb4652e91d)] - **deps**: update to c-ares 1.17.1 (Danny Sonnenschein) [#36207](https://github.com/nodejs/node/pull/36207)
- [[`21fbcb6f81`](https://github.com/nodejs/node/commit/21fbcb6f81)] - **deps**: V8: backport 4bf051d536a1 (Anna Henningsen) [#36482](https://github.com/nodejs/node/pull/36482)
- [[`30fe0ff681`](https://github.com/nodejs/node/commit/30fe0ff681)] - **deps**: upgrade npm to 7.1.2 (Darcy Clarke) [#36487](https://github.com/nodejs/node/pull/36487)
- [[`0baa610c3e`](https://github.com/nodejs/node/commit/0baa610c3e)] - **deps**: upgrade npm to 7.1.1 (Ruy Adorno) [#36459](https://github.com/nodejs/node/pull/36459)
- [[`5929b08851`](https://github.com/nodejs/node/commit/5929b08851)] - **deps**: upgrade npm to 7.1.0 (Ruy Adorno) [#36395](https://github.com/nodejs/node/pull/36395)
- [[`deaafd5788`](https://github.com/nodejs/node/commit/deaafd5788)] - **dns**: refactor to use more primordials (Antoine du Hamel) [#36314](https://github.com/nodejs/node/pull/36314)
- [[`e30af7be33`](https://github.com/nodejs/node/commit/e30af7be33)] - **fs**: refactor to use optional chaining (ZiJian Liu) [#36524](https://github.com/nodejs/node/pull/36524)
- [[`213dcd7930`](https://github.com/nodejs/node/commit/213dcd7930)] - **http**: add test for incomingmessage destroy (Daniele Belardi) [#33035](https://github.com/nodejs/node/pull/33035)
- [[`36b4ddd382`](https://github.com/nodejs/node/commit/36b4ddd382)] - **http**: use standard args order in IncomingMEssage onError (Daniele Belardi) [#33035](https://github.com/nodejs/node/pull/33035)
- [[`60b5e696fc`](https://github.com/nodejs/node/commit/60b5e696fc)] - **http**: remove trailing space (Daniele Belardi) [#33035](https://github.com/nodejs/node/pull/33035)
- [[`f11a648d8e`](https://github.com/nodejs/node/commit/f11a648d8e)] - **http**: add comments in \_http_incoming (Daniele Belardi) [#33035](https://github.com/nodejs/node/pull/33035)
- [[`4b81d79b58`](https://github.com/nodejs/node/commit/4b81d79b58)] - **http**: fix lint error in incoming message (Daniele Belardi) [#33035](https://github.com/nodejs/node/pull/33035)
- [[`397e31e25f`](https://github.com/nodejs/node/commit/397e31e25f)] - **http**: reafactor incoming message destroy (Daniele Belardi) [#33035](https://github.com/nodejs/node/pull/33035)
- [[`9852ebca8d`](https://github.com/nodejs/node/commit/9852ebca8d)] - **http**: do not loop over prototype in Agent (Michaël Zasso) [#36410](https://github.com/nodejs/node/pull/36410)
- [[`e46a46a4cd`](https://github.com/nodejs/node/commit/e46a46a4cd)] - **inspector**: refactor to use more primordials (Antoine du Hamel) [#36356](https://github.com/nodejs/node/pull/36356)
- [[`728f512c7d`](https://github.com/nodejs/node/commit/728f512c7d)] - **lib**: make safe primordials safe to iterate (Antoine du Hamel) [#36391](https://github.com/nodejs/node/pull/36391)
- [[`f368d697cf`](https://github.com/nodejs/node/commit/f368d697cf)] - **_Revert_** "**perf_hooks**: make PerformanceObserver an AsyncResource" (Nicolai Stange) [#36343](https://github.com/nodejs/node/pull/36343)
- [[`e2ced0d401`](https://github.com/nodejs/node/commit/e2ced0d401)] - **perf_hooks**: invoke performance_entry_callback via MakeSyncCallback() (Nicolai Stange) [#36343](https://github.com/nodejs/node/pull/36343)
- [[`7c903ec6c8`](https://github.com/nodejs/node/commit/7c903ec6c8)] - **repl**: disable blocking completions by default (Anna Henningsen) [#36564](https://github.com/nodejs/node/pull/36564)
- [[`d38a0ec93e`](https://github.com/nodejs/node/commit/d38a0ec93e)] - **src**: remove unnecessary ToLocalChecked node_errors (Daniel Bevenius) [#36547](https://github.com/nodejs/node/pull/36547)
- [[`bbc0d14cd2`](https://github.com/nodejs/node/commit/bbc0d14cd2)] - **src**: use correct microtask queue for checkpoints (Anna Henningsen) [#36581](https://github.com/nodejs/node/pull/36581)
- [[`7efb3111e8`](https://github.com/nodejs/node/commit/7efb3111e8)] - **src**: remove unnecessary ToLocalChecked call (Daniel Bevenius) [#36523](https://github.com/nodejs/node/pull/36523)
- [[`68687d3419`](https://github.com/nodejs/node/commit/68687d3419)] - **src**: remove empty name check in node_env_var.cc (raisinten) [#36133](https://github.com/nodejs/node/pull/36133)
- [[`1b4984de98`](https://github.com/nodejs/node/commit/1b4984de98)] - **src**: remove duplicate V macros in node_v8.cc (Daniel Bevenius) [#36454](https://github.com/nodejs/node/pull/36454)
- [[`5ff7f42e65`](https://github.com/nodejs/node/commit/5ff7f42e65)] - **src**: use correct outer Context’s microtask queue (Anna Henningsen) [#36482](https://github.com/nodejs/node/pull/36482)
- [[`96c095f237`](https://github.com/nodejs/node/commit/96c095f237)] - **src**: guard against env != null in node_errors.cc (Anna Henningsen) [#36414](https://github.com/nodejs/node/pull/36414)
- [[`4f3d7bb417`](https://github.com/nodejs/node/commit/4f3d7bb417)] - **src**: introduce convenience node::MakeSyncCallback() (Nicolai Stange) [#36343](https://github.com/nodejs/node/pull/36343)
- [[`e59788262c`](https://github.com/nodejs/node/commit/e59788262c)] - **src**: add typedef for CleanupHookCallback callback (Daniel Bevenius) [#36442](https://github.com/nodejs/node/pull/36442)
- [[`2a60e3b9df`](https://github.com/nodejs/node/commit/2a60e3b9df)] - **src**: fix indentation in memory_tracker-inl.h (Daniel Bevenius) [#36425](https://github.com/nodejs/node/pull/36425)
- [[`210390f6fd`](https://github.com/nodejs/node/commit/210390f6fd)] - **src**: remove identical V macro (Daniel Bevenius) [#36427](https://github.com/nodejs/node/pull/36427)
- [[`02afe586aa`](https://github.com/nodejs/node/commit/02afe586aa)] - **src**: use using declarations consistently (Daniel Bevenius) [#36365](https://github.com/nodejs/node/pull/36365)
- [[`169406b7d7`](https://github.com/nodejs/node/commit/169406b7d7)] - **src**: add missing context scopes (Anna Henningsen) [#36413](https://github.com/nodejs/node/pull/36413)
- [[`3f33d0bcda`](https://github.com/nodejs/node/commit/3f33d0bcda)] - **stream**: fix pipe deadlock when starting with needDrain (Robert Nagy) [#36563](https://github.com/nodejs/node/pull/36563)
- [[`d8b5b9499c`](https://github.com/nodejs/node/commit/d8b5b9499c)] - **stream**: accept iterable as a valid first argument (ZiJian Liu) [#36479](https://github.com/nodejs/node/pull/36479)
- [[`58319d5336`](https://github.com/nodejs/node/commit/58319d5336)] - **tls**: forward new SecureContext options (Alba Mendez) [#36416](https://github.com/nodejs/node/pull/36416)
- [[`fa40366276`](https://github.com/nodejs/node/commit/fa40366276)] - **util**: simplify constructor retrieval in inspect() (Rich Trott) [#36466](https://github.com/nodejs/node/pull/36466)
- [[`cc544dbfaa`](https://github.com/nodejs/node/commit/cc544dbfaa)] - **util**: fix instanceof checks with null prototypes during inspection (Ruben Bridgewater) [#36178](https://github.com/nodejs/node/pull/36178)
- [[`13d6597b4b`](https://github.com/nodejs/node/commit/13d6597b4b)] - **util**: fix module prefixes during inspection (Ruben Bridgewater) [#36178](https://github.com/nodejs/node/pull/36178)
- [[`20ecc82569`](https://github.com/nodejs/node/commit/20ecc82569)] - **worker**: fix broadcast channel SharedArrayBuffer passing (Anna Henningsen) [#36501](https://github.com/nodejs/node/pull/36501)
- [[`56fe9bae26`](https://github.com/nodejs/node/commit/56fe9bae26)] - **worker**: refactor MessagePort entanglement management (Anna Henningsen) [#36345](https://github.com/nodejs/node/pull/36345)

#### Documentation commits

- [[`19c233232f`](https://github.com/nodejs/node/commit/19c233232f)] - **doc**: fix AbortSignal example for stream.Readable (Michaël Zasso) [#36596](https://github.com/nodejs/node/pull/36596)
- [[`9fbab3e2f5`](https://github.com/nodejs/node/commit/9fbab3e2f5)] - **doc**: update and run license-builder for Babel (Michaël Zasso) [#36504](https://github.com/nodejs/node/pull/36504)
- [[`a1ba6686a0`](https://github.com/nodejs/node/commit/a1ba6686a0)] - **doc**: add remark about Collaborators discussion page (FrankQiu) [#36420](https://github.com/nodejs/node/pull/36420)
- [[`c5602fb166`](https://github.com/nodejs/node/commit/c5602fb166)] - **doc**: simplify worker_threads.md text (Rich Trott) [#36545](https://github.com/nodejs/node/pull/36545)
- [[`149f2cfac1`](https://github.com/nodejs/node/commit/149f2cfac1)] - **doc**: add two tips for speeding the dev builds (Momtchil Momtchev) [#36452](https://github.com/nodejs/node/pull/36452)
- [[`ad75c78c32`](https://github.com/nodejs/node/commit/ad75c78c32)] - **doc**: add note about timingSafeEqual for TypedArray (Tobias Nießen) [#36323](https://github.com/nodejs/node/pull/36323)
- [[`9830fe5c9e`](https://github.com/nodejs/node/commit/9830fe5c9e)] - **doc**: move Derek Lewis to emeritus (Rich Trott) [#36514](https://github.com/nodejs/node/pull/36514)
- [[`eb29a16bae`](https://github.com/nodejs/node/commit/eb29a16bae)] - **doc**: add issue reference to github pr template (Chinmoy Chakraborty) [#36440](https://github.com/nodejs/node/pull/36440)
- [[`f09985d42a`](https://github.com/nodejs/node/commit/f09985d42a)] - **doc**: update url.md (Rock) [#36147](https://github.com/nodejs/node/pull/36147)
- [[`c3ec90d23c`](https://github.com/nodejs/node/commit/c3ec90d23c)] - **doc**: make explicit reverting node_version.h changes (Richard Lau) [#36461](https://github.com/nodejs/node/pull/36461)
- [[`7a34452b1d`](https://github.com/nodejs/node/commit/7a34452b1d)] - **doc**: add license info to the README (FrankQiu) [#36278](https://github.com/nodejs/node/pull/36278)
- [[`22f039339f`](https://github.com/nodejs/node/commit/22f039339f)] - **doc**: revise addon mulitple initializations text (Rich Trott) [#36457](https://github.com/nodejs/node/pull/36457)
- [[`25a245443a`](https://github.com/nodejs/node/commit/25a245443a)] - **doc**: add v15.4.0 link to CHANGELOG.md (Danielle Adams) [#36456](https://github.com/nodejs/node/pull/36456)
- [[`1ec8516fd6`](https://github.com/nodejs/node/commit/1ec8516fd6)] - **doc**: add PoojaDurgad to collaborators (Pooja D P) [#36511](https://github.com/nodejs/node/pull/36511)
- [[`98918110a1`](https://github.com/nodejs/node/commit/98918110a1)] - **doc**: edit addon text about event loop blocking (Rich Trott) [#36448](https://github.com/nodejs/node/pull/36448)
- [[`62bfe3d313`](https://github.com/nodejs/node/commit/62bfe3d313)] - **doc**: note v15.0.0 changed default --unhandled-rejections=throw (kai zhu) [#36361](https://github.com/nodejs/node/pull/36361)
- [[`129053fe4c`](https://github.com/nodejs/node/commit/129053fe4c)] - **doc**: update terminology (Michael Dawson) [#36475](https://github.com/nodejs/node/pull/36475)
- [[`e331de2571`](https://github.com/nodejs/node/commit/e331de2571)] - **doc**: reword POSIX threads text in addons.md (Rich Trott) [#36436](https://github.com/nodejs/node/pull/36436)
- [[`04f166389b`](https://github.com/nodejs/node/commit/04f166389b)] - **doc**: add RaisinTen as a triager (raisinten) [#36404](https://github.com/nodejs/node/pull/36404)
- [[`3341b2cb9d`](https://github.com/nodejs/node/commit/3341b2cb9d)] - **doc**: document ABORT_ERR code (Benjamin Gruenbaum) [#36319](https://github.com/nodejs/node/pull/36319)
- [[`6a6b3af736`](https://github.com/nodejs/node/commit/6a6b3af736)] - **doc**: provide more context on techinical values (Michael Dawson) [#36201](https://github.com/nodejs/node/pull/36201)

#### Other commits

- [[`e1f00fd996`](https://github.com/nodejs/node/commit/e1f00fd996)] - **benchmark**: reduce code duplication (Rich Trott) [#36568](https://github.com/nodejs/node/pull/36568)
- [[`82a26268d7`](https://github.com/nodejs/node/commit/82a26268d7)] - **build**: do not run GitHub actions for draft PRs (Michaël Zasso) [#35910](https://github.com/nodejs/node/pull/35910)
- [[`95c80f5fb0`](https://github.com/nodejs/node/commit/95c80f5fb0)] - **build**: run some workflows only on nodejs/node (Michaël Zasso) [#36507](https://github.com/nodejs/node/pull/36507)
- [[`584ea8b26c`](https://github.com/nodejs/node/commit/584ea8b26c)] - **build**: fix make test-npm (Ruy Adorno) [#36369](https://github.com/nodejs/node/pull/36369)
- [[`01576fbc19`](https://github.com/nodejs/node/commit/01576fbc19)] - **test**: increase abort logic coverage (Moshe vilner) [#36586](https://github.com/nodejs/node/pull/36586)
- [[`22ac2279ee`](https://github.com/nodejs/node/commit/22ac2279ee)] - **test**: increase coverage for stream (ZiJian Liu) [#36538](https://github.com/nodejs/node/pull/36538)
- [[`9fc2479707`](https://github.com/nodejs/node/commit/9fc2479707)] - **test**: increase coverage for worker (ZiJian Liu) [#36491](https://github.com/nodejs/node/pull/36491)
- [[`81e603b7cf`](https://github.com/nodejs/node/commit/81e603b7cf)] - **test**: specify global object for globals (Rich Trott) [#36498](https://github.com/nodejs/node/pull/36498)
- [[`109ab787fd`](https://github.com/nodejs/node/commit/109ab787fd)] - **test**: increase coverage for fs/dir read (Zijian Liu) [#36388](https://github.com/nodejs/node/pull/36388)
- [[`9f2d3c291b`](https://github.com/nodejs/node/commit/9f2d3c291b)] - **test**: remove test-http2-client-upload as flaky (Rich Trott) [#36496](https://github.com/nodejs/node/pull/36496)
- [[`d299ceeac7`](https://github.com/nodejs/node/commit/d299ceeac7)] - **test**: increase coverage for net/blocklist (Zijian Liu) [#36405](https://github.com/nodejs/node/pull/36405)
- [[`f7635fd86d`](https://github.com/nodejs/node/commit/f7635fd86d)] - **test**: make executable name more general (Shelley Vohr) [#36489](https://github.com/nodejs/node/pull/36489)
- [[`acd78d9d25`](https://github.com/nodejs/node/commit/acd78d9d25)] - **test**: increased externalized string length (Shelley Vohr) [#36451](https://github.com/nodejs/node/pull/36451)
- [[`0f749a35ec`](https://github.com/nodejs/node/commit/0f749a35ec)] - **test**: add test for async contexts in PerformanceObserver (ZauberNerd) [#36343](https://github.com/nodejs/node/pull/36343)
- [[`dd705ad1f0`](https://github.com/nodejs/node/commit/dd705ad1f0)] - **test**: increase execFile abort coverage (Moshe vilner) [#36429](https://github.com/nodejs/node/pull/36429)
- [[`31b062d591`](https://github.com/nodejs/node/commit/31b062d591)] - **test**: fix flaky test-repl (Rich Trott) [#36415](https://github.com/nodejs/node/pull/36415)
- [[`023291b43c`](https://github.com/nodejs/node/commit/023291b43c)] - **test**: check null proto-of-proto in util.inspect() (Rich Trott) [#36399](https://github.com/nodejs/node/pull/36399)
- [[`d3d1f338c7`](https://github.com/nodejs/node/commit/d3d1f338c7)] - **test**: add SIGTRAP to test-signal-handler (Ash Cripps) [#36368](https://github.com/nodejs/node/pull/36368)
- [[`166aa8a7b5`](https://github.com/nodejs/node/commit/166aa8a7b5)] - **test**: fix child-process-pipe-dataflow (Santiago Gimeno) [#36366](https://github.com/nodejs/node/pull/36366)
- [[`ecbb757ae0`](https://github.com/nodejs/node/commit/ecbb757ae0)] - **tools**: fix make-v8.sh (Richard Lau) [#36594](https://github.com/nodejs/node/pull/36594)
- [[`e3c5adc6d0`](https://github.com/nodejs/node/commit/e3c5adc6d0)] - **tools**: fix release script sign function (Antoine du Hamel) [#36556](https://github.com/nodejs/node/pull/36556)
- [[`0d4d34748d`](https://github.com/nodejs/node/commit/0d4d34748d)] - **tools**: update ESLint to 7.16.0 (Yongsheng Zhang) [#36579](https://github.com/nodejs/node/pull/36579)
- [[`f3828c9dcb`](https://github.com/nodejs/node/commit/f3828c9dcb)] - **tools**: fix update-eslint.sh (Yongsheng Zhang) [#36579](https://github.com/nodejs/node/pull/36579)
- [[`27260c70b4`](https://github.com/nodejs/node/commit/27260c70b4)] - **tools**: fix release script (Antoine du Hamel) [#36540](https://github.com/nodejs/node/pull/36540)
- [[`c6700ad041`](https://github.com/nodejs/node/commit/c6700ad041)] - **tools**: remove unused variable in configure.py (Rich Trott) [#36525](https://github.com/nodejs/node/pull/36525)
- [[`7b8d373d5e`](https://github.com/nodejs/node/commit/7b8d373d5e)] - **tools**: lint shell scripts (Antoine du Hamel) [#36099](https://github.com/nodejs/node/pull/36099)
- [[`c6e65d09ef`](https://github.com/nodejs/node/commit/c6e65d09ef)] - **tools**: update ini in tools/node-lint-md-cli-rollup (Myles Borins) [#36474](https://github.com/nodejs/node/pull/36474)
- [[`7542a3bd55`](https://github.com/nodejs/node/commit/7542a3bd55)] - **tools**: enable no-unsafe-optional-chaining lint rule (Colin Ihrig) [#36411](https://github.com/nodejs/node/pull/36411)
- [[`26f8ccfbe6`](https://github.com/nodejs/node/commit/26f8ccfbe6)] - **tools**: update ESLint to 7.15.0 (Colin Ihrig) [#36411](https://github.com/nodejs/node/pull/36411)
- [[`8ecf2f9976`](https://github.com/nodejs/node/commit/8ecf2f9976)] - **tools**: update doc tool dependencies (Michaël Zasso) [#36407](https://github.com/nodejs/node/pull/36407)
- [[`040b39f076`](https://github.com/nodejs/node/commit/040b39f076)] - **tools**: enable no-unused-expressions lint rule (Michaël Zasso) [#36248](https://github.com/nodejs/node/pull/36248)

Windows 32-bit Installer: https://nodejs.org/dist/v15.5.0/node-v15.5.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v15.5.0/node-v15.5.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v15.5.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v15.5.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v15.5.0/node-v15.5.0.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v15.5.0/node-v15.5.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v15.5.0/node-v15.5.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v15.5.0/node-v15.5.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v15.5.0/node-v15.5.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v15.5.0/node-v15.5.0-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v15.5.0/node-v15.5.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v15.5.0/node-v15.5.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v15.5.0/node-v15.5.0.tar.gz \
Other release files: https://nodejs.org/dist/v15.5.0/ \
Documentation: https://nodejs.org/docs/v15.5.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

14ba6dd3629af5640cf0fde81b86ce9e17bcfdc7b25f7fe2a0a112d422c3d8cb  node-v15.5.0-aix-ppc64.tar.gz
f7b2859579d5c1c6e6935e939e78b5405f21797782b1f3c9607aecd6ad6a3ebf  node-v15.5.0-darwin-x64.tar.gz
5aa2c5475eac51757784121d69e8dbbd00a66dbcf12aa46c6da36b2e8be8e076  node-v15.5.0-darwin-x64.tar.xz
99c6088e276fe894576b378643c994eb5d80b285ef20f1a4fa91dd9dd16b6b23  node-v15.5.0-headers.tar.gz
544b2edcc7113bdb3e8d0cd468ea746f97e3baaa4019e8d2dde6ff0cadf9c243  node-v15.5.0-headers.tar.xz
0dd46103a8a6ef1b41642d17fe2b141f1e929f6a605b853a1480a37cb44bde1d  node-v15.5.0-linux-arm64.tar.gz
caf23a20ef6dbce192c954bc7c14296756153e23561b324266e0d2e5dba02527  node-v15.5.0-linux-arm64.tar.xz
1b61058752942aaf3c81ba1858a56efd81c6a5ddb749118438a561be8b486141  node-v15.5.0-linux-armv7l.tar.gz
a44cb19bb7c3c658074acc0e6346b1b98df5164a8c0d35c0e850418e6b829d91  node-v15.5.0-linux-armv7l.tar.xz
6774340f555120199f29c8229d887b67d6a376dedfcb1a01ef747b57900510b7  node-v15.5.0-linux-ppc64le.tar.gz
7394ee14097f0391652b444d73bfd9f655a86344e0524ace97496b08fd2bd513  node-v15.5.0-linux-ppc64le.tar.xz
3afc5b2fda9fc388d4f84b90fa519fc46f6a09bd4d30aa04337cfaf8cf985199  node-v15.5.0-linux-s390x.tar.gz
0fd2258e1524ccc4c23ff0da422846ca482f4dc1c722b45d3e39bf671f8f032c  node-v15.5.0-linux-s390x.tar.xz
75160c2f307fe4ee623d911b77a7acf249fa9ac46c4e4aa5f8cade6c26161ea0  node-v15.5.0-linux-x64.tar.gz
d1390db801acbbda692a3fefef861f8195374f374018f9404cabf53ab9816b93  node-v15.5.0-linux-x64.tar.xz
3741ff9384c953a7ff44a8e40c8d65c9e4e7834e39ae627486c20781b21ce87b  node-v15.5.0.pkg
a4f10a2e67dc99ed64c297be988fbe37f8a62f8fb8ff880f121f8be4e30df3d1  node-v15.5.0.tar.gz
4fbabdef2b152f524b290f277f915f929e2771188d99814b358cab19d6f5ecf3  node-v15.5.0.tar.xz
69f62ccd38be1602d8065f4bdaf18959c2b978d6378909fd925f2f2a547f9493  node-v15.5.0-win-x64.7z
19353f6403deed2be4b2c056b161d4bad724418473e49e7a1cbe76eea06421be  node-v15.5.0-win-x64.zip
65523f1291cb5f45bfdeeb2e15ee8065763c9c0112dd9fcb18151801a473c620  node-v15.5.0-win-x86.7z
36a14186556c57d943d4d61918ff7377d34609e0baed90b8a3be0e94065ca895  node-v15.5.0-win-x86.zip
8e3837f4b3ca1df94c9a7380dcdc0b623f94d0cd1d580eb4d28fef52dbb59967  node-v15.5.0-x64.msi
40d2239d59bcab4a2d8cdc1d65727c61ede2b39945f5b6343a9d2ba8acaf0960  node-v15.5.0-x86.msi
a885d5c1fab40ea83488b215cccfeb38378ab7a1353314f4c7f6447092c853f9  win-x64/node.exe
e5082525203684fd34553f9a26c12d722a1a06ffeac8def760b5fea6227d167e  win-x64/node.lib
3e865979d1fc8935a968f6908db76ed65fe1203f25c817944f11296197eaf5b5  win-x64/node_pdb.7z
a713e3b4b51c5fd7fde6c1261ccc333415534e1cba8477b9d10870fc208ceeb7  win-x64/node_pdb.zip
96d2264e54abdf0c032a14e4248504c0fee06b9a9d13fa3ee29b0b34295fce4d  win-x86/node.exe
844ed42e7cacc6443224b880400c7f884bd058e07dc44e52d871ac5ea736e979  win-x86/node.lib
9bb43401623fbd31fb09e3c23dfb69ed54d17044f9d01cdc12ce2f8f66968086  win-x86/node_pdb.7z
47131736ea4396b16e1c26a2a505cc4750421204010701f51c791df5f403ff17  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEj8yhP+8dDC6RAI4Jdw96mlrhVgAFAl/iQawACgkQdw96mlrh
VgBbZA//Wxy29fETK8ePNA9VHIzwWjwoGO79BUWRYU8qUXHFlxAN0CkemiDvNaN9
DwMX/cXvoBKleUMaG2ek5wWhx0NLY9yM2qTD4ngClSmnwxRYbEhzd7z+HwANKrxO
PxVWh3DaaBuyBAzh93BqGUy2JssyWXPQ4t0EYftW123hT5e1YCpEDRJ7dlyRFDNY
lhEcl/tsPZUjhgYuKW6nCzcC9BqkkIgmt8/tswu4xuxP+DijQ0VEt4Lp0dp63u+w
4sfsTcXS9rF0/ZVauWHEqpFtMAjqBwYPdW6oxl/9fmFcXLhBWSM/M9P+OErc/kTR
CftL1axpwd9U/vIwWopu4SDshM8aRxojc37mAGLF7BrB2XspwWTypxvG/o3NoxM4
wS/yod1pMg0NdXfKWLFqtCqoh3vQSjcXvFFZdsNLzvP84dKbEELvLl3vr74wvmzk
9/uQ7Q3SRBUZrduPMVe7UdoT3+hvMTCq2Cr/RpvRGVHrkY5LG+Fr39mnLisD7YLf
0kO0epYHOLC3jjC5HCRp4dVyU3YJQMTl7WftAqnJ8iLIRzI4WL7wJs0OLQRKPYRL
X8At+0hXmCQVfMjIiyaluU29qAVcU/ujXsIN4kNG3+NILy6CX4IkXwzf49/yS5d5
lFFEYjiWmAC0Uq0UM9RbLV5K2NUikZL0dImQ9ePTFVc+ozh/BAQ=
=RBoq
-----END PGP SIGNATURE-----

```
