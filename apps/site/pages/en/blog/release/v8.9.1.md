---
date: '2017-11-07T22:43:02.457Z'
category: release
title: Node v8.9.1 (LTS)
layout: blog-post
author: <PERSON>
---

### Notable Changes

- **openssl**:
  - upgrade openssl sources to 1.0.2m (<PERSON><PERSON><PERSON>) [#16691](https://github.com/nodejs/node/pull/16691)
- **_Revert_** "**https**:
  - refactor to use http internals" (<PERSON><PERSON>) [#16660](https://github.com/nodejs/node/pull/16660)

### Commits

- [[`6a7e5ceaa9`](https://github.com/nodejs/node/commit/6a7e5ceaa9)] - **deps**: V8: cherry-pick 32141e9 from upstream (<PERSON>) [#16704](https://github.com/nodejs/node/pull/16704)
- [[`a815e1b6a2`](https://github.com/nodejs/node/commit/a815e1b6a2)] - **deps**: cherry-pick e7f4e9e from upstream libuv (<PERSON><PERSON><PERSON>) [#16724](https://github.com/nodejs/node/pull/16724)
- [[`7f86e8190c`](https://github.com/nodejs/node/commit/7f86e8190c)] - **deps**: update openssl asm and asm_obsolete files (Shigeki Ohtsu) [#16691](https://github.com/nodejs/node/pull/16691)
- [[`1af2244020`](https://github.com/nodejs/node/commit/1af2244020)] - **deps**: add -no_rand_screen to openssl s_client (Shigeki Ohtsu) [nodejs/io.js#1836](https://github.com/nodejs/io.js/pull/1836)
- [[`9d98dcc395`](https://github.com/nodejs/node/commit/9d98dcc395)] - **deps**: fix asm build error of openssl in x86_win32 (Shigeki Ohtsu) [iojs/io.js#1389](https://github.com/iojs/io.js/pull/1389)
- [[`99319efc45`](https://github.com/nodejs/node/commit/99319efc45)] - **deps**: fix openssl assembly error on ia32 win32 (Fedor Indutny) [iojs/io.js#1389](https://github.com/iojs/io.js/pull/1389)
- [[`151a8da4b7`](https://github.com/nodejs/node/commit/151a8da4b7)] - **deps**: copy all openssl header files to include dir (Shigeki Ohtsu) [#16691](https://github.com/nodejs/node/pull/16691)
- [[`d68e53452c`](https://github.com/nodejs/node/commit/d68e53452c)] - **deps**: upgrade openssl sources to 1.0.2m (Shigeki Ohtsu) [#16691](https://github.com/nodejs/node/pull/16691)
- [[`a3be5bc560`](https://github.com/nodejs/node/commit/a3be5bc560)] - **doc**: add 9.x to version picker and mark 8.x as LTS (Chris Young) [#16672](https://github.com/nodejs/node/pull/16672)
- [[`08b75c1591`](https://github.com/nodejs/node/commit/08b75c1591)] - **_Revert_** "**https**: refactor to use http internals" (Myles Borins) [#16660](https://github.com/nodejs/node/pull/16660)
- [[`d334a95834`](https://github.com/nodejs/node/commit/d334a95834)] - **openssl**: fix keypress requirement in apps on win32 (Shigeki Ohtsu) [iojs/io.js#1389](https://github.com/iojs/io.js/pull/1389)
- [[`bf26b96fd6`](https://github.com/nodejs/node/commit/bf26b96fd6)] - **src**: add 'dynamic' process.release.lts property (Rod Vagg) [#16656](https://github.com/nodejs/node/pull/16656)
- [[`dfac6cc0bb`](https://github.com/nodejs/node/commit/dfac6cc0bb)] - **test**: update process-release for Node 8 Carbon (Jeremiah Senkpiel) [#16656](https://github.com/nodejs/node/pull/16656)

Windows 32-bit Installer: https://nodejs.org/dist/v8.9.1/node-v8.9.1-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v8.9.1/node-v8.9.1-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v8.9.1/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v8.9.1/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v8.9.1/node-v8.9.1.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v8.9.1/node-v8.9.1-darwin-x64.tar.gz \
Linux 32-bit Binary: https://nodejs.org/dist/v8.9.1/node-v8.9.1-linux-x86.tar.xz \
Linux 64-bit Binary: https://nodejs.org/dist/v8.9.1/node-v8.9.1-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v8.9.1/node-v8.9.1-linux-ppc64le.tar.xz \
Linux PPC BE 64-bit Binary: https://nodejs.org/dist/v8.9.1/node-v8.9.1-linux-ppc64.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v8.9.1/node-v8.9.1-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v8.9.1/node-v8.9.1-aix-ppc64.tar.gz \
SmartOS 32-bit Binary: https://nodejs.org/dist/v8.9.1/node-v8.9.1-sunos-x86.tar.xz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v8.9.1/node-v8.9.1-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v8.9.1/node-v8.9.1-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v8.9.1/node-v8.9.1-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v8.9.1/node-v8.9.1-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v8.9.1/node-v8.9.1.tar.gz \
Other release files: https://nodejs.org/dist/v8.9.1/ \
Documentation: https://nodejs.org/docs/v8.9.1/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

70c9bbb0b7e9125ba2d1e83826cd7ab981d21bf59f58b53809bb1922e59211a9  node-v8.9.1-aix-ppc64.tar.gz
05c992a6621d28d564b92bf3051a5dc0adf83839237c0d4653a8cdb8a1c73b94  node-v8.9.1-darwin-x64.tar.gz
ed71abc42e00f9d1f55f0977ff55cad2f68f3e8693211d33922d9286e6f6540b  node-v8.9.1-darwin-x64.tar.xz
20bba14a649ac39210a74720e399bde117ed38f95bde3548c16b36b8a1702cfc  node-v8.9.1-headers.tar.gz
2f5e2d2bd3b5242d20a65be645b55f41e62550dfacc35d8b445f8613aec117e3  node-v8.9.1-headers.tar.xz
47521340ff82617c1e6ba63ce300685e1b8b7cf5c0ec2e71628bcdb398085b29  node-v8.9.1-linux-arm64.tar.gz
f774660980dcf931bf29847a5f26317823a063fa4a56f85f37c3222d77cce7c1  node-v8.9.1-linux-arm64.tar.xz
fedae6bb781d9f1d2ec37ba6206f021f873598ab5e9218f1a84cd45348322709  node-v8.9.1-linux-armv6l.tar.gz
b22e0dbc067c7e92aa0f6f24627a67bd1983bb239154658541489417bcfc739d  node-v8.9.1-linux-armv6l.tar.xz
54efdd6a22d03294e4b6dc00338fa2d37e9740040d85638a62a3603cf31d3b26  node-v8.9.1-linux-armv7l.tar.gz
39564e969b4098794b07e5cabf4af9efe93d1c77e0f03412bca57131bf29d671  node-v8.9.1-linux-armv7l.tar.xz
d3e11a9ef301afdecb10ed26470492fd03402b86bf9efc3f89a9aef541bf9a2c  node-v8.9.1-linux-ppc64le.tar.gz
bfde0cc192859fafdcbc5f04913e4eb8cd092bb689a74f8a1fd09f9b0eeb9659  node-v8.9.1-linux-ppc64le.tar.xz
7ab8c4bf36364624b6bc7610319f1e2c32a7c882aa6392ce285faaee39597dce  node-v8.9.1-linux-ppc64.tar.gz
e440170091d1f64d8730c59a58ea43a8fbd37bdab299e20090b319d4f6568a83  node-v8.9.1-linux-ppc64.tar.xz
48160ddaa7397cf85ca0cf333cc87dc3485956c75a3cdf98f04735bb81b37da6  node-v8.9.1-linux-s390x.tar.gz
bf9c37cc33b524724c2e474c2745c2ba34843a8b8bf8051c8e40ab0d934965cd  node-v8.9.1-linux-s390x.tar.xz
0e49da19cdf4c89b52656e858346775af21f1953c308efbc803b665d6069c15c  node-v8.9.1-linux-x64.tar.gz
8be82805f7c1ab3e64d4569fb9a90ded2de78dd27cadbb91bad1bf975dae1e2d  node-v8.9.1-linux-x64.tar.xz
830f5f4ff29c2b30089a19e1b71d52d02e965b4e1f08282a09616d99aae1a42b  node-v8.9.1-linux-x86.tar.gz
74a6e140716b2d8a240ab0760fb8edc403d06edace42659bdf8fa6de15992cf0  node-v8.9.1-linux-x86.tar.xz
faa6397688c11458ad220c363898bd5028f1dbcf626dbe9be1c9d1d16f695e0d  node-v8.9.1.pkg
b40ff46aa99640235cc1d3e27abd8749425ed8ad17936dde3ebb06d0d74fed82  node-v8.9.1-sunos-x64.tar.gz
2447d5b4cd787605d21f82159072764649910658d2f9d4f6dd2847c7380eff7c  node-v8.9.1-sunos-x64.tar.xz
a5a31c9c211fccfa54068270ab95aab0c73d05d789d9cbc16fc521e1e4698c2c  node-v8.9.1-sunos-x86.tar.gz
813dc3cdbfe061dd39efac2cf55679a2c03d1923c6f7e61ab8db2fc158f41cf1  node-v8.9.1-sunos-x86.tar.xz
32491b7fcc4696b2cdead45c47e52ad16bbed8f78885d32e873952fee0f971e1  node-v8.9.1.tar.gz
ef160c21f60f8aca64145985e01b4044435e381dc16e8f0640ed0223e84f17e0  node-v8.9.1.tar.xz
ff28dd5ff5a09a904e364742b58011af33d0a3fe148831e55b2c60f1bc251569  node-v8.9.1-win-x64.7z
db89c6e041da359561fbe7da075bb4f9881a0f7d3e98c203e83732cfb283fa4a  node-v8.9.1-win-x64.zip
a30b6a56d424f8a34e65fe8f197a6db17dee6fa10ed50ffdf8490ca0787d995a  node-v8.9.1-win-x86.7z
e3c38a7802acf1b2e89ff172f460dd1476ffc119bbcc88aa8d5364acd9714aa2  node-v8.9.1-win-x86.zip
5b747214518d62891e48ca58483df84bc3b0cea8b34176f22f92364e07ffdfc3  node-v8.9.1-x64.msi
2269b89726b055a86e988adb39b35cdd23434302416d3be0d73697f51da3e339  node-v8.9.1-x86.msi
ef1a6f906a31d115f0a2c8abfdd1d34f62f0789abd539910838ee1b62bf22dce  win-x64/node.exe
28c8b55b3cccfe2c2c64ba6178340ac997545f1e1e4652122724df0dbbf0380b  win-x64/node.lib
d6a356265c7b020d72a52a3d1f7783237d2b951e2b74d81015c509d5359a06b9  win-x64/node_pdb.7z
b8c4d963ab23c3b3e8a106d150e33c713734e9a84844549a632e2bddf8fc698c  win-x64/node_pdb.zip
e8697af447c53faea624f854f44219107adca2d78c78a122c3ce9793aef97e2a  win-x86/node.exe
a7956a209e0e8699977399bbe71319873898a5a70b9162d6eab4f0bfc09791c9  win-x86/node.lib
6d0a2847d205f179e0e9b3debb264e3bb1eb89a5e2c01792e6533f581ea3ba75  win-x86/node_pdb.7z
18a8506287415596f27c71d8e3efde37fbc3b7addb0e669c2452491891ef1a85  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEd5hKmG68KqeGvA9msB+7koIcWHoFAlohHwgACgkQsB+7koIc
WHqu/A/9GoS85Qp6Ayj6OPOi1F/aeWLxzUqDEqzVtHDbvXNyEVJPZPCGn6U2d6Jp
+EDq6BxXmK1rEEiyq/q7fReA3win1wR0CuNYC9Vtxuaux2+kaCd55ZcmlrTwBjKA
A2ItmGk3OWaelXXuPCb2Wj5DHl+Odp81MEXTj/91dEd9RBOqPdzQ7X5yWt2ZqOZO
fXYxeF0ZtMgGi5Bx5gWPyoIE+a0Q6SVWicP0BKmwcsA+d/dTTRgHKAUVrlLLryFx
FCHRPc+J1i/cEEjvvxkLrn1Nh2aciHVIQiDl4txYRNMhcmwVgpAJw9sTPFByzPjI
dJaQlqk9MB2TI22p/99hhOlr/RMtdrT7CbNHwfquIuNeV7a6SbfDa1Nc+f6/IShA
lTYo2uW5Ri0PtZeZEU/HELUYZ37Dpgrwg5MQ1uC8vaFzodAKlGARxPd+qAIuADPI
w5/CP+X826aao86LgM3rei1tJUIp2JfYCOx8goyWIMV9OWG/E4rRiPLA8YkwEReu
+Hp7xCi0qK6bfyLTz6Q8STHHnoBmp1+l+Fn4ZTCM2UDd5AbVN0ENmjyg7TB6lSwv
3sXfF60qHX6cnQ95+16cwNrN+grm0qgaLbmVOGB9ghUV0nP/++m9fXVeK54Mo4nk
YY9LxcIQeC7MlBKtjqRGzDxkfdvvfHFbhedYGbGwft6ZA/PwIGo=
=O3nW
-----END PGP SIGNATURE-----

```
