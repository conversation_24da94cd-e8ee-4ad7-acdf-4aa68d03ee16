---
date: '2018-03-28T16:31:48.275Z'
category: release
title: Node v9.10.0 (Current)
layout: blog-post
author: <PERSON><PERSON>
---

### Notable Changes

- **Upgrade to OpenSSL 1.0.2o**: Does not contain any security fixes that are known to impact Node.js.
- **Fix for inspector DNS rebinding vulnerability (CVE-2018-7160)**: A malicious website could use a DNS rebinding attack to trick a web browser to bypass same-origin-policy checks and allow HTTP connections to localhost or to hosts on the local network, potentially to an open inspector port as a debugger, therefore gaining full code execution access. The inspector now only allows connections that have a browser `Host` value that is either not subject to DNS resolution or matches `localhost` or `localhost6`.
- **Fix for `'path'` module regular expression denial of service (CVE-2018-7158)**: A regular expression used for parsing POSIX paths could be used to cause a denial of service if an attacker were able to have a specially crafted path string passed through one of the impacted `'path'` module functions.
- **Reject spaces in HTTP `Content-Length` header values (CVE-2018-7159)**: The Node.js HTTP parser allowed for spaces inside `Content-Length` header values. Such values now lead to rejected connections in the same way as non-numeric values.
- **Update root certificates**: 5 additional root certificates have been added to the Node.js binary and 30 have been removed.

- **cluster**:
  - Add support for `NODE_OPTIONS="--inspect"` (Sameer Srivastava) [#19165](https://github.com/nodejs/node/pull/19165)
- **crypto**:
  - Expose the public key of a certificate (Hannes Magnusson) [#17690](https://github.com/nodejs/node/pull/17690)
- **n-api**:
  - Add `napi_fatal_exception` to trigger an `uncaughtException` in JavaScript (Mathias Buus) [#19337](https://github.com/nodejs/node/pull/19337)
- **path**:
  - Fix regression in `posix.normalize` (Michaël Zasso) [#19520](https://github.com/nodejs/node/pull/19520)
- **stream**:
  - Improve stream creation performance (Brian White) [#19401](https://github.com/nodejs/node/pull/19401)
- **Added new collaborators**
  - [BethGriggs](https://github.com/BethGriggs) Beth Griggs

### Commits

- [[`926214aefe`](https://github.com/nodejs/node/commit/926214aefe)] - **cluster**: add support for NODE_OPTIONS="--inspect" (Sameer Srivastava) [#19165](https://github.com/nodejs/node/pull/19165)
- [[`6ead99aa73`](https://github.com/nodejs/node/commit/6ead99aa73)] - **console**: don't swallow call stack exceeded errors (Dan Kaplun) [#19423](https://github.com/nodejs/node/pull/19423)
- [[`02671dc12b`](https://github.com/nodejs/node/commit/02671dc12b)] - **crypto**: update root certificates (Ben Noordhuis) [#19322](https://github.com/nodejs/node/pull/19322)
- [[`fd8c79ddfc`](https://github.com/nodejs/node/commit/fd8c79ddfc)] - **(SEMVER-MINOR)** **crypto**: add docs & tests for cert.pubkey & cert.fingerprint256 (Hannes Magnusson) [#17690](https://github.com/nodejs/node/pull/17690)
- [[`23312675cb`](https://github.com/nodejs/node/commit/23312675cb)] - **(SEMVER-MINOR)** **crypto**: provide full cert details to checkServerIdentity (Hannes Magnusson) [#17690](https://github.com/nodejs/node/pull/17690)
- [[`26e2938a50`](https://github.com/nodejs/node/commit/26e2938a50)] - **(SEMVER-MINOR)** **crypto**: add cert.pubkey containing the raw pubkey of certificate (Hannes Magnusson) [#17690](https://github.com/nodejs/node/pull/17690)
- [[`f5d9324315`](https://github.com/nodejs/node/commit/f5d9324315)] - **deps**: add -no_rand_screen to openssl s_client (Shigeki Ohtsu) [nodejs/io.js#1836](https://github.com/nodejs/io.js/pull/1836)
- [[`f5eb182b50`](https://github.com/nodejs/node/commit/f5eb182b50)] - **deps**: fix asm build error of openssl in x86_win32 (Shigeki Ohtsu) [iojs/io.js#1389](https://github.com/iojs/io.js/pull/1389)
- [[`ddcb3fc886`](https://github.com/nodejs/node/commit/ddcb3fc886)] - **deps**: fix openssl assembly error on ia32 win32 (Fedor Indutny) [iojs/io.js#1389](https://github.com/iojs/io.js/pull/1389)
- [[`d908169bad`](https://github.com/nodejs/node/commit/d908169bad)] - **deps**: copy all openssl header files to include dir (Shigeki Ohtsu) [#19638](https://github.com/nodejs/node/pull/19638)
- [[`0cd883fe09`](https://github.com/nodejs/node/commit/0cd883fe09)] - **deps**: upgrade openssl sources to 1.0.2o (Shigeki Ohtsu) [#19638](https://github.com/nodejs/node/pull/19638)
- [[`c39167dc26`](https://github.com/nodejs/node/commit/c39167dc26)] - **deps**: reject interior blanks in Content-Length (Ben Noordhuis) [nodejs-private/http-parser-private#1](https://github.com/nodejs-private/http-parser-private/pull/1)
- [[`3bc15a69ae`](https://github.com/nodejs/node/commit/3bc15a69ae)] - **deps**: upgrade http-parser to v2.8.0 (Ben Noordhuis) [nodejs-private/http-parser-private#1](https://github.com/nodejs-private/http-parser-private/pull/1)
- [[`6591d9f761`](https://github.com/nodejs/node/commit/6591d9f761)] - **deps**: cherry-pick 0c35b72 from upstream V8 (Gus Caplan) [#18038](https://github.com/nodejs/node/pull/18038)
- [[`e533911696`](https://github.com/nodejs/node/commit/e533911696)] - **doc**: remove use of "random port" re dgram send (Thomas Hunter II) [#19620](https://github.com/nodejs/node/pull/19620)
- [[`3894981af2`](https://github.com/nodejs/node/commit/3894981af2)] - **doc**: improve assert legacy text (Rich Trott) [#19622](https://github.com/nodejs/node/pull/19622)
- [[`8191ada9ae`](https://github.com/nodejs/node/commit/8191ada9ae)] - **doc**: improve Buffer() text (Rich Trott) [#19567](https://github.com/nodejs/node/pull/19567)
- [[`2fadc9ef68`](https://github.com/nodejs/node/commit/2fadc9ef68)] - **doc**: fix run-on sentence in buffer.md (Rich Trott) [#19567](https://github.com/nodejs/node/pull/19567)
- [[`962c5816a2`](https://github.com/nodejs/node/commit/962c5816a2)] - **doc**: change v-notation for version in buffer.md (Rich Trott) [#19567](https://github.com/nodejs/node/pull/19567)
- [[`5a2f336994`](https://github.com/nodejs/node/commit/5a2f336994)] - **doc**: add missing fs.Stats.size section (Vse Mozhet Byt) [#19583](https://github.com/nodejs/node/pull/19583)
- [[`8653c42a41`](https://github.com/nodejs/node/commit/8653c42a41)] - **doc**: rename HTTP2 to HTTP/2 (Timothy Gu) [#19603](https://github.com/nodejs/node/pull/19603)
- [[`b70ac0ab2e`](https://github.com/nodejs/node/commit/b70ac0ab2e)] - **doc**: remove confusing note about child process stdio (Anna Henningsen) [#19552](https://github.com/nodejs/node/pull/19552)
- [[`5e3d971f79`](https://github.com/nodejs/node/commit/5e3d971f79)] - **doc**: add BethGriggs to collaborators (Beth Griggs) [#19610](https://github.com/nodejs/node/pull/19610)
- [[`5e9f9297b3`](https://github.com/nodejs/node/commit/5e9f9297b3)] - **doc**: document `make docopen` (Ayush Gupta) [#19321](https://github.com/nodejs/node/pull/19321)
- [[`4db7848e09`](https://github.com/nodejs/node/commit/4db7848e09)] - **doc**: remove example labels from buffer.md (Rich Trott) [#19582](https://github.com/nodejs/node/pull/19582)
- [[`f07e820e6d`](https://github.com/nodejs/node/commit/f07e820e6d)] - **doc**: add 'v' prefix to all versions in metadata (Tobias Nießen) [#19590](https://github.com/nodejs/node/pull/19590)
- [[`7e9b7a5683`](https://github.com/nodejs/node/commit/7e9b7a5683)] - **doc**: add missing metadata for fs.open (Tobias Nießen) [#19585](https://github.com/nodejs/node/pull/19585)
- [[`d47e5d022f`](https://github.com/nodejs/node/commit/d47e5d022f)] - **doc**: add link & simplify data event (net.Socket) (Christopher Hiller) [#19487](https://github.com/nodejs/node/pull/19487)
- [[`43f24c0406`](https://github.com/nodejs/node/commit/43f24c0406)] - **doc**: add directory structure in writing-tests.md (juggernaut451) [#18802](https://github.com/nodejs/node/pull/18802)
- [[`157fc28710`](https://github.com/nodejs/node/commit/157fc28710)] - **doc**: add added in versions to fs.Stats properties (jvelezpo) [#19266](https://github.com/nodejs/node/pull/19266)
- [[`fa17002215`](https://github.com/nodejs/node/commit/fa17002215)] - **doc**: add missing metadata for settings.windowsHide (Tobias Nießen) [#19578](https://github.com/nodejs/node/pull/19578)
- [[`4532a8913d`](https://github.com/nodejs/node/commit/4532a8913d)] - **doc**: add `require.main` to `require` properties (Vse Mozhet Byt) [#19573](https://github.com/nodejs/node/pull/19573)
- [[`1e8ece149a`](https://github.com/nodejs/node/commit/1e8ece149a)] - **doc**: add missing metadata for cluster.settings.cwd (Tobias Nießen) [#19569](https://github.com/nodejs/node/pull/19569)
- [[`933c58cd76`](https://github.com/nodejs/node/commit/933c58cd76)] - **doc**: add types for some `process` properties (Vse Mozhet Byt) [#19571](https://github.com/nodejs/node/pull/19571)
- [[`ae0e243028`](https://github.com/nodejs/node/commit/ae0e243028)] - **doc**: fix n-api example string (Steven R. Loomis) [#19205](https://github.com/nodejs/node/pull/19205)
- [[`7c9ba3db40`](https://github.com/nodejs/node/commit/7c9ba3db40)] - **doc**: correct introduced_in metadata for buffer doc (Rich Trott) [#19545](https://github.com/nodejs/node/pull/19545)
- [[`1073f09cad`](https://github.com/nodejs/node/commit/1073f09cad)] - **doc**: minor improvements to buffer.md (Rich Trott) [#19547](https://github.com/nodejs/node/pull/19547)
- [[`9845fc3e4a`](https://github.com/nodejs/node/commit/9845fc3e4a)] - **doc**: Add a missing comma (jiangq) [#19555](https://github.com/nodejs/node/pull/19555)
- [[`d1c45e258c`](https://github.com/nodejs/node/commit/d1c45e258c)] - **doc**: update child_process.md (Ari Leo Frankel) [#19075](https://github.com/nodejs/node/pull/19075)
- [[`8e3f59fbb5`](https://github.com/nodejs/node/commit/8e3f59fbb5)] - **doc**: clarify child_process promise rejections (TomCoded) [#19541](https://github.com/nodejs/node/pull/19541)
- [[`e9f41eecc8`](https://github.com/nodejs/node/commit/e9f41eecc8)] - **doc**: move StackOverflow to unofficial section (josephleon) [#19416](https://github.com/nodejs/node/pull/19416)
- [[`3f49174969`](https://github.com/nodejs/node/commit/3f49174969)] - **doc**: move who-to-cc to COLABORATOR_GUIDE.md (Rich Trott) [#19460](https://github.com/nodejs/node/pull/19460)
- [[`65c9a5278c`](https://github.com/nodejs/node/commit/65c9a5278c)] - **doc**: require passing CI for landing code (Rich Trott) [#19458](https://github.com/nodejs/node/pull/19458)
- [[`98d038a1f3`](https://github.com/nodejs/node/commit/98d038a1f3)] - **doc**: simplify COLLABORATOR_GUIDE.md instructions (Rich Trott) [#19458](https://github.com/nodejs/node/pull/19458)
- [[`e5bcd8d981`](https://github.com/nodejs/node/commit/e5bcd8d981)] - **doc**: reduce CI options in COLLABORATOR_GUIDE.md (Rich Trott) [#19458](https://github.com/nodejs/node/pull/19458)
- [[`26e97a124d`](https://github.com/nodejs/node/commit/26e97a124d)] - **doc**: add new documentation rule (estrada9166) [#18726](https://github.com/nodejs/node/pull/18726)
- [[`ed55386d74`](https://github.com/nodejs/node/commit/ed55386d74)] - **doc**: add fs declarations to stream doc js examples (Ivan Filenko) [#18804](https://github.com/nodejs/node/pull/18804)
- [[`9c672624b3`](https://github.com/nodejs/node/commit/9c672624b3)] - **doc**: remove \*\*Note:\*\* tags (James M Snell) [#18592](https://github.com/nodejs/node/pull/18592)
- [[`742b304ea3`](https://github.com/nodejs/node/commit/742b304ea3)] - **doc**: warn about using util.inspect/util.format (James M Snell) [#17791](https://github.com/nodejs/node/pull/17791)
- [[`d3833b0734`](https://github.com/nodejs/node/commit/d3833b0734)] - **doc**: update collaborator guide (Ruben Bridgewater) [#19116](https://github.com/nodejs/node/pull/19116)
- [[`c3886b50c9`](https://github.com/nodejs/node/commit/c3886b50c9)] - **doc**: add note about browsers and HTTP/2 (Steven) [#19476](https://github.com/nodejs/node/pull/19476)
- [[`cc7ba0bb9d`](https://github.com/nodejs/node/commit/cc7ba0bb9d)] - **doc**: fix/improve inspector profiler example (Ali Ijaz Sheikh) [#19379](https://github.com/nodejs/node/pull/19379)
- [[`9c9263e7cc`](https://github.com/nodejs/node/commit/9c9263e7cc)] - **doc**: add trivikr to collaborators (Trivikram) [#19384](https://github.com/nodejs/node/pull/19384)
- [[`5960cde4eb`](https://github.com/nodejs/node/commit/5960cde4eb)] - **doc**: fix changelog (Myles Borins) [#19515](https://github.com/nodejs/node/pull/19515)
- [[`b351e0eda6`](https://github.com/nodejs/node/commit/b351e0eda6)] - **http**: use more destructuring (Tobias Nießen) [#19481](https://github.com/nodejs/node/pull/19481)
- [[`49c0efd2a2`](https://github.com/nodejs/node/commit/49c0efd2a2)] - **http2**: remove some unnecessary next ticks (James M Snell) [#19451](https://github.com/nodejs/node/pull/19451)
- [[`583d5afa5e`](https://github.com/nodejs/node/commit/583d5afa5e)] - **inspector**: do not allow host names (Eugene Ostroukhov)
- [[`fc1a610a00`](https://github.com/nodejs/node/commit/fc1a610a00)] - **inspector**: check Host header for local connections (Eugene Ostroukhov)
- [[`419e88ea4a`](https://github.com/nodejs/node/commit/419e88ea4a)] - **lib,test**: lint fixes for linter upgrade (Rich Trott) [#19528](https://github.com/nodejs/node/pull/19528)
- [[`fd8523fe44`](https://github.com/nodejs/node/commit/fd8523fe44)] - **n-api**: re-write test_make_callback (Gabriel Schulhof) [#19448](https://github.com/nodejs/node/pull/19448)
- [[`29a04b7ed6`](https://github.com/nodejs/node/commit/29a04b7ed6)] - **(SEMVER-MINOR)** **n-api**: add napi_fatal_exception (Mathias Buus) [#19337](https://github.com/nodejs/node/pull/19337)
- [[`223b42648f`](https://github.com/nodejs/node/commit/223b42648f)] - **openssl**: fix keypress requirement in apps on win32 (Shigeki Ohtsu) [iojs/io.js#1389](https://github.com/iojs/io.js/pull/1389)
- [[`40916a27bc`](https://github.com/nodejs/node/commit/40916a27bc)] - **path**: fix regression in posix.normalize (Michaël Zasso) [#19520](https://github.com/nodejs/node/pull/19520)
- [[`fad5dcce3b`](https://github.com/nodejs/node/commit/fad5dcce3b)] - **src**: drop CNNIC+StartCom certificate whitelisting (Ben Noordhuis) [#19322](https://github.com/nodejs/node/pull/19322)
- [[`780a5d6f3a`](https://github.com/nodejs/node/commit/780a5d6f3a)] - **src**: use `unordered_map` for perf marks (Anna Henningsen) [#19558](https://github.com/nodejs/node/pull/19558)
- [[`f13cc3237e`](https://github.com/nodejs/node/commit/f13cc3237e)] - **stream**: improve stream creation performance (Brian White) [#19401](https://github.com/nodejs/node/pull/19401)
- [[`8996d3cf45`](https://github.com/nodejs/node/commit/8996d3cf45)] - **test**: remove third param from assert.strictEqual (<EMAIL>) [#19536](https://github.com/nodejs/node/pull/19536)
- [[`c1a327b0ed`](https://github.com/nodejs/node/commit/c1a327b0ed)] - **test**: remove custom error message (DingDean) [#19526](https://github.com/nodejs/node/pull/19526)
- [[`9265f4bcb7`](https://github.com/nodejs/node/commit/9265f4bcb7)] - **test**: remove string literal from assertions (Nathaniel Weeks) [#19276](https://github.com/nodejs/node/pull/19276)
- [[`efa38bd1a0`](https://github.com/nodejs/node/commit/efa38bd1a0)] - **test**: remove message from assert.strictEqual() (willhayslett) [#19525](https://github.com/nodejs/node/pull/19525)
- [[`40be64d96d`](https://github.com/nodejs/node/commit/40be64d96d)] - **test**: rename regression tests more expressively (Ujjwal Sharma) [#19495](https://github.com/nodejs/node/pull/19495)
- [[`0310df8fe6`](https://github.com/nodejs/node/commit/0310df8fe6)] - **test**: refactor parallel/test-tls-ca-concat.js (juggernaut451) [#19092](https://github.com/nodejs/node/pull/19092)
- [[`5f1a01d816`](https://github.com/nodejs/node/commit/5f1a01d816)] - **test**: fix buggy getTTYfd() implementation (Rich Trott) [#17781](https://github.com/nodejs/node/pull/17781)
- [[`c6b993bde7`](https://github.com/nodejs/node/commit/c6b993bde7)] - **test**: move firstInvalidFD() out of common module (Rich Trott) [#17781](https://github.com/nodejs/node/pull/17781)
- [[`8e69026962`](https://github.com/nodejs/node/commit/8e69026962)] - **test**: remove getTTYfd() from common module (Rich Trott) [#17781](https://github.com/nodejs/node/pull/17781)
- [[`a8d9ccf8fe`](https://github.com/nodejs/node/commit/a8d9ccf8fe)] - **test**: remove common.projectDir (Rich Trott) [#17781](https://github.com/nodejs/node/pull/17781)
- [[`74582933c9`](https://github.com/nodejs/node/commit/74582933c9)] - **test**: refactor test-fs-readfile-tostring-fail (Rich Trott) [#19404](https://github.com/nodejs/node/pull/19404)
- [[`a56ba1258d`](https://github.com/nodejs/node/commit/a56ba1258d)] - **tools**: update certdata.txt (Ben Noordhuis) [#19322](https://github.com/nodejs/node/pull/19322)
- [[`e895d54224`](https://github.com/nodejs/node/commit/e895d54224)] - **tools**: simplify tools/doc/preprocess.js (Vse Mozhet Byt) [#19539](https://github.com/nodejs/node/pull/19539)
- [[`4c3465f68a`](https://github.com/nodejs/node/commit/4c3465f68a)] - **tools**: fix nits in tools/doc/common.js (Vse Mozhet Byt) [#19599](https://github.com/nodejs/node/pull/19599)
- [[`ab561c090b`](https://github.com/nodejs/node/commit/ab561c090b)] - **tools**: shorten metadata parsing (Tobias Nießen) [#19512](https://github.com/nodejs/node/pull/19512)
- [[`0db7b8cd87`](https://github.com/nodejs/node/commit/0db7b8cd87)] - **tools**: make metadata parsing less permissive (Tobias Nießen) [#19512](https://github.com/nodejs/node/pull/19512)
- [[`4007d6cbfe`](https://github.com/nodejs/node/commit/4007d6cbfe)] - **tools**: update ESLint to 4.19.1 (Rich Trott) [#19528](https://github.com/nodejs/node/pull/19528)
- [[`89e7a5faad`](https://github.com/nodejs/node/commit/89e7a5faad)] - **tools**: fix nits in tools/doc/preprocess.js (Vse Mozhet Byt) [#19473](https://github.com/nodejs/node/pull/19473)
- [[`0414a8c7ed`](https://github.com/nodejs/node/commit/0414a8c7ed)] - **tools**: fix logic nit in tools/doc/generate.js (Vse Mozhet Byt) [#19475](https://github.com/nodejs/node/pull/19475)

Windows 32-bit Installer: https://nodejs.org/dist/v9.10.0/node-v9.10.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v9.10.0/node-v9.10.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v9.10.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v9.10.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v9.10.0/node-v9.10.0.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v9.10.0/node-v9.10.0-darwin-x64.tar.gz \
Linux 32-bit Binary: https://nodejs.org/dist/v9.10.0/node-v9.10.0-linux-x86.tar.xz \
Linux 64-bit Binary: https://nodejs.org/dist/v9.10.0/node-v9.10.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v9.10.0/node-v9.10.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v9.10.0/node-v9.10.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v9.10.0/node-v9.10.0-aix-ppc64.tar.gz \
SmartOS 32-bit Binary: https://nodejs.org/dist/v9.10.0/node-v9.10.0-sunos-x86.tar.xz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v9.10.0/node-v9.10.0-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v9.10.0/node-v9.10.0-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v9.10.0/node-v9.10.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v9.10.0/node-v9.10.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v9.10.0/node-v9.10.0.tar.gz \
Other release files: https://nodejs.org/dist/v9.10.0/ \
Documentation: https://nodejs.org/docs/v9.10.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

3ad90e0ed617eaa19d77d9d02c5c42f8b03560abbd02ddaa0db0016725660b9a  node-v9.10.0-aix-ppc64.tar.gz
c4b98cc2f3c00b770f24549de112902b56d57be7963a1047cd116b357bc61569  node-v9.10.0-darwin-x64.tar.gz
35a2e934d475aaca229d4d054365118bd303baa9c9954d087d0cee02ec42ba90  node-v9.10.0-darwin-x64.tar.xz
a41222cd88298b2e277cf1e04e88e9e962f9cbd0b0ce66837cea1f75ac21d33a  node-v9.10.0-headers.tar.gz
efda9c7d6344e529b3966ef7e95e89d93528490032d6d8df1af23581ae8c2393  node-v9.10.0-headers.tar.xz
2ff3351616e58d1355b643f6013cb45b30bf84aad523de05cdbf01d6c7b68e30  node-v9.10.0-linux-arm64.tar.gz
ba1d682aa1d5a12eeb39e7f51e4c67c6122b24482869ca2547c6f094eae90658  node-v9.10.0-linux-arm64.tar.xz
061f0a1d4563407626c826417f301913a6a6c61d12cc59b2f3ba806d995749b3  node-v9.10.0-linux-armv6l.tar.gz
42d1c45dc27686f9d3ea93472f0723139b72524c72b46f81fc1514fbcd9d7707  node-v9.10.0-linux-armv6l.tar.xz
8f1b5c62951f0dda9f3592d19198d8f8aea7a2c1ef43a6adf235ba8a65765e61  node-v9.10.0-linux-armv7l.tar.gz
bafe8061f6b27710ee64284c6a01793645eeb914bb711a9e6fc752a536ddfdc4  node-v9.10.0-linux-armv7l.tar.xz
fe4a120bc64065355956f22bf52695d35e68e7cefba6fcb94f6b53b445234b51  node-v9.10.0-linux-ppc64le.tar.gz
f727a1f8350656a7149021b1ceae6e83bffb520c4ce9e20d9e329036eee58ace  node-v9.10.0-linux-ppc64le.tar.xz
2f0693357d002f2e6be90bc2b6a9cf385b59a88adc2098a8afcbae3a8af88c6d  node-v9.10.0-linux-s390x.tar.gz
e0887c4605d2f796c5e95fd9096672b77bd1d43c01f11450f1f8019a9b0d816d  node-v9.10.0-linux-s390x.tar.xz
21a69c0f0181ec451444739d5c2f1df27cb96e7f328461dfa658e65846dc99ef  node-v9.10.0-linux-x64.tar.gz
b9bfffc03ef0e2c97d463619911552c7f5b1b8699de07bb913990a8b33800cb9  node-v9.10.0-linux-x64.tar.xz
c0113bc0ff5d48a5b2827ada0fa70ee9296f27b043ff4cec1f805374f7fe88f2  node-v9.10.0-linux-x86.tar.gz
1e96a62e76dd93d2a1f49d164b5e3bcfb5722844ef5ac15158c299f73cd446e3  node-v9.10.0-linux-x86.tar.xz
5959d1dec918b6585464c8022abcc0d2566931e4c2bcf0e4f5c935a737b3e742  node-v9.10.0.pkg
44b49e36b7bc853e15028c6af4837c7e9d95f08127d6751d274a2dfc5345552e  node-v9.10.0-sunos-x64.tar.gz
0b94e6d921f172f5ecb40b82f53d6bbf1836067b7897c7bd0362b450764221b8  node-v9.10.0-sunos-x64.tar.xz
19fadd9e2488fd377b771384b28fc4f274dbb2ae43aebefcbafd8e73a9a6a5cd  node-v9.10.0-sunos-x86.tar.gz
2385369de958788ecf5adaddabece9a4fae2646fffc1507993a71435cf5a82f5  node-v9.10.0-sunos-x86.tar.xz
e5654e552bcc7d011fe0c5bade53ba5c3acbd8d26bfda2cf57057537a03c8d76  node-v9.10.0.tar.gz
945a35a2599dfc0a306cdb3aae1c70034d6c28b03ab85daf8f2166fdaaade63a  node-v9.10.0.tar.xz
56bfb27221c35273d17ed2edac19ceaf62a2c76a1fd911af94b976c19e98345f  node-v9.10.0-win-x64.7z
3f159de87fd987e7bf30bbffce722e2e5133c44fc847883053359e9b08d6fa88  node-v9.10.0-win-x64.zip
0374a0cd01f932d836a032462ce7105b73850f7f4232a4f9b5cc77a506eba4a1  node-v9.10.0-win-x86.7z
833a0f4ea29ed16e61774918f38921c41e1e9f7ba53209e0442163e7d30bb3ed  node-v9.10.0-win-x86.zip
c053e4fe3b6c9f68231c28df41eb2569453abb0404c1fb22cc78a5cf2b967283  node-v9.10.0-x64.msi
d590505b28ee081c592efc82899d57418ee6c0665423a121589614e493281ad3  node-v9.10.0-x86.msi
bda61e2a5f0b4043475735743892649854710fb30e73335b2cd71d2349c1e13d  win-x64/node.exe
a280cbf7597330557094cf782896c42d2043f322349908d494be201cd77c6167  win-x64/node.lib
a6d89e52142d0953a4520c81d3b66a3ce9344106c9cf41d2e596fbc6b22f16d8  win-x64/node_pdb.7z
38481709e85e5e7d58d01ff4fadf73b7670f53db7550fe1ea882bb399d5900dd  win-x64/node_pdb.zip
280a5a8bc64f87c7288c310d4aacebd6916bad5948e9de7e544b533df3c89ea4  win-x86/node.exe
d80069347fa780a4e9864385e472d0d2da628a3ae7ee559f691700b609d571d1  win-x86/node.lib
b3516fa691896f723e05dba0dc15590e8ef35090333fd6b623c6a1dd0055dec9  win-x86/node_pdb.7z
0f1e89b86a807805b307699079de06b84f655bef3ecc6ac84619a91c0b113b7b  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQEzBAEBCAAdFiEEDv/hvO/ZyE49CYFSkzsB9AtcqUYFAlq7wk0ACgkQkzsB9Atc
qUZuAggAhvUCCGt8fOhW1i4t/GManaPYBME0zG+rJ85Ou/33q7CgQ//z5dWfRMb5
dzCNjK2FaEbdTH2PZkYIsG444zHqQWCHdlIJ2N2+Qg7+kJ+1WGiphi6ERtTCEK8n
M7TtlKBZIAqN8hcxVaNMLoJOa51GHiHQoyW7e8W7GCeCBOSBt7kvS9l1GJtV/D7J
AbHmE2MVJFp7i0AdVoanhMBtrAXKNjjseeFQAFUMG5t66oJUGDPWDdDEVyRu8kLd
fVQkWWWiXwbmwyGoX/ocS+OjFMgYTKCcuMswHIx5U7DQGaOynPWGJW3yk9goTwjD
PETIRrZngctYUrgJFiEWx7tJZ+ENiQ==
=tHfb
-----END PGP SIGNATURE-----

```
