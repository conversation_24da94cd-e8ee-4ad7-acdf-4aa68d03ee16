---
date: '2024-02-14T17:35:59.309Z'
category: release
title: Node v20.11.1 (LTS)
layout: blog-post
author: <PERSON>
---

## 2024-02-14, Version 20.11.1 'Iron' (LTS), @RafaelGSS prepared by @marco-ippolito

### Notable changes

This is a security release.

### Notable changes

- CVE-2024-21892 - Code injection and privilege escalation through Linux capabilities- (High)
- CVE-2024-22019 - http: Reading unprocessed HTTP request with unbounded chunk extension allows DoS attacks- (High)
- CVE-2024-21896 - Path traversal by monkey-patching Buffer internals- (High)
- CVE-2024-22017 - setuid() does not drop all privileges due to io_uring - (High)
- CVE-2023-46809 - Node.js is vulnerable to the Marvin Attack (timing variant of the Ble<PERSON><PERSON><PERSON>er attack against PKCS#1 v1.5 padding) - (Medium)
- CVE-2024-21891 - Multiple permission model bypasses due to improper path traversal sequence sanitization - (Medium)
- CVE-2024-21890 - Improper handling of wildcards in --allow-fs-read and --allow-fs-write (Medium)
- CVE-2024-22025 - Denial of Service by resource exhaustion in fetch() brotli decoding - (Medium)
- undici version 5.28.3
- libuv version 1.48.0
- OpenSSL version 3.0.13+quic1

### Commits

- \[[`7079c062bb`](https://github.com/nodejs/node/commit/7079c062bb)] - **crypto**: disable PKCS#1 padding for privateDecrypt (Michael Dawson) [nodejs-private/node-private#525](https://github.com/nodejs-private/node-private/pull/525)
- \[[`186a6e1ffb`](https://github.com/nodejs/node/commit/186a6e1ffb)] - **deps**: fix GHSA-f74f-cvh7-c6q6/CVE-2024-24806 (Santiago Gimeno) [#51737](https://github.com/nodejs/node/pull/51737)
- \[[`686da19abb`](https://github.com/nodejs/node/commit/686da19abb)] - **deps**: disable io_uring support in libuv by default (Tobias Nießen) [nodejs-private/node-private#529](https://github.com/nodejs-private/node-private/pull/529)
- \[[`f7b44bfbce`](https://github.com/nodejs/node/commit/f7b44bfbce)] - **deps**: update archs files for openssl-3.0.13+quic1 (Node.js GitHub Bot) [#51614](https://github.com/nodejs/node/pull/51614)
- \[[`7a30fecea2`](https://github.com/nodejs/node/commit/7a30fecea2)] - **deps**: upgrade openssl sources to quictls/openssl-3.0.13+quic1 (Node.js GitHub Bot) [#51614](https://github.com/nodejs/node/pull/51614)
- \[[`480fc169a8`](https://github.com/nodejs/node/commit/480fc169a8)] - **fs**: protect against modified Buffer internals in possiblyTransformPath (Tobias Nießen) [nodejs-private/node-private#497](https://github.com/nodejs-private/node-private/pull/497)
- \[[`77ac7c3153`](https://github.com/nodejs/node/commit/77ac7c3153)] - **http**: add maximum chunk extension size (Paolo Insogna) [nodejs-private/node-private#519](https://github.com/nodejs-private/node-private/pull/519)
- \[[`ed7d149675`](https://github.com/nodejs/node/commit/ed7d149675)] - **lib**: use cache fs internals against path traversal (RafaelGSS) [nodejs-private/node-private#516](https://github.com/nodejs-private/node-private/pull/516)
- \[[`89bd5fc38f`](https://github.com/nodejs/node/commit/89bd5fc38f)] - **lib**: update undici to v5.28.3 (Matteo Collina) [nodejs-private/node-private#539](https://github.com/nodejs-private/node-private/pull/539)
- \[[`d01dd4291d`](https://github.com/nodejs/node/commit/d01dd4291d)] - **permission**: fix wildcard when children > 1 (Rafael Gonzaga) [#51209](https://github.com/nodejs/node/pull/51209)
- \[[`40ff37dfcc`](https://github.com/nodejs/node/commit/40ff37dfcc)] - **src**: fix HasOnly(capability) in node::credentials (Tobias Nießen) [nodejs-private/node-private#505](https://github.com/nodejs-private/node-private/pull/505)
- \[[`3f6addd590`](https://github.com/nodejs/node/commit/3f6addd590)] - **src,deps**: disable setuid() etc if io_uring enabled (Tobias Nießen) [nodejs-private/node-private#529](https://github.com/nodejs-private/node-private/pull/529)
- \[[`d6da413aa4`](https://github.com/nodejs/node/commit/d6da413aa4)] - **test,doc**: clarify wildcard usage (RafaelGSS) [nodejs-private/node-private#517](https://github.com/nodejs-private/node-private/pull/517)
- \[[`c213910aea`](https://github.com/nodejs/node/commit/c213910aea)] - **zlib**: pause stream if outgoing buffer is full (Matteo Collina) [nodejs-private/node-private#541](https://github.com/nodejs-private/node-private/pull/541)

Windows 32-bit Installer: https://nodejs.org/dist/v20.11.1/node-v20.11.1-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v20.11.1/node-v20.11.1-x64.msi \
Windows ARM 64-bit Installer: https://nodejs.org/dist/v20.11.1/node-v20.11.1-arm64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v20.11.1/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v20.11.1/win-x64/node.exe \
Windows ARM 64-bit Binary: https://nodejs.org/dist/v20.11.1/win-arm64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v20.11.1/node-v20.11.1.pkg \
macOS Apple Silicon 64-bit Binary: https://nodejs.org/dist/v20.11.1/node-v20.11.1-darwin-arm64.tar.gz \
macOS Intel 64-bit Binary: https://nodejs.org/dist/v20.11.1/node-v20.11.1-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v20.11.1/node-v20.11.1-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v20.11.1/node-v20.11.1-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v20.11.1/node-v20.11.1-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v20.11.1/node-v20.11.1-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v20.11.1/node-v20.11.1-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v20.11.1/node-v20.11.1-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v20.11.1/node-v20.11.1.tar.gz \
Other release files: https://nodejs.org/dist/v20.11.1/ \
Documentation: https://nodejs.org/docs/v20.11.1/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

43a881788549e1b3425eb5f2b92608f438f146e08213de09c5bd5ff841cae7ae  node-v20.11.1-aix-ppc64.tar.gz
3f8e77b775372c0b27d2b85ce899d80339691f480e64dde43d4eb01504a58679  node-v20.11.1-arm64.msi
e0065c61f340e85106a99c4b54746c5cee09d59b08c5712f67f99e92aa44995d  node-v20.11.1-darwin-arm64.tar.gz
fd771bf3881733bfc0622128918ae6baf2ed1178146538a53c30ac2f7006af5b  node-v20.11.1-darwin-arm64.tar.xz
c52e7fb0709dbe63a4cbe08ac8af3479188692937a7bd8e776e0eedfa33bb848  node-v20.11.1-darwin-x64.tar.gz
ed69f1f300beb75fb4cad45d96aacd141c3ddca03b6d77c76b42cb258202363d  node-v20.11.1-darwin-x64.tar.xz
0aa42c91b441e945ff43bd3a837759c58b436de57dcd033d02e5cbcd2fba1f87  node-v20.11.1-headers.tar.gz
edce238817acf5adce3123366b55304aff2a1f0849231d1b49f42370e454b6f8  node-v20.11.1-headers.tar.xz
e34ab2fc2726b4abd896bcbff0250e9b2da737cbd9d24267518a802ed0606f3b  node-v20.11.1-linux-arm64.tar.gz
c957f29eb4e341903520caf362534f0acd1db7be79c502ae8e283994eed07fe1  node-v20.11.1-linux-arm64.tar.xz
e42791f76ece283c7a4b97fbf716da72c5128c54a9779f10f03ae74a4bcfb8f6  node-v20.11.1-linux-armv7l.tar.gz
28e0120d2d150a8f41717899d33167b8b32053778665583d49ff971bfd188d1b  node-v20.11.1-linux-armv7l.tar.xz
9823305ac3a66925a9b61d8032f6bbb4c3e33c28e7f957ebb27e49732feffb23  node-v20.11.1-linux-ppc64le.tar.gz
51343cacf5cdf5c4b5e93e919d19dd373d6ef43d5f2c666eae299f26e31d08b5  node-v20.11.1-linux-ppc64le.tar.xz
4c66b2f247fdd8720853321526d7cda483018fcb32014b75c30f3a54ecacaea7  node-v20.11.1-linux-s390x.tar.gz
b32616b705cd0ddbb230b95c693e3d7a37becc2ced9bcadea8dc824cceed6be0  node-v20.11.1-linux-s390x.tar.xz
bf3a779bef19452da90fb88358ec2c57e0d2f882839b20dc6afc297b6aafc0d7  node-v20.11.1-linux-x64.tar.gz
d8dab549b09672b03356aa2257699f3de3b58c96e74eb26a8b495fbdc9cf6fbe  node-v20.11.1-linux-x64.tar.xz
f1cd449fcbeb1b948e8498cb8edd9655fa319d109a7f4c5bd96a9b122b91538a  node-v20.11.1-win-arm64.7z
e85461ec124956a2853c4ee6e13c4f4889d63c88beb3d530c1ee0c4b51dc10e7  node-v20.11.1-win-arm64.zip
fb9b5348259988a562a48eed7349e7e716c0bec78d98ad0a336b2993a8b3bf34  node-v20.11.1-win-x64.7z
bc032628d77d206ffa7f133518a6225a9c5d6d9210ead30d67e294ff37044bda  node-v20.11.1-win-x64.zip
c2b1863d8979546804a39fc63d0a9bc9c6e49cb2f6c9d1e52844a24629b24765  node-v20.11.1-win-x86.7z
b98e95f78416d1359b647cfa09ba2a48b76d41b56a776df822bf36ffe8e76a2d  node-v20.11.1-win-x86.zip
c54f5f7e2416e826fd84e878f28e3b53363ae9c3f60a140af4434b2453b5ae89  node-v20.11.1-x64.msi
63e2aed4dabb96eed6903a3974e006d3c29c218472aac60ae3c3c7de00df13b1  node-v20.11.1-x86.msi
c46019a095a1549d000e85da13f17972a448e0be5854a51786ecccde7278a012  node-v20.11.1.pkg
4af1ba6ea848cc05908b8a62b02fb27684dd52b2a7988ee82b0cfa72deb90b94  node-v20.11.1.tar.gz
77813edbf3f7f16d2d35d3353443dee4e61d5ee84d9e3138c7538a3c0ca5209e  node-v20.11.1.tar.xz
a5a9d30a8f7d56e00ccb27c1a7d24c8d0bc96a2689ebba8eb7527698793496f1  win-arm64/node.exe
93529170cebe57c0f4830a4cc6a261b6cc9bcf0cd8b3e88ac4995a5015031d79  win-arm64/node.lib
c14c6e927406b8683cbfb8a67ca4c8fd5093ca7812b5b1627e3d6a53d3674565  win-arm64/node_pdb.7z
68034cd09d8dfaa755d1b280da13e20388cc486ac57b037b3e11dfe2d6b74284  win-arm64/node_pdb.zip
bc585910690318aaebe3c57669cb83ca9d1e5791efd63195e238f54686e6c2ec  win-x64/node.exe
53a982d490cb9fcc4b231a8b95147de423b36186bc6f4ba5697b20117fdcbd5d  win-x64/node.lib
ccac9f2f5219ed858aeddb306d6493478ba9675c7cbf009e83742437d6752c4f  win-x64/node_pdb.7z
bec5da4035c84580843978a59ef9bcc1c0eaca881cf9e1c94e63a1862cf14421  win-x64/node_pdb.zip
3829137e062b1e2eb9947ef05e4b717ae578a8fce1c5c60fe4f6ae7ef2ec0240  win-x86/node.exe
c5321bb65dcecb3989f9b8f6ec56369c16627ca4bade0c78afb6b88f7dde50e4  win-x86/node.lib
20ca60ced1fc21f15ea952b4406aec6bde39d20eab11cf042040628841b2249e  win-x86/node_pdb.7z
bef05cebedce5949ae35e87e7d4789c16fa73caf478483fcf92e5dbb9ba5d774  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQGzBAEBCAAdFiEEiQwI24V5Fi/uDfnbi+q0389VXvQFAmXM+TcACgkQi+q0389V
XvQl3AwAqqm2uBMDzd+BlR1sG7y/eUtUYPVdwmCh0DeFXPHxuaIbFf0PGMEgcV8u
kn3OBF4pnSCPZNbJYJsLO1S+b/5Vk+Vlkq1WkOxqQHUHmM9GcJUuShadl0YaDNen
WXXMoYKWqMRJ6fQ3tRRh+vbMSXtsLqXT8TMVJq+Qb7a7yj4QRjw/Dd+8uKGGIhBY
U04HWsz33RJLu6AUnhF03eO1N8E1V48JptklDx5ZkY8GYa3F6jQsFld+jhmkZ9tg
4q9NDNijVpj56UsUhLAYD0J9IKS18tvQxNrKmBGUSZjFOByVhbUdLXnSMtW1i1U9
cYhP6Q5wg/fnjqCfQ90TauoJZOblKIL/PHlf6cQGPrrRa1bz3xGyCAIve5KFhLxf
Vfj1ctk2ktzmuNhjAu5G/1VALQUNpiTm4Yz433JpoMMZ3mTHN+fuALOX4TQbdLRz
HKphTz02436348XC9bNz2cvjm74cy9fqwjQ/y84AmxiTJMFPg0XqICg4tu9rd49d
8FJc4TLZ
=r/CD
-----END PGP SIGNATURE-----
```
