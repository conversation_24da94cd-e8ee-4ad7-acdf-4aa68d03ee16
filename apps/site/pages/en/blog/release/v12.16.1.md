---
date: '2020-02-18T20:06:29.634Z'
category: release
title: Node v12.16.1 (LTS)
layout: blog-post
author: <PERSON><PERSON>
---

### Notable changes

Node.js 12.16.0 included 6 regressions that are being fixed in this release

**Accidental Unflagging of Self Resolving Modules**:

12.16.0 included a large update to the ESM implementation. One of the new features,
Self Referential Modules, was accidentally released without requiring the `--experimental-modules`
flag. This release is being made to appropriately flag the feature.

**Process Cleanup Changed Introduced WASM-Related Assertion**:

A change during Node.js process cleanup led to a crash in combination with
specific usage of WASM. This has been fixed by partially reverted said change.
A regression test and a full fix are being worked on and will likely be included
in future 12.x and 13.x releases.

**Use Largepages Runtime Option Introduced Linking Failure**:

A Semver-Minor change to introduce `--use-largepages` as a runtime option
introduced a linking failure. This had been fixed in master but regressed as the fix has not yet gone out
in a Current release. The feature has been reverted, but will be able to reland with a fix in a future
Semver-Minor release.

**Async Hooks was Causing an Exception When Handling Errors**:

Changes in async hooks internals introduced a case where an internal api call could be called with undefined
causing a process to crash. The change to async hooks was reverted. A regression test and fix has been proposed
and the change could re-land in a future Semver-Patch release if the regression is reliably fixed.

**New Enumerable Read-Only Property on EventEmitter breaks @types/extend**

A new property for enumerating events was added to the EventEmitter class. This
broke existing code that was using the `@types/extend` module for extending classses
as `@types/extend` was attemping to write over the existing field which the new
change made read-only. As this is the first property on EventEmitter that is
read-only this feature could be considered Semver-Major. The new feature has been
reverted but could re-land in a future Semver-Minor release if a non breaking way
of applying it is found.

**Exceptions in the HTTP parser were not emitting an uncaughtException**

A refactoring to Node.js interanls resulted in a bug where errors in the HTTP
parser were not being emitted by `process.on('uncaughtException')` when the `async_hooks` `after`
hook exists. The fix to this bug has been included in this release.

### Commits

- [[`51fdd759b9`](https://github.com/nodejs/node/commit/51fdd759b9)] - **async_hooks**: ensure event after been emitted on runInAsyncScope (legendecas) [#31784](https://github.com/nodejs/node/pull/31784)
- [[`7a1b0ac06f`](https://github.com/nodejs/node/commit/7a1b0ac06f)] - **_Revert_** "**build**: re-introduce --use-largepages as no-op" (Myles Borins) [#31782](https://github.com/nodejs/node/pull/31782)
- [[`a53eeca2a9`](https://github.com/nodejs/node/commit/a53eeca2a9)] - **_Revert_** "**build**: switch realpath to pwd" (Myles Borins) [#31782](https://github.com/nodejs/node/pull/31782)
- [[`6d432994e6`](https://github.com/nodejs/node/commit/6d432994e6)] - **_Revert_** "**build**: warn upon --use-largepages config option" (Myles Borins) [#31782](https://github.com/nodejs/node/pull/31782)
- [[`a5bc00af12`](https://github.com/nodejs/node/commit/a5bc00af12)] - **_Revert_** "**events**: allow monitoring error events" (Myles Borins)
- [[`f0b2d875d9`](https://github.com/nodejs/node/commit/f0b2d875d9)] - **module**: 12.x self resolve flag as experimental modules (Guy Bedford) [#31757](https://github.com/nodejs/node/pull/31757)
- [[`42b68a4e24`](https://github.com/nodejs/node/commit/42b68a4e24)] - **src**: inform callback scopes about exceptions in HTTP parser (Anna Henningsen) [#31801](https://github.com/nodejs/node/pull/31801)
- [[`065a32f064`](https://github.com/nodejs/node/commit/065a32f064)] - **_Revert_** "**src**: make --use-largepages a runtime option" (Myles Borins) [#31782](https://github.com/nodejs/node/pull/31782)
- [[`3d5beebc62`](https://github.com/nodejs/node/commit/3d5beebc62)] - **_Revert_** "**src**: make large_pages node.cc include conditional" (Myles Borins) [#31782](https://github.com/nodejs/node/pull/31782)
- [[`43d02e20e0`](https://github.com/nodejs/node/commit/43d02e20e0)] - **src**: keep main-thread Isolate attached to platform during Dispose (Anna Henningsen) [#31795](https://github.com/nodejs/node/pull/31795)
- [[`7a5954ef26`](https://github.com/nodejs/node/commit/7a5954ef26)] - **src**: fix -Winconsistent-missing-override warning (Colin Ihrig) [#30549](https://github.com/nodejs/node/pull/30549)

Windows 32-bit Installer: https://nodejs.org/dist/v12.16.1/node-v12.16.1-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v12.16.1/node-v12.16.1-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v12.16.1/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v12.16.1/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v12.16.1/node-v12.16.1.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v12.16.1/node-v12.16.1-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v12.16.1/node-v12.16.1-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v12.16.1/node-v12.16.1-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v12.16.1/node-v12.16.1-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v12.16.1/node-v12.16.1-aix-ppc64.tar.gz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v12.16.1/node-v12.16.1-sunos-x64.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v12.16.1/node-v12.16.1-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v12.16.1/node-v12.16.1-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v12.16.1/node-v12.16.1.tar.gz \
Other release files: https://nodejs.org/dist/v12.16.1/ \
Documentation: https://nodejs.org/docs/v12.16.1/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

d928dd3dc4a79c39e45aa8f1f00c33117eb6145001427cb4dd838340932d8f2d  node-v12.16.1-aix-ppc64.tar.gz
34895bce210ca4b3cf19cd480e6563588880dd7f5d798f3782e3650580d35920  node-v12.16.1-darwin-x64.tar.gz
7d20b9955c96d99fad1ef0e849b340d3ca1386dacd6c79e19d1ad3695bad7f6b  node-v12.16.1-darwin-x64.tar.xz
815e732561b6be1b2389b0cac53e34b65ad1835a5f0354d9888d992a4613947f  node-v12.16.1-headers.tar.gz
602ad5ba8ccf076215e0547632329d8a5c3ab0275a293ee1aebbfdd9345d2a6a  node-v12.16.1-headers.tar.xz
22750695d432e22f2a1faadfcd534a88a18933ffd658d45b08a5afa61acbc24a  node-v12.16.1-linux-arm64.tar.gz
396c43ba507b8ec33c70cdc6f73b4a7c725bddb3f74a85a8b8ed77b436856fed  node-v12.16.1-linux-arm64.tar.xz
d418d0516dfd744a8109e4ed58b021e3a1babb64baed2ebc30e613df97c643fb  node-v12.16.1-linux-armv7l.tar.gz
593dbc7e52518815d02dd5019c54afa5ada3dd143482cbc533c8c1ea80f44349  node-v12.16.1-linux-armv7l.tar.xz
c8b1e47ac7a7a98f9f9b596b1ff567ad5416141317a3dcf4bff988508eb85bd6  node-v12.16.1-linux-ppc64le.tar.gz
eee65be6b23245221920f18dded281f74a00dfa2479713134eea3146f1593ea6  node-v12.16.1-linux-ppc64le.tar.xz
fef9eb79a9f5c50193ba369a2399998a86411f26eadc924674d915b06b71ac8f  node-v12.16.1-linux-s390x.tar.gz
6a77be335dec9349a40216cea072f2ca6e53bf10a7276040bfbd33984cdc5b95  node-v12.16.1-linux-s390x.tar.xz
b2d9787da97d6c0d5cbf24c69fdbbf376b19089f921432c5a61aa323bc070bea  node-v12.16.1-linux-x64.tar.gz
b826753f14df9771609ffb8e7d2cc4cb395247cb704cf0cea0f04132d9cf3505  node-v12.16.1-linux-x64.tar.xz
268703502c475a3e6878de0b1e67e6cc06315f41f878c3ec0bd58291537357da  node-v12.16.1.pkg
fec3dd2fdadc2b2fd8be29ebe8cdb859eace84e920a32053585feae0e641e9ce  node-v12.16.1-sunos-x64.tar.gz
fd5786acdc316a5eb64b73e274f44aa74ed2a74ef0b292cec2983bcab1ee96f0  node-v12.16.1-sunos-x64.tar.xz
4fe8c3454f9bee5bbe72d44aa25cd931859b3037b7a9473081b3b2bd1b465b95  node-v12.16.1.tar.gz
0a95845ba02c46102b5671d0c5732460073f2d397488337e18d1fc30146d412d  node-v12.16.1.tar.xz
e97aa4c4dc44185f55be7f46ff70a5594066f50853f3b7ad409cc108d32eef17  node-v12.16.1-win-x64.7z
b93b73572c5e495154a9823d494de5729c77d1c83b041171154c4b5f3f76b590  node-v12.16.1-win-x64.zip
2091f727d84044062e2657e20a24ccfbad3c699ee337c47d60ce77fcdadd711a  node-v12.16.1-win-x86.7z
7d4cb6b9b96d221e5fbb28083d688f36561b05ae2f796a22ebd606f319834b39  node-v12.16.1-win-x86.zip
bb70e966062e860e3497b4c5cd490485b722f28ad8eb0abfda7fe91e987c59f1  node-v12.16.1-x64.msi
9fd8788c089bb77999b7f2e6ef94cb1721dd9f5b1b08d4ed2f81e89529c9137a  node-v12.16.1-x86.msi
3f8dabbe93d05367035df2078cb72a20c1b74cf1b41648ea91d902825902fcee  win-x64/node.exe
4eb31990d5e46ba2a51d4fc14ef187ef51c403afe8823c71c557c0ba3cb6d732  win-x64/node.lib
057803deaa2b29b597e15a82cfe78271f9320fc0cd23239564ab04df19ead3f6  win-x64/node_pdb.7z
deee04ab036212fbaa2eeb5efec7e5ff87666dce3968c68450bf8fa12d1c2a59  win-x64/node_pdb.zip
78fa91d73172df8e10e1824394087a9d6409259fdc3bd5a83fa90d53e4edb6a6  win-x86/node.exe
67967c338c91652bbb376a0a4f3e0162478033d872d71af673e5ce127888ee50  win-x86/node.lib
9e8b18c0df1fed91154fc9f7e5cf4e93a85b99ce653be0f77d6cd87f15556cc0  win-x86/node_pdb.7z
b1a9c35d168c5876d040e1aeeb3e7c44c919f2716c560b44556cf9c6975991fc  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQEzBAEBCAAdFiEEDv/hvO/ZyE49CYFSkzsB9AtcqUYFAl5MPjUACgkQkzsB9Atc
qUZrhgf/YyjE93K71UoKdkAPN316DY6asYc4+hoOwny+r4W1+0QSiWl7Pe+lm4tC
OfCjrIr5i+OC+/VimWLlZ2ufwxWKJCX560TpE9vLe5z6+CTNEkTR2kA20lT/BnTH
js/A+6BAilkMNrdXWspRGgIytk4/njsgq7uTutY0lx7jOclFPXuW615FCqUNA0Ev
jyTy5b2htDH3J9QrLiHTlLZcz438W1RPvdQ8N8vIfY3QFTcugmBiltIg++rQ4Zh+
tQt/48hI/Xb4MvIQ83azzBwkvnXci4sVcekseuMeKjb5vbnd1qxvSPFMS6zvKmHi
PrJMcpCXJiFuo92YBTL/rCkiTr/jsQ==
=wB4y
-----END PGP SIGNATURE-----

```
