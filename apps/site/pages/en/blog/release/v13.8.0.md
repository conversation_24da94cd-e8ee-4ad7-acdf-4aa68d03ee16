---
date: '2020-02-06T11:38:12.580Z'
category: release
title: Node v13.8.0 (Current)
layout: blog-post
author: <PERSON>
---

### Notable Changes

This is a security release.

Vulnerabilities fixed:

- **CVE-2019-15606**: HTTP header values do not have trailing OWS trimmed.
- **CVE-2019-15605**: HTTP request smuggling using malformed Transfer-Encoding header.
- **CVE-2019-15604**: Remotely trigger an assertion on a TLS server with a malformed certificate string.

Also, HTTP parsing is more strict to be more secure. Since this may
cause problems in interoperability with some non-conformant HTTP
implementations, it is possible to disable the strict checks with the
`--insecure-http-parser` command line flag, or the `insecureHTTPParser`
http option. Using the insecure HTTP parser should be avoided.

### Commits

- [[`b7da194714`](https://github.com/nodejs/node/commit/b7da194714)] - **benchmark**: support optional headers with wrk (<PERSON>) [nodejs-private/node-private#189](https://github.com/nodejs-private/node-private/pull/189)
- [[`1156a9e5f8`](https://github.com/nodejs/node/commit/1156a9e5f8)] - **crypto**: fix assertion caused by unsupported ext (Fedor Indutny) [nodejs-private/node-private#175](https://github.com/nodejs-private/node-private/pull/175)
- [[`8f41e837bb`](https://github.com/nodejs/node/commit/8f41e837bb)] - **deps**: update llhttp to 2.0.4 (Beth Griggs) [nodejs-private/node-private#199](https://github.com/nodejs-private/node-private/pull/199)
- [[`07d56e49cf`](https://github.com/nodejs/node/commit/07d56e49cf)] - **(SEMVER-MINOR)** **http**: make --insecure-http-parser configurable per-stream or per-server (Anna Henningsen) [#31448](https://github.com/nodejs/node/pull/31448)
- [[`25b6897e8a`](https://github.com/nodejs/node/commit/25b6897e8a)] - **http**: strip trailing OWS from header values (Sam Roberts) [nodejs-private/node-private#189](https://github.com/nodejs-private/node-private/pull/189)
- [[`eea3a7429b`](https://github.com/nodejs/node/commit/eea3a7429b)] - **test**: using TE to smuggle reqs is not possible (Sam Roberts) [nodejs-private/node-private#199](https://github.com/nodejs-private/node-private/pull/199)

Windows 32-bit Installer: https://nodejs.org/dist/v13.8.0/node-v13.8.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v13.8.0/node-v13.8.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v13.8.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v13.8.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v13.8.0/node-v13.8.0.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v13.8.0/node-v13.8.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v13.8.0/node-v13.8.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v13.8.0/node-v13.8.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v13.8.0/node-v13.8.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v13.8.0/node-v13.8.0-aix-ppc64.tar.gz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v13.8.0/node-v13.8.0-sunos-x64.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v13.8.0/node-v13.8.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v13.8.0/node-v13.8.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v13.8.0/node-v13.8.0.tar.gz \
Other release files: https://nodejs.org/dist/v13.8.0/ \
Documentation: https://nodejs.org/docs/v13.8.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

25a267d18f6d7d66528bbe9a9d54730a8a39b27e9d315114ca008a105a01e55c  node-v13.8.0-aix-ppc64.tar.gz
ae480e2b124cb55667763848b8ec0fde1bc35d5e0b76debe881034689a68eaea  node-v13.8.0-darwin-x64.tar.gz
09eb3068e03df502d119a63201552a20910981acc64b4f5913ee07183cabb886  node-v13.8.0-darwin-x64.tar.xz
01eec708bb3cb0d57108c24f87ccf00a389aab9127ff671b8ee4e14808b23cb8  node-v13.8.0-headers.tar.gz
d29fcff404843a52e1f19718fc60779170162c844a9bedc746f8d9de00a240ae  node-v13.8.0-headers.tar.xz
69a51fa98a9543f09f2a3838a04b49fd774005398de9732caf337e027145c988  node-v13.8.0-linux-arm64.tar.gz
f1d4167a6911e42d836a5459c992cdaf35a03ab0700ea80831d7df5d706d1baf  node-v13.8.0-linux-arm64.tar.xz
228d79dfc07749d90cfa7938cffb7201d8e12ca7f92cba2f1766431b8d2acedf  node-v13.8.0-linux-armv7l.tar.gz
e35e1c39478d3d0508231d042a450564aed935a4b9eed245e81ff9074db59215  node-v13.8.0-linux-armv7l.tar.xz
7fe719cb5c92fecbad462a240ff4f1b12220d022c781af9a10c64282c95a0fc2  node-v13.8.0-linux-ppc64le.tar.gz
e243be4a2a62c90d33c3f1bf49afdad894e154e6faa4282719c31eb541a7b39a  node-v13.8.0-linux-ppc64le.tar.xz
acbd4d0336f0fdea3222dfa707705b4ebfc1fa0986df510b77dc23406e8635e6  node-v13.8.0-linux-s390x.tar.gz
b466a5d4ab128a94cf0559d201a60e7ad867170cd2ae64dda2ee893c93cef059  node-v13.8.0-linux-s390x.tar.xz
bf30432175ea8a95fa3e5fe09e96d9fc17b07099742d5c83c4cf9d0edfc411ff  node-v13.8.0-linux-x64.tar.gz
47a8cb675358f2ff534ad3d6709f14de0433f76d3af92cf389b8dcc78a1236ad  node-v13.8.0-linux-x64.tar.xz
a5d4de1c8f64fac3d0b0fae6de966c4d091e1cdc570e9f4b880f314c222befb0  node-v13.8.0.pkg
95b370e2bcb127e235d1388ce1f5703d7789417b670c2b6e5287b47bb9f7e692  node-v13.8.0-sunos-x64.tar.gz
19fec6076c2ace2b68c80b06fcbea92390a9bf575281d16759e4b2dc1b49620f  node-v13.8.0-sunos-x64.tar.xz
815b5e1b18114f35da89e4d98febeaba97555d51ef593bd5175db2b05f2e8be6  node-v13.8.0.tar.gz
bb6104c9da90cfba02e231c524899bad6592d67ea5dc9dcb88ac0ff2b63f83c0  node-v13.8.0.tar.xz
b0bc2ba000a8828ff5c7371f250ea7f07c662646696f4fdefa99207ecb5481b1  node-v13.8.0-win-x64.7z
f198f3e4b120fc84b61d12e7222530c5bb9c6f864735bb41a8db1cf1b94a64c3  node-v13.8.0-win-x64.zip
1f7af35b3b3999931e61df8e4d0e21fb6c416b7d3f780a31bb3c23c439672501  node-v13.8.0-win-x86.7z
e0abc7394d79c5be19a615ebadd6029e05dca0741ae41f5dc5cc9476f040f998  node-v13.8.0-win-x86.zip
264c9dc82400446b7df1103324c6f85c5bf413f088ead884e7262bbfaf41188b  node-v13.8.0-x64.msi
ea7ae6a7c1d8627070955ec0362e50b997f80ba419b8f00cc8299310c38e7ff5  node-v13.8.0-x86.msi
d7bb5ffed9175053cc5f8cc12ac014c81621cd88af855418c37675b7331206e7  win-x64/node.exe
93d8299044f598d700f67480a72d710a59888a14fea2e9f0d5fcc248b0ec02e2  win-x64/node.lib
04a1533a22c5b8c061e58b28cfc921f8de6d604c62ffb63584d566427811c4a7  win-x64/node_pdb.7z
5656b66192d186d98ade188d0259cc8b4a4bef3e00fce9e89f1514b21e1c4812  win-x64/node_pdb.zip
f2898d8356128272ed7bdc7e7edb9d86ed5a3ad9256ccfb27b8e5dd7c61c176e  win-x86/node.exe
876a8db699a1d5f5d298900b998b07d1bcd15273633c684f266718940cc211f5  win-x86/node.lib
08fc7c0e6f9f37597ac7fd7cb78d3b8e2520f098217b39641fdb8344c3916bd4  win-x86/node_pdb.7z
82d65d46fdd35b4b7ae3eab0d585f466a3d04e6192a33f329d45e3702f90db42  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQEzBAEBCAAdFiEETtd49TnjY0x3nIfG1wYoSKGrAFwFAl47+GsACgkQ1wYoSKGr
AFxEWwf+Imz8ml0R4bRl9SBUv4eE0+ocoi4sjNssSdHAVC4lf6fg7Lv/104wjgr5
lXx38gICHyuV86CxUXsbI5a2U3e5V6BYVYf37a3XcJfqGnqqSI2HkbDn2aBCG0dF
WUOWs9eYau8zMeS3N336DgGwbUGnoN1zPTBGK0sKXPM4QKF5fjT1LyxWGjjuikLH
1JvrDM2L9RBV6YHzdmmgxUp2TEjFhhepHq/pkp6hQdMmvIx8UnzmAmn1aPKIbg81
iJftuqFjinbK4NumA2oXNwiBnwJdxAgc47Nmo5D4bcX2rFQEkfcbhg6por5If94k
jMVV+QOMhokVF4cM4dzuJJfcGjGIrw==
=NojX
-----END PGP SIGNATURE-----

```
