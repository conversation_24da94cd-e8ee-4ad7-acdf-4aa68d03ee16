---
date: '2018-09-11T05:34:54.568Z'
category: release
title: Node v8.12.0 (LTS)
layout: blog-post
author: <PERSON><PERSON>
---

### Notable Changes

- **async_hooks**:
  - rename PromiseWrap.parentId (<PERSON>) [#18633](https://github.com/nodejs/node/pull/18633)
  - remove runtime deprecation (<PERSON>) [#19517](https://github.com/nodejs/node/pull/19517)
  - deprecate unsafe emit{Before,After} (<PERSON>) [#18513](https://github.com/nodejs/node/pull/18513)
- **cluster**:
  - add cwd to cluster.settings (cjihrig) [#18399](https://github.com/nodejs/node/pull/18399)
  - support windowsHide option for workers (<PERSON>) [#17412](https://github.com/nodejs/node/pull/17412)
- **crypto**:
  - allow passing null as IV unless required (<PERSON>) [#18644](https://github.com/nodejs/node/pull/18644)
- **deps**:
  - upgrade npm to 6.4.1 (<PERSON>) [#22591](https://github.com/nodejs/node/pull/22591)
  - upgrade libuv to 1.19.2 (cjihrig) [#18918](https://github.com/nodejs/node/pull/18918)
  - Upgrade node-inspect to 1.11.5 (Jan Krems) [#21055](https://github.com/nodejs/node/pull/21055)
- **fs,net**:
  - support as and as+ flags in stringToFlags() (Sarat Addepalli) [#18801](https://github.com/nodejs/node/pull/18801)
  - emit 'ready' for fs streams and sockets (Sameer Srivastava) [#19408](https://github.com/nodejs/node/pull/19408)
- **http, http2**:
  - add options to http.createServer() (Peter Marton) [#15752](https://github.com/nodejs/node/pull/15752)-
  - add 103 Early Hints status code (Yosuke Furukawa) [#16644](https://github.com/nodejs/node/pull/16644)
  - add http fallback options to .createServer (Peter Marton) [#15752](https://github.com/nodejs/node/pull/15752)
- **n-api**:
  - take n-api out of experimental (Michael Dawson) [#19262](https://github.com/nodejs/node/pull/19262)
- **perf_hooks**:
  - add warning when too many entries in the timeline (James M Snell) [#18087](https://github.com/nodejs/node/pull/18087)
- **src**:
  - add public API for managing NodePlatform (Cheng Zhao) [#16981](https://github.com/nodejs/node/pull/16981)
  - allow --perf-(basic-)?prof in NODE_OPTIONS (Leko) [#17600](https://github.com/nodejs/node/pull/17600)
  - node internals' postmortem metadata (Matheus Marchini) [#14901](https://github.com/nodejs/node/pull/14901)
- **tls**:
  - expose Finished messages in TLSSocket (Anton Salikhmetov) [#19102](https://github.com/nodejs/node/pull/19102)
- **trace_events**:
  - add file pattern cli option (Andreas Madsen) [#18480](https://github.com/nodejs/node/pull/18480)
- **util**:
  - implement util.getSystemErrorName() (Joyee Cheung) [#18186](https://github.com/nodejs/node/pull/18186)

### Commits

- [[`b7f9334454`](https://github.com/nodejs/node/commit/b7f9334454)] - **(SEMVER-MINOR)** **async_hooks**: rename PromiseWrap.parentId (Ali Ijaz Sheikh) [#18633](https://github.com/nodejs/node/pull/18633)
- [[`373f4d6225`](https://github.com/nodejs/node/commit/373f4d6225)] - **(SEMVER-MINOR)** **async_hooks**: remove runtime deprecation (Ali Ijaz Sheikh) [#19517](https://github.com/nodejs/node/pull/19517)
- [[`daacff8584`](https://github.com/nodejs/node/commit/daacff8584)] - **(SEMVER-MINOR)** **async_hooks**: deprecate unsafe emit{Before,After} (Ali Ijaz Sheikh) [#18513](https://github.com/nodejs/node/pull/18513)
- [[`8f5e9916d1`](https://github.com/nodejs/node/commit/8f5e9916d1)] - **async_wrap**: fix memory leak in AsyncResource (Michael Dawson) [#20668](https://github.com/nodejs/node/pull/20668)
- [[`0a3ebb030e`](https://github.com/nodejs/node/commit/0a3ebb030e)] - **benchmark**: add JSStreamWrap benchmark (Anna Henningsen) [#17983](https://github.com/nodejs/node/pull/17983)
- [[`4009e3f245`](https://github.com/nodejs/node/commit/4009e3f245)] - **buffer**: fix typo in lib/buffer.js (Ujjwal Sharma) [#19126](https://github.com/nodejs/node/pull/19126)
- [[`20d805e4bc`](https://github.com/nodejs/node/commit/20d805e4bc)] - **build**: disable openssl build warnings on macos (Ben Noordhuis) [#19046](https://github.com/nodejs/node/pull/19046)
- [[`abcc9119d2`](https://github.com/nodejs/node/commit/abcc9119d2)] - **build**: fix rm commands in tarball rule (Ben Noordhuis) [#18332](https://github.com/nodejs/node/pull/18332)
- [[`0bef96094e`](https://github.com/nodejs/node/commit/0bef96094e)] - **build**: include the libuv and zlib into node (Yihong Wang) [#18383](https://github.com/nodejs/node/pull/18383)
- [[`2ec7dd4edc`](https://github.com/nodejs/node/commit/2ec7dd4edc)] - **build**: fix configure script for double-digits (Misty De Meo) [#21183](https://github.com/nodejs/node/pull/21183)
- [[`020057ade7`](https://github.com/nodejs/node/commit/020057ade7)] - **build**: make lint-ci work properly on Linux make (Rod Vagg) [#19746](https://github.com/nodejs/node/pull/19746)
- [[`18fd620606`](https://github.com/nodejs/node/commit/18fd620606)] - **build**: add node_lib_target_name to cctest deps (Daniel Bevenius) [#18576](https://github.com/nodejs/node/pull/18576)
- [[`9bd5fc2b34`](https://github.com/nodejs/node/commit/9bd5fc2b34)] - **build**: make gyp user defined variables lowercase (Daniel Bevenius) [#16238](https://github.com/nodejs/node/pull/16238)
- [[`1d90700514`](https://github.com/nodejs/node/commit/1d90700514)] - **child_process**: fix stdio sockets creation (Santiago Gimeno) [#18701](https://github.com/nodejs/node/pull/18701)
- [[`dc000a55d3`](https://github.com/nodejs/node/commit/dc000a55d3)] - **(SEMVER-MINOR)** **cluster**: add cwd to cluster.settings (cjihrig) [#18399](https://github.com/nodejs/node/pull/18399)
- [[`76805f0043`](https://github.com/nodejs/node/commit/76805f0043)] - **(SEMVER-MINOR)** **cluster**: support windowsHide option for workers (Todd Wong) [#17412](https://github.com/nodejs/node/pull/17412)
- [[`4d5cb4c8b5`](https://github.com/nodejs/node/commit/4d5cb4c8b5)] - **crypto**: use bool over int consistently (Tobias Nießen) [#19238](https://github.com/nodejs/node/pull/19238)
- [[`5a3dc37bc8`](https://github.com/nodejs/node/commit/5a3dc37bc8)] - **crypto**: Use math.h definitions of isnan and isinf (Jeroen Roovers) [#19196](https://github.com/nodejs/node/pull/19196)
- [[`fc34f5cae2`](https://github.com/nodejs/node/commit/fc34f5cae2)] - **(SEMVER-MINOR)** **crypto**: allow passing null as IV unless required (Tobias Nießen) [#18644](https://github.com/nodejs/node/pull/18644)
- [[`4f3bf0449c`](https://github.com/nodejs/node/commit/4f3bf0449c)] - **crypto**: use non-deprecated v8::Object::Set (Daniel Bevenius) [#17482](https://github.com/nodejs/node/pull/17482)
- [[`c491ac424b`](https://github.com/nodejs/node/commit/c491ac424b)] - **crypto**: remove BIO_set_shutdown (Daniel Bevenius) [#17542](https://github.com/nodejs/node/pull/17542)
- [[`f82d58db4c`](https://github.com/nodejs/node/commit/f82d58db4c)] - **(SEMVER-MINOR)** **deps**: upgrade npm to 6.4.1 (Kat Marchán) [#22591](https://github.com/nodejs/node/pull/22591)
- [[`5294919d05`](https://github.com/nodejs/node/commit/5294919d05)] - **deps**: V8: cherry-pick 9040405 from upstream (Junliang Yan) [#22375](https://github.com/nodejs/node/pull/22375)
- [[`ae63db8624`](https://github.com/nodejs/node/commit/ae63db8624)] - **deps**: backport 804a693 from upstream V8 (Matheus Marchini) [#21855](https://github.com/nodejs/node/pull/21855)
- [[`bf2daab673`](https://github.com/nodejs/node/commit/bf2daab673)] - **deps**: Upgrade node-inspect to 1.11.5 (Jan Krems) [#21055](https://github.com/nodejs/node/pull/21055)
- [[`d9ab189f55`](https://github.com/nodejs/node/commit/d9ab189f55)] - **deps**: cherry-pick b767cde1e7 from upstream V8 (Ben Noordhuis) [#19710](https://github.com/nodejs/node/pull/19710)
- [[`812b97c826`](https://github.com/nodejs/node/commit/812b97c826)] - **deps**: fix typo in openssl upgrading doc (Daniel Bevenius) [#19789](https://github.com/nodejs/node/pull/19789)
- [[`60733a7a78`](https://github.com/nodejs/node/commit/60733a7a78)] - **deps**: upgrade libuv to 1.19.2 (cjihrig) [#18918](https://github.com/nodejs/node/pull/18918)
- [[`31883368c7`](https://github.com/nodejs/node/commit/31883368c7)] - **deps**: cherry-pick 0c35b72 from upstream V8 (Gus Caplan) [#18038](https://github.com/nodejs/node/pull/18038)
- [[`74ca456af0`](https://github.com/nodejs/node/commit/74ca456af0)] - **(SEMVER-MINOR)** **deps**: upgrade npm to 6.2.0 (Kat Marchán) [#21592](https://github.com/nodejs/node/pull/21592)
- [[`ffb72f810e`](https://github.com/nodejs/node/commit/ffb72f810e)] - **deps**: cherry-pick 09b53ee from upstream V8 (Anna Henningsen) [#21767](https://github.com/nodejs/node/pull/21767)
- [[`8e0f28b8f0`](https://github.com/nodejs/node/commit/8e0f28b8f0)] - **deps**: V8: backport 49712d8a from upstream (Ali Ijaz Sheikh) [#21334](https://github.com/nodejs/node/pull/21334)
- [[`efe28b8581`](https://github.com/nodejs/node/commit/efe28b8581)] - **deps**: V8: fix bug in InternalPerformPromiseThen (Ali Ijaz Sheikh) [#21426](https://github.com/nodejs/node/pull/21426)
- [[`9aeffab452`](https://github.com/nodejs/node/commit/9aeffab452)] - **deps**: V8: cherry-pick 8361fa58 from upstream (Ali Ijaz Sheikh) [#21294](https://github.com/nodejs/node/pull/21294)
- [[`f987a512d4`](https://github.com/nodejs/node/commit/f987a512d4)] - **deps**: V8: backport b49206d from upstream (Ali Ijaz Sheikh) [#20727](https://github.com/nodejs/node/pull/20727)
- [[`185aca054e`](https://github.com/nodejs/node/commit/185aca054e)] - **deps**: float fix on node-gyp in npm tree (Myles Borins) [#21448](https://github.com/nodejs/node/pull/21448)
- [[`677236494b`](https://github.com/nodejs/node/commit/677236494b)] - **(SEMVER-MINOR)** **deps**: upgrade npm to 6.1.0 (Rebecca Turner) [#20190](https://github.com/nodejs/node/pull/20190)
- [[`e6cd7e57b3`](https://github.com/nodejs/node/commit/e6cd7e57b3)] - **deps**: V8: cherry-pick 5ebd6fcd from upstream (Ali Ijaz Sheikh) [#21269](https://github.com/nodejs/node/pull/21269)
- [[`d868eb784c`](https://github.com/nodejs/node/commit/d868eb784c)] - **deps**: V8: cherry-pick 502c6ae6 from upstream (Ali Ijaz Sheikh) [#21269](https://github.com/nodejs/node/pull/21269)
- [[`656ceea393`](https://github.com/nodejs/node/commit/656ceea393)] - **deps**: cherry-pick dbfe4a49d8 from upstream V8 (Jan Krems) [#16889](https://github.com/nodejs/node/pull/16889)
- [[`a02319368c`](https://github.com/nodejs/node/commit/a02319368c)] - **doc**: fix/add link to Android info (Vse Mozhet Byt) [#19004](https://github.com/nodejs/node/pull/19004)
- [[`cae60ca57a`](https://github.com/nodejs/node/commit/cae60ca57a)] - **doc**: add warning to assert.doesNotThrow() (Ruben Bridgewater) [#18699](https://github.com/nodejs/node/pull/18699)
- [[`7ed297d528`](https://github.com/nodejs/node/commit/7ed297d528)] - **doc**: remove warning against readable/readable.read (Rich Trott) [#19193](https://github.com/nodejs/node/pull/19193)
- [[`94d27e21ef`](https://github.com/nodejs/node/commit/94d27e21ef)] - **doc**: add inspector usage example (Ali Ijaz Sheikh) [#19172](https://github.com/nodejs/node/pull/19172)
- [[`1116d3274d`](https://github.com/nodejs/node/commit/1116d3274d)] - **doc**: make suggestion more direct in stream.md (Rich Trott) [#19124](https://github.com/nodejs/node/pull/19124)
- [[`369e1efca9`](https://github.com/nodejs/node/commit/369e1efca9)] - **doc**: remove subsystem from pull request template (Rich Trott) [#19125](https://github.com/nodejs/node/pull/19125)
- [[`d14137590e`](https://github.com/nodejs/node/commit/d14137590e)] - **doc**: remove tentativeness in pull-requests.md (Rich Trott) [#19123](https://github.com/nodejs/node/pull/19123)
- [[`e2190ad755`](https://github.com/nodejs/node/commit/e2190ad755)] - **doc**: add simple example to rename function (punteek) [#18812](https://github.com/nodejs/node/pull/18812)
- [[`d9895c4ba7`](https://github.com/nodejs/node/commit/d9895c4ba7)] - **doc**: add URL.format() example (Zeke Sikelianos) [#18888](https://github.com/nodejs/node/pull/18888)
- [[`c2978ac045`](https://github.com/nodejs/node/commit/c2978ac045)] - **doc**: update list of re-exported symbols (Richard Lau) [#19013](https://github.com/nodejs/node/pull/19013)
- [[`7f6e0b3510`](https://github.com/nodejs/node/commit/7f6e0b3510)] - **doc**: Readable unpipe on Writable error event (George Sapkin) [#18080](https://github.com/nodejs/node/pull/18080)
- [[`ce66b02f97`](https://github.com/nodejs/node/commit/ce66b02f97)] - **doc**: add RegExp Unicode Property Escapes to intl (Vse Mozhet Byt) [#19052](https://github.com/nodejs/node/pull/19052)
- [[`68e78e8e9e`](https://github.com/nodejs/node/commit/68e78e8e9e)] - **doc**: make the background section concise and improve its formality (Wilson) [#18928](https://github.com/nodejs/node/pull/18928)
- [[`dbc5bedd3e`](https://github.com/nodejs/node/commit/dbc5bedd3e)] - **doc**: add process.debugPort to doc/api/process.md (flickz) [#18716](https://github.com/nodejs/node/pull/18716)
- [[`dc6dadd585`](https://github.com/nodejs/node/commit/dc6dadd585)] - **doc**: `readable.push(undefined)` in non-object mode (陈刚) [#18283](https://github.com/nodejs/node/pull/18283)
- [[`4a795dd084`](https://github.com/nodejs/node/commit/4a795dd084)] - **doc**: improve buf.lastIndexOf() text (Rich Trott) [#19904](https://github.com/nodejs/node/pull/19904)
- [[`24a105f63f`](https://github.com/nodejs/node/commit/24a105f63f)] - **doc**: remove eu-strip from tarball (jvelezpo) [#20304](https://github.com/nodejs/node/pull/20304)
- [[`14a5dd4769`](https://github.com/nodejs/node/commit/14a5dd4769)] - **doc**: add tools/doc/README link in doc/STYLE_GUIDE (Vse Mozhet Byt) [#20071](https://github.com/nodejs/node/pull/20071)
- [[`f391181b27`](https://github.com/nodejs/node/commit/f391181b27)] - **doc**: update tools/doc/README.md (Vse Mozhet Byt) [#20047](https://github.com/nodejs/node/pull/20047)
- [[`ab559b88f6`](https://github.com/nodejs/node/commit/ab559b88f6)] - **doc**: add trivikr to collaborators (Trivikram) [#19384](https://github.com/nodejs/node/pull/19384)
- [[`98fe68fbb0`](https://github.com/nodejs/node/commit/98fe68fbb0)] - **doc**: add pronouns to readme (Teddy Katz) [#22036](https://github.com/nodejs/node/pull/22036)
- [[`274b2d2a89`](https://github.com/nodejs/node/commit/274b2d2a89)] - **doc**: remove confusing "cats" from style guide (Rich Trott) [#19246](https://github.com/nodejs/node/pull/19246)
- [[`20ee726c9c`](https://github.com/nodejs/node/commit/20ee726c9c)] - **doc**: remove superfluous adverb from style guide (Rich Trott) [#19246](https://github.com/nodejs/node/pull/19246)
- [[`b9b422abe2`](https://github.com/nodejs/node/commit/b9b422abe2)] - **doc**: add watson to collaborators (Thomas Watson) [#19234](https://github.com/nodejs/node/pull/19234)
- [[`eae80e43ae`](https://github.com/nodejs/node/commit/eae80e43ae)] - **doc**: add MoonBall to collaborators (Chen Gang) [#19109](https://github.com/nodejs/node/pull/19109)
- [[`f876887cae`](https://github.com/nodejs/node/commit/f876887cae)] - **doc**: update description of 'clientError' event (Luigi Pinca) [#18885](https://github.com/nodejs/node/pull/18885)
- [[`07e2bd4b73`](https://github.com/nodejs/node/commit/07e2bd4b73)] - **doc**: remove CII badge in README (Roman Reiss) [#18908](https://github.com/nodejs/node/pull/18908)
- [[`8fad7affd9`](https://github.com/nodejs/node/commit/8fad7affd9)] - **doc**: fix nits in tools/doc/README.md (Vse Mozhet Byt) [#18874](https://github.com/nodejs/node/pull/18874)
- [[`a1902caf09`](https://github.com/nodejs/node/commit/a1902caf09)] - **doc**: improved documentation for fs.unlink() (dustinnewman98) [#18843](https://github.com/nodejs/node/pull/18843)
- [[`8c5ad68add`](https://github.com/nodejs/node/commit/8c5ad68add)] - **doc**: fix broken link in pull-requests.md (Justin Lee) [#18873](https://github.com/nodejs/node/pull/18873)
- [[`399ba4b8d8`](https://github.com/nodejs/node/commit/399ba4b8d8)] - **doc**: mark accessing IPC channel fd as undefined (Bartosz Sosnowski) [#17545](https://github.com/nodejs/node/pull/17545)
- [[`2cbeea0926`](https://github.com/nodejs/node/commit/2cbeea0926)] - **doc**: add Yihong Wang to collaborators (Yihong Wang) [#18824](https://github.com/nodejs/node/pull/18824)
- [[`f57c53c811`](https://github.com/nodejs/node/commit/f57c53c811)] - **doc**: add missing metadata for fs.open (Tobias Nießen) [#19585](https://github.com/nodejs/node/pull/19585)
- [[`ebd73ad27a`](https://github.com/nodejs/node/commit/ebd73ad27a)] - **doc**: activate `no-multiple-empty-lines` rule (Ruben Bridgewater) [#18747](https://github.com/nodejs/node/pull/18747)
- [[`adca631f8a`](https://github.com/nodejs/node/commit/adca631f8a)] - **doc**: note that linting is required in releases.md (Gibson Fahnestock) [#18776](https://github.com/nodejs/node/pull/18776)
- [[`a5ee6eeea7`](https://github.com/nodejs/node/commit/a5ee6eeea7)] - **doc**: remove extra space in README.md (Matheus Marchini) [#18822](https://github.com/nodejs/node/pull/18822)
- [[`9c52231a05`](https://github.com/nodejs/node/commit/9c52231a05)] - **doc**: update crypo Certficate class. (Antoine AMARA) [#18721](https://github.com/nodejs/node/pull/18721)
- [[`a26454ea32`](https://github.com/nodejs/node/commit/a26454ea32)] - **doc**: add error check to fs example (Evan Lucas) [#18681](https://github.com/nodejs/node/pull/18681)
- [[`531cb6238d`](https://github.com/nodejs/node/commit/531cb6238d)] - **doc**: add missing metadata for settings.windowsHide (Tobias Nießen) [#19578](https://github.com/nodejs/node/pull/19578)
- [[`bb85fd6f5b`](https://github.com/nodejs/node/commit/bb85fd6f5b)] - **doc**: add missing metadata for cluster.settings.cwd (Tobias Nießen) [#19569](https://github.com/nodejs/node/pull/19569)
- [[`4709734cfc`](https://github.com/nodejs/node/commit/4709734cfc)] - **doc**: cleanup n-api.md doc (Michael Dawson) [#20430](https://github.com/nodejs/node/pull/20430)
- [[`e1a7244fbd`](https://github.com/nodejs/node/commit/e1a7244fbd)] - **doc**: Uint8Array support in Buffer functions (SheetJS) [#19949](https://github.com/nodejs/node/pull/19949)
- [[`3ad5e30e05`](https://github.com/nodejs/node/commit/3ad5e30e05)] - **doc**: remove ES6/ECMAScript 2015 from buffer.md (Rich Trott) [#19685](https://github.com/nodejs/node/pull/19685)
- [[`41bb1107cf`](https://github.com/nodejs/node/commit/41bb1107cf)] - **doc**: Uint8Array support in Buffer functions (SheetJS) [#19949](https://github.com/nodejs/node/pull/19949)
- [[`cf0577eef2`](https://github.com/nodejs/node/commit/cf0577eef2)] - **doc**: remove ES6/ECMAScript 2015 from buffer.md (Rich Trott) [#19685](https://github.com/nodejs/node/pull/19685)
- [[`fceeee616b`](https://github.com/nodejs/node/commit/fceeee616b)] - **doc**: Update tools/icu/README.md (Steven R. Loomis) [#16939](https://github.com/nodejs/node/pull/16939)
- [[`52f5829cdb`](https://github.com/nodejs/node/commit/52f5829cdb)] - **doc**: fix typo in http2.md (Vse Mozhet Byt) [#18872](https://github.com/nodejs/node/pull/18872)
- [[`50316e2021`](https://github.com/nodejs/node/commit/50316e2021)] - **doc,tools**: formalize, unify, codify default values (Vse Mozhet Byt) [#19737](https://github.com/nodejs/node/pull/19737)
- [[`98f5b17ee1`](https://github.com/nodejs/node/commit/98f5b17ee1)] - **errors**: make message non-enumerable (Ruben Bridgewater) [#19719](https://github.com/nodejs/node/pull/19719)
- [[`9dc1f509f1`](https://github.com/nodejs/node/commit/9dc1f509f1)] - **errors**: move error creation helpers to errors.js (Joyee Cheung) [#18546](https://github.com/nodejs/node/pull/18546)
- [[`9696bf920f`](https://github.com/nodejs/node/commit/9696bf920f)] - **errors**: lazy load util in internal/errors.js (Joyee Cheung) [#18358](https://github.com/nodejs/node/pull/18358)
- [[`e25d5d077d`](https://github.com/nodejs/node/commit/e25d5d077d)] - **(SEMVER-MINOR)** **fs**: support as and as+ flags in stringToFlags() (Sarat Addepalli) [#18801](https://github.com/nodejs/node/pull/18801)
- [[`35a1bd97ba`](https://github.com/nodejs/node/commit/35a1bd97ba)] - **(SEMVER-MINOR)** **fs,net**: emit 'ready' for fs streams and sockets (Sameer Srivastava) [#19408](https://github.com/nodejs/node/pull/19408)
- [[`68a810cd85`](https://github.com/nodejs/node/commit/68a810cd85)] - **http**: prevent aborted event when already completed (Andrew Johnston) [#18999](https://github.com/nodejs/node/pull/18999)
- [[`c4fa1f72a2`](https://github.com/nodejs/node/commit/c4fa1f72a2)] - **http**: prevent aborted event when already completed (Andrew Johnston) [#18999](https://github.com/nodejs/node/pull/18999)
- [[`1fc00f0821`](https://github.com/nodejs/node/commit/1fc00f0821)] - **http**: do not rely on the 'agentRemove' event (Luigi Pinca) [#20786](https://github.com/nodejs/node/pull/20786)
- [[`e094275799`](https://github.com/nodejs/node/commit/e094275799)] - **http**: simplify parser lifetime tracking (Anna Henningsen) [#18135](https://github.com/nodejs/node/pull/18135)
- [[`01dc646382`](https://github.com/nodejs/node/commit/01dc646382)] - **(SEMVER-MINOR)** **http**: add options to http.createServer() (Peter Marton) [#15752](https://github.com/nodejs/node/pull/15752)
- [[`7c43099d1e`](https://github.com/nodejs/node/commit/7c43099d1e)] - **(SEMVER-MINOR)** **http, http2**: add 103 Early Hints status code (Yosuke Furukawa) [#16644](https://github.com/nodejs/node/pull/16644)
- [[`87818dc8bc`](https://github.com/nodejs/node/commit/87818dc8bc)] - **http2**: destroy the socket properly and add tests (Mathias Buus) [#19852](https://github.com/nodejs/node/pull/19852)
- [[`de51a83e58`](https://github.com/nodejs/node/commit/de51a83e58)] - **http2**: remove unused using declarations node_http2 (Daniel Bevenius) [#20420](https://github.com/nodejs/node/pull/20420)
- [[`a29cd25b41`](https://github.com/nodejs/node/commit/a29cd25b41)] - **http2**: refer to stream errors by name (Anna Henningsen) [#18966](https://github.com/nodejs/node/pull/18966)
- [[`06329a8eaf`](https://github.com/nodejs/node/commit/06329a8eaf)] - **http2**: remove duplicate words in comments (Tobias Nießen) [#17939](https://github.com/nodejs/node/pull/17939)
- [[`955080f7ee`](https://github.com/nodejs/node/commit/955080f7ee)] - **http2**: pass session to DEBUG_HTTP2SESSION2 (Daniel Bevenius) [#20815](https://github.com/nodejs/node/pull/20815)
- [[`b1b0486049`](https://github.com/nodejs/node/commit/b1b0486049)] - **http2**: add req and res options to server creation (Peter Marton) [#15560](https://github.com/nodejs/node/pull/15560)
- [[`3f78847e0e`](https://github.com/nodejs/node/commit/3f78847e0e)] - **(SEMVER-MINOR)** **http2**: add http fallback options to .createServer (Peter Marton) [#15752](https://github.com/nodejs/node/pull/15752)
- [[`cf833e4901`](https://github.com/nodejs/node/commit/cf833e4901)] - **lib**: change hook -\> hooks in code comment (Daniel Bevenius) [#19053](https://github.com/nodejs/node/pull/19053)
- [[`29b5d3999e`](https://github.com/nodejs/node/commit/29b5d3999e)] - **lib**: re-fix v8_prof_processor (Anna Henningsen) [#19059](https://github.com/nodejs/node/pull/19059)
- [[`2702fd779e`](https://github.com/nodejs/node/commit/2702fd779e)] - **lib**: replace `eval` with `vm.runInThisContext` (Myles Borins) [#18623](https://github.com/nodejs/node/pull/18623)
- [[`7e23946c87`](https://github.com/nodejs/node/commit/7e23946c87)] - **lib**: provide proper deprecation code (Ruben Bridgewater) [#18694](https://github.com/nodejs/node/pull/18694)
- [[`7c6e391419`](https://github.com/nodejs/node/commit/7c6e391419)] - **lib, src**: use process.config instead of regex (Jon Moss) [#17814](https://github.com/nodejs/node/pull/17814)
- [[`0f83f251fe`](https://github.com/nodejs/node/commit/0f83f251fe)] - **module**: enable dynamic import flag for esmodules (Myles Borins) [#18387](https://github.com/nodejs/node/pull/18387)
- [[`d7192c4e6a`](https://github.com/nodejs/node/commit/d7192c4e6a)] - **module**: Set dynamic import callback (Jan Krems) [#15713](https://github.com/nodejs/node/pull/15713)
- [[`35a8ff7e55`](https://github.com/nodejs/node/commit/35a8ff7e55)] - **n-api**: create functions directly (Gabriel Schulhof) [#21688](https://github.com/nodejs/node/pull/21688)
- [[`7033bbaa01`](https://github.com/nodejs/node/commit/7033bbaa01)] - **n-api**: throw when entry point is null (Gabriel Schulhof) [#20779](https://github.com/nodejs/node/pull/20779)
- [[`4911c4e9fa`](https://github.com/nodejs/node/commit/4911c4e9fa)] - **n-api**: improve runtime perf of n-api func call (Kenny Yuan) [#21072](https://github.com/nodejs/node/pull/21072)
- [[`0b2f52706d`](https://github.com/nodejs/node/commit/0b2f52706d)] - **(SEMVER-MINOR)** **n-api**: take n-api out of experimental (Michael Dawson) [#19262](https://github.com/nodejs/node/pull/19262)
- [[`4a267f0e3c`](https://github.com/nodejs/node/commit/4a267f0e3c)] - **net**: simplify net.Socket#end() (Anna Henningsen) [#18708](https://github.com/nodejs/node/pull/18708)
- [[`3d38bab64e`](https://github.com/nodejs/node/commit/3d38bab64e)] - **net**: use `_final` instead of `on('finish')` (Anna Henningsen) [#18608](https://github.com/nodejs/node/pull/18608)
- [[`1a1288d03c`](https://github.com/nodejs/node/commit/1a1288d03c)] - **perf_hooks**: fix timing (Timothy Gu) [#18993](https://github.com/nodejs/node/pull/18993)
- [[`b4192b007b`](https://github.com/nodejs/node/commit/b4192b007b)] - **(SEMVER-MINOR)** **perf_hooks**: add warning when too many entries in the timeline (James M Snell) [#18087](https://github.com/nodejs/node/pull/18087)
- [[`68d33c692e`](https://github.com/nodejs/node/commit/68d33c692e)] - **perf_hooks**: fix scheduling regression (Anatoli Papirovski) [#18051](https://github.com/nodejs/node/pull/18051)
- [[`711098e88c`](https://github.com/nodejs/node/commit/711098e88c)] - **(SEMVER-MINOR)** **process**: Send signal name to signal handlers (Robert Rossmann) [#15606](https://github.com/nodejs/node/pull/15606)
- [[`2ec981b078`](https://github.com/nodejs/node/commit/2ec981b078)] - **process**: use more direct sync I/O for stdio (Anna Henningsen) [#18019](https://github.com/nodejs/node/pull/18019)
- [[`a6fca750be`](https://github.com/nodejs/node/commit/a6fca750be)] - **repl**: better handling of recoverable errors (Prince J Wesley) [#18915](https://github.com/nodejs/node/pull/18915)
- [[`66343c546c`](https://github.com/nodejs/node/commit/66343c546c)] - **(SEMVER-MINOR)** **src**: add environment cleanup hooks (Anna Henningsen) [#19377](https://github.com/nodejs/node/pull/19377)
- [[`f33f3238f9`](https://github.com/nodejs/node/commit/f33f3238f9)] - **src**: #include \<stdio.h\>" to iculslocs (Steven R. Loomis) [#19150](https://github.com/nodejs/node/pull/19150)
- [[`02ea033e05`](https://github.com/nodejs/node/commit/02ea033e05)] - **src**: fix error message in async_hooks constructor (Daniel Bevenius) [#19000](https://github.com/nodejs/node/pull/19000)
- [[`d478bc7375`](https://github.com/nodejs/node/commit/d478bc7375)] - **src**: fix bootstrap_node on bsd (sylkat) [#22663](https://github.com/nodejs/node/pull/22663)
- [[`cbe92390c1`](https://github.com/nodejs/node/commit/cbe92390c1)] - **src**: use `DoTryWrite()` for not-all-Buffer writev()s too (Anna Henningsen) [#18019](https://github.com/nodejs/node/pull/18019)
- [[`69efa9f6b3`](https://github.com/nodejs/node/commit/69efa9f6b3)] - **src**: remove node namespace qualifiers (Daniel Bevenius) [#18962](https://github.com/nodejs/node/pull/18962)
- [[`8af6b75e10`](https://github.com/nodejs/node/commit/8af6b75e10)] - **(SEMVER-MINOR)** **src**: add public API for managing NodePlatform (Cheng Zhao) [#16981](https://github.com/nodejs/node/pull/16981)
- [[`e194c3782b`](https://github.com/nodejs/node/commit/e194c3782b)] - **src**: fix deprecation warning in node_perf.cc (Daniel Bevenius) [#18877](https://github.com/nodejs/node/pull/18877)
- [[`161869ece0`](https://github.com/nodejs/node/commit/161869ece0)] - **(SEMVER-MINOR)** **src**: allow --perf-(basic-)?prof in NODE_OPTIONS (Leko) [#17600](https://github.com/nodejs/node/pull/17600)
- [[`eaf99d9393`](https://github.com/nodejs/node/commit/eaf99d9393)] - **src**: add node_encoding.cc (James M Snell) [#21112](https://github.com/nodejs/node/pull/21112)
- [[`0321afed4c`](https://github.com/nodejs/node/commit/0321afed4c)] - **src**: add node_process.cc (James M Snell) [#21105](https://github.com/nodejs/node/pull/21105)
- [[`54ea1ccf2d`](https://github.com/nodejs/node/commit/54ea1ccf2d)] - **src**: refactor bootstrap to use bootstrap object (James M Snell) [#20917](https://github.com/nodejs/node/pull/20917)
- [[`6f545d1902`](https://github.com/nodejs/node/commit/6f545d1902)] - **src**: fix compiler warning in process.ppid (cjihrig) [#16958](https://github.com/nodejs/node/pull/16958)
- [[`9125e2b6fa`](https://github.com/nodejs/node/commit/9125e2b6fa)] - **src**: add convenience ctor for async trigger id scope (Anna Henningsen) [#19204](https://github.com/nodejs/node/pull/19204)
- [[`2ee4bb7826`](https://github.com/nodejs/node/commit/2ee4bb7826)] - **src**: move `Environment` ctor/dtor into env.cc (Anna Henningsen) [#19202](https://github.com/nodejs/node/pull/19202)
- [[`342dbff852`](https://github.com/nodejs/node/commit/342dbff852)] - **src**: make `AsyncResource` destructor virtual (Anna Henningsen) [#20633](https://github.com/nodejs/node/pull/20633)
- [[`b916620bf5`](https://github.com/nodejs/node/commit/b916620bf5)] - **src**: fix typo in util.h comment (Anna Henningsen) [#20656](https://github.com/nodejs/node/pull/20656)
- [[`8076a793ed`](https://github.com/nodejs/node/commit/8076a793ed)] - **src**: fix nullptr dereference for signal during startup (Anna Henningsen) [#20637](https://github.com/nodejs/node/pull/20637)
- [[`1cb9772a40`](https://github.com/nodejs/node/commit/1cb9772a40)] - **src**: remove unused freelist.h header (Anna Henningsen) [#20544](https://github.com/nodejs/node/pull/20544)
- [[`e17f05a817`](https://github.com/nodejs/node/commit/e17f05a817)] - **src**: create per-isolate strings after platform setup (Ulan Degenbaev) [#20175](https://github.com/nodejs/node/pull/20175)
- [[`d38ccbb07f`](https://github.com/nodejs/node/commit/d38ccbb07f)] - **src**: use `unordered_map` for perf marks (Anna Henningsen) [#19558](https://github.com/nodejs/node/pull/19558)
- [[`553e34ef9c`](https://github.com/nodejs/node/commit/553e34ef9c)] - **src**: simplify http2 perf tracking code (Anna Henningsen) [#19470](https://github.com/nodejs/node/pull/19470)
- [[`67182912d7`](https://github.com/nodejs/node/commit/67182912d7)] - **src**: add "icu::" prefix before ICU symbols (Steven R. Loomis)
- [[`2cf263519a`](https://github.com/nodejs/node/commit/2cf263519a)] - **src**: use unique_ptr for scheduled delayed tasks (Franziska Hinkelmann) [#17083](https://github.com/nodejs/node/pull/17083)
- [[`2148b1921e`](https://github.com/nodejs/node/commit/2148b1921e)] - **src**: use unique_ptr in platform implementation (Franziska Hinkelmann) [#16970](https://github.com/nodejs/node/pull/16970)
- [[`e9327541e1`](https://github.com/nodejs/node/commit/e9327541e1)] - **src**: cancel pending delayed platform tasks on exit (Anna Henningsen) [#16700](https://github.com/nodejs/node/pull/16700)
- [[`bf8068e6f9`](https://github.com/nodejs/node/commit/bf8068e6f9)] - **src**: prepare v8 platform for multi-isolate support (Anna Henningsen) [#16700](https://github.com/nodejs/node/pull/16700)
- [[`59f13304e1`](https://github.com/nodejs/node/commit/59f13304e1)] - **src**: refactor callback #defines into C++ templates (Anna Henningsen) [#18133](https://github.com/nodejs/node/pull/18133)
- [[`a8d2ab50fc`](https://github.com/nodejs/node/commit/a8d2ab50fc)] - **src**: rename `On*` -\> `Emit*` for stream callbacks (Anna Henningsen) [#17701](https://github.com/nodejs/node/pull/17701)
- [[`15c4717e0a`](https://github.com/nodejs/node/commit/15c4717e0a)] - **src**: harden JSStream callbacks (Anna Henningsen) [#18028](https://github.com/nodejs/node/pull/18028)
- [[`5ea1492b74`](https://github.com/nodejs/node/commit/5ea1492b74)] - **src**: fix code coverage cleanup (Michael Dawson) [#18081](https://github.com/nodejs/node/pull/18081)
- [[`0d2a720c70`](https://github.com/nodejs/node/commit/0d2a720c70)] - **src**: update make for new code coverage locations (Michael Dawson) [#17987](https://github.com/nodejs/node/pull/17987)
- [[`2c6f482ba2`](https://github.com/nodejs/node/commit/2c6f482ba2)] - **src**: remove duplicate words in comments (Tobias Nießen) [#17939](https://github.com/nodejs/node/pull/17939)
- [[`7fa97d4f09`](https://github.com/nodejs/node/commit/7fa97d4f09)] - **src**: make FSEventWrap/StatWatcher::Start more robust (Timothy Gu) [#17432](https://github.com/nodejs/node/pull/17432)
- [[`c39b0020b5`](https://github.com/nodejs/node/commit/c39b0020b5)] - **src**: expose uv.errmap to binding (Joyee Cheung) [#17338](https://github.com/nodejs/node/pull/17338)
- [[`75b456d0b8`](https://github.com/nodejs/node/commit/75b456d0b8)] - **src**: do not redefine private for GenDebugSymbols (Joyee Cheung) [#18653](https://github.com/nodejs/node/pull/18653)
- [[`7cf26e5813`](https://github.com/nodejs/node/commit/7cf26e5813)] - **src**: remove superfluous check in backtrace_posix.cc (Anna Henningsen) [#16950](https://github.com/nodejs/node/pull/16950)
- [[`0564454b75`](https://github.com/nodejs/node/commit/0564454b75)] - **(SEMVER-MINOR)** **src, test**: node internals' postmortem metadata (Matheus Marchini) [#14901](https://github.com/nodejs/node/pull/14901)
- [[`c92d66a749`](https://github.com/nodejs/node/commit/c92d66a749)] - **stream**: delete redundant code (陈刚) [#18145](https://github.com/nodejs/node/pull/18145)
- [[`af27768df4`](https://github.com/nodejs/node/commit/af27768df4)] - **stream**: delete redundant code (陈刚) [#18145](https://github.com/nodejs/node/pull/18145)
- [[`9c781f041d`](https://github.com/nodejs/node/commit/9c781f041d)] - **test**: fix test-abort-backtrace in shared lib build (Yihong Wang) [#19213](https://github.com/nodejs/node/pull/19213)
- [[`a0fd0b69eb`](https://github.com/nodejs/node/commit/a0fd0b69eb)] - **test**: Remove unnecessary asserion messages in test-crypto-hash.js (Piotr Grzesik) [#18984](https://github.com/nodejs/node/pull/18984)
- [[`e6131c2687`](https://github.com/nodejs/node/commit/e6131c2687)] - **test**: do not check text for engine-generated error (Rich Trott) [#19215](https://github.com/nodejs/node/pull/19215)
- [[`8adb0b37bd`](https://github.com/nodejs/node/commit/8adb0b37bd)] - **test**: address unreliable test-performance (Rich Trott) [#19228](https://github.com/nodejs/node/pull/19228)
- [[`e1e6a0965e`](https://github.com/nodejs/node/commit/e1e6a0965e)] - **test**: refactor http-https-default-ports (Ken Lin) [#19130](https://github.com/nodejs/node/pull/19130)
- [[`d3a10c70f1`](https://github.com/nodejs/node/commit/d3a10c70f1)] - **test**: skip postmortem metadata test when nm fails (Joyee Cheung) [#19107](https://github.com/nodejs/node/pull/19107)
- [[`f64100a7df`](https://github.com/nodejs/node/commit/f64100a7df)] - **test**: add more information to assert.strictEqual (Ujjwal Sharma) [#19162](https://github.com/nodejs/node/pull/19162)
- [[`21be1279dd`](https://github.com/nodejs/node/commit/21be1279dd)] - **test**: move require http2 to after crypto check (Daniel Bevenius) [#19111](https://github.com/nodejs/node/pull/19111)
- [[`61dd5b73b5`](https://github.com/nodejs/node/commit/61dd5b73b5)] - **test**: specify 'dir' for directory symlinks (Kyle Farnung) [#19049](https://github.com/nodejs/node/pull/19049)
- [[`85aa639440`](https://github.com/nodejs/node/commit/85aa639440)] - **test**: refactor test after review (Andrew Johnston) [#18999](https://github.com/nodejs/node/pull/18999)
- [[`01e2eba0c7`](https://github.com/nodejs/node/commit/01e2eba0c7)] - **test**: move test-timers-throw-reschedule to sequential (Myles Borins) [#22379](https://github.com/nodejs/node/pull/22379)
- [[`812c950691`](https://github.com/nodejs/node/commit/812c950691)] - **test**: rename test-regress-GH-877.js (Ujjwal Sharma) [#19161](https://github.com/nodejs/node/pull/19161)
- [[`67bdb35e0f`](https://github.com/nodejs/node/commit/67bdb35e0f)] - **test**: rename test-regress-GH-784.js (Ujjwal Sharma) [#19161](https://github.com/nodejs/node/pull/19161)
- [[`37936304e8`](https://github.com/nodejs/node/commit/37936304e8)] - **test**: address nits and rename the corresponding fixture (Ujjwal Sharma) [#19161](https://github.com/nodejs/node/pull/19161)
- [[`d76a96c6ad`](https://github.com/nodejs/node/commit/d76a96c6ad)] - **test**: rename tests to remove "regress" keyword (Ujjwal Sharma) [#19161](https://github.com/nodejs/node/pull/19161)
- [[`b70dd35ba4`](https://github.com/nodejs/node/commit/b70dd35ba4)] - **test**: rename test-regress-GH-4027 (Ujjwal Sharma) [#19161](https://github.com/nodejs/node/pull/19161)
- [[`6092060227`](https://github.com/nodejs/node/commit/6092060227)] - **test**: rename test-regress-GH-4015 (Ujjwal Sharma) [#19161](https://github.com/nodejs/node/pull/19161)
- [[`6417564b29`](https://github.com/nodejs/node/commit/6417564b29)] - **test**: rename test-regress-GH-1697 (Ujjwal Sharma) [#19161](https://github.com/nodejs/node/pull/19161)
- [[`20a4ec88a0`](https://github.com/nodejs/node/commit/20a4ec88a0)] - **test**: rename test-regress-GH-1726 (Ujjwal Sharma) [#19161](https://github.com/nodejs/node/pull/19161)
- [[`d7b657fceb`](https://github.com/nodejs/node/commit/d7b657fceb)] - **test**: refactor test-async-wrap-getasyncid (Santiago Gimeno) [#18727](https://github.com/nodejs/node/pull/18727)
- [[`905c350147`](https://github.com/nodejs/node/commit/905c350147)] - **test**: remove assert message and add block scope (wuweiweiwu) [#19054](https://github.com/nodejs/node/pull/19054)
- [[`7d0f02e48d`](https://github.com/nodejs/node/commit/7d0f02e48d)] - **test**: fix flaky inspector-stop-profile-after-done (Rich Trott) [#18126](https://github.com/nodejs/node/pull/18126)
- [[`4b4383918f`](https://github.com/nodejs/node/commit/4b4383918f)] - **test**: http2 compat response.write() error checks (Trivikram) [#18859](https://github.com/nodejs/node/pull/18859)
- [[`78e79c7f7d`](https://github.com/nodejs/node/commit/78e79c7f7d)] - **test**: fix deprecation warning in binding.cc (Daniel Bevenius) [#18877](https://github.com/nodejs/node/pull/18877)
- [[`2f6866e1d9`](https://github.com/nodejs/node/commit/2f6866e1d9)] - **test**: check symbols in shared lib (Yihong Wang) [#18806](https://github.com/nodejs/node/pull/18806)
- [[`a8ae04d528`](https://github.com/nodejs/node/commit/a8ae04d528)] - **test**: http2 client ping errors (Trivikram) [#18849](https://github.com/nodejs/node/pull/18849)
- [[`901f5799f3`](https://github.com/nodejs/node/commit/901f5799f3)] - **test**: http2 client settings invalid callback (Trivikram) [#18850](https://github.com/nodejs/node/pull/18850)
- [[`cd44b82f52`](https://github.com/nodejs/node/commit/cd44b82f52)] - **test**: http2 client operations after destroy (Trivikram) [#18845](https://github.com/nodejs/node/pull/18845)
- [[`ffa7b50eef`](https://github.com/nodejs/node/commit/ffa7b50eef)] - **test**: refactor parallel/test-tls-pause (juggernaut451) [#18714](https://github.com/nodejs/node/pull/18714)
- [[`83e704d396`](https://github.com/nodejs/node/commit/83e704d396)] - **test**: stdio pipe behavior tests (Bartosz Sosnowski) [#18614](https://github.com/nodejs/node/pull/18614)
- [[`488e1bbe81`](https://github.com/nodejs/node/commit/488e1bbe81)] - **test**: refactor parallel/test-tls-0-dns-altname (juggernaut451) [#18803](https://github.com/nodejs/node/pull/18803)
- [[`35e691cb40`](https://github.com/nodejs/node/commit/35e691cb40)] - **test**: refactor parallel/test-tls-addca (juggernaut451) [#18798](https://github.com/nodejs/node/pull/18798)
- [[`f534bd889a`](https://github.com/nodejs/node/commit/f534bd889a)] - **test**: make tls test more rigorous (Ben Noordhuis) [#18792](https://github.com/nodejs/node/pull/18792)
- [[`ecf3616f51`](https://github.com/nodejs/node/commit/ecf3616f51)] - **test**: reduce benchmark test run time (juggernaut451) [#18787](https://github.com/nodejs/node/pull/18787)
- [[`29009aec96`](https://github.com/nodejs/node/commit/29009aec96)] - **test**: try to connect after server was closed (Leko) [#18257](https://github.com/nodejs/node/pull/18257)
- [[`d0083cbccd`](https://github.com/nodejs/node/commit/d0083cbccd)] - **test**: wrap countdown callback in common.mustCall (Bamieh) [#18506](https://github.com/nodejs/node/pull/18506)
- [[`0977f042e6`](https://github.com/nodejs/node/commit/0977f042e6)] - **test**: add lib path env when node_shared=true (Yihong Wang) [#18626](https://github.com/nodejs/node/pull/18626)
- [[`11695907e1`](https://github.com/nodejs/node/commit/11695907e1)] - **test**: add multiline repl input regression test (cjihrig) [#18718](https://github.com/nodejs/node/pull/18718)
- [[`0b6ab530c1`](https://github.com/nodejs/node/commit/0b6ab530c1)] - **test**: add crypto check to test-benchmark-tls (Daniel Bevenius) [#18724](https://github.com/nodejs/node/pull/18724)
- [[`6bc307ff32`](https://github.com/nodejs/node/commit/6bc307ff32)] - **test**: add useful info to error msg and refactor (Chin Huang) [#18541](https://github.com/nodejs/node/pull/18541)
- [[`1654b12037`](https://github.com/nodejs/node/commit/1654b12037)] - **test**: fix missing param in benchmark-timers (Anatoli Papirovski) [#18734](https://github.com/nodejs/node/pull/18734)
- [[`bbc7443724`](https://github.com/nodejs/node/commit/bbc7443724)] - **test**: fix and improve error message (Kevin Caulfield) [#18449](https://github.com/nodejs/node/pull/18449)
- [[`4eca50a2a0`](https://github.com/nodejs/node/commit/4eca50a2a0)] - **test**: fix flaky repl-timeout-throw (Santiago Gimeno) [#18692](https://github.com/nodejs/node/pull/18692)
- [[`3cdae1541b`](https://github.com/nodejs/node/commit/3cdae1541b)] - **test**: remove NodeTestFixture from Env constructor (Daniel Bevenius) [#18558](https://github.com/nodejs/node/pull/18558)
- [[`25ce5c3852`](https://github.com/nodejs/node/commit/25ce5c3852)] - **test**: introduce SetUpTestCase/TearDownTestCase (Daniel Bevenius) [#18558](https://github.com/nodejs/node/pull/18558)
- [[`e98efcfa6a`](https://github.com/nodejs/node/commit/e98efcfa6a)] - **test**: replace assert.equal with assert.strictEqual (Sho Miyamoto) [#18119](https://github.com/nodejs/node/pull/18119)
- [[`9a9ea0d756`](https://github.com/nodejs/node/commit/9a9ea0d756)] - **test**: bypass dns for IPv6 net tests (Refael Ackermann) [#16976](https://github.com/nodejs/node/pull/16976)
- [[`d0588f151d`](https://github.com/nodejs/node/commit/d0588f151d)] - **test**: fix flaky http-client-timeout-agent (Santiago Gimeno) [#19856](https://github.com/nodejs/node/pull/19856)
- [[`1e25f00353`](https://github.com/nodejs/node/commit/1e25f00353)] - **test**: move http-client-timeout-agent to sequential (Rich Trott) [#19809](https://github.com/nodejs/node/pull/19809)
- [[`0517cd8504`](https://github.com/nodejs/node/commit/0517cd8504)] - **test**: fix test-cluster-send-handle-large-payload (Rich Trott) [#19311](https://github.com/nodejs/node/pull/19311)
- [[`8053474679`](https://github.com/nodejs/node/commit/8053474679)] - **test**: add http2/tls destroy regression test (Anna Henningsen) [#21598](https://github.com/nodejs/node/pull/21598)
- [[`ed0d939dbc`](https://github.com/nodejs/node/commit/ed0d939dbc)] - **test**: remove --harmony-sharedarraybuffer usage (Ben Smith) [#16343](https://github.com/nodejs/node/pull/16343)
- [[`0f45ecb68c`](https://github.com/nodejs/node/commit/0f45ecb68c)] - **test**: add http \_dump regression test (Anna Henningsen) [#21595](https://github.com/nodejs/node/pull/21595)
- [[`6d3cbcbb13`](https://github.com/nodejs/node/commit/6d3cbcbb13)] - **test**: make test-error-reporting engine agnostic (Rich Trott) [#16272](https://github.com/nodejs/node/pull/16272)
- [[`b1110b22b4`](https://github.com/nodejs/node/commit/b1110b22b4)] - **test**: fix test when NODE_OPTIONS env var is set to --trace-warnings (Ashok) [#20027](https://github.com/nodejs/node/pull/20027)
- [[`f0f44f69a6`](https://github.com/nodejs/node/commit/f0f44f69a6)] - **test**: check TTY mode reset on exit (Anna Henningsen) [#21027](https://github.com/nodejs/node/pull/21027)
- [[`71ee19e064`](https://github.com/nodejs/node/commit/71ee19e064)] - **test**: plug AliasedBuffer cctest memory leak (Anna Henningsen) [#20665](https://github.com/nodejs/node/pull/20665)
- [[`3c6464a4f4`](https://github.com/nodejs/node/commit/3c6464a4f4)] - **test**: add regression test for large write (Anna Henningsen) [#19551](https://github.com/nodejs/node/pull/19551)
- [[`21cdb73d67`](https://github.com/nodejs/node/commit/21cdb73d67)] - **test**: allow running with `NODE_PENDING_DEPRECATION` (Anna Henningsen) [#18991](https://github.com/nodejs/node/pull/18991)
- [[`ad862a0114`](https://github.com/nodejs/node/commit/ad862a0114)] - **test**: properly tag anonymous namespaces (Michael Dawson) [#18583](https://github.com/nodejs/node/pull/18583)
- [[`1942440696`](https://github.com/nodejs/node/commit/1942440696)] - **test**: refactor test-repl (Anna Henningsen) [#17926](https://github.com/nodejs/node/pull/17926)
- [[`7d263ff708`](https://github.com/nodejs/node/commit/7d263ff708)] - **test**: fix unreliable async-hooks/test-signalwrap (Rich Trott) [#17827](https://github.com/nodejs/node/pull/17827)
- [[`fa6f808c71`](https://github.com/nodejs/node/commit/fa6f808c71)] - **test**: add test for postmortem metadata validation (cjihrig) [#17685](https://github.com/nodejs/node/pull/17685)
- [[`88c4adfdde`](https://github.com/nodejs/node/commit/88c4adfdde)] - **test**: remove test case 0 from tls-cnnic-whitelist (Daniel Bevenius) [#19767](https://github.com/nodejs/node/pull/19767)
- [[`64b4ea47ed`](https://github.com/nodejs/node/commit/64b4ea47ed)] - **test**: set clientOpts.port property (Daniel Bevenius) [#19767](https://github.com/nodejs/node/pull/19767)
- [[`b7564c48dd`](https://github.com/nodejs/node/commit/b7564c48dd)] - **test**: fix cctest -Wunused-variable warning (Ben Noordhuis) [#18530](https://github.com/nodejs/node/pull/18530)
- [[`d55e4adc3d`](https://github.com/nodejs/node/commit/d55e4adc3d)] - **test,benchmark,doc**: enable dot-notation rule (Ruben Bridgewater) [#18749](https://github.com/nodejs/node/pull/18749)
- [[`1f49de4b24`](https://github.com/nodejs/node/commit/1f49de4b24)] - **(SEMVER-MINOR)** **tls**: expose Finished messages in TLSSocket (Anton Salikhmetov) [#19102](https://github.com/nodejs/node/pull/19102)
- [[`1cf17df769`](https://github.com/nodejs/node/commit/1cf17df769)] - **tls**: accept array of protocols in TLSSocket (Mark S. Everitt) [#16655](https://github.com/nodejs/node/pull/16655)
- [[`8292bc3892`](https://github.com/nodejs/node/commit/8292bc3892)] - **tls**: use correct class name in deprecation message (Anna Henningsen) [#17561](https://github.com/nodejs/node/pull/17561)
- [[`c56aafd645`](https://github.com/nodejs/node/commit/c56aafd645)] - **tools**: add log output to crashes (Ruben Bridgewater) [#20295](https://github.com/nodejs/node/pull/20295)
- [[`422b6e8b9f`](https://github.com/nodejs/node/commit/422b6e8b9f)] - **tools**: show stdout/stderr for timed out tests (Rich Trott) [#20260](https://github.com/nodejs/node/pull/20260)
- [[`f8c5042454`](https://github.com/nodejs/node/commit/f8c5042454)] - **tools**: include exit code in TAP log (Refael Ackermann) [#19855](https://github.com/nodejs/node/pull/19855)
- [[`11e53cd323`](https://github.com/nodejs/node/commit/11e53cd323)] - **tools**: include exit code in test failures (Rich Trott) [#19855](https://github.com/nodejs/node/pull/19855)
- [[`246c2d18cb`](https://github.com/nodejs/node/commit/246c2d18cb)] - **tools**: fix TypeError from `test.py --time` (Richard Lau) [#20368](https://github.com/nodejs/node/pull/20368)
- [[`1241b90a13`](https://github.com/nodejs/node/commit/1241b90a13)] - **tools**: simplify HTML generation (Vse Mozhet Byt) [#20307](https://github.com/nodejs/node/pull/20307)
- [[`ac05c2b226`](https://github.com/nodejs/node/commit/ac05c2b226)] - **tools**: modernize and optimize doc/addon-verify.js (Vse Mozhet Byt) [#20188](https://github.com/nodejs/node/pull/20188)
- [[`fc41817f97`](https://github.com/nodejs/node/commit/fc41817f97)] - **tools**: don’t emit illegal utf-8 from icutrim/iculslocs (Steven R. Loomis) [#19756](https://github.com/nodejs/node/pull/19756)
- [[`cf2a7e9ce6`](https://github.com/nodejs/node/commit/cf2a7e9ce6)] - **tools**: apply editorconfig rules to tools also (Tobias Nießen) [#19521](https://github.com/nodejs/node/pull/19521)
- [[`36ffc3b69b`](https://github.com/nodejs/node/commit/36ffc3b69b)] - **tools**: remove src dir from JS editorconfig rule (Tobias Nießen) [#19521](https://github.com/nodejs/node/pull/19521)
- [[`ff4c30e9bb`](https://github.com/nodejs/node/commit/ff4c30e9bb)] - **tools**: dry utility function in tools/doc/json.js (Vse Mozhet Byt) [#19692](https://github.com/nodejs/node/pull/19692)
- [[`59b99e88fb`](https://github.com/nodejs/node/commit/59b99e88fb)] - **tools**: fix comment nits in tools/doc/\*.js files (Vse Mozhet Byt) [#19696](https://github.com/nodejs/node/pull/19696)
- [[`eb5f08546e`](https://github.com/nodejs/node/commit/eb5f08546e)] - **tools**: fix nits in tools/doc/type-parser.js (Vse Mozhet Byt) [#19612](https://github.com/nodejs/node/pull/19612)
- [[`4a1b064cdc`](https://github.com/nodejs/node/commit/4a1b064cdc)] - **tools**: simplify tools/doc/preprocess.js (Vse Mozhet Byt) [#19539](https://github.com/nodejs/node/pull/19539)
- [[`fe4e511ae8`](https://github.com/nodejs/node/commit/fe4e511ae8)] - **tools**: fix nits in tools/doc/common.js (Vse Mozhet Byt) [#19599](https://github.com/nodejs/node/pull/19599)
- [[`11b8d4793f`](https://github.com/nodejs/node/commit/11b8d4793f)] - **tools**: shorten metadata parsing (Tobias Nießen) [#19512](https://github.com/nodejs/node/pull/19512)
- [[`aa3be00b08`](https://github.com/nodejs/node/commit/aa3be00b08)] - **tools**: make metadata parsing less permissive (Tobias Nießen) [#19512](https://github.com/nodejs/node/pull/19512)
- [[`2fb47a5cbf`](https://github.com/nodejs/node/commit/2fb47a5cbf)] - **tools**: fix nits in tools/doc/preprocess.js (Vse Mozhet Byt) [#19473](https://github.com/nodejs/node/pull/19473)
- [[`e1c28b6f46`](https://github.com/nodejs/node/commit/e1c28b6f46)] - **tools**: fix logic nit in tools/doc/generate.js (Vse Mozhet Byt) [#19475](https://github.com/nodejs/node/pull/19475)
- [[`7d4d96b63d`](https://github.com/nodejs/node/commit/7d4d96b63d)] - **tools**: bump remark-cli to 4.0 (Refael Ackermann) [#17028](https://github.com/nodejs/node/pull/17028)
- [[`814021182e`](https://github.com/nodejs/node/commit/814021182e)] - **tools**: fix custom eslint rule errors (Ruben Bridgewater) [#18853](https://github.com/nodejs/node/pull/18853)
- [[`ce62e142b3`](https://github.com/nodejs/node/commit/ce62e142b3)] - **tools**: ignore VS compiler output in deps/v8 (Michaël Zasso) [#18952](https://github.com/nodejs/node/pull/18952)
- [[`817f43637b`](https://github.com/nodejs/node/commit/817f43637b)] - **tools**: custom eslint autofix for inspector-check.js (Shobhit Chittora) [#16646](https://github.com/nodejs/node/pull/16646)
- [[`c32b087161`](https://github.com/nodejs/node/commit/c32b087161)] - **tools**: auto fix custom crypto-check eslint rule (Shobhit Chittora) [#16647](https://github.com/nodejs/node/pull/16647)
- [[`7f1a9421c0`](https://github.com/nodejs/node/commit/7f1a9421c0)] - **tools**: fix eslint isRequired (Ruben Bridgewater) [#18729](https://github.com/nodejs/node/pull/18729)
- [[`bf09b7a155`](https://github.com/nodejs/node/commit/bf09b7a155)] - **tools**: treat SIGABRT as crash (Anna Henningsen) [#19990](https://github.com/nodejs/node/pull/19990)
- [[`79919a3a9a`](https://github.com/nodejs/node/commit/79919a3a9a)] - **tools**: ensure doc-only doesn't update package-lock (Myles Borins) [#21015](https://github.com/nodejs/node/pull/21015)
- [[`c5eb1f83d0`](https://github.com/nodejs/node/commit/c5eb1f83d0)] - **tools**: update tooling to work with new macOS CLI … (Rich Trott) [#21173](https://github.com/nodejs/node/pull/21173)
- [[`5362e2fbb3`](https://github.com/nodejs/node/commit/5362e2fbb3)] - **tools**: fix test-npm-package (Michaël Zasso) [#19293](https://github.com/nodejs/node/pull/19293)
- [[`ab967b725e`](https://github.com/nodejs/node/commit/ab967b725e)] - **tools**: fix icu readme lint error (Anatoli Papirovski) [#18445](https://github.com/nodejs/node/pull/18445)
- [[`f2506d46b5`](https://github.com/nodejs/node/commit/f2506d46b5)] - **tools**: don't lint-md as part of main lint target (Refael Ackermann) [#17587](https://github.com/nodejs/node/pull/17587)
- [[`3857e108ca`](https://github.com/nodejs/node/commit/3857e108ca)] - **tools**: speed up lint-md-build (Refael Ackermann) [#16945](https://github.com/nodejs/node/pull/16945)
- [[`c4716dc711`](https://github.com/nodejs/node/commit/c4716dc711)] - **tools, test**: fix prof polyfill readline (killagu) [#18641](https://github.com/nodejs/node/pull/18641)
- [[`4df93dc8ac`](https://github.com/nodejs/node/commit/4df93dc8ac)] - **tools,bootstrap**: preprocess gypi files to json (Gus Caplan) [#19140](https://github.com/nodejs/node/pull/19140)
- [[`7a35e18177`](https://github.com/nodejs/node/commit/7a35e18177)] - **tools,gyp**: fix regex for version matching (Rich Trott) [#21216](https://github.com/nodejs/node/pull/21216)
- [[`e602726c68`](https://github.com/nodejs/node/commit/e602726c68)] - **(SEMVER-MINOR)** **trace_events**: add file pattern cli option (Andreas Madsen) [#18480](https://github.com/nodejs/node/pull/18480)
- [[`9fdba04e5e`](https://github.com/nodejs/node/commit/9fdba04e5e)] - **tty**: fix console printing on Windows (Anna Henningsen) [#18214](https://github.com/nodejs/node/pull/18214)
- [[`40a36b3af8`](https://github.com/nodejs/node/commit/40a36b3af8)] - **url**: added url fragment lookup table (Hakan Kimeiga) [#17627](https://github.com/nodejs/node/pull/17627)
- [[`654ce4ba17`](https://github.com/nodejs/node/commit/654ce4ba17)] - **url**: added space to class string of iterator objects (Haejin Jo) [#17558](https://github.com/nodejs/node/pull/17558)
- [[`66520afdb8`](https://github.com/nodejs/node/commit/66520afdb8)] - **util**: skip type checks in internal getSystemErrorName (Joyee Cheung) [#18546](https://github.com/nodejs/node/pull/18546)
- [[`58b5a610d8`](https://github.com/nodejs/node/commit/58b5a610d8)] - **(SEMVER-MINOR)** **util**: implement util.getSystemErrorName() (Joyee Cheung) [#18186](https://github.com/nodejs/node/pull/18186)
- [[`ec1828c2b6`](https://github.com/nodejs/node/commit/ec1828c2b6)] - **(SEMVER-MAJOR)** **v8**: add new to the throw statement (Ruben Bridgewater) [#13857](https://github.com/nodejs/node/pull/13857)
- [[`8a5c100793`](https://github.com/nodejs/node/commit/8a5c100793)] - **win, tools**: add nasm to boxstarter script (Bartosz Sosnowski) [#19950](https://github.com/nodejs/node/pull/19950)

Windows 32-bit Installer: https://nodejs.org/dist/v8.12.0/node-v8.12.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v8.12.0/node-v8.12.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v8.12.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v8.12.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v8.12.0/node-v8.12.0.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v8.12.0/node-v8.12.0-darwin-x64.tar.gz \
Linux 32-bit Binary: https://nodejs.org/dist/v8.12.0/node-v8.12.0-linux-x86.tar.xz \
Linux 64-bit Binary: https://nodejs.org/dist/v8.12.0/node-v8.12.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v8.12.0/node-v8.12.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v8.12.0/node-v8.12.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v8.12.0/node-v8.12.0-aix-ppc64.tar.gz \
SmartOS 32-bit Binary: https://nodejs.org/dist/v8.12.0/node-v8.12.0-sunos-x86.tar.xz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v8.12.0/node-v8.12.0-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v8.12.0/node-v8.12.0-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v8.12.0/node-v8.12.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v8.12.0/node-v8.12.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v8.12.0/node-v8.12.0.tar.gz \
Other release files: https://nodejs.org/dist/v8.12.0/ \
Documentation: https://nodejs.org/docs/v8.12.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

6a514208421b0643769fb7064d2edaadedd10c6eef4647b645907481e575fca6  node-v8.12.0-aix-ppc64.tar.gz
ca131b84dfcf2b6f653a6521d31f7a108ad7d83f4d7e781945b2eca8172064aa  node-v8.12.0-darwin-x64.tar.gz
2d8b162cfc568d3d585c459fa0ded25b6d64e5f984a73bc0f89f2d6e5461baa3  node-v8.12.0-darwin-x64.tar.xz
68768e555161e54795fb61ac3de3de0933638f6356a8cead097b34eab16a1aa3  node-v8.12.0-headers.tar.gz
a5a8b2bdc0e202dfd8a0d2ee3667e28ec828b1c06f62d0c627cfbe262f173b51  node-v8.12.0-headers.tar.xz
781ecf1ecb14b4c671ef0732988636282d6fb7071c4bd52567f663b008796bc9  node-v8.12.0-linux-arm64.tar.gz
0fbdcfd8cf0cb5e8f5beff84ba091fd47126ba44c628e3a351501419b211aa1a  node-v8.12.0-linux-arm64.tar.xz
d7e9d6666075fa210767215b333bf9fb45f2e049b80e01208054749cd782d47e  node-v8.12.0-linux-armv6l.tar.gz
699ff3799f1b6374be342b1ae0e71b69507cc431cd3094d791f00a10d7673fdb  node-v8.12.0-linux-armv6l.tar.xz
9c564069c5c9f0c9062cb399db02670c79fc4a2a079e67edcc5785a73a663f6e  node-v8.12.0-linux-armv7l.tar.gz
a437f347e12d499f72edea97ebd127abe403417316a3f23cba7e1db6fcf770d1  node-v8.12.0-linux-armv7l.tar.xz
a0c52c8f175566b1b6664ea9bbb82ba93c17c0178d2148368ed070d7a54da291  node-v8.12.0-linux-ppc64le.tar.gz
3070989b41045faea4a923ea9d290ab9ac0983e462fc435e645b002ead05de68  node-v8.12.0-linux-ppc64le.tar.xz
7e13da69c0932800ba1d6d96c95ebaf520cc220d6ff61ca1d0b544c61ee84628  node-v8.12.0-linux-s390x.tar.gz
ac5d0645af733d5f9eb49caf27d2cb72f9ed0858806af2c967aff23c7ad40ede  node-v8.12.0-linux-s390x.tar.xz
3df19b748ee2b6dfe3a03448ebc6186a3a86aeab557018d77a0f7f3314594ef6  node-v8.12.0-linux-x64.tar.gz
29a20479cd1e3a03396a4e74a1784ccdd1cf2f96928b56f6ffa4c8dae40c88f2  node-v8.12.0-linux-x64.tar.xz
56ecffbd8a656991f71e4b53ab00af333c97453062cadc20a2103b933088d24d  node-v8.12.0-linux-x86.tar.gz
2f9820411c5d53a19daf85d575f01f99664d2aff1c85c281c7b12d8086b83b31  node-v8.12.0-linux-x86.tar.xz
c11b745e28628a89eaf667031541ab58c2c2f0baf03f7aecf36d668463a59c62  node-v8.12.0.pkg
74664b84c571c608943dc1dfff53d3eb058f929f5dba763d779b830cddef3331  node-v8.12.0-sunos-x64.tar.gz
69aa7141050924079ffc9b1c8d76fe22b2801e6a71ac57a473c3553e2a91e38c  node-v8.12.0-sunos-x64.tar.xz
05ac84818d45e64b0958f7bd81e073c5b8ca7bd9d7f71569ae8e511aeefaae09  node-v8.12.0-sunos-x86.tar.gz
cca30c21f932d13ca4b31ccfbe57711c6c52006ac49fd53c90c8ba7d8ff4e09b  node-v8.12.0-sunos-x86.tar.xz
b4797843136edd9195c28221a1680ae52c29d867fc5fc1c99f7d6e2f2126a67b  node-v8.12.0.tar.gz
5a9dff58016c18fb4bf902d963b124ff058a550ebcd9840c677757387bce419a  node-v8.12.0.tar.xz
c1fb949ca07542b491b896d73e9606bd26501bbaae8deb8b1e3dd8652349a6b6  node-v8.12.0-win-x64.7z
9b22c9b23148b61ea0052826b3ac0255b8a3a542c125272b8f014f15bf11b091  node-v8.12.0-win-x64.zip
4516c85c7b353fb6b8c5116f72c553c7ff52fe6801119cf0afc8d7d3a11c08f1  node-v8.12.0-win-x86.7z
f3c7f4b06c1252137ad5bc416bc5b347c58c271d32cd6e6030a624aa21b1bdfd  node-v8.12.0-win-x86.zip
e359b26b8ba52eb24c49af6222c4bf9e454d3d266290897b492e13fc2f06e316  node-v8.12.0-x64.msi
0e7b5601c6819d5fd042f21dc375c9cf067fa43c7ecb84ca8ce3646ae9624e73  node-v8.12.0-x86.msi
b270192df4ebc036b8ce1898244c28bfeb89992ac89c8f73c60a458faf57d93a  win-x64/node.exe
9206d0c000a2ea83e41bfaa00ac001b8b3cd9e6c6ba8f5f6826bf26dbedf396a  win-x64/node.lib
0d0687170293f839e2a0bcf97e38163bb68a1043b29b7609e75dbc9bbe53b5fc  win-x64/node_pdb.7z
47897e35f3316f9271e7a26ac6d81d22b1a43a7319a2aacc4304b45e0296cf5b  win-x64/node_pdb.zip
4ce2168afe0f7c5bfe070b3d4741c1d492d6d00a6076c6903f9097b8ab061036  win-x86/node.exe
d042cab5ef26cec479fb001bf7cfea2aa14570efae3b7edecd8dde5e7ae01747  win-x86/node.lib
350c96042bfc6106eb78e60c270239c51c89697eb5c3f242eea5e3f90aa53654  win-x86/node_pdb.7z
98db5e9cb1b36be698b91b964fd4c3353075f4de650deb20deb4043a11aa94e3  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----
Comment: GPGTools - https://gpgtools.org

iQEzBAEBCAAdFiEEDv/hvO/ZyE49CYFSkzsB9AtcqUYFAluXUIUACgkQkzsB9Atc
qUb77wf/WZPWPF6QGZV3kOQvXPJWflIO4bqGqSUxeg9ana+RVuy4anPjtYCJuxkm
TLJaiwVWDWFo6qs8tR7lbdxSwNEbCtYHNt+0LduJCLZU5L2aZbdQGbjj29B9TvpV
mYCBHnmpxOreYis3q73eldTkzJFi/cCvBuQ646aD4X/b+yRFgEC4gfyNQBZCJ97y
DBCveW9umRJ40i3fbB5SzSmkq+O9MSNb/iJ+oYdpPjETNrPvdgjFTYQqY10uW4u2
qOmGOIVotHrCc4ga/7cOGqjjCnTLxZJ+WVZ45liH/F/E2pNlQajZNsiSRMB5FhNZ
IO6t+C/lpv9zy7PRVN5e0lbD1Tc1FA==
=81ZM
-----END PGP SIGNATURE-----

```
