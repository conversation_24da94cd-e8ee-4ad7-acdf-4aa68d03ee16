---
date: '2016-08-16T14:55:11.659Z'
category: release
title: Node v6.4.0 (Current)
layout: blog-post
author: <PERSON>
---

### Notable changes

- **build**: zlib symbols and additional OpenSSL symbols are now exposed on Windows platforms. (<PERSON>) [#7983](https://github.com/nodejs/node/pull/7983) and [#7576](https://github.com/nodejs/node/pull/7576)
- **child_process**, **cluster**: Forked child processes and cluster workers now support stdio configuration. (<PERSON>) [#7811](https://github.com/nodejs/node/pull/7811) and [#7838](https://github.com/nodejs/node/pull/7838)
- **child_process**: `argv[0]` can now be set to arbitrary values in spawned processes. (<PERSON>) [#7696](https://github.com/nodejs/node/pull/7696)
- **fs**: `fs.ReadStream` now exposes the number of bytes it has read so far. (<PERSON><PERSON>) [#7942](https://github.com/nodejs/node/pull/7942)
- **repl**: The REPL now supports editor mode. (<PERSON> <PERSON>) [#7275](https://github.com/nodejs/node/pull/7275)
- **util**: `inspect()` can now be configured globally using `util.inspect.defaultOptions`. (Roman Reiss) [#8013](https://github.com/nodejs/node/pull/8013)

### Commits

- [[`06a0a053ea`](https://github.com/nodejs/node/commit/06a0a053ea)] - 2016-08-15, Version 6.4.0 (Current) (cjihrig) [#8070](https://github.com/nodejs/node/pull/8070)
- [[`342a85b1a7`](https://github.com/nodejs/node/commit/342a85b1a7)] - Working on v6.3.2 (Evan Lucas) [#7782](https://github.com/nodejs/node/pull/7782)
- [[`f135a4c3d1`](https://github.com/nodejs/node/commit/f135a4c3d1)] - 2016-07-21, Version 6.3.1 (Current) (Evan Lucas) [#7782](https://github.com/nodejs/node/pull/7782)
- [[`57043aad33`](https://github.com/nodejs/node/commit/57043aad33)] - **assert**: fix deepEqual/deepStrictEqual on equivalent typed arrays (Feross Aboukhadijeh) [#8002](https://github.com/nodejs/node/pull/8002)
- [[`f6713bfabd`](https://github.com/nodejs/node/commit/f6713bfabd)] - **bench**: add bench for fs.realpath() fix (Trevor Norris) [#7899](https://github.com/nodejs/node/pull/7899)
- [[`0d4b04659f`](https://github.com/nodejs/node/commit/0d4b04659f)] - **benchmark**: favor === over == (Rich Trott) [#8000](https://github.com/nodejs/node/pull/8000)
- [[`98f51ffeb6`](https://github.com/nodejs/node/commit/98f51ffeb6)] - **buffer**: fix unintended unsigned overflow (Fedor Indutny) [#7494](https://github.com/nodejs/node/pull/7494)
- [[`28071a130e`](https://github.com/nodejs/node/commit/28071a130e)] - **buffer**: introduce latin1 encoding term (Trevor Norris) [#7111](https://github.com/nodejs/node/pull/7111)
- [[`b0a557eef7`](https://github.com/nodejs/node/commit/b0a557eef7)] - **build**: add correct shared library naming on OS X (Stewart Addison) [#7687](https://github.com/nodejs/node/pull/7687)
- [[`6ed4ea8fd6`](https://github.com/nodejs/node/commit/6ed4ea8fd6)] - **build**: turn on thin static archives (Ben Noordhuis) [#7957](https://github.com/nodejs/node/pull/7957)
- [[`c843e58914`](https://github.com/nodejs/node/commit/c843e58914)] - **(SEMVER-MINOR)** **build**: export zlib symbols on Windows (Alex Hultman) [#7983](https://github.com/nodejs/node/pull/7983)
- [[`889c62fec1`](https://github.com/nodejs/node/commit/889c62fec1)] - **build**: fix dependency on missing header file (Ben Noordhuis) [#7945](https://github.com/nodejs/node/pull/7945)
- [[`a4394b8745`](https://github.com/nodejs/node/commit/a4394b8745)] - **build**: fix typo in non-essential source file name (Ben Noordhuis) [#7945](https://github.com/nodejs/node/pull/7945)
- [[`636cf2236a`](https://github.com/nodejs/node/commit/636cf2236a)] - **build**: adding config.gypi dep to addons/.buildstamp (Daniel Bevenius) [#7893](https://github.com/nodejs/node/pull/7893)
- [[`ddf292fc32`](https://github.com/nodejs/node/commit/ddf292fc32)] - **build**: don't link against liblog on host system (Ben Noordhuis) [#7762](https://github.com/nodejs/node/pull/7762)
- [[`f0312e6560`](https://github.com/nodejs/node/commit/f0312e6560)] - **(SEMVER-MINOR)** **build**: export more openssl symbols on Windows (Alex Hultman) [#7576](https://github.com/nodejs/node/pull/7576)
- [[`e561895275`](https://github.com/nodejs/node/commit/e561895275)] - **(SEMVER-MINOR)** **child_process**: control argv0 for spawned processes (Pat Pannuto) [#7696](https://github.com/nodejs/node/pull/7696)
- [[`da481c634f`](https://github.com/nodejs/node/commit/da481c634f)] - **(SEMVER-MINOR)** **child_process**: support stdio option in fork() (cjihrig) [#7811](https://github.com/nodejs/node/pull/7811)
- [[`a4f0b13e2b`](https://github.com/nodejs/node/commit/a4f0b13e2b)] - **(SEMVER-MINOR)** **cluster**: support stdio option for workers (cjihrig) [#7838](https://github.com/nodejs/node/pull/7838)
- [[`5f3ab3ffd1`](https://github.com/nodejs/node/commit/5f3ab3ffd1)] - **(SEMVER-MINOR)** **crypto**: fix undefined behavior in ParseExtension (Fedor Indutny) [#7494](https://github.com/nodejs/node/pull/7494)
- [[`60d6e048f0`](https://github.com/nodejs/node/commit/60d6e048f0)] - **(SEMVER-MINOR)** **deps**: v8_inspector: console support (Aleksei Koziatinskii) [#7988](https://github.com/nodejs/node/pull/7988)
- [[`a9fe85ee9c`](https://github.com/nodejs/node/commit/a9fe85ee9c)] - **deps**: v8_inspector update (Ali Ijaz Sheikh) [#8014](https://github.com/nodejs/node/pull/8014)
- [[`4d81362b99`](https://github.com/nodejs/node/commit/4d81362b99)] - **deps**: v8_inspector: remove jinja2 tests (Ali Ijaz Sheikh) [#7796](https://github.com/nodejs/node/pull/7796)
- [[`57312fc0c5`](https://github.com/nodejs/node/commit/57312fc0c5)] - **deps**: remove jinja.el from deps/v8_inspector (Ali Ijaz Sheikh) [#7796](https://github.com/nodejs/node/pull/7796)
- [[`507c65d94a`](https://github.com/nodejs/node/commit/507c65d94a)] - **deps**: update v8_inspector (Ali Ijaz Sheikh) [#7796](https://github.com/nodejs/node/pull/7796)
- [[`3f46b5c18e`](https://github.com/nodejs/node/commit/3f46b5c18e)] - **deps**: float gyp patch for long filenames (Anna Henningsen) [#7963](https://github.com/nodejs/node/pull/7963)
- [[`e6887e2ceb`](https://github.com/nodejs/node/commit/e6887e2ceb)] - **deps**: cherry-pick a76d133 from v8 upstream (Matt Loring) [#7689](https://github.com/nodejs/node/pull/7689)
- [[`a03e3d3cff`](https://github.com/nodejs/node/commit/a03e3d3cff)] - **deps**: cherry-pick b93c80a from v8 upstream (Matt Loring) [#7689](https://github.com/nodejs/node/pull/7689)
- [[`75b37a6bac`](https://github.com/nodejs/node/commit/75b37a6bac)] - **deps**: cherry-pick 43547df from V8 upstream (Franziska Hinkelmann) [#7863](https://github.com/nodejs/node/pull/7863)
- [[`af63871593`](https://github.com/nodejs/node/commit/af63871593)] - **deps**: cherry-pick a51f429 from V8 upstream (Franziska Hinkelmann) [#7834](https://github.com/nodejs/node/pull/7834)
- [[`e82e80417b`](https://github.com/nodejs/node/commit/e82e80417b)] - **deps**: backport 2bcbe2f from V8 upstream (ofrobots) [#7814](https://github.com/nodejs/node/pull/7814)
- [[`51a2041b90`](https://github.com/nodejs/node/commit/51a2041b90)] - **(SEMVER-MINOR)** **dgram**: generalized send queue to handle close (Matteo Collina) [#7066](https://github.com/nodejs/node/pull/7066)
- [[`7eb95f6faa`](https://github.com/nodejs/node/commit/7eb95f6faa)] - **doc**: minor updates to onboarding doc (Rich Trott) [#8060](https://github.com/nodejs/node/pull/8060)
- [[`5259322e62`](https://github.com/nodejs/node/commit/5259322e62)] - **doc**: add POST_STATUS_TO_PR info to onboarding doc (Rich Trott) [#8059](https://github.com/nodejs/node/pull/8059)
- [[`**********`](https://github.com/nodejs/node/commit/**********)] - **doc**: update windows prerequisites (Ben Noordhuis) [#8049](https://github.com/nodejs/node/pull/8049)
- [[`3fe122f57e`](https://github.com/nodejs/node/commit/3fe122f57e)] - **doc**: update licenses (Ali Ijaz Sheikh) [#7796](https://github.com/nodejs/node/pull/7796)
- [[`14b762f81f`](https://github.com/nodejs/node/commit/14b762f81f)] - **doc**: move orangemocha to collaborators list (Rich Trott) [#8062](https://github.com/nodejs/node/pull/8062)
- [[`ffbead92a0`](https://github.com/nodejs/node/commit/ffbead92a0)] - **doc**: Add fhinkel to collaborators (Franziska Hinkelmann) [#8052](https://github.com/nodejs/node/pull/8052)
- [[`96d15e2f3c`](https://github.com/nodejs/node/commit/96d15e2f3c)] - **doc**: fix cluster message event docs (Zach Bjornson) [#8017](https://github.com/nodejs/node/pull/8017)
- [[`4a8b8048f2`](https://github.com/nodejs/node/commit/4a8b8048f2)] - **doc**: add `added:` information for cluster (Anna Henningsen) [#7640](https://github.com/nodejs/node/pull/7640)
- [[`38255080db`](https://github.com/nodejs/node/commit/38255080db)] - **doc**: remove spurious new line in CHANGELOG_V6.md (Luigi Pinca) [#8009](https://github.com/nodejs/node/pull/8009)
- [[`9f78c3f64f`](https://github.com/nodejs/node/commit/9f78c3f64f)] - **doc**: fix typo in vm.runInNewContext() description (Luigi Pinca) [#8005](https://github.com/nodejs/node/pull/8005)
- [[`c4765a1b66`](https://github.com/nodejs/node/commit/c4765a1b66)] - **doc**: Clean up roff source in manpage (Alhadis) [#7819](https://github.com/nodejs/node/pull/7819)
- [[`cbcd03c912`](https://github.com/nodejs/node/commit/cbcd03c912)] - **doc**: add CTC meeting minutes 2016-08-03 (Josh Gavant) [#7980](https://github.com/nodejs/node/pull/7980)
- [[`7d0e5a0622`](https://github.com/nodejs/node/commit/7d0e5a0622)] - **doc**: clarify collaborators & ctc members relationships (yorkie) [#7996](https://github.com/nodejs/node/pull/7996)
- [[`dedfcb7858`](https://github.com/nodejs/node/commit/dedfcb7858)] - **doc**: clarify fd closing by `fs.readFile` etc. (kibertoad) [#7561](https://github.com/nodejs/node/pull/7561)
- [[`ce776d22f9`](https://github.com/nodejs/node/commit/ce776d22f9)] - **doc**: fix a markdown error in CTC meeting minutes (Сковорода Никита Андреевич) [#7729](https://github.com/nodejs/node/pull/7729)
- [[`b20518a013`](https://github.com/nodejs/node/commit/b20518a013)] - **doc**: add `added:` information for events (Luigi Pinca) [#7822](https://github.com/nodejs/node/pull/7822)
- [[`7fa4be0f87`](https://github.com/nodejs/node/commit/7fa4be0f87)] - **doc**: improve server.listen() random port (Phillip Johnsen) [#7976](https://github.com/nodejs/node/pull/7976)
- [[`7c427bdccc`](https://github.com/nodejs/node/commit/7c427bdccc)] - **doc**: clarify "Reviewed-By" iff "LGTM" (Bryan English) [#7183](https://github.com/nodejs/node/pull/7183)
- [[`cdbeae9adc`](https://github.com/nodejs/node/commit/cdbeae9adc)] - **doc**: add CTC meeting minutes 2016-07-13 (Josh Gavant) [#7968](https://github.com/nodejs/node/pull/7968)
- [[`2245e843cc`](https://github.com/nodejs/node/commit/2245e843cc)] - **doc**: add CTC meeting minutes 2016-07-20 (Josh Gavant) [#7970](https://github.com/nodejs/node/pull/7970)
- [[`cb0baca982`](https://github.com/nodejs/node/commit/cb0baca982)] - **doc**: use consistent markdown in README (Rich Trott) [#7971](https://github.com/nodejs/node/pull/7971)
- [[`3d1a06451a`](https://github.com/nodejs/node/commit/3d1a06451a)] - **doc**: use `git-secure-tag` for release tags (Fedor Indutny) [#7603](https://github.com/nodejs/node/pull/7603)
- [[`e116cf96a0`](https://github.com/nodejs/node/commit/e116cf96a0)] - **doc**: use blockquotes for Stability: markers (Anna Henningsen) [#7757](https://github.com/nodejs/node/pull/7757)
- [[`c934f51aa4`](https://github.com/nodejs/node/commit/c934f51aa4)] - **doc**: fix default encoding mention in crypto.md (hugnosis) [#7805](https://github.com/nodejs/node/pull/7805)
- [[`df35ae6246`](https://github.com/nodejs/node/commit/df35ae6246)] - **doc**: fix minor formatting issue in 0.10 changelog (Сковорода Никита Андреевич) [#7727](https://github.com/nodejs/node/pull/7727)
- [[`5f12807c46`](https://github.com/nodejs/node/commit/5f12807c46)] - **doc**: remove extra indentation in iojs changelog (Сковорода Никита Андреевич) [#7727](https://github.com/nodejs/node/pull/7727)
- [[`abd0bc0523`](https://github.com/nodejs/node/commit/abd0bc0523)] - **doc**: \*.md formatting fixes in the top-level dir (Сковорода Никита Андреевич) [#7727](https://github.com/nodejs/node/pull/7727)
- [[`c72019b75a`](https://github.com/nodejs/node/commit/c72019b75a)] - **doc**: convert tabs to spaces (Сковорода Никита Андреевич) [#7727](https://github.com/nodejs/node/pull/7727)
- [[`0fbb83a67b`](https://github.com/nodejs/node/commit/0fbb83a67b)] - **doc**: piscisaureus has stepped-down from the CTC (James M Snell) [#7969](https://github.com/nodejs/node/pull/7969)
- [[`48422c240a`](https://github.com/nodejs/node/commit/48422c240a)] - **doc**: add @addaleax to the CTC (Anna Henningsen) [#7966](https://github.com/nodejs/node/pull/7966)
- [[`0094adc0b2`](https://github.com/nodejs/node/commit/0094adc0b2)] - **doc**: add CTC meeting minutes 2016-06-22 (Josh Gavant) [#7390](https://github.com/nodejs/node/pull/7390)
- [[`fd9b7b4c5a`](https://github.com/nodejs/node/commit/fd9b7b4c5a)] - **doc**: add CTC meeting minutes 2016-07-06 (Josh Gavant) [#7570](https://github.com/nodejs/node/pull/7570)
- [[`4616261110`](https://github.com/nodejs/node/commit/4616261110)] - **doc**: add CTC meeting minutes 2016-06-29 (Josh Gavant) [#7571](https://github.com/nodejs/node/pull/7571)
- [[`bb90867339`](https://github.com/nodejs/node/commit/bb90867339)] - **doc**: add CTC meeting minutes 2016-07-27 (William Kapke) [#7900](https://github.com/nodejs/node/pull/7900)
- [[`7d0c1bf781`](https://github.com/nodejs/node/commit/7d0c1bf781)] - **doc**: fix path markdown formatting (Joey Cozza) [#7817](https://github.com/nodejs/node/pull/7817)
- [[`04ec64aacc`](https://github.com/nodejs/node/commit/04ec64aacc)] - **doc**: add missing semicolon (Ravindra barthwal) [#7915](https://github.com/nodejs/node/pull/7915)
- [[`8d8d70d826`](https://github.com/nodejs/node/commit/8d8d70d826)] - **doc**: fill in missing V8 version (Timothy Gu) [#7878](https://github.com/nodejs/node/pull/7878)
- [[`6ce9c80ccb`](https://github.com/nodejs/node/commit/6ce9c80ccb)] - **doc**: remove extra spaces and concats in examples (Joe Esposito) [#7885](https://github.com/nodejs/node/pull/7885)
- [[`23b6468667`](https://github.com/nodejs/node/commit/23b6468667)] - **doc**: add information about CTC quorum rules (Rich Trott) [#7813](https://github.com/nodejs/node/pull/7813)
- [[`0645c3d0c4`](https://github.com/nodejs/node/commit/0645c3d0c4)] - **doc**: align breakEvalOnSigint - repl option (Prince J Wesley) [#7849](https://github.com/nodejs/node/pull/7849)
- [[`14a0c3181c`](https://github.com/nodejs/node/commit/14a0c3181c)] - **doc**: remove platform assumption from CONTRIBUTING (Bethany N Griggs) [#7783](https://github.com/nodejs/node/pull/7783)
- [[`5c4b938665`](https://github.com/nodejs/node/commit/5c4b938665)] - **doc**: minor typo fixes in stream docs (Alex Perkins) [#7763](https://github.com/nodejs/node/pull/7763)
- [[`57fb0d2ee2`](https://github.com/nodejs/node/commit/57fb0d2ee2)] - **doc**: add/fix version metadata for Buffer methods (Brian White) [#7784](https://github.com/nodejs/node/pull/7784)
- [[`49a669bcda`](https://github.com/nodejs/node/commit/49a669bcda)] - **doc**: improve function parameter descriptions (Brian White) [#7784](https://github.com/nodejs/node/pull/7784)
- [[`bdc8690610`](https://github.com/nodejs/node/commit/bdc8690610)] - **doc**: add missing properties in Buffer docs (Brian White) [#7784](https://github.com/nodejs/node/pull/7784)
- [[`a8e7c7f2bf`](https://github.com/nodejs/node/commit/a8e7c7f2bf)] - **doc**: improve wording and style of Buffer docs (Brian White) [#7784](https://github.com/nodejs/node/pull/7784)
- [[`9a4a00bcdb`](https://github.com/nodejs/node/commit/9a4a00bcdb)] - **doc**: improve links in Buffer docs (Brian White) [#7784](https://github.com/nodejs/node/pull/7784)
- [[`0103d9dcea`](https://github.com/nodejs/node/commit/0103d9dcea)] - **doc**: reorganize Buffer link references (Brian White) [#7784](https://github.com/nodejs/node/pull/7784)
- [[`17ae49a055`](https://github.com/nodejs/node/commit/17ae49a055)] - **doc**: improve Buffer code examples (Brian White) [#7784](https://github.com/nodejs/node/pull/7784)
- [[`0ffeddb5b4`](https://github.com/nodejs/node/commit/0ffeddb5b4)] - **doc**: various documentation formatting fixes (Сковорода Никита Андреевич) [#7637](https://github.com/nodejs/node/pull/7637)
- [[`1fa9330ac6`](https://github.com/nodejs/node/commit/1fa9330ac6)] - **doc**: add princejwesley to collaborators (Prince J Wesley) [#7877](https://github.com/nodejs/node/pull/7877)
- [[`715ac62670`](https://github.com/nodejs/node/commit/715ac62670)] - **doc**: clarify that the node.js irc channel is not under tsc oversight (James M Snell) [#7810](https://github.com/nodejs/node/pull/7810)
- [[`edb877da65`](https://github.com/nodejs/node/commit/edb877da65)] - **doc**: fix `added:` date for `NODE_REPL_HISTORY` (Anna Henningsen) [#7775](https://github.com/nodejs/node/pull/7775)
- [[`27f92efaee`](https://github.com/nodejs/node/commit/27f92efaee)] - **doctool**: improve the title of pages in doc (yorkie) [#7939](https://github.com/nodejs/node/pull/7939)
- [[`18a3064937`](https://github.com/nodejs/node/commit/18a3064937)] - **fs**: restore JS implementation of realpath (Bartosz Sosnowski) [#7899](https://github.com/nodejs/node/pull/7899)
- [[`0bb9d21f0e`](https://github.com/nodejs/node/commit/0bb9d21f0e)] - **(SEMVER-MINOR)** **fs**: add bytesRead to ReadStream (Linus Unnebäck) [#7942](https://github.com/nodejs/node/pull/7942)
- [[`db3a7e83eb`](https://github.com/nodejs/node/commit/db3a7e83eb)] - **http**: specify \_implicitHeader in OutgoingMessage (yorkie) [#7949](https://github.com/nodejs/node/pull/7949)
- [[`b75ca50c90`](https://github.com/nodejs/node/commit/b75ca50c90)] - **inspector**: Do not crash if the port is n/a (Eugene Ostroukhov) [#7874](https://github.com/nodejs/node/pull/7874)
- [[`7dc66f82e3`](https://github.com/nodejs/node/commit/7dc66f82e3)] - **lib**: remove double check of string type (Franziska Hinkelmann) [#7985](https://github.com/nodejs/node/pull/7985)
- [[`5cc4b0ed15`](https://github.com/nodejs/node/commit/5cc4b0ed15)] - **meta**: clarify process for breaking changes (Rich Trott) [#7955](https://github.com/nodejs/node/pull/7955)
- [[`79ecfb5183`](https://github.com/nodejs/node/commit/79ecfb5183)] - **meta**: include a minimal CTC removal policy (Rich Trott) [#7720](https://github.com/nodejs/node/pull/7720)
- [[`376d73b3b9`](https://github.com/nodejs/node/commit/376d73b3b9)] - **meta**: provide example activities (Rich Trott) [#7744](https://github.com/nodejs/node/pull/7744)
- [[`ccbb46378f`](https://github.com/nodejs/node/commit/ccbb46378f)] - **module**: fix node_modules search path in edge case (hefangshi) [#6670](https://github.com/nodejs/node/pull/6670)
- [[`2f32191686`](https://github.com/nodejs/node/commit/2f32191686)] - **(SEMVER-MINOR)** **process**: save original argv\[0\] (Pat Pannuto) [#7696](https://github.com/nodejs/node/pull/7696)
- [[`d9c9e46780`](https://github.com/nodejs/node/commit/d9c9e46780)] - **repl**: disable Ctrl+C support on win32 for now (Anna Henningsen) [#7977](https://github.com/nodejs/node/pull/7977)
- [[`61e57e06a6`](https://github.com/nodejs/node/commit/61e57e06a6)] - **repl**: don't override all internal repl defaults (cjihrig) [#7826](https://github.com/nodejs/node/pull/7826)
- [[`4875aa2aa2`](https://github.com/nodejs/node/commit/4875aa2aa2)] - **(SEMVER-MINOR)** **repl**: Add editor mode support (Prince J Wesley) [#7275](https://github.com/nodejs/node/pull/7275)
- [[`fc3ba2ff4f`](https://github.com/nodejs/node/commit/fc3ba2ff4f)] - **(SEMVER-MINOR)** **repl**: Use displayErrors for SyntaxError (Prince J Wesley) [#7589](https://github.com/nodejs/node/pull/7589)
- [[`b3164ae22e`](https://github.com/nodejs/node/commit/b3164ae22e)] - **(SEMVER-MINOR)** **repl**: add support for custom completions (Diosney Sarmiento) [#7527](https://github.com/nodejs/node/pull/7527)
- [[`980f4da8c4`](https://github.com/nodejs/node/commit/980f4da8c4)] - **repl**: prevent undefined ref in completion (Evan Lucas) [#7718](https://github.com/nodejs/node/pull/7718)
- [[`6e6cf36761`](https://github.com/nodejs/node/commit/6e6cf36761)] - **repl**: default useGlobal to true (cjihrig) [#7795](https://github.com/nodejs/node/pull/7795)
- [[`08e6eeee70`](https://github.com/nodejs/node/commit/08e6eeee70)] - **repl,util**: insert carriage returns in output (JungMinu) [#8028](https://github.com/nodejs/node/pull/8028)
- [[`fb8840cac2`](https://github.com/nodejs/node/commit/fb8840cac2)] - **src**: use RAII for mutexes in node_watchdog.cc (Anna Henningsen) [#7933](https://github.com/nodejs/node/pull/7933)
- [[`780395ffca`](https://github.com/nodejs/node/commit/780395ffca)] - **src**: fix use-after-free in inspector agent (Ben Noordhuis) [#7907](https://github.com/nodejs/node/pull/7907)
- [[`9d45569ed4`](https://github.com/nodejs/node/commit/9d45569ed4)] - **src**: avoid manual memory management in inspector (Ben Noordhuis) [#7906](https://github.com/nodejs/node/pull/7906)
- [[`a20336e708`](https://github.com/nodejs/node/commit/a20336e708)] - **src**: remove unused using decls (Haojian Wu) [#7990](https://github.com/nodejs/node/pull/7990)
- [[`317ae96c33`](https://github.com/nodejs/node/commit/317ae96c33)] - **src**: make EnvDelete behave like the delete operator (Franziska Hinkelmann) [#7975](https://github.com/nodejs/node/pull/7975)
- [[`1ab796fa96`](https://github.com/nodejs/node/commit/1ab796fa96)] - **src**: do not copy on failing setProperty() (Franziska Hinkelmann) [#7908](https://github.com/nodejs/node/pull/7908)
- [[`cf65a7ce9e`](https://github.com/nodejs/node/commit/cf65a7ce9e)] - **src**: unifying PipeConnectWrap and TCPConnectWrap (Daniel Bevenius) [#7501](https://github.com/nodejs/node/pull/7501)
- [[`63c62cce35`](https://github.com/nodejs/node/commit/63c62cce35)] - **src**: Only use TR1 type_traits on OSX<10.9 (Ehsan Akhgari) [#7778](https://github.com/nodejs/node/pull/7778)
- [[`d7143095cb`](https://github.com/nodejs/node/commit/d7143095cb)] - **src**: fix build on CentOS (Rich Trott) [#7873](https://github.com/nodejs/node/pull/7873)
- [[`303f4102d3`](https://github.com/nodejs/node/commit/303f4102d3)] - **src**: pull OnConnection from pipe_wrap and tcp_wrap (Daniel Bevenius) [#7547](https://github.com/nodejs/node/pull/7547)
- [[`c967af8c07`](https://github.com/nodejs/node/commit/c967af8c07)] - **src**: suppress coverity message (cjihrig) [#7587](https://github.com/nodejs/node/pull/7587)
- [[`f3e5b39696`](https://github.com/nodejs/node/commit/f3e5b39696)] - **src**: guard against overflow in ParseArrayIndex() (Ben Noordhuis) [#7497](https://github.com/nodejs/node/pull/7497)
- [[`c730a5d026`](https://github.com/nodejs/node/commit/c730a5d026)] - **src**: move ParseArrayIndex() to src/node_buffer.cc (Ben Noordhuis) [#7497](https://github.com/nodejs/node/pull/7497)
- [[`da9bd2fc48`](https://github.com/nodejs/node/commit/da9bd2fc48)] - **src**: alias BINARY to LATIN1 (Ben Noordhuis) [#7284](https://github.com/nodejs/node/pull/7284)
- [[`7ba0f860a6`](https://github.com/nodejs/node/commit/7ba0f860a6)] - **src**: fix erroneous fallthrough in ParseEncoding() (Ben Noordhuis) [#7262](https://github.com/nodejs/node/pull/7262)
- [[`a059aea9a2`](https://github.com/nodejs/node/commit/a059aea9a2)] - **src**: remove final trace of raw encoding (Trevor Norris) [#7111](https://github.com/nodejs/node/pull/7111)
- [[`2db26cb165`](https://github.com/nodejs/node/commit/2db26cb165)] - **test**: add test for debug usage message (Rich Trott) [#8061](https://github.com/nodejs/node/pull/8061)
- [[`2e435998eb`](https://github.com/nodejs/node/commit/2e435998eb)] - **test**: mark test failing on AIX as flaky (Michael Dawson) [#8065](https://github.com/nodejs/node/pull/8065)
- [[`554b0f9d91`](https://github.com/nodejs/node/commit/554b0f9d91)] - **test**: fix failing inspector cctest (Eugene Ostroukhov) [#8019](https://github.com/nodejs/node/pull/8019)
- [[`c565c17636`](https://github.com/nodejs/node/commit/c565c17636)] - **test**: fix memory leaks in inspector tests (Ben Noordhuis) [#7906](https://github.com/nodejs/node/pull/7906)
- [[`5d68e4ba9b`](https://github.com/nodejs/node/commit/5d68e4ba9b)] - **test**: console constructor missing new keyword (Rich Trott) [#8003](https://github.com/nodejs/node/pull/8003)
- [[`9735accd3e`](https://github.com/nodejs/node/commit/9735accd3e)] - **test**: allow globals to be whitelisted (cjihrig) [#7826](https://github.com/nodejs/node/pull/7826)
- [[`a385277eb5`](https://github.com/nodejs/node/commit/a385277eb5)] - **test**: fix flaky test-vm-sigint-existing-handler (Anna Henningsen) [#7982](https://github.com/nodejs/node/pull/7982)
- [[`b5beae2529`](https://github.com/nodejs/node/commit/b5beae2529)] - **test**: remove internal headers from addons (Gibson Fahnestock) [#7947](https://github.com/nodejs/node/pull/7947)
- [[`02b12fe880`](https://github.com/nodejs/node/commit/02b12fe880)] - **test**: improve chained property readability (Rich Trott) [#7920](https://github.com/nodejs/node/pull/7920)
- [[`d94063a22b`](https://github.com/nodejs/node/commit/d94063a22b)] - **test**: fix test-vm-sigint flakiness (Santiago Gimeno) [#7854](https://github.com/nodejs/node/pull/7854)
- [[`facd7dade1`](https://github.com/nodejs/node/commit/facd7dade1)] - **test**: don't hard code deprecation count (Prince J Wesley) [#7927](https://github.com/nodejs/node/pull/7927)
- [[`4aee970d92`](https://github.com/nodejs/node/commit/4aee970d92)] - **test**: decrease inconsistency in the common.js (Vse Mozhet Byt) [#7758](https://github.com/nodejs/node/pull/7758)
- [[`10f0c94c35`](https://github.com/nodejs/node/commit/10f0c94c35)] - **test**: fix flaky test-tls-wrap-timeout (Rich Trott) [#7857](https://github.com/nodejs/node/pull/7857)
- [[`ccfa6bf4d4`](https://github.com/nodejs/node/commit/ccfa6bf4d4)] - **test**: speed up test-net-reconnect-error (Rich Trott) [#7886](https://github.com/nodejs/node/pull/7886)
- [[`577adc74cd`](https://github.com/nodejs/node/commit/577adc74cd)] - **test**: ensure stream preprocessing order (Vse Mozhet Byt) [#7741](https://github.com/nodejs/node/pull/7741)
- [[`8f51e36898`](https://github.com/nodejs/node/commit/8f51e36898)] - **test**: use common platform helpers everywhere (Santiago Gimeno) [#7845](https://github.com/nodejs/node/pull/7845)
- [[`2f45941807`](https://github.com/nodejs/node/commit/2f45941807)] - **test**: handle IPv6 localhost issues within tests (Rich Trott) [#7766](https://github.com/nodejs/node/pull/7766)
- [[`e56db1477c`](https://github.com/nodejs/node/commit/e56db1477c)] - **test**: fix flaky test-\*-connect-address-family (Rich Trott) [#7605](https://github.com/nodejs/node/pull/7605)
- [[`1ab6df6b04`](https://github.com/nodejs/node/commit/1ab6df6b04)] - **test**: make import common as the first line (Sakthipriyan Vairamani) [#7786](https://github.com/nodejs/node/pull/7786)
- [[`0daceffd38`](https://github.com/nodejs/node/commit/0daceffd38)] - **test,assert**: add deepEqual/deepStrictEqual tests for typed arrays (Feross Aboukhadijeh) [#8002](https://github.com/nodejs/node/pull/8002)
- [[`4416ffab8a`](https://github.com/nodejs/node/commit/4416ffab8a)] - **test,util**: fix flaky test-util-sigint-watchdog (Anna Henningsen) [#7933](https://github.com/nodejs/node/pull/7933)
- [[`4535149794`](https://github.com/nodejs/node/commit/4535149794)] - **timers**: remove unused repeat param in timer_wrap (Jan Schär) [#7994](https://github.com/nodejs/node/pull/7994)
- [[`381aef8145`](https://github.com/nodejs/node/commit/381aef8145)] - **timers**: fix cleanup of nested same-timeout timers (Erin Spiceland) [#7827](https://github.com/nodejs/node/pull/7827)
- [[`e611c293bb`](https://github.com/nodejs/node/commit/e611c293bb)] - **tools**: enable rest-spread-spacing (Rich Trott) [#8073](https://github.com/nodejs/node/pull/8073)
- [[`7eb0e7a479`](https://github.com/nodejs/node/commit/7eb0e7a479)] - **tools**: favor === over == in license2rtf.js (Rich Trott)
- [[`583a2515da`](https://github.com/nodejs/node/commit/583a2515da)] - **tools**: update license-builder.sh for v8_inspector (Ali Ijaz Sheikh) [#7796](https://github.com/nodejs/node/pull/7796)
- [[`97934f99bb`](https://github.com/nodejs/node/commit/97934f99bb)] - **tools**: enable linting for chained properties (Rich Trott) [#7999](https://github.com/nodejs/node/pull/7999)
- [[`60ff991c09`](https://github.com/nodejs/node/commit/60ff991c09)] - **tools**: update to ESLint 3.2.2 (Rich Trott) [#7999](https://github.com/nodejs/node/pull/7999)
- [[`d37a17ec5f`](https://github.com/nodejs/node/commit/d37a17ec5f)] - **tools**: add remark-lint configuration in .remarkrc (Сковорода Никита Андреевич) [#7729](https://github.com/nodejs/node/pull/7729)
- [[`cb16e97e9f`](https://github.com/nodejs/node/commit/cb16e97e9f)] - **tools**: add .vscode folder to .gitignore (Josh Gavant) [#7967](https://github.com/nodejs/node/pull/7967)
- [[`fecf611ca8`](https://github.com/nodejs/node/commit/fecf611ca8)] - **tools,test**: show signal code when test crashes (Santiago Gimeno) [#7859](https://github.com/nodejs/node/pull/7859)
- [[`2f20910e24`](https://github.com/nodejs/node/commit/2f20910e24)] - **tty**: set the handle to blocking mode (Jeremiah Senkpiel) [#6816](https://github.com/nodejs/node/pull/6816)
- [[`cfec3ae5fd`](https://github.com/nodejs/node/commit/cfec3ae5fd)] - **(SEMVER-MINOR)** **util**: add inspect.defaultOptions (Roman Reiss) [#8013](https://github.com/nodejs/node/pull/8013)
- [[`295d1ea016`](https://github.com/nodejs/node/commit/295d1ea016)] - **util**: support classes in util.deprecate() (vladimir) [#7690](https://github.com/nodejs/node/pull/7690)
- [[`0a07201ca1`](https://github.com/nodejs/node/commit/0a07201ca1)] - **util**: fix formatting of objects with SIMD enabled (Anna Henningsen) [#7864](https://github.com/nodejs/node/pull/7864)
- [[`f1c50a8c5e`](https://github.com/nodejs/node/commit/f1c50a8c5e)] - **win,msi**: fix inclusion of translations (João Reis) [#7798](https://github.com/nodejs/node/pull/7798)
- [[`dbbcb9dbd9`](https://github.com/nodejs/node/commit/dbbcb9dbd9)] - **win,msi**: Added Italian translation (Matteo Collina) [#4647](https://github.com/nodejs/node/pull/4647)
- [[`909254c901`](https://github.com/nodejs/node/commit/909254c901)] - **zlib**: remove unneeded property (Jan Schär) [#7987](https://github.com/nodejs/node/pull/7987)

Windows 32-bit Installer: https://nodejs.org/dist/v6.4.0/node-v6.4.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v6.4.0/node-v6.4.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v6.4.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v6.4.0/win-x64/node.exe \
Mac OS X 64-bit Installer: https://nodejs.org/dist/v6.4.0/node-v6.4.0.pkg \
Mac OS X 64-bit Binary: https://nodejs.org/dist/v6.4.0/node-v6.4.0-darwin-x64.tar.gz \
Linux 32-bit Binary: https://nodejs.org/dist/v6.4.0/node-v6.4.0-linux-x86.tar.xz \
Linux 64-bit Binary: https://nodejs.org/dist/v6.4.0/node-v6.4.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v6.4.0/node-v6.4.0-linux-ppc64le.tar.xz \
SmartOS 32-bit Binary: https://nodejs.org/dist/v6.4.0/node-v6.4.0-sunos-x86.tar.xz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v6.4.0/node-v6.4.0-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v6.4.0/node-v6.4.0-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v6.4.0/node-v6.4.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v6.4.0/node-v6.4.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v6.4.0/node-v6.4.0.tar.gz \
Other release files: https://nodejs.org/dist/v6.4.0/ \
Documentation: https://nodejs.org/docs/v6.4.0/api/

Shasums (GPG signing hash: SHA512, file hash: SHA256):

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA512

4d04f11e12a360a0d5c6da5cd393fbe75a13f111a492c2a12185992b83542f9c  node-v6.4.0-darwin-x64.tar.gz
ffbb7c4f65a262bb682d60b04ad822f195e90c263d4a13ce27d59d84067e1269  node-v6.4.0-darwin-x64.tar.xz
6e094188a1c6dda967d160a92ec55ff9b46120a2207291eb067404dbf9cbd6c6  node-v6.4.0-headers.tar.gz
e6eff7186e51a9defa51088bb53a32e7f821000b4879ee5bb2c1c0eda9feb979  node-v6.4.0-headers.tar.xz
42493fcd0266949b219bb4a0f36e1e765f266aed104c594cff012b906dcc53c1  node-v6.4.0-linux-arm64.tar.gz
8c7bfba97061f15d567a5e5725ebeb795192a055074d7266d9de36a8acb3ab9a  node-v6.4.0-linux-arm64.tar.xz
94e97543f4f32f6b8368d37c6ea8eebcbf43dd2fa170e02166d9887646d42ec4  node-v6.4.0-linux-armv6l.tar.gz
b55b65a1918b321ead91af1ae9b50517ad8bed5358a9efcdaa18019c6e73f41e  node-v6.4.0-linux-armv6l.tar.xz
51fddefc79a2e8303b975345eea10128a2180a3f40c06238c77daa3c0009b8c4  node-v6.4.0-linux-armv7l.tar.gz
48b9db9e41d7832afeafb48a0b95b11ec736ac881a60742b82819bdefe1ea48e  node-v6.4.0-linux-armv7l.tar.xz
9e28554ec3f4f18d675d952f00f1f06535254c9513b5cde3d6f9241be4ef063a  node-v6.4.0-linux-ppc64le.tar.gz
7d2a0e86ffeace9e1650255fc88637518666ed4a983bf39a68fedddb800cbcb1  node-v6.4.0-linux-ppc64le.tar.xz
43b3091418816b3576b48d6a462bc96eec1d2cb107164631d8b5887a597b1992  node-v6.4.0-linux-ppc64.tar.gz
b3259edbdeb88773799b3b5fdd34e8e58741cf2ab15528f5d0a6600102c9df92  node-v6.4.0-linux-ppc64.tar.xz
990636e44b9f7a270cf82f988e5faecb5850fcda9580da65e5721b90ed3dddb2  node-v6.4.0-linux-x64.tar.gz
54ecdb5578cac005ef179f07d421e70bd8a966bc50b5a319188a7b36cf3e441c  node-v6.4.0-linux-x64.tar.xz
3630f172fee9c30ef95df3972ae9b2b234273ad504f245e0db363797fa2c08f4  node-v6.4.0-linux-x86.tar.gz
26d8de42de0599d33e893522f9ca389eacc6afc2c28f3fdd91505c3ceafed0d0  node-v6.4.0-linux-x86.tar.xz
4d27bd28de1b5cfdc1305915855d6024ac9e2b16bc443829ff93b796a880a3aa  node-v6.4.0.pkg
16f316b4195f480a0b318c27c30d84f60f9a0cf567b4f083aad0db733a64aaec  node-v6.4.0-sunos-x64.tar.gz
87933629f7e4897abf388ee40a5d077791238cd0d93994d106c063a4a56e1df3  node-v6.4.0-sunos-x64.tar.xz
96b24de07ad692e74a8543df3e965bb9ee337130a5c2cf2531c6290e934cda54  node-v6.4.0-sunos-x86.tar.gz
e26276a5f9798a421b17abfc530273edf8e70e3fd07c85024229b4a137ec6c1b  node-v6.4.0-sunos-x86.tar.xz
a58c930618cf5b0862aa329a626e850519036acfcd746983010c9946df1797d0  node-v6.4.0.tar.gz
49b6882db88a9b08939b1d06e4e926bec0d6f4f67eee3bdb475e3487c6bd7dac  node-v6.4.0.tar.xz
3dd69f45fba97337f57481cb167696822b5d2657740fc6eff2cfea1a017a546f  node-v6.4.0-win-x64.7z
bb994370e43c1539e11588a86e69064459141b6984b705f1c79a1a13181193b9  node-v6.4.0-win-x64.zip
765cc4330e5efbbd099d095430ad945fc150b90f888fb77f33f72ad2b2e0693e  node-v6.4.0-win-x86.7z
5709ddfb479522632f9f1246f1e328a22341e61b44174d60e63996f558a5453d  node-v6.4.0-win-x86.zip
c2f9970ff7402078f4210f981f96672c28bba018b4390517cbc4a1a785a4b7b7  node-v6.4.0-x64.msi
35df69a68e14e9029589b22d79211c4b73cdf70a6df42777ad86e1e5b0fb759a  node-v6.4.0-x86.msi
150ea62e87a087390172aa0e97030658faf5622c2ade2c7ee12acc8d336c40e3  win-x64/node.exe
479666193acab8c4e730e9a8a2d4754353b0d7d72f653f971b749ce74ed2c722  win-x64/node.lib
e82cc3aa5820bd76c6829839dd602ca77814cd0870b21557ab5fa991d36085f6  win-x64/node_pdb.7z
8d133f15030a453b14dd96166d7d63da79c6c143c3d4f190cd5bbb3110ef8db1  win-x64/node_pdb.zip
2401cead2af0d68f3d7aa7b65f57d0a512aa825e2c3cd8e02ac8ec208ee4f5e6  win-x86/node.exe
6bc07f4adfc312e538fe1ce0ca9d59da1f299a11e20ad2daf4f114f6dd196a78  win-x86/node.lib
2ffc4b2ddf0efd32e6fe75ceda06baec12e8e183c9d2da09f63e6c7a90964144  win-x86/node_pdb.7z
eee8297bec4cb4ac5be35a9dacc08a45491a596229a802d5d1083ed5256f3097  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----
Comment: GPGTools - https://gpgtools.org

iQIcBAEBCgAGBQJXsyeVAAoJEHQ0OQvb6bnFc0QP/irn694UVjab4jILQeeFQNT1
BzoCPcm7MN75dxMGu/2tzpnkEyQLTlqb69Ikhixy7edqzpI3oj8X7TRdduSkZgwD
/JzwbKSoXcUHdqNBbyuGq0AWfG03/VqJJ1V5fSmKDETdPAvqwKdedwMD+geXKlZw
0NcMpa4qtIq+5waVeQOUyEpDS8UpveZDlb8Ih1q6cddhiBmu0QvxmUB7ehXc2n6p
B/MQAaUKurYpz2m+HQI5M7G6htDFWNomAXoMgBnjdn3Sj2e+32QwvhYleVR4pIGW
wjXhkbIRyIiHUwTq1clBWWovFW1wx/nvYHSAonNFfab1SwRUIyMEAcgyLDLBXaE7
HKpqrdsSA2AwqwIE/5lKOZMPX7SrPGUzMvgRT0X3lfg2MxDGYwxP+w5dudM8v4r9
TX5IP1SiSykf5/EwcH0DLRFkATo5gBGEh2DnV37zv8aSgeCqAG1Yq4O5PLPIBHfk
vK0r9r7G2o5Hq0fsQC7nuf3vAuImc64p2RHvGRkPKJQcFfo6RBFtBd/0+AAgPg2p
ywk4HRP1vj3WxzrN4MIj3K8Id36NwGu9fFZHwTuzyHXkGsxMlVHLCT9Lh4g5o7if
bh4i/AnZJYUzrqyQ/7wpbKrRIwpQl99SGii2ZNjajDCp/bT8xM6czKTRV3A4npjH
GKimGdNeYQ7iUigdekY7
=nn+Y
-----END PGP SIGNATURE-----

```
