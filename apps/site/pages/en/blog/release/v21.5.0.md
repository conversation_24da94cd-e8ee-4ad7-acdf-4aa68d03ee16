---
date: '2023-12-19T19:09:27.135Z'
category: release
title: Node v21.5.0 (Current)
layout: blog-post
author: <PERSON>
---

## 2023-12-19, Version 21.5.0 (Current), @RafaelGSS

### Notable Changes

- \[[`0dd53da722`](https://github.com/nodejs/node/commit/0dd53da722)] - **(SEMVER-MINOR)** **deps**: add simdjson (<PERSON><PERSON><PERSON>) [#50322](https://github.com/nodejs/node/pull/50322)
- \[[`9f54987fbc`](https://github.com/nodejs/node/commit/9f54987fbc)] - **module**: merge config with `package_json_reader` (<PERSON><PERSON><PERSON>) [#50322](https://github.com/nodejs/node/pull/50322)
- \[[`45e4f82912`](https://github.com/nodejs/node/commit/45e4f82912)] - **src**: move package resolver to c++ (<PERSON><PERSON><PERSON>) [#50322](https://github.com/nodejs/node/pull/50322)

#### Deprecations

- \[[`26ed4ad01f`](https://github.com/nodejs/node/commit/26ed4ad01f)] - **doc**: deprecate hash constructor (Marco Ippolito) [#51077](https://github.com/nodejs/node/pull/51077)
- \[[`58ca66a1a7`](https://github.com/nodejs/node/commit/58ca66a1a7)] - **doc**: deprecate `dirent.path` (Antoine du Hamel) [#51020](https://github.com/nodejs/node/pull/51020)

### Commits

- \[[`1bbdbdfbeb`](https://github.com/nodejs/node/commit/1bbdbdfbeb)] - **benchmark**: update iterations in benchmark/perf_hooks (Lei Shi) [#50869](https://github.com/nodejs/node/pull/50869)
- \[[`087fb0908e`](https://github.com/nodejs/node/commit/087fb0908e)] - **benchmark**: update iterations in benchmark/crypto/aes-gcm-throughput.js (Lei Shi) [#50929](https://github.com/nodejs/node/pull/50929)
- \[[`53b16c71fb`](https://github.com/nodejs/node/commit/53b16c71fb)] - **benchmark**: update iteration and size in benchmark/crypto/randomBytes.js (Lei Shi) [#50868](https://github.com/nodejs/node/pull/50868)
- \[[`38fd0ca753`](https://github.com/nodejs/node/commit/38fd0ca753)] - **benchmark**: add undici websocket benchmark (Chenyu Yang) [#50586](https://github.com/nodejs/node/pull/50586)
- \[[`b148c43244`](https://github.com/nodejs/node/commit/b148c43244)] - **benchmark**: add create-hash benchmark (Joyee Cheung) [#51026](https://github.com/nodejs/node/pull/51026)
- \[[`fdd8c18f96`](https://github.com/nodejs/node/commit/fdd8c18f96)] - **benchmark**: update interations and len in benchmark/util/text-decoder.js (Lei Shi) [#50938](https://github.com/nodejs/node/pull/50938)
- \[[`a9972057ac`](https://github.com/nodejs/node/commit/a9972057ac)] - **benchmark**: update iterations of benchmark/util/type-check.js (Lei Shi) [#50937](https://github.com/nodejs/node/pull/50937)
- \[[`b80bb1329b`](https://github.com/nodejs/node/commit/b80bb1329b)] - **benchmark**: update iterations in benchmark/util/normalize-encoding.js (Lei Shi) [#50934](https://github.com/nodejs/node/pull/50934)
- \[[`dbee03d646`](https://github.com/nodejs/node/commit/dbee03d646)] - **benchmark**: update iterations in benchmark/util/inspect-array.js (Lei Shi) [#50933](https://github.com/nodejs/node/pull/50933)
- \[[`f2d83a3a84`](https://github.com/nodejs/node/commit/f2d83a3a84)] - **benchmark**: update iterations in benchmark/util/format.js (Lei Shi) [#50932](https://github.com/nodejs/node/pull/50932)
- \[[`2581fce553`](https://github.com/nodejs/node/commit/2581fce553)] - **bootstrap**: improve snapshot unsupported builtin warnings (Joyee Cheung) [#50944](https://github.com/nodejs/node/pull/50944)
- \[[`735bad3694`](https://github.com/nodejs/node/commit/735bad3694)] - **build**: fix warnings from uv for gn build (Cheng Zhao) [#51069](https://github.com/nodejs/node/pull/51069)
- \[[`8da9d969f9`](https://github.com/nodejs/node/commit/8da9d969f9)] - **deps**: V8: cherry-pick 0fd478bcdabd (Joyee Cheung) [#50572](https://github.com/nodejs/node/pull/50572)
- \[[`429fbb37c1`](https://github.com/nodejs/node/commit/429fbb37c1)] - **deps**: update simdjson to v3.6.2 (Yagiz Nizipli) [#50986](https://github.com/nodejs/node/pull/50986)
- \[[`9950103253`](https://github.com/nodejs/node/commit/9950103253)] - **deps**: update zlib to 1.3-22124f5 (Node.js GitHub Bot) [#50910](https://github.com/nodejs/node/pull/50910)
- \[[`0b61823e8b`](https://github.com/nodejs/node/commit/0b61823e8b)] - **deps**: update undici to 5.28.2 (Node.js GitHub Bot) [#51024](https://github.com/nodejs/node/pull/51024)
- \[[`95d8a273cc`](https://github.com/nodejs/node/commit/95d8a273cc)] - **deps**: cherry-pick bfbe4e38d7 from libuv upstream (Abdirahim Musse) [#50650](https://github.com/nodejs/node/pull/50650)
- \[[`06038a489e`](https://github.com/nodejs/node/commit/06038a489e)] - **deps**: update libuv to 1.47.0 (Node.js GitHub Bot) [#50650](https://github.com/nodejs/node/pull/50650)
- \[[`0dd53da722`](https://github.com/nodejs/node/commit/0dd53da722)] - **(SEMVER-MINOR)** **deps**: add simdjson (Yagiz Nizipli) [#50322](https://github.com/nodejs/node/pull/50322)
- \[[`04eaa5cdd7`](https://github.com/nodejs/node/commit/04eaa5cdd7)] - **doc**: run license-builder (github-actions\[bot]) [#51111](https://github.com/nodejs/node/pull/51111)
- \[[`26ed4ad01f`](https://github.com/nodejs/node/commit/26ed4ad01f)] - **doc**: deprecate hash constructor (Marco Ippolito) [#51077](https://github.com/nodejs/node/pull/51077)
- \[[`637ffce4c4`](https://github.com/nodejs/node/commit/637ffce4c4)] - **doc**: add note regarding `--experimental-detect-module` (Shubherthi Mitra) [#51089](https://github.com/nodejs/node/pull/51089)
- \[[`838179b096`](https://github.com/nodejs/node/commit/838179b096)] - **doc**: correct tracingChannel.traceCallback() (Gerhard Stöbich) [#51068](https://github.com/nodejs/node/pull/51068)
- \[[`539bee4f0a`](https://github.com/nodejs/node/commit/539bee4f0a)] - **doc**: use length argument in pbkdf2Key (Tobias Nießen) [#51066](https://github.com/nodejs/node/pull/51066)
- \[[`c45a9a3187`](https://github.com/nodejs/node/commit/c45a9a3187)] - **doc**: add deprecation notice to `dirent.path` (Antoine du Hamel) [#51059](https://github.com/nodejs/node/pull/51059)
- \[[`58ca66a1a7`](https://github.com/nodejs/node/commit/58ca66a1a7)] - **doc**: deprecate `dirent.path` (Antoine du Hamel) [#51020](https://github.com/nodejs/node/pull/51020)
- \[[`c2b6edf9ab`](https://github.com/nodejs/node/commit/c2b6edf9ab)] - **esm**: fix hook name in error message (Bruce MacNaughton) [#50466](https://github.com/nodejs/node/pull/50466)
- \[[`35e8f26f07`](https://github.com/nodejs/node/commit/35e8f26f07)] - **fs**: throw fchownSync error from c++ (Yagiz Nizipli) [#51075](https://github.com/nodejs/node/pull/51075)
- \[[`c3c8237089`](https://github.com/nodejs/node/commit/c3c8237089)] - **fs**: update params in jsdoc for createReadStream and createWriteStream (Jungku Lee) [#51063](https://github.com/nodejs/node/pull/51063)
- \[[`3f7f3ce8c9`](https://github.com/nodejs/node/commit/3f7f3ce8c9)] - **fs**: improve error performance of readvSync (IlyasShabi) [#50100](https://github.com/nodejs/node/pull/50100)
- \[[`7f95926f17`](https://github.com/nodejs/node/commit/7f95926f17)] - **http**: handle multi-value content-disposition header (Arsalan Ahmad) [#50977](https://github.com/nodejs/node/pull/50977)
- \[[`7a8a2d5632`](https://github.com/nodejs/node/commit/7a8a2d5632)] - **lib**: don't parse windows drive letters as schemes (华) [#50580](https://github.com/nodejs/node/pull/50580)
- \[[`aa2be4bb76`](https://github.com/nodejs/node/commit/aa2be4bb76)] - **module**: load source maps in `commonjs` translator (Hiroki Osame) [#51033](https://github.com/nodejs/node/pull/51033)
- \[[`c0e5e74876`](https://github.com/nodejs/node/commit/c0e5e74876)] - **module**: document `parentURL` in register options (Hiroki Osame) [#51039](https://github.com/nodejs/node/pull/51039)
- \[[`4eedf5e694`](https://github.com/nodejs/node/commit/4eedf5e694)] - **module**: fix recently introduced coverity warning (Michael Dawson) [#50843](https://github.com/nodejs/node/pull/50843)
- \[[`9f54987fbc`](https://github.com/nodejs/node/commit/9f54987fbc)] - **module**: merge config with `package_json_reader` (Yagiz Nizipli) [#50322](https://github.com/nodejs/node/pull/50322)
- \[[`5f95dca638`](https://github.com/nodejs/node/commit/5f95dca638)] - **node-api**: introduce experimental feature flags (Gabriel Schulhof) [#50991](https://github.com/nodejs/node/pull/50991)
- \[[`3fb7fc909e`](https://github.com/nodejs/node/commit/3fb7fc909e)] - **quic**: further implementation details (James M Snell) [#48244](https://github.com/nodejs/node/pull/48244)
- \[[`fa25e069fc`](https://github.com/nodejs/node/commit/fa25e069fc)] - **src**: implement countObjectsWithPrototype (Joyee Cheung) [#50572](https://github.com/nodejs/node/pull/50572)
- \[[`abe90527e4`](https://github.com/nodejs/node/commit/abe90527e4)] - **src**: register udp_wrap external references (Joyee Cheung) [#50943](https://github.com/nodejs/node/pull/50943)
- \[[`84e2f51d14`](https://github.com/nodejs/node/commit/84e2f51d14)] - **src**: register spawn_sync external references (Joyee Cheung) [#50943](https://github.com/nodejs/node/pull/50943)
- \[[`2cfee53d7b`](https://github.com/nodejs/node/commit/2cfee53d7b)] - **src**: register process_wrap external references (Joyee Cheung) [#50943](https://github.com/nodejs/node/pull/50943)
- \[[`9b7f79a8bd`](https://github.com/nodejs/node/commit/9b7f79a8bd)] - **src**: fix double free reported by coverity (Michael Dawson) [#51046](https://github.com/nodejs/node/pull/51046)
- \[[`fc5503246e`](https://github.com/nodejs/node/commit/fc5503246e)] - **src**: remove unused headers in `node_file.cc` (Jungku Lee) [#50927](https://github.com/nodejs/node/pull/50927)
- \[[`c3abdc58af`](https://github.com/nodejs/node/commit/c3abdc58af)] - **src**: implement --trace-promises (Joyee Cheung) [#50899](https://github.com/nodejs/node/pull/50899)
- \[[`f90fc83e97`](https://github.com/nodejs/node/commit/f90fc83e97)] - **src**: fix dynamically linked zlib version (Richard Lau) [#51007](https://github.com/nodejs/node/pull/51007)
- \[[`9bf144379f`](https://github.com/nodejs/node/commit/9bf144379f)] - **src**: omit bool values of package.json main field (Yagiz Nizipli) [#50965](https://github.com/nodejs/node/pull/50965)
- \[[`45e4f82912`](https://github.com/nodejs/node/commit/45e4f82912)] - **src**: move package resolver to c++ (Yagiz Nizipli) [#50322](https://github.com/nodejs/node/pull/50322)
- \[[`71acd36778`](https://github.com/nodejs/node/commit/71acd36778)] - **stream**: implement TransformStream cleanup using "transformer.cancel" (Debadree Chatterjee) [#50126](https://github.com/nodejs/node/pull/50126)
- \[[`5112306064`](https://github.com/nodejs/node/commit/5112306064)] - **stream**: fix fd is null when calling clearBuffer (kylo5aby) [#50994](https://github.com/nodejs/node/pull/50994)
- \[[`ed070755ec`](https://github.com/nodejs/node/commit/ed070755ec)] - **test**: deflake test-diagnostics-channel-memory-leak (Joyee Cheung) [#50572](https://github.com/nodejs/node/pull/50572)
- \[[`aee01ff1b4`](https://github.com/nodejs/node/commit/aee01ff1b4)] - **test**: test syncrhnous methods of child_process in snapshot (Joyee Cheung) [#50943](https://github.com/nodejs/node/pull/50943)
- \[[`cc949869a3`](https://github.com/nodejs/node/commit/cc949869a3)] - **test**: handle relative https redirect (Richard Lau) [#51121](https://github.com/nodejs/node/pull/51121)
- \[[`048349ed4c`](https://github.com/nodejs/node/commit/048349ed4c)] - **test**: fix test runner colored output test (Moshe Atlow) [#51064](https://github.com/nodejs/node/pull/51064)
- \[[`7f5291d783`](https://github.com/nodejs/node/commit/7f5291d783)] - **test**: resolve path of embedtest binary correctly (Cheng Zhao) [#50276](https://github.com/nodejs/node/pull/50276)
- \[[`4ddd0daf5f`](https://github.com/nodejs/node/commit/4ddd0daf5f)] - **test**: escape cwd in regexp (Jérémy Lal) [#50980](https://github.com/nodejs/node/pull/50980)
- \[[`3ccd5faabb`](https://github.com/nodejs/node/commit/3ccd5faabb)] - **test_runner**: format coverage report for tap reporter (Pulkit Gupta) [#51119](https://github.com/nodejs/node/pull/51119)
- \[[`d5c9adf3df`](https://github.com/nodejs/node/commit/d5c9adf3df)] - **test_runner**: fix infinite loop when files are undefined in test runner (Pulkit Gupta) [#51047](https://github.com/nodejs/node/pull/51047)
- \[[`328a41701c`](https://github.com/nodejs/node/commit/328a41701c)] - **tools**: update lint-md-dependencies to rollup\@4.7.0 (Node.js GitHub Bot) [#51106](https://github.com/nodejs/node/pull/51106)
- \[[`297cb6f5c2`](https://github.com/nodejs/node/commit/297cb6f5c2)] - **tools**: update doc to highlight.js\@11.9.0 unified\@11.0.4 (Node.js GitHub Bot) [#50459](https://github.com/nodejs/node/pull/50459)
- \[[`4705023343`](https://github.com/nodejs/node/commit/4705023343)] - **tools**: fix simdjson updater (Yagiz Nizipli) [#50986](https://github.com/nodejs/node/pull/50986)
- \[[`c9841583db`](https://github.com/nodejs/node/commit/c9841583db)] - **tools**: update eslint to 8.55.0 (Node.js GitHub Bot) [#51025](https://github.com/nodejs/node/pull/51025)
- \[[`2b4671125e`](https://github.com/nodejs/node/commit/2b4671125e)] - **tools**: update lint-md-dependencies to rollup\@4.6.1 (Node.js GitHub Bot) [#51022](https://github.com/nodejs/node/pull/51022)
- \[[`cd891b37f6`](https://github.com/nodejs/node/commit/cd891b37f6)] - **util**: improve performance of function areSimilarFloatArrays (Liu Jia) [#51040](https://github.com/nodejs/node/pull/51040)
- \[[`e178a43509`](https://github.com/nodejs/node/commit/e178a43509)] - **vm**: use v8::DeserializeInternalFieldsCallback explicitly (Joyee Cheung) [#50984](https://github.com/nodejs/node/pull/50984)
- \[[`fd028e146f`](https://github.com/nodejs/node/commit/fd028e146f)] - **win,tools**: upgrade Windows signing to smctl (Stefan Stojanovic) [#50956](https://github.com/nodejs/node/pull/50956)

Windows 32-bit Installer: https://nodejs.org/dist/v21.5.0/node-v21.5.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v21.5.0/node-v21.5.0-x64.msi \
Windows ARM 64-bit Installer: https://nodejs.org/dist/v21.5.0/node-v21.5.0-arm64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v21.5.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v21.5.0/win-x64/node.exe \
Windows ARM 64-bit Binary: https://nodejs.org/dist/v21.5.0/win-arm64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v21.5.0/node-v21.5.0.pkg \
macOS Apple Silicon 64-bit Binary: https://nodejs.org/dist/v21.5.0/node-v21.5.0-darwin-arm64.tar.gz \
macOS Intel 64-bit Binary: https://nodejs.org/dist/v21.5.0/node-v21.5.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v21.5.0/node-v21.5.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v21.5.0/node-v21.5.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v21.5.0/node-v21.5.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v21.5.0/node-v21.5.0-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v21.5.0/node-v21.5.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v21.5.0/node-v21.5.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v21.5.0/node-v21.5.0.tar.gz \
Other release files: https://nodejs.org/dist/v21.5.0/ \
Documentation: https://nodejs.org/docs/v21.5.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

e4b4c3214a8dc59f511f50cb949b4ef5c05a59bc15c6376c1b98bcfd43dcbb4c  node-v21.5.0-aix-ppc64.tar.gz
aaf26bd76a2c771d623bf2aa68f8fa4b440382ddc5b3d6e26bc4a21ab63271da  node-v21.5.0-arm64.msi
66af9055dcceacc2fcd01ccbb47a565a1dd8f4314300a0f37f13771059490a09  node-v21.5.0-darwin-arm64.tar.gz
64f7eb52c9eb325326cc33c3b11ce90b00859616209bbd436c0acb1e612ef389  node-v21.5.0-darwin-arm64.tar.xz
de858cb5257f101dc0681cd65493b589a35ac61f69f46b62cbc1cdaf7cb51a1b  node-v21.5.0-darwin-x64.tar.gz
e396e2bf2e999215bfd5cf50b8050dd5072a7d74a63d4047a6a9e71f0a9fc553  node-v21.5.0-darwin-x64.tar.xz
eeb9270798f488e58a38f7637b69c6557d1da17573b79b96f50e0f01911053bb  node-v21.5.0-headers.tar.gz
37344fac0560e0e985acba054356c1287b302ba767671233b5633e7674d50d9f  node-v21.5.0-headers.tar.xz
ed8d7c80f301af4546d60bb0d25930ade432a45560d2eecf17c23818c05ce12f  node-v21.5.0-linux-arm64.tar.gz
05183535f1dafe13e456b767de426719f26228e2a08ec19f9b24232876065b4d  node-v21.5.0-linux-arm64.tar.xz
104f1ac9c2799c7fb0467cb19fe1a8f5b2ddd79d3fcd852547940d1cc7e0ca48  node-v21.5.0-linux-armv7l.tar.gz
20a69cbd0ba3ef8fd7fead40fa57971590d7765a66cb3f26fa0b2542ad70ed8a  node-v21.5.0-linux-armv7l.tar.xz
05d954d0072c0583f0745773db7947249567f0a1250056269be7110dde359bcc  node-v21.5.0-linux-ppc64le.tar.gz
6ec7bb999533a3f70ffc4ddf0283d85a8cbba79205e77b080ee4c7f90cd4bc8c  node-v21.5.0-linux-ppc64le.tar.xz
a80587110aa12063eea520df8e1925e715aa7a96ee323745e349be76d976e791  node-v21.5.0-linux-s390x.tar.gz
2c22a05d0f264f00e7a5432f752c4070aded2a3c490beb4b94137e6750addd69  node-v21.5.0-linux-s390x.tar.xz
6e61f81fe1759892fb1f84f62fe470c8d4d6dfc07969af5700f06b4672a9e8d3  node-v21.5.0-linux-x64.tar.gz
7ba86504afb7d865e037a776be114f250710646378313dad02c0885f127af59b  node-v21.5.0-linux-x64.tar.xz
8153e6fed4b7d871bc9ce8be579379ec7535c820d9622a0ac2e2d40601be0b8f  node-v21.5.0.pkg
64f9a761b11be7e55325b029d1e3f1d41795e0e535e770a4b2a2a83c0f4c16da  node-v21.5.0.tar.gz
afd7d4713573cd814f7e4df320de8d5c8e147b4101bc9fbbe2a6d52eb5f8b072  node-v21.5.0.tar.xz
b2cf94d6297b7b71d1a5dfea115025e23eb07629447867a252857387b3bf167c  node-v21.5.0-win-arm64.7z
ac492a4beaeb92367220f68a760e6976c917239074b623b7b8dc6c7411f17097  node-v21.5.0-win-arm64.zip
209c771e3d1b86f6edee19682142999cc1a28664459f6c5258ad2361525525b1  node-v21.5.0-win-x64.7z
25dc31215fb705f8c4c03d0f0f57e248d62313cd5d4651bcc16d832a1b1ebb01  node-v21.5.0-win-x64.zip
0605d26eca7ed6ba6b6030e779c94a99e6c41850fb4a6b9105af67268478f722  node-v21.5.0-win-x86.7z
45a2d47b6b6afc7cd2e0db6da4d3de35d19efe230b943fc84d3b3577bd31d900  node-v21.5.0-win-x86.zip
2e2583deb489fec8b17e8b90145c6bc327531b75544e7f6f8eaa8ee97c6c0853  node-v21.5.0-x64.msi
4b502d084c965bdcb1acc61695cb62a71a468f7f3703c6df52da878ffdf3649a  node-v21.5.0-x86.msi
12a77f8ea5a8f9113f96ad00d43a73616a5fe1de7a474f7e13f30404325e08ad  win-arm64/node.exe
c4fc3b1a81c71efbcc224e8f1cbc007d2be208a673614acda7b0166df199cfcb  win-arm64/node.lib
05b05eaa1ca603ed2c107b41bd7be837d21bff91c5a31350f3f1fd7d71e1e785  win-arm64/node_pdb.7z
bb420b368326da35eedaa0ccab5e8608ab698727b4e51c1f852097d127285909  win-arm64/node_pdb.zip
3384bb0174ee8e90f1d9619d5eda9a549df9b93fdc6ac81b04efd40eec77d0de  win-x64/node.exe
3ad4bbb2e42888da6974e1e9e5e1cfe995255c9b6ea0ec34f4a7d492eb402efa  win-x64/node.lib
0ad3c905d75aee6c52061297669c92c2b3cd02619e36b78a835f65bd701d9cf4  win-x64/node_pdb.7z
029d7068b1181c660873b18eabab197a7609f0df5b2de12524bb99774a52f2b8  win-x64/node_pdb.zip
28823b730743ad7feefc0bd0952dfa40fefc64761398a29f6127f117c9496eae  win-x86/node.exe
191e8a964f21e5ce045ff7d63ee12db5f782a516e888c1a3f4c8da8f0552c22e  win-x86/node.lib
c13d2e35595c779ec9ca165198d1cbea25cd12fe905f32c19ceac90f4ab42dd0  win-x86/node_pdb.7z
353ca4eb8489b65fba11d39c76c864e59b7551c6f27a7cd0f8a6392f7c445eab  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQGzBAEBCAAdFiEEiQwI24V5Fi/uDfnbi+q0389VXvQFAmWB6iAACgkQi+q0389V
XvQvBQv/dKCBhaeeFOGw7xrCovTePoXO6TEifelyr6PmLTyUe+JQePlKmf2xSwEC
OXaGefaUObkptqtBIR6Qg+VPvRasBeARK4tHLCXempAgtNXOgePIBG/xUkXu60M3
sNz1xHZUTWYy/s0x6eIj0NO7cdDUGRPFkmNhoFzXU2Ojtay1Az8yi2d/N3XkTUWM
lQ3iCLZzkb3hQfvU8U0fA4uOCsMmvd1ozQtULh0nkDa0JZxunLOvbrMFyNxQUkpC
ngRgvMzgZH3qckI3QIqoz8r4uq00N4p2mKa+He74hzgmhtDHfI41JozCEX1nO4CE
MIqLlIG5bsOdSYCH6f+dpJ/NVVWdX0gdSA0bEBmBGqCwI9fTcm1wXUQw27+wbIwf
hnilb03nQhcwRwl8nH6fCuozKvceG35sSz30ytBY9sXACVJOrJveKwHNFGjMPHnt
/HYHsDQHLYBUrikucK5g5kMv18IjqB4D45+zDfer1RwkGPhoKpnT8Vi/g8w6QuqR
Ciy0+Kgy
=0SzZ
-----END PGP SIGNATURE-----
```
