---
date: '2021-01-04T18:17:46.782Z'
category: release
title: Node v14.15.4 (LTS)
layout: blog-post
author: <PERSON>
---

### Notable Changes

Vulnerabilities fixed:

- **CVE-2020-1971**: OpenSSL - EDIPARTYNAME NULL pointer de-reference (High)

  - This is a vulnerability in OpenSSL which may be exploited through
    Node.js. You can read more about it in
    https://www.openssl.org/news/secadv/20201208.txt

- **CVE-2020-8265**: use-after-free in TLSWrap (High)

  - Affected Node.js versions are vulnerable to a use-after-free bug in
    its TLS implementation. When writing to a TLS enabled socket,
    node::StreamBase::Write calls node::TLSWrap::DoWrite with a freshly
    allocated WriteWrap object as first argument. If the DoWrite method
    does not return an error, this object is passed back to the caller as
    part of a StreamWriteResult structure. This may be exploited to
    corrupt memory leading to a Denial of Service or potentially other
    exploits.

- **CVE-2020-8287**: HTTP Request Smuggling in nodejs (Low)
  - Affected versions of Node.js allow two copies of a header field in
    a http request. For example, two Transfer-Encoding header fields. In
    this case Node.js identifies the first header field and ignores the
    second. This can lead to HTTP Request Smuggling
    (https://cwe.mitre.org/data/definitions/444.html).

### Commits

- [[`305c0f4977`](https://github.com/nodejs/node/commit/305c0f4977)] - **deps**: upgrade npm to 6.14.10 (Ruy Adorno) [#36571](https://github.com/nodejs/node/pull/36571)
- [[`d62c650f75`](https://github.com/nodejs/node/commit/d62c650f75)] - **deps**: update archs files for OpenSSL-1.1.1i (Myles Borins) [#36521](https://github.com/nodejs/node/pull/36521)
- [[`2de2672eb5`](https://github.com/nodejs/node/commit/2de2672eb5)] - **deps**: upgrade openssl sources to 1.1.1i (Myles Borins) [#36521](https://github.com/nodejs/node/pull/36521)
- [[`7ecac8143f`](https://github.com/nodejs/node/commit/7ecac8143f)] - **http**: add test for http transfer encoding smuggling (Matteo Collina) [nodejs-private/node-private#228](https://github.com/nodejs-private/node-private/pull/228)
- [[`641f786bb1`](https://github.com/nodejs/node/commit/641f786bb1)] - **http**: unset `F_CHUNKED` on new `Transfer-Encoding` (Matteo Collina) [nodejs-private/node-private#228](https://github.com/nodejs-private/node-private/pull/228)
- [[`4f8772f9b7`](https://github.com/nodejs/node/commit/4f8772f9b7)] - **src**: retain pointers to WriteWrap/ShutdownWrap (James M Snell) [nodejs-private/node-private#23](https://github.com/nodejs-private/node-private/pull/23)

Windows 32-bit Installer: https://nodejs.org/dist/v14.15.4/node-v14.15.4-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v14.15.4/node-v14.15.4-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v14.15.4/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v14.15.4/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v14.15.4/node-v14.15.4.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v14.15.4/node-v14.15.4-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v14.15.4/node-v14.15.4-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v14.15.4/node-v14.15.4-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v14.15.4/node-v14.15.4-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v14.15.4/node-v14.15.4-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v14.15.4/node-v14.15.4-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v14.15.4/node-v14.15.4-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v14.15.4/node-v14.15.4.tar.gz \
Other release files: https://nodejs.org/dist/v14.15.4/ \
Documentation: https://nodejs.org/docs/v14.15.4/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

03ac3f7d33f17b762d676988b725c58140b5f9a131c849f9b78cbe7f7f84c234  node-v14.15.4-aix-ppc64.tar.gz
6b0e19e5c2601ef97510f7eb4f52cc8ee261ba14cb05f31eb1a41a5043b0304e  node-v14.15.4-darwin-x64.tar.gz
3078556d474a3e3cd6845d79577ca78c14a3c0ef78d569ae3644834584235e88  node-v14.15.4-darwin-x64.tar.xz
3eb7d4ec4964b10a48987c35c37b3e7720735cf3b9806fe40a805fa1d49a9c7d  node-v14.15.4-headers.tar.gz
048e5486c86c8e9396a5b765744fad85b9a3ae5ba99f7dd472b41fee16446233  node-v14.15.4-headers.tar.xz
b681bda8eaa1ed2ac85e0ed2c2041a0408963c2198a24da183dc3ab60d93d975  node-v14.15.4-linux-arm64.tar.gz
b990bd99679158c3164c55a20c2a6677c3d9e9ffdfa0d4a40afe9c9b5e97a96f  node-v14.15.4-linux-arm64.tar.xz
ffce90b07675434491361dfc74eee230f9ffc65c6c08efb88a18781bcb931871  node-v14.15.4-linux-armv7l.tar.gz
bafe4bfb22b046cdda3475d23cd6999c5ea85180c180c4bbb94014920aa7231b  node-v14.15.4-linux-armv7l.tar.xz
d8b92d3826b7a04b886da92275a64978431fde1b47cc92de48f5fb16131bebb2  node-v14.15.4-linux-ppc64le.tar.gz
b2456aa4fbfeeea2aae2a0b969c0fc45c12cd218ca48461c6031f590c2ee8495  node-v14.15.4-linux-ppc64le.tar.xz
c13f16657074437fd66f888736260af486df89a7d77d8b323655cee6408a8084  node-v14.15.4-linux-s390x.tar.gz
29f794d492eccaf0b08e6492f91162447ad95cfefc213fc580a72e29e11501a9  node-v14.15.4-linux-s390x.tar.xz
b51c033d40246cd26e52978125a3687df5cd02ee532e8614feff0ba6c13a774f  node-v14.15.4-linux-x64.tar.gz
ed01043751f86bb534d8c70b16ab64c956af88fd35a9506b7e4a68f5b8243d8a  node-v14.15.4-linux-x64.tar.xz
86e42bb639ea69165a1527a8af5bcf8908633750645198b1c373be6c6ea9db31  node-v14.15.4.pkg
8610d81b2d4afc3a79afbe6209d510125131c45cae22cac63bb767dcfec1cbf0  node-v14.15.4.tar.gz
adb7ecf66c74b52a14a08cc22bb0f9aedc157cac1ac93240f7f455e8c8edec9c  node-v14.15.4.tar.xz
1da86f92f158e53ee79469daf5e6a3e3f2e6e7c233bbf4b91ab12e1ca9503444  node-v14.15.4-win-x64.7z
b2a0765240f8fbd3ba90a050b8c87069d81db36c9f3745aff7516e833e4d2ed6  node-v14.15.4-win-x64.zip
e631956bbf65cb046c7d38d0f0a4633755e86df1ff5f37c3610d55f706c1f0c0  node-v14.15.4-win-x86.7z
49b2ee6106192894809ce14a2932dca41a6bcf5602ce19413fcb3a477f8613b2  node-v14.15.4-win-x86.zip
346a053dcd7508f1e5fbb2da0e34cbb3da206ab2439c4bab5a219c3b75e62475  node-v14.15.4-x64.msi
2e91ec4aca9bccf105d920bf46b4fbe219aa9dd5439e006f65dc426273cbbdfa  node-v14.15.4-x86.msi
dc105951e62ca3be85b6d42ce1db5dc0fce6adac90c76861d9e62302a43ac6b6  win-x64/node.exe
632ef6fd3d8e23c8b333e0dde5a802c80e5bb08e572ac184d9c8ebf47db2be96  win-x64/node.lib
79aaa8027f4e98bfc3448d2282dde4678bbb345c1639eae2380ffc9d81ee4581  win-x64/node_pdb.7z
54ab0ed636dffac1335b7ac29e045ace732f6eef513fa2c549f6a00e367ecdcc  win-x64/node_pdb.zip
89ceec9ba49002797ab4f0e9753f4eae9f415d773aaf65b41b15640a4313d407  win-x86/node.exe
a6670b0a10985f58d1a66467041926060c16cc6932d3ba490a020f4cb3fd6280  win-x86/node.lib
ed7d37ade431e53738d6ef1481a05ad1cfcbea1aaa20289a134a43b01cdcc5b5  win-x86/node_pdb.7z
81f8634988510fd63fb85eb8ade339c252ce35d8b0a5620b6046e29061b00f87  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQEzBAEBCAAdFiEETtd49TnjY0x3nIfG1wYoSKGrAFwFAl/zWvQACgkQ1wYoSKGr
AFwWTwgAtMV70O16Du5ZGlsQau42xBvMeAN8AvpkkUE7wX/r87cU90gScFKrVTvJ
URYmSu7AI7+nS3euqHU0AFqWEHOWSRXZ/1c83Bb0imLpfYlt6nTE8EZdVI8ul2T5
dyQZe95f/TR+NhjnoC+1Acqstf37BZ96aDSMkEcNWrjNA/jwFgK0ut8juRTefZaZ
3jUMBL3apZosxmw3CR5sbtDJM2c0LX1/5dRajmNnfnSHVvTor2YgRWI++DVrgMz3
bFiwwabKKrFs7ICFOpEzZVADOO84pE/G6wiSP4SkKTjxO6E2JPJNE2bXoPVK3VB3
cuDMZNhKfyg2HxVvy4R6aGwCnF9Big==
=YV6/
-----END PGP SIGNATURE-----

```
