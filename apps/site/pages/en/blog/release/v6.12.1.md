---
date: '2017-12-05T14:20:07.741Z'
category: release
title: Node v6.12.1 (LTS)
layout: blog-post
author: <PERSON><PERSON>
---

### Notable Changes

- **build**:
  - fix npm install with --shared (<PERSON>) [#16438](https://github.com/nodejs/node/pull/16438)
- **build**:
  - building with python 3 is now supported (<PERSON>) [#16058](https://github.com/nodejs/node/pull/16058)
- **src**:
  - v8 options can be specified with either '\_' or '-' in NODE_OPTIONS (<PERSON>) [#14093](https://github.com/nodejs/node/pull/14093)

### Commits

- [[`575a920a16`](https://github.com/nodejs/node/commit/575a920a16)] - **assert**: fix actual and expected order (<PERSON>) [#15866](https://github.com/nodejs/node/pull/15866)
- [[`a0c1d10e91`](https://github.com/nodejs/node/commit/a0c1d10e91)] - **build**: remove cctest extension (<PERSON><PERSON>) [#16680](https://github.com/nodejs/node/pull/16680)
- [[`c287f1235c`](https://github.com/nodejs/node/commit/c287f1235c)] - **build**: include src\tracing when linting on win (Daniel Bevenius) [#16720](https://github.com/nodejs/node/pull/16720)
- [[`706812bc2f`](https://github.com/nodejs/node/commit/706812bc2f)] - **build**: skip bin override on windows (Hitesh Kanwathirtha) [#16460](https://github.com/nodejs/node/pull/16460)
- [[`f4627603aa`](https://github.com/nodejs/node/commit/f4627603aa)] - **build**: fix npm install with --shared (Ben Noordhuis) [#16438](https://github.com/nodejs/node/pull/16438)
- [[`6d63612e93`](https://github.com/nodejs/node/commit/6d63612e93)] - **build**: correct minor typo in lttng help message (Daniel Bevenius) [#16101](https://github.com/nodejs/node/pull/16101)
- [[`de82db7f85`](https://github.com/nodejs/node/commit/de82db7f85)] - **build**: ignore empty folders in test-addons (Gregor) [#16031](https://github.com/nodejs/node/pull/16031)
- [[`ac1beb0fb0`](https://github.com/nodejs/node/commit/ac1beb0fb0)] - **build**: use bin override if no `python` in PATH (Bradley T. Hughes) [#16241](https://github.com/nodejs/node/pull/16241)
- [[`d4b3b633d8`](https://github.com/nodejs/node/commit/d4b3b633d8)] - **build**: allow build with system python 3 (Emily Marigold Klassen) [#16058](https://github.com/nodejs/node/pull/16058)
- [[`fc2ab06014`](https://github.com/nodejs/node/commit/fc2ab06014)] - **build, windows**: use /bigobj for debug builds (Nikolai Vavilov) [#16289](https://github.com/nodejs/node/pull/16289)
- [[`ccca11d026`](https://github.com/nodejs/node/commit/ccca11d026)] - **build,win**: set /MP separately in Debug and Release (Nikolai Vavilov) [#16415](https://github.com/nodejs/node/pull/16415)
- [[`a14f564686`](https://github.com/nodejs/node/commit/a14f564686)] - **build,win**: use /MP for debug builds (Nikolai Vavilov) [#16333](https://github.com/nodejs/node/pull/16333)
- [[`8813867577`](https://github.com/nodejs/node/commit/8813867577)] - **child_process**: set shell to false in fork() (Alex Gresnel) [#15352](https://github.com/nodejs/node/pull/15352)
- [[`f2cafff9b0`](https://github.com/nodejs/node/commit/f2cafff9b0)] - **crypto**: fix error of createCipher in wrap mode (Shigeki Ohtsu) [#15037](https://github.com/nodejs/node/pull/15037)
- [[`7115079c4f`](https://github.com/nodejs/node/commit/7115079c4f)] - **crypto**: warn if counter mode used in createCipher (Shigeki Ohtsu) [#13821](https://github.com/nodejs/node/pull/13821)
- [[`50c3dabc0f`](https://github.com/nodejs/node/commit/50c3dabc0f)] - **deps**: backport 4af8029 from upstream V8 (Michaël Zasso) [#17290](https://github.com/nodejs/node/pull/17290)
- [[`101eb981fe`](https://github.com/nodejs/node/commit/101eb981fe)] - **doc**: mention constant-time in crypto doc (Mithun Sasidharan) [#16604](https://github.com/nodejs/node/pull/16604)
- [[`1bc5c3836c`](https://github.com/nodejs/node/commit/1bc5c3836c)] - **doc**: recommend node-core-utils for metadata (Rich Trott) [#16978](https://github.com/nodejs/node/pull/16978)
- [[`4583f1be0c`](https://github.com/nodejs/node/commit/4583f1be0c)] - **doc**: reorganize COLLABORATOR_GUIDE.md (Rich Trott) [#15710](https://github.com/nodejs/node/pull/15710)
- [[`fce790285f`](https://github.com/nodejs/node/commit/fce790285f)] - **doc**: improve documentation for the vm module (Franziska Hinkelmann) [#16867](https://github.com/nodejs/node/pull/16867)
- [[`727a0fe641`](https://github.com/nodejs/node/commit/727a0fe641)] - **doc**: update subprocess.killed (cjihrig) [#16748](https://github.com/nodejs/node/pull/16748)
- [[`44c0385b04`](https://github.com/nodejs/node/commit/44c0385b04)] - **doc**: more accurate zlib windowBits information (Anna Henningsen) [#16511](https://github.com/nodejs/node/pull/16511)
- [[`732af9b8a4`](https://github.com/nodejs/node/commit/732af9b8a4)] - **doc**: add Gibson Fahnestock to Release team (Gibson Fahnestock) [#16620](https://github.com/nodejs/node/pull/16620)
- [[`935b15285f`](https://github.com/nodejs/node/commit/935b15285f)] - **doc**: slightly relax 50 character rule (James M Snell) [#16523](https://github.com/nodejs/node/pull/16523)
- [[`39c63da6d2`](https://github.com/nodejs/node/commit/39c63da6d2)] - **doc**: add note to releases.md (Jon Moss) [#16507](https://github.com/nodejs/node/pull/16507)
- [[`60ae428f30`](https://github.com/nodejs/node/commit/60ae428f30)] - **doc**: add dot in documentations (erwinwahyura) [#16542](https://github.com/nodejs/node/pull/16542)
- [[`7ae23b744b`](https://github.com/nodejs/node/commit/7ae23b744b)] - **doc**: fix missing newline character (Daijiro Wachi) [#16447](https://github.com/nodejs/node/pull/16447)
- [[`af869f03c1`](https://github.com/nodejs/node/commit/af869f03c1)] - **doc**: add recommendations for first timers (Refael Ackermann) [#16350](https://github.com/nodejs/node/pull/16350)
- [[`b7d609c2f8`](https://github.com/nodejs/node/commit/b7d609c2f8)] - **doc**: replace undocumented encoding aliases (Vse Mozhet Byt) [#16368](https://github.com/nodejs/node/pull/16368)
- [[`2cbf75da7e`](https://github.com/nodejs/node/commit/2cbf75da7e)] - **doc**: replace methods used in the example code (Damian) [#16416](https://github.com/nodejs/node/pull/16416)
- [[`0b5a0ada2a`](https://github.com/nodejs/node/commit/0b5a0ada2a)] - **doc**: fix comment in assert.md (umatoma) [#16335](https://github.com/nodejs/node/pull/16335)
- [[`4fbc490704`](https://github.com/nodejs/node/commit/4fbc490704)] - **doc**: add space after period (Diego Rodríguez Baquero) [#16334](https://github.com/nodejs/node/pull/16334)
- [[`c3cc0fd258`](https://github.com/nodejs/node/commit/c3cc0fd258)] - **doc**: minor correction to note on process section (Daniel Bevenius) [#16311](https://github.com/nodejs/node/pull/16311)
- [[`47bf494979`](https://github.com/nodejs/node/commit/47bf494979)] - **doc**: add apapirovski to collaborators (Anatoli Papirovski) [#16302](https://github.com/nodejs/node/pull/16302)
- [[`9c96d7f4fd`](https://github.com/nodejs/node/commit/9c96d7f4fd)] - **doc**: clarify os.cpus() returns logical CPU cores (Luke Childs) [#16282](https://github.com/nodejs/node/pull/16282)
- [[`ba62b0e48a`](https://github.com/nodejs/node/commit/ba62b0e48a)] - **doc**: support multidimensional arrays in type link (Vse Mozhet Byt) [#16207](https://github.com/nodejs/node/pull/16207)
- [[`aefaed40f0`](https://github.com/nodejs/node/commit/aefaed40f0)] - **doc**: move Shigeki to TSC Emeritus (Rich Trott) [#16195](https://github.com/nodejs/node/pull/16195)
- [[`1fdcf75f9c`](https://github.com/nodejs/node/commit/1fdcf75f9c)] - **doc**: Update a typo in module.js' comments (Orta) [#16205](https://github.com/nodejs/node/pull/16205)
- [[`799c6fdc1c`](https://github.com/nodejs/node/commit/799c6fdc1c)] - **doc**: add missing comma (Jon Moss) [#16204](https://github.com/nodejs/node/pull/16204)
- [[`8c070f9ed5`](https://github.com/nodejs/node/commit/8c070f9ed5)] - **doc**: added note to fs.watchFile on previousStat (NiveditN) [#16099](https://github.com/nodejs/node/pull/16099)
- [[`2515cad90e`](https://github.com/nodejs/node/commit/2515cad90e)] - **doc**: ensure collaborators validate commits (Bradley Farias) [#16162](https://github.com/nodejs/node/pull/16162)
- [[`7647d41da1`](https://github.com/nodejs/node/commit/7647d41da1)] - **doc**: move 8 collaborators to emeriti (Rich Trott) [#16173](https://github.com/nodejs/node/pull/16173)
- [[`de8155ebf2`](https://github.com/nodejs/node/commit/de8155ebf2)] - **doc**: include V8 commit URL in V8 backport guide (Gibson Fahnestock) [#16054](https://github.com/nodejs/node/pull/16054)
- [[`6f1ba792d7`](https://github.com/nodejs/node/commit/6f1ba792d7)] - **doc**: add pronoun for fhinkel (F. Hinkelmann) [#16069](https://github.com/nodejs/node/pull/16069)
- [[`8da3b51472`](https://github.com/nodejs/node/commit/8da3b51472)] - **doc**: document windows shell support (Tim Ermilov) [#16104](https://github.com/nodejs/node/pull/16104)
- [[`281023b20d`](https://github.com/nodejs/node/commit/281023b20d)] - **doc**: exempt test/doc only changes from 48-hr rule (Anna Henningsen) [#16135](https://github.com/nodejs/node/pull/16135)
- [[`04d5835722`](https://github.com/nodejs/node/commit/04d5835722)] - **doc**: rename good first contrib label (Jeremiah Senkpiel) [#16150](https://github.com/nodejs/node/pull/16150)
- [[`1064258f9d`](https://github.com/nodejs/node/commit/1064258f9d)] - **doc**: remove bold typography from STYLE_GUIDE.md (Rich Trott) [#16085](https://github.com/nodejs/node/pull/16085)
- [[`23e9bba9c8`](https://github.com/nodejs/node/commit/23e9bba9c8)] - **doc**: ctc -\> tsc in onboarding extras (Bryan English) [#15621](https://github.com/nodejs/node/pull/15621)
- [[`ff66d63642`](https://github.com/nodejs/node/commit/ff66d63642)] - **doc**: fix emitKeypressEvents stream type (Oblosys) [#15399](https://github.com/nodejs/node/pull/15399)
- [[`1bd6962842`](https://github.com/nodejs/node/commit/1bd6962842)] - **doc**: make stream.Readable consistent (Sakthipriyan Vairamani (thefourtheye)) [#16786](https://github.com/nodejs/node/pull/16786)
- [[`6b9bd51021`](https://github.com/nodejs/node/commit/6b9bd51021)] - **doc**: correct effects to affects (gowpen) [#16794](https://github.com/nodejs/node/pull/16794)
- [[`6af9311939`](https://github.com/nodejs/node/commit/6af9311939)] - **doc**: correct EventEmitter reference (gowpen) [#16791](https://github.com/nodejs/node/pull/16791)
- [[`1a633e3cd8`](https://github.com/nodejs/node/commit/1a633e3cd8)] - **doc**: add docs for Zlib#close() (Luigi Pinca) [#16592](https://github.com/nodejs/node/pull/16592)
- [[`290df5ac41`](https://github.com/nodejs/node/commit/290df5ac41)] - **doc**: add details about rss on process.memoryUsage (Anthony Nandaa) [#16566](https://github.com/nodejs/node/pull/16566)
- [[`3e6da45ce0`](https://github.com/nodejs/node/commit/3e6da45ce0)] - **doc**: howto decode buffers extending from Writable (dicearr) [#16403](https://github.com/nodejs/node/pull/16403)
- [[`c64ed977fc`](https://github.com/nodejs/node/commit/c64ed977fc)] - **doc, win**: remove note about resize (Bartosz Sosnowski) [#16320](https://github.com/nodejs/node/pull/16320)
- [[`644989cf6a`](https://github.com/nodejs/node/commit/644989cf6a)] - **fs**: use Number::New since all fields are uint64_t (Huáng Jùnliàng) [#16705](https://github.com/nodejs/node/pull/16705)
- [[`925e58fecb`](https://github.com/nodejs/node/commit/925e58fecb)] - **fs**: fix stat dev unsigned cast overflow (Huáng Jùnliàng) [#16705](https://github.com/nodejs/node/pull/16705)
- [[`92b13e455f`](https://github.com/nodejs/node/commit/92b13e455f)] - **https**: Use secureProtocol in Agent#getName (Andreas Lind) [#9452](https://github.com/nodejs/node/pull/9452)
- [[`b0ac76d145`](https://github.com/nodejs/node/commit/b0ac76d145)] - **meta**: add note about email sync to CONTRIBUTING.md (Vse Mozhet Byt) [#16340](https://github.com/nodejs/node/pull/16340)
- [[`bf7f63d51b`](https://github.com/nodejs/node/commit/bf7f63d51b)] - **net**: change assert to conform to other files (James Hodgskiss) [#15861](https://github.com/nodejs/node/pull/15861)
- [[`ae3ad5502b`](https://github.com/nodejs/node/commit/ae3ad5502b)] - **src**: remove unused includes from node_wrap.h (Daniel Bevenius) [#16179](https://github.com/nodejs/node/pull/16179)
- [[`a368e5fa63`](https://github.com/nodejs/node/commit/a368e5fa63)] - **src**: make StreamBase prototype accessors robust (Joyee Cheung) [#16860](https://github.com/nodejs/node/pull/16860)
- [[`c79dd9e3ce`](https://github.com/nodejs/node/commit/c79dd9e3ce)] - **src**: CHECK() for argument overflow in Spawn() (cjihrig) [#16761](https://github.com/nodejs/node/pull/16761)
- [[`7c69ca58e0`](https://github.com/nodejs/node/commit/7c69ca58e0)] - **src**: move handle properties to prototype (Ben Noordhuis) [#16482](https://github.com/nodejs/node/pull/16482)
- [[`c87a620ed8`](https://github.com/nodejs/node/commit/c87a620ed8)] - **src**: remove superfluous HandleScope (Ben Noordhuis) [#16482](https://github.com/nodejs/node/pull/16482)
- [[`2f5edc6fd5`](https://github.com/nodejs/node/commit/2f5edc6fd5)] - **src**: remove unused include in tty_wrap.h (Daniel Bevenius) [#16379](https://github.com/nodejs/node/pull/16379)
- [[`42cb64ee91`](https://github.com/nodejs/node/commit/42cb64ee91)] - **src**: fix etw provider include on Windows (Joyee Cheung) [#16639](https://github.com/nodejs/node/pull/16639)
- [[`b00ced5b52`](https://github.com/nodejs/node/commit/b00ced5b52)] - **src**: do not include x.h if x-inl.h is included (Joyee Cheung) [#16548](https://github.com/nodejs/node/pull/16548)
- [[`a4688b0c43`](https://github.com/nodejs/node/commit/a4688b0c43)] - **src**: make header file self-contained (Joyee Cheung) [#16518](https://github.com/nodejs/node/pull/16518)
- [[`cf80089477`](https://github.com/nodejs/node/commit/cf80089477)] - **src**: node_dtrace line continuations clean up (Daniel Bevenius) [#15777](https://github.com/nodejs/node/pull/15777)
- [[`4639cfff0a`](https://github.com/nodejs/node/commit/4639cfff0a)] - **src**: rename perfctr_macros.py-\>noperfctr_macros.py (Daniel Bevenius) [#16100](https://github.com/nodejs/node/pull/16100)
- [[`70f574e6ff`](https://github.com/nodejs/node/commit/70f574e6ff)] - **src**: add help for NODE_PENDING_DEPRECATION env (Thomas Corbière) [#15609](https://github.com/nodejs/node/pull/15609)
- [[`75b1e30e6b`](https://github.com/nodejs/node/commit/75b1e30e6b)] - **src**: add --pending-deprecation to NODE_OPTIONS (Thomas Corbière) [#15494](https://github.com/nodejs/node/pull/15494)
- [[`f659e49862`](https://github.com/nodejs/node/commit/f659e49862)] - **src**: whitelist v8 options with '\_' or '-' (Sam Roberts) [#14093](https://github.com/nodejs/node/pull/14093)
- [[`79171e0c2f`](https://github.com/nodejs/node/commit/79171e0c2f)] - **src**: turn key length exception into CHECK (Ben Noordhuis) [#15183](https://github.com/nodejs/node/pull/15183)
- [[`e18df46092`](https://github.com/nodejs/node/commit/e18df46092)] - **src**: notify V8 for low memory when alloc fails (Anna Henningsen) [#8482](https://github.com/nodejs/node/pull/8482)
- [[`6a0eb9f6cf`](https://github.com/nodejs/node/commit/6a0eb9f6cf)] - **src**: provide allocation + nullptr check shortcuts (Anna Henningsen) [#8482](https://github.com/nodejs/node/pull/8482)
- [[`4aec8cfcd2`](https://github.com/nodejs/node/commit/4aec8cfcd2)] - **src**: pass desired return type to allocators (Anna Henningsen) [#8482](https://github.com/nodejs/node/pull/8482)
- [[`19f3ac9749`](https://github.com/nodejs/node/commit/19f3ac9749)] - **src**: add Malloc() size param + overflow detection (Anna Henningsen) [#8482](https://github.com/nodejs/node/pull/8482)
- [[`6269ba334d`](https://github.com/nodejs/node/commit/6269ba334d)] - **test**: allow tests to pass without internet (Daniel Bevenius) [#16255](https://github.com/nodejs/node/pull/16255)
- [[`f0eeddb4b8`](https://github.com/nodejs/node/commit/f0eeddb4b8)] - **test**: reuse existing PassThrough implementation (Tobias Nießen) [#16936](https://github.com/nodejs/node/pull/16936)
- [[`4752fc4336`](https://github.com/nodejs/node/commit/4752fc4336)] - **test**: refactor comments in test-child-process-spawnsync-maxbuf (ChrBergert) [#16829](https://github.com/nodejs/node/pull/16829)
- [[`f226ca6b12`](https://github.com/nodejs/node/commit/f226ca6b12)] - **test**: used fixturesDir from fixtures modules (Klemen Kogovsek) [#16813](https://github.com/nodejs/node/pull/16813)
- [[`5e2231e407`](https://github.com/nodejs/node/commit/5e2231e407)] - **test**: add a test description (Grant Gasparyan) [#16833](https://github.com/nodejs/node/pull/16833)
- [[`a8cff7ad4a`](https://github.com/nodejs/node/commit/a8cff7ad4a)] - **test**: use common/fixtures module in hash-seed test (Javier Blanco) [#16823](https://github.com/nodejs/node/pull/16823)
- [[`090cc9713e`](https://github.com/nodejs/node/commit/090cc9713e)] - **test**: improve template value for test message (Stephan Smith) [#16826](https://github.com/nodejs/node/pull/16826)
- [[`1d3793eb77`](https://github.com/nodejs/node/commit/1d3793eb77)] - **test**: change concatenated string to template (Deepthi Sebastian) [#16929](https://github.com/nodejs/node/pull/16929)
- [[`79dfc3f475`](https://github.com/nodejs/node/commit/79dfc3f475)] - **test**: change concatenated string to template (Anawesha Khuntia) [#16912](https://github.com/nodejs/node/pull/16912)
- [[`2232231d4f`](https://github.com/nodejs/node/commit/2232231d4f)] - **test**: change string concatenation to template (Suryanarayana Murthy N) [#16919](https://github.com/nodejs/node/pull/16919)
- [[`674cbf8402`](https://github.com/nodejs/node/commit/674cbf8402)] - **test**: replace string concatenation with template (Kabir Islam) [#16916](https://github.com/nodejs/node/pull/16916)
- [[`969defaae9`](https://github.com/nodejs/node/commit/969defaae9)] - **test**: enable mustCall() during child exit (Vipin Menon) [#16915](https://github.com/nodejs/node/pull/16915)
- [[`9d4abaa243`](https://github.com/nodejs/node/commit/9d4abaa243)] - **test**: replace string concatenation with template (Tanvi Kini) [#16913](https://github.com/nodejs/node/pull/16913)
- [[`2a1ebae567`](https://github.com/nodejs/node/commit/2a1ebae567)] - **test**: cover vm.runInNewContext() (cjihrig) [#16906](https://github.com/nodejs/node/pull/16906)
- [[`2043ce39d5`](https://github.com/nodejs/node/commit/2043ce39d5)] - **test**: improve assertion messages (Neil Vass) [#16885](https://github.com/nodejs/node/pull/16885)
- [[`668644008e`](https://github.com/nodejs/node/commit/668644008e)] - **test**: improve assert messages in stream test (Katie Stockton Roberts) [#16884](https://github.com/nodejs/node/pull/16884)
- [[`714eb0bc7c`](https://github.com/nodejs/node/commit/714eb0bc7c)] - **test**: improve assertion in test-require-dot (Adam Wegrzynek) [#16805](https://github.com/nodejs/node/pull/16805)
- [[`8e5b4f543c`](https://github.com/nodejs/node/commit/8e5b4f543c)] - **test**: add values to error message (Adam Jeffery) [#16831](https://github.com/nodejs/node/pull/16831)
- [[`b3b7858a97`](https://github.com/nodejs/node/commit/b3b7858a97)] - **test**: replace common.fixtiresDir with fixtures.readKey() (woj) [#16817](https://github.com/nodejs/node/pull/16817)
- [[`3acf156b68`](https://github.com/nodejs/node/commit/3acf156b68)] - **test**: remove message argument in cluster setup test (mbornath) [#16838](https://github.com/nodejs/node/pull/16838)
- [[`cedf8a1cb2`](https://github.com/nodejs/node/commit/cedf8a1cb2)] - **test**: move test-http-keepalive-maxsockets to sequential (Rich Trott) [#16777](https://github.com/nodejs/node/pull/16777)
- [[`ffbb4e68e8`](https://github.com/nodejs/node/commit/ffbb4e68e8)] - **test**: use default assertion message (jonask) [#16819](https://github.com/nodejs/node/pull/16819)
- [[`dd558a56af`](https://github.com/nodejs/node/commit/dd558a56af)] - **test**: include file mode in assert message (Sascha Tandel) [#16815](https://github.com/nodejs/node/pull/16815)
- [[`3d8b3f7b4a`](https://github.com/nodejs/node/commit/3d8b3f7b4a)] - **test**: refactor tls test to use fixtres.readSync (Brian O'Connell) [#16816](https://github.com/nodejs/node/pull/16816)
- [[`54d4557199`](https://github.com/nodejs/node/commit/54d4557199)] - **test**: use fixtures module in test-repl (Maring, Damian Lion) [#16809](https://github.com/nodejs/node/pull/16809)
- [[`9f9e824fc5`](https://github.com/nodejs/node/commit/9f9e824fc5)] - **test**: update test to use fixtures.readKey (Dara Hayes) [#16811](https://github.com/nodejs/node/pull/16811)
- [[`a99755f3fd`](https://github.com/nodejs/node/commit/a99755f3fd)] - **test**: fix typos in read-buffer tests (Jimi van der Woning) [#16834](https://github.com/nodejs/node/pull/16834)
- [[`e7a456a5ee`](https://github.com/nodejs/node/commit/e7a456a5ee)] - **test**: replace common.fixturesDir with fixtures module (Dumitru Glavan) [#16803](https://github.com/nodejs/node/pull/16803)
- [[`04af0fdab7`](https://github.com/nodejs/node/commit/04af0fdab7)] - **test**: replace common.fixturesDir with fixtures.readSync() (Adri Van Houdt) [#16802](https://github.com/nodejs/node/pull/16802)
- [[`755f5e3fd1`](https://github.com/nodejs/node/commit/755f5e3fd1)] - **test**: update test to use fixtures (Adam Wegrzynek) [#16799](https://github.com/nodejs/node/pull/16799)
- [[`143d8a1b3d`](https://github.com/nodejs/node/commit/143d8a1b3d)] - **test**: fix typo (Oscar Funes) [#15938](https://github.com/nodejs/node/pull/15938)
- [[`84741fdc81`](https://github.com/nodejs/node/commit/84741fdc81)] - **test**: update test-timers-block-eventloop.js (zhangzifa) [#16314](https://github.com/nodejs/node/pull/16314)
- [[`8e62fcb2cf`](https://github.com/nodejs/node/commit/8e62fcb2cf)] - **test**: replace fixturesDir in test-tls-connect (Casie Lynch) [#15849](https://github.com/nodejs/node/pull/15849)
- [[`d6dc579f3c`](https://github.com/nodejs/node/commit/d6dc579f3c)] - **test**: use fixtures module (Iryna Yaremtso) [#15901](https://github.com/nodejs/node/pull/15901)
- [[`10c24a157c`](https://github.com/nodejs/node/commit/10c24a157c)] - **test**: add details in assertions in test-vm-context (Vladimir Ilic) [#16116](https://github.com/nodejs/node/pull/16116)
- [[`cb1d16d26b`](https://github.com/nodejs/node/commit/cb1d16d26b)] - **test**: increase fs.exists coverage (Nigel Kibodeaux) [#15963](https://github.com/nodejs/node/pull/15963)
- [[`d3981ae552`](https://github.com/nodejs/node/commit/d3981ae552)] - **test**: use fixtures module in test-fs-realpath.js (Raphael Rheault) [#15904](https://github.com/nodejs/node/pull/15904)
- [[`532c9606b3`](https://github.com/nodejs/node/commit/532c9606b3)] - **test**: use fixtures module (Scott J Beck) [#15843](https://github.com/nodejs/node/pull/15843)
- [[`58fe9b4ec3`](https://github.com/nodejs/node/commit/58fe9b4ec3)] - **test**: imporove assert messages (Hadis-Fard) [#16021](https://github.com/nodejs/node/pull/16021)
- [[`91f9779794`](https://github.com/nodejs/node/commit/91f9779794)] - **test**: show values instead of assertion message (Cheyenne Arrowsmith) [#15979](https://github.com/nodejs/node/pull/15979)
- [[`0ace5a158d`](https://github.com/nodejs/node/commit/0ace5a158d)] - **test**: include values in assertion messages (nhoel) [#15996](https://github.com/nodejs/node/pull/15996)
- [[`8663b05711`](https://github.com/nodejs/node/commit/8663b05711)] - **test**: use process.features.debug in common module (Rich Trott) [#16537](https://github.com/nodejs/node/pull/16537)
- [[`1fffa165a1`](https://github.com/nodejs/node/commit/1fffa165a1)] - **test**: use common.buildType in repl-domain-abort (Rich Trott) [#16538](https://github.com/nodejs/node/pull/16538)
- [[`7d93da54bb`](https://github.com/nodejs/node/commit/7d93da54bb)] - **test**: skip test-process-config if no config.gypi (Gibson Fahnestock) [#16436](https://github.com/nodejs/node/pull/16436)
- [[`5c20164354`](https://github.com/nodejs/node/commit/5c20164354)] - **test**: use fixtures module in tls-handshake-error (Mark Walker) [#15939](https://github.com/nodejs/node/pull/15939)
- [[`4f04d15aa3`](https://github.com/nodejs/node/commit/4f04d15aa3)] - **test**: add failing vm tests to known_issues (Michaël Zasso) [#16410](https://github.com/nodejs/node/pull/16410)
- [[`2b1042bb29`](https://github.com/nodejs/node/commit/2b1042bb29)] - **test**: allow for different nsswitch.conf settings (Daniel Bevenius) [#16378](https://github.com/nodejs/node/pull/16378)
- [[`5095b991c0`](https://github.com/nodejs/node/commit/5095b991c0)] - **test**: handle blank shells in test-os.js (Gibson Fahnestock) [#16287](https://github.com/nodejs/node/pull/16287)
- [[`62dd6a2c40`](https://github.com/nodejs/node/commit/62dd6a2c40)] - **test**: increase enoughTestMem to 1.75 Gb (Rich Trott) [#16374](https://github.com/nodejs/node/pull/16374)
- [[`9c229b4bd3`](https://github.com/nodejs/node/commit/9c229b4bd3)] - **test**: use fixtures.readKey in https-timeout-server (Nicolas 'Pixel' Noble) [#15871](https://github.com/nodejs/node/pull/15871)
- [[`773652903d`](https://github.com/nodejs/node/commit/773652903d)] - **test**: use fixtures.readKey instead of fixturesDir (Paul Marion Camantigue) [#15976](https://github.com/nodejs/node/pull/15976)
- [[`34dfce7710`](https://github.com/nodejs/node/commit/34dfce7710)] - **test**: replace fixturesDir with fixtures module (tpurcell) [#16262](https://github.com/nodejs/node/pull/16262)
- [[`0a88e1bd60`](https://github.com/nodejs/node/commit/0a88e1bd60)] - **test**: replace fixturesDir with fixtures module (André Føyn Berge) [#15947](https://github.com/nodejs/node/pull/15947)
- [[`9e74e542a2`](https://github.com/nodejs/node/commit/9e74e542a2)] - **test**: skip test due to file size limit (jBarz) [#16273](https://github.com/nodejs/node/pull/16273)
- [[`e070e592dd`](https://github.com/nodejs/node/commit/e070e592dd)] - **test**: remove error msg in test-vm-symbols.js (Daniel Abrão) [#15873](https://github.com/nodejs/node/pull/15873)
- [[`257ece287c`](https://github.com/nodejs/node/commit/257ece287c)] - **test**: remove error messages in test-buffer-alloc (Braden Whitten) [#15867](https://github.com/nodejs/node/pull/15867)
- [[`32fa91519a`](https://github.com/nodejs/node/commit/32fa91519a)] - **test**: update assert error messages (Omar Gonzalez) [#16035](https://github.com/nodejs/node/pull/16035)
- [[`da85e6c552`](https://github.com/nodejs/node/commit/da85e6c552)] - **test**: expand error message (Stefania Sharp) [#15991](https://github.com/nodejs/node/pull/15991)
- [[`cbbe125f71`](https://github.com/nodejs/node/commit/cbbe125f71)] - **test**: use fixtures module (Kanika Shah) [#15959](https://github.com/nodejs/node/pull/15959)
- [[`6f15b011c0`](https://github.com/nodejs/node/commit/6f15b011c0)] - **test**: remove literal messages (Oscar Funes) [#15938](https://github.com/nodejs/node/pull/15938)
- [[`aa269ad59b`](https://github.com/nodejs/node/commit/aa269ad59b)] - **test**: fix stderr reference (Oscar Funes) [#15938](https://github.com/nodejs/node/pull/15938)
- [[`3f35fc063e`](https://github.com/nodejs/node/commit/3f35fc063e)] - **test**: use fixtures module in test-https-truncate (Gene Wu) [#15875](https://github.com/nodejs/node/pull/15875)
- [[`c58eaaf1a8`](https://github.com/nodejs/node/commit/c58eaaf1a8)] - **test**: use fixtures module (Alvaro Cruz) [#15874](https://github.com/nodejs/node/pull/15874)
- [[`48e1320c44`](https://github.com/nodejs/node/commit/48e1320c44)] - **test**: use fixtures module (Lance Barlaan) [#15872](https://github.com/nodejs/node/pull/15872)
- [[`339bdca558`](https://github.com/nodejs/node/commit/339bdca558)] - **test**: use default message for assert.strictEqual (hwaisiu) [#15970](https://github.com/nodejs/node/pull/15970)
- [[`ab580c3ae2`](https://github.com/nodejs/node/commit/ab580c3ae2)] - **test**: improve assert message in internet test (Nikki St Onge) [#15998](https://github.com/nodejs/node/pull/15998)
- [[`6285e7221e`](https://github.com/nodejs/node/commit/6285e7221e)] - **test**: replace common.fixturesDir (Shawn McGinty) [#15834](https://github.com/nodejs/node/pull/15834)
- [[`fa8315cb68`](https://github.com/nodejs/node/commit/fa8315cb68)] - **test**: refactor test-process-kill-null (Luigi Pinca) [#16236](https://github.com/nodejs/node/pull/16236)
- [[`c26abc8e94`](https://github.com/nodejs/node/commit/c26abc8e94)] - **test**: add missing spaces in concatenations (Vse Mozhet Byt) [#16244](https://github.com/nodejs/node/pull/16244)
- [[`a94a75f69a`](https://github.com/nodejs/node/commit/a94a75f69a)] - **test**: update output to include exit code & signal (Jenna Zeigen) [#15945](https://github.com/nodejs/node/pull/15945)
- [[`8eb84d6780`](https://github.com/nodejs/node/commit/8eb84d6780)] - **test**: change common.fixturesDir to fixtures.path (tejbirsingh) [#15860](https://github.com/nodejs/node/pull/15860)
- [[`806f03e54c`](https://github.com/nodejs/node/commit/806f03e54c)] - **test**: split up and refactor test-domain (Anna Henningsen) [#13614](https://github.com/nodejs/node/pull/13614)
- [[`e5fbc03563`](https://github.com/nodejs/node/commit/e5fbc03563)] - **test**: replace fixturesDir with common.fixtures (Kasim Doctor) [#15810](https://github.com/nodejs/node/pull/15810)
- [[`2ab826c497`](https://github.com/nodejs/node/commit/2ab826c497)] - **test**: replaced fs.readSync with fixtures.readSync (Lam Chan) [#15882](https://github.com/nodejs/node/pull/15882)
- [[`1fe3e866cf`](https://github.com/nodejs/node/commit/1fe3e866cf)] - **test**: improve coverage for process.umask (Evan Lucas) [#16188](https://github.com/nodejs/node/pull/16188)
- [[`0689ea66ed`](https://github.com/nodejs/node/commit/0689ea66ed)] - **test**: remove message from notStrictEqual (twk-b) [#16048](https://github.com/nodejs/node/pull/16048)
- [[`fafbbb6347`](https://github.com/nodejs/node/commit/fafbbb6347)] - **test**: use fixtures module (Ben Hallion) [#15808](https://github.com/nodejs/node/pull/15808)
- [[`f2108fa51d`](https://github.com/nodejs/node/commit/f2108fa51d)] - **test**: use ES6 classes instead of util.inherits (Tobias Nießen) [#16938](https://github.com/nodejs/node/pull/16938)
- [[`eb11a70424`](https://github.com/nodejs/node/commit/eb11a70424)] - **test**: refactor test-cluster-setup-master (Jean-Baptiste Brossard) [#16065](https://github.com/nodejs/node/pull/16065)
- [[`e00a4c820f`](https://github.com/nodejs/node/commit/e00a4c820f)] - **test**: replace fixtureDir with fixtures methods (Vladimir Ilic) [#16114](https://github.com/nodejs/node/pull/16114)
- [[`f46e1187b3`](https://github.com/nodejs/node/commit/f46e1187b3)] - **test**: remove error messages in crypto-binary test (Kim Gentes) [#15981](https://github.com/nodejs/node/pull/15981)
- [[`086d8519a1`](https://github.com/nodejs/node/commit/086d8519a1)] - **test**: use fixtures module over fixturesDir (JamesNimlos) [#15847](https://github.com/nodejs/node/pull/15847)
- [[`38179fd1ed`](https://github.com/nodejs/node/commit/38179fd1ed)] - **test**: use common.fixtures module (Shaun Sweet) [#15992](https://github.com/nodejs/node/pull/15992)
- [[`229a1fa299`](https://github.com/nodejs/node/commit/229a1fa299)] - **test**: replace fixturesDir with fixtures.path (Bear Trickey) [#15994](https://github.com/nodejs/node/pull/15994)
- [[`c10594f70f`](https://github.com/nodejs/node/commit/c10594f70f)] - **test**: update fixturesDir import (Tyler Seabrook) [#15887](https://github.com/nodejs/node/pull/15887)
- [[`53449f303f`](https://github.com/nodejs/node/commit/53449f303f)] - **test**: replace fixturesDir with fixtures methods (Komivi Agbakpem) [#15967](https://github.com/nodejs/node/pull/15967)
- [[`a28d666f0e`](https://github.com/nodejs/node/commit/a28d666f0e)] - **test**: replace fixturesDir with the fixtures module (WeiPlanet) [#16027](https://github.com/nodejs/node/pull/16027)
- [[`d59175090d`](https://github.com/nodejs/node/commit/d59175090d)] - **test**: change crypto decipheriv assertion messages (Daniel Kostro) [#16007](https://github.com/nodejs/node/pull/16007)
- [[`541866ea86`](https://github.com/nodejs/node/commit/541866ea86)] - **test**: replaces fixturesDir with fixtures (Mike Fleming) [#15835](https://github.com/nodejs/node/pull/15835)
- [[`57ae105c72`](https://github.com/nodejs/node/commit/57ae105c72)] - **test**: remove test messages for assert.strictEqual (Ali Groening) [#15995](https://github.com/nodejs/node/pull/15995)
- [[`87b9b7c8c4`](https://github.com/nodejs/node/commit/87b9b7c8c4)] - **test**: move to common.fixtures (Justin Beckwith) [#15987](https://github.com/nodejs/node/pull/15987)
- [[`72f69f3c2c`](https://github.com/nodejs/node/commit/72f69f3c2c)] - **test**: added fixtures module (Michael Pal) [#15980](https://github.com/nodejs/node/pull/15980)
- [[`65c5ff8e92`](https://github.com/nodejs/node/commit/65c5ff8e92)] - **test**: use fixtures in test-tls-multi-key.js (Cheyenne Arrowsmith) [#15844](https://github.com/nodejs/node/pull/15844)
- [[`9eac5aab8c`](https://github.com/nodejs/node/commit/9eac5aab8c)] - **test**: switch to use common.fixtures.fixturesDir (Roger Jiang) [#15814](https://github.com/nodejs/node/pull/15814)
- [[`449538851c`](https://github.com/nodejs/node/commit/449538851c)] - **test**: use common.fixtures module (Chi-chi Wang) [#16012](https://github.com/nodejs/node/pull/16012)
- [[`04f3f6dd6a`](https://github.com/nodejs/node/commit/04f3f6dd6a)] - **test**: escape script filename on Windows (Bartosz Sosnowski) [#16124](https://github.com/nodejs/node/pull/16124)
- [[`501acdf38c`](https://github.com/nodejs/node/commit/501acdf38c)] - **test**: improve assert message in test-dh-regr (Mabry Cervin) [#15912](https://github.com/nodejs/node/pull/15912)
- [[`4c98e07702`](https://github.com/nodejs/node/commit/4c98e07702)] - **test**: fixtures in test-net-pipe-connect-errors (Eric Freiberg) [#15922](https://github.com/nodejs/node/pull/15922)
- [[`244bfb398d`](https://github.com/nodejs/node/commit/244bfb398d)] - **test**: fixtures in test-process-redirect-warnings-env (Kat Rosario) [#15930](https://github.com/nodejs/node/pull/15930)
- [[`18479d3cff`](https://github.com/nodejs/node/commit/18479d3cff)] - **test**: fix ordering of strictEqual actual/expected (Chad Zezula) [#16008](https://github.com/nodejs/node/pull/16008)
- [[`66fd6a1409`](https://github.com/nodejs/node/commit/66fd6a1409)] - **test**: use fixtures.readSync (szhang351)
- [[`6d33564b1a`](https://github.com/nodejs/node/commit/6d33564b1a)] - **test**: replaced fixturesDir with common.fixtures (Dolapo Toki) [#15836](https://github.com/nodejs/node/pull/15836)
- [[`a6f04bec9e`](https://github.com/nodejs/node/commit/a6f04bec9e)] - **test**: use fixtures.fixturesDir (Gene Wu) [#15822](https://github.com/nodejs/node/pull/15822)
- [[`2103453977`](https://github.com/nodejs/node/commit/2103453977)] - **test**: replaces fixturesDir with fixtures methods (Christian Murphy) [#15817](https://github.com/nodejs/node/pull/15817)
- [[`e705ad2076`](https://github.com/nodejs/node/commit/e705ad2076)] - **test**: fixtures in test-process-redirect-warnings (Nicolas Chaulet) [#15917](https://github.com/nodejs/node/pull/15917)
- [[`9ddbcc877b`](https://github.com/nodejs/node/commit/9ddbcc877b)] - **test**: update test-crypto-from-binary (Raj Parekh) [#16011](https://github.com/nodejs/node/pull/16011)
- [[`6b8830c1df`](https://github.com/nodejs/node/commit/6b8830c1df)] - **test**: use fixtures in test-https-set-timeout-server (Bob Clewell) [#15886](https://github.com/nodejs/node/pull/15886)
- [[`57590cd097`](https://github.com/nodejs/node/commit/57590cd097)] - **test**: make use of common/fixtures.fixturesDir (Jem Bezooyen) [#15815](https://github.com/nodejs/node/pull/15815)
- [[`c9d07faa04`](https://github.com/nodejs/node/commit/c9d07faa04)] - **test**: use common/fixtures in test-https-close (Alberto Lopez de Lara) [#15870](https://github.com/nodejs/node/pull/15870)
- [[`68a2d394dd`](https://github.com/nodejs/node/commit/68a2d394dd)] - **test**: use fixtures in test-process-warnings (Suresh Srinivas) [#15869](https://github.com/nodejs/node/pull/15869)
- [[`28756b318a`](https://github.com/nodejs/node/commit/28756b318a)] - **test**: use fixtures in tls-friendly-error-message (tobyfarley) [#15905](https://github.com/nodejs/node/pull/15905)
- [[`a05fe5f716`](https://github.com/nodejs/node/commit/a05fe5f716)] - **test**: use common/fixtures in tls-connect-no-host (Donovan Buck) [#15986](https://github.com/nodejs/node/pull/15986)
- [[`cf31eb7532`](https://github.com/nodejs/node/commit/cf31eb7532)] - **test**: use common/fixtures in test-https-agent (jpaulptr) [#15941](https://github.com/nodejs/node/pull/15941)
- [[`c9c37d076c`](https://github.com/nodejs/node/commit/c9c37d076c)] - **test**: use common fixtures module (Kat Rosario) [#15856](https://github.com/nodejs/node/pull/15856)
- [[`76ab029bea`](https://github.com/nodejs/node/commit/76ab029bea)] - **test**: fs.readFileSync -\> fixtures.readKey (Ethan Brown) [#16030](https://github.com/nodejs/node/pull/16030)
- [[`dabdb2d186`](https://github.com/nodejs/node/commit/dabdb2d186)] - **test**: reduce run time for misc benchmark tests (Rich Trott) [#16120](https://github.com/nodejs/node/pull/16120)
- [[`3f56ac4450`](https://github.com/nodejs/node/commit/3f56ac4450)] - **test**: improve assertion message in dgram test (Shakeel Mohamed) [#16121](https://github.com/nodejs/node/pull/16121)
- [[`44a60c3807`](https://github.com/nodejs/node/commit/44a60c3807)] - **test**: use of fixtures in test-pipe-head (Nicolas Chaulet) [#15868](https://github.com/nodejs/node/pull/15868)
- [[`c4db4e44b8`](https://github.com/nodejs/node/commit/c4db4e44b8)] - **test**: use fixtures in test-https-localaddress.js (Charles T Wall III) [#15811](https://github.com/nodejs/node/pull/15811)
- [[`c252d874d7`](https://github.com/nodejs/node/commit/c252d874d7)] - **test**: use common/fixtures in fs-symlink test (AlexeyM) [#15830](https://github.com/nodejs/node/pull/15830)
- [[`07c14f3054`](https://github.com/nodejs/node/commit/07c14f3054)] - **test**: replace common.fixtures with fixtures module (Jonathan Eskew) [#15877](https://github.com/nodejs/node/pull/15877)
- [[`0f23836e7b`](https://github.com/nodejs/node/commit/0f23836e7b)] - **test**: improve assert message (Tri Nguyen) [#15909](https://github.com/nodejs/node/pull/15909)
- [[`bbdbf8b9b0`](https://github.com/nodejs/node/commit/bbdbf8b9b0)] - **test**: replace fixturesDir with fixtures method (suraiyah) [#15894](https://github.com/nodejs/node/pull/15894)
- [[`c35420d21d`](https://github.com/nodejs/node/commit/c35420d21d)] - **test**: normalize fixtures use (Ruxandra Fediuc) [#15855](https://github.com/nodejs/node/pull/15855)
- [[`3c176fd6f6`](https://github.com/nodejs/node/commit/3c176fd6f6)] - **test**: replace common.fixturesDir w/common.fixtures (Jason Walton) [#15853](https://github.com/nodejs/node/pull/15853)
- [[`77f9ef32bd`](https://github.com/nodejs/node/commit/77f9ef32bd)] - **test**: switch to use common.fixtures module for fixturesDir (r1cebank) [#15821](https://github.com/nodejs/node/pull/15821)
- [[`71e68799ef`](https://github.com/nodejs/node/commit/71e68799ef)] - **test**: fixturesDir replaced to fixtures module (Pawel Golda) [#15809](https://github.com/nodejs/node/pull/15809)
- [[`d70f9f6a35`](https://github.com/nodejs/node/commit/d70f9f6a35)] - **test**: replace common.fixturesDir with fixtures (Stefania Sharp) [#16015](https://github.com/nodejs/node/pull/16015)
- [[`4cf84ea76e`](https://github.com/nodejs/node/commit/4cf84ea76e)] - **test**: replaces common.fixturesDir usage (Ruy Adorno) [#15818](https://github.com/nodejs/node/pull/15818)
- [[`788d7db4e9`](https://github.com/nodejs/node/commit/788d7db4e9)] - **test**: use common.fixtures.path() (Tobias Kieslich) [#16112](https://github.com/nodejs/node/pull/16112)
- [[`b7865ea70d`](https://github.com/nodejs/node/commit/b7865ea70d)] - **test**: replace common.fixturesDir with fixtures (Shakeel Mohamed) [#15857](https://github.com/nodejs/node/pull/15857)
- [[`9b39ca6cbb`](https://github.com/nodejs/node/commit/9b39ca6cbb)] - **test**: use fixtures module in test (Nigel Kibodeaux) [#16117](https://github.com/nodejs/node/pull/16117)
- [[`5e65069289`](https://github.com/nodejs/node/commit/5e65069289)] - **test**: use template literals in test-string-decoder (Edward Andrew Robinson) [#15884](https://github.com/nodejs/node/pull/15884)
- [[`d2b74fe1e3`](https://github.com/nodejs/node/commit/d2b74fe1e3)] - **test**: switch to fixtures module (Christopher Sidebottom) [#15880](https://github.com/nodejs/node/pull/15880)
- [[`1144be09b7`](https://github.com/nodejs/node/commit/1144be09b7)] - **test**: rewrite assert message (Martin Michaelis) [#15879](https://github.com/nodejs/node/pull/15879)
- [[`095df35a5e`](https://github.com/nodejs/node/commit/095df35a5e)] - **test**: change fixturesDir to fixtures.path (Guilherme Akio Sakae) [#15863](https://github.com/nodejs/node/pull/15863)
- [[`4fd5bf5ff7`](https://github.com/nodejs/node/commit/4fd5bf5ff7)] - **test**: replace fixturesDir with common.fixtures (Oliver Luebeck) [#15907](https://github.com/nodejs/node/pull/15907)
- [[`e3e234ea1c`](https://github.com/nodejs/node/commit/e3e234ea1c)] - **test**: update http test client function signatures (Jakub Mrowiec - Alkagar) [#15807](https://github.com/nodejs/node/pull/15807)
- [[`08ca73f52a`](https://github.com/nodejs/node/commit/08ca73f52a)] - **test**: replace common.fixturesDir w/ fixtures.path (Druotic) [#15819](https://github.com/nodejs/node/pull/15819)
- [[`39ae3f1802`](https://github.com/nodejs/node/commit/39ae3f1802)] - **test**: replace fixtureDir with fixtures.path (matthewreed26) [#15943](https://github.com/nodejs/node/pull/15943)
- [[`1365a6f597`](https://github.com/nodejs/node/commit/1365a6f597)] - **test**: use common.fixtures module for file path (Adil L) [#16017](https://github.com/nodejs/node/pull/16017)
- [[`bd8d4401ee`](https://github.com/nodejs/node/commit/bd8d4401ee)] - **test**: use fixtures module (Maurice Hayward) [#16034](https://github.com/nodejs/node/pull/16034)
- [[`bba5263d00`](https://github.com/nodejs/node/commit/bba5263d00)] - **test**: replace fixturesDir with fixtures module (tabulatedreams) [#16036](https://github.com/nodejs/node/pull/16036)
- [[`a8e7fa4e75`](https://github.com/nodejs/node/commit/a8e7fa4e75)] - **test**: replace fixturesDir with fixtures module (Ivan Etchart) [#15893](https://github.com/nodejs/node/pull/15893)
- [[`1fc3851642`](https://github.com/nodejs/node/commit/1fc3851642)] - **test**: change fixturesDir to fixtures.path (Savio Lucena) [#15902](https://github.com/nodejs/node/pull/15902)
- [[`683e48cb55`](https://github.com/nodejs/node/commit/683e48cb55)] - **test**: changed fixtures require (creisle) [#15899](https://github.com/nodejs/node/pull/15899)
- [[`f82f691d5e`](https://github.com/nodejs/node/commit/f82f691d5e)] - **test**: replaced fixturesDir with fixtures module (Alex McKenzie) [#15908](https://github.com/nodejs/node/pull/15908)
- [[`e68ef291e7`](https://github.com/nodejs/node/commit/e68ef291e7)] - **test**: use common.fixtures in tls test (Ben Michel) [#15965](https://github.com/nodejs/node/pull/15965)
- [[`71daa68c3d`](https://github.com/nodejs/node/commit/71daa68c3d)] - **test**: use fixtures module instead of common (Joe Grace) [#15925](https://github.com/nodejs/node/pull/15925)
- [[`e81fc8aca7`](https://github.com/nodejs/node/commit/e81fc8aca7)] - **test**: replaced fixturesDir with fixtures module (Alex McKenzie) [#15927](https://github.com/nodejs/node/pull/15927)
- [[`33ea6deeab`](https://github.com/nodejs/node/commit/33ea6deeab)] - **test**: replace fixturesDir with fixtures module (Greg Matthews) [#15932](https://github.com/nodejs/node/pull/15932)
- [[`be2b70bb56`](https://github.com/nodejs/node/commit/be2b70bb56)] - **test**: replace fixturesDir with fixtures (Mujtaba Al-Tameemi) [#15949](https://github.com/nodejs/node/pull/15949)
- [[`25a5bf02c7`](https://github.com/nodejs/node/commit/25a5bf02c7)] - **test**: remove common.fixturesDir (Luis Del Águila) [#15950](https://github.com/nodejs/node/pull/15950)
- [[`51d87e338e`](https://github.com/nodejs/node/commit/51d87e338e)] - **test**: replace fixturesDir with fixtures module (BinarySo1o) [#15961](https://github.com/nodejs/node/pull/15961)
- [[`05286b6c80`](https://github.com/nodejs/node/commit/05286b6c80)] - **test**: replaced fixturesDir with common.fixtures (jopann) [#15971](https://github.com/nodejs/node/pull/15971)
- [[`683c5fa58f`](https://github.com/nodejs/node/commit/683c5fa58f)] - **test**: use common.fixtures module in test-preload (Laura Cabrera) [#15975](https://github.com/nodejs/node/pull/15975)
- [[`000965d427`](https://github.com/nodejs/node/commit/000965d427)] - **test**: replaced common.fixturesDir with readKey (Sean Cox) [#15933](https://github.com/nodejs/node/pull/15933)
- [[`0f8b315a9e`](https://github.com/nodejs/node/commit/0f8b315a9e)] - **test**: replace fixturesDir in tls-env-bad-extra-ca (Annie Weng) [#15813](https://github.com/nodejs/node/pull/15813)
- [[`48a55d1364`](https://github.com/nodejs/node/commit/48a55d1364)] - **test**: use common.fixtures in checkServerIdentity (Emily Marigold Klassen) [#15951](https://github.com/nodejs/node/pull/15951)
- [[`909e587a93`](https://github.com/nodejs/node/commit/909e587a93)] - **test**: replaced common.fixturesDir with readKey (rhalldearn) [#15952](https://github.com/nodejs/node/pull/15952)
- [[`544cbd7884`](https://github.com/nodejs/node/commit/544cbd7884)] - **test**: replace fixturesDir with fixtures.readKey (Thomas Schorn) [#15948](https://github.com/nodejs/node/pull/15948)
- [[`4005ed619f`](https://github.com/nodejs/node/commit/4005ed619f)] - **test**: replace common.fixturesDir with fixtures. (Sam Skjonsberg) [#15802](https://github.com/nodejs/node/pull/15802)
- [[`8c5b51d9c3`](https://github.com/nodejs/node/commit/8c5b51d9c3)] - **test**: replace fixturesDir with common.fixtures (rachelnicole) [#16051](https://github.com/nodejs/node/pull/16051)
- [[`107acb1c56`](https://github.com/nodejs/node/commit/107acb1c56)] - **test**: update fixturesDir to fixtures.readKey (bitandbang) [#16016](https://github.com/nodejs/node/pull/16016)
- [[`643a2c6b19`](https://github.com/nodejs/node/commit/643a2c6b19)] - **test**: replace fixturesDir with common.fixtures (Pooya Paridel) [#15837](https://github.com/nodejs/node/pull/15837)
- [[`14aee78554`](https://github.com/nodejs/node/commit/14aee78554)] - **test**: update 'fixturesDir' refs in a test file (James M. Greene) [#15824](https://github.com/nodejs/node/pull/15824)
- [[`e1c45efdbb`](https://github.com/nodejs/node/commit/e1c45efdbb)] - **test**: use fixtures.readKey in https-agent test (Greg Byram) [#15913](https://github.com/nodejs/node/pull/15913)
- [[`2c6aa17fa9`](https://github.com/nodejs/node/commit/2c6aa17fa9)] - **test**: add test for fork() + shell (cjihrig) [#15352](https://github.com/nodejs/node/pull/15352)
- [[`148a030345`](https://github.com/nodejs/node/commit/148a030345)] - **test**: remove node-tap lookalike (cjihrig) [#13707](https://github.com/nodejs/node/pull/13707)
- [[`fa5c706bec`](https://github.com/nodejs/node/commit/fa5c706bec)] - **test**: refactor exitedAfterDisconnect test (Rich Trott) [#16729](https://github.com/nodejs/node/pull/16729)
- [[`9416dab7ac`](https://github.com/nodejs/node/commit/9416dab7ac)] - **test**: use fixtures module in test-https-pfx (Ken Takagi) [#15895](https://github.com/nodejs/node/pull/15895)
- [[`7e9779aade`](https://github.com/nodejs/node/commit/7e9779aade)] - **test**: refactor test-readline-keys (Rich Trott) [#11281](https://github.com/nodejs/node/pull/11281)
- [[`8264328087`](https://github.com/nodejs/node/commit/8264328087)] - **test,net**: remove scatological terminology (Rich Trott) [#16599](https://github.com/nodejs/node/pull/16599)
- [[`bb81390db2`](https://github.com/nodejs/node/commit/bb81390db2)] - **timers**: fix eventloop block (zhangzifa) [#15072](https://github.com/nodejs/node/pull/15072)
- [[`f3749d7b2c`](https://github.com/nodejs/node/commit/f3749d7b2c)] - **tools**: remove unneeded parentheses in doc/html.js (Vse Mozhet Byt) [#16845](https://github.com/nodejs/node/pull/16845)
- [[`1c192f50f6`](https://github.com/nodejs/node/commit/1c192f50f6)] - **tools**: replace string concatenation with template literals (Kevin Yu) [#16804](https://github.com/nodejs/node/pull/16804)
- [[`ce007be05b`](https://github.com/nodejs/node/commit/ce007be05b)] - **tools**: replace string concatenation with template literals (Giovanni Lela) [#16806](https://github.com/nodejs/node/pull/16806)
- [[`d165d3fd1c`](https://github.com/nodejs/node/commit/d165d3fd1c)] - **tools**: replace string concetation with templates (Patrick Heneise) [#16801](https://github.com/nodejs/node/pull/16801)
- [[`a8d7f5f52e`](https://github.com/nodejs/node/commit/a8d7f5f52e)] - **tools**: fix cpplint.py when path contains non-ascii (sharkfisher) [#16047](https://github.com/nodejs/node/pull/16047)
- [[`b48471ac10`](https://github.com/nodejs/node/commit/b48471ac10)] - **tools**: rename unused variale in more pythonic way (Nikhil Komawar) [#16171](https://github.com/nodejs/node/pull/16171)
- [[`5b5b5c0f15`](https://github.com/nodejs/node/commit/5b5b5c0f15)] - **tools**: use template literal in error message (Tim Chon) [#15846](https://github.com/nodejs/node/pull/15846)
- [[`ae5930bbe4`](https://github.com/nodejs/node/commit/ae5930bbe4)] - **tty,doc**: add type-check to isatty (Bryan English) [#15567](https://github.com/nodejs/node/pull/15567)

Windows 32-bit Installer: https://nodejs.org/dist/v6.12.1/node-v6.12.1-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v6.12.1/node-v6.12.1-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v6.12.1/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v6.12.1/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v6.12.1/node-v6.12.1.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v6.12.1/node-v6.12.1-darwin-x64.tar.gz \
Linux 32-bit Binary: https://nodejs.org/dist/v6.12.1/node-v6.12.1-linux-x86.tar.xz \
Linux 64-bit Binary: https://nodejs.org/dist/v6.12.1/node-v6.12.1-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v6.12.1/node-v6.12.1-linux-ppc64le.tar.xz \
Linux PPC BE 64-bit Binary: https://nodejs.org/dist/v6.12.1/node-v6.12.1-linux-ppc64.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v6.12.1/node-v6.12.1-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v6.12.1/node-v6.12.1-aix-ppc64.tar.gz \
SmartOS 32-bit Binary: https://nodejs.org/dist/v6.12.1/node-v6.12.1-sunos-x86.tar.xz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v6.12.1/node-v6.12.1-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v6.12.1/node-v6.12.1-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v6.12.1/node-v6.12.1-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v6.12.1/node-v6.12.1-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v6.12.1/node-v6.12.1.tar.gz \
Other release files: https://nodejs.org/dist/v6.12.1/ \
Documentation: https://nodejs.org/docs/v6.12.1/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

894890be550a764b3f6b7ce80054efce5d5ca0d18624fc560062908cb0dd3391  node-v6.12.1-aix-ppc64.tar.gz
fe772a0aa220d5b4967fa9e93337a61699e2c5e8931bba0e8db9c0420d899fd7  node-v6.12.1-darwin-x64.tar.gz
2363b225576aa6991a195e09050dc3754addec86b91e82cdae7f234094de9728  node-v6.12.1-darwin-x64.tar.xz
43e41320496fda559a3f9df7fdc95e705a930e911acfb75900deafc23961701c  node-v6.12.1-headers.tar.gz
af432d0328a1b5c2c8f4bda0fa11c79d59864e61b0dac0c7f9d6d4204cc077d9  node-v6.12.1-headers.tar.xz
6f88ed709061f55622beb07bcab40ce855eb6bc30fe77041269152d3cdaf0f6a  node-v6.12.1-linux-arm64.tar.gz
056c0340fc770a23dd3414ff48e765b02112702879de30978ba0dba492296eea  node-v6.12.1-linux-arm64.tar.xz
ce2727a1ea932641953b9d429f005fdc9ebb02be5a2ae08a7a54814669238ef8  node-v6.12.1-linux-armv6l.tar.gz
e74c72f42c3da03b6489d6ab9990db67704c8daa746fad2cdd0b6fb7c39aff1e  node-v6.12.1-linux-armv6l.tar.xz
b1eb93547feae3d5a657893ef36bc39da2be70a94bf37773dc49536ca15f54f6  node-v6.12.1-linux-armv7l.tar.gz
5b1f871748006cc4c6e58191548a1b43b7b3e76b4fc1af859a0e4ae13ed78546  node-v6.12.1-linux-armv7l.tar.xz
08e88362c523bf46113f4389215e1d33ee743b60947c319e8ef16b9d572df653  node-v6.12.1-linux-ppc64le.tar.gz
02c53e1a157a6db1e5184c23fca962b4e1d64c9d9583fbcfd65eb8b499d5d72b  node-v6.12.1-linux-ppc64le.tar.xz
33a762b122184af321685959ace61317075487653bc1012fd879706231814522  node-v6.12.1-linux-ppc64.tar.gz
5ef9fda54754547cacb44099cf8e81f0b778d09f4996ebd217941fdbeef02782  node-v6.12.1-linux-ppc64.tar.xz
81813efabb4af0d1232bc4a9b7e6f850a49c677e328ca87dba201065b60ea87b  node-v6.12.1-linux-s390x.tar.gz
d573d4a1803c2d0d0b7afddbfa4f875095bb59fc4c85ba64825ffc03e4446032  node-v6.12.1-linux-s390x.tar.xz
0868c3e3b6a49500ae3185f80ab04b2526c88dbdb39b324b9d71870c62d1f785  node-v6.12.1-linux-x64.tar.gz
f58ca90d1e33e60ea88573aad398a2a35cac233a99f74d13968b8a54c240bb85  node-v6.12.1-linux-x64.tar.xz
c83ed23843eaa26cc9a7d088922a87bb92ca3ee8bcadf95f0177f0362cdfacd4  node-v6.12.1-linux-x86.tar.gz
7671886da0a8b74476d4bba3285437d2ab8d54e1cd3cb4085a355a8d184463cf  node-v6.12.1-linux-x86.tar.xz
d8f10fafa3cbe41450b2856eb142048fee6564b5da269a7951a57eba9e74ac37  node-v6.12.1.pkg
594abdc6d27fd775758927cf1f4a4ab9c018112c0b565053200dfdd55e8201b2  node-v6.12.1-sunos-x64.tar.gz
a5ba63f572ac65387ba3c44bf0f4888e7a175798c3584911fa30baa97813b1e6  node-v6.12.1-sunos-x64.tar.xz
3af30fd195c99d2e7f5b2a9709e2f387e8199c4187257873e7d1d9c67e458199  node-v6.12.1-sunos-x86.tar.gz
b00c9860d0beaa8ad5946191cbede375f985e4e38172e7be187f61d76b578a99  node-v6.12.1-sunos-x86.tar.xz
818b7b69e065c76f6bf505425ec7415930a870b3c00c8d27be19a1829ed2a913  node-v6.12.1.tar.gz
a83c9c23dd8ea4ff3a2cc7329242ccedb2b6fd525a3e5a61805258e3331e6822  node-v6.12.1.tar.xz
4cf6ada2eedfb70ab78cc7034a51a1dace0e71cc3fe135ba4aa5f1d8ab75ada7  node-v6.12.1-win-x64.7z
02160fd5f45e012b94fbcad875056ee1037ca26dcc8008daa36d0e807cd24533  node-v6.12.1-win-x64.zip
a7e91ddb08cea9720c3ed1173653144b827553e658fe8c5a2fe5c4dfdf6b51c1  node-v6.12.1-win-x86.7z
61795e5cfb136ea70eed5a0247cbd4138c0f4d96ec15baa240d72bfad56137a7  node-v6.12.1-win-x86.zip
c98cc3876807bb6ad0918e17af6d6629fdc8702d7985e8daf97a04debfcfdddd  node-v6.12.1-x64.msi
66d2177f400294ab0cbeee103ec1ba62e5ce44822c0c6c312127a6d6cbfdb6c4  node-v6.12.1-x86.msi
3b98446e253835a3b6bf56aabecddbf92ee4ae22d8acf772484f781d33b01bb2  win-x64/node.exe
a010702c21f04ff4782d2d37eb7ab4bac5e03a8875bc44fa4bd975904be7c7f0  win-x64/node.lib
aed3fdce4f9091a739ff1c7f9fb246bec3710fe4eab35dd5c7ec834feb28cd26  win-x64/node_pdb.7z
fc77f3c5fdaac90628b9263293be225194c74e44ff9138d266a50ba471553109  win-x64/node_pdb.zip
ef215b0f4085e2bdaead7de6129bf4e237558710c9d38940b63ff8d265093887  win-x86/node.exe
b829b7414e1647ceeb99efe1dec6a4dc7a8619b0cd5c57746f56ef5d52076d5c  win-x86/node.lib
8a537af0531d1ae1601a1529440fd533322074ddc76ad2b0e90c963274f800fb  win-x86/node_pdb.7z
988d4956eeaff365fa7794b921b5013bb8288d3abbe5777db0c5dcf6fb13eee5  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----
Comment: GPGTools - https://gpgtools.org

iQEcBAEBCAAGBQJaJqgBAAoJEJM7AfQLXKlGE1sH/0F5zFX7AR++o52oIVakW8+v
zCauEEd/xx+CYa6jYk1KJKFaDGyWret+xSGY6+xWMbVopBu0EPMVclFdVuUUjIKr
5cnpcx4FwaMSFfj/9gztgQsvMU0WwSYHChLQUb9cz3Nooneq5jb0r6lmnAg3W924
lQflCslI/p1jh9BKh3GXQ9oRj4nu2GHGgCq0guuXR1VuR1qSmQU6+WKNkEcJLzcf
JfkOtaYQwaAnR935uW0etu+7I3/PiOCwZps/LaJ9zg/XkK/S9zxnko7TbR3qxijf
XzTxEgwTF55Kt302jZXAQqBUAo2b2afIuRYXgZIXdVB9u0TdNdsKOIJw4beqlW8=
=43y3
-----END PGP SIGNATURE-----

```
