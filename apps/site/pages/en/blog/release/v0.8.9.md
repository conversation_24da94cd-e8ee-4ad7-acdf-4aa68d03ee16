---
date: '2012-09-11T18:50:17.000Z'
category: release
title: Version 0.8.9 (Stable)
layout: blog-post
author: The Node.js Project
---

2012.09.11, Version 0.8.9 (Stable)

- v8: upgrade to **********

- GYP: upgrade to r1477

- npm: Upgrade to 1.1.61

- npm: Don't create world-writable files (isaacs)

- windows: fix single-accept mode for shared server sockets (<PERSON>)

- windows: fix uninitialized memory access in uv_update_time() (<PERSON>)

- windows: don't throw when a signal handler is attached (<PERSON>)

- unix: fix memory leak in udp (<PERSON>)

- unix: map errno ESPIPE (<PERSON>)

- unix, windows: fix memory corruption in fs-poll.c (<PERSON>)

- sunos: fix os.cpus() on x86_64 (<PERSON>)

- child process: fix processes with IPC channel don't emit 'close' (<PERSON>)

- build: add a "--dest-os" option to force a gyp "flavor" (<PERSON>)

- build: set `process.platform` to "sunos" on SmartOS (<PERSON>)

- build: fix `make -j` fails after `make clean` (<PERSON><PERSON>)

- build: fix openssl configuration for "arm" builds (<PERSON>)

- tls: support unix domain socket/named pipe in tls.connect (Shigeki Ohtsu)

- https: make https.get() accept a URL (koichik)

- http: respect HTTP/1.0 TE header (Ben Noordhuis)

- crypto, tls: Domainify setSNICallback, pbkdf2, randomBytes (Ben Noordhuis)

- stream.pipe: Don't call destroy() unless it's a function (isaacs)

Source Code: https://nodejs.org/dist/v0.8.9/node-v0.8.9.tar.gz

Macintosh Installer (Universal): https://nodejs.org/dist/v0.8.9/node-v0.8.9.pkg

Windows Installer: https://nodejs.org/dist/v0.8.9/node-v0.8.9-x86.msi

Windows x64 Installer: https://nodejs.org/dist/v0.8.9/x64/node-v0.8.9-x64.msi

Windows x64 Files: https://nodejs.org/dist/v0.8.9/x64/

Linux 32-bit Binary: https://nodejs.org/dist/v0.8.9/node-v0.8.9-linux-x86.tar.gz

Linux 64-bit Binary: https://nodejs.org/dist/v0.8.9/node-v0.8.9-linux-x64.tar.gz

Solaris 32-bit Binary: https://nodejs.org/dist/v0.8.9/node-v0.8.9-sunos-x86.tar.gz

Solaris 64-bit Binary: https://nodejs.org/dist/v0.8.9/node-v0.8.9-sunos-x64.tar.gz

Other release files: https://nodejs.org/dist/v0.8.9/

Website: https://nodejs.org/docs/v0.8.9/

Documentation: https://nodejs.org/docs/v0.8.9/api/

Shasums:

```
68aa7341807fb114f334151b7a1c8859e96b83d4  node-v0.8.9-darwin-x64.tar.gz
9e4a9422c1fd71750e9c46235d58aedaac3ba002  node-v0.8.9-darwin-x86.tar.gz
6236f781632555abf69d77f4bdfeb1e4e83779f3  node-v0.8.9-linux-x64.tar.gz
7f46084541d4909f44cfef2bb95f1e4f7435629e  node-v0.8.9-linux-x86.tar.gz
33b0fe68f63519f3c8e6dc4d2aa51c96f62d2a56  node-v0.8.9-sunos-x64.tar.gz
e05863cb3a7d4add340ad434228f57da04a03b3d  node-v0.8.9-sunos-x86.tar.gz
104f325d5289c51c6eb6a0634691dcdb39abb1db  node-v0.8.9-x86.msi
1dd2cf48fb9b1f3e11e6e6750084ad4b2a2b0a85  node-v0.8.9.pkg
2d3234adceedc2dc87284af88609ede6ecd71734  node-v0.8.9.tar.gz
2997e2075cd04cf693453ce5664fa37615faa9a7  node.exe
d2834bd8ed3569b7880211dfe31a4f21cd475ab8  node.exp
41eac45ae350324de321a85787897bd8aa6b371c  node.lib
ab6666144b23b2594521d27a95fb36b0904d48a2  node.pdb
f29db0a61a7bb32a7198ab059eca25b1283b9d6d  x64/node-v0.8.9-x64.msi
2a7e69cef1bf7bc88109e007406e6feeaaa007b2  x64/node.exe
0423cd6602684c24e65358b9caa51740af67677b  x64/node.exp
92155062a70100bfb5cf1389dd93e8851e7f3d0b  x64/node.lib
cbbee351b84d7da0a91f56d6bbf6805e7b85cc8f  x64/node.pdb
```
