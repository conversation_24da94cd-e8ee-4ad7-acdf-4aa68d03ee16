---
date: '2013-02-25T21:48:51.000Z'
category: release
title: Node v0.8.21 (Stable)
layout: blog-post
author: The Node.js Project
---

2013.02.25, Version 0.8.21 (Stable)

- http: Do not free the wrong parser on socket close (isaacs)

- http: Handle hangup writes more gently (isaacs)

- zlib: fix assert on bad input (<PERSON>)

- test: add TAP output to the test runner (<PERSON>)

- unix: Handle EINPROGRESS from domain sockets (<PERSON>)

Source Code: https://nodejs.org/dist/v0.8.21/node-v0.8.21.tar.gz

Macintosh Installer (Universal): https://nodejs.org/dist/v0.8.21/node-v0.8.21.pkg

Windows Installer: https://nodejs.org/dist/v0.8.21/node-v0.8.21-x86.msi

Windows x64 Installer: https://nodejs.org/dist/v0.8.21/x64/node-v0.8.21-x64.msi

Windows x64 Files: https://nodejs.org/dist/v0.8.21/x64/

Linux 32-bit Binary: https://nodejs.org/dist/v0.8.21/node-v0.8.21-linux-x86.tar.gz

Linux 64-bit Binary: https://nodejs.org/dist/v0.8.21/node-v0.8.21-linux-x64.tar.gz

Solaris 32-bit Binary: https://nodejs.org/dist/v0.8.21/node-v0.8.21-sunos-x86.tar.gz

Solaris 64-bit Binary: https://nodejs.org/dist/v0.8.21/node-v0.8.21-sunos-x64.tar.gz

Other release files: https://nodejs.org/dist/v0.8.21/

Website: https://nodejs.org/docs/v0.8.21/

Documentation: https://nodejs.org/docs/v0.8.21/api/

Shasums:

```
383b9009a587b7390a5a00cef4ece441fb16dd82  node-v0.8.21-darwin-x64.tar.gz
47654ca8ecc93c846e6f7493a19931b99d5e4b87  node-v0.8.21-darwin-x86.tar.gz
46d66f3b95f447811e9253d66050859b4bb81ea0  node-v0.8.21-linux-x64.tar.gz
1f16e50dacd5e942970c28be1e578e6260a116c4  node-v0.8.21-linux-x86.tar.gz
b24b5e0acc53f004122c0cb2df775c4493b1e048  node-v0.8.21-sunos-x64.tar.gz
54ca0c94dff35e941fe90d10755427a15d6519ae  node-v0.8.21-sunos-x86.tar.gz
38c5855c1ebd70fde111d50343a163a03cff9765  node-v0.8.21-x86.msi
60cd1fb8f43943bd3ed7c07745df1b3e81bafc13  node-v0.8.21.pkg
8b75377eafb5e77d6dff141c9533202d5a589ce4  node-v0.8.21.tar.gz
c310779c80d21be7556ec0921d5afca8f64792eb  node.exe
61b98f9dfbe70184788b6f010ce7667c9cba0fdc  node.exp
78c37ac837bbb8fefd691e351b3c20f136b212ee  node.lib
40cc197db5c9fc4ded4509b5ae21bc53d178998e  node.pdb
ec29a824e51308349f7895c010469f8e59939094  x64/node-v0.8.21-x64.msi
e4c5563c38a01dac1a97f6366175d1fef86da262  x64/node.exe
8e51783d5e03148f01db8386102e4ffc4e30deda  x64/node.exp
a6017052052bb469a50327b4f40c990c25da0932  x64/node.lib
fb8d24e5208b14d35997bc3b46e325316e6ad94d  x64/node.pdb
```

Shasums:

```
62cd69928ce2da9e8512b3efb96aba353ee54a91  node-v0.8.21-darwin-x64.tar.gz
62cf9b990f8e424e7fdb1d50a46d779e4d508b23  node-v0.8.21-darwin-x86.tar.gz
83e92aec4f4a0167e7c5b3ef04388fc33712d97b  node-v0.8.21-linux-x64.tar.gz
83a8aa10dafa196b372958d1052f61fe1c16c2a4  node-v0.8.21-linux-x86.tar.gz
588830b0d095c0ef3583d9f7f574426f5b456e2e  node-v0.8.21-sunos-x64.tar.gz
e4eff5914432da3cf12a833aaa0444693671cca1  node-v0.8.21-sunos-x86.tar.gz
8146b2254caa814157aa511075dcf7b42d0ecb59  node-v0.8.21-x86.msi
60cd1fb8f43943bd3ed7c07745df1b3e81bafc13  node-v0.8.21.pkg
65ab7307f1aee12be4c88e396e2510967a52b1c6  node-v0.8.21.tar.gz
5d770ad554ee4a73278b2d90029e758e0a676074  node.exe
17514f32c57c64bd0d367c71ed0e6dc399fc9e12  node.exp
9404eff8562dfb0a6e0e72167278ac6131be5d3a  node.lib
94908174f715d2707d48e6d53a1f96f33059f56c  node.pdb
36750abf56120a63d9fa9b2b75bdc98dfac051f7  x64/node-v0.8.21-x64.msi
b61b9620e936bc9c19043a02625aed9922aeb653  x64/node.exe
21a0e855e652e7fbe84e7efc1f7a3542fa870372  x64/node.exp
f9d017c07030599e3442a958020381a37402dc81  x64/node.lib
3b35fbac0962aaff46bdd2f06412dcb1f80b9895  x64/node.pdb
```
