---
date: '2016-11-08T19:55:00.204Z'
category: release
title: Node v4.6.2 (LTS)
layout: blog-post
author: <PERSON><PERSON>
---

### Notable Changes

- **build**: It is now possible to build the documentation from the release tarball (<PERSON>) [#8413](https://github.com/nodejs/node/pull/8413)
- **buffer**: Buffer.alloc() will no longer incorrectly return a zero filled buffer when an encoding is passed (<PERSON>) [#9238](https://github.com/nodejs/node/pull/9238)
- **deps**: upgrade npm in LTS to 2.15.11 (<PERSON>) [#8928](https://github.com/nodejs/node/pull/8928)
- **repl**: Enable tab completion for global properties (Lance Ball) [#7369](https://github.com/nodejs/node/pull/7369)
- **url**: `url.format()` will now encode all `#` in `search` (<PERSON><PERSON> Myller) [#8072](https://github.com/nodejs/node/pull/8072)

### Commits

- [[`06a1c9bf80`](https://github.com/nodejs/node/commit/06a1c9bf80)] - **assert**: remove code that is never reached (Rich Trott) [#8132](https://github.com/nodejs/node/pull/8132)
- [[`861e584d46`](https://github.com/nodejs/node/commit/861e584d46)] - **async_wrap**: add a missing case to test-async-wrap-throw-no-init (yorkie) [#8198](https://github.com/nodejs/node/pull/8198)
- [[`a3d08025fa`](https://github.com/nodejs/node/commit/a3d08025fa)] - **benchmark**: add benches for fs.stat & fs.statSync (Anna Henningsen) [#8338](https://github.com/nodejs/node/pull/8338)
- [[`408a585261`](https://github.com/nodejs/node/commit/408a585261)] - **buffer**: fix `fill` with encoding in Buffer.alloc() (Teddy Katz) [#9238](https://github.com/nodejs/node/pull/9238)
- [[`17c4187949`](https://github.com/nodejs/node/commit/17c4187949)] - **buffer**: optimize hex_decode (Christopher Jeffrey) [#7602](https://github.com/nodejs/node/pull/7602)
- [[`50cfea0081`](https://github.com/nodejs/node/commit/50cfea0081)] - **build**: run `npm install` for doc builds in tarball (Anna Henningsen) [#8413](https://github.com/nodejs/node/pull/8413)
- [[`c4be179064`](https://github.com/nodejs/node/commit/c4be179064)] - **build**: add missing files to zip and 7z packages (Richard Lau) [#8069](https://github.com/nodejs/node/pull/8069)
- [[`41e27f6a6a`](https://github.com/nodejs/node/commit/41e27f6a6a)] - **build**: don't link against liblog on host system (Ben Noordhuis) [#7762](https://github.com/nodejs/node/pull/7762)
- [[`7766997f7e`](https://github.com/nodejs/node/commit/7766997f7e)] - **build**: add conflict marker check during CI lint (Brian White) [#7625](https://github.com/nodejs/node/pull/7625)
- [[`2a66ddbcbb`](https://github.com/nodejs/node/commit/2a66ddbcbb)] - **build**: re-add --ninja option to configure (Ehsan Akhgari) [#6780](https://github.com/nodejs/node/pull/6780)
- [[`950cc1df83`](https://github.com/nodejs/node/commit/950cc1df83)] - **build**: adding config.gypi dep to addons/.buildstamp (Daniel Bevenius) [#7893](https://github.com/nodejs/node/pull/7893)
- [[`e64063c344`](https://github.com/nodejs/node/commit/e64063c344)] - **build**: don't require processing docs for nightlies (Johan Bergström) [#8325](https://github.com/nodejs/node/pull/8325)
- [[`00ea7388cb`](https://github.com/nodejs/node/commit/00ea7388cb)] - **build**: fix dependencies on AIX (Michael Dawson) [#8285](https://github.com/nodejs/node/pull/8285)
- [[`8dfab3ad68`](https://github.com/nodejs/node/commit/8dfab3ad68)] - **build**: fix dependencies on AIX (Michael Dawson) [#8272](https://github.com/nodejs/node/pull/8272)
- [[`1b5f35f1be`](https://github.com/nodejs/node/commit/1b5f35f1be)] - **build**: turn on thin static archives (Ben Noordhuis) [#7957](https://github.com/nodejs/node/pull/7957)
- [[`c41efe4d68`](https://github.com/nodejs/node/commit/c41efe4d68)] - **build**: add node_module_version to config.gypi (Marcin Cieślak) [#8171](https://github.com/nodejs/node/pull/8171)
- [[`f556b43e3e`](https://github.com/nodejs/node/commit/f556b43e3e)] - **build**: add --enable-d8 configure option (Ben Noordhuis) [#7538](https://github.com/nodejs/node/pull/7538)
- [[`612dfeb647`](https://github.com/nodejs/node/commit/612dfeb647)] - **child_process**: Check stderr before accessing it (Robert Chiras) [#6877](https://github.com/nodejs/node/pull/6877)
- [[`5ed5142158`](https://github.com/nodejs/node/commit/5ed5142158)] - **child_process**: workaround fd passing issue on OS X (Santiago Gimeno) [#7572](https://github.com/nodejs/node/pull/7572)
- [[`227db0ab21`](https://github.com/nodejs/node/commit/227db0ab21)] - **cluster**: remove bind() and self (cjihrig) [#7710](https://github.com/nodejs/node/pull/7710)
- [[`3003131e9a`](https://github.com/nodejs/node/commit/3003131e9a)] - **configure**: reword help for --without-npm (BlackYoup) [#7471](https://github.com/nodejs/node/pull/7471)
- [[`2b933339d0`](https://github.com/nodejs/node/commit/2b933339d0)] - **debugger**: use arrow function for lexical `this` (Guy Fraser) [#7415](https://github.com/nodejs/node/pull/7415)
- [[`52cba4147d`](https://github.com/nodejs/node/commit/52cba4147d)] - **deps**: backport 2bcbe2f from V8 upstream (ofrobots) [#7814](https://github.com/nodejs/node/pull/7814)
- [[`2b01bc8e55`](https://github.com/nodejs/node/commit/2b01bc8e55)] - **deps**: backport a76d133 from v8 upstream (Matt Loring) [#7689](https://github.com/nodejs/node/pull/7689)
- [[`e1f12fb358`](https://github.com/nodejs/node/commit/e1f12fb358)] - **deps**: cherry-pick b93c80a from v8 upstream (Matt Loring) [#7689](https://github.com/nodejs/node/pull/7689)
- [[`2d07fd71ee`](https://github.com/nodejs/node/commit/2d07fd71ee)] - **deps**: backport e093a04, 09db540 from upstream V8 (Ali Ijaz Sheikh) [#7689](https://github.com/nodejs/node/pull/7689)
- [[`4369055878`](https://github.com/nodejs/node/commit/4369055878)] - **deps**: cherry-pick 1f53e42 from v8 upstream (Ben Noordhuis) [#7612](https://github.com/nodejs/node/pull/7612)
- [[`05d40d9573`](https://github.com/nodejs/node/commit/05d40d9573)] - **deps**: upgrade npm in LTS to 2.15.11 (Kat Marchán) [#8928](https://github.com/nodejs/node/pull/8928)
- [[`36b3ff0cfc`](https://github.com/nodejs/node/commit/36b3ff0cfc)] - **deps**: float gyp patch for long filenames (Anna Henningsen) [#7963](https://github.com/nodejs/node/pull/7963)
- [[`9ddc615d0e`](https://github.com/nodejs/node/commit/9ddc615d0e)] - **deps**: no /safeseh for ml64.exe (Fedor Indutny) [#7759](https://github.com/nodejs/node/pull/7759)
- [[`ea36c61eda`](https://github.com/nodejs/node/commit/ea36c61eda)] - **deps**: `MASM.UseSafeExceptionHandlers` for OpenSSL (Fedor Indutny) [#7427](https://github.com/nodejs/node/pull/7427)
- [[`0b87b1a095`](https://github.com/nodejs/node/commit/0b87b1a095)] - **dns**: tweak regex for IPv6 addresses (Luigi Pinca) [#8665](https://github.com/nodejs/node/pull/8665)
- [[`0e2aba96bc`](https://github.com/nodejs/node/commit/0e2aba96bc)] - **doc**: make sure links are correctly passed to marked (Timothy Gu) [#8494](https://github.com/nodejs/node/pull/8494)
- [[`3a43b0d981`](https://github.com/nodejs/node/commit/3a43b0d981)] - **doc**: correct metadata of `Buffer.from` (Anna Henningsen) [#9167](https://github.com/nodejs/node/pull/9167)
- [[`880ca99847`](https://github.com/nodejs/node/commit/880ca99847)] - **doc**: fix broken link in dgram doc (Brian White) [#8365](https://github.com/nodejs/node/pull/8365)
- [[`65ca2af471`](https://github.com/nodejs/node/commit/65ca2af471)] - **doc**: add missing semicolon (Ravindra barthwal) [#7915](https://github.com/nodejs/node/pull/7915)
- [[`da3b938be3`](https://github.com/nodejs/node/commit/da3b938be3)] - **doc**: add `added:` information for globals (Luigi Pinca) [#8901](https://github.com/nodejs/node/pull/8901)
- [[`b4ba4af525`](https://github.com/nodejs/node/commit/b4ba4af525)] - **doc**: add CTC meeting minutes 2016-09-07 (Josh Gavant) [#8499](https://github.com/nodejs/node/pull/8499)
- [[`4b49b0e30c`](https://github.com/nodejs/node/commit/4b49b0e30c)] - **doc**: add CTC meeting minutes 2016-09-14 (Josh Gavant) [#8726](https://github.com/nodejs/node/pull/8726)
- [[`88b0067229`](https://github.com/nodejs/node/commit/88b0067229)] - **doc**: add CTC meeting minutes 2016-09-21 (Josh Gavant) [#8727](https://github.com/nodejs/node/pull/8727)
- [[`f7c4e9489f`](https://github.com/nodejs/node/commit/f7c4e9489f)] - **doc**: update npm LICENSE using license-builder.sh (Kat Marchán) [#8928](https://github.com/nodejs/node/pull/8928)
- [[`6effc4aadc`](https://github.com/nodejs/node/commit/6effc4aadc)] - **doc**: add `added:` information for crypto (Luigi Pinca) [#8281](https://github.com/nodejs/node/pull/8281)
- [[`d750fc6336`](https://github.com/nodejs/node/commit/d750fc6336)] - **doc**: add `added:` information for dgram (Luigi Pinca) [#8196](https://github.com/nodejs/node/pull/8196)
- [[`b92e3fc72e`](https://github.com/nodejs/node/commit/b92e3fc72e)] - **doc**: add `added:` information for util (Luigi Pinca) [#8206](https://github.com/nodejs/node/pull/8206)
- [[`578bf511f9`](https://github.com/nodejs/node/commit/578bf511f9)] - **doc**: add `added:` information for events (Luigi Pinca) [#7822](https://github.com/nodejs/node/pull/7822)
- [[`6ef58e7211`](https://github.com/nodejs/node/commit/6ef58e7211)] - **doc**: add gibfahn to collaborators (Gibson Fahnestock) [#8533](https://github.com/nodejs/node/pull/8533)
- [[`5ff1fc7d86`](https://github.com/nodejs/node/commit/5ff1fc7d86)] - **doc**: add imyller to collaborators (Ilkka Myller) [#8530](https://github.com/nodejs/node/pull/8530)
- [[`88bb65dd74`](https://github.com/nodejs/node/commit/88bb65dd74)] - **doc**: add not-an-aardvark to collaborators (not-an-aardvark) [#8525](https://github.com/nodejs/node/pull/8525)
- [[`5bec1eb0d4`](https://github.com/nodejs/node/commit/5bec1eb0d4)] - **doc**: update onboarding PR landing info (Rich Trott) [#8479](https://github.com/nodejs/node/pull/8479)
- [[`ecd2b52982`](https://github.com/nodejs/node/commit/ecd2b52982)] - **doc**: encourage 2FA before onboarding (Rich Trott) [#8776](https://github.com/nodejs/node/pull/8776)
- [[`2adbd53837`](https://github.com/nodejs/node/commit/2adbd53837)] - **doc**: add commit formats for release blog posts (fen) [#8631](https://github.com/nodejs/node/pull/8631)
- [[`764502bb37`](https://github.com/nodejs/node/commit/764502bb37)] - **doc**: add CTC meeting minutes 2016-08-24 (Josh Gavant) [#8423](https://github.com/nodejs/node/pull/8423)
- [[`3037a9da08`](https://github.com/nodejs/node/commit/3037a9da08)] - **doc**: add eugeneo to collaborators (Eugene Ostroukhov) [#8696](https://github.com/nodejs/node/pull/8696)
- [[`0fd1d8dfd7`](https://github.com/nodejs/node/commit/0fd1d8dfd7)] - **doc**: add ak239 to collaborators (Aleksey Kozyatinskiy) [#8676](https://github.com/nodejs/node/pull/8676)
- [[`64c4bb30fe`](https://github.com/nodejs/node/commit/64c4bb30fe)] - **doc**: add link to help repo in README (Rich Trott) [#8570](https://github.com/nodejs/node/pull/8570)
- [[`d123fc1307`](https://github.com/nodejs/node/commit/d123fc1307)] - **doc**: update exercise portion of onboarding doc (Rich Trott) [#8559](https://github.com/nodejs/node/pull/8559)
- [[`c6b622f6b3`](https://github.com/nodejs/node/commit/c6b622f6b3)] - **doc**: add CTC meeting minutes 2016-08-31 (Josh Gavant) [#8424](https://github.com/nodejs/node/pull/8424)
- [[`055d39c724`](https://github.com/nodejs/node/commit/055d39c724)] - **doc**: add CI help/support info to onboarding doc (Rich Trott) [#8407](https://github.com/nodejs/node/pull/8407)
- [[`a7e6fc08d8`](https://github.com/nodejs/node/commit/a7e6fc08d8)] - **doc**: add 2016-08-17 CTC meeting minutes (Josh Gavant) [#8245](https://github.com/nodejs/node/pull/8245)
- [[`ca63c127c7`](https://github.com/nodejs/node/commit/ca63c127c7)] - **doc**: add 2016-08-10 CTC meeting minutes (Josh Gavant) [#8229](https://github.com/nodejs/node/pull/8229)
- [[`3f2e3dfb32`](https://github.com/nodejs/node/commit/3f2e3dfb32)] - **doc**: update CI content in onboarding doc (Rich Trott) [#8374](https://github.com/nodejs/node/pull/8374)
- [[`9e1325c42e`](https://github.com/nodejs/node/commit/9e1325c42e)] - **doc**: update authors list (James M Snell) [#8346](https://github.com/nodejs/node/pull/8346)
- [[`c529bf5521`](https://github.com/nodejs/node/commit/c529bf5521)] - **doc**: add return type of clientRequest.setTimeout (Mike Ralphson) [#8356](https://github.com/nodejs/node/pull/8356)
- [[`c094b2a51c`](https://github.com/nodejs/node/commit/c094b2a51c)] - **doc**: update targos email in readme per request (James M Snell) [#8389](https://github.com/nodejs/node/pull/8389)
- [[`5c417ee25b`](https://github.com/nodejs/node/commit/5c417ee25b)] - **doc**: update landing pr info in onboarding doc (Rich Trott) [#8344](https://github.com/nodejs/node/pull/8344)
- [[`763fa85ccf`](https://github.com/nodejs/node/commit/763fa85ccf)] - **doc**: bad/better examples for fs.access() and fs.exists() (Dan Fabulich) [#7832](https://github.com/nodejs/node/pull/7832)
- [[`0c933e5bab`](https://github.com/nodejs/node/commit/0c933e5bab)] - **doc**: adding danbev to collaborators (Daniel Bevenius) [#8359](https://github.com/nodejs/node/pull/8359)
- [[`e069dc45b0`](https://github.com/nodejs/node/commit/e069dc45b0)] - **doc**: add lpinca to collaborators (Luigi Pinca) [#8331](https://github.com/nodejs/node/pull/8331)
- [[`e5f4367da5`](https://github.com/nodejs/node/commit/e5f4367da5)] - **doc**: readline write() is processed as input (James M Snell) [#8295](https://github.com/nodejs/node/pull/8295)
- [[`b3617fcc7d`](https://github.com/nodejs/node/commit/b3617fcc7d)] - **doc**: add `added:` information for modules (Luigi Pinca) [#8250](https://github.com/nodejs/node/pull/8250)
- [[`0b605636c5`](https://github.com/nodejs/node/commit/0b605636c5)] - **doc**: add Myles Borins to the CTC (Rod Vagg) [#8260](https://github.com/nodejs/node/pull/8260)
- [[`a8a8f0a6f1`](https://github.com/nodejs/node/commit/a8a8f0a6f1)] - **doc**: add `added:` information for cluster (Anna Henningsen) [#7640](https://github.com/nodejs/node/pull/7640)
- [[`2a2971b26e`](https://github.com/nodejs/node/commit/2a2971b26e)] - **doc**: use blockquotes for Stability: markers (Anna Henningsen) [#7757](https://github.com/nodejs/node/pull/7757)
- [[`3a3fde69c7`](https://github.com/nodejs/node/commit/3a3fde69c7)] - **doc**: fix variable scoping bug in server example code (lazlojuly) [#8124](https://github.com/nodejs/node/pull/8124)
- [[`f1e14e4227`](https://github.com/nodejs/node/commit/f1e14e4227)] - **doc**: fix cluster message event docs (Zach Bjornson) [#8017](https://github.com/nodejs/node/pull/8017)
- [[`9b29cfc3a6`](https://github.com/nodejs/node/commit/9b29cfc3a6)] - **doc**: Clean up roff source in manpage (Alhadis) [#7819](https://github.com/nodejs/node/pull/7819)
- [[`364af49e0f`](https://github.com/nodejs/node/commit/364af49e0f)] - **doc**: add CTC meeting minutes 2016-06-22 (Josh Gavant) [#7390](https://github.com/nodejs/node/pull/7390)
- [[`9892a5ddc3`](https://github.com/nodejs/node/commit/9892a5ddc3)] - **doc**: remove extra spaces and concats in examples (Joe Esposito) [#7885](https://github.com/nodejs/node/pull/7885)
- [[`3ad74089f5`](https://github.com/nodejs/node/commit/3ad74089f5)] - **doc**: correct sample output of buf.compare (Hargobind S. Khalsa) [#7777](https://github.com/nodejs/node/pull/7777)
- [[`26e695c46c`](https://github.com/nodejs/node/commit/26e695c46c)] - **doc**: remove "feature branch" jargon (Rich Trott) [#8194](https://github.com/nodejs/node/pull/8194)
- [[`d676467208`](https://github.com/nodejs/node/commit/d676467208)] - **doc**: remove outdated LTS info from ROADMAP.md (Rich Trott) [#8161](https://github.com/nodejs/node/pull/8161)
- [[`b3545e148d`](https://github.com/nodejs/node/commit/b3545e148d)] - **doc**: update release announce instruction to tweet (Tracy Hinds) [#8126](https://github.com/nodejs/node/pull/8126)
- [[`2032bba65f`](https://github.com/nodejs/node/commit/2032bba65f)] - **doc**: add @joshgav to collaborators (Josh Gavant) [#8146](https://github.com/nodejs/node/pull/8146)
- [[`727c24f3a2`](https://github.com/nodejs/node/commit/727c24f3a2)] - **doc**: update Reviewing section of onboarding doc (Rich Trott)
- [[`04515b891a`](https://github.com/nodejs/node/commit/04515b891a)] - **doc**: move orangemocha to collaborators list (Rich Trott) [#8062](https://github.com/nodejs/node/pull/8062)
- [[`d3344aa216`](https://github.com/nodejs/node/commit/d3344aa216)] - **doc**: Add fhinkel to collaborators (Franziska Hinkelmann) [#8052](https://github.com/nodejs/node/pull/8052)
- [[`532bbde4bf`](https://github.com/nodejs/node/commit/532bbde4bf)] - **doc**: add CTC meeting minutes 2016-08-03 (Josh Gavant) [#7980](https://github.com/nodejs/node/pull/7980)
- [[`98fe74fbc8`](https://github.com/nodejs/node/commit/98fe74fbc8)] - **doc**: fix a markdown error in CTC meeting minutes (Сковорода Никита Андреевич) [#7729](https://github.com/nodejs/node/pull/7729)
- [[`e74daadeb6`](https://github.com/nodejs/node/commit/e74daadeb6)] - **doc**: clarify collaborators & ctc members relationships (yorkie) [#7996](https://github.com/nodejs/node/pull/7996)
- [[`6bfdc92860`](https://github.com/nodejs/node/commit/6bfdc92860)] - **doc**: clarify "Reviewed-By" iff "LGTM" (Bryan English) [#7183](https://github.com/nodejs/node/pull/7183)
- [[`94a82cd0a7`](https://github.com/nodejs/node/commit/94a82cd0a7)] - **doc**: add CTC meeting minutes 2016-07-13 (Josh Gavant) [#7968](https://github.com/nodejs/node/pull/7968)
- [[`012ccf010e`](https://github.com/nodejs/node/commit/012ccf010e)] - **doc**: add CTC meeting minutes 2016-07-20 (Josh Gavant) [#7970](https://github.com/nodejs/node/pull/7970)
- [[`08111e84b1`](https://github.com/nodejs/node/commit/08111e84b1)] - **doc**: use consistent markdown in README (Rich Trott) [#7971](https://github.com/nodejs/node/pull/7971)
- [[`009df788de`](https://github.com/nodejs/node/commit/009df788de)] - **doc**: use `git-secure-tag` for release tags (Fedor Indutny) [#7603](https://github.com/nodejs/node/pull/7603)
- [[`abefdca5ae`](https://github.com/nodejs/node/commit/abefdca5ae)] - **doc**: piscisaureus has stepped-down from the CTC (James M Snell) [#7969](https://github.com/nodejs/node/pull/7969)
- [[`9700660d2b`](https://github.com/nodejs/node/commit/9700660d2b)] - **doc**: add @addaleax to the CTC (Anna Henningsen) [#7966](https://github.com/nodejs/node/pull/7966)
- [[`f255180853`](https://github.com/nodejs/node/commit/f255180853)] - **doc**: add CTC meeting minutes 2016-07-06 (Josh Gavant) [#7570](https://github.com/nodejs/node/pull/7570)
- [[`b60473fac7`](https://github.com/nodejs/node/commit/b60473fac7)] - **doc**: add CTC meeting minutes 2016-06-29 (Josh Gavant) [#7571](https://github.com/nodejs/node/pull/7571)
- [[`ac40b2a9b6`](https://github.com/nodejs/node/commit/ac40b2a9b6)] - **doc**: add CTC meeting minutes 2016-07-27 (William Kapke) [#7900](https://github.com/nodejs/node/pull/7900)
- [[`bbbbb19658`](https://github.com/nodejs/node/commit/bbbbb19658)] - **doc**: add information about CTC quorum rules (Rich Trott) [#7813](https://github.com/nodejs/node/pull/7813)
- [[`d759d4e0a6`](https://github.com/nodejs/node/commit/d759d4e0a6)] - **doc**: remove platform assumption from CONTRIBUTING (Bethany N Griggs) [#7783](https://github.com/nodejs/node/pull/7783)
- [[`b01854dd9d`](https://github.com/nodejs/node/commit/b01854dd9d)] - **doc**: add princejwesley to collaborators (Prince J Wesley) [#7877](https://github.com/nodejs/node/pull/7877)
- [[`26f5168c02`](https://github.com/nodejs/node/commit/26f5168c02)] - **doc**: clarify that the node.js irc channel is not under tsc oversight (James M Snell) [#7810](https://github.com/nodejs/node/pull/7810)
- [[`506e367062`](https://github.com/nodejs/node/commit/506e367062)] - **doc**: update readme with andrasq as a collaborator (Andras) [#7801](https://github.com/nodejs/node/pull/7801)
- [[`590c52a309`](https://github.com/nodejs/node/commit/590c52a309)] - **doc**: update CTC governance information (Rich Trott) [#7719](https://github.com/nodejs/node/pull/7719)
- [[`fdff642e0b`](https://github.com/nodejs/node/commit/fdff642e0b)] - **doc**: fix util.deprecate() example (Evan Lucas) [#7674](https://github.com/nodejs/node/pull/7674)
- [[`8fec02ffb8`](https://github.com/nodejs/node/commit/8fec02ffb8)] - **doc**: delete non-existing zlib constants (Franziska Hinkelmann) [#7520](https://github.com/nodejs/node/pull/7520)
- [[`d6c2e383a2`](https://github.com/nodejs/node/commit/d6c2e383a2)] - **doc**: minor updates to onboarding doc (Rich Trott) [#8060](https://github.com/nodejs/node/pull/8060)
- [[`e46d1e026e`](https://github.com/nodejs/node/commit/e46d1e026e)] - **doc**: add POST_STATUS_TO_PR info to onboarding doc (Rich Trott) [#8059](https://github.com/nodejs/node/pull/8059)
- [[`4f3107190d`](https://github.com/nodejs/node/commit/4f3107190d)] - **doc**: add `added:` info for dgram.\*Membership() (Rich Trott) [#6753](https://github.com/nodejs/node/pull/6753)
- [[`0e52861629`](https://github.com/nodejs/node/commit/0e52861629)] - **doc**: grammar fixes to event loop guide (Ryan Lewis) [#7479](https://github.com/nodejs/node/pull/7479)
- [[`29139bff65`](https://github.com/nodejs/node/commit/29139bff65)] - **doc**: improve server.listen() random port (Phillip Johnsen) [#8025](https://github.com/nodejs/node/pull/8025)
- [[`b680eb99ad`](https://github.com/nodejs/node/commit/b680eb99ad)] - **doctool**: improve the title of pages in doc (yorkie)
- [[`3d6f107a2f`](https://github.com/nodejs/node/commit/3d6f107a2f)] - **fs**: fix handling of `uv_stat_t` fields (Anna Henningsen) [#8515](https://github.com/nodejs/node/pull/8515)
- [[`2e29b76666`](https://github.com/nodejs/node/commit/2e29b76666)] - **intl**: Don't crash if v8BreakIterator not available (Steven R. Loomis) [#4253](https://github.com/nodejs/node/pull/4253)
- [[`f6e332da2d`](https://github.com/nodejs/node/commit/f6e332da2d)] - **lib**: implement consistent brace style (Rich Trott) [#8348](https://github.com/nodejs/node/pull/8348)
- [[`9d9bcd7c55`](https://github.com/nodejs/node/commit/9d9bcd7c55)] - **meta**: clarify process for breaking changes (Rich Trott) [#7955](https://github.com/nodejs/node/pull/7955)
- [[`6d49f22e35`](https://github.com/nodejs/node/commit/6d49f22e35)] - **meta**: include a minimal CTC removal policy (Rich Trott) [#7720](https://github.com/nodejs/node/pull/7720)
- [[`7faf6dc0da`](https://github.com/nodejs/node/commit/7faf6dc0da)] - **meta**: provide example activities (Rich Trott) [#7744](https://github.com/nodejs/node/pull/7744)
- [[`fe48415c60`](https://github.com/nodejs/node/commit/fe48415c60)] - **net**: add length check when normalizing args (Brian White) [#8112](https://github.com/nodejs/node/pull/8112)
- [[`3906206ecc`](https://github.com/nodejs/node/commit/3906206ecc)] - **net**: remove unnecessary variables (Brian White) [#8112](https://github.com/nodejs/node/pull/8112)
- [[`9f1b790f79`](https://github.com/nodejs/node/commit/9f1b790f79)] - **net**: make holding the buffer in memory more robust (Anna Henningsen) [#8252](https://github.com/nodejs/node/pull/8252)
- [[`b630be2309`](https://github.com/nodejs/node/commit/b630be2309)] - **net**: export isIPv4, isIPv6 directly from cares (Sakthipriyan Vairamani) [#7481](https://github.com/nodejs/node/pull/7481)
- [[`c235708bef`](https://github.com/nodejs/node/commit/c235708bef)] - **readline**: keypress trigger for escape character (Prince J Wesley) [#7382](https://github.com/nodejs/node/pull/7382)
- [[`8198dbc5a4`](https://github.com/nodejs/node/commit/8198dbc5a4)] - **repl**: Enable tab completion for global properties (Lance Ball) [#7369](https://github.com/nodejs/node/pull/7369)
- [[`12300626d7`](https://github.com/nodejs/node/commit/12300626d7)] - **src**: no abort from getter if object isn't wrapped (Trevor Norris) [#6184](https://github.com/nodejs/node/pull/6184)
- [[`166a9b85d9`](https://github.com/nodejs/node/commit/166a9b85d9)] - **src**: always clear wrap before persistent Reset() (Trevor Norris) [#6184](https://github.com/nodejs/node/pull/6184)
- [[`b3149cee8c`](https://github.com/nodejs/node/commit/b3149cee8c)] - **src**: inherit first from AsyncWrap (Trevor Norris) [#6184](https://github.com/nodejs/node/pull/6184)
- [[`8b93fddd1b`](https://github.com/nodejs/node/commit/8b93fddd1b)] - **src**: disable stdio buffering (Ben Noordhuis) [#7610](https://github.com/nodejs/node/pull/7610)
- [[`72be320962`](https://github.com/nodejs/node/commit/72be320962)] - **src**: suppress coverity message (cjihrig) [#7587](https://github.com/nodejs/node/pull/7587)
- [[`6ba3ad5d34`](https://github.com/nodejs/node/commit/6ba3ad5d34)] - **src**: guard against overflow in ParseArrayIndex() (Ben Noordhuis) [#7497](https://github.com/nodejs/node/pull/7497)
- [[`e1f961d050`](https://github.com/nodejs/node/commit/e1f961d050)] - **src**: move ParseArrayIndex() to src/node_buffer.cc (Ben Noordhuis) [#7497](https://github.com/nodejs/node/pull/7497)
- [[`57921ebec5`](https://github.com/nodejs/node/commit/57921ebec5)] - **src**: remove unnecessary HandleScopes (Ben Noordhuis) [#7711](https://github.com/nodejs/node/pull/7711)
- [[`6838ad5f8e`](https://github.com/nodejs/node/commit/6838ad5f8e)] - **src**: fix handle leak in UDPWrap::Instantiate() (Ben Noordhuis) [#7711](https://github.com/nodejs/node/pull/7711)
- [[`dadcf6b263`](https://github.com/nodejs/node/commit/dadcf6b263)] - **src**: fix handle leak in BuildStatsObject() (Ben Noordhuis) [#7711](https://github.com/nodejs/node/pull/7711)
- [[`7aa268922a`](https://github.com/nodejs/node/commit/7aa268922a)] - **src**: fix handle leak in Buffer::New() (Ben Noordhuis) [#7711](https://github.com/nodejs/node/pull/7711)
- [[`606deecd16`](https://github.com/nodejs/node/commit/606deecd16)] - **src**: don't include a null character in the WriteConsoleW call (Nikolai Vavilov) [#7764](https://github.com/nodejs/node/pull/7764)
- [[`a5b6c2cdd7`](https://github.com/nodejs/node/commit/a5b6c2cdd7)] - **src**: use RAII for mutexes and condition variables (Ben Noordhuis) [#7334](https://github.com/nodejs/node/pull/7334)
- [[`19d6f06058`](https://github.com/nodejs/node/commit/19d6f06058)] - **stream_base**: always use Base template class (Trevor Norris) [#6184](https://github.com/nodejs/node/pull/6184)
- [[`d5f03db819`](https://github.com/nodejs/node/commit/d5f03db819)] - **test**: fix test-cluster-dgram-1 flakiness (Santiago Gimeno)
- [[`a83bbaa5a3`](https://github.com/nodejs/node/commit/a83bbaa5a3)] - **test**: refactor test-tick-processor (Rich Trott) [#8180](https://github.com/nodejs/node/pull/8180)
- [[`1c81c078c2`](https://github.com/nodejs/node/commit/1c81c078c2)] - **test**: add assert.notDeepStrictEqual() tests (Rich Trott) [#8177](https://github.com/nodejs/node/pull/8177)
- [[`57c98f18a9`](https://github.com/nodejs/node/commit/57c98f18a9)] - **test**: favor `===` over `==` in crypto tests (Rich Trott) [#8176](https://github.com/nodejs/node/pull/8176)
- [[`11f761ab1a`](https://github.com/nodejs/node/commit/11f761ab1a)] - **test**: refactor pummel/test-dtrace-jsstack (Rich Trott) [#8175](https://github.com/nodejs/node/pull/8175)
- [[`2997b79fcc`](https://github.com/nodejs/node/commit/2997b79fcc)] - **test**: favor strict equality in test-exec (Rich Trott) [#8173](https://github.com/nodejs/node/pull/8173)
- [[`558f7d999c`](https://github.com/nodejs/node/commit/558f7d999c)] - **test**: add assert.notDeepEqual() tests (Rich Trott) [#8156](https://github.com/nodejs/node/pull/8156)
- [[`49c488625d`](https://github.com/nodejs/node/commit/49c488625d)] - **test**: add missing assert.deepEqual() test case (Rich Trott) [#8152](https://github.com/nodejs/node/pull/8152)
- [[`eec078cd66`](https://github.com/nodejs/node/commit/eec078cd66)] - **test**: favor strict equality in http tests (Rich Trott) [#8151](https://github.com/nodejs/node/pull/8151)
- [[`e3669f8c21`](https://github.com/nodejs/node/commit/e3669f8c21)] - **test**: favor strict equality in pummel net tests (Rich Trott) [#8135](https://github.com/nodejs/node/pull/8135)
- [[`ac83d199fb`](https://github.com/nodejs/node/commit/ac83d199fb)] - **test**: confirm that assert truncates long values (Rich Trott) [#8134](https://github.com/nodejs/node/pull/8134)
- [[`9c826beef7`](https://github.com/nodejs/node/commit/9c826beef7)] - **test**: favor `===` over `==` in test-timers.js (Rich Trott) [#8131](https://github.com/nodejs/node/pull/8131)
- [[`af02d2a642`](https://github.com/nodejs/node/commit/af02d2a642)] - **test**: favor strict equality check (Rich Trott) [#8130](https://github.com/nodejs/node/pull/8130)
- [[`30034048b0`](https://github.com/nodejs/node/commit/30034048b0)] - **test**: fix assertion in test-watch-file.js (Rich Trott) [#8129](https://github.com/nodejs/node/pull/8129)
- [[`b063dc90b1`](https://github.com/nodejs/node/commit/b063dc90b1)] - **test**: use strict equality in regression test (Rich Trott) [#8098](https://github.com/nodejs/node/pull/8098)
- [[`dc7bc2e679`](https://github.com/nodejs/node/commit/dc7bc2e679)] - **test**: add test for debug usage message (Rich Trott) [#8061](https://github.com/nodejs/node/pull/8061)
- [[`ce2cfbdc3a`](https://github.com/nodejs/node/commit/ce2cfbdc3a)] - **test**: console constructor missing new keyword (Rich Trott) [#8003](https://github.com/nodejs/node/pull/8003)
- [[`69f4edd368`](https://github.com/nodejs/node/commit/69f4edd368)] - **test**: speed up test-net-reconnect-error (Rich Trott) [#7886](https://github.com/nodejs/node/pull/7886)
- [[`50acf72d80`](https://github.com/nodejs/node/commit/50acf72d80)] - **test**: increase RAM requirement for intensive tests (Rich Trott) [#7772](https://github.com/nodejs/node/pull/7772)
- [[`924ea0a2bd`](https://github.com/nodejs/node/commit/924ea0a2bd)] - **test**: fix flaky test-http-server-consumed-timeout (Rich Trott) [#7717](https://github.com/nodejs/node/pull/7717)
- [[`97a3d89c80`](https://github.com/nodejs/node/commit/97a3d89c80)] - **test**: improve coverage of the util module (Michaël Zasso) [#8633](https://github.com/nodejs/node/pull/8633)
- [[`52bb37734b`](https://github.com/nodejs/node/commit/52bb37734b)] - **test**: mark test-child-process-fork-dgram as flaky (Michael Dawson) [#8274](https://github.com/nodejs/node/pull/8274)
- [[`97c68ddaad`](https://github.com/nodejs/node/commit/97c68ddaad)] - **test**: improve error message in test-tick-processor (Rich Trott) [#7693](https://github.com/nodejs/node/pull/7693)
- [[`cd9e8e0361`](https://github.com/nodejs/node/commit/cd9e8e0361)] - **test**: fix old tty tests (Jeremiah Senkpiel) [#7613](https://github.com/nodejs/node/pull/7613)
- [[`22990d8851`](https://github.com/nodejs/node/commit/22990d8851)] - **test**: move parallel/test-tty-\* to pseudo-tty/ (Jeremiah Senkpiel) [#7613](https://github.com/nodejs/node/pull/7613)
- [[`afee32fed5`](https://github.com/nodejs/node/commit/afee32fed5)] - **test**: fix `fs-watch-recursive` flakiness on OS X (Santiago Gimeno) [#4629](https://github.com/nodejs/node/pull/4629)
- [[`c543f4a879`](https://github.com/nodejs/node/commit/c543f4a879)] - **test**: stream writable ended state (Italo A. Casas) [#8778](https://github.com/nodejs/node/pull/8778)
- [[`f46a04cc6d`](https://github.com/nodejs/node/commit/f46a04cc6d)] - **test**: add tests for add/remove header after sent (Niklas Ingholt) [#8682](https://github.com/nodejs/node/pull/8682)
- [[`e79351c3ac`](https://github.com/nodejs/node/commit/e79351c3ac)] - **test**: improve test-https-agent.js (Dan.Williams) [#8517](https://github.com/nodejs/node/pull/8517)
- [[`9ffb2f3c0d`](https://github.com/nodejs/node/commit/9ffb2f3c0d)] - **test**: add coverage for client.\_addHandle() (Rich Trott) [#8518](https://github.com/nodejs/node/pull/8518)
- [[`8da2dcb70a`](https://github.com/nodejs/node/commit/8da2dcb70a)] - **test**: refector parallel/test-http.js (Junshu Okamoto) [#8471](https://github.com/nodejs/node/pull/8471)
- [[`69404ec473`](https://github.com/nodejs/node/commit/69404ec473)] - **test**: fix flaky test-force-repl (Rich Trott) [#8484](https://github.com/nodejs/node/pull/8484)
- [[`5a07bb62ea`](https://github.com/nodejs/node/commit/5a07bb62ea)] - **test**: swapped == and equal to === and strictEqual (Christopher Dunavan) [#8472](https://github.com/nodejs/node/pull/8472)
- [[`ad1230e731`](https://github.com/nodejs/node/commit/ad1230e731)] - **test**: skip pseudo-tty/no_dropped_stdio test (Michael Dawson) [#8470](https://github.com/nodejs/node/pull/8470)
- [[`6d03170751`](https://github.com/nodejs/node/commit/6d03170751)] - **test**: clean up net server try ports test (Thomas Hunter II) [#8458](https://github.com/nodejs/node/pull/8458)
- [[`775c84ec38`](https://github.com/nodejs/node/commit/775c84ec38)] - **test**: add test-debug-protocol-execute (Rich Trott) [#8454](https://github.com/nodejs/node/pull/8454)
- [[`0d1082426a`](https://github.com/nodejs/node/commit/0d1082426a)] - **test**: mark pseudo-tty/no_dropped_stdio as flaky (Michael Dawson) [#8385](https://github.com/nodejs/node/pull/8385)
- [[`c034c861bb`](https://github.com/nodejs/node/commit/c034c861bb)] - **test**: test non-buffer/string with zlib (Rich Trott) [#8350](https://github.com/nodejs/node/pull/8350)
- [[`bb8690433c`](https://github.com/nodejs/node/commit/bb8690433c)] - **test**: fix ::1 error in test-dns-ipv6 (Gibson Fahnestock) [#8254](https://github.com/nodejs/node/pull/8254)
- [[`2f458ea663`](https://github.com/nodejs/node/commit/2f458ea663)] - **test**: add test for zlib.create\*Raw() (Rich Trott) [#8306](https://github.com/nodejs/node/pull/8306)
- [[`a368ea673c`](https://github.com/nodejs/node/commit/a368ea673c)] - **test**: refactor test-debug-signal-cluster (Rich Trott) [#8289](https://github.com/nodejs/node/pull/8289)
- [[`a48469f098`](https://github.com/nodejs/node/commit/a48469f098)] - **test**: add check in test-signal-handler (Rich Trott) [#8248](https://github.com/nodejs/node/pull/8248)
- [[`cadb2612c6`](https://github.com/nodejs/node/commit/cadb2612c6)] - **test**: add test for attempted multiple IPC channels (cjihrig) [#8159](https://github.com/nodejs/node/pull/8159)
- [[`21c1b8467e`](https://github.com/nodejs/node/commit/21c1b8467e)] - **test**: decrease inconsistency in the common.js (Vse Mozhet Byt) [#7758](https://github.com/nodejs/node/pull/7758)
- [[`d40873ddcd`](https://github.com/nodejs/node/commit/d40873ddcd)] - **test**: ensure stream preprocessing order (Vse Mozhet Byt) [#7741](https://github.com/nodejs/node/pull/7741)
- [[`0e1f098b09`](https://github.com/nodejs/node/commit/0e1f098b09)] - **test**: avoid usage of mixed IPV6 addresses (Gireesh Punathil) [#7702](https://github.com/nodejs/node/pull/7702)
- [[`741373cb49`](https://github.com/nodejs/node/commit/741373cb49)] - **test**: clean up test-buffer-badhex (Jeremiah Senkpiel) [#7773](https://github.com/nodejs/node/pull/7773)
- [[`58f3fa17eb`](https://github.com/nodejs/node/commit/58f3fa17eb)] - **test**: s/assert.fail/common.fail as appropriate (cjihrig) [#7735](https://github.com/nodejs/node/pull/7735)
- [[`b0e2f9a37a`](https://github.com/nodejs/node/commit/b0e2f9a37a)] - **test**: add common.rootDir (cjihrig) [#7685](https://github.com/nodejs/node/pull/7685)
- [[`c94f3a5784`](https://github.com/nodejs/node/commit/c94f3a5784)] - **test**: handle IPv6 localhost issues within tests (Rich Trott) [#7766](https://github.com/nodejs/node/pull/7766)
- [[`b64828d8df`](https://github.com/nodejs/node/commit/b64828d8df)] - **test**: accept expected AIX result test-stdio-closed (Rich Trott) [#8755](https://github.com/nodejs/node/pull/8755)
- [[`3dbcc3d2d9`](https://github.com/nodejs/node/commit/3dbcc3d2d9)] - **test**: fix flaky test-\*-connect-address-family (Rich Trott) [#7605](https://github.com/nodejs/node/pull/7605)
- [[`733233d3ea`](https://github.com/nodejs/node/commit/733233d3ea)] - **test**: add uncaught exception test for debugger (Rich Trott) [#8087](https://github.com/nodejs/node/pull/8087)
- [[`c9af24d2a7`](https://github.com/nodejs/node/commit/c9af24d2a7)] - **test**: add test for assert.notStrictEqual() (Rich Trott) [#8091](https://github.com/nodejs/node/pull/8091)
- [[`337d2dd381`](https://github.com/nodejs/node/commit/337d2dd381)] - **test**: implement consistent braces (Rich Trott) [#8348](https://github.com/nodejs/node/pull/8348)
- [[`77df523264`](https://github.com/nodejs/node/commit/77df523264)] - **test**: exclude tests for AIX (Michael Dawson) [#8076](https://github.com/nodejs/node/pull/8076)
- [[`50ae37e350`](https://github.com/nodejs/node/commit/50ae37e350)] - **test**: add --repeat option to tools/test.py (Michael Dawson) [#6700](https://github.com/nodejs/node/pull/6700)
- [[`ea72e9f143`](https://github.com/nodejs/node/commit/ea72e9f143)] - **test,doc**: clarify `buf.indexOf(num)` input range (Anna Henningsen) [#7611](https://github.com/nodejs/node/pull/7611)
- [[`c841b5a6b9`](https://github.com/nodejs/node/commit/c841b5a6b9)] - **tls**: copy the Buffer object before using (Sakthipriyan Vairamani) [#8055](https://github.com/nodejs/node/pull/8055)
- [[`6076293d6c`](https://github.com/nodejs/node/commit/6076293d6c)] - **tls_wrap**: do not abort on new TLSWrap() (Trevor Norris) [#6184](https://github.com/nodejs/node/pull/6184)
- [[`6e5906c7f1`](https://github.com/nodejs/node/commit/6e5906c7f1)] - **tools**: use long format for gpg fingerprint (Myles Borins) [#9258](https://github.com/nodejs/node/pull/9258)
- [[`7409c332b8`](https://github.com/nodejs/node/commit/7409c332b8)] - **tools**: check tag is on github before release (Rod Vagg) [#9142](https://github.com/nodejs/node/pull/9142)
- [[`b632badda2`](https://github.com/nodejs/node/commit/b632badda2)] - **tools**: make detached SHASUM .sig file for releases (Rod Vagg) [#9071](https://github.com/nodejs/node/pull/9071)
- [[`5867ffe27e`](https://github.com/nodejs/node/commit/5867ffe27e)] - **tools**: explicitly set digest algo for SHASUM to 256 (Rod Vagg) [#9071](https://github.com/nodejs/node/pull/9071)
- [[`bdfa3b388b`](https://github.com/nodejs/node/commit/bdfa3b388b)] - **tools**: favor === over == in license2rtf.js (Rich Trott)
- [[`d7e3edc744`](https://github.com/nodejs/node/commit/d7e3edc744)] - **tools**: add remark-lint configuration in .remarkrc (Сковорода Никита Андреевич) [#7729](https://github.com/nodejs/node/pull/7729)
- [[`afbfbc04c9`](https://github.com/nodejs/node/commit/afbfbc04c9)] - **tools**: add .vscode folder to .gitignore (Josh Gavant) [#7967](https://github.com/nodejs/node/pull/7967)
- [[`3f4a5fe61e`](https://github.com/nodejs/node/commit/3f4a5fe61e)] - **tools**: increase lint coverage (Rich Trott) [#7647](https://github.com/nodejs/node/pull/7647)
- [[`d1a50b3ed2`](https://github.com/nodejs/node/commit/d1a50b3ed2)] - **tools**: enforce JS brace style with linting (Rich Trott) [#8348](https://github.com/nodejs/node/pull/8348)
- [[`76b8d81f38`](https://github.com/nodejs/node/commit/76b8d81f38)] - **tools,test**: show signal code when test crashes (Santiago Gimeno) [#7859](https://github.com/nodejs/node/pull/7859)
- [[`389a6d2cc2`](https://github.com/nodejs/node/commit/389a6d2cc2)] - **url**: fix off-by-one error in loop handling dots (Luigi Pinca) [#8420](https://github.com/nodejs/node/pull/8420)
- [[`be9d9bd7c3`](https://github.com/nodejs/node/commit/be9d9bd7c3)] - **url**: fix inconsistent port in url.resolveObject (Ilkka Myller) [#8214](https://github.com/nodejs/node/pull/8214)
- [[`96cfa926bd`](https://github.com/nodejs/node/commit/96cfa926bd)] - **url**: `url.format()` encodes all `#` in `search` (Ilkka Myller) [#8072](https://github.com/nodejs/node/pull/8072)
- [[`f7796f23e3`](https://github.com/nodejs/node/commit/f7796f23e3)] - **util**: inspect boxed symbols like other primitives (Anna Henningsen) [#7641](https://github.com/nodejs/node/pull/7641)
- [[`410e083d7c`](https://github.com/nodejs/node/commit/410e083d7c)] - **win,build**: forward release_urlbase to configure (João Reis) [#8430](https://github.com/nodejs/node/pull/8430)
- [[`26e73740e9`](https://github.com/nodejs/node/commit/26e73740e9)] - **win,build**: exit when addons fail to build (João Reis) [#8412](https://github.com/nodejs/node/pull/8412)
- [[`30e751f38b`](https://github.com/nodejs/node/commit/30e751f38b)] - **win,build**: skip finding VS when not needed (João Reis) [#8412](https://github.com/nodejs/node/pull/8412)
- [[`b3090f8e64`](https://github.com/nodejs/node/commit/b3090f8e64)] - **win,build**: fail on invalid option in vcbuild (João Reis) [#8412](https://github.com/nodejs/node/pull/8412)
- [[`1b5213bfc3`](https://github.com/nodejs/node/commit/1b5213bfc3)] - **win,msi**: fix inclusion of translations (João Reis) [#7798](https://github.com/nodejs/node/pull/7798)
- [[`e8be413d0d`](https://github.com/nodejs/node/commit/e8be413d0d)] - **win,msi**: add zh-CN translations for the installer (Minqi Pan) [#2569](https://github.com/nodejs/node/pull/2569)
- [[`99f85b8340`](https://github.com/nodejs/node/commit/99f85b8340)] - **win,msi**: Added Italian translation (Matteo Collina) [#4647](https://github.com/nodejs/node/pull/4647)

Windows 32-bit Installer: https://nodejs.org/dist/v4.6.2/node-v4.6.2-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v4.6.2/node-v4.6.2-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v4.6.2/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v4.6.2/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v4.6.2/node-v4.6.2.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v4.6.2/node-v4.6.2-darwin-x64.tar.gz \
Linux 32-bit Binary: https://nodejs.org/dist/v4.6.2/node-v4.6.2-linux-x86.tar.xz \
Linux 64-bit Binary: https://nodejs.org/dist/v4.6.2/node-v4.6.2-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v4.6.2/node-v4.6.2-linux-ppc64le.tar.xz \
Linux PPC BE 64-bit Binary: https://nodejs.org/dist/v4.6.2/node-v4.6.2-linux-ppc64.tar.xz \
SmartOS 32-bit Binary: https://nodejs.org/dist/v4.6.2/node-v4.6.2-sunos-x86.tar.xz \ \
SmartOS 64-bit Binary: https://nodejs.org/dist/v4.6.2/node-v4.6.2-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v4.6.2/node-v4.6.2-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v4.6.2/node-v4.6.2-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v4.6.2/node-v4.6.2-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v4.6.2/node-v4.6.2.tar.gz \
Other release files: https://nodejs.org/dist/v4.6.2/ \
Documentation: https://nodejs.org/docs/v4.6.2/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

0e19ba9c0e16ea1d96df2a3a5edc07e0950325f465ab1049d6ab7b7d98d384f2  node-v4.6.2-darwin-x64.tar.gz
9d8e22131a9aa50d33b599310763f02c9c6cc1f3e8eac0b72c9998e74bd2b571  node-v4.6.2-darwin-x64.tar.xz
3b3b7045cfb372a8d1472edab596213111369d6fca30dad5714c9f22a1da9edf  node-v4.6.2-headers.tar.gz
35e654a84d75aec865de48779b22ebdf16765f10adbdabceae298dd6244fa378  node-v4.6.2-headers.tar.xz
831eab2a0fed0c3716e2c6bb9d9f2d64b8f365e5501ec373eb207ce5621ba6d2  node-v4.6.2-linux-arm64.tar.gz
389e9987852d5f813d7708602b84a9ef94b28bcfe2e080afcd5b6871ab36c0f8  node-v4.6.2-linux-arm64.tar.xz
a45b09f7b1274a5a2c494aa529fb84fbf8f3040c9b24e5be73a8896b24c803e6  node-v4.6.2-linux-armv6l.tar.gz
2f1ae711d425cec2e028917907ce82039a1422a0454480000a17281f57b271d0  node-v4.6.2-linux-armv6l.tar.xz
969fa30c2c12b6165fa9b56b8466e9a6113129bdbfe46861b0990b15ff43c483  node-v4.6.2-linux-armv7l.tar.gz
515a8f80ae7d9ba02dcec3591cf42d62716efb178f19f971da4cf58862da6161  node-v4.6.2-linux-armv7l.tar.xz
30818e8ebc302bdd5b60d0d7287d336ce4db51302eacb7ad5684d7a1f013af48  node-v4.6.2-linux-ppc64le.tar.gz
f4581c5de35968b8998f3d4761141cdd662450e6cbf0712150ca125649559091  node-v4.6.2-linux-ppc64le.tar.xz
64c06404a3b0cba67e0b62f3ecc2cb7857fd11a22535f99c97beb231520e1ae6  node-v4.6.2-linux-ppc64.tar.gz
95ffcfed1143067db5f12d62abced07257ec79feaa055e340bfa12b56f2692d3  node-v4.6.2-linux-ppc64.tar.xz
0a2d6417526509bc4c0d953e1563b0d5c972fe270a45da0ca8ed02d41fb1c223  node-v4.6.2-linux-x64.tar.gz
309f3d7bb0a4929a7e723d4fc65ee2773cf8ba334604b0d139c8691c06815cfc  node-v4.6.2-linux-x64.tar.xz
6f992d823873eebb6cb99c96f5ac4f0dd8a205824bb4a54beb93eb60f7ca22eb  node-v4.6.2-linux-x86.tar.gz
1a6e5e2c671637182c66b5c26c576fe228055a9ddaf16f1492c56a155fa3a810  node-v4.6.2-linux-x86.tar.xz
a25a61f920ca6406e525d955b89ac6347a2d9dc1d80ae6cc0f6ceb50fb8d5e30  node-v4.6.2.pkg
d5e4c9f3b1a9d9756a81ea810cf2bfd8de8263cae1e9e1b558584637503f0d15  node-v4.6.2-sunos-x64.tar.gz
6fa50de72dbcdbc1372839c01184da584493301f274f4bb94dc83e56ed0c4c4c  node-v4.6.2-sunos-x64.tar.xz
22c32dcfd70e556a01bb13648f240b4911cf990f1682985d4c311ecd8addcb21  node-v4.6.2-sunos-x86.tar.gz
9f7c5c60daa9f69487e6bd40b8cfe89254482302053751b04dc773d8178592e9  node-v4.6.2-sunos-x86.tar.xz
3c8f3af398c348cdf90fef9b983e4b46aae96a56238236542925e0bb4ee27ee8  node-v4.6.2.tar.gz
859458b337686556c1f9e195062abc6c394210c2280ee47ed1ddc725b2982c9e  node-v4.6.2.tar.xz
d16157a712c7bd0e65ea290606b5dcd4bbb43138760a0b4ed3ef159369352a13  node-v4.6.2-win-x64.7z
f4106162d3b7827747c6da994421474e6882caf78e0f99c50572e766e82c4e06  node-v4.6.2-win-x64.zip
6b362791d885024ebee32714a741c617d3bc789bc1dd1ac78b01af77b4c39f53  node-v4.6.2-win-x86.7z
508a0d6df3558ca0c176eda527a60ec4f4ed5feb4ba5e18b73396f055ddb6f35  node-v4.6.2-win-x86.zip
ff0a04bf22d3dd9c8885fbcce15552dc7c0d80fa60c6755a61e6568bf537cbad  node-v4.6.2-x64.msi
9ac90674c7fb6194e2c11262d9d9d8fcf83485f4bb6681204a9fdc48a7dd2f16  node-v4.6.2-x86.msi
3806127084f3b168508ba62588ae5a85ae0069910db6dcd82b35ab2c7be26d6a  win-x64/node.exe
8587d2f89a1af37741674d6e0f175c09e1711a22394d3b06e7c946aa32f94418  win-x64/node.lib
767be40936a8b887aebc9413117f67837b8c4f1aa8a9939bc2947fc9e287dbf0  win-x64/node_pdb.7z
2e0bcdf5d2a354a2fa5ba2cdd053af3a6231cfcabc5d3e473d7c1608ae0766fb  win-x64/node_pdb.zip
42bb7ad25cd214d62a14b030b207796d63f44fc1ba0c288ade74d18462831a2c  win-x86/node.exe
fb4da42d970192a805df211b53832e3e96a58a22a92b7116b8749b7d87561dd3  win-x86/node.lib
8b807d92968469e19dad255912dd7347125b1d120a0f6719f48be2959f94fa5f  win-x86/node_pdb.7z
313da37cd7efda81a1e7a381a2b98041a2409a778e208899f248fd66216863f8  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQEcBAEBCAAGBQJYbopHAAoJEJM7AfQLXKlGMgAH/04odpZVHOb4arZhTVkkWSUa
EZf5/ViWM/zBhXQIIbMIfxCo0pUTVGFp3UroDv55l4ya+Utaukk0TulmkSPqJ9Q6
9R+9wOWLludenDqfxaXdMnw0tGmqzB9wxAG/gxfZuqOurzKpU2MRASlOgVydh10g
/5YWc0TbZs/qCHrXL3jiYNZAhqik5Sq2+xeavHaw5ET5ivBGs659kla7VescLoYJ
7/t6vas2GeLDRuEI024nFnQVyqatHDn1pZhkwM+Gcf5OESNPjKoRsSuxf4pNocgO
oFJveFaXq6dLMiNDgRr4qE61pR3o9Qem0P/q2uTXpa2qkD22DwAiDa6yF0+h8rM=
=dr9d
-----END PGP SIGNATURE-----

```
