---
date: '2018-06-06T14:26:37.350Z'
category: release
title: Node v10.4.0 (Current)
layout: blog-post
author: <PERSON><PERSON>
---

### Notable Changes

- **deps**:
  - update V8 to 6.7.288.43 (<PERSON><PERSON><PERSON>) [#19989](https://github.com/nodejs/node/pull/19989)
- **stream**:
  - ensure Stream.pipeline re-throws errors without callback (<PERSON>) [#20437](https://github.com/nodejs/node/pull/20437)

### Commits

- [[`9ada68b186`](https://github.com/nodejs/node/commit/9ada68b186)] - **benchmark**: refactor benchmark/assert/throws.js (<PERSON>) [#21030](https://github.com/nodejs/node/pull/21030)
- [[`b8470b929f`](https://github.com/nodejs/node/commit/b8470b929f)] - **benchmark**: refactor deepequal-typedarrays (<PERSON>) [#21030](https://github.com/nodejs/node/pull/21030)
- [[`686587ba1a`](https://github.com/nodejs/node/commit/686587ba1a)] - **benchmark**: refactor deepequal-set (Rich Trott) [#21030](https://github.com/nodejs/node/pull/21030)
- [[`56c67595db`](https://github.com/nodejs/node/commit/56c67595db)] - **benchmark**: refactor prims-and-objs-big-loop (Rich Trott) [#21030](https://github.com/nodejs/node/pull/21030)
- [[`6fbb00e887`](https://github.com/nodejs/node/commit/6fbb00e887)] - **benchmark**: refactor prims-and-objs-big-array-set (Rich Trott) [#21030](https://github.com/nodejs/node/pull/21030)
- [[`4d714421e9`](https://github.com/nodejs/node/commit/4d714421e9)] - **benchmark**: refactor deepequal-object (Rich Trott) [#21030](https://github.com/nodejs/node/pull/21030)
- [[`9b0fc59723`](https://github.com/nodejs/node/commit/9b0fc59723)] - **benchmark**: refactor deepequal-map (Rich Trott) [#21030](https://github.com/nodejs/node/pull/21030)
- [[`90d86586d2`](https://github.com/nodejs/node/commit/90d86586d2)] - **benchmark**: refactor deepequal-buffer (Rich Trott) [#21030](https://github.com/nodejs/node/pull/21030)
- [[`be249d9eb5`](https://github.com/nodejs/node/commit/be249d9eb5)] - **benchmark**: fix "comparisons"' typo (Yuta Hiroto) [#21085](https://github.com/nodejs/node/pull/21085)
- [[`bad3c92124`](https://github.com/nodejs/node/commit/bad3c92124)] - **(SEMVER-MINOR)** **build**: reset embedder string to "-node.0" (Michaël Zasso) [#19989](https://github.com/nodejs/node/pull/19989)
- [[`35d6661973`](https://github.com/nodejs/node/commit/35d6661973)] - **deps**: cherry-pick 6989b3f6d7 from V8 upstream (Timothy Gu) [#20826](https://github.com/nodejs/node/pull/20826)
- [[`4e788dc2f5`](https://github.com/nodejs/node/commit/4e788dc2f5)] - **(SEMVER-MINOR)** **deps**: backport 91ddb65d from upstream V8 (Maya Lekova) [#19989](https://github.com/nodejs/node/pull/19989)
- [[`fb2686148e`](https://github.com/nodejs/node/commit/fb2686148e)] - **deps**: cherry-pick ff0a9793334 from upstream V8 (Anna Henningsen) [#20719](https://github.com/nodejs/node/pull/20719)
- [[`40c8bbecec`](https://github.com/nodejs/node/commit/40c8bbecec)] - **deps**: cherry-pick 23652c5f from upstream V8 (Eugene Ostroukhov) [#20608](https://github.com/nodejs/node/pull/20608)
- [[`a7aff77a97`](https://github.com/nodejs/node/commit/a7aff77a97)] - **(SEMVER-MINOR)** **deps**: cherry-pick 39d546a from upstream V8 (Gus Caplan) [#20016](https://github.com/nodejs/node/pull/20016)
- [[`fed1d18054`](https://github.com/nodejs/node/commit/fed1d18054)] - **(SEMVER-MINOR)** **deps**: update v8.gyp (Michaël Zasso) [#19989](https://github.com/nodejs/node/pull/19989)
- [[`da8ad4aba9`](https://github.com/nodejs/node/commit/da8ad4aba9)] - **(SEMVER-MINOR)** **deps**: update V8 to 6.7.288.43 (Michaël Zasso) [#19989](https://github.com/nodejs/node/pull/19989)
- [[`2c671ab2fd`](https://github.com/nodejs/node/commit/2c671ab2fd)] - **doc**: fix typo in addons.md (Rich Trott) [#21137](https://github.com/nodejs/node/pull/21137)
- [[`e2a792866c`](https://github.com/nodejs/node/commit/e2a792866c)] - **doc**: add offboarding doc (Rich Trott) [#21103](https://github.com/nodejs/node/pull/21103)
- [[`15aa3c1046`](https://github.com/nodejs/node/commit/15aa3c1046)] - **doc**: add notable-change to onboarding.md exercise (Rich Trott) [#21040](https://github.com/nodejs/node/pull/21040)
- [[`29c35bd0de`](https://github.com/nodejs/node/commit/29c35bd0de)] - **doc**: remove link prediction from STYLE_GUIDE.md (Rich Trott) [#21031](https://github.com/nodejs/node/pull/21031)
- [[`261ef1d242`](https://github.com/nodejs/node/commit/261ef1d242)] - **doc**: remove POST_STATUS_TO_PR from onboarding.md (Rich Trott) [#21042](https://github.com/nodejs/node/pull/21042)
- [[`2edf1728a0`](https://github.com/nodejs/node/commit/2edf1728a0)] - **doc**: fix typos on `e.g.` abbreviations (Rich Trott) [#21045](https://github.com/nodejs/node/pull/21045)
- [[`b1f0907416`](https://github.com/nodejs/node/commit/b1f0907416)] - **doc**: use "is" rather than "has been" (Rich Trott) [#21043](https://github.com/nodejs/node/pull/21043)
- [[`f5bf2c8d08`](https://github.com/nodejs/node/commit/f5bf2c8d08)] - **doc**: move upstream information to onboarding doc (Rich Trott) [#21029](https://github.com/nodejs/node/pull/21029)
- [[`09aec436cb`](https://github.com/nodejs/node/commit/09aec436cb)] - **doc**: remove vestigial onboarding section (Rich Trott) [#21028](https://github.com/nodejs/node/pull/21028)
- [[`fd201e0d32`](https://github.com/nodejs/node/commit/fd201e0d32)] - **doc**: add guides on writing tests involving promises (Joyee Cheung) [#20988](https://github.com/nodejs/node/pull/20988)
- [[`4cd44203de`](https://github.com/nodejs/node/commit/4cd44203de)] - **doc**: remove invalid `vm.Script` arguments (Simen Bekkhus) [#20984](https://github.com/nodejs/node/pull/20984)
- [[`4012e0550a`](https://github.com/nodejs/node/commit/4012e0550a)] - **doc**: fix typo in n-api.md (ohbarye) [#21060](https://github.com/nodejs/node/pull/21060)
- [[`bb8d341714`](https://github.com/nodejs/node/commit/bb8d341714)] - **doc**: better font stack for monospace in docs (Roman Reiss) [#21036](https://github.com/nodejs/node/pull/21036)
- [[`1b8e8e90af`](https://github.com/nodejs/node/commit/1b8e8e90af)] - **doc**: make minor improvements to fs.realpath() docs (Rich Trott) [#20953](https://github.com/nodejs/node/pull/20953)
- [[`c2ae93db63`](https://github.com/nodejs/node/commit/c2ae93db63)] - **doc**: add missing link for 10.3.0 changelog (Myles Borins) [#21017](https://github.com/nodejs/node/pull/21017)
- [[`8dc7c883a7`](https://github.com/nodejs/node/commit/8dc7c883a7)] - **doc**: improve note on zlib APIs threadpool usage (Luigi Pinca) [#20380](https://github.com/nodejs/node/pull/20380)
- [[`ab43581f15`](https://github.com/nodejs/node/commit/ab43581f15)] - **doc**: make constants enumeration consistent (Diego Rodríguez Baquero) [#20991](https://github.com/nodejs/node/pull/20991)
- [[`44ef458d9c`](https://github.com/nodejs/node/commit/44ef458d9c)] - **fs**: ensure options.flag defaults to 'r' in readFile (Unknown) [#20268](https://github.com/nodejs/node/pull/20268)
- [[`341b2c21f3`](https://github.com/nodejs/node/commit/341b2c21f3)] - **http2**: fix premature destroy (Anatoli Papirovski) [#21051](https://github.com/nodejs/node/pull/21051)
- [[`d4787cfd61`](https://github.com/nodejs/node/commit/d4787cfd61)] - **http2**: force through RST_STREAM in destroy (Anatoli Papirovski) [#21016](https://github.com/nodejs/node/pull/21016)
- [[`2a9912c0df`](https://github.com/nodejs/node/commit/2a9912c0df)] - **http2**: delay closing stream (Anatoli Papirovski) [#20997](https://github.com/nodejs/node/pull/20997)
- [[`182c73bf7f`](https://github.com/nodejs/node/commit/182c73bf7f)] - **http2**: switch to new runtime-controlled debugging system (Anna Henningsen) [#20987](https://github.com/nodejs/node/pull/20987)
- [[`1d22254c4d`](https://github.com/nodejs/node/commit/1d22254c4d)] - **https**: removed extra \_http_server require (ErnestoSalazar) [#21069](https://github.com/nodejs/node/pull/21069)
- [[`1c211ec901`](https://github.com/nodejs/node/commit/1c211ec901)] - **inspector**: code cleanup (Eugene Ostroukhov) [#21070](https://github.com/nodejs/node/pull/21070)
- [[`a30bf55e69`](https://github.com/nodejs/node/commit/a30bf55e69)] - **lib**: use focused ESLint disabling in util.js (Rich Trott) [#21041](https://github.com/nodejs/node/pull/21041)
- [[`f2c9e5af09`](https://github.com/nodejs/node/commit/f2c9e5af09)] - **lib**: introduce internal/validators (Michaël Zasso) [#21149](https://github.com/nodejs/node/pull/21149)
- [[`46d1025add`](https://github.com/nodejs/node/commit/46d1025add)] - **net**: use object destructuring (starkewang) [#20959](https://github.com/nodejs/node/pull/20959)
- [[`afc811cc1c`](https://github.com/nodejs/node/commit/afc811cc1c)] - **src**: break out of timers loop if `!can_call_into_js()` (Anna Henningsen) [#20884](https://github.com/nodejs/node/pull/20884)
- [[`8862f0a613`](https://github.com/nodejs/node/commit/8862f0a613)] - **src**: store pointer to Environment on DestroyParam (Anatoli Papirovski) [#21099](https://github.com/nodejs/node/pull/21099)
- [[`66f4c7bdec`](https://github.com/nodejs/node/commit/66f4c7bdec)] - **src**: fix typo string_search.h comment (Masashi Hirano) [#21115](https://github.com/nodejs/node/pull/21115)
- [[`f79096a3f2`](https://github.com/nodejs/node/commit/f79096a3f2)] - **src**: do not cache `NumberOfHeapSpaces()` globally (Anna Henningsen) [#20971](https://github.com/nodejs/node/pull/20971)
- [[`7c0c61bde1`](https://github.com/nodejs/node/commit/7c0c61bde1)] - **(SEMVER-MINOR)** **src**: update postmortem constant name (cjihrig) [#19989](https://github.com/nodejs/node/pull/19989)
- [[`2d3137c5a9`](https://github.com/nodejs/node/commit/2d3137c5a9)] - **(SEMVER-MINOR)** **src**: fix GetCpuProfiler() deprecation warning (Michaël Zasso) [#19989](https://github.com/nodejs/node/pull/19989)
- [[`af62a16ff6`](https://github.com/nodejs/node/commit/af62a16ff6)] - **(SEMVER-MINOR)** **_Revert_** "**src**: fix GetCpuProfiler() deprecation warning" (Michaël Zasso) [#19989](https://github.com/nodejs/node/pull/19989)
- [[`af06581b84`](https://github.com/nodejs/node/commit/af06581b84)] - **src**: restore stdio on program exit (Ben Noordhuis) [#20592](https://github.com/nodejs/node/pull/20592)
- [[`45eeea4330`](https://github.com/nodejs/node/commit/45eeea4330)] - **src**: implement debug output utilities (Anna Henningsen) [#20987](https://github.com/nodejs/node/pull/20987)
- [[`ebbd036d0b`](https://github.com/nodejs/node/commit/ebbd036d0b)] - **src**: remove unused private data member (Ben Noordhuis) [#20974](https://github.com/nodejs/node/pull/20974)
- [[`d4f507b23b`](https://github.com/nodejs/node/commit/d4f507b23b)] - **src**: remove unused req_wrap-inl.h (Daniel Bevenius) [#20996](https://github.com/nodejs/node/pull/20996)
- [[`44fe78b09a`](https://github.com/nodejs/node/commit/44fe78b09a)] - **stream**: inline needMoreData function (Miklos Suveges) [#21009](https://github.com/nodejs/node/pull/21009)
- [[`d1e81b0f17`](https://github.com/nodejs/node/commit/d1e81b0f17)] - **stream**: ensure Stream.pipeline re-throws errors without callback (Blaine Bublitz) [#20437](https://github.com/nodejs/node/pull/20437)
- [[`8161287b40`](https://github.com/nodejs/node/commit/8161287b40)] - **test**: move benchmark-dgram to sequential (Anatoli Papirovski) [#21144](https://github.com/nodejs/node/pull/21144)
- [[`9d41ab466b`](https://github.com/nodejs/node/commit/9d41ab466b)] - **test**: refactor child-process-fork-net (Rich Trott) [#21095](https://github.com/nodejs/node/pull/21095)
- [[`820236fd0d`](https://github.com/nodejs/node/commit/820236fd0d)] - **test**: mark test-trace-events-fs-sync as flaky (Matheus Marchini) [#21039](https://github.com/nodejs/node/pull/21039)
- [[`2d36150852`](https://github.com/nodejs/node/commit/2d36150852)] - **test**: string-decorater.lastChar (Masashi Hirano) [#21084](https://github.com/nodejs/node/pull/21084)
- [[`1733ef9dec`](https://github.com/nodejs/node/commit/1733ef9dec)] - **test**: make handling of noWarnCode stricter (Tobias Nießen) [#21075](https://github.com/nodejs/node/pull/21075)
- [[`1e607d0910`](https://github.com/nodejs/node/commit/1e607d0910)] - **test**: add source for test.wasm (Daniel Bevenius) [#21082](https://github.com/nodejs/node/pull/21082)
- [[`28f2dcb22a`](https://github.com/nodejs/node/commit/28f2dcb22a)] - **test**: update test-dns error message (Rich Trott) [#21116](https://github.com/nodejs/node/pull/21116)
- [[`c60810a853`](https://github.com/nodejs/node/commit/c60810a853)] - **test**: increase slop limit in memory leak test (Ben Noordhuis) [#21080](https://github.com/nodejs/node/pull/21080)
- [[`fda8654161`](https://github.com/nodejs/node/commit/fda8654161)] - **test**: log before and after RSS in memory leak test (Ben Noordhuis) [#21080](https://github.com/nodejs/node/pull/21080)
- [[`8e3e18ef7d`](https://github.com/nodejs/node/commit/8e3e18ef7d)] - **test**: unmark test-zlib.zlib-binding.deflate flaky (Anatoli Papirovski) [#21109](https://github.com/nodejs/node/pull/21109)
- [[`bd0d19dae7`](https://github.com/nodejs/node/commit/bd0d19dae7)] - **test**: minor adjustments to test-http2-respond-file (Anna Henningsen) [#21098](https://github.com/nodejs/node/pull/21098)
- [[`c4fc1ff295`](https://github.com/nodejs/node/commit/c4fc1ff295)] - **test**: fix flaky async-hooks/test-zlib.zlib-binding.deflate (Anna Henningsen) [#21077](https://github.com/nodejs/node/pull/21077)
- [[`c8ee379d85`](https://github.com/nodejs/node/commit/c8ee379d85)] - **test**: run crypto benchmark only once in tests (Rich Trott) [#21032](https://github.com/nodejs/node/pull/21032)
- [[`a3fdd2e4c5`](https://github.com/nodejs/node/commit/a3fdd2e4c5)] - **test**: add option to test-benchmark-timers (Rich Trott) [#21032](https://github.com/nodejs/node/pull/21032)
- [[`60abd08c7f`](https://github.com/nodejs/node/commit/60abd08c7f)] - **test**: remove unused empty fixture (Rich Trott) [#21044](https://github.com/nodejs/node/pull/21044)
- [[`f7886ab8ad`](https://github.com/nodejs/node/commit/f7886ab8ad)] - **test**: avoid empty fixture in module test (Rich Trott) [#21044](https://github.com/nodejs/node/pull/21044)
- [[`c74c83a4c1`](https://github.com/nodejs/node/commit/c74c83a4c1)] - **test**: avoid empty fixture in fs test (Rich Trott) [#21044](https://github.com/nodejs/node/pull/21044)
- [[`d84aa51dc7`](https://github.com/nodejs/node/commit/d84aa51dc7)] - **test**: removed message from strictEqual (Lucas Liepert) [#20983](https://github.com/nodejs/node/pull/20983)
- [[`e4224fd793`](https://github.com/nodejs/node/commit/e4224fd793)] - **test**: improve path tests (Shivang) [#20967](https://github.com/nodejs/node/pull/20967)
- [[`df97791447`](https://github.com/nodejs/node/commit/df97791447)] - **(SEMVER-MINOR)** **test**: update postmortem metadata test (cjihrig) [#19989](https://github.com/nodejs/node/pull/19989)
- [[`aa08f6464c`](https://github.com/nodejs/node/commit/aa08f6464c)] - **(SEMVER-MINOR)** **test**: add read_only_space heap space (cjihrig) [#19989](https://github.com/nodejs/node/pull/19989)
- [[`ea81d42ddc`](https://github.com/nodejs/node/commit/ea81d42ddc)] - **test**: show actual error in next-tick-when-exiting (Shailesh Shekhawat) [#20956](https://github.com/nodejs/node/pull/20956)
- [[`7e1f61070e`](https://github.com/nodejs/node/commit/7e1f61070e)] - **test**: fix flaky test-domain-timers (Anatoli Papirovski) [#21019](https://github.com/nodejs/node/pull/21019)
- [[`2bbd99c7b2`](https://github.com/nodejs/node/commit/2bbd99c7b2)] - **test**: check TTY mode reset on exit (Anna Henningsen) [#21027](https://github.com/nodejs/node/pull/21027)
- [[`adbbf0d625`](https://github.com/nodejs/node/commit/adbbf0d625)] - **test**: mark test-fs-readfile-tostring-fail as flaky (Matheus Marchini) [#21013](https://github.com/nodejs/node/pull/21013)
- [[`ff5f20fc7b`](https://github.com/nodejs/node/commit/ff5f20fc7b)] - **test**: add test for fs.promises.lchmod (Masashi Hirano) [#20584](https://github.com/nodejs/node/pull/20584)
- [[`04af69750c`](https://github.com/nodejs/node/commit/04af69750c)] - **test**: mark test-child-process-fork-net as flaky (Matheus Marchini) [#21018](https://github.com/nodejs/node/pull/21018)
- [[`edf42985d7`](https://github.com/nodejs/node/commit/edf42985d7)] - **test**: fix worker send error (Gireesh Punathil) [#20973](https://github.com/nodejs/node/pull/20973)
- [[`ba71fe8bd3`](https://github.com/nodejs/node/commit/ba71fe8bd3)] - **timers**: check can_call_into_js in Immediates (Anatoli Papirovski) [#21057](https://github.com/nodejs/node/pull/21057)
- [[`440e899d94`](https://github.com/nodejs/node/commit/440e899d94)] - **tools**: ensure doc-only doesn't update package-lock (Myles Borins) [#21015](https://github.com/nodejs/node/pull/21015)
- [[`b5b7459e5c`](https://github.com/nodejs/node/commit/b5b7459e5c)] - **trace_events**: add version metadata (James M Snell) [#20852](https://github.com/nodejs/node/pull/20852)
- [[`4c48b69e90`](https://github.com/nodejs/node/commit/4c48b69e90)] - **(SEMVER-MINOR)** **util**: add type check function for BigIntObject (Michaël Zasso) [#19989](https://github.com/nodejs/node/pull/19989)
- [[`b2808ed929`](https://github.com/nodejs/node/commit/b2808ed929)] - **util**: fix inspection of module namespaces (Gus Caplan) [#20962](https://github.com/nodejs/node/pull/20962)
- [[`ec058193a8`](https://github.com/nodejs/node/commit/ec058193a8)] - **v8**: backport 9fb02b526f1cd3b859a530a01adb08bc0d089f4f (Gus Caplan) [#20575](https://github.com/nodejs/node/pull/20575)
- [[`48aa4c32d0`](https://github.com/nodejs/node/commit/48aa4c32d0)] - **zlib**: removed extra util require (ErnestoSalazar) [#21069](https://github.com/nodejs/node/pull/21069)

Windows 32-bit Installer: https://nodejs.org/dist/v10.4.0/node-v10.4.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v10.4.0/node-v10.4.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v10.4.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v10.4.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v10.4.0/node-v10.4.0.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v10.4.0/node-v10.4.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v10.4.0/node-v10.4.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v10.4.0/node-v10.4.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v10.4.0/node-v10.4.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v10.4.0/node-v10.4.0-aix-ppc64.tar.gz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v10.4.0/node-v10.4.0-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v10.4.0/node-v10.4.0-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v10.4.0/node-v10.4.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v10.4.0/node-v10.4.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v10.4.0/node-v10.4.0.tar.gz \
Other release files: https://nodejs.org/dist/v10.4.0/ \
Documentation: https://nodejs.org/docs/v10.4.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

cc90b3662a6e479b42f71091fe8ffd155d520cf81c96b3012d9126568e7fc03b  node-v10.4.0-aix-ppc64.tar.gz
82b27983c990a6860e8d729e0b15acf9643ffca0eff282a926268849dfd2c3d2  node-v10.4.0-darwin-x64.tar.gz
98598938814908b865f0cbcbd3e15971543268286d22e2f745ae1d8073ec8476  node-v10.4.0-darwin-x64.tar.xz
71a60eb09fddc7110f7deec02452b42de483ebf04963745250258b30e55375cd  node-v10.4.0-headers.tar.gz
75ca77c1d91061716225a96e06de7bfbbe4c04738baf789d3a3926c01e72525e  node-v10.4.0-headers.tar.xz
e54af0d3046c45fa45ce3f207a8f652969489c17b8328110e626aab19d8ab430  node-v10.4.0-linux-arm64.tar.gz
18d01e0937cdd05386f59f792613aac7b6614a37312ede0c299bd589584976e9  node-v10.4.0-linux-arm64.tar.xz
8741405d66293173d151a1e2dedd14a2d1b9b6e475e5e5892c3758f3eece085c  node-v10.4.0-linux-armv6l.tar.gz
a94d89ace411674213da6cbfe3783cd9d6655c11f9dd7672211af4b5fd4c83cb  node-v10.4.0-linux-armv6l.tar.xz
3f8d77e7e860a20814b9d0152a009ccf042e38958c1bcbb4cdeff25573cfb522  node-v10.4.0-linux-armv7l.tar.gz
2d8ae21db9d368f6157dbc41ee3ba50d37cf5e1ee70ffccba512e711c0e3cdd8  node-v10.4.0-linux-armv7l.tar.xz
0ef455d03eabcd7e1ff6c38a66c803f5bcdc4d4442f3c6c1923fc49036c633db  node-v10.4.0-linux-ppc64le.tar.gz
6c206a97660748601c6312def4f3b804ed2c738ec53575a3dbe36c801c52384b  node-v10.4.0-linux-ppc64le.tar.xz
4fa87ede40b362f388fc3d38a0bbb86f9f32630c52f4c8e25e8c4207e893328c  node-v10.4.0-linux-s390x.tar.gz
5ea47bfac54106a2ed80ad830efa35c241a2c5f0eea8a0768c8bc6651108ab71  node-v10.4.0-linux-s390x.tar.xz
cc237ba4bf23dc351d22972983d934a5775a6380792db000045fcd834de32ac9  node-v10.4.0-linux-x64.tar.gz
ce2232578408f7d6bdc7d8bbb49d3416225fe68c52540ac23f4a6e0294d947f6  node-v10.4.0-linux-x64.tar.xz
ec5c1368b00dc801cbd81086b1f6f5c0c56c81531328d61438e0abc07bcae055  node-v10.4.0.pkg
9a3868fce46e79a64f55447397d330963386d5ddf693b2ba55fb2cb290fbf161  node-v10.4.0-sunos-x64.tar.gz
97d92de3aa0133d57701d2149783a9aabfcb7b6b7af57d820d7990e29ada74bb  node-v10.4.0-sunos-x64.tar.xz
88d9c8179bde19e057cd1d8b835b50726e4c94f8418c91472001e212da96d290  node-v10.4.0.tar.gz
b58f5a39253921c668eb7678679df36f9fb5e46c885242d20f13168973603762  node-v10.4.0.tar.xz
bc2b3f8eab380a068810bae58fe28363d32baaf0d5f41ece4fac8091712eb43f  node-v10.4.0-win-x64.7z
315fc4099902a71b634fee15e4e160a0780703c59a66e7e4542045f6f2b91451  node-v10.4.0-win-x64.zip
f70d0f8c48fc67e04aadfd3d0a35d24db182fa6b4d2b22415a0fda831f432d93  node-v10.4.0-win-x86.7z
f7522469d99f864ad63ba85c468939c147b65abdae41d3212d078fb674ece702  node-v10.4.0-win-x86.zip
497c07135b1285f7b30ed49905fd4b0f1c70babeff95c69aac5f9f64ff52f9f5  node-v10.4.0-x64.msi
317750af435a2f6865389e27c1d890adfe11fbc9a316b20c62c3cda2bad7a04d  node-v10.4.0-x86.msi
920e36c591a8d0376636b1149eb9018beac3cb4d17cb5aa95691062ad780531b  win-x64/node.exe
8a716197eb364e6fb82e27ef60cbd7464ee33761292e02701b46b5a191a8a42b  win-x64/node.lib
44d798d32d8dc6513cc7637966799007568174b0ac862bf53f71c2389f3daadd  win-x64/node_pdb.7z
8baa6b37892edb7007e2aa43e12c4a041a52aa3fd449dae8b82b16d28b839ef5  win-x64/node_pdb.zip
028c3d0d7f0b3db1e1efce4de7aa5731ef224991da0747943e45b924e0f8d58a  win-x86/node.exe
03afed5670805f78a70ac9b65edc19b9bf7b7e70132b837a88a2c26979a9f9a1  win-x86/node.lib
04b17630e3264baed0b3f316ab9407bbc1f898f1d2cb5e84e7aa4f39ad7487d5  win-x86/node_pdb.7z
e07c39e42e8ce99141adf54f548a5cf09b9b7bc33f3a04b86bdfe4cdc5af1360  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQEzBAEBCAAdFiEEDv/hvO/ZyE49CYFSkzsB9AtcqUYFAlsX7s0ACgkQkzsB9Atc
qUb5rggAscVsGKG9wh8KG3/Uox+7u36DnVbTVCl3Kj3PQ52Z6XcOiRRIiijC9+Fe
ld49OqLfOUT1OoTeiDsKDifPWGiMUPew7DUbYCpRlSAy0/G5sz+DqN9Puo5aWfhD
FD/rzpqDjjbbiZTYPDE7K04kAoY+1FkihhMyvd4BGJaITmOch8cawTo4zRuJAjZh
Fmh4kUfNfdOUqP15sD5Yq3skduwpM8hURp/igDRd5z06pNmTnuYPsCrBniQti4vE
Mxe1HPfolB3J7bGosy5YNrbSrlZ+4Tp5l0v76PLcnLuZ+KQ4Ce41hL0PSHgzktKz
qwqr4NRDQGDqxnW/yiiCEe03zAleEQ==
=FJTN
-----END PGP SIGNATURE-----

```
