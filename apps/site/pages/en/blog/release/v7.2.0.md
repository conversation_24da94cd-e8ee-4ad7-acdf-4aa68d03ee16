---
date: '2016-11-22T23:48:43.899Z'
category: release
title: Node v7.2.0 (Current)
layout: blog-post
author: <PERSON>
---

_This is a security release impacting Windows 10 users._

### Notable changes

- **crypto**: The `Decipher` methods `setAuthTag()` and `setAAD` now return `this`. (<PERSON><PERSON>) [#9398](https://github.com/nodejs/node/pull/9398)
- **dns**: Implemented `{ttl: true}` for `resolve4()` and `resolve6()`. (<PERSON>) [#9296](https://github.com/nodejs/node/pull/9296) & [#9296](https://github.com/nodejs/node/pull/9296)
- **libuv**: Upgrade to v1.10.1 (cjihrig) [#9647](https://github.com/nodejs/node/pull/9647)
  - Fixed a potential buffer overflow when writing data to console on Windows 10. (CVE-2016-9551)
- **process**: Added a new `external` property to the data returned by `memoryUsage()`. (<PERSON><PERSON>) [#9587](https://github.com/nodejs/node/pull/9587)
- **tls**: Fixed a memory leak when writes were queued on TLS connection that was destroyed during handshake. (Fedor Indutny) [#9626](https://github.com/nodejs/node/pull/9626)
- **V8 (dep)**: Upgrade to v5.4.500.43 (Michaël Zasso) [#9697](https://github.com/nodejs/node/pull/9697)
- **v8**: The data returned by `getHeapStatistics()` now includes three new fields: `malloced_memory`, `peak_malloced_memory`, and `does_zap_garbage`. (Gareth Ellis) [#8610](https://github.com/nodejs/node/pull/8610)

### Commits

- [[`819a38df96`](https://github.com/nodejs/node/commit/819a38df96)] - **benchmark**: split timers benchmark and refactor (Rich Trott) [#9497](https://github.com/nodejs/node/pull/9497)
- [[`0083bf2233`](https://github.com/nodejs/node/commit/0083bf2233)] - **build**: default to ppc64 on AIX (Gibson Fahnestock) [#9645](https://github.com/nodejs/node/pull/9645)
- [[`3efb43c8ba`](https://github.com/nodejs/node/commit/3efb43c8ba)] - **build**: Add option to compile for coverage reports (Wayne Andrews) [#9463](https://github.com/nodejs/node/pull/9463)
- [[`af74db3961`](https://github.com/nodejs/node/commit/af74db3961)] - **crypto**: use SSL_get_servername. (Adam Langley) [#9347](https://github.com/nodejs/node/pull/9347)
- [[`bcdbf22f0d`](https://github.com/nodejs/node/commit/bcdbf22f0d)] - **crypto**: fix handling of root_cert_store. (Adam Langley) [#9409](https://github.com/nodejs/node/pull/9409)
- [[`3f45cc19b0`](https://github.com/nodejs/node/commit/3f45cc19b0)] - **crypto**: Use reference count to manage cert_store (Adam Majer) [#9409](https://github.com/nodejs/node/pull/9409)
- [[`08a7e7b009`](https://github.com/nodejs/node/commit/08a7e7b009)] - **(SEMVER-MINOR)** **crypto**: return `this` in setAuthTag/setAAD (Kirill Fomichev) [#9398](https://github.com/nodejs/node/pull/9398)
- [[`786631c7b4`](https://github.com/nodejs/node/commit/786631c7b4)] - **deps**: upgrade libuv to 1.10.1 (cjihrig) [#9647](https://github.com/nodejs/node/pull/9647)
- [[`1520afd336`](https://github.com/nodejs/node/commit/1520afd336)] - **deps**: update V8 to 5.4.500.43 (Michaël Zasso) [#9697](https://github.com/nodejs/node/pull/9697)
- [[`33bcd6fec8`](https://github.com/nodejs/node/commit/33bcd6fec8)] - **deps**: update V8 to 5.4.500.41 (Michaël Zasso) [#9412](https://github.com/nodejs/node/pull/9412)
- [[`0a3e5cc57a`](https://github.com/nodejs/node/commit/0a3e5cc57a)] - **(SEMVER-MINOR)** **dns**: implement {ttl: true} for dns.resolve6() (Ben Noordhuis) [#9296](https://github.com/nodejs/node/pull/9296)
- [[`1bd79368cd`](https://github.com/nodejs/node/commit/1bd79368cd)] - **(SEMVER-MINOR)** **dns**: implement {ttl: true} for dns.resolve4() (Ben Noordhuis) [#9296](https://github.com/nodejs/node/pull/9296)
- [[`fa98eec410`](https://github.com/nodejs/node/commit/fa98eec410)] - **doc**: fix typo in assert code example (Vse Mozhet Byt) [#9704](https://github.com/nodejs/node/pull/9704)
- [[`409851427a`](https://github.com/nodejs/node/commit/409851427a)] - **doc**: fix typo in doc/tls.md (Syuhei Kobayashi) [#9566](https://github.com/nodejs/node/pull/9566)
- [[`ebc9c4ba97`](https://github.com/nodejs/node/commit/ebc9c4ba97)] - **doc**: add missing link in changelog (Evan Lucas) [#9540](https://github.com/nodejs/node/pull/9540)
- [[`bbd5853236`](https://github.com/nodejs/node/commit/bbd5853236)] - **doc**: v6 is now LTS rather than Current (Jeremiah Senkpiel) [#9182](https://github.com/nodejs/node/pull/9182)
- [[`8030994554`](https://github.com/nodejs/node/commit/8030994554)] - **doc**: fix some table problems in changelog.md (Jeremiah Senkpiel) [#9183](https://github.com/nodejs/node/pull/9183)
- [[`b070df8932`](https://github.com/nodejs/node/commit/b070df8932)] - **doc**: fix typo in BUILDING.md (monkick) [#9569](https://github.com/nodejs/node/pull/9569)
- [[`39f04829d6`](https://github.com/nodejs/node/commit/39f04829d6)] - **doc**: remove backtick escaping for manpage refs (Anna Henningsen) [#9632](https://github.com/nodejs/node/pull/9632)
- [[`159799aa1d`](https://github.com/nodejs/node/commit/159799aa1d)] - **doc**: improve description of urlObject.query (Rahat Ahmed) [#9625](https://github.com/nodejs/node/pull/9625)
- [[`d62376c8d6`](https://github.com/nodejs/node/commit/d62376c8d6)] - **doc**: small improvements in readline code examples (Vse Mozhet Byt) [#9628](https://github.com/nodejs/node/pull/9628)
- [[`69ffe0cf8c`](https://github.com/nodejs/node/commit/69ffe0cf8c)] - **doc**: child_process .stdio accepts a String type (Kenneth Skovhus) [#9637](https://github.com/nodejs/node/pull/9637)
- [[`c99fb1e0d2`](https://github.com/nodejs/node/commit/c99fb1e0d2)] - **doc**: remove invalid padding from privateEncrypt (JungMinu) [#9611](https://github.com/nodejs/node/pull/9611)
- [[`b258a70a40`](https://github.com/nodejs/node/commit/b258a70a40)] - **doc**: add return types and props types to OS module (imatvieiev) [#9648](https://github.com/nodejs/node/pull/9648)
- [[`425a8646e2`](https://github.com/nodejs/node/commit/425a8646e2)] - **doc**: add italoacasas to collaborators (Italo A. Casas) [#9677](https://github.com/nodejs/node/pull/9677)
- [[`8bf42b4ec4`](https://github.com/nodejs/node/commit/8bf42b4ec4)] - **doc**: strip trailing whitespace (Sam Roberts) [#9620](https://github.com/nodejs/node/pull/9620)
- [[`16819d29b0`](https://github.com/nodejs/node/commit/16819d29b0)] - **doc**: fix "either as either" typo (Sam Roberts) [#9665](https://github.com/nodejs/node/pull/9665)
- [[`c18ca1593e`](https://github.com/nodejs/node/commit/c18ca1593e)] - **doc**: fix tls "the the" typo (Sam Roberts) [#9665](https://github.com/nodejs/node/pull/9665)
- [[`f43e47aab2`](https://github.com/nodejs/node/commit/f43e47aab2)] - **doc**: describe when a tls server emits 'close' (Sam Roberts) [#9665](https://github.com/nodejs/node/pull/9665)
- [[`a086566be6`](https://github.com/nodejs/node/commit/a086566be6)] - **doc**: fix an SNI mistyped as SNS (Sam Roberts) [#9665](https://github.com/nodejs/node/pull/9665)
- [[`4ddc23828d`](https://github.com/nodejs/node/commit/4ddc23828d)] - **doc**: move TSC and CTC meeting minutes out of core repo (James M Snell) [#9503](https://github.com/nodejs/node/pull/9503)
- [[`474d4aa2e3`](https://github.com/nodejs/node/commit/474d4aa2e3)] - **doc**: fix typo in doc/repl.md line: 6 (Mitsuo Utano) [#9582](https://github.com/nodejs/node/pull/9582)
- [[`7af680e6fe`](https://github.com/nodejs/node/commit/7af680e6fe)] - **doc**: make comment indentation consistent (Daniel Bevenius) [#9518](https://github.com/nodejs/node/pull/9518)
- [[`d964eacd6a`](https://github.com/nodejs/node/commit/d964eacd6a)] - **doc**: remove redundant warning information (Brian White) [#9590](https://github.com/nodejs/node/pull/9590)
- [[`25a6f88d98`](https://github.com/nodejs/node/commit/25a6f88d98)] - **doc**: improve process.emitWarning() example (Brian White) [#9590](https://github.com/nodejs/node/pull/9590)
- [[`d5fa1d5307`](https://github.com/nodejs/node/commit/d5fa1d5307)] - **doc**: clarify eventType in fs.watch (Nikolai Vavilov) [#9318](https://github.com/nodejs/node/pull/9318)
- [[`3014dfd254`](https://github.com/nodejs/node/commit/3014dfd254)] - **doc**: wrap long lines in http.request (Timothy Gu) [#9584](https://github.com/nodejs/node/pull/9584)
- [[`89216a45b7`](https://github.com/nodejs/node/commit/89216a45b7)] - **doc**: fix type of http.request's `agent` option (Timothy Gu) [#9584](https://github.com/nodejs/node/pull/9584)
- [[`bff4e88f0b`](https://github.com/nodejs/node/commit/bff4e88f0b)] - **doc**: fix a typo in the assert.md (Vse Mozhet Byt) [#9598](https://github.com/nodejs/node/pull/9598)
- [[`d83cb48b3a`](https://github.com/nodejs/node/commit/d83cb48b3a)] - **doc**: fix typo e.g., => e.g. (Daijiro Yamada) [#9563](https://github.com/nodejs/node/pull/9563)
- [[`d532a57a4b`](https://github.com/nodejs/node/commit/d532a57a4b)] - **doc**: consistent 'Returns:' (Roman Reiss) [#9554](https://github.com/nodejs/node/pull/9554)
- [[`92bd19e0bd`](https://github.com/nodejs/node/commit/92bd19e0bd)] - **doc**: simplify process.memoryUsage() example code (Thomas Watson Steen) [#9560](https://github.com/nodejs/node/pull/9560)
- [[`4ae4e00ae9`](https://github.com/nodejs/node/commit/4ae4e00ae9)] - **doc**: fix typo about cluster doc, (eg. -> e.g.) (YutamaKotaro) [#9568](https://github.com/nodejs/node/pull/9568)
- [[`64dec14502`](https://github.com/nodejs/node/commit/64dec14502)] - **doc**: fix e.g., to e.g. in doc/http.md (ikasumi_wt) [#9564](https://github.com/nodejs/node/pull/9564)
- [[`7c9e8cbd76`](https://github.com/nodejs/node/commit/7c9e8cbd76)] - **doc**: fix the index order in pseudocode of modules (kohta ito) [#9562](https://github.com/nodejs/node/pull/9562)
- [[`d09a9f4d27`](https://github.com/nodejs/node/commit/d09a9f4d27)] - **doc**: remove Roadmap Working Group (William Kapke) [#9545](https://github.com/nodejs/node/pull/9545)
- [[`77aded3ba1`](https://github.com/nodejs/node/commit/77aded3ba1)] - **doc**: add process api data types to documentation (imatvieiev) [#9505](https://github.com/nodejs/node/pull/9505)
- [[`7488b0041f`](https://github.com/nodejs/node/commit/7488b0041f)] - **doc**: added types to path docs (imatvieiev) [#9514](https://github.com/nodejs/node/pull/9514)
- [[`549b6f23db`](https://github.com/nodejs/node/commit/549b6f23db)] - **doc**: fix fs constants link (Timothy) [#9508](https://github.com/nodejs/node/pull/9508)
- [[`31a34d7992`](https://github.com/nodejs/node/commit/31a34d7992)] - **doc**: fix minor style issue in code examples (Daniel Bevenius) [#9482](https://github.com/nodejs/node/pull/9482)
- [[`a412b9fa9a`](https://github.com/nodejs/node/commit/a412b9fa9a)] - **doc**: grammar and structure revisions of wg doc (Ryan Lewis) [#9495](https://github.com/nodejs/node/pull/9495)
- [[`92f163e465`](https://github.com/nodejs/node/commit/92f163e465)] - **doc**: clarify the exit code part of writing_tests (Jeremiah Senkpiel) [#9502](https://github.com/nodejs/node/pull/9502)
- [[`62478eb3d9`](https://github.com/nodejs/node/commit/62478eb3d9)] - **doc**: fix link to Event Loop page (timathon) [#9527](https://github.com/nodejs/node/pull/9527)
- [[`c07f648662`](https://github.com/nodejs/node/commit/c07f648662)] - **doc**: Fix inaccuracy in https.request docs (Andreas Lind) [#9453](https://github.com/nodejs/node/pull/9453)
- [[`6f513e0b46`](https://github.com/nodejs/node/commit/6f513e0b46)] - **doc**: add npm link to README (Oscar Morrison) [#7894](https://github.com/nodejs/node/pull/7894)
- [[`f0d40e8be3`](https://github.com/nodejs/node/commit/f0d40e8be3)] - **doc**: fix link to cli.md in vm.md (Daniel Bevenius) [#9481](https://github.com/nodejs/node/pull/9481)
- [[`8a9c45a4a9`](https://github.com/nodejs/node/commit/8a9c45a4a9)] - **fs**: Fix default params for fs.write(Sync) (Andreas Lind) [#7856](https://github.com/nodejs/node/pull/7856)
- [[`9a0bcfc452`](https://github.com/nodejs/node/commit/9a0bcfc452)] - **fs**: export `realpathCacheKey` from `internal/fs` (Anna Henningsen) [#8862](https://github.com/nodejs/node/pull/8862)
- [[`6b01bfa9d6`](https://github.com/nodejs/node/commit/6b01bfa9d6)] - **gitignore**: ignore all tap files (Johan Bergström) [#9262](https://github.com/nodejs/node/pull/9262)
- [[`23584e4ec5`](https://github.com/nodejs/node/commit/23584e4ec5)] - **gtest**: output tap comments as yamlish (Johan Bergström) [#9262](https://github.com/nodejs/node/pull/9262)
- [[`f5442ece33`](https://github.com/nodejs/node/commit/f5442ece33)] - **lib,test**: remove unneeded escaping of / (Rich Trott) [#9485](https://github.com/nodejs/node/pull/9485)
- [[`34c8b0b411`](https://github.com/nodejs/node/commit/34c8b0b411)] - **module**: check -e flag in debug break setup (Kelvin Jin) [#8876](https://github.com/nodejs/node/pull/8876)
- [[`163397a206`](https://github.com/nodejs/node/commit/163397a206)] - **(SEMVER-MINOR)** **process**: add `process.memoryUsage.external` (Fedor Indutny) [#9587](https://github.com/nodejs/node/pull/9587)
- [[`15af912ab5`](https://github.com/nodejs/node/commit/15af912ab5)] - **src**: fix memory leak introduced in 34febfbf4 (Ben Noordhuis) [#9604](https://github.com/nodejs/node/pull/9604)
- [[`30475beef6`](https://github.com/nodejs/node/commit/30475beef6)] - **src**: use ABORT() macro instead of abort() (Evan Lucas) [#9613](https://github.com/nodejs/node/pull/9613)
- [[`c4f33b48f7`](https://github.com/nodejs/node/commit/c4f33b48f7)] - **(SEMVER-MINOR)** **src**: extend `HeapStatistics` with new fields (Gareth Ellis) [#8610](https://github.com/nodejs/node/pull/8610)
- [[`4517276c74`](https://github.com/nodejs/node/commit/4517276c74)] - **src**: fix method name, output format (Josh Gavant) [#9627](https://github.com/nodejs/node/pull/9627)
- [[`7420ce8b7e`](https://github.com/nodejs/node/commit/7420ce8b7e)] - **src**: squelch unused function warnings in util.h (solebox) [#9115](https://github.com/nodejs/node/pull/9115)
- [[`a83a286631`](https://github.com/nodejs/node/commit/a83a286631)] - **test**: add test for broken child process stdio (cjihrig) [#9528](https://github.com/nodejs/node/pull/9528)
- [[`7c1a2f56fc`](https://github.com/nodejs/node/commit/7c1a2f56fc)] - **test**: add new.target add-on regression test (Ben Noordhuis) [#9689](https://github.com/nodejs/node/pull/9689)
- [[`a220170861`](https://github.com/nodejs/node/commit/a220170861)] - **test**: refactor test-async-wrap-\* (Rich Trott) [#9663](https://github.com/nodejs/node/pull/9663)
- [[`6c63ab7c9a`](https://github.com/nodejs/node/commit/6c63ab7c9a)] - **test**: simplify test-http-client-unescaped-path (Rod Vagg) [#9649](https://github.com/nodejs/node/pull/9649)
- [[`731a1fa602`](https://github.com/nodejs/node/commit/731a1fa602)] - **test**: Use strictEqual in test-tls-writewrap-leak (Aaron Petcoff) [#9666](https://github.com/nodejs/node/pull/9666)
- [[`a29be5282e`](https://github.com/nodejs/node/commit/a29be5282e)] - **test**: fix memory leaks in malloc cctests (Ben Noordhuis) [#9667](https://github.com/nodejs/node/pull/9667)
- [[`776d291a07`](https://github.com/nodejs/node/commit/776d291a07)] - **test**: run tests even if os.cpus() fails (Bethany Griggs) [#9616](https://github.com/nodejs/node/pull/9616)
- [[`51e24e770a`](https://github.com/nodejs/node/commit/51e24e770a)] - **test**: use setImmediate() in test of stream2 (masashi.g) [#9583](https://github.com/nodejs/node/pull/9583)
- [[`875d1b93fc`](https://github.com/nodejs/node/commit/875d1b93fc)] - **test**: add test case of PassThrough (Yoshiya Hinosawa) [#9581](https://github.com/nodejs/node/pull/9581)
- [[`3b4ec5f6c5`](https://github.com/nodejs/node/commit/3b4ec5f6c5)] - **test**: check that `process.execPath` is a realpath (Anna Henningsen) [#9229](https://github.com/nodejs/node/pull/9229)
- [[`ccc6e75bea`](https://github.com/nodejs/node/commit/ccc6e75bea)] - **test**: ensure nextTick is not scheduled in exit (Jeremiah Senkpiel) [#9555](https://github.com/nodejs/node/pull/9555)
- [[`00a5490ecd`](https://github.com/nodejs/node/commit/00a5490ecd)] - **test**: increase coverage of process.emitWarning (Jeremiah Senkpiel) [#9556](https://github.com/nodejs/node/pull/9556)
- [[`f3db5e4720`](https://github.com/nodejs/node/commit/f3db5e4720)] - **test**: refactor test-zlib.js (Rich Trott) [#9544](https://github.com/nodejs/node/pull/9544)
- [[`58fc7a137c`](https://github.com/nodejs/node/commit/58fc7a137c)] - **test**: change from setTimeout to setImmediate (MURAKAMI Masahiko) [#9578](https://github.com/nodejs/node/pull/9578)
- [[`e7eb9ccdcf`](https://github.com/nodejs/node/commit/e7eb9ccdcf)] - **test**: improve test-stream2-objects.js (Yoshiya Hinosawa) [#9565](https://github.com/nodejs/node/pull/9565)
- [[`dae3d3e53c`](https://github.com/nodejs/node/commit/dae3d3e53c)] - **test**: refactor test-next-tick-error-spin (Rich Trott) [#9537](https://github.com/nodejs/node/pull/9537)
- [[`8c859d58ab`](https://github.com/nodejs/node/commit/8c859d58ab)] - **test**: refactor test-tls-inception (Rich Trott) [#9536](https://github.com/nodejs/node/pull/9536)
- [[`96471556b5`](https://github.com/nodejs/node/commit/96471556b5)] - **test**: move tick-processor tests to own directory (Rich Trott) [#9506](https://github.com/nodejs/node/pull/9506)
- [[`342d8e05cb`](https://github.com/nodejs/node/commit/342d8e05cb)] - **test**: refactor inspector-helper.js (Rich Trott) [#9499](https://github.com/nodejs/node/pull/9499)
- [[`dab3e451ac`](https://github.com/nodejs/node/commit/dab3e451ac)] - **test**: refactor make-callback-recurse test (Rich Trott) [#9498](https://github.com/nodejs/node/pull/9498)
- [[`2a9625656d`](https://github.com/nodejs/node/commit/2a9625656d)] - **test**: move timer-dependent test to sequential (Rich Trott) [#9487](https://github.com/nodejs/node/pull/9487)
- [[`ee7606940a`](https://github.com/nodejs/node/commit/ee7606940a)] - **test**: fix helper-debugger-repl.js (Rich Trott) [#9486](https://github.com/nodejs/node/pull/9486)
- [[`9491352b86`](https://github.com/nodejs/node/commit/9491352b86)] - **test**: remove watchdog in test-debug-signal-cluster (Rich Trott) [#9476](https://github.com/nodejs/node/pull/9476)
- [[`6a94ffb1cf`](https://github.com/nodejs/node/commit/6a94ffb1cf)] - **test**: output tap13 instead of almost-tap (Johan Bergström) [#9262](https://github.com/nodejs/node/pull/9262)
- [[`02c2bf7d34`](https://github.com/nodejs/node/commit/02c2bf7d34)] - **timers**: use consistent checks for canceled timers (Jeremiah Senkpiel) [#9685](https://github.com/nodejs/node/pull/9685)
- [[`ee65b4872d`](https://github.com/nodejs/node/commit/ee65b4872d)] - **tls**: fix leak of WriteWrap+TLSWrap combination (Fedor Indutny) [#9586](https://github.com/nodejs/node/pull/9586)
- [[`19ca6cddcf`](https://github.com/nodejs/node/commit/19ca6cddcf)] - **tools**: disallow trailing whitespace for markdown (Sam Roberts) [#9676](https://github.com/nodejs/node/pull/9676)
- [[`29bf871977`](https://github.com/nodejs/node/commit/29bf871977)] - **tools**: use better regexp for manpage references (Anna Henningsen) [#9632](https://github.com/nodejs/node/pull/9632)
- [[`cc6901d482`](https://github.com/nodejs/node/commit/cc6901d482)] - **tools**: improve docopen target in Makefile (Sakthipriyan Vairamani (thefourtheye)) [#9436](https://github.com/nodejs/node/pull/9436)
- [[`deabb5cfaa`](https://github.com/nodejs/node/commit/deabb5cfaa)] - **tools**: make run-valgrind.py useful (Ben Noordhuis) [#9520](https://github.com/nodejs/node/pull/9520)
- [[`887c76a664`](https://github.com/nodejs/node/commit/887c76a664)] - **tools**: fix run-valgrind.py script (Ben Noordhuis) [#9520](https://github.com/nodejs/node/pull/9520)
- [[`65b60801ce`](https://github.com/nodejs/node/commit/65b60801ce)] - **tools**: copy run-valgrind.py to tools/ (Ben Noordhuis) [#9520](https://github.com/nodejs/node/pull/9520)
- [[`45df0ee717`](https://github.com/nodejs/node/commit/45df0ee717)] - **v8**: update make-v8.sh to use git (Jaideep Bajwa) [#9393](https://github.com/nodejs/node/pull/9393)
- [[`adcc5b15f7`](https://github.com/nodejs/node/commit/adcc5b15f7)] - **zlib**: fix linting recently-introduced lint error (Rich Trott) [#9524](https://github.com/nodejs/node/pull/9524)
- [[`841a2c41d4`](https://github.com/nodejs/node/commit/841a2c41d4)] - **zlib**: name every function Ref: #8913 (solebox) [#9389](https://github.com/nodejs/node/pull/9389)

Windows 32-bit Installer: https://nodejs.org/dist/v7.2.0/node-v7.2.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v7.2.0/node-v7.2.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v7.2.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v7.2.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v7.2.0/node-v7.2.0.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v7.2.0/node-v7.2.0-darwin-x64.tar.gz \
Linux 32-bit Binary: https://nodejs.org/dist/v7.2.0/node-v7.2.0-linux-x86.tar.xz \
Linux 64-bit Binary: https://nodejs.org/dist/v7.2.0/node-v7.2.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v7.2.0/node-v7.2.0-linux-ppc64le.tar.xz \
Linux PPC BE 64-bit Binary: https://nodejs.org/dist/v7.2.0/node-v7.2.0-linux-ppc64.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v7.2.0/node-v7.2.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v7.2.0/node-v7.2.0-aix-ppc64.tar.gz \
SmartOS 32-bit Binary: https://nodejs.org/dist/v7.2.0/node-v7.2.0-sunos-x86.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v7.2.0/node-v7.2.0-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v7.2.0/node-v7.2.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v7.2.0/node-v7.2.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v7.2.0/node-v7.2.0.tar.gz \
Other release files: https://nodejs.org/dist/v7.2.0/ \
Documentation: https://nodejs.org/docs/v7.2.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

27643e5c280b513314c0335a4430437a416d3bc4d0f3ffe783cd1f2cb88ee956  node-v7.2.0-aix-ppc64.tar.gz
9aa14d62042e8fbf1a49a7694bb0b55225dfa606196d3ddb3a391dcd1b920d20  node-v7.2.0-darwin-x64.tar.gz
1615269561d149b58465ccba8471a2126d582851d72af6798045d3a435c5d8a2  node-v7.2.0-darwin-x64.tar.xz
d3d8a7eb253f99b01ab9a92ec0e654e028be1f826af32cf3744caf65912b2571  node-v7.2.0-headers.tar.gz
a77ca492ddbc193025d3f475b5cfbb6360d4f46b5840b0f49c9b8d2214bb47bc  node-v7.2.0-headers.tar.xz
679e6b0ef4b8286eaa698bf1db21a66ec7d930dfa505badd8b38253d13e865b1  node-v7.2.0-linux-arm64.tar.gz
fa671da0bcbdd8db8d7802b006590828d30b5ec58d9fd2a8702881c98f8f3a46  node-v7.2.0-linux-arm64.tar.xz
15b4254fef8fbc73a79e8a214049f135dd8f50dbe408b44650c1d50eec5441c7  node-v7.2.0-linux-armv7l.tar.gz
343d909160742dfa43aca9e2c5e1e309fe232006fbf1d1176d495724f88b715a  node-v7.2.0-linux-armv7l.tar.xz
7f932b473747f0742d2077e4af7330a7762b566cde1fc1ca75ebeaaede37fe0f  node-v7.2.0-linux-ppc64le.tar.gz
50353d1dcbf3c22a56951646b065b8b71bbabc790aed6a0c44079cdab4d52708  node-v7.2.0-linux-ppc64le.tar.xz
cb2ade364ac7a8c4cb3118539b883f089b79597ddfcb68c45c599ac59d91f07c  node-v7.2.0-linux-ppc64.tar.gz
f974dbf3477c95b986e0c4ca933d0ed7ff35ebe599342e68bce76fe2cc1dd2a4  node-v7.2.0-linux-ppc64.tar.xz
b84572674fe38c35444cabe13961cf190ebc91697a9053c4b9adc633b7c3ddd1  node-v7.2.0-linux-s390x.tar.gz
66ab86aba60ffd55504afe534c39cba309bea227ca44b3027fcb879459696bc6  node-v7.2.0-linux-s390x.tar.xz
f3b8451924b36e289a7113ad42fa36bb95d0dd471fed3e2e6822caad3ee871e6  node-v7.2.0-linux-x64.tar.gz
12f0f1c0eea7b9e397c93982596d8807889e19d08f88f723c8bc5588d345a06c  node-v7.2.0-linux-x64.tar.xz
d7a014d7828c198134f2f54b30a9781c51c7085a88d69ce1f59a59e6eb5b52d2  node-v7.2.0-linux-x86.tar.gz
db0d6f8486100e953ea926b7d13a69ca9d8bb83552ec4a68f360b9d40dddf3ce  node-v7.2.0-linux-x86.tar.xz
94207bbfbf28543e455a862a870c9f43ee53db56f0f34558fdfc5c302923156b  node-v7.2.0.pkg
07b9e253cd0be91266d40e9c37531c02e0d8306d7517f0acb4c373f79cf9584b  node-v7.2.0-sunos-x86.tar.gz
a9912f65302bdd902f10bee9f03a29fc89bfe8022f31a842e26a3106c97c166d  node-v7.2.0-sunos-x86.tar.xz
c3f53a5d8cea145e25706bb21cdac62f1b3314db35785bcf468558a29cfc352f  node-v7.2.0.tar.gz
486d4db7ef659521ad2fafefca877638da07bef61e2aee090207ff52149294fb  node-v7.2.0.tar.xz
47a6c2957444118cb08fdacb3d9f957e90610e317ca909c725fd7c8803b1ea15  node-v7.2.0-win-x64.7z
887c207972cdc191953dbcfa7b4a3f13667063a9aa82e471c779247c555722d5  node-v7.2.0-win-x64.zip
f9347b7b8e9805c877b023f9fbc063c92f3616af4145ff60ccb400f17a135879  node-v7.2.0-win-x86.7z
c8ea0c32c403b9c678bee21ee8c148c94180845e1bba90ecf39bd0bfab2b4810  node-v7.2.0-win-x86.zip
728047841ee8ef46c0273b57dd1f7ed0fc97e89c0d970c1be5387bcf2be5eaa8  node-v7.2.0-x64.msi
3518b031bed17dfe2334981fd4ed3f0c8b71315a42462e6ccd4e949de3e06bc0  node-v7.2.0-x86.msi
a11c376e2d0d8b3a1c2bd36d354ba52178e77313f0b1ecb3dd2b77ff8794886b  win-x64/node.exe
9c7b9728d2dc164d515402c4a9ba0fdc1844531ca32117d498ff16b11196eac6  win-x64/node.lib
3134d9a6d3d4e720d4e042483bbc694b445813a559680ec384b519102b8f5cb4  win-x64/node_pdb.7z
5cdc18e9e29f2e5c8e23d9b762174db359c4b98266ea0fd7a2eb003573d812a2  win-x64/node_pdb.zip
d4180f31a4da4bd74b95fe9ea572b57e458596bf38c1c8136ed6056831ccdeea  win-x86/node.exe
b3eed1e2ce1ef4d22edf82918c378e1d73e279e8df343f8d2e257b022b62708d  win-x86/node.lib
0321437fb0c20330d8aa2d3f55a8899420db9e77bbd46fffab232c8a5419f4bf  win-x86/node_pdb.7z
be8c62dff94fce5da4f05239cc4d26f639a78b2edad906b84545531b999c88bb  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----
Comment: GPGTools - https://gpgtools.org

iQIcBAEBCAAGBQJYNNfCAAoJEEX17r2BPa6OhFoQAIEM6rCQEBeAYS3odCufTd59
bfCOlb0ilGqQQeXTjOZd8yc4iG3zXOifRHE71lHQXfPmvw/dGe1bLZRu3Hu14wnt
QNwVceiSlcK9Ucn3LebmiYi/Y0RISHd3Audt3bI6Iaze0yx1gcgrwBcK6ghDGT6a
lug02Uuyf2QCHGFW6G2Licx3HFurIBY850a7Ymr2AdZveQNTCxBTEVEvEtOkiR1r
uPTAHb3yWHEWJ/wtWkLoY/JBGIqGd/qWU9lHDEqj4yqaGMMShvWfB3l2HWQfozlL
S1a4dtLDi3zNEuNI87Sbg0XzCdLU29ch28y/kDyYeHj+6EBH63ioF0CdvWRE/zTL
FxeYuMf5e8Ng2M0y3eVNYUSTOnBY9RmJMjAnXzVWqOO4v7XLftp0XK+fWWrT/CWp
AYT6SZU6r5RUujpdfqThRBxtgAPt08HcYTgDDERDuVM9pbPO7ZoCuRmJRJvICNKN
FFLY43igHlr+U/s7EzwFyjXOHA+wdoJ5sMbaOHtHQ7rPRFvJMySB+HBuJ6XqPvEo
zRWmL90gOCyR4lmTxSP5Yudv02nOkAaz1r52qc52jxZxucGUOQgv3hygNwPrb8YM
toMf98UMnv/GAiPwDBIXC6/bpNC1YWyY7q3GGtXTHDX0Nva3aFhmGGG6Enur4S3V
KukYrei4mhVcWN04RUeo
=3rz0
-----END PGP SIGNATURE-----

```
