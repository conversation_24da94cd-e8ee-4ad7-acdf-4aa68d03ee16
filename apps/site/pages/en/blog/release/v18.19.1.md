---
date: '2024-02-14T17:35:50.369Z'
category: release
title: Node v18.19.1 (LTS)
layout: blog-post
author: <PERSON>
---

## 2024-02-14, Version 18.19.1 'Hydrogen' (LTS), @RafaelGSS prepared by @marco-ippolito

### Notable changes

This is a security release.

### Notable changes

- CVE-2024-21892 - Code injection and privilege escalation through Linux capabilities- (High)
- CVE-2024-22019 - http: Reading unprocessed HTTP request with unbounded chunk extension allows DoS attacks- (High)
- CVE-2023-46809 - Node.js is vulnerable to the Marvin Attack (timing variant of the <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> attack against PKCS#1 v1.5 padding) - (Medium)
- CVE-2024-22025 - Denial of Service by resource exhaustion in fetch() brotli decoding - (Medium)
- undici version 5.28.3
- npm version 10.2.4

### Commits

- \[[`69e0a1dba8`](https://github.com/nodejs/node/commit/69e0a1dba8)] - **crypto**: update root certificates to NSS 3.95 (Node.js GitHub Bot) [#50805](https://github.com/nodejs/node/pull/50805)
- \[[`d3d357ab09`](https://github.com/nodejs/node/commit/d3d357ab09)] - **crypto**: disable PKCS#1 padding for privateDecrypt (Michael Dawson) [nodejs-private/node-private#525](https://github.com/nodejs-private/node-private/pull/525)
- \[[`3d27175c42`](https://github.com/nodejs/node/commit/3d27175c42)] - **deps**: fix GHSA-f74f-cvh7-c6q6/CVE-2024-24806 (Santiago Gimeno) [#51614](https://github.com/nodejs/node/pull/51614)
- \[[`331558b8ab`](https://github.com/nodejs/node/commit/331558b8ab)] - **deps**: update archs files for openssl-3.0.13+quic1 (Node.js GitHub Bot) [#51614](https://github.com/nodejs/node/pull/51614)
- \[[`99b77dfb9c`](https://github.com/nodejs/node/commit/99b77dfb9c)] - **deps**: upgrade openssl sources to quictls/openssl-3.0.13+quic1 (Node.js GitHub Bot) [#51614](https://github.com/nodejs/node/pull/51614)
- \[[`6cdc71bff1`](https://github.com/nodejs/node/commit/6cdc71bff1)] - **deps**: upgrade npm to 10.2.4 (npm team) [#50751](https://github.com/nodejs/node/pull/50751)
- \[[`911cb33cda`](https://github.com/nodejs/node/commit/911cb33cda)] - **http**: add maximum chunk extension size (Paolo Insogna) [nodejs-private/node-private#520](https://github.com/nodejs-private/node-private/pull/520)
- \[[`f48b89689d`](https://github.com/nodejs/node/commit/f48b89689d)] - **lib**: update undici to v5.28.3 (Matteo Collina) [nodejs-private/node-private#536](https://github.com/nodejs-private/node-private/pull/536)
- \[[`e6b4c105e0`](https://github.com/nodejs/node/commit/e6b4c105e0)] - **src**: fix HasOnly(capability) in node::credentials (Tobias Nießen) [nodejs-private/node-private#505](https://github.com/nodejs-private/node-private/pull/505)
- \[[`97c49076cd`](https://github.com/nodejs/node/commit/97c49076cd)] - **test**: skip test-child-process-stdio-reuse-readable-stdio on Windows (Joyee Cheung) [#49621](https://github.com/nodejs/node/pull/49621)
- \[[`60affdde8e`](https://github.com/nodejs/node/commit/60affdde8e)] - **tools**: add macOS notarization verification step (Ulises Gascón) [#50833](https://github.com/nodejs/node/pull/50833)
- \[[`ccc676a327`](https://github.com/nodejs/node/commit/ccc676a327)] - **tools**: use macOS keychain to notarize the releases (Ulises Gascón) [#50715](https://github.com/nodejs/node/pull/50715)
- \[[`31f1ceb380`](https://github.com/nodejs/node/commit/31f1ceb380)] - **tools**: remove unused file (Ulises Gascon) [#50622](https://github.com/nodejs/node/pull/50622)
- \[[`bd5f6fb92a`](https://github.com/nodejs/node/commit/bd5f6fb92a)] - **tools**: add macOS notarization stapler (Ulises Gascón) [#50625](https://github.com/nodejs/node/pull/50625)
- \[[`4168c4f71b`](https://github.com/nodejs/node/commit/4168c4f71b)] - **tools**: improve macOS notarization process output readability (Ulises Gascón) [#50389](https://github.com/nodejs/node/pull/50389)
- \[[`4622f775aa`](https://github.com/nodejs/node/commit/4622f775aa)] - **tools**: remove unused `version` function (Ulises Gascón) [#50390](https://github.com/nodejs/node/pull/50390)
- \[[`b90804b1e7`](https://github.com/nodejs/node/commit/b90804b1e7)] - **win,tools**: upgrade Windows signing to smctl (Stefan Stojanovic) [#50956](https://github.com/nodejs/node/pull/50956)
- \[[`f31d47e135`](https://github.com/nodejs/node/commit/f31d47e135)] - **zlib**: pause stream if outgoing buffer is full (Matteo Collina) [nodejs-private/node-private#542](https://github.com/nodejs-private/node-private/pull/542)

Windows 32-bit Installer: https://nodejs.org/dist/v18.19.1/node-v18.19.1-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v18.19.1/node-v18.19.1-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v18.19.1/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v18.19.1/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v18.19.1/node-v18.19.1.pkg \
macOS Apple Silicon 64-bit Binary: https://nodejs.org/dist/v18.19.1/node-v18.19.1-darwin-arm64.tar.gz \
macOS Intel 64-bit Binary: https://nodejs.org/dist/v18.19.1/node-v18.19.1-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v18.19.1/node-v18.19.1-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v18.19.1/node-v18.19.1-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v18.19.1/node-v18.19.1-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v18.19.1/node-v18.19.1-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v18.19.1/node-v18.19.1-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v18.19.1/node-v18.19.1-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v18.19.1/node-v18.19.1.tar.gz \
Other release files: https://nodejs.org/dist/v18.19.1/ \
Documentation: https://nodejs.org/docs/v18.19.1/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

747a958008620f96a030c44a7fd6be96f4b6cce7c250e25b57e71d856df1f466  node-v18.19.1-aix-ppc64.tar.gz
0c7249318868877032ed21cc0ed450015ee44b31b9b281955521cd3fc39fbfa3  node-v18.19.1-darwin-arm64.tar.gz
11a9782062a4ba438d24fc290a45611b267a0886e57009fe1c80c3979b6896f0  node-v18.19.1-darwin-arm64.tar.xz
ab67c52c0d215d6890197c951e1bd479b6140ab630212b96867395e21d813016  node-v18.19.1-darwin-x64.tar.gz
5bd94f20a24d5ec5292a020e80dbc41877d97b5ccd9ac632dd11e725bcf4cbf0  node-v18.19.1-darwin-x64.tar.xz
26b9d26623ee9c96bf49f12af2ddf80ad51f74ee219a1efc4cebc297ef7cde9b  node-v18.19.1-headers.tar.gz
a184cabc7462773a484c0a24962c4bfa4a3e752f61049e17b74665ade391a4f7  node-v18.19.1-headers.tar.xz
2913e8544d95c8be9e6034c539ec0584014532166a088bf742629756c3ec42e2  node-v18.19.1-linux-arm64.tar.gz
228ad1eee660fba3f9fd2cccf02f05b8ebccc294d27f22c155d20b233a9d76b3  node-v18.19.1-linux-arm64.tar.xz
bbe61134fb41d96a335ad36a6dbfd0a05cfcb14b31046263de72a7e487b348d5  node-v18.19.1-linux-armv7l.tar.gz
7cebb39fc38db5f0b472a4ab6664b502b35901e92a452c72ea230416219f4f29  node-v18.19.1-linux-armv7l.tar.xz
1dee4b0c95ee00ab81b01db908eff22c51fb8da91cab6e71e3f48fd1b3fd9d16  node-v18.19.1-linux-ppc64le.tar.gz
2e5812b8fc00548e2e8ab9daa88ace13974c16b6ba5595a7a50c35f848f7d432  node-v18.19.1-linux-ppc64le.tar.xz
3d06ceb2be850f3d0e72e7bb6402c058f8dcc809fb3f9458400e93967c433ec5  node-v18.19.1-linux-s390x.tar.gz
15106acf4c9e3aca02416dd89fb5c71af77097042455a73f9caa064c1988ead5  node-v18.19.1-linux-s390x.tar.xz
724802c45237477dbe5777923743e6c77906830cae03a82b5653ebd75b301dda  node-v18.19.1-linux-x64.tar.gz
f35f24edd4415cd609a2ebc03be03ed2cfe211d7333d55c752d831754fb849f0  node-v18.19.1-linux-x64.tar.xz
e8d383fc81ed2ee4c980f829e8687542ffda436eb4258636d2d628e83c9dff1c  node-v18.19.1-win-x64.7z
ff08f8fe253fba9274992d7052e9d9a70141342d7b36ddbd6e84cbe823e312c6  node-v18.19.1-win-x64.zip
e0e949da689ef06b98b97668ec8f41268c89f74b03c564bc929d88486512db8d  node-v18.19.1-win-x86.7z
179b642fede02bcd8009235b3608416117315429b58634fc918ae4d3d07435b6  node-v18.19.1-win-x86.zip
4bd88b56fe9147c778674f24a4a0c8693378454558ee2205036b15cb502bea73  node-v18.19.1-x64.msi
cc39f4d3556dc13139d76dfd22e3ee9350bf3d197b738664b171bf6893eaf1a1  node-v18.19.1-x86.msi
a7bded4becd7db897e1c196facd7877f5b0dfdb044d14f546dc8e14757a393c6  node-v18.19.1.pkg
637aa3f779a5a733657d4dde220747b82ab699075635990a9a611b00d2c20b09  node-v18.19.1.tar.gz
090f96a2ecde080b6b382c6d642bca5d0be4702a78cb555be7bf02b20bd16ded  node-v18.19.1.tar.xz
ae958dbb83f4753931599090875edc2c9d1d7a56f9e864340813fdf949a28b8e  win-x64/node.exe
834663bcd36a8aca80b89e2a711aa20811eb5560b6598c236b5f46724b7f8dfb  win-x64/node.lib
5d44933090c151dab8ddf627d4a78a5bb074c779ba6b1358310bd4e539fece0e  win-x64/node_pdb.7z
fd7b5400d210ec71545cf26a082c630cb679d9f8310d9e3e1b9a2051d34f1061  win-x64/node_pdb.zip
ccde023ef93f691c64cc1ff33b6c57d65bc293feb54ceab9601499618fc8eafa  win-x86/node.exe
2b92b61e4507a95f00bf7788ba156077cbf432857d17ac1da9d994cae75fe987  win-x86/node.lib
179a6ce7da7add02e601b73107c5da00137ed0f68121cb67ecb740ba990649bd  win-x86/node_pdb.7z
5e918a9c770e3d7d149c8e7dea33b32de5067385591c5f323dd43196e7b2401e  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQGzBAEBCAAdFiEEiQwI24V5Fi/uDfnbi+q0389VXvQFAmXM+PMACgkQi+q0389V
XvT/XAv+KG6Cm00Hme0gGvcJy2J2SB8vdChnXVHAu3lCptdvH+/CqyaygTAn9Yub
29kzzot0f16v8gjnxNe04gzLP8yvI15P309hgjJKMfsJ/BeyZtTjbtVqx04u1z72
pMRLcvjDM45VGr67N5A/Kxal9pDdSDgr5+hEQpHkvro89hMaJwMwXJzZqTwgdWTI
k37EaGnrHqmVZyqxjUc97Ithr5oJFWmhH2vlBlazRnsMhv/LU+K5O21RfAou804m
Aw2CmsSoT4qlnk1NCtxXzxZjOVmz/5+86z8qIkjDDCWBUNMxRU77OXNYcHrpUwMi
R1UrUoePk/yUm9gkVDU0vELm4X0MoZOGcDrZvDhxO9IaHffSPjG0RljSsWRHxdce
T60srlDPYzXqSIEWzzU5y5lWnHG7eKy4ryTkswRjwGwuoPAfv/iSo2F0qvtXG7iC
qVCDCzmYOHeGn4nazusbqveh89KUvoxvfAcNp+YgPRc7F6lIWyfAuVlegjfWFlSR
3O/80Xum
=Y4eF
-----END PGP SIGNATURE-----
```
