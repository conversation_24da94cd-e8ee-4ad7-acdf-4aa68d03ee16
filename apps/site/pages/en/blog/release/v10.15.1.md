---
date: '2019-01-29T18:52:59.349Z'
category: release
title: Node v10.15.1 (LTS)
layout: blog-post
author: <PERSON>
---

### Notable Changes

- **doc**:

  - add oyyd to collaborators (<PERSON><PERSON><PERSON>) [#24300](https://github.com/nodejs/node/pull/24300)

- **tls**:
  - throw if protocol too long (<PERSON>) [#23606](https://github.com/nodejs/node/pull/23606)

### Commits

- [[`fbf5321dcf`](https://github.com/nodejs/node/commit/fbf5321dcf)] - **async_hooks**: add HandleScopes to C++ embedder/addon API (<PERSON>) [#24285](https://github.com/nodejs/node/pull/24285)
- [[`0c206e0d6d`](https://github.com/nodejs/node/commit/0c206e0d6d)] - **benchmark**: support more options in startup benchmark (<PERSON><PERSON>) [#24220](https://github.com/nodejs/node/pull/24220)
- [[`9a64ceca39`](https://github.com/nodejs/node/commit/9a64ceca39)] - **buffer**: fix writeUInt16BE range check (Brian White) [#24208](https://github.com/nodejs/node/pull/24208)
- [[`0b81054d17`](https://github.com/nodejs/node/commit/0b81054d17)] - **build**: fix Python detection when depot_tools are in PATH in Windows (Guy Bedford) [#22539](https://github.com/nodejs/node/pull/22539)
- [[`b61a51c4f5`](https://github.com/nodejs/node/commit/b61a51c4f5)] - **build**: remove sudo:false from .travis.yml (Rich Trott) [#24511](https://github.com/nodejs/node/pull/24511)
- [[`5c3736a772`](https://github.com/nodejs/node/commit/5c3736a772)] - **build**: use print() function in configure.py (cclauss) [#24484](https://github.com/nodejs/node/pull/24484)
- [[`5d2dadccff`](https://github.com/nodejs/node/commit/5d2dadccff)] - **build**: check minimum ICU in configure for system-icu (Steven R. Loomis) [#24255](https://github.com/nodejs/node/pull/24255)
- [[`31376d9a97`](https://github.com/nodejs/node/commit/31376d9a97)] - **build**: remove unnecessary prerequisite in Makefile (Rich Trott) [#24342](https://github.com/nodejs/node/pull/24342)
- [[`33fd13c5ce`](https://github.com/nodejs/node/commit/33fd13c5ce)] - **build**: fix benchmark tests on CI (Rich Trott) [#24307](https://github.com/nodejs/node/pull/24307)
- [[`07b7db2f81`](https://github.com/nodejs/node/commit/07b7db2f81)] - **build**: use BUILDTYPE in bench-addons-build targets (Daniel Bevenius) [#24033](https://github.com/nodejs/node/pull/24033)
- [[`4e21eb4004`](https://github.com/nodejs/node/commit/4e21eb4004)] - **build**: lint commit message in separate Travis job (Richard Lau) [#24254](https://github.com/nodejs/node/pull/24254)
- [[`042749fd23`](https://github.com/nodejs/node/commit/042749fd23)] - **build**: only try to find node when it's needed by the target (Joyee Cheung) [#24115](https://github.com/nodejs/node/pull/24115)
- [[`72d2d2cd8e`](https://github.com/nodejs/node/commit/72d2d2cd8e)] - **build**: expose more openssl categories for addons (Jonathan Cardoso Machado) [#23344](https://github.com/nodejs/node/pull/23344)
- [[`dc5647f71b`](https://github.com/nodejs/node/commit/dc5647f71b)] - **build,tools**: update make-v8.sh for ppc64le (Refael Ackermann) [#24293](https://github.com/nodejs/node/pull/24293)
- [[`5dfc1bb46c`](https://github.com/nodejs/node/commit/5dfc1bb46c)] - **build,tools**: update make-v8.sh for s390x (Refael Ackermann) [#23839](https://github.com/nodejs/node/pull/23839)
- [[`04f8d6bffd`](https://github.com/nodejs/node/commit/04f8d6bffd)] - **child_process**: allow 'http_parser' monkey patching again (Jimb Esser) [#24006](https://github.com/nodejs/node/pull/24006)
- [[`3ef68d8d97`](https://github.com/nodejs/node/commit/3ef68d8d97)] - **cli**: add missing env vars to --help (cjihrig) [#24383](https://github.com/nodejs/node/pull/24383)
- [[`4f13ac7941`](https://github.com/nodejs/node/commit/4f13ac7941)] - **console**: improve code readability (gengjiawen) [#24412](https://github.com/nodejs/node/pull/24412)
- [[`07b9a663e0`](https://github.com/nodejs/node/commit/07b9a663e0)] - **console**: cover .assert with single argument (Morgan Roderick) [#24188](https://github.com/nodejs/node/pull/24188)
- [[`4749640b2e`](https://github.com/nodejs/node/commit/4749640b2e)] - **crypto**: reduce memory usage of SignFinal (Tobias Nießen) [#23427](https://github.com/nodejs/node/pull/23427)
- [[`733cb1ef84`](https://github.com/nodejs/node/commit/733cb1ef84)] - **deps**: cherry-pick b87d408 from upstream V8 (Peter Marshall) [#24272](https://github.com/nodejs/node/pull/24272)
- [[`17b55bf1a4`](https://github.com/nodejs/node/commit/17b55bf1a4)] - **deps**: V8: cherry-pick 52a9e67 (Ali Ijaz Sheikh) [#25027](https://github.com/nodejs/node/pull/25027)
- [[`185ccedf7c`](https://github.com/nodejs/node/commit/185ccedf7c)] - **doc**: clarify who may land on an LTS staging branch (Myles Borins) [#24465](https://github.com/nodejs/node/pull/24465)
- [[`3283186934`](https://github.com/nodejs/node/commit/3283186934)] - **doc**: revise `author ready` explanation (Rich Trott) [#24558](https://github.com/nodejs/node/pull/24558)
- [[`f918ad8e98`](https://github.com/nodejs/node/commit/f918ad8e98)] - **doc**: add readable and writable property to Readable and Writable (Dexter Leng) [#23933](https://github.com/nodejs/node/pull/23933)
- [[`d288a395f6`](https://github.com/nodejs/node/commit/d288a395f6)] - **doc**: move trott to tsc emeritus (Rich Trott) [#24492](https://github.com/nodejs/node/pull/24492)
- [[`f0602f8df3`](https://github.com/nodejs/node/commit/f0602f8df3)] - **doc**: add Ruben Bridgewater to release team (Ruben Bridgewater) [#23432](https://github.com/nodejs/node/pull/23432)
- [[`b1bbedd701`](https://github.com/nodejs/node/commit/b1bbedd701)] - **doc**: edit COLLABORATOR_GUIDE.md on closing issues (Rich Trott) [#24477](https://github.com/nodejs/node/pull/24477)
- [[`08284dcdd8`](https://github.com/nodejs/node/commit/08284dcdd8)] - **doc**: move Timothy to TSC emeritus (Timothy Gu) [#24535](https://github.com/nodejs/node/pull/24535)
- [[`6bb860cd20`](https://github.com/nodejs/node/commit/6bb860cd20)] - **doc**: add NODE_DEBUG_NATIVE to API docs (cjihrig) [#24383](https://github.com/nodejs/node/pull/24383)
- [[`ef1056f4bd`](https://github.com/nodejs/node/commit/ef1056f4bd)] - **doc**: add missing env variables to man page (cjihrig) [#24383](https://github.com/nodejs/node/pull/24383)
- [[`40c9ee028b`](https://github.com/nodejs/node/commit/40c9ee028b)] - **doc**: minor cleanup of tls.getProtocol() (Sam Roberts) [#24533](https://github.com/nodejs/node/pull/24533)
- [[`c16b93233c`](https://github.com/nodejs/node/commit/c16b93233c)] - **doc**: add Beth Griggs to release team (Beth Griggs) [#24532](https://github.com/nodejs/node/pull/24532)
- [[`492e2a4d11`](https://github.com/nodejs/node/commit/492e2a4d11)] - **doc**: add filehandle.write(string\[, position\[, encoding\]\]) (Dara Hayes) [#23224](https://github.com/nodejs/node/pull/23224)
- [[`a620c25c76`](https://github.com/nodejs/node/commit/a620c25c76)] - **doc**: udpate list item spacing in changelogs (Rich Trott) [#24391](https://github.com/nodejs/node/pull/24391)
- [[`7fd4ef7df1`](https://github.com/nodejs/node/commit/7fd4ef7df1)] - **doc**: update crypto examples to not use deprecated api (Mayank Asthana) [#24107](https://github.com/nodejs/node/pull/24107)
- [[`2734c20bd9`](https://github.com/nodejs/node/commit/2734c20bd9)] - **doc**: simplify first-time contributors section of Collaborator Guide (Rich Trott) [#24387](https://github.com/nodejs/node/pull/24387)
- [[`e11d46cb84`](https://github.com/nodejs/node/commit/e11d46cb84)] - **doc**: adjusting formatting when printing (Thomas Hunter II) [#24325](https://github.com/nodejs/node/pull/24325)
- [[`c19d6e26a3`](https://github.com/nodejs/node/commit/c19d6e26a3)] - **doc**: better linkage to node-addon-api (Michael Dawson) [#24371](https://github.com/nodejs/node/pull/24371)
- [[`ae3a19486f`](https://github.com/nodejs/node/commit/ae3a19486f)] - **doc**: update collaborator guide with LTS labels (Charalampos Fanoulis) [#24379](https://github.com/nodejs/node/pull/24379)
- [[`e111d71e60`](https://github.com/nodejs/node/commit/e111d71e60)] - **doc**: document http request.finished boolean (Thomas Watson) [#24319](https://github.com/nodejs/node/pull/24319)
- [[`1ca3c9d3e2`](https://github.com/nodejs/node/commit/1ca3c9d3e2)] - **doc**: document NODE_TLS_REJECT_UNAUTHORIZED (cjihrig) [#24289](https://github.com/nodejs/node/pull/24289)
- [[`68aecff860`](https://github.com/nodejs/node/commit/68aecff860)] - **doc**: clarify issues and pull requests guidance (Rich Trott) [#24316](https://github.com/nodejs/node/pull/24316)
- [[`ac3e264f1c`](https://github.com/nodejs/node/commit/ac3e264f1c)] - **doc**: fix comma splices in process.md (Rich Trott) [#24357](https://github.com/nodejs/node/pull/24357)
- [[`672879f406`](https://github.com/nodejs/node/commit/672879f406)] - **doc**: use real protocol names in ALPN example (Sam Roberts) [#24232](https://github.com/nodejs/node/pull/24232)
- [[`8a60798f4c`](https://github.com/nodejs/node/commit/8a60798f4c)] - **doc**: update core-validate-commit url (Daijiro Wachi) [#24331](https://github.com/nodejs/node/pull/24331)
- [[`a9a6cb1b06`](https://github.com/nodejs/node/commit/a9a6cb1b06)] - **doc**: fix echo example programs (Sam Roberts) [#24235](https://github.com/nodejs/node/pull/24235)
- [[`90f3f5e88f`](https://github.com/nodejs/node/commit/90f3f5e88f)] - **doc**: clarify allowed encoding parameter types (Sam Roberts) [#24230](https://github.com/nodejs/node/pull/24230)
- [[`4209e122b7`](https://github.com/nodejs/node/commit/4209e122b7)] - **doc**: correct async_hooks resource names (Gerhard Stoebich) [#24001](https://github.com/nodejs/node/pull/24001)
- [[`d2cc9d72b6`](https://github.com/nodejs/node/commit/d2cc9d72b6)] - **doc**: sort bottom-of-file markdown links (Sam Roberts) [#24679](https://github.com/nodejs/node/pull/24679)
- [[`b4c1d8273c`](https://github.com/nodejs/node/commit/b4c1d8273c)] - **doc**: update fs.open() changes record for optional 'flags' (Rod Vagg) [#24240](https://github.com/nodejs/node/pull/24240)
- [[`cf209171c9`](https://github.com/nodejs/node/commit/cf209171c9)] - **doc**: add links to Stream section (Dmitry Igrishin) [#24301](https://github.com/nodejs/node/pull/24301)
- [[`0260db525a`](https://github.com/nodejs/node/commit/0260db525a)] - **doc**: correct async_hooks sample outputs (Gerhard Stoebich) [#24050](https://github.com/nodejs/node/pull/24050)
- [[`c8d2635ed1`](https://github.com/nodejs/node/commit/c8d2635ed1)] - **doc**: add oyyd to collaborators (Ouyang Yadong) [#24300](https://github.com/nodejs/node/pull/24300)
- [[`b305db8634`](https://github.com/nodejs/node/commit/b305db8634)] - **doc**: edit BUILDING.md (Rich Trott) [#24243](https://github.com/nodejs/node/pull/24243)
- [[`abe3edad48`](https://github.com/nodejs/node/commit/abe3edad48)] - **doc**: fix code examples in stream.md (Grant Carthew) [#24112](https://github.com/nodejs/node/pull/24112)
- [[`31441f42c4`](https://github.com/nodejs/node/commit/31441f42c4)] - **doc**: describe what tls servername is for (Sam Roberts) [#24236](https://github.com/nodejs/node/pull/24236)
- [[`cc688bb23f`](https://github.com/nodejs/node/commit/cc688bb23f)] - **doc**: fix some inconsistent use of hostname (Sam Roberts) [#24199](https://github.com/nodejs/node/pull/24199)
- [[`6f3bc0d28a`](https://github.com/nodejs/node/commit/6f3bc0d28a)] - **doc, test**: document and test vm timeout escapes (James M Snell) [#23743](https://github.com/nodejs/node/pull/23743)
- [[`ef8c1deda6`](https://github.com/nodejs/node/commit/ef8c1deda6)] - **doc,meta**: update PR approving info (Vse Mozhet Byt) [#24561](https://github.com/nodejs/node/pull/24561)
- [[`be56fb7ab9`](https://github.com/nodejs/node/commit/be56fb7ab9)] - **events**: extract listener check as a function (ZYSzys) [#24303](https://github.com/nodejs/node/pull/24303)
- [[`4a16a4da45`](https://github.com/nodejs/node/commit/4a16a4da45)] - **fs**: inline typeof check (dexterleng) [#24390](https://github.com/nodejs/node/pull/24390)
- [[`35d2397ae5`](https://github.com/nodejs/node/commit/35d2397ae5)] - **http**: remove obsolete function escapeHeaderValue (Lauri Piisang) [#24173](https://github.com/nodejs/node/pull/24173)
- [[`8df4a168b3`](https://github.com/nodejs/node/commit/8df4a168b3)] - **http2**: replace unreachable error with assertion (Rich Trott) [#24407](https://github.com/nodejs/node/pull/24407)
- [[`5516fbf1d7`](https://github.com/nodejs/node/commit/5516fbf1d7)] - **http2**: order declarations in http2.js (ZYSzys) [#24411](https://github.com/nodejs/node/pull/24411)
- [[`5e3c6799cb`](https://github.com/nodejs/node/commit/5e3c6799cb)] - **http2**: elevate v8 namespaces of repeated references (Gagandeep Singh) [#24453](https://github.com/nodejs/node/pull/24453)
- [[`4246a40b30`](https://github.com/nodejs/node/commit/4246a40b30)] - **lib**: move encodeStr function to internal for reusable (ZYSzys) [#24242](https://github.com/nodejs/node/pull/24242)
- [[`6bd055f7de`](https://github.com/nodejs/node/commit/6bd055f7de)] - **lib**: refactor setupInspector in bootstrap/node.js (leeight) [#24446](https://github.com/nodejs/node/pull/24446)
- [[`62a5679ca3`](https://github.com/nodejs/node/commit/62a5679ca3)] - **lib**: set stderr.\_destroy to dummyDestroy (Joyee Cheung) [#24398](https://github.com/nodejs/node/pull/24398)
- [[`3450a4c536`](https://github.com/nodejs/node/commit/3450a4c536)] - **lib**: gather all errors constant in the same place for consistency (ZYSzys) [#24038](https://github.com/nodejs/node/pull/24038)
- [[`5c2c5b9094`](https://github.com/nodejs/node/commit/5c2c5b9094)] - **lib**: improved conditional check in zlib (Dan Corman) [#24190](https://github.com/nodejs/node/pull/24190)
- [[`7527632235`](https://github.com/nodejs/node/commit/7527632235)] - **lib**: adjust params from uvExceptionWithHostPort (msmichellegar) [#24159](https://github.com/nodejs/node/pull/24159)
- [[`3966b698f6`](https://github.com/nodejs/node/commit/3966b698f6)] - **lib**: combine contructor, tag, Object into a function (Paul Isache) [#24171](https://github.com/nodejs/node/pull/24171)
- [[`c84b420457`](https://github.com/nodejs/node/commit/c84b420457)] - **_Revert_** "**net**: partially revert "simplify Socket.prototype.\_final"" (Anna Henningsen) [#24290](https://github.com/nodejs/node/pull/24290)
- [[`0c2d1d57e8`](https://github.com/nodejs/node/commit/0c2d1d57e8)] - **net**: add comments explaining error check (Steven Gabarro) [#24222](https://github.com/nodejs/node/pull/24222)
- [[`2d0105c751`](https://github.com/nodejs/node/commit/2d0105c751)] - **net**: remove unreachable check in internalConnect (Philipp Dunkel) [#24158](https://github.com/nodejs/node/pull/24158)
- [[`897114bf94`](https://github.com/nodejs/node/commit/897114bf94)] - **net**: partially revert "simplify Socket.prototype.\_final" (Anna Henningsen) [#24288](https://github.com/nodejs/node/pull/24288)
- [[`10a27277ad`](https://github.com/nodejs/node/commit/10a27277ad)] - **net**: simplify Socket.prototype.\_final (Anna Henningsen) [#24075](https://github.com/nodejs/node/pull/24075)
- [[`b7876ba6e1`](https://github.com/nodejs/node/commit/b7876ba6e1)] - **src**: elevate namespaces for repeated entities (Sarath Govind K K) [#24475](https://github.com/nodejs/node/pull/24475)
- [[`4b82aa80fe`](https://github.com/nodejs/node/commit/4b82aa80fe)] - **src**: elevate namespaces of repeated artifacts (Maya Anilson) [#24429](https://github.com/nodejs/node/pull/24429)
- [[`bfde244576`](https://github.com/nodejs/node/commit/bfde244576)] - **src**: elevate repeated use of v8 namespaced type (Shubham Urkade) [#24427](https://github.com/nodejs/node/pull/24427)
- [[`be14283bcd`](https://github.com/nodejs/node/commit/be14283bcd)] - **src**: use smart pointers in cares_wrap.cc (Daniel Bevenius) [#23813](https://github.com/nodejs/node/pull/23813)
- [[`fa52ba621b`](https://github.com/nodejs/node/commit/fa52ba621b)] - **src**: elevate v8 namespaces of referenced artifacts (Kanika Singhal) [#24424](https://github.com/nodejs/node/pull/24424)
- [[`9a69d030ce`](https://github.com/nodejs/node/commit/9a69d030ce)] - **src**: reuse std::make_unique (alyssaq) [#24132](https://github.com/nodejs/node/pull/24132)
- [[`44a1993e9d`](https://github.com/nodejs/node/commit/44a1993e9d)] - **src**: avoid extra `Persistent` in `DefaultTriggerAsyncIdScope` (Anna Henningsen) [#23844](https://github.com/nodejs/node/pull/23844)
- [[`15d05bbf02`](https://github.com/nodejs/node/commit/15d05bbf02)] - **src**: simplify `TimerFunctionCall()` in `node_perf.cc` (Anna Henningsen) [#23782](https://github.com/nodejs/node/pull/23782)
- [[`383d512ed7`](https://github.com/nodejs/node/commit/383d512ed7)] - **src**: memory management using smart pointer (Uttam Pawar) [#23628](https://github.com/nodejs/node/pull/23628)
- [[`ffb4087def`](https://github.com/nodejs/node/commit/ffb4087def)] - **src**: remove function hasTextDecoder in encoding.js (Chi-chi Wang) [#23625](https://github.com/nodejs/node/pull/23625)
- [[`fa60eb83be`](https://github.com/nodejs/node/commit/fa60eb83be)] - **stream**: correctly pause and resume after once('readable') (Matteo Collina) [#24366](https://github.com/nodejs/node/pull/24366)
- [[`a7c1d0908a`](https://github.com/nodejs/node/commit/a7c1d0908a)] - **stream**: do not use crypto.DEFAULT_ENCODING in lazy_transform.js (Joyee Cheung) [#24396](https://github.com/nodejs/node/pull/24396)
- [[`965098a8ca`](https://github.com/nodejs/node/commit/965098a8ca)] - **stream**: change comment on duplex stream options (Jesse W. Collins) [#24247](https://github.com/nodejs/node/pull/24247)
- [[`6ce4ef3387`](https://github.com/nodejs/node/commit/6ce4ef3387)] - **stream**: make `.destroy()` interact better with write queue (Anna Henningsen) [#24062](https://github.com/nodejs/node/pull/24062)
- [[`bdab2e98f1`](https://github.com/nodejs/node/commit/bdab2e98f1)] - **test**: mark test-cli-node-options flaky on arm (Rich Trott) [#25032](https://github.com/nodejs/node/pull/25032)
- [[`e5c4759eab`](https://github.com/nodejs/node/commit/e5c4759eab)] - **test**: use destructuring on require (Juan José Arboleda) [#24455](https://github.com/nodejs/node/pull/24455)
- [[`cb860870cd`](https://github.com/nodejs/node/commit/cb860870cd)] - **test**: fix test case in test-child-process-fork-dgram.js (gengjiawen) [#24459](https://github.com/nodejs/node/pull/24459)
- [[`9d7cb1f6d7`](https://github.com/nodejs/node/commit/9d7cb1f6d7)] - **test**: replace callback with arrow functions (sreepurnajasti) [#24541](https://github.com/nodejs/node/pull/24541)
- [[`a9795f701d`](https://github.com/nodejs/node/commit/a9795f701d)] - **test**: replace callback with arrow function (potham) [#24531](https://github.com/nodejs/node/pull/24531)
- [[`088b0db932`](https://github.com/nodejs/node/commit/088b0db932)] - **test**: replace anonymous function with arrow (Gagandeep Singh) [#24527](https://github.com/nodejs/node/pull/24527)
- [[`083925def0`](https://github.com/nodejs/node/commit/083925def0)] - **test**: replace anonymous function with arrow (Gagandeep Singh) [#24526](https://github.com/nodejs/node/pull/24526)
- [[`95ba7615d1`](https://github.com/nodejs/node/commit/95ba7615d1)] - **test**: add information to assertion (Rich Trott) [#24566](https://github.com/nodejs/node/pull/24566)
- [[`313eaf958d`](https://github.com/nodejs/node/commit/313eaf958d)] - **test**: replace anonymous function with arrow func (Gagandeep Singh) [#24525](https://github.com/nodejs/node/pull/24525)
- [[`6b904f6799`](https://github.com/nodejs/node/commit/6b904f6799)] - **test**: change anonymous closure function to arrow function (Nethra Ravindran) [#24433](https://github.com/nodejs/node/pull/24433)
- [[`46e63a2a78`](https://github.com/nodejs/node/commit/46e63a2a78)] - **test**: replace closure functions with arrow functions (Gagandeep Singh) [#24522](https://github.com/nodejs/node/pull/24522)
- [[`8e6729bb82`](https://github.com/nodejs/node/commit/8e6729bb82)] - **test**: replace anonymous function with arrow function (Gagandeep Singh) [#24529](https://github.com/nodejs/node/pull/24529)
- [[`54abfda5d3`](https://github.com/nodejs/node/commit/54abfda5d3)] - **test**: favor arrow function in callback (Pranay Kothapalli) [#24542](https://github.com/nodejs/node/pull/24542)
- [[`d82c0de250`](https://github.com/nodejs/node/commit/d82c0de250)] - **test**: remove unused reject handlers (Dan Foley) [#24540](https://github.com/nodejs/node/pull/24540)
- [[`e0a11142b4`](https://github.com/nodejs/node/commit/e0a11142b4)] - **test**: refactor test to use arrow functions (sagirk) [#24479](https://github.com/nodejs/node/pull/24479)
- [[`7dd64858c2`](https://github.com/nodejs/node/commit/7dd64858c2)] - **test**: replace closure with arrow function (Maya Anilson) [#24489](https://github.com/nodejs/node/pull/24489)
- [[`d71a607a09`](https://github.com/nodejs/node/commit/d71a607a09)] - **test**: using arrow functions (NoSkillGirl) [#24436](https://github.com/nodejs/node/pull/24436)
- [[`5b1fd6e246`](https://github.com/nodejs/node/commit/5b1fd6e246)] - **test**: replace anonymous closure with arrow func (suman-mitra) [#24480](https://github.com/nodejs/node/pull/24480)
- [[`b7b6c12510`](https://github.com/nodejs/node/commit/b7b6c12510)] - **test**: replace callback with arrow functions (sreepurnajasti) [#24490](https://github.com/nodejs/node/pull/24490)
- [[`e02d553f7b`](https://github.com/nodejs/node/commit/e02d553f7b)] - **test**: replcae anonymous closure with arrow function (Sarath Govind K K) [#24476](https://github.com/nodejs/node/pull/24476)
- [[`351e69d5c5`](https://github.com/nodejs/node/commit/351e69d5c5)] - **test**: refactor test-http-write-empty-string to use arrow functions (sagirk) [#24483](https://github.com/nodejs/node/pull/24483)
- [[`d245f53db4`](https://github.com/nodejs/node/commit/d245f53db4)] - **test**: replace anonymous closure with arrow functions (suman-mitra) [#24481](https://github.com/nodejs/node/pull/24481)
- [[`8734c679f8`](https://github.com/nodejs/node/commit/8734c679f8)] - **test**: add whatwg-encoding TextDecoder custom inspection with showHidden (ZauberNerd) [#24166](https://github.com/nodejs/node/pull/24166)
- [[`7920e7bfb4`](https://github.com/nodejs/node/commit/7920e7bfb4)] - **test**: replace anonymous closure functions with arrow functions (sagirk) [#24478](https://github.com/nodejs/node/pull/24478)
- [[`283a6b86bb`](https://github.com/nodejs/node/commit/283a6b86bb)] - **test**: replace anonymous closure functions with arrow function (Abhishek Dixit) [#24420](https://github.com/nodejs/node/pull/24420)
- [[`66c3dcab72`](https://github.com/nodejs/node/commit/66c3dcab72)] - **test**: replace anonymous closure with arrow funct (Prabu Subra) [#24439](https://github.com/nodejs/node/pull/24439)
- [[`e7d41c0312`](https://github.com/nodejs/node/commit/e7d41c0312)] - **test**: modify order of parameters for assertion (Mrityunjoy Saha) [#24430](https://github.com/nodejs/node/pull/24430)
- [[`164069cdb0`](https://github.com/nodejs/node/commit/164069cdb0)] - **test**: replace closure with arrow functions (kanishk30) [#24440](https://github.com/nodejs/node/pull/24440)
- [[`f129e2c063`](https://github.com/nodejs/node/commit/f129e2c063)] - **test**: replace anonymous closure function with arrow function (Kunda Sunil Kumar) [#24435](https://github.com/nodejs/node/pull/24435)
- [[`94553b2ea5`](https://github.com/nodejs/node/commit/94553b2ea5)] - **test**: add typeerror test for EC crypto keygen (Matteo) [#24400](https://github.com/nodejs/node/pull/24400)
- [[`1ec6923276`](https://github.com/nodejs/node/commit/1ec6923276)] - **test**: compare objects not identical by reference (Marie Terrier) [#24189](https://github.com/nodejs/node/pull/24189)
- [[`4425926452`](https://github.com/nodejs/node/commit/4425926452)] - **test**: change anonymous closure functions to arrow functions (Namit Bhalla) [#24418](https://github.com/nodejs/node/pull/24418)
- [[`40773c0f2a`](https://github.com/nodejs/node/commit/40773c0f2a)] - **test**: use print() function on both Python 2 and 3 (cclauss) [#24485](https://github.com/nodejs/node/pull/24485)
- [[`2ffbde3963`](https://github.com/nodejs/node/commit/2ffbde3963)] - **test**: favor arrow functions in callbacks (UjjwalUpadhyay) [#24425](https://github.com/nodejs/node/pull/24425)
- [[`8f7326c369`](https://github.com/nodejs/node/commit/8f7326c369)] - **test**: replace anonymous closure functions with arrow function (Amanpreet) [#24417](https://github.com/nodejs/node/pull/24417)
- [[`644a9d6919`](https://github.com/nodejs/node/commit/644a9d6919)] - **test**: fix arguments order in napi test_exception (kanishk30) [#24413](https://github.com/nodejs/node/pull/24413)
- [[`abe9778ea4`](https://github.com/nodejs/node/commit/abe9778ea4)] - **test**: fix the arguments order in `assert.strictEqual` (Jay Arthanareeswaran) [#24416](https://github.com/nodejs/node/pull/24416)
- [[`94d200fe46`](https://github.com/nodejs/node/commit/94d200fe46)] - **test**: replace closure with arrow functions (Amanpreet) [#24438](https://github.com/nodejs/node/pull/24438)
- [[`380da0473a`](https://github.com/nodejs/node/commit/380da0473a)] - **test**: change callback function to arrow function (Jay Arthanareeswaran) [#24419](https://github.com/nodejs/node/pull/24419)
- [[`5ad224d6ae`](https://github.com/nodejs/node/commit/5ad224d6ae)] - **test**: fix the arguments order in `assert.strictEqual` (apoorvanand) [#24431](https://github.com/nodejs/node/pull/24431)
- [[`52259d71d6`](https://github.com/nodejs/node/commit/52259d71d6)] - **test**: assertion equality fix (NoSkillGirl) [#24422](https://github.com/nodejs/node/pull/24422)
- [[`2d25cddbc1`](https://github.com/nodejs/node/commit/2d25cddbc1)] - **test**: remove unused function arguments in async-hooks tests (Simon Bruce) [#24406](https://github.com/nodejs/node/pull/24406)
- [[`c29c510b5a`](https://github.com/nodejs/node/commit/c29c510b5a)] - **test**: fix actual parameter order for 'assert.strictEqual' (Selvaraj) [#24428](https://github.com/nodejs/node/pull/24428)
- [[`bf3bed56db`](https://github.com/nodejs/node/commit/bf3bed56db)] - **test**: swap actual&optional params (Nikhil M) [#24426](https://github.com/nodejs/node/pull/24426)
- [[`d2d6287355`](https://github.com/nodejs/node/commit/d2d6287355)] - **test**: skip test that use --tls-v1.x flags (Daniel Bevenius) [#24376](https://github.com/nodejs/node/pull/24376)
- [[`39a561b3bc`](https://github.com/nodejs/node/commit/39a561b3bc)] - **test**: change callback function to arrow function (Lakshmi Shanmugam) [#24421](https://github.com/nodejs/node/pull/24421)
- [[`f4c2d9efbc`](https://github.com/nodejs/node/commit/f4c2d9efbc)] - **test**: replace anonymous closure for test-http-expect-handling.js (Jayasankar) [#24423](https://github.com/nodejs/node/pull/24423)
- [[`cf7bf27325`](https://github.com/nodejs/node/commit/cf7bf27325)] - **test**: replace callback functions with arrow functions (potham) [#24432](https://github.com/nodejs/node/pull/24432)
- [[`518bc9679d`](https://github.com/nodejs/node/commit/518bc9679d)] - **test**: use arrow functions for callbacks (Pushkal B) [#24444](https://github.com/nodejs/node/pull/24444)
- [[`53973fde9d`](https://github.com/nodejs/node/commit/53973fde9d)] - **test**: replace anonymous closure function (Jayasankar) [#24415](https://github.com/nodejs/node/pull/24415)
- [[`555ef65042`](https://github.com/nodejs/node/commit/555ef65042)] - **test**: fixed the arguments order in `assert.strictEqual` (Lakshmi Shanmugam) [#24414](https://github.com/nodejs/node/pull/24414)
- [[`3b6135ff2c`](https://github.com/nodejs/node/commit/3b6135ff2c)] - **test**: use destructuring and remove unused arguments (Julia) [#24375](https://github.com/nodejs/node/pull/24375)
- [[`8c8199211b`](https://github.com/nodejs/node/commit/8c8199211b)] - **test**: https agent clientcertengine coverage (Osmond van Hemert) [#24248](https://github.com/nodejs/node/pull/24248)
- [[`987df276cb`](https://github.com/nodejs/node/commit/987df276cb)] - **test**: remove unused function arguments in async-hooks tests (Rich Trott) [#24368](https://github.com/nodejs/node/pull/24368)
- [[`585db59b0a`](https://github.com/nodejs/node/commit/585db59b0a)] - **test**: add typeerror for vm/compileFunction params (Dan Corman) [#24179](https://github.com/nodejs/node/pull/24179)
- [[`6cad1b6877`](https://github.com/nodejs/node/commit/6cad1b6877)] - **test**: deep object to table not covered (Osmond van Hemert) [#24257](https://github.com/nodejs/node/pull/24257)
- [[`916ead940d`](https://github.com/nodejs/node/commit/916ead940d)] - **test**: add tests for Socket.setNoDelay (James Herrington) [#24250](https://github.com/nodejs/node/pull/24250)
- [[`fbdfd608dd`](https://github.com/nodejs/node/commit/fbdfd608dd)] - **test**: add process no deprecation (razvanbh) [#24196](https://github.com/nodejs/node/pull/24196)
- [[`b29b23546d`](https://github.com/nodejs/node/commit/b29b23546d)] - **test**: fix arguments order in assertions (Emanuel Kluge) [#24149](https://github.com/nodejs/node/pull/24149)
- [[`d4fd76a782`](https://github.com/nodejs/node/commit/d4fd76a782)] - **test**: remove unused parameters in function definition (Paul Hodgson) [#24268](https://github.com/nodejs/node/pull/24268)
- [[`817d871327`](https://github.com/nodejs/node/commit/817d871327)] - **test**: esm loader unknown builtin module (Fran Herrero) [#24183](https://github.com/nodejs/node/pull/24183)
- [[`8728361533`](https://github.com/nodejs/node/commit/8728361533)] - **test**: fixed order of actual and expected arguments (kiyomizumia) [#24178](https://github.com/nodejs/node/pull/24178)
- [[`e21d784cf8`](https://github.com/nodejs/node/commit/e21d784cf8)] - **test**: add else and error case for TextDecoder (Lauri Piisang) [#24162](https://github.com/nodejs/node/pull/24162)
- [[`aba7b47e5c`](https://github.com/nodejs/node/commit/aba7b47e5c)] - **test**: url format path ending hashchar not covered (Osmond van Hemert) [#24259](https://github.com/nodejs/node/pull/24259)
- [[`9970d562c6`](https://github.com/nodejs/node/commit/9970d562c6)] - **test**: test add and remove for lib/domain (Petar Dodev) [#24163](https://github.com/nodejs/node/pull/24163)
- [[`51643c208e`](https://github.com/nodejs/node/commit/51643c208e)] - **test**: fix args order in process-getactiverequests (Vladyslav Kopylash) [#24186](https://github.com/nodejs/node/pull/24186)
- [[`454ede2d90`](https://github.com/nodejs/node/commit/454ede2d90)] - **test**: check control characters replacing (Alessandro Gatti) [#24182](https://github.com/nodejs/node/pull/24182)
- [[`4d1a80363a`](https://github.com/nodejs/node/commit/4d1a80363a)] - **test**: fix v8 Set/Get compiler warnings (Daniel Bevenius) [#24246](https://github.com/nodejs/node/pull/24246)
- [[`a30f5a02b8`](https://github.com/nodejs/node/commit/a30f5a02b8)] - **test**: fix flaky test-vm-timeout-escape-nexttick (Rich Trott) [#24251](https://github.com/nodejs/node/pull/24251)
- [[`1bceb9d397`](https://github.com/nodejs/node/commit/1bceb9d397)] - **test**: fix test-repl-envvars (Anna Henningsen) [#25226](https://github.com/nodejs/node/pull/25226)
- [[`d04c3c2718`](https://github.com/nodejs/node/commit/d04c3c2718)] - **test**: move benchmark tests out of main test suite (Rich Trott) [#24265](https://github.com/nodejs/node/pull/24265)
- [[`09bb49165f`](https://github.com/nodejs/node/commit/09bb49165f)] - **test**: fix strictEqual argument order (Martin Kask) [#24153](https://github.com/nodejs/node/pull/24153)
- [[`704d886000`](https://github.com/nodejs/node/commit/704d886000)] - **test**: correct order of args in assert.strictEqual() (Natalie Cluer) [#24157](https://github.com/nodejs/node/pull/24157)
- [[`63dc2214f9`](https://github.com/nodejs/node/commit/63dc2214f9)] - **test**: add test case for completion bash flag (Aivo Paas) [#24168](https://github.com/nodejs/node/pull/24168)
- [[`dd67f39ae2`](https://github.com/nodejs/node/commit/dd67f39ae2)] - **test**: add test for deepEqual Float32Array (Yehiyam Livneh) [#24164](https://github.com/nodejs/node/pull/24164)
- [[`2359273868`](https://github.com/nodejs/node/commit/2359273868)] - **test**: fix arguments order in assert.strictEqual() (Ulises Santana Suárez) [#24192](https://github.com/nodejs/node/pull/24192)
- [[`3bb63721e3`](https://github.com/nodejs/node/commit/3bb63721e3)] - **test**: fix assert.strictEqual argument order (John Mc Quillan) [#24172](https://github.com/nodejs/node/pull/24172)
- [[`a6f786dee9`](https://github.com/nodejs/node/commit/a6f786dee9)] - **test**: replacing fixture directory with temp (saurabhSiddhu) [#24077](https://github.com/nodejs/node/pull/24077)
- [[`704b68aee4`](https://github.com/nodejs/node/commit/704b68aee4)] - **test**: increase coverage internal readline (Berry de Witte) [#24150](https://github.com/nodejs/node/pull/24150)
- [[`d8ac55a012`](https://github.com/nodejs/node/commit/d8ac55a012)] - **test**: use NULL instead of 0 in common.h (Daniel Bevenius) [#24104](https://github.com/nodejs/node/pull/24104)
- [[`4b9518dba3`](https://github.com/nodejs/node/commit/4b9518dba3)] - **test**: move test-fs-watch-system-limit from sequential to pummel (Marcus Scott) [#23692](https://github.com/nodejs/node/pull/23692)
- [[`4054c24cac`](https://github.com/nodejs/node/commit/4054c24cac)] - **test**: fix uses of deprecated assert.fail with multiple args (ivan.filenko) [#23673](https://github.com/nodejs/node/pull/23673)
- [[`c417c7a89a`](https://github.com/nodejs/node/commit/c417c7a89a)] - **test**: use assert.strictEqual instead of assert.equal (ivan.filenko) [#23673](https://github.com/nodejs/node/pull/23673)
- [[`1b5b1cc08b`](https://github.com/nodejs/node/commit/1b5b1cc08b)] - **test**: add test for strictDeepEqual (Nikita Malyschkin) [#24197](https://github.com/nodejs/node/pull/24197)
- [[`cd2dedfa4f`](https://github.com/nodejs/node/commit/cd2dedfa4f)] - **test**: add coverage for systemerror set name (Amer Alimanović) [#24200](https://github.com/nodejs/node/pull/24200)
- [[`9fa71468f5`](https://github.com/nodejs/node/commit/9fa71468f5)] - **test**: fix order of arguments in assert.strictEqual (Alex Seifert) [#24145](https://github.com/nodejs/node/pull/24145)
- [[`2d6e942035`](https://github.com/nodejs/node/commit/2d6e942035)] - **test**: add test for 'ERR_INVALID_CALLBACK' (razvanbh) [#24224](https://github.com/nodejs/node/pull/24224)
- [[`540b741ae2`](https://github.com/nodejs/node/commit/540b741ae2)] - **test**: add coverage for escape key switch case (Artur Daschevici) [#24194](https://github.com/nodejs/node/pull/24194)
- [[`53b12c3731`](https://github.com/nodejs/node/commit/53b12c3731)] - **test**: fix NewFromUtf8 compiler warning (Daniel Bevenius) [#24216](https://github.com/nodejs/node/pull/24216)
- [[`8e9ff69d7f`](https://github.com/nodejs/node/commit/8e9ff69d7f)] - **test**: add error code tests in dgram test (Mark Arranz) [#24215](https://github.com/nodejs/node/pull/24215)
- [[`e57a5c3734`](https://github.com/nodejs/node/commit/e57a5c3734)] - **test**: fix order of arguments in test-delayed-require assertion (reineke-fox) [#24165](https://github.com/nodejs/node/pull/24165)
- [[`d7a3a3bd9f`](https://github.com/nodejs/node/commit/d7a3a3bd9f)] - **test**: change arguments order in strictEqual (Paul Isache) [#24156](https://github.com/nodejs/node/pull/24156)
- [[`efd697bc57`](https://github.com/nodejs/node/commit/efd697bc57)] - **test**: switch order of strictEqual arguments (Jonah Polack) [#24185](https://github.com/nodejs/node/pull/24185)
- [[`9052a22dd1`](https://github.com/nodejs/node/commit/9052a22dd1)] - **test**: fix the arguments order in `assert.strictEqual` (mzucker) [#24227](https://github.com/nodejs/node/pull/24227)
- [[`d7722dd9d8`](https://github.com/nodejs/node/commit/d7722dd9d8)] - **test**: fix the arguments order in `assert.strictEqual` (mzucker) [#24226](https://github.com/nodejs/node/pull/24226)
- [[`2e0d3c9de9`](https://github.com/nodejs/node/commit/2e0d3c9de9)] - **test**: fix order in assert.strictEqual to actual, expected (Kevin Seidel) [#24184](https://github.com/nodejs/node/pull/24184)
- [[`b63e9cb3fe`](https://github.com/nodejs/node/commit/b63e9cb3fe)] - **test**: fix arguments order in assert.strictEqual (szabolcsit) [#24143](https://github.com/nodejs/node/pull/24143)
- [[`e0c6f5cbf7`](https://github.com/nodejs/node/commit/e0c6f5cbf7)] - **test**: fix assert argument order (Manish Poddar) [#24160](https://github.com/nodejs/node/pull/24160)
- [[`fc84ccd0f0`](https://github.com/nodejs/node/commit/fc84ccd0f0)] - **test**: removed extraneous argument 's' (Jackson Chui) [#24213](https://github.com/nodejs/node/pull/24213)
- [[`90f98905f1`](https://github.com/nodejs/node/commit/90f98905f1)] - **test**: fix arguments order in test-fs-write-buffer (razvanbh) [#24155](https://github.com/nodejs/node/pull/24155)
- [[`1588fba73f`](https://github.com/nodejs/node/commit/1588fba73f)] - **test**: fix argument order in assert.strictEqual() (Clement) [#24147](https://github.com/nodejs/node/pull/24147)
- [[`f46fa9072a`](https://github.com/nodejs/node/commit/f46fa9072a)] - **test**: switch arguments in strictEqual (Mathieu Pavageau) [#24141](https://github.com/nodejs/node/pull/24141)
- [[`8f2bdaca69`](https://github.com/nodejs/node/commit/8f2bdaca69)] - **test**: fix arguments order (Simona Cotin) [#24151](https://github.com/nodejs/node/pull/24151)
- [[`380789eb68`](https://github.com/nodejs/node/commit/380789eb68)] - **test**: fixe argument order in assert.strictEqual (Marc Posth) [#24140](https://github.com/nodejs/node/pull/24140)
- [[`cd07b02472`](https://github.com/nodejs/node/commit/cd07b02472)] - **test**: fixing arguments order in `assert.strictEqual()` (G. Carcaci) [#24152](https://github.com/nodejs/node/pull/24152)
- [[`6e8fa5361a`](https://github.com/nodejs/node/commit/6e8fa5361a)] - **test**: add tests for OutgoingMessage setTimeout (Robin Drexler) [#24148](https://github.com/nodejs/node/pull/24148)
- [[`abf9bd15db`](https://github.com/nodejs/node/commit/abf9bd15db)] - **test**: swap expected and actual in assert.strictEqual (Florin-Daniel BÎLBÎE) [#24146](https://github.com/nodejs/node/pull/24146)
- [[`f0eee63ee0`](https://github.com/nodejs/node/commit/f0eee63ee0)] - **test**: fix assert parameter order (Roland Broekema) [#24144](https://github.com/nodejs/node/pull/24144)
- [[`78a320130d`](https://github.com/nodejs/node/commit/78a320130d)] - **test**: change order of assert.strictEqual() (Remy Parzinski) [#24142](https://github.com/nodejs/node/pull/24142)
- [[`64fd19f102`](https://github.com/nodejs/node/commit/64fd19f102)] - **test**: fix invalid argument order in test-http-expect-continue.js (Morgan Roderick) [#24138](https://github.com/nodejs/node/pull/24138)
- [[`2d88af354f`](https://github.com/nodejs/node/commit/2d88af354f)] - **test**: strictEqual argument order (actual, expected) (Ahmad Nassri) [#24137](https://github.com/nodejs/node/pull/24137)
- [[`11a84a7b32`](https://github.com/nodejs/node/commit/11a84a7b32)] - **test**: swap the order of arguments (Musa Hamwala) [#24134](https://github.com/nodejs/node/pull/24134)
- [[`e599889649`](https://github.com/nodejs/node/commit/e599889649)] - **test**: fs readfile, swap arguments in strictEqual (Petar Dodev) [#24133](https://github.com/nodejs/node/pull/24133)
- [[`c37b3196b6`](https://github.com/nodejs/node/commit/c37b3196b6)] - **test**: fix arguments order (Fran Herrero) [#24131](https://github.com/nodejs/node/pull/24131)
- [[`74f1dad613`](https://github.com/nodejs/node/commit/74f1dad613)] - **test**: http-client-timeout error assert arguments (Tadhg Creedon) [#24130](https://github.com/nodejs/node/pull/24130)
- [[`b16311b19f`](https://github.com/nodejs/node/commit/b16311b19f)] - **test**: mark test-vm-timeout-\* known issue tests flaky (James M Snell) [#23743](https://github.com/nodejs/node/pull/23743)
- [[`0f6a9524f8`](https://github.com/nodejs/node/commit/0f6a9524f8)] - **tls**: destroy TLS socket if StreamWrap is destroyed (Anna Henningsen) [#24290](https://github.com/nodejs/node/pull/24290)
- [[`0c73221699`](https://github.com/nodejs/node/commit/0c73221699)] - **tls**: do not rely on 'drain' handlers in StreamWrap (Anna Henningsen) [#24290](https://github.com/nodejs/node/pull/24290)
- [[`3170cb49d8`](https://github.com/nodejs/node/commit/3170cb49d8)] - **tls**: throw if protocol too long (Andre Jodat-Danbrani) [#23606](https://github.com/nodejs/node/pull/23606)
- [[`cc4d866697`](https://github.com/nodejs/node/commit/cc4d866697)] - **tools**: update to remark-lint-preset-node@1.2.0 (Rich Trott) [#24391](https://github.com/nodejs/node/pull/24391)
- [[`21843c7659`](https://github.com/nodejs/node/commit/21843c7659)] - **tools**: fix `make lint-md-rollup` and run it (Daijiro Wachi) [#24333](https://github.com/nodejs/node/pull/24333)
- [[`e8e93df148`](https://github.com/nodejs/node/commit/e8e93df148)] - **tools**: update remark-lint to v6.0.3 from v6.0.2 (Daijiro Wachi) [#24333](https://github.com/nodejs/node/pull/24333)
- [[`2bed68f341`](https://github.com/nodejs/node/commit/2bed68f341)] - **tools**: update remark version to v10 from v8 (Daijiro Wachi) [#24333](https://github.com/nodejs/node/pull/24333)
- [[`39ccf1461e`](https://github.com/nodejs/node/commit/39ccf1461e)] - **tools**: update ESLint to 5.9.0 (cjihrig) [#24280](https://github.com/nodejs/node/pull/24280)
- [[`a1d7ed7de6`](https://github.com/nodejs/node/commit/a1d7ed7de6)] - **tracing**: fix static destruction order issue (Anna Henningsen) [#24123](https://github.com/nodejs/node/pull/24123)
- [[`8c107a37f9`](https://github.com/nodejs/node/commit/8c107a37f9)] - **url**: make the context non-enumerable (Joyee Cheung) [#24218](https://github.com/nodejs/node/pull/24218)
- [[`eeb4715a56`](https://github.com/nodejs/node/commit/eeb4715a56)] - **util**: remove unreachable branch (rahulshuklab4u) [#24447](https://github.com/nodejs/node/pull/24447)
- [[`7576a518b8`](https://github.com/nodejs/node/commit/7576a518b8)] - **util**: deleted unreachable code from util.inspect (kiyomizumia) [#24187](https://github.com/nodejs/node/pull/24187)
- [[`c6a43fa2ef`](https://github.com/nodejs/node/commit/c6a43fa2ef)] - **zlib**: do not leak on destroy (Mathias Buus) [#23734](https://github.com/nodejs/node/pull/23734)

Windows 32-bit Installer: https://nodejs.org/dist/v10.15.1/node-v10.15.1-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v10.15.1/node-v10.15.1-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v10.15.1/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v10.15.1/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v10.15.1/node-v10.15.1.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v10.15.1/node-v10.15.1-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v10.15.1/node-v10.15.1-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v10.15.1/node-v10.15.1-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v10.15.1/node-v10.15.1-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v10.15.1/node-v10.15.1-aix-ppc64.tar.gz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v10.15.1/node-v10.15.1-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v10.15.1/node-v10.15.1-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v10.15.1/node-v10.15.1-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v10.15.1/node-v10.15.1-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v10.15.1/node-v10.15.1.tar.gz \
Other release files: https://nodejs.org/dist/v10.15.1/ \
Documentation: https://nodejs.org/docs/v10.15.1/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

5085537ad9d6a6adc24bbfbb5c40711be61222378b37202fa189b69135aa5503  node-v10.15.1-aix-ppc64.tar.gz
327dcef4b61dead1ae04d2743d3390a2b7e6cc6c389c62cfcfeb0486c5a9f181  node-v10.15.1-darwin-x64.tar.gz
0aa52569dd94a8fa4c78e38983f0ee4304a4dc758a9b1ab994a145559830b9fa  node-v10.15.1-darwin-x64.tar.xz
92f761b98d8a728edebbba99d3226c7802e7aeaeaac69f3f09198d20eed6193f  node-v10.15.1-headers.tar.gz
6df299bf5d112d3e07500c8212ff730c975214b0486c09b169b296df2c90f4b4  node-v10.15.1-headers.tar.xz
d1cc9b84befd7b001e61bb40c96b8b9d0776f186ebc4e7993fcc0d5c2631b24c  node-v10.15.1-linux-arm64.tar.gz
0fb6d24972fd991f476a942b1b21aca5059f93b9302318c5883120445ee6cd54  node-v10.15.1-linux-arm64.tar.xz
e359133754437af98fc815945b40c7406ca0372daee4118d2319da3e00586c24  node-v10.15.1-linux-armv6l.tar.gz
a0be84325ac98d7fc1a2e7fa471c968646890af628ea8f06b929edb29d48f327  node-v10.15.1-linux-armv6l.tar.xz
1cef71ded0411a809795a1f001fbafc48f04f9e361069a9764f530f32af7ecf0  node-v10.15.1-linux-armv7l.tar.gz
577d406a76f92966ce9a0a3b6c71ba8295282815554c247c8c2ec14524bd9037  node-v10.15.1-linux-armv7l.tar.xz
3a89a226d9046781baf129e48fd084d16ef3e35640a5adbe59cb2cc487a490f0  node-v10.15.1-linux-ppc64le.tar.gz
bee3c11adf5ad24954fe6d5ea5e78785f67a036a90b02e5fadd9c49438c49837  node-v10.15.1-linux-ppc64le.tar.xz
33cacdd000977709f5f6c324ac6a4672dee21f28b2462a09eb5692d8c51d610c  node-v10.15.1-linux-s390x.tar.gz
e7f76cc08b57eb499fb4b064bbe3821bcfe36cf342520c25f0f1dc9605bf018c  node-v10.15.1-linux-s390x.tar.xz
ca1dfa9790876409c8d9ecab7b4cdb93e3276cedfc64d56ef1a4ff1778a40214  node-v10.15.1-linux-x64.tar.gz
77db68544c7812e925b82ccc41cd4669fdeb191cea8e20053e3f0e86889c4fce  node-v10.15.1-linux-x64.tar.xz
bcfa8147c384a2184d33c190211c8b4037e514e75368308738a0c57095266499  node-v10.15.1.pkg
2c2a3d854a13d42f99bce032ce7d2b814de15bd3ab00b44b961b68fd0f0ad1ff  node-v10.15.1-sunos-x64.tar.gz
4316c0deeb9328d138110591a04271e21d0eb4f8c7a01c4fd2e4584bc4625f4c  node-v10.15.1-sunos-x64.tar.xz
5202f6f6bfda16554c8121ea78e4cffee52e2707e1136c88f3c40b0c2af8100f  node-v10.15.1.tar.gz
1a55f7b9fb80442182d9e1eba4fca4dac3c781cdcb25d6be37b24d253f61c858  node-v10.15.1.tar.xz
3f195287303326725d3bcb4af5db7edec6832f872e10e244443057c523b5fea2  node-v10.15.1-win-x64.7z
bb5bdc9363e4050c94b3f82888141b81630230f86e520abb7dde49081f1292b9  node-v10.15.1-win-x64.zip
53a32f169619e5170a1d4c595fe49e99d36c057bd2ef2ca79b829745101515f4  node-v10.15.1-win-x86.7z
6fd3fa409290e5c74d5da8e206bfa6830be0c07a80cf83a02c7e8c3ffb03cca5  node-v10.15.1-win-x86.zip
fbb258288090715ce82f7301198bd552ff9a9fc03652ffb63269ba232b626d22  node-v10.15.1-x64.msi
415a644dedc7be22f350e5972ca1eefb4f19a739b6c3315731103eadc810c4be  node-v10.15.1-x86.msi
b4783f5b54e9c8dd84937dd6bd07a9f46a01369bf76235cbcce4315ef3103c31  win-x64/node.exe
d57c68010131d35cc64d3c2ca60be9e9613cf220c3204bc151d19f805fbece2f  win-x64/node.lib
e02ec9190908769b9a9ddeac063620b012a0ce7dd063239855f1b6f8795a85dc  win-x64/node_pdb.7z
1eeffb562150db59869544a1700f4b976055b3a9b65790b685575feade2a81df  win-x64/node_pdb.zip
c6427a5f8ab6d0e785b31e360f9076c602271e53da06199ad5cc253e0e0fd401  win-x86/node.exe
9c4c2437ee1ee782a5ede7225702707c7ab473617eb1b93beb3c377b86aefb4e  win-x86/node.lib
d13d29f411d8298e071a2d423ad1450b4dddfc0f64c84eedad781ae159ffb2dd  win-x86/node_pdb.7z
c9b57e611d9b14eadaab0d574edea05d6ab4cfe406805ce58c86314e2af5ea4d  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEueL1mBqm4M0oFg2f8TmTp1WZZTwFAlxQntEACgkQ8TmTp1WZ
ZTz48w//fXCQa5bw0nIcO+EeUDfbovkPa+6Ey4aFVMdpSkrGmzyMElabZikIKx1P
gJhtr7Pd8AHyXRhy57bSKcUGAn+mSINrnqIpES5AjMZuo+daNxH3vuLNvN+X55Ag
c4n/JN38jXUjAIlWITitA4gxmafbGGuEWRD7RIE7s2wfzQuaFe0At5H3VhhfJ1sc
KVrDiy2qLZWEk/6GUNWJniefxUcb95cU78UnQ/tfykZTvB1noW+dEm3Xa1CYafp9
zMetbNFLuHJ/LT0pYNDjXWnzBe8Q3pnuvath3mkYQZ9VCSfJOvyomvkTz5Cj2VLw
LnifwXK5JWWg8DuZkp7TR6zPrIqC6kUuF61oKSu5aLDPVkmXOQmyh3MKzkjgafjG
C2q9CnDGuLI6sec+ge3Wl0E3XpjVr16bqn9MjKyG/wmj5S6pG9Kf+UF085E3Rd/J
w3mUFW34Qqko93r5CYMrvI3jLC/6vP95egdKHVZE7KTVDn3/ld+yPVY0o7TJcVlj
qda4K8xe2FJuKzDRoOaAbBqkwIT8rdVG72YeuzU9laWGOxyLuVqpTgF1K1NIRWFW
FNvRfs86AV78olP/NOcVa0rQqtkgg+oM3+46qramHqzpcsZqirhhHhHL/VZQhiEE
c6zuZoEhl33aeQhryMSU+AT381HeT1Hycwuybu+Jt9t5LXn97GM=
=Xq28
-----END PGP SIGNATURE-----

```
