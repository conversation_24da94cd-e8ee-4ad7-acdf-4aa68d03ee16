---
date: '2022-01-11T00:00:46.599Z'
category: release
title: Node v12.22.9 (LTS)
layout: blog-post
author: <PERSON>
---

### Notable changes

#### Improper handling of URI Subject Alternative Names (Medium)(CVE-2021-44531)

Accepting arbitrary Subject Alternative Name (SAN) types, unless a PKI is specifically defined to use a particular SAN type, can result in bypassing name-constrained intermediates. Node.js was accepting URI SAN types, which PKIs are often not defined to use. Additionally, when a protocol allows URI SANs, Node.js did not match the URI correctly.

Versions of Node.js with the fix for this disable the URI SAN type when checking a certificate against a hostname. This behavior can be reverted through the `--security-revert` command-line option.

More details will be available at [CVE-2021-44531](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2021-44531) after publication.

#### Certificate Verification Bypass via String Injection (Medium)(CVE-2021-44532)

Node.js converts SANs (Subject Alternative Names) to a string format. It uses this string to check peer certificates against hostnames when validating connections. The string format was subject to an injection vulnerability when name constraints were used within a certificate chain, allowing the bypass of these name constraints.

Versions of Node.js with the fix for this escape SANs containing the problematic characters in order to prevent the injection. This behavior can be reverted through the `--security-revert` command-line option.

More details will be available at [CVE-2021-44532](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2021-44532) after publication.

#### Incorrect handling of certificate subject and issuer fields (Medium)(CVE-2021-44533)

Node.js did not handle multi-value Relative Distinguished Names correctly. Attackers could craft certificate subjects containing a single-value Relative Distinguished Name that would be interpreted as a multi-value Relative Distinguished Name, for example, in order to inject a Common Name that would allow bypassing the certificate subject verification.

Affected versions of Node.js do not accept multi-value Relative Distinguished Names and are thus not vulnerable to such attacks themselves. However, third-party code that uses node's ambiguous presentation of certificate subjects may be vulnerable.

More details will be available at [CVE-2021-44533](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2021-44533) after publication.

#### Prototype pollution via `console.table` properties (Low)(CVE-2022-21824)

Due to the formatting logic of the `console.table()` function it was not safe to allow user controlled input to be passed to the `properties` parameter while simultaneously passing a plain object with at least one property as the first parameter, which could be `__proto__`. The prototype pollution has very limited control, in that it only allows an empty string to be assigned numerical keys of the object prototype.

Versions of Node.js with the fix for this use a null protoype for the object these properties are being assigned to.

More details will be available at [CVE-2022-21824](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2022-21824) after publication.

Thanks to Patrik Oldsberg (rugvip) for reporting this vulnerability.

### Commits

- \[[`be69403528`](https://github.com/nodejs/node/commit/be69403528)] - **console**: fix prototype pollution via console.table (Tobias Nießen) [nodejs-private/node-private#307](https://github.com/nodejs-private/node-private/pull/307)
- \[[`19873abfb2`](https://github.com/nodejs/node/commit/19873abfb2)] - **crypto,tls**: implement safe x509 GeneralName format (Tobias Nießen and Akshay Kumar) [nodejs-private/node-private#300](https://github.com/nodejs-private/node-private/pull/300)
- \[[`ff9ac7d757`](https://github.com/nodejs/node/commit/ff9ac7d757)] - **doc**: fix date for v12.22.8 (Richard Lau) [#41213](https://github.com/nodejs/node/pull/41213)
- \[[`a5c7843cab`](https://github.com/nodejs/node/commit/a5c7843cab)] - **src**: add cve reverts and associated tests (Michael Dawson and Akshay Kumar) [nodejs-private/node-private#300](https://github.com/nodejs-private/node-private/pull/300)
- \[[`d4e5d1b9ca`](https://github.com/nodejs/node/commit/d4e5d1b9ca)] - **src**: remove unused x509 functions (Tobias Nießen and Akshay Kumar) [nodejs-private/node-private#300](https://github.com/nodejs-private/node-private/pull/300)
- \[[`8c2db2c86b`](https://github.com/nodejs/node/commit/8c2db2c86b)] - **tls**: fix handling of x509 subject and issuer (Tobias Nießen and Akshay Kumar) [nodejs-private/node-private#300](https://github.com/nodejs-private/node-private/pull/300)
- \[[`e0fe6a635e`](https://github.com/nodejs/node/commit/e0fe6a635e)] - **tls**: drop support for URI alternative names (Tobias Nießen and Akshay Kumar) [nodejs-private/node-private#300](https://github.com/nodejs-private/node-private/pull/300)

Windows 32-bit Installer: https://nodejs.org/dist/v12.22.9/node-v12.22.9-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v12.22.9/node-v12.22.9-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v12.22.9/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v12.22.9/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v12.22.9/node-v12.22.9.pkg \
macOS Intel 64-bit Binary: https://nodejs.org/dist/v12.22.9/node-v12.22.9-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v12.22.9/node-v12.22.9-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v12.22.9/node-v12.22.9-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v12.22.9/node-v12.22.9-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v12.22.9/node-v12.22.9-aix-ppc64.tar.gz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v12.22.9/node-v12.22.9-sunos-x64.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v12.22.9/node-v12.22.9-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v12.22.9/node-v12.22.9-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v12.22.9/node-v12.22.9.tar.gz \
Other release files: https://nodejs.org/dist/v12.22.9/ \
Documentation: https://nodejs.org/docs/v12.22.9/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

bb1de3edd4aef85ed7a9581f8bab579cace8c3a5a4909e6aabc412c9fdb636ee  node-v12.22.9-aix-ppc64.tar.gz
92cc54a86e7a52016c1cd0bbda5d3c857b37795340292d9c547b1c5f4373a2a5  node-v12.22.9-darwin-x64.tar.gz
36b4291b73b56a231a117aa830ac563794696bf1dd6d6348b66114febb06f4b4  node-v12.22.9-darwin-x64.tar.xz
c9c3a449755ecf76bd2ae3e22f0fd1a580189a5d34f08c675263d1f7a98e8b02  node-v12.22.9-headers.tar.gz
9cce6bfb6fd13a385438667d6de7dde8b4f0f7a193a28d3f61132bb5409f3a91  node-v12.22.9-headers.tar.xz
307aa26c68600e2f73d699e58a15c59ea06928e4a348cd5a216278d9f2ee0d6c  node-v12.22.9-linux-arm64.tar.gz
9e20f49f6f7df04fb2b917cd19e851b02e3042f32044e26da37639d3aafe2a98  node-v12.22.9-linux-arm64.tar.xz
43b175e6b06c9be3e4c2343de0bc434dae16991a747dae5a4ddc0ebb7644e433  node-v12.22.9-linux-armv7l.tar.gz
bafbe7e99c33c520751ded4c859945c806d4c406474aa4d9bbb0ddea58de6d82  node-v12.22.9-linux-armv7l.tar.xz
792412232dcff03b02ce24d403e992354f57c3c649e9372fe46d3c0248644e5f  node-v12.22.9-linux-ppc64le.tar.gz
fd380f0d8accd60a773631f37646f576eb5147b86e28d01c98e7ca1960345bcf  node-v12.22.9-linux-ppc64le.tar.xz
f0f7bf3befdf175f480310841ab1791596a0bad9fb3d217c4ffba63d54669c1b  node-v12.22.9-linux-s390x.tar.gz
e25f945fad2feb3c15d3ff6c39ed951868ad53dbf70f422e8c1b81bf96cca2a5  node-v12.22.9-linux-s390x.tar.xz
860c481f0e7963cbe5afa669d9e5deefa773fb67da571823945ac79a3ea76d3c  node-v12.22.9-linux-x64.tar.gz
33fbab8033d7356932fa0103d9aabd0f07e59672847f5ffa22a4108258345e52  node-v12.22.9-linux-x64.tar.xz
88e035e59aade5e36af485ec5f677d0144d3577462e7310b4d9805f6751238f1  node-v12.22.9.pkg
8579ef1097a081dcee906f5ebff51c9153f7790dd14697295c1e829444527eea  node-v12.22.9-sunos-x64.tar.gz
f1aa14191b646d883327837f985775b61d38292e5b87d41e3d26ce0aa04b8a27  node-v12.22.9-sunos-x64.tar.xz
592ecc53ed8a64dd1a2eb574b4c85d30656e4aee3a02ef997fa8ad92bf025d6a  node-v12.22.9.tar.gz
da982c03e584c2b6e50f432cc5e46605d4e3d8451125be25a645fe716873e24a  node-v12.22.9.tar.xz
7484545149cef5d250fab6b352c6804e45b702031672a9f92d698a3684cd5f29  node-v12.22.9-win-x64.7z
829d8adad028dce92307a63f39e4514af72a3f1fa51647b7521f35497b0db4f7  node-v12.22.9-win-x64.zip
eaa594f928227ead9ba78d8ed4671bfb3387c4f4ef7f0ef810273f87c24fcb1d  node-v12.22.9-win-x86.7z
7c63214bfcc139df3e788e3b4ba7c84a072a80e56aae52c567c5555ab2121475  node-v12.22.9-win-x86.zip
a289f3aa81c72a0c0d0f835d7ab6892534e112d1052771582cc0c8b77e1c7267  node-v12.22.9-x64.msi
c0c8d61293de67c867b5180856b18adc2788e69bd74c1585341bafe62cc542c5  node-v12.22.9-x86.msi
6d253935c3dcdb1f4a696f7a697e316d2f5da70e12f69c58022ca62da1f29dcd  win-x64/node.exe
28e5c24831deedbf4fb8a9560f2c4f95205479c589f54a9a53ec346f6a5cf8bf  win-x64/node.lib
3d805b79433a5092edb3fc928cc7767b4f3d10e26c61085641bde17ad207d8ff  win-x64/node_pdb.7z
a69a7058bdb9f6687f4e25bed747163cc6fe323e8507ef9efa574017f2485bfe  win-x64/node_pdb.zip
e7372fc52416b1e434db7d9faac10dd7c57c0415e456568d518e7c2d5fffa701  win-x86/node.exe
dad0e6bef1c45f4f43fbf84c33df6b910ace8122eff3f8d39d5ebecd25320ba4  win-x86/node.lib
2e40e2e170a7355fa0da8ec4e6eca337d0792ab3dad5caeec2e89ecb6d2bd932  win-x86/node_pdb.7z
5e92feff6c530f98f00a0d4f0f21da85223a6c83764aea958c4153491e1e536f  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEyC+jrhy+3Gvka5NgxDzsRcF6uTwFAmHcvfgACgkQxDzsRcF6
uTyqORAApQ99B0oOy9TV+il7ExHo4Vb62A2n4eWRz6F5kO6jWuCJQNcDFEmq64Sj
NZw9blgOfCnGLlkeXPtbjm02rXuv99RO3j3RIpcvW76eG+PdQV9vFRB9FuOqi5tV
RIcSZyStQ2SStqEJpsSlVIsLAFTj8cKVPHMul6RSC0an2ozQEQE/efkAR0b3qkA+
MeT2HI/rceSwAbMZnNUWdeUOE0ltAghNv63EoF1pNSKh4Yn/J9vDDDwqlOv7Ki1S
kqwassFPOyHeRtsQvXAK/eh7UuNzVFMackhhMDXTHOR/YlUil+lWNwspV7wEgKeb
wfE0gEFrOyT75ntKa1o8iX6ntLGuCeZkC/erWC+9BR8D9JxR/yPNsZxyOyUpI8uA
SBe8ECVKxJF1UeiRDIBFqYQ3QUFx6CbWd6PNdHFJl5RUAdmTbAhsVVE5WyEBaBLv
5WBV959le+hAgG94y61EoFSnmki7VmSfhxY2wS161Xk2MckV9BA0kmrET+RXz+nD
gf35v+xr2zDhAZ+Lth0UNEG6GnvOQ2/RFTQciJ5tu6XoKiVSKdVbibAL5BIk/RkC
Osd4GJD5HUjk1FwyKP/VQNjr41ATv95p1BC7q0bvhqamrBugiCAbFHyex+2QEFLu
IUy/ipCLEOJsJVhfGQdIKS3c6v9o0JK6dXKDEHzUzzFMfA+oc4U=
=jrJh
-----END PGP SIGNATURE-----

```
