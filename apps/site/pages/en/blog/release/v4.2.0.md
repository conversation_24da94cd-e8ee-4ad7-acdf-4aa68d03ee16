---
date: '2015-10-12T18:20:40.988Z'
category: release
title: Node v4.2.0 (LTS)
layout: blog-post
author: The Node.js Project
---

We are proud to announce the release of Node.js v4.2.0 "Argon", the first release covered under the new [Long Term Support](https://github.com/nodejs/LTS) plan!

As an LTS release, support for v4.x will continue for a period of 30 months from today. This means that individuals and businesses building on Node.js will have a stable and supported platform on which to grow. For more details on what LTS means for Node.js, see <PERSON>'s [blog post](https://nodesource.com/blog/essential-steps-long-term-support-for-nodejs).

### Argon?

To better differentiate LTS releases from regular Node.js releases, we have introduced a naming convention based on the Periodic Table of Elements that applies only to LTS releases. The name selected for v4.2.0 is "Argon", so as to be in alphabetical order by the first letter.

Developers can easily determine if they are working with an LTS version of Node.js by checking the `process.release.lts` property within node. This property will be undefined in regular releases.

```bash
$ node -v
v4.2.0
$ node -pe process.release.lts
Argon
```

### Notable changes

The first Node.js LTS release! See https://github.com/nodejs/LTS/ for details of the LTS process.

- **icu**: Updated to version 56 with significant performance improvements (Steven R. Loomis) [#3281](https://github.com/nodejs/node/pull/3281)
- **node**:
  - Added new `-c` (or `--check`) command line argument for checking script syntax without executing the code (Dave Eddy) [#2411](https://github.com/nodejs/node/pull/2411)
  - Added `process.versions.icu` to hold the current ICU library version (Evan Lucas) [#3102](https://github.com/nodejs/node/pull/3102)
  - Added `process.release.lts` to hold the current LTS codename when the binary is from an active LTS release line (Rod Vagg) [#3212](https://github.com/nodejs/node/pull/3212)
- **npm**: Upgraded to npm 2.14.7 from 2.14.4, see [release notes](https://github.com/npm/npm/releases/tag/v2.14.7) for full details (Kat Marchán) [#3299](https://github.com/nodejs/node/pull/3299)

### Known issues

See https://github.com/nodejs/node/labels/confirmed-bug for complete and current list of known issues.

- Some problems with unreferenced timers running during `beforeExit` are still to be resolved. See [#1264](https://github.com/nodejs/node/issues/1264).
- Surrogate pair in REPL can freeze terminal. [#690](https://github.com/nodejs/node/issues/690)
- Calling `dns.setServers()` while a DNS query is in progress can cause the process to crash on a failed assertion. [#894](https://github.com/nodejs/node/issues/894)
- `url.resolve` may transfer the auth portion of the url when resolving between two full hosts, see [#1435](https://github.com/nodejs/node/issues/1435).

### Commits

- [[`8383c4fe00`](https://github.com/nodejs/node/commit/8383c4fe00)] - **assert**: support arrow functions in .throws() (Ben Noordhuis) [#3276](https://github.com/nodejs/node/pull/3276)
- [[`3eaa593a32`](https://github.com/nodejs/node/commit/3eaa593a32)] - **async_wrap**: correctly pass parent to init callback (Trevor Norris) [#3216](https://github.com/nodejs/node/pull/3216)
- [[`54795620f6`](https://github.com/nodejs/node/commit/54795620f6)] - **buffer**: don't abort on prototype getters (Trevor Norris) [#3302](https://github.com/nodejs/node/pull/3302)
- [[`660f7591c8`](https://github.com/nodejs/node/commit/660f7591c8)] - **buffer**: FreeCallback should be tied to ArrayBuffer (Fedor Indutny) [#3198](https://github.com/nodejs/node/pull/3198)
- [[`651a5b51eb`](https://github.com/nodejs/node/commit/651a5b51eb)] - **buffer**: only check if instance is Uint8Array (Trevor Norris) [#3080](https://github.com/nodejs/node/pull/3080)
- [[`d5a1b1ad7c`](https://github.com/nodejs/node/commit/d5a1b1ad7c)] - **buffer**: clean up usage of \_\_proto\_\_ (Trevor Norris) [#3080](https://github.com/nodejs/node/pull/3080)
- [[`af24376e18`](https://github.com/nodejs/node/commit/af24376e18)] - **build**: Intl: deps: bump ICU to 56.1 (GA) (Steven R. Loomis) [#3281](https://github.com/nodejs/node/pull/3281)
- [[`9136359d57`](https://github.com/nodejs/node/commit/9136359d57)] - **build**: make icu download path customizable (Johan Bergström) [#3200](https://github.com/nodejs/node/pull/3200)
- [[`b3c5ad10a8`](https://github.com/nodejs/node/commit/b3c5ad10a8)] - **build**: add --with-arm-fpu option (Jérémy Lal) [#3228](https://github.com/nodejs/node/pull/3228)
- [[`f00f3268e4`](https://github.com/nodejs/node/commit/f00f3268e4)] - **build**: intl: avoid 'duplicate main()' on ICU 56 (Steven R. Loomis) [#3066](https://github.com/nodejs/node/pull/3066)
- [[`071c72a6a3`](https://github.com/nodejs/node/commit/071c72a6a3)] - **deps**: upgrade to npm 2.14.7 (Kat Marchán) [#3299](https://github.com/nodejs/node/pull/3299)
- [[`8b50e95f06`](https://github.com/nodejs/node/commit/8b50e95f06)] - **(SEMVER-MINOR)** **deps**: backport 1ee712a from V8 upstream (Julien Gilli) [#3036](https://github.com/nodejs/node/pull/3036)
- [[`747271372f`](https://github.com/nodejs/node/commit/747271372f)] - **doc**: update the assert module summary (David Boivin) [#2799](https://github.com/nodejs/node/pull/2799)
- [[`0d506556b0`](https://github.com/nodejs/node/commit/0d506556b0)] - **doc**: replace node-gyp link with nodejs/node-gyp (Roman Klauke) [#3320](https://github.com/nodejs/node/pull/3320)
- [[`40a159e4f4`](https://github.com/nodejs/node/commit/40a159e4f4)] - **doc**: Amend capitalization of word JavaScript (Dave Hodder) [#3285](https://github.com/nodejs/node/pull/3285)
- [[`6dd34761fd`](https://github.com/nodejs/node/commit/6dd34761fd)] - **doc**: add method links in dns.markdown (Alejandro Oviedo) [#3196](https://github.com/nodejs/node/pull/3196)
- [[`333e8336be`](https://github.com/nodejs/node/commit/333e8336be)] - **doc**: add method links in child_process.markdown (Alejandro Oviedo) [#3186](https://github.com/nodejs/node/pull/3186)
- [[`0cfc6d39ca`](https://github.com/nodejs/node/commit/0cfc6d39ca)] - **doc**: recommend Infinity on emitter.setMaxListeners (Jason Karns) [#2559](https://github.com/nodejs/node/pull/2559)
- [[`d4fc6d93ef`](https://github.com/nodejs/node/commit/d4fc6d93ef)] - **doc**: add help repo link to CONTRIBUTING.md (Doug Shamoo) [#3233](https://github.com/nodejs/node/pull/3233)
- [[`28aac7f19d`](https://github.com/nodejs/node/commit/28aac7f19d)] - **doc**: add TLS session resumption example (Roman Reiss) [#3147](https://github.com/nodejs/node/pull/3147)
- [[`365cf22cce`](https://github.com/nodejs/node/commit/365cf22cce)] - **doc**: update AUTHORS list (Rod Vagg) [#3211](https://github.com/nodejs/node/pull/3211)
- [[`d4399613b7`](https://github.com/nodejs/node/commit/d4399613b7)] - **doc**: standardize references to userland (Martial) [#3192](https://github.com/nodejs/node/pull/3192)
- [[`75de258376`](https://github.com/nodejs/node/commit/75de258376)] - **doc**: fix spelling in Buffer documentation (Rod Machen) [#3226](https://github.com/nodejs/node/pull/3226)
- [[`725c7276dd`](https://github.com/nodejs/node/commit/725c7276dd)] - **doc**: fix README.md link to joyent/node intl wiki (Steven R. Loomis) [#3067](https://github.com/nodejs/node/pull/3067)
- [[`4a35ba4966`](https://github.com/nodejs/node/commit/4a35ba4966)] - **(SEMVER-MINOR)** **fs**: include filename in watch errors (charlierudolph) [#2748](https://github.com/nodejs/node/pull/2748)
- [[`2ddbbfd164`](https://github.com/nodejs/node/commit/2ddbbfd164)] - **http**: cork/uncork before flushing pipelined res (Fedor Indutny) [#3172](https://github.com/nodejs/node/pull/3172)
- [[`f638402e2f`](https://github.com/nodejs/node/commit/f638402e2f)] - **http**: add comment about `outputSize` in res/server (Fedor Indutny) [#3128](https://github.com/nodejs/node/pull/3128)
- [[`1850879b0e`](https://github.com/nodejs/node/commit/1850879b0e)] - **js_stream**: prevent abort if isalive doesn't exist (Trevor Norris) [#3282](https://github.com/nodejs/node/pull/3282)
- [[`63644dd1cd`](https://github.com/nodejs/node/commit/63644dd1cd)] - **lib**: remove redundant code, add tests in timers.js (Rich Trott) [#3143](https://github.com/nodejs/node/pull/3143)
- [[`74f443583c`](https://github.com/nodejs/node/commit/74f443583c)] - **module**: use UNC paths when loading native addons (Justin Chase) [#2965](https://github.com/nodejs/node/pull/2965)
- [[`01cb3fc36b`](https://github.com/nodejs/node/commit/01cb3fc36b)] - **net**: don't throw on bytesWritten access (Trevor Norris) [#3305](https://github.com/nodejs/node/pull/3305)
- [[`9d65528b01`](https://github.com/nodejs/node/commit/9d65528b01)] - **(SEMVER-MINOR)** **node**: add -c|--check CLI arg to syntax check script (Dave Eddy) [#2411](https://github.com/nodejs/node/pull/2411)
- [[`42b936e78d`](https://github.com/nodejs/node/commit/42b936e78d)] - **(SEMVER-MINOR)** **src**: add process.release.lts property (Rod Vagg) [#3212](https://github.com/nodejs/node/pull/3212)
- [[`589287b2e3`](https://github.com/nodejs/node/commit/589287b2e3)] - **src**: convert BE-utf16-string to LE before search (Karl Skomski) [#3295](https://github.com/nodejs/node/pull/3295)
- [[`2314378f06`](https://github.com/nodejs/node/commit/2314378f06)] - **src**: fix u-a-free if uv returns err in ASYNC_CALL (Karl Skomski) [#3049](https://github.com/nodejs/node/pull/3049)
- [[`d99336a391`](https://github.com/nodejs/node/commit/d99336a391)] - **(SEMVER-MINOR)** **src**: replace naive search in Buffer::IndexOf (Karl Skomski) [#2539](https://github.com/nodejs/node/pull/2539)
- [[`546e8333ba`](https://github.com/nodejs/node/commit/546e8333ba)] - **(SEMVER-MINOR)** **src**: fix --abort-on-uncaught-exception (Jeremy Whitlock) [#3036](https://github.com/nodejs/node/pull/3036)
- [[`7271cb047c`](https://github.com/nodejs/node/commit/7271cb047c)] - **(SEMVER-MINOR)** **src**: add process.versions.icu (Evan Lucas) [#3102](https://github.com/nodejs/node/pull/3102)
- [[`7b9f78acb2`](https://github.com/nodejs/node/commit/7b9f78acb2)] - **stream**: avoid pause with unpipe in buffered write (Brian White) [#2325](https://github.com/nodejs/node/pull/2325)
- [[`f0f8afd879`](https://github.com/nodejs/node/commit/f0f8afd879)] - **test**: remove common.inspect() (Rich Trott) [#3257](https://github.com/nodejs/node/pull/3257)
- [[`5ca4f6f8bd`](https://github.com/nodejs/node/commit/5ca4f6f8bd)] - **test**: test `util` rather than `common` (Rich Trott) [#3256](https://github.com/nodejs/node/pull/3256)
- [[`7a5ae34345`](https://github.com/nodejs/node/commit/7a5ae34345)] - **test**: refresh temp directory when using pipe (Rich Trott) [#3231](https://github.com/nodejs/node/pull/3231)
- [[`7c85557ef0`](https://github.com/nodejs/node/commit/7c85557ef0)] - **test**: Fix test-fs-read-stream-fd-leak race cond (Junliang Yan) [#3218](https://github.com/nodejs/node/pull/3218)
- [[`26a7ec6960`](https://github.com/nodejs/node/commit/26a7ec6960)] - **test**: fix losing original env vars issue (Junliang Yan) [#3190](https://github.com/nodejs/node/pull/3190)
- [[`e922716192`](https://github.com/nodejs/node/commit/e922716192)] - **test**: remove deprecated error logging (Rich Trott) [#3079](https://github.com/nodejs/node/pull/3079)
- [[`8f29d95a8c`](https://github.com/nodejs/node/commit/8f29d95a8c)] - **test**: report timeout in TapReporter (Karl Skomski) [#2647](https://github.com/nodejs/node/pull/2647)
- [[`2d0fe4c657`](https://github.com/nodejs/node/commit/2d0fe4c657)] - **test**: linting for buffer-free-callback test (Rich Trott) [#3230](https://github.com/nodejs/node/pull/3230)
- [[`70c9e4337e`](https://github.com/nodejs/node/commit/70c9e4337e)] - **test**: make common.js mandatory via linting rule (Rich Trott) [#3157](https://github.com/nodejs/node/pull/3157)
- [[`b7179562aa`](https://github.com/nodejs/node/commit/b7179562aa)] - **test**: load common.js in all tests (Rich Trott) [#3157](https://github.com/nodejs/node/pull/3157)
- [[`bab555a1c1`](https://github.com/nodejs/node/commit/bab555a1c1)] - **test**: speed up stringbytes-external test (Evan Lucas) [#3005](https://github.com/nodejs/node/pull/3005)
- [[`ddf258376d`](https://github.com/nodejs/node/commit/ddf258376d)] - **test**: use normalize() for unicode paths (Roman Reiss) [#3007](https://github.com/nodejs/node/pull/3007)
- [[`46876d519c`](https://github.com/nodejs/node/commit/46876d519c)] - **test**: remove arguments.callee usage (Roman Reiss) [#3167](https://github.com/nodejs/node/pull/3167)
- [[`af10df6108`](https://github.com/nodejs/node/commit/af10df6108)] - **tls**: use parent handle's close callback (Fedor Indutny) [#2991](https://github.com/nodejs/node/pull/2991)
- [[`9c2748bad1`](https://github.com/nodejs/node/commit/9c2748bad1)] - **tools**: remove leftover license boilerplate (Nathan Rajlich) [#3225](https://github.com/nodejs/node/pull/3225)
- [[`5d9f83ff2a`](https://github.com/nodejs/node/commit/5d9f83ff2a)] - **tools**: apply linting to custom rules code (Rich Trott) [#3195](https://github.com/nodejs/node/pull/3195)
- [[`18a8b2ec73`](https://github.com/nodejs/node/commit/18a8b2ec73)] - **tools**: remove unused gflags module (Ben Noordhuis) [#3220](https://github.com/nodejs/node/pull/3220)
- [[`e0fffca836`](https://github.com/nodejs/node/commit/e0fffca836)] - **util**: fix for inspecting promises (Evan Lucas) [#3221](https://github.com/nodejs/node/pull/3221)
- [[`8dfdee3733`](https://github.com/nodejs/node/commit/8dfdee3733)] - **util**: correctly inspect Map/Set Iterators (Evan Lucas) [#3119](https://github.com/nodejs/node/pull/3119)
- [[`b5c51fdba0`](https://github.com/nodejs/node/commit/b5c51fdba0)] - **util**: fix check for Array constructor (Evan Lucas) [#3119](https://github.com/nodejs/node/pull/3119)

Windows 32-bit Installer: https://nodejs.org/dist/v4.2.0/node-v4.2.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v4.2.0/node-v4.2.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v4.2.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v4.2.0/win-x64/node.exe \
Mac OS X 64-bit Installer: https://nodejs.org/dist/v4.2.0/node-v4.2.0.pkg \
Mac OS X 64-bit Binary: https://nodejs.org/dist/v4.2.0/node-v4.2.0-darwin-x64.tar.gz \
Linux 32-bit Binary: https://nodejs.org/dist/v4.2.0/node-v4.2.0-linux-x86.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v4.2.0/node-v4.2.0-linux-x64.tar.gz \
SmartOS 32-bit Binary: https://nodejs.org/dist/v4.2.0/node-v4.2.0-sunos-x86.tar.gz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v4.2.0/node-v4.2.0-sunos-x64.tar.gz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v4.2.0/node-v4.2.0-linux-armv6l.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v4.2.0/node-v4.2.0-linux-armv7l.tar.gz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v4.2.0/node-v4.2.0-linux-arm64.tar.gz \
Source Code: https://nodejs.org/dist/v4.2.0/node-v4.2.0.tar.gz \
Other release files: https://nodejs.org/dist/v4.2.0/ \
Documentation: https://nodejs.org/docs/v4.2.0/api/

Shasums (GPG signing hash: SHA512, file hash: SHA256):

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA512

078c9a2c2fbfb603700d94b618df3b8b211e10f361bced5e4439142715aebde5  node-v4.2.0-darwin-x64.tar.gz
5ca8495fd39f5fccaaaad4a4c10602723b4146efd8cb2c933f1733cf0f2bd2b3  node-v4.2.0-darwin-x64.tar.xz
b82bd1c46b49599e5ee6565b0b4ecb5ef6f3af573f897f9df2470eafe255feed  node-v4.2.0-headers.tar.gz
2cb4beaaacd2c097cd2ea5cc6de47427ee9e495cc7283a1e0476ae3a727e7cab  node-v4.2.0-headers.tar.xz
ae67d6ddcb72505937982d6a0bcb031b3522f7abc11d9e2d449ea021a5f75faf  node-v4.2.0-linux-arm64.tar.gz
6b96bc7de2e085ba04213aeb353396dd6ae5fcf6076c62598a83d7c009b4da3a  node-v4.2.0-linux-arm64.tar.xz
c1844dd131020b2b2982de974d73b051159776fe3ebf6fa91f08a2f5e8c46555  node-v4.2.0-linux-armv7l.tar.gz
c28312e164ea13dc6f2027926d55a7460ca7051c6f7e8828203fc686027d27e0  node-v4.2.0-linux-armv7l.tar.xz
98b60c86d541f44c5d07111f9ffc9a81848f976cfbe84f71cb9c7d6bfd34e1a4  node-v4.2.0-linux-x64.tar.gz
fd4830966fa1afd4ddbbde6b1e7fb581ce99917ad5dd41da9cb9b27d191c16a1  node-v4.2.0-linux-x64.tar.xz
bc5e0791c23d897ea6c83976dd14eec8ac33f74188806012992aca335c4e9214  node-v4.2.0-linux-x86.tar.gz
aff372b9e6f7ccfef16c55cda5c9666847322e24c8bc574e6081f8b20ef1fe0a  node-v4.2.0-linux-x86.tar.xz
83005cd68ebf94c528b1da28cc3915df56426224ce6f6337ad2b067be87a00b0  node-v4.2.0.pkg
d074ae0ab26f2f680adf7b94cfae0a8eac5ae4dcf083594a361b837fea6fdc72  node-v4.2.0-sunos-x64.tar.gz
c24dd20cc4eb296418fd13913c60910929698488be4079441aeb34e5a4a23295  node-v4.2.0-sunos-x64.tar.xz
fc753685f937d338f1367614ca49158d446e29b35694fac6b8e6be262db5ec38  node-v4.2.0-sunos-x86.tar.gz
77cf3a17555dc83871c61bfbfc7fea9373c4664fb174e4b90281a5541be9aa42  node-v4.2.0-sunos-x86.tar.xz
94ea40efeb598fed6e1a78990b698da73275c49876ec551dc36e11513c99fb6b  node-v4.2.0.tar.gz
611e899c10fc26e077f26d70802728e87e743f9bd0c9ad9b4a9b1aeb6a45b511  node-v4.2.0.tar.xz
9be6a2f95fb17c70aada09e0c5e2dadd2ce2bb2c5ac83bcce47d0f6c290ba15c  node-v4.2.0-x64.msi
6f1c92772369ffb82d9eed586d74d343ea0d61bb9770746aae7589779d39a9af  node-v4.2.0-x86.msi
701498dd467ccfcc2dd083c53212654d9bc04e5289e1021a6e411e758c411eeb  win-x64/node.exe
2ce13cf0c50a0df6d6591ea84201a318877b5eb39eeac3dc189130da19c34685  win-x64/node.lib
cf75e01de1c397342a01fa4d682851ae0694554c921604a27415c7681c02f7be  win-x86/node.exe
4eca7dbfa577f015bb546d74ca330ce12ee5605924e463b7451b72f041a9b772  win-x86/node.lib
-----BEGIN PGP SIGNATURE-----
Comment: GPGTools - https://gpgtools.org

iQEcBAEBCgAGBQJWG/msAAoJEHNBsVwHCHesiwQH/2MWeonw7cb2fOmmTA2nItB7
hpVUwnMwN1eziOZhZmG2V+iEd0h7QKWUWpVUrPyr6LMUndesa6g1oD9r93u02Wje
tSbxFgg28lUyTWyGhL18ZNapZGi4HYpxaJBgZ/szjk1nDmVJ80XMnDh7qKQx19/X
Ccwkqq3epkuix5utvUrj6+KAtxqeIYEoW5p+7OaRf+KzZNle6oD3JzjwZ7w8FjHB
3AVR/P95e2VGoIotlxG1SgF8Qe+rPqAj9xiKTKgHuPwBWqH09ulQcRiiMw/5xXal
dSkkiIu/sks+/MKTU+ltR2emQq5Ck75bY1Gvn1ki+jNP1KZcmYkgRoHjH0RkSYE=
=gqB4
-----END PGP SIGNATURE-----

```
