---
date: '2022-01-11T00:27:39.750Z'
category: release
title: Node v16.13.2 (LTS)
layout: blog-post
author: <PERSON>
---

### Notable changes

#### Improper handling of URI Subject Alternative Names (Medium)(CVE-2021-44531)

Accepting arbitrary Subject Alternative Name (SAN) types, unless a PKI is specifically defined to use a particular SAN type, can result in bypassing name-constrained intermediates. Node.js was accepting URI SAN types, which PKIs are often not defined to use. Additionally, when a protocol allows URI SANs, Node.js did not match the URI correctly.

Versions of Node.js with the fix for this disable the URI SAN type when checking a certificate against a hostname. This behavior can be reverted through the `--security-revert` command-line option.

More details will be available at [CVE-2021-44531](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2021-44531) after publication.

#### Certificate Verification Bypass via String Injection (Medium)(CVE-2021-44532)

Node.js converts SANs (Subject Alternative Names) to a string format. It uses this string to check peer certificates against hostnames when validating connections. The string format was subject to an injection vulnerability when name constraints were used within a certificate chain, allowing the bypass of these name constraints.

Versions of Node.js with the fix for this escape SANs containing the problematic characters in order to prevent the injection. This behavior can be reverted through the `--security-revert` command-line option.

More details will be available at [CVE-2021-44532](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2021-44532) after publication.

#### Incorrect handling of certificate subject and issuer fields (Medium)(CVE-2021-44533)

Node.js did not handle multi-value Relative Distinguished Names correctly. Attackers could craft certificate subjects containing a single-value Relative Distinguished Name that would be interpreted as a multi-value Relative Distinguished Name, for example, in order to inject a Common Name that would allow bypassing the certificate subject verification.

Affected versions of Node.js do not accept multi-value Relative Distinguished Names and are thus not vulnerable to such attacks themselves. However, third-party code that uses node's ambiguous presentation of certificate subjects may be vulnerable.

More details will be available at [CVE-2021-44533](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2021-44533) after publication.

#### Prototype pollution via `console.table` properties (Low)(CVE-2022-21824)

Due to the formatting logic of the `console.table()` function it was not safe to allow user controlled input to be passed to the `properties` parameter while simultaneously passing a plain object with at least one property as the first parameter, which could be `__proto__`. The prototype pollution has very limited control, in that it only allows an empty string to be assigned numerical keys of the object prototype.

Versions of Node.js with the fix for this use a null protoype for the object these properties are being assigned to.

More details will be available at [CVE-2022-21824](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2022-21824) after publication.

Thanks to Patrik Oldsberg (rugvip) for reporting this vulnerability.

### Commits

- \[[`8dd4ca4537`](https://github.com/nodejs/node/commit/8dd4ca4537)] - **console**: fix prototype pollution via console.table (Tobias Nießen) [nodejs-private/node-private#307](https://github.com/nodejs-private/node-private/pull/307)
- \[[`e52882da4c`](https://github.com/nodejs/node/commit/e52882da4c)] - **crypto,tls**: implement safe x509 GeneralName format (Tobias Nießen) [nodejs-private/node-private#300](https://github.com/nodejs-private/node-private/pull/300)
- \[[`9a0a189b0b`](https://github.com/nodejs/node/commit/9a0a189b0b)] - **src**: add cve reverts and associated tests (Michael Dawson) [nodejs-private/node-private#300](https://github.com/nodejs-private/node-private/pull/300)
- \[[`4a262d42bc`](https://github.com/nodejs/node/commit/4a262d42bc)] - **src**: remove unused x509 functions (Tobias Nießen) [nodejs-private/node-private#300](https://github.com/nodejs-private/node-private/pull/300)
- \[[`965536fe3d`](https://github.com/nodejs/node/commit/965536fe3d)] - **tls**: fix handling of x509 subject and issuer (Tobias Nießen) [nodejs-private/node-private#300](https://github.com/nodejs-private/node-private/pull/300)
- \[[`a2cbfa95ff`](https://github.com/nodejs/node/commit/a2cbfa95ff)] - **tls**: drop support for URI alternative names (Tobias Nießen) [nodejs-private/node-private#300](https://github.com/nodejs-private/node-private/pull/300)

Windows 32-bit Installer: https://nodejs.org/dist/v16.13.2/node-v16.13.2-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v16.13.2/node-v16.13.2-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v16.13.2/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v16.13.2/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v16.13.2/node-v16.13.2.pkg \
macOS Apple Silicon 64-bit Binary: https://nodejs.org/dist/v16.13.2/node-v16.13.2-darwin-arm64.tar.gz \
macOS Intel 64-bit Binary: https://nodejs.org/dist/v16.13.2/node-v16.13.2-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v16.13.2/node-v16.13.2-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v16.13.2/node-v16.13.2-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v16.13.2/node-v16.13.2-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v16.13.2/node-v16.13.2-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v16.13.2/node-v16.13.2-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v16.13.2/node-v16.13.2-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v16.13.2/node-v16.13.2.tar.gz \
Other release files: https://nodejs.org/dist/v16.13.2/ \
Documentation: https://nodejs.org/docs/v16.13.2/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

54078953e99360087e1c300f7cc36165b838229034c451367428b40095656133  node-v16.13.2-aix-ppc64.tar.gz
09d300008ad58792c12622a5eafdb14c931587bb88713df4df64cdf4ff2188d1  node-v16.13.2-darwin-arm64.tar.gz
a67021c57fe05e87ba2e3fe91f828e22a38a032048301bfb3338ba0a7844219d  node-v16.13.2-darwin-arm64.tar.xz
900a952bb77533d349e738ff8a5179a4344802af694615f36320a888b49b07e6  node-v16.13.2-darwin-x64.tar.gz
ba5e44a0d6f3cbf2cc06ea27a01bca544504bfe3d4ff69369dad85c60a226ee6  node-v16.13.2-darwin-x64.tar.xz
a3e023ea3bc30b629c0e86f069c1900ec8f4ccb5ede1984b96ed831d4b4c469e  node-v16.13.2-headers.tar.gz
2076c3156913509a2378b34f529f2e0c17abb688652ec823b66a3b0f0466e5df  node-v16.13.2-headers.tar.xz
e87d7c173d7c70672d71cc816ffe0baea2b0458cb7f96c248560410e9cd37522  node-v16.13.2-linux-arm64.tar.gz
a3cf8e4e9fbea27573eee6da84720bf7227ddd22842b842d48049d6dfe55fb03  node-v16.13.2-linux-arm64.tar.xz
59af2a54018112c31faec25502e01c1781c28ef85e168f383806606eba846ddf  node-v16.13.2-linux-armv7l.tar.gz
e09c6d5b24f7ac26a7efec72acaf973f7a96d7f15f9cb9078c067d16ef23d50c  node-v16.13.2-linux-armv7l.tar.xz
1455299d2b2c6e913eff9d75b261df7658f27ab062b99213efcdd076198faf71  node-v16.13.2-linux-ppc64le.tar.gz
9227a9e154493a1c341cde8a3cef52f7f2ac03805e6b5a55e2c11b726e1a77d4  node-v16.13.2-linux-ppc64le.tar.xz
331c6396743093a99ee89c01df32bff7c67985d64e3584d846af80331ee44507  node-v16.13.2-linux-s390x.tar.gz
c4ba46fc19366f7377d28a60a98f741bfa38045d7924306244c51d1660afcc8d  node-v16.13.2-linux-s390x.tar.xz
a0f23911d5d9c371e95ad19e4e538d19bffc0965700f187840eb39a91b0c3fb0  node-v16.13.2-linux-x64.tar.gz
7f5e9a42d6e86147867d35643c7b1680c27ccd45db85666fc52798ead5e74421  node-v16.13.2-linux-x64.tar.xz
6a517182b2195f37636dc50a2d65ab0484130dd397d31bb22ab9d0a0df48c48f  node-v16.13.2.pkg
cd5a07cae25985704a5b1878355b2793d62d70fc97b8a181ad2bf86201121b08  node-v16.13.2.tar.gz
98b1de1ff92a292b93d2b2c93bc2a98656647b3d0c0d5623069f4f8047a8b4a0  node-v16.13.2.tar.xz
df1cd0a54fbc0ddf6a6149a133bd737d55806b0cb3f3bb2be91e03c3760226af  node-v16.13.2-win-x64.7z
107e3ece84b7fa1e80b3bdf03181d395246c7867e27b17b6d7e6fa9c7932b467  node-v16.13.2-win-x64.zip
8ccbbf348eafc8ffd140f3d193c76a060056d10148a053aeed327774ce70ec59  node-v16.13.2-win-x86.7z
79a30a8f846e2a3951a36a56f8d824070001d5762e3ba517d6b563364179a18f  node-v16.13.2-win-x86.zip
1690d6c4947e9b998b183b8eca3056729763e63d2306bc38fc1170b4f0de0689  node-v16.13.2-x64.msi
a220a4b55ec0a6b9aa7b5651cded20a167a75f8a51aa908ff15dd25890f19e00  node-v16.13.2-x86.msi
b30d47d1450739f14a1e6dfd187cf0944c6464fff18d60989bd89860da6c9fa4  win-x64/node.exe
c078b821ad8079c44556ec064cbd3a7e4ecbc894128698df7687ec6aae9c3075  win-x64/node.lib
8e1b546e7203dadcec859a7ca13955036c109592bc7303e413f9fcd1cbd46757  win-x64/node_pdb.7z
b6ea03f867a72eae1fd9ae4f5156738542e581be6235c3f0aa3a79b96c0c0f2a  win-x64/node_pdb.zip
4473873cc5b4c0d9ff5172325c6d9b544e2a4e3176725b7cfc1aa246219ecb98  win-x86/node.exe
4c5ff3e89d6c4e098c502a616dca2c981c6cedd2ceead00155ddfef05bd9a1b2  win-x86/node.lib
18bfb673a2cecdc162a3af2d46ebc56958a34d0212c3d5c863444b9ec4aef8c2  win-x86/node_pdb.7z
653763b41926a122a9b1f5f8f8f8e3b09fb4af69675a26024a0130cbada5575d  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEdPEmArbxxOkT+qN606iWE2Q7YgEFAmHcxDYACgkQ06iWE2Q7
YgHURhAA258wDSxa7OKTgs2sRnVYGj4MNg/QA6IFzqJfSZZZ/uO6nI5oP+gwx90L
N48zikwE/3/cfEsGBRA332EN6j9/P4TcTkrfc4MTESAWhKSTdX509XRyeiG0X+0A
u44qdxj8CEfykIiz0dq2w0SCdGg7ynOQm7kSGdlvKhuhw2NJKhepxz8/iwg3KQDf
R31YBvmEzc81sLWFTpgRpWiF6rGk5dA1aLtRkV/JJKcJIqSITDTFkOTW6MJnDBA4
+60QgD2UfuXGnaZHvsQvcRM8wXevzbIq9jP/iZBURK7AgvzGHkRVPf5Nus/PKB3m
JrY/TW3nMy2EMySNu3EZPuYAs2ukSzePLg3IvTGfJiw4kcEYvbouEYwlU49TFl0c
RFh4XmQpXKNfKxiiOK7GBy3e8Bwuzod5GkUNR1IHqgiI7QkG+evnIpx3h8dKfBiy
UhJQRES75K34cdjEY7Fxt+zTXXNYJDIGG6rz9THtL0zgX2CFSxgkHLPtLL/evfu/
bdYS2PZe8mX2ygrszppVniIRtcr8752BIXEiX02pNBVsQz1CmzaTHSE3cN93CUCw
MKSFzVFkDoDR9nIgV4DGFQ4afMcl/dPQ0dsGe2EjI6mGbuB01qsO9NvdtVQ+46tT
7j+ZsKrhFAztFH55XO9QGfQ+lTC05N7KQFOCObrS13kRXqMF9J8=
=bDx1
-----END PGP SIGNATURE-----

```
