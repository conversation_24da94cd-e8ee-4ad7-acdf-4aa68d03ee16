---
date: '2017-11-07T18:10:51.229Z'
category: release
title: Node v9.1.0 (Current)
layout: blog-post
author: <PERSON>
---

### Notable Changes

- **CLI**:
  - `NODE_OPTIONS` now supports the `--stack-trace-limit` option. [#16495](https://github.com/nodejs/node/pull/16495)
- **deps**:
  - OpenSSL is upgraded to 1.0.2m [#16691](https://github.com/nodejs/node/pull/16691)
- **http**:
  - A `'connect'` event handler leak has been fixed. [#16725](https://github.com/nodejs/node/pull/16725)
  - The `103 Early Hints` status code is now supported. [#16644](https://github.com/nodejs/node/pull/16644)

### Commits

- [[`32417999ac`](https://github.com/nodejs/node/commit/32417999ac)] - **build**: suppress lint-md output (<PERSON>) [#16551](https://github.com/nodejs/node/pull/16551)
- [[`433745e7eb`](https://github.com/nodejs/node/commit/433745e7eb)] - **build**: add missing comma in sources list (Daniel Bevenius) [#16613](https://github.com/nodejs/node/pull/16613)
- [[`8bc5249223`](https://github.com/nodejs/node/commit/8bc5249223)] - **build**: make test-doc and lint addon docs (Joyee Cheung) [#16377](https://github.com/nodejs/node/pull/16377)
- [[`88ad01fce7`](https://github.com/nodejs/node/commit/88ad01fce7)] - **build**: make doc target quiet (Daniel Bevenius) [#16516](https://github.com/nodejs/node/pull/16516)
- [[`f3e01618f1`](https://github.com/nodejs/node/commit/f3e01618f1)] - **build,src**: Add CloudABI as a POSIX-like runtime environment. (Ed Schouten) [#16612](https://github.com/nodejs/node/pull/16612)
- [[`7349d42945`](https://github.com/nodejs/node/commit/7349d42945)] - **(SEMVER-MINOR)** **cli**: add --stack-trace-limit to NODE_OPTIONS (Anna Henningsen) [#16495](https://github.com/nodejs/node/pull/16495)
- [[`ed0fbd8d72`](https://github.com/nodejs/node/commit/ed0fbd8d72)] - **deps**: cherry-pick e7f4e9e from upstream libuv (Bartosz Sosnowski) [#16724](https://github.com/nodejs/node/pull/16724)
- [[`185229e258`](https://github.com/nodejs/node/commit/185229e258)] - **deps**: update openssl asm and asm_obsolete files (Shigeki Ohtsu) [#16691](https://github.com/nodejs/node/pull/16691)
- [[`162686f5f4`](https://github.com/nodejs/node/commit/162686f5f4)] - **deps**: add -no_rand_screen to openssl s_client (Shigeki Ohtsu) [nodejs/io.js#1836](https://github.com/nodejs/io.js/pull/1836)
- [[`e0f6dee961`](https://github.com/nodejs/node/commit/e0f6dee961)] - **deps**: fix asm build error of openssl in x86_win32 (Shigeki Ohtsu) [iojs/io.js#1389](https://github.com/iojs/io.js/pull/1389)
- [[`3d7eea5da8`](https://github.com/nodejs/node/commit/3d7eea5da8)] - **deps**: fix openssl assembly error on ia32 win32 (Fedor Indutny) [iojs/io.js#1389](https://github.com/iojs/io.js/pull/1389)
- [[`3438765781`](https://github.com/nodejs/node/commit/3438765781)] - **deps**: copy all openssl header files to include dir (Shigeki Ohtsu) [#16691](https://github.com/nodejs/node/pull/16691)
- [[`b130febd1d`](https://github.com/nodejs/node/commit/b130febd1d)] - **deps**: upgrade openssl sources to 1.0.2m (Shigeki Ohtsu) [#16691](https://github.com/nodejs/node/pull/16691)
- [[`90e8e81bbb`](https://github.com/nodejs/node/commit/90e8e81bbb)] - **doc**: mention constant-time in crypto doc (Mithun Sasidharan) [#16604](https://github.com/nodejs/node/pull/16604)
- [[`dee7800ae8`](https://github.com/nodejs/node/commit/dee7800ae8)] - **doc**: add links to EventEmitter in errors.md (Delapouite) [#16861](https://github.com/nodejs/node/pull/16861)
- [[`f097e2775b`](https://github.com/nodejs/node/commit/f097e2775b)] - **doc**: fix a link in dgram.md (Vse Mozhet Byt) [#16854](https://github.com/nodejs/node/pull/16854)
- [[`978aa8476b`](https://github.com/nodejs/node/commit/978aa8476b)] - **doc**: add isTTY property documentation (SonaySevik) [#16828](https://github.com/nodejs/node/pull/16828)
- [[`6739f41f2d`](https://github.com/nodejs/node/commit/6739f41f2d)] - **doc**: fix json generator warnings (Luigi Pinca) [#16742](https://github.com/nodejs/node/pull/16742)
- [[`2bb148f7bb`](https://github.com/nodejs/node/commit/2bb148f7bb)] - **doc**: make stream.Readable consistent (Sakthipriyan Vairamani (thefourtheye)) [#16786](https://github.com/nodejs/node/pull/16786)
- [[`e05d4f43b6`](https://github.com/nodejs/node/commit/e05d4f43b6)] - **doc**: correct effects to affects (gowpen) [#16794](https://github.com/nodejs/node/pull/16794)
- [[`d7df4dfa1c`](https://github.com/nodejs/node/commit/d7df4dfa1c)] - **doc**: correct EventEmitter reference (gowpen) [#16791](https://github.com/nodejs/node/pull/16791)
- [[`77e4ec8c51`](https://github.com/nodejs/node/commit/77e4ec8c51)] - **doc**: update license to include node-inspect (Myles Borins) [#16659](https://github.com/nodejs/node/pull/16659)
- [[`7388144dbc`](https://github.com/nodejs/node/commit/7388144dbc)] - **doc**: add 9.x to version picker and mark 8.x as LTS (Chris Young) [#16672](https://github.com/nodejs/node/pull/16672)
- [[`e585c41487`](https://github.com/nodejs/node/commit/e585c41487)] - **doc**: add docs for Zlib#close() (Luigi Pinca) [#16592](https://github.com/nodejs/node/pull/16592)
- [[`d5ea177652`](https://github.com/nodejs/node/commit/d5ea177652)] - **doc**: add nodejs/gyp team for GYP related issues (Gibson Fahnestock) [#16638](https://github.com/nodejs/node/pull/16638)
- [[`09181eb976`](https://github.com/nodejs/node/commit/09181eb976)] - **doc**: add details about rss on process.memoryUsage (Anthony Nandaa) [#16566](https://github.com/nodejs/node/pull/16566)
- [[`3fd7eddb44`](https://github.com/nodejs/node/commit/3fd7eddb44)] - **doc**: add windowsVerbatimArguments docs (Andrew Stucki) [#16299](https://github.com/nodejs/node/pull/16299)
- [[`1771bb5039`](https://github.com/nodejs/node/commit/1771bb5039)] - **doc**: fix Changelog link order (Gibson Fahnestock) [#16632](https://github.com/nodejs/node/pull/16632)
- [[`6ee28b2823`](https://github.com/nodejs/node/commit/6ee28b2823)] - **doc**: util.isDeepStrictEqual returns boolean (Lucas Azzola) [#16653](https://github.com/nodejs/node/pull/16653)
- [[`59a4789eee`](https://github.com/nodejs/node/commit/59a4789eee)] - **doc**: howto decode buffers extending from Writable (dicearr) [#16403](https://github.com/nodejs/node/pull/16403)
- [[`d733dd9468`](https://github.com/nodejs/node/commit/d733dd9468)] - **doc**: add \*-inl.h include rule to C++ style guide (Joyee Cheung) [#16548](https://github.com/nodejs/node/pull/16548)
- [[`1cef9ef1de`](https://github.com/nodejs/node/commit/1cef9ef1de)] - **doc**: make default values and periods consistent (Matej Krajčovič) [#16563](https://github.com/nodejs/node/pull/16563)
- [[`77f0359708`](https://github.com/nodejs/node/commit/77f0359708)] - **http**: use 'connect' event only if socket is connecting (Luigi Pinca) [#16725](https://github.com/nodejs/node/pull/16725)
- [[`9c39d79908`](https://github.com/nodejs/node/commit/9c39d79908)] - **http**: use arrow fns for lexical `this` in Agent (Bryan English) [#16475](https://github.com/nodejs/node/pull/16475)
- [[`1b090c9b66`](https://github.com/nodejs/node/commit/1b090c9b66)] - **http, http2**: add 103 Early Hints status code (Yosuke Furukawa) [#16644](https://github.com/nodejs/node/pull/16644)
- [[`d6d461003f`](https://github.com/nodejs/node/commit/d6d461003f)] - **http, tls**: better support for IPv6 addresses (Mattias Holmlund) [#14772](https://github.com/nodejs/node/pull/14772)
- [[`762a11fab3`](https://github.com/nodejs/node/commit/762a11fab3)] - **http2**: improve errors thrown in header validation (Joyee Cheung) [#16718](https://github.com/nodejs/node/pull/16718)
- [[`72d0e7e70b`](https://github.com/nodejs/node/commit/72d0e7e70b)] - **http2**: refactor multiple internals (James M Snell) [#16676](https://github.com/nodejs/node/pull/16676)
- [[`e3283c71ce`](https://github.com/nodejs/node/commit/e3283c71ce)] - **http2**: allocate on every chunk send (James M Snell) [#16669](https://github.com/nodejs/node/pull/16669)
- [[`dfe56847ac`](https://github.com/nodejs/node/commit/dfe56847ac)] - **http2**: refactor settings handling (James M Snell) [#16668](https://github.com/nodejs/node/pull/16668)
- [[`bf7dc38ae4`](https://github.com/nodejs/node/commit/bf7dc38ae4)] - **http2**: make sessions garbage-collectible (Anna Henningsen) [#16461](https://github.com/nodejs/node/pull/16461)
- [[`3f529620cc`](https://github.com/nodejs/node/commit/3f529620cc)] - **http2**: remove unused assignment (Anna Henningsen) [#16461](https://github.com/nodejs/node/pull/16461)
- [[`b50c33470e`](https://github.com/nodejs/node/commit/b50c33470e)] - **http2**: track async state for sending (Anna Henningsen) [#16461](https://github.com/nodejs/node/pull/16461)
- [[`224ea159ae`](https://github.com/nodejs/node/commit/224ea159ae)] - **http2**: move uv_prepare handle to `Http2Session` (Anna Henningsen) [#16461](https://github.com/nodejs/node/pull/16461)
- [[`6074c8cdbb`](https://github.com/nodejs/node/commit/6074c8cdbb)] - **inspector**: include node_platform.h header (Alexey Kuzmin) [#16677](https://github.com/nodejs/node/pull/16677)
- [[`e0c7b3d13f`](https://github.com/nodejs/node/commit/e0c7b3d13f)] - **lib**: shuffle v8_prof_polyfill.js for unit testing (Ben Noordhuis) [#16769](https://github.com/nodejs/node/pull/16769)
- [[`c14030ec7a`](https://github.com/nodejs/node/commit/c14030ec7a)] - **lib**: fix version check in tick processor (Ben Noordhuis) [#16769](https://github.com/nodejs/node/pull/16769)
- [[`a0b94f4e12`](https://github.com/nodejs/node/commit/a0b94f4e12)] - **lib**: refactor ES module loader for readability (Anna Henningsen) [#16579](https://github.com/nodejs/node/pull/16579)
- [[`083a6e3830`](https://github.com/nodejs/node/commit/083a6e3830)] - **openssl**: fix keypress requirement in apps on win32 (Shigeki Ohtsu) [iojs/io.js#1389](https://github.com/iojs/io.js/pull/1389)
- [[`05f90478fc`](https://github.com/nodejs/node/commit/05f90478fc)] - **repl**: avoid crashing from null and undefined errors (cPhost) [#16574](https://github.com/nodejs/node/pull/16574)
- [[`da66610798`](https://github.com/nodejs/node/commit/da66610798)] - **src**: fix -Winconsistent-missing-override warning (Ben Noordhuis) [#16726](https://github.com/nodejs/node/pull/16726)
- [[`6a2cb124e3`](https://github.com/nodejs/node/commit/6a2cb124e3)] - **src**: clean up uv_fs_t's in module_wrap.cc (cjihrig) [#16722](https://github.com/nodejs/node/pull/16722)
- [[`8a2b4ee7bb`](https://github.com/nodejs/node/commit/8a2b4ee7bb)] - **src**: remove unnecessary call to SetHiddenPrototype (Toon Verwaest) [#16554](https://github.com/nodejs/node/pull/16554)
- [[`a87f846cc1`](https://github.com/nodejs/node/commit/a87f846cc1)] - **src**: add method to compute storage in WriteWrap (Anna Henningsen) [#16727](https://github.com/nodejs/node/pull/16727)
- [[`a814786a06`](https://github.com/nodejs/node/commit/a814786a06)] - **src**: improve module loader readability (Anna Henningsen) [#16536](https://github.com/nodejs/node/pull/16536)
- [[`c40b3c6d2a`](https://github.com/nodejs/node/commit/c40b3c6d2a)] - **src**: add 'dynamic' process.release.lts property (Rod Vagg) [#16656](https://github.com/nodejs/node/pull/16656)
- [[`f3a65a85d9`](https://github.com/nodejs/node/commit/f3a65a85d9)] - **src**: pass context to Get() operations for cares_wrap (Evan Lucas) [#16641](https://github.com/nodejs/node/pull/16641)
- [[`4f8765d4c8`](https://github.com/nodejs/node/commit/4f8765d4c8)] - **src**: remove unused includes in string_bytes.h (Daniel Bevenius) [#16606](https://github.com/nodejs/node/pull/16606)
- [[`c4736cfcba`](https://github.com/nodejs/node/commit/c4736cfcba)] - **src**: fix etw provider include on Windows (Joyee Cheung) [#16639](https://github.com/nodejs/node/pull/16639)
- [[`f31b796175`](https://github.com/nodejs/node/commit/f31b796175)] - **src**: add `InternalCallbackScope` util constructor (Anna Henningsen) [#16461](https://github.com/nodejs/node/pull/16461)
- [[`97fd6df920`](https://github.com/nodejs/node/commit/97fd6df920)] - **src**: do not include x.h if x-inl.h is included (Joyee Cheung) [#16548](https://github.com/nodejs/node/pull/16548)
- [[`2294ba49be`](https://github.com/nodejs/node/commit/2294ba49be)] - **test**: tick processor version check regression test (Ben Noordhuis) [#16769](https://github.com/nodejs/node/pull/16769)
- [[`b44157378a`](https://github.com/nodejs/node/commit/b44157378a)] - **test**: use default assertion message (jonask) [#16819](https://github.com/nodejs/node/pull/16819)
- [[`a409b874d9`](https://github.com/nodejs/node/commit/a409b874d9)] - **test**: improve message in test-fs-readfile-pipe-large (fjau) [#16840](https://github.com/nodejs/node/pull/16840)
- [[`527dddac2f`](https://github.com/nodejs/node/commit/527dddac2f)] - **test**: remove custom message from assertion (Nicolas Morel) [#16824](https://github.com/nodejs/node/pull/16824)
- [[`a85d6e9be8`](https://github.com/nodejs/node/commit/a85d6e9be8)] - **test**: show incorrect value on test failure (Sean Karson) [#16818](https://github.com/nodejs/node/pull/16818)
- [[`50d505c188`](https://github.com/nodejs/node/commit/50d505c188)] - **test**: include file mode in assert message (Sascha Tandel) [#16815](https://github.com/nodejs/node/pull/16815)
- [[`5f88543778`](https://github.com/nodejs/node/commit/5f88543778)] - **test**: refactor tls test to use fixtres.readSync (Brian O'Connell) [#16816](https://github.com/nodejs/node/pull/16816)
- [[`d054e94fdc`](https://github.com/nodejs/node/commit/d054e94fdc)] - **test**: add detailed message for assertion failure (Attila Gonda) [#16812](https://github.com/nodejs/node/pull/16812)
- [[`6831e42988`](https://github.com/nodejs/node/commit/6831e42988)] - **test**: use fixtures module in test-repl (Maring, Damian Lion) [#16809](https://github.com/nodejs/node/pull/16809)
- [[`ef679803a0`](https://github.com/nodejs/node/commit/ef679803a0)] - **test**: update test to use fixtures.readKey (Dara Hayes) [#16811](https://github.com/nodejs/node/pull/16811)
- [[`219ac4bde6`](https://github.com/nodejs/node/commit/219ac4bde6)] - **test**: fix typos in read-buffer tests (Jimi van der Woning) [#16834](https://github.com/nodejs/node/pull/16834)
- [[`e4b3c00e48`](https://github.com/nodejs/node/commit/e4b3c00e48)] - **test**: replace fixturesDir with usage of fixtures module (Octavian Ionescu) [#16810](https://github.com/nodejs/node/pull/16810)
- [[`021ccb4011`](https://github.com/nodejs/node/commit/021ccb4011)] - **test**: use default assertion messages (John Byrne) [#16808](https://github.com/nodejs/node/pull/16808)
- [[`2156828f20`](https://github.com/nodejs/node/commit/2156828f20)] - **test**: clarified assert message for test-require-json.js (Matthias Reis) [#16807](https://github.com/nodejs/node/pull/16807)
- [[`ec1b1108af`](https://github.com/nodejs/node/commit/ec1b1108af)] - **test**: replace common.fixturesDir with fixtures module (Dumitru Glavan) [#16803](https://github.com/nodejs/node/pull/16803)
- [[`700c5e7795`](https://github.com/nodejs/node/commit/700c5e7795)] - **test**: replace common.fixturesDir with fixtures.readSync() (Adri Van Houdt) [#16802](https://github.com/nodejs/node/pull/16802)
- [[`bcd818af2a`](https://github.com/nodejs/node/commit/bcd818af2a)] - **test**: replace `common.fixturesDir` usage (Sascha Tandel) [#16800](https://github.com/nodejs/node/pull/16800)
- [[`f3e63f254c`](https://github.com/nodejs/node/commit/f3e63f254c)] - **test**: update test to use fixtures (Adam Wegrzynek) [#16799](https://github.com/nodejs/node/pull/16799)
- [[`0ab3d37be5`](https://github.com/nodejs/node/commit/0ab3d37be5)] - **test**: refactor exitedAfterDisconnect test (Rich Trott) [#16729](https://github.com/nodejs/node/pull/16729)
- [[`26f1a1d9e9`](https://github.com/nodejs/node/commit/26f1a1d9e9)] - **test**: fix test-cli-node-options on Windows (Anna Henningsen) [#16709](https://github.com/nodejs/node/pull/16709)
- [[`fc58c5231f`](https://github.com/nodejs/node/commit/fc58c5231f)] - **test**: fix malformed parallel.status line (Rich Trott) [#16702](https://github.com/nodejs/node/pull/16702)
- [[`689c9d401e`](https://github.com/nodejs/node/commit/689c9d401e)] - **test**: mark test-async-wrap-uncaughtexception as flaky (Refael Ackermann) [#16694](https://github.com/nodejs/node/pull/16694)
- [[`0b337cbee8`](https://github.com/nodejs/node/commit/0b337cbee8)] - **test**: fix flaky test-http2-server-rst-stream.js (Anatoli Papirovski) [#16690](https://github.com/nodejs/node/pull/16690)
- [[`5077faffaa`](https://github.com/nodejs/node/commit/5077faffaa)] - **test**: pause child until parent is ready (jBarz) [#15774](https://github.com/nodejs/node/pull/15774)
- [[`d178c6dc91`](https://github.com/nodejs/node/commit/d178c6dc91)] - **test**: update process-release for Node 8 Carbon (Jeremiah Senkpiel) [#16656](https://github.com/nodejs/node/pull/16656)
- [[`ffe4d7b468`](https://github.com/nodejs/node/commit/ffe4d7b468)] - **test**: increase coverage for ModuleMap (Rob Paton) [#16045](https://github.com/nodejs/node/pull/16045)
- [[`f9b2099d51`](https://github.com/nodejs/node/commit/f9b2099d51)] - **test**: use fixtures module in test-https-pfx (Ken Takagi) [#15895](https://github.com/nodejs/node/pull/15895)
- [[`6998591be7`](https://github.com/nodejs/node/commit/6998591be7)] - **test,net**: remove scatological terminology (Rich Trott) [#16599](https://github.com/nodejs/node/pull/16599)
- [[`87b4e3ed49`](https://github.com/nodejs/node/commit/87b4e3ed49)] - **tls**: accept array of protocols in TLSSocket (Mark S. Everitt) [#16655](https://github.com/nodejs/node/pull/16655)
- [[`e9396d28b1`](https://github.com/nodejs/node/commit/e9396d28b1)] - **tools**: remove unneeded parentheses in doc/html.js (Vse Mozhet Byt) [#16845](https://github.com/nodejs/node/pull/16845)
- [[`6e22dc817f`](https://github.com/nodejs/node/commit/6e22dc817f)] - **tools**: replace string concatenation with template literals (Kevin Yu) [#16804](https://github.com/nodejs/node/pull/16804)
- [[`e781d93bd0`](https://github.com/nodejs/node/commit/e781d93bd0)] - **tools**: replace string concatenation with template literals (Giovanni Lela) [#16806](https://github.com/nodejs/node/pull/16806)
- [[`422d3158ee`](https://github.com/nodejs/node/commit/422d3158ee)] - **tools**: replace string concetation with templates (Patrick Heneise) [#16801](https://github.com/nodejs/node/pull/16801)
- [[`fa5a8419c7`](https://github.com/nodejs/node/commit/fa5a8419c7)] - **tools**: update to ESLint 4.10.0 (cjihrig) [#16738](https://github.com/nodejs/node/pull/16738)
- [[`326a048a5c`](https://github.com/nodejs/node/commit/326a048a5c)] - **tools**: add fixer for no-let-in-for-declaration (Weijia Wang) [#16642](https://github.com/nodejs/node/pull/16642)
- [[`d6a0ffe367`](https://github.com/nodejs/node/commit/d6a0ffe367)] - **zlib**: warn before crash on invalid internals usage (Anna Henningsen) [#16657](https://github.com/nodejs/node/pull/16657)

Windows 32-bit Installer: https://nodejs.org/dist/v9.1.0/node-v9.1.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v9.1.0/node-v9.1.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v9.1.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v9.1.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v9.1.0/node-v9.1.0.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v9.1.0/node-v9.1.0-darwin-x64.tar.gz \
Linux 32-bit Binary: https://nodejs.org/dist/v9.1.0/node-v9.1.0-linux-x86.tar.xz \
Linux 64-bit Binary: https://nodejs.org/dist/v9.1.0/node-v9.1.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v9.1.0/node-v9.1.0-linux-ppc64le.tar.xz \
Linux PPC BE 64-bit Binary: https://nodejs.org/dist/v9.1.0/node-v9.1.0-linux-ppc64.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v9.1.0/node-v9.1.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v9.1.0/node-v9.1.0-aix-ppc64.tar.gz \
SmartOS 32-bit Binary: https://nodejs.org/dist/v9.1.0/node-v9.1.0-sunos-x86.tar.xz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v9.1.0/node-v9.1.0-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v9.1.0/node-v9.1.0-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v9.1.0/node-v9.1.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v9.1.0/node-v9.1.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v9.1.0/node-v9.1.0.tar.gz \
Other release files: https://nodejs.org/dist/v9.1.0/ \
Documentation: https://nodejs.org/docs/v9.1.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

ad17f74b4ff2de4c3e9306fae9dbc018b27bdd150cfaf86b70aad0024e370e2c  node-v9.1.0-aix-ppc64.tar.gz
8e180de9b3f6a79f09a391c89de54920bbf5375337d3e13c5bfcbce85af711ab  node-v9.1.0-darwin-x64.tar.gz
ecc71499ccd1d9351a9e0f7664b27b45b4ddc868792d7431d7c4aaa9dcfd81fb  node-v9.1.0-darwin-x64.tar.xz
94286236f6b6f13bcede0b6b5a8b0af3b1f7afd4111ccc113b83af427cacbea0  node-v9.1.0-headers.tar.gz
f59ae615ad45b0a7946f2efa094f0d4a4967c085dc4fd4172a37f95085665f6c  node-v9.1.0-headers.tar.xz
fa28790ec02834a41dda046fed64edce8551b0ee488d2c74dc7e018475fff831  node-v9.1.0-linux-arm64.tar.gz
37d329fa06b22e30243c503b64d9666d56abb6c0ce903251d4b43e7ad0833fb3  node-v9.1.0-linux-arm64.tar.xz
eb325af21980e5373a04a617625b5b79f7280fd1dc6bc9dcfdef087e6862a16b  node-v9.1.0-linux-armv6l.tar.gz
a182800ab92e21ed867c44b352b2f01ac7c629d842b0f42426e0a81681bc1078  node-v9.1.0-linux-armv6l.tar.xz
08211a4adb0bd79da945d9c4780d7b9cbe71ef3b7207ca44d7c613574a298d28  node-v9.1.0-linux-armv7l.tar.gz
7f504a6057f13b71c9a73d6445dd3fe4a4282a4e86efedd1baf5e60a9d535d7d  node-v9.1.0-linux-armv7l.tar.xz
45b48466ed11952d2556c9c72a341e5e665c57115ac58e2370be828f1fd7e27e  node-v9.1.0-linux-ppc64le.tar.gz
90d0a94f394a57a5bdb987b22de97a6e3031605991b3cff73135900172fa65c2  node-v9.1.0-linux-ppc64le.tar.xz
0dd4cc88ce6ef05ef7fc0a72ab6b91ac84df9625fd1d33ddcbea12ab9fb52e2f  node-v9.1.0-linux-ppc64.tar.gz
f409b74bffec93db835ed2b42eda2231e783056799695949e2ba69b604660acf  node-v9.1.0-linux-ppc64.tar.xz
e49f47a026c2f8a164b70a44292fa489f47cc41a8de318a9a265dbc17b83efe8  node-v9.1.0-linux-s390x.tar.gz
424def53f7839c5289e1122dcd62266ba59f0aaa15125ca5b4fb02c5b7ca17d7  node-v9.1.0-linux-s390x.tar.xz
5a05b32e677c6d5c2e85df30663aa1898aa8fe45ac6b797554069c97b9a5d228  node-v9.1.0-linux-x64.tar.gz
b796ed115c97acd23211c294129b0a834ff3a6a0f583111443b89d2b24e0d4d9  node-v9.1.0-linux-x64.tar.xz
f2b0783f8be2d104e436e05b5f8477f4250f8af7cf785320ba6721546017d2d9  node-v9.1.0-linux-x86.tar.gz
3ba6004a276aee384ee6fbf5cb5c0465c00733299adbe477afdbb1d76767aa36  node-v9.1.0-linux-x86.tar.xz
eebfc33e030b278819bb00b5b9ef6b483c25ca56dcd78d3ad5cb7b865a74f331  node-v9.1.0.pkg
01f1ff72bb95462342fdcfa58ee8561c96117a562c66adcde5ff34d8814e8199  node-v9.1.0-sunos-x64.tar.gz
abb6a3dd9d735b3125e11462fda6830eeb3e8303c703282b5a4f698199d836f8  node-v9.1.0-sunos-x64.tar.xz
ae423dedb33dc90a64ae46394c3c67c6ec16d25956c7a345853ca5ce822bbdb4  node-v9.1.0-sunos-x86.tar.gz
3c28115018acd0eb6a08964ed5cd3c722851a3033be26e48fa185df660b038f3  node-v9.1.0-sunos-x86.tar.xz
42dc9f4fe1db689284d5e848963a4f9af1f5092a9a167a890ccd88dcd6fd51df  node-v9.1.0.tar.gz
7e34d581332aedc85306859e613a81cce2531bdd30eef358cb54db8a4f5c3c49  node-v9.1.0.tar.xz
2ff2890c567497a719c0fda5c2c62dd2e5323f621cdcb12ae9249adde57c2472  node-v9.1.0-win-x64.7z
f2e76f536ff4fedd9884684f0b1979e04d0fd1c2a65d7d1a5a013de885dd07b3  node-v9.1.0-win-x64.zip
4964bc7962160cca1f5aceaa33f1c4076ac40a94a4a058db39964c6a982afab9  node-v9.1.0-win-x86.7z
4a19d1e23b5837f581588b1d5fad873b66408a78aa6a5f8b101939b02882dacd  node-v9.1.0-win-x86.zip
7e65a392af0e892e4440aa6c6882ce350a36a9a2e6b8f20619a98e5f21655cdd  node-v9.1.0-x64.msi
422657bb3005afcd8c36a962040af702c5d13587277dcc2ec0c826e9505e82d3  node-v9.1.0-x86.msi
13e62ab400929cdac50ba79d021aacab81c77cf38c31ddaa471f1aa5d9f84e95  win-x64/node.exe
5e239ffa56d82e3353b45f9b95126dbf9a3e57fa4bd9ee4b44d28642134e98b9  win-x64/node.lib
bea2306486270a6ea749ca3159e08e527e0f5aea92943e22ebbb12bd8d920dcc  win-x64/node_pdb.7z
a2a66c091ff23633a7c4fcf7c0483f885b975651fa80889ee2bca4618d3e0199  win-x64/node_pdb.zip
391fe1db712be674129b708462e137304b3f2a9f4cb7c296669846f1f57bd7d7  win-x86/node.exe
77a16a81133e1cf12534db344c73eed965af755b426f296513971266d9a3c145  win-x86/node.lib
d44e1d2bbdda5cca082f65ea0b193390634855df96a78d70c541b34244a961a8  win-x86/node_pdb.7z
e92800b01bbf97f77091253832fdb7b61f97e49005d3b35919ce3ec5e640d704  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----
Comment: GPGTools - https://gpgtools.org

iQIcBAEBCAAGBQJaAfV3AAoJEHQ0OQvb6bnFFdoQALrt0LJb0/nNL9Lb1Lxa80GS
0L3Am3Vcl2SAOJ9hJd3epIr854uL50VARfeXGO+9kLFN9GkpsJ7Y4rZn0JhzV/a1
wW0PuXfGlYWorDiHnH1zpKv/niRIkJH80Atv8DPFfPX74bhNtokcgds0sL5cqd98
bzsIiyC/g+MZPwpIuk/wqHLnIQCNkRv1Voy4HBIIVePtzZstsrPwPtRTavVfGHZ6
OhJMSozjpS8VCMRffhwxNZ3IYV6DSlBGfgAimVccBw826SLfqlW7o8Fp/MYx4bc+
+tDURvvxNKp+6AE2gi6YtEVesPEF8GNDziveBn3tMzu27N2oMkhoa01EDoZTjzJb
zsJV+xXTL3poQ0v34P2+qKvPCSypBDiYxjbwBEJYJAD0EGm/of+mD405rQTqyUJ3
3foILjc9ba1mStWJVfd45un30taLQf68BrrOYlspmnfGu4moKQ0Y48eNS0RIbBoq
Hnium0WPxt2fnIHVI4ZM2hZU8QovdjuCfl3AeB+u/nk9V2pIgu7iAbsJmFk2Bunf
U/0vJGmoho4zwlQI5WIgQYroRAEHLV+dpM99PkS0naNOXl8PPw4KxGi6Nfv9KlCj
okYLQc2RVhxTy2xGnSG7VrHXjYOvhClPlJvppkWHZxSQe6ZCG2TUxNwf3cCmsOxD
Eb9ZH5FF8I49fWTYK38S
=OydC
-----END PGP SIGNATURE-----

```
