---
date: '2016-12-06T19:39:44.152Z'
category: release
title: Node v6.9.2 (LTS)
layout: blog-post
author: <PERSON><PERSON>
---

### Notable Changes

- **buffer**: coerce slice parameters consistently (<PERSON><PERSON><PERSON><PERSON><PERSON> (thefourt<PERSON><PERSON>)) [#9101](https://github.com/nodejs/node/pull/9101)
- **deps**:
  - _npm_: upgrade npm to 3.10.9 (<PERSON>) [#9286](https://github.com/nodejs/node/pull/9286)
  - _V8_: Various fixes to destructuring edge cases
    - cherry-pick 3c39bac from V8 upstream (<PERSON><PERSON><PERSON>) [#9138](https://github.com/nodejs/node/pull/9138)
    - cherry pick 7166503 from upstream v8 (<PERSON><PERSON><PERSON>) [#9173](https://github.com/nodejs/node/pull/9173)
- **gtest**: the test reporter now outputs tap comments as yamlish (<PERSON>) [#9262](https://github.com/nodejs/node/pull/9262)
- **inspector**: inspector now prompts user to use 127.0.0.1 rather than localhost (<PERSON>) [#9451](https://github.com/nodejs/node/pull/9451)
- **tls**: fix memory leak when writing data to TLSWrap instance during handshake (Fedor Indutny) [#9586](https://github.com/nodejs/node/pull/9586)

### Commits

- [[`f3b0cf5052`](https://github.com/nodejs/node/commit/f3b0cf5052)] - **async_wrap**: call destroy() callback in uv_idle_t (Trevor Norris) [#10096](https://github.com/nodejs/node/pull/10096)
- [[`3e5b2eb49c`](https://github.com/nodejs/node/commit/3e5b2eb49c)] - **async_wrap**: make Initialize a static class member (Trevor Norris) [#10096](https://github.com/nodejs/node/pull/10096)
- [[`9ed60d308c`](https://github.com/nodejs/node/commit/9ed60d308c)] - **async_wrap**: mode constructor/destructor to .cc (Trevor Norris) [#10096](https://github.com/nodejs/node/pull/10096)
- [[`5eeac8cc57`](https://github.com/nodejs/node/commit/5eeac8cc57)] - **benchmark**: add microbenchmarks for ES Map (Rod Vagg) [#7581](https://github.com/nodejs/node/pull/7581)
- [[`e108f20d5c`](https://github.com/nodejs/node/commit/e108f20d5c)] - **buffer**: use correct name for custom inspect symbol (Charmander) [#9289](https://github.com/nodejs/node/pull/9289)
- [[`0cffa3c87e`](https://github.com/nodejs/node/commit/0cffa3c87e)] - **buffer**: coerce offset using Math.trunc() (cjihrig) [#9341](https://github.com/nodejs/node/pull/9341)
- [[`0276e9e82c`](https://github.com/nodejs/node/commit/0276e9e82c)] - **buffer**: coerce slice parameters consistently (Sakthipriyan Vairamani (thefourtheye)) [#9101](https://github.com/nodejs/node/pull/9101)
- [[`c1aee029d5`](https://github.com/nodejs/node/commit/c1aee029d5)] - **build**: start comments at beginning of line (Sakthipriyan Vairamani (thefourtheye)) [#9375](https://github.com/nodejs/node/pull/9375)
- [[`1ed58ed7fe`](https://github.com/nodejs/node/commit/1ed58ed7fe)] - **build**: reduce noise from doc target (Daniel Bevenius) [#9457](https://github.com/nodejs/node/pull/9457)
- [[`5c2ed2eefe`](https://github.com/nodejs/node/commit/5c2ed2eefe)] - **build**: make node-gyp output silent (Sakthipriyan Vairamani (thefourtheye)) [#8990](https://github.com/nodejs/node/pull/8990)
- [[`c857586fd7`](https://github.com/nodejs/node/commit/c857586fd7)] - **build**: use wxneeded on openbsd (Aaron Bieber) [#9232](https://github.com/nodejs/node/pull/9232)
- [[`9de8cfecd9`](https://github.com/nodejs/node/commit/9de8cfecd9)] - **build**: fix config.gypi target (Daniel Bevenius) [#9053](https://github.com/nodejs/node/pull/9053)
- [[`b867294aa0`](https://github.com/nodejs/node/commit/b867294aa0)] - **child_process**: remove unreachable execSync() code (cjihrig) [#9209](https://github.com/nodejs/node/pull/9209)
- [[`cbfde3cd0a`](https://github.com/nodejs/node/commit/cbfde3cd0a)] - **child_process**: update outdated comment (Tanuja-Sawant)
- [[`4d4d02ace4`](https://github.com/nodejs/node/commit/4d4d02ace4)] - **crypto**: fix faulty logic in iv size check (Ben Noordhuis) [#9032](https://github.com/nodejs/node/pull/9032)
- [[`9cfa91b585`](https://github.com/nodejs/node/commit/9cfa91b585)] - **crypto**: use SSL_get_SSL_CTX. (Adam Langley) [#8995](https://github.com/nodejs/node/pull/8995)
- [[`0d15ec82e3`](https://github.com/nodejs/node/commit/0d15ec82e3)] - **deps**: cherry pick 7166503 from upstream v8 (Cristian Cavalli) [#9173](https://github.com/nodejs/node/pull/9173)
- [[`f0a8bcc735`](https://github.com/nodejs/node/commit/f0a8bcc735)] - **deps**: back port OpenBSD fix in c-ares/c-ares (Aaron Bieber) [#9232](https://github.com/nodejs/node/pull/9232)
- [[`96e8e869c0`](https://github.com/nodejs/node/commit/96e8e869c0)] - **deps**: cherry-pick 6f68f30 from v8 upstream (Stefan Budeanu) [#9610](https://github.com/nodejs/node/pull/9610)
- [[`804b398239`](https://github.com/nodejs/node/commit/804b398239)] - **deps**: revert botched V8 backport (Myles Borins) [#9610](https://github.com/nodejs/node/pull/9610)
- [[`a8840bbbe4`](https://github.com/nodejs/node/commit/a8840bbbe4)] - **deps**: cherry-pick 3c39bac from V8 upstream (Cristian Cavalli) [#9138](https://github.com/nodejs/node/pull/9138)
- [[`bda45b510c`](https://github.com/nodejs/node/commit/bda45b510c)] - **deps**: backport 5c8cb16 from upstream V8 (Cristian Cavalli) [#9422](https://github.com/nodejs/node/pull/9422)
- [[`39b4a1ca9b`](https://github.com/nodejs/node/commit/39b4a1ca9b)] - **deps**: revert default gtest reporter change (Brian White) [#8948](https://github.com/nodejs/node/pull/8948)
- [[`2230c26c49`](https://github.com/nodejs/node/commit/2230c26c49)] - **deps**: upgrade npm to 3.10.9 (Kat Marchán) [#9286](https://github.com/nodejs/node/pull/9286)
- [[`0fcf249078`](https://github.com/nodejs/node/commit/0fcf249078)] - **deps**: cherry-pick bb4974d from v8 upstream (Matt Loring) [#9192](https://github.com/nodejs/node/pull/9192)
- [[`d926f16c52`](https://github.com/nodejs/node/commit/d926f16c52)] - **doc**: update minute-taking procedure for CTC (Rich Trott) [#9425](https://github.com/nodejs/node/pull/9425)
- [[`6fc0f1b99f`](https://github.com/nodejs/node/commit/6fc0f1b99f)] - **doc**: note that tests should include a description (Gibson Fahnestock) [#9415](https://github.com/nodejs/node/pull/9415)
- [[`d36c6f5e2e`](https://github.com/nodejs/node/commit/d36c6f5e2e)] - **doc**: update GOVERNANCE.md to use "meeting chair" (Rich Trott) [#9432](https://github.com/nodejs/node/pull/9432)
- [[`1726dc7f68`](https://github.com/nodejs/node/commit/1726dc7f68)] - **doc**: update Diagnostics WG info (Josh Gavant) [#9329](https://github.com/nodejs/node/pull/9329)
- [[`7b60288942`](https://github.com/nodejs/node/commit/7b60288942)] - **doc**: use 'an' over 'a', remove redundant sentence (Zeke Sikelianos) [#9345](https://github.com/nodejs/node/pull/9345)
- [[`6908bc4ed7`](https://github.com/nodejs/node/commit/6908bc4ed7)] - **doc**: add more internal links to fs.Stats object (Zeke Sikelianos) [#9345](https://github.com/nodejs/node/pull/9345)
- [[`5d971afc04`](https://github.com/nodejs/node/commit/5d971afc04)] - **doc**: fix outdate ninja link (Yangyang Liu) [#9278](https://github.com/nodejs/node/pull/9278)
- [[`c31fa2468f`](https://github.com/nodejs/node/commit/c31fa2468f)] - **doc**: fix broken links to Buffer.from(string) (Jesse McCarthy) [#9294](https://github.com/nodejs/node/pull/9294)
- [[`c379c29e1f`](https://github.com/nodejs/node/commit/c379c29e1f)] - **doc**: fs: fix link to mkdtemp (coderaiser) [#9379](https://github.com/nodejs/node/pull/9379)
- [[`7c90d9638a`](https://github.com/nodejs/node/commit/7c90d9638a)] - **doc**: update OpenSSL links (kobelb) [#9338](https://github.com/nodejs/node/pull/9338)
- [[`627c0cb3ee`](https://github.com/nodejs/node/commit/627c0cb3ee)] - **doc**: child_process .stdio accepts a String type (Kenneth Skovhus) [#9637](https://github.com/nodejs/node/pull/9637)
- [[`653f092639`](https://github.com/nodejs/node/commit/653f092639)] - **doc**: simplify process.memoryUsage() example code (Thomas Watson Steen) [#9560](https://github.com/nodejs/node/pull/9560)
- [[`d2b0caef33`](https://github.com/nodejs/node/commit/d2b0caef33)] - **doc**: update CONTRIBUTING.md to address editing PRs (Gibson Fahnestock) [#9259](https://github.com/nodejs/node/pull/9259)
- [[`eeaadcdd6a`](https://github.com/nodejs/node/commit/eeaadcdd6a)] - **doc**: add italoacasas to collaborators (Italo A. Casas) [#9677](https://github.com/nodejs/node/pull/9677)
- [[`adee93962a`](https://github.com/nodejs/node/commit/adee93962a)] - **doc**: more realistic custom inspect example (Ryan Scheel (Havvy)) [#8875](https://github.com/nodejs/node/pull/8875)
- [[`ae3ce7ff60`](https://github.com/nodejs/node/commit/ae3ce7ff60)] - **doc**: clarify buffer toString docs. (Olan Byrne) [#8984](https://github.com/nodejs/node/pull/8984)
- [[`a5860b4dbd`](https://github.com/nodejs/node/commit/a5860b4dbd)] - **doc**: clarify relation between a file and a module (marzelin) [#9026](https://github.com/nodejs/node/pull/9026)
- [[`6f212b910b`](https://github.com/nodejs/node/commit/6f212b910b)] - **doc**: mention case-insensitive env on windows (Oliver Salzburg) [#9166](https://github.com/nodejs/node/pull/9166)
- [[`ee01594d07`](https://github.com/nodejs/node/commit/ee01594d07)] - **doc**: fixes formatting in process (Rod Machen) [#9235](https://github.com/nodejs/node/pull/9235)
- [[`4f2523697c`](https://github.com/nodejs/node/commit/4f2523697c)] - **doc**: fix link to cli.md in vm.md (Daniel Bevenius) [#9481](https://github.com/nodejs/node/pull/9481)
- [[`1b792742e8`](https://github.com/nodejs/node/commit/1b792742e8)] - **doc**: add Sakthipriyan to the CTC (Rod Vagg) [#9427](https://github.com/nodejs/node/pull/9427)
- [[`4c4b0f7a0e`](https://github.com/nodejs/node/commit/4c4b0f7a0e)] - **doc**: add 2016-10-26 CTC meeting minutes (Rich Trott) [#9348](https://github.com/nodejs/node/pull/9348)
- [[`925a51b6a5`](https://github.com/nodejs/node/commit/925a51b6a5)] - **doc**: add 2016-10-05 CTC meeting minutes (Josh Gavant) [#9326](https://github.com/nodejs/node/pull/9326)
- [[`2a9fc7ccd3`](https://github.com/nodejs/node/commit/2a9fc7ccd3)] - **doc**: add 2016-09-28 CTC meeting minutes (Josh Gavant) [#9325](https://github.com/nodejs/node/pull/9325)
- [[`ae73ecbe3f`](https://github.com/nodejs/node/commit/ae73ecbe3f)] - **doc**: add 2016-10-19 CTC meeting minutes (Josh Gavant) [#9193](https://github.com/nodejs/node/pull/9193)
- [[`53de0c258f`](https://github.com/nodejs/node/commit/53de0c258f)] - **doc**: improve header styling for API docs (Jeremiah Senkpiel) [#8811](https://github.com/nodejs/node/pull/8811)
- [[`79e998abbb`](https://github.com/nodejs/node/commit/79e998abbb)] - **doc**: add CTC meeting minutes for 2016-10-12 (Michael Dawson) [#9070](https://github.com/nodejs/node/pull/9070)
- [[`3ee94f24a8`](https://github.com/nodejs/node/commit/3ee94f24a8)] - **doc**: remove confusing reference in governance doc (Rich Trott) [#9073](https://github.com/nodejs/node/pull/9073)
- [[`cfcf9481c7`](https://github.com/nodejs/node/commit/cfcf9481c7)] - **doc**: v6 is now LTS rather than Current (Jeremiah Senkpiel) [#9182](https://github.com/nodejs/node/pull/9182)
- [[`a03811508a`](https://github.com/nodejs/node/commit/a03811508a)] - **doc**: suggest nodejs/help for general support (Myles Borins) [#9128](https://github.com/nodejs/node/pull/9128)
- [[`e680ad552d`](https://github.com/nodejs/node/commit/e680ad552d)] - **doc**: fix header level for crypto.constants (Evan Lucas) [#9187](https://github.com/nodejs/node/pull/9187)
- [[`6c9a84b034`](https://github.com/nodejs/node/commit/6c9a84b034)] - **doc**: add ctc-review label information (Rich Trott) [#9072](https://github.com/nodejs/node/pull/9072)
- [[`bdd91e0d8e`](https://github.com/nodejs/node/commit/bdd91e0d8e)] - **doc**: fix typo in zlib.md (Parambir Singh) [#9123](https://github.com/nodejs/node/pull/9123)
- [[`fd006e5c46`](https://github.com/nodejs/node/commit/fd006e5c46)] - **doc**: further improve child_process doc types (Indrek Ardel) [#9095](https://github.com/nodejs/node/pull/9095)
- [[`e5777b344c`](https://github.com/nodejs/node/commit/e5777b344c)] - **doc**: edit Stream api grammar (Benji Marinacci) [#9100](https://github.com/nodejs/node/pull/9100)
- [[`2c5b27a247`](https://github.com/nodejs/node/commit/2c5b27a247)] - **doc**: improved example for http.get (marzelin) [#9065](https://github.com/nodejs/node/pull/9065)
- [[`de2f050ac3`](https://github.com/nodejs/node/commit/de2f050ac3)] - **doc**: update reference to list hash algorithms in crypto.md (scott stern) [#9043](https://github.com/nodejs/node/pull/9043)
- [[`b2a2a57836`](https://github.com/nodejs/node/commit/b2a2a57836)] - **doc**: specify that errno is a number, not a string (John Vilk) [#9007](https://github.com/nodejs/node/pull/9007)
- [[`0d21f951b2`](https://github.com/nodejs/node/commit/0d21f951b2)] - **doc**: highlight deprecated API in ToC (Ilya Frolov) [#7189](https://github.com/nodejs/node/pull/7189)
- [[`0a2a39cb95`](https://github.com/nodejs/node/commit/0a2a39cb95)] - **doc**: explains why Reviewed-By is added in PRs (jessicaquynh) [#9044](https://github.com/nodejs/node/pull/9044)
- [[`3af679ee36`](https://github.com/nodejs/node/commit/3af679ee36)] - **doc**: explain why GitHub merge button is not used (jessicaquynh) [#9044](https://github.com/nodejs/node/pull/9044)
- [[`c0f8198d64`](https://github.com/nodejs/node/commit/c0f8198d64)] - **doc**: fix typo (Nikolai Vavilov) [#9089](https://github.com/nodejs/node/pull/9089)
- [[`70eadea8e1`](https://github.com/nodejs/node/commit/70eadea8e1)] - **doc**: fix broken links in changelogs (Evan Lucas) [#8122](https://github.com/nodejs/node/pull/8122)
- [[`d3128996e0`](https://github.com/nodejs/node/commit/d3128996e0)] - **doc**: revise http documentation (Timothy Gu) [#8486](https://github.com/nodejs/node/pull/8486)
- [[`2ea5db92de`](https://github.com/nodejs/node/commit/2ea5db92de)] - **doc**: do not link in the headings (Sakthipriyan Vairamani (thefourtheye)) [#9416](https://github.com/nodejs/node/pull/9416)
- [[`ec90f73e64`](https://github.com/nodejs/node/commit/ec90f73e64)] - **doc**: reference signal(7) for the list of signals (Emanuele DelBono) [#9323](https://github.com/nodejs/node/pull/9323)
- [[`638ef09455`](https://github.com/nodejs/node/commit/638ef09455)] - **doc**: fix typo in http.md (anu0012) [#9144](https://github.com/nodejs/node/pull/9144)
- [[`4141c77a25`](https://github.com/nodejs/node/commit/4141c77a25)] - **gitignore**: ignore all tap files (Johan Bergström) [#9262](https://github.com/nodejs/node/pull/9262)
- [[`847b15c177`](https://github.com/nodejs/node/commit/847b15c177)] - **governance**: expand use of CTC issue tracker (Rich Trott) [#8945](https://github.com/nodejs/node/pull/8945)
- [[`575fc4eca0`](https://github.com/nodejs/node/commit/575fc4eca0)] - **gtest**: output tap comments as yamlish (Johan Bergström) [#9262](https://github.com/nodejs/node/pull/9262)
- [[`cf5a00e904`](https://github.com/nodejs/node/commit/cf5a00e904)] - **inspector**: do not prompt to use localhost (Eugene Ostroukhov) [#9451](https://github.com/nodejs/node/pull/9451)
- [[`b5bcd25c7b`](https://github.com/nodejs/node/commit/b5bcd25c7b)] - **inspector**: fix request path nullptr dereference (Ben Noordhuis) [#9184](https://github.com/nodejs/node/pull/9184)
- [[`b3f8f8902d`](https://github.com/nodejs/node/commit/b3f8f8902d)] - **inspector**: no URLs when the debugger is connected (Eugene Ostroukhov) [#8919](https://github.com/nodejs/node/pull/8919)
- [[`a178abfae6`](https://github.com/nodejs/node/commit/a178abfae6)] - **lib**: change == to === in linkedlist (jedireza) [#9362](https://github.com/nodejs/node/pull/9362)
- [[`5efb3c373a`](https://github.com/nodejs/node/commit/5efb3c373a)] - **lib**: fix beforeExit not working with -e (Ben Noordhuis) [#8821](https://github.com/nodejs/node/pull/8821)
- [[`0f1a22d28a`](https://github.com/nodejs/node/commit/0f1a22d28a)] - **net**: fix ambiguity in EOF handling (Fedor Indutny) [#9066](https://github.com/nodejs/node/pull/9066)
- [[`58b60fc79d`](https://github.com/nodejs/node/commit/58b60fc79d)] - **repl**: don’t write to input stream in editor mode (Anna Henningsen) [#9207](https://github.com/nodejs/node/pull/9207)
- [[`ed3de0854e`](https://github.com/nodejs/node/commit/ed3de0854e)] - **repl**: make `key` of `repl.write()` optional always (Anna Henningsen) [#9207](https://github.com/nodejs/node/pull/9207)
- [[`8a91616ba9`](https://github.com/nodejs/node/commit/8a91616ba9)] - **src**: replace SetNamedPropertyHandler() (AnnaMag) [#9062](https://github.com/nodejs/node/pull/9062)
- [[`89eb175c89`](https://github.com/nodejs/node/commit/89eb175c89)] - **src**: remove unused function (Brian White) [#9243](https://github.com/nodejs/node/pull/9243)
- [[`0e37a6a2ce`](https://github.com/nodejs/node/commit/0e37a6a2ce)] - **src**: fix typo rval to value (Miguel Angel Asencio Hurtado) [#9023](https://github.com/nodejs/node/pull/9023)
- [[`59d8255b52`](https://github.com/nodejs/node/commit/59d8255b52)] - **test**: remove watchdog in test-debug-signal-cluster (Rich Trott) [#9476](https://github.com/nodejs/node/pull/9476)
- [[`24fc1e24ac`](https://github.com/nodejs/node/commit/24fc1e24ac)] - **test**: cleanup test-dgram-error-message-address (Michael Macherey) [#8938](https://github.com/nodejs/node/pull/8938)
- [[`0216dbe293`](https://github.com/nodejs/node/commit/0216dbe293)] - **test**: remove timers from streams test (Anna Henningsen)
- [[`4ccdbb27c5`](https://github.com/nodejs/node/commit/4ccdbb27c5)] - **test**: improve test-debugger-util-regression (Santiago Gimeno) [#9490](https://github.com/nodejs/node/pull/9490)
- [[`093d677252`](https://github.com/nodejs/node/commit/093d677252)] - **test**: fix flaky test-net-GH-5504 (Santiago Gimeno) [#9461](https://github.com/nodejs/node/pull/9461)
- [[`aaf783443b`](https://github.com/nodejs/node/commit/aaf783443b)] - **test**: fix flaky test-force-repl-with-eval (Santiago Gimeno) [#9460](https://github.com/nodejs/node/pull/9460)
- [[`b91d5e10f5`](https://github.com/nodejs/node/commit/b91d5e10f5)] - **test**: update http-header-obstext (Gibson Fahnestock) [#9415](https://github.com/nodejs/node/pull/9415)
- [[`259b94202a`](https://github.com/nodejs/node/commit/259b94202a)] - **test**: move timer-dependent test to sequential (Rich Trott) [#9431](https://github.com/nodejs/node/pull/9431)
- [[`54def06d73`](https://github.com/nodejs/node/commit/54def06d73)] - **test**: add test for HTTP client "aborted" event (Kyle E. Mitchell) [#7376](https://github.com/nodejs/node/pull/7376)
- [[`2c056a40c7`](https://github.com/nodejs/node/commit/2c056a40c7)] - **test**: remove timer in test-dgram-send-empty-array (Rich Trott) [#9361](https://github.com/nodejs/node/pull/9361)
- [[`5e1fd2822e`](https://github.com/nodejs/node/commit/5e1fd2822e)] - **test**: refactor test-http-client-readable (Rich Trott) [#9344](https://github.com/nodejs/node/pull/9344)
- [[`bec1ccae99`](https://github.com/nodejs/node/commit/bec1ccae99)] - **test**: clean up dgram-broadcast-multi-process test (Isobel Redelmeier) [#9308](https://github.com/nodejs/node/pull/9308)
- [[`ce05b70595`](https://github.com/nodejs/node/commit/ce05b70595)] - **test**: fix freebsd10-64 CI failures (Rich Trott) [#9317](https://github.com/nodejs/node/pull/9317)
- [[`8b2b08a636`](https://github.com/nodejs/node/commit/8b2b08a636)] - **test**: fix flaky test-fs-watch-recursive on OS X (Rich Trott) [#9303](https://github.com/nodejs/node/pull/9303)
- [[`4ef7f00e2d`](https://github.com/nodejs/node/commit/4ef7f00e2d)] - **test**: refactor test-async-wrap-check-providers (Gerges Beshay) [#9297](https://github.com/nodejs/node/pull/9297)
- [[`4fcc2c1d3b`](https://github.com/nodejs/node/commit/4fcc2c1d3b)] - **test**: run all of test-timers-blocking-callback (Rich Trott) [#9305](https://github.com/nodejs/node/pull/9305)
- [[`1d54f07b31`](https://github.com/nodejs/node/commit/1d54f07b31)] - **test**: refactor /parallel/test-cluster-uncaught-exception.js to ES6 (Deverick) [#9239](https://github.com/nodejs/node/pull/9239)
- [[`88e60c2124`](https://github.com/nodejs/node/commit/88e60c2124)] - **test**: use strict assertions in module loader test (Ben Noordhuis) [#9263](https://github.com/nodejs/node/pull/9263)
- [[`0c32b03bdc`](https://github.com/nodejs/node/commit/0c32b03bdc)] - **test**: remove err timer from test-http-set-timeout (BethGriggs) [#9264](https://github.com/nodejs/node/pull/9264)
- [[`8d985c293c`](https://github.com/nodejs/node/commit/8d985c293c)] - **test**: clean up `test-child-process-exec-cwd.js` (Jeena Lee) [#9231](https://github.com/nodejs/node/pull/9231)
- [[`b83b5176d4`](https://github.com/nodejs/node/commit/b83b5176d4)] - **test**: add child_process.exec() timeout coverage (cjihrig) [#9208](https://github.com/nodejs/node/pull/9208)
- [[`0fdfba8fbe`](https://github.com/nodejs/node/commit/0fdfba8fbe)] - **test**: fix flaky test by removing timer (Evan Lucas) [#9199](https://github.com/nodejs/node/pull/9199)
- [[`ad4cc361dd`](https://github.com/nodejs/node/commit/ad4cc361dd)] - **test**: add coverage for execFileSync() errors (cjihrig) [#9211](https://github.com/nodejs/node/pull/9211)
- [[`ef1cf6b040`](https://github.com/nodejs/node/commit/ef1cf6b040)] - **test**: remove test-v8-inspector-json-protocol test (Ben Noordhuis) [#9184](https://github.com/nodejs/node/pull/9184)
- [[`1fee6c11e5`](https://github.com/nodejs/node/commit/1fee6c11e5)] - **test**: writable stream needDrain state (Italo A. Casas) [#8799](https://github.com/nodejs/node/pull/8799)
- [[`7fbfb739c1`](https://github.com/nodejs/node/commit/7fbfb739c1)] - **test**: writable stream ending state (Italo A. Casas) [#8707](https://github.com/nodejs/node/pull/8707)
- [[`f64d93f198`](https://github.com/nodejs/node/commit/f64d93f198)] - **test**: writable stream finished state (Italo A. Casas) [#8791](https://github.com/nodejs/node/pull/8791)
- [[`210ae5607c`](https://github.com/nodejs/node/commit/210ae5607c)] - **test**: prevent workers outliving parent (Sam Roberts) [#9257](https://github.com/nodejs/node/pull/9257)
- [[`1d79af6525`](https://github.com/nodejs/node/commit/1d79af6525)] - **test**: case sensitivity of env variables (Oliver Salzburg) [#9166](https://github.com/nodejs/node/pull/9166)
- [[`18a235b9a7`](https://github.com/nodejs/node/commit/18a235b9a7)] - **test**: make flaky pummel test more reliable (Ben Noordhuis) [#9241](https://github.com/nodejs/node/pull/9241)
- [[`a46c02746a`](https://github.com/nodejs/node/commit/a46c02746a)] - **test**: move flaky test to test/pummel (Ben Noordhuis) [#9241](https://github.com/nodejs/node/pull/9241)
- [[`60704fbb20`](https://github.com/nodejs/node/commit/60704fbb20)] - **test**: fix flaky test-timers-blocking-callback (Rich Trott) [#9198](https://github.com/nodejs/node/pull/9198)
- [[`ce2d434ab6`](https://github.com/nodejs/node/commit/ce2d434ab6)] - **test**: remove arbitrary timer (Rich Trott) [#9197](https://github.com/nodejs/node/pull/9197)
- [[`5c42d98bbd`](https://github.com/nodejs/node/commit/5c42d98bbd)] - **test**: remove duplicate required module (Rich Trott) [#9169](https://github.com/nodejs/node/pull/9169)
- [[`88cd4cfcb0`](https://github.com/nodejs/node/commit/88cd4cfcb0)] - **test**: rename target to exports for consistency (Daniel Bevenius) [#9135](https://github.com/nodejs/node/pull/9135)
- [[`02f7e3aca3`](https://github.com/nodejs/node/commit/02f7e3aca3)] - **test**: checking if error constructor is assert.AssertionError (larissayvette) [#9119](https://github.com/nodejs/node/pull/9119)
- [[`6f780893eb`](https://github.com/nodejs/node/commit/6f780893eb)] - **test**: fix flaky test-child-process-fork-dgram (Rich Trott) [#9098](https://github.com/nodejs/node/pull/9098)
- [[`39a53a0f29`](https://github.com/nodejs/node/commit/39a53a0f29)] - **test**: remove unneeded escaping in template strings (Rich Trott) [#9112](https://github.com/nodejs/node/pull/9112)
- [[`127ed73f3c`](https://github.com/nodejs/node/commit/127ed73f3c)] - **test**: remove unused common.libDir (Rich Trott) [#9124](https://github.com/nodejs/node/pull/9124)
- [[`def6874b5f`](https://github.com/nodejs/node/commit/def6874b5f)] - **test**: use npm sandbox in test-npm-install (João Reis) [#9079](https://github.com/nodejs/node/pull/9079)
- [[`97748c6d02`](https://github.com/nodejs/node/commit/97748c6d02)] - **test**: move module out of fixture directory (Rich Trott) [#9022](https://github.com/nodejs/node/pull/9022)
- [[`ae3f31b267`](https://github.com/nodejs/node/commit/ae3f31b267)] - **test**: fix issues reported by Coverity (Eugene Ostroukhov) [#8870](https://github.com/nodejs/node/pull/8870)
- [[`9cc9001244`](https://github.com/nodejs/node/commit/9cc9001244)] - **test**: refactor test-file-\* (Jenna Vuong) [#8999](https://github.com/nodejs/node/pull/8999)
- [[`cc6b2f49cf`](https://github.com/nodejs/node/commit/cc6b2f49cf)] - **test**: fixes that do not affect performance (larissayvette) [#9011](https://github.com/nodejs/node/pull/9011)
- [[`a643d3caed`](https://github.com/nodejs/node/commit/a643d3caed)] - **test**: output tap13 instead of almost-tap (Johan Bergström) [#9262](https://github.com/nodejs/node/pull/9262)
- [[`7b75cb9e5a`](https://github.com/nodejs/node/commit/7b75cb9e5a)] - **test,lib,benchmark**: match function names (Rich Trott) [#9113](https://github.com/nodejs/node/pull/9113)
- [[`9cb236ff45`](https://github.com/nodejs/node/commit/9cb236ff45)] - **tls**: fix leak of WriteWrap+TLSWrap combination (Fedor Indutny) [#9586](https://github.com/nodejs/node/pull/9586)
- [[`bd7c1e7542`](https://github.com/nodejs/node/commit/bd7c1e7542)] - **tools**: allow test.py to use full paths of tests (Francis Gulotta) [#9694](https://github.com/nodejs/node/pull/9694)
- [[`2388648bea`](https://github.com/nodejs/node/commit/2388648bea)] - **tools**: make --repeat work with -j in test.py (Rich Trott) [#9249](https://github.com/nodejs/node/pull/9249)
- [[`07d34f98b2`](https://github.com/nodejs/node/commit/07d34f98b2)] - **tools**: remove dangling eslint symlink (Sam Roberts) [#9299](https://github.com/nodejs/node/pull/9299)
- [[`a120199ea9`](https://github.com/nodejs/node/commit/a120199ea9)] - **tools**: enable ES2016 syntax support in ESLint (Michaël Zasso) [#9218](https://github.com/nodejs/node/pull/9218)
- [[`9077f63dcf`](https://github.com/nodejs/node/commit/9077f63dcf)] - **tools**: replace custom lint rule for getter/setter (Rich Trott) [#9194](https://github.com/nodejs/node/pull/9194)
- [[`e9d5cd79bb`](https://github.com/nodejs/node/commit/e9d5cd79bb)] - **tools**: update ESLint to v3.8.0 (Rich Trott) [#9112](https://github.com/nodejs/node/pull/9112)
- [[`87285ed984`](https://github.com/nodejs/node/commit/87285ed984)] - **tools**: avoid let in for loops (jessicaquynh) [#9049](https://github.com/nodejs/node/pull/9049)
- [[`e2bb2a2550`](https://github.com/nodejs/node/commit/e2bb2a2550)] - **tools**: fix release script on macOS 10.12 (Evan Lucas) [#8824](https://github.com/nodejs/node/pull/8824)
- [[`8b85d47112`](https://github.com/nodejs/node/commit/8b85d47112)] - **tools**: use long format for gpg fingerprint (Myles Borins) [#9258](https://github.com/nodejs/node/pull/9258)
- [[`52a04bbfe2`](https://github.com/nodejs/node/commit/52a04bbfe2)] - **util**: use template strings (Alejandro Oviedo Garcia) [#9120](https://github.com/nodejs/node/pull/9120)
- [[`7dc875c08a`](https://github.com/nodejs/node/commit/7dc875c08a)] - **v8**: update make-v8.sh to use git (Jaideep Bajwa) [#9393](https://github.com/nodejs/node/pull/9393)

Windows 32-bit Installer: https://nodejs.org/dist/v6.9.2/node-v6.9.2-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v6.9.2/node-v6.9.2-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v6.9.2/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v6.9.2/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v6.9.2/node-v6.9.2.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v6.9.2/node-v6.9.2-darwin-x64.tar.gz \
Linux 32-bit Binary: https://nodejs.org/dist/v6.9.2/node-v6.9.2-linux-x86.tar.xz \
Linux 64-bit Binary: https://nodejs.org/dist/v6.9.2/node-v6.9.2-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v6.9.2/node-v6.9.2-linux-ppc64le.tar.xz \
Linux PPC BE 64-bit Binary: https://nodejs.org/dist/v6.9.2/node-v6.9.2-linux-ppc64.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v6.9.2/node-v6.9.2-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v6.9.2/node-v6.9.2-aix-ppc64.tar.gz \
SmartOS 32-bit Binary: https://nodejs.org/dist/v6.9.2/node-v6.9.2-sunos-x86.tar.xz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v6.9.2/node-v6.9.2-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v6.9.2/node-v6.9.2-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v6.9.2/node-v6.9.2-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v6.9.2/node-v6.9.2-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v6.9.2/node-v6.9.2.tar.gz \
Other release files: https://nodejs.org/dist/v6.9.2/ \
Documentation: https://nodejs.org/docs/v6.9.2/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

0e4df135bca73e05aa0c3d71684833b683b6b68b6a14c48f05e161fdd79bd697  node-v6.9.2-aix-ppc64.tar.gz
8a66fbd5dfee4c8912459bd87daf0d95b11d2a5ff459ecf5fd9d75056010de0e  node-v6.9.2-darwin-x64.tar.gz
e14b5d06ad16c10fb2ff2e0e6bc894d511d70ad3121c1815a241b10295518747  node-v6.9.2-darwin-x64.tar.xz
4bea6488d6c4b4c883cbffafb632fcc5636a8a949e7244438c6d9883a35e1741  node-v6.9.2-headers.tar.gz
f71a136d3463143171d6dc7e12cc88306e30d849b5cd7570a31daf02260d16c4  node-v6.9.2-headers.tar.xz
05d00c80967e2765eb3edd2bbbe7410c1153323c0dcfe7c7d12c9cad0b32c587  node-v6.9.2-linux-arm64.tar.gz
40fe68bcd70263e0163d2668b0b8a27b28427ab608bd3666a771de8902dd0ce7  node-v6.9.2-linux-arm64.tar.xz
d62c6131c329a8efdd7ae6f75ec3a6655480673f8ef65e1518d44c59485cc712  node-v6.9.2-linux-armv6l.tar.gz
d6ea6851c7ca80ecd2619eb7100040f114d16461d705cc0a5f1367d4c1428126  node-v6.9.2-linux-armv6l.tar.xz
187106e585f2c7dacbca2ad4419d383f824e0d18665b14d939fd412030e616df  node-v6.9.2-linux-armv7l.tar.gz
99a630fe3df76876f6e9a172f230a2013adfdb20e69754facd1fea8a364d5062  node-v6.9.2-linux-armv7l.tar.xz
f4b736390a945b68829031fbe5ae1373c73ffb3adb2e461481239334f98ca7cd  node-v6.9.2-linux-ppc64le.tar.gz
e9cc104fc35edd066bb8805190e6b23670379dcae2fc06b637f1313a9ce1ee88  node-v6.9.2-linux-ppc64le.tar.xz
e6302d48cc8c32ce5cab99372d02a2289fc28b2a6634fabcf3b271b57fcc4f8b  node-v6.9.2-linux-ppc64.tar.gz
89fa91d1b3674f8f777612e8b156a8b9086fdd282199ba550682f0bd79ec44e1  node-v6.9.2-linux-ppc64.tar.xz
e03d75506292d6713e90331297ed2e02442b236925c5ddc77413d688b62b5538  node-v6.9.2-linux-s390x.tar.gz
3bbac1aa269adebe8003adc8ec5664d4ee432953455bc622f4783d274950eb77  node-v6.9.2-linux-s390x.tar.xz
cbf6a35b035c56f991c2e6a4aedbcd9f09555234ac0dd5b2c15128e2b5f4eb50  node-v6.9.2-linux-x64.tar.gz
da766edda11cc38eefb1ce29683f248f40c997c0ee2e06903b01429b4c94b71a  node-v6.9.2-linux-x64.tar.xz
9794a5af57f408635b4215ede49b52993ef6ac3fd33ed5188b05082455d5a439  node-v6.9.2-linux-x86.tar.gz
9dae6ddbafcefd271c3df6e01633422dc7495479269fb1358e4c540929ef8835  node-v6.9.2-linux-x86.tar.xz
4e1d594053e12bc4862d838e97669434d299eb08ba7b50f00cc0a6860afe558f  node-v6.9.2.pkg
3306791cb1e1745084cef846e4e2f0d8751e29bc67d5fa3ef661661736fa1e71  node-v6.9.2-sunos-x64.tar.gz
4cb6c4ee390de75338662eb5cd03b8e2efdc5dc4b4ce8598d8383d06afdd278e  node-v6.9.2-sunos-x64.tar.xz
2f49758d6f0c868183924d925164530cd62a280ad291b683da1a9c41f757cf06  node-v6.9.2-sunos-x86.tar.gz
fb75c64971c4c138a6d079063385aa548468bcd000e4eeffd22900e4e1930121  node-v6.9.2-sunos-x86.tar.xz
997121460f3b4757907c2d7ff68ebdbf87af92b85bf2d07db5a7cb7aa5dae7d9  node-v6.9.2.tar.gz
f41b320b37ce23a34dbc597040e71535ea4f6baa2342bc526bb45f5f9aa9c9fb  node-v6.9.2.tar.xz
162cd0313718a181700b4fa20b475fd1e14d084d09192dde81bf1a3cdf3dd886  node-v6.9.2-win-x64.7z
3525201f28c2298953c4e0b03fe4fb080bf295ec9a722af2abbaa4ad53d3b491  node-v6.9.2-win-x64.zip
dfad2301e26c7eaeeb688d5b7791148d6e6b0559efc1a9af98f675c8686e7671  node-v6.9.2-win-x86.7z
9738fa999cf3cf181a820189c8397197ffc3e1dd02fbbba44841629dcced77e5  node-v6.9.2-win-x86.zip
9b2fcdd0d81e69a9764c3ce5a33087e02e94e8e23ea2b8c9efceebe79d49936e  node-v6.9.2-x64.msi
f8b911a249d45358464135c41e7b16fe4abef8d047efb6183f043bc965632aea  node-v6.9.2-x86.msi
b1a6014b15f632036c03b8ee3425d327b9e5571e85cfded74d9028444ab307b2  win-x64/node.exe
021ec2310f1c63e82b2ef92597319097a93e14425bdc5c5e5bc4c65ff6e1dea6  win-x64/node.lib
a87695c48265cadd47d10839b851a13a88edab17d70a1c7fdbf5d45f495ed415  win-x64/node_pdb.7z
7adc38d0d34bfb2c62a4176814cff7d6928a18ae522b6845e0277f02d07d0704  win-x64/node_pdb.zip
e4820919a176c0f121c4570d4cdd54c7886913f55e733d56080ae9a998368a74  win-x86/node.exe
a3d950ac9680cf74f4ba45f653f41b3fa5dce693ec9be9223ed1099f8a97fa58  win-x86/node.lib
db8aa9f25d9b36ddd4b8b1857b66dee59e714c53f9625eabd1ec947e0b109f79  win-x86/node_pdb.7z
caff2db8611f2092cfcc107f3b4e6a93a77de7829384c9e2977c01a530039a3a  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQEcBAEBCAAGBQJYbosfAAoJEJM7AfQLXKlGtQMH/3xZcjqSpB5W/GuKsa2zU/X1
rFNJvroPH6a4hwcYt70o/5rnF7WHMg3x9SCDIOnIQPvYMvNtnYMSYOCyFjeiuqu1
Q2kD2jRWi24X1WEUok7fG5TdFSkNd4cJHSUOiMzagcx7x539zpDuAKg8jk3bFn2c
QKnFX1F1F6rM33CoxPr7X95433ZO/Qt1ZBo7bXGLMvcHFg7xkGZTtW6jPYC+hPj1
63sGe21KeyyvFjqVCsNzSlVLDipVFOpN/EE8G2Wg9HNl6NptvE3PiWmDbO7il+kN
x440CnHH9wimE3C1+Xm/rWy9JRZVd4dZUWzfN0s+UkWjPToHed9Ff9j73ud5mag=
=M70i
-----END PGP SIGNATURE-----

```
