---
date: '2022-05-04T17:12:06.533Z'
category: release
title: Node v14.19.2 (LTS)
layout: blog-post
author: <PERSON> and <PERSON>
---

### Notable Changes

**doc:**

- New release key for <PERSON> English

Learn more at: <https://github.com/nodejs/node/pull/42102>
Contributed by <PERSON> (@bengl)

**npm:**

- Upgrade `npm` to `v6.14.17`.

Learn more at: <https://github.com/nodejs/node/pull/42900>
Contributed by <PERSON><PERSON> (@ruyadorno)

**V8:**

- V8 had a stack overflow issue affecting the `vm` module, cherry-picking [`cc9a8a37445e`](https://github.com/v8/v8/commit/cc9a8a37445eeffff17474020bb6038c2f9af9fc)
  from V8 solves this issue.

Learn more at: <https://github.com/nodejs/node/pull/41826>
Contributed by <PERSON> (@devsnek)

- Using `getHeapSnapshot()` was causing a Node.js crash due a V8 issue, this is fixed by backporting [`367b0c1e7a32`](https://github.com/v8/v8/commit/367b0c1e7a323deafeab56736b01bc7e14fc1998)
  from V8.

Learn more at: <https://github.com/nodejs/node/pull/42637>
Contributed by Chengzhong Wu (@legendecas)

### Commits

- \[[`c73ac527d6`](https://github.com/nodejs/node/commit/c73ac527d6)] - **build**: set DESTCPU correctly for 'make binary' on Apple Silicon (Chris Heisterkamp) [#40147](https://github.com/nodejs/node/pull/40147)
- \[[`dcaed6db24`](https://github.com/nodejs/node/commit/dcaed6db24)] - **build**: use ccache in make-v8.sh on ppc64le and s390x (Richard Lau) [#42204](https://github.com/nodejs/node/pull/42204)
- \[[`4203d132b1`](https://github.com/nodejs/node/commit/4203d132b1)] - **child_process**: queue pending messages (Erick Wendel) [#41221](https://github.com/nodejs/node/pull/41221)
- \[[`a3ebdbfe8f`](https://github.com/nodejs/node/commit/a3ebdbfe8f)] - **deps**: upgrade npm to 6.14.17 (Ruy Adorno) [#42900](https://github.com/nodejs/node/pull/42900)
- \[[`39e44f8382`](https://github.com/nodejs/node/commit/39e44f8382)] - **deps**: V8: cherry-pick cc9a8a37445e (Gus Caplan) [#41826](https://github.com/nodejs/node/pull/41826)
- \[[`b52a268b6f`](https://github.com/nodejs/node/commit/b52a268b6f)] - **deps**: V8: cherry-pick 367b0c1e7a32 (legendecas) [#42637](https://github.com/nodejs/node/pull/42637)
- \[[`77ba012065`](https://github.com/nodejs/node/commit/77ba012065)] - **doc**: fix documentation of `FileHandle.prototype.appendFile` (Antoine du Hamel) [#42588](https://github.com/nodejs/node/pull/42588)
- \[[`3d3d7ed1b7`](https://github.com/nodejs/node/commit/3d3d7ed1b7)] - **doc**: specify flag needed for JSON and Wasm modules (Rich Trott) [#42736](https://github.com/nodejs/node/pull/42736)
- \[[`542d812c93`](https://github.com/nodejs/node/commit/542d812c93)] - **doc**: use openpgp.org for keyserver examples (Nick Schonning) [#39227](https://github.com/nodejs/node/pull/39227)
- \[[`7f2825b1a9`](https://github.com/nodejs/node/commit/7f2825b1a9)] - **doc**: add release key for Bryan English (Bryan English) [#42102](https://github.com/nodejs/node/pull/42102)
- \[[`75302d3dce`](https://github.com/nodejs/node/commit/75302d3dce)] - **fs**: fix write methods param validation and docs (Livia Medeiros) [#41677](https://github.com/nodejs/node/pull/41677)
- \[[`d4171e0eac`](https://github.com/nodejs/node/commit/d4171e0eac)] - **stream**: resume stream on drain (Robert Nagy) [#41848](https://github.com/nodejs/node/pull/41848)
- \[[`de474c8b6f`](https://github.com/nodejs/node/commit/de474c8b6f)] - **worker**: do not send message if port is closing (Rich Trott) [#42357](https://github.com/nodejs/node/pull/42357)

Windows 32-bit Installer: https://nodejs.org/dist/v14.19.2/node-v14.19.2-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v14.19.2/node-v14.19.2-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v14.19.2/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v14.19.2/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v14.19.2/node-v14.19.2.pkg \
macOS Intel 64-bit Binary: https://nodejs.org/dist/v14.19.2/node-v14.19.2-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v14.19.2/node-v14.19.2-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v14.19.2/node-v14.19.2-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v14.19.2/node-v14.19.2-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v14.19.2/node-v14.19.2-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v14.19.2/node-v14.19.2-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v14.19.2/node-v14.19.2-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v14.19.2/node-v14.19.2.tar.gz \
Other release files: https://nodejs.org/dist/v14.19.2/ \
Documentation: https://nodejs.org/docs/v14.19.2/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

519f8a87e6181682c4953554262713d9f2632eedb3b149ee2b5189622f6a7139  node-v14.19.2-aix-ppc64.tar.gz
1e80fca29e6876c0312bec825d99a90a562b5501c4d25bf081665b6433c30abf  node-v14.19.2-darwin-x64.tar.gz
62b1ddde13cffe288f97faac6b7712b4b113edc4ec89cf316b7a7489e736b937  node-v14.19.2-darwin-x64.tar.xz
aff0400e36d90d4b6e807e4b063197ff160c72c96097f4cd3d6a6849e449922c  node-v14.19.2-headers.tar.gz
b6d4d705f7a404ae173ad74c46ee4f28c4a0a97fe42f856eda98f647b1caa248  node-v14.19.2-headers.tar.xz
b972847ccd8a699b72f8ac455d4233fa584972e2ebd3dd99768ff5c95334304d  node-v14.19.2-linux-arm64.tar.gz
9497c94de210cc20cee64c94fc63c3fd7939dd988dd32f3f818f5ecf0c07675f  node-v14.19.2-linux-arm64.tar.xz
f7163f54896ac913a4cf461526455e72ed7c18b6b830e8d9315bfdc64db9dab7  node-v14.19.2-linux-armv7l.tar.gz
cf5f3ecfac934ae889eac8331750e0bfda5d25c7290194c90e09437091af2ec2  node-v14.19.2-linux-armv7l.tar.xz
0abc3a857a0df1eb3b34e81edc290ca24fe9c680d88eca19fd211e51d4828854  node-v14.19.2-linux-ppc64le.tar.gz
e2878491d7bce357a5f33a6a8ae23c19c5937437d8c8b5472fe966d7f8fc27c9  node-v14.19.2-linux-ppc64le.tar.xz
3535d8aab159b1def2610c0efe62fc6290ab5be8fa4cd8c50987b731c13a8233  node-v14.19.2-linux-s390x.tar.gz
b64a7a5a7da8a0f42c84a2f4e4d9a82bc2d0aeec3b1d54d15eef68ec4f78e8bc  node-v14.19.2-linux-s390x.tar.xz
fd72086a1849a428c99d94ef1aca94686c9080792e1586a75ca031a030424544  node-v14.19.2-linux-x64.tar.gz
3f7aaf28ff24f417fc7540469bd076668936b87fc9450c39e877ae4be374affb  node-v14.19.2-linux-x64.tar.xz
e5c0847cc3ace6351f13440fa5a29c22fc0079bbf795faef67402cad085a9463  node-v14.19.2.pkg
db05c50e18e35faa344cdb86e14b0364860da80a51d56d33895a78cb6010baae  node-v14.19.2.tar.gz
ef4375a9152ff69f2823d7b20a3b53767a046164bbac7824429cb216d1688cf0  node-v14.19.2.tar.xz
64f34f258f0c764bcac7967914451a9b1178752114365132eb4fb5a611a849c8  node-v14.19.2-win-x64.7z
073c6e262cad15a7e2707692d0e4fb8f0083d7bda6964375312be78fa231f563  node-v14.19.2-win-x64.zip
b5d391fd939a2cea2fc5ad20619dff8efb86dbcd90d289a08c4cf0fe823abf5c  node-v14.19.2-win-x86.7z
62fa7e53d6d45aa6f16ab94b1364160e5690ba2690c9524e8c729c8230a75096  node-v14.19.2-win-x86.zip
23b976cbbc393e0d7a1b6ecb78c1c37a4716cf0c1d1367a801335a1d926c5923  node-v14.19.2-x64.msi
8454ce9384546962d6d7a6f2e7032d4ac0381c85306d195719d00479d19d2716  node-v14.19.2-x86.msi
3ffef39d5138a6643c2a50c6c89ba25d9d1327b22038fe12e4dc5e8015ce2369  win-x64/node.exe
862b89b100b71645d3e015616e1c58ab4a44ba002cf65bd44a4908ef8545b210  win-x64/node.lib
d4ecd77c76234569ab2769871779d73b4fad0a8bd971730183ace327ce4a4962  win-x64/node_pdb.7z
0b39aa14e1942815806e6234a773cb13bdcb4377474c2d2ae8e55bdbdfd6b2a4  win-x64/node_pdb.zip
9946e7321249b0fe2460314a607858ca45b7229e21e625868090202424ed6a26  win-x86/node.exe
57dfb2aa3b31658701d720bf1a191ec94b80705e99f5425bbd31ae93c432e64c  win-x86/node.lib
4e6a02c2bf16e45f0c6748e6ab57b003433a5cd937418d3f2b5074feb9a16c52  win-x86/node_pdb.7z
94ccca4eb2e662eec7d84a993ea5cdbe67f2db4ddd50b49eab87b1ab3b71a6d4  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQEzBAEBCAAdFiEETtd49TnjY0x3nIfG1wYoSKGrAFwFAmJys1EACgkQ1wYoSKGr
AFwvqgf/QirrTXlarhpWKIKSx5xDeiA5udHkiblOhFffmMHRw6ldBHs31utjuxFY
kilBf1h1q/PQj7K/OTFBz5AijjhEi2dVTj9RGFCr8zNsO2aUXwEsNa0g+0ZNrIAt
TLZcCXnsfheww3cXARypiaXkE7yS1r1CLcvL2Tq27F3cdqqJtLWjWyt59XQ41Ik5
ojduGpS/V4DuYbWvCLV9NMq5+g0vs2Q1t2RsK2cd88mVuh5mN12CRhLkf3o+DDe7
gGiki4DaJVNvqJobwlxbACu4HPVnD0VpbbKVXNxWq5aDgjHO0fHKnw5PJXSc/0CD
wbeWWuZVWOgZ50WF0g3XBqekFJCzgg==
=trIR
-----END PGP SIGNATURE-----

```
