---
date: '2018-11-20T22:45:31.634Z'
category: release
title: Node v8.13.0 (LTS)
layout: blog-post
author: <PERSON><PERSON>
---

### Notable changes

- **assert**:
  - backport some assert commits (<PERSON><PERSON>) [#23223](https://github.com/nodejs/node/pull/23223)
- **deps**:
  - upgrade to libuv 1.23.2 (c<PERSON><PERSON><PERSON>) [#23336](https://github.com/nodejs/node/pull/23336)
  - V8: cherry-pick 64-bit hash seed commits (<PERSON>) [#23274](https://github.com/nodejs/node/pull/23274)
- **http**:
  - added aborted property to request (<PERSON>) [#20094](https://github.com/nodejs/node/pull/20094)
- **http2**:
  - graduate from experimental (<PERSON>) [#22466](https://github.com/nodejs/node/pull/22466)

### Commits

- [[`0d241ba385`](https://github.com/nodejs/node/commit/0d241ba385)] - **assert**: ensure .rejects() disallows sync throws (<PERSON>) [#19650](https://github.com/nodejs/node/pull/19650)
- [[`3babc5bb53`](https://github.com/nodejs/node/commit/3babc5bb53)] - **(SEMVER-MINOR)** **assert**: add rejects() and doesNotReject() (feugy) [#18023](https://github.com/nodejs/node/pull/18023)
- [[`18071db274`](https://github.com/nodejs/node/commit/18071db274)] - **assert**: fix throws trace (Ruben Bridgewater) [#18595](https://github.com/nodejs/node/pull/18595)
- [[`562787efb2`](https://github.com/nodejs/node/commit/562787efb2)] - **assert**: fix strict regression (Ruben Bridgewater) [#17903](https://github.com/nodejs/node/pull/17903)
- [[`f2af930ebb`](https://github.com/nodejs/node/commit/f2af930ebb)] - **(SEMVER-MINOR)** **assert**: .throws accept objects (Ruben Bridgewater) [#17584](https://github.com/nodejs/node/pull/17584)
- [[`147aeedc8d`](https://github.com/nodejs/node/commit/147aeedc8d)] - **(SEMVER-MINOR)** **assert**: improve assert.throws (Ruben Bridgewater) [#17585](https://github.com/nodejs/node/pull/17585)
- [[`c9d84b6d4f`](https://github.com/nodejs/node/commit/c9d84b6d4f)] - **assert**: fix throws and doesNotThrow stack frames (Ruben Bridgewater) [#17703](https://github.com/nodejs/node/pull/17703)
- [[`a42d0726ac`](https://github.com/nodejs/node/commit/a42d0726ac)] - **assert**: use object argument in innerFail (Ruben Bridgewater) [#17582](https://github.com/nodejs/node/pull/17582)
- [[`84948cf14f`](https://github.com/nodejs/node/commit/84948cf14f)] - **assert**: fix .throws operator (Ruben Bridgewater) [#17575](https://github.com/nodejs/node/pull/17575)
- [[`c6d94f8fa5`](https://github.com/nodejs/node/commit/c6d94f8fa5)] - **(SEMVER-MINOR)** **assert**: add strict functionality export (Ruben Bridgewater) [#17002](https://github.com/nodejs/node/pull/17002)
- [[`26d145a77f`](https://github.com/nodejs/node/commit/26d145a77f)] - **async_hooks**: add missing async_hooks destroys in AsyncReset (Bastian Krol) [#23272](https://github.com/nodejs/node/pull/23272)
- [[`104fbc64ed`](https://github.com/nodejs/node/commit/104fbc64ed)] - **build**: update arm64 minimum supported platform (Gibson Fahnestock) [#19164](https://github.com/nodejs/node/pull/19164)
- [[`afcf059898`](https://github.com/nodejs/node/commit/afcf059898)] - **build**: do not cd on vcbuild help (Vse Mozhet Byt) [#19291](https://github.com/nodejs/node/pull/19291)
- [[`ca8d4e3450`](https://github.com/nodejs/node/commit/ca8d4e3450)] - **build**: define NOMINMAX on windows (Ben Noordhuis) [#22731](https://github.com/nodejs/node/pull/22731)
- [[`5245d6ac97`](https://github.com/nodejs/node/commit/5245d6ac97)] - **deps**: V8: partially revert d868eb7 (Ali Ijaz Sheikh) [#24499](https://github.com/nodejs/node/pull/24499)
- [[`62dd1d7bd4`](https://github.com/nodejs/node/commit/62dd1d7bd4)] - **deps**: upgrade to libuv 1.23.2 (cjihrig) [#23336](https://github.com/nodejs/node/pull/23336)
- [[`b38190ebb0`](https://github.com/nodejs/node/commit/b38190ebb0)] - **deps**: upgrade to libuv 1.23.1 (cjihrig) [#22997](https://github.com/nodejs/node/pull/22997)
- [[`d9d541c415`](https://github.com/nodejs/node/commit/d9d541c415)] - **deps**: upgrade to libuv 1.23.0 (cjihrig) [#22365](https://github.com/nodejs/node/pull/22365)
- [[`e3d08af7c1`](https://github.com/nodejs/node/commit/e3d08af7c1)] - **deps**: upgrade to libuv 1.22.0 (cjihrig) [#21731](https://github.com/nodejs/node/pull/21731)
- [[`11cb09b25a`](https://github.com/nodejs/node/commit/11cb09b25a)] - **deps**: upgrade to libuv 1.21.0 (cjihrig) [#21466](https://github.com/nodejs/node/pull/21466)
- [[`c54f4bc8e8`](https://github.com/nodejs/node/commit/c54f4bc8e8)] - **deps**: upgrade to libuv 1.20.3 (cjihrig) [#20585](https://github.com/nodejs/node/pull/20585)
- [[`2307653abf`](https://github.com/nodejs/node/commit/2307653abf)] - **deps**: upgrade to libuv 1.20.2 (cjihrig) [#20129](https://github.com/nodejs/node/pull/20129)
- [[`a1b94d35e7`](https://github.com/nodejs/node/commit/a1b94d35e7)] - **deps**: upgrade libuv to 1.20.0 (cjihrig) [#19758](https://github.com/nodejs/node/pull/19758)
- [[`ce65d84537`](https://github.com/nodejs/node/commit/ce65d84537)] - **deps**: backport a8f6869 from upstream V8 (Ben Newman) [#22714](https://github.com/nodejs/node/pull/22714)
- [[`7ab253f62e`](https://github.com/nodejs/node/commit/7ab253f62e)] - **deps**: V8: cherry-pick 64-bit hash seed commits (Yang Guo) [#23274](https://github.com/nodejs/node/pull/23274)
- [[`60f7bfa4d7`](https://github.com/nodejs/node/commit/60f7bfa4d7)] - **deps**: update to nghttp2 1.33.0 (Anna Henningsen) [#22649](https://github.com/nodejs/node/pull/22649)
- [[`48f31bdf20`](https://github.com/nodejs/node/commit/48f31bdf20)] - **deps**: V8: backport 20 CPU profiler commits from upstream (Peter Marshall) [#21558](https://github.com/nodejs/node/pull/21558)
- [[`9e2077afee`](https://github.com/nodejs/node/commit/9e2077afee)] - **deps**: backport 9a23bdd from upstream V8 (Daniel Beckert) [#22418](https://github.com/nodejs/node/pull/22418)
- [[`610297e2ab`](https://github.com/nodejs/node/commit/610297e2ab)] - **doc**: improve best practices in onboarding-extras (Rich Trott) [#19315](https://github.com/nodejs/node/pull/19315)
- [[`9446bb68ea`](https://github.com/nodejs/node/commit/9446bb68ea)] - **doc**: fix minor issues in async_hooks.md (Rich Trott) [#19313](https://github.com/nodejs/node/pull/19313)
- [[`5b9af6ea73`](https://github.com/nodejs/node/commit/5b9af6ea73)] - **doc**: update username and email (Yuta Hiroto) [#19338](https://github.com/nodejs/node/pull/19338)
- [[`bae7c608e2`](https://github.com/nodejs/node/commit/bae7c608e2)] - **doc**: document http2 timeouts (Sagi Tsofan) [#22798](https://github.com/nodejs/node/pull/22798)
- [[`d0be932375`](https://github.com/nodejs/node/commit/d0be932375)] - **doc**: simplify http2 wording and formatting (Rich Trott) [#22541](https://github.com/nodejs/node/pull/22541)
- [[`3fe9293efc`](https://github.com/nodejs/node/commit/3fe9293efc)] - **doc**: make createPushResponse() more detailled (MaleDong) [#22366](https://github.com/nodejs/node/pull/22366)
- [[`3980ca1840`](https://github.com/nodejs/node/commit/3980ca1840)] - **doc**: clarify http2 docs around class exports (James M Snell) [#22247](https://github.com/nodejs/node/pull/22247)
- [[`32bfd7ebfb`](https://github.com/nodejs/node/commit/32bfd7ebfb)] - **doc**: add missing `require` to example in http2.md (Kevin Simper) [#21858](https://github.com/nodejs/node/pull/21858)
- [[`2116ace0ad`](https://github.com/nodejs/node/commit/2116ace0ad)] - **doc**: fix http2stream.pushStream error doc (Сковорода Никита Андреевич) [#21487](https://github.com/nodejs/node/pull/21487)
- [[`4228141012`](https://github.com/nodejs/node/commit/4228141012)] - **doc**: Improve doc for Http2 headers object (Gerhard Stoebich) [#21296](https://github.com/nodejs/node/pull/21296)
- [[`11a63ddf48`](https://github.com/nodejs/node/commit/11a63ddf48)] - **doc**: fix typo in http2.md (Keita Akutsu) [#20843](https://github.com/nodejs/node/pull/20843)
- [[`4f0035485f`](https://github.com/nodejs/node/commit/4f0035485f)] - **doc**: add parameters for Http2Stream:error event (Ujjwal Sharma) [#20610](https://github.com/nodejs/node/pull/20610)
- [[`77acef4af2`](https://github.com/nodejs/node/commit/77acef4af2)] - **doc**: add params for ClientHttp2Session:altsvc (Ujjwal Sharma) [#20598](https://github.com/nodejs/node/pull/20598)
- [[`448922d0de`](https://github.com/nodejs/node/commit/448922d0de)] - **doc**: add parameters for Http2Session:stream event (Ujjwal Sharma) [#20547](https://github.com/nodejs/node/pull/20547)
- [[`41e89316e6`](https://github.com/nodejs/node/commit/41e89316e6)] - **doc**: add parameters for settings events (Ujjwal Sharma) [#20371](https://github.com/nodejs/node/pull/20371)
- [[`1a6a054899`](https://github.com/nodejs/node/commit/1a6a054899)] - **doc**: improve parameters for Http2Session:goaway event (Ujjwal Sharma)
- [[`98ed30f3f5`](https://github.com/nodejs/node/commit/98ed30f3f5)] - **doc**: improve docs for Http2Session:frameError (Ujjwal Sharma) [#20236](https://github.com/nodejs/node/pull/20236)
- [[`b32cf8fa40`](https://github.com/nodejs/node/commit/b32cf8fa40)] - **doc**: add parameters for Http2Session:error event (Ujjwal Sharma) [#20206](https://github.com/nodejs/node/pull/20206)
- [[`c0d1423bd3`](https://github.com/nodejs/node/commit/c0d1423bd3)] - **doc**: close event does not take arguments (Indranil Dasgupta) [#20031](https://github.com/nodejs/node/pull/20031)
- [[`459690aca4`](https://github.com/nodejs/node/commit/459690aca4)] - **doc**: improve style guide text (Rich Trott) [#19269](https://github.com/nodejs/node/pull/19269)
- [[`eaabbf4ff0`](https://github.com/nodejs/node/commit/eaabbf4ff0)] - **doc**: make caveat in stream.md more concise (Rich Trott) [#19251](https://github.com/nodejs/node/pull/19251)
- [[`0340dd8c8d`](https://github.com/nodejs/node/commit/0340dd8c8d)] - **doc**: add and unify return statements in crypto.md (Vse Mozhet Byt) [#19853](https://github.com/nodejs/node/pull/19853)
- [[`b0d6067d87`](https://github.com/nodejs/node/commit/b0d6067d87)] - **doc**: fix 8.12.0 changelog (Myles Borins) [#22803](https://github.com/nodejs/node/pull/22803)
- [[`af5cebb326`](https://github.com/nodejs/node/commit/af5cebb326)] - **doc,http2**: add parameters for Http2Session:connect event (Ujjwal Sharma) [#20193](https://github.com/nodejs/node/pull/20193)
- [[`57618aae0a`](https://github.com/nodejs/node/commit/57618aae0a)] - **errors**: fix undefined HTTP2 and tls errors (Shailesh Shekhawat) [#21564](https://github.com/nodejs/node/pull/21564)
- [[`e3bddeec18`](https://github.com/nodejs/node/commit/e3bddeec18)] - **http**: fix undefined error in parser event (Anatoli Papirovski) [#20029](https://github.com/nodejs/node/pull/20029)
- [[`1edd7f6393`](https://github.com/nodejs/node/commit/1edd7f6393)] - **(SEMVER-MINOR)** **http**: added aborted property to request (Robert Nagy) [#20094](https://github.com/nodejs/node/pull/20094)
- [[`7f34c277ac`](https://github.com/nodejs/node/commit/7f34c277ac)] - **http2**: simplify timeout tracking (Anna Henningsen) [#19206](https://github.com/nodejs/node/pull/19206)
- [[`18a2b3dc8e`](https://github.com/nodejs/node/commit/18a2b3dc8e)] - **(SEMVER-MINOR)** **http2**: graduate from experimental (James M Snell) [#22466](https://github.com/nodejs/node/pull/22466)
- [[`10576d6e77`](https://github.com/nodejs/node/commit/10576d6e77)] - **(SEMVER-MINOR)** **http2**: add ping event (James M Snell) [#23009](https://github.com/nodejs/node/pull/23009)
- [[`ca933ce577`](https://github.com/nodejs/node/commit/ca933ce577)] - **http2**: do not falsely emit 'aborted' on push (Anatoli Papirovski) [#22878](https://github.com/nodejs/node/pull/22878)
- [[`49f44f3b44`](https://github.com/nodejs/node/commit/49f44f3b44)] - **(SEMVER-MINOR)** **http2**: add origin frame support (James M Snell) [#22956](https://github.com/nodejs/node/pull/22956)
- [[`9f7934159e`](https://github.com/nodejs/node/commit/9f7934159e)] - **http2**: check if stream is not destroyed before sending trailers (Matteo Collina) [#22896](https://github.com/nodejs/node/pull/22896)
- [[`2de17ead89`](https://github.com/nodejs/node/commit/2de17ead89)] - **(SEMVER-MINOR)** **http2**: add http2stream.endAfterHeaders property (James M Snell) [#22843](https://github.com/nodejs/node/pull/22843)
- [[`805bf40bfd`](https://github.com/nodejs/node/commit/805bf40bfd)] - **http2**: don't expose the original socket through the socket proxy (Szymon Marczak) [#22650](https://github.com/nodejs/node/pull/22650)
- [[`6a396ff911`](https://github.com/nodejs/node/commit/6a396ff911)] - **http2**: throw better error when accessing unbound socket proxy (James M Snell) [#22486](https://github.com/nodejs/node/pull/22486)
- [[`348cde07fd`](https://github.com/nodejs/node/commit/348cde07fd)] - **http2**: emit timeout on compat request and response (James M Snell) [#22252](https://github.com/nodejs/node/pull/22252)
- [[`cc561cc5a7`](https://github.com/nodejs/node/commit/cc561cc5a7)] - **http2**: explicitly disallow nested push streams (James M Snell) [#22245](https://github.com/nodejs/node/pull/22245)
- [[`5c3edd3479`](https://github.com/nodejs/node/commit/5c3edd3479)] - **http2**: avoid race condition in OnHeaderCallback (James M Snell) [#22256](https://github.com/nodejs/node/pull/22256)
- [[`f2f66b4cfb`](https://github.com/nodejs/node/commit/f2f66b4cfb)] - **http2**: remove `streamError` from docs (James M Snell) [#22246](https://github.com/nodejs/node/pull/22246)
- [[`d602c7a2ed`](https://github.com/nodejs/node/commit/d602c7a2ed)] - **http2**: release request()'s "connect" event listener after it runs (James Ide) [#21916](https://github.com/nodejs/node/pull/21916)
- [[`745e1e6192`](https://github.com/nodejs/node/commit/745e1e6192)] - **http2**: remove unused nghttp2 error list (Anna Henningsen) [#21827](https://github.com/nodejs/node/pull/21827)
- [[`e5175e6596`](https://github.com/nodejs/node/commit/e5175e6596)] - **http2**: remove `waitTrailers` listener after closing a stream (RidgeA) [#21764](https://github.com/nodejs/node/pull/21764)
- [[`071a022dbc`](https://github.com/nodejs/node/commit/071a022dbc)] - **http2**: order declarations in core.js (Rich Trott) [#21689](https://github.com/nodejs/node/pull/21689)
- [[`1cdf93ecdc`](https://github.com/nodejs/node/commit/1cdf93ecdc)] - **http2**: pass incoming set-cookie header as array (Gerhard Stoebich) [#21360](https://github.com/nodejs/node/pull/21360)
- [[`20b72fc94d`](https://github.com/nodejs/node/commit/20b72fc94d)] - **http2**: track memory allocated by nghttp2 (Anna Henningsen) [#21374](https://github.com/nodejs/node/pull/21374)
- [[`e9e4f434b3`](https://github.com/nodejs/node/commit/e9e4f434b3)] - **http2**: fix memory leak when headers are not emitted (Anna Henningsen) [#21373](https://github.com/nodejs/node/pull/21373)
- [[`0f3e65099d`](https://github.com/nodejs/node/commit/0f3e65099d)] - **http2**: fix memory leak for uncommon headers (Anna Henningsen) [#21336](https://github.com/nodejs/node/pull/21336)
- [[`0a8d0861f2`](https://github.com/nodejs/node/commit/0a8d0861f2)] - **http2**: safer Http2Session destructor (Anatoli Papirovski) [#21194](https://github.com/nodejs/node/pull/21194)
- [[`3c8c53f4f4`](https://github.com/nodejs/node/commit/3c8c53f4f4)] - **http2**: fix premature destroy (Anatoli Papirovski) [#21051](https://github.com/nodejs/node/pull/21051)
- [[`b22266cc97`](https://github.com/nodejs/node/commit/b22266cc97)] - **http2**: force through RST_STREAM in destroy (Anatoli Papirovski) [#21016](https://github.com/nodejs/node/pull/21016)
- [[`91be1dc2a5`](https://github.com/nodejs/node/commit/91be1dc2a5)] - **http2**: delay closing stream (Anatoli Papirovski) [#20997](https://github.com/nodejs/node/pull/20997)
- [[`0a6672fbcf`](https://github.com/nodejs/node/commit/0a6672fbcf)] - **http2**: fix several serious bugs (Anatoli Papirovski) [#20772](https://github.com/nodejs/node/pull/20772)
- [[`b0c92cadfa`](https://github.com/nodejs/node/commit/b0c92cadfa)] - **http2**: fix end without read (Anatoli Papirovski) [#20621](https://github.com/nodejs/node/pull/20621)
- [[`d1b78252b1`](https://github.com/nodejs/node/commit/d1b78252b1)] - **http2**: avoid bind and properly clean up in compat (Robert Nagy) [#20374](https://github.com/nodejs/node/pull/20374)
- [[`395ce845da`](https://github.com/nodejs/node/commit/395ce845da)] - **http2**: rename http2_state class to Http2State (Daniel Bevenius) [#20423](https://github.com/nodejs/node/pull/20423)
- [[`74192ddb66`](https://github.com/nodejs/node/commit/74192ddb66)] - **http2**: reduce require calls in http2/core (Daniel Bevenius) [#20422](https://github.com/nodejs/node/pull/20422)
- [[`28a6e59bd3`](https://github.com/nodejs/node/commit/28a6e59bd3)] - **http2**: fix ping callback (Ruben Bridgewater) [#20311](https://github.com/nodejs/node/pull/20311)
- [[`41dca9e851`](https://github.com/nodejs/node/commit/41dca9e851)] - **http2**: fix responses to long payload reqs (Anatoli Papirovski) [#20084](https://github.com/nodejs/node/pull/20084)
- [[`fa5a3809a3`](https://github.com/nodejs/node/commit/fa5a3809a3)] - **http2**: refactor how trailers are done (James M Snell) [#19959](https://github.com/nodejs/node/pull/19959)
- [[`5862d0372c`](https://github.com/nodejs/node/commit/5862d0372c)] - **http2**: fix ping duration calculation (James M Snell) [#19956](https://github.com/nodejs/node/pull/19956)
- [[`2ae98ce7cb`](https://github.com/nodejs/node/commit/2ae98ce7cb)] - **lib**: define printErr() in script string (cjihrig) [#19285](https://github.com/nodejs/node/pull/19285)
- [[`b0e3ce9c4b`](https://github.com/nodejs/node/commit/b0e3ce9c4b)] - **net,http2**: refactor \_write and \_writev (Ujjwal Sharma) [#20643](https://github.com/nodejs/node/pull/20643)
- [[`0187e3bef8`](https://github.com/nodejs/node/commit/0187e3bef8)] - **process**: avoid using the same fd for ipc and stdio (cjihrig) [#21466](https://github.com/nodejs/node/pull/21466)
- [[`5b2f6508f9`](https://github.com/nodejs/node/commit/5b2f6508f9)] - **src**: make AsyncWrap constructors delegate (Daniel Bevenius) [#19366](https://github.com/nodejs/node/pull/19366)
- [[`9e8f4e5047`](https://github.com/nodejs/node/commit/9e8f4e5047)] - **src**: remove unused uv.h include from async_wrap.cc (Daniel Bevenius) [#19342](https://github.com/nodejs/node/pull/19342)
- [[`042434f9af`](https://github.com/nodejs/node/commit/042434f9af)] - **src**: fix indenting of wrap-\>EmitTraceEventBefore (Daniel Bevenius) [#19340](https://github.com/nodejs/node/pull/19340)
- [[`3ad10e5789`](https://github.com/nodejs/node/commit/3ad10e5789)] - **src**: add extractPromiseWrap function (Daniel Bevenius) [#19340](https://github.com/nodejs/node/pull/19340)
- [[`b67bf38f31`](https://github.com/nodejs/node/commit/b67bf38f31)] - **src**: fix fs.write() externalized string handling (Ben Noordhuis) [#18216](https://github.com/nodejs/node/pull/18216)
- [[`0157e3ebca`](https://github.com/nodejs/node/commit/0157e3ebca)] - **src,deps**: add ABI safe use of CheckMemoryPressure (Ali Ijaz Sheikh) [#24499](https://github.com/nodejs/node/pull/24499)
- [[`dbc7d9baae`](https://github.com/nodejs/node/commit/dbc7d9baae)] - **test**: read() on dir on AIX does not return EISDIR (Ben Noordhuis) [#23330](https://github.com/nodejs/node/pull/23330)
- [[`3cd4462370`](https://github.com/nodejs/node/commit/3cd4462370)] - **test**: ensure failed assertions cause build to fail (Teddy Katz) [#19650](https://github.com/nodejs/node/pull/19650)
- [[`9f15bc40b8`](https://github.com/nodejs/node/commit/9f15bc40b8)] - **test**: skip failing tests for osx mojave (jn99) [#23550](https://github.com/nodejs/node/pull/23550)
- [[`aba1ff202c`](https://github.com/nodejs/node/commit/aba1ff202c)] - **test**: refactor test-fs-readfile-tostring-fail (Rich Trott) [#19404](https://github.com/nodejs/node/pull/19404)
- [[`38ed6c2b25`](https://github.com/nodejs/node/commit/38ed6c2b25)] - **test**: fix flaky test-http2-ping-flood (Rich Trott) [#19395](https://github.com/nodejs/node/pull/19395)
- [[`b407060556`](https://github.com/nodejs/node/commit/b407060556)] - **test**: fix flaky test-http2-settings-flood (Rich Trott) [#19349](https://github.com/nodejs/node/pull/19349)
- [[`069fd79424`](https://github.com/nodejs/node/commit/069fd79424)] - **test**: improve debugging information for http2 test (Rich Trott) [#23058](https://github.com/nodejs/node/pull/23058)
- [[`c0f8e49c32`](https://github.com/nodejs/node/commit/c0f8e49c32)] - **test**: remove setImmediate from timeout test (Rich Trott) [#23058](https://github.com/nodejs/node/pull/23058)
- [[`b66cba0766`](https://github.com/nodejs/node/commit/b66cba0766)] - **test**: add test-http2-large-file sequential test (James M Snell) [#22254](https://github.com/nodejs/node/pull/22254)
- [[`7ea08eedac`](https://github.com/nodejs/node/commit/7ea08eedac)] - **test**: improve reliability in http2-session-timeout (Rich Trott) [#22026](https://github.com/nodejs/node/pull/22026)
- [[`dcf04dc7df`](https://github.com/nodejs/node/commit/dcf04dc7df)] - **test**: refactor test-http2-compat-serverresponse-finished.js (Anto Aravinth) [#21929](https://github.com/nodejs/node/pull/21929)
- [[`322f39d490`](https://github.com/nodejs/node/commit/322f39d490)] - **test**: minor adjustments to test-http2-respond-file (Anna Henningsen) [#21098](https://github.com/nodejs/node/pull/21098)
- [[`5d29e2c631`](https://github.com/nodejs/node/commit/5d29e2c631)] - **test**: fix flaky http2-session-unref (Anatoli Papirovski) [#20772](https://github.com/nodejs/node/pull/20772)
- [[`e5f8b08305`](https://github.com/nodejs/node/commit/e5f8b08305)] - **test**: improve reliability of http2-session-timeout (Rich Trott) [#20692](https://github.com/nodejs/node/pull/20692)
- [[`c30a8f468d`](https://github.com/nodejs/node/commit/c30a8f468d)] - **test**: fix flaky http2-flow-control test (Anatoli Papirovski) [#20556](https://github.com/nodejs/node/pull/20556)
- [[`aa341d1d3d`](https://github.com/nodejs/node/commit/aa341d1d3d)] - **test**: verify arguments length in common.expectsError (Ruben Bridgewater) [#20311](https://github.com/nodejs/node/pull/20311)
- [[`c7ba556264`](https://github.com/nodejs/node/commit/c7ba556264)] - **test**: removed assert.strictEqual message (kailash k yogeshwar) [#20223](https://github.com/nodejs/node/pull/20223)
- [[`5abe246a44`](https://github.com/nodejs/node/commit/5abe246a44)] - **test**: add strictEqual method to assert (Christine E. Taylor) [#20189](https://github.com/nodejs/node/pull/20189)
- [[`887417eb37`](https://github.com/nodejs/node/commit/887417eb37)] - **test**: remove message from strictEqual assertions (Bryan Azofeifa) [#20174](https://github.com/nodejs/node/pull/20174)
- [[`fe3836a871`](https://github.com/nodejs/node/commit/fe3836a871)] - **test**: delete test/parallel/test-regress-GH-4948 (Ujjwal Sharma)
- [[`4bcdc1b83c`](https://github.com/nodejs/node/commit/4bcdc1b83c)] - **test**: fix assertion argument order (Rich Trott) [#19264](https://github.com/nodejs/node/pull/19264)
- [[`534bc82578`](https://github.com/nodejs/node/commit/534bc82578)] - **test**: name test files appropriately (Ujjwal Sharma) [#19212](https://github.com/nodejs/node/pull/19212)
- [[`d58867a6a7`](https://github.com/nodejs/node/commit/d58867a6a7)] - **test**: call gc() explicitly to avoid OOM (Refael Ackermann) [#22301](https://github.com/nodejs/node/pull/22301)
- [[`8209ccb313`](https://github.com/nodejs/node/commit/8209ccb313)] - **test**: prepare test-assert for strictEqual linting (Rich Trott) [#22849](https://github.com/nodejs/node/pull/22849)
- [[`52b21caff2`](https://github.com/nodejs/node/commit/52b21caff2)] - **test**: remove string literal from assertion (Rich Trott) [#22849](https://github.com/nodejs/node/pull/22849)
- [[`976d55f9e3`](https://github.com/nodejs/node/commit/976d55f9e3)] - **test**: remove string literal from assertion (Rich Trott) [#22849](https://github.com/nodejs/node/pull/22849)
- [[`702d67f4c4`](https://github.com/nodejs/node/commit/702d67f4c4)] - **test**: refactor flag check (Rich Trott) [#22849](https://github.com/nodejs/node/pull/22849)
- [[`e9416d4f67`](https://github.com/nodejs/node/commit/e9416d4f67)] - **test**: simplify assertion in http2 tests (Rich Trott) [#22849](https://github.com/nodejs/node/pull/22849)
- [[`f2158f30fb`](https://github.com/nodejs/node/commit/f2158f30fb)] - **test**: improve assertion in test-inspector.js (Rich Trott) [#22849](https://github.com/nodejs/node/pull/22849)
- [[`f5985c734c`](https://github.com/nodejs/node/commit/f5985c734c)] - **tls,http2**: handle writes after SSL destroy more gracefully (Anna Henningsen) [#18987](https://github.com/nodejs/node/pull/18987)

Windows 32-bit Installer: https://nodejs.org/dist/v8.13.0/node-v8.13.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v8.13.0/node-v8.13.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v8.13.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v8.13.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v8.13.0/node-v8.13.0.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v8.13.0/node-v8.13.0-darwin-x64.tar.gz \
Linux 32-bit Binary: https://nodejs.org/dist/v8.13.0/node-v8.13.0-linux-x86.tar.xz \
Linux 64-bit Binary: https://nodejs.org/dist/v8.13.0/node-v8.13.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v8.13.0/node-v8.13.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v8.13.0/node-v8.13.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v8.13.0/node-v8.13.0-aix-ppc64.tar.gz \
SmartOS 32-bit Binary: https://nodejs.org/dist/v8.13.0/node-v8.13.0-sunos-x86.tar.xz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v8.13.0/node-v8.13.0-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v8.13.0/node-v8.13.0-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v8.13.0/node-v8.13.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v8.13.0/node-v8.13.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v8.13.0/node-v8.13.0.tar.gz \
Other release files: https://nodejs.org/dist/v8.13.0/ \
Documentation: https://nodejs.org/docs/v8.13.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

22a763858774c078d4a9cc121b7744ce3f1c5312bff939c407292261b3a69d50  node-v8.13.0-aix-ppc64.tar.gz
f6261c7a09a2c8bd77a8760a0e110410f508e5641ca132511b4883600f2d65f0  node-v8.13.0-darwin-x64.tar.gz
c4d3ca9d5dd0a1a70d43c15d94a0e5486252bd0640e1d05ad7048e85d25d216d  node-v8.13.0-darwin-x64.tar.xz
373acc08aa4a02c775f81a6996dcba2b97bd9b26145f386e692990ecb29f79a5  node-v8.13.0-headers.tar.gz
6a6edf983064dff9f6aec03f7d23fea14051debc1f1c807373eb6df2d6de6f9f  node-v8.13.0-headers.tar.xz
ef3fe95d79ced4882b7bf2d94cf05186fdf352e752598d94747bcbbe49877ed8  node-v8.13.0-linux-arm64.tar.gz
e8e0daab515dfd0c2323b8e1274475f61c8ca1cf582dcc254583cbf8593425e3  node-v8.13.0-linux-arm64.tar.xz
77b8617ef6620e707853aa7eb087f5de2d7f818ba0b6700122a9fe06a9176065  node-v8.13.0-linux-armv6l.tar.gz
2c27ec5fafb1e2bf75f5aef0d1a33952a29d300d165cb7e875f11cfdd96c0b29  node-v8.13.0-linux-armv6l.tar.xz
8877505c726aad0d0f85b2f415e88aac5e1afa2f8c198018699620692f7283f0  node-v8.13.0-linux-armv7l.tar.gz
ded647ea55988c5d5afa6baae05a187518255c0cc96936765ff2676541c79d68  node-v8.13.0-linux-armv7l.tar.xz
782b34ee2d26a3c621b52d16cad60a07e19c627933704e26fa6b312cdf84a6b0  node-v8.13.0-linux-ppc64le.tar.gz
c984d399fb546cec2dedac6790caad7a0fbead092c5f29b046a63d2898944318  node-v8.13.0-linux-ppc64le.tar.xz
bb84c1dde31fa5d16fe0710cf444f691f6870c95435fe972f1d5faf83160e9a2  node-v8.13.0-linux-s390x.tar.gz
c70d92b143d242902890be76fbbf27e83cdbeb75cec71090d3242552fe62dcd4  node-v8.13.0-linux-s390x.tar.xz
0c5ff3e3223cd1aaa6ca4aad30fb7c94596422d5c1a4dcc4b1a5b4e118a00273  node-v8.13.0-linux-x64.tar.gz
8fa1227b28113e25a8960d7fa6779a8c18bf20cfaafc9c5d0e46a3ee88373669  node-v8.13.0-linux-x64.tar.xz
a27c3de5b61322711b7167911f37513a4529da930e836f23827819ab5240e1dd  node-v8.13.0-linux-x86.tar.gz
632021c49972d26501528e468b545cdb4e850616530886ce45d28b5d73a224d9  node-v8.13.0-linux-x86.tar.xz
aa289d92179f71a8b3516259d7f266c7f9a245cd16a6853a861b6cf9bea92ad0  node-v8.13.0.pkg
8ccf7b2bb72c7e00b2882465b21d0f065f8dcad302bea90c9e320fed6c750f1e  node-v8.13.0-sunos-x64.tar.gz
cf091c8514d663a860451386e0b3418e39cccaf332c850d5d536daec55b667df  node-v8.13.0-sunos-x64.tar.xz
98f94e3d36365a34363c9ab0cc805fc8b633767a30f64826495c2130af309e24  node-v8.13.0-sunos-x86.tar.gz
3f7006c3d3eaaf4512523eba8b29450209d5561a32c8b75f2b711d820cb66c54  node-v8.13.0-sunos-x86.tar.xz
9ba06fe4da9539721281bba4e21be0cd31546c4f47bbdccc21a9fca243e75083  node-v8.13.0.tar.gz
2aa99474a336c6339d14f08cc27d387c5168e6fb6cbcaaea1d5ff7aa89642de2  node-v8.13.0.tar.xz
9a63066648b70e19300009738659facb8dda3fa092cc34e8e312dc214d209957  node-v8.13.0-win-x64.7z
46293121d207350bd8d334efb67303337933c4dac886a840f88ff6059bff9443  node-v8.13.0-win-x64.zip
b31b084defaf4cd62710c1f0410acd022e44ef60fe12ea6ba7d1f6252c11c9ac  node-v8.13.0-win-x86.7z
d3c8888698a78ef06f933a8e08f50897290c0ed92ed67326079c944dfa63433a  node-v8.13.0-win-x86.zip
764ef072c2812c4fc91e0b13ef028ea4aee003c156fb69ff56e15f3e1441dd75  node-v8.13.0-x64.msi
887499505a1d7899cf01fed2c337451c9cd626b63155668f9963d3912adda6dd  node-v8.13.0-x86.msi
0b6f6201d71e4659ad0b5dae03c105f570df5118c91fffa3893d8f0281da1586  win-x64/node.exe
003099585f59c542e0097dd8d7accd0e31ee1f7911843baac3526d4d66ea13df  win-x64/node.lib
624236b3839d9c817e8c679e0c83a967f373473ac1d2a2d6f285e4efe7813e8c  win-x64/node_pdb.7z
d2fc2aa8fcf8d3f55dd549b973932fa73be0e62d647658fba3b6ec35c028398f  win-x64/node_pdb.zip
7dfa0a1aa8be580f7fe82a58eaace7cc0173ed6c28e5f7d61882e7e61254ac06  win-x86/node.exe
b04d7ee6f5e48b40e2f1cc9e2e648ec31ddb97981a32012504d88cd462253f70  win-x86/node.lib
9e0952036087ebfdaebce419c78746191334d9ebb94f3f99f272b58c1cb2bede  win-x86/node_pdb.7z
fe1143ebe80932fe55a6f4160935b1a65e0be614280b1c2dbca286afebdf3e46  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQEzBAEBCAAdFiEEDv/hvO/ZyE49CYFSkzsB9AtcqUYFAlv4JFAACgkQkzsB9Atc
qUYzUQgAvIg04/iZ2BiZszHfCxc58R3181IRdK2QWlolOLJl1070Yn0t4tAzYxAW
Ed9lP2flkUQwOGyD3N5/U2x0MWQUKVpjMBq13+KpCuXAqFaRp773lkO5m/Xf+PPV
/juoKLO09krNaPyOimfjaeSdT8kza5De8WrkaAdjuUQB4/k8LQg0E3UUF8QxwLbo
URd2ZbM16vD/oUVRiR+WEelG9AorXJbGhLx1x2KActCMHGg+dQJZI9dCuXce5mMK
7R2PptFFHw4XPx9oSNYLRAJ6CtY0dMWPGhs1wk7E2z5QRJ4Co/rG2/XeCSXzSA7c
xyMMW3D5tbNjUM0UJECV1mscsE9HkA==
=eL95
-----END PGP SIGNATURE-----

```
