---
date: '2022-09-23T16:14:04.993Z'
category: release
title: Node v18.9.1 (Current)
layout: blog-post
author: <PERSON>
---

### Notable changes

The following CVEs are fixed in this release:

- **[CVE-2022-32212](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2022-32212)**: DNS rebinding in --inspect on macOS (High)
  - Insufficient fix for macOS devices on v18.5.0
- **[CVE-2022-32222](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2022-32222)**: Node 18 reads openssl.cnf from /home/<USER>/build/ upon startup on MacOS (Medium)
- **[CVE-2022-32213](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2022-32213)**: HTTP Request Smuggling - Flawed Parsing of Transfer-Encoding (Medium)
  - Insufficient fix on v18.5.0
- **[CVE-2022-32215](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2022-32215)**: HTTP Request Smuggling - Incorrect Parsing of Multi-line Transfer-Encoding (Medium)
  - Insufficient fix on v18.5.0
- **[CVE-2022-35256](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2022-35256)**: HTTP Request Smuggling - Incorrect Parsing of Header Fields (Medium)
- **[CVE-2022-35255](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2022-35255)**: Weak randomness in WebCrypto keygen

More detailed information on each of the vulnerabilities can be found in [September 22nd 2022 Security Releases](/blog/vulnerability/september-2022-security-releases/) blog post.

#### llhttp updated to 6.0.10

`llhttp` is updated to 6.0.10 which includes fixes for the following vulnerabilities.

- **HTTP Request Smuggling - CVE-2022-32213 bypass via obs-fold mechanic (Medium)([CVE-2022-32213](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2022-32213) )**: The `llhttp` parser in the `http` module does not correctly parse and validate Transfer-Encoding headers. This can lead to HTTP Request Smuggling (HRS).
- **HTTP Request Smuggling - Incorrect Parsing of Multi-line Transfer-Encoding (Medium)([CVE-2022-32215](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2022-32215))**: The `llhttp` parser in the `http` module does not correctly handle multi-line Transfer-Encoding headers. This can lead to HTTP Request Smuggling (HRS).
- **HTTP Request Smuggling - Incorrect Parsing of Header Fields (Medium)([CVE-35256](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2022-35256))**: The llhttp parser in the `http` does not correctly handle header fields that are not terminated with CLRF. This can lead to HTTP Request Smuggling (HRS).

### Commits

- \[[`0c2a5723be`](https://github.com/nodejs/node/commit/0c2a5723be)] - **crypto**: fix weak randomness in WebCrypto keygen (Ben Noordhuis) [nodejs-private/node-private#](https://github.com/nodejs-private/node-private/pull/346)
- \[[`ffb6f4d51d`](https://github.com/nodejs/node/commit/ffb6f4d51d)] - **deps**: MacOS - fix location of OpenSSL config file (Michael Dawson) [nodejs-private/node-private#345](https://github.com/nodejs-private/node-private/pull/345)
- \[[`01bffcdd93`](https://github.com/nodejs/node/commit/01bffcdd93)] - **http**: disable chunked encoding when OBS fold is used (Paolo Insogna) [nodejs-private/node-private#341](https://github.com/nodejs-private/node-private/pull/341)
- \[[`2c379d341d`](https://github.com/nodejs/node/commit/2c379d341d)] - **src**: fix IPv4 non routable validation (RafaelGSS) [nodejs-private/node-private#337](https://github.com/nodejs-private/node-private/pull/337)

Windows 32-bit Installer: https://nodejs.org/dist/v18.9.1/node-v18.9.1-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v18.9.1/node-v18.9.1-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v18.9.1/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v18.9.1/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v18.9.1/node-v18.9.1.pkg \
macOS Apple Silicon 64-bit Binary: https://nodejs.org/dist/v18.9.1/node-v18.9.1-darwin-arm64.tar.gz \
macOS Intel 64-bit Binary: https://nodejs.org/dist/v18.9.1/node-v18.9.1-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v18.9.1/node-v18.9.1-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v18.9.1/node-v18.9.1-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v18.9.1/node-v18.9.1-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v18.9.1/node-v18.9.1-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v18.9.1/node-v18.9.1-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v18.9.1/node-v18.9.1-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v18.9.1/node-v18.9.1.tar.gz \
Other release files: https://nodejs.org/dist/v18.9.1/ \
Documentation: https://nodejs.org/docs/v18.9.1/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

6b602994ea7e22d49e1b2406d3d1119133d6bc89e52f70cd61090968b9e5ec93  node-v18.9.1-aix-ppc64.tar.gz
289dca525c5535bddf389b69386ceb12d7c77eeae9aa2f666652877f982f9b5d  node-v18.9.1-darwin-arm64.tar.gz
b80c029f945c522d553b70f4a8de14a077983dc36b4481a3051cd7103fb4a04c  node-v18.9.1-darwin-arm64.tar.xz
ef7d92bb3b21b50242175483dca6ccd98052d6f4be3ce5b9ae55f0b95c0db25d  node-v18.9.1-darwin-x64.tar.gz
dff4fe1259b7801121bf7335cddd742801c8b34a4aba9dc3eb5943c1edb163ee  node-v18.9.1-darwin-x64.tar.xz
fb963b1e81110447f6c19dc5211c1bc2f44b53460d10daac8dd920ebff081ffc  node-v18.9.1-headers.tar.gz
62f3863047d94f3ce1250f61be20fd697e47e972e636ff3385d469d55e8dd71a  node-v18.9.1-headers.tar.xz
a1610d6f75f45fb0dc73164231c63308d653c09a57dd14a989cf4de9b96e965b  node-v18.9.1-linux-arm64.tar.gz
d4edf28b695374faafc944f291151bf2fcfcf4b575207eadaee86a2c2aa1cbbe  node-v18.9.1-linux-arm64.tar.xz
d488cd0cda2c71d397c69db4088d4bec631c1489e1d58afbf2ed6e7d0ccc2660  node-v18.9.1-linux-armv7l.tar.gz
82502c7fb666b3842491d6244cd1eda72562ebe801dbe5c37bddab28acb91414  node-v18.9.1-linux-armv7l.tar.xz
6a853f4702c41c0da9f625def2db01e24a91e89a2c8dbbeb7b79556572390aa6  node-v18.9.1-linux-ppc64le.tar.gz
3b892a3f3f37d262f344b2cbf0a2aa1deb8534c3674d42a256f5153df409c087  node-v18.9.1-linux-ppc64le.tar.xz
042b5069395cb1f377a6b25203afdb099187ca44c67f848f805ecc7f8d97f412  node-v18.9.1-linux-s390x.tar.gz
eb0cc3db68e17faab8d60ad8e69f0a21eaf14dfd593c4f1b7117d49f51baaf43  node-v18.9.1-linux-s390x.tar.xz
33ecf5f39618f4beb90a9be98880325cb4f06e33b52e315040a54fd0700f2434  node-v18.9.1-linux-x64.tar.gz
0777cf4e281359061a6b5d0afe6750f5efd0e874f489a5ebb53ec8b8f77e8b82  node-v18.9.1-linux-x64.tar.xz
60160570e4d22c1735e74c0e954bcd94621870871a170b6b2cb4089d91204053  node-v18.9.1-win-x64.7z
763e691ed8f51b8664da4dcc5a0f5d428dbd69d4162630a6fcf366e5e9e25e81  node-v18.9.1-win-x64.zip
c9a22fe916685f1178d3ff60bdfc49a0d8d0b17944c640d0a0bfc8e25317bdaf  node-v18.9.1-win-x86.7z
860cd7354943eb137715c510b77a7e230666b47998edd6f5ea803db1aaf8999a  node-v18.9.1-win-x86.zip
b2886faeaed5a1ddc03325e8c1fca143e0bbfa250ae7a69a8326be364ad28577  node-v18.9.1-x64.msi
af847e88b3a3d0ceb63ffd572ff906d3a60b2a235334b7336f11904cbe7d35bb  node-v18.9.1-x86.msi
a3219e92b15afd4baa6a3bc8e3ad25f3036cb07bea08d2622c9a59db8d0a24f9  node-v18.9.1.pkg
50ae12386eb79058ad2d38335e41ca120904900a36b1bcfb10934be9373f737b  node-v18.9.1.tar.gz
f381963d43568ba699915c88629dc6da4a1963804dcd37b2e6e1d10d923dd5d9  node-v18.9.1.tar.xz
6d5094f77f1273b8127046d9c528bb800470b178a0b44d271907de5cf19b9dde  win-x64/node.exe
3111a04d3ae94921ac20f2afc4e167c59e50c07609ee940d1a8eec46f08310ad  win-x64/node.lib
28b6e90a8880b076b46e3f4662d19ea3e020f7b06c12135de31a62a2015019fb  win-x64/node_pdb.7z
464771c89a6bd4fd3684e172d2dbd510906c30c4273c6526d26ddc1f7e3bce78  win-x64/node_pdb.zip
f93ea0dbeb0e5326f53c7f1258d5315542c045651e43dae5ce18f7f32977fa3d  win-x86/node.exe
e0b45a34da85070b41e13169a6ed30ea782d400dd8e8597d665727bac8d621f0  win-x86/node.lib
f0a4d77ebccca0909f5532d9c14ac140dbb003075397b086ad4c7ede0b803b7e  win-x86/node_pdb.7z
a1b7b350faceec615894e3c1e4a812122ef6f1c652bc3e531a6ea07104cfb155  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQGzBAEBCAAdFiEEiQwI24V5Fi/uDfnbi+q0389VXvQFAmMt2FIACgkQi+q0389V
XvS72gwAmWfUJOtxB+bXx3yTHNYTecAPiL43U62VTYW/1Abe36r5VjxxQCHgs4Kn
GZ/hdifpQ56FjnRCGbcdcN0O8MB4Ypp9p8rVOym3bZTIWxyf1bwpluG5iq2hmMU/
PoFZ3jxEWmm33IAJgMXFGQdLYNNessD2Zmd1IYEL13OXN1vYzZGZMTYC9ANlpjb8
9KoNzMQcdI/KgJg+7NR53A0IlSfMRyU7VuVA7+py8guuwYwZOmYMc2o9HgKYpb+c
XXtkXKSuEteIePLN+Sttic83oNzRm67I3Ze2vllw1GYp7uRtLRttwYXpBvceY1HA
SKNtzzXlpMJe6uGgszSJXllXxY698/+mu3ltE76ngjmxKBE4CwVjuK1OSNTyEyj2
dUt8yfH20Ug5EwZLTaW/fy/xL8R06yzCaxCm+LcuSHYdXOHbRylhrqyUtolDIO/S
2vU+cfBnPq5cC1d1lgPnLAwaMi38MWzKCll4B4g2y5Vx79V8npSen719QKYfUUnv
FfWr4Ttk
=kANn
-----END PGP SIGNATURE-----

```
