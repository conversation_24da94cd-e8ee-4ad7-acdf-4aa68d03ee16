---
date: '2022-02-10T18:31:29.510Z'
category: release
title: Node v17.5.0 (Current)
layout: blog-post
author: <PERSON><PERSON>
---

### Notable Changes

#### Add fetch API

Adds experimental support to the fetch API. This adds a `--experimental-fetch`
flag that installs the `fetch`, `Request`, `Response` and `Headers` globals.

- \[[`76a229c4ff`](https://github.com/nodejs/node/commit/76a229c4ff)] - **(SEMVER-MINOR)** **lib**: add fetch (<PERSON><PERSON><PERSON>) [#41749](https://github.com/nodejs/node/pull/41749)

#### Add stream methods

- \[[`1ae648567a`](https://github.com/nodejs/node/commit/1ae648567a)] - **(SEMVER-MINOR)** **stream**: add iterator helper find (linkgoron) [#41849](https://github.com/nodejs/node/pull/41849)
- \[[`62e1a68077`](https://github.com/nodejs/node/commit/62e1a68077)] - **(SEMVER-MINOR)** **stream**: add toArray (Benjamin Gruenbaum) [#41553](https://github.com/nodejs/node/pull/41553)
- \[[`da11b95988`](https://github.com/nodejs/node/commit/da11b95988)] - **(SEMVER-MINOR)** **stream**: add forEach method (Benjamin Gruenbaum) [#41445](https://github.com/nodejs/node/pull/41445)
- \[[`34684a1c89`](https://github.com/nodejs/node/commit/34684a1c89)] - **(SEMVER-MINOR)** **stream**: support some and every (Benjamin Gruenbaum) [#41573](https://github.com/nodejs/node/pull/41573)

#### Other Notable Changes

- \[[`2cae99425b`](https://github.com/nodejs/node/commit/2cae99425b)] - **(SEMVER-MINOR)** **node-api**: add node_api_symbol_for() (Darshan Sen) [#41329](https://github.com/nodejs/node/pull/41329)
- \[[`9c6b078892`](https://github.com/nodejs/node/commit/9c6b078892)] - **module**: unflag esm json modules (Geoffrey Booth) [#41736](https://github.com/nodejs/node/pull/41736)
- \[[`28650eca00`](https://github.com/nodejs/node/commit/28650eca00)] - **deps**: upgrade npm to 8.4.1 (npm team) [#41836](https://github.com/nodejs/node/pull/41836)

### Commits

- \[[`0844935e1f`](https://github.com/nodejs/node/commit/0844935e1f)] - **async_hooks**: fix imports in context example (Yash Ladha) [#39229](https://github.com/nodejs/node/pull/39229)
- \[[`44ca20b7a1`](https://github.com/nodejs/node/commit/44ca20b7a1)] - **benchmark**: avoid input param manipulation (Jithil P Ponnan) [#41741](https://github.com/nodejs/node/pull/41741)
- \[[`ad32ad6cfd`](https://github.com/nodejs/node/commit/ad32ad6cfd)] - **benchmark**: enable no-empty ESLint rule (Rich Trott) [#41831](https://github.com/nodejs/node/pull/41831)
- \[[`28b95a5a9f`](https://github.com/nodejs/node/commit/28b95a5a9f)] - **benchmark**: use Object.hasOwn() instead of hasOwnProperty() (Rich Trott) [#41769](https://github.com/nodejs/node/pull/41769)
- \[[`ca1cd4254a`](https://github.com/nodejs/node/commit/ca1cd4254a)] - **benchmark**: replace hasOwnProperty() with Object.hasOwn() (Rich Trott) [#41724](https://github.com/nodejs/node/pull/41724)
- \[[`6fefd5112a`](https://github.com/nodejs/node/commit/6fefd5112a)] - **(SEMVER-MINOR)** **benchmark**: add `subarray` to `buffer-slice` (Benjamin Gruenbaum) [#41596](https://github.com/nodejs/node/pull/41596)
- \[[`878b2e73bb`](https://github.com/nodejs/node/commit/878b2e73bb)] - **benchmark**: remove unreachable code from crypto/hash-stream-creation (Rich Trott) [#41535](https://github.com/nodejs/node/pull/41535)
- \[[`6064b1df5d`](https://github.com/nodejs/node/commit/6064b1df5d)] - **buffer**: fix atob/btoa no-arg case (Benjamin Gruenbaum) [#41478](https://github.com/nodejs/node/pull/41478)
- \[[`89c327387b`](https://github.com/nodejs/node/commit/89c327387b)] - **(SEMVER-MINOR)** **buffer**: alias `subarray` and `slice` (Benjamin Gruenbaum) [#41596](https://github.com/nodejs/node/pull/41596)
- \[[`adcde6bb78`](https://github.com/nodejs/node/commit/adcde6bb78)] - **build**: check if python is a executable program (himself65) [#36696](https://github.com/nodejs/node/pull/36696)
- \[[`33351765c7`](https://github.com/nodejs/node/commit/33351765c7)] - **build**: enable zoslib installation on z/OS (alexcfyung) [#41493](https://github.com/nodejs/node/pull/41493)
- \[[`e8062bfb92`](https://github.com/nodejs/node/commit/e8062bfb92)] - **build**: add --v8-enable-hugepage flag (Daoming Qiu) [#41487](https://github.com/nodejs/node/pull/41487)
- \[[`8681c837ca`](https://github.com/nodejs/node/commit/8681c837ca)] - **build**: add loong64 configure (Shi Pujin) [#41323](https://github.com/nodejs/node/pull/41323)
- \[[`7c2b5861be`](https://github.com/nodejs/node/commit/7c2b5861be)] - **build**: fix libuv builds for android aarch64 (Darshan Sen) [#41555](https://github.com/nodejs/node/pull/41555)
- \[[`e117c0f86c`](https://github.com/nodejs/node/commit/e117c0f86c)] - **crypto**: check return code from EVP_DigestUpdate (Michael Dawson) [#41800](https://github.com/nodejs/node/pull/41800)
- \[[`9c59abe588`](https://github.com/nodejs/node/commit/9c59abe588)] - **crypto**: fix `webcrypto.subtle` signature (Antoine du Hamel) [#41761](https://github.com/nodejs/node/pull/41761)
- \[[`627f19a84b`](https://github.com/nodejs/node/commit/627f19a84b)] - **crypto**: revise variables for const use instead of let (Rich Trott) [#41614](https://github.com/nodejs/node/pull/41614)
- \[[`9e39e62ae2`](https://github.com/nodejs/node/commit/9e39e62ae2)] - **crypto**: remove wildcard options for checkEmail (Tobias Nießen) [#41599](https://github.com/nodejs/node/pull/41599)
- \[[`4a4c907604`](https://github.com/nodejs/node/commit/4a4c907604)] - **crypto**: adjust types for getRandomValues (LiviaMedeiros) [#41481](https://github.com/nodejs/node/pull/41481)
- \[[`b43faf0fc5`](https://github.com/nodejs/node/commit/b43faf0fc5)] - **crypto**: remove checkIP options argument (Tobias Nießen) [#41571](https://github.com/nodejs/node/pull/41571)
- \[[`076ec47557`](https://github.com/nodejs/node/commit/076ec47557)] - **(SEMVER-MINOR)** **crypto**: support RFC 2818 compatible checkHost (Tobias Nießen) [#41569](https://github.com/nodejs/node/pull/41569)
- \[[`28650eca00`](https://github.com/nodejs/node/commit/28650eca00)] - **deps**: upgrade npm to 8.4.1 (npm-robot) [#41836](https://github.com/nodejs/node/pull/41836)
- \[[`bee4451ed8`](https://github.com/nodejs/node/commit/bee4451ed8)] - **deps**: V8: cherry-pick cc9a8a37445e (Gus Caplan) [#41823](https://github.com/nodejs/node/pull/41823)
- \[[`4b174ce7f6`](https://github.com/nodejs/node/commit/4b174ce7f6)] - **deps**: upgrade npm to 8.3.2 (npm team) [#41621](https://github.com/nodejs/node/pull/41621)
- \[[`0d4f043f0e`](https://github.com/nodejs/node/commit/0d4f043f0e)] - **dgram**: remove unreachable connectState assign (Rongjian Zhang) [#38590](https://github.com/nodejs/node/pull/38590)
- \[[`d641eb7f80`](https://github.com/nodejs/node/commit/d641eb7f80)] - **doc**: use the same case as the section heading (Mestery) [#41876](https://github.com/nodejs/node/pull/41876)
- \[[`ffd1933ad9`](https://github.com/nodejs/node/commit/ffd1933ad9)] - **doc**: use Oxford comma in crypto docs (Tobias Nießen) [#41875](https://github.com/nodejs/node/pull/41875)
- \[[`145a3b8294`](https://github.com/nodejs/node/commit/145a3b8294)] - **doc**: use sentence case in readme introduction (Mestery) [#41874](https://github.com/nodejs/node/pull/41874)
- \[[`f8acca8df8`](https://github.com/nodejs/node/commit/f8acca8df8)] - **doc**: add missing space before hyphen (Mestery) [#41873](https://github.com/nodejs/node/pull/41873)
- \[[`5085813688`](https://github.com/nodejs/node/commit/5085813688)] - **doc**: add stream pipelining note on Http usage (Rafael Silva) [#41796](https://github.com/nodejs/node/pull/41796)
- \[[`0a8995d36b`](https://github.com/nodejs/node/commit/0a8995d36b)] - **doc**: improve SSL_OP_PRIORITIZE_CHACHA description (Tobias Nießen) [#41866](https://github.com/nodejs/node/pull/41866)
- \[[`1c62eb64f6`](https://github.com/nodejs/node/commit/1c62eb64f6)] - **doc**: add missing commas in cluster docs (Tobias Nießen) [#41865](https://github.com/nodejs/node/pull/41865)
- \[[`3676505413`](https://github.com/nodejs/node/commit/3676505413)] - **doc**: add history information for Corepack (Antoine du Hamel) [#41813](https://github.com/nodejs/node/pull/41813)
- \[[`8a42a20b8a`](https://github.com/nodejs/node/commit/8a42a20b8a)] - **doc**: add comments to empty blocks in worker_threads text (Rich Trott) [#41831](https://github.com/nodejs/node/pull/41831)
- \[[`c3721fcf9a`](https://github.com/nodejs/node/commit/c3721fcf9a)] - **doc**: remove empty block from console.timeEnd() example (Rich Trott) [#41831](https://github.com/nodejs/node/pull/41831)
- \[[`451f3331d9`](https://github.com/nodejs/node/commit/451f3331d9)] - **doc**: feature management proposal (Michael Dawson) [#41420](https://github.com/nodejs/node/pull/41420)
- \[[`72a857f56c`](https://github.com/nodejs/node/commit/72a857f56c)] - **doc**: add overhead hints for heap snapshot generation (Gerhard Stöbich) [#41822](https://github.com/nodejs/node/pull/41822)
- \[[`7aeaa27d67`](https://github.com/nodejs/node/commit/7aeaa27d67)] - **doc**: fix X509 CA acronym capitalization (Tobias Nießen) [#41841](https://github.com/nodejs/node/pull/41841)
- \[[`d3a4c62600`](https://github.com/nodejs/node/commit/d3a4c62600)] - **doc**: use sentence case for X509 error codes header (Tobias Nießen) [#41829](https://github.com/nodejs/node/pull/41829)
- \[[`cb16fad0ab`](https://github.com/nodejs/node/commit/cb16fad0ab)] - **doc**: add initial version of maintaining-http.md (Michael Dawson) [#41798](https://github.com/nodejs/node/pull/41798)
- \[[`49f6b97b64`](https://github.com/nodejs/node/commit/49f6b97b64)] - **doc**: add registry numbers for Electron 19 and 20 (Keeley Hammond) [#41814](https://github.com/nodejs/node/pull/41814)
- \[[`4db343bb8e`](https://github.com/nodejs/node/commit/4db343bb8e)] - **doc**: add note about resource type in async_hooks (Tony Gorez) [#41797](https://github.com/nodejs/node/pull/41797)
- \[[`d726a3442c`](https://github.com/nodejs/node/commit/d726a3442c)] - **doc**: use example.com for examples (Ateş Göral) [#41827](https://github.com/nodejs/node/pull/41827)
- \[[`4eb1938f51`](https://github.com/nodejs/node/commit/4eb1938f51)] - **doc**: align tls port types with net port types (Tobias Nießen) [#41799](https://github.com/nodejs/node/pull/41799)
- \[[`e5db13626b`](https://github.com/nodejs/node/commit/e5db13626b)] - **doc**: use UDPv4/UDPv6 consistently with TCPv4/TCPv6 (Tobias Nießen) [#41824](https://github.com/nodejs/node/pull/41824)
- \[[`af641bdec4`](https://github.com/nodejs/node/commit/af641bdec4)] - **doc**: improve wording surrounding TLS 1.3 ciphers (Tobias Nießen) [#41778](https://github.com/nodejs/node/pull/41778)
- \[[`99e0f1458b`](https://github.com/nodejs/node/commit/99e0f1458b)] - **doc**: add format-md step to release guide (Danielle Adams) [#41809](https://github.com/nodejs/node/pull/41809)
- \[[`62f2c078ec`](https://github.com/nodejs/node/commit/62f2c078ec)] - **doc**: add v16 changelog link to iojs changelog (Danielle Adams) [#41808](https://github.com/nodejs/node/pull/41808)
- \[[`c0629b48ce`](https://github.com/nodejs/node/commit/c0629b48ce)] - **doc**: add security-steward rotation information (Michael Dawson) [#41707](https://github.com/nodejs/node/pull/41707)
- \[[`032df4ee17`](https://github.com/nodejs/node/commit/032df4ee17)] - **doc**: use Object.hasOwn() in util doc (Rich Trott) [#41780](https://github.com/nodejs/node/pull/41780)
- \[[`257c5b68d1`](https://github.com/nodejs/node/commit/257c5b68d1)] - **doc**: remove section on "recent" ECDH changes (Tobias Nießen) [#41773](https://github.com/nodejs/node/pull/41773)
- \[[`86644b95a5`](https://github.com/nodejs/node/commit/86644b95a5)] - **doc**: clarify that import also uses main (Ben McCann) [#41720](https://github.com/nodejs/node/pull/41720)
- \[[`4889a3177f`](https://github.com/nodejs/node/commit/4889a3177f)] - **doc**: update modules.md wording (Tobias Hernstig) [#41728](https://github.com/nodejs/node/pull/41728)
- \[[`26c5efef35`](https://github.com/nodejs/node/commit/26c5efef35)] - **doc**: update Mesteery email (Mestery) [#41683](https://github.com/nodejs/node/pull/41683)
- \[[`4339299800`](https://github.com/nodejs/node/commit/4339299800)] - **doc**: avoid incomplete sentence in cluster docs (Tobias Nießen) [#41701](https://github.com/nodejs/node/pull/41701)
- \[[`7f7bcd7757`](https://github.com/nodejs/node/commit/7f7bcd7757)] - **doc**: fix typo in contributing guides (Yoshiki Kurihara) [#41723](https://github.com/nodejs/node/pull/41723)
- \[[`30336c23b5`](https://github.com/nodejs/node/commit/30336c23b5)] - **doc**: improve docs to give descriptive info for the platform property (Harshil jain) [#41650](https://github.com/nodejs/node/pull/41650)
- \[[`29678d66b8`](https://github.com/nodejs/node/commit/29678d66b8)] - **doc**: fix link to npm documentation (Antoine du Hamel) [#41712](https://github.com/nodejs/node/pull/41712)
- \[[`ae34900ccb`](https://github.com/nodejs/node/commit/ae34900ccb)] - **doc**: clarify treatment of non-string base in URL() (Rich Trott) [#41685](https://github.com/nodejs/node/pull/41685)
- \[[`b83e661d30`](https://github.com/nodejs/node/commit/b83e661d30)] - **doc**: fix typo in `technical-priorities.md` (Akhil Marsonya) [#41694](https://github.com/nodejs/node/pull/41694)
- \[[`8e7df048a7`](https://github.com/nodejs/node/commit/8e7df048a7)] - **doc**: remove unadvisable cluster example (Tobias Nießen) [#41668](https://github.com/nodejs/node/pull/41668)
- \[[`0ee468d6ba`](https://github.com/nodejs/node/commit/0ee468d6ba)] - **doc**: document flow for supporting type generation (Michael Dawson) [#41464](https://github.com/nodejs/node/pull/41464)
- \[[`9af2862a8b`](https://github.com/nodejs/node/commit/9af2862a8b)] - **doc**: clarify parameter for napi_get_cb_info (Michael Dawson) [#41635](https://github.com/nodejs/node/pull/41635)
- \[[`a35c6c38f1`](https://github.com/nodejs/node/commit/a35c6c38f1)] - **doc**: revise url.resolve() text (Rich Trott) [#41661](https://github.com/nodejs/node/pull/41661)
- \[[`986dbb3222`](https://github.com/nodejs/node/commit/986dbb3222)] - **doc**: clarify treatment of non-string argument to new URL() (Rich Trott) [#41658](https://github.com/nodejs/node/pull/41658)
- \[[`2bba6cd8b6`](https://github.com/nodejs/node/commit/2bba6cd8b6)] - **doc**: fix documentation for `MODULE_NOT_FOUND` and `ERR_MODULE_NOT_FOUND` (Antoine du Hamel) [#41645](https://github.com/nodejs/node/pull/41645)
- \[[`d40fd755d3`](https://github.com/nodejs/node/commit/d40fd755d3)] - **doc**: improve TLS/SSL introduction (Tobias Nießen) [#41649](https://github.com/nodejs/node/pull/41649)
- \[[`b951764c32`](https://github.com/nodejs/node/commit/b951764c32)] - **doc**: modernize and simplify cluster example (Tobias Nießen) [#41626](https://github.com/nodejs/node/pull/41626)
- \[[`0d6227ceab`](https://github.com/nodejs/node/commit/0d6227ceab)] - **doc**: simplify readline/stdin text (Rich Trott) [#41583](https://github.com/nodejs/node/pull/41583)
- \[[`ffab924444`](https://github.com/nodejs/node/commit/ffab924444)] - **doc**: suggest worker threads in cluster docs (Tobias Nießen) [#41616](https://github.com/nodejs/node/pull/41616)
- \[[`c111a13d22`](https://github.com/nodejs/node/commit/c111a13d22)] - **doc**: add 16 and 17 to previous versions (Antoine du Hamel) [#41646](https://github.com/nodejs/node/pull/41646)
- \[[`81e039fa9d`](https://github.com/nodejs/node/commit/81e039fa9d)] - **doc**: improve `'hex'` Buffer decoding description and examples (Giora Guttsait) [#41598](https://github.com/nodejs/node/pull/41598)
- \[[`09763a2a93`](https://github.com/nodejs/node/commit/09763a2a93)] - **doc**: add note for handling signal events in trace events (Gabriel Trujillo) [#41438](https://github.com/nodejs/node/pull/41438)
- \[[`2e7bf00359`](https://github.com/nodejs/node/commit/2e7bf00359)] - **(SEMVER-MINOR)** **doc**: deprecate `buffer.slice` (Benjamin Gruenbaum) [#41596](https://github.com/nodejs/node/pull/41596)
- \[[`174155dee6`](https://github.com/nodejs/node/commit/174155dee6)] - **doc**: demonstrate dangers of `buffer.slice()` (Shalvah) [#41628](https://github.com/nodejs/node/pull/41628)
- \[[`22484014cd`](https://github.com/nodejs/node/commit/22484014cd)] - **doc**: add missing word in cluster.workers details (Tobias Nießen) [#41624](https://github.com/nodejs/node/pull/41624)
- \[[`86272ca5ac`](https://github.com/nodejs/node/commit/86272ca5ac)] - **doc**: fix async_hooks example in api docs (Akhil Marsonya) [#41609](https://github.com/nodejs/node/pull/41609)
- \[[`5943cb9d1e`](https://github.com/nodejs/node/commit/5943cb9d1e)] - **doc**: fix deprecated alias description in cluster (Tobias Nießen) [#41618](https://github.com/nodejs/node/pull/41618)
- \[[`574c19a8c6`](https://github.com/nodejs/node/commit/574c19a8c6)] - **doc**: fix backticks around 'default' (Tobias Nießen) [#41613](https://github.com/nodejs/node/pull/41613)
- \[[`4780449b4e`](https://github.com/nodejs/node/commit/4780449b4e)] - **doc**: update timingSafeEqual error case (Alex Agranov) [#41507](https://github.com/nodejs/node/pull/41507)
- \[[`9c73bd5610`](https://github.com/nodejs/node/commit/9c73bd5610)] - **doc**: simplify util.TextDecoder example (Rich Trott) [#41574](https://github.com/nodejs/node/pull/41574)
- \[[`e72693160e`](https://github.com/nodejs/node/commit/e72693160e)] - **doc**: move Mesteery to collaborators (Tobias Nießen) [#41597](https://github.com/nodejs/node/pull/41597)
- \[[`952623dff8`](https://github.com/nodejs/node/commit/952623dff8)] - **doc**: fix cjs example code for process.arch (Job) [#41593](https://github.com/nodejs/node/pull/41593)
- \[[`2478b47a08`](https://github.com/nodejs/node/commit/2478b47a08)] - **doc**: remove redunant `await` calls from stream docs (Giora Guttsait) [#41592](https://github.com/nodejs/node/pull/41592)
- \[[`f6b5b8d5bb`](https://github.com/nodejs/node/commit/f6b5b8d5bb)] - **doc**: make contributing info more discoverable (Michael Dawson) [#41408](https://github.com/nodejs/node/pull/41408)
- \[[`6370b4df68`](https://github.com/nodejs/node/commit/6370b4df68)] - **doc**: recommend package exports instead of requiring folders (Antoine du Hamel) [#41381](https://github.com/nodejs/node/pull/41381)
- \[[`b5104e89fc`](https://github.com/nodejs/node/commit/b5104e89fc)] - **doc**: edit async_context context loss text (Rich Trott) [#41550](https://github.com/nodejs/node/pull/41550)
- \[[`9ea8e49032`](https://github.com/nodejs/node/commit/9ea8e49032)] - **doc**: use sentence case for Web Crypto headers (Tobias Nießen) [#41577](https://github.com/nodejs/node/pull/41577)
- \[[`92484d4945`](https://github.com/nodejs/node/commit/92484d4945)] - **doc**: make Web Crypto example spec compliant (Tobias Nießen) [#41556](https://github.com/nodejs/node/pull/41556)
- \[[`60e7437a60`](https://github.com/nodejs/node/commit/60e7437a60)] - **doc**: do not reference SSL when discussing SNI (Tobias Nießen) [#41549](https://github.com/nodejs/node/pull/41549)
- \[[`31d75f2f5d`](https://github.com/nodejs/node/commit/31d75f2f5d)] - **doc**: fix typos in esm.md (Yu) [#41499](https://github.com/nodejs/node/pull/41499)
- \[[`cb4dbc10bc`](https://github.com/nodejs/node/commit/cb4dbc10bc)] - **doc**: adjust assignment in condition in stream doc (Rich Trott) [#41510](https://github.com/nodejs/node/pull/41510)
- \[[`5c700fa8e2`](https://github.com/nodejs/node/commit/5c700fa8e2)] - **doc**: improve Web Crypto headings related to ECC (Tobias Nießen) [#41542](https://github.com/nodejs/node/pull/41542)
- \[[`84292165e7`](https://github.com/nodejs/node/commit/84292165e7)] - **doc**: clarify module system selection (Antoine du Hamel) [#41383](https://github.com/nodejs/node/pull/41383)
- \[[`be63dc5acc`](https://github.com/nodejs/node/commit/be63dc5acc)] - **doc**: remove statement about (EC)DHE performance (Tobias Nießen) [#41528](https://github.com/nodejs/node/pull/41528)
- \[[`c6749bc590`](https://github.com/nodejs/node/commit/c6749bc590)] - **doc,readline**: add missing node protocol in example (Mestery) [#41560](https://github.com/nodejs/node/pull/41560)
- \[[`74bd31213c`](https://github.com/nodejs/node/commit/74bd31213c)] - **domain**: pass opts to `EventEmitter.init` (Chen Gang) [#41414](https://github.com/nodejs/node/pull/41414)
- \[[`e1d5a354c5`](https://github.com/nodejs/node/commit/e1d5a354c5)] - **esm**: improve validation of resolved URLs (Jacob Smith) [#41446](https://github.com/nodejs/node/pull/41446)
- \[[`bfddcb11a5`](https://github.com/nodejs/node/commit/bfddcb11a5)] - **http2**: fix pseudo-headers order (ofir) [#41735](https://github.com/nodejs/node/pull/41735)
- \[[`499171bbd8`](https://github.com/nodejs/node/commit/499171bbd8)] - **http2**: fix no response event on continue request (ofirbarak) [#41739](https://github.com/nodejs/node/pull/41739)
- \[[`2a18859324`](https://github.com/nodejs/node/commit/2a18859324)] - **http2**: fix memory leak on nghttp2 hd threshold (Rafael Silva) [#41502](https://github.com/nodejs/node/pull/41502)
- \[[`59a15e8f1a`](https://github.com/nodejs/node/commit/59a15e8f1a)] - **lib**: refactor to use `validateObject()` validator (Mohammed Keyvanzadeh) [#41845](https://github.com/nodejs/node/pull/41845)
- \[[`4834be373d`](https://github.com/nodejs/node/commit/4834be373d)] - **lib**: add comments to empty catch statements (Rich Trott) [#41831](https://github.com/nodejs/node/pull/41831)
- \[[`76a229c4ff`](https://github.com/nodejs/node/commit/76a229c4ff)] - **(SEMVER-MINOR)** **lib**: add fetch (Michaël Zasso) [#41749](https://github.com/nodejs/node/pull/41749)
- \[[`cedd26a396`](https://github.com/nodejs/node/commit/cedd26a396)] - **lib**: refactor source map stack trace prepare (Mohammed Keyvanzadeh) [#41698](https://github.com/nodejs/node/pull/41698)
- \[[`8ab0540d4a`](https://github.com/nodejs/node/commit/8ab0540d4a)] - **lib**: throw error in structuedClone when no arguments are passed (Giora Guttsait) [#41651](https://github.com/nodejs/node/pull/41651)
- \[[`306dd1c181`](https://github.com/nodejs/node/commit/306dd1c181)] - **lib**: fix consistency of methods that emit warnings (Yoshiki Kurihara) [#41249](https://github.com/nodejs/node/pull/41249)
- \[[`e500d11806`](https://github.com/nodejs/node/commit/e500d11806)] - **lib**: remove erroneous JSDoc entry (Rich Trott) [#41604](https://github.com/nodejs/node/pull/41604)
- \[[`8a99eed7aa`](https://github.com/nodejs/node/commit/8a99eed7aa)] - **lib**: modify `DOMException` to pass WPT (Khaidi Chu) [#41517](https://github.com/nodejs/node/pull/41517)
- \[[`bd5d1583ed`](https://github.com/nodejs/node/commit/bd5d1583ed)] - **meta**: update AUTHORS (Node.js GitHub Bot) [#41868](https://github.com/nodejs/node/pull/41868)
- \[[`f5adf54eb6`](https://github.com/nodejs/node/commit/f5adf54eb6)] - **meta**: update AUTHORS (Node.js GitHub Bot) [#41763](https://github.com/nodejs/node/pull/41763)
- \[[`e5abdf7285`](https://github.com/nodejs/node/commit/e5abdf7285)] - **meta**: update mailmap/AUTHORS info for existing collaborator (Rich Trott) [#41750](https://github.com/nodejs/node/pull/41750)
- \[[`14415fb7e0`](https://github.com/nodejs/node/commit/14415fb7e0)] - **meta**: adjust mailmap/AUTHORS to reflect README change (Rich Trott) [#41751](https://github.com/nodejs/node/pull/41751)
- \[[`60eddadb65`](https://github.com/nodejs/node/commit/60eddadb65)] - **meta**: update AUTHORS (Node.js GitHub Bot) [#41659](https://github.com/nodejs/node/pull/41659)
- \[[`336c18d057`](https://github.com/nodejs/node/commit/336c18d057)] - **meta**: update AUTHORS (Node.js GitHub Bot) [#41548](https://github.com/nodejs/node/pull/41548)
- \[[`9c6b078892`](https://github.com/nodejs/node/commit/9c6b078892)] - **module**: unflag esm json modules (Geoffrey Booth) [#41736](https://github.com/nodejs/node/pull/41736)
- \[[`2cae99425b`](https://github.com/nodejs/node/commit/2cae99425b)] - **(SEMVER-MINOR)** **node-api**: add node_api_symbol_for() (Darshan Sen) [#41329](https://github.com/nodejs/node/pull/41329)
- \[[`14cb9ea93e`](https://github.com/nodejs/node/commit/14cb9ea93e)] - **perf_hooks**: remove useless calls in Histogram (Michael Dawson) [#41579](https://github.com/nodejs/node/pull/41579)
- \[[`e1ae61bce5`](https://github.com/nodejs/node/commit/e1ae61bce5)] - **policy**: revise manifest.js to avoid empty blocks (Rich Trott) [#41831](https://github.com/nodejs/node/pull/41831)
- \[[`d83d55e6e5`](https://github.com/nodejs/node/commit/d83d55e6e5)] - **policy**: check for null instead of falsy in loop (Rich Trott) [#41614](https://github.com/nodejs/node/pull/41614)
- \[[`e11451c4e8`](https://github.com/nodejs/node/commit/e11451c4e8)] - **policy**: replace entries with keys (Mohammed Keyvanzadeh) [#41482](https://github.com/nodejs/node/pull/41482)
- \[[`cfaa1e224d`](https://github.com/nodejs/node/commit/cfaa1e224d)] - **process**: unhandledRejection support more errors (Benjamin Gruenbaum) [#41682](https://github.com/nodejs/node/pull/41682)
- \[[`cf1d3d1565`](https://github.com/nodejs/node/commit/cf1d3d1565)] - **process**: check for null instead of falsy in while loop (Rich Trott) [#41614](https://github.com/nodejs/node/pull/41614)
- \[[`3b45c05a23`](https://github.com/nodejs/node/commit/3b45c05a23)] - **process**: use validateString validator (Mohammed Keyvanzadeh) [#41595](https://github.com/nodejs/node/pull/41595)
- \[[`700f766bea`](https://github.com/nodejs/node/commit/700f766bea)] - **process**: ignore asyncId 0 in exception handler (Anatoli Papirovski) [#41424](https://github.com/nodejs/node/pull/41424)
- \[[`cbcf794562`](https://github.com/nodejs/node/commit/cbcf794562)] - **readline**: improve code coverage for readline promises (Erick Wendel) [#41817](https://github.com/nodejs/node/pull/41817)
- \[[`0a31149202`](https://github.com/nodejs/node/commit/0a31149202)] - **(SEMVER-MINOR)** **readline**: add feature yank and yank pop (Ray) [#41301](https://github.com/nodejs/node/pull/41301)
- \[[`5a4c74c647`](https://github.com/nodejs/node/commit/5a4c74c647)] - **readline**: undo previous edit when get key code 0x1F (Ray) [#41392](https://github.com/nodejs/node/pull/41392)
- \[[`301a4f8781`](https://github.com/nodejs/node/commit/301a4f8781)] - **repl**: check for precise values rather than falsy in loops (Rich Trott) [#41614](https://github.com/nodejs/node/pull/41614)
- \[[`c3d3abf112`](https://github.com/nodejs/node/commit/c3d3abf112)] - **src**: slightly simplify URLHost::ToString (Anna Henningsen) [#41747](https://github.com/nodejs/node/pull/41747)
- \[[`43f2abae7c`](https://github.com/nodejs/node/commit/43f2abae7c)] - **src**: slightly simplify V8CoverageConnection::GetFilename (Anna Henningsen) [#41748](https://github.com/nodejs/node/pull/41748)
- \[[`bbfa58691e`](https://github.com/nodejs/node/commit/bbfa58691e)] - **src**: fix typo in js_native_api_v8.cc (Caio Agiani) [#41764](https://github.com/nodejs/node/pull/41764)
- \[[`1ae648567a`](https://github.com/nodejs/node/commit/1ae648567a)] - **(SEMVER-MINOR)** **stream**: add iterator helper find (linkgoron) [#41849](https://github.com/nodejs/node/pull/41849)
- \[[`07b6bf4e7e`](https://github.com/nodejs/node/commit/07b6bf4e7e)] - **stream**: resume stream on drain (Robert Nagy) [#41848](https://github.com/nodejs/node/pull/41848)
- \[[`f8d2955b71`](https://github.com/nodejs/node/commit/f8d2955b71)] - **stream**: initial port of test262 tests (Benjamin Gruenbaum) [#41775](https://github.com/nodejs/node/pull/41775)
- \[[`6c82276b85`](https://github.com/nodejs/node/commit/6c82276b85)] - **stream**: remove empty block (Rich Trott) [#41831](https://github.com/nodejs/node/pull/41831)
- \[[`e11a079469`](https://github.com/nodejs/node/commit/e11a079469)] - **stream**: use synchronous error validation & validate abort signal option (iMoses) [#41777](https://github.com/nodejs/node/pull/41777)
- \[[`9c2504a066`](https://github.com/nodejs/node/commit/9c2504a066)] - **(SEMVER-MINOR)** **stream**: add reduce (Benjamin Gruenbaum) [#41669](https://github.com/nodejs/node/pull/41669)
- \[[`a05b8328d8`](https://github.com/nodejs/node/commit/a05b8328d8)] - **stream**: use synchronous error validation on iteration helpers (iMoses) [#41652](https://github.com/nodejs/node/pull/41652)
- \[[`311050ebaa`](https://github.com/nodejs/node/commit/311050ebaa)] - **stream**: add asIndexedPairs (Benjamin Gruenbaum) [#41681](https://github.com/nodejs/node/pull/41681)
- \[[`9b1a37979c`](https://github.com/nodejs/node/commit/9b1a37979c)] - **stream**: add drop and take (Benjamin Gruenbaum) [#41630](https://github.com/nodejs/node/pull/41630)
- \[[`b3390b9ddb`](https://github.com/nodejs/node/commit/b3390b9ddb)] - **stream**: never flatten on toArray (Benjamin Gruenbaum) [#41615](https://github.com/nodejs/node/pull/41615)
- \[[`46ec74dcd1`](https://github.com/nodejs/node/commit/46ec74dcd1)] - **(SEMVER-MINOR)** **stream**: support flatMap (Benjamin Gruenbaum) [#41612](https://github.com/nodejs/node/pull/41612)
- \[[`16dec4268c`](https://github.com/nodejs/node/commit/16dec4268c)] - **stream**: check for null instead of falsy in loops (Rich Trott) [#41614](https://github.com/nodejs/node/pull/41614)
- \[[`34684a1c89`](https://github.com/nodejs/node/commit/34684a1c89)] - **(SEMVER-MINOR)** **stream**: support some and every (Benjamin Gruenbaum) [#41573](https://github.com/nodejs/node/pull/41573)
- \[[`62e1a68077`](https://github.com/nodejs/node/commit/62e1a68077)] - **(SEMVER-MINOR)** **stream**: add toArray (Benjamin Gruenbaum) [#41553](https://github.com/nodejs/node/pull/41553)
- \[[`5027e94a8a`](https://github.com/nodejs/node/commit/5027e94a8a)] - **stream**: rename unknown primordial (Mohammed Keyvanzadeh) [#40622](https://github.com/nodejs/node/pull/40622)
- \[[`da11b95988`](https://github.com/nodejs/node/commit/da11b95988)] - **(SEMVER-MINOR)** **stream**: add forEach method (Benjamin Gruenbaum) [#41445](https://github.com/nodejs/node/pull/41445)
- \[[`9226322394`](https://github.com/nodejs/node/commit/9226322394)] - **stream**: avoid function call where possible (Rich Trott) [#41534](https://github.com/nodejs/node/pull/41534)
- \[[`573cafe29d`](https://github.com/nodejs/node/commit/573cafe29d)] - **test**: remove eslint-disable comments from fixtures (Rich Trott) [#41859](https://github.com/nodejs/node/pull/41859)
- \[[`0d9248fe0a`](https://github.com/nodejs/node/commit/0d9248fe0a)] - **test**: remove test-worker-memory flaky designation (Rich Trott) [#41867](https://github.com/nodejs/node/pull/41867)
- \[[`c694dae41c`](https://github.com/nodejs/node/commit/c694dae41c)] - **test**: enable no-empty ESLint rule (Rich Trott) [#41831](https://github.com/nodejs/node/pull/41831)
- \[[`87902a7c17`](https://github.com/nodejs/node/commit/87902a7c17)] - **test**: avoid using Object.prototype methods directly on objects (Rich Trott) [#41801](https://github.com/nodejs/node/pull/41801)
- \[[`ea966404ad`](https://github.com/nodejs/node/commit/ea966404ad)] - **test**: exclude ibm i tests until we resolve (Michael Dawson) [#41812](https://github.com/nodejs/node/pull/41812)
- \[[`9c704b13af`](https://github.com/nodejs/node/commit/9c704b13af)] - **test**: make worker-take-heapsnapshot non-flaky (Michael Dawson) [#41684](https://github.com/nodejs/node/pull/41684)
- \[[`a97c817451`](https://github.com/nodejs/node/commit/a97c817451)] - **test**: mark test-fs-rmdir-recursive flaky on win (Michael Dawson) [#41533](https://github.com/nodejs/node/pull/41533)
- \[[`5f1a59cb41`](https://github.com/nodejs/node/commit/5f1a59cb41)] - **test**: make fs watch test more stable (Benjamin Gruenbaum) [#41715](https://github.com/nodejs/node/pull/41715)
- \[[`abbc06b5ab`](https://github.com/nodejs/node/commit/abbc06b5ab)] - **test**: fix typo in MessageChannel test (Tobias Nießen) [#41746](https://github.com/nodejs/node/pull/41746)
- \[[`94645d8f71`](https://github.com/nodejs/node/commit/94645d8f71)] - **test**: replace commented out expectations with tests (Darshan Sen) [#41667](https://github.com/nodejs/node/pull/41667)
- \[[`351ebf289a`](https://github.com/nodejs/node/commit/351ebf289a)] - **test**: improve stability of oom test (Benjamin Gruenbaum) [#41681](https://github.com/nodejs/node/pull/41681)
- \[[`0267d27a7d`](https://github.com/nodejs/node/commit/0267d27a7d)] - **test**: use Object.hasOwn() where applicable (Rich Trott) [#41664](https://github.com/nodejs/node/pull/41664)
- \[[`cca92107a4`](https://github.com/nodejs/node/commit/cca92107a4)] - **test**: remove unneeded test statement (Rich Trott) [#41663](https://github.com/nodejs/node/pull/41663)
- \[[`30df37f733`](https://github.com/nodejs/node/commit/30df37f733)] - **test**: remove error allowance in debugger test (Jithil P Ponnan) [#41640](https://github.com/nodejs/node/pull/41640)
- \[[`b02bf41e18`](https://github.com/nodejs/node/commit/b02bf41e18)] - **test**: add stream map tests (Benjamin Gruenbaum) [#41642](https://github.com/nodejs/node/pull/41642)
- \[[`12c1ac4d3b`](https://github.com/nodejs/node/commit/12c1ac4d3b)] - **test**: simplify test-gc-http-client (Luigi Pinca) [#41620](https://github.com/nodejs/node/pull/41620)
- \[[`9d99c24d50`](https://github.com/nodejs/node/commit/9d99c24d50)] - **test**: prepare tests for no-cond-assign ESLint rule (Rich Trott) [#41614](https://github.com/nodejs/node/pull/41614)
- \[[`ea6c202436`](https://github.com/nodejs/node/commit/ea6c202436)] - **test**: move test-gc-http-client-onerror to sequential (Luigi Pinca) [#41619](https://github.com/nodejs/node/pull/41619)
- \[[`55adcb7f12`](https://github.com/nodejs/node/commit/55adcb7f12)] - **test**: fix typo in test-stream-toArray (Tobias Nießen) [#41634](https://github.com/nodejs/node/pull/41634)
- \[[`506a0dde0e`](https://github.com/nodejs/node/commit/506a0dde0e)] - **test**: improve test coverage of internal/worker/io (Yoshiki Kurihara) [#41511](https://github.com/nodejs/node/pull/41511)
- \[[`6de8e51437`](https://github.com/nodejs/node/commit/6de8e51437)] - **test**: add DataView test entry for whatwg (Mohammed Keyvanzadeh) [#40622](https://github.com/nodejs/node/pull/40622)
- \[[`f947f72b39`](https://github.com/nodejs/node/commit/f947f72b39)] - **test**: improve util-format code coverage (Rich Trott) [#41572](https://github.com/nodejs/node/pull/41572)
- \[[`38e4b11391`](https://github.com/nodejs/node/commit/38e4b11391)] - **test**: fix typo in test_js_native_api_v8 (Tobias Nießen) [#41584](https://github.com/nodejs/node/pull/41584)
- \[[`5e3f751260`](https://github.com/nodejs/node/commit/5e3f751260)] - **test**: add missing await in fs-rm/fs-rmdir tests (Benjamin Coe) [#41545](https://github.com/nodejs/node/pull/41545)
- \[[`c0aec6783a`](https://github.com/nodejs/node/commit/c0aec6783a)] - **test**: increase coverage for stream writable (Rich Trott) [#41486](https://github.com/nodejs/node/pull/41486)
- \[[`ef7c79822f`](https://github.com/nodejs/node/commit/ef7c79822f)] - **test**: add coverage for util.inspect() (Rich Trott) [#41527](https://github.com/nodejs/node/pull/41527)
- \[[`2b4e46f07d`](https://github.com/nodejs/node/commit/2b4e46f07d)] - **timers**: check for nullish instead of falsy in loops (Rich Trott) [#41614](https://github.com/nodejs/node/pull/41614)
- \[[`0d471fb0d4`](https://github.com/nodejs/node/commit/0d471fb0d4)] - **tools**: enable no-empty ESLint rule (Rich Trott) [#41831](https://github.com/nodejs/node/pull/41831)
- \[[`72f02d87b9`](https://github.com/nodejs/node/commit/72f02d87b9)] - **tools**: update lint-md-dependencies to rollup\@2.67.0 (Node.js GitHub Bot) [#41737](https://github.com/nodejs/node/pull/41737)
- \[[`32cdae7f65`](https://github.com/nodejs/node/commit/32cdae7f65)] - **tools**: update doc to rehype-stringify\@9.0.3 (Node.js GitHub Bot) [#41854](https://github.com/nodejs/node/pull/41854)
- \[[`e6a1896ec1`](https://github.com/nodejs/node/commit/e6a1896ec1)] - **tools**: update eslint to 8.8.0 (Node.js GitHub Bot) [#41738](https://github.com/nodejs/node/pull/41738)
- \[[`3190e365f0`](https://github.com/nodejs/node/commit/3190e365f0)] - **tools**: use Set instead of { \[key]: true } object (Tobias Nießen) [#41695](https://github.com/nodejs/node/pull/41695)
- \[[`3f4ce07be9`](https://github.com/nodejs/node/commit/3f4ce07be9)] - **tools**: add compile_commands to ignore file (Yash Ladha) [#41580](https://github.com/nodejs/node/pull/41580)
- \[[`5fe7cfabaa`](https://github.com/nodejs/node/commit/5fe7cfabaa)] - **tools**: use Set instead of { \[key]: true } object (Tobias Nießen) [#41675](https://github.com/nodejs/node/pull/41675)
- \[[`d33d1899c4`](https://github.com/nodejs/node/commit/d33d1899c4)] - **tools**: fix typo in `tools/code_cache/README.md` (Tobias Nießen) [#41657](https://github.com/nodejs/node/pull/41657)
- \[[`ae7df178fd`](https://github.com/nodejs/node/commit/ae7df178fd)] - **tools**: enable no-cond-assign-ESLint rule (Rich Trott) [#41614](https://github.com/nodejs/node/pull/41614)
- \[[`9202b86814`](https://github.com/nodejs/node/commit/9202b86814)] - **tools**: update lint-md-dependencies to rollup\@2.65.0 (Node.js GitHub Bot) [#41638](https://github.com/nodejs/node/pull/41638)
- \[[`57ada37a1f`](https://github.com/nodejs/node/commit/57ada37a1f)] - **tools**: increase maximum line length to 120 characters (Rich Trott) [#41586](https://github.com/nodejs/node/pull/41586)
- \[[`3084dff702`](https://github.com/nodejs/node/commit/3084dff702)] - **tools**: add missing `.PHONY` and `.NOTPARALLEL` targets in `Makefile` (Antoine du Hamel) [#41515](https://github.com/nodejs/node/pull/41515)
- \[[`544267feb2`](https://github.com/nodejs/node/commit/544267feb2)] - **tools**: update lint-md-dependencies (Node.js GitHub Bot) [#41440](https://github.com/nodejs/node/pull/41440)
- \[[`127d2fae62`](https://github.com/nodejs/node/commit/127d2fae62)] - **tools**: bump eslint from 8.6.0 to 8.7.0 (Rich Trott) [#41570](https://github.com/nodejs/node/pull/41570)
- \[[`ad5d7c7b06`](https://github.com/nodejs/node/commit/ad5d7c7b06)] - **tools**: update doc to highlight.js\@11.4.0 to-vfile\@7.2.3 (Node.js GitHub Bot) [#41441](https://github.com/nodejs/node/pull/41441)
- \[[`34dfc60742`](https://github.com/nodejs/node/commit/34dfc60742)] - **tools,test**: enable no-prototype-builtins (Rich Trott) [#41801](https://github.com/nodejs/node/pull/41801)
- \[[`aee74c8156`](https://github.com/nodejs/node/commit/aee74c8156)] - **util**: use hasOwnProperty() primordial (Rich Trott) [#41692](https://github.com/nodejs/node/pull/41692)
- \[[`2b082def2e`](https://github.com/nodejs/node/commit/2b082def2e)] - **util**: remove unused fast path in internal debuglog (Rich Trott) [#41605](https://github.com/nodejs/node/pull/41605)
- \[[`0e35d01a4a`](https://github.com/nodejs/node/commit/0e35d01a4a)] - **util**: check for null instead of flasy in loop (Rich Trott) [#41614](https://github.com/nodejs/node/pull/41614)
- \[[`869cbb7b25`](https://github.com/nodejs/node/commit/869cbb7b25)] - **(SEMVER-MINOR)** **worker**: graduate get/setEnvironmentData APIs (James M Snell) [#41272](https://github.com/nodejs/node/pull/41272)

Windows 32-bit Installer: https://nodejs.org/dist/v17.5.0/node-v17.5.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v17.5.0/node-v17.5.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v17.5.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v17.5.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v17.5.0/node-v17.5.0.pkg \
macOS Apple Silicon 64-bit Binary: https://nodejs.org/dist/v17.5.0/node-v17.5.0-darwin-arm64.tar.gz \
macOS Intel 64-bit Binary: https://nodejs.org/dist/v17.5.0/node-v17.5.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v17.5.0/node-v17.5.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v17.5.0/node-v17.5.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v17.5.0/node-v17.5.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v17.5.0/node-v17.5.0-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v17.5.0/node-v17.5.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v17.5.0/node-v17.5.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v17.5.0/node-v17.5.0.tar.gz \
Other release files: https://nodejs.org/dist/v17.5.0/ \
Documentation: https://nodejs.org/docs/v17.5.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

5608146533a5ff53d9fc737e1edef90da564ff89c979f15f317c0af861747da6  node-v17.5.0-aix-ppc64.tar.gz
b5b5d8557d1556cc8224e0e0b0711b6baba79be74b22e6eaf9870ac71fcd757a  node-v17.5.0-darwin-arm64.tar.gz
74f7523928496c6deb5d382291848a9f9ebe0a44aee3231a5a28f7a28964366e  node-v17.5.0-darwin-arm64.tar.xz
c5863c6ecdd6a3a1d14ef5d75135a82d33b68afb7a6a47558b86c72463d26877  node-v17.5.0-darwin-x64.tar.gz
2cc535ecf92d60bfb90919a3518e4b38c0cc7f83282a16ffdb462311fb408dd2  node-v17.5.0-darwin-x64.tar.xz
9de3dbe9b9e02b112500ecf1c5b367ca185f2121a1f2b3a6a4756423a083db64  node-v17.5.0-headers.tar.gz
e5f9a098b01a72b39e59fefba4341d69fe42357f6e1e37a82f81bcaa1443dde1  node-v17.5.0-headers.tar.xz
82e46024637fb887f870ac4673072f7a3b3de3a8a6bf9a0945dbf28cf1a3aed8  node-v17.5.0-linux-arm64.tar.gz
72688d14df49daf59bb7c2b820a0d03ba2c27964d916dec7e86b04426438c2cb  node-v17.5.0-linux-arm64.tar.xz
88ccbafce492712a840c83e784b67cf913b63bb4f7bf587f7be4f8425241fd4a  node-v17.5.0-linux-armv7l.tar.gz
d7226c3cbe99f9a418dd73fc7344718ee18a0a8dbcbae63378d6531a7285b63f  node-v17.5.0-linux-armv7l.tar.xz
f4acac166d90a2ac1b18668a5be9fa348244c21ad22b622ceba64480d01ce93e  node-v17.5.0-linux-ppc64le.tar.gz
1f4ad504568cf62d51cee4f0ccf5117b90fbc7ba0b972aa7edda04bc296ae1e2  node-v17.5.0-linux-ppc64le.tar.xz
ab674a250d10bd0751faca1b2f50e216ed9dd3ba4ed02e417f2743210b9f5294  node-v17.5.0-linux-s390x.tar.gz
f04c8e410092815ba1320c7a6ff6e60e8bc233efc03b55cb83d517cfe30681a0  node-v17.5.0-linux-s390x.tar.xz
86fabd8177686b5f2f4ec8ac81e88a008b74a34e789080188f1c13f5d665ca6e  node-v17.5.0-linux-x64.tar.gz
17b4659f10d50779943959ed16a68c909188f82a8bbf97fd741ca6f31bafb0ee  node-v17.5.0-linux-x64.tar.xz
14496b2d1e140967a0d5df75cbd80cd3f729ff1d7d7223716f98291f333dd41f  node-v17.5.0.pkg
e6bc52ba176be22806840f07db1f604e4e813d26f69d565683cbf0d056208795  node-v17.5.0.tar.gz
9b24e6830576c57ee36ba48333def8be575232987b4da939568b7b89f773cdc3  node-v17.5.0.tar.xz
51d65f7e36989791ed3a2c7838d2c3f11ce32db444f92b25e9a9b5da10602724  node-v17.5.0-win-x64.7z
ddc538ac465002f9e169416f5ad4190b153a3f59754dbbbcaae92578eb4df8ec  node-v17.5.0-win-x64.zip
f4153dd5f012f8be64b0a8489b5117c7ea0acecdb22aefadff4f4b644d5fa26e  node-v17.5.0-win-x86.7z
69c70657ae7da37caf9cb712a2ba72fafefb85f0b342ae14bc0501c184d60a41  node-v17.5.0-win-x86.zip
b443c6ee36a4a2d43cccc39513f8f3d9e56cfeee0ff461610faceb41f2c930cc  node-v17.5.0-x64.msi
4746c0fd19bc801780181702e501ac5f23236413c23b022e1180320da7fceff8  node-v17.5.0-x86.msi
5d2630ec67ae7942dce1f40d83a2c67b0b9a47f0162bc6ebc6c84926b8794ca7  win-x64/node.exe
aec298781843ce67339e8d169d873af8e50ad31a5fce075e7b142341b119df81  win-x64/node.lib
b80853b10d6b43c09898de1e569e04d98a64f8c23f8a67dfed25658370068905  win-x64/node_pdb.7z
d8d0ca73586a3d95c2af99521921660923204e2b12437c3c042db0128b51c49c  win-x64/node_pdb.zip
68dcf231f16f2371c1b18f0dbfeddbb25e8e48320a9cb80c2657ebe760a73093  win-x86/node.exe
4007a33c8d98c82340bb0a76f955ffa8db9738cc0a2b00cdcd4b3776922f5a18  win-x86/node.lib
56c7b3d98ec2216065b795549cfc34da5770d989266dad2864e5293f6e9fb93e  win-x86/node_pdb.7z
e6a5ff18f5274baa647bc229c3b650ba8a944d325a50c01d962fcb7f47ac0c63  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEEI9StI21e7DMQ5spl7AUGb2S+AoFAmIFWZMACgkQl7AUGb2S
+ArsGw//cu03nO+BBBGB5uUiceo8HpNbrKT4EDuaKr6y5486MVuuYHX1y79A9p5j
SVts0nGwKq+22dzgNVcX+shvvxp+h8YsmazX/BbCWpgcCGs5ePGRyeVTW1e7H1sY
9Jdmk8PRvBhKGxXPQ4sDiXU/E02M30nU/ZDlKHrB3RguvAaIqfxU4YbRejN0VWy8
3Vx+xHJjLZqMx4hGlanChyD5IzrJXJZkwSHlq5uTc7fEDeNwkgN9uHzFaGZCXMBD
p7vtV3h0PbJ5J6prb2s2FT2uQoq+puGrG68sVpBvjYhlCBP82r0D9na6almYakxF
cvGcKNXTm/n3DA2a1KT8b+Ao5fTUPNRokGgPwXmVkSAihYPfDcxV6fAFObS4Idh+
VvsrCLMT9TwRUjtdUp7ocVJXVAwQHyQdYfSD7VicShzCIcXxqFLmxCt4rAuFWSC+
2rs3kj0f3awC6l4uBW2pqET0fhfLvS4yphxkuij3WFNqSHJ0O5AH3skusKrhIgCe
AIoC4TfOVhh00EzGrhUOt/4TcSq6TjQRpgf+sN0zVfJh3seBaxAJztgh27FJl6ix
lS/emm7tzbXkmL0hOP0WNWjo0q0xy2+6a42i4+68j/FrDegEmKSVM/frXO08wWtk
RFQz3X5GSTEYGTCpFGxEWHDdivu8MtVpgOVGSOoWHRw/V/X2/Ss=
=RZgI
-----END PGP SIGNATURE-----

```
