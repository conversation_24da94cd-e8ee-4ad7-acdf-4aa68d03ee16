---
date: '2024-05-02T14:35:52.972Z'
category: release
title: Node v22.1.0 (Current)
layout: blog-post
author: <PERSON><PERSON><PERSON>
---

## 2024-05-02, Version 22.1.0 (Current), @targos prepared by @aduh95

### module: implement `NODE_COMPILE_CACHE` for automatic on-disk code caching

This patch implements automatic on-disk code caching that can be enabled
via an environment variable `NODE_COMPILE_CACHE=/path/to/cache/dir`.

When set, whenever Node.js compiles a CommonJS or a ECMAScript Module,
it will use on-disk [V8 code cache](https://v8.dev/blog/code-caching-for-devs)
persisted in the specified directory
to speed up the compilation. This may slow down the first load of a
module graph, but subsequent loads of the same module graph may get
a significant speedup if the contents of the modules do not change.
Locally, this speeds up loading of `test/fixtures/snapshot/typescript.js`
from \~130ms to \~80ms.

To clean up the generated code cache, simply remove the directory.
It will be recreated the next time the same directory is used for
`NODE_COMPILE_CACHE`.

Compilation cache generated by one version of Node.js may not be used
by a different version of Node.js. Cache generated by different versions
of Node.js will be stored separately if the same directory is used
to persist the cache, so they can co-exist.

Caveat: currently when using this with V8 JavaScript code coverage, the
coverage being collected by V8 may be less precise in functions that are
deserialized from the code cache. It's recommended to turn this off when
running tests to generate precise coverage.

Contributed by Joyee Cheung in [#52535](https://github.com/nodejs/node/pull/52535).

### Other Notable Changes

- \[[`44ee04cf9f`](https://github.com/nodejs/node/commit/44ee04cf9f)] - **buffer**: improve `base64` and `base64url` performance (Yagiz Nizipli) [#52428](https://github.com/nodejs/node/pull/52428)
- \[[`3c37ce5710`](https://github.com/nodejs/node/commit/3c37ce5710)] - **(SEMVER-MINOR)** **dns**: add order option and support ipv6first (Paolo Insogna) [#52492](https://github.com/nodejs/node/pull/52492)
- \[[`3026401be1`](https://github.com/nodejs/node/commit/3026401be1)] - **events,doc**: mark CustomEvent as stable (Daeyeon Jeong) [#52618](https://github.com/nodejs/node/pull/52618)
- \[[`64428dc1c9`](https://github.com/nodejs/node/commit/64428dc1c9)] - **(SEMVER-MINOR)** **lib, url**: add a `windows` option to path parsing (Aviv Keller) [#52509](https://github.com/nodejs/node/pull/52509)
- \[[`d79ae74f71`](https://github.com/nodejs/node/commit/d79ae74f71)] - **(SEMVER-MINOR)** **net**: add CLI option for autoSelectFamilyAttemptTimeout (Paolo Insogna) [#52474](https://github.com/nodejs/node/pull/52474)
- \[[`43fa6a1a45`](https://github.com/nodejs/node/commit/43fa6a1a45)] - **(SEMVER-MINOR)** **src**: add `string_view` overload to snapshot FromBlob (Anna Henningsen) [#52595](https://github.com/nodejs/node/pull/52595)
- \[[`c6fe433d42`](https://github.com/nodejs/node/commit/c6fe433d42)] - **src,permission**: throw async errors on async APIs (Rafael Gonzaga) [#52730](https://github.com/nodejs/node/pull/52730)
- \[[`e247a61d15`](https://github.com/nodejs/node/commit/e247a61d15)] - **(SEMVER-MINOR)** **test_runner**: add --test-skip-pattern cli option (Aviv Keller) [#52529](https://github.com/nodejs/node/pull/52529)
- \[[`9b18df9dcb`](https://github.com/nodejs/node/commit/9b18df9dcb)] - **(SEMVER-MINOR)** **url**: implement parse method for safer URL parsing (Ali Hassan) [#52280](https://github.com/nodejs/node/pull/52280)

### Commits

- \[[`35643c18c0`](https://github.com/nodejs/node/commit/35643c18c0)] - **benchmark**: reduce the buffer size for blob (Debadree Chatterjee) [#52548](https://github.com/nodejs/node/pull/52548)
- \[[`7cdfe8a3fc`](https://github.com/nodejs/node/commit/7cdfe8a3fc)] - **benchmark**: inherit stdio/stderr instead of pipe (Ali Hassan) [#52456](https://github.com/nodejs/node/pull/52456)
- \[[`7b82c17f22`](https://github.com/nodejs/node/commit/7b82c17f22)] - **benchmark**: add ipc support to spawn stdio config (Ali Hassan) [#52456](https://github.com/nodejs/node/pull/52456)
- \[[`dfda6fed61`](https://github.com/nodejs/node/commit/dfda6fed61)] - **buffer**: add missing ARG_TYPE(ArrayBuffer) for isUtf8 (Jungku Lee) [#52477](https://github.com/nodejs/node/pull/52477)
- \[[`44ee04cf9f`](https://github.com/nodejs/node/commit/44ee04cf9f)] - **buffer**: improve `base64` and `base64url` performance (Yagiz Nizipli) [#52428](https://github.com/nodejs/node/pull/52428)
- \[[`c64a1a3b89`](https://github.com/nodejs/node/commit/c64a1a3b89)] - **build**: fix typo in node.gyp (Michaël Zasso) [#52719](https://github.com/nodejs/node/pull/52719)
- \[[`4f713fbc2e`](https://github.com/nodejs/node/commit/4f713fbc2e)] - **build**: fix headers install for shared mode on Win (Segev Finer) [#52442](https://github.com/nodejs/node/pull/52442)
- \[[`4baeb7b21d`](https://github.com/nodejs/node/commit/4baeb7b21d)] - **build**: fix arm64 cross-compilation bug on non-arm machines (Mahdi Sharifi) [#52559](https://github.com/nodejs/node/pull/52559)
- \[[`d5cd468ce8`](https://github.com/nodejs/node/commit/d5cd468ce8)] - **build,tools,node-api**: fix building node-api tests for Windows Debug (Vladimir Morozov) [#52632](https://github.com/nodejs/node/pull/52632)
- \[[`910533fcfd`](https://github.com/nodejs/node/commit/910533fcfd)] - **crypto**: simplify assertions in Safe\*Print (David Benjamin) [#49709](https://github.com/nodejs/node/pull/49709)
- \[[`61e1ac0b8c`](https://github.com/nodejs/node/commit/61e1ac0b8c)] - **crypto**: enable NODE_EXTRA_CA_CERTS with BoringSSL (Shelley Vohr) [#52217](https://github.com/nodejs/node/pull/52217)
- \[[`6e98eee256`](https://github.com/nodejs/node/commit/6e98eee256)] - **deps**: upgrade npm to 10.7.0 (npm team) [#52767](https://github.com/nodejs/node/pull/52767)
- \[[`27a5f9418c`](https://github.com/nodejs/node/commit/27a5f9418c)] - **deps**: V8: cherry-pick 500de8bd371b (Richard Lau) [#52676](https://github.com/nodejs/node/pull/52676)
- \[[`3b422ddcea`](https://github.com/nodejs/node/commit/3b422ddcea)] - **deps**: update corepack to 0.28.0 (Node.js GitHub Bot) [#52616](https://github.com/nodejs/node/pull/52616)
- \[[`d40e4d4c42`](https://github.com/nodejs/node/commit/d40e4d4c42)] - **deps**: update ada to 2.7.8 (Node.js GitHub Bot) [#52517](https://github.com/nodejs/node/pull/52517)
- \[[`5b52a4870a`](https://github.com/nodejs/node/commit/5b52a4870a)] - **deps**: update icu to 75.1 (Node.js GitHub Bot) [#52573](https://github.com/nodejs/node/pull/52573)
- \[[`80cbe72c1f`](https://github.com/nodejs/node/commit/80cbe72c1f)] - **deps**: update undici to 6.13.0 (Node.js GitHub Bot) [#52493](https://github.com/nodejs/node/pull/52493)
- \[[`9a44059055`](https://github.com/nodejs/node/commit/9a44059055)] - **deps**: update zlib to 1.3.0.1-motley-7d77fb7 (Node.js GitHub Bot) [#52516](https://github.com/nodejs/node/pull/52516)
- \[[`d67a9a5360`](https://github.com/nodejs/node/commit/d67a9a5360)] - **deps**: update minimatch to 9.0.4 (Node.js GitHub Bot) [#52524](https://github.com/nodejs/node/pull/52524)
- \[[`8738b89971`](https://github.com/nodejs/node/commit/8738b89971)] - **deps**: upgrade npm to 10.5.2 (npm team) [#52458](https://github.com/nodejs/node/pull/52458)
- \[[`8e4fd2842b`](https://github.com/nodejs/node/commit/8e4fd2842b)] - **deps,src**: simplify base64 encoding (Daniel Lemire) [#52714](https://github.com/nodejs/node/pull/52714)
- \[[`3c37ce5710`](https://github.com/nodejs/node/commit/3c37ce5710)] - **(SEMVER-MINOR)** **dns**: add order option and support ipv6first (Paolo Insogna) [#52492](https://github.com/nodejs/node/pull/52492)
- \[[`3987a28a9e`](https://github.com/nodejs/node/commit/3987a28a9e)] - **doc**: update process.versions properties (ishabi) [#52736](https://github.com/nodejs/node/pull/52736)
- \[[`c0b58e07f1`](https://github.com/nodejs/node/commit/c0b58e07f1)] - **doc**: remove mold use on mac for speeding up build (Cong Zhang) [#52252](https://github.com/nodejs/node/pull/52252)
- \[[`9a032cf6e2`](https://github.com/nodejs/node/commit/9a032cf6e2)] - **doc**: remove relative limitation to pm (Rafael Gonzaga) [#52648](https://github.com/nodejs/node/pull/52648)
- \[[`90c6e77238`](https://github.com/nodejs/node/commit/90c6e77238)] - **doc**: fix info string causing duplicated code blocks (Mathieu Leenhardt) [#52660](https://github.com/nodejs/node/pull/52660)
- \[[`4d577fa048`](https://github.com/nodejs/node/commit/4d577fa048)] - **doc**: add .gitattributes for md files (Hüseyin Açacak) [#52161](https://github.com/nodejs/node/pull/52161)
- \[[`04c8e110e5`](https://github.com/nodejs/node/commit/04c8e110e5)] - **doc**: run license-builder (github-actions\[bot]) [#52631](https://github.com/nodejs/node/pull/52631)
- \[[`**********`](https://github.com/nodejs/node/commit/**********)] - **doc**: add info on contributor spotlight program (Michael Dawson) [#52598](https://github.com/nodejs/node/pull/52598)
- \[[`eeb80ad836`](https://github.com/nodejs/node/commit/eeb80ad836)] - **doc**: correct unsafe URL example in http docs (Malte Legenhausen) [#52555](https://github.com/nodejs/node/pull/52555)
- \[[`c83526a688`](https://github.com/nodejs/node/commit/c83526a688)] - **doc**: replace U+00A0 with U+0020 (Luigi Pinca) [#52590](https://github.com/nodejs/node/pull/52590)
- \[[`31831e9db8`](https://github.com/nodejs/node/commit/31831e9db8)] - **doc**: sort options alphabetically (Luigi Pinca) [#52589](https://github.com/nodejs/node/pull/52589)
- \[[`a93f5d4aaa`](https://github.com/nodejs/node/commit/a93f5d4aaa)] - **doc**: correct stream.finished changes (KaKa) [#52551](https://github.com/nodejs/node/pull/52551)
- \[[`27ffa35540`](https://github.com/nodejs/node/commit/27ffa35540)] - **doc**: add RedYetiDev to triage team (Aviv Keller) [#52556](https://github.com/nodejs/node/pull/52556)
- \[[`63cc2b870e`](https://github.com/nodejs/node/commit/63cc2b870e)] - **doc**: fix issue detected in markdown lint update (Rich Trott) [#52566](https://github.com/nodejs/node/pull/52566)
- \[[`7e93c4892b`](https://github.com/nodejs/node/commit/7e93c4892b)] - **doc**: update test runner coverage limitations (Moshe Atlow) [#52515](https://github.com/nodejs/node/pull/52515)
- \[[`3026401be1`](https://github.com/nodejs/node/commit/3026401be1)] - **events,doc**: mark CustomEvent as stable (Daeyeon Jeong) [#52618](https://github.com/nodejs/node/pull/52618)
- \[[`c6e0fe2f22`](https://github.com/nodejs/node/commit/c6e0fe2f22)] - **fs**: allow setting Stat date properties (Nicolò Ribaudo) [#52708](https://github.com/nodejs/node/pull/52708)
- \[[`f23fa1de72`](https://github.com/nodejs/node/commit/f23fa1de72)] - **fs**: fix read / readSync positional offset types (Ruy Adorno) [#52603](https://github.com/nodejs/node/pull/52603)
- \[[`a7e03d301a`](https://github.com/nodejs/node/commit/a7e03d301a)] - **fs**: fixes recursive fs.watch crash on Linux when deleting files (Matteo Collina) [#52349](https://github.com/nodejs/node/pull/52349)
- \[[`d5ecb6cd00`](https://github.com/nodejs/node/commit/d5ecb6cd00)] - **http2**: fix excessive CPU usage when using `allowHTTP1=true` (Eugene) [#52713](https://github.com/nodejs/node/pull/52713)
- \[[`d1adc9b140`](https://github.com/nodejs/node/commit/d1adc9b140)] - **lib**: enforce ASCII order in error code imports (Antoine du Hamel) [#52625](https://github.com/nodejs/node/pull/52625)
- \[[`9ffdcade37`](https://github.com/nodejs/node/commit/9ffdcade37)] - **lib**: use predefined variable instead of bit operation (Deokjin Kim) [#52580](https://github.com/nodejs/node/pull/52580)
- \[[`fdcde845ee`](https://github.com/nodejs/node/commit/fdcde845ee)] - **lib**: refactor lazy loading of undici for fetch method (Victor Chen) [#52275](https://github.com/nodejs/node/pull/52275)
- \[[`f6145aa2ca`](https://github.com/nodejs/node/commit/f6145aa2ca)] - **lib**: convert WeakMaps in cjs loader with private symbol properties (Chengzhong Wu) [#52095](https://github.com/nodejs/node/pull/52095)
- \[[`014bf01efc`](https://github.com/nodejs/node/commit/014bf01efc)] - **lib**: replace string prototype usage with alternatives (Aviv Keller) [#52440](https://github.com/nodejs/node/pull/52440)
- \[[`dc399ddd03`](https://github.com/nodejs/node/commit/dc399ddd03)] - **lib, doc**: rename readme.md to README.md (Aviv Keller) [#52471](https://github.com/nodejs/node/pull/52471)
- \[[`64428dc1c9`](https://github.com/nodejs/node/commit/64428dc1c9)] - **(SEMVER-MINOR)** **lib, url**: add a `windows` option to path parsing (Aviv Keller) [#52509](https://github.com/nodejs/node/pull/52509)
- \[[`9b2b6abb62`](https://github.com/nodejs/node/commit/9b2b6abb62)] - **lib,src**: iterate module requests of a module wrap in JS (Chengzhong Wu) [#52058](https://github.com/nodejs/node/pull/52058)
- \[[`896a80e366`](https://github.com/nodejs/node/commit/896a80e366)] - **meta**: standardize regex (Aviv Keller) [#52693](https://github.com/nodejs/node/pull/52693)
- \[[`20c07e922e`](https://github.com/nodejs/node/commit/20c07e922e)] - **meta**: move one or more collaborators to emeritus (Node.js GitHub Bot) [#52633](https://github.com/nodejs/node/pull/52633)
- \[[`e70d8a4fa9`](https://github.com/nodejs/node/commit/e70d8a4fa9)] - **meta**: move one or more collaborators to emeritus (Node.js GitHub Bot) [#52457](https://github.com/nodejs/node/pull/52457)
- \[[`20ab8f2a88`](https://github.com/nodejs/node/commit/20ab8f2a88)] - **module**: support ESM detection in the CJS loader (Joyee Cheung) [#52047](https://github.com/nodejs/node/pull/52047)
- \[[`544c602b75`](https://github.com/nodejs/node/commit/544c602b75)] - **module**: skip NODE_COMPILE_CACHE when policy is enabled (Joyee Cheung) [#52577](https://github.com/nodejs/node/pull/52577)
- \[[`3df3afc284`](https://github.com/nodejs/node/commit/3df3afc284)] - **module**: detect ESM syntax by trying to recompile as SourceTextModule (Joyee Cheung) [#52413](https://github.com/nodejs/node/pull/52413)
- \[[`4d77fd2c46`](https://github.com/nodejs/node/commit/4d77fd2c46)] - **(SEMVER-MINOR)** **module**: implement NODE_COMPILE_CACHE for automatic on-disk code caching (Joyee Cheung) [#52535](https://github.com/nodejs/node/pull/52535)
- \[[`9794d21b07`](https://github.com/nodejs/node/commit/9794d21b07)] - **module**: fix submodules loaded by require() and import() (Joyee Cheung) [#52487](https://github.com/nodejs/node/pull/52487)
- \[[`b00766d9e7`](https://github.com/nodejs/node/commit/b00766d9e7)] - **module**: tidy code and comments (Jacob Smith) [#52437](https://github.com/nodejs/node/pull/52437)
- \[[`d79ae74f71`](https://github.com/nodejs/node/commit/d79ae74f71)] - **(SEMVER-MINOR)** **net**: add CLI option for autoSelectFamilyAttemptTimeout (Paolo Insogna) [#52474](https://github.com/nodejs/node/pull/52474)
- \[[`b17cfea289`](https://github.com/nodejs/node/commit/b17cfea289)] - **node-api**: address coverity report (Michael Dawson) [#52584](https://github.com/nodejs/node/pull/52584)
- \[[`1fca8baac1`](https://github.com/nodejs/node/commit/1fca8baac1)] - **node-api**: copy external type tags when they are set (Niels Martignène) [#52426](https://github.com/nodejs/node/pull/52426)
- \[[`d086ab42a1`](https://github.com/nodejs/node/commit/d086ab42a1)] - **quic**: address recent coverity warnings (Michael Dawson) [#52647](https://github.com/nodejs/node/pull/52647)
- \[[`fb4edf70cf`](https://github.com/nodejs/node/commit/fb4edf70cf)] - **quic**: rework TLSContext, additional cleanups (James M Snell) [#51340](https://github.com/nodejs/node/pull/51340)
- \[[`0c58d0319b`](https://github.com/nodejs/node/commit/0c58d0319b)] - **src**: remove misplaced windows code under posix guard in node.cc (Ali Hassan) [#52545](https://github.com/nodejs/node/pull/52545)
- \[[`e20d2f1de3`](https://github.com/nodejs/node/commit/e20d2f1de3)] - **src**: cast to v8::Value before using v8::EmbedderGraph::V8Node (Joyee Cheung) [#52638](https://github.com/nodejs/node/pull/52638)
- \[[`43fa6a1a45`](https://github.com/nodejs/node/commit/43fa6a1a45)] - **(SEMVER-MINOR)** **src**: add `string_view` overload to snapshot FromBlob (Anna Henningsen) [#52595](https://github.com/nodejs/node/pull/52595)
- \[[`a56faff4d0`](https://github.com/nodejs/node/commit/a56faff4d0)] - **src**: parse inspector profiles with simdjson (Joyee Cheung) [#51783](https://github.com/nodejs/node/pull/51783)
- \[[`ac04c6434a`](https://github.com/nodejs/node/commit/ac04c6434a)] - **src**: remove regex usage for env file parsing (IlyasShabi) [#52406](https://github.com/nodejs/node/pull/52406)
- \[[`f283d27285`](https://github.com/nodejs/node/commit/f283d27285)] - **src**: fix loadEnvFile ENOENT error (mathis-west-1) [#52438](https://github.com/nodejs/node/pull/52438)
- \[[`c6fe433d42`](https://github.com/nodejs/node/commit/c6fe433d42)] - **src,permission**: throw async errors on async APIs (Rafael Gonzaga) [#52730](https://github.com/nodejs/node/pull/52730)
- \[[`9f9eca965a`](https://github.com/nodejs/node/commit/9f9eca965a)] - **stream**: update ongoing promise in async iterator return() method (Mattias Buelens) [#52657](https://github.com/nodejs/node/pull/52657)
- \[[`d568a9a38e`](https://github.com/nodejs/node/commit/d568a9a38e)] - **test**: mark `test-error-serdes` as flaky (Antoine du Hamel) [#52739](https://github.com/nodejs/node/pull/52739)
- \[[`45f7002b90`](https://github.com/nodejs/node/commit/45f7002b90)] - **test**: mark test as flaky (Michael Dawson) [#52671](https://github.com/nodejs/node/pull/52671)
- \[[`10596e20e8`](https://github.com/nodejs/node/commit/10596e20e8)] - **test**: fix backtick usage in docs (Aviv Keller) [#52643](https://github.com/nodejs/node/pull/52643)
- \[[`b2f754c9f1`](https://github.com/nodejs/node/commit/b2f754c9f1)] - **test**: skip test-fs-watch-recursive-delete.js on IBM i (Abdirahim Musse) [#52645](https://github.com/nodejs/node/pull/52645)
- \[[`ed080d868d`](https://github.com/nodejs/node/commit/ed080d868d)] - **test**: ensure that all worker servers are ready (Luigi Pinca) [#52563](https://github.com/nodejs/node/pull/52563)
- \[[`c8c61737e4`](https://github.com/nodejs/node/commit/c8c61737e4)] - **test**: fix test-tls-ticket-cluster.js (Hüseyin Açacak) [#52431](https://github.com/nodejs/node/pull/52431)
- \[[`18aa5d6640`](https://github.com/nodejs/node/commit/18aa5d6640)] - **test**: split wasi poll test for windows (Hüseyin Açacak) [#52538](https://github.com/nodejs/node/pull/52538)
- \[[`e34e0a9ba1`](https://github.com/nodejs/node/commit/e34e0a9ba1)] - **test**: write tests for assertIsArray http2 util (Sinan Sonmez (Chaush)) [#52511](https://github.com/nodejs/node/pull/52511)
- \[[`e247a61d15`](https://github.com/nodejs/node/commit/e247a61d15)] - **(SEMVER-MINOR)** **test_runner**: add --test-skip-pattern cli option (Aviv Keller) [#52529](https://github.com/nodejs/node/pull/52529)
- \[[`e066ba2ae4`](https://github.com/nodejs/node/commit/e066ba2ae4)] - **test_runner**: better error handing for test hook (Alex Yang) [#52401](https://github.com/nodejs/node/pull/52401)
- \[[`328755341d`](https://github.com/nodejs/node/commit/328755341d)] - **test_runner**: don't exceed call stack when filtering (Colin Ihrig) [#52488](https://github.com/nodejs/node/pull/52488)
- \[[`b4ccb6c626`](https://github.com/nodejs/node/commit/b4ccb6c626)] - **test_runner**: move end of work check to finalize() (Colin Ihrig) [#52488](https://github.com/nodejs/node/pull/52488)
- \[[`2ef9380472`](https://github.com/nodejs/node/commit/2ef9380472)] - **tools**: update lint-md-dependencies to rollup\@4.17.0 (Node.js GitHub Bot) [#52729](https://github.com/nodejs/node/pull/52729)
- \[[`db421bdefc`](https://github.com/nodejs/node/commit/db421bdefc)] - **tools**: take co-authors into account in `find-inactive-collaborators` (Antoine du Hamel) [#52669](https://github.com/nodejs/node/pull/52669)
- \[[`01103a85cb`](https://github.com/nodejs/node/commit/01103a85cb)] - **tools**: fix invalid escape sequence in mkssldef (Michaël Zasso) [#52624](https://github.com/nodejs/node/pull/52624)
- \[[`382d951b01`](https://github.com/nodejs/node/commit/382d951b01)] - **tools**: update lint-md-dependencies to rollup\@4.15.0 (Node.js GitHub Bot) [#52617](https://github.com/nodejs/node/pull/52617)
- \[[`f9ddd77ff3`](https://github.com/nodejs/node/commit/f9ddd77ff3)] - **tools**: add lint rule to keep primordials in ASCII order (Antoine du Hamel) [#52592](https://github.com/nodejs/node/pull/52592)
- \[[`552642a498`](https://github.com/nodejs/node/commit/552642a498)] - **tools**: update lint-md-dependencies (Rich Trott) [#52581](https://github.com/nodejs/node/pull/52581)
- \[[`df61feb655`](https://github.com/nodejs/node/commit/df61feb655)] - **tools**: fix heading spaces for osx-entitlements.plist (Jackson Tian) [#52561](https://github.com/nodejs/node/pull/52561)
- \[[`6b4bbfbb1f`](https://github.com/nodejs/node/commit/6b4bbfbb1f)] - **tools**: update lint-md-dependencies to rollup\@4.14.2 vfile-reporter\@8.1.1 (Node.js GitHub Bot) [#52518](https://github.com/nodejs/node/pull/52518)
- \[[`4e5ce3afb7`](https://github.com/nodejs/node/commit/4e5ce3afb7)] - **tools**: use stylistic ESLint plugin for formatting (Michaël Zasso) [#50714](https://github.com/nodejs/node/pull/50714)
- \[[`15c5686381`](https://github.com/nodejs/node/commit/15c5686381)] - **tools**: update minimatch index path (Marco Ippolito) [#52523](https://github.com/nodejs/node/pull/52523)
- \[[`8ae1507ae1`](https://github.com/nodejs/node/commit/8ae1507ae1)] - **tools**: add a linter for README lists (Antoine du Hamel) [#52476](https://github.com/nodejs/node/pull/52476)
- \[[`0b970316bc`](https://github.com/nodejs/node/commit/0b970316bc)] - **typings**: fix invalid JSDoc declarations (Yagiz Nizipli) [#52659](https://github.com/nodejs/node/pull/52659)
- \[[`9b18df9dcb`](https://github.com/nodejs/node/commit/9b18df9dcb)] - **(SEMVER-MINOR)** **url**: implement parse method for safer URL parsing (Ali Hassan) [#52280](https://github.com/nodejs/node/pull/52280)
- \[[`d33131af3a`](https://github.com/nodejs/node/commit/d33131af3a)] - **vm**: fix ASCII-betical order (Aviv Keller) [#52686](https://github.com/nodejs/node/pull/52686)

Windows 32-bit Installer: https://nodejs.org/dist/v22.1.0/node-v22.1.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v22.1.0/node-v22.1.0-x64.msi \
Windows ARM 64-bit Installer: https://nodejs.org/dist/v22.1.0/node-v22.1.0-arm64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v22.1.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v22.1.0/win-x64/node.exe \
Windows ARM 64-bit Binary: https://nodejs.org/dist/v22.1.0/win-arm64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v22.1.0/node-v22.1.0.pkg \
macOS Apple Silicon 64-bit Binary: https://nodejs.org/dist/v22.1.0/node-v22.1.0-darwin-arm64.tar.gz \
macOS Intel 64-bit Binary: https://nodejs.org/dist/v22.1.0/node-v22.1.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v22.1.0/node-v22.1.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v22.1.0/node-v22.1.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v22.1.0/node-v22.1.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v22.1.0/node-v22.1.0-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v22.1.0/node-v22.1.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v22.1.0/node-v22.1.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v22.1.0/node-v22.1.0.tar.gz \
Other release files: https://nodejs.org/dist/v22.1.0/ \
Documentation: https://nodejs.org/docs/v22.1.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

5a6ddbc1fb0035bc7fa76e8da108b7a9866b47ea2a9f34225ddc6aac2c9007a3  node-v22.1.0-aix-ppc64.tar.gz
5c0d1008bb8a2653f25d162fe9af6fdb264ab8e61a784a52eed5a84077269fae  node-v22.1.0-arm64.msi
93904abf2b6afd0dc2a7c2947a83e10ed65cc39171db17663edb6f763aaa5a57  node-v22.1.0-darwin-arm64.tar.gz
f3222c15342919f16516e1d409dc885d4e448e4ce386ca6552f6d96c8a6d496b  node-v22.1.0-darwin-arm64.tar.xz
3a2c351b3eeeb4d12dee978f93f8f510589384b66a09feb903b609a58cf1bb5b  node-v22.1.0-darwin-x64.tar.gz
3e5bf773f01cdb9d03e12d89393ef12dee200c073163c82bc2eddffa36869747  node-v22.1.0-darwin-x64.tar.xz
5c00fa0f18d75c39a4a1068833999cc02620e467877e65ea59ecfaa092dc686a  node-v22.1.0-headers.tar.gz
b2f2319803f4dde2c1e4ab2d6040680eedc98b76336a10019801f85b2c50b398  node-v22.1.0-headers.tar.xz
9c111af1f951e8869615bca3601ce7ab6969374933bdba6397469843b808f222  node-v22.1.0-linux-arm64.tar.gz
030fc4d4cfdd2a2dc5f19aa577bf3dadbc042363b28e0ef66b1e7c8072e7445e  node-v22.1.0-linux-arm64.tar.xz
e2d46a045ab4e14e7234162c8167bed96c71ac82fbfae1feac21d77ea44c62d0  node-v22.1.0-linux-armv7l.tar.gz
48f197fa1ae7ad80f01c759ef0d91bb637f09cb04f7fa2eb57c7de79b2cf7bbd  node-v22.1.0-linux-armv7l.tar.xz
df47c5506bc865b093cfa0411b4adbb66187da00decd05a83c22b9d5ffdca088  node-v22.1.0-linux-ppc64le.tar.gz
9cfbb9d64c51aa238a8f4697faeb71ccf846155f3a0b9f5acb686a8f3554eb07  node-v22.1.0-linux-ppc64le.tar.xz
1961314d0d51cf9c4719705ba809f8451597c577dfb0cb500a058115a05d586a  node-v22.1.0-linux-s390x.tar.gz
da9d2f03bdaec5233adc360e517b8f4c2ebe283a9737f9065c829362b31c04de  node-v22.1.0-linux-s390x.tar.xz
d8ae35a9e2bb0c0c0611ee9bacf564ea51cc8291ace1447f95ee6aeaf4f1d61d  node-v22.1.0-linux-x64.tar.gz
22330ad3a1796ac30d75fab6f98cbe2b883239d4e31c2a52ec8f4e6cc52ace54  node-v22.1.0-linux-x64.tar.xz
969680f15ac27f88197735b18bc4d60b1b275a1b30dd6fb16104c5126d199a00  node-v22.1.0.pkg
1179664c69612f3f67df6fac32008df31f92e28c61d57c1e3297b84ea8879b5b  node-v22.1.0.tar.gz
9d7d5f40d9dbd6260c99b5e494b5f9bc755e8f0ffac70e121adce5fb442f23cb  node-v22.1.0.tar.xz
0a3d5932ff8d7aac36d5d763feb5de1da442a56ff7378222d9840d77e0db8b62  node-v22.1.0-win-arm64.7z
d32cf67728183026a236256ae488b27b77daa13c2faca83328dc5d188718ecdf  node-v22.1.0-win-arm64.zip
c3c57c78d3a5fbd223a6e17efc3052c5eee19e827a591148d7e5f68cf98e504b  node-v22.1.0-win-x64.7z
4b0f90bc3f1f41df16f9022ecded0c4eff55f8c3ece66ba348275c69fea4eb8e  node-v22.1.0-win-x64.zip
d1933f9813da34fdef20a1c2c8e82fa735ec96c503ecd818795ad8fab4a92d08  node-v22.1.0-win-x86.7z
3db474ecb74ab91ed7a2edc282a9ee77e297db00afad150a7fe33f24c2181c7c  node-v22.1.0-win-x86.zip
bae87f5171ec151846dff560dd78230e568b56151fd1bde41a1c7e9178d840e7  node-v22.1.0-x64.msi
7496b237a0ff72b120d7ce2f90d2c75dae85b128dd0c0c16c2536d1e7fa519cd  node-v22.1.0-x86.msi
b424b2c9485e39ec031ef01f1fcbdc736e9d57b05b93a135fb723afc052e52bc  win-arm64/node.exe
b552cb4f049fd4ce7b791ff9ab3964d5dfed118a737b0ac3ff67dad21c7de129  win-arm64/node.lib
d4e0f343358bab4d08309aec6d5d7531c253c34b187e0d5c9d9528b6f8a3115f  win-arm64/node_pdb.7z
a75e9e3857acb8b13102c30f1eba8268a4531fd0eae94c185b5a5b2d004df20b  win-arm64/node_pdb.zip
80db531026ff474745f03beb98f28c51914e0202e58cd87a1104f71ed0d8b118  win-x64/node.exe
7e8534fb900597f0d66c41bcae423f0bfe23290b09f20bb57cd07c2622db4ec4  win-x64/node.lib
10e8dd46f0d9b7a69ba73a38f45b7a866e4077a80a219489e5cfe820f1174db3  win-x64/node_pdb.7z
408fd5b2a28339051e0ce2830b4dc1ddfa2c4944abb24837644efaa8d78d8f96  win-x64/node_pdb.zip
cd6f3255b1ca77ed4dd81e56184329e5bf2a68c54152a93490c3f3e896bfc906  win-x86/node.exe
0f5f7f3178e3c605ecc460ed2157a7e02157c283cbf0274284e4258f3378135c  win-x86/node.lib
e3eea39004ec58042bb46e80cd65286e4a8f34c484cb8c13bdaa3f7a74fff7ae  win-x86/node_pdb.7z
3c3562e258ad58a271fc408ece941e6162f8a3e48a9e94818c44d2a93cbce726  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEj8yhP+8dDC6RAI4Jdw96mlrhVgAFAmYzpEQACgkQdw96mlrh
VgAX5w//fbVnd5ZJvNTCkLGLxgDAPmwcJ5hsHgbKbcrxksH6ZU1GFDA9faQaNY7I
ORIna3i3xd4aqQBYwL7cscEAnt0qy+wUDSxvZ/ymN0PVRGrVe2y/KbJUK7bK1IkR
36HgoukTdqTvZm4nN3uvinL60RrIZP+W/+hyhOmMEBx1yonqJlw3Ampwxakod5In
GN8Z+ASmDBFgtc0oG4ZuK+yc9Y4v9I2V1O9fkcGixJwYpqOXNOlHDjanciKDaqNA
sVwpJ1YJsvfSL0oiEDLX1n3H4ahOxHnpzJAsvt5lnBTAQgP3F1m/C5i+YKchbwG9
zGMUBavX48NP0CkyykXFM0ikF5dxsLQlClVyPMsx3lgPLg9wI+dW0lEFvZFUG8lw
Znlr3veCvhC/ifdHr5snDwsjGPQiPl7dYV8FHO7apSKMC1yehKbDu/Qzj7+3mqGz
/8BMzh1iasSYrkD9PWfOMKFWP0uvf9z+zwOlTp9A3UgT+VCb/rU2hiLFx1fLWt2B
qEO5x3TCmAGHF5KG/m8smXb+wTarUa3YuBoEDBP+bC/E8S+vX90LSeCixUrcS0oF
epUSp1oiShKmaOtn7ltLuSr6FrG6URQZpWolQeEg+DHvNE28V+/jA2msULXR+EBT
XHgFFmFdVI0HgtsLfw9PQlf+/79YKVoRJEoOULfab6hNPXdJpGA=
=230W
-----END PGP SIGNATURE-----
```
