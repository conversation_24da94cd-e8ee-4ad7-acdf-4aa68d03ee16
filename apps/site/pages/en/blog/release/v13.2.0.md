---
date: '2019-11-21T22:43:00.450Z'
category: release
title: Node v13.2.0 (Current)
layout: blog-post
author: <PERSON><PERSON>
---

### Notable Changes

- **addons**:
  - Deprecate one- and two-argument `AtExit()`. Use the three-argument variant of `AtExit()` or `AddEnvironmentCleanupHook()` instead (<PERSON>) [#30227](https://github.com/nodejs/node/pull/30227)
- **child_process,cluster**:
  - The `serialization` option is added that allows child process IPC to use the V8 serialization API (to e.g., pass through data types like sets or maps) (<PERSON>) [#30162](https://github.com/nodejs/node/pull/30162)
- **deps**:
  - Update V8 to 7.9
  - Update `npm` to 6.13.1 (<PERSON><PERSON>) [#30271](https://github.com/nodejs/node/pull/30271)
- **embedder**:
  - Exposes the ability to pass cli flags / options through an API as embedder (<PERSON>) [#30466](https://github.com/nodejs/node/pull/30466)
  - <PERSON><PERSON> adding linked bindings to Environment (<PERSON>) [#30274](https://github.com/nodejs/node/pull/30274)
- **esm**:
  - Unflag `--experimental-modules` (Guy Bedford) [#29866](https://github.com/nodejs/node/pull/29866)
- **stream**:
  - Add `writable.writableCorked` property (Robert Nagy) [#29012](https://github.com/nodejs/node/pull/29012)
- **worker**:
  - Allow specifying resource limits (Anna Henningsen) [#26628](https://github.com/nodejs/node/pull/26628)
- **v8**:
  - The Serialization API is now stable (Anna Henningsen) [#30234](https://github.com/nodejs/node/pull/30234)

### Commits

- [[`b76c13ec86`](https://github.com/nodejs/node/commit/b76c13ec86)] - **assert**: replace var with let in lib/assert.js (PerfectPan) [#30261](https://github.com/nodejs/node/pull/30261)
- [[`7f49816e8a`](https://github.com/nodejs/node/commit/7f49816e8a)] - **benchmark**: use let instead of var in async_hooks (dnlup) [#30470](https://github.com/nodejs/node/pull/30470)
- [[`0130d2b6e0`](https://github.com/nodejs/node/commit/0130d2b6e0)] - **benchmark**: use let instead of var in assert (dnlup) [#30450](https://github.com/nodejs/node/pull/30450)
- [[`9cae205f4d`](https://github.com/nodejs/node/commit/9cae205f4d)] - **buffer**: change var to let (Vladislav Botvin) [#30292](https://github.com/nodejs/node/pull/30292)
- [[`b5198cd3b0`](https://github.com/nodejs/node/commit/b5198cd3b0)] - **(SEMVER-MINOR)** **build**: reset embedder string to "-node.0" (Michaël Zasso) [#30513](https://github.com/nodejs/node/pull/30513)
- [[`f4f210adc1`](https://github.com/nodejs/node/commit/f4f210adc1)] - **build**: store cache on timed out builds on Travis (Richard Lau) [#30469](https://github.com/nodejs/node/pull/30469)
- [[`277e5fadf8`](https://github.com/nodejs/node/commit/277e5fadf8)] - **(SEMVER-MINOR)** **build,tools**: update V8 gypfiles for V8 7.9 (Michaël Zasso) [#30020](https://github.com/nodejs/node/pull/30020)
- [[`e51beef8d4`](https://github.com/nodejs/node/commit/e51beef8d4)] - **(SEMVER-MINOR)** **child_process,cluster**: allow using V8 serialization API (Anna Henningsen) [#30162](https://github.com/nodejs/node/pull/30162)
- [[`6bf0e40bad`](https://github.com/nodejs/node/commit/6bf0e40bad)] - **cluster**: destruct primordials in lib/internal/cluster/worker.js (peze) [#30246](https://github.com/nodejs/node/pull/30246)
- [[`18ec8a84be`](https://github.com/nodejs/node/commit/18ec8a84be)] - **(SEMVER-MINOR)** **crypto**: add support for IEEE-P1363 DSA signatures (Tobias Nießen) [#29292](https://github.com/nodejs/node/pull/29292)
- [[`39d0a25ddd`](https://github.com/nodejs/node/commit/39d0a25ddd)] - **crypto**: fix key requirements in asymmetric cipher (Tobias Nießen) [#30249](https://github.com/nodejs/node/pull/30249)
- [[`8c2e2ce6bf`](https://github.com/nodejs/node/commit/8c2e2ce6bf)] - **crypto**: update root certificates (AshCripps) [#30195](https://github.com/nodejs/node/pull/30195)
- [[`4f282f52f0`](https://github.com/nodejs/node/commit/4f282f52f0)] - **deps**: patch V8 to 7.9.317.23 (Myles Borins) [#30560](https://github.com/nodejs/node/pull/30560)
- [[`9b71534d23`](https://github.com/nodejs/node/commit/9b71534d23)] - **deps**: upgrade npm to 6.13.1 (claudiahdz) [#30533](https://github.com/nodejs/node/pull/30533)
- [[`f17c794faf`](https://github.com/nodejs/node/commit/f17c794faf)] - **(SEMVER-MINOR)** **deps**: patch V8 to be API/ABI compatible with 7.8 (from 7.9) (Michaël Zasso) [#30513](https://github.com/nodejs/node/pull/30513)
- [[`5a1ad570ea`](https://github.com/nodejs/node/commit/5a1ad570ea)] - **deps**: V8: cherry-pick a7dffcd767be (Christian Clauss) [#30218](https://github.com/nodejs/node/pull/30218)
- [[`2c6cf902b0`](https://github.com/nodejs/node/commit/2c6cf902b0)] - **(SEMVER-MINOR)** **deps**: V8: cherry-pick 50031fae736f (Michaël Zasso) [#30020](https://github.com/nodejs/node/pull/30020)
- [[`1e5e8c3922`](https://github.com/nodejs/node/commit/1e5e8c3922)] - **deps**: V8: cherry-pick e5dbc95 (Gabriel Schulhof) [#30130](https://github.com/nodejs/node/pull/30130)
- [[`9c356ba91c`](https://github.com/nodejs/node/commit/9c356ba91c)] - **(SEMVER-MINOR)** **deps**: V8: backport 5e755c6ee6d3 (Michaël Zasso) [#30020](https://github.com/nodejs/node/pull/30020)
- [[`fe99841c88`](https://github.com/nodejs/node/commit/fe99841c88)] - **(SEMVER-MINOR)** **deps**: V8: backport 07ee86a5a28b (Michaël Zasso) [#30020](https://github.com/nodejs/node/pull/30020)
- [[`5131bbe477`](https://github.com/nodejs/node/commit/5131bbe477)] - **(SEMVER-MINOR)** **deps**: V8: cherry-pick 777fa98 (Michaël Zasso) [#30020](https://github.com/nodejs/node/pull/30020)
- [[`824e8b6f9b`](https://github.com/nodejs/node/commit/824e8b6f9b)] - **(SEMVER-MINOR)** **deps**: V8: cherry-pick 7228ef8 (Michaël Zasso) [#30020](https://github.com/nodejs/node/pull/30020)
- [[`4c7acc256a`](https://github.com/nodejs/node/commit/4c7acc256a)] - **(SEMVER-MINOR)** **deps**: V8: cherry-pick 6b0a953 (Michaël Zasso) [#30020](https://github.com/nodejs/node/pull/30020)
- [[`ebef1b2308`](https://github.com/nodejs/node/commit/ebef1b2308)] - **(SEMVER-MINOR)** **deps**: V8: cherry-pick bba5f1f (Michaël Zasso) [#30020](https://github.com/nodejs/node/pull/30020)
- [[`28ca44c724`](https://github.com/nodejs/node/commit/28ca44c724)] - **(SEMVER-MINOR)** **deps**: V8: cherry-pick cfe9172 (Michaël Zasso) [#30020](https://github.com/nodejs/node/pull/30020)
- [[`ba4abfd198`](https://github.com/nodejs/node/commit/ba4abfd198)] - **(SEMVER-MINOR)** **deps**: V8: cherry-pick 3e82c8d (Michaël Zasso) [#30020](https://github.com/nodejs/node/pull/30020)
- [[`2abdcbbe5e`](https://github.com/nodejs/node/commit/2abdcbbe5e)] - **(SEMVER-MINOR)** **deps**: V8: cherry-pick f2d92ec (Michaël Zasso) [#30020](https://github.com/nodejs/node/pull/30020)
- [[`46383616e3`](https://github.com/nodejs/node/commit/46383616e3)] - **(SEMVER-MINOR)** **deps**: make v8.h compatible with VS2015 (Joao Reis) [#30020](https://github.com/nodejs/node/pull/30020)
- [[`5bc35732aa`](https://github.com/nodejs/node/commit/5bc35732aa)] - **(SEMVER-MINOR)** **deps**: V8: forward declaration of `Rtl\*FunctionTable` (Refael Ackermann) [#27375](https://github.com/nodejs/node/pull/27375)
- [[`627a804627`](https://github.com/nodejs/node/commit/627a804627)] - **(SEMVER-MINOR)** **deps**: V8: patch register-arm64.h (Refael Ackermann) [#27375](https://github.com/nodejs/node/pull/27375)
- [[`13e6b0b82a`](https://github.com/nodejs/node/commit/13e6b0b82a)] - **(SEMVER-MINOR)** **deps**: update V8's postmortem script (Colin Ihrig) [#30020](https://github.com/nodejs/node/pull/30020)
- [[`a4a6565348`](https://github.com/nodejs/node/commit/a4a6565348)] - **(SEMVER-MINOR)** **deps**: update V8's postmortem script (Colin Ihrig) [#29694](https://github.com/nodejs/node/pull/29694)
- [[`4182e3bad7`](https://github.com/nodejs/node/commit/4182e3bad7)] - **(SEMVER-MINOR)** **deps**: patch V8 to run on older XCode versions (Ujjwal Sharma) [#29694](https://github.com/nodejs/node/pull/29694)
- [[`6566c15157`](https://github.com/nodejs/node/commit/6566c15157)] - **(SEMVER-MINOR)** **deps**: V8: silence irrelevant warnings (Michaël Zasso) [#26685](https://github.com/nodejs/node/pull/26685)
- [[`6018db2ef9`](https://github.com/nodejs/node/commit/6018db2ef9)] - **(SEMVER-MINOR)** **deps**: V8: un-cherry-pick bd019bd (Refael Ackermann) [#26685](https://github.com/nodejs/node/pull/26685)
- [[`605cb9f0fc`](https://github.com/nodejs/node/commit/605cb9f0fc)] - **(SEMVER-MINOR)** **deps**: update V8 to 7.9.317.22 (Michaël Zasso) [#30513](https://github.com/nodejs/node/pull/30513)
- [[`b82f63d9ca`](https://github.com/nodejs/node/commit/b82f63d9ca)] - **deps**: update nghttp2 to 1.40.0 (gengjiawen) [#30493](https://github.com/nodejs/node/pull/30493)
- [[`401d2e9115`](https://github.com/nodejs/node/commit/401d2e9115)] - **deps**: update npm to 6.13.0 (Ruy Adorno) [#30271](https://github.com/nodejs/node/pull/30271)
- [[`f8ee70c94d`](https://github.com/nodejs/node/commit/f8ee70c94d)] - **dgram**: remove listeners on bind error (Anna Henningsen) [#30210](https://github.com/nodejs/node/pull/30210)
- [[`0433d7995a`](https://github.com/nodejs/node/commit/0433d7995a)] - **dgram**: reset bind state before emitting error (Anna Henningsen) [#30210](https://github.com/nodejs/node/pull/30210)
- [[`0f8662d615`](https://github.com/nodejs/node/commit/0f8662d615)] - **dns**: switch var to const/let (Dmitriy Kikinskiy) [#30302](https://github.com/nodejs/node/pull/30302)
- [[`ab887bd5f6`](https://github.com/nodejs/node/commit/ab887bd5f6)] - **doc**: add mention for using promisify on class methods (Denys Otrishko) [#30355](https://github.com/nodejs/node/pull/30355)
- [[`9940116aba`](https://github.com/nodejs/node/commit/9940116aba)] - **doc**: explain GIT_REMOTE_REF in COLLABORATOR_GUIDE (Denys Otrishko) [#30371](https://github.com/nodejs/node/pull/30371)
- [[`027bde563d`](https://github.com/nodejs/node/commit/027bde563d)] - **doc**: fix overriding of prefix option (Luigi Pinca) [#30518](https://github.com/nodejs/node/pull/30518)
- [[`b7757533bc`](https://github.com/nodejs/node/commit/b7757533bc)] - **doc**: update http.md mention of socket (Jesse O'Connor) [#30155](https://github.com/nodejs/node/pull/30155)
- [[`7f664e454b`](https://github.com/nodejs/node/commit/7f664e454b)] - **doc**: adds NO_COLOR to assert doc page (Shobhit Chittora) [#30483](https://github.com/nodejs/node/pull/30483)
- [[`fba2f9a3d6`](https://github.com/nodejs/node/commit/fba2f9a3d6)] - **doc**: document timed out Travis CI builds (Richard Lau) [#30469](https://github.com/nodejs/node/pull/30469)
- [[`c40e242b32`](https://github.com/nodejs/node/commit/c40e242b32)] - **doc**: replace const / var with let (Duncan Healy) [#30446](https://github.com/nodejs/node/pull/30446)
- [[`a93345b7cd`](https://github.com/nodejs/node/commit/a93345b7cd)] - **doc**: update outdated commonjs compat info (Geoffrey Booth) [#30512](https://github.com/nodejs/node/pull/30512)
- [[`b590533253`](https://github.com/nodejs/node/commit/b590533253)] - **doc**: esm: improve dual package hazard docs (Geoffrey Booth) [#30345](https://github.com/nodejs/node/pull/30345)
- [[`d631a0a3e4`](https://github.com/nodejs/node/commit/d631a0a3e4)] - **doc**: update 8.x to 10.x in backporting guide (garygsc) [#30481](https://github.com/nodejs/node/pull/30481)
- [[`7e603bed52`](https://github.com/nodejs/node/commit/7e603bed52)] - **doc**: createRequire can take import.meta.url directly (Geoffrey Booth) [#30495](https://github.com/nodejs/node/pull/30495)
- [[`e4a296ce8d`](https://github.com/nodejs/node/commit/e4a296ce8d)] - **doc**: add entry to url.parse() changes metadata (Luigi Pinca) [#30348](https://github.com/nodejs/node/pull/30348)
- [[`64cf00b0b9`](https://github.com/nodejs/node/commit/64cf00b0b9)] - **doc**: simplify text in pull-requests.md (Rich Trott) [#30458](https://github.com/nodejs/node/pull/30458)
- [[`1e2672012f`](https://github.com/nodejs/node/commit/1e2672012f)] - **doc**: remove "multiple variants" from BUILDING.md (Rich Trott) [#30366](https://github.com/nodejs/node/pull/30366)
- [[`2d16a74ff9`](https://github.com/nodejs/node/commit/2d16a74ff9)] - **doc**: remove "maintenance is supported by" text in BUILDING.md (Rich Trott) [#30365](https://github.com/nodejs/node/pull/30365)
- [[`c832565290`](https://github.com/nodejs/node/commit/c832565290)] - **doc**: add lookup to http.request() options (Luigi Pinca) [#30353](https://github.com/nodejs/node/pull/30353)
- [[`b8afe57e85`](https://github.com/nodejs/node/commit/b8afe57e85)] - **doc**: fix up N-API doc (Michael Dawson) [#30254](https://github.com/nodejs/node/pull/30254)
- [[`b558d941bd`](https://github.com/nodejs/node/commit/b558d941bd)] - **doc**: fix some recent doc nits (vsemozhetbyt) [#30341](https://github.com/nodejs/node/pull/30341)
- [[`1133981eac`](https://github.com/nodejs/node/commit/1133981eac)] - **doc**: add link to node-code-ide-configs in testing (Trivikram Kamat) [#24012](https://github.com/nodejs/node/pull/24012)
- [[`041f3a306e`](https://github.com/nodejs/node/commit/041f3a306e)] - **doc**: update divergent specifier hazard guidance (Geoffrey Booth) [#30051](https://github.com/nodejs/node/pull/30051)
- [[`085af30361`](https://github.com/nodejs/node/commit/085af30361)] - **doc**: include --experimental-resolve-self in manpage (Guy Bedford) [#29978](https://github.com/nodejs/node/pull/29978)
- [[`31a3b724f0`](https://github.com/nodejs/node/commit/31a3b724f0)] - **doc**: update GOVERNANCE.md (Rich Trott) [#30259](https://github.com/nodejs/node/pull/30259)
- [[`15a7032d44`](https://github.com/nodejs/node/commit/15a7032d44)] - **doc**: move inactive Collaborators to emeriti (Rich Trott) [#30243](https://github.com/nodejs/node/pull/30243)
- [[`fabc489dba`](https://github.com/nodejs/node/commit/fabc489dba)] - **doc**: update examples in writing-tests.md (garygsc) [#30126](https://github.com/nodejs/node/pull/30126)
- [[`1836eae7a6`](https://github.com/nodejs/node/commit/1836eae7a6)] - **doc, console**: remove non-existant methods from docs (Simon Schick) [#30346](https://github.com/nodejs/node/pull/30346)
- [[`7ad2e024dd`](https://github.com/nodejs/node/commit/7ad2e024dd)] - **doc,meta**: allow Travis results for doc/comment changes (Rich Trott) [#30330](https://github.com/nodejs/node/pull/30330)
- [[`2deea28070`](https://github.com/nodejs/node/commit/2deea28070)] - **doc,meta**: remove wait period for npm pull requests (Rich Trott) [#30329](https://github.com/nodejs/node/pull/30329)
- [[`7e0f90e286`](https://github.com/nodejs/node/commit/7e0f90e286)] - **domain**: rename var to let and const (Maria Stogova) [#30312](https://github.com/nodejs/node/pull/30312)
- [[`c2c74fc93e`](https://github.com/nodejs/node/commit/c2c74fc93e)] - **encoding**: make TextDecoder handle BOM correctly (Anna Henningsen) [#30132](https://github.com/nodejs/node/pull/30132)
- [[`f9eab48dd0`](https://github.com/nodejs/node/commit/f9eab48dd0)] - **esm**: disable non-js exts outside package scopes (Guy Bedford) [#30501](https://github.com/nodejs/node/pull/30501)
- [[`3d8cdf191d`](https://github.com/nodejs/node/commit/3d8cdf191d)] - **esm**: unflag --experimental-modules (Guy Bedford) [#29866](https://github.com/nodejs/node/pull/29866)
- [[`293e8a2384`](https://github.com/nodejs/node/commit/293e8a2384)] - **esm**: exit the process with an error if loader has an issue (Michaël Zasso) [#30219](https://github.com/nodejs/node/pull/30219)
- [[`45fd44c6ec`](https://github.com/nodejs/node/commit/45fd44c6ec)] - **fs**: change var to let (Nadya) [#30318](https://github.com/nodejs/node/pull/30318)
- [[`bb6f944607`](https://github.com/nodejs/node/commit/bb6f944607)] - **fs**: add noop stub for FSWatcher.prototype.start (Lucas Holmquist) [#30160](https://github.com/nodejs/node/pull/30160)
- [[`4fe62c1620`](https://github.com/nodejs/node/commit/4fe62c1620)] - **http**: revise \_http_server.js (telenord) [#30279](https://github.com/nodejs/node/pull/30279)
- [[`62e15a793a`](https://github.com/nodejs/node/commit/62e15a793a)] - **http**: outgoing cork (Robert Nagy) [#29053](https://github.com/nodejs/node/pull/29053)
- [[`50f9476a44`](https://github.com/nodejs/node/commit/50f9476a44)] - **http**: http_common rename var to let and const (telenord) [#30288](https://github.com/nodejs/node/pull/30288)
- [[`b8aceace95`](https://github.com/nodejs/node/commit/b8aceace95)] - **http**: http_incoming rename var to let and const (telenord) [#30285](https://github.com/nodejs/node/pull/30285)
- [[`a37ade8648`](https://github.com/nodejs/node/commit/a37ade8648)] - **http**: replace vars with lets and consts in lib/\_http_agent.js (palmires) [#30301](https://github.com/nodejs/node/pull/30301)
- [[`e59cc8aad8`](https://github.com/nodejs/node/commit/e59cc8aad8)] - **http,async_hooks**: keep resource object alive from socket (Anna Henningsen) [#30196](https://github.com/nodejs/node/pull/30196)
- [[`1b84175924`](https://github.com/nodejs/node/commit/1b84175924)] - **http2**: remove duplicated assertIsObject (Yongsheng Zhang) [#30541](https://github.com/nodejs/node/pull/30541)
- [[`666588143e`](https://github.com/nodejs/node/commit/666588143e)] - **http2**: use custom BaseObject smart pointers (Anna Henningsen) [#30374](https://github.com/nodejs/node/pull/30374)
- [[`f25b00aaca`](https://github.com/nodejs/node/commit/f25b00aaca)] - **(SEMVER-MINOR)** **https**: add client support for TLS keylog events (Sam Roberts) [#30053](https://github.com/nodejs/node/pull/30053)
- [[`88da3af6f6`](https://github.com/nodejs/node/commit/88da3af6f6)] - **https**: change var to let in lib/https.js (galina.prokofeva) [#30320](https://github.com/nodejs/node/pull/30320)
- [[`f15a3b0281`](https://github.com/nodejs/node/commit/f15a3b0281)] - **lib**: replace var with let (David OLIVIER) [#30381](https://github.com/nodejs/node/pull/30381)
- [[`31a63ab1ec`](https://github.com/nodejs/node/commit/31a63ab1ec)] - **lib**: replace var with let and const in readline.js (VinceOPS) [#30377](https://github.com/nodejs/node/pull/30377)
- [[`3eeeea419d`](https://github.com/nodejs/node/commit/3eeeea419d)] - **lib**: change var to let/const in internal/querystring.js (Artem Maksimov) [#30286](https://github.com/nodejs/node/pull/30286)
- [[`f10608655b`](https://github.com/nodejs/node/commit/f10608655b)] - **lib**: change var to let in internal/streams (Kyriakos Markakis) [#30430](https://github.com/nodejs/node/pull/30430)
- [[`3ce6e15844`](https://github.com/nodejs/node/commit/3ce6e15844)] - **lib**: replace var with let/const (Kenza Houmani) [#30440](https://github.com/nodejs/node/pull/30440)
- [[`d37d340472`](https://github.com/nodejs/node/commit/d37d340472)] - **lib**: change var to let in string_decoder (mkdorff) [#30393](https://github.com/nodejs/node/pull/30393)
- [[`9a1c16eda4`](https://github.com/nodejs/node/commit/9a1c16eda4)] - **lib**: replaced var to let in lib/v8.js (Vadim Gorbachev) [#30305](https://github.com/nodejs/node/pull/30305)
- [[`3e4a6a5968`](https://github.com/nodejs/node/commit/3e4a6a5968)] - **lib**: change var to let in lib/\_stream_duplex.js (Ilia Safronov) [#30297](https://github.com/nodejs/node/pull/30297)
- [[`c7c566023f`](https://github.com/nodejs/node/commit/c7c566023f)] - **module**: reduce circular dependency of internal/modules/cjs/loader (Joyee Cheung) [#30349](https://github.com/nodejs/node/pull/30349)
- [[`e98d89cef9`](https://github.com/nodejs/node/commit/e98d89cef9)] - **module**: conditional exports with flagged conditions (Guy Bedford) [#29978](https://github.com/nodejs/node/pull/29978)
- [[`caedcd9ef9`](https://github.com/nodejs/node/commit/caedcd9ef9)] - **module**: fix for empty object in InternalModuleReadJSON (Guy Bedford) [#30256](https://github.com/nodejs/node/pull/30256)
- [[`66e1adf200`](https://github.com/nodejs/node/commit/66e1adf200)] - **net**: destructure primordials (Guilherme Goncalves) [#30447](https://github.com/nodejs/node/pull/30447)
- [[`9230ffffd0`](https://github.com/nodejs/node/commit/9230ffffd0)] - **net**: replaced vars to lets and consts (alexahdp) [#30287](https://github.com/nodejs/node/pull/30287)
- [[`9248c8b960`](https://github.com/nodejs/node/commit/9248c8b960)] - **path**: replace var with let in lib/path.js (peze) [#30260](https://github.com/nodejs/node/pull/30260)
- [[`e363f8e17f`](https://github.com/nodejs/node/commit/e363f8e17f)] - **process**: add coverage tests for sourceMapFromDataUrl method (Nolik) [#30319](https://github.com/nodejs/node/pull/30319)
- [[`7b4187413e`](https://github.com/nodejs/node/commit/7b4187413e)] - **process**: make source map getter resistant against prototype tampering (Anna Henningsen) [#30228](https://github.com/nodejs/node/pull/30228)
- [[`183464a24d`](https://github.com/nodejs/node/commit/183464a24d)] - **querystring**: replace var with let/const (Raoul Jaeckel) [#30429](https://github.com/nodejs/node/pull/30429)
- [[`7188b9599d`](https://github.com/nodejs/node/commit/7188b9599d)] - **src**: fix -Winconsistent-missing-override warning (Colin Ihrig) [#30549](https://github.com/nodejs/node/pull/30549)
- [[`966404fd24`](https://github.com/nodejs/node/commit/966404fd24)] - **src**: add file name to 'Module did not self-register' error (Jeremy Apthorp) [#30125](https://github.com/nodejs/node/pull/30125)
- [[`21dd6019ec`](https://github.com/nodejs/node/commit/21dd6019ec)] - **(SEMVER-MINOR)** **src**: expose ArrayBuffer version of Buffer::New() (Anna Henningsen) [#30476](https://github.com/nodejs/node/pull/30476)
- [[`2e43686c5a`](https://github.com/nodejs/node/commit/2e43686c5a)] - **src**: mark ArrayBuffers with free callbacks as untransferable (Anna Henningsen) [#30475](https://github.com/nodejs/node/pull/30475)
- [[`564c18e214`](https://github.com/nodejs/node/commit/564c18e214)] - **src**: remove HandleWrap instances from list once closed (Anna Henningsen) [#30374](https://github.com/nodejs/node/pull/30374)
- [[`4222f2400a`](https://github.com/nodejs/node/commit/4222f2400a)] - **src**: remove keep alive option from SetImmediate() (Anna Henningsen) [#30374](https://github.com/nodejs/node/pull/30374)
- [[`940a2972b2`](https://github.com/nodejs/node/commit/940a2972b2)] - **src**: use BaseObjectPtr for keeping channel alive in dns bindings (Anna Henningsen) [#30374](https://github.com/nodejs/node/pull/30374)
- [[`a2dbadc1ce`](https://github.com/nodejs/node/commit/a2dbadc1ce)] - **src**: introduce custom smart pointers for `BaseObject`s (Anna Henningsen) [#30374](https://github.com/nodejs/node/pull/30374)
- [[`1a92c88418`](https://github.com/nodejs/node/commit/1a92c88418)] - **src**: migrate off ArrayBuffer::GetContents (Anna Henningsen) [#30339](https://github.com/nodejs/node/pull/30339)
- [[`0d5de1a20e`](https://github.com/nodejs/node/commit/0d5de1a20e)] - **(SEMVER-MINOR)** **src**: remove custom tracking for SharedArrayBuffers (Anna Henningsen) [#30020](https://github.com/nodejs/node/pull/30020)
- [[`f0ff2ed9d5`](https://github.com/nodejs/node/commit/f0ff2ed9d5)] - **(SEMVER-MINOR)** **src**: update v8abbr.h for V8 update (Colin Ihrig) [#30020](https://github.com/nodejs/node/pull/30020)
- [[`2c8276eda8`](https://github.com/nodejs/node/commit/2c8276eda8)] - **(SEMVER-MINOR)** **src**: expose ability to set options (Shelley Vohr) [#30466](https://github.com/nodejs/node/pull/30466)
- [[`592d51cb23`](https://github.com/nodejs/node/commit/592d51cb23)] - **src**: enhance feature access `CHECK`s during bootstrap (Anna Henningsen) [#30452](https://github.com/nodejs/node/pull/30452)
- [[`d648c933b5`](https://github.com/nodejs/node/commit/d648c933b5)] - **src**: lib/internal/timers.js var -\> let/const (Nikolay Krashnikov) [#30314](https://github.com/nodejs/node/pull/30314)
- [[`70ad676023`](https://github.com/nodejs/node/commit/70ad676023)] - **src**: persist strings that are used multiple times in the environment (Vadim Gorbachev) [#30321](https://github.com/nodejs/node/pull/30321)
- [[`b744070d74`](https://github.com/nodejs/node/commit/b744070d74)] - **(SEMVER-MINOR)** **src**: allow adding linked bindings to Environment (Anna Henningsen) [#30274](https://github.com/nodejs/node/pull/30274)
- [[`058a8d5363`](https://github.com/nodejs/node/commit/058a8d5363)] - **src**: do not use `std::function` for `OnScopeLeave` (Anna Henningsen) [#30134](https://github.com/nodejs/node/pull/30134)
- [[`906d279e69`](https://github.com/nodejs/node/commit/906d279e69)] - **src**: run RunBeforeExitCallbacks as part of EmitBeforeExit (Anna Henningsen) [#30229](https://github.com/nodejs/node/pull/30229)
- [[`66b3619b4e`](https://github.com/nodejs/node/commit/66b3619b4e)] - **src**: use unique_ptr for InitializeInspector() (Anna Henningsen) [#30229](https://github.com/nodejs/node/pull/30229)
- [[`db7deb6e7a`](https://github.com/nodejs/node/commit/db7deb6e7a)] - **src**: make WaitForInspectorDisconnect an exit hook (Anna Henningsen) [#30229](https://github.com/nodejs/node/pull/30229)
- [[`cd233e3f16`](https://github.com/nodejs/node/commit/cd233e3f16)] - **src**: make EndStartedProfilers an exit hook (Anna Henningsen) [#30229](https://github.com/nodejs/node/pull/30229)
- [[`8234d04b56`](https://github.com/nodejs/node/commit/8234d04b56)] - **src**: track no of active JS signal handlers (Anna Henningsen) [#30229](https://github.com/nodejs/node/pull/30229)
- [[`0072a8eddf`](https://github.com/nodejs/node/commit/0072a8eddf)] - **src**: remove AsyncScope and AsyncCallbackScope (Anna Henningsen) [#30236](https://github.com/nodejs/node/pull/30236)
- [[`e3371f0c93`](https://github.com/nodejs/node/commit/e3371f0c93)] - **src**: use callback scope for main script (Anna Henningsen) [#30236](https://github.com/nodejs/node/pull/30236)
- [[`cd6d6215cc`](https://github.com/nodejs/node/commit/cd6d6215cc)] - **(SEMVER-MINOR)** **src**: deprecate two- and one-argument AtExit() (Anna Henningsen) [#30227](https://github.com/nodejs/node/pull/30227)
- [[`5f4535a97c`](https://github.com/nodejs/node/commit/5f4535a97c)] - **src**: make AtExit() callbacks run in reverse order (Anna Henningsen) [#30230](https://github.com/nodejs/node/pull/30230)
- [[`44968f0edc`](https://github.com/nodejs/node/commit/44968f0edc)] - **src**: remove unimplemented method from node.h (Anna Henningsen) [#30098](https://github.com/nodejs/node/pull/30098)
- [[`4524c7ad36`](https://github.com/nodejs/node/commit/4524c7ad36)] - **stream**: replace var with let (daern91) [#30379](https://github.com/nodejs/node/pull/30379)
- [[`41720d78c9`](https://github.com/nodejs/node/commit/41720d78c9)] - **stream**: add writableCorked to Duplex (Anna Henningsen) [#29053](https://github.com/nodejs/node/pull/29053)
- [[`7cbdac9a71`](https://github.com/nodejs/node/commit/7cbdac9a71)] - **stream**: increase MAX_HWM (Robert Nagy) [#29938](https://github.com/nodejs/node/pull/29938)
- [[`c254d7469d`](https://github.com/nodejs/node/commit/c254d7469d)] - **(SEMVER-MINOR)** **stream**: add writableCorked property (Robert Nagy) [#29012](https://github.com/nodejs/node/pull/29012)
- [[`cb9c64a6e0`](https://github.com/nodejs/node/commit/cb9c64a6e0)] - **test**: move test not requiring internet from internet to parallel (Rich Trott) [#30545](https://github.com/nodejs/node/pull/30545)
- [[`902c6702df`](https://github.com/nodejs/node/commit/902c6702df)] - **test**: use reserved .invalid TLD for invalid address in test (Rich Trott) [#30545](https://github.com/nodejs/node/pull/30545)
- [[`92f766bd83`](https://github.com/nodejs/node/commit/92f766bd83)] - **test**: improve assertion message in internet dgram test (Rich Trott) [#30545](https://github.com/nodejs/node/pull/30545)
- [[`a5f25ecf07`](https://github.com/nodejs/node/commit/a5f25ecf07)] - **test**: cover 'close' method in Dir class (Artem Maksimov) [#30310](https://github.com/nodejs/node/pull/30310)
- [[`45e57303f3`](https://github.com/nodejs/node/commit/45e57303f3)] - **test**: add test for options validation of createServer (Yongsheng Zhang) [#30541](https://github.com/nodejs/node/pull/30541)
- [[`6be03981b2`](https://github.com/nodejs/node/commit/6be03981b2)] - **test**: clean up http-set-trailers (Denys Otrishko) [#30522](https://github.com/nodejs/node/pull/30522)
- [[`2952c5d72b`](https://github.com/nodejs/node/commit/2952c5d72b)] - **(SEMVER-MINOR)** **test**: increase limit again for network space overhead test (Michaël Zasso) [#30020](https://github.com/nodejs/node/pull/30020)
- [[`4131b14011`](https://github.com/nodejs/node/commit/4131b14011)] - **(SEMVER-MINOR)** **test**: update test-postmortem-metadata.js (Colin Ihrig) [#30020](https://github.com/nodejs/node/pull/30020)
- [[`c464ede598`](https://github.com/nodejs/node/commit/c464ede598)] - **test**: handle undefined default_configuration (Shelley Vohr) [#30465](https://github.com/nodejs/node/pull/30465)
- [[`5ec550de02`](https://github.com/nodejs/node/commit/5ec550de02)] - **test**: Change from var to const (Jure Stepisnik) [#30431](https://github.com/nodejs/node/pull/30431)
- [[`13bac0ac0f`](https://github.com/nodejs/node/commit/13bac0ac0f)] - **test**: changed var to let in test-repl-editor (JL Phillips) [#30443](https://github.com/nodejs/node/pull/30443)
- [[`0d12e9cc29`](https://github.com/nodejs/node/commit/0d12e9cc29)] - **test**: improve test-fs-open (Artem Maksimov) [#30280](https://github.com/nodejs/node/pull/30280)
- [[`89bc2526ab`](https://github.com/nodejs/node/commit/89bc2526ab)] - **test**: change var to let (nathias) [#30444](https://github.com/nodejs/node/pull/30444)
- [[`fa071efea4`](https://github.com/nodejs/node/commit/fa071efea4)] - **test**: changed var to const in test (Kerry Mahne) [#30434](https://github.com/nodejs/node/pull/30434)
- [[`13a22432fc`](https://github.com/nodejs/node/commit/13a22432fc)] - **test**: var to const in test-repl-multiline.js (SoulMonk) [#30433](https://github.com/nodejs/node/pull/30433)
- [[`109da52141`](https://github.com/nodejs/node/commit/109da52141)] - **test**: deflake test-http-dump-req-when-res-ends.js (Luigi Pinca) [#30360](https://github.com/nodejs/node/pull/30360)
- [[`72bbd5cdb0`](https://github.com/nodejs/node/commit/72bbd5cdb0)] - **test**: change var to const in parallel/test-stream-transform-final\* (Kenza Houmani) [#30448](https://github.com/nodejs/node/pull/30448)
- [[`cd82e4d9d8`](https://github.com/nodejs/node/commit/cd82e4d9d8)] - **test**: replace Object.assign with object spread (Grigoriy Levanov) [#30306](https://github.com/nodejs/node/pull/30306)
- [[`aec695eb6c`](https://github.com/nodejs/node/commit/aec695eb6c)] - **test**: fix Python unittests in ./test and ./tools (Christian Clauss) [#30340](https://github.com/nodejs/node/pull/30340)
- [[`ea0c1a67c5`](https://github.com/nodejs/node/commit/ea0c1a67c5)] - **test**: mark test-http-dump-req-when-res-ends as flaky on windows (AshCripps) [#30316](https://github.com/nodejs/node/pull/30316)
- [[`308f5e4710`](https://github.com/nodejs/node/commit/308f5e4710)] - **test**: fix test-benchmark-cluster (Rich Trott) [#30342](https://github.com/nodejs/node/pull/30342)
- [[`bb0727a132`](https://github.com/nodejs/node/commit/bb0727a132)] - **test**: do not run release-npm test without crypto (Michaël Zasso) [#30265](https://github.com/nodejs/node/pull/30265)
- [[`ab5bca379f`](https://github.com/nodejs/node/commit/ab5bca379f)] - **test**: remove AtExit() addon test (Anna Henningsen) [#30275](https://github.com/nodejs/node/pull/30275)
- [[`de68720908`](https://github.com/nodejs/node/commit/de68720908)] - **test**: deflake test-tls-close-notify.js (Luigi Pinca) [#30202](https://github.com/nodejs/node/pull/30202)
- [[`8fe684961b`](https://github.com/nodejs/node/commit/8fe684961b)] - **_Revert_** "**test**: test configure ninja" (Anna Henningsen) [#30295](https://github.com/nodejs/node/pull/30295)
- [[`0dedecc7e0`](https://github.com/nodejs/node/commit/0dedecc7e0)] - **test**: test configure ninja (Patrick Housley) [#30033](https://github.com/nodejs/node/pull/30033)
- [[`01fa18c99c`](https://github.com/nodejs/node/commit/01fa18c99c)] - **(SEMVER-MINOR)** **tls**: cli option to enable TLS key logging to file (Sam Roberts) [#30055](https://github.com/nodejs/node/pull/30055)
- [[`5869f2bee7`](https://github.com/nodejs/node/commit/5869f2bee7)] - **tls**: change loop var to let (Xavier Redondo) [#30445](https://github.com/nodejs/node/pull/30445)
- [[`26a9bdfca3`](https://github.com/nodejs/node/commit/26a9bdfca3)] - **tls**: replace var with let (Daniil Pletnev) [#30308](https://github.com/nodejs/node/pull/30308)
- [[`bad0b66580`](https://github.com/nodejs/node/commit/bad0b66580)] - **tls**: replace var with let and const (Nolik) [#30299](https://github.com/nodejs/node/pull/30299)
- [[`ae5aa3ee83`](https://github.com/nodejs/node/commit/ae5aa3ee83)] - **tls**: refactor tls_wrap.cc (Artem Maksimov) [#30303](https://github.com/nodejs/node/pull/30303)
- [[`80b1717c0f`](https://github.com/nodejs/node/commit/80b1717c0f)] - **tools**: fix build at non-English windows (Rongjian Zhang) [#30492](https://github.com/nodejs/node/pull/30492)
- [[`642b0b883f`](https://github.com/nodejs/node/commit/642b0b883f)] - **tools**: update tzdata to 2019c (Albert Wang) [#30356](https://github.com/nodejs/node/pull/30356)
- [[`3a44adebf8`](https://github.com/nodejs/node/commit/3a44adebf8)] - **tools**: pull xcode_emulation.py from node-gyp (Christian Clauss) [#30272](https://github.com/nodejs/node/pull/30272)
- [[`92fa4e0096`](https://github.com/nodejs/node/commit/92fa4e0096)] - **tools**: make doctool work if no internet available (Richard Lau) [#30214](https://github.com/nodejs/node/pull/30214)
- [[`0f9f18aabe`](https://github.com/nodejs/node/commit/0f9f18aabe)] - **tools**: update certdata.txt (AshCripps) [#30195](https://github.com/nodejs/node/pull/30195)
- [[`dbdc3818e0`](https://github.com/nodejs/node/commit/dbdc3818e0)] - **tools**: check-imports using utf-8 (Christian Clauss) [#30220](https://github.com/nodejs/node/pull/30220)
- [[`3b45f8fd9c`](https://github.com/nodejs/node/commit/3b45f8fd9c)] - **url**: replace var with let in lib/url.js (xefimx) [#30281](https://github.com/nodejs/node/pull/30281)
- [[`35dc84859f`](https://github.com/nodejs/node/commit/35dc84859f)] - **util**: replace var with let (Susana Ferreira) [#30439](https://github.com/nodejs/node/pull/30439)
- [[`3727a6572b`](https://github.com/nodejs/node/commit/3727a6572b)] - **v8**: mark serdes API as stable (Anna Henningsen) [#30234](https://github.com/nodejs/node/pull/30234)
- [[`9b11bdb001`](https://github.com/nodejs/node/commit/9b11bdb001)] - **v8**: inspect unserializable objects (Anna Henningsen) [#30167](https://github.com/nodejs/node/pull/30167)
- [[`2ec40c265a`](https://github.com/nodejs/node/commit/2ec40c265a)] - **(SEMVER-MINOR)** **worker**: allow specifying resource limits (Anna Henningsen) [#26628](https://github.com/nodejs/node/pull/26628)

Windows 32-bit Installer: https://nodejs.org/dist/v13.2.0/node-v13.2.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v13.2.0/node-v13.2.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v13.2.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v13.2.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v13.2.0/node-v13.2.0.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v13.2.0/node-v13.2.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v13.2.0/node-v13.2.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v13.2.0/node-v13.2.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v13.2.0/node-v13.2.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v13.2.0/node-v13.2.0-aix-ppc64.tar.gz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v13.2.0/node-v13.2.0-sunos-x64.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v13.2.0/node-v13.2.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v13.2.0/node-v13.2.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v13.2.0/node-v13.2.0.tar.gz \
Other release files: https://nodejs.org/dist/v13.2.0/ \
Documentation: https://nodejs.org/docs/v13.2.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

9b5751453a3192a92def580c730302c549486644d8b665dd6fb60e39056bdcae  node-v13.2.0-aix-ppc64.tar.gz
2bcba358ef68ea21655728126c678063c60119e18e65d04f615d6b22dba8f7a5  node-v13.2.0-darwin-x64.tar.gz
c3eec7f79fc9e26f36068349dad0aa256564643e2ba19159cb30ad40934fede9  node-v13.2.0-darwin-x64.tar.xz
6239177fe59b664b791662ca7f28933fdbf988a8496e2dd2b345cce022ec5044  node-v13.2.0-headers.tar.gz
606aa979bb2bf723013302bd719da6f83e651c4bc07e5cf8e83ce900eb46178a  node-v13.2.0-headers.tar.xz
b1634a1c9eb8735b25ad21bce3ab5a86d7471982fe2523eeeaf9d831f807864b  node-v13.2.0-linux-arm64.tar.gz
e6c4a5fe57585f69a20bd028275db31f43de421308e31d117f319b577210e527  node-v13.2.0-linux-arm64.tar.xz
f3e918ac85d51d4e415c40b8c5c346a3b01c3154c78fa1ae0df67607eb82051c  node-v13.2.0-linux-armv7l.tar.gz
f43bb572a84ce0dcd65bdd031603d80e30609dc1525468a42733d4d9b7e5afb3  node-v13.2.0-linux-armv7l.tar.xz
52ce1134960644e4c491bac9e20677c528b1eb7d197f78aa9e8a8b96168ae3dd  node-v13.2.0-linux-ppc64le.tar.gz
e2383074aa132893ab7d14d054f8c49f45511e4ab9244accacbc449124dbb2de  node-v13.2.0-linux-ppc64le.tar.xz
12f83d6634c93f1f04e745dedbe8e658b784d7ab35b1de640919ab5241f5edc2  node-v13.2.0-linux-s390x.tar.gz
7b7209c0ca5a04e5259e8603eda90e923482b9018461525f62e519150cf7b96b  node-v13.2.0-linux-s390x.tar.xz
dcf3954ecf6a34d65cab277d3565c654996b1d3e6d07cbbd98939cee0792c668  node-v13.2.0-linux-x64.tar.gz
366df8a38b522a5899c3f48d8c9e359b3370495cf84867b2673dc10483adbdef  node-v13.2.0-linux-x64.tar.xz
6bfc7666b5bdb0cf9e43a957355ddb6718f1b7fa5d678932da89229acff3fa70  node-v13.2.0.pkg
c9c37c847877da602322623bdaf2d681b70b5ca1fe7afe4e035e06f4cc017228  node-v13.2.0-sunos-x64.tar.gz
db7c8c143a9a3a14ed562a98cc11b1c67826351923ff79dee48f4100520de58b  node-v13.2.0-sunos-x64.tar.xz
379dcecb721984a99dc9e16c2a096d6eb7a760d50b188582d9ce33e0478a1a5e  node-v13.2.0.tar.gz
1deec7df3e7a9684f11b1069824cccf9baeecd139a1dbf6a83d29e732d5f0b64  node-v13.2.0.tar.xz
cf1ee206af0a14d9283ba93af3e09275253f69d73551cf7216e14d00296ad692  node-v13.2.0-win-x64.7z
e2866a8f53c45bb544d7b00aed1e5fddbbd7071bd331b02e66a6ce7c38c6918c  node-v13.2.0-win-x64.zip
0454cda2ed6603c0d73121d1ec0aa15d30abafa79fdd742b11d21fa1cef47ba0  node-v13.2.0-win-x86.7z
d1b91964ee50a1f9a0b2d757d5903a0955a3a1a81846a91b93eecdb70347b232  node-v13.2.0-win-x86.zip
64522732b95faa408240bcc21aa4296462297c99edf6d9a426dad757b10279ad  node-v13.2.0-x64.msi
e0169dba11fd29c556a0b3c2bcb39f16d31a706f3141ab7290cbe025b6f49817  node-v13.2.0-x86.msi
c1ca567b15e90aded41d723da6be261cce30b2329a3fe4f39c34aa3239309e39  win-x64/node.exe
004c71119b0111bf572eba31fcb77fa6616d78270e0a5c817c501bb9e4c44084  win-x64/node.lib
e4ca5a5530d192a0441985a14dcb076981d0a2b7c1c967795a42d4acb12bc8f8  win-x64/node_pdb.7z
900034e610c480da30d62e28df70d46cd9a90943340b8cd42bac653db243c353  win-x64/node_pdb.zip
f92d6e04ca87448a98d86a34328d323c297b7e772ba6a3d6333e01890211eb0b  win-x86/node.exe
924e1ac6ef5d778514a454fa49463bb93fdd888cb48fc7781133c1b233b7b12b  win-x86/node.lib
c6036175d76a5bcd5b9d250bef51d70e0eb2b5fa4adcf1aa2c1bc22041b6b085  win-x86/node_pdb.7z
482398a3ad260fd8495760a0d6c401241d84c74c7320af1e68489a54be0bc722  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQEzBAEBCAAdFiEEDv/hvO/ZyE49CYFSkzsB9AtcqUYFAl3XEVQACgkQkzsB9Atc
qUbWxQgAtnQEO384H4q42Myu0uIuuqWAYdHTXj2ehsuo0TJ+fjt1Q9DyFn7wU5BH
0GMFPLnE68kKgMf614AfmY3MxxL9hwkXB2Sw/TWrE8ahW6GS3bHXJP++CJ2TOk0r
3rFa/lI71Abk0mL73Am49T0hY/Grh8hmAjpOlg5n2O6TVfR1Ve4EZGs6BTapmgXN
xc/Aj/J/iG8IPMR1IoqAawvzB/85nvx5iURchR9ysGyE5t1mky3pUVO/t0ngTqrv
uB48P4MKM5t9UXcRPNLnH/kJuHnYsA7iRjtchefbONFIDox/6A84vRX8PToHTJsV
JnNNz6GjnIrO8j13YJDdQTvVD3WEuQ==
=i8MK
-----END PGP SIGNATURE-----

```
