---
date: '2024-07-09T02:31:49.222Z'
category: release
title: Node v22.4.1 (Current)
layout: blog-post
author: <PERSON>
---

## 2024-07-08, Version 22.4.1 (Current), @RafaelGSS

This is a security release.

### Notable Changes

- CVE-2024-36138 - Bypass incomplete fix of CVE-2024-27980 (High)
- CVE-2024-22020 - Bypass network import restriction via data URL (Medium)
- CVE-2024-22018 - fs.lstat bypasses permission model (Low)
- CVE-2024-36137 - fs.fchown/fchmod bypasses permission model (Low)
- CVE-2024-37372 - Permission model improperly processes UNC paths (Low)

### Commits

- \[[`110902ff5e`](https://github.com/nodejs/node/commit/110902ff5e)] - **lib,esm**: handle bypass network-import via data: (RafaelGSS) [nodejs-private/node-private#522](https://github.com/nodejs-private/node-private/pull/522)
- \[[`0a0de3d491`](https://github.com/nodejs/node/commit/0a0de3d491)] - **lib,permission**: support fs.lstat (RafaelGSS)
- \[[`93574335ff`](https://github.com/nodejs/node/commit/93574335ff)] - **lib,permission**: disable fchmod/fchown when pm enabled (RafaelGSS) [nodejs-private/node-private#584](https://github.com/nodejs-private/node-private/pull/584)
- \[[`09899e6302`](https://github.com/nodejs/node/commit/09899e6302)] - **src**: handle permissive extension on cmd check (RafaelGSS) [nodejs-private/node-private#596](https://github.com/nodejs-private/node-private/pull/596)
- \[[`5d9c811634`](https://github.com/nodejs/node/commit/5d9c811634)] - **src,permission**: fix UNC path resolution (RafaelGSS) [nodejs-private/node-private#581](https://github.com/nodejs-private/node-private/pull/581)

Windows 32-bit Installer: https://nodejs.org/dist/v22.4.1/node-v22.4.1-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v22.4.1/node-v22.4.1-x64.msi \
Windows ARM 64-bit Installer: https://nodejs.org/dist/v22.4.1/node-v22.4.1-arm64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v22.4.1/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v22.4.1/win-x64/node.exe \
Windows ARM 64-bit Binary: https://nodejs.org/dist/v22.4.1/win-arm64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v22.4.1/node-v22.4.1.pkg \
macOS Apple Silicon 64-bit Binary: https://nodejs.org/dist/v22.4.1/node-v22.4.1-darwin-arm64.tar.gz \
macOS Intel 64-bit Binary: https://nodejs.org/dist/v22.4.1/node-v22.4.1-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v22.4.1/node-v22.4.1-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v22.4.1/node-v22.4.1-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v22.4.1/node-v22.4.1-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v22.4.1/node-v22.4.1-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v22.4.1/node-v22.4.1-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v22.4.1/node-v22.4.1-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v22.4.1/node-v22.4.1.tar.gz \
Other release files: https://nodejs.org/dist/v22.4.1/ \
Documentation: https://nodejs.org/docs/v22.4.1/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

b5bf624fd09c198397dccba48efe36f1ceca282f86f60405fea4ee8954a1466b  node-v22.4.1-aix-ppc64.tar.gz
8015d8bccad6bea13f8712db5947afced3dabca9bfe103cca3cbc8f0047ee450  node-v22.4.1-arm64.msi
6e0b15d15a80878a57fc938588f64b1d6b59314ef47a0d439e17cb5e68b82a82  node-v22.4.1-darwin-arm64.tar.gz
8bc5f7f98575146b73b0cf99d2246e32faaa0f0865cc24cc706d05fdf8d99aeb  node-v22.4.1-darwin-arm64.tar.xz
64aae9256cdffddbf535a7f6d30c5d2d4a8e01eafb035ab743203e402c68663b  node-v22.4.1-darwin-x64.tar.gz
4d8b1f7764afd59a4a4c968ec481ace1338f03131ed6720bde39f26979070d8b  node-v22.4.1-darwin-x64.tar.xz
e82b1cc359068820222a911698149b48f59633dba5cdefa4a77c02ef0c7f79b1  node-v22.4.1-headers.tar.gz
0aa57f793cfcb3e989c2f49f8d1592475d69d2ff8c026162f65098f5163a41d0  node-v22.4.1-headers.tar.xz
1816e42d4848aa1484910373a1f2f68f43fd6f96a4ef478a9553d05ffa3f8fb2  node-v22.4.1-linux-arm64.tar.gz
8c9efb158660e1645b6b0bee6173903625206f43537b9f3a43aa56e43cd1fa7d  node-v22.4.1-linux-arm64.tar.xz
6b0b400aab703da6a4d82cb465e74b53e79762a5a59368323f58a7c23e2ef5c4  node-v22.4.1-linux-armv7l.tar.gz
b7b85046a9fde5ba6759aef6808014594aef689386d8ffbe2a2145f1905315c1  node-v22.4.1-linux-armv7l.tar.xz
bacefdced5e4bf511c6cedf60d0e4c1057f587b61a8c20bbff790b4cce44865a  node-v22.4.1-linux-ppc64le.tar.gz
c6ecc116c89238138a3f159015bfb8ff1f7b02600e0609d2283f807ebb244a90  node-v22.4.1-linux-ppc64le.tar.xz
819dbee557b7ef5f48a4e9a3fb25caaefb3b262034c77443a5f6d08f9de741ba  node-v22.4.1-linux-s390x.tar.gz
ee6d3825c42853921883bb809b38f7e21658cdaba82f498cae54d865cce2653d  node-v22.4.1-linux-s390x.tar.xz
addb41bd7d5bdef51dc3bd76292889692664b3c9de1b7c5f89aba95e474aee84  node-v22.4.1-linux-x64.tar.gz
e85039bcb298c7a7c9324aebad3f0fb2c472de4c5cedf9016c37f954687a22a8  node-v22.4.1-linux-x64.tar.xz
72c77162f1508e4b03e95361deda098ee16770eed5c1f31789c2bffe4818dc0e  node-v22.4.1-win-arm64.7z
915d476e2a27a09a3ff599d8b7453898dbeded8548beb272dea4242178cf31d8  node-v22.4.1-win-arm64.zip
24e9d5be7edd1433e633b3b8fe3c704f92c9580678c2a595570a67bdc25c01de  node-v22.4.1-win-x64.7z
d1417d8b605636b6b482ad308763b36d071cf592efb412c8dec0d65b235dc9da  node-v22.4.1-win-x64.zip
1751dfd48920506274bd4b5243f5802acd4ec9d2fdab08dc736a61a1bf0c88a8  node-v22.4.1-win-x86.7z
f6b41e5c53d888da751b988d2103eaa64c2bca7c617d9ad784e9bb48b573c56d  node-v22.4.1-win-x86.zip
82ab3431a35b68572d55f7a10049d8cdfe0f3d01a9f085daed4e7fa4caa9f26e  node-v22.4.1-x64.msi
ed819d12c5e59a1752fad8192177aa307a360d5f5d73a70806e772e3b98a74fe  node-v22.4.1-x86.msi
88966c6db8fcbdb5f9f816d175641071f3de3b8cb332f7e5c1f28636ba01d2d1  node-v22.4.1.pkg
b3051c3358c96d06dd17305c065cc6f5205c1f4d72dd42cb184f7ba79605f8a8  node-v22.4.1.tar.gz
65fc857f5aa8256aafc900b344c0115c9aeae25a02541fd5ce0dbd4dfd1c5fb9  node-v22.4.1.tar.xz
048febf57e8f3279197721d683815d2a586458fae8f7ad70656a3b3ec378c339  win-arm64/node.exe
30e63a6726cda6539eeb37c311adf915bccd5c1462723b97a6c07ac91e8ae728  win-arm64/node.lib
29674f442192ba7d4817563740f2c26a6164cc4371f853a0715e628028e82d9c  win-arm64/node_pdb.7z
052350b350e9730c9da6addf28b88d51199ecbf179ea84dceff6335709f60d05  win-arm64/node_pdb.zip
9769486f6311f0e12583a4603f926053e0ab6a899126afd15cd2e7bb9184509f  win-x64/node.exe
c4d08d45267da3625a30730bf5c8e41518f25d9809179feb267f1b393f5c5f05  win-x64/node.lib
58ade781306ab582a9a5e71a36d0f54156b4dc34e089cbce91eb64b9c102fe93  win-x64/node_pdb.7z
fdfd36bf2e6ff637232e3d0c2006a420f24bb8755b3932285ccb444093ece3ae  win-x64/node_pdb.zip
daf7604b21a7dbec3d76e23fb07014ceddaade6de7865631e697c647c0d66ea2  win-x86/node.exe
fc3bf3c1e561da1e1c152be9aa5ed1bce8d263a5124841a4ba41ebc37c727f3e  win-x86/node.lib
29eed4d9e9a57c1299e40c6bf831889b223e9da8ab6c19228d015fbff204ae0e  win-x86/node_pdb.7z
5b45c9697c501fbbb749996f235b801db706bc0d53611bfe910b5cde5763eca0  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQGzBAEBCAAdFiEEiQwI24V5Fi/uDfnbi+q0389VXvQFAmaMLBsACgkQi+q0389V
XvT6owv/Z/GHd/i4JqR4hh2bZMqPx0KGhZ5su81i7VoLIfWHaeB0slOUBJB2dOMY
GtKZsKhg/duCZSh6gBpQrsb+0+SFjzI6bWPzOiuskKXRzb7SDJ7yRICFU40V1QhN
A/bI0eqc/XvKr6eHYjiQuomKZYeMRzQ+06qFK/xTEcORJdA8t/k5RXD3BXnBfBEF
BvHHIOqc13kpOjwWz6Zvh8iuWG36wn/wMKaBDLPZZYpSNUKPwNYw6IzjQlkElyYv
Kc+pd8B4x2IxYmVyaZk9spnzYDR++VU30AsOKpyfynu0t1zKZqqf1HgZPf6+W3oe
eUlqI6yigoaLbF63/1+QXzPLNrWODxBx/3DUuAfPM/3jATREEb517tk1ztid1xZ5
4nl0Pm/hPFBV+xJCIjx3xyAPSGsun1ctv2rm9A2OSJesTOjt1nsdCkt6hCjevt4r
xRvKW0F2und30hdC8sqQ8bT5j90DBT/Kn9ocP7ii9SiPKmOBaxFytWC1IWxw+mj5
qjVaLj7j
=Q/lv
-----END PGP SIGNATURE-----
```
