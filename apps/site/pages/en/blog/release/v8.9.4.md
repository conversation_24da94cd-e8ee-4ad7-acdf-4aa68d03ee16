---
date: '2018-01-03T04:33:21.807Z'
category: release
title: Node v8.9.4 (LTS)
layout: blog-post
author: <PERSON>
---

### Notable Changes

- **deps**:
  - upgrade npm to 5.6.0 (<PERSON>) [#17535](https://github.com/nodejs/node/pull/17535)
- **build**:
  - configure can now be run from any directory (Gibson Fahnestock) [#17321](https://github.com/nodejs/node/pull/17321)

### Commits

- [[`62ad4cca07`](https://github.com/nodejs/node/commit/62ad4cca07)] - **tools/doc**: add tools/remark-\* to eslint<PERSON><PERSON> (<PERSON>) [#17240](https://github.com/nodejs/node/pull/17240)
- [[`ce91a38970`](https://github.com/nodejs/node/commit/ce91a38970)] - **benchmark**: fix http/simple.js benchmark (<PERSON><PERSON><PERSON>) [#17583](https://github.com/nodejs/node/pull/17583)
- [[`3fe7f9f102`](https://github.com/nodejs/node/commit/3fe7f9f102)] - **benchmark**: set maxHeaderListPairs in h2 headers.js (Anatoli Papirovski) [#17194](https://github.com/nodejs/node/pull/17194)
- [[`4597bd753a`](https://github.com/nodejs/node/commit/4597bd753a)] - **benchmark**: use unique filenames in fs benchmarks (Rich Trott) [#16776](https://github.com/nodejs/node/pull/16776)
- [[`92723701cd`](https://github.com/nodejs/node/commit/92723701cd)] - **benchmark,path**: remove unused variables (薛定谔的猫) [#15789](https://github.com/nodejs/node/pull/15789)
- [[`58a667c884`](https://github.com/nodejs/node/commit/58a667c884)] - **build**: add a `make help` option for common targets (Gibson Fahnestock) [#17323](https://github.com/nodejs/node/pull/17323)
- [[`5b04621c40`](https://github.com/nodejs/node/commit/5b04621c40)] - **build**: allow running configure from any directory (Gibson Fahnestock) [#17321](https://github.com/nodejs/node/pull/17321)
- [[`6ed330610a`](https://github.com/nodejs/node/commit/6ed330610a)] - **build**: define HAVE_OPENSSL macro for cctest (Matheus Marchini) [#17461](https://github.com/nodejs/node/pull/17461)
- [[`2e6f96e15d`](https://github.com/nodejs/node/commit/2e6f96e15d)] - **build**: add serial commas to messages in configure script (Rich Trott) [#17464](https://github.com/nodejs/node/pull/17464)
- [[`1802f3f8fc`](https://github.com/nodejs/node/commit/1802f3f8fc)] - **build**: fix test-v8 target (Michaël Zasso) [#17269](https://github.com/nodejs/node/pull/17269)
- [[`337d2b9972`](https://github.com/nodejs/node/commit/337d2b9972)] - **build**: add make lint-js-fix (Joyee Cheung) [#17283](https://github.com/nodejs/node/pull/17283)
- [[`134bbd8f30`](https://github.com/nodejs/node/commit/134bbd8f30)] - **build**: fix bsd build with gcc (Matheus Marchini) [#16737](https://github.com/nodejs/node/pull/16737)
- [[`bacbdc968d`](https://github.com/nodejs/node/commit/bacbdc968d)] - **build**: remove empty VCLibrarianTool entry (Daniel Bevenius) [#17191](https://github.com/nodejs/node/pull/17191)
- [[`2c891412b2`](https://github.com/nodejs/node/commit/2c891412b2)] - **build**: Allow linking against an external copy of nghttp2. (Ed Schouten) [#16788](https://github.com/nodejs/node/pull/16788)
- [[`1941d0a405`](https://github.com/nodejs/node/commit/1941d0a405)] - **build**: do not build doc in source tarball (Joyee Cheung) [#17100](https://github.com/nodejs/node/pull/17100)
- [[`792eee9803`](https://github.com/nodejs/node/commit/792eee9803)] - **build**: minor corrections to configure descriptions (Daniel Bevenius) [#17094](https://github.com/nodejs/node/pull/17094)
- [[`3036b36b76`](https://github.com/nodejs/node/commit/3036b36b76)] - **build**: enforce order of dependency when building addons (Joyee Cheung) [#17048](https://github.com/nodejs/node/pull/17048)
- [[`2f708c172f`](https://github.com/nodejs/node/commit/2f708c172f)] - **build**: fix cctest target --with-dtrace (Daniel Bevenius) [#17039](https://github.com/nodejs/node/pull/17039)
- [[`9532e982dc`](https://github.com/nodejs/node/commit/9532e982dc)] - **build**: prevent echoing of recipes for test target (Daniel Bevenius) [#17010](https://github.com/nodejs/node/pull/17010)
- [[`73eab91c8e`](https://github.com/nodejs/node/commit/73eab91c8e)] - **build**: fix cctest compilation (Daniel Bevenius) [#16887](https://github.com/nodejs/node/pull/16887)
- [[`811892edf0`](https://github.com/nodejs/node/commit/811892edf0)] - **build,win**: vcbuild refactoring call configure (Refael Ackermann) [#17299](https://github.com/nodejs/node/pull/17299)
- [[`54f6b294a1`](https://github.com/nodejs/node/commit/54f6b294a1)] - **crypto**: use SetNull instead of Set (Daniel Bevenius) [#17521](https://github.com/nodejs/node/pull/17521)
- [[`000be870e0`](https://github.com/nodejs/node/commit/000be870e0)] - **crypto**: make createXYZ inlineable (Matteo Collina) [#16067](https://github.com/nodejs/node/pull/16067)
- [[`13e853fb68`](https://github.com/nodejs/node/commit/13e853fb68)] - **deps**: upgrade npm to 5.6.0 (Kat Marchán) [#17535](https://github.com/nodejs/node/pull/17535)
- [[`c57cd9bf8b`](https://github.com/nodejs/node/commit/c57cd9bf8b)] - **deps**: V8: cherry-pick cfc3404f from upstream (Ali Ijaz Sheikh) [#17354](https://github.com/nodejs/node/pull/17354)
- [[`f34ee5c954`](https://github.com/nodejs/node/commit/f34ee5c954)] - **deps**: V8: backport 14ac02c from upstream (Ali Ijaz Sheikh) [#17512](https://github.com/nodejs/node/pull/17512)
- [[`5076cf3de7`](https://github.com/nodejs/node/commit/5076cf3de7)] - **doc**: use "JavaScript" instead of "Javascript" (Rich Trott) [#17163](https://github.com/nodejs/node/pull/17163)
- [[`81afb5c4c7`](https://github.com/nodejs/node/commit/81afb5c4c7)] - **doc**: prepare for v8/V8 linting in doc text (Rich Trott) [#17163](https://github.com/nodejs/node/pull/17163)
- [[`772ad878be`](https://github.com/nodejs/node/commit/772ad878be)] - **doc**: add capitalization styling to STYLE_GUIDE (Rich Trott) [#17163](https://github.com/nodejs/node/pull/17163)
- [[`9a06d988fd`](https://github.com/nodejs/node/commit/9a06d988fd)] - **doc**: make error descriptions more concise (Rich Trott) [#16954](https://github.com/nodejs/node/pull/16954)
- [[`d85a63546c`](https://github.com/nodejs/node/commit/d85a63546c)] - **doc**: fix modules.md export example (Anatoli Papirovski) [#17579](https://github.com/nodejs/node/pull/17579)
- [[`08220a309e`](https://github.com/nodejs/node/commit/08220a309e)] - **doc**: add link to debugger in process.md (Delapouite) [#17522](https://github.com/nodejs/node/pull/17522)
- [[`26e0fa8979`](https://github.com/nodejs/node/commit/26e0fa8979)] - **doc**: simplify and clarify FIPS text in BUILDING.md (Rich Trott) [#17538](https://github.com/nodejs/node/pull/17538)
- [[`f36ba1adca`](https://github.com/nodejs/node/commit/f36ba1adca)] - **doc**: esm loader example with module.builtinModules (Guy Bedford) [#17385](https://github.com/nodejs/node/pull/17385)
- [[`545c526b4e`](https://github.com/nodejs/node/commit/545c526b4e)] - **doc**: 'constructor' implies use of new keyword (Cameron Moorehead) [#17364](https://github.com/nodejs/node/pull/17364)
- [[`e53691c208`](https://github.com/nodejs/node/commit/e53691c208)] - **doc**: add "Hello world" example for N-API (Franziska Hinkelmann) [#17425](https://github.com/nodejs/node/pull/17425)
- [[`ce4a49ee7a`](https://github.com/nodejs/node/commit/ce4a49ee7a)] - **doc**: immprove inode text in fs.md (Rich Trott) [#17519](https://github.com/nodejs/node/pull/17519)
- [[`e7c1578768`](https://github.com/nodejs/node/commit/e7c1578768)] - **doc**: improve text for Console constructor (Rich Trott) [#17519](https://github.com/nodejs/node/pull/17519)
- [[`820d97b0ed`](https://github.com/nodejs/node/commit/820d97b0ed)] - **doc**: improve readability of README.md (Rich Trott) [#17519](https://github.com/nodejs/node/pull/17519)
- [[`29cda14049`](https://github.com/nodejs/node/commit/29cda14049)] - **doc**: improve readability of COLLABORATOR_GUIDE.md (Rich Trott) [#17519](https://github.com/nodejs/node/pull/17519)
- [[`9390ad1a65`](https://github.com/nodejs/node/commit/9390ad1a65)] - **doc**: add info on post-publishing ARM6 builds (Michael Dawson) [#17455](https://github.com/nodejs/node/pull/17455)
- [[`418ee1c13a`](https://github.com/nodejs/node/commit/418ee1c13a)] - **doc**: mention node-test-pull-request-lite job (Jon Moss) [#17513](https://github.com/nodejs/node/pull/17513)
- [[`2c327c6c68`](https://github.com/nodejs/node/commit/2c327c6c68)] - **doc**: fix typo in repl.md (Rich Trott) [#17502](https://github.com/nodejs/node/pull/17502)
- [[`07735b9fc2`](https://github.com/nodejs/node/commit/07735b9fc2)] - **doc**: fix common typo involving one-time listeners (Rich Trott) [#17502](https://github.com/nodejs/node/pull/17502)
- [[`25d7b8a4af`](https://github.com/nodejs/node/commit/25d7b8a4af)] - **doc**: fix typo in dns.md (Rich Trott) [#17502](https://github.com/nodejs/node/pull/17502)
- [[`4d826f09c1`](https://github.com/nodejs/node/commit/4d826f09c1)] - **doc**: remove unused link reference (Anatoli Papirovski) [#17510](https://github.com/nodejs/node/pull/17510)
- [[`8767acb401`](https://github.com/nodejs/node/commit/8767acb401)] - **doc**: remove IPC channel implementation details (Bartosz Sosnowski) [#17460](https://github.com/nodejs/node/pull/17460)
- [[`b49dfeed7b`](https://github.com/nodejs/node/commit/b49dfeed7b)] - **doc**: update AUTHORS list (Michaël Zasso) [#17452](https://github.com/nodejs/node/pull/17452)
- [[`9519616564`](https://github.com/nodejs/node/commit/9519616564)] - **doc**: use serial comma in tls.md (Rich Trott) [#17464](https://github.com/nodejs/node/pull/17464)
- [[`4667de8aac`](https://github.com/nodejs/node/commit/4667de8aac)] - **doc**: add serial comma in CPP_STYLE_GUIDE.md (Rich Trott) [#17464](https://github.com/nodejs/node/pull/17464)
- [[`bc9a490f54`](https://github.com/nodejs/node/commit/bc9a490f54)] - **doc**: edit module introduction (Rich Trott) [#17463](https://github.com/nodejs/node/pull/17463)
- [[`9b7168f3cb`](https://github.com/nodejs/node/commit/9b7168f3cb)] - **doc**: standardize preposition usage in fs.md (Rich Trott) [#17463](https://github.com/nodejs/node/pull/17463)
- [[`cfaba6b0ba`](https://github.com/nodejs/node/commit/cfaba6b0ba)] - **doc**: improve punctuation in fs.open() text (Rich Trott) [#17463](https://github.com/nodejs/node/pull/17463)
- [[`0d7dca5858`](https://github.com/nodejs/node/commit/0d7dca5858)] - **doc**: use colon consistently in assert.md (Rich Trott) [#17463](https://github.com/nodejs/node/pull/17463)
- [[`cb262d284e`](https://github.com/nodejs/node/commit/cb262d284e)] - **doc**: update example in module registration (Franziska Hinkelmann) [#17424](https://github.com/nodejs/node/pull/17424)
- [[`43215c6c3c`](https://github.com/nodejs/node/commit/43215c6c3c)] - **doc**: introduce categories to Cpp style guide (Franziska Hinkelmann) [#17095](https://github.com/nodejs/node/pull/17095)
- [[`afd001b29b`](https://github.com/nodejs/node/commit/afd001b29b)] - **doc**: add missing serial commas (Rich Trott) [#17384](https://github.com/nodejs/node/pull/17384)
- [[`aa45aa4a90`](https://github.com/nodejs/node/commit/aa45aa4a90)] - **doc**: be concise about serial commas (Rich Trott) [#17384](https://github.com/nodejs/node/pull/17384)
- [[`a3d218dafb`](https://github.com/nodejs/node/commit/a3d218dafb)] - **doc**: document tls.checkServerIdentity (Hannes Magnusson) [#17203](https://github.com/nodejs/node/pull/17203)
- [[`e786fde2a5`](https://github.com/nodejs/node/commit/e786fde2a5)] - **doc**: improve checkServerIdentity docs (Hannes Magnusson) [#17203](https://github.com/nodejs/node/pull/17203)
- [[`b18cd4e8b1`](https://github.com/nodejs/node/commit/b18cd4e8b1)] - **doc**: add guide to maintaining npm (Myles Borins) [#16541](https://github.com/nodejs/node/pull/16541)
- [[`3690d73901`](https://github.com/nodejs/node/commit/3690d73901)] - **doc**: fix doc example for cctest (Matheus Marchini) [#17355](https://github.com/nodejs/node/pull/17355)
- [[`0605f01b05`](https://github.com/nodejs/node/commit/0605f01b05)] - **doc**: clarify fast-track of reversions (Refael Ackermann) [#17332](https://github.com/nodejs/node/pull/17332)
- [[`2ceecce725`](https://github.com/nodejs/node/commit/2ceecce725)] - **doc**: fix typo in stream.md (Matthew Leon) [#17357](https://github.com/nodejs/node/pull/17357)
- [[`5d1b76c1bd`](https://github.com/nodejs/node/commit/5d1b76c1bd)] - **doc**: non-partitioned async crypto operations (Jamie Davis) [#17250](https://github.com/nodejs/node/pull/17250)
- [[`04732f1bdf`](https://github.com/nodejs/node/commit/04732f1bdf)] - **doc**: move Code of Conduct to admin repo (Myles Borins) [#17301](https://github.com/nodejs/node/pull/17301)
- [[`f1b4c4b2ee`](https://github.com/nodejs/node/commit/f1b4c4b2ee)] - **doc**: fix typo occuring -\> occurring (Leko) [#17350](https://github.com/nodejs/node/pull/17350)
- [[`3043d1573c`](https://github.com/nodejs/node/commit/3043d1573c)] - **doc**: Add link for ECMAScript 2015 (smatsu-hl) [#17317](https://github.com/nodejs/node/pull/17317)
- [[`3efa621878`](https://github.com/nodejs/node/commit/3efa621878)] - **doc**: caution against removing pseudoheaders (James M Snell) [#17329](https://github.com/nodejs/node/pull/17329)
- [[`7b7ab9cfb3`](https://github.com/nodejs/node/commit/7b7ab9cfb3)] - **doc**: replace string with template string (Leko) [#17316](https://github.com/nodejs/node/pull/17316)
- [[`1289cbd09d`](https://github.com/nodejs/node/commit/1289cbd09d)] - **doc**: replace function with arrow function in vm.md (narirou) [#17307](https://github.com/nodejs/node/pull/17307)
- [[`891e469490`](https://github.com/nodejs/node/commit/891e469490)] - **doc**: replace function with arrow function (Leko) [#17304](https://github.com/nodejs/node/pull/17304)
- [[`ceab92804d`](https://github.com/nodejs/node/commit/ceab92804d)] - **doc**: fix typo in api doc of url.format(urlObject) (pkovacs) [#17295](https://github.com/nodejs/node/pull/17295)
- [[`cd3defe124`](https://github.com/nodejs/node/commit/cd3defe124)] - **doc**: add ES Modules entry to who-to-cc (Rich Trott) [#17205](https://github.com/nodejs/node/pull/17205)
- [[`64b7398daf`](https://github.com/nodejs/node/commit/64b7398daf)] - **doc**: add maclover7 to collaborators (Jon Moss) [#17289](https://github.com/nodejs/node/pull/17289)
- [[`092aa6079a`](https://github.com/nodejs/node/commit/092aa6079a)] - **doc**: update http URLs to https in README.md (Ronald Eddy Jr) [#17264](https://github.com/nodejs/node/pull/17264)
- [[`342a291bd0`](https://github.com/nodejs/node/commit/342a291bd0)] - **doc**: update http URLs to https in doc/api (Ronald Eddy Jr) [#17263](https://github.com/nodejs/node/pull/17263)
- [[`c1db3a2507`](https://github.com/nodejs/node/commit/c1db3a2507)] - **doc**: update http URLs to https in GOVERNANCE.md (Ronald Eddy Jr) [#17262](https://github.com/nodejs/node/pull/17262)
- [[`5fe370cc99`](https://github.com/nodejs/node/commit/5fe370cc99)] - **doc**: update http URLs to https in CONTRIBUTING.md (Ronald Eddy Jr) [#17261](https://github.com/nodejs/node/pull/17261)
- [[`36f5f13bb2`](https://github.com/nodejs/node/commit/36f5f13bb2)] - **doc**: add SharedArrayBuffer to Buffer documentation (Thomas den Hollander) [#15489](https://github.com/nodejs/node/pull/15489)
- [[`1b689fb2a1`](https://github.com/nodejs/node/commit/1b689fb2a1)] - **doc**: document resolve hook formats (Lucas Azzola) [#16375](https://github.com/nodejs/node/pull/16375)
- [[`ec1a35c6b6`](https://github.com/nodejs/node/commit/ec1a35c6b6)] - **doc**: fs.readFile is async but not partitioned (Jamie Davis) [#17154](https://github.com/nodejs/node/pull/17154)
- [[`eb041a0cec`](https://github.com/nodejs/node/commit/eb041a0cec)] - **doc**: use better terminology for build machines (Anna Henningsen) [#17142](https://github.com/nodejs/node/pull/17142)
- [[`fef966f4c0`](https://github.com/nodejs/node/commit/fef966f4c0)] - **doc**: update mgol in AUTHORS.txt, add to .mailmap (Michał Gołębiowski-Owczarek) [#17239](https://github.com/nodejs/node/pull/17239)
- [[`38a78f4010`](https://github.com/nodejs/node/commit/38a78f4010)] - **doc**: update release table in V8 guide (Ali Ijaz Sheikh) [#17136](https://github.com/nodejs/node/pull/17136)
- [[`641fae08e6`](https://github.com/nodejs/node/commit/641fae08e6)] - **doc**: add guybedford to collaborators (Guy Bedford) [#17197](https://github.com/nodejs/node/pull/17197)
- [[`c67bab338e`](https://github.com/nodejs/node/commit/c67bab338e)] - **doc**: update AUTHORS list (Michaël Zasso) [#16571](https://github.com/nodejs/node/pull/16571)
- [[`017a75bcdf`](https://github.com/nodejs/node/commit/017a75bcdf)] - **doc**: normalize ToC indentation with heading levels in README (Rich Trott) [#17106](https://github.com/nodejs/node/pull/17106)
- [[`214959320f`](https://github.com/nodejs/node/commit/214959320f)] - **doc**: add Contributing to Node.js to the README ToC (Rich Trott) [#17106](https://github.com/nodejs/node/pull/17106)
- [[`738261499f`](https://github.com/nodejs/node/commit/738261499f)] - **doc**: merge Working Groups with Contributing to Node.js in README (Rich Trott) [#17106](https://github.com/nodejs/node/pull/17106)
- [[`b9d51b8e9c`](https://github.com/nodejs/node/commit/b9d51b8e9c)] - **doc**: remove IRC node-dev link from README (Rich Trott) [#17106](https://github.com/nodejs/node/pull/17106)
- [[`d6e02867c8`](https://github.com/nodejs/node/commit/d6e02867c8)] - **doc**: add missing introduced_in comments (Luigi Pinca) [#16741](https://github.com/nodejs/node/pull/16741)
- [[`7ecaa1b849`](https://github.com/nodejs/node/commit/7ecaa1b849)] - **doc**: change v8 to V8 (Rich Trott) [#17089](https://github.com/nodejs/node/pull/17089)
- [[`58d986f2ad`](https://github.com/nodejs/node/commit/58d986f2ad)] - **doc**: avoid mentioning 'uncaughtException' (Luigi Pinca) [#16905](https://github.com/nodejs/node/pull/16905)
- [[`7ee9d69888`](https://github.com/nodejs/node/commit/7ee9d69888)] - **doc**: add note about using cluster without networking (pimlie) [#17031](https://github.com/nodejs/node/pull/17031)
- [[`9e28f6f546`](https://github.com/nodejs/node/commit/9e28f6f546)] - **doc**: explicitly document highWaterMark option (Sebastian Silbermann) [#17049](https://github.com/nodejs/node/pull/17049)
- [[`21a017fb9b`](https://github.com/nodejs/node/commit/21a017fb9b)] - **doc**: fix a link in dgram.md (Vse Mozhet Byt) [#17107](https://github.com/nodejs/node/pull/17107)
- [[`3b524cf510`](https://github.com/nodejs/node/commit/3b524cf510)] - **doc**: reorganize collaborator guide (Joyee Cheung) [#17056](https://github.com/nodejs/node/pull/17056)
- [[`2145b3734d`](https://github.com/nodejs/node/commit/2145b3734d)] - **doc**: delete unused definition in README.md (Vse Mozhet Byt) [#17108](https://github.com/nodejs/node/pull/17108)
- [[`f67c0bfac0`](https://github.com/nodejs/node/commit/f67c0bfac0)] - **doc**: add Support section in README (Rich Trott) [#16533](https://github.com/nodejs/node/pull/16533)
- [[`010a3309a6`](https://github.com/nodejs/node/commit/010a3309a6)] - **doc**: document common pattern for instanceof checks (Michael Dawson) [#16699](https://github.com/nodejs/node/pull/16699)
- [[`706aab9d91`](https://github.com/nodejs/node/commit/706aab9d91)] - **doc**: mention smart pointers in Cpp style guide (Franziska Hinkelmann) [#17055](https://github.com/nodejs/node/pull/17055)
- [[`6b99323ceb`](https://github.com/nodejs/node/commit/6b99323ceb)] - **doc**: add Table of Contents to Cpp style guide (Franziska Hinkelmann) [#17052](https://github.com/nodejs/node/pull/17052)
- [[`996108cb14`](https://github.com/nodejs/node/commit/996108cb14)] - **doc**: add hashseed to collaborators (Yang Guo) [#16863](https://github.com/nodejs/node/pull/16863)
- [[`8622353c68`](https://github.com/nodejs/node/commit/8622353c68)] - **doc**: make stream.Readable consistent (Sakthipriyan Vairamani (thefourtheye)) [#16786](https://github.com/nodejs/node/pull/16786)
- [[`58cd649146`](https://github.com/nodejs/node/commit/58cd649146)] - **doc**: correct effects to affects (gowpen) [#16794](https://github.com/nodejs/node/pull/16794)
- [[`24178ac002`](https://github.com/nodejs/node/commit/24178ac002)] - **doc**: correct EventEmitter reference (gowpen) [#16791](https://github.com/nodejs/node/pull/16791)
- [[`b8561c2261`](https://github.com/nodejs/node/commit/b8561c2261)] - **doc**: fix a typo in n-api documentation (Vipin Menon) [#16879](https://github.com/nodejs/node/pull/16879)
- [[`4ede5eceb7`](https://github.com/nodejs/node/commit/4ede5eceb7)] - **doc**: fix typos in N-API (Swathi Kalahastri) [#16911](https://github.com/nodejs/node/pull/16911)
- [[`f1de0da7b9`](https://github.com/nodejs/node/commit/f1de0da7b9)] - **doc,test**: remove unnecessary await with return instances (Rich Trott) [#17265](https://github.com/nodejs/node/pull/17265)
- [[`97d9be555a`](https://github.com/nodejs/node/commit/97d9be555a)] - **doc,win**: clarify WSL support (João Reis) [#17008](https://github.com/nodejs/node/pull/17008)
- [[`20b40d154f`](https://github.com/nodejs/node/commit/20b40d154f)] - **errors,tools**: ASCIIbetical instead of alphabetical (Refael Ackermann) [#15578](https://github.com/nodejs/node/pull/15578)
- [[`b413e0920e`](https://github.com/nodejs/node/commit/b413e0920e)] - **fs**: use arrow functions instead of `.bind` and `self` (Weijia Wang) [#17137](https://github.com/nodejs/node/pull/17137)
- [[`63a35133f9`](https://github.com/nodejs/node/commit/63a35133f9)] - **http**: do not assign intermediate variable (Jon Moss) [#17335](https://github.com/nodejs/node/pull/17335)
- [[`673e2637f8`](https://github.com/nodejs/node/commit/673e2637f8)] - **http2**: use more descriptive names (James M Snell) [#17328](https://github.com/nodejs/node/pull/17328)
- [[`9fd0b32c38`](https://github.com/nodejs/node/commit/9fd0b32c38)] - **http2**: remove unnecessary event handlers (James M Snell) [#17328](https://github.com/nodejs/node/pull/17328)
- [[`31cdbd636a`](https://github.com/nodejs/node/commit/31cdbd636a)] - **http2**: reduce code duplication in settings (James M Snell) [#17328](https://github.com/nodejs/node/pull/17328)
- [[`bdcbe81ad0`](https://github.com/nodejs/node/commit/bdcbe81ad0)] - **http2**: general cleanups (James M Snell) [#17328](https://github.com/nodejs/node/pull/17328)
- [[`4b43e52f1a`](https://github.com/nodejs/node/commit/4b43e52f1a)] - **inspector**: no async tracking for promises (Anna Henningsen) [#17118](https://github.com/nodejs/node/pull/17118)
- [[`bf123f3dc2`](https://github.com/nodejs/node/commit/bf123f3dc2)] - **inspector**: include node_platform.h header (Alexey Kuzmin) [#16677](https://github.com/nodejs/node/pull/16677)
- [[`e6a568b49d`](https://github.com/nodejs/node/commit/e6a568b49d)] - **internal**: add emitExperimentalWarning function (Cody Deckard) [#16497](https://github.com/nodejs/node/pull/16497)
- [[`5d06a4f907`](https://github.com/nodejs/node/commit/5d06a4f907)] - **lib**: replace string concatenation with template (Vijayalakshmi Kannan) [#16923](https://github.com/nodejs/node/pull/16923)
- [[`c3fdef7469`](https://github.com/nodejs/node/commit/c3fdef7469)] - **module**: fix for #17130 shared loader cjs dep (Guy Bedford) [#17131](https://github.com/nodejs/node/pull/17131)
- [[`371e3f1f0b`](https://github.com/nodejs/node/commit/371e3f1f0b)] - **module**: be lazy when creating CJS facades (Bradley Farias) [#17153](https://github.com/nodejs/node/pull/17153)
- [[`b0da03bdf1`](https://github.com/nodejs/node/commit/b0da03bdf1)] - **n-api**: use nullptr instead of NULL in node_api.cc (Daniel Bevenius) [#17276](https://github.com/nodejs/node/pull/17276)
- [[`2e1e166139`](https://github.com/nodejs/node/commit/2e1e166139)] - **path**: remove obsolete comment (Rich Trott) [#17023](https://github.com/nodejs/node/pull/17023)
- [[`6deae6e4c0`](https://github.com/nodejs/node/commit/6deae6e4c0)] - **repl**: remove internal frames from runtime errors (Lance Ball) [#15351](https://github.com/nodejs/node/pull/15351)
- [[`3f2d3b388b`](https://github.com/nodejs/node/commit/3f2d3b388b)] - **src**: remove unused include node_crypto_clienthello (Daniel Bevenius) [#17546](https://github.com/nodejs/node/pull/17546)
- [[`28a81ce102`](https://github.com/nodejs/node/commit/28a81ce102)] - **src**: fix missing handlescope bug in inspector (Ben Noordhuis) [#17539](https://github.com/nodejs/node/pull/17539)
- [[`c78559eb83`](https://github.com/nodejs/node/commit/c78559eb83)] - **src**: node_http2_state.h should not be executable (Jon Moss) [#17408](https://github.com/nodejs/node/pull/17408)
- [[`3c8bdd9652`](https://github.com/nodejs/node/commit/3c8bdd9652)] - **src**: fix typo in NODE_OPTIONS whitelist (Evan Lucas) [#17369](https://github.com/nodejs/node/pull/17369)
- [[`c602c4c7cd`](https://github.com/nodejs/node/commit/c602c4c7cd)] - **src**: make base64.h self-contained (Daniel Bevenius) [#17177](https://github.com/nodejs/node/pull/17177)
- [[`0802128d68`](https://github.com/nodejs/node/commit/0802128d68)] - **src**: add napi_handle_scope_mismatch to msg list (neta) [#17161](https://github.com/nodejs/node/pull/17161)
- [[`76f63b50db`](https://github.com/nodejs/node/commit/76f63b50db)] - **src**: fix compiler warning (cjihrig) [#17195](https://github.com/nodejs/node/pull/17195)
- [[`f26b5761f7`](https://github.com/nodejs/node/commit/f26b5761f7)] - **src**: remove unprofessional slang in assertions (Alexey Orlenko) [#17166](https://github.com/nodejs/node/pull/17166)
- [[`0b07dda767`](https://github.com/nodejs/node/commit/0b07dda767)] - **src**: inspector context name = program title + pid (Ben Noordhuis) [#17087](https://github.com/nodejs/node/pull/17087)
- [[`c5920737a1`](https://github.com/nodejs/node/commit/c5920737a1)] - **src**: abstract getpid() operation (Ben Noordhuis) [#17087](https://github.com/nodejs/node/pull/17087)
- [[`f12efd5990`](https://github.com/nodejs/node/commit/f12efd5990)] - **src**: use unique_ptr for http2_state (Franziska Hinkelmann) [#17078](https://github.com/nodejs/node/pull/17078)
- [[`e46f06c53d`](https://github.com/nodejs/node/commit/e46f06c53d)] - **src**: use std::unique_ptr in base-object-inl.h (Franziska Hinkelmann) [#17079](https://github.com/nodejs/node/pull/17079)
- [[`c9da446533`](https://github.com/nodejs/node/commit/c9da446533)] - **src**: fix size of CounterSet (Witthawat Piwawatthanapanit) [#16984](https://github.com/nodejs/node/pull/16984)
- [[`05422689d7`](https://github.com/nodejs/node/commit/05422689d7)] - **src**: use smart pointer instead of new and delete (Franziska Hinkelmann) [#17020](https://github.com/nodejs/node/pull/17020)
- [[`54706f0531`](https://github.com/nodejs/node/commit/54706f0531)] - **src**: perf_hooks: fix wrong sized delete (Ali Ijaz Sheikh) [#16898](https://github.com/nodejs/node/pull/16898)
- [[`7db8f01ef7`](https://github.com/nodejs/node/commit/7db8f01ef7)] - **src**: implement backtrace-on-abort for windows (Anna Henningsen) [#16951](https://github.com/nodejs/node/pull/16951)
- [[`13a46fcec7`](https://github.com/nodejs/node/commit/13a46fcec7)] - **src**: remove unnecessary call to SetHiddenPrototype (Toon Verwaest) [#16554](https://github.com/nodejs/node/pull/16554)
- [[`9ec35c583f`](https://github.com/nodejs/node/commit/9ec35c583f)] - **src**: clean up uv_fs_t's in module_wrap.cc (cjihrig) [#16722](https://github.com/nodejs/node/pull/16722)
- [[`81970f87ed`](https://github.com/nodejs/node/commit/81970f87ed)] - **src**: fix UB in InternalModuleReadFile() (Ben Noordhuis) [#16871](https://github.com/nodejs/node/pull/16871)
- [[`b1802ede3b`](https://github.com/nodejs/node/commit/b1802ede3b)] - **src**: turn inspector raw pointer into unique_ptr (Franziska Hinkelmann) [#16974](https://github.com/nodejs/node/pull/16974)
- [[`af579907de`](https://github.com/nodejs/node/commit/af579907de)] - **src**: fix bad sizeof expression (Ben Noordhuis) [#17014](https://github.com/nodejs/node/pull/17014)
- [[`cae8772617`](https://github.com/nodejs/node/commit/cae8772617)] - **stream**: use arrow fns for 'this' in readable (Vipin Menon) [#16927](https://github.com/nodejs/node/pull/16927)
- [[`9830b10133`](https://github.com/nodejs/node/commit/9830b10133)] - **test**: remove hidden use of common.PORT in parallel tests (Rich Trott) [#17466](https://github.com/nodejs/node/pull/17466)
- [[`3bf3dc465f`](https://github.com/nodejs/node/commit/3bf3dc465f)] - **test**: remove fixturesDir from common module (Rich Trott) [#17400](https://github.com/nodejs/node/pull/17400)
- [[`0cd4503b76`](https://github.com/nodejs/node/commit/0cd4503b76)] - **test**: remove common.fixturesDir from tests (Rich Trott) [#17400](https://github.com/nodejs/node/pull/17400)
- [[`c02ac841e9`](https://github.com/nodejs/node/commit/c02ac841e9)] - **test**: refactor test-child-process-pass-fd (Rich Trott) [#17596](https://github.com/nodejs/node/pull/17596)
- [[`8eef736f8d`](https://github.com/nodejs/node/commit/8eef736f8d)] - **test**: refactor test-http-default-port (Anna Henningsen) [#17562](https://github.com/nodejs/node/pull/17562)
- [[`bb96831d83`](https://github.com/nodejs/node/commit/bb96831d83)] - **test**: refactored to remove unnecessary variables (Mithun Sasidharan) [#17553](https://github.com/nodejs/node/pull/17553)
- [[`bbb503f90b`](https://github.com/nodejs/node/commit/bbb503f90b)] - **test**: use Countdown in http-agent test (Federico Kauffman) [#17537](https://github.com/nodejs/node/pull/17537)
- [[`831f021e2d`](https://github.com/nodejs/node/commit/831f021e2d)] - **test**: update http test to use common.mustCall (Collins Abitekaniza) [#17528](https://github.com/nodejs/node/pull/17528)
- [[`d5b5278f33`](https://github.com/nodejs/node/commit/d5b5278f33)] - **test**: improve assert messages in repl-reset-event (Adri Van Houdt) [#16836](https://github.com/nodejs/node/pull/16836)
- [[`19aa3b1185`](https://github.com/nodejs/node/commit/19aa3b1185)] - **test**: update test-http-should-keep-alive to use countdown (TomerOmri) [#17505](https://github.com/nodejs/node/pull/17505)
- [[`24305daabe`](https://github.com/nodejs/node/commit/24305daabe)] - **test**: fix flaky test-benchmark-es (Rich Trott) [#17516](https://github.com/nodejs/node/pull/17516)
- [[`e00a4f3b66`](https://github.com/nodejs/node/commit/e00a4f3b66)] - **test**: use Countdown in http test (idandagan1) [#17506](https://github.com/nodejs/node/pull/17506)
- [[`e4edb038db`](https://github.com/nodejs/node/commit/e4edb038db)] - **test**: use Number.isNaN instead of global isNaN (Mithun Sasidharan) [#17515](https://github.com/nodejs/node/pull/17515)
- [[`90ee2b5657`](https://github.com/nodejs/node/commit/90ee2b5657)] - **test**: use Countdown in http-response-statuscode (Mandeep Singh) [#17327](https://github.com/nodejs/node/pull/17327)
- [[`925db27d94`](https://github.com/nodejs/node/commit/925db27d94)] - **test**: use Countdown in test-http-set-cookies (Shilo Mangam) [#17504](https://github.com/nodejs/node/pull/17504)
- [[`56c2f0f434`](https://github.com/nodejs/node/commit/56c2f0f434)] - **test**: Use common.mustCall in http test (sreepurnajasti) [#17487](https://github.com/nodejs/node/pull/17487)
- [[`65b0db4267`](https://github.com/nodejs/node/commit/65b0db4267)] - **test**: update http test to use Countdown (Francisco Gerardo Neri Andriano) [#17477](https://github.com/nodejs/node/pull/17477)
- [[`a81f732fe9`](https://github.com/nodejs/node/commit/a81f732fe9)] - **test**: replace fs.accessSync with fs.existsSync (Leko) [#17446](https://github.com/nodejs/node/pull/17446)
- [[`e442dfbe76`](https://github.com/nodejs/node/commit/e442dfbe76)] - **test**: fix flaky test-benchmark-querystring (Rich Trott) [#17517](https://github.com/nodejs/node/pull/17517)
- [[`f8856ea4f4`](https://github.com/nodejs/node/commit/f8856ea4f4)] - **test**: add common.crashOnUnhandledRejection() (IHsuan) [#17247](https://github.com/nodejs/node/pull/17247)
- [[`4af3a744a3`](https://github.com/nodejs/node/commit/4af3a744a3)] - **test**: refactor code to use common.mustCall (Mithun Sasidharan) [#17437](https://github.com/nodejs/node/pull/17437)
- [[`977eb5aa06`](https://github.com/nodejs/node/commit/977eb5aa06)] - **test**: add more settings to test-benchmark-dgram (Rich Trott) [#17462](https://github.com/nodejs/node/pull/17462)
- [[`6c6635dd2e`](https://github.com/nodejs/node/commit/6c6635dd2e)] - **test**: add dgram benchmark test (jopann) [#17462](https://github.com/nodejs/node/pull/17462)
- [[`d0738fe6e3`](https://github.com/nodejs/node/commit/d0738fe6e3)] - **test**: fix flaky test-benchmark-events (Rich Trott) [#17472](https://github.com/nodejs/node/pull/17472)
- [[`6b18b35b30`](https://github.com/nodejs/node/commit/6b18b35b30)] - **test**: update test-http-request-dont-override-options to use common.mustCall (Mithun Sasidharan) [#17438](https://github.com/nodejs/node/pull/17438)
- [[`e01cd821f5`](https://github.com/nodejs/node/commit/e01cd821f5)] - **test**: replace assert.throws with common.expectsError (Leko) [#17445](https://github.com/nodejs/node/pull/17445)
- [[`daa0cab9c2`](https://github.com/nodejs/node/commit/daa0cab9c2)] - **test**: use common.mustCall in test-http-malformed-request (Mithun Sasidharan) [#17439](https://github.com/nodejs/node/pull/17439)
- [[`26a5d9a66c`](https://github.com/nodejs/node/commit/26a5d9a66c)] - **test**: forbid `common.mustCall*()` in process exit handlers (Rich Trott) [#17453](https://github.com/nodejs/node/pull/17453)
- [[`fddba1840c`](https://github.com/nodejs/node/commit/fddba1840c)] - **test**: use Countdown in http test (Mithun Sasidharan) [#17436](https://github.com/nodejs/node/pull/17436)
- [[`4231923557`](https://github.com/nodejs/node/commit/4231923557)] - **test**: update test-http-response-multiheaders to use countdown (hmammedzadeh) [#17419](https://github.com/nodejs/node/pull/17419)
- [[`45b65857f4`](https://github.com/nodejs/node/commit/45b65857f4)] - **test**: update test-http-timeout to use countdown (Mithun Sasidharan) [#17341](https://github.com/nodejs/node/pull/17341)
- [[`2ab0534322`](https://github.com/nodejs/node/commit/2ab0534322)] - **test**: make common.mustNotCall show file:linenumber (Lance Ball) [#17257](https://github.com/nodejs/node/pull/17257)
- [[`6723619a07`](https://github.com/nodejs/node/commit/6723619a07)] - **test**: update test-http-upgrade-client to use countdown (Mithun Sasidharan) [#17339](https://github.com/nodejs/node/pull/17339)
- [[`8e73a06bbf`](https://github.com/nodejs/node/commit/8e73a06bbf)] - **test**: update test-http-status-reason-invalid-chars to use countdown (Mithun Sasidharan) [#17342](https://github.com/nodejs/node/pull/17342)
- [[`d00df5d6d7`](https://github.com/nodejs/node/commit/d00df5d6d7)] - **test**: refactored test-http-allow-req-after-204-res to countdown (Mithun Sasidharan) [#17211](https://github.com/nodejs/node/pull/17211)
- [[`323ffacfd0`](https://github.com/nodejs/node/commit/323ffacfd0)] - **test**: update test/parallel/test-http-pipe-fs.js to use countdown (ChungNgoops) [#17346](https://github.com/nodejs/node/pull/17346)
- [[`279d844fda`](https://github.com/nodejs/node/commit/279d844fda)] - **test**: refactored test-http-response-splitting to use countdown (Mithun Sasidharan) [#17348](https://github.com/nodejs/node/pull/17348)
- [[`97a264e194`](https://github.com/nodejs/node/commit/97a264e194)] - **test**: make CreateParams stack-allocated (Daniel Bevenius) [#17366](https://github.com/nodejs/node/pull/17366)
- [[`a71f214282`](https://github.com/nodejs/node/commit/a71f214282)] - **test**: use v8 Default Allocator in cctest fixture (Daniel Bevenius) [#17366](https://github.com/nodejs/node/pull/17366)
- [[`f5cd808212`](https://github.com/nodejs/node/commit/f5cd808212)] - **test**: replace function with arrow function (Leko) [#17345](https://github.com/nodejs/node/pull/17345)
- [[`683daea419`](https://github.com/nodejs/node/commit/683daea419)] - **test**: fix flaky async-hooks/test-graph.signal (Rich Trott) [#17509](https://github.com/nodejs/node/pull/17509)
- [[`0498649c25`](https://github.com/nodejs/node/commit/0498649c25)] - **test**: remove common.tmpDirName (Rich Trott) [#17266](https://github.com/nodejs/node/pull/17266)
- [[`ef7261006e`](https://github.com/nodejs/node/commit/ef7261006e)] - **test**: replace function with ES6 arrow function (Junichi Kajiwara) [#17306](https://github.com/nodejs/node/pull/17306)
- [[`ecb1f17c1e`](https://github.com/nodejs/node/commit/ecb1f17c1e)] - **test**: add es6 module global leakage tests (WhoMeNope) [#16341](https://github.com/nodejs/node/pull/16341)
- [[`86d00b2af8`](https://github.com/nodejs/node/commit/86d00b2af8)] - **test**: Enable specifying flaky tests on fips (Nikhil Komawar) [#16329](https://github.com/nodejs/node/pull/16329)
- [[`d5795d016b`](https://github.com/nodejs/node/commit/d5795d016b)] - **test**: refactored http test to use countdown (Mithun Sasidharan) [#17241](https://github.com/nodejs/node/pull/17241)
- [[`74d5e6bcb1`](https://github.com/nodejs/node/commit/74d5e6bcb1)] - **test**: Update test-http-parser-free to use countdown timer (Mandeep Singh) [#17322](https://github.com/nodejs/node/pull/17322)
- [[`6137afad8c`](https://github.com/nodejs/node/commit/6137afad8c)] - **test**: Update test-http-client-agent to use countdown timer (Mandeep Singh) [#17325](https://github.com/nodejs/node/pull/17325)
- [[`341f0c5be7`](https://github.com/nodejs/node/commit/341f0c5be7)] - **test**: fix flaky parallel/test-http2-client-upload (Anna Henningsen) [#17361](https://github.com/nodejs/node/pull/17361)
- [[`c1efba8737`](https://github.com/nodejs/node/commit/c1efba8737)] - **test**: fix isNAN-\>Number.isNAN (yuza yuko) [#17309](https://github.com/nodejs/node/pull/17309)
- [[`7f4aafc46d`](https://github.com/nodejs/node/commit/7f4aafc46d)] - **test**: make use of Number.isNaN to test-readfloat.js (Hiromu Yoshiwara) [#17310](https://github.com/nodejs/node/pull/17310)
- [[`8b64df06d2`](https://github.com/nodejs/node/commit/8b64df06d2)] - **test**: replace function with arrow function (spring_raining) [#17312](https://github.com/nodejs/node/pull/17312)
- [[`6ed8c28315`](https://github.com/nodejs/node/commit/6ed8c28315)] - **test**: replace function with arrow function (Hiroaki KARASAWA) [#17308](https://github.com/nodejs/node/pull/17308)
- [[`8874473cff`](https://github.com/nodejs/node/commit/8874473cff)] - **test**: replace function with arrow function (kou-hin) [#17305](https://github.com/nodejs/node/pull/17305)
- [[`eccb5cb34f`](https://github.com/nodejs/node/commit/eccb5cb34f)] - **test**: use arrow function (koooge) [#17318](https://github.com/nodejs/node/pull/17318)
- [[`fa397148b3`](https://github.com/nodejs/node/commit/fa397148b3)] - **test**: use Number.isNaN() (MURAKAMI Masahiko) [#17319](https://github.com/nodejs/node/pull/17319)
- [[`0aca036e91`](https://github.com/nodejs/node/commit/0aca036e91)] - **test**: add test of stream Transform (Yoshiya Hinosawa) [#17303](https://github.com/nodejs/node/pull/17303)
- [[`39107d7a79`](https://github.com/nodejs/node/commit/39107d7a79)] - **test**: use common.crashOnUnhandledRejection (yozian) [#17242](https://github.com/nodejs/node/pull/17242)
- [[`2d697a8068`](https://github.com/nodejs/node/commit/2d697a8068)] - **test**: use common.crashOnUnhandledRejection (Kcin1993) [#17235](https://github.com/nodejs/node/pull/17235)
- [[`0fb5d27496`](https://github.com/nodejs/node/commit/0fb5d27496)] - **test**: add common.crashOnUnhandledRejection() (Andy Chen) [#17234](https://github.com/nodejs/node/pull/17234)
- [[`e07c200d24`](https://github.com/nodejs/node/commit/e07c200d24)] - **test**: use common.crashOnUnhandledRejection (zhengyuanjie) [#17215](https://github.com/nodejs/node/pull/17215)
- [[`3255686722`](https://github.com/nodejs/node/commit/3255686722)] - **test**: use common.crashOnUnhandledRejection (Jason Chung) [#17233](https://github.com/nodejs/node/pull/17233)
- [[`bea920068f`](https://github.com/nodejs/node/commit/bea920068f)] - **test**: use common.crashOnUnhandledRejection() (<EMAIL>) [#17232](https://github.com/nodejs/node/pull/17232)
- [[`ef6d68c2f9`](https://github.com/nodejs/node/commit/ef6d68c2f9)] - **test**: use common.crashOnUnhandledRejection (Kurt Hsu) [#17229](https://github.com/nodejs/node/pull/17229)
- [[`2b5bc37bca`](https://github.com/nodejs/node/commit/2b5bc37bca)] - **test**: add common.crashOnHandleRejection (jackyen) [#17225](https://github.com/nodejs/node/pull/17225)
- [[`0daf33e5e7`](https://github.com/nodejs/node/commit/0daf33e5e7)] - **test**: add crashonUnhandledRejection (danielLin) [#17237](https://github.com/nodejs/node/pull/17237)
- [[`f4e6d06e29`](https://github.com/nodejs/node/commit/f4e6d06e29)] - **test**: keep coverage reports after coverage-clean (Anatoli Papirovski) [#15470](https://github.com/nodejs/node/pull/15470)
- [[`b4726fabb6`](https://github.com/nodejs/node/commit/b4726fabb6)] - **test**: add test on unhandled rejection (Larry Lu) [#17228](https://github.com/nodejs/node/pull/17228)
- [[`040fe7025a`](https://github.com/nodejs/node/commit/040fe7025a)] - **test**: use common.crashOnUnhandledRejection (aryung chen) [#17221](https://github.com/nodejs/node/pull/17221)
- [[`22d352540e`](https://github.com/nodejs/node/commit/22d352540e)] - **test**: use common.crashOnUnhandledRejection (Zack Yang) [#17217](https://github.com/nodejs/node/pull/17217)
- [[`1474460d2e`](https://github.com/nodejs/node/commit/1474460d2e)] - **test**: add common.crashOnUnhandledRejection() (Scya597) [#17212](https://github.com/nodejs/node/pull/17212)
- [[`7900f8ae63`](https://github.com/nodejs/node/commit/7900f8ae63)] - **test**: remove unlink function which is needless (buji) [#17119](https://github.com/nodejs/node/pull/17119)
- [[`179a4f4fa4`](https://github.com/nodejs/node/commit/179a4f4fa4)] - **test**: dont need to remove nonexistent directory (buji) [#17119](https://github.com/nodejs/node/pull/17119)
- [[`12940b1bed`](https://github.com/nodejs/node/commit/12940b1bed)] - **test**: use common.crashOnUnhandledRejection() (Ivan Wei) [#17227](https://github.com/nodejs/node/pull/17227)
- [[`f56e0ca30d`](https://github.com/nodejs/node/commit/f56e0ca30d)] - **test**: add common.crashOnUnhandledRejection() (Kyle Yu) [#17236](https://github.com/nodejs/node/pull/17236)
- [[`92d4d1e523`](https://github.com/nodejs/node/commit/92d4d1e523)] - **test**: use crashOnUnhandledRejection (YuLun Shih) [#17220](https://github.com/nodejs/node/pull/17220)
- [[`90630ae091`](https://github.com/nodejs/node/commit/90630ae091)] - **test**: fix linting error (James M Snell) [#17251](https://github.com/nodejs/node/pull/17251)
- [[`0e4a681e98`](https://github.com/nodejs/node/commit/0e4a681e98)] - **test**: use common.crashOnUnhandledRejection (jimliu7434) [#17231](https://github.com/nodejs/node/pull/17231)
- [[`8a0a2658cc`](https://github.com/nodejs/node/commit/8a0a2658cc)] - **test**: use crashOnUnhandledRejection (Roth Peng) [#17226](https://github.com/nodejs/node/pull/17226)
- [[`5dd620babd`](https://github.com/nodejs/node/commit/5dd620babd)] - **test**: use common.crashOnUnhandledRejection (esbb48) [#17218](https://github.com/nodejs/node/pull/17218)
- [[`74e6f0d08f`](https://github.com/nodejs/node/commit/74e6f0d08f)] - **test**: use arrow function instead of bind (Lance Ball) [#17202](https://github.com/nodejs/node/pull/17202)
- [[`b1b98210a6`](https://github.com/nodejs/node/commit/b1b98210a6)] - **test**: use crashOnUnhandledRejection (Chiahao Lin) [#17219](https://github.com/nodejs/node/pull/17219)
- [[`f608f708c4`](https://github.com/nodejs/node/commit/f608f708c4)] - **test**: use common.crashOnUnhandledRejection (Whien) [#17214](https://github.com/nodejs/node/pull/17214)
- [[`bd8d7d7258`](https://github.com/nodejs/node/commit/bd8d7d7258)] - **test**: clean up inappropriate language (Gus Caplan) [#17170](https://github.com/nodejs/node/pull/17170)
- [[`27ee7117dd`](https://github.com/nodejs/node/commit/27ee7117dd)] - **test**: wrap callback in common.mustCall (suman-mitra) [#17173](https://github.com/nodejs/node/pull/17173)
- [[`7b9623a819`](https://github.com/nodejs/node/commit/7b9623a819)] - **test**: remove unused parameter in test-next-tick-error-spin.js (Francois KY) [#17185](https://github.com/nodejs/node/pull/17185)
- [[`1f20891445`](https://github.com/nodejs/node/commit/1f20891445)] - **test**: remove unused parameter (Fran Herrero) [#17193](https://github.com/nodejs/node/pull/17193)
- [[`cebedd6845`](https://github.com/nodejs/node/commit/cebedd6845)] - **test**: remove unused variable (Pierre-Loic Doulcet) [#17186](https://github.com/nodejs/node/pull/17186)
- [[`9c6b845d43`](https://github.com/nodejs/node/commit/9c6b845d43)] - **test**: remove unused variable (Guillaume Flandre) [#17187](https://github.com/nodejs/node/pull/17187)
- [[`71d554e8cb`](https://github.com/nodejs/node/commit/71d554e8cb)] - **test**: remove unused parameter (François Descamps) [#17184](https://github.com/nodejs/node/pull/17184)
- [[`431ed2be5d`](https://github.com/nodejs/node/commit/431ed2be5d)] - **test**: remove unused parameter (Xavier Balloy) [#17188](https://github.com/nodejs/node/pull/17188)
- [[`3d1e129dbc`](https://github.com/nodejs/node/commit/3d1e129dbc)] - **test**: make debugging of inspector-port-zero easier (Gibson Fahnestock) [#16685](https://github.com/nodejs/node/pull/16685)
- [[`c86f185b49`](https://github.com/nodejs/node/commit/c86f185b49)] - **test**: replace assert.throws w/ common.expectsError (sgreylyn) [#17091](https://github.com/nodejs/node/pull/17091)
- [[`ca0b5fa68b`](https://github.com/nodejs/node/commit/ca0b5fa68b)] - **test**: reduce benchmark cases in test-benchmark-buffer (Rich Trott) [#17111](https://github.com/nodejs/node/pull/17111)
- [[`2f276c894e`](https://github.com/nodejs/node/commit/2f276c894e)] - **test**: fs.write() if 3rd argument is a callback, not offset (Patrick Heneise) [#17045](https://github.com/nodejs/node/pull/17045)
- [[`ec28fe68ad`](https://github.com/nodejs/node/commit/ec28fe68ad)] - **test**: utilize common.mustCall() on child exit (sreepurnajasti) [#16996](https://github.com/nodejs/node/pull/16996)
- [[`e3d0e0360e`](https://github.com/nodejs/node/commit/e3d0e0360e)] - **test**: use arrow functions instead of bind (Tobias Nießen) [#17070](https://github.com/nodejs/node/pull/17070)
- [[`f44ad16298`](https://github.com/nodejs/node/commit/f44ad16298)] - **test**: move timing-sensitive test to sequential (Rich Trott) [#16775](https://github.com/nodejs/node/pull/16775)
- [[`af4d74e6d0`](https://github.com/nodejs/node/commit/af4d74e6d0)] - **test**: make REPL test pass in coverage mode (Anna Henningsen) [#17082](https://github.com/nodejs/node/pull/17082)
- [[`b1bcd4fe11`](https://github.com/nodejs/node/commit/b1bcd4fe11)] - **test**: --enable-static linked executable (Daniel Bevenius) [#14986](https://github.com/nodejs/node/pull/14986)
- [[`621390fe65`](https://github.com/nodejs/node/commit/621390fe65)] - **test**: add basic WebAssembly test (Steve Kinney) [#16760](https://github.com/nodejs/node/pull/16760)
- [[`8528a5d5f0`](https://github.com/nodejs/node/commit/8528a5d5f0)] - **test**: flag known flake (Refael Ackermann) [#16941](https://github.com/nodejs/node/pull/16941)
- [[`3da14495c7`](https://github.com/nodejs/node/commit/3da14495c7)] - **test**: refactor exitedAfterDisconnect test (Rich Trott) [#16729](https://github.com/nodejs/node/pull/16729)
- [[`5a1a56722b`](https://github.com/nodejs/node/commit/5a1a56722b)] - **test**: add tests for eslint rules (Teddy Katz) [#16138](https://github.com/nodejs/node/pull/16138)
- [[`927a28126b`](https://github.com/nodejs/node/commit/927a28126b)] - **test**: fixup test-http2-create-client-secure-session (James M Snell) [#17328](https://github.com/nodejs/node/pull/17328)
- [[`eb8344b3a9`](https://github.com/nodejs/node/commit/eb8344b3a9)] - **test**: use default assertion messages (John Byrne) [#16808](https://github.com/nodejs/node/pull/16808)
- [[`d2b32ce074`](https://github.com/nodejs/node/commit/d2b32ce074)] - **test**: add detailed message for assertion failure (Attila Gonda) [#16812](https://github.com/nodejs/node/pull/16812)
- [[`55bf57dc9a`](https://github.com/nodejs/node/commit/55bf57dc9a)] - **test**: improve assert messages in napi exception test (Paul Blanche) [#16820](https://github.com/nodejs/node/pull/16820)
- [[`25ff7bf1cd`](https://github.com/nodejs/node/commit/25ff7bf1cd)] - **test**: improve error emssage reporting in testNapiRun.js (Paul Ashfield) [#16821](https://github.com/nodejs/node/pull/16821)
- [[`2d63b65aed`](https://github.com/nodejs/node/commit/2d63b65aed)] - **test**: refactor addons-napi/test_promise/test.js (ka3e) [#16814](https://github.com/nodejs/node/pull/16814)
- [[`ad331f20bd`](https://github.com/nodejs/node/commit/ad331f20bd)] - **test**: add coverage to tty module (cjihrig) [#16959](https://github.com/nodejs/node/pull/16959)
- [[`9bade7a095`](https://github.com/nodejs/node/commit/9bade7a095)] - **test,doc**: do not indicate that non-functions "return" values (Rich Trott) [#17267](https://github.com/nodejs/node/pull/17267)
- [[`7e571ae204`](https://github.com/nodejs/node/commit/7e571ae204)] - **test,doc**: document where common modules go (Gibson Fahnestock) [#16089](https://github.com/nodejs/node/pull/16089)
- [[`eb3db343c3`](https://github.com/nodejs/node/commit/eb3db343c3)] - **tools**: fix gitignore for tools/doc/ (Richard Littauer) [#17224](https://github.com/nodejs/node/pull/17224)
- [[`0e47f4151e`](https://github.com/nodejs/node/commit/0e47f4151e)] - **tools**: simplify no-let-in-for-declaration rule (cjihrig) [#17572](https://github.com/nodejs/node/pull/17572)
- [[`dd1105ddba`](https://github.com/nodejs/node/commit/dd1105ddba)] - **tools**: simplify buffer-constructor rule (cjihrig) [#17572](https://github.com/nodejs/node/pull/17572)
- [[`20044a239d`](https://github.com/nodejs/node/commit/20044a239d)] - **tools**: simplify prefer-assert-methods rule (cjihrig) [#17572](https://github.com/nodejs/node/pull/17572)
- [[`9ece12f542`](https://github.com/nodejs/node/commit/9ece12f542)] - **tools**: simplify prefer-common-mustnotcall rule (cjihrig) [#17572](https://github.com/nodejs/node/pull/17572)
- [[`3e4ca90077`](https://github.com/nodejs/node/commit/3e4ca90077)] - **tools**: replace space with \b in regex (Diego Rodríguez Baquero) [#17479](https://github.com/nodejs/node/pull/17479)
- [[`36c977e551`](https://github.com/nodejs/node/commit/36c977e551)] - **tools**: enable no-return-await lint rule (Rich Trott) [#17265](https://github.com/nodejs/node/pull/17265)
- [[`aa546e7365`](https://github.com/nodejs/node/commit/aa546e7365)] - **tools**: add Boxstarter script (Bartosz Sosnowski) [#17046](https://github.com/nodejs/node/pull/17046)
- [[`3fb2183464`](https://github.com/nodejs/node/commit/3fb2183464)] - **tools**: update to ESLint 4.12.0 (cjihrig) [#16948](https://github.com/nodejs/node/pull/16948)
- [[`b6be2d7181`](https://github.com/nodejs/node/commit/b6be2d7181)] - **tools**: add lint fixer for `require-buffer` (Bamieh) [#17144](https://github.com/nodejs/node/pull/17144)
- [[`baf95b68e5`](https://github.com/nodejs/node/commit/baf95b68e5)] - **tools**: make doc tool a bit more readable (Tobias Nießen) [#17125](https://github.com/nodejs/node/pull/17125)
- [[`9a76a6cfcf`](https://github.com/nodejs/node/commit/9a76a6cfcf)] - **tools**: remove useless function declaration (Tobias Nießen) [#17125](https://github.com/nodejs/node/pull/17125)
- [[`df39389175`](https://github.com/nodejs/node/commit/df39389175)] - **tools**: avoid using process.cwd in tools/lint-js (Tobias Nießen) [#17121](https://github.com/nodejs/node/pull/17121)
- [[`5e5cb1bd54`](https://github.com/nodejs/node/commit/5e5cb1bd54)] - **tools**: use built-in padStart instead of padString (Tobias Nießen) [#17120](https://github.com/nodejs/node/pull/17120)
- [[`7658eec62e`](https://github.com/nodejs/node/commit/7658eec62e)] - **tools**: allow running test.py without configuring (Gibson Fahnestock) [#16621](https://github.com/nodejs/node/pull/16621)
- [[`d7eaa3ffb4`](https://github.com/nodejs/node/commit/d7eaa3ffb4)] - **tools**: fail tests if malformed status file (Rich Trott) [#16703](https://github.com/nodejs/node/pull/16703)
- [[`8319b68873`](https://github.com/nodejs/node/commit/8319b68873)] - **tools**: try installing js-yaml only once (Joyee Cheung) [#16661](https://github.com/nodejs/node/pull/16661)
- [[`a91fb54693`](https://github.com/nodejs/node/commit/a91fb54693)] - **tools**: add fixer for no-let-in-for-declaration (Weijia Wang) [#16642](https://github.com/nodejs/node/pull/16642)
- [[`ba73a67d45`](https://github.com/nodejs/node/commit/ba73a67d45)] - **tools**: update to ESLint 4.10.0 (cjihrig) [#16738](https://github.com/nodejs/node/pull/16738)
- [[`38b0da7fab`](https://github.com/nodejs/node/commit/38b0da7fab)] - **tools,test**: use Execute instead of check_output (Refael Ackermann) [#17381](https://github.com/nodejs/node/pull/17381)
- [[`6fa51d6198`](https://github.com/nodejs/node/commit/6fa51d6198)] - **tty**: fix 'resize' event regression (Ben Noordhuis) [#16225](https://github.com/nodejs/node/pull/16225)
- [[`5ee05f2187`](https://github.com/nodejs/node/commit/5ee05f2187)] - **tty**: refactor exports (cjihrig) [#16959](https://github.com/nodejs/node/pull/16959)
- [[`3236944761`](https://github.com/nodejs/node/commit/3236944761)] - **util**: fix negative 0 check in inspect (Gus Caplan) [#17507](https://github.com/nodejs/node/pull/17507)
- [[`943258e093`](https://github.com/nodejs/node/commit/943258e093)] - **util**: remove check for global.process (Gus Caplan) [#17435](https://github.com/nodejs/node/pull/17435)

Windows 32-bit Installer: https://nodejs.org/dist/v8.9.4/node-v8.9.4-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v8.9.4/node-v8.9.4-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v8.9.4/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v8.9.4/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v8.9.4/node-v8.9.4.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v8.9.4/node-v8.9.4-darwin-x64.tar.gz \
Linux 32-bit Binary: https://nodejs.org/dist/v8.9.4/node-v8.9.4-linux-x86.tar.xz \
Linux 64-bit Binary: https://nodejs.org/dist/v8.9.4/node-v8.9.4-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v8.9.4/node-v8.9.4-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v8.9.4/node-v8.9.4-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v8.9.4/node-v8.9.4-aix-ppc64.tar.gz \
SmartOS 32-bit Binary: https://nodejs.org/dist/v8.9.4/node-v8.9.4-sunos-x86.tar.xz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v8.9.4/node-v8.9.4-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v8.9.4/node-v8.9.4-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v8.9.4/node-v8.9.4-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v8.9.4/node-v8.9.4-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v8.9.4/node-v8.9.4.tar.gz \
Other release files: https://nodejs.org/dist/v8.9.4/ \
Documentation: https://nodejs.org/docs/v8.9.4/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

155ae63f0bb47050e0c31b4f8c17dadc79dcfa8e8f4ec9e3974fd7592afa9a4f  node-v8.9.4-aix-ppc64.tar.gz
ca50f7d2035eb805306e303b644bb1cde170ce2615e0a2c6e95fb80881c48c24  node-v8.9.4-darwin-x64.tar.gz
cb79e2da37d2b646a06adaddcda67ff6ba0f77f9ca733b041dabf3dad79c7468  node-v8.9.4-darwin-x64.tar.xz
ef7248e81706daeeec946c19808a50b60ac250e648365d78fda6e40f1f9b23a5  node-v8.9.4-headers.tar.gz
11ed407a4bc3d8c3e73305ac54e91e64c9a9f6a2ae5476791d6fcc14ac159bfc  node-v8.9.4-headers.tar.xz
2b133c7d23033fbc2419e66fc08bba35c427a97aba83ed6848b6b4678c0cac65  node-v8.9.4-linux-arm64.tar.gz
7c0369a5dbc98d0989c208ca3ee1b6db4cba576343014fdbf7d36fd2659f7089  node-v8.9.4-linux-arm64.tar.xz
81f138e935323246bd5da518eb0ea8ad00008f3c8a8d606e17589a545a9c73d1  node-v8.9.4-linux-armv6l.tar.gz
501bcae62ea1769924facc9628f407d37753e7a024cf3b12a18ea9dab1b380c9  node-v8.9.4-linux-armv6l.tar.xz
a0dd9009cb8d4be89c8a31131df16ad5ea1580d10ae426c5142aa34b0ad4ea76  node-v8.9.4-linux-armv7l.tar.gz
fe19f195df3d4f362d0cf0eef43c1a6a0b6006a1be2a89ee1808091c2ef4d722  node-v8.9.4-linux-armv7l.tar.xz
c5df73b8571edf97f83b484d6139332fad3b710d51be4aeb8d846059862d4675  node-v8.9.4-linux-ppc64le.tar.gz
21178be5e4c1dbdd99610d24aa934234a368c542ebabb3d98c31d393cf4adf06  node-v8.9.4-linux-ppc64le.tar.xz
d6e53ab2f8364528d4c6800adc1e7fccec607fd07a97b83985732c749a7fc846  node-v8.9.4-linux-s390x.tar.gz
90c6c284db9482a478dd5110e2171435156d56a013aeda2f636b6240eba156bd  node-v8.9.4-linux-s390x.tar.xz
21fb4690e349f82d708ae766def01d7fec1b085ce1f5ab30d9bda8ee126ca8fc  node-v8.9.4-linux-x64.tar.gz
68b94aac38cd5d87ab79c5b38306e34a20575f31a3ea788d117c20fffcca3370  node-v8.9.4-linux-x64.tar.xz
cc2f7a300353422ede336f5e72b71f0d6eac46732a31b7640648378830dd7513  node-v8.9.4-linux-x86.tar.gz
79f241f31eab5dfe2976fb0633c598dababd207ab0b8a163004f296cd7794a65  node-v8.9.4-linux-x86.tar.xz
b93767f7e186b1ae7204fedafa4110534f577d18d4204f422b626afdd5061e28  node-v8.9.4.pkg
e4a5d945091043c937125cd0d515258785cd4ea806fe3b77000d888de23d2ba0  node-v8.9.4-sunos-x64.tar.gz
b33e8f1495b88fcc0ab1e2579f2f7cf4d39886d577430dcb920a024829d4cf28  node-v8.9.4-sunos-x64.tar.xz
551729411793e427f5760fe8e46f45612e1e8e7c63e55ad34243ebf8ea9a4a7a  node-v8.9.4-sunos-x86.tar.gz
6b439bb7204362c0af7a654bce24fcf8059e1772b2f0a9e4e1f8a0b8caa85d26  node-v8.9.4-sunos-x86.tar.xz
729b44b32b2f82ecd5befac4f7518de0c4e3add34e8fe878f745740a66cbbc01  node-v8.9.4.tar.gz
6cdcde9c9c1ca9f450a0b24eafa229ca759e576daa0fae892ce74d541ecdc86f  node-v8.9.4.tar.xz
15a847a28358f9ae40bae42f49b033b0180bc10661632c63a9c8487ae980a8ba  node-v8.9.4-win-x64.7z
48946e99ac4484e071df25741d2300f3a656f476c5ff3f8116a4746c07ebe3b7  node-v8.9.4-win-x64.zip
50ad674fb4c89edf35d3fee2136da86631cb7c0504589eb71ce8a3bb176493ed  node-v8.9.4-win-x86.7z
02e3c65000ac055e05c604aec4cf318212efbd4b60a945ed319072d58314ca32  node-v8.9.4-win-x86.zip
547689da69bacadfee619d208702b73698d14297bd5fef5d80656897989e91b6  node-v8.9.4-x64.msi
f9442188c2f66d167a0ac610dee6d16e226ba28ca93f9569e0276268eb8f85dc  node-v8.9.4-x86.msi
b73841f25d6e75d635770fd1a32e4d74d6ab2feed0fd7708bb40b967ae06f33e  win-x64/node.exe
5439dc6f0d632ecdeb7342986743a03fe0818e34f0a67e38de74fa9c94886a39  win-x64/node.lib
6ab35445dd564978019cf4f3cfe11dd342b8450015fc054df99aa6f35f21736a  win-x64/node_pdb.7z
c064abba981c2373e7e1a8c53b4e4ed1d4927bd9c0f7c065b24dd13b731598bd  win-x64/node_pdb.zip
c8430b20cd067d8784d5faae04f9447987a472b22b6d0a2403ea4362ecd3d0bc  win-x86/node.exe
c4edece2c0aa68e816c4e067f397eb12e9d0c81bb37b3d349dbaf47cf246b0b7  win-x86/node.lib
6a2ee7a0b0074ece27d171418d82ce25a60b87750ec30c5c9fbeaaca8c206fa5  win-x86/node_pdb.7z
1b44176d888c1bc6a6b05fcc6234031b3b8a58da9de8b99661088f998ac5e269  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEd5hKmG68KqeGvA9msB+7koIcWHoFAlpMW20ACgkQsB+7koIc
WHpz/xAAke4OR9kgp2EiKwMYZpgvv0vGtpFnCHhi20eN6axjMP3DU5QEUisAniyf
VSdWmBI903thvIS8+IWXUWItfOdNLu6cz23GFXhkl+AT10JJsEU6JFpuaQa4niEP
Pao4i+RruFxLkTYThPksPNPKRKM7Ehd1wqBY5RtHRqtKroklVU3HKG5aLNDRBykO
dFmXec6oWbNRb5gg5HK7amZoFAp1K4hhlIxrgnU7t4A9cWS3Q0736wt4SctP0fyM
lcZsZgKrp1IaxoCE/Dtk2o7ZfqmWA7k40GoNdb/0iSBGFZEQn820Byslivi+04uA
KvA+fNJuM+bdx/oPQ+k0gYqcBdrx7VoKHKY6XhMT7Daolcwr0lKCYn81vFSbANP8
ovLFFW5T1dAC/qKv0jit+Jhoeu3jB5nYZOQUiLgs5L+gTaDJs4iF02uahpMHEeLR
jmoIsZ+ZfvFrs6Jb/St/gGX+woFOVjenoQBVokm6PnzYBAktsm5x3xvUz2A35K9b
cMn535Jbr8qHcm9TVqMG+POoMCdnv0M2QKTGCRhfrK7F39XIxBnCYz/RSNEdixCJ
agWVWzG9GlgCl/wj5blG1tnS6o5x5udCQIOQBeTyVCCXpVSZcUdsZKe+gCciIH0e
mBDz0UQ/dayTE5JFy8KoPURA0Tb6bj7z0Msyjmjl5rcm6iV3Dwk=
=hRYy
-----END PGP SIGNATURE-----

```
