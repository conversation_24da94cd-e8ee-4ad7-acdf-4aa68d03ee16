---
date: '2018-03-06T20:04:04.973Z'
category: release
title: Node v6.13.1 (LTS)
layout: blog-post
author: <PERSON><PERSON>
---

### Notable Changes

- **http, tls**:
  - better support for IPv6 addresses (<PERSON><PERSON>) [#14772](https://github.com/nodejs/node/pull/14772)

### Commits

- [[`d333ba5e2a`](https://github.com/nodejs/node/commit/d333ba5e2a)] - **doc**: add v<PERSON><PERSON><PERSON><PERSON> as collaborator (v<PERSON><PERSON><PERSON><PERSON>) [#18432](https://github.com/nodejs/node/pull/18432)
- [[`7fc5c69a4a`](https://github.com/nodejs/node/commit/7fc5c69a4a)] - **doc**: use PBKDF2 in text (<PERSON>) [#18279](https://github.com/nodejs/node/pull/18279)
- [[`1e8d1200ce`](https://github.com/nodejs/node/commit/1e8d1200ce)] - **doc**: Add example of null to assert.ifError (Leko) [#18236](https://github.com/nodejs/node/pull/18236)
- [[`46e43111af`](https://github.com/nodejs/node/commit/46e43111af)] - **doc**: V8 branch used in 8.x not active anymore (Franziska Hinkelmann) [#18155](https://github.com/nodejs/node/pull/18155)
- [[`b83b104c17`](https://github.com/nodejs/node/commit/b83b104c17)] - **doc**: add builtin module in building.md (Suixinlei) [#17705](https://github.com/nodejs/node/pull/17705)
- [[`2e76df5b4e`](https://github.com/nodejs/node/commit/2e76df5b4e)] - **doc**: warn users about non-ASCII paths on build (Matheus Marchini) [#16735](https://github.com/nodejs/node/pull/16735)
- [[`2c21421092`](https://github.com/nodejs/node/commit/2c21421092)] - **doc**: simplify sentences that use "considered" (Rich Trott) [#18095](https://github.com/nodejs/node/pull/18095)
- [[`8f9362d6e8`](https://github.com/nodejs/node/commit/8f9362d6e8)] - **doc**: add documentation for deprecation properties (Jon Moss) [#16539](https://github.com/nodejs/node/pull/16539)
- [[`1505b71dab`](https://github.com/nodejs/node/commit/1505b71dab)] - **doc**: add Leko to collaborators (Leko) [#18117](https://github.com/nodejs/node/pull/18117)
- [[`838f7bdb6e`](https://github.com/nodejs/node/commit/838f7bdb6e)] - **doc**: be less tentative about undefined behavior (Rich Trott) [#18091](https://github.com/nodejs/node/pull/18091)
- [[`17c88c4c18`](https://github.com/nodejs/node/commit/17c88c4c18)] - **doc**: examples for fast-tracking regression fixes (Refael Ackermann) [#17379](https://github.com/nodejs/node/pull/17379)
- [[`e021fb73d2`](https://github.com/nodejs/node/commit/e021fb73d2)] - **doc,test**: mention Duplex support for TLS (Anna Henningsen) [#17599](https://github.com/nodejs/node/pull/17599)
- [[`df038ad90f`](https://github.com/nodejs/node/commit/df038ad90f)] - **fs**: fix options.end of fs.ReadStream() (陈刚) [#18121](https://github.com/nodejs/node/pull/18121)
- [[`8e7ac25aa6`](https://github.com/nodejs/node/commit/8e7ac25aa6)] - **http, tls**: better support for IPv6 addresses (Mattias Holmlund) [#14772](https://github.com/nodejs/node/pull/14772)
- [[`969c39eb3a`](https://github.com/nodejs/node/commit/969c39eb3a)] - **lib**: enable dot-notation eslint rule (Anatoli Papirovski) [#18007](https://github.com/nodejs/node/pull/18007)
- [[`37071b8dda`](https://github.com/nodejs/node/commit/37071b8dda)] - **path**: fix path.normalize for relative paths (Weijia Wang) [#17974](https://github.com/nodejs/node/pull/17974)
- [[`fdf73b110f`](https://github.com/nodejs/node/commit/fdf73b110f)] - **test**: preserve env in test cases (Beth Griggs) [#14822](https://github.com/nodejs/node/pull/14822)
- [[`bb2d292562`](https://github.com/nodejs/node/commit/bb2d292562)] - **test**: change assert message to default (ryanmahan) [#18259](https://github.com/nodejs/node/pull/18259)
- [[`27107b957c`](https://github.com/nodejs/node/commit/27107b957c)] - **test**: use countdown timer (Mandeep Singh) [#17326](https://github.com/nodejs/node/pull/17326)
- [[`eaa30e4947`](https://github.com/nodejs/node/commit/eaa30e4947)] - **test**: simplify loadDHParam in TLS test (Tobias Nießen) [#18103](https://github.com/nodejs/node/pull/18103)
- [[`2004efded8`](https://github.com/nodejs/node/commit/2004efded8)] - **test**: improve to use template string (sreepurnajasti) [#18097](https://github.com/nodejs/node/pull/18097)
- [[`16ef24bccf`](https://github.com/nodejs/node/commit/16ef24bccf)] - **test**: use smaller input file for test-zlib.js (Rich Trott) [#17988](https://github.com/nodejs/node/pull/17988)
- [[`48790382f1`](https://github.com/nodejs/node/commit/48790382f1)] - **tools**: add number-isnan rule (Jon Moss) [#17556](https://github.com/nodejs/node/pull/17556)

Windows 32-bit Installer: https://nodejs.org/dist/v6.13.1/node-v6.13.1-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v6.13.1/node-v6.13.1-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v6.13.1/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v6.13.1/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v6.13.1/node-v6.13.1.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v6.13.1/node-v6.13.1-darwin-x64.tar.gz \
Linux 32-bit Binary: https://nodejs.org/dist/v6.13.1/node-v6.13.1-linux-x86.tar.xz \
Linux 64-bit Binary: https://nodejs.org/dist/v6.13.1/node-v6.13.1-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v6.13.1/node-v6.13.1-linux-ppc64le.tar.xz \
Linux PPC BE 64-bit Binary: https://nodejs.org/dist/v6.13.1/node-v6.13.1-linux-ppc64.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v6.13.1/node-v6.13.1-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v6.13.1/node-v6.13.1-aix-ppc64.tar.gz \
SmartOS 32-bit Binary: https://nodejs.org/dist/v6.13.1/node-v6.13.1-sunos-x86.tar.xz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v6.13.1/node-v6.13.1-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v6.13.1/node-v6.13.1-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v6.13.1/node-v6.13.1-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v6.13.1/node-v6.13.1-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v6.13.1/node-v6.13.1.tar.gz \
Other release files: https://nodejs.org/dist/v6.13.1/ \
Documentation: https://nodejs.org/docs/v6.13.1/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

202fccebc6629de680c13ce3ec69c8ba91ddaa1eace00fa18f9d9b10775442c2  node-v6.13.1-aix-ppc64.tar.gz
6a34a95342550e75dc8354c96ff5fd71c8401650f6bf83cfc1051b76fd44334d  node-v6.13.1-darwin-x64.tar.gz
5b5370d7dcaff65eb1509614c62f99bb70042d2f45e1d0e35edca2a8ddcfe32f  node-v6.13.1-darwin-x64.tar.xz
8f99b4f03926eceee4dc3c7a489f9c529628fac1f617a4e410dc1da1efe9b355  node-v6.13.1-headers.tar.gz
52c948317c2e4897339d5cbeefb2f2d53ccddf4d6a2f142c8c96af19c68c02ae  node-v6.13.1-headers.tar.xz
27c9dd1c907f751f073f6d092b72a184a836aac7cac40fdf056edcc1987102b3  node-v6.13.1-linux-arm64.tar.gz
f144ce6563b8f04b2d077df836663788a5c5a126e0375ea52b283a38fa22d9c1  node-v6.13.1-linux-arm64.tar.xz
bb9d1aee17eed7c0777d6d7885aedad450d1fe197705c01fd9928d36d18ef5aa  node-v6.13.1-linux-armv6l.tar.gz
3965e55a412c0f587d4be981c2cfec09f86d40a3386e8d1ec0bc4e4f4b95ab6e  node-v6.13.1-linux-armv6l.tar.xz
b79921ac58b5d74c7edc012e25d4fb760be2aa5c431cc553e3077c68e8fddaf0  node-v6.13.1-linux-armv7l.tar.gz
082082c5767b49cd4d9ae9cbef51a7625de38ac7796292eff0c433a1c7aff891  node-v6.13.1-linux-armv7l.tar.xz
d94f1a709123b864f31c7b877e7a6a654404d8d64a6edf3ce7ebe4f50357a794  node-v6.13.1-linux-ppc64le.tar.gz
ac6a3a975a12e17426e316c2bc5896dfd703c6bca220366897e30a1408d4d2ea  node-v6.13.1-linux-ppc64le.tar.xz
ff70ea5baa6655509cfcf4bad907849bb4c2a63fc2a03a0dcedd562fdf0fca03  node-v6.13.1-linux-ppc64.tar.gz
66f5c48b40acc6f79b85bcf86114fd5e2e65f99d2ee4f9cc4bf979aecd9b27a2  node-v6.13.1-linux-ppc64.tar.xz
c83cc489f427d325e21a14f24f2a5703351d50e35bfe938f57fe2250509d4bfd  node-v6.13.1-linux-s390x.tar.gz
d9528bfa42618627e4e3283ab7658efc9f688c501dcadbc9e3f1672b9cc241c7  node-v6.13.1-linux-s390x.tar.xz
b8eb262c8a0713da7c56736a7e28533303369dae8f0cbdbe901dd3c5f6a19829  node-v6.13.1-linux-x64.tar.gz
f82072a238bf9e290b84fbadaf0cf0e09a05d2a515d59337775342b4ef96db25  node-v6.13.1-linux-x64.tar.xz
236b603b1c114b404cea05f5eacddc5620b4b2aa36475e841808b856efdb298a  node-v6.13.1-linux-x86.tar.gz
95f18d6814a31fcf90abcee4ed2984c0c23611791924692f348e48c4e5472401  node-v6.13.1-linux-x86.tar.xz
aa2d67d5c481fa2b49909eef338898615e68ffb5a93cd2d83ea8566c7b7d3088  node-v6.13.1.pkg
f3559c936654392f07d6f2f0e401d2dfd567e04fa15c2cee0c327d1e90aeb1ab  node-v6.13.1-sunos-x64.tar.gz
03a49cd7e22dcdb9b51bc737307894e57cdb59d57eefbb867f954aa434a566e0  node-v6.13.1-sunos-x64.tar.xz
72959a90454a3f09a7caf7440bc0159b3d1d0f7edcd0cfa9d98967ffbe43be27  node-v6.13.1-sunos-x86.tar.gz
87a6a440f8444235b41b2c9efd4755ced485507a83fa878805bba08b8d72936f  node-v6.13.1-sunos-x86.tar.xz
649374430815aaf425b7b60621a9b7b072a1584cebc676d3cbf0ee4b9bbd94ee  node-v6.13.1.tar.gz
c437350b476503a0f5605a5cc08bc41fe3bdb8ec100939ec7ea6600e44d56a46  node-v6.13.1.tar.xz
adb9ad94f7617749ad894b477cf828264df852b68219fda90711bbc4c43729e2  node-v6.13.1-win-x64.7z
6ef8382388d5a7e4329d9cccd87b1e048677aa1e4fb2e8d750345e194aeb69c7  node-v6.13.1-win-x64.zip
4c89ca47e85af587b00a856249678bbf2611304704e1969ec761b68bbe4a75e4  node-v6.13.1-win-x86.7z
2ad103c0087055c09aaba72ffb7e89d2b9e6a1d105a390d2c8f1f0df5a3df3c9  node-v6.13.1-win-x86.zip
595e074c59ac91ef215aecfba06a9d54067322409f9963024596fa258ec6fc40  node-v6.13.1-x64.msi
fa9efd40080163cfc2eb80bef9fa3eaca991e0de9fa8f6a958737911ddee4de2  node-v6.13.1-x86.msi
3b46c46c1d5944eef6a93f44b0a8be1c1346232c536c93be39675bc1b9a82c7f  win-x64/node.exe
bb607fa6c69484e7ebde7827665388e7eee8d28ebcf8c93a62e9ddf13055f706  win-x64/node.lib
91ca9a11fc7ccb2b2c140b956420d4ba0105f6eda69bb2fbde195f07411b42f1  win-x64/node_pdb.7z
4470fb4701104e67e2aee7c8a60c30ffa99d7df902292694069ddd7ae57fe4ca  win-x64/node_pdb.zip
c58a4ca08c935ff5e5d453262220ea0a3cba81d7d1589dbb85fe8171c11375a8  win-x86/node.exe
4a44485dcfdd1417713dbcff873e3e7987327096530731d5bc2ec5d96e67ad39  win-x86/node.lib
a9904dbc80d83eb7503b73dc29d2f870f2400ce1bfb164860105025b586aa7bd  win-x86/node_pdb.7z
d675d03144ff62ddcc1b55215162f713320d9e8593e162d834d657a0c1723459  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQEzBAEBCAAdFiEEDv/hvO/ZyE49CYFSkzsB9AtcqUYFAlqe8/MACgkQkzsB9Atc
qUYTeAf9Fcn6k6YgT2Ge0GNGb/qoSfJhPB2er0C1gC3jnq/8Qt9BqN/PhPJrgrIf
JhxIcUEXkTfOlM4MPiB/TO2DOu2CmOdjFuwLwF2fi84zyTOXviK8HHR9i1qJoZDH
1/tDxSRGchLsZSbFJ4Qp9wSYeQa6VtdWgEL1a3spVdTcKKR2ljvuCJG3lUQ01nHI
LN/4ul0PJ3CYpuYbxv9uU4luP0H26TOU8RfDs6QTOSrnJkC09P2HO8qcE0EwQl6R
jBRZLtMFOoiJMawqK/Z2xI2l4cl98+9VLY87wBwXL/5mpfRUpv1Le11qgHq0qbpB
8rX8hs/gQ28xCuUwrXYzJpzvHXdiKw==
=7A7T
-----END PGP SIGNATURE-----

```
