---
date: '2019-12-03T22:01:21.491Z'
category: release
title: Node v13.3.0 (Current)
layout: blog-post
author: <PERSON><PERSON>
---

### Notable Changes

- **fs**:
  - Reworked experimental recursive `rmdir()` (cjihrig) [#30644](https://github.com/nodejs/node/pull/30644)
    - The `maxBusyTries` option is renamed to `maxRetries`, and its default is
      set to 0. The `emfileWait` option has been removed, and `EMFILE` errors
      use the same retry logic as other errors. The `retryDelay` option is now
      supported. `ENFILE` errors are now retried.
- **http**:
  - Make maximum header size configurable per-stream or per-server (<PERSON>) [#30570](https://github.com/nodejs/node/pull/30570)
- **http2**:
  - Make maximum tolerated rejected streams configurable (<PERSON>ys <PERSON>) [#30534](https://github.com/nodejs/node/pull/30534)
  - Allow to configure maximum tolerated invalid frames (<PERSON><PERSON>) [#30534](https://github.com/nodejs/node/pull/30534)
- **wasi**:
  - Introduce initial WASI support (cjihrig) [#30258](https://github.com/nodejs/node/pull/30258)

### Commits

- [[`4cd4e7c17a`](https://github.com/nodejs/node/commit/4cd4e7c17a)] - **benchmark,doc,lib,test**: prepare for padding lint rule (Rich Trott) [#30696](https://github.com/nodejs/node/pull/30696)
- [[`63eb4fee46`](https://github.com/nodejs/node/commit/63eb4fee46)] - **buffer**: fix 6-byte writeUIntBE() range check (Brian White) [#30459](https://github.com/nodejs/node/pull/30459)
- [[`e8af569200`](https://github.com/nodejs/node/commit/e8af569200)] - **buffer**: release buffers with free callbacks on env exit (Anna Henningsen) [#30551](https://github.com/nodejs/node/pull/30551)
- [[`648766bccf`](https://github.com/nodejs/node/commit/648766bccf)] - **build**: do not build mksnapshot and mkcodecache for --shared (Joyee Cheung) [#30647](https://github.com/nodejs/node/pull/30647)
- [[`6545314a4f`](https://github.com/nodejs/node/commit/6545314a4f)] - **build**: add --without-node-code-cache configure option (Joyee Cheung) [#30647](https://github.com/nodejs/node/pull/30647)
- [[`80ada94cd3`](https://github.com/nodejs/node/commit/80ada94cd3)] - **build**: use Node.js instead of Node in configure (Tobias Nießen) [#30642](https://github.com/nodejs/node/pull/30642)
- [[`0aae502c67`](https://github.com/nodejs/node/commit/0aae502c67)] - **build,win**: propagate error codes in vcbuild (João Reis) [#30724](https://github.com/nodejs/node/pull/30724)
- [[`6a53152b42`](https://github.com/nodejs/node/commit/6a53152b42)] - **build,win**: add test-ci-native and test-ci-js (João Reis) [#30724](https://github.com/nodejs/node/pull/30724)
- [[`30a4f68a15`](https://github.com/nodejs/node/commit/30a4f68a15)] - **child_process**: document kill() return value (cjihrig) [#30669](https://github.com/nodejs/node/pull/30669)
- [[`dae36a9692`](https://github.com/nodejs/node/commit/dae36a9692)] - **child_process**: replace var with let/const (dnlup) [#30389](https://github.com/nodejs/node/pull/30389)
- [[`4b13bca31a`](https://github.com/nodejs/node/commit/4b13bca31a)] - **child_process**: replace var with const/let in internal/child_process.js (Luis Camargo) [#30414](https://github.com/nodejs/node/pull/30414)
- [[`378c54fe97`](https://github.com/nodejs/node/commit/378c54fe97)] - **cluster**: replace vars in child.js (EmaSuriano) [#30383](https://github.com/nodejs/node/pull/30383)
- [[`708e67a732`](https://github.com/nodejs/node/commit/708e67a732)] - **cluster**: replace var with let (Herrmann, Rene R. (656)) [#30425](https://github.com/nodejs/node/pull/30425)
- [[`55fbe45f69`](https://github.com/nodejs/node/commit/55fbe45f69)] - **cluster**: replace var by let in shared_handle.js (poutch) [#30402](https://github.com/nodejs/node/pull/30402)
- [[`4affc30a12`](https://github.com/nodejs/node/commit/4affc30a12)] - **crypto**: automatically manage memory for ECDSA_SIG (Tobias Nießen) [#30641](https://github.com/nodejs/node/pull/30641)
- [[`55c2ac70b7`](https://github.com/nodejs/node/commit/55c2ac70b7)] - **crypto**: remove redundant validateUint32 argument (Tobias Nießen) [#30579](https://github.com/nodejs/node/pull/30579)
- [[`0ba877a541`](https://github.com/nodejs/node/commit/0ba877a541)] - **deps**: V8: cherry-pick 0dfd9ea51241 (bcoe) [#30713](https://github.com/nodejs/node/pull/30713)
- [[`b470354057`](https://github.com/nodejs/node/commit/b470354057)] - **deps**: patch V8 to 7.9.317.25 (Myles Borins) [#30679](https://github.com/nodejs/node/pull/30679)
- [[`d257448bca`](https://github.com/nodejs/node/commit/d257448bca)] - **deps**: update llhttp to 2.0.1 (Fedor Indutny) [#30553](https://github.com/nodejs/node/pull/30553)
- [[`456d250d2d`](https://github.com/nodejs/node/commit/456d250d2d)] - **deps**: V8: backport 93f189f19a03 (Michaël Zasso) [#30681](https://github.com/nodejs/node/pull/30681)
- [[`aa01ebdbca`](https://github.com/nodejs/node/commit/aa01ebdbca)] - **deps**: V8: cherry-pick ca5b0ec (Anna Henningsen) [#30708](https://github.com/nodejs/node/pull/30708)
- [[`f37450f580`](https://github.com/nodejs/node/commit/f37450f580)] - **dns**: use length for building TXT string (Anna Henningsen) [#30690](https://github.com/nodejs/node/pull/30690)
- [[`3d302ff276`](https://github.com/nodejs/node/commit/3d302ff276)] - **doc**: fix typographical error (Rich Trott) [#30735](https://github.com/nodejs/node/pull/30735)
- [[`19b31c1bc5`](https://github.com/nodejs/node/commit/19b31c1bc5)] - **doc**: revise REPL uncaught exception text (Rich Trott) [#30729](https://github.com/nodejs/node/pull/30729)
- [[`61af1fcaa1`](https://github.com/nodejs/node/commit/61af1fcaa1)] - **doc**: update signature algorithm in release doc (Myles Borins) [#30673](https://github.com/nodejs/node/pull/30673)
- [[`a8002d92ab`](https://github.com/nodejs/node/commit/a8002d92ab)] - **doc**: update README.md to fix active/maint times (Michael Dawson) [#30707](https://github.com/nodejs/node/pull/30707)
- [[`f46df0b496`](https://github.com/nodejs/node/commit/f46df0b496)] - **doc**: update socket.bufferSize text (Rich Trott) [#30723](https://github.com/nodejs/node/pull/30723)
- [[`cbd50262c0`](https://github.com/nodejs/node/commit/cbd50262c0)] - **doc**: note that buf.buffer's contents might differ (AJ Jordan) [#29651](https://github.com/nodejs/node/pull/29651)
- [[`a25626c1ed`](https://github.com/nodejs/node/commit/a25626c1ed)] - **doc**: clarify IncomingMessage.destroy() description (Sam Foxman) [#30255](https://github.com/nodejs/node/pull/30255)
- [[`8fcb450934`](https://github.com/nodejs/node/commit/8fcb450934)] - **doc**: fixed a typo in process.md (Harendra Singh) [#30277](https://github.com/nodejs/node/pull/30277)
- [[`ad9f737e44`](https://github.com/nodejs/node/commit/ad9f737e44)] - **doc**: documenting a bit more FreeBSD case (David Carlier) [#30325](https://github.com/nodejs/node/pull/30325)
- [[`40b762177f`](https://github.com/nodejs/node/commit/40b762177f)] - **doc**: add missing 'added' versions to module.builtinModules (Thomas Watson) [#30562](https://github.com/nodejs/node/pull/30562)
- [[`aca0119089`](https://github.com/nodejs/node/commit/aca0119089)] - **doc**: fix worker.resourceLimits indentation (Daniel Nalborczyk) [#30663](https://github.com/nodejs/node/pull/30663)
- [[`43e78578a6`](https://github.com/nodejs/node/commit/43e78578a6)] - **doc**: fix worker.resourceLimits type (Daniel Nalborczyk) [#30664](https://github.com/nodejs/node/pull/30664)
- [[`20dbce17d5`](https://github.com/nodejs/node/commit/20dbce17d5)] - **doc**: avoid proposal syntax in code example (Alex Zherdev) [#30685](https://github.com/nodejs/node/pull/30685)
- [[`1e7c567734`](https://github.com/nodejs/node/commit/1e7c567734)] - **doc**: address nits for src/README.md (Anna Henningsen) [#30693](https://github.com/nodejs/node/pull/30693)
- [[`87136c9bde`](https://github.com/nodejs/node/commit/87136c9bde)] - **doc**: revise socket.connect() note (Rich Trott) [#30691](https://github.com/nodejs/node/pull/30691)
- [[`fcde49700c`](https://github.com/nodejs/node/commit/fcde49700c)] - **doc**: remove "this API is unstable" note for v8 serdes API (bruce-one) [#30631](https://github.com/nodejs/node/pull/30631)
- [[`809a2b056b`](https://github.com/nodejs/node/commit/809a2b056b)] - **doc**: fixup incorrect flag name reference (Guy Bedford) [#30651](https://github.com/nodejs/node/pull/30651)
- [[`3d978839c1`](https://github.com/nodejs/node/commit/3d978839c1)] - **doc**: minor updates to releases.md (Beth Griggs) [#30636](https://github.com/nodejs/node/pull/30636)
- [[`e9f031c741`](https://github.com/nodejs/node/commit/e9f031c741)] - **doc**: add 13 and 12 to previous versions (Andrew Hughes) [#30590](https://github.com/nodejs/node/pull/30590)
- [[`8ab18b6b6f`](https://github.com/nodejs/node/commit/8ab18b6b6f)] - **doc**: update AUTHORS list (Gus Caplan) [#30672](https://github.com/nodejs/node/pull/30672)
- [[`329a821d25`](https://github.com/nodejs/node/commit/329a821d25)] - **doc**: add explanation why keep var with for loop (Lucas Recknagel) [#30380](https://github.com/nodejs/node/pull/30380)
- [[`426ca263c8`](https://github.com/nodejs/node/commit/426ca263c8)] - **doc**: document "Resume Build" limitation (Richard Lau) [#30604](https://github.com/nodejs/node/pull/30604)
- [[`00f7cc65a1`](https://github.com/nodejs/node/commit/00f7cc65a1)] - **doc**: add note of caution about non-conforming streams (Robert Nagy) [#29895](https://github.com/nodejs/node/pull/29895)
- [[`7d98a59c39`](https://github.com/nodejs/node/commit/7d98a59c39)] - **doc**: add note about debugging worker_threads (Denys Otrishko) [#30594](https://github.com/nodejs/node/pull/30594)
- [[`8ef629a78a`](https://github.com/nodejs/node/commit/8ef629a78a)] - **doc**: simplify "is recommended" language in assert documentation (Rich Trott) [#30558](https://github.com/nodejs/node/pull/30558)
- [[`19d192d1f0`](https://github.com/nodejs/node/commit/19d192d1f0)] - **doc**: fix a typo in a date for version 13.2.0 (Kirlat) [#30587](https://github.com/nodejs/node/pull/30587)
- [[`b67759a93c`](https://github.com/nodejs/node/commit/b67759a93c)] - **doc,deps**: document how to maintain ICU in Node.js (Steven R. Loomis) [#30607](https://github.com/nodejs/node/pull/30607)
- [[`bfcc9142f3`](https://github.com/nodejs/node/commit/bfcc9142f3)] - **doc,n-api**: mark napi_detach_arraybuffer as experimental (legendecas) [#30703](https://github.com/nodejs/node/pull/30703)
- [[`365f0ab09b`](https://github.com/nodejs/node/commit/365f0ab09b)] - **esm**: data URLs should ignore unknown parameters (Bradley Farias) [#30593](https://github.com/nodejs/node/pull/30593)
- [[`0285aa0967`](https://github.com/nodejs/node/commit/0285aa0967)] - **events**: improve performance caused by primordials (guzhizhou) [#30577](https://github.com/nodejs/node/pull/30577)
- [[`3475f9b82c`](https://github.com/nodejs/node/commit/3475f9b82c)] - **fs**: add ENFILE to rimraf retry logic (cjihrig) [#30644](https://github.com/nodejs/node/pull/30644)
- [[`f725953433`](https://github.com/nodejs/node/commit/f725953433)] - **fs**: add retryDelay option to rimraf (cjihrig) [#30644](https://github.com/nodejs/node/pull/30644)
- [[`51bc379243`](https://github.com/nodejs/node/commit/51bc379243)] - **fs**: remove rimraf's emfileWait option (cjihrig) [#30644](https://github.com/nodejs/node/pull/30644)
- [[`612a3a2e6c`](https://github.com/nodejs/node/commit/612a3a2e6c)] - **fs**: make rimraf default to 0 retries (cjihrig) [#30644](https://github.com/nodejs/node/pull/30644)
- [[`fa1f87b199`](https://github.com/nodejs/node/commit/fa1f87b199)] - **fs**: rename rimraf's maxBusyTries to maxRetries (cjihrig) [#30644](https://github.com/nodejs/node/pull/30644)
- [[`8ee27ffe77`](https://github.com/nodejs/node/commit/8ee27ffe77)] - **fs**: change var to let (Àlvar Pérez) [#30407](https://github.com/nodejs/node/pull/30407)
- [[`850c2a72ea`](https://github.com/nodejs/node/commit/850c2a72ea)] - **fs**: cover fs.opendir ERR_INVALID_CALLBACK (Vladislav Botvin) [#30307](https://github.com/nodejs/node/pull/30307)
- [[`62574087ea`](https://github.com/nodejs/node/commit/62574087ea)] - **(SEMVER-MINOR)** **http**: make maximum header size configurable per-stream or per-server (Anna Henningsen) [#30570](https://github.com/nodejs/node/pull/30570)
- [[`1d1d136806`](https://github.com/nodejs/node/commit/1d1d136806)] - **http**: set socket.server unconditionally (Anna Henningsen) [#30571](https://github.com/nodejs/node/pull/30571)
- [[`6848bfbf65`](https://github.com/nodejs/node/commit/6848bfbf65)] - **http**: replace var with let (Guilherme Goncalves) [#30421](https://github.com/nodejs/node/pull/30421)
- [[`8256d38349`](https://github.com/nodejs/node/commit/8256d38349)] - **http**: destructure primordials in lib/\_http_server.js (Artem Maksimov) [#30315](https://github.com/nodejs/node/pull/30315)
- [[`3b169f1dbd`](https://github.com/nodejs/node/commit/3b169f1dbd)] - **http**: improve performance caused by primordials (Lucas Recknagel) [#30416](https://github.com/nodejs/node/pull/30416)
- [[`6f313f9ab0`](https://github.com/nodejs/node/commit/6f313f9ab0)] - **http2**: fix session memory accounting after pausing (Michael Lehenbauer) [#30684](https://github.com/nodejs/node/pull/30684)
- [[`7d37bcebea`](https://github.com/nodejs/node/commit/7d37bcebea)] - **(SEMVER-MINOR)** **http2**: make maximum tolerated rejected streams configurable (Denys Otrishko) [#30534](https://github.com/nodejs/node/pull/30534)
- [[`092a3c28aa`](https://github.com/nodejs/node/commit/092a3c28aa)] - **(SEMVER-MINOR)** **http2**: allow to configure maximum tolerated invalid frames (Denys Otrishko) [#30534](https://github.com/nodejs/node/pull/30534)
- [[`e92afd998f`](https://github.com/nodejs/node/commit/e92afd998f)] - **(SEMVER-MINOR)** **http2**: replace direct array usage with struct for js_fields\_ (Denys Otrishko) [#30534](https://github.com/nodejs/node/pull/30534)
- [[`30ef8e4cbd`](https://github.com/nodejs/node/commit/30ef8e4cbd)] - **http2**: change var to let compact.js (Maria Emmanouil) [#30392](https://github.com/nodejs/node/pull/30392)
- [[`1a2ed4a5f4`](https://github.com/nodejs/node/commit/1a2ed4a5f4)] - **http2**: core.js replace var with let (Daniel Schuech) [#30403](https://github.com/nodejs/node/pull/30403)
- [[`f7ca7e6677`](https://github.com/nodejs/node/commit/f7ca7e6677)] - **http2**: replace var with let/const (Paolo Ceschi Berrini) [#30417](https://github.com/nodejs/node/pull/30417)
- [[`6322611077`](https://github.com/nodejs/node/commit/6322611077)] - **inspector**: properly shut down uv_async_t (Anna Henningsen) [#30612](https://github.com/nodejs/node/pull/30612)
- [[`de3a1c3019`](https://github.com/nodejs/node/commit/de3a1c3019)] - **lib**: enforce use of primordial Number (Sebastien Ahkrin) [#30700](https://github.com/nodejs/node/pull/30700)
- [[`5a9340d723`](https://github.com/nodejs/node/commit/5a9340d723)] - **lib**: use static Number properties from primordials (Michaël Zasso) [#30686](https://github.com/nodejs/node/pull/30686)
- [[`892bde635e`](https://github.com/nodejs/node/commit/892bde635e)] - **lib**: enforce use of Boolean from primordials (Michaël Zasso) [#30698](https://github.com/nodejs/node/pull/30698)
- [[`ae2c7d0b02`](https://github.com/nodejs/node/commit/ae2c7d0b02)] - **lib**: replace Date.now function by primordial DateNow (Tchoupinax) [#30689](https://github.com/nodejs/node/pull/30689)
- [[`c09e3deac5`](https://github.com/nodejs/node/commit/c09e3deac5)] - **lib**: replace ArrayBuffer.isView by primordial ArrayBuffer (Vincent Dhennin) [#30692](https://github.com/nodejs/node/pull/30692)
- [[`5ef4dceb95`](https://github.com/nodejs/node/commit/5ef4dceb95)] - **lib**: enforce use of Array from primordials (Michaël Zasso) [#30635](https://github.com/nodejs/node/pull/30635)
- [[`a4dfe3b7dc`](https://github.com/nodejs/node/commit/a4dfe3b7dc)] - **lib**: flatten access to primordials (Michaël Zasso) [#30610](https://github.com/nodejs/node/pull/30610)
- [[`b545b91de5`](https://github.com/nodejs/node/commit/b545b91de5)] - **lib**: use let instead of var (Shubham Chaturvedi) [#30375](https://github.com/nodejs/node/pull/30375)
- [[`5120926337`](https://github.com/nodejs/node/commit/5120926337)] - **lib**: replace var with let/const (jens-cappelle) [#30391](https://github.com/nodejs/node/pull/30391)
- [[`b18b056d64`](https://github.com/nodejs/node/commit/b18b056d64)] - **lib**: replace var w/ let (Chris Oyler) [#30386](https://github.com/nodejs/node/pull/30386)
- [[`3796885096`](https://github.com/nodejs/node/commit/3796885096)] - **lib**: replace var with let/const (Tijl Claessens) [#30390](https://github.com/nodejs/node/pull/30390)
- [[`ffe3040659`](https://github.com/nodejs/node/commit/ffe3040659)] - **lib**: adding perf notes js_stream_socket.js (ryan jarvinen) [#30415](https://github.com/nodejs/node/pull/30415)
- [[`797b938c49`](https://github.com/nodejs/node/commit/797b938c49)] - **lib**: replace var with let (Dennis Saenger) [#30396](https://github.com/nodejs/node/pull/30396)
- [[`0b64e45e41`](https://github.com/nodejs/node/commit/0b64e45e41)] - **lib**: main_thread_only change var to let (matijagaspar) [#30398](https://github.com/nodejs/node/pull/30398)
- [[`d024630f44`](https://github.com/nodejs/node/commit/d024630f44)] - **lib**: change var to let in stream_base_commons (Kyriakos Markakis) [#30426](https://github.com/nodejs/node/pull/30426)
- [[`3c041edbe7`](https://github.com/nodejs/node/commit/3c041edbe7)] - **lib**: use let instead of var (Semir Ajruli) [#30424](https://github.com/nodejs/node/pull/30424)
- [[`d277c375fd`](https://github.com/nodejs/node/commit/d277c375fd)] - **lib**: changed var to let (Oliver Belaifa) [#30427](https://github.com/nodejs/node/pull/30427)
- [[`0fd89cc0f1`](https://github.com/nodejs/node/commit/0fd89cc0f1)] - **lib**: replace var with let/const (Dries Stelten) [#30409](https://github.com/nodejs/node/pull/30409)
- [[`bdba03e3ed`](https://github.com/nodejs/node/commit/bdba03e3ed)] - **lib**: change var to let (Dimitris Ktistakis) [#30408](https://github.com/nodejs/node/pull/30408)
- [[`48fef42ca9`](https://github.com/nodejs/node/commit/48fef42ca9)] - **lib**: replace var with let/const (Tembrechts) [#30404](https://github.com/nodejs/node/pull/30404)
- [[`502173b54e`](https://github.com/nodejs/node/commit/502173b54e)] - **lib**: replace var to let in cli_table.js (Jing Lin) [#30400](https://github.com/nodejs/node/pull/30400)
- [[`2cf8a7f117`](https://github.com/nodejs/node/commit/2cf8a7f117)] - **module**: fix specifier resolution algorithm (Rongjian Zhang) [#30574](https://github.com/nodejs/node/pull/30574)
- [[`be9788bf20`](https://github.com/nodejs/node/commit/be9788bf20)] - **n-api**: detach external ArrayBuffers on env exit (Anna Henningsen) [#30551](https://github.com/nodejs/node/pull/30551)
- [[`8171cef921`](https://github.com/nodejs/node/commit/8171cef921)] - **(SEMVER-MINOR)** **n-api**: implement napi_is_detached_arraybuffer (Denys Otrishko) [#30613](https://github.com/nodejs/node/pull/30613)
- [[`cc5875b2e6`](https://github.com/nodejs/node/commit/cc5875b2e6)] - **n-api**: add missed nullptr check in napi_has_own_property (Denys Otrishko) [#30626](https://github.com/nodejs/node/pull/30626)
- [[`017280e6e2`](https://github.com/nodejs/node/commit/017280e6e2)] - **net**: replaced vars to lets and consts (nathias) [#30401](https://github.com/nodejs/node/pull/30401)
- [[`56248a827a`](https://github.com/nodejs/node/commit/56248a827a)] - **process**: replace var with let/const (Jesper Ek) [#30382](https://github.com/nodejs/node/pull/30382)
- [[`5c40b2f9ac`](https://github.com/nodejs/node/commit/5c40b2f9ac)] - **process**: replace vars in per_thread.js (EmaSuriano) [#30385](https://github.com/nodejs/node/pull/30385)
- [[`c50bbf58da`](https://github.com/nodejs/node/commit/c50bbf58da)] - **readline**: change var to let (dnlup) [#30435](https://github.com/nodejs/node/pull/30435)
- [[`b91d22cc8d`](https://github.com/nodejs/node/commit/b91d22cc8d)] - **repl**: fix referrer for dynamic import (Corey Farrell) [#30609](https://github.com/nodejs/node/pull/30609)
- [[`4e5818a456`](https://github.com/nodejs/node/commit/4e5818a456)] - **repl**: change var to let (Oliver Belaifa) [#30428](https://github.com/nodejs/node/pull/30428)
- [[`e65ad865c6`](https://github.com/nodejs/node/commit/e65ad865c6)] - **src**: change header file in node_stat_watcher.cc (Reza Fatahi) [#29976](https://github.com/nodejs/node/pull/29976)
- [[`be84ceefb8`](https://github.com/nodejs/node/commit/be84ceefb8)] - **src**: clean up node_file.h (Anna Henningsen) [#30530](https://github.com/nodejs/node/pull/30530)
- [[`bccfd124b0`](https://github.com/nodejs/node/commit/bccfd124b0)] - **src**: remove unused variable in node_dir.cc (gengjiawen) [#30267](https://github.com/nodejs/node/pull/30267)
- [[`fc11db18fe`](https://github.com/nodejs/node/commit/fc11db18fe)] - **src**: inline SetSNICallback (Anna Henningsen) [#30548](https://github.com/nodejs/node/pull/30548)
- [[`7bd587ef0c`](https://github.com/nodejs/node/commit/7bd587ef0c)] - **src**: use BaseObjectPtr to store SNI context (Anna Henningsen) [#30548](https://github.com/nodejs/node/pull/30548)
- [[`8ec0d75de7`](https://github.com/nodejs/node/commit/8ec0d75de7)] - **src**: cleanup unused headers (Alexandre Ferrando) [#30328](https://github.com/nodejs/node/pull/30328)
- [[`6c249c0982`](https://github.com/nodejs/node/commit/6c249c0982)] - **src**: run native immediates during Environment cleanup (Anna Henningsen) [#30666](https://github.com/nodejs/node/pull/30666)
- [[`bea25016d1`](https://github.com/nodejs/node/commit/bea25016d1)] - **src**: no SetImmediate from destructor in stream_pipe code (Anna Henningsen) [#30666](https://github.com/nodejs/node/pull/30666)
- [[`94357db815`](https://github.com/nodejs/node/commit/94357db815)] - **src**: add more `can_call_into_js()` guards (Anna Henningsen) [#30666](https://github.com/nodejs/node/pull/30666)
- [[`d54432f974`](https://github.com/nodejs/node/commit/d54432f974)] - **src**: keep object alive in stream_pipe code (Anna Henningsen) [#30666](https://github.com/nodejs/node/pull/30666)
- [[`d194c0ff37`](https://github.com/nodejs/node/commit/d194c0ff37)] - **src**: replaced var with let (Aldo Ambrosioni) [#30397](https://github.com/nodejs/node/pull/30397)
- [[`44f28ea155`](https://github.com/nodejs/node/commit/44f28ea155)] - **src**: fix -Wsign-compare warnings (cjihrig) [#30565](https://github.com/nodejs/node/pull/30565)
- [[`1916acb3cb`](https://github.com/nodejs/node/commit/1916acb3cb)] - **src**: fix signal handler crash on close (Shelley Vohr) [#30582](https://github.com/nodejs/node/pull/30582)
- [[`9e9e48bf7e`](https://github.com/nodejs/node/commit/9e9e48bf7e)] - **src**: use uv_async_t for WeakRefs (Anna Henningsen) [#30616](https://github.com/nodejs/node/pull/30616)
- [[`9d8d2e1f45`](https://github.com/nodejs/node/commit/9d8d2e1f45)] - **src,doc**: fix broken links (cjihrig) [#30662](https://github.com/nodejs/node/pull/30662)
- [[`f135c38796`](https://github.com/nodejs/node/commit/f135c38796)] - **src,doc**: add C++ internals documentation (Anna Henningsen) [#30552](https://github.com/nodejs/node/pull/30552)
- [[`e968e26dbd`](https://github.com/nodejs/node/commit/e968e26dbd)] - **stream**: improve performance for sync write finishes (Anna Henningsen) [#30710](https://github.com/nodejs/node/pull/30710)
- [[`49e047f7a1`](https://github.com/nodejs/node/commit/49e047f7a1)] - **test**: add coverage for ERR_TLS_INVALID_PROTOCOL_VERSION (Rich Trott) [#30741](https://github.com/nodejs/node/pull/30741)
- [[`81d81a5904`](https://github.com/nodejs/node/commit/81d81a5904)] - **test**: add an indicator `isIBMi` (Xu Meng) [#30714](https://github.com/nodejs/node/pull/30714)
- [[`37c70ee198`](https://github.com/nodejs/node/commit/37c70ee198)] - **test**: use arrow functions in async-hooks tests (garygsc) [#30137](https://github.com/nodejs/node/pull/30137)
- [[`b5c7dad95a`](https://github.com/nodejs/node/commit/b5c7dad95a)] - **test**: fix test-benchmark-streams (Rich Trott) [#30757](https://github.com/nodejs/node/pull/30757)
- [[`1e199ceb71`](https://github.com/nodejs/node/commit/1e199ceb71)] - **test**: move test-http-max-http-headers to parallel (Rich Trott) [#30712](https://github.com/nodejs/node/pull/30712)
- [[`1918b4e84f`](https://github.com/nodejs/node/commit/1918b4e84f)] - **test**: correct header length subtraction (Rich Trott) [#30712](https://github.com/nodejs/node/pull/30712)
- [[`1222be81e3`](https://github.com/nodejs/node/commit/1222be81e3)] - **test**: remove unused callback argument (Rich Trott) [#30712](https://github.com/nodejs/node/pull/30712)
- [[`d69b9b753a`](https://github.com/nodejs/node/commit/d69b9b753a)] - **test**: simplify forEach() usage (Rich Trott) [#30712](https://github.com/nodejs/node/pull/30712)
- [[`01ab031cca`](https://github.com/nodejs/node/commit/01ab031cca)] - **test**: remove unused callback argument (Rich Trott) [#30712](https://github.com/nodejs/node/pull/30712)
- [[`93707c4916`](https://github.com/nodejs/node/commit/93707c4916)] - **test**: increase coverage for trace_events.js (Rich Trott) [#30705](https://github.com/nodejs/node/pull/30705)
- [[`4800b623ed`](https://github.com/nodejs/node/commit/4800b623ed)] - **test**: use arrow functions in addons tests (garygsc) [#30131](https://github.com/nodejs/node/pull/30131)
- [[`ba0115fe6f`](https://github.com/nodejs/node/commit/ba0115fe6f)] - **test**: refactor createHook test (Jeny) [#30568](https://github.com/nodejs/node/pull/30568)
- [[`099d3fdf87`](https://github.com/nodejs/node/commit/099d3fdf87)] - **test**: port worker + buffer test to N-API (Anna Henningsen) [#30551](https://github.com/nodejs/node/pull/30551)
- [[`83861fb333`](https://github.com/nodejs/node/commit/83861fb333)] - **test**: revert 6d022c13 (Anna Henningsen) [#30708](https://github.com/nodejs/node/pull/30708)
- [[`a3b758d634`](https://github.com/nodejs/node/commit/a3b758d634)] - **test**: move test-https-server-consumed-timeout to parallel (Rich Trott) [#30677](https://github.com/nodejs/node/pull/30677)
- [[`00f532f15e`](https://github.com/nodejs/node/commit/00f532f15e)] - **test**: remove unnecessary common.platformTimeout() call (Rich Trott) [#30677](https://github.com/nodejs/node/pull/30677)
- [[`ecb902f33c`](https://github.com/nodejs/node/commit/ecb902f33c)] - **test**: do not skip test-http-server-consumed-timeout (Rich Trott) [#30677](https://github.com/nodejs/node/pull/30677)
- [[`49458deb4f`](https://github.com/nodejs/node/commit/49458deb4f)] - **test**: remove unused function argument from http test (Rich Trott) [#30677](https://github.com/nodejs/node/pull/30677)
- [[`a2f440d326`](https://github.com/nodejs/node/commit/a2f440d326)] - **test**: add logging in case of infinite loop (Rich Trott) [#30649](https://github.com/nodejs/node/pull/30649)
- [[`3e3ad396bd`](https://github.com/nodejs/node/commit/3e3ad396bd)] - **test**: remove destructuring from test-inspector-contexts (Rich Trott) [#30649](https://github.com/nodejs/node/pull/30649)
- [[`3571e132a7`](https://github.com/nodejs/node/commit/3571e132a7)] - **test**: check for session.post() errors in test-insepctor-context (Rich Trott) [#30649](https://github.com/nodejs/node/pull/30649)
- [[`37696320a2`](https://github.com/nodejs/node/commit/37696320a2)] - **test**: add mustCall() to test-inspector-contexts (Rich Trott) [#30649](https://github.com/nodejs/node/pull/30649)
- [[`0972fa3c16`](https://github.com/nodejs/node/commit/0972fa3c16)] - **test**: add regression test for signal handler removal in exit (Anna Henningsen) [#30589](https://github.com/nodejs/node/pull/30589)
- [[`5ecfd947e2`](https://github.com/nodejs/node/commit/5ecfd947e2)] - **(SEMVER-MINOR)** **test**: update and harden http2-reset-flood (Denys Otrishko) [#30534](https://github.com/nodejs/node/pull/30534)
- [[`70d6fa122a`](https://github.com/nodejs/node/commit/70d6fa122a)] - **test**: skip test-domain-error-types in debug mode temporariliy (Rich Trott) [#30629](https://github.com/nodejs/node/pull/30629)
- [[`949f2ad528`](https://github.com/nodejs/node/commit/949f2ad528)] - **test**: move test-worker-prof to sequential (Rich Trott) [#30628](https://github.com/nodejs/node/pull/30628)
- [[`d4b61709f1`](https://github.com/nodejs/node/commit/d4b61709f1)] - **test**: dir class initialisation w/o handler (Dmitriy Kikinskiy) [#30313](https://github.com/nodejs/node/pull/30313)
- [[`60b17b4fe6`](https://github.com/nodejs/node/commit/60b17b4fe6)] - **test**: change object assign by spread operator (poutch) [#30438](https://github.com/nodejs/node/pull/30438)
- [[`97e627335f`](https://github.com/nodejs/node/commit/97e627335f)] - **test**: use useful message argument in test function (Rich Trott) [#30618](https://github.com/nodejs/node/pull/30618)
- [[`d651c7dd6b`](https://github.com/nodejs/node/commit/d651c7dd6b)] - **test**: test for minimum ICU version consistency (Richard Lau) [#30608](https://github.com/nodejs/node/pull/30608)
- [[`dade9069c3`](https://github.com/nodejs/node/commit/dade9069c3)] - **test**: code&learn var to let update (Nazar Malyy) [#30436](https://github.com/nodejs/node/pull/30436)
- [[`e401e8c8ed`](https://github.com/nodejs/node/commit/e401e8c8ed)] - **test**: change object assign to spread object (poutch) [#30422](https://github.com/nodejs/node/pull/30422)
- [[`2ecc735c48`](https://github.com/nodejs/node/commit/2ecc735c48)] - **test**: use spread instead of Object.assign (dnlup) [#30419](https://github.com/nodejs/node/pull/30419)
- [[`d8da9dacab`](https://github.com/nodejs/node/commit/d8da9dacab)] - **test**: changed var to let in module-errors (Jamar Torres) [#30413](https://github.com/nodejs/node/pull/30413)
- [[`9dab32f340`](https://github.com/nodejs/node/commit/9dab32f340)] - **test**: use spread instead of object.assign (Shubham Chaturvedi) [#30412](https://github.com/nodejs/node/pull/30412)
- [[`7e7a8165a8`](https://github.com/nodejs/node/commit/7e7a8165a8)] - **test**: replace var with let in pre_execution.js (Vladimir Adamic) [#30411](https://github.com/nodejs/node/pull/30411)
- [[`8a9ee48797`](https://github.com/nodejs/node/commit/8a9ee48797)] - **test**: change var to let in test-trace-events (Jon Church) [#30406](https://github.com/nodejs/node/pull/30406)
- [[`d6a448825c`](https://github.com/nodejs/node/commit/d6a448825c)] - **test**: dns utils replace var (Osmond van Hemert) [#30405](https://github.com/nodejs/node/pull/30405)
- [[`01e0571e94`](https://github.com/nodejs/node/commit/01e0571e94)] - **test**: test cover cases when trace is empty (telenord) [#30311](https://github.com/nodejs/node/pull/30311)
- [[`f8dfa2d704`](https://github.com/nodejs/node/commit/f8dfa2d704)] - **test**: switch to object spread in common/benchmark.js (palmires) [#30309](https://github.com/nodejs/node/pull/30309)
- [[`36671f9bf8`](https://github.com/nodejs/node/commit/36671f9bf8)] - **test**: add common.mustCall() to stream test (Rich Trott) [#30561](https://github.com/nodejs/node/pull/30561)
- [[`106235fe91`](https://github.com/nodejs/node/commit/106235fe91)] - **test**: move explanatory comment to expected location in file (Rich Trott) [#30561](https://github.com/nodejs/node/pull/30561)
- [[`081b4e2496`](https://github.com/nodejs/node/commit/081b4e2496)] - **test**: move stream test to parallel (Rich Trott) [#30561](https://github.com/nodejs/node/pull/30561)
- [[`103d01e057`](https://github.com/nodejs/node/commit/103d01e057)] - **test**: remove string literal as message in strictEqual() in stream test (Rich Trott) [#30561](https://github.com/nodejs/node/pull/30561)
- [[`ebba3228e2`](https://github.com/nodejs/node/commit/ebba3228e2)] - **test**: use arrow function for callback in stream test (Rich Trott) [#30561](https://github.com/nodejs/node/pull/30561)
- [[`e122d397c0`](https://github.com/nodejs/node/commit/e122d397c0)] - **test**: replace setTimeout with setImmediate in stream test (Rich Trott) [#30561](https://github.com/nodejs/node/pull/30561)
- [[`20ee4997f3`](https://github.com/nodejs/node/commit/20ee4997f3)] - **test**: refactor test-dgram-multicast-set-interface-lo.js (Taylor Gagne) [#30536](https://github.com/nodejs/node/pull/30536)
- [[`7aa1df7076`](https://github.com/nodejs/node/commit/7aa1df7076)] - **tls**: introduce ERR_TLS_INVALID_CONTEXT (Rich Trott) [#30718](https://github.com/nodejs/node/pull/30718)
- [[`0b0f0237c1`](https://github.com/nodejs/node/commit/0b0f0237c1)] - **tls**: add memory tracking support to SSLWrap (Anna Henningsen) [#30548](https://github.com/nodejs/node/pull/30548)
- [[`89e2c71b27`](https://github.com/nodejs/node/commit/89e2c71b27)] - **tls**: allow empty subject even with altNames defined (Jason Macgowan) [#22906](https://github.com/nodejs/node/pull/22906)
- [[`941a91daed`](https://github.com/nodejs/node/commit/941a91daed)] - **tools**: enforce blank line between functions (Rich Trott) [#30696](https://github.com/nodejs/node/pull/30696)
- [[`5a6f836a15`](https://github.com/nodejs/node/commit/5a6f836a15)] - **tools**: add unified plugin changing links for html docs (Marek Łabuz) [#29946](https://github.com/nodejs/node/pull/29946)
- [[`84f7b5c752`](https://github.com/nodejs/node/commit/84f7b5c752)] - **tools**: enable more eslint rules (cjihrig) [#30598](https://github.com/nodejs/node/pull/30598)
- [[`5522467cf5`](https://github.com/nodejs/node/commit/5522467cf5)] - **tools**: update ESLint to 6.7.1 (cjihrig) [#30598](https://github.com/nodejs/node/pull/30598)
- [[`1f10681496`](https://github.com/nodejs/node/commit/1f10681496)] - **tty**: truecolor check moved before 256 check (Duncan Healy) [#30474](https://github.com/nodejs/node/pull/30474)
- [[`6a0dd1cbbd`](https://github.com/nodejs/node/commit/6a0dd1cbbd)] - **util**: fix .format() not always calling toString when it should be (Ruben Bridgewater) [#30343](https://github.com/nodejs/node/pull/30343)
- [[`1040e7222f`](https://github.com/nodejs/node/commit/1040e7222f)] - **util**: fix inspection of errors with tampered name or stack property (Ruben Bridgewater) [#30576](https://github.com/nodejs/node/pull/30576)
- [[`18e9b56bf6`](https://github.com/nodejs/node/commit/18e9b56bf6)] - **util**: use let instead of var for util/inspect.js (Luciano) [#30399](https://github.com/nodejs/node/pull/30399)
- [[`9ec53cf5c1`](https://github.com/nodejs/node/commit/9ec53cf5c1)] - **(SEMVER-MINOR)** **wasi**: introduce initial WASI support (cjihrig) [#30258](https://github.com/nodejs/node/pull/30258)

Windows 32-bit Installer: https://nodejs.org/dist/v13.3.0/node-v13.3.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v13.3.0/node-v13.3.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v13.3.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v13.3.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v13.3.0/node-v13.3.0.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v13.3.0/node-v13.3.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v13.3.0/node-v13.3.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v13.3.0/node-v13.3.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v13.3.0/node-v13.3.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v13.3.0/node-v13.3.0-aix-ppc64.tar.gz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v13.3.0/node-v13.3.0-sunos-x64.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v13.3.0/node-v13.3.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v13.3.0/node-v13.3.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v13.3.0/node-v13.3.0.tar.gz \
Other release files: https://nodejs.org/dist/v13.3.0/ \
Documentation: https://nodejs.org/docs/v13.3.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

dd9e7f3fb9ef6bd5a27933f66c7dbf4d84aa5b001eab6315595924e5e44e4ae1  node-v13.3.0-aix-ppc64.tar.gz
187ea9028daa6d9abad9c1cbb4e12ba51427c3748da29eae616fa352c0f4cd49  node-v13.3.0-darwin-x64.tar.gz
b3a43aa893067d05e7ea78b039ca2d21d4a3ec04f43a761a1c31b692ce53fc1f  node-v13.3.0-darwin-x64.tar.xz
6809251dcbcb8c38b67aa3bf211a9d5de742981b27adcdff8ab259dae30025eb  node-v13.3.0-headers.tar.gz
cd463852a46d514728ea147050d15304472cd4e13552ea94142cb79d2a1928b0  node-v13.3.0-headers.tar.xz
7df90bda5d21337c7793b481ee71fd89811c26cd0d6124665a79cd8bffb2f7ba  node-v13.3.0-linux-arm64.tar.gz
fb201a908bea36be828eee36a7cd898c67ad4ae6846982f393466e2b8b826c16  node-v13.3.0-linux-arm64.tar.xz
806cbdb68fe352f68d9b7a34a1d3ff7661a2359062b7a056c3cf28da3306f480  node-v13.3.0-linux-armv7l.tar.gz
5a029a34a3906aee791b188a4471a34343d257670b67e1ade2796cb0466523d9  node-v13.3.0-linux-armv7l.tar.xz
8abd0e80a62af2ca8b469bed5eca7b8c868b72c0faaed73dbecfe648c35dad45  node-v13.3.0-linux-ppc64le.tar.gz
1b24e0918f52a1c99f66a1db6375d4e81662480c702a80d6483cdddad2bca80a  node-v13.3.0-linux-ppc64le.tar.xz
25708b13bbfd8388a15d53768f8536fca1a1a6fd949f4f9debe4e3d6eeda533b  node-v13.3.0-linux-s390x.tar.gz
d60a64562e3ffaa03964e90e11dad6dba4f294add5b7a8c9e57a7ca3ae81d710  node-v13.3.0-linux-s390x.tar.xz
155b0510732d2f48150dc6bc4b25eb44ce5cd54d21c70d2ca7f31be3b9ab7fa6  node-v13.3.0-linux-x64.tar.gz
b208393ae411bd55188e903171901765aaa8381c08155e4a7b34174737a1bc65  node-v13.3.0-linux-x64.tar.xz
cf7cda8a042397ad4a4bc4f90201a1113822b02c6b18c289a13c912a4ec010bd  node-v13.3.0-sunos-x64.tar.gz
dd1fab03d6c635f4d473e2c44568478d0fa582aea6b0f2fad728ad2e53424c0a  node-v13.3.0-sunos-x64.tar.xz
9bc1cded78ffe6d5a646976efbd224f0cae15b2efcdda6058a6b948bd409a77e  node-v13.3.0-win-x64.7z
82e0f43aea81374a8028a7d3c9993245c4910ac95cd0f66ee5b29b940a6f3d93  node-v13.3.0-win-x64.zip
42a883d37a0b3c99e4d1356daa3c9db295ed543f4a2a62c89c3d67ced5c955d4  node-v13.3.0-win-x86.7z
067355bccafcd810aab50e0c2bcf57e9d89ffed7130a1bddd366bccec870fc0d  node-v13.3.0-win-x86.zip
82a23b8a990f6834a3c8e4855a3393fd4fff0da15b5c70a8abf2775e70edf607  node-v13.3.0-x64.msi
df8f9b8b705feaff1bdf3fc477662984f3b9bb6227c953d43cae709131fc6d9f  node-v13.3.0-x86.msi
8a76651fa9ff4226482e82a2cea37a1e972d4e411e405548b17b25702f8e4a40  node-v13.3.0.pkg
cab5bd4712265745cfce1416ea0f425f1de71452f0ddc544d815aacee2aede3e  node-v13.3.0.tar.gz
e7da93cc9f2b866e169d9c6b42193bb4d25ed942e2fd19120787e0e581936648  node-v13.3.0.tar.xz
9dbe5f1fcee6c2f7576c330da207ef1d351252fd82c061789a0c1737c72d1e68  win-x64/node.exe
9d8e207dfee84d6e68ec3442c8aa84243c79e9a80621bc8108edecdb7fc59f35  win-x64/node.lib
3f32c028bbdbfe5872d16019961a3ecffc72be8f8d5c8e60e4f9f11fdeffc979  win-x64/node_pdb.7z
a074b11c8d16c401d315e0d281cd081770db4f9031918450287df9f471ff2f4f  win-x64/node_pdb.zip
0c48408950723c56dd3242fece10a6047e86eab0393628bbd5130c00ffc97cad  win-x86/node.exe
080ea2bfceb5fc165ba24a7ef6e8ef2e80151dbdd457626ae5ab6e7705594e43  win-x86/node.lib
6ddc296d6d17d93b2adf862649d7e3e84d82c33253a15b580865fa7823efb5ac  win-x86/node_pdb.7z
e7969d71386d8ec1a8dc7ad385aed85c5da0a8af9901547ff42c2d5ac7278b47  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEpIwr7mgOhBYyzU5E8HSWs+s8F2IFAl3m2ucACgkQ8HSWs+s8
F2JZIBAAkcowDmkK39RaifNl5PExUEbwMU3dK8l7YU2KfQvwgfae/CtAcZrFJ6bX
pEsjjXIF3PIoYxEKFj4ZV/etBE5xqPjwboAVsBxfuIdfka28F0ASHs+wOTy7iF/3
DfaQ41Rh9YdJlwhk+2ii9pTtCce77Z0eLm41oCySC/tVqUPHTUcEUIF/8e+lqGcS
rJoBM3ClIzVo+1zOrXVF42Nny7BglBzeABkMbZ4s9t6XTu0PUM/9Ssda3PlL8qyJ
0q/fehUUdsbvWFjtFlkfsT2ioFfB7GozasCziYn76W0c9lo7TIAivwpe883HaR9m
rdlfV6J2k47DCYOV/jkPyR4R/dRth9vCt+bzOYSFKnpT32rCqK1hZyxVgWskgCoo
mY5euWtDsDGnQKzYmLWJYPFOmsdLaeSZW13kDye2UX+0Ys2NCdmLSJT0lqHDoCJd
3Avx4PZ0dalbxmpCqy7OZ6cP5U5Lq/EqdRln99WOSYb03x9P+5r7KA/U3ae7HoHs
NX2GtH+FjEQvo6I5y0YvalB1kPJEmHpHG6TUyei2ePotFCZlj3M23C28OkVSxAxU
9jiKrE4kzmc+EleHG3tpdENmggr4MaKwMt0thKsZZX7ltwoILl73mLfvf+wzzxPu
kqfP2NTFXoa4UrkC6TyuaRMx3McngYRubnmpzoPE1DbF+2SD+r4=
=pSx8
-----END PGP SIGNATURE-----

```
