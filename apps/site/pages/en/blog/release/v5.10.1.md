---
date: '2016-04-05T23:33:44.892Z'
category: release
title: Node v5.10.1 (Current)
layout: blog-post
author: <PERSON><PERSON>
---

### Notable changes

- **http**:

  - Enclose IPv6 Host header in square brackets. This will enable proper separation of the host address from any port reference (<PERSON><PERSON>) [#5314](https://github.com/nodejs/node/pull/5314)

- **path**:
  - Make win32.isAbsolute more consistent (<PERSON>) [#6028](https://github.com/nodejs/node/pull/6028)

### Commits

- [[`0f5a51ae4b`](https://github.com/nodejs/node/commit/0f5a51ae4b)] - **assert**: Check typed array view type in deepEqual (<PERSON>) [#5910](https://github.com/nodejs/node/pull/5910)
- [[`e966d1f5db`](https://github.com/nodejs/node/commit/e966d1f5db)] - **buffer**: don't set `kNoZeroFill` flag in allocUnsafe (<PERSON>) [#6007](https://github.com/nodejs/node/pull/6007)
- [[`3f75751c2e`](https://github.com/nodejs/node/commit/3f75751c2e)] - **build**: introduce ci targets for lint/benchmark (Johan Bergström) [#5921](https://github.com/nodejs/node/pull/5921)
- [[`781290b61d`](https://github.com/nodejs/node/commit/781290b61d)] - **doc**: refine child_process detach behaviour (Robert Jefe Lindstaedt) [#5330](https://github.com/nodejs/node/pull/5330)
- [[`aa9fb03202`](https://github.com/nodejs/node/commit/aa9fb03202)] - **doc**: use HTTPS for links where possible (Rich Trott) [#6019](https://github.com/nodejs/node/pull/6019)
- [[`dd25984838`](https://github.com/nodejs/node/commit/dd25984838)] - **doc**: note assert.throws() pitfall (Rich Trott) [#6029](https://github.com/nodejs/node/pull/6029)
- [[`f879f5e68a`](https://github.com/nodejs/node/commit/f879f5e68a)] - **doc**: document unspecified behavior for buf.write\* methods (James M Snell) [#5925](https://github.com/nodejs/node/pull/5925)
- [[`f12c3861e0`](https://github.com/nodejs/node/commit/f12c3861e0)] - **doc**: clarify stdout/stderr arguments to callback (James M Snell) [#6015](https://github.com/nodejs/node/pull/6015)
- [[`ce173716be`](https://github.com/nodejs/node/commit/ce173716be)] - **doc**: add 'Command Line Options' to 'View on single page' (firedfox) [#6011](https://github.com/nodejs/node/pull/6011)
- [[`7337ef6422`](https://github.com/nodejs/node/commit/7337ef6422)] - **doc**: minor argument formatting in stream.markdown (James M Snell) [#6016](https://github.com/nodejs/node/pull/6016)
- [[`0ae5d027c6`](https://github.com/nodejs/node/commit/0ae5d027c6)] - **doc**: clarify that \_\_dirname is module local (James M Snell) [#6018](https://github.com/nodejs/node/pull/6018)
- [[`8bec8aa41f`](https://github.com/nodejs/node/commit/8bec8aa41f)] - **doc**: consolidate timers docs in timers.markdown (Bryan English) [#5837](https://github.com/nodejs/node/pull/5837)
- [[`0a13099c42`](https://github.com/nodejs/node/commit/0a13099c42)] - **etw**: add event messages (João Reis) [#5936](https://github.com/nodejs/node/pull/5936)
- [[`c6ac6f2ea1`](https://github.com/nodejs/node/commit/c6ac6f2ea1)] - **http**: Corrects IPv6 address in Host header (Mihai Potra) [#5314](https://github.com/nodejs/node/pull/5314)
- [[`8317778925`](https://github.com/nodejs/node/commit/8317778925)] - **meta**: add "joining a wg" section to WORKING_GROUPS.md (Matteo Collina) [#5488](https://github.com/nodejs/node/pull/5488)
- [[`f3f19ee5e2`](https://github.com/nodejs/node/commit/f3f19ee5e2)] - **net**: refactor self=this to arrow functions (Benjamin Gruenbaum) [#5857](https://github.com/nodejs/node/pull/5857)
- [[`1c4007927d`](https://github.com/nodejs/node/commit/1c4007927d)] - **path**: fix win32.isAbsolute() inconsistency (Brian White) [#6028](https://github.com/nodejs/node/pull/6028)
- [[`059b607a4f`](https://github.com/nodejs/node/commit/059b607a4f)] - **test**: make use of globals explicit (Rich Trott) [#6014](https://github.com/nodejs/node/pull/6014)
- [[`cc8fcc5a07`](https://github.com/nodejs/node/commit/cc8fcc5a07)] - **test**: be explicit about polluting of `global` (Rich Trott) [#6017](https://github.com/nodejs/node/pull/6017)
- [[`7db7a820b9`](https://github.com/nodejs/node/commit/7db7a820b9)] - **test**: make arch available in status files (Santiago Gimeno) [#5997](https://github.com/nodejs/node/pull/5997)
- [[`02f2ebd9b4`](https://github.com/nodejs/node/commit/02f2ebd9b4)] - **test**: explicitly set global in test-repl (Rich Trott) [#6026](https://github.com/nodejs/node/pull/6026)
- [[`2ab1237137`](https://github.com/nodejs/node/commit/2ab1237137)] - **test**: fix flaky test-net-socket-timeout-unref (Rich Trott) [#6003](https://github.com/nodejs/node/pull/6003)
- [[`0127c2bd39`](https://github.com/nodejs/node/commit/0127c2bd39)] - **test**: fix test-dns.js flakiness (Rich Trott) [#5996](https://github.com/nodejs/node/pull/5996)
- [[`6052ced37f`](https://github.com/nodejs/node/commit/6052ced37f)] - **test**: fix error message checks in test-module-loading (James M Snell) [#5986](https://github.com/nodejs/node/pull/5986)
- [[`a40b0cb673`](https://github.com/nodejs/node/commit/a40b0cb673)] - **test**: refactor http-end-throw-socket-handling (Santiago Gimeno) [#5676](https://github.com/nodejs/node/pull/5676)
- [[`96bb315262`](https://github.com/nodejs/node/commit/96bb315262)] - **test**: ensure \_handle property existence (Rich Trott) [#5916](https://github.com/nodejs/node/pull/5916)
- [[`4f1fa2adeb`](https://github.com/nodejs/node/commit/4f1fa2adeb)] - **test**: fix offending max-len linter error (Sakthipriyan Vairamani) [#5980](https://github.com/nodejs/node/pull/5980)
- [[`f14d71ccea`](https://github.com/nodejs/node/commit/f14d71ccea)] - **test**: stdin is not always a net.Socket (Jeremiah Senkpiel) [#5935](https://github.com/nodejs/node/pull/5935)
- [[`50a062e691`](https://github.com/nodejs/node/commit/50a062e691)] - **tools**: remove obsolete lint config file (Rich Trott) [#5959](https://github.com/nodejs/node/pull/5959)
- [[`7491fdcfe9`](https://github.com/nodejs/node/commit/7491fdcfe9)] - **tools**: remove disabling of already-disabled rule (Rich Trott) [#6013](https://github.com/nodejs/node/pull/6013)

Windows 32-bit Installer: https://nodejs.org/dist/v5.10.1/node-v5.10.1-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v5.10.1/node-v5.10.1-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v5.10.1/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v5.10.1/win-x64/node.exe \
Mac OS X 64-bit Installer: https://nodejs.org/dist/v5.10.1/node-v5.10.1.pkg \
Mac OS X 64-bit Binary: https://nodejs.org/dist/v5.10.1/node-v5.10.1-darwin-x64.tar.gz \
Linux 32-bit Binary: https://nodejs.org/dist/v5.10.1/node-v5.10.1-linux-x86.tar.xz \
Linux 64-bit Binary: https://nodejs.org/dist/v5.10.1/node-v5.10.1-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v5.10.1/node-v5.10.1-linux-ppc64le.tar.xz \
SmartOS 32-bit Binary: https://nodejs.org/dist/v5.10.1/node-v5.10.1-sunos-x86.tar.xz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v5.10.1/node-v5.10.1-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v5.10.1/node-v5.10.1-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v5.10.1/node-v5.10.1-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v5.10.1/node-v5.10.1-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v5.10.1/node-v5.10.1.tar.gz \
Other release files: https://nodejs.org/dist/v5.10.1/ \
Documentation: https://nodejs.org/docs/v5.10.1/api/

Shasums (GPG signing hash: SHA512, file hash: SHA256):

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA1

00ffc5c662580e1a5062a8740a9b9a40dbf7dadb5c8aa16bdf0ed33c7c1dfbfb  node-v5.10.1-darwin-x64.tar.gz
52d6103a14690c4541e6924d28687b23dbcf65cd171a50529db0998004e888c7  node-v5.10.1-darwin-x64.tar.xz
77e1469d6c2c6f49e68cb3fb8052ee90284700e7da013a7d47ad1cefcd8afc63  node-v5.10.1-headers.tar.gz
a3c5c8f545ca320305128731f546a24387719768ccf5fa8a5e3059ee26f43370  node-v5.10.1-headers.tar.xz
98e4f003818968d5b9bcf17c921d33a5e3d6866be63d80510ae7ff8877e817db  node-v5.10.1-linux-arm64.tar.gz
2c264c596a9bf1a962b37ea51afa3540f819a1428627a28b6a25abe284eb04ab  node-v5.10.1-linux-arm64.tar.xz
5d6f652ce962a0fb59edf5e305af3a7e9147489ebb90a1244f3fa67d86fcf54b  node-v5.10.1-linux-armv6l.tar.gz
bfa551df4739d3f975c4114f1e87b0060bb1998d27b4916a0238561bd4478e7c  node-v5.10.1-linux-armv6l.tar.xz
f1ccddf5fc894a4a4becdf9b32e579b2aad6c6ace189890dbdcb934afbaab060  node-v5.10.1-linux-armv7l.tar.gz
d35134ec4c6ac723da20898c3061c11b71823e09167cf830ab56983cb33affeb  node-v5.10.1-linux-armv7l.tar.xz
b7137dc0bc9a6e3a91be67f05330d2722d58b4e50047d3455a7c035c455701b4  node-v5.10.1-linux-ppc64le.tar.gz
2bc959b36318c0a64b86e0eedbc5179b59db9a8b4f1577039182d7c283b1ffc2  node-v5.10.1-linux-ppc64le.tar.xz
897506e1e83cba9b780b030c9cc7299b0ae8872c0b8b0081a86996079025cea5  node-v5.10.1-linux-x64.tar.gz
edc2afd401d5bbbc05aee186e94ea21078bd4d733da0fc1e95cb8dfbae04bcc8  node-v5.10.1-linux-x64.tar.xz
a4c17570bd03424cd57affd10a4ef7d3b76167d3646656236bc78b86bc622a88  node-v5.10.1-linux-x86.tar.gz
d2fd77706d6b122f978ac68eba540e3ad8efb93909b56a1fe944af81b74b552e  node-v5.10.1-linux-x86.tar.xz
9bf1a7a92debe7613e6ff0d1852aa09e8ed95cf01f276e71848d44235059423a  node-v5.10.1.pkg
0cb823dacc340aa79b70a08d7d81a72260c3414b6f07398ab80e755efcc93f85  node-v5.10.1-sunos-x64.tar.gz
780c323919d1912989784e5577212a56856e3ceb7eab11a8d3d43bf6a7da3582  node-v5.10.1-sunos-x64.tar.xz
1d2731f7ffb46d9f2153993f24ac36a5e98dd136ad28600e45d918205997403f  node-v5.10.1-sunos-x86.tar.gz
75096b173bbbd920b2039b3ede985025f0c8dc91f999224626163d3cb9458cb5  node-v5.10.1-sunos-x86.tar.xz
c6e278b612b53c240ddf85521403e55abfd8f0201d2f2c7e3d2c21383054aacd  node-v5.10.1.tar.gz
9eecd853cdd06ebee24a1bb8d753cd20af5a19297c5d3a3b1680fe36b47d5cbe  node-v5.10.1.tar.xz
a1207fa1e831c1347d338546e353ea195d5cb1acb7e290dad8836153aad11c35  node-v5.10.1-x64.msi
87a902472fe0458ee444456ef5a9bdbd9247c7747b4deeb7a7ded43e56b43daa  node-v5.10.1-x86.msi
4985991e02af90ca5628a6f3e5aa8d913c72b1c898fa0202db47cb757b576ac9  win-x64/node.exe
8b2c8cfb473fa45bf24567302c0f5adf4d573afc13d27797e717dc0696336f01  win-x64/node.lib
bbf7ca1d2bb2c590d97e785bbc7fe0f5478b4ee586d47a0e95ca6cff1b9f968b  win-x86/node.exe
23be78c07830ad80f1b188dd9ef38dc9e267728d1f09acd435b42914875ae89d  win-x86/node.lib
-----BEGIN PGP SIGNATURE-----

iQEcBAEBAgAGBQJXBEq/AAoJEJM7AfQLXKlGLc8H/1tjkCtDxKsrVWCTdTA+L4nD
qkptH/E78gmGSq6BjQUV/usfhJVvH4kot9OGjIAs9rJezSco+y2jLkNfo6gm4SSt
tI6pFxrZDnBC+qtXcdCcfjXiRhmPVInJVLHnU0H6BhkMEHcyjyPHim+UNIfbmOB4
MD+Lduqf6xe8VTscipPujXFsS6B2XT2pMFCAyUJqz2xd23QubAGWSLWJ/eK3P0h5
hhC7EHoVDkVhlByRhuSK74yKqm8jb4Rtv1tYJi2UM8Ip/XQhM+YcLFV3+k7e5huu
X7sIluHGRzjaqts4BR79Fr0i80yBL06b7JCDl8McMn1wfZPDG+E/iSnU/zfS7g8=
=aUEi
-----END PGP SIGNATURE-----

```
