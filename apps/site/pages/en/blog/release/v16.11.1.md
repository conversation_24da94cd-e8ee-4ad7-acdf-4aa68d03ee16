---
date: '2021-10-12T15:35:28.798Z'
category: release
title: Node v16.11.1 (Current)
layout: blog-post
author: <PERSON>
---

### Notable changes

- **CVE-2021-22959**: HTTP Request Smuggling due to spaced in headers (Medium)
  - The http parser accepts requests with a space (SP) right after the header name before the colon. This can lead to HTTP Request Smuggling (HRS). More details will be available at [CVE-2021-22959](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2021-22959) after publication.
- **CVE-2021-22960**: HTTP Request Smuggling when parsing the body (Medium)
  - The parse ignores chunk extensions when parsing the body of chunked requests. This leads to HTTP Request Smuggling (HRS) under certain conditions. More details will be available at [CVE-2021-22960](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2021-22960) after publication.

### Commits

- [[`af488f8dc8`](https://github.com/nodejs/node/commit/af488f8dc8)] - **deps**: update llhttp to 6.0.4 (<PERSON> <PERSON>a) [nodejs-private/node-private#284](https://github.com/nodejs-private/node-private/pull/284)
- [[`2d1eefad98`](https://github.com/nodejs/node/commit/2d1eefad98)] - **http**: add regression test for smuggling content length (Matteo Collina) [nodejs-private/node-private#284](https://github.com/nodejs-private/node-private/pull/284)
- [[`45d419ab1c`](https://github.com/nodejs/node/commit/45d419ab1c)] - **http**: add regression test for chunked smuggling (Matteo Collina) [nodejs-private/node-private#284](https://github.com/nodejs-private/node-private/pull/284)

Windows 32-bit Installer: https://nodejs.org/dist/v16.11.1/node-v16.11.1-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v16.11.1/node-v16.11.1-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v16.11.1/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v16.11.1/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v16.11.1/node-v16.11.1.pkg \
macOS Apple Silicon 64-bit Binary: https://nodejs.org/dist/v16.11.1/node-v16.11.1-darwin-arm64.tar.gz \
macOS Intel 64-bit Binary: https://nodejs.org/dist/v16.11.1/node-v16.11.1-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v16.11.1/node-v16.11.1-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v16.11.1/node-v16.11.1-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v16.11.1/node-v16.11.1-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v16.11.1/node-v16.11.1-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v16.11.1/node-v16.11.1-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v16.11.1/node-v16.11.1-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v16.11.1/node-v16.11.1.tar.gz \
Other release files: https://nodejs.org/dist/v16.11.1/ \
Documentation: https://nodejs.org/docs/v16.11.1/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

f386076358c470150cd8ad5cc18f008ec51d146ad2e66345fcd063f7228a5d95  node-v16.11.1-aix-ppc64.tar.gz
5e772e478390fab3001b7148a923e4f22fca50170000f18b28475337d3a97248  node-v16.11.1-darwin-arm64.tar.gz
aebaf4eed1c2c522800f6c0fab3ae08ffcaf298d05a38ac738aa0047308e8ad7  node-v16.11.1-darwin-arm64.tar.xz
ba54b8ed504bd934d03eb860fefe991419b4209824280d4274f6a911588b5e45  node-v16.11.1-darwin-x64.tar.gz
ca25f6ebb10713cd545998bbc9d3715511e321d304b376574f3dd4ce9b70f75e  node-v16.11.1-darwin-x64.tar.xz
c365177ca0ff05dc59c4e9200186fb1a37ca5e37262dc15b3d9bb2df7b6d0079  node-v16.11.1-headers.tar.gz
33407d35fb23b2f467de27c12442ed50f07b9e9756107b8f6a8686897bd2f5d9  node-v16.11.1-headers.tar.xz
d51b372477287ee41e5bf2d90972868ed28b5c5465bc2df14e86c398926916c1  node-v16.11.1-linux-arm64.tar.gz
083fc51f0ea26de9041aaf9821874651a9fd3b20d1cf57071ce6b523a0436f17  node-v16.11.1-linux-arm64.tar.xz
73f424921efe9f5fe8ce05b047f0553ea794596396e01d68e5468a83b6d99b66  node-v16.11.1-linux-armv7l.tar.gz
1763c8858c0b5151427076db51cbb614dc30c249b09ec4eb80642d67e4cab85c  node-v16.11.1-linux-armv7l.tar.xz
3f6d4f35cb48cce8a88385b6790526f087ca9dbedf092b08ae521fd36e873610  node-v16.11.1-linux-ppc64le.tar.gz
707140addd8be88eb8a3180d68ecda6d443e0a1d19add98008bdc5c6292e475c  node-v16.11.1-linux-ppc64le.tar.xz
07879bca5401e9e9f9979731b10041f3626e4e1d9c1c6313ec132aa85c275d47  node-v16.11.1-linux-s390x.tar.gz
855b5c83c2ccb05273d50bb04376335c68d47df57f3187cdebe1f22b972d2825  node-v16.11.1-linux-s390x.tar.xz
48fba5e9d60e12e777994dafba7b04449c3d0cd004340970fd674220e572a39e  node-v16.11.1-linux-x64.tar.gz
493bcc9b660eff983a6de65a0f032eb2717f57207edf74c745bcb86e360310b3  node-v16.11.1-linux-x64.tar.xz
a23014fcf74c3647b0f80d81399389909e6ec1630909d03a133107370f806320  node-v16.11.1.pkg
8b5a54b284e524984772d34dc2efc68a10fc2f91db11390c6035ef2a9509ef2c  node-v16.11.1.tar.gz
67587f4de25e30a9cc0b51a6033eca3bc82d7b4e0d79bb84a265e88f76ab6278  node-v16.11.1.tar.xz
a5d2683c8a478ed7bf9d6f77449ef03d984345efe75b00130f6f793ed621b6ed  node-v16.11.1-win-x64.7z
4d3c179b82d42e66e321c3948a4e332ed78592917a69d38b86e3a242d7e62fb7  node-v16.11.1-win-x64.zip
388f54bb3a278002735273b4e023462502ce1dcf907736942f9f0bc9a665933d  node-v16.11.1-win-x86.7z
57c1f78814b6cd581b4eadccd41bf88d0787559084d95dd9b504a0fef2aaabfe  node-v16.11.1-win-x86.zip
f712bd1995c2f6b81450433554ae726c0ffa14b5dbbcef8c3462a0b58ac52fe8  node-v16.11.1-x64.msi
f4c40c7b235feefc59d3feda691035a2f62bd9d6989185abb8aacb6e23d955bb  node-v16.11.1-x86.msi
974c1c0dda7a169a9d0b69fae1df16ed3b047dead2bffd9b55ce64743c5f842c  win-x64/node.exe
7c94657df6918a77dc8edefaf3b5415dbfb9eb83a88c17d216a48c8c36fcc58d  win-x64/node.lib
f95025f32ebb5f96a5b24800a99ab6cf09958711e22a56ce1e1c30d8f045bef4  win-x64/node_pdb.7z
fa09b1ccce293b63dfc65cf37d55ff4e43ccf31a068b16186f0a5e200fea96e2  win-x64/node_pdb.zip
758fd1300bfaa62c16d4f29663429255d6d0ff68fde4b6d0d0336b26f216127a  win-x86/node.exe
8090f51a19ff2d5e765920262a4367203be2e69e64ac3725e4e14dd034c98443  win-x86/node.lib
e99cb2f53b864d8d3e98949b9989987a927c4c82cb6cb2a9ceb86ce13fb2a8fd  win-x86/node_pdb.7z
1a7a58d93b3bf788cf34bdd6f8eff44b6dfadaa731d535ef3759da5ea64a42ed  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEdPEmArbxxOkT+qN606iWE2Q7YgEFAmFlqGkACgkQ06iWE2Q7
YgHZ4xAAuLEtlC+h00k3ZK5rJiQRXNBKv1s3TxDMj71n3wnaSID9zBDppu9KDTeb
ygOt1AbWkYvafPUdHngBtaaz64ah2DxRR9SU/fzkLVTVJDJ0g0th6d+S7eQfkvnH
mMY2E1c1XksCrbb9dvtvBXHAc/KE39vpkdNojiOv2kdedd3Mh1bOroqyUBJeJxLo
wK5Il2mI30DfNxM4D7JszNGbzSX3WP5SLt34S4feKWgM8gArqGuTZGp7pjH1lI3v
XXsuule3tDretGS3j0wuxqlc0Ose4OzSq+8jGpCMgquepJw5lZwN0i7EmB1Z/r1v
8b8iWfwWE+6j6QiNhu8bnyrulbwBUq0JitmbiHLlj5vDG1Wh/WE2bDVK6242ZLlJ
/PcN0gpY98ep2ib5MSRUWklm7+diO+BDAcRakDzYDmfSgXuyGjs9gYmuz7+n+2jF
vzWu0Hr5++mWw8DCoRYbH+/20Ki+vAGJRJxO0fHitwCKmpLCoJsVssMuJMklZdIl
n3atg4Fm7jPH7OfH7s41DGCvuf7YDNo5v3bci2XMwFiT7cg131Di5ajdKZi4KDY6
J+OfqwSmWQMSukIwfzVJxu7LCaU4MKHeUzLjMm+mBJow85eIBa0XG4gGsTbluHM2
EuckAiVSB4CwF6QAE+bReVCLYL1JfSKSIJfV6gX8VA+9uZDO7nw=
=9O8j
-----END PGP SIGNATURE-----

```
