---
date: '2015-09-23T02:01:38.545Z'
category: release
title: Node v4.1.1 (Current)
layout: blog-post
author: <PERSON>
---

This release contains some minor security-related updates. You are advised to upgrade to 4.1.1 if you are currently running v4.1.0.

### Notable changes

- **buffer**: Fixed a bug introduced in v4.1.0 where allocating a new zero-length buffer can result in the _next_ allocation of a TypedArray in JavaScript not being zero-filled. In certain circumstances this could result in data leakage via reuse of memory space in TypedArrays, breaking the normally safe assumption that TypedArrays should be always zero-filled. (<PERSON>) [#2931](https://github.com/nodejs/node/pull/2931).
- **http**: Guard against response-splitting of HTTP trailing headers added via [`response.addTrailers()`](https://nodejs.org/api/http.html#http_response_addtrailers_headers) by removing new-line (`[\r\n]`) characters from values. Note that standard header values are already stripped of new-line characters. The expected security impact is low because trailing headers are rarely used. (<PERSON>) [#2945](https://github.com/nodejs/node/pull/2945).
- **npm**: Upgrade to npm 2.14.4 from 2.14.3, see [release notes](https://github.com/npm/npm/releases/tag/v2.14.4) for full details (Kat Marchán) [#2958](https://github.com/nodejs/node/pull/2958)
  - Upgrades `graceful-fs` on multiple dependencies to no longer rely on monkey-patching `fs`
  - Fix `npm link` for pre-release / RC builds of Node
- **v8**: Update post-mortem metadata to allow post-mortem debugging tools to find and inspect:
  - JavaScript objects that use dictionary properties (Julien Gilli) [#2959](https://github.com/nodejs/node/pull/2959)
  - ScopeInfo and thus closures (Julien Gilli) [#2974](https://github.com/nodejs/node/pull/2974)

### Known issues

See https://github.com/nodejs/node/labels/confirmed-bug for complete and current list of known issues.

- Some problems with unreferenced timers running during `beforeExit` are still to be resolved. See [#1264](https://github.com/nodejs/node/issues/1264).
- Surrogate pair in REPL can freeze terminal. [#690](https://github.com/nodejs/node/issues/690)
- Calling `dns.setServers()` while a DNS query is in progress can cause the process to crash on a failed assertion. [#894](https://github.com/nodejs/node/issues/894)
- `url.resolve` may transfer the auth portion of the url when resolving between two full hosts, see [#1435](https://github.com/nodejs/node/issues/1435).

### Commits

- [[`d63e02e08d`](https://github.com/nodejs/node/commit/d63e02e08d)] - **buffer**: don't set zero fill for zero-length buffer (Trevor Norris) [#2931](https://github.com/nodejs/node/pull/2931)
- [[`5905b14bff`](https://github.com/nodejs/node/commit/5905b14bff)] - **build**: fix icutrim when building small-icu on BE (Stewart Addison) [#2602](https://github.com/nodejs/node/pull/2602)
- [[`f010cb5d96`](https://github.com/nodejs/node/commit/f010cb5d96)] - **configure**: detect mipsel host (Jérémy Lal) [#2971](https://github.com/nodejs/node/pull/2971)
- [[`b93ad5abbd`](https://github.com/nodejs/node/commit/b93ad5abbd)] - **deps**: backport 357e6b9 from V8's upstream (Julien Gilli) [#2974](https://github.com/nodejs/node/pull/2974)
- [[`8da3da4d41`](https://github.com/nodejs/node/commit/8da3da4d41)] - **deps**: backport ff7d70b from V8's upstream (Julien Gilli) [#2959](https://github.com/nodejs/node/pull/2959)
- [[`2600fb8ae6`](https://github.com/nodejs/node/commit/2600fb8ae6)] - **deps**: upgraded to node-gyp@3.0.3 in npm (Kat Marchán) [#2958](https://github.com/nodejs/node/pull/2958)
- [[`793aad2d7a`](https://github.com/nodejs/node/commit/793aad2d7a)] - **deps**: upgrade to npm 2.14.4 (Kat Marchán) [#2958](https://github.com/nodejs/node/pull/2958)
- [[`43e2b7f836`](https://github.com/nodejs/node/commit/43e2b7f836)] - **doc**: remove usage of events.EventEmitter (Sakthipriyan Vairamani) [#2921](https://github.com/nodejs/node/pull/2921)
- [[`9c59d2f16a`](https://github.com/nodejs/node/commit/9c59d2f16a)] - **doc**: remove extra using v8::HandleScope statement (Christopher J. Brody) [#2983](https://github.com/nodejs/node/pull/2983)
- [[`f7edbab367`](https://github.com/nodejs/node/commit/f7edbab367)] - **doc**: clarify description of assert.ifError() (Rich Trott) [#2941](https://github.com/nodejs/node/pull/2941)
- [[`b2ddf0f9a2`](https://github.com/nodejs/node/commit/b2ddf0f9a2)] - **doc**: refine process.kill() and exit explanations (Rich Trott) [#2918](https://github.com/nodejs/node/pull/2918)
- [[`f68fed2e6f`](https://github.com/nodejs/node/commit/f68fed2e6f)] - **http**: remove redundant code in \_deferToConnect (Malcolm Ahoy) [#2769](https://github.com/nodejs/node/pull/2769)
- [[`f542e74c93`](https://github.com/nodejs/node/commit/f542e74c93)] - **http**: guard against response splitting in trailers (Ben Noordhuis) [#2945](https://github.com/nodejs/node/pull/2945)
- [[`bc9f629387`](https://github.com/nodejs/node/commit/bc9f629387)] - **http_parser**: do not dealloc during kOnExecute (Fedor Indutny) [#2956](https://github.com/nodejs/node/pull/2956)
- [[`1860e0cebd`](https://github.com/nodejs/node/commit/1860e0cebd)] - **lib,src**: remove usage of events.EventEmitter (Sakthipriyan Vairamani) [#2921](https://github.com/nodejs/node/pull/2921)
- [[`d4cd5ac407`](https://github.com/nodejs/node/commit/d4cd5ac407)] - **readline**: fix tab completion bug (Matt Harrison) [#2816](https://github.com/nodejs/node/pull/2816)
- [[`9760e04839`](https://github.com/nodejs/node/commit/9760e04839)] - **repl**: don't use tty control codes when $TERM is set to "dumb" (Salman Aljammaz) [#2712](https://github.com/nodejs/node/pull/2712)
- [[`cb971cc97d`](https://github.com/nodejs/node/commit/cb971cc97d)] - **repl**: backslash bug fix (Sakthipriyan Vairamani) [#2968](https://github.com/nodejs/node/pull/2968)
- [[`2034f68668`](https://github.com/nodejs/node/commit/2034f68668)] - **src**: honor --abort_on_uncaught_exception flag (Evan Lucas) [#2776](https://github.com/nodejs/node/pull/2776)
- [[`0b1ca4a9ef`](https://github.com/nodejs/node/commit/0b1ca4a9ef)] - **src**: Add ABORT macro (Evan Lucas) [#2776](https://github.com/nodejs/node/pull/2776)
- [[`4519dd00f9`](https://github.com/nodejs/node/commit/4519dd00f9)] - **test**: test sync version of mkdir & rmdir (Sakthipriyan Vairamani) [#2588](https://github.com/nodejs/node/pull/2588)
- [[`816f609c8b`](https://github.com/nodejs/node/commit/816f609c8b)] - **test**: use tmpDir instead of fixtures in readdir (Sakthipriyan Vairamani) [#2587](https://github.com/nodejs/node/pull/2587)
- [[`2084f52585`](https://github.com/nodejs/node/commit/2084f52585)] - **test**: test more http response splitting scenarios (Ben Noordhuis) [#2945](https://github.com/nodejs/node/pull/2945)
- [[`fa08d1d8a1`](https://github.com/nodejs/node/commit/fa08d1d8a1)] - **test**: add test-spawn-cmd-named-pipe (Alexis Campailla) [#2770](https://github.com/nodejs/node/pull/2770)
- [[`71b5d80682`](https://github.com/nodejs/node/commit/71b5d80682)] - **test**: make cluster tests more time tolerant (Michael Dawson) [#2891](https://github.com/nodejs/node/pull/2891)
- [[`3e09dcfc32`](https://github.com/nodejs/node/commit/3e09dcfc32)] - **test**: update cwd-enoent tests for AIX (Imran Iqbal) [#2909](https://github.com/nodejs/node/pull/2909)
- [[`6ea8ec1c59`](https://github.com/nodejs/node/commit/6ea8ec1c59)] - **tools**: single, cross-platform tick processor (Matt Loring) [#2868](https://github.com/nodejs/node/pull/2868)

Windows 32-bit Installer: https://nodejs.org/dist/v4.1.1/node-v4.1.1-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v4.1.1/node-v4.1.1-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v4.1.1/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v4.1.1/win-x64/node.exe \
Mac OS X 64-bit Installer: https://nodejs.org/dist/v4.1.1/node-v4.1.1.pkg \
Mac OS X 64-bit Binary: https://nodejs.org/dist/v4.1.1/node-v4.1.1-darwin-x64.tar.gz \
Linux 32-bit Binary: https://nodejs.org/dist/v4.1.1/node-v4.1.1-linux-x86.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v4.1.1/node-v4.1.1-linux-x64.tar.gz \
SmartOS 32-bit Binary: https://nodejs.org/dist/v4.1.1/node-v4.1.1-sunos-x86.tar.gz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v4.1.1/node-v4.1.1-sunos-x64.tar.gz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v4.1.1/node-v4.1.1-linux-armv6l.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v4.1.1/node-v4.1.1-linux-armv7l.tar.gz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v4.1.1/node-v4.1.1-linux-arm64.tar.gz \
Source Code: https://nodejs.org/dist/v4.1.1/node-v4.1.1.tar.gz \
Other release files: https://nodejs.org/dist/v4.1.1/ \
Documentation: https://nodejs.org/docs/v4.1.1/api/

Shasums (GPG signing hash: SHA512, file hash: SHA256):

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA1

b7e72bf8364c35992a8bebc57bf68c596d622c33d409c0943bf7d24ca7205c76  node-v4.1.1-darwin-x64.tar.gz
ce887b8c4d38fc269d22c072224a13c686445ea4c84ee506d4edd27de169c34c  node-v4.1.1-darwin-x64.tar.xz
d134614e78cfc406611e366c9618704a47c2ee1bf60f0a11909ba84d8b2a9e28  node-v4.1.1-headers.tar.gz
9edcf9fd5c79a3696faef185bc2e9ee51c709dda2381b1bc6deb49f240897e5d  node-v4.1.1-headers.tar.xz
b2e1915a0c65dd9faee7f05a56792371958980e02d1f7cde447c8260bb805052  node-v4.1.1-linux-arm64.tar.gz
63b4705f3ae5ee9f97b319dbc68463c12478fbcfc1bdf654f760a2e5bea565e8  node-v4.1.1-linux-arm64.tar.xz
ca38cef96180916891a262bbb39f335eaa8de6c0c06933609f4f3d7bebdc94b5  node-v4.1.1-linux-armv6l.tar.gz
06eff36b1f65b917ddedd2d6143d56ccc509518ac7cace6375011e2c5a40c226  node-v4.1.1-linux-armv6l.tar.xz
2896f0ab7c53bb7b489a09f7344e059f898ae929c2a9bfb7dfce85a5846ab9d2  node-v4.1.1-linux-armv7l.tar.gz
a88e19a3f6be90c7f93b890b3ef2e91f9563ab0b270f619b0fb78c773771c0af  node-v4.1.1-linux-armv7l.tar.xz
f5f7e11a503c997486d50d8683741a554bdda1d1181125a05ac5844cb29d1572  node-v4.1.1-linux-x64.tar.gz
ffd058c4742c0525cc9d59069f29768096caac6d8d7eac2300d486a7f2d8122e  node-v4.1.1-linux-x64.tar.xz
3f9836b8a7e6e3d6591af6ef59e6055255439420518c3f77e0e65832a8486be1  node-v4.1.1-linux-x86.tar.gz
dc2813fcf233d5fd8a375839757a0225748cf65f3d1027cab6188cd9e99897cf  node-v4.1.1-linux-x86.tar.xz
1d7ee48a3d66d895692ca8085470358306eb11f398564834c3030cf3fe9f77e0  node-v4.1.1.pkg
e1e991519f4147ccef0c1816d26905ccf0a0be094af08d302a63e1025a7369df  node-v4.1.1-sunos-x64.tar.gz
6b0d3278bba8313c7894cf55b755556c549651d0027a3a735114fb99b3afa148  node-v4.1.1-sunos-x64.tar.xz
915ec11b4a64becd817a810b7d8ecb426da3c52465d3ac3dfae50b53ad1ea28c  node-v4.1.1-sunos-x86.tar.gz
ef71fbfa086a5d6929f8a7cac0addb99d9c4f5a1f9caa889aecb1e5a980b4449  node-v4.1.1-sunos-x86.tar.xz
6a610935ff52de713cf2af6a26002322e24fd7933a444436f0817a2b84e15a58  node-v4.1.1.tar.gz
f7ca9ceb0b7cc49b12f28a652c908a1f0ffbf34cec73ad0805fe717b14996bb9  node-v4.1.1.tar.xz
04b65daa09c1daff6d0a4101a3256d18eb9d5b50ba3ba49184b5b032dd9a4c06  node-v4.1.1-x64.msi
e73db653f543e3f6bcd28451d82e491064405b70546849579b31587f74b1a504  node-v4.1.1-x86.msi
9e985444df6374fb9efaa8c43630a26ca4fc77dcdcb5564abf7c30a62033dd53  win-x64/node.exe
e416599fb719d32d88e5e1abb27d1225c65bea452d8f11d1608e6a2c91c7695c  win-x64/node.lib
8fe8b23e11e6356b6ab50f18060939c3e7a9f56d8ca2189fc556c8185f1a5083  win-x86/node.exe
e2a6a441e26cd60043f7537552fd10a3f678bc9265af539256410c6da2a0e9b4  win-x86/node.lib
-----BEGIN PGP SIGNATURE-----
Version: GnuPG v1

iQEcBAEBAgAGBQJWA1eGAAoJEMJzeS99g1Rd/uMH+wT/zvf2BLZjyd95XvmgZ3yx
nCwZjGRLUfsMPd9Hk5x0D6Wpjq7hpIcN06W3ea6t7zgiZ/yCHBRfZ8KZHUkHP+Tm
/yNQwpZajEIL1RGssx/Wm1VMB3sysAl3RZ665OtvpuBgQ0w6PKNqB+WJG8G/1GSd
lB1sVYCq+CagjknPUMM+tYnxGDzSnJRcKdGI3DVvAu57AHdsYmuEfVxic2jRF1m+
yB2ncABRXYqcELt6U293B82Lr3zBYUd8gcBd2VzgOUSmMZh0YlqgPKt2Ll5/fEnY
fe3ditIQsQTiWtKzXr++Hd9iD2B+ppL3XBbiDByKFznIHg4BR61l8OuotHoL334=
=Oh4x
-----END PGP SIGNATURE-----

```
