---
date: '2020-04-30T13:54:45.549Z'
category: release
title: Node v13.14.0 (Current)
layout: blog-post
author: <PERSON><PERSON>
---

### Notable Changes

- **async_hooks**:
  - Merge `run` and `exit` methods (<PERSON><PERSON>) [#31950](https://github.com/nodejs/node/pull/31950)
  - Prevent sync methods of async storage exiting outer context (<PERSON>) [#31950](https://github.com/nodejs/node/pull/31950)
- **vm**:
  - Add `importModuleDynamically` option to compileFunction (<PERSON>) [#32985](https://github.com/nodejs/node/pull/32985)

#### New core collaborators

With this release, we welcome two new Node.js core collaborators:

- <PERSON> @juanarbol [#32906](https://github.com/nodejs/node/pull/32906)
- <PERSON><PERSON> @puzpuzpuz [#32817](https://github.com/nodejs/node/pull/32817)

### Commits

- [[`52d8afc07e`](https://github.com/nodejs/node/commit/52d8afc07e)] - **(SEMVER-MINOR)** **async_hooks**: merge run and exit methods (Andrey Pechkurov) [#31950](https://github.com/nodejs/node/pull/31950)
- [[`b304df97ff`](https://github.com/nodejs/node/commit/b304df97ff)] - **(SEMVER-MINOR)** **async_hooks**: prevent sync methods of async storage exiting outer context (Stephen Belanger) [#31950](https://github.com/nodejs/node/pull/31950)
- [[`a1178b6c5b`](https://github.com/nodejs/node/commit/a1178b6c5b)] - **buffer**: add type check in bidirectionalIndexOf (Gerhard Stoebich) [#32770](https://github.com/nodejs/node/pull/32770)
- [[`d9378747ae`](https://github.com/nodejs/node/commit/d9378747ae)] - **buffer**: mark pool ArrayBuffer as untransferable (Anna Henningsen) [#32759](https://github.com/nodejs/node/pull/32759)
- [[`9bcfc8ed58`](https://github.com/nodejs/node/commit/9bcfc8ed58)] - **buffer,n-api**: fix double ArrayBuffer::Detach() during cleanup (Anna Henningsen) [#33039](https://github.com/nodejs/node/pull/33039)
- [[`606719fa16`](https://github.com/nodejs/node/commit/606719fa16)] - **build**: fix vcbuild error for missing Visual Studio (Thomas) [#32658](https://github.com/nodejs/node/pull/32658)
- [[`832ea520be`](https://github.com/nodejs/node/commit/832ea520be)] - **build**: remove .git folders when testing V8 (Richard Lau) [#32877](https://github.com/nodejs/node/pull/32877)
- [[`e1809c8d71`](https://github.com/nodejs/node/commit/e1809c8d71)] - **build**: add configure flag to build V8 with DCHECKs (Anna Henningsen) [#32787](https://github.com/nodejs/node/pull/32787)
- [[`5c4d8cd72b`](https://github.com/nodejs/node/commit/5c4d8cd72b)] - **build**: re-enable ASAN Action using clang (Matheus Marchini) [#32776](https://github.com/nodejs/node/pull/32776)
- [[`c8d43604e7`](https://github.com/nodejs/node/commit/c8d43604e7)] - **build**: use same flags as V8 for ASAN (Matheus Marchini) [#32776](https://github.com/nodejs/node/pull/32776)
- [[`c6078f0ca1`](https://github.com/nodejs/node/commit/c6078f0ca1)] - **build**: add build from tarball (John Kleinschmidt) [#32129](https://github.com/nodejs/node/pull/32129)
- [[`8fb7852e0b`](https://github.com/nodejs/node/commit/8fb7852e0b)] - **cli, report**: move --report-on-fatalerror to stable (cjihrig) [#32496](https://github.com/nodejs/node/pull/32496)
- [[`98a2c67a50`](https://github.com/nodejs/node/commit/98a2c67a50)] - **cluster**: removed unused addressType argument from constructor (Yash Ladha) [#32963](https://github.com/nodejs/node/pull/32963)
- [[`7b630aea32`](https://github.com/nodejs/node/commit/7b630aea32)] - **deps**: update archs files for OpenSSL-1.1.1g (Hassaan Pasha) [#32971](https://github.com/nodejs/node/pull/32971)
- [[`7940d2ca86`](https://github.com/nodejs/node/commit/7940d2ca86)] - **deps**: upgrade openssl sources to 1.1.1g (Hassaan Pasha) [#32971](https://github.com/nodejs/node/pull/32971)
- [[`3956ab5187`](https://github.com/nodejs/node/commit/3956ab5187)] - **deps**: V8: backport 3f8dc4b2e5ba (Ujjwal Sharma) [#32993](https://github.com/nodejs/node/pull/32993)
- [[`1a82b78bda`](https://github.com/nodejs/node/commit/1a82b78bda)] - **deps**: V8: cherry-pick e1eac1b16c96 (Milad Farazmand) [#32974](https://github.com/nodejs/node/pull/32974)
- [[`afe7f41442`](https://github.com/nodejs/node/commit/afe7f41442)] - **deps**: upgrade to libuv 1.37.0 (cjihrig) [#32866](https://github.com/nodejs/node/pull/32866)
- [[`771ca7d4ed`](https://github.com/nodejs/node/commit/771ca7d4ed)] - **deps**: upgrade to libuv 1.36.0 (cjihrig) [#32866](https://github.com/nodejs/node/pull/32866)
- [[`ea857684e9`](https://github.com/nodejs/node/commit/ea857684e9)] - **deps**: V8: cherry-pick dc3a90be6ca7 (Michaël Zasso) [#32795](https://github.com/nodejs/node/pull/32795)
- [[`fc9191ad58`](https://github.com/nodejs/node/commit/fc9191ad58)] - **doc**: assign missing deprecation code (Richard Lau) [#33109](https://github.com/nodejs/node/pull/33109)
- [[`ea67a3097d`](https://github.com/nodejs/node/commit/ea67a3097d)] - **doc**: improve WHATWG url constructor code example (Liran Tal) [#32782](https://github.com/nodejs/node/pull/32782)
- [[`7085e6f7b8`](https://github.com/nodejs/node/commit/7085e6f7b8)] - **doc**: make openssl maintenance position independent (Sam Roberts) [#32977](https://github.com/nodejs/node/pull/32977)
- [[`c489a7e62b`](https://github.com/nodejs/node/commit/c489a7e62b)] - **doc**: improve release documentation (Michaël Zasso) [#33042](https://github.com/nodejs/node/pull/33042)
- [[`16bd3006f1`](https://github.com/nodejs/node/commit/16bd3006f1)] - **doc**: document major finished changes in v14 (Robert Nagy) [#33065](https://github.com/nodejs/node/pull/33065)
- [[`7719f525ab`](https://github.com/nodejs/node/commit/7719f525ab)] - **doc**: add documentation for transferList arg at worker threads (Juan José Arboleda) [#32881](https://github.com/nodejs/node/pull/32881)
- [[`84b12b681a`](https://github.com/nodejs/node/commit/84b12b681a)] - **doc**: avoid tautology in README (Ishaan Jain) [#33005](https://github.com/nodejs/node/pull/33005)
- [[`50c6aa6dc5`](https://github.com/nodejs/node/commit/50c6aa6dc5)] - **doc**: updated directory entry information (Eileen) [#32791](https://github.com/nodejs/node/pull/32791)
- [[`1b61e56538`](https://github.com/nodejs/node/commit/1b61e56538)] - **doc**: ignore no-literal-urls in README (Nick Schonning) [#32676](https://github.com/nodejs/node/pull/32676)
- [[`e9b59781c3`](https://github.com/nodejs/node/commit/e9b59781c3)] - **doc**: convert bare email addresses to mailto links (Nick Schonning) [#32676](https://github.com/nodejs/node/pull/32676)
- [[`9af2eb3b64`](https://github.com/nodejs/node/commit/9af2eb3b64)] - **doc**: ignore no-literal-urls in changelogs (Nick Schonning) [#32676](https://github.com/nodejs/node/pull/32676)
- [[`1b325f525c`](https://github.com/nodejs/node/commit/1b325f525c)] - **doc**: add angle brackets around implicit links (Nick Schonning) [#32676](https://github.com/nodejs/node/pull/32676)
- [[`99f4af4190`](https://github.com/nodejs/node/commit/99f4af4190)] - **doc**: remove repeated word in modules.md (Prosper Opara) [#32931](https://github.com/nodejs/node/pull/32931)
- [[`287bd8af9b`](https://github.com/nodejs/node/commit/287bd8af9b)] - **doc**: elevate diagnostic report to tier1 (Gireesh Punathil) [#32732](https://github.com/nodejs/node/pull/32732)
- [[`8c48d16691`](https://github.com/nodejs/node/commit/8c48d16691)] - **doc**: fix typo in security-release-process.md (Edward Elric) [#32926](https://github.com/nodejs/node/pull/32926)
- [[`faeb4088fa`](https://github.com/nodejs/node/commit/faeb4088fa)] - **doc**: corrected ERR_SOCKET_CANNOT_SEND message (William Armiros) [#32847](https://github.com/nodejs/node/pull/32847)
- [[`76e960c67f`](https://github.com/nodejs/node/commit/76e960c67f)] - **doc**: fix usage of folder and directory terms in fs.md (karan singh virdi) [#32919](https://github.com/nodejs/node/pull/32919)
- [[`c5967596c0`](https://github.com/nodejs/node/commit/c5967596c0)] - **doc**: fix typo in zlib.md (雨夜带刀) [#32901](https://github.com/nodejs/node/pull/32901)
- [[`8c1a69c1e7`](https://github.com/nodejs/node/commit/8c1a69c1e7)] - **doc**: synch SECURITY.md with website (Rich Trott) [#32903](https://github.com/nodejs/node/pull/32903)
- [[`43adbe6bc8`](https://github.com/nodejs/node/commit/43adbe6bc8)] - **doc**: add `tsc-agenda` to onboarding labels list (Rich Trott) [#32832](https://github.com/nodejs/node/pull/32832)
- [[`45a125cf3a`](https://github.com/nodejs/node/commit/45a125cf3a)] - **doc**: add N-API version 6 to table (Michael Dawson) [#32829](https://github.com/nodejs/node/pull/32829)
- [[`cc4764579b`](https://github.com/nodejs/node/commit/cc4764579b)] - **doc**: add juanarbol as collaborator (Juan José Arboleda) [#32906](https://github.com/nodejs/node/pull/32906)
- [[`5dba49db7c`](https://github.com/nodejs/node/commit/5dba49db7c)] - **doc**: missing brackets (William Bonawentura) [#32657](https://github.com/nodejs/node/pull/32657)
- [[`7980f6f749`](https://github.com/nodejs/node/commit/7980f6f749)] - **doc**: improve consistency in usage of NULL (Michael Dawson) [#32726](https://github.com/nodejs/node/pull/32726)
- [[`3f4bb8d67f`](https://github.com/nodejs/node/commit/3f4bb8d67f)] - **doc**: improve net docs (Robert Nagy) [#32811](https://github.com/nodejs/node/pull/32811)
- [[`b7da58773c`](https://github.com/nodejs/node/commit/b7da58773c)] - **doc**: note that signatures of binary may be from subkeys (Reşat SABIQ) [#32591](https://github.com/nodejs/node/pull/32591)
- [[`ae034c4ab2`](https://github.com/nodejs/node/commit/ae034c4ab2)] - **doc**: add transform stream destroy() return value (cjihrig) [#32788](https://github.com/nodejs/node/pull/32788)
- [[`a0be60e3ad`](https://github.com/nodejs/node/commit/a0be60e3ad)] - **doc**: updated guidance for n-api changes (Michael Dawson) [#32721](https://github.com/nodejs/node/pull/32721)
- [[`95cd771f9b`](https://github.com/nodejs/node/commit/95cd771f9b)] - **doc**: remove warning from `response.writeHead` (Cecchi MacNaughton) [#32700](https://github.com/nodejs/node/pull/32700)
- [[`c0e4ac495a`](https://github.com/nodejs/node/commit/c0e4ac495a)] - **doc**: improve AsyncLocalStorage sample (Andrey Pechkurov) [#32757](https://github.com/nodejs/node/pull/32757)
- [[`ea09c0f111`](https://github.com/nodejs/node/commit/ea09c0f111)] - **doc**: document `buffer.from` returns internal pool buffer (Harshitha KP) [#32703](https://github.com/nodejs/node/pull/32703)
- [[`19961988d3`](https://github.com/nodejs/node/commit/19961988d3)] - **doc**: add puzpuzpuz to collaborators (Andrey Pechkurov) [#32817](https://github.com/nodejs/node/pull/32817)
- [[`27837fe4f6`](https://github.com/nodejs/node/commit/27837fe4f6)] - **fs**: update validateOffsetLengthRead in utils.js (daemon1024) [#32896](https://github.com/nodejs/node/pull/32896)
- [[`04b1f63b72`](https://github.com/nodejs/node/commit/04b1f63b72)] - **fs**: extract kWriteFileMaxChunkSize constant (rickyes) [#32640](https://github.com/nodejs/node/pull/32640)
- [[`0b2cff28b9`](https://github.com/nodejs/node/commit/0b2cff28b9)] - **fs**: remove unnecessary else statement (Jesus Hernandez) [#32662](https://github.com/nodejs/node/pull/32662)
- [[`8774cb4a86`](https://github.com/nodejs/node/commit/8774cb4a86)] - **fs**: use finished over destroy w/ cb (Robert Nagy) [#32809](https://github.com/nodejs/node/pull/32809)
- [[`4d9e69d07d`](https://github.com/nodejs/node/commit/4d9e69d07d)] - **http**: doc deprecate abort and improve docs (Robert Nagy) [#32807](https://github.com/nodejs/node/pull/32807)
- [[`85b333b8f8`](https://github.com/nodejs/node/commit/85b333b8f8)] - **http**: refactor agent 'free' handler (Robert Nagy) [#32801](https://github.com/nodejs/node/pull/32801)
- [[`a673c8fe35`](https://github.com/nodejs/node/commit/a673c8fe35)] - **http2**: wait for secureConnect before initializing (bcoe) [#32958](https://github.com/nodejs/node/pull/32958)
- [[`fce8c4e0d9`](https://github.com/nodejs/node/commit/fce8c4e0d9)] - **inspector**: only write coverage in fully bootstrapped Environments (Joyee Cheung) [#32960](https://github.com/nodejs/node/pull/32960)
- [[`ee3c461a26`](https://github.com/nodejs/node/commit/ee3c461a26)] - **lib**: unnecessary const assignment for class (Yash Ladha) [#32962](https://github.com/nodejs/node/pull/32962)
- [[`944dceb618`](https://github.com/nodejs/node/commit/944dceb618)] - **lib**: simplify function process.emitWarning (himself65) [#32992](https://github.com/nodejs/node/pull/32992)
- [[`8a85afabba`](https://github.com/nodejs/node/commit/8a85afabba)] - **lib**: remove unnecesary else block (David Daza) [#32644](https://github.com/nodejs/node/pull/32644)
- [[`83f1e98a8e`](https://github.com/nodejs/node/commit/83f1e98a8e)] - **lib**: created isValidCallback helper (Yash Ladha) [#32665](https://github.com/nodejs/node/pull/32665)
- [[`636267045e`](https://github.com/nodejs/node/commit/636267045e)] - **module**: refactor condition (Myles Borins) [#32989](https://github.com/nodejs/node/pull/32989)
- [[`cb93c60e64`](https://github.com/nodejs/node/commit/cb93c60e64)] - **module**: exports not exported for null resolutions (Guy Bedford) [#32838](https://github.com/nodejs/node/pull/32838)
- [[`e540d5cd9b`](https://github.com/nodejs/node/commit/e540d5cd9b)] - **module**: improve error for invalid package targets (Myles Borins) [#32052](https://github.com/nodejs/node/pull/32052)
- [[`4432bb2415`](https://github.com/nodejs/node/commit/4432bb2415)] - **module**: partial doc removal of --experimental-modules (Myles Borins) [#32915](https://github.com/nodejs/node/pull/32915)
- [[`0c7391c9b8`](https://github.com/nodejs/node/commit/0c7391c9b8)] - **module**: remove experimental modules warning (Guy Bedford) [#31974](https://github.com/nodejs/node/pull/31974)
- [[`520347c198`](https://github.com/nodejs/node/commit/520347c198)] - **module**: fix memory leak when require error occurs (Qinhui Chen) [#32837](https://github.com/nodejs/node/pull/32837)
- [[`48a72bf7eb`](https://github.com/nodejs/node/commit/48a72bf7eb)] - **n-api**: fix false assumption on napi_async_context structures (legendecas) [#32928](https://github.com/nodejs/node/pull/32928)
- [[`7bd51fb8af`](https://github.com/nodejs/node/commit/7bd51fb8af)] - **perf_hooks**: remove unnecessary assignment when name is undefined (rickyes) [#32910](https://github.com/nodejs/node/pull/32910)
- [[`3b590d4f17`](https://github.com/nodejs/node/commit/3b590d4f17)] - **process**: suggest --trace-warnings when printing warning (Anna Henningsen) [#32797](https://github.com/nodejs/node/pull/32797)
- [[`c318a52e95`](https://github.com/nodejs/node/commit/c318a52e95)] - **src**: add AsyncWrapObject constructor template factory (Stephen Belanger) [#33051](https://github.com/nodejs/node/pull/33051)
- [[`44a5b73421`](https://github.com/nodejs/node/commit/44a5b73421)] - **src**: do not compare against wide characters (Christopher Beeson) [#32921](https://github.com/nodejs/node/pull/32921)
- [[`02653b8310`](https://github.com/nodejs/node/commit/02653b8310)] - **src**: fix empty-named env var assertion failure (Christopher Beeson) [#32921](https://github.com/nodejs/node/pull/32921)
- [[`2264b564dc`](https://github.com/nodejs/node/commit/2264b564dc)] - **src**: assignment to valid type (Yash Ladha) [#32879](https://github.com/nodejs/node/pull/32879)
- [[`d3f65e8e15`](https://github.com/nodejs/node/commit/d3f65e8e15)] - **src**: delete MicroTaskPolicy namespace (Juan José Arboleda) [#32853](https://github.com/nodejs/node/pull/32853)
- [[`015f33cf55`](https://github.com/nodejs/node/commit/015f33cf55)] - **src**: use using NewStringType (rickyes) [#32843](https://github.com/nodejs/node/pull/32843)
- [[`0fdc55f51b`](https://github.com/nodejs/node/commit/0fdc55f51b)] - **src**: fix null deref in AllocatedBuffer::clear (Matt Kulukundis) [#32892](https://github.com/nodejs/node/pull/32892)
- [[`c1f54c7313`](https://github.com/nodejs/node/commit/c1f54c7313)] - **src**: remove validation of unreachable code (Juan José Arboleda) [#32818](https://github.com/nodejs/node/pull/32818)
- [[`e529a32f07`](https://github.com/nodejs/node/commit/e529a32f07)] - **src**: elevate v8 namespaces (Nimit) [#32872](https://github.com/nodejs/node/pull/32872)
- [[`9fd0c3528a`](https://github.com/nodejs/node/commit/9fd0c3528a)] - **src**: remove redundant v8::HeapSnapshot namespace (Juan José Arboleda) [#32854](https://github.com/nodejs/node/pull/32854)
- [[`a72d1d3ad6`](https://github.com/nodejs/node/commit/a72d1d3ad6)] - **src**: remove unused using in node_worker.cc (Daniel Bevenius) [#32840](https://github.com/nodejs/node/pull/32840)
- [[`5b01772282`](https://github.com/nodejs/node/commit/5b01772282)] - **src**: use basename(argv0) for --trace-uncaught suggestion (Anna Henningsen) [#32798](https://github.com/nodejs/node/pull/32798)
- [[`2f7e372077`](https://github.com/nodejs/node/commit/2f7e372077)] - **src**: ignore GCC -Wcast-function-type for v8.h (Daniel Bevenius) [#32679](https://github.com/nodejs/node/pull/32679)
- [[`bff11a9cd0`](https://github.com/nodejs/node/commit/bff11a9cd0)] - **src**: remove unused v8 Array namespace (Juan José Arboleda) [#32749](https://github.com/nodejs/node/pull/32749)
- [[`507240cec7`](https://github.com/nodejs/node/commit/507240cec7)] - **stream**: close iterator in Readable.from (Vadzim Zieńka) [#32844](https://github.com/nodejs/node/pull/32844)
- [[`b36eb756e7`](https://github.com/nodejs/node/commit/b36eb756e7)] - **stream**: inline unbuffered \_write (Robert Nagy) [#32886](https://github.com/nodejs/node/pull/32886)
- [[`780c0efc70`](https://github.com/nodejs/node/commit/780c0efc70)] - **test**: refactor test-async-hooks-constructor (himself65) [#33063](https://github.com/nodejs/node/pull/33063)
- [[`5bdb401801`](https://github.com/nodejs/node/commit/5bdb401801)] - **test**: remove timers-blocking-callback (Jeremiah Senkpiel) [#32870](https://github.com/nodejs/node/pull/32870)
- [[`f658cb8dc4`](https://github.com/nodejs/node/commit/f658cb8dc4)] - **test**: better error validations for event-capture (Adrian Estrada) [#32771](https://github.com/nodejs/node/pull/32771)
- [[`2c943358b2`](https://github.com/nodejs/node/commit/2c943358b2)] - **test**: refactor events tests for invalid listeners (Adrian Estrada) [#32769](https://github.com/nodejs/node/pull/32769)
- [[`e6e0647709`](https://github.com/nodejs/node/commit/e6e0647709)] - **test**: test-async-wrap-constructor prefer forEach (Daniel Estiven Rico Posada) [#32631](https://github.com/nodejs/node/pull/32631)
- [[`944e010324`](https://github.com/nodejs/node/commit/944e010324)] - **test**: mark test-child-process-fork-args as flaky on Windows (Andrey Pechkurov) [#32950](https://github.com/nodejs/node/pull/32950)
- [[`87149c4b22`](https://github.com/nodejs/node/commit/87149c4b22)] - **test**: changed function to arrow function (Nimit) [#32875](https://github.com/nodejs/node/pull/32875)
- [[`4baf41f15e`](https://github.com/nodejs/node/commit/4baf41f15e)] - **test**: replace console.log/error() with debuglog (daemon1024) [#32692](https://github.com/nodejs/node/pull/32692)
- [[`740f86409d`](https://github.com/nodejs/node/commit/740f86409d)] - **test**: only detect uname on supported os (Xu Meng) [#32833](https://github.com/nodejs/node/pull/32833)
- [[`23a4d60448`](https://github.com/nodejs/node/commit/23a4d60448)] - **test**: mark cpu-prof-dir-worker flaky on all (Sam Roberts) [#32828](https://github.com/nodejs/node/pull/32828)
- [[`46cafadeac`](https://github.com/nodejs/node/commit/46cafadeac)] - **test**: replace equal with strictEqual (Jesus Hernandez) [#32727](https://github.com/nodejs/node/pull/32727)
- [[`edc10d4fa6`](https://github.com/nodejs/node/commit/edc10d4fa6)] - **test**: mark test-worker-prof flaky on arm (Sam Roberts) [#32826](https://github.com/nodejs/node/pull/32826)
- [[`98db564f4b`](https://github.com/nodejs/node/commit/98db564f4b)] - **test**: mark test-http2-reset-flood flaky on all (Sam Roberts) [#32825](https://github.com/nodejs/node/pull/32825)
- [[`f1273e8e87`](https://github.com/nodejs/node/commit/f1273e8e87)] - **test**: cover node entry type in perf_hooks (Julian Duque) [#32751](https://github.com/nodejs/node/pull/32751)
- [[`f4e9bd6d36`](https://github.com/nodejs/node/commit/f4e9bd6d36)] - **test**: use symlinks to copy shells (John Kleinschmidt) [#32129](https://github.com/nodejs/node/pull/32129)
- [[`efb3c71fea`](https://github.com/nodejs/node/commit/efb3c71fea)] - **tls**: add highWaterMark option for connect (rickyes) [#32786](https://github.com/nodejs/node/pull/32786)
- [[`bfa19c47a4`](https://github.com/nodejs/node/commit/bfa19c47a4)] - **tls**: move getAllowUnauthorized to internal/options (James M Snell) [#32917](https://github.com/nodejs/node/pull/32917)
- [[`1436f5359c`](https://github.com/nodejs/node/commit/1436f5359c)] - **tls**: provide default cipher list from command line (Anna Henningsen) [#32760](https://github.com/nodejs/node/pull/32760)
- [[`c402edd60f`](https://github.com/nodejs/node/commit/c402edd60f)] - **tools**: remove unused code in doc generation tool (Rich Trott) [#32913](https://github.com/nodejs/node/pull/32913)
- [[`f7b25c0069`](https://github.com/nodejs/node/commit/f7b25c0069)] - **tools**: decrease timeout in test.py (Anna Henningsen) [#32868](https://github.com/nodejs/node/pull/32868)
- [[`a3aa71a79e`](https://github.com/nodejs/node/commit/a3aa71a79e)] - **util,readline**: NFC-normalize strings before getStringWidth (Anna Henningsen) [#33052](https://github.com/nodejs/node/pull/33052)
- [[`84fd829b45`](https://github.com/nodejs/node/commit/84fd829b45)] - **(SEMVER-MINOR)** **vm**: add importModuleDynamically option to compileFunction (Gus Caplan) [#32985](https://github.com/nodejs/node/pull/32985)
- [[`f14916ffc9`](https://github.com/nodejs/node/commit/f14916ffc9)] - **worker**: fix process.env var empty key access (Christopher Beeson) [#32921](https://github.com/nodejs/node/pull/32921)
- [[`b80b08fe35`](https://github.com/nodejs/node/commit/b80b08fe35)] - **worker**: fix type check in receiveMessageOnPort (Anna Henningsen) [#32745](https://github.com/nodejs/node/pull/32745)

Windows 32-bit Installer: https://nodejs.org/dist/v13.14.0/node-v13.14.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v13.14.0/node-v13.14.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v13.14.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v13.14.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v13.14.0/node-v13.14.0.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v13.14.0/node-v13.14.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v13.14.0/node-v13.14.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v13.14.0/node-v13.14.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v13.14.0/node-v13.14.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v13.14.0/node-v13.14.0-aix-ppc64.tar.gz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v13.14.0/node-v13.14.0-sunos-x64.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v13.14.0/node-v13.14.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v13.14.0/node-v13.14.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v13.14.0/node-v13.14.0.tar.gz \
Other release files: https://nodejs.org/dist/v13.14.0/ \
Documentation: https://nodejs.org/docs/v13.14.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

a1c87a6262d65853d03a84c2037d5989b664f8e06a1ad3d0c7e5a666a3efb18a  node-v13.14.0-aix-ppc64.tar.gz
a56eb353fecd45f731d74fc77c58dde052320c1bc272de9b03151fbaf962feaf  node-v13.14.0-darwin-x64.tar.gz
f077335619b32a0f26395eb4f3761920884a9d1437662d36be23e31e14fe7094  node-v13.14.0-darwin-x64.tar.xz
29d5680aa65c6dfbbf89777550c49fb0731d55aaecf3e9b22b2e6d523ed82d18  node-v13.14.0-headers.tar.gz
e95f09c4f1d5ea84ec29f6c77ef3dbab530a1fc5d1485103016e1b6fadd6163f  node-v13.14.0-headers.tar.xz
4603cc724f3f0551407f1ea41b8fbae83492e80c02d16360cb9722500364f447  node-v13.14.0-linux-arm64.tar.gz
e4736df097846bbe8195a185cc67ada3410a1f8993949e516bbf62b62198d2a7  node-v13.14.0-linux-arm64.tar.xz
d676a6ca4725a70abceda0eb45adcb0c94bb6b0899d76e511d1712e35c88288a  node-v13.14.0-linux-armv7l.tar.gz
76fe76fa095c2be3a25081fac970376a9bde3144dd0790318853a4e33dc9d7e8  node-v13.14.0-linux-armv7l.tar.xz
61649b0c2cd00e034d7aa8339c9529caeabd1db55c91a43d670eeb8944caf303  node-v13.14.0-linux-ppc64le.tar.gz
aff9fef48a31badbc753e0bfea64a070297130c507e0319f3a9ff559d2112f84  node-v13.14.0-linux-ppc64le.tar.xz
df6e7bccd744b405d9d6f84a254b0c0140dd4b2673ef553fb48386836e27c1ce  node-v13.14.0-linux-s390x.tar.gz
5db0d9d758bd3c5e6aaf4fe900da06593c272975fc0666bfee525eedd7d4e0c8  node-v13.14.0-linux-s390x.tar.xz
230717f6e14f4acbb0846d58c224be6acb8da59b0db1de52c77d2bf90315cfaa  node-v13.14.0-linux-x64.tar.gz
9ab808ba3872d58e827ea60a6e43a352f59361bc8eb36fe0327a587086b466f9  node-v13.14.0-linux-x64.tar.xz
b1dec375d3de1d25fde4b27fcd431719714942cf23d5ebf3d05c3f9937a1a20c  node-v13.14.0-sunos-x64.tar.gz
d460e742b46c1332a33b04549df912f9abdc470b581fe6cbc75832df189dd5fb  node-v13.14.0-sunos-x64.tar.xz
78ef76daa966f88893d17ffc49f0157700309001021e4aac158b06ae72007b11  node-v13.14.0-win-x64.7z
8c6e77fac5e911a2e70b6ca34804b3b59b6a3c685ab4e3b17756397df86063fa  node-v13.14.0-win-x64.zip
88919861e56a193a5cd2c2185d383b9934638318b04a206189fefe8bbca8837f  node-v13.14.0-win-x86.7z
a5209ca2645cbbbdfb1200a72d161987cb9515cdab610adf2aac1f30dc814cd9  node-v13.14.0-win-x86.zip
4413ade3aa25c2efec47e6b2819455a7bcda86c0b9a8d245748280549c05b103  node-v13.14.0-x64.msi
3ee8964e399e98ec4f6a430922c1713041251e4f7c8a24285d9cc2d36bab0b15  node-v13.14.0-x86.msi
04c843e285300d2d1c1e6ca03cd2298a7dc43e6e96c5b9592a2e409d117d9e52  node-v13.14.0.pkg
fb69f44071a4c6728fc7e0d20c679ff4359e082a5738f5268d5b4e646cbd9491  node-v13.14.0.tar.gz
6e03cd241c3e4c2c5cea333f8e39e1b266f390d24d1a232abf386a9e12b529be  node-v13.14.0.tar.xz
8468f81ef779568e6c9f03f582ac90ce4d68364f8a01839457c0b05b8cdf1bef  win-x64/node.exe
691e787fedbb32cf77b29e962d3a765027dc3bfa09ce7ee629d0663f1c4acd23  win-x64/node.lib
5a2d657fbe0d719e021bbd4c7c8c16c6c424ba9a47325ac987ddcd1c4b3b2247  win-x64/node_pdb.7z
aca067f12de1d1533ea566f470a0e5e4a16d8bafe0d6acfd76e495311256d89b  win-x64/node_pdb.zip
1eea7bd375eb6fb40f7d1ee8dc917583d03e8f7ebf02e7ceccc2322fbe5d15e1  win-x86/node.exe
a942e6093ed127cc6e1a9bf6e2994e0db0d30f0db6320b8a52ec56c53b504a3c  win-x86/node.lib
b8f6d408938dad61f50657a42f5f0e363b3c3176eb8f052279a9a40c64bcc768  win-x86/node_pdb.7z
03c27fbe076d8bdcd8b74909e05af1324ff0375633c56b9c5bc8b9965bce1080  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEpIwr7mgOhBYyzU5E8HSWs+s8F2IFAl6p/8QACgkQ8HSWs+s8
F2KBChAAoduhJlXNnHKIw8cspvOJ12KbBwRSvj/QCu98JS0PEUbfIHWBQhH6hGuL
8IetcGUNU84pdXVf6sDesREsP/xAM3TigItesxZ7zSI3bosE551vtRh62Ok5cfaX
hh7T4rvdZv2Q4hxbgcy3GQGjaSwjkpTZ12DK4N+Tu0JTyDBNo/EvywqUEw9v7OXN
rINMsktCxzht8HXZL5AZeX2UVcghI9WiQeTlySGCrH8aHHEStGf6YgsfXzu+AIj7
qcuFJpLgoNZM51Nb0FekN8ko4T3eqtj/UiqM+NtbBAhgx/mTuLzXGLQ9vArx1qXH
me7xKz87JFYDdAyFfu1aWdWRNYGytr5fpjRL7o7Pi/69j1uZ/zWbNzBL+NPWWpa1
w6Xr9j0c/Kh1cXlDz2o4/k+UBz83H0e9qnRDy2LL/tkekit56qLYvL2u/IWydOs7
ePcAcSEQdO8VwviPBfxKx42xQW0YmH1v6e1a6aew/1lE/mFve9g8B2EbB2rSHTd9
98vCUHn7EU9lFLZr1fXnZah3BhQNRPXk7tCncSRsXe9OF4frAq5rzOd1FWcqhDhM
Ikj3629qvVcnRSfKI0PPg9EImGlmOpsQqYE2IicJARJh/VoyWbjWM30ey+nHrFfM
v694UA5ASlz5mSMZXnlIR3dSe4RFqBQojnmigMSPMnVPeOWyKvo=
=eVzY
-----END PGP SIGNATURE-----

```
