---
date: '2017-09-05T19:51:32.723Z'
category: release
title: Node v6.11.3 (LTS)
layout: blog-post
author: <PERSON><PERSON>
---

This LTS release comes with 152 commits. This includes 75 which are test related,
25 which are doc related, 21 which are build / tool related and 3 commits which are updates to dependencies.

### Notable Changes

- **build**:
  - Codesigning is fixed on macOS (<PERSON>) [#14179](https://github.com/nodejs/node/pull/14179)
- **deps**:
  - Snapshots are turned back on!!! (<PERSON>) [#14385](https://github.com/nodejs/node/pull/14385)
- **path**:
  - win32 volume-relative paths are working again! (<PERSON>) [#14440](https://github.com/nodejs/node/pull/14440)
- **tools**:
  - v6.x can now build with ICU 59 (<PERSON>) [#12078](https://github.com/nodejs/node/pull/12078)

### Commits

- [[`95be08e3d2`](https://github.com/nodejs/node/commit/95be08e3d2)] - **benchmark**: fix typo in inspect-proxy (Vse Mozhe<PERSON> Byt) [#14237](https://github.com/nodejs/node/pull/14237)
- [[`dbb65203f1`](https://github.com/nodejs/node/commit/dbb65203f1)] - **buffer**: remove a wrongly added attribute specifier (Jiajie Hu) [#14466](https://github.com/nodejs/node/pull/14466)
- [[`977fdce406`](https://github.com/nodejs/node/commit/977fdce406)] - **build**: codesign tarball binary on macOS (Evan Lucas) [#14179](https://github.com/nodejs/node/pull/14179)
- [[`a04d4ea8d7`](https://github.com/nodejs/node/commit/a04d4ea8d7)] - **build**: clean up config_fips.gypi (Daniel Bevenius) [#13837](https://github.com/nodejs/node/pull/13837)
- [[`c7025de6e2`](https://github.com/nodejs/node/commit/c7025de6e2)] - **build**: support dtrace on ARM (Bradley T. Hughes) [#12193](https://github.com/nodejs/node/pull/12193)
- [[`efc0f64c17`](https://github.com/nodejs/node/commit/efc0f64c17)] - **build**: reduce one level of spawning in node_gyp (Refael Ackermann) [#12653](https://github.com/nodejs/node/pull/12653)
- [[`1a7e872302`](https://github.com/nodejs/node/commit/1a7e872302)] - **build,tools**: do not force codesign prefix (Evan Lucas) [#14179](https://github.com/nodejs/node/pull/14179)
- [[`325813ea97`](https://github.com/nodejs/node/commit/325813ea97)] - **build,win**: fix python detection script (Jason Ginchereau) [#14546](https://github.com/nodejs/node/pull/14546)
- [[`c2090a0634`](https://github.com/nodejs/node/commit/c2090a0634)] - **build,windows**: restore DISTTYPEDIR (Refael Ackermann) [#13969](https://github.com/nodejs/node/pull/13969)
- [[`2a1a93dcbf`](https://github.com/nodejs/node/commit/2a1a93dcbf)] - **build,windows**: implement PEP514 python detection (Refael Ackermann) [#13900](https://github.com/nodejs/node/pull/13900)
- [[`2bc7c3a8dd`](https://github.com/nodejs/node/commit/2bc7c3a8dd)] - **child_process**: fix handleless NODE_HANDLE handling (Santiago Gimeno) [#13235](https://github.com/nodejs/node/pull/13235)
- [[`5900ebe9e4`](https://github.com/nodejs/node/commit/5900ebe9e4)] - **crypto**: remove root_cert_store from node_crypto.h (Daniel Bevenius) [#13194](https://github.com/nodejs/node/pull/13194)
- [[`987332abdf`](https://github.com/nodejs/node/commit/987332abdf)] - **deps**: cherry-pick 18ea996 from c-ares upstream (Anna Henningsen) [#13883](https://github.com/nodejs/node/pull/13883)
- [[`7fed989deb`](https://github.com/nodejs/node/commit/7fed989deb)] - **deps**: cherry-pick f5fad6d from upstream v8 (daniel.bevenius) [#12826](https://github.com/nodejs/node/pull/12826)
- [[`67ce52c740`](https://github.com/nodejs/node/commit/67ce52c740)] - **deps**: backport rehash strings after deserialization (Yang Guo) [#14385](https://github.com/nodejs/node/pull/14385)
- [[`6518932466`](https://github.com/nodejs/node/commit/6518932466)] - **dns**: fix `resolve` failed starts without network (XadillaX) [#13076](https://github.com/nodejs/node/pull/13076)
- [[`438a6427e5`](https://github.com/nodejs/node/commit/438a6427e5)] - **doc**: add gabrielschulhof to collaborators (Gabriel Schulhof) [#14692](https://github.com/nodejs/node/pull/14692)
- [[`c013c545a4`](https://github.com/nodejs/node/commit/c013c545a4)] - **doc**: update experimental status to reflect use (James M Snell) [#12723](https://github.com/nodejs/node/pull/12723)
- [[`ad2431ce28`](https://github.com/nodejs/node/commit/ad2431ce28)] - **doc**: describe labelling process for backports (Anna Henningsen) [#12431](https://github.com/nodejs/node/pull/12431)
- [[`013155d0b4`](https://github.com/nodejs/node/commit/013155d0b4)] - **doc**: add XadillaX to collaborators (XadillaX) [#14388](https://github.com/nodejs/node/pull/14388)
- [[`226ef71a1b`](https://github.com/nodejs/node/commit/226ef71a1b)] - **doc**: replace dead link in v8 module (Devin Boyer) [#14372](https://github.com/nodejs/node/pull/14372)
- [[`8b69504e17`](https://github.com/nodejs/node/commit/8b69504e17)] - **doc**: move LTS README link to increase prominence (Gibson Fahnestock) [#14259](https://github.com/nodejs/node/pull/14259)
- [[`b0286acd52`](https://github.com/nodejs/node/commit/b0286acd52)] - **doc**: update umask for clarity (James Sumners) [#14170](https://github.com/nodejs/node/pull/14170)
- [[`49d3dee0c2`](https://github.com/nodejs/node/commit/49d3dee0c2)] - **doc**: correct stream Duplex allowHalfOpen doc (Rich Trott) [#14127](https://github.com/nodejs/node/pull/14127)
- [[`9599faae18`](https://github.com/nodejs/node/commit/9599faae18)] - **doc**: note 'resize' event conditions on Windows (Dean Coakley) [#13576](https://github.com/nodejs/node/pull/13576)
- [[`8dbf827de3`](https://github.com/nodejs/node/commit/8dbf827de3)] - **doc**: fix mistake in http.md (Moogen Tian) [#14126](https://github.com/nodejs/node/pull/14126)
- [[`a3cd733dea`](https://github.com/nodejs/node/commit/a3cd733dea)] - **doc**: fix indentation issues in sample code (Rich Trott) [#13950](https://github.com/nodejs/node/pull/13950)
- [[`3b3d47c483`](https://github.com/nodejs/node/commit/3b3d47c483)] - **doc**: add CTC members to Collaborators list (Rich Trott) [#13284](https://github.com/nodejs/node/pull/13284)
- [[`f1d91ce3d2`](https://github.com/nodejs/node/commit/f1d91ce3d2)] - **doc**: fix example in child_process.md (Ruslan Iusupov) [#13716](https://github.com/nodejs/node/pull/13716)
- [[`db4fabae27`](https://github.com/nodejs/node/commit/db4fabae27)] - **doc**: note that fs.futimes only works on AIX \>7.1 (Gibson Fahnestock) [#13659](https://github.com/nodejs/node/pull/13659)
- [[`7648cca3de`](https://github.com/nodejs/node/commit/7648cca3de)] - **doc**: add @nodejs/documentation to CC table (Vse Mozhet Byt) [#13952](https://github.com/nodejs/node/pull/13952)
- [[`b03430b4ec`](https://github.com/nodejs/node/commit/b03430b4ec)] - **doc**: add gireeshpunathil to collaborators (Gireesh Punathil) [#13967](https://github.com/nodejs/node/pull/13967)
- [[`c34a7472d2`](https://github.com/nodejs/node/commit/c34a7472d2)] - **doc**: fix mistake in path.relative (Tobias Nießen) [#13912](https://github.com/nodejs/node/pull/13912)
- [[`7142c92dd6`](https://github.com/nodejs/node/commit/7142c92dd6)] - **doc**: fixed formatting issue in cli docs (Chris Young) [#13808](https://github.com/nodejs/node/pull/13808)
- [[`87906d25d4`](https://github.com/nodejs/node/commit/87906d25d4)] - **doc**: add missing zlib link to stream API docs (Rob Wu) [#13838](https://github.com/nodejs/node/pull/13838)
- [[`5ba65f2870`](https://github.com/nodejs/node/commit/5ba65f2870)] - **doc**: add entry for subprocess.killed property (Rich Trott) [#14578](https://github.com/nodejs/node/pull/14578)
- [[`73c720dd9e`](https://github.com/nodejs/node/commit/73c720dd9e)] - **doc**: change `child` to `subprocess` (Rich Trott) [#14578](https://github.com/nodejs/node/pull/14578)
- [[`7f6f1c2ddc`](https://github.com/nodejs/node/commit/7f6f1c2ddc)] - **doc, util, console**: clarify ambiguous docs (Natanael Log) [#14027](https://github.com/nodejs/node/pull/14027)
- [[`d6ab8e2f43`](https://github.com/nodejs/node/commit/d6ab8e2f43)] - **doc,stream**: \_transform happens one at a time (Matteo Collina) [#14321](https://github.com/nodejs/node/pull/14321)
- [[`c307f03b2b`](https://github.com/nodejs/node/commit/c307f03b2b)] - **doc,test**: fs - reserved characters under win32 (XadillaX) [#13875](https://github.com/nodejs/node/pull/13875)
- [[`1d5ba9c8e9`](https://github.com/nodejs/node/commit/1d5ba9c8e9)] - **docs**: add note about fs.rmdir() (Oleksandr Kushchak) [#14323](https://github.com/nodejs/node/pull/14323)
- [[`f8b60e40a4`](https://github.com/nodejs/node/commit/f8b60e40a4)] - **gyp**: implement LD/LDXX for ninja and FIPS (Sam Roberts)
- [[`963ea0e99b`](https://github.com/nodejs/node/commit/963ea0e99b)] - **lib**: update indentation of ternaries (Rich Trott) [#14247](https://github.com/nodejs/node/pull/14247)
- [[`0cacd6c89e`](https://github.com/nodejs/node/commit/0cacd6c89e)] - **lib**: normalize indentation in parentheses (Rich Trott) [#14125](https://github.com/nodejs/node/pull/14125)
- [[`4dabeeecdd`](https://github.com/nodejs/node/commit/4dabeeecdd)] - **lib**: remove excess indentation (Rich Trott) [#14090](https://github.com/nodejs/node/pull/14090)
- [[`f20ed49b5e`](https://github.com/nodejs/node/commit/f20ed49b5e)] - **lib**: use consistent indentation for ternaries (Rich Trott) [#14078](https://github.com/nodejs/node/pull/14078)
- [[`81edf592ae`](https://github.com/nodejs/node/commit/81edf592ae)] - **lib**: fix typos (Ruben Bridgewater) [#14044](https://github.com/nodejs/node/pull/14044)
- [[`39f62403c7`](https://github.com/nodejs/node/commit/39f62403c7)] - **linkedlist**: correct grammar in comments (alexbostock) [#14546](https://github.com/nodejs/node/pull/14546)
- [[`e82a9144ed`](https://github.com/nodejs/node/commit/e82a9144ed)] - **path**: remove unnecessary string copies (Tobias Nießen) [#14438](https://github.com/nodejs/node/pull/14438)
- [[`eefd32264e`](https://github.com/nodejs/node/commit/eefd32264e)] - **path**: fix win32 volume-relative paths (Timothy Gu) [#14440](https://github.com/nodejs/node/pull/14440)
- [[`fe6735cc19`](https://github.com/nodejs/node/commit/fe6735cc19)] - **src**: use existing strings over creating new ones (Anna Henningsen) [#14587](https://github.com/nodejs/node/pull/14587)
- [[`4d3b76d2be`](https://github.com/nodejs/node/commit/4d3b76d2be)] - **src**: remove GTEST_DONT_DEFINE_ASSERT_EQ in util.h (Daniel Bevenius) [#12638](https://github.com/nodejs/node/pull/12638)
- [[`51364b746f`](https://github.com/nodejs/node/commit/51364b746f)] - **src**: move crypto_bio/clienthello to crypto ns (Daniel Bevenius) [#13957](https://github.com/nodejs/node/pull/13957)
- [[`35f911152a`](https://github.com/nodejs/node/commit/35f911152a)] - **src**: add missing new line to printed message (Timothy Gu) [#13940](https://github.com/nodejs/node/pull/13940)
- [[`135e1e3b0b`](https://github.com/nodejs/node/commit/135e1e3b0b)] - **src**: merge `fn_name` in NODE_SET_PROTOTYPE_METHOD (XadillaX) [#13547](https://github.com/nodejs/node/pull/13547)
- [[`403c45fcc9`](https://github.com/nodejs/node/commit/403c45fcc9)] - **src**: only call FatalException if not verbose (Daniel Bevenius) [#12826](https://github.com/nodejs/node/pull/12826)
- [[`547e74bb22`](https://github.com/nodejs/node/commit/547e74bb22)] - **src**: use option parser for expose_internals (Sam Roberts) [#12245](https://github.com/nodejs/node/pull/12245)
- [[`f9e427945b`](https://github.com/nodejs/node/commit/f9e427945b)] - **src**: supply missing comments for CLI options (Sam Roberts) [#12245](https://github.com/nodejs/node/pull/12245)
- [[`9ca67e0147`](https://github.com/nodejs/node/commit/9ca67e0147)] - **src**: make root_cert_vector function scoped (Daniel Bevenius) [#12788](https://github.com/nodejs/node/pull/12788)
- [[`2ce80d97e9`](https://github.com/nodejs/node/commit/2ce80d97e9)] - **test**: refactor test-domain-abort-on-uncaught (Rich Trott) [#14541](https://github.com/nodejs/node/pull/14541)
- [[`4b9de44022`](https://github.com/nodejs/node/commit/4b9de44022)] - **test**: refactor test-vm-new-script-new-context (Rich Trott) [#14536](https://github.com/nodejs/node/pull/14536)
- [[`e5375a97e0`](https://github.com/nodejs/node/commit/e5375a97e0)] - **test**: add check on an addon that does not register (Ezequiel Garcia) [#13954](https://github.com/nodejs/node/pull/13954)
- [[`1a88c3e5f6`](https://github.com/nodejs/node/commit/1a88c3e5f6)] - **test**: improve error logging for inspector test (Rich Trott) [#14508](https://github.com/nodejs/node/pull/14508)
- [[`95a95cced3`](https://github.com/nodejs/node/commit/95a95cced3)] - **test**: fix flaky test-force-repl (Rich Trott) [#14439](https://github.com/nodejs/node/pull/14439)
- [[`6fd3dd20c0`](https://github.com/nodejs/node/commit/6fd3dd20c0)] - **test**: replace concatenation with template literal (rockcoder23) [#14270](https://github.com/nodejs/node/pull/14270)
- [[`3ba55d8c47`](https://github.com/nodejs/node/commit/3ba55d8c47)] - **test**: replace concatenation with template literal (Ching Hsu) [#14284](https://github.com/nodejs/node/pull/14284)
- [[`7f7a0709be`](https://github.com/nodejs/node/commit/7f7a0709be)] - **test**: replace concatenation with template literals (Zongmin Lei) [#14298](https://github.com/nodejs/node/pull/14298)
- [[`11ed4c2823`](https://github.com/nodejs/node/commit/11ed4c2823)] - **test**: replace string concatenation with template (ziyun) [#14286](https://github.com/nodejs/node/pull/14286)
- [[`bbd1c791f5`](https://github.com/nodejs/node/commit/bbd1c791f5)] - **test**: use path.join for long path concatenation (zzz) [#14280](https://github.com/nodejs/node/pull/14280)
- [[`c4f21b37a1`](https://github.com/nodejs/node/commit/c4f21b37a1)] - **test**: replace concatenation with template literals (SkyAo) [#14296](https://github.com/nodejs/node/pull/14296)
- [[`d7afa17939`](https://github.com/nodejs/node/commit/d7afa17939)] - **test**: fix error handling test-http-full-response (Rich Trott) [#14252](https://github.com/nodejs/node/pull/14252)
- [[`7a8eddf015`](https://github.com/nodejs/node/commit/7a8eddf015)] - **test**: use regex error check in test-crypto-random (Zhang Weijie) [#14273](https://github.com/nodejs/node/pull/14273)
- [[`3047cf1b48`](https://github.com/nodejs/node/commit/3047cf1b48)] - **test**: check error with regex in test-signal-safety (shaman) [#14285](https://github.com/nodejs/node/pull/14285)
- [[`bbe328830f`](https://github.com/nodejs/node/commit/bbe328830f)] - **test**: use regex error checks in test-util-format (Superwoods) [#14299](https://github.com/nodejs/node/pull/14299)
- [[`a696e2ecae`](https://github.com/nodejs/node/commit/a696e2ecae)] - **test**: use template literal for string concat (tobewhatwewant) [#14288](https://github.com/nodejs/node/pull/14288)
- [[`77506e48b0`](https://github.com/nodejs/node/commit/77506e48b0)] - **test**: simplify string concatenation (jiangplus) [#14278](https://github.com/nodejs/node/pull/14278)
- [[`b9b343c412`](https://github.com/nodejs/node/commit/b9b343c412)] - **test**: use regexp to confir error message (Bang Wu) [#14268](https://github.com/nodejs/node/pull/14268)
- [[`94ff5918b6`](https://github.com/nodejs/node/commit/94ff5918b6)] - **test**: use regluar expression in vm test (akira.xue) [#14266](https://github.com/nodejs/node/pull/14266)
- [[`8b945e7649`](https://github.com/nodejs/node/commit/8b945e7649)] - **test**: use regular expression to match error msg (Flandre) [#14265](https://github.com/nodejs/node/pull/14265)
- [[`a168361eb9`](https://github.com/nodejs/node/commit/a168361eb9)] - **test**: check complete error message (Fraser Xu) [#14264](https://github.com/nodejs/node/pull/14264)
- [[`1e403902ba`](https://github.com/nodejs/node/commit/1e403902ba)] - **test**: fix flaky test-net-can-reset-timeout (Rich Trott) [#14257](https://github.com/nodejs/node/pull/14257)
- [[`688e5ed6fd`](https://github.com/nodejs/node/commit/688e5ed6fd)] - **test**: remove common.noop (Rich Trott) [#12822](https://github.com/nodejs/node/pull/12822)
- [[`40a61e1399`](https://github.com/nodejs/node/commit/40a61e1399)] - **test**: add get/set effective uid/gid tests (Evan Lucas) [#14091](https://github.com/nodejs/node/pull/14091)
- [[`1633f8b243`](https://github.com/nodejs/node/commit/1633f8b243)] - **test**: simplify test skipping (Vse Mozhet Byt) [#14021](https://github.com/nodejs/node/pull/14021)
- [[`b7b38bdbaf`](https://github.com/nodejs/node/commit/b7b38bdbaf)] - **test**: adjust indentation for stricter linting (Rich Trott) [#14431](https://github.com/nodejs/node/pull/14431)
- [[`46e4a026b6`](https://github.com/nodejs/node/commit/46e4a026b6)] - **test**: skip test-fs-readdir-ucs2 if no support (Rich Trott) [#14029](https://github.com/nodejs/node/pull/14029)
- [[`49632287d1`](https://github.com/nodejs/node/commit/49632287d1)] - **test**: fix flaky http(s)-set-server-timeout tests (Rich Trott) [#14380](https://github.com/nodejs/node/pull/14380)
- [[`ae7eeff489`](https://github.com/nodejs/node/commit/ae7eeff489)] - **test**: fix flaky test-https-set-timeout-server (Rich Trott) [#14134](https://github.com/nodejs/node/pull/14134)
- [[`c5c65c8ce9`](https://github.com/nodejs/node/commit/c5c65c8ce9)] - **test**: fix require nits in some test-tls-\* tests (Vse Mozhet Byt) [#14008](https://github.com/nodejs/node/pull/14008)
- [[`346f199e28`](https://github.com/nodejs/node/commit/346f199e28)] - **test**: refactor test-http(s)-set-timeout-server (Alexey Orlenko) [#13935](https://github.com/nodejs/node/pull/13935)
- [[`ac851c482c`](https://github.com/nodejs/node/commit/ac851c482c)] - **test**: refactor test-http-invalidheaderfield (Rich Trott) [#13996](https://github.com/nodejs/node/pull/13996)
- [[`49e786628f`](https://github.com/nodejs/node/commit/49e786628f)] - **test**: replace indexOf with includes and startsWith (Nataly Shrits) [#13852](https://github.com/nodejs/node/pull/13852)
- [[`2eb926b487`](https://github.com/nodejs/node/commit/2eb926b487)] - **test**: remove undef NDEBUG from at-exit addons test (Daniel Bevenius) [#13998](https://github.com/nodejs/node/pull/13998)
- [[`0bcbcca21c`](https://github.com/nodejs/node/commit/0bcbcca21c)] - **test**: refactor test-fs-watchfile (Rich Trott) [#13721](https://github.com/nodejs/node/pull/13721)
- [[`bd8574ccee`](https://github.com/nodejs/node/commit/bd8574ccee)] - **test**: refactor test-child-process-send-type-error (Rich Trott) [#13904](https://github.com/nodejs/node/pull/13904)
- [[`74945dd18a`](https://github.com/nodejs/node/commit/74945dd18a)] - **test**: refactor test-cluster-basic (Rich Trott) [#13905](https://github.com/nodejs/node/pull/13905)
- [[`dc3d29519d`](https://github.com/nodejs/node/commit/dc3d29519d)] - **test**: remove unneeded HandleScope usage (Ezequiel Garcia) [#13859](https://github.com/nodejs/node/pull/13859)
- [[`beca25ab9e`](https://github.com/nodejs/node/commit/beca25ab9e)] - **test**: skip fips tests using OpenSSL config file (Daniel Bevenius) [#13786](https://github.com/nodejs/node/pull/13786)
- [[`d3c85a4806`](https://github.com/nodejs/node/commit/d3c85a4806)] - **test**: refactor test-tls-invoked-queued (Rich Trott) [#13893](https://github.com/nodejs/node/pull/13893)
- [[`676a94e44d`](https://github.com/nodejs/node/commit/676a94e44d)] - **test**: refactor test-tls-env-extra-ca (Rich Trott) [#13886](https://github.com/nodejs/node/pull/13886)
- [[`fd6bbc098e`](https://github.com/nodejs/node/commit/fd6bbc098e)] - **test**: make http(s)-set-timeout-server more similar (Julien Klepatch) [#13822](https://github.com/nodejs/node/pull/13822)
- [[`8ba784383c`](https://github.com/nodejs/node/commit/8ba784383c)] - **test**: remove `require('buffer')` from 4 test files (XadillaX) [#13844](https://github.com/nodejs/node/pull/13844)
- [[`cd962e6de3`](https://github.com/nodejs/node/commit/cd962e6de3)] - **test**: remove unnecessary require('buffer').Buffer (lena) [#13851](https://github.com/nodejs/node/pull/13851)
- [[`fff0b83f1b`](https://github.com/nodejs/node/commit/fff0b83f1b)] - **test**: remove `require('buffer')` from 4 test files (Zongmin Lei) [#13846](https://github.com/nodejs/node/pull/13846)
- [[`6d02bf40d0`](https://github.com/nodejs/node/commit/6d02bf40d0)] - **test**: remove require('buffer') from 4 buffer tests (OriLev) [#13855](https://github.com/nodejs/node/pull/13855)
- [[`0abc82db50`](https://github.com/nodejs/node/commit/0abc82db50)] - **test**: remove require('buffer') on 6 fs test files (sallen450) [#13845](https://github.com/nodejs/node/pull/13845)
- [[`1fb19ac0c5`](https://github.com/nodejs/node/commit/1fb19ac0c5)] - **test**: remove unnecessary Buffer import (Steven Winston) [#13860](https://github.com/nodejs/node/pull/13860)
- [[`5a9d7b3bf5`](https://github.com/nodejs/node/commit/5a9d7b3bf5)] - **test**: use string instead of RegExp in split() (Vse Mozhet Byt) [#13710](https://github.com/nodejs/node/pull/13710)
- [[`6731d1b067`](https://github.com/nodejs/node/commit/6731d1b067)] - **test**: remove needless RegExp flags (Vse Mozhet Byt) [#13690](https://github.com/nodejs/node/pull/13690)
- [[`842b84c4e7`](https://github.com/nodejs/node/commit/842b84c4e7)] - **test**: refactor test-http-set-timeout-server (Rich Trott) [#13802](https://github.com/nodejs/node/pull/13802)
- [[`389f29406a`](https://github.com/nodejs/node/commit/389f29406a)] - **test**: make test-http(s)-set-timeout-server alike (jklepatch) [#13625](https://github.com/nodejs/node/pull/13625)
- [[`5e9b2030b9`](https://github.com/nodejs/node/commit/5e9b2030b9)] - **test**: use mustNotCall() in test-fs-watch (Rich Trott) [#13595](https://github.com/nodejs/node/pull/13595)
- [[`9356e9667d`](https://github.com/nodejs/node/commit/9356e9667d)] - **test**: add mustCall() to child-process test (Rich Trott) [#13605](https://github.com/nodejs/node/pull/13605)
- [[`406b3c0371`](https://github.com/nodejs/node/commit/406b3c0371)] - **test**: use mustNotCall in test-http-eof-on-connect (Rich Trott) [#13587](https://github.com/nodejs/node/pull/13587)
- [[`2f19dcddaa`](https://github.com/nodejs/node/commit/2f19dcddaa)] - **test**: refactor test-fs-read-\* (Rich Trott) [#13501](https://github.com/nodejs/node/pull/13501)
- [[`3bdf7bf9e9`](https://github.com/nodejs/node/commit/3bdf7bf9e9)] - **test**: refactor domain tests (Rich Trott) [#13480](https://github.com/nodejs/node/pull/13480)
- [[`543d2de700`](https://github.com/nodejs/node/commit/543d2de700)] - **test**: check callback not invoked on lookup error (Rich Trott) [#13456](https://github.com/nodejs/node/pull/13456)
- [[`91fb0cb6b0`](https://github.com/nodejs/node/commit/91fb0cb6b0)] - **test**: refactor test-dgram-oob-buffer (Rich Trott) [#13443](https://github.com/nodejs/node/pull/13443)
- [[`08f7cca3b3`](https://github.com/nodejs/node/commit/08f7cca3b3)] - **test**: add documentation for common.mustNotCall() (Rich Trott) [#13359](https://github.com/nodejs/node/pull/13359)
- [[`55c96cf8a8`](https://github.com/nodejs/node/commit/55c96cf8a8)] - **test**: refactor test-net-server-bind (Rich Trott) [#13273](https://github.com/nodejs/node/pull/13273)
- [[`371b648d1b`](https://github.com/nodejs/node/commit/371b648d1b)] - **test**: use mustCall() in test-readline-interface (Rich Trott) [#13259](https://github.com/nodejs/node/pull/13259)
- [[`3808e3701c`](https://github.com/nodejs/node/commit/3808e3701c)] - **test**: use mustNotCall() in test-stream2-objects (Rich Trott) [#13249](https://github.com/nodejs/node/pull/13249)
- [[`b793fc6cf6`](https://github.com/nodejs/node/commit/b793fc6cf6)] - **test**: replace `indexOf` with `includes` (Aditya Anand) [#13215](https://github.com/nodejs/node/pull/13215)
- [[`b7c7112d7b`](https://github.com/nodejs/node/commit/b7c7112d7b)] - **test**: move stream2 test from pummel to parallel (Rich Trott) [#13146](https://github.com/nodejs/node/pull/13146)
- [[`7a5248d172`](https://github.com/nodejs/node/commit/7a5248d172)] - **test**: simplify assert usage in test-stream2-basic (Rich Trott) [#13146](https://github.com/nodejs/node/pull/13146)
- [[`e15e2e7a30`](https://github.com/nodejs/node/commit/e15e2e7a30)] - **test**: check noop function invocations (Rich Trott) [#13146](https://github.com/nodejs/node/pull/13146)
- [[`4a3e089984`](https://github.com/nodejs/node/commit/4a3e089984)] - **test**: confirm callback is invoked in fs test (Rich Trott) [#13132](https://github.com/nodejs/node/pull/13132)
- [[`8b161e0a78`](https://github.com/nodejs/node/commit/8b161e0a78)] - **test**: check number of message events (Rich Trott) [#13125](https://github.com/nodejs/node/pull/13125)
- [[`c2a0a936e1`](https://github.com/nodejs/node/commit/c2a0a936e1)] - **test**: increase coverage for path.parse (Tobias Nießen) [#14438](https://github.com/nodejs/node/pull/14438)
- [[`202bfcc1c0`](https://github.com/nodejs/node/commit/202bfcc1c0)] - **test**: mark test-fs-read-buffer-to-string-fail as flaky (jeyanthinath) [#14495](https://github.com/nodejs/node/pull/14495)
- [[`a1cef1fc8f`](https://github.com/nodejs/node/commit/a1cef1fc8f)] - **test**: harden test-dgram-bind-shared-ports (Refael Ackermann) [#13100](https://github.com/nodejs/node/pull/13100)
- [[`f578c9bbb6`](https://github.com/nodejs/node/commit/f578c9bbb6)] - **test**: add mustCallAtLeast (Refael Ackermann) [#12935](https://github.com/nodejs/node/pull/12935)
- [[`a7b94500f2`](https://github.com/nodejs/node/commit/a7b94500f2)] - **test**: add common.noop, default for common.mustCall() (James M Snell) [#12027](https://github.com/nodejs/node/pull/12027)
- [[`f3c0b8cd6e`](https://github.com/nodejs/node/commit/f3c0b8cd6e)] - **test,fs**: delay unlink in test-regress-GH-4027.js (Jaime Bernardo) [#14010](https://github.com/nodejs/node/pull/14010)
- [[`e8438c1b22`](https://github.com/nodejs/node/commit/e8438c1b22)] - **timers**: do not use user object call/apply (Rich Trott) [#12960](https://github.com/nodejs/node/pull/12960)
- [[`31f572c3ea`](https://github.com/nodejs/node/commit/31f572c3ea)] - **tools**: update to ESLint 4.3.0 (Rich Trott) [#14417](https://github.com/nodejs/node/pull/14417)
- [[`7d851e3b6a`](https://github.com/nodejs/node/commit/7d851e3b6a)] - **tools**: update package.json `engine` field (AJ Jordan) [#14165](https://github.com/nodejs/node/pull/14165)
- [[`c5adb5f008`](https://github.com/nodejs/node/commit/c5adb5f008)] - **tools**: update ESLint to 4.2.0 (Rich Trott) [#14155](https://github.com/nodejs/node/pull/14155)
- [[`2af21650d6`](https://github.com/nodejs/node/commit/2af21650d6)] - **tools**: generate template literal for addon tests (Rich Trott) [#14094](https://github.com/nodejs/node/pull/14094)
- [[`62de339327`](https://github.com/nodejs/node/commit/62de339327)] - **tools**: remove legacy indentation linting (Rich Trott) [#14515](https://github.com/nodejs/node/pull/14515)
- [[`8b7c4fc06f`](https://github.com/nodejs/node/commit/8b7c4fc06f)] - **tools**: remove align-multiline-assignment lint rule (Rich Trott) [#14079](https://github.com/nodejs/node/pull/14079)
- [[`509205fddd`](https://github.com/nodejs/node/commit/509205fddd)] - **tools**: update to ESLint 4.1.1 (Rich Trott) [#13946](https://github.com/nodejs/node/pull/13946)
- [[`8f664e52d9`](https://github.com/nodejs/node/commit/8f664e52d9)] - **tools**: add script to update ESLint (Rich Trott) [#13895](https://github.com/nodejs/node/pull/13895)
- [[`d34bc78fd4`](https://github.com/nodejs/node/commit/d34bc78fd4)] - **tools**: update to ESLint 4.1.0 (Rich Trott) [#13895](https://github.com/nodejs/node/pull/13895)
- [[`01d82d843b`](https://github.com/nodejs/node/commit/01d82d843b)] - **tools**: use no-use-before-define ESLint rule (Vse Mozhet Byt) [#14032](https://github.com/nodejs/node/pull/14032)
- [[`70901b271c`](https://github.com/nodejs/node/commit/70901b271c)] - **tools**: remove comment in eslint rule (Daniel Bevenius) [#13945](https://github.com/nodejs/node/pull/13945)
- [[`74d5cba007`](https://github.com/nodejs/node/commit/74d5cba007)] - **tools**: add missing #include "unicode/putil.h" (Steven R. Loomis) [#12078](https://github.com/nodejs/node/pull/12078)
- [[`7bb200f624`](https://github.com/nodejs/node/commit/7bb200f624)] - **tools**: add rule prefering common.mustNotCall() (James M Snell) [#12027](https://github.com/nodejs/node/pull/12027)
- [[`d5bf1379b5`](https://github.com/nodejs/node/commit/d5bf1379b5)] - **v8**: fix RegExp nits in v8_prof_polyfill.js (Vse Mozhet Byt) [#13709](https://github.com/nodejs/node/pull/13709)
- [[`9e2d85e441`](https://github.com/nodejs/node/commit/9e2d85e441)] - **v8**: handle proxy objects in MakeMirror(), v2 (Ben Noordhuis) [#14343](https://github.com/nodejs/node/pull/14343)
- [[`bccd2f59b0`](https://github.com/nodejs/node/commit/bccd2f59b0)] - **v8**: handle proxy objects in MakeMirror(), v1 (Ben Noordhuis) [#14343](https://github.com/nodejs/node/pull/14343)
- [[`e79c054f76`](https://github.com/nodejs/node/commit/e79c054f76)] - **zlib**: fix crash when initializing failed (Anna Henningsen) [#14666](https://github.com/nodejs/node/pull/14666)

Windows 32-bit Installer: https://nodejs.org/dist/v6.11.3/node-v6.11.3-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v6.11.3/node-v6.11.3-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v6.11.3/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v6.11.3/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v6.11.3/node-v6.11.3.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v6.11.3/node-v6.11.3-darwin-x64.tar.gz \
Linux 32-bit Binary: https://nodejs.org/dist/v6.11.3/node-v6.11.3-linux-x86.tar.xz \
Linux 64-bit Binary: https://nodejs.org/dist/v6.11.3/node-v6.11.3-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v6.11.3/node-v6.11.3-linux-ppc64le.tar.xz \
Linux PPC BE 64-bit Binary: https://nodejs.org/dist/v6.11.3/node-v6.11.3-linux-ppc64.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v6.11.3/node-v6.11.3-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v6.11.3/node-v6.11.3-aix-ppc64.tar.gz \
SmartOS 32-bit Binary: https://nodejs.org/dist/v6.11.3/node-v6.11.3-sunos-x86.tar.xz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v6.11.3/node-v6.11.3-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v6.11.3/node-v6.11.3-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v6.11.3/node-v6.11.3-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v6.11.3/node-v6.11.3-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v6.11.3/node-v6.11.3.tar.gz \
Other release files: https://nodejs.org/dist/v6.11.3/ \
Documentation: https://nodejs.org/docs/v6.11.3/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

7f344d29a6feb0fd77529dc30ce2badd881d8fa382e6bbee97dee45a31b1e322  node-v6.11.3-aix-ppc64.tar.gz
be80751e600b37f2228170fe5eeb04d6f0febb6076f586310685ea2a34e558fb  node-v6.11.3-darwin-x64.tar.gz
c86f6508b4e9ff856d566e1d845f3822a3375936faddd8089dc70dfad1b0656b  node-v6.11.3-darwin-x64.tar.xz
a1deca4c444295bd4e477849104d20c15d0f30bda4387f3fab23b543b1125410  node-v6.11.3-headers.tar.gz
a6c5a6ce2c0693a7fa8cc1457f68c3214b8bd7dd03f6fdc41027ea1247f2549d  node-v6.11.3-headers.tar.xz
1a7712fa64989809a6949b08d449af00f93116ccc44e10789ea1615df806ca96  node-v6.11.3-linux-arm64.tar.gz
9bbb2d3a7b1530885a93324421031cfc4409f58b61c0963e779cb4ef0573885c  node-v6.11.3-linux-arm64.tar.xz
4e13c00a1a94ade809b7478db98f7664a80b64bd8256eae9bd15b8ca39343ad5  node-v6.11.3-linux-armv6l.tar.gz
8d1b8d80b21b27ceb82255a1563356d4ce5a3d0213985be43285f3cfd76bed78  node-v6.11.3-linux-armv6l.tar.xz
7588ee46820af77a6ee286bd51799f403810dd20a688cece227e241a37ec863c  node-v6.11.3-linux-armv7l.tar.gz
31ddf7b326ca9f02bcd9bafc401889c39e651c854262a3ffdbbfdc1fcd594728  node-v6.11.3-linux-armv7l.tar.xz
ef6b281b37412f239a0b7ba0b081e5ce49e875e79de6af62e4afd98dc3db9ffd  node-v6.11.3-linux-ppc64le.tar.gz
e0a38534de88323b630df29dd4731f6701b6fee66382f1d1ef80ec757adde9d9  node-v6.11.3-linux-ppc64le.tar.xz
b0800910c26cd7804748925ff61d20d8ec7d26be8b3f7a41b0c6e1499cb2212b  node-v6.11.3-linux-ppc64.tar.gz
4b420d632977dae87baf6f71d53ca463966bcd99f7dcc443d736517d0f52c5c0  node-v6.11.3-linux-ppc64.tar.xz
95ac7ca0842ff3ec5fd6e423adf924d69442a6dac2f718a225432d1c99cb18c3  node-v6.11.3-linux-s390x.tar.gz
75776ce338c1ee66a51b0317d9493c772423e689a9d22bc717c311d3b675220a  node-v6.11.3-linux-s390x.tar.xz
610705d45eb2846a9e10690678a078d9159e5f941487aca20c6f53b33104358c  node-v6.11.3-linux-x64.tar.gz
c8e1ad4b731e15e1318465a616f2283055e145e0b17a3a04d5591ec264cd30d3  node-v6.11.3-linux-x64.tar.xz
9c82c884cbe971f5e69303077aacedd76cc76ff684d5c4ab3daa7e8881a15c95  node-v6.11.3-linux-x86.tar.gz
9afabd3b547bc96345898242cf81553738bf4e4f63a7e40de852a46ab109e390  node-v6.11.3-linux-x86.tar.xz
0158ca2521cdfafa5b2d714a9d7c5a19518c3201142ccbca449bdc4061f17073  node-v6.11.3.pkg
c60ac1aefb553f28df5a821fb0ec27a7da1bff5c1fe333a9be9ea84d7f488195  node-v6.11.3-sunos-x64.tar.gz
68481b66cc23252c320284f687e7d7e097ab35006ba65749cdc1f57461c8db28  node-v6.11.3-sunos-x64.tar.xz
2abec834dce32503478d7b97570624327fa77ee0cabbc91ab83a0ed57bd7c01f  node-v6.11.3-sunos-x86.tar.gz
f6d83797c9893ca9e63ef32358b4b6702605feca2c979d394498fdae05458cd5  node-v6.11.3-sunos-x86.tar.xz
c60562a7bc316f137297b7aa23ae354e3ac6779fe8ddf46fe43a75b6b92443e8  node-v6.11.3.tar.gz
5f09b77010cb3ec4e321ecdc30beb6b49d8a2843155b7f0ad97202ec308ab6bc  node-v6.11.3.tar.xz
a767bca7033cbbde3294080728b564145ac4fbf13f408a9070d9b5acc5c56068  node-v6.11.3-win-x64.7z
3e1bde603d29f0965a9b2ed4f025067c88a7f97eef6953268cba818c396aad65  node-v6.11.3-win-x64.zip
59e1dd9dbd312cb16a3a2c1a549625c8d5352d1465d91f47889818beeafa625b  node-v6.11.3-win-x86.7z
1f0be54016ba7efddf210564c5ea748ccdeb2aad0f92af6f826ce7b7fa746ab8  node-v6.11.3-win-x86.zip
ea3007f9f4c714afd5947581463814a487501bbb9dc95a498112b4d24d4ce07f  node-v6.11.3-x64.msi
4e43e285a34dc1afc49665174db56fdb60f28c9cdf49491aea54910dcc142cdb  node-v6.11.3-x86.msi
be23a6af06fdeabc8197e0c4114cb1d472e2ed84e6fde83ca1f2308338905c7e  win-x64/node.exe
5cef2edacb058bb751a2b28b68f6aabcec349d20564d4bfb02c91cf0b26c5fae  win-x64/node.lib
cdf591e39ffd94528cf3a9a6ba3679820bcec2b673b5a4276889f02e7b12fef3  win-x64/node_pdb.7z
100a10e47b6b11fb280ae3d1d160aa5373db2445adfe1322a924b2c7b2016caf  win-x64/node_pdb.zip
e1e3b3df077784eb86f50122d53d765b522caef80068a0dd0af61331713cd83a  win-x86/node.exe
2ac040d8b73d56d3d44bc5f8e247bf4549ca66c36f37d235464fb3abc62cdba1  win-x86/node.lib
2ea22fa1a321b56254131905b4e46587268f60a8525c22cd12ac0e31b0207c05  win-x86/node_pdb.7z
5ee8563c16a0b8a40a69b34513244a0202d0ded163d41b47679226c2e56e676c  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQEcBAEBCAAGBQJZrwAVAAoJEJM7AfQLXKlGKBAIAKiPxib6hEOpJhk6nIYLEm2p
VtOxiWGurzEdspQ5jC1qHvId/UUt27KIRSzjmIG2Ux6ZBtKo+twh78I2KJxtOKw3
7f7RXE5ph8dQEkZCYvNKcAsKzb/gcpQF/vxksVPSvBP9WLooRWyCdCk29aIkTMUy
HWe1QJNA4QYuIdbi6Ukxzwb0IUndTSXmVCGrJh83nZenISZeLBS/ndozDBELFJ/n
U6XN779ObtVPJPXpTI0ZNF/a9pB+/d57G42KcHIXNV7+Fzdv7VVGQS2nbbA0jI7l
CZoTiZQVXG7op1MwcQq6wUFkvAluDVYCoI1rZIbRdIEXWAmvZTyySkng8cnmFYk=
=evgh
-----END PGP SIGNATURE-----

```
