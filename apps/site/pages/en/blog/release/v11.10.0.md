---
date: '2019-02-14T23:00:04.254Z'
category: release
title: Node v11.10.0 (Current)
layout: blog-post
author: <PERSON><PERSON><PERSON>
---

### Notable Changes

- **deps**
  - Updated libuv to 1.26.0. [#26037](https://github.com/nodejs/node/pull/26037)
  - Updated npm to 6.7.0. [#25804](https://github.com/nodejs/node/pull/25804)
- **http, http2**
  - `response.writeHead` now returns the response object. [#25974](https://github.com/nodejs/node/pull/25974)
- **perf_hooks**
  - Implemented a histogram based API. [#25378](https://github.com/nodejs/node/pull/25378)
- **process**
  - Exposed `process.features.inspector`. [#25819](https://github.com/nodejs/node/pull/25378)
- **repl**
  - Added `repl.setupHistory` for programmatic repl. [#25895](https://github.com/nodejs/node/pull/25895)
- **tls**
  - Introduced client "session" event. [#25831](https://github.com/nodejs/node/pull/25831)

### Commits

- [[`ccf60bbad2`](https://github.com/nodejs/node/commit/ccf60bbad2)] - **assert**: add internal assert.fail() (Rich Trott) [#26047](https://github.com/nodejs/node/pull/26047)
- [[`0b4055e616`](https://github.com/nodejs/node/commit/0b4055e616)] - **assert**: create internal/assert micro-module (Rich Trott) [#25956](https://github.com/nodejs/node/pull/25956)
- [[`37d207cc0c`](https://github.com/nodejs/node/commit/37d207cc0c)] - **assert**: refactor internal assert.js (Rich Trott) [#25956](https://github.com/nodejs/node/pull/25956)
- [[`2b1f88185f`](https://github.com/nodejs/node/commit/2b1f88185f)] - **benchmark**: remove unreachable return (ZYSzys) [#25883](https://github.com/nodejs/node/pull/25883)
- [[`c4d16e80b7`](https://github.com/nodejs/node/commit/c4d16e80b7)] - **benchmark**: refactor for consistent style (Rich Trott) [#25944](https://github.com/nodejs/node/pull/25944)
- [[`c4e2bbbcab`](https://github.com/nodejs/node/commit/c4e2bbbcab)] - **benchmark**: use consistent coding style in assert/\* (Rich Trott) [#25865](https://github.com/nodejs/node/pull/25865)
- [[`18b344c0d2`](https://github.com/nodejs/node/commit/18b344c0d2)] - **benchmark**: refactor benchmark/common.js (Rich Trott) [#25805](https://github.com/nodejs/node/pull/25805)
- [[`40398fd07a`](https://github.com/nodejs/node/commit/40398fd07a)] - **benchmark**: refactor \_http-benchmarkers.js (Rich Trott) [#25803](https://github.com/nodejs/node/pull/25803)
- [[`d5d163d8b9`](https://github.com/nodejs/node/commit/d5d163d8b9)] - **build**: export deprecated OpenSSL symbols on Windows (Richard Lau) [#25991](https://github.com/nodejs/node/pull/25991)
- [[`197efb7f84`](https://github.com/nodejs/node/commit/197efb7f84)] - **child_process**: close pipe ends that are re-piped (Gireesh Punathil) [#21209](https://github.com/nodejs/node/pull/21209)
- [[`f87352366a`](https://github.com/nodejs/node/commit/f87352366a)] - **cluster**: migrate round_robin_handle to internal assert (Rich Trott) [#26047](https://github.com/nodejs/node/pull/26047)
- [[`8c9800ce27`](https://github.com/nodejs/node/commit/8c9800ce27)] - **crypto**: include 'Buffer' in error output of Hash.update method (Amit Zur) [#25533](https://github.com/nodejs/node/pull/25533)
- [[`baa0865886`](https://github.com/nodejs/node/commit/baa0865886)] - **crypto**: don't crash X509ToObject on error (David Benjamin) [#25717](https://github.com/nodejs/node/pull/25717)
- [[`3e010aff83`](https://github.com/nodejs/node/commit/3e010aff83)] - **crypto**: fix malloc mixing in X509ToObject (David Benjamin) [#25717](https://github.com/nodejs/node/pull/25717)
- [[`da46be2542`](https://github.com/nodejs/node/commit/da46be2542)] - **crypto**: fix public key encoding name in comment (David Benjamin) [#25736](https://github.com/nodejs/node/pull/25736)
- [[`8b5a2c4f61`](https://github.com/nodejs/node/commit/8b5a2c4f61)] - **deps**: upgrade to libuv 1.26.0 (cjihrig) [#26037](https://github.com/nodejs/node/pull/26037)
- [[`1c5fbeab34`](https://github.com/nodejs/node/commit/1c5fbeab34)] - **deps**: upgrade npm to 6.7.0 (Kat Marchán) [#25804](https://github.com/nodejs/node/pull/25804)
- [[`3f8c22b4cb`](https://github.com/nodejs/node/commit/3f8c22b4cb)] - **deps**: update llhttp to 1.1.1 (Fedor Indutny) [#25753](https://github.com/nodejs/node/pull/25753)
- [[`823fd5b493`](https://github.com/nodejs/node/commit/823fd5b493)] - **(SEMVER-MINOR)** **deps**: float fix for building HdrHistogram on Win x86 (jasnell) [#25378](https://github.com/nodejs/node/pull/25378)
- [[`c01bbc5258`](https://github.com/nodejs/node/commit/c01bbc5258)] - **deps**: update acorn to 6.0.7 (Michaël Zasso) [#25844](https://github.com/nodejs/node/pull/25844)
- [[`a6c8e40655`](https://github.com/nodejs/node/commit/a6c8e40655)] - **deps**: patch to fix \*.onion MX query on c-ares (XadillaX) [#25840](https://github.com/nodejs/node/pull/25840)
- [[`8b71464711`](https://github.com/nodejs/node/commit/8b71464711)] - **deps**: remove OpenSSL git and travis configuration (Sam Roberts) [#25689](https://github.com/nodejs/node/pull/25689)
- [[`673e434714`](https://github.com/nodejs/node/commit/673e434714)] - **deps**: v8, cherry-pick 9365d09, aac2f8c, 47d34a3 (Benjamin Coe) [#25429](https://github.com/nodejs/node/pull/25429)
- [[`411f6fe832`](https://github.com/nodejs/node/commit/411f6fe832)] - **deps**: cherry-pick c736883 from upstream V8 (Yang Guo)
- [[`4a254a6ce4`](https://github.com/nodejs/node/commit/4a254a6ce4)] - **doc**: edit N-API introductory material in Collaborator Guide (Rich Trott) [#26051](https://github.com/nodejs/node/pull/26051)
- [[`44fc2f6094`](https://github.com/nodejs/node/commit/44fc2f6094)] - **doc**: clarify effect of stream.destroy() on write() (Sam Roberts) [#25973](https://github.com/nodejs/node/pull/25973)
- [[`21e6d353af`](https://github.com/nodejs/node/commit/21e6d353af)] - **doc**: renamed remote's name (Thang Tran) [#26050](https://github.com/nodejs/node/pull/26050)
- [[`e629afa6ae`](https://github.com/nodejs/node/commit/e629afa6ae)] - **doc**: fix minor typo in dgram.md (Daniel Bevenius) [#26055](https://github.com/nodejs/node/pull/26055)
- [[`663b6251a0`](https://github.com/nodejs/node/commit/663b6251a0)] - **doc**: fix some nits in perf_hooks (Vse Mozhet Byt) [#26022](https://github.com/nodejs/node/pull/26022)
- [[`9420a737fe`](https://github.com/nodejs/node/commit/9420a737fe)] - **doc**: edit process.report related documentation (cjihrig) [#25983](https://github.com/nodejs/node/pull/25983)
- [[`eb4b5ea233`](https://github.com/nodejs/node/commit/eb4b5ea233)] - **doc**: clarify http timeouts (Andrew Moss) [#25748](https://github.com/nodejs/node/pull/25748)
- [[`a225f99ea8`](https://github.com/nodejs/node/commit/a225f99ea8)] - **doc**: revise Introducing New Modules (Rich Trott) [#25975](https://github.com/nodejs/node/pull/25975)
- [[`f516f68032`](https://github.com/nodejs/node/commit/f516f68032)] - **doc**: add a sentence about REPLACEME in code changes (Lance Ball) [#25961](https://github.com/nodejs/node/pull/25961)
- [[`3b74cc6c26`](https://github.com/nodejs/node/commit/3b74cc6c26)] - **doc**: revise Collaborator Guide on reverting (Rich Trott) [#25942](https://github.com/nodejs/node/pull/25942)
- [[`353de0f752`](https://github.com/nodejs/node/commit/353de0f752)] - **doc**: fix err_synthetic issue on v11.x (sreepurnajasti) [#25770](https://github.com/nodejs/node/pull/25770)
- [[`cc4ae20d20`](https://github.com/nodejs/node/commit/cc4ae20d20)] - **doc**: improve doc on unintended breaking changes (Rich Trott) [#25887](https://github.com/nodejs/node/pull/25887)
- [[`1f6acbb279`](https://github.com/nodejs/node/commit/1f6acbb279)] - **doc**: document os.userInfo() throwing SystemError (Raido Kuli) [#25724](https://github.com/nodejs/node/pull/25724)
- [[`699d161f9e`](https://github.com/nodejs/node/commit/699d161f9e)] - **doc**: fix machine field in example report (cjihrig) [#25855](https://github.com/nodejs/node/pull/25855)
- [[`618f641271`](https://github.com/nodejs/node/commit/618f641271)] - **doc**: remove redundant LTS/Current information in Collaborator Guide (Rich Trott) [#25842](https://github.com/nodejs/node/pull/25842)
- [[`7a1f166cfa`](https://github.com/nodejs/node/commit/7a1f166cfa)] - **doc**: add documentation for request.path (Kei Ito) [#25788](https://github.com/nodejs/node/pull/25788)
- [[`f5db5090bc`](https://github.com/nodejs/node/commit/f5db5090bc)] - **doc**: remove outdated COLLABORATOR_GUIDE sentence about breaking changes (Rich Trott) [#25780](https://github.com/nodejs/node/pull/25780)
- [[`accb8aec35`](https://github.com/nodejs/node/commit/accb8aec35)] - **doc**: revise inspect security info in cli.md (Rich Trott) [#25779](https://github.com/nodejs/node/pull/25779)
- [[`fd98d62909`](https://github.com/nodejs/node/commit/fd98d62909)] - **doc**: revise style guide (Rich Trott) [#25778](https://github.com/nodejs/node/pull/25778)
- [[`60c5099f4b`](https://github.com/nodejs/node/commit/60c5099f4b)] - **domain**: avoid circular memory references (Anna Henningsen) [#25993](https://github.com/nodejs/node/pull/25993)
- [[`2b48a381b9`](https://github.com/nodejs/node/commit/2b48a381b9)] - **fs**: remove redundant callback check (ZYSzys) [#25160](https://github.com/nodejs/node/pull/25160)
- [[`29c195e17f`](https://github.com/nodejs/node/commit/29c195e17f)] - **fs**: remove useless internalFS (ZYSzys) [#25161](https://github.com/nodejs/node/pull/25161)
- [[`51982978eb`](https://github.com/nodejs/node/commit/51982978eb)] - **http**: improve performance for incoming headers (Weijia Wang) [#26041](https://github.com/nodejs/node/pull/26041)
- [[`90c9f1d323`](https://github.com/nodejs/node/commit/90c9f1d323)] - **http**: reduce multiple output arrays into one (Weijia Wang) [#26004](https://github.com/nodejs/node/pull/26004)
- [[`a5247cc180`](https://github.com/nodejs/node/commit/a5247cc180)] - **(SEMVER-MINOR)** **http**: makes response.writeHead return the response (Mark S. Everitt) [#25974](https://github.com/nodejs/node/pull/25974)
- [[`b7fb49e70a`](https://github.com/nodejs/node/commit/b7fb49e70a)] - **http**: remove redundant call to socket.setTimeout() (Luigi Pinca) [#25928](https://github.com/nodejs/node/pull/25928)
- [[`25c19eb1d8`](https://github.com/nodejs/node/commit/25c19eb1d8)] - **http**: make timeout event work with agent timeout (Luigi Pinca) [#25488](https://github.com/nodejs/node/pull/25488)
- [[`0899c8bb32`](https://github.com/nodejs/node/commit/0899c8bb32)] - **http2**: improve compat performance (Matteo Collina) [#25567](https://github.com/nodejs/node/pull/25567)
- [[`237b5e65e4`](https://github.com/nodejs/node/commit/237b5e65e4)] - **(SEMVER-MINOR)** **http2**: makes response.writeHead return the response (Mark S. Everitt) [#25974](https://github.com/nodejs/node/pull/25974)
- [[`6967407b19`](https://github.com/nodejs/node/commit/6967407b19)] - **inspector, trace_events**: make sure messages are sent on a main thread (Eugene Ostroukhov) [#24814](https://github.com/nodejs/node/pull/24814)
- [[`d02ad40d42`](https://github.com/nodejs/node/commit/d02ad40d42)] - **inspector,vm**: remove --eval wrapper (Anna Henningsen) [#25832](https://github.com/nodejs/node/pull/25832)
- [[`32e6bb32b2`](https://github.com/nodejs/node/commit/32e6bb32b2)] - **lib**: merge 'undefined' into one 'break' branch (MaleDong) [#26039](https://github.com/nodejs/node/pull/26039)
- [[`b2b37c631a`](https://github.com/nodejs/node/commit/b2b37c631a)] - **lib**: simplify 'umask' (MaleDong) [#26035](https://github.com/nodejs/node/pull/26035)
- [[`b1a8927adc`](https://github.com/nodejs/node/commit/b1a8927adc)] - **lib**: fix the typo error (MaleDong) [#26032](https://github.com/nodejs/node/pull/26032)
- [[`d5f4a1f2ac`](https://github.com/nodejs/node/commit/d5f4a1f2ac)] - **lib**: save a copy of Symbol in primordials (Joyee Cheung) [#26033](https://github.com/nodejs/node/pull/26033)
- [[`bd932a347e`](https://github.com/nodejs/node/commit/bd932a347e)] - **lib**: move per_context.js under lib/internal/bootstrap (Joyee Cheung) [#26033](https://github.com/nodejs/node/pull/26033)
- [[`f40e0fcdcb`](https://github.com/nodejs/node/commit/f40e0fcdcb)] - **lib**: replace 'assert' with 'internal/assert' for many built-ins (Rich Trott) [#25956](https://github.com/nodejs/node/pull/25956)
- [[`8ade433f51`](https://github.com/nodejs/node/commit/8ade433f51)] - **lib**: move signal event handling into bootstrap/node.js (Joyee Cheung) [#25859](https://github.com/nodejs/node/pull/25859)
- [[`92ca50636c`](https://github.com/nodejs/node/commit/92ca50636c)] - **lib**: save primordials during bootstrap and use it in builtins (Joyee Cheung) [#25816](https://github.com/nodejs/node/pull/25816)
- [[`1b8d2ca85f`](https://github.com/nodejs/node/commit/1b8d2ca85f)] - **lib**: remove dollar symbol for private function (MaleDong) [#25590](https://github.com/nodejs/node/pull/25590)
- [[`b06f2fafe7`](https://github.com/nodejs/node/commit/b06f2fafe7)] - **lib**: use `internal/options` to query `--abort-on-uncaught-exception` (Joyee Cheung) [#25862](https://github.com/nodejs/node/pull/25862)
- [[`0b302e4520`](https://github.com/nodejs/node/commit/0b302e4520)] - **lib**: fix a few minor issues flagged by lgtm (Robin Neatherway) [#25873](https://github.com/nodejs/node/pull/25873)
- [[`99bc0df74c`](https://github.com/nodejs/node/commit/99bc0df74c)] - **lib**: refactor ERR_SYNTHETIC (cjihrig) [#25749](https://github.com/nodejs/node/pull/25749)
- [[`1c6fadea31`](https://github.com/nodejs/node/commit/1c6fadea31)] - **meta**: clarify EoL platform support (João Reis) [#25838](https://github.com/nodejs/node/pull/25838)
- [[`03ffcf76b7`](https://github.com/nodejs/node/commit/03ffcf76b7)] - **n-api**: finalize during second-pass callback (Gabriel Schulhof) [#25992](https://github.com/nodejs/node/pull/25992)
- [[`5f6a710d8d`](https://github.com/nodejs/node/commit/5f6a710d8d)] - **os,report**: use UV_MAXHOSTNAMESIZE (cjihrig) [#26038](https://github.com/nodejs/node/pull/26038)
- [[`2cbb7a85db`](https://github.com/nodejs/node/commit/2cbb7a85db)] - **(SEMVER-MINOR)** **perf_hooks**: implement histogram based api (James M Snell) [#25378](https://github.com/nodejs/node/pull/25378)
- [[`e81c6c81de`](https://github.com/nodejs/node/commit/e81c6c81de)] - **perf_hooks**: only enable GC tracking when it's requested (Joyee Cheung) [#25853](https://github.com/nodejs/node/pull/25853)
- [[`9d6291ad46`](https://github.com/nodejs/node/commit/9d6291ad46)] - **process**: refactor lib/internal/bootstrap/node.js (Joyee Cheung) [#26033](https://github.com/nodejs/node/pull/26033)
- [[`8d3eb47d48`](https://github.com/nodejs/node/commit/8d3eb47d48)] - **process**: use primordials in bootstrap/node.js (Joyee Cheung) [#26033](https://github.com/nodejs/node/pull/26033)
- [[`85bc64a5c9`](https://github.com/nodejs/node/commit/85bc64a5c9)] - **process**: document the bootstrap process in node.js (Joyee Cheung) [#26033](https://github.com/nodejs/node/pull/26033)
- [[`ae21fca36b`](https://github.com/nodejs/node/commit/ae21fca36b)] - **process**: normalize process.execPath in CreateProcessObject() (Joyee Cheung) [#26002](https://github.com/nodejs/node/pull/26002)
- [[`614bb9f3c8`](https://github.com/nodejs/node/commit/614bb9f3c8)] - **process**: normalize process.argv before user code execution (Joyee Cheung) [#26000](https://github.com/nodejs/node/pull/26000)
- [[`9a7e883b83`](https://github.com/nodejs/node/commit/9a7e883b83)] - **process**: group main thread execution preparation code (Joyee Cheung) [#26000](https://github.com/nodejs/node/pull/26000)
- [[`d7bf070652`](https://github.com/nodejs/node/commit/d7bf070652)] - **process**: move deprecation warning initialization into pre_execution.js (Joyee Cheung) [#25825](https://github.com/nodejs/node/pull/25825)
- [[`d7ed125fd1`](https://github.com/nodejs/node/commit/d7ed125fd1)] - **process**: stub unsupported worker methods (cjihrig) [#25587](https://github.com/nodejs/node/pull/25587)
- [[`c8bf4327d8`](https://github.com/nodejs/node/commit/c8bf4327d8)] - **process**: move process mutation into bootstrap/node.js (Joyee Cheung) [#25821](https://github.com/nodejs/node/pull/25821)
- [[`1d76ba1b3d`](https://github.com/nodejs/node/commit/1d76ba1b3d)] - **(SEMVER-MINOR)** **process**: expose process.features.inspector (Joyee Cheung) [#25819](https://github.com/nodejs/node/pull/25819)
- [[`e6a4fb6d01`](https://github.com/nodejs/node/commit/e6a4fb6d01)] - **process**: split execution into main scripts (Joyee Cheung) [#25667](https://github.com/nodejs/node/pull/25667)
- [[`f4cfbf4c9e`](https://github.com/nodejs/node/commit/f4cfbf4c9e)] - **process**: move setup of process warnings into node.js (Anto Aravinth) [#25263](https://github.com/nodejs/node/pull/25263)
- [[`cc253b5f2d`](https://github.com/nodejs/node/commit/cc253b5f2d)] - **process**: simplify report uncaught exception logic (cjihrig) [#25744](https://github.com/nodejs/node/pull/25744)
- [[`4c22d6eaa1`](https://github.com/nodejs/node/commit/4c22d6eaa1)] - **(SEMVER-MINOR)** **repl**: add repl.setupHistory for programmatic repl (Lance Ball) [#25895](https://github.com/nodejs/node/pull/25895)
- [[`2c737a89d5`](https://github.com/nodejs/node/commit/2c737a89d5)] - **repl**: remove obsolete buffer clearing (Ruben Bridgewater) [#25731](https://github.com/nodejs/node/pull/25731)
- [[`5aaeb01655`](https://github.com/nodejs/node/commit/5aaeb01655)] - **repl**: fix eval return value (Ruben Bridgewater) [#25731](https://github.com/nodejs/node/pull/25731)
- [[`e66cb58a9b`](https://github.com/nodejs/node/commit/e66cb58a9b)] - **repl**: simplify and improve completion (Ruben Bridgewater) [#25731](https://github.com/nodejs/node/pull/25731)
- [[`dfd47aa1e8`](https://github.com/nodejs/node/commit/dfd47aa1e8)] - **report**: make more items programmatically accessible (Anna Henningsen) [#26019](https://github.com/nodejs/node/pull/26019)
- [[`88019b051c`](https://github.com/nodejs/node/commit/88019b051c)] - **report**: rename setDiagnosticReportOptions() (cjihrig) [#25990](https://github.com/nodejs/node/pull/25990)
- [[`c8ceece815`](https://github.com/nodejs/node/commit/c8ceece815)] - **report**: refactor report option validation (cjihrig) [#25990](https://github.com/nodejs/node/pull/25990)
- [[`afb2d17c16`](https://github.com/nodejs/node/commit/afb2d17c16)] - **report**: use uv_getnameinfo() for socket endpoints (cjihrig) [#25962](https://github.com/nodejs/node/pull/25962)
- [[`3f400310bd`](https://github.com/nodejs/node/commit/3f400310bd)] - **report**: widen scope of #ifndef (cjihrig) [#25960](https://github.com/nodejs/node/pull/25960)
- [[`8494a61d79`](https://github.com/nodejs/node/commit/8494a61d79)] - **report**: remove unnecessary case block scopes (cjihrig) [#25960](https://github.com/nodejs/node/pull/25960)
- [[`7443288c68`](https://github.com/nodejs/node/commit/7443288c68)] - **report**: remove empty string stream insertion (cjihrig) [#25960](https://github.com/nodejs/node/pull/25960)
- [[`6c51ec3014`](https://github.com/nodejs/node/commit/6c51ec3014)] - **report**: include information about event loop itself (Anna Henningsen) [#25906](https://github.com/nodejs/node/pull/25906)
- [[`30a4e8900a`](https://github.com/nodejs/node/commit/30a4e8900a)] - **report**: print libuv handle addresses as hex (cjihrig) [#25910](https://github.com/nodejs/node/pull/25910)
- [[`6d39a54354`](https://github.com/nodejs/node/commit/6d39a54354)] - **report**: use libuv calls for OS and machine info (cjihrig) [#25900](https://github.com/nodejs/node/pull/25900)
- [[`1007416596`](https://github.com/nodejs/node/commit/1007416596)] - **report**: separate release metadata (Richard Lau) [#25826](https://github.com/nodejs/node/pull/25826)
- [[`b1e0c43abd`](https://github.com/nodejs/node/commit/b1e0c43abd)] - **report**: disambiguate glibc versions (cjihrig) [#25781](https://github.com/nodejs/node/pull/25781)
- [[`f6c8820b46`](https://github.com/nodejs/node/commit/f6c8820b46)] - **report**: fix typo in error message (cjihrig) [#25782](https://github.com/nodejs/node/pull/25782)
- [[`d4631816ef`](https://github.com/nodejs/node/commit/d4631816ef)] - **report**: use consistent format for dumpEventTime (Anna Henningsen) [#25751](https://github.com/nodejs/node/pull/25751)
- [[`cc22fd7be9`](https://github.com/nodejs/node/commit/cc22fd7be9)] - **report**: split up osVersion and machine values (cjihrig) [#25755](https://github.com/nodejs/node/pull/25755)
- [[`f71d6762ca`](https://github.com/nodejs/node/commit/f71d6762ca)] - **src**: remove redundant cast in node_http2.h (gengjiawen) [#25978](https://github.com/nodejs/node/pull/25978)
- [[`adaa2ae70b`](https://github.com/nodejs/node/commit/adaa2ae70b)] - **src**: add lock to inspector `MainThreadHandle` dtor (Anna Henningsen) [#26010](https://github.com/nodejs/node/pull/26010)
- [[`731c2731d2`](https://github.com/nodejs/node/commit/731c2731d2)] - **src**: add WeakReference utility (Anna Henningsen) [#25993](https://github.com/nodejs/node/pull/25993)
- [[`7ab34ae421`](https://github.com/nodejs/node/commit/7ab34ae421)] - **src**: remove unused method in class Http2Stream (gengjiawen) [#25979](https://github.com/nodejs/node/pull/25979)
- [[`d7ae1054ef`](https://github.com/nodejs/node/commit/d7ae1054ef)] - **src**: remove redundant cast in node_file.cc (gengjiawen) [#25977](https://github.com/nodejs/node/pull/25977)
- [[`6c6e678eaa`](https://github.com/nodejs/node/commit/6c6e678eaa)] - **src**: remove unused class in node_errors.h (gengjiawen) [#25980](https://github.com/nodejs/node/pull/25980)
- [[`24d9e9c8b6`](https://github.com/nodejs/node/commit/24d9e9c8b6)] - **src**: remove redundant void (gengjiawen) [#26003](https://github.com/nodejs/node/pull/26003)
- [[`5de103430f`](https://github.com/nodejs/node/commit/5de103430f)] - **src**: use NULL check macros to check nullptr (ZYSzys) [#25916](https://github.com/nodejs/node/pull/25916)
- [[`c47eb932bc`](https://github.com/nodejs/node/commit/c47eb932bc)] - **src**: move process.reallyExit impl into node_process_methods.cc (Joyee Cheung) [#25860](https://github.com/nodejs/node/pull/25860)
- [[`01bb7b7559`](https://github.com/nodejs/node/commit/01bb7b7559)] - **src**: split ownsProcessState off isMainThread (Anna Henningsen) [#25881](https://github.com/nodejs/node/pull/25881)
- [[`fd6ce533aa`](https://github.com/nodejs/node/commit/fd6ce533aa)] - **src**: remove main_isolate (Anna Henningsen) [#25823](https://github.com/nodejs/node/pull/25823)
- [[`b72ec23201`](https://github.com/nodejs/node/commit/b72ec23201)] - **src**: move public C++ APIs into src/api/\*.cc (Joyee Cheung) [#25541](https://github.com/nodejs/node/pull/25541)
- [[`0a154ff7ad`](https://github.com/nodejs/node/commit/0a154ff7ad)] - **src**: move v8_platform implementation into node_v8_platform-inl.h (Joyee Cheung) [#25541](https://github.com/nodejs/node/pull/25541)
- [[`d342707fa7`](https://github.com/nodejs/node/commit/d342707fa7)] - **src**: remove unused `internalBinding('config')` properties (Joyee Cheung) [#25463](https://github.com/nodejs/node/pull/25463)
- [[`756558617e`](https://github.com/nodejs/node/commit/756558617e)] - **src**: pass cli options to bootstrap/loaders.js lexically (Joyee Cheung) [#25463](https://github.com/nodejs/node/pull/25463)
- [[`85d5f67efe`](https://github.com/nodejs/node/commit/85d5f67efe)] - **src**: fix return type in Hash (gengjiawen) [#25936](https://github.com/nodejs/node/pull/25936)
- [[`779a5773cf`](https://github.com/nodejs/node/commit/779a5773cf)] - **src**: refactor macro to std::min in node_buffer.cc (gengjiawen) [#25919](https://github.com/nodejs/node/pull/25919)
- [[`76687dedce`](https://github.com/nodejs/node/commit/76687dedce)] - **src**: remove unused variable (cjihrig) [#25481](https://github.com/nodejs/node/pull/25481)
- [[`b280d90279`](https://github.com/nodejs/node/commit/b280d90279)] - **src**: simplify NativeModule caching and remove redundant data (Joyee Cheung) [#25352](https://github.com/nodejs/node/pull/25352)
- [[`469cdacd59`](https://github.com/nodejs/node/commit/469cdacd59)] - **src**: pass along errors from StreamBase req obj creations (Anna Henningsen) [#25822](https://github.com/nodejs/node/pull/25822)
- [[`d6f3b8785f`](https://github.com/nodejs/node/commit/d6f3b8785f)] - **src**: pass along errors from fs object creations (Anna Henningsen) [#25822](https://github.com/nodejs/node/pull/25822)
- [[`0672c24dc3`](https://github.com/nodejs/node/commit/0672c24dc3)] - **src**: pass along errors from http2 object creation (Anna Henningsen) [#25822](https://github.com/nodejs/node/pull/25822)
- [[`e3fd7520d0`](https://github.com/nodejs/node/commit/e3fd7520d0)] - **src**: pass along errors from tls object creation (Anna Henningsen) [#25822](https://github.com/nodejs/node/pull/25822)
- [[`e0af205c98`](https://github.com/nodejs/node/commit/e0af205c98)] - **src**: nullcheck on trace controller (Gireesh Punathil) [#25943](https://github.com/nodejs/node/pull/25943)
- [[`c72c4b041d`](https://github.com/nodejs/node/commit/c72c4b041d)] - **src**: allow --perf-prof-unwinding-info in NODE_OPTIONS (Tom Gallacher) [#25565](https://github.com/nodejs/node/pull/25565)
- [[`e6a2548807`](https://github.com/nodejs/node/commit/e6a2548807)] - **src**: allow --perf-basic-prof-only-functions in NODE_OPTIONS (Tom Gallacher) [#25565](https://github.com/nodejs/node/pull/25565)
- [[`7cf484c656`](https://github.com/nodejs/node/commit/7cf484c656)] - **src**: refactor SSLError case statement (Sam Roberts) [#25861](https://github.com/nodejs/node/pull/25861)
- [[`55a313bb31`](https://github.com/nodejs/node/commit/55a313bb31)] - **src**: make watchdog async callback a lambda (Gireesh Punathil) [#25945](https://github.com/nodejs/node/pull/25945)
- [[`de9f37d314`](https://github.com/nodejs/node/commit/de9f37d314)] - **src**: make deleted function public in agent.h (gengjiawen) [#25909](https://github.com/nodejs/node/pull/25909)
- [[`620d429343`](https://github.com/nodejs/node/commit/620d429343)] - **src**: use bool instead of integer literal in connection_wrap.cc (gengjiawen) [#25923](https://github.com/nodejs/node/pull/25923)
- [[`8cedfb8196`](https://github.com/nodejs/node/commit/8cedfb8196)] - **src**: refactor to nullptr in cpp code (gengjiawen) [#25888](https://github.com/nodejs/node/pull/25888)
- [[`f5d50342b0`](https://github.com/nodejs/node/commit/f5d50342b0)] - **src**: clean unused code in agent.h (gengjiawen) [#25914](https://github.com/nodejs/node/pull/25914)
- [[`2d575044ff`](https://github.com/nodejs/node/commit/2d575044ff)] - **src**: fix compiler warnings in node_buffer.cc (Daniel Bevenius) [#25665](https://github.com/nodejs/node/pull/25665)
- [[`015ed0b1d7`](https://github.com/nodejs/node/commit/015ed0b1d7)] - **src**: remove redundant method in node_worker.h (gengjiawen) [#25849](https://github.com/nodejs/node/pull/25849)
- [[`44655e93dd`](https://github.com/nodejs/node/commit/44655e93dd)] - **src**: delete unreachable code in node_perf.h (gengjiawen) [#25850](https://github.com/nodejs/node/pull/25850)
- [[`5a66e380ff`](https://github.com/nodejs/node/commit/5a66e380ff)] - **src**: fix data type in node_crypto.cc (gengjiawen) [#25889](https://github.com/nodejs/node/pull/25889)
- [[`d4c4f77d31`](https://github.com/nodejs/node/commit/d4c4f77d31)] - **src**: const_cast is necessary for 1.1.1, not 0.9.7 (Sam Roberts) [#25861](https://github.com/nodejs/node/pull/25861)
- [[`b5a8376ffe`](https://github.com/nodejs/node/commit/b5a8376ffe)] - **src**: organize TLSWrap declarations by parent (Sam Roberts) [#25861](https://github.com/nodejs/node/pull/25861)
- [[`0772ce35fb`](https://github.com/nodejs/node/commit/0772ce35fb)] - **src**: remove unused TLWrap::EnableTrace() (Sam Roberts) [#25861](https://github.com/nodejs/node/pull/25861)
- [[`703549665e`](https://github.com/nodejs/node/commit/703549665e)] - **src**: add PrintLibuvHandleInformation debug helper (Anna Henningsen) [#25905](https://github.com/nodejs/node/pull/25905)
- [[`2e80b912ef`](https://github.com/nodejs/node/commit/2e80b912ef)] - **src**: use `visibility("default")` exports on POSIX (Jeremy Apthorp) [#25893](https://github.com/nodejs/node/pull/25893)
- [[`e28d891788`](https://github.com/nodejs/node/commit/e28d891788)] - **src**: fix race condition in `~NodeTraceBuffer` (Anna Henningsen) [#25896](https://github.com/nodejs/node/pull/25896)
- [[`bd771d90fd`](https://github.com/nodejs/node/commit/bd771d90fd)] - **src**: remove unimplemented method in node_http2.h (gengjiawen) [#25732](https://github.com/nodejs/node/pull/25732)
- [[`00f8e86702`](https://github.com/nodejs/node/commit/00f8e86702)] - **src**: use nullptr in node_buffer.cc (gengjiawen) [#25820](https://github.com/nodejs/node/pull/25820)
- [[`84358b5010`](https://github.com/nodejs/node/commit/84358b5010)] - **src**: handle errors while printing error objects (Anna Henningsen) [#25834](https://github.com/nodejs/node/pull/25834)
- [[`f027290542`](https://github.com/nodejs/node/commit/f027290542)] - **src**: use struct as arguments to node::Assert (Anna Henningsen) [#25869](https://github.com/nodejs/node/pull/25869)
- [[`8a8c17880e`](https://github.com/nodejs/node/commit/8a8c17880e)] - **src**: remove unused AsyncResource constructor in node.h (gengjiawen) [#25793](https://github.com/nodejs/node/pull/25793)
- [[`7556994d83`](https://github.com/nodejs/node/commit/7556994d83)] - **src**: remove unused method in js_stream.h (gengjiawen) [#25790](https://github.com/nodejs/node/pull/25790)
- [[`882902c672`](https://github.com/nodejs/node/commit/882902c672)] - **src**: pass along errors from PromiseWrap instantiation (Anna Henningsen) [#25837](https://github.com/nodejs/node/pull/25837)
- [[`998cea567f`](https://github.com/nodejs/node/commit/998cea567f)] - **src**: workaround MSVC compiler bug (Refael Ackermann) [#25596](https://github.com/nodejs/node/pull/25596)
- [[`b779c072d0`](https://github.com/nodejs/node/commit/b779c072d0)] - **src**: make `StreamPipe::Unpipe()` more resilient (Anna Henningsen) [#25716](https://github.com/nodejs/node/pull/25716)
- [[`0b014d5299`](https://github.com/nodejs/node/commit/0b014d5299)] - **src**: make deleted functions public in node.h (gengjiawen) [#25764](https://github.com/nodejs/node/pull/25764)
- [[`be499c3c7b`](https://github.com/nodejs/node/commit/be499c3c7b)] - **src**: simplify SlicedArguments (Anna Henningsen) [#25745](https://github.com/nodejs/node/pull/25745)
- [[`35454e0008`](https://github.com/nodejs/node/commit/35454e0008)] - **src**: fix indentation in a few node_http2 enums (Daniel Bevenius) [#25761](https://github.com/nodejs/node/pull/25761)
- [[`3f080d12f4`](https://github.com/nodejs/node/commit/3f080d12f4)] - **src**: add debug check for inspector uv_async_t (Anna Henningsen) [#25777](https://github.com/nodejs/node/pull/25777)
- [[`0949039d26`](https://github.com/nodejs/node/commit/0949039d26)] - **src**: add handle scope to `OnFatalError()` (Anna Henningsen) [#25775](https://github.com/nodejs/node/pull/25775)
- [[`d9c2690705`](https://github.com/nodejs/node/commit/d9c2690705)] - **src**: turn ROUND_UP into an inline function (Anna Henningsen) [#25743](https://github.com/nodejs/node/pull/25743)
- [[`3cd134cec4`](https://github.com/nodejs/node/commit/3cd134cec4)] - **src,lib**: remove dead `process.binding()` code (Anna Henningsen) [#25829](https://github.com/nodejs/node/pull/25829)
- [[`cb86357d22`](https://github.com/nodejs/node/commit/cb86357d22)] - **test**: replaced anonymous fn with arrow syntax (Pushkal B) [#26029](https://github.com/nodejs/node/pull/26029)
- [[`64cc234a84`](https://github.com/nodejs/node/commit/64cc234a84)] - **test**: use emitter.listenerCount() in test-http-connect (Luigi Pinca) [#26031](https://github.com/nodejs/node/pull/26031)
- [[`6323a9fc57`](https://github.com/nodejs/node/commit/6323a9fc57)] - **test**: refactor two http client timeout tests (Luigi Pinca) [#25473](https://github.com/nodejs/node/pull/25473)
- [[`61330b2f84`](https://github.com/nodejs/node/commit/61330b2f84)] - **test**: add assert test for position indicator (Rich Trott) [#26024](https://github.com/nodejs/node/pull/26024)
- [[`896962fd08`](https://github.com/nodejs/node/commit/896962fd08)] - **test**: add `Worker` + `--prof` regression test (Anna Henningsen) [#26011](https://github.com/nodejs/node/pull/26011)
- [[`3eb6f6130a`](https://github.com/nodejs/node/commit/3eb6f6130a)] - **test**: capture stderr from child processes (Gireesh Punathil) [#26007](https://github.com/nodejs/node/pull/26007)
- [[`d123f944a2`](https://github.com/nodejs/node/commit/d123f944a2)] - **test**: remove extraneous report validation argument (cjihrig) [#25986](https://github.com/nodejs/node/pull/25986)
- [[`de587bae8a`](https://github.com/nodejs/node/commit/de587bae8a)] - **test**: refactor to block-scope (LakshmiSwethaG) [#25532](https://github.com/nodejs/node/pull/25532)
- [[`4dca3ab23d`](https://github.com/nodejs/node/commit/4dca3ab23d)] - **test**: exit sequence sanity tests (Gireesh Punathil) [#25085](https://github.com/nodejs/node/pull/25085)
- [[`ef9139e5a0`](https://github.com/nodejs/node/commit/ef9139e5a0)] - **test**: end tls gracefully, rather than destroy (Sam Roberts) [#25508](https://github.com/nodejs/node/pull/25508)
- [[`7e9f5ea295`](https://github.com/nodejs/node/commit/7e9f5ea295)] - **test**: pin regression test for #8074 to TLS 1.2 (Sam Roberts) [#25508](https://github.com/nodejs/node/pull/25508)
- [[`1b9a608dca`](https://github.com/nodejs/node/commit/1b9a608dca)] - **test**: refactor test-http-agent-timeout-option (Luigi Pinca) [#25886](https://github.com/nodejs/node/pull/25886)
- [[`c457d007cd`](https://github.com/nodejs/node/commit/c457d007cd)] - **test**: clarify confusion over "client" in comment (Sam Roberts) [#25508](https://github.com/nodejs/node/pull/25508)
- [[`1be867685c`](https://github.com/nodejs/node/commit/1be867685c)] - **test**: use mustCall(), not global state checks (Sam Roberts) [#25508](https://github.com/nodejs/node/pull/25508)
- [[`50d2c8e945`](https://github.com/nodejs/node/commit/50d2c8e945)] - **test**: use common.mustCall(), and log the events (Sam Roberts) [#25508](https://github.com/nodejs/node/pull/25508)
- [[`1b542e8ba0`](https://github.com/nodejs/node/commit/1b542e8ba0)] - **test**: use mustCall in ephemeralkeyinfo test (Sam Roberts) [#25508](https://github.com/nodejs/node/pull/25508)
- [[`898cf782f8`](https://github.com/nodejs/node/commit/898cf782f8)] - **test**: send a bad record only after connection done (Sam Roberts) [#25508](https://github.com/nodejs/node/pull/25508)
- [[`ace267b21c`](https://github.com/nodejs/node/commit/ace267b21c)] - **test**: do not race connection and rejection (Sam Roberts) [#25508](https://github.com/nodejs/node/pull/25508)
- [[`639dc07ca0`](https://github.com/nodejs/node/commit/639dc07ca0)] - **test**: do not assume tls handshake order (Sam Roberts) [#25508](https://github.com/nodejs/node/pull/25508)
- [[`cc6b30f4b7`](https://github.com/nodejs/node/commit/cc6b30f4b7)] - **test**: do not assume server gets secure connection (Sam Roberts) [#25508](https://github.com/nodejs/node/pull/25508)
- [[`c17a37d95a`](https://github.com/nodejs/node/commit/c17a37d95a)] - **test**: wait for TCP connect, not TLS handshake (Sam Roberts) [#25508](https://github.com/nodejs/node/pull/25508)
- [[`1f8991fae6`](https://github.com/nodejs/node/commit/1f8991fae6)] - **test**: add util.isDeepStrictEqual edge case tests (Rich Trott) [#25932](https://github.com/nodejs/node/pull/25932)
- [[`baa10aeb75`](https://github.com/nodejs/node/commit/baa10aeb75)] - **test**: add BigInt test for isDeepStrictEqual (Rich Trott) [#25932](https://github.com/nodejs/node/pull/25932)
- [[`c866b52942`](https://github.com/nodejs/node/commit/c866b52942)] - **test**: remove obsolete code (Ruben Bridgewater) [#25731](https://github.com/nodejs/node/pull/25731)
- [[`ee3165d6e7`](https://github.com/nodejs/node/commit/ee3165d6e7)] - **test**: relax expectations in test-icu-transcode (Yang Guo) [#25866](https://github.com/nodejs/node/pull/25866)
- [[`025a7c3e31`](https://github.com/nodejs/node/commit/025a7c3e31)] - **test**: do not fail SLOW tests if they are not slow (Yang Guo) [#25868](https://github.com/nodejs/node/pull/25868)
- [[`059d30e369`](https://github.com/nodejs/node/commit/059d30e369)] - **test**: add hasCrypto to worker-cleanexit-with-moduleload (Daniel Bevenius) [#25811](https://github.com/nodejs/node/pull/25811)
- [[`7cb943937f`](https://github.com/nodejs/node/commit/7cb943937f)] - **test**: refactor test-http-agent-timeout-option (Luigi Pinca) [#25854](https://github.com/nodejs/node/pull/25854)
- [[`cdf3e84804`](https://github.com/nodejs/node/commit/cdf3e84804)] - **test**: exclude additional test for coverage (Michael Dawson) [#25833](https://github.com/nodejs/node/pull/25833)
- [[`704a440d52`](https://github.com/nodejs/node/commit/704a440d52)] - **test**: allow coverage threshold to be enforced (Benjamin Coe) [#25675](https://github.com/nodejs/node/pull/25675)
- [[`5bffcf6246`](https://github.com/nodejs/node/commit/5bffcf6246)] - **test**: run html/webappapis/timers WPT (Joyee Cheung) [#25618](https://github.com/nodejs/node/pull/25618)
- [[`579220815a`](https://github.com/nodejs/node/commit/579220815a)] - **test**: pull html/webappapis/timers WPT (Joyee Cheung) [#25618](https://github.com/nodejs/node/pull/25618)
- [[`d683da7ffa`](https://github.com/nodejs/node/commit/d683da7ffa)] - **test, tools**: suppress addon function cast warnings (Daniel Bevenius) [#25663](https://github.com/nodejs/node/pull/25663)
- [[`2009f18064`](https://github.com/nodejs/node/commit/2009f18064)] - **test,tracing**: use close event to wait for stdio (Anna Henningsen) [#25894](https://github.com/nodejs/node/pull/25894)
- [[`8495a788c6`](https://github.com/nodejs/node/commit/8495a788c6)] - **tls**: renegotiate should take care of its own state (Sam Roberts) [#25997](https://github.com/nodejs/node/pull/25997)
- [[`fb83f842a8`](https://github.com/nodejs/node/commit/fb83f842a8)] - **tls**: in-line comments and other cleanups (Sam Roberts) [#25861](https://github.com/nodejs/node/pull/25861)
- [[`4d0b56f3f7`](https://github.com/nodejs/node/commit/4d0b56f3f7)] - **tls**: don't shadow the tls global with a local (Sam Roberts) [#25861](https://github.com/nodejs/node/pull/25861)
- [[`7656d58eed`](https://github.com/nodejs/node/commit/7656d58eed)] - **(SEMVER-MINOR)** **tls**: introduce client 'session' event (Sam Roberts) [#25831](https://github.com/nodejs/node/pull/25831)
- [[`6ca8d26020`](https://github.com/nodejs/node/commit/6ca8d26020)] - **tools**: apply more stringent lint rules for benchmark code (Rich Trott) [#25944](https://github.com/nodejs/node/pull/25944)
- [[`c55d662bd1`](https://github.com/nodejs/node/commit/c55d662bd1)] - **tools**: replace deprecated ESLint configuration (Rich Trott) [#25877](https://github.com/nodejs/node/pull/25877)
- [[`e13c1850d2`](https://github.com/nodejs/node/commit/e13c1850d2)] - **tools**: update ESLint to 5.13.0 (Rich Trott) [#25877](https://github.com/nodejs/node/pull/25877)
- [[`8d14870b15`](https://github.com/nodejs/node/commit/8d14870b15)] - **tools**: update dmn in update-estlint.sh (Rich Trott) [#25877](https://github.com/nodejs/node/pull/25877)
- [[`988c7141d4`](https://github.com/nodejs/node/commit/988c7141d4)] - **tools**: improve prerequisites for test-all-suites (Rich Trott) [#25892](https://github.com/nodejs/node/pull/25892)
- [[`f395728b32`](https://github.com/nodejs/node/commit/f395728b32)] - **tools**: exclude benchmark code from coverage report (Rich Trott) [#25841](https://github.com/nodejs/node/pull/25841)
- [[`9d2ea1802b`](https://github.com/nodejs/node/commit/9d2ea1802b)] - **tools**: add test-all-suites to Makefile (Rich Trott) [#25799](https://github.com/nodejs/node/pull/25799)
- [[`9f1bcd44df`](https://github.com/nodejs/node/commit/9f1bcd44df)] - **tools**: make test.py Python 3 compatible (Sakthipriyan Vairamani (thefourtheye)) [#25767](https://github.com/nodejs/node/pull/25767)
- [[`454278a701`](https://github.com/nodejs/node/commit/454278a701)] - **tools**: refloat Node.js patches to cpplint.py (Refael Ackermann) [#25771](https://github.com/nodejs/node/pull/25771)
- [[`b9289f41af`](https://github.com/nodejs/node/commit/b9289f41af)] - **tools**: bump cpplint.py to 3d8f6f876d (Refael Ackermann) [#25771](https://github.com/nodejs/node/pull/25771)
- [[`9c9aefe2a0`](https://github.com/nodejs/node/commit/9c9aefe2a0)] - **worker**: set stack size for worker threads (Anna Henningsen) [#26049](https://github.com/nodejs/node/pull/26049)
- [[`23868ba45e`](https://github.com/nodejs/node/commit/23868ba45e)] - **worker**: keep stdio after exit (Anna Henningsen) [#26017](https://github.com/nodejs/node/pull/26017)
- [[`6c1e92817f`](https://github.com/nodejs/node/commit/6c1e92817f)] - **worker**: set up child Isolate inside Worker thread (Anna Henningsen) [#26011](https://github.com/nodejs/node/pull/26011)
- [[`1764aae193`](https://github.com/nodejs/node/commit/1764aae193)] - **worker**: pre-allocate thread id (Anna Henningsen) [#26011](https://github.com/nodejs/node/pull/26011)
- [[`f63817fd38`](https://github.com/nodejs/node/commit/f63817fd38)] - **worker**: refactor thread id management (Anna Henningsen) [#25796](https://github.com/nodejs/node/pull/25796)
- [[`8db6b8a95a`](https://github.com/nodejs/node/commit/8db6b8a95a)] - **worker**: move worker thread setup code into the main script (Joyee Cheung) [#25667](https://github.com/nodejs/node/pull/25667)
- [[`5d2e064973`](https://github.com/nodejs/node/commit/5d2e064973)] - **worker**: no throw on property access/postMessage after termination (Christopher Jeffrey) [#25871](https://github.com/nodejs/node/pull/25871)
- [[`508a2e7f0f`](https://github.com/nodejs/node/commit/508a2e7f0f)] - **worker**: use correct ctor for error serialization (Anna Henningsen) [#25951](https://github.com/nodejs/node/pull/25951)
- [[`52d4b7a928`](https://github.com/nodejs/node/commit/52d4b7a928)] - **worker**: remove undocumented .onclose property (Rich Trott) [#25904](https://github.com/nodejs/node/pull/25904)
- [[`e70aa30ebd`](https://github.com/nodejs/node/commit/e70aa30ebd)] - **worker**: add mutex lock to MessagePort ctor (Anna Henningsen) [#25911](https://github.com/nodejs/node/pull/25911)
- [[`55c270253b`](https://github.com/nodejs/node/commit/55c270253b)] - **worker**: throw for duplicates in transfer list (Anna Henningsen) [#25815](https://github.com/nodejs/node/pull/25815)
- [[`c959d60242`](https://github.com/nodejs/node/commit/c959d60242)] - **worker,etw**: only enable ETW on the main thread (Anna Henningsen) [#25907](https://github.com/nodejs/node/pull/25907)

Windows 32-bit Installer: https://nodejs.org/dist/v11.10.0/node-v11.10.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v11.10.0/node-v11.10.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v11.10.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v11.10.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v11.10.0/node-v11.10.0.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v11.10.0/node-v11.10.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v11.10.0/node-v11.10.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v11.10.0/node-v11.10.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v11.10.0/node-v11.10.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v11.10.0/node-v11.10.0-aix-ppc64.tar.gz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v11.10.0/node-v11.10.0-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v11.10.0/node-v11.10.0-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v11.10.0/node-v11.10.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v11.10.0/node-v11.10.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v11.10.0/node-v11.10.0.tar.gz \
Other release files: https://nodejs.org/dist/v11.10.0/ \
Documentation: https://nodejs.org/docs/v11.10.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

c9526a855f9322330caa8f800de8cd734895d8575292286f5576f62c614aa5b4  node-v11.10.0-aix-ppc64.tar.gz
1956528c6f3934a97508e36a4855c154f40f1923ccd61e296d5a85679103e3a1  node-v11.10.0-darwin-x64.tar.gz
6855b539537ae448e03b8490c9c470160b668ccd82d9937b7bdfb3fb6fa5880d  node-v11.10.0-darwin-x64.tar.xz
17ec75ef5d54f8b26faa31d9d70e98a32f333e0b5398608099e4104fb3492c73  node-v11.10.0-headers.tar.gz
c9136ffd91eaf82eb363a734dae0a26bcaa9cadb94af9dfd6e37560e1582b047  node-v11.10.0-headers.tar.xz
9407ff7019f1d6048134443638bb9473543ebdd8de268057eb929aaa044e6180  node-v11.10.0-linux-arm64.tar.gz
60ed6caa90d8188a55f0dbc63f4aef263fb4863e036d32989b820a2e40582c66  node-v11.10.0-linux-arm64.tar.xz
148ba015bf25bf3c52bde3a6a6baa9f11fcfd6918085b7ae24b740cab38667b9  node-v11.10.0-linux-armv6l.tar.gz
f832c6026286e02b8f9569bb8dcea3832f71ec634bcbde2e97e740180977ebdf  node-v11.10.0-linux-armv6l.tar.xz
7e9c8a52d34e972c588609ac9de4698991565be913a5537f2b667591b955886d  node-v11.10.0-linux-armv7l.tar.gz
dac3961b7d25b54a8d9a4e84f7d8ac2c68a2fee11b744dfa1fa115701c1213d2  node-v11.10.0-linux-armv7l.tar.xz
666d137bbe386f4c4efdc09cc35ef976a874cf7122f1f429163f738cd1957906  node-v11.10.0-linux-ppc64le.tar.gz
567a8670d44a93ea2bc09115ae41724eb4a1c6a7ced2a4944c358bfb44c5d5a5  node-v11.10.0-linux-ppc64le.tar.xz
74e4feef88571d608d96b63cec82f0b26fd139b99760e592ab29dca1c7c3b0dd  node-v11.10.0-linux-s390x.tar.gz
f5015866e659d00746bedddd29707a56c8b14f3869ef1f4a1b8e98d085547f80  node-v11.10.0-linux-s390x.tar.xz
4117de50800ecc6d5f7a9c3989d5497fa9dd37df87a904ac4d49948ab10d39ba  node-v11.10.0-linux-x64.tar.gz
fe4c617aaf88b5228bce0341d1c77bbae2622d69eaa17a15b7d4bcc60c4777c5  node-v11.10.0-linux-x64.tar.xz
d24d423fd69921e12a2ea8be1bf8ee0f0287132bbdd487a58ce2ba55d901eeb0  node-v11.10.0.pkg
8bb2b4126d7334f5f6213598b40f3bfa305ffac9ace96f6a0538f3b290a33c78  node-v11.10.0-sunos-x64.tar.gz
2f9a61f76c316dc2e0710df8c457cc7dc22500886a5fa6d71423bc03fbc798a9  node-v11.10.0-sunos-x64.tar.xz
45835c210955cd05cab259e664cc19a6f2748dbda6bc9e13edc9a2e8cc498770  node-v11.10.0.tar.gz
9fc2ac66ae4608c4c4bd6accc3f0af58ec52dd45fe35c9c0b4059e377119d1cf  node-v11.10.0.tar.xz
f09b12011d4928d73e402190705a39cee0114c466c2d84341030603aef20691d  node-v11.10.0-win-x64.7z
c39e711aebe678455fa74edf6d8f6184d6d93e20f160197799040a0c17005dba  node-v11.10.0-win-x64.zip
6c135cdbccda813b24417bf7316d3cbbc7979d8b6b4ecaff3d62a8714d38ce11  node-v11.10.0-win-x86.7z
f59a0f5ca466b0b41fab14cf5fc5a3ff0f828dce8b03f5fb72504e7c2e2bc0d7  node-v11.10.0-win-x86.zip
ddad334110a914de93c600a780e53e35ca68209d7baf5bd1d3c8805f75a97b77  node-v11.10.0-x64.msi
cd4614ded8e30b371ec1ea37cdf40fcafb6aad659d7c468b99740f02c3b0a10c  node-v11.10.0-x86.msi
b951d239c7bab8888af7329c8aad2cc1fb99cb1c007c17a4ac2488fe8c216c06  win-x64/node.exe
36a01d94f5c136fd0198f92ad345acff844aee0316c96ba20a98ecfb456fa895  win-x64/node.lib
7b318606b89c9a2d5fe3762fd9dde54f8172f6c00c8c82745396f40194037304  win-x64/node_pdb.7z
288c867faa73fb89d7f148c6f89d81afde0c5937b9b8f29ae443483cf1fae1f5  win-x64/node_pdb.zip
0b5f859f43bbb37554c2d9c9271b373239173779aca13546acfb2f9147d197d7  win-x86/node.exe
a75ad641b7187792b5e3dcb3997fc80a6b9a309d3c5f9cd87e1e9eb48fe49374  win-x86/node.lib
668075e77c1cadaa921da1b19f56b6acb153d6de31aed0afbb359837a84300ef  win-x86/node_pdb.7z
2770a18f94df5f3be2d3ef21182ac120e138ea0c4e9b67d445b6bcbbecbafc0a  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEj8yhP+8dDC6RAI4Jdw96mlrhVgAFAlxl8nQACgkQdw96mlrh
VgAOGw//UNnR03QjdMZG8219+vRZSCp6qtr9kikjfWv9pyButIKQtfTLl+s1muwM
M7+19ohh8lYkeVYsHSxV/iz4D9TDD7dNO8dJbA78HdUtRVNxHDulH2WoeK4+Oh+E
16P+D1RpQJHIiD1lUpbdtkYWrQ6ovuYhil4n/vx43ejhBdLrzXgrAZ530rc3G0xP
ggP2rowPsio+5NFm0F6drlJqy1un065x/JKGroWSJRZHU4d+SKhDWvtVwNBOFM4/
X7U47M2ChgkPr7WtjwUxYmmeSAGZt3r27b0NdoZuaHE7eNsCcXiF/Yi0gYu+1BR6
JNWOD5/BwhVY7lw+l2voI2McpodIDk//VzRXepRRpaaCXcV26OfvjFsPkpBlPtRH
/bm4p8CUrl2Wt6UYZ6NUkoHP9mPkDrOu2etyHz5fVZ+q8WBzTEWEh5e+JLIrBatD
XnQ9ANk0Ntp+c5vGtDCjkMmpkSfrV6lh4sJYNwFKelJeWz659xCUYs7r/wswiK/x
Acls6+1Y8YUtihu8aZIRT8s7F9ZPr8tnR5ebd4eM+A7DoqW+gagm1J2DHgMTmz+b
JPABvn4U0CUPmaNc7WmdGDCHkCn1AgNiyyHxSKyEMgtGOESB79oncOLIQhZYiAC1
ubD3mpgIDvvQQtU6vwW1g750GI+bbbWhPi5tFTITo9G62iqpJnc=
=14Nn
-----END PGP SIGNATURE-----

```
