---
date: '2016-02-09T17:32:54.296Z'
category: release
title: Node v4.3.0 (LTS)
layout: blog-post
author: <PERSON>
---

This is an important security release. For full details see /blog/vulnerability/february-2016-security-releases/ for details on patched vulnerabilities.

### Notable changes

- **http**: fix defects in HTTP header parsing for requests and responses that can allow request smuggling (CVE-2016-2086) or response splitting (CVE-2016-2216). HTTP header parsing now aligns more closely with the HTTP spec including restricting the acceptable characters.
- **http-parser**: upgrade from 2.5.0 to 2.5.1
- **openssl**: upgrade from 1.0.2e to 1.0.2f. To mitigate against the Logjam attack, TLS clients now reject <PERSON><PERSON><PERSON>-<PERSON><PERSON> handshakes with parameters shorter than 1024-bits, up from the previous limit of 768-bits.
- **src**:
  - introduce new `--security-revert={cvenum}` command line flag for selective reversion of specific CVE fixes
  - allow the fix for CVE-2016-2216 to be selectively reverted using `--security-revert=CVE-2016-2216`

### Commits

- [[`d94f864abd`](https://github.com/nodejs/node/commit/d94f864abd0933c125afeb84b6f72ec709c63b43)] - **deps**: add -no_rand_screen to openssl s_client (Shigeki Ohtsu) [#1836](https://github.com/nodejs/node/pull/1836)
- [[`136295e202`](https://github.com/nodejs/node/commit/136295e202)] - **deps**: upgrade openssl sources to 1.0.2f (Myles Borins) [#4961](https://github.com/nodejs/node/pull/4961)
- [[`0eae95eae3`](https://github.com/nodejs/node/commit/0eae95eae3)] - **(SEMVER-MINOR)** **deps**: update http-parser to version 2.5.1 (James M Snell)
- [[`cf2b714b02`](https://github.com/nodejs/node/commit/cf2b714b02)] - **(SEMVER-MINOR)** **http**: strictly forbid invalid characters from headers (James M Snell)
- [[`49ae2e0334`](https://github.com/nodejs/node/commit/49ae2e0334)] - **src**: avoid compiler warning in node_revert.cc (James M Snell)
- [[`da3750f981`](https://github.com/nodejs/node/commit/da3750f981)] - **(SEMVER-MAJOR)** **src**: add --security-revert command line flag (James M Snell)

Windows 32-bit Installer: https://nodejs.org/dist/v4.3.0/node-v4.3.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v4.3.0/node-v4.3.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v4.3.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v4.3.0/win-x64/node.exe \
Mac OS X 64-bit Installer: https://nodejs.org/dist/v4.3.0/node-v4.3.0.pkg \
Mac OS X 64-bit Binary: https://nodejs.org/dist/v4.3.0/node-v4.3.0-darwin-x64.tar.gz \
Linux 32-bit Binary: https://nodejs.org/dist/v4.3.0/node-v4.3.0-linux-x86.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v4.3.0/node-v4.3.0-linux-x64.tar.gz \
SmartOS 32-bit Binary: https://nodejs.org/dist/v4.3.0/node-v4.3.0-sunos-x86.tar.gz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v4.3.0/node-v4.3.0-sunos-x64.tar.gz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v4.3.0/node-v4.3.0-linux-armv6l.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v4.3.0/node-v4.3.0-linux-armv7l.tar.gz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v4.3.0/node-v4.3.0-linux-arm64.tar.gz \
Source Code: https://nodejs.org/dist/v4.3.0/node-v4.3.0.tar.gz \
Other release files: https://nodejs.org/dist/v4.3.0/ \
Documentation: https://nodejs.org/docs/v4.3.0/api/

Shasums (GPG signing hash: SHA512, file hash: SHA256):

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA512

72858adf7bc84b632f5635dd4a8030226048af5ad5cb166c7ee169dfcb4645ef  node-v4.3.0-darwin-x64.tar.gz
b36acbb638db5091becb2edc7e450aa02dc3a8062cd11014eb79434901236ee3  node-v4.3.0-darwin-x64.tar.xz
113a7c5689fd7a1d60160398a9cb0b03b0b88632ba0a2df148ef6e4c96986ec9  node-v4.3.0-headers.tar.gz
34a510fd4caa2830463f3df4765ab274bb5054be7437e7ca3f47f01827f8e1c6  node-v4.3.0-headers.tar.xz
47a52191e264efdbc36f5ec6510abd71fd5d3337d75120c2ddc6a285873763b7  node-v4.3.0-linux-arm64.tar.gz
2d7e28d80667212547d75f7b77f1bba5b015195cebde94d62be787725ee9715b  node-v4.3.0-linux-arm64.tar.xz
2e035c649f72b5fbd712e6cf52e83e9f013b9a266dc907d3595b1c143c9906df  node-v4.3.0-linux-armv6l.tar.gz
eec2411bb08341982c5e40537b7660f09a8001d68d6bd7ef36254c6b813de340  node-v4.3.0-linux-armv6l.tar.xz
49dfc4c4e5d1d07c91503c2a601665b68b6f5fc95d94517628f9a0f43b178158  node-v4.3.0-linux-armv7l.tar.gz
ed2cc35422161e59946c3d53040e7c421ba607876f312181afb0994188c406e5  node-v4.3.0-linux-armv7l.tar.xz
90ce6e23ad9748813742e1cf09e86fa4c0f3d53972d5dbe920a38bcc842e2d09  node-v4.3.0-linux-x64.tar.gz
9e46eedf6cf6de8472fcf939b2ba6c78d6caaa9348f95385146ebc9cddf6470e  node-v4.3.0-linux-x64.tar.xz
6972ed77c36f026498a0fde6b237fbc554325fa3a7426ee17ce563bdc08caa69  node-v4.3.0-linux-x86.tar.gz
2054242f13a6bdb43ff33238dff20aeb5ef06e73a22b9e55b0a01d94c6f8dd9f  node-v4.3.0-linux-x86.tar.xz
cafa3e7dc44fdd1459fcd81e4a770629f8fed4c74f77c446f9acde30a444bb28  node-v4.3.0.pkg
c97723abd27c2b48ec2cbc9cc9b7dd057c96bf05c1895d7740414f84955753db  node-v4.3.0-sunos-x64.tar.gz
6c850504d23c138c8de00731a814e8aec4ca9b96781970e3f4caa21d2d91c94d  node-v4.3.0-sunos-x64.tar.xz
8f645328daa96702bf110cfcf2021620dc76fcb16ce80423a5f72dd23893cf71  node-v4.3.0-sunos-x86.tar.gz
e7dac63c559050a09f8a201fab92af14d1f19cf6a6c5ad9f0d2cc53a2ffc4355  node-v4.3.0-sunos-x86.tar.xz
18504ac6d903cd061f60a29dafcda416a078112f3404d23a7901c41a8e9706b9  node-v4.3.0.tar.gz
4b4cb93929c2368219eb36a707b0cb87b08df5757c5c1073c18c15b79c873566  node-v4.3.0.tar.xz
48d2a640d8d7f390cbe7f5e6ddb5a0240f1a5e49e1d6f97f222bc54a69773238  node-v4.3.0-x64.msi
eb22e601c152b0ebad11e4ddd24b777aff6e36c8e0de92f9a088959cad3d0f47  node-v4.3.0-x86.msi
cd7f06223a59fa7057a0b528dd411896f995a541c1e82dd838ef1b3c72d5383d  win-x64/node.exe
6e2363d5ad5438f15373571ce44ffdd4a2bc3f8873fe9d51bbb108b34a7b7307  win-x64/node.lib
e5bf98250aa9972cf810c52793d684eeba17afe7e1241d93b39f7110a6825b53  win-x86/node.exe
7025ae784e4f78bb4dcb5d6635d773b3ada138b99cfea0a285a6c55311de9213  win-x86/node.lib
-----BEGIN PGP SIGNATURE-----
Comment: GPGTools - https://gpgtools.org

iQEcBAEBCgAGBQJWuhwKAAoJEHNBsVwHCHesMUUIAJIQP0LRemTTPlCgthwFlFDP
FRiImqbIUSPCVf+lEzfBqGAOlHmPLqGLKhiaYt5TxgglomRYNuLcUhc8g19HSkvV
KT9PU2JjSUHZ0dc2FgzlrWKZzET9DpBeNQzKHE3i26Z5SGdvVdZSShuS1WgoBzao
SwQqB1F38mThrL0FAg7PQ80nNH/Tl+mqdJF9Yrjrblfisv+qAnFqlhSC8L78jhLc
geWnuW22qm4CuDQp5DcwJAlYHk0P6AtVtVsvi+XUwUIe4RKpMUmvkYUa2zPF5T/9
RtKneJXAF/hqEXhPfDE4gF0gGIVE0avHQAYV7NyXvi7R+jwhlNnwd3m0kyEdeZ4=
=70Ju
-----END PGP SIGNATURE-----

```
