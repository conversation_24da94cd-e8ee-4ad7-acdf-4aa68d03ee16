---
date: '2016-09-15T21:00:36.348Z'
category: release
title: Node v6.6.0 (Current)
layout: blog-post
author: <PERSON>
---

### Notable changes

- **crypto**: Added `crypto.timingSafeEqual()`. (not-an-aardvark) [#8304](https://github.com/nodejs/node/pull/8304)
- **events**: Made the "max event listeners" memory leak warning more accessible. (<PERSON>) [#8298](https://github.com/nodejs/node/pull/8298)
- **promises**: Unhandled rejections now emit a [process warning](https://nodejs.org/dist/latest-v6.x/docs/api/process.html#process_event_warning) after the first tick. (<PERSON>) [#8223](https://github.com/nodejs/node/pull/8223)
- **repl**: Added auto alignment for `.editor` mode. (<PERSON>) [#8241](https://github.com/nodejs/node/pull/8241)
- **util**: Some functionality has been added to `util.inspect()`:
  - Returning `this` from a custom inspect function now works. (<PERSON>) [#8174](https://github.com/nodejs/node/pull/8174)
  - Added support for Symbol-based custom inspection methods. (Anna Henningsen) [#8174](https://github.com/nodejs/node/pull/8174)

### Commits

- [[`2ecc8c4c23`](https://github.com/nodejs/node/commit/2ecc8c4c23)] - **async_wrap**: add a missing case to test-async-wrap-throw-no-init (yorkie) [#8198](https://github.com/nodejs/node/pull/8198)
- [[`00f4bc3105`](https://github.com/nodejs/node/commit/00f4bc3105)] - **benchmark**: add benches for fs.stat & fs.statSync (Anna Henningsen) [#8338](https://github.com/nodejs/node/pull/8338)
- [[`7cc1391287`](https://github.com/nodejs/node/commit/7cc1391287)] - **benchmark**: fix off-by-one error in fs benchmarks (Anna Henningsen) [#8338](https://github.com/nodejs/node/pull/8338)
- [[`6e3db283ed`](https://github.com/nodejs/node/commit/6e3db283ed)] - **buffer**: fix ArrayBuffer checks (Brian White) [#8453](https://github.com/nodejs/node/pull/8453)
- [[`dd51b1f428`](https://github.com/nodejs/node/commit/dd51b1f428)] - **buffer,string_decoder**: consolidate encoding validation logic (James M Snell) [#7207](https://github.com/nodejs/node/pull/7207)
- [[`a830e37dc9`](https://github.com/nodejs/node/commit/a830e37dc9)] - **build**: don't require processing docs for nightlies (Johan Bergström) [#8325](https://github.com/nodejs/node/pull/8325)
- [[`836bfc188b`](https://github.com/nodejs/node/commit/836bfc188b)] - **build**: fix dependencies on AIX (Michael Dawson) [#8285](https://github.com/nodejs/node/pull/8285)
- [[`bc9d2fb543`](https://github.com/nodejs/node/commit/bc9d2fb543)] - **build**: fix dependencies on AIX (Michael Dawson) [#8272](https://github.com/nodejs/node/pull/8272)
- [[`206b105b1e`](https://github.com/nodejs/node/commit/206b105b1e)] - **build**: add missing files to zip and 7z packages (Richard Lau) [#8069](https://github.com/nodejs/node/pull/8069)
- [[`afb9917f16`](https://github.com/nodejs/node/commit/afb9917f16)] - **(SEMVER-MINOR)** **crypto**: add crypto.timingSafeEqual() (not-an-aardvark) [#8304](https://github.com/nodejs/node/pull/8304)
- [[`1640e7a4da`](https://github.com/nodejs/node/commit/1640e7a4da)] - **crypto**: fix getDecoder() encoding check (atstojanov) [#8301](https://github.com/nodejs/node/pull/8301)
- [[`49f996f4f6`](https://github.com/nodejs/node/commit/49f996f4f6)] - **crypto**: make malloc failure check cross-platform (Rich Trott) [#8352](https://github.com/nodejs/node/pull/8352)
- [[`9c460d7475`](https://github.com/nodejs/node/commit/9c460d7475)] - **deps**: add back no-op harmony shipping flags (Ali Ijaz Sheikh) [#8445](https://github.com/nodejs/node/pull/8445)
- [[`c8bcf1b591`](https://github.com/nodejs/node/commit/c8bcf1b591)] - **deps**: workaround clang-3.4 ICE (Ali Ijaz Sheikh) [#8343](https://github.com/nodejs/node/pull/8343)
- [[`ac3471ca23`](https://github.com/nodejs/node/commit/ac3471ca23)] - **deps**: v8_inspector update (Eugene Ostroukhov) [#8150](https://github.com/nodejs/node/pull/8150)
- [[`f829660c71`](https://github.com/nodejs/node/commit/f829660c71)] - **deps**: cherry-pick 8ed65b97 from V8's upstream (Anna Henningsen) [#8411](https://github.com/nodejs/node/pull/8411)
- [[`c48cb2de48`](https://github.com/nodejs/node/commit/c48cb2de48)] - **doc**: link SIGTSTP / SIGCONT events in readline doc (Italo A. Casas) [#8475](https://github.com/nodejs/node/pull/8475)
- [[`ce1a46c02d`](https://github.com/nodejs/node/commit/ce1a46c02d)] - **doc**: update onboarding PR landing info (Rich Trott) [#8479](https://github.com/nodejs/node/pull/8479)
- [[`92acff82ea`](https://github.com/nodejs/node/commit/92acff82ea)] - **doc**: add CTC meeting minutes 2016-08-31 (Josh Gavant) [#8424](https://github.com/nodejs/node/pull/8424)
- [[`53877357fc`](https://github.com/nodejs/node/commit/53877357fc)] - **doc**: fix link on timers.md (yorkie) [#8488](https://github.com/nodejs/node/pull/8488)
- [[`09da5756e5`](https://github.com/nodejs/node/commit/09da5756e5)] - **doc**: add `added:` information for crypto (Luigi Pinca) [#8281](https://github.com/nodejs/node/pull/8281)
- [[`19df5cef3b`](https://github.com/nodejs/node/commit/19df5cef3b)] - **doc**: fix example in repl documentation (Franziska Hinkelmann) [#8469](https://github.com/nodejs/node/pull/8469)
- [[`3ce6eaa3b9`](https://github.com/nodejs/node/commit/3ce6eaa3b9)] - **doc**: note that listening on SIGSEGV & co is unsafe (Anna Henningsen) [#8410](https://github.com/nodejs/node/pull/8410)
- [[`c7771e6fbc`](https://github.com/nodejs/node/commit/c7771e6fbc)] - **doc**: clarify sentence in event loop doc (Luigi Pinca) [#8400](https://github.com/nodejs/node/pull/8400)
- [[`2c45782b12`](https://github.com/nodejs/node/commit/2c45782b12)] - **doc**: add CI help/support info to onboarding doc (Rich Trott) [#8407](https://github.com/nodejs/node/pull/8407)
- [[`cffe7b731d`](https://github.com/nodejs/node/commit/cffe7b731d)] - **doc**: add 2016-08-17 CTC meeting minutes (Josh Gavant) [#8245](https://github.com/nodejs/node/pull/8245)
- [[`fdd59cc04c`](https://github.com/nodejs/node/commit/fdd59cc04c)] - **doc**: update BUILDING.md (Rich Trott) [#8398](https://github.com/nodejs/node/pull/8398)
- [[`d0a92be798`](https://github.com/nodejs/node/commit/d0a92be798)] - **doc**: add 2016-08-10 CTC meeting minutes (Josh Gavant) [#8229](https://github.com/nodejs/node/pull/8229)
- [[`ca31187087`](https://github.com/nodejs/node/commit/ca31187087)] - **doc**: update CI content in onboarding doc (Rich Trott) [#8374](https://github.com/nodejs/node/pull/8374)
- [[`44983b1fdc`](https://github.com/nodejs/node/commit/44983b1fdc)] - **doc**: update authors list (James M Snell) [#8346](https://github.com/nodejs/node/pull/8346)
- [[`ee83f5d541`](https://github.com/nodejs/node/commit/ee83f5d541)] - **doc**: add return type of clientRequest.setTimeout (Mike Ralphson) [#8356](https://github.com/nodejs/node/pull/8356)
- [[`1695c84240`](https://github.com/nodejs/node/commit/1695c84240)] - **doc**: fix a wrong link,add '.md' to the link (Alexis374) [#8315](https://github.com/nodejs/node/pull/8315)
- [[`65096de443`](https://github.com/nodejs/node/commit/65096de443)] - **doc**: fix typos (Mike Ralphson) [#8370](https://github.com/nodejs/node/pull/8370)
- [[`6d421a2ee2`](https://github.com/nodejs/node/commit/6d421a2ee2)] - **doc**: fix broken link in dgram doc (Brian White) [#8365](https://github.com/nodejs/node/pull/8365)
- [[`fbabd36de9`](https://github.com/nodejs/node/commit/fbabd36de9)] - **doc**: update targos email in readme per request (James M Snell) [#8389](https://github.com/nodejs/node/pull/8389)
- [[`76a3050c34`](https://github.com/nodejs/node/commit/76a3050c34)] - **doc**: update landing pr info in onboarding doc (Rich Trott) [#8344](https://github.com/nodejs/node/pull/8344)
- [[`372e4f3f79`](https://github.com/nodejs/node/commit/372e4f3f79)] - **doc**: bad/better examples for fs.access() and fs.exists() (Dan Fabulich) [#7832](https://github.com/nodejs/node/pull/7832)
- [[`9f18878eee`](https://github.com/nodejs/node/commit/9f18878eee)] - **doc**: fix typo in stream doc (Hubert Mine) [#8326](https://github.com/nodejs/node/pull/8326)
- [[`59e96bb1af`](https://github.com/nodejs/node/commit/59e96bb1af)] - **doc**: adding danbev to collaborators (Daniel Bevenius) [#8359](https://github.com/nodejs/node/pull/8359)
- [[`b553e57f5f`](https://github.com/nodejs/node/commit/b553e57f5f)] - **doc**: clarify that path on windows accepts / and \ (James M Snell) [#8291](https://github.com/nodejs/node/pull/8291)
- [[`feab3d4c25`](https://github.com/nodejs/node/commit/feab3d4c25)] - **doc**: add lpinca to collaborators (Luigi Pinca) [#8331](https://github.com/nodejs/node/pull/8331)
- [[`d2b2abe9d9`](https://github.com/nodejs/node/commit/d2b2abe9d9)] - **doc**: doc that listen can be called multiple times (James M Snell) [#8294](https://github.com/nodejs/node/pull/8294)
- [[`54d76137cd`](https://github.com/nodejs/node/commit/54d76137cd)] - **doc**: readline write() is processed as input (James M Snell) [#8295](https://github.com/nodejs/node/pull/8295)
- [[`48be8bc4e1`](https://github.com/nodejs/node/commit/48be8bc4e1)] - **doc**: `'ipc'` is required with fork stdio option (James M Snell) [#8290](https://github.com/nodejs/node/pull/8290)
- [[`47ecd21133`](https://github.com/nodejs/node/commit/47ecd21133)] - **doc**: improve fs.truncate functions' documentation (Sakthipriyan Vairamani) [#7648](https://github.com/nodejs/node/pull/7648)
- [[`d565183c37`](https://github.com/nodejs/node/commit/d565183c37)] - **doc**: add `added:` information for modules (Luigi Pinca) [#8250](https://github.com/nodejs/node/pull/8250)
- [[`b6f5104145`](https://github.com/nodejs/node/commit/b6f5104145)] - **doc**: fix onReadable reentry after unshift called (Zwb) [#8200](https://github.com/nodejs/node/pull/8200)
- [[`93ac875d53`](https://github.com/nodejs/node/commit/93ac875d53)] - **doc**: add `added:` information for dgram (Luigi Pinca) [#8196](https://github.com/nodejs/node/pull/8196)
- [[`260663fbfa`](https://github.com/nodejs/node/commit/260663fbfa)] - **doc**: add Myles Borins to the CTC (Rod Vagg) [#8260](https://github.com/nodejs/node/pull/8260)
- [[`a7c21759d9`](https://github.com/nodejs/node/commit/a7c21759d9)] - **doc**: fix buf.readUIntBE, buf.readUIntLE examples (David Keeler) [#8240](https://github.com/nodejs/node/pull/8240)
- [[`e4fcf55701`](https://github.com/nodejs/node/commit/e4fcf55701)] - **doc**: fix "timout" typo in timeout (Fangshi He) [#8231](https://github.com/nodejs/node/pull/8231)
- [[`03f5297ccd`](https://github.com/nodejs/node/commit/03f5297ccd)] - **doc**: include the optional options parameter (Sakthipriyan Vairamani) [#7842](https://github.com/nodejs/node/pull/7842)
- [[`605db31fe7`](https://github.com/nodejs/node/commit/605db31fe7)] - **(SEMVER-MINOR)** **events**: make memory leak warning more accessible (Anna Henningsen) [#8298](https://github.com/nodejs/node/pull/8298)
- [[`fa4c4d655a`](https://github.com/nodejs/node/commit/fa4c4d655a)] - **http**: fix connection upgrade checks (Brian White) [#8238](https://github.com/nodejs/node/pull/8238)
- [[`b603ac24cb`](https://github.com/nodejs/node/commit/b603ac24cb)] - **inspector**: use script name for target title (Eugene Ostroukhov) [#8243](https://github.com/nodejs/node/pull/8243)
- [[`13a522ac39`](https://github.com/nodejs/node/commit/13a522ac39)] - **inspector**: make sure all messages are dispatched (Eugene Ostroukhov) [#8264](https://github.com/nodejs/node/pull/8264)
- [[`250a380231`](https://github.com/nodejs/node/commit/250a380231)] - **inspector**: simplify buffer management (Eugene Ostroukhov) [#8257](https://github.com/nodejs/node/pull/8257)
- [[`354166c061`](https://github.com/nodejs/node/commit/354166c061)] - **inspector**: use new inspector headers (Eugene Ostroukhov) [#8150](https://github.com/nodejs/node/pull/8150)
- [[`3ef8ba8bdc`](https://github.com/nodejs/node/commit/3ef8ba8bdc)] - **net**: make holding the buffer in memory more robust (Anna Henningsen) [#8252](https://github.com/nodejs/node/pull/8252)
- [[`180867d6a6`](https://github.com/nodejs/node/commit/180867d6a6)] - **(SEMVER-MINOR)** **promise**: warn on unhandled rejections (Benjamin Gruenbaum) [#8223](https://github.com/nodejs/node/pull/8223)
- [[`408308f2e6`](https://github.com/nodejs/node/commit/408308f2e6)] - **(SEMVER-MINOR)** **readline**: key interval delay for \r & \n (Prince J Wesley) [#8109](https://github.com/nodejs/node/pull/8109)
- [[`6f20f477c4`](https://github.com/nodejs/node/commit/6f20f477c4)] - **(SEMVER-MINOR)** **repl**: Auto alignment for .editor mode (Prince J Wesley) [#8241](https://github.com/nodejs/node/pull/8241)
- [[`0d24247e50`](https://github.com/nodejs/node/commit/0d24247e50)] - **src**: pull AfterConnect from pipe_wrap and tcp_wrap (Daniel Bevenius) [#8448](https://github.com/nodejs/node/pull/8448)
- [[`16202264d1`](https://github.com/nodejs/node/commit/16202264d1)] - **src**: remove unneeded Environment error methods (Ben Noordhuis) [#8427](https://github.com/nodejs/node/pull/8427)
- [[`8cbbb47e39`](https://github.com/nodejs/node/commit/8cbbb47e39)] - **src**: update f function call comment (Daniel Bevenius) [#8416](https://github.com/nodejs/node/pull/8416)
- [[`d1d1433b02`](https://github.com/nodejs/node/commit/d1d1433b02)] - **src**: normalize malloc, realloc (Michael Dawson) [#7564](https://github.com/nodejs/node/pull/7564)
- [[`2c2a21ab56`](https://github.com/nodejs/node/commit/2c2a21ab56)] - **src**: unbreak build when compiling against uclibc (Ben Noordhuis) [#8308](https://github.com/nodejs/node/pull/8308)
- [[`4e368c58ff`](https://github.com/nodejs/node/commit/4e368c58ff)] - **src**: moving f function call comment (Daniel Bevenius) [#8405](https://github.com/nodejs/node/pull/8405)
- [[`0f2c619f55`](https://github.com/nodejs/node/commit/0f2c619f55)] - **src**: avoid duplicate AtExit functions (Ali Ijaz Sheikh) [#8273](https://github.com/nodejs/node/pull/8273)
- [[`3358861470`](https://github.com/nodejs/node/commit/3358861470)] - **test**: refector parallel/test-http.js (Junshu Okamoto) [#8471](https://github.com/nodejs/node/pull/8471)
- [[`2ce364ad10`](https://github.com/nodejs/node/commit/2ce364ad10)] - **test**: modernize JS and tighten equality checking (Peter Ogilvie) [#8476](https://github.com/nodejs/node/pull/8476)
- [[`1a30fe563d`](https://github.com/nodejs/node/commit/1a30fe563d)] - **test**: swapped == and equal to === and strictEqual (Christopher Dunavan) [#8472](https://github.com/nodejs/node/pull/8472)
- [[`d2b6b66037`](https://github.com/nodejs/node/commit/d2b6b66037)] - **test**: refactor test-net-pipe-connect-errors (Rich Trott) [#8473](https://github.com/nodejs/node/pull/8473)
- [[`3294c2c82b`](https://github.com/nodejs/node/commit/3294c2c82b)] - **test**: skip pseudo-tty/no_dropped_stdio test (Michael Dawson) [#8470](https://github.com/nodejs/node/pull/8470)
- [[`5baa4e0156`](https://github.com/nodejs/node/commit/5baa4e0156)] - **test**: clean up net server try ports test (Thomas Hunter II) [#8458](https://github.com/nodejs/node/pull/8458)
- [[`d0e61d4cc3`](https://github.com/nodejs/node/commit/d0e61d4cc3)] - **test**: make crypto.timingSafeEqual test less flaky (not-an-aardvark) [#8456](https://github.com/nodejs/node/pull/8456)
- [[`154d6893b0`](https://github.com/nodejs/node/commit/154d6893b0)] - **test**: add test-debug-protocol-execute (Rich Trott) [#8454](https://github.com/nodejs/node/pull/8454)
- [[`6c30dddc36`](https://github.com/nodejs/node/commit/6c30dddc36)] - **test**: exercise EE function type checking paths (cjihrig) [#8168](https://github.com/nodejs/node/pull/8168)
- [[`d536d6d334`](https://github.com/nodejs/node/commit/d536d6d334)] - **test**: increase `_debugger` coverage (Rich Trott) [#8403](https://github.com/nodejs/node/pull/8403)
- [[`6d07e57a36`](https://github.com/nodejs/node/commit/6d07e57a36)] - **test**: mark pseudo-tty/no_dropped_stdio as flaky (Michael Dawson) [#8385](https://github.com/nodejs/node/pull/8385)
- [[`680a4c8b8e`](https://github.com/nodejs/node/commit/680a4c8b8e)] - **test**: test non-buffer/string with zlib (Rich Trott) [#8350](https://github.com/nodejs/node/pull/8350)
- [[`f0300eda27`](https://github.com/nodejs/node/commit/f0300eda27)] - **test**: fix test-cluster-dgram-1 flakiness (Santiago Gimeno) [#8383](https://github.com/nodejs/node/pull/8383)
- [[`b8fa7c949d`](https://github.com/nodejs/node/commit/b8fa7c949d)] - **test**: fix ::1 error in test-dns-ipv6 (Gibson Fahnestock) [#8254](https://github.com/nodejs/node/pull/8254)
- [[`c07e063dd9`](https://github.com/nodejs/node/commit/c07e063dd9)] - **test**: fix `fs-watch-recursive` flakiness on OS X (Santiago Gimeno) [#4629](https://github.com/nodejs/node/pull/4629)
- [[`1aa7168e91`](https://github.com/nodejs/node/commit/1aa7168e91)] - **test**: refactor/cleanup a number of cluster tests (James M Snell) [#8261](https://github.com/nodejs/node/pull/8261)
- [[`39cf7bf24d`](https://github.com/nodejs/node/commit/39cf7bf24d)] - **test**: refactor parallel/test-process-env.js (Rich Trott) [#8324](https://github.com/nodejs/node/pull/8324)
- [[`40e84218a0`](https://github.com/nodejs/node/commit/40e84218a0)] - **test**: add test for zlib.create\*Raw() (Rich Trott) [#8306](https://github.com/nodejs/node/pull/8306)
- [[`cc61d1a3ba`](https://github.com/nodejs/node/commit/cc61d1a3ba)] - **test**: cleanup test require symlink (Paul Grock) [#8305](https://github.com/nodejs/node/pull/8305)
- [[`cde0a1edfd`](https://github.com/nodejs/node/commit/cde0a1edfd)] - **test**: refactor test-debug-signal-cluster (Rich Trott) [#8289](https://github.com/nodejs/node/pull/8289)
- [[`bc6a7ca8e7`](https://github.com/nodejs/node/commit/bc6a7ca8e7)] - **test**: additional refactoring/cleanup of buffer tests (James M Snell) [#8283](https://github.com/nodejs/node/pull/8283)
- [[`5d43131af4`](https://github.com/nodejs/node/commit/5d43131af4)] - **test**: add known issue test for path parse issue #6229 (James M Snell) [#8293](https://github.com/nodejs/node/pull/8293)
- [[`d2179bd6f1`](https://github.com/nodejs/node/commit/d2179bd6f1)] - **test**: make sure over truncation of file zero fills (Sakthipriyan Vairamani) [#7648](https://github.com/nodejs/node/pull/7648)
- [[`5cff625d6a`](https://github.com/nodejs/node/commit/5cff625d6a)] - **test**: add check in test-signal-handler (Rich Trott) [#8248](https://github.com/nodejs/node/pull/8248)
- [[`0b0a1ceac3`](https://github.com/nodejs/node/commit/0b0a1ceac3)] - **test**: clean up / refactor buffer tests, remove duplication (James M Snell) [#8256](https://github.com/nodejs/node/pull/8256)
- [[`0f977f9797`](https://github.com/nodejs/node/commit/0f977f9797)] - **test**: mark test-child-process-fork-dgram as flaky (Michael Dawson) [#8274](https://github.com/nodejs/node/pull/8274)
- [[`6f269bed99`](https://github.com/nodejs/node/commit/6f269bed99)] - **test**: refactor test-util-inspect (Rich Trott) [#8189](https://github.com/nodejs/node/pull/8189)
- [[`eff771f399`](https://github.com/nodejs/node/commit/eff771f399)] - **tickprocessor**: apply c++filt manually on mac (Fedor Indutny) [#8480](https://github.com/nodejs/node/pull/8480)
- [[`f47ce9d9f8`](https://github.com/nodejs/node/commit/f47ce9d9f8)] - **tools**: replace custom ESLint rule with built-in (Rich Trott) [#8478](https://github.com/nodejs/node/pull/8478)
- [[`36235ac323`](https://github.com/nodejs/node/commit/36235ac323)] - **tools**: update ESLint to 3.5.0 (Rich Trott) [#8478](https://github.com/nodejs/node/pull/8478)
- [[`ef5cb129ae`](https://github.com/nodejs/node/commit/ef5cb129ae)] - **tools**: fix new-parens violations (Rich Trott) [#8478](https://github.com/nodejs/node/pull/8478)
- [[`773e1c1304`](https://github.com/nodejs/node/commit/773e1c1304)] - **tools**: enable caching for jslint task (Rich Trott) [#8296](https://github.com/nodejs/node/pull/8296)
- [[`54f689fc1e`](https://github.com/nodejs/node/commit/54f689fc1e)] - **tools**: update ESLint to 3.4.0 (Rich Trott) [#8296](https://github.com/nodejs/node/pull/8296)
- [[`f840bc87d6`](https://github.com/nodejs/node/commit/f840bc87d6)] - **url**: fix off-by-one error in loop handling dots (Luigi Pinca) [#8420](https://github.com/nodejs/node/pull/8420)
- [[`6ae7af0fa3`](https://github.com/nodejs/node/commit/6ae7af0fa3)] - **url**: keep auth in `url.resolve()` if host matches (Ilkka Myller) [#8215](https://github.com/nodejs/node/pull/8215)
- [[`eec5d02266`](https://github.com/nodejs/node/commit/eec5d02266)] - **url**: `url.format()` encodes all `#` in `search` (Ilkka Myller) [#8072](https://github.com/nodejs/node/pull/8072)
- [[`d67ece2f68`](https://github.com/nodejs/node/commit/d67ece2f68)] - **util**: improve function signature of `util._extend` (Sakthipriyan Vairamani) [#8187](https://github.com/nodejs/node/pull/8187)
- [[`e9c4805c1f`](https://github.com/nodejs/node/commit/e9c4805c1f)] - **(SEMVER-MINOR)** **util**: allow returning `this` from custom inspect (Anna Henningsen) [#8174](https://github.com/nodejs/node/pull/8174)
- [[`4cb55ff392`](https://github.com/nodejs/node/commit/4cb55ff392)] - **(SEMVER-MINOR)** **util**: allow symbol-based custom inspection methods (Anna Henningsen) [#8174](https://github.com/nodejs/node/pull/8174)
- [[`151d1ea6a6`](https://github.com/nodejs/node/commit/151d1ea6a6)] - **vm**: change ContextifyScript to Script in comment (Daniel Bevenius) [#8415](https://github.com/nodejs/node/pull/8415)
- [[`f346dee940`](https://github.com/nodejs/node/commit/f346dee940)] - **win,build**: forward release_urlbase to configure (João Reis) [#8430](https://github.com/nodejs/node/pull/8430)
- [[`6bb057ec6a`](https://github.com/nodejs/node/commit/6bb057ec6a)] - **win,build**: exit when addons fail to build (João Reis) [#8412](https://github.com/nodejs/node/pull/8412)
- [[`14d356d0ab`](https://github.com/nodejs/node/commit/14d356d0ab)] - **win,build**: skip finding VS when not needed (João Reis) [#8412](https://github.com/nodejs/node/pull/8412)
- [[`81d063e174`](https://github.com/nodejs/node/commit/81d063e174)] - **win,build**: fail on invalid option in vcbuild (João Reis) [#8412](https://github.com/nodejs/node/pull/8412)

Windows 32-bit Installer: https://nodejs.org/dist/v6.6.0/node-v6.6.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v6.6.0/node-v6.6.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v6.6.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v6.6.0/win-x64/node.exe \
Mac OS X 64-bit Installer: https://nodejs.org/dist/v6.6.0/node-v6.6.0.pkg \
Mac OS X 64-bit Binary: https://nodejs.org/dist/v6.6.0/node-v6.6.0-darwin-x64.tar.gz \
Linux 32-bit Binary: https://nodejs.org/dist/v6.6.0/node-v6.6.0-linux-x86.tar.xz \
Linux 64-bit Binary: https://nodejs.org/dist/v6.6.0/node-v6.6.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v6.6.0/node-v6.6.0-linux-ppc64le.tar.xz \
SmartOS 32-bit Binary: https://nodejs.org/dist/v6.6.0/node-v6.6.0-sunos-x86.tar.xz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v6.6.0/node-v6.6.0-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v6.6.0/node-v6.6.0-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v6.6.0/node-v6.6.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v6.6.0/node-v6.6.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v6.6.0/node-v6.6.0.tar.gz \
Other release files: https://nodejs.org/dist/v6.6.0/ \
Documentation: https://nodejs.org/docs/v6.6.0/api/

Shasums (GPG signing hash: SHA512, file hash: SHA256):

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA512

c8d1fe38eb794ca46aacf6c8e90676eec7a8aeec83b4b09f57ce503509e7a19f  node-v6.6.0-darwin-x64.tar.gz
e5e6248714618a81595cc2c04d3420535cd8a26d74274013a0eb61e0b3d23f5f  node-v6.6.0-darwin-x64.tar.xz
60b81c7276105a51e71ad8bc7f59163105e7c5dd1d992b173b5b66449b6df3fc  node-v6.6.0-headers.tar.gz
b3b6989c12cdf3652253c1b87d4328d151576a2ff4b189e05ebcba99ed84896c  node-v6.6.0-headers.tar.xz
9abae64e411d8ea1541a4776e78d9cf53ad8e20e8b34cf77d9b3579e8edb6f65  node-v6.6.0-linux-arm64.tar.gz
fce02f2fc7bdc79899de4696395d02f3e7209e4ab4053336db790b8f58cce385  node-v6.6.0-linux-arm64.tar.xz
d311754cddc9b387a2798226ecb5487e515c555e050fdd08ef3d6665c3c0d336  node-v6.6.0-linux-armv6l.tar.gz
b6a5dce140b0795da61a9936454670887b431922e56326b53f1900e72e2ac48d  node-v6.6.0-linux-armv6l.tar.xz
e4dc3295f6602b0f4cd3433a6e520294743e2c342692b4fad388d33910cdd465  node-v6.6.0-linux-armv7l.tar.gz
a3a1aac41c21d216ef380269b80225c658188f9182fc087e1823afe66935e3e1  node-v6.6.0-linux-armv7l.tar.xz
b38ff6058f0213567d31a5d194d669ce75894336f6d0324426f01722c989d3c4  node-v6.6.0-linux-ppc64le.tar.gz
c25c106ee0540f13cd119e47b85191f8a362e48d636a9e8398f5db42ac30d755  node-v6.6.0-linux-ppc64le.tar.xz
90d483c63fdbc6594185b3e143bf8d5627812288a029f02f578363d6dd505285  node-v6.6.0-linux-ppc64.tar.gz
3a2cbd56384217df585ad81eff7ddb92cf3601374152b05eafe27a2c7ef02fa9  node-v6.6.0-linux-ppc64.tar.xz
a2f109eb0fec81d90206e9c0bca05327c706b244b5d7c5bd247ac140f811e54e  node-v6.6.0-linux-s390x.tar.gz
aa181bdba1a4f8d06e61fb5496be202c271dadc46a0ef6ecb4b7b4454f1909c4  node-v6.6.0-linux-s390x.tar.xz
c22ab0dfa9d0b8d9de02ef7c0d860298a5d1bf6cae7413fb18b99e8a3d25648a  node-v6.6.0-linux-x64.tar.gz
fdf4377ea4dc9ba2f09d81d9ad1eae42e7eb870c4b1b69f2761f22f28cb5ba31  node-v6.6.0-linux-x64.tar.xz
05f3bfdfe8e1911e66b4bf645a439480212767e71664b8c97f0cba46671e8160  node-v6.6.0-linux-x86.tar.gz
e6fcca4783491c7b5866e7f969e93b66f47380e9cc92c08607d7683cfa200775  node-v6.6.0-linux-x86.tar.xz
7a9af7bf1eb98f8c206037a0dbbcd797c5684e2570d6acc8d915351bad4a5571  node-v6.6.0.pkg
21b05b3664c338b76ec8c139a7ae80fb9b1c65215ee2da6d899dcbd6c6d67554  node-v6.6.0-sunos-x64.tar.gz
b57733493ca428b859ca6f371f6fc0f93bca77926e81e80ddabe1c5d240431e8  node-v6.6.0-sunos-x64.tar.xz
ffa81fc834a7f24958b3c1bf07dc668f6d0e0cda48582a23bc749abca42cae8c  node-v6.6.0-sunos-x86.tar.gz
3572cbe039cb4a791d4f1db3ed855250cb06c4136a5af0eb277aa27a75469c29  node-v6.6.0-sunos-x86.tar.xz
11b957de855a392ceaa8b300ec66236d6f9c6baa184837d00bdaba2da4aefe91  node-v6.6.0.tar.gz
640485d2b672d3313203eae164576539e61f1e55d68122ff2c7fb59896e21a33  node-v6.6.0.tar.xz
92d708377519f9c4cb6b6006ab50118fe98ea3b7fa49ecb8a1c02f2c13c8ab92  node-v6.6.0-win-x64.7z
5b1c49f8fd36bd9c277259773f42832db74c41f87033deaccfbd82e15330bae4  node-v6.6.0-win-x64.zip
3a3eaa4690ead41631e761f24f0e0cf2d66d1378f07a59153e397abdf470273d  node-v6.6.0-win-x86.7z
95862922b8469e00a7ae5f1f82d51c739fdc6ee12a8e1d46c0f100f2ea18c082  node-v6.6.0-win-x86.zip
675f5088021b99c53136d9f7306ba93f2a126df75777f0b97fb6013da2e5c539  node-v6.6.0-x64.msi
8ce48431e7d0182d21efc398fcc478f8852b96df8a7287dd40db9ef53cca01a3  node-v6.6.0-x86.msi
1cd746e30ce1af03495858ee3f8c3e1c18350b8a8259e560b5ac25e95bc5b5de  win-x64/node.exe
efd924b61fe3f6ba67c28ac605c13304ac16976e020a57480f555cffd0a9741d  win-x64/node.lib
84b27e00d6c562dbc6d68f32c9755874c7f29932ebc04a57580cc05aac64d78b  win-x64/node_pdb.7z
238b24d1b7c24ab7ba3c964fda775500965dbcf1dd1484fa3397eded5e9d3d94  win-x64/node_pdb.zip
708a825e7c5fdb1015ea3ca56e257c74fc5ec602a83e8794ea3ab4e100c38f0b  win-x86/node.exe
461fd40ecd79bbea9710d0a940f4d143fc344ffff62309cd86ee414ccfd3916c  win-x86/node.lib
7161b5361d67279a74d2564195be9bf5a9bb2d332d9c9dde6b59a2926bfb1636  win-x86/node_pdb.7z
2394477a698c40ae08bcb9ec01ea7ed7efe9f32498212c511bd9d67103662acc  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----
Comment: GPGTools - https://gpgtools.org

iQIcBAEBCgAGBQJX2wmcAAoJEEX17r2BPa6OkcEP/AqvpLT+XmIHd+2S5NguUNqu
iIZIBmw/iNBomfR5X3b92+TU0zMAmiakAWR8KgiHIdausbokITWv3Qia7lWro1ue
IFuXbsP9JcN+AMHi3/JKOekcg5IBqRDrzXxfHACwLokBwY5e+PwyYtEHPBx+Ls55
QuKcR24Jo70UQ1lkXNSJPSZdn85EcQ1Dw1kdhO3Mi87n4nitTRmWnFLKeJA46Ey2
ejDRUy+xt0jsy7sDeGEKDFu5ajajIRLnaaGsr21gxpjsbPH7Tg1c0gUUbs9fhrvn
gAc8R+5iXsRi24FrlaaLkfvnhF6/7uTN4YG3fbAexT9bPftd9pn7iwHKkKaTrnw/
w+n1bgH4Ogs6w8x0SX1DcvHONj5WqoIgRWRLCnju9GpjkfJQ33qNLHBTXx4qqrb2
J2mJzmvo5T9WyrEV7aWA3Laa8Fwk6MLZe4Cu/mLT9pJ3kNj/5WXCGjpWUS90jSxn
OXWY8XXY8B3e6y0AYT4J26sKd7rIv5ux0PLor/xJT5SYH4XeKbVAn8HfhakIDvR7
oIZW2UJxd/spfIbHSeODEisS5YqDrWyzAxSJfJzt1R2oI299EEtZRIzmiWXsEEXv
ee31irYPSVg0aF+Qbps+ojeOQzbukp2HMrh2K3orVvf0PzifP1Gh76nrw4BLiKLi
sY3xYr5CG0AIAjpSw0w3
=xwUE
-----END PGP SIGNATURE-----

```
