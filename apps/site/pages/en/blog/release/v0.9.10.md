---
date: '2013-02-19T22:00:08.000Z'
category: release
title: Node v0.9.10 (Unstable)
layout: blog-post
author: The Node.js Project
---

2013.02.19, Version 0.9.10 (Unstable)

- V8: Upgrade to **********

- npm: Upgrade to 1.2.12

- fs: Change default WriteStream config, increase perf (isaacs)

- process: streamlining tick callback logic (<PERSON>)

- stream_wrap, udp_wrap: add read-only fd property (<PERSON>)

- buffer: accept negative indices in Buffer#slice() (<PERSON>)

- tls: Cycle data when underlying socket drains (isaacs)

- stream: read(0) should not always trigger \_read(n,cb) (isaacs)

- stream: Empty strings/buffers do not signal EOF any longer (isaacs)

- crypto: improve cipher/decipher error messages (<PERSON>)

- net: Respect the 'readable' flag on sockets (isaacs)

- net: don't suppress ECONNRESET (<PERSON>)

- typed arrays: copy Buffer in typed array constructor (<PERSON>)

- typed arrays: make DataView throw on non-ArrayBuffer (<PERSON>)

- windows: MSI installer enhancements (<PERSON>, <PERSON>)

Source Code: https://nodejs.org/dist/v0.9.10/node-v0.9.10.tar.gz

Macintosh Installer (Universal): https://nodejs.org/dist/v0.9.10/node-v0.9.10.pkg

Windows Installer: https://nodejs.org/dist/v0.9.10/node-v0.9.10-x86.msi

Windows x64 Installer: https://nodejs.org/dist/v0.9.10/x64/node-v0.9.10-x64.msi

Windows x64 Files: https://nodejs.org/dist/v0.9.10/x64/

Linux 32-bit Binary: https://nodejs.org/dist/v0.9.10/node-v0.9.10-linux-x86.tar.gz

Linux 64-bit Binary: https://nodejs.org/dist/v0.9.10/node-v0.9.10-linux-x64.tar.gz

Solaris 32-bit Binary: https://nodejs.org/dist/v0.9.10/node-v0.9.10-sunos-x86.tar.gz

Solaris 64-bit Binary: https://nodejs.org/dist/v0.9.10/node-v0.9.10-sunos-x64.tar.gz

Other release files: https://nodejs.org/dist/v0.9.10/

Website: https://nodejs.org/docs/v0.9.10/

Documentation: https://nodejs.org/docs/v0.9.10/api/

Shasums:

```
813d5f42b156b7d64f00b86e13d26ada3ef352e5  node-v0.9.10-darwin-x64.tar.gz
99dbe66fd0fc176fb3459ffcf62212dcb27bef10  node-v0.9.10-darwin-x86.tar.gz
3a5a465238cbdbdac9786c204fd27be61ce7159a  node-v0.9.10-linux-x64.tar.gz
3340ac206ec1a1f827c954efdfa1351dcfe9f419  node-v0.9.10-linux-x86.tar.gz
b920b2e57c6df1e080966fdeccbfd1b384b156d4  node-v0.9.10-sunos-x64.tar.gz
9c1744352bb1bc71f48f8aea1aff6aeefe35a394  node-v0.9.10-sunos-x86.tar.gz
f1daaafc330cc9993a3a6f7ca8b9cc870b49e75c  node-v0.9.10-x86.msi
2b6c70f57c1513e8f5151785b1ac263565983918  node-v0.9.10.pkg
265542c15cf939b7c71a545758d835ed44d791d3  node-v0.9.10.tar.gz
653f24d53f411217d57ed18d73921ff4721f00dd  node.exe
7d62da67a7b33628d7d90c9d5037cf564dfc5ce4  node.exp
cfebbcd81db602b2f051328a9924e19ca2cb6235  node.lib
a31694cb9e03d13a616f3cc634852d8dc98e69b8  node.pdb
5f94c1cc9301a8b85082fee7549aa376aedd8ec5  x64/node-v0.9.10-x64.msi
7716c01ce60f7c65100cc405726badf8476afebd  x64/node.exe
121d4a721968ba4631d29de07e5d6c326d259b4b  x64/node.exp
17f651000f6b0c840efe539ae5257cb894481c49  x64/node.lib
d04242a5ec3a3104931ec8de6a846a8f7746b1fd  x64/node.pdb
```
