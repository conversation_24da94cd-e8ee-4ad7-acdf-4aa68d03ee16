---
date: '2018-03-21T15:54:22.477Z'
category: release
title: Node v9.9.0 (Current)
layout: blog-post
author: <PERSON><PERSON>
---

### Notable Changes

- **assert**:
  - From now on all error messages produced by `assert` in strict mode will produce a error diff. (<PERSON><PERSON>) [#17615](https://github.com/nodejs/node/pull/17615)
  - From now on it is possible to use a validation object in throws instead of the other possibilities. (<PERSON><PERSON>) [#17584](https://github.com/nodejs/node/pull/17584)
- **crypto**:
  - allow passing null as IV unless required (<PERSON>) [#18644](https://github.com/nodejs/node/pull/18644)
- **fs**:
  - support as and as+ flags in stringToFlags() (Sarat Addepalli) [#18801](https://github.com/nodejs/node/pull/18801)
- **tls**:
  - expose Finished messages in TLSSocket (<PERSON>) [#19102](https://github.com/nodejs/node/pull/19102)
- **tty**:
  - Add getColorDepth function to determine if terminal supports colors. (<PERSON><PERSON>) [#17615](https://github.com/nodejs/node/pull/17615)
- **util**:
  - add util.inspect compact option (Ruben Bridgewater) [#17576](https://github.com/nodejs/node/pull/17576)
- **Added new collaborators**
  - [watson](https://github.com/watson) Thomas Watson

### Commits

- [[`acc86ed246`](https://github.com/nodejs/node/commit/acc86ed246)] - 2018-03-XX, Version 9.9.0 (Current) (Michaël Zasso)
- [[`8d33e5c214`](https://github.com/nodejs/node/commit/8d33e5c214)] - **assert**: improve error check (Ruben Bridgewater) [#17574](https://github.com/nodejs/node/pull/17574)
- [[`5e6b42ec9c`](https://github.com/nodejs/node/commit/5e6b42ec9c)] - **assert**: show proper differences (Ruben Bridgewater) [#18611](https://github.com/nodejs/node/pull/18611)
- [[`9abbb6b857`](https://github.com/nodejs/node/commit/9abbb6b857)] - **assert**: fix infinite loop (Ruben Bridgewater) [#18611](https://github.com/nodejs/node/pull/18611)
- [[`e9ac468146`](https://github.com/nodejs/node/commit/e9ac468146)] - **assert**: fix throws trace (Ruben Bridgewater) [#18595](https://github.com/nodejs/node/pull/18595)
- [[`d3c2534bbe`](https://github.com/nodejs/node/commit/d3c2534bbe)] - **assert**: use destructuring for errors (Ruben Bridgewater) [#18247](https://github.com/nodejs/node/pull/18247)
- [[`5aa3a2d172`](https://github.com/nodejs/node/commit/5aa3a2d172)] - **(SEMVER-MINOR)** **assert**: improve error messages (Ruben Bridgewater) [#17615](https://github.com/nodejs/node/pull/17615)
- [[`f96ea47cf5`](https://github.com/nodejs/node/commit/f96ea47cf5)] - **assert**: fix strict regression (Ruben Bridgewater) [#17903](https://github.com/nodejs/node/pull/17903)
- [[`ebd60fa505`](https://github.com/nodejs/node/commit/ebd60fa505)] - **(SEMVER-MINOR)** **assert**: .throws accept objects (Ruben Bridgewater) [#17584](https://github.com/nodejs/node/pull/17584)
- [[`612ba1a3f0`](https://github.com/nodejs/node/commit/612ba1a3f0)] - **(SEMVER-MINOR)** **assert**: improve assert.throws (Ruben Bridgewater) [#17585](https://github.com/nodejs/node/pull/17585)
- [[`24aeca7dd5`](https://github.com/nodejs/node/commit/24aeca7dd5)] - **assert**: fix throws and doesNotThrow stack frames (Ruben Bridgewater) [#17703](https://github.com/nodejs/node/pull/17703)
- [[`db73d1c13b`](https://github.com/nodejs/node/commit/db73d1c13b)] - **assert**: use object argument in innerFail (Ruben Bridgewater) [#17582](https://github.com/nodejs/node/pull/17582)
- [[`bae5de1949`](https://github.com/nodejs/node/commit/bae5de1949)] - **(SEMVER-MINOR)** **assert**: add strict functionality export (Ruben Bridgewater) [#17002](https://github.com/nodejs/node/pull/17002)
- [[`f0f31d080a`](https://github.com/nodejs/node/commit/f0f31d080a)] - **async_hooks**: add copyHooks function (Daniel Bevenius) [#19391](https://github.com/nodejs/node/pull/19391)
- [[`71b1c7f79f`](https://github.com/nodejs/node/commit/71b1c7f79f)] - **async_hooks**: don't set hook_fields\[kTotals\] to 0 (Daniel Bevenius) [#19219](https://github.com/nodejs/node/pull/19219)
- [[`530b8a4077`](https://github.com/nodejs/node/commit/530b8a4077)] - **benchmark**: fix benchmark for url (Sergey Golovin) [#19084](https://github.com/nodejs/node/pull/19084)
- [[`563bed00f5`](https://github.com/nodejs/node/commit/563bed00f5)] - **benchmark,lib,test,tools**: use consistent quotes (Rich Trott) [#19156](https://github.com/nodejs/node/pull/19156)
- [[`3f7c4eea04`](https://github.com/nodejs/node/commit/3f7c4eea04)] - **build**: do not cd on vcbuild help (Vse Mozhet Byt) [#19291](https://github.com/nodejs/node/pull/19291)
- [[`5a1437cdbd`](https://github.com/nodejs/node/commit/5a1437cdbd)] - **build**: update arm64 minimum supported platform (Gibson Fahnestock) [#19164](https://github.com/nodejs/node/pull/19164)
- [[`07845fc19e`](https://github.com/nodejs/node/commit/07845fc19e)] - **console**: port errors to new system (Ruben Bridgewater) [#18857](https://github.com/nodejs/node/pull/18857)
- [[`03c321a713`](https://github.com/nodejs/node/commit/03c321a713)] - **(SEMVER-MINOR)** **crypto**: allow passing null as IV unless required (Tobias Nießen) [#18644](https://github.com/nodejs/node/pull/18644)
- [[`044995e546`](https://github.com/nodejs/node/commit/044995e546)] - **crypto**: use bool over int consistently (Tobias Nießen) [#19238](https://github.com/nodejs/node/pull/19238)
- [[`36f664ef9a`](https://github.com/nodejs/node/commit/36f664ef9a)] - **deps**: V8: backport 596d55a from upstream (Myles Borins) [#19477](https://github.com/nodejs/node/pull/19477)
- [[`5966b8cc06`](https://github.com/nodejs/node/commit/5966b8cc06)] - **deps**: v8: cherry-pick fixes for v8:7535 (Flarna) [#19333](https://github.com/nodejs/node/pull/19333)
- [[`cb732aeda4`](https://github.com/nodejs/node/commit/cb732aeda4)] - **doc**: enable eslint prefer-template rule (Ruben Bridgewater) [#18831](https://github.com/nodejs/node/pull/18831)
- [[`ff82acb95a`](https://github.com/nodejs/node/commit/ff82acb95a)] - **doc**: update buffer examples (Ruben Bridgewater) [#18758](https://github.com/nodejs/node/pull/18758)
- [[`a4c28d77f7`](https://github.com/nodejs/node/commit/a4c28d77f7)] - **doc**: fix deprecation removed by mistake (Michaël Zasso) [#19482](https://github.com/nodejs/node/pull/19482)
- [[`b229912f6f`](https://github.com/nodejs/node/commit/b229912f6f)] - **doc**: do not announce obvious examples (Rich Trott) [#19270](https://github.com/nodejs/node/pull/19270)
- [[`c1fa0926e3`](https://github.com/nodejs/node/commit/c1fa0926e3)] - **doc**: fix typos on n-api (Kyle Robinson Young) [#19385](https://github.com/nodejs/node/pull/19385)
- [[`99e6734f19`](https://github.com/nodejs/node/commit/99e6734f19)] - **doc**: improve best practices in onboarding-extras (Rich Trott) [#19315](https://github.com/nodejs/node/pull/19315)
- [[`5a56327e79`](https://github.com/nodejs/node/commit/5a56327e79)] - **doc**: fix minor issues in async_hooks.md (Rich Trott) [#19313](https://github.com/nodejs/node/pull/19313)
- [[`5da3ee7719`](https://github.com/nodejs/node/commit/5da3ee7719)] - **doc**: clarify default TLS handshake timeout (Rich Trott) [#19290](https://github.com/nodejs/node/pull/19290)
- [[`7f652c2bcc`](https://github.com/nodejs/node/commit/7f652c2bcc)] - **doc**: update username and email (Yuta Hiroto) [#19338](https://github.com/nodejs/node/pull/19338)
- [[`e247f19ac3`](https://github.com/nodejs/node/commit/e247f19ac3)] - **doc**: improve style guide text (Rich Trott) [#19269](https://github.com/nodejs/node/pull/19269)
- [[`c9b12f302a`](https://github.com/nodejs/node/commit/c9b12f302a)] - **doc**: remove superfluous text in onboarding-extras (Rich Trott) [#19247](https://github.com/nodejs/node/pull/19247)
- [[`6c5afebf55`](https://github.com/nodejs/node/commit/6c5afebf55)] - **doc**: make caveat in stream.md more concise (Rich Trott) [#19251](https://github.com/nodejs/node/pull/19251)
- [[`8e88a180b9`](https://github.com/nodejs/node/commit/8e88a180b9)] - **doc**: add warning to assert.doesNotThrow() (Ruben Bridgewater) [#18699](https://github.com/nodejs/node/pull/18699)
- [[`a04e4ae5e4`](https://github.com/nodejs/node/commit/a04e4ae5e4)] - **doc**: remove confusing "cats" from style guide (Rich Trott) [#19246](https://github.com/nodejs/node/pull/19246)
- [[`7c3617558e`](https://github.com/nodejs/node/commit/7c3617558e)] - **doc**: remove superfluous adverb from style guide (Rich Trott) [#19246](https://github.com/nodejs/node/pull/19246)
- [[`d117f5ff22`](https://github.com/nodejs/node/commit/d117f5ff22)] - **doc**: remove warning against readable/readable.read (Rich Trott) [#19193](https://github.com/nodejs/node/pull/19193)
- [[`5c21d16c31`](https://github.com/nodejs/node/commit/5c21d16c31)] - **doc**: add watson to collaborators (Thomas Watson) [#19234](https://github.com/nodejs/node/pull/19234)
- [[`9557e66ae1`](https://github.com/nodejs/node/commit/9557e66ae1)] - **doc**: update labels info in onboarding-extras.md (Rich Trott) [#19160](https://github.com/nodejs/node/pull/19160)
- [[`84acb9fae5`](https://github.com/nodejs/node/commit/84acb9fae5)] - **doc**: add inspector usage example (Ali Ijaz Sheikh) [#19172](https://github.com/nodejs/node/pull/19172)
- [[`27088cfaa7`](https://github.com/nodejs/node/commit/27088cfaa7)] - **doc**: improve onboarding instructions (Joyee Cheung) [#19108](https://github.com/nodejs/node/pull/19108)
- [[`9ec0eab019`](https://github.com/nodejs/node/commit/9ec0eab019)] - **doc**: make suggestion more direct in stream.md (Rich Trott) [#19124](https://github.com/nodejs/node/pull/19124)
- [[`968b867bf2`](https://github.com/nodejs/node/commit/968b867bf2)] - **doc**: document asserts Weak(Map|Set) behavior (Ruben Bridgewater) [#18248](https://github.com/nodejs/node/pull/18248)
- [[`745709396c`](https://github.com/nodejs/node/commit/745709396c)] - **(SEMVER-MINOR)** **doc**: improve .throws RegExp info (Ruben Bridgewater) [#17585](https://github.com/nodejs/node/pull/17585)
- [[`5a78c6c0a6`](https://github.com/nodejs/node/commit/5a78c6c0a6)] - **(SEMVER-MINOR)** **doc**: improve assert documentation (Ruben Bridgewater) [#17002](https://github.com/nodejs/node/pull/17002)
- [[`f4f0266bfe`](https://github.com/nodejs/node/commit/f4f0266bfe)] - **errors**: add comments about falsy error types (Ruben Bridgewater) [#18857](https://github.com/nodejs/node/pull/18857)
- [[`ffa16aad60`](https://github.com/nodejs/node/commit/ffa16aad60)] - **errors**: update all internal errors (Ruben Bridgewater) [#18857](https://github.com/nodejs/node/pull/18857)
- [[`d57a2421fc`](https://github.com/nodejs/node/commit/d57a2421fc)] - **errors**: implement new error handling (Ruben Bridgewater) [#18857](https://github.com/nodejs/node/pull/18857)
- [[`607b33cfcc`](https://github.com/nodejs/node/commit/607b33cfcc)] - **(SEMVER-MINOR)** **fs**: support as and as+ flags in stringToFlags() (Sarat Addepalli) [#18801](https://github.com/nodejs/node/pull/18801)
- [[`b01bd800c6`](https://github.com/nodejs/node/commit/b01bd800c6)] - **fs**: fix `createReadStream(…, {end: n})` for non-seekable fds (Anna Henningsen) [#19329](https://github.com/nodejs/node/pull/19329)
- [[`3914e97741`](https://github.com/nodejs/node/commit/3914e97741)] - **http2**: fixes error handling (Matteo Collina) [#19232](https://github.com/nodejs/node/pull/19232)
- [[`3bf69cd3e7`](https://github.com/nodejs/node/commit/3bf69cd3e7)] - **http2**: some general code improvements (James M Snell) [#19400](https://github.com/nodejs/node/pull/19400)
- [[`4277635bed`](https://github.com/nodejs/node/commit/4277635bed)] - **http2**: clean up Http2Settings (James M Snell) [#19400](https://github.com/nodejs/node/pull/19400)
- [[`42b6d801dc`](https://github.com/nodejs/node/commit/42b6d801dc)] - **http2**: don't aggressively inline (James M Snell) [#19400](https://github.com/nodejs/node/pull/19400)
- [[`89fbbc48ff`](https://github.com/nodejs/node/commit/89fbbc48ff)] - **http2**: simplify timeout tracking (Anna Henningsen) [#19206](https://github.com/nodejs/node/pull/19206)
- [[`f06622cd56`](https://github.com/nodejs/node/commit/f06622cd56)] - **lib**: define printErr() in script string (cjihrig) [#19285](https://github.com/nodejs/node/pull/19285)
- [[`b35eabb837`](https://github.com/nodejs/node/commit/b35eabb837)] - **lib**: handle `throw undefined` in assert.throws() (Ben Noordhuis) [#18029](https://github.com/nodejs/node/pull/18029)
- [[`0e6f720991`](https://github.com/nodejs/node/commit/0e6f720991)] - **n-api**: separate out async_hooks test (Gabriel Schulhof) [#19392](https://github.com/nodejs/node/pull/19392)
- [[`528798c3f4`](https://github.com/nodejs/node/commit/528798c3f4)] - **n-api**: add missing exception checking (Michael Dawson) [#19362](https://github.com/nodejs/node/pull/19362)
- [[`f679ac19e0`](https://github.com/nodejs/node/commit/f679ac19e0)] - **n-api**: resolve promise in test (Gabriel Schulhof) [#19245](https://github.com/nodejs/node/pull/19245)
- [[`12f19a6b86`](https://github.com/nodejs/node/commit/12f19a6b86)] - **n-api**: update documentation (Gabriel Schulhof) [#19078](https://github.com/nodejs/node/pull/19078)
- [[`0c9577edfc`](https://github.com/nodejs/node/commit/0c9577edfc)] - **n-api,test**: add int64 bounds tests (Kyle Farnung) [#19309](https://github.com/nodejs/node/pull/19309)
- [[`f36521becf`](https://github.com/nodejs/node/commit/f36521becf)] - **n-api,test**: add a new.target test to addons-napi (Taylor Woll) [#19236](https://github.com/nodejs/node/pull/19236)
- [[`5b12d3a58e`](https://github.com/nodejs/node/commit/5b12d3a58e)] - **net**: do not inherit the no-half-open enforcer (Luigi Pinca) [#18974](https://github.com/nodejs/node/pull/18974)
- [[`a9bd8bff8a`](https://github.com/nodejs/node/commit/a9bd8bff8a)] - **path**: remove redundant function (Sergey Golovin) [#19237](https://github.com/nodejs/node/pull/19237)
- [[`55f7bbb0bd`](https://github.com/nodejs/node/commit/55f7bbb0bd)] - **repl**: refactor code for readability (Ruben Bridgewater) [#17919](https://github.com/nodejs/node/pull/17919)
- [[`6997af7378`](https://github.com/nodejs/node/commit/6997af7378)] - **repl**: upper case comments first char (Ruben Bridgewater) [#17919](https://github.com/nodejs/node/pull/17919)
- [[`3e6858e4a7`](https://github.com/nodejs/node/commit/3e6858e4a7)] - **repl**: better handling of recoverable errors (Prince J Wesley) [#18915](https://github.com/nodejs/node/pull/18915)
- [[`49391a70e1`](https://github.com/nodejs/node/commit/49391a70e1)] - **src**: fix util abort (Ruben Bridgewater) [#19223](https://github.com/nodejs/node/pull/19223)
- [[`1ba1861731`](https://github.com/nodejs/node/commit/1ba1861731)] - **src**: remove unused using declarations async_wrap (Daniel Bevenius) [#18893](https://github.com/nodejs/node/pull/18893)
- [[`8757799d69`](https://github.com/nodejs/node/commit/8757799d69)] - **src**: remove unused stdlib.h include (Daniel Bevenius) [#19427](https://github.com/nodejs/node/pull/19427)
- [[`da62c5ca68`](https://github.com/nodejs/node/commit/da62c5ca68)] - **src**: fix minor typo in comment stream_base.h (Daniel Bevenius) [#19429](https://github.com/nodejs/node/pull/19429)
- [[`43c482b9c8`](https://github.com/nodejs/node/commit/43c482b9c8)] - **src**: fix indentation of params in env-inl.h (Daniel Bevenius) [#19390](https://github.com/nodejs/node/pull/19390)
- [[`054dd28da6`](https://github.com/nodejs/node/commit/054dd28da6)] - **src**: make AsyncWrap constructors delegate (Daniel Bevenius) [#19366](https://github.com/nodejs/node/pull/19366)
- [[`7a3d1d205e`](https://github.com/nodejs/node/commit/7a3d1d205e)] - **src**: remove unused uv.h include from async_wrap.cc (Daniel Bevenius) [#19342](https://github.com/nodejs/node/pull/19342)
- [[`126a161928`](https://github.com/nodejs/node/commit/126a161928)] - **src**: fix indenting of wrap-\>EmitTraceEventBefore (Daniel Bevenius) [#19340](https://github.com/nodejs/node/pull/19340)
- [[`03fb817a1d`](https://github.com/nodejs/node/commit/03fb817a1d)] - **src**: add extractPromiseWrap function (Daniel Bevenius) [#19340](https://github.com/nodejs/node/pull/19340)
- [[`e208282f68`](https://github.com/nodejs/node/commit/e208282f68)] - **src**: refactor emit before/after/promiseResolve (Daniel Bevenius) [#19295](https://github.com/nodejs/node/pull/19295)
- [[`49481d0e3b`](https://github.com/nodejs/node/commit/49481d0e3b)] - **src**: add convenience ctor for async trigger id scope (Anna Henningsen) [#19204](https://github.com/nodejs/node/pull/19204)
- [[`4b9914a318`](https://github.com/nodejs/node/commit/4b9914a318)] - **src**: avoid duplicate Before/AtExitCallback structs (Daniel Bevenius) [#19226](https://github.com/nodejs/node/pull/19226)
- [[`27754c5408`](https://github.com/nodejs/node/commit/27754c5408)] - **src**: add incr/decr operators for Reference (Daniel Bevenius) [#19083](https://github.com/nodejs/node/pull/19083)
- [[`64f646269a`](https://github.com/nodejs/node/commit/64f646269a)] - **src**: use smart pointer in AsyncWrap::WeakCallback (Daniel Bevenius) [#19168](https://github.com/nodejs/node/pull/19168)
- [[`152c931f53`](https://github.com/nodejs/node/commit/152c931f53)] - **stream**: make Duplex inherits from DuplexBase (Luigi Pinca) [#18974](https://github.com/nodejs/node/pull/18974)
- [[`9c0c0e68ac`](https://github.com/nodejs/node/commit/9c0c0e68ac)] - **stream**: add no-half-open enforcer only if needed (Luigi Pinca) [#18953](https://github.com/nodejs/node/pull/18953)
- [[`1eac1d7d85`](https://github.com/nodejs/node/commit/1eac1d7d85)] - **test**: minor refactoring (Ruben Bridgewater) [#18669](https://github.com/nodejs/node/pull/18669)
- [[`574d061c3c`](https://github.com/nodejs/node/commit/574d061c3c)] - **test**: remove assert.doesNotThrow() (Ruben Bridgewater) [#18669](https://github.com/nodejs/node/pull/18669)
- [[`5478746203`](https://github.com/nodejs/node/commit/5478746203)] - **test**: refactor assert test (Ruben Bridgewater) [#18610](https://github.com/nodejs/node/pull/18610)
- [[`4e9279df5c`](https://github.com/nodejs/node/commit/4e9279df5c)] - **test**: remove NodeTestFixture from Env constructor (Daniel Bevenius) [#18558](https://github.com/nodejs/node/pull/18558)
- [[`22b8f9fba6`](https://github.com/nodejs/node/commit/22b8f9fba6)] - **test**: introduce SetUpTestCase/TearDownTestCase (Daniel Bevenius) [#18558](https://github.com/nodejs/node/pull/18558)
- [[`519850f21e`](https://github.com/nodejs/node/commit/519850f21e)] - **test**: http2 client setNextStreamID errors (Trivikram) [#18848](https://github.com/nodejs/node/pull/18848)
- [[`e3ce084f7c`](https://github.com/nodejs/node/commit/e3ce084f7c)] - **test**: fix flaky test-http2-ping-flood (Rich Trott) [#19395](https://github.com/nodejs/node/pull/19395)
- [[`7df6d9ddc8`](https://github.com/nodejs/node/commit/7df6d9ddc8)] - **test**: rename regression tests file names (Ujjwal Sharma) [#19332](https://github.com/nodejs/node/pull/19332)
- [[`f49042131a`](https://github.com/nodejs/node/commit/f49042131a)] - **test**: use descriptive names for regression tests (Ujjwal Sharma) [#19275](https://github.com/nodejs/node/pull/19275)
- [[`01749f07bd`](https://github.com/nodejs/node/commit/01749f07bd)] - **test**: fix flaky test-http2-settings-flood (Rich Trott) [#19349](https://github.com/nodejs/node/pull/19349)
- [[`9aa5090689`](https://github.com/nodejs/node/commit/9aa5090689)] - **test**: fix test-cluster-send-handle-large-payload (Rich Trott) [#19311](https://github.com/nodejs/node/pull/19311)
- [[`11a0ef566a`](https://github.com/nodejs/node/commit/11a0ef566a)] - **test**: delete test/parallel/test-regress-GH-4948 (Ujjwal Sharma) [#19279](https://github.com/nodejs/node/pull/19279)
- [[`be20914958`](https://github.com/nodejs/node/commit/be20914958)] - **test**: shared lib build doesn't handle SIGPIPE (Yihong Wang) [#19211](https://github.com/nodejs/node/pull/19211)
- [[`f84f548986`](https://github.com/nodejs/node/commit/f84f548986)] - **test**: fix assertion argument order (Rich Trott) [#19264](https://github.com/nodejs/node/pull/19264)
- [[`84ae59e5f8`](https://github.com/nodejs/node/commit/84ae59e5f8)] - **test**: fix path in doctool/test-doctool-json (Vse Mozhet Byt) [#19287](https://github.com/nodejs/node/pull/19287)
- [[`b8ca616baa`](https://github.com/nodejs/node/commit/b8ca616baa)] - **test**: fix compiler warnings in callback-scope (Daniel Bevenius) [#19252](https://github.com/nodejs/node/pull/19252)
- [[`d3bc72e9cc`](https://github.com/nodejs/node/commit/d3bc72e9cc)] - **test**: name test files appropriately (Ujjwal Sharma) [#19212](https://github.com/nodejs/node/pull/19212)
- [[`f0c8f6969f`](https://github.com/nodejs/node/commit/f0c8f6969f)] - **test**: fix test-abort-backtrace in shared lib build (Yihong Wang) [#19213](https://github.com/nodejs/node/pull/19213)
- [[`e4c320e5d7`](https://github.com/nodejs/node/commit/e4c320e5d7)] - **test**: Remove unnecessary asserion messages in test-crypto-hash.js (Piotr Grzesik) [#18984](https://github.com/nodejs/node/pull/18984)
- [[`411f3e03fe`](https://github.com/nodejs/node/commit/411f3e03fe)] - **test**: remove flaky status for test-npm-install (Rich Trott) [#19216](https://github.com/nodejs/node/pull/19216)
- [[`a4a4819954`](https://github.com/nodejs/node/commit/a4a4819954)] - **test**: do not check text for engine-generated error (Rich Trott) [#19215](https://github.com/nodejs/node/pull/19215)
- [[`38eb432260`](https://github.com/nodejs/node/commit/38eb432260)] - **test**: refactor http-https-default-ports (Ken Lin) [#19130](https://github.com/nodejs/node/pull/19130)
- [[`0ece7cc227`](https://github.com/nodejs/node/commit/0ece7cc227)] - **test**: rename test-regress-GH-877.js (Ujjwal Sharma) [#19161](https://github.com/nodejs/node/pull/19161)
- [[`636a5f627e`](https://github.com/nodejs/node/commit/636a5f627e)] - **test**: rename test-regress-GH-784.js (Ujjwal Sharma) [#19161](https://github.com/nodejs/node/pull/19161)
- [[`c0c6d5848f`](https://github.com/nodejs/node/commit/c0c6d5848f)] - **test**: address nits and rename the corresponding fixture (Ujjwal Sharma) [#19161](https://github.com/nodejs/node/pull/19161)
- [[`22484e1fb2`](https://github.com/nodejs/node/commit/22484e1fb2)] - **test**: rename tests to remove "regress" keyword (Ujjwal Sharma) [#19161](https://github.com/nodejs/node/pull/19161)
- [[`2262a34f0b`](https://github.com/nodejs/node/commit/2262a34f0b)] - **test**: rename test-regress-GH-4027 (Ujjwal Sharma) [#19161](https://github.com/nodejs/node/pull/19161)
- [[`bdbfc0e20e`](https://github.com/nodejs/node/commit/bdbfc0e20e)] - **test**: rename test-regress-GH-4015 (Ujjwal Sharma) [#19161](https://github.com/nodejs/node/pull/19161)
- [[`da44c2ccf0`](https://github.com/nodejs/node/commit/da44c2ccf0)] - **test**: rename test-regress-GH-1697 (Ujjwal Sharma) [#19161](https://github.com/nodejs/node/pull/19161)
- [[`fa43d2f69e`](https://github.com/nodejs/node/commit/fa43d2f69e)] - **test**: rename test-regress-GH-1726 (Ujjwal Sharma) [#19161](https://github.com/nodejs/node/pull/19161)
- [[`46b5915dab`](https://github.com/nodejs/node/commit/46b5915dab)] - **test**: skip postmortem metadata test when nm fails (Joyee Cheung) [#19107](https://github.com/nodejs/node/pull/19107)
- [[`16ab3b54d1`](https://github.com/nodejs/node/commit/16ab3b54d1)] - **test**: address unreliable test-performance (Rich Trott) [#19228](https://github.com/nodejs/node/pull/19228)
- [[`1e5c7e3e47`](https://github.com/nodejs/node/commit/1e5c7e3e47)] - **test**: refactor common.expectsError (Ruben Bridgewater) [#17703](https://github.com/nodejs/node/pull/17703)
- [[`060216689a`](https://github.com/nodejs/node/commit/060216689a)] - **(SEMVER-MINOR)** **tls**: expose Finished messages in TLSSocket (Anton Salikhmetov) [#19102](https://github.com/nodejs/node/pull/19102)
- [[`b04dd7b351`](https://github.com/nodejs/node/commit/b04dd7b351)] - **tools**: enable eslint one-var rule (Ruben Bridgewater) [#18831](https://github.com/nodejs/node/pull/18831)
- [[`d4d7df8371`](https://github.com/nodejs/node/commit/d4d7df8371)] - **tools**: enable eslint strict key-spacing (Ruben Bridgewater) [#18831](https://github.com/nodejs/node/pull/18831)
- [[`9e10ddc215`](https://github.com/nodejs/node/commit/9e10ddc215)] - **tools**: enable eslint no-undef-init rule (Ruben Bridgewater) [#18831](https://github.com/nodejs/node/pull/18831)
- [[`9d1e409ee3`](https://github.com/nodejs/node/commit/9d1e409ee3)] - **tools**: enable no-unsafe-finally (Ruben Bridgewater) [#18745](https://github.com/nodejs/node/pull/18745)
- [[`d7958657d7`](https://github.com/nodejs/node/commit/d7958657d7)] - **tools**: add assert.doesNotThrow eslint rule (Ruben Bridgewater) [#18669](https://github.com/nodejs/node/pull/18669)
- [[`66694e28b1`](https://github.com/nodejs/node/commit/66694e28b1)] - **tools**: fix test-npm-package (Michaël Zasso) [#19293](https://github.com/nodejs/node/pull/19293)
- [[`9613e02ff7`](https://github.com/nodejs/node/commit/9613e02ff7)] - **tools,bootstrap**: preprocess gypi files to json (Gus Caplan) [#19140](https://github.com/nodejs/node/pull/19140)
- [[`74f0d1aa60`](https://github.com/nodejs/node/commit/74f0d1aa60)] - **(SEMVER-MINOR)** **tty**: refactor to es6 (Ruben Bridgewater) [#17615](https://github.com/nodejs/node/pull/17615)
- [[`ead727c274`](https://github.com/nodejs/node/commit/ead727c274)] - **(SEMVER-MINOR)** **tty**: add getColorDepth function (Ruben Bridgewater) [#17615](https://github.com/nodejs/node/pull/17615)
- [[`072adfea8c`](https://github.com/nodejs/node/commit/072adfea8c)] - **url**: replace "magic" numbers by constants (Sergey Golovin) [#19035](https://github.com/nodejs/node/pull/19035)
- [[`c18ac52970`](https://github.com/nodejs/node/commit/c18ac52970)] - **(SEMVER-MINOR)** **util**: add util.inspect compact option (Ruben Bridgewater) [#17576](https://github.com/nodejs/node/pull/17576)
- [[`ce3a5af69f`](https://github.com/nodejs/node/commit/ce3a5af69f)] - **(SEMVER-MINOR)** **util**: rename util.inspect argument (Ruben Bridgewater) [#17576](https://github.com/nodejs/node/pull/17576)
- [[`fd4c05ab56`](https://github.com/nodejs/node/commit/fd4c05ab56)] - **(SEMVER-MINOR)** **util**: fix custom inspect description (Ruben Bridgewater) [#17576](https://github.com/nodejs/node/pull/17576)

Windows 32-bit Installer: https://nodejs.org/dist/v9.9.0/node-v9.9.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v9.9.0/node-v9.9.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v9.9.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v9.9.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v9.9.0/node-v9.9.0.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v9.9.0/node-v9.9.0-darwin-x64.tar.gz \
Linux 32-bit Binary: https://nodejs.org/dist/v9.9.0/node-v9.9.0-linux-x86.tar.xz \
Linux 64-bit Binary: https://nodejs.org/dist/v9.9.0/node-v9.9.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v9.9.0/node-v9.9.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v9.9.0/node-v9.9.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v9.9.0/node-v9.9.0-aix-ppc64.tar.gz \
SmartOS 32-bit Binary: https://nodejs.org/dist/v9.9.0/node-v9.9.0-sunos-x86.tar.xz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v9.9.0/node-v9.9.0-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v9.9.0/node-v9.9.0-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v9.9.0/node-v9.9.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v9.9.0/node-v9.9.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v9.9.0/node-v9.9.0.tar.gz \
Other release files: https://nodejs.org/dist/v9.9.0/ \
Documentation: https://nodejs.org/docs/v9.9.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

204f65d57e940913144cc25e2e835c1f64c68938c0c1217f0954ff045e233f5e  node-v9.9.0-aix-ppc64.tar.gz
15e71817fe172119d6f9e0166ab869f12c67bdb7371d27a78b0621c408239692  node-v9.9.0-darwin-x64.tar.gz
f1c410a034da2a109e50443438fa57491d7842a189f31fe684a20f361f348a4a  node-v9.9.0-darwin-x64.tar.xz
f03a97b8cbefe10914343d3ef0d9df959e7d2a43a6640e5a262e2f3fa6981f4b  node-v9.9.0-headers.tar.gz
b42d1843661e734dd01351a87e3c7ff423fdcb8b49b600c2c076352f1b7fa97f  node-v9.9.0-headers.tar.xz
ebc0f4cc33fb8ee11c9d6d8bca6962aabe3ae4c88207b301ca4a692726e9e125  node-v9.9.0-linux-arm64.tar.gz
06feb32656c91c3963270de18d4a7befe52d2d8c0fd78fbbb7792a881ffeec7a  node-v9.9.0-linux-arm64.tar.xz
0468e84d64d17179c8c11f7a7c97038b4cbc60a0850dadfdb46e718125389407  node-v9.9.0-linux-armv6l.tar.gz
f6a3bba18c238490fc685ed32dda91b09c92e3a0b73316b1dd0e4a4cb9bc3bfc  node-v9.9.0-linux-armv6l.tar.xz
040f5573e65a7ad3897fcd69aa28ed1fa9accdd969cba10541df06f4805bf1f3  node-v9.9.0-linux-armv7l.tar.gz
d8c4aa2c4a396a208c1ed7ce503d6bd7782460359bbedfb2ccfe48e3369ff4be  node-v9.9.0-linux-armv7l.tar.xz
d4cf175fa39f9f256bbdc6439ce327f079f065ad63e4e3a1e6f72b4463d54a7b  node-v9.9.0-linux-ppc64le.tar.gz
4aa332eedf958315a6f1900e7a32807d092f4a642a5c8cb81541d7fc450c306e  node-v9.9.0-linux-ppc64le.tar.xz
9ce3584db61d25ea36db410bb2836ac4474ffc0803dcde2a0bb9db848d93b805  node-v9.9.0-linux-s390x.tar.gz
8730779ae582fb03d0176b175df33669570242d9df55d6d261eb10b9fa29e97c  node-v9.9.0-linux-s390x.tar.xz
887cb4db6207f303b5ba15b6e15298f19d288fce2064e8caa7bb7cae170cbe85  node-v9.9.0-linux-x64.tar.gz
457151b4e6f27222232742ce44de1df73768a251309dddbfca2b385052ee977f  node-v9.9.0-linux-x64.tar.xz
9e29073aadc19be24cd46648eb99d60b3f0d42534d7ba85c152ac33be90685ac  node-v9.9.0-linux-x86.tar.gz
8b1f399b1fd258f28a3c6ec7dbab93cfd7c88c6f305d182f796934bac1ae63c3  node-v9.9.0-linux-x86.tar.xz
1ea9f7a2fee9ec282bf4e660167d73ab7c1202f4a8633f1e1d3ee1800ab105fa  node-v9.9.0.pkg
681a6faa44a4e22515b18bf6c295fb1283d2ec113f52376f8bcb3b0ba7283d03  node-v9.9.0-sunos-x64.tar.gz
bf0526de716825daacdd7a6b443163518685c42684c00c580eb6be492b2d7ea6  node-v9.9.0-sunos-x64.tar.xz
dfc223491e33b10f79810e42e431b774dab655f75f0a4c851971c7b630202eac  node-v9.9.0-sunos-x86.tar.gz
f3ae6e3503237486644a00ad03c8474fc5c87f464798db067c0401e700de9175  node-v9.9.0-sunos-x86.tar.xz
e774cf32bc7c1d61d2e654e67eaafd2a13f22f176933706de60250db5b5eabda  node-v9.9.0.tar.gz
ab8a721b8db1193754885e891ebde9a982c82fd9207df04a2aebafbb9fb27042  node-v9.9.0.tar.xz
de1a542b68500dae65389c1cec50ed907ee9dbebfa254c66e6ff2173c30aec89  node-v9.9.0-win-x64.7z
5186747c7cab6ff153e725c42446727e460d54e6c62353d5dc550046f5c05653  node-v9.9.0-win-x64.zip
a817ca204a40024cb9a81e950f7dfc5509a464bb7e971fd5dbeefae6b5c1ce34  node-v9.9.0-win-x86.7z
409020b145199b8fd2d0ba32a24e61e7daef0c6a9cac38f2bfaee0fef650c0af  node-v9.9.0-win-x86.zip
ece15616b9093eb6025a945f2121ab281f3e24932618babb641dc5497704b8a7  node-v9.9.0-x64.msi
f6abe59582aa068a9c48c3705f8c644c857900eebf321dcc3d488d181e7d3694  node-v9.9.0-x86.msi
2b03d19c6c8930cdc5cdf051b4eec1b0370f1bb9b798d38c6ed926b6fbf57baf  win-x64/node.exe
cafe53188185ee4041f608f60d8666bf2d86bc3acb7318df343998494ae64bbd  win-x64/node.lib
865b4af5272b26d06cf0ee49a9b1005eee68278b60230e370900d2b4137e7b37  win-x64/node_pdb.7z
8a89c6bf7edc55ded4cab59c17937bfdd5b41949bc6f5a63162a3a5995dcd446  win-x64/node_pdb.zip
816c93d4aae6839bd2afb71d3bf10c9495810836deffa783b3b686bbe0a36d6c  win-x86/node.exe
afe60cb7df8e42ccae4eb473a4f7bca0fb2c8431d333c56031408e85b752653b  win-x86/node.lib
72dab8e7207bcdd1172989db5668cfc65e36da8227746cf294d33d8339b60897  win-x86/node_pdb.7z
390c73e5d0a3b358a5aae55598b87e44a21d9dc6dddaf4b4e483ae56e196b615  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQEzBAEBCAAdFiEEDv/hvO/ZyE49CYFSkzsB9AtcqUYFAlqyfp4ACgkQkzsB9Atc
qUZ5MQf9Gt+FvCEB25FVGi7RUD7uElNjG7SVbZ730OGSiVQ3QIsglOaS/Y5qGSAh
vQxmnRjk5dSXkwj0PD+fGTJiwKn8fqkAz5MUvKmSZr0kZdlSFglAjpS23lx7EkiG
VuPGelK07hvTTUUw+Vu1za20BPKM4PXr6lzXn++DJsdWUBiKa2pH+akMaWXxkwVU
wT5v+eu4NCjyrDIPaiP1+LaTzZbqSh0iE4CaEwwEXwvCrmEw56bsP5G26hSORk61
kCMo8LNY5zEleyTY2z38ht95mXcbT5qvoEdsZRT79KGK2nHsLyyT9fTrd1J7b6m1
3uBemhUHr2cw1tClfHcKX42Juv6HXg==
=i5/q
-----END PGP SIGNATURE-----

```
