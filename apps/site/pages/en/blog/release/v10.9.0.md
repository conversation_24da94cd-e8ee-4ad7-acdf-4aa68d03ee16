---
date: '2018-08-16T01:53:21.643Z'
category: release
title: Node v10.9.0 (Current)
layout: blog-post
author: <PERSON>
---

**This is a security release, fixing a number of vulnerabilities in OpenSSL and Node.js.** Refer to the [August 2018 Security Releases](/blog/vulnerability/august-2018-security-releases/) announcement for full details.

### Notable Changes

- **buffer**:
  - Fix out-of-bounds (OOB) write in `Buffer.write()` for UCS-2 encoding (CVE-2018-12115)
  - Fix unintentional exposure of uninitialized memory in `Buffer.alloc()` (CVE-2018-7166)
- **deps**:
  - Upgrade to OpenSSL 1.1.0i, fixing:
    - Client DoS due to large DH parameter (CVE-2018-0732)
    - ECDSA key extraction via local side-channel (CVE not assigned)
  - Upgrade V8 from 6.7 to 6.8 (<PERSON><PERSON><PERSON>) [#21079](https://github.com/nodejs/node/pull/21079)
    - Memory reduction and performance improvements, details at: https://v8project.blogspot.com/2018/06/v8-release-68.html
- **http**: `http.get()` and `http.request()` (and `https` variants) can now accept three arguments to allow for a `URL` _and_ an `options` object (Sam Ruby) [#21616](https://github.com/nodejs/node/pull/21616)
- Added new collaborators
  - Sam Ruby (https://github.com/rubys)
  - George Adams (https://github.com/gdams)

### Commits

- [[`58a9ae118e`](https://github.com/nodejs/node/commit/58a9ae118e)] - **assert**: fix loose assert with map and set (Ruben Bridgewater) [#22145](https://github.com/nodejs/node/pull/22145)
- [[`1c577016b8`](https://github.com/nodejs/node/commit/1c577016b8)] - **benchmark**: improve assert benchmarks (Ruben Bridgewater) [#22211](https://github.com/nodejs/node/pull/22211)
- [[`734323d9eb`](https://github.com/nodejs/node/commit/734323d9eb)] - **buffer**: stop alloc() uninitialized memory return (cjihrig) [nodejs-private/node-private#137](https://github.com/nodejs-private/node-private/pull/137)
- [[`2c4c17b708`](https://github.com/nodejs/node/commit/2c4c17b708)] - **buffer**: avoid overrun on UCS-2 string write (Rod Vagg) [nodejs-private/node-private#138](https://github.com/nodejs-private/node-private/pull/138)
- [[`6622ac798d`](https://github.com/nodejs/node/commit/6622ac798d)] - **buffer**: use FastBuffer when fill is set to 0 (Сковорода Никита Андреевич) [#21989](https://github.com/nodejs/node/pull/21989)
- [[`f506a5f46e`](https://github.com/nodejs/node/commit/f506a5f46e)] - **build**: make --shared-\[...\]-path work on Windows (Jeremy Apthorp) [#21530](https://github.com/nodejs/node/pull/21530)
- [[`1be6fb93c8`](https://github.com/nodejs/node/commit/1be6fb93c8)] - **build**: add CONFIG_FLAGS to with-code-cache target (Daniel Bevenius) [#22207](https://github.com/nodejs/node/pull/22207)
- [[`4520bb8a73`](https://github.com/nodejs/node/commit/4520bb8a73)] - **build**: make tools/doc/node_modules non-phony (Daniel Bevenius) [#22189](https://github.com/nodejs/node/pull/22189)
- [[`c42ff4ebd8`](https://github.com/nodejs/node/commit/c42ff4ebd8)] - **build**: add crypto check to build targets (Daniel Bevenius) [#22148](https://github.com/nodejs/node/pull/22148)
- [[`cdb8c1b44d`](https://github.com/nodejs/node/commit/cdb8c1b44d)] - **build**: extract common parts from addon .buildstamp (Daniel Bevenius) [#22171](https://github.com/nodejs/node/pull/22171)
- [[`1e7a8c3016`](https://github.com/nodejs/node/commit/1e7a8c3016)] - **build**: reset embedder string to "-node.0" (Michaël Zasso) [#21079](https://github.com/nodejs/node/pull/21079)
- [[`86ab2c041e`](https://github.com/nodejs/node/commit/86ab2c041e)] - **crypto**: remove unused SSLWrap handle methods (Jon Moss) [#22216](https://github.com/nodejs/node/pull/22216)
- [[`9212875406`](https://github.com/nodejs/node/commit/9212875406)] - **crypto**: simplify state failure handling (Tobias Nießen) [#22131](https://github.com/nodejs/node/pull/22131)
- [[`916a1d59f0`](https://github.com/nodejs/node/commit/916a1d59f0)] - **crypto**: simplify Hmac::HmacUpdate (Tobias Nießen) [#22132](https://github.com/nodejs/node/pull/22132)
- [[`2dc7f17e8b`](https://github.com/nodejs/node/commit/2dc7f17e8b)] - **(SEMVER-MINOR)** **crypto**: add better scrypt option aliases (Anna Henningsen) [#21525](https://github.com/nodejs/node/pull/21525)
- [[`fcf422e921`](https://github.com/nodejs/node/commit/fcf422e921)] - **deps**: backport c608122b from upstream (Ruben Bridgewater) [#22210](https://github.com/nodejs/node/pull/22210)
- [[`a07ccaeb19`](https://github.com/nodejs/node/commit/a07ccaeb19)] - **deps**: update archs files for OpenSSL-1.1.0i (Shigeki Ohtsu) [#22318](https://github.com/nodejs/node/pull/22318)
- [[`473996c90f`](https://github.com/nodejs/node/commit/473996c90f)] - **deps**: add s390 asm rules for OpenSSL-1.1.0 (Shigeki Ohtsu) [#19794](https://github.com/nodejs/node/pull/19794)
- [[`05e48fd018`](https://github.com/nodejs/node/commit/05e48fd018)] - **deps**: upgrade openssl sources to 1.1.0i (Shigeki Ohtsu) [#22318](https://github.com/nodejs/node/pull/22318)
- [[`f8bc5d6320`](https://github.com/nodejs/node/commit/f8bc5d6320)] - **deps**: cherry-pick 09bca09 from upstream V8 (Matheus Marchini) [#22068](https://github.com/nodejs/node/pull/22068)
- [[`c69fdc9d5f`](https://github.com/nodejs/node/commit/c69fdc9d5f)] - **(SEMVER-MINOR)** **deps**: remove thread_local to fix V8 compilation (Peter Marshall) [#21668](https://github.com/nodejs/node/pull/21668)
- [[`981fff714e`](https://github.com/nodejs/node/commit/981fff714e)] - **deps**: refactor v8.gyp (Michaël Zasso) [#22017](https://github.com/nodejs/node/pull/22017)
- [[`5fa3ffad20`](https://github.com/nodejs/node/commit/5fa3ffad20)] - **(SEMVER-MINOR)** **deps**: patch the V8 API to be backwards compatible with 6.7 (Peter Marshall) [#21668](https://github.com/nodejs/node/pull/21668)
- [[`6eed40acbb`](https://github.com/nodejs/node/commit/6eed40acbb)] - **deps**: cherry-pick 804a693 from upstream V8 (Matheus Marchini) [#21855](https://github.com/nodejs/node/pull/21855)
- [[`7eccaf86d6`](https://github.com/nodejs/node/commit/7eccaf86d6)] - **deps**: V8: Backport of 0dd3390 from upstream (James M Snell) [#21899](https://github.com/nodejs/node/pull/21899)
- [[`328c89925a`](https://github.com/nodejs/node/commit/328c89925a)] - **deps**: cherry-pick 907d7bc from upstream V8 (Michaël Zasso) [#21838](https://github.com/nodejs/node/pull/21838)
- [[`afacfd2992`](https://github.com/nodejs/node/commit/afacfd2992)] - **deps**: cherry-pick 2075910 from upstream V8 (Michaël Zasso) [#21838](https://github.com/nodejs/node/pull/21838)
- [[`4f24256274`](https://github.com/nodejs/node/commit/4f24256274)] - **deps**: cherry-pick 555c811 from upstream V8 (Anna Henningsen) [#21741](https://github.com/nodejs/node/pull/21741)
- [[`7b4272a14d`](https://github.com/nodejs/node/commit/7b4272a14d)] - **deps**: cherry-pick 477df06 from upstream v8 (Gus Caplan) [#21644](https://github.com/nodejs/node/pull/21644)
- [[`a0bf7aa07c`](https://github.com/nodejs/node/commit/a0bf7aa07c)] - **deps**: cherry-pick 70c4340 from upstream V8 (Matheus Marchini) [#21126](https://github.com/nodejs/node/pull/21126)
- [[`4994ac65b0`](https://github.com/nodejs/node/commit/4994ac65b0)] - **deps**: cherry-pick acc336c from upstream V8 (Matheus Marchini) [#21126](https://github.com/nodejs/node/pull/21126)
- [[`be569f82f1`](https://github.com/nodejs/node/commit/be569f82f1)] - **deps**: cherry-pick b20faff from upstream V8 (Matheus Marchini) [#21126](https://github.com/nodejs/node/pull/21126)
- [[`6df5feb13f`](https://github.com/nodejs/node/commit/6df5feb13f)] - **deps**: cherry-pick aa6ce3e from upstream V8 (Michaël Zasso) [#21079](https://github.com/nodejs/node/pull/21079)
- [[`8b9a956f9e`](https://github.com/nodejs/node/commit/8b9a956f9e)] - **deps**: cherry-pick 5dd3395 from upstream V8 (Matheus Marchini) [#21386](https://github.com/nodejs/node/pull/21386)
- [[`548008a6f6`](https://github.com/nodejs/node/commit/548008a6f6)] - **deps**: update v8.gyp and run Torque (Michaël Zasso) [#21079](https://github.com/nodejs/node/pull/21079)
- [[`9c74271a96`](https://github.com/nodejs/node/commit/9c74271a96)] - **deps**: update V8 to 6.8.275.24 (Michaël Zasso) [#21079](https://github.com/nodejs/node/pull/21079)
- [[`a3f3c40966`](https://github.com/nodejs/node/commit/a3f3c40966)] - **doc**: simplify urlObject.hash text (Rich Trott) [#22326](https://github.com/nodejs/node/pull/22326)
- [[`d2848697dc`](https://github.com/nodejs/node/commit/d2848697dc)] - **doc**: simplify urlObject.hash description (Rich Trott) [#22326](https://github.com/nodejs/node/pull/22326)
- [[`6d29986f4d`](https://github.com/nodejs/node/commit/6d29986f4d)] - **doc**: simplify format description of urlObject.auth (Rich Trott) [#22324](https://github.com/nodejs/node/pull/22324)
- [[`a658a4df34`](https://github.com/nodejs/node/commit/a658a4df34)] - **doc**: remove redundant explanation of format (Rich Trott) [#22324](https://github.com/nodejs/node/pull/22324)
- [[`3236697c0b`](https://github.com/nodejs/node/commit/3236697c0b)] - **doc**: use italics for words-as-words (Rich Trott) [#22324](https://github.com/nodejs/node/pull/22324)
- [[`da76b61f59`](https://github.com/nodejs/node/commit/da76b61f59)] - **doc**: bump ICU version to avoid confusion (Csaba Palfi) [#22313](https://github.com/nodejs/node/pull/22313)
- [[`e04b0532bf`](https://github.com/nodejs/node/commit/e04b0532bf)] - **doc**: document 'inherit' option for stdio (non-shorthand) (James Bromwell) [#22309](https://github.com/nodejs/node/pull/22309)
- [[`882c2c017a`](https://github.com/nodejs/node/commit/882c2c017a)] - **doc**: clarify http2 docs around class exports (James M Snell) [#22247](https://github.com/nodejs/node/pull/22247)
- [[`dd96ba5b89`](https://github.com/nodejs/node/commit/dd96ba5b89)] - **doc**: add multiple issue templates for GitHub (Tobias Nießen) [#22215](https://github.com/nodejs/node/pull/22215)
- [[`d95a22c304`](https://github.com/nodejs/node/commit/d95a22c304)] - **doc**: declare all parameter types (Sam Ruby) [#21782](https://github.com/nodejs/node/pull/21782)
- [[`9e25028981`](https://github.com/nodejs/node/commit/9e25028981)] - **doc**: add missing option for child_process.spawnSync() (James Bromwell) [#22231](https://github.com/nodejs/node/pull/22231)
- [[`ef8d0fc490`](https://github.com/nodejs/node/commit/ef8d0fc490)] - **doc**: list encodings supported by buffer.transcode (James M Snell) [#22263](https://github.com/nodejs/node/pull/22263)
- [[`1b41cd44b5`](https://github.com/nodejs/node/commit/1b41cd44b5)] - **doc**: discuss special protocol handling (James M Snell) [#22261](https://github.com/nodejs/node/pull/22261)
- [[`cea8d4f4e9`](https://github.com/nodejs/node/commit/cea8d4f4e9)] - **doc**: replace \_WG\_ with \_team\_ (Rich Trott) [#22183](https://github.com/nodejs/node/pull/22183)
- [[`fafdae4ce1`](https://github.com/nodejs/node/commit/fafdae4ce1)] - **doc**: add subprocess.ref() and subprocess.unref() (Thomas Hunter II) [#22220](https://github.com/nodejs/node/pull/22220)
- [[`d4f3615aaf`](https://github.com/nodejs/node/commit/d4f3615aaf)] - **doc**: add gdams to collaborators (George Adams) [#22236](https://github.com/nodejs/node/pull/22236)
- [[`e75885f2e6`](https://github.com/nodejs/node/commit/e75885f2e6)] - **doc**: specify `options` parameter type in zlib.md (Vse Mozhet Byt) [#21920](https://github.com/nodejs/node/pull/21920)
- [[`40af9767a2`](https://github.com/nodejs/node/commit/40af9767a2)] - **doc**: declare all parameter types (Sam Ruby) [#21782](https://github.com/nodejs/node/pull/21782)
- [[`38dd407c83`](https://github.com/nodejs/node/commit/38dd407c83)] - **doc**: remove unused error codes from errors.md (Сковорода Никита Андреевич) [#21491](https://github.com/nodejs/node/pull/21491)
- [[`6c7733f58a`](https://github.com/nodejs/node/commit/6c7733f58a)] - **doc**: update recommendations for createCipher (Tobias Nießen) [#22087](https://github.com/nodejs/node/pull/22087)
- [[`34300aaaa4`](https://github.com/nodejs/node/commit/34300aaaa4)] - **doc**: correct crypto.randomFill() and randomFillSync() (Gerhard Stoebich) [#21550](https://github.com/nodejs/node/pull/21550)
- [[`28870a46ac`](https://github.com/nodejs/node/commit/28870a46ac)] - **doc**: add rubys to collaborators (Sam Ruby) [#22109](https://github.com/nodejs/node/pull/22109)
- [[`d2ad9a2c13`](https://github.com/nodejs/node/commit/d2ad9a2c13)] - **doc**: fix return type of server.address() (Weijia Wang) [#22043](https://github.com/nodejs/node/pull/22043)
- [[`168abb5801`](https://github.com/nodejs/node/commit/168abb5801)] - **doc**: rename stackStartFunction in assert.md (Eugene Y. Q. Shen) [#22077](https://github.com/nodejs/node/pull/22077)
- [[`d364f9c8e7`](https://github.com/nodejs/node/commit/d364f9c8e7)] - **doc**: fix changelog for v10.8.0 (Michaël Zasso) [#22072](https://github.com/nodejs/node/pull/22072)
- [[`abac0c56b8`](https://github.com/nodejs/node/commit/abac0c56b8)] - **doc**: mark DEP0004 and DEP0042 as End-of-Life (Jon Moss) [#22033](https://github.com/nodejs/node/pull/22033)
- [[`c6a56ae23e`](https://github.com/nodejs/node/commit/c6a56ae23e)] - **doc**: correct grammatical error in BUILDING.md (Brandon Lee) [#22067](https://github.com/nodejs/node/pull/22067)
- [[`29bc55320c`](https://github.com/nodejs/node/commit/29bc55320c)] - **doc**: fixup process.binding deprecation code (James M Snell) [#22062](https://github.com/nodejs/node/pull/22062)
- [[`ec9d529a32`](https://github.com/nodejs/node/commit/ec9d529a32)] - **doc**: documentation deprecation of process.binding (James M Snell) [#22004](https://github.com/nodejs/node/pull/22004)
- [[`37369eba38`](https://github.com/nodejs/node/commit/37369eba38)] - **(SEMVER-MINOR)** **http**: allow url and options to be passed to http\*.request and http\*.get (Sam Ruby) [#21616](https://github.com/nodejs/node/pull/21616)
- [[`1ca46ab6f4`](https://github.com/nodejs/node/commit/1ca46ab6f4)] - **http,tls**: name anonymous callbacks (Marco Levrero) [#21412](https://github.com/nodejs/node/pull/21412)
- [[`8d226c6a79`](https://github.com/nodejs/node/commit/8d226c6a79)] - **http2**: correcting the heading format (Anto Aravinth) [#22262](https://github.com/nodejs/node/pull/22262)
- [[`7223a91a50`](https://github.com/nodejs/node/commit/7223a91a50)] - **http2**: explicitly disallow nested push streams (James M Snell) [#22245](https://github.com/nodejs/node/pull/22245)
- [[`cee78bf7a2`](https://github.com/nodejs/node/commit/cee78bf7a2)] - **http2**: avoid race condition in OnHeaderCallback (James M Snell) [#22256](https://github.com/nodejs/node/pull/22256)
- [[`fcca2f7e49`](https://github.com/nodejs/node/commit/fcca2f7e49)] - **http2**: remove `streamError` from docs (James M Snell) [#22246](https://github.com/nodejs/node/pull/22246)
- [[`2bf9a4a09e`](https://github.com/nodejs/node/commit/2bf9a4a09e)] - **https**: allow url and options to be passed to https.request (Sam Ruby) [#22003](https://github.com/nodejs/node/pull/22003)
- [[`4c5dc6e012`](https://github.com/nodejs/node/commit/4c5dc6e012)] - **inspector**: tie objects lifetime to the thread they belong to (Eugene Ostroukhov) [#22242](https://github.com/nodejs/node/pull/22242)
- [[`1afcea107e`](https://github.com/nodejs/node/commit/1afcea107e)] - **inspector**: unmark tests as flaky (Eugene Ostroukhov) [#22253](https://github.com/nodejs/node/pull/22253)
- [[`39898695b6`](https://github.com/nodejs/node/commit/39898695b6)] - **inspector**: add inspector_protocol as a direct dependency (Andrey Lushnikov) [#21975](https://github.com/nodejs/node/pull/21975)
- [[`311ec12702`](https://github.com/nodejs/node/commit/311ec12702)] - **inspector**: fixed V8InspectorClient::currentTimeMS (Aleksey Kozyatinskiy) [#21917](https://github.com/nodejs/node/pull/21917)
- [[`8f7e37337f`](https://github.com/nodejs/node/commit/8f7e37337f)] - **lib**: remove unused filterInternalStackFrames param (MaleDong) [#22267](https://github.com/nodejs/node/pull/22267)
- [[`3f729aac20`](https://github.com/nodejs/node/commit/3f729aac20)] - **lib**: extract validateString validator (Jon Moss) [#22101](https://github.com/nodejs/node/pull/22101)
- [[`f570c19c89`](https://github.com/nodejs/node/commit/f570c19c89)] - **perf_hooks**: avoid memory leak on gc observer (James M Snell) [#22241](https://github.com/nodejs/node/pull/22241)
- [[`76a65921d3`](https://github.com/nodejs/node/commit/76a65921d3)] - **readline,zlib**: named anonymous functions (Anto Aravinth) [#21792](https://github.com/nodejs/node/pull/21792)
- [[`e4f346892c`](https://github.com/nodejs/node/commit/e4f346892c)] - **repl**: support mult-line string-keyed objects (Sam Ruby) [#21805](https://github.com/nodejs/node/pull/21805)
- [[`d0b0ea971a`](https://github.com/nodejs/node/commit/d0b0ea971a)] - **src**: remove unnecessary writes in tls_wrap.cc (Anna Henningsen) [#21984](https://github.com/nodejs/node/pull/21984)
- [[`b2ac7a750f`](https://github.com/nodejs/node/commit/b2ac7a750f)] - **src**: avoid possible race during NodeBIO initialization (Anna Henningsen) [#21984](https://github.com/nodejs/node/pull/21984)
- [[`d85b0a3c10`](https://github.com/nodejs/node/commit/d85b0a3c10)] - **src**: use smart pointers for NodeBIO (Anna Henningsen) [#21984](https://github.com/nodejs/node/pull/21984)
- [[`82e71dd8bd`](https://github.com/nodejs/node/commit/82e71dd8bd)] - **src**: fix integer overflow in GetNow (Anatoli Papirovski) [#22214](https://github.com/nodejs/node/pull/22214)
- [[`2737b46e16`](https://github.com/nodejs/node/commit/2737b46e16)] - **src**: add READONLY_STRING_PROPERTY and simplify config (Jon Moss) [#22222](https://github.com/nodejs/node/pull/22222)
- [[`8b5485dcf5`](https://github.com/nodejs/node/commit/8b5485dcf5)] - **src**: fix up doc comment for experimental-worker bool (Anna Henningsen) [#22165](https://github.com/nodejs/node/pull/22165)
- [[`e90e56f4ca`](https://github.com/nodejs/node/commit/e90e56f4ca)] - **src**: remove calls to deprecated v8 functions (NumberValue) (Ujjwal Sharma) [#22094](https://github.com/nodejs/node/pull/22094)
- [[`c09872b749`](https://github.com/nodejs/node/commit/c09872b749)] - **src**: remove unused env-\>vm_parsing_context_symbol (Jon Moss) [#22034](https://github.com/nodejs/node/pull/22034)
- [[`6ca00d7044`](https://github.com/nodejs/node/commit/6ca00d7044)] - **src**: remove unused env strings (Jon Moss) [#22137](https://github.com/nodejs/node/pull/22137)
- [[`0ca831a0ed`](https://github.com/nodejs/node/commit/0ca831a0ed)] - **src**: clean up PackageConfig pseudo-boolean fields (Anna Henningsen) [#21987](https://github.com/nodejs/node/pull/21987)
- [[`00c33a5131`](https://github.com/nodejs/node/commit/00c33a5131)] - **src**: clean up agent loop when exiting through destructor (Anna Henningsen) [#21867](https://github.com/nodejs/node/pull/21867)
- [[`ba480d33ce`](https://github.com/nodejs/node/commit/ba480d33ce)] - **src**: use only one tracing write fs req at a time (Anna Henningsen) [#21867](https://github.com/nodejs/node/pull/21867)
- [[`6b58746b2e`](https://github.com/nodejs/node/commit/6b58746b2e)] - **src**: use unique_ptr for internal JSON trace writer (Anna Henningsen) [#21867](https://github.com/nodejs/node/pull/21867)
- [[`ce48936077`](https://github.com/nodejs/node/commit/ce48936077)] - **src**: plug trace file file descriptor leak (Anna Henningsen) [#21867](https://github.com/nodejs/node/pull/21867)
- [[`89e23021fb`](https://github.com/nodejs/node/commit/89e23021fb)] - **src**: initialize file trace writer on tracing thread (Anna Henningsen) [#21867](https://github.com/nodejs/node/pull/21867)
- [[`56edd5fc5b`](https://github.com/nodejs/node/commit/56edd5fc5b)] - **src**: close tracing event loop (Anna Henningsen) [#21867](https://github.com/nodejs/node/pull/21867)
- [[`4c9c1bbc45`](https://github.com/nodejs/node/commit/4c9c1bbc45)] - **src**: fix tracing if cwd or file path is inaccessible (Anna Henningsen) [#21867](https://github.com/nodejs/node/pull/21867)
- [[`c101b396aa`](https://github.com/nodejs/node/commit/c101b396aa)] - **src**: refactor default trace writer out of agent (Anna Henningsen) [#21867](https://github.com/nodejs/node/pull/21867)
- [[`daafe6c195`](https://github.com/nodejs/node/commit/daafe6c195)] - **src**: refactor tracing agent code (Anna Henningsen) [#21867](https://github.com/nodejs/node/pull/21867)
- [[`4379140dbf`](https://github.com/nodejs/node/commit/4379140dbf)] - **src**: minor refactor of node_trace_events.cc (Anna Henningsen) [#21867](https://github.com/nodejs/node/pull/21867)
- [[`cde0e5f396`](https://github.com/nodejs/node/commit/cde0e5f396)] - **src**: reduce unnecessary includes (Anna Henningsen) [#21867](https://github.com/nodejs/node/pull/21867)
- [[`31e3e6f1f8`](https://github.com/nodejs/node/commit/31e3e6f1f8)] - **stream**: fix readable behavior for highWaterMark === 0 (Denys Otrishko) [#21690](https://github.com/nodejs/node/pull/21690)
- [[`9d89b3c7ec`](https://github.com/nodejs/node/commit/9d89b3c7ec)] - **test**: rename some allegories (Vse Mozhet Byt) [#22307](https://github.com/nodejs/node/pull/22307)
- [[`1d15f33277`](https://github.com/nodejs/node/commit/1d15f33277)] - **test**: call gc() explicitly to avoid OOM (Refael Ackermann) [#22301](https://github.com/nodejs/node/pull/22301)
- [[`a7dad4565b`](https://github.com/nodejs/node/commit/a7dad4565b)] - **test**: move test-http-client-timeout-option-with-agent to sequential (Ouyang Yadong) [#22083](https://github.com/nodejs/node/pull/22083)
- [[`a414b0757a`](https://github.com/nodejs/node/commit/a414b0757a)] - **test**: add test-http2-large-file sequential test (James M Snell) [#22254](https://github.com/nodejs/node/pull/22254)
- [[`01fe2cee5b`](https://github.com/nodejs/node/commit/01fe2cee5b)] - **test**: fix error messages for OpenSSL-1.1.0i (Shigeki Ohtsu) [#22318](https://github.com/nodejs/node/pull/22318)
- [[`c145690aad`](https://github.com/nodejs/node/commit/c145690aad)] - **test**: improve test coverage for comparisons (Ruben Bridgewater) [#22212](https://github.com/nodejs/node/pull/22212)
- [[`bdc644f2ec`](https://github.com/nodejs/node/commit/bdc644f2ec)] - **test**: remove common.fileExists() (Rich Trott) [#22151](https://github.com/nodejs/node/pull/22151)
- [[`bc1cb7b7fc`](https://github.com/nodejs/node/commit/bc1cb7b7fc)] - **test**: handle errors correctly in GC http test (Ouyang Yadong) [#22185](https://github.com/nodejs/node/pull/22185)
- [[`cefc4a03cc`](https://github.com/nodejs/node/commit/cefc4a03cc)] - **test**: remove second arg from assert.ifError() (Musa Hamwala) [#22190](https://github.com/nodejs/node/pull/22190)
- [[`b1cbbbc7af`](https://github.com/nodejs/node/commit/b1cbbbc7af)] - **test**: move require of https to after crypto check (Daniel Bevenius) [#22148](https://github.com/nodejs/node/pull/22148)
- [[`a6ab19a96a`](https://github.com/nodejs/node/commit/a6ab19a96a)] - **test**: move require of http2 to after crypto check (Daniel Bevenius) [#22148](https://github.com/nodejs/node/pull/22148)
- [[`7a4c7e6c82`](https://github.com/nodejs/node/commit/7a4c7e6c82)] - **test**: don't mask descriptor.enumerable (Thomas Leah) [#22172](https://github.com/nodejs/node/pull/22172)
- [[`5018661a85`](https://github.com/nodejs/node/commit/5018661a85)] - **test**: remove common.fileExists() (Richard Lau) [#22200](https://github.com/nodejs/node/pull/22200)
- [[`77ce40fa03`](https://github.com/nodejs/node/commit/77ce40fa03)] - **test**: remove unused argument in assertion (yahavfuchs) [#22113](https://github.com/nodejs/node/pull/22113)
- [[`ca0fb3acce`](https://github.com/nodejs/node/commit/ca0fb3acce)] - **test**: check arg type for dnsPromises.resolve (Masashi Hirano) [#22000](https://github.com/nodejs/node/pull/22000)
- [[`6daa4f8797`](https://github.com/nodejs/node/commit/6daa4f8797)] - **test**: update postmortem metadata test (cjihrig) [#21079](https://github.com/nodejs/node/pull/21079)
- [[`16a929b867`](https://github.com/nodejs/node/commit/16a929b867)] - **test**: fix scriptParsed event expectations (Ingvar Stepanyan) [#21079](https://github.com/nodejs/node/pull/21079)
- [[`e58c17b849`](https://github.com/nodejs/node/commit/e58c17b849)] - **test**: update certificates and private keys (Fedor Indutny) [#22184](https://github.com/nodejs/node/pull/22184)
- [[`d38ccaa421`](https://github.com/nodejs/node/commit/d38ccaa421)] - **test**: fix n-api addon build warnings (Kyle Farnung) [#21808](https://github.com/nodejs/node/pull/21808)
- [[`d66e52fb8e`](https://github.com/nodejs/node/commit/d66e52fb8e)] - **test**: run ESM tests in parallel (Michaël Zasso) [#21919](https://github.com/nodejs/node/pull/21919)
- [[`6cff57e98d`](https://github.com/nodejs/node/commit/6cff57e98d)] - **test**: fix incorrect file mode check (Timothy Gu) [#22023](https://github.com/nodejs/node/pull/22023)
- [[`dafaff3a5e`](https://github.com/nodejs/node/commit/dafaff3a5e)] - **test**: remove unused config (Benjamin Gruenbaum) [#21985](https://github.com/nodejs/node/pull/21985)
- [[`a569ae4b44`](https://github.com/nodejs/node/commit/a569ae4b44)] - **test**: remove third argument from assert.strictEqual() (Rishabh Singh) [#22051](https://github.com/nodejs/node/pull/22051)
- [[`a60060b499`](https://github.com/nodejs/node/commit/a60060b499)] - **test**: remove third argument from call to assert.strictEqual() (Michael Sommer) [#22047](https://github.com/nodejs/node/pull/22047)
- [[`246a94f301`](https://github.com/nodejs/node/commit/246a94f301)] - **test**: see value of "hadError" in tls test (Oryan Moshe) [#22069](https://github.com/nodejs/node/pull/22069)
- [[`a40ee213b3`](https://github.com/nodejs/node/commit/a40ee213b3)] - **test**: improve reliability in http2-session-timeout (Rich Trott) [#22026](https://github.com/nodejs/node/pull/22026)
- [[`e2d97eeb65`](https://github.com/nodejs/node/commit/e2d97eeb65)] - **test**: remove outdated documentation (Jon Moss) [#22009](https://github.com/nodejs/node/pull/22009)
- [[`94746d6a47`](https://github.com/nodejs/node/commit/94746d6a47)] - **test**: remove outdated, non-functioning test (Anatoli Papirovski) [#20894](https://github.com/nodejs/node/pull/20894)
- [[`0beffc0f3b`](https://github.com/nodejs/node/commit/0beffc0f3b)] - **test**: remove test/gc, integrate into parallel (Anna Henningsen) [#22001](https://github.com/nodejs/node/pull/22001)
- [[`c2372eac16`](https://github.com/nodejs/node/commit/c2372eac16)] - **test**: add tracing crash regression test (Eugene Ostroukhov) [#21867](https://github.com/nodejs/node/pull/21867)
- [[`7e23080d45`](https://github.com/nodejs/node/commit/7e23080d45)] - **test**: pass through stderr in benchmark tests (Anna Henningsen) [#21860](https://github.com/nodejs/node/pull/21860)
- [[`52020dc09a`](https://github.com/nodejs/node/commit/52020dc09a)] - **test**: refactor test-http2-compat-serverresponse-finished.js (Anto Aravinth) [#21929](https://github.com/nodejs/node/pull/21929)
- [[`88665b3cef`](https://github.com/nodejs/node/commit/88665b3cef)] - **test,doc**: fix async-hooks coverage doc for md lint (Rod Vagg) [#22296](https://github.com/nodejs/node/pull/22296)
- [[`d60b017135`](https://github.com/nodejs/node/commit/d60b017135)] - **test,doc**: adjust markdown table for linting (Rich Trott) [#22221](https://github.com/nodejs/node/pull/22221)
- [[`8f56cc0321`](https://github.com/nodejs/node/commit/8f56cc0321)] - **test,doc**: adjust async-hooks coverage doc for lint (Rich Trott) [#22221](https://github.com/nodejs/node/pull/22221)
- [[`5c41caa1cc`](https://github.com/nodejs/node/commit/5c41caa1cc)] - **test,doc**: wrap common module md doc at 80 chars (Rich Trott) [#22221](https://github.com/nodejs/node/pull/22221)
- [[`21883be05d`](https://github.com/nodejs/node/commit/21883be05d)] - **test,doc**: fix lint error in test fixtures (Rich Trott) [#22221](https://github.com/nodejs/node/pull/22221)
- [[`ec2209dc8b`](https://github.com/nodejs/node/commit/ec2209dc8b)] - **tls**: change var to const (Eugen Cazacu) [#22219](https://github.com/nodejs/node/pull/22219)
- [[`2d1c1853e9`](https://github.com/nodejs/node/commit/2d1c1853e9)] - **tls**: remove SLAB_BUFFER_SIZE (Anatoli Papirovski) [#21199](https://github.com/nodejs/node/pull/21199)
- [[`f989681e34`](https://github.com/nodejs/node/commit/f989681e34)] - **tls**: preallocate SSL cipher array (Tobias Nießen) [#22136](https://github.com/nodejs/node/pull/22136)
- [[`6cd2d1dddc`](https://github.com/nodejs/node/commit/6cd2d1dddc)] - **tools**: fix header escaping regression (Sam Ruby) [#22084](https://github.com/nodejs/node/pull/22084)
- [[`80dd0445c6`](https://github.com/nodejs/node/commit/80dd0445c6)] - **tools**: add no-misleading-character-class ESLint rule (Vse Mozhet Byt) [#22278](https://github.com/nodejs/node/pull/22278)
- [[`bc35f17b7b`](https://github.com/nodejs/node/commit/bc35f17b7b)] - **tools**: do not autolink section to itself (Vse Mozhet Byt) [#22138](https://github.com/nodejs/node/pull/22138)
- [[`950a4a9b91`](https://github.com/nodejs/node/commit/950a4a9b91)] - **tools**: update ESLint to 5.3.0 (Rich Trott) [#22134](https://github.com/nodejs/node/pull/22134)
- [[`0c67d326dc`](https://github.com/nodejs/node/commit/0c67d326dc)] - **tools**: convert addon-verify to remark (Sam Ruby) [#21978](https://github.com/nodejs/node/pull/21978)
- [[`c85d00b786`](https://github.com/nodejs/node/commit/c85d00b786)] - **tools**: produce JSON documentation using unified/remark/rehype (Sam Ruby) [#21697](https://github.com/nodejs/node/pull/21697)
- [[`f0c871b0c7`](https://github.com/nodejs/node/commit/f0c871b0c7)] - **tools**: add `make format-cpp` to run clang-format on C++ diffs (Joyee Cheung) [#21997](https://github.com/nodejs/node/pull/21997)
- [[`5a4abbadfe`](https://github.com/nodejs/node/commit/5a4abbadfe)] - **tools**: update to using dmn 1.0.11 (Rich Trott) [#22035](https://github.com/nodejs/node/pull/22035)
- [[`7a7c194f4e`](https://github.com/nodejs/node/commit/7a7c194f4e)] - **tools**: fix docs and run known_issues by default (Jon Moss) [#21910](https://github.com/nodejs/node/pull/21910)
- [[`4995b28a11`](https://github.com/nodejs/node/commit/4995b28a11)] - **tools,build**: apply markdown linting to test dir (Rich Trott) [#22221](https://github.com/nodejs/node/pull/22221)
- [[`ad46cca104`](https://github.com/nodejs/node/commit/ad46cca104)] - **trace_events**: add node.promises category, rejection counter (James M Snell) [#22124](https://github.com/nodejs/node/pull/22124)
- [[`b171fa2530`](https://github.com/nodejs/node/commit/b171fa2530)] - **util**: improve display of iterators and weak entries (Ruben Bridgewater) [#20961](https://github.com/nodejs/node/pull/20961)
- [[`f1c22eaa56`](https://github.com/nodejs/node/commit/f1c22eaa56)] - **util,assert**: fix boxed primitives bug (Ruben Bridgewater) [#22243](https://github.com/nodejs/node/pull/22243)
- [[`677d10cdd1`](https://github.com/nodejs/node/commit/677d10cdd1)] - **worker**: fix deadlock when calling terminate from exit handler (Anna Henningsen) [#22073](https://github.com/nodejs/node/pull/22073)
- [[`4b0d2de5f4`](https://github.com/nodejs/node/commit/4b0d2de5f4)] - **zlib**: remove unused parameters (MaleDong) [#22115](https://github.com/nodejs/node/pull/22115)

Windows 32-bit Installer: https://nodejs.org/dist/v10.9.0/node-v10.9.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v10.9.0/node-v10.9.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v10.9.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v10.9.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v10.9.0/node-v10.9.0.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v10.9.0/node-v10.9.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v10.9.0/node-v10.9.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v10.9.0/node-v10.9.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v10.9.0/node-v10.9.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v10.9.0/node-v10.9.0-aix-ppc64.tar.gz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v10.9.0/node-v10.9.0-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v10.9.0/node-v10.9.0-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v10.9.0/node-v10.9.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v10.9.0/node-v10.9.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v10.9.0/node-v10.9.0.tar.gz \
Other release files: https://nodejs.org/dist/v10.9.0/ \
Documentation: https://nodejs.org/docs/v10.9.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

238afcb7b047691db3883b01a97f1ec458824d6d7d8412840319aeb82f3a3d5c  node-v10.9.0-aix-ppc64.tar.gz
3c4fe75dacfcc495a432a7ba2dec9045cff359af2a5d7d0429c84a424ef686fc  node-v10.9.0-darwin-x64.tar.gz
05efb0916b5c633df29d20c6e4b6e113d5ab550e7d796da77a12d4d25ef661d1  node-v10.9.0-darwin-x64.tar.xz
aa18780a04e427b537da8f4f731ea05a8cecdd22bdf2dcd0a7f384a8e0e35874  node-v10.9.0-headers.tar.gz
a8b1b1c7b597a8aa27d8d5210788033c62bc9cc8a76c7f8a52ab5bf674e01923  node-v10.9.0-headers.tar.xz
de3f9625fd15acefce6123e7ac7e51f26b965315f0f64f00aef359d68677ec82  node-v10.9.0-linux-arm64.tar.gz
3d6aa717d98bbf901c5325a02a81048eb47212bdc44963ef8d59c6e52e987b7c  node-v10.9.0-linux-arm64.tar.xz
a404f0c10150cdfa2b43d3385591c20a85432a9cdd5f97a1ce96176e268f4230  node-v10.9.0-linux-armv6l.tar.gz
558b59937cdc341c2c44b722223dc17e29d4a5c710e476648bce452f6e876777  node-v10.9.0-linux-armv6l.tar.xz
ffa57690d5a563e13ae87f6f73b4ccd91b7ba018fabf3a015076d4d44cb85840  node-v10.9.0-linux-armv7l.tar.gz
bd7fd62a1bd371d9079431581e8410cbfaa0359ccd55be6d18ae09020a341fde  node-v10.9.0-linux-armv7l.tar.xz
7a61df1c5258fc91453fd38bb9f692b0761bcb57ebf3343b25a8b8f27b00ed96  node-v10.9.0-linux-ppc64le.tar.gz
552586b4f238812c9f1b79241a685fd1de47a07e125153d87d85f12073509722  node-v10.9.0-linux-ppc64le.tar.xz
c9e211d0db4c88760251e72bd93da4a9d37be0fe54b270183aabc0b0c5a9135d  node-v10.9.0-linux-s390x.tar.gz
844f20a6f9acfe3a0c5d8df74871d13481dc6001b1cdc06bd1e4a0fa478eac55  node-v10.9.0-linux-s390x.tar.xz
d061760884e4705adfc858eb669c44eb66cd57e8cdf6d5d57a190e76723af416  node-v10.9.0-linux-x64.tar.gz
c5acb8b7055ee0b6ac653dc4e458c5db45348cecc564b388f4ed1def84a329ff  node-v10.9.0-linux-x64.tar.xz
b8c4edc1ee389ffc96833165e9d7249d8b2d48db11ae9ec91d5787953b6d11da  node-v10.9.0.pkg
f19ccc132c22e9facc1cd71c3f573aed650f31394efb37901f55a3be39e2801e  node-v10.9.0-sunos-x64.tar.gz
bd2d955047884d1d858182966ca3024e29e2afeca6a5ee8759fd4e27b1f05f22  node-v10.9.0-sunos-x64.tar.xz
1ed31ba75080b16193f1c7a351807481b1f7da08bfaf7172bc07f0cf07e6ea71  node-v10.9.0.tar.gz
d17ef8eb72d6a31f50a663d554beb9bcb55aa2ce57cf189abfc9b1ba20530d02  node-v10.9.0.tar.xz
befc6962861ea4248e460120dcbfcc16fb2bf19b3d7699fa4ba72726c8cb2011  node-v10.9.0-win-x64.7z
6a75cdbb69d62ed242d6cbf0238a470bcbf628567ee339d4d098a5efcda2401e  node-v10.9.0-win-x64.zip
9437af7a5d014ff9b2cf93044db528a9abd65b37dd4ea33a1433fee1ddef0729  node-v10.9.0-win-x86.7z
74b7143508b7822ac131dfac03fb6c72cabe80344410a754240cc22b7a1b84db  node-v10.9.0-win-x86.zip
67b7daca5e4c2b25b18024230af5ca205717317ca1a561f4f087c2da2e4d77f3  node-v10.9.0-x64.msi
bbbf1b69ac53b5a4a631f4e78b5e72f50d54c3d5dc95cba9ae856514a7703f37  node-v10.9.0-x86.msi
f86871e83202e43b61ceb25a6c3c8f31c72bac5c536fc4a22ef3c444176789dd  win-x64/node.exe
b0ca775f54aac5b6ab7685c1385449c70c9171f23155d1b351554d56360eff1c  win-x64/node.lib
54a28f70d61daccc8737809363c238d833da3e485bff6355296cec7bc40c056b  win-x64/node_pdb.7z
81d80db1158345d371ae60b8e6a9077d221eb98d4d1b30804fc9c6f3df9b157c  win-x64/node_pdb.zip
e627f2caea2ee31caf03476a3b71f90bd276f035ba0acfc08ec0ef57f1cd4322  win-x86/node.exe
5e10b3c9b2ed2b904bd7b8f4f1041421a72a8a400d12561b604ee3ff43d54338  win-x86/node.lib
5942839ccfc72350e7c520f826d84ba0d379c04b34558ab8c8fec6dc60f87d7c  win-x86/node_pdb.7z
0dbe8f4ae98e9ef41452d7eadd24f8a11a0833086fd1ee73a2230978b368ff8d  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQEzBAEBCAAdFiEE3Y8jOLrnUB491ax4wnN5L32DVF0FAlt01wsACgkQwnN5L32D
VF3M2gf/Q4qZrUC6RbXkv50XsSffY3E9TA3WxdRTSEYB30A0KdTNfhRQjuDzRrC1
NQsxhUw4xDTQ+oeV1e7exOj5G7mDBvvtytkB8UVnafvAiIeXml4kUlgVd+mvJO/j
V8a3WEXd5rKcg9dgsfRfxZ7NpflwbY971WeQu6SpR7uTd7Oc/oPwmWxa77pGohDx
K0H/wlB1OopfJ95ivZ22MIwgTsg6/mMldrKX1phkgacGJgKp5nsbTLa/tsje6mG1
oNCprH1AETrfViTYGkJ3jXW1trtqWRD35Uhb2+u+Km1nKgUqf5nc1ghIR0xjiO6g
njtBOD7Aud2RRjstEyuwZyhaEiVNHw==
=6S4w
-----END PGP SIGNATURE-----

```
