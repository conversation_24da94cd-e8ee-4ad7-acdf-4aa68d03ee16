---
date: '2017-11-14T23:09:41.422Z'
category: release
title: Node v9.2.0 (Current)
layout: blog-post
author: <PERSON>
---

### Notable Changes

- **crypto**:
  - Support building with both 1.1.0 and 1.0.2 (<PERSON>) [#16130](https://github.com/nodejs/node/pull/16130)
- **fs**:
  - fs.realpathSync.native and fs.realpath.native are now exposed (<PERSON>) [#15776](https://github.com/nodejs/node/pull/15776)
- **process**:
  - expose process.ppid (cjihrig) [#16839](https://github.com/nodejs/node/pull/16839)

### Commits

- [[`02ea0ee507`](https://github.com/nodejs/node/commit/02ea0ee507)] - **build**: fix cctest compilation (<PERSON>) [#16887](https://github.com/nodejs/node/pull/16887)
- [[`a4557f294a`](https://github.com/nodejs/node/commit/a4557f294a)] - **build**: remove cctest extension (<PERSON><PERSON>) [#16680](https://github.com/nodejs/node/pull/16680)
- [[`1dc4fc1390`](https://github.com/nodejs/node/commit/1dc4fc1390)] - **build**: include src\tracing when linting on win (Daniel Bevenius) [#16720](https://github.com/nodejs/node/pull/16720)
- [[`4c11801ed7`](https://github.com/nodejs/node/commit/4c11801ed7)] - **build**: add missing options to help message (Daniel Bevenius) [#16707](https://github.com/nodejs/node/pull/16707)
- [[`bed0560fb5`](https://github.com/nodejs/node/commit/bed0560fb5)] - **console**: avoid adding infinite error listeners (Matteo Collina) [#16770](https://github.com/nodejs/node/pull/16770)
- [[`31dadd2007`](https://github.com/nodejs/node/commit/31dadd2007)] - **(SEMVER-MINOR)** **crypto**: deprecate {ecdhCurve: false} (David Benjamin) [#16130](https://github.com/nodejs/node/pull/16130)
- [[`f952caa677`](https://github.com/nodejs/node/commit/f952caa677)] - **(SEMVER-MINOR)** **crypto**: clear some SSL_METHOD deprecation warnings (David Benjamin) [#16130](https://github.com/nodejs/node/pull/16130)
- [[`a5e7255385`](https://github.com/nodejs/node/commit/a5e7255385)] - **(SEMVER-MINOR)** **crypto**: make ALPN the same for OpenSSL 1.0.2 & 1.1.0 (David Benjamin) [#16130](https://github.com/nodejs/node/pull/16130)
- [[`07102ace9e`](https://github.com/nodejs/node/commit/07102ace9e)] - **(SEMVER-MINOR)** **crypto**: remove deprecated ECDH calls w/ OpenSSL 1.1 (David Benjamin) [#16130](https://github.com/nodejs/node/pull/16130)
- [[`627a15f9e5`](https://github.com/nodejs/node/commit/627a15f9e5)] - **(SEMVER-MINOR)** **crypto**: emulate OpenSSL 1.0 ticket scheme in 1.1 (David Benjamin) [#16130](https://github.com/nodejs/node/pull/16130)
- [[`8a8ac8ce4d`](https://github.com/nodejs/node/commit/8a8ac8ce4d)] - **(SEMVER-MINOR)** **crypto**: hard-code tlsSocket.getCipher().version (David Benjamin) [#16130](https://github.com/nodejs/node/pull/16130)
- [[`c42935b79c`](https://github.com/nodejs/node/commit/c42935b79c)] - **(SEMVER-MINOR)** **crypto**: add compat logic for "DSS1" and "dss1" (David Benjamin) [#16130](https://github.com/nodejs/node/pull/16130)
- [[`5c24fc32c9`](https://github.com/nodejs/node/commit/5c24fc32c9)] - **(SEMVER-MINOR)** **crypto**: Make Hmac 1.1.0-compatible (David Benjamin) [#16130](https://github.com/nodejs/node/pull/16130)
- [[`fa1fc16c3e`](https://github.com/nodejs/node/commit/fa1fc16c3e)] - **(SEMVER-MINOR)** **crypto**: make SignBase compatible with OpenSSL 1.1.0 (David Benjamin) [#16130](https://github.com/nodejs/node/pull/16130)
- [[`abe3dc48cc`](https://github.com/nodejs/node/commit/abe3dc48cc)] - **(SEMVER-MINOR)** **crypto**: make Hash 1.1.0-compatible (David Benjamin) [#16130](https://github.com/nodejs/node/pull/16130)
- [[`59acd27409`](https://github.com/nodejs/node/commit/59acd27409)] - **(SEMVER-MINOR)** **crypto**: make CipherBase 1.1.0-compatible (David Benjamin) [#16130](https://github.com/nodejs/node/pull/16130)
- [[`6c3ae36cab`](https://github.com/nodejs/node/commit/6c3ae36cab)] - **(SEMVER-MINOR)** **crypto**: remove locking callbacks for OpenSSL 1.1.0 (David Benjamin) [#16130](https://github.com/nodejs/node/pull/16130)
- [[`81760ffea9`](https://github.com/nodejs/node/commit/81760ffea9)] - **(SEMVER-MINOR)** **crypto**: use RSA and DH accessors (David Benjamin) [#16130](https://github.com/nodejs/node/pull/16130)
- [[`568d9d0eac`](https://github.com/nodejs/node/commit/568d9d0eac)] - **(SEMVER-MINOR)** **crypto**: test DH keys work without a public half (David Benjamin) [#16130](https://github.com/nodejs/node/pull/16130)
- [[`6a9c528a50`](https://github.com/nodejs/node/commit/6a9c528a50)] - **(SEMVER-MINOR)** **crypto**: account for new 1.1.0 SSL APIs (David Benjamin) [#16130](https://github.com/nodejs/node/pull/16130)
- [[`cc744b9b26`](https://github.com/nodejs/node/commit/cc744b9b26)] - **(SEMVER-MINOR)** **crypto**: remove unnecessary SSLerr calls (David Benjamin) [#16130](https://github.com/nodejs/node/pull/16130)
- [[`201393f655`](https://github.com/nodejs/node/commit/201393f655)] - **(SEMVER-MINOR)** **crypto**: estimate kExternalSize (David Benjamin) [#16130](https://github.com/nodejs/node/pull/16130)
- [[`efd9bc36fa`](https://github.com/nodejs/node/commit/efd9bc36fa)] - **(SEMVER-MINOR)** **crypto**: make node_crypto_bio compat w/ OpenSSL 1.1 (David Benjamin) [#16130](https://github.com/nodejs/node/pull/16130)
- [[`8da4983cb4`](https://github.com/nodejs/node/commit/8da4983cb4)] - **(SEMVER-MINOR)** **crypto**: use X509_STORE_CTX_new (David Benjamin) [#16130](https://github.com/nodejs/node/pull/16130)
- [[`9c6f63bf3b`](https://github.com/nodejs/node/commit/9c6f63bf3b)] - **deps**: cherry-pick 3c8195d from V8 upstream (Franziska Hinkelmann) [#16897](https://github.com/nodejs/node/pull/16897)
- [[`6ddba2e08e`](https://github.com/nodejs/node/commit/6ddba2e08e)] - **deps**: patch V8 to 6.2.414.44 (Myles Borins) [#16848](https://github.com/nodejs/node/pull/16848)
- [[`f82d3e44c8`](https://github.com/nodejs/node/commit/f82d3e44c8)] - **deps**: upgrade libuv to 1.16.1 (cjihrig) [#16835](https://github.com/nodejs/node/pull/16835)
- [[`38ac50a084`](https://github.com/nodejs/node/commit/38ac50a084)] - **deps**: cherry-pick cc55747 from V8 upstream (Franziska Hinkelmann) [#16890](https://github.com/nodejs/node/pull/16890)
- [[`75405a1481`](https://github.com/nodejs/node/commit/75405a1481)] - **deps**: ICU 60 bump (Steven R. Loomis) [#16876](https://github.com/nodejs/node/pull/16876)
- [[`28b7bf062a`](https://github.com/nodejs/node/commit/28b7bf062a)] - **deps**: cherry-pick b8331cc030 from upstream V8 (Daniel Bevenius) [#16900](https://github.com/nodejs/node/pull/16900)
- [[`2266cafba5`](https://github.com/nodejs/node/commit/2266cafba5)] - **_Revert_** "**deps**: cherry-pick b8331cc030 from upstream V8" (Daniel Bevenius) [#16899](https://github.com/nodejs/node/pull/16899)
- [[`81f14bffff`](https://github.com/nodejs/node/commit/81f14bffff)] - **deps**: cherry-pick b8331cc030 from upstream V8 (Daniel Bevenius) [#16743](https://github.com/nodejs/node/pull/16743)
- [[`6922fda1b5`](https://github.com/nodejs/node/commit/6922fda1b5)] - **doc**: recommend node-core-utils for metadata (Rich Trott) [#16978](https://github.com/nodejs/node/pull/16978)
- [[`ccf1f6aa13`](https://github.com/nodejs/node/commit/ccf1f6aa13)] - **doc**: fix typo in http2 doc (Gus Caplan) [#16993](https://github.com/nodejs/node/pull/16993)
- [[`54768f5094`](https://github.com/nodejs/node/commit/54768f5094)] - **doc**: reorganize COLLABORATOR_GUIDE.md (Rich Trott) [#15710](https://github.com/nodejs/node/pull/15710)
- [[`c4e2343bfb`](https://github.com/nodejs/node/commit/c4e2343bfb)] - **doc**: drop support for VS2015 (Nikolai Vavilov) [#16868](https://github.com/nodejs/node/pull/16868)
- [[`74f33724a2`](https://github.com/nodejs/node/commit/74f33724a2)] - **doc**: clarify the prerequisites for building with VS2017 (Nikolai Vavilov) [#16903](https://github.com/nodejs/node/pull/16903)
- [[`1510fda1b0`](https://github.com/nodejs/node/commit/1510fda1b0)] - **doc**: outline commit message for breaking changes (Maton Anthony) [#16846](https://github.com/nodejs/node/pull/16846)
- [[`1fcd95e517`](https://github.com/nodejs/node/commit/1fcd95e517)] - **doc**: remove duplicate 'the' from http2 API doc (Vipin Menon) [#16924](https://github.com/nodejs/node/pull/16924)
- [[`b46714c023`](https://github.com/nodejs/node/commit/b46714c023)] - **doc**: fix typos in N-API (Swathi Kalahastri) [#16911](https://github.com/nodejs/node/pull/16911)
- [[`3ba52c1582`](https://github.com/nodejs/node/commit/3ba52c1582)] - **doc**: correct the spelling of omitting in dgram.md (Vidya Subramanyam) [#16910](https://github.com/nodejs/node/pull/16910)
- [[`e60eff6c01`](https://github.com/nodejs/node/commit/e60eff6c01)] - **doc**: fix a typo in the documentation (Mamatha J V) [#16909](https://github.com/nodejs/node/pull/16909)
- [[`6e9973e912`](https://github.com/nodejs/node/commit/6e9973e912)] - **doc**: improve documentation for the vm module (Franziska Hinkelmann) [#16867](https://github.com/nodejs/node/pull/16867)
- [[`15dcb96b28`](https://github.com/nodejs/node/commit/15dcb96b28)] - **doc**: fix a typo in n-api documentation (Vipin Menon) [#16879](https://github.com/nodejs/node/pull/16879)
- [[`928647c77c`](https://github.com/nodejs/node/commit/928647c77c)] - **doc**: fix typo in assert.md (Andres Kalle) [#16866](https://github.com/nodejs/node/pull/16866)
- [[`a184dbcb2c`](https://github.com/nodejs/node/commit/a184dbcb2c)] - **doc**: update subprocess.killed (cjihrig) [#16748](https://github.com/nodejs/node/pull/16748)
- [[`deff9f5527`](https://github.com/nodejs/node/commit/deff9f5527)] - **events**: remove emit micro-optimizations (Anatoli Papirovski) [#16869](https://github.com/nodejs/node/pull/16869)
- [[`8611e3b93b`](https://github.com/nodejs/node/commit/8611e3b93b)] - **(SEMVER-MINOR)** **fs**: expose realpath(3) bindings (Ben Noordhuis) [#15776](https://github.com/nodejs/node/pull/15776)
- [[`8dfd5a515a`](https://github.com/nodejs/node/commit/8dfd5a515a)] - **http2**: multiple smaller code cleanups (James M Snell) [#16764](https://github.com/nodejs/node/pull/16764)
- [[`8245e5a2d4`](https://github.com/nodejs/node/commit/8245e5a2d4)] - **http2**: simplify subsequent rstStream calls (Anatoli Papirovski) [#16753](https://github.com/nodejs/node/pull/16753)
- [[`afbdd017c1`](https://github.com/nodejs/node/commit/afbdd017c1)] - **lib**: replace string concatenation with template (Suryanarayana Murthy N) [#16933](https://github.com/nodejs/node/pull/16933)
- [[`6c0fd55488`](https://github.com/nodejs/node/commit/6c0fd55488)] - **lib**: guard inspector console using process var (Daniel Bevenius) [#15008](https://github.com/nodejs/node/pull/15008)
- [[`c1792544e8`](https://github.com/nodejs/node/commit/c1792544e8)] - **lib**: improve the usage of TypeError\[INVALID_ARG_TYPE\] (Weijia Wang) [#16401](https://github.com/nodejs/node/pull/16401)
- [[`44c3cc2bec`](https://github.com/nodejs/node/commit/44c3cc2bec)] - **lib**: change concatenated string to template (Pawan Jangid) [#16930](https://github.com/nodejs/node/pull/16930)
- [[`8eb32e1b35`](https://github.com/nodejs/node/commit/8eb32e1b35)] - **lib**: replace String concatenation with template (saiHemak) [#16922](https://github.com/nodejs/node/pull/16922)
- [[`678e738d70`](https://github.com/nodejs/node/commit/678e738d70)] - **lib**: change concatenated string to template (Nayana Das K) [#16925](https://github.com/nodejs/node/pull/16925)
- [[`df181745b8`](https://github.com/nodejs/node/commit/df181745b8)] - **lib**: replace string concatenation with template (Jayashree S Kumar) [#16921](https://github.com/nodejs/node/pull/16921)
- [[`a9358068db`](https://github.com/nodejs/node/commit/a9358068db)] - **lib**: replace string concatenation with template (Chandrakala) [#16920](https://github.com/nodejs/node/pull/16920)
- [[`16c622209a`](https://github.com/nodejs/node/commit/16c622209a)] - **lib**: replace string concatenation with template (subrahmanya chari p) [#16917](https://github.com/nodejs/node/pull/16917)
- [[`64a0c80773`](https://github.com/nodejs/node/commit/64a0c80773)] - **loader**: test search module (Cyril Lakech) [#16795](https://github.com/nodejs/node/pull/16795)
- [[`bfdaa28fdb`](https://github.com/nodejs/node/commit/bfdaa28fdb)] - **meta**: 32 bit linux is experimental (Refael Ackermann) [#16723](https://github.com/nodejs/node/pull/16723)
- [[`76e6422868`](https://github.com/nodejs/node/commit/76e6422868)] - **src**: fix compiler warning in process.ppid (cjihrig) [#16958](https://github.com/nodejs/node/pull/16958)
- [[`60a6caea76`](https://github.com/nodejs/node/commit/60a6caea76)] - **src**: turn inspector raw pointer into unique_ptr (Franziska Hinkelmann) [#16974](https://github.com/nodejs/node/pull/16974)
- [[`79648496ec`](https://github.com/nodejs/node/commit/79648496ec)] - **src**: explain implementation of vm module (Franziska Hinkelmann) [#16962](https://github.com/nodejs/node/pull/16962)
- [[`a79d86db21`](https://github.com/nodejs/node/commit/a79d86db21)] - **src**: use unrefed async for GC tracking (Anna Henningsen) [#16758](https://github.com/nodejs/node/pull/16758)
- [[`5df3dc1169`](https://github.com/nodejs/node/commit/5df3dc1169)] - **src**: make StreamBase prototype accessors robust (Joyee Cheung) [#16860](https://github.com/nodejs/node/pull/16860)
- [[`41937bedf9`](https://github.com/nodejs/node/commit/41937bedf9)] - **(SEMVER-MINOR)** **src**: add process.ppid (cjihrig) [#16839](https://github.com/nodejs/node/pull/16839)
- [[`0b93bbb419`](https://github.com/nodejs/node/commit/0b93bbb419)] - **src**: add openssl-system-ca-path configure option (Daniel Bevenius) [#16790](https://github.com/nodejs/node/pull/16790)
- [[`43c5726028`](https://github.com/nodejs/node/commit/43c5726028)] - **src**: fix UB in InternalModuleReadFile() (Ben Noordhuis) [#16871](https://github.com/nodejs/node/pull/16871)
- [[`bce5db2225`](https://github.com/nodejs/node/commit/bce5db2225)] - **src**: CHECK() for argument overflow in Spawn() (cjihrig) [#16761](https://github.com/nodejs/node/pull/16761)
- [[`120db20a1a`](https://github.com/nodejs/node/commit/120db20a1a)] - **test**: reuse existing PassThrough implementation (Tobias Nießen) [#16936](https://github.com/nodejs/node/pull/16936)
- [[`9f0b0fbd0e`](https://github.com/nodejs/node/commit/9f0b0fbd0e)] - **test**: use fixtures module for path resolve (sercan yersen) [#16842](https://github.com/nodejs/node/pull/16842)
- [[`d5f2601bc8`](https://github.com/nodejs/node/commit/d5f2601bc8)] - **test**: refactor comments in test-child-process-spawnsync-maxbuf (ChrBergert) [#16829](https://github.com/nodejs/node/pull/16829)
- [[`93af193821`](https://github.com/nodejs/node/commit/93af193821)] - **test**: refactor addons-napi/test_promise/test.js (ka3e) [#16814](https://github.com/nodejs/node/pull/16814)
- [[`ad02676816`](https://github.com/nodejs/node/commit/ad02676816)] - **test**: used fixturesDir from fixtures modules (Klemen Kogovsek) [#16813](https://github.com/nodejs/node/pull/16813)
- [[`809dc099ac`](https://github.com/nodejs/node/commit/809dc099ac)] - **test**: refactor fs.write() test (Patrick Heneise) [#16827](https://github.com/nodejs/node/pull/16827)
- [[`35fc317d8f`](https://github.com/nodejs/node/commit/35fc317d8f)] - **test**: add a test description (Grant Gasparyan) [#16833](https://github.com/nodejs/node/pull/16833)
- [[`83f9604adc`](https://github.com/nodejs/node/commit/83f9604adc)] - **test**: use ES6 classes instead of util.inherits (Tobias Nießen) [#16938](https://github.com/nodejs/node/pull/16938)
- [[`7c364a269c`](https://github.com/nodejs/node/commit/7c364a269c)] - **test**: use common/fixtures module in hash-seed test (Javier Blanco) [#16823](https://github.com/nodejs/node/pull/16823)
- [[`3136578871`](https://github.com/nodejs/node/commit/3136578871)] - **test**: make test-tls-external-accessor agnostic (Rich Trott) [#16272](https://github.com/nodejs/node/pull/16272)
- [[`0be7f8c48c`](https://github.com/nodejs/node/commit/0be7f8c48c)] - **test**: make test-require-json engine agnostic (Rich Trott) [#16272](https://github.com/nodejs/node/pull/16272)
- [[`835ca63595`](https://github.com/nodejs/node/commit/835ca63595)] - **test**: make test-repl engine agnostic (Rich Trott) [#16272](https://github.com/nodejs/node/pull/16272)
- [[`f8337cea8e`](https://github.com/nodejs/node/commit/f8337cea8e)] - **test**: make test-repl-syntax-error-stack agnostic (Rich Trott) [#16272](https://github.com/nodejs/node/pull/16272)
- [[`c81b086928`](https://github.com/nodejs/node/commit/c81b086928)] - **test**: make test-repl-harmony engine agnostic (Rich Trott) [#16272](https://github.com/nodejs/node/pull/16272)
- [[`591a6927ee`](https://github.com/nodejs/node/commit/591a6927ee)] - **test**: make test-querystring-escape engine agnostic (Rich Trott) [#16272](https://github.com/nodejs/node/pull/16272)
- [[`e2f564821e`](https://github.com/nodejs/node/commit/e2f564821e)] - **test**: make test-process-env-symbols agnostic (Rich Trott) [#16272](https://github.com/nodejs/node/pull/16272)
- [[`9bf887475e`](https://github.com/nodejs/node/commit/9bf887475e)] - **test**: make test-os-eol engine agnostic (Rich Trott) [#16272](https://github.com/nodejs/node/pull/16272)
- [[`79e183186c`](https://github.com/nodejs/node/commit/79e183186c)] - **test**: make error stack test engine agnostic (Rich Trott) [#16272](https://github.com/nodejs/node/pull/16272)
- [[`b5b23bd3e8`](https://github.com/nodejs/node/commit/b5b23bd3e8)] - **test**: make test-http-outgoing-proto agnostic (Rich Trott) [#16272](https://github.com/nodejs/node/pull/16272)
- [[`bd7822b8f6`](https://github.com/nodejs/node/commit/bd7822b8f6)] - **test**: make test-error-reporting engine agnostic (Rich Trott) [#16272](https://github.com/nodejs/node/pull/16272)
- [[`4604294647`](https://github.com/nodejs/node/commit/4604294647)] - **test**: make test-console engine agnostic (Rich Trott) [#16272](https://github.com/nodejs/node/pull/16272)
- [[`025eadfcd5`](https://github.com/nodejs/node/commit/025eadfcd5)] - **test**: make test-console-count engine agnostic (Rich Trott) [#16272](https://github.com/nodejs/node/pull/16272)
- [[`c74467f938`](https://github.com/nodejs/node/commit/c74467f938)] - **test**: make test-cli-syntax engine agnostic (Rich Trott) [#16272](https://github.com/nodejs/node/pull/16272)
- [[`2e2e8020e7`](https://github.com/nodejs/node/commit/2e2e8020e7)] - **test**: make test-buffer-slow engine agnostic (Rich Trott) [#16272](https://github.com/nodejs/node/pull/16272)
- [[`7a5378377f`](https://github.com/nodejs/node/commit/7a5378377f)] - **test**: improve template value for test message (Stephan Smith) [#16826](https://github.com/nodejs/node/pull/16826)
- [[`6ea8768141`](https://github.com/nodejs/node/commit/6ea8768141)] - **test**: unmark flaky test (Anna Henningsen) [#16758](https://github.com/nodejs/node/pull/16758)
- [[`651fee4c54`](https://github.com/nodejs/node/commit/651fee4c54)] - **test**: change concatenated string to template (Deepthi Sebastian) [#16929](https://github.com/nodejs/node/pull/16929)
- [[`1ea546c9ee`](https://github.com/nodejs/node/commit/1ea546c9ee)] - **test**: change concatenated string to template (Anawesha Khuntia) [#16912](https://github.com/nodejs/node/pull/16912)
- [[`385f65826a`](https://github.com/nodejs/node/commit/385f65826a)] - **test**: change string concatenation to template (Suryanarayana Murthy N) [#16919](https://github.com/nodejs/node/pull/16919)
- [[`61fbd857d7`](https://github.com/nodejs/node/commit/61fbd857d7)] - **test**: use template string for concatenation (Vipin Menon) [#16918](https://github.com/nodejs/node/pull/16918)
- [[`fbec5ec4d9`](https://github.com/nodejs/node/commit/fbec5ec4d9)] - **test**: replace string concatenation with template (Kabir Islam) [#16916](https://github.com/nodejs/node/pull/16916)
- [[`407eb6f93e`](https://github.com/nodejs/node/commit/407eb6f93e)] - **test**: enable mustCall() during child exit (Vipin Menon) [#16915](https://github.com/nodejs/node/pull/16915)
- [[`26e4c587eb`](https://github.com/nodejs/node/commit/26e4c587eb)] - **(SEMVER-MINOR)** **test**: fix flaky test-http2-create-client-connect (David Benjamin) [#16130](https://github.com/nodejs/node/pull/16130)
- [[`8c294203cf`](https://github.com/nodejs/node/commit/8c294203cf)] - **(SEMVER-MINOR)** **test**: fix test-https-agent-session-eviction for 1.1 (David Benjamin) [#16130](https://github.com/nodejs/node/pull/16130)
- [[`3d438f84b2`](https://github.com/nodejs/node/commit/3d438f84b2)] - **(SEMVER-MINOR)** **test**: configure certs in tests (David Benjamin) [#16130](https://github.com/nodejs/node/pull/16130)
- [[`08ac21423e`](https://github.com/nodejs/node/commit/08ac21423e)] - **(SEMVER-MINOR)** **test**: revise test-tls-econnreset for OpenSSL 1.1.0 (David Benjamin) [#16130](https://github.com/nodejs/node/pull/16130)
- [[`d95b608d98`](https://github.com/nodejs/node/commit/d95b608d98)] - **(SEMVER-MINOR)** **test**: test with a larger RSA key (David Benjamin) [#16130](https://github.com/nodejs/node/pull/16130)
- [[`85ffc2f960`](https://github.com/nodejs/node/commit/85ffc2f960)] - **(SEMVER-MINOR)** **test**: remove sha from test expectations (David Benjamin) [#16130](https://github.com/nodejs/node/pull/16130)
- [[`bec042183c`](https://github.com/nodejs/node/commit/bec042183c)] - **(SEMVER-MINOR)** **test**: update test expectations for OpenSSL 1.1.0 (David Benjamin) [#16130](https://github.com/nodejs/node/pull/16130)
- [[`b42013c31e`](https://github.com/nodejs/node/commit/b42013c31e)] - **test**: replace string concatenation with template (Sabari Lakshmi Krishnamoorthy) [#16914](https://github.com/nodejs/node/pull/16914)
- [[`fbc7451553`](https://github.com/nodejs/node/commit/fbc7451553)] - **test**: replace string concatenation with template (Tanvi Kini) [#16913](https://github.com/nodejs/node/pull/16913)
- [[`7f7dec8b08`](https://github.com/nodejs/node/commit/7f7dec8b08)] - **test**: cover vm.runInNewContext() (cjihrig) [#16906](https://github.com/nodejs/node/pull/16906)
- [[`8311561ed5`](https://github.com/nodejs/node/commit/8311561ed5)] - **test**: improve assertion messages (Neil Vass) [#16885](https://github.com/nodejs/node/pull/16885)
- [[`1ee6df9612`](https://github.com/nodejs/node/commit/1ee6df9612)] - **test**: pass process.env to child processes (Rod Vagg) [#16405](https://github.com/nodejs/node/pull/16405)
- [[`172652ba27`](https://github.com/nodejs/node/commit/172652ba27)] - **test**: improve assert messages in stream test (Katie Stockton Roberts) [#16884](https://github.com/nodejs/node/pull/16884)
- [[`271c89e569`](https://github.com/nodejs/node/commit/271c89e569)] - **test**: improve assertion in test-require-dot (Adam Wegrzynek) [#16805](https://github.com/nodejs/node/pull/16805)
- [[`5d3a4ad1cf`](https://github.com/nodejs/node/commit/5d3a4ad1cf)] - **test**: improve error message reporting in testNapiRun.js (Paul Ashfield) [#16821](https://github.com/nodejs/node/pull/16821)
- [[`f71f41d79b`](https://github.com/nodejs/node/commit/f71f41d79b)] - **test**: add values to error message (Adam Jeffery) [#16831](https://github.com/nodejs/node/pull/16831)
- [[`c1cdc658c0`](https://github.com/nodejs/node/commit/c1cdc658c0)] - **test**: replace common.fixturesDir with fixtures.readKey() (woj) [#16817](https://github.com/nodejs/node/pull/16817)
- [[`c662cc0b70`](https://github.com/nodejs/node/commit/c662cc0b70)] - **test**: use internet.addresses in internet tests (Joyee Cheung) [#16390](https://github.com/nodejs/node/pull/16390)
- [[`a465f2bc78`](https://github.com/nodejs/node/commit/a465f2bc78)] - **test**: introduce test/common/internet.addresses (Joyee Cheung) [#16390](https://github.com/nodejs/node/pull/16390)
- [[`bc19a93093`](https://github.com/nodejs/node/commit/bc19a93093)] - **test**: use tmpDir in test-fs-utimes (Rich Trott) [#16774](https://github.com/nodejs/node/pull/16774)
- [[`4d55a1dc2f`](https://github.com/nodejs/node/commit/4d55a1dc2f)] - **test**: improve assert messages in napi exception test (Paul Blanche) [#16820](https://github.com/nodejs/node/pull/16820)
- [[`8ad4f768c0`](https://github.com/nodejs/node/commit/8ad4f768c0)] - **test**: remove message argument in cluster setup test (mbornath) [#16838](https://github.com/nodejs/node/pull/16838)
- [[`21e9888237`](https://github.com/nodejs/node/commit/21e9888237)] - **test**: check session timeout in http2 (Anatoli Papirovski) [#16754](https://github.com/nodejs/node/pull/16754)
- [[`be266bdbbd`](https://github.com/nodejs/node/commit/be266bdbbd)] - **test**: move test-http-keepalive-maxsockets to sequential (Rich Trott) [#16777](https://github.com/nodejs/node/pull/16777)
- [[`adcaddfce8`](https://github.com/nodejs/node/commit/adcaddfce8)] - **test**: improve assert messages in test-global (Mark McNelis) [#16843](https://github.com/nodejs/node/pull/16843)
- [[`535eb64e55`](https://github.com/nodejs/node/commit/535eb64e55)] - **tools**: enforce no unused trailing arguments tools directory (Rich Trott) [#16953](https://github.com/nodejs/node/pull/16953)
- [[`ad27e2c2e8`](https://github.com/nodejs/node/commit/ad27e2c2e8)] - **tools**: remove unused trailing function arguments (Rich Trott) [#16953](https://github.com/nodejs/node/pull/16953)
- [[`7ba35995a7`](https://github.com/nodejs/node/commit/7ba35995a7)] - **tools**: fix inspector-check reporting (Daniel Bevenius) [#16902](https://github.com/nodejs/node/pull/16902)
- [[`25dd8f66be`](https://github.com/nodejs/node/commit/25dd8f66be)] - **tools**: add direct anchors for error codes (Joyee Cheung) [#16779](https://github.com/nodejs/node/pull/16779)
- [[`625999b840`](https://github.com/nodejs/node/commit/625999b840)] - **tools**: don't lint files that have not changed (Joyee Cheung) [#16581](https://github.com/nodejs/node/pull/16581)
- [[`942a9ed6a8`](https://github.com/nodejs/node/commit/942a9ed6a8)] - **tools,build**: allow build without `remark-cli` (Refael Ackermann) [#16893](https://github.com/nodejs/node/pull/16893)

Windows 32-bit Installer: https://nodejs.org/dist/v9.2.0/node-v9.2.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v9.2.0/node-v9.2.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v9.2.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v9.2.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v9.2.0/node-v9.2.0.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v9.2.0/node-v9.2.0-darwin-x64.tar.gz \
Linux 32-bit Binary: https://nodejs.org/dist/v9.2.0/node-v9.2.0-linux-x86.tar.xz \
Linux 64-bit Binary: https://nodejs.org/dist/v9.2.0/node-v9.2.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v9.2.0/node-v9.2.0-linux-ppc64le.tar.xz \
Linux PPC BE 64-bit Binary: https://nodejs.org/dist/v9.2.0/node-v9.2.0-linux-ppc64.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v9.2.0/node-v9.2.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v9.2.0/node-v9.2.0-aix-ppc64.tar.gz \
SmartOS 32-bit Binary: https://nodejs.org/dist/v9.2.0/node-v9.2.0-sunos-x86.tar.xz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v9.2.0/node-v9.2.0-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v9.2.0/node-v9.2.0-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v9.2.0/node-v9.2.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v9.2.0/node-v9.2.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v9.2.0/node-v9.2.0.tar.gz \
Other release files: https://nodejs.org/dist/v9.2.0/ \
Documentation: https://nodejs.org/docs/v9.2.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

066ab559f3744b0301637bb8d51e22da655e6a5ab0b38e2ff5d9a68e669cedf5  node-v9.2.0-aix-ppc64.tar.gz
42f78982b8614f099bc70819db40074ff7c1c49d4bac71eca5ffe4120452f289  node-v9.2.0-darwin-x64.tar.gz
78c1b7a4e12c38e0b68d7f5801b51dc2c7520584ef09cc6932d765476f018c87  node-v9.2.0-darwin-x64.tar.xz
63573d4d5fe5c9d63f179261d2c49267cbd4e540628c23d86c69071f1adec276  node-v9.2.0-headers.tar.gz
48e20282487c6f8d955d270123359ddda73faf550254740aaad2336f74fea336  node-v9.2.0-headers.tar.xz
39c2e7a40fcf219d322232c1fe86e6db6eb45361969fb9a2314abbe494207d33  node-v9.2.0-linux-arm64.tar.gz
1b5c44160b7bfc9e5d63697d63c82330ae3e6094ab48449bd958ebe0baef73f0  node-v9.2.0-linux-arm64.tar.xz
3698353153ed8e7af68b150accf77cc0ae4aac2cd57a01520d322c263a433dca  node-v9.2.0-linux-armv6l.tar.gz
73b423ead7ec5c82b30efc7978c9eecb56b2d4ab02c55e2a7e0a7449d0883a7e  node-v9.2.0-linux-armv6l.tar.xz
2b5c94aedda93b669f99ae60e90a5007969aad2b92818d30b91111224a0ad8f9  node-v9.2.0-linux-armv7l.tar.gz
cd9268ab8b4eb79c9708130cea44966ff11be54a80dffd3d1c19bb8d43bfc63e  node-v9.2.0-linux-armv7l.tar.xz
9a173db0c0b88dcddf08542736c597c87bf86d536df8fa65a4fdff75e7bb4243  node-v9.2.0-linux-ppc64le.tar.gz
17adbe48888764b473b908ef4c4b3bb8aa8417f9a5c18a9ddb966380ecb9c5eb  node-v9.2.0-linux-ppc64le.tar.xz
34b3d6d7e6036e38aacab6754f55711ac9582a3d963a06f5f75e07e2d986d2a2  node-v9.2.0-linux-ppc64.tar.gz
13dca201fa19e2d49cdc663f31ce6f6f98f202f693d3ddb17880c6737bb66ca6  node-v9.2.0-linux-ppc64.tar.xz
c5f6315da901bbb54ffa0b23b58cd0b46f0ebfbe2a4f1847ec1d330602000bfd  node-v9.2.0-linux-s390x.tar.gz
e49c0bb33b23ef306dbe586f0f00b18156a86d946b898bab00910b19213cfd1b  node-v9.2.0-linux-s390x.tar.xz
36ef2b3d1a99555390835d6fd4ad194a769df6841cbcc46cba0dffbaf6e6aa34  node-v9.2.0-linux-x64.tar.gz
ded9cef0243bbe8ac5a417be0c6471bed6ed82367714ccb23f79a5be0ba2bd5d  node-v9.2.0-linux-x64.tar.xz
84579415724cd607d177cd493881b409be08203198abbb26834e06b0b4462b59  node-v9.2.0-linux-x86.tar.gz
54f34cd65298753e6835702d22c2006648e06120e057c84261980cdc022627f6  node-v9.2.0-linux-x86.tar.xz
47759e657d53cf96b5daf702853c33d771caa99c726348f6092a4297116f4f8b  node-v9.2.0.pkg
3a06ddcd8df0527d42fd90d5a3240201a6db1b5d9f0ad7a8cd24e45d389d3cba  node-v9.2.0-sunos-x64.tar.gz
7cf6b9bc9d20fbaf11e5e0766fa3827159a6196f39e26d529747b55339c030a4  node-v9.2.0-sunos-x64.tar.xz
e269670b285170944e9bb90c9f5873935a58097611d6a239c064305543039adc  node-v9.2.0-sunos-x86.tar.gz
4af3c9b264bc43c870a0473abd6ffad230d829db871c082af294f5de447ef4c4  node-v9.2.0-sunos-x86.tar.xz
905ce9067b56f298ce110db4dd63cdeca8fce8097394f8ea9e4fe37fef2834ee  node-v9.2.0.tar.gz
64caf263eadc1aea072ce5d30ded7f4534aa7f59c8c6993eee4accad96e3bbc2  node-v9.2.0.tar.xz
5295fd5926ecf67b5aaf0fba38022790d8a186dfcdd3b9070238ad73e748ba9d  node-v9.2.0-win-x64.7z
5e7feb536547c715155d772f90857239b37897c1a1a664809fd84a7aeae1a724  node-v9.2.0-win-x64.zip
ded691d3c919cb4fc1176df5a2dbe34f6e05817817008f82fadda1697b86895f  node-v9.2.0-win-x86.7z
17e9173217ea442773e9fed15a14e4865329650e58eba271db2f68aaaf09ce1e  node-v9.2.0-win-x86.zip
eb6c095497f925aaf26e0c82156548a9332eb0805de9fd700e4f827107572f82  node-v9.2.0-x64.msi
f2ff3c4a8a33baa37162412d14b74273efde0bc0930d4f3f7cf4adc5ac0d9438  node-v9.2.0-x86.msi
91108728a62eb49ae88b52b0ae1e7665a84e7ad3e2905e1dc3ebe579dbdb3824  win-x64/node.exe
71b8dd1d6693c04381256a899e648c8ce931d58206f5cec6ec11d022764f6ec5  win-x64/node.lib
666bac7f0b40fc1647f71ff250b355e03ab29def9115006cae883e1c55f6dd6d  win-x64/node_pdb.7z
dab53ddc3379fba069eccad039b9be086bf9b064dbd8ea2cfddd11a45b138030  win-x64/node_pdb.zip
c552d7c02788662017dbf9cbaf399caa9576c000299c94a39fe50281b07878c9  win-x86/node.exe
8d3af8295d333a72d37a7824fd701ce7c26b3081bcaad369d0e7cc8ca2eb009c  win-x86/node.lib
0435e1b8599017db79c83a0a9491a1c126a91ff315fc11f00c3cc981ec1d3573  win-x86/node_pdb.7z
d6c8b6e7d9a90da6b522101c53449fe3df70adab48d821ea127b4694d9fe59b4  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEua6ZBf/XgD8lcUZhtjtTWkwgbKkFAloNovQACgkQtjtTWkwg
bKkvNQ/9GlPegK7/KOsF2R4xuMPSVwOPyTRyZPV4hRWzC+g2T+d8fWGHuZJ/MjZl
5nc3fRBtNVWVewvhUjRUaf0Ex0XkCn3PuirUAVg8ZByPx/ljjeKDPx6CUekv/kk1
hjPh7S5MtEW8VV7/XJcxpiX9eFVGjyfQMF7hUhPPJ8IIFtneV/y2j4zRJbn4FTCL
QzUjb8s+G6kXYEhXNsVpnmOAvSU9hStFcNxnb2WwmMgBIVn291B148R7ZFd3rxLy
6Bzmv6qWawMLvndquZW9sh0VtVDAS0nnXO3hUjtsGtmoBC/GlBeNLxGMJk7++auI
pOAnbDLI6024CJBYx6oNaqWAdmVYFciFqom0C48a2q0lnFwlUlkYtYF2hm3eZlFT
AJPVlkKBQr3mlm88tWB8JD9paL+01TWmT7Li7RiI+Jw3V/UDy+2u9jY19tnPIsZe
YW+zL3SCqe4UI4vXIPv8hO/PHbyGMxMYgP0PH3J6EI51G89s9pFmWApPM29h0zhd
q3G3Whw0nIcxR8QvdmAJ5ON++2sr2EGN1Gs3pPHTWMgncHHjcNvoPJdT61pk5v70
4a4u+w8MuPsvn3q63pFvsFVSXAU6z9euQMi3IIuVC/E6ltguGOq98TpcZS269vaU
godnvMca4gze4dXxIePAWDhP+pfDVDLx5djpja5cmwy6SosjG3k=
=fqSp
-----END PGP SIGNATURE-----

```
