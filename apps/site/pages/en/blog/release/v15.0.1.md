---
date: '2020-10-21T20:26:35.297Z'
category: release
title: Node v15.0.1 (Current)
layout: blog-post
author: <PERSON>
---

### Notable changes

- **crypto**: fix regression on randomFillSync (<PERSON>) [#35723](https://github.com/nodejs/node/pull/35723)
  - This fixes issue https://github.com/nodejs/node/issues/35722.
- **deps**: upgrade npm to 7.0.3 (<PERSON><PERSON>) [#35724](https://github.com/nodejs/node/pull/35724)
- **doc**: add release key for <PERSON> (<PERSON>) [#35545](https://github.com/nodejs/node/pull/35545)

### Commits

- [[`c509485c19`](https://github.com/nodejs/node/commit/c509485c19)] - **build**: use make functions instead of echo (<PERSON>) [#35707](https://github.com/nodejs/node/pull/35707)
- [[`f5acc2d030`](https://github.com/nodejs/node/commit/f5acc2d030)] - **crypto**: fix regression on randomFillSync (<PERSON> Snell) [#35723](https://github.com/nodejs/node/pull/35723)
- [[`595c8df48d`](https://github.com/nodejs/node/commit/595c8df48d)] - **deps**: upgrade npm to 7.0.3 (Ruy Adorno) [#35724](https://github.com/nodejs/node/pull/35724)
- [[`69e7f20f2d`](https://github.com/nodejs/node/commit/69e7f20f2d)] - **deps**: V8: set correct V8 version patch number (Michaël Zasso) [#35732](https://github.com/nodejs/node/pull/35732)
- [[`b78294dc00`](https://github.com/nodejs/node/commit/b78294dc00)] - **doc**: use kbd element in readline doc (Rich Trott) [#35698](https://github.com/nodejs/node/pull/35698)
- [[`1efa87082b`](https://github.com/nodejs/node/commit/1efa87082b)] - **doc**: add release key for Danielle Adams (Danielle Adams) [#35545](https://github.com/nodejs/node/pull/35545)
- [[`6e91d644e3`](https://github.com/nodejs/node/commit/6e91d644e3)] - **doc**: use kbd element in os doc (Rich Trott) [#35656](https://github.com/nodejs/node/pull/35656)
- [[`5a48a7b6f8`](https://github.com/nodejs/node/commit/5a48a7b6f8)] - **doc**: add a statement in the documentation. (Pooja D.P) [#35585](https://github.com/nodejs/node/pull/35585)
- [[`6a7a61be7c`](https://github.com/nodejs/node/commit/6a7a61be7c)] - **src**: mark/pop OpenSSL errors in NewRootCertStore (Daniel Bevenius) [#35514](https://github.com/nodejs/node/pull/35514)
- [[`d54edece99`](https://github.com/nodejs/node/commit/d54edece99)] - **test**: refactor test-crypto-pbkdf2 (Tobias Nießen) [#35693](https://github.com/nodejs/node/pull/35693)

Windows 32-bit Installer: https://nodejs.org/dist/v15.0.1/node-v15.0.1-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v15.0.1/node-v15.0.1-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v15.0.1/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v15.0.1/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v15.0.1/node-v15.0.1.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v15.0.1/node-v15.0.1-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v15.0.1/node-v15.0.1-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v15.0.1/node-v15.0.1-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v15.0.1/node-v15.0.1-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v15.0.1/node-v15.0.1-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v15.0.1/node-v15.0.1-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v15.0.1/node-v15.0.1-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v15.0.1/node-v15.0.1.tar.gz \
Other release files: https://nodejs.org/dist/v15.0.1/ \
Documentation: https://nodejs.org/docs/v15.0.1/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

f070380cab039487ae0d16db122c4f6eaf0a165ccbf03685214e3ec5a7e98644  node-v15.0.1-aix-ppc64.tar.gz
8f7e2ddd44d2aef20d568489f2cf844383037725ce2fc04ad722a312ef08b2d0  node-v15.0.1-darwin-x64.tar.gz
78571df5b35d3ec73d7543332776bcb8cab3bc0e3abd12b1440fbcd01c74c055  node-v15.0.1-darwin-x64.tar.xz
ad2d9f2a42064e6cc33967b87462b95ffac0f7511d1dc9a0681c9ccd9038b4b1  node-v15.0.1-headers.tar.gz
b9b1b2fe9ff74f43a4b2a21dfa522d66e78f806409fab1bf1a9890aaf22239d9  node-v15.0.1-headers.tar.xz
138ea304781fb8f7c830f5800bea61631164b304df99f5a008cc0eeaadbe6548  node-v15.0.1-linux-arm64.tar.gz
403571f7e37dfefd7bd46411fff4ec7d81bf3d1a34feb37939ad35a06e61d855  node-v15.0.1-linux-arm64.tar.xz
58e488f4ce86db179a1536b789cbeb20565286ee890225e4103480faa4e2f528  node-v15.0.1-linux-armv7l.tar.gz
5c6a1c037936e205a567a6f07c051f5c36983e6672ed36afb3816d7bd742e346  node-v15.0.1-linux-armv7l.tar.xz
30584c586aaa1a1fa1e6bec39c1b95f57ddc9d3144931e79f6e47424a680c0b6  node-v15.0.1-linux-ppc64le.tar.gz
bf7e7b208a5fc34d4f414d577971fb388e34d7d67992702bfcfbf31d72e94cc0  node-v15.0.1-linux-ppc64le.tar.xz
d9109cbb64f67012f09d2d554b3474435a8150ac8e26cc2ce6cf598ab7b6bba2  node-v15.0.1-linux-s390x.tar.gz
537fec4b3e2c06459991d25641da83533fee551575326b36d54e44364a10678a  node-v15.0.1-linux-s390x.tar.xz
60d1ede0ddddaf2e47addf8cfc6955909b231d02710522341f3bf611344cd79e  node-v15.0.1-linux-x64.tar.gz
cc9c3eed21755b490e5333ccab208ce15b539c35f64a764eeeae77c58746a7ff  node-v15.0.1-linux-x64.tar.xz
51a45cdde9e6ecb78b46ec1d4bed3b06bcead9e6703b4ed18841124e9250cdd4  node-v15.0.1.pkg
b9a00a4847863914ffe7751c2d81b67cb96a8f958cbc692f988c8c78db14ebec  node-v15.0.1.tar.gz
b8d0937a681cfe9b3a4b166b96b114e5535a675a3c52aae8a9d599840fb5bd0f  node-v15.0.1.tar.xz
1d99fb549ba7de708b1a04c9abf5dd6583596d430ce3e2cb723e540ea87fcde0  node-v15.0.1-win-x64.7z
efa7a74d91789a6e9f068f375e49f108ff87578fd88ff4b4e7fefd930c04db6c  node-v15.0.1-win-x64.zip
5920774dbc47ff891412f68ae7655fef9011eda545c07d5280e31745020c8285  node-v15.0.1-win-x86.7z
dbaf2ea2022536e4b2408536d685c6e266e3adc5cfd27d228a5373ca582e2d3f  node-v15.0.1-win-x86.zip
d0fe72c45af84b7a9e5963d6237a4c598dc1f1e494f40d49e985fa9e4e7e3b86  node-v15.0.1-x64.msi
f8685529c9d70ccadb0df464cb6d41801aee67af8d00e6fee78df66749728ae8  node-v15.0.1-x86.msi
559fd3328aabd9f4a7fdb04795d8276922015afbaf2e2c55e67ebd142ed064bf  win-x64/node.exe
0668ebb22765d3e797c4401f36587f5cc4ada69611e2793cb79209625aca0931  win-x64/node.lib
cf56e1377da62d4a1fda3e94b068cdb8ba36529172b0f4d4531e9ad446af7757  win-x64/node_pdb.7z
2f3d94a71330dd38c30e80666a58fc90f55ba084d00926f2fbbd15a4bc95c3ea  win-x64/node_pdb.zip
730a64986b4f03f27a86dc756fd2a3bb7f18e53a45886d18829cca6f66ba26ec  win-x86/node.exe
3cbb6fca39edc95246ed5169ac8fb1c7e575e45be873dbae255577f66f4d2b2b  win-x86/node.lib
9231817c9d57a14bb7df7604ebe8fec26b222ac076f22ba4708a15e4a8e9b3d9  win-x86/node_pdb.7z
a4d1f90123e977314117f5bc7d74380d1d624a57f20b7a00c1398ff142e7b071  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQEzBAEBCAAdFiEETtd49TnjY0x3nIfG1wYoSKGrAFwFAl+QmQoACgkQ1wYoSKGr
AFzjcggAg7Xjo3OifqHToCa0v44514lJ640HV13K1oZSlzgTcIKME7+06Z10VCRs
qOhN9qKwziNZi0FJy4YBcCIRt0oqD5COXAHsfqt9fgiiA0/1w2gyBXDnkch+TKuy
JsLdp71dyEUss3opVXjwyJ7Fvae9CWCE5E6SZ5EPn+iszSDutQPqxiK2C3zth+JC
WYh6g9Q3N17wanF4F3Z6Pz6qgY6PjuR+OfQW7lDGLdhU0moGxjcrnsP+HAVAKAas
grBLc3SG8Ru7AeeBo8+2DSVth1sfElh9GX2I8TUDuztMI0ApyoVCOPr85vsWKgel
P2y1BANUwpLwoTve+2tQ9ERHnoq59Q==
=smX4
-----END PGP SIGNATURE-----

```
