---
date: '2023-02-16T22:16:31.243Z'
category: release
title: Node v16.19.1 (LTS)
layout: blog-post
author: <PERSON>
---

### Notable Changes

The following CVEs are fixed in this release:

- **[CVE-2023-23918](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2023-23918)**: Node.js Permissions policies can be bypassed via process.mainModule (High)
- **[CVE-2023-23919](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2023-23919)**: Node.js OpenSSL error handling issues in nodejs crypto library (Medium)
- **[CVE-2023-23920](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2023-23920)**: Node.js insecure loading of ICU data through ICU_DATA environment variable (Low)

Fixed by an update to undici:

- **[CVE-2023-23936](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2023-23936)**: Fetch API in Node.js did not protect against CRLF injection in host headers (Medium)
  - See <https://github.com/nodejs/undici/security/advisories/GHSA-5r9g-qh6m-jxff> for more information.
- **[CVE-2023-24807](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2023-24807)**: Regular Expression Denial of Service in Headers in Node.js fetch API (Low)
  - See <https://github.com/nodejs/undici/security/advisories/GHSA-r6ch-mqf9-qc9w> for more information.

More detailed information on each of the vulnerabilities can be found in [February 2023 Security Releases](/blog/vulnerability/february-2023-security-releases/) blog post.

This security release includes OpenSSL security updates as outlined in the recent
[OpenSSL security advisory](https://www.openssl.org/news/secadv/20230207.txt).

### Commits

- \[[`7fef050447`](https://github.com/nodejs/node/commit/7fef050447)] - **build**: build ICU with ICU_NO_USER_DATA_OVERRIDE (RafaelGSS) [nodejs-private/node-private#374](https://github.com/nodejs-private/node-private/pull/374)
- \[[`b558e9f476`](https://github.com/nodejs/node/commit/b558e9f476)] - **crypto**: clear OpenSSL error on invalid ca cert (RafaelGSS) [nodejs-private/node-private#375](https://github.com/nodejs-private/node-private/pull/375)
- \[[`160adb7ffc`](https://github.com/nodejs/node/commit/160adb7ffc)] - **crypto**: clear OpenSSL error queue after calling X509_check_private_key() (Filip Skokan) [#45495](https://github.com/nodejs/node/pull/45495)
- \[[`d0ece30948`](https://github.com/nodejs/node/commit/d0ece30948)] - **crypto**: clear OpenSSL error queue after calling X509_verify() (Takuro Sato) [#45377](https://github.com/nodejs/node/pull/45377)
- \[[`2d9ae4f184`](https://github.com/nodejs/node/commit/2d9ae4f184)] - **deps**: update undici to v5.19.1 (Matteo Collina) [nodejs-private/node-private#388](https://github.com/nodejs-private/node-private/pull/388)
- \[[`d80e8312fd`](https://github.com/nodejs/node/commit/d80e8312fd)] - **deps**: cherry-pick Windows ARM64 fix for openssl (Richard Lau) [#46568](https://github.com/nodejs/node/pull/46568)
- \[[`de5c8d2c2f`](https://github.com/nodejs/node/commit/de5c8d2c2f)] - **deps**: update archs files for quictls/openssl-1.1.1t+quic (RafaelGSS) [#46568](https://github.com/nodejs/node/pull/46568)
- \[[`1a8ccfe908`](https://github.com/nodejs/node/commit/1a8ccfe908)] - **deps**: upgrade openssl sources to OpenSSL_1_1_1t+quic (RafaelGSS) [#46568](https://github.com/nodejs/node/pull/46568)
- \[[`693789780b`](https://github.com/nodejs/node/commit/693789780b)] - **doc**: clarify release notes for Node.js 16.19.0 (Richard Lau) [#45846](https://github.com/nodejs/node/pull/45846)
- \[[`f95ef064f4`](https://github.com/nodejs/node/commit/f95ef064f4)] - **lib**: makeRequireFunction patch when experimental policy (RafaelGSS) [nodejs-private/node-private#358](https://github.com/nodejs-private/node-private/pull/358)
- \[[`b02d895137`](https://github.com/nodejs/node/commit/b02d895137)] - **policy**: makeRequireFunction on mainModule.require (RafaelGSS) [nodejs-private/node-private#358](https://github.com/nodejs-private/node-private/pull/358)
- \[[`d7f83c420c`](https://github.com/nodejs/node/commit/d7f83c420c)] - **test**: avoid left behind child processes (Richard Lau) [#46276](https://github.com/nodejs/node/pull/46276)

Windows 32-bit Installer: https://nodejs.org/dist/v16.19.1/node-v16.19.1-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v16.19.1/node-v16.19.1-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v16.19.1/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v16.19.1/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v16.19.1/node-v16.19.1.pkg \
macOS Apple Silicon 64-bit Binary: https://nodejs.org/dist/v16.19.1/node-v16.19.1-darwin-arm64.tar.gz \
macOS Intel 64-bit Binary: https://nodejs.org/dist/v16.19.1/node-v16.19.1-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v16.19.1/node-v16.19.1-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v16.19.1/node-v16.19.1-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v16.19.1/node-v16.19.1-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v16.19.1/node-v16.19.1-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v16.19.1/node-v16.19.1-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v16.19.1/node-v16.19.1-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v16.19.1/node-v16.19.1.tar.gz \
Other release files: https://nodejs.org/dist/v16.19.1/ \
Documentation: https://nodejs.org/docs/v16.19.1/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

b93adce984bca2712bf4e48c49f828cd1ae1a8d89e6ebd9e4fecb2165cf6b438  node-v16.19.1-aix-ppc64.tar.gz
168f787f457bf645f3fc41e7419b62071db7d42519ce461b1d7ebfc0acbdbfb1  node-v16.19.1-darwin-arm64.tar.gz
69113e841529e7bd162f96890ce3bf4f59e88908cb264ad3ff75401d7f632f79  node-v16.19.1-darwin-arm64.tar.xz
d7f683b2a8f78db8a28235a175e130c760f0d3cd335404e02f223e3a9adc30c7  node-v16.19.1-darwin-x64.tar.gz
6127d4a82697365214434d06889811de36cb8da86c4c0058d16324a1a66cedb0  node-v16.19.1-darwin-x64.tar.xz
b2d010190ad40b52ab2fb92131db51375ef682a37f924fe2ce3813767d68fee9  node-v16.19.1-headers.tar.gz
0097c65867748073530e19df21071f1b7567465b5b454275b6a98c37828b0827  node-v16.19.1-headers.tar.xz
d4bfa62f5b1aacf74169e8ff58af812d0ef34ef6152c6ad812f220e9bf6cc462  node-v16.19.1-linux-arm64.tar.gz
042b3ae7e994a77bfdb0e366d0389c1b7602bb744830da15f9325f404f979ce2  node-v16.19.1-linux-arm64.tar.xz
53d88ced853a9e2fa80a216764ff42fb971d0b46c0a16c5e2dc99beead9bc5d8  node-v16.19.1-linux-armv7l.tar.gz
dc03071a0e46dd59eb2e60624b1c4a8bb258530be58271a64a2e13274c8b4734  node-v16.19.1-linux-armv7l.tar.xz
0e8121a1fdcdcd27d48cd8391089051b8e4a9e1902847c0561692789f3c41999  node-v16.19.1-linux-ppc64le.tar.gz
cfbeb41a5fbc15b4fbd12abdc6cfef5668ed6bdfc30b68c5d244dd80be1d0e78  node-v16.19.1-linux-ppc64le.tar.xz
4bc7b66cc00dd15a01055e9b403d7efff4a36331fa00bf9dc69989d8bca667ae  node-v16.19.1-linux-s390x.tar.gz
3291a5d092631462df757470ef601da37ba973e9ec749cac7417edb53e79ca73  node-v16.19.1-linux-s390x.tar.xz
ca63da538e02de15b7e974f7a17ce4732cc0d63023942301d30044c472ed9ddd  node-v16.19.1-linux-x64.tar.gz
fa796a23837dc5c22914b1349b6117df4d497e2001a4cd7b28b0767e22f3bb51  node-v16.19.1-linux-x64.tar.xz
5b44e1d083ea379dbf6c2c431f74f7990da6e77bd898d3b2a324240015fc28b3  node-v16.19.1.pkg
e795d23b2924b69e02fcc670335a5cd3a7ce121557fdc585f9e5bda0e77550ea  node-v16.19.1.tar.gz
17fb716406198125b30c94dd3d1756207b297705626afe16d8dc479a65a1d8b5  node-v16.19.1.tar.xz
020930ed45d64055ed37cc4a607d7f352554057ccaeeedbbea41e4230062084d  node-v16.19.1-win-x64.7z
77e0198497fee24552d6a6f1737eed595b619af1b749ee0bee4b938026e55f73  node-v16.19.1-win-x64.zip
2dbb3636f7fc15f79ceed94384a8dc56d25b9cab1ed588d284f7fe5427125afc  node-v16.19.1-win-x86.7z
180ffdbfa86ddc82520f05f31bdda6a8237e24a2dd8cd39f53ceb2f6dfbdf4a3  node-v16.19.1-win-x86.zip
7b1f2c62b91b599fb8c7ecddc34385ec1b7a99092aaaca74bec8e2caa3623687  node-v16.19.1-x64.msi
9d73be67ed7b953afa220efb642121e64c2bb9d8319c883e07cf1ef267b0614f  node-v16.19.1-x86.msi
264fa3ca6ad85c396729b66d21b17763c242ad6681b1f3902bcdbce1f1d45828  win-x64/node.exe
d6bada534f8806049a8a05f2a7d99e2a1fcf3675d3269b2a887da6c0c42f0929  win-x64/node.lib
8eb738110c8a3ac88d9decd57e0c6cf96b66a1902d0a71c1d44510780893a52b  win-x64/node_pdb.7z
4292dbbcac393b9a5f8f96dd58ec595c610607cc7bd99ff339b7fba2066023fe  win-x64/node_pdb.zip
28ef8332f6cd83d2610da9179862a6ee3a0ed39167f0838584f2571a059e96a9  win-x86/node.exe
b171f4785f44b475710a24f1d0eb3f0333082a7f288e5a2b7fcc56866f75af51  win-x86/node.lib
5261515011c9cdd4085322de172fb0ac7063fbbfa7ee6d65599f64b383490188  win-x86/node_pdb.7z
e5e2ba8f6235de813f04673f94d408b84733ab7f03d3c613a2be2d269c21a9e1  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEyC+jrhy+3Gvka5NgxDzsRcF6uTwFAmPuqBMACgkQxDzsRcF6
uTxwOw//QQ16C1RIWDULSqzl7/jvRRHNhNPh/m/vYjr1IMiE+LSwgY+IyQEhMv4T
sxFSX/I3HmqVDsTUN7PjTUrDEK7Ps4/NDJFVJLl+hFnR7tbvOXczrmJfjhSngLFc
4bEKCGzo0dXxx3iG5Fa1GS1ejwpoXgbSzZj4AoyO1mAiWXoesxKFqcdOfHD7KhDu
gTD4IHvNwpIDbCjGBpURJzeFvo37LhGdypUuW411ONiiHEC1bfip1n3TI5faIkZm
SEaFrZ9nZNeF5DZqO1zyL51gjranXhT7UhcJTJ18fsTn2rheaBwiXnjl5S93aK9t
jZIn7diilJ1khcKzsBQQlbE2EJmhji1374vLiYtB7zSlKA3X+MAxdPQFvnRoXtyh
hbMjTSJMZ3Jw6ouLNYqdDPdZIWUtuwPJvQSUr9EyWYbKN739V+84bOBlx1g37h5g
CwiKmdSGog8MOMun4TQVL1Ods12nf535Fqiaq5puwl9+CQfui7nbLKZ6QgI9VwPA
yYaU4vDFp9uby08seQHd6IDpo9ckX0Yr3ztzV5Hx5UpO7xSbjCVsOakeiID3/rF+
tITN21uMFYH6HfAGP3aHdxGe6PE68w2CViKYLqtxKhW5SwkD8+ITQTVYaXNIscag
tAp/Jup48gN8m1tlffMRtQoe3DPNU+1B1y/HfuQ1iXNk7hkgB6k=
=XWk1
-----END PGP SIGNATURE-----

```
