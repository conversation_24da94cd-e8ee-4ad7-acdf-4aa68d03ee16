---
date: '2013-03-01T19:17:40.000Z'
category: release
title: Node v0.9.11 (Unstable)
layout: blog-post
author: The Node.js Project
---

2013.03.01, Version 0.9.11 (Unstable)

- V8: downgrade 3.14.5

- openssl: update to 1.0.1e

- da<PERSON><PERSON>: Make process.title work properly (<PERSON>)

- fs: Support mode/flag options to read/append/writeFile (isaacs)

- stream: \_read() no longer takes a callback (isaacs)

- stream: Add stream.unshift(chunk) (isaacs)

- stream: remove lowWaterMark feature (isaacs)

- net: omit superfluous 'connect' event (<PERSON>)

- build, windows: disable SEH (<PERSON>)

- core: remove errno global (<PERSON>)

- core: Remove the nextTick for running the main file (isaacs)

- core: Mark exit() calls with status codes (isaacs)

- core: Fix debug signal handler race condition lock (isaacs)

- crypto: clear error stack (<PERSON>)

- test: optionally set common.PORT via env variable (<PERSON>)

- path: Throw TypeError on non-string args to path.resolve/join (isaa<PERSON>, <PERSON><PERSON><PERSON>)

- crypto: fix uninitialized memory access in openssl (<PERSON>s)

Source Code: https://nodejs.org/dist/v0.9.11/node-v0.9.11.tar.gz

Macintosh Installer (Universal): https://nodejs.org/dist/v0.9.11/node-v0.9.11.pkg

Windows Installer: https://nodejs.org/dist/v0.9.11/node-v0.9.11-x86.msi

Windows x64 Installer: https://nodejs.org/dist/v0.9.11/x64/node-v0.9.11-x64.msi

Windows x64 Files: https://nodejs.org/dist/v0.9.11/x64/

Linux 32-bit Binary: https://nodejs.org/dist/v0.9.11/node-v0.9.11-linux-x86.tar.gz

Linux 64-bit Binary: https://nodejs.org/dist/v0.9.11/node-v0.9.11-linux-x64.tar.gz

Solaris 32-bit Binary: https://nodejs.org/dist/v0.9.11/node-v0.9.11-sunos-x86.tar.gz

Solaris 64-bit Binary: https://nodejs.org/dist/v0.9.11/node-v0.9.11-sunos-x64.tar.gz

Other release files: https://nodejs.org/dist/v0.9.11/

Website: https://nodejs.org/docs/v0.9.11/

Documentation: https://nodejs.org/docs/v0.9.11/api/

Shasums:

```
2e11c53449523642f1b3f2cad9726d032ebf0a1b  node-v0.9.11-darwin-x64.tar.gz
6a8c18185d67ff50979a143b9e100d56b35aedde  node-v0.9.11-darwin-x86.tar.gz
16a95dfc6974ba3562801d9f7bb9e2fa0c001d32  node-v0.9.11-linux-x64.tar.gz
4711aae106edf9a2bf9f644b08db1b608e9829c1  node-v0.9.11-linux-x86.tar.gz
897c21d0fc59faebbdf515e0dfee27551386c4af  node-v0.9.11-sunos-x64.tar.gz
09c2b469ef984237bbd606d78f523d1c8b92e680  node-v0.9.11-sunos-x86.tar.gz
81627efd5c591f636147100e2e95bbbb17fd0290  node-v0.9.11-x86.msi
a0e91028c7fd091db1667ccf9dba6216ee321e98  node-v0.9.11.pkg
66370601eb824305b12c7f3e5b2a5e8ca94f1209  node-v0.9.11.tar.gz
db08f56a8258dd8a6a525595a023ad9eb72603e6  node.exe
759fe57cbceeee5820c7de176b6ef8c2a5af7fab  node.exp
299f0f8aebbc755b37fc6b40e463cb455a5bb1c6  node.lib
f12b3dc557ae16834ae3a793d768e251d1ba5db3  node.pdb
807520a39d6f7518e53da796fa1ca62316219146  x64/node-v0.9.11-x64.msi
e27a6adc077629ae9a367b4a5cb7fb31853863d3  x64/node.exe
fdcf8ccea3b2c5e3f4f99fa606715e1e27a96acc  x64/node.exp
fa0db53aac8d97bab3566c7d80fceb8e148c6d0d  x64/node.lib
e24cf62376b8439ae40b84d37c55ab23fbad4e47  x64/node.pdb
```
