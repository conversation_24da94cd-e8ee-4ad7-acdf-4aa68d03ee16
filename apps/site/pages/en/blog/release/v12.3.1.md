---
date: '2019-05-22T19:00:50.364Z'
category: release
title: Node v12.3.1 (Current)
layout: blog-post
author: <PERSON><PERSON>
---

### Notable changes

- **deps**:
  - Fix handling of +0/-0 when constant field tracking is enabled (<PERSON><PERSON><PERSON>) [#27792](https://github.com/nodejs/node/pull/27792)
  - Fix `os.freemem()` and `os.totalmem` correctness (cjihrig) [#27718](https://github.com/nodejs/node/pull/27718)
- **src**:
  - Fix v12.3.0 regression that prevents native addons from compiling [#27804](https://github.com/nodejs/node/pull/27804)

### Commits

- [[`c478884725`](https://github.com/nodejs/node/commit/c478884725)] - **deps**: V8: cherry-pick 94c87fe (<PERSON><PERSON><PERSON>) [#27792](https://github.com/nodejs/node/pull/27792)
- [[`aed74ccb4c`](https://github.com/nodejs/node/commit/aed74ccb4c)] - **deps**: upgrade to libuv 1.29.1 (cjihrig) [#27718](https://github.com/nodejs/node/pull/27718)
- [[`7438a557af`](https://github.com/nodejs/node/commit/7438a557af)] - **src**: remove util-inl.h include in node.h (Anna Henningsen) [#27804](https://github.com/nodejs/node/pull/27804)
- [[`6f7005465a`](https://github.com/nodejs/node/commit/6f7005465a)] - **src, lib**: take control of prepareStackTrace (Gus Caplan) [#23926](https://github.com/nodejs/node/pull/23926)

Windows 32-bit Installer: https://nodejs.org/dist/v12.3.1/node-v12.3.1-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v12.3.1/node-v12.3.1-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v12.3.1/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v12.3.1/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v12.3.1/node-v12.3.1.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v12.3.1/node-v12.3.1-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v12.3.1/node-v12.3.1-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v12.3.1/node-v12.3.1-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v12.3.1/node-v12.3.1-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v12.3.1/node-v12.3.1-aix-ppc64.tar.gz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v12.3.1/node-v12.3.1-sunos-x64.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v12.3.1/node-v12.3.1-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v12.3.1/node-v12.3.1-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v12.3.1/node-v12.3.1.tar.gz \
Other release files: https://nodejs.org/dist/v12.3.1/ \
Documentation: https://nodejs.org/docs/v12.3.1/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

3ab8ed94704c6d74bbf6553a3481352cbeb51dc267d8bf32390398984cb9add3  node-v12.3.1-aix-ppc64.tar.gz
b9c979f63a356090d8ff88ed141fd856ad853165c73633794a9d3a060334378e  node-v12.3.1-darwin-x64.tar.gz
03221c9aa9b5e926c687404bdcd30689dd6ed25c57f2bd63f6122a650a623855  node-v12.3.1-darwin-x64.tar.xz
1c812a9028a8958f43b28a41298ae5cab9e11662527b7610c88d74fdb24fd5ce  node-v12.3.1-headers.tar.gz
b338c85133b1339b32283b0c6c8fb58e2d26f37ef66ef5e5dbe642d7dc4af965  node-v12.3.1-headers.tar.xz
5926be88109c8efe048eedd875487041174fadd470fed4fe6ffb5eadfa50cb6b  node-v12.3.1-linux-arm64.tar.gz
88df7f2e0c4a58661bb79b637daa417929efc6c4d6a77bba42a5127c5c383257  node-v12.3.1-linux-arm64.tar.xz
712cf15d2c322bbeb44d3a24f815f65ecdb8570f3460384086eb599d2651da35  node-v12.3.1-linux-armv7l.tar.gz
ae45e8baf337ac9597d5a9c53efcf04794d6a6bb55a1160ed146282374bb21c9  node-v12.3.1-linux-armv7l.tar.xz
7e9888149b17ac46f0f942db862f5840b6f72fadc4e8b010d8857e95254ae403  node-v12.3.1-linux-ppc64le.tar.gz
fc1f67141a012974e6d1a04d661e1de80a28d55947e76a690036ee2edae68de4  node-v12.3.1-linux-ppc64le.tar.xz
b40ec9856473335b93d3cedf4195b26a0442985afd7ef811341e2f11eb852a15  node-v12.3.1-linux-s390x.tar.gz
30416b899c48aef989a8671f247c961f5b74a6550812c9176560c92d63d134b0  node-v12.3.1-linux-s390x.tar.xz
78c12398128e79dfec3092325da026d422d296c9d3089a9b2ee7bf7bd2e3be87  node-v12.3.1-linux-x64.tar.gz
46f52868c0643fe0d167ce24c3c873880c8e1494276c89c07114fb099da4f75a  node-v12.3.1-linux-x64.tar.xz
2c71af75946573a4989203e25cd891c8c49d8e35f1d10901011d859b240b010b  node-v12.3.1-sunos-x64.tar.gz
4448fc7a7a370ab9bfe13df3f7f8042bbde29ed2663aaad4bf418e9a34699acd  node-v12.3.1-sunos-x64.tar.xz
4187254f11ef425dd939679d2e0bdd3d9b9b1512eeac4c49a1f6cbe32f01adaf  node-v12.3.1-win-x64.7z
aac3c4543f846c7ebf63e1498dec7955119dffffe65722bd8c6d2124ed4ecbd7  node-v12.3.1-win-x64.zip
7456b3eae5d18b351e7b9c78032b7a84a21e9c8a5fd49bb1959c91fc4bcd34c0  node-v12.3.1-win-x86.7z
73d39170d0f8aff5943e35e340349d6c4a5af4a80259d021168bef668dabaa46  node-v12.3.1-win-x86.zip
c3d15610ea12b501eeb2e466af24e4338b4a564f0975d0ad6ef359c110223086  node-v12.3.1-x64.msi
9f12cee62a8ea20bbd02957558cf5c21a8d7575d8eb6aed25244935e6841d0ba  node-v12.3.1-x86.msi
36438c00b3d3e89f99d192647b7e4dd97422140f62395efc9ec1bd80cf1e4ddc  node-v12.3.1.pkg
d9132342815f04fdb8eb6cac5607fcee929a79e0339449774f411efed81693ac  node-v12.3.1.tar.gz
ba2b50acffe67068f44e06e5c6938f9a391086d7f8386aea829a199db02d0bfe  node-v12.3.1.tar.xz
ebcc15fb0dfcdbadccaa37bd920749c8bebb90cabd90cedd432c4a4052c0a234  win-x64/node.exe
5b198b078cb2924b29148de188296151ce8fca9683735c63d3104969e132bdd0  win-x64/node.lib
0d8d9c0518c371b39be1fa494c1a260f190a4addbe4bd1ebdd4c707ce5c1ea72  win-x64/node_pdb.7z
df6a0473a92877773a7871c21e7ab53536f23b121f37e78b7aa6202c2b8ccd81  win-x64/node_pdb.zip
057d2f51531bad1c2953ec35abbe58c3342c615bab10f9d790fa63873fd8aaf4  win-x86/node.exe
094cf74776ea08a116e65d19e1facf705c2ac64e6f04c347aa4bf2293c1378c0  win-x86/node.lib
26cabb919c57e5b30fcc0073d0807c456f9f04badd2ed1b70909a1ce023ab993  win-x86/node_pdb.7z
47187cd9de1ab49a10223428a5072a80f51e857c24f08e4a4b5ea7d7a3d2d18a  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEpIwr7mgOhBYyzU5E8HSWs+s8F2IFAlzlm8wACgkQ8HSWs+s8
F2ImtBAAySxWjoN0+xWuyb47SnoSbc/87zwlNy5jApfOzPoHJcSHQGQYqWsbREQp
IeLNzj0le+uPHQr1DbUKj1xG2O6CKAoaA1LFqxeT5mqjzCK0lw85W5QWEOPxnsQY
GwlHfTjw0+cPxFFtW16O/w8EpHXgUe8YVxRRCEfi6v1Kl09USQhl2V2LLDnnGpAb
hIUrRl9gB6SH8JVUhhpODljl7FobkNfDaHAIEVyYBuYTPWgLUmsAF4rySGR+zYf8
XgV3RKkXIiiSWBMoG46V6RVgH80AcMvxhsVuwDLcmJXJoiRNZK2OP+9O/KX6YrmT
NGLfOzU1gDimg5+MDqxlgr5QzaiVNbpUHele6jTV2myMbSGBBZE9MVSJkJW7QhcT
Mg0L3rKspdKpJV5K2YpRap7QStRH0aRgRIbvay5n8otlYZY5YMzF0Hx04cIG3J3W
+pRLFUrJz9gqOHx8oAkSpJ7dfPB1I1/5rwoEohV4If/0h4J98USXxOr9DJn9x2/n
HPLyCYbEs5dPkznPkGjNPmqyTfko/BfWSjMbYZEt7DjQqm49g3QPRixAOkf0h225
TfVV3EjSIugtiCd3GKWCd5Q7/wwm+JCEBG0d+JVvz8HeaCHMb1ZlmRIWnIyGSCt9
96imN11OlFkxm2HkL9c9o0nedWz3V0o2HWULK8cMCA4A8JTmRiU=
=NFq+
-----END PGP SIGNATURE-----

```
