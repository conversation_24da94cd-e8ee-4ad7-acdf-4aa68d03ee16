---
date: '2021-02-10T20:39:50.647Z'
category: release
title: Node v12.20.2 (LTS)
layout: blog-post
author: <PERSON><PERSON>
---

### Notable changes

- **deps**:
  - upgrade npm to 6.14.11 (<PERSON><PERSON>) [#37173](https://github.com/nodejs/node/pull/37173)

### Commits

- [[`e8a4e560ea`](https://github.com/nodejs/node/commit/e8a4e560ea)] - **async_hooks**: fix leak in AsyncLocalStorage exit (<PERSON>) [#35779](https://github.com/nodejs/node/pull/35779)
- [[`427968d266`](https://github.com/nodejs/node/commit/427968d266)] - **deps**: upgrade npm to 6.14.11 (Ruy <PERSON>) [#37173](https://github.com/nodejs/node/pull/37173)
- [[`cd9a8106be`](https://github.com/nodejs/node/commit/cd9a8106be)] - **http**: do not loop over prototype in Agent (<PERSON><PERSON><PERSON>) [#36410](https://github.com/nodejs/node/pull/36410)
- [[`4ac8f37800`](https://github.com/nodejs/node/commit/4ac8f37800)] - **http2**: check write not scheduled in scope destructor (David Halls) [#36241](https://github.com/nodejs/node/pull/36241)

Windows 32-bit Installer: https://nodejs.org/dist/v12.20.2/node-v12.20.2-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v12.20.2/node-v12.20.2-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v12.20.2/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v12.20.2/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v12.20.2/node-v12.20.2.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v12.20.2/node-v12.20.2-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v12.20.2/node-v12.20.2-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v12.20.2/node-v12.20.2-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v12.20.2/node-v12.20.2-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v12.20.2/node-v12.20.2-aix-ppc64.tar.gz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v12.20.2/node-v12.20.2-sunos-x64.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v12.20.2/node-v12.20.2-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v12.20.2/node-v12.20.2-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v12.20.2/node-v12.20.2.tar.gz \
Other release files: https://nodejs.org/dist/v12.20.2/ \
Documentation: https://nodejs.org/docs/v12.20.2/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

663510979b18d0cde123156f4fe78569716559cebe798390db4f2fd5e1940206  node-v12.20.2-aix-ppc64.tar.gz
c226e98116c169d230dd71d9adbab0fc8cc696af914de8cb80cedaa496af54cc  node-v12.20.2-darwin-x64.tar.gz
bacc1d30b62e4ea532b60672d3395ee593816d08bbcb20f907b6e02489a502f9  node-v12.20.2-darwin-x64.tar.xz
e5823b133fe58670c4f19f39c9f7f268c934f39cc4339031ac7d96fbc4ac8e72  node-v12.20.2-headers.tar.gz
da9dbb04e1d97b1531c5d6348b2d619f03a778a67d1c7ff4609232bba847c7f2  node-v12.20.2-headers.tar.xz
cd7a83dd8d9e00953079b09520647d474431f5aaad1a67200005ddff9166d55d  node-v12.20.2-linux-arm64.tar.gz
b1140e13b37250e594b8c2918a0d532fbafd816bbeee854ae2b0bcb407d9a72b  node-v12.20.2-linux-arm64.tar.xz
3a8102e4c76f588de73c0b25d2f8aaec350dff4dd445d26d65030d665e622c91  node-v12.20.2-linux-armv7l.tar.gz
c78b355ad131e03340132e8c31892c7d4916389def37f1beed1d7df55e6771c9  node-v12.20.2-linux-armv7l.tar.xz
987bc61b5f0b847eef960bc34d48cea9d14e282708f2233839d54f7d99ce8585  node-v12.20.2-linux-ppc64le.tar.gz
bdbcedf776e8504698eb98dfa39c246835017150abb8ee6c1aacb61f223ad08b  node-v12.20.2-linux-ppc64le.tar.xz
2db220600a4097c1e5c8e7e20c00a548df69696781b353cb12c24b69e2c90aae  node-v12.20.2-linux-s390x.tar.gz
d99fd6a3ad78f564b26fd5980eedd59b08d62822c32ec06c6e252bcb5f1e050a  node-v12.20.2-linux-s390x.tar.xz
e85d6866ae036782b0f5f53419a941fd742c5d5dc83fff86428b629570caa703  node-v12.20.2-linux-x64.tar.gz
bebb3596c114ca94c2f7ee0bf9370417656962c481e9b6e86744d748da422742  node-v12.20.2-linux-x64.tar.xz
1a0c934eb7cb7ccbf531db884a62edde87fffe40a7ceb331a8cc84f2612aaab5  node-v12.20.2.pkg
de0eac806ee19989c547903736e48cd4e1d660a754a78d11426928194f687718  node-v12.20.2-sunos-x64.tar.gz
71a4d4290b54876e463e4099c9f470285c7550bd7df36960369bd7203c615ef3  node-v12.20.2-sunos-x64.tar.xz
47869f9857d40f2ecb42be6c776a8794f4664fb3fb2fdbeb3caad00dc0a36a1f  node-v12.20.2.tar.gz
961ad600f0706c396390ebc7767fb01d5e3792a5022dc2e7774796756eec6d3c  node-v12.20.2.tar.xz
98c60066735f960b2f8fc6a39df6a10257969b4d0617ff2c59669be70421ac9c  node-v12.20.2-win-x64.7z
535881af6e29db15e121f847220e60659f4bc00b84afac00c577c1021954f2ab  node-v12.20.2-win-x64.zip
50169657a0ab697f11995d55cd6e1dfe0cb2b875ac1c0e8a17901670055137e9  node-v12.20.2-win-x86.7z
6d74bcd99ba6467e609af9e466105f8c6a2bb6e798ea8e0e3998bba214d684a2  node-v12.20.2-win-x86.zip
ae243f9d3def45f68b84c409ea9e15053d9375fd783a92bd36668c9c7b17332e  node-v12.20.2-x64.msi
cc3bf10d191bae17dbc34c7213d3b2b32cbc8c91bb2f73b7cbe25c06978940c7  node-v12.20.2-x86.msi
a8bdef825da10224bf848f8f5076c69e7f936847819bcc28550b691cd8ff8de4  win-x64/node.exe
2662b79e36ee678661554d290a3b8277c4c92cc74dffcda37a9f8f8e83287c73  win-x64/node.lib
0576aad8d4e68409404db0c19b3818ef7d24dc0a2184148a44bffcac8221434b  win-x64/node_pdb.7z
3574f0c7a2a3b5a09cce65332c49787f7d58c42f2877cfadc45b9e96298edc71  win-x64/node_pdb.zip
13c8e8711afa577c2aedcfdc2abc0d94d8298a9088023f4f0ed9cbc0f159c152  win-x86/node.exe
794dd4c597af2483d162426a37c99746d319aaa358219ace7bb179140f16d5f2  win-x86/node.lib
6df10df71780d29cb574434e1f9b3db92ed2b63dfe45d1858bbee050fdfc45a7  win-x86/node_pdb.7z
81d548dbbcf9494e4f5d29e3f7905112dd12306eb8b8f54f5c3a5dbe00f8d81b  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEEI9StI21e7DMQ5spl7AUGb2S+AoFAmAkQ8YACgkQl7AUGb2S
+AqPgBAAqGvXOQLVhFHk02aLZRS6gotKg0HhcHK53SFk5EN20cUEMTT/lBz7puDg
0Lw17K9M9uI/QoAbi9IuZuoHfBStOxDFDR1ojZjwNATZrkqFMVgi5nbN/srpesGj
LaRIAc7XFa4bfV/1w4nME7wWjdkKxPj/MPijmK9ADPmB8hITnsK0DUcItmj0IJ1Q
1vX7Ctp9pt3kcDPoo98SKFPrWkkCQJIUcAsJzO8uWgx5RDl+FPbNHaQ2X/r3P/QP
z1ktLL+ahAzCSQ9Xm6XXvJg/zj1R0wiC/Hd5F4uqZe0HSM3SnCn+kOsz7ygihkMP
5SjRwM9S03qJCZjPG51f15Z5aL1Qhd8HxO912XTt//m9Ehcmiu1pkK7Fq2A+mhwn
t3iatTDjj8PfFm2whcEM9p4jba5fJmt+WslemMxWORCE0yypSUJu0sVqGPb8SyjM
eDVFEtl+GSdxMqnbNupYSXti+Bxs9i0fY31rjRZSE7v703M2OlUdmFK+shbBM+6R
w0TX/kx+sXg9ixK58kAvlhNTVHowGt8dN7M3FB9u239lp23ecjHtUJHEo9R8Hffv
fvFqlqe0JzrDR7fVFid+uKD68mkPvZbCED/qT0Yrw5wWk11FfLdiuwc3yyeZQjAB
0jxekjCJqsRadbsY+lQAQnsDfbMvNs5VDkgSiN//wcjNyDRHjic=
=TdsO
-----END PGP SIGNATURE-----

```
