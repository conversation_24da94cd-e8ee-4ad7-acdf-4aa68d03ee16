---
date: '2016-06-02T22:47:05.512Z'
category: release
title: Node v6.2.1 (Current)
layout: blog-post
author: <PERSON>
---

### Notable changes

- **buffer**: Ignore negative lengths in calls to `<PERSON><PERSON><PERSON>()` and `Buffer.allocUnsafe()`. This fixes a possible security concern (reported by <PERSON><PERSON>) where user input is passed unchecked to the Buffer constructor or `allocUnsafe()` as it can expose parts of the memory slab used by other Buffers in the application. Note that negative lengths are not supported by the Buffer API and user input to the constructor should always be sanitised and type-checked. (<PERSON>) [#7051](https://github.com/nodejs/node/pull/7051)
- **npm**: Upgrade npm to 3.9.3 (<PERSON>) [#7030](https://github.com/nodejs/node/pull/7030)
  - [`npm/npm@42d71be`](https://github.com/npm/npm/commit/42d71be2cec674dd9e860ad414f53184f667620d) [npm/npm#12685](https://github.com/npm/npm/pull/12685) When using `npm ls <pkg>` without a semver specifier, `npm ls` would skip any packages in your tree that matched by name, but had a prerelease version in their `package.json`. ([@zkat](https://github.com/zkat))
  - [`npm/npm@f04e05`](https://github.com/npm/npm/commit/df04e05af1f257a1903372e1baf334c0969fbdbd) [npm/npm#10013](https://github.com/npm/npm/issues/10013) `read-package-tree@5.1.4`: Fixes an issue where `npm install` would fail if your `node_modules` was symlinked. ([@iarna](https://github.com/iarna))
  - [`b894413`](https://github.com/npm/npm/commit/b8944139a935680c4a267468bb2d3c3082b5609f) [#12372](https://github.com/npm/npm/issues/12372) Changing a nested dependency in an `npm-shrinkwrap.json` and then running `npm install` would not get up the updated package. This corrects that. ([@misterbyrne](https://github.com/misterbyrne))
  - This release includes `npm@3.9.0`, which is the result of our Windows testing push -- the test suite (should) pass on Windows now. We're working on getting AppVeyor to a place where we can just rely on it like Travis.
- **tty**: Default to blocking mode for stdio on OS X. A bug fix in libuv 1.9.0, introduced in Node.js v6.0.0, exposed problems with Node's use of non-blocking stdio, particularly on OS X which has a small output buffer. This change should fix CLI applications that have been having problems with output since Node.js v6.0.0 on OS X. The core team is continuing to address stdio concerns that exist across supported platforms and progress can be tracked at <https://github.com/nodejs/node/issues/6980>. (Jeremiah Senkpiel) [#6895](https://github.com/nodejs/node/pull/6895)
- **V8**: Upgrade to V8 *********. This includes a fix that addresses problems experienced by users of node-inspector since Node.js v6.0.0, see <https://github.com/node-inspector/node-inspector/issues/864> for details. (Michaël Zasso) [#6928](https://github.com/nodejs/node/pull/6928)

### Commits

- [[`99c05a1af0`](https://github.com/nodejs/node/commit/99c05a1af0)] - **async_wrap**: pass uid to JS as double (Trevor Norris) [#7096](https://github.com/nodejs/node/pull/7096)
- [[`371be9cd80`](https://github.com/nodejs/node/commit/371be9cd80)] - **buffer**: ignore negative allocation lengths (Anna Henningsen) [#7051](https://github.com/nodejs/node/pull/7051)
- [[`1bcc226edf`](https://github.com/nodejs/node/commit/1bcc226edf)] - **buffer**: fix dataview-set benchmark (Ingvar Stepanyan) [#6922](https://github.com/nodejs/node/pull/6922)
- [[`98270c6d15`](https://github.com/nodejs/node/commit/98270c6d15)] - **buffer**: fix single digit hex string handling (Justin Sprigg) [#6775](https://github.com/nodejs/node/pull/6775)
- [[`1fece2f8c0`](https://github.com/nodejs/node/commit/1fece2f8c0)] - **build**: re-add --ninja option to configure (Ehsan Akhgari) [#6780](https://github.com/nodejs/node/pull/6780)
- [[`e7b03be191`](https://github.com/nodejs/node/commit/e7b03be191)] - **build**: update build-addons when node-gyp changes (Lance Ball) [#6787](https://github.com/nodejs/node/pull/6787)
- [[`55c0b3e0e5`](https://github.com/nodejs/node/commit/55c0b3e0e5)] - **build**: unbreak configure with python 2.6 (Ben Noordhuis) [#6874](https://github.com/nodejs/node/pull/6874)
- [[`0503681348`](https://github.com/nodejs/node/commit/0503681348)] - **child_process**: measure buffer length in bytes (Rich Trott) [#6764](https://github.com/nodejs/node/pull/6764)
- [[`27d0eb054c`](https://github.com/nodejs/node/commit/27d0eb054c)] - **child_process**: emit IPC messages on next tick (cjihrig) [#6909](https://github.com/nodejs/node/pull/6909)
- [[`b28468e0a7`](https://github.com/nodejs/node/commit/b28468e0a7)] - **child_process**: allow buffer encoding in spawnSync (cjihrig) [#6939](https://github.com/nodejs/node/pull/6939)
- [[`6a62bb0070`](https://github.com/nodejs/node/commit/6a62bb0070)] - **cluster**: expose result of send() (cjihrig) [#6998](https://github.com/nodejs/node/pull/6998)
- [[`2132d349b5`](https://github.com/nodejs/node/commit/2132d349b5)] - **cluster**: rewrite debug ports consistently (cjihrig) [#7050](https://github.com/nodejs/node/pull/7050)
- [[`0bd8f4c4d8`](https://github.com/nodejs/node/commit/0bd8f4c4d8)] - **cluster**: reset handle index on close (Santiago Gimeno) [#6981](https://github.com/nodejs/node/pull/6981)
- [[`93e150f1d6`](https://github.com/nodejs/node/commit/93e150f1d6)] - **cluster**: guard against undefined message handlers (cjihrig) [#6902](https://github.com/nodejs/node/pull/6902)
- [[`28b73428e1`](https://github.com/nodejs/node/commit/28b73428e1)] - **cluster**: close ownerless handles on disconnect() (cjihrig) [#6909](https://github.com/nodejs/node/pull/6909)
- [[`2184e772d2`](https://github.com/nodejs/node/commit/2184e772d2)] - **debugger**: propagate --debug-port= to debuggee (Ben Noordhuis) [#3470](https://github.com/nodejs/node/pull/3470)
- [[`ded02b909f`](https://github.com/nodejs/node/commit/ded02b909f)] - **deps**: upgrade npm to 3.9.3 (Kat Marchán) [#7030](https://github.com/nodejs/node/pull/7030)
- [[`bfd7b24c63`](https://github.com/nodejs/node/commit/bfd7b24c63)] - **deps**: upgrade to V8 ********* (Michaël Zasso) [#6928](https://github.com/nodejs/node/pull/6928)
- [[`8e6f8b2fb5`](https://github.com/nodejs/node/commit/8e6f8b2fb5)] - **dgram**: copy the list in send (Matteo Collina) [#6804](https://github.com/nodejs/node/pull/6804)
- [[`588c76cd5c`](https://github.com/nodejs/node/commit/588c76cd5c)] - **dgram,test**: add addMembership/dropMembership tests (Rich Trott) [#6753](https://github.com/nodejs/node/pull/6753)
- [[`e93198e86d`](https://github.com/nodejs/node/commit/e93198e86d)] - **doc**: edit pull request template (Rich Trott) [#7058](https://github.com/nodejs/node/pull/7058)
- [[`1c1256718e`](https://github.com/nodejs/node/commit/1c1256718e)] - **doc**: addresses nits in string_decoder, url, util (Jeremiah Senkpiel) [#7026](https://github.com/nodejs/node/pull/7026)
- [[`14b3ba35fc`](https://github.com/nodejs/node/commit/14b3ba35fc)] - **doc**: improve debugger doc prose (Rich Trott) [#7007](https://github.com/nodejs/node/pull/7007)
- [[`3c2c4c8d5c`](https://github.com/nodejs/node/commit/3c2c4c8d5c)] - **doc**: update labels and CI info in onboarding doc (Rich Trott) [#7006](https://github.com/nodejs/node/pull/7006)
- [[`b5e93c97f8`](https://github.com/nodejs/node/commit/b5e93c97f8)] - **doc**: fix typos in WORKING_GROUPS.md (Joao Andrade) [#7032](https://github.com/nodejs/node/pull/7032)
- [[`f15448681a`](https://github.com/nodejs/node/commit/f15448681a)] - **doc**: buffers are not sent over IPC with a socket (Tim Kuijsten) [#6951](https://github.com/nodejs/node/pull/6951)
- [[`3518ab93b1`](https://github.com/nodejs/node/commit/3518ab93b1)] - **doc**: minor improvements to util.md (Sakthipriyan Vairamani) [#6932](https://github.com/nodejs/node/pull/6932)
- [[`216a3cdcce`](https://github.com/nodejs/node/commit/216a3cdcce)] - **doc**: add `added:` information for vm (Anna Henningsen) [#7011](https://github.com/nodejs/node/pull/7011)
- [[`b30d07845d`](https://github.com/nodejs/node/commit/b30d07845d)] - **doc**: add `added:` information for console (Adrian Estrada) [#6995](https://github.com/nodejs/node/pull/6995)
- [[`72d4692e94`](https://github.com/nodejs/node/commit/72d4692e94)] - **doc**: add info on what's used for fswatch on AIX (Michael Dawson) [#6837](https://github.com/nodejs/node/pull/6837)
- [[`7c38327dee`](https://github.com/nodejs/node/commit/7c38327dee)] - **doc**: update process.hrtime docs to include optional parameter (doug.wade) [#6585](https://github.com/nodejs/node/pull/6585)
- [[`0f17a28a00`](https://github.com/nodejs/node/commit/0f17a28a00)] - **doc**: improve server.listen() documentation prose (Rich Trott) [#7000](https://github.com/nodejs/node/pull/7000)
- [[`3ae9f1469d`](https://github.com/nodejs/node/commit/3ae9f1469d)] - **doc**: improve `server.address()` doc text (Rich Trott) [#7001](https://github.com/nodejs/node/pull/7001)
- [[`ae1bf83b6c`](https://github.com/nodejs/node/commit/ae1bf83b6c)] - **doc**: clarified use of sexual language in the CoC (Bryan Hughes) [#6973](https://github.com/nodejs/node/pull/6973)
- [[`3909209e7a`](https://github.com/nodejs/node/commit/3909209e7a)] - **doc**: general improvements to tty.md (James M Snell) [#6931](https://github.com/nodejs/node/pull/6931)
- [[`bc2efe22f6`](https://github.com/nodejs/node/commit/bc2efe22f6)] - **doc**: add `added:` data for cli.md (Rich Trott) [#6960](https://github.com/nodejs/node/pull/6960)
- [[`856638d0b7`](https://github.com/nodejs/node/commit/856638d0b7)] - **doc**: add `added:` information for child_process (Anna Henningsen) [#6927](https://github.com/nodejs/node/pull/6927)
- [[`a5e3eddfbf`](https://github.com/nodejs/node/commit/a5e3eddfbf)] - **doc**: general improvements to url.md copy (James M Snell) [#6904](https://github.com/nodejs/node/pull/6904)
- [[`b7ca0a2653`](https://github.com/nodejs/node/commit/b7ca0a2653)] - **doc**: add yorkie to collaborators (Yazhong Liu) [#7004](https://github.com/nodejs/node/pull/7004)
- [[`a9b90a6952`](https://github.com/nodejs/node/commit/a9b90a6952)] - **doc**: general improvements to tls.md copy (James M Snell) [#6933](https://github.com/nodejs/node/pull/6933)
- [[`5990a7fe91`](https://github.com/nodejs/node/commit/5990a7fe91)] - **doc**: fix broken references (Anna Henningsen) [#6941](https://github.com/nodejs/node/pull/6941)
- [[`98e497bdad`](https://github.com/nodejs/node/commit/98e497bdad)] - **doc**: fix broken references in changelogs (Anna Henningsen) [#6942](https://github.com/nodejs/node/pull/6942)
- [[`f3ae42168a`](https://github.com/nodejs/node/commit/f3ae42168a)] - **doc**: general improvements to string_decoder.md copy (James M Snell) [#6940](https://github.com/nodejs/node/pull/6940)
- [[`8f623a3c75`](https://github.com/nodejs/node/commit/8f623a3c75)] - **doc**: add firedfox to collaborators (Daniel Wang) [#6961](https://github.com/nodejs/node/pull/6961)
- [[`145a6b946a`](https://github.com/nodejs/node/commit/145a6b946a)] - **doc**: add bmeck to collaborators (Bradley Meck) [#6962](https://github.com/nodejs/node/pull/6962)
- [[`95f8d59e0d`](https://github.com/nodejs/node/commit/95f8d59e0d)] - **doc**: remove "\" within backticks (Rod Machen) [#6952](https://github.com/nodejs/node/pull/6952)
- [[`ee1865dd2f`](https://github.com/nodejs/node/commit/ee1865dd2f)] - **doc**: clarify buffer class (Steve Mao) [#6914](https://github.com/nodejs/node/pull/6914)
- [[`db3d2a7b96`](https://github.com/nodejs/node/commit/db3d2a7b96)] - **doc**: fix typos in timers topic to aid readability (Kevin Donahue) [#6916](https://github.com/nodejs/node/pull/6916)
- [[`0f0003fc54`](https://github.com/nodejs/node/commit/0f0003fc54)] - **doc**: add jhamhader to collaborators (Yuval Brik) [#6946](https://github.com/nodejs/node/pull/6946)
- [[`f6558ec537`](https://github.com/nodejs/node/commit/f6558ec537)] - **doc**: add @othiym23 to list of collaborators (Forrest L Norvell) [#6945](https://github.com/nodejs/node/pull/6945)
- [[`9fa1b19eda`](https://github.com/nodejs/node/commit/9fa1b19eda)] - **doc**: reference list of language-specific globals (Anna Henningsen) [#6900](https://github.com/nodejs/node/pull/6900)
- [[`15f6224418`](https://github.com/nodejs/node/commit/15f6224418)] - **doc**: make doc-only -> fallback to user binary (Robert Jefe Lindstaedt) [#6906](https://github.com/nodejs/node/pull/6906)
- [[`a320a019f1`](https://github.com/nodejs/node/commit/a320a019f1)] - **doc**: general improvements to util.md (James M Snell) [#6897](https://github.com/nodejs/node/pull/6897)
- [[`527a8a4844`](https://github.com/nodejs/node/commit/527a8a4844)] - **doc**: add `added:` information for zlib (Anna Henningsen) [#6840](https://github.com/nodejs/node/pull/6840)
- [[`cb8de85100`](https://github.com/nodejs/node/commit/cb8de85100)] - **doc**: make the api doc print-friendly (Marian) [#6748](https://github.com/nodejs/node/pull/6748)
- [[`f1a8c3164a`](https://github.com/nodejs/node/commit/f1a8c3164a)] - **doc**: add bengl to collaborators (Bryan English) [#6921](https://github.com/nodejs/node/pull/6921)
- [[`565d4ca12e`](https://github.com/nodejs/node/commit/565d4ca12e)] - **doc**: Update DCO to v1.1 (William Kapke) [#6353](https://github.com/nodejs/node/pull/6353)
- [[`eff73c78c5`](https://github.com/nodejs/node/commit/eff73c78c5)] - **doc**: general improvements to v8.md copy (James M Snell) [#6829](https://github.com/nodejs/node/pull/6829)
- [[`e5a7cec828`](https://github.com/nodejs/node/commit/e5a7cec828)] - **doc**: fix typo in Error.captureStackTrace (Mohsen) [#6811](https://github.com/nodejs/node/pull/6811)
- [[`5afb91bef7`](https://github.com/nodejs/node/commit/5afb91bef7)] - **doc**: make param names consistent & fix doc link (Sakthipriyan Vairamani) [#6832](https://github.com/nodejs/node/pull/6832)
- [[`e1fb4805cf`](https://github.com/nodejs/node/commit/e1fb4805cf)] - **doc**: add `added:` info for `process.cpuUsage` (Anna Henningsen) [#6863](https://github.com/nodejs/node/pull/6863)
- [[`8a0329f110`](https://github.com/nodejs/node/commit/8a0329f110)] - **doc**: fix mkdtemp example by removing hyphen (Sakthipriyan Vairamani) [#6834](https://github.com/nodejs/node/pull/6834)
- [[`45ca7cfcdd`](https://github.com/nodejs/node/commit/45ca7cfcdd)] - **doc**: reduce GitHub template verbosity (Rich Trott) [#6801](https://github.com/nodejs/node/pull/6801)
- [[`12a3d0120b`](https://github.com/nodejs/node/commit/12a3d0120b)] - **doc**: improve vm.md copy (James M Snell) [#6827](https://github.com/nodejs/node/pull/6827)
- [[`0ae512a3cf`](https://github.com/nodejs/node/commit/0ae512a3cf)] - **doc**: Add resolveNaptr and naptr rrtype docs (Doug Wade) [#6586](https://github.com/nodejs/node/pull/6586)
- [[`8309dbaf9c`](https://github.com/nodejs/node/commit/8309dbaf9c)] - **doc**: fix name to match git log (Robert Jefe Lindstaedt) [#6880](https://github.com/nodejs/node/pull/6880)
- [[`b52d838f0d`](https://github.com/nodejs/node/commit/b52d838f0d)] - **doc**: add note for fs.watch virtualized env (Robert Jefe Lindstaedt) [#6809](https://github.com/nodejs/node/pull/6809)
- [[`08f1361cb6`](https://github.com/nodejs/node/commit/08f1361cb6)] - **doc**: add `added:` information for punycode (Daniel Wang) [#6805](https://github.com/nodejs/node/pull/6805)
- [[`253db33527`](https://github.com/nodejs/node/commit/253db33527)] - **doc**: add `added:` info for dgram.\*Membership() (Rich Trott) [#6753](https://github.com/nodejs/node/pull/6753)
- [[`5cad04b063`](https://github.com/nodejs/node/commit/5cad04b063)] - **doc**: clarify fs.mkdtemp prefix argument (James M Snell) [#6800](https://github.com/nodejs/node/pull/6800)
- [[`5a1e823fa5`](https://github.com/nodejs/node/commit/5a1e823fa5)] - **doc**: add `added:` information for fs (Anna Henningsen) [#6717](https://github.com/nodejs/node/pull/6717)
- [[`bf4724a7bb`](https://github.com/nodejs/node/commit/bf4724a7bb)] - **doc**: remove link to Sign in crypto.md (Kirill Fomichev) [#6812](https://github.com/nodejs/node/pull/6812)
- [[`ba3089970d`](https://github.com/nodejs/node/commit/ba3089970d)] - **doc**: add `added:` in for `tty` (Rich Trott) [#6783](https://github.com/nodejs/node/pull/6783)
- [[`758fadfa0d`](https://github.com/nodejs/node/commit/758fadfa0d)] - **doc**: update openssl.org hash links (silverwind) [#6817](https://github.com/nodejs/node/pull/6817)
- [[`b2c7d466d4`](https://github.com/nodejs/node/commit/b2c7d466d4)] - **doc,test**: add `How to write a Node.js test` guide (Santiago Gimeno) [#6984](https://github.com/nodejs/node/pull/6984)
- [[`c4329aa226`](https://github.com/nodejs/node/commit/c4329aa226)] - **fs**: move mkdtemp\* functions near static functions (Sakthipriyan Vairamani) [#6828](https://github.com/nodejs/node/pull/6828)
- [[`c068880757`](https://github.com/nodejs/node/commit/c068880757)] - **fs**: mkdtemp shouldn't crash if no callback passed (Sakthipriyan Vairamani) [#6828](https://github.com/nodejs/node/pull/6828)
- [[`2ab36093e6`](https://github.com/nodejs/node/commit/2ab36093e6)] - **http**: use `localAddress` instead of `path` (Dirceu Pereira Tiegs) [#5190](https://github.com/nodejs/node/pull/5190)
- [[`6f0d8b3a1b`](https://github.com/nodejs/node/commit/6f0d8b3a1b)] - **installer**: don't install node_internals.h (Ben Noordhuis) [#6913](https://github.com/nodejs/node/pull/6913)
- [[`178f3080f8`](https://github.com/nodejs/node/commit/178f3080f8)] - **module**: don't cache uninitialized builtins (Anna Henningsen) [#6907](https://github.com/nodejs/node/pull/6907)
- [[`1908b7f00a`](https://github.com/nodejs/node/commit/1908b7f00a)] - **path**: fix basename() regressions (Brian White) [#6590](https://github.com/nodejs/node/pull/6590)
- [[`10671406ac`](https://github.com/nodejs/node/commit/10671406ac)] - **process**: internal/process/stdio.js cleanup / modernization (James M Snell) [#6766](https://github.com/nodejs/node/pull/6766)
- [[`64445674f0`](https://github.com/nodejs/node/commit/64445674f0)] - **src**: add include guards to internal headers (Ben Noordhuis) [#6948](https://github.com/nodejs/node/pull/6948)
- [[`4333fda46d`](https://github.com/nodejs/node/commit/4333fda46d)] - **src**: no abort from getter if object isn't wrapped (Trevor Norris) [#6184](https://github.com/nodejs/node/pull/6184)
- [[`4da3e1e461`](https://github.com/nodejs/node/commit/4da3e1e461)] - **src**: always clear wrap before persistent Reset() (Trevor Norris) [#6184](https://github.com/nodejs/node/pull/6184)
- [[`7e5775704e`](https://github.com/nodejs/node/commit/7e5775704e)] - **src**: inherit first from AsyncWrap (Trevor Norris) [#6184](https://github.com/nodejs/node/pull/6184)
- [[`0841496992`](https://github.com/nodejs/node/commit/0841496992)] - **src**: fix without-intl build (Anna Henningsen) [#6820](https://github.com/nodejs/node/pull/6820)
- [[`0d08fc415f`](https://github.com/nodejs/node/commit/0d08fc415f)] - **stream_base**: always use Base template class (Trevor Norris) [#6184](https://github.com/nodejs/node/pull/6184)
- [[`756ec80d50`](https://github.com/nodejs/node/commit/756ec80d50)] - **string_bytes**: Make base64 encode/decode reusable (Eugene Ostroukhov) [#6910](https://github.com/nodejs/node/pull/6910)
- [[`79ad172589`](https://github.com/nodejs/node/commit/79ad172589)] - **string_decoder**: rewrite implementation (Brian White) [#6777](https://github.com/nodejs/node/pull/6777)
- [[`8b720c4582`](https://github.com/nodejs/node/commit/8b720c4582)] - **test**: remove non-incremental common.PORT changes (Rich Trott) [#7055](https://github.com/nodejs/node/pull/7055)
- [[`6439fbfac0`](https://github.com/nodejs/node/commit/6439fbfac0)] - **test**: test TTY problems by fakeing a TTY using openpty (Jeremiah Senkpiel) [#6895](https://github.com/nodejs/node/pull/6895)
- [[`81a9f96a29`](https://github.com/nodejs/node/commit/81a9f96a29)] - **test**: make test-child-process-fork-net more robust (Rich Trott) [#7033](https://github.com/nodejs/node/pull/7033)
- [[`6cf0f622ef`](https://github.com/nodejs/node/commit/6cf0f622ef)] - **test**: fix spurious EADDRINUSE in test-https-strict (Rich Trott) [#7024](https://github.com/nodejs/node/pull/7024)
- [[`dea120f247`](https://github.com/nodejs/node/commit/dea120f247)] - **test**: update weak module for gc tests (Rich Trott) [#7014](https://github.com/nodejs/node/pull/7014)
- [[`3bfbe8a62a`](https://github.com/nodejs/node/commit/3bfbe8a62a)] - **test**: remove `common.PORT` from gc tests (Rich Trott) [#7013](https://github.com/nodejs/node/pull/7013)
- [[`b23cd48ca0`](https://github.com/nodejs/node/commit/b23cd48ca0)] - **test**: fix test-debug-port-numbers on OS X (Santiago Gimeno) [#7046](https://github.com/nodejs/node/pull/7046)
- [[`0a258e5369`](https://github.com/nodejs/node/commit/0a258e5369)] - **test**: remove modifcation to common.PORT (Rich Trott) [#6990](https://github.com/nodejs/node/pull/6990)
- [[`8c289df175`](https://github.com/nodejs/node/commit/8c289df175)] - **test**: use strictEqual consistently in agent test (Ben Noordhuis) [#6654](https://github.com/nodejs/node/pull/6654)
- [[`e4ac808c4d`](https://github.com/nodejs/node/commit/e4ac808c4d)] - **test**: work around debugger not killing inferior (Ben Noordhuis) [#7037](https://github.com/nodejs/node/pull/7037)
- [[`b5949f8bbc`](https://github.com/nodejs/node/commit/b5949f8bbc)] - **test**: verify cluster worker exit (cjihrig) [#6993](https://github.com/nodejs/node/pull/6993)
- [[`6f3f5af396`](https://github.com/nodejs/node/commit/6f3f5af396)] - **test**: add regression test for Proxy as vm context (Michaël Zasso) [#6967](https://github.com/nodejs/node/pull/6967)
- [[`38a3323cc9`](https://github.com/nodejs/node/commit/38a3323cc9)] - **test**: improve debug-break-on-uncaught reliability (Rich Trott) [#6793](https://github.com/nodejs/node/pull/6793)
- [[`83e6d53817`](https://github.com/nodejs/node/commit/83e6d53817)] - **test**: test cluster worker disconnection on error (Santiago Gimeno) [#6909](https://github.com/nodejs/node/pull/6909)
- [[`4cc6a18448`](https://github.com/nodejs/node/commit/4cc6a18448)] - **test**: verify IPC messages are emitted on next tick (Santiago Gimeno) [#6909](https://github.com/nodejs/node/pull/6909)
- [[`69e119dbfb`](https://github.com/nodejs/node/commit/69e119dbfb)] - **test**: refactor spawnSync() cwd test (cjihrig) [#6939](https://github.com/nodejs/node/pull/6939)
- [[`32cc43a1bd`](https://github.com/nodejs/node/commit/32cc43a1bd)] - **test**: fix component printing on windows (Ben Noordhuis) [#6915](https://github.com/nodejs/node/pull/6915)
- [[`c81b6f8d0d`](https://github.com/nodejs/node/commit/c81b6f8d0d)] - **test**: refactor to eliminate \_\_defineGetter\_\_ (Rich Trott) [#6774](https://github.com/nodejs/node/pull/6774)
- [[`1965e445ec`](https://github.com/nodejs/node/commit/1965e445ec)] - **test**: refactor test-tls-reuse-host-from-socket (Rich Trott) [#6756](https://github.com/nodejs/node/pull/6756)
- [[`2cf3a53ce1`](https://github.com/nodejs/node/commit/2cf3a53ce1)] - **test**: fix test-debug-port-cluster flakiness (Rich Trott) [#6769](https://github.com/nodejs/node/pull/6769)
- [[`5374afdef8`](https://github.com/nodejs/node/commit/5374afdef8)] - **test**: add logging for test-debug-port-cluster (Rich Trott) [#6769](https://github.com/nodejs/node/pull/6769)
- [[`bae7adb6fa`](https://github.com/nodejs/node/commit/bae7adb6fa)] - **test**: fix flaky test-stdout-close-catch (Santiago Gimeno) [#6808](https://github.com/nodejs/node/pull/6808)
- [[`528ca04e8d`](https://github.com/nodejs/node/commit/528ca04e8d)] - **test**: add more path.basename() tests (Brian White) [#6590](https://github.com/nodejs/node/pull/6590)
- [[`1469b98fa1`](https://github.com/nodejs/node/commit/1469b98fa1)] - **test**: remove duplicate path tests (Brian White) [#6590](https://github.com/nodejs/node/pull/6590)
- [[`81e765f521`](https://github.com/nodejs/node/commit/81e765f521)] - **test**: robust handling of env for npm-test-install (Myles Borins) [#6797](https://github.com/nodejs/node/pull/6797)
- [[`2895860138`](https://github.com/nodejs/node/commit/2895860138)] - **test**: cluster-setup-master online workers check (Devon Rifkin) [#6535](https://github.com/nodejs/node/pull/6535)
- [[`7c932c2d49`](https://github.com/nodejs/node/commit/7c932c2d49)] - **test**: added tests for https-agent-getname (suryagh) [#6762](https://github.com/nodejs/node/pull/6762)
- [[`827b3eb503`](https://github.com/nodejs/node/commit/827b3eb503)] - **test**: add --repeat option to tools/test.py (Michael Dawson) [#6700](https://github.com/nodejs/node/pull/6700)
- [[`ea287fc1a6`](https://github.com/nodejs/node/commit/ea287fc1a6)] - **test,win**: skip addons/load-long-path on WOW64 (Alexis Campailla) [#6675](https://github.com/nodejs/node/pull/6675)
- [[`21e31352d7`](https://github.com/nodejs/node/commit/21e31352d7)] - **tls**: catch `certCbDone` exceptions (Fedor Indutny) [#6887](https://github.com/nodejs/node/pull/6887)
- [[`257e54b9c0`](https://github.com/nodejs/node/commit/257e54b9c0)] - **tls,https**: respect address family when connecting (Ben Noordhuis) [#6654](https://github.com/nodejs/node/pull/6654)
- [[`5779ed2a4a`](https://github.com/nodejs/node/commit/5779ed2a4a)] - **tls_wrap**: do not abort on new TLSWrap() (Trevor Norris) [#6184](https://github.com/nodejs/node/pull/6184)
- [[`108523e06e`](https://github.com/nodejs/node/commit/108523e06e)] - **tools**: make sure doctool anchors respect includes (Anna Henningsen) [#6943](https://github.com/nodejs/node/pull/6943)
- [[`bf3afce668`](https://github.com/nodejs/node/commit/bf3afce668)] - **tools**: restore change of signatures to opts hashes (Jesse McCarthy) [#6690](https://github.com/nodejs/node/pull/6690)
- [[`ceee56b28b`](https://github.com/nodejs/node/commit/ceee56b28b)] - **tools**: disallow deprecated define getter/setter (Rich Trott) [#6774](https://github.com/nodejs/node/pull/6774)
- [[`614907e516`](https://github.com/nodejs/node/commit/614907e516)] - **tools**: print stderr on bad test.py `vmArch` check (Jeremiah Senkpiel) [#6786](https://github.com/nodejs/node/pull/6786)
- [[`4d3a7594a5`](https://github.com/nodejs/node/commit/4d3a7594a5)] - **tty**: use blocking mode on OS X (Jeremiah Senkpiel) [#6895](https://github.com/nodejs/node/pull/6895)
- [[`36ed4a2d7a`](https://github.com/nodejs/node/commit/36ed4a2d7a)] - **udp**: use libuv API to get file descriptor (Saúl Ibarra Corretgé) [#6908](https://github.com/nodejs/node/pull/6908)
- [[`f3e3eebec8`](https://github.com/nodejs/node/commit/f3e3eebec8)] - **unix,stream**: fix getting the correct fd for a handle (Saúl Ibarra Corretgé) [#6753](https://github.com/nodejs/node/pull/6753)
- [[`d270706881`](https://github.com/nodejs/node/commit/d270706881)] - **util**: pretty-print SIMD types (Ben Noordhuis) [#6917](https://github.com/nodejs/node/pull/6917)
- [[`55b736a63b`](https://github.com/nodejs/node/commit/55b736a63b)] - **vm**: don't abort process when stack space runs out (Anna Henningsen) [#6907](https://github.com/nodejs/node/pull/6907)
- [[`cb2ef35b76`](https://github.com/nodejs/node/commit/cb2ef35b76)] - **win,build**: add creation of zip and 7z package (Bartosz Sosnowski) [#5995](https://github.com/nodejs/node/pull/5995)
- [[`1e26b82ce4`](https://github.com/nodejs/node/commit/1e26b82ce4)] - **zlib**: release callback and buffer after processing (Matt Lavin) [#6955](https://github.com/nodejs/node/pull/6955)
- [[`64415564de`](https://github.com/nodejs/node/commit/64415564de)] - **zlib**: remove `_closed` in source (Anna Henningsen) [#6574](https://github.com/nodejs/node/pull/6574)

Windows 32-bit Installer: https://nodejs.org/dist/v6.2.1/node-v6.2.1-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v6.2.1/node-v6.2.1-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v6.2.1/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v6.2.1/win-x64/node.exe \
Mac OS X 64-bit Installer: https://nodejs.org/dist/v6.2.1/node-v6.2.1.pkg \
Mac OS X 64-bit Binary: https://nodejs.org/dist/v6.2.1/node-v6.2.1-darwin-x64.tar.gz \
Linux 32-bit Binary: https://nodejs.org/dist/v6.2.1/node-v6.2.1-linux-x86.tar.xz \
Linux 64-bit Binary: https://nodejs.org/dist/v6.2.1/node-v6.2.1-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v6.2.1/node-v6.2.1-linux-ppc64le.tar.xz \
SmartOS 32-bit Binary: https://nodejs.org/dist/v6.2.1/node-v6.2.1-sunos-x86.tar.xz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v6.2.1/node-v6.2.1-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v6.2.1/node-v6.2.1-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v6.2.1/node-v6.2.1-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v6.2.1/node-v6.2.1-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v6.2.1/node-v6.2.1.tar.gz \
Other release files: https://nodejs.org/dist/v6.2.1/ \
Documentation: https://nodejs.org/docs/v6.2.1/api/

Shasums (GPG signing hash: SHA512, file hash: SHA256):

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA1

a6e99c2e482133b0f2db6bb8284ef79ea60c554e8dbfc24a1a691671cc5d0517  node-v6.2.1-darwin-x64.tar.gz
5ccbac97e3e38d9e8531552f670ae62a95c8561e74b7bd218ad92215fa783f06  node-v6.2.1-darwin-x64.tar.xz
f55aa8ea6de59e074c4f9d5ab65b77e68bcf607971be05ff31b88acb91714f4f  node-v6.2.1-headers.tar.gz
6c06cc1cbfbcc417acfd83a1a7406f4894e76bba5a0f1dd221fba765fdadee3a  node-v6.2.1-headers.tar.xz
86f7498f0c355e8f8c17cecaf7e0bd6f68e189e00e0adb6700560e291edce08d  node-v6.2.1-linux-arm64.tar.gz
040f7f1f31a0ffede00733cffabb5a2e66931345bc3d9cedb1af683304890b31  node-v6.2.1-linux-arm64.tar.xz
f791c57f336fc67c7231c9c54799b4e39427a2de99decf47898d57353b0fb64b  node-v6.2.1-linux-armv6l.tar.gz
1cb17dbfb405e750ae323346de5263bd2be4bf30b1211de7ffb07512332558a9  node-v6.2.1-linux-armv6l.tar.xz
01ac0cbae90a88f73b016db7e8052d05f7ab7083d5a14b6b10067a41eea538ff  node-v6.2.1-linux-armv7l.tar.gz
aac349e02d41eb9e1205af96e42d9a593ba4827553aa1db988cabaf0041fb08a  node-v6.2.1-linux-armv7l.tar.xz
2ca673b76add031e6244b36e2cd3c1da1982e21aec84eeda24bcbc1b77772ab2  node-v6.2.1-linux-ppc64le.tar.gz
f8dfe167d4a43adb05a9a14eb96a7081189dd2036e259972fd1fc643b148a1e8  node-v6.2.1-linux-ppc64le.tar.xz
4fa09f89af1b835caf4be73ca4c7a02a1bc640c36c4ec512dbdb0827315003e9  node-v6.2.1-linux-ppc64.tar.gz
6bb127999105e9eb2f4958b0be170aea38951f49a3fa7efacdea344e7b3111b6  node-v6.2.1-linux-ppc64.tar.xz
c6ae9c90858fb47a2915ad6494e5eb8e6f34f4512de1d5a461e5fb0c003590b1  node-v6.2.1-linux-x64.tar.gz
1027de8bd78ef2cba2baf5a676d54f185f9632324465cfd8572c3568ca6e1f64  node-v6.2.1-linux-x64.tar.xz
f5656c288c1dfc760a23000d8dc1eb3d0ff484a7798e5f28718a728ae96e9125  node-v6.2.1-linux-x86.tar.gz
9d46df7c687f212107a39a667fb6b0da9734c71c3513ff8db21f521a033270f1  node-v6.2.1-linux-x86.tar.xz
dec8054fdf3266b912842cb3307a3b5e6cc34bf00f2d3bd19a5b71fb4d9a3290  node-v6.2.1.pkg
c7e931670bca8055ed6d090bab276bee5c2db570e74c0828f64a2b5c6dc9e973  node-v6.2.1-sunos-x64.tar.gz
550c162ba7dd4d7b1e8dd69f98887e54ebfc79a5fe5ba79308b2166408a4a8a6  node-v6.2.1-sunos-x64.tar.xz
39ac16d0b200a29261767d6aa99d1a9da343e7c4b629ce332f7d6b9fcfb1767a  node-v6.2.1-sunos-x86.tar.gz
7fc08597de9b69ba8b38007b0df896e8776d138dc7e4716ce09c325cf8df4aff  node-v6.2.1-sunos-x86.tar.xz
fa26d4380150fbb69a48bddaec6143e176effe043cfe44e512c5ef9d22618b63  node-v6.2.1.tar.gz
dbaeb8fb68a599e5164b17c74f66d24f424ee4ab3a25d8de8a3c6808e5b42bfb  node-v6.2.1.tar.xz
217631ccc76fb869ea9a82e8c12eebbe67c66f877bf98d913bf8f19d1dae56f1  node-v6.2.1-win-x64.7z
90e93905b52131a84e4c3488177a0bf060a4c3e5633c1f567d580fd9141d2848  node-v6.2.1-win-x64.zip
4103f49aea62d6b2e7e022493c61af93c401404c5497ddd77f0e3787e0e8bce7  node-v6.2.1-win-x86.7z
967dd36e2eec59c4e32e7cc3bf565f589080ac1be0cc877ee8aa1240fbb9b7d7  node-v6.2.1-win-x86.zip
528b7c2cb707e81df4d5e53ea3b8d8c32e71ab3ba7e2ee4993a4f44da6e353ba  node-v6.2.1-x64.msi
8095a7c506ed1ada1599942ae2a8cb60909def15bc4d97c7dca51c3d02f046ec  node-v6.2.1-x86.msi
1fea4396837da41f363312d3ee7cbc34ba23edfb24e39c6aa6ced732cdea7878  win-x64/node.exe
0dca5ad3d2ec6e4eed2f370c812c94fc1f5d5434e44a2b6e970a133d9be5bf37  win-x64/node.lib
4403d0dd9702907eb2ad8d49b0c7f2af5f5b7d263758bf1e79fe89008f523012  win-x64/node_pdb.7z
03201f05e0f3f455116f29b03bc7a930d0c4b0c593327944eceb44454f746335  win-x64/node_pdb.zip
e5d6e2800e05c376f0b13fccf4ea5f45dbc890b13221401fe7ae3bbcf77a9635  win-x86/node.exe
c7312d02d0b98cbc7700cc22d47f40d4c2284e96410216484d606057936dbad4  win-x86/node.lib
3e777fe925cb48917cb9748bdff62fb0e8570385572cdcfd9cf5cb68dfcdb784  win-x86/node_pdb.7z
0b6edd7afb0ea3024a12c55923df448726e99459a9be44d418e7d1d7ad5ab2cd  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----
Version: GnuPG v1

iQEcBAEBAgAGBQJXULUeAAoJEMJzeS99g1Rd36wH/3pDBdJVZVgARpEOfJHAWUx7
Rhma9WpukZ1K7d/5c98jdvgErIUM/aQYx2qKUvDLBHUHdRjQM7uuCHu7bm4AMiYb
9ugqsAxLqAHKtOzBWaY6SEAtnG7nHUsKsmeVos35Y9VjlGsOymL5VD13Wjv4o/uX
kL9fFroCv2QJ6Y/ho9PW6t1t4SmvtOpHr4vvtu3SFEnz/Ov9+SAkRHEXPLZZKfkI
Ul2U2Pq9gG9heLTJMUKmaUFnULA1vejtIfy1fDZpBIxbgSaBLjoVQbQjnnpuF+2r
BeMzhURkxye584k7noOZbEKIFSM1h6DqJ4fjWjSkJZKnaJ6/WuymInsM3uT3x2I=
=kvV9
-----END PGP SIGNATURE-----

```
