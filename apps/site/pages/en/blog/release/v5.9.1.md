---
date: '2016-03-23T17:40:02.432Z'
category: release
title: Node v5.9.1 (Current)
layout: blog-post
author: <PERSON>
---

### Notable changes

- **buffer**: Now properly throws `RangeError`s on out-of-bounds writes (<PERSON>) [#5605](https://github.com/nodejs/node/pull/5605).
  - This effects `write{Float|Double}` when the `noAssert` option is not used.
- **timers**:
  - Returned timeout objects now have a `Timeout` constructor name (<PERSON>) [#5793](https://github.com/nodejs/node/pull/5793).
  - Performance of `Immediate` processing is now ~20-40% faster (<PERSON>) [#4169](https://github.com/nodejs/node/pull/4169).
- **vm**: Fixed a contextify regression introduced in v5.9.0 (<PERSON>) [#5800](https://github.com/nodejs/node/pull/5800).

### Commits

- [[`341b3d01c8`](https://github.com/nodejs/node/commit/341b3d01c8)] - **benchmark**: fix linting errors (<PERSON>) [#5840](https://github.com/nodejs/node/pull/5840)
- [[`72fb796bed`](https://github.com/nodejs/node/commit/72fb796bed)] - **buffer**: throw range error before truncating write (Matt Loring) [#5605](https://github.com/nodejs/node/pull/5605)
- [[`c5d83695e1`](https://github.com/nodejs/node/commit/c5d83695e1)] - **contextify**: tie lifetimes of context & sandbox (Ali Ijaz Sheikh) [#5800](https://github.com/nodejs/node/pull/5800)
- [[`ae24d05451`](https://github.com/nodejs/node/commit/ae24d05451)] - **deps**: remove unused openssl files (Ben Noordhuis) [#5619](https://github.com/nodejs/node/pull/5619)
- [[`54abbe7e6f`](https://github.com/nodejs/node/commit/54abbe7e6f)] - **dns**: use template literals (Benjamin Gruenbaum) [#5809](https://github.com/nodejs/node/pull/5809)
- [[`3fef69bf15`](https://github.com/nodejs/node/commit/3fef69bf15)] - **dns**: use isIp consistently (Benjamin Gruenbaum) [#5804](https://github.com/nodejs/node/pull/5804)
- [[`d2d0fe9d34`](https://github.com/nodejs/node/commit/d2d0fe9d34)] - **doc**: update crypto docs to use good defaults (Bill Automata) [#5505](https://github.com/nodejs/node/pull/5505)
- [[`1631f06477`](https://github.com/nodejs/node/commit/1631f06477)] - **doc**: add CTC meeting minutes 2016-02-10 (Rod Vagg) [#5273](https://github.com/nodejs/node/pull/5273)
- [[`7ab597d646`](https://github.com/nodejs/node/commit/7ab597d646)] - **doc**: add CTC meeting minutes 2016-02-03 (Rod Vagg) [#5272](https://github.com/nodejs/node/pull/5272)
- [[`e20d0b8802`](https://github.com/nodejs/node/commit/e20d0b8802)] - **doc**: explain error message on missing main file (Wolfgang Steiner) [#5812](https://github.com/nodejs/node/pull/5812)
- [[`e99082e32d`](https://github.com/nodejs/node/commit/e99082e32d)] - **doc**: add a cli options doc page (Jeremiah Senkpiel) [#5787](https://github.com/nodejs/node/pull/5787)
- [[`0ffd794b27`](https://github.com/nodejs/node/commit/0ffd794b27)] - **doc**: Add windows example for Path.format (Mithun Patel) [#5700](https://github.com/nodejs/node/pull/5700)
- [[`f53cc37578`](https://github.com/nodejs/node/commit/f53cc37578)] - **doc**: grammar, clarity and links in timers doc (Bryan English) [#5792](https://github.com/nodejs/node/pull/5792)
- [[`3ada8cc09a`](https://github.com/nodejs/node/commit/3ada8cc09a)] - **doc**: align doc/api/tls.markdown with style guide (Stefano Vozza) [#5706](https://github.com/nodejs/node/pull/5706)
- [[`5d28ce3942`](https://github.com/nodejs/node/commit/5d28ce3942)] - **doc**: topic blocking vs non-blocking (Jarrett Widman) [#5326](https://github.com/nodejs/node/pull/5326)
- [[`d9b4e15f75`](https://github.com/nodejs/node/commit/d9b4e15f75)] - **doc**: fix typo in synchronous randomBytes example (Andrea Giammarchi) [#5781](https://github.com/nodejs/node/pull/5781)
- [[`d8318c2226`](https://github.com/nodejs/node/commit/d8318c2226)] - **doc**: fix crypto update() signatures (Brian White) [#5500](https://github.com/nodejs/node/pull/5500)
- [[`15c5662959`](https://github.com/nodejs/node/commit/15c5662959)] - **doc**: fix multiline return comments in querystring (Claudio Rodriguez) [#5705](https://github.com/nodejs/node/pull/5705)
- [[`75f723c0aa`](https://github.com/nodejs/node/commit/75f723c0aa)] - **doc**: fix invalid path doc comments (Rich Trott) [#5670](https://github.com/nodejs/node/pull/5670)
- [[`724b87d75c`](https://github.com/nodejs/node/commit/724b87d75c)] - **doc**: explain path.format() algorithm (Rich Trott) [#5688](https://github.com/nodejs/node/pull/5688)
- [[`89df17ed0b`](https://github.com/nodejs/node/commit/89df17ed0b)] - **doc**: fix return value of write methods (Felix Böhm) [#5736](https://github.com/nodejs/node/pull/5736)
- [[`5ab51ee151`](https://github.com/nodejs/node/commit/5ab51ee151)] - **doc**: reformat & improve node.1 manual page (Jeremiah Senkpiel) [#5497](https://github.com/nodejs/node/pull/5497)
- [[`f34a00cee2`](https://github.com/nodejs/node/commit/f34a00cee2)] - **docs**: fix man pages link if tok type is code (Mithun Patel) [#5721](https://github.com/nodejs/node/pull/5721)
- [[`3bff3111f4`](https://github.com/nodejs/node/commit/3bff3111f4)] - **https**: fix ssl socket leak when keepalive is used (Alexander Penev) [#5713](https://github.com/nodejs/node/pull/5713)
- [[`7b21c09b73`](https://github.com/nodejs/node/commit/7b21c09b73)] - **lib**: simplify code with String.prototype.repeat() (Jackson Tian) [#5359](https://github.com/nodejs/node/pull/5359)
- [[`c75f97f43b`](https://github.com/nodejs/node/commit/c75f97f43b)] - **lib**: reduce usage of `self = this` (Jackson Tian) [#5231](https://github.com/nodejs/node/pull/5231)
- [[`1ccf9b4a56`](https://github.com/nodejs/node/commit/1ccf9b4a56)] - **net**: remove unused `var self = this` from old code (Benjamin Gruenbaum) [#5224](https://github.com/nodejs/node/pull/5224)
- [[`6e5835b8cd`](https://github.com/nodejs/node/commit/6e5835b8cd)] - **path**: refactor path.format() repeated code (Rich Trott) [#5673](https://github.com/nodejs/node/pull/5673)
- [[`15c7b3a127`](https://github.com/nodejs/node/commit/15c7b3a127)] - **src,tools**: use template literals (Rich Trott) [#5778](https://github.com/nodejs/node/pull/5778)
- [[`ca971b0d77`](https://github.com/nodejs/node/commit/ca971b0d77)] - **test**: smaller chunk size for smaller person.jpg (Jérémy Lal) [#5813](https://github.com/nodejs/node/pull/5813)
- [[`f95fc175eb`](https://github.com/nodejs/node/commit/f95fc175eb)] - **test**: strip non-free icc profile from person.jpg (Jérémy Lal) [#5813](https://github.com/nodejs/node/pull/5813)
- [[`7c2c7b0577`](https://github.com/nodejs/node/commit/7c2c7b0577)] - **test**: remove timer from test-http-1.0 (Santiago Gimeno) [#5129](https://github.com/nodejs/node/pull/5129)
- [[`70512e51a4`](https://github.com/nodejs/node/commit/70512e51a4)] - **test**: repl tab completion test (Santiago Gimeno) [#5534](https://github.com/nodejs/node/pull/5534)
- [[`89f091d621`](https://github.com/nodejs/node/commit/89f091d621)] - **test**: make test-net-connect-options-ipv6.js better (Michael Dawson) [#5791](https://github.com/nodejs/node/pull/5791)
- [[`d2fa64490f`](https://github.com/nodejs/node/commit/d2fa64490f)] - **test**: fix `test-cluster-worker-kill` (Santiago Gimeno) [#5814](https://github.com/nodejs/node/pull/5814)
- [[`f0d885a0a9`](https://github.com/nodejs/node/commit/f0d885a0a9)] - **test**: fix flaky test-cluster-shared-leak (Claudio Rodriguez) [#5802](https://github.com/nodejs/node/pull/5802)
- [[`b352cc7db4`](https://github.com/nodejs/node/commit/b352cc7db4)] - **test**: minimize test-http-get-pipeline-problem (Rich Trott) [#5728](https://github.com/nodejs/node/pull/5728)
- [[`21770c3806`](https://github.com/nodejs/node/commit/21770c3806)] - **test**: reduce brittleness of tab complete test (Matt Loring) [#5772](https://github.com/nodejs/node/pull/5772)
- [[`46f0e02620`](https://github.com/nodejs/node/commit/46f0e02620)] - **timers**: fix lint from 4fe02e2 (Jeremiah Senkpiel) [#5825](https://github.com/nodejs/node/pull/5825)
- [[`20a68e9eef`](https://github.com/nodejs/node/commit/20a68e9eef)] - **timers**: give Timeouts a constructor name (Jeremiah Senkpiel) [#5793](https://github.com/nodejs/node/pull/5793)
- [[`d3654d80f3`](https://github.com/nodejs/node/commit/d3654d80f3)] - **timers**: improve setImmediate() performance (Brian White) [#4169](https://github.com/nodejs/node/pull/4169)
- [[`b1a4870200`](https://github.com/nodejs/node/commit/b1a4870200)] - **tools**: remove unused imports (Sakthipriyan Vairamani) [#5765](https://github.com/nodejs/node/pull/5765)

Windows 32-bit Installer: https://nodejs.org/dist/v5.9.1/node-v5.9.1-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v5.9.1/node-v5.9.1-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v5.9.1/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v5.9.1/win-x64/node.exe \
Mac OS X 64-bit Installer: https://nodejs.org/dist/v5.9.1/node-v5.9.1.pkg \
Mac OS X 64-bit Binary: https://nodejs.org/dist/v5.9.1/node-v5.9.1-darwin-x64.tar.gz \
Linux 32-bit Binary: https://nodejs.org/dist/v5.9.1/node-v5.9.1-linux-x86.tar.xz \
Linux 64-bit Binary: https://nodejs.org/dist/v5.9.1/node-v5.9.1-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v5.9.1/node-v5.9.1-linux-ppc64le.tar.xz \
SmartOS 32-bit Binary: https://nodejs.org/dist/v5.9.1/node-v5.9.1-sunos-x86.tar.xz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v5.9.1/node-v5.9.1-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v5.9.1/node-v5.9.1-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v5.9.1/node-v5.9.1-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v5.9.1/node-v5.9.1-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v5.9.1/node-v5.9.1.tar.gz \
Other release files: https://nodejs.org/dist/v5.9.1/ \
Documentation: https://nodejs.org/docs/v5.9.1/api/

Shasums (GPG signing hash: SHA512, file hash: SHA256):

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA512

90dbbd2072582f0373a738114131112f3f8a2c7f7f64bbf4991a51d2808d4935  node-v5.9.1-darwin-x64.tar.gz
5e27117f8aa3d684868ac49e808834eff6242a1af3a166bc5dad4f3b175c6845  node-v5.9.1-darwin-x64.tar.xz
fb16ac5bb0c49f7b4de6a93744fee638c55165f8fac88157a324dc358ae24cb5  node-v5.9.1-headers.tar.gz
c79c7d524e47ecc6999bfb2a7243bec55328cae773054eabb0cd169f275f5397  node-v5.9.1-headers.tar.xz
09fd524d987e3c70aed7aa52d21f6448fe06cdd05c627a6de326384b98a3bb0e  node-v5.9.1-linux-arm64.tar.gz
ec748da69f8033df4f1106183637c547148604986c672132fac3f32f1f4faeb2  node-v5.9.1-linux-arm64.tar.xz
755965b20e4fa991072e7bb07937bd3c075b689b10d21161bfb34037dd5c52b9  node-v5.9.1-linux-armv6l.tar.gz
a5ee262f5ffc7146ac4139a9e2fe12a7670d87dce64b14d8a89e38f68272f1e7  node-v5.9.1-linux-armv6l.tar.xz
b4a7880a9906175121a40ff8862009bc2bf80ac2bfd5e0e4aab6d9088b52fe21  node-v5.9.1-linux-armv7l.tar.gz
e6ce828dd85f33b55753ed80e3d9bfc214aff55b37b1bb7ba2bc3a8cdeba3f3a  node-v5.9.1-linux-armv7l.tar.xz
0211aa951355eddadb07ffae0b945b954df7b8ba72231d75d2a8a09335a8f0a6  node-v5.9.1-linux-ppc64le.tar.gz
3cc17c63f5a0390fa94353b927b54f25587ffb7792e4c08949338bd0455c11a9  node-v5.9.1-linux-ppc64le.tar.xz
4b9951e6afd75010f53264fc1a61e2d92ae23a590bbb58fea3e62d6f0104f657  node-v5.9.1-linux-x64.tar.gz
e8da38196c37c07ba922c569356988e01348f48344619b60dc0a1b99c941a3de  node-v5.9.1-linux-x64.tar.xz
749eb56ae38ee0cb4ac659d6de50954a4e57bd2796fda6bc75725f7b98f21887  node-v5.9.1-linux-x86.tar.gz
67d9243af1a5660b2c21e83661dab0f77d2c130cc5d1ffd4eb62e0fae0f6b62b  node-v5.9.1-linux-x86.tar.xz
d2e4c33f7cc5cc379882906e3df9787a7efaec3d9d4eb4076c12211f546e31a1  node-v5.9.1.pkg
17feb55c0ca64d1fb57e7aa54d3226d1b72adc3484c276f3ac040891b566325c  node-v5.9.1-sunos-x64.tar.gz
bd3f22203af80497cb63a6c9c40cd8e317b26c90e83a2b628a083438bf367a14  node-v5.9.1-sunos-x64.tar.xz
8a430122c7ad4271414d3d5b735277d19abe7fc8a3f7d3789630b58908166740  node-v5.9.1-sunos-x86.tar.gz
978aac18aa9c03e8dbaffe3de9d5c15bac3b0b4e5a037579bdff6416c3a1250f  node-v5.9.1-sunos-x86.tar.xz
9bfa882ba832c526add1f939121c79d95b9c592785ec8afe043086a0c7a30c18  node-v5.9.1.tar.gz
668067077dfceb3d2039d0df693fec6aa20b920d01a42b53dc61d4aa91e0ae55  node-v5.9.1.tar.xz
3090c58cc46d6068dfa7947f734e146481fbc6699bf34bff658fe4fe5ad4b75f  node-v5.9.1-x64.msi
73eb560ae88336497ea2e9064b1dc90c336024c652196e48f5f6c5576ab28ea1  node-v5.9.1-x86.msi
d18c47df465b5a59924817c177e3be432495411d5c5cb6e80fc23c3763f17d66  win-x64/node.exe
ac7f227c1aff701cf3e57e2af8fb21b3ddfdbab5808ba8e685f9a9bafd6ebebe  win-x64/node.lib
693a85ac8b474d50b419089e295d56f240827473d102412c1a98edafe243606b  win-x86/node.exe
8d9ca2f0c61715b8539ce9805977adac8bdd1c1049792ed20302ff6d66339727  win-x86/node.lib
-----BEGIN PGP SIGNATURE-----
Comment: GPGTools - https://gpgtools.org

iQIcBAEBCgAGBQJW8tQZAAoJEEX17r2BPa6OcxgP/0Q1bwH5/ekjnPQK0Mb51s6N
nMEN6GidmijEY7alAIEQvrfIjMo/UCBZEbfKD6oQPwSRqDLxMzmJJWkusezClPWb
kTA8mB4fWy5I9gQO/HOuJTBRXTZuPJKlV3SjeZtFg/9v5wAdjYpxFu3t9Gy9ePAK
pZ9kq67PdLTE2Z25wKqqpPSojR508QEHx6/HH/w/g86FCljkT7hGV1A8t6XZyxtr
NWWEONv7++MZpFHFMHJ4VVi4+BrwR3POC/P93wt36hgKkJ7cfRwrOra9UmgUCpwW
Ym9zKTxvswHxvz+X2957n64lJO6eGGALyMYSmVG+ozBl6tNyj+eyWtPPOKhBsUL5
qMRtAmXm6S3zmGLrSNGw5zvhOvlVLAcxtcgQePLanfMg35RhZSmdbb6+GNVN86hZ
ZzsdCv44STY+7MBdrZ/yN60eVzN6KTYswxLyTJToGHD4rAdwn/0nVHWAB8gveazV
o95ppu04DYTKAww16Qng+OmXzf8MP0AnY7ODgH3XgdkWrLGkFrc7Vz6ZWsjRVf6r
nyNM8l1n0unsQbDCpfmelrb+qhxM3aI1D40QoSyb/Eu19hcaCm0jLhvsYo7vb/bo
WI6pENg0lvKrV+Odt/rTNbFwnvWzQ811JzlLlaRaQFwnRrY4AYEkvz7y3tlqlCLX
IPT3xpC/2VQXkv3s2bbC
=BEhZ
-----END PGP SIGNATURE-----

```
