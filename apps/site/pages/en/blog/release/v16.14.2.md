---
date: '2022-03-18T01:12:35.934Z'
category: release
title: Node v16.14.2 (LTS)
layout: blog-post
author: <PERSON>
---

### Notable Changes

Update to OpenSSL 1.1.1n, which addresses the following vulnerability:

- Infinite loop in `BN_mod_sqrt()` reachable when parsing certificates (High)(CVE-2022-0778)
  More details are available at <https://www.openssl.org/news/secadv/20220315.txt>

### Commits

- \[[`3924618c74`](https://github.com/nodejs/node/commit/3924618c74)] - **deps**: update archs files for OpenSSL-1.1.1 (<PERSON><PERSON><PERSON>) [#42352](https://github.com/nodejs/node/pull/42352)
- \[[`7a6a870d58`](https://github.com/nodejs/node/commit/7a6a870d58)] - **deps**: upgrade openssl sources to OpenSSL_1_1_1n (<PERSON><PERSON><PERSON>) [#42352](https://github.com/nodejs/node/pull/42352)
- \[[`c533b430f4`](https://github.com/nodejs/node/commit/c533b430f4)] - **test**: fix tests affected by OpenSSL update (Michael Dawson) [#42352](https://github.com/nodejs/node/pull/42352)

Windows 32-bit Installer: https://nodejs.org/dist/v16.14.2/node-v16.14.2-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v16.14.2/node-v16.14.2-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v16.14.2/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v16.14.2/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v16.14.2/node-v16.14.2.pkg \
macOS Apple Silicon 64-bit Binary: https://nodejs.org/dist/v16.14.2/node-v16.14.2-darwin-arm64.tar.gz \
macOS Intel 64-bit Binary: https://nodejs.org/dist/v16.14.2/node-v16.14.2-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v16.14.2/node-v16.14.2-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v16.14.2/node-v16.14.2-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v16.14.2/node-v16.14.2-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v16.14.2/node-v16.14.2-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v16.14.2/node-v16.14.2-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v16.14.2/node-v16.14.2-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v16.14.2/node-v16.14.2.tar.gz \
Other release files: https://nodejs.org/dist/v16.14.2/ \
Documentation: https://nodejs.org/docs/v16.14.2/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

acae171e4d58905e7e4c712a614de17bdf6a6720addbb5e440a94efda78807e4  node-v16.14.2-aix-ppc64.tar.gz
a66d9217d2003bd416d3dd06dfd2c7a044c4c9ff2e43a27865790bd0d59c682d  node-v16.14.2-darwin-arm64.tar.gz
d6fce58cab15017eba3529fe345a8dfcacbd37599ac8af6ec3e4e74d75c82b2b  node-v16.14.2-darwin-arm64.tar.xz
d3076ca7fcc7269c8ff9b03fe7d1c277d913a7e84a46a14eff4af7791ff9d055  node-v16.14.2-darwin-x64.tar.gz
8c45f73fd1227ac6e9ad9127398251c381c9af1b2cd364fb32726dc5f38bf393  node-v16.14.2-darwin-x64.tar.xz
505e1c287e55bb8ec25d2e20d7d87a57b0c2666e1aa7064e0661ad76b60db594  node-v16.14.2-headers.tar.gz
a4e858e062af48cbf57b8505044f1fe38c7debeb7a9eeddcb3b16e90bff4c73b  node-v16.14.2-headers.tar.xz
8a792a4cb6d83a960f7bd2901225c492e40ace541fbd73ff59ac4a332c3aaafb  node-v16.14.2-linux-arm64.tar.gz
f7c5a573c06a520d6c2318f6ae204141b8420386553a692fc359f8ae3d88df96  node-v16.14.2-linux-arm64.tar.xz
339d6505835efdc3cda6f44af9db2b3ca5df240b7b34845a0b2d26342a4c328f  node-v16.14.2-linux-armv7l.tar.gz
364ca85c71ce52eb2789bba223d6aed0b76c6f064e9f2074493388fb1c917245  node-v16.14.2-linux-armv7l.tar.xz
52884670d96d1b16d00b3dfd8f18f54649cd796aafbf66d574c92d2bddbd771b  node-v16.14.2-linux-ppc64le.tar.gz
48469382586c9a1c0f372ed88cbb13870d225538a305eb35efcb7bd7732d330b  node-v16.14.2-linux-ppc64le.tar.xz
39cfa5f998378e620c1f659bd933ea0460ee5cbec575e6831e27b4bf3e575525  node-v16.14.2-linux-s390x.tar.gz
3197925919ca357e17a31132dc6ef4e5afae819fa09905cfe9f7ff7924a00bf5  node-v16.14.2-linux-s390x.tar.xz
57e02c27eb5e52f560f72d96240e898cb52818dc9fc50f45478ce39ece38583a  node-v16.14.2-linux-x64.tar.gz
e40c6f81bfd078976d85296b5e657be19e06862497741ad82902d0704b34bb1b  node-v16.14.2-linux-x64.tar.xz
ff790d712b5db675020b1e387e1824c45e71803e25d7356dc7399aaeff8caf65  node-v16.14.2.pkg
082170f362c4da0e97f3a1899e3f5e4c998bbc245b803c77c6ab113a2b5cbd5f  node-v16.14.2.tar.gz
e922e215cc68eb5f94d33e8a0b61e2c863b7731cc8600ab955d3822da90ff8d1  node-v16.14.2.tar.xz
3f4b168eaa479397ec40ab8f514e2a924b078a032845fb896d364c0b4084b19c  node-v16.14.2-win-x64.7z
4731da4fbb2015d414e871fa9118cabb643bdb6dbdc8a69a3ed563266ac93229  node-v16.14.2-win-x64.zip
5b550768e452cf4d8039aa903c1e5881326c1837e7db6f14d5b11dea8302629a  node-v16.14.2-win-x86.7z
b48aa66a6f35933bd048b4b1290ca0e5d43ffb31147618f29cbc60b5a2b43009  node-v16.14.2-win-x86.zip
df1a010e89fd6fe1cca920425a165dffef45e6b1f072aeef9ed47eba8ff5f283  node-v16.14.2-x64.msi
8d9bdfdfe8a3d18eb724f71b64a0dc41d18b1f6d904d4f35556e70d0fa3bece8  node-v16.14.2-x86.msi
92e971faed43bebbd8c8695167d9b5d732a322d6ffc0b8b3034560a119f8d487  win-x64/node.exe
4b083c771184555dcc23bca56986e0761cf58ebce6ea2b27a81a524a18d5d7ee  win-x64/node.lib
5d550962ef37620d36c58be08781660b3f118a3b1e6776c64729df6f5efbf06c  win-x64/node_pdb.7z
420c2793f1ae4e5e8ae7b150800403f2753ca66ae1d0f9ad82f81d732e2ac67e  win-x64/node_pdb.zip
34c7200f548fa5e6014211410b53edc187e59b9f75155e6afac23496b7b97cb7  win-x86/node.exe
838696e87e61ac96bedbf45331be7e22f4cf5ad1fec1aef75b8dbbd49efa82a2  win-x86/node.lib
db7b9eee61d1d598b832db323bebd145f16a2e225e19b43f72275e19c5b65793  win-x86/node_pdb.7z
de87c0bf84c10553bf13732a4778c12e11b06efbaa9fd58a8b8c6a3c71b28862  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEyC+jrhy+3Gvka5NgxDzsRcF6uTwFAmIz2HQACgkQxDzsRcF6
uTxN0BAAmFmu3bK0sl+D8qxgJMJHzEIearqkDwhixKKn3GNe542c54rcue71zR4K
egu5p4IEd9P4Aw22vZpz+15D/HXABaMT1ryqfinWNhxVdAle7+01Vym2C3+5i0FK
MIBV/a54yYnykg2FNF1QGN+3/gz/zbR+tSV+RW67xJnX0Xeq292WotJqHwp2HnKy
4GprGJFsscY1EjUm/Jrv8ijiJ4P6n/wTrmj9UbDWUiZcf9nrceF5KwcmUaJGlHiI
OuV8A/9tpFJQ7fxA74TRndZ/8fINvHLoCQWptLVWa1A5aKyI6OYjwo0KD78tNyc4
x357qnP49Mu+ZWVi46PXL6GXL31X3K37oDX2ef3XSUl5Szqvdf1li33gr5OtMDn7
v4D06d0n1TV27m5xCjOnqw5j3KflhP3L394/0EolDG6B1rrgU6j6Qx215RIP12vp
ap4fy24yqRDNv7t7YcPSynspfZ5xMOu8HefmdYyGbQ4GfpwMKSxReg8fzPJiB5+U
LGGcdmlgVQgdo0pgdhlNZ0aYQoFtpo5EfFQu9i3aZDW/MWrZkSF9mu4V2NGITJ7K
JhLGPYBTuhHMGRy3AKxgD08FZXEFuhPGK+hDT4Ewxu+lw/Lk6dUx6e2zzAQwZfFS
Bb3AnQYFW1TrTeSdI86daqwYf+wqULMK8O9n1qN2ctcDCl8xO30=
=Ir/e
-----END PGP SIGNATURE-----

```
