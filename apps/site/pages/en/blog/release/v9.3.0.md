---
date: '2017-12-12T21:00:52.175Z'
category: release
title: Node v9.3.0 (Current)
layout: blog-post
author: <PERSON><PERSON>
---

### Notable Changes

- **async_hooks**:
  - add trace events to async_hooks (<PERSON>) [#15538](https://github.com/nodejs/node/pull/15538)
  - add provider types for net server (<PERSON>) [#17157](https://github.com/nodejs/node/pull/17157)
- **console**:
  - console.debug can now be used outside of the inspector (<PERSON>) [#17033](https://github.com/nodejs/node/pull/17033)
- **deps**:
  - upgrade libuv to 1.18.0 (cjihrig) [#17282](https://github.com/nodejs/node/pull/17282)
  - patch V8 to 6.2.414.46 (<PERSON><PERSON>) [#17206](https://github.com/nodejs/node/pull/17206)
- **module**:
  - module.builtinModules will return a list of built in modules (<PERSON>) [#16386](https://github.com/nodejs/node/pull/16386)
- **n-api**:
  - add helper for addons to get the event loop (<PERSON>sen) [#17109](https://github.com/nodejs/node/pull/17109)
- **process**:
  - process.setUncaughtExceptionCaptureCallback can now be used to customize behavior for `--abort-on-uncaught-exception` (Anna Henningsen) [#17159](https://github.com/nodejs/node/pull/17159)
  - A signal handler is now able to receive the signal code that triggered the handler. (Robert Rossmann) [#15606](https://github.com/nodejs/node/pull/15606)
- **src**:
  - embedders can now use Node::CreatePlatform to create an instance of NodePlatform (Cheng Zhao) [#16981](https://github.com/nodejs/node/pull/16981)
- **stream**:
  - writable.writableHighWaterMark and readable.readableHighWaterMark will return the values the stream object was instantiated with (Calvin Metcalf) [#12860](https://github.com/nodejs/node/pull/12860)
- **Added new collaborators**
  - [maclover7](https://github.com/maclover7) Jon Moss
  - [guybedford](https://github.com/guybedford) Guy Bedford
  - [hashseed](https://github.com/hashseed) Yang Guo

### Commits

- [[`623b589921`](https://github.com/nodejs/node/commit/623b589921)] - tools/doc: add tools/remark-\* to eslintignore (Ivan Wei) [#17240](https://github.com/nodejs/node/pull/17240)
- [[`cf0d7cfc46`](https://github.com/nodejs/node/commit/cf0d7cfc46)] - **async_hooks**: add destroy event for gced AsyncResources (Sebastian Mayr) [#16998](https://github.com/nodejs/node/pull/16998)
- [[`cf7e15cf78`](https://github.com/nodejs/node/commit/cf7e15cf78)] - **(SEMVER-MINOR)** **async_hooks**: add trace events to async_hooks (Andreas Madsen) [#15538](https://github.com/nodejs/node/pull/15538)
- [[`e0ce7cf1e9`](https://github.com/nodejs/node/commit/e0ce7cf1e9)] - **(SEMVER-MINOR)** **async_wrap**: add provider types for net server (Andreas Madsen) [#17157](https://github.com/nodejs/node/pull/17157)
- [[`cbd0be59f0`](https://github.com/nodejs/node/commit/cbd0be59f0)] - **benchmark**: fix http/simple.js benchmark (Anatoli Papirovski) [#17583](https://github.com/nodejs/node/pull/17583)
- [[`120d756e47`](https://github.com/nodejs/node/commit/120d756e47)] - **benchmark**: refactor to use template string (Antonio V) [#17313](https://github.com/nodejs/node/pull/17313)
- [[`b16d570395`](https://github.com/nodejs/node/commit/b16d570395)] - **benchmark**: set maxHeaderListPairs in h2 headers.js (Anatoli Papirovski) [#17194](https://github.com/nodejs/node/pull/17194)
- [[`9ffdee811d`](https://github.com/nodejs/node/commit/9ffdee811d)] - **benchmark**: use unique filenames in fs benchmarks (Rich Trott) [#16776](https://github.com/nodejs/node/pull/16776)
- [[`ee84fc333d`](https://github.com/nodejs/node/commit/ee84fc333d)] - **benchmark,path**: remove unused variables (薛定谔的猫) [#15789](https://github.com/nodejs/node/pull/15789)
- [[`883281bca9`](https://github.com/nodejs/node/commit/883281bca9)] - **buffer**: don't predefine error (buji) [#17021](https://github.com/nodejs/node/pull/17021)
- [[`dcb53c10e2`](https://github.com/nodejs/node/commit/dcb53c10e2)] - **build**: allow running configure from any directory (Gibson Fahnestock) [#17321](https://github.com/nodejs/node/pull/17321)
- [[`5d1463a0bc`](https://github.com/nodejs/node/commit/5d1463a0bc)] - **build**: define HAVE_OPENSSL macro for cctest (Matheus Marchini) [#17461](https://github.com/nodejs/node/pull/17461)
- [[`4bb27a2db3`](https://github.com/nodejs/node/commit/4bb27a2db3)] - **build**: add a `make help` option for common targets (Gibson Fahnestock) [#17323](https://github.com/nodejs/node/pull/17323)
- [[`5e0f39323f`](https://github.com/nodejs/node/commit/5e0f39323f)] - **build**: add serial commas to messages in configure script (Rich Trott) [#17464](https://github.com/nodejs/node/pull/17464)
- [[`742a4566ee`](https://github.com/nodejs/node/commit/742a4566ee)] - **build**: fix test-v8 target (Michaël Zasso) [#17269](https://github.com/nodejs/node/pull/17269)
- [[`46c1d999d9`](https://github.com/nodejs/node/commit/46c1d999d9)] - **build**: add make lint-js-fix (Joyee Cheung) [#17283](https://github.com/nodejs/node/pull/17283)
- [[`0a40a1133d`](https://github.com/nodejs/node/commit/0a40a1133d)] - **build**: fix bsd build with gcc (Matheus Marchini) [#16737](https://github.com/nodejs/node/pull/16737)
- [[`0f727c07b9`](https://github.com/nodejs/node/commit/0f727c07b9)] - **build**: remove empty VCLibrarianTool entry (Daniel Bevenius) [#17191](https://github.com/nodejs/node/pull/17191)
- [[`09bd797711`](https://github.com/nodejs/node/commit/09bd797711)] - **build**: Allow linking against an external copy of nghttp2. (Ed Schouten) [#16788](https://github.com/nodejs/node/pull/16788)
- [[`9093392954`](https://github.com/nodejs/node/commit/9093392954)] - **build**: do not build doc in source tarball (Joyee Cheung) [#17100](https://github.com/nodejs/node/pull/17100)
- [[`9a4abe47d5`](https://github.com/nodejs/node/commit/9a4abe47d5)] - **build**: minor corrections to configure descriptions (Daniel Bevenius) [#17094](https://github.com/nodejs/node/pull/17094)
- [[`035a24e619`](https://github.com/nodejs/node/commit/035a24e619)] - **build**: enforce order of dependency when building addons (Joyee Cheung) [#17048](https://github.com/nodejs/node/pull/17048)
- [[`91385be239`](https://github.com/nodejs/node/commit/91385be239)] - **build**: fix cctest target --with-dtrace (Daniel Bevenius) [#17039](https://github.com/nodejs/node/pull/17039)
- [[`2eec94489d`](https://github.com/nodejs/node/commit/2eec94489d)] - **_Revert_** "**build**: for --enable-static, run only cctest" (Daniel Bevenius) [#14986](https://github.com/nodejs/node/pull/14986)
- [[`578d80b59b`](https://github.com/nodejs/node/commit/578d80b59b)] - **build**: prevent echoing of recipes for test target (Daniel Bevenius) [#17010](https://github.com/nodejs/node/pull/17010)
- [[`5fc1e27e98`](https://github.com/nodejs/node/commit/5fc1e27e98)] - **build, win**: faster Release rebuilds (Bartosz Sosnowski) [#17393](https://github.com/nodejs/node/pull/17393)
- [[`90a5e9f19b`](https://github.com/nodejs/node/commit/90a5e9f19b)] - **build,win**: vcbuild refactoring call configure (Refael Ackermann) [#17299](https://github.com/nodejs/node/pull/17299)
- [[`87c885bd44`](https://github.com/nodejs/node/commit/87c885bd44)] - **build,win,msi**: support WiX with VS2017 (João Reis) [#17101](https://github.com/nodejs/node/pull/17101)
- [[`23967b2713`](https://github.com/nodejs/node/commit/23967b2713)] - **console**: make dirxml an alias for console.log (Benjamin Zaslavsky) [#17152](https://github.com/nodejs/node/pull/17152)
- [[`40d4fee8d7`](https://github.com/nodejs/node/commit/40d4fee8d7)] - **console**: add support for console.debug (Benjamin Zaslavsky) [#17033](https://github.com/nodejs/node/pull/17033)
- [[`4a5e32206a`](https://github.com/nodejs/node/commit/4a5e32206a)] - **crypto**: remove BIO_set_shutdown (Daniel Bevenius) [#17542](https://github.com/nodejs/node/pull/17542)
- [[`c951e2c7d4`](https://github.com/nodejs/node/commit/c951e2c7d4)] - **crypto**: remove explicit qualifiers in Initialize (Daniel Bevenius) [#17490](https://github.com/nodejs/node/pull/17490)
- [[`8c2143091d`](https://github.com/nodejs/node/commit/8c2143091d)] - **crypto**: do not reach into OpenSSL internals for ThrowCryptoError (David Benjamin) [#16701](https://github.com/nodejs/node/pull/16701)
- [[`49402b12d0`](https://github.com/nodejs/node/commit/49402b12d0)] - **crypto**: declare int return type for set_field (Daniel Bevenius) [#17468](https://github.com/nodejs/node/pull/17468)
- [[`9e50f1721e`](https://github.com/nodejs/node/commit/9e50f1721e)] - **crypto**: use SetNull instead of Set (Daniel Bevenius) [#17521](https://github.com/nodejs/node/pull/17521)
- [[`e3df569d1c`](https://github.com/nodejs/node/commit/e3df569d1c)] - **deps**: upgrade libuv to 1.18.0 (cjihrig) [#17282](https://github.com/nodejs/node/pull/17282)
- [[`9f282ddaf7`](https://github.com/nodejs/node/commit/9f282ddaf7)] - **deps**: cherry-pick 1420e44db0 from upstream V8 (Timothy Gu) [#17344](https://github.com/nodejs/node/pull/17344)
- [[`47cd49a8cb`](https://github.com/nodejs/node/commit/47cd49a8cb)] - **deps**: backport 3c8195d from V8 upstream (Myles Borins) [#17383](https://github.com/nodejs/node/pull/17383)
- [[`465a32a087`](https://github.com/nodejs/node/commit/465a32a087)] - **_Revert_** "**deps**: cherry-pick 3c8195d from V8 upstream" (Myles Borins) [#17383](https://github.com/nodejs/node/pull/17383)
- [[`49d23a3021`](https://github.com/nodejs/node/commit/49d23a3021)] - **deps**: V8: backport 14ac02c from upstream (Ali Ijaz Sheikh) [#17512](https://github.com/nodejs/node/pull/17512)
- [[`7c2a9bba64`](https://github.com/nodejs/node/commit/7c2a9bba64)] - **deps**: patch V8 to 6.2.414.46 (Myles Borins) [#17206](https://github.com/nodejs/node/pull/17206)
- [[`04115724dc`](https://github.com/nodejs/node/commit/04115724dc)] - **deps**: cherry-pick 98c40a4bae915 from V8 upstream (Anna Henningsen) [#17134](https://github.com/nodejs/node/pull/17134)
- [[`7812c93a41`](https://github.com/nodejs/node/commit/7812c93a41)] - **deps**: cherry-pick c690f54d95802 from V8 upstream (Anna Henningsen) [#17134](https://github.com/nodejs/node/pull/17134)
- [[`24bb99a808`](https://github.com/nodejs/node/commit/24bb99a808)] - **deps**: cherry-pick upstream ICU fix (Mathias Bynens) [#16931](https://github.com/nodejs/node/pull/16931)
- [[`026f76024b`](https://github.com/nodejs/node/commit/026f76024b)] - **dns**: fix crash while setting server during query (XadillaX) [#14891](https://github.com/nodejs/node/pull/14891)
- [[`ccffbd96d1`](https://github.com/nodejs/node/commit/ccffbd96d1)] - **doc**: fix modules.md export example (Anatoli Papirovski) [#17579](https://github.com/nodejs/node/pull/17579)
- [[`7e2fa5a2d6`](https://github.com/nodejs/node/commit/7e2fa5a2d6)] - **doc**: add link to debugger in process.md (Delapouite) [#17522](https://github.com/nodejs/node/pull/17522)
- [[`a965dda849`](https://github.com/nodejs/node/commit/a965dda849)] - **doc**: simplify and clarify FIPS text in BUILDING.md (Rich Trott) [#17538](https://github.com/nodejs/node/pull/17538)
- [[`b015747156`](https://github.com/nodejs/node/commit/b015747156)] - **doc**: esm loader example with module.builtinModules (Guy Bedford) [#17385](https://github.com/nodejs/node/pull/17385)
- [[`1eff647fd3`](https://github.com/nodejs/node/commit/1eff647fd3)] - **doc**: 'constructor' implies use of new keyword (Cameron Moorehead) [#17364](https://github.com/nodejs/node/pull/17364)
- [[`8a17b7b6f3`](https://github.com/nodejs/node/commit/8a17b7b6f3)] - **doc**: use correct and consistent typography for products (Rich Trott) [#17492](https://github.com/nodejs/node/pull/17492)
- [[`0a0a56aa34`](https://github.com/nodejs/node/commit/0a0a56aa34)] - **doc**: add "Hello world" example for N-API (Franziska Hinkelmann) [#17425](https://github.com/nodejs/node/pull/17425)
- [[`865c4520b6`](https://github.com/nodejs/node/commit/865c4520b6)] - **doc**: immprove inode text in fs.md (Rich Trott) [#17519](https://github.com/nodejs/node/pull/17519)
- [[`18d6dab19d`](https://github.com/nodejs/node/commit/18d6dab19d)] - **doc**: improve text for Console constructor (Rich Trott) [#17519](https://github.com/nodejs/node/pull/17519)
- [[`cb09959e8f`](https://github.com/nodejs/node/commit/cb09959e8f)] - **doc**: improve readability of README.md (Rich Trott) [#17519](https://github.com/nodejs/node/pull/17519)
- [[`0948238aa2`](https://github.com/nodejs/node/commit/0948238aa2)] - **doc**: improve readability of COLLABORATOR_GUIDE.md (Rich Trott) [#17519](https://github.com/nodejs/node/pull/17519)
- [[`7f2764debb`](https://github.com/nodejs/node/commit/7f2764debb)] - **doc**: add info on post-publishing ARM6 builds (Michael Dawson) [#17455](https://github.com/nodejs/node/pull/17455)
- [[`6aa6d418e2`](https://github.com/nodejs/node/commit/6aa6d418e2)] - **doc**: mention node-test-pull-request-lite job (Jon Moss) [#17513](https://github.com/nodejs/node/pull/17513)
- [[`b8141a42d0`](https://github.com/nodejs/node/commit/b8141a42d0)] - **doc**: fix typo in repl.md (Rich Trott) [#17502](https://github.com/nodejs/node/pull/17502)
- [[`232a486c0c`](https://github.com/nodejs/node/commit/232a486c0c)] - **doc**: fix common typo involving one-time listeners (Rich Trott) [#17502](https://github.com/nodejs/node/pull/17502)
- [[`07df234ea2`](https://github.com/nodejs/node/commit/07df234ea2)] - **doc**: fix typo in dns.md (Rich Trott) [#17502](https://github.com/nodejs/node/pull/17502)
- [[`6c97f7fed5`](https://github.com/nodejs/node/commit/6c97f7fed5)] - **doc**: use American spellings per style guide (Rich Trott) [#17471](https://github.com/nodejs/node/pull/17471)
- [[`35d492c428`](https://github.com/nodejs/node/commit/35d492c428)] - **doc**: remove unused link reference (Anatoli Papirovski) [#17510](https://github.com/nodejs/node/pull/17510)
- [[`e9ee168a3d`](https://github.com/nodejs/node/commit/e9ee168a3d)] - **doc**: remove IPC channel implementation details (Bartosz Sosnowski) [#17460](https://github.com/nodejs/node/pull/17460)
- [[`7e38821df2`](https://github.com/nodejs/node/commit/7e38821df2)] - **doc**: use arrow functions in util.md sample code (Mithun Sasidharan) [#17459](https://github.com/nodejs/node/pull/17459)
- [[`53ed05582a`](https://github.com/nodejs/node/commit/53ed05582a)] - **doc**: update AUTHORS list (Michaël Zasso) [#17452](https://github.com/nodejs/node/pull/17452)
- [[`f7b0054b2b`](https://github.com/nodejs/node/commit/f7b0054b2b)] - **doc**: use serial comma in tls.md (Rich Trott) [#17464](https://github.com/nodejs/node/pull/17464)
- [[`20dcbfce89`](https://github.com/nodejs/node/commit/20dcbfce89)] - **doc**: add serial comma in CPP_STYLE_GUIDE.md (Rich Trott) [#17464](https://github.com/nodejs/node/pull/17464)
- [[`01be9462d5`](https://github.com/nodejs/node/commit/01be9462d5)] - **doc**: edit module introduction (Rich Trott) [#17463](https://github.com/nodejs/node/pull/17463)
- [[`4fff2ab7ca`](https://github.com/nodejs/node/commit/4fff2ab7ca)] - **doc**: standardize preposition usage in fs.md (Rich Trott) [#17463](https://github.com/nodejs/node/pull/17463)
- [[`f3ec355123`](https://github.com/nodejs/node/commit/f3ec355123)] - **doc**: improve punctuation in fs.open() text (Rich Trott) [#17463](https://github.com/nodejs/node/pull/17463)
- [[`ef7444cc94`](https://github.com/nodejs/node/commit/ef7444cc94)] - **doc**: use colon consistently in assert.md (Rich Trott) [#17463](https://github.com/nodejs/node/pull/17463)
- [[`cd7cee57e9`](https://github.com/nodejs/node/commit/cd7cee57e9)] - **doc**: update example in module registration (Franziska Hinkelmann) [#17424](https://github.com/nodejs/node/pull/17424)
- [[`d3e76e78ff`](https://github.com/nodejs/node/commit/d3e76e78ff)] - **doc**: introduce categories to Cpp style guide (Franziska Hinkelmann) [#17095](https://github.com/nodejs/node/pull/17095)
- [[`e00923bf5e`](https://github.com/nodejs/node/commit/e00923bf5e)] - **doc**: add missing serial commas (Rich Trott) [#17384](https://github.com/nodejs/node/pull/17384)
- [[`9df52dd115`](https://github.com/nodejs/node/commit/9df52dd115)] - **doc**: be concise about serial commas (Rich Trott) [#17384](https://github.com/nodejs/node/pull/17384)
- [[`7849d53158`](https://github.com/nodejs/node/commit/7849d53158)] - **doc**: document tls.checkServerIdentity (Hannes Magnusson) [#17203](https://github.com/nodejs/node/pull/17203)
- [[`a596577a31`](https://github.com/nodejs/node/commit/a596577a31)] - **doc**: improve checkServerIdentity docs (Hannes Magnusson) [#17203](https://github.com/nodejs/node/pull/17203)
- [[`2a4f4f8125`](https://github.com/nodejs/node/commit/2a4f4f8125)] - **doc**: add guide to maintaining npm (Myles Borins) [#16541](https://github.com/nodejs/node/pull/16541)
- [[`3807c6887a`](https://github.com/nodejs/node/commit/3807c6887a)] - **doc**: fix doc example for cctest (Matheus Marchini) [#17355](https://github.com/nodejs/node/pull/17355)
- [[`bd55a79422`](https://github.com/nodejs/node/commit/bd55a79422)] - **doc**: clarify fast-track of reversions (Refael Ackermann) [#17332](https://github.com/nodejs/node/pull/17332)
- [[`dcd87acb7b`](https://github.com/nodejs/node/commit/dcd87acb7b)] - **doc**: make error descriptions more concise (Rich Trott) [#16954](https://github.com/nodejs/node/pull/16954)
- [[`cc91a00af6`](https://github.com/nodejs/node/commit/cc91a00af6)] - **doc**: fix typo in stream.md (Matthew Leon) [#17357](https://github.com/nodejs/node/pull/17357)
- [[`048878288b`](https://github.com/nodejs/node/commit/048878288b)] - **doc**: non-partitioned async crypto operations (Jamie Davis) [#17250](https://github.com/nodejs/node/pull/17250)
- [[`0443909848`](https://github.com/nodejs/node/commit/0443909848)] - **doc**: move Code of Conduct to admin repo (Myles Borins) [#17301](https://github.com/nodejs/node/pull/17301)
- [[`5756d67f95`](https://github.com/nodejs/node/commit/5756d67f95)] - **doc**: fix typo occuring -\> occurring (Leko) [#17350](https://github.com/nodejs/node/pull/17350)
- [[`94be7fdfec`](https://github.com/nodejs/node/commit/94be7fdfec)] - **doc**: Add link for ECMAScript 2015 (smatsu-hl) [#17317](https://github.com/nodejs/node/pull/17317)
- [[`a0acd91470`](https://github.com/nodejs/node/commit/a0acd91470)] - **doc**: caution against removing pseudoheaders (James M Snell) [#17329](https://github.com/nodejs/node/pull/17329)
- [[`2bd241e974`](https://github.com/nodejs/node/commit/2bd241e974)] - **doc**: replace string with template string (Leko) [#17316](https://github.com/nodejs/node/pull/17316)
- [[`0b1448897d`](https://github.com/nodejs/node/commit/0b1448897d)] - **doc**: replace function with arrow function in vm.md (narirou) [#17307](https://github.com/nodejs/node/pull/17307)
- [[`078b4a625b`](https://github.com/nodejs/node/commit/078b4a625b)] - **doc**: replace function with arrow function (Leko) [#17304](https://github.com/nodejs/node/pull/17304)
- [[`4fafeae4a2`](https://github.com/nodejs/node/commit/4fafeae4a2)] - **doc**: update maintainting V8 guide (Michaël Zasso) [#17260](https://github.com/nodejs/node/pull/17260)
- [[`524db29844`](https://github.com/nodejs/node/commit/524db29844)] - **doc**: fix typo in api doc of url.format(urlObject) (pkovacs) [#17295](https://github.com/nodejs/node/pull/17295)
- [[`c901ccec40`](https://github.com/nodejs/node/commit/c901ccec40)] - **doc**: add ES Modules entry to who-to-cc (Rich Trott) [#17205](https://github.com/nodejs/node/pull/17205)
- [[`e45c9c651a`](https://github.com/nodejs/node/commit/e45c9c651a)] - **doc**: add maclover7 to collaborators (Jon Moss) [#17289](https://github.com/nodejs/node/pull/17289)
- [[`f13667221b`](https://github.com/nodejs/node/commit/f13667221b)] - **doc**: update http URLs to https in README.md (Ronald Eddy Jr) [#17264](https://github.com/nodejs/node/pull/17264)
- [[`c67612963c`](https://github.com/nodejs/node/commit/c67612963c)] - **doc**: update http URLs to https in doc/api (Ronald Eddy Jr) [#17263](https://github.com/nodejs/node/pull/17263)
- [[`c345a107a6`](https://github.com/nodejs/node/commit/c345a107a6)] - **doc**: update http URLs to https in GOVERNANCE.md (Ronald Eddy Jr) [#17262](https://github.com/nodejs/node/pull/17262)
- [[`f3c5f76fe8`](https://github.com/nodejs/node/commit/f3c5f76fe8)] - **doc**: update http URLs to https in CONTRIBUTING.md (Ronald Eddy Jr) [#17261](https://github.com/nodejs/node/pull/17261)
- [[`df5436cee1`](https://github.com/nodejs/node/commit/df5436cee1)] - **doc**: add SharedArrayBuffer to Buffer documentation (Thomas den Hollander) [#15489](https://github.com/nodejs/node/pull/15489)
- [[`821951e2a9`](https://github.com/nodejs/node/commit/821951e2a9)] - **doc**: document resolve hook formats (Lucas Azzola) [#16375](https://github.com/nodejs/node/pull/16375)
- [[`04c4c1f260`](https://github.com/nodejs/node/commit/04c4c1f260)] - **doc**: fs.readFile is async but not partitioned (Jamie Davis) [#17154](https://github.com/nodejs/node/pull/17154)
- [[`74506f72e6`](https://github.com/nodejs/node/commit/74506f72e6)] - **doc**: add description for inspector-only console methods. (Benjamin Zaslavsky) [#17004](https://github.com/nodejs/node/pull/17004)
- [[`1a3aadb2e9`](https://github.com/nodejs/node/commit/1a3aadb2e9)] - **doc**: use better terminology for build machines (Anna Henningsen) [#17142](https://github.com/nodejs/node/pull/17142)
- [[`2fccf84015`](https://github.com/nodejs/node/commit/2fccf84015)] - **doc**: use "JavaScript" instead of "Javascript" (Rich Trott) [#17163](https://github.com/nodejs/node/pull/17163)
- [[`9dcf748000`](https://github.com/nodejs/node/commit/9dcf748000)] - **doc**: prepare for v8/V8 linting in doc text (Rich Trott) [#17163](https://github.com/nodejs/node/pull/17163)
- [[`bd1dbcef85`](https://github.com/nodejs/node/commit/bd1dbcef85)] - **doc**: add capitalization styling to STYLE_GUIDE (Rich Trott) [#17163](https://github.com/nodejs/node/pull/17163)
- [[`68870161cc`](https://github.com/nodejs/node/commit/68870161cc)] - **doc**: update mgol in AUTHORS.txt, add to .mailmap (Michał Gołębiowski-Owczarek) [#17239](https://github.com/nodejs/node/pull/17239)
- [[`ef4c909335`](https://github.com/nodejs/node/commit/ef4c909335)] - **doc**: update release table in V8 guide (Ali Ijaz Sheikh) [#17136](https://github.com/nodejs/node/pull/17136)
- [[`3f363d3cda`](https://github.com/nodejs/node/commit/3f363d3cda)] - **doc**: add guybedford to collaborators (Guy Bedford) [#17197](https://github.com/nodejs/node/pull/17197)
- [[`7b5a05bc0f`](https://github.com/nodejs/node/commit/7b5a05bc0f)] - **doc**: update AUTHORS list (Michaël Zasso) [#16571](https://github.com/nodejs/node/pull/16571)
- [[`4c23e6a8c7`](https://github.com/nodejs/node/commit/4c23e6a8c7)] - **doc**: normalize ToC indentation with heading levels in README (Rich Trott) [#17106](https://github.com/nodejs/node/pull/17106)
- [[`f1d19d5eb9`](https://github.com/nodejs/node/commit/f1d19d5eb9)] - **doc**: add Contributing to Node.js to the README ToC (Rich Trott) [#17106](https://github.com/nodejs/node/pull/17106)
- [[`fa82f3a5c4`](https://github.com/nodejs/node/commit/fa82f3a5c4)] - **doc**: merge Working Groups with Contributing to Node.js in README (Rich Trott) [#17106](https://github.com/nodejs/node/pull/17106)
- [[`39cfecd568`](https://github.com/nodejs/node/commit/39cfecd568)] - **doc**: remove IRC node-dev link from README (Rich Trott) [#17106](https://github.com/nodejs/node/pull/17106)
- [[`976ed7507b`](https://github.com/nodejs/node/commit/976ed7507b)] - **doc**: add missing introduced_in comments (Luigi Pinca) [#16741](https://github.com/nodejs/node/pull/16741)
- [[`39cb687ee5`](https://github.com/nodejs/node/commit/39cb687ee5)] - **doc**: change v8 to V8 (Rich Trott) [#17089](https://github.com/nodejs/node/pull/17089)
- [[`4b35dfbb31`](https://github.com/nodejs/node/commit/4b35dfbb31)] - **doc**: avoid mentioning 'uncaughtException' (Luigi Pinca) [#16905](https://github.com/nodejs/node/pull/16905)
- [[`18b08f082a`](https://github.com/nodejs/node/commit/18b08f082a)] - **doc**: add note about using cluster without networking (pimlie) [#17031](https://github.com/nodejs/node/pull/17031)
- [[`2f34d35b0a`](https://github.com/nodejs/node/commit/2f34d35b0a)] - **doc**: explicitly document highWaterMark option (Sebastian Silbermann) [#17049](https://github.com/nodejs/node/pull/17049)
- [[`c917cd6fdd`](https://github.com/nodejs/node/commit/c917cd6fdd)] - **doc**: fix a link in dgram.md (Vse Mozhet Byt) [#17107](https://github.com/nodejs/node/pull/17107)
- [[`a12bc2df0e`](https://github.com/nodejs/node/commit/a12bc2df0e)] - **doc**: reorganize collaborator guide (Joyee Cheung) [#17056](https://github.com/nodejs/node/pull/17056)
- [[`4a9c75a279`](https://github.com/nodejs/node/commit/4a9c75a279)] - **doc**: delete unused definition in README.md (Vse Mozhet Byt) [#17108](https://github.com/nodejs/node/pull/17108)
- [[`378439e2cb`](https://github.com/nodejs/node/commit/378439e2cb)] - **doc**: add Support section in README (Rich Trott) [#16533](https://github.com/nodejs/node/pull/16533)
- [[`8dc05e4630`](https://github.com/nodejs/node/commit/8dc05e4630)] - **doc**: document common pattern for instanceof checks (Michael Dawson) [#16699](https://github.com/nodejs/node/pull/16699)
- [[`03803ee505`](https://github.com/nodejs/node/commit/03803ee505)] - **doc**: mention smart pointers in Cpp style guide (Franziska Hinkelmann) [#17055](https://github.com/nodejs/node/pull/17055)
- [[`b87030c5cf`](https://github.com/nodejs/node/commit/b87030c5cf)] - **doc**: correct the wrong added meta data (Gaara) [#17072](https://github.com/nodejs/node/pull/17072)
- [[`73295370cc`](https://github.com/nodejs/node/commit/73295370cc)] - **doc**: document fs.realpath.native() (Ben Noordhuis) [#17059](https://github.com/nodejs/node/pull/17059)
- [[`4bdd05dd84`](https://github.com/nodejs/node/commit/4bdd05dd84)] - **doc**: add Table of Contents to Cpp style guide (Franziska Hinkelmann) [#17052](https://github.com/nodejs/node/pull/17052)
- [[`7d49bd0045`](https://github.com/nodejs/node/commit/7d49bd0045)] - **doc**: add `clientCertEngine` to docs (Rich Trott)
- [[`7594032fac`](https://github.com/nodejs/node/commit/7594032fac)] - **doc**: add hashseed to collaborators (Yang Guo)
- [[`a256482318`](https://github.com/nodejs/node/commit/a256482318)] - **doc,test**: remove unnecessary await with return instances (Rich Trott) [#17265](https://github.com/nodejs/node/pull/17265)
- [[`bccdea623d`](https://github.com/nodejs/node/commit/bccdea623d)] - **doc,win**: clarify WSL support (João Reis) [#17008](https://github.com/nodejs/node/pull/17008)
- [[`9b16e15f44`](https://github.com/nodejs/node/commit/9b16e15f44)] - **domain**: re-implement domain over async_hook (vladimir) [#16222](https://github.com/nodejs/node/pull/16222)
- [[`9c2f24e288`](https://github.com/nodejs/node/commit/9c2f24e288)] - **errors**: fix typo in TLS_SESSION_ATTACK message (Tom Hallam) [#17388](https://github.com/nodejs/node/pull/17388)
- [[`a333e71342`](https://github.com/nodejs/node/commit/a333e71342)] - **errors**: consistent format for error message (Anatoli Papirovski) [#16904](https://github.com/nodejs/node/pull/16904)
- [[`715baf8214`](https://github.com/nodejs/node/commit/715baf8214)] - **fs**: use rest param & Reflect.apply in makeCallback (Mithun Sasidharan) [#17486](https://github.com/nodejs/node/pull/17486)
- [[`7ebaf83602`](https://github.com/nodejs/node/commit/7ebaf83602)] - **fs**: use arrow functions instead of `.bind` and `self` (Weijia Wang) [#17137](https://github.com/nodejs/node/pull/17137)
- [[`24dc57bc71`](https://github.com/nodejs/node/commit/24dc57bc71)] - **http**: simplify checkIsHttpToken() (Rich Trott) [#17399](https://github.com/nodejs/node/pull/17399)
- [[`5a4b6c4bc0`](https://github.com/nodejs/node/commit/5a4b6c4bc0)] - **http**: do not assign intermediate variable (Jon Moss) [#17335](https://github.com/nodejs/node/pull/17335)
- [[`a6b6acb68c`](https://github.com/nodejs/node/commit/a6b6acb68c)] - **http, stream**: writeHWM -\> writableHighWaterMark (Matteo Collina) [#17050](https://github.com/nodejs/node/pull/17050)
- [[`658338e317`](https://github.com/nodejs/node/commit/658338e317)] - **http2**: use more descriptive names (James M Snell) [#17328](https://github.com/nodejs/node/pull/17328)
- [[`4994d57890`](https://github.com/nodejs/node/commit/4994d57890)] - **http2**: remove unnecessary event handlers (James M Snell) [#17328](https://github.com/nodejs/node/pull/17328)
- [[`67abc1e697`](https://github.com/nodejs/node/commit/67abc1e697)] - **http2**: reduce code duplication in settings (James M Snell) [#17328](https://github.com/nodejs/node/pull/17328)
- [[`e5f92cda7e`](https://github.com/nodejs/node/commit/e5f92cda7e)] - **http2**: general cleanups (James M Snell) [#17328](https://github.com/nodejs/node/pull/17328)
- [[`54cd7dfd88`](https://github.com/nodejs/node/commit/54cd7dfd88)] - **inspector**: Fix crash for WS connection (Eugene Ostroukhov) [#17085](https://github.com/nodejs/node/pull/17085)
- [[`94e0488a33`](https://github.com/nodejs/node/commit/94e0488a33)] - **inspector**: no async tracking for promises (Anna Henningsen) [#17118](https://github.com/nodejs/node/pull/17118)
- [[`8fd316f63b`](https://github.com/nodejs/node/commit/8fd316f63b)] - **internal**: add emitExperimentalWarning function (Cody Deckard) [#16497](https://github.com/nodejs/node/pull/16497)
- [[`1a8b0e9fa5`](https://github.com/nodejs/node/commit/1a8b0e9fa5)] - **lib**: replace string concatenation with template (Vijayalakshmi Kannan) [#16923](https://github.com/nodejs/node/pull/16923)
- [[`b719b77215`](https://github.com/nodejs/node/commit/b719b77215)] - **module**: print better message on esm syntax error (Ben Noordhuis) [#17281](https://github.com/nodejs/node/pull/17281)
- [[`5736dc4ab9`](https://github.com/nodejs/node/commit/5736dc4ab9)] - **module**: fix for #17130 shared loader cjs dep (Guy Bedford) [#17131](https://github.com/nodejs/node/pull/17131)
- [[`06da8a7f16`](https://github.com/nodejs/node/commit/06da8a7f16)] - **module**: be lazy when creating CJS facades (Bradley Farias) [#17153](https://github.com/nodejs/node/pull/17153)
- [[`7ae7124039`](https://github.com/nodejs/node/commit/7ae7124039)] - **(SEMVER-MINOR)** **module**: add builtinModules (Jon Moss) [#16386](https://github.com/nodejs/node/pull/16386)
- [[`caff930d47`](https://github.com/nodejs/node/commit/caff930d47)] - **module**: replace default paths in require.resolve() (cjihrig) [#17113](https://github.com/nodejs/node/pull/17113)
- [[`b833a5989c`](https://github.com/nodejs/node/commit/b833a5989c)] - **n-api**: use nullptr instead of NULL in node_api.cc (Daniel Bevenius) [#17276](https://github.com/nodejs/node/pull/17276)
- [[`8d222d42ab`](https://github.com/nodejs/node/commit/8d222d42ab)] - **(SEMVER-MINOR)** **n-api**: add helper for addons to get the event loop (Anna Henningsen) [#17109](https://github.com/nodejs/node/pull/17109)
- [[`8366a74bbf`](https://github.com/nodejs/node/commit/8366a74bbf)] - **path**: remove obsolete comment (Rich Trott) [#17023](https://github.com/nodejs/node/pull/17023)
- [[`a159a2c6ac`](https://github.com/nodejs/node/commit/a159a2c6ac)] - **process**: slight refinements to nextTick (Anatoli Papirovski) [#17421](https://github.com/nodejs/node/pull/17421)
- [[`347164a703`](https://github.com/nodejs/node/commit/347164a703)] - **(SEMVER-MINOR)** **process**: add flag for uncaught exception abort (Anna Henningsen) [#17159](https://github.com/nodejs/node/pull/17159)
- [[`9d657247df`](https://github.com/nodejs/node/commit/9d657247df)] - **process**: slightly simplify next tick execution (Anatoli Papirovski) [#16888](https://github.com/nodejs/node/pull/16888)
- [[`8d90db5120`](https://github.com/nodejs/node/commit/8d90db5120)] - **(SEMVER-MINOR)** **process**: Send signal name to signal handlers (Robert Rossmann) [#15606](https://github.com/nodejs/node/pull/15606)
- [[`9a9aa88797`](https://github.com/nodejs/node/commit/9a9aa88797)] - **process**: improve unhandled rejection message (Madara Uchiha) [#17158](https://github.com/nodejs/node/pull/17158)
- [[`8dcc40a84f`](https://github.com/nodejs/node/commit/8dcc40a84f)] - **src**: remove unused include node_crypto_clienthello (Daniel Bevenius) [#17546](https://github.com/nodejs/node/pull/17546)
- [[`fb3ea4c4dc`](https://github.com/nodejs/node/commit/fb3ea4c4dc)] - **src**: fix missing handlescope bug in inspector (Ben Noordhuis) [#17539](https://github.com/nodejs/node/pull/17539)
- [[`40acda2e6b`](https://github.com/nodejs/node/commit/40acda2e6b)] - **src**: use uv_os_getpid() to get process id (cjihrig) [#17415](https://github.com/nodejs/node/pull/17415)
- [[`9b41c0b021`](https://github.com/nodejs/node/commit/9b41c0b021)] - **src**: node_http2_state.h should not be executable (Jon Moss) [#17408](https://github.com/nodejs/node/pull/17408)
- [[`419cde79b1`](https://github.com/nodejs/node/commit/419cde79b1)] - **src**: use non-deprecated versions of `->To*()` utils (Leko) [#17343](https://github.com/nodejs/node/pull/17343)
- [[`ceda8c57aa`](https://github.com/nodejs/node/commit/ceda8c57aa)] - **src**: use nullptr instead of NULL (Daniel Bevenius) [#17373](https://github.com/nodejs/node/pull/17373)
- [[`7f55f98a84`](https://github.com/nodejs/node/commit/7f55f98a84)] - **src**: fix typo in NODE_OPTIONS whitelist (Evan Lucas) [#17369](https://github.com/nodejs/node/pull/17369)
- [[`9b27bc85ae`](https://github.com/nodejs/node/commit/9b27bc85ae)] - **src**: introduce USE() for silencing compiler warnings (Anna Henningsen) [#17333](https://github.com/nodejs/node/pull/17333)
- [[`0db1f87825`](https://github.com/nodejs/node/commit/0db1f87825)] - **src**: use NODE_BUILTIN_MODULE_CONTEXT_AWARE() macro (Ben Noordhuis) [#17071](https://github.com/nodejs/node/pull/17071)
- [[`6a7a59a8c1`](https://github.com/nodejs/node/commit/6a7a59a8c1)] - **src**: remove `ClearFatalExceptionHandlers()` (Anna Henningsen) [#17333](https://github.com/nodejs/node/pull/17333)
- [[`9c7a42a2e4`](https://github.com/nodejs/node/commit/9c7a42a2e4)] - **src**: explicitly register built-in modules (Yihong Wang) [#16565](https://github.com/nodejs/node/pull/16565)
- [[`4667c5e720`](https://github.com/nodejs/node/commit/4667c5e720)] - **src**: start heap object tracking after platform is initialized (Hannes Payer) [#17249](https://github.com/nodejs/node/pull/17249)
- [[`63f6947a41`](https://github.com/nodejs/node/commit/63f6947a41)] - **src**: make base64.h self-contained (Daniel Bevenius) [#17177](https://github.com/nodejs/node/pull/17177)
- [[`14ebda5218`](https://github.com/nodejs/node/commit/14ebda5218)] - **(SEMVER-MINOR)** **src**: add public API for managing NodePlatform (Cheng Zhao) [#16981](https://github.com/nodejs/node/pull/16981)
- [[`9832b8e206`](https://github.com/nodejs/node/commit/9832b8e206)] - **src**: add napi_handle_scope_mismatch to msg list (neta) [#17161](https://github.com/nodejs/node/pull/17161)
- [[`0b128842f6`](https://github.com/nodejs/node/commit/0b128842f6)] - **src**: fix compiler warning (cjihrig) [#17195](https://github.com/nodejs/node/pull/17195)
- [[`9c0c33625a`](https://github.com/nodejs/node/commit/9c0c33625a)] - **src**: remove unprofessional slang in assertions (Alexey Orlenko) [#17166](https://github.com/nodejs/node/pull/17166)
- [[`936c0b2b83`](https://github.com/nodejs/node/commit/936c0b2b83)] - **src**: implement v8::TaskRunner API in NodePlatform (Anna Henningsen) [#17134](https://github.com/nodejs/node/pull/17134)
- [[`a9be7bf35b`](https://github.com/nodejs/node/commit/a9be7bf35b)] - **src**: remove unused variable (cjihrig) [#17150](https://github.com/nodejs/node/pull/17150)
- [[`84b707089e`](https://github.com/nodejs/node/commit/84b707089e)] - **(SEMVER-MINOR)** **src**: add helper for addons to get the event loop (Anna Henningsen) [#17109](https://github.com/nodejs/node/pull/17109)
- [[`362b8c7d5d`](https://github.com/nodejs/node/commit/362b8c7d5d)] - **src**: inspector context name = program title + pid (Ben Noordhuis) [#17087](https://github.com/nodejs/node/pull/17087)
- [[`7ecec6704f`](https://github.com/nodejs/node/commit/7ecec6704f)] - **src**: abstract getpid() operation (Ben Noordhuis) [#17087](https://github.com/nodejs/node/pull/17087)
- [[`e7db034571`](https://github.com/nodejs/node/commit/e7db034571)] - **src**: add NODE_VERSION_IS_LTS to node_version.h (Gibson Fahnestock) [#16697](https://github.com/nodejs/node/pull/16697)
- [[`60423f5845`](https://github.com/nodejs/node/commit/60423f5845)] - **src**: use unique_ptr for http2_state (Franziska Hinkelmann) [#17078](https://github.com/nodejs/node/pull/17078)
- [[`e9000901ca`](https://github.com/nodejs/node/commit/e9000901ca)] - **src**: add missing include in node_platform.h (Anna Henningsen) [#17133](https://github.com/nodejs/node/pull/17133)
- [[`1b76cfe3c2`](https://github.com/nodejs/node/commit/1b76cfe3c2)] - **src**: use unique_ptr for scheduled delayed tasks (Franziska Hinkelmann) [#17083](https://github.com/nodejs/node/pull/17083)
- [[`af63df80b4`](https://github.com/nodejs/node/commit/af63df80b4)] - **src**: use std::unique_ptr in base-object-inl.h (Franziska Hinkelmann) [#17079](https://github.com/nodejs/node/pull/17079)
- [[`4387a73514`](https://github.com/nodejs/node/commit/4387a73514)] - **src**: remove superfluous check in backtrace_posix.cc (Anna Henningsen) [#16950](https://github.com/nodejs/node/pull/16950)
- [[`3ab3b0d4e2`](https://github.com/nodejs/node/commit/3ab3b0d4e2)] - **src**: fix size of CounterSet (Witthawat Piwawatthanapanit) [#16984](https://github.com/nodejs/node/pull/16984)
- [[`d74c7c5461`](https://github.com/nodejs/node/commit/d74c7c5461)] - **src**: rename req-wrap -\> req_wrap (Daniel Bevenius) [#17022](https://github.com/nodejs/node/pull/17022)
- [[`5119bb1a6d`](https://github.com/nodejs/node/commit/5119bb1a6d)] - **src**: rename base-object -\> base_object (Daniel Bevenius) [#17022](https://github.com/nodejs/node/pull/17022)
- [[`8ba513ee2e`](https://github.com/nodejs/node/commit/8ba513ee2e)] - **src**: rename async-wrap -\> async_wrap (Daniel Bevenius) [#17022](https://github.com/nodejs/node/pull/17022)
- [[`da8414e09a`](https://github.com/nodejs/node/commit/da8414e09a)] - **src**: use smart pointer instead of new and delete (Franziska Hinkelmann) [#17020](https://github.com/nodejs/node/pull/17020)
- [[`17e31dc66a`](https://github.com/nodejs/node/commit/17e31dc66a)] - **src**: perf_hooks: fix wrong sized delete (Ali Ijaz Sheikh) [#16898](https://github.com/nodejs/node/pull/16898)
- [[`a1a99570aa`](https://github.com/nodejs/node/commit/a1a99570aa)] - **src**: make ownership of stdio_pipes explicit (Franziska Hinkelmann) [#17030](https://github.com/nodejs/node/pull/17030)
- [[`98a07709f4`](https://github.com/nodejs/node/commit/98a07709f4)] - **src**: use unique pointer for tracing_agent (Franziska Hinkelmann) [#17012](https://github.com/nodejs/node/pull/17012)
- [[`a05c49c48d`](https://github.com/nodejs/node/commit/a05c49c48d)] - **src**: use unique_ptr for requests in crypto (Franziska Hinkelmann) [#17000](https://github.com/nodejs/node/pull/17000)
- [[`6f805c6967`](https://github.com/nodejs/node/commit/6f805c6967)] - **src**: implement backtrace-on-abort for windows (Anna Henningsen) [#16951](https://github.com/nodejs/node/pull/16951)
- [[`7ac760b603`](https://github.com/nodejs/node/commit/7ac760b603)] - **src**: fix SetClientCertEngine() nullptr dereference (Ben Noordhuis) [#16965](https://github.com/nodejs/node/pull/16965)
- [[`f6ec5fa4e8`](https://github.com/nodejs/node/commit/f6ec5fa4e8)] - **src**: fix bad sizeof expression (Ben Noordhuis) [#17014](https://github.com/nodejs/node/pull/17014)
- [[`8522e2420d`](https://github.com/nodejs/node/commit/8522e2420d)] - **src**: use unique_ptr in platform implementation (Franziska Hinkelmann) [#16970](https://github.com/nodejs/node/pull/16970)
- [[`c2431d553b`](https://github.com/nodejs/node/commit/c2431d553b)] - **src**: cancel pending delayed platform tasks on exit (Anna Henningsen) [#16700](https://github.com/nodejs/node/pull/16700)
- [[`37a60a8c3c`](https://github.com/nodejs/node/commit/37a60a8c3c)] - **src**: prepare v8 platform for multi-isolate support (Anna Henningsen) [#16700](https://github.com/nodejs/node/pull/16700)
- [[`b36c726206`](https://github.com/nodejs/node/commit/b36c726206)] - **stream**: improve the error message of `ERR_INVALID_ARG_TYPE` (Weijia Wang) [#17145](https://github.com/nodejs/node/pull/17145)
- [[`78b82b03c5`](https://github.com/nodejs/node/commit/78b82b03c5)] - **stream**: use arrow fns for 'this' in readable (Vipin Menon) [#16927](https://github.com/nodejs/node/pull/16927)
- [[`edb9846884`](https://github.com/nodejs/node/commit/edb9846884)] - **(SEMVER-MINOR)** **stream**: remove usage of \*State.highWaterMark (Calvin Metcalf) [#12860](https://github.com/nodejs/node/pull/12860)
- [[`e7ae8eb457`](https://github.com/nodejs/node/commit/e7ae8eb457)] - **test**: refactor test-child-process-pass-fd (Rich Trott) [#17596](https://github.com/nodejs/node/pull/17596)
- [[`5a9172fe06`](https://github.com/nodejs/node/commit/5a9172fe06)] - **test**: remove unnecessary use of common.PORT in addons test (Rich Trott) [#17563](https://github.com/nodejs/node/pull/17563)
- [[`39e2fb6ad4`](https://github.com/nodejs/node/commit/39e2fb6ad4)] - **test**: simplify common.PORT code (Rich Trott) [#17559](https://github.com/nodejs/node/pull/17559)
- [[`f45ef442bb`](https://github.com/nodejs/node/commit/f45ef442bb)] - **test**: refactor test-http-default-port (Anna Henningsen) [#17562](https://github.com/nodejs/node/pull/17562)
- [[`49d662846e`](https://github.com/nodejs/node/commit/49d662846e)] - **test**: replace assert.throws w/ common.expectsError (Anatoli Papirovski) [#17557](https://github.com/nodejs/node/pull/17557)
- [[`f7e5ab082d`](https://github.com/nodejs/node/commit/f7e5ab082d)] - **test**: refactored to remove unnecessary variables (Mithun Sasidharan) [#17553](https://github.com/nodejs/node/pull/17553)
- [[`bb780d2d84`](https://github.com/nodejs/node/commit/bb780d2d84)] - **test**: use Countdown in http-agent test (Federico Kauffman) [#17537](https://github.com/nodejs/node/pull/17537)
- [[`510116ebe6`](https://github.com/nodejs/node/commit/510116ebe6)] - **test**: update http test to use common.mustCall (Collins Abitekaniza) [#17528](https://github.com/nodejs/node/pull/17528)
- [[`39d8e4413a`](https://github.com/nodejs/node/commit/39d8e4413a)] - **test**: improve assert messages in repl-reset-event (Adri Van Houdt) [#16836](https://github.com/nodejs/node/pull/16836)
- [[`6576382eaa`](https://github.com/nodejs/node/commit/6576382eaa)] - **test**: update test-http-should-keep-alive to use countdown (TomerOmri) [#17505](https://github.com/nodejs/node/pull/17505)
- [[`f3d619882e`](https://github.com/nodejs/node/commit/f3d619882e)] - **test**: fix flaky test-benchmark-es (Rich Trott) [#17516](https://github.com/nodejs/node/pull/17516)
- [[`ff59d3a30e`](https://github.com/nodejs/node/commit/ff59d3a30e)] - **test**: replace assert.throws w/ common.expectsError (Mithun Sasidharan) [#17483](https://github.com/nodejs/node/pull/17483)
- [[`28b2d8ac20`](https://github.com/nodejs/node/commit/28b2d8ac20)] - **test**: use common.expectsError in tests (Mithun Sasidharan) [#17484](https://github.com/nodejs/node/pull/17484)
- [[`d15cdc6fdb`](https://github.com/nodejs/node/commit/d15cdc6fdb)] - **test**: replace assert.throws w/ common.expectsError (Mithun Sasidharan) [#17498](https://github.com/nodejs/node/pull/17498)
- [[`993b1cbc6d`](https://github.com/nodejs/node/commit/993b1cbc6d)] - **test**: use Countdown in http test (idandagan1) [#17506](https://github.com/nodejs/node/pull/17506)
- [[`1aae28b7c9`](https://github.com/nodejs/node/commit/1aae28b7c9)] - **test**: use Number.isNaN instead of global isNaN (Mithun Sasidharan) [#17515](https://github.com/nodejs/node/pull/17515)
- [[`2a5da9c2c9`](https://github.com/nodejs/node/commit/2a5da9c2c9)] - **test**: use Countdown in http-response-statuscode (Mandeep Singh) [#17327](https://github.com/nodejs/node/pull/17327)
- [[`919625bd6a`](https://github.com/nodejs/node/commit/919625bd6a)] - **test**: use Countdown in test-http-set-cookies (Shilo Mangam) [#17504](https://github.com/nodejs/node/pull/17504)
- [[`f399667784`](https://github.com/nodejs/node/commit/f399667784)] - **test**: replace assert.throws w/ common.expectsError (Mithun Sasidharan) [#17497](https://github.com/nodejs/node/pull/17497)
- [[`c2ff36ed7f`](https://github.com/nodejs/node/commit/c2ff36ed7f)] - **test**: replace assert.throws w/ common.expectsError (Mithun Sasidharan) [#17494](https://github.com/nodejs/node/pull/17494)
- [[`af8e27d10e`](https://github.com/nodejs/node/commit/af8e27d10e)] - **test**: Use common.mustCall in http test (sreepurnajasti) [#17487](https://github.com/nodejs/node/pull/17487)
- [[`7b8622f946`](https://github.com/nodejs/node/commit/7b8622f946)] - **test**: update http test to use Countdown (Francisco Gerardo Neri Andriano) [#17477](https://github.com/nodejs/node/pull/17477)
- [[`fb553b5b59`](https://github.com/nodejs/node/commit/fb553b5b59)] - **test**: improve crypto test coverage (Leko) [#17426](https://github.com/nodejs/node/pull/17426)
- [[`928aecc92c`](https://github.com/nodejs/node/commit/928aecc92c)] - **test**: replace fs.accessSync with fs.existsSync (Leko) [#17446](https://github.com/nodejs/node/pull/17446)
- [[`7d3a84388d`](https://github.com/nodejs/node/commit/7d3a84388d)] - **test**: fix flaky test-benchmark-querystring (Rich Trott) [#17517](https://github.com/nodejs/node/pull/17517)
- [[`50f120eaac`](https://github.com/nodejs/node/commit/50f120eaac)] - **test**: fix flaky test-benchmark-util (Rich Trott) [#17473](https://github.com/nodejs/node/pull/17473)
- [[`a407a48bdf`](https://github.com/nodejs/node/commit/a407a48bdf)] - **test**: expand coverage for crypto (Leko) [#17447](https://github.com/nodejs/node/pull/17447)
- [[`07547346a8`](https://github.com/nodejs/node/commit/07547346a8)] - **test**: add common.crashOnUnhandledRejection() (IHsuan) [#17247](https://github.com/nodejs/node/pull/17247)
- [[`8c32b4a37a`](https://github.com/nodejs/node/commit/8c32b4a37a)] - **test**: refactor code to use common.mustCall (Mithun Sasidharan) [#17437](https://github.com/nodejs/node/pull/17437)
- [[`fe9d9f732b`](https://github.com/nodejs/node/commit/fe9d9f732b)] - **test**: remove hidden use of common.PORT in parallel tests (Rich Trott) [#17466](https://github.com/nodejs/node/pull/17466)
- [[`cca3526faf`](https://github.com/nodejs/node/commit/cca3526faf)] - **test**: add more settings to test-benchmark-dgram (Rich Trott) [#17462](https://github.com/nodejs/node/pull/17462)
- [[`562007ce2a`](https://github.com/nodejs/node/commit/562007ce2a)] - **test**: add dgram benchmark test (jopann) [#17462](https://github.com/nodejs/node/pull/17462)
- [[`619cbc4364`](https://github.com/nodejs/node/commit/619cbc4364)] - **test**: fix flaky test-benchmark-events (Rich Trott) [#17472](https://github.com/nodejs/node/pull/17472)
- [[`d8018bc91d`](https://github.com/nodejs/node/commit/d8018bc91d)] - **test**: update test-http-request-dont-override-options to use common.mustCall (Mithun Sasidharan) [#17438](https://github.com/nodejs/node/pull/17438)
- [[`0ac87c2525`](https://github.com/nodejs/node/commit/0ac87c2525)] - **test**: replace assert.throws with common.expectsError (Leko) [#17445](https://github.com/nodejs/node/pull/17445)
- [[`07fd4cfbe0`](https://github.com/nodejs/node/commit/07fd4cfbe0)] - **test**: use common.mustCall in test-http-malformed-request (Mithun Sasidharan) [#17439](https://github.com/nodejs/node/pull/17439)
- [[`0ade4888f2`](https://github.com/nodejs/node/commit/0ade4888f2)] - **test**: forbid `common.mustCall*()` in process exit handlers (Rich Trott) [#17453](https://github.com/nodejs/node/pull/17453)
- [[`85e6271995`](https://github.com/nodejs/node/commit/85e6271995)] - **test**: use Countdown in http test (Mithun Sasidharan) [#17436](https://github.com/nodejs/node/pull/17436)
- [[`8c81ba0b1c`](https://github.com/nodejs/node/commit/8c81ba0b1c)] - **test**: remove common.PORT from parallel tests (Rich Trott) [#17410](https://github.com/nodejs/node/pull/17410)
- [[`5fecdbaca9`](https://github.com/nodejs/node/commit/5fecdbaca9)] - **test**: update test-http-response-multiheaders to use countdown (hmammedzadeh) [#17419](https://github.com/nodejs/node/pull/17419)
- [[`69e775d454`](https://github.com/nodejs/node/commit/69e775d454)] - **test**: update test-http-timeout to use countdown (Mithun Sasidharan) [#17341](https://github.com/nodejs/node/pull/17341)
- [[`9cbb0dadc0`](https://github.com/nodejs/node/commit/9cbb0dadc0)] - **test**: make common.mustNotCall show file:linenumber (Lance Ball) [#17257](https://github.com/nodejs/node/pull/17257)
- [[`259f2d331d`](https://github.com/nodejs/node/commit/259f2d331d)] - **test**: remove fixturesDir from common module (Rich Trott) [#17400](https://github.com/nodejs/node/pull/17400)
- [[`92b29cd659`](https://github.com/nodejs/node/commit/92b29cd659)] - **test**: remove common.fixturesDir from tests (Rich Trott) [#17400](https://github.com/nodejs/node/pull/17400)
- [[`0afcea280e`](https://github.com/nodejs/node/commit/0afcea280e)] - **test**: add test case for missing branch (Leko) [#17418](https://github.com/nodejs/node/pull/17418)
- [[`c9a4f4f8f1`](https://github.com/nodejs/node/commit/c9a4f4f8f1)] - **test**: update test-http-upgrade-client to use countdown (Mithun Sasidharan) [#17339](https://github.com/nodejs/node/pull/17339)
- [[`91d541627e`](https://github.com/nodejs/node/commit/91d541627e)] - **test**: update test-http-status-reason-invalid-chars to use countdown (Mithun Sasidharan) [#17342](https://github.com/nodejs/node/pull/17342)
- [[`4fb070873e`](https://github.com/nodejs/node/commit/4fb070873e)] - **test**: refactored test-http-allow-req-after-204-res to countdown (Mithun Sasidharan) [#17211](https://github.com/nodejs/node/pull/17211)
- [[`ef25de7493`](https://github.com/nodejs/node/commit/ef25de7493)] - **test**: update test/parallel/test-http-pipe-fs.js to use countdown (ChungNgoops) [#17346](https://github.com/nodejs/node/pull/17346)
- [[`1866b05042`](https://github.com/nodejs/node/commit/1866b05042)] - **test**: refactored test-http-response-splitting to use countdown (Mithun Sasidharan) [#17348](https://github.com/nodejs/node/pull/17348)
- [[`ee1c95f992`](https://github.com/nodejs/node/commit/ee1c95f992)] - **test**: expanded assertions for console.timeEnd() output (NiveditN) [#17368](https://github.com/nodejs/node/pull/17368)
- [[`8336e4f88e`](https://github.com/nodejs/node/commit/8336e4f88e)] - **test**: add test case for process.dlopen with undefined (Leko) [#17343](https://github.com/nodejs/node/pull/17343)
- [[`f0608814af`](https://github.com/nodejs/node/commit/f0608814af)] - **test**: add test case for throwing an exception with vm.Script (Leko) [#17343](https://github.com/nodejs/node/pull/17343)
- [[`78592a34c6`](https://github.com/nodejs/node/commit/78592a34c6)] - **test**: make CreateParams stack-allocated (Daniel Bevenius) [#17366](https://github.com/nodejs/node/pull/17366)
- [[`ca81d4bb3f`](https://github.com/nodejs/node/commit/ca81d4bb3f)] - **test**: use v8 Default Allocator in cctest fixture (Daniel Bevenius) [#17366](https://github.com/nodejs/node/pull/17366)
- [[`6e3a8be43a`](https://github.com/nodejs/node/commit/6e3a8be43a)] - **test**: replace function with arrow function (Leko) [#17345](https://github.com/nodejs/node/pull/17345)
- [[`f5a1e6cbc4`](https://github.com/nodejs/node/commit/f5a1e6cbc4)] - **test**: fix flaky async-hooks/test-graph.signal (Rich Trott) [#17509](https://github.com/nodejs/node/pull/17509)
- [[`f1b26be684`](https://github.com/nodejs/node/commit/f1b26be684)] - **test**: remove common.tmpDirName (Rich Trott) [#17266](https://github.com/nodejs/node/pull/17266)
- [[`047bac2475`](https://github.com/nodejs/node/commit/047bac2475)] - **test**: fixup test-http2-create-client-secure-session (James M Snell) [#17328](https://github.com/nodejs/node/pull/17328)
- [[`3d45a94b56`](https://github.com/nodejs/node/commit/3d45a94b56)] - **test**: mock the lookup function in parallel tests (Joyee Cheung) [#17296](https://github.com/nodejs/node/pull/17296)
- [[`4e789a3bf8`](https://github.com/nodejs/node/commit/4e789a3bf8)] - **test**: add common.dns.errorLookupMock (Joyee Cheung) [#17296](https://github.com/nodejs/node/pull/17296)
- [[`71eb186572`](https://github.com/nodejs/node/commit/71eb186572)] - **test**: replace function with ES6 arrow function (Junichi Kajiwara) [#17306](https://github.com/nodejs/node/pull/17306)
- [[`36e2643d7b`](https://github.com/nodejs/node/commit/36e2643d7b)] - **test**: add es6 module global leakage tests (WhoMeNope) [#16341](https://github.com/nodejs/node/pull/16341)
- [[`afdfc4de8f`](https://github.com/nodejs/node/commit/afdfc4de8f)] - **test**: Enable specifying flaky tests on fips (Nikhil Komawar) [#16329](https://github.com/nodejs/node/pull/16329)
- [[`24d08fee45`](https://github.com/nodejs/node/commit/24d08fee45)] - **test**: refactored http test to use countdown (Mithun Sasidharan) [#17241](https://github.com/nodejs/node/pull/17241)
- [[`b033d38022`](https://github.com/nodejs/node/commit/b033d38022)] - **test**: Update test-http-parser-free to use countdown timer (Mandeep Singh) [#17322](https://github.com/nodejs/node/pull/17322)
- [[`4a749c3a70`](https://github.com/nodejs/node/commit/4a749c3a70)] - **test**: Update test-http-client-agent to use countdown timer (Mandeep Singh) [#17325](https://github.com/nodejs/node/pull/17325)
- [[`1e3aed0be3`](https://github.com/nodejs/node/commit/1e3aed0be3)] - **test**: fix flaky parallel/test-http2-client-upload (Anna Henningsen) [#17361](https://github.com/nodejs/node/pull/17361)
- [[`1adccc6a6a`](https://github.com/nodejs/node/commit/1adccc6a6a)] - **test**: fix isNAN-\>Number.isNAN (yuza yuko) [#17309](https://github.com/nodejs/node/pull/17309)
- [[`91e21171c7`](https://github.com/nodejs/node/commit/91e21171c7)] - **test**: make use of Number.isNaN to test-readfloat.js (Hiromu Yoshiwara) [#17310](https://github.com/nodejs/node/pull/17310)
- [[`97a279e375`](https://github.com/nodejs/node/commit/97a279e375)] - **test**: replace function with arrow function (spring_raining) [#17312](https://github.com/nodejs/node/pull/17312)
- [[`e35acedca5`](https://github.com/nodejs/node/commit/e35acedca5)] - **test**: refactor using template string (Yoshiya Hinosawa) [#17314](https://github.com/nodejs/node/pull/17314)
- [[`f51cb1c0cd`](https://github.com/nodejs/node/commit/f51cb1c0cd)] - **test**: replace function with arrow function (Hiroaki KARASAWA) [#17308](https://github.com/nodejs/node/pull/17308)
- [[`3f4d0fc76b`](https://github.com/nodejs/node/commit/3f4d0fc76b)] - **test**: replace function with arrow function (kou-hin) [#17305](https://github.com/nodejs/node/pull/17305)
- [[`d8e4d9593b`](https://github.com/nodejs/node/commit/d8e4d9593b)] - **test**: use arrow function (koooge) [#17318](https://github.com/nodejs/node/pull/17318)
- [[`b420209fc6`](https://github.com/nodejs/node/commit/b420209fc6)] - **test**: use common.hasIntl instead of typeof Intl (Aqui Tsuchida) [#17311](https://github.com/nodejs/node/pull/17311)
- [[`284dad7468`](https://github.com/nodejs/node/commit/284dad7468)] - **test**: use Number.isNaN() (MURAKAMI Masahiko) [#17319](https://github.com/nodejs/node/pull/17319)
- [[`94abefba93`](https://github.com/nodejs/node/commit/94abefba93)] - **test**: add test of stream Transform (Yoshiya Hinosawa) [#17303](https://github.com/nodejs/node/pull/17303)
- [[`e026132726`](https://github.com/nodejs/node/commit/e026132726)] - **test**: refactor concat string to template string (jimmy) [#17252](https://github.com/nodejs/node/pull/17252)
- [[`0e5ff6f44b`](https://github.com/nodejs/node/commit/0e5ff6f44b)] - **test**: use common.crashOnUnhandledRejection (yozian) [#17242](https://github.com/nodejs/node/pull/17242)
- [[`24b1839aed`](https://github.com/nodejs/node/commit/24b1839aed)] - **test**: use common.crashOnUnhandledRejection (Kcin1993) [#17235](https://github.com/nodejs/node/pull/17235)
- [[`497195a1a3`](https://github.com/nodejs/node/commit/497195a1a3)] - **test**: add common.crashOnUnhandledRejection() (Andy Chen) [#17234](https://github.com/nodejs/node/pull/17234)
- [[`c375816667`](https://github.com/nodejs/node/commit/c375816667)] - **test**: use common.crashOnUnhandledRejection (zhengyuanjie) [#17215](https://github.com/nodejs/node/pull/17215)
- [[`cb3348715b`](https://github.com/nodejs/node/commit/cb3348715b)] - **test**: use common.crashOnUnhandledRejection (Jason Chung) [#17233](https://github.com/nodejs/node/pull/17233)
- [[`8d1ec5d24a`](https://github.com/nodejs/node/commit/8d1ec5d24a)] - **test**: use common.crashOnUnhandledRejection() (<EMAIL>) [#17232](https://github.com/nodejs/node/pull/17232)
- [[`e3db509b47`](https://github.com/nodejs/node/commit/e3db509b47)] - **test**: use common.crashOnUnhandledRejection (Kurt Hsu) [#17229](https://github.com/nodejs/node/pull/17229)
- [[`017379e89b`](https://github.com/nodejs/node/commit/017379e89b)] - **test**: add common.crashOnHandleRejection (jackyen) [#17225](https://github.com/nodejs/node/pull/17225)
- [[`ce284fcb5d`](https://github.com/nodejs/node/commit/ce284fcb5d)] - **test**: add crashonUnhandledRejection (danielLin) [#17237](https://github.com/nodejs/node/pull/17237)
- [[`5cbe0f2420`](https://github.com/nodejs/node/commit/5cbe0f2420)] - **test**: keep coverage reports after coverage-clean (Anatoli Papirovski) [#15470](https://github.com/nodejs/node/pull/15470)
- [[`2d2e7803b2`](https://github.com/nodejs/node/commit/2d2e7803b2)] - **test**: add test on unhandled rejection (Larry Lu) [#17228](https://github.com/nodejs/node/pull/17228)
- [[`a536b031d8`](https://github.com/nodejs/node/commit/a536b031d8)] - **test**: use common.crashOnUnhandledRejection (aryung chen) [#17221](https://github.com/nodejs/node/pull/17221)
- [[`2010b800b8`](https://github.com/nodejs/node/commit/2010b800b8)] - **test**: use common.crashOnUnhandledRejection (Zack Yang) [#17217](https://github.com/nodejs/node/pull/17217)
- [[`d50671b061`](https://github.com/nodejs/node/commit/d50671b061)] - **test**: add common.crashOnUnhandledRejection() (Scya597) [#17212](https://github.com/nodejs/node/pull/17212)
- [[`42a8f03a8b`](https://github.com/nodejs/node/commit/42a8f03a8b)] - **test**: remove unlink function which is needless (buji) [#17119](https://github.com/nodejs/node/pull/17119)
- [[`5c70cef403`](https://github.com/nodejs/node/commit/5c70cef403)] - **test**: dont need to remove nonexistent directory (buji) [#17119](https://github.com/nodejs/node/pull/17119)
- [[`696c962bf3`](https://github.com/nodejs/node/commit/696c962bf3)] - **test**: use common.crashOnUnhandledRejection() (Ivan Wei) [#17227](https://github.com/nodejs/node/pull/17227)
- [[`caa59b9a47`](https://github.com/nodejs/node/commit/caa59b9a47)] - **test**: add common.crashOnUnhandledRejection() (Kyle Yu) [#17236](https://github.com/nodejs/node/pull/17236)
- [[`c232542494`](https://github.com/nodejs/node/commit/c232542494)] - **test**: use crashOnUnhandledRejection (YuLun Shih) [#17220](https://github.com/nodejs/node/pull/17220)
- [[`63f9a13299`](https://github.com/nodejs/node/commit/63f9a13299)] - **test**: fix linting error (James M Snell) [#17251](https://github.com/nodejs/node/pull/17251)
- [[`dc4aa89224`](https://github.com/nodejs/node/commit/dc4aa89224)] - **test**: use common.crashOnUnhandledRejection (jimliu7434) [#17231](https://github.com/nodejs/node/pull/17231)
- [[`9bf2da3429`](https://github.com/nodejs/node/commit/9bf2da3429)] - **test**: use crashOnUnhandledRejection (Roth Peng) [#17226](https://github.com/nodejs/node/pull/17226)
- [[`582f1f01f8`](https://github.com/nodejs/node/commit/582f1f01f8)] - **test**: use common.crashOnUnhandledRejection (esbb48) [#17218](https://github.com/nodejs/node/pull/17218)
- [[`5cfd4ea3ed`](https://github.com/nodejs/node/commit/5cfd4ea3ed)] - **test**: use arrow function instead of bind (Lance Ball) [#17202](https://github.com/nodejs/node/pull/17202)
- [[`25ff8bef18`](https://github.com/nodejs/node/commit/25ff8bef18)] - **test**: use crashOnUnhandledRejection (Chiahao Lin) [#17219](https://github.com/nodejs/node/pull/17219)
- [[`965051dc14`](https://github.com/nodejs/node/commit/965051dc14)] - **test**: use common.crashOnUnhandledRejection (Whien) [#17214](https://github.com/nodejs/node/pull/17214)
- [[`72e480d85e`](https://github.com/nodejs/node/commit/72e480d85e)] - **test**: clean up inappropriate language (Gus Caplan) [#17170](https://github.com/nodejs/node/pull/17170)
- [[`c2bb4b211e`](https://github.com/nodejs/node/commit/c2bb4b211e)] - **test**: bypass dns for IPv6 net tests (Refael Ackermann) [#16976](https://github.com/nodejs/node/pull/16976)
- [[`417e7d1ac2`](https://github.com/nodejs/node/commit/417e7d1ac2)] - **test**: wrap callback in common.mustCall (suman-mitra) [#17173](https://github.com/nodejs/node/pull/17173)
- [[`b2c10cad51`](https://github.com/nodejs/node/commit/b2c10cad51)] - **test**: remove unused parameter in test-next-tick-error-spin.js (Francois KY) [#17185](https://github.com/nodejs/node/pull/17185)
- [[`2bbc1f070d`](https://github.com/nodejs/node/commit/2bbc1f070d)] - **test**: remove unused parameter (Fran Herrero) [#17193](https://github.com/nodejs/node/pull/17193)
- [[`c2b30a99b7`](https://github.com/nodejs/node/commit/c2b30a99b7)] - **test**: remove unused variable (Pierre-Loic Doulcet) [#17186](https://github.com/nodejs/node/pull/17186)
- [[`2e311266f7`](https://github.com/nodejs/node/commit/2e311266f7)] - **test**: remove unused variable (Guillaume Flandre) [#17187](https://github.com/nodejs/node/pull/17187)
- [[`a08bcaeca9`](https://github.com/nodejs/node/commit/a08bcaeca9)] - **test**: remove unused parameter (François Descamps) [#17184](https://github.com/nodejs/node/pull/17184)
- [[`36281f4003`](https://github.com/nodejs/node/commit/36281f4003)] - **test**: remove unused parameter (Xavier Balloy) [#17188](https://github.com/nodejs/node/pull/17188)
- [[`15b6bcf68b`](https://github.com/nodejs/node/commit/15b6bcf68b)] - **test**: make debugging of inspector-port-zero easier (Gibson Fahnestock) [#16685](https://github.com/nodejs/node/pull/16685)
- [[`9914bcaae9`](https://github.com/nodejs/node/commit/9914bcaae9)] - **test**: replace assert.throws w/ common.expectsError (sgreylyn) [#17091](https://github.com/nodejs/node/pull/17091)
- [[`e16d833076`](https://github.com/nodejs/node/commit/e16d833076)] - **test**: reduce benchmark cases in test-benchmark-buffer (Rich Trott) [#17111](https://github.com/nodejs/node/pull/17111)
- [[`79ba8637d0`](https://github.com/nodejs/node/commit/79ba8637d0)] - **test**: fs.write() if 3rd argument is a callback, not offset (Patrick Heneise) [#17045](https://github.com/nodejs/node/pull/17045)
- [[`23c98fa796`](https://github.com/nodejs/node/commit/23c98fa796)] - **test**: utilize common.mustCall() on child exit (sreepurnajasti) [#16996](https://github.com/nodejs/node/pull/16996)
- [[`2776816945`](https://github.com/nodejs/node/commit/2776816945)] - **test**: use arrow functions instead of bind (Tobias Nießen) [#17070](https://github.com/nodejs/node/pull/17070)
- [[`b9311697db`](https://github.com/nodejs/node/commit/b9311697db)] - **test**: move timing-sensitive test to sequential (Rich Trott) [#16775](https://github.com/nodejs/node/pull/16775)
- [[`acf6f24ef2`](https://github.com/nodejs/node/commit/acf6f24ef2)] - **test**: make REPL test pass in coverage mode (Anna Henningsen) [#17082](https://github.com/nodejs/node/pull/17082)
- [[`70060eef65`](https://github.com/nodejs/node/commit/70060eef65)] - **test**: --enable-static linked executable (Daniel Bevenius) [#14986](https://github.com/nodejs/node/pull/14986)
- [[`113dd2b573`](https://github.com/nodejs/node/commit/113dd2b573)] - **test**: add basic WebAssembly test (Steve Kinney) [#16760](https://github.com/nodejs/node/pull/16760)
- [[`f80cf5a33d`](https://github.com/nodejs/node/commit/f80cf5a33d)] - **test**: add coverage to tty module (cjihrig) [#16959](https://github.com/nodejs/node/pull/16959)
- [[`121245f25f`](https://github.com/nodejs/node/commit/121245f25f)] - **test**: add tls clientcertengine tests (Rich Trott)
- [[`3b1db7f54b`](https://github.com/nodejs/node/commit/3b1db7f54b)] - **test**: flag known flake (Refael Ackermann)
- [[`0093840044`](https://github.com/nodejs/node/commit/0093840044)] - **test,doc**: do not indicate that non-functions "return" values (Rich Trott) [#17267](https://github.com/nodejs/node/pull/17267)
- [[`b6929e2aa9`](https://github.com/nodejs/node/commit/b6929e2aa9)] - **test,doc**: document where common modules go (Gibson Fahnestock) [#16089](https://github.com/nodejs/node/pull/16089)
- [[`89d31ee048`](https://github.com/nodejs/node/commit/89d31ee048)] - **timers**: improvements to TimersList management (Anatoli Papirovski) [#17429](https://github.com/nodejs/node/pull/17429)
- [[`bd79c3788b`](https://github.com/nodejs/node/commit/bd79c3788b)] - **timers**: clean up for readability (Anatoli Papirovski) [#17279](https://github.com/nodejs/node/pull/17279)
- [[`fd501b31c6`](https://github.com/nodejs/node/commit/fd501b31c6)] - **timers**: cross JS/C++ border less frequently (Anna Henningsen) [#17064](https://github.com/nodejs/node/pull/17064)
- [[`33c1e8b3d5`](https://github.com/nodejs/node/commit/33c1e8b3d5)] - **tls**: implement clientCertEngine option (joelostrowski)
- [[`f7a1e39139`](https://github.com/nodejs/node/commit/f7a1e39139)] - **tools**: simplify no-let-in-for-declaration rule (cjihrig) [#17572](https://github.com/nodejs/node/pull/17572)
- [[`e157e1c922`](https://github.com/nodejs/node/commit/e157e1c922)] - **tools**: simplify buffer-constructor rule (cjihrig) [#17572](https://github.com/nodejs/node/pull/17572)
- [[`01e7b446d1`](https://github.com/nodejs/node/commit/01e7b446d1)] - **tools**: simplify prefer-assert-methods rule (cjihrig) [#17572](https://github.com/nodejs/node/pull/17572)
- [[`d59b0a7c73`](https://github.com/nodejs/node/commit/d59b0a7c73)] - **tools**: simplify prefer-common-mustnotcall rule (cjihrig) [#17572](https://github.com/nodejs/node/pull/17572)
- [[`aa32bd08a8`](https://github.com/nodejs/node/commit/aa32bd08a8)] - **tools**: prefer common.expectsError in tests (Anatoli Papirovski) [#17557](https://github.com/nodejs/node/pull/17557)
- [[`89964183c0`](https://github.com/nodejs/node/commit/89964183c0)] - **tools**: don't lint-md as part of main lint target (Refael Ackermann) [#17587](https://github.com/nodejs/node/pull/17587)
- [[`70cfe687ca`](https://github.com/nodejs/node/commit/70cfe687ca)] - **tools**: replace space with \b in regex (Diego Rodríguez Baquero) [#17479](https://github.com/nodejs/node/pull/17479)
- [[`e57af5aada`](https://github.com/nodejs/node/commit/e57af5aada)] - **tools**: lint for additional strings in docs (Rich Trott) [#17492](https://github.com/nodejs/node/pull/17492)
- [[`0e5dc8f925`](https://github.com/nodejs/node/commit/0e5dc8f925)] - **tools**: update markdown lint presets (Rich Trott) [#17382](https://github.com/nodejs/node/pull/17382)
- [[`6c65e04231`](https://github.com/nodejs/node/commit/6c65e04231)] - **tools**: enable no-return-await lint rule (Rich Trott) [#17265](https://github.com/nodejs/node/pull/17265)
- [[`1e34a0e9a8`](https://github.com/nodejs/node/commit/1e34a0e9a8)] - **tools**: add cpplint rule for NULL usage (Daniel Bevenius) [#17373](https://github.com/nodejs/node/pull/17373)
- [[`e41344f1b8`](https://github.com/nodejs/node/commit/e41344f1b8)] - **tools**: add docs for prefer-util-format-errors rule (Jon Moss) [#17376](https://github.com/nodejs/node/pull/17376)
- [[`1cc6df29a7`](https://github.com/nodejs/node/commit/1cc6df29a7)] - **tools**: add Boxstarter script (Bartosz Sosnowski) [#17046](https://github.com/nodejs/node/pull/17046)
- [[`6624ac3131`](https://github.com/nodejs/node/commit/6624ac3131)] - **tools**: update to ESLint 4.12.0 (cjihrig) [#16948](https://github.com/nodejs/node/pull/16948)
- [[`8e5b7117bc`](https://github.com/nodejs/node/commit/8e5b7117bc)] - **tools**: prohibit notDeepEqual usage (Ruben Bridgewater) [#16325](https://github.com/nodejs/node/pull/16325)
- [[`b7f81ae266`](https://github.com/nodejs/node/commit/b7f81ae266)] - **tools**: add lint fixer for `require-buffer` (Bamieh) [#17144](https://github.com/nodejs/node/pull/17144)
- [[`f0f32dccfe`](https://github.com/nodejs/node/commit/f0f32dccfe)] - **tools**: fix gitignore for tools/doc/ (Richard Littauer) [#17224](https://github.com/nodejs/node/pull/17224)
- [[`5247ab3792`](https://github.com/nodejs/node/commit/5247ab3792)] - **tools**: make doc tool a bit more readable (Tobias Nießen) [#17125](https://github.com/nodejs/node/pull/17125)
- [[`c8247a7c7d`](https://github.com/nodejs/node/commit/c8247a7c7d)] - **tools**: remove useless function declaration (Tobias Nießen) [#17125](https://github.com/nodejs/node/pull/17125)
- [[`34bfbfece4`](https://github.com/nodejs/node/commit/34bfbfece4)] - **tools**: avoid using process.cwd in tools/lint-js (Tobias Nießen) [#17121](https://github.com/nodejs/node/pull/17121)
- [[`c4eb683020`](https://github.com/nodejs/node/commit/c4eb683020)] - **tools**: use built-in padStart instead of padString (Tobias Nießen) [#17120](https://github.com/nodejs/node/pull/17120)
- [[`4954eef481`](https://github.com/nodejs/node/commit/4954eef481)] - **tools**: allow running test.py without configuring (Gibson Fahnestock) [#16621](https://github.com/nodejs/node/pull/16621)
- [[`16f181e3b9`](https://github.com/nodejs/node/commit/16f181e3b9)] - **tools**: bump remark-cli to 4.0 (Refael Ackermann) [#17028](https://github.com/nodejs/node/pull/17028)
- [[`4f518a4780`](https://github.com/nodejs/node/commit/4f518a4780)] - **tools**: fail tests if malformed status file (Rich Trott) [#16703](https://github.com/nodejs/node/pull/16703)
- [[`7fe6a8f5d5`](https://github.com/nodejs/node/commit/7fe6a8f5d5)] - **tools**: try installing js-yaml only once (Joyee Cheung) [#16661](https://github.com/nodejs/node/pull/16661)
- [[`4d0c70a6f6`](https://github.com/nodejs/node/commit/4d0c70a6f6)] - **tools**: speed up lint-md-build (Refael Ackermann) [#16945](https://github.com/nodejs/node/pull/16945)
- [[`03d2514b46`](https://github.com/nodejs/node/commit/03d2514b46)] - **tools,test**: throw if common.PORT used in parallel tests (Rich Trott) [#17559](https://github.com/nodejs/node/pull/17559)
- [[`8bd74c4061`](https://github.com/nodejs/node/commit/8bd74c4061)] - **tools,test**: use Execute instead of check_output (Refael Ackermann) [#17381](https://github.com/nodejs/node/pull/17381)
- [[`855bb8d486`](https://github.com/nodejs/node/commit/855bb8d486)] - **trace_events**: add executionAsyncId to init events (Andreas Madsen) [#17196](https://github.com/nodejs/node/pull/17196)
- [[`f321921573`](https://github.com/nodejs/node/commit/f321921573)] - **tty**: fix 'resize' event regression (Ben Noordhuis) [#16225](https://github.com/nodejs/node/pull/16225)
- [[`4e3aa9a899`](https://github.com/nodejs/node/commit/4e3aa9a899)] - **tty**: refactor exports (cjihrig) [#16959](https://github.com/nodejs/node/pull/16959)
- [[`8383c348b8`](https://github.com/nodejs/node/commit/8383c348b8)] - **util**: fix negative 0 check in inspect (Gus Caplan) [#17507](https://github.com/nodejs/node/pull/17507)
- [[`c5d20b36e1`](https://github.com/nodejs/node/commit/c5d20b36e1)] - **util**: remove check for global.process (Gus Caplan) [#17435](https://github.com/nodejs/node/pull/17435)
- [[`a37eb32c32`](https://github.com/nodejs/node/commit/a37eb32c32)] - **util**: escaping object keys in util.inspect() (buji) [#16986](https://github.com/nodejs/node/pull/16986)
- [[`57ee0dd5e3`](https://github.com/nodejs/node/commit/57ee0dd5e3)] - **zlib**: remove unnecessary else branch (john) [#17057](https://github.com/nodejs/node/pull/17057)
- [[`45ca714005`](https://github.com/nodejs/node/commit/45ca714005)] - **zlib**: fix assert fail for bad write in object mode (Kevin Locke) [#16960](https://github.com/nodejs/node/pull/16960)
- [[`fa01fe6819`](https://github.com/nodejs/node/commit/fa01fe6819)] - **zlib**: fix decompression of empty data streams (Anna Henningsen) [#17042](https://github.com/nodejs/node/pull/17042)

Windows 32-bit Installer: https://nodejs.org/dist/v9.3.0/node-v9.3.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v9.3.0/node-v9.3.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v9.3.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v9.3.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v9.3.0/node-v9.3.0.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v9.3.0/node-v9.3.0-darwin-x64.tar.gz \
Linux 32-bit Binary: https://nodejs.org/dist/v9.3.0/node-v9.3.0-linux-x86.tar.xz \
Linux 64-bit Binary: https://nodejs.org/dist/v9.3.0/node-v9.3.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v9.3.0/node-v9.3.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v9.3.0/node-v9.3.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v9.3.0/node-v9.3.0-aix-ppc64.tar.gz \
SmartOS 32-bit Binary: https://nodejs.org/dist/v9.3.0/node-v9.3.0-sunos-x86.tar.xz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v9.3.0/node-v9.3.0-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v9.3.0/node-v9.3.0-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v9.3.0/node-v9.3.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v9.3.0/node-v9.3.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v9.3.0/node-v9.3.0.tar.gz \
Other release files: https://nodejs.org/dist/v9.3.0/ \
Documentation: https://nodejs.org/docs/v9.3.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

d0bfbd7697c1249b536586cfdc257828f2f56f30945fd120d735f646f297828c  node-v9.3.0-aix-ppc64.tar.gz
0539601e67e9be6995d4ba4abe565a748e25699060631369f59166d5de43d21a  node-v9.3.0-darwin-x64.tar.gz
476554cd4a4758923e2de4d9bd1c851fcd3229f87c513b2456a0cc9a51abb828  node-v9.3.0-darwin-x64.tar.xz
e084a59cdb0397d034e3fd27699f97963723d4eafff5023abe2a01613aebebd3  node-v9.3.0-headers.tar.gz
4c4206f3471f5242d10f0a97ae6d0c809fe3cff13072f8665995d5b3e128b579  node-v9.3.0-headers.tar.xz
7b11f47f695d3be97b5b69c6546b7af005f9b66f093bda497310546e2dd5605c  node-v9.3.0-linux-arm64.tar.gz
11300e878220962adade33238dd6e55928ad6d1362e0daa1f12e272137e68c0b  node-v9.3.0-linux-arm64.tar.xz
bdb46d3fc1baf19f9562143a94bcf6a285041327a8032352e85ba8b438b7909b  node-v9.3.0-linux-armv6l.tar.gz
c8f796e254e5f00157a56f30f0aa7f40e5a0c382b2b3f2c889f20c85f079ad9f  node-v9.3.0-linux-armv6l.tar.xz
1fd03343082842caeac1bc810fb4292ef46724f1387274b32e4282f0b2b57498  node-v9.3.0-linux-armv7l.tar.gz
8df0812625f873c43b32b1ba290c7ee23e6e94903f6fc869a0f914e22637f805  node-v9.3.0-linux-armv7l.tar.xz
7ec7f7321b223f3f0461b3d8b93762a043eb1dbe9bd7d784472db12fbd4c5699  node-v9.3.0-linux-ppc64le.tar.gz
6769fff5413c112e29ba771d294f57f94dbd7945573f44d99fe37ac390405693  node-v9.3.0-linux-ppc64le.tar.xz
1d2964a0f0897ae44797b2255ea30c525a938dc2c49f07488219611ed8300c5d  node-v9.3.0-linux-s390x.tar.gz
defddf58190537f8814e29e2e7c23f462920f95a259247a6d898550474c429f4  node-v9.3.0-linux-s390x.tar.xz
c7e86cc0e01102ce3eaff0e8e9d8ed8d046aa5bae2464bd80efb233c8720322a  node-v9.3.0-linux-x64.tar.gz
0424dd6fa059fc32e0b73f460cb587b92b13c7b0af56331bbdc53a52e43f24ea  node-v9.3.0-linux-x64.tar.xz
5a01619bb5c086d9a0a7e060801e1d03f44552a15d0c23144257ba1e233f5fb0  node-v9.3.0-linux-x86.tar.gz
e3a8cd4ecd3c9e322bb282c765990df4584659c3f0447c61430ab7b48f638351  node-v9.3.0-linux-x86.tar.xz
109a663779e3aacc770b06dfc65b390103adb45a0b763de5ddd1d77d2b2c555c  node-v9.3.0.pkg
dfdeb0ec128b19cc03db7a69832f4a4d0edc070d60df824fc7a66db3f4f90bf1  node-v9.3.0-sunos-x64.tar.gz
550305b2ef172cdc18521b39f5acb64f435e8255a4189ac753749283d0f14ac3  node-v9.3.0-sunos-x64.tar.xz
2059733ce7741a7600e49cf6f2f3f551ffc9966d9ec0706f8b73e024296533b8  node-v9.3.0-sunos-x86.tar.gz
837a96b086e29842b4a0b6be57d3c46322b990d1f892ec12ac18aa085b2db430  node-v9.3.0-sunos-x86.tar.xz
b7338f2b1588264c9591fef08246d72ceed664eb18f2556692b4679302bbe2a5  node-v9.3.0.tar.gz
b288075e2f09faf33046583271a4e85c34684b5b637973dd6eaa5445220c57cd  node-v9.3.0.tar.xz
e82bb00c604d01d9512a9a3e501b79c6f21aac1142c61c209b054883a37c8e67  node-v9.3.0-win-x64.7z
53bceda79c8d5dd8a185221a5ea2fc8f2b24ed7e0c9c91d18f6016b3624ff096  node-v9.3.0-win-x64.zip
6e986688ad44538f0ab0253f393d20a978dd752586162fd6a036e0a5c45daaae  node-v9.3.0-win-x86.7z
8062b3f9e62d05c14d98e2d11e6d82c64a7a2accb884b309ed730dfe429b4465  node-v9.3.0-win-x86.zip
fe04c4a19bf6c400c1ea2f984e15f2b1e440d627681bfc97db92e243d42e5185  node-v9.3.0-x64.msi
d612af918fa8202c6d29f269c4452508712b3f01f9b8c4489441cf0c985a4b36  node-v9.3.0-x86.msi
98291ebf3fdbdfa0f2a8cae19f837d75e643a1b9770d1f90b0291528cf6642b7  win-x64/node.exe
b853a8a66dcd6b3223bd6b33d15f2252fa4b63474c1692856ce9c59af3c57eef  win-x64/node.lib
724d5e5c03b88eb047b0141036d3287ed3d40608c1d1d0d9a7e1bbceac7f788f  win-x64/node_pdb.7z
30f7da55b350a213fcb5e28ac394790e9813d0dc490690da47e00a73d22290c7  win-x64/node_pdb.zip
a28166a4833af4c6bfe818215bcde29539cdce8e60d34867d66aab5b9f5ee590  win-x86/node.exe
ede0c92ba266a236859973abcea45df58841d5b58317c807df5ace74a2da111e  win-x86/node.lib
fef029c6e17b86f18e01aecfc623f9858f0436e38aa4cfdca9e411b7e33d1713  win-x86/node_pdb.7z
e2ca22335a611683ef4ff0bcc9b54be2b7ddaac5ef5545cbae3a12d1bcc121a8  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQEzBAEBCAAdFiEEDv/hvO/ZyE49CYFSkzsB9AtcqUYFAlowQ10ACgkQkzsB9Atc
qUYJgwf7B32SybW0lkNIRaZgRPEI1qmLwDVfbQNeXiZHRVTT4vS3Pu4aybneVttp
jNX3NXwkTxBVybz7FBDP0T+GOAFkQEnGtscZFLoX5oX4uR5Vi7C9VFkX/FtF6Byw
UoLu+ZVjFZUTb1iy8BmL4Loal3K5PN7ioI7gWM+DFwrlK3QHjM0oM8EdP0BFMtDl
ycfYkcw9X8gJP+EJOvmC32oUhme9ikLDwfcf+f3rTwwhTscs15n3WbZZthk/4bJt
zmmmLBNmfgOrSqIWR6cG1Zg5cTY0sMQN9XF1cr4x+Mix1WucsG4pHa3OhbRUwGIh
ANN7wA85x/DvW/BQJO/LfypbUWvYTw==
=7rDu
-----END PGP SIGNATURE-----

```
