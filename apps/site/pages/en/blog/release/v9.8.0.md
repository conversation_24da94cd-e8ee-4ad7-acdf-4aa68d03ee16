---
date: '2018-03-08T01:56:28.996Z'
category: release
title: Node v9.8.0 (Current)
layout: blog-post
author: <PERSON><PERSON>
---

### Notable Changes

- **crypto**:
  - add cert.fingerprint256 as SHA256 fingerprint (<PERSON><PERSON>) [#17690](https://github.com/nodejs/node/pull/17690)
- **http2**:
  - Fixed issues with aborted connections in the HTTP/2 implementation (<PERSON>) [#18987](https://github.com/nodejs/node/pull/18987) [#19002](https://github.com/nodejs/node/pull/19002)
- **loader**:
  - --inspect-brk now works properly for esmodules (<PERSON>) [#18949](https://github.com/nodejs/node/pull/18949)
- **src**:
  - make process.dlopen() load well-known symbol (<PERSON>) [#18934](https://github.com/nodejs/node/pull/18934)
- **trace_events**:
  - add file pattern cli option (<PERSON>) [#18480](https://github.com/nodejs/node/pull/18480)
- **Added new collaborators**
  - [MoonBall](https://github.com/MoonBall) Chen Gang

### Commits

- [[`6ae2cafde3`](https://github.com/nodejs/node/commit/6ae2cafde3)] - **buffer**: coerce offset to integer (Ruben Bridgewater) [#18215](https://github.com/nodejs/node/pull/18215)
- [[`6d17383041`](https://github.com/nodejs/node/commit/6d17383041)] - **buffer**: fix typo in lib/buffer.js (Ujjwal Sharma) [#19126](https://github.com/nodejs/node/pull/19126)
- [[`4b34b2e185`](https://github.com/nodejs/node/commit/4b34b2e185)] - **build**: fix gocvr version used for coverage (Michael Dawson) [#19094](https://github.com/nodejs/node/pull/19094)
- [[`a938e52ffe`](https://github.com/nodejs/node/commit/a938e52ffe)] - **build**: disable openssl build warnings on macos (Ben Noordhuis) [#19046](https://github.com/nodejs/node/pull/19046)
- [[`44d80c5620`](https://github.com/nodejs/node/commit/44d80c5620)] - **build**: fix coverage after gcovr update (killagu) [#18958](https://github.com/nodejs/node/pull/18958)
- [[`28a5362e83`](https://github.com/nodejs/node/commit/28a5362e83)] - **build**: fix lint-md-build dependency (Joyee Cheung) [#18981](https://github.com/nodejs/node/pull/18981)
- [[`e74e422a53`](https://github.com/nodejs/node/commit/e74e422a53)] - **(SEMVER-MINOR)** **crypto**: add cert.fingerprint256 as SHA256 fingerprint (Hannes Magnusson) [#17690](https://github.com/nodejs/node/pull/17690)
- [[`056001dc8f`](https://github.com/nodejs/node/commit/056001dc8f)] - **(SEMVER-MINOR)** **deps**: cherry-pick 0bcb1d6f from upstream V8 (Jakob Kummerow) [#18212](https://github.com/nodejs/node/pull/18212)
- [[`1fadb2edb4`](https://github.com/nodejs/node/commit/1fadb2edb4)] - **doc**: fix/add link to Android info (Vse Mozhet Byt) [#19004](https://github.com/nodejs/node/pull/19004)
- [[`68524610f2`](https://github.com/nodejs/node/commit/68524610f2)] - **doc**: remove subsystem from pull request template (Rich Trott) [#19125](https://github.com/nodejs/node/pull/19125)
- [[`d3a70e9cd4`](https://github.com/nodejs/node/commit/d3a70e9cd4)] - **doc**: remove tentativeness in pull-requests.md (Rich Trott) [#19123](https://github.com/nodejs/node/pull/19123)
- [[`f03079fce6`](https://github.com/nodejs/node/commit/f03079fce6)] - **doc**: update cc list (Ruben Bridgewater) [#19099](https://github.com/nodejs/node/pull/19099)
- [[`9d2de16b13`](https://github.com/nodejs/node/commit/9d2de16b13)] - **doc**: add introduced_in metadata to \_toc.md (Rich Trott) [#19113](https://github.com/nodejs/node/pull/19113)
- [[`ae2dabb8fc`](https://github.com/nodejs/node/commit/ae2dabb8fc)] - **doc**: new team for bundlers or delivery of Node.js (Michael Dawson) [#19098](https://github.com/nodejs/node/pull/19098)
- [[`0e4f4266a1`](https://github.com/nodejs/node/commit/0e4f4266a1)] - **doc**: add simple example to rename function (punteek) [#18812](https://github.com/nodejs/node/pull/18812)
- [[`e42600fc4b`](https://github.com/nodejs/node/commit/e42600fc4b)] - **doc**: add missing `Returns` in fs & util (Sho Miyamoto) [#18775](https://github.com/nodejs/node/pull/18775)
- [[`4ecf5bbe74`](https://github.com/nodejs/node/commit/4ecf5bbe74)] - **doc**: fix a typo in util.isDeepStrictEqual (Sho Miyamoto) [#18775](https://github.com/nodejs/node/pull/18775)
- [[`cab6c8e95c`](https://github.com/nodejs/node/commit/cab6c8e95c)] - **doc**: add URL.format() example (Zeke Sikelianos) [#18888](https://github.com/nodejs/node/pull/18888)
- [[`a4462b7944`](https://github.com/nodejs/node/commit/a4462b7944)] - **doc**: fix n-api asynchronous threading docs (Eric Bickle) [#19073](https://github.com/nodejs/node/pull/19073)
- [[`bfa894cf37`](https://github.com/nodejs/node/commit/bfa894cf37)] - **doc**: add MoonBall to collaborators (Chen Gang) [#19109](https://github.com/nodejs/node/pull/19109)
- [[`77154cd65d`](https://github.com/nodejs/node/commit/77154cd65d)] - **doc**: update list of re-exported symbols (Richard Lau) [#19013](https://github.com/nodejs/node/pull/19013)
- [[`459f2095a1`](https://github.com/nodejs/node/commit/459f2095a1)] - **doc**: Readable unpipe on Writable error event (George Sapkin) [#18080](https://github.com/nodejs/node/pull/18080)
- [[`68c1337819`](https://github.com/nodejs/node/commit/68c1337819)] - **doc**: add RegExp Unicode Property Escapes to intl (Vse Mozhet Byt) [#19052](https://github.com/nodejs/node/pull/19052)
- [[`71d09ecbf1`](https://github.com/nodejs/node/commit/71d09ecbf1)] - **doc**: make the background section concise and improve its formality (Wilson) [#18928](https://github.com/nodejs/node/pull/18928)
- [[`951054004d`](https://github.com/nodejs/node/commit/951054004d)] - **doc**: lowercase primitives in test/common/README.md (Vse Mozhet Byt) [#18875](https://github.com/nodejs/node/pull/18875)
- [[`5b8c97f6bc`](https://github.com/nodejs/node/commit/5b8c97f6bc)] - **events**: show throw stack trace for uncaught exception (Anna Henningsen) [#19003](https://github.com/nodejs/node/pull/19003)
- [[`0789eeceb6`](https://github.com/nodejs/node/commit/0789eeceb6)] - **http**: prevent aborted event when already completed (Andrew Johnston) [#18999](https://github.com/nodejs/node/pull/18999)
- [[`ae4d83facf`](https://github.com/nodejs/node/commit/ae4d83facf)] - **http**: prevent aborted event when already completed (Andrew Johnston) [#18999](https://github.com/nodejs/node/pull/18999)
- [[`50d1233935`](https://github.com/nodejs/node/commit/50d1233935)] - **http2**: no stream destroy while its data is on the wire (Anna Henningsen) [#19002](https://github.com/nodejs/node/pull/19002)
- [[`551d9752c8`](https://github.com/nodejs/node/commit/551d9752c8)] - **http2**: fix flaky test-http2-https-fallback (Matteo Collina) [#19093](https://github.com/nodejs/node/pull/19093)
- [[`8bc930c269`](https://github.com/nodejs/node/commit/8bc930c269)] - **http2**: fix endless loop when writing empty string (Anna Henningsen) [#18924](https://github.com/nodejs/node/pull/18924)
- [[`aa0fca9426`](https://github.com/nodejs/node/commit/aa0fca9426)] - **http2**: use original error for cancelling pending streams (Anna Henningsen) [#18988](https://github.com/nodejs/node/pull/18988)
- [[`447136999d`](https://github.com/nodejs/node/commit/447136999d)] - **http2**: send error text in case of ALPN mismatch (Anna Henningsen) [#18986](https://github.com/nodejs/node/pull/18986)
- [[`ef8f90f34e`](https://github.com/nodejs/node/commit/ef8f90f34e)] - **http2**: fix condition where data is lost (Matteo Collina) [#18895](https://github.com/nodejs/node/pull/18895)
- [[`e584113b66`](https://github.com/nodejs/node/commit/e584113b66)] - **lib**: re-fix v8_prof_processor (Anna Henningsen) [#19059](https://github.com/nodejs/node/pull/19059)
- [[`12856b0dd2`](https://github.com/nodejs/node/commit/12856b0dd2)] - **lib**: change hook -\> hooks in code comment (Daniel Bevenius) [#19053](https://github.com/nodejs/node/pull/19053)
- [[`db8d197e79`](https://github.com/nodejs/node/commit/db8d197e79)] - **lib,test**: remove yoda statements (Ruben Bridgewater) [#18746](https://github.com/nodejs/node/pull/18746)
- [[`59547cc438`](https://github.com/nodejs/node/commit/59547cc438)] - **loader**: fix --inspect-brk (Gus Caplan) [#18949](https://github.com/nodejs/node/pull/18949)
- [[`39e032fe86`](https://github.com/nodejs/node/commit/39e032fe86)] - **module**: fix main lookup regression from #18728 (Guy Bedford) [#18788](https://github.com/nodejs/node/pull/18788)
- [[`f3e3429296`](https://github.com/nodejs/node/commit/f3e3429296)] - **module**: support main w/o extension, pjson cache (Guy Bedford) [#18728](https://github.com/nodejs/node/pull/18728)
- [[`95f6467ffd`](https://github.com/nodejs/node/commit/95f6467ffd)] - **module**: fix cyclical dynamic import (Bradley Farias) [#18965](https://github.com/nodejs/node/pull/18965)
- [[`5c4f703607`](https://github.com/nodejs/node/commit/5c4f703607)] - **n-api**: update reference test (Gabriel Schulhof) [#19086](https://github.com/nodejs/node/pull/19086)
- [[`1b32fc3276`](https://github.com/nodejs/node/commit/1b32fc3276)] - **n-api**: fix object test (Gabriel Schulhof) [#19039](https://github.com/nodejs/node/pull/19039)
- [[`ef4714c2b6`](https://github.com/nodejs/node/commit/ef4714c2b6)] - **net**: inline and simplify onSocketEnd (Anna Henningsen) [#18607](https://github.com/nodejs/node/pull/18607)
- [[`28880cf89d`](https://github.com/nodejs/node/commit/28880cf89d)] - **perf_hooks**: fix timing (Timothy Gu) [#18993](https://github.com/nodejs/node/pull/18993)
- [[`96f0bec48b`](https://github.com/nodejs/node/commit/96f0bec48b)] - **repl**: make last error available as `_error` (Anna Henningsen) [#18919](https://github.com/nodejs/node/pull/18919)
- [[`420d56c2ea`](https://github.com/nodejs/node/commit/420d56c2ea)] - **src**: don't touch js object in Http2Session dtor (Ben Noordhuis) [#18656](https://github.com/nodejs/node/pull/18656)
- [[`f89f659dcf`](https://github.com/nodejs/node/commit/f89f659dcf)] - **src**: remove unnecessary Reset() calls (Ben Noordhuis) [#18656](https://github.com/nodejs/node/pull/18656)
- [[`67a9742aed`](https://github.com/nodejs/node/commit/67a9742aed)] - **src**: prevent persistent handle resource leaks (Ben Noordhuis) [#18656](https://github.com/nodejs/node/pull/18656)
- [[`08bcdde888`](https://github.com/nodejs/node/commit/08bcdde888)] - **(SEMVER-MINOR)** **src**: handle exceptions in env-\>SetImmediates (James M Snell) [#18297](https://github.com/nodejs/node/pull/18297)
- [[`cc52dae7c4`](https://github.com/nodejs/node/commit/cc52dae7c4)] - **src**: #include \<stdio.h\>" to iculslocs (Steven R. Loomis) [#19150](https://github.com/nodejs/node/pull/19150)
- [[`2f17c52674`](https://github.com/nodejs/node/commit/2f17c52674)] - **src**: use std::unique_ptr for STACK_OF(X509) (Ben Noordhuis) [#19087](https://github.com/nodejs/node/pull/19087)
- [[`f10470ce2d`](https://github.com/nodejs/node/commit/f10470ce2d)] - **src**: refactor GetPeerCertificate (Daniel Bevenius) [#19087](https://github.com/nodejs/node/pull/19087)
- [[`4fae6e3904`](https://github.com/nodejs/node/commit/4fae6e3904)] - **(SEMVER-MINOR)** **src**: make process.dlopen() load well-known symbol (Ben Noordhuis) [#18934](https://github.com/nodejs/node/pull/18934)
- [[`89edbae7ab`](https://github.com/nodejs/node/commit/89edbae7ab)] - **(SEMVER-MINOR)** **src**: clean up process.dlopen() (Ben Noordhuis) [#18934](https://github.com/nodejs/node/pull/18934)
- [[`08b83ee27a`](https://github.com/nodejs/node/commit/08b83ee27a)] - **src**: refactor setting JS properties on WriteWrap (Anna Henningsen) [#18963](https://github.com/nodejs/node/pull/18963)
- [[`4d5cd5c6c5`](https://github.com/nodejs/node/commit/4d5cd5c6c5)] - **src**: fix error message in async_hooks constructor (Daniel Bevenius) [#19000](https://github.com/nodejs/node/pull/19000)
- [[`6787913a68`](https://github.com/nodejs/node/commit/6787913a68)] - **test**: add more information to assert.strictEqual (Ujjwal Sharma) [#19162](https://github.com/nodejs/node/pull/19162)
- [[`ee653ecd09`](https://github.com/nodejs/node/commit/ee653ecd09)] - **test**: move require http2 to after crypto check (Daniel Bevenius) [#19111](https://github.com/nodejs/node/pull/19111)
- [[`5bbf009c1d`](https://github.com/nodejs/node/commit/5bbf009c1d)] - **test**: check symbols in shared lib (Yihong Wang) [#18806](https://github.com/nodejs/node/pull/18806)
- [[`d8833762cb`](https://github.com/nodejs/node/commit/d8833762cb)] - **test**: refactor test-async-wrap-getasyncid (Santiago Gimeno) [#18727](https://github.com/nodejs/node/pull/18727)
- [[`23107ba7b1`](https://github.com/nodejs/node/commit/23107ba7b1)] - **test**: remove assert message and add block scope (wuweiweiwu) [#19054](https://github.com/nodejs/node/pull/19054)
- [[`cc90bbd0f4`](https://github.com/nodejs/node/commit/cc90bbd0f4)] - **test**: fix flaky inspector-stop-profile-after-done (Rich Trott) [#18126](https://github.com/nodejs/node/pull/18126)
- [[`8d595bb25c`](https://github.com/nodejs/node/commit/8d595bb25c)] - **test**: check endless loop while writing empty string (XadillaX) [#18924](https://github.com/nodejs/node/pull/18924)
- [[`a4550069ca`](https://github.com/nodejs/node/commit/a4550069ca)] - **test**: allow running with `NODE_PENDING_DEPRECATION` (Anna Henningsen) [#18991](https://github.com/nodejs/node/pull/18991)
- [[`fd27165f73`](https://github.com/nodejs/node/commit/fd27165f73)] - **test**: specify 'dir' for directory symlinks (Kyle Farnung) [#19049](https://github.com/nodejs/node/pull/19049)
- [[`eca333a6e8`](https://github.com/nodejs/node/commit/eca333a6e8)] - **test**: refactor test after review (Andrew Johnston) [#18999](https://github.com/nodejs/node/pull/18999)
- [[`c943cd09a7`](https://github.com/nodejs/node/commit/c943cd09a7)] - **test**: fix repl-tab-complete --without-ssl (Daniel Bevenius) [#17867](https://github.com/nodejs/node/pull/17867)
- [[`f864509991`](https://github.com/nodejs/node/commit/f864509991)] - **test,benchmark**: use new Buffer API where appropriate (Сковорода Никита Андреевич) [#18980](https://github.com/nodejs/node/pull/18980)
- [[`479b622e49`](https://github.com/nodejs/node/commit/479b622e49)] - **tls,http2**: handle writes after SSL destroy more gracefully (Anna Henningsen) [#18987](https://github.com/nodejs/node/pull/18987)
- [[`3d4cda3a7d`](https://github.com/nodejs/node/commit/3d4cda3a7d)] - **(SEMVER-MINOR)** **trace_events**: add file pattern cli option (Andreas Madsen) [#18480](https://github.com/nodejs/node/pull/18480)
- [[`3e8e1524ac`](https://github.com/nodejs/node/commit/3e8e1524ac)] - **util**: use blue on non-windows systems for number (Gus Caplan) [#18925](https://github.com/nodejs/node/pull/18925)

Windows 32-bit Installer: https://nodejs.org/dist/v9.8.0/node-v9.8.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v9.8.0/node-v9.8.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v9.8.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v9.8.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v9.8.0/node-v9.8.0.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v9.8.0/node-v9.8.0-darwin-x64.tar.gz \
Linux 32-bit Binary: https://nodejs.org/dist/v9.8.0/node-v9.8.0-linux-x86.tar.xz \
Linux 64-bit Binary: https://nodejs.org/dist/v9.8.0/node-v9.8.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v9.8.0/node-v9.8.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v9.8.0/node-v9.8.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v9.8.0/node-v9.8.0-aix-ppc64.tar.gz \
SmartOS 32-bit Binary: https://nodejs.org/dist/v9.8.0/node-v9.8.0-sunos-x86.tar.xz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v9.8.0/node-v9.8.0-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v9.8.0/node-v9.8.0-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v9.8.0/node-v9.8.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v9.8.0/node-v9.8.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v9.8.0/node-v9.8.0.tar.gz \
Other release files: https://nodejs.org/dist/v9.8.0/ \
Documentation: https://nodejs.org/docs/v9.8.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

9cf4a126d679a91821f9832c9b8a534c34d59761b22fca6783c2ff8c45499d47  node-v9.8.0-aix-ppc64.tar.gz
c95326d6d8d01e5d4fbf0ca1b1e4fc0d800d00516f38532cc887e17c78f2af07  node-v9.8.0-darwin-x64.tar.gz
7964e1f86219d3053949a91ab607e0cb6c8e58a670f90a2fdbf460f86b274cc0  node-v9.8.0-darwin-x64.tar.xz
db70fe73d960eb847971a8f3826ad2fd5041ef89301491a2b43596e66a50a3ad  node-v9.8.0-headers.tar.gz
514be6a713a8adbc058d3366a89a1738411899c8ea6ac5d70f50900d1c35cc5c  node-v9.8.0-headers.tar.xz
2999cfe889aa75aaf0b98fdcb90ebfc32cf55fcafe6149264b302a67394964a7  node-v9.8.0-linux-arm64.tar.gz
f3537d06f010e77739be073003fca0b477efec8a72d503dc5a1d942a19973b07  node-v9.8.0-linux-arm64.tar.xz
7b711f217d62225afe2af77e5ecd72607e1800d14e749b88b4a92f90b357209e  node-v9.8.0-linux-armv6l.tar.gz
a64e2cfd6de695a7c099d567ec419f7d9e95876e229117abe52da0d14c5a5a3b  node-v9.8.0-linux-armv6l.tar.xz
638268d6b9717ac2e591b20d5fa63aeeb9d1d195cfd500ef403e85dd073a2681  node-v9.8.0-linux-armv7l.tar.gz
006926548b5aad77edafea283dc8bd24a2316ce67f59ccfd940c5543a02d5c03  node-v9.8.0-linux-armv7l.tar.xz
2026e6c316f9ef645fdd696e0f6a2b0983af8cfee33f48e9aa4841eae52e2c20  node-v9.8.0-linux-ppc64le.tar.gz
71229efb95fde81c92959b8d4b7e658964be2e45aed73dbe520002cd5a84ed1d  node-v9.8.0-linux-ppc64le.tar.xz
00932ccefc8127f2048f60bb882b7414fbb49063ef3cc60c7f81a7ffe9b3a0a4  node-v9.8.0-linux-s390x.tar.gz
13554cfc8cec68da011e3666a9e19caebe9d7f9389d843fa38578f0fdde92b73  node-v9.8.0-linux-s390x.tar.xz
4e519de3507f810b6567d995169c4b36f433bf5731340ebc1fbbd0b6b6e6c310  node-v9.8.0-linux-x64.tar.gz
9f631739b0a9b96b8760c42869e88592db9c3fda2425202bd8b2d09e6371133a  node-v9.8.0-linux-x64.tar.xz
d1093306679b90c7c870364ef8cc5880c1595743d223f9f20ea616d9940b02ea  node-v9.8.0-linux-x86.tar.gz
93757d8f9d60d902de6efd8f96cdf4b57bdd12808711a9cf1491f40773ace861  node-v9.8.0-linux-x86.tar.xz
03a7fc03315d82166e6bbf8a8979f971189a69a75dfb873af8cc74a127f7867b  node-v9.8.0.pkg
2559a3802039ef4c91063c4e2d9547dd6049e85a0e7f0c4bf15f5d3445c32e00  node-v9.8.0-sunos-x64.tar.gz
646ec84f5b48c398e535d554c9246d6607431337793654f25f60381786450abe  node-v9.8.0-sunos-x64.tar.xz
bf0b9b69ea295601c8536c53db76ccce1301db99fdfcad830f4b6f70bb7c5510  node-v9.8.0-sunos-x86.tar.gz
e177a52fca0c679603c4e88d02b73e53682f7aa8b318c8fefb34d4c080915e84  node-v9.8.0-sunos-x86.tar.xz
e84a1baf8248411d2c875c2f4e519878af3fb62fdfe04f9607cc085e6a0e59d6  node-v9.8.0.tar.gz
0706bb49e4fa5fa64c6c51941becb4b3854a6c0335425d7312bc086c37b41eac  node-v9.8.0.tar.xz
ede566d6d4260fbfddb8a92073bc92abe01f2ed26ac23825f65cc7aec5285270  node-v9.8.0-win-x64.7z
dd3971c126907a033f2bc93fdd29327af3ff5b9d151b3b90eb5db1832fe6df9e  node-v9.8.0-win-x64.zip
1545ad8233230e76940d56e698507fb55807e69ff8ee5ddad7f486dbc76af60d  node-v9.8.0-win-x86.7z
b79185a4c6e3662640a73c0dd0ac81a6c6a5d27bbbef244fe32f4db6886e6a33  node-v9.8.0-win-x86.zip
35715bb7ec90b4025de735a2ca30b7336ffd5ab9678bfc35e509d3d253d9e89e  node-v9.8.0-x64.msi
ccb1589cfbce371ab3103aaf1b55214f677939b7fdae411dbd9253e8fd484def  node-v9.8.0-x86.msi
31822ea3a450863cb774046b8b79a2dacf0655d618cf7381132e496cf52c6e79  win-x64/node.exe
20a8ecb534e2ebbe8341839ae527535a8c6d48d93578f87569206ca1e60ecb30  win-x64/node.lib
119ffbc062db1858a9a13148c3e1d733de1d78f37322b3bdb826ed8eea2129bc  win-x64/node_pdb.7z
43b2c5adb0a5d7f32016051e664c57f4c608ec4a4991336e1b4094a8fe33d569  win-x64/node_pdb.zip
13dba6c3df441880a9827bf05c5f912c3422fcdaa583c4698d1c3483a597aca0  win-x86/node.exe
c38e2026305abc0d7a11116a88f49feb1641d6fe3e8d57b2d9c809f0447c911d  win-x86/node.lib
8a84a01400465cd13d9dd0910d32e3b2e91da0549eca6ff8c15e3d8d0de4bfc5  win-x86/node_pdb.7z
4f9c68256476fa7e07d3a341bbe2b31b959f39ca0fad1dfc020a1effbf93c7de  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQEzBAEBCAAdFiEEDv/hvO/ZyE49CYFSkzsB9AtcqUYFAlqgmAcACgkQkzsB9Atc
qUbJVQf+OxZA9/Sy0ljygvCOlyvIZpehQEw+h4ygISZYfjp5kVYUcmlbkfSLBiQk
rFmwjjzZEbRIlYBU+Wcm6LK4grYjMjdKUAs75n6nsemwElVSJCQewDOoHWEBDybP
a4717nbOl6p/C7WB33aHdUl5msA4sTEQLyDwAFHUU+kBXCRUwOcyQQpnst6L/N//
zI2Z6lVk1mIKnvBTNMDU90RwPd9Jw9dXPSJYJ3oV3piZyxiHhoalXbjskUNCobho
J+ABwzsEVsDhBnX77qo5zuIKNDH6v1TjiGd6UDii7pPU8QTM6iQkMjwq1XDxUX7h
bBOP+hQjQMW8RGXAJXiDU4VYeoSlxQ==
=KhfI
-----END PGP SIGNATURE-----

```
