---
date: '2018-11-28T00:41:54.959Z'
category: release
title: Node v6.15.0 (LTS)
layout: blog-post
author: <PERSON>
---

**This is a security release.** All Node.js users should consult the security release summary at /blog/vulnerability/november-2018-security-releases/ for details on patched vulnerabilities.

Fixes for the following CVEs are included in this release:

- Node.js: Debugger port 5858 listens on any interface by default (CVE-2018-12120)
- Node.js: Denial of Service with large HTTP headers (CVE-2018-12121)
- Node.js: Slowloris HTTP Denial of Service (CVE-2018-12122 / Node.js)
- Node.js: Hostname spoofing in URL parser for javascript protocol (CVE-2018-12123)
- Node.js: HTTP request splitting (CVE-2018-12116)
- OpenSSL: Timing vulnerability in DSA signature generation (CVE-2018-0734)
- OpenSSL: Microarchitecture timing vulnerability in ECC scalar multiplication (CVE-2018-5407)

### Notable Changes

- **debugger**: Backport of [nodejs/node#8106](https://github.com/nodejs/node/pull/8106) to prevent the debugger from listening on `0.0.0.0`. It now defaults to `127.0.0.1`. Reported by Ben Noordhuis. (CVE-2018-12120 / Ben Noordhuis).
- **deps**: Upgrade to OpenSSL 1.0.2q, fixing CVE-2018-0734 and CVE-2018-5407
- **http**:
  - Headers received by HTTP servers must not exceed 8192 bytes in total to prevent possible Denial of Service attacks. Reported by Trevor Norris. (CVE-2018-12121 / Matteo Collina)
  - A timeout of 40 seconds now applies to servers receiving HTTP headers. This value can be adjusted with `server.headersTimeout`. Where headers are not completely received within this period, the socket is destroyed on the next received chunk. In conjunction with `server.setTimeout()`, this aids in protecting against excessive resource retention and possible Denial of Service. Reported by Jan Maybach ([liebdich.com](https://liebdich.com)). (CVE-2018-12122 / Matteo Collina)
  - Two-byte characters are now strictly disallowed for the `path` option in HTTP client requests. Paths containing characters outside of the range `\u0021` - `\u00ff` will now be rejected with a `TypeError`. This behavior can be reverted if necessary by supplying the `--security-revert=CVE-2018-12116` command line argument (this is not recommended). Reported as security concern for Node.js 6 and 8 by [Arkadiy Tetelman](https://twitter.com/arkadiyt) ([Lob](https://lob.com)), fixed by backporting a change by Benno Fünfstück applied to Node.js 10 and later. (CVE-2018-12116 / Matteo Collina)
- **url**: Fix a bug that would allow a hostname being spoofed when parsing URLs with `url.parse()` with the `'javascript:'` protocol. Reported by [Martin Bajanik](https://twitter.com/_bayotop) ([Kentico](https://kenticocloud.com/)). (CVE-2018-12123 / Matteo Collina)

### Commits

- [[`4beba664e1`](https://github.com/nodejs/node/commit/4beba664e1)] - **deps**: add -no_rand_screen to openssl s_client (Shigeki Ohtsu) [nodejs/node#1836](https://github.com/nodejs/node/pull/1836)
- [[`049fe7978f`](https://github.com/nodejs/node/commit/049fe7978f)] - **deps**: fix asm build error of openssl in x86_win32 (Shigeki Ohtsu) [nodejs/node#1389](https://github.com/nodejs/node/pull/1389)
- [[`e9becec84d`](https://github.com/nodejs/node/commit/e9becec84d)] - **deps**: fix openssl assembly error on ia32 win32 (Fedor Indutny) [nodejs/node#1389](https://github.com/nodejs/node/pull/1389)
- [[`78b3a5b2f7`](https://github.com/nodejs/node/commit/78b3a5b2f7)] - **deps**: copy all openssl header files to include dir (Sam Roberts) [#24530](https://github.com/nodejs/node/pull/24530)
- [[`6120f2429e`](https://github.com/nodejs/node/commit/6120f2429e)] - **deps**: upgrade openssl sources to 1.0.2q (Sam Roberts) [#24530](https://github.com/nodejs/node/pull/24530)
- [[`92231a56d9`](https://github.com/nodejs/node/commit/92231a56d9)] - **deps,http**: http_parser set max header size to 8KB (Matteo Collina) [nodejs-private/node-private#143](https://github.com/nodejs-private/node-private/pull/143)
- [[`dd20c0186f`](https://github.com/nodejs/node/commit/dd20c0186f)] - **(SEMVER-MINOR)** **http**: add --security-revert for CVE-2018-12116 (Matteo Collina) [nodejs-private/node-private#146](https://github.com/nodejs-private/node-private/pull/146)
- [[`811b63c794`](https://github.com/nodejs/node/commit/811b63c794)] - **(SEMVER-MINOR)** **http**: disallow two-byte characters in URL path (Benno Fünfstück) [nodejs-private/node-private#146](https://github.com/nodejs-private/node-private/pull/146)
- [[`618eebdd17`](https://github.com/nodejs/node/commit/618eebdd17)] - **(SEMVER-MINOR)** **http,https**: protect against slow headers attack (Matteo Collina) [nodejs-private/node-private#152](https://github.com/nodejs-private/node-private/pull/152)
- [[`b78d403da3`](https://github.com/nodejs/node/commit/b78d403da3)] - **openssl**: fix keypress requirement in apps on win32 (Shigeki Ohtsu) [nodejs/node#1389](https://github.com/nodejs/node/pull/1389)
- [[`35344e87bf`](https://github.com/nodejs/node/commit/35344e87bf)] - **src**: minor cleanup for node_revert (James M Snell) [#14864](https://github.com/nodejs/node/pull/14864)
- [[`a9791c9090`](https://github.com/nodejs/node/commit/a9791c9090)] - **src**: make debugger listen on 127.0.0.1 by default (Ben Noordhuis) [nodejs-private/node-private#148](https://github.com/nodejs-private/node-private/pull/148)
- [[`9c268d0492`](https://github.com/nodejs/node/commit/9c268d0492)] - **url**: avoid hostname spoofing w/ javascript protocol (Matteo Collina) [nodejs-private/node-private#145](https://github.com/nodejs-private/node-private/pull/145)

Windows 32-bit Installer: https://nodejs.org/dist/v6.15.0/node-v6.15.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v6.15.0/node-v6.15.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v6.15.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v6.15.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v6.15.0/node-v6.15.0.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v6.15.0/node-v6.15.0-darwin-x64.tar.gz \
Linux 32-bit Binary: https://nodejs.org/dist/v6.15.0/node-v6.15.0-linux-x86.tar.xz \
Linux 64-bit Binary: https://nodejs.org/dist/v6.15.0/node-v6.15.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v6.15.0/node-v6.15.0-linux-ppc64le.tar.xz \
Linux PPC BE 64-bit Binary: https://nodejs.org/dist/v6.15.0/node-v6.15.0-linux-ppc64.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v6.15.0/node-v6.15.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v6.15.0/node-v6.15.0-aix-ppc64.tar.gz \
SmartOS 32-bit Binary: https://nodejs.org/dist/v6.15.0/node-v6.15.0-sunos-x86.tar.xz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v6.15.0/node-v6.15.0-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v6.15.0/node-v6.15.0-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v6.15.0/node-v6.15.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v6.15.0/node-v6.15.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v6.15.0/node-v6.15.0.tar.gz \
Other release files: https://nodejs.org/dist/v6.15.0/ \
Documentation: https://nodejs.org/docs/v6.15.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

16dbfb9fc50992b7fe0551f5851ce7b540a850ee7692a709405eb87d622a3937  node-v6.15.0-aix-ppc64.tar.gz
131f8db034a120095b6c151c7890b763461d1675db5ecd6a2ca1e81387cebabc  node-v6.15.0-darwin-x64.tar.gz
14f41708079f759cdde50a71bce10cf741ef6763a790e42782eb9fc336f8e1d3  node-v6.15.0-darwin-x64.tar.xz
841c5f32f62957f3d583057b65a0c4c0f45bdb24eec07614393520883be17dcc  node-v6.15.0-headers.tar.gz
b7d74e83823dbfe42c4f81755a5ff6ea0e8a1a5e951cb1752161a0c0fb386f75  node-v6.15.0-headers.tar.xz
73653a567279be4b29f94f53f831cf886016ce200fd147d1c243838d4a96633d  node-v6.15.0-linux-arm64.tar.gz
5c9b3952ca65e4e4ba14da042a093e395b17b1f50567f4b2c86c6ac4a9399d23  node-v6.15.0-linux-arm64.tar.xz
5a818c8668515ebbf05c569a020951985ca13381159bc68c935ad735d536c573  node-v6.15.0-linux-armv6l.tar.gz
b99aecd4364b0293c485e722d57a8f34734fcdb4779479fbeb31415b3377df7d  node-v6.15.0-linux-armv6l.tar.xz
50b48b7fcb7a65ef1426056dcf1416f0b90aabbfd61fb251f8bf779319ad1054  node-v6.15.0-linux-armv7l.tar.gz
f33efaf3597d7f809d7ba7194122609f23f1abbea752b6c77dac2397043de156  node-v6.15.0-linux-armv7l.tar.xz
89c188b76655cb093240bea05bf62cf588d287c28c735ff95ae11629e9241ed0  node-v6.15.0-linux-ppc64le.tar.gz
64a4b7f70abefefa04e67307664842ed3022306fca8db64979d19883e604b658  node-v6.15.0-linux-ppc64le.tar.xz
c59d7f81ffac260b3c76ca86cf483fcdc17124a44043382bcb995667c3cce75f  node-v6.15.0-linux-ppc64.tar.gz
aa4a00f6073a1fb90c2c4dc981dc1ddc6d0c620b31438b0cd4b78924608b699d  node-v6.15.0-linux-ppc64.tar.xz
5d6873d0554331b2dc9608f6ec0274e05534f31e50d79c9a2a801afe4d70cc7f  node-v6.15.0-linux-s390x.tar.gz
e2e1e20e45082e57fbea1c8ba9ba2c3af0084de55167075d9e5f9197f5a0817d  node-v6.15.0-linux-s390x.tar.xz
4ef04373b2005a55aeaff24bc896f2045951d1909e7c1ac38ba4d1e5c9e85626  node-v6.15.0-linux-x64.tar.gz
63408a3a947fe057a572ea9c31d321f6b78acedc5014f39285a543e09e4c03f4  node-v6.15.0-linux-x64.tar.xz
63fd6a6f13b5cd7f2f7ff196db80e10e6c584647c492ec4b5a1f9fb7ee4b7add  node-v6.15.0-linux-x86.tar.gz
cad7faa4d1ba7dde0a14dcb0e72478e9474040c48abb8af8e9416f6e7f7f4c52  node-v6.15.0-linux-x86.tar.xz
2c747672da6bad59e37be1f23eed7175786538870e4bf6298e59fe6e3097d2c0  node-v6.15.0.pkg
a362620b5a0cb31d23a409d73c03b90f61ac456774291ebcffe7d57386ccd1b8  node-v6.15.0-sunos-x64.tar.gz
2e3340da1bb8f99ecb7c41fad7ece568066132ca5ece39c01188cb235829bd78  node-v6.15.0-sunos-x64.tar.xz
a70b79a4d46ac600e82b6a0998e57b8c39f3c940b079e91e7060f9ecf9ab5517  node-v6.15.0-sunos-x86.tar.gz
fa3b68eafeedf27f1d5e4d1afccab5d62ecd8f2082116ccddaa43e543685efff  node-v6.15.0-sunos-x86.tar.xz
05a896382571b8b952c9ef7cda0631abf0d7c9cc7cacf7c821d554cdb6f13451  node-v6.15.0.tar.gz
a757fa05fe4d0747b70e7f212e4dd77be75100eae7659cff87213808ac55e23f  node-v6.15.0.tar.xz
9158fae6a2d0c58a584dec70eadee1bf159da813956c23f2ccb8dd52752ccfc3  node-v6.15.0-win-x64.7z
4726c5dac77c15207dfb0cc05bb8fc1acaf276b635a95c70f04b865c00e40bf3  node-v6.15.0-win-x64.zip
dbf669b762b8e19040479250f4bb9128c84ddf9a002864866b2c18f3d091a551  node-v6.15.0-win-x86.7z
5263ffa117aba10838d0521d6f79e7cf56920b8d4d8dc60f4e48d004c997de9f  node-v6.15.0-win-x86.zip
654664b6384bc8d155fd1d5531160d0f66f1bed136d1fb4b900cbe9b9ee16079  node-v6.15.0-x64.msi
228a6b2b06d1ed349999e0fc883bfc2a0edd1bb72fe221b03993741b95fa8e9a  node-v6.15.0-x86.msi
308f0674c237358b5f836394cb54b72f6f7b2177a1bd22af9a544eaad0682dfb  win-x64/node.exe
9a1362c215d57c48061f8dd4fbaa176e18f00a632aaed67befd9d95b5b6a6d5a  win-x64/node.lib
9acf08fcf885e4ed78db3cd56d5d5754e20873c73951e5d63efbb9abcd724d21  win-x64/node_pdb.7z
5016b3fa79dfc13ec20023d0bfa1bcca607dee536c388827238b3b4686560a39  win-x64/node_pdb.zip
a914b3c8d6c1ce735d440cef2668528f5a9a21b7d7734609cd03267d491faf34  win-x86/node.exe
c4b0c4052779e29b5bd3fe803fea01f0b63b48fbffbd73ba99a988609c915de6  win-x86/node.lib
39f298c3e88387df71a3a60c2104adb24afc42ef66409eecd713299c38d8d2b9  win-x86/node_pdb.7z
639c48b85a31c0301ccc3eb6623638b8a6bd5c1957cb9ca72cf686b172882ba9  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQEzBAEBCAAdFiEE3Y8jOLrnUB491ax4wnN5L32DVF0FAlv947EACgkQwnN5L32D
VF1cdwgAl5nR+gi6SeqWEvrDoCJcM44TyjAilSOcACJFljXQ/MXr7m1TY+J+dNRl
+USXvnI6Sdr1v7NoXaGtHOBRqkzcKIGpeWuPFfHeJBA4UVe9P7I2DaOQ0FBpxxG3
EeEidECsyjPK22eL2SepwvI++Sj+ZE8rdC3L3DBgg0MS/UyP7M7/2IeL3DWgqqk5
RTkTWhG9Dq24hRAwaYKrjsOq9tB0BFDIhsndVpP/FyLHqzcHTas2cP+5Q+5raqK7
7KuheIH0mGh6TR1qUyZITRTRJxiuGKb8CI+XfdqX1skNjAu+5vdPlIn/OE7llqfV
QRm7nNFi6DEzhWtRJA3jvJGrlVNgTw==
=ZPD2
-----END PGP SIGNATURE-----

```
