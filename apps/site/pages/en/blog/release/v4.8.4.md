---
date: '2017-07-11T16:53:46.603Z'
category: release
title: Node v4.8.4 (Maintenance)
layout: blog-post
author: <PERSON><PERSON>
---

### Notable Changes

- **build**:
  - Disable V8 snapshots - The hashseed embedded in the snapshot is currently the same for all runs of the binary. This opens node up to collision attacks which could result in a Denial of Service. We have temporarily disabled snapshots until a more robust solution is found (<PERSON>)
- **deps**:
  - CVE-2017-1000381 - The c-ares function ares_parse_naptr_reply(), which is used for parsing NAPTR responses, could be triggered to read memory outside of the given input buffer if the passed in DNS response packet was crafted in a particular way. This patch checks that there is enough data for the required elements of an NAPTR record (2 int16, 3 bytes for string lengths) before processing a record. (<PERSON>)

### Commits

- [[`9d51bdc9d4`](https://github.com/nodejs/node/commit/9d51bdc9d4)] - **build**: disable V8 snapshots (<PERSON>) [nodejs/node-private#84](https://github.com/nodejs/node-private/pull/84)
- [[`80fe2662e4`](https://github.com/nodejs/node/commit/80fe2662e4)] - **deps**: cherry-pick 9478908a49 from cares upstream (David Drysdale) [nodejs/node-private#88](https://github.com/nodejs/node-private/pull/88)
- [[`d6969a717f`](https://github.com/nodejs/node/commit/d6969a717f)] - **http**: use Buffer.from to avoid Buffer(num) call (Сковорода Никита Андреевич) [nodejs/node-private#83](https://github.com/nodejs/node-private/pull/83)
- [[`58a8f150e5`](https://github.com/nodejs/node/commit/58a8f150e5)] - **test**: verify hash seed uniqueness (Ali Ijaz Sheikh) [nodejs/node-private#84](https://github.com/nodejs/node-private/pull/84)

Windows 32-bit Installer: https://nodejs.org/dist/v4.8.4/node-v4.8.4-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v4.8.4/node-v4.8.4-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v4.8.4/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v4.8.4/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v4.8.4/node-v4.8.4.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v4.8.4/node-v4.8.4-darwin-x64.tar.gz \
Linux 32-bit Binary: https://nodejs.org/dist/v4.8.4/node-v4.8.4-linux-x86.tar.xz \
Linux 64-bit Binary: https://nodejs.org/dist/v4.8.4/node-v4.8.4-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v4.8.4/node-v4.8.4-linux-ppc64le.tar.xz \
Linux PPC BE 64-bit Binary: https://nodejs.org/dist/v4.8.4/node-v4.8.4-linux-ppc64.tar.xz \
SmartOS 32-bit Binary: https://nodejs.org/dist/v4.8.4/node-v4.8.4-sunos-x86.tar.xz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v4.8.4/node-v4.8.4-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v4.8.4/node-v4.8.4-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v4.8.4/node-v4.8.4-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v4.8.4/node-v4.8.4-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v4.8.4/node-v4.8.4.tar.gz \
Other release files: https://nodejs.org/dist/v4.8.4/ \
Documentation: https://nodejs.org/docs/v4.8.4/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

06213900c7d6ed598355cd11348b204bbb4d8069eaa7425c067ee596f38e7c4d  node-v4.8.4-darwin-x64.tar.gz
0b944e08b51233005fa954056f1762a3fad92280e48e384b46dd5bd5695e6afd  node-v4.8.4-darwin-x64.tar.xz
fe5cbb91c64df926c49d923348b4861a4e9284c8ecc87e504fbd8ddfbada8384  node-v4.8.4-headers.tar.gz
34e879759e0b577dd3900df2092ab3c15df040fbce1fbecc6cca2bef7301ffdf  node-v4.8.4-headers.tar.xz
1eb8f8cadc8024fcb01f333ca8cac1db39735491a35122bf5ab1a40ee6bedced  node-v4.8.4-linux-arm64.tar.gz
ae2f5a61b6218fced05de8ca8d163d985238aa5e535af1ba2075bd53aa31244f  node-v4.8.4-linux-arm64.tar.xz
aaf8c7c4160ef4932004989cddc3723d8a5170ad86a2bd27e10b3e452702e086  node-v4.8.4-linux-armv6l.tar.gz
3daa79540c2133859d8d474004e6a7821cca4aa34931657f790707664c5f6346  node-v4.8.4-linux-armv6l.tar.xz
8d85b03be742b9494032f34833d766e3037934372cd0e118fd79fde00a3d8d91  node-v4.8.4-linux-armv7l.tar.gz
294ef24d3ec7224702b882d81ff9fa7e8c4988af7824c8378cafd2b72294fb14  node-v4.8.4-linux-armv7l.tar.xz
2a0627ad0683677febe99a523bb759f40c1934b7da2a1d9623dddda4ecd50063  node-v4.8.4-linux-ppc64le.tar.gz
a107957c34b21998abc914006b9566c262243ae38502595611da5cbdebea9283  node-v4.8.4-linux-ppc64le.tar.xz
9f252a4e892cdf99a081f3dfdb37ebdfc2c180df39f8850a35dc46fc59d3c2f6  node-v4.8.4-linux-ppc64.tar.gz
bb8d7167de39198fa631ab8a5b96cefa377048c5886895e1ddb2686991a39abe  node-v4.8.4-linux-ppc64.tar.xz
d1297b6ddec12498f19589c0f44768a9f250ad36ea17f1f715b44aeb89cd32b2  node-v4.8.4-linux-x64.tar.gz
a98fc4cf9fcfe1c94dc34c8f58383dc4c2a40f0078735bd8dbf4fa20e075af17  node-v4.8.4-linux-x64.tar.xz
53f377f91aad1719539ba7fb591587b3ff8db25dd22d5f23647753cf60184044  node-v4.8.4-linux-x86.tar.gz
49ceb4e295837b182a61afd03b9aee353734aba646e98686d928bba2cfe502ce  node-v4.8.4-linux-x86.tar.xz
b228e03c67aafb2b01456ef58dc6b238db6d31627205c7a69a6e8e072c961c9a  node-v4.8.4.pkg
edff2a0eb071df6d15706202d82da7f4f6fa113e77881d6b70b0b6c350816cd8  node-v4.8.4-sunos-x64.tar.gz
11a061ddcb6f877ffb977a269e731e59071d6b3314d254d0a5898e69438f193b  node-v4.8.4-sunos-x64.tar.xz
44803d9efb3f5b3b77f90d19e233c8a4e98b752de13ff10ceff9fe8a8094d011  node-v4.8.4-sunos-x86.tar.gz
d9a618c458e9ca91a2787fcd6598da5d77aff9292360078356d11ab11b515c88  node-v4.8.4-sunos-x86.tar.xz
b5480f69e9e929d2814de900fb5e673743a9d93f4fecbd79066baf18219f8584  node-v4.8.4.tar.gz
35fe633a48cbe93c79327161d9dc964ac9810f4ceb2ed8628487e6e14a15905b  node-v4.8.4.tar.xz
bad999c784955ed808a2ce67ed2c0dc4b71e2abde873fbe912128f74ae5310b0  node-v4.8.4-win-x64.7z
8ebb31340af2d490a272cf0d3fdf07a60914fd79f40ac54b25babba7fcd9af25  node-v4.8.4-win-x64.zip
95c026c449f653a4de6768ae3c319383411c9c4898bef25691ae0b6038774d31  node-v4.8.4-win-x86.7z
02a38a9a3c723e8df033a9d15d1ffe492b7da42cea8f56d81148c3ac930afe81  node-v4.8.4-win-x86.zip
f5a15cbf5944f566e0ff917686f9290a688dedc24ce4bd237846f6353c20b640  node-v4.8.4-x64.msi
72190dcb1cebdbb05899acc3dea5d2dcad94dc811db289d015bb0e065c92e4ae  node-v4.8.4-x86.msi
30e1308011f5e738d01ef406d3fe2648a66a7c042eb9aba3ca21b934a55fb61c  win-x64/node.exe
51e7ebeed49def988e3398bf8a70bbba67ed38346439924dbdf9c01827016789  win-x64/node.lib
0a919967ed1cb6d1f7763ddbb2594f8f93adbcab2296192310faaf52d35fd6d1  win-x64/node_pdb.7z
ab593c61f1a39c93774c551116fa96f6b904c3dde9a4caf95cd199aec33f8aca  win-x64/node_pdb.zip
540897f8d48d913b45eadb5201fc71475cd1652b414a7f035460aad2613c7594  win-x86/node.exe
35eb7a9960a773e8cac4c02336c500357a88c1937fda93466c1bd57eb8a63310  win-x86/node.lib
09dc53e16ab7d9b46419b5be872035349f428034beaa83008c3fd6798bdbc9e7  win-x86/node_pdb.7z
3070955c6e16c9f9ec5c6ce3a4bb1edc1efd2e1513ba6442d796f16207f5ee5e  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQEcBAEBCAAGBQJZZP/TAAoJEJM7AfQLXKlGd+gH/AhLQk+gW3NxrdgHhYUbQstS
uMjhSHBeNrwSk2UeFYimjE46SPf/v/zmjovcMyTEL/YzcNCSrEvuzdzjJ1U0ltxf
RngCDoZzJghpozLVQw97jClirblxOID6OpMNMV6zixs+JYzGvjK56RS8jE5mya1s
wZnHEOUAl/BVDvgQ7RGbL4lE9Kgms4PlnRZVqHPK23hZ9YwhMFRr3uMmduM8pasm
5Ea/wDIq7a9AlSgVk1CYkyfI0abgeIvihRa0BI8Q9I8wq517//VRi0Bcs2jcsUWO
/LZVxnz1lEuxRJB5FZ2Cx+PwxMwqITV1oUlNUhpZiPEukpZIeFtthYTrPTAZllI=
=0OBH
-----END PGP SIGNATURE-----

```
