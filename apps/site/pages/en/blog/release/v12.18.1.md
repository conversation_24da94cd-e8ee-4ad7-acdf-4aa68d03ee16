---
date: '2020-06-17T16:28:46.068Z'
category: release
title: Node v12.18.1 (LTS)
layout: blog-post
author: <PERSON>
---

### Notable Changes

- **deps**:
  - V8: cherry-pick 548f6c81d424 (<PERSON><PERSON><PERSON><PERSON> Blyžė) [#33484](https://github.com/nodejs/node/pull/33484)
  - update to uvwasi 0.0.9 (<PERSON>) [#33445](https://github.com/nodejs/node/pull/33445)
  - upgrade to libuv 1.38.0 (<PERSON>) [#33446](https://github.com/nodejs/node/pull/33446)
  - upgrade npm to 6.14.5 (<PERSON><PERSON>) [#33239](https://github.com/nodejs/node/pull/33239)

### Commits

- [[`ba93c8d87d`](https://github.com/nodejs/node/commit/ba93c8d87d)] - **async_hooks**: clear async_id_stack for terminations in more places (<PERSON>) [#33347](https://github.com/nodejs/node/pull/33347)
- [[`964adfafa5`](https://github.com/nodejs/node/commit/964adfafa5)] - **buffer**: improve copy() performance (Nikolai Vavilov) [#33214](https://github.com/nodejs/node/pull/33214)
- [[`af95bd70bd`](https://github.com/nodejs/node/commit/af95bd70bd)] - **deps**: V8: cherry-pick 548f6c81d424 (Dominykas Blyžė) [#33484](https://github.com/nodejs/node/pull/33484)
- [[`5c7176bf90`](https://github.com/nodejs/node/commit/5c7176bf90)] - **deps**: update to uvwasi 0.0.9 (Colin Ihrig) [#33445](https://github.com/nodejs/node/pull/33445)
- [[`402aa1b840`](https://github.com/nodejs/node/commit/402aa1b840)] - **deps**: upgrade to libuv 1.38.0 (Colin Ihrig) [#33446](https://github.com/nodejs/node/pull/33446)
- [[`4d6f56a76a`](https://github.com/nodejs/node/commit/4d6f56a76a)] - **deps**: upgrade npm to 6.14.5 (Ruy Adorno) [#33239](https://github.com/nodejs/node/pull/33239)
- [[`98a7026311`](https://github.com/nodejs/node/commit/98a7026311)] - **doc**: document module.path (Antoine du Hamel) [#33323](https://github.com/nodejs/node/pull/33323)
- [[`9572701705`](https://github.com/nodejs/node/commit/9572701705)] - **doc**: add fs.open() multiple constants example (Ethan Arrowood) [#33281](https://github.com/nodejs/node/pull/33281)
- [[`7d8a226958`](https://github.com/nodejs/node/commit/7d8a226958)] - **doc**: fix typos in handle scope descriptions (Tobias Nießen) [#33267](https://github.com/nodejs/node/pull/33267)
- [[`0c9b826ef8`](https://github.com/nodejs/node/commit/0c9b826ef8)] - **doc**: update function description for `decipher.setAAD` (Jonathan Buhacoff) [#33095](https://github.com/nodejs/node/pull/33095)
- [[`4749156f4b`](https://github.com/nodejs/node/commit/4749156f4b)] - **doc**: add comment about highWaterMark limit (Benjamin Gruenbaum) [#33432](https://github.com/nodejs/node/pull/33432)
- [[`a48aeb3f74`](https://github.com/nodejs/node/commit/a48aeb3f74)] - **doc**: clarify about the Node.js-only extensions in perf_hooks (Joyee Cheung) [#33199](https://github.com/nodejs/node/pull/33199)
- [[`a9ed287f00`](https://github.com/nodejs/node/commit/a9ed287f00)] - **doc**: fix extension in esm example (Gus Caplan) [#33408](https://github.com/nodejs/node/pull/33408)
- [[`d2897a2836`](https://github.com/nodejs/node/commit/d2897a2836)] - **doc**: enhance guides by fixing and making grammar more consistent (Chris Holland) [#33152](https://github.com/nodejs/node/pull/33152)
- [[`3d8ba292e2`](https://github.com/nodejs/node/commit/3d8ba292e2)] - **doc**: add examples for implementing ESM (unknown) [#33168](https://github.com/nodejs/node/pull/33168)
- [[`318fcf8188`](https://github.com/nodejs/node/commit/318fcf8188)] - **doc**: add note about clientError writable handling (Paolo Insogna) [#33308](https://github.com/nodejs/node/pull/33308)
- [[`30c9cb556f`](https://github.com/nodejs/node/commit/30c9cb556f)] - **doc**: fix typo in n-api.md (Daniel Bevenius) [#33319](https://github.com/nodejs/node/pull/33319)
- [[`9dde1db332`](https://github.com/nodejs/node/commit/9dde1db332)] - **doc**: add warning for socket.connect reuse (Robert Nagy) [#33204](https://github.com/nodejs/node/pull/33204)
- [[`0c7cf24431`](https://github.com/nodejs/node/commit/0c7cf24431)] - **doc**: correct description of `decipher.setAuthTag` in crypto.md (Jonathan Buhacoff)
- [[`59619b0c9a`](https://github.com/nodejs/node/commit/59619b0c9a)] - **doc**: mention python3-distutils dependency in BUILDING.md (osher) [#33174](https://github.com/nodejs/node/pull/33174)
- [[`0cee4c3eae`](https://github.com/nodejs/node/commit/0cee4c3eae)] - **doc**: removed unnecessary util imports from vm examples (Karol Walasek) [#33179](https://github.com/nodejs/node/pull/33179)
- [[`903862089b`](https://github.com/nodejs/node/commit/903862089b)] - **doc**: update Buffer(size) documentation (Nikolai Vavilov) [#33198](https://github.com/nodejs/node/pull/33198)
- [[`8b44be9b26`](https://github.com/nodejs/node/commit/8b44be9b26)] - **doc**: add Uint8Array to `end` and `write` (Pranshu Srivastava) [#33217](https://github.com/nodejs/node/pull/33217)
- [[`4a584200f8`](https://github.com/nodejs/node/commit/4a584200f8)] - **doc**: specify unit of time passed to `fs.utimes` (Simen Bekkhus) [#33230](https://github.com/nodejs/node/pull/33230)
- [[`ad7a890597`](https://github.com/nodejs/node/commit/ad7a890597)] - **doc**: add troubleshooting guide for AsyncLocalStorage (Andrey Pechkurov) [#33248](https://github.com/nodejs/node/pull/33248)
- [[`2262962ab7`](https://github.com/nodejs/node/commit/2262962ab7)] - **doc**: remove AsyncWrap mentions from async_hooks.md (Andrey Pechkurov) [#33249](https://github.com/nodejs/node/pull/33249)
- [[`ac5cdd682a`](https://github.com/nodejs/node/commit/ac5cdd682a)] - **doc**: add warnings about transferring Buffers and ArrayBuffer (James M Snell) [#33252](https://github.com/nodejs/node/pull/33252)
- [[`033bc96ec1`](https://github.com/nodejs/node/commit/033bc96ec1)] - **doc**: update napi_async_init documentation (Michael Dawson) [#33181](https://github.com/nodejs/node/pull/33181)
- [[`ea3a68f74f`](https://github.com/nodejs/node/commit/ea3a68f74f)] - **doc**: doc and test URLSearchParams discrepancy (James M Snell) [#33236](https://github.com/nodejs/node/pull/33236)
- [[`c6cf0483f2`](https://github.com/nodejs/node/commit/c6cf0483f2)] - **doc**: explicitly doc package.exports is breaking (Myles Borins) [#33074](https://github.com/nodejs/node/pull/33074)
- [[`e572cf93e5`](https://github.com/nodejs/node/commit/e572cf93e5)] - **doc**: fix style and grammer in buffer.md (Nikolai Vavilov) [#33194](https://github.com/nodejs/node/pull/33194)
- [[`5d80576889`](https://github.com/nodejs/node/commit/5d80576889)] - **errors**: skip fatal error highlighting on windows (Thomas) [#33132](https://github.com/nodejs/node/pull/33132)
- [[`a029dca90e`](https://github.com/nodejs/node/commit/a029dca90e)] - **esm**: improve commonjs hint on module not found (Antoine du Hamel) [#33220](https://github.com/nodejs/node/pull/33220)
- [[`c129e8809e`](https://github.com/nodejs/node/commit/c129e8809e)] - **fs**: forbid concurrent operations on Dir handle (Anna Henningsen) [#33274](https://github.com/nodejs/node/pull/33274)
- [[`aa4611cccb`](https://github.com/nodejs/node/commit/aa4611cccb)] - **fs**: clean up Dir.read() uv_fs_t data before calling into JS (Anna Henningsen) [#33274](https://github.com/nodejs/node/pull/33274)
- [[`fa4a37c57b`](https://github.com/nodejs/node/commit/fa4a37c57b)] - **http2**: comment on usage of `Object.create(null)` (Pranshu Srivastava) [#33183](https://github.com/nodejs/node/pull/33183)
- [[`66dbaff848`](https://github.com/nodejs/node/commit/66dbaff848)] - **http2**: add `bytesWritten` test for `Http2Stream` (Pranshu Srivastava) [#33162](https://github.com/nodejs/node/pull/33162)
- [[`59769c4d14`](https://github.com/nodejs/node/commit/59769c4d14)] - **lib**: fix typo in timers insert function comment (Daniel Bevenius) [#33301](https://github.com/nodejs/node/pull/33301)
- [[`6881410951`](https://github.com/nodejs/node/commit/6881410951)] - **lib**: refactored scheduling policy assignment (Yash Ladha) [#32663](https://github.com/nodejs/node/pull/32663)
- [[`9017bce54b`](https://github.com/nodejs/node/commit/9017bce54b)] - **lib**: fix grammar in internal/bootstrap/loaders.js (szTheory) [#33211](https://github.com/nodejs/node/pull/33211)
- [[`d64dbfa1e7`](https://github.com/nodejs/node/commit/d64dbfa1e7)] - **meta**: add issue template for API reference docs (Derek Lewis) [#32944](https://github.com/nodejs/node/pull/32944)
- [[`4f6e4ae49d`](https://github.com/nodejs/node/commit/4f6e4ae49d)] - **module**: add specific error for dir import (Antoine du HAMEL) [#33220](https://github.com/nodejs/node/pull/33220)
- [[`77caf92314`](https://github.com/nodejs/node/commit/77caf92314)] - **module**: better error for named exports from cjs (Myles Borins) [#33256](https://github.com/nodejs/node/pull/33256)
- [[`82da74b1cd`](https://github.com/nodejs/node/commit/82da74b1cd)] - **n-api**: add uint32 test for -1 (Gabriel Schulhof)
- [[`68551d22d2`](https://github.com/nodejs/node/commit/68551d22d2)] - **perf_hooks**: fix error message for invalid entryTypes (Michaël Zasso) [#33285](https://github.com/nodejs/node/pull/33285)
- [[`e67df04df2`](https://github.com/nodejs/node/commit/e67df04df2)] - **src**: use BaseObjectPtr in StreamReq::Dispose (James M Snell) [#33102](https://github.com/nodejs/node/pull/33102)
- [[`c797c7c7ab`](https://github.com/nodejs/node/commit/c797c7c7ab)] - **src**: reduce duplication in RegisterHandleCleanups (Daniel Bevenius) [#33421](https://github.com/nodejs/node/pull/33421)
- [[`548db2e5b9`](https://github.com/nodejs/node/commit/548db2e5b9)] - **src**: remove unused IsolateSettings variable (Daniel Bevenius) [#33417](https://github.com/nodejs/node/pull/33417)
- [[`e668376b5b`](https://github.com/nodejs/node/commit/e668376b5b)] - **src**: remove unused misc variable (Daniel Bevenius) [#33417](https://github.com/nodejs/node/pull/33417)
- [[`9883ba6ddd`](https://github.com/nodejs/node/commit/9883ba6ddd)] - **src**: add promise_resolve to SetupHooks comment (Daniel Bevenius) [#33365](https://github.com/nodejs/node/pull/33365)
- [[`b924910fe7`](https://github.com/nodejs/node/commit/b924910fe7)] - **src**: distinguish refed/unrefed threadsafe Immediates (Anna Henningsen) [#33320](https://github.com/nodejs/node/pull/33320)
- [[`29d24db914`](https://github.com/nodejs/node/commit/29d24db914)] - **src**: add #include \<string\> in json_utils.h (Cheng Zhao) [#33332](https://github.com/nodejs/node/pull/33332)
- [[`a0bc2e3b64`](https://github.com/nodejs/node/commit/a0bc2e3b64)] - **src**: replace to CHECK_NOT_NULL in node_crypto (himself65) [#33383](https://github.com/nodejs/node/pull/33383)
- [[`1f159e45f2`](https://github.com/nodejs/node/commit/1f159e45f2)] - **src**: add primordials to arguments comment (Daniel Bevenius) [#33318](https://github.com/nodejs/node/pull/33318)
- [[`fe780a5fe0`](https://github.com/nodejs/node/commit/fe780a5fe0)] - **src**: remove unused using declarations in node.cc (Daniel Bevenius) [#33261](https://github.com/nodejs/node/pull/33261)
- [[`82c43d1594`](https://github.com/nodejs/node/commit/82c43d1594)] - **src**: delete unused variables to resolve compile time print warning (rickyes) [#33358](https://github.com/nodejs/node/pull/33358)
- [[`548672d39c`](https://github.com/nodejs/node/commit/548672d39c)] - **src**: use MaybeLocal.ToLocal instead of IsEmpty (Daniel Bevenius) [#33312](https://github.com/nodejs/node/pull/33312)
- [[`f27ae6ef46`](https://github.com/nodejs/node/commit/f27ae6ef46)] - **src**: fix typo in comment in async_wrap.cc (Daniel Bevenius) [#33350](https://github.com/nodejs/node/pull/33350)
- [[`b6300793fb`](https://github.com/nodejs/node/commit/b6300793fb)] - **src**: remove unnecessary Isolate::GetCurrent() calls (Anna Henningsen) [#33298](https://github.com/nodejs/node/pull/33298)
- [[`642f81317e`](https://github.com/nodejs/node/commit/642f81317e)] - **src**: fix invalid windowBits=8 gzip segfault (Ben Noordhuis) [#33045](https://github.com/nodejs/node/pull/33045)
- [[`a5e8c5ce0d`](https://github.com/nodejs/node/commit/a5e8c5ce0d)] - **src**: split out callback queue implementation from Environment (Anna Henningsen) [#33272](https://github.com/nodejs/node/pull/33272)
- [[`ed62d43e79`](https://github.com/nodejs/node/commit/ed62d43e79)] - **src**: clean up large pages code (Gabriel Schulhof) [#33255](https://github.com/nodejs/node/pull/33255)
- [[`c05483483f`](https://github.com/nodejs/node/commit/c05483483f)] - **_Revert_** "**src**: add test/abort build tasks" (Richard Lau) [#33196](https://github.com/nodejs/node/pull/33196)
- [[`b43fc64aa7`](https://github.com/nodejs/node/commit/b43fc64aa7)] - **_Revert_** "**src**: add aliased-buffer-overflow abort test" (Richard Lau) [#33196](https://github.com/nodejs/node/pull/33196)
- [[`edf75e4299`](https://github.com/nodejs/node/commit/edf75e4299)] - **src**: use basename(argv0) for --trace-uncaught suggestion (Anna Henningsen) [#32798](https://github.com/nodejs/node/pull/32798)
- [[`4294d92b26`](https://github.com/nodejs/node/commit/4294d92b26)] - **stream**: make from read one at a time (Robert Nagy) [#33201](https://github.com/nodejs/node/pull/33201)
- [[`194789f25b`](https://github.com/nodejs/node/commit/194789f25b)] - **stream**: make all streams error in a pipeline (Matteo Collina) [#30869](https://github.com/nodejs/node/pull/30869)
- [[`5da7d52a9f`](https://github.com/nodejs/node/commit/5da7d52a9f)] - **test**: regression tests for async_hooks + Promise + Worker interaction (Anna Henningsen) [#33347](https://github.com/nodejs/node/pull/33347)
- [[`9f594be75a`](https://github.com/nodejs/node/commit/9f594be75a)] - **test**: fix test-dns-idna2008 (Rich Trott) [#33367](https://github.com/nodejs/node/pull/33367)
- [[`33a787873f`](https://github.com/nodejs/node/commit/33a787873f)] - **test**: refactor WPTRunner (Joyee Cheung) [#33297](https://github.com/nodejs/node/pull/33297)
- [[`fa1631355f`](https://github.com/nodejs/node/commit/fa1631355f)] - **test**: update WPT interfaces and hr-time (Joyee Cheung) [#33297](https://github.com/nodejs/node/pull/33297)
- [[`c459832e4b`](https://github.com/nodejs/node/commit/c459832e4b)] - **test**: fix test-net-throttle (Rich Trott) [#33329](https://github.com/nodejs/node/pull/33329)
- [[`cd92052935`](https://github.com/nodejs/node/commit/cd92052935)] - **test**: add hr-time Web platform tests (Michaël Zasso) [#33287](https://github.com/nodejs/node/pull/33287)
- [[`0177cbf9e0`](https://github.com/nodejs/node/commit/0177cbf9e0)] - **test**: rename test-lookupService-promises (rickyes) [#33100](https://github.com/nodejs/node/pull/33100)
- [[`139eb6bd68`](https://github.com/nodejs/node/commit/139eb6bd68)] - **test**: skip some console tests on dumb terminal (Adam Majer) [#33165](https://github.com/nodejs/node/pull/33165)
- [[`1766514c5b`](https://github.com/nodejs/node/commit/1766514c5b)] - **test**: add tests for options.fs in fs streams (Julian Duque) [#33185](https://github.com/nodejs/node/pull/33185)
- [[`7315c2288a`](https://github.com/nodejs/node/commit/7315c2288a)] - **tls**: fix --tls-keylog option (Alba Mendez) [#33366](https://github.com/nodejs/node/pull/33366)
- [[`e240d56983`](https://github.com/nodejs/node/commit/e240d56983)] - **tools**: update dependencies for markdown linting (Rich Trott) [#33412](https://github.com/nodejs/node/pull/33412)
- [[`2645b1c85b`](https://github.com/nodejs/node/commit/2645b1c85b)] - **tools**: update ESLint to 7.0.0 (Colin Ihrig) [#33316](https://github.com/nodejs/node/pull/33316)
- [[`cdd7d3a66d`](https://github.com/nodejs/node/commit/cdd7d3a66d)] - **tools**: remove obsolete no-restricted-syntax eslint rules (Ruben Bridgewater) [#32161](https://github.com/nodejs/node/pull/32161)
- [[`5d5e66c10c`](https://github.com/nodejs/node/commit/5d5e66c10c)] - **tools**: add eslint rule to only pass through 'test' to debuglog (Ruben Bridgewater) [#32161](https://github.com/nodejs/node/pull/32161)
- [[`22f2c2c871`](https://github.com/nodejs/node/commit/22f2c2c871)] - **wasi**: fix poll_oneoff memory interface (Colin Ihrig) [#33250](https://github.com/nodejs/node/pull/33250)
- [[`33aacbefb1`](https://github.com/nodejs/node/commit/33aacbefb1)] - **wasi**: prevent syscalls before start (Tobias Nießen) [#33235](https://github.com/nodejs/node/pull/33235)
- [[`5eed20b3b7`](https://github.com/nodejs/node/commit/5eed20b3b7)] - **worker**: fix race condition in node_messaging.cc (Anna Henningsen) [#33429](https://github.com/nodejs/node/pull/33429)
- [[`b4d903402b`](https://github.com/nodejs/node/commit/b4d903402b)] - **worker**: fix crash when .unref() is called during exit (Anna Henningsen) [#33394](https://github.com/nodejs/node/pull/33394)
- [[`8a926982e5`](https://github.com/nodejs/node/commit/8a926982e5)] - **worker**: call CancelTerminateExecution() before exiting Locker (Anna Henningsen) [#33347](https://github.com/nodejs/node/pull/33347)
- [[`631e433cf5`](https://github.com/nodejs/node/commit/631e433cf5)] - **zlib**: reject windowBits=8 when mode=GZIP (Ben Noordhuis) [#33045](https://github.com/nodejs/node/pull/33045)

Windows 32-bit Installer: https://nodejs.org/dist/v12.18.1/node-v12.18.1-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v12.18.1/node-v12.18.1-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v12.18.1/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v12.18.1/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v12.18.1/node-v12.18.1.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v12.18.1/node-v12.18.1-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v12.18.1/node-v12.18.1-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v12.18.1/node-v12.18.1-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v12.18.1/node-v12.18.1-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v12.18.1/node-v12.18.1-aix-ppc64.tar.gz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v12.18.1/node-v12.18.1-sunos-x64.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v12.18.1/node-v12.18.1-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v12.18.1/node-v12.18.1-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v12.18.1/node-v12.18.1.tar.gz \
Other release files: https://nodejs.org/dist/v12.18.1/ \
Documentation: https://nodejs.org/docs/v12.18.1/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

ff5ff4f96630cff7c7aa8fe54f42bdae6ada43ac8043badb4fb4ed3ae3e5b3e9  node-v12.18.1-aix-ppc64.tar.gz
80e1d644fe78838da47cd16de234b612c20e06ffe14447125db9622e381ed1ba  node-v12.18.1-darwin-x64.tar.gz
f7829470fce27686c4e91c5c84ba5f4488fbbb88e222116cc0a960531cedf708  node-v12.18.1-darwin-x64.tar.xz
ee79ef8156c02bfba3960391e85966a49c311317fe08195de52bd94b89d5efc8  node-v12.18.1-headers.tar.gz
4693c672dfe1442c01f606bb6bd37c54c69bc5de262d69b68d5bffd462e78607  node-v12.18.1-headers.tar.xz
b78fc542858b83a96d712d6a2f493ae87e1af55040bc55fb68671af191016d19  node-v12.18.1-linux-arm64.tar.gz
b6683e6e887c6c44a3fe9ff419e80d36eaafed39eb2c2d1b04ee54b440a03217  node-v12.18.1-linux-arm64.tar.xz
8222c9f2a191a6c18c634261aa8fd3af240ff5c2480fa93b618b6f15101ad054  node-v12.18.1-linux-armv7l.tar.gz
c9e982b061e69208672319b7621d8c99e08f41cae986b2f62dfa3c704bbda0fa  node-v12.18.1-linux-armv7l.tar.xz
6f11fdad35c73916445783babed82b797f811758a473d7a7cc4335a2165d112b  node-v12.18.1-linux-ppc64le.tar.gz
a1da92aadd5445490be343e16fbf26b7d095eeb44d23a4edec6cb9d3a323cada  node-v12.18.1-linux-ppc64le.tar.xz
91ce06e9f24339f9d27f57e9b215f923f2c23be7b1eaace5d3132c323faa09f6  node-v12.18.1-linux-s390x.tar.gz
0623ddfa7b8f76dc23eacc80961ebfd8a45eb29183c8af17b29ab41982d30d8e  node-v12.18.1-linux-s390x.tar.xz
b89a0d497674f388705c877ad4f57766695cfe26ea6c6c9d3ad6ff98827edbfe  node-v12.18.1-linux-x64.tar.gz
863f816967e297c9eb221ad3cf32521f7ac46fffc66750e60f159ed63809affa  node-v12.18.1-linux-x64.tar.xz
18426243bc47411865b79e8945b6e07ae42ece0afcd6723014045f398602c4d1  node-v12.18.1.pkg
c759d4d6565671c427a75c1251b701616cefb217a7fb28bb9c668a28c63f23c7  node-v12.18.1-sunos-x64.tar.gz
f7d33d47848541ac1a15913883bdc8376865aab48914144c19259ee45081b126  node-v12.18.1-sunos-x64.tar.xz
be532894112d06e671cb3d9388e2a3fa2c690d40bdfa9e432921a9e078b3f241  node-v12.18.1.tar.gz
ca59051c7a307841c15b4fe141c74354cb191964106e1bdfe405551a3d6a5c7a  node-v12.18.1.tar.xz
a1842ce62132483d4fe3f7bf16639fa656c5d7ece9ecc09df3958a93a9a6d54e  node-v12.18.1-win-x64.7z
93039ebfc7c5bfad168b015f77667757925070fff3ae84c3eb73348b3123a82a  node-v12.18.1-win-x64.zip
0e7f814063785bb9a108d88bcc15c31d826c21cd3ef22920f9fe5eb54490dde0  node-v12.18.1-win-x86.7z
d0b955201949a8da102affa0a9cdda167718f2538f2aaf4f3acd53f098bc0764  node-v12.18.1-win-x86.zip
f318bb719bd956d4734247c58b3937daef04a01dbee4abdfe6217bdea87932f4  node-v12.18.1-x64.msi
f4a2fc3406e9208b3f8be30c7ae46427d76fcea18cf878368ca7a8fa47dc7ea6  node-v12.18.1-x86.msi
3455deb066f79080b8fa8bc76196d54ff8908bafd933fe73dea29b7e8b0c05c7  win-x64/node.exe
125affc115fe2b0162dfb4c8b12967d0b584d171c19dc820babe89ba64ea6b83  win-x64/node.lib
e8ce4e64f68aa989e0055eb613bb0ccbd093bc484c3c77a6594da53092cf1747  win-x64/node_pdb.7z
a7d746a835b4d5bec065661b5d278eabdfe5c82dbaf9e4346b68118cd4420023  win-x64/node_pdb.zip
f889e45bb1024ade94c30d12ba70a92ff673802715a315035bd6a1d7a3e1afa2  win-x86/node.exe
213e2183bb108a0f64c9e37f144ddd7d8b14718858112c3ccabd09eed16621ca  win-x86/node.lib
8d9d7053c1b729bd84dfa90f6d90580e845e22886f34a86599847875814ad895  win-x86/node_pdb.7z
eaae14543facc5355279f7cd2df1300418fcb157c0c0c4fbc80cb20022c93067  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEueL1mBqm4M0oFg2f8TmTp1WZZTwFAl7qRI4ACgkQ8TmTp1WZ
ZTzyZQ/+LHYyZcTJetpmI/n4bDlkgbjbnh5r1YL/JPGiAEBCPljMj5+tiOrvG6ri
/DEKcBBVWdADlBmCdOjXD3TuxLYqeRFAkahuVm1m8P1FhgDvDjtTBEGMlCl/eIvs
DYyDdZWCC4oD8IwAVSoIq78XCHv2oTG2HZOz9fmLpPSkC7wp0XLPanpDHQVXIAiA
9mBeS/3P5Z1jm48sX1NZCd0ejleJ88rH18p/Sj77+OcT0tfMKBDnAVWkcyDgmxGz
8JAWgOIpVnq8MER1BaqhrzZ/N2ayAzmt187gDqYtfeHixAQulIYyl5IhlgX1zLI3
mowZFokoWfktaY3tCrHVAdGDdCiakuuVwtrDPUtmDOtSegvaWQmn45spT5N97L/w
d5aJHd2x2x19uRxENl8VUHeyfXn8b3JKox7ADuzeXZ+aND7SwNH6RHe64Qh+tngF
HyxDPzNPG+UVbm9EzWTjYL9wQ5P2nKDcBByjmAP7YtIrBCqivPd0uL/y47T9MsCu
kdvPSBX+O0czevmWcr7Zgt5oNR8hIKEhZ3aQ26DlB89O0Wi/n2rfvjPuZtdbJyGf
B87MoWLbkgVV76j8wgh18mSueHiBBgV8M0d1/vjdxrMEkZJkOQ3SB/xvD7dHjSFo
iglHPsReS0T7+LytWRLtF77YbbJ6HppRfarLUBeAdNRxojsFBE0=
=fih8
-----END PGP SIGNATURE-----

```
