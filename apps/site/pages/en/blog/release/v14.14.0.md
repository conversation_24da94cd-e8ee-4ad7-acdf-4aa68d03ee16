---
date: '2020-10-16T00:08:13.893Z'
category: release
title: Node v14.14.0 (Current)
layout: blog-post
author: <PERSON><PERSON>
---

### Notable Changes

- [[`7e7afc5186`](https://github.com/nodejs/node/commit/7e7afc5186)] - **crypto**: update certdata to NSS 3.56 (<PERSON>) [#35546](https://github.com/nodejs/node/pull/35546)
- [[`8877430530`](https://github.com/nodejs/node/commit/8877430530)] - **doc**: add aduh95 to collaborators (<PERSON>) [#35542](https://github.com/nodejs/node/pull/35542)
- [[`1610728d7c`](https://github.com/nodejs/node/commit/1610728d7c)] - **(SEMVER-MINOR)** **fs**: add rm method (<PERSON>) [#35494](https://github.com/nodejs/node/pull/35494)
- [[`6ff152cc67`](https://github.com/nodejs/node/commit/6ff152cc67)] - **(SEMVER-MINOR)** **http**: allow passing array of key/val into writeHead (Robert Nagy) [#35274](https://github.com/nodejs/node/pull/35274)
- [[`93f947af0a`](https://github.com/nodejs/node/commit/93f947af0a)] - **(SEMVER-MINOR)** **src**: expose v8::Isolate setup callbacks (Shelley Vohr) [#35512](https://github.com/nodejs/node/pull/35512)

### Commits

- [[`16c17ddd88`](https://github.com/nodejs/node/commit/16c17ddd88)] - **build**: fix Commit Queue failure comment (Antoine du Hamel) [#35599](https://github.com/nodejs/node/pull/35599)
- [[`b7305284e2`](https://github.com/nodejs/node/commit/b7305284e2)] - **build**: gitHub actions: Python 3.9 and actions/setup-python@v2 (Christian Clauss) [#35521](https://github.com/nodejs/node/pull/35521)
- [[`e8fcbc8318`](https://github.com/nodejs/node/commit/e8fcbc8318)] - **build**: fix landed message for multiple commits in commit-queue (Denys Otrishko) [#35226](https://github.com/nodejs/node/pull/35226)
- [[`84c0adefa0`](https://github.com/nodejs/node/commit/84c0adefa0)] - **build**: add Commit Queue actions url to failure comment (Denys Otrishko) [#35206](https://github.com/nodejs/node/pull/35206)
- [[`9c74d4598d`](https://github.com/nodejs/node/commit/9c74d4598d)] - **build**: fuzzer that targets node::LoadEnvironment() (davkor) [#34844](https://github.com/nodejs/node/pull/34844)
- [[`f552e5c251`](https://github.com/nodejs/node/commit/f552e5c251)] - **build**: improved release lint error message (Shelley Vohr) [#35523](https://github.com/nodejs/node/pull/35523)
- [[`7e7afc5186`](https://github.com/nodejs/node/commit/7e7afc5186)] - **crypto**: update certdata to NSS 3.56 (Shelley Vohr) [#35546](https://github.com/nodejs/node/pull/35546)
- [[`b8529a7104`](https://github.com/nodejs/node/commit/b8529a7104)] - **deps**: V8: cherry-pick 3176bfd447a9 (Anna Henningsen) [#35612](https://github.com/nodejs/node/pull/35612)
- [[`0c877762ea`](https://github.com/nodejs/node/commit/0c877762ea)] - **doc**: document Buffer.concat may use internal pool (Andrey Pechkurov) [#35541](https://github.com/nodejs/node/pull/35541)
- [[`6284f0dbc2`](https://github.com/nodejs/node/commit/6284f0dbc2)] - **doc**: use test username instead of real (Pooja D.P) [#35611](https://github.com/nodejs/node/pull/35611)
- [[`78259b6519`](https://github.com/nodejs/node/commit/78259b6519)] - **doc**: add doc for starting ci job via label (Juan José Arboleda) [#35551](https://github.com/nodejs/node/pull/35551)
- [[`41d7500bf9`](https://github.com/nodejs/node/commit/41d7500bf9)] - **doc**: fix unit of size argument of readable.read (Tobias Nießen) [#35051](https://github.com/nodejs/node/pull/35051)
- [[`00eff4a534`](https://github.com/nodejs/node/commit/00eff4a534)] - **doc**: add missing deprecation number (Benjamin Coe) [#35630](https://github.com/nodejs/node/pull/35630)
- [[`9288f9d74b`](https://github.com/nodejs/node/commit/9288f9d74b)] - **doc**: harmonize YAML comments (Antoine du Hamel) [#35575](https://github.com/nodejs/node/pull/35575)
- [[`16f8298170`](https://github.com/nodejs/node/commit/16f8298170)] - **doc**: revise description of process.ppid (Pooja D.P) [#35589](https://github.com/nodejs/node/pull/35589)
- [[`cad86d40de`](https://github.com/nodejs/node/commit/cad86d40de)] - **doc**: add symlink information for process.execpath (Pooja D.P) [#35590](https://github.com/nodejs/node/pull/35590)
- [[`4025fc811d`](https://github.com/nodejs/node/commit/4025fc811d)] - **doc**: add PoojaDurgad as a triager (Pooja D.P) [#35153](https://github.com/nodejs/node/pull/35153)
- [[`809cd07968`](https://github.com/nodejs/node/commit/809cd07968)] - **doc**: document rmdir/recursive deprecation (Benjamin Coe) [#35579](https://github.com/nodejs/node/pull/35579)
- [[`9d1b7ac334`](https://github.com/nodejs/node/commit/9d1b7ac334)] - **doc**: edit fs.md for minor style changes (Rich Trott) [#35505](https://github.com/nodejs/node/pull/35505)
- [[`c1bb364105`](https://github.com/nodejs/node/commit/c1bb364105)] - **doc**: run license builder (Myles Borins) [#35577](https://github.com/nodejs/node/pull/35577)
- [[`970975b588`](https://github.com/nodejs/node/commit/970975b588)] - **doc**: use kbd element in process doc (Rich Trott) [#35584](https://github.com/nodejs/node/pull/35584)
- [[`64ebbddb5f`](https://github.com/nodejs/node/commit/64ebbddb5f)] - **doc**: fixup perf_hooks (Antoine du Hamel) [#35527](https://github.com/nodejs/node/pull/35527)
- [[`b074717af7`](https://github.com/nodejs/node/commit/b074717af7)] - **doc**: remove incorrect synchronous label (Colin Ihrig) [#35561](https://github.com/nodejs/node/pull/35561)
- [[`ddf13e0df0`](https://github.com/nodejs/node/commit/ddf13e0df0)] - **doc**: make fs.rm()'s force docs consistent (Colin Ihrig) [#35561](https://github.com/nodejs/node/pull/35561)
- [[`4164477b43`](https://github.com/nodejs/node/commit/4164477b43)] - **doc**: simplify wording in tracing APIs doc (Pooja D.P) [#35556](https://github.com/nodejs/node/pull/35556)
- [[`c865b02397`](https://github.com/nodejs/node/commit/c865b02397)] - **doc**: improve SIGINT error text (Rich Trott) [#35558](https://github.com/nodejs/node/pull/35558)
- [[`7d1cdd411f`](https://github.com/nodejs/node/commit/7d1cdd411f)] - **doc**: move package.import content higher (Myles Borins) [#35535](https://github.com/nodejs/node/pull/35535)
- [[`62755b6230`](https://github.com/nodejs/node/commit/62755b6230)] - **doc**: harmonize changes list ordering (Antoine du Hamel) [#35454](https://github.com/nodejs/node/pull/35454)
- [[`8cacca0f62`](https://github.com/nodejs/node/commit/8cacca0f62)] - **doc**: changes description must end with a period (Antoine du Hamel) [#35454](https://github.com/nodejs/node/pull/35454)
- [[`28c94ca123`](https://github.com/nodejs/node/commit/28c94ca123)] - **doc**: harmonize version list style in YAML comments (Antoine du Hamel) [#35454](https://github.com/nodejs/node/pull/35454)
- [[`b3f15b7d89`](https://github.com/nodejs/node/commit/b3f15b7d89)] - **doc**: fix missing PR-URLs in YAML comments (Antoine du Hamel) [#35454](https://github.com/nodejs/node/pull/35454)
- [[`02bf73e239`](https://github.com/nodejs/node/commit/02bf73e239)] - **doc**: remove outstanding YAML comment (Antoine du Hamel) [#35454](https://github.com/nodejs/node/pull/35454)
- [[`a5552468d2`](https://github.com/nodejs/node/commit/a5552468d2)] - **doc**: harmonize YAML comments style in deprecations.md (Antoine du Hamel) [#35454](https://github.com/nodejs/node/pull/35454)
- [[`863ba4b7da`](https://github.com/nodejs/node/commit/863ba4b7da)] - **doc**: refactor the n-api matrix (Michael Dawson) [#35345](https://github.com/nodejs/node/pull/35345)
- [[`85dc84d1bb`](https://github.com/nodejs/node/commit/85dc84d1bb)] - **doc**: use sentence case for class property (Rich Trott) [#35540](https://github.com/nodejs/node/pull/35540)
- [[`01c9c59c85`](https://github.com/nodejs/node/commit/01c9c59c85)] - **doc**: add history entry for exports patterns (Antoine du Hamel) [#35410](https://github.com/nodejs/node/pull/35410)
- [[`51b988ba0f`](https://github.com/nodejs/node/commit/51b988ba0f)] - **doc**: fix deprecation history (Antoine du Hamel) [#35455](https://github.com/nodejs/node/pull/35455)
- [[`328c624cdf`](https://github.com/nodejs/node/commit/328c624cdf)] - **doc**: fix util.inspect change history (Antoine du Hamel) [#35528](https://github.com/nodejs/node/pull/35528)
- [[`d53cfcd9e7`](https://github.com/nodejs/node/commit/d53cfcd9e7)] - **doc**: improve kbd element rendering (Rich Trott) [#35497](https://github.com/nodejs/node/pull/35497)
- [[`8877430530`](https://github.com/nodejs/node/commit/8877430530)] - **doc**: add aduh95 to collaborators (Antoine du Hamel) [#35542](https://github.com/nodejs/node/pull/35542)
- [[`8cdc59b34c`](https://github.com/nodejs/node/commit/8cdc59b34c)] - **doc**: fix YAML syntax errors (Antoine du Hamel) [#35529](https://github.com/nodejs/node/pull/35529)
- [[`3c90b1a278`](https://github.com/nodejs/node/commit/3c90b1a278)] - **errors**: support possible deletion of globalThis.Error (Michaël Zasso) [#35499](https://github.com/nodejs/node/pull/35499)
- [[`a3c7f8e576`](https://github.com/nodejs/node/commit/a3c7f8e576)] - **fs**: rimraf should not recurse on failure (Benjamin Coe) [#35566](https://github.com/nodejs/node/pull/35566)
- [[`939f8e8bfa`](https://github.com/nodejs/node/commit/939f8e8bfa)] - **fs**: throw rm() validation errors (Colin Ihrig) [#35602](https://github.com/nodejs/node/pull/35602)
- [[`3a401b8695`](https://github.com/nodejs/node/commit/3a401b8695)] - **fs**: update rm/rmdir validation messages (Colin Ihrig) [#35565](https://github.com/nodejs/node/pull/35565)
- [[`937fa5d292`](https://github.com/nodejs/node/commit/937fa5d292)] - **fs**: use validateBoolean() in rm/rmdir validation (Colin Ihrig) [#35565](https://github.com/nodejs/node/pull/35565)
- [[`1ad9aca194`](https://github.com/nodejs/node/commit/1ad9aca194)] - **fs**: remove extraneous assignments in rmdir() (Colin Ihrig) [#35567](https://github.com/nodejs/node/pull/35567)
- [[`1fadcf2163`](https://github.com/nodejs/node/commit/1fadcf2163)] - **fs**: simplify validateRmOptions() error handling (Colin Ihrig) [#35567](https://github.com/nodejs/node/pull/35567)
- [[`8e3b11adb3`](https://github.com/nodejs/node/commit/8e3b11adb3)] - **fs**: use errno constant with ERR_FS_EISDIR (Colin Ihrig) [#35563](https://github.com/nodejs/node/pull/35563)
- [[`1610728d7c`](https://github.com/nodejs/node/commit/1610728d7c)] - **(SEMVER-MINOR)** **fs**: add rm method (Ian Sutherland) [#35494](https://github.com/nodejs/node/pull/35494)
- [[`6ff152cc67`](https://github.com/nodejs/node/commit/6ff152cc67)] - **(SEMVER-MINOR)** **http**: allow passing array of key/val into writeHead (Robert Nagy) [#35274](https://github.com/nodejs/node/pull/35274)
- [[`2f1319967c`](https://github.com/nodejs/node/commit/2f1319967c)] - **http**: make response.setTimeout() work (Ben Noordhuis) [#34913](https://github.com/nodejs/node/pull/34913)
- [[`59a2cb5ebb`](https://github.com/nodejs/node/commit/59a2cb5ebb)] - **inspector**: do not hardcode Debugger.CallFrameId in tests (Dmitry Gozman) [#35570](https://github.com/nodejs/node/pull/35570)
- [[`cd0b1365ae`](https://github.com/nodejs/node/commit/cd0b1365ae)] - **lib**: fix readFile flag option typo (Daniil Demidovich) [#35292](https://github.com/nodejs/node/pull/35292)
- [[`70927560f6`](https://github.com/nodejs/node/commit/70927560f6)] - **lib**: change http client path assignment (Yohanan Baruchel) [#35508](https://github.com/nodejs/node/pull/35508)
- [[`fa171dbb7f`](https://github.com/nodejs/node/commit/fa171dbb7f)] - **lib**: use remaining typed arrays from primordials (Michaël Zasso) [#35499](https://github.com/nodejs/node/pull/35499)
- [[`7e8fdd399f`](https://github.com/nodejs/node/commit/7e8fdd399f)] - **lib**: use Number.parseFloat from primordials (Michaël Zasso) [#35499](https://github.com/nodejs/node/pull/35499)
- [[`5d727f0308`](https://github.com/nodejs/node/commit/5d727f0308)] - **lib**: use Number.parseInt from primordials (Michaël Zasso) [#35499](https://github.com/nodejs/node/pull/35499)
- [[`77f1e1ea57`](https://github.com/nodejs/node/commit/77f1e1ea57)] - **lib**: use global Error constructors from primordials (Michaël Zasso) [#35499](https://github.com/nodejs/node/pull/35499)
- [[`30c6b3e809`](https://github.com/nodejs/node/commit/30c6b3e809)] - **lib**: replace String global with primordials (Sebastien Ahkrin) [#35397](https://github.com/nodejs/node/pull/35397)
- [[`ebf3900795`](https://github.com/nodejs/node/commit/ebf3900795)] - **lib**: replace Int8Array global with primordials (Sebastien Ahkrin) [#35397](https://github.com/nodejs/node/pull/35397)
- [[`d6ba4ecc4d`](https://github.com/nodejs/node/commit/d6ba4ecc4d)] - **lib**: replace Int32Array global with primordials (Sebastien Ahkrin) [#35397](https://github.com/nodejs/node/pull/35397)
- [[`f544f7a102`](https://github.com/nodejs/node/commit/f544f7a102)] - **lib**: replace Float64Array global with primordials (Sebastien Ahkrin) [#35397](https://github.com/nodejs/node/pull/35397)
- [[`b82fc409ca`](https://github.com/nodejs/node/commit/b82fc409ca)] - **module**: cjs-module-lexer@0.4.1 big endian fix (Guy Bedford) [#35634](https://github.com/nodejs/node/pull/35634)
- [[`cb2f6ffd3e`](https://github.com/nodejs/node/commit/cb2f6ffd3e)] - **module**: use Wasm CJS lexer when available (Guy Bedford) [#35583](https://github.com/nodejs/node/pull/35583)
- [[`c995242068`](https://github.com/nodejs/node/commit/c995242068)] - **n-api**: support for object freeze/seal (Shelley Vohr) [#35359](https://github.com/nodejs/node/pull/35359)
- [[`4d1d3f454d`](https://github.com/nodejs/node/commit/4d1d3f454d)] - **src**: reduced substring calls (Yash Ladha) [#34808](https://github.com/nodejs/node/pull/34808)
- [[`5946b1ee23`](https://github.com/nodejs/node/commit/5946b1ee23)] - **(SEMVER-MINOR)** **src**: move node_contextify to modern THROW_ERR\_\* (James M Snell) [#35470](https://github.com/nodejs/node/pull/35470)
- [[`541082ccd9`](https://github.com/nodejs/node/commit/541082ccd9)] - **(SEMVER-MINOR)** **src**: move node_process to modern THROW_ERR\* (James M Snell) [#35472](https://github.com/nodejs/node/pull/35472)
- [[`2e67d650bb`](https://github.com/nodejs/node/commit/2e67d650bb)] - **src**: fix freeing unintialized pointer bug in ParseSoaReply (Aastha Gupta) [#35502](https://github.com/nodejs/node/pull/35502)
- [[`93f947af0a`](https://github.com/nodejs/node/commit/93f947af0a)] - **(SEMVER-MINOR)** **src**: expose v8::Isolate setup callbacks (Shelley Vohr) [#35512](https://github.com/nodejs/node/pull/35512)
- [[`573410fb69`](https://github.com/nodejs/node/commit/573410fb69)] - **(SEMVER-MINOR)** **stream**: multiple stream backports (Robert Nagy) [#34887](https://github.com/nodejs/node/pull/34887)
- [[`775af7af4f`](https://github.com/nodejs/node/commit/775af7af4f)] - **test**: add regression test for v8.getHeapSnapshot() crash (Anna Henningsen) [#35612](https://github.com/nodejs/node/pull/35612)
- [[`3d21792f86`](https://github.com/nodejs/node/commit/3d21792f86)] - **test**: mark test-webcrypto-encrypt-decrypt-aes flaky (James M Snell) [#35587](https://github.com/nodejs/node/pull/35587)
- [[`4a2ba4384b`](https://github.com/nodejs/node/commit/4a2ba4384b)] - **test**: do not use the same EventEmitter instance (Luigi Pinca) [#35560](https://github.com/nodejs/node/pull/35560)
- [[`768529736a`](https://github.com/nodejs/node/commit/768529736a)] - **test**: add ALPNProtocols option to clientOptions (Luigi Pinca) [#35482](https://github.com/nodejs/node/pull/35482)
- [[`3a77d1e208`](https://github.com/nodejs/node/commit/3a77d1e208)] - **test**: adjust comments for upcoming lint rule (Rich Trott) [#35485](https://github.com/nodejs/node/pull/35485)
- [[`2992d0b82c`](https://github.com/nodejs/node/commit/2992d0b82c)] - **tools**: refloat 7 Node.js patches to cpplint.py (Rich Trott) [#35569](https://github.com/nodejs/node/pull/35569)
- [[`a19b320a31`](https://github.com/nodejs/node/commit/a19b320a31)] - **tools**: bump cpplint.py to 1.4.6 (Rich Trott) [#35569](https://github.com/nodejs/node/pull/35569)
- [[`bd344108eb`](https://github.com/nodejs/node/commit/bd344108eb)] - **_Revert_** "**tools**: add missing uv_setup_argv() calls" (Beth Griggs) [#35641](https://github.com/nodejs/node/pull/35641)
- [[`e8434d88fe`](https://github.com/nodejs/node/commit/e8434d88fe)] - **tools,test**: enable multiline-comment-style rule in tests (Rich Trott) [#35485](https://github.com/nodejs/node/pull/35485)

Windows 32-bit Installer: https://nodejs.org/dist/v14.14.0/node-v14.14.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v14.14.0/node-v14.14.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v14.14.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v14.14.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v14.14.0/node-v14.14.0.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v14.14.0/node-v14.14.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v14.14.0/node-v14.14.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v14.14.0/node-v14.14.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v14.14.0/node-v14.14.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v14.14.0/node-v14.14.0-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v14.14.0/node-v14.14.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v14.14.0/node-v14.14.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v14.14.0/node-v14.14.0.tar.gz \
Other release files: https://nodejs.org/dist/v14.14.0/ \
Documentation: https://nodejs.org/docs/v14.14.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

38e9aba76b3c7a75be19f0c44bec9fca6c8c9d3515574fbd209197a82abdb07e  node-v14.14.0-aix-ppc64.tar.gz
c492c905a240eafa5448d6ef8988371afbd76ffa38b7e3deab41bdeed4a580fe  node-v14.14.0-darwin-x64.tar.gz
2f8283d36b3ae018483f6d294c81df246f7201925722d7c7659b7e2769d07797  node-v14.14.0-darwin-x64.tar.xz
888065c4e8c3aa583328af178b9e51abefa55f675238e66923ae025dfd01f706  node-v14.14.0-headers.tar.gz
5f96479f0d30ff0ff7a687a89df199a112f0dba7a8bd06ec82ca4bc16a2e6130  node-v14.14.0-headers.tar.xz
de15496b7a311b5819470cc6df5397095b4e154a3479c6ed41075f0de96ec8a1  node-v14.14.0-linux-arm64.tar.gz
6aab9915ba00d096343c94bd4894c13801d689c5a214bb9109c235acf1a8cd7d  node-v14.14.0-linux-arm64.tar.xz
9ec25df024af8defbe527919f6ead118542a57a6fb39a02391ba4417c80edd80  node-v14.14.0-linux-armv7l.tar.gz
e2dafe2b66fc375f1f0030ded0dec4c90fd08c3d9c659b271a95d0e4ddf139dc  node-v14.14.0-linux-armv7l.tar.xz
96b46025c49be804939759e6f50752da7ce369116adc21766fa8665492aeeaed  node-v14.14.0-linux-ppc64le.tar.gz
157a7f7f79d7263f91a82c0ef4350a887f79ee9eb19ede3b8e1ba49f805f513d  node-v14.14.0-linux-ppc64le.tar.xz
a75905727ed7863d37cffde8cd4038032981cac7b266272515b1dd107de33a99  node-v14.14.0-linux-s390x.tar.gz
c6a4e41601d987ca4750ba1f3fc7cc95ed511f2270b6bc249a288797ff5123e5  node-v14.14.0-linux-s390x.tar.xz
438cc26853b17f4aad79fb441f6dbcc1128aff9ffcd0c132ae044259f96ff6a8  node-v14.14.0-linux-x64.tar.gz
8ba22704fee4ad33c7e514ec508272a9502efcee5e608b8218164394c69cf2cd  node-v14.14.0-linux-x64.tar.xz
9ee59e87a3907a00304376eb4672800db5f0204933a86fe06dd777fc818331a3  node-v14.14.0.pkg
afb0b401d62d9fcfc68258f50d0bf042998d349ce9c1d7a2d45dd87870b3aab7  node-v14.14.0.tar.gz
04e935f4bd6b1e91c4a491e18d4d7a797009c3760b950cdefb193c4c079df0e6  node-v14.14.0.tar.xz
af5baa624cb222acf4209d21cfa39c725818e9571ed13df37ba6bf27ba1dd6ca  node-v14.14.0-win-x64.7z
8fd96e1e14526939ed10186eaa51fe47f68612f097e05c1eb92f46c71120c551  node-v14.14.0-win-x64.zip
bb3e81bcd7224a162d0d3cce02541352ceb142f71f23ab4d8c34ff2738de9f69  node-v14.14.0-win-x86.7z
16f859aadb6c4a750b4487868d701f802109f30294d06e3411c71e8202e01897  node-v14.14.0-win-x86.zip
326acab228b970a9dd21dd9f5b2d61e0a9be8a01452ec6de5b3450d380e20b3d  node-v14.14.0-x64.msi
4bd6db3058f21f3e10f171a97321f535e78a29646418fb468f4aa0e32348651e  node-v14.14.0-x86.msi
e7f4736c907c0371b6e8ab9c059d8da5c39d0424f30b24c2e4e0a1536f003080  win-x64/node.exe
fed883832f78ea588427947182cf4a65e98813f850f804bedf866996bbfb5eb6  win-x64/node.lib
c2726286f7a28e24fa08e2840866e35d00feb7c1f8c42e81c5e09818351cb11c  win-x64/node_pdb.7z
f3c65c789a1098ed3709bc06ea1f11f4b963a60b89aa3fd622daeb8308b6eecd  win-x64/node_pdb.zip
db27faa64543f424c3af8497c35fb31bc9c346c870414da6c73f3d896188d232  win-x86/node.exe
4059ab3d45f0e786a6e254492a69474801028f93e9f80ac577ae39935fd46b18  win-x86/node.lib
af82610d89126c44dd09b0a9a1704c91eb87dbb9da8bbda86355889f39190665  win-x86/node_pdb.7z
52d866435fd3321ab6b8fd8a5784fbd0756794491d108db111fb763a716bf510  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQEzBAEBCAAdFiEEDv/hvO/ZyE49CYFSkzsB9AtcqUYFAl+I5CoACgkQkzsB9Atc
qUZqDgf/S9V0OORWhIzi8rrkfqh0KMo6mR8ga7Jq8aK2QK8P3I+0g22cH+vQR2Sz
V15iRfVnszdDETsuSBdKD1Clegqrh9k7suUwapua0Y34/XUo47bbPyX15WcCcQJC
b7MmxxNh8AcrgeuoNhzLpz+2lQTonf42KPNj5P4PcqnnsWBDqyoXmH/REroMrlIZ
rixhOYrtN8bMUpDjzG6aal614+5ir3q/IixWvKP+CSc0yLSY2bFT6U+h5y3Rr0A9
a6qPNW1eJ/mUL8A87E4tSY1a0Z4naS9cv57BMFT5hM7vmTf1mtH6y43QQZUHBvJM
wz5Y1YNDjIui83xwBi6Q9E1rGwG0jg==
=yKNN
-----END PGP SIGNATURE-----

```
