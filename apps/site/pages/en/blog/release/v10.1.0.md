---
date: '2018-05-09T02:44:05.024Z'
category: release
title: Node v10.1.0 (Current)
layout: blog-post
author: <PERSON><PERSON>
---

### Notable Changes

- **console**:
  - make console.table() use colored inspect (TSUYUSATO Kitsune) [#20510](https://github.com/nodejs/node/pull/20510)
- **fs**:
  - move fs/promises to fs.promises (cjihrig) [#20504](https://github.com/nodejs/node/pull/20504)
- **http**:
  - added aborted property to request (<PERSON>) [#20094](https://github.com/nodejs/node/pull/20094)
- **n-api**:
  - initialize a module via a special symbol (<PERSON>) [#20161](https://github.com/nodejs/node/pull/20161)
- **src**:
  - add public API to expose the main V8 Platform (Allen Yonghuang Wang) [#20447](https://github.com/nodejs/node/pull/20447)

### Commits

- [[`7293c58d51`](https://github.com/nodejs/node/commit/7293c58d51)] - **assert**: make skipping indicator blue (<PERSON><PERSON> Bridgewater) [#20315](https://github.com/nodejs/node/pull/20315)
- [[`f5054d3412`](https://github.com/nodejs/node/commit/f5054d3412)] - **assert**: minor error message improvements (Ruben Bridgewater) [#20315](https://github.com/nodejs/node/pull/20315)
- [[`16970ffda4`](https://github.com/nodejs/node/commit/16970ffda4)] - **benchmark**: track exec time in next-tick-exec (Anatoli Papirovski) [#20462](https://github.com/nodejs/node/pull/20462)
- [[`289e4cef3f`](https://github.com/nodejs/node/commit/289e4cef3f)] - **benchmark**: fix next-tick-depth (Anatoli Papirovski) [#20461](https://github.com/nodejs/node/pull/20461)
- [[`b0e6f10530`](https://github.com/nodejs/node/commit/b0e6f10530)] - **benchmark**: add bench for zlib gzip + gunzip cycle (Anna Henningsen) [#20034](https://github.com/nodejs/node/pull/20034)
- [[`167de1f038`](https://github.com/nodejs/node/commit/167de1f038)] - **build**: check for different deprecation signatures (Ruben Bridgewater) [#20384](https://github.com/nodejs/node/pull/20384)
- [[`348d391a71`](https://github.com/nodejs/node/commit/348d391a71)] - **build**: remove --xcode configure switch (Ben Noordhuis) [#20328](https://github.com/nodejs/node/pull/20328)
- [[`2ce4b7cb8c`](https://github.com/nodejs/node/commit/2ce4b7cb8c)] - **build**: do not depend on `cp` in `PATH` (Anna Henningsen) [#20296](https://github.com/nodejs/node/pull/20296)
- [[`c5b3459003`](https://github.com/nodejs/node/commit/c5b3459003)] - **build**: use -9 with `kill` in Makefile (Rich Trott) [#20195](https://github.com/nodejs/node/pull/20195)
- [[`b5931e1d45`](https://github.com/nodejs/node/commit/b5931e1d45)] - **child_process**: name anonymous functions (Denis Fäcke) [#20399](https://github.com/nodejs/node/pull/20399)
- [[`ec2037da12`](https://github.com/nodejs/node/commit/ec2037da12)] - **child_process**: fix leak when passing http sockets (Santiago Gimeno) [#20305](https://github.com/nodejs/node/pull/20305)
- [[`a7758c76c0`](https://github.com/nodejs/node/commit/a7758c76c0)] - **(SEMVER-MINOR)** **console**: make console.table() use colored inspect (TSUYUSATO Kitsune) [#20510](https://github.com/nodejs/node/pull/20510)
- [[`29bc735d42`](https://github.com/nodejs/node/commit/29bc735d42)] - **console**: fix console.table() display edge case (Rich Trott) [#20323](https://github.com/nodejs/node/pull/20323)
- [[`dfcf20f5fd`](https://github.com/nodejs/node/commit/dfcf20f5fd)] - **crypto**: use new OpenSSL constants in CipherBase (Tobias Nießen) [#20339](https://github.com/nodejs/node/pull/20339)
- [[`e17280e8c9`](https://github.com/nodejs/node/commit/e17280e8c9)] - **crypto**: make pbkdf2 use checkIsArrayBufferView (Daniel Bevenius) [#20251](https://github.com/nodejs/node/pull/20251)
- [[`61e93963ce`](https://github.com/nodejs/node/commit/61e93963ce)] - **crypto**: add checkIsArrayBufferView (Daniel Bevenius) [#20251](https://github.com/nodejs/node/pull/20251)
- [[`e81bb9f8a3`](https://github.com/nodejs/node/commit/e81bb9f8a3)] - **crypto**: add getIntOption function to reduce dupl (Daniel Bevenius) [#20247](https://github.com/nodejs/node/pull/20247)
- [[`391d2f830a`](https://github.com/nodejs/node/commit/391d2f830a)] - **crypto**: simplify diffiehellman getFormat function (Daniel Bevenius) [#20246](https://github.com/nodejs/node/pull/20246)
- [[`3cf53b66c2`](https://github.com/nodejs/node/commit/3cf53b66c2)] - **deps**: patch V8 to 6.6.346.27 (Myles Borins) [#20480](https://github.com/nodejs/node/pull/20480)
- [[`da8bc6ab50`](https://github.com/nodejs/node/commit/da8bc6ab50)] - **deps**: cherry-pick 76cab5f from upstream V8 (Michaël Zasso) [#20350](https://github.com/nodejs/node/pull/20350)
- [[`05ce635e9a`](https://github.com/nodejs/node/commit/05ce635e9a)] - **doc**: match console.count()/countReset() signatures (Lambdac0re) [#20599](https://github.com/nodejs/node/pull/20599)
- [[`e995ae5992`](https://github.com/nodejs/node/commit/e995ae5992)] - **doc**: clarify `this` in event listeners (daGo) [#20537](https://github.com/nodejs/node/pull/20537)
- [[`bd27a59865`](https://github.com/nodejs/node/commit/bd27a59865)] - **doc**: move tunniclm to Emeritus (Rich Trott) [#20533](https://github.com/nodejs/node/pull/20533)
- [[`ec65fe48d8`](https://github.com/nodejs/node/commit/ec65fe48d8)] - **doc**: add trace category for fs sync methods (Chin Huang) [#20526](https://github.com/nodejs/node/pull/20526)
- [[`8148fca730`](https://github.com/nodejs/node/commit/8148fca730)] - **doc**: update "Who to cc..." in COLLABORATOR_GUIDE (Vse Mozhet Byt) [#20564](https://github.com/nodejs/node/pull/20564)
- [[`70586c0334`](https://github.com/nodejs/node/commit/70586c0334)] - **doc**: excise "periodically" before "emit events" (Jesse W. Collins) [#20581](https://github.com/nodejs/node/pull/20581)
- [[`01560b69a7`](https://github.com/nodejs/node/commit/01560b69a7)] - **doc**: edit text about revoking deprecations (Rich Trott) [#20519](https://github.com/nodejs/node/pull/20519)
- [[`eed3f10615`](https://github.com/nodejs/node/commit/eed3f10615)] - **doc**: edit text for DEP0013 (Rich Trott) [#20519](https://github.com/nodejs/node/pull/20519)
- [[`b2b4871c15`](https://github.com/nodejs/node/commit/b2b4871c15)] - **doc**: minor edit to DEP0065 (Rich Trott) [#20519](https://github.com/nodejs/node/pull/20519)
- [[`73f5ea9bd9`](https://github.com/nodejs/node/commit/73f5ea9bd9)] - **doc**: fix minor typographical error in DEP0079 text (Rich Trott) [#20519](https://github.com/nodejs/node/pull/20519)
- [[`faa81937a5`](https://github.com/nodejs/node/commit/faa81937a5)] - **doc**: edit text for DEP0082 (Rich Trott) [#20519](https://github.com/nodejs/node/pull/20519)
- [[`f796b0856c`](https://github.com/nodejs/node/commit/f796b0856c)] - **doc**: fix text for DEP0085 (Rich Trott) [#20519](https://github.com/nodejs/node/pull/20519)
- [[`74c74db35e`](https://github.com/nodejs/node/commit/74c74db35e)] - **doc**: edit text for DEP0094 (Rich Trott) [#20519](https://github.com/nodejs/node/pull/20519)
- [[`c6e4ffa429`](https://github.com/nodejs/node/commit/c6e4ffa429)] - **doc**: edit text for DEP0012 (Rich Trott) [#20519](https://github.com/nodejs/node/pull/20519)
- [[`adf5b80b20`](https://github.com/nodejs/node/commit/adf5b80b20)] - **doc**: edit text for DEP0101 (Rich Trott) [#20519](https://github.com/nodejs/node/pull/20519)
- [[`67e44bf588`](https://github.com/nodejs/node/commit/67e44bf588)] - **doc**: edit text for DEP0104 (Rich Trott) [#20519](https://github.com/nodejs/node/pull/20519)
- [[`0e8805186f`](https://github.com/nodejs/node/commit/0e8805186f)] - **doc**: add parameters for Http2Session:stream event (Ujjwal Sharma) [#20547](https://github.com/nodejs/node/pull/20547)
- [[`98ccaead0d`](https://github.com/nodejs/node/commit/98ccaead0d)] - **doc**: clearer doc-only deprecations (Ruben Bridgewater) [#20381](https://github.com/nodejs/node/pull/20381)
- [[`bea4ffcc97`](https://github.com/nodejs/node/commit/bea4ffcc97)] - **doc**: update one more command in crypto.md (Shobhit Chittora) [#20500](https://github.com/nodejs/node/pull/20500)
- [[`018b5ad800`](https://github.com/nodejs/node/commit/018b5ad800)] - **doc**: add snake_case section for C-like structs (Daniel Bevenius) [#20423](https://github.com/nodejs/node/pull/20423)
- [[`8bd45d8212`](https://github.com/nodejs/node/commit/8bd45d8212)] - **doc**: updates crypto doc with openssl list -cipher-algorithms (Shobhit Chittora) [#20502](https://github.com/nodejs/node/pull/20502)
- [[`880772a7ff`](https://github.com/nodejs/node/commit/880772a7ff)] - **doc**: fix N-API property descriptor documentation (Gabriel Schulhof) [#20433](https://github.com/nodejs/node/pull/20433)
- [[`79e1260cd8`](https://github.com/nodejs/node/commit/79e1260cd8)] - **doc**: fix manpage warning (Jérémy Lal) [#20383](https://github.com/nodejs/node/pull/20383)
- [[`745e0a5583`](https://github.com/nodejs/node/commit/745e0a5583)] - **doc**: document using `domain` in REPL (Ayush Gupta) [#20382](https://github.com/nodejs/node/pull/20382)
- [[`b2e8b9c473`](https://github.com/nodejs/node/commit/b2e8b9c473)] - **doc**: fix mkdtemp() documentation (Rich Trott) [#20512](https://github.com/nodejs/node/pull/20512)
- [[`d7db306d3b`](https://github.com/nodejs/node/commit/d7db306d3b)] - **doc**: update examples for fs.access() (BeniCheni) [#20460](https://github.com/nodejs/node/pull/20460)
- [[`b2d6eb74d4`](https://github.com/nodejs/node/commit/b2d6eb74d4)] - **doc**: cleanup n-api.md doc (Michael Dawson) [#20430](https://github.com/nodejs/node/pull/20430)
- [[`e5537477d4`](https://github.com/nodejs/node/commit/e5537477d4)] - **doc**: update Collaborator Guide reference (Rich Trott) [#20473](https://github.com/nodejs/node/pull/20473)
- [[`391c420c3e`](https://github.com/nodejs/node/commit/391c420c3e)] - **doc**: synchronize argument names for appendFile() (Rich Trott) [#20489](https://github.com/nodejs/node/pull/20489)
- [[`8b17e7ae04`](https://github.com/nodejs/node/commit/8b17e7ae04)] - **doc**: update cli flag in crypto.md (Shobhit Chittora) [#20400](https://github.com/nodejs/node/pull/20400)
- [[`74685f1b8f`](https://github.com/nodejs/node/commit/74685f1b8f)] - **doc**: add missing periods in documentation.md (Vse Mozhet Byt) [#20469](https://github.com/nodejs/node/pull/20469)
- [[`b0ed31cf9c`](https://github.com/nodejs/node/commit/b0ed31cf9c)] - **doc**: update writing-and-running-benchmarks.md (xsbchen) [#20379](https://github.com/nodejs/node/pull/20379)
- [[`658fbdc105`](https://github.com/nodejs/node/commit/658fbdc105)] - **doc**: add http.ClientRequest maxHeadersCount (Daiki Arai) [#20361](https://github.com/nodejs/node/pull/20361)
- [[`7a769ebba8`](https://github.com/nodejs/node/commit/7a769ebba8)] - **doc**: add squash guideline to pull-requests doc (Rich Trott) [#20413](https://github.com/nodejs/node/pull/20413)
- [[`166df9e15c`](https://github.com/nodejs/node/commit/166df9e15c)] - **doc**: remove squash guideline from onboarding doc (Rich Trott) [#20413](https://github.com/nodejs/node/pull/20413)
- [[`56c27c6a2b`](https://github.com/nodejs/node/commit/56c27c6a2b)] - **doc**: add more missing backticks (Vse Mozhet Byt) [#20438](https://github.com/nodejs/node/pull/20438)
- [[`abf11550b2`](https://github.com/nodejs/node/commit/abf11550b2)] - **doc**: add missing periods or colons (Vse Mozhet Byt) [#20401](https://github.com/nodejs/node/pull/20401)
- [[`261776d6b4`](https://github.com/nodejs/node/commit/261776d6b4)] - **doc**: mitigate `marked` bug (Vse Mozhet Byt) [#20411](https://github.com/nodejs/node/pull/20411)
- [[`54e93315ed`](https://github.com/nodejs/node/commit/54e93315ed)] - **doc**: specify types of listener parameter (Vse Mozhet Byt) [#20405](https://github.com/nodejs/node/pull/20405)
- [[`fcc5492df2`](https://github.com/nodejs/node/commit/fcc5492df2)] - **doc**: clarify FileHandle text (Rich Trott) [#20450](https://github.com/nodejs/node/pull/20450)
- [[`5a839b9911`](https://github.com/nodejs/node/commit/5a839b9911)] - **doc**: remove unclear text from fs.write() (Rich Trott) [#20450](https://github.com/nodejs/node/pull/20450)
- [[`459c20c0b8`](https://github.com/nodejs/node/commit/459c20c0b8)] - **doc**: edit fs.createReadStream() highWaterMark (Rich Trott) [#20450](https://github.com/nodejs/node/pull/20450)
- [[`f36a5e3ba5`](https://github.com/nodejs/node/commit/f36a5e3ba5)] - **doc**: remove redundant table of contents for N-API (Ayush Gupta) [#20395](https://github.com/nodejs/node/pull/20395)
- [[`4bc87c185b`](https://github.com/nodejs/node/commit/4bc87c185b)] - **doc**: add parameters for settings events (Ujjwal Sharma) [#20371](https://github.com/nodejs/node/pull/20371)
- [[`d7557e111e`](https://github.com/nodejs/node/commit/d7557e111e)] - **doc**: refine napi_get_property_names() doc (Gabriel Schulhof) [#20427](https://github.com/nodejs/node/pull/20427)
- [[`b61ae7fe09`](https://github.com/nodejs/node/commit/b61ae7fe09)] - **doc**: remove "has been known" tentativeness (Rich Trott) [#20412](https://github.com/nodejs/node/pull/20412)
- [[`de9d1f15de`](https://github.com/nodejs/node/commit/de9d1f15de)] - **doc**: remove parenthetical in onboarding-extras (Rich Trott) [#20393](https://github.com/nodejs/node/pull/20393)
- [[`5542a98aa4`](https://github.com/nodejs/node/commit/5542a98aa4)] - **doc**: improve process event headers (Ruben Bridgewater) [#20312](https://github.com/nodejs/node/pull/20312)
- [[`90026c3f3e`](https://github.com/nodejs/node/commit/90026c3f3e)] - **doc**: improve assert docs (Ruben Bridgewater) [#20313](https://github.com/nodejs/node/pull/20313)
- [[`57e5a3e15f`](https://github.com/nodejs/node/commit/57e5a3e15f)] - **doc**: remove redundant empty lines (Vse Mozhet Byt) [#20398](https://github.com/nodejs/node/pull/20398)
- [[`9cf3ae5bc3`](https://github.com/nodejs/node/commit/9cf3ae5bc3)] - **doc**: add missing backticks in n-api.md (Vse Mozhet Byt) [#20390](https://github.com/nodejs/node/pull/20390)
- [[`be34388a07`](https://github.com/nodejs/node/commit/be34388a07)] - **doc**: unify and dedupe returned values in timers.md (Vse Mozhet Byt) [#20310](https://github.com/nodejs/node/pull/20310)
- [[`9c11a18f70`](https://github.com/nodejs/node/commit/9c11a18f70)] - **doc**: remove eu-strip from tarball (jvelezpo) [#20304](https://github.com/nodejs/node/pull/20304)
- [[`b47044ac0f`](https://github.com/nodejs/node/commit/b47044ac0f)] - **doc**: improve parameters for Http2Session:goaway event (Ujjwal Sharma)
- [[`701f536ef4`](https://github.com/nodejs/node/commit/701f536ef4)] - **doc**: remove superfluous URL require statement (Mark Tiedemann) [#20364](https://github.com/nodejs/node/pull/20364)
- [[`d9bc9217a7`](https://github.com/nodejs/node/commit/d9bc9217a7)] - **doc**: fix typo in console.md (Daniel Hritzkiv) [#20349](https://github.com/nodejs/node/pull/20349)
- [[`cc09d7ec5b`](https://github.com/nodejs/node/commit/cc09d7ec5b)] - **doc**: remove console.table() as inspector-dependent (Rich Trott) [#20346](https://github.com/nodejs/node/pull/20346)
- [[`14188b1266`](https://github.com/nodejs/node/commit/14188b1266)] - **doc**: add Slack community to support options (Tracy) [#18191](https://github.com/nodejs/node/pull/18191)
- [[`3a3144cf04`](https://github.com/nodejs/node/commit/3a3144cf04)] - **doc**: remove os.uptime() Windows note (cjihrig) [#20308](https://github.com/nodejs/node/pull/20308)
- [[`c139d2ab8d`](https://github.com/nodejs/node/commit/c139d2ab8d)] - **doc**: fix unhandled to uncaught (Ruben Bridgewater) [#20293](https://github.com/nodejs/node/pull/20293)
- [[`7f6172b64b`](https://github.com/nodejs/node/commit/7f6172b64b)] - **doc**: improve docs for Http2Session:frameError (Ujjwal Sharma) [#20236](https://github.com/nodejs/node/pull/20236)
- [[`c9b202f817`](https://github.com/nodejs/node/commit/c9b202f817)] - **doc**: add emitter.off() to events.md (Ajido) [#20291](https://github.com/nodejs/node/pull/20291)
- [[`3bf736e569`](https://github.com/nodejs/node/commit/3bf736e569)] - **doc**: update pull request template in guide (Zachary Vacura) [#20277](https://github.com/nodejs/node/pull/20277)
- [[`171cbb1c64`](https://github.com/nodejs/node/commit/171cbb1c64)] - **doc**: fix net.Socket link inconsistencies (Hackzzila) [#20271](https://github.com/nodejs/node/pull/20271)
- [[`26525ef5ab`](https://github.com/nodejs/node/commit/26525ef5ab)] - **doc**: fix typos in doc/changelogs/CHANGELOG_V10.md (Vse Mozhet Byt) [#20265](https://github.com/nodejs/node/pull/20265)
- [[`3bc5353110`](https://github.com/nodejs/node/commit/3bc5353110)] - **doc**: fix spelling of API name in 10.0.0 changelog (Tobias Nießen) [#20257](https://github.com/nodejs/node/pull/20257)
- [[`e25f2c9e91`](https://github.com/nodejs/node/commit/e25f2c9e91)] - **errors**: remove dead code (Ruben Bridgewater) [#20483](https://github.com/nodejs/node/pull/20483)
- [[`b89d8178b4`](https://github.com/nodejs/node/commit/b89d8178b4)] - **errors**: minor (SystemError) refactoring (Ruben Bridgewater) [#20337](https://github.com/nodejs/node/pull/20337)
- [[`58a65d6689`](https://github.com/nodejs/node/commit/58a65d6689)] - **events**: optimize condition for optimal scenario (Anatoli Papirovski) [#20452](https://github.com/nodejs/node/pull/20452)
- [[`eb483dbac5`](https://github.com/nodejs/node/commit/eb483dbac5)] - **fs**: fchmod-\>fchown in promises/lchown (Сковорода Никита Андреевич) [#20407](https://github.com/nodejs/node/pull/20407)
- [[`eb724f00a3`](https://github.com/nodejs/node/commit/eb724f00a3)] - **fs**: remove broken code in promises/write (Сковорода Никита Андреевич) [#20407](https://github.com/nodejs/node/pull/20407)
- [[`0b5dd102e0`](https://github.com/nodejs/node/commit/0b5dd102e0)] - **fs**: move fs/promises to fs.promises (cjihrig) [#20504](https://github.com/nodejs/node/pull/20504)
- [[`e45e5b809d`](https://github.com/nodejs/node/commit/e45e5b809d)] - **fs**: point isFd to isUint32 (Daniel Bevenius) [#20330](https://github.com/nodejs/node/pull/20330)
- [[`f0b2b2605a`](https://github.com/nodejs/node/commit/f0b2b2605a)] - **http**: refactor outgoing headers processing (Anatoli Papirovski) [#20250](https://github.com/nodejs/node/pull/20250)
- [[`1385ffcccf`](https://github.com/nodejs/node/commit/1385ffcccf)] - **(SEMVER-MINOR)** **http**: added aborted property to request (Robert Nagy) [#20094](https://github.com/nodejs/node/pull/20094)
- [[`6acefc36ee`](https://github.com/nodejs/node/commit/6acefc36ee)] - **http2**: rename http2_state class to Http2State (Daniel Bevenius) [#20423](https://github.com/nodejs/node/pull/20423)
- [[`42bbaa338d`](https://github.com/nodejs/node/commit/42bbaa338d)] - **http2**: reduce require calls in http2/core (Daniel Bevenius) [#20422](https://github.com/nodejs/node/pull/20422)
- [[`e397d19e58`](https://github.com/nodejs/node/commit/e397d19e58)] - **http2**: remove unnecessary v8 qualified names (Daniel Bevenius) [#20420](https://github.com/nodejs/node/pull/20420)
- [[`b2bbc3619e`](https://github.com/nodejs/node/commit/b2bbc3619e)] - **http2**: remove unused using declarations node_http2 (Daniel Bevenius) [#20420](https://github.com/nodejs/node/pull/20420)
- [[`7d9f1f3971`](https://github.com/nodejs/node/commit/7d9f1f3971)] - **http2**: fix ping callback (Ruben Bridgewater) [#20311](https://github.com/nodejs/node/pull/20311)
- [[`46bd86235d`](https://github.com/nodejs/node/commit/46bd86235d)] - **http2**: fix responses to long payload reqs (Anatoli Papirovski) [#20084](https://github.com/nodejs/node/pull/20084)
- [[`0b16c2482d`](https://github.com/nodejs/node/commit/0b16c2482d)] - **https**: defines maxHeadersCount in the constructor (Daiki Arai) [#20359](https://github.com/nodejs/node/pull/20359)
- [[`1490164230`](https://github.com/nodejs/node/commit/1490164230)] - **inspector**: allow concurrent inspector sessions (Eugene Ostroukhov) [#20137](https://github.com/nodejs/node/pull/20137)
- [[`375994f940`](https://github.com/nodejs/node/commit/375994f940)] - **inspector**: Use default uv_listen backlog size (Eugene Ostroukhov) [#20254](https://github.com/nodejs/node/pull/20254)
- [[`7b8e9ca7b8`](https://github.com/nodejs/node/commit/7b8e9ca7b8)] - **lib**: expose FixedQueue internally and fix nextTick bug (Anatoli Papirovski) [#20468](https://github.com/nodejs/node/pull/20468)
- [[`b6de6a7e35`](https://github.com/nodejs/node/commit/b6de6a7e35)] - **lib**: named anonymous functions (Carrie Coxwell) [#20408](https://github.com/nodejs/node/pull/20408)
- [[`9eacd66bcb`](https://github.com/nodejs/node/commit/9eacd66bcb)] - **lib**: make sure `console` is writable (Kyle Farnung) [#20185](https://github.com/nodejs/node/pull/20185)
- [[`17dbf6c77f`](https://github.com/nodejs/node/commit/17dbf6c77f)] - **n-api**: make test_error functions static (Gabriel Schulhof)
- [[`ad793ab93c`](https://github.com/nodejs/node/commit/ad793ab93c)] - **n-api**: test and doc napi_throw() of a primitive (Gabriel Schulhof) [#20428](https://github.com/nodejs/node/pull/20428)
- [[`1908668826`](https://github.com/nodejs/node/commit/1908668826)] - **n-api**: document the look of napi_external values (Gabriel Schulhof) [#20426](https://github.com/nodejs/node/pull/20426)
- [[`7ac491b8ac`](https://github.com/nodejs/node/commit/7ac491b8ac)] - **n-api**: document that native strings are copied (Gabriel Schulhof) [#20425](https://github.com/nodejs/node/pull/20425)
- [[`705d9ecd13`](https://github.com/nodejs/node/commit/705d9ecd13)] - **n-api**: remove unused Test function (Daniel Bevenius) [#20320](https://github.com/nodejs/node/pull/20320)
- [[`8d24b6ed34`](https://github.com/nodejs/node/commit/8d24b6ed34)] - **n-api**: update cli documentation (Gabriel Schulhof) [#20301](https://github.com/nodejs/node/pull/20301)
- [[`cd83df386b`](https://github.com/nodejs/node/commit/cd83df386b)] - **(SEMVER-MINOR)** **n-api**: initialize a module via a special symbol (Gabriel Schulhof) [#20161](https://github.com/nodejs/node/pull/20161)
- [[`b5c1c146f5`](https://github.com/nodejs/node/commit/b5c1c146f5)] - **n-api,test**: remove superfluous persistent (Gabriel Schulhof) [#20299](https://github.com/nodejs/node/pull/20299)
- [[`2de3343474`](https://github.com/nodejs/node/commit/2de3343474)] - **n-api,test**: make method static (Gabriel Schulhof) [#20292](https://github.com/nodejs/node/pull/20292)
- [[`b239591ed8`](https://github.com/nodejs/node/commit/b239591ed8)] - **n-api,test**: make methods static (Gabriel Schulhof) [#20243](https://github.com/nodejs/node/pull/20243)
- [[`d3a219c6ec`](https://github.com/nodejs/node/commit/d3a219c6ec)] - **repl**: add spaces to load/save messages (cjihrig) [#20536](https://github.com/nodejs/node/pull/20536)
- [[`d357875ea1`](https://github.com/nodejs/node/commit/d357875ea1)] - **(SEMVER-MINOR)** **src**: add public API to expose the main V8 Platform (Allen Yonghuang Wang) [#20447](https://github.com/nodejs/node/pull/20447)
- [[`df2cddc9c7`](https://github.com/nodejs/node/commit/df2cddc9c7)] - **src**: removed unnecessary prototypes from Environment::SetProtoMethod (Brandon Ruggles) [#20321](https://github.com/nodejs/node/pull/20321)
- [[`54f30658a3`](https://github.com/nodejs/node/commit/54f30658a3)] - **src**: fix inconsistency in extern declaration (Yang Guo) [#20436](https://github.com/nodejs/node/pull/20436)
- [[`f5d42532a3`](https://github.com/nodejs/node/commit/f5d42532a3)] - **src**: refactor `BaseObject` internal field management (Anna Henningsen) [#20455](https://github.com/nodejs/node/pull/20455)
- [[`c21a52f415`](https://github.com/nodejs/node/commit/c21a52f415)] - **src**: access `ContextifyContext*` more directly in property cbs (Anna Henningsen) [#20455](https://github.com/nodejs/node/pull/20455)
- [[`c0f153528e`](https://github.com/nodejs/node/commit/c0f153528e)] - **src**: remove `kFlagNoShutdown` flag (Anna Henningsen) [#20388](https://github.com/nodejs/node/pull/20388)
- [[`58be6efd29`](https://github.com/nodejs/node/commit/58be6efd29)] - **src**: avoid `std::make_unique` (Anna Henningsen) [#20386](https://github.com/nodejs/node/pull/20386)
- [[`31812edb2d`](https://github.com/nodejs/node/commit/31812edb2d)] - **src**: remove unnecessary copy operations in tracing (Anna Henningsen) [#20356](https://github.com/nodejs/node/pull/20356)
- [[`e0d2bc5cce`](https://github.com/nodejs/node/commit/e0d2bc5cce)] - **src**: improve fatal exception (Ruben Bridgewater) [#20294](https://github.com/nodejs/node/pull/20294)
- [[`44fdd36b96`](https://github.com/nodejs/node/commit/44fdd36b96)] - **src**: remove SecureContext `_external` getter (Anna Henningsen) [#20237](https://github.com/nodejs/node/pull/20237)
- [[`81de533836`](https://github.com/nodejs/node/commit/81de533836)] - **src**: create per-isolate strings after platform setup (Ulan Degenbaev) [#20175](https://github.com/nodejs/node/pull/20175)
- [[`b5bc6bd94b`](https://github.com/nodejs/node/commit/b5bc6bd94b)] - **src**: fix Systemtap node_gc_stop probe (William Cohen) [#20152](https://github.com/nodejs/node/pull/20152)
- [[`6bf816fde2`](https://github.com/nodejs/node/commit/6bf816fde2)] - **src**: limit foreground tasks draining loop (Ulan Degenbaev) [#19987](https://github.com/nodejs/node/pull/19987)
- [[`bd2e521096`](https://github.com/nodejs/node/commit/bd2e521096)] - **src**: rename return var in VerifySpkac functions (Daniel Bevenius) [#20218](https://github.com/nodejs/node/pull/20218)
- [[`a4dae6c226`](https://github.com/nodejs/node/commit/a4dae6c226)] - **src**: prefer false instead of bool zero (Daniel Bevenius) [#20218](https://github.com/nodejs/node/pull/20218)
- [[`4c4be85655`](https://github.com/nodejs/node/commit/4c4be85655)] - **_Revert_** "**stream**: prevent 'end' to be emitted after 'error'" (Brian White) [#20449](https://github.com/nodejs/node/pull/20449)
- [[`05b7b8d506`](https://github.com/nodejs/node/commit/05b7b8d506)] - **stream**: fix error handling with async iteration (Julien Fontanet) [#20329](https://github.com/nodejs/node/pull/20329)
- [[`fd912a37a0`](https://github.com/nodejs/node/commit/fd912a37a0)] - **stream**: only check options once in Duplex ctor (Daniel Bevenius) [#20353](https://github.com/nodejs/node/pull/20353)
- [[`e19200a666`](https://github.com/nodejs/node/commit/e19200a666)] - **test**: fix flaky http2-flow-control test (Anatoli Papirovski) [#20556](https://github.com/nodejs/node/pull/20556)
- [[`b2d3db433d`](https://github.com/nodejs/node/commit/b2d3db433d)] - **test**: use common.canCreateSymLink() consistently (cjihrig) [#20540](https://github.com/nodejs/node/pull/20540)
- [[`578e0546e0`](https://github.com/nodejs/node/commit/578e0546e0)] - **test**: fix test-cli-node-options.js on mips (Ruben Bridgewater) [#20377](https://github.com/nodejs/node/pull/20377)
- [[`601f138063`](https://github.com/nodejs/node/commit/601f138063)] - **test**: fix buffer writes on mips (Ruben Bridgewater) [#20377](https://github.com/nodejs/node/pull/20377)
- [[`1de67c71fb`](https://github.com/nodejs/node/commit/1de67c71fb)] - **test**: fix common.canCreateSymLink() on non-Windows (Masashi Hirano) [#20511](https://github.com/nodejs/node/pull/20511)
- [[`70b2e169b4`](https://github.com/nodejs/node/commit/70b2e169b4)] - **test**: fix up N-API error test (Gabriel Schulhof) [#20487](https://github.com/nodejs/node/pull/20487)
- [[`6052ccc009`](https://github.com/nodejs/node/commit/6052ccc009)] - **test**: rename misnamed test (Rich Trott) [#20532](https://github.com/nodejs/node/pull/20532)
- [[`80bdff0086`](https://github.com/nodejs/node/commit/80bdff0086)] - **test**: add fs/promises filehandle stat test (Masashi Hirano) [#20492](https://github.com/nodejs/node/pull/20492)
- [[`4dce39a919`](https://github.com/nodejs/node/commit/4dce39a919)] - **test**: use fs.copyFileSync() (Richard Lau) [#20340](https://github.com/nodejs/node/pull/20340)
- [[`b24ee078f6`](https://github.com/nodejs/node/commit/b24ee078f6)] - **test**: remove unnecessary strictEqual() argument from remoteClose() (Daylor Yanes) [#20343](https://github.com/nodejs/node/pull/20343)
- [[`2b8b40f800`](https://github.com/nodejs/node/commit/2b8b40f800)] - **test**: fix a TODO and remove obsolete TODOs (Ruben Bridgewater) [#20319](https://github.com/nodejs/node/pull/20319)
- [[`645a97a44e`](https://github.com/nodejs/node/commit/645a97a44e)] - **test**: verify arguments length in common.expectsError (Ruben Bridgewater) [#20311](https://github.com/nodejs/node/pull/20311)
- [[`b646566ab4`](https://github.com/nodejs/node/commit/b646566ab4)] - **test**: removed assert.strictEqual message (kailash k yogeshwar) [#20223](https://github.com/nodejs/node/pull/20223)
- [[`61a56fe437`](https://github.com/nodejs/node/commit/61a56fe437)] - **test**: added coverage for fs/promises API (Mithun Sasidharan) [#20219](https://github.com/nodejs/node/pull/20219)
- [[`769b6c8fd2`](https://github.com/nodejs/node/commit/769b6c8fd2)] - **test**: fix flaky child-process-exec-kill-throws (Santiago Gimeno) [#20213](https://github.com/nodejs/node/pull/20213)
- [[`99e0b913c6`](https://github.com/nodejs/node/commit/99e0b913c6)] - **test**: add checkMethods function for Certificate (Daniel Bevenius) [#20224](https://github.com/nodejs/node/pull/20224)
- [[`d4b19cf43f`](https://github.com/nodejs/node/commit/d4b19cf43f)] - **test,n-api**: re-write test_error in C (Gabriel Schulhof) [#20244](https://github.com/nodejs/node/pull/20244)
- [[`e552158dd2`](https://github.com/nodejs/node/commit/e552158dd2)] - **timers**: named anonymous functions (Kyle Martin) [#20397](https://github.com/nodejs/node/pull/20397)
- [[`1109104206`](https://github.com/nodejs/node/commit/1109104206)] - **tls**: remove sharedCreds in Server constructor (Daniel Bevenius) [#20491](https://github.com/nodejs/node/pull/20491)
- [[`1ebec18624`](https://github.com/nodejs/node/commit/1ebec18624)] - **tls**: cleanup onhandshakestart callback (Anatoli Papirovski) [#20466](https://github.com/nodejs/node/pull/20466)
- [[`9b30bc4f81`](https://github.com/nodejs/node/commit/9b30bc4f81)] - **tls**: fix getEphemeralKeyInfo to support X25519 (Shigeki Ohtsu) [#20273](https://github.com/nodejs/node/pull/20273)
- [[`73cd2798df`](https://github.com/nodejs/node/commit/73cd2798df)] - **tls**: specify options.name in validateKeyCert (Daniel Bevenius) [#20284](https://github.com/nodejs/node/pull/20284)
- [[`f7267b4af0`](https://github.com/nodejs/node/commit/f7267b4af0)] - **tools**: add eslint check for skipIfEslintMissing (Richard Lau) [#20372](https://github.com/nodejs/node/pull/20372)
- [[`2a1efa26a7`](https://github.com/nodejs/node/commit/2a1efa26a7)] - **tools**: add v10 to alternative version docs menu (Vse Mozhet Byt) [#20586](https://github.com/nodejs/node/pull/20586)
- [[`a4d2089c76`](https://github.com/nodejs/node/commit/a4d2089c76)] - **tools**: remove redundant code in doc/html.js (Vse Mozhet Byt) [#20514](https://github.com/nodejs/node/pull/20514)
- [[`3912551252`](https://github.com/nodejs/node/commit/3912551252)] - **tools**: fix TypeError from `test.py --time` (Richard Lau) [#20368](https://github.com/nodejs/node/pull/20368)
- [[`b0c0352742`](https://github.com/nodejs/node/commit/b0c0352742)] - **tools**: dedupe property access in doc/type-parser (Vse Mozhet Byt) [#20387](https://github.com/nodejs/node/pull/20387)
- [[`ccf1b24af2`](https://github.com/nodejs/node/commit/ccf1b24af2)] - **tools**: remove redundant RegExp flag (Vse Mozhet Byt) [#20309](https://github.com/nodejs/node/pull/20309)
- [[`a12d13ad06`](https://github.com/nodejs/node/commit/a12d13ad06)] - **tools**: simplify HTML generation (Vse Mozhet Byt) [#20307](https://github.com/nodejs/node/pull/20307)
- [[`8ddbac2fd6`](https://github.com/nodejs/node/commit/8ddbac2fd6)] - **tools**: add log output to crashes (Ruben Bridgewater) [#20295](https://github.com/nodejs/node/pull/20295)
- [[`ab13f13a6c`](https://github.com/nodejs/node/commit/ab13f13a6c)] - **tools**: show stdout/stderr for timed out tests (Rich Trott) [#20260](https://github.com/nodejs/node/pull/20260)
- [[`b5584c448a`](https://github.com/nodejs/node/commit/b5584c448a)] - **tools**: modernize and optimize doc/addon-verify.js (Vse Mozhet Byt) [#20188](https://github.com/nodejs/node/pull/20188)
- [[`ff619d39e6`](https://github.com/nodejs/node/commit/ff619d39e6)] - **url**: fix WHATWG host formatting error (Yichao 'Peak' Ji) [#20493](https://github.com/nodejs/node/pull/20493)
- [[`1b9c40cc71`](https://github.com/nodejs/node/commit/1b9c40cc71)] - **util**: named anonymous functions (Carrie Coxwell) [#20408](https://github.com/nodejs/node/pull/20408)
- [[`e854c953fd`](https://github.com/nodejs/node/commit/e854c953fd)] - **util**: improve spliceOne perf (Anatoli Papirovski) [#20453](https://github.com/nodejs/node/pull/20453)
- [[`3962c734ae`](https://github.com/nodejs/node/commit/3962c734ae)] - **util**: fix isInsideNodeModules inside error (Anatoli Papirovski) [#20266](https://github.com/nodejs/node/pull/20266)

Windows 32-bit Installer: https://nodejs.org/dist/v10.1.0/node-v10.1.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v10.1.0/node-v10.1.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v10.1.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v10.1.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v10.1.0/node-v10.1.0.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v10.1.0/node-v10.1.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v10.1.0/node-v10.1.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v10.1.0/node-v10.1.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v10.1.0/node-v10.1.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v10.1.0/node-v10.1.0-aix-ppc64.tar.gz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v10.1.0/node-v10.1.0-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v10.1.0/node-v10.1.0-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v10.1.0/node-v10.1.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v10.1.0/node-v10.1.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v10.1.0/node-v10.1.0.tar.gz \
Other release files: https://nodejs.org/dist/v10.1.0/ \
Documentation: https://nodejs.org/docs/v10.1.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

f4f96c3180f9fd8113632e499d5129709d52d8206e250924ff4c2d7d07ba5715  node-v10.1.0-aix-ppc64.tar.gz
383ef526e27b92113f8dc0dad406b771f6ecaf9e3fddd4f5357590a3cf543d7b  node-v10.1.0-darwin-x64.tar.gz
2f39d90b8bd80c75c2a97ec4ddaf2f4f11477fae20c5c28b31b59470c9a29a5c  node-v10.1.0-darwin-x64.tar.xz
37ccc9b953c2c62f704f5dee2ce38272fc194e64c03a446c2e3512154dbdd212  node-v10.1.0-headers.tar.gz
c92e3561249a56cb264cead82a470ba461008808a06d8c7bdf4d9aa2b7c6e507  node-v10.1.0-headers.tar.xz
62c395fd3ffca1671dd0b7a84e7250485e484e7d62f9be13fd45879e9ef45290  node-v10.1.0-linux-arm64.tar.gz
01e252589cf1ce61d2aaa316895001eb24c90a48b04a0e4896de252d2840ffe9  node-v10.1.0-linux-arm64.tar.xz
9888d66a30f9e14acdfd165f7e4a5029128b59ebdb20c5371f0293bde8c85987  node-v10.1.0-linux-armv6l.tar.gz
fbfb4b2ef96e0914d5d865349f4b72ef722549ab74e1528a64c5740013fe2751  node-v10.1.0-linux-armv6l.tar.xz
ac70505fa49576dbae848d4a6a66b99bef62e20fcbc581edada9babc6801cd94  node-v10.1.0-linux-armv7l.tar.gz
bd8a8bc2baf16401683a462438c3430174d0cba6d42478ed17d25c6a0116c8e1  node-v10.1.0-linux-armv7l.tar.xz
33ac904f443dd73c1b14dd55fe6ea5286b75244f79ccde67f8b0eeeb0d8d6f5c  node-v10.1.0-linux-ppc64le.tar.gz
0bcc6f368a2f57365ccaf3b77e5d2eccb1d84efed1b073bc974541a84829edac  node-v10.1.0-linux-ppc64le.tar.xz
dee8c829b0fc7ab924e2ac13242cb007a170489a3afd5ef5432ca7d25a210afe  node-v10.1.0-linux-s390x.tar.gz
89b6e0e097d6daad64aacf8a5fc478c695b1c59474b007730c37db760c57dad2  node-v10.1.0-linux-s390x.tar.xz
a750eaa8dd2abf175216377da86ed24d1265597b86c542fcf9cdf7b8043e006e  node-v10.1.0-linux-x64.tar.gz
cb5860c0d3249330d882fb8c6619c082cf3cda177536d4ed979388dde0034f22  node-v10.1.0-linux-x64.tar.xz
e97486f5e73bf767877daea5cb6ea66be1b3ccbc193c4de779ca73e911fd5503  node-v10.1.0.pkg
676bd2b518aa58e97664f814679cba5218e04d54c1d1b79e0d5ceea069cc285d  node-v10.1.0-sunos-x64.tar.gz
95a82d717371ba4fbbcf960768157dd43dc7d348b31c81e243ed995df8c14715  node-v10.1.0-sunos-x64.tar.xz
e4b5a1d9fac4b20b5421afe34108867df359c69c4c4e2ad6e2bd75b113ddf0db  node-v10.1.0.tar.gz
a6671055b4085795f5427f42349eae14c5dfe7692c923dffb7635b9f62cc7902  node-v10.1.0.tar.xz
afe3b7cb256b402a158629dfb561fd63878f2e07f1a90e4a836084f7a2418235  node-v10.1.0-win-x64.7z
8448bbd50731e98a4ed8bb4f3f3964170ff3a26418e9bcb3caf770faa9fb06d7  node-v10.1.0-win-x64.zip
cda9dbcde8f154827a8e4cd101d54760b5270c83cc8d38cefada1343388074c3  node-v10.1.0-win-x86.7z
bfe8274bb963577d150239d4b03b4d2ddd033a6fbee621c3144dc866054dfc53  node-v10.1.0-win-x86.zip
5e8c41ed9f5424eb43ddbb83befedcfda301cbc26f3bae88a13811061848d8d1  node-v10.1.0-x64.msi
29781e8d8e24ff67aafa427950a7004a023b99907f0328dda633549ca3afd32a  node-v10.1.0-x86.msi
20dedc9188d68a9041beec78f7b7f0d8afadfda8817849d035cb3679259ca265  win-x64/node.exe
4623b1dc138883e66cd834e16c02b0b0470f6e4eab81d8a603f9b6d5b2ccf3a2  win-x64/node.lib
0f7405ab40b7d903957f0ed7fdfec1876e8592f973347022a4c31115e4a5d722  win-x64/node_pdb.7z
f66a3eff82ff619a3f0e8a0adce09de5b4287eb2fc2f0cbdd17ff8308ca18b60  win-x64/node_pdb.zip
1eb72db6c8be2bbfa6faed1dcde50d2f48bf7ffea1969508a126fabee30648dd  win-x86/node.exe
68ffdf52b7459390792cdfee5632d7d121d2234c9261d5ab113527427a5f5312  win-x86/node.lib
2850b3f869d364af6efe643e37e2890278d812ba62f055a985885d5b7d0f6777  win-x86/node_pdb.7z
8a6ad8b72965b03d3ac058803a9dd739e8addb55aa6b6352257e9a493454e393  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQEzBAEBCAAdFiEEDv/hvO/ZyE49CYFSkzsB9AtcqUYFAlryYDoACgkQkzsB9Atc
qUZdqQgAmS4R6DYmyO9eg0R+saiDrO1zztIbkgQIESSz218WL1CXkBRHtXusPszl
0KQJkb15JEdfGDQUpFqUEv7/SUcBCFgf0z6mDpcpS4oygKKYZz5xEBO/8lnmSG+z
7VQb5Pd9N+I5dVvhAV3ywOBx9phW98V7TGrKH0Gpjx8p+0t95+HT0FzZA5IJSROz
adIk/p1q62RM3NKCryTy/HRy7kD3NkKaPJSOG8y1b31AGEvu/x08VTi54RJSVcBq
3HUxP5nvmp1svtNQF89cYKZZsP1an1y8jo1nJeKiq7jOVXpBC952X9+K/Nxxh1OO
0ZdS7LPeqxjsvN/gbkm0QUuGbkWxhQ==
=mUuU
-----END PGP SIGNATURE-----

```
