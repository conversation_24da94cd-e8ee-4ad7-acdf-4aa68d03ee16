---
date: '2018-05-15T21:30:07.173Z'
category: release
title: Node v8.11.2 (LTS)
layout: blog-post
author: <PERSON><PERSON>
---

### Notable Changes

- **deps**:
  - update node-inspect to 1.11.3 (<PERSON>) [#18354](https://github.com/nodejs/node/pull/18354)
  - update nghttp2 to 1.29.0 (<PERSON>) [#17908](https://github.com/nodejs/node/pull/17908)
- **http2**:
  - Sync with current release stream
- **n-api**:
  - Sync with current release stream

### Commits

- [[`ce3866bdcc`](https://github.com/nodejs/node/commit/ce3866bdcc)] - **async_hooks**: clean up comments (<PERSON>) [#18467](https://github.com/nodejs/node/pull/18467)
- [[`86e3c89ea4`](https://github.com/nodejs/node/commit/86e3c89ea4)] - **benchmark**: improve compare output (<PERSON><PERSON>) [#18597](https://github.com/nodejs/node/pull/18597)
- [[`18be476116`](https://github.com/nodejs/node/commit/18be476116)] - **benchmark**: fix punycode test for --without-intl (Timothy Gu) [#16251](https://github.com/nodejs/node/pull/16251)
- [[`88d3028e22`](https://github.com/nodejs/node/commit/88d3028e22)] - **build**: no longer have v8-debug.h as dependency. (Yang Guo) [#18677](https://github.com/nodejs/node/pull/18677)
- [[`7b6d93c145`](https://github.com/nodejs/node/commit/7b6d93c145)] - **build**: add doc linting when runnning `make lint` (Camilo Gonzalez) [#18472](https://github.com/nodejs/node/pull/18472)
- [[`9bce14172a`](https://github.com/nodejs/node/commit/9bce14172a)] - **build**: do not suppress output in make doc-only (Joyee Cheung) [#18507](https://github.com/nodejs/node/pull/18507)
- [[`333d7dda84`](https://github.com/nodejs/node/commit/333d7dda84)] - **build**: make lint-js independent of local node (Joyee Cheung) [#18272](https://github.com/nodejs/node/pull/18272)
- [[`d537f45aaa`](https://github.com/nodejs/node/commit/d537f45aaa)] - **build**: make lint-md independent of local node (Joyee Cheung) [#18272](https://github.com/nodejs/node/pull/18272)
- [[`658dd409fd`](https://github.com/nodejs/node/commit/658dd409fd)] - **build**: refine static and shared lib build (Yihong Wang) [#17604](https://github.com/nodejs/node/pull/17604)
- [[`659b2a1821`](https://github.com/nodejs/node/commit/659b2a1821)] - **build**: allow x86_64 as a dest_cpu alias for x64 (Rod Vagg) [#18052](https://github.com/nodejs/node/pull/18052)
- [[`424703a556`](https://github.com/nodejs/node/commit/424703a556)] - **build**: add cflags for OpenBSD, remove stray comma. (Aaron Bieber) [#18448](https://github.com/nodejs/node/pull/18448)
- [[`ab4809f195`](https://github.com/nodejs/node/commit/ab4809f195)] - **build,win**: restore vcbuild TAG functionality (Rod Vagg) [#18031](https://github.com/nodejs/node/pull/18031)
- [[`bf4d0743be`](https://github.com/nodejs/node/commit/bf4d0743be)] - **cluster**: fix inspector port assignment (Santiago Gimeno) [#18696](https://github.com/nodejs/node/pull/18696)
- [[`16bf5fed69`](https://github.com/nodejs/node/commit/16bf5fed69)] - **crypto**: reuse variable instead of reevaluation (Tobias Nießen) [#17735](https://github.com/nodejs/node/pull/17735)
- [[`9acc7f3fbb`](https://github.com/nodejs/node/commit/9acc7f3fbb)] - **deps**: update nghttp2 to 1.29.0 (James M Snell) [#17908](https://github.com/nodejs/node/pull/17908)
- [[`ab005592be`](https://github.com/nodejs/node/commit/ab005592be)] - **deps**: V8: backport 76c3ac5 from upstream (Ali Ijaz Sheikh) [#18298](https://github.com/nodejs/node/pull/18298)
- [[`f12db24947`](https://github.com/nodejs/node/commit/f12db24947)] - **deps**: cherry-pick a803fad from upstream V8 (Michaël Zasso) [#19824](https://github.com/nodejs/node/pull/19824)
- [[`09f5e252bf`](https://github.com/nodejs/node/commit/09f5e252bf)] - **deps**: cherry-pick 7abdadc from upstream V8 (Michaël Zasso) [#19824](https://github.com/nodejs/node/pull/19824)
- [[`c97237bc10`](https://github.com/nodejs/node/commit/c97237bc10)] - **deps**: cherry-pick a4bddba from upstream V8 (Michaël Zasso) [#19824](https://github.com/nodejs/node/pull/19824)
- [[`d02b72e799`](https://github.com/nodejs/node/commit/d02b72e799)] - **deps**: V8: backport 596d55a from upstream (Myles Borins) [#19477](https://github.com/nodejs/node/pull/19477)
- [[`79a7a17312`](https://github.com/nodejs/node/commit/79a7a17312)] - **deps**: update node-inspect to 1.11.3 (Jan Krems) [#18354](https://github.com/nodejs/node/pull/18354)
- [[`5394bc5c42`](https://github.com/nodejs/node/commit/5394bc5c42)] - **deps,src**: align ssize_t ABI between Node & nghttp2 (Anna Henningsen) [#18565](https://github.com/nodejs/node/pull/18565)
- [[`165d214a54`](https://github.com/nodejs/node/commit/165d214a54)] - **doc**: add Http2Session.connecting property (Pieter Mees) [#19842](https://github.com/nodejs/node/pull/19842)
- [[`1ff3544a4b`](https://github.com/nodejs/node/commit/1ff3544a4b)] - **doc**: guard against md list parsing edge case (Vse Mozhet Byt) [#19647](https://github.com/nodejs/node/pull/19647)
- [[`f59eab0165`](https://github.com/nodejs/node/commit/f59eab0165)] - **doc**: rename HTTP2 to HTTP/2 (Timothy Gu) [#19603](https://github.com/nodejs/node/pull/19603)
- [[`da185cec8f`](https://github.com/nodejs/node/commit/da185cec8f)] - **doc**: add note about browsers and HTTP/2 (Steven) [#19476](https://github.com/nodejs/node/pull/19476)
- [[`30070c7568`](https://github.com/nodejs/node/commit/30070c7568)] - **doc**: warn against concurrent http2stream.respondWithFD (Anna Henningsen) [#18762](https://github.com/nodejs/node/pull/18762)
- [[`39267e8bb0`](https://github.com/nodejs/node/commit/39267e8bb0)] - **doc**: fix typo in http2.md (Vse Mozhet Byt) [#18602](https://github.com/nodejs/node/pull/18602)
- [[`2da965c5b8`](https://github.com/nodejs/node/commit/2da965c5b8)] - **doc**: remove removed apis from http2 docs (Kelvin Jin) [#18439](https://github.com/nodejs/node/pull/18439)
- [[`9a4a8c127e`](https://github.com/nodejs/node/commit/9a4a8c127e)] - **doc**: unify type linkification (Vse Mozhet Byt) [#18407](https://github.com/nodejs/node/pull/18407)
- [[`15023c7d28`](https://github.com/nodejs/node/commit/15023c7d28)] - **doc**: fix documentation of http2Stream.pushstream() (Peter Dalgaard-Jensen) [#18258](https://github.com/nodejs/node/pull/18258)
- [[`fac76f9a6b`](https://github.com/nodejs/node/commit/fac76f9a6b)] - **doc**: fix typo in http2stream.close param default (Moritz Peters) [#18166](https://github.com/nodejs/node/pull/18166)
- [[`88babd5a23`](https://github.com/nodejs/node/commit/88babd5a23)] - **doc**: fix s/rstStream/close in example (James M Snell) [#18088](https://github.com/nodejs/node/pull/18088)
- [[`d9d0d0e98e`](https://github.com/nodejs/node/commit/d9d0d0e98e)] - **doc**: update pushStream docs to use err first (James M Snell) [#18088](https://github.com/nodejs/node/pull/18088)
- [[`940457394a`](https://github.com/nodejs/node/commit/940457394a)] - **doc**: compact eslint directives in common/README (Vse Mozhet Byt) [#17971](https://github.com/nodejs/node/pull/17971)
- [[`e9de5a976b`](https://github.com/nodejs/node/commit/e9de5a976b)] - **doc**: re-alphabetise sections in common/README.md (Vse Mozhet Byt) [#17971](https://github.com/nodejs/node/pull/17971)
- [[`c924adf33d`](https://github.com/nodejs/node/commit/c924adf33d)] - **doc**: fix code nits in common/README (Vse Mozhet Byt) [#17971](https://github.com/nodejs/node/pull/17971)
- [[`0205b3f0c1`](https://github.com/nodejs/node/commit/0205b3f0c1)] - **doc**: correct spelling (sreepurnajasti) [#17911](https://github.com/nodejs/node/pull/17911)
- [[`591f78bb0a`](https://github.com/nodejs/node/commit/591f78bb0a)] - **doc**: grammar fixes in http2.md (Rich Trott) [#17972](https://github.com/nodejs/node/pull/17972)
- [[`35ee8943da`](https://github.com/nodejs/node/commit/35ee8943da)] - **doc**: add docs for common/http2.js utility (James M Snell) [#17942](https://github.com/nodejs/node/pull/17942)
- [[`f0ba2c6ceb`](https://github.com/nodejs/node/commit/f0ba2c6ceb)] - **doc**: Add a missing comma (jiangq) [#19555](https://github.com/nodejs/node/pull/19555)
- [[`7c6fa183cb`](https://github.com/nodejs/node/commit/7c6fa183cb)] - **doc**: fix typos on n-api (Kyle Robinson Young) [#19385](https://github.com/nodejs/node/pull/19385)
- [[`1abb168838`](https://github.com/nodejs/node/commit/1abb168838)] - **doc**: fix n-api asynchronous threading docs (Eric Bickle) [#19073](https://github.com/nodejs/node/pull/19073)
- [[`87d0fd8212`](https://github.com/nodejs/node/commit/87d0fd8212)] - **doc**: mark NAPI_AUTO_LENGTH as code (Tobias Nießen) [#18697](https://github.com/nodejs/node/pull/18697)
- [[`58688d97dc`](https://github.com/nodejs/node/commit/58688d97dc)] - **doc**: fix exporting a function example (Aonghus O Nia) [#18661](https://github.com/nodejs/node/pull/18661)
- [[`4d43607474`](https://github.com/nodejs/node/commit/4d43607474)] - **doc**: fix typo in n-api.md (Vse Mozhet Byt) [#18590](https://github.com/nodejs/node/pull/18590)
- [[`9729278007`](https://github.com/nodejs/node/commit/9729278007)] - **doc**: small typo in n-api.md (iskore) [#18555](https://github.com/nodejs/node/pull/18555)
- [[`7ed1dfef28`](https://github.com/nodejs/node/commit/7ed1dfef28)] - **doc**: remove usage of you in n-api doc (Michael Dawson) [#18528](https://github.com/nodejs/node/pull/18528)
- [[`84e0a03727`](https://github.com/nodejs/node/commit/84e0a03727)] - **doc**: remove uannecessary Require (Michael Dawson) [#18184](https://github.com/nodejs/node/pull/18184)
- [[`51513fdf9e`](https://github.com/nodejs/node/commit/51513fdf9e)] - **doc**: napi: make header style consistent (Ali Ijaz Sheikh) [#18122](https://github.com/nodejs/node/pull/18122)
- [[`02ae3295d5`](https://github.com/nodejs/node/commit/02ae3295d5)] - **doc**: napi: fix unbalanced emphasis (Ali Ijaz Sheikh) [#18122](https://github.com/nodejs/node/pull/18122)
- [[`79ecc2c586`](https://github.com/nodejs/node/commit/79ecc2c586)] - **doc**: updates examples to use NULL (Michael Dawson) [#18008](https://github.com/nodejs/node/pull/18008)
- [[`b2213798a3`](https://github.com/nodejs/node/commit/b2213798a3)] - **doc**: fix MDN links to avoid redirections (Vse Mozhet Byt) [#18631](https://github.com/nodejs/node/pull/18631)
- [[`f4ddaaec0e`](https://github.com/nodejs/node/commit/f4ddaaec0e)] - **doc**: move Fedor to TSC Emeritus (Myles Borins) [#18752](https://github.com/nodejs/node/pull/18752)
- [[`b8f2acd2e8`](https://github.com/nodejs/node/commit/b8f2acd2e8)] - **doc**: add mmarchini to collaborators (Matheus Marchini) [#18740](https://github.com/nodejs/node/pull/18740)
- [[`16f9631475`](https://github.com/nodejs/node/commit/16f9631475)] - **doc**: add history for url.parse (Steven) [#18685](https://github.com/nodejs/node/pull/18685)
- [[`d30c3533ff`](https://github.com/nodejs/node/commit/d30c3533ff)] - **doc**: fix links to Style Guide and CPP Style Guide (Justin Lee) [#18683](https://github.com/nodejs/node/pull/18683)
- [[`176ed1e9b1`](https://github.com/nodejs/node/commit/176ed1e9b1)] - **doc**: add devsnek to collaborators (Gus Caplan) [#18679](https://github.com/nodejs/node/pull/18679)
- [[`25db460f03`](https://github.com/nodejs/node/commit/25db460f03)] - **doc**: expand on promises and async_hooks (Ali Ijaz Sheikh) [#18540](https://github.com/nodejs/node/pull/18540)
- [[`73adadd56a`](https://github.com/nodejs/node/commit/73adadd56a)] - **doc**: add section for strategic initiatives (Michael Dawson) [#17104](https://github.com/nodejs/node/pull/17104)
- [[`8ca6d34801`](https://github.com/nodejs/node/commit/8ca6d34801)] - **doc**: add introduce about cli options (Weijia Wang) [#18475](https://github.com/nodejs/node/pull/18475)
- [[`1ea1970c37`](https://github.com/nodejs/node/commit/1ea1970c37)] - **doc**: modify the return value of request.write() (陈刚) [#18526](https://github.com/nodejs/node/pull/18526)
- [[`50bdf0ed78`](https://github.com/nodejs/node/commit/50bdf0ed78)] - **doc**: be more explicit in the sypnosis (Tim O. Peters) [#17977](https://github.com/nodejs/node/pull/17977)
- [[`f8ad381e61`](https://github.com/nodejs/node/commit/f8ad381e61)] - **doc**: add missing meta for createCipheriv (Tobias Nießen) [#18651](https://github.com/nodejs/node/pull/18651)
- [[`0071560eb4`](https://github.com/nodejs/node/commit/0071560eb4)] - **doc**: fix description of createDecipheriv (Tobias Nießen) [#18651](https://github.com/nodejs/node/pull/18651)
- [[`c89781583b`](https://github.com/nodejs/node/commit/c89781583b)] - **doc**: fix various nits (Vse Mozhet Byt) [#19743](https://github.com/nodejs/node/pull/19743)
- [[`1091dfc801`](https://github.com/nodejs/node/commit/1091dfc801)] - **doc**: linkify missing types (Vse Mozhet Byt) [#18444](https://github.com/nodejs/node/pull/18444)
- [[`1107a494e4`](https://github.com/nodejs/node/commit/1107a494e4)] - **doc**: shell option for the execFile and execFileSync functions (jvelezpo) [#18237](https://github.com/nodejs/node/pull/18237)
- [[`36ea472393`](https://github.com/nodejs/node/commit/36ea472393)] - **doc**: improve http.request documentation (Guangcong Luo) [#18289](https://github.com/nodejs/node/pull/18289)
- [[`e5d5137963`](https://github.com/nodejs/node/commit/e5d5137963)] - **doc**: streamline README intro (Rich Trott) [#18483](https://github.com/nodejs/node/pull/18483)
- [[`eec9334a2e`](https://github.com/nodejs/node/commit/eec9334a2e)] - **doc**: move Brian White to TSC Emeriti list (Rich Trott) [#18482](https://github.com/nodejs/node/pull/18482)
- [[`ac41aacb05`](https://github.com/nodejs/node/commit/ac41aacb05)] - **doc**: improve stream documentation (陈刚) [#18375](https://github.com/nodejs/node/pull/18375)
- [[`7feeb1574e`](https://github.com/nodejs/node/commit/7feeb1574e)] - **doc**: add Gibson Fahnestock to TSC (Rich Trott) [#18481](https://github.com/nodejs/node/pull/18481)
- [[`142ad8d450`](https://github.com/nodejs/node/commit/142ad8d450)] - **doc**: reorder section on updating PR branch (Ali Ijaz Sheikh) [#18355](https://github.com/nodejs/node/pull/18355)
- [[`39ea4f12c5`](https://github.com/nodejs/node/commit/39ea4f12c5)] - **doc**: fix manpage warnings (Roman Reiss)
- [[`5209f9e1e2`](https://github.com/nodejs/node/commit/5209f9e1e2)] - **doc**: warn about GCM authenticity (Tobias Nießen) [#18376](https://github.com/nodejs/node/pull/18376)
- [[`e84e9db6fe`](https://github.com/nodejs/node/commit/e84e9db6fe)] - **doc**: capitalize non-primitive types (Vse Mozhet Byt) [#18111](https://github.com/nodejs/node/pull/18111)
- [[`84fa6eb173`](https://github.com/nodejs/node/commit/84fa6eb173)] - **doc, http2**: add sections for server.close() (Chris Miller) [#19802](https://github.com/nodejs/node/pull/19802)
- [[`cbc8561949`](https://github.com/nodejs/node/commit/cbc8561949)] - **errors**: remove ERR_OUTOFMEMORY (Tobias Nießen) [#17877](https://github.com/nodejs/node/pull/17877)
- [[`2995506bbf`](https://github.com/nodejs/node/commit/2995506bbf)] - **fs**: fix stack overflow in fs.readdirSync (Joyee Cheung) [#18647](https://github.com/nodejs/node/pull/18647)
- [[`a653f23dfc`](https://github.com/nodejs/node/commit/a653f23dfc)] - **fs**: fix `createReadStream(…, {end: n})` for non-seekable fds (Anna Henningsen) [#19329](https://github.com/nodejs/node/pull/19329)
- [[`6bfdba125f`](https://github.com/nodejs/node/commit/6bfdba125f)] - **http**: remove default 'drain' listener on upgrade (Luigi Pinca) [#18866](https://github.com/nodejs/node/pull/18866)
- [[`29c395d975`](https://github.com/nodejs/node/commit/29c395d975)] - **http**: allow \_httpMessage to be GC'ed (Luigi Pinca) [#18865](https://github.com/nodejs/node/pull/18865)
- [[`d2a884edf9`](https://github.com/nodejs/node/commit/d2a884edf9)] - **http**: fix parsing of binary upgrade response body (Ben Noordhuis) [#17806](https://github.com/nodejs/node/pull/17806)
- [[`1d88266543`](https://github.com/nodejs/node/commit/1d88266543)] - **http**: free the parser before emitting 'upgrade' (Luigi Pinca) [#18209](https://github.com/nodejs/node/pull/18209)
- [[`1455b1dec2`](https://github.com/nodejs/node/commit/1455b1dec2)] - **http2**: emit session connect on next tick (Pieter Mees) [#19842](https://github.com/nodejs/node/pull/19842)
- [[`6d6e2e2454`](https://github.com/nodejs/node/commit/6d6e2e2454)] - **http2**: callback valid check before closing request (Trivikram) [#19061](https://github.com/nodejs/node/pull/19061)
- [[`eddf3a6c70`](https://github.com/nodejs/node/commit/eddf3a6c70)] - **http2**: destroy() stream, upon errnoException (Sarat Addepalli) [#19389](https://github.com/nodejs/node/pull/19389)
- [[`e4c10e1201`](https://github.com/nodejs/node/commit/e4c10e1201)] - **http2**: remove some unnecessary next ticks (James M Snell) [#19451](https://github.com/nodejs/node/pull/19451)
- [[`c976cb5be5`](https://github.com/nodejs/node/commit/c976cb5be5)] - **http2**: no stream destroy while its data is on the wire (Anna Henningsen) [#19002](https://github.com/nodejs/node/pull/19002)
- [[`bfd7d6d0de`](https://github.com/nodejs/node/commit/bfd7d6d0de)] - **http2**: fix flaky test-http2-https-fallback (Matteo Collina) [#19093](https://github.com/nodejs/node/pull/19093)
- [[`b75897f982`](https://github.com/nodejs/node/commit/b75897f982)] - **http2**: fix endless loop when writing empty string (Anna Henningsen) [#18924](https://github.com/nodejs/node/pull/18924)
- [[`7e4a9c9fe2`](https://github.com/nodejs/node/commit/7e4a9c9fe2)] - **http2**: use original error for cancelling pending streams (Anna Henningsen) [#18988](https://github.com/nodejs/node/pull/18988)
- [[`2a04f57444`](https://github.com/nodejs/node/commit/2a04f57444)] - **http2**: send error text in case of ALPN mismatch (Anna Henningsen) [#18986](https://github.com/nodejs/node/pull/18986)
- [[`f366373ad8`](https://github.com/nodejs/node/commit/f366373ad8)] - **http2**: fix condition where data is lost (Matteo Collina) [#18895](https://github.com/nodejs/node/pull/18895)
- [[`20fb59fdc4`](https://github.com/nodejs/node/commit/20fb59fdc4)] - **http2**: use `_final` instead of `on('finish')` (Anna Henningsen) [#18609](https://github.com/nodejs/node/pull/18609)
- [[`ac64b4f6a7`](https://github.com/nodejs/node/commit/ac64b4f6a7)] - **http2**: add checks for server close callback (James M Snell) [#18182](https://github.com/nodejs/node/pull/18182)
- [[`8b0a1b32de`](https://github.com/nodejs/node/commit/8b0a1b32de)] - **http2**: refactor read mechanism (Anna Henningsen) [#18030](https://github.com/nodejs/node/pull/18030)
- [[`a4d910c644`](https://github.com/nodejs/node/commit/a4d910c644)] - **http2**: remember sent headers (James M Snell) [#18045](https://github.com/nodejs/node/pull/18045)
- [[`3cd205431b`](https://github.com/nodejs/node/commit/3cd205431b)] - **http2**: use aliased buffer for perf stats, add stats (James M Snell) [#18020](https://github.com/nodejs/node/pull/18020)
- [[`46d1b331e0`](https://github.com/nodejs/node/commit/46d1b331e0)] - **http2**: verify flood error and unsolicited frames (James M Snell) [#17969](https://github.com/nodejs/node/pull/17969)
- [[`a85518ed22`](https://github.com/nodejs/node/commit/a85518ed22)] - **http2**: verify that a dependency cycle may exist (James M Snell) [#17968](https://github.com/nodejs/node/pull/17968)
- [[`9c85ada4e3`](https://github.com/nodejs/node/commit/9c85ada4e3)] - **http2**: implement maxSessionMemory (James M Snell) [#17967](https://github.com/nodejs/node/pull/17967)
- [[`9a6ea7eb02`](https://github.com/nodejs/node/commit/9a6ea7eb02)] - **http2**: properly handle already closed stream error (James M Snell) [#17942](https://github.com/nodejs/node/pull/17942)
- [[`0078a97793`](https://github.com/nodejs/node/commit/0078a97793)] - **http2**: add aligned padding strategy (James M Snell) [#17938](https://github.com/nodejs/node/pull/17938)
- [[`1c313e09d6`](https://github.com/nodejs/node/commit/1c313e09d6)] - **http2**: add initial support for originSet (James M Snell) [#17935](https://github.com/nodejs/node/pull/17935)
- [[`1a24feccb5`](https://github.com/nodejs/node/commit/1a24feccb5)] - **http2**: add altsvc support (James M Snell) [#17917](https://github.com/nodejs/node/pull/17917)
- [[`c915bc54d4`](https://github.com/nodejs/node/commit/c915bc54d4)] - **http2**: strictly limit number on concurrent streams (James M Snell) [#16766](https://github.com/nodejs/node/pull/16766)
- [[`dcc7f4d84c`](https://github.com/nodejs/node/commit/dcc7f4d84c)] - **http2**: perf_hooks integration (James M Snell) [#17906](https://github.com/nodejs/node/pull/17906)
- [[`72b42de33a`](https://github.com/nodejs/node/commit/72b42de33a)] - **http2**: implement ref() and unref() on client sessions (Kelvin Jin) [#17620](https://github.com/nodejs/node/pull/17620)
- [[`55f6bdb698`](https://github.com/nodejs/node/commit/55f6bdb698)] - **http2**: keep session objects alive during Http2Scope (Anna Henningsen) [#17863](https://github.com/nodejs/node/pull/17863)
- [[`c61a54ec3d`](https://github.com/nodejs/node/commit/c61a54ec3d)] - **http2**: fix compiling with `--debug-http2` (Anna Henningsen) [#17863](https://github.com/nodejs/node/pull/17863)
- [[`04632214c1`](https://github.com/nodejs/node/commit/04632214c1)] - **http2**: convert Http2Settings to an AsyncWrap (James M Snell) [#17763](https://github.com/nodejs/node/pull/17763)
- [[`ea98fd573e`](https://github.com/nodejs/node/commit/ea98fd573e)] - **http2**: refactor outgoing write mechanism (Anna Henningsen) [#17718](https://github.com/nodejs/node/pull/17718)
- [[`05b823d4ad`](https://github.com/nodejs/node/commit/05b823d4ad)] - **http2**: remove redundant write indirection (Anna Henningsen) [#17718](https://github.com/nodejs/node/pull/17718)
- [[`fc40b7de46`](https://github.com/nodejs/node/commit/fc40b7de46)] - **http2**: cleanup Http2Stream/Http2Session destroy (James M Snell) [#17406](https://github.com/nodejs/node/pull/17406)
- [[`1d65f2b879`](https://github.com/nodejs/node/commit/1d65f2b879)] - **http2**: be sure to destroy the Http2Stream (James M Snell) [#17406](https://github.com/nodejs/node/pull/17406)
- [[`8431b4297c`](https://github.com/nodejs/node/commit/8431b4297c)] - **http2**: only schedule write when necessary (Anna Henningsen) [#17183](https://github.com/nodejs/node/pull/17183)
- [[`38cfb707bd`](https://github.com/nodejs/node/commit/38cfb707bd)] - **http2**: don't call into JS from GC (Anna Henningsen) [#17183](https://github.com/nodejs/node/pull/17183)
- [[`a1539e5731`](https://github.com/nodejs/node/commit/a1539e5731)] - **http2**: simplify onSelectPadding (Anna Henningsen) [#17717](https://github.com/nodejs/node/pull/17717)
- [[`9a4bac2081`](https://github.com/nodejs/node/commit/9a4bac2081)] - **http2,perf_hooks**: perf state using AliasedBuffer (Kyle Farnung) [#18300](https://github.com/nodejs/node/pull/18300)
- [[`9129bc4fde`](https://github.com/nodejs/node/commit/9129bc4fde)] - **lib**: set process.execPath on OpenBSD (Aaron Bieber) [#18543](https://github.com/nodejs/node/pull/18543)
- [[`2019b023a7`](https://github.com/nodejs/node/commit/2019b023a7)] - **lib**: refactor ES module loader for readability (Anna Henningsen) [#16579](https://github.com/nodejs/node/pull/16579)
- [[`3df0570c90`](https://github.com/nodejs/node/commit/3df0570c90)] - **lib**: fix spelling in comments (Tobias Nießen) [#18018](https://github.com/nodejs/node/pull/18018)
- [[`20844d1716`](https://github.com/nodejs/node/commit/20844d1716)] - **lib**: remove debugger dead code (Qingyan Li) [#18426](https://github.com/nodejs/node/pull/18426)
- [[`07a6770614`](https://github.com/nodejs/node/commit/07a6770614)] - **n-api**: add more `int64_t` tests (Kyle Farnung) [#19402](https://github.com/nodejs/node/pull/19402)
- [[`8b3ef4660a`](https://github.com/nodejs/node/commit/8b3ef4660a)] - **n-api**: back up env before finalize (Gabriel Schulhof) [#19718](https://github.com/nodejs/node/pull/19718)
- [[`92f699e021`](https://github.com/nodejs/node/commit/92f699e021)] - **n-api**: ensure in-module exceptions are propagated (Gabriel Schulhof) [#19537](https://github.com/nodejs/node/pull/19537)
- [[`367113f5d7`](https://github.com/nodejs/node/commit/367113f5d7)] - **n-api**: bump version of n-api supported (Michael Dawson) [#19497](https://github.com/nodejs/node/pull/19497)
- [[`24b8bb6708`](https://github.com/nodejs/node/commit/24b8bb6708)] - **n-api**: re-write test_make_callback (Gabriel Schulhof) [#19448](https://github.com/nodejs/node/pull/19448)
- [[`3a6b7e610d`](https://github.com/nodejs/node/commit/3a6b7e610d)] - **n-api**: add napi_fatal_exception (Mathias Buus) [#19337](https://github.com/nodejs/node/pull/19337)
- [[`9949d55ae9`](https://github.com/nodejs/node/commit/9949d55ae9)] - **n-api**: separate out async_hooks test (Gabriel Schulhof) [#19392](https://github.com/nodejs/node/pull/19392)
- [[`f29d8e0e8d`](https://github.com/nodejs/node/commit/f29d8e0e8d)] - **n-api**: add missing exception checking (Michael Dawson) [#19362](https://github.com/nodejs/node/pull/19362)
- [[`faf94b1c49`](https://github.com/nodejs/node/commit/faf94b1c49)] - **n-api**: resolve promise in test (Gabriel Schulhof) [#19245](https://github.com/nodejs/node/pull/19245)
- [[`df63adf7aa`](https://github.com/nodejs/node/commit/df63adf7aa)] - **n-api**: update documentation (Gabriel Schulhof) [#19078](https://github.com/nodejs/node/pull/19078)
- [[`b26410e86f`](https://github.com/nodejs/node/commit/b26410e86f)] - **n-api**: update reference test (Gabriel Schulhof) [#19086](https://github.com/nodejs/node/pull/19086)
- [[`cb3f90a1a9`](https://github.com/nodejs/node/commit/cb3f90a1a9)] - **n-api**: fix object test (Gabriel Schulhof) [#19039](https://github.com/nodejs/node/pull/19039)
- [[`9244e1d234`](https://github.com/nodejs/node/commit/9244e1d234)] - **n-api**: remove extra reference from test (Gabriel Schulhof) [#18542](https://github.com/nodejs/node/pull/18542)
- [[`927fc0b19f`](https://github.com/nodejs/node/commit/927fc0b19f)] - **n-api**: add methods to open/close callback scope (Michael Dawson) [#18089](https://github.com/nodejs/node/pull/18089)
- [[`969a520990`](https://github.com/nodejs/node/commit/969a520990)] - **n-api**: wrap control flow macro in do/while (Ben Noordhuis) [#18532](https://github.com/nodejs/node/pull/18532)
- [[`d89f5937eb`](https://github.com/nodejs/node/commit/d89f5937eb)] - **n-api**: implement wrapping using private properties (Gabriel Schulhof) [#18311](https://github.com/nodejs/node/pull/18311)
- [[`af655f586c`](https://github.com/nodejs/node/commit/af655f586c)] - **n-api**: change assert ok check to notStrictEqual. (Aaron Kau) [#18414](https://github.com/nodejs/node/pull/18414)
- [[`ca10fda064`](https://github.com/nodejs/node/commit/ca10fda064)] - **n-api**: throw RangeError napi_create_typedarray() (Jinho Bang) [#18037](https://github.com/nodejs/node/pull/18037)
- [[`853b4d593c`](https://github.com/nodejs/node/commit/853b4d593c)] - **n-api**: expose n-api version in process.versions (Michael Dawson) [#18067](https://github.com/nodejs/node/pull/18067)
- [[`48be8a4793`](https://github.com/nodejs/node/commit/48be8a4793)] - **n-api**: throw RangeError in napi_create_dataview() with invalid range (Jinho Bang) [#17869](https://github.com/nodejs/node/pull/17869)
- [[`a744535f99`](https://github.com/nodejs/node/commit/a744535f99)] - **n-api**: fix memory leak in napi_async_destroy() (alnyan) [#17714](https://github.com/nodejs/node/pull/17714)
- [[`584fadc605`](https://github.com/nodejs/node/commit/584fadc605)] - **n-api,test**: add int64 bounds tests (Kyle Farnung) [#19309](https://github.com/nodejs/node/pull/19309)
- [[`4c1181dc02`](https://github.com/nodejs/node/commit/4c1181dc02)] - **n-api,test**: add a new.target test to addons-napi (Taylor Woll) [#19236](https://github.com/nodejs/node/pull/19236)
- [[`3225601ffc`](https://github.com/nodejs/node/commit/3225601ffc)] - **net**: remove Socket.prototoype.read (Anna Henningsen) [#18568](https://github.com/nodejs/node/pull/18568)
- [[`35aaee1059`](https://github.com/nodejs/node/commit/35aaee1059)] - **net**: remove redundant code from \_writeGeneric() (Luigi Pinca) [#18429](https://github.com/nodejs/node/pull/18429)
- [[`54442efcd2`](https://github.com/nodejs/node/commit/54442efcd2)] - **perf_hooks**: refactor internals (James M Snell) [#17822](https://github.com/nodejs/node/pull/17822)
- [[`6bdfb1f8f0`](https://github.com/nodejs/node/commit/6bdfb1f8f0)] - **perf_hooks,http2**: add performance.clear() (James M Snell) [#18046](https://github.com/nodejs/node/pull/18046)
- [[`1faae90b74`](https://github.com/nodejs/node/commit/1faae90b74)] - **readline**: use Date.now() and move test to parallel (Anatoli Papirovski) [#18563](https://github.com/nodejs/node/pull/18563)
- [[`965b56a34e`](https://github.com/nodejs/node/commit/965b56a34e)] - **readline**: update references to archived repository (Tobias Nießen) [#17924](https://github.com/nodejs/node/pull/17924)
- [[`801a49935b`](https://github.com/nodejs/node/commit/801a49935b)] - **src**: add nullptr check for session in DEBUG macro (Daniel Bevenius) [#18815](https://github.com/nodejs/node/pull/18815)
- [[`4e807d648e`](https://github.com/nodejs/node/commit/4e807d648e)] - **src**: introduce internal buffer slice constructor (Anna Henningsen) [#18030](https://github.com/nodejs/node/pull/18030)
- [[`0b828e5125`](https://github.com/nodejs/node/commit/0b828e5125)] - **src**: remove declarations for missing functions (Anna Henningsen) [#18134](https://github.com/nodejs/node/pull/18134)
- [[`3766e04f31`](https://github.com/nodejs/node/commit/3766e04f31)] - **src**: silence http2 -Wunused-result warnings (cjihrig) [#17954](https://github.com/nodejs/node/pull/17954)
- [[`2b7732788a`](https://github.com/nodejs/node/commit/2b7732788a)] - **src**: add optional keep-alive object to SetImmediate (Anna Henningsen) [#17183](https://github.com/nodejs/node/pull/17183)
- [[`f3e082c4ea`](https://github.com/nodejs/node/commit/f3e082c4ea)] - **src**: replace SetAccessor w/ SetAccessorProperty (Jure Triglav) [#17665](https://github.com/nodejs/node/pull/17665)
- [[`45e28a8628`](https://github.com/nodejs/node/commit/45e28a8628)] - **src**: minor refactoring to StreamBase writes (Anna Henningsen) [#17564](https://github.com/nodejs/node/pull/17564)
- [[`42b4f3ce0b`](https://github.com/nodejs/node/commit/42b4f3ce0b)] - **src**: fix abort when taking a heap snapshot (Ben Noordhuis) [#18898](https://github.com/nodejs/node/pull/18898)
- [[`b48ca0a140`](https://github.com/nodejs/node/commit/b48ca0a140)] - **src**: fix crypto.pbkdf2 callback error argument (BufoViridis) [#18458](https://github.com/nodejs/node/pull/18458)
- [[`973488b77b`](https://github.com/nodejs/node/commit/973488b77b)] - **src**: replace var for let / const. (alejandro estrada) [#18649](https://github.com/nodejs/node/pull/18649)
- [[`9ac91b14de`](https://github.com/nodejs/node/commit/9ac91b14de)] - **src**: fix util abort (Ruben Bridgewater) [#19224](https://github.com/nodejs/node/pull/19224)
- [[`c0f40be23b`](https://github.com/nodejs/node/commit/c0f40be23b)] - **src**: free memory before re-setting URLHost value (Ivan Filenko) [#18357](https://github.com/nodejs/node/pull/18357)
- [[`5e4f9b37ba`](https://github.com/nodejs/node/commit/5e4f9b37ba)] - **src,doc,test**: Fix common misspellings (Roman Reiss) [#18151](https://github.com/nodejs/node/pull/18151)
- [[`10231a9e44`](https://github.com/nodejs/node/commit/10231a9e44)] - **stream**: cleanup() when unpiping all streams. (陈刚) [#18266](https://github.com/nodejs/node/pull/18266)
- [[`bf523822ba`](https://github.com/nodejs/node/commit/bf523822ba)] - **stream**: simplify `src._readableState` to `state` (陈刚) [#18264](https://github.com/nodejs/node/pull/18264)
- [[`37e594ed4a`](https://github.com/nodejs/node/commit/37e594ed4a)] - **stream**: remove unreachable code (Luigi Pinca) [#18239](https://github.com/nodejs/node/pull/18239)
- [[`f96b0bf494`](https://github.com/nodejs/node/commit/f96b0bf494)] - **string_decoder**: reset decoder on end (Justin Ridgewell) [#18494](https://github.com/nodejs/node/pull/18494)
- [[`4dbdb8ae4e`](https://github.com/nodejs/node/commit/4dbdb8ae4e)] - **test**: http2 errors on req.close() (Trivikram) [#18854](https://github.com/nodejs/node/pull/18854)
- [[`83d8ad351c`](https://github.com/nodejs/node/commit/83d8ad351c)] - **test**: http2 stream.respond() error checks (Trivikram) [#18861](https://github.com/nodejs/node/pull/18861)
- [[`b0664426f5`](https://github.com/nodejs/node/commit/b0664426f5)] - **test**: check endless loop while writing empty string (XadillaX) [#18924](https://github.com/nodejs/node/pull/18924)
- [[`7eba62e028`](https://github.com/nodejs/node/commit/7eba62e028)] - **test**: make test-tls-external-accessor agnostic (Rich Trott) [#16272](https://github.com/nodejs/node/pull/16272)
- [[`9e68947ff4`](https://github.com/nodejs/node/commit/9e68947ff4)] - **test**: add hasCrypto when using binding('crypto') (Daniel Bevenius) [#17867](https://github.com/nodejs/node/pull/17867)
- [[`6129ff4b99`](https://github.com/nodejs/node/commit/6129ff4b99)] - **test**: remove unnecessary timer (cjihrig) [#18719](https://github.com/nodejs/node/pull/18719)
- [[`2838f9b150`](https://github.com/nodejs/node/commit/2838f9b150)] - **test**: convert new tests to use error types (Jack Horton) [#18581](https://github.com/nodejs/node/pull/18581)
- [[`5c0983e5a2`](https://github.com/nodejs/node/commit/5c0983e5a2)] - **test**: improve error message output (Bhavani Shankar) [#18498](https://github.com/nodejs/node/pull/18498)
- [[`bebcdfe382`](https://github.com/nodejs/node/commit/bebcdfe382)] - **test**: show pending exception error in napi tests (Ben Wilcox) [#18413](https://github.com/nodejs/node/pull/18413)
- [[`5b1b74c5a5`](https://github.com/nodejs/node/commit/5b1b74c5a5)] - **test**: refactor addons-napi/test_exception/test.js (Rich Trott) [#18340](https://github.com/nodejs/node/pull/18340)
- [[`8cfa87832d`](https://github.com/nodejs/node/commit/8cfa87832d)] - **test**: fixed typos in napi test (furstenheim) [#18148](https://github.com/nodejs/node/pull/18148)
- [[`ad8c079af7`](https://github.com/nodejs/node/commit/ad8c079af7)] - **test**: remove ambiguous error messages from test_error (Nicholas Drane) [#17812](https://github.com/nodejs/node/pull/17812)
- [[`2e100c82be`](https://github.com/nodejs/node/commit/2e100c82be)] - **test**: remove literals that obscure assert messages (Rich Trott) [#17642](https://github.com/nodejs/node/pull/17642)
- [[`077e1870ae`](https://github.com/nodejs/node/commit/077e1870ae)] - **test**: add unhandled rejection guard (babygoat) [#17275](https://github.com/nodejs/node/pull/17275)
- [[`9236332cc3`](https://github.com/nodejs/node/commit/9236332cc3)] - **test**: update a few tests to work on OpenBSD (Aaron Bieber) [#18543](https://github.com/nodejs/node/pull/18543)
- [[`cbd698a521`](https://github.com/nodejs/node/commit/cbd698a521)] - **test**: refactor test-http-abort-before-end (cjihrig) [#18508](https://github.com/nodejs/node/pull/18508)
- [[`ab8edc9d48`](https://github.com/nodejs/node/commit/ab8edc9d48)] - **test**: fix flaky timers-block-eventloop test (Anatoli Papirovski) [#18567](https://github.com/nodejs/node/pull/18567)
- [[`53b702fdba`](https://github.com/nodejs/node/commit/53b702fdba)] - **test**: remove common.PORT from parallel tests (Rich Trott) [#17410](https://github.com/nodejs/node/pull/17410)
- [[`da162278de`](https://github.com/nodejs/node/commit/da162278de)] - **test**: mock the lookup function in parallel tests (Joyee Cheung) [#17296](https://github.com/nodejs/node/pull/17296)
- [[`34af49401b`](https://github.com/nodejs/node/commit/34af49401b)] - **test**: add common.dns.errorLookupMock (Joyee Cheung) [#17296](https://github.com/nodejs/node/pull/17296)
- [[`bff7258535`](https://github.com/nodejs/node/commit/bff7258535)] - **test**: do not check TXT content in test-dns-any (Joyee Cheung) [#18547](https://github.com/nodejs/node/pull/18547)
- [[`daeb6de8ec`](https://github.com/nodejs/node/commit/daeb6de8ec)] - **test**: use internet.addresses in internet tests (Joyee Cheung) [#16390](https://github.com/nodejs/node/pull/16390)
- [[`7813a0de0a`](https://github.com/nodejs/node/commit/7813a0de0a)] - **test**: introduce test/common/internet.addresses (Joyee Cheung) [#16390](https://github.com/nodejs/node/pull/16390)
- [[`745600a0b3`](https://github.com/nodejs/node/commit/745600a0b3)] - **test**: remove orphaned entries from status (Kyle Farnung) [#18092](https://github.com/nodejs/node/pull/18092)
- [[`e42ab10957`](https://github.com/nodejs/node/commit/e42ab10957)] - **test**: add assertions for TextEncoder/Decoder (Sho Miyamoto) [#18132](https://github.com/nodejs/node/pull/18132)
- [[`a1b0d5da07`](https://github.com/nodejs/node/commit/a1b0d5da07)] - **test**: move tmpdir to submodule of common (Rich Trott) [#17856](https://github.com/nodejs/node/pull/17856)
- [[`5155d8e7c3`](https://github.com/nodejs/node/commit/5155d8e7c3)] - **test**: update references to archived repository (Tobias Nießen) [#17924](https://github.com/nodejs/node/pull/17924)
- [[`1043b6fd7c`](https://github.com/nodejs/node/commit/1043b6fd7c)] - **test**: fix spelling in test case comments (Tobias Nießen) [#18018](https://github.com/nodejs/node/pull/18018)
- [[`fdb4dbc04b`](https://github.com/nodejs/node/commit/fdb4dbc04b)] - **test**: remove destructor from node_test_fixture (Daniel Bevenius) [#18524](https://github.com/nodejs/node/pull/18524)
- [[`e55e08c80e`](https://github.com/nodejs/node/commit/e55e08c80e)] - **test**: verify the shell option works properly on execFile (jvelezpo) [#18384](https://github.com/nodejs/node/pull/18384)
- [[`0cf9d0483f`](https://github.com/nodejs/node/commit/0cf9d0483f)] - **test**: add test for tls benchmarks (Anatoli Papirovski) [#18489](https://github.com/nodejs/node/pull/18489)
- [[`d630874250`](https://github.com/nodejs/node/commit/d630874250)] - **test**: speed up parallel/test-tls-session-cache (Anna Henningsen) [#18424](https://github.com/nodejs/node/pull/18424)
- [[`a1fb263880`](https://github.com/nodejs/node/commit/a1fb263880)] - **test**: fix flaky test-http-dns-error (Bryan English) [#16534](https://github.com/nodejs/node/pull/16534)
- [[`6f3fb46541`](https://github.com/nodejs/node/commit/6f3fb46541)] - **test**: use correct size in test-stream-buffer-list (Luigi Pinca) [#18239](https://github.com/nodejs/node/pull/18239)
- [[`a52f15efae`](https://github.com/nodejs/node/commit/a52f15efae)] - **timers**: fix a bug in error handling (Anatoli Papirovski) [#20497](https://github.com/nodejs/node/pull/20497)
- [[`e15f57745d`](https://github.com/nodejs/node/commit/e15f57745d)] - **timers**: allow Immediates to be unrefed (Anatoli Papirovski) [#18139](https://github.com/nodejs/node/pull/18139)
- [[`95c1e2d606`](https://github.com/nodejs/node/commit/95c1e2d606)] - **tls**: set servername on client side too (James M Snell) [#17935](https://github.com/nodejs/node/pull/17935)
- [[`d4bccccf23`](https://github.com/nodejs/node/commit/d4bccccf23)] - **tools**: add fixer for prefer-assert-iferror.js (Shobhit Chittora) [#16648](https://github.com/nodejs/node/pull/16648)
- [[`016a28ac08`](https://github.com/nodejs/node/commit/016a28ac08)] - **tools**: non-Ascii linter for /lib only (Sarat Addepalli) [#18043](https://github.com/nodejs/node/pull/18043)
- [[`a0a45fc3b6`](https://github.com/nodejs/node/commit/a0a45fc3b6)] - **tools**: add .mjs linting for Windows (Vse Mozhet Byt) [#18569](https://github.com/nodejs/node/pull/18569)
- [[`e0d2842b29`](https://github.com/nodejs/node/commit/e0d2842b29)] - **tools**: add check for using process.binding crypto (Daniel Bevenius) [#17867](https://github.com/nodejs/node/pull/17867)
- [[`a8b5a96d15`](https://github.com/nodejs/node/commit/a8b5a96d15)] - **tools**: auto fix custom eslint rule (Shobhit Chittora) [#16652](https://github.com/nodejs/node/pull/16652)
- [[`5d03c8219a`](https://github.com/nodejs/node/commit/5d03c8219a)] - **url**: simplify loop in parser (Tobias Nießen) [#18468](https://github.com/nodejs/node/pull/18468)
- [[`e0e0ef7bab`](https://github.com/nodejs/node/commit/e0e0ef7bab)] - **util**: escaping object keys in util.inspect() (buji) [#16986](https://github.com/nodejs/node/pull/16986)
- [[`8ac69c457b`](https://github.com/nodejs/node/commit/8ac69c457b)] - **v8**: add missing ',' in OpenBSD's 'sources' section. (Aaron Bieber) [#18448](https://github.com/nodejs/node/pull/18448)
- [[`c61754fad9`](https://github.com/nodejs/node/commit/c61754fad9)] - **win, build**: fix intl-none option (Birunthan Mohanathas) [#18292](https://github.com/nodejs/node/pull/18292)

Windows 32-bit Installer: https://nodejs.org/dist/v8.11.2/node-v8.11.2-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v8.11.2/node-v8.11.2-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v8.11.2/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v8.11.2/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v8.11.2/node-v8.11.2.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v8.11.2/node-v8.11.2-darwin-x64.tar.gz \
Linux 32-bit Binary: https://nodejs.org/dist/v8.11.2/node-v8.11.2-linux-x86.tar.xz \
Linux 64-bit Binary: https://nodejs.org/dist/v8.11.2/node-v8.11.2-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v8.11.2/node-v8.11.2-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v8.11.2/node-v8.11.2-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v8.11.2/node-v8.11.2-aix-ppc64.tar.gz \
SmartOS 32-bit Binary: https://nodejs.org/dist/v8.11.2/node-v8.11.2-sunos-x86.tar.xz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v8.11.2/node-v8.11.2-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v8.11.2/node-v8.11.2-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v8.11.2/node-v8.11.2-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v8.11.2/node-v8.11.2-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v8.11.2/node-v8.11.2.tar.gz \
Other release files: https://nodejs.org/dist/v8.11.2/ \
Documentation: https://nodejs.org/docs/v8.11.2/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

9a39fa0477246ada0ba248f410c57fec25abf3c581018f4b7dbaefb3f1d6dd7f  node-v8.11.2-aix-ppc64.tar.gz
6bd619ab577a1256531e50e31924d99a094eb920de8b5bd060a056a2608282b9  node-v8.11.2-darwin-x64.tar.gz
b6503f5566932cc231f581805a4ccc79eadfdad19d351e0c40a2055cff82fdc3  node-v8.11.2-darwin-x64.tar.xz
6125250fe8e074e509a7c691ebf04a93c20246fea5637ef6fe8aa844cef98b41  node-v8.11.2-headers.tar.gz
45189696c1a3f78a41adaadcc64a94cdaf4fec5210a31d6af095e87932bbacd4  node-v8.11.2-headers.tar.xz
8b376554ea7dc4ae3e2bfbd407b45ee4f978c199b1764430a40802dd6019d882  node-v8.11.2-linux-arm64.tar.gz
c8e9833402277c1a5a31b41cc31c9cedad68ba87c2ef78aed6a3ced81af9da7f  node-v8.11.2-linux-arm64.tar.xz
f9614139addda5c327486a5d7e92d6a9d1bd6d8dccb3dd4eed0d0e81ec2a605d  node-v8.11.2-linux-armv6l.tar.gz
6d0577d40fa61606d6c4da4bb329ca561f559dd11285af535e281b08a491ffd6  node-v8.11.2-linux-armv6l.tar.xz
5cfef0c7bd142964b195fbeed574e5887beeec5ad4f403561a78f1924b53bb38  node-v8.11.2-linux-armv7l.tar.gz
b7e2d9db8a3e6ddcb16238d08e256595874f5396c7404a9b1fe795b45654c83a  node-v8.11.2-linux-armv7l.tar.xz
6f00fc607273c148670423b64485e750977f2968b1f699635ba2310012adc41e  node-v8.11.2-linux-ppc64le.tar.gz
840d76db330bfc9c0b63aab61e7d79baeeccbb69833d2e3a5687013e47f03bbf  node-v8.11.2-linux-ppc64le.tar.xz
cc8e3551f1c687c7912205c1e992547a021f705696f3e5e1a893f50df29803df  node-v8.11.2-linux-s390x.tar.gz
ed8069f679b1bbd911c11ad193ebfeddfac0d33724c359e8f2d88f02aba6c235  node-v8.11.2-linux-s390x.tar.xz
67dc4c06a58d4b23c5378325ad7e0a2ec482b48cea802252b99ebe8538a3ab79  node-v8.11.2-linux-x64.tar.gz
213599127d24496cbf1cbb2a7c51060a3506d6b11132c59bb7f9f8a0edd210a7  node-v8.11.2-linux-x64.tar.xz
7700e140e13f49c641dc2dc6874a6c8d0aba0969fa6b728a14887d4bef3b29a8  node-v8.11.2-linux-x86.tar.gz
b380090649229d0ccc7afb8a6b432d60dd012da5e40dca903c7ac2d15f33a7a1  node-v8.11.2-linux-x86.tar.xz
b4dae89125bedd97552d88134df9110938661f43cde09c55e2b1ad61812ecaea  node-v8.11.2.pkg
a2409071006c36d92ed2aa868ee5093edb11ca7c081a47195f81fa54c0fb554f  node-v8.11.2-sunos-x64.tar.gz
f88abcfeeb8c49aaf9d6a39a09311c372cc3f711e14456a3c515aba22cf4ddbe  node-v8.11.2-sunos-x64.tar.xz
2851fe1e9beab968e639445678ac017056e3c93ed3e8a6e4f3d16a3e616c6d67  node-v8.11.2-sunos-x86.tar.gz
f6cd3779b9c770ce82c2082084b255a790530d869fdaaf5900d5ff29a9362d18  node-v8.11.2-sunos-x86.tar.xz
0ac2c4de270caa08b5adcdb3c6bcb8aae3651a37d035d385bc819fbacaf350e6  node-v8.11.2.tar.gz
539946c0381809576bed07424a35fc1740d52f4bd56305d6278d9e76c88f4979  node-v8.11.2.tar.xz
8bbd03b041f8326aba5ab754e4619eb3322907ddbfd77b93ddbcdaa435533ce0  node-v8.11.2-win-x64.7z
bbac1f56e8001a093acb2942f8a43c3bf22aec58216f446b7bf01f6f822dfe3f  node-v8.11.2-win-x64.zip
03f1bf7a206b8d24f921606ddafd865b5d60bd54e7998fc5d1744cef2e12c443  node-v8.11.2-win-x86.7z
f002b0c4e3fcfb30f15b8f52076a483f789f5413a6e406c98d629c4a9025e51f  node-v8.11.2-win-x86.zip
108dd9ccd131931e42b57dc1243dc28aacfebe207de48c420a576fe453d33ed7  node-v8.11.2-x64.msi
334d5ed17811b22fafd2c359f5f5dc7e803b13ec1099a9426dbcb2ca9871add0  node-v8.11.2-x86.msi
5909e2899950e95cfb67ffca7ca4a77e70158c84bbaa3136ffd144075a12aaff  win-x64/node.exe
20899b5265163175a265cd7b211dc7054642fe65155ded3c13795b0e1d430f9f  win-x64/node.lib
d5d6355cd1965b1c78e2cabbf9546ca429863d47e36ed46aa0ebffd75fd3c6ca  win-x64/node_pdb.7z
b70ae6ad1d65299e7e632da9ee536826a53fb01b5e934b7f86213010de3d6db5  win-x64/node_pdb.zip
cae63b5db208fca6a95f342064964bf3a2ff16c738801f002662d92a612adfed  win-x86/node.exe
d6694de6420ea035f3c28a77f1527234baa1c97e690ebe9ba82ad44540c43829  win-x86/node.lib
43a2b686a5658ef82db7258efd7801c47618d076065f24702c626857edb767b0  win-x86/node_pdb.7z
61fb800c2bf4338b117cb9ae80b89f6f4f35ae87027a33b2985217671ecefcb8  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQEzBAEBCAAdFiEEDv/hvO/ZyE49CYFSkzsB9AtcqUYFAlr7UUkACgkQkzsB9Atc
qUZ/GAf+PznNJePu5G1CnpKeJZpIfpYAT2kOU0U4tQP8KyU6+gqDdipKOTde3O/7
/KPFDGNyZCM1ZyssSXwVeACBJkZK9tt+wylsB1FwLkAMFOgvSlIkxVAvyJ4VOTDp
pLgHC41UOpdNJDTZHwElyDffxKOhm/bE16qXab16FKR5d9t59041jRxjfAUVeOhp
aQ9MhSC+32+JpwLQsxOeRAsR34svq/dgLnhJGqCviQT9tH4KLLdIEzwxQA0rxHt3
M2X6yZg7wuFj3tdXYD1lkFUPmgFHjFTBuURdcwxSuMql4BoBNGq3PHGC+4GpmowV
hu4ZqXeIZyuHrhPKZXaX9+PPI2fzxA==
=wDxC
-----END PGP SIGNATURE-----

```
