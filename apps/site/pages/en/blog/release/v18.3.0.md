---
date: '2022-06-02T03:30:21.893Z'
category: release
title: Node v18.3.0 (Current)
layout: blog-post
author: <PERSON> English
---

### Notable Changes

- \[[`dc3b91f351`](https://github.com/nodejs/node/commit/dc3b91f351)] - **deps**: update undici to 5.4.0 (Node.js GitHub Bot) [#43262](https://github.com/nodejs/node/pull/43262)
- \[[`d6cf409d78`](https://github.com/nodejs/node/commit/d6cf409d78)] - **(SEMVER-MINOR)** **util**: add parseArgs module (<PERSON>) [#42675](https://github.com/nodejs/node/pull/42675)
- \[[`9539cfa358`](https://github.com/nodejs/node/commit/9539cfa358)] - **(SEMVER-MINOR)** **http**: add uniqueHeaders option to request and createServer (<PERSON>) [#41397](https://github.com/nodejs/node/pull/41397)
- \[[`41fdc2617d`](https://github.com/nodejs/node/commit/41fdc2617d)] - **deps**: upgrade npm to 8.11.0 (npm team) [#43210](https://github.com/nodejs/node/pull/43210)
- \[[`0000654e47`](https://github.com/nodejs/node/commit/0000654e47)] - **deps**: patch V8 to ********** (Michaël Zasso) [#43067](https://github.com/nodejs/node/pull/43067)
- \[[`b3c8e609fd`](https://github.com/nodejs/node/commit/b3c8e609fd)] - **(SEMVER-MINOR)** **deps**: update V8 to 10.2.154.2 (Michaël Zasso) [#42740](https://github.com/nodejs/node/pull/42740)
- \[[`3e89b7336d`](https://github.com/nodejs/node/commit/3e89b7336d)] - **(SEMVER-MINOR)** **fs**: make params in writing methods optional (LiviaMedeiros) [#42601](https://github.com/nodejs/node/pull/42601)
- \[[`9539cfa358`](https://github.com/nodejs/node/commit/9539cfa358)] - **(SEMVER-MINOR)** **http**: add uniqueHeaders option to request and createServer (Paolo Insogna) [#41397](https://github.com/nodejs/node/pull/41397)
- \[[`8f5b4570e5`](https://github.com/nodejs/node/commit/8f5b4570e5)] - **(SEMVER-MINOR)** **net**: add ability to reset a tcp socket (pupilTong) [#43112](https://github.com/nodejs/node/pull/43112)
- \[[`5eff7b4a6a`](https://github.com/nodejs/node/commit/5eff7b4a6a)] - **(SEMVER-MINOR)** _**Revert**_ "**build**: make x86 Windows support temporarily experimental" (Michaël Zasso) [#42740](https://github.com/nodejs/node/pull/42740)
  - This means 32-bit Windows binaries are back with this release.

### Commits

- \[[`aefc9dda9a`](https://github.com/nodejs/node/commit/aefc9dda9a)] - **benchmark**: add node-error benchmark (RafaelGSS) [#43077](https://github.com/nodejs/node/pull/43077)
- \[[`85d81a764f`](https://github.com/nodejs/node/commit/85d81a764f)] - **bootstrap**: include code cache in the embedded snapshot (Joyee Cheung) [#43023](https://github.com/nodejs/node/pull/43023)
- \[[`5eff7b4a6a`](https://github.com/nodejs/node/commit/5eff7b4a6a)] - **(SEMVER-MINOR)** _**Revert**_ "**build**: make x86 Windows support temporarily experimental" (Michaël Zasso) [#42740](https://github.com/nodejs/node/pull/42740)
- \[[`d6634707c5`](https://github.com/nodejs/node/commit/d6634707c5)] - **(SEMVER-MINOR)** **build**: run V8 tests with detected Python version (Richard Lau) [#42740](https://github.com/nodejs/node/pull/42740)
- \[[`b8b5e6df67`](https://github.com/nodejs/node/commit/b8b5e6df67)] - **(SEMVER-MINOR)** **build**: reset embedder string to "-node.0" (Michaël Zasso) [#42740](https://github.com/nodejs/node/pull/42740)
- \[[`285ef30877`](https://github.com/nodejs/node/commit/285ef30877)] - **console**: fix console.dir crash on a revoked proxy (Daeyeon Jeong) [#43100](https://github.com/nodejs/node/pull/43100)
- \[[`920d8c5300`](https://github.com/nodejs/node/commit/920d8c5300)] - **crypto**: align webcrypto RSA key import/export with other implementations (Filip Skokan) [#42816](https://github.com/nodejs/node/pull/42816)
- \[[`c76caadf2e`](https://github.com/nodejs/node/commit/c76caadf2e)] - **debugger**: throw a more useful error when the frame is missing (Kohei Ueno) [#42776](https://github.com/nodejs/node/pull/42776)
- \[[`dc3b91f351`](https://github.com/nodejs/node/commit/dc3b91f351)] - **deps**: update undici to 5.4.0 (Node.js GitHub Bot) [#43262](https://github.com/nodejs/node/pull/43262)
- \[[`35250bf2f6`](https://github.com/nodejs/node/commit/35250bf2f6)] - **deps**: regenerate OpenSSL archs files (Daniel Bevenius) [#42973](https://github.com/nodejs/node/pull/42973)
- \[[`ecacc3a727`](https://github.com/nodejs/node/commit/ecacc3a727)] - **deps**: exclude linker scripts for windows builds (Daniel Bevenius) [#42973](https://github.com/nodejs/node/pull/42973)
- \[[`41fdc2617d`](https://github.com/nodejs/node/commit/41fdc2617d)] - **deps**: upgrade npm to 8.11.0 (npm team) [#43210](https://github.com/nodejs/node/pull/43210)
- \[[`87b248e27e`](https://github.com/nodejs/node/commit/87b248e27e)] - **deps**: update undici to 5.3.0 (Node.js GitHub Bot) [#43197](https://github.com/nodejs/node/pull/43197)
- \[[`d42de132a7`](https://github.com/nodejs/node/commit/d42de132a7)] - **deps**: upgrade npm to 8.10.0 (npm team) [#43061](https://github.com/nodejs/node/pull/43061)
- \[[`0000654e47`](https://github.com/nodejs/node/commit/0000654e47)] - **deps**: patch V8 to ********** (Michaël Zasso) [#43067](https://github.com/nodejs/node/pull/43067)
- \[[`742ffefb44`](https://github.com/nodejs/node/commit/742ffefb44)] - **(SEMVER-MINOR)** **deps**: make V8 10.2 ABI-compatible with 10.1 (Michaël Zasso) [#42740](https://github.com/nodejs/node/pull/42740)
- \[[`c626a533c7`](https://github.com/nodejs/node/commit/c626a533c7)] - **deps**: make V8 compilable with older glibc (Michaël Zasso) [#42657](https://github.com/nodejs/node/pull/42657)
- \[[`76546b12a2`](https://github.com/nodejs/node/commit/76546b12a2)] - **deps**: V8: fix v8-cppgc.h for MSVC (Jiawen Geng) [#42657](https://github.com/nodejs/node/pull/42657)
- \[[`45e1fd4473`](https://github.com/nodejs/node/commit/45e1fd4473)] - **deps**: silence V8's warning on CompileFunction (Michaël Zasso) [#40907](https://github.com/nodejs/node/pull/40907)
- \[[`2130891a9a`](https://github.com/nodejs/node/commit/2130891a9a)] - **(SEMVER-MINOR)** **deps**: disable trap handler for Windows cross-compiler (Michaël Zasso) [#40488](https://github.com/nodejs/node/pull/40488)
- \[[`e678b6c63d`](https://github.com/nodejs/node/commit/e678b6c63d)] - **deps**: fix V8 build issue with inline methods (Jiawen Geng) [#38807](https://github.com/nodejs/node/pull/38807)
- \[[`f83323f304`](https://github.com/nodejs/node/commit/f83323f304)] - **deps**: V8: forward declaration of `Rtl*FunctionTable` (Refael Ackermann) [#32116](https://github.com/nodejs/node/pull/32116)
- \[[`4be7584ca6`](https://github.com/nodejs/node/commit/4be7584ca6)] - **deps**: V8: un-cherry-pick bd019bd (Refael Ackermann) [#32116](https://github.com/nodejs/node/pull/32116)
- \[[`b3c8e609fd`](https://github.com/nodejs/node/commit/b3c8e609fd)] - **(SEMVER-MINOR)** **deps**: update V8 to 10.2.154.2 (Michaël Zasso) [#42740](https://github.com/nodejs/node/pull/42740)
- \[[`0afdc3e9a8`](https://github.com/nodejs/node/commit/0afdc3e9a8)] - **doc**: use serial comma in errors docs (Tobias Nießen) [#43242](https://github.com/nodejs/node/pull/43242)
- \[[`00989338b3`](https://github.com/nodejs/node/commit/00989338b3)] - **doc**: add note regarding `%Array.prototype.concat%` in `primordials.md` (Antoine du Hamel) [#43166](https://github.com/nodejs/node/pull/43166)
- \[[`badf72dd0a`](https://github.com/nodejs/node/commit/badf72dd0a)] - **doc**: use serial comma in worker_threads docs (Tobias Nießen) [#43220](https://github.com/nodejs/node/pull/43220)
- \[[`776e746f0a`](https://github.com/nodejs/node/commit/776e746f0a)] - **doc**: fix napi version for node_api_symbol_for (Danielle Adams) [#42878](https://github.com/nodejs/node/pull/42878)
- \[[`76b46801f8`](https://github.com/nodejs/node/commit/76b46801f8)] - **doc**: document `signal` option for `EventTarget#addEventListener` (Antoine du Hamel) [#43170](https://github.com/nodejs/node/pull/43170)
- \[[`3082c75efd`](https://github.com/nodejs/node/commit/3082c75efd)] - **doc**: make minor adjustments (LiviaMedeiros) [#43175](https://github.com/nodejs/node/pull/43175)
- \[[`598c1f102e`](https://github.com/nodejs/node/commit/598c1f102e)] - **doc**: use serial comma in dgram docs (Tobias Nießen) [#43191](https://github.com/nodejs/node/pull/43191)
- \[[`b772c13a62`](https://github.com/nodejs/node/commit/b772c13a62)] - **doc**: use serial comma in process docs (Tobias Nießen) [#43179](https://github.com/nodejs/node/pull/43179)
- \[[`f90a3d7a54`](https://github.com/nodejs/node/commit/f90a3d7a54)] - **doc**: improved parallel specification (mawaregetsuka) [#42679](https://github.com/nodejs/node/pull/42679)
- \[[`71074eedef`](https://github.com/nodejs/node/commit/71074eedef)] - **doc**: improve callback params for `fs.mkdir` (Daeyeon Jeong) [#43016](https://github.com/nodejs/node/pull/43016)
- \[[`2b8b224077`](https://github.com/nodejs/node/commit/2b8b224077)] - **doc**: remove outdated footnote (Python 2 --> 3 for V8 tests) (DeeDeeG) [#43105](https://github.com/nodejs/node/pull/43105)
- \[[`945f228cf1`](https://github.com/nodejs/node/commit/945f228cf1)] - **doc**: add release key for RafaelGSS (Rafael Gonzaga) [#43131](https://github.com/nodejs/node/pull/43131)
- \[[`56fc712f93`](https://github.com/nodejs/node/commit/56fc712f93)] - **doc**: use serial comma in assert docs (Tobias Nießen) [#43154](https://github.com/nodejs/node/pull/43154)
- \[[`093a3cf2f1`](https://github.com/nodejs/node/commit/093a3cf2f1)] - **doc**: fix errors in Performance hooks doc (OneNail) [#43152](https://github.com/nodejs/node/pull/43152)
- \[[`4d9f43a8a7`](https://github.com/nodejs/node/commit/4d9f43a8a7)] - **doc**: use serial comma in dns docs (Tobias Nießen) [#43145](https://github.com/nodejs/node/pull/43145)
- \[[`4e9393e32f`](https://github.com/nodejs/node/commit/4e9393e32f)] - **doc**: use ASCII apostrophes consistently (Tobias Nießen) [#43114](https://github.com/nodejs/node/pull/43114)
- \[[`b3181851d7`](https://github.com/nodejs/node/commit/b3181851d7)] - **doc**: add strategic initiative for shadow realm (Chengzhong Wu) [#43037](https://github.com/nodejs/node/pull/43037)
- \[[`6f48dfb499`](https://github.com/nodejs/node/commit/6f48dfb499)] - **fs**: add trailing commas (LiviaMedeiros) [#43127](https://github.com/nodejs/node/pull/43127)
- \[[`3e89b7336d`](https://github.com/nodejs/node/commit/3e89b7336d)] - **(SEMVER-MINOR)** **fs**: make params in writing methods optional (LiviaMedeiros) [#42601](https://github.com/nodejs/node/pull/42601)
- \[[`9539cfa358`](https://github.com/nodejs/node/commit/9539cfa358)] - **(SEMVER-MINOR)** **http**: add uniqueHeaders option to request and createServer (Paolo Insogna) [#41397](https://github.com/nodejs/node/pull/41397)
- \[[`b187060f76`](https://github.com/nodejs/node/commit/b187060f76)] - **http2**: set origin name correctly when servername is empty (ofirbarak) [#42838](https://github.com/nodejs/node/pull/42838)
- \[[`a07f5f28f3`](https://github.com/nodejs/node/commit/a07f5f28f3)] - **http2**: improve tests and docs (Daeyeon Jeong) [#42858](https://github.com/nodejs/node/pull/42858)
- \[[`701d40496d`](https://github.com/nodejs/node/commit/701d40496d)] - **lib**: refactor `validateInt32` and `validateUint32` (mawaregetsuka) [#43071](https://github.com/nodejs/node/pull/43071)
- \[[`09da76493a`](https://github.com/nodejs/node/commit/09da76493a)] - **meta**: update AUTHORS (Node.js GitHub Bot) [#43231](https://github.com/nodejs/node/pull/43231)
- \[[`1da6b7c32b`](https://github.com/nodejs/node/commit/1da6b7c32b)] - **meta**: add mailmap entry for legendecas (Chengzhong Wu) [#43156](https://github.com/nodejs/node/pull/43156)
- \[[`1be254b24e`](https://github.com/nodejs/node/commit/1be254b24e)] - **meta**: add mailmap entry for npm team (Luigi Pinca) [#43143](https://github.com/nodejs/node/pull/43143)
- \[[`7d8d9d625a`](https://github.com/nodejs/node/commit/7d8d9d625a)] - **meta**: add mailmap entry for Morgan Roderick (Luigi Pinca) [#43144](https://github.com/nodejs/node/pull/43144)
- \[[`8f5b4570e5`](https://github.com/nodejs/node/commit/8f5b4570e5)] - **(SEMVER-MINOR)** **net**: add ability to reset a tcp socket (pupilTong) [#43112](https://github.com/nodejs/node/pull/43112)
- \[[`11c783fa63`](https://github.com/nodejs/node/commit/11c783fa63)] - **net**: remoteAddress always undefined called before connected (OneNail) [#43011](https://github.com/nodejs/node/pull/43011)
- \[[`9c4e070567`](https://github.com/nodejs/node/commit/9c4e070567)] - **(SEMVER-MINOR)** **node-api**: emit uncaught-exception on unhandled tsfn callbacks (Chengzhong Wu) [#36510](https://github.com/nodejs/node/pull/36510)
- \[[`54d68b1afd`](https://github.com/nodejs/node/commit/54d68b1afd)] - **perf_hooks**: fix start_time of perf_hooks (theanarkh) [#43069](https://github.com/nodejs/node/pull/43069)
- \[[`baa5d0005a`](https://github.com/nodejs/node/commit/baa5d0005a)] - **src**: refactor GetCipherValue and related functions (Tobias Nießen) [#43171](https://github.com/nodejs/node/pull/43171)
- \[[`6ee9fb14e4`](https://github.com/nodejs/node/commit/6ee9fb14e4)] - **src**: make SecureContext fields private (Tobias Nießen) [#43173](https://github.com/nodejs/node/pull/43173)
- \[[`1e2552ba6c`](https://github.com/nodejs/node/commit/1e2552ba6c)] - **src**: reuse GetServerName (Tobias Nießen) [#43168](https://github.com/nodejs/node/pull/43168)
- \[[`d8a14a2486`](https://github.com/nodejs/node/commit/d8a14a2486)] - **src**: fix static analysis warning and use smart ptr (Tobias Nießen) [#43117](https://github.com/nodejs/node/pull/43117)
- \[[`72a767b7ca`](https://github.com/nodejs/node/commit/72a767b7ca)] - **src**: remove SecureContext::operator\* (Tobias Nießen) [#43121](https://github.com/nodejs/node/pull/43121)
- \[[`82fb037388`](https://github.com/nodejs/node/commit/82fb037388)] - **src**: move context snapshot index to SnapshotData (Joyee Cheung) [#43023](https://github.com/nodejs/node/pull/43023)
- \[[`9577878258`](https://github.com/nodejs/node/commit/9577878258)] - **src**: replace TraceEventScope with sync events (Chengzhong Wu) [#42977](https://github.com/nodejs/node/pull/42977)
- \[[`41b69e3cf4`](https://github.com/nodejs/node/commit/41b69e3cf4)] - **src,lib**: migrate to console on context's extra binding (Chengzhong Wu) [#43142](https://github.com/nodejs/node/pull/43142)
- \[[`3e1ed1ee0c`](https://github.com/nodejs/node/commit/3e1ed1ee0c)] - **test**: improve code coverage for inspector connection errors (Kohei Ueno) [#42310](https://github.com/nodejs/node/pull/42310)
- \[[`d7ca2234dc`](https://github.com/nodejs/node/commit/d7ca2234dc)] - **test**: use mustSucceed instead of mustCall with assert.ifError (MURAKAMI Masahiko) [#43188](https://github.com/nodejs/node/pull/43188)
- \[[`9cc5ce6a24`](https://github.com/nodejs/node/commit/9cc5ce6a24)] - **test**: improve readline/emitKeypressEvents.js coverage (OneNail) [#42908](https://github.com/nodejs/node/pull/42908)
- \[[`447bbd0d66`](https://github.com/nodejs/node/commit/447bbd0d66)] - **tls**: fix convertALPNProtocols accepting ArrayBufferViews (LiviaMedeiros) [#43211](https://github.com/nodejs/node/pull/43211)
- \[[`df691464aa`](https://github.com/nodejs/node/commit/df691464aa)] - **tools**: update lint-md-dependencies to rollup\@2.75.1 (Node.js GitHub Bot) [#43230](https://github.com/nodejs/node/pull/43230)
- \[[`d9fb25c936`](https://github.com/nodejs/node/commit/d9fb25c936)] - **tools**: refactor build-addons.js to ESM (Feng Yu) [#43099](https://github.com/nodejs/node/pull/43099)
- \[[`0efeceb4a6`](https://github.com/nodejs/node/commit/0efeceb4a6)] - **tools**: update lint-md-dependencies to rollup\@2.74.1 (Node.js GitHub Bot) [#43172](https://github.com/nodejs/node/pull/43172)
- \[[`ed352a945c`](https://github.com/nodejs/node/commit/ed352a945c)] - **tools**: update eslint to 8.16.0 (Node.js GitHub Bot) [#43174](https://github.com/nodejs/node/pull/43174)
- \[[`1183058908`](https://github.com/nodejs/node/commit/1183058908)] - **tools**: refactor update-authors.js to ESM (Feng Yu) [#43098](https://github.com/nodejs/node/pull/43098)
- \[[`5228028962`](https://github.com/nodejs/node/commit/5228028962)] - **(SEMVER-MINOR)** **tools**: update V8 gypfiles for 10.2 (Michaël Zasso) [#42740](https://github.com/nodejs/node/pull/42740)
- \[[`d6cf409d78`](https://github.com/nodejs/node/commit/d6cf409d78)] - **(SEMVER-MINOR)** **util**: add parseArgs module (Benjamin Coe) [#42675](https://github.com/nodejs/node/pull/42675)
- \[[`d91b489784`](https://github.com/nodejs/node/commit/d91b489784)] - **worker**: fix heap snapshot crash on exit (Chengzhong Wu) [#43123](https://github.com/nodejs/node/pull/43123)

Windows 32-bit Installer: https://nodejs.org/dist/v18.3.0/node-v18.3.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v18.3.0/node-v18.3.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v18.3.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v18.3.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v18.3.0/node-v18.3.0.pkg \
macOS Apple Silicon 64-bit Binary: https://nodejs.org/dist/v18.3.0/node-v18.3.0-darwin-arm64.tar.gz \
macOS Intel 64-bit Binary: https://nodejs.org/dist/v18.3.0/node-v18.3.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v18.3.0/node-v18.3.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v18.3.0/node-v18.3.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v18.3.0/node-v18.3.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v18.3.0/node-v18.3.0-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v18.3.0/node-v18.3.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v18.3.0/node-v18.3.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v18.3.0/node-v18.3.0.tar.gz \
Other release files: https://nodejs.org/dist/v18.3.0/ \
Documentation: https://nodejs.org/docs/v18.3.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

dc4664de812d7f1d5aecf82e3a6ffbd63e1c2e0c5b32d9bde6c6dc51c9c51f50  node-v18.3.0-aix-ppc64.tar.gz
5800be1084a61dbbe6748535c7f0aefa8bc9b2a5aeb6482916f4b7500d6fc892  node-v18.3.0-darwin-arm64.tar.gz
437e836a1e77d3e19c6e8a7526b8077fb38062a01511b99f3801457db6a63bec  node-v18.3.0-darwin-arm64.tar.xz
e4d4b81d08adbf34c40d46d4143ec1fab92372b459b466e9e6ec9a228a93badd  node-v18.3.0-darwin-x64.tar.gz
8df649a6e285cc7f36eb79d304f85d2f002c1341fa3468fb82b792a48cce5a90  node-v18.3.0-darwin-x64.tar.xz
6da17bee959f11fbe459826746ab7a2cf04da90286ad408b8c9eb143948b8b77  node-v18.3.0-headers.tar.gz
db0443991a43b0f141a7a6dc7ad5e49835773bb394974e6c8faeb2128e6ff04b  node-v18.3.0-headers.tar.xz
17753a86da5f4e37c711f7feffd08cdf3ba5c241088dab5389214d0d235ca1d1  node-v18.3.0-linux-arm64.tar.gz
35db77c8346b324f5cdba76120410751a1ca4ea8d81ae52684683bcf3eb11930  node-v18.3.0-linux-arm64.tar.xz
fb85e8352d43de365effd4b9620bc22f2bc291213c13be2aeac88c8ea741c5ce  node-v18.3.0-linux-armv7l.tar.gz
1c098aa61552bb9c5cf53d61ea6599352e9e02a0df74d4978f172faffd33b612  node-v18.3.0-linux-armv7l.tar.xz
ac24ee8fc7aff17cd9c041c804ea3acae6c73f9803351a0a65ae771ab51cca6d  node-v18.3.0-linux-ppc64le.tar.gz
0adc1a1082fb179d8688e99a196a92c7cdf3e60f94bc9b409aad02948e296a75  node-v18.3.0-linux-ppc64le.tar.xz
cd5243d689d0748d0c0f504040b4bf18b3b1bbb8fb26cf0f0addd0c3ed49f35c  node-v18.3.0-linux-s390x.tar.gz
c5896ba74636c33986fb703aedf5421c59eee037819480122ddfd6e04dba26ed  node-v18.3.0-linux-s390x.tar.xz
9b64ed313363872f83f6586ad985e793258c5ba6e569812b9dd349ec819956cf  node-v18.3.0-linux-x64.tar.gz
e374f0e7726fd36e33846f186c3d17e41f7d62758e9af72c379b6583e73ffd48  node-v18.3.0-linux-x64.tar.xz
e4a9a4d364ebd41e7910f83f520211593667425b9cf399bf369dae79cee5e910  node-v18.3.0.pkg
5bb594347e84be8e1a900ae290762e439ac283a34a5821c209c19446111bba97  node-v18.3.0.tar.gz
3f694a81626e5057cda57898e771d213cfc5a649855f4cf1c6f6cd150c530625  node-v18.3.0.tar.xz
588e579934780d7260b8aba6e80576b208f951eb75872abcb0f1a0b757698916  node-v18.3.0-win-x64.7z
7c38bf820817deeafd9242ad56b30edecb02d694177c7811a89c71d3bdb1c64d  node-v18.3.0-win-x64.zip
2b8627704923f271037297b5c616012140115ccda844833bf5f308d8dd17fc17  node-v18.3.0-win-x86.7z
8cd47e81eff333bb4dbc193bafa2e471538e9a0341590409e463a72a71f09481  node-v18.3.0-win-x86.zip
9655750cdc909abd3c199d9b1bfb9032acd7dcdf3677e9850d07050dedb8b50d  node-v18.3.0-x64.msi
76aa64c4224def1e20d41c34213a4915266c6e8338eaf5fc6e59827178eb0926  node-v18.3.0-x86.msi
25f840c688171e891f6efad8ed098f7b2cb1704da22bb5ae9bef38a8f0ba1c7a  win-x64/node.exe
8dd17e07475a098640979532b8ad77896812605b13c4d715a8aa8804833494b4  win-x64/node.lib
a14c50c2b4a09220f5e6b3eb2dfe67cc0d40050c3086617600213627e053df2a  win-x64/node_pdb.7z
bfb3828a9de1e96ba641401167978c67de0dd56fdb453e0627d740b44c60764d  win-x64/node_pdb.zip
cdc6137e50cd1ffa993cfa2e31f51bc131c82fb7de28d6846269b75fdde85738  win-x86/node.exe
333bd4a83c23547ac8f5f250e6ff171e03919cfa3293b438de0e0baa5976767d  win-x86/node.lib
1a761e0f7551544dbe55fec1c8e67fff6c4d87f7719c2e195ad0ac0d10053ba5  win-x86/node_pdb.7z
4a43edb49304af66c38c0e3f1284c7c38e06890af770fd13517d80b9b9f8a96b  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEFB8HWVt7P/50MJqTdAVTO+V8fVcFAmKYLeoACgkQdAVTO+V8
fVcL9hAAqCKPV1oigqOftJEPGZYXayDV+FSl7n7gsY7aEMlRWpol49wYOyHAoysY
3h2sb0sUlWtdKZ/xYtDZvETY09JMHaQuYQsjxqQ5SDy0LmS1LheSJ5/rM/uBE2O2
eHX5MG2RP6G5eKK1V2Wj3ZxMt/UHCW43jX/3R/TlmGXtq+AtR+hd+XIH7HeaQ6Re
864d1ikWtkUtPl1Uvw/+tsBG9hPuvN8RRTYuz/lBXFrgsUl6tRfjZx2JXtwKtvG6
K4BfQhGUCP3KM6liiOVW678PNUIR8m+PwnyqB6h5FbyMp2Qa0GCPfwRejrwnRMrt
1uZ2/NU/cIna/sAqxYvgjhnFiIVSdSieQn1ogJOgevDjd7j+SRy3kN8RNmCZEtHd
r++IQwChQtJaDvvU0sShjjGKl3bRqj0GUFYNfxG6oeZePE9vsdB/5j4PaVh+XfWa
Xsbz5EZXUZA7LNaX4dB0d0Gw6DzvrK3ZYEpc3e6hXNKCce5ID8IADyp6TSGpU4oI
ymrhSGiLYdJ+l1fWGI+p2AKfjADaOxggwzXNqxh9kcC21i6LCFGllPtFogriUqbQ
ZPAMUd+D49feKyWBXJK6FPehsf+aY7A18L0+7IyiOsn+RGebTS64gVF6KmpNTVqW
pvJsPLftpAqGQXV7cQ0nYMOgvzM7utUAjNXKMP/Wcb/lGekNzr0=
=7D6Y
-----END PGP SIGNATURE-----

```
