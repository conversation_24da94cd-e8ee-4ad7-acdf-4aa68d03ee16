---
date: '2018-12-26T16:29:25.982Z'
category: release
title: Node v6.16.0 (LTS)
layout: blog-post
author: <PERSON><PERSON>
---

The 6.15.0 security release introduced some unexpected breakages on the 6.x release line.
This is a special release to fix a regression in the HTTP binary upgrade response body and add
a missing CLI flag to adjust the max header size of the http parser.

### Notable Changes

- **cli**:
  - add --max-http-header-size flag (cjihrig) [#24811](https://github.com/nodejs/node/pull/24811)
- **http**:
  - add maxHeaderSize property (cjihrig) [#24860](https://github.com/nodejs/node/pull/24860)

### Commits

- [[`f233b160c9`](https://github.com/nodejs/node/commit/f233b160c9)] - **(SEMVER-MINOR)** **cli**: add --max-http-header-size flag (cjihrig) [#24811](https://github.com/nodejs/node/pull/24811)
- [[`59f83d6896`](https://github.com/nodejs/node/commit/59f83d6896)] - **(SEMVER-MINOR)** **deps**: cherry-pick http_parser_set_max_header_size (cjihrig) [#24811](https://github.com/nodejs/node/pull/24811)
- [[`c0c4de71f0`](https://github.com/nodejs/node/commit/c0c4de71f0)] - **(SEMVER-MINOR)** **http**: add maxHeaderSize property (cjihrig) [#24860](https://github.com/nodejs/node/pull/24860)
- [[`8a3e0c0697`](https://github.com/nodejs/node/commit/8a3e0c0697)] - **http**: fix regression of binary upgrade response body (Matteo Collina) [#25036](https://github.com/nodejs/node/pull/25036)

Windows 32-bit Installer: https://nodejs.org/dist/v6.16.0/node-v6.16.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v6.16.0/node-v6.16.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v6.16.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v6.16.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v6.16.0/node-v6.16.0.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v6.16.0/node-v6.16.0-darwin-x64.tar.gz \
Linux 32-bit Binary: https://nodejs.org/dist/v6.16.0/node-v6.16.0-linux-x86.tar.xz \
Linux 64-bit Binary: https://nodejs.org/dist/v6.16.0/node-v6.16.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v6.16.0/node-v6.16.0-linux-ppc64le.tar.xz \
Linux PPC BE 64-bit Binary: https://nodejs.org/dist/v6.16.0/node-v6.16.0-linux-ppc64.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v6.16.0/node-v6.16.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v6.16.0/node-v6.16.0-aix-ppc64.tar.gz \
SmartOS 32-bit Binary: https://nodejs.org/dist/v6.16.0/node-v6.16.0-sunos-x86.tar.xz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v6.16.0/node-v6.16.0-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v6.16.0/node-v6.16.0-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v6.16.0/node-v6.16.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v6.16.0/node-v6.16.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v6.16.0/node-v6.16.0.tar.gz \
Other release files: https://nodejs.org/dist/v6.16.0/ \
Documentation: https://nodejs.org/docs/v6.16.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

2defd3ab3f4628cade7d4b415be013fa0ad8e6ec4ee7da556affc3209b6eb0a4  node-v6.16.0-aix-ppc64.tar.gz
12167a8d26f323191b79e37cc9ab042b929ddd5bec4210aa9bd0dbf2c6a3bc5d  node-v6.16.0-darwin-x64.tar.gz
9767fe05ffd33ce42070c56cb61a12c73a6d886ba32fbbbbedb2ca90e0bba525  node-v6.16.0-darwin-x64.tar.xz
f799b143375a0f9d70dac394da0ffd201657c911fe16a0341ff0687af2ce5cdc  node-v6.16.0-headers.tar.gz
f5449926ce150c386090542b3e9ee369b19bc6148ab2f552e9fcb1024f14391e  node-v6.16.0-headers.tar.xz
6b94c3c0e807f5350f4e973cece77f373d637f7d7c3c24f90e583407beee916a  node-v6.16.0-linux-arm64.tar.gz
10507ebca2f736064dd325854f5b2e1f60f24b2e6a78fadcd7933d8edb978b70  node-v6.16.0-linux-arm64.tar.xz
a70487b82e4a50ea0a8e10b5b6f922d52a870b15a5e34a8102d93e0765ea8ee1  node-v6.16.0-linux-armv7l.tar.gz
fe3b6d712c1b762ed35782c2f4fc4977711b61435998b89850dad309e38eb0bb  node-v6.16.0-linux-armv7l.tar.xz
1c6c30d8d795f8d888526ae97e3dfd0a332fdbf2e703f1696679879fda8a1c62  node-v6.16.0-linux-ppc64le.tar.gz
30085079ee1c039e04bf6533023fe62191ee46d19ddc999f5593324177d12fa4  node-v6.16.0-linux-ppc64le.tar.xz
6f2a3f7713a05ec726af209d5bfe7945c5be6d99a2e2f4a561301c36f5998db8  node-v6.16.0-linux-ppc64.tar.gz
5fb8ef8cd31b15c03101c6e4d04e11accf1ae1b34aeae9007d1cb6a7a51a27d2  node-v6.16.0-linux-ppc64.tar.xz
58d90689ca1d41843532ef098b91e1860530f8a4c131f498b46facecce492c5f  node-v6.16.0-linux-s390x.tar.gz
5edd1552bf369bc7eb9643b479d12a25e04a605b8176add2ead7e99802014b43  node-v6.16.0-linux-s390x.tar.xz
7f26cd9a2845df23773755a428d61b74fd80d48a991e964d12e85ae90ced81a0  node-v6.16.0-linux-x64.tar.gz
56c701b19777ffd122832ead132bf0590c9b6280a5cabe19e7642441167f7262  node-v6.16.0-linux-x64.tar.xz
93e47d1bc0dcbe98288247302c65708104d882cc61fe7d0ce2d68a78cdd555db  node-v6.16.0-linux-x86.tar.gz
d35a33dcf043d7b30228d423e3a295be7aa9d4fb07f647acf2442cd53d9edcb1  node-v6.16.0-linux-x86.tar.xz
af45957ea17e5358eaa361476648817a4d68e7ae7d1e8f7f0b097cf02f389757  node-v6.16.0.pkg
80bc70012bce0a95284ac09045edb937f3c2da61d7d76f952cb4ccd280b81b44  node-v6.16.0-sunos-x64.tar.gz
6141555dd7bb1fd2d4df7d91121b9c4750d027c2ef9f64d6ea65ebad83866acf  node-v6.16.0-sunos-x64.tar.xz
4fc09dfcd0cf5b18db3c829de9703968c9b3e5b806fe51d05ee59aa1f105527f  node-v6.16.0-sunos-x86.tar.gz
4b2e2b827d0e1c044d436cdd231829aa0dc333fdfb386bd9b74295fdc5d75556  node-v6.16.0-sunos-x86.tar.xz
5432c6cba59bfef5794951193e93dbbd1707960b6c722925afcdb4517f4dc742  node-v6.16.0.tar.gz
0d0882a9da1ccc217518d3d1a60dd238da9f52bed0c7daac42b8dc3d83bd7546  node-v6.16.0.tar.xz
7e5c93340e966a16bd4659bd827867d3116611b55e9c194c00a7a578830f917e  node-v6.16.0-win-x64.7z
03807861d364e0a2b09f475aa073022b49b277a51bd79cd255cf3c37611354e7  node-v6.16.0-win-x64.zip
734dd3b3baba0f142904c79b757abc841eb6d8ff5b0109ce8caa4aeffed2d1ea  node-v6.16.0-win-x86.7z
885d6316b4852472cfb04a4fb7dad9f5ae0f08e3b3fd3f554c893b0b871e9f0b  node-v6.16.0-win-x86.zip
cd58a0467828c34a59aa0ab8d10099cf928cb30adcf313bda8ec08939e91e56a  node-v6.16.0-x64.msi
5e2424b372a40db42775e747a98e11979bc0dfc7bd3d1d87a49f9e19f087bf93  node-v6.16.0-x86.msi
e7532234c07e6a0d90842fbe530daad29e3000a4282514948846b3cb905d3e53  win-x64/node.exe
7524268fe5d2756a31efb62851f4fc7863ace64c3e08245bcd93d24612be9274  win-x64/node.lib
43b3fabc7c5f70412660aae27cf3928b2bc09a46c0b467e8f216135bb32bbd93  win-x64/node_pdb.7z
cf08f786f1d2276e3d11114bf9686f9c1e115f6d9f00db44510bd5f3969f5695  win-x64/node_pdb.zip
75d6d30a32afd0a6304441ac74e1099b31c0e5d8435f389b9c956afbd64b2be2  win-x86/node.exe
e42ded01468ee7d3cc68fb77c13430a08ed7502dfc90c1edda65c82ea3cd913e  win-x86/node.lib
eea89a8e40707628135e6f7473da3f38109d02aad21798c2c820916833c6675a  win-x86/node_pdb.7z
485817339eb96dd33ea7b2a5547942e4108a4c5b3f8821e700013bc289976407  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQEzBAEBCAAdFiEEDv/hvO/ZyE49CYFSkzsB9AtcqUYFAlwjrAsACgkQkzsB9Atc
qUZnVAgAuIkxxAnu0K/ee/cMm/YFJ1+SwW2jdRxzQS0YfxEWkV2xVnTDhs8ZYmYB
r/b3lJ13yscYRY626lhhuv0CMKC38018FnwAenzvAZ1gBQtLXwVnFPwtwGZ8njLg
MFzJmcGAE6LP7xgsq9pkmupnNt6uQ4J9Muet7TabmKFksbVHUpg0YWZZNfFZfEh2
V7evE0AiPM+LNpETdlk0+gpT1iVRxAib1ORXs+to1fAWAocy46d4DS4yBEwfe6U5
TerYs6lBl5aLkQL0lIV814lfeggG/8DFBzzMON7l4DQLpMvVuTR9y+dBHkKDSzkt
YFrY4Omy/w12XKIdpPjxLM1EOuDNuQ==
=2ePF
-----END PGP SIGNATURE-----

```
