---
date: '2012-12-21T20:30:15.000Z'
category: release
title: Node v0.9.4 (Unstable)
layout: blog-post
author: The Node.js Project
---

2012.12.21, Version 0.9.4 (Unstable)

- streams: Update all streaming interfaces to use new classes (isaacs)

- node: remove idle gc (<PERSON>)

- http: protect against response splitting attacks (<PERSON>)

- fs: Raise error when null bytes detected in paths (isaacs)

- fs: fix 'object is not a function' callback errors (<PERSON>)

- fs: add autoClose=true option to fs.createReadStream (Farid Neshat)

- process: add getgroups(), setgroups(), initgroups() (<PERSON>)

- openssl: optimized asm code on x86 and x64 (<PERSON>)

- crypto: fix leak in GetPeerCertificate (Fedor Indutny)

- add systemtap support (<PERSON>)

- windows: add ETW and PerfCounters support (<PERSON>)

- windows: fix normalization of UNC paths (<PERSON>)

- crypto: fix ssl error handling (<PERSON>)

- node: remove eio-emul.h (<PERSON>)

- os: add os.endianness() function (<PERSON>)

- readline: don't emit "line" events with a trailing 'n' char (<PERSON>lich)

- build: add configure option to generate xcode build files (Timothy J Fontaine)

- build: allow linking against system libuv, cares, http_parser (Stephen Gallagher)

- typed arrays: add slice() support to ArrayBuffer (Anthony Pesch)

- debugger: exit and kill child on SIGTERM or SIGHUP (Fedor Indutny)

- url: url.format escapes delimiters in path and query (J. Lee Coltrane)

Source Code: https://nodejs.org/dist/v0.9.4/node-v0.9.4.tar.gz

Macintosh Installer (Universal): https://nodejs.org/dist/v0.9.4/node-v0.9.4.pkg

Windows Installer: https://nodejs.org/dist/v0.9.4/node-v0.9.4-x86.msi

Windows x64 Installer: https://nodejs.org/dist/v0.9.4/x64/node-v0.9.4-x64.msi

Windows x64 Files: https://nodejs.org/dist/v0.9.4/x64/

Linux 32-bit Binary: https://nodejs.org/dist/v0.9.4/node-v0.9.4-linux-x86.tar.gz

Linux 64-bit Binary: https://nodejs.org/dist/v0.9.4/node-v0.9.4-linux-x64.tar.gz

Solaris 32-bit Binary: https://nodejs.org/dist/v0.9.4/node-v0.9.4-sunos-x86.tar.gz

Solaris 64-bit Binary: https://nodejs.org/dist/v0.9.4/node-v0.9.4-sunos-x64.tar.gz

Other release files: https://nodejs.org/dist/v0.9.4/

Website: https://nodejs.org/docs/v0.9.4/

Documentation: https://nodejs.org/docs/v0.9.4/api/

Shasums:

```
7919d5fa63583c9a38a8ade1e977809b53476f7f  node-v0.9.4-darwin-x64.tar.gz
82eb2f39c61b14be8a9d2c1b6ba624030ab3ddbe  node-v0.9.4-darwin-x86.tar.gz
34cd2f6bed15d4d7d28944575b7098f4500651e2  node-v0.9.4-linux-x64.tar.gz
bf5da5432a5d80343e1d53c57b242283be11c97f  node-v0.9.4-linux-x86.tar.gz
6eebcf27a545d598c0ffa8056d7d5c83add5746c  node-v0.9.4-sunos-x64.tar.gz
1c2e28389b34a0e51f5ad2a1c09677f2941765b1  node-v0.9.4-sunos-x86.tar.gz
4d7b4dfd150f17dac787400e74fc469b3432f3a2  node-v0.9.4-x86.msi
20ceaafeb6d4ecf5d2f37d3d825d47cc81927dd6  node-v0.9.4.pkg
30b3f8dee0b2ace3d83c200eeffc6f450d3b4366  node-v0.9.4.tar.gz
a5b4304f4a50ac3385577f7833890ca6f44301c0  node.exe
5b960053a6bddfd10cf6408ea1581b7237512248  node.exp
f1aaac71a21e7a0c884211957e5d79bed1deb1d3  node.lib
73016ab15ee7ee6f6363391d3e41a698ed764e65  node.pdb
5a624fc9a5dac85a6f00945ccc2d31d9b8a51499  x64/node-v0.9.4-x64.msi
fe738e969f9b050b7b1675ecf3cda567021e121b  x64/node.exe
d32c7ea441ad0d24cd2fd2b4932d2924a95f25ec  x64/node.exp
753f7fadc501a1b86d94af23710137f6e93c5cff  x64/node.lib
a707ef46e6483f40a30c85c037eeadbf9e5923b8  x64/node.pdb
```
