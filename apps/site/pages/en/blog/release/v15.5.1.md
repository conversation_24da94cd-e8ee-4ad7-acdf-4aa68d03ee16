---
date: '2021-01-04T18:15:24.205Z'
category: release
title: Node v15.5.1 (Current)
layout: blog-post
author: <PERSON>
---

### Notable changes

Vulnerabilities fixed:

- **CVE-2020-8265**: use-after-free in TLSWrap (High)

  - Affected Node.js versions are vulnerable to a use-after-free bug in
    its TLS implementation. When writing to a TLS enabled socket,
    node::StreamBase::Write calls node::TLSWrap::DoWrite with a freshly
    allocated WriteWrap object as first argument. If the DoWrite method
    does not return an error, this object is passed back to the caller as
    part of a StreamWriteResult structure. This may be exploited to
    corrupt memory leading to a Denial of Service or potentially other
    exploits.

- **CVE-2020-8287**: HTTP Request Smuggling in nodejs (Low)
  - Affected versions of Node.js allow two copies of a header field in
    a http request. For example, two Transfer-Encoding header fields. In
    this case Node.js identifies the first header field and ignores the
    second. This can lead to HTTP Request Smuggling
    (https://cwe.mitre.org/data/definitions/444.html).

### Commits

- [[`c5dbe831b7`](https://github.com/nodejs/node/commit/c5dbe831b7)] - **http**: add test for http transfer encoding smuggling (Matteo Collina) [nodejs-private/node-private#228](https://github.com/nodejs-private/node-private/pull/228)
- [[`e0c9a2285c`](https://github.com/nodejs/node/commit/e0c9a2285c)] - **http**: unset `F_CHUNKED` on new `Transfer-Encoding` (Matteo Collina) [nodejs-private/node-private#228](https://github.com/nodejs-private/node-private/pull/228)
- [[`9834ef85a0`](https://github.com/nodejs/node/commit/9834ef85a0)] - **src**: retain pointers to WriteWrap/ShutdownWrap (James M Snell) [nodejs-private/node-private#23](https://github.com/nodejs-private/node-private/pull/23)

Windows 32-bit Installer: https://nodejs.org/dist/v15.5.1/node-v15.5.1-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v15.5.1/node-v15.5.1-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v15.5.1/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v15.5.1/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v15.5.1/node-v15.5.1.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v15.5.1/node-v15.5.1-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v15.5.1/node-v15.5.1-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v15.5.1/node-v15.5.1-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v15.5.1/node-v15.5.1-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v15.5.1/node-v15.5.1-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v15.5.1/node-v15.5.1-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v15.5.1/node-v15.5.1-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v15.5.1/node-v15.5.1.tar.gz \
Other release files: https://nodejs.org/dist/v15.5.1/ \
Documentation: https://nodejs.org/docs/v15.5.1/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

1b56c38c36efb3d23c37fd7b2a46660ca06963d47dfd9160befb80e508169a48  node-v15.5.1-aix-ppc64.tar.gz
4507dab0481b0b5374b5758b1eba7d105c8cbcb173548119b04d9ef7d9f1d40f  node-v15.5.1-darwin-x64.tar.gz
756a2e96714580150e95bffccfff44e0797b634693291660d7320a280639169a  node-v15.5.1-darwin-x64.tar.xz
5c3d3f1ccc494cbdeace332301d94e60b667fe9982c6c0140faa5e836140a463  node-v15.5.1-headers.tar.gz
c8fed73644c3550665e2a02b348b624ff91e67013e8797de8b7cdf4f70df7989  node-v15.5.1-headers.tar.xz
a2d14db86c6f8a070f227940ea44a3409966f6bed14df0ec6f676fe2e2f601c9  node-v15.5.1-linux-arm64.tar.gz
b431a81ba4729233d686c922690b2d355381b1dd83b1fc486c4a27683ac15649  node-v15.5.1-linux-arm64.tar.xz
9c660bcf3143e07a0c192d89c0dcf8dbd1a4b90088bdf04d37dfa71b480866ca  node-v15.5.1-linux-armv7l.tar.gz
cc2084f85eab8c6bf8db8a96ab443886b6461ad1b8fba170d71c17eefc210507  node-v15.5.1-linux-armv7l.tar.xz
6c6dc15f4701bba28062cbec852a297f266e5068249911dda3c59199cd58de32  node-v15.5.1-linux-ppc64le.tar.gz
301ac66d16f692ee2cd2bb4d18b8fb5f8eb25aa474d67b5be5a84472f19af246  node-v15.5.1-linux-ppc64le.tar.xz
e05f949ea11e2aafc08a7972c0f41a11a3628762e857d44965e0605d3bcd143f  node-v15.5.1-linux-s390x.tar.gz
fa77245208b8f6fe1f40cc1b067bf08c1e33f857a328e78ededdc6ba1f016bae  node-v15.5.1-linux-s390x.tar.xz
8dd81dbd63082b24c9a1f16baf4ce743c0c8dff1e589b634119d6ebfca54457e  node-v15.5.1-linux-x64.tar.gz
dbc41a611d99aedf2cfd3d0acc50759a6b9084c7447862e990f51958d4a7aa41  node-v15.5.1-linux-x64.tar.xz
a8548daf881b81f08151fecbe059f49d1fbd8437da53f74a18fb071df23642e3  node-v15.5.1.pkg
9730d3099c051bd8733b3e6d62c54ba9b2d82ec40e6c65ee966ba3f346ff4157  node-v15.5.1.tar.gz
2c229acc2d4d47a872f0401c1dc4fa92d72317ca867609a3402a78fd78236b61  node-v15.5.1.tar.xz
ac0aa1f4dc266327bf133453d2e1c3f453d320e98134d690624c6d9931db86d8  node-v15.5.1-win-x64.7z
e1f826f9647fc7058b48c669991956a427fe4b6ccefa415a18b41715483f958d  node-v15.5.1-win-x64.zip
7ffa9b4e9c123905768c9ac19ef2746c41faf80e9ad75045a1cb963eac8db75e  node-v15.5.1-win-x86.7z
863aee4a2e3b675c7730f5946ffda20040b21afe2e0f5f0f873792e79d601adf  node-v15.5.1-win-x86.zip
aa745e356417c1ef1394ee583779f8dbb7e052d22349cb211bc692b562988873  node-v15.5.1-x64.msi
3b02fa7ef25bcc1b109e31fc2ce7c4afa95e084562ca3f66b299705b032cfe60  node-v15.5.1-x86.msi
f6f7093d3d1bdd231cd1580f1c4865f61bc1487d454852efe20fceba107aa1fb  win-x64/node.exe
e5082525203684fd34553f9a26c12d722a1a06ffeac8def760b5fea6227d167e  win-x64/node.lib
b17fc6d85709a269c5f869001b5d8a2ea1bba99dd4d99b1b77971555c30c3357  win-x64/node_pdb.7z
089ac9e1d64daaf8ffa1cec95a9dac00655a93a78754e9fad9ea16d89bf818d3  win-x64/node_pdb.zip
61cdbb7e974d3e903db9dc319010e8d1c0a2c0de84143929ede0a896c3209d80  win-x86/node.exe
844ed42e7cacc6443224b880400c7f884bd058e07dc44e52d871ac5ea736e979  win-x86/node.lib
b1d293f5ecca1d7faabf26051a0722a0e058dc25d058cc2864e8e40717bee499  win-x86/node_pdb.7z
1bc218b4321322771ce28d7a8d365b6b950cc3de3fe9f71820f560fd7c0e78e4  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQEzBAEBCAAdFiEETtd49TnjY0x3nIfG1wYoSKGrAFwFAl/zWtIACgkQ1wYoSKGr
AFzIIggAqw43M7hp8Yo1m7wtumKbG0kU0OFP6oOAV7MLNNtRI6vAYtSl+ZwYa8GH
6cPHbLN9YY5XVOaO4HWKtIfUT/TWQlFFQv8Sn80qIy3+qykV813h3LGUyU/cPI+H
jjC5LUd7Vt2CbEkb0uVeV83W71yF/+4dff9dIAWhTw2+O9hpQLNh8TJ9xGZ+2JLG
kcC2zyGF9Ot5PdMnU6/2J1JSrBOjKz0ShlSCqboUvsEkCoaYTn2dI2m8sMfUbM4G
HOejqww+tHGyqBYyZvlIcQP0beTNXy8XiyOl4kF2TGrkML78oxm34BgtCOyuDdDZ
ZAjAgeX/lCVPTMLifAYETaCWeRJPeA==
=KtB7
-----END PGP SIGNATURE-----

```
