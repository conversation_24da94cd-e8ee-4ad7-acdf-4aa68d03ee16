---
date: '2021-10-20T20:26:21.675Z'
category: release
title: Node v17.0.1 (Current)
layout: blog-post
author: <PERSON><PERSON><PERSON>
---

### Notable Changes

#### Fixed distribution for native addon builds

This release fixes an issue introduced in Node.js v17.0.0, where some V8 headers
were missing from the distributed tarball, making it impossible to build native
addons. These headers are now included. [#40526](https://github.com/nodejs/node/pull/40526)

#### Fixed stream issues

- Fixed a regression in `stream.promises.pipeline`, which was introduced in version
  16.10.0, is fixed. It is now possible again to pass an array of streams to the
  function. [#40193](https://github.com/nodejs/node/pull/40193)
- Fixed a bug in `stream.Duplex.from`, which didn't work properly when an async
  generator function was passed to it. [#40499](https://github.com/nodejs/node/pull/40499)

### Commits

- [[`3f033556c3`](https://github.com/nodejs/node/commit/3f033556c3)] - **build**: include missing V8 headers in distribution (<PERSON><PERSON><PERSON>) [#40526](https://github.com/nodejs/node/pull/40526)
- [[`adbd92ef1d`](https://github.com/nodejs/node/commit/adbd92ef1d)] - **crypto**: avoid double free (Michael Dawson) [#40380](https://github.com/nodejs/node/pull/40380)
- [[`8dce85aadc`](https://github.com/nodejs/node/commit/8dce85aadc)] - **doc**: format doc/api/\*.md with markdown formatter (Rich Trott) [#40403](https://github.com/nodejs/node/pull/40403)
- [[`977016a72f`](https://github.com/nodejs/node/commit/977016a72f)] - **doc**: specify that maxFreeSockets is per host (Luigi Pinca) [#40483](https://github.com/nodejs/node/pull/40483)
- [[`f9f2442739`](https://github.com/nodejs/node/commit/f9f2442739)] - **src**: add missing inialization in agent.h (Michael Dawson) [#40379](https://github.com/nodejs/node/pull/40379)
- [[`111f0bd9b6`](https://github.com/nodejs/node/commit/111f0bd9b6)] - **stream**: fix fromAsyncGen (Robert Nagy) [#40499](https://github.com/nodejs/node/pull/40499)
- [[`b84f101049`](https://github.com/nodejs/node/commit/b84f101049)] - **stream**: support array of streams in promises pipeline (Mestery) [#40193](https://github.com/nodejs/node/pull/40193)
- [[`3f7c503b69`](https://github.com/nodejs/node/commit/3f7c503b69)] - **test**: adjust CLI flags test to ignore blank lines in doc (Rich Trott) [#40403](https://github.com/nodejs/node/pull/40403)
- [[`7c42d9fcc6`](https://github.com/nodejs/node/commit/7c42d9fcc6)] - **test**: split test-crypto-dh.js (Joyee Cheung) [#40451](https://github.com/nodejs/node/pull/40451)

Windows 32-bit Installer: https://nodejs.org/dist/v17.0.1/node-v17.0.1-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v17.0.1/node-v17.0.1-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v17.0.1/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v17.0.1/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v17.0.1/node-v17.0.1.pkg \
macOS Apple Silicon 64-bit Binary: https://nodejs.org/dist/v17.0.1/node-v17.0.1-darwin-arm64.tar.gz \
macOS Intel 64-bit Binary: https://nodejs.org/dist/v17.0.1/node-v17.0.1-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v17.0.1/node-v17.0.1-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v17.0.1/node-v17.0.1-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v17.0.1/node-v17.0.1-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v17.0.1/node-v17.0.1-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v17.0.1/node-v17.0.1-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v17.0.1/node-v17.0.1-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v17.0.1/node-v17.0.1.tar.gz \
Other release files: https://nodejs.org/dist/v17.0.1/ \
Documentation: https://nodejs.org/docs/v17.0.1/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

fa09aa43637816e27d240d551416c8b32d5503e1a6371d01fe60d3700e146ec3  node-v17.0.1-aix-ppc64.tar.gz
b49c65be9112f7e5de4e39f4f01e541ee73b3d28d3e2bbd3ea85a86952d0dc2d  node-v17.0.1-darwin-arm64.tar.gz
d004ac6f115c23c2686b7bd8ae2d072b5e8b49a0c09b5eaf85c0ba5931fee35e  node-v17.0.1-darwin-arm64.tar.xz
0dfe6f904f3f20652e3d34c60885b790603f120d5d51a53031355827a4eaf6a9  node-v17.0.1-darwin-x64.tar.gz
27614e262c2aa7863a5fbf82a11b4eca4706c34897ad974fa91be5e38e220af7  node-v17.0.1-darwin-x64.tar.xz
156000451f0a7c058bb67d30e1b4fde624557f234d2e238042a43d3959486b96  node-v17.0.1-headers.tar.gz
9d450c41f9f4dc9ae237bc8e409389c7ea5cd2374bc86ee44a34434b492fe35b  node-v17.0.1-headers.tar.xz
b993eccc0d493065ba1e4b30e189bae43b9d7aba587625eed891125f316e1333  node-v17.0.1-linux-arm64.tar.gz
6cbd83ba5778a1af740a152839026cbd068610ec6e5ebf67739e546eba426171  node-v17.0.1-linux-arm64.tar.xz
e9e6bf1263b549576519aec2fb4b5efaf3513f0c00de5c2b25dd0e3dd09ecbd0  node-v17.0.1-linux-armv7l.tar.gz
1eae82da1d14257903d8087d4145c3f61970db36dcf8c4e7e646f13d33c799ee  node-v17.0.1-linux-armv7l.tar.xz
aaa1445b1d8b988196d6b3ce9ea795da8ff68acc20c665d0e16c33b5f558e853  node-v17.0.1-linux-ppc64le.tar.gz
3f5665b92bce8c81caf35d1b0e10f59594499c8e5afeb8a12e101dd7dc62e6ed  node-v17.0.1-linux-ppc64le.tar.xz
4c03f04c77c88dcf110ebc858e2140bf23081ad25f6f531634ca73ac38f54ad7  node-v17.0.1-linux-s390x.tar.gz
df8c44e3f10c14924a2b78530a6dd9e08557bc6694bc6342f18cd4fbdca30dfb  node-v17.0.1-linux-s390x.tar.xz
c2aaef730245ad180d2a2b9d2d2806feca57e93e0691faabb41175d26bed9c89  node-v17.0.1-linux-x64.tar.gz
30484910d6a25c96902f329c1fdfb753ddff9bf8c65a6e5ec5c818bac8135953  node-v17.0.1-linux-x64.tar.xz
f8a6ae27367af79ab0a6878203574123ba82b1e329dcc750da8662e370a646b6  node-v17.0.1.pkg
f06242a7958b89f1bc3c7070af1ff5e477a9d3b76d2348456617f87e8f4f6988  node-v17.0.1.tar.gz
6ec480f872cb7c34877044985e3d7bd89329ace5b8e2ad90b57980601786341c  node-v17.0.1.tar.xz
97d9f26136a1d793ae2bc62400876b36239d34d13b220029fa0b6183b4697b1e  node-v17.0.1-win-x64.7z
0b644e2499018884027a0fe5e0e159a18acd33e500c63a89898ba687189f7337  node-v17.0.1-win-x64.zip
7d4eaa5f80955a1a110a6bc56bcafacb6b12b59f09023022a85ccbec8f4f1dd1  node-v17.0.1-win-x86.7z
91c99bc60bdb71052d13119b9f78bde7a949ed58cd0f0f6f413d570860f6e384  node-v17.0.1-win-x86.zip
bf28fb5f946e7cc993e4dd35d7a0305bd2ff8d19cb80acae8b8f405d810f397f  node-v17.0.1-x64.msi
f1465db9bfe87f12911a277f8f47f76b69ef92bd8e10336beaa9ea19616b60ee  node-v17.0.1-x86.msi
9f85861d85abeeb5fbd1ef6cea6bc7b672bc66aa13643e609941ee84b9402596  win-x64/node.exe
95d44c197ebbc042dbed838ecde8f78c8902dc6d266605859f93c28d2051b3e0  win-x64/node.lib
7375b619ea62d36693235c7f22ba5ec66b394849003be6f706e0062a089bc922  win-x64/node_pdb.7z
7de2e8627898182c81424ba19b554f859b944d3a22c8bd9c8fbc27eb38acbf3b  win-x64/node_pdb.zip
24296cec10bff94bb31e1038a2cd7b9296e3ba8c000b27d4ae69eea69564c41d  win-x86/node.exe
708622a96033ea289f09421906e51a32d0cbfde560fb82a78498f3f5a7617152  win-x86/node.lib
082e71e8e3717642d43a4de832206b333192e21d5ea24a7d7304b588e0a4dfe0  win-x86/node_pdb.7z
0923dbe6af7787071b761def14e63d777c6a2082576f8c1ffa4d1ba752c481ad  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEj8yhP+8dDC6RAI4Jdw96mlrhVgAFAmFwewAACgkQdw96mlrh
VgA/8A/+KdkPqmx0iAbwti6CWjBxL2DSKhd3nritntzunSQTPQaLmeBa+0lhJ1g3
+LMa5kQOlMbXHl0Ve0jzy0ZgpO/JomdJziLkkSpjsrdYTv6arOxJjauVqOJPZZRY
81cMIxP281JWtowvwM1aoI/g2ecbOuQJCeqG4wVsMgjQWjNfbK84MtiRF/NfAh7W
t7iAgOpWHCWHXW0XR+MQApJQTeCOdlRazn88RgXm1cqpFpStoLhjdQSs+X1Enund
HVKrfd2BZiPeLlv4fMyjYp9F2t2Osn6yvJoLWewnBVveP9Z8m3iNZzRP+pIax5RB
e9hNJfDJzudW7ylz2VIO8V8gfTPUemoEV4H3LuIrZlrh6jdMVgUM+YbROZMxSbBC
ivkWwZ+T/gIbi+rOO0o/M5yn/5XIi44k8Sb9sQ9SCqE/8aWPyfjk6yYNsfzYJc2e
+Aeg8HscUqT/1/NzDw3Im+zUl145H96qbM5NruEqJrukTS4jYtA8CM/B28NoFh/c
6C36vMxlDJoRsLIongXaYZSn6LTfKPRx4HLpEVSjkUx5EGewIXsPuNmbgWlGVsmc
XvimmSDY6ARHSUbT315KTtwXQ9vQYW6iMvrMSVowMiB6MA7d6I9rhT0qwswxb+9x
r79QaSFQ0BgSURIoxtBCh0ZQigz32raJNF/s6Z6nkI64uJDRUis=
=QJX+
-----END PGP SIGNATURE-----

```
