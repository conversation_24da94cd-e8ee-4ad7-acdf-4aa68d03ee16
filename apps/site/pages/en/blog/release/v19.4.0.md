---
date: '2023-01-06T13:16:26.671Z'
category: release
title: Node v19.4.0 (Current)
layout: blog-post
author: <PERSON>
---

### Notable Changes

- **buffer**:
  - (SEMVER-MINOR) add buffer.isUtf8 for utf8 validation (<PERSON><PERSON><PERSON>) [#45947](https://github.com/nodejs/node/pull/45947)
- **http**:
  - (SEMVER-MINOR) improved timeout defaults handling (Paolo <PERSON>) [#45778](https://github.com/nodejs/node/pull/45778)
- **net**:
  - add autoSelectFamily global getter and setter (Paolo Insogna) [#45777](https://github.com/nodejs/node/pull/45777)
- **os**:
  - (SEMVER-MINOR) add availableParallelism() (<PERSON>) [#45895](https://github.com/nodejs/node/pull/45895)
- **util**:
  - add fast path for text-decoder fatal flag (<PERSON><PERSON><PERSON>) [#45803](https://github.com/nodejs/node/pull/45803)

### Commits

- \[[`54b748acc0`](https://github.com/nodejs/node/commit/54b748acc0)] - **async_hooks**: refactor to use `validateObject` (Deokjin Kim) [#46004](https://github.com/nodejs/node/pull/46004)
- \[[`cf2ff81f26`](https://github.com/nodejs/node/commit/cf2ff81f26)] - **benchmark**: include webstreams benchmark (Rafael Gonzaga) [#45876](https://github.com/nodejs/node/pull/45876)
- \[[`6e3d7f8c2d`](https://github.com/nodejs/node/commit/6e3d7f8c2d)] - **bootstrap**: optimize modules loaded in the built-in snapshot (Joyee Cheung) [#45849](https://github.com/nodejs/node/pull/45849)
- \[[`d181b76374`](https://github.com/nodejs/node/commit/d181b76374)] - **bootstrap**: make CJS loader snapshotable (Joyee Cheung) [#45849](https://github.com/nodejs/node/pull/45849)
- \[[`508e830765`](https://github.com/nodejs/node/commit/508e830765)] - **bootstrap**: include event_target into the built-in snapshot (Joyee Cheung) [#45849](https://github.com/nodejs/node/pull/45849)
- \[[`dd77c05480`](https://github.com/nodejs/node/commit/dd77c05480)] - **bootstrap**: support module_wrap binding in snapshot (Joyee Cheung) [#45849](https://github.com/nodejs/node/pull/45849)
- \[[`fbe399c75c`](https://github.com/nodejs/node/commit/fbe399c75c)] - **(SEMVER-MINOR)** **buffer**: add buffer.isUtf8 for utf8 validation (Yagiz Nizipli) [#45947](https://github.com/nodejs/node/pull/45947)
- \[[`233a66f937`](https://github.com/nodejs/node/commit/233a66f937)] - **build**: fix arm64 cross-compile from powershell (Stefan Stojanovic) [#45890](https://github.com/nodejs/node/pull/45890)
- \[[`e7b98a3da2`](https://github.com/nodejs/node/commit/e7b98a3da2)] - **build**: add option to disable shared readonly heap (Anna Henningsen) [#45887](https://github.com/nodejs/node/pull/45887)
- \[[`777c551edf`](https://github.com/nodejs/node/commit/777c551edf)] - **crypto**: ensure exported webcrypto EC keys use uncompressed point format (Ben Noordhuis) [#46021](https://github.com/nodejs/node/pull/46021)
- \[[`f7dba5bef7`](https://github.com/nodejs/node/commit/f7dba5bef7)] - **crypto**: fix globalThis.crypto this check (Filip Skokan) [#45857](https://github.com/nodejs/node/pull/45857)
- \[[`56f3ad101b`](https://github.com/nodejs/node/commit/56f3ad101b)] - **crypto**: fix CryptoKey prototype WPT (Filip Skokan) [#45857](https://github.com/nodejs/node/pull/45857)
- \[[`c9747f1140`](https://github.com/nodejs/node/commit/c9747f1140)] - **crypto**: use globalThis.crypto over require('crypto').webcrypto (Filip Skokan) [#45817](https://github.com/nodejs/node/pull/45817)
- \[[`6eede72241`](https://github.com/nodejs/node/commit/6eede72241)] - **crypto**: fix CryptoKey WebIDL conformance (Filip Skokan) [#45855](https://github.com/nodejs/node/pull/45855)
- \[[`c9802862b7`](https://github.com/nodejs/node/commit/c9802862b7)] - **crypto**: fix error when getRandomValues is called without arguments (Filip Skokan) [#45854](https://github.com/nodejs/node/pull/45854)
- \[[`3d09754186`](https://github.com/nodejs/node/commit/3d09754186)] - **debugger**: refactor console in lib/internal/debugger/inspect.js (Debadree Chatterjee) [#45847](https://github.com/nodejs/node/pull/45847)
- \[[`fdda2ff53b`](https://github.com/nodejs/node/commit/fdda2ff53b)] - **deps**: V8: cherry-pick 30861a39323d (Aaron Friel) [#45851](https://github.com/nodejs/node/pull/45851)
- \[[`71bf513062`](https://github.com/nodejs/node/commit/71bf513062)] - **deps**: patch V8 to *********** (Michaël Zasso) [#45996](https://github.com/nodejs/node/pull/45996)
- \[[`0552b13232`](https://github.com/nodejs/node/commit/0552b13232)] - **deps**: update simdutf to 2.0.9 (Node.js GitHub Bot) [#45975](https://github.com/nodejs/node/pull/45975)
- \[[`e73be1b3b9`](https://github.com/nodejs/node/commit/e73be1b3b9)] - **deps**: update to uvwasi 0.0.14 (Colin Ihrig) [#45970](https://github.com/nodejs/node/pull/45970)
- \[[`e4323f01c1`](https://github.com/nodejs/node/commit/e4323f01c1)] - **deps**: fix updater github workflow job (Yagiz Nizipli) [#45972](https://github.com/nodejs/node/pull/45972)
- \[[`05fee67238`](https://github.com/nodejs/node/commit/05fee67238)] - _**Revert**_ "**deps**: disable avx512 for simutf on benchmark ci" (Yagiz Nizipli) [#45948](https://github.com/nodejs/node/pull/45948)
- \[[`98fc94a444`](https://github.com/nodejs/node/commit/98fc94a444)] - **deps**: disable avx512 for simutf on benchmark ci (Yagiz Nizipli) [#45803](https://github.com/nodejs/node/pull/45803)
- \[[`344c5ec0ea`](https://github.com/nodejs/node/commit/344c5ec0ea)] - **deps**: add simdutf dependency (Yagiz Nizipli) [#45803](https://github.com/nodejs/node/pull/45803)
- \[[`7bdad948c8`](https://github.com/nodejs/node/commit/7bdad948c8)] - **deps**: V8: backport 8ca9f77d0f7c (Anna Henningsen) [#45871](https://github.com/nodejs/node/pull/45871)
- \[[`29f90cf5af`](https://github.com/nodejs/node/commit/29f90cf5af)] - **deps**: update timezone to 2022g (Node.js GitHub Bot) [#45731](https://github.com/nodejs/node/pull/45731)
- \[[`99fec0bf64`](https://github.com/nodejs/node/commit/99fec0bf64)] - **deps**: update undici to 5.14.0 (Node.js GitHub Bot) [#45812](https://github.com/nodejs/node/pull/45812)
- \[[`faee973fa7`](https://github.com/nodejs/node/commit/faee973fa7)] - **deps**: V8: cherry-pick bc831f8ba33b (Yagiz Nizipli) [#45788](https://github.com/nodejs/node/pull/45788)
- \[[`e2944109c6`](https://github.com/nodejs/node/commit/e2944109c6)] - **deps**: V8: cherry-pick bf0bd4868dde (Michaël Zasso) [#45908](https://github.com/nodejs/node/pull/45908)
- \[[`e113d169ee`](https://github.com/nodejs/node/commit/e113d169ee)] - **doc**: update isUtf8 description (Yagiz Nizipli) [#45973](https://github.com/nodejs/node/pull/45973)
- \[[`9e16406066`](https://github.com/nodejs/node/commit/9e16406066)] - **doc**: sort http.createServer() options alphabetically (Luigi Pinca) [#45680](https://github.com/nodejs/node/pull/45680)
- \[[`49253e1a8f`](https://github.com/nodejs/node/commit/49253e1a8f)] - **doc**: use console.error for error case in timers and tls (Deokjin Kim) [#46002](https://github.com/nodejs/node/pull/46002)
- \[[`8be1b666a7`](https://github.com/nodejs/node/commit/8be1b666a7)] - **doc**: fix wrong output of example in `url.protocol` (Deokjin Kim) [#45954](https://github.com/nodejs/node/pull/45954)
- \[[`9251dce8b2`](https://github.com/nodejs/node/commit/9251dce8b2)] - **doc**: use `os.availableParallelism()` in async_context and cluster (Deokjin Kim) [#45979](https://github.com/nodejs/node/pull/45979)
- \[[`952e03ae66`](https://github.com/nodejs/node/commit/952e03ae66)] - **doc**: make EventEmitterAsyncResource's `options` as optional (Deokjin Kim) [#45985](https://github.com/nodejs/node/pull/45985)
- \[[`71cc3b3712`](https://github.com/nodejs/node/commit/71cc3b3712)] - **doc**: replace single executable champion in strategic initiatives doc (Darshan Sen) [#45956](https://github.com/nodejs/node/pull/45956)
- \[[`eaf6b63637`](https://github.com/nodejs/node/commit/eaf6b63637)] - **doc**: update error message of example in repl (Deokjin Kim) [#45920](https://github.com/nodejs/node/pull/45920)
- \[[`d8b5b7da75`](https://github.com/nodejs/node/commit/d8b5b7da75)] - **doc**: fix typos in packages.md (Eric Mutta) [#45957](https://github.com/nodejs/node/pull/45957)
- \[[`4457e051c9`](https://github.com/nodejs/node/commit/4457e051c9)] - **doc**: remove port from example in `url.hostname` (Deokjin Kim) [#45927](https://github.com/nodejs/node/pull/45927)
- \[[`908f4fab52`](https://github.com/nodejs/node/commit/908f4fab52)] - **doc**: show output of example in http (Deokjin Kim) [#45915](https://github.com/nodejs/node/pull/45915)
- \[[`faf5c23084`](https://github.com/nodejs/node/commit/faf5c23084)] - **(SEMVER-MINOR)** **doc**: add parallelism note to os.cpus() (Colin Ihrig) [#45895](https://github.com/nodejs/node/pull/45895)
- \[[`9ed547b73c`](https://github.com/nodejs/node/commit/9ed547b73c)] - **doc**: fix wrong output of example in `url.password` (Deokjin Kim) [#45928](https://github.com/nodejs/node/pull/45928)
- \[[`a89f8c1337`](https://github.com/nodejs/node/commit/a89f8c1337)] - **doc**: fix some history entries in `deprecations.md` (Antoine du Hamel) [#45891](https://github.com/nodejs/node/pull/45891)
- \[[`cf30fca23f`](https://github.com/nodejs/node/commit/cf30fca23f)] - **doc**: add tip for NODE_MODULE (theanarkh) [#45797](https://github.com/nodejs/node/pull/45797)
- \[[`d500445aec`](https://github.com/nodejs/node/commit/d500445aec)] - **doc**: reduce likelihood of mismerges during release (Richard Lau) [#45864](https://github.com/nodejs/node/pull/45864)
- \[[`e229f060e3`](https://github.com/nodejs/node/commit/e229f060e3)] - **doc**: add backticks to webcrypto rsaOaepParams (Filip Skokan) [#45883](https://github.com/nodejs/node/pull/45883)
- \[[`dfa58c1947`](https://github.com/nodejs/node/commit/dfa58c1947)] - **doc**: remove release cleanup step (Michaël Zasso) [#45858](https://github.com/nodejs/node/pull/45858)
- \[[`b93a9670a8`](https://github.com/nodejs/node/commit/b93a9670a8)] - **doc**: add stream/promises pipeline and finished to doc (Marco Ippolito) [#45832](https://github.com/nodejs/node/pull/45832)
- \[[`c86f4a17d6`](https://github.com/nodejs/node/commit/c86f4a17d6)] - **doc**: remove Juan Jose keys (Rafael Gonzaga) [#45827](https://github.com/nodejs/node/pull/45827)
- \[[`c37a119f90`](https://github.com/nodejs/node/commit/c37a119f90)] - **doc**: remove last example use of require('crypto').webcrypto (Filip Skokan) [#45819](https://github.com/nodejs/node/pull/45819)
- \[[`7e047dfcbb`](https://github.com/nodejs/node/commit/7e047dfcbb)] - **doc**: fix wrong output of example in util (Deokjin Kim) [#45825](https://github.com/nodejs/node/pull/45825)
- \[[`8046e0ef53`](https://github.com/nodejs/node/commit/8046e0ef53)] - **errors**: refactor to use a method that formats a list string (Daeyeon Jeong) [#45793](https://github.com/nodejs/node/pull/45793)
- \[[`2d49e0e635`](https://github.com/nodejs/node/commit/2d49e0e635)] - **esm**: rewrite loader hooks test (Geoffrey Booth) [#46016](https://github.com/nodejs/node/pull/46016)
- \[[`47cc0e4bdb`](https://github.com/nodejs/node/commit/47cc0e4bdb)] - **events**: fix violation of symbol naming convention (Deokjin Kim) [#45978](https://github.com/nodejs/node/pull/45978)
- \[[`22a66cff66`](https://github.com/nodejs/node/commit/22a66cff66)] - **fs**: refactor to use `validateInteger` (Deokjin Kim) [#46008](https://github.com/nodejs/node/pull/46008)
- \[[`bc43922949`](https://github.com/nodejs/node/commit/bc43922949)] - **http**: replace `var` with `const` on code of comment (Deokjin Kim) [#45951](https://github.com/nodejs/node/pull/45951)
- \[[`7ea72ee421`](https://github.com/nodejs/node/commit/7ea72ee421)] - **(SEMVER-MINOR)** **http**: improved timeout defaults handling (Paolo Insogna) [#45778](https://github.com/nodejs/node/pull/45778)
- \[[`7f1daedf4c`](https://github.com/nodejs/node/commit/7f1daedf4c)] - **lib**: update JSDoc of `getOwnPropertyValueOrDefault` (Deokjin Kim) [#46010](https://github.com/nodejs/node/pull/46010)
- \[[`28f9089b83`](https://github.com/nodejs/node/commit/28f9089b83)] - **lib**: use `kEmptyObject` as default value for options (Deokjin Kim) [#46011](https://github.com/nodejs/node/pull/46011)
- \[[`f6c6673ec4`](https://github.com/nodejs/node/commit/f6c6673ec4)] - **lib**: lazy-load deps in modules/run_main.js (Joyee Cheung) [#45849](https://github.com/nodejs/node/pull/45849)
- \[[`e529ea4144`](https://github.com/nodejs/node/commit/e529ea4144)] - **lib**: lazy-load deps in source_map_cache.js (Joyee Cheung) [#45849](https://github.com/nodejs/node/pull/45849)
- \[[`943852ab83`](https://github.com/nodejs/node/commit/943852ab83)] - **lib**: add getLazy() method to internal/util (Joyee Cheung) [#45849](https://github.com/nodejs/node/pull/45849)
- \[[`25d0a94453`](https://github.com/nodejs/node/commit/25d0a94453)] - **meta**: update AUTHORS (Node.js GitHub Bot) [#46040](https://github.com/nodejs/node/pull/46040)
- \[[`0a70316ecc`](https://github.com/nodejs/node/commit/0a70316ecc)] - **meta**: update AUTHORS (Node.js GitHub Bot) [#45968](https://github.com/nodejs/node/pull/45968)
- \[[`86e30fcb4d`](https://github.com/nodejs/node/commit/86e30fcb4d)] - **meta**: add `nodejs/loaders` to CODEOWNERS (Geoffrey Booth) [#45940](https://github.com/nodejs/node/pull/45940)
- \[[`e95695654d`](https://github.com/nodejs/node/commit/e95695654d)] - **meta**: add `nodejs/test_runner` to CODEOWNERS (Antoine du Hamel) [#45935](https://github.com/nodejs/node/pull/45935)
- \[[`353dab5bdf`](https://github.com/nodejs/node/commit/353dab5bdf)] - **meta**: update AUTHORS (Node.js GitHub Bot) [#45899](https://github.com/nodejs/node/pull/45899)
- \[[`0b3512f690`](https://github.com/nodejs/node/commit/0b3512f690)] - **modules**: move callbacks and conditions into modules/esm/utils.js (Joyee Cheung) [#45849](https://github.com/nodejs/node/pull/45849)
- \[[`c6ab449d1b`](https://github.com/nodejs/node/commit/c6ab449d1b)] - **modules**: move modules/cjs/helpers.js to modules/helpers.js (Joyee Cheung) [#45849](https://github.com/nodejs/node/pull/45849)
- \[[`4d62b099b4`](https://github.com/nodejs/node/commit/4d62b099b4)] - **net**: handle socket.write(cb) edge case (Santiago Gimeno) [#45922](https://github.com/nodejs/node/pull/45922)
- \[[`8e6b8dbb41`](https://github.com/nodejs/node/commit/8e6b8dbb41)] - **net**: add autoSelectFamily global getter and setter (Paolo Insogna) [#45777](https://github.com/nodejs/node/pull/45777)
- \[[`f3bb6a38ae`](https://github.com/nodejs/node/commit/f3bb6a38ae)] - **node-api**: generalize finalizer second pass callback (Chengzhong Wu) [#44141](https://github.com/nodejs/node/pull/44141)
- \[[`d71883e271`](https://github.com/nodejs/node/commit/d71883e271)] - **(SEMVER-MINOR)** **os**: add availableParallelism() (Colin Ihrig) [#45895](https://github.com/nodejs/node/pull/45895)
- \[[`4c0850539a`](https://github.com/nodejs/node/commit/4c0850539a)] - **process,worker**: ensure code after exit() effectless (ywave620) [#45620](https://github.com/nodejs/node/pull/45620)
- \[[`24cae6b4a3`](https://github.com/nodejs/node/commit/24cae6b4a3)] - **repl**: improve robustness wrt to prototype pollution (Antoine du Hamel) [#45604](https://github.com/nodejs/node/pull/45604)
- \[[`af25c95b22`](https://github.com/nodejs/node/commit/af25c95b22)] - **src**: fix typo in `node_file.cc` (Vadim) [#45998](https://github.com/nodejs/node/pull/45998)
- \[[`261d6d0726`](https://github.com/nodejs/node/commit/261d6d0726)] - **src**: fix crash on OnStreamRead on Windows (Santiago Gimeno) [#45878](https://github.com/nodejs/node/pull/45878)
- \[[`6c5b7e660b`](https://github.com/nodejs/node/commit/6c5b7e660b)] - **src**: add worker per-isolate binding initialization (Chengzhong Wu) [#45547](https://github.com/nodejs/node/pull/45547)
- \[[`db535b6caa`](https://github.com/nodejs/node/commit/db535b6caa)] - **src**: define per-isolate internal bindings registration callback (Chengzhong Wu) [#45547](https://github.com/nodejs/node/pull/45547)
- \[[`ded87f6dc4`](https://github.com/nodejs/node/commit/ded87f6dc4)] - **src**: fix creating `Isolate`s from addons (Anna Henningsen) [#45885](https://github.com/nodejs/node/pull/45885)
- \[[`c2ed0ccb28`](https://github.com/nodejs/node/commit/c2ed0ccb28)] - **src**: use string_view for FastStringKey implementation (Anna Henningsen) [#45914](https://github.com/nodejs/node/pull/45914)
- \[[`b995138b96`](https://github.com/nodejs/node/commit/b995138b96)] - **src**: use CreateEnvironment instead of inlining its code where possible (Anna Henningsen) [#45886](https://github.com/nodejs/node/pull/45886)
- \[[`4454f5fd71`](https://github.com/nodejs/node/commit/4454f5fd71)] - **src**: fix UB in overflow checks (Ben Noordhuis) [#45882](https://github.com/nodejs/node/pull/45882)
- \[[`27d3201502`](https://github.com/nodejs/node/commit/27d3201502)] - **src**: check size of args before using for exec_path (A. Wilcox) [#45902](https://github.com/nodejs/node/pull/45902)
- \[[`2f898f2983`](https://github.com/nodejs/node/commit/2f898f2983)] - **src**: fix tls certificate root store data race (Ben Noordhuis) [#45767](https://github.com/nodejs/node/pull/45767)
- \[[`eff92a61b9`](https://github.com/nodejs/node/commit/eff92a61b9)] - **src**: add undici and acorn to `process.versions` (Debadree Chatterjee) [#45621](https://github.com/nodejs/node/pull/45621)
- \[[`ab22a8ff4b`](https://github.com/nodejs/node/commit/ab22a8ff4b)] - **stream**: refactor to use `validateFunction` (Deokjin Kim) [#46007](https://github.com/nodejs/node/pull/46007)
- \[[`0858956f5f`](https://github.com/nodejs/node/commit/0858956f5f)] - **stream**: fix typo in JSDoc (Deokjin Kim) [#45991](https://github.com/nodejs/node/pull/45991)
- \[[`2807efaea6`](https://github.com/nodejs/node/commit/2807efaea6)] - **test**: use `process.hrtime.bigint` instead of `process.hrtime` (Deokjin Kim) [#45877](https://github.com/nodejs/node/pull/45877)
- \[[`0f5a145973`](https://github.com/nodejs/node/commit/0f5a145973)] - **test**: print failed JS/parallel tests (Geoffrey Booth) [#45960](https://github.com/nodejs/node/pull/45960)
- \[[`c6c094702b`](https://github.com/nodejs/node/commit/c6c094702b)] - **test**: split parallel fs-watch-recursive tests (Yagiz Nizipli) [#45865](https://github.com/nodejs/node/pull/45865)
- \[[`97a8e055be`](https://github.com/nodejs/node/commit/97a8e055be)] - **test**: add all WebCryptoAPI globals to WPTRunner's loadLazyGlobals (Filip Skokan) [#45857](https://github.com/nodejs/node/pull/45857)
- \[[`95ce16d8d9`](https://github.com/nodejs/node/commit/95ce16d8d9)] - **test**: fix test broken under --node-builtin-modules-path (Geoffrey Booth) [#45894](https://github.com/nodejs/node/pull/45894)
- \[[`97868befe7`](https://github.com/nodejs/node/commit/97868befe7)] - **test**: fix mock.method to support class instances (Erick Wendel) [#45608](https://github.com/nodejs/node/pull/45608)
- \[[`71056daf76`](https://github.com/nodejs/node/commit/71056daf76)] - **test**: update encoding wpt to latest (Yagiz Nizipli) [#45850](https://github.com/nodejs/node/pull/45850)
- \[[`10367c4cae`](https://github.com/nodejs/node/commit/10367c4cae)] - **test**: update url wpt to latest (Yagiz Nizipli) [#45852](https://github.com/nodejs/node/pull/45852)
- \[[`53f02cf631`](https://github.com/nodejs/node/commit/53f02cf631)] - **test**: add CryptoKey transferring tests (Filip Skokan) [#45811](https://github.com/nodejs/node/pull/45811)
- \[[`5de08ef275`](https://github.com/nodejs/node/commit/5de08ef275)] - **test**: add postject to fixtures (Darshan Sen) [#45298](https://github.com/nodejs/node/pull/45298)
- \[[`fea122d51e`](https://github.com/nodejs/node/commit/fea122d51e)] - **test**: enable idlharness WebCryptoAPI WPTs (Filip Skokan) [#45822](https://github.com/nodejs/node/pull/45822)
- \[[`3c2ce5635e`](https://github.com/nodejs/node/commit/3c2ce5635e)] - **test**: remove use of --experimental-global-webcrypto flag (Filip Skokan) [#45816](https://github.com/nodejs/node/pull/45816)
- \[[`b5e124537e`](https://github.com/nodejs/node/commit/b5e124537e)] - **test,crypto**: update WebCryptoAPI WPT (Filip Skokan) [#45860](https://github.com/nodejs/node/pull/45860)
- \[[`7ae24abd7b`](https://github.com/nodejs/node/commit/7ae24abd7b)] - **test_runner**: use os.availableParallelism() (Colin Ihrig) [#45969](https://github.com/nodejs/node/pull/45969)
- \[[`c5004d42af`](https://github.com/nodejs/node/commit/c5004d42af)] - **test_runner**: run t.after() if test body throws (Colin Ihrig) [#45870](https://github.com/nodejs/node/pull/45870)
- \[[`bdbb676bee`](https://github.com/nodejs/node/commit/bdbb676bee)] - **test_runner**: parse yaml (Moshe Atlow) [#45815](https://github.com/nodejs/node/pull/45815)
- \[[`ca9b9b9ce6`](https://github.com/nodejs/node/commit/ca9b9b9ce6)] - **tls**: don't treat fatal TLS alerts as EOF (David Benjamin) [#44563](https://github.com/nodejs/node/pull/44563)
- \[[`d08a574ecf`](https://github.com/nodejs/node/commit/d08a574ecf)] - **tls**: fix re-entrancy issue with TLS close_notify (David Benjamin) [#44563](https://github.com/nodejs/node/pull/44563)
- \[[`0f0d22a63e`](https://github.com/nodejs/node/commit/0f0d22a63e)] - **tools**: update lint-md-dependencies to rollup\@3.9.0 (Node.js GitHub Bot) [#46039](https://github.com/nodejs/node/pull/46039)
- \[[`5a8d125fc4`](https://github.com/nodejs/node/commit/5a8d125fc4)] - **tools**: update doc to unist-util-select\@4.0.2 (Node.js GitHub Bot) [#46038](https://github.com/nodejs/node/pull/46038)
- \[[`54776ffe80`](https://github.com/nodejs/node/commit/54776ffe80)] - **tools**: add release host var to promotion script (Ruy Adorno) [#45913](https://github.com/nodejs/node/pull/45913)
- \[[`f968fdb78a`](https://github.com/nodejs/node/commit/f968fdb78a)] - **tools**: add url to `AUTHORS` update automation (Antoine du Hamel) [#45971](https://github.com/nodejs/node/pull/45971)
- \[[`7c518cbac1`](https://github.com/nodejs/node/commit/7c518cbac1)] - **tools**: update lint-md-dependencies to rollup\@3.8.1 (Node.js GitHub Bot) [#45967](https://github.com/nodejs/node/pull/45967)
- \[[`1282f7f656`](https://github.com/nodejs/node/commit/1282f7f656)] - **tools**: update GitHub workflow action (Mohammed Keyvanzadeh) [#45937](https://github.com/nodejs/node/pull/45937)
- \[[`f446af78e9`](https://github.com/nodejs/node/commit/f446af78e9)] - **tools**: update lint-md dependencies (Node.js GitHub Bot) [#45813](https://github.com/nodejs/node/pull/45813)
- \[[`794611ade9`](https://github.com/nodejs/node/commit/794611ade9)] - **tools**: enforce use of trailing commas in `tools/` (Antoine du Hamel) [#45889](https://github.com/nodejs/node/pull/45889)
- \[[`124c2b32d9`](https://github.com/nodejs/node/commit/124c2b32d9)] - **tools**: fix incorrect version history order (Fabien Michel) [#45728](https://github.com/nodejs/node/pull/45728)
- \[[`27cf389c22`](https://github.com/nodejs/node/commit/27cf389c22)] - **tools**: update eslint to 8.29.0 (Node.js GitHub Bot) [#45733](https://github.com/nodejs/node/pull/45733)
- \[[`ae842a40b5`](https://github.com/nodejs/node/commit/ae842a40b5)] - **util**: add fast path for text-decoder fatal flag (Yagiz Nizipli) [#45803](https://github.com/nodejs/node/pull/45803)
- \[[`389cc3e1d6`](https://github.com/nodejs/node/commit/389cc3e1d6)] - **vm**: refactor to use `validateStringArray` (Deokjin Kim) [#46020](https://github.com/nodejs/node/pull/46020)
- \[[`7bd6a2c258`](https://github.com/nodejs/node/commit/7bd6a2c258)] - **wasi**: fast calls (snek) [#43697](https://github.com/nodejs/node/pull/43697)

Windows 32-bit Installer: https://nodejs.org/dist/v19.4.0/node-v19.4.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v19.4.0/node-v19.4.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v19.4.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v19.4.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v19.4.0/node-v19.4.0.pkg \
macOS Apple Silicon 64-bit Binary: https://nodejs.org/dist/v19.4.0/node-v19.4.0-darwin-arm64.tar.gz \
macOS Intel 64-bit Binary: https://nodejs.org/dist/v19.4.0/node-v19.4.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v19.4.0/node-v19.4.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v19.4.0/node-v19.4.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v19.4.0/node-v19.4.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v19.4.0/node-v19.4.0-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v19.4.0/node-v19.4.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v19.4.0/node-v19.4.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v19.4.0/node-v19.4.0.tar.gz \
Other release files: https://nodejs.org/dist/v19.4.0/ \
Documentation: https://nodejs.org/docs/v19.4.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

9fb2fbb9339fe8f3e436b82253c4c53b75b2364c02d9f6857d20efdd90e56479  node-v19.4.0-aix-ppc64.tar.gz
9b6bfec4787ec5e500d21e835467fc4bb67689ef7d30a66e31d5bc372405eb42  node-v19.4.0-darwin-arm64.tar.gz
d0af4e4ca73b21df447fe93f54e3ce4ceff09a0b26308141c5ec8a97fde28d01  node-v19.4.0-darwin-arm64.tar.xz
13ec36d26994432731c33a24b55e29a0137e688386adb3254f54ecdbb5ab19c7  node-v19.4.0-darwin-x64.tar.gz
322decb736c707648f3fba403c349e56cdf2e53cb8a317e6434102899e855a16  node-v19.4.0-darwin-x64.tar.xz
b871cd24a5b387209902b3af1d9b00c031e66719251abc4ba76013cb33c135bf  node-v19.4.0-headers.tar.gz
795a909add7c85c4d9257f4114c2a0a2dfacc2fc55a092591b5f36ad808de888  node-v19.4.0-headers.tar.xz
68417f33ca2556a73486a27a75a8214dd4532506a94bfdfcb9943474c9a7c13e  node-v19.4.0-linux-arm64.tar.gz
e53fe1c30ccda500a021f4936dc1e98e9bc13be9a333379d7bd7d49a40226242  node-v19.4.0-linux-arm64.tar.xz
bf1f731941f7025a26bc9682a17941e278e588b8cf1c7163c0ca1919e9637f12  node-v19.4.0-linux-armv7l.tar.gz
5d47f0b2f3e9b19cae4df665abcb093e44d123a9b0bbb8c5fd7fe42d9dd04489  node-v19.4.0-linux-armv7l.tar.xz
aaa796e32e436d37bda06f8123b94727a08079ba9202510a645574a94f788938  node-v19.4.0-linux-ppc64le.tar.gz
c0d2da1191a8ce6bd6e10a6c5792e0e540340bd4da5bb3c265eab3fb2d7002af  node-v19.4.0-linux-ppc64le.tar.xz
adcc881eb2052b587d5554c2410e38b1990bf10e8c4f8309fe69f38d3df235ae  node-v19.4.0-linux-s390x.tar.gz
39f259e227366a138eef65a727f9bee1a8b7bb75857450b3770c6eb937e5caa7  node-v19.4.0-linux-s390x.tar.xz
e39635d2cb60bba7aea80801fc6524806cb6980b68bf8c9b74389c93db445f63  node-v19.4.0-linux-x64.tar.gz
2f3b7a02e41eeda113326370f51bd1d2a54de6b8a3628b0d36623c40ca4db783  node-v19.4.0-linux-x64.tar.xz
4fd543fa3eff180d5d7dabf8f8a4400f47a38c054e167808d9403e7196b114f4  node-v19.4.0-win-x64.7z
91d627e8eda9bd78af7316d98657df58d7f32b272b733f8a5a2cc1f06aa83608  node-v19.4.0-win-x64.zip
43278b59b4682d5587ebabb0b044ec06f75808e0f7bf6e9334c73a7a350e65a7  node-v19.4.0-win-x86.7z
0db854d0a6f28dc10e834d8ce6768488a67a1069aa139d5531e1607d17a8f8c3  node-v19.4.0-win-x86.zip
b4b0f39031d6be0495af5d4f5626890e60e810385d722b9c74199cf57427dea3  node-v19.4.0-x64.msi
e05ad07e83d551d7d1e3c2334ed504db39e1792abe0a4fc37369dfde5d18a038  node-v19.4.0-x86.msi
314faa5071d95f5abe51390b0b6b8e0ac143bc061cfefb9f60256c421fcf460f  node-v19.4.0.pkg
fa814c352728af517ee9b4dab25ad1761abb4dd9f18c0bb3e0a78e2ad2ef9bf5  node-v19.4.0.tar.gz
020541d670f528bd03ba0b92a1d0c46d6850982175a72f2aa557aefeda31b261  node-v19.4.0.tar.xz
7ed77ba068581d2920ac961a718a4421038008bb10db9f89d6c5927143255c05  win-x64/node.exe
39237840169dfaec4b7ca2df0021194195e7525dac9314528fd8ad9a96d29fbd  win-x64/node.lib
56d52ca91f1b5a6c70e8ce07ea37c5eea22554b35939f05f347c28b983d13841  win-x64/node_pdb.7z
40972e2df1e6dffdf06e4add37483fd596c2ce0d18b25c34cb8c64bf264cd007  win-x64/node_pdb.zip
07b25e847543e05bf8ea0cf72f5ae02296d17cb727221273e63d4746f3e86de0  win-x86/node.exe
d4128250108890b26cdc194e2288883b1c777d00241df791e6b2cebc3733b9b2  win-x86/node.lib
5f8ebaadbfb7c9c6803cf7c1cfd93b36a22fb7a355826a4e9d42096db15e5fb6  win-x86/node_pdb.7z
7d66614f04a60b0372369d75653dd25b0ce57418777694701a0c701e449b317b  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQGzBAEBCAAdFiEEiQwI24V5Fi/uDfnbi+q0389VXvQFAmO4HtQACgkQi+q0389V
XvTPXgwAnSo4mmyzKSf++RWpOlbF3o75AVGt+zho3aOifULhNIE5lC61j0zeQhs0
7nNKsC5CXt4Kp+Vh8jnjDEYyU3LDvaWcYHBYrCG/oFucVLdkaXd7PORQCJ7cORZa
E9ktuAeUN9Td9VotbTtdPAaWAND09mlKVUtXrPjMCJBHw7VTpbqGF7ssKCrFsOBX
x0v7uz8C7WSJyI4b6PjApRap/vktIvqE5X3f2Y1uKv+Y6eXanHfU/SMYDiPNpXuE
SydszhcvmnUCaaQ7iN1lRsgICyvd+zWbUeB7tuvj5NMzsxXeFqkMgyfGTke13I4v
ee93m1RaFz0sI0r029VtYiyDmC8/QV8Aruak0UzZO2bX4BvXNeEHW1k0rc87ofTc
zKNrsLrilO+AVk9M2AMwCUv33JKeZCqBuYVIR+c7nNcxHw2rqdXdvwbsU7UljNjS
3IZks9SiE7+armC4CWMRoHvfxBcesHNXXIWFtem/7aCLMVBWgEtAusNZmzTw7L7U
O4UQI+Um
=WrOY
-----END PGP SIGNATURE-----

```
