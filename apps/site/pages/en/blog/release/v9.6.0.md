---
date: '2018-02-22T17:03:37.680Z'
category: release
title: Node v9.6.0 (Current)
layout: blog-post
author: <PERSON><PERSON>
---

### Notable Changes

- **async_hooks**:
  - deprecate unsafe emit{Before,After} (<PERSON>) [#18513](https://github.com/nodejs/node/pull/18513)
  - rename PromiseWrap.parentId to PromiseWrap.isChainedPromise (<PERSON>) [#18633](https://github.com/nodejs/node/pull/18633)
- **deps**:
  - update node-inspect to 1.11.3 (<PERSON>) [#18354](https://github.com/nodejs/node/pull/18354)
  - ICU 60.2 bump (<PERSON>) [#17687](https://github.com/nodejs/node/pull/17687)
  - Introduce ScriptOrModule and HostDefinedOptions to V8 (<PERSON>) [#16889](https://github.com/nodejs/node/pull/16889)
- **http**:
  - add options to http.createServer() for `IncomingMessage` and `ServerReponse` (<PERSON>) [#15752](https://github.com/nodejs/node/pull/15752)
- **http2**:
  - add http fallback options to .createServer (Peter Marton) [#15752](https://github.com/nodejs/node/pull/15752)
- **https**:
  - Adds the remaining options from tls.createSecureContext() to the string generated by Agent#getName(). This allows https.request() to accept the options and generate unique sockets appropriately. (Jeff Principe) [#16402](https://github.com/nodejs/node/pull/16402)
- **inspector**:
  - --inspect-brk for es modules (Guy Bedford) [#18194](https://github.com/nodejs/node/pull/18194)
- **lib**:
  - allow process kill by signal number (Sam Roberts) [#16944](https://github.com/nodejs/node/pull/16944)
- **module**:
  - enable dynamic import (Myles Borins) [#18387](https://github.com/nodejs/node/pull/18387)
  - dynamic import is now supported (Jan Krems) [#15713](https://github.com/nodejs/node/pull/15713)
- **n-api**:
  - add methods to open/close callback scope (Michael Dawson) [#18089](https://github.com/nodejs/node/pull/18089)
- **src**:
  - allow --perf-(basic-)?prof in NODE_OPTIONS (Leko) [#17600](https://github.com/nodejs/node/pull/17600)
- **vm**:
  - add support for es modules (Gus Caplan) [#17560](https://github.com/nodejs/node/pull/17560)

### Commits

- [[`7f5334e243`](https://github.com/nodejs/node/commit/7f5334e243)] - **(SEMVER-MINOR)** **async_hooks**: deprecate unsafe emit{Before,After} (Ali Ijaz Sheikh) [#18513](https://github.com/nodejs/node/pull/18513)
- [[`8e39c3bfd6`](https://github.com/nodejs/node/commit/8e39c3bfd6)] - **(SEMVER-MINOR)** **async_hooks**: rename PromiseWrap.parentId (Ali Ijaz Sheikh) [#18633](https://github.com/nodejs/node/pull/18633)
- [[`0865d11c08`](https://github.com/nodejs/node/commit/0865d11c08)] - **async_hooks**: clean up comments (Ali Ijaz Sheikh) [#18467](https://github.com/nodejs/node/pull/18467)
- [[`4d78eb8663`](https://github.com/nodejs/node/commit/4d78eb8663)] - **benchmark**: improve compare output (Ruben Bridgewater) [#18597](https://github.com/nodejs/node/pull/18597)
- [[`ffbad8350e`](https://github.com/nodejs/node/commit/ffbad8350e)] - **benchmark**: spread operator benchmark (James M Snell) [#18442](https://github.com/nodejs/node/pull/18442)
- [[`9ae513a7de`](https://github.com/nodejs/node/commit/9ae513a7de)] - **benchmark**: shorten config name in http benchmark (Joyee Cheung) [#18452](https://github.com/nodejs/node/pull/18452)
- [[`d469a06ace`](https://github.com/nodejs/node/commit/d469a06ace)] - **benchmark**: cut down http benchmark run time (Joyee Cheung) [#18379](https://github.com/nodejs/node/pull/18379)
- [[`9c125825a9`](https://github.com/nodejs/node/commit/9c125825a9)] - **benchmark**: refactor (Ruben Bridgewater) [#18320](https://github.com/nodejs/node/pull/18320)
- [[`f0186704cd`](https://github.com/nodejs/node/commit/f0186704cd)] - **benchmark**: (timers) refactor (Ruben Bridgewater) [#18320](https://github.com/nodejs/node/pull/18320)
- [[`28156e16d1`](https://github.com/nodejs/node/commit/28156e16d1)] - **benchmark**: (http(2)) refactor (Ruben Bridgewater) [#18320](https://github.com/nodejs/node/pull/18320)
- [[`076b3d9b0a`](https://github.com/nodejs/node/commit/076b3d9b0a)] - **benchmark**: (es) refactor (Ruben Bridgewater) [#18320](https://github.com/nodejs/node/pull/18320)
- [[`76cb958975`](https://github.com/nodejs/node/commit/76cb958975)] - **benchmark**: (url) refactor (Ruben Bridgewater) [#18320](https://github.com/nodejs/node/pull/18320)
- [[`0851822b87`](https://github.com/nodejs/node/commit/0851822b87)] - **benchmark**: (crypto) refactor (Ruben Bridgewater) [#18320](https://github.com/nodejs/node/pull/18320)
- [[`cb13c7c653`](https://github.com/nodejs/node/commit/cb13c7c653)] - **benchmark**: (buffer) refactor (Ruben Bridgewater) [#18320](https://github.com/nodejs/node/pull/18320)
- [[`9acf7545f0`](https://github.com/nodejs/node/commit/9acf7545f0)] - **benchmark**: (assert) refactor (Ruben Bridgewater) [#18320](https://github.com/nodejs/node/pull/18320)
- [[`7da01f43fd`](https://github.com/nodejs/node/commit/7da01f43fd)] - **benchmark**: fix variables not being set (Ruben Bridgewater) [#18320](https://github.com/nodejs/node/pull/18320)
- [[`4a5d7d4248`](https://github.com/nodejs/node/commit/4a5d7d4248)] - **benchmark**: fix platform in basename-win32 (Ruben Bridgewater) [#18320](https://github.com/nodejs/node/pull/18320)
- [[`f3ab106750`](https://github.com/nodejs/node/commit/f3ab106750)] - **buffer**: remove obsolete NaN check (Ruben Bridgewater) [#18744](https://github.com/nodejs/node/pull/18744)
- [[`c38576e526`](https://github.com/nodejs/node/commit/c38576e526)] - **buffer**: simplify check size in assertSize (Mihail Bodrov) [#18665](https://github.com/nodejs/node/pull/18665)
- [[`080368b5d0`](https://github.com/nodejs/node/commit/080368b5d0)] - **build**: no longer have v8-debug.h as dependency. (Yang Guo) [#18677](https://github.com/nodejs/node/pull/18677)
- [[`15db2969fe`](https://github.com/nodejs/node/commit/15db2969fe)] - **build**: do not suppress output in make doc-only (Joyee Cheung) [#18507](https://github.com/nodejs/node/pull/18507)
- [[`c642e229da`](https://github.com/nodejs/node/commit/c642e229da)] - **build**: add doc linting when runnning `make lint` (Camilo Gonzalez) [#18472](https://github.com/nodejs/node/pull/18472)
- [[`be5c293d73`](https://github.com/nodejs/node/commit/be5c293d73)] - **build**: allow x86_64 as a dest_cpu alias for x64 (Rod Vagg) [#18052](https://github.com/nodejs/node/pull/18052)
- [[`9c6bb5f386`](https://github.com/nodejs/node/commit/9c6bb5f386)] - **build**: add cflags for OpenBSD, remove stray comma. (Aaron Bieber) [#18448](https://github.com/nodejs/node/pull/18448)
- [[`2c7de9df50`](https://github.com/nodejs/node/commit/2c7de9df50)] - **build,win**: replace run-python subroutine with single find_python call (Nikolai Vavilov) [#18621](https://github.com/nodejs/node/pull/18621)
- [[`91f2cf9297`](https://github.com/nodejs/node/commit/91f2cf9297)] - **child_process**: fix stdio sockets creation (Santiago Gimeno) [#18701](https://github.com/nodejs/node/pull/18701)
- [[`a893b42791`](https://github.com/nodejs/node/commit/a893b42791)] - **crypto**: use non-deprecated v8::Object::Set (Daniel Bevenius) [#17482](https://github.com/nodejs/node/pull/17482)
- [[`2d98b58c08`](https://github.com/nodejs/node/commit/2d98b58c08)] - **deps**: V8: backport 76c3ac5 from upstream (Ali Ijaz Sheikh) [#18298](https://github.com/nodejs/node/pull/18298)
- [[`442903fb1b`](https://github.com/nodejs/node/commit/442903fb1b)] - **deps**: update node-inspect to 1.11.3 (Jan Krems) [#18354](https://github.com/nodejs/node/pull/18354)
- [[`9e7f8633b6`](https://github.com/nodejs/node/commit/9e7f8633b6)] - **deps**: ICU 60.2 bump (Steven R. Loomis) [#17687](https://github.com/nodejs/node/pull/17687)
- [[`11566fe532`](https://github.com/nodejs/node/commit/11566fe532)] - **deps**: cherry-pick dbfe4a49d8 from upstream V8 (Jan Krems) [#16889](https://github.com/nodejs/node/pull/16889)
- [[`6edf952628`](https://github.com/nodejs/node/commit/6edf952628)] - **doc**: fix nits in tools/doc/README.md (Vse Mozhet Byt) [#18874](https://github.com/nodejs/node/pull/18874)
- [[`7624686888`](https://github.com/nodejs/node/commit/7624686888)] - **doc**: fix minor grammar/typographical issues in onboarding.md (Rich Trott) [#18847](https://github.com/nodejs/node/pull/18847)
- [[`2f836e76bd`](https://github.com/nodejs/node/commit/2f836e76bd)] - **doc**: update onboarding.md for faster exercise completion (Rich Trott) [#18846](https://github.com/nodejs/node/pull/18846)
- [[`e1f82735fe`](https://github.com/nodejs/node/commit/e1f82735fe)] - **doc**: improved documentation for fs.unlink() (dustinnewman98) [#18843](https://github.com/nodejs/node/pull/18843)
- [[`63b0c158f7`](https://github.com/nodejs/node/commit/63b0c158f7)] - **doc**: fix broken link in pull-requests.md (Justin Lee) [#18873](https://github.com/nodejs/node/pull/18873)
- [[`8047c27855`](https://github.com/nodejs/node/commit/8047c27855)] - **doc**: fix typo in http2.md (Vse Mozhet Byt) [#18872](https://github.com/nodejs/node/pull/18872)
- [[`0dd8ea4a00`](https://github.com/nodejs/node/commit/0dd8ea4a00)] - **doc**: refactor manpage to use mdoc(7) macros (Alhadis) [#18559](https://github.com/nodejs/node/pull/18559)
- [[`33271e60f3`](https://github.com/nodejs/node/commit/33271e60f3)] - **doc**: mark accessing IPC channel fd as undefined (Bartosz Sosnowski) [#17545](https://github.com/nodejs/node/pull/17545)
- [[`02e9e9949c`](https://github.com/nodejs/node/commit/02e9e9949c)] - **doc**: fix minor typos in GOVERNANCE.md (Rich Trott) [#18829](https://github.com/nodejs/node/pull/18829)
- [[`1bff0aaae5`](https://github.com/nodejs/node/commit/1bff0aaae5)] - **doc**: add Yihong Wang to collaborators (Yihong Wang) [#18824](https://github.com/nodejs/node/pull/18824)
- [[`1c77929231`](https://github.com/nodejs/node/commit/1c77929231)] - **doc**: warn against concurrent http2stream.respondWithFD (Anna Henningsen) [#18762](https://github.com/nodejs/node/pull/18762)
- [[`cd2fa0412f`](https://github.com/nodejs/node/commit/cd2fa0412f)] - **doc**: activate `no-multiple-empty-lines` rule (Ruben Bridgewater) [#18747](https://github.com/nodejs/node/pull/18747)
- [[`20ad397f93`](https://github.com/nodejs/node/commit/20ad397f93)] - **doc**: note that linting is required in releases.md (Gibson Fahnestock) [#18776](https://github.com/nodejs/node/pull/18776)
- [[`0fc33fb282`](https://github.com/nodejs/node/commit/0fc33fb282)] - **doc**: remove extra space in README.md (Matheus Marchini) [#18822](https://github.com/nodejs/node/pull/18822)
- [[`9bec493510`](https://github.com/nodejs/node/commit/9bec493510)] - **doc**: update crypo Certficate class. (Antoine AMARA) [#18721](https://github.com/nodejs/node/pull/18721)
- [[`17d4dd5cce`](https://github.com/nodejs/node/commit/17d4dd5cce)] - **doc**: move Fedor to TSC Emeritus (Myles Borins) [#18752](https://github.com/nodejs/node/pull/18752)
- [[`92ed0710da`](https://github.com/nodejs/node/commit/92ed0710da)] - **doc**: add mmarchini to collaborators (Matheus Marchini) [#18740](https://github.com/nodejs/node/pull/18740)
- [[`b5073a0744`](https://github.com/nodejs/node/commit/b5073a0744)] - **doc**: mark NAPI_AUTO_LENGTH as code (Tobias Nießen) [#18697](https://github.com/nodejs/node/pull/18697)
- [[`3cfb313e8e`](https://github.com/nodejs/node/commit/3cfb313e8e)] - **doc**: add error check to fs example (Evan Lucas) [#18681](https://github.com/nodejs/node/pull/18681)
- [[`876e186573`](https://github.com/nodejs/node/commit/876e186573)] - **doc**: fix exporting a function example (Aonghus O Nia) [#18661](https://github.com/nodejs/node/pull/18661)
- [[`7b377cffdb`](https://github.com/nodejs/node/commit/7b377cffdb)] - **doc**: add history for url.parse (Steven) [#18685](https://github.com/nodejs/node/pull/18685)
- [[`4981e98889`](https://github.com/nodejs/node/commit/4981e98889)] - **doc**: fix links to Style Guide and CPP Style Guide (Justin Lee) [#18683](https://github.com/nodejs/node/pull/18683)
- [[`af977dbf49`](https://github.com/nodejs/node/commit/af977dbf49)] - **doc**: add devsnek to collaborators (Gus Caplan) [#18679](https://github.com/nodejs/node/pull/18679)
- [[`f0f01039b4`](https://github.com/nodejs/node/commit/f0f01039b4)] - **doc**: fix links in YAML metadata of assert.md (Vse Mozhet Byt) [#18670](https://github.com/nodejs/node/pull/18670)
- [[`832e0522eb`](https://github.com/nodejs/node/commit/832e0522eb)] - **doc**: add missing meta for createCipheriv (Tobias Nießen) [#18651](https://github.com/nodejs/node/pull/18651)
- [[`affddd372a`](https://github.com/nodejs/node/commit/affddd372a)] - **doc**: fix description of createDecipheriv (Tobias Nießen) [#18651](https://github.com/nodejs/node/pull/18651)
- [[`4722004900`](https://github.com/nodejs/node/commit/4722004900)] - **doc**: fix MDN links to avoid redirections (Vse Mozhet Byt) [#18631](https://github.com/nodejs/node/pull/18631)
- [[`e7508e5fcd`](https://github.com/nodejs/node/commit/e7508e5fcd)] - **doc**: fix link in https.md (Vse Mozhet Byt) [#18630](https://github.com/nodejs/node/pull/18630)
- [[`dc4da22220`](https://github.com/nodejs/node/commit/dc4da22220)] - **doc**: be more explicit in the sypnosis (Tim O. Peters) [#17977](https://github.com/nodejs/node/pull/17977)
- [[`54391548d0`](https://github.com/nodejs/node/commit/54391548d0)] - **doc**: add missing "changes" key in YAML comment (Luigi Pinca) [#18605](https://github.com/nodejs/node/pull/18605)
- [[`7241fa0fbd`](https://github.com/nodejs/node/commit/7241fa0fbd)] - **doc**: fix typo in http2.md (Vse Mozhet Byt) [#18602](https://github.com/nodejs/node/pull/18602)
- [[`7a432c1af3`](https://github.com/nodejs/node/commit/7a432c1af3)] - **doc**: update onboarding-extras (Gus Caplan) [#18545](https://github.com/nodejs/node/pull/18545)
- [[`c18d958750`](https://github.com/nodejs/node/commit/c18d958750)] - **doc**: modify the return value of request.write() (陈刚) [#18526](https://github.com/nodejs/node/pull/18526)
- [[`e8a75ee113`](https://github.com/nodejs/node/commit/e8a75ee113)] - **doc**: fix typo in n-api.md (Vse Mozhet Byt) [#18590](https://github.com/nodejs/node/pull/18590)
- [[`4f521c7896`](https://github.com/nodejs/node/commit/4f521c7896)] - **doc**: add introduce about cli options (Weijia Wang) [#18475](https://github.com/nodejs/node/pull/18475)
- [[`4dea9e03d6`](https://github.com/nodejs/node/commit/4dea9e03d6)] - **doc**: small typo in n-api.md (iskore) [#18555](https://github.com/nodejs/node/pull/18555)
- [[`6256d70916`](https://github.com/nodejs/node/commit/6256d70916)] - **doc**: add section for strategic initiatives (Michael Dawson) [#17104](https://github.com/nodejs/node/pull/17104)
- [[`5f0b3431e1`](https://github.com/nodejs/node/commit/5f0b3431e1)] - **doc**: remove usage of you in n-api doc (Michael Dawson) [#18528](https://github.com/nodejs/node/pull/18528)
- [[`2418c86c1e`](https://github.com/nodejs/node/commit/2418c86c1e)] - **doc**: expand on promises and async_hooks (Ali Ijaz Sheikh) [#18540](https://github.com/nodejs/node/pull/18540)
- [[`a7ad003e37`](https://github.com/nodejs/node/commit/a7ad003e37)] - **doc**: shell option for the execFile and execFileSync functions (jvelezpo) [#18237](https://github.com/nodejs/node/pull/18237)
- [[`dae86b3edb`](https://github.com/nodejs/node/commit/dae86b3edb)] - **doc**: improve http.request documentation (Guangcong Luo) [#18289](https://github.com/nodejs/node/pull/18289)
- [[`ffc8e8eb40`](https://github.com/nodejs/node/commit/ffc8e8eb40)] - **doc**: remove removed apis from http2 docs (Kelvin Jin) [#18439](https://github.com/nodejs/node/pull/18439)
- [[`25a7bdece5`](https://github.com/nodejs/node/commit/25a7bdece5)] - **doc**: streamline README intro (Rich Trott) [#18483](https://github.com/nodejs/node/pull/18483)
- [[`58003d4ddf`](https://github.com/nodejs/node/commit/58003d4ddf)] - **doc**: move Brian White to TSC Emeriti list (Rich Trott) [#18482](https://github.com/nodejs/node/pull/18482)
- [[`74a823c788`](https://github.com/nodejs/node/commit/74a823c788)] - **doc**: improve stream documentation (陈刚) [#18375](https://github.com/nodejs/node/pull/18375)
- [[`ae372f0e3d`](https://github.com/nodejs/node/commit/ae372f0e3d)] - **doc**: linkify missing types (Vse Mozhet Byt) [#18444](https://github.com/nodejs/node/pull/18444)
- [[`22093abbc8`](https://github.com/nodejs/node/commit/22093abbc8)] - **doc**: add Gibson Fahnestock to TSC (Rich Trott) [#18481](https://github.com/nodejs/node/pull/18481)
- [[`61d4e1d207`](https://github.com/nodejs/node/commit/61d4e1d207)] - **doc**: reorder section on updating PR branch (Ali Ijaz Sheikh) [#18355](https://github.com/nodejs/node/pull/18355)
- [[`8a627b17a4`](https://github.com/nodejs/node/commit/8a627b17a4)] - **doc**: add pending-deprecation to COLLABORATOR_GUIDE (Сковорода Никита Андреевич) [#18433](https://github.com/nodejs/node/pull/18433)
- [[`b76e111ee4`](https://github.com/nodejs/node/commit/b76e111ee4)] - **doc**: fix manpage warnings (Roman Reiss)
- [[`b841abc328`](https://github.com/nodejs/node/commit/b841abc328)] - **doc**: warn about GCM authenticity (Tobias Nießen) [#18376](https://github.com/nodejs/node/pull/18376)
- [[`2d968ca0d5`](https://github.com/nodejs/node/commit/2d968ca0d5)] - **doc**: Update tools/icu/README.md (Steven R. Loomis) [#16939](https://github.com/nodejs/node/pull/16939)
- [[`8c6dc62dc4`](https://github.com/nodejs/node/commit/8c6dc62dc4)] - **doc**: dedupe links (Vse Mozhet Byt) [#18213](https://github.com/nodejs/node/pull/18213)
- [[`6b1a40e914`](https://github.com/nodejs/node/commit/6b1a40e914)] - **doc**: capitalize non-primitive types (Vse Mozhet Byt) [#18111](https://github.com/nodejs/node/pull/18111)
- [[`44bf0f4f12`](https://github.com/nodejs/node/commit/44bf0f4f12)] - **domain**: further abstract usage in C++ (Anatoli Papirovski) [#18291](https://github.com/nodejs/node/pull/18291)
- [[`35471bcfdf`](https://github.com/nodejs/node/commit/35471bcfdf)] - **domain**: fix error emit handling (Anatoli Papirovski) [#17588](https://github.com/nodejs/node/pull/17588)
- [[`28edc1db99`](https://github.com/nodejs/node/commit/28edc1db99)] - **events**: use Reflect.apply (Anatoli Papirovski) [#17456](https://github.com/nodejs/node/pull/17456)
- [[`3ae5cf205f`](https://github.com/nodejs/node/commit/3ae5cf205f)] - **events**: move domain handling from events to domain (vdeturckheim) [#17403](https://github.com/nodejs/node/pull/17403)
- [[`0568f755da`](https://github.com/nodejs/node/commit/0568f755da)] - **fs**: remove useless comments which duplicate names of variables (Sergey Golovin) [#18739](https://github.com/nodejs/node/pull/18739)
- [[`5b75572494`](https://github.com/nodejs/node/commit/5b75572494)] - **fs**: replace magic numbers by named constants (Sergey Golovin) [#18757](https://github.com/nodejs/node/pull/18757)
- [[`35ce3a8931`](https://github.com/nodejs/node/commit/35ce3a8931)] - **fs**: make URL paths no longer experimental (James M Snell) [#18591](https://github.com/nodejs/node/pull/18591)
- [[`34f49343ee`](https://github.com/nodejs/node/commit/34f49343ee)] - **fs**: fix stack overflow in fs.readdirSync (Joyee Cheung) [#18647](https://github.com/nodejs/node/pull/18647)
- [[`6ce8b24c6d`](https://github.com/nodejs/node/commit/6ce8b24c6d)] - **http**: simplify checkInvalidHeaderChar (Seth Brenith) [#18381](https://github.com/nodejs/node/pull/18381)
- [[`c247cb02a1`](https://github.com/nodejs/node/commit/c247cb02a1)] - **(SEMVER-MINOR)** **http**: add options to http.createServer() (Peter Marton) [#15752](https://github.com/nodejs/node/pull/15752)
- [[`935eac189d`](https://github.com/nodejs/node/commit/935eac189d)] - **http**: remove domain specific code (Anatoli Papirovski) [#18477](https://github.com/nodejs/node/pull/18477)
- [[`8b2a272772`](https://github.com/nodejs/node/commit/8b2a272772)] - **http**: process headers after setting up agent (Rod Vagg) [#16568](https://github.com/nodejs/node/pull/16568)
- [[`d76403985f`](https://github.com/nodejs/node/commit/d76403985f)] - **http**: switch on string values (Seth Brenith) [#18351](https://github.com/nodejs/node/pull/18351)
- [[`5e5276b418`](https://github.com/nodejs/node/commit/5e5276b418)] - **http2**: use `_final` instead of `on('finish')` (Anna Henningsen) [#18609](https://github.com/nodejs/node/pull/18609)
- [[`c0d6945f4c`](https://github.com/nodejs/node/commit/c0d6945f4c)] - **http2**: add req and res options to server creation (Peter Marton) [#15560](https://github.com/nodejs/node/pull/15560)
- [[`7806c51f30`](https://github.com/nodejs/node/commit/7806c51f30)] - **(SEMVER-MINOR)** **http2**: add http fallback options to .createServer (Peter Marton) [#15752](https://github.com/nodejs/node/pull/15752)
- [[`7c682f2fd0`](https://github.com/nodejs/node/commit/7c682f2fd0)] - **(SEMVER-MINOR)** **https**: add extra options to Agent#getName() (Jeff Principe) [#16402](https://github.com/nodejs/node/pull/16402)
- [[`74051c64aa`](https://github.com/nodejs/node/commit/74051c64aa)] - **inspector**: --inspect-brk for es modules (Guy Bedford) [#18194](https://github.com/nodejs/node/pull/18194)
- [[`741e82e710`](https://github.com/nodejs/node/commit/741e82e710)] - **(SEMVER-MINOR)** **lib**: allow process kill by signal number (Sam Roberts) [#16944](https://github.com/nodejs/node/pull/16944)
- [[`810925bc17`](https://github.com/nodejs/node/commit/810925bc17)] - **lib**: replace `eval` with `vm.runInThisContext` (Myles Borins) [#18623](https://github.com/nodejs/node/pull/18623)
- [[`16aeddda24`](https://github.com/nodejs/node/commit/16aeddda24)] - **lib**: switch to Number.isNaN (Ruben Bridgewater) [#18744](https://github.com/nodejs/node/pull/18744)
- [[`1557d93a2b`](https://github.com/nodejs/node/commit/1557d93a2b)] - **lib**: set process.execPath on OpenBSD (Aaron Bieber) [#18543](https://github.com/nodejs/node/pull/18543)
- [[`0a97e1d2c0`](https://github.com/nodejs/node/commit/0a97e1d2c0)] - **lib**: provide proper deprecation code (Ruben Bridgewater) [#18694](https://github.com/nodejs/node/pull/18694)
- [[`51a8e1d2d8`](https://github.com/nodejs/node/commit/51a8e1d2d8)] - **lib**: remove debugger dead code (Qingyan Li) [#18426](https://github.com/nodejs/node/pull/18426)
- [[`650ec2d8f1`](https://github.com/nodejs/node/commit/650ec2d8f1)] - **lib**: extract validation functions (Timothy O. Peters) [#18421](https://github.com/nodejs/node/pull/18421)
- [[`1fd1395ee9`](https://github.com/nodejs/node/commit/1fd1395ee9)] - **lib,doc**: revert format name to cjs over commonjs (Guy Bedford) [#18596](https://github.com/nodejs/node/pull/18596)
- [[`cb36b6733c`](https://github.com/nodejs/node/commit/cb36b6733c)] - **loader**: fix up #18394 (Gus Caplan) [#18509](https://github.com/nodejs/node/pull/18509)
- [[`afc87c22d0`](https://github.com/nodejs/node/commit/afc87c22d0)] - **module**: refactor loader (Gus Caplan) [#16874](https://github.com/nodejs/node/pull/16874)
- [[`d89f310127`](https://github.com/nodejs/node/commit/d89f310127)] - **module**: enable dynamic import flag for esmodules (Myles Borins) [#18387](https://github.com/nodejs/node/pull/18387)
- [[`00d5422c43`](https://github.com/nodejs/node/commit/00d5422c43)] - **module**: Set dynamic import callback (Jan Krems) [#15713](https://github.com/nodejs/node/pull/15713)
- [[`9c818cfa83`](https://github.com/nodejs/node/commit/9c818cfa83)] - **n-api**: remove extra reference from test (Gabriel Schulhof) [#18542](https://github.com/nodejs/node/pull/18542)
- [[`4bf8b6a62d`](https://github.com/nodejs/node/commit/4bf8b6a62d)] - **(SEMVER-MINOR)** **n-api**: add methods to open/close callback scope (Michael Dawson) [#18089](https://github.com/nodejs/node/pull/18089)
- [[`d2581120da`](https://github.com/nodejs/node/commit/d2581120da)] - **n-api**: wrap control flow macro in do/while (Ben Noordhuis) [#18532](https://github.com/nodejs/node/pull/18532)
- [[`ae8f5db1b1`](https://github.com/nodejs/node/commit/ae8f5db1b1)] - **n-api**: implement wrapping using private properties (Gabriel Schulhof) [#18311](https://github.com/nodejs/node/pull/18311)
- [[`a07cd06e6c`](https://github.com/nodejs/node/commit/a07cd06e6c)] - **n-api**: change assert ok check to notStrictEqual. (Aaron Kau) [#18414](https://github.com/nodejs/node/pull/18414)
- [[`b9ea4c46e5`](https://github.com/nodejs/node/commit/b9ea4c46e5)] - **net**: simplify net.Socket#end() (Anna Henningsen) [#18708](https://github.com/nodejs/node/pull/18708)
- [[`6ed4e690e4`](https://github.com/nodejs/node/commit/6ed4e690e4)] - **net**: remove Socket.prototoype.read (Anna Henningsen) [#18568](https://github.com/nodejs/node/pull/18568)
- [[`958f5eda9a`](https://github.com/nodejs/node/commit/958f5eda9a)] - **net**: remove redundant code from \_writeGeneric() (Luigi Pinca) [#18429](https://github.com/nodejs/node/pull/18429)
- [[`25ce45825f`](https://github.com/nodejs/node/commit/25ce45825f)] - **net,src**: refactor writeQueueSize tracking (Anatoli Papirovski) [#17650](https://github.com/nodejs/node/pull/17650)
- [[`3439635763`](https://github.com/nodejs/node/commit/3439635763)] - **path**: replace duplicate conditions by functions (Sergey Golovin) [#18693](https://github.com/nodejs/node/pull/18693)
- [[`5331454a30`](https://github.com/nodejs/node/commit/5331454a30)] - **path**: replace "magic" numbers by readable constants (Sergey Golovin) [#18654](https://github.com/nodejs/node/pull/18654)
- [[`0a47b98f04`](https://github.com/nodejs/node/commit/0a47b98f04)] - **perf_hooks**: add warning when too many entries in the timeline (James M Snell) [#18087](https://github.com/nodejs/node/pull/18087)
- [[`cec3d1ea80`](https://github.com/nodejs/node/commit/cec3d1ea80)] - **process**: fix reading zero-length env vars on win32 (Anna Henningsen) [#18463](https://github.com/nodejs/node/pull/18463)
- [[`36332eba27`](https://github.com/nodejs/node/commit/36332eba27)] - **readline**: use Date.now() and move test to parallel (Anatoli Papirovski) [#18563](https://github.com/nodejs/node/pull/18563)
- [[`9957916c26`](https://github.com/nodejs/node/commit/9957916c26)] - **src**: add nullptr check for session in DEBUG macro (Daniel Bevenius) [#18815](https://github.com/nodejs/node/pull/18815)
- [[`de3231c13a`](https://github.com/nodejs/node/commit/de3231c13a)] - **src**: factor out some common vm functions (Timothy Gu) [#17560](https://github.com/nodejs/node/pull/17560)
- [[`a258f6b5ce`](https://github.com/nodejs/node/commit/a258f6b5ce)] - **src**: flatten ContextifyContext (Gus Caplan) [#17560](https://github.com/nodejs/node/pull/17560)
- [[`a7419d0902`](https://github.com/nodejs/node/commit/a7419d0902)] - **src**: replace var for let / const. (alejandro estrada) [#18649](https://github.com/nodejs/node/pull/18649)
- [[`d190c9a41e`](https://github.com/nodejs/node/commit/d190c9a41e)] - **src**: add "icu::" prefix before ICU symbols (Steven R. Loomis)
- [[`3ec3c329c6`](https://github.com/nodejs/node/commit/3ec3c329c6)] - **src**: fix crypto.pbkdf2 callback error argument (BufoViridis) [#18458](https://github.com/nodejs/node/pull/18458)
- [[`464df6d9b5`](https://github.com/nodejs/node/commit/464df6d9b5)] - **(SEMVER-MINOR)** **src**: allow --perf-(basic-)?prof in NODE_OPTIONS (Leko) [#17600](https://github.com/nodejs/node/pull/17600)
- [[`43956e9600`](https://github.com/nodejs/node/commit/43956e9600)] - **src**: free memory before re-setting URLHost value (Ivan Filenko) [#18357](https://github.com/nodejs/node/pull/18357)
- [[`272fd2e334`](https://github.com/nodejs/node/commit/272fd2e334)] - **src**: fix vector subscript out of range (Anatoli Papirovski) [#18460](https://github.com/nodejs/node/pull/18460)
- [[`64c36d31b6`](https://github.com/nodejs/node/commit/64c36d31b6)] - **src, lib**: return promises from link (Gus Caplan) [#18394](https://github.com/nodejs/node/pull/18394)
- [[`ba46103291`](https://github.com/nodejs/node/commit/ba46103291)] - **stream**: fix misleading error message (Luigi Pinca) [#18604](https://github.com/nodejs/node/pull/18604)
- [[`27532f4e9a`](https://github.com/nodejs/node/commit/27532f4e9a)] - **stream**: cleanup() when unpiping all streams. (陈刚) [#18266](https://github.com/nodejs/node/pull/18266)
- [[`a4cc0fb1b6`](https://github.com/nodejs/node/commit/a4cc0fb1b6)] - **stream**: delete unused code (陈刚) [#18278](https://github.com/nodejs/node/pull/18278)
- [[`450f5f43bc`](https://github.com/nodejs/node/commit/450f5f43bc)] - **stream**: delete redundant code (陈刚) [#18145](https://github.com/nodejs/node/pull/18145)
- [[`560f657957`](https://github.com/nodejs/node/commit/560f657957)] - **stream**: delete redundant code (陈刚) [#18145](https://github.com/nodejs/node/pull/18145)
- [[`9af1e4b286`](https://github.com/nodejs/node/commit/9af1e4b286)] - **string_decoder**: reset decoder on end (Justin Ridgewell) [#18494](https://github.com/nodejs/node/pull/18494)
- [[`b02f4e1902`](https://github.com/nodejs/node/commit/b02f4e1902)] - **test**: http2 client settings invalid callback (Trivikram) [#18850](https://github.com/nodejs/node/pull/18850)
- [[`b7e6ac78fe`](https://github.com/nodejs/node/commit/b7e6ac78fe)] - **test**: http2 client ping errors (Trivikram) [#18849](https://github.com/nodejs/node/pull/18849)
- [[`f90490d475`](https://github.com/nodejs/node/commit/f90490d475)] - **test**: http2 client operations after destroy (Trivikram) [#18845](https://github.com/nodejs/node/pull/18845)
- [[`d73f214380`](https://github.com/nodejs/node/commit/d73f214380)] - **test**: refactor parallel/test-tls-pause (juggernaut451) [#18714](https://github.com/nodejs/node/pull/18714)
- [[`fa0b987a71`](https://github.com/nodejs/node/commit/fa0b987a71)] - **test**: refactor stream-\*-constructor-set-methods (Luigi Pinca) [#18817](https://github.com/nodejs/node/pull/18817)
- [[`dba5e35326`](https://github.com/nodejs/node/commit/dba5e35326)] - **test**: refactor parallel/test-tls-0-dns-altname (juggernaut451) [#18803](https://github.com/nodejs/node/pull/18803)
- [[`f960ad491c`](https://github.com/nodejs/node/commit/f960ad491c)] - **test**: add common.skipIfEslintMissing (Myles Borins) [#18807](https://github.com/nodejs/node/pull/18807)
- [[`dc456853f8`](https://github.com/nodejs/node/commit/dc456853f8)] - **test**: fix warnings in addon tests (Ali Ijaz Sheikh) [#18810](https://github.com/nodejs/node/pull/18810)
- [[`7874cb0f3c`](https://github.com/nodejs/node/commit/7874cb0f3c)] - **test**: refactor parallel/test-tls-addca (juggernaut451) [#18798](https://github.com/nodejs/node/pull/18798)
- [[`b3b5ac5169`](https://github.com/nodejs/node/commit/b3b5ac5169)] - **test**: refactor of test-tls-over-http-tunnel (juggernaut451) [#18784](https://github.com/nodejs/node/pull/18784)
- [[`849f5c31c8`](https://github.com/nodejs/node/commit/849f5c31c8)] - **test**: make tls test more rigorous (Ben Noordhuis) [#18792](https://github.com/nodejs/node/pull/18792)
- [[`cf10a94b48`](https://github.com/nodejs/node/commit/cf10a94b48)] - **test**: reduce benchmark test run time (juggernaut451) [#18787](https://github.com/nodejs/node/pull/18787)
- [[`8b5ca482fa`](https://github.com/nodejs/node/commit/8b5ca482fa)] - **test**: try to connect after server was closed (Leko) [#18257](https://github.com/nodejs/node/pull/18257)
- [[`75c691b788`](https://github.com/nodejs/node/commit/75c691b788)] - **test**: wrap countdown callback in common.mustCall (Bamieh) [#18506](https://github.com/nodejs/node/pull/18506)
- [[`ed55374b98`](https://github.com/nodejs/node/commit/ed55374b98)] - **test**: update a few tests to work on OpenBSD (Aaron Bieber) [#18543](https://github.com/nodejs/node/pull/18543)
- [[`7e75a78c5a`](https://github.com/nodejs/node/commit/7e75a78c5a)] - **test**: add lib path env when node_shared=true (Yihong Wang) [#18626](https://github.com/nodejs/node/pull/18626)
- [[`d6786d2110`](https://github.com/nodejs/node/commit/d6786d2110)] - **test**: add multiline repl input regression test (cjihrig) [#18718](https://github.com/nodejs/node/pull/18718)
- [[`18c493397f`](https://github.com/nodejs/node/commit/18c493397f)] - **test**: remove unnecessary timer (cjihrig) [#18719](https://github.com/nodejs/node/pull/18719)
- [[`5b88cb747e`](https://github.com/nodejs/node/commit/5b88cb747e)] - **test**: add crypto check to test-benchmark-tls (Daniel Bevenius) [#18724](https://github.com/nodejs/node/pull/18724)
- [[`6c041638c3`](https://github.com/nodejs/node/commit/6c041638c3)] - **test**: fix missing param in benchmark-timers (Anatoli Papirovski) [#18734](https://github.com/nodejs/node/pull/18734)
- [[`3362ae79df`](https://github.com/nodejs/node/commit/3362ae79df)] - **test**: fix and improve error message (Kevin Caulfield) [#18449](https://github.com/nodejs/node/pull/18449)
- [[`e9c9200aba`](https://github.com/nodejs/node/commit/e9c9200aba)] - **test**: add useful info to error msg and refactor (Chin Huang) [#18541](https://github.com/nodejs/node/pull/18541)
- [[`72d71594bd`](https://github.com/nodejs/node/commit/72d71594bd)] - **test**: fix flaky repl-timeout-throw (Santiago Gimeno) [#18692](https://github.com/nodejs/node/pull/18692)
- [[`2089814b67`](https://github.com/nodejs/node/commit/2089814b67)] - **test**: properly tag anonymous namespaces (Michael Dawson) [#18583](https://github.com/nodejs/node/pull/18583)
- [[`a667ac1665`](https://github.com/nodejs/node/commit/a667ac1665)] - **test**: fix flaky timers-block-eventloop test (Anatoli Papirovski) [#18567](https://github.com/nodejs/node/pull/18567)
- [[`f3e6c7636a`](https://github.com/nodejs/node/commit/f3e6c7636a)] - **test**: refactor test-http-abort-before-end (cjihrig) [#18508](https://github.com/nodejs/node/pull/18508)
- [[`0277993f49`](https://github.com/nodejs/node/commit/0277993f49)] - **test**: improve error message output (Bhavani Shankar) [#18498](https://github.com/nodejs/node/pull/18498)
- [[`30a233cfce`](https://github.com/nodejs/node/commit/30a233cfce)] - **test**: fix flaky test-http2-session-unref (Anatoli Papirovski) [#18589](https://github.com/nodejs/node/pull/18589)
- [[`ef2d9c2c54`](https://github.com/nodejs/node/commit/ef2d9c2c54)] - **test**: do not check TXT content in test-dns-any (Joyee Cheung) [#18547](https://github.com/nodejs/node/pull/18547)
- [[`10dc25df83`](https://github.com/nodejs/node/commit/10dc25df83)] - **test**: improve tests for test-http-url.parse (Weijia Wang) [#18523](https://github.com/nodejs/node/pull/18523)
- [[`a13fbdd4c3`](https://github.com/nodejs/node/commit/a13fbdd4c3)] - **test**: remove destructor from node_test_fixture (Daniel Bevenius) [#18524](https://github.com/nodejs/node/pull/18524)
- [[`52aeb2a070`](https://github.com/nodejs/node/commit/52aeb2a070)] - **test**: verify the shell option works properly on execFile (jvelezpo) [#18384](https://github.com/nodejs/node/pull/18384)
- [[`0d390f7bdf`](https://github.com/nodejs/node/commit/0d390f7bdf)] - **test**: add test for tls benchmarks (Anatoli Papirovski) [#18489](https://github.com/nodejs/node/pull/18489)
- [[`da0d776593`](https://github.com/nodejs/node/commit/da0d776593)] - **test**: mark test-inspector-stop-profile-after-done flaky (Myles Borins) [#18491](https://github.com/nodejs/node/pull/18491)
- [[`8c9b41aaee`](https://github.com/nodejs/node/commit/8c9b41aaee)] - **test**: show pending exception error in napi tests (Ben Wilcox) [#18413](https://github.com/nodejs/node/pull/18413)
- [[`f6c9a2bc47`](https://github.com/nodejs/node/commit/f6c9a2bc47)] - **test**: speed up parallel/test-tls-session-cache (Anna Henningsen) [#18424](https://github.com/nodejs/node/pull/18424)
- [[`6b74064e65`](https://github.com/nodejs/node/commit/6b74064e65)] - **test**: fix flaky test-http-dns-error (Bryan English) [#16534](https://github.com/nodejs/node/pull/16534)
- [[`eb252527e5`](https://github.com/nodejs/node/commit/eb252527e5)] - **test**: move tmpdir to submodule of common (Rich Trott) [#17856](https://github.com/nodejs/node/pull/17856)
- [[`b5267a6926`](https://github.com/nodejs/node/commit/b5267a6926)] - **test**: force context allocation in test module (Yang Guo) [#18312](https://github.com/nodejs/node/pull/18312)
- [[`cc8091448b`](https://github.com/nodejs/node/commit/cc8091448b)] - **test**: fix flaky cluster unix socket test (Ben Noordhuis) [#17407](https://github.com/nodejs/node/pull/17407)
- [[`19abee149d`](https://github.com/nodejs/node/commit/19abee149d)] - **test**: fix a bug & lint issues in inspector-helper (Anatoli Papirovski) [#18293](https://github.com/nodejs/node/pull/18293)
- [[`b5752ee6a4`](https://github.com/nodejs/node/commit/b5752ee6a4)] - **test**: fix require-deps-deprecation for installed deps (Benjamin Zaslavsky) [#17848](https://github.com/nodejs/node/pull/17848)
- [[`66f8d346b8`](https://github.com/nodejs/node/commit/66f8d346b8)] - **test,benchmark,doc**: enable dot-notation rule (Ruben Bridgewater) [#18749](https://github.com/nodejs/node/pull/18749)
- [[`146e8ac83a`](https://github.com/nodejs/node/commit/146e8ac83a)] - **timers**: remove domain specific code (Anatoli Papirovski) [#18477](https://github.com/nodejs/node/pull/18477)
- [[`f8f1423e7a`](https://github.com/nodejs/node/commit/f8f1423e7a)] - **tls**: tls_wrap causes debug assert in vector (Kyle Farnung) [#18830](https://github.com/nodejs/node/pull/18830)
- [[`3725d4ccea`](https://github.com/nodejs/node/commit/3725d4ccea)] - **tls**: remove cleartext input data queue (Anna Henningsen) [#17883](https://github.com/nodejs/node/pull/17883)
- [[`aa241eda98`](https://github.com/nodejs/node/commit/aa241eda98)] - **tools**: custom eslint autofix for inspector-check.js (Shobhit Chittora) [#16646](https://github.com/nodejs/node/pull/16646)
- [[`3f865ea6cf`](https://github.com/nodejs/node/commit/3f865ea6cf)] - **tools**: auto fix custom crypto-check eslint rule (Shobhit Chittora) [#16647](https://github.com/nodejs/node/pull/16647)
- [[`ae3398aad6`](https://github.com/nodejs/node/commit/ae3398aad6)] - **tools**: fix eslint isRequired (Ruben Bridgewater) [#18729](https://github.com/nodejs/node/pull/18729)
- [[`a33dc81b2f`](https://github.com/nodejs/node/commit/a33dc81b2f)] - **tools**: add fixer for prefer-assert-iferror.js (Shobhit Chittora) [#16648](https://github.com/nodejs/node/pull/16648)
- [[`aabbdc84c2`](https://github.com/nodejs/node/commit/aabbdc84c2)] - **tools**: add .mjs linting for Windows (Vse Mozhet Byt) [#18569](https://github.com/nodejs/node/pull/18569)
- [[`e00bb1657f`](https://github.com/nodejs/node/commit/e00bb1657f)] - **tools**: non-Ascii linter for /lib only (Sarat Addepalli) [#18043](https://github.com/nodejs/node/pull/18043)
- [[`4f4bfbecbf`](https://github.com/nodejs/node/commit/4f4bfbecbf)] - **tools**: auto fix custom eslint rule (Shobhit Chittora) [#16652](https://github.com/nodejs/node/pull/16652)
- [[`ef45bb4305`](https://github.com/nodejs/node/commit/ef45bb4305)] - **tools**: fix icu readme lint error (Anatoli Papirovski) [#18445](https://github.com/nodejs/node/pull/18445)
- [[`1767ef06f3`](https://github.com/nodejs/node/commit/1767ef06f3)] - **url**: simplify constructor URLSearchParams. Remove needless check null (Mihail Bodrov) [#18700](https://github.com/nodejs/node/pull/18700)
- [[`07e4ba2519`](https://github.com/nodejs/node/commit/07e4ba2519)] - **url**: simplify loop in parser (Tobias Nießen) [#18468](https://github.com/nodejs/node/pull/18468)
- [[`c8f729f7a3`](https://github.com/nodejs/node/commit/c8f729f7a3)] - **v8**: add missing ',' in OpenBSD's 'sources' section. (Aaron Bieber) [#18448](https://github.com/nodejs/node/pull/18448)
- [[`02afdbc5c6`](https://github.com/nodejs/node/commit/02afdbc5c6)] - **vm**: flip Module#link's signature (Gus Caplan) [#18471](https://github.com/nodejs/node/pull/18471)
- [[`1cbd76a100`](https://github.com/nodejs/node/commit/1cbd76a100)] - **vm**: add modules (Gus Caplan) [#17560](https://github.com/nodejs/node/pull/17560)
- [[`c34e2f4fc5`](https://github.com/nodejs/node/commit/c34e2f4fc5)] - **win, build**: fix intl-none option (Birunthan Mohanathas) [#18292](https://github.com/nodejs/node/pull/18292)

Windows 32-bit Installer: https://nodejs.org/dist/v9.6.0/node-v9.6.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v9.6.0/node-v9.6.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v9.6.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v9.6.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v9.6.0/node-v9.6.0.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v9.6.0/node-v9.6.0-darwin-x64.tar.gz \
Linux 32-bit Binary: https://nodejs.org/dist/v9.6.0/node-v9.6.0-linux-x86.tar.xz \
Linux 64-bit Binary: https://nodejs.org/dist/v9.6.0/node-v9.6.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v9.6.0/node-v9.6.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v9.6.0/node-v9.6.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v9.6.0/node-v9.6.0-aix-ppc64.tar.gz \
SmartOS 32-bit Binary: https://nodejs.org/dist/v9.6.0/node-v9.6.0-sunos-x86.tar.xz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v9.6.0/node-v9.6.0-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v9.6.0/node-v9.6.0-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v9.6.0/node-v9.6.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v9.6.0/node-v9.6.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v9.6.0/node-v9.6.0.tar.gz \
Other release files: https://nodejs.org/dist/v9.6.0/ \
Documentation: https://nodejs.org/docs/v9.6.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

67b25b3e29b5db4a73b4100f03dbb2216115d828c12d18c3978afb3e22525ae8  node-v9.6.0-aix-ppc64.tar.gz
4f2001075b724791bf652b5432073aaaaec03cb00b17cda054ab0a05126035fe  node-v9.6.0-darwin-x64.tar.gz
049febe6c37e9b3a7f3edbc4d4bf887a5e725c61a536a949bab6f145ee42bdf2  node-v9.6.0-darwin-x64.tar.xz
cda2631169c25e62919b4cf177e2c7b801fa54183b7d1ab6841687f879b14479  node-v9.6.0-headers.tar.gz
d4da9bc0e5ab8506658bb808a1e7d053abb456d185ccc907f6f9d0a5ae72b23a  node-v9.6.0-headers.tar.xz
8a96c6facbf9ddbd840e5d34664c5cd2835dbd4081fda4dba8a818c1766410ca  node-v9.6.0-linux-arm64.tar.gz
eb10815b1d21e8f5111b90c15b19050279b44666d08a12f26906271e9f6b02dd  node-v9.6.0-linux-arm64.tar.xz
60660f02e603d5f4349a273f1276670ff30744cc7b8e57526e675fd7590d6d1a  node-v9.6.0-linux-armv6l.tar.gz
0db6ef7df084b313b76e22e861226872f8ef5156749ae1245085ed9fad2e8f66  node-v9.6.0-linux-armv6l.tar.xz
70f77964bac32b36c51bee14814acbc47a5bfc9dba082c6b95aae7a3abc98b05  node-v9.6.0-linux-armv7l.tar.gz
b386569d78063dbc4a2d4de61040e411f025383aa3f96aec75303670545c1799  node-v9.6.0-linux-armv7l.tar.xz
08d5bf2e145d9d1d6589a2b4862d084919dc5720f017f40fa724982705f5269c  node-v9.6.0-linux-ppc64le.tar.gz
439a043279f35f5f0ed658c49fa025656204fd510a674e3499c2922d1a2fbb47  node-v9.6.0-linux-ppc64le.tar.xz
1905f33c0e7d438a8a7e231bac4356ade10be7873d821881d04e1aec72c03587  node-v9.6.0-linux-s390x.tar.gz
6b11e570a334034d1f6d293707ff8e955b3fc6791db7db6542eb33bcd17fab51  node-v9.6.0-linux-s390x.tar.xz
90628529c45623b312dde6b4dfdd18dc7f94fcc0e4db9cbd21b5d5f2fa80c005  node-v9.6.0-linux-x64.tar.gz
24b8670c819278135b4b8851d880615dee8458fdb5f5390ed0c88c383377f5d3  node-v9.6.0-linux-x64.tar.xz
2d7be0f3c307d8c1513429b0e88d66ae616df8c814d6204936cc17d18f34301e  node-v9.6.0-linux-x86.tar.gz
0e7b6f763b6c0668ed18ed50d1d5247d931d5f3cd096fb7a9d550e3cf651f752  node-v9.6.0-linux-x86.tar.xz
0b2c23799714eea89c015750bf76c645ee528ed549024d2fff770ded255b18cb  node-v9.6.0.pkg
b82c2d431edf9451bd434c132cbb92dc220e3caf0e5c7ca5df613bc53f91de7b  node-v9.6.0-sunos-x64.tar.gz
717da5b5defe82e84e14fc7b2b7e8b410cd5a47b1554425edc7f422b1e470883  node-v9.6.0-sunos-x64.tar.xz
e22952588b8f99d88d51beec7c296c47ebdfae0add45a7d56d173468df17185f  node-v9.6.0-sunos-x86.tar.gz
355ae0b80d97e3e94728aa5d009bd1221b6c05a28944372ecd3629aa346fb7fe  node-v9.6.0-sunos-x86.tar.xz
3f068bb0866b19ea3ec830ed827b2a45db9fa7866aab49634900b1df59c1a802  node-v9.6.0.tar.gz
1766db5eb720d84e5de6c50f14930643391aebebac3c86389a899351127b320b  node-v9.6.0.tar.xz
3e32a113877948e524ada3d0b09cc8ae517476c7513f31a641426d0187b3ec27  node-v9.6.0-win-x64.7z
5f1da1ef6ba776449900c931e7acc3b60e47f3080425bf0e6a1c0c5a07dbd6fd  node-v9.6.0-win-x64.zip
ded7448dc042df9c6dff163feb04cf89a851d1af05afee4d520f8caa7bbcc51c  node-v9.6.0-win-x86.7z
13d522003242cad9c16f0ffb606476d2ba0a99e4ba50915d5ccc33a1ba4a3e1d  node-v9.6.0-win-x86.zip
140e40ee7066da362ea9f4e3b066e69ea098e2d67334b41233b0e1f1615d6588  node-v9.6.0-x64.msi
eadc16162a06de6feacb3bb69a6fcdc67f01f4d18102a268ac237faf9cf32fb7  node-v9.6.0-x86.msi
a0973c95abf77e0030a5b5a28418ae72acb0516f6db5063e26a9c9b55ddfc63b  win-x64/node.exe
ffa4a11a7cb5130aa779109d82be0c9fc30dcbc380ab13566234ecd93782b8a7  win-x64/node.lib
bde1c56a4448d65f31236a960cf567676a4293024a277161a2cca1e81fd7c706  win-x64/node_pdb.7z
ce1455d24171a085cbe463ade73ae15a6ff384064f472d292a572a68ab8fd8a1  win-x64/node_pdb.zip
74d87a65ac9752896a7adf8afe7ea6eb2086fc47e51d895074535eda623a427a  win-x86/node.exe
b28c588ec4147e6193f4b0e3a80f4a1d71cbe63ebd4d8aec5451313bc5f6560d  win-x86/node.lib
9c90bb9486c05abc8a5e5d3414a61b9fb1bfa83fa19766dd746a045ed6993bb1  win-x86/node_pdb.7z
cc0078b983d75f6afb2b21c0629a3d2cdb363eb6a2db4e161a36cb3b14fbb077  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQEzBAEBCAAdFiEEDv/hvO/ZyE49CYFSkzsB9AtcqUYFAlqgmDAACgkQkzsB9Atc
qUY6gAf+OPTV0Ezrp/H/CMOBYfh7novr59f9VHbKcYOx8yIma5aqKdk0D5d/Rmv5
4MiIyi/6Jxo0DqDDe54SRE4UUcj13bLdTUhWhW9DiwKKhgNx5ZCvE0dOlJ86KNR9
BWycpi+DRwDcMEtD9z1Rp9IzGOhMi+366/e2yIZpPDhQnqbpik+jmgqR2Mo1AENi
LCF4UijWPVFsL82GTlK+ZKInPfHm0nitgAwX2DN//ikuhBo87X61w96MR7zjyORE
LxJX46tyLkjkTmlZtqd4ziUijZnnkcOtw7gk7VgPQDxr0D3i2gGPHkecugOVlAFm
2VmlZTdTznrlm46s28n4carn7fzVkw==
=quz8
-----END PGP SIGNATURE-----

```
