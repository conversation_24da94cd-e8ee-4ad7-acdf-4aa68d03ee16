---
date: '2018-10-23T18:09:16.575Z'
category: release
title: Node v11.0.0 (Current)
layout: blog-post
author: <PERSON>
---

Node.js 11.0.0 is here! This is the newest Node.js Current Release line with a
focus primarily on improving internals, performance, and an update to V8 7.0.

### Notable Changes

- Build
  - FreeBSD 10 is no longer supported. [#22617](https://github.com/nodejs/node/pull/22617)
- `child_process`
  - The default value of the `windowsHide` option has been changed to `true`. [#21316](https://github.com/nodejs/node/pull/21316)
- `console`
  - `console.countReset()` will emit a warning if the timer being reset does not exist. [#21649](https://github.com/nodejs/node/pull/21649)
  - `console.time()` will no longer reset a timer if it already exists. [#20442](https://github.com/nodejs/node/pull/20442)
- Dependencies
  - V8 has been updated to 7.0. [#22754](https://github.com/nodejs/node/pull/22754)
- `fs`
  - The `fs.read()` method now requires a callback. [#22146](https://github.com/nodejs/node/pull/22146)
  - The previously deprecated `fs.SyncWriteStream` utility has been removed.[#20735](https://github.com/nodejs/node/pull/20735)
- `http`
  - The `http`, `https`, and `tls` modules now use the WHATWG URL parser by default. [#20270](https://github.com/nodejs/node/pull/20270)
- General
  - Use of `process.binding()` has been deprecated. Userland code using `process.binding()` should re-evaluate that use and begin migrating. If there are no supported API alternatives, please open an issue in the Node.js GitHub repository so that a suitable alternative may be discussed.
  - An experimental implementation of `queueMicrotask()` has been added. [#22951](https://github.com/nodejs/node/pull/22951)
- Internal
  - Windows performance-counter support has been removed. [#22485](https://github.com/nodejs/node/pull/22485)
  - The `--expose-http2` command-line option has been removed. [#20887](https://github.com/nodejs/node/pull/20887)
- Timers
  - Interval timers will be rescheduled even if previous interval threw an error. [#20002](https://github.com/nodejs/node/pull/20002)
  - `nextTick` queue will be run after each immediate and timer. [#22842](https://github.com/nodejs/node/pull/22842)
- `util`
  - The WHATWG `TextEncoder` and `TextDecoder` are now globals. [#22281](https://github.com/nodejs/node/pull/22281)
  - `util.inspect()` output size is limited to 128 MB by default. [#22756](https://github.com/nodejs/node/pull/22756)
  - A runtime warning will be emitted when `NODE_DEBUG` is set for either `http` or `http2`. [#21914](https://github.com/nodejs/node/pull/21914)

### Semver-Major Commits

- [[`0518b9edf3`](https://github.com/nodejs/node/commit/0518b9edf3)] - **(SEMVER-MAJOR)** **assert**: multiple improvements (Ruben Bridgewater) [#21628](https://github.com/nodejs/node/pull/21628)
- [[`21c3a402d4`](https://github.com/nodejs/node/commit/21c3a402d4)] - **(SEMVER-MAJOR)** **assert**: validate input stricter (Ruben Bridgewater) [#20481](https://github.com/nodejs/node/pull/20481)
- [[`439b75b9c0`](https://github.com/nodejs/node/commit/439b75b9c0)] - **(SEMVER-MAJOR)** **assert, util**: \*DeepEqual() handles ArrayBuffers (Caleb Sander) [#22266](https://github.com/nodejs/node/pull/22266)
- [[`5d95542212`](https://github.com/nodejs/node/commit/5d95542212)] - **(SEMVER-MAJOR)** **buffer**: move process.binding('buffer') to internalBinding (Weijia Wang) [#22370](https://github.com/nodejs/node/pull/22370)
- [[`8fb6bce3a0`](https://github.com/nodejs/node/commit/8fb6bce3a0)] - **(SEMVER-MAJOR)** **buffer**: unconditionally use internalBinding (cjihrig) [#23234](https://github.com/nodejs/node/pull/23234)
- [[`755520c4c3`](https://github.com/nodejs/node/commit/755520c4c3)] - **(SEMVER-MAJOR)** **buffer**: show hidden item count (Ruben Bridgewater) [#22289](https://github.com/nodejs/node/pull/22289)
- [[`60b5b38b48`](https://github.com/nodejs/node/commit/60b5b38b48)] - **(SEMVER-MAJOR)** **buffer**: do not always use defaults (Ruben Bridgewater) [#20054](https://github.com/nodejs/node/pull/20054)
- [[`b3b3f53a33`](https://github.com/nodejs/node/commit/b3b3f53a33)] - **(SEMVER-MAJOR)** **build**: exclude npm test directories on Windows (Richard Lau) [#23001](https://github.com/nodejs/node/pull/23001)
- [[`dd296a8344`](https://github.com/nodejs/node/commit/dd296a8344)] - **(SEMVER-MAJOR)** **build**: reset embedder string to "-node.0" (Michaël Zasso) [#22754](https://github.com/nodejs/node/pull/22754)
- [[`4b25ef5341`](https://github.com/nodejs/node/commit/4b25ef5341)] - **(SEMVER-MAJOR)** **build**: reset embedder string to "-node.0" (Michaël Zasso) [#21983](https://github.com/nodejs/node/pull/21983)
- [[`c0fb95d700`](https://github.com/nodejs/node/commit/c0fb95d700)] - **(SEMVER-MAJOR)** **build**: stop supporting FreeBSD 10 (Michaël Zasso) [#22617](https://github.com/nodejs/node/pull/22617)
- [[`4b47d2907d`](https://github.com/nodejs/node/commit/4b47d2907d)] - **(SEMVER-MAJOR)** **build**: do not copy v8-inspector\* headers ar part of install (Alexey Kozyatinskiy) [#22586](https://github.com/nodejs/node/pull/22586)
- [[`2d4dd10829`](https://github.com/nodejs/node/commit/2d4dd10829)] - **(SEMVER-MAJOR)** **build**: add '-z relro -z now' linker flags (Shao,Ting) [#20513](https://github.com/nodejs/node/pull/20513)
- [[`9c9c01f183`](https://github.com/nodejs/node/commit/9c9c01f183)] - **(SEMVER-MAJOR)** **child_process**: move process.binding('spawn_sync') to internalBinding (Anto Aravinth) [#22260](https://github.com/nodejs/node/pull/22260)
- [[`af883e1f99`](https://github.com/nodejs/node/commit/af883e1f99)] - **(SEMVER-MAJOR)** **child_process**: fix switches for alternative shells on Windows (Tessei Kameyama) [#21943](https://github.com/nodejs/node/pull/21943)
- [[`56cf058878`](https://github.com/nodejs/node/commit/56cf058878)] - **(SEMVER-MAJOR)** **child_process**: make process_wrap binding internal (cjihrig) [#22479](https://github.com/nodejs/node/pull/22479)
- [[`420d8afe3d`](https://github.com/nodejs/node/commit/420d8afe3d)] - **(SEMVER-MAJOR)** **child_process**: change windowsHide default to true (cjihrig) [#21316](https://github.com/nodejs/node/pull/21316)
- [[`d4164ca559`](https://github.com/nodejs/node/commit/d4164ca559)] - **(SEMVER-MAJOR)** **console**: console.countReset() should emit warning (Dominic Farolino) [#21649](https://github.com/nodejs/node/pull/21649)
- [[`a59826403a`](https://github.com/nodejs/node/commit/a59826403a)] - **(SEMVER-MAJOR)** **console**: console.time() should not reset a timer when it exists (Gus Caplan) [#20442](https://github.com/nodejs/node/pull/20442)
- [[`90e8f79f65`](https://github.com/nodejs/node/commit/90e8f79f65)] - **(SEMVER-MAJOR)** **constants**: freeze the constants object (Bryan English) [#19813](https://github.com/nodejs/node/pull/19813)
- [[`058c5b81cd`](https://github.com/nodejs/node/commit/058c5b81cd)] - **(SEMVER-MAJOR)** **crypto**: do not allow multiple calls to setAuthTag (Tobias Nießen) [#22931](https://github.com/nodejs/node/pull/22931)
- [[`19ad6b8f72`](https://github.com/nodejs/node/commit/19ad6b8f72)] - **(SEMVER-MAJOR)** **crypto**: deprecate digest == null in PBKDF2 (Tobias Nießen) [#22861](https://github.com/nodejs/node/pull/22861)
- [[`0ade10df79`](https://github.com/nodejs/node/commit/0ade10df79)] - **(SEMVER-MAJOR)** **crypto**: hide native handles from JS modules (Tobias Nießen) [#22747](https://github.com/nodejs/node/pull/22747)
- [[`503fd55a35`](https://github.com/nodejs/node/commit/503fd55a35)] - **(SEMVER-MAJOR)** **crypto**: make \_toBuf non-enumerable (Tobias Nießen) [#22551](https://github.com/nodejs/node/pull/22551)
- [[`221df2286d`](https://github.com/nodejs/node/commit/221df2286d)] - **(SEMVER-MAJOR)** **crypto**: deprecate aliases for randomBytes (Tobias Nießen) [#22519](https://github.com/nodejs/node/pull/22519)
- [[`50aa85dc9b`](https://github.com/nodejs/node/commit/50aa85dc9b)] - **(SEMVER-MAJOR)** **crypto**: deprecate \_toBuf (Tobias Nießen) [#22501](https://github.com/nodejs/node/pull/22501)
- [[`eab916c4e8`](https://github.com/nodejs/node/commit/eab916c4e8)] - **(SEMVER-MAJOR)** **crypto**: move process.binding('tls_wrap') internal (Daniel Bevenius) [#22429](https://github.com/nodejs/node/pull/22429)
- [[`bf5cc3bf1a`](https://github.com/nodejs/node/commit/bf5cc3bf1a)] - **(SEMVER-MAJOR)** **crypto**: move process.binding('crypto') to internal (Daniel Bevenius) [#22426](https://github.com/nodejs/node/pull/22426)
- [[`39dd3a4430`](https://github.com/nodejs/node/commit/39dd3a4430)] - **(SEMVER-MAJOR)** **crypto**: deprecate useless crypto APIs (Tobias Nießen) [#22126](https://github.com/nodejs/node/pull/22126)
- [[`933d8eb689`](https://github.com/nodejs/node/commit/933d8eb689)] - **(SEMVER-MAJOR)** **crypto**: move createCipher to runtime deprecation (Tobias Nießen) [#22089](https://github.com/nodejs/node/pull/22089)
- [[`d2ee7d64aa`](https://github.com/nodejs/node/commit/d2ee7d64aa)] - **(SEMVER-MAJOR)** **crypto**: remove deprecated legacy API (Antoine du HAMEL) [#21153](https://github.com/nodejs/node/pull/21153)
- [[`faf449ca04`](https://github.com/nodejs/node/commit/faf449ca04)] - **(SEMVER-MAJOR)** **crypto**: throw in setAuthTag on invalid length (Tobias Nießen) [#20040](https://github.com/nodejs/node/pull/20040)
- [[`d81a7b4baa`](https://github.com/nodejs/node/commit/d81a7b4baa)] - **(SEMVER-MAJOR)** **crypto**: throw on invalid authentication tag length (Tobias Nießen) [#17825](https://github.com/nodejs/node/pull/17825)
- [[`2f9775995f`](https://github.com/nodejs/node/commit/2f9775995f)] - **(SEMVER-MAJOR)** **crypto**: move Decipher.finaltol to End-of-Life (Tobias Nießen) [#19941](https://github.com/nodejs/node/pull/19941)
- [[`083d1012c7`](https://github.com/nodejs/node/commit/083d1012c7)] - **(SEMVER-MAJOR)** **deps**: cherry-pick b0af309 from upstream V8 (Anna Henningsen) [#23415](https://github.com/nodejs/node/pull/23415)
- [[`dca0300a86`](https://github.com/nodejs/node/commit/dca0300a86)] - **(SEMVER-MAJOR)** **deps**: cherry-pick 2363cdf from upstream V8 (Michaël Zasso) [#22754](https://github.com/nodejs/node/pull/22754)
- [[`1da9d60003`](https://github.com/nodejs/node/commit/1da9d60003)] - **(SEMVER-MAJOR)** **deps**: update v8.gyp (Michaël Zasso) [#22754](https://github.com/nodejs/node/pull/22754)
- [[`0e7ddbd3d7`](https://github.com/nodejs/node/commit/0e7ddbd3d7)] - **(SEMVER-MAJOR)** **deps**: update V8 to 7.0.276.20 (Michaël Zasso) [#22754](https://github.com/nodejs/node/pull/22754)
- [[`a3f258c769`](https://github.com/nodejs/node/commit/a3f258c769)] - **(SEMVER-MAJOR)** **deps**: cherry-pick a8f6869 from upstream V8 (Michaël Zasso) [#21983](https://github.com/nodejs/node/pull/21983)
- [[`fc1770b0d1`](https://github.com/nodejs/node/commit/fc1770b0d1)] - **(SEMVER-MAJOR)** **deps**: cherry-pick bf5ea81 from upstream V8 (Michaël Zasso) [#21983](https://github.com/nodejs/node/pull/21983)
- [[`7766baf943`](https://github.com/nodejs/node/commit/7766baf943)] - **(SEMVER-MAJOR)** **deps**: cherry-pick ba752ea from upstream V8 (Michaël Zasso) [#21983](https://github.com/nodejs/node/pull/21983)
- [[`8dc159658c`](https://github.com/nodejs/node/commit/8dc159658c)] - **(SEMVER-MAJOR)** **deps**: cherry-pick c608122 from upstream V8 (Michaël Zasso) [#21983](https://github.com/nodejs/node/pull/21983)
- [[`5bb985d331`](https://github.com/nodejs/node/commit/5bb985d331)] - **(SEMVER-MAJOR)** **deps**: cherry-pick 0dd3390 from upstream V8 (Michaël Zasso) [#21983](https://github.com/nodejs/node/pull/21983)
- [[`f04ab3c756`](https://github.com/nodejs/node/commit/f04ab3c756)] - **(SEMVER-MAJOR)** **deps**: update v8.gyp (Michaël Zasso) [#21983](https://github.com/nodejs/node/pull/21983)
- [[`586db2414a`](https://github.com/nodejs/node/commit/586db2414a)] - **(SEMVER-MAJOR)** **deps**: update V8 to 6.9.427.22 (Michaël Zasso) [#21983](https://github.com/nodejs/node/pull/21983)
- [[`c8950cdabc`](https://github.com/nodejs/node/commit/c8950cdabc)] - **(SEMVER-MAJOR)** **dgram**: make process.binding('udp_wrap') internal (cjihrig) [#22475](https://github.com/nodejs/node/pull/22475)
- [[`3ce6bc3b50`](https://github.com/nodejs/node/commit/3ce6bc3b50)] - **(SEMVER-MAJOR)** **dgram**: remove unnecessary fd property from Socket (Ouyang Yadong) [#21684](https://github.com/nodejs/node/pull/21684)
- [[`fe069cca6a`](https://github.com/nodejs/node/commit/fe069cca6a)] - **(SEMVER-MAJOR)** **dgram**: deprecate all previous private APIs (cjihrig) [#22011](https://github.com/nodejs/node/pull/22011)
- [[`2bea9cefbc`](https://github.com/nodejs/node/commit/2bea9cefbc)] - **(SEMVER-MAJOR)** **dgram**: implement socket.bind({ fd }) (Ouyang Yadong) [#21745](https://github.com/nodejs/node/pull/21745)
- [[`8b2e77c248`](https://github.com/nodejs/node/commit/8b2e77c248)] - **(SEMVER-MAJOR)** **dns**: deprecate passing falsy hostname to dns.lookup (Ouyang Yadong) [#23173](https://github.com/nodejs/node/pull/23173)
- [[`8b0c482647`](https://github.com/nodejs/node/commit/8b0c482647)] - **(SEMVER-MAJOR)** **dns**: make process.binding('cares_wrap') internal (cjihrig) [#22474](https://github.com/nodejs/node/pull/22474)
- [[`4e1c4e8193`](https://github.com/nodejs/node/commit/4e1c4e8193)] - **(SEMVER-MAJOR)** **dns**: type check for dns.setServers argument. (Masashi Hirano) [#21944](https://github.com/nodejs/node/pull/21944)
- [[`a158d412b3`](https://github.com/nodejs/node/commit/a158d412b3)] - **(SEMVER-MAJOR)** **dns**: report out of memory properly (Ruben Bridgewater) [#20317](https://github.com/nodejs/node/pull/20317)
- [[`c267639daa`](https://github.com/nodejs/node/commit/c267639daa)] - **(SEMVER-MAJOR)** **doc**: clarify ciphers option format (Brian White) [#21557](https://github.com/nodejs/node/pull/21557)
- [[`985d180855`](https://github.com/nodejs/node/commit/985d180855)] - **(SEMVER-MAJOR)** **doc**: move support for invalid GCM tags to EOL (Tobias Nießen) [#17825](https://github.com/nodejs/node/pull/17825)
- [[`cf350856cf`](https://github.com/nodejs/node/commit/cf350856cf)] - **(SEMVER-MAJOR)** **doc**: note that setAuthTag throws on invalid length (Tobias Nießen) [#17825](https://github.com/nodejs/node/pull/17825)
- [[`f8d69911be`](https://github.com/nodejs/node/commit/f8d69911be)] - **(SEMVER-MAJOR)** **errors**: use ERR_OUT_OF_RANGE for index errors (Rich Trott) [#22969](https://github.com/nodejs/node/pull/22969)
- [[`186857f15c`](https://github.com/nodejs/node/commit/186857f15c)] - **(SEMVER-MAJOR)** **errors**: remove ERR_INVALID_ARRAY_LENGTH (Ruben Bridgewater) [#20484](https://github.com/nodejs/node/pull/20484)
- [[`6e942e7353`](https://github.com/nodejs/node/commit/6e942e7353)] - **(SEMVER-MAJOR)** **fs**: make fs_event_wrap binding internal (cjihrig) [#22480](https://github.com/nodejs/node/pull/22480)
- [[`8e1b6e7718`](https://github.com/nodejs/node/commit/8e1b6e7718)] - **(SEMVER-MAJOR)** **fs**: require callback in read (Ruben Bridgewater) [#22146](https://github.com/nodejs/node/pull/22146)
- [[`42bded83e8`](https://github.com/nodejs/node/commit/42bded83e8)] - **(SEMVER-MAJOR)** **fs**: throw ERR_INVALID_ARG_VALUE when buffer being written is empty (AdityaSrivast) [#21262](https://github.com/nodejs/node/pull/21262)
- [[`7bd48896e9`](https://github.com/nodejs/node/commit/7bd48896e9)] - **(SEMVER-MAJOR)** **fs**: move SyncWriteStream to end-of-life (James M Snell) [#20735](https://github.com/nodejs/node/pull/20735)
- [[`19374fd25b`](https://github.com/nodejs/node/commit/19374fd25b)] - **(SEMVER-MAJOR)** **fs**: improve argument handling for ReadStream (Ujjwal Sharma) [#19898](https://github.com/nodejs/node/pull/19898)
- [[`f22c7c10ca`](https://github.com/nodejs/node/commit/f22c7c10ca)] - **(SEMVER-MAJOR)** **http**: always emit close on req and res (Robert Nagy) [#20611](https://github.com/nodejs/node/pull/20611)
- [[`1744205ff5`](https://github.com/nodejs/node/commit/1744205ff5)] - **(SEMVER-MAJOR)** **http**: move process.binding('http_parser') to internalBinding (James M Snell) [#22329](https://github.com/nodejs/node/pull/22329)
- [[`4b00c4fafa`](https://github.com/nodejs/node/commit/4b00c4fafa)] - **(SEMVER-MAJOR)** **http**: make client `.aborted` boolean (Robert Nagy) [#20230](https://github.com/nodejs/node/pull/20230)
- [[`564048dc29`](https://github.com/nodejs/node/commit/564048dc29)] - **(SEMVER-MAJOR)** **http,https,tls**: switch to WHATWG URL parser (Hackzzila) [#20270](https://github.com/nodejs/node/pull/20270)
- [[`4fa5448e5d`](https://github.com/nodejs/node/commit/4fa5448e5d)] - **(SEMVER-MAJOR)** **http2**: move process.binding('http2') to internalBinding (James M Snell) [#22328](https://github.com/nodejs/node/pull/22328)
- [[`8f3cfc89fa`](https://github.com/nodejs/node/commit/8f3cfc89fa)] - **(SEMVER-MAJOR)** **icu**: make process.binding('icu') internal (cjihrig) [#23234](https://github.com/nodejs/node/pull/23234)
- [[`de0441f6f6`](https://github.com/nodejs/node/commit/de0441f6f6)] - **(SEMVER-MAJOR)** **lib**: implement queueMicrotask (Gus Caplan) [#22951](https://github.com/nodejs/node/pull/22951)
- [[`dcc0c2c5c9`](https://github.com/nodejs/node/commit/dcc0c2c5c9)] - **(SEMVER-MAJOR)** **lib**: move process.binding('js_stream') to internalBinding (Anto Aravinth) [#22239](https://github.com/nodejs/node/pull/22239)
- [[`6a689c8aa3`](https://github.com/nodejs/node/commit/6a689c8aa3)] - **(SEMVER-MAJOR)** **lib**: make pipe_wrap binding internal (cjihrig) [#22482](https://github.com/nodejs/node/pull/22482)
- [[`36468ca928`](https://github.com/nodejs/node/commit/36468ca928)] - **(SEMVER-MAJOR)** **lib**: require a callback for end-of-stream (Ruben Bridgewater) [#21058](https://github.com/nodejs/node/pull/21058)
- [[`6f6f7f749b`](https://github.com/nodejs/node/commit/6f6f7f749b)] - **(SEMVER-MAJOR)** **lib**: add internal PriorityQueue class (Anatoli Papirovski) [#20555](https://github.com/nodejs/node/pull/20555)
- [[`e836128703`](https://github.com/nodejs/node/commit/e836128703)] - **(SEMVER-MAJOR)** **lib**: introduce internal/validators (Michaël Zasso) [#19973](https://github.com/nodejs/node/pull/19973)
- [[`1b92214d09`](https://github.com/nodejs/node/commit/1b92214d09)] - **(SEMVER-MAJOR)** **module**: fix inconsistency between load and \_findPath (Denys Otrishko) [#22382](https://github.com/nodejs/node/pull/22382)
- [[`b36c581d5b`](https://github.com/nodejs/node/commit/b36c581d5b)] - **(SEMVER-MAJOR)** **module**: accept Windows relative path (João Reis) [#22186](https://github.com/nodejs/node/pull/22186)
- [[`4a0466f23a`](https://github.com/nodejs/node/commit/4a0466f23a)] - **(SEMVER-MAJOR)** **net**: throw error if port/path does not exist in options (Yaniv Friedensohn) [#22085](https://github.com/nodejs/node/pull/22085)
- [[`49681e7414`](https://github.com/nodejs/node/commit/49681e7414)] - **(SEMVER-MAJOR)** **process**: refactor emitWarning (Ruben Bridgewater) [#20726](https://github.com/nodejs/node/pull/20726)
- [[`2fd248f639`](https://github.com/nodejs/node/commit/2fd248f639)] - **(SEMVER-MAJOR)** **process**: migrate methods to throw errors with code (Michaël Zasso) [#19973](https://github.com/nodejs/node/pull/19973)
- [[`2bf4697ff4`](https://github.com/nodejs/node/commit/2bf4697ff4)] - **(SEMVER-MAJOR)** **repl**: remove duplicate util binding (cjihrig) [#22675](https://github.com/nodejs/node/pull/22675)
- [[`eeb1d514ad`](https://github.com/nodejs/node/commit/eeb1d514ad)] - **(SEMVER-MAJOR)** **repl**: changes ctrl+u to delete from cursor to line start (Shobhit Chittora) [#20686](https://github.com/nodejs/node/pull/20686)
- [[`5f714ac0bd`](https://github.com/nodejs/node/commit/5f714ac0bd)] - **(SEMVER-MAJOR)** **src**: remove long-deprecated APIs without `Isolate*` arg (Anna Henningsen) [#23178](https://github.com/nodejs/node/pull/23178)
- [[`24186e0d20`](https://github.com/nodejs/node/commit/24186e0d20)] - **(SEMVER-MAJOR)** **src**: remove public API for option variables (Anna Henningsen) [#23069](https://github.com/nodejs/node/pull/23069)
- [[`0f73875e7b`](https://github.com/nodejs/node/commit/0f73875e7b)] - **(SEMVER-MAJOR)** **src**: update postmortem constants (cjihrig) [#22754](https://github.com/nodejs/node/pull/22754)
- [[`a5604a73d8`](https://github.com/nodejs/node/commit/a5604a73d8)] - **(SEMVER-MAJOR)** **src**: use HeapStatistics to get external memory (Rodrigo Bruno) [#22754](https://github.com/nodejs/node/pull/22754)
- [[`7429d181c5`](https://github.com/nodejs/node/commit/7429d181c5)] - **(SEMVER-MAJOR)** **src**: update NODE_MODULE_VERSION to 67 (Michaël Zasso) [#22754](https://github.com/nodejs/node/pull/22754)
- [[`9d71e6a607`](https://github.com/nodejs/node/commit/9d71e6a607)] - **(SEMVER-MAJOR)** **src**: deprecate global COUNTER\_\* and remove perfctr (James M Snell) [#22485](https://github.com/nodejs/node/pull/22485)
- [[`dbf72030b7`](https://github.com/nodejs/node/commit/dbf72030b7)] - **(SEMVER-MAJOR)** **src**: update postmortem constant name (cjihrig) [#21983](https://github.com/nodejs/node/pull/21983)
- [[`90ae4bd0c9`](https://github.com/nodejs/node/commit/90ae4bd0c9)] - **(SEMVER-MAJOR)** **src**: add InitializeV8Platform function (Daniel Bevenius) [#21983](https://github.com/nodejs/node/pull/21983)
- [[`d5e7294445`](https://github.com/nodejs/node/commit/d5e7294445)] - **(SEMVER-MAJOR)** **src**: initialize PerIsolateData eagerly (Andreas Haas) [#21983](https://github.com/nodejs/node/pull/21983)
- [[`2e28090855`](https://github.com/nodejs/node/commit/2e28090855)] - **(SEMVER-MAJOR)** **src**: update NODE_MODULE_VERSION to 66 (Michaël Zasso) [#21983](https://github.com/nodejs/node/pull/21983)
- [[`a8572b191e`](https://github.com/nodejs/node/commit/a8572b191e)] - **(SEMVER-MAJOR)** **src**: use default parameters for CreateIsolateData (Anna Henningsen) [#22465](https://github.com/nodejs/node/pull/22465)
- [[`da8641f3b4`](https://github.com/nodejs/node/commit/da8641f3b4)] - **(SEMVER-MAJOR)** **src**: move process.binding('async_wrap') internal (Daniel Bevenius) [#22469](https://github.com/nodejs/node/pull/22469)
- [[`57d98bc732`](https://github.com/nodejs/node/commit/57d98bc732)] - **(SEMVER-MAJOR)** **src**: move process.binding('tcp_wrap') to internal (Daniel Bevenius) [#22432](https://github.com/nodejs/node/pull/22432)
- [[`0bdb95f4cf`](https://github.com/nodejs/node/commit/0bdb95f4cf)] - **(SEMVER-MAJOR)** **src**: move process.binding('signal_wrap') to internalBinding (James M Snell) [#22290](https://github.com/nodejs/node/pull/22290)
- [[`c7962dcba4`](https://github.com/nodejs/node/commit/c7962dcba4)] - **(SEMVER-MAJOR)** **src**: move process.binding('uv') to internalBinding (James M Snell) [#22163](https://github.com/nodejs/node/pull/22163)
- [[`9f5cc1fc92`](https://github.com/nodejs/node/commit/9f5cc1fc92)] - **(SEMVER-MAJOR)** **src**: move process.binding('performance') to internalBinding (James M Snell) [#22029](https://github.com/nodejs/node/pull/22029)
- [[`f479050916`](https://github.com/nodejs/node/commit/f479050916)] - **(SEMVER-MAJOR)** **src**: rename PROVIDER_FSREQWRAP to PROVIDER_FSREQCALLBACK (Jon Moss) [#21971](https://github.com/nodejs/node/pull/21971)
- [[`0f3c2c64d2`](https://github.com/nodejs/node/commit/0f3c2c64d2)] - **(SEMVER-MAJOR)** **src**: use modern v8::Platform worker threads APIs (Gabriel Charette) [#21079](https://github.com/nodejs/node/pull/21079)
- [[`6f9705275b`](https://github.com/nodejs/node/commit/6f9705275b)] - **(SEMVER-MAJOR)** **src**: update NODE_MODULE_VERSION to 65 (Michaël Zasso) [#21079](https://github.com/nodejs/node/pull/21079)
- [[`cf37945b12`](https://github.com/nodejs/node/commit/cf37945b12)] - **(SEMVER-MAJOR)** **src**: include cwd in chdir error message (Anna Henningsen) [#21526](https://github.com/nodejs/node/pull/21526)
- [[`bfcf5b01bb`](https://github.com/nodejs/node/commit/bfcf5b01bb)] - **(SEMVER-MAJOR)** **src**: remove tick_info-\>has_thrown (Anatoli Papirovski) [#20894](https://github.com/nodejs/node/pull/20894)
- [[`2930bd1317`](https://github.com/nodejs/node/commit/2930bd1317)] - **(SEMVER-MAJOR)** **src**: refactor timers to remove TimerWrap (Anatoli Papirovski) [#20894](https://github.com/nodejs/node/pull/20894)
- [[`3294d1bf62`](https://github.com/nodejs/node/commit/3294d1bf62)] - **(SEMVER-MAJOR)** **src**: remove --expose-http2 option (Daniel Bevenius) [#20887](https://github.com/nodejs/node/pull/20887)
- [[`3152b7c0d3`](https://github.com/nodejs/node/commit/3152b7c0d3)] - **(SEMVER-MAJOR)** **src**: assign ERR_SCRIPT_EXECUTION\_\* codes in C++ (Joyee Cheung) [#20147](https://github.com/nodejs/node/pull/20147)
- [[`1d1ab76e17`](https://github.com/nodejs/node/commit/1d1ab76e17)] - **(SEMVER-MAJOR)** **src**: make process.env.TZ setter clear tz cache (Ben Noordhuis) [#20026](https://github.com/nodejs/node/pull/20026)
- [[`627f10937e`](https://github.com/nodejs/node/commit/627f10937e)] - **(SEMVER-MAJOR)** **src,lib**: move `natives` and `constants` to `internalBinding()` (Anna Henningsen) [#23663](https://github.com/nodejs/node/pull/23663)
- [[`172b4d7ceb`](https://github.com/nodejs/node/commit/172b4d7ceb)] - **(SEMVER-MAJOR)** **src,lib**: rename FSReqWrap to FSReqCallback (Jon Moss) [#21971](https://github.com/nodejs/node/pull/21971)
- [[`884b23daf7`](https://github.com/nodejs/node/commit/884b23daf7)] - **(SEMVER-MAJOR)** **stream**: move process.binding('stream_wrap') to internalBinding (James M Snell) [#22345](https://github.com/nodejs/node/pull/22345)
- [[`32c51f10d3`](https://github.com/nodejs/node/commit/32c51f10d3)] - **(SEMVER-MAJOR)** **stream**: make the pipeline callback mandatory (Ruben Bridgewater) [#21054](https://github.com/nodejs/node/pull/21054)
- [[`06f6ac179c`](https://github.com/nodejs/node/commit/06f6ac179c)] - **(SEMVER-MAJOR)** **string_decoder**: fix number of replacement chars (Anna Henningsen) [#22709](https://github.com/nodejs/node/pull/22709)
- [[`2285177383`](https://github.com/nodejs/node/commit/2285177383)] - **(SEMVER-MAJOR)** **test**: remove test-buffer-bindingobj-no-zerofill.js (Weijia Wang) [#23234](https://github.com/nodejs/node/pull/23234)
- [[`1b274287c9`](https://github.com/nodejs/node/commit/1b274287c9)] - **(SEMVER-MAJOR)** **test**: add string-decoder fuzz test (Anna Henningsen) [#22709](https://github.com/nodejs/node/pull/22709)
- [[`8aca934009`](https://github.com/nodejs/node/commit/8aca934009)] - **(SEMVER-MAJOR)** **test**: update postmortem metadata test for V8 7.0 (cjihrig) [#22754](https://github.com/nodejs/node/pull/22754)
- [[`36cc812d18`](https://github.com/nodejs/node/commit/36cc812d18)] - **(SEMVER-MAJOR)** **test**: update postmortem metadata test for V8 6.9 (cjihrig) [#21983](https://github.com/nodejs/node/pull/21983)
- [[`f7d572fa2b`](https://github.com/nodejs/node/commit/f7d572fa2b)] - **(SEMVER-MAJOR)** **test**: add new_large_object_space heap space (Michaël Zasso) [#21983](https://github.com/nodejs/node/pull/21983)
- [[`e865acd4db`](https://github.com/nodejs/node/commit/e865acd4db)] - **(SEMVER-MAJOR)** **test**: update postmortem metadata test (Matheus Marchini) [#21983](https://github.com/nodejs/node/pull/21983)
- [[`19984ad7bb`](https://github.com/nodejs/node/commit/19984ad7bb)] - **(SEMVER-MAJOR)** **test**: fix inspector tests after V8 upgrade (Alexey Kozyatinskiy) [#21983](https://github.com/nodejs/node/pull/21983)
- [[`34f56e2d71`](https://github.com/nodejs/node/commit/34f56e2d71)] - **(SEMVER-MAJOR)** **test**: fix crypto test case (Tobias Nießen) [#22126](https://github.com/nodejs/node/pull/22126)
- [[`0deb27bd29`](https://github.com/nodejs/node/commit/0deb27bd29)] - **(SEMVER-MAJOR)** **test**: add dns memory error test (Rich Trott) [#20317](https://github.com/nodejs/node/pull/20317)
- [[`52428c81cd`](https://github.com/nodejs/node/commit/52428c81cd)] - **(SEMVER-MAJOR)** **timers**: run nextTicks after each immediate and timer (Anatoli Papirovski) [#22842](https://github.com/nodejs/node/pull/22842)
- [[`23a56e0c28`](https://github.com/nodejs/node/commit/23a56e0c28)] - **(SEMVER-MAJOR)** **timers**: use only a single TimerWrap instance (Anatoli Papirovski) [#20555](https://github.com/nodejs/node/pull/20555)
- [[`198eb9c5d6`](https://github.com/nodejs/node/commit/198eb9c5d6)] - **(SEMVER-MAJOR)** **timers**: reschedule interval even if it threw (Anatoli Papirovski) [#20002](https://github.com/nodejs/node/pull/20002)
- [[`3c2aa4b9f3`](https://github.com/nodejs/node/commit/3c2aa4b9f3)] - **(SEMVER-MAJOR)** **tls**: de-duplicate for TLSSocket methods (Jon Moss) [#22142](https://github.com/nodejs/node/pull/22142)
- [[`fa3d6bedf9`](https://github.com/nodejs/node/commit/fa3d6bedf9)] - **(SEMVER-MAJOR)** **tls**: use internal API instead of crypto module (Tobias Nießen) [#22501](https://github.com/nodejs/node/pull/22501)
- [[`3095eecc47`](https://github.com/nodejs/node/commit/3095eecc47)] - **(SEMVER-MAJOR)** **tls**: warn on NODE_TLS_REJECT_UNAUTHORIZED = '0' (cjihrig) [#21900](https://github.com/nodejs/node/pull/21900)
- [[`a15ea5d7ca`](https://github.com/nodejs/node/commit/a15ea5d7ca)] - **(SEMVER-MAJOR)** **tls**: throw error on bad ciphers option (Brian White) [#21557](https://github.com/nodejs/node/pull/21557)
- [[`eadcee1137`](https://github.com/nodejs/node/commit/eadcee1137)] - **(SEMVER-MAJOR)** **tls**: throw if SNICallback is not a function (Rich Trott) [#20969](https://github.com/nodejs/node/pull/20969)
- [[`4d00cd4ce7`](https://github.com/nodejs/node/commit/4d00cd4ce7)] - **(SEMVER-MAJOR)** **tls**: move convertNPNProtocols to End-of-Life (James M Snell) [#20736](https://github.com/nodejs/node/pull/20736)
- [[`e6cdf24bb5`](https://github.com/nodejs/node/commit/e6cdf24bb5)] - **(SEMVER-MAJOR)** **tools**: remove lldbinit file from install script (Clemens Hammacher) [#21983](https://github.com/nodejs/node/pull/21983)
- [[`267b0b5f3d`](https://github.com/nodejs/node/commit/267b0b5f3d)] - **(SEMVER-MAJOR)** **tools**: fix compilation after V8 upgrade (Michaël Zasso) [#21983](https://github.com/nodejs/node/pull/21983)
- [[`c1e2d6b0f1`](https://github.com/nodejs/node/commit/c1e2d6b0f1)] - **(SEMVER-MAJOR)** **trace_events**: move trace_events to internalBinding (James M Snell) [#22159](https://github.com/nodejs/node/pull/22159)
- [[`df073cdda4`](https://github.com/nodejs/node/commit/df073cdda4)] - **(SEMVER-MAJOR)** **tty**: make process.binding('tty_wrap') internal (cjihrig) [#22477](https://github.com/nodejs/node/pull/22477)
- [[`91eec00ca2`](https://github.com/nodejs/node/commit/91eec00ca2)] - **(SEMVER-MAJOR)** **tty**: make \_read throw ERR_TTY_WRITABLE_NOT_READABLE (Matteo Collina) [#21654](https://github.com/nodejs/node/pull/21654)
- [[`922a1b03b6`](https://github.com/nodejs/node/commit/922a1b03b6)] - **(SEMVER-MAJOR)** **url**: docs deprecate legacy url API (James M Snell) [#22715](https://github.com/nodejs/node/pull/22715)
- [[`e917a23d2e`](https://github.com/nodejs/node/commit/e917a23d2e)] - **(SEMVER-MAJOR)** **url**: move process.binding('url') to internalBinding (Weijia Wang) [#22204](https://github.com/nodejs/node/pull/22204)
- [[`1a1fe53e3d`](https://github.com/nodejs/node/commit/1a1fe53e3d)] - **(SEMVER-MAJOR)** **util**: change %o depth default (Ruben Bridgewater) [#22846](https://github.com/nodejs/node/pull/22846)
- [[`ac7450a09a`](https://github.com/nodejs/node/commit/ac7450a09a)] - **(SEMVER-MAJOR)** **util**: change util.inspect depth default (Ruben Bridgewater) [#22846](https://github.com/nodejs/node/pull/22846)
- [[`5e6940d4f6`](https://github.com/nodejs/node/commit/5e6940d4f6)] - **(SEMVER-MAJOR)** **util**: set `super_` property to non-enumerable (Ruben Bridgewater) [#23107](https://github.com/nodejs/node/pull/23107)
- [[`932be0164f`](https://github.com/nodejs/node/commit/932be0164f)] - **(SEMVER-MAJOR)** **util**: make TextEncoder/TextDecoder global (James M Snell) [#22281](https://github.com/nodejs/node/pull/22281)
- [[`eb61127c48`](https://github.com/nodejs/node/commit/eb61127c48)] - **(SEMVER-MAJOR)** **util**: limit inspection output size to 128 MB (Ruben Bridgewater) [#22756](https://github.com/nodejs/node/pull/22756)
- [[`7e4b0a4850`](https://github.com/nodejs/node/commit/7e4b0a4850)] - **(SEMVER-MAJOR)** **util**: make util binding internal (cjihrig) [#22675](https://github.com/nodejs/node/pull/22675)
- [[`980877ffa2`](https://github.com/nodejs/node/commit/980877ffa2)] - **(SEMVER-MAJOR)** **util**: adding warnings when NODE_DEBUG is set as http/http2 (Anto Aravinth) [#21914](https://github.com/nodejs/node/pull/21914)
- [[`b3e93a91eb`](https://github.com/nodejs/node/commit/b3e93a91eb)] - **(SEMVER-MAJOR)** **util**: do not escape single quotes if not necessary (Ruben Bridgewater) [#21624](https://github.com/nodejs/node/pull/21624)
- [[`80496a5570`](https://github.com/nodejs/node/commit/80496a5570)] - **(SEMVER-MAJOR)** **util**: add inspect suffix to BigInt64Array elements (Teddy Katz) [#21499](https://github.com/nodejs/node/pull/21499)
- [[`e270ae9f01`](https://github.com/nodejs/node/commit/e270ae9f01)] - **(SEMVER-MAJOR)** **util**: change items unknown style (Ruben Bridgewater) [#20792](https://github.com/nodejs/node/pull/20792)
- [[`27df81cd18`](https://github.com/nodejs/node/commit/27df81cd18)] - **(SEMVER-MAJOR)** **util**: remove custom inspection function (Ruben Bridgewater) [#20722](https://github.com/nodejs/node/pull/20722)
- [[`892932f9bd`](https://github.com/nodejs/node/commit/892932f9bd)] - **(SEMVER-MAJOR)** **v8**: move process.binding('v8') to internalBinding (James M Snell) [#22288](https://github.com/nodejs/node/pull/22288)
- [[`cf3bb593de`](https://github.com/nodejs/node/commit/cf3bb593de)] - **(SEMVER-MAJOR)** **v8**: move serdes to internalBinding (Gus Caplan) [#22161](https://github.com/nodejs/node/pull/22161)
- [[`4963a04b30`](https://github.com/nodejs/node/commit/4963a04b30)] - **(SEMVER-MAJOR)** **vm**: move process.binding('contextify') to internalBinding (James M Snell) [#22419](https://github.com/nodejs/node/pull/22419)
- [[`07682eb0c4`](https://github.com/nodejs/node/commit/07682eb0c4)] - **(SEMVER-MAJOR)** **zlib**: move `bytesRead` accessors to runtime deprecation (Anna Henningsen) [#23308](https://github.com/nodejs/node/pull/23308)
- [[`4f48ddb72f`](https://github.com/nodejs/node/commit/4f48ddb72f)] - **(SEMVER-MAJOR)** **zlib**: move process.binding('zlib') to internalBinding (Anna Henningsen) [#23307](https://github.com/nodejs/node/pull/23307)

### Semver-Minor Commits

- [[`b61d31a845`](https://github.com/nodejs/node/commit/b61d31a845)] - **(SEMVER-MINOR)** **src**: add deprecation warning to errname() (Dolapo Toki) [#23597](https://github.com/nodejs/node/pull/23597)
- [[`39fcda0ca4`](https://github.com/nodejs/node/commit/39fcda0ca4)] - **(SEMVER-MINOR)** **src,test**: add public wrapper for Environment::GetCurrent (Shelley Vohr) [#23676](https://github.com/nodejs/node/pull/23676)
- [[`48a2568f41`](https://github.com/nodejs/node/commit/48a2568f41)] - **(SEMVER-MINOR)** **timers**: add hasRef method to Timeout & Immediate (Anatoli Papirovski) [#20898](https://github.com/nodejs/node/pull/20898)
- [[`bed4a8c6e0`](https://github.com/nodejs/node/commit/bed4a8c6e0)] - **(SEMVER-MINOR)** **tls**: support changing credentials dynamically (cjihrig) [#23644](https://github.com/nodejs/node/pull/23644)

### Semver-Patch Commits

- [[`eccc65919a`](https://github.com/nodejs/node/commit/eccc65919a)] - **assert**: add comments for diff algorithm (Ruben Bridgewater) [#23048](https://github.com/nodejs/node/pull/23048)
- [[`02c44a4894`](https://github.com/nodejs/node/commit/02c44a4894)] - **assert**: reduce diff noise (Ruben Bridgewater) [#23048](https://github.com/nodejs/node/pull/23048)
- [[`b8a8eedf32`](https://github.com/nodejs/node/commit/b8a8eedf32)] - **assert**: switch `inputs` to `values` (Ruben Bridgewater) [#23056](https://github.com/nodejs/node/pull/23056)
- [[`be26c76114`](https://github.com/nodejs/node/commit/be26c76114)] - **assert**: improve the strict equal messages (Ruben Bridgewater) [#23056](https://github.com/nodejs/node/pull/23056)
- [[`1d859ef532`](https://github.com/nodejs/node/commit/1d859ef532)] - **assert**: improve loose assertion message (Ruben Bridgewater) [#22155](https://github.com/nodejs/node/pull/22155)
- [[`0339d3dc36`](https://github.com/nodejs/node/commit/0339d3dc36)] - **async_hooks**: add missing async_hooks destroys in AsyncReset (Bastian Krol) [#23272](https://github.com/nodejs/node/pull/23272)
- [[`996b3c5bb1`](https://github.com/nodejs/node/commit/996b3c5bb1)] - **benchmark**: coerce PORT to number (Ali Ijaz Sheikh) [#23721](https://github.com/nodejs/node/pull/23721)
- [[`cdca587b3d`](https://github.com/nodejs/node/commit/cdca587b3d)] - **benchmark**: added a test benchmark for worker (Muzafar Umarov) [#23475](https://github.com/nodejs/node/pull/23475)
- [[`2ca7aebefc`](https://github.com/nodejs/node/commit/2ca7aebefc)] - **benchmark**: add common.binding() (cjihrig) [#23460](https://github.com/nodejs/node/pull/23460)
- [[`0d548924b0`](https://github.com/nodejs/node/commit/0d548924b0)] - **bootstrapper**: move internalBinding to NativeModule (Gus Caplan) [#23025](https://github.com/nodejs/node/pull/23025)
- [[`1bd44d7f75`](https://github.com/nodejs/node/commit/1bd44d7f75)] - **build**: fix coverage generation (Michael Dawson) [#23769](https://github.com/nodejs/node/pull/23769)
- [[`6c7d8b4e12`](https://github.com/nodejs/node/commit/6c7d8b4e12)] - **build**: spawn `make test-ci` with `-j1` (Refael Ackermann) [#23733](https://github.com/nodejs/node/pull/23733)
- [[`d548e63123`](https://github.com/nodejs/node/commit/d548e63123)] - **build**: fix `./configure --enable-d8` (Ben Noordhuis) [#23656](https://github.com/nodejs/node/pull/23656)
- [[`c9fd435d28`](https://github.com/nodejs/node/commit/c9fd435d28)] - **build**: add .DS_store to .gitgnore (Marcos Frony) [#23554](https://github.com/nodejs/node/pull/23554)
- [[`9d9f691d26`](https://github.com/nodejs/node/commit/9d9f691d26)] - **_Revert_** "**build**: extract common code from NODE_EXE/\_G_EXE" (Daniel Bevenius) [#22458](https://github.com/nodejs/node/pull/22458)
- [[`4e2fa8b0dc`](https://github.com/nodejs/node/commit/4e2fa8b0dc)] - **build**: extract common code from NODE_EXE/\_G_EXE (Daniel Bevenius) [#22310](https://github.com/nodejs/node/pull/22310)
- [[`a6124892ff`](https://github.com/nodejs/node/commit/a6124892ff)] - **console**: add trace-events for time and count (James M Snell) [#23703](https://github.com/nodejs/node/pull/23703)
- [[`a144d64e68`](https://github.com/nodejs/node/commit/a144d64e68)] - **crypto**: migrate to getOptions() (nick-ng) [#23562](https://github.com/nodejs/node/pull/23562)
- [[`f4d1d9cb31`](https://github.com/nodejs/node/commit/f4d1d9cb31)] - **crypto**: remove DiffieHellman.initialised\_ (Tobias Nießen) [#23717](https://github.com/nodejs/node/pull/23717)
- [[`1ad660b72d`](https://github.com/nodejs/node/commit/1ad660b72d)] - **crypto**: reduce memory usage of SignFinal (Tobias Nießen) [#23427](https://github.com/nodejs/node/pull/23427)
- [[`1336830069`](https://github.com/nodejs/node/commit/1336830069)] - **crypto**: DRY Diffie-Hellman initialization code (Ben Noordhuis) [#23657](https://github.com/nodejs/node/pull/23657)
- [[`6975639651`](https://github.com/nodejs/node/commit/6975639651)] - **crypto**: simplify internal state handling (Tobias Nießen) [#23648](https://github.com/nodejs/node/pull/23648)
- [[`b2b48083a6`](https://github.com/nodejs/node/commit/b2b48083a6)] - **crypto**: simplify error handling in ECDH::New (Tobias Nießen) [#23647](https://github.com/nodejs/node/pull/23647)
- [[`ed0070e318`](https://github.com/nodejs/node/commit/ed0070e318)] - **crypto**: move field initialization to class (Diana Holland) [#23610](https://github.com/nodejs/node/pull/23610)
- [[`cb569a37e9`](https://github.com/nodejs/node/commit/cb569a37e9)] - **crypto**: fix length argument to snprintf() (Ben Noordhuis) [#23622](https://github.com/nodejs/node/pull/23622)
- [[`709b3b1e1c`](https://github.com/nodejs/node/commit/709b3b1e1c)] - **crypto**: downgrade DEP0115 to `--pending-deprecation` only (Anna Henningsen) [#23017](https://github.com/nodejs/node/pull/23017)
- [[`360465dfe2`](https://github.com/nodejs/node/commit/360465dfe2)] - **crypto**: assign missing deprecation code (Tobias Nießen) [#22827](https://github.com/nodejs/node/pull/22827)
- [[`c4e74ec1cd`](https://github.com/nodejs/node/commit/c4e74ec1cd)] - **deps**: add missing ares_android.h file (cjihrig) [#23682](https://github.com/nodejs/node/pull/23682)
- [[`e2258adff7`](https://github.com/nodejs/node/commit/e2258adff7)] - **deps**: patch V8 to 7.0.276.28 (Michaël Zasso) [#23424](https://github.com/nodejs/node/pull/23424)
- [[`8165657d9e`](https://github.com/nodejs/node/commit/8165657d9e)] - **deps**: patch V8 to 7.0.276.25 (Michaël Zasso) [#23290](https://github.com/nodejs/node/pull/23290)
- [[`a67650f4be`](https://github.com/nodejs/node/commit/a67650f4be)] - **deps**: V8: cherry-pick 64-bit hash seed commits (Yang Guo) [#23264](https://github.com/nodejs/node/pull/23264)
- [[`4fcfa9d1dc`](https://github.com/nodejs/node/commit/4fcfa9d1dc)] - **deps**: provide more V8 backwards compatibility (Anna Henningsen) [#23158](https://github.com/nodejs/node/pull/23158)
- [[`ef85f08a5e`](https://github.com/nodejs/node/commit/ef85f08a5e)] - **deps**: revert 9136dd8088a9 from upstream V8 (Anna Henningsen) [#23158](https://github.com/nodejs/node/pull/23158)
- [[`d25646b4c5`](https://github.com/nodejs/node/commit/d25646b4c5)] - **deps**: patch V8 to 7.0.276.24 (Michaël Zasso) [#23158](https://github.com/nodejs/node/pull/23158)
- [[`6117af3490`](https://github.com/nodejs/node/commit/6117af3490)] - **deps**: patch V8 to 7.0.276.22 (Michaël Zasso) [#23160](https://github.com/nodejs/node/pull/23160)
- [[`2811ae4801`](https://github.com/nodejs/node/commit/2811ae4801)] - **deps**: patch V8 to 6.9.427.23 (Michaël Zasso) [#22898](https://github.com/nodejs/node/pull/22898)
- [[`56d7411be3`](https://github.com/nodejs/node/commit/56d7411be3)] - **deps**: cherry-pick e1a7699 from upstream V8 (Camillo Bruni) [#22390](https://github.com/nodejs/node/pull/22390)
- [[`349612b233`](https://github.com/nodejs/node/commit/349612b233)] - **deps**: cherry-pick e1a7699 from upstream V8 (Camillo Bruni) [#22390](https://github.com/nodejs/node/pull/22390)
- [[`2f9dabd0d8`](https://github.com/nodejs/node/commit/2f9dabd0d8)] - **deps**: cherry-pick 9eb96bb from upstream V8 (Timothy Gu) [#22390](https://github.com/nodejs/node/pull/22390)
- [[`54c87f37f4`](https://github.com/nodejs/node/commit/54c87f37f4)] - **deps**: cherry-pick 6ee8345 from upstream V8 (Joyee Cheung) [#22106](https://github.com/nodejs/node/pull/22106)
- [[`e2ea82b9ce`](https://github.com/nodejs/node/commit/e2ea82b9ce)] - **dgram**: fix linting issue (Jon Moss) [#22175](https://github.com/nodejs/node/pull/22175)
- [[`dd756248db`](https://github.com/nodejs/node/commit/dd756248db)] - **dns**: fix inconsistent (hostname vs host) (Ulises Gascón) [#23572](https://github.com/nodejs/node/pull/23572)
- [[`d6b3f6513b`](https://github.com/nodejs/node/commit/d6b3f6513b)] - **doc**: add missing YAML labels (Vse Mozhet Byt) [#23810](https://github.com/nodejs/node/pull/23810)
- [[`3f292bf783`](https://github.com/nodejs/node/commit/3f292bf783)] - **doc**: remove reference to sslv3 in tls.md (James M Snell) [#23745](https://github.com/nodejs/node/pull/23745)
- [[`e8d293ecdc`](https://github.com/nodejs/node/commit/e8d293ecdc)] - **doc**: revise security-reporting example text (Rich Trott) [#23759](https://github.com/nodejs/node/pull/23759)
- [[`eaff120bfd`](https://github.com/nodejs/node/commit/eaff120bfd)] - **doc**: formalize non-const reference usage in C++ style guide (Anna Henningsen) [#23155](https://github.com/nodejs/node/pull/23155)
- [[`512faa8ec6`](https://github.com/nodejs/node/commit/512faa8ec6)] - **doc**: fix index in table of contents in BUILDING.md (ZYSzys) [#23777](https://github.com/nodejs/node/pull/23777)
- [[`50c99d87b0`](https://github.com/nodejs/node/commit/50c99d87b0)] - **doc**: add missing deprecation labels (James M Snell) [#23761](https://github.com/nodejs/node/pull/23761)
- [[`889a49f79c`](https://github.com/nodejs/node/commit/889a49f79c)] - **doc**: document use of buffer.swap16() for utf16be (James M Snell) [#23747](https://github.com/nodejs/node/pull/23747)
- [[`4c7f16def0`](https://github.com/nodejs/node/commit/4c7f16def0)] - **doc**: add Backport-PR-URL info in backport guide (Ali Ijaz Sheikh) [#23701](https://github.com/nodejs/node/pull/23701)
- [[`a5b1e7b6c4`](https://github.com/nodejs/node/commit/a5b1e7b6c4)] - **doc**: improve README.md (Rich Trott) [#23705](https://github.com/nodejs/node/pull/23705)
- [[`27892345b9`](https://github.com/nodejs/node/commit/27892345b9)] - **doc**: simplify security reporting text (Rich Trott) [#23686](https://github.com/nodejs/node/pull/23686)
- [[`9c5ec790a0`](https://github.com/nodejs/node/commit/9c5ec790a0)] - **doc**: cleanup and references in C++ guide (Refael Ackermann) [#23650](https://github.com/nodejs/node/pull/23650)
- [[`9430ac2f0c`](https://github.com/nodejs/node/commit/9430ac2f0c)] - **doc**: add info how to run single tests to BUILDING.md (Felix Schlenkrich) [#23490](https://github.com/nodejs/node/pull/23490)
- [[`3ad2267cd0`](https://github.com/nodejs/node/commit/3ad2267cd0)] - **doc**: add "tick" function name and argument description (Artur Hayrapetyan) [#23551](https://github.com/nodejs/node/pull/23551)
- [[`f14a8e5870`](https://github.com/nodejs/node/commit/f14a8e5870)] - **doc**: fix url example to match behavior (Сковорода Никита Андреевич) [#23359](https://github.com/nodejs/node/pull/23359)
- [[`ba11ad3322`](https://github.com/nodejs/node/commit/ba11ad3322)] - **doc**: use reserved domains for examples in url.md (Сковорода Никита Андреевич) [#23359](https://github.com/nodejs/node/pull/23359)
- [[`e6c310d29f`](https://github.com/nodejs/node/commit/e6c310d29f)] - **doc**: fix pr-url in repl.md (Сковорода Никита Андреевич) [#23359](https://github.com/nodejs/node/pull/23359)
- [[`4f38d45f1c`](https://github.com/nodejs/node/commit/4f38d45f1c)] - **doc**: wrap links in \<\> (Сковорода Никита Андреевич) [#23359](https://github.com/nodejs/node/pull/23359)
- [[`d911bab8c3`](https://github.com/nodejs/node/commit/d911bab8c3)] - **doc**: edit BUILDING.md (Rich Trott) [#23435](https://github.com/nodejs/node/pull/23435)
- [[`7d07e161d5`](https://github.com/nodejs/node/commit/7d07e161d5)] - **doc**: describe SNI host name format (Sam Roberts) [#23357](https://github.com/nodejs/node/pull/23357)
- [[`9d6a1d661b`](https://github.com/nodejs/node/commit/9d6a1d661b)] - **doc**: revise security-reporting text in README (Rich Trott) [#23407](https://github.com/nodejs/node/pull/23407)
- [[`2303e4c63c`](https://github.com/nodejs/node/commit/2303e4c63c)] - **doc**: rewrite consensus seeking in guide (Rich Trott) [#23349](https://github.com/nodejs/node/pull/23349)
- [[`db8b5247fd`](https://github.com/nodejs/node/commit/db8b5247fd)] - **doc**: edit for minor fixes to prcoess.md (Rich Trott) [#23347](https://github.com/nodejs/node/pull/23347)
- [[`927878e4a0`](https://github.com/nodejs/node/commit/927878e4a0)] - **doc**: remove personal pronoun from worker_threads (Rich Trott) [#23347](https://github.com/nodejs/node/pull/23347)
- [[`bc45605775`](https://github.com/nodejs/node/commit/bc45605775)] - **doc**: remove personal pronoun from domain.md (Rich Trott) [#23347](https://github.com/nodejs/node/pull/23347)
- [[`f41d42ffb5`](https://github.com/nodejs/node/commit/f41d42ffb5)] - **doc**: remove style instruction that is not followed (Rich Trott) [#23346](https://github.com/nodejs/node/pull/23346)
- [[`992c1d56de`](https://github.com/nodejs/node/commit/992c1d56de)] - **doc**: add WebAssembly to globals (Steven) [#23339](https://github.com/nodejs/node/pull/23339)
- [[`5ed4b8974a`](https://github.com/nodejs/node/commit/5ed4b8974a)] - **doc**: fix confusing language about microtask queue (Gus Caplan) [#23197](https://github.com/nodejs/node/pull/23197)
- [[`67ba8ff31a`](https://github.com/nodejs/node/commit/67ba8ff31a)] - **doc**: fix type of DEP0116 (Tobias Nießen) [#22765](https://github.com/nodejs/node/pull/22765)
- [[`193d6d1bda`](https://github.com/nodejs/node/commit/193d6d1bda)] - **doc**: update notes about GCM decryption (Tobias Nießen) [#21445](https://github.com/nodejs/node/pull/21445)
- [[`baca6d337f`](https://github.com/nodejs/node/commit/baca6d337f)] - **doc**: add a missing anchor to error codes (Сковорода Никита Андреевич) [#21483](https://github.com/nodejs/node/pull/21483)
- [[`72258c3cbc`](https://github.com/nodejs/node/commit/72258c3cbc)] - **doc,meta**: assign PR semantics (Refael Ackermann) [#23292](https://github.com/nodejs/node/pull/23292)
- [[`d08544f99c`](https://github.com/nodejs/node/commit/d08544f99c)] - **doc,meta**: refresh wording in colab guide (Refael Ackermann) [#23292](https://github.com/nodejs/node/pull/23292)
- [[`cabf144db9`](https://github.com/nodejs/node/commit/cabf144db9)] - **doc,meta**: add references to outside C++ guides (Refael Ackermann) [#23317](https://github.com/nodejs/node/pull/23317)
- [[`37e40e369d`](https://github.com/nodejs/node/commit/37e40e369d)] - **http**: reduce duplicated code for cleaning parser (Weijia Wang) [#23351](https://github.com/nodejs/node/pull/23351)
- [[`70ba041735`](https://github.com/nodejs/node/commit/70ba041735)] - **http2**: make Http2Settings constructors delegate (Daniel Bevenius) [#23326](https://github.com/nodejs/node/pull/23326)
- [[`f40399a0c4`](https://github.com/nodejs/node/commit/f40399a0c4)] - **lib**: migrate process.binding to internalBinding (surreal8) [#23517](https://github.com/nodejs/node/pull/23517)
- [[`ff5f1fb0cd`](https://github.com/nodejs/node/commit/ff5f1fb0cd)] - **lib**: migrate process.binding to getOptions (Randy Wressell) [#23522](https://github.com/nodejs/node/pull/23522)
- [[`66d4ac1af5`](https://github.com/nodejs/node/commit/66d4ac1af5)] - **lib**: migrate process.binding('config') to getOptions() (Jonny Kalambay) [#23526](https://github.com/nodejs/node/pull/23526)
- [[`c1ec3bf989`](https://github.com/nodejs/node/commit/c1ec3bf989)] - **lib**: removed unused variable (Long Nguyen) [#23497](https://github.com/nodejs/node/pull/23497)
- [[`540c01af28`](https://github.com/nodejs/node/commit/540c01af28)] - **lib**: switch to internalBinding for cjs loader (Steven Scott) [#23492](https://github.com/nodejs/node/pull/23492)
- [[`313b44b0ee`](https://github.com/nodejs/node/commit/313b44b0ee)] - **lib**: remove an unused variable (Claire Liu) [#23482](https://github.com/nodejs/node/pull/23482)
- [[`1143ea8f1b`](https://github.com/nodejs/node/commit/1143ea8f1b)] - **lib**: migrate from process.binding to internalBinding (Andres Monge) [#23586](https://github.com/nodejs/node/pull/23586)
- [[`4291c43aff`](https://github.com/nodejs/node/commit/4291c43aff)] - **lib**: remove unused 'e' from catch (Matt Holmes) [#23458](https://github.com/nodejs/node/pull/23458)
- [[`278775a84b`](https://github.com/nodejs/node/commit/278775a84b)] - **lib**: migrate to getOptions in loaders.js (David Xue) [#23455](https://github.com/nodejs/node/pull/23455)
- [[`3663fc8725`](https://github.com/nodejs/node/commit/3663fc8725)] - **lib**: http server, friendly error messages (Sagi Tsofan) [#22995](https://github.com/nodejs/node/pull/22995)
- [[`ea8000f119`](https://github.com/nodejs/node/commit/ea8000f119)] - **lib**: lazy load internal/queue_microtask (Gus Caplan) [#23046](https://github.com/nodejs/node/pull/23046)
- [[`bb26d4f2f8`](https://github.com/nodejs/node/commit/bb26d4f2f8)] - **meta**: clarify fast-track approval (James M Snell) [#23744](https://github.com/nodejs/node/pull/23744)
- [[`df8e586964`](https://github.com/nodejs/node/commit/df8e586964)] - **module**: removed unused variable (Martin Omander) [#23624](https://github.com/nodejs/node/pull/23624)
- [[`15b12411e9`](https://github.com/nodejs/node/commit/15b12411e9)] - **_Revert_** "**module**: fix inconsistency between load and \_findPath" (John-David Dalton) [#23228](https://github.com/nodejs/node/pull/23228)
- [[`0257fd7ce9`](https://github.com/nodejs/node/commit/0257fd7ce9)] - **process**: wrap process.binding for selective fallthrough (James M Snell) [#22269](https://github.com/nodejs/node/pull/22269)
- [[`3c329bee05`](https://github.com/nodejs/node/commit/3c329bee05)] - **readline**: assert without the use of event listener (Lian Li) [#23472](https://github.com/nodejs/node/pull/23472)
- [[`6855b619c9`](https://github.com/nodejs/node/commit/6855b619c9)] - **repl**: remove unused variable from try catch (mmisiarek) [#23452](https://github.com/nodejs/node/pull/23452)
- [[`4ed1fba740`](https://github.com/nodejs/node/commit/4ed1fba740)] - **repl**: remove unused variable e from try catch (Khalid Adil) [#23449](https://github.com/nodejs/node/pull/23449)
- [[`83d0404971`](https://github.com/nodejs/node/commit/83d0404971)] - **repl**: do not swallow errors in nested REPLs (Rich Trott) [#23004](https://github.com/nodejs/node/pull/23004)
- [[`f0e5afc968`](https://github.com/nodejs/node/commit/f0e5afc968)] - **src**: fix missing deprecation assignment (James M Snell) [#23809](https://github.com/nodejs/node/pull/23809)
- [[`b8cb60fcb9`](https://github.com/nodejs/node/commit/b8cb60fcb9)] - **src**: use more explicit return type in Sign::SignFinal() (Anna Henningsen) [#23779](https://github.com/nodejs/node/pull/23779)
- [[`6c8a96fefa`](https://github.com/nodejs/node/commit/6c8a96fefa)] - **src**: initial large page (2M) support (Suresh Srinivas) [#22079](https://github.com/nodejs/node/pull/22079)
- [[`74ddae783d`](https://github.com/nodejs/node/commit/74ddae783d)] - **src**: add trace events for env.cc (James M Snell) [#23674](https://github.com/nodejs/node/pull/23674)
- [[`59feb5378b`](https://github.com/nodejs/node/commit/59feb5378b)] - **src**: changed stdio_pipes\_ to std::vector (Steven Auger) [#23615](https://github.com/nodejs/node/pull/23615)
- [[`e4fdedd3f1`](https://github.com/nodejs/node/commit/e4fdedd3f1)] - **src**: update v8::Object::GetPropertyNames() usage (cjihrig) [#23660](https://github.com/nodejs/node/pull/23660)
- [[`da52c3fc9b`](https://github.com/nodejs/node/commit/da52c3fc9b)] - **src**: remove OCB support ifdef OPENSSL_NO_OCB (Shelley Vohr) [#23635](https://github.com/nodejs/node/pull/23635)
- [[`2f6b73745c`](https://github.com/nodejs/node/commit/2f6b73745c)] - **src**: remove function hasTextDecoder in encoding.js (Chi-chi Wang) [#23625](https://github.com/nodejs/node/pull/23625)
- [[`fd7fc99e90`](https://github.com/nodejs/node/commit/fd7fc99e90)] - **src**: change macro to fn (Gino Notto) [#23603](https://github.com/nodejs/node/pull/23603)
- [[`e84a7f027d`](https://github.com/nodejs/node/commit/e84a7f027d)] - **src**: add default initializer in tls_wrap (Richard Hoehn) [#23567](https://github.com/nodejs/node/pull/23567)
- [[`33351a112d`](https://github.com/nodejs/node/commit/33351a112d)] - **src**: use MallocedBuffer abstraction for buffers (Cody Hazelwood) [#23543](https://github.com/nodejs/node/pull/23543)
- [[`866d81cf39`](https://github.com/nodejs/node/commit/866d81cf39)] - **src**: use default initializers over settings fields on the constructor (Andrew J D McCann) [#23532](https://github.com/nodejs/node/pull/23532)
- [[`26fa85c65e`](https://github.com/nodejs/node/commit/26fa85c65e)] - **src**: remove unused UVHandle methods (MarianneDr) [#23535](https://github.com/nodejs/node/pull/23535)
- [[`35d9990140`](https://github.com/nodejs/node/commit/35d9990140)] - **src**: move default assignment of async_id\_ in async_wrap.h (David Corona) [#23495](https://github.com/nodejs/node/pull/23495)
- [[`ec7375ad0e`](https://github.com/nodejs/node/commit/ec7375ad0e)] - **src**: change constructor behavior in stream_base-inl.h (Ian Sutherland) [#23447](https://github.com/nodejs/node/pull/23447)
- [[`b5f5585b0a`](https://github.com/nodejs/node/commit/b5f5585b0a)] - **src**: throw if functions used as constructors in node_crypto.cc (Bruce A. MacNaughton) [#23582](https://github.com/nodejs/node/pull/23582)
- [[`fc963cd81c`](https://github.com/nodejs/node/commit/fc963cd81c)] - **src**: reduce platform worker barrier lifetime (Ali Ijaz Sheikh) [#23419](https://github.com/nodejs/node/pull/23419)
- [[`b61bbbbb03`](https://github.com/nodejs/node/commit/b61bbbbb03)] - **src**: trace_event: secondary storage for metadata (Ali Ijaz Sheikh) [#20900](https://github.com/nodejs/node/pull/20900)
- [[`ecacf33356`](https://github.com/nodejs/node/commit/ecacf33356)] - **src**: fix bug in MallocedBuffer constructor (Tobias Nießen) [#23434](https://github.com/nodejs/node/pull/23434)
- [[`a83096a65d`](https://github.com/nodejs/node/commit/a83096a65d)] - **src**: improve SSL version extraction logic (Gireesh Punathil) [#23050](https://github.com/nodejs/node/pull/23050)
- [[`f40b1dbe5d`](https://github.com/nodejs/node/commit/f40b1dbe5d)] - **src**: revert removal of SecureContext `_external` getter (Vitaly Dyatlov) [#21711](https://github.com/nodejs/node/pull/21711)
- [[`51fd86730f`](https://github.com/nodejs/node/commit/51fd86730f)] - **src**: remove unused limits header from util-inl.h (Daniel Bevenius) [#23353](https://github.com/nodejs/node/pull/23353)
- [[`5f21755e60`](https://github.com/nodejs/node/commit/5f21755e60)] - **src**: replace NO_RETURN with \[\[noreturn\]\] (Refael Ackermann) [#23337](https://github.com/nodejs/node/pull/23337)
- [[`4d21e34a6d`](https://github.com/nodejs/node/commit/4d21e34a6d)] - **src**: fix usage of deprecated v8::Date::New (Michaël Zasso) [#23288](https://github.com/nodejs/node/pull/23288)
- [[`c2fee5d1cb`](https://github.com/nodejs/node/commit/c2fee5d1cb)] - **src**: ready background workers before bootstrap (Ali Ijaz Sheikh) [#23233](https://github.com/nodejs/node/pull/23233)
- [[`6580ce54dc`](https://github.com/nodejs/node/commit/6580ce54dc)] - **src**: remove accidentally added src/txt (Joyee Cheung) [#23273](https://github.com/nodejs/node/pull/23273)
- [[`8f84613c93`](https://github.com/nodejs/node/commit/8f84613c93)] - **src**: use default parameters for `UVException()` (Anna Henningsen) [#23176](https://github.com/nodejs/node/pull/23176)
- [[`a7b59d6204`](https://github.com/nodejs/node/commit/a7b59d6204)] - **src**: flip Atomics.notify alias (Gus Caplan) [#22844](https://github.com/nodejs/node/pull/22844)
- [[`8989c76c6e`](https://github.com/nodejs/node/commit/8989c76c6e)] - **_Revert_** "**src**: implement query callbacks for vm" (Anna Henningsen) [#22911](https://github.com/nodejs/node/pull/22911)
- [[`85c356c10e`](https://github.com/nodejs/node/commit/85c356c10e)] - **src**: implement query callbacks for vm (Timothy Gu) [#22390](https://github.com/nodejs/node/pull/22390)
- [[`b85460498f`](https://github.com/nodejs/node/commit/b85460498f)] - **src**: remove old process.binding('trace_events').emit (James M Snell) [#22127](https://github.com/nodejs/node/pull/22127)
- [[`afc5636fe6`](https://github.com/nodejs/node/commit/afc5636fe6)] - **src**: rename WorkerThreadMain to PlatformWorkerThread (Michaël Zasso) [#21982](https://github.com/nodejs/node/pull/21982)
- [[`2faab111ef`](https://github.com/nodejs/node/commit/2faab111ef)] - **src**: remove defunct timer_wrap file (Jon Moss) [#21777](https://github.com/nodejs/node/pull/21777)
- [[`e767aa1a2e`](https://github.com/nodejs/node/commit/e767aa1a2e)] - **_Revert_** "**src**: make process.env.TZ setter clear tz cache" (Ruben Bridgewater) [#20228](https://github.com/nodejs/node/pull/20228)
- [[`20373c476d`](https://github.com/nodejs/node/commit/20373c476d)] - **stream**: undo internalBinding() conversion in compat mechanism (Anna Henningsen) [#23662](https://github.com/nodejs/node/pull/23662)
- [[`6a080ab782`](https://github.com/nodejs/node/commit/6a080ab782)] - **test**: add blocks and comments to fs-promises tests (Ian Sutherland) [#23627](https://github.com/nodejs/node/pull/23627)
- [[`b19f339bcf`](https://github.com/nodejs/node/commit/b19f339bcf)] - **test**: increase coverage for readfile with withFileTypes (christian-bromann) [#23557](https://github.com/nodejs/node/pull/23557)
- [[`3b014a1ead`](https://github.com/nodejs/node/commit/3b014a1ead)] - **test**: skip failing tests for osx mojave (jn99) [#23550](https://github.com/nodejs/node/pull/23550)
- [[`5c91b28f04`](https://github.com/nodejs/node/commit/5c91b28f04)] - **test**: fix argument order in assertion (Illescas, Ricardo) [#23581](https://github.com/nodejs/node/pull/23581)
- [[`c55f25abfa`](https://github.com/nodejs/node/commit/c55f25abfa)] - **test**: reversed params in assert.strictEqual() (Dusan Radovanovic) [#23591](https://github.com/nodejs/node/pull/23591)
- [[`24e79bdfc8`](https://github.com/nodejs/node/commit/24e79bdfc8)] - **test**: correct order of args in buffer compare (James Irwin) [#23521](https://github.com/nodejs/node/pull/23521)
- [[`a3c6a8d1a8`](https://github.com/nodejs/node/commit/a3c6a8d1a8)] - **test**: enable trace-events tests for workers (Richard Lau) [#23698](https://github.com/nodejs/node/pull/23698)
- [[`add4f019e4`](https://github.com/nodejs/node/commit/add4f019e4)] - **test**: check codes of thrown errors (Nancy Truong) [#23519](https://github.com/nodejs/node/pull/23519)
- [[`b5c75a331d`](https://github.com/nodejs/node/commit/b5c75a331d)] - **test**: error when empty buffer is passed to filehandle.read() (Masashi Hirano) [#23250](https://github.com/nodejs/node/pull/23250)
- [[`a29631b237`](https://github.com/nodejs/node/commit/a29631b237)] - **test**: error when empty buffer is passed to fs.read() (shisama) [#23141](https://github.com/nodejs/node/pull/23141)
- [[`6445307716`](https://github.com/nodejs/node/commit/6445307716)] - **test**: fix strictEqual arguments order (Jonathan Samines) [#23486](https://github.com/nodejs/node/pull/23486)
- [[`06890ff01c`](https://github.com/nodejs/node/commit/06890ff01c)] - **test**: add test coverage for fs.truncate (christian-bromann) [#23620](https://github.com/nodejs/node/pull/23620)
- [[`eb48f287ab`](https://github.com/nodejs/node/commit/eb48f287ab)] - **test**: use smaller keys for a faster keygen test (Sam Roberts) [#23430](https://github.com/nodejs/node/pull/23430)
- [[`d5525986a8`](https://github.com/nodejs/node/commit/d5525986a8)] - **test**: increased code coverage for slowCases (Jared Haines) [#23592](https://github.com/nodejs/node/pull/23592)
- [[`0b510da6ba`](https://github.com/nodejs/node/commit/0b510da6ba)] - **test**: assertions arguments match docs (Amanuel Ghebreweldi) [#23594](https://github.com/nodejs/node/pull/23594)
- [[`58faae9f3a`](https://github.com/nodejs/node/commit/58faae9f3a)] - **test**: fix assert.strictEqual() argument order (Derek) [#23598](https://github.com/nodejs/node/pull/23598)
- [[`bcd14b2c0f`](https://github.com/nodejs/node/commit/bcd14b2c0f)] - **test**: fix assert parameter order in test-https-localaddress.js (Ian Sutherland) [#23599](https://github.com/nodejs/node/pull/23599)
- [[`1c6a55146e`](https://github.com/nodejs/node/commit/1c6a55146e)] - **test**: change order of assert.strictEquals arguments (Chuck Theobald) [#23600](https://github.com/nodejs/node/pull/23600)
- [[`e345897f06`](https://github.com/nodejs/node/commit/e345897f06)] - **test**: fix assert equal order of arguments (David Jiang) [#23602](https://github.com/nodejs/node/pull/23602)
- [[`d778f9e1f0`](https://github.com/nodejs/node/commit/d778f9e1f0)] - **test**: fix order of assert args in client response domain test (Emily Kolar) [#23604](https://github.com/nodejs/node/pull/23604)
- [[`d08ac84aaa`](https://github.com/nodejs/node/commit/d08ac84aaa)] - **test**: re-order strictEqual paramater calls (Paul Tichonczuk) [#23607](https://github.com/nodejs/node/pull/23607)
- [[`50a280acdb`](https://github.com/nodejs/node/commit/50a280acdb)] - **test**: fix assertions args order (Milton Sosa) [#23608](https://github.com/nodejs/node/pull/23608)
- [[`ff75d98479`](https://github.com/nodejs/node/commit/ff75d98479)] - **test**: fix parameters in test-repl.js (Israel Ortiz) [#23609](https://github.com/nodejs/node/pull/23609)
- [[`c160aacd20`](https://github.com/nodejs/node/commit/c160aacd20)] - **test**: reverse arguments in assert.strictEqual (Vsevolod Geraskin) [#23613](https://github.com/nodejs/node/pull/23613)
- [[`4422269274`](https://github.com/nodejs/node/commit/4422269274)] - **test**: update assertion parameter order (Sean Healy) [#23614](https://github.com/nodejs/node/pull/23614)
- [[`2f481f7bb0`](https://github.com/nodejs/node/commit/2f481f7bb0)] - **test**: fix backward assertion arguments (Stéphane Vasseur) [#23616](https://github.com/nodejs/node/pull/23616)
- [[`907461c289`](https://github.com/nodejs/node/commit/907461c289)] - **test**: reversed 1st and 2nd arguments for assert.strictEqual() (vchoubey08) [#23617](https://github.com/nodejs/node/pull/23617)
- [[`1a43e53f1a`](https://github.com/nodejs/node/commit/1a43e53f1a)] - **test**: correct assertion argument order (Jeff Marvin) [#23618](https://github.com/nodejs/node/pull/23618)
- [[`e7cbc3f4f1`](https://github.com/nodejs/node/commit/e7cbc3f4f1)] - **test**: fix assertion order (erickwendel) [#23626](https://github.com/nodejs/node/pull/23626)
- [[`42f43d5827`](https://github.com/nodejs/node/commit/42f43d5827)] - **test**: updated assert test values to doc standards (keeysnc) [#23593](https://github.com/nodejs/node/pull/23593)
- [[`af59b9dd02`](https://github.com/nodejs/node/commit/af59b9dd02)] - **test**: switch order of assertion arguments (Mel) [#23563](https://github.com/nodejs/node/pull/23563)
- [[`ca24bcf571`](https://github.com/nodejs/node/commit/ca24bcf571)] - **test**: fix assert.strictEqual() argument order (Savio Resende) [#23564](https://github.com/nodejs/node/pull/23564)
- [[`7e79e012b6`](https://github.com/nodejs/node/commit/7e79e012b6)] - **test**: fix parameter order of assertions (Pete Lombardo) [#23565](https://github.com/nodejs/node/pull/23565)
- [[`2d5b6c2bb3`](https://github.com/nodejs/node/commit/2d5b6c2bb3)] - **test**: fix assert value order (Ethan Weber) [#23566](https://github.com/nodejs/node/pull/23566)
- [[`d49937a934`](https://github.com/nodejs/node/commit/d49937a934)] - **test**: fix strictEqual order for timers test (Saleh Abdel Motaal) [#23568](https://github.com/nodejs/node/pull/23568)
- [[`986b6cb01f`](https://github.com/nodejs/node/commit/986b6cb01f)] - **test**: corrected assertion arguments order (francois) [#23569](https://github.com/nodejs/node/pull/23569)
- [[`c3140d078b`](https://github.com/nodejs/node/commit/c3140d078b)] - **test**: fix strictEqual input parameters order (AlixAng) [#23570](https://github.com/nodejs/node/pull/23570)
- [[`b49f4a93a6`](https://github.com/nodejs/node/commit/b49f4a93a6)] - **test**: fix order of arguments passed to strictEqual (Joe Shindelar) [#23571](https://github.com/nodejs/node/pull/23571)
- [[`2d86696f35`](https://github.com/nodejs/node/commit/2d86696f35)] - **test**: augment tests for SourceTextModule (Andrew Eisenberg) [#23572](https://github.com/nodejs/node/pull/23572)
- [[`d35965bbf9`](https://github.com/nodejs/node/commit/d35965bbf9)] - **test**: fix arguments ordering for assertions to match the docs (Liran Tal) [#23575](https://github.com/nodejs/node/pull/23575)
- [[`152e7a53c2`](https://github.com/nodejs/node/commit/152e7a53c2)] - **test**: fixed strictEqual arguments order (Ruy Adorno) [#23576](https://github.com/nodejs/node/pull/23576)
- [[`0d9215986f`](https://github.com/nodejs/node/commit/0d9215986f)] - **test**: add crypto.scrypt test case with different encoding (Yitong) [#23578](https://github.com/nodejs/node/pull/23578)
- [[`96c1dd428c`](https://github.com/nodejs/node/commit/96c1dd428c)] - **test**: reversed actual and expected values for .strictEqual() (Salman Shakeel) [#23579](https://github.com/nodejs/node/pull/23579)
- [[`4b873ee18b`](https://github.com/nodejs/node/commit/4b873ee18b)] - **test**: increased code coverage for proxySessionHandler (Justin Lee) [#23583](https://github.com/nodejs/node/pull/23583)
- [[`62c6e446bd`](https://github.com/nodejs/node/commit/62c6e446bd)] - **test**: fix assertion arguments order (seantcoyote) [#23584](https://github.com/nodejs/node/pull/23584)
- [[`99a7e25ba1`](https://github.com/nodejs/node/commit/99a7e25ba1)] - **test**: fix assert.strictEqual() parameter order in test-path-maklong.js (blakehall) [#23587](https://github.com/nodejs/node/pull/23587)
- [[`53fb82d6b1`](https://github.com/nodejs/node/commit/53fb82d6b1)] - **test**: fix argument order in assertions (Illescas, Ricardo) [#23589](https://github.com/nodejs/node/pull/23589)
- [[`59a221d2a0`](https://github.com/nodejs/node/commit/59a221d2a0)] - **test**: fix order of parameters to assert.strictEqual (Jason Nutter) [#23590](https://github.com/nodejs/node/pull/23590)
- [[`e806167fec`](https://github.com/nodejs/node/commit/e806167fec)] - **test**: removed unused variable in fs-watch-file-slow (Maki Toda) [#23548](https://github.com/nodejs/node/pull/23548)
- [[`0d9e54b3d6`](https://github.com/nodejs/node/commit/0d9e54b3d6)] - **test**: update strictEqual arguments order (Clinton Pahl) [#23552](https://github.com/nodejs/node/pull/23552)
- [[`c254e40b18`](https://github.com/nodejs/node/commit/c254e40b18)] - **test**: removed unused error variable in try catch (Murtaza H) [#23553](https://github.com/nodejs/node/pull/23553)
- [[`90467658ea`](https://github.com/nodejs/node/commit/90467658ea)] - **test**: reverse order of args in reconnect-error assert (Jackelin Herrera) [#23555](https://github.com/nodejs/node/pull/23555)
- [[`3604d78cd3`](https://github.com/nodejs/node/commit/3604d78cd3)] - **test**: added async-hook benchmark (peter) [#23556](https://github.com/nodejs/node/pull/23556)
- [[`4118e90b43`](https://github.com/nodejs/node/commit/4118e90b43)] - **test**: fix order of assert arguments in vm-new-script-this-context (Victor Poriazov) [#23558](https://github.com/nodejs/node/pull/23558)
- [[`2f38550458`](https://github.com/nodejs/node/commit/2f38550458)] - **test**: modernize test-crypto-domain (naris93) [#23559](https://github.com/nodejs/node/pull/23559)
- [[`7298f8a147`](https://github.com/nodejs/node/commit/7298f8a147)] - **test**: fix strictEqual assertion order on readline tests (Joe Grosspietsch) [#23561](https://github.com/nodejs/node/pull/23561)
- [[`bea0819126`](https://github.com/nodejs/node/commit/bea0819126)] - **test**: switch strictEqual parameters - actual first before expected (Chris Bautista) [#23537](https://github.com/nodejs/node/pull/23537)
- [[`bd3b52fc17`](https://github.com/nodejs/node/commit/bd3b52fc17)] - **test**: assert.strictEqual parameters ordered correctly (Justin denBroeder) [#23538](https://github.com/nodejs/node/pull/23538)
- [[`07d3f470da`](https://github.com/nodejs/node/commit/07d3f470da)] - **test**: fix assert.strictEqual() arguments order (Ivan Lukasevych) [#23539](https://github.com/nodejs/node/pull/23539)
- [[`ef2cbf826a`](https://github.com/nodejs/node/commit/ef2cbf826a)] - **test**: reverse the order of assertion statement arguments in pingpong test (Allan Zheng) [#23540](https://github.com/nodejs/node/pull/23540)
- [[`44b569c8b0`](https://github.com/nodejs/node/commit/44b569c8b0)] - **test**: added test for generateKeyPair (David Xue) [#23541](https://github.com/nodejs/node/pull/23541)
- [[`ea90776227`](https://github.com/nodejs/node/commit/ea90776227)] - **test**: swap expected and actual arguments in assert.strictEqual() (Erin Bush) [#23542](https://github.com/nodejs/node/pull/23542)
- [[`1f6c86d1ea`](https://github.com/nodejs/node/commit/1f6c86d1ea)] - **test**: fix assertions argument order (KelvinLawHF1) [#23544](https://github.com/nodejs/node/pull/23544)
- [[`0655229240`](https://github.com/nodejs/node/commit/0655229240)] - **test**: fix assertion argument order (Carl Richmond) [#23545](https://github.com/nodejs/node/pull/23545)
- [[`4518ca9c32`](https://github.com/nodejs/node/commit/4518ca9c32)] - **test**: refactor callback functions to arrow functions (Sean Healy) [#23546](https://github.com/nodejs/node/pull/23546)
- [[`c9afea9e79`](https://github.com/nodejs/node/commit/c9afea9e79)] - **test**: updating assertion and expect order in test-tls-client-verify.js (Eli Itah) [#23547](https://github.com/nodejs/node/pull/23547)
- [[`47b7f2ac44`](https://github.com/nodejs/node/commit/47b7f2ac44)] - **test**: use correct argument order for assert.strictEqual() (Oktavianus Ludiro) [#23527](https://github.com/nodejs/node/pull/23527)
- [[`1fd1e605be`](https://github.com/nodejs/node/commit/1fd1e605be)] - **test**: corrected the order of arguments in assert.strictEqual() (Diana Lee) [#23528](https://github.com/nodejs/node/pull/23528)
- [[`cb9fe73ab7`](https://github.com/nodejs/node/commit/cb9fe73ab7)] - **test**: fix assert.strictEqual() argument order (ssamuels0916) [#23529](https://github.com/nodejs/node/pull/23529)
- [[`1c220889e0`](https://github.com/nodejs/node/commit/1c220889e0)] - **test**: fix strictEqual assertion argument in test-tls-ecdh-auto (jaxyz) [#23530](https://github.com/nodejs/node/pull/23530)
- [[`d0a77f0a86`](https://github.com/nodejs/node/commit/d0a77f0a86)] - **test**: correct labelling of asserts errors (nofwayy) [#23531](https://github.com/nodejs/node/pull/23531)
- [[`ffab8ba33f`](https://github.com/nodejs/node/commit/ffab8ba33f)] - **test**: reorder asserts arguments (Marcos Frony) [#23534](https://github.com/nodejs/node/pull/23534)
- [[`69365ef25c`](https://github.com/nodejs/node/commit/69365ef25c)] - **test**: updating assertion on test so it fits the new method signature (<EMAIL>) [#23536](https://github.com/nodejs/node/pull/23536)
- [[`9e6c983884`](https://github.com/nodejs/node/commit/9e6c983884)] - **test**: refactor functions to es6 (Michael Chen) [#23510](https://github.com/nodejs/node/pull/23510)
- [[`b06113aba1`](https://github.com/nodejs/node/commit/b06113aba1)] - **test**: replaced functions with arrow functions (edgarzapeka) [#23511](https://github.com/nodejs/node/pull/23511)
- [[`e4e89837db`](https://github.com/nodejs/node/commit/e4e89837db)] - **test**: corret assertion arg order in test-regress-GH-892.js (Elvis-Philip N) [#23513](https://github.com/nodejs/node/pull/23513)
- [[`05ce3946ee`](https://github.com/nodejs/node/commit/05ce3946ee)] - **test**: fix test-dgram-pingpong assertion arg order (David Ward) [#23514](https://github.com/nodejs/node/pull/23514)
- [[`4958e7ad34`](https://github.com/nodejs/node/commit/4958e7ad34)] - **test**: fix assert.strictEqual() argument order (Ben Schaaf) [#23515](https://github.com/nodejs/node/pull/23515)
- [[`1eea1aa513`](https://github.com/nodejs/node/commit/1eea1aa513)] - **test**: fix assert.strictEqual arg order in test-tls-ecdh-multiple.js (Takdeer Sodhan) [#23516](https://github.com/nodejs/node/pull/23516)
- [[`d5485ec90b`](https://github.com/nodejs/node/commit/d5485ec90b)] - **test**: use the correct parameter order on assert.strictEqual() (Tyler Vann-Campbell) [#23520](https://github.com/nodejs/node/pull/23520)
- [[`e9efac6dfd`](https://github.com/nodejs/node/commit/e9efac6dfd)] - **test**: fix assert order in test-vm-context (Lee Gray) [#23523](https://github.com/nodejs/node/pull/23523)
- [[`ba5cf7b2a9`](https://github.com/nodejs/node/commit/ba5cf7b2a9)] - **test**: switch arguments of assert() (Arne Schramm) [#23524](https://github.com/nodejs/node/pull/23524)
- [[`87eeb6b373`](https://github.com/nodejs/node/commit/87eeb6b373)] - **test**: swap assert argument order in test-vm-create-and-run-in-context.js (Pascal Lambert) [#23525](https://github.com/nodejs/node/pull/23525)
- [[`2cd0ef09ec`](https://github.com/nodejs/node/commit/2cd0ef09ec)] - **test**: fix order of assert.strictEqual() args to actual, expected (Joshua Belcher) [#23501](https://github.com/nodejs/node/pull/23501)
- [[`f6204c58c0`](https://github.com/nodejs/node/commit/f6204c58c0)] - **test**: fixed incorrect variable order in assert.strictEqual() (Daniyal Mokhammad) [#23502](https://github.com/nodejs/node/pull/23502)
- [[`fd3b1d115c`](https://github.com/nodejs/node/commit/fd3b1d115c)] - **test**: properly order test assertion variables (David Scott) [#23503](https://github.com/nodejs/node/pull/23503)
- [[`e087f2665c`](https://github.com/nodejs/node/commit/e087f2665c)] - **test**: modernize test-child-process-flush-stdio (Viacheslav Liakhov) [#23504](https://github.com/nodejs/node/pull/23504)
- [[`c377053e82`](https://github.com/nodejs/node/commit/c377053e82)] - **test**: put expected assert value in correct place (Jean-Francois Arseneau) [#23505](https://github.com/nodejs/node/pull/23505)
- [[`345974a8ad`](https://github.com/nodejs/node/commit/345974a8ad)] - **test**: fix argument order in assertions (Illescas, Ricardo) [#23506](https://github.com/nodejs/node/pull/23506)
- [[`8cc52b0bc0`](https://github.com/nodejs/node/commit/8cc52b0bc0)] - **test**: fix assertions args order in test/parallel/test-fs-chmod.js (Milton Sosa) [#23507](https://github.com/nodejs/node/pull/23507)
- [[`556293283a`](https://github.com/nodejs/node/commit/556293283a)] - **test**: fix strictEqual assertion arguments (Alejandro Oviedo Garcia) [#23508](https://github.com/nodejs/node/pull/23508)
- [[`bb8dd485c2`](https://github.com/nodejs/node/commit/bb8dd485c2)] - **test**: fix ordering of assertion values (Andrew MacCuaig)
- [[`5bc49f9c0c`](https://github.com/nodejs/node/commit/5bc49f9c0c)] - **test**: update function keywords to fat arrows (Robert Monks) [#23493](https://github.com/nodejs/node/pull/23493)
- [[`697359637a`](https://github.com/nodejs/node/commit/697359637a)] - **test**: reversed arguments in strictqual to reflect documentation (scabhi) [#23494](https://github.com/nodejs/node/pull/23494)
- [[`e0eb19b1d2`](https://github.com/nodejs/node/commit/e0eb19b1d2)] - **test**: modernized test to use arrow functions (Greg Goforth) [#23496](https://github.com/nodejs/node/pull/23496)
- [[`670770e275`](https://github.com/nodejs/node/commit/670770e275)] - **test**: use arrow functions in test-exception-handler (Jenna Zeigen) [#23498](https://github.com/nodejs/node/pull/23498)
- [[`ab052af697`](https://github.com/nodejs/node/commit/ab052af697)] - **test**: fix argument order in asserts (@CAYdenberg) [#23499](https://github.com/nodejs/node/pull/23499)
- [[`0eb5f13062`](https://github.com/nodejs/node/commit/0eb5f13062)] - **test**: modernizing test-dgram-listen-after-bind with arrow functions (chrisforrette) [#23500](https://github.com/nodejs/node/pull/23500)
- [[`945f9d728e`](https://github.com/nodejs/node/commit/945f9d728e)] - **test**: fix strictEqual argument order (Felix Schlenkrich) [#23490](https://github.com/nodejs/node/pull/23490)
- [[`3fc8c7aca8`](https://github.com/nodejs/node/commit/3fc8c7aca8)] - **test**: rename process.argv\[0\] to process.execPath, rename ex to err (Kayla Altepeter) [#23488](https://github.com/nodejs/node/pull/23488)
- [[`280aed1312`](https://github.com/nodejs/node/commit/280aed1312)] - **test**: fix assertion argument order (Carl Richmond) [#23489](https://github.com/nodejs/node/pull/23489)
- [[`b041922663`](https://github.com/nodejs/node/commit/b041922663)] - **test**: fix assertion order test-tls-server-verify (Carolina Pinzon) [#23549](https://github.com/nodejs/node/pull/23549)
- [[`147102372d`](https://github.com/nodejs/node/commit/147102372d)] - **test**: move tick.js from test/async-hooks to test/common (Artur Hayrapetyan) [#23551](https://github.com/nodejs/node/pull/23551)
- [[`39377bc58f`](https://github.com/nodejs/node/commit/39377bc58f)] - **test**: fix assertion order (Chris Nguyen) [#23533](https://github.com/nodejs/node/pull/23533)
- [[`e9962b9cf9`](https://github.com/nodejs/node/commit/e9962b9cf9)] - **test**: change to arrow functions in send-bad-arguments (Anna Zhao) [#23483](https://github.com/nodejs/node/pull/23483)
- [[`d70a0cd294`](https://github.com/nodejs/node/commit/d70a0cd294)] - **test**: removed unused variable (Michal Hynek) [#23481](https://github.com/nodejs/node/pull/23481)
- [[`a797923ba5`](https://github.com/nodejs/node/commit/a797923ba5)] - **test**: fix argument order for assert.strictEqual (Stacey) [#23485](https://github.com/nodejs/node/pull/23485)
- [[`6936f9cb14`](https://github.com/nodejs/node/commit/6936f9cb14)] - **test**: fix assert.strictEqual params order (Rock Hu) [#23480](https://github.com/nodejs/node/pull/23480)
- [[`b6e9f99910`](https://github.com/nodejs/node/commit/b6e9f99910)] - **test**: removed mustCallAsync from common and added inside testcase (Quinn Langille) [#23467](https://github.com/nodejs/node/pull/23467)
- [[`1408e323f9`](https://github.com/nodejs/node/commit/1408e323f9)] - **test**: remove unused "e" from catch in http2 test (Stephen Heitman) [#23476](https://github.com/nodejs/node/pull/23476)
- [[`b5c698d328`](https://github.com/nodejs/node/commit/b5c698d328)] - **test**: remove unused variable from catch (Paige Kato) [#23477](https://github.com/nodejs/node/pull/23477)
- [[`e527321a98`](https://github.com/nodejs/node/commit/e527321a98)] - **test**: inline common module boolean (ashleysimpson) [#23479](https://github.com/nodejs/node/pull/23479)
- [[`cbc140fb6a`](https://github.com/nodejs/node/commit/cbc140fb6a)] - **test**: swap the order arguments are passed to assert (Dylson Valente Neto) [#23580](https://github.com/nodejs/node/pull/23580)
- [[`f1997b7150`](https://github.com/nodejs/node/commit/f1997b7150)] - **test**: flip assertion arguments for make-callback/test.js (Tim Cheung) [#23470](https://github.com/nodejs/node/pull/23470)
- [[`ec675b8ea4`](https://github.com/nodejs/node/commit/ec675b8ea4)] - **test**: replace function with arrow function (Yitong) [#23474](https://github.com/nodejs/node/pull/23474)
- [[`923f37ff7c`](https://github.com/nodejs/node/commit/923f37ff7c)] - **test**: swap actual and expected in assertions (Yitong) [#23474](https://github.com/nodejs/node/pull/23474)
- [[`90504b97cd`](https://github.com/nodejs/node/commit/90504b97cd)] - **test**: correctly order assertion arguments (Emily Kolar) [#23473](https://github.com/nodejs/node/pull/23473)
- [[`2d2388d9dd`](https://github.com/nodejs/node/commit/2d2388d9dd)] - **test**: fix errors in test-buffer-alloc.js (Rich Trott) [#23645](https://github.com/nodejs/node/pull/23645)
- [[`5e68333ead`](https://github.com/nodejs/node/commit/5e68333ead)] - **test**: mark `test-http2-session-timeout` as flake on ARM (Refael Ackermann) [#23639](https://github.com/nodejs/node/pull/23639)
- [[`2d0532e7c1`](https://github.com/nodejs/node/commit/2d0532e7c1)] - **test**: update test-cluster-worker-events to use arrow functions (S. Everett Abbott) [#23469](https://github.com/nodejs/node/pull/23469)
- [[`ec8fbfb26a`](https://github.com/nodejs/node/commit/ec8fbfb26a)] - **test**: correct order for assert.strictEqual for inspector-helper test (Maggie Nolan) [#23468](https://github.com/nodejs/node/pull/23468)
- [[`722e0d4921`](https://github.com/nodejs/node/commit/722e0d4921)] - **test**: fix incorrect expectation order (Amie) [#23466](https://github.com/nodejs/node/pull/23466)
- [[`b35d234b12`](https://github.com/nodejs/node/commit/b35d234b12)] - **test**: remove unused e variable in catch statement (Denny Scott) [#23465](https://github.com/nodejs/node/pull/23465)
- [[`30c48fd296`](https://github.com/nodejs/node/commit/30c48fd296)] - **test**: correct assert test (Richard Markins) [#23463](https://github.com/nodejs/node/pull/23463)
- [[`7d0f50cc34`](https://github.com/nodejs/node/commit/7d0f50cc34)] - **test**: fix incorrect ordering of args in assert.strictEqual() (mdaum) [#23461](https://github.com/nodejs/node/pull/23461)
- [[`09a664431a`](https://github.com/nodejs/node/commit/09a664431a)] - **test**: swap assert.strictEqual args to actual, expected (epeden) [#23459](https://github.com/nodejs/node/pull/23459)
- [[`47784c47f4`](https://github.com/nodejs/node/commit/47784c47f4)] - **test**: fix assert.strictEqual argument order (andy addington) [#23457](https://github.com/nodejs/node/pull/23457)
- [[`ce7555ddeb`](https://github.com/nodejs/node/commit/ce7555ddeb)] - **test**: strictEqual correct order for http-information-processing test (Ivan Sieder) [#23456](https://github.com/nodejs/node/pull/23456)
- [[`4296837468`](https://github.com/nodejs/node/commit/4296837468)] - **test**: replace assert.throws w/ common.expectsError (Andrew Eisenberg) [#23454](https://github.com/nodejs/node/pull/23454)
- [[`aa63e5539e`](https://github.com/nodejs/node/commit/aa63e5539e)] - **test**: fix http local address test assertion (Danu Widatama) [#23451](https://github.com/nodejs/node/pull/23451)
- [[`3829e99b29`](https://github.com/nodejs/node/commit/3829e99b29)] - **test**: fix order of values in test assertions (Jared Haines) [#23450](https://github.com/nodejs/node/pull/23450)
- [[`1c36943b8d`](https://github.com/nodejs/node/commit/1c36943b8d)] - **test**: fix `assert.strictEqual` arguments in test/parallel/test-c-ares.js (jungkumseok) [#23448](https://github.com/nodejs/node/pull/23448)
- [[`db2be04eb8`](https://github.com/nodejs/node/commit/db2be04eb8)] - **test**: improve test coverage for fs module (<EMAIL>) [#23601](https://github.com/nodejs/node/pull/23601)
- [[`a0468fe900`](https://github.com/nodejs/node/commit/a0468fe900)] - **test**: fix parameter order passed to strictEqual (Shannon) [#23577](https://github.com/nodejs/node/pull/23577)
- [[`0579784eab`](https://github.com/nodejs/node/commit/0579784eab)] - **test**: adding test coverage for SourceTextModule.evaluate (Kayla Altepeter) [#23595](https://github.com/nodejs/node/pull/23595)
- [[`208ee3e570`](https://github.com/nodejs/node/commit/208ee3e570)] - **test**: move some gc tests back to parallel/, unmark flaky (Anna Henningsen) [#23356](https://github.com/nodejs/node/pull/23356)
- [[`939a27e91b`](https://github.com/nodejs/node/commit/939a27e91b)] - **test**: improve test-gc-http-client-onerror (Denys Otrishko) [#23196](https://github.com/nodejs/node/pull/23196)
- [[`91bad82638`](https://github.com/nodejs/node/commit/91bad82638)] - **test**: improve test-gc-http-client-connaborted (Denys Otrishko) [#23193](https://github.com/nodejs/node/pull/23193)
- [[`bd88c9864f`](https://github.com/nodejs/node/commit/bd88c9864f)] - **test**: fix assert.strictEqual argument order (et4891) [#23518](https://github.com/nodejs/node/pull/23518)
- [[`f2c57e7e1c`](https://github.com/nodejs/node/commit/f2c57e7e1c)] - **test**: fixing assertion value order (Joe Sepi) [#23574](https://github.com/nodejs/node/pull/23574)
- [[`66eb35f1e6`](https://github.com/nodejs/node/commit/66eb35f1e6)] - **test**: rename common.ddCommand() (Rich Trott) [#23411](https://github.com/nodejs/node/pull/23411)
- [[`8561462005`](https://github.com/nodejs/node/commit/8561462005)] - **test**: refactor common.ddCommand() (Rich Trott) [#23411](https://github.com/nodejs/node/pull/23411)
- [[`0fa857f619`](https://github.com/nodejs/node/commit/0fa857f619)] - **test**: add logging to test-worker-memory (Rich Trott) [#23418](https://github.com/nodejs/node/pull/23418)
- [[`3c4d316d03`](https://github.com/nodejs/node/commit/3c4d316d03)] - **test**: add test for a vm indexed property (conectado) [#23318](https://github.com/nodejs/node/pull/23318)
- [[`e774d1b898`](https://github.com/nodejs/node/commit/e774d1b898)] - **test**: fix compiler warning in doc/api/addons.md (Daniel Bevenius) [#23323](https://github.com/nodejs/node/pull/23323)
- [[`c030854a54`](https://github.com/nodejs/node/commit/c030854a54)] - **test**: add WPT console-tests-historical (Rich Trott) [#23340](https://github.com/nodejs/node/pull/23340)
- [[`bd7e57a023`](https://github.com/nodejs/node/commit/bd7e57a023)] - **test**: separate WPT console test from other test (Rich Trott) [#23340](https://github.com/nodejs/node/pull/23340)
- [[`172e552655`](https://github.com/nodejs/node/commit/172e552655)] - **test**: add WPT console-label-conversion test (Rich Trott) [#23340](https://github.com/nodejs/node/pull/23340)
- [[`0b61f3970e`](https://github.com/nodejs/node/commit/0b61f3970e)] - **test**: rename WPT console test (Rich Trott) [#23340](https://github.com/nodejs/node/pull/23340)
- [[`52b58a2ac5`](https://github.com/nodejs/node/commit/52b58a2ac5)] - **test**: fix broken test (cjihrig) [#23232](https://github.com/nodejs/node/pull/23232)
- [[`8e189794df`](https://github.com/nodejs/node/commit/8e189794df)] - **test**: remove skip of OS X bug (Rich Trott) [#22546](https://github.com/nodejs/node/pull/22546)
- [[`1f1675817c`](https://github.com/nodejs/node/commit/1f1675817c)] - **test**: check option start or end is not safe integer (Masashi Hirano) [#21704](https://github.com/nodejs/node/pull/21704)
- [[`60ef7d1a8f`](https://github.com/nodejs/node/commit/60ef7d1a8f)] - **test**: fix assertion in test-console (Luigi Pinca) [#20557](https://github.com/nodejs/node/pull/20557)
- [[`7db4281e52`](https://github.com/nodejs/node/commit/7db4281e52)] - **tls**: close StreamWrap and its stream correctly (Ouyang Yadong) [#23654](https://github.com/nodejs/node/pull/23654)
- [[`934eb7ec59`](https://github.com/nodejs/node/commit/934eb7ec59)] - **tls**: prevent multiple connection errors (cjihrig) [#23636](https://github.com/nodejs/node/pull/23636)
- [[`d1a23cc954`](https://github.com/nodejs/node/commit/d1a23cc954)] - **tls**: update try catch syntax (Matt Jiles) [#23484](https://github.com/nodejs/node/pull/23484)
- [[`318f1cdc99`](https://github.com/nodejs/node/commit/318f1cdc99)] - **tls**: make StreamWrap work correctly in "drain" callback (Ouyang Yadong) [#23294](https://github.com/nodejs/node/pull/23294)
- [[`dc33b3e811`](https://github.com/nodejs/node/commit/dc33b3e811)] - **tls**: update test & docs for ArrayBuffer/DataView (Beni von Cheni) [#23210](https://github.com/nodejs/node/pull/23210)
- [[`cdd58e6bd4`](https://github.com/nodejs/node/commit/cdd58e6bd4)] - **tools**: clarify commit message linting (Rich Trott) [#23742](https://github.com/nodejs/node/pull/23742)
- [[`40280e62a3`](https://github.com/nodejs/node/commit/40280e62a3)] - **tools**: do not lint commit message if var undefined (Rich Trott) [#23725](https://github.com/nodejs/node/pull/23725)
- [[`77b3666b84`](https://github.com/nodejs/node/commit/77b3666b84)] - **tools**: prefer filter to remove empty strings (Sakthipriyan Vairamani (thefourtheye)) [#23727](https://github.com/nodejs/node/pull/23727)
- [[`74ebfa379a`](https://github.com/nodejs/node/commit/74ebfa379a)] - **tools**: update ESLint to 5.7.0 (cjihrig) [#23629](https://github.com/nodejs/node/pull/23629)
- [[`8460df4334`](https://github.com/nodejs/node/commit/8460df4334)] - **tools**: update node-lint-md-cli-rollup (Rich Trott) [#23358](https://github.com/nodejs/node/pull/23358)
- [[`47af3a1bfd`](https://github.com/nodejs/node/commit/47af3a1bfd)] - **tools,icu**: read full ICU version info from file (Refael Ackermann) [#23269](https://github.com/nodejs/node/pull/23269)
- [[`74c4bb7e77`](https://github.com/nodejs/node/commit/74c4bb7e77)] - **tools,test**: add list of slow tests (Refael Ackermann) [#23251](https://github.com/nodejs/node/pull/23251)
- [[`5b79d55ce3`](https://github.com/nodejs/node/commit/5b79d55ce3)] - **tools,test**: cleanup and dedup code (Refael Ackermann) [#23251](https://github.com/nodejs/node/pull/23251)
- [[`1ef83c882b`](https://github.com/nodejs/node/commit/1ef83c882b)] - **trace_events**: destroy platform before tracing (Ali Ijaz Sheikh) [#22938](https://github.com/nodejs/node/pull/22938)
- [[`4b7cd4bd60`](https://github.com/nodejs/node/commit/4b7cd4bd60)] - **trace_events**: add trace category enabled tracking (James M Snell) [#22128](https://github.com/nodejs/node/pull/22128)
- [[`c85933cbd0`](https://github.com/nodejs/node/commit/c85933cbd0)] - **trace_events,async_hooks**: use intrinsic trace (James M Snell) [#22127](https://github.com/nodejs/node/pull/22127)
- [[`c834be0a06`](https://github.com/nodejs/node/commit/c834be0a06)] - **_Revert_** "**tty**: make \_read throw ERR_TTY_WRITABLE_NOT_READABLE" (Anna Henningsen) [#23053](https://github.com/nodejs/node/pull/23053)
- [[`f4e4ef5cad`](https://github.com/nodejs/node/commit/f4e4ef5cad)] - **util**: handle null prototype on inspect (Anto Aravinth) [#22331](https://github.com/nodejs/node/pull/22331)
- [[`849aaaeeb0`](https://github.com/nodejs/node/commit/849aaaeeb0)] - **_Revert_** "**util**: change util.inspect depth default" (Anna Henningsen) [#20017](https://github.com/nodejs/node/pull/20017)
- [[`85373aeb4c`](https://github.com/nodejs/node/commit/85373aeb4c)] - **_Revert_** "**util**: change %o depth default" (Anna Henningsen) [#20017](https://github.com/nodejs/node/pull/20017)
- [[`2f83ddc353`](https://github.com/nodejs/node/commit/2f83ddc353)] - **vm**: pass parsing_context to ScriptCompiler::CompileFunctionInContext (Dara Hayes) [#23206](https://github.com/nodejs/node/pull/23206)
- [[`6487f07e0c`](https://github.com/nodejs/node/commit/6487f07e0c)] - **vm**: add dynamic import support (Gus Caplan) [#22381](https://github.com/nodejs/node/pull/22381)
- [[`7673de8f58`](https://github.com/nodejs/node/commit/7673de8f58)] - **worker**: remove delete MessagePort.prototype.hasRef (James Traver) [#23471](https://github.com/nodejs/node/pull/23471)
- [[`188ffcb960`](https://github.com/nodejs/node/commit/188ffcb960)] - **zlib**: refactor zlib internals (Anna Henningsen) [#23360](https://github.com/nodejs/node/pull/23360)
- [[`e0828635c5`](https://github.com/nodejs/node/commit/e0828635c5)] - **zlib**: generate error code names in C++ (Anna Henningsen) [#23413](https://github.com/nodejs/node/pull/23413)

Windows 32-bit Installer: https://nodejs.org/dist/v11.0.0/node-v11.0.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v11.0.0/node-v11.0.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v11.0.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v11.0.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v11.0.0/node-v11.0.0.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v11.0.0/node-v11.0.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v11.0.0/node-v11.0.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v11.0.0/node-v11.0.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v11.0.0/node-v11.0.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v11.0.0/node-v11.0.0-aix-ppc64.tar.gz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v11.0.0/node-v11.0.0-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v11.0.0/node-v11.0.0-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v11.0.0/node-v11.0.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v11.0.0/node-v11.0.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v11.0.0/node-v11.0.0.tar.gz \
Other release files: https://nodejs.org/dist/v11.0.0/ \
Documentation: https://nodejs.org/docs/v11.0.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

e4599aa883f4402e6e140bfc01b13c1c46900927b568f6a855d9812bd07fbff7  node-v11.0.0-aix-ppc64.tar.gz
f70e12d246ba35e88c5c08a195215c5c913ce66c9d95d0bd21cc1d3e69904279  node-v11.0.0-darwin-x64.tar.gz
481d88451571e97bf852365a4b9ebb958d7388b9dd93c4b3ce5289028de37f61  node-v11.0.0-darwin-x64.tar.xz
87aaa30a723346d640a2c5f99e111494515d850ee87c37e08fa2a98f51c7cb91  node-v11.0.0-headers.tar.gz
4ddd927470df6d673ff8ec3b7ad0c8a7d3dbaafeb522f402de52596640eecf7b  node-v11.0.0-headers.tar.xz
e1b32dd0b41360dba18cc9919f359fd4df644cf8fc2a7687afe4454182fb4129  node-v11.0.0-linux-arm64.tar.gz
0d0c7013fd00a8e713c36aa96bcc268eeadbad2ce48d87e19849d66f4c618cad  node-v11.0.0-linux-arm64.tar.xz
5f1ec0f8e0277292171b3a0eebadfbe8a703a6072e62a8d58d75df4d37438996  node-v11.0.0-linux-armv6l.tar.gz
3ac15138258f73b049a6ac02b4a3b9943a27131c96b23198cbcbf862c6b9d80f  node-v11.0.0-linux-armv6l.tar.xz
1d4d9f51d29070eb0033875cc6095f7432fe18fc5efa28abf2051af1b59c2592  node-v11.0.0-linux-armv7l.tar.gz
670b31a5525d669a134494ed3b39bacbbc3c038a9034b3c06a0c697378235b08  node-v11.0.0-linux-armv7l.tar.xz
f1bd19c7e77cc94c15a797b2a7bb16b07aa0cbbbf83f33d5cc38afd72ba1b90e  node-v11.0.0-linux-ppc64le.tar.gz
6d6c0decf8ac04926e379397f7383abfec966599ea1b2d3f8b2086621e766231  node-v11.0.0-linux-ppc64le.tar.xz
f15cd80af908cbc7f92db97deb8682b347ac179d51a436845832ea101396c807  node-v11.0.0-linux-s390x.tar.gz
ce2c05931bdaf3ed14c6a24ea2bc7e881d205af433e6bf2b3297b5c5e6877589  node-v11.0.0-linux-s390x.tar.xz
e571de4e9b42fc411314d51ff730de37e3cef4d1d31cf4b84854e7a34a454cc7  node-v11.0.0-linux-x64.tar.gz
418b1a73bcafbf5dd3bc787f0440c2b4123dfecc2b239f5651f0256cb5ab606d  node-v11.0.0-linux-x64.tar.xz
44b57ab4b4d64168129c53c5053b01e75177bc53699853a9907ee22020c510af  node-v11.0.0.pkg
b5fd7016c96fec44081543472366d5bc0925ffa41824f60a3a09f7c979f2a7cb  node-v11.0.0-sunos-x64.tar.gz
23d8b5d10f6bb3f82f16b05de25462ae51d7956ad860e37333a203809f00f7ef  node-v11.0.0-sunos-x64.tar.xz
1f7e67f8d713e6a0c3b786d3b3d2eb03b7825cfbed395a5a9565e3c606caea3d  node-v11.0.0.tar.gz
235ab494fb76cdfe302fce68c4038a14ae82b1679cd5d40ae8049ba3a9c8bdd4  node-v11.0.0.tar.xz
6794f6582772c0bda5e4b56a834fc276440eca0032501b48089b116988371c6c  node-v11.0.0-win-x64.7z
4dd3ef7f5c8181be39cd45476b22ed34b6da2bf77377b699384d426702f8c969  node-v11.0.0-win-x64.zip
6303ab6cbe2be250d454075838db3747055cd153730430c96beb6bebd0ab9dab  node-v11.0.0-win-x86.7z
574c709cd8e8ba1fc464c4bd00d69ee2c4c670a094d4b435586a90ce8936e342  node-v11.0.0-win-x86.zip
a4be82fad7610131a68507aad93db4bb5809025af499b667e8201c93ee85aea4  node-v11.0.0-x64.msi
1b02f4f4643ffdf32ffc10035144a8eeb5ddf3e4f9753faaa1dabf9ecbfe48e6  node-v11.0.0-x86.msi
92b7d75dc5e81511c18a3227c445e98d1c3efd596604cc097bf88887e21f91d7  win-x64/node.exe
cd4dcb822384a1bfd356e30031fab1cf414f8b3a20d4c31cb30ceac1531995cc  win-x64/node.lib
b54657052cf5e9acb15ee99f0b0a08297aa2fbbea8af910e4e362ff15503106c  win-x64/node_pdb.7z
4c463d320b74ea5dc01937542670137b1457b27a235653691ab77c2c6b91950d  win-x64/node_pdb.zip
379ae612a5dbc2a533248a6f4b190d134fd0177e844c620c0b5c283786e8dde9  win-x86/node.exe
a5fe4873606038a13f89697912812b8457f6904faa047ac3d0d32df7f0742170  win-x86/node.lib
b1183e41e4a7feba740b4eab3945587a3bde989153a2d6302b624e10e2a4fdea  win-x86/node_pdb.7z
93f0f65db21f5e2a5e6fa740894c7f71f0c82fdb90106a3ae0bbff32c61e5bfa  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQEcBAEBCAAGBQJbz2M8AAoJEHNBsVwHCHesdBsH/jpuKmw72OFvzK/+RIcqCMFc
Omhgw8E1Kgs5d4c7+zy/pP9Inx787ogohe9VFov/1JI5/Pv++yKPUMZrj/hrLfW2
gUSPnBviDvV7bCXbFN+JFCgjQwE1oPgs9ptuk8DqJF6JKQDNB+qqjSJR1jRbXoBZ
yr42Pnx1daVZuL8miPBA6l8gqrXD9ky1KlCs/eUN9VAOxzrHO0XNuRvJy9QRpNAg
VSIGyOtH4gthv1b2/l/62QO2gd6K/rwxGm9DHGqe2Zem7sHF0O68KeHWiRukMubT
hdr89WeFYp5gEyVOMni+vdUqfIQ65nFPjIZpo+3pPlmWrhbmCQZjsNDnHNTOe+0=
=fq7t
-----END PGP SIGNATURE-----

```
