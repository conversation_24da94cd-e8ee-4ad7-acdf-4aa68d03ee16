---
date: '2019-01-25T01:53:48.141Z'
category: release
title: Node v11.8.0 (Current)
layout: blog-post
author: <PERSON><PERSON>
---

### Notable Changes

- **events**:
  - For unhandled `error` events with an argument that is not an `Error` object, the resulting exeption will have more information about the argument. [nodejs/node#25621](https://github.com/nodejs/node/pull/25621)
- **child_process**:
  - When the `max<PERSON>uffer` option is passed, `stdout` and `stderr` will be truncated rather than unavailable in case of an error. [nodejs/node#24951](https://github.com/nodejs/node/pull/24951)
- **policy**:
  - Experimental support for module integrity checks through a manifest file is implemented now. [nodejs/node#23834](https://github.com/nodejs/node/pull/23834)
- **n-api**:
  - The `napi_threadsafe_function` feature is now stable. [nodejs/node#25556](https://github.com/nodejs/node/pull/25556)
- **report**:
  - An experimental diagnostic API for capturing process state is available as `process.report` and through command line flags. [nodejs/node#22712](https://github.com/nodejs/node/pull/22712)
- **tls**:
  - `tls.connect()` takes a `timeout` option analogous to the `net.connect()` one. [nodejs/node#25517](https://github.com/nodejs/node/pull/25517)
- **worker**:
  - `process.umask()` is available as a read-only function inside Worker threads now [nodejs/node#25526](https://github.com/nodejs/node/pull/25526)
  - An `execArgv` option that supports a subset of Node.js command line options is supported now. [nodejs/node#25467](https://github.com/nodejs/node/pull/25467)

### Commits

- [[`5fab92c88a`](https://github.com/nodejs/node/commit/5fab92c88a)] - **build**: remove AIX/ppc (32bit) dead code (Refael Ackermann) [#25523](https://github.com/nodejs/node/pull/25523)
- [[`34da9a3089`](https://github.com/nodejs/node/commit/34da9a3089)] - **build**: make install.py python 3 compatiable (Sakthipriyan Vairamani (thefourtheye)) [#25583](https://github.com/nodejs/node/pull/25583)
- [[`8cc936a8ea`](https://github.com/nodejs/node/commit/8cc936a8ea)] - **build**: remove erroneous duplicate declaration from node_inspector.gypi (Refael Ackermann) [#25586](https://github.com/nodejs/node/pull/25586)
- [[`28894af902`](https://github.com/nodejs/node/commit/28894af902)] - **build**: do not lint python scripts under test/fixtures (Joyee Cheung) [#25639](https://github.com/nodejs/node/pull/25639)
- [[`47d040dd77`](https://github.com/nodejs/node/commit/47d040dd77)] - **build**: introduce --openssl-is-fips flag (Daniel Bevenius) [#25412](https://github.com/nodejs/node/pull/25412)
- [[`ac5fa2c7f6`](https://github.com/nodejs/node/commit/ac5fa2c7f6)] - **child_process**: truncate output when maxBuffer is exceeded (Jeremiah Senkpiel) [#24951](https://github.com/nodejs/node/pull/24951)
- [[`3c661f0aa6`](https://github.com/nodejs/node/commit/3c661f0aa6)] - **console**: refactor inspector console extension installation (Joyee Cheung) [#25450](https://github.com/nodejs/node/pull/25450)
- [[`f415069c65`](https://github.com/nodejs/node/commit/f415069c65)] - **crypto**: add crypto modules to cannotUseCache (Daniel Bevenius) [#25606](https://github.com/nodejs/node/pull/25606)
- [[`bb7f71ad8a`](https://github.com/nodejs/node/commit/bb7f71ad8a)] - **crypto**: fix key handle extraction (Tobias Nießen) [#25562](https://github.com/nodejs/node/pull/25562)
- [[`c0859d7176`](https://github.com/nodejs/node/commit/c0859d7176)] - **deps**: upgrade to libuv 1.25.0 (cjihrig) [#25571](https://github.com/nodejs/node/pull/25571)
- [[`9d8a225c6c`](https://github.com/nodejs/node/commit/9d8a225c6c)] - **doc**: add note regarding pushing release tags (Myles Borins) [#25569](https://github.com/nodejs/node/pull/25569)
- [[`5440f9d4bc`](https://github.com/nodejs/node/commit/5440f9d4bc)] - **doc**: use correct placeholder for policy docs (Anna Henningsen) [#25627](https://github.com/nodejs/node/pull/25627)
- [[`4f38106ef5`](https://github.com/nodejs/node/commit/4f38106ef5)] - **(SEMVER-MINOR)** **doc**: add node-report documentation (Vipin Menon) [#22712](https://github.com/nodejs/node/pull/22712)
- [[`eac438acc8`](https://github.com/nodejs/node/commit/eac438acc8)] - **doc**: running coverage for individual suites (Benjamin Coe) [#25622](https://github.com/nodejs/node/pull/25622)
- [[`65478faa7b`](https://github.com/nodejs/node/commit/65478faa7b)] - **doc**: hyperlink reference to process.nextTick (Sam Roberts) [#25615](https://github.com/nodejs/node/pull/25615)
- [[`c5d89e6333`](https://github.com/nodejs/node/commit/c5d89e6333)] - **doc**: reword stream docs to clarify that decodeStrings encodes strings (Daniel George Holz) [#25468](https://github.com/nodejs/node/pull/25468)
- [[`0c046e8e68`](https://github.com/nodejs/node/commit/0c046e8e68)] - **doc**: correct my wrong note about buf.fill() (Vse Mozhet Byt) [#25585](https://github.com/nodejs/node/pull/25585)
- [[`10bff7a58c`](https://github.com/nodejs/node/commit/10bff7a58c)] - **doc**: add a note to `buf.fill()` description (Vse Mozhet Byt) [#25547](https://github.com/nodejs/node/pull/25547)
- [[`688fb8d619`](https://github.com/nodejs/node/commit/688fb8d619)] - **doc**: fix typo in Buffer API (H1Gdev) [#25544](https://github.com/nodejs/node/pull/25544)
- [[`417023046e`](https://github.com/nodejs/node/commit/417023046e)] - **doc**: add Rich back to TSC list (Michael Dawson) [#25535](https://github.com/nodejs/node/pull/25535)
- [[`26c5bd8a5c`](https://github.com/nodejs/node/commit/26c5bd8a5c)] - **doc**: add metadata about ecdh curve options (Sam Roberts) [#25502](https://github.com/nodejs/node/pull/25502)
- [[`593714e4bd`](https://github.com/nodejs/node/commit/593714e4bd)] - **events**: show inspected error in uncaught 'error' message (Anna Henningsen) [#25621](https://github.com/nodejs/node/pull/25621)
- [[`d6b50c66cc`](https://github.com/nodejs/node/commit/d6b50c66cc)] - **http**: make ClientRequest#setTimeout() noop at end (Tim De Pauw) [#25536](https://github.com/nodejs/node/pull/25536)
- [[`e55c5c341d`](https://github.com/nodejs/node/commit/e55c5c341d)] - **http**: reuse noop function in socketOnError() (cjihrig) [#25566](https://github.com/nodejs/node/pull/25566)
- [[`9a410a189e`](https://github.com/nodejs/node/commit/9a410a189e)] - **http2**: allow fully synchronous `_final()` (Anna Henningsen) [#25609](https://github.com/nodejs/node/pull/25609)
- [[`f688e73984`](https://github.com/nodejs/node/commit/f688e73984)] - **n-api**: change #ifdef to #if in node_api_types (Daniel Bevenius) [#25635](https://github.com/nodejs/node/pull/25635)
- [[`2b1858298a`](https://github.com/nodejs/node/commit/2b1858298a)] - **(SEMVER-MINOR)** **n-api**: mark thread-safe function as stable (Gabriel Schulhof) [#25556](https://github.com/nodejs/node/pull/25556)
- [[`0ebe6ebbb1`](https://github.com/nodejs/node/commit/0ebe6ebbb1)] - **os**: implement os.release() using uv_os_uname() (cjihrig) [#25600](https://github.com/nodejs/node/pull/25600)
- [[`da8c526888`](https://github.com/nodejs/node/commit/da8c526888)] - **(SEMVER-MINOR)** **policy**: manifest with subresource integrity checks (Bradley Farias) [#23834](https://github.com/nodejs/node/pull/23834)
- [[`647a37f5d8`](https://github.com/nodejs/node/commit/647a37f5d8)] - **process**: clarify the pre- and post-condition of esm setup (Joyee Cheung) [#25530](https://github.com/nodejs/node/pull/25530)
- [[`b2834ce65b`](https://github.com/nodejs/node/commit/b2834ce65b)] - **process**: fix call process.reallyExit, vs., binding (Benjamin Coe) [#25655](https://github.com/nodejs/node/pull/25655)
- [[`92dd8998e7`](https://github.com/nodejs/node/commit/92dd8998e7)] - **process**: check env-\>EmitProcessEnvWarning() last (Benjamin) [#25575](https://github.com/nodejs/node/pull/25575)
- [[`07f1bb001c`](https://github.com/nodejs/node/commit/07f1bb001c)] - **process**: allow reading umask in workers (cjihrig) [#25526](https://github.com/nodejs/node/pull/25526)
- [[`f3d0591abf`](https://github.com/nodejs/node/commit/f3d0591abf)] - **report**: use `uv_handle_type_name()` to get handle type (Anna Henningsen) [#25610](https://github.com/nodejs/node/pull/25610)
- [[`03ba34401b`](https://github.com/nodejs/node/commit/03ba34401b)] - **report**: downgrade reinterpret_cast to static_cast (Anna Henningsen) [#25610](https://github.com/nodejs/node/pull/25610)
- [[`07a0dc89ad`](https://github.com/nodejs/node/commit/07a0dc89ad)] - **report**: roll extra loop iteration in `PrintNativeStack()` (Anna Henningsen) [#25610](https://github.com/nodejs/node/pull/25610)
- [[`64959b6668`](https://github.com/nodejs/node/commit/64959b6668)] - **report**: remove `internalBinding('config').hasReport` (Anna Henningsen) [#25610](https://github.com/nodejs/node/pull/25610)
- [[`4031b5c267`](https://github.com/nodejs/node/commit/4031b5c267)] - **report**: remove `InitializeReport()` (Anna Henningsen) [#25598](https://github.com/nodejs/node/pull/25598)
- [[`0f91e0355a`](https://github.com/nodejs/node/commit/0f91e0355a)] - **report**: simplify rlimit to JSON logic (cjihrig) [#25597](https://github.com/nodejs/node/pull/25597)
- [[`a02b621312`](https://github.com/nodejs/node/commit/a02b621312)] - **report**: simplify option checking (cjihrig) [#25597](https://github.com/nodejs/node/pull/25597)
- [[`c598d98970`](https://github.com/nodejs/node/commit/c598d98970)] - **report**: use uv_pid_t instead of custom PID_TYPE (cjihrig) [#25597](https://github.com/nodejs/node/pull/25597)
- [[`213eddd323`](https://github.com/nodejs/node/commit/213eddd323)] - **report**: remove unnecessary includes (cjihrig) [#25597](https://github.com/nodejs/node/pull/25597)
- [[`42bbe58c47`](https://github.com/nodejs/node/commit/42bbe58c47)] - **report**: remove unnecessary intermediate variable (cjihrig) [#25597](https://github.com/nodejs/node/pull/25597)
- [[`a161a9b9c3`](https://github.com/nodejs/node/commit/a161a9b9c3)] - **src**: remove unnecessary `filename` variable (Anna Henningsen) [#25610](https://github.com/nodejs/node/pull/25610)
- [[`c59edcadc1`](https://github.com/nodejs/node/commit/c59edcadc1)] - **src**: remove using v8::Function in node_os.cc (cjihrig) [#25640](https://github.com/nodejs/node/pull/25640)
- [[`dbecc82524`](https://github.com/nodejs/node/commit/dbecc82524)] - **src**: remove outdated `Neuter()` call in `node_buffer.cc` (Anna Henningsen) [#25479](https://github.com/nodejs/node/pull/25479)
- [[`8f42c9efe9`](https://github.com/nodejs/node/commit/8f42c9efe9)] - **src**: silence compiler warning in node_report.cc (Daniel Bevenius) [#25557](https://github.com/nodejs/node/pull/25557)
- [[`549216a138`](https://github.com/nodejs/node/commit/549216a138)] - **(SEMVER-MINOR)** **src**: merge into core (Gireesh Punathil) [#22712](https://github.com/nodejs/node/pull/22712)
- [[`55768c0079`](https://github.com/nodejs/node/commit/55768c0079)] - **src**: restrict unloading addons to Worker threads (Anna Henningsen) [#25577](https://github.com/nodejs/node/pull/25577)
- [[`d9a8113a5b`](https://github.com/nodejs/node/commit/d9a8113a5b)] - **src**: pass along errors from `--security-reverts` (Anna Henningsen) [#25466](https://github.com/nodejs/node/pull/25466)
- [[`291cedf25d`](https://github.com/nodejs/node/commit/291cedf25d)] - **src**: reduce includes of node_internals.h (Joyee Cheung) [#25507](https://github.com/nodejs/node/pull/25507)
- [[`03e05cb4fb`](https://github.com/nodejs/node/commit/03e05cb4fb)] - **src**: fix FIPS section in Sign::SignFinal (Daniel Bevenius) [#25412](https://github.com/nodejs/node/pull/25412)
- [[`0897504adc`](https://github.com/nodejs/node/commit/0897504adc)] - **src**: call `Environment::Exit()` for fatal exceptions (Anna Henningsen) [#25472](https://github.com/nodejs/node/pull/25472)
- [[`7ffa8ec756`](https://github.com/nodejs/node/commit/7ffa8ec756)] - **src**: reset `StopTracingAgent()` before platform teardown (Anna Henningsen) [#25472](https://github.com/nodejs/node/pull/25472)
- [[`a9ffce908d`](https://github.com/nodejs/node/commit/a9ffce908d)] - **test**: fix pummel/test-exec (Rich Trott) [#25677](https://github.com/nodejs/node/pull/25677)
- [[`08ade9b0d3`](https://github.com/nodejs/node/commit/08ade9b0d3)] - **test**: clarify the path relativeness of WPT runner classes (Joyee Cheung) [#25616](https://github.com/nodejs/node/pull/25616)
- [[`74ee8d3b72`](https://github.com/nodejs/node/commit/74ee8d3b72)] - **test**: run html/webappapis/microtask-queuing WPT (Joyee Cheung) [#25616](https://github.com/nodejs/node/pull/25616)
- [[`572a70feae`](https://github.com/nodejs/node/commit/572a70feae)] - **test**: pull html/webappapis/microtask-queuing WPT (Joyee Cheung) [#25616](https://github.com/nodejs/node/pull/25616)
- [[`90a64ab280`](https://github.com/nodejs/node/commit/90a64ab280)] - **test**: add stdio checks to cp-exec-maxBuffer (Jeremiah Senkpiel) [#24951](https://github.com/nodejs/node/pull/24951)
- [[`0800f91dcc`](https://github.com/nodejs/node/commit/0800f91dcc)] - **(SEMVER-MINOR)** **test**: add node-report tests (LakshmiSwethaG) [#22712](https://github.com/nodejs/node/pull/22712)
- [[`7490fc880e`](https://github.com/nodejs/node/commit/7490fc880e)] - **test**: switch to native v8 coverage (Benjamin Coe) [#25157](https://github.com/nodejs/node/pull/25157)
- [[`ecd358b1fd`](https://github.com/nodejs/node/commit/ecd358b1fd)] - **test**: revoke flaky designation for tests (Gireesh Punathil) [#25611](https://github.com/nodejs/node/pull/25611)
- [[`5a0332ed31`](https://github.com/nodejs/node/commit/5a0332ed31)] - **test**: remove potential race condition in https renegotiation test (Rich Trott) [#25601](https://github.com/nodejs/node/pull/25601)
- [[`6881454d92`](https://github.com/nodejs/node/commit/6881454d92)] - **test**: replace common.PORT with `0` in https renegotiation test (Rich Trott) [#25599](https://github.com/nodejs/node/pull/25599)
- [[`5684da5360`](https://github.com/nodejs/node/commit/5684da5360)] - **test**: changed function to arrow function (yathamravali) [#25441](https://github.com/nodejs/node/pull/25441)
- [[`efe089e01a`](https://github.com/nodejs/node/commit/efe089e01a)] - **test**: use stronger curves for keygen (Daniel Bevenius) [#25564](https://github.com/nodejs/node/pull/25564)
- [[`3dcdf27399`](https://github.com/nodejs/node/commit/3dcdf27399)] - **test**: change ciphers from RC4 to no-such-cipher (Daniel Bevenius) [#25534](https://github.com/nodejs/node/pull/25534)
- [[`faa1776048`](https://github.com/nodejs/node/commit/faa1776048)] - **test**: relax chunk count expectations (Gireesh Punathil) [#25415](https://github.com/nodejs/node/pull/25415)
- [[`b8d780c0ee`](https://github.com/nodejs/node/commit/b8d780c0ee)] - **test**: ensure npm version is not release candidate (Myles Borins) [#25538](https://github.com/nodejs/node/pull/25538)
- [[`2112b707e6`](https://github.com/nodejs/node/commit/2112b707e6)] - **test**: improve code coverage for i18n (Michael Dawson) [#25428](https://github.com/nodejs/node/pull/25428)
- [[`4e52b07fb7`](https://github.com/nodejs/node/commit/4e52b07fb7)] - **test**: use fipsMode instead of common.hasFipsCrypto (Daniel Bevenius) [#25510](https://github.com/nodejs/node/pull/25510)
- [[`4c207d9b84`](https://github.com/nodejs/node/commit/4c207d9b84)] - **test**: do not use uninitialized memory in common flags check (Anna Henningsen) [#25475](https://github.com/nodejs/node/pull/25475)
- [[`cfcb759e5d`](https://github.com/nodejs/node/commit/cfcb759e5d)] - **test**: prepare test-hash-seed for CI (Rich Trott) [#25522](https://github.com/nodejs/node/pull/25522)
- [[`35240cab05`](https://github.com/nodejs/node/commit/35240cab05)] - **test**: refactor min() in test-hash-seed (Rich Trott) [#25522](https://github.com/nodejs/node/pull/25522)
- [[`779ce29f39`](https://github.com/nodejs/node/commit/779ce29f39)] - **test**: add check for wrk to test-keep-alive (Rich Trott) [#25516](https://github.com/nodejs/node/pull/25516)
- [[`ab861433c9`](https://github.com/nodejs/node/commit/ab861433c9)] - **test**: fix test-repl timeout and tmpdir refresh (Brian White) [#25425](https://github.com/nodejs/node/pull/25425)
- [[`6347940e9f`](https://github.com/nodejs/node/commit/6347940e9f)] - **test**: refactor pummel/test-net-pingpong (Rich Trott) [#25485](https://github.com/nodejs/node/pull/25485)
- [[`307da2d3e7`](https://github.com/nodejs/node/commit/307da2d3e7)] - **test**: refactor pummel/test-net-many-clients (Rich Trott) [#25485](https://github.com/nodejs/node/pull/25485)
- [[`69c0841a5a`](https://github.com/nodejs/node/commit/69c0841a5a)] - **test**: refactor pummel/test-net-connect-econnrefused (Rich Trott) [#25485](https://github.com/nodejs/node/pull/25485)
- [[`817b44db54`](https://github.com/nodejs/node/commit/817b44db54)] - **test**: refactor pummel/test-keep-alive (Rich Trott) [#25485](https://github.com/nodejs/node/pull/25485)
- [[`aa9a86aa32`](https://github.com/nodejs/node/commit/aa9a86aa32)] - **test,worker**: verify that `.terminate()` breaks microtask queue (Anna Henningsen) [#25480](https://github.com/nodejs/node/pull/25480)
- [[`c3409f57fd`](https://github.com/nodejs/node/commit/c3409f57fd)] - **tls**: do not free cert in `.getCertificate()` (Anna Henningsen) [#25490](https://github.com/nodejs/node/pull/25490)
- [[`58952a1a96`](https://github.com/nodejs/node/commit/58952a1a96)] - **(SEMVER-MINOR)** **tls**: make tls.connect() accept a timeout option (Luigi Pinca) [#25517](https://github.com/nodejs/node/pull/25517)
- [[`1cbadd8d1c`](https://github.com/nodejs/node/commit/1cbadd8d1c)] - **tools**: improve valgrind support (Anna Henningsen) [#25498](https://github.com/nodejs/node/pull/25498)
- [[`d9da4af245`](https://github.com/nodejs/node/commit/d9da4af245)] - **tools**: update ESLint to 5.12.1 (cjihrig) [#25573](https://github.com/nodejs/node/pull/25573)
- [[`338f456107`](https://github.com/nodejs/node/commit/338f456107)] - **util**: fix iterable types with special prototype (Ruben Bridgewater) [#25457](https://github.com/nodejs/node/pull/25457)
- [[`219b1b8ce1`](https://github.com/nodejs/node/commit/219b1b8ce1)] - **(SEMVER-MINOR)** **worker**: enable passing command line flags (Yael Hermon) [#25467](https://github.com/nodejs/node/pull/25467)

Windows 32-bit Installer: https://nodejs.org/dist/v11.8.0/node-v11.8.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v11.8.0/node-v11.8.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v11.8.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v11.8.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v11.8.0/node-v11.8.0.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v11.8.0/node-v11.8.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v11.8.0/node-v11.8.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v11.8.0/node-v11.8.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v11.8.0/node-v11.8.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v11.8.0/node-v11.8.0-aix-ppc64.tar.gz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v11.8.0/node-v11.8.0-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v11.8.0/node-v11.8.0-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v11.8.0/node-v11.8.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v11.8.0/node-v11.8.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v11.8.0/node-v11.8.0.tar.gz \
Other release files: https://nodejs.org/dist/v11.8.0/ \
Documentation: https://nodejs.org/docs/v11.8.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

6150a5f8ae0184a7b69deaa8c18eb3013cd02738d829b4cd8a476f0b184f52b9  node-v11.8.0-aix-ppc64.tar.gz
fbb26b38f408c2f2324a5821062c16103f54de16d24f9f24c4e270a3a41f4832  node-v11.8.0-darwin-x64.tar.gz
cc24b25c423539470dfe7901487dce0e636538df4729cfc0e1c25d5df4bda544  node-v11.8.0-darwin-x64.tar.xz
947fdf5febf277a0b7394f0c4afebec9ad0b8002a22aefcfc219a80876fc13a3  node-v11.8.0-headers.tar.gz
d4cdad040d4aae9221aef22f5c5350d6abc43782a41da412cfa86ac639f36197  node-v11.8.0-headers.tar.xz
ff3f49fc6da1cd1e9792862dab0774fc83578201fcd414f90af09f4cb5ac3c38  node-v11.8.0-linux-arm64.tar.gz
42b190c686cb8bafbdbf418a6c20c6785a23ba0f1a1a85af44595057a3d5e25e  node-v11.8.0-linux-arm64.tar.xz
a239790ab901579cf20f4bad2a2810e9ce044ee1132f89403def5e50b155151b  node-v11.8.0-linux-armv6l.tar.gz
61eb8836031c654a9f073b70a02dc8ecc44997b4b427060721dc1e7452027076  node-v11.8.0-linux-armv6l.tar.xz
e35fc951fb30a174aa45a5a337482f76100b7d21b1266f7d9dcda5c9f12cc730  node-v11.8.0-linux-armv7l.tar.gz
6c6164949d0d3255f1d7c38820a160e844608479e5999686dc8122f721411778  node-v11.8.0-linux-armv7l.tar.xz
13abc3d8713d124b933a62123db536d4cc6a93a393cbbb3fe65443408bc63272  node-v11.8.0-linux-ppc64le.tar.gz
2696375b73277401781f2fb0990bad16134671ececd494156311ef0e4a15cdf5  node-v11.8.0-linux-ppc64le.tar.xz
a5f7ffeae0cd7de2710dc7a167b6bd14b75f7fbb0807acd7f6aa543d11794a48  node-v11.8.0-linux-s390x.tar.gz
fc37d058851b240e26b926d0b76c17f24ef3bd9624ae84b60c2719064b9753c2  node-v11.8.0-linux-s390x.tar.xz
5787b70b35eb5c819be4475d3aedd332d68d01dc12651374a209961b7202a6bc  node-v11.8.0-linux-x64.tar.gz
85ca19c495d5cac6acf6ee4a3c8dfb4489fb67fefc184c61eb4513eb5ef99a88  node-v11.8.0-linux-x64.tar.xz
35ad40d438eb1f984ba7d8e711fa5f9c9d8d9f5226ade183811ea09aa291d955  node-v11.8.0.pkg
e9fa0bc817d2a16f2debd75fd4c7aa674dd96c7e3a113faa0e8866dbba249168  node-v11.8.0-sunos-x64.tar.gz
39c2c0f5f5f47229be7e617f6f5e2c88280b2b4dbef572a1985e7303cd155426  node-v11.8.0-sunos-x64.tar.xz
958d1bbd08fb8d2ae6aad1c4e644fef61e502409fe6c7d0e742013ce0a2d2f26  node-v11.8.0.tar.gz
7041550b94211d65ef0bee76c02bd5ddfdd9122c6d4b289323194edbfbe447a0  node-v11.8.0.tar.xz
b3f17bb5bd6c4364028c0b4d0591b2b3cb1b227a51c2cbfbaaa436cf96af4175  node-v11.8.0-win-x64.7z
cd4db1b9e7ac29ed81d433f2de85582902670a121961aa3f350533d5b2fd44c6  node-v11.8.0-win-x64.zip
ad7741fd315187ab814cea4fd001b691fb3aa11c3068b53c8f62a99c592db3fa  node-v11.8.0-win-x86.7z
b046886fe5315cf3c7b04805862bc4cdf6e572455921eb566dcfa280241daaa0  node-v11.8.0-win-x86.zip
2b9156efcbcf73deb5c87ce86931c75800abe7114bb241403eed62e21f42a099  node-v11.8.0-x64.msi
6ebf2958cd2ef3b33c98e11a8b7f91d6fa47f27200bde4743c00e237cb058341  node-v11.8.0-x86.msi
211258b065f80a5e3ebfe61ada93832678d9cec53c6cc19fce15cccea8bc24fd  win-x64/node.exe
193478e8f838a80a11655da46f2d626a3a634ed8cc3a52e6df2a71b8250bbba6  win-x64/node.lib
3f8b2725c828c3b3f33e764685e1cbbb29dfd2ce8128efe8377d71c6a0e2da28  win-x64/node_pdb.7z
67ea4e3d611b63a2d21e4be5926767f1819d17e22b7cfc0fc0522c3d9537f708  win-x64/node_pdb.zip
3dd73bf1bdaae48fe48fd665e94adb66d2de81eac7b9795c37d4907061a50110  win-x86/node.exe
34a2dc7db777b8fb8abe15db4d9a923d17be19e9170831d97e76758ef6059aa4  win-x86/node.lib
dc3939b539a4152fc17ca8557d41eb89841481c7af189ac7f40b11313474c729  win-x86/node_pdb.7z
c997fa8093de08b7390684c98157f4ecff733f982ed2050057a707e80838dea5  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQEzBAEBCAAdFiEEDv/hvO/ZyE49CYFSkzsB9AtcqUYFAlxKa98ACgkQkzsB9Atc
qUZf6wf/cg667aQJUUFplEFcjV59ZQyeTwkIw9qqAKZvtThIoTl+fpz6YDflr+q2
vHq7141rehuALUJFv/GLjwtewAsnoN75vrYBhQ7qFEYQxdESKJv1clv21vzHkoMH
KWr8/ChQGXqT3eAkJZSl66vyClVNfNKC3b5L8yMud/3Ik89ksGCthvJ7c7oE9eA7
vGnnAGmrsmGk4/R9OQahNSmNxavD3bhQ5f0g4hKQ+QFuglPpd0tYfoTmM4WrehxB
SeybVFK4vkMB0XEjV1511Y6ulVPaBtwecDtCu+sbCSwK7EYedChJtfwNXC24zdMm
TKTx95b5GK83BPm3EgDmkKvQDqAQNw==
=M+q3
-----END PGP SIGNATURE-----

```
