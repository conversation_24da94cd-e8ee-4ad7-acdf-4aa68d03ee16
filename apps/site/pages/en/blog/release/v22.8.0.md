---
date: '2024-09-03T13:51:48.461Z'
category: release
title: Node v22.8.0 (Current)
layout: blog-post
author: <PERSON>
---

## 2024-09-03, Version 22.8.0 (Current), @RafaelGSS

### New JS API for compile cache

This release adds a new API `module.enableCompileCache()` that can be used to enable on-disk code caching of all modules loaded after this API is called.
Previously this could only be enabled by the `NODE_COMPILE_CACHE` environment variable, so it could only set by end-users.
This API allows tooling and library authors to enable caching of their own code.
This is a built-in alternative to the [v8-compile-cache](https://www.npmjs.com/package/v8-compile-cache)/[v8-compile-cache-lib ](https://www.npmjs.com/package/v8-compile-cache-lib) packages,
but have [better performance](https://github.com/nodejs/node/issues/47472#issuecomment-1970331362) and supports ESM.

Thanks to <PERSON><PERSON> for working on this.

### New option for vm.createContext() to create a context with a freezable globalThis

Node.js implements a flavor of `vm.createContext()` and friends that creates a context without contextifying its global
object when vm.constants.DONT_CONTEXTIFY is used. This is suitable when users want to freeze the context
(impossible when the global is contextified i.e. has interceptors installed) or speed up the global access if they
don't need the interceptor behavior.

Thanks to Joyee Cheung for working on this.

### Support for coverage thresholds

Node.js now supports requiring code coverage to meet a specific threshold before the process exits successfully.
To use this feature, you need to enable the `--experimental-test-coverage` flag.

You can set thresholds for the following types of coverage:

- **Branch coverage**: Use `--test-coverage-branches=<threshold>`
- **Function coverage**: Use `--test-coverage-functions=<threshold>`
- **Line coverage**: Use `--test-coverage-lines=<threshold>`

`<threshold>` should be an integer between 0 and 100. If an invalid value is provided, a `TypeError` will be thrown.

If the code coverage fails to meet the specified thresholds for any category, the process will exit with code `1`.

For instance, to enforce a minimum of 80% line coverage and 60% branch coverage, you can run:

```console
$ node --experimental-test-coverage --test-coverage-lines=80 --test-coverage-branches=60 example.js
```

Thanks Aviv Keller for working on this.

### Other Notable Changes

- \[[`1f2cc2fa47`](https://github.com/nodejs/node/commit/1f2cc2fa47)] - **(SEMVER-MINOR)** **src,lib**: add performance.uvMetricsInfo (Rafael Gonzaga) [#54413](https://github.com/nodejs/node/pull/54413)
- \[[`1e01bdc0d0`](https://github.com/nodejs/node/commit/1e01bdc0d0)] - **(SEMVER-MINOR)** **net**: exclude ipv6 loopback addresses from server.listen (Giovanni Bucci) [#54264](https://github.com/nodejs/node/pull/54264)
- \[[`97fa075c2e`](https://github.com/nodejs/node/commit/97fa075c2e)] - **(SEMVER-MINOR)** **test_runner**: support running tests in process (Colin Ihrig) [#53927](https://github.com/nodejs/node/pull/53927)
- \[[`858b583c88`](https://github.com/nodejs/node/commit/858b583c88)] - **(SEMVER-MINOR)** **test_runner**: defer inheriting hooks until run() (Colin Ihrig) [#53927](https://github.com/nodejs/node/pull/53927)

### Commits

- \[[`94985df9d6`](https://github.com/nodejs/node/commit/94985df9d6)] - **benchmark**: fix benchmark for file path and URL conversion (Early Riser) [#54190](https://github.com/nodejs/node/pull/54190)
- \[[`ac178b094b`](https://github.com/nodejs/node/commit/ac178b094b)] - **buffer**: truncate instead of throw when writing beyond buffer (Robert Nagy) [#54524](https://github.com/nodejs/node/pull/54524)
- \[[`afd8c1eb4f`](https://github.com/nodejs/node/commit/afd8c1eb4f)] - **buffer**: allow invalid encoding in from (Robert Nagy) [#54533](https://github.com/nodejs/node/pull/54533)
- \[[`6f0cf35cd3`](https://github.com/nodejs/node/commit/6f0cf35cd3)] - **build**: reclaim disk space on macOS GHA runner (jakecastelli) [#54658](https://github.com/nodejs/node/pull/54658)
- \[[`467ac3aec4`](https://github.com/nodejs/node/commit/467ac3aec4)] - **build**: don't clean obj.target directory if it doesn't exist (Joyee Cheung) [#54337](https://github.com/nodejs/node/pull/54337)
- \[[`71fdf961df`](https://github.com/nodejs/node/commit/71fdf961df)] - **build**: update required python version to 3.8 (Aviv Keller) [#54358](https://github.com/nodejs/node/pull/54358)
- \[[`73604cf1c5`](https://github.com/nodejs/node/commit/73604cf1c5)] - **deps**: update nghttp2 to 1.63.0 (Node.js GitHub Bot) [#54589](https://github.com/nodejs/node/pull/54589)
- \[[`b00c087285`](https://github.com/nodejs/node/commit/b00c087285)] - **deps**: V8: cherry-pick e74d0f437fcd (Joyee Cheung) [#54279](https://github.com/nodejs/node/pull/54279)
- \[[`33a6b3c7a9`](https://github.com/nodejs/node/commit/33a6b3c7a9)] - **deps**: backport ICU-22787 to fix ClangCL on Windows (Stefan Stojanovic) [#54502](https://github.com/nodejs/node/pull/54502)
- \[[`fe56949cbb`](https://github.com/nodejs/node/commit/fe56949cbb)] - **deps**: update c-ares to v1.33.1 (Node.js GitHub Bot) [#54549](https://github.com/nodejs/node/pull/54549)
- \[[`290f6ce619`](https://github.com/nodejs/node/commit/290f6ce619)] - **deps**: update amaro to 0.1.8 (Node.js GitHub Bot) [#54520](https://github.com/nodejs/node/pull/54520)
- \[[`b5843568b4`](https://github.com/nodejs/node/commit/b5843568b4)] - **deps**: update amaro to 0.1.7 (Node.js GitHub Bot) [#54473](https://github.com/nodejs/node/pull/54473)
- \[[`9c709209b4`](https://github.com/nodejs/node/commit/9c709209b4)] - **deps**: update undici to 6.19.8 (Node.js GitHub Bot) [#54456](https://github.com/nodejs/node/pull/54456)
- \[[`a5ce24181b`](https://github.com/nodejs/node/commit/a5ce24181b)] - **deps**: sqlite: fix Windows compilation (Colin Ihrig) [#54433](https://github.com/nodejs/node/pull/54433)
- \[[`3caf29ea88`](https://github.com/nodejs/node/commit/3caf29ea88)] - **deps**: update sqlite to 3.46.1 (Node.js GitHub Bot) [#54433](https://github.com/nodejs/node/pull/54433)
- \[[`68758d4b08`](https://github.com/nodejs/node/commit/68758d4b08)] - **doc**: add support me link for anonrig (Yagiz Nizipli) [#54611](https://github.com/nodejs/node/pull/54611)
- \[[`f5c5529266`](https://github.com/nodejs/node/commit/f5c5529266)] - **doc**: add alert on REPL from TCP socket (Rafael Gonzaga) [#54594](https://github.com/nodejs/node/pull/54594)
- \[[`bf824483cd`](https://github.com/nodejs/node/commit/bf824483cd)] - **doc**: fix typo in styleText description (Rafael Gonzaga) [#54616](https://github.com/nodejs/node/pull/54616)
- \[[`825d933fd4`](https://github.com/nodejs/node/commit/825d933fd4)] - **doc**: add getHeapStatistics() property descriptions (Benji Marinacci) [#54584](https://github.com/nodejs/node/pull/54584)
- \[[`80e5150160`](https://github.com/nodejs/node/commit/80e5150160)] - **doc**: fix module compile cache description (沈鸿飞) [#54625](https://github.com/nodejs/node/pull/54625)
- \[[`7fd033fe56`](https://github.com/nodejs/node/commit/7fd033fe56)] - **doc**: run license-builder (github-actions\[bot]) [#54562](https://github.com/nodejs/node/pull/54562)
- \[[`c499913732`](https://github.com/nodejs/node/commit/c499913732)] - **doc**: fix information about including coverage files (Aviv Keller) [#54527](https://github.com/nodejs/node/pull/54527)
- \[[`c3dc83befc`](https://github.com/nodejs/node/commit/c3dc83befc)] - **doc**: support collaborators - talk amplification (Michael Dawson) [#54508](https://github.com/nodejs/node/pull/54508)
- \[[`fc57beaad3`](https://github.com/nodejs/node/commit/fc57beaad3)] - **doc**: add note about shasum generation failure (Marco Ippolito) [#54487](https://github.com/nodejs/node/pull/54487)
- \[[`1800a58f49`](https://github.com/nodejs/node/commit/1800a58f49)] - **doc**: update websocket flag description to reflect stable API status (Yelim Koo) [#54482](https://github.com/nodejs/node/pull/54482)
- \[[`61affd77a7`](https://github.com/nodejs/node/commit/61affd77a7)] - **doc**: fix capitalization in module.md (shallow-beach) [#54488](https://github.com/nodejs/node/pull/54488)
- \[[`25419915c7`](https://github.com/nodejs/node/commit/25419915c7)] - **doc**: add esm examples to node:https (Alfredo González) [#54399](https://github.com/nodejs/node/pull/54399)
- \[[`83b5efeb54`](https://github.com/nodejs/node/commit/83b5efeb54)] - **doc**: reserve ABI 130 for Electron 33 (Calvin) [#54383](https://github.com/nodejs/node/pull/54383)
- \[[`6ccbd32ae8`](https://github.com/nodejs/node/commit/6ccbd32ae8)] - **doc, meta**: add missing `,` to `BUILDING.md` (Aviv Keller) [#54409](https://github.com/nodejs/node/pull/54409)
- \[[`fc08a9b0cd`](https://github.com/nodejs/node/commit/fc08a9b0cd)] - **fs**: refactor handleTimestampsAndMode to remove redundant call (HEESEUNG) [#54369](https://github.com/nodejs/node/pull/54369)
- \[[`4a664b5fcb`](https://github.com/nodejs/node/commit/4a664b5fcb)] - **lib**: respect terminal capabilities on styleText (Rafael Gonzaga) [#54389](https://github.com/nodejs/node/pull/54389)
- \[[`a9ce2b6a28`](https://github.com/nodejs/node/commit/a9ce2b6a28)] - **lib**: fix emit warning for debuglog.time when disabled (Vinicius Lourenço) [#54275](https://github.com/nodejs/node/pull/54275)
- \[[`b5a23c9783`](https://github.com/nodejs/node/commit/b5a23c9783)] - **meta**: remind users to use a supported version in bug reports (Aviv Keller) [#54481](https://github.com/nodejs/node/pull/54481)
- \[[`0d7171d8e9`](https://github.com/nodejs/node/commit/0d7171d8e9)] - **meta**: add more labels to dep-updaters (Aviv Keller) [#54454](https://github.com/nodejs/node/pull/54454)
- \[[`c4996c189f`](https://github.com/nodejs/node/commit/c4996c189f)] - **meta**: run coverage-windows when `vcbuild.bat` updated (Aviv Keller) [#54412](https://github.com/nodejs/node/pull/54412)
- \[[`3cf645768e`](https://github.com/nodejs/node/commit/3cf645768e)] - **module**: use amaro default transform values (Marco Ippolito) [#54517](https://github.com/nodejs/node/pull/54517)
- \[[`336496b90e`](https://github.com/nodejs/node/commit/336496b90e)] - **module**: add sourceURL magic comment hinting generated source (Chengzhong Wu) [#54402](https://github.com/nodejs/node/pull/54402)
- \[[`04f83b50ad`](https://github.com/nodejs/node/commit/04f83b50ad)] - _**Revert**_ "**net**: validate host name for server listen" (jakecastelli) [#54554](https://github.com/nodejs/node/pull/54554)
- \[[`1e01bdc0d0`](https://github.com/nodejs/node/commit/1e01bdc0d0)] - **(SEMVER-MINOR)** **net**: exclude ipv6 loopback addresses from server.listen (Giovanni Bucci) [#54264](https://github.com/nodejs/node/pull/54264)
- \[[`3cd10a3f40`](https://github.com/nodejs/node/commit/3cd10a3f40)] - **node-api**: remove RefBase and CallbackWrapper (Vladimir Morozov) [#53590](https://github.com/nodejs/node/pull/53590)
- \[[`72c554abab`](https://github.com/nodejs/node/commit/72c554abab)] - **sqlite**: return results with null prototype (Michaël Zasso) [#54350](https://github.com/nodejs/node/pull/54350)
- \[[`e071651bb2`](https://github.com/nodejs/node/commit/e071651bb2)] - **src**: disable fast methods for `buffer.write` (Michaël Zasso) [#54565](https://github.com/nodejs/node/pull/54565)
- \[[`f8cbbc685a`](https://github.com/nodejs/node/commit/f8cbbc685a)] - **src**: use v8::Isolate::GetDefaultLocale() to compute navigator.language (Joyee Cheung) [#54279](https://github.com/nodejs/node/pull/54279)
- \[[`4baf4637eb`](https://github.com/nodejs/node/commit/4baf4637eb)] - **(SEMVER-MINOR)** **src**: add JS APIs for compile cache and NODE_DISABLE_COMPILE_CACHE (Joyee Cheung) [#54501](https://github.com/nodejs/node/pull/54501)
- \[[`101e299656`](https://github.com/nodejs/node/commit/101e299656)] - **src**: move more crypto_dh.cc code to ncrypto (James M Snell) [#54459](https://github.com/nodejs/node/pull/54459)
- \[[`e6e1f4e8bd`](https://github.com/nodejs/node/commit/e6e1f4e8bd)] - **src**: remove redundant AESCipherMode (Tobias Nießen) [#54438](https://github.com/nodejs/node/pull/54438)
- \[[`1ff3f63f5e`](https://github.com/nodejs/node/commit/1ff3f63f5e)] - **src**: handle errors correctly in `permission.cc` (Michaël Zasso) [#54541](https://github.com/nodejs/node/pull/54541)
- \[[`4938188682`](https://github.com/nodejs/node/commit/4938188682)] - **src**: return `v8::Object` from error constructors (Michaël Zasso) [#54541](https://github.com/nodejs/node/pull/54541)
- \[[`4578e9485b`](https://github.com/nodejs/node/commit/4578e9485b)] - **src**: use better return types in KVStore (Michaël Zasso) [#54539](https://github.com/nodejs/node/pull/54539)
- \[[`7d9e994791`](https://github.com/nodejs/node/commit/7d9e994791)] - **src**: change SetEncodedValue to return Maybe\<void> (Tobias Nießen) [#54443](https://github.com/nodejs/node/pull/54443)
- \[[`eef303028f`](https://github.com/nodejs/node/commit/eef303028f)] - **src**: remove cached data tag from snapshot metadata (Joyee Cheung) [#54122](https://github.com/nodejs/node/pull/54122)
- \[[`3a74c400d5`](https://github.com/nodejs/node/commit/3a74c400d5)] - **src**: improve `buffer.transcode` performance (Yagiz Nizipli) [#54153](https://github.com/nodejs/node/pull/54153)
- \[[`909c5320fd`](https://github.com/nodejs/node/commit/909c5320fd)] - **src**: move more crypto code to ncrypto (James M Snell) [#54320](https://github.com/nodejs/node/pull/54320)
- \[[`9ba75faf5f`](https://github.com/nodejs/node/commit/9ba75faf5f)] - **(SEMVER-MINOR)** **src,lib**: add performance.uvMetricsInfo (Rafael Gonzaga) [#54413](https://github.com/nodejs/node/pull/54413)
- \[[`fffc300c6d`](https://github.com/nodejs/node/commit/fffc300c6d)] - **stream**: change stream to use index instead of `for...of` (Wiyeong Seo) [#54474](https://github.com/nodejs/node/pull/54474)
- \[[`a4a6ef8d29`](https://github.com/nodejs/node/commit/a4a6ef8d29)] - **test**: fix test-tls-client-auth test for OpenSSL32 (Michael Dawson) [#54610](https://github.com/nodejs/node/pull/54610)
- \[[`76345a5d7c`](https://github.com/nodejs/node/commit/76345a5d7c)] - **test**: update TLS test for OpenSSL 3.2 (Richard Lau) [#54612](https://github.com/nodejs/node/pull/54612)
- \[[`522d5a359d`](https://github.com/nodejs/node/commit/522d5a359d)] - **test**: run V8 Fast API tests in release mode too (Michaël Zasso) [#54570](https://github.com/nodejs/node/pull/54570)
- \[[`edbecf5209`](https://github.com/nodejs/node/commit/edbecf5209)] - **test**: increase key size for ca2-cert.pem (Michael Dawson) [#54599](https://github.com/nodejs/node/pull/54599)
- \[[`bc976cfc93`](https://github.com/nodejs/node/commit/bc976cfc93)] - **test**: update test-abortsignal-cloneable to use node:test (James M Snell) [#54581](https://github.com/nodejs/node/pull/54581)
- \[[`9f1ce732a8`](https://github.com/nodejs/node/commit/9f1ce732a8)] - **test**: update test-assert-typedarray-deepequal to use node:test (James M Snell) [#54585](https://github.com/nodejs/node/pull/54585)
- \[[`c74f2aeb92`](https://github.com/nodejs/node/commit/c74f2aeb92)] - **test**: update test-assert to use node:test (James M Snell) [#54585](https://github.com/nodejs/node/pull/54585)
- \[[`a0be95e4cc`](https://github.com/nodejs/node/commit/a0be95e4cc)] - **test**: merge ongc and gcutil into gc.js (tannal) [#54355](https://github.com/nodejs/node/pull/54355)
- \[[`c10aff665e`](https://github.com/nodejs/node/commit/c10aff665e)] - **test**: move a couple of tests over to using node:test (James M Snell) [#54582](https://github.com/nodejs/node/pull/54582)
- \[[`dbbc790949`](https://github.com/nodejs/node/commit/dbbc790949)] - **test**: update test-aborted-util to use node:test (James M Snell) [#54578](https://github.com/nodejs/node/pull/54578)
- \[[`64442fce6b`](https://github.com/nodejs/node/commit/64442fce6b)] - **test**: refactor test-abortcontroller to use node:test (James M Snell) [#54574](https://github.com/nodejs/node/pull/54574)
- \[[`72345dee1c`](https://github.com/nodejs/node/commit/72345dee1c)] - **test**: fix embedding test for Windows (Vladimir Morozov) [#53659](https://github.com/nodejs/node/pull/53659)
- \[[`846e2b2896`](https://github.com/nodejs/node/commit/846e2b2896)] - **test**: refactor test_runner tests to change default reporter (Colin Ihrig) [#54547](https://github.com/nodejs/node/pull/54547)
- \[[`b5eb24c86a`](https://github.com/nodejs/node/commit/b5eb24c86a)] - **test**: force spec reporter in test-runner-watch-mode.mjs (Colin Ihrig) [#54538](https://github.com/nodejs/node/pull/54538)
- \[[`66ae9f4c0a`](https://github.com/nodejs/node/commit/66ae9f4c0a)] - **test**: use valid hostnames (Luigi Pinca) [#54556](https://github.com/nodejs/node/pull/54556)
- \[[`02d664b75f`](https://github.com/nodejs/node/commit/02d664b75f)] - **test**: fix improper path to URL conversion (Antoine du Hamel) [#54509](https://github.com/nodejs/node/pull/54509)
- \[[`8a4f8a9eff`](https://github.com/nodejs/node/commit/8a4f8a9eff)] - **test**: add tests for runner coverage with different stdout column widths (Pietro Marchini) [#54494](https://github.com/nodejs/node/pull/54494)
- \[[`b0ed8dbb2f`](https://github.com/nodejs/node/commit/b0ed8dbb2f)] - **test**: prevent V8 from writing into the system's tmpdir (Michaël Zasso) [#54395](https://github.com/nodejs/node/pull/54395)
- \[[`5ee234a5a6`](https://github.com/nodejs/node/commit/5ee234a5a6)] - **test,crypto**: update WebCryptoAPI WPT (Filip Skokan) [#54593](https://github.com/nodejs/node/pull/54593)
- \[[`a4bebf8559`](https://github.com/nodejs/node/commit/a4bebf8559)] - **test_runner**: ensure test watcher picks up new test files (Pietro Marchini) [#54225](https://github.com/nodejs/node/pull/54225)
- \[[`d4310fe9c1`](https://github.com/nodejs/node/commit/d4310fe9c1)] - **(SEMVER-MINOR)** **test_runner**: add support for coverage thresholds (Aviv Keller) [#54429](https://github.com/nodejs/node/pull/54429)
- \[[`0cf78aa24b`](https://github.com/nodejs/node/commit/0cf78aa24b)] - **test_runner**: refactor `mock_loader` (Antoine du Hamel) [#54223](https://github.com/nodejs/node/pull/54223)
- \[[`97fa075c2e`](https://github.com/nodejs/node/commit/97fa075c2e)] - **(SEMVER-MINOR)** **test_runner**: support running tests in process (Colin Ihrig) [#53927](https://github.com/nodejs/node/pull/53927)
- \[[`858b583c88`](https://github.com/nodejs/node/commit/858b583c88)] - **(SEMVER-MINOR)** **test_runner**: defer inheriting hooks until run() (Colin Ihrig) [#53927](https://github.com/nodejs/node/pull/53927)
- \[[`45b0250692`](https://github.com/nodejs/node/commit/45b0250692)] - **test_runner**: account for newline in source maps (Colin Ihrig) [#54444](https://github.com/nodejs/node/pull/54444)
- \[[`1c29e74d30`](https://github.com/nodejs/node/commit/1c29e74d30)] - **test_runner**: make `mock.module`'s `specifier` consistent with `import()` (Antoine du Hamel) [#54416](https://github.com/nodejs/node/pull/54416)
- \[[`cbe30a02a3`](https://github.com/nodejs/node/commit/cbe30a02a3)] - **test_runner**: finish build phase before running tests (Colin Ihrig) [#54423](https://github.com/nodejs/node/pull/54423)
- \[[`8a4b26f00c`](https://github.com/nodejs/node/commit/8a4b26f00c)] - **timers**: fix validation (Paolo Insogna) [#54404](https://github.com/nodejs/node/pull/54404)
- \[[`38798140c4`](https://github.com/nodejs/node/commit/38798140c4)] - **tools**: remove unused python files (Aviv Keller) [#53928](https://github.com/nodejs/node/pull/53928)
- \[[`da6c61def8`](https://github.com/nodejs/node/commit/da6c61def8)] - **tools**: add swc license (Marco Ippolito) [#54462](https://github.com/nodejs/node/pull/54462)
- \[[`16d4c437e1`](https://github.com/nodejs/node/commit/16d4c437e1)] - **typings**: provide internal types for wasi bindings (Andrew Moon) [#54119](https://github.com/nodejs/node/pull/54119)
- \[[`fe5666f006`](https://github.com/nodejs/node/commit/fe5666f006)] - **vm**: return all own names and symbols in property enumerator interceptor (Chengzhong Wu) [#54522](https://github.com/nodejs/node/pull/54522)
- \[[`db80eac496`](https://github.com/nodejs/node/commit/db80eac496)] - **(SEMVER-MINOR)** **vm**: introduce vanilla contexts via vm.constants.DONT_CONTEXTIFY (Joyee Cheung) [#54394](https://github.com/nodejs/node/pull/54394)
- \[[`8ffdd1e2b2`](https://github.com/nodejs/node/commit/8ffdd1e2b2)] - **zlib**: simplify validators (Yagiz Nizipli) [#54442](https://github.com/nodejs/node/pull/54442)

Windows 32-bit Installer: https://nodejs.org/dist/v22.8.0/node-v22.8.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v22.8.0/node-v22.8.0-x64.msi \
Windows ARM 64-bit Installer: https://nodejs.org/dist/v22.8.0/node-v22.8.0-arm64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v22.8.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v22.8.0/win-x64/node.exe \
Windows ARM 64-bit Binary: https://nodejs.org/dist/v22.8.0/win-arm64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v22.8.0/node-v22.8.0.pkg \
macOS Apple Silicon 64-bit Binary: https://nodejs.org/dist/v22.8.0/node-v22.8.0-darwin-arm64.tar.gz \
macOS Intel 64-bit Binary: https://nodejs.org/dist/v22.8.0/node-v22.8.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v22.8.0/node-v22.8.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v22.8.0/node-v22.8.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v22.8.0/node-v22.8.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v22.8.0/node-v22.8.0-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v22.8.0/node-v22.8.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v22.8.0/node-v22.8.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v22.8.0/node-v22.8.0.tar.gz \
Other release files: https://nodejs.org/dist/v22.8.0/ \
Documentation: https://nodejs.org/docs/v22.8.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

a832a113d2bf953c4866ad3c9f0d63fd09a33d205f26633835737f46933f8c5c  node-v22.8.0-aix-ppc64.tar.gz
e986e4fb3cd19cdb3050d91b863c568cb32e89e0be2fcbe6063867b202e252ac  node-v22.8.0-arm64.msi
723d53380d500087d738ee1a255fea121060602140a8e93cf7bd3013cd7531a4  node-v22.8.0-darwin-arm64.tar.gz
822d9fb8a12d4db7f0bfed008a5fbe2fd312be204a10b30930b0f3ab9e083d0e  node-v22.8.0-darwin-arm64.tar.xz
2f56ac0a9b7f03aea854b8bf2246741179c0b758a0b1b7239d1d5b242aca0f1b  node-v22.8.0-darwin-x64.tar.gz
ff60bf68aafd06540b5b1758cdd1100f1de55ea9caa7a370bb09d07733b1b81a  node-v22.8.0-darwin-x64.tar.xz
2b7ef6e8b667710d97167e5d1e610bcbeb53fa2b1b30dec73658ba3d41aabc18  node-v22.8.0-headers.tar.gz
7df44d48036b965cbca6a20bdc9fe958fbdcfa3fad2c93e4acb4f5f063e2253c  node-v22.8.0-headers.tar.xz
a23afe3a54270accabcd9be0fc0dbec6645e8c5b8b06040dcdf0086a7047622d  node-v22.8.0-linux-arm64.tar.gz
9498fec4ce1ac2178d48dea825c012ca2ab532486824b8ed0dd3a7ffae87db0f  node-v22.8.0-linux-arm64.tar.xz
b59fc61871fde20018f569e9a971599b0b0c9906843f789f22b0c3983a6cc2ac  node-v22.8.0-linux-armv7l.tar.gz
257381f0ea23e8f6f60f76c48ae07ab6a40c112108a885846f7f79cde5e02781  node-v22.8.0-linux-armv7l.tar.xz
6d7a1239cc39387676fdad2311bc95aef6b95819c4e7a0ed0edfbc6ca2caeb52  node-v22.8.0-linux-ppc64le.tar.gz
c0704004a519e148cef71d7a74097234bf84150cdde5242362a0e87c6514b6f5  node-v22.8.0-linux-ppc64le.tar.xz
63114451139bf8a03c90dd57ae896840d76ded1e4f72e7feaecfc9eab8da4957  node-v22.8.0-linux-s390x.tar.gz
cd780416de584957016261ee702840d5a6b169e7bb63565ef2b3f865e3a671e6  node-v22.8.0-linux-s390x.tar.xz
a0bc969f61b96575a4b2cdd239729aa3e721384fa9ca3fb926a9582c019c1bbc  node-v22.8.0-linux-x64.tar.gz
b6827dccd983acad09496f28a0f277218cc49302a8a7179ccbd7bf38305f5623  node-v22.8.0-linux-x64.tar.xz
30700aa91ec1c7d6f05b376cbe73e9d14a7148153f4d54514c9b492b0dcbf143  node-v22.8.0.pkg
cd5a5bd63c6c0a44278c9da0126e5d2c06a688462f18ed44f314a346a46e7ec5  node-v22.8.0.tar.gz
f130e82176d1ee0702d99afc1995d0061bf8ed357c38834a32a08c9ef74f1ac7  node-v22.8.0.tar.xz
30c10cf2ec1317ff2fb092f456e797907d4a70b3f190c11870ceaefb13b4664e  node-v22.8.0-win-arm64.7z
89df2f4a03bc0a85083fa0120eb9391ea56e941378312fc0b83821f1f479cabb  node-v22.8.0-win-arm64.zip
91c007a75c837abfabae44bdc98e9b4c6f90029d4662b9c4e4b9d1b67a046f17  node-v22.8.0-win-x64.7z
d6e1c4fca93997224cac0bec09b4201aa018f50171d38c6b85abe483012839c9  node-v22.8.0-win-x64.zip
565212e330b65b6edc635726d222f44f0a66f35e1b3a84b3181e302dfd48d370  node-v22.8.0-win-x86.7z
d34f514ffd372331e045a0a3900517164cc762ae54a5b3133552d82a4fa6e2c5  node-v22.8.0-win-x86.zip
f7870f6da18f9f884f09eed40da74529018888d51f620f3cb7c14b75b823af3f  node-v22.8.0-x64.msi
c10cfe8ea214eeb3fc7e51997ee16db04d68a62b9355dbc091073c5c12535287  node-v22.8.0-x86.msi
808a02cbea61ad30da21b0cdebe9a7f1ace466f42a0419c7f44c6aaaee3bf832  win-arm64/node.exe
a155da85a633f586997023ca6e4fdd576b031da4d2488d0fdc6cacd48513735a  win-arm64/node.lib
a8fd60482b38cf3a26b4f208ea53afd82ed3fbea00a829a384274234c6062eaa  win-arm64/node_pdb.7z
4b55f899389bc96d1efc9a5a759162f2c1d670a8f13763910474cdfb72e60dce  win-arm64/node_pdb.zip
693db8fce6bb56248b144a46f20c406c7b54a262e7514120c6cae85fd66e2c10  win-x64/node.exe
9d0b2b6a78f071562f85926e32913213d619326f98ce15ed38142948167c90a0  win-x64/node.lib
659e442d9561ffb09ae81b93304d4da8a6b84f722c1c743961850dabbb5526fc  win-x64/node_pdb.7z
deb913d0297acba29e414b5d72795f7c57ef0c3fac44e69b02f6121e41e2ebbf  win-x64/node_pdb.zip
7359847131b5228a6fa3f0927f009158b1c8c24862ae5060df3163e3b5856f0c  win-x86/node.exe
86b3354a9ef56075d164787f2fe5b77b31088a2d7e79762cccc1d57e84ea51b2  win-x86/node.lib
1ecc56ba6a3e6188025e27ab74b22d06c63a16b7c6dc362f9b7026279b936f7d  win-x86/node_pdb.7z
569e81de407f3b4c6b19821d6d8369e922136cd8b84350367b08bcf5736b2092  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQGzBAEBCAAdFiEEiQwI24V5Fi/uDfnbi+q0389VXvQFAmbXEuEACgkQi+q0389V
XvQtwgwAm3yNd8qbDICXS+rB2NQYsfgKKYZl/AOYt/UDguudK9wbV1Bw9zE65wjs
nqOru3rM5BeR+ujljpYFG3ZlmnRtVFBeVrd5bNHHIVVvQJ4OPORAx+CRfP2COTZt
VpwyZGAGRgbbiCAlrnQ7VfOKogcL0YjjqPQpgPPDESQ1BQjqbqdg6WAALUSAPcYU
x2pWHUBPzrlVjCxn9wUqawc7KpoVgrOHsOCtBbniY+y1jR6W4h95zALt3a3dJVTQ
jWU0TiZdx8+plqgqbJ4eYe0SniDCimKMxp/zOV1pNHEH2aRmYp/AR49MkmDbKp6H
JG2PV2VALvUTEWjQtrTbq6YVzFZcVyEW2HlbN3UyRmzDdWsIxhLmm0AYfNKI3vl5
84zy/d5zDoXtrsI/FDzdcaqYlH8lL2yCZK0hoLnEGynHMsXm2exajUjYCaG3UmKs
mD2oDHfm3oEEB7XddrqG4mPDe0wgpk1llqBaLNhyTmPzYewkJoPTiTEdwehtgRsV
n9nxU0aa
=6yKj
-----END PGP SIGNATURE-----
```
