---
date: '2021-02-23T13:02:35.475Z'
category: release
title: Node v14.16.0 (LTS)
layout: blog-post
author: <PERSON>
---

### Notable changes

Vulnerabilities fixed:

- **CVE-2021-22883**: HTTP2 'unknownProtocol' cause Denial of Service by resource exhaustion
  - Affected Node.js versions are vulnerable to denial of service attacks when too many connection attempts with an 'unknownProtocol' are established. This leads to a leak of file descriptors. If a file descriptor limit is configured on the system, then the server is unable to accept new connections and prevent the process also from opening, e.g. a file. If no file descriptor limit is configured, then this lead to an excessive memory usage and cause the system to run out of memory.
- **CVE-2021-22884**: DNS rebinding in --inspect
  - Affected Node.js versions are vulnerable to denial of service attacks when the whitelist includes “localhost6”. When “localhost6” is not present in /etc/hosts, it is just an ordinary domain that is resolved via DNS, i.e., over network. If the attacker controls the victim's DNS server or can spoof its responses, the DNS rebinding protection can be bypassed by using the “localhost6” domain. As long as the attacker uses the “localhost6” domain, they can still apply the attack described in CVE-2018-7160.
- **CVE-2021-23840**: OpenSSL - Integer overflow in CipherUpdate
  - This is a vulnerability in OpenSSL which may be exploited through Node.js. You can read more about it in https://www.openssl.org/news/secadv/20210216.txt

### Commits

- [[`313d26800c`](https://github.com/nodejs/node/commit/313d26800c)] - **deps**: update archs files for OpenSSL-1.1.1j (Daniel Bevenius) [#37412](https://github.com/nodejs/node/pull/37412)
- [[`6098012b48`](https://github.com/nodejs/node/commit/6098012b48)] - **deps**: upgrade openssl sources to 1.1.1j (Daniel Bevenius) [#37412](https://github.com/nodejs/node/pull/37412)
- [[`afea10b097`](https://github.com/nodejs/node/commit/afea10b097)] - **(SEMVER-MINOR)** **http2**: add unknownProtocol timeout (Daniel Bevenius) [nodejs-private/node-private#246](https://github.com/nodejs-private/node-private/pull/246)
- [[`1ca3f5abcb`](https://github.com/nodejs/node/commit/1ca3f5abcb)] - **src**: drop localhost6 as allowed host for inspector (Matteo Collina) [nodejs-private/node-private#244](https://github.com/nodejs-private/node-private/pull/244)

Windows 32-bit Installer: https://nodejs.org/dist/v14.16.0/node-v14.16.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v14.16.0/node-v14.16.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v14.16.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v14.16.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v14.16.0/node-v14.16.0.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v14.16.0/node-v14.16.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v14.16.0/node-v14.16.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v14.16.0/node-v14.16.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v14.16.0/node-v14.16.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v14.16.0/node-v14.16.0-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v14.16.0/node-v14.16.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v14.16.0/node-v14.16.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v14.16.0/node-v14.16.0.tar.gz \
Other release files: https://nodejs.org/dist/v14.16.0/ \
Documentation: https://nodejs.org/docs/v14.16.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

086c8b4aa726c3b197765c9f66e05b25f8e811853d0686dfbf380299b4bf5869  node-v14.16.0-aix-ppc64.tar.gz
14ec767e376d1e2e668f997065926c5c0086ec46516d1d45918af8ae05bd4583  node-v14.16.0-darwin-x64.tar.gz
e20317c315198b3a6eaefbcf60b76082558d63be264736d56ecc7f5b0dabe60b  node-v14.16.0-darwin-x64.tar.xz
4b44b92903a61c29af20550f9d25bfc3029657df6b5f0a12072a70360f7eedee  node-v14.16.0-headers.tar.gz
e0704d9f5accdb876dd45be1b2ade2176219893b8a5df863130f3d9a11e69702  node-v14.16.0-headers.tar.xz
2b78771550f8a3e6e990d8e60e9ade82c7a9e2738b6222e92198bcd5ea857ea6  node-v14.16.0-linux-arm64.tar.gz
440489a08bfd020e814c9e65017f58d692299ac3f150c8e78d01abb1104c878a  node-v14.16.0-linux-arm64.tar.xz
a64860a02e4b692d262d453952cae99a3c9842d3fc90336b54ca03e990c780b8  node-v14.16.0-linux-armv7l.tar.gz
5d28ac28088c9a8c679285ed09df979125982941eec2c7cb465eee9a3b0be3c0  node-v14.16.0-linux-armv7l.tar.xz
2339b4b1a8db39348cb1877b0cfdee3b2ef2b730f461ef7263610cbaaea5232a  node-v14.16.0-linux-ppc64le.tar.gz
47a3768924a1f09ab8f9f50d0c73503e7bf9bb5a2bee58acf91e1e02d412ec78  node-v14.16.0-linux-ppc64le.tar.xz
485d6f38270de0763d9132b78fc88cefc8312a71f53587c874a2c09e58396057  node-v14.16.0-linux-s390x.tar.gz
335348e46f45284b6356416ef58f85602d2dee99094588b65900f6c8839df77e  node-v14.16.0-linux-s390x.tar.xz
7212031d7468718d7c8f5e1766380daaabe09d54611675338e7a88a97c3e31b6  node-v14.16.0-linux-x64.tar.gz
2e079cf638766fedd720d30ec8ffef5d6ceada4e8b441fc2a093cb9a865f4087  node-v14.16.0-linux-x64.tar.xz
8f8f6f7b6db7c518be52878a3a58d48bcd575446a9c6ce75a60cdb6cfe1a3dd0  node-v14.16.0.pkg
f6b904b06951de4c52089dd4456155d853e835b0dc4640f75458c6eb49f9e8ce  node-v14.16.0.tar.gz
4e7648a617f79b459d583f7dbdd31fbbac5b846d41598f3b54331a5b6115dfa6  node-v14.16.0.tar.xz
67f352be37b1b0c00ff7531e6b2a93b0021e5f06743f16394b9dbd6fc47a7fe9  node-v14.16.0-win-x64.7z
716045c2f16ea10ca97bd04cf2e5ef865f9c4d6d677a9bc25e2ea522b594af4f  node-v14.16.0-win-x64.zip
0f862027290301918db84dd705024a9b623a08f2ad86dfb3829c32c9c050e25c  node-v14.16.0-win-x86.7z
9699067581e0d333b13158d4ebb27b6357444564548aaa220d821cdc6d840bd2  node-v14.16.0-win-x86.zip
d9243c9d02f5e4801b8b3ab848f45ce0da2882b5fff448191548ca49af434066  node-v14.16.0-x64.msi
61d549ed39fc264df9978f824042f3f4cac90a866e933c5088384d5dedf283fe  node-v14.16.0-x86.msi
5c10d99cc7229fd8092585f7a5e15771d62dc84b9edeb0c2140a2d187b305506  win-x64/node.exe
3f67805bbcfea13e9acc892f4590cacc5eb2514ac817d003f5327c07a2385a0d  win-x64/node.lib
fe3cab4ca83172f495b83710731224e01cd936c2d823a53480bed1113fa704f8  win-x64/node_pdb.7z
099ed74db3ceb4e47816bd1a9d792a4ab465ba60ec945b65e95e24b7049f76e0  win-x64/node_pdb.zip
c7b723504b5ee487785a12989026db061f5ee1fb120826fcc1afcbec5fc5d1d5  win-x86/node.exe
dfe06cfa2c95a49a92bfebc8f68ec1216b951c9363cfc8b36fbc80fc1588fb15  win-x86/node.lib
235bc5ec83e9dfd328dbca9c71f2f036f9bb13d2378c3869ced7a1edc5ff63a1  win-x86/node_pdb.7z
58a50e0a495189c054c96e76d2f9a6c8f45d6880852db3404eaca99a133c91a4  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQEzBAEBCAAdFiEETtd49TnjY0x3nIfG1wYoSKGrAFwFAmA0/HgACgkQ1wYoSKGr
AFxqNQgArjEJi6JfqFHsMDkKp75ZHZY7X+1VjbwaPZd7r1dY4gbe/ZqHdXEzVHEB
Y1Pbqrkng8zOzHDWqxGhjuOYCwjcKynLAKcfKqCpZ/reqQzf2lmdB2zM8clqG++r
rWT9auJz4JtxC5cSQ48rEjju2aQpwSARlrZepr99k8QoRWL8lZhp8t/ccYwWMunN
oE8v/4wR1WX23GnQmAze9I7ABYmxClFmDSUG85ml4VAI7+G0bLiH72gCe6sJR2Ms
e5gS5XqbRvth4oUH4f/IRRoS0BOl5oXY74Qg2d23qMG6WKBu6Gu/phcFWpSuiMnV
BHFfl4y/8zRNLHPD00KwOp4qucMLvA==
=XRSJ
-----END PGP SIGNATURE-----

```
