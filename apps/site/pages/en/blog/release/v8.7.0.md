---
date: '2017-10-11T20:52:57.770Z'
category: release
title: Node v8.7.0 (Current)
layout: blog-post
author: <PERSON><PERSON>
---

### Notable Changes

- **deps**:
  - update npm to 5.4.2
    [#15600](https://github.com/nodejs/node/pull/15600)
  - upgrade libuv to 1.15.0
    [#15745](https://github.com/nodejs/node/pull/15745)
  - update V8 to 6.1.534.42
    [15393](https://github.com/nodejs/node/pull/15393)
- **dgram**:
  - support for setting dgram socket buffer size
    [#13623](https://github.com/nodejs/node/pull/13623)
- **fs**:
  - add support O_DSYNC file open constant
    [#15451](https://github.com/nodejs/node/pull/15451)
- **util**:
  - deprecate obj.inspect for custom inspection
    [#15631](https://github.com/nodejs/node/pull/15631)
- **tools, build**:
  - there is a fancy new macOS installer
    [#15179](https://github.com/nodejs/node/pull/15179)
- **Added new collaborators**
  - [bmeurer](https://github.com/bmeurer) - Benedikt Meurer
  - [kfarnung](https://github.com/kfarnung) - Kyle Farnung

### Commits

- [[`16bdbb9e76`](https://github.com/nodejs/node/commit/16bdbb9e76)] - **async_hooks**: fix reference in code comment (Brian White) [#15748](https://github.com/nodejs/node/pull/15748)
- [[`1bc0c1fb5f`](https://github.com/nodejs/node/commit/1bc0c1fb5f)] - **async_hooks**: consistent internal naming (Andreas Madsen) [#15569](https://github.com/nodejs/node/pull/15569)
- [[`9da8346c96`](https://github.com/nodejs/node/commit/9da8346c96)] - **async_wrap**: allow user to pass execution_async_id (Trevor Norris) [#14208](https://github.com/nodejs/node/pull/14208)
- [[`09b3faef40`](https://github.com/nodejs/node/commit/09b3faef40)] - **async_wrap**: add constructor for PromiseWrap (Trevor Norris) [#14208](https://github.com/nodejs/node/pull/14208)
- [[`67cef9b182`](https://github.com/nodejs/node/commit/67cef9b182)] - **build**: allow build with system python 3 (Emily Marigold Klassen) [#16058](https://github.com/nodejs/node/pull/16058)
- [[`3d2481e6cb`](https://github.com/nodejs/node/commit/3d2481e6cb)] - **build**: call setlocal in vcbuild.bat (Daniel Bevenius) [#15754](https://github.com/nodejs/node/pull/15754)
- [[`ed8c89a07d`](https://github.com/nodejs/node/commit/ed8c89a07d)] - **build**: fix shared installing target (Yorkie Liu) [#15148](https://github.com/nodejs/node/pull/15148)
- [[`7dd0ca40e2`](https://github.com/nodejs/node/commit/7dd0ca40e2)] - **build**: run es-module tests in CI (Benjamin Coe) [#15276](https://github.com/nodejs/node/pull/15276)
- [[`81515c7b62`](https://github.com/nodejs/node/commit/81515c7b62)] - **build**: add test-with-async-hooks (Trevor Norris) [#14208](https://github.com/nodejs/node/pull/14208)
- [[`1ed0c7706f`](https://github.com/nodejs/node/commit/1ed0c7706f)] - **crypto**: better crypto error messages (Greg Alexander) [#15518](https://github.com/nodejs/node/pull/15518)
- [[`be4e809af2`](https://github.com/nodejs/node/commit/be4e809af2)] - **crypto**: use X509V3_EXT_d2i (David Benjamin) [#15348](https://github.com/nodejs/node/pull/15348)
- [[`93d5ead37a`](https://github.com/nodejs/node/commit/93d5ead37a)] - **crypto**: use SSL_SESSION_get_id (David Benjamin) [#15348](https://github.com/nodejs/node/pull/15348)
- [[`9eeaab4ba5`](https://github.com/nodejs/node/commit/9eeaab4ba5)] - **crypto**: only try to set FIPS mode if different (Gibson Fahnestock) [#12210](https://github.com/nodejs/node/pull/12210)
- [[`77bdfc96ae`](https://github.com/nodejs/node/commit/77bdfc96ae)] - **deps**: upgrade libuv to 1.15.0 (cjihrig) [#15745](https://github.com/nodejs/node/pull/15745)
- [[`c17ff62376`](https://github.com/nodejs/node/commit/c17ff62376)] - **deps**: cherry-pick f4a2b7f3 from V8 upstream. (Erin Spiceland) [#16053](https://github.com/nodejs/node/pull/16053)
- [[`1c0ae10c26`](https://github.com/nodejs/node/commit/1c0ae10c26)] - **deps**: V8: cherry-pick 163d360 from upstream (Ali Ijaz Sheikh) [#15664](https://github.com/nodejs/node/pull/15664)
- [[`3f2ea53043`](https://github.com/nodejs/node/commit/3f2ea53043)] - **deps**: update npm to 5.4.2 (Michaël Zasso)
- [[`6a019183c6`](https://github.com/nodejs/node/commit/6a019183c6)] - **deps**: cherry-pick 0353a1e from upstream V8 (Michaël Zasso) [#15599](https://github.com/nodejs/node/pull/15599)
- [[`97c0880052`](https://github.com/nodejs/node/commit/97c0880052)] - **deps**: update V8 to 6.1.534.42 (Michaël Zasso) [#15521](https://github.com/nodejs/node/pull/15521)
- [[`b4ad15be5f`](https://github.com/nodejs/node/commit/b4ad15be5f)] - **deps**: cherry-pick 9b21865822243 from V8 upstream (Anna Henningsen) [#15391](https://github.com/nodejs/node/pull/15391)
- [[`e1828eb50d`](https://github.com/nodejs/node/commit/e1828eb50d)] - **deps**: cherry-pick b6158eb6befae from V8 upstream (Anna Henningsen) [#15391](https://github.com/nodejs/node/pull/15391)
- [[`aa1a3ea998`](https://github.com/nodejs/node/commit/aa1a3ea998)] - **(SEMVER-MINOR)** **deps**: revert ABI breaking changes in V8 6.1 (Anna Henningsen) [#15393](https://github.com/nodejs/node/pull/15393)
- [[`847174759d`](https://github.com/nodejs/node/commit/847174759d)] - **deps**: patch V8 to 6.1.534.38 (Myles Borins) [#15431](https://github.com/nodejs/node/pull/15431)
- [[`c0b5b09381`](https://github.com/nodejs/node/commit/c0b5b09381)] - **(SEMVER-MINOR)** **deps**: add postmortem metadata for V8 TurboFan (Michaël Zasso) [#14730](https://github.com/nodejs/node/pull/14730)
- [[`9934dfeb5e`](https://github.com/nodejs/node/commit/9934dfeb5e)] - **deps**: cherry-pick 1aead19 from upstream V8 (Ben Noordhuis) [#15184](https://github.com/nodejs/node/pull/15184)
- [[`273822f756`](https://github.com/nodejs/node/commit/273822f756)] - **deps**: cherry-pick e020aae394 from V8 upstream (Ben Noordhuis) [#14913](https://github.com/nodejs/node/pull/14913)
- [[`d85283b76b`](https://github.com/nodejs/node/commit/d85283b76b)] - **deps**: backport f9c4b7a from upstream V8 (Matt Loring) [#14001](https://github.com/nodejs/node/pull/14001)
- [[`19a5021ee3`](https://github.com/nodejs/node/commit/19a5021ee3)] - **deps**: backport bca8409 from upstream V8 (Matt Loring) [#14001](https://github.com/nodejs/node/pull/14001)
- [[`2601a515f9`](https://github.com/nodejs/node/commit/2601a515f9)] - **deps**: backport 6e9e2e5 from upstream V8 (Matt Loring) [#14001](https://github.com/nodejs/node/pull/14001)
- [[`ede9d2ed8e`](https://github.com/nodejs/node/commit/ede9d2ed8e)] - **(SEMVER-MINOR)** **deps**: cherry-pick f19b889 from upstream V8 (Michaël Zasso) [#14730](https://github.com/nodejs/node/pull/14730)
- [[`63ebad5a04`](https://github.com/nodejs/node/commit/63ebad5a04)] - **(SEMVER-MINOR)** **deps**: fix addons compilation with VS2013 (Bartosz Sosnowski) [#13263](https://github.com/nodejs/node/pull/13263)
- [[`21004dda00`](https://github.com/nodejs/node/commit/21004dda00)] - **deps**: limit regress/regress-crbug-514081 v8 test (Michael Dawson) [#6678](https://github.com/nodejs/node/pull/6678)
- [[`d67fb8188f`](https://github.com/nodejs/node/commit/d67fb8188f)] - **(SEMVER-MINOR)** **deps**: update V8 to 6.1.534.36 (Michaël Zasso) [#15393](https://github.com/nodejs/node/pull/15393)
- [[`827f843dfa`](https://github.com/nodejs/node/commit/827f843dfa)] - **dgram**: refactor SO_RCVBUF and SO_SNDBUF methods (cjihrig) [#15483](https://github.com/nodejs/node/pull/15483)
- [[`e3658143e5`](https://github.com/nodejs/node/commit/e3658143e5)] - **(SEMVER-MINOR)** **dgram**: support for setting socket buffer size (Damien O'Reilly) [#13623](https://github.com/nodejs/node/pull/13623)
- [[`bae46dc806`](https://github.com/nodejs/node/commit/bae46dc806)] - **doc**: add kfarnung to collaborators (Kyle Farnung) [#16108](https://github.com/nodejs/node/pull/16108)
- [[`d1266a3c57`](https://github.com/nodejs/node/commit/d1266a3c57)] - **doc**: mention collaboration summit in onboarding.md (Joyee Cheung) [#16079](https://github.com/nodejs/node/pull/16079)
- [[`140c98b327`](https://github.com/nodejs/node/commit/140c98b327)] - **doc**: document the benchmark CI (Joyee Cheung) [#16086](https://github.com/nodejs/node/pull/16086)
- [[`66a2c710f2`](https://github.com/nodejs/node/commit/66a2c710f2)] - **doc**: fix macosx-firewall suggestion BUILDING (suraiyah) [#15829](https://github.com/nodejs/node/pull/15829)
- [[`44719ed74d`](https://github.com/nodejs/node/commit/44719ed74d)] - **doc**: add clearer setup description (Emily Platzer) [#15962](https://github.com/nodejs/node/pull/15962)
- [[`9f6d535b87`](https://github.com/nodejs/node/commit/9f6d535b87)] - **doc**: update style guide for markdown extension (Rich Trott) [#15786](https://github.com/nodejs/node/pull/15786)
- [[`acd4924448`](https://github.com/nodejs/node/commit/acd4924448)] - **doc**: fix http2 API docs typos (Daniela Borges Matos de Carvalho) [#15778](https://github.com/nodejs/node/pull/15778)
- [[`74755415cc`](https://github.com/nodejs/node/commit/74755415cc)] - **doc**: fix: correctly use `public key` instead of `private key` (Pavel Pomerantsev) [#16038](https://github.com/nodejs/node/pull/16038)
- [[`0ae84c2434`](https://github.com/nodejs/node/commit/0ae84c2434)] - **doc**: fix incorrect vm.createContext usage (tshemsedinov) [#16059](https://github.com/nodejs/node/pull/16059)
- [[`344d6132ee`](https://github.com/nodejs/node/commit/344d6132ee)] - **doc**: fix YAML syntax in fs.md (Luigi Pinca) [#15769](https://github.com/nodejs/node/pull/15769)
- [[`df1d988270`](https://github.com/nodejs/node/commit/df1d988270)] - **doc**: explain common.restore\* functions (Rich Trott) [#15720](https://github.com/nodejs/node/pull/15720)
- [[`dcad2df78b`](https://github.com/nodejs/node/commit/dcad2df78b)] - **doc**: fix typo in tls.md (kohta ito) [#15738](https://github.com/nodejs/node/pull/15738)
- [[`979e38b13c`](https://github.com/nodejs/node/commit/979e38b13c)] - **doc**: add 'git clean -xfd' to backport guide (Lance Ball) [#15715](https://github.com/nodejs/node/pull/15715)
- [[`978f78ef01`](https://github.com/nodejs/node/commit/978f78ef01)] - **doc**: alphabetize TSC Emeriti in README.md (Rich Trott) [#15722](https://github.com/nodejs/node/pull/15722)
- [[`54a43a6d38`](https://github.com/nodejs/node/commit/54a43a6d38)] - **doc**: change encoding to decoding (Sakthipriyan Vairamani (thefourtheye)) [#15706](https://github.com/nodejs/node/pull/15706)
- [[`cf579eae25`](https://github.com/nodejs/node/commit/cf579eae25)] - **doc**: fix dead link in doc/releases.md (Luigi Pinca) [#15733](https://github.com/nodejs/node/pull/15733)
- [[`fcea265421`](https://github.com/nodejs/node/commit/fcea265421)] - **doc**: fix v8.6 changelog entry (Ruben Bridgewater) [#15716](https://github.com/nodejs/node/pull/15716)
- [[`5630c8cd5d`](https://github.com/nodejs/node/commit/5630c8cd5d)] - **doc**: add missing TOC entry in CONTRIBUTING.md (Vse Mozhet Byt) [#15729](https://github.com/nodejs/node/pull/15729)
- [[`db0ba97bec`](https://github.com/nodejs/node/commit/db0ba97bec)] - **doc**: update fs.utimes{,Sync} changelog (Luigi Pinca) [#15680](https://github.com/nodejs/node/pull/15680)
- [[`cc902832e2`](https://github.com/nodejs/node/commit/cc902832e2)] - **doc**: edit COLLABORATORS_GUIDE.md for readability (Rich Trott) [#15629](https://github.com/nodejs/node/pull/15629)
- [[`f8e93e888e`](https://github.com/nodejs/node/commit/f8e93e888e)] - **doc**: fix links in some intra-repository docs (Vse Mozhet Byt) [#15675](https://github.com/nodejs/node/pull/15675)
- [[`9c247c56ab`](https://github.com/nodejs/node/commit/9c247c56ab)] - **doc**: standardize function param/object prop style (Gibson Fahnestock) [#13769](https://github.com/nodejs/node/pull/13769)
- [[`e5b5a03e00`](https://github.com/nodejs/node/commit/e5b5a03e00)] - **doc**: do not begin yaml value with backtick (Jon Moss) [#15447](https://github.com/nodejs/node/pull/15447)
- [[`f8805c4465`](https://github.com/nodejs/node/commit/f8805c4465)] - **doc**: fix link in the test/README.md (Rimas Misevičius) [#15642](https://github.com/nodejs/node/pull/15642)
- [[`1141e930a3`](https://github.com/nodejs/node/commit/1141e930a3)] - **doc**: update libuv license (Timothy Gu) [#15649](https://github.com/nodejs/node/pull/15649)
- [[`db70874c8f`](https://github.com/nodejs/node/commit/db70874c8f)] - **doc**: add bmeurer to collaborators (Benedikt Meurer) [#15677](https://github.com/nodejs/node/pull/15677)
- [[`ec56cbe572`](https://github.com/nodejs/node/commit/ec56cbe572)] - **doc**: improve fs.utimes (Refael Ackermann) [#14154](https://github.com/nodejs/node/pull/14154)
- [[`6565ddabd0`](https://github.com/nodejs/node/commit/6565ddabd0)] - **doc**: add callback function signatures in fs.md (Matej Krajčovič) [#13424](https://github.com/nodejs/node/pull/13424)
- [[`22b2d1a786`](https://github.com/nodejs/node/commit/22b2d1a786)] - **doc**: fix mistake in http2stream.respondWithFile. (Antoine AMARA) [#15501](https://github.com/nodejs/node/pull/15501)
- [[`d1d2ca5bef`](https://github.com/nodejs/node/commit/d1d2ca5bef)] - **doc**: retire bnoordhuis from the TSC (Ben Noordhuis) [#15626](https://github.com/nodejs/node/pull/15626)
- [[`e0a76347d4`](https://github.com/nodejs/node/commit/e0a76347d4)] - **doc**: update table of contents for common/README.md (Rich Trott) [#15595](https://github.com/nodejs/node/pull/15595)
- [[`6003afcc71`](https://github.com/nodejs/node/commit/6003afcc71)] - **doc,test**: minor improvements to O_DSYNC (Tobias Nießen) [#15547](https://github.com/nodejs/node/pull/15547)
- [[`a814a551f3`](https://github.com/nodejs/node/commit/a814a551f3)] - **(SEMVER-MINOR)** **fs**: add O_DSYNC (Jussi Räsänen) [#15451](https://github.com/nodejs/node/pull/15451)
- [[`9c1e48dca5`](https://github.com/nodejs/node/commit/9c1e48dca5)] - **http**: client keep-alive for UNIX domain sockets (Bryan English) [#13214](https://github.com/nodejs/node/pull/13214)
- [[`10622c6331`](https://github.com/nodejs/node/commit/10622c6331)] - **http2**: near full http1 compatibility, add tests (Anatoli Papirovski) [#15702](https://github.com/nodejs/node/pull/15702)
- [[`86dfcc609c`](https://github.com/nodejs/node/commit/86dfcc609c)] - **http2**: making sending to the socket more efficient (James M Snell) [#15693](https://github.com/nodejs/node/pull/15693)
- [[`68cd233a7b`](https://github.com/nodejs/node/commit/68cd233a7b)] - **http2**: eliminate dead code (James M Snell) [#15693](https://github.com/nodejs/node/pull/15693)
- [[`078ee27f13`](https://github.com/nodejs/node/commit/078ee27f13)] - **http2**: refactor method arguments to avoid bools (James M Snell) [#15693](https://github.com/nodejs/node/pull/15693)
- [[`86ee05d5ca`](https://github.com/nodejs/node/commit/86ee05d5ca)] - **http2**: simplify TypeName (James M Snell) [#15693](https://github.com/nodejs/node/pull/15693)
- [[`df271f4f00`](https://github.com/nodejs/node/commit/df271f4f00)] - **http2**: setting shuttingDown=true after validation (Trivikram Kamat) [#15676](https://github.com/nodejs/node/pull/15676)
- [[`a4a5bee933`](https://github.com/nodejs/node/commit/a4a5bee933)] - **http2**: adjust error emit in core, add tests (Anatoli Papirovski) [#15586](https://github.com/nodejs/node/pull/15586)
- [[`5f469a26f3`](https://github.com/nodejs/node/commit/5f469a26f3)] - **n-api**: add check for large strings (Michael Dawson) [#15611](https://github.com/nodejs/node/pull/15611)
- [[`de52eb8680`](https://github.com/nodejs/node/commit/de52eb8680)] - **perf_hooks**: remove docs for unimplemented API (Sam Roberts) [#15641](https://github.com/nodejs/node/pull/15641)
- [[`e4c461ba7d`](https://github.com/nodejs/node/commit/e4c461ba7d)] - **src**: replace manual memory mgmt with std::string (Ben Noordhuis) [#15782](https://github.com/nodejs/node/pull/15782)
- [[`6642f54184`](https://github.com/nodejs/node/commit/6642f54184)] - **src**: fix ^ in stack trace with vm's columnOffset (Timothy Gu) [#15771](https://github.com/nodejs/node/pull/15771)
- [[`824b8dfe9e`](https://github.com/nodejs/node/commit/824b8dfe9e)] - **src**: remove unused node_dtrace.h from node_win32 (Daniel Bevenius) [#15768](https://github.com/nodejs/node/pull/15768)
- [[`0004214ea7`](https://github.com/nodejs/node/commit/0004214ea7)] - **src**: trace_event macro line continuation cleanup (Daniel Bevenius) [#15750](https://github.com/nodejs/node/pull/15750)
- [[`15063844cb`](https://github.com/nodejs/node/commit/15063844cb)] - **src**: fix windows-only build breakage (Ben Noordhuis) [#15724](https://github.com/nodejs/node/pull/15724)
- [[`965efd7b47`](https://github.com/nodejs/node/commit/965efd7b47)] - **src**: remove unused includes in src/tracing (Daniel Bevenius) [#15682](https://github.com/nodejs/node/pull/15682)
- [[`64d0c7422d`](https://github.com/nodejs/node/commit/64d0c7422d)] - **src**: use UV_EINVAL instead of EINVAL in udp_wrap (Daniel Bevenius) [#15444](https://github.com/nodejs/node/pull/15444)
- [[`6551bb3ace`](https://github.com/nodejs/node/commit/6551bb3ace)] - **src**: fix compiler warning in udp_wrap.cc (Daniel Bevenius) [#15402](https://github.com/nodejs/node/pull/15402)
- [[`7e1003aad3`](https://github.com/nodejs/node/commit/7e1003aad3)] - **src**: remove unused using in node_trace_writer.h (Daniel Bevenius) [#15646](https://github.com/nodejs/node/pull/15646)
- [[`25fd85df36`](https://github.com/nodejs/node/commit/25fd85df36)] - **src**: add help for NODE_PENDING_DEPRECATION env (Thomas Corbière) [#15609](https://github.com/nodejs/node/pull/15609)
- [[`ca02576fb4`](https://github.com/nodejs/node/commit/ca02576fb4)] - **src**: fix typo in probe description (Evan Lucas) [#15397](https://github.com/nodejs/node/pull/15397)
- [[`69f8738a59`](https://github.com/nodejs/node/commit/69f8738a59)] - **src**: remove unused variable in node_url.cc (cjihrig) [#15592](https://github.com/nodejs/node/pull/15592)
- [[`9fcf5d7f25`](https://github.com/nodejs/node/commit/9fcf5d7f25)] - **src**: remove unused computation (cjihrig) [#15593](https://github.com/nodejs/node/pull/15593)
- [[`44ea5254f3`](https://github.com/nodejs/node/commit/44ea5254f3)] - **src**: clear async id stack if bootstrap throws (Trevor Norris) [#15553](https://github.com/nodejs/node/pull/15553)
- [[`67205391b3`](https://github.com/nodejs/node/commit/67205391b3)] - **src**: move node_trace_writer/buffer.h to agent.cc (Daniel Bevenius) [#15598](https://github.com/nodejs/node/pull/15598)
- [[`fd1a8924fd`](https://github.com/nodejs/node/commit/fd1a8924fd)] - **src**: constify PerformanceEntry data members (Ben Noordhuis) [#15458](https://github.com/nodejs/node/pull/15458)
- [[`e72761a27f`](https://github.com/nodejs/node/commit/e72761a27f)] - **src**: return references from getters, not copies (Ben Noordhuis) [#15458](https://github.com/nodejs/node/pull/15458)
- [[`aded597c10`](https://github.com/nodejs/node/commit/aded597c10)] - **src**: handle uv_async_init() failure (Ben Noordhuis) [#15458](https://github.com/nodejs/node/pull/15458)
- [[`d202c05f7e`](https://github.com/nodejs/node/commit/d202c05f7e)] - **src**: remove unused static variable (Ben Noordhuis) [#15458](https://github.com/nodejs/node/pull/15458)
- [[`902feeaad8`](https://github.com/nodejs/node/commit/902feeaad8)] - **src**: use InstantiateModule instead of deprecated (Daniel Bevenius) [#15423](https://github.com/nodejs/node/pull/15423)
- [[`e8da556eca`](https://github.com/nodejs/node/commit/e8da556eca)] - **src**: keep track of env properly in node_perf.cc (Anna Henningsen) [#15391](https://github.com/nodejs/node/pull/15391)
- [[`2e8652e164`](https://github.com/nodejs/node/commit/2e8652e164)] - **(SEMVER-MINOR)** **src**: fix SmartOS compilation (Michaël Zasso) [#14730](https://github.com/nodejs/node/pull/14730)
- [[`a43f681c20`](https://github.com/nodejs/node/commit/a43f681c20)] - **src,etw**: fix event 9 on 64 bit Windows (João Reis) [#15563](https://github.com/nodejs/node/pull/15563)
- [[`ae91ffe53c`](https://github.com/nodejs/node/commit/ae91ffe53c)] - **stream**: fix disparity between buffer and the count (jlvivero) [#15661](https://github.com/nodejs/node/pull/15661)
- [[`3d6390b32b`](https://github.com/nodejs/node/commit/3d6390b32b)] - **stream**: fix todo (Ruben Bridgewater) [#15667](https://github.com/nodejs/node/pull/15667)
- [[`6f42b680e3`](https://github.com/nodejs/node/commit/6f42b680e3)] - **test**: replace common.fixturesDir w/ fixtures.path (Druotic) [#15819](https://github.com/nodejs/node/pull/15819)
- [[`b1e6373dcc`](https://github.com/nodejs/node/commit/b1e6373dcc)] - **test**: replaces fixturesDir with fixtures (Alireza Alidousti) [#15838](https://github.com/nodejs/node/pull/15838)
- [[`50cae5c44f`](https://github.com/nodejs/node/commit/50cae5c44f)] - **test**: remove assert message (Joe Henry)
- [[`e48c8b3b6c`](https://github.com/nodejs/node/commit/e48c8b3b6c)] - **test**: replace fixtureDir with fixtures.path (matthewreed26) [#15943](https://github.com/nodejs/node/pull/15943)
- [[`572492a088`](https://github.com/nodejs/node/commit/572492a088)] - **test**: clarify assert messages in crypto tests (cpandrews8) [#16019](https://github.com/nodejs/node/pull/16019)
- [[`d962ee35de`](https://github.com/nodejs/node/commit/d962ee35de)] - **test**: use common.fixtures module for file path (Adil L) [#16017](https://github.com/nodejs/node/pull/16017)
- [[`8f367bb1a6`](https://github.com/nodejs/node/commit/8f367bb1a6)] - **test**: fix race condition in addon test (Kinnan Kwok) [#16037](https://github.com/nodejs/node/pull/16037)
- [[`5d63c1033d`](https://github.com/nodejs/node/commit/5d63c1033d)] - **test**: create benchmark test for misc and module (Charles T Wall III) [#16044](https://github.com/nodejs/node/pull/16044)
- [[`e9f6a624db`](https://github.com/nodejs/node/commit/e9f6a624db)] - **test**: include expected result in error messages (Chowdhurian) [#16039](https://github.com/nodejs/node/pull/16039)
- [[`f8496553df`](https://github.com/nodejs/node/commit/f8496553df)] - **test**: use fixtures module (Maurice Hayward) [#16034](https://github.com/nodejs/node/pull/16034)
- [[`e4f0483fb9`](https://github.com/nodejs/node/commit/e4f0483fb9)] - **test**: replace fixturesDir with fixtures module (tabulatedreams) [#16036](https://github.com/nodejs/node/pull/16036)
- [[`387b0b8b10`](https://github.com/nodejs/node/commit/387b0b8b10)] - **test**: replace concat with template literals (gitHubTracey) [#15885](https://github.com/nodejs/node/pull/15885)
- [[`6e25b081b6`](https://github.com/nodejs/node/commit/6e25b081b6)] - **test**: clarify assertion failure (ryshep111) [#15889](https://github.com/nodejs/node/pull/15889)
- [[`6a44442b5f`](https://github.com/nodejs/node/commit/6a44442b5f)] - **test**: use fixtures.readKey (Robin Lungwitz) [#15892](https://github.com/nodejs/node/pull/15892)
- [[`f7ab12685e`](https://github.com/nodejs/node/commit/f7ab12685e)] - **test**: replace fixturesDir with fixtures module (Ivan Etchart) [#15893](https://github.com/nodejs/node/pull/15893)
- [[`36a0d3f0b1`](https://github.com/nodejs/node/commit/36a0d3f0b1)] - **test**: cleanup test-buffer-sharedarraybuffer (Rafal Leszczynski) [#15896](https://github.com/nodejs/node/pull/15896)
- [[`bbbf58e951`](https://github.com/nodejs/node/commit/bbbf58e951)] - **test**: change fixturesDir to fixtures.path (Savio Lucena) [#15902](https://github.com/nodejs/node/pull/15902)
- [[`dba620b178`](https://github.com/nodejs/node/commit/dba620b178)] - **test**: changed fixtures require (creisle) [#15899](https://github.com/nodejs/node/pull/15899)
- [[`ccecaca056`](https://github.com/nodejs/node/commit/ccecaca056)] - **test**: replaced fixturesDir with fixtures module (Alex McKenzie) [#15908](https://github.com/nodejs/node/pull/15908)
- [[`547c284335`](https://github.com/nodejs/node/commit/547c284335)] - **test**: replace string concatenation with templates (Colin Leong) [#15903](https://github.com/nodejs/node/pull/15903)
- [[`a625d82c78`](https://github.com/nodejs/node/commit/a625d82c78)] - **test**: updated error message (Emily Platzer) [#15906](https://github.com/nodejs/node/pull/15906)
- [[`3b682aa857`](https://github.com/nodejs/node/commit/3b682aa857)] - **test**: assert.strictEqual using template literals (jmcgui05) [#15944](https://github.com/nodejs/node/pull/15944)
- [[`329d22fb32`](https://github.com/nodejs/node/commit/329d22fb32)] - **test**: use common.fixtures in tls test (Ben Michel) [#15965](https://github.com/nodejs/node/pull/15965)
- [[`9f9bd38aa0`](https://github.com/nodejs/node/commit/9f9bd38aa0)] - **test**: replace error msg w/ template literal (Sushil Tailor) [#15910](https://github.com/nodejs/node/pull/15910)
- [[`181d4bf5b3`](https://github.com/nodejs/node/commit/181d4bf5b3)] - **test**: add NODE_UNIQUE_ID value to err message (Daniele Lisi) [#15914](https://github.com/nodejs/node/pull/15914)
- [[`2d25a3b5f8`](https://github.com/nodejs/node/commit/2d25a3b5f8)] - **test**: replace string concatenation with template (Rob Paton) [#15915](https://github.com/nodejs/node/pull/15915)
- [[`802f99ba27`](https://github.com/nodejs/node/commit/802f99ba27)] - **test**: change concatenation to template literal (nodexpertsdev) [#15916](https://github.com/nodejs/node/pull/15916)
- [[`c5c51ebae4`](https://github.com/nodejs/node/commit/c5c51ebae4)] - **test**: improve asset msg in test (Gene Wu) [#15918](https://github.com/nodejs/node/pull/15918)
- [[`f201edc4be`](https://github.com/nodejs/node/commit/f201edc4be)] - **test**: replace fixturesDir with fixtures module (penDerGraft) [#15919](https://github.com/nodejs/node/pull/15919)
- [[`906f2b14ca`](https://github.com/nodejs/node/commit/906f2b14ca)] - **test**: remove message from asserts (Justin Lee) [#15920](https://github.com/nodejs/node/pull/15920)
- [[`a14b447bbb`](https://github.com/nodejs/node/commit/a14b447bbb)] - **test**: improve an error message (Pavel Pomerantsev) [#15921](https://github.com/nodejs/node/pull/15921)
- [[`27e0532eab`](https://github.com/nodejs/node/commit/27e0532eab)] - **test**: added string_decoder.js a parallel test (Uttam Pawar) [#15923](https://github.com/nodejs/node/pull/15923)
- [[`2ea339a346`](https://github.com/nodejs/node/commit/2ea339a346)] - **test**: use fixtures module instead of common (Joe Grace) [#15925](https://github.com/nodejs/node/pull/15925)
- [[`5bfc4f5e5a`](https://github.com/nodejs/node/commit/5bfc4f5e5a)] - **test**: replace fixtureDir with fixtures module (Charlie Duong) [#15823](https://github.com/nodejs/node/pull/15823)
- [[`7d8a808959`](https://github.com/nodejs/node/commit/7d8a808959)] - **test**: replaced fixturesDir with fixtures module (Alex McKenzie) [#15881](https://github.com/nodejs/node/pull/15881)
- [[`d3272c487a`](https://github.com/nodejs/node/commit/d3272c487a)] - **test**: use common.fixtures module (Christopher Choi) [#15891](https://github.com/nodejs/node/pull/15891)
- [[`e7c55bf77d`](https://github.com/nodejs/node/commit/e7c55bf77d)] - **test**: replaced literals in errors with templates (Paul Milham) [#15911](https://github.com/nodejs/node/pull/15911)
- [[`205927fe6b`](https://github.com/nodejs/node/commit/205927fe6b)] - **test**: display better error message for assertion (Russell Dempsey) [#15883](https://github.com/nodejs/node/pull/15883)
- [[`768060d5e7`](https://github.com/nodejs/node/commit/768060d5e7)] - **test**: changed buffer-zero output (heeeunkimmm) [#15926](https://github.com/nodejs/node/pull/15926)
- [[`0286da0992`](https://github.com/nodejs/node/commit/0286da0992)] - **test**: replaced fixturesDir with fixtures module (Alex McKenzie) [#15927](https://github.com/nodejs/node/pull/15927)
- [[`84dd5783c6`](https://github.com/nodejs/node/commit/84dd5783c6)] - **test**: remove literal error messages (Faisal Yaqoob) [#15928](https://github.com/nodejs/node/pull/15928)
- [[`633772a90c`](https://github.com/nodejs/node/commit/633772a90c)] - **test**: refactor test to use the fixtures module (Daniel Kostro) [#15934](https://github.com/nodejs/node/pull/15934)
- [[`dd23140015`](https://github.com/nodejs/node/commit/dd23140015)] - **test**: replace fixturesDir with fixtures module (Greg Matthews) [#15932](https://github.com/nodejs/node/pull/15932)
- [[`5b29e5a1f3`](https://github.com/nodejs/node/commit/5b29e5a1f3)] - **test**: modify test messages to template literals (Alice Tsui) [#15931](https://github.com/nodejs/node/pull/15931)
- [[`7df8e0b0db`](https://github.com/nodejs/node/commit/7df8e0b0db)] - **test**: replace common.fixturesDir with fixture (BradLarson) [#15940](https://github.com/nodejs/node/pull/15940)
- [[`26536e46ed`](https://github.com/nodejs/node/commit/26536e46ed)] - **test**: changes to use template literal (joanne-jjb) [#15937](https://github.com/nodejs/node/pull/15937)
- [[`e12dc40c2f`](https://github.com/nodejs/node/commit/e12dc40c2f)] - **test**: replace fixturesDir with fixtures (Mujtaba Al-Tameemi) [#15949](https://github.com/nodejs/node/pull/15949)
- [[`30631528e4`](https://github.com/nodejs/node/commit/30631528e4)] - **test**: remove common.fixturesDir (Luis Del Águila) [#15950](https://github.com/nodejs/node/pull/15950)
- [[`9059b09a34`](https://github.com/nodejs/node/commit/9059b09a34)] - **test**: remove template literal (Emily Ford) [#15953](https://github.com/nodejs/node/pull/15953)
- [[`ba9aa46b6d`](https://github.com/nodejs/node/commit/ba9aa46b6d)] - **test**: removed string from assert message arg (dpaulino) [#15954](https://github.com/nodejs/node/pull/15954)
- [[`3fd4f62f35`](https://github.com/nodejs/node/commit/3fd4f62f35)] - **test**: replace literal with template string (Brant Barger) [#15957](https://github.com/nodejs/node/pull/15957)
- [[`a224760639`](https://github.com/nodejs/node/commit/a224760639)] - **test**: upgrade from fixturesDir to fixtures.path (jacjam) [#15960](https://github.com/nodejs/node/pull/15960)
- [[`b564fe2231`](https://github.com/nodejs/node/commit/b564fe2231)] - **test**: use defaultHistoryPath instead of path.join (Chris Budy) [#15969](https://github.com/nodejs/node/pull/15969)
- [[`ece6cd1f9e`](https://github.com/nodejs/node/commit/ece6cd1f9e)] - **test**: replace fixturesDir with fixtures module (BinarySo1o) [#15961](https://github.com/nodejs/node/pull/15961)
- [[`d1bb608b45`](https://github.com/nodejs/node/commit/d1bb608b45)] - **test**: replaced fixturesDir with common.fixtures (jopann) [#15971](https://github.com/nodejs/node/pull/15971)
- [[`adceca44b2`](https://github.com/nodejs/node/commit/adceca44b2)] - **test**: improve assert messages (Eric Pemberton) [#15972](https://github.com/nodejs/node/pull/15972)
- [[`ab046beeeb`](https://github.com/nodejs/node/commit/ab046beeeb)] - **test**: replacing assert message with template (Barry Tam) [#15974](https://github.com/nodejs/node/pull/15974)
- [[`75ab6c00a9`](https://github.com/nodejs/node/commit/75ab6c00a9)] - **test**: use common.fixtures module in test-preload (Laura Cabrera) [#15975](https://github.com/nodejs/node/pull/15975)
- [[`530b62fc0d`](https://github.com/nodejs/node/commit/530b62fc0d)] - **test**: more informative test failure messages (Alec Ferguson) [#15977](https://github.com/nodejs/node/pull/15977)
- [[`4a9e3312fd`](https://github.com/nodejs/node/commit/4a9e3312fd)] - **test**: alter assert.strictEqual to default message (Luke Greenleaf) [#15978](https://github.com/nodejs/node/pull/15978)
- [[`e9d31bc6e4`](https://github.com/nodejs/node/commit/e9d31bc6e4)] - **test**: replaced common.fixturesDir with readKey (Sean Cox) [#15933](https://github.com/nodejs/node/pull/15933)
- [[`054f8f6683`](https://github.com/nodejs/node/commit/054f8f6683)] - **test**: replace fixturesDir in tls-env-bad-extra-ca (Annie Weng) [#15813](https://github.com/nodejs/node/pull/15813)
- [[`d410f74e23`](https://github.com/nodejs/node/commit/d410f74e23)] - **test**: use common.fixtures in checkServerIdentity (Emily Marigold Klassen) [#15951](https://github.com/nodejs/node/pull/15951)
- [[`145d1db923`](https://github.com/nodejs/node/commit/145d1db923)] - **test**: replaced common.fixturesDir with readKey (rhalldearn) [#15952](https://github.com/nodejs/node/pull/15952)
- [[`9592a486e0`](https://github.com/nodejs/node/commit/9592a486e0)] - **test**: use fixtures.path for cmd string building (John Miller) [#15982](https://github.com/nodejs/node/pull/15982)
- [[`4594315eae`](https://github.com/nodejs/node/commit/4594315eae)] - **test**: replace fixturesDir with fixtures.readKey (Thomas Schorn) [#15948](https://github.com/nodejs/node/pull/15948)
- [[`73231d95af`](https://github.com/nodejs/node/commit/73231d95af)] - **test**: replace common.fixturesDir with readKey (ashleyraymaceli) [#15946](https://github.com/nodejs/node/pull/15946)
- [[`73a41cf653`](https://github.com/nodejs/node/commit/73a41cf653)] - **test**: replace common.fixturesDir with fixtures. (Sam Skjonsberg) [#15802](https://github.com/nodejs/node/pull/15802)
- [[`de198a9dc0`](https://github.com/nodejs/node/commit/de198a9dc0)] - **test**: update test to use fixtures module (gbugaisky) [#15955](https://github.com/nodejs/node/pull/15955)
- [[`7ca02b0f0c`](https://github.com/nodejs/node/commit/7ca02b0f0c)] - **test**: replace fixturesDir with common.fixtures (rachelnicole) [#16051](https://github.com/nodejs/node/pull/16051)
- [[`1d7e1c0f18`](https://github.com/nodejs/node/commit/1d7e1c0f18)] - **test**: remove messages in assert.strictEqual (Saeed H) [#16014](https://github.com/nodejs/node/pull/16014)
- [[`8ea96488bc`](https://github.com/nodejs/node/commit/8ea96488bc)] - **test**: update fixturesDir to fixtures.readKey (bitandbang) [#16016](https://github.com/nodejs/node/pull/16016)
- [[`b766d27197`](https://github.com/nodejs/node/commit/b766d27197)] - **test**: replace fixturesDir with common.fixtures (Paul Berry) [#15973](https://github.com/nodejs/node/pull/15973)
- [[`c47ebe20dd`](https://github.com/nodejs/node/commit/c47ebe20dd)] - **test**: replace fixturesDir with common.fixtures (Pooya Paridel) [#15837](https://github.com/nodejs/node/pull/15837)
- [[`516fda6c64`](https://github.com/nodejs/node/commit/516fda6c64)] - **test**: update 'fixturesDir' refs in a test file (James M. Greene) [#15824](https://github.com/nodejs/node/pull/15824)
- [[`e1a1d2e13d`](https://github.com/nodejs/node/commit/e1a1d2e13d)] - **test**: replace common.fixturesDir in test-exception (Chowdhurian) [#15964](https://github.com/nodejs/node/pull/15964)
- [[`47169216d8`](https://github.com/nodejs/node/commit/47169216d8)] - **test**: use fixtures.readKey in https-agent test (Greg Byram) [#15913](https://github.com/nodejs/node/pull/15913)
- [[`f39c7926cf`](https://github.com/nodejs/node/commit/f39c7926cf)] - **test**: http2 client destroy tests in one file (Trivikram Kamat) [#15749](https://github.com/nodejs/node/pull/15749)
- [[`21a8a820a3`](https://github.com/nodejs/node/commit/21a8a820a3)] - **test**: add common.fixtures to https-req-split (Bruce Fletcher) [#15801](https://github.com/nodejs/node/pull/15801)
- [[`bd49ada52a`](https://github.com/nodejs/node/commit/bd49ada52a)] - **test**: http2 stored settings returned when present (Trivikram Kamat) [#15751](https://github.com/nodejs/node/pull/15751)
- [[`1e79a06ac6`](https://github.com/nodejs/node/commit/1e79a06ac6)] - **test**: fix flaky async-hooks/test-tlswrap (Rich Trott) [#15744](https://github.com/nodejs/node/pull/15744)
- [[`22ea3a8cd2`](https://github.com/nodejs/node/commit/22ea3a8cd2)] - **test**: remove `common.PORT` from test-tlswrap (Rich Trott) [#15742](https://github.com/nodejs/node/pull/15742)
- [[`3f1210992c`](https://github.com/nodejs/node/commit/3f1210992c)] - **test**: refactor test-internal-errors (Rich Trott) [#15721](https://github.com/nodejs/node/pull/15721)
- [[`995948a1f9`](https://github.com/nodejs/node/commit/995948a1f9)] - **test**: skip test if host is too slow (Rich Trott) [#15688](https://github.com/nodejs/node/pull/15688)
- [[`af304b21c7`](https://github.com/nodejs/node/commit/af304b21c7)] - **test**: mark test-bindings and test-debug-end flaky (João Reis) [#15747](https://github.com/nodejs/node/pull/15747)
- [[`1582260067`](https://github.com/nodejs/node/commit/1582260067)] - **test**: increase test coverage for os.js (kuroljov) [#14098](https://github.com/nodejs/node/pull/14098)
- [[`88f69d3ec3`](https://github.com/nodejs/node/commit/88f69d3ec3)] - **test**: check that this != new.target in addon (Ben Noordhuis) [#15681](https://github.com/nodejs/node/pull/15681)
- [[`7842f63069`](https://github.com/nodejs/node/commit/7842f63069)] - **test**: Http2Stream destroy server before shutdown (Trivikram Kamat) [#15597](https://github.com/nodejs/node/pull/15597)
- [[`41539381fe`](https://github.com/nodejs/node/commit/41539381fe)] - **test**: http2Stream redundant shutdown and single cb (Trivikram Kamat) [#15612](https://github.com/nodejs/node/pull/15612)
- [[`803d5bbf50`](https://github.com/nodejs/node/commit/803d5bbf50)] - **test**: update es-module.status prefix (Jack Horton) [#15690](https://github.com/nodejs/node/pull/15690)
- [[`bd7b216936`](https://github.com/nodejs/node/commit/bd7b216936)] - **test**: fix test-https-writable-true-after-close (Rich Trott) [#15705](https://github.com/nodejs/node/pull/15705)
- [[`0aea258f0e`](https://github.com/nodejs/node/commit/0aea258f0e)] - **test**: fix http-writable-true-after-close flakyness (Matteo Collina) [#15520](https://github.com/nodejs/node/pull/15520)
- [[`bbdd93f34f`](https://github.com/nodejs/node/commit/bbdd93f34f)] - **test**: skip test when checking async_hooks (Trevor Norris) [#14208](https://github.com/nodejs/node/pull/14208)
- [[`98fc665940`](https://github.com/nodejs/node/commit/98fc665940)] - **test**: print resource stack on error (Trevor Norris) [#14208](https://github.com/nodejs/node/pull/14208)
- [[`ab7448e0d5`](https://github.com/nodejs/node/commit/ab7448e0d5)] - **tools**: replace concatenation with string templates (Ethan Arrowood) [#15858](https://github.com/nodejs/node/pull/15858)
- [[`0e707f3f9e`](https://github.com/nodejs/node/commit/0e707f3f9e)] - **tools**: replace concat with template literals (Minya Liang) [#16046](https://github.com/nodejs/node/pull/16046)
- [[`ca5f4f0ed3`](https://github.com/nodejs/node/commit/ca5f4f0ed3)] - **tools**: use more template literals (Govee91) [#15942](https://github.com/nodejs/node/pull/15942)
- [[`94c6296d83`](https://github.com/nodejs/node/commit/94c6296d83)] - **tools**: use template literals (Sarah Meyer) [#15956](https://github.com/nodejs/node/pull/15956)
- [[`eebb2d775a`](https://github.com/nodejs/node/commit/eebb2d775a)] - **(SEMVER-MINOR)** **tools, build**: refactor macOS installer (JP Wesselink) [#15179](https://github.com/nodejs/node/pull/15179)
- [[`f68f572d7f`](https://github.com/nodejs/node/commit/f68f572d7f)] - **tty**: require readline at top of file (Bryan English) [#15647](https://github.com/nodejs/node/pull/15647)
- [[`d181147b2c`](https://github.com/nodejs/node/commit/d181147b2c)] - **url**: const-ify APIs, and pass URL by ref (Sam Roberts) [#15615](https://github.com/nodejs/node/pull/15615)
- [[`1cc4245bfb`](https://github.com/nodejs/node/commit/1cc4245bfb)] - **url**: fix remaining calculation (Rimas Misevičius) [#15637](https://github.com/nodejs/node/pull/15637)
- [[`34b4180d7d`](https://github.com/nodejs/node/commit/34b4180d7d)] - **url**: change variable name to be more descriptive (Yang-Kichang) [#15551](https://github.com/nodejs/node/pull/15551)
- [[`58c68c2fcb`](https://github.com/nodejs/node/commit/58c68c2fcb)] - **util**: use faster -0 check (Brian White) [#15726](https://github.com/nodejs/node/pull/15726)
- [[`d2e1545406`](https://github.com/nodejs/node/commit/d2e1545406)] - **(SEMVER-MINOR)** **util**: deprecate obj.inspect for custom inspection (Rich Trott) [#15631](https://github.com/nodejs/node/pull/15631)

Windows 32-bit Installer: https://nodejs.org/dist/v8.7.0/node-v8.7.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v8.7.0/node-v8.7.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v8.7.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v8.7.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v8.7.0/node-v8.7.0.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v8.7.0/node-v8.7.0-darwin-x64.tar.gz \
Linux 32-bit Binary: https://nodejs.org/dist/v8.7.0/node-v8.7.0-linux-x86.tar.xz \
Linux 64-bit Binary: https://nodejs.org/dist/v8.7.0/node-v8.7.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v8.7.0/node-v8.7.0-linux-ppc64le.tar.xz \
Linux PPC BE 64-bit Binary: https://nodejs.org/dist/v8.7.0/node-v8.7.0-linux-ppc64.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v8.7.0/node-v8.7.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v8.7.0/node-v8.7.0-aix-ppc64.tar.gz \
SmartOS 32-bit Binary: https://nodejs.org/dist/v8.7.0/node-v8.7.0-sunos-x86.tar.xz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v8.7.0/node-v8.7.0-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v8.7.0/node-v8.7.0-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v8.7.0/node-v8.7.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v8.7.0/node-v8.7.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v8.7.0/node-v8.7.0.tar.gz \
Other release files: https://nodejs.org/dist/v8.7.0/ \
Documentation: https://nodejs.org/docs/v8.7.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

b5b39887d41c12c5dbecddb9f7c9a40887776bcd7bee598ce1f5a6fd2373e542  node-v8.7.0-aix-ppc64.tar.gz
5e59798c1deafd671a35ef4dcdb9b97ce98f9255a056832dc98d454613e9ea08  node-v8.7.0-darwin-x64.tar.gz
ce5a8bf996816086c404315d928057b3807fc1d8133ece9758b3bef674adfeeb  node-v8.7.0-darwin-x64.tar.xz
d2316d757cae4ee1ffc2955946016f4b35442a516b196645135bc8797947845b  node-v8.7.0-headers.tar.gz
5929caeabaa1839dae56dd9d1d39f0358f6173cdd4b6b213d09d0d20b8a3c423  node-v8.7.0-headers.tar.xz
e60bd4b3082e2f75d16bd23654f21e2c4652e180273d7e9c836528c26dee2e40  node-v8.7.0-linux-arm64.tar.gz
5a1a1907fbb6d90667ce70ad42602534f6cc6eda873d1c50a3259349aee73418  node-v8.7.0-linux-arm64.tar.xz
5ed53588bd30365ee9929cafeffb02143e4569997835d90b2d66084bccbf746b  node-v8.7.0-linux-armv6l.tar.gz
4fd3121fd3fa4d32a1a128fec9bd731d9ffd3bd3585cd022782e39afece6dc8c  node-v8.7.0-linux-armv6l.tar.xz
3cb2447cec4910faeccf0762e9429d595fd3b0dd029051dc850244875d959faf  node-v8.7.0-linux-armv7l.tar.gz
66244596d066e651799476a25e39530f078cf95c542d6743d8e2ba3bd7f60c12  node-v8.7.0-linux-armv7l.tar.xz
fdd3d6befe817f549c58b392f4fd858c75576f5ae9184a16e278a38128b0fa0d  node-v8.7.0-linux-ppc64le.tar.gz
04eb4200bd0ee9e4cc45b69adbb3ae91a8ba9cfaa0b4536257a08ffe4ec38610  node-v8.7.0-linux-ppc64le.tar.xz
327ec3ff9e99ad37450385166eead9737f7cad0d5aaa0ea700bb447a4613ead9  node-v8.7.0-linux-ppc64.tar.gz
f138da2337ac342c48f3278d62f24a2f7658d9ce2e7a3ac54fd0952dda842c8c  node-v8.7.0-linux-ppc64.tar.xz
f740079422834e0bbc799ff31701b049d2f91f5b2e6ae52d81f0375ddeea5a4d  node-v8.7.0-linux-s390x.tar.gz
a8569f4293467da1b2b0cadb71cc256e84359dbcba194469d8fc0215c902b5d9  node-v8.7.0-linux-s390x.tar.xz
115c7bd133170fd7a1bf408b2e293021e4b5a80a66a4962829ce5d362ce43762  node-v8.7.0-linux-x64.tar.gz
9d6f649576cac74ef0b6634af8265156370cf8fdf3676f03e867347d3207675d  node-v8.7.0-linux-x64.tar.xz
939d88df6f179050fcf93d417d2c094195eede60cae43897fe713e624d33b486  node-v8.7.0-linux-x86.tar.gz
c991f434d084cd16a2e29b627eb5bd4f7f7a222d341c8050582d3ef47ae83d85  node-v8.7.0-linux-x86.tar.xz
f99883b98ef6f8947c2e29588da3fde98159262bb57d75a97eae4d0fdcbb6f79  node-v8.7.0.pkg
89498bedfc289b2910d09c8b31136662cd9e7d939fddd4c0e3487b8c210faba8  node-v8.7.0-sunos-x64.tar.gz
8cecd63816e529e660b7b9178d3a273748208804e60a99887d3d0ac0534faed9  node-v8.7.0-sunos-x64.tar.xz
a476c66dfd1f9ef9379db79a85bc11ce8e1db35c32e6339c049a3f9005b25c9d  node-v8.7.0-sunos-x86.tar.gz
e356d4e4d293dcd7df14bfdec1314936cbf3d71174c9180b565e24c66f669969  node-v8.7.0-sunos-x86.tar.xz
c7184526e5504fa2a91aff1aacd7c344451da539d73775b6d9ea8efe948d0fa8  node-v8.7.0.tar.gz
5a17d08c68ee7c1e748fd32534edda766cb57c13ed98e693f3075bc9d9a0b59a  node-v8.7.0.tar.xz
27a5a69c7ffae89a3846b778f970643cc53e45e53e16801921aa4ea69e924695  node-v8.7.0-win-x64.7z
e95be435674e82ea7133c3268cb70044eabde2d0aef28b2a3df5c7d8d23cadcc  node-v8.7.0-win-x64.zip
f0bd40e04415750b1289403a37eb15d86da77e99132fe6a7c637c4c620c41ef1  node-v8.7.0-win-x86.7z
859fcf71a50eac7f8a00d4357725a2d0de01a7bbd7835eafa634051a5627f5a7  node-v8.7.0-win-x86.zip
ffd191fbdedb14d2f81e5259e63354ef191cfe845f817004b75a335c4ac54acc  node-v8.7.0-x64.msi
451540e5b9926b059c3a38115fba9e1112cb040866188fb3fa10b40504dae782  node-v8.7.0-x86.msi
520dfde7f107af367dab5a0239c07c46daeff8939f06cc6e5edc5661e4112035  win-x64/node.exe
e9a9079efe223ad9fe6582c81cd3593b820684812aedd65050026830da6b3129  win-x64/node.lib
7ca6b4688e051ea7a80989db29ec7327783f19ebc55e826e4d9eabf000378dfa  win-x64/node_pdb.7z
ad4c5de3c7d1db4a99eca98729d9fbfc40a5cee4e253beb85033a326f4ab5a01  win-x64/node_pdb.zip
eb7589079591e8c4ca660b8cdbefc4b341be38cd538416c768a08d55e8df0e6b  win-x86/node.exe
d72cb4137048edf243c1fa971578a698c3ecbf69768efd3aaa4ef30e9be8cffc  win-x86/node.lib
e8b6b4291ac0873d40c9828b59f29e567166de9c5854b935311483e5692fe2a2  win-x86/node_pdb.7z
66db1f5de94850c8ca8f353f95238cb7742f6e567fefd579bc827a2a6c36d403  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQEzBAEBCAAdFiEEDv/hvO/ZyE49CYFSkzsB9AtcqUYFAlnehCcACgkQkzsB9Atc
qUbEZgf8CZen4tw35vvse6ViobPeaT8N1OMPlBA3u9PPk/wKwOBxpKB8YlxkzLDm
5hi0hIdzfw7IXGIIjq8Rl67rBcF2dCF960sxWxbqCUpyIPcsO2pOWurhXvGvJc+J
730+ixLA3hARDQxLH8ccAp/LY5DhXD2qmtkikwRuTteuYHEaHcIhtDF5lvfjH5Ud
LCnVM9WeB1B+/XrrDULzpJYDQoQszymiq30GyUZ84hD0H3qcBpFGv7WxneWHULKn
UazjWzNMCqq671dEjKc+csPES1PNb3cw504sZuzRo2PFc3nS1Ngz+cyCZC3G/Urh
Lwz952H6uhxEexQcyL/ggdwfLHrn/Q==
=jfal
-----END PGP SIGNATURE-----

```
