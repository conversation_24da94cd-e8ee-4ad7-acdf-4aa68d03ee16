---
date: '2021-05-04T15:15:04.671Z'
category: release
title: Node v16.1.0 (Current)
layout: blog-post
author: <PERSON><PERSON><PERSON>
---

### Notable Changes

- [[`8a90f55a05`](https://github.com/nodejs/node/commit/8a90f55a05)] - **(SEMVER-MINOR)** **fs**: allow no-params fsPromises fileHandle read (<PERSON><PERSON><PERSON>) [#38287](https://github.com/nodejs/node/pull/38287)

### Commits

- [[`28e16488cf`](https://github.com/nodejs/node/commit/28e16488cf)] - **async_hooks,doc**: replace process.stdout.fd with 1 (<PERSON><PERSON>) [#38382](https://github.com/nodejs/node/pull/38382)
- [[`cbab7ec6e5`](https://github.com/nodejs/node/commit/cbab7ec6e5)] - **benchmark**: avoid using `console.log()` (<PERSON>) [#38370](https://github.com/nodejs/node/pull/38370)
- [[`ba15b20062`](https://github.com/nodejs/node/commit/ba15b20062)] - **benchmark**: use `process.hrtime.bigint()` (Antoine du Hamel) [#38369](https://github.com/nodejs/node/pull/38369)
- [[`bc6e719884`](https://github.com/nodejs/node/commit/bc6e719884)] - **bootstrap**: freeze more intrinsics (Antoine du Hamel) [#38217](https://github.com/nodejs/node/pull/38217)
- [[`29faf0f12e`](https://github.com/nodejs/node/commit/29faf0f12e)] - **build**: fix label-pr workflow (Michaël Zasso) [#38399](https://github.com/nodejs/node/pull/38399)
- [[`b5d669a6ea`](https://github.com/nodejs/node/commit/b5d669a6ea)] - **build**: label PRs with GitHub Action instead of nodejs-github-bot (Phillip Johnsen) [#38301](https://github.com/nodejs/node/pull/38301)
- [[`195f679331`](https://github.com/nodejs/node/commit/195f679331)] - **crypto**: don't crash with some selfsigned certs (Nils Dralle) [#37990](https://github.com/nodejs/node/pull/37990)
- [[`4b073b0beb`](https://github.com/nodejs/node/commit/4b073b0beb)] - **crypto**: fix generateKeyPair type checks (Nitzan Uziely) [#38364](https://github.com/nodejs/node/pull/38364)
- [[`c1d9b5b386`](https://github.com/nodejs/node/commit/c1d9b5b386)] - **crypto**: fix scrypt keylen validation (Antoine du Hamel) [#38385](https://github.com/nodejs/node/pull/38385)
- [[`7354479ad5`](https://github.com/nodejs/node/commit/7354479ad5)] - **crypto**: fix DiffieHellman `generator` validation (eladkeyshawn) [#38311](https://github.com/nodejs/node/pull/38311)
- [[`0e446d6048`](https://github.com/nodejs/node/commit/0e446d6048)] - **debugger**: enable linter on `internal/inspector/inspect_client` (Antoine du Hamel) [#38417](https://github.com/nodejs/node/pull/38417)
- [[`9f0e80aa4d`](https://github.com/nodejs/node/commit/9f0e80aa4d)] - **debugger**: refactor `internal/inspector/_inspect` to use more primordials (Antoine du Hamel) [#38406](https://github.com/nodejs/node/pull/38406)
- [[`a0c566f85a`](https://github.com/nodejs/node/commit/a0c566f85a)] - **debugger**: apply automatic lint fixes for inspect_repl.js (Rich Trott) [#38411](https://github.com/nodejs/node/pull/38411)
- [[`b884ea739b`](https://github.com/nodejs/node/commit/b884ea739b)] - **debugger**: apply automatic lint fixes for \_inspect.js (Rich Trott) [#38411](https://github.com/nodejs/node/pull/38411)
- [[`f946aa0360`](https://github.com/nodejs/node/commit/f946aa0360)] - **debugger**: remove unused function argument (Rich Trott) [#38400](https://github.com/nodejs/node/pull/38400)
- [[`203a9689a3`](https://github.com/nodejs/node/commit/203a9689a3)] - **debugger**: align message with Node.js standard (Rich Trott) [#38400](https://github.com/nodejs/node/pull/38400)
- [[`ef617dcbb0`](https://github.com/nodejs/node/commit/ef617dcbb0)] - **debugger**: add usage example for `--port` (Rafael Gonzaga) [#38400](https://github.com/nodejs/node/pull/38400)
- [[`37b5ce2d5a`](https://github.com/nodejs/node/commit/37b5ce2d5a)] - **debugger**: fix race condition/deadlock on initialization (Rich Trott) [#38161](https://github.com/nodejs/node/pull/38161)
- [[`2a6203d155`](https://github.com/nodejs/node/commit/2a6203d155)] - **debugger**: replace internal use of deprecated API (Rich Trott) [#38161](https://github.com/nodejs/node/pull/38161)
- [[`6fff9fff97`](https://github.com/nodejs/node/commit/6fff9fff97)] - **debugger**: allow longer time to connect (Rich Trott) [#38161](https://github.com/nodejs/node/pull/38161)
- [[`def85daace`](https://github.com/nodejs/node/commit/def85daace)] - **debugger**: accommodate line chunking in Windows (Rich Trott) [#38161](https://github.com/nodejs/node/pull/38161)
- [[`07361e6b77`](https://github.com/nodejs/node/commit/07361e6b77)] - **debugger**: fix inspect restart on Windows (Rich Trott) [#38161](https://github.com/nodejs/node/pull/38161)
- [[`d65615e119`](https://github.com/nodejs/node/commit/d65615e119)] - **debugger**: remove unused code (Rich Trott) [#38161](https://github.com/nodejs/node/pull/38161)
- [[`62b03bc4f6`](https://github.com/nodejs/node/commit/62b03bc4f6)] - **debugger**: move node-inspect to internal library (Rich Trott) [#38161](https://github.com/nodejs/node/pull/38161)
- [[`e3b75cb5aa`](https://github.com/nodejs/node/commit/e3b75cb5aa)] - **deps**: V8: cherry-pick fd75c97d3f56 (Michaël Zasso) [#38455](https://github.com/nodejs/node/pull/38455)
- [[`aabddfbeb5`](https://github.com/nodejs/node/commit/aabddfbeb5)] - **deps**: upgrade npm to 7.11.2 (Ruy Adorno) [#38475](https://github.com/nodejs/node/pull/38475)
- [[`7b9fb92d51`](https://github.com/nodejs/node/commit/7b9fb92d51)] - **deps**: update to cjs-module-lexer@1.2.1 (Guy Bedford) [#38450](https://github.com/nodejs/node/pull/38450)
- [[`47626c52a3`](https://github.com/nodejs/node/commit/47626c52a3)] - **deps**: patch V8 to 9.0.257.24 (Michaël Zasso) [#38423](https://github.com/nodejs/node/pull/38423)
- [[`f455e08621`](https://github.com/nodejs/node/commit/f455e08621)] - **deps**: patch V8 to 9.0.257.21 (Michaël Zasso) [#38333](https://github.com/nodejs/node/pull/38333)
- [[`dd61a26d8c`](https://github.com/nodejs/node/commit/dd61a26d8c)] - **deps**: update llhttp to 6.0.1 (Fedor Indutny) [#38359](https://github.com/nodejs/node/pull/38359)
- [[`05f41cdbcc`](https://github.com/nodejs/node/commit/05f41cdbcc)] - **deps**: patch V8 to 9.0.257.19 (Michaël Zasso) [#38270](https://github.com/nodejs/node/pull/38270)
- [[`224faa0a05`](https://github.com/nodejs/node/commit/224faa0a05)] - **_Revert_** "**doc**: os.uptime() temporary bug notice" (Michaël Zasso) [#38494](https://github.com/nodejs/node/pull/38494)
- [[`3b0480dde8`](https://github.com/nodejs/node/commit/3b0480dde8)] - **doc**: document `'secureConnect'` event limitation (James M Snell) [#38447](https://github.com/nodejs/node/pull/38447)
- [[`92586046ec`](https://github.com/nodejs/node/commit/92586046ec)] - **doc**: fix outdated util inspect documentation and layout example (Ruben Bridgewater) [#37079](https://github.com/nodejs/node/pull/37079)
- [[`13de4cf1ca`](https://github.com/nodejs/node/commit/13de4cf1ca)] - **doc**: mark Node.js 10 as End-of-Life (Richard Lau) [#38482](https://github.com/nodejs/node/pull/38482)
- [[`3cbfde1f25`](https://github.com/nodejs/node/commit/3cbfde1f25)] - **doc**: mark querystring api as legacy (James M Snell) [#38436](https://github.com/nodejs/node/pull/38436)
- [[`a5929c2487`](https://github.com/nodejs/node/commit/a5929c2487)] - **doc**: update node-api support matrix (Michael Dawson) [#38424](https://github.com/nodejs/node/pull/38424)
- [[`f08650cefe`](https://github.com/nodejs/node/commit/f08650cefe)] - **doc**: add arguments for stream event of Http2Server and Http2SecureServer (Qingyu Deng) [#37892](https://github.com/nodejs/node/pull/37892)
- [[`2d59273bed`](https://github.com/nodejs/node/commit/2d59273bed)] - **doc**: indicate that abort tests do not generate core files (Rich Trott) [#38422](https://github.com/nodejs/node/pull/38422)
- [[`f1970127ee`](https://github.com/nodejs/node/commit/f1970127ee)] - **doc**: add try/catch in http2 respondWithFile example (Matteo Collina) [#38410](https://github.com/nodejs/node/pull/38410)
- [[`f6f1317f43`](https://github.com/nodejs/node/commit/f6f1317f43)] - **doc**: note the system requirements for V8 tests (DeeDeeG) [#38319](https://github.com/nodejs/node/pull/38319)
- [[`4b19beaf3c`](https://github.com/nodejs/node/commit/4b19beaf3c)] - **doc**: minor clarification to pathObject (James M Snell) [#38437](https://github.com/nodejs/node/pull/38437)
- [[`1eae4af6f7`](https://github.com/nodejs/node/commit/1eae4af6f7)] - **doc**: clarify that fs.Dir async iterator closes automatically (James M Snell) [#38438](https://github.com/nodejs/node/pull/38438)
- [[`14afb39259`](https://github.com/nodejs/node/commit/14afb39259)] - **doc**: document new TCP_KEEPCNT and TCP_KEEPINTVL socket option defaults (Arnold Zokas) [#38313](https://github.com/nodejs/node/pull/38313)
- [[`ed5ef21690`](https://github.com/nodejs/node/commit/ed5ef21690)] - **doc**: do not mention TCP in the allowHalfOpen option description (Luigi Pinca) [#38360](https://github.com/nodejs/node/pull/38360)
- [[`042985c139`](https://github.com/nodejs/node/commit/042985c139)] - **doc**: update message to match actual output (Rich Trott) [#35271](https://github.com/nodejs/node/pull/35271)
- [[`bcc5e2af76`](https://github.com/nodejs/node/commit/bcc5e2af76)] - **doc**: request default snap track be updated for LTS (Rod Vagg) [#37708](https://github.com/nodejs/node/pull/37708)
- [[`dfd4c7ba93`](https://github.com/nodejs/node/commit/dfd4c7ba93)] - **doc**: mark `process.hrtime()` as legacy (Antoine du Hamel) [#38371](https://github.com/nodejs/node/pull/38371)
- [[`67cd88da00`](https://github.com/nodejs/node/commit/67cd88da00)] - **doc**: fix typo in worker_threads.md (takayama) [#38368](https://github.com/nodejs/node/pull/38368)
- [[`a9314cda7d`](https://github.com/nodejs/node/commit/a9314cda7d)] - **doc**: fix version history for `"exports"` patterns (Antoine du Hamel) [#38355](https://github.com/nodejs/node/pull/38355)
- [[`76885cd578`](https://github.com/nodejs/node/commit/76885cd578)] - **doc**: fix `package.json` `"imports"` field history (Antoine du Hamel) [#38356](https://github.com/nodejs/node/pull/38356)
- [[`0e88ae7ec1`](https://github.com/nodejs/node/commit/0e88ae7ec1)] - **doc**: fix typo in buffer.md (divlo) [#38323](https://github.com/nodejs/node/pull/38323)
- [[`1cccc2da51`](https://github.com/nodejs/node/commit/1cccc2da51)] - **doc**: fix YAML comment opening tags (Jayden Seric) [#38324](https://github.com/nodejs/node/pull/38324)
- [[`25052dc987`](https://github.com/nodejs/node/commit/25052dc987)] - **doc**: add nodejs-sec email template (Daniel Bevenius) [#38290](https://github.com/nodejs/node/pull/38290)
- [[`3858029262`](https://github.com/nodejs/node/commit/3858029262)] - **doc**: update TSC members list with three new members (Rich Trott) [#38352](https://github.com/nodejs/node/pull/38352)
- [[`2eef587674`](https://github.com/nodejs/node/commit/2eef587674)] - **doc**: use `foo.prototype.bar` notation in buffer.md (Voltrex) [#38032](https://github.com/nodejs/node/pull/38032)
- [[`8a90f55a05`](https://github.com/nodejs/node/commit/8a90f55a05)] - **(SEMVER-MINOR)** **fs**: allow no-params fsPromises fileHandle read (Nitzan Uziely) [#38287](https://github.com/nodejs/node/pull/38287)
- [[`a696f1080c`](https://github.com/nodejs/node/commit/a696f1080c)] - **inspector**: remove redundant method for connection check (Yash Ladha) [#37986](https://github.com/nodejs/node/pull/37986)
- [[`fcac2e0363`](https://github.com/nodejs/node/commit/fcac2e0363)] - **lib**: harden lint checks for globals (Antoine du Hamel) [#38419](https://github.com/nodejs/node/pull/38419)
- [[`277122e1fa`](https://github.com/nodejs/node/commit/277122e1fa)] - **lib**: fix and improve os typings (Akhil Marsonya) [#38316](https://github.com/nodejs/node/pull/38316)
- [[`f2c0258b4c`](https://github.com/nodejs/node/commit/f2c0258b4c)] - **lib**: add support for JSTransferable as a mixin (James M Snell) [#38383](https://github.com/nodejs/node/pull/38383)
- [[`96f54d3446`](https://github.com/nodejs/node/commit/96f54d3446)] - **meta**: post comment when pr labeled fast-track (James M Snell) [#38446](https://github.com/nodejs/node/pull/38446)
- [[`4711f57cf2`](https://github.com/nodejs/node/commit/4711f57cf2)] - **perf_hooks**: add toJSON to performance class (Yash Ladha) [#37771](https://github.com/nodejs/node/pull/37771)
- [[`013fa59602`](https://github.com/nodejs/node/commit/013fa59602)] - **perf_hooks**: fix PerformanceObserver 'gc' crash (James M Snell) [#38414](https://github.com/nodejs/node/pull/38414)
- [[`10147f191e`](https://github.com/nodejs/node/commit/10147f191e)] - **readline**: move utilities to internal modules (Antoine du Hamel) [#38466](https://github.com/nodejs/node/pull/38466)
- [[`620ee42ab4`](https://github.com/nodejs/node/commit/620ee42ab4)] - **repl**: document top level await limitation with const/let (James M Snell) [#38449](https://github.com/nodejs/node/pull/38449)
- [[`aa24681dcb`](https://github.com/nodejs/node/commit/aa24681dcb)] - **repl**: display prompt once after error callback (Anna Henningsen) [#38314](https://github.com/nodejs/node/pull/38314)
- [[`9c06103a4f`](https://github.com/nodejs/node/commit/9c06103a4f)] - **src**: fix validation of negative offset to avoid abort (James M Snell) [#38421](https://github.com/nodejs/node/pull/38421)
- [[`7d8cc2abf1`](https://github.com/nodejs/node/commit/7d8cc2abf1)] - **src**: use %progbits instead of @progbits (Stephen Gallagher) [#38312](https://github.com/nodejs/node/pull/38312)
- [[`17856f1f88`](https://github.com/nodejs/node/commit/17856f1f88)] - **src**: print arbitrary javascript exception value in node report (legendecas) [#38009](https://github.com/nodejs/node/pull/38009)
- [[`769a210d55`](https://github.com/nodejs/node/commit/769a210d55)] - **src**: refactor to use THROW\_\* argument based snprintf (Filip Skokan) [#38357](https://github.com/nodejs/node/pull/38357)
- [[`e3538bbcd2`](https://github.com/nodejs/node/commit/e3538bbcd2)] - **src**: fix abort in pbkdf2 (Tobias Nießen) [#38354](https://github.com/nodejs/node/pull/38354)
- [[`09cacd7418`](https://github.com/nodejs/node/commit/09cacd7418)] - **src**: fix setting Converter sub char length (James M Snell) [#38331](https://github.com/nodejs/node/pull/38331)
- [[`3649ec5276`](https://github.com/nodejs/node/commit/3649ec5276)] - **src**: avoid deferred gc/cleanup for Buffer.from (James M Snell) [#38337](https://github.com/nodejs/node/pull/38337)
- [[`f2ffaba78c`](https://github.com/nodejs/node/commit/f2ffaba78c)] - **stream**: the position of \_read() is wrong (helloyou2012) [#38292](https://github.com/nodejs/node/pull/38292)
- [[`7ce39b8608`](https://github.com/nodejs/node/commit/7ce39b8608)] - **test**: fix `common.mustCall` `length` and `name` properties (Antoine du Hamel) [#38464](https://github.com/nodejs/node/pull/38464)
- [[`d1cd1178e7`](https://github.com/nodejs/node/commit/d1cd1178e7)] - **test**: address deprecation warning (Rich Trott) [#38448](https://github.com/nodejs/node/pull/38448)
- [[`67e9e71f75`](https://github.com/nodejs/node/commit/67e9e71f75)] - **test**: crypto KeyObject.from() ERR_INVALID_ARG_TYPE (pezhmanparsaee) [#37890](https://github.com/nodejs/node/pull/37890)
- [[`9ad611c0b2`](https://github.com/nodejs/node/commit/9ad611c0b2)] - **test**: fix flaky test-crypto-timing-safe-dqual-benchmarks (Rich Trott) [#38476](https://github.com/nodejs/node/pull/38476)
- [[`10b6b4e244`](https://github.com/nodejs/node/commit/10b6b4e244)] - **test**: update url Web Platform Tests (Leko) [#38435](https://github.com/nodejs/node/pull/38435)
- [[`4f6c4eb144`](https://github.com/nodejs/node/commit/4f6c4eb144)] - **test**: move abort test from pummel to abort directory (Rich Trott) [#38396](https://github.com/nodejs/node/pull/38396)
- [[`231ef4b0ce`](https://github.com/nodejs/node/commit/231ef4b0ce)] - **test**: move slower tests into pummel and skip on slow devices (Rich Trott) [#38395](https://github.com/nodejs/node/pull/38395)
- [[`45322dfa12`](https://github.com/nodejs/node/commit/45322dfa12)] - **test**: skip some pummel tests on slower machines (Rich Trott) [#38394](https://github.com/nodejs/node/pull/38394)
- [[`1bc47a4c0f`](https://github.com/nodejs/node/commit/1bc47a4c0f)] - **test**: fix test to allow quictls fork of OpenSSL 3 (Richard Lau) [#38372](https://github.com/nodejs/node/pull/38372)
- [[`6ac02755f5`](https://github.com/nodejs/node/commit/6ac02755f5)] - **test**: extend timeout on debugger tests for slower machines (Rich Trott) [#38161](https://github.com/nodejs/node/pull/38161)
- [[`93b0c78de0`](https://github.com/nodejs/node/commit/93b0c78de0)] - **test**: fix comment typo (Rich Trott) [#38161](https://github.com/nodejs/node/pull/38161)
- [[`6c3e5043b0`](https://github.com/nodejs/node/commit/6c3e5043b0)] - **test**: fix test-inspector-cli-address (Rich Trott) [#38161](https://github.com/nodejs/node/pull/38161)
- [[`27d7588ad5`](https://github.com/nodejs/node/commit/27d7588ad5)] - **test**: add ancestor package.json checks for tmpdir (Richard Lau) [#38285](https://github.com/nodejs/node/pull/38285)
- [[`30de03630e`](https://github.com/nodejs/node/commit/30de03630e)] - **test**: replace function with arrow function and remove unused argument (Andres) [#38235](https://github.com/nodejs/node/pull/38235)
- [[`eb8f5ce44f`](https://github.com/nodejs/node/commit/eb8f5ce44f)] - **test**: use .test domain for not found address (Richard Lau) [#38286](https://github.com/nodejs/node/pull/38286)
- [[`a4084d66c6`](https://github.com/nodejs/node/commit/a4084d66c6)] - **test,debugger**: migrate node-inspect tests to core (Rich Trott) [#38161](https://github.com/nodejs/node/pull/38161)
- [[`16eb078aa8`](https://github.com/nodejs/node/commit/16eb078aa8)] - **test,readline**: improve tab completion coverage (Antoine du Hamel) [#38465](https://github.com/nodejs/node/pull/38465)
- [[`b3ca1b358e`](https://github.com/nodejs/node/commit/b3ca1b358e)] - **timers**: remove redundant unref calls (Giora Guttsait) [#38320](https://github.com/nodejs/node/pull/38320)
- [[`5b393d9258`](https://github.com/nodejs/node/commit/5b393d9258)] - **tls**: validate ticket keys buffer (Antoine du Hamel) [#38308](https://github.com/nodejs/node/pull/38308)
- [[`f6745e9370`](https://github.com/nodejs/node/commit/f6745e9370)] - **tls**: fix `tlsSocket.setMaxSendFragment` abort (eladkeyshawn) [#38170](https://github.com/nodejs/node/pull/38170)
- [[`499da2d9e3`](https://github.com/nodejs/node/commit/499da2d9e3)] - **tools**: use mktemp to create the workspace directory (Luigi Pinca) [#38432](https://github.com/nodejs/node/pull/38432)
- [[`8a83bfd1bd`](https://github.com/nodejs/node/commit/8a83bfd1bd)] - **tools**: use a shallow clone of the npm/cli repository (Luigi Pinca) [#38463](https://github.com/nodejs/node/pull/38463)
- [[`bec959ef8b`](https://github.com/nodejs/node/commit/bec959ef8b)] - **tools**: disable LTO for "v8_cppgc_shared" target (Jesse Chan) [#38346](https://github.com/nodejs/node/pull/38346)
- [[`6350d35b3c`](https://github.com/nodejs/node/commit/6350d35b3c)] - **tools**: remove fixer for non-ascii-character ESLint custom rule (Rich Trott) [#38413](https://github.com/nodejs/node/pull/38413)
- [[`dce8d2968a`](https://github.com/nodejs/node/commit/dce8d2968a)] - **tools**: fix doc generation when version info is not available (Antoine du Hamel) [#38398](https://github.com/nodejs/node/pull/38398)
- [[`1033f6c8cb`](https://github.com/nodejs/node/commit/1033f6c8cb)] - **tools**: add \_depot_tools to PATH (for V8 tests) (DeeDeeG) [#38299](https://github.com/nodejs/node/pull/38299)
- [[`28f02cb8cf`](https://github.com/nodejs/node/commit/28f02cb8cf)] - **tools**: update ESLint to 7.25.0 (Colin Ihrig) [#38378](https://github.com/nodejs/node/pull/38378)
- [[`f1ea2c8e2b`](https://github.com/nodejs/node/commit/f1ea2c8e2b)] - **tools**: update markdown linter rules (Rich Trott) [#38384](https://github.com/nodejs/node/pull/38384)
- [[`02e875c645`](https://github.com/nodejs/node/commit/02e875c645)] - **tools**: remove node-inspect from license (Rich Trott) [#38161](https://github.com/nodejs/node/pull/38161)
- [[`d3bd4b4771`](https://github.com/nodejs/node/commit/d3bd4b4771)] - **tools**: fix type mismatch in test runner (Richard Lau) [#38289](https://github.com/nodejs/node/pull/38289)
- [[`9a2651352b`](https://github.com/nodejs/node/commit/9a2651352b)] - **typings**: add JSDoc typings for fs (Voltrex) [#38306](https://github.com/nodejs/node/pull/38306)
- [[`e389e86b6b`](https://github.com/nodejs/node/commit/e389e86b6b)] - **typings**: add JSDoc typings for util (Rohit Gohri) [#38213](https://github.com/nodejs/node/pull/38213)
- [[`ec5b06eae3`](https://github.com/nodejs/node/commit/ec5b06eae3)] - **util**: fix infinite recursion during inspection (Ruben Bridgewater) [#37079](https://github.com/nodejs/node/pull/37079)
- [[`67bd0ec15c`](https://github.com/nodejs/node/commit/67bd0ec15c)] - **zlib**: fix brotli flush range (Khaidi Chu) [#38408](https://github.com/nodejs/node/pull/38408)

Windows 32-bit Installer: https://nodejs.org/dist/v16.1.0/node-v16.1.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v16.1.0/node-v16.1.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v16.1.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v16.1.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v16.1.0/node-v16.1.0.pkg \
macOS Apple Silicon 64-bit Binary: https://nodejs.org/dist/v16.1.0/node-v16.1.0-darwin-arm64.tar.gz \
macOS Intel 64-bit Binary: https://nodejs.org/dist/v16.1.0/node-v16.1.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v16.1.0/node-v16.1.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v16.1.0/node-v16.1.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v16.1.0/node-v16.1.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v16.1.0/node-v16.1.0-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v16.1.0/node-v16.1.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v16.1.0/node-v16.1.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v16.1.0/node-v16.1.0.tar.gz \
Other release files: https://nodejs.org/dist/v16.1.0/ \
Documentation: https://nodejs.org/docs/v16.1.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

174f356190d82f24a53085d102799fd90e9031003adb891510aeef58bf2cc302  node-v16.1.0-aix-ppc64.tar.gz
4ed9f6d78528fc80997a02a461437a3c3e82ba530fe8338ecf970e733883f8a8  node-v16.1.0-darwin-arm64.tar.gz
f355d63e8b7564c1ea6486236b720c7ef87a1e5247cb8dec91de6f2ee688087f  node-v16.1.0-darwin-arm64.tar.xz
22525ecc3b91f4d9a5d44dffe061cdb23f1a3e4a5555552e7940987883a93547  node-v16.1.0-darwin-x64.tar.gz
536df4af3dc78b45ee1c167f66cacd6bd6064625be3bd8ecabb28ce2f8955283  node-v16.1.0-darwin-x64.tar.xz
1fab5fa83fa2dea284abea765a1c0a0fb08d7ebedcd5fffce52c0e5cf3c62090  node-v16.1.0-headers.tar.gz
51b230e1648d6da404bb3220a0002b2fdc67355731204155f52ffbcf894e4b0d  node-v16.1.0-headers.tar.xz
a96f07133c6a45b1287e03d4fab466436fcc6589cd9a84f6081facad02bae6d8  node-v16.1.0-linux-arm64.tar.gz
3354a1bbe5436ce8bab9a5c687ce4f450153dbb0a46e40078d86e27043deb81f  node-v16.1.0-linux-arm64.tar.xz
cd4a9900183fc9d61f2033c39e5f78b4ab3f0ea7a38623cafa7d78a78dea14cc  node-v16.1.0-linux-armv7l.tar.gz
9e9cec49fe15cc77fbeb1e24d697aa05664c2cef2c6afa25b39d0a787651608a  node-v16.1.0-linux-armv7l.tar.xz
8b29c005a447360e6178a862e6ea431f7157fc2dae32618764bed4c14224dd3a  node-v16.1.0-linux-ppc64le.tar.gz
0f3bb1fc81cd9cb52a9db04cbdcc71be724955f6506c59ca8524f4202e649828  node-v16.1.0-linux-ppc64le.tar.xz
289b462a1f678bd2443c6969596f37683bdba0cb2e732247da43fd63e173bdee  node-v16.1.0-linux-s390x.tar.gz
b2feb1fa7472faeb1eb7b325c5ce424a83603819b2576459bc2e6c64fc77478c  node-v16.1.0-linux-s390x.tar.xz
50dadc0c130ff7d079d0fb4a86e40756c76edb3cd3b42b8cf2a57497116695fa  node-v16.1.0-linux-x64.tar.gz
94d14ed1871a69e3dedd3a54d8c547c978b49566892616a227bf8be2f171a8a8  node-v16.1.0-linux-x64.tar.xz
f6dfd949a5d696e9496d8980a3a4e453581298329feeec7f8a1955f5bc4ab81a  node-v16.1.0.pkg
cdc229fff0e18ff9ca29c057691ff29da7bee8b7107b72d8181bdb5c3eeaba00  node-v16.1.0.tar.gz
a0a9230f92c1f1757e63fd1c17cc1a3db63c1d7ef6c1ba1ed4951cc32b02087c  node-v16.1.0.tar.xz
5bd63f0d12a1c278fc89b82feefec3a9a78a3768bf9f0db3038452b990516880  node-v16.1.0-win-x64.7z
ef72a4063b62fe853a09956fbc7a139f94312a9f14a43e1d6ff2263105115490  node-v16.1.0-win-x64.zip
2b9fad85b7dff2bca3dfddd42607ee1f99135cd1fa713632c3e68967ca2b86c6  node-v16.1.0-win-x86.7z
819a9d2691fee7d79f70c6aba572239a6a0c205c222bb8928feb4b52c90b3250  node-v16.1.0-win-x86.zip
5164deedaecff7c17fe77024b467c7169508efafc4385db3500b77e7f810cb88  node-v16.1.0-x64.msi
e0dbc4819dc01db24eda860516b4b04391912f89596f2e6c817a302595d20ccc  node-v16.1.0-x86.msi
3027562faa7773bb12885ad94dd6b9c12af6919080f412ea13fed3f25f87f9d0  win-x64/node.exe
b131d7b22910fab6bec4bfbe6ee150bfb8f861572d68a2b2e955522084e34c46  win-x64/node.lib
9340bc2f158af1ef8b6831107e5c0411a67cb0baa32b0c4874bb9e9c2984cffa  win-x64/node_pdb.7z
ecdb382912c68189a8fd5834e09e2dc574b123e40e14912006d13e46740a342c  win-x64/node_pdb.zip
57f13f66da561c0a9d0749c96fce70a33c6d7099e6f0fcc55c3621f0c1829758  win-x86/node.exe
87a5ea9b2890e13654f800bb657cb3457afa45db5e49dcd5ea2b7704283c4bd2  win-x86/node.lib
a4e34103cf1d5c1da16c5a18d0988e868dcb8a44f92c3244b56328abb94bbf1d  win-x86/node_pdb.7z
3512ba6f1ded408eab64955d8ec441cb21e1c67018cf48a46d5107b40f66a3ca  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEj8yhP+8dDC6RAI4Jdw96mlrhVgAFAmCRZIYACgkQdw96mlrh
VgC4RA//QN9aWdzDwMhef5D2lSWNeA7e0YEkdnz5P81WR7jadr6xjWIyjulrk2D1
j3B1kwwtMC7iJDSE7JPAvRmBNhVFY1VF7m1Vki3yN854QK6OBTeIAx2LXSz31gZE
K1RPtc2wFOrLBdnnfAX7pMBYDi1nOf3I9LByHGQSv7+wf6M7hdiqIvjFxJCnl5Wf
vurgJomhF/l96//LcK4dHHB8ZoxOzIDw56waSlkDLdIvLjk9Ldgoa+JXlQfSNMhe
tq/UqmssEtbvxgmY5QeqhUh9IRDMvGLCJ/yaZsvBWOcyfDkKv8F/m4HOT/Z+VPTH
1mTONW9SlJR2wq3IGKO9enLrp7Cl5cxxvWyiBIPDXbtji2GWuVMU1RqFIYZiRPfD
JcxV1eHU1LrxcHU8k/v2rBRyr5Pu8dyoIEIqvzPtlbszp9vw8mkX0GNvqQh9nX2n
qFCrCRTnzebO3GV2DnxDjy0+9bMDfpkxt7y08ZA8F+fTC25hE7MZjkB0+dSrT1c4
Qe13dbPaRNcYIsWZr8mpD2Unv1TjEZfnIsv+63VjhAiS0GNpWwjBW6tLPKsBsXYq
v2iABU8/Sy4FXA9ECgrZXrYNMnd39bHoFbIMOt6a1fw9eJ523QpXsrQtZDMvGR9C
ZEQvrgiWnJDV0s/goIbKnBw2LSnEcFG+HzVq6N2bJLWiJhSZdzo=
=J+SL
-----END PGP SIGNATURE-----

```
