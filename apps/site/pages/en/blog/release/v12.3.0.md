---
date: '2019-05-21T19:41:06.233Z'
category: release
title: Node v12.3.0 (Current)
layout: blog-post
author: <PERSON><PERSON>
---

### Notable changes

- **esm**:
  - Added the `--experimental-wasm-modules` flag to support WebAssembly modules (<PERSON><PERSON> & <PERSON>) [#27659](https://github.com/nodejs/node/pull/27659)
- **process**:
  - Log errors using `util.inspect` in case of fatal exceptions (<PERSON><PERSON>) [#27243](https://github.com/nodejs/node/pull/27243)
- **repl**:
  - Add `process.on('uncaughtException')` support (<PERSON><PERSON>) [#27151](https://github.com/nodejs/node/pull/27151)
- **stream**:
  - Implemented `Readable.from` async iterator utility (<PERSON>) [#27660](https://github.com/nodejs/node/pull/27660)
- **tls**:
  - Expose built-in root certificates (<PERSON>) [#26415](https://github.com/nodejs/node/pull/26415)
  - Support `net.Server` options (<PERSON>) [#27665](https://github.com/nodejs/node/pull/27665)
  - Expose `keylog` event on TLSSocket (Alba Mendez) [#27654](https://github.com/nodejs/node/pull/27654)
- **worker**:
  - Added the ability to unshift messages from the `MessagePort` (Anna Henningsen) [#27294](https://github.com/nodejs/node/pull/27294)

### Commits

- [[`7cc21d8afa`](https://github.com/nodejs/node/commit/7cc21d8afa)] - **assert**: remove unused code (Ruben Bridgewater) [#27676](https://github.com/nodejs/node/pull/27676)
- [[`6983a0c336`](https://github.com/nodejs/node/commit/6983a0c336)] - **assert**: add compatibility for older Node.js versions (Ruben Bridgewater) [#27672](https://github.com/nodejs/node/pull/27672)
- [[`493ead144d`](https://github.com/nodejs/node/commit/493ead144d)] - **assert**: loose deep equal should not compare symbol properties (Ruben Bridgewater) [#27653](https://github.com/nodejs/node/pull/27653)
- [[`ec642f18cc`](https://github.com/nodejs/node/commit/ec642f18cc)] - **assert**: use less read operations (Ruben Bridgewater) [#27525](https://github.com/nodejs/node/pull/27525)
- [[`3367bad080`](https://github.com/nodejs/node/commit/3367bad080)] - **assert**: refine assertion message (Ruben Bridgewater) [#27525](https://github.com/nodejs/node/pull/27525)
- [[`e573c99bfd`](https://github.com/nodejs/node/commit/e573c99bfd)] - **assert**: fix `assert.fail()` stack (Ruben Bridgewater) [#27525](https://github.com/nodejs/node/pull/27525)
- [[`6070e8872d`](https://github.com/nodejs/node/commit/6070e8872d)] - **async_hooks**: don't reuse resource in HttpAgent (Gerhard Stoebich) [#27581](https://github.com/nodejs/node/pull/27581)
- [[`e74e661044`](https://github.com/nodejs/node/commit/e74e661044)] - **async_hooks**: only disable promise hook if wanted (Anna Henningsen) [#27590](https://github.com/nodejs/node/pull/27590)
- [[`026bebfcbc`](https://github.com/nodejs/node/commit/026bebfcbc)] - **bootstrap**: --frozen-intrinsics unfreeze console (Guy Bedford) [#27663](https://github.com/nodejs/node/pull/27663)
- [[`e0589006a8`](https://github.com/nodejs/node/commit/e0589006a8)] - **build**: add arm64 to vcbuild.bat help message (Jon Kunkee) [#27683](https://github.com/nodejs/node/pull/27683)
- [[`766a731137`](https://github.com/nodejs/node/commit/766a731137)] - **build**: export OpenSSL UI symbols (Sam Roberts) [#27586](https://github.com/nodejs/node/pull/27586)
- [[`2bc177aa4f`](https://github.com/nodejs/node/commit/2bc177aa4f)] - **child_process**: setup stdio on error when possible (cjihrig) [#27696](https://github.com/nodejs/node/pull/27696)
- [[`b380c0f311`](https://github.com/nodejs/node/commit/b380c0f311)] - **child_process**: refactor stdioStringToArray function (zero1five) [#27657](https://github.com/nodejs/node/pull/27657)
- [[`da102cda54`](https://github.com/nodejs/node/commit/da102cda54)] - **console**: don't attach unnecessary error handlers (cjihrig) [#27691](https://github.com/nodejs/node/pull/27691)
- [[`83f243038f`](https://github.com/nodejs/node/commit/83f243038f)] - **deps**: V8: cherry-pick cca9ae3c9a (Benedikt Meurer) [#27729](https://github.com/nodejs/node/pull/27729)
- [[`750556dcfd`](https://github.com/nodejs/node/commit/750556dcfd)] - **deps**: update OpenSSL configs' timestamps (Jon Kunkee) [#27544](https://github.com/nodejs/node/pull/27544)
- [[`314fdda0c3`](https://github.com/nodejs/node/commit/314fdda0c3)] - **deps**: regenerate OpenSSL configs with fixed tooling (Jon Kunkee) [#27544](https://github.com/nodejs/node/pull/27544)
- [[`c7e5fca32c`](https://github.com/nodejs/node/commit/c7e5fca32c)] - **deps**: make VC-WIN config generation deterministic (Jon Kunkee) [#27543](https://github.com/nodejs/node/pull/27543)
- [[`76c9e86609`](https://github.com/nodejs/node/commit/76c9e86609)] - **deps**: patch V8 to 7.4.288.27 (Matheus Marchini) [#27615](https://github.com/nodejs/node/pull/27615)
- [[`9f5b6900e7`](https://github.com/nodejs/node/commit/9f5b6900e7)] - **doc**: corrected tlsSocket.getPeerCertificate response type (Dan Beglin) [#27757](https://github.com/nodejs/node/pull/27757)
- [[`d1da11765d`](https://github.com/nodejs/node/commit/d1da11765d)] - **doc**: correct parameter type on 'subprocess.kill(\[signal\])' (himself65) [#27760](https://github.com/nodejs/node/pull/27760)
- [[`7e750868c6`](https://github.com/nodejs/node/commit/7e750868c6)] - **doc**: replace createRequireFromPath() references (cjihrig) [#27762](https://github.com/nodejs/node/pull/27762)
- [[`55fe340dc2`](https://github.com/nodejs/node/commit/55fe340dc2)] - **doc**: improve createRequire() example (cjihrig) [#27762](https://github.com/nodejs/node/pull/27762)
- [[`378f44c2ed`](https://github.com/nodejs/node/commit/378f44c2ed)] - **doc**: update `util.format` formatters documentation (Ruben Bridgewater) [#27621](https://github.com/nodejs/node/pull/27621)
- [[`f663e74d0b`](https://github.com/nodejs/node/commit/f663e74d0b)] - **doc**: remove stability highlight for stable functions (Michael Dawson) [#27753](https://github.com/nodejs/node/pull/27753)
- [[`cf516f7b6a`](https://github.com/nodejs/node/commit/cf516f7b6a)] - **doc**: rewrite "About this Documentation" section (Rich Trott) [#27725](https://github.com/nodejs/node/pull/27725)
- [[`df01645c7c`](https://github.com/nodejs/node/commit/df01645c7c)] - **doc**: correct entry for electron v4.0.4 (Jacob) [#27394](https://github.com/nodejs/node/pull/27394)
- [[`1f7a527f04`](https://github.com/nodejs/node/commit/1f7a527f04)] - **doc**: clarify behavior of fs.mkdir (Gaelan) [#27505](https://github.com/nodejs/node/pull/27505)
- [[`d570995427`](https://github.com/nodejs/node/commit/d570995427)] - **doc**: remove non-existent entry-type flag (dnalborczyk) [#27678](https://github.com/nodejs/node/pull/27678)
- [[`da4a3797cb`](https://github.com/nodejs/node/commit/da4a3797cb)] - **doc**: format correction for experimental loader hooks (Daniel Nalborczyk) [#27537](https://github.com/nodejs/node/pull/27537)
- [[`cc45080109`](https://github.com/nodejs/node/commit/cc45080109)] - **doc**: dns.lookup() documentation error code (jvelezpo) [#27625](https://github.com/nodejs/node/pull/27625)
- [[`7923b4a407`](https://github.com/nodejs/node/commit/7923b4a407)] - **doc**: add call-once note to napi_queue_async_work (Gabriel Schulhof) [#27582](https://github.com/nodejs/node/pull/27582)
- [[`8d448be9fd`](https://github.com/nodejs/node/commit/8d448be9fd)] - **doc**: simplify test/README.md (Rich Trott) [#27630](https://github.com/nodejs/node/pull/27630)
- [[`172fa639a6`](https://github.com/nodejs/node/commit/172fa639a6)] - **doc**: simplify About This Documentation text (Rich Trott) [#27619](https://github.com/nodejs/node/pull/27619)
- [[`66cf89f57d`](https://github.com/nodejs/node/commit/66cf89f57d)] - **doc**: move Rod Vagg to TSC emeritus (Rod Vagg) [#27633](https://github.com/nodejs/node/pull/27633)
- [[`8a1f2d0bfc`](https://github.com/nodejs/node/commit/8a1f2d0bfc)] - **doc**: doc deprecate the legacy http parser (cjihrig) [#27498](https://github.com/nodejs/node/pull/27498)
- [[`a23e86f029`](https://github.com/nodejs/node/commit/a23e86f029)] - **doc**: add Sam Roberts to TSC (Rod Vagg) [#27606](https://github.com/nodejs/node/pull/27606)
- [[`c53a674be7`](https://github.com/nodejs/node/commit/c53a674be7)] - **doc**: add example to test doc for clarity (Aditya Pratap Singh) [#27561](https://github.com/nodejs/node/pull/27561)
- [[`c0cdf30e6e`](https://github.com/nodejs/node/commit/c0cdf30e6e)] - **doc**: improve CCM example (Tobias Nießen) [#27396](https://github.com/nodejs/node/pull/27396)
- [[`b5498ed19b`](https://github.com/nodejs/node/commit/b5498ed19b)] - **doc,meta**: codify security release commit message (Richard Lau) [#27643](https://github.com/nodejs/node/pull/27643)
- [[`6e2c8d0e18`](https://github.com/nodejs/node/commit/6e2c8d0e18)] - **doc,n-api**: update N-API version matrix for v12.x (Richard Lau) [#27745](https://github.com/nodejs/node/pull/27745)
- [[`767889b0a3`](https://github.com/nodejs/node/commit/767889b0a3)] - **doc,n-api**: fix `introduced_in` metadata (Richard Lau) [#27745](https://github.com/nodejs/node/pull/27745)
- [[`4ed8a9ba7e`](https://github.com/nodejs/node/commit/4ed8a9ba7e)] - **doc,tools**: updates for 6.x End-of-Life (Richard Lau) [#27658](https://github.com/nodejs/node/pull/27658)
- [[`80f30741bd`](https://github.com/nodejs/node/commit/80f30741bd)] - **esm**: use correct error arguments (cjihrig) [#27763](https://github.com/nodejs/node/pull/27763)
- [[`47f913bedc`](https://github.com/nodejs/node/commit/47f913bedc)] - **(SEMVER-MINOR)** **esm**: --experimental-wasm-modules integration support (Myles Borins) [#27659](https://github.com/nodejs/node/pull/27659)
- [[`89fda94b6a`](https://github.com/nodejs/node/commit/89fda94b6a)] - **esm**: fix esm load bug (ZYSzys) [#25491](https://github.com/nodejs/node/pull/25491)
- [[`1f935f899f`](https://github.com/nodejs/node/commit/1f935f899f)] - **events**: improve max listeners warning (Ruben Bridgewater) [#27694](https://github.com/nodejs/node/pull/27694)
- [[`6f23816bcf`](https://github.com/nodejs/node/commit/6f23816bcf)] - **fs**: extract path conversion and validation to getValidatedPath (ZYSzys) [#27656](https://github.com/nodejs/node/pull/27656)
- [[`206ae31a7e`](https://github.com/nodejs/node/commit/206ae31a7e)] - **http**: always call response.write() callback (Luigi Pinca) [#27709](https://github.com/nodejs/node/pull/27709)
- [[`bfb9356942`](https://github.com/nodejs/node/commit/bfb9356942)] - **http**: do not default to chunked encoding for TRACE (Luigi Pinca) [#27673](https://github.com/nodejs/node/pull/27673)
- [[`4a9af1778d`](https://github.com/nodejs/node/commit/4a9af1778d)] - **http**: add an alias at addListener on Server connection socket (himself65) [#27325](https://github.com/nodejs/node/pull/27325)
- [[`a66b391d20`](https://github.com/nodejs/node/commit/a66b391d20)] - **http2**: do no throw in writeHead if state.closed (Matteo Collina) [#27682](https://github.com/nodejs/node/pull/27682)
- [[`74046cee72`](https://github.com/nodejs/node/commit/74046cee72)] - **http2**: do not override the allowHalfOpen option (Luigi Pinca) [#27623](https://github.com/nodejs/node/pull/27623)
- [[`c7461567ce`](https://github.com/nodejs/node/commit/c7461567ce)] - **inspector**: mark profile type const (gengjiawen) [#27712](https://github.com/nodejs/node/pull/27712)
- [[`24b26c0687`](https://github.com/nodejs/node/commit/24b26c0687)] - **inspector**: fix typo (gengjiawen) [#27712](https://github.com/nodejs/node/pull/27712)
- [[`700459e008`](https://github.com/nodejs/node/commit/700459e008)] - **inspector**: added NodeRuntime domain (Aleksei Koziatinskii) [#27600](https://github.com/nodejs/node/pull/27600)
- [[`d2d3bf8b3b`](https://github.com/nodejs/node/commit/d2d3bf8b3b)] - **inspector**: code cleanup (Eugene Ostroukhov) [#27591](https://github.com/nodejs/node/pull/27591)
- [[`4dbebfd464`](https://github.com/nodejs/node/commit/4dbebfd464)] - **lib**: fix typo in pre_execution.js (gengjiawen) [#27649](https://github.com/nodejs/node/pull/27649)
- [[`88b4d00fc6`](https://github.com/nodejs/node/commit/88b4d00fc6)] - **lib**: restore `global.module` after --eval code is run (Anna Henningsen) [#27587](https://github.com/nodejs/node/pull/27587)
- [[`3ac4a7122b`](https://github.com/nodejs/node/commit/3ac4a7122b)] - **meta**: move jhamhader to Collaborator Emeriti list (Rich Trott) [#27707](https://github.com/nodejs/node/pull/27707)
- [[`9f9871c4b2`](https://github.com/nodejs/node/commit/9f9871c4b2)] - **meta**: move chrisdickinson to Collaborator Emeriti list (Rich Trott) [#27703](https://github.com/nodejs/node/pull/27703)
- [[`2e85642f4a`](https://github.com/nodejs/node/commit/2e85642f4a)] - **meta**: move whitlockjc to Collaborator Emeriti list (Rich Trott) [#27702](https://github.com/nodejs/node/pull/27702)
- [[`fc8ad7731f`](https://github.com/nodejs/node/commit/fc8ad7731f)] - **meta**: move estliberitas to Collaborator Emeriti list (Rich Trott) [#27697](https://github.com/nodejs/node/pull/27697)
- [[`ea62149212`](https://github.com/nodejs/node/commit/ea62149212)] - **meta**: move firedfox to Collaborator Emeriti list (Rich Trott) [#27618](https://github.com/nodejs/node/pull/27618)
- [[`6bef4c0083`](https://github.com/nodejs/node/commit/6bef4c0083)] - **meta**: move AnnaMag to Collaborator Emeriti list (Rich Trott) [#27603](https://github.com/nodejs/node/pull/27603)
- [[`14d58c2f95`](https://github.com/nodejs/node/commit/14d58c2f95)] - **meta**: move pmq20 to Collaborator Emeriti list (Rich Trott) [#27602](https://github.com/nodejs/node/pull/27602)
- [[`876441eefb`](https://github.com/nodejs/node/commit/876441eefb)] - **meta**: move orangemocha to Collaborator Emeriti list (Rich Trott) [#27626](https://github.com/nodejs/node/pull/27626)
- [[`140b44f3ea`](https://github.com/nodejs/node/commit/140b44f3ea)] - **module**: fix createRequireFromPath() slash logic (cjihrig) [#27634](https://github.com/nodejs/node/pull/27634)
- [[`8a96182827`](https://github.com/nodejs/node/commit/8a96182827)] - **module**: add missing space in error message (cjihrig) [#27627](https://github.com/nodejs/node/pull/27627)
- [[`c33e83497e`](https://github.com/nodejs/node/commit/c33e83497e)] - **module**: simplify createRequire() validation (cjihrig) [#27629](https://github.com/nodejs/node/pull/27629)
- [[`119a590f84`](https://github.com/nodejs/node/commit/119a590f84)] - **module**: improve resolve paths validation (cjihrig) [#27613](https://github.com/nodejs/node/pull/27613)
- [[`2f512e32a7`](https://github.com/nodejs/node/commit/2f512e32a7)] - **module**: handle relative paths in resolve paths (cjihrig) [#27598](https://github.com/nodejs/node/pull/27598)
- [[`74feb0b81e`](https://github.com/nodejs/node/commit/74feb0b81e)] - **process**: mark process.env as side-effect-free (Anna Henningsen) [#27684](https://github.com/nodejs/node/pull/27684)
- [[`0393045198`](https://github.com/nodejs/node/commit/0393045198)] - **(SEMVER-MINOR)** **process**: inspect error in case of a fatal exception (Ruben Bridgewater) [#27243](https://github.com/nodejs/node/pull/27243)
- [[`688a0bd2b8`](https://github.com/nodejs/node/commit/688a0bd2b8)] - **repl**: do not run --eval code if there is none (Anna Henningsen) [#27587](https://github.com/nodejs/node/pull/27587)
- [[`c78de13238`](https://github.com/nodejs/node/commit/c78de13238)] - **(SEMVER-MINOR)** **repl**: handle uncaughtException properly (Ruben Bridgewater) [#27151](https://github.com/nodejs/node/pull/27151)
- [[`d21e066f5a`](https://github.com/nodejs/node/commit/d21e066f5a)] - **src**: update UNREACHABLE macro to take a string (Nitish Sakhawalkar) [#26502](https://github.com/nodejs/node/pull/26502)
- [[`ae8b64df78`](https://github.com/nodejs/node/commit/ae8b64df78)] - **src**: remove util-inl.h from header files (Sam Roberts) [#27631](https://github.com/nodejs/node/pull/27631)
- [[`e736e20e87`](https://github.com/nodejs/node/commit/e736e20e87)] - **src**: declare unused priv argument (Sam Roberts) [#27631](https://github.com/nodejs/node/pull/27631)
- [[`d2e1efe8a3`](https://github.com/nodejs/node/commit/d2e1efe8a3)] - **src**: fix warnings about redefined BSWAP macros (Sam Roberts) [#27631](https://github.com/nodejs/node/pull/27631)
- [[`3c707976da`](https://github.com/nodejs/node/commit/3c707976da)] - **src**: remove extra semicolons after macros (gengjiawen) [#27579](https://github.com/nodejs/node/pull/27579)
- [[`a18692c4df`](https://github.com/nodejs/node/commit/a18692c4df)] - **src**: extract common macro to util.h (gengjiawen) [#27512](https://github.com/nodejs/node/pull/27512)
- [[`f6642e90b2`](https://github.com/nodejs/node/commit/f6642e90b2)] - **src**: elevate namespaces in node_worker.cc (Preveen Padmanabhan) [#27568](https://github.com/nodejs/node/pull/27568)
- [[`62fe3422fb`](https://github.com/nodejs/node/commit/62fe3422fb)] - **src**: refactor deprecated UVException usage in pipe-wrap.cc (gengjiawen) [#27562](https://github.com/nodejs/node/pull/27562)
- [[`b338d53916`](https://github.com/nodejs/node/commit/b338d53916)] - **src**: fix typos (gengjiawen) [#27580](https://github.com/nodejs/node/pull/27580)
- [[`32fd0ac901`](https://github.com/nodejs/node/commit/32fd0ac901)] - **stream**: use readableObjectMode public api for js stream (Anto Aravinth) [#27655](https://github.com/nodejs/node/pull/27655)
- [[`05c3d53ecc`](https://github.com/nodejs/node/commit/05c3d53ecc)] - **(SEMVER-MINOR)** **stream**: implement Readable.from async iterator utility (Guy Bedford) [#27660](https://github.com/nodejs/node/pull/27660)
- [[`f872210ffd`](https://github.com/nodejs/node/commit/f872210ffd)] - **test**: relax check in verify-graph (Gerhard Stoebich) [#27742](https://github.com/nodejs/node/pull/27742)
- [[`8b4101a97f`](https://github.com/nodejs/node/commit/8b4101a97f)] - **test**: un-mark worker syntax error tests as flaky (Anna Henningsen) [#27705](https://github.com/nodejs/node/pull/27705)
- [[`1757250997`](https://github.com/nodejs/node/commit/1757250997)] - **test**: clearing require cache crashes esm loader (Antoine du HAMEL) [#25491](https://github.com/nodejs/node/pull/25491)
- [[`7252a64a23`](https://github.com/nodejs/node/commit/7252a64a23)] - **test**: pass null params to napi_xxx_property() (Octavian Soldea) [#27628](https://github.com/nodejs/node/pull/27628)
- [[`9ed5882dec`](https://github.com/nodejs/node/commit/9ed5882dec)] - **test**: use common.PORT instead of an extraneous variable (Benjamin Ki) [#27565](https://github.com/nodejs/node/pull/27565)
- [[`f01183c29a`](https://github.com/nodejs/node/commit/f01183c29a)] - **test**: move dgram invalid host test to internet tests (Benjamin Ki) [#27565](https://github.com/nodejs/node/pull/27565)
- [[`8cba1affe3`](https://github.com/nodejs/node/commit/8cba1affe3)] - **test**: better assertion for async hook tests (Ali Ijaz Sheikh) [#27601](https://github.com/nodejs/node/pull/27601)
- [[`0c7f18ebd3`](https://github.com/nodejs/node/commit/0c7f18ebd3)] - **test**: test error when breakOnSigint is not a boolean for evaluate (Ruwan Geeganage) [#27503](https://github.com/nodejs/node/pull/27503)
- [[`3801859032`](https://github.com/nodejs/node/commit/3801859032)] - **test**: add tests for hasItems method in FreeList (Ruwan Geeganage) [#27588](https://github.com/nodejs/node/pull/27588)
- [[`691866f124`](https://github.com/nodejs/node/commit/691866f124)] - **test**: fix test-linux-perf flakiness (Matheus Marchini) [#27615](https://github.com/nodejs/node/pull/27615)
- [[`d7fcd75f62`](https://github.com/nodejs/node/commit/d7fcd75f62)] - **test**: remove unneeded `--expose-internals` (Rich Trott) [#27608](https://github.com/nodejs/node/pull/27608)
- [[`815a95734e`](https://github.com/nodejs/node/commit/815a95734e)] - **test**: refactor test-tls-enable-trace-cli.js (cjihrig) [#27553](https://github.com/nodejs/node/pull/27553)
- [[`b6e540a9a2`](https://github.com/nodejs/node/commit/b6e540a9a2)] - **test**: fix flaky test-tls-multiple-cas-as-string (Luigi Pinca) [#27569](https://github.com/nodejs/node/pull/27569)
- [[`a5dab9e85a`](https://github.com/nodejs/node/commit/a5dab9e85a)] - **test**: deflake test-tls-js-stream (Luigi Pinca) [#27478](https://github.com/nodejs/node/pull/27478)
- [[`bdd75d0622`](https://github.com/nodejs/node/commit/bdd75d0622)] - **(SEMVER-MINOR)** **tls**: expose built-in root certificates (Ben Noordhuis) [#26415](https://github.com/nodejs/node/pull/26415)
- [[`e61823c43a`](https://github.com/nodejs/node/commit/e61823c43a)] - **(SEMVER-MINOR)** **tls**: support `net.Server` options (Luigi Pinca) [#27665](https://github.com/nodejs/node/pull/27665)
- [[`eb1f4e50c7`](https://github.com/nodejs/node/commit/eb1f4e50c7)] - **(SEMVER-MINOR)** **tls**: expose keylog event on TLSSocket (Alba Mendez) [#27654](https://github.com/nodejs/node/pull/27654)
- [[`6624f802d9`](https://github.com/nodejs/node/commit/6624f802d9)] - **tls**: fix createSecureContext() cipher list filter (Sam Roberts) [#27614](https://github.com/nodejs/node/pull/27614)
- [[`b8b02c35ee`](https://github.com/nodejs/node/commit/b8b02c35ee)] - **tls**: add missing 'new' (cjihrig) [#27614](https://github.com/nodejs/node/pull/27614)
- [[`a8a11862e0`](https://github.com/nodejs/node/commit/a8a11862e0)] - **tools**: update markdown linter for Windows line endings (Rich Trott) [#27756](https://github.com/nodejs/node/pull/27756)
- [[`c3d16756f2`](https://github.com/nodejs/node/commit/c3d16756f2)] - **tools**: remove unneeded dependency files (Rich Trott) [#27730](https://github.com/nodejs/node/pull/27730)
- [[`0db846f734`](https://github.com/nodejs/node/commit/0db846f734)] - **tools**: refactor js2c.py for maximal Python3 compatibility (Refael Ackermann) [#25518](https://github.com/nodejs/node/pull/25518)
- [[`0e16b352b4`](https://github.com/nodejs/node/commit/0e16b352b4)] - **tools**: decrease code duplication for isString() in lint rules (cjihrig) [#27719](https://github.com/nodejs/node/pull/27719)
- [[`47184d1a0a`](https://github.com/nodejs/node/commit/47184d1a0a)] - **tools**: update capitalized-comments eslint rule (Ruben Bridgewater) [#27675](https://github.com/nodejs/node/pull/27675)
- [[`ea62f4a820`](https://github.com/nodejs/node/commit/ea62f4a820)] - **tools**: update dmn to 2.2.2 (Rich Trott) [#27686](https://github.com/nodejs/node/pull/27686)
- [[`d2dad0b4b8`](https://github.com/nodejs/node/commit/d2dad0b4b8)] - **tools**: DRY isRequireCall() in lint rules (cjihrig) [#27680](https://github.com/nodejs/node/pull/27680)
- [[`1b8bc77990`](https://github.com/nodejs/node/commit/1b8bc77990)] - **tools**: add `12.x` to alternative docs versions (Richard Lau) [#27658](https://github.com/nodejs/node/pull/27658)
- [[`1365683c23`](https://github.com/nodejs/node/commit/1365683c23)] - **tools**: allow RegExp in required-modules eslint rule (Richard Lau) [#27647](https://github.com/nodejs/node/pull/27647)
- [[`169ddc5097`](https://github.com/nodejs/node/commit/169ddc5097)] - **tools**: force common be required before any other modules (ZYSzys) [#27650](https://github.com/nodejs/node/pull/27650)
- [[`c6ab6b279c`](https://github.com/nodejs/node/commit/c6ab6b279c)] - **tools**: enable block-scoped-var eslint rule (Roman Reiss) [#27616](https://github.com/nodejs/node/pull/27616)
- [[`fd823ea7a8`](https://github.com/nodejs/node/commit/fd823ea7a8)] - **tools**: enable camelcase linting in tools (Rich Trott) [#27607](https://github.com/nodejs/node/pull/27607)
- [[`217e6b5a06`](https://github.com/nodejs/node/commit/217e6b5a06)] - **tools**: switch to camelcasing in apilinks.js (Rich Trott) [#27607](https://github.com/nodejs/node/pull/27607)
- [[`10b4a8103d`](https://github.com/nodejs/node/commit/10b4a8103d)] - **util**: if present, fallback to `toString` using the %s formatter (Ruben Bridgewater) [#27621](https://github.com/nodejs/node/pull/27621)
- [[`5205902762`](https://github.com/nodejs/node/commit/5205902762)] - **util**: remove outdated comment (Ruben Bridgewater) [#27733](https://github.com/nodejs/node/pull/27733)
- [[`099c9ce1a1`](https://github.com/nodejs/node/commit/099c9ce1a1)] - **util**: unify constructor inspection in `util.inspect` (Ruben Bridgewater) [#27733](https://github.com/nodejs/node/pull/27733)
- [[`d8b48675a7`](https://github.com/nodejs/node/commit/d8b48675a7)] - **util**: simplify inspection limit handling (Ruben Bridgewater) [#27733](https://github.com/nodejs/node/pull/27733)
- [[`6984ca1c2f`](https://github.com/nodejs/node/commit/6984ca1c2f)] - **util**: reconstruct constructor in more cases (Ruben Bridgewater) [#27668](https://github.com/nodejs/node/pull/27668)
- [[`8f48edd28f`](https://github.com/nodejs/node/commit/8f48edd28f)] - **vm**: mark global proxy as side-effect-free (Anna Henningsen) [#27523](https://github.com/nodejs/node/pull/27523)
- [[`c7cf8d9b74`](https://github.com/nodejs/node/commit/c7cf8d9b74)] - **(SEMVER-MINOR)** **worker**: add ability to unshift message from MessagePort (Anna Henningsen) [#27294](https://github.com/nodejs/node/pull/27294)
- [[`e004d427ce`](https://github.com/nodejs/node/commit/e004d427ce)] - **worker**: use special message as MessagePort close command (Anna Henningsen) [#27705](https://github.com/nodejs/node/pull/27705)
- [[`b7ed4d7187`](https://github.com/nodejs/node/commit/b7ed4d7187)] - **worker**: move `receiving_messages_` field to `MessagePort` (Anna Henningsen) [#27705](https://github.com/nodejs/node/pull/27705)

Windows 32-bit Installer: https://nodejs.org/dist/v12.3.0/node-v12.3.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v12.3.0/node-v12.3.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v12.3.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v12.3.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v12.3.0/node-v12.3.0.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v12.3.0/node-v12.3.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v12.3.0/node-v12.3.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v12.3.0/node-v12.3.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v12.3.0/node-v12.3.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v12.3.0/node-v12.3.0-aix-ppc64.tar.gz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v12.3.0/node-v12.3.0-sunos-x64.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v12.3.0/node-v12.3.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v12.3.0/node-v12.3.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v12.3.0/node-v12.3.0.tar.gz \
Other release files: https://nodejs.org/dist/v12.3.0/ \
Documentation: https://nodejs.org/docs/v12.3.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

ad994e17d12427dfb96b796c8a390af14541499edea9cc4b7349305ae650597c  node-v12.3.0-aix-ppc64.tar.gz
4a9faa038fb4e6e930a0fecd9818a4820b4ca91d1c45a1c1279fe49cdbd28160  node-v12.3.0-darwin-x64.tar.gz
fe1dbbb65d38ef3da56e93197d90d1a1bccc181053913f80efac11832a27447b  node-v12.3.0-darwin-x64.tar.xz
66a313ef7d943103fd175e83e8c8a2cccc331f784a54e38bff29240e995c180f  node-v12.3.0-headers.tar.gz
ea0e98a1c5c2f95df9388e08bd6b58fa40daf3afb34ad6a82282ed8738787fbb  node-v12.3.0-headers.tar.xz
ffb57ec86f4f279e75e755515fb74149702f3467c4841c47c18cfb5d89e67c6d  node-v12.3.0-linux-arm64.tar.gz
3d6e7046b938ff1f2535bdcc892a82abf44d875d1e96779cc3ea9a074ccfa200  node-v12.3.0-linux-arm64.tar.xz
c3824e14b88d04f4e2593e2512958b96c58221be79c3308b1b9d9b2d0c7f3ccf  node-v12.3.0-linux-armv7l.tar.gz
caf85dcaf85a2fd6d099ce9abaf1fd81e83897873ddf04d58df0449e3e5dcea5  node-v12.3.0-linux-armv7l.tar.xz
5d5a2a71169e60b433e8c648fe9d80533184b24c661042d1bdd44ac97239d2b5  node-v12.3.0-linux-ppc64le.tar.gz
74a0a208820072621402f06ef462e5f53585a7f77a364a40e9f9c4479a2390a7  node-v12.3.0-linux-ppc64le.tar.xz
7fa7adc4121d3102f9744770a37bf1f57568a8a5d033ecb5c095a3d67f965450  node-v12.3.0-linux-s390x.tar.gz
a6ae7ed30851b15c74f5da0dbacb876de26b79db23c73e6915a27df695a3bdef  node-v12.3.0-linux-s390x.tar.xz
1159739eb0e77d874bcbb59809b504c790ff275506c7a855dd8cabb495e93cc1  node-v12.3.0-linux-x64.tar.gz
2ce2c7a4d7fe3c560415e8dcbc5905a66a8bf28e2a2b2cff7e7a4eeb5753015d  node-v12.3.0-linux-x64.tar.xz
42f6fb6fdcbef00bde25fad8c5883a7688bee9a0dfb56191edbde66beb4436c0  node-v12.3.0-sunos-x64.tar.gz
3d59bfcf196defcdd54c1b8b52bc1f6337384db2e4fc93a0d00826db95c3c3cb  node-v12.3.0-sunos-x64.tar.xz
53f810c5fe35a8cf95afba72ceda8bb1d7ea29b81db119f55f7344e83de94abb  node-v12.3.0-win-x64.7z
b36c9f9809c90ce704465f066861314d6e33176a5d45da14c1519c3e4f35063e  node-v12.3.0-win-x64.zip
dbf2cd9f23915d24c82046888612c6ed944373f9c957ff56e0309b8748af3a3f  node-v12.3.0-win-x86.7z
068f514effbb680e71c90ee2b72cb788fb28ce4a8fd4bc209e8695fba0a28e1c  node-v12.3.0-win-x86.zip
9c82a9137d0d481429c1df16c83c487d835a010f05bc2439baaf00da86d8e6ec  node-v12.3.0-x64.msi
e033e87144578f16e017e6c040e4dd9d94d3c1d148e0e6061387ac0af3969790  node-v12.3.0-x86.msi
7b8321e0f48ce6a9d0975bf1bb4ce902b4fdba85026c235b8d681e9df744bcbd  node-v12.3.0.pkg
e26c54677964cb6b242a6221f3c121620564718f8be1e9a36a16f69860c575ea  node-v12.3.0.tar.gz
dc5e3eb2dabc38b31a7454a4426e852b9dbbb9b72722eedaa0d0e3d4fde2fd04  node-v12.3.0.tar.xz
bdec7c59c23e53b772af758b0bd2ceff588ce7b74afb770ceee1d012e7e6d23a  win-x64/node.exe
bd8ac8d42c9eccba3f8936522e2f814d60e9f455d14a0f14f01a8bde6213dbed  win-x64/node.lib
cb36ca5fdcb698234bb0de0f9b549f69164d4d769ae504c8ce04b4c21553cd1c  win-x64/node_pdb.7z
4a9cebad325a6149e5742352ba48f0c19044ee744c1a589c06bf44131af0ce55  win-x64/node_pdb.zip
a41bbb08154c7b44cf615146e11706c5db221369ad7b963c0b99acf9bb48c3a4  win-x86/node.exe
5b8b66716002053a1c80c41ca80f9c5a7212715f5bfac6e4d421eb11f7f662c1  win-x86/node.lib
1a3172d7ff28fed8d15fbf8d5ad8a934dc1dec8a32caf576ff4cc41681d8a148  win-x86/node_pdb.7z
5d03d5033283c93069b3bf4f2935b352c5ae59ef61b482b9b68eaa063ceae762  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEpIwr7mgOhBYyzU5E8HSWs+s8F2IFAlzkUPcACgkQ8HSWs+s8
F2IM/hAArzgtHjU6c3UR4Hd8GK55DbUJpCRVQHjHsz19xoCjTTVmRMMElaqjPCR1
PWxZFqqtVq2g8UVHd8sAlpPOVMxVGycdRMHFJHQQ9DxxpXH2f8wSyHPMidWnXfBR
GO71RM/7C/F/p2m3olVMLWaKg3RBZ0C0qIo6YKne/TjVSH50ukFLfwSZkGFDcgbT
Zim35Zky0LKwapgbn2t30mi87x4ZzVGRq5Rqx1n3gch1w0kRbpD9y6zpE9JTRbLu
WEpbPvGSJzsKiDGhvQnRGtoN1qvwAncoUeHoEnyRVrzaCoQpY4tgWRLf8GWGIKX5
rebc5F8OGxJNKpWMKUS5CAw9I73Fto80PO812RxxbATV7o5rqm/KXxnBF/bV6Tt8
q+vWTcPnq7Vf4OJPIL7doub65gv3fpY/Fau3qG7yjnQYja0vMLeAkwpYhO3ux/r1
A1FyJi/rlaKvAy28q+X22dYS9HqjBZQAWeou3wpo91jITDfGHl20ygHTQXMueQKA
grIEbeo/518Rji+/fjPbxWxfju0usmYN6BW8lycCf295oMFvh4YJpcEbw0OLxY/i
JETx/+Lmhf3/VkngudRxICDWmwvuinueWMGwVJ5N/Oat7ZdAIqlk/Tne7taLPL5H
GZZln6Bh65lmx09VptQkglUJ15dDaCesXS9KMe/JqbvmtRWlQJE=
=NfEz
-----END PGP SIGNATURE-----

```
