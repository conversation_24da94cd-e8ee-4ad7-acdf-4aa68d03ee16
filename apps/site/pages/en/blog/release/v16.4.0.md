---
date: '2021-06-23T11:48:33.680Z'
category: release
title: Node v16.4.0 (Current)
layout: blog-post
author: <PERSON>
---

### Notable changes

- **async_hooks**:
  - stabilize part of AsyncLocalStorage (<PERSON>) [#37675](https://github.com/nodejs/node/pull/37675)
- **deps**:
  - upgrade npm to 7.18.1 (npm-robot) [#39065](https://github.com/nodejs/node/pull/39065)
  - update V8 to 9.1.269.36 (<PERSON><PERSON><PERSON>) [#38273](https://github.com/nodejs/node/pull/38273)
- **dns**:
  - allow `--dns-result-order` to change default dns verbatim (Ouyang <PERSON>dong) [#38099](https://github.com/nodejs/node/pull/38099)

### Commits

- [[`d2b972ee52`](https://github.com/nodejs/node/commit/d2b972ee52)] - **async_hooks**: check for empty contexts before removing (<PERSON>) [#39095](https://github.com/nodejs/node/pull/39095)
- [[`03e75fda4c`](https://github.com/nodejs/node/commit/03e75fda4c)] - **async_hooks**: switch between native and context hooks correctly (Stephen Belanger) [#38912](https://github.com/nodejs/node/pull/38912)
- [[`8115e6ee6d`](https://github.com/nodejs/node/commit/8115e6ee6d)] - **(SEMVER-MINOR)** **async_hooks**: stabilize part of AsyncLocalStorage (Vladimir de Turckheim) [#37675](https://github.com/nodejs/node/pull/37675)
- [[`5f51729014`](https://github.com/nodejs/node/commit/5f51729014)] - **bootstrap**: move event loop handle checking into snapshot builder (Joyee Cheung) [#39007](https://github.com/nodejs/node/pull/39007)
- [[`9d100aa269`](https://github.com/nodejs/node/commit/9d100aa269)] - **bootstrap**: split NodeMainInstance::Run() (Joyee Cheung) [#39007](https://github.com/nodejs/node/pull/39007)
- [[`2aaf2f231f`](https://github.com/nodejs/node/commit/2aaf2f231f)] - **build**: reconfigure when gyp files change on Windows (Joyee Cheung) [#39066](https://github.com/nodejs/node/pull/39066)
- [[`7f225a05ee`](https://github.com/nodejs/node/commit/7f225a05ee)] - **_Revert_** "**build**: work around bug in MSBuild v16.10.0" (Michaël Zasso) [#38977](https://github.com/nodejs/node/pull/38977)
- [[`1853127dde`](https://github.com/nodejs/node/commit/1853127dde)] - **build**: reset embedder string to "-node.0" (Michaël Zasso) [#38273](https://github.com/nodejs/node/pull/38273)
- [[`c0d236f5ea`](https://github.com/nodejs/node/commit/c0d236f5ea)] - **build**: make build-addons errors fail the build (Richard Lau) [#38983](https://github.com/nodejs/node/pull/38983)
- [[`173292bcf8`](https://github.com/nodejs/node/commit/173292bcf8)] - **build**: fix commit-queue default branch (Mary Marchini) [#38998](https://github.com/nodejs/node/pull/38998)
- [[`e939e243bf`](https://github.com/nodejs/node/commit/e939e243bf)] - **build**: don't pass python override to V8 build (Richard Lau) [#38969](https://github.com/nodejs/node/pull/38969)
- [[`651c58b412`](https://github.com/nodejs/node/commit/651c58b412)] - **build**: correct Xcode spelling in .gitignore (bl-ue) [#38895](https://github.com/nodejs/node/pull/38895)
- [[`5203c9ced7`](https://github.com/nodejs/node/commit/5203c9ced7)] - **build**: fast-track npm PRs and dont-land them on LTS (Michaël Zasso) [#38885](https://github.com/nodejs/node/pull/38885)
- [[`7de57d4d33`](https://github.com/nodejs/node/commit/7de57d4d33)] - **build**: dont-land gyp-next PRs on LTS branches (Michaël Zasso) [#38887](https://github.com/nodejs/node/pull/38887)
- [[`e87cd4542b`](https://github.com/nodejs/node/commit/e87cd4542b)] - **child_process**: refactor to use `validateBoolean` (Qingyu Deng) [#38927](https://github.com/nodejs/node/pull/38927)
- [[`69fa9e16e9`](https://github.com/nodejs/node/commit/69fa9e16e9)] - **(SEMVER-MINOR)** **child_process**: allow `options.cwd` receive a URL (Khaidi Chu) [#38862](https://github.com/nodejs/node/pull/38862)
- [[`cf9d686c35`](https://github.com/nodejs/node/commit/cf9d686c35)] - **crypto**: fix aes crash when tag length too small (Khaidi Chu) [#38914](https://github.com/nodejs/node/pull/38914)
- [[`1799ea36f0`](https://github.com/nodejs/node/commit/1799ea36f0)] - **crypto**: use compatible version of EVP_CIPHER_name (Shelley Vohr) [#38925](https://github.com/nodejs/node/pull/38925)
- [[`6d5dc63ae4`](https://github.com/nodejs/node/commit/6d5dc63ae4)] - **crypto**: fix label cast in EVP_PKEY_CTX_set0_rsa_oaep_label (Shelley Vohr) [#38926](https://github.com/nodejs/node/pull/38926)
- [[`6e93c17bf5`](https://github.com/nodejs/node/commit/6e93c17bf5)] - **crypto**: use EVP_get_cipherbynid directly (Shelley Vohr) [#38901](https://github.com/nodejs/node/pull/38901)
- [[`82c293959e`](https://github.com/nodejs/node/commit/82c293959e)] - **crypto**: add missing rand.h include (Shelley Vohr) [#38864](https://github.com/nodejs/node/pull/38864)
- [[`e4f802de9a`](https://github.com/nodejs/node/commit/e4f802de9a)] - **debugger**: rename internal library for clarity (Rich Trott) [#39080](https://github.com/nodejs/node/pull/39080)
- [[`1e8bdab581`](https://github.com/nodejs/node/commit/1e8bdab581)] - **debugger**: use ERR_DEBUGGER_STARTUP_ERROR in \_inspect.js (Rich Trott) [#39024](https://github.com/nodejs/node/pull/39024)
- [[`b43cb69fbb`](https://github.com/nodejs/node/commit/b43cb69fbb)] - **debugger**: use error codes in debugger REPL (Rich Trott) [#39024](https://github.com/nodejs/node/pull/39024)
- [[`dc9218136b`](https://github.com/nodejs/node/commit/dc9218136b)] - **debugger**: use ERR_DEBUGGER_ERROR in debugger client (Rich Trott) [#39024](https://github.com/nodejs/node/pull/39024)
- [[`711916a271`](https://github.com/nodejs/node/commit/711916a271)] - **debugger**: remove unnecessary boilerplate copyright comment (Rich Trott) [#38952](https://github.com/nodejs/node/pull/38952)
- [[`0f65e41442`](https://github.com/nodejs/node/commit/0f65e41442)] - **debugger**: reduce scope of eslint disable comment (Rich Trott) [#38946](https://github.com/nodejs/node/pull/38946)
- [[`1fa724ec5a`](https://github.com/nodejs/node/commit/1fa724ec5a)] - **deps**: upgrade npm to 7.18.1 (npm-robot) [#39065](https://github.com/nodejs/node/pull/39065)
- [[`c6aa68598d`](https://github.com/nodejs/node/commit/c6aa68598d)] - **deps**: upgrade npm to 7.17.0 (npm-robot) [#38999](https://github.com/nodejs/node/pull/38999)
- [[`864fe9910b`](https://github.com/nodejs/node/commit/864fe9910b)] - **deps**: make V8 9.1 abi-compatible with 9.0 (Michaël Zasso) [#38991](https://github.com/nodejs/node/pull/38991)
- [[`c93f3573eb`](https://github.com/nodejs/node/commit/c93f3573eb)] - **deps**: V8: cherry-pick fa4cb172cde2 (Michaël Zasso) [#38273](https://github.com/nodejs/node/pull/38273)
- [[`3c6c28b0a1`](https://github.com/nodejs/node/commit/3c6c28b0a1)] - **deps**: V8: cherry-pick 4c074516397b (Michaël Zasso) [#38273](https://github.com/nodejs/node/pull/38273)
- [[`3c37396d5c`](https://github.com/nodejs/node/commit/3c37396d5c)] - **deps**: V8: cherry-pick 5f4413194480 (Michaël Zasso) [#38273](https://github.com/nodejs/node/pull/38273)
- [[`3433559a55`](https://github.com/nodejs/node/commit/3433559a55)] - **deps**: V8: cherry-pick 272445f10927 (Michaël Zasso) [#38273](https://github.com/nodejs/node/pull/38273)
- [[`f56c78574e`](https://github.com/nodejs/node/commit/f56c78574e)] - **deps**: V8: cherry-pick c0fceaa0669b (Michaël Zasso) [#38273](https://github.com/nodejs/node/pull/38273)
- [[`7197fcec93`](https://github.com/nodejs/node/commit/7197fcec93)] - **deps**: V8: cherry-pick d59db06bf542 (Michaël Zasso) [#38273](https://github.com/nodejs/node/pull/38273)
- [[`bf7aa9fef8`](https://github.com/nodejs/node/commit/bf7aa9fef8)] - **deps**: silence irrelevant V8 warnings (Michaël Zasso) [#37587](https://github.com/nodejs/node/pull/37587)
- [[`eac377bc15`](https://github.com/nodejs/node/commit/eac377bc15)] - **deps**: V8: backport aaacffa1e003 (Michaël Zasso) [#38273](https://github.com/nodejs/node/pull/38273)
- [[`1a7c8a12c1`](https://github.com/nodejs/node/commit/1a7c8a12c1)] - **deps**: fix V8 build issue with inline methods (Jiawen Geng) [#35415](https://github.com/nodejs/node/pull/35415)
- [[`3c9a75522b`](https://github.com/nodejs/node/commit/3c9a75522b)] - **deps**: make v8.h compatible with VS2015 (Joao Reis) [#32116](https://github.com/nodejs/node/pull/32116)
- [[`8ed258339a`](https://github.com/nodejs/node/commit/8ed258339a)] - **deps**: V8: forward declaration of `Rtl*FunctionTable` (Refael Ackermann) [#32116](https://github.com/nodejs/node/pull/32116)
- [[`4ef37c83a9`](https://github.com/nodejs/node/commit/4ef37c83a9)] - **deps**: V8: patch register-arm64.h (Refael Ackermann) [#32116](https://github.com/nodejs/node/pull/32116)
- [[`7c61c6ee25`](https://github.com/nodejs/node/commit/7c61c6ee25)] - **deps**: V8: un-cherry-pick bd019bd (Refael Ackermann) [#32116](https://github.com/nodejs/node/pull/32116)
- [[`e82ef4148e`](https://github.com/nodejs/node/commit/e82ef4148e)] - **(SEMVER-MINOR)** **deps**: update V8 to 9.1.269.36 (Michaël Zasso) [#38273](https://github.com/nodejs/node/pull/38273)
- [[`70af146745`](https://github.com/nodejs/node/commit/70af146745)] - **deps**: upgrade npm to 7.16.0 (npm-robot) [#38920](https://github.com/nodejs/node/pull/38920)
- [[`a71df7630e`](https://github.com/nodejs/node/commit/a71df7630e)] - **(SEMVER-MINOR)** **dns**: allow `--dns-result-order` to change default dns verbatim (Ouyang Yadong) [#38099](https://github.com/nodejs/node/pull/38099)
- [[`dce256b210`](https://github.com/nodejs/node/commit/dce256b210)] - **doc**: remove references to deleted freenode channels (devsnek) [#39047](https://github.com/nodejs/node/pull/39047)
- [[`1afff98805`](https://github.com/nodejs/node/commit/1afff98805)] - **doc**: fix typos (bl-ue) [#39049](https://github.com/nodejs/node/pull/39049)
- [[`858f66e691`](https://github.com/nodejs/node/commit/858f66e691)] - **doc**: add missing parameter types (Voltrex) [#39013](https://github.com/nodejs/node/pull/39013)
- [[`ed91379186`](https://github.com/nodejs/node/commit/ed91379186)] - **doc**: clearify that http does chunked encoding itself (Mao Wtm) [#28379](https://github.com/nodejs/node/pull/28379)
- [[`51561f390a`](https://github.com/nodejs/node/commit/51561f390a)] - **doc**: add missing changelog links (Antoine du Hamel) [#39016](https://github.com/nodejs/node/pull/39016)
- [[`a19170eb9d`](https://github.com/nodejs/node/commit/a19170eb9d)] - **doc**: clarify that only one Python version is required to build (bl-ue) [#38894](https://github.com/nodejs/node/pull/38894)
- [[`7b219992e0`](https://github.com/nodejs/node/commit/7b219992e0)] - **doc**: fix markup for aesImportParams (Tobias Nießen) [#38898](https://github.com/nodejs/node/pull/38898)
- [[`405b50cdba`](https://github.com/nodejs/node/commit/405b50cdba)] - **doc**: use `await` in filehandle.truncate() snippet (RA80533) [#38939](https://github.com/nodejs/node/pull/38939)
- [[`5218fe86d1`](https://github.com/nodejs/node/commit/5218fe86d1)] - **doc**: fixed typo in process.md (Derevianchenko Maksym) [#38941](https://github.com/nodejs/node/pull/38941)
- [[`f903ad85f2`](https://github.com/nodejs/node/commit/f903ad85f2)] - **doc**: add missing semis after classes (Darshan Sen) [#38931](https://github.com/nodejs/node/pull/38931)
- [[`0bdeeda3b5`](https://github.com/nodejs/node/commit/0bdeeda3b5)] - **doc**: update write callback documentation (Simone Busoli) [#38959](https://github.com/nodejs/node/pull/38959)
- [[`7a7c0588ad`](https://github.com/nodejs/node/commit/7a7c0588ad)] - **doc**: mark util.inherits as legacy (Voltrex) [#38896](https://github.com/nodejs/node/pull/38896)
- [[`f6964dc506`](https://github.com/nodejs/node/commit/f6964dc506)] - **doc**: clarify when `readable._read(...)` is called (Shaun Keys) [#38726](https://github.com/nodejs/node/pull/38726)
- [[`3481b02e77`](https://github.com/nodejs/node/commit/3481b02e77)] - **doc**: mark Node.js v15.x as EOL (Antoine du Hamel) [#38891](https://github.com/nodejs/node/pull/38891)
- [[`17a9846920`](https://github.com/nodejs/node/commit/17a9846920)] - **doc**: fix .mjs syntax in crypto.md (himself65) [#38882](https://github.com/nodejs/node/pull/38882)
- [[`8c7b2bab5f`](https://github.com/nodejs/node/commit/8c7b2bab5f)] - **doc,fs**: remove experimental status for WHATWG URL as path (Antoine du Hamel) [#38870](https://github.com/nodejs/node/pull/38870)
- [[`eddde6c31a`](https://github.com/nodejs/node/commit/eddde6c31a)] - **errors**: don't rekey on primitive type (Benjamin Coe) [#39025](https://github.com/nodejs/node/pull/39025)
- [[`3d7892ef39`](https://github.com/nodejs/node/commit/3d7892ef39)] - **errors**: add ERR_DEBUGGER_STARTUP_ERROR (Rich Trott) [#39024](https://github.com/nodejs/node/pull/39024)
- [[`631856ea32`](https://github.com/nodejs/node/commit/631856ea32)] - **errors**: add ERR_DEBUGGER_ERROR (Rich Trott) [#39024](https://github.com/nodejs/node/pull/39024)
- [[`336571fbdd`](https://github.com/nodejs/node/commit/336571fbdd)] - **_Revert_** "**http**: make HEAD method to work with keep-alive" (Michaël Zasso) [#38949](https://github.com/nodejs/node/pull/38949)
- [[`c2b4fbba0f`](https://github.com/nodejs/node/commit/c2b4fbba0f)] - **lib**: remove semicolon in preparation for babel/eslint-parser update (Rich Trott) [#39094](https://github.com/nodejs/node/pull/39094)
- [[`f17dde81f3`](https://github.com/nodejs/node/commit/f17dde81f3)] - **lib**: make internal/options lazy (Joyee Cheung) [#38993](https://github.com/nodejs/node/pull/38993)
- [[`551430514b`](https://github.com/nodejs/node/commit/551430514b)] - **lib**: add JSDoc typings for child_process (Voltrex) [#38222](https://github.com/nodejs/node/pull/38222)
- [[`ded83350a0`](https://github.com/nodejs/node/commit/ded83350a0)] - **lib**: make primordials Promise methods safe (Antoine du Hamel) [#38650](https://github.com/nodejs/node/pull/38650)
- [[`637c1fa83c`](https://github.com/nodejs/node/commit/637c1fa83c)] - **lib**: refactor debuglog init (Antoine du Hamel) [#38838](https://github.com/nodejs/node/pull/38838)
- [[`5b5e07a2cc`](https://github.com/nodejs/node/commit/5b5e07a2cc)] - **meta**: update label-pr-config (Michaël Zasso) [#38950](https://github.com/nodejs/node/pull/38950)
- [[`92ed1c6cce`](https://github.com/nodejs/node/commit/92ed1c6cce)] - **module**: fix legacy `node` specifier resolution to resolve `"main"` field (Antoine du Hamel) [#38979](https://github.com/nodejs/node/pull/38979)
- [[`4174f139b6`](https://github.com/nodejs/node/commit/4174f139b6)] - **net**: use missing validator (Voltrex) [#38984](https://github.com/nodejs/node/pull/38984)
- [[`f7724ab342`](https://github.com/nodejs/node/commit/f7724ab342)] - **node-api**: avoid crashing on passed-in null string (Gabriel Schulhof) [#38923](https://github.com/nodejs/node/pull/38923)
- [[`ec3e5b4c15`](https://github.com/nodejs/node/commit/ec3e5b4c15)] - **node-api**: avoid SecondPassCallback crash (Michael Dawson) [#38899](https://github.com/nodejs/node/pull/38899)
- [[`74f5e30d69`](https://github.com/nodejs/node/commit/74f5e30d69)] - **node-api**: rtn pending excep on napi_new_instance (legendecas) [#38798](https://github.com/nodejs/node/pull/38798)
- [[`4c6193fea1`](https://github.com/nodejs/node/commit/4c6193fea1)] - **report**: generates report on threads with no isolates (legendecas) [#38994](https://github.com/nodejs/node/pull/38994)
- [[`3c7a7d9ee4`](https://github.com/nodejs/node/commit/3c7a7d9ee4)] - **(SEMVER-MINOR)** **src**: allow to negate boolean CLI flags (Michaël Zasso) [#39023](https://github.com/nodejs/node/pull/39023)
- [[`284d9c6228`](https://github.com/nodejs/node/commit/284d9c6228)] - **src**: cleanup uv_fs_t regardless of success or not (legendecas) [#38996](https://github.com/nodejs/node/pull/38996)
- [[`902bb858d7`](https://github.com/nodejs/node/commit/902bb858d7)] - **src**: refactor to use locale functions (Darshan Sen) [#39014](https://github.com/nodejs/node/pull/39014)
- [[`10370c5e8a`](https://github.com/nodejs/node/commit/10370c5e8a)] - **src**: fix multiple AddLinkedBinding() calls (Anna Henningsen) [#39012](https://github.com/nodejs/node/pull/39012)
- [[`ff8313c3a5`](https://github.com/nodejs/node/commit/ff8313c3a5)] - **src**: throw error in LoadBuiltinModuleSource when reading fails (Joyee Cheung) [#38904](https://github.com/nodejs/node/pull/38904)
- [[`9ba5518f08`](https://github.com/nodejs/node/commit/9ba5518f08)] - **src**: skip test_fatal/test_threads for Debug builds (Daniel Bevenius) [#38805](https://github.com/nodejs/node/pull/38805)
- [[`06afb8df65`](https://github.com/nodejs/node/commit/06afb8df65)] - **(SEMVER-MINOR)** **src**: make InitializeOncePerProcess more flexible (Shelley Vohr) [#38888](https://github.com/nodejs/node/pull/38888)
- [[`db4b192113`](https://github.com/nodejs/node/commit/db4b192113)] - **src**: add not-weak DCHECK to PersistentToLocal::Strong (Anna Henningsen) [#38875](https://github.com/nodejs/node/pull/38875)
- [[`08b2a4a138`](https://github.com/nodejs/node/commit/08b2a4a138)] - **src,test**: raise error for --enable-fips when no FIPS (Daniel Bevenius) [#38859](https://github.com/nodejs/node/pull/38859)
- [[`5d92c09bbf`](https://github.com/nodejs/node/commit/5d92c09bbf)] - **src,url**: separate some tables out of node_url.cc (Khaidi Chu) [#38988](https://github.com/nodejs/node/pull/38988)
- [[`c20e28e1a0`](https://github.com/nodejs/node/commit/c20e28e1a0)] - **stream**: fix pipeline pump (Robert Nagy) [#39006](https://github.com/nodejs/node/pull/39006)
- [[`7b026d8a72`](https://github.com/nodejs/node/commit/7b026d8a72)] - **test**: move inspector-cli tests to sequential (Rich Trott) [#39079](https://github.com/nodejs/node/pull/39079)
- [[`a53911b166`](https://github.com/nodejs/node/commit/a53911b166)] - **test**: improve buffer coverage (Rongjian Zhang) [#38538](https://github.com/nodejs/node/pull/38538)
- [[`5e9175f148`](https://github.com/nodejs/node/commit/5e9175f148)] - **test**: fix name of variable in inspector-cli test (Tobias Nießen) [#38869](https://github.com/nodejs/node/pull/38869)
- [[`bd924610ec`](https://github.com/nodejs/node/commit/bd924610ec)] - **test**: fix typo (Houssem Chebab) [#39045](https://github.com/nodejs/node/pull/39045)
- [[`d50df5dec1`](https://github.com/nodejs/node/commit/d50df5dec1)] - **test**: fix typo in test-http2-invalidheaderfield.js (Ikko Ashimine) [#39021](https://github.com/nodejs/node/pull/39021)
- [[`6111671d45`](https://github.com/nodejs/node/commit/6111671d45)] - **test**: adapt abort tests for new Windows code (Michaël Zasso) [#38273](https://github.com/nodejs/node/pull/38273)
- [[`1816d46cef`](https://github.com/nodejs/node/commit/1816d46cef)] - **test**: adapt test-linux-perf to V8 changes (Michaël Zasso) [#38273](https://github.com/nodejs/node/pull/38273)
- [[`32961c4781`](https://github.com/nodejs/node/commit/32961c4781)] - **test**: fix V8 serdes test for V8 9.1 (Michaël Zasso) [#38273](https://github.com/nodejs/node/pull/38273)
- [[`f652284b3b`](https://github.com/nodejs/node/commit/f652284b3b)] - **test**: remove obsolete TLS test (Rich Trott) [#39001](https://github.com/nodejs/node/pull/39001)
- [[`81bbeab3bd`](https://github.com/nodejs/node/commit/81bbeab3bd)] - **test**: improve coverage of lib/events.js (Rongjian Zhang) [#38582](https://github.com/nodejs/node/pull/38582)
- [[`e82111f890`](https://github.com/nodejs/node/commit/e82111f890)] - **test**: http outgoing \_headers setter null (ycjcl868) [#38881](https://github.com/nodejs/node/pull/38881)
- [[`1f10e84939`](https://github.com/nodejs/node/commit/1f10e84939)] - **test**: suppress warning in test_environment.cc (Daniel Bevenius) [#38868](https://github.com/nodejs/node/pull/38868)
- [[`379b5f79a9`](https://github.com/nodejs/node/commit/379b5f79a9)] - **tls**: tweak clientCertEngine argument parsing (Shelley Vohr) [#38900](https://github.com/nodejs/node/pull/38900)
- [[`78d2e0ed8e`](https://github.com/nodejs/node/commit/78d2e0ed8e)] - **tools**: update babel-eslint-parser to 7.14.5 (Rich Trott) [#39094](https://github.com/nodejs/node/pull/39094)
- [[`fed641127a`](https://github.com/nodejs/node/commit/fed641127a)] - **tools**: update ESLint to 7.29.0 (Rich Trott) [#39083](https://github.com/nodejs/node/pull/39083)
- [[`3ae2a0be48`](https://github.com/nodejs/node/commit/3ae2a0be48)] - **tools**: fix typo (Houssem Chebab) [#39044](https://github.com/nodejs/node/pull/39044)
- [[`a1d0aef60e`](https://github.com/nodejs/node/commit/a1d0aef60e)] - **tools**: update doctool dependencies, migrate to ESM (Michaël Zasso) [#38966](https://github.com/nodejs/node/pull/38966)
- [[`2a292cf574`](https://github.com/nodejs/node/commit/2a292cf574)] - **tools**: update V8 gypfiles for 9.1 (Michaël Zasso) [#38273](https://github.com/nodejs/node/pull/38273)
- [[`0c90fd8454`](https://github.com/nodejs/node/commit/0c90fd8454)] - **tools**: avoid crashing CQ when git push fails (Antoine du Hamel) [#36861](https://github.com/nodejs/node/pull/36861)
- [[`f817c2d3bb`](https://github.com/nodejs/node/commit/f817c2d3bb)] - **tools**: fix typo in commit-queue.sh (bl-ue) [#39000](https://github.com/nodejs/node/pull/39000)
- [[`be5101eb32`](https://github.com/nodejs/node/commit/be5101eb32)] - **tools**: update ESLint to 7.28.0 (Luigi Pinca) [#38955](https://github.com/nodejs/node/pull/38955)
- [[`9bf9ddb490`](https://github.com/nodejs/node/commit/9bf9ddb490)] - **tools**: refactor snapshot builder (Joyee Cheung) [#38902](https://github.com/nodejs/node/pull/38902)
- [[`0706565097`](https://github.com/nodejs/node/commit/0706565097)] - **tools**: bump remark-preset-lint-node to 2.3.0 (Rich Trott) [#38910](https://github.com/nodejs/node/pull/38910)
- [[`7d35fa7938`](https://github.com/nodejs/node/commit/7d35fa7938)] - **tools**: update gyp-next to v0.9.1 (Jiawen Geng) [#38867](https://github.com/nodejs/node/pull/38867)
- [[`00c20e621f`](https://github.com/nodejs/node/commit/00c20e621f)] - **tools,doc**: forbid CJS globals in ESM code snippets (Antoine du Hamel) [#38889](https://github.com/nodejs/node/pull/38889)
- [[`99161b09f6`](https://github.com/nodejs/node/commit/99161b09f6)] - **url,src**: simplify ipv6 logic by using uv_inet_pton (Khaidi Chu) [#38842](https://github.com/nodejs/node/pull/38842)
- [[`f40725f2a1`](https://github.com/nodejs/node/commit/f40725f2a1)] - **vm**: use missing validator (Voltrex) [#38935](https://github.com/nodejs/node/pull/38935)
- [[`f959cb3c68`](https://github.com/nodejs/node/commit/f959cb3c68)] - **worker**: do not look up context twice in PostMessage (Anna Henningsen) [#38784](https://github.com/nodejs/node/pull/38784)

Windows 32-bit Installer: https://nodejs.org/dist/v16.4.0/node-v16.4.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v16.4.0/node-v16.4.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v16.4.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v16.4.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v16.4.0/node-v16.4.0.pkg \
macOS Apple Silicon 64-bit Binary: https://nodejs.org/dist/v16.4.0/node-v16.4.0-darwin-arm64.tar.gz \
macOS Intel 64-bit Binary: https://nodejs.org/dist/v16.4.0/node-v16.4.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v16.4.0/node-v16.4.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v16.4.0/node-v16.4.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v16.4.0/node-v16.4.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v16.4.0/node-v16.4.0-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v16.4.0/node-v16.4.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v16.4.0/node-v16.4.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v16.4.0/node-v16.4.0.tar.gz \
Other release files: https://nodejs.org/dist/v16.4.0/ \
Documentation: https://nodejs.org/docs/v16.4.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

7695554a71873ea2393248438508c5d73a4fc195f760a9317e33670826ab691f  node-v16.4.0-aix-ppc64.tar.gz
771469be99d6af048d9b192cd7b338c68a4604e0fcc7f8804278c91b5ad3f74f  node-v16.4.0-darwin-arm64.tar.gz
69847f02e277a5455e8ae06ccaeb6bb51672ff36aeda64c7f8a9c3dba87774e8  node-v16.4.0-darwin-arm64.tar.xz
95c81b54ea3069fcf230664d5d80b10e46f8fff5163644b7076fe48df13fc2fb  node-v16.4.0-darwin-x64.tar.gz
2fe18b643d3846c22921b88041fcac6f6f532b7c1a60fc25e984de98c2466575  node-v16.4.0-darwin-x64.tar.xz
5085d9669b5df764faf234ad6d7ed1ce6c468d9f3c7f680c7b008d5be12dc4b2  node-v16.4.0-headers.tar.gz
92d8d154af3fdc31695e29d19a1541d55205c4e4dcfafdb2e826e79a39be29b3  node-v16.4.0-headers.tar.xz
8500c9b61717eeeb6f62ed88723dc4896b1bd0a38d8a0f8f8bfcd99e4879e921  node-v16.4.0-linux-arm64.tar.gz
dd8895d727f38f5d3bcfc9e6cfeeb5e61a55ef788ca23a789f87c4e36f8a599e  node-v16.4.0-linux-arm64.tar.xz
0a40f6d679f8eccab2b9c43a385172fa0b0584246eef2c41c986fdf572206fa8  node-v16.4.0-linux-armv7l.tar.gz
f455f936e3ba97fc5150cb0e161a90cc1eeaa4d6c6b39b5c679cee792d21a5e5  node-v16.4.0-linux-armv7l.tar.xz
8b68bb6bb00af0db969e4c3c0f47bba408c71dfb3a33dd9a95336bd1f03769e8  node-v16.4.0-linux-ppc64le.tar.gz
a309bb0da47872988dfe4cab3e170856844ef1f61f62cd3c5202a096a5c5a1f5  node-v16.4.0-linux-ppc64le.tar.xz
9008300e37e088bf8abc8848208ac3fec23b4c26165c3b396c6a533e8c6b08da  node-v16.4.0-linux-s390x.tar.gz
8dd5ae54694aaea813b7d3c499b57412f625dbaebdca389a1ed7377125726324  node-v16.4.0-linux-s390x.tar.xz
6fb7bc9aece48f2d94941c586ed5d541ac29c8981bc09585fcabe9e4b87d57fa  node-v16.4.0-linux-x64.tar.gz
cb2076ea116f0132c2233ac00da306b3061cda2eacbd0fab5b1263e0c9ff0686  node-v16.4.0-linux-x64.tar.xz
14741dd37ce35dee9675ea0ed7134884db65e218e4841534229a53698c6a6484  node-v16.4.0.pkg
37e526b494a0ae53d2205421b93cc3d6fa2d9f4d8349891814a23e69d59a6d8d  node-v16.4.0.tar.gz
f91e212e0b64d5fa745b15da4b8ac504acf72fb9216bfa77d3f66ca0e178c81d  node-v16.4.0.tar.xz
19da293c890b41793516a97775e1bcfb94c62dfc0c38df3fdf830bde22c7d63c  node-v16.4.0-win-x64.7z
2232f638c4913059e3de358d54629288092cd068553deaa60b9b46d6d760abcb  node-v16.4.0-win-x64.zip
48e0a70a87c604371e67757d68008b4d5bb32c01862da9a3b3c9bdcb2a850639  node-v16.4.0-win-x86.7z
24fc12e114165d1671dc6cc8ee4e078b0795a49dc6895204510366db2f1252b0  node-v16.4.0-win-x86.zip
286223b111698ab68ea1c2bf7379e542ec47187baa00e905bdb55898eed3599a  node-v16.4.0-x64.msi
56193c89e4091c9485a1e90a39b37b17dc64ffc6983cce978265471c289f5718  node-v16.4.0-x86.msi
afede6dc68ac545d90ee46247438b63661965c7d91aee04dd4efa2cbec7970c0  win-x64/node.exe
fe0a67955d3d9078a1495d936b59c9161ca8f8dbe29e95bfbf90a04b8bf6de23  win-x64/node.lib
7f2c849dfc3c3d0e915ee7ccbccdfd7711ef20288834d26807f743758512bec3  win-x64/node_pdb.7z
9297615005e2177a4576894f9e67902604fad1153d548a7c65e3670633bf18bd  win-x64/node_pdb.zip
0b0d8a752c579838961b6ef8e4d2e7b7a9b839e7b73add30b8aa2889d3f92444  win-x86/node.exe
3a4e948add4fcb30eca711e7a76a3c16fe1f61ae7ab4eec85b80992ca96496fa  win-x86/node.lib
8e41e601620d9d3ad0a68fc29071c7edbfd84b3f13e74e070ea2f791fb9a0a8f  win-x86/node_pdb.7z
b1c72aa329f308c3a0aa5411ab18083aa384c84c33d133712fcb80d6f6b2d5d4  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEdPEmArbxxOkT+qN606iWE2Q7YgEFAmDTHtMACgkQ06iWE2Q7
YgFRJhAAzpwqIYTz79O4jiG92kkmMAxmEeBtg2PQ9t0zmC0jejgJbFbxOSRQ4JlG
A4DIzW3xnROjCpb9jQIGLoEiUKZFpqq3jtKpWbPceWioDzpswMs+rnguiIuWAg6R
QEEyAfqfM0ZY4jmxQoJyIe/dss2OxZdL5+57FsmAeoubcfxtEQjarwPyG3s4tbVu
scjU0WLfFhQdckNRTnCUN8llD10rvtlgG0vagaSmwsbwn4vsnZnpyfVU7m/ujxLd
cxIltUUf8jk5rxmQYy7ad3rPTeu8i4r75H1xHO1u5vFCyoiadCJK2JLbK+RiTge7
fu/aXvqLp8XXnZEobTo213AyJysmrBIPp42RuyRZV/oOsKK3mykbbaqldHKa9xHk
W8vh9aK199iTvL0OrFa3OG7jSCHav/jJtUX9RrtIS9mX/DrT298T21TkjV6zpL7r
OshE4pSHnRwVMPxBt2nBWtLc6EVy5oziABh4Jce/NIbSHJdObMX1B5dqZhwcGSRC
oHgOvFU4zBgZCIl3cvyMCw6NBcojtaXSGBRWAvh2B5WXRykcidqedfCN1VrjLicn
1yHX/k48K77ELwT02RLgvZUZSWxsVy5OKXJhgXOsGGho4tgSzfk6jpTYNGhaAZ/h
pQbfGdVOWfQiyJRWVbiY+dL0nX/Y35Mw3SOQ5z34fGmu6pq4f2g=
=dlmQ
-----END PGP SIGNATURE-----

```
