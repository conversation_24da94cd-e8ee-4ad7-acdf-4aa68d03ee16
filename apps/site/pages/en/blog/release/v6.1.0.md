---
date: '2016-05-05T23:07:06.434Z'
category: release
title: Node v6.1.0 (Current)
layout: blog-post
author: <PERSON>
---

Please see our [blog post](/blog/vulnerability/openssl-may-2016/) for more info on the security contents of this release.

### Notable Changes

- **assert**: `deep{Strict}Equal()` now works correctly with circular references. (<PERSON>) [#6432](https://github.com/nodejs/node/pull/6432)
- **debugger**: Arrays are now formatted correctly in the debugger repl. (cjihrig) [#6448](https://github.com/nodejs/node/pull/6448)
- **deps**: Upgrade OpenSSL sources to 1.0.2h (<PERSON><PERSON><PERSON>) [#6550](https://github.com/nodejs/node/pull/6550)
- **net**: Introduced a `Socket#connecting` property. (<PERSON>or Indutny) [#6404](https://github.com/nodejs/node/pull/6404)
  - Previously this information was only available as the undocumented, internal `_connecting` property.
- **process**: Introduced `process.cpuUsage()`. (<PERSON>) [#6157](https://github.com/nodejs/node/pull/6157)
- **stream**: `Writable#setDefaultEncoding()` now returns `this`. (Alexander Makarenko) [#5040](https://github.com/nodejs/node/pull/5040)
- **util**: Two new additions to `util.inspect()`:
  - Added a `maxArrayLength` option to truncate the formatting of Arrays. (James M Snell) [#6334](https://github.com/nodejs/node/pull/6334)
    - This is set to `100` by default.
  - Added a `showProxy` option for formatting proxy intercepting handlers. (James M Snell) [#6465](https://github.com/nodejs/node/pull/6465)
    - Inspecting proxies is non-trivial and as such this is off by default.

### Commits

- [[`76c9ab5fcf`](https://github.com/nodejs/node/commit/76c9ab5fcf)] - **assert**: allow circular references (Rich Trott) [#6432](https://github.com/nodejs/node/pull/6432)
- [[`7b9ae70757`](https://github.com/nodejs/node/commit/7b9ae70757)] - **benchmark**: Fix crash in net benchmarks (Matt Loring) [#6407](https://www.github.com/nodejs/node/pull/6407)
- [[`0d1985358a`](https://github.com/nodejs/node/commit/0d1985358a)] - **build**: use shorthand lint target from test (Johan Bergström) [#6406](https://github.com/nodejs/node/pull/6406)
- [[`7153f96f0e`](https://github.com/nodejs/node/commit/7153f96f0e)] - **build**: unbreak -prof, disable PIE on OS X (Ben Noordhuis) [#6453](https://github.com/nodejs/node/pull/6453)
- [[`8956432e18`](https://github.com/nodejs/node/commit/8956432e18)] - **build**: exclude tap files from tarballs (Brian White) [#6348](https://github.com/nodejs/node/pull/6348)
- [[`11e7cc5310`](https://github.com/nodejs/node/commit/11e7cc5310)] - **build**: don't compile with -B (Ben Noordhuis) [#6393](https://github.com/nodejs/node/pull/6393)
- [[`1330496bbf`](https://github.com/nodejs/node/commit/1330496bbf)] - **cluster**: remove use of bind() in destroy() (yorkie) [#6502](https://github.com/nodejs/node/pull/6502)
- [[`fdde36909c`](https://github.com/nodejs/node/commit/fdde36909c)] - **crypto**: fix error in deprecation message (Rich Trott) [#6344](https://github.com/nodejs/node/pull/6344)
- [[`2d503b1d4b`](https://github.com/nodejs/node/commit/2d503b1d4b)] - **debugger**: display array contents in repl (cjihrig) [#6448](https://github.com/nodejs/node/pull/6448)
- [[`54f8600613`](https://github.com/nodejs/node/commit/54f8600613)] - **deps**: update openssl asm and asm_obsolete files (Shigeki Ohtsu) [#6550](https://github.com/nodejs/node/pull/6550)
- [[`a5a2944877`](https://github.com/nodejs/node/commit/a5a2944877)] - **deps**: add -no_rand_screen to openssl s_client (Shigeki Ohtsu) [nodejs/io.js#1836](https://github.com/nodejs/io.js/pull/1836)
- [[`3fe68129c8`](https://github.com/nodejs/node/commit/3fe68129c8)] - **deps**: fix asm build error of openssl in x86_win32 (Shigeki Ohtsu) [iojs/io.js#1389](https://github.com/iojs/io.js/pull/1389)
- [[`d159462fed`](https://github.com/nodejs/node/commit/d159462fed)] - **deps**: fix openssl assembly error on ia32 win32 (Fedor Indutny) [iojs/io.js#1389](https://github.com/iojs/io.js/pull/1389)
- [[`3af28d3693`](https://github.com/nodejs/node/commit/3af28d3693)] - **deps**: copy all openssl header files to include dir (Shigeki Ohtsu) [#6550](https://github.com/nodejs/node/pull/6550)
- [[`e6ab3ece65`](https://github.com/nodejs/node/commit/e6ab3ece65)] - **deps**: upgrade openssl sources to 1.0.2h (Shigeki Ohtsu) [#6550](https://github.com/nodejs/node/pull/6550)
- [[`65b6574d59`](https://github.com/nodejs/node/commit/65b6574d59)] - **deps**: backport IsValid changes from 4e8736d in V8 (Michaël Zasso) [#6544](https://github.com/nodejs/node/pull/6544)
- [[`33f24c821b`](https://github.com/nodejs/node/commit/33f24c821b)] - **doc**: adds 'close' events to fs.ReadStream and fs.WriteStream (Jenna Vuong) [#6499](https://github.com/nodejs/node/pull/6499)
- [[`4f728df1bf`](https://github.com/nodejs/node/commit/4f728df1bf)] - **doc**: linkify remaining references to fs.Stats object (Kevin Donahue) [#6485](https://github.com/nodejs/node/pull/6485)
- [[`9a29b50c52`](https://github.com/nodejs/node/commit/9a29b50c52)] - **doc**: fix the lint of an example in cluster.md (yorkie) [#6516](https://github.com/nodejs/node/pull/6516)
- [[`d674493fa5`](https://github.com/nodejs/node/commit/d674493fa5)] - **doc**: add missing underscore for markdown italics (Kevin Donahue) [#6529](https://github.com/nodejs/node/pull/6529)
- [[`7c30f15e1b`](https://github.com/nodejs/node/commit/7c30f15e1b)] - **doc**: ensure consistent grammar in node.1 file (justshiv) [#6426](https://github.com/nodejs/node/pull/6426)
- [[`e5ce53a217`](https://github.com/nodejs/node/commit/e5ce53a217)] - **doc**: fix sentence fragment in fs doc (Rich Trott) [#6488](https://github.com/nodejs/node/pull/6488)
- [[`3e028a143c`](https://github.com/nodejs/node/commit/3e028a143c)] - **doc**: remove obsolete comment in isError() example (cjihrig) [#6486](https://github.com/nodejs/node/pull/6486)
- [[`969f96a019`](https://github.com/nodejs/node/commit/969f96a019)] - **doc**: fix a typo in `__dirname` section (William Luo) [#6473](https://github.com/nodejs/node/pull/6473)
- [[`ab7055b003`](https://github.com/nodejs/node/commit/ab7055b003)] - **doc**: fix fs.realpath man pg links (phette23) [#6451](https://github.com/nodejs/node/pull/6451)
- [[`13e660888f`](https://github.com/nodejs/node/commit/13e660888f)] - **doc**: extra clarification of historySize option (vsemozhetbyt) [#6397](https://github.com/nodejs/node/pull/6397)
- [[`3d5b732660`](https://github.com/nodejs/node/commit/3d5b732660)] - **doc**: clarifies http.serverResponse implementation (Allen Hernandez) [#6072](https://github.com/nodejs/node/pull/6072)
- [[`7034ebe2bc`](https://github.com/nodejs/node/commit/7034ebe2bc)] - **doc**: use `Node.js` in synopsis document (Rich Trott) [#6476](https://github.com/nodejs/node/pull/6476)
- [[`4ae39f9863`](https://github.com/nodejs/node/commit/4ae39f9863)] - **doc**: remove all scrollbar styling (Claudio Rodriguez) [#6479](https://github.com/nodejs/node/pull/6479)
- [[`e6c8da45b1`](https://github.com/nodejs/node/commit/e6c8da45b1)] - **(SEMVER-MINOR)** **doc**: make `writable.setDefaultEncoding()` return `this` (Alexander Makarenko) [#5040](https://github.com/nodejs/node/pull/5040)
- [[`4068d64f4f`](https://github.com/nodejs/node/commit/4068d64f4f)] - **doc**: fix EventEmitter#eventNames() example (Сковорода Никита Андреевич) [#6417](https://github.com/nodejs/node/pull/6417)
- [[`bfcde97251`](https://github.com/nodejs/node/commit/bfcde97251)] - **doc**: fix incorrect syntax in examples (Evan Lucas) [#6463](https://github.com/nodejs/node/pull/6463)
- [[`8eb87ee239`](https://github.com/nodejs/node/commit/8eb87ee239)] - **doc**: Remove extra space in REPL example (Juan) [#6447](https://github.com/nodejs/node/pull/6447)
- [[`fd37d54eb5`](https://github.com/nodejs/node/commit/fd37d54eb5)] - **doc**: added note warning about change to console.endTime() (Ben Page) [#6454](https://github.com/nodejs/node/pull/6454)
- [[`b3f75ec801`](https://github.com/nodejs/node/commit/b3f75ec801)] - **doc**: expand documentation for process.exit() (James M Snell) [#6410](https://github.com/nodejs/node/pull/6410)
- [[`fc0fbf1c63`](https://github.com/nodejs/node/commit/fc0fbf1c63)] - **doc**: subdivide TOC, add auxiliary links (Jeremiah Senkpiel) [#6167](https://github.com/nodejs/node/pull/6167)
- [[`150dd36503`](https://github.com/nodejs/node/commit/150dd36503)] - **doc**: no Node.js(1) (Jeremiah Senkpiel) [#6167](https://github.com/nodejs/node/pull/6167)
- [[`ab84d69048`](https://github.com/nodejs/node/commit/ab84d69048)] - **doc**: better example & synopsis (Jeremiah Senkpiel) [#6167](https://github.com/nodejs/node/pull/6167)
- [[`f6d72791a1`](https://github.com/nodejs/node/commit/f6d72791a1)] - **doc**: update build instructions for OS X (Rich Trott) [#6309](https://github.com/nodejs/node/pull/6309)
- [[`36207c6daf`](https://github.com/nodejs/node/commit/36207c6daf)] - **doc**: correctly document the behavior of ee.once(). (Lance Ball) [#6371](https://github.com/nodejs/node/pull/6371)
- [[`19fb1345ba`](https://github.com/nodejs/node/commit/19fb1345ba)] - **doc**: use Buffer.from() instead of new Buffer() (Jackson Tian) [#6367](https://github.com/nodejs/node/pull/6367)
- [[`fb6753c75c`](https://github.com/nodejs/node/commit/fb6753c75c)] - **doc**: fix v6 changelog (James M Snell) [#6435](https://github.com/nodejs/node/pull/6435)
- [[`2c92a1fe03`](https://github.com/nodejs/node/commit/2c92a1fe03)] - **events**: pass the original listener added by once (DavidCai) [#6394](https://github.com/nodejs/node/pull/6394)
- [[`9ea6b282e8`](https://github.com/nodejs/node/commit/9ea6b282e8)] - **meta**: split CHANGELOG into two files (Myles Borins) [#6337](https://github.com/nodejs/node/pull/6337)
- [[`cbbe95e1e1`](https://github.com/nodejs/node/commit/cbbe95e1e1)] - **(SEMVER-MINOR)** **net**: introduce `Socket#connecting` property (Fedor Indutny) [#6404](https://github.com/nodejs/node/pull/6404)
- [[`534f03c2f0`](https://github.com/nodejs/node/commit/534f03c2f0)] - **openssl**: fix keypress requirement in apps on win32 (Shigeki Ohtsu) [iojs/io.js#1389](https://github.com/iojs/io.js/pull/1389)
- [[`1b9fdba04e`](https://github.com/nodejs/node/commit/1b9fdba04e)] - **(SEMVER-MINOR)** **process**: add process.cpuUsage() - implementation, doc, tests (Patrick Mueller) [#6157](https://github.com/nodejs/node/pull/6157)
- [[`fa9d82d120`](https://github.com/nodejs/node/commit/fa9d82d120)] - **src**: unify implementations of Utf8Value etc. (Anna Henningsen) [#6357](https://github.com/nodejs/node/pull/6357)
- [[`65030c77b7`](https://github.com/nodejs/node/commit/65030c77b7)] - **test**: fix alpn tests for openssl1.0.2h (Shigeki Ohtsu) [#6550](https://github.com/nodejs/node/pull/6550)
- [[`7641f9a6de`](https://github.com/nodejs/node/commit/7641f9a6de)] - **test**: refactor large event emitter tests (cjihrig) [#6446](https://github.com/nodejs/node/pull/6446)
- [[`5fe5fa2897`](https://github.com/nodejs/node/commit/5fe5fa2897)] - **test**: make addon testing part of `make test` (Ben Noordhuis) [#6232](https://github.com/nodejs/node/pull/6232)
- [[`457d12a0a1`](https://github.com/nodejs/node/commit/457d12a0a1)] - **test**: add failing url parse tests as known_issue (James M Snell) [#5885](https://github.com/nodejs/node/pull/5885)
- [[`089362f8b8`](https://github.com/nodejs/node/commit/089362f8b8)] - **test,tools**: limit lint tolerance of gc global (Rich Trott) [#6324](https://github.com/nodejs/node/pull/6324)
- [[`6d1606ee94`](https://github.com/nodejs/node/commit/6d1606ee94)] - **test,tools**: adjust function argument alignment (Rich Trott) [#6390](https://github.com/nodejs/node/pull/6390)
- [[`08e0884ae0`](https://github.com/nodejs/node/commit/08e0884ae0)] - **tools**: add -F flag for fixing lint issues (Rich Trott) [#6483](https://github.com/nodejs/node/pull/6483)
- [[`9f23cb24f2`](https://github.com/nodejs/node/commit/9f23cb24f2)] - **tools**: fix exit code when linting from CI (Brian White) [#6412](https://github.com/nodejs/node/pull/6412)
- [[`e62c42b8f4`](https://github.com/nodejs/node/commit/e62c42b8f4)] - **tools**: remove default parameters from lint rule (Rich Trott) [#6411](https://github.com/nodejs/node/pull/6411)
- [[`66903f6695`](https://github.com/nodejs/node/commit/66903f6695)] - **tools**: add tests for the doctool (Ian Kronquist) [#6031](https://github.com/nodejs/node/pull/6031)
- [[`3f608b16a7`](https://github.com/nodejs/node/commit/3f608b16a7)] - **tools**: lint for function argument alignment (Rich Trott) [#6390](https://github.com/nodejs/node/pull/6390)
- [[`91ab769940`](https://github.com/nodejs/node/commit/91ab769940)] - **(SEMVER-MINOR)** **util**: truncate inspect array and typed array (James M Snell) [#6334](https://github.com/nodejs/node/pull/6334)
- [[`0bca959617`](https://github.com/nodejs/node/commit/0bca959617)] - **(SEMVER-MINOR)** **util**: fix inspecting of proxy objects (James M Snell) [#6465](https://github.com/nodejs/node/pull/6465)

Windows 32-bit Installer: https://nodejs.org/dist/v6.1.0/node-v6.1.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v6.1.0/node-v6.1.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v6.1.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v6.1.0/win-x64/node.exe \
Mac OS X 64-bit Installer: https://nodejs.org/dist/v6.1.0/node-v6.1.0.pkg \
Mac OS X 64-bit Binary: https://nodejs.org/dist/v6.1.0/node-v6.1.0-darwin-x64.tar.gz \
Linux 32-bit Binary: https://nodejs.org/dist/v6.1.0/node-v6.1.0-linux-x86.tar.xz \
Linux 64-bit Binary: https://nodejs.org/dist/v6.1.0/node-v6.1.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v6.1.0/node-v6.1.0-linux-ppc64le.tar.xz \
SmartOS 32-bit Binary: https://nodejs.org/dist/v6.1.0/node-v6.1.0-sunos-x86.tar.xz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v6.1.0/node-v6.1.0-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v6.1.0/node-v6.1.0-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v6.1.0/node-v6.1.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v6.1.0/node-v6.1.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v6.1.0/node-v6.1.0.tar.gz \
Other release files: https://nodejs.org/dist/v6.1.0/ \
Documentation: https://nodejs.org/docs/v6.1.0/api/

Shasums (GPG signing hash: SHA512, file hash: SHA256):

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA512

481aeab001b6c79589daf3e1f8f5b9ad7021a3979b49fd842313010482ff7b56  node-v6.1.0-darwin-x64.tar.gz
a599974633ffbc8a3964699080d4f369297198eac4e18a990f801a6b42b742e6  node-v6.1.0-darwin-x64.tar.xz
9419fce25791df7afd27d173a1148ef9f53c349cd9a2e8d9bdac5276d80a1bb5  node-v6.1.0-headers.tar.gz
8e35bf983def6f7bc321fbabcd3bf5e7909e30417cb9c43f31171a63cf664b10  node-v6.1.0-headers.tar.xz
87670387877d1cbe36642970e3ca84a77121d3200771b80ec2286bc261e060f7  node-v6.1.0-linux-arm64.tar.gz
7fde2a7b2b7d96d14d3f3f655474434af38583b471973ba3ff07ae29a1f0d821  node-v6.1.0-linux-arm64.tar.xz
aeff5e4b77fba25feb19ec4739116373234e93aff0d2c9662c4ebd87fa4b34ae  node-v6.1.0-linux-armv6l.tar.gz
856120ca6e03501c29fadced18f737487092ad75845247e8e2feea30b97f53c9  node-v6.1.0-linux-armv6l.tar.xz
55d7a2e5071b13db6fe5d952dc5ee5d2223ae4a0de2f2c2d7a68d65e1cfa377b  node-v6.1.0-linux-armv7l.tar.gz
ee9583ee94c602b63d758edb564e944a24d4cadac38bde95b25e4995362297e8  node-v6.1.0-linux-armv7l.tar.xz
bca98e8a4f5c11dee9eb4fec2bd0a42c1f8a921863bba417fc88c7274a0c0c04  node-v6.1.0-linux-ppc64le.tar.gz
5b2b85bbc2f289d3f6c458fa77cdf7c74befccfade02c7a287c6992addcbf3f0  node-v6.1.0-linux-ppc64le.tar.xz
ce46dd0188181b70661b9162feffdbd8a860cb75cb6661c37d6d61982e3f72c5  node-v6.1.0-linux-x64.tar.gz
6ae9d08f8c606810e17db6a6c9ff2689072e27682a0e1875cbb75effa8ae3d5e  node-v6.1.0-linux-x64.tar.xz
07d6f0b11b98c876f2d029722696da6cc9b93a2ce6b34abb7a517e0afb04fb34  node-v6.1.0-linux-x86.tar.gz
41b34eef859fb0d39498bc900778f5e692fe4367b7d1588df3ae1f1f98bc4330  node-v6.1.0-linux-x86.tar.xz
b58bfcbce73dc04342a465dca92d802bf5e437f7da224b6157d6c87fe805c5f6  node-v6.1.0.pkg
d7cbb0bfe0915ac7bdb37e87ea7145f6e86ef2943e77f306c648e47d3d33c898  node-v6.1.0-sunos-x64.tar.gz
930596fb6ddb8aedc3c358002786f207f30b3d7f0b377766d5017452ea36196f  node-v6.1.0-sunos-x64.tar.xz
38ec9537ab2fbf6112365b3a291ebe962d14b384b474e54fa77227b2e98624c9  node-v6.1.0-sunos-x86.tar.gz
254c080b41db83979d200c4ab28fbbc667b8528d9e260ce9f2599293bb17e4a0  node-v6.1.0-sunos-x86.tar.xz
9e67ef0b8611e16e6e311eccf0489a50fe76ceebeea3023ef4f51be647ae4bc3  node-v6.1.0.tar.gz
c7940301917232527490a166df78f9d3d58e70e10fd502f73889d936763cfe84  node-v6.1.0.tar.xz
b32b1105da5c08023976717d9aaeb0e3ba93d09f170aa3d81ad8ddfb0abfbdd4  node-v6.1.0-x64.msi
26b762f6066feeae59107c064eeaf70019880cb113279d51e35dff46c6c81be2  node-v6.1.0-x86.msi
a0f1fa671ae76821dd1098fb67bbc9601bdf5229a1dac5585441d279765d0d20  win-x64/node.exe
135215ad3ff8774386d6525e69d62ca3a24da5a7962f1464e1b5758b58583eb3  win-x64/node.lib
a6391706a5ac17bcf9f8ff5fb9c0359b6787dcfd45a6089f91d36edb49738c69  win-x86/node.exe
17608b03161b6fd3e956ff0a53da0a0d483a3860fa236eb8e1eaf205158916fc  win-x86/node.lib
-----BEGIN PGP SIGNATURE-----
Comment: GPGTools - https://gpgtools.org

iQIcBAEBCgAGBQJXLLHaAAoJEEX17r2BPa6O3C4P+wXrjmby0cm/K1HLpmCtitMu
4aMpt1/MlVVZjlCrm/vtvt56L7KznU0BgMq0DjJDvmWuWOs6wwWHj9SF69BvsHXV
A9tKuVHSKq/S9dFqv+Ucz3L+ITYCZSpZubKCfUbRPvCMTl4Aw1lF+JwCzpiB9GCx
kxJdN+HVuHJonmcSku1V/lP/zSylkHWGC4YvRwmRcZMIPedTfvYtIWHzRXIpMwjN
WD2BSiHGqqh0U2KFe+drwrZZEYjLfS0urwgIsdnS+BIfDATaMyP5W++iKZ1G9QvD
bbi+Lyye9jnJNhJvFUHA5df33jn3avfBMFgsToFqWw+nmLTrJyco+kaEBSSnhD5l
sl1S/Vc7jrZgX1Hz4iiPM8t6c8h1hH/eck5HuqQcP114x/EP3LPlKGdy3MRZcvWO
VCrI9A3zQitbmiwNC1kml6S50/XrRJL/bLrBPqlg2p42t/LMsq6KKfUDzE9ibooR
O7gxMd6Bk4B2R8RQtF0uQ6X/KQK4U65RKRjgQznMK+eQfJ/z4D1i1lr2APp8QwiS
UXMG4zLnJThb7G6z1Z2HtS5RL+0LuOaFj5MH4ft7PTAhvaVybGeGb+qv5s2Hhva/
RAvkB/wyx/3uCLAJDfDYSH+lFu9hak0+eLY59Qv9hVziTqIOXSTuxiumVAu/gWhS
MWdeZRgt8pObdLGV1eaW
=/0Ti
-----END PGP SIGNATURE-----

```
