---
date: '2017-09-12T15:35:38.706Z'
category: release
title: Node v8.5.0 (Current)
layout: blog-post
author: <PERSON><PERSON>
---

### Notable Changes

- **build**
  - Snapshots are now re-enabled in V8
    [#14875](https://github.com/nodejs/node/pull/14875)
- **console**
  - Implement minimal `console.group()`.
    [#14910](https://github.com/nodejs/node/pull/14910)
- **deps**
  - upgrade libuv to 1.14.1
    [#14866](https://github.com/nodejs/node/pull/14866)
  - update nghttp2 to v1.25.0
    [#14955](https://github.com/nodejs/node/pull/14955)
- **dns**
  - Add `verbatim` option to dns.lookup(). When true, results from the DNS
    resolver are passed on as-is, without the reshuffling that Node.js
    otherwise does that puts IPv4 addresses before IPv6 addresses.
    [#14731](https://github.com/nodejs/node/pull/14731)
- **fs**
  - add fs.copyFile and fs.copyFileSync which allows for more efficient
    copying of files.
    [#15034](https://github.com/nodejs/node/pull/15034)
- **inspector**
  - Enable async stack traces [#13870](https://github.com/nodejs/node/pull/13870)
- **module**
  - Add support for ESM. This is currently behind the `--experimental-modules` flag
    and requires the .mjs extension.
    `node --experimental-modules index.mjs`
    [#14369](https://github.com/nodejs/node/pull/14369)
- **napi**
  - implement promise
    [#14365](https://github.com/nodejs/node/pull/14365)
- **os**
  - Add support for CIDR notation to the output of the networkInterfaces() method.
    [#14307](https://github.com/nodejs/node/pull/14307)
- **perf_hooks**
  - An initial implementation of the Performance Timing API for Node.js. This is the
    same Performance Timing API implemented by modern browsers with a number of Node.js
    specific properties. The User Timing mark() and measure() APIs are implemented.
    [#14680](https://github.com/nodejs/node/pull/14680)
- **tls**
  - multiple PFX in createSecureContext
    [#14793](https://github.com/nodejs/node/pull/14793)
- **Added new collaborators**
  - [BridgeAR](https://github.com/BridgeAR) – Ruben Bridgewater

### Commits

- [[`87c3e1d7de`](https://github.com/nodejs/node/commit/87c3e1d7de)] - fix --prof-process --preprocess flag (davidmarkclements) [#14966](https://github.com/nodejs/node/pull/14966)
- [[`bcf0e5d676`](https://github.com/nodejs/node/commit/bcf0e5d676)] - **assert**: handle errors properly with deep\*Equal (Ruben Bridgewater) [#15001](https://github.com/nodejs/node/pull/15001)
- [[`7174dc2e8a`](https://github.com/nodejs/node/commit/7174dc2e8a)] - **assert**: handle sparse arrays in deepStrictEqual (Ruben Bridgewater) [#15027](https://github.com/nodejs/node/pull/15027)
- [[`b40105df3b`](https://github.com/nodejs/node/commit/b40105df3b)] - **async_hooks**: don't abort unnecessarily (Trevor Norris) [#14722](https://github.com/nodejs/node/pull/14722)
- [[`3e73ea8745`](https://github.com/nodejs/node/commit/3e73ea8745)] - **async_hooks**: improve comments and function names (Trevor Norris) [#14722](https://github.com/nodejs/node/pull/14722)
- [[`700d576962`](https://github.com/nodejs/node/commit/700d576962)] - **async_hooks**: emitAfter correctly on fatalException (Trevor Norris) [#14914](https://github.com/nodejs/node/pull/14914)
- [[`78a36e0dd1`](https://github.com/nodejs/node/commit/78a36e0dd1)] - **async_wrap**: unroll unnecessarily DRY code (Trevor Norris) [#14722](https://github.com/nodejs/node/pull/14722)
- [[`fadccbaa17`](https://github.com/nodejs/node/commit/fadccbaa17)] - **async_wrap**: return undefined if domain is disposed (Trevor Norris) [#14722](https://github.com/nodejs/node/pull/14722)
- [[`8d11220e0b`](https://github.com/nodejs/node/commit/8d11220e0b)] - **benchmark**: add default configs to buffer benchmark (Rich Trott) [#15175](https://github.com/nodejs/node/pull/15175)
- [[`7feb99455a`](https://github.com/nodejs/node/commit/7feb99455a)] - **benchmark**: fix issues in dns benchmark (Ian Perkins) [#14936](https://github.com/nodejs/node/pull/14936)
- [[`978889f8c0`](https://github.com/nodejs/node/commit/978889f8c0)] - **benchmark**: fix dgram/bind-params.js benchmark (Rich Trott) [#14948](https://github.com/nodejs/node/pull/14948)
- [[`7f1ea7c3af`](https://github.com/nodejs/node/commit/7f1ea7c3af)] - **benchmark**: removed unused arguments from callbacks (Abhishek Raj) [#14919](https://github.com/nodejs/node/pull/14919)
- [[`ca3ec90285`](https://github.com/nodejs/node/commit/ca3ec90285)] - **benchmark**: convert var to es6 const (Sebastian Murphy) [#12886](https://github.com/nodejs/node/pull/12886)
- [[`bda5585012`](https://github.com/nodejs/node/commit/bda5585012)] - **buffer**: fix MAX_LENGTH constant export (Anna Henningsen) [#14821](https://github.com/nodejs/node/pull/14821)
- [[`b9e1f60333`](https://github.com/nodejs/node/commit/b9e1f60333)] - **buffer**: increase coverage by removing dead code (Marcelo Gobelli) [#15100](https://github.com/nodejs/node/pull/15100)
- [[`5b8fa29649`](https://github.com/nodejs/node/commit/5b8fa29649)] - **build**: display HTTP2 configure --help options (Daniel Bevenius) [#15198](https://github.com/nodejs/node/pull/15198)
- [[`6de4e10c7a`](https://github.com/nodejs/node/commit/6de4e10c7a)] - **build**: add NetBSD support to opensslconf.h (Roy Marples) [#14313](https://github.com/nodejs/node/pull/14313)
- [[`ebb3c2ce6f`](https://github.com/nodejs/node/commit/ebb3c2ce6f)] - **build**: add npx to zip and 7z packages (Richard Lau) [#15033](https://github.com/nodejs/node/pull/15033)
- [[`b946693f4b`](https://github.com/nodejs/node/commit/b946693f4b)] - **build**: fix indentation in node.gyp (Alexey Orlenko) [#15051](https://github.com/nodejs/node/pull/15051)
- [[`c8be90cabf`](https://github.com/nodejs/node/commit/c8be90cabf)] - **build**: for --enable-static, run only cctest (Daniel Bevenius) [#14892](https://github.com/nodejs/node/pull/14892)
- [[`77dfa73cf2`](https://github.com/nodejs/node/commit/77dfa73cf2)] - **build**: better support for python3 systems (Ben Noordhuis) [#14737](https://github.com/nodejs/node/pull/14737)
- [[`8f3537f66a`](https://github.com/nodejs/node/commit/8f3537f66a)] - **build**: allow proper generation of html docs (Jon Moss) [#14932](https://github.com/nodejs/node/pull/14932)
- [[`838d3fef72`](https://github.com/nodejs/node/commit/838d3fef72)] - **build**: don't add libraries when --enable-static (Daniel Bevenius) [#14912](https://github.com/nodejs/node/pull/14912)
- [[`9d373981f4`](https://github.com/nodejs/node/commit/9d373981f4)] - **build**: remove duplicated code (Ruslan Bekenev) [#13482](https://github.com/nodejs/node/pull/13482)
- [[`e12a9c567c`](https://github.com/nodejs/node/commit/e12a9c567c)] - **build**: re-enable snapshots in v8.x (Myles Borins) [#14875](https://github.com/nodejs/node/pull/14875)
- [[`3a68b0bb98`](https://github.com/nodejs/node/commit/3a68b0bb98)] - **console**: improve console.group() (Rich Trott) [#14999](https://github.com/nodejs/node/pull/14999)
- [[`a46e59d52d`](https://github.com/nodejs/node/commit/a46e59d52d)] - **(SEMVER-MINOR)** **console**: implement minimal `console.group()` (Rich Trott) [#14910](https://github.com/nodejs/node/pull/14910)
- [[`78a71aa123`](https://github.com/nodejs/node/commit/78a71aa123)] - **crypto**: fix error of createCipher in wrap mode (Shigeki Ohtsu) [#15037](https://github.com/nodejs/node/pull/15037)
- [[`41bf40e209`](https://github.com/nodejs/node/commit/41bf40e209)] - **crypto**: warn if counter mode used in createCipher (Shigeki Ohtsu) [#13821](https://github.com/nodejs/node/pull/13821)
- [[`ba5a697bdb`](https://github.com/nodejs/node/commit/ba5a697bdb)] - **deps**: cherry-pick 5005faed5 from V8 upstream (Miguel Martins) [#15177](https://github.com/nodejs/node/pull/15177)
- [[`d18bb3d1dd`](https://github.com/nodejs/node/commit/d18bb3d1dd)] - **deps**: cherry-pick 1aead19 from upstream V8 (Ben Noordhuis) [#15184](https://github.com/nodejs/node/pull/15184)
- [[`acf9650730`](https://github.com/nodejs/node/commit/acf9650730)] - **deps**: upgrade libuv to 1.14.1 (cjihrig) [#14866](https://github.com/nodejs/node/pull/14866)
- [[`296729c41e`](https://github.com/nodejs/node/commit/296729c41e)] - **deps**: cherry-pick 0ef4a0c64b6 from c-ares upstream (Anna Henningsen) [#15023](https://github.com/nodejs/node/pull/15023)
- [[`3f7bdc5ab7`](https://github.com/nodejs/node/commit/3f7bdc5ab7)] - **deps**: cherry-pick e020aae394 from V8 upstream (Ben Noordhuis) [#14913](https://github.com/nodejs/node/pull/14913)
- [[`c46e7e1988`](https://github.com/nodejs/node/commit/c46e7e1988)] - **deps**: fixup nghttp2 version number (Anna Henningsen) [#14955](https://github.com/nodejs/node/pull/14955)
- [[`4eb907f26b`](https://github.com/nodejs/node/commit/4eb907f26b)] - **deps**: update nghttp2 to v1.25.0 (Anna Henningsen) [#14955](https://github.com/nodejs/node/pull/14955)
- [[`9f46bde440`](https://github.com/nodejs/node/commit/9f46bde440)] - **deps**: backport d727680 from V8 upstream (Matt Loring) [#14947](https://github.com/nodejs/node/pull/14947)
- [[`56bb199ef0`](https://github.com/nodejs/node/commit/56bb199ef0)] - **deps**: cherry-pick eb306f463e from nghttp2 upstream (Anna Henningsen) [#14808](https://github.com/nodejs/node/pull/14808)
- [[`55eed604a9`](https://github.com/nodejs/node/commit/55eed604a9)] - **deps**: backport f9c4b7a from upstream V8 (Matt Loring) [#14001](https://github.com/nodejs/node/pull/14001)
- [[`b7f7d67677`](https://github.com/nodejs/node/commit/b7f7d67677)] - **deps**: backport bca8409 from upstream V8 (Matt Loring) [#14001](https://github.com/nodejs/node/pull/14001)
- [[`a67e7f9b35`](https://github.com/nodejs/node/commit/a67e7f9b35)] - **deps**: backport 6e9e2e5 from upstream V8 (Matt Loring) [#14001](https://github.com/nodejs/node/pull/14001)
- [[`6e2f62262d`](https://github.com/nodejs/node/commit/6e2f62262d)] - **deps**: backport 3d8e87a from upstream V8 (Matt Loring) [#14001](https://github.com/nodejs/node/pull/14001)
- [[`6cb718b87a`](https://github.com/nodejs/node/commit/6cb718b87a)] - **deps**: backport 5152d97 from upstream V8 (Matt Loring) [#14001](https://github.com/nodejs/node/pull/14001)
- [[`c6e2b8adf7`](https://github.com/nodejs/node/commit/c6e2b8adf7)] - **deps**: backport c4852ea from upstream V8 (Matt Loring) [#14001](https://github.com/nodejs/node/pull/14001)
- [[`bfb97b71b6`](https://github.com/nodejs/node/commit/bfb97b71b6)] - **deps**: cherry-pick fa4ec9f from V8 upstream (Jaideep Bajwa) [#14608](https://github.com/nodejs/node/pull/14608)
- [[`1a2f749e16`](https://github.com/nodejs/node/commit/1a2f749e16)] - **deps**: fix inspector v8 test (Eugene Ostroukhov) [#14827](https://github.com/nodejs/node/pull/14827)
- [[`13577d4ada`](https://github.com/nodejs/node/commit/13577d4ada)] - **dns**: add `verbatim` option to dns.lookup() (Ben Noordhuis) [#14731](https://github.com/nodejs/node/pull/14731)
- [[`ffed33710c`](https://github.com/nodejs/node/commit/ffed33710c)] - **doc**: add ESM doc to \_toc.md and all.md (Vse Mozhet Byt) [#15248](https://github.com/nodejs/node/pull/15248)
- [[`1b51287603`](https://github.com/nodejs/node/commit/1b51287603)] - **doc**: fix Error property markdown level (Sam Roberts) [#15247](https://github.com/nodejs/node/pull/15247)
- [[`af3b173e82`](https://github.com/nodejs/node/commit/af3b173e82)] - **doc**: add missing space in test/README.md (Vse Mozhet Byt) [#15278](https://github.com/nodejs/node/pull/15278)
- [[`c90c68e8a0`](https://github.com/nodejs/node/commit/c90c68e8a0)] - **doc**: document bytes to chars after setEncoding (Jessica Quynh Tran) [#13442](https://github.com/nodejs/node/pull/13442)
- [[`ea86cb59b9`](https://github.com/nodejs/node/commit/ea86cb59b9)] - **doc**: describe what security issues are (Sam Roberts) [#14485](https://github.com/nodejs/node/pull/14485)
- [[`ddbcc9e59d`](https://github.com/nodejs/node/commit/ddbcc9e59d)] - **doc**: add options argument to crypto docs (Adina Shanholtz) [#14846](https://github.com/nodejs/node/pull/14846)
- [[`da5e6d33d5`](https://github.com/nodejs/node/commit/da5e6d33d5)] - **doc**: instructions for generating coverage reports (Simon Brewster) [#15190](https://github.com/nodejs/node/pull/15190)
- [[`286111a2b0`](https://github.com/nodejs/node/commit/286111a2b0)] - **doc**: clarify async/asynchronous in deprecations.md (Rich Trott) [#15172](https://github.com/nodejs/node/pull/15172)
- [[`9542844feb`](https://github.com/nodejs/node/commit/9542844feb)] - **doc**: `readFileSync` instead of `fs.readFileSync` (Piotr Mionskowski) [#15137](https://github.com/nodejs/node/pull/15137)
- [[`959b270fe1`](https://github.com/nodejs/node/commit/959b270fe1)] - **doc**: /s/SHASUM256/SHASUMS256 (Jon Moss) [#15101](https://github.com/nodejs/node/pull/15101)
- [[`3697cd86c4`](https://github.com/nodejs/node/commit/3697cd86c4)] - **doc**: fix comment about http2.createSecureServer (creeperyang) [#15085](https://github.com/nodejs/node/pull/15085)
- [[`76780445b3`](https://github.com/nodejs/node/commit/76780445b3)] - **doc**: remove braces which shouldn't be there (Jan Schär) [#15094](https://github.com/nodejs/node/pull/15094)
- [[`2610ae326f`](https://github.com/nodejs/node/commit/2610ae326f)] - **doc**: clarify http.get data consumption requirement (AJ Jordan) [#15049](https://github.com/nodejs/node/pull/15049)
- [[`e7838d7077`](https://github.com/nodejs/node/commit/e7838d7077)] - **doc**: add 8.4.0 link to CHANGELOG.md (Ruslan Iusupov) [#15064](https://github.com/nodejs/node/pull/15064)
- [[`feeff48d5c`](https://github.com/nodejs/node/commit/feeff48d5c)] - **doc**: add links to alternative versions of doc (Chris Young) [#10958](https://github.com/nodejs/node/pull/10958)
- [[`a5242851b9`](https://github.com/nodejs/node/commit/a5242851b9)] - **doc**: update configure to require g++ 4.9.4 (Dave Olszewski) [#14204](https://github.com/nodejs/node/pull/14204)
- [[`87ff86b2d8`](https://github.com/nodejs/node/commit/87ff86b2d8)] - **doc**: building - note on Windows SDK 15063 (Refael Ackermann) [#14394](https://github.com/nodejs/node/pull/14394)
- [[`449549bc4f`](https://github.com/nodejs/node/commit/449549bc4f)] - **doc**: threadpool size, and APIs using the pool (Sam Roberts) [#14995](https://github.com/nodejs/node/pull/14995)
- [[`6bb8133638`](https://github.com/nodejs/node/commit/6bb8133638)] - **doc**: sort bottom-of-file dns markdown links (Sam Roberts) [#14992](https://github.com/nodejs/node/pull/14992)
- [[`a06d1295c5`](https://github.com/nodejs/node/commit/a06d1295c5)] - **doc**: crypto.randomBytes does not block when async (Sam Roberts) [#14993](https://github.com/nodejs/node/pull/14993)
- [[`83ba2aa46b`](https://github.com/nodejs/node/commit/83ba2aa46b)] - **doc**: environmental-\>environment & NodeJS-\>Node.js (Rod Vagg) [#14974](https://github.com/nodejs/node/pull/14974)
- [[`f1bc168ad5`](https://github.com/nodejs/node/commit/f1bc168ad5)] - **doc**: fix typo in Buffer.from(string, \[encoding\]) (Michał Wadas) [#15013](https://github.com/nodejs/node/pull/15013)
- [[`9b9e7b4044`](https://github.com/nodejs/node/commit/9b9e7b4044)] - **doc**: add note for Windows build path (Kyle Lamse) [#14354](https://github.com/nodejs/node/pull/14354)
- [[`57c7eae1df`](https://github.com/nodejs/node/commit/57c7eae1df)] - **doc**: rephrase text of child_process.execSync() (hafiz) [#14953](https://github.com/nodejs/node/pull/14953)
- [[`188713ca46`](https://github.com/nodejs/node/commit/188713ca46)] - **doc**: beautify net.md formats (sevenryze) [#14987](https://github.com/nodejs/node/pull/14987)
- [[`a8648e287c`](https://github.com/nodejs/node/commit/a8648e287c)] - **doc**: link to correct "OS Constants" heading in docs (James Kyle) [#14969](https://github.com/nodejs/node/pull/14969)
- [[`e187c98186`](https://github.com/nodejs/node/commit/e187c98186)] - **doc**: remove misterdjules from the CTC members list (Julien Gilli) [#1498](https://github.com/nodejs/node/pull/1498)
- [[`78b2bc77f2`](https://github.com/nodejs/node/commit/78b2bc77f2)] - **doc**: update http2.md example code (RefinedSoftwareLLC) [#14979](https://github.com/nodejs/node/pull/14979)
- [[`6179c2764a`](https://github.com/nodejs/node/commit/6179c2764a)] - **doc**: fix doc for napi_get_value_string_utf8 (Daniel Taveras) [#14529](https://github.com/nodejs/node/pull/14529)
- [[`daae6bc652`](https://github.com/nodejs/node/commit/daae6bc652)] - **doc**: fixed link definitions in http2.md footer (sharababy) [#14946](https://github.com/nodejs/node/pull/14946)
- [[`6c93d01fba`](https://github.com/nodejs/node/commit/6c93d01fba)] - **doc**: remove `you` and fixup note in stream.md (James M Snell) [#14938](https://github.com/nodejs/node/pull/14938)
- [[`96d95d4fed`](https://github.com/nodejs/node/commit/96d95d4fed)] - **doc**: minor fixes to http/2 docs (Anand Suresh) [#14877](https://github.com/nodejs/node/pull/14877)
- [[`bfa3cbe158`](https://github.com/nodejs/node/commit/bfa3cbe158)] - **doc**: remove redundant only from doc/api/stream.md (George Sapkin) [#14858](https://github.com/nodejs/node/pull/14858)
- [[`c5380c83c6`](https://github.com/nodejs/node/commit/c5380c83c6)] - **doc**: add missing word (Jon Moss) [#14924](https://github.com/nodejs/node/pull/14924)
- [[`abe014834e`](https://github.com/nodejs/node/commit/abe014834e)] - **doc**: fix http api document (陈刚) [#14625](https://github.com/nodejs/node/pull/14625)
- [[`050a2249c1`](https://github.com/nodejs/node/commit/050a2249c1)] - **doc**: explain what to do if git push is rejected (Rich Trott) [#14848](https://github.com/nodejs/node/pull/14848)
- [[`3d621393bd`](https://github.com/nodejs/node/commit/3d621393bd)] - **doc**: add BridgeAR to collaborators (Ruben Bridgewater) [#14862](https://github.com/nodejs/node/pull/14862)
- [[`c8f0e5ab82`](https://github.com/nodejs/node/commit/c8f0e5ab82)] - **doc**: fix typo in cli.md (hsmtkk) [#14855](https://github.com/nodejs/node/pull/14855)
- [[`0dc9d284a4`](https://github.com/nodejs/node/commit/0dc9d284a4)] - **doc**: added napi_get_value_string_latin1 (Kyle Farnung) [#14678](https://github.com/nodejs/node/pull/14678)
- [[`72cc2caf78`](https://github.com/nodejs/node/commit/72cc2caf78)] - **doc**: fix word wrapping for api stability boxes (Saad Quadri) [#14809](https://github.com/nodejs/node/pull/14809)
- [[`205d5f674a`](https://github.com/nodejs/node/commit/205d5f674a)] - **doc,fs**: rename defaultEncoding option to encoding (Aleh Zasypkin) [#14867](https://github.com/nodejs/node/pull/14867)
- [[`aaf55db95b`](https://github.com/nodejs/node/commit/aaf55db95b)] - **doc,lib,src,test**: strip executable bits off files (Anna Henningsen) [#15132](https://github.com/nodejs/node/pull/15132)
- [[`7f62378e76`](https://github.com/nodejs/node/commit/7f62378e76)] - **doc,stream**: remove wrong remark on readable.read (Jan Schär) [#15014](https://github.com/nodejs/node/pull/15014)
- [[`ea2b5760d5`](https://github.com/nodejs/node/commit/ea2b5760d5)] - **errors**: remove duplicated ERR_HTTP_INVALID_STATUS_CODE error (Jon Moss) [#15003](https://github.com/nodejs/node/pull/15003)
- [[`71f90c6f80`](https://github.com/nodejs/node/commit/71f90c6f80)] - **(SEMVER-MINOR)** **fs**: add fs.copyFile{Sync} (cjihrig) [#15034](https://github.com/nodejs/node/pull/15034)
- [[`3d9ad82729`](https://github.com/nodejs/node/commit/3d9ad82729)] - **gyp**: fix ninja build failure (GYP patch) (Daniel Bevenius) [#12484](https://github.com/nodejs/node/pull/12484)
- [[`12191f6ed8`](https://github.com/nodejs/node/commit/12191f6ed8)] - **gyp**: enable cctest to use objects (gyp part) (Daniel Bevenius) [#12450](https://github.com/nodejs/node/pull/12450)
- [[`538894978b`](https://github.com/nodejs/node/commit/538894978b)] - **gyp**: add compile_commands.json gyp generator (Ben Noordhuis) [#12450](https://github.com/nodejs/node/pull/12450)
- [[`7eb3679eea`](https://github.com/nodejs/node/commit/7eb3679eea)] - **gyp**: inherit parent for `*.host` (Johan Bergström) [#6173](https://github.com/nodejs/node/pull/6173)
- [[`5fb252a5a2`](https://github.com/nodejs/node/commit/5fb252a5a2)] - **gyp**: fix gyp to work on MacOSX without XCode (Shigeki Ohtsu) [iojs/io.js#1325](https://github.com/iojs/io.js/pull/1325)
- [[`0343eceda4`](https://github.com/nodejs/node/commit/0343eceda4)] - **http2**: fix refs to status 205, add tests (Anatoli Papirovski) [#15153](https://github.com/nodejs/node/pull/15153)
- [[`d8ff550528`](https://github.com/nodejs/node/commit/d8ff550528)] - **http2**: store headersSent after stream destroyed (Anatoli Papirovski) [#15232](https://github.com/nodejs/node/pull/15232)
- [[`4882f079f1`](https://github.com/nodejs/node/commit/4882f079f1)] - **http2**: set decodeStrings to false, test (Anatoli Papirovski) [#15140](https://github.com/nodejs/node/pull/15140)
- [[`93a4cf60ff`](https://github.com/nodejs/node/commit/93a4cf60ff)] - **http2**: use session not socket timeout, tests (Anatoli Papirovski) [#15188](https://github.com/nodejs/node/pull/15188)
- [[`764213cc7b`](https://github.com/nodejs/node/commit/764213cc7b)] - **http2**: add compat trailers, adjust multi-headers (Anatoli Papirovski) [#15193](https://github.com/nodejs/node/pull/15193)
- [[`cc82f541e5`](https://github.com/nodejs/node/commit/cc82f541e5)] - **http2**: fix closedCode NaN, increase test coverage (Anatoli Papirovski) [#15154](https://github.com/nodejs/node/pull/15154)
- [[`afa72dfdf3`](https://github.com/nodejs/node/commit/afa72dfdf3)] - **http2**: guard against destroyed session, timeouts (James M Snell) [#15106](https://github.com/nodejs/node/pull/15106)
- [[`f6c51888db`](https://github.com/nodejs/node/commit/f6c51888db)] - **http2**: correct emit error in onConnect, full tests (Anatoli Papirovski) [#15080](https://github.com/nodejs/node/pull/15080)
- [[`fd51cb8ca3`](https://github.com/nodejs/node/commit/fd51cb8ca3)] - **http2**: adjust error types, test coverage (Anatoli Papirovski) [#15109](https://github.com/nodejs/node/pull/15109)
- [[`f612a6dd5c`](https://github.com/nodejs/node/commit/f612a6dd5c)] - **http2**: handle 100-continue flow & writeContinue (Anatoli Papirovski) [#15039](https://github.com/nodejs/node/pull/15039)
- [[`989dfaf930`](https://github.com/nodejs/node/commit/989dfaf930)] - **http2**: refactor error handling (Matteo Collina) [#14991](https://github.com/nodejs/node/pull/14991)
- [[`d231ef645e`](https://github.com/nodejs/node/commit/d231ef645e)] - **http2**: ignore invalid headers explicitly (Anna Henningsen) [#14955](https://github.com/nodejs/node/pull/14955)
- [[`1b57c375aa`](https://github.com/nodejs/node/commit/1b57c375aa)] - **http2**: minor refactor of passing headers to JS (Anna Henningsen) [#14808](https://github.com/nodejs/node/pull/14808)
- [[`80fe40aabf`](https://github.com/nodejs/node/commit/80fe40aabf)] - **http2**: handful of http/2 src cleanups (James M Snell) [#14825](https://github.com/nodejs/node/pull/14825)
- [[`9589641c5c`](https://github.com/nodejs/node/commit/9589641c5c)] - **http2**: Expose Http2ServerRequest/Response (Pini Houri) [#14690](https://github.com/nodejs/node/pull/14690)
- [[`8c61b72f90`](https://github.com/nodejs/node/commit/8c61b72f90)] - **(SEMVER-MINOR)** **inspector**: enable async stack traces (Miroslav Bajtoš) [#13870](https://github.com/nodejs/node/pull/13870)
- [[`e2ae08b48d`](https://github.com/nodejs/node/commit/e2ae08b48d)] - **inspector**: rewrite inspector test helper (Eugene Ostroukhov) [#14797](https://github.com/nodejs/node/pull/14797)
- [[`105acf4af7`](https://github.com/nodejs/node/commit/105acf4af7)] - **inspector**: log exceptions in message handlers (Eugene Ostroukhov) [#14980](https://github.com/nodejs/node/pull/14980)
- [[`d5a376ab7a`](https://github.com/nodejs/node/commit/d5a376ab7a)] - **lib**: remove circular reference (Ruben Bridgewater) [#14885](https://github.com/nodejs/node/pull/14885)
- [[`605d625e62`](https://github.com/nodejs/node/commit/605d625e62)] - **lib**: simplify the readonly properties of icu (Jackson Tian) [#13221](https://github.com/nodejs/node/pull/13221)
- [[`ea0a882041`](https://github.com/nodejs/node/commit/ea0a882041)] - **lib**: remove the invalid command line options (Jackson Tian) [#13764](https://github.com/nodejs/node/pull/13764)
- [[`9129057e03`](https://github.com/nodejs/node/commit/9129057e03)] - **lib**: clean up usage of threw (Jackson Tian) [#10534](https://github.com/nodejs/node/pull/10534)
- [[`f34e0f97e7`](https://github.com/nodejs/node/commit/f34e0f97e7)] - **lib**: instantiate console methods eagerly (Ben Noordhuis) [#14791](https://github.com/nodejs/node/pull/14791)
- [[`01846a06c2`](https://github.com/nodejs/node/commit/01846a06c2)] - **meta**: merge TSC and CTC back into a single body (James M Snell) [#14973](https://github.com/nodejs/node/pull/14973)
- [[`859abe5169`](https://github.com/nodejs/node/commit/859abe5169)] - **meta**: considerations for new core modules (James M Snell) [#15022](https://github.com/nodejs/node/pull/15022)
- [[`cc72118e71`](https://github.com/nodejs/node/commit/cc72118e71)] - **meta**: improve definition of a collaborator (James M Snell) [#14981](https://github.com/nodejs/node/pull/14981)
- [[`865a3c3daf`](https://github.com/nodejs/node/commit/865a3c3daf)] - **(SEMVER-MINOR)** **module**: Allow runMain to be ESM (Bradley Farias) [#14369](https://github.com/nodejs/node/pull/14369)
- [[`4bf0d4e133`](https://github.com/nodejs/node/commit/4bf0d4e133)] - **n-api**: implement napi_run_script (Gabriel Schulhof) [#15216](https://github.com/nodejs/node/pull/15216)
- [[`3a18df0750`](https://github.com/nodejs/node/commit/3a18df0750)] - **n-api**: adds function to adjust external memory (Chris Young) [#14310](https://github.com/nodejs/node/pull/14310)
- [[`503370e2d3`](https://github.com/nodejs/node/commit/503370e2d3)] - **(SEMVER-MINOR)** **n-api**: implement promise (Gabriel Schulhof) [#14365](https://github.com/nodejs/node/pull/14365)
- [[`a6344d5a83`](https://github.com/nodejs/node/commit/a6344d5a83)] - **(SEMVER-MINOR)** **n-api**: add ability to remove a wrapping (Gabriel Schulhof) [#14658](https://github.com/nodejs/node/pull/14658)
- [[`67fde146e0`](https://github.com/nodejs/node/commit/67fde146e0)] - **net**: check EADDRINUSE after binding localPort (Joyee Cheung) [#15097](https://github.com/nodejs/node/pull/15097)
- [[`b4e8850576`](https://github.com/nodejs/node/commit/b4e8850576)] - **net**: move debug statement (Brian White) [#12616](https://github.com/nodejs/node/pull/12616)
- [[`136eea4bcb`](https://github.com/nodejs/node/commit/136eea4bcb)] - **(SEMVER-MINOR)** **os**: add CIDR support (Mudit Ameta) [#14307](https://github.com/nodejs/node/pull/14307)
- [[`29f9101a0f`](https://github.com/nodejs/node/commit/29f9101a0f)] - **path**: fix normalize on directories with two dots (Michaël Zasso) [#14107](https://github.com/nodejs/node/pull/14107)
- [[`e3f5c58423`](https://github.com/nodejs/node/commit/e3f5c58423)] - **perf_hooks**: fix presumed typo in node_perf.cc (Anna Henningsen) [#15019](https://github.com/nodejs/node/pull/15019)
- [[`69e3bc64cc`](https://github.com/nodejs/node/commit/69e3bc64cc)] - **perf_hooks**: mark as experimental (James M Snell) [#14997](https://github.com/nodejs/node/pull/14997)
- [[`f75faddb1f`](https://github.com/nodejs/node/commit/f75faddb1f)] - **(SEMVER-MINOR)** **perf_hooks**: implementation of the perf timing API (James M Snell) [#14680](https://github.com/nodejs/node/pull/14680)
- [[`4d2aa16d33`](https://github.com/nodejs/node/commit/4d2aa16d33)] - **process**: keep process prototype in inheritance chain (Jimmy Thomson) [#14715](https://github.com/nodejs/node/pull/14715)
- [[`ae85d5f024`](https://github.com/nodejs/node/commit/ae85d5f024)] - **promises**: more robust stringification (Timothy Gu) [#13784](https://github.com/nodejs/node/pull/13784)
- [[`eee2aa693b`](https://github.com/nodejs/node/commit/eee2aa693b)] - **repl**: force editorMode in .load (Lance Ball) [#14861](https://github.com/nodejs/node/pull/14861)
- [[`f81812b1ff`](https://github.com/nodejs/node/commit/f81812b1ff)] - **src**: turn key length exception into CHECK (Ben Noordhuis) [#15183](https://github.com/nodejs/node/pull/15183)
- [[`f113d7332f`](https://github.com/nodejs/node/commit/f113d7332f)] - **src**: fix compiler warnings in node_perf.cc (Daniel Bevenius) [#15112](https://github.com/nodejs/node/pull/15112)
- [[`a83d427091`](https://github.com/nodejs/node/commit/a83d427091)] - **src**: remove unused persistent properties from env (Anna Henningsen) [#15096](https://github.com/nodejs/node/pull/15096)
- [[`391855c252`](https://github.com/nodejs/node/commit/391855c252)] - **src**: fix build on certain platforms (Anna Henningsen) [#14996](https://github.com/nodejs/node/pull/14996)
- [[`8cee5d66bd`](https://github.com/nodejs/node/commit/8cee5d66bd)] - **src**: reduce code duplication (James M Snell) [#14937](https://github.com/nodejs/node/pull/14937)
- [[`5a05dfe0a7`](https://github.com/nodejs/node/commit/5a05dfe0a7)] - **src**: fixup strings, reduce duplication (James M Snell) [#14937](https://github.com/nodejs/node/pull/14937)
- [[`1c3cb49f00`](https://github.com/nodejs/node/commit/1c3cb49f00)] - **src**: miscellaneous cleanups for node_config (James M Snell) [#14868](https://github.com/nodejs/node/pull/14868)
- [[`7213be9f59`](https://github.com/nodejs/node/commit/7213be9f59)] - **src**: fix DEBUG_HTTP2 type arguments (Daniel Bevenius) [#15197](https://github.com/nodejs/node/pull/15197)
- [[`ffe572addd`](https://github.com/nodejs/node/commit/ffe572addd)] - **src**: replace assert() with CHECK() (Ben Noordhuis) [#14663](https://github.com/nodejs/node/pull/14663)
- [[`abc5cdc923`](https://github.com/nodejs/node/commit/abc5cdc923)] - **src**: remove unnecessary helper function (Brian White) [#14959](https://github.com/nodejs/node/pull/14959)
- [[`992d1dd956`](https://github.com/nodejs/node/commit/992d1dd956)] - **src**: detect nul bytes in InternalModuleReadFile() (Ben Noordhuis) [#14854](https://github.com/nodejs/node/pull/14854)
- [[`4570fa16c7`](https://github.com/nodejs/node/commit/4570fa16c7)] - **src**: remove extra copy from Copy() in node_url.cc (Anna Henningsen) [#14907](https://github.com/nodejs/node/pull/14907)
- [[`081c3e107d`](https://github.com/nodejs/node/commit/081c3e107d)] - **src**: minor cleanup for node_revert (James M Snell) [#14864](https://github.com/nodejs/node/pull/14864)
- [[`dcd7817fbc`](https://github.com/nodejs/node/commit/dcd7817fbc)] - **src**: use `unordered_set` instead of custom rb tree (Anna Henningsen) [#14826](https://github.com/nodejs/node/pull/14826)
- [[`fadcbab617`](https://github.com/nodejs/node/commit/fadcbab617)] - **src**: Node implementation of v8::Platform (Matt Loring) [#14001](https://github.com/nodejs/node/pull/14001)
- [[`c861462faa`](https://github.com/nodejs/node/commit/c861462faa)] - **stream**: fix Writable instanceof for subclasses (Anna Henningsen) [#14945](https://github.com/nodejs/node/pull/14945)
- [[`2adabe6777`](https://github.com/nodejs/node/commit/2adabe6777)] - **test**: fix single test runner regression (Timothy Gu) [#15329](https://github.com/nodejs/node/pull/15329)
- [[`e3d0ff901b`](https://github.com/nodejs/node/commit/e3d0ff901b)] - **test**: split test-cli-node-options (Refael Ackermann) [#14195](https://github.com/nodejs/node/pull/14195)
- [[`e87cb32db2`](https://github.com/nodejs/node/commit/e87cb32db2)] - **test**: remove envPlus, use Object.assign everywhere (Gibson Fahnestock) [#14845](https://github.com/nodejs/node/pull/14845)
- [[`dea959e841`](https://github.com/nodejs/node/commit/dea959e841)] - **test**: fix flaky test-readline-interface (Rich Trott) [#15066](https://github.com/nodejs/node/pull/15066)
- [[`ae91b1efc0`](https://github.com/nodejs/node/commit/ae91b1efc0)] - **test**: continue normalizing fixtures use (Miguel Angel Asencio Hurtado) [#14716](https://github.com/nodejs/node/pull/14716)
- [[`77bc72ad54`](https://github.com/nodejs/node/commit/77bc72ad54)] - **(SEMVER-MINOR)** **test**: fix inspector helper port sniffing (Timothy Gu) [#13870](https://github.com/nodejs/node/pull/13870)
- [[`7facfaab66`](https://github.com/nodejs/node/commit/7facfaab66)] - **test**: preserve env in test cases (Beth Griggs) [#14822](https://github.com/nodejs/node/pull/14822)
- [[`2310cfcea1`](https://github.com/nodejs/node/commit/2310cfcea1)] - **test**: exclude write-coverage from coverage report (Benjamin Coe) [#15194](https://github.com/nodejs/node/pull/15194)
- [[`6fa05e671c`](https://github.com/nodejs/node/commit/6fa05e671c)] - **test**: use no-save and no-package-lock flags (Simon Brewster) [#15196](https://github.com/nodejs/node/pull/15196)
- [[`ac71d99253`](https://github.com/nodejs/node/commit/ac71d99253)] - **test**: add http2 compat setTimeout tests (Anatoli Papirovski) [#15156](https://github.com/nodejs/node/pull/15156)
- [[`7106734773`](https://github.com/nodejs/node/commit/7106734773)] - **test**: add test-benchmark-buffer (Rich Trott) [#15175](https://github.com/nodejs/node/pull/15175)
- [[`0b9fde4d4a`](https://github.com/nodejs/node/commit/0b9fde4d4a)] - **test**: refactor test-fs-readfile-unlink (Rich Trott) [#15173](https://github.com/nodejs/node/pull/15173)
- [[`9f79bd8fba`](https://github.com/nodejs/node/commit/9f79bd8fba)] - **test**: http2 test coverage for NghttpError (James M Snell) [#15105](https://github.com/nodejs/node/pull/15105)
- [[`c0dba0f3f4`](https://github.com/nodejs/node/commit/c0dba0f3f4)] - **test**: http2 test coverage for assertValidPseudoHeader (James M Snell) [#15105](https://github.com/nodejs/node/pull/15105)
- [[`837c29c73b`](https://github.com/nodejs/node/commit/837c29c73b)] - **test**: http2 test coverage for updateOptionsBuffer (James M Snell) [#15105](https://github.com/nodejs/node/pull/15105)
- [[`e3e9e5039d`](https://github.com/nodejs/node/commit/e3e9e5039d)] - **test**: increase Http2ServerResponse test coverage (Anatoli Papirovski) [#15074](https://github.com/nodejs/node/pull/15074)
- [[`72aae0417c`](https://github.com/nodejs/node/commit/72aae0417c)] - **test**: split path tests into multiple files (Michaël Zasso) [#15093](https://github.com/nodejs/node/pull/15093)
- [[`d176a18547`](https://github.com/nodejs/node/commit/d176a18547)] - **test**: add a test for Expect & checkExpectation (Anatoli Papirovski) [#15040](https://github.com/nodejs/node/pull/15040)
- [[`cfbf5057d6`](https://github.com/nodejs/node/commit/cfbf5057d6)] - **test**: add http2 test for method CONNECT (Anatoli Papirovski) [#15052](https://github.com/nodejs/node/pull/15052)
- [[`5b13add028`](https://github.com/nodejs/node/commit/5b13add028)] - **test**: remove unused param in test-graph.pipe (Simon Brewster) [#15007](https://github.com/nodejs/node/pull/15007)
- [[`5cb6500de9`](https://github.com/nodejs/node/commit/5cb6500de9)] - **test**: increase coverage for http2 response headers (Anatoli Papirovski) [#15035](https://github.com/nodejs/node/pull/15035)
- [[`7050608593`](https://github.com/nodejs/node/commit/7050608593)] - **test**: fix hijackStdout behavior in console (XadillaX) [#14647](https://github.com/nodejs/node/pull/14647)
- [[`458b8ab5df`](https://github.com/nodejs/node/commit/458b8ab5df)] - **test**: add regression test for 14814 (Anna Henningsen) [#15023](https://github.com/nodejs/node/pull/15023)
- [[`f89ef77144`](https://github.com/nodejs/node/commit/f89ef77144)] - **test**: run abort tests (Rich Trott) [#14013](https://github.com/nodejs/node/pull/14013)
- [[`a91a3fe6c4`](https://github.com/nodejs/node/commit/a91a3fe6c4)] - **test**: improve test-abort-backtrace (Rich Trott) [#14013](https://github.com/nodejs/node/pull/14013)
- [[`b85a73407b`](https://github.com/nodejs/node/commit/b85a73407b)] - **test**: improve test-abort-uncaught-exception (Rich Trott) [#14013](https://github.com/nodejs/node/pull/14013)
- [[`f694ea6f2b`](https://github.com/nodejs/node/commit/f694ea6f2b)] - **test**: pipe some error output if npm fails (Jeremiah Senkpiel) [#12490](https://github.com/nodejs/node/pull/12490)
- [[`f1284d32a5`](https://github.com/nodejs/node/commit/f1284d32a5)] - **test**: simplify test-tls-client-default-ciphers (Jon Moss) [#14928](https://github.com/nodejs/node/pull/14928)
- [[`d4c2eba376`](https://github.com/nodejs/node/commit/d4c2eba376)] - **test**: remove unused function args (Mohd Maqbool Alam) [#14971](https://github.com/nodejs/node/pull/14971)
- [[`9c7f27b91b`](https://github.com/nodejs/node/commit/9c7f27b91b)] - **test**: extend async addon test (Anna Henningsen) [#14922](https://github.com/nodejs/node/pull/14922)
- [[`8c927dd71f`](https://github.com/nodejs/node/commit/8c927dd71f)] - **test**: fix async-hooks tests (Bartosz Sosnowski) [#14865](https://github.com/nodejs/node/pull/14865)
- [[`1849c519ca`](https://github.com/nodejs/node/commit/1849c519ca)] - **test**: add test-benchmark-process (Rich Trott) [#14951](https://github.com/nodejs/node/pull/14951)
- [[`b480b20e02`](https://github.com/nodejs/node/commit/b480b20e02)] - **test**: add test-benchmark-path (Rich Trott) [#14951](https://github.com/nodejs/node/pull/14951)
- [[`2e3e136519`](https://github.com/nodejs/node/commit/2e3e136519)] - **test**: add test-benchmark-os (Rich Trott) [#14951](https://github.com/nodejs/node/pull/14951)
- [[`7e541d6a97`](https://github.com/nodejs/node/commit/7e541d6a97)] - **test**: add test-benchmark-events (Rich Trott) [#14951](https://github.com/nodejs/node/pull/14951)
- [[`981ef464e2`](https://github.com/nodejs/node/commit/981ef464e2)] - **test**: add test-benchmark-domain (Rich Trott) [#14951](https://github.com/nodejs/node/pull/14951)
- [[`34d1a779b1`](https://github.com/nodejs/node/commit/34d1a779b1)] - **test**: add known issue for vm module (Franziska Hinkelmann) [#14661](https://github.com/nodejs/node/pull/14661)
- [[`ae27cb8ea3`](https://github.com/nodejs/node/commit/ae27cb8ea3)] - **test**: do not modify fixtures in test-fs-chmod (Rich Trott) [#14926](https://github.com/nodejs/node/pull/14926)
- [[`eb46609622`](https://github.com/nodejs/node/commit/eb46609622)] - **test**: improve assertion fail messages (Refael Ackermann) [#14949](https://github.com/nodejs/node/pull/14949)
- [[`36b8b46443`](https://github.com/nodejs/node/commit/36b8b46443)] - **test**: remove unused parameters (Daniil Shakir) [#14968](https://github.com/nodejs/node/pull/14968)
- [[`6421a9cb9a`](https://github.com/nodejs/node/commit/6421a9cb9a)] - **test**: remove unused arguments from function (Ankit Parashar) [#14931](https://github.com/nodejs/node/pull/14931)
- [[`e244f8433e`](https://github.com/nodejs/node/commit/e244f8433e)] - **test**: update windows module load error message (cjihrig) [#14950](https://github.com/nodejs/node/pull/14950)
- [[`8f61bf2cda`](https://github.com/nodejs/node/commit/8f61bf2cda)] - **test**: increase coverage for http2.connect (Michael Albert) [#14832](https://github.com/nodejs/node/pull/14832)
- [[`c0312dc781`](https://github.com/nodejs/node/commit/c0312dc781)] - **test**: make timers-blocking-callback more reliable (Rich Trott) [#14831](https://github.com/nodejs/node/pull/14831)
- [[`762155578a`](https://github.com/nodejs/node/commit/762155578a)] - **test**: remove erroneous assert message from test (Beth Griggs) [#14918](https://github.com/nodejs/node/pull/14918)
- [[`1217b1a556`](https://github.com/nodejs/node/commit/1217b1a556)] - **test**: add test for cluster benchmarks (Rich Trott) [#14812](https://github.com/nodejs/node/pull/14812)
- [[`03fd38c1bb`](https://github.com/nodejs/node/commit/03fd38c1bb)] - **test**: Mark test-stop-profile-after-done flaky (Eugene Ostroukhov)
- [[`4f49ae52f8`](https://github.com/nodejs/node/commit/4f49ae52f8)] - **test**: check util.inspect circular Set and Map refs (Ruben Bridgewater) [#14790](https://github.com/nodejs/node/pull/14790)
- [[`4dd095c982`](https://github.com/nodejs/node/commit/4dd095c982)] - **test**: refactor async-hooks/test-httparser tests (Runite618) [#14818](https://github.com/nodejs/node/pull/14818)
- [[`27ec693a53`](https://github.com/nodejs/node/commit/27ec693a53)] - **test**: add missing console.error to exec-maxBuffer (Beth Griggs) [#14796](https://github.com/nodejs/node/pull/14796)
- [[`7f02c36c4f`](https://github.com/nodejs/node/commit/7f02c36c4f)] - **test**: fix test-cluster-send-handle-large-payload (Rich Trott) [#14780](https://github.com/nodejs/node/pull/14780)
- [[`4205648216`](https://github.com/nodejs/node/commit/4205648216)] - **test**: invoke callback with common.mustCall() (Griffith Tchenpan) [#8597](https://github.com/nodejs/node/pull/8597)
- [[`a3feb54c7f`](https://github.com/nodejs/node/commit/a3feb54c7f)] - **test**: make test-tls-alert-handling more strict (Rich Trott) [#14650](https://github.com/nodejs/node/pull/14650)
- [[`d4f2a52953`](https://github.com/nodejs/node/commit/d4f2a52953)] - **test**: check crypto before requiring tls module (Daniel Bevenius) [#14708](https://github.com/nodejs/node/pull/14708)
- [[`868b441f3e`](https://github.com/nodejs/node/commit/868b441f3e)] - **test**: begin normalizing fixtures use (James M Snell) [#14332](https://github.com/nodejs/node/pull/14332)
- [[`c76ec7130e`](https://github.com/nodejs/node/commit/c76ec7130e)] - **test**: improve multiple zlib tests (James M Snell) [#14455](https://github.com/nodejs/node/pull/14455)
- [[`8fb0895176`](https://github.com/nodejs/node/commit/8fb0895176)] - **test**: improve multiple vm tests (James M Snell) [#14458](https://github.com/nodejs/node/pull/14458)
- [[`4d6da3f770`](https://github.com/nodejs/node/commit/4d6da3f770)] - **test, win**: fix IPv6 detection on Windows (Bartosz Sosnowski) [#14865](https://github.com/nodejs/node/pull/14865)
- [[`02260eab98`](https://github.com/nodejs/node/commit/02260eab98)] - **test,doc**: make module name match gyp target name (Gabriel Schulhof) [#15209](https://github.com/nodejs/node/pull/15209)
- [[`dae86e4cf5`](https://github.com/nodejs/node/commit/dae86e4cf5)] - **timers**: fix outdated comment (Tim Costa) [#14314](https://github.com/nodejs/node/pull/14314)
- [[`d6ad9d72f7`](https://github.com/nodejs/node/commit/d6ad9d72f7)] - **(SEMVER-MINOR)** **tls**: multiple PFX in createSecureContext (Yury Popov) [#14793](https://github.com/nodejs/node/pull/14793)
- [[`97908ea4d0`](https://github.com/nodejs/node/commit/97908ea4d0)] - **tools**: bump vswhere helper to 2.0.0 (Refael Ackermann) [#14557](https://github.com/nodejs/node/pull/14557)
- [[`87e44d8651`](https://github.com/nodejs/node/commit/87e44d8651)] - **tools**: add eslint rule for inspector checking (Daniel Bevenius) [#13813](https://github.com/nodejs/node/pull/13813)
- [[`1d97ff4800`](https://github.com/nodejs/node/commit/1d97ff4800)] - **tools**: add eslint rule for hasCrypto checking (Daniel Bevenius) [#13813](https://github.com/nodejs/node/pull/13813)
- [[`bc250a1e38`](https://github.com/nodejs/node/commit/bc250a1e38)] - **tools**: fix linter error in html.js (Michaël Zasso) [#15063](https://github.com/nodejs/node/pull/15063)
- [[`5ee4e86efc`](https://github.com/nodejs/node/commit/5ee4e86efc)] - **tools**: add custom private key option (Ruslan Bekenev) [#14401](https://github.com/nodejs/node/pull/14401)
- [[`8f34b834b7`](https://github.com/nodejs/node/commit/8f34b834b7)] - **tools**: update GYP to 324dd166 (Refael Ackermann) [#14718](https://github.com/nodejs/node/pull/14718)
- [[`e4ea45412e`](https://github.com/nodejs/node/commit/e4ea45412e)] - **tools**: remove stray package-lock.json file (Rich Trott) [#14873](https://github.com/nodejs/node/pull/14873)
- [[`37c43ede43`](https://github.com/nodejs/node/commit/37c43ede43)] - **tools**: fix update-eslint.sh (Myles Borins) [#14850](https://github.com/nodejs/node/pull/14850)
- [[`b0f4539ce5`](https://github.com/nodejs/node/commit/b0f4539ce5)] - **tools**: delete an unused argument (phisixersai) [#14251](https://github.com/nodejs/node/pull/14251)
- [[`9da6c1056c`](https://github.com/nodejs/node/commit/9da6c1056c)] - **tools**: checkout for unassigned DEP00XX codes (James M Snell) [#14702](https://github.com/nodejs/node/pull/14702)
- [[`bd40cc6ef8`](https://github.com/nodejs/node/commit/bd40cc6ef8)] - **tracing**: Update to use new Platform tracing apis (Matt Loring) [#14001](https://github.com/nodejs/node/pull/14001)
- [[`a4fc43202e`](https://github.com/nodejs/node/commit/a4fc43202e)] - **url**: remove unused code from autoEscapeStr (Cyril Lakech) [#15086](https://github.com/nodejs/node/pull/15086)
- [[`2aec977fa2`](https://github.com/nodejs/node/commit/2aec977fa2)] - **util**: remove duplicate code in format (Anatoli Papirovski) [#15098](https://github.com/nodejs/node/pull/15098)
- [[`de10c0f515`](https://github.com/nodejs/node/commit/de10c0f515)] - **util**: fix inspect array w. negative maxArrayLength (Ruben Bridgewater) [#14880](https://github.com/nodejs/node/pull/14880)
- [[`c3c6cb1c13`](https://github.com/nodejs/node/commit/c3c6cb1c13)] - **util**: use proper circular reference checking (Anna Henningsen) [#14790](https://github.com/nodejs/node/pull/14790)

Windows 32-bit Installer: https://nodejs.org/dist/v8.5.0/node-v8.5.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v8.5.0/node-v8.5.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v8.5.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v8.5.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v8.5.0/node-v8.5.0.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v8.5.0/node-v8.5.0-darwin-x64.tar.gz \
Linux 32-bit Binary: https://nodejs.org/dist/v8.5.0/node-v8.5.0-linux-x86.tar.xz \
Linux 64-bit Binary: https://nodejs.org/dist/v8.5.0/node-v8.5.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v8.5.0/node-v8.5.0-linux-ppc64le.tar.xz \
Linux PPC BE 64-bit Binary: https://nodejs.org/dist/v8.5.0/node-v8.5.0-linux-ppc64.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v8.5.0/node-v8.5.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v8.5.0/node-v8.5.0-aix-ppc64.tar.gz \
SmartOS 32-bit Binary: https://nodejs.org/dist/v8.5.0/node-v8.5.0-sunos-x86.tar.xz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v8.5.0/node-v8.5.0-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v8.5.0/node-v8.5.0-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v8.5.0/node-v8.5.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v8.5.0/node-v8.5.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v8.5.0/node-v8.5.0.tar.gz \
Other release files: https://nodejs.org/dist/v8.5.0/ \
Documentation: https://nodejs.org/docs/v8.5.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

5555ac88cdf15b0984efb87a81b6b692ad2a86711516453b8e8fd4923003b578  node-v8.5.0-aix-ppc64.tar.gz
0c8d4c4d90f858a19a29fe1ae7f42b2b7f1a4d3caaa25bea2e08479c00ebbd5f  node-v8.5.0-darwin-x64.tar.gz
30cdfc139d8909eabde9bb4439c1b2409b9814ff05032368e9ed96555af1c860  node-v8.5.0-darwin-x64.tar.xz
c0e2c5ad594cc0d5898d933fc4d859974e62034b9e691af4fc3fdc8f0755300f  node-v8.5.0-headers.tar.gz
8e83244ac9e0fedd472aed02da6857f1befdba74fd29cf56a40476fd7f020aec  node-v8.5.0-headers.tar.xz
09004f9cc8039918e48ce846173dbd37e8673c9a6ada34bdb2e073534c70c9af  node-v8.5.0-linux-arm64.tar.gz
e0decdebe73dba516d6c72401e337cee6277c6e00f817b0bf1c592360adfd4e6  node-v8.5.0-linux-arm64.tar.xz
b6e8f2963c149a556ca2c3f2bfbd42a94ea2f51608e4d1873364b86c283c11da  node-v8.5.0-linux-armv6l.tar.gz
fee64a5843ebcd20b3a553b2fcc032729a90fcf23da236ebdd7dc241fcf93736  node-v8.5.0-linux-armv6l.tar.xz
3caed49e1367eb0a173646a9f82616401127a96cf8c569f5bf5a1dbfba74680c  node-v8.5.0-linux-armv7l.tar.gz
c0b8adcc54a754a84768d8860d4e2abd2eaba35b579fdc0a576e830c7d7ac599  node-v8.5.0-linux-armv7l.tar.xz
82dedda02b88df74d9f0f415d27da3b7ab584b1679b032d70773fe59a954f3c1  node-v8.5.0-linux-ppc64le.tar.gz
abc1db0f2a731d67b598b6e784acee341e0e6272fe61c5f337522ab576ff2cd0  node-v8.5.0-linux-ppc64le.tar.xz
73e218998534f1c467591d474a9436ada1b6adc4977709f482393b6b00eedd6c  node-v8.5.0-linux-ppc64.tar.gz
adeed3f1b7ebc7495a694ffa1514cb9e4700ad4a580c1cd6f529e814e184e11f  node-v8.5.0-linux-ppc64.tar.xz
09eb6157502faabf4e3d22fbe3c31245f5c9e14cf3ec9e25ba5ce1bc19ea84ee  node-v8.5.0-linux-s390x.tar.gz
b75d06cadaa1611b0ee10ebc5a3127f529afc1e19d3ee1299ba05f31c9f969fa  node-v8.5.0-linux-s390x.tar.xz
0000710235e04553147b9c18deadc7cefa4297d4dce190de94cc625d2cf6b9ba  node-v8.5.0-linux-x64.tar.gz
a9651fcc6259b4a944ebb72e6dd116602c7b26ddf939599b060d1d3e6ead8c36  node-v8.5.0-linux-x64.tar.xz
00ea2be3a78b1694439254d9c7232ce79748d5ff3e0d9bb2ff1fcd090d502d63  node-v8.5.0-linux-x86.tar.gz
b420254e8ec211bc28059176890d4dab83e2ee7e72965a6953aec9bf87b9db97  node-v8.5.0-linux-x86.tar.xz
20362cacfceb34512a03ba766e239c142fe89ed6ecbff21a851dd52015482c88  node-v8.5.0.pkg
6f8ce37b15c6e950d98b0523326484e5c23fd2df36f69c032f08790e90ad0f73  node-v8.5.0-sunos-x64.tar.gz
fb06d47106af700633885c476177e52e5e6be3c4bcdb84d7263dd0ff3e47040e  node-v8.5.0-sunos-x64.tar.xz
495b3383d8116b8f82648754c2df54950f68205852b0094f95632da9c558ff68  node-v8.5.0-sunos-x86.tar.gz
b441d93edac5aac5e46cac93354638d7ab5d5e4216fcd3bf279196fe84f6e761  node-v8.5.0-sunos-x86.tar.xz
dd5ac97074882ba62892bdb91ec5e624c338d772a6218da08c182231031a4446  node-v8.5.0.tar.gz
e1cde5d8e5049e7e1b61aef128f26162059d885dc8cab5014bf5cbf693f75c3c  node-v8.5.0.tar.xz
bce2013fdd2d6fbd6bbca1397e07600d01081be711e18fb8a9515443f4eb89a6  node-v8.5.0-win-x64.7z
c385d162c15a7a85d42755ee8c78c1c73bd251e2f7cbf82e5e20c824564aa9df  node-v8.5.0-win-x64.zip
797bed56fe22b52ab8639e4377ffa6a62f921ab0b9ff671fe2ecb6fb2bbcd1f4  node-v8.5.0-win-x86.7z
e2cfb40045359a57fc7933ea8e2f5dad46fade1edb2acb4c1acec130783468c3  node-v8.5.0-win-x86.zip
bc874e741880b873bb0ffae64adc0aa34c858e6f7adabdc26443a2b2e79f7691  node-v8.5.0-x64.msi
0868de61ada1ea12d077195e118ac0792c0fc56661ada6407656bcd194d1d4c0  node-v8.5.0-x86.msi
0855c0933b037d9c45bf391ac1a3cb8c353e3d0ebfaa82d82c3f5cc5b3c6023c  win-x64/node.exe
9191eda81b92f8d32610989c1082fee6932c7c2dc0ddd66ac2a944bbdd9023bc  win-x64/node.lib
b9ed1b1b2431a4d8939b536ce799e856b7b033e393f4e1783ee23dd7a647733f  win-x64/node_pdb.7z
226686aae2a2847be035ea5c721585255721fd1b16db82577516014d8483989b  win-x64/node_pdb.zip
778ed817ae30baf7395ec05454489802b3b6e5c34282dd027d9b6b72698e1da0  win-x86/node.exe
b12a496304f543c0460685ec72b859bd58d814de15cf7fcb1250256638cc8403  win-x86/node.lib
7c90fd81c42acc8e6e24d0da3969d993001f7626a081103f17d11ea6fe7118cb  win-x86/node_pdb.7z
9be69dcc9d231743571df6d2ca3e4c65d5aaeece510861c3a92d6963c7faa5aa  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQEcBAEBCAAGBQJZt/4pAAoJEJM7AfQLXKlG4RsH/0hkyAYAOrWNNLauWCeCS4Cv
meZXo00M8tq7yZ3yurlIV9YrsvSn4CHAVah0HBvlhJci3p55RRJckOrRbW07VGRG
3NZVah9IFCH8bIGFTUzbqEdyPJsW7d/ZVaLjzSwQh0N5VN8VqYD3WFT1vWTJODgk
MNCDd1z3cBuSReiM4V0js9y4SPoon6Sg6LEg3D7oXFFj3w8ogSbHF9q92FLdbLON
adVkhL8h+GFvjEnhItlk9J3e4HqaWdFpu3+Y2svB7C0bbOf16MmIu7ZjuTBgVEcR
aB25o1daR1RGDL93f1/Gny4VnCLVh2wc6uPAYOz37LlK++/JZw653B6cmdpl9Hc=
=KAOL
-----END PGP SIGNATURE-----

```
