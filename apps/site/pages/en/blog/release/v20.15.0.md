---
date: '2024-06-20T16:04:41.661Z'
category: release
title: Node v20.15.0 (LTS)
layout: blog-post
author: <PERSON>
---

## 2024-06-20, Version 20.15.0 'Iron' (LTS), @marco-ippolito

### test_runner: support test plans

It is now possible to count the number of assertions and subtests that are expected to run within a test. If the number of assertions and subtests that run does not match the expected count, the test will fail.

```js
test('top level test', t => {
  t.plan(2);
  t.assert.ok('some relevant assertion here');
  t.subtest('subtest', () => {});
});
```

Contributed by <PERSON> in [#52860](https://github.com/nodejs/node/pull/52860)

### inspector: introduce the `--inspect-wait` flag

This release introduces the `--inspect-wait` flag, which allows debugger to wait for attachement. This flag is useful when you want to debug the code from the beginning. Unlike `--inspect-brk`, which breaks on the first line, this flag waits for debugger to be connected and then runs the code as soon as a session is established.

Contributed by <PERSON><PERSON><PERSON> in [#52734](https://github.com/nodejs/node/pull/52734)

### zlib: expose zlib.crc32()

This release exposes the crc32() function from zlib to user-land.

It computes a 32-bit Cyclic Redundancy Check checksum of data. If
value is specified, it is used as the starting value of the checksum,
otherwise, 0 is used as the starting value.

The CRC algorithm is designed to compute checksums and to detect error
in data transmission. It's not suitable for cryptographic authentication.

```js
const zlib = require('node:zlib');
const { Buffer } = require('node:buffer');

let crc = zlib.crc32('hello'); // 907060870
crc = zlib.crc32('world', crc); // 4192936109

crc = zlib.crc32(Buffer.from('hello', 'utf16le')); // 1427272415
crc = zlib.crc32(Buffer.from('world', 'utf16le'), crc); // 4150509955
```

Contributed by Joyee Cheung in [#52692](https://github.com/nodejs/node/pull/52692)

### cli: allow running wasm in limited vmem with --disable-wasm-trap-handler

By default, Node.js enables trap-handler-based WebAssembly bound
checks. As a result, V8 does not need to insert inline bound checks
int the code compiled from WebAssembly which may speedup WebAssembly
execution significantly, but this optimization requires allocating
a big virtual memory cage (currently 10GB). If the Node.js process
does not have access to a large enough virtual memory address space
due to system configurations or hardware limitations, users won't
be able to run any WebAssembly that involves allocation in this
virtual memory cage and will see an out-of-memory error.

```console
$ ulimit -v 5000000
$ node -p "new WebAssembly.Memory({ initial: 10, maximum: 100 });"
[eval]:1
new WebAssembly.Memory({ initial: 10, maximum: 100 });
^

RangeError: WebAssembly.Memory(): could not allocate memory
    at [eval]:1:1
    at runScriptInThisContext (node:internal/vm:209:10)
    at node:internal/process/execution:118:14
    at [eval]-wrapper:6:24
    at runScript (node:internal/process/execution:101:62)
    at evalScript (node:internal/process/execution:136:3)
    at node:internal/main/eval_string:49:3

```

`--disable-wasm-trap-handler` disables this optimization so that
users can at least run WebAssembly (with a less optimial performance)
when the virtual memory address space available to their Node.js
process is lower than what the V8 WebAssembly memory cage needs.

Contributed by Joyee Cheung in [#52766](https://github.com/nodejs/node/pull/52766)

### Other Notable Changes

- \[[`12512c3d0e`](https://github.com/nodejs/node/commit/12512c3d0e)] - **doc**: add pimterry to collaborators (Tim Perry) [#52874](https://github.com/nodejs/node/pull/52874)
- \[[`9d485b40bb`](https://github.com/nodejs/node/commit/9d485b40bb)] - **(SEMVER-MINOR)** **tools**: fix get_asan_state() in tools/test.py (Joyee Cheung) [#52766](https://github.com/nodejs/node/pull/52766)
- \[[`e98c305f52`](https://github.com/nodejs/node/commit/e98c305f52)] - **(SEMVER-MINOR)** **tools**: support max_virtual_memory test configuration (Joyee Cheung) [#52766](https://github.com/nodejs/node/pull/52766)
- \[[`dce0300896`](https://github.com/nodejs/node/commit/dce0300896)] - **(SEMVER-MINOR)** **tools**: support != in test status files (Joyee Cheung) [#52766](https://github.com/nodejs/node/pull/52766)

### Commits

- \[[`227093bfec`](https://github.com/nodejs/node/commit/227093bfec)] - **assert**: add deep equal check for more Error type (Zhenwei Jin) [#51805](https://github.com/nodejs/node/pull/51805)
- \[[`184cfe5a71`](https://github.com/nodejs/node/commit/184cfe5a71)] - **benchmark**: filter non-present deps from `start-cli-version` (Adam Majer) [#51746](https://github.com/nodejs/node/pull/51746)
- \[[`8b3e83bb53`](https://github.com/nodejs/node/commit/8b3e83bb53)] - **buffer**: even faster atob (Daniel Lemire) [#52443](https://github.com/nodejs/node/pull/52443)
- \[[`8d628c3255`](https://github.com/nodejs/node/commit/8d628c3255)] - **buffer**: use size_t instead of uint32_t to avoid segmentation fault (Xavier Stouder) [#48033](https://github.com/nodejs/node/pull/48033)
- \[[`16ae2b2933`](https://github.com/nodejs/node/commit/16ae2b2933)] - **buffer**: remove lines setting indexes to integer value (Zhenwei Jin) [#52588](https://github.com/nodejs/node/pull/52588)
- \[[`48c15d0dcd`](https://github.com/nodejs/node/commit/48c15d0dcd)] - **build**: remove deprecated calls for argument groups (Mohammed Keyvanzadeh) [#52913](https://github.com/nodejs/node/pull/52913)
- \[[`1be8232d17`](https://github.com/nodejs/node/commit/1be8232d17)] - **build**: drop base64 dep in GN build (Cheng) [#52856](https://github.com/nodejs/node/pull/52856)
- \[[`918962d6e7`](https://github.com/nodejs/node/commit/918962d6e7)] - **build**: make simdjson a public dep in GN build (Cheng) [#52755](https://github.com/nodejs/node/pull/52755)
- \[[`5215b6fd8e`](https://github.com/nodejs/node/commit/5215b6fd8e)] - **build, tools**: copy release assets to staging R2 bucket once built (flakey5) [#51394](https://github.com/nodejs/node/pull/51394)
- \[[`473fa73857`](https://github.com/nodejs/node/commit/473fa73857)] - **(SEMVER-MINOR)** **cli**: allow running wasm in limited vmem with --disable-wasm-trap-handler (Joyee Cheung) [#52766](https://github.com/nodejs/node/pull/52766)
- \[[`954d2aded4`](https://github.com/nodejs/node/commit/954d2aded4)] - **cluster**: replace `forEach` with `for-of` loop (Jérôme Benoit) [#50317](https://github.com/nodejs/node/pull/50317)
- \[[`794e450ea7`](https://github.com/nodejs/node/commit/794e450ea7)] - **console**: colorize console error and warn (Jithil P Ponnan) [#51629](https://github.com/nodejs/node/pull/51629)
- \[[`0fb7c18f10`](https://github.com/nodejs/node/commit/0fb7c18f10)] - **crypto**: fix duplicated switch-case return values (Mustafa Ateş UZUN) [#49030](https://github.com/nodejs/node/pull/49030)
- \[[`cd1415c8b2`](https://github.com/nodejs/node/commit/cd1415c8b2)] - _**Revert**_ "**crypto**: make timingSafeEqual faster for Uint8Array" (Tobias Nießen) [#53390](https://github.com/nodejs/node/pull/53390)
- \[[`b774544bb1`](https://github.com/nodejs/node/commit/b774544bb1)] - **deps**: enable unbundling of simdjson, simdutf, ada (Daniel Lemire) [#52924](https://github.com/nodejs/node/pull/52924)
- \[[`da4dbfc5fd`](https://github.com/nodejs/node/commit/da4dbfc5fd)] - **doc**: remove reference to AUTHORS file (Marco Ippolito) [#52960](https://github.com/nodejs/node/pull/52960)
- \[[`2f3f2ff8af`](https://github.com/nodejs/node/commit/2f3f2ff8af)] - **doc**: update hljs with the latest styles (Aviv Keller) [#52911](https://github.com/nodejs/node/pull/52911)
- \[[`3a1d17a9b1`](https://github.com/nodejs/node/commit/3a1d17a9b1)] - **doc**: mention quicker way to build docs (Alex Crawford) [#52937](https://github.com/nodejs/node/pull/52937)
- \[[`be309bd19d`](https://github.com/nodejs/node/commit/be309bd19d)] - **doc**: mention push.followTags config (Rafael Gonzaga) [#52906](https://github.com/nodejs/node/pull/52906)
- \[[`e62c6e2684`](https://github.com/nodejs/node/commit/e62c6e2684)] - **doc**: document pipeline with `end` option (Alois Klink) [#48970](https://github.com/nodejs/node/pull/48970)
- \[[`af27225cf6`](https://github.com/nodejs/node/commit/af27225cf6)] - **doc**: add example for `execFileSync` method and ref to stdio (Evan Shortiss) [#39412](https://github.com/nodejs/node/pull/39412)
- \[[`086626f9b1`](https://github.com/nodejs/node/commit/086626f9b1)] - **doc**: add examples and notes to http server.close et al (mary marchini) [#49091](https://github.com/nodejs/node/pull/49091)
- \[[`3aa3337a00`](https://github.com/nodejs/node/commit/3aa3337a00)] - **doc**: fix `dns.lookup` family `0` and `all` descriptions (Adam Jones) [#51653](https://github.com/nodejs/node/pull/51653)
- \[[`585f2a2e7f`](https://github.com/nodejs/node/commit/585f2a2e7f)] - **doc**: update `fs.realpath` documentation (sinkhaha) [#48170](https://github.com/nodejs/node/pull/48170)
- \[[`4bf3d44e1d`](https://github.com/nodejs/node/commit/4bf3d44e1d)] - **doc**: update fs read documentation for clarity (Mert Can Altin) [#52453](https://github.com/nodejs/node/pull/52453)
- \[[`ae5d47dde3`](https://github.com/nodejs/node/commit/ae5d47dde3)] - **doc**: watermark string behavior (Benjamin Gruenbaum) [#52842](https://github.com/nodejs/node/pull/52842)
- \[[`1e429d10d3`](https://github.com/nodejs/node/commit/1e429d10d3)] - **doc**: exclude commits with baking-for-lts (Marco Ippolito) [#52896](https://github.com/nodejs/node/pull/52896)
- \[[`3df3e37cdb`](https://github.com/nodejs/node/commit/3df3e37cdb)] - **doc**: add names next to release key bash commands (Aviv Keller) [#52878](https://github.com/nodejs/node/pull/52878)
- \[[`12512c3d0e`](https://github.com/nodejs/node/commit/12512c3d0e)] - **doc**: add pimterry to collaborators (Tim Perry) [#52874](https://github.com/nodejs/node/pull/52874)
- \[[`97e0fef019`](https://github.com/nodejs/node/commit/97e0fef019)] - **doc**: add more definitions to GLOSSARY.md (Aviv Keller) [#52798](https://github.com/nodejs/node/pull/52798)
- \[[`91fadac162`](https://github.com/nodejs/node/commit/91fadac162)] - **doc**: make docs more welcoming and descriptive for newcomers (Serkan Özel) [#38056](https://github.com/nodejs/node/pull/38056)
- \[[`a3b20126fd`](https://github.com/nodejs/node/commit/a3b20126fd)] - **doc**: add OpenSSL errors to API docs (John Lamp) [#34213](https://github.com/nodejs/node/pull/34213)
- \[[`9587ae9b5b`](https://github.com/nodejs/node/commit/9587ae9b5b)] - **doc**: simplify copy-pasting of `branch-diff` commands (Antoine du Hamel) [#52757](https://github.com/nodejs/node/pull/52757)
- \[[`6ea72a53c3`](https://github.com/nodejs/node/commit/6ea72a53c3)] - **doc**: add test_runner to subsystem (Raz Luvaton) [#52774](https://github.com/nodejs/node/pull/52774)
- \[[`972eafd983`](https://github.com/nodejs/node/commit/972eafd983)] - **events**: update MaxListenersExceededWarning message log (sinkhaha) [#51921](https://github.com/nodejs/node/pull/51921)
- \[[`74753ed1fe`](https://github.com/nodejs/node/commit/74753ed1fe)] - **events**: add stop propagation flag to `Event.stopImmediatePropagation` (Mickael Meausoone) [#39463](https://github.com/nodejs/node/pull/39463)
- \[[`75dd009649`](https://github.com/nodejs/node/commit/75dd009649)] - **events**: replace NodeCustomEvent with CustomEvent (Feng Yu) [#43876](https://github.com/nodejs/node/pull/43876)
- \[[`7d38c2e012`](https://github.com/nodejs/node/commit/7d38c2e012)] - **fs**: keep fs.promises.readFile read until EOF is reached (Zhenwei Jin) [#52178](https://github.com/nodejs/node/pull/52178)
- \[[`8cb13120d3`](https://github.com/nodejs/node/commit/8cb13120d3)] - **(SEMVER-MINOR)** **inspector**: introduce the `--inspect-wait` flag (Kohei Ueno) [#52734](https://github.com/nodejs/node/pull/52734)
- \[[`d5ab1de1fd`](https://github.com/nodejs/node/commit/d5ab1de1fd)] - **meta**: move `@anonrig` to TSC regular member (Yagiz Nizipli) [#52932](https://github.com/nodejs/node/pull/52932)
- \[[`f82d086e90`](https://github.com/nodejs/node/commit/f82d086e90)] - **path**: fix toNamespacedPath on Windows (Hüseyin Açacak) [#52915](https://github.com/nodejs/node/pull/52915)
- \[[`121ea13b50`](https://github.com/nodejs/node/commit/121ea13b50)] - **process**: improve event-loop (Aras Abbasi) [#52108](https://github.com/nodejs/node/pull/52108)
- \[[`eceac784aa`](https://github.com/nodejs/node/commit/eceac784aa)] - **repl**: fix disruptive autocomplete without inspector (Nitzan Uziely) [#40661](https://github.com/nodejs/node/pull/40661)
- \[[`89a910be82`](https://github.com/nodejs/node/commit/89a910be82)] - **src**: fix Worker termination in `inspector.waitForDebugger` (Daeyeon Jeong) [#52527](https://github.com/nodejs/node/pull/52527)
- \[[`033f985e8a`](https://github.com/nodejs/node/commit/033f985e8a)] - **src**: use `S_ISDIR` to check if the file is a directory (theanarkh) [#52164](https://github.com/nodejs/node/pull/52164)
- \[[`95128399f8`](https://github.com/nodejs/node/commit/95128399f8)] - **src**: allow preventing debug signal handler start (Shelley Vohr) [#46681](https://github.com/nodejs/node/pull/46681)
- \[[`b162aeae9e`](https://github.com/nodejs/node/commit/b162aeae9e)] - **src**: fix typo Unabled -> Unable (Simon Siefke) [#52820](https://github.com/nodejs/node/pull/52820)
- \[[`2dcbf1894a`](https://github.com/nodejs/node/commit/2dcbf1894a)] - **src**: avoid unused variable 'error' warning (Michaël Zasso) [#52886](https://github.com/nodejs/node/pull/52886)
- \[[`978ee0a635`](https://github.com/nodejs/node/commit/978ee0a635)] - **src**: only apply fix in main thread (Paolo Insogna) [#52702](https://github.com/nodejs/node/pull/52702)
- \[[`8fc52b38c6`](https://github.com/nodejs/node/commit/8fc52b38c6)] - **src**: fix test local edge case (Paolo Insogna) [#52702](https://github.com/nodejs/node/pull/52702)
- \[[`d02907ecc4`](https://github.com/nodejs/node/commit/d02907ecc4)] - **src**: remove misplaced windows code under posix guard in node.cc (Ali Hassan) [#52545](https://github.com/nodejs/node/pull/52545)
- \[[`af29120fa7`](https://github.com/nodejs/node/commit/af29120fa7)] - **stream**: use `ByteLengthQueuingStrategy` when not in `objectMode` (Jason) [#48847](https://github.com/nodejs/node/pull/48847)
- \[[`a5f3dd137c`](https://github.com/nodejs/node/commit/a5f3dd137c)] - **string_decoder**: throw an error when writing a too long buffer (zhenweijin) [#52215](https://github.com/nodejs/node/pull/52215)
- \[[`65fa95d57d`](https://github.com/nodejs/node/commit/65fa95d57d)] - **test**: add `Debugger.setInstrumentationBreakpoint` known issue (Konstantin Ulitin) [#31137](https://github.com/nodejs/node/pull/31137)
- \[[`0513e07805`](https://github.com/nodejs/node/commit/0513e07805)] - **test**: use `for-of` instead of `forEach` (Gibby Free) [#49790](https://github.com/nodejs/node/pull/49790)
- \[[`1d01325928`](https://github.com/nodejs/node/commit/1d01325928)] - **test**: verify request payload is uploaded consistently (Austin Wright) [#34066](https://github.com/nodejs/node/pull/34066)
- \[[`7dda156872`](https://github.com/nodejs/node/commit/7dda156872)] - **test**: add fuzzer for native/js string conversion (Adam Korczynski) [#51120](https://github.com/nodejs/node/pull/51120)
- \[[`5fb829b340`](https://github.com/nodejs/node/commit/5fb829b340)] - **test**: add fuzzer for `ClientHelloParser` (AdamKorcz) [#51088](https://github.com/nodejs/node/pull/51088)
- \[[`cc74bf789f`](https://github.com/nodejs/node/commit/cc74bf789f)] - **test**: fix broken env fuzzer by initializing process (AdamKorcz) [#51080](https://github.com/nodejs/node/pull/51080)
- \[[`800b6f65cf`](https://github.com/nodejs/node/commit/800b6f65cf)] - **test**: replace `forEach()` in `test-stream-pipe-unpipe-stream` (Dario) [#50786](https://github.com/nodejs/node/pull/50786)
- \[[`d08c9a6a31`](https://github.com/nodejs/node/commit/d08c9a6a31)] - **test**: test pipeline `end` on transform streams (Alois Klink) [#48970](https://github.com/nodejs/node/pull/48970)
- \[[`0be8123ede`](https://github.com/nodejs/node/commit/0be8123ede)] - **test**: improve coverage of lib/readline.js (Rongjian Zhang) [#38646](https://github.com/nodejs/node/pull/38646)
- \[[`410224415c`](https://github.com/nodejs/node/commit/410224415c)] - **test**: updated for each to for of in test file (lyannel) [#50308](https://github.com/nodejs/node/pull/50308)
- \[[`556e9a2127`](https://github.com/nodejs/node/commit/556e9a2127)] - **test**: move `test-http-server-request-timeouts-mixed` to sequential (Madhuri) [#45722](https://github.com/nodejs/node/pull/45722)
- \[[`0638274c07`](https://github.com/nodejs/node/commit/0638274c07)] - **test**: fix DNS cancel tests (Szymon Marczak) [#44432](https://github.com/nodejs/node/pull/44432)
- \[[`311bdc62bd`](https://github.com/nodejs/node/commit/311bdc62bd)] - **test**: add http agent to `executionAsyncResource` (psj-tar-gz) [#34966](https://github.com/nodejs/node/pull/34966)
- \[[`6001b164ab`](https://github.com/nodejs/node/commit/6001b164ab)] - **test**: reduce memory usage of test-worker-stdio (Adam Majer) [#37769](https://github.com/nodejs/node/pull/37769)
- \[[`986bfa26e9`](https://github.com/nodejs/node/commit/986bfa26e9)] - **test**: add common.expectRequiredModule() (Joyee Cheung) [#52868](https://github.com/nodejs/node/pull/52868)
- \[[`2246d4fd1e`](https://github.com/nodejs/node/commit/2246d4fd1e)] - **test**: crypto-rsa-dsa testing for dynamic openssl (Michael Dawson) [#52781](https://github.com/nodejs/node/pull/52781)
- \[[`1dce5dea0b`](https://github.com/nodejs/node/commit/1dce5dea0b)] - **test**: skip some console tests on dumb terminal (Adam Majer) [#37770](https://github.com/nodejs/node/pull/37770)
- \[[`0addeb240c`](https://github.com/nodejs/node/commit/0addeb240c)] - **test**: skip v8-updates/test-linux-perf-logger (Michaël Zasso) [#52821](https://github.com/nodejs/node/pull/52821)
- \[[`56e19e38f3`](https://github.com/nodejs/node/commit/56e19e38f3)] - **test**: drop test-crypto-timing-safe-equal-benchmarks (Rafael Gonzaga) [#52751](https://github.com/nodejs/node/pull/52751)
- \[[`0c5e58958c`](https://github.com/nodejs/node/commit/0c5e58958c)] - **test, crypto**: use correct object on assert (响马) [#51820](https://github.com/nodejs/node/pull/51820)
- \[[`d54aa47ec1`](https://github.com/nodejs/node/commit/d54aa47ec1)] - **(SEMVER-MINOR)** **test_runner**: support test plans (Colin Ihrig) [#52860](https://github.com/nodejs/node/pull/52860)
- \[[`0289a023a5`](https://github.com/nodejs/node/commit/0289a023a5)] - **test_runner**: fix watch mode race condition (Moshe Atlow) [#52954](https://github.com/nodejs/node/pull/52954)
- \[[`cf817e192e`](https://github.com/nodejs/node/commit/cf817e192e)] - **test_runner**: preserve hook promise when executed twice (Moshe Atlow) [#52791](https://github.com/nodejs/node/pull/52791)
- \[[`de541235fe`](https://github.com/nodejs/node/commit/de541235fe)] - **tools**: fix v8-update workflow (Michaël Zasso) [#52957](https://github.com/nodejs/node/pull/52957)
- \[[`f6290bc327`](https://github.com/nodejs/node/commit/f6290bc327)] - **tools**: add --certify-safe to nci-ci (Matteo Collina) [#52940](https://github.com/nodejs/node/pull/52940)
- \[[`0830b3115d`](https://github.com/nodejs/node/commit/0830b3115d)] - **tools**: fix doc update action (Marco Ippolito) [#52890](https://github.com/nodejs/node/pull/52890)
- \[[`9d485b40bb`](https://github.com/nodejs/node/commit/9d485b40bb)] - **(SEMVER-MINOR)** **tools**: fix get_asan_state() in tools/test.py (Joyee Cheung) [#52766](https://github.com/nodejs/node/pull/52766)
- \[[`e98c305f52`](https://github.com/nodejs/node/commit/e98c305f52)] - **(SEMVER-MINOR)** **tools**: support max_virtual_memory test configuration (Joyee Cheung) [#52766](https://github.com/nodejs/node/pull/52766)
- \[[`dce0300896`](https://github.com/nodejs/node/commit/dce0300896)] - **(SEMVER-MINOR)** **tools**: support != in test status files (Joyee Cheung) [#52766](https://github.com/nodejs/node/pull/52766)
- \[[`57006001ec`](https://github.com/nodejs/node/commit/57006001ec)] - **tools**: prepare custom rules for ESLint v9 (Michaël Zasso) [#52889](https://github.com/nodejs/node/pull/52889)
- \[[`403a4a7557`](https://github.com/nodejs/node/commit/403a4a7557)] - **tools**: update lint-md-dependencies to rollup\@4.17.2 (Node.js GitHub Bot) [#52836](https://github.com/nodejs/node/pull/52836)
- \[[`01eff5860e`](https://github.com/nodejs/node/commit/01eff5860e)] - **tools**: update `gr2m/create-or-update-pull-request-action` (Antoine du Hamel) [#52843](https://github.com/nodejs/node/pull/52843)
- \[[`514f01ed59`](https://github.com/nodejs/node/commit/514f01ed59)] - **tools**: use sccache GitHub action (Michaël Zasso) [#52839](https://github.com/nodejs/node/pull/52839)
- \[[`8f8fb91927`](https://github.com/nodejs/node/commit/8f8fb91927)] - **tools**: specify a commit-message for V8 update workflow (Antoine du Hamel) [#52844](https://github.com/nodejs/node/pull/52844)
- \[[`b83fbf8709`](https://github.com/nodejs/node/commit/b83fbf8709)] - **tools**: fix V8 update workflow (Antoine du Hamel) [#52822](https://github.com/nodejs/node/pull/52822)
- \[[`be9d6f2176`](https://github.com/nodejs/node/commit/be9d6f2176)] - **url,tools,benchmark**: replace deprecated `substr()` (Jungku Lee) [#51546](https://github.com/nodejs/node/pull/51546)
- \[[`7603a51d45`](https://github.com/nodejs/node/commit/7603a51d45)] - **util**: fix `%s` format behavior with `Symbol.toPrimitive` (Chenyu Yang) [#50992](https://github.com/nodejs/node/pull/50992)
- \[[`d7eba50cf3`](https://github.com/nodejs/node/commit/d7eba50cf3)] - **util**: improve `isInsideNodeModules` (uzlopak) [#52147](https://github.com/nodejs/node/pull/52147)
- \[[`4ae4f7e517`](https://github.com/nodejs/node/commit/4ae4f7e517)] - **watch**: allow listening for grouped changes (Matthieu Sieben) [#52722](https://github.com/nodejs/node/pull/52722)
- \[[`1ff8f318c0`](https://github.com/nodejs/node/commit/1ff8f318c0)] - **watch**: enable passthrough ipc in watch mode (Zack) [#50890](https://github.com/nodejs/node/pull/50890)
- \[[`739adf90b1`](https://github.com/nodejs/node/commit/739adf90b1)] - **watch**: fix arguments parsing (Moshe Atlow) [#52760](https://github.com/nodejs/node/pull/52760)
- \[[`5161d95c30`](https://github.com/nodejs/node/commit/5161d95c30)] - **(SEMVER-MINOR)** **zlib**: expose zlib.crc32() (Joyee Cheung) [#52692](https://github.com/nodejs/node/pull/52692)

Windows 32-bit Installer: https://nodejs.org/dist/v20.15.0/node-v20.15.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v20.15.0/node-v20.15.0-x64.msi \
Windows ARM 64-bit Installer: https://nodejs.org/dist/v20.15.0/node-v20.15.0-arm64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v20.15.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v20.15.0/win-x64/node.exe \
Windows ARM 64-bit Binary: https://nodejs.org/dist/v20.15.0/win-arm64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v20.15.0/node-v20.15.0.pkg \
macOS Apple Silicon 64-bit Binary: https://nodejs.org/dist/v20.15.0/node-v20.15.0-darwin-arm64.tar.gz \
macOS Intel 64-bit Binary: https://nodejs.org/dist/v20.15.0/node-v20.15.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v20.15.0/node-v20.15.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v20.15.0/node-v20.15.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v20.15.0/node-v20.15.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v20.15.0/node-v20.15.0-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v20.15.0/node-v20.15.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v20.15.0/node-v20.15.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v20.15.0/node-v20.15.0.tar.gz \
Other release files: https://nodejs.org/dist/v20.15.0/ \
Documentation: https://nodejs.org/docs/v20.15.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

6b6e7119a5b539bfcf3b37872b37771750fd7e8bd255eaab1285da749d71775b  node-v20.15.0-aix-ppc64.tar.gz
bfc8a911a23e31b7fbf6ad335834b89df743f1922a461b25ece9bb2d0a2e7f29  node-v20.15.0-arm64.msi
2646c338d2c5ecabba4f745fc315d6fdfbb7e01b5badecc154ad27dda00325fc  node-v20.15.0-darwin-arm64.tar.gz
041a9dae8caece11e63e217247bdfe50f38bedd1a89e39212a2c9aa7d7726c3a  node-v20.15.0-darwin-arm64.tar.xz
95ca98b974d06f84095dd0a977dea5fde79e909d103715fb782856ded12d2146  node-v20.15.0-darwin-x64.tar.gz
0f5318eea20055f12bffdee53380645d86335a15a6f1281bbc597f80c4d8fa6a  node-v20.15.0-darwin-x64.tar.xz
0cede3eb4d8cfb885570533adaf860d53586036a1caaa797093b21723206fef6  node-v20.15.0-headers.tar.gz
4c5888d8cc00ac95a48696622229e63913591c4688fc69edf158422a9ca0708a  node-v20.15.0-headers.tar.xz
9e593b61bf2a2e01073eba20508f0adc6daa746f577ec4e8d777032f15b5fa48  node-v20.15.0-linux-arm64.tar.gz
ecdd2645630a35abd98ebcf2d989f38cec8cca6f198712d8ce72eaee77131307  node-v20.15.0-linux-arm64.tar.xz
63cea18f789b926c35576d721b49e416df40f9d66083a313d7da985dbedabfe2  node-v20.15.0-linux-armv7l.tar.gz
f34ad30051c9b2083952afa3de559b7b5df5c2ce82526ba360184876c456475c  node-v20.15.0-linux-armv7l.tar.xz
445c8f1d4ed6d26629189879d6d4843913c81b48f7e1e4c1eb7586167e8c2027  node-v20.15.0-linux-ppc64le.tar.gz
3daf147707d798929682d23f149a11d2db937658d8ac9e04be14e85fda7bb0b3  node-v20.15.0-linux-ppc64le.tar.xz
ea1973a743afe834b1ac413922d5cf70a19becc583277b17bf9a6442a58e75cc  node-v20.15.0-linux-s390x.tar.gz
0b438b05a2aa826c69ae87f78f27dd928331e43f7432efb1b2c0c2be41c459ec  node-v20.15.0-linux-s390x.tar.xz
3a873874caae30b5a9125fdf8872f7800cc0216eb89d097470110cd267a596e2  node-v20.15.0-linux-x64.tar.gz
4f57f7828e6adb9f6bc77932f10e316cca68b0f160c82b21d9a2c7647c7f10bf  node-v20.15.0-linux-x64.tar.xz
b68d9b5db9d62dfcf5d1ef9cf6f870f45532496d5eb33d0e8dc43a4029e6f94e  node-v20.15.0.pkg
01e2c034467a324a33e778c81f2808dff13d289eaa9307d3e9b06c171e4d932d  node-v20.15.0.tar.gz
0f4a7a051c35d95eb905e8cb2aa43c5d402b131203908fe633eb3cfa050ef907  node-v20.15.0.tar.xz
189168e78272d647bbbe0d76dd4353d99c337c3b46d9789f5c5f3eb3c71b0956  node-v20.15.0-win-arm64.7z
ce9fc699ad0a395a17e94a1fcc4c9068542a5cc95def4e1bdc2daec678cee5b4  node-v20.15.0-win-arm64.zip
d19232e89f9ffff09d0efa7dbc05be824104b450501027a6c520c7ad58daa656  node-v20.15.0-win-x64.7z
37b1b83b6efecd53fc5dd16e0674e0ec674dd80a5924e5f3a8d9613c02437bec  node-v20.15.0-win-x64.zip
7fa94ce887865d6143afa5d1d3c04aaed05913ac7c48baabe93ca18ef62b5218  node-v20.15.0-win-x86.7z
148bbab53efada93a1d0367e09c6deba248490b80d7a7def02d5baeab22eeb02  node-v20.15.0-win-x86.zip
0945b75af2eb884790064d90dc2e05cb3443c196c2ff546d7354b81a0721f882  node-v20.15.0-x64.msi
13961959ef59ba6312f17539702aef174f643344e7a058cff076ed543e661c62  node-v20.15.0-x86.msi
cc94ef66ccfc2377fab3799c23e3a8762159db8a3726773f4ab6972f2709d16d  win-arm64/node.exe
29f88cf01739d3dd344f32686a79c8afbb03fc60117db9e42236e414b3be366e  win-arm64/node.lib
5e3b9f6371bc0935eea3517f7c4e3d0b0395346e5778bf838cb8876fc25f4c1b  win-arm64/node_pdb.7z
540f96f6dba9ea7af99634bcba6be0428e8caf744e6f1fc8d62c8f863d3d2d5b  win-arm64/node_pdb.zip
d7d3dfa9f0adb95b8519ea07187b29d1f100ae6999c1d303999175b1b388556d  win-x64/node.exe
87056190b7cd06f40058f8e059efd328cdcc7600b825afa102c0aa5039865af5  win-x64/node.lib
b8254a8ad38ba19ecddc9558799217064a1fc623c0e4138ff7e7e15aa2361e4d  win-x64/node_pdb.7z
150efbfdd6d5b769c1a5eacc614eb617fda1292af8012b27f234251ee2483f0b  win-x64/node_pdb.zip
4d8181d8364557fdbc16b1790f80ab429b6e7f74bf6b018cb72ceaeda40cae73  win-x86/node.exe
a60232f8f24ff9fd6c6bf516f4c3c087ca1fa4df2f19bb7af68d7d64b64c3805  win-x86/node.lib
372ab8d4cd3b08e5194badeee222658fa13862454fd55d67fce3a5a3bd1ccd93  win-x86/node_pdb.7z
006af2174f1dcb90523e0412ab6bc65ab759d54d2a2f1d71f8ae6006b0b9a4eb  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEzGj1oxBv9EgyLkjtJ/XjjVsKIV8FAmZ0UlwACgkQJ/XjjVsK
IV95HRAAh/ttO9cS/86gtHLGohUoVwQG4D8vl+WDsW6L7Bd8RlYGPguwaJA0NboP
JVFgnFAfPlXOVgfy8nxwY0sdkLNfq8BROIFadwpUGI5berh2WB9XTOBf0e+Isfd5
zf5UAcXpQ71okx+EI7q7OKrhDkj0dUZJT3TCoT5rokdF/Sl00Gg2GMpV0F4VjP8L
GoJPTWjTVrhud8QbJ5R2HTR98sW/9A0ZAHtYfGl72qlR4n58AFegaIgq9WxJP+y2
7mQesP2ojayLfg2X6krzOGBGYMcGoAuTuu8JP0+6n2MPQMiqPOimpg5EB6n8TFB4
TBmfXmYdq7t2uR8cX5QXMEEsYwEyIPORNdI2LvPg3dj9vb4L65VIIpv0Jiu/4FQp
b7C2OvP8nISFxCcy9P7Roe1HFFBjpOHa6RAk+FaQ2N75/2r13dKcZwonwnDxyXaY
e5NDEmYQo9ByesIm3j1Bo0a6nn7O4EM6t44cYgAzSrZuy8s5ZIOsh4FTeJcQiT2r
5/OwKiIa5ad2FxW9KWCVtexWO7Esu/UX0hDFpfXiHkjF9rwfq34CUgSSxeXSTA2u
VhYDe5UaqKJnLiquPyac3LQI3GR65GvO9EFJ5Mm5rwMIzMVuXv5MhTyifsw2xu/m
psUIkGS/gGqQbbOCA+6P34MXEtRuvL/E4Fme5iYlt69RoLXI8PA=
=fBlB
-----END PGP SIGNATURE-----
```
