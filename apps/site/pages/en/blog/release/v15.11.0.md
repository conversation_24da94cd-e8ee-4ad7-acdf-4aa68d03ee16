---
date: '2021-03-03T06:18:16.684Z'
category: release
title: Node v15.11.0 (Current)
layout: blog-post
author: <PERSON><PERSON><PERSON>
---

### Notable Changes

- [[`a3e3156b52`](https://github.com/nodejs/node/commit/a3e3156b52)] - **(SEMVER-MINOR)** **crypto**: make FIPS related options always awailable (Vít Ondruch) [#36341](https://github.com/nodejs/node/pull/36341)
- [[`9ba5c0f9ba`](https://github.com/nodejs/node/commit/9ba5c0f9ba)] - **(SEMVER-MINOR)** **errors**: remove experimental from --enable-source-maps (<PERSON>) [#37362](https://github.com/nodejs/node/pull/37362)

### Commits

- [[`d039e6fa80`](https://github.com/nodejs/node/commit/d039e6fa80)] - **assert**: refactor to avoid unsafe array iteration (<PERSON>) [#37344](https://github.com/nodejs/node/pull/37344)
- [[`d2e5529e08`](https://github.com/nodejs/node/commit/d2e5529e08)] - **bootstrap**: include v8 module into the builtin snapshot (Joyee Cheung) [#36943](https://github.com/nodejs/node/pull/36943)
- [[`59861bac0e`](https://github.com/nodejs/node/commit/59861bac0e)] - **bootstrap**: include fs module into the builtin snapshot (Joyee Cheung) [#36943](https://github.com/nodejs/node/pull/36943)
- [[`458a4108b7`](https://github.com/nodejs/node/commit/458a4108b7)] - **buffer**: make Blob's constructor more spec-compliant (Michaël Zasso) [#37361](https://github.com/nodejs/node/pull/37361)
- [[`0d564ce214`](https://github.com/nodejs/node/commit/0d564ce214)] - **buffer**: make Blob's slice method more spec-compliant (Michaël Zasso) [#37361](https://github.com/nodejs/node/pull/37361)
- [[`ddae112133`](https://github.com/nodejs/node/commit/ddae112133)] - **child_process**: fix spawn and fork abort behavior (Nitzan Uziely) [#37325](https://github.com/nodejs/node/pull/37325)
- [[`b1e188de8d`](https://github.com/nodejs/node/commit/b1e188de8d)] - **crypto**: refactor hasAnyNotIn to avoid unsafe array iteration (Antoine du Hamel) [#37433](https://github.com/nodejs/node/pull/37433)
- [[`291d9e9936`](https://github.com/nodejs/node/commit/291d9e9936)] - **crypto**: check ed/x webcrypto key import algorithm names (Filip Skokan) [#37305](https://github.com/nodejs/node/pull/37305)
- [[`a3e3156b52`](https://github.com/nodejs/node/commit/a3e3156b52)] - **(SEMVER-MINOR)** **crypto**: make FIPS related options always awailable (Vít Ondruch) [#36341](https://github.com/nodejs/node/pull/36341)
- [[`b634469c38`](https://github.com/nodejs/node/commit/b634469c38)] - **crypto**: refactor to avoid unsafe array iteration (Antoine du Hamel) [#37364](https://github.com/nodejs/node/pull/37364)
- [[`01773ab614`](https://github.com/nodejs/node/commit/01773ab614)] - **crypto**: use BoringSSL compatible errors (Shelley Vohr) [#37297](https://github.com/nodejs/node/pull/37297)
- [[`f3d67000a0`](https://github.com/nodejs/node/commit/f3d67000a0)] - **deps**: upgrade npm to 7.6.0 (Ruy Adorno) [#37559](https://github.com/nodejs/node/pull/37559)
- [[`e1045f1004`](https://github.com/nodejs/node/commit/e1045f1004)] - **deps**: upgrade npm to 7.5.6 (Ruy Adorno) [#37496](https://github.com/nodejs/node/pull/37496)
- [[`80d3c118f4`](https://github.com/nodejs/node/commit/80d3c118f4)] - **deps**: V8: cherry-pick 373f4ae739ee (Richard Lau) [#37505](https://github.com/nodejs/node/pull/37505)
- [[`1408de7e24`](https://github.com/nodejs/node/commit/1408de7e24)] - **deps**: cherry-pick 8957d4677aa794c230577f234071af0 from V8 upstream (Antoine du Hamel) [#37471](https://github.com/nodejs/node/pull/37471)
- [[`725d48ae77`](https://github.com/nodejs/node/commit/725d48ae77)] - **doc**: remove experimental from --enable-source-maps (Colin Ihrig) [#37540](https://github.com/nodejs/node/pull/37540)
- [[`5d939b7a49`](https://github.com/nodejs/node/commit/5d939b7a49)] - **doc**: fix typo in doc/api/packages.md (marsonya) [#37536](https://github.com/nodejs/node/pull/37536)
- [[`cbfc6b1692`](https://github.com/nodejs/node/commit/cbfc6b1692)] - **doc**: document how to register external bindings for snapshot (Joyee Cheung) [#37463](https://github.com/nodejs/node/pull/37463)
- [[`dd7a04dc9f`](https://github.com/nodejs/node/commit/dd7a04dc9f)] - **doc**: fix typo "director" instead of "directory" (humanwebpl) [#37523](https://github.com/nodejs/node/pull/37523)
- [[`ba81e7cb5e`](https://github.com/nodejs/node/commit/ba81e7cb5e)] - **doc**: revise LTS text in collaborator guide (Rich Trott) [#37527](https://github.com/nodejs/node/pull/37527)
- [[`7529a97a5c`](https://github.com/nodejs/node/commit/7529a97a5c)] - **doc**: revise CI text in collaborator guide (Rich Trott) [#37526](https://github.com/nodejs/node/pull/37526)
- [[`1285b907ce`](https://github.com/nodejs/node/commit/1285b907ce)] - **doc**: revise objections section of collaborator guide (Rich Trott) [#37525](https://github.com/nodejs/node/pull/37525)
- [[`bc86208a0a`](https://github.com/nodejs/node/commit/bc86208a0a)] - **doc**: revise premature disclosure text in collaborator guide (Rich Trott) [#37524](https://github.com/nodejs/node/pull/37524)
- [[`46af56752e`](https://github.com/nodejs/node/commit/46af56752e)] - **doc**: change links to use HEAD in top level docs (Michael Dawson) [#37494](https://github.com/nodejs/node/pull/37494)
- [[`3b737e63ce`](https://github.com/nodejs/node/commit/3b737e63ce)] - **doc**: apply sentence case to headers in doc/guides (marsonya) [#37506](https://github.com/nodejs/node/pull/37506)
- [[`fb5e5bed21`](https://github.com/nodejs/node/commit/fb5e5bed21)] - **doc**: fix typo in webcrypto.md (marsonya) [#37507](https://github.com/nodejs/node/pull/37507)
- [[`3b7cb75554`](https://github.com/nodejs/node/commit/3b7cb75554)] - **doc**: document the NO_COLOR and FORCE_COLOR env vars (James M Snell) [#37477](https://github.com/nodejs/node/pull/37477)
- [[`0fac27d546`](https://github.com/nodejs/node/commit/0fac27d546)] - **doc**: add url.resolve replacement example (Antoine du Hamel) [#37501](https://github.com/nodejs/node/pull/37501)
- [[`2228f44b25`](https://github.com/nodejs/node/commit/2228f44b25)] - **doc**: apply sentence case to guides headers (marsonya) [#37497](https://github.com/nodejs/node/pull/37497)
- [[`617819e4fb`](https://github.com/nodejs/node/commit/617819e4fb)] - **doc**: update CI requirements for landing pull requests (Antoine du Hamel) [#37308](https://github.com/nodejs/node/pull/37308)
- [[`4a40759b33`](https://github.com/nodejs/node/commit/4a40759b33)] - **doc**: recommend queueMicrotask over process.nextTick (James M Snell) [#37484](https://github.com/nodejs/node/pull/37484)
- [[`834f63793a`](https://github.com/nodejs/node/commit/834f63793a)] - **doc**: apply sentence case to headers in doc/guides (marsonya) [#37478](https://github.com/nodejs/node/pull/37478)
- [[`7ac0820da0`](https://github.com/nodejs/node/commit/7ac0820da0)] - **doc**: fix typo in doc/api/http2/md (marsonya) [#37479](https://github.com/nodejs/node/pull/37479)
- [[`4ad7a78448`](https://github.com/nodejs/node/commit/4ad7a78448)] - **doc**: alphabetize vm Module class properties (Rich Trott) [#37451](https://github.com/nodejs/node/pull/37451)
- [[`a193d7ca87`](https://github.com/nodejs/node/commit/a193d7ca87)] - **doc**: alphabetize crypto Cipher class entries (Rich Trott) [#37450](https://github.com/nodejs/node/pull/37450)
- [[`54b6f1bcf9`](https://github.com/nodejs/node/commit/54b6f1bcf9)] - **doc**: use HEAD for links in api docs (Michael Dawson) [#37437](https://github.com/nodejs/node/pull/37437)
- [[`549d24b8ad`](https://github.com/nodejs/node/commit/549d24b8ad)] - **doc**: fix alignment of parameters (Michael Dawson) [#37422](https://github.com/nodejs/node/pull/37422)
- [[`f3559a922b`](https://github.com/nodejs/node/commit/f3559a922b)] - **doc**: fix typo in doc/api/esm.md (marsonya) [#37400](https://github.com/nodejs/node/pull/37400)
- [[`c3d236d405`](https://github.com/nodejs/node/commit/c3d236d405)] - **doc**: fix "referred to" in fs docs (Tobias Nießen) [#37388](https://github.com/nodejs/node/pull/37388)
- [[`9ac8c74539`](https://github.com/nodejs/node/commit/9ac8c74539)] - **doc**: document x509 error codes (Dan Čermák) [#37096](https://github.com/nodejs/node/pull/37096)
- [[`9a454afcd6`](https://github.com/nodejs/node/commit/9a454afcd6)] - **doc**: fix typo in esm.md (Jay Tailor) [#37417](https://github.com/nodejs/node/pull/37417)
- [[`b3bf3d9824`](https://github.com/nodejs/node/commit/b3bf3d9824)] - **doc**: use HEAD in links where possible (Michael Dawson) [#37421](https://github.com/nodejs/node/pull/37421)
- [[`6675342cd9`](https://github.com/nodejs/node/commit/6675342cd9)] - **doc**: clarify that async_hook callbacks cannot be async (James M Snell) [#37384](https://github.com/nodejs/node/pull/37384)
- [[`4b54c10500`](https://github.com/nodejs/node/commit/4b54c10500)] - **doc**: use \*\*Default:\*\* more consistently (Colin Ihrig) [#37387](https://github.com/nodejs/node/pull/37387)
- [[`f20ce47dbb`](https://github.com/nodejs/node/commit/f20ce47dbb)] - **doc,child_process**: `pid` can be `undefined` when `ENOENT` (dr-js) [#37014](https://github.com/nodejs/node/pull/37014)
- [[`6205e29cb9`](https://github.com/nodejs/node/commit/6205e29cb9)] - **doc,lib**: prepare for stricter multi-line array linting (Rich Trott) [#37088](https://github.com/nodejs/node/pull/37088)
- [[`9ba5c0f9ba`](https://github.com/nodejs/node/commit/9ba5c0f9ba)] - **(SEMVER-MINOR)** **errors**: remove experimental from --enable-source-maps (Benjamin Coe) [#37362](https://github.com/nodejs/node/pull/37362)
- [[`c0cdb83433`](https://github.com/nodejs/node/commit/c0cdb83433)] - **fs**: fix writeFile signal does not close file (Nitzan Uziely) [#37402](https://github.com/nodejs/node/pull/37402)
- [[`e8b1e2c0a3`](https://github.com/nodejs/node/commit/e8b1e2c0a3)] - **fs**: fix pre-aborted writeFile AbortSignal file leak (Nitzan Uziely) [#37393](https://github.com/nodejs/node/pull/37393)
- [[`6b42e65983`](https://github.com/nodejs/node/commit/6b42e65983)] - **fs**: fixup negative length in fs.truncate (James M Snell) [#37483](https://github.com/nodejs/node/pull/37483)
- [[`d141fce634`](https://github.com/nodejs/node/commit/d141fce634)] - **fs**: use createDeferredPromise() in promises.watch() (Colin Ihrig) [#37386](https://github.com/nodejs/node/pull/37386)
- [[`bb81accb16`](https://github.com/nodejs/node/commit/bb81accb16)] - **lib**: use \<array\>.push and \<array\>.unshift instead of \<array\>.concat (Antoine du Hamel) [#37239](https://github.com/nodejs/node/pull/37239)
- [[`dc3c299862`](https://github.com/nodejs/node/commit/dc3c299862)] - **lib**: remove outdated todo comment (Antoine du Hamel) [#37396](https://github.com/nodejs/node/pull/37396)
- [[`856d20b772`](https://github.com/nodejs/node/commit/856d20b772)] - **lib**: add URI handling functions to primordials (Antoine du Hamel) [#37394](https://github.com/nodejs/node/pull/37394)
- [[`a1ed78cb3b`](https://github.com/nodejs/node/commit/a1ed78cb3b)] - **module**: improve support of data: URLs (Antoine du Hamel) [#37392](https://github.com/nodejs/node/pull/37392)
- [[`27816eac61`](https://github.com/nodejs/node/commit/27816eac61)] - **node-api**: force env shutdown deferring behavior (Gabriel Schulhof) [#37303](https://github.com/nodejs/node/pull/37303)
- [[`f1381f7a7a`](https://github.com/nodejs/node/commit/f1381f7a7a)] - **src**: fix alloc-dealloc-mismatch in node_snapshotable.h (Darshan Sen) [#37443](https://github.com/nodejs/node/pull/37443)
- [[`5ea2ed611f`](https://github.com/nodejs/node/commit/5ea2ed611f)] - **src**: fix ETW_WRITE_EMPTY_EVENT macro (Michaël Zasso) [#37334](https://github.com/nodejs/node/pull/37334)
- [[`96bcd52d3e`](https://github.com/nodejs/node/commit/96bcd52d3e)] - **src**: disable unfixable MSVC warnings (Michaël Zasso) [#37334](https://github.com/nodejs/node/pull/37334)
- [[`c75f5f372d`](https://github.com/nodejs/node/commit/c75f5f372d)] - **src**: avoid implicit type conversions (take 2) (Michaël Zasso) [#37334](https://github.com/nodejs/node/pull/37334)
- [[`e400f8c9c8`](https://github.com/nodejs/node/commit/e400f8c9c8)] - **src**: support serialization of binding data (Joyee Cheung) [#36943](https://github.com/nodejs/node/pull/36943)
- [[`daad7bbd34`](https://github.com/nodejs/node/commit/daad7bbd34)] - **src**: adjust THP sysfs config token retrieval and file closure (James Addison) [#37187](https://github.com/nodejs/node/pull/37187)
- [[`4cc76457d9`](https://github.com/nodejs/node/commit/4cc76457d9)] - **stream**: move duplicated code to an internal module (Rich Trott) [#37508](https://github.com/nodejs/node/pull/37508)
- [[`3d3df0c005`](https://github.com/nodejs/node/commit/3d3df0c005)] - **stream**: add AbortSignal support to finished (Nitzan Uziely) [#37354](https://github.com/nodejs/node/pull/37354)
- [[`429dffd32e`](https://github.com/nodejs/node/commit/429dffd32e)] - **stream**: add AbortSignal to promisified pipeline (Nitzan Uziely) [#37359](https://github.com/nodejs/node/pull/37359)
- [[`9696cf7142`](https://github.com/nodejs/node/commit/9696cf7142)] - **test**: remove FLAKY status for test-http2-multistream-destroy-on-read-tls (Rich Trott) [#37533](https://github.com/nodejs/node/pull/37533)
- [[`453113938d`](https://github.com/nodejs/node/commit/453113938d)] - **test**: make status file names consistent (Rich Trott) [#37532](https://github.com/nodejs/node/pull/37532)
- [[`00b3446a8e`](https://github.com/nodejs/node/commit/00b3446a8e)] - **test**: account for pending deprecations in esm test (Rich Trott) [#37542](https://github.com/nodejs/node/pull/37542)
- [[`f2aa305348`](https://github.com/nodejs/node/commit/f2aa305348)] - **test**: fix incorrect timers-promisified case (ttzztztz) [#37425](https://github.com/nodejs/node/pull/37425)
- [[`ce7fbbf94c`](https://github.com/nodejs/node/commit/ce7fbbf94c)] - **test**: fix typo in test_node_crypto.cc (Ikko Ashimine) [#37469](https://github.com/nodejs/node/pull/37469)
- [[`ba319f0c60`](https://github.com/nodejs/node/commit/ba319f0c60)] - **test**: remove FLAKY for test-http2-compat-client-upload-reject (Rich Trott) [#37462](https://github.com/nodejs/node/pull/37462)
- [[`dfa0440341`](https://github.com/nodejs/node/commit/dfa0440341)] - **test**: validate no debug info for http2 (Michael Dawson) [#37447](https://github.com/nodejs/node/pull/37447)
- [[`b38404ee17`](https://github.com/nodejs/node/commit/b38404ee17)] - **test**: remove FLAKY designation for test-http2-client-upload-reject (Rich Trott) [#37461](https://github.com/nodejs/node/pull/37461)
- [[`b569105183`](https://github.com/nodejs/node/commit/b569105183)] - **test**: clarify usage of tmpdir.refresh() (Darshan Sen) [#37383](https://github.com/nodejs/node/pull/37383)
- [[`4f41900687`](https://github.com/nodejs/node/commit/4f41900687)] - **test**: update upload.zip to be uncorrupted (Greg Ziskind) [#37294](https://github.com/nodejs/node/pull/37294)
- [[`d5c311ed15`](https://github.com/nodejs/node/commit/d5c311ed15)] - **test**: fix flaky test-worker-prof (Rich Trott) [#37372](https://github.com/nodejs/node/pull/37372)
- [[`df538ebc8e`](https://github.com/nodejs/node/commit/df538ebc8e)] - **test**: fix flaky test-webcrypto-encrypt-decrypt-aes (Darshan Sen) [#37380](https://github.com/nodejs/node/pull/37380)
- [[`19d6eb929c`](https://github.com/nodejs/node/commit/19d6eb929c)] - **test**: fix flaky test-fs-promises-file-handle-read (Rich Trott) [#37371](https://github.com/nodejs/node/pull/37371)
- [[`c554aa149c`](https://github.com/nodejs/node/commit/c554aa149c)] - **test,child_process**: add check for `subProcess.pid` (dr-js) [#37014](https://github.com/nodejs/node/pull/37014)
- [[`5c27fd73b0`](https://github.com/nodejs/node/commit/5c27fd73b0)] - **tools**: run doctool tests on GitHub Actions CI (Antoine du Hamel) [#37398](https://github.com/nodejs/node/pull/37398)
- [[`49013fcee1`](https://github.com/nodejs/node/commit/49013fcee1)] - **tools**: make comma-dangle ESLint rule more stringent … (Rich Trott) [#37088](https://github.com/nodejs/node/pull/37088)
- [[`31f4600b7a`](https://github.com/nodejs/node/commit/31f4600b7a)] - **worker**: fix interaction of terminate() with messaging port (Anna Henningsen) [#37319](https://github.com/nodejs/node/pull/37319)
- [[`d93137b2a9`](https://github.com/nodejs/node/commit/d93137b2a9)] - **workers**: fix spawning from preload scripts (James M Snell) [#37481](https://github.com/nodejs/node/pull/37481)

Windows 32-bit Installer: https://nodejs.org/dist/v15.11.0/node-v15.11.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v15.11.0/node-v15.11.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v15.11.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v15.11.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v15.11.0/node-v15.11.0.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v15.11.0/node-v15.11.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v15.11.0/node-v15.11.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v15.11.0/node-v15.11.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v15.11.0/node-v15.11.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v15.11.0/node-v15.11.0-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v15.11.0/node-v15.11.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v15.11.0/node-v15.11.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v15.11.0/node-v15.11.0.tar.gz \
Other release files: https://nodejs.org/dist/v15.11.0/ \
Documentation: https://nodejs.org/docs/v15.11.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

dbe6ab79a7fcff06e72d5a062c5850132d06ecc79bbda5049d33a0702483f906  node-v15.11.0-aix-ppc64.tar.gz
def5bea3d66e1ca4239d2c6cb22be9b841cd937f037f7b13976960c895ebf803  node-v15.11.0-darwin-x64.tar.gz
521b1fe91fc039a6be2aa4299ba9838a323fca2eeb9360a9fb060467c3609454  node-v15.11.0-darwin-x64.tar.xz
9d37f33ce9c19adc08336a707f2ef352694bfff19001b2c835b2bcc794f3dad0  node-v15.11.0-headers.tar.gz
dbde89dce1aecdaaa278df6df5d71aa84c3981e516d0881171a594c1a53924d3  node-v15.11.0-headers.tar.xz
bac8f957c35df2985ab10098d7ee494de28872ce8382df412ef745895f30f0d8  node-v15.11.0-linux-arm64.tar.gz
6f88a96fe56cc3fb8a5983adf77c6f3bcabd957c61f9bb909806791a88cce059  node-v15.11.0-linux-arm64.tar.xz
5afd0dd1bcbbca579ebbd15e0269c0f3a2070f25a4a91772a8544f04f10418fd  node-v15.11.0-linux-armv7l.tar.gz
d5358ab1128819fd4e422489d86b797e29580ebfde70cd57d50b138c3170746a  node-v15.11.0-linux-armv7l.tar.xz
0431af5015fc11b62b99a1e043ffadda1a765ac16e3b4267a3405635cd63c0a8  node-v15.11.0-linux-ppc64le.tar.gz
3770d55974da2fa2752ad0544f37ed40771b11097b3d1884000bfa858a26c755  node-v15.11.0-linux-ppc64le.tar.xz
b7fde7316347ccb718125cdb9cce305ab3b72d6df2eca6eba04f1087d198db51  node-v15.11.0-linux-s390x.tar.gz
aeeb11ca9631c63ebd95341fb601c484080fc622cbc84ed3079939b2cda27c98  node-v15.11.0-linux-s390x.tar.xz
74df02fbe07f280ffbd20db01cd876db2bfc3463c3620849d5b4c2b4a21216e8  node-v15.11.0-linux-x64.tar.gz
389de1384e20ca0e878de359c3b4afb1e0e8f1ca5a3cc5059177cf04d5ed43a5  node-v15.11.0-linux-x64.tar.xz
c5831187bcd8d336c71a5cb4765bd3725ba14d01844eb56f33b3566e6964fae6  node-v15.11.0.pkg
367aea2415df651bf23242e1b627071173f044ffd9f216d9ff69d3322a889bad  node-v15.11.0.tar.gz
1a7091a210423970619b4af95dba6f4c6e2b576b9700460e5220afff24a8d2d1  node-v15.11.0.tar.xz
3c3dd5b380826fa9285aae2be1a55b56df1334da1b88175cb9964d4a6d9da1bd  node-v15.11.0-win-x64.7z
57904b14bb30bc8931a09f4d5d131f5c755093b0608cdd7d30d1f03cc41e61b2  node-v15.11.0-win-x64.zip
ff8684b8b3d86e43c14ab171e747c474729e0de539280ef2355a93a39cd1f2f9  node-v15.11.0-win-x86.7z
e4bf3e877211ecec1bc6d26849b4317e5ce0c8b7787331b519e78847a988ae3b  node-v15.11.0-win-x86.zip
5a792858e21a58050801d30885ca5c96dc908468ceb37783c219175ea67d923b  node-v15.11.0-x64.msi
57e93fe0a0bc4b56b4a3b913c956f96db2afefc79553a4dc06488f44985c3ce2  node-v15.11.0-x86.msi
1bfa1c2660078a4620d320ebde339d183ecd725e38febecd873cbdfda4edcd6a  win-x64/node.exe
0692711edaf626282aaeff0a28cce749488d03a5d1db6490c7bb19866ec4aedd  win-x64/node.lib
4c9e4b326e142cbc8ea55d07f10b90e37e982a7bd44f4d4ad40b0d887fbdaded  win-x64/node_pdb.7z
b1095c438bccc4495338c109b05be23bb8ebce771faebf21e41660b54b5d312d  win-x64/node_pdb.zip
2cddc4bc386cadb033c7b717180255150a5c6688d75d47a3705f0407119ab8d3  win-x86/node.exe
f884d9353d2b848084edd4b5be8bf4a2b84a2ce2c45f22dc569ebbf59314f5d5  win-x86/node.lib
05c5b5c4ddacf0f50baf884695a6293dc63fa74b3ff1f19d474ab549f5536ecd  win-x86/node_pdb.7z
d98fef2119452dc2307e3e353ab4284e49bb686ecd9c624a05919681aa6c71e5  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEj8yhP+8dDC6RAI4Jdw96mlrhVgAFAmA/KB4ACgkQdw96mlrh
VgBoCA//ecXWCT6/7AuEN3nsG3l0r57ZOzDdGWfE6ACzZmsJBolcWcDDnJkgMhrV
2f583weOBBf0wNjPPLeplwLs5roeXi1jx2XrFfc4olleIKqM62QRACcL3hNnYjoT
jh6S892sPtIDADTj7VHF/cjNcJ4ygihNV6KZihPFM1xHrJQ2/dta/CiwZxEhEoAF
RCmNksMiZb7/09GTeQb6pk/uUkh1bw/FvhGtylLjPcXwECq0hCfDXNfhEbNaMtnD
JLIMdtQtwlI0xDP5B9vnPT/ErUYeFQcbX6Dhuh33tmLTcOm3TK1HlRU28h8CmPvu
8fEBAEWPh2nvuIWPXpwMrTxsvvdqeozfgTsgpGENKYizYHE/EiFyjQ5JKM9xPDKT
Pujcc6w9qIxFuuIZEU+sxEBduH8H9SqNu7Cpe9ozaKV0ISeCXl3Ad3xZ8/jvceVy
hfn7XZ6HNDD39h3RHhGfGovcqLbil91XG7/sJtWd6TF9FZd5IrNfjlkBNsH2A/Gt
5XUjA2Xzrn1aY/A4N3ZYmWPIaTOf3W3IT6B5brjKEyCSLIxNZrhMUFm6GTYx8J+M
YtcHyBmveb5tC7q8RWbwS1l0+/EV2NqvgRwYZMPTSNnqQIM2A4VbkXgyvkJjh9xF
mijQgrwnO0OucCRNCV7HGrJIgikHEDLdAkLR6MOQgQvzAI6GtU0=
=Vh03
-----END PGP SIGNATURE-----

```
