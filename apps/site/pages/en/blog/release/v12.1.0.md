---
date: '2019-04-29T14:43:24.760Z'
category: release
title: Node v12.1.0 (Current)
layout: blog-post
author: <PERSON><PERSON><PERSON>
---

### Notable changes

- **intl**:
  - Update ICU to 64.2. This adds support for Japanese Era (Reiwa) (<PERSON><PERSON><PERSON><PERSON>) [#27361](https://github.com/nodejs/node/pull/27361).
  - Fixes a bug in ICU that affected Node.js 12.0.0 in the case where `new Date().toLocaleString()` was called with a non-default locale (<PERSON>) [#27415](https://github.com/nodejs/node/pull/27415).
- **C++ API**:
  - Added an overload `EmitAsyncDestroy` that can be used during garbage collection (<PERSON>) [#27255](https://github.com/nodejs/node/pull/27255).

### Commits

- [[`8ca110cc6f`](https://github.com/nodejs/node/commit/8ca110cc6f)] - **benchmark**: fix http bench-parser.js (<PERSON>) [#27359](https://github.com/nodejs/node/pull/27359)
- [[`2f9bafba08`](https://github.com/nodejs/node/commit/2f9bafba08)] - **bootstrap**: delay the instantiation of maps in per-context scripts (Joyee Cheung) [#27371](https://github.com/nodejs/node/pull/27371)
- [[`e7026f1428`](https://github.com/nodejs/node/commit/e7026f1428)] - **build**: allow icu download to use other hashes besides md5 (Steven R. Loomis) [#27370](https://github.com/nodejs/node/pull/27370)
- [[`50234460f9`](https://github.com/nodejs/node/commit/50234460f9)] - **build**: disable custom v8 snapshot by default (Joyee Cheung) [#27365](https://github.com/nodejs/node/pull/27365)
- [[`b21b28f653`](https://github.com/nodejs/node/commit/b21b28f653)] - **crypto**: update root certificates (Sam Roberts) [#27374](https://github.com/nodejs/node/pull/27374)
- [[`3282ccb845`](https://github.com/nodejs/node/commit/3282ccb845)] - **deps**: backport ICU-20575 to fix err/crasher (Steven R. Loomis) [#27435](https://github.com/nodejs/node/pull/27435)
- [[`e922a22725`](https://github.com/nodejs/node/commit/e922a22725)] - **deps**: backport ICU-20558 to fix Intl crasher (Steven R. Loomis) [#27415](https://github.com/nodejs/node/pull/27415)
- [[`d852d9e904`](https://github.com/nodejs/node/commit/d852d9e904)] - **deps**: update ICU to 64.2 (Ujjwal Sharma) [#27361](https://github.com/nodejs/node/pull/27361)
- [[`ee80a210ff`](https://github.com/nodejs/node/commit/ee80a210ff)] - **dgram**: change 'this' to 'self' for 'isConnected' (MaleDong) [#27338](https://github.com/nodejs/node/pull/27338)
- [[`8302148c83`](https://github.com/nodejs/node/commit/8302148c83)] - **doc**: add Node 12 to the first list of versions (Rivaldo Junior) [#27414](https://github.com/nodejs/node/pull/27414)
- [[`f6ceefa4bd`](https://github.com/nodejs/node/commit/f6ceefa4bd)] - **doc**: update comment in bootstrap for primordials (Myles Borins) [#27398](https://github.com/nodejs/node/pull/27398)
- [[`9c30806fb5`](https://github.com/nodejs/node/commit/9c30806fb5)] - **doc**: simplify GOVERNANCE.md text (Rich Trott) [#27354](https://github.com/nodejs/node/pull/27354)
- [[`453510c7ef`](https://github.com/nodejs/node/commit/453510c7ef)] - **doc**: fix pull request number (Ruben Bridgewater) [#27336](https://github.com/nodejs/node/pull/27336)
- [[`36762883a0`](https://github.com/nodejs/node/commit/36762883a0)] - **doc**: clarify behaviour of writeFile(fd) (Sam Roberts) [#27282](https://github.com/nodejs/node/pull/27282)
- [[`f70588fb67`](https://github.com/nodejs/node/commit/f70588fb67)] - **doc**: fix v12.0.0 changelog id (Beth Griggs) [#27368](https://github.com/nodejs/node/pull/27368)
- [[`a6d1fa5954`](https://github.com/nodejs/node/commit/a6d1fa5954)] - **doc**: simplify Collaborator pre-nomination text (Rich Trott) [#27348](https://github.com/nodejs/node/pull/27348)
- [[`dd709fc84a`](https://github.com/nodejs/node/commit/dd709fc84a)] - **lib**: throw a special error in internal/assert (Joyee Cheung) [#26635](https://github.com/nodejs/node/pull/26635)
- [[`4dfe54a89a`](https://github.com/nodejs/node/commit/4dfe54a89a)] - **module**: initialize module_wrap.callbackMap during pre-execution (Joyee Cheung) [#27323](https://github.com/nodejs/node/pull/27323)
- [[`8b5d73867f`](https://github.com/nodejs/node/commit/8b5d73867f)] - **(SEMVER-MINOR)** **n-api**: do not require JS Context for `napi_async_destroy()` (Anna Henningsen) [#27255](https://github.com/nodejs/node/pull/27255)
- [[`d00014e599`](https://github.com/nodejs/node/commit/d00014e599)] - **process**: reduce the number of internal frames in async stack trace (Joyee Cheung) [#27392](https://github.com/nodejs/node/pull/27392)
- [[`dc510fb435`](https://github.com/nodejs/node/commit/dc510fb435)] - **report**: print common items first for readability (gengjiawen) [#27367](https://github.com/nodejs/node/pull/27367)
- [[`3614a00276`](https://github.com/nodejs/node/commit/3614a00276)] - **src**: refactor deprecated UVException in node_file.cc (gengjiawen) [#27280](https://github.com/nodejs/node/pull/27280)
- [[`071300b39d`](https://github.com/nodejs/node/commit/071300b39d)] - **src**: move OnMessage to node_errors.cc (Joyee Cheung) [#27304](https://github.com/nodejs/node/pull/27304)
- [[`81e7b49c8f`](https://github.com/nodejs/node/commit/81e7b49c8f)] - **src**: use predefined AliasedBuffer types in the code base (Joyee Cheung) [#27334](https://github.com/nodejs/node/pull/27334)
- [[`8089d29567`](https://github.com/nodejs/node/commit/8089d29567)] - **src**: apply clang-tidy modernize-deprecated-headers found by Jenkins CI (gengjiawen) [#27279](https://github.com/nodejs/node/pull/27279)
- [[`619c5b6eb3`](https://github.com/nodejs/node/commit/619c5b6eb3)] - **(SEMVER-MINOR)** **src**: do not require JS Context for `~AsyncResoure()` (Anna Henningsen) [#27255](https://github.com/nodejs/node/pull/27255)
- [[`809cf595eb`](https://github.com/nodejs/node/commit/809cf595eb)] - **(SEMVER-MINOR)** **src**: add `Environment` overload of `EmitAsyncDestroy` (Anna Henningsen) [#27255](https://github.com/nodejs/node/pull/27255)
- [[`7bc47cba2e`](https://github.com/nodejs/node/commit/7bc47cba2e)] - **src**: apply clang-tidy rule modernize-use-equals-default (gengjiawen) [#27264](https://github.com/nodejs/node/pull/27264)
- [[`ad42cd69cf`](https://github.com/nodejs/node/commit/ad42cd69cf)] - **src**: use std::vector\<size_t\> instead of IndexArray (Joyee Cheung) [#27321](https://github.com/nodejs/node/pull/27321)
- [[`228127fc67`](https://github.com/nodejs/node/commit/228127fc67)] - **src**: enable context snapshot after running per-context scripts (Joyee Cheung) [#27321](https://github.com/nodejs/node/pull/27321)
- [[`45d6106129`](https://github.com/nodejs/node/commit/45d6106129)] - **src**: enable snapshot with per-isolate data (Joyee Cheung) [#27321](https://github.com/nodejs/node/pull/27321)
- [[`631bea8fd2`](https://github.com/nodejs/node/commit/631bea8fd2)] - **src**: implement IsolateData serialization and deserialization (Joyee Cheung) [#27321](https://github.com/nodejs/node/pull/27321)
- [[`a636338945`](https://github.com/nodejs/node/commit/a636338945)] - **src**: allow creating NodeMainInstance that does not own the isolate (Joyee Cheung) [#27321](https://github.com/nodejs/node/pull/27321)
- [[`50732c1b3f`](https://github.com/nodejs/node/commit/50732c1b3f)] - **test**: refactor net-connect-handle-econnrefused (Luigi Pinca) [#27014](https://github.com/nodejs/node/pull/27014)
- [[`e9021cc498`](https://github.com/nodejs/node/commit/e9021cc498)] - **test**: move test-net-connect-handle-econnrefused (Luigi Pinca) [#27014](https://github.com/nodejs/node/pull/27014)
- [[`ebbed6047d`](https://github.com/nodejs/node/commit/ebbed6047d)] - **test**: rework to remove flakiness, and be parallel (Sam Roberts) [#27300](https://github.com/nodejs/node/pull/27300)
- [[`f0b2992f5c`](https://github.com/nodejs/node/commit/f0b2992f5c)] - **test**: fix ineffective error tests (Masashi Hirano) [#27333](https://github.com/nodejs/node/pull/27333)
- [[`d84a6d05a1`](https://github.com/nodejs/node/commit/d84a6d05a1)] - **test**: make test-worker-esm-missing-main more robust (Rich Trott) [#27340](https://github.com/nodejs/node/pull/27340)
- [[`8486917b9a`](https://github.com/nodejs/node/commit/8486917b9a)] - **test**: increase coverage in lib/internal/dns/promises.js (Rich Trott) [#27330](https://github.com/nodejs/node/pull/27330)
- [[`6ca0270320`](https://github.com/nodejs/node/commit/6ca0270320)] - **tls**: include invalid method name in thrown error (Sam Roberts) [#27390](https://github.com/nodejs/node/pull/27390)
- [[`dcbe5b9dff`](https://github.com/nodejs/node/commit/dcbe5b9dff)] - **tools**: update certdata.txt (Sam Roberts) [#27374](https://github.com/nodejs/node/pull/27374)
- [[`53f0ef36c0`](https://github.com/nodejs/node/commit/53f0ef36c0)] - **tools**: update LICENSE and tools/icu/current_ver.dep (Ujjwal Sharma) [#27361](https://github.com/nodejs/node/pull/27361)
- [[`481789c235`](https://github.com/nodejs/node/commit/481789c235)] - **tools**: fix use-after-free mkcodecache warning (Ben Noordhuis) [#27332](https://github.com/nodejs/node/pull/27332)
- [[`d62a3243b1`](https://github.com/nodejs/node/commit/d62a3243b1)] - **tools**: update tools/license-builder.sh (Ujjwal Sharma) [#27362](https://github.com/nodejs/node/pull/27362)
- [[`b44323f3de`](https://github.com/nodejs/node/commit/b44323f3de)] - **tools**: implement node_mksnapshot (Joyee Cheung) [#27321](https://github.com/nodejs/node/pull/27321)
- [[`ae2333db65`](https://github.com/nodejs/node/commit/ae2333db65)] - **util**: add prototype support for boxed primitives (Ruben Bridgewater) [#27351](https://github.com/nodejs/node/pull/27351)
- [[`8f3442809a`](https://github.com/nodejs/node/commit/8f3442809a)] - **util**: rename setIteratorBraces to getIteratorBraces (Ruben Bridgewater) [#27342](https://github.com/nodejs/node/pull/27342)
- [[`973d705aa9`](https://github.com/nodejs/node/commit/973d705aa9)] - **util**: improve `Symbol.toStringTag` handling (Ruben Bridgewater) [#27342](https://github.com/nodejs/node/pull/27342)

Windows 32-bit Installer: https://nodejs.org/dist/v12.1.0/node-v12.1.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v12.1.0/node-v12.1.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v12.1.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v12.1.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v12.1.0/node-v12.1.0.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v12.1.0/node-v12.1.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v12.1.0/node-v12.1.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v12.1.0/node-v12.1.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v12.1.0/node-v12.1.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v12.1.0/node-v12.1.0-aix-ppc64.tar.gz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v12.1.0/node-v12.1.0-sunos-x64.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v12.1.0/node-v12.1.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v12.1.0/node-v12.1.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v12.1.0/node-v12.1.0.tar.gz \
Other release files: https://nodejs.org/dist/v12.1.0/ \
Documentation: https://nodejs.org/docs/v12.1.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

dfedd5f7f31284b4b1c4cabc07c1f8619e998ebeac8183ea4c4b8cb1e1a61611  node-v12.1.0-aix-ppc64.tar.gz
57c592b13940aa44611aec08e7b425f35565a2c95c51736f433cb36eb65105b7  node-v12.1.0-darwin-x64.tar.gz
f676a9c9a59191773eeef346b5363282499bb4a782374342c35211c328e610a8  node-v12.1.0-darwin-x64.tar.xz
642e302f678f5f3583a2864428e9349c18e34ff7ac7baaa63cdcdd404da0fa1a  node-v12.1.0-headers.tar.gz
6e6970abc4883f60186d78ec98329040525f9ab28b8e71cc2bf5ede1757bc61a  node-v12.1.0-headers.tar.xz
e0ca3fe82c35d7e03b6a4c9983cf6797677f797148777b61c2bb3c01257026f2  node-v12.1.0-linux-arm64.tar.gz
67805a7976bef30d0e12211f18a99c6de405931e493fd32f451512473661ee10  node-v12.1.0-linux-arm64.tar.xz
e67fe3a0df6077ca2d936dbbd8db934fdbc2690c243d8cc78094f233e167bb23  node-v12.1.0-linux-armv7l.tar.gz
f6b97ca44afb6e8bf94292050dc0c5b4f49e9cb4a67d68a4f0a7c789688ab149  node-v12.1.0-linux-armv7l.tar.xz
ac5ce8f1523fe7712eb06f3414fe199eb9a8f9c703fb08c66a04071c5caa26cc  node-v12.1.0-linux-ppc64le.tar.gz
705883d23e5e0fb7bd13a73bcabbaebe3713570827fe0d8737b336d52d9cedbf  node-v12.1.0-linux-ppc64le.tar.xz
b81dbe47388b9f1c8369b8c7e61d26e46f57922ca1f7b42a5bbe10c8ee4a8dea  node-v12.1.0-linux-s390x.tar.gz
4c960f0000acdeda5d1d934271770b0c2a472d2c25a5b9f5153fd39bf94d2f3b  node-v12.1.0-linux-s390x.tar.xz
d23587e3dbb2baebb1d5f1418a64f1c8ce6a9315a2281bff7cf87c9d1ed34ee4  node-v12.1.0-linux-x64.tar.gz
331c43176a20e705c6f4fdb61c69fee44dd3c2c93a20410be2c13b4f8515ef7b  node-v12.1.0-linux-x64.tar.xz
aaedd28fadb6410e0f24cdc95d3c96ed875bdc3cca3444ac233a4a6374dd7d34  node-v12.1.0.pkg
a65f33f8ec6e517c531532c2dabc04cb0966650e183c64d7cb0dc85b4dbcc0c1  node-v12.1.0-sunos-x64.tar.gz
eab517854c8617394f6dc496507fc9eea6443deb0b2fcde75a229ba1045ecf66  node-v12.1.0-sunos-x64.tar.xz
3333a6cbf160657defece414f63b076fb758dc31af45cd9418c106141fdba0c5  node-v12.1.0.tar.gz
1efb792c689fed2e028025e1398e84193281f329427a17a62b0bffee8e771395  node-v12.1.0.tar.xz
6cd431f4d2eb8e35c8ec5397864503a43366b040f1d1099101d24d9f3f04bce6  node-v12.1.0-win-x64.7z
6dc3ef4a6b4ce527f187270a1b0c5560771126df487ab9ddc4c3cd3b37d57eb6  node-v12.1.0-win-x64.zip
e1ab604bfdc1f40dd9f2194ca171702638a02e5b7d2f0d344f3c5ec8ae8ee767  node-v12.1.0-win-x86.7z
3b70b63dc6ad0bdff2f26d434cf2f62c9d4afc4e410a6b4dd4aa935968f4c76b  node-v12.1.0-win-x86.zip
263a106284890f71f03d8a080b9fad4121eaaa47110cb365a4d69df6971b11e7  node-v12.1.0-x64.msi
aca7a76d4f10e828ea4b4f155047fb3f9c3c97293a19e7edabe917387726a5a2  node-v12.1.0-x86.msi
2958c6f2ec9ce14b05259e8f35cff54bc97f234e92c445ac57d176e158bc800a  win-x64/node.exe
76876e9a1a292e511c7f4a9f842d3d0a4059391c6a7460d53061beecc0a7340b  win-x64/node.lib
45b80eb795379e38ba78a5d02cbf9d1a7b7fb9e9c30ed0a4fa9e946f2e76dce3  win-x64/node_pdb.7z
e5454dffad46c426492c1003fa51fc46d6ec796917c0c1e04f2a32cb313fee5d  win-x64/node_pdb.zip
d067d9678dce1b19dc8b8ec3e741a46d328b875e7cc368572080e655d4a504b1  win-x86/node.exe
80e632bbfeaadbfa8aa630ee9a1f56719ffad468cb4b0aad31b3c9a19f0d0511  win-x86/node.lib
2a57a594eb6505e0a5d8dafc754df0dc6467f84bda09bf7161b583644f5f6d12  win-x86/node_pdb.7z
cb600f29df26488338ca3fc11cb37083176d29f05c6081755bca3e481422b40a  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEj8yhP+8dDC6RAI4Jdw96mlrhVgAFAlzHDP0ACgkQdw96mlrh
VgCZzRAAg0DsydjlBMdb9dAMNKtpiErQV7wNYtw7fAa1+157woVKSLMfjV9E+Z/j
eeR8HHjV+pirJ6sorIamlmeXdX4oYi158MGlGSeIEYcWs1v/HSXv+7L3kixkjDRZ
PxdQozpnWw06NosJzykKPhun+Bh2fO3GvJc0IziP9iqitjmZ+36wxi80AmEWyhp0
kSp5Pljdau85F80tsKiupYjvHJ5zoWvYuoH5lqCz/3RwJHwTLe1uyQLBPT2Na6wH
yApZJKaMVsWyJd1x4+Igh5cKSUvR6nj9zkqNnXVwKT3P4Bt8wL7WU+xlTMhxpCOK
fDYUbt0OWCly00lGRH4PztRF5SIQJfsHtBFfyl+WBggwn59vhrD0sh739OrDgcPj
aCRSjYVCwnvTGGsLkqIIRNuhz74GOUGfe130kNfQcerwLQgNSl9KN5bqBbwyAfJX
Jfx9XGqOKHWz6GSwH5IrQOsrPNJ29F+FmAEmwyVHK2zGhTE4r7yZVfI64L/Xxm6F
oL7K+HH8ixUp5MU3uKRVpHtrsHepTwP4VjZJ9+PZcneOIZcnmYX38YRZwUGULNfK
MEPHeNmjKyC0csFGWoJm+GFk4pYHxzKOj91mUuwjxanOKWeFWoJHWzvLDWDa+O8X
O1FWrKc/ytw5Ail/dLhb/B4FWJfQukjXNZ4HA8MM6FMh1AmXruY=
=nq7K
-----END PGP SIGNATURE-----

```
