---
date: '2023-03-14T19:02:15.962Z'
category: release
title: Node v19.8.0 (Current)
layout: blog-post
author: <PERSON><PERSON><PERSON>
---

### Notable Changes

- \[[`2fece54ca1`](https://github.com/nodejs/node/commit/2fece54ca1)] - **(SEMVER-MINOR)** **buffer**: add `Buffer.copyBytesFrom(...)` (<PERSON>) [#46500](https://github.com/nodejs/node/pull/46500)
- \[[`2eb887549a`](https://github.com/nodejs/node/commit/2eb887549a)] - **(SEMVER-MINOR)** **events**: add `listener` argument to `listenerCount` (Paolo <PERSON>) [#46523](https://github.com/nodejs/node/pull/46523)
- \[[`c1651bea41`](https://github.com/nodejs/node/commit/c1651bea41)] - **(SEMVER-MINOR)** **lib**: add `AsyncLocalStorage.bind()` and `.snapshot()` (flakey5) [#46387](https://github.com/nodejs/node/pull/46387)
- \[[`36f36b99b0`](https://github.com/nodejs/node/commit/36f36b99b0)] - **(SEMVER-MINOR)** **src**: add `fs.openAsBlob` to support File-backed Blobs (James M Snell) [#45258](https://github.com/nodejs/node/pull/45258)
- \[[`bb9b1c637d`](https://github.com/nodejs/node/commit/bb9b1c637d)] - **(SEMVER-MINOR)** **tls**: support automatic DHE (Tobias Nießen) [#46978](https://github.com/nodejs/node/pull/46978)
- \[[`1e20b05acd`](https://github.com/nodejs/node/commit/1e20b05acd)] - **(SEMVER-MINOR)** **url**: implement `URLSearchParams` `size` getter (James M Snell) [#46308](https://github.com/nodejs/node/pull/46308)
- \[[`60e5f45141`](https://github.com/nodejs/node/commit/60e5f45141)] - **(SEMVER-MINOR)** **wasi**: add support for version when creating WASI (Michael Dawson) [#46469](https://github.com/nodejs/node/pull/46469)
- \[[`a646a22d0f`](https://github.com/nodejs/node/commit/a646a22d0f)] - **(SEMVER-MINOR)** **worker**: add support for worker name in inspector and trace_events (Debadree Chatterjee) [#46832](https://github.com/nodejs/node/pull/46832)
- \[[`bd5ef380a5`](https://github.com/nodejs/node/commit/bd5ef380a5)] - **doc**: add marco-ippolito to collaborators (Marco Ippolito) [#46816](https://github.com/nodejs/node/pull/46816)

### Commits

- \[[`e11f08e2c2`](https://github.com/nodejs/node/commit/e11f08e2c2)] - **assert**: fix exception message for assert(0) on try catch block (hidecology) [#46760](https://github.com/nodejs/node/pull/46760)
- \[[`a38de61e87`](https://github.com/nodejs/node/commit/a38de61e87)] - **assert**: remove deprecated getFunction() usage (Ruben Bridgewater) [#46661](https://github.com/nodejs/node/pull/46661)
- \[[`e07c9b82b7`](https://github.com/nodejs/node/commit/e07c9b82b7)] - **assert,util**: revert recursive breaking change (Ruben Bridgewater) [#46593](https://github.com/nodejs/node/pull/46593)
- \[[`7f85a2cb6f`](https://github.com/nodejs/node/commit/7f85a2cb6f)] - **assert,util**: improve deep equal comparison performance (Ruben Bridgewater) [#46593](https://github.com/nodejs/node/pull/46593)
- \[[`7cfd31a753`](https://github.com/nodejs/node/commit/7cfd31a753)] - **benchmark**: add a benchmark for URLSearchParams creation and toString() (Debadree Chatterjee) [#46810](https://github.com/nodejs/node/pull/46810)
- \[[`258d5f7b3c`](https://github.com/nodejs/node/commit/258d5f7b3c)] - **benchmark**: replace table in docs with description of file tree structure (Theodor Steiner) [#46991](https://github.com/nodejs/node/pull/46991)
- \[[`0617c5e81b`](https://github.com/nodejs/node/commit/0617c5e81b)] - **benchmark**: stablize encode benchmark (Joyee Cheung) [#46658](https://github.com/nodejs/node/pull/46658)
- \[[`04166fe2fa`](https://github.com/nodejs/node/commit/04166fe2fa)] - **benchmark**: split `Buffer.byteLength` benchmark (Joyee Cheung) [#46616](https://github.com/nodejs/node/pull/46616)
- \[[`760a35144f`](https://github.com/nodejs/node/commit/760a35144f)] - **benchmark**: add benchmark for EventTarget add and remove (Debadree Chatterjee) [#46779](https://github.com/nodejs/node/pull/46779)
- \[[`9890eaa23d`](https://github.com/nodejs/node/commit/9890eaa23d)] - **benchmark**: fix worker startup benchmark (Joyee Cheung) [#46680](https://github.com/nodejs/node/pull/46680)
- \[[`86b36212f6`](https://github.com/nodejs/node/commit/86b36212f6)] - **benchmark**: rework assert benchmarks for correctness (Ruben Bridgewater) [#46593](https://github.com/nodejs/node/pull/46593)
- \[[`cc74821477`](https://github.com/nodejs/node/commit/cc74821477)] - **bootstrap**: print stack trace during environment creation failure (Joyee Cheung) [#46533](https://github.com/nodejs/node/pull/46533)
- \[[`2fece54ca1`](https://github.com/nodejs/node/commit/2fece54ca1)] - **(SEMVER-MINOR)** **buffer**: add Buffer.copyBytesFrom(...) (James M Snell) [#46500](https://github.com/nodejs/node/pull/46500)
- \[[`b3e1034660`](https://github.com/nodejs/node/commit/b3e1034660)] - **buffer**: use v8 fast API calls for `Buffer.byteLength` implementation (Joyee Cheung) [#46616](https://github.com/nodejs/node/pull/46616)
- \[[`4b3b009afd`](https://github.com/nodejs/node/commit/4b3b009afd)] - **build**: fix Visual Studio installation detection for Arm64 (Radek Bartoň) [#46420](https://github.com/nodejs/node/pull/46420)
- \[[`d4899b2b75`](https://github.com/nodejs/node/commit/d4899b2b75)] - **build,test**: add proper support for IBM i (Xu Meng) [#46739](https://github.com/nodejs/node/pull/46739)
- \[[`81592ff073`](https://github.com/nodejs/node/commit/81592ff073)] - **child_process**: add trailing commas in source files (Antoine du Hamel) [#46758](https://github.com/nodejs/node/pull/46758)
- \[[`16bbbacba8`](https://github.com/nodejs/node/commit/16bbbacba8)] - **cluster**: add trailing commas in source files (Antoine du Hamel) [#46695](https://github.com/nodejs/node/pull/46695)
- \[[`2b7eb56e9b`](https://github.com/nodejs/node/commit/2b7eb56e9b)] - **debugger**: improve validations and documents for watch and unwatch (Eungyu Lee) [#46947](https://github.com/nodejs/node/pull/46947)
- \[[`afbd818669`](https://github.com/nodejs/node/commit/afbd818669)] - **debugger**: add a command to set which lines to check for context (Eungyu Lee) [#46812](https://github.com/nodejs/node/pull/46812)
- \[[`83b529ff27`](https://github.com/nodejs/node/commit/83b529ff27)] - **debugger**: add trailing commas in source files (Antoine du Hamel) [#46714](https://github.com/nodejs/node/pull/46714)
- \[[`84f5a1f942`](https://github.com/nodejs/node/commit/84f5a1f942)] - **deps**: update undici to 5.21.0 (Node.js GitHub Bot) [#47063](https://github.com/nodejs/node/pull/47063)
- \[[`fb1ac98900`](https://github.com/nodejs/node/commit/fb1ac98900)] - **deps**: update simdutf to 3.2.2 (Node.js GitHub Bot) [#46841](https://github.com/nodejs/node/pull/46841)
- \[[`7ab7f97c4e`](https://github.com/nodejs/node/commit/7ab7f97c4e)] - **deps**: update uvwasi to v0.0.16 (Michael Dawson) [#46434](https://github.com/nodejs/node/pull/46434)
- \[[`b825e2db65`](https://github.com/nodejs/node/commit/b825e2db65)] - **deps**: update ada to 1.0.4 (Node.js GitHub Bot) [#46853](https://github.com/nodejs/node/pull/46853)
- \[[`8b1afe3f45`](https://github.com/nodejs/node/commit/8b1afe3f45)] - **deps**: update corepack to 0.17.0 (Node.js GitHub Bot) [#46842](https://github.com/nodejs/node/pull/46842)
- \[[`151fb60b28`](https://github.com/nodejs/node/commit/151fb60b28)] - **deps**: update simdutf to 3.2.1 (Node.js GitHub Bot) [#46800](https://github.com/nodejs/node/pull/46800)
- \[[`92f2f1910e`](https://github.com/nodejs/node/commit/92f2f1910e)] - **deps**: upgrade npm to 9.5.1 (npm team) [#46783](https://github.com/nodejs/node/pull/46783)
- \[[`4e18e0a43a`](https://github.com/nodejs/node/commit/4e18e0a43a)] - **deps**: update ada to 1.0.3 (Node.js GitHub Bot) [#46784](https://github.com/nodejs/node/pull/46784)
- \[[`68dde38c8e`](https://github.com/nodejs/node/commit/68dde38c8e)] - **deps**: update nghttp2 to 1.52.0 (Michaël Zasso) [#46636](https://github.com/nodejs/node/pull/46636)
- \[[`d9069e7614`](https://github.com/nodejs/node/commit/d9069e7614)] - **deps**: fix libuv for android (Julian Dropmann) [#46746](https://github.com/nodejs/node/pull/46746)
- \[[`c786ed3ecc`](https://github.com/nodejs/node/commit/c786ed3ecc)] - **deps**: V8: cherry-pick 90be99fab31c (Michaël Zasso) [#46646](https://github.com/nodejs/node/pull/46646)
- \[[`fb146ee741`](https://github.com/nodejs/node/commit/fb146ee741)] - **deps**: update simdutf to 3.2.0 (Node.js GitHub Bot) [#46621](https://github.com/nodejs/node/pull/46621)
- \[[`adff278c47`](https://github.com/nodejs/node/commit/adff278c47)] - **deps,test**: update postject to 1.0.0-alpha.5 (Node.js GitHub Bot) [#46934](https://github.com/nodejs/node/pull/46934)
- \[[`247dfb7d73`](https://github.com/nodejs/node/commit/247dfb7d73)] - **dgram**: fix unhandled exception aborting a closed udp socket (Ramana Venkata) [#46770](https://github.com/nodejs/node/pull/46770)
- \[[`c310a32857`](https://github.com/nodejs/node/commit/c310a32857)] - **doc**: remove remaining SSL_OP_NETSCAPE\_\*\_BUG (Tobias Nießen) [#47066](https://github.com/nodejs/node/pull/47066)
- \[[`89f31a1c7f`](https://github.com/nodejs/node/commit/89f31a1c7f)] - **doc**: fix typo in test.md (Victor Hiairrassary) [#47053](https://github.com/nodejs/node/pull/47053)
- \[[`94882f579f`](https://github.com/nodejs/node/commit/94882f579f)] - **doc**: amend support tier qualifier (Gireesh Punathil) [#42805](https://github.com/nodejs/node/pull/42805)
- \[[`cbdaaf6197`](https://github.com/nodejs/node/commit/cbdaaf6197)] - **doc**: fix typo on esm loaders example (Ruy Adorno) [#47015](https://github.com/nodejs/node/pull/47015)
- \[[`17d3eb02f7`](https://github.com/nodejs/node/commit/17d3eb02f7)] - **doc**: add missing test runner flags to man page (Colin Ihrig) [#46982](https://github.com/nodejs/node/pull/46982)
- \[[`5f0f1c4197`](https://github.com/nodejs/node/commit/5f0f1c4197)] - **doc**: fix history information for `node:diagnostics_channel` (Thomas Hunter II) [#46984](https://github.com/nodejs/node/pull/46984)
- \[[`67e20f53cd`](https://github.com/nodejs/node/commit/67e20f53cd)] - **doc**: fix myUrl is not defined in url (Youngmin Yoo) [#46968](https://github.com/nodejs/node/pull/46968)
- \[[`f903ea502c`](https://github.com/nodejs/node/commit/f903ea502c)] - **doc**: remove useless SSL_OP\_\* options (Tobias Nießen) [#46954](https://github.com/nodejs/node/pull/46954)
- \[[`5fdd3f454f`](https://github.com/nodejs/node/commit/5fdd3f454f)] - **doc**: fix description of TLS dhparam option (Tobias Nießen) [#46949](https://github.com/nodejs/node/pull/46949)
- \[[`ba5ff15b38`](https://github.com/nodejs/node/commit/ba5ff15b38)] - **doc**: improve fs code example quality (jakecastelli) [#46948](https://github.com/nodejs/node/pull/46948)
- \[[`6f18b947be`](https://github.com/nodejs/node/commit/6f18b947be)] - **doc**: fix port of destination server is not defined in http2 (Deokjin Kim) [#46940](https://github.com/nodejs/node/pull/46940)
- \[[`1b555ae72d`](https://github.com/nodejs/node/commit/1b555ae72d)] - **doc**: use number which is bigger than 1024 as port in http2 (Deokjin Kim) [#46938](https://github.com/nodejs/node/pull/46938)
- \[[`07036cf1af`](https://github.com/nodejs/node/commit/07036cf1af)] - **doc**: add release key for Juan Arboleda (Juan José) [#46922](https://github.com/nodejs/node/pull/46922)
- \[[`553fd5b90a`](https://github.com/nodejs/node/commit/553fd5b90a)] - **doc**: fix links to SSL_CTX_set_options (Tobias Nießen) [#46953](https://github.com/nodejs/node/pull/46953)
- \[[`282bf29884`](https://github.com/nodejs/node/commit/282bf29884)] - **doc**: fix fs missing import (jakecastelli) [#46907](https://github.com/nodejs/node/pull/46907)
- \[[`f9739a85cb`](https://github.com/nodejs/node/commit/f9739a85cb)] - **doc**: add request to hold off publicising sec releases (Michael Dawson) [#46702](https://github.com/nodejs/node/pull/46702)
- \[[`92a61388de`](https://github.com/nodejs/node/commit/92a61388de)] - **doc**: fix stream iterator helpers examples (Benjamin Gruenbaum) [#46897](https://github.com/nodejs/node/pull/46897)
- \[[`8aca3cf410`](https://github.com/nodejs/node/commit/8aca3cf410)] - **doc**: add history info for `node:test` (Antoine du Hamel) [#46851](https://github.com/nodejs/node/pull/46851)
- \[[`c0b6413086`](https://github.com/nodejs/node/commit/c0b6413086)] - **doc**: sort import order (jakecastelli) [#46847](https://github.com/nodejs/node/pull/46847)
- \[[`9d2532e2bb`](https://github.com/nodejs/node/commit/9d2532e2bb)] - **doc**: use destructing import (jakecastelli) [#46847](https://github.com/nodejs/node/pull/46847)
- \[[`48cf9845fe`](https://github.com/nodejs/node/commit/48cf9845fe)] - **doc**: add steps about signing the binary in single-executable docs (Darshan Sen) [#46764](https://github.com/nodejs/node/pull/46764)
- \[[`bd5ef380a5`](https://github.com/nodejs/node/commit/bd5ef380a5)] - **doc**: add marco-ippolito to collaborators (Marco Ippolito) [#46816](https://github.com/nodejs/node/pull/46816)
- \[[`60d1a4887f`](https://github.com/nodejs/node/commit/60d1a4887f)] - **doc**: document how to use the tls.DEFAULT_CIPHERS (Andreas Martens) [#46482](https://github.com/nodejs/node/pull/46482)
- \[[`00edc50874`](https://github.com/nodejs/node/commit/00edc50874)] - **doc**: add document for profiling and heap snapshot (cola119) [#46787](https://github.com/nodejs/node/pull/46787)
- \[[`fc319d6a4f`](https://github.com/nodejs/node/commit/fc319d6a4f)] - **doc**: add test:coverage event to custom reporter examples (Richie McColl) [#46752](https://github.com/nodejs/node/pull/46752)
- \[[`1b3a25ef22`](https://github.com/nodejs/node/commit/1b3a25ef22)] - **doc**: include context on .toWeb() parameters (Debadree Chatterjee) [#46617](https://github.com/nodejs/node/pull/46617)
- \[[`88057dda3b`](https://github.com/nodejs/node/commit/88057dda3b)] - **doc**: add in security steward for recent release (Michael Dawson) [#46701](https://github.com/nodejs/node/pull/46701)
- \[[`d627164819`](https://github.com/nodejs/node/commit/d627164819)] - **doc**: clarify semver-minor notable changes approach (Beth Griggs) [#46592](https://github.com/nodejs/node/pull/46592)
- \[[`7806cae4fa`](https://github.com/nodejs/node/commit/7806cae4fa)] - **doc**: maintaining nghttp2 (Marco Ippolito) [#46539](https://github.com/nodejs/node/pull/46539)
- \[[`dd66c48a74`](https://github.com/nodejs/node/commit/dd66c48a74)] - **doc**: add emit to NodeEventTarget (Deokjin Kim) [#46356](https://github.com/nodejs/node/pull/46356)
- \[[`458671daeb`](https://github.com/nodejs/node/commit/458671daeb)] - **doc,test**: extend the list of platforms supported by single-executables (Darshan Sen) [#47026](https://github.com/nodejs/node/pull/47026)
- \[[`18f0398242`](https://github.com/nodejs/node/commit/18f0398242)] - **esm**: allow resolve to return import assertions (Geoffrey Booth) [#46153](https://github.com/nodejs/node/pull/46153)
- \[[`5eb5be8c71`](https://github.com/nodejs/node/commit/5eb5be8c71)] - **esm**: move hooks handling into separate class (Geoffrey Booth) [#45869](https://github.com/nodejs/node/pull/45869)
- \[[`9d4d916fe8`](https://github.com/nodejs/node/commit/9d4d916fe8)] - **esm**: fix import assertion warning (Antoine du Hamel) [#46971](https://github.com/nodejs/node/pull/46971)
- \[[`2c621d6e3a`](https://github.com/nodejs/node/commit/2c621d6e3a)] - **esm**: add a runtime warning when using import assertions (Antoine du Hamel) [#46901](https://github.com/nodejs/node/pull/46901)
- \[[`1a23eab614`](https://github.com/nodejs/node/commit/1a23eab614)] - **events**: add trailing commas in source files (Antoine du Hamel) [#46759](https://github.com/nodejs/node/pull/46759)
- \[[`2eb887549a`](https://github.com/nodejs/node/commit/2eb887549a)] - **(SEMVER-MINOR)** **events**: add listener argument to listenerCount (Paolo Insogna) [#46523](https://github.com/nodejs/node/pull/46523)
- \[[`4c12e6eeeb`](https://github.com/nodejs/node/commit/4c12e6eeeb)] - **fs**: add trailing commas in source files (Antoine du Hamel) [#46696](https://github.com/nodejs/node/pull/46696)
- \[[`774eb1995c`](https://github.com/nodejs/node/commit/774eb1995c)] - **http**: use listenerCount when adding noop event (Paolo Insogna) [#46769](https://github.com/nodejs/node/pull/46769)
- \[[`aac5c28091`](https://github.com/nodejs/node/commit/aac5c28091)] - **http**: correctly calculate strict content length (Robert Nagy) [#46601](https://github.com/nodejs/node/pull/46601)
- \[[`e08514e337`](https://github.com/nodejs/node/commit/e08514e337)] - **http**: fix validation of "Link" header (Steve Herzog) [#46466](https://github.com/nodejs/node/pull/46466)
- \[[`6f9cb982a1`](https://github.com/nodejs/node/commit/6f9cb982a1)] - **http**: unify header treatment (Marco Ippolito) [#46528](https://github.com/nodejs/node/pull/46528)
- \[[`05614f8cf6`](https://github.com/nodejs/node/commit/05614f8cf6)] - **lib**: enforce use of trailing commas (Antoine du Hamel) [#46881](https://github.com/nodejs/node/pull/46881)
- \[[`5c7fc9290e`](https://github.com/nodejs/node/commit/5c7fc9290e)] - **lib**: add trailing commas to all public core modules (Antoine du Hamel) [#46848](https://github.com/nodejs/node/pull/46848)
- \[[`08bf01593f`](https://github.com/nodejs/node/commit/08bf01593f)] - **lib**: fix BroadcastChannel initialization location (Shelley Vohr) [#46864](https://github.com/nodejs/node/pull/46864)
- \[[`4e1865126c`](https://github.com/nodejs/node/commit/4e1865126c)] - **lib**: rename internal module declaration as internal bindings (okmttdhr, okp) [#46663](https://github.com/nodejs/node/pull/46663)
- \[[`f914bfff7d`](https://github.com/nodejs/node/commit/f914bfff7d)] - **lib**: add trailing commas to more internal files (Antoine du Hamel) [#46811](https://github.com/nodejs/node/pull/46811)
- \[[`281f176ba4`](https://github.com/nodejs/node/commit/281f176ba4)] - **lib**: fix DOMException property descriptors after being lazy loaded (Filip Skokan) [#46799](https://github.com/nodejs/node/pull/46799)
- \[[`1c6a92b543`](https://github.com/nodejs/node/commit/1c6a92b543)] - **lib**: update punycode to 2.3.0 (Yagiz Nizipli) [#46719](https://github.com/nodejs/node/pull/46719)
- \[[`7b5c00aacd`](https://github.com/nodejs/node/commit/7b5c00aacd)] - **lib**: add trailing commas in `internal/perf` (Antoine du Hamel) [#46697](https://github.com/nodejs/node/pull/46697)
- \[[`c1651bea41`](https://github.com/nodejs/node/commit/c1651bea41)] - **(SEMVER-MINOR)** **lib**: add AsyncLocalStorage.bind() and .snapshot() (flakey5) [#46387](https://github.com/nodejs/node/pull/46387)
- \[[`345c8c343b`](https://github.com/nodejs/node/commit/345c8c343b)] - **lib,src**: fix a few typos in comments (Tobias Nießen) [#46835](https://github.com/nodejs/node/pull/46835)
- \[[`4219c1e893`](https://github.com/nodejs/node/commit/4219c1e893)] - **meta**: add single-executable labels and code owners (Joyee Cheung) [#47004](https://github.com/nodejs/node/pull/47004)
- \[[`b199acd95c`](https://github.com/nodejs/node/commit/b199acd95c)] - **meta**: remove AUTHORS file (Rich Trott) [#46845](https://github.com/nodejs/node/pull/46845)
- \[[`c7f056cbe2`](https://github.com/nodejs/node/commit/c7f056cbe2)] - **meta**: remove unnecessary onboarding step (Rich Trott) [#46793](https://github.com/nodejs/node/pull/46793)
- \[[`4e0b93222c`](https://github.com/nodejs/node/commit/4e0b93222c)] - **meta**: update CODEOWNERS of url implementations (Yagiz Nizipli) [#46775](https://github.com/nodejs/node/pull/46775)
- \[[`9d63ac2724`](https://github.com/nodejs/node/commit/9d63ac2724)] - **meta**: update AUTHORS (Node.js GitHub Bot) [#46726](https://github.com/nodejs/node/pull/46726)
- \[[`40a7b0b993`](https://github.com/nodejs/node/commit/40a7b0b993)] - **net**: fix setting of value in 'setDefaultAutoSelectFamilyAttemptTimeout' (Deokjin Kim) [#47012](https://github.com/nodejs/node/pull/47012)
- \[[`e0d098bd21`](https://github.com/nodejs/node/commit/e0d098bd21)] - **net**: rework autoSelectFamily implementation (Paolo Insogna) [#46587](https://github.com/nodejs/node/pull/46587)
- \[[`58b1f33bd7`](https://github.com/nodejs/node/commit/58b1f33bd7)] - **node-api**: add \_\_wasm32\_\_ guards on async works (Chengzhong Wu) [#46633](https://github.com/nodejs/node/pull/46633)
- \[[`e5b8597f78`](https://github.com/nodejs/node/commit/e5b8597f78)] - **os**: improve network interface performance (Ruben Bridgewater) [#46598](https://github.com/nodejs/node/pull/46598)
- \[[`d3d76c33ea`](https://github.com/nodejs/node/commit/d3d76c33ea)] - **punycode**: add pending deprecation (Antoine du Hamel) [#46719](https://github.com/nodejs/node/pull/46719)
- \[[`56dbb15e7c`](https://github.com/nodejs/node/commit/56dbb15e7c)] - **repl**: remove lastInputPreview conditional check (Duy Mac Van) [#46857](https://github.com/nodejs/node/pull/46857)
- \[[`c7d4ff3f72`](https://github.com/nodejs/node/commit/c7d4ff3f72)] - **repl**: fix .load infinite loop caused by shared use of lineEnding RegExp (Theodor Steiner) [#46742](https://github.com/nodejs/node/pull/46742)
- \[[`4f2bf8c384`](https://github.com/nodejs/node/commit/4f2bf8c384)] - **repl**: add trailing commas in source files (Antoine du Hamel) [#46757](https://github.com/nodejs/node/pull/46757)
- \[[`ed31316c2e`](https://github.com/nodejs/node/commit/ed31316c2e)] - **src**: use std::array for passing argv in node::url (Anna Henningsen) [#47035](https://github.com/nodejs/node/pull/47035)
- \[[`815d2af34d`](https://github.com/nodejs/node/commit/815d2af34d)] - **src**: remove TLSEXT_TYPE_alpn guard (Tobias Nießen) [#46956](https://github.com/nodejs/node/pull/46956)
- \[[`b051ac7220`](https://github.com/nodejs/node/commit/b051ac7220)] - **src**: remove use of SSL_OP_SINGLE_DH_USE (Tobias Nießen) [#46955](https://github.com/nodejs/node/pull/46955)
- \[[`9e65996d16`](https://github.com/nodejs/node/commit/9e65996d16)] - **src**: remove unused `v8::Uint32Array` from encoding (Yagiz Nizipli) [#47003](https://github.com/nodejs/node/pull/47003)
- \[[`6b60f38676`](https://github.com/nodejs/node/commit/6b60f38676)] - **src**: use AliasedUint32Array for encodeInto results (Joyee Cheung) [#46658](https://github.com/nodejs/node/pull/46658)
- \[[`dcba3a0673`](https://github.com/nodejs/node/commit/dcba3a0673)] - **src**: move encoding bindings to a new binding (Joyee Cheung) [#46658](https://github.com/nodejs/node/pull/46658)
- \[[`6740679965`](https://github.com/nodejs/node/commit/6740679965)] - **src**: fix negative nodeTiming milestone values (Chengzhong Wu) [#46588](https://github.com/nodejs/node/pull/46588)
- \[[`074692a6f0`](https://github.com/nodejs/node/commit/074692a6f0)] - **src**: fix missing trailing , (Cheng Zhao) [#46909](https://github.com/nodejs/node/pull/46909)
- \[[`32bd38fb05`](https://github.com/nodejs/node/commit/32bd38fb05)] - **src**: make util.h self-containted (Joyee Cheung) [#46817](https://github.com/nodejs/node/pull/46817)
- \[[`0d9c345f4d`](https://github.com/nodejs/node/commit/0d9c345f4d)] - **src**: remove shadowed variable in OptionsParser (Shelley Vohr) [#46672](https://github.com/nodejs/node/pull/46672)
- \[[`578a2c53a5`](https://github.com/nodejs/node/commit/578a2c53a5)] - **src**: do not track BaseObjects directly in Realm (Joyee Cheung) [#46470](https://github.com/nodejs/node/pull/46470)
- \[[`9fab228115`](https://github.com/nodejs/node/commit/9fab228115)] - **src**: fix cb scope bugs involved in termination (ywave620) [#45596](https://github.com/nodejs/node/pull/45596)
- \[[`c0fcad3827`](https://github.com/nodejs/node/commit/c0fcad3827)] - **src**: use an array for faster binding data lookup (Joyee Cheung) [#46620](https://github.com/nodejs/node/pull/46620)
- \[[`973287a462`](https://github.com/nodejs/node/commit/973287a462)] - **src**: per-realm binding data (Chengzhong Wu) [#46556](https://github.com/nodejs/node/pull/46556)
- \[[`ad5f42d1e9`](https://github.com/nodejs/node/commit/ad5f42d1e9)] - **src**: add SetFastMethodNoSideEffect() (Joyee Cheung) [#46619](https://github.com/nodejs/node/pull/46619)
- \[[`518b890f59`](https://github.com/nodejs/node/commit/518b890f59)] - _**Revert**_ "**src**: let http2 streams end after session close" (Rich Trott) [#46721](https://github.com/nodejs/node/pull/46721)
- \[[`19b5d0750c`](https://github.com/nodejs/node/commit/19b5d0750c)] - **src**: use string_view for report and related code (Anna Henningsen) [#46723](https://github.com/nodejs/node/pull/46723)
- \[[`36f36b99b0`](https://github.com/nodejs/node/commit/36f36b99b0)] - **(SEMVER-MINOR)** **src**: update Blob implementation to use DataQueue / File-backed Blobs (James M Snell) [#45258](https://github.com/nodejs/node/pull/45258)
- \[[`9b6270afe2`](https://github.com/nodejs/node/commit/9b6270afe2)] - **(SEMVER-MINOR)** **src**: implement DataQueue (James M Snell) [#45258](https://github.com/nodejs/node/pull/45258)
- \[[`d48ed95a66`](https://github.com/nodejs/node/commit/d48ed95a66)] - **(SEMVER-MINOR)** **src, lib**: fixup lint and format issues for DataQueue/Blob (James M Snell) [#45258](https://github.com/nodejs/node/pull/45258)
- \[[`f8866812fd`](https://github.com/nodejs/node/commit/f8866812fd)] - **stream**: enable usage of webstreams on compose() (Debadree Chatterjee) [#46675](https://github.com/nodejs/node/pull/46675)
- \[[`4ad48d9cb9`](https://github.com/nodejs/node/commit/4ad48d9cb9)] - **stream**: always delay construct callback by a nextTick (Matteo Collina) [#46818](https://github.com/nodejs/node/pull/46818)
- \[[`93e91f3dde`](https://github.com/nodejs/node/commit/93e91f3dde)] - **stream**: fix respondWithNewView() errors when view\.byteOffset != 0 (Debadree Chatterjee) [#46465](https://github.com/nodejs/node/pull/46465)
- \[[`1f386570af`](https://github.com/nodejs/node/commit/1f386570af)] - **stream**: fix pipeline callback not called on ended stream (Debadree Chatterjee) [#46600](https://github.com/nodejs/node/pull/46600)
- \[[`c972612c9d`](https://github.com/nodejs/node/commit/c972612c9d)] - **test**: fix flakyness in test-runner reporter test (Moshe Atlow) [#45930](https://github.com/nodejs/node/pull/45930)
- \[[`11509a4a2d`](https://github.com/nodejs/node/commit/11509a4a2d)] - **test**: move `test-tls-autoselectfamily-servername` to `test/internet` (Antoine du Hamel) [#47029](https://github.com/nodejs/node/pull/47029)
- \[[`9556d98054`](https://github.com/nodejs/node/commit/9556d98054)] - **test**: fallback to IPv4 if IPv6 is unavailable (Abdirahim Musse) [#47017](https://github.com/nodejs/node/pull/47017)
- \[[`5b81689efa`](https://github.com/nodejs/node/commit/5b81689efa)] - **test**: simplify test-tls-ecdh-multiple (Tobias Nießen) [#46963](https://github.com/nodejs/node/pull/46963)
- \[[`c8d528e979`](https://github.com/nodejs/node/commit/c8d528e979)] - **test**: update WPT resources, common, streams, FileAPI, broadcastchannel (Filip Skokan) [#46912](https://github.com/nodejs/node/pull/46912)
- \[[`acfd9b8879`](https://github.com/nodejs/node/commit/acfd9b8879)] - **test**: improve test coverage of lib/dns (Anderson Paiva) [#46910](https://github.com/nodejs/node/pull/46910)
- \[[`21153f164d`](https://github.com/nodejs/node/commit/21153f164d)] - **test**: simplify test-tls-ecdh-auto (Tobias Nießen) [#46911](https://github.com/nodejs/node/pull/46911)
- \[[`e5b8896186`](https://github.com/nodejs/node/commit/e5b8896186)] - **test**: move testPath from CWD to temporary directory (Livia Medeiros) [#46890](https://github.com/nodejs/node/pull/46890)
- \[[`db2ace1f94`](https://github.com/nodejs/node/commit/db2ace1f94)] - **test**: assume priv ports start at 1024 if it can't be changed (KrayzeeKev) [#46536](https://github.com/nodejs/node/pull/46536)
- \[[`0e45470fd3`](https://github.com/nodejs/node/commit/0e45470fd3)] - **test**: update web-platform tests for url (Xuguang Mei) [#46860](https://github.com/nodejs/node/pull/46860)
- \[[`6fa142d8f8`](https://github.com/nodejs/node/commit/6fa142d8f8)] - **test**: move socket from CWD to temporary directory (Livia Medeiros) [#46863](https://github.com/nodejs/node/pull/46863)
- \[[`df155b8fd5`](https://github.com/nodejs/node/commit/df155b8fd5)] - **test**: fix os-release check for Ubuntu in SEA test (Anna Henningsen) [#46838](https://github.com/nodejs/node/pull/46838)
- \[[`e585a11fd5`](https://github.com/nodejs/node/commit/e585a11fd5)] - **test**: fix test-net-connect-reset-until-connected (Vita Batrla) [#46781](https://github.com/nodejs/node/pull/46781)
- \[[`f21ed3a63f`](https://github.com/nodejs/node/commit/f21ed3a63f)] - **test**: simplify test-tls-alert (Tobias Nießen) [#46805](https://github.com/nodejs/node/pull/46805)
- \[[`e5fa7a139a`](https://github.com/nodejs/node/commit/e5fa7a139a)] - **test**: fix WPT title when no META title is present (Filip Skokan) [#46804](https://github.com/nodejs/node/pull/46804)
- \[[`bd097ca4bf`](https://github.com/nodejs/node/commit/bd097ca4bf)] - **test**: update encoding WPTs (Filip Skokan) [#46802](https://github.com/nodejs/node/pull/46802)
- \[[`3ab1aabb3f`](https://github.com/nodejs/node/commit/3ab1aabb3f)] - **test**: remove useless WPT init scripts (Filip Skokan) [#46801](https://github.com/nodejs/node/pull/46801)
- \[[`323415535b`](https://github.com/nodejs/node/commit/323415535b)] - **test**: remove useless require('../common') from WPTs (Filip Skokan) [#46796](https://github.com/nodejs/node/pull/46796)
- \[[`76a9634305`](https://github.com/nodejs/node/commit/76a9634305)] - **test**: isolate hr-time specific wpt global init (Filip Skokan) [#46795](https://github.com/nodejs/node/pull/46795)
- \[[`3daf508993`](https://github.com/nodejs/node/commit/3daf508993)] - **test**: stop faking performance idlharness (Filip Skokan) [#46794](https://github.com/nodejs/node/pull/46794)
- \[[`e52ad92b08`](https://github.com/nodejs/node/commit/e52ad92b08)] - **test**: remove unreachable return (jakecastelli) [#46807](https://github.com/nodejs/node/pull/46807)
- \[[`9c7a2e30fb`](https://github.com/nodejs/node/commit/9c7a2e30fb)] - **test**: fix test-v8-collect-gc-profile-in-worker.js (theanarkh) [#46735](https://github.com/nodejs/node/pull/46735)
- \[[`a92be13dad`](https://github.com/nodejs/node/commit/a92be13dad)] - **test**: improve control flow in test-tls-dhe (Tobias Nießen) [#46751](https://github.com/nodejs/node/pull/46751)
- \[[`4e9915e383`](https://github.com/nodejs/node/commit/4e9915e383)] - **test**: include strace openat test (Rafael Gonzaga) [#46150](https://github.com/nodejs/node/pull/46150)
- \[[`2c4f670c6b`](https://github.com/nodejs/node/commit/2c4f670c6b)] - **test**: fix IPv6 checks on IBM i (Abdirahim Musse) [#46546](https://github.com/nodejs/node/pull/46546)
- \[[`b2cfcf9cd8`](https://github.com/nodejs/node/commit/b2cfcf9cd8)] - **test**: fix default WPT titles (Filip Skokan) [#46778](https://github.com/nodejs/node/pull/46778)
- \[[`f4cdc6f20f`](https://github.com/nodejs/node/commit/f4cdc6f20f)] - **test**: remove OpenSSL 1.0.2 error message compat (Tobias Nießen) [#46709](https://github.com/nodejs/node/pull/46709)
- \[[`d5784c79bc`](https://github.com/nodejs/node/commit/d5784c79bc)] - **test**: fix flaky test-watch-mode-files_watcher (Moshe Atlow) [#46738](https://github.com/nodejs/node/pull/46738)
- \[[`abba45e120`](https://github.com/nodejs/node/commit/abba45e120)] - **test**: remove obsolete util.isDeepStrictEqual tests (Ruben Bridgewater) [#46593](https://github.com/nodejs/node/pull/46593)
- \[[`3401315e4e`](https://github.com/nodejs/node/commit/3401315e4e)] - **test**: use newish OpenSSL feature in test-tls-dhe (Tobias Nießen) [#46708](https://github.com/nodejs/node/pull/46708)
- \[[`95bbd0f7d6`](https://github.com/nodejs/node/commit/95bbd0f7d6)] - **test**: update web-platform tests for url (Yagiz Nizipli) [#46547](https://github.com/nodejs/node/pull/46547)
- \[[`13f14a5efa`](https://github.com/nodejs/node/commit/13f14a5efa)] - **test,crypto**: update WebCryptoAPI WPT (Filip Skokan) [#47010](https://github.com/nodejs/node/pull/47010)
- \[[`5e31599c26`](https://github.com/nodejs/node/commit/5e31599c26)] - **test_runner**: default to spec reporter when on TTY environment (Moshe Atlow) [#46969](https://github.com/nodejs/node/pull/46969)
- \[[`18146fc8c1`](https://github.com/nodejs/node/commit/18146fc8c1)] - **test_runner**: handle errors not bound to tests (Colin Ihrig) [#46962](https://github.com/nodejs/node/pull/46962)
- \[[`7960ccb61e`](https://github.com/nodejs/node/commit/7960ccb61e)] - **test_runner**: throw if harness is not bootstrapped (Colin Ihrig) [#46962](https://github.com/nodejs/node/pull/46962)
- \[[`b832d77500`](https://github.com/nodejs/node/commit/b832d77500)] - **test_runner**: track bootstrapping process (Colin Ihrig) [#46962](https://github.com/nodejs/node/pull/46962)
- \[[`debc0adcf0`](https://github.com/nodejs/node/commit/debc0adcf0)] - **test_runner**: avoid running twice tests in describe (Moshe Atlow) [#46888](https://github.com/nodejs/node/pull/46888)
- \[[`0923cbcfe6`](https://github.com/nodejs/node/commit/0923cbcfe6)] - **test_runner**: fix reconstruction of errors extracted from YAML (Moshe Atlow) [#46872](https://github.com/nodejs/node/pull/46872)
- \[[`ecf714e1d5`](https://github.com/nodejs/node/commit/ecf714e1d5)] - **test_runner**: reset count on watch mode (Moshe Atlow) [#46577](https://github.com/nodejs/node/pull/46577)
- \[[`6d32a16319`](https://github.com/nodejs/node/commit/6d32a16319)] - **test_runner**: bootstrap reporters before running tests (Moshe Atlow) [#46737](https://github.com/nodejs/node/pull/46737)
- \[[`ffa86f7fa9`](https://github.com/nodejs/node/commit/ffa86f7fa9)] - **test_runner**: emit test-only diagnostic warning (Richie McColl) [#46540](https://github.com/nodejs/node/pull/46540)
- \[[`3a1a7fa741`](https://github.com/nodejs/node/commit/3a1a7fa741)] - **test_runner**: flatten TAP output when running using `--test` (Moshe Atlow) [#46440](https://github.com/nodejs/node/pull/46440)
- \[[`069ff1cc63`](https://github.com/nodejs/node/commit/069ff1cc63)] - **test_runner**: remove root tracking set (Colin Ihrig) [#46961](https://github.com/nodejs/node/pull/46961)
- \[[`4b7198c3cb`](https://github.com/nodejs/node/commit/4b7198c3cb)] - **test_runner**: give the root test a harness reference (Colin Ihrig) [#46962](https://github.com/nodejs/node/pull/46962)
- \[[`762dc7cb7a`](https://github.com/nodejs/node/commit/762dc7cb7a)] - **test_runner**: align behavior of it and test (Moshe Atlow) [#46889](https://github.com/nodejs/node/pull/46889)
- \[[`aa41f27d53`](https://github.com/nodejs/node/commit/aa41f27d53)] - **test_runner**: add `describe.only` and `it.only` shorthands (Richie McColl) [#46604](https://github.com/nodejs/node/pull/46604)
- \[[`dfe529b709`](https://github.com/nodejs/node/commit/dfe529b709)] - **test_runner**: better handle async bootstrap errors (Colin Ihrig) [#46720](https://github.com/nodejs/node/pull/46720)
- \[[`320ddc0a0c`](https://github.com/nodejs/node/commit/320ddc0a0c)] - **test_runner**: centralize CLI option handling (Colin Ihrig) [#46707](https://github.com/nodejs/node/pull/46707)
- \[[`66016e2a29`](https://github.com/nodejs/node/commit/66016e2a29)] - **test_runner**: display skipped tests in spec reporter output (Richie McColl) [#46651](https://github.com/nodejs/node/pull/46651)
- \[[`25069a60c7`](https://github.com/nodejs/node/commit/25069a60c7)] - **timers**: use V8 fast API calls (Joyee Cheung) [#46579](https://github.com/nodejs/node/pull/46579)
- \[[`bb9b1c637d`](https://github.com/nodejs/node/commit/bb9b1c637d)] - **(SEMVER-MINOR)** **tls**: support automatic DHE (Tobias Nießen) [#46978](https://github.com/nodejs/node/pull/46978)
- \[[`4df008457d`](https://github.com/nodejs/node/commit/4df008457d)] - **tls**: add trailing commas in source files (Antoine du Hamel) [#46715](https://github.com/nodejs/node/pull/46715)
- \[[`36c48eab31`](https://github.com/nodejs/node/commit/36c48eab31)] - **tools**: add daily WPT Report workflow step summary (Filip Skokan) [#46763](https://github.com/nodejs/node/pull/46763)
- \[[`12a561875b`](https://github.com/nodejs/node/commit/12a561875b)] - **tools**: add undici WPTs to daily WPT Report (Filip Skokan) [#46763](https://github.com/nodejs/node/pull/46763)
- \[[`0f1ecbccca`](https://github.com/nodejs/node/commit/0f1ecbccca)] - **tools**: dont use cached node versions in daily wpt (Filip Skokan) [#47024](https://github.com/nodejs/node/pull/47024)
- \[[`2e7ba3159b`](https://github.com/nodejs/node/commit/2e7ba3159b)] - **tools**: automate cares update (Marco Ippolito) [#46993](https://github.com/nodejs/node/pull/46993)
- \[[`8723844f29`](https://github.com/nodejs/node/commit/8723844f29)] - **tools**: update lint-md-dependencies to rollup\@3.18.0 (Node.js GitHub Bot) [#46935](https://github.com/nodejs/node/pull/46935)
- \[[`f4a59b723d`](https://github.com/nodejs/node/commit/f4a59b723d)] - **tools**: add automation for updating OpenSSL dependency (Facundo Tuesca) [#45605](https://github.com/nodejs/node/pull/45605)
- \[[`ecce6475b9`](https://github.com/nodejs/node/commit/ecce6475b9)] - **tools**: refactor dep_updaters (Tony Gorez) [#46488](https://github.com/nodejs/node/pull/46488)
- \[[`132fc45d16`](https://github.com/nodejs/node/commit/132fc45d16)] - **tools**: fix daily wpt nightly version selection (Filip Skokan) [#46891](https://github.com/nodejs/node/pull/46891)
- \[[`078600c130`](https://github.com/nodejs/node/commit/078600c130)] - **tools**: update eslint to 8.35.0 (Node.js GitHub Bot) [#46854](https://github.com/nodejs/node/pull/46854)
- \[[`724f9d61a3`](https://github.com/nodejs/node/commit/724f9d61a3)] - **tools**: create llhttp update action (Marco Ippolito) [#46766](https://github.com/nodejs/node/pull/46766)
- \[[`f558797744`](https://github.com/nodejs/node/commit/f558797744)] - **tools**: fix linter message when using global `DOMException` (Antoine du Hamel) [#46822](https://github.com/nodejs/node/pull/46822)
- \[[`f4cbe4ea4b`](https://github.com/nodejs/node/commit/f4cbe4ea4b)] - **tools**: update lint-md-dependencies to rollup\@3.17.3 (Node.js GitHub Bot) [#46843](https://github.com/nodejs/node/pull/46843)
- \[[`4b91420307`](https://github.com/nodejs/node/commit/4b91420307)] - **tools**: upload daily WPT Report to both staging and production (Filip Skokan) [#46803](https://github.com/nodejs/node/pull/46803)
- \[[`2f09d3f9a1`](https://github.com/nodejs/node/commit/2f09d3f9a1)] - **tools**: update lint-md-dependencies to rollup\@3.17.2 (Node.js GitHub Bot) [#46771](https://github.com/nodejs/node/pull/46771)
- \[[`f6bd145768`](https://github.com/nodejs/node/commit/f6bd145768)] - **tools**: run format-cpp on node-api test c files (Chengzhong Wu) [#46694](https://github.com/nodejs/node/pull/46694)
- \[[`694659cecb`](https://github.com/nodejs/node/commit/694659cecb)] - **tools**: cleanup WPT refs in daily-wpt-fyi.yml (Filip Skokan) [#46740](https://github.com/nodejs/node/pull/46740)
- \[[`1756830e36`](https://github.com/nodejs/node/commit/1756830e36)] - **tools**: use actions pinned by commit hash in coverage-linux (Gabriela Gutierrez) [#46294](https://github.com/nodejs/node/pull/46294)
- \[[`25ccaa7b3a`](https://github.com/nodejs/node/commit/25ccaa7b3a)] - **tools**: fix regex strings in Python tools (Jan Osusky) [#46671](https://github.com/nodejs/node/pull/46671)
- \[[`dd400341ad`](https://github.com/nodejs/node/commit/dd400341ad)] - **tools**: fixed path (Marco Ippolito) [#46700](https://github.com/nodejs/node/pull/46700)
- \[[`a560a78962`](https://github.com/nodejs/node/commit/a560a78962)] - **tools**: update nghttp2 action (Marco Ippolito) [#46700](https://github.com/nodejs/node/pull/46700)
- \[[`2ff9b20c3c`](https://github.com/nodejs/node/commit/2ff9b20c3c)] - **tools**: update-nghttp2 preserve config.h (Marco Ippolito) [#46698](https://github.com/nodejs/node/pull/46698)
- \[[`6ff0b801f1`](https://github.com/nodejs/node/commit/6ff0b801f1)] - **tools**: update lint-md-dependencies to rollup\@3.17.1 (Node.js GitHub Bot) [#46712](https://github.com/nodejs/node/pull/46712)
- \[[`b7e027af4d`](https://github.com/nodejs/node/commit/b7e027af4d)] - **tools**: update lint-md-dependencies to rollup\@3.17.0 (Node.js GitHub Bot) [#46712](https://github.com/nodejs/node/pull/46712)
- \[[`617b5b106a`](https://github.com/nodejs/node/commit/617b5b106a)] - **tools**: update wpt.fyi used daily checkout ref (Filip Skokan) [#46730](https://github.com/nodejs/node/pull/46730)
- \[[`63a83b4451`](https://github.com/nodejs/node/commit/63a83b4451)] - **typings**: improve `primordials` typings (Antoine du Hamel) [#46970](https://github.com/nodejs/node/pull/46970)
- \[[`1fa6352853`](https://github.com/nodejs/node/commit/1fa6352853)] - **url**: offload `URLSearchParams` initialization (Yagiz Nizipli) [#46867](https://github.com/nodejs/node/pull/46867)
- \[[`e754277a44`](https://github.com/nodejs/node/commit/e754277a44)] - **url**: fix array overrun in node:url::SetArgs() (Yagiz Nizipli) [#47001](https://github.com/nodejs/node/pull/47001)
- \[[`b1747feb57`](https://github.com/nodejs/node/commit/b1747feb57)] - **url**: set `formatUrl` method as no side effect (Yagiz Nizipli) [#46884](https://github.com/nodejs/node/pull/46884)
- \[[`b8560ec8cc`](https://github.com/nodejs/node/commit/b8560ec8cc)] - **url**: remove unnecessary call to `FunctionPrototypeBind` (Antoine du Hamel) [#46870](https://github.com/nodejs/node/pull/46870)
- \[[`f8765be197`](https://github.com/nodejs/node/commit/f8765be197)] - **url**: remove unused `kFormat` from url (Yagiz Nizipli) [#46867](https://github.com/nodejs/node/pull/46867)
- \[[`b10fe5856b`](https://github.com/nodejs/node/commit/b10fe5856b)] - **url**: improve `isURLThis` detection (Yagiz Nizipli) [#46866](https://github.com/nodejs/node/pull/46866)
- \[[`1e20b05acd`](https://github.com/nodejs/node/commit/1e20b05acd)] - **(SEMVER-MINOR)** **url**: implement URLSearchParams size getter (James M Snell) [#46308](https://github.com/nodejs/node/pull/46308)
- \[[`5a3ad8763b`](https://github.com/nodejs/node/commit/5a3ad8763b)] - **url**: simplify and improve url formatting (Yagiz Nizipli) [#46736](https://github.com/nodejs/node/pull/46736)
- \[[`a52405599c`](https://github.com/nodejs/node/commit/a52405599c)] - **url**: improve performance by removing host (Yagiz Nizipli) [#46547](https://github.com/nodejs/node/pull/46547)
- \[[`9d55a5e5bb`](https://github.com/nodejs/node/commit/9d55a5e5bb)] - **url**: fix url spec compliance issues (Yagiz Nizipli) [#46547](https://github.com/nodejs/node/pull/46547)
- \[[`77b4aca2cc`](https://github.com/nodejs/node/commit/77b4aca2cc)] - **vm**: fix leak in vm.compileFunction when importModuleDynamically is used (Joyee Cheung) [#46785](https://github.com/nodejs/node/pull/46785)
- \[[`b2a80d788a`](https://github.com/nodejs/node/commit/b2a80d788a)] - **wasi**: add wasi sock_accept stub (Michael Dawson) [#46434](https://github.com/nodejs/node/pull/46434)
- \[[`60e5f45141`](https://github.com/nodejs/node/commit/60e5f45141)] - **(SEMVER-MINOR)** **wasi**: add support for version when creating WASI (Michael Dawson) [#46469](https://github.com/nodejs/node/pull/46469)
- \[[`a646a22d0f`](https://github.com/nodejs/node/commit/a646a22d0f)] - **(SEMVER-MINOR)** **worker**: add support for worker name in inspector and trace_events (Debadree Chatterjee) [#46832](https://github.com/nodejs/node/pull/46832)

Windows 32-bit Installer: https://nodejs.org/dist/v19.8.0/node-v19.8.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v19.8.0/node-v19.8.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v19.8.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v19.8.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v19.8.0/node-v19.8.0.pkg \
macOS Apple Silicon 64-bit Binary: https://nodejs.org/dist/v19.8.0/node-v19.8.0-darwin-arm64.tar.gz \
macOS Intel 64-bit Binary: https://nodejs.org/dist/v19.8.0/node-v19.8.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v19.8.0/node-v19.8.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v19.8.0/node-v19.8.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v19.8.0/node-v19.8.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v19.8.0/node-v19.8.0-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v19.8.0/node-v19.8.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v19.8.0/node-v19.8.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v19.8.0/node-v19.8.0.tar.gz \
Other release files: https://nodejs.org/dist/v19.8.0/ \
Documentation: https://nodejs.org/docs/v19.8.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

531b19242e504e985599413f59b06d2c6be8089678c03c5523272ef7dabd0d1b  node-v19.8.0-aix-ppc64.tar.gz
a4cd2534f84d4c1399ac6bc970492440bb35c91b08023703f09346d02973b148  node-v19.8.0-darwin-arm64.tar.gz
f06c0b4ce2971f12487ab28b536ff9f12aa43f799ddab2a8fffb8cc1ce5bc151  node-v19.8.0-darwin-arm64.tar.xz
ebcba3767de967593624be989aaaf143d53ed81aeb5e7d861d1abb6b0bd6db57  node-v19.8.0-darwin-x64.tar.gz
45964fa815b23d8b7780a1a77ab66dc7eead70ef3f2916fb16a9b07b06872135  node-v19.8.0-darwin-x64.tar.xz
aed1352baf95234a27d4a6e95faf77c4863ea092939c37af6974019ed36fee43  node-v19.8.0-headers.tar.gz
cfbeb53f56497ef850c414854eff9547ef216f0ecf5fa2ccdb0c7538541e46c2  node-v19.8.0-headers.tar.xz
dc71b2b84dca27613d61c6c054046c96e18dc54bce9762d5fbcb22989ddefe53  node-v19.8.0-linux-arm64.tar.gz
4c623fc16334fc9766368bad3c01c577381e1062592a9295b57efce3cdedf455  node-v19.8.0-linux-arm64.tar.xz
3cc847138e258f0fa21f698eb0f871cd65c367fea723a111d7db56b0b7c9fdb4  node-v19.8.0-linux-armv7l.tar.gz
2879d6d163b59dae67361de4e6ba81476c6a71b293267d3aac87df05d48b9578  node-v19.8.0-linux-armv7l.tar.xz
860b829a8f802c0bd6a1b2002ca5cd7d39b3d5de05e300b328591420bf66ce3a  node-v19.8.0-linux-ppc64le.tar.gz
f05a5215c13be1d014a0e5292ebc147b2aa357b96293adb63fc71bf88dc2c6db  node-v19.8.0-linux-ppc64le.tar.xz
73f3bb0b5b22ba687851b7d24af4482ef753d26e2afb77361420b4ffaf4f1209  node-v19.8.0-linux-s390x.tar.gz
afdb00b5ca342d3f49e5c327941eeea9460b02bedc895126b3cd44bb6869f325  node-v19.8.0-linux-s390x.tar.xz
a7f584b2f28eb18e1130ee590cf0062e4ee967412d1e8d029f3eb46c56598afc  node-v19.8.0-linux-x64.tar.gz
9e336589505e969912cf15a7d760d0e48085c3985c309273513a8aac0b12f241  node-v19.8.0-linux-x64.tar.xz
5dfae47e3a679accaace5eae8398502838731a3fcde299ce0dd17acce6037b21  node-v19.8.0.pkg
d2963d2ac2d1721372d5d6b11e94641915dd51ffdda51ec9067a0f633838dfbb  node-v19.8.0.tar.gz
3b7c07e2780920718c1ac7d24b67335f048dd9c3dd695691f41da80f4ea349a6  node-v19.8.0.tar.xz
e70d356d65763d99bfb6b2e6ef04816633de7b954ba8004e4a1798766eaf267b  node-v19.8.0-win-x64.7z
2309de329aba9d206b9a516279e44376f420032ed6282d7055e32fa1ba61f659  node-v19.8.0-win-x64.zip
2283ba125952421a9b7a0302f105ef28e1d16d1a883a9a1656d9fc1adf7384a8  node-v19.8.0-win-x86.7z
61981327b910df63c50a33b9270997d9fbf4f372badab62aff9d8500b8e0f6b6  node-v19.8.0-win-x86.zip
8f433955b3810602e4c91cdb7ebaa5b4e9736e03918eef31ec90a42f634dbfd0  node-v19.8.0-x64.msi
02fe6e43b60169451e90f78267e9acad08dda8975aea8850bb89bfd4537f1c37  node-v19.8.0-x86.msi
ea03b40cbf957a07de7b0cce0f47e22801754cb16ef5c9d195b6c5c58355be0a  win-x64/node.exe
327c77b75f1cc09c242aa0fddf1b589f9e7933fd59dbbc21fd1680a813a4c309  win-x64/node.lib
4490b46bf58433e40f0148a1b2f55fcb69b55fe26c86cac77d6e114a728ced05  win-x64/node_pdb.7z
a30d5adaf6566224c21cd693f111b880788bc183efa09337c382ba08feecd11a  win-x64/node_pdb.zip
794f05a789c04a95073f4f374cd3417c0eea9d0218b0a53c071949df2ce1f27a  win-x86/node.exe
b287682bcdcfd537affc557689572a4a52d84b6163a781b33522bac5a4042077  win-x86/node.lib
73f03b56c156cde9a519a460d722d6c4090b7cdf2f76c02f6fef6a161ad1f5f7  win-x86/node_pdb.7z
31ee421d1e46c6b50fb7a93aa17fe97fea6f3a28aee1818a05da0ce3c52bd711  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEj8yhP+8dDC6RAI4Jdw96mlrhVgAFAmQQxHAACgkQdw96mlrh
VgDVzg/+NZHdAPdXCrsVmkLPS1CT4HFt+YBPVvLuXoMadxFGx1F4AhnOwX5oV8s+
9zPNPt8YFCW1GFBAPbfnnuvsyi2rCnYc1WXkhRclZgf2q5hLWjguUdSElPcqCzGS
CdCkTa53wfIqr9XAvDH+5NG717cbj/dpU6XLFvjUQSLiImXg/02GyQD7Ai4CQ9EM
ln8lO+MZp76xzin2uisuzstwTRMafLZhloEfBmpJ8a/6JVPJhanu2EDNc5FO+KmL
VQDS4QM/4XAu5rcOtOsI5Z2iIQERDd4wJ5K7GdMQf+pUi6XYugnyLALh7aBZDQ/6
rZ/l/Dv0ZSSGZFT/ZT3j8n4vIheZ69DLh7OifGKbGHMmN/2VCWJ7zTCYJD9cQrcs
aQehoTr/iPXAmtFGZg5hp/CeczxKsOakRmkvgfSybhWkyPB8GbnadipDZq7OIik0
gJzh1s8l41S+DoRaKl6BPjMerHxE479/lRA4sx29rjoAJtLPn/guXyRkN2RaBbNO
eU2DrDx5/AXPwybeVtGfvovpB807ZpIYH+yKGQfAWOBuihCBp1hvIOSSOEi1V0Cv
SiwUniOAt9NigfrCD7cAB7CEkL4Vskz2BwByYnYFaDREZvTFzAZwY2VzklHy8bWk
j77JuTgXAnL0h6sJZC6VDvoDaXKViOOgsWz8gTrlYiRKxBtUifU=
=wsb9
-----END PGP SIGNATURE-----

```
