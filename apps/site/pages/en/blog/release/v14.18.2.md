---
date: '2021-11-30T16:23:30.349Z'
category: release
title: Node v14.18.2 (LTS)
layout: blog-post
author: <PERSON>
---

### Notable changes

This release contains a c-ares update to fix a regression introduced in
Node.js 14.17.5 resolving CNAME records containing underscores
[#39780](https://github.com/nodejs/node/issues/39780).

Also included are commits to allow Node.js 14 to continue to build and
pass tests on our Jenkins CI, including adding Python 3.10 to the list
of allowable Python versions for building.

### Commits

- \[[`7923c61a62`](https://github.com/nodejs/node/commit/7923c61a62)] - **build**: pin build-docs workflow to Node.js 14 (<PERSON>) [#40939](https://github.com/nodejs/node/pull/40939)
- \[[`da356128fb`](https://github.com/nodejs/node/commit/da356128fb)] - **build**: support Python 3.10.0 (<PERSON><PERSON><PERSON>) [#40296](https://github.com/nodejs/node/pull/40296)
- \[[`9c3a85d279`](https://github.com/nodejs/node/commit/9c3a85d279)] - **deps**: update c-ares to 1.18.1 (Richard Lau) [#40660](https://github.com/nodejs/node/pull/40660)
- \[[`cd7c340545`](https://github.com/nodejs/node/commit/cd7c340545)] - **deps**: V8: patch jinja2 for Python 3.10 compat (Michaël Zasso) [#40296](https://github.com/nodejs/node/pull/40296)
- \[[`6330d435f5`](https://github.com/nodejs/node/commit/6330d435f5)] - **doc**: mark Node.js 10 as End-of-Life (Richard Lau) [#38482](https://github.com/nodejs/node/pull/38482)
- \[[`8ca082ec71`](https://github.com/nodejs/node/commit/8ca082ec71)] - **doc**: fix CJS-ESM selector in Safari (Bradley Farias) [#40135](https://github.com/nodejs/node/pull/40135)
- \[[`92490d1c89`](https://github.com/nodejs/node/commit/92490d1c89)] - **doc**: add macOS arm64 experimental status (Michael Rienstra) [#40127](https://github.com/nodejs/node/pull/40127)
- \[[`8894bdd4d8`](https://github.com/nodejs/node/commit/8894bdd4d8)] - **lib**: fix regular expression to detect \`/\` and \`\\\` (Francesco Trotta) [#40325](https://github.com/nodejs/node/pull/40325)
- \[[`704989b698`](https://github.com/nodejs/node/commit/704989b698)] - **test**: deflake child-process-pipe-dataflow (Luigi Pinca) [#40838](https://github.com/nodejs/node/pull/40838)
- \[[`df401cd346`](https://github.com/nodejs/node/commit/df401cd346)] - **test**: update upload.zip to be uncorrupted (Greg Ziskind) [#37294](https://github.com/nodejs/node/pull/37294)
- \[[`aa947f7dbf`](https://github.com/nodejs/node/commit/aa947f7dbf)] - **tools**: add script to update c-ares (Richard Lau) [#40660](https://github.com/nodejs/node/pull/40660)
- \[[`6b7b2bba41`](https://github.com/nodejs/node/commit/6b7b2bba41)] - **tools**: patch jinja2 for Python 3.10 compat (Michaël Zasso) [#40296](https://github.com/nodejs/node/pull/40296)
- \[[`39583f77d8`](https://github.com/nodejs/node/commit/39583f77d8)] - **worker**: avoid potential deadlock on NearHeapLimit (Santiago Gimeno) [#38403](https://github.com/nodejs/node/pull/38403)

Windows 32-bit Installer: https://nodejs.org/dist/v14.18.2/node-v14.18.2-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v14.18.2/node-v14.18.2-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v14.18.2/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v14.18.2/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v14.18.2/node-v14.18.2.pkg \
macOS Intel 64-bit Binary: https://nodejs.org/dist/v14.18.2/node-v14.18.2-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v14.18.2/node-v14.18.2-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v14.18.2/node-v14.18.2-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v14.18.2/node-v14.18.2-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v14.18.2/node-v14.18.2-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v14.18.2/node-v14.18.2-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v14.18.2/node-v14.18.2-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v14.18.2/node-v14.18.2.tar.gz \
Other release files: https://nodejs.org/dist/v14.18.2/ \
Documentation: https://nodejs.org/docs/v14.18.2/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

841b47d77ea253929e00da489686340edec60bc27e898bd3cf69ec7f4ff9b37b  node-v14.18.2-aix-ppc64.tar.gz
fe6159a973e044e4dfd47a5de86fd34e837279bd424e61af69106c12986a8737  node-v14.18.2-darwin-x64.tar.gz
27b2fc006f2ba2264a462a5d7789cb6fcf0037d3122f1426295a62c4e38baa8c  node-v14.18.2-darwin-x64.tar.xz
cc86e63a45fef03e4f7691f9e8f77b83b848f88a740aaef686f3b3c00c79caff  node-v14.18.2-headers.tar.gz
98341099ecab7cb0f1a1ce072a7b347217abe8bc4aec5954f36bb9de336288b4  node-v14.18.2-headers.tar.xz
e78e18e01a08b2459d738fc5fec6bd79f2b3dccf85e5122cd646b3385964bc1e  node-v14.18.2-linux-arm64.tar.gz
0805d52af1e08c2bb48fb1c3f93c5c8acaa8d5332965c42c85ebec29dc656073  node-v14.18.2-linux-arm64.tar.xz
70211985c0881c36af91d39108c5f61c0ac11de3b43b89b3f7b1a9ac333d1d3e  node-v14.18.2-linux-armv7l.tar.gz
6fff8a8bf7f43133833b089d0ed7dae7496af95eee51ff12440f9219b8e4f096  node-v14.18.2-linux-armv7l.tar.xz
f93121ba525f853cec2b1ce84ac59d7aad6adf7d48986c689e9574e25248d7ec  node-v14.18.2-linux-ppc64le.tar.gz
d536f01d3a9da7a8289ab77674e8e7a2b6140f791abf1e169617e22cd6588bf8  node-v14.18.2-linux-ppc64le.tar.xz
f0419d0d6bdbb318c3268279564a66eac1caf43ce565615879702546446522fa  node-v14.18.2-linux-s390x.tar.gz
11ab72a3304b8ea1b658272431cc0e4943fa9b1b59bdef9d9eefef1cb16d8566  node-v14.18.2-linux-s390x.tar.xz
83fa18a0e3642235446b66653eb27c169224ae9c1a15a32d6c3d9ddefb154ed4  node-v14.18.2-linux-x64.tar.gz
dae683f911fe5af11f1dc9d7746d80fccdb1e7b4bef31a570f5daffadd897c42  node-v14.18.2-linux-x64.tar.xz
b487c73c46aa29a8fc0b1f4abedae60ec3aeda5eec7496cf5c92b4e4846855d9  node-v14.18.2.pkg
0b10f2fe864ed79c80e7484649d4d04c5dcdf8990c69b945ed465571dc79c6fd  node-v14.18.2.tar.gz
3e8a9ce10f8bcd3628eb6dd049f7f03c84ba9219be6f9743e2221154b9cc680b  node-v14.18.2.tar.xz
2be03c38c54552be56de6475e37f74c81f1186cb0a9a67963b55b222b3c0c1cc  node-v14.18.2-win-x64.7z
e98ffda5483d7af12fa095ffe0d195e656da6ce463e7269b74719994409e14f0  node-v14.18.2-win-x64.zip
a083d7b2d95e9304794d18d4cb40c4f5931c2769b7394737c1d82722c2079118  node-v14.18.2-win-x86.7z
667b7889dbd1d8d42e97364f67fea944bcc9cf48ca696a6942e8ef207f213516  node-v14.18.2-win-x86.zip
666aa129e6053c7e5e3b2e308526890848fbd95068135ffbbf528c0209e2b67e  node-v14.18.2-x64.msi
434e0b5260f222e5ca336139f697cc9772a64c6dcfcbb268c6ef8cc6fa2f7c51  node-v14.18.2-x86.msi
95ee8cad4f9d12816cc95a4eb487619c7d230640c4a8d24db4612031200eb1c0  win-x64/node.exe
2bb9f49d1439ed54c2d064b21e82d3cd48ff1f7f6bd76e01a1b81ed6cce275e8  win-x64/node.lib
2ebde5fc21528af6406e96734c44225bdbebf2189785a986ef738ae35e5ae917  win-x64/node_pdb.7z
6867627aece67dcb9c8b9debd00c0edef088a429d16a205c179ab03e78a114dc  win-x64/node_pdb.zip
5b19c74416da181703069aa49367c4376f225d4edeea91e6c216d868bfcf6a05  win-x86/node.exe
1d1de98ea483ce8212bd2a3c9148094e4895c5b951ddef0503bb61af82224414  win-x86/node.lib
b9c7145db326e422c0dfe3a27894d4b77afe5abe46ad04b27974171d473f4a95  win-x86/node_pdb.7z
453d8646e9af668acd3bdfa38042d61c90a7e9efed2ed3c7caca4cdefe55ef7c  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEyC+jrhy+3Gvka5NgxDzsRcF6uTwFAmGmT48ACgkQxDzsRcF6
uTw6tQ/+NgoT7kRVqQRNzzAOmHnck7My5K+9qZe97JwPORE3M+ZJrVGzPQUFiXPl
/8hMXjIP9zmwyWnkXgwwEndz+GF5qABtOKQD9nYzKqSxAeLMAFq9wHih0ewcqDa3
QUv/xYBAQXYqTct5gXP9MfKBcsy3UTWtzUfvkwZWuuk1mbhRkQOoHUKnvpDxwu+q
4RMR3MfYalPbGgECx/SHjE5MqeTgNwkjUD/IgSNqFRfm7lWoNq8uwQgdYAwEpbS8
y/bVZkUlozPXSs7MeKfTgGQ3j6kh4BsvDoJaotgDYWKpuv7JyRSJnIPuk1/06Ptg
AnepKONLETGt9Kk4LIJNlCHSMSWiGHIK/8F1Vek7OwbzaoNMorBFbwvZg0xtPYEx
CEDKXZEfpvuHYQ/Yyn9mAkZApaJyAFdO0lNIGfT7RvN0qHKkBktbNh6TWRX8eF4T
94Xj2uCycOIcxXRCPT8v0yIdRScv3ApIh+TmmlrWjcY3LXbigNMpsKKFPdX7Jh/+
NJRlaLHn1jm0HZkueDubSU2cs4QlNeSwpl5mEXDCwQfmQ7vq+DntWRoE7k2CFlX8
Ln2Y06jsgs3ylrBgFT7ltGHOJ1qr3WTqJFPt0msBXWBZ0iJBp4ETE4lRXkGHKc/j
8+6vqp+Fh8exx7mhg+jB5tpR3NjfI+m7MMtvGLLyt+ae/TXBGhA=
=tGJA
-----END PGP SIGNATURE-----

```
