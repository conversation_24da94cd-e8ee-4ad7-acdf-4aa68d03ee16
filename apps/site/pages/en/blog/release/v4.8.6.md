---
date: '2017-11-07T17:26:46.112Z'
category: release
title: Node v4.8.6 (Maintenance)
layout: blog-post
author: <PERSON><PERSON>
---

### Notable Changes

- **crypto**:
  - update root certificates (<PERSON>) [#13279](https://github.com/nodejs/node/pull/13279)
  - update root certificates (<PERSON>) [#12402](https://github.com/nodejs/node/pull/12402)
- **deps**:
  - add support for more modern versions of INTL (<PERSON>) [#13040](https://github.com/nodejs/node/pull/13040)
  - upgrade openssl sources to 1.0.2m (<PERSON><PERSON><PERSON>) [#16691](https://github.com/nodejs/node/pull/16691)
  - upgrade openssl sources to 1.0.2l (<PERSON>) [#13233](https://github.com/nodejs/node/pull/13233)

### Commits

- [[`e064ae62e4`](https://github.com/nodejs/node/commit/e064ae62e4)] - **build**: fix make test-v8 (<PERSON>) [#15562](https://github.com/nodej<PERSON>/node/pull/15562)
- [[`a7f7a87a1b`](https://github.com/nodejs/node/commit/a7f7a87a1b)] - **build**: run test-hash-seed at the end of test-v8 (Michaël Zasso) [#14219](https://github.com/nodejs/node/pull/14219)
- [[`05e8b1b7d9`](https://github.com/nodejs/node/commit/05e8b1b7d9)] - **build**: codesign tarball binary on macOS (Evan Lucas) [#14179](https://github.com/nodejs/node/pull/14179)
- [[`e2b6fdf93e`](https://github.com/nodejs/node/commit/e2b6fdf93e)] - **build**: avoid /docs/api and /docs/doc/api upload (Rod Vagg) [#12957](https://github.com/nodejs/node/pull/12957)
- [[`59d35c0775`](https://github.com/nodejs/node/commit/59d35c0775)] - **build,tools**: do not force codesign prefix (Evan Lucas) [#14179](https://github.com/nodejs/node/pull/14179)
- [[`210fa72e9e`](https://github.com/nodejs/node/commit/210fa72e9e)] - **crypto**: update root certificates (Ben Noordhuis) [#13279](https://github.com/nodejs/node/pull/13279)
- [[`752b46a259`](https://github.com/nodejs/node/commit/752b46a259)] - **crypto**: update root certificates (Ben Noordhuis) [#12402](https://github.com/nodejs/node/pull/12402)
- [[`3640ba4acb`](https://github.com/nodejs/node/commit/3640ba4acb)] - **crypto**: clear err stack after ECDH::BufferToPoint (Ryan Kelly) [#13275](https://github.com/nodejs/node/pull/13275)
- [[`545235fc4b`](https://github.com/nodejs/node/commit/545235fc4b)] - **deps**: add missing #include "unicode/normlzr.h" (Bruno Pagani) [#13040](https://github.com/nodejs/node/pull/13040)
- [[`ea09a1c3e6`](https://github.com/nodejs/node/commit/ea09a1c3e6)] - **deps**: update openssl asm and asm_obsolete files (Shigeki Ohtsu) [#16691](https://github.com/nodejs/node/pull/16691)
- [[`68661a95b5`](https://github.com/nodejs/node/commit/68661a95b5)] - **deps**: add -no_rand_screen to openssl s_client (Shigeki Ohtsu) [nodejs/io.js#1836](https://github.com/nodejs/io.js/pull/1836)
- [[`bdcb2525fb`](https://github.com/nodejs/node/commit/bdcb2525fb)] - **deps**: fix asm build error of openssl in x86_win32 (Shigeki Ohtsu) [iojs/io.js#1389](https://github.com/iojs/io.js/pull/1389)
- [[`3f93ffee89`](https://github.com/nodejs/node/commit/3f93ffee89)] - **deps**: fix openssl assembly error on ia32 win32 (Fedor Indutny) [iojs/io.js#1389](https://github.com/iojs/io.js/pull/1389)
- [[`16fbd9da0d`](https://github.com/nodejs/node/commit/16fbd9da0d)] - **deps**: copy all openssl header files to include dir (Shigeki Ohtsu) [#16691](https://github.com/nodejs/node/pull/16691)
- [[`55e15ec820`](https://github.com/nodejs/node/commit/55e15ec820)] - **deps**: upgrade openssl sources to 1.0.2m (Shigeki Ohtsu) [#16691](https://github.com/nodejs/node/pull/16691)
- [[`9c3e246ffe`](https://github.com/nodejs/node/commit/9c3e246ffe)] - **deps**: backport 4e18190 from V8 upstream (jshin) [#15562](https://github.com/nodejs/node/pull/15562)
- [[`43d1ac3a62`](https://github.com/nodejs/node/commit/43d1ac3a62)] - **deps**: backport bff3074 from V8 upstream (Myles Borins) [#15562](https://github.com/nodejs/node/pull/15562)
- [[`b259fd3bd5`](https://github.com/nodejs/node/commit/b259fd3bd5)] - **deps**: cherry pick d7f813b4 from V8 upstream (akos.palfi) [#15562](https://github.com/nodejs/node/pull/15562)
- [[`85800c4ba4`](https://github.com/nodejs/node/commit/85800c4ba4)] - **deps**: backport e28183b5 from upstream V8 (karl) [#15562](https://github.com/nodejs/node/pull/15562)
- [[`06eb181916`](https://github.com/nodejs/node/commit/06eb181916)] - **deps**: update openssl asm and asm_obsolete files (Daniel Bevenius) [#13233](https://github.com/nodejs/node/pull/13233)
- [[`c0fe1fccc3`](https://github.com/nodejs/node/commit/c0fe1fccc3)] - **deps**: update openssl config files (Daniel Bevenius) [#13233](https://github.com/nodejs/node/pull/13233)
- [[`523eb60424`](https://github.com/nodejs/node/commit/523eb60424)] - **deps**: add -no_rand_screen to openssl s_client (Shigeki Ohtsu) [nodejs/io.js#1836](https://github.com/nodejs/io.js/pull/1836)
- [[`0aacd5a8cd`](https://github.com/nodejs/node/commit/0aacd5a8cd)] - **deps**: fix asm build error of openssl in x86_win32 (Shigeki Ohtsu) [iojs/io.js#1389](https://github.com/iojs/io.js/pull/1389)
- [[`80c48c0720`](https://github.com/nodejs/node/commit/80c48c0720)] - **deps**: fix openssl assembly error on ia32 win32 (Fedor Indutny) [iojs/io.js#1389](https://github.com/iojs/io.js/pull/1389)
- [[`bbd92b4676`](https://github.com/nodejs/node/commit/bbd92b4676)] - **deps**: copy all openssl header files to include dir (Daniel Bevenius) [#13233](https://github.com/nodejs/node/pull/13233)
- [[`8507f0fb5d`](https://github.com/nodejs/node/commit/8507f0fb5d)] - **deps**: upgrade openssl sources to 1.0.2l (Daniel Bevenius) [#13233](https://github.com/nodejs/node/pull/13233)
- [[`9bfada8f0c`](https://github.com/nodejs/node/commit/9bfada8f0c)] - **deps**: add example of comparing OpenSSL changes (Daniel Bevenius) [#13234](https://github.com/nodejs/node/pull/13234)
- [[`71f9cdf241`](https://github.com/nodejs/node/commit/71f9cdf241)] - **deps**: cherry-pick 09db540,686558d from V8 upstream (Jesse Rosenberger) [#14829](https://github.com/nodejs/node/pull/14829)
- [[`751f1ac08e`](https://github.com/nodejs/node/commit/751f1ac08e)] - **_Revert_** "**deps**: backport e093a04, 09db540 from upstream V8" (Jesse Rosenberger) [#14829](https://github.com/nodejs/node/pull/14829)
- [[`ed6298c7de`](https://github.com/nodejs/node/commit/ed6298c7de)] - **deps**: cherry-pick 18ea996 from c-ares upstream (Anna Henningsen) [#13883](https://github.com/nodejs/node/pull/13883)
- [[`639180adfa`](https://github.com/nodejs/node/commit/639180adfa)] - **deps**: update openssl asm and asm_obsolete files (Shigeki Ohtsu) [#12913](https://github.com/nodejs/node/pull/12913)
- [[`9ba73e1797`](https://github.com/nodejs/node/commit/9ba73e1797)] - **deps**: cherry-pick 4ae5993 from upstream OpenSSL (Shigeki Ohtsu) [#12913](https://github.com/nodejs/node/pull/12913)
- [[`f8e282e51c`](https://github.com/nodejs/node/commit/f8e282e51c)] - **doc**: fix typo in zlib.md (Luigi Pinca) [#16480](https://github.com/nodejs/node/pull/16480)
- [[`532a2941cb`](https://github.com/nodejs/node/commit/532a2941cb)] - **doc**: add missing make command to UPGRADING.md (Daniel Bevenius) [#13233](https://github.com/nodejs/node/pull/13233)
- [[`1db33296cb`](https://github.com/nodejs/node/commit/1db33296cb)] - **doc**: add entry for subprocess.killed property (Rich Trott) [#14578](https://github.com/nodejs/node/pull/14578)
- [[`0fa09dfd77`](https://github.com/nodejs/node/commit/0fa09dfd77)] - **doc**: change `child` to `subprocess` (Rich Trott) [#14578](https://github.com/nodejs/node/pull/14578)
- [[`43bbfafaef`](https://github.com/nodejs/node/commit/43bbfafaef)] - **docs**: Fix broken links in crypto.md (Zuzana Svetlikova) [#15182](https://github.com/nodejs/node/pull/15182)
- [[`1bde7f5cef`](https://github.com/nodejs/node/commit/1bde7f5cef)] - **openssl**: fix keypress requirement in apps on win32 (Shigeki Ohtsu) [iojs/io.js#1389](https://github.com/iojs/io.js/pull/1389)
- [[`e69f47b686`](https://github.com/nodejs/node/commit/e69f47b686)] - **openssl**: fix keypress requirement in apps on win32 (Shigeki Ohtsu) [iojs/io.js#1389](https://github.com/iojs/io.js/pull/1389)
- [[`cb92f93cd5`](https://github.com/nodejs/node/commit/cb92f93cd5)] - **test**: remove internal headers from addons (Gibson Fahnestock) [#7947](https://github.com/nodejs/node/pull/7947)
- [[`5d9164c315`](https://github.com/nodejs/node/commit/5d9164c315)] - **test**: move test-cluster-debug-port to sequential (Oleksandr Kushchak) [#16292](https://github.com/nodejs/node/pull/16292)
- [[`07c912e849`](https://github.com/nodejs/node/commit/07c912e849)] - **tools**: update certdata.txt (Ben Noordhuis) [#13279](https://github.com/nodejs/node/pull/13279)
- [[`c40bffcb88`](https://github.com/nodejs/node/commit/c40bffcb88)] - **tools**: update certdata.txt (Ben Noordhuis) [#12402](https://github.com/nodejs/node/pull/12402)
- [[`161162713f`](https://github.com/nodejs/node/commit/161162713f)] - **tools**: be explicit about including key-id (Myles Borins) [#13309](https://github.com/nodejs/node/pull/13309)
- [[`0c820c092b`](https://github.com/nodejs/node/commit/0c820c092b)] - **v8**: fix stack overflow in recursive method (Ben Noordhuis) [#12460](https://github.com/nodejs/node/pull/12460)
- [[`a1f992975f`](https://github.com/nodejs/node/commit/a1f992975f)] - **zlib**: fix crash when initializing failed (Anna Henningsen) [#14666](https://github.com/nodejs/node/pull/14666)
- [[`31bf595b94`](https://github.com/nodejs/node/commit/31bf595b94)] - **zlib**: fix node crashing on invalid options (Alexey Orlenko) [#13098](https://github.com/nodejs/node/pull/13098)

Windows 32-bit Installer: https://nodejs.org/dist/v4.8.6/node-v4.8.6-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v4.8.6/node-v4.8.6-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v4.8.6/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v4.8.6/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v4.8.6/node-v4.8.6.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v4.8.6/node-v4.8.6-darwin-x64.tar.gz \
Linux 32-bit Binary: https://nodejs.org/dist/v4.8.6/node-v4.8.6-linux-x86.tar.xz \
Linux 64-bit Binary: https://nodejs.org/dist/v4.8.6/node-v4.8.6-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v4.8.6/node-v4.8.6-linux-ppc64le.tar.xz \
Linux PPC BE 64-bit Binary: https://nodejs.org/dist/v4.8.6/node-v4.8.6-linux-ppc64.tar.xz \
SmartOS 32-bit Binary: https://nodejs.org/dist/v4.8.6/node-v4.8.6-sunos-x86.tar.xz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v4.8.6/node-v4.8.6-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v4.8.6/node-v4.8.6-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v4.8.6/node-v4.8.6-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v4.8.6/node-v4.8.6-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v4.8.6/node-v4.8.6.tar.gz \
Other release files: https://nodejs.org/dist/v4.8.6/ \
Documentation: https://nodejs.org/docs/v4.8.6/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

ee7ce30a1b7069efea27bbef1829a3a665ae6650cc4a79eb556707c6779ba540  node-v4.8.6-darwin-x64.tar.gz
724b15bfbc0d8bd2e0ab9db5fabcf65c5214dd8a4edf8f482502382a76dd3bcc  node-v4.8.6-darwin-x64.tar.xz
75a76a42d01aec1e6b70078adae797078b46c4fa5bd049fc2814ec028937d356  node-v4.8.6-headers.tar.gz
ef08ff8c87db784c2afd994e2341be3975685c55cd9006c80403e60fea94240b  node-v4.8.6-headers.tar.xz
b132ab051b1a48db3e9385b086c77fc4438f888a236b7e9cbe998171196592cb  node-v4.8.6-linux-arm64.tar.gz
03fad7fe117ec2f81c98af89e99b93b9ce3505bf57a51b630f3648bf0f868c0a  node-v4.8.6-linux-arm64.tar.xz
68f599d287f11ca528acc3f1371305301ba34b8fbd915e65fd71a5144b169c87  node-v4.8.6-linux-armv6l.tar.gz
16081118979537dcdd4fd210fa916a97e481dde5dbc269156faa8825f8830cbd  node-v4.8.6-linux-armv6l.tar.xz
d4cfb8286de99911548a29d792a108dbbc43df2d747099d311ff882f25c3c608  node-v4.8.6-linux-armv7l.tar.gz
2bf41f0067587af422773984c352295870ceee94965eaea3f7926cf62c9db5d0  node-v4.8.6-linux-armv7l.tar.xz
7339f4e8e3fdc34c6992f3e92ffd5a41e331bc28f8d07fcf83fe8211c766d772  node-v4.8.6-linux-ppc64le.tar.gz
0514f7a0722707ca9a4a4a98f2c8be779f71115aecd75bb06be97e5db692a90f  node-v4.8.6-linux-ppc64le.tar.xz
58c0788793f580f3f4edc79df16046b3ee815bc42fcaf4dde5a1a164ca248c3c  node-v4.8.6-linux-ppc64.tar.gz
7a2670b2aed631096939c0499933a637afdb5f2a84e4093ec70bcfade437378f  node-v4.8.6-linux-ppc64.tar.xz
3d4c29e5dceafa68a7a326079c160cf58e5443b4be199ba9595f8e8fa6f58fdb  node-v4.8.6-linux-x64.tar.gz
ea36423fb4142ab05035550dee32b1dfcb78f711087781520be82c256857fdf7  node-v4.8.6-linux-x64.tar.xz
208c747602f32313c3eeecab7e93491868c0e7041d309d9fb48f69ac52e7da7c  node-v4.8.6-linux-x86.tar.gz
b0c9a933abda460a4fb7145b8a649cd52ce4f20563598a9e0c94a9e9c6d4603b  node-v4.8.6-linux-x86.tar.xz
599eff42f17c00d4629d6030def6f0510e1d9b1630c84a3bc822bbc7afdf2994  node-v4.8.6.pkg
3fb9097298658ba4364588338f4ddffa138f28b2b800e13fdd1f01ceb3b52b5e  node-v4.8.6-sunos-x64.tar.gz
922cd1e5705701816ca6c3116f935d8b7f37c9adba3064865fa990021a2f1b1d  node-v4.8.6-sunos-x64.tar.xz
1466a6dee7590513bc937362004bc5a7c3855a8dfa0fb386bbd08ab1f6ec33f3  node-v4.8.6-sunos-x86.tar.gz
528e262f1521869be369a344bb47d847837a22d70202697ccd52af68ce9fa9f4  node-v4.8.6-sunos-x86.tar.xz
f37a92ca7254ecac80f905a5ba5c5ccf8d886c7dab80d84cf6e28af24b1ff680  node-v4.8.6.tar.gz
a5b1e94f9879035387a7005a4ec9cd45a9cc2ac43ad548cd7a3b9fd4941f1774  node-v4.8.6.tar.xz
129831bbc974c32cdcbab10aa9309631c848f467cb4a52661e3b5ebe7928b859  node-v4.8.6-win-x64.7z
aec6721b53b36a7903c86e0d9e4abb2474fd919ebebeb28f94db35e545b60a06  node-v4.8.6-win-x64.zip
ba6ecf43e9027a7c2349808e6d9ab7e2cffc2a41817192d9f601fcc164d739b6  node-v4.8.6-win-x86.7z
ef5704dfaa092a94c7a7779dd3d787edaef10da85108e99d1242fb60e098311b  node-v4.8.6-win-x86.zip
d06abf23b00eaeaf9a7e1ddb1089bd10f35907f26c69c4453bd3539900b74455  node-v4.8.6-x64.msi
4ec045de8c4a6dd6dd5fd94241caf57c2a1ae35d6f2de21ad4e3a47ab380f1a4  node-v4.8.6-x86.msi
37454b1d5a3ada17a61aaded277e12513ca20054c0da97f89ceca2d72fbd098c  win-x64/node.exe
1418dfb4cc371ed3a938e61392654ff0c9ff429f4e16460afc5ae69bbcd7235a  win-x64/node.lib
bd2e96e26d8fda0268ac705ec0c2dd2592fcf4045d974996fcc7bc7e18689b27  win-x64/node_pdb.7z
7f499a5c60b503982c75749630fb193764a6b21dc82faa77f4c287d02d9892e8  win-x64/node_pdb.zip
03001a0301cbb0886a87a013595f1a03499ad31ee13d0a3c9831fe15c24a9df8  win-x86/node.exe
711ecc17c080e314e432279c2978ece0216d9bf58ceb4f78c0d7f20627f98a89  win-x86/node.lib
eae9859025238c3a6eab0b8347227f1e5df9e170dd1948f3a485720e0c2871bb  win-x86/node_pdb.7z
d2538a0ca005fef1635f08aed63b70c41d3cda88067503cde51f11b3d64e6edd  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQEzBAEBCAAdFiEEDv/hvO/ZyE49CYFSkzsB9AtcqUYFAloB6rEACgkQkzsB9Atc
qUY+PQf+K1+SvwaMwOSrHLPwIG9RkbuIT0RREJ5A84WH7rtaAraOmLMQGGeizB2f
We/RTpNSJLt34EX0a4W+Y4zSa4+C8yRno/OgUjRJNoVCkEZQufGuvwpKtqwwmamS
I/dLRre+ZV6uXy8WONiAbt+2FXwgFelaECjlhYFwNyZo/z2YR/tsaxA0ZSl+sKdA
x+iurPjh/4KhhB7QayyXPp/7afGsClaF6q684ZH1w4f5uBVdJ1SlZeap8PWsJBXJ
7GAnZ5YASaDXmKcOv/A038A3CgZvdD389sZLOI6eyIizFy9/Jc95kI/5Z5nftu+9
8kz6W3XnpnnMHb3Pj9DX3GJyCSCuTg==
=s3mc
-----END PGP SIGNATURE-----

```
