---
date: '2017-05-02T18:11:07.199Z'
category: release
title: Node v6.10.3 (LTS)
layout: blog-post
author: <PERSON><PERSON>
---

### Notable Changes

- **module**:
  - The [module loading global fallback](https://nodejs.org/dist/latest-v4.x/docs/api/modules.html#modules_loading_from_the_global_folders) to the Node executable's directory now works correctly on Windows. (<PERSON>) [#9283](https://github.com/nodejs/node/pull/9283)
- **src**:
  - fix base64 decoding in rare edgecase (<PERSON>) [#11995](https://github.com/nodejs/node/pull/11995)
- **tls**:
  - fix rare segmentation faults when using TLS
    - (<PERSON>) [#11947](https://github.com/nodejs/node/pull/11947)
    - (<PERSON>) [#11898](https://github.com/nodejs/node/pull/11898)
    - (jBarz) [#11776](https://github.com/nodejs/node/pull/11776)

### Commits

- [[`858bbaa4aa`](https://github.com/nodejs/node/commit/858bbaa4aa)] - Partial revert "tls: keep track of stream that is closed" (<PERSON> Norris) [#11947](https://github.com/nodejs/node/pull/11947)
- [[`12c0ce749f`](https://github.com/nodejs/node/commit/12c0ce749f)] - **assert, tools**: enforce strict (not)equal in eslint (Gibson Fahnestock) [#10698](https://github.com/nodejs/node/pull/10698)
- [[`abbf6e38f1`](https://github.com/nodejs/node/commit/abbf6e38f1)] - **benchmark**: fix fs\bench-realpathSync.js (Vse Mozhet Byt) [#11904](https://github.com/nodejs/node/pull/11904)
- [[`53d7a89497`](https://github.com/nodejs/node/commit/53d7a89497)] - **buffer**: remove unneeded eslint-disable comment (Rich Trott) [#11906](https://github.com/nodejs/node/pull/11906)
- [[`5d74c9e749`](https://github.com/nodejs/node/commit/5d74c9e749)] - **buffer**: refactor Buffer.prototype.inspect() (Rich Trott) [#11600](https://github.com/nodejs/node/pull/11600)
- [[`e7e83f6f10`](https://github.com/nodejs/node/commit/e7e83f6f10)] - **build**: use $(RM) in Makefile for consistency (Gibson Fahnestock) [#12157](https://github.com/nodejs/node/pull/12157)
- [[`986ef6fffa`](https://github.com/nodejs/node/commit/986ef6fffa)] - **build**: add checks for openssl configure options (Daniel Bevenius) [#12175](https://github.com/nodejs/node/pull/12175)
- [[`c2c467e242`](https://github.com/nodejs/node/commit/c2c467e242)] - **build**: make configure print statements consistent (Daniel Bevenius) [#12176](https://github.com/nodejs/node/pull/12176)
- [[`2c2a6649c1`](https://github.com/nodejs/node/commit/2c2a6649c1)] - **build**: add node_use_openssl check to install.py (Daniel Bevenius) [#11766](https://github.com/nodejs/node/pull/11766)
- [[`a899b0b92b`](https://github.com/nodejs/node/commit/a899b0b92b)] - **build**: fix llvm version detection in freebsd-10 (Shigeki Ohtsu) [#11668](https://github.com/nodejs/node/pull/11668)
- [[`ba23506419`](https://github.com/nodejs/node/commit/ba23506419)] - **build**: --without-ssl implies --without-inspector (Ben Noordhuis) [#12200](https://github.com/nodejs/node/pull/12200)
- [[`cd78a2bd07`](https://github.com/nodejs/node/commit/cd78a2bd07)] - **deps**: backport 75f2d65f00 from upstream V8 (Yang Guo) [#12535](https://github.com/nodejs/node/pull/12535)
- [[`62e047e040`](https://github.com/nodejs/node/commit/62e047e040)] - **deps**: backport ec1ffe3 from upstream V8 (Daniel Bevenius) [#12061](https://github.com/nodejs/node/pull/12061)
- [[`8cdddcdb68`](https://github.com/nodejs/node/commit/8cdddcdb68)] - **deps**: cherry-pick ca0f9573 from V8 upstream (Ali Ijaz Sheikh) [#11940](https://github.com/nodejs/node/pull/11940)
- [[`d15188f6e2`](https://github.com/nodejs/node/commit/d15188f6e2)] - **doc**: modernize and fix code examples in modules.md (Vse Mozhet Byt) [#12224](https://github.com/nodejs/node/pull/12224)
- [[`03f9388eb7`](https://github.com/nodejs/node/commit/03f9388eb7)] - **doc**: clarify out-of-bounds behavior of buf\[index\] (Nikolai Vavilov) [#11286](https://github.com/nodejs/node/pull/11286)
- [[`eddfd5230e`](https://github.com/nodejs/node/commit/eddfd5230e)] - **doc**: add refack to collaborators (Refael Ackermann) [#12277](https://github.com/nodejs/node/pull/12277)
- [[`22af92ac2e`](https://github.com/nodejs/node/commit/22af92ac2e)] - **doc**: add richardlau to collaborators (Richard Lau) [#12020](https://github.com/nodejs/node/pull/12020)
- [[`8d1a474ec2`](https://github.com/nodejs/node/commit/8d1a474ec2)] - **doc**: fix confusing example in process.md (Vse Mozhet Byt) [#12282](https://github.com/nodejs/node/pull/12282)
- [[`88f402c4c1`](https://github.com/nodejs/node/commit/88f402c4c1)] - **doc**: update information on test/known_issues (Jan Krems) [#12262](https://github.com/nodejs/node/pull/12262)
- [[`34f9dfde1f`](https://github.com/nodejs/node/commit/34f9dfde1f)] - **doc**: fix confusing reference in net.md (Vse Mozhet Byt) [#12247](https://github.com/nodejs/node/pull/12247)
- [[`7e67176d79`](https://github.com/nodejs/node/commit/7e67176d79)] - **doc**: document the performance team (Gibson Fahnestock) [#12213](https://github.com/nodejs/node/pull/12213)
- [[`3b38e7109f`](https://github.com/nodejs/node/commit/3b38e7109f)] - **doc**: add aqrln to collaborators (Alexey Orlenko) [#12273](https://github.com/nodejs/node/pull/12273)
- [[`811ccdf06a`](https://github.com/nodejs/node/commit/811ccdf06a)] - **doc**: modernize and fix code examples in https.md (Vse Mozhet Byt) [#12171](https://github.com/nodejs/node/pull/12171)
- [[`c0d9b1c02b`](https://github.com/nodejs/node/commit/c0d9b1c02b)] - **doc**: fix string interpolation in Stream 'finish' (Vinay Hiremath) [#12221](https://github.com/nodejs/node/pull/12221)
- [[`f4dd304c32`](https://github.com/nodejs/node/commit/f4dd304c32)] - **doc**: add table of contents to README.md (Jason Marsh) [#11635](https://github.com/nodejs/node/pull/11635)
- [[`d007427c63`](https://github.com/nodejs/node/commit/d007427c63)] - **doc**: add logo to README (Roman Reiss) [#12148](https://github.com/nodejs/node/pull/12148)
- [[`9dda771c1f`](https://github.com/nodejs/node/commit/9dda771c1f)] - **doc**: update and modernize examples in fs.ms (Vse Mozhet Byt) [#12035](https://github.com/nodejs/node/pull/12035)
- [[`81f561bed8`](https://github.com/nodejs/node/commit/81f561bed8)] - **doc**: stdout/err/in are all Duplex streams (Sebastian Van Sande) [#11194](https://github.com/nodejs/node/pull/11194)
- [[`97035136d6`](https://github.com/nodejs/node/commit/97035136d6)] - **doc**: fix process.stdout fd number (Fumiya KARASAWA) [#12055](https://github.com/nodejs/node/pull/12055)
- [[`aab9526d69`](https://github.com/nodejs/node/commit/aab9526d69)] - **doc**: update collaborator email address (Rich Trott) [#11996](https://github.com/nodejs/node/pull/11996)
- [[`6885dccd3d`](https://github.com/nodejs/node/commit/6885dccd3d)] - **doc**: correct info in child_process.md (Vse Mozhet Byt) [#11949](https://github.com/nodejs/node/pull/11949)
- [[`fb0a2e426d`](https://github.com/nodejs/node/commit/fb0a2e426d)] - **doc**: remove superfluous sample assert code (Rich Trott) [#11933](https://github.com/nodejs/node/pull/11933)
- [[`3ad0a1430d`](https://github.com/nodejs/node/commit/3ad0a1430d)] - **doc**: require uses fs root for '/' prefix (Bradley Farias) [#11897](https://github.com/nodejs/node/pull/11897)
- [[`d149844b49`](https://github.com/nodejs/node/commit/d149844b49)] - **doc**: fix gitter badge in README (Roman Reiss) [#11944](https://github.com/nodejs/node/pull/11944)
- [[`4a97bc7a39`](https://github.com/nodejs/node/commit/4a97bc7a39)] - **doc**: add missing word in stream.md (Jyotman Singh) [#11914](https://github.com/nodejs/node/pull/11914)
- [[`f53c48e173`](https://github.com/nodejs/node/commit/f53c48e173)] - **doc**: add vsemozhetbyt to collaborators (Vse Mozhet Byt) [#11932](https://github.com/nodejs/node/pull/11932)
- [[`c10a4a2a7a`](https://github.com/nodejs/node/commit/c10a4a2a7a)] - **doc**: add note that vm module is not a security mechanism (Ruslan Bekenev) [#11557](https://github.com/nodejs/node/pull/11557)
- [[`b8e3a5f109`](https://github.com/nodejs/node/commit/b8e3a5f109)] - **doc**: fix a typo in api/process.md (Gaara) [#11780](https://github.com/nodejs/node/pull/11780)
- [[`463f29413b`](https://github.com/nodejs/node/commit/463f29413b)] - **doc**: correct comment error in stream.md (Alexander) [#11804](https://github.com/nodejs/node/pull/11804)
- [[`8a521fe0dc`](https://github.com/nodejs/node/commit/8a521fe0dc)] - **doc**: var -\> let / const in events.md (Vse Mozhet Byt) [#11810](https://github.com/nodejs/node/pull/11810)
- [[`331c0a8a26`](https://github.com/nodejs/node/commit/331c0a8a26)] - **doc**: console.log() -\> console.error() in events.md (Vse Mozhet Byt) [#11810](https://github.com/nodejs/node/pull/11810)
- [[`82d2f13680`](https://github.com/nodejs/node/commit/82d2f13680)] - **doc**: update to current V8 versions (Franziska Hinkelmann) [#11787](https://github.com/nodejs/node/pull/11787)
- [[`be537d0062`](https://github.com/nodejs/node/commit/be537d0062)] - **doc**: package main can be directory with an index (Bradley Farias) [#11581](https://github.com/nodejs/node/pull/11581)
- [[`0e13887421`](https://github.com/nodejs/node/commit/0e13887421)] - **doc**: reduce font size on smaller screens (Gibson Fahnestock) [#11695](https://github.com/nodejs/node/pull/11695)
- [[`0acebb985f`](https://github.com/nodejs/node/commit/0acebb985f)] - **doc**: fix occurences of "the the" (Jeroen Mandersloot) [#11711](https://github.com/nodejs/node/pull/11711)
- [[`8de856b191`](https://github.com/nodejs/node/commit/8de856b191)] - **doc**: fix process links to console.log/error (Sam Roberts) [#11718](https://github.com/nodejs/node/pull/11718)
- [[`12760339dc`](https://github.com/nodejs/node/commit/12760339dc)] - **doc**: add Franziska Hinkelmann to the CTC (Rod Vagg) [#11488](https://github.com/nodejs/node/pull/11488)
- [[`e8f0dba3af`](https://github.com/nodejs/node/commit/e8f0dba3af)] - **doc**: fixed readable.isPaused() version annotation (Laurent Fortin) [#11677](https://github.com/nodejs/node/pull/11677)
- [[`40b27ba8bb`](https://github.com/nodejs/node/commit/40b27ba8bb)] - **doc**: remove Locked from stability index (Rich Trott) [#11661](https://github.com/nodejs/node/pull/11661)
- [[`70a6a0a918`](https://github.com/nodejs/node/commit/70a6a0a918)] - **doc**: unlock module (Rich Trott) [#11661](https://github.com/nodejs/node/pull/11661)
- [[`e02d724273`](https://github.com/nodejs/node/commit/e02d724273)] - **doc**: fix misleading ASCII comments (Rahat Ahmed) [#11657](https://github.com/nodejs/node/pull/11657)
- [[`3419b7a9d4`](https://github.com/nodejs/node/commit/3419b7a9d4)] - **doc**: add `Daijiro Wachi` to collaborators (Daijiro Wachi) [#11676](https://github.com/nodejs/node/pull/11676)
- [[`a042c8a0ef`](https://github.com/nodejs/node/commit/a042c8a0ef)] - **doc**: fix typo in stream doc (Bradley Curran) [#11560](https://github.com/nodejs/node/pull/11560)
- [[`c0663e51d1`](https://github.com/nodejs/node/commit/c0663e51d1)] - **doc**: fixup errors.md (Vse Mozhet Byt) [#11566](https://github.com/nodejs/node/pull/11566)
- [[`0aab0503be`](https://github.com/nodejs/node/commit/0aab0503be)] - **doc**: add link to references in net.Socket (Joyee Cheung) [#11625](https://github.com/nodejs/node/pull/11625)
- [[`109fd72f11`](https://github.com/nodejs/node/commit/109fd72f11)] - **doc**: use common malformed instead of misformatted (James Sumners) [#11518](https://github.com/nodejs/node/pull/11518)
- [[`6c3b104548`](https://github.com/nodejs/node/commit/6c3b104548)] - **doc**: fix typo in STYLE_GUIDE.md (Nikolai Vavilov) [#11615](https://github.com/nodejs/node/pull/11615)
- [[`c9b302b96e`](https://github.com/nodejs/node/commit/c9b302b96e)] - **doc**: make os api doc more consistent (Evan Lucas) [#10994](https://github.com/nodejs/node/pull/10994)
- [[`cbfc3fcd9d`](https://github.com/nodejs/node/commit/cbfc3fcd9d)] - **doc**: use correct tls certificate property name (Sam Roberts) [#10389](https://github.com/nodejs/node/pull/10389)
- [[`4fd765eec8`](https://github.com/nodejs/node/commit/4fd765eec8)] - **doc**: clarify memory sharing behavior of buffer ctor (Zach Bjornson) [#10778](https://github.com/nodejs/node/pull/10778)
- [[`c138ba3684`](https://github.com/nodejs/node/commit/c138ba3684)] - **doc**: new TLSSocket has no secure context options (Sam Roberts) [#10545](https://github.com/nodejs/node/pull/10545)
- [[`16048480e7`](https://github.com/nodejs/node/commit/16048480e7)] - **doc**: fix stylistic issues in api/net.md (Alexey Orlenko) [#11786](https://github.com/nodejs/node/pull/11786)
- [[`de22ff642f`](https://github.com/nodejs/node/commit/de22ff642f)] - **doc**: fix broken URL to event loop guide (Poker) [#11670](https://github.com/nodejs/node/pull/11670)
- [[`0dfb9daa04`](https://github.com/nodejs/node/commit/0dfb9daa04)] - **doc**: add supported platforms list for v6.x (Michael Dawson) [#11943](https://github.com/nodejs/node/pull/11943)
- [[`b9766bdd1b`](https://github.com/nodejs/node/commit/b9766bdd1b)] - **doc**: add supported platforms list (Michael Dawson) [#11943](https://github.com/nodejs/node/pull/11943)
- [[`f1c2f2675c`](https://github.com/nodejs/node/commit/f1c2f2675c)] - **doc,test**: tls .ca option supports multi-PEM files (Sam Roberts) [#10389](https://github.com/nodejs/node/pull/10389)
- [[`1158f44599`](https://github.com/nodejs/node/commit/1158f44599)] - **events,test**: fix TypeError in EventEmitter warning (jseagull) [#9021](https://github.com/nodejs/node/pull/9021)
- [[`0fff04f24f`](https://github.com/nodejs/node/commit/0fff04f24f)] - **lib**: add comment to script eval \_tickCallback (Gibson Fahnestock) [#12050](https://github.com/nodejs/node/pull/12050)
- [[`1a7d6337fb`](https://github.com/nodejs/node/commit/1a7d6337fb)] - **lib**: fix event race condition with -e (Ben Noordhuis) [#11958](https://github.com/nodejs/node/pull/11958)
- [[`f8426d9177`](https://github.com/nodejs/node/commit/f8426d9177)] - **lib**: remove unused msg parameter in debug_agent (mr-spd) [#11833](https://github.com/nodejs/node/pull/11833)
- [[`e3105cf50a`](https://github.com/nodejs/node/commit/e3105cf50a)] - **meta**: move WORKING_GROUPS.md to CTC repo (James M Snell) [#11555](https://github.com/nodejs/node/pull/11555)
- [[`f0288f3969`](https://github.com/nodejs/node/commit/f0288f3969)] - **meta**: remove out of date ROADMAP.md file (James M Snell) [#11556](https://github.com/nodejs/node/pull/11556)
- [[`500d17b071`](https://github.com/nodejs/node/commit/500d17b071)] - **module**: fix loading from global folders on Windows (Richard Lau) [#9283](https://github.com/nodejs/node/pull/9283)
- [[`06752d1fc0`](https://github.com/nodejs/node/commit/06752d1fc0)] - **net**: remove misleading comment (Ben Noordhuis) [#11573](https://github.com/nodejs/node/pull/11573)
- [[`bed6acb1ed`](https://github.com/nodejs/node/commit/bed6acb1ed)] - **_Revert_** "**src**: fix delete operator on vm context" (Myles Borins) [#12721](https://github.com/nodejs/node/pull/12721)
- [[`c667e6e083`](https://github.com/nodejs/node/commit/c667e6e083)] - **src**: add fcntl.h include to node.cc (Bartosz Sosnowski) [#12540](https://github.com/nodejs/node/pull/12540)
- [[`1a63321dbf`](https://github.com/nodejs/node/commit/1a63321dbf)] - **src**: fix base64 decoding (Nikolai Vavilov) [#11995](https://github.com/nodejs/node/pull/11995)
- [[`1434e7ff11`](https://github.com/nodejs/node/commit/1434e7ff11)] - **src**: ensure that fd 0-2 are valid on windows (Bartosz Sosnowski) [#11863](https://github.com/nodejs/node/pull/11863)
- [[`1035967989`](https://github.com/nodejs/node/commit/1035967989)] - **src**: remove outdated FIXME in node_crypto.cc (Daniel Bevenius) [#11669](https://github.com/nodejs/node/pull/11669)
- [[`c33933eb6d`](https://github.com/nodejs/node/commit/c33933eb6d)] - **src, buffer**: do not segfault on out-of-range index (Timothy Gu) [#11927](https://github.com/nodejs/node/pull/11927)
- [[`f9287461dd`](https://github.com/nodejs/node/commit/f9287461dd)] - **stream**: avoid additional validation for Buffers (Brian White) [#10580](https://github.com/nodejs/node/pull/10580)
- [[`457c47d85e`](https://github.com/nodejs/node/commit/457c47d85e)] - **stream_base,tls_wrap**: notify on destruct (Trevor Norris) [#11947](https://github.com/nodejs/node/pull/11947)
- [[`aae3765e6f`](https://github.com/nodejs/node/commit/aae3765e6f)] - **test**: refactor several parallel/test-timer tests (Beth Griggs) [#10524](https://github.com/nodejs/node/pull/10524)
- [[`8c922736d0`](https://github.com/nodejs/node/commit/8c922736d0)] - **test**: add a second argument to assert.throws() (dave-k) [#12139](https://github.com/nodejs/node/pull/12139)
- [[`a30ae72350`](https://github.com/nodejs/node/commit/a30ae72350)] - **test**: skip irrelevant test on Windows (Rich Trott) [#12261](https://github.com/nodejs/node/pull/12261)
- [[`49ee30b8ac`](https://github.com/nodejs/node/commit/49ee30b8ac)] - **test**: more robust check for location of `node.exe` (Refael Ackermann) [#12120](https://github.com/nodejs/node/pull/12120)
- [[`a93eaa4b2c`](https://github.com/nodejs/node/commit/a93eaa4b2c)] - **test**: performance, remove Popen(shell=True) on Win (Refael Ackermann) [#12138](https://github.com/nodejs/node/pull/12138)
- [[`5f928a85e5`](https://github.com/nodejs/node/commit/5f928a85e5)] - **test**: increase querystring coverage (DavidCai) [#12163](https://github.com/nodejs/node/pull/12163)
- [[`7af87384bc`](https://github.com/nodejs/node/commit/7af87384bc)] - **test**: fix flaky test-child-process-exec-timeout (Santiago Gimeno) [#12159](https://github.com/nodejs/node/pull/12159)
- [[`4caae6924f`](https://github.com/nodejs/node/commit/4caae6924f)] - **test**: reduce buffer size in buffer-creation test (Sakthipriyan Vairamani (thefourtheye)) [#11177](https://github.com/nodejs/node/pull/11177)
- [[`eb19acb84e`](https://github.com/nodejs/node/commit/eb19acb84e)] - **test**: fix misleading comment (Franziska Hinkelmann) [#12048](https://github.com/nodejs/node/pull/12048)
- [[`e2279e297a`](https://github.com/nodejs/node/commit/e2279e297a)] - **test**: fix broken tests in test-buffer-includes (Alexey Orlenko) [#12040](https://github.com/nodejs/node/pull/12040)
- [[`cddc32c954`](https://github.com/nodejs/node/commit/cddc32c954)] - **test**: replace throw with common.fail (Dejon "DJ" Gill) [#9700](https://github.com/nodejs/node/pull/9700)
- [[`f23377c82d`](https://github.com/nodejs/node/commit/f23377c82d)] - **test**: test validity of prefix in mkdtempSync (Luca Maraschi) [#12009](https://github.com/nodejs/node/pull/12009)
- [[`c65de59f52`](https://github.com/nodejs/node/commit/c65de59f52)] - **test**: add regex for expected error message (John F. Mercer) [#12011](https://github.com/nodejs/node/pull/12011)
- [[`fcc19e1637`](https://github.com/nodejs/node/commit/fcc19e1637)] - **test**: add second argument to assert.throws() (Rj Bernaldo) [#12016](https://github.com/nodejs/node/pull/12016)
- [[`b69cac72e4`](https://github.com/nodejs/node/commit/b69cac72e4)] - **test**: refactor test-cluster-disconnect (Rich Trott) [#11981](https://github.com/nodejs/node/pull/11981)
- [[`4cb4803db2`](https://github.com/nodejs/node/commit/4cb4803db2)] - **test**: add coverage for child_process bounds check (Rich Trott) [#11800](https://github.com/nodejs/node/pull/11800)
- [[`2ee2cc6907`](https://github.com/nodejs/node/commit/2ee2cc6907)] - **test**: refactor test-cli-eval.js (cjihrig) [#10898](https://github.com/nodejs/node/pull/10898)
- [[`b6c30e14fc`](https://github.com/nodejs/node/commit/b6c30e14fc)] - **test**: fix broken assertion (cjihrig) [#10840](https://github.com/nodejs/node/pull/10840)
- [[`4d6b484cf4`](https://github.com/nodejs/node/commit/4d6b484cf4)] - **test**: refactor test-cli-eval.js (Sumit Goel) [#10759](https://github.com/nodejs/node/pull/10759)
- [[`31dea5c319`](https://github.com/nodejs/node/commit/31dea5c319)] - **test**: invalid chars in http client path (Luca Maraschi) [#11964](https://github.com/nodejs/node/pull/11964)
- [[`6063a4ac17`](https://github.com/nodejs/node/commit/6063a4ac17)] - **test**: improve test-vm-cached-data.js (Nick Peleh) [#11974](https://github.com/nodejs/node/pull/11974)
- [[`38017905d6`](https://github.com/nodejs/node/commit/38017905d6)] - **test**: add test for child_process.execFile() (Rich Trott) [#11929](https://github.com/nodejs/node/pull/11929)
- [[`485bb1b334`](https://github.com/nodejs/node/commit/485bb1b334)] - **test**: fix flaky test-tls-socket-close (Rich Trott) [#11921](https://github.com/nodejs/node/pull/11921)
- [[`9dd918b0ab`](https://github.com/nodejs/node/commit/9dd918b0ab)] - **test**: fix assertion in vm test (AnnaMag) [#11862](https://github.com/nodejs/node/pull/11862)
- [[`8e5c8a3ac1`](https://github.com/nodejs/node/commit/8e5c8a3ac1)] - **test**: failing behaviour on sandboxed Proxy (AnnaMag) [#11671](https://github.com/nodejs/node/pull/11671)
- [[`72710d05b9`](https://github.com/nodejs/node/commit/72710d05b9)] - **test**: fix flaky test-domain-abort-on-uncaught (Rich Trott) [#11817](https://github.com/nodejs/node/pull/11817)
- [[`46b2e45b40`](https://github.com/nodejs/node/commit/46b2e45b40)] - **test**: added test for indexed properties (AnnaMag) [#11769](https://github.com/nodejs/node/pull/11769)
- [[`a3f327fd21`](https://github.com/nodejs/node/commit/a3f327fd21)] - **test**: add regex to assert.throws (Matej Krajčovič) [#11815](https://github.com/nodejs/node/pull/11815)
- [[`4d916da0d7`](https://github.com/nodejs/node/commit/4d916da0d7)] - **test**: fail when child dies in fork-net (Joyee Cheung) [#11684](https://github.com/nodejs/node/pull/11684)
- [[`7d4941f01a`](https://github.com/nodejs/node/commit/7d4941f01a)] - **test**: fix args in parallel/test-fs-null-bytes.js (Vse Mozhet Byt) [#11601](https://github.com/nodejs/node/pull/11601)
- [[`cd98f5d303`](https://github.com/nodejs/node/commit/cd98f5d303)] - **test**: fix flaky test-http-set-timeout-server (Santiago Gimeno) [#11790](https://github.com/nodejs/node/pull/11790)
- [[`67b6d7d123`](https://github.com/nodejs/node/commit/67b6d7d123)] - **test**: add test for loading from global folders (Richard Lau) [#9283](https://github.com/nodejs/node/pull/9283)
- [[`aa9815081d`](https://github.com/nodejs/node/commit/aa9815081d)] - **test**: add script to create 0-dns-cert.pem (Shigeki Ohtsu) [#11579](https://github.com/nodejs/node/pull/11579)
- [[`5a93eab30d`](https://github.com/nodejs/node/commit/5a93eab30d)] - **test**: increase coverage of console (DavidCai) [#11653](https://github.com/nodejs/node/pull/11653)
- [[`4befdd1c13`](https://github.com/nodejs/node/commit/4befdd1c13)] - **test**: limit lint rule disabling in message test (Rich Trott) [#11724](https://github.com/nodejs/node/pull/11724)
- [[`91b8da67a5`](https://github.com/nodejs/node/commit/91b8da67a5)] - **test**: skip the test with proper TAP message (Sakthipriyan Vairamani (thefourtheye)) [#11584](https://github.com/nodejs/node/pull/11584)
- [[`a43aa0eaa9`](https://github.com/nodejs/node/commit/a43aa0eaa9)] - **test**: changed test1 of test-vm-timeout.js (maurice_hayward) [#11590](https://github.com/nodejs/node/pull/11590)
- [[`1b4f69acae`](https://github.com/nodejs/node/commit/1b4f69acae)] - **test**: remove obsolete eslint-disable comment (Rich Trott) [#11643](https://github.com/nodejs/node/pull/11643)
- [[`7cc4645b70`](https://github.com/nodejs/node/commit/7cc4645b70)] - **test**: fix tests when npn feature is disabled. (Shigeki Ohtsu) [#11655](https://github.com/nodejs/node/pull/11655)
- [[`96924ed8f5`](https://github.com/nodejs/node/commit/96924ed8f5)] - **test**: add test-buffer-prototype-inspect (Rich Trott) [#11600](https://github.com/nodejs/node/pull/11600)
- [[`a27098d921`](https://github.com/nodejs/node/commit/a27098d921)] - **test**: enable max-len for test-repl (Rich Trott) [#11559](https://github.com/nodejs/node/pull/11559)
- [[`640b72e27d`](https://github.com/nodejs/node/commit/640b72e27d)] - **test**: fix flaky test-https-agent-create-connection (Santiago Gimeno) [#11649](https://github.com/nodejs/node/pull/11649)
- [[`678e225d56`](https://github.com/nodejs/node/commit/678e225d56)] - **test**: improve https coverage to check create connection (chiaki-yokoo) [#11435](https://github.com/nodejs/node/pull/11435)
- [[`d4f2ef7a59`](https://github.com/nodejs/node/commit/d4f2ef7a59)] - **test**: apply strict mode in test-repl (Rich Trott) [#11575](https://github.com/nodejs/node/pull/11575)
- [[`0322d3b8d5`](https://github.com/nodejs/node/commit/0322d3b8d5)] - **test**: skip tests with common.skip (Sakthipriyan Vairamani (thefourtheye)) [#11585](https://github.com/nodejs/node/pull/11585)
- [[`4575f92428`](https://github.com/nodejs/node/commit/4575f92428)] - **test**: move common tls connect setup into fixtures (Sam Roberts) [#10389](https://github.com/nodejs/node/pull/10389)
- [[`4207bceacd`](https://github.com/nodejs/node/commit/4207bceacd)] - **test**: check tls server verification with addCACert (Sam Roberts) [#10389](https://github.com/nodejs/node/pull/10389)
- [[`1d7fab3740`](https://github.com/nodejs/node/commit/1d7fab3740)] - **test**: tls cert chain completion scenarios (Sam Roberts) [#10389](https://github.com/nodejs/node/pull/10389)
- [[`a1cb6992d9`](https://github.com/nodejs/node/commit/a1cb6992d9)] - **test**: getgroups() may contain duplicate GIDs (Sam Roberts) [#10389](https://github.com/nodejs/node/pull/10389)
- [[`eb47897f52`](https://github.com/nodejs/node/commit/eb47897f52)] - **test**: refactor test-stream2-readable-wrap.js (dpg5000) [#10551](https://github.com/nodejs/node/pull/10551)
- [[`6f85c81f0d`](https://github.com/nodejs/node/commit/6f85c81f0d)] - **test**: s/assert.equal/assert.strictEqual/ (Gibson Fahnestock) [#10698](https://github.com/nodejs/node/pull/10698)
- [[`afea1d041e`](https://github.com/nodejs/node/commit/afea1d041e)] - **test**: refactor test-beforeexit-event-exit.js (cjihrig) [#10577](https://github.com/nodejs/node/pull/10577)
- [[`f0645200aa`](https://github.com/nodejs/node/commit/f0645200aa)] - **test**: improve test-fs-access (Adrian Estrada) [#10542](https://github.com/nodejs/node/pull/10542)
- [[`f874256aae`](https://github.com/nodejs/node/commit/f874256aae)] - **test**: skip when openssl CLI doesn't exist (Sota Yamashita) [#11095](https://github.com/nodejs/node/pull/11095)
- [[`9162316ef5`](https://github.com/nodejs/node/commit/9162316ef5)] - **test**: add arrow functions to test-util-inspect (Alexey Orlenko) [#11781](https://github.com/nodejs/node/pull/11781)
- [[`db61c952de`](https://github.com/nodejs/node/commit/db61c952de)] - **test**: use eslint to fix var-\>const/let (Gibson Fahnestock) [#10685](https://github.com/nodejs/node/pull/10685)
- [[`632aee186d`](https://github.com/nodejs/node/commit/632aee186d)] - **timers**: fix not to close reused timer handle (Shigeki Ohtsu) [#11646](https://github.com/nodejs/node/pull/11646)
- [[`39f7aaa290`](https://github.com/nodejs/node/commit/39f7aaa290)] - **timers**: unlock the timers API (Rich Trott) [#11580](https://github.com/nodejs/node/pull/11580)
- [[`d0868ff36c`](https://github.com/nodejs/node/commit/d0868ff36c)] - **tls**: fix segfault on destroy after partial read (Ben Noordhuis) [#11898](https://github.com/nodejs/node/pull/11898)
- [[`1baee1829b`](https://github.com/nodejs/node/commit/1baee1829b)] - **tls**: keep track of stream that is closed (jBarz) [#11776](https://github.com/nodejs/node/pull/11776)
- [[`b1ddf11c14`](https://github.com/nodejs/node/commit/b1ddf11c14)] - **tls**: fix macro to check NPN feature (Shigeki Ohtsu) [#11655](https://github.com/nodejs/node/pull/11655)
- [[`6eb1c25263`](https://github.com/nodejs/node/commit/6eb1c25263)] - **tools**: ignore URLs in line length linting (Rich Trott) [#11890](https://github.com/nodejs/node/pull/11890)
- [[`cad425c571`](https://github.com/nodejs/node/commit/cad425c571)] - **tools**: update dotfile whitelist in .gitignore (Michaël Zasso) [#12116](https://github.com/nodejs/node/pull/12116)
- [[`3858861463`](https://github.com/nodejs/node/commit/3858861463)] - **tools**: add links to the stability index reference (Michael Cox) [#11664](https://github.com/nodejs/node/pull/11664)
- [[`3e6d9922b9`](https://github.com/nodejs/node/commit/3e6d9922b9)] - **tools**: remove NODE_PATH from environment for tests (Rich Trott) [#11612](https://github.com/nodejs/node/pull/11612)
- [[`de63698066`](https://github.com/nodejs/node/commit/de63698066)] - **tools, test**: require const/let in test (Gibson Fahnestock) [#10685](https://github.com/nodejs/node/pull/10685)
- [[`63449972d1`](https://github.com/nodejs/node/commit/63449972d1)] - **url**: use `hasIntl` instead of `try-catch` (Daijiro Wachi) [#11571](https://github.com/nodejs/node/pull/11571)

Windows 32-bit Installer: https://nodejs.org/dist/v6.10.3/node-v6.10.3-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v6.10.3/node-v6.10.3-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v6.10.3/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v6.10.3/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v6.10.3/node-v6.10.3.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v6.10.3/node-v6.10.3-darwin-x64.tar.gz \
Linux 32-bit Binary: https://nodejs.org/dist/v6.10.3/node-v6.10.3-linux-x86.tar.xz \
Linux 64-bit Binary: https://nodejs.org/dist/v6.10.3/node-v6.10.3-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v6.10.3/node-v6.10.3-linux-ppc64le.tar.xz \
Linux PPC BE 64-bit Binary: https://nodejs.org/dist/v6.10.3/node-v6.10.3-linux-ppc64.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v6.10.3/node-v6.10.3-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v6.10.3/node-v6.10.3-aix-ppc64.tar.gz \
SmartOS 32-bit Binary: https://nodejs.org/dist/v6.10.3/node-v6.10.3-sunos-x86.tar.xz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v6.10.3/node-v6.10.3-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v6.10.3/node-v6.10.3-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v6.10.3/node-v6.10.3-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v6.10.3/node-v6.10.3-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v6.10.3/node-v6.10.3.tar.gz \
Other release files: https://nodejs.org/dist/v6.10.3/ \
Documentation: https://nodejs.org/docs/v6.10.3/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

1cc6ffb9f41512be53cac90bccebc3c7d8143e06f0b95441deee218b3f47f2f5  node-v6.10.3-aix-ppc64.tar.gz
c09b2e60b7c12d88199d773f7ce046a6890e7c5d3be0cf68312ae3da474f32a2  node-v6.10.3-darwin-x64.tar.gz
d6744fe89a5ffb2b00e1703344280cda3c6a8e04371ebf1fede7995bbfeb9d23  node-v6.10.3-darwin-x64.tar.xz
855016ab29ad2c31b930c291b031a7314624ba613ef9127e87b55824d42fa7bf  node-v6.10.3-headers.tar.gz
fc310532ef322e2c8593c7552ba8d2fefa9d4e285e4a2d8af88e7b515994d276  node-v6.10.3-headers.tar.xz
c2ff51d8a824d5d2ca8ba7a2e8e4306c4c356edf5c0aa7f4cb2f93091c0e1310  node-v6.10.3-linux-arm64.tar.gz
8b0ea8f0616f0c9dbd953bcb330d22b253cc1e3f3c657885e8476427462169c0  node-v6.10.3-linux-arm64.tar.xz
420be39bca0d9a2643fedf6a805ee1705cb1934074b1e6dc351dd3ff46121790  node-v6.10.3-linux-armv6l.tar.gz
b34bc4f71c74d437338ff9f714e12e6f58bafb428cc6f8d17ed1985df8c5563f  node-v6.10.3-linux-armv6l.tar.xz
c36f0713fcf397a6b278b61f4c1511938f7ad1e5385c31e530d074ce83163426  node-v6.10.3-linux-armv7l.tar.gz
d64867d32cd386ebc802445e24613bf9420c9ba0d7049cd17a2e175a5942860a  node-v6.10.3-linux-armv7l.tar.xz
4fde09f3eb7fe279102a17cb0206e2b48ab569cf7bb29ca54538cd919e4afcba  node-v6.10.3-linux-ppc64le.tar.gz
d810e40e33b760ce30f5ee57496b50abcb4a6fb88f84186e9e7be5bea5c90e43  node-v6.10.3-linux-ppc64le.tar.xz
2fbb50c7a1844aa296a964d2bee906cce182de34f760fd7900839ef19445ffa8  node-v6.10.3-linux-ppc64.tar.gz
af1c33a4736e626338148f3f93e9a937dcdadbc734a2d36af4ee46636eeeb021  node-v6.10.3-linux-ppc64.tar.xz
b4013a4e54c629700c40d9eb6fd3de636dddc8b8aa1e6e1b8c5173a02b245f76  node-v6.10.3-linux-s390x.tar.gz
2ffe4b64bd1ddc566d72834c8f61fb3b34c5bd513305d96f4a07b6aea12f3ef3  node-v6.10.3-linux-s390x.tar.xz
c6a60f823a4df31f1ed3a4044d250e322f2f2794d97798d47c6ee4af9376f927  node-v6.10.3-linux-x64.tar.gz
00d0aea8e47a68da6e3278d7c2fc1504de46a34d97b4b2fa5610b04a64fce04c  node-v6.10.3-linux-x64.tar.xz
42767df2dff5b9834764f6ec4970741b9b601ac654a3ce39718bc1320273b721  node-v6.10.3-linux-x86.tar.gz
0cd9bd4c68ed8aa062961bbe1f914cd62c004fd5d53dbec5800eb067bdd33807  node-v6.10.3-linux-x86.tar.xz
0a2c7a536f9009b9f18b10640b10ea6e5db1573104e9b7f5a80779e708c218b8  node-v6.10.3.pkg
f93478a8805f280c61b0d9019377117bcf8076574d2e822acb126abfe91d4466  node-v6.10.3-sunos-x64.tar.gz
e103113d0a9cb7fd91ee72c39c3d43cf456ddb4722b54bff7453331c19517db3  node-v6.10.3-sunos-x64.tar.xz
5322e8d9ba87c25187fc19785afbc307477d575644e1b06b380f3977c433ec0b  node-v6.10.3-sunos-x86.tar.gz
ae9f7718b71447a956f3e0b4743a733a43d037beb9390cbd807e2bb178343a5f  node-v6.10.3-sunos-x86.tar.xz
a8f679f595fd921305c28d126935ad59b4419ac8474a99997a31e01ab50acd3d  node-v6.10.3.tar.gz
82262a703e61164e09170a14d88b1726720651b0c7ee87a277654247b21b5388  node-v6.10.3.tar.xz
86993d66c110592be3a4d4e78c5d386b17e1d8437cc6fa2a5a227369c1915a1e  node-v6.10.3-win-x64.7z
df61044aaf011820800061f23ab47f58ce33855529a1825cd9d6ca7be2550021  node-v6.10.3-win-x64.zip
d236ab5607a1cee6ef5f1a7ae1c1bfbe212218dd7d823ea2d57d12e3d3bf7cd8  node-v6.10.3-win-x86.7z
b66efb376a21d31f2ae79b9b1f8b510cb8737634055041b333f763cbeb022c35  node-v6.10.3-win-x86.zip
3ab1286e8fdadc60f57a97f2222ad6b3d7a48847a52ef812cc8d5f354bf500ff  node-v6.10.3-x64.msi
d4000be1329737bd2f4c2e54abc939ca249c11cbcc8898240fece37cb74cf09f  node-v6.10.3-x86.msi
2a23e246d877d87d1eb8470df6cf5efff6339df591ccf5608e9c3740787c2294  win-x64/node.exe
5000f297ca228d1d120c2e850f3d6e291cbb42b9010f26da754bd61f194eabc1  win-x64/node.lib
17b92f5d9dd1a235fbae0674ac81f697cb97d39d9ae558a9e9db0c3f145822f7  win-x64/node_pdb.7z
909b2cfaf7d8f343226d3a5a97bf6113fce43484b480518d36b2d958e3f21e84  win-x64/node_pdb.zip
518ea2f7e77780ad2eaeadaa818c8d45ac480a62d8b503f88e25412572ebce49  win-x86/node.exe
1b207f4d6d766038ccdf7fcd1f405b3883d1780c81625e32daf663eb56bfa70f  win-x86/node.lib
b0cf0c8d0544906dbb79d6bea49f56f61a073ee074e10a31a8f10ddebe805f2b  win-x86/node_pdb.7z
49e6ee0aab1096341b74a6d7a3dcf114e00c1745e3ab440c6681fe5becf664d9  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQEcBAEBCAAGBQJZCMptAAoJEJM7AfQLXKlGb3UH/Aq8uQiFKC9okTIGlZIv9ciI
4m4l3aWZjllmt15CX+0dxKrWmF7mNaS+9W9LB9gGB98QcIsEL3Hgo+h1k9QfMZeB
khKR8pfj/lsMQb9qOQw/EWVilvvQ+lZH3WuTMF3VKJtmwd5ptgqtg11RHJM08EpK
Ob3zIOtVWHapPud8TTDVhfbJ3wWy0egDnE+G1A88L7JUZTY7aeHxKg20CxZwpTaQ
7LPE0cwK/9CQa7MlyROI9Gtz9A9BL5AlILv5OVnT0AHGeY1L2ADLZkECHeWUkU8c
7MpnA3slOEB8drldwQrmkCl87tQET3x8ZcxInJZMdwgpLZdIvIEPAmdwtaTz4YY=
=mMDB
-----END PGP SIGNATURE-----

```
