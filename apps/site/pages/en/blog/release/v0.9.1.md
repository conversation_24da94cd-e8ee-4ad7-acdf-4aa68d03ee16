---
date: '2012-08-28T22:33:45.659Z'
category: release
title: Version 0.9.1 (Unstable)
layout: blog-post
author: The Node.js Project
---

2012.08.28, Version 0.9.1 (Unstable)

- buffer: Add Buffer.isEncoding(enc) to test for valid encoding values (isaacs)

- Raise UV_ECANCELED on premature close. (<PERSON>)

- Remove c-ares from libuv, move to a top-level node dependency (<PERSON>)

- ref/unref for all HandleWraps, timers, servers, and sockets (<PERSON>)

- addon: remove node-waf, superseded by node-gyp (<PERSON>)

- child_process: emit error on exec failure (<PERSON>)

- cluster: do not use internal server API (<PERSON>)

- constants: add O_DIRECT (<PERSON>)

- crypto: add sync interface to crypto.pbkdf2() (<PERSON>)

- darwin: emulate fdatasync() (<PERSON><PERSON>)

- dgram: make .bind() always asynchronous (<PERSON>)

- events: Make emitter.listeners() side-effect free (<PERSON><PERSON><PERSON>, <PERSON>)

- fs: Throw early on invalid encoding args (isaacs)

- fs: fix naming of truncate/ftruncate functions (isaacs)

- http: bubble up parser errors to ClientRequest (Brian White)

- linux: improve cpuinfo parser on ARM and MIPS (Ben Noordhuis)

- net: add support for IPv6 addresses ending in :: (Josh Erickson)

- net: support Server.listen(Pipe) (Andreas Madsen)

- node: don't scan add-on for "init" symbol (Ben Noordhuis)

- remove process.uvCounters() (Ben Noordhuis)

- repl: console writes to repl rather than process stdio (Nathan Rajlich)

- timers: implement setImmediate (Timothy J Fontaine)

- tls: fix segfault in pummel/test-tls-ci-reneg-attack (Ben Noordhuis)

- tools: Move gyp addon tools to node-gyp (Nathan Rajlich)

- unix: preliminary signal handler support (Ben Noordhuis)

- unix: remove dependency on ev_child (Ben Noordhuis)

- unix: work around darwin bug, don't poll() on pipe (Fedor Indutny)

- util: Formally deprecate util.pump() (Ben Noordhuis)

- windows: make active and closing handle state independent (Bert Belder)

- windows: report spawn errors to the exit callback (Bert Belder)

- windows: signal handling support with uv_signal_t (Bert Belder)

Source Code: https://nodejs.org/dist/v0.9.1/node-v0.9.1.tar.gz

Macintosh Installer (Universal): https://nodejs.org/dist/v0.9.1/node-v0.9.1.pkg

Windows Installer: https://nodejs.org/dist/v0.9.1/node-v0.9.1-x86.msi

Windows x64 Installer: https://nodejs.org/dist/v0.9.1/x64/node-v0.9.1-x64.msi

Windows x64 Files: https://nodejs.org/dist/v0.9.1/x64/

Linux 32-bit Binary: https://nodejs.org/dist/v0.9.1/node-v0.9.1-linux-x86.tar.gz

Linux 64-bit Binary: https://nodejs.org/dist/v0.9.1/node-v0.9.1-linux-x64.tar.gz

Solaris 32-bit Binary: https://nodejs.org/dist/v0.9.1/node-v0.9.1-sunos-x86.tar.gz

Solaris 64-bit Binary: https://nodejs.org/dist/v0.9.1/node-v0.9.1-sunos-x64.tar.gz

Other release files: https://nodejs.org/dist/v0.9.1/

Website: https://nodejs.org/docs/v0.9.1/

Documentation: https://nodejs.org/docs/v0.9.1/api/

Shasums:

```
b86a5b0b2c9a89d08baaeb3d5270a8f247b4ba43  node-v0.9.1-darwin-x64.tar.gz
800af9d15a4b65e624351bb94dedc3b7eb322b86  node-v0.9.1-darwin-x86.tar.gz
a7ea43b0db472ca84dc9838960dae57ff0abaa72  node-v0.9.1-linux-x64.tar.gz
89c4be492a946eefa45aa87d16787875aa0f2dff  node-v0.9.1-linux-x86.tar.gz
6495d2814541e945dba5941b8d3facf2a61dc55d  node-v0.9.1-sunos-x64.tar.gz
05ea83ba4d648f5b949833bb28d69e6eeebabb78  node-v0.9.1-sunos-x86.tar.gz
3ccf1ac2fea7f2d05bcaed590bc54ee61c982fbb  node-v0.9.1-x86.msi
bd0ede40e4681b16088284fe6a18ae3a5c5a3795  node-v0.9.1.tar.gz
159470a999ef23fa940ad3c54265053c3d2a4332  node.exe
02472e388a44b91e2644178a0fe011354fcd361e  node.exp
c6d6ce22be4b47ad022d82dd91ebd4c040742fee  node.lib
7b4cec8b4106d90a077388fe483fdbb13119dcb1  node.pdb
a5fbf5e22c7623a9206abc6608d084ff53c7c4c9  x64/node-v0.9.1-x64.msi
9755685a9d7bea6dc63d3260829c26c5b0430a7a  x64/node.exe
621f177733647848f7f667926fe4f498a1f50c97  x64/node.exp
5877dffbf012c458dab88f3392af59cb33720d2f  x64/node.lib
dc513ce5c16771dcfab9c1777d5949252290b412  x64/node.pdb
```
