---
date: '2017-07-11T17:01:31.302Z'
category: release
title: Node v8.1.4 (Current)
layout: blog-post
author: <PERSON>
---

### Notable changes

- **build**:
  - Disable V8 snapshots - The hashseed embedded in the snapshot is currently the same for all runs of the binary. This opens node up to collision attacks which could result in a Denial of Service. We have temporarily disabled snapshots until a more robust solution is found (<PERSON>)
- **deps**:
  - CVE-2017-1000381 - The c-ares function ares_parse_naptr_reply(), which is used for parsing NAPTR responses, could be triggered to read memory outside of the given input buffer if the passed in DNS response packet was crafted in a particular way. This patch checks that there is enough data for the required elements of an NAPTR record (2 int16, 3 bytes for string lengths) before processing a record. (<PERSON>)

### Commits

- [[`51d69d2bec`](https://github.com/nodejs/node/commit/51d69d2bec)] - **build**: disable V8 snapshots (<PERSON>) [nodejs/node-private#84](https://github.com/nodejs/node-private/pull/84)
- [[`d70fac47af`](https://github.com/nodejs/node/commit/d70fac47af)] - **deps**: cherry-pick 9478908a49 from cares upstream (David Drysdale) [nodejs/node-private#88](https://github.com/nodejs/node-private/pull/88)
- [[`803d689873`](https://github.com/nodejs/node/commit/803d689873)] - **test**: verify hash seed uniqueness (Ali Ijaz Sheikh) [nodejs/node-private#84](https://github.com/nodejs/node-private/pull/84)

Windows 32-bit Installer: https://nodejs.org/dist/v8.1.4/node-v8.1.4-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v8.1.4/node-v8.1.4-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v8.1.4/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v8.1.4/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v8.1.4/node-v8.1.4.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v8.1.4/node-v8.1.4-darwin-x64.tar.gz \
Linux 32-bit Binary: https://nodejs.org/dist/v8.1.4/node-v8.1.4-linux-x86.tar.xz \
Linux 64-bit Binary: https://nodejs.org/dist/v8.1.4/node-v8.1.4-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v8.1.4/node-v8.1.4-linux-ppc64le.tar.xz \
Linux PPC BE 64-bit Binary: https://nodejs.org/dist/v8.1.4/node-v8.1.4-linux-ppc64.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v8.1.4/node-v8.1.4-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v8.1.4/node-v8.1.4-aix-ppc64.tar.gz \
SmartOS 32-bit Binary: https://nodejs.org/dist/v8.1.4/node-v8.1.4-sunos-x86.tar.xz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v8.1.4/node-v8.1.4-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v8.1.4/node-v8.1.4-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v8.1.4/node-v8.1.4-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v8.1.4/node-v8.1.4-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v8.1.4/node-v8.1.4.tar.gz \
Other release files: https://nodejs.org/dist/v8.1.4/ \
Documentation: https://nodejs.org/docs/v8.1.4/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

3d3f66dc9ef56456815b3edf3a4d0a984ce298e55f2e80bf5643108c3daa7f49  node-v8.1.4-aix-ppc64.tar.gz
a24858a10dd4ca8ad55fd61a7472b4fe9140eb3fa347c41717360c3f29438748  node-v8.1.4-darwin-x64.tar.gz
96967b51074f4095a69f4218adab4b901f3c86b11abf3700a70e13ab84c53d02  node-v8.1.4-darwin-x64.tar.xz
b3661b962399b06e14b452762ccb173298d30b1d4869ac2d12d8ff7dd172a2a7  node-v8.1.4-headers.tar.gz
9a3b081c6dc0098e445e29c1740105decb4e0e106eb85cf586c194492a04c371  node-v8.1.4-headers.tar.xz
fdbed111bb66c603c0b41dbcab2db1cb16c569acfb3da28325896084b4b3c165  node-v8.1.4-linux-arm64.tar.gz
3553c617f594286fea7052678d04aec9e167adddf702b70431d88ce42573c339  node-v8.1.4-linux-arm64.tar.xz
4c307b5fdd9bbe1a5ac96414f09687154b58d6beaae0bb9e43103418ab9cd6ab  node-v8.1.4-linux-armv6l.tar.gz
89d0ca44cf23f759a772698d59f8685cca11758126eae9d8c7a5ee441a4da067  node-v8.1.4-linux-armv6l.tar.xz
ed4fdfce417fda2224679153934ad67c662a7eb4fb1866027efe1ac8c6051646  node-v8.1.4-linux-armv7l.tar.gz
d33aece6a60a03bec3d306f1775ad5a4af0e6202badcf16c6af58e5a714e275a  node-v8.1.4-linux-armv7l.tar.xz
0fa3e3b90e985eca151a99c7ed93d9f42e1bb046fd1f7f7cb2bb603c890f8592  node-v8.1.4-linux-ppc64le.tar.gz
ee06dc3801d81a5eff48810f1faa50df4cf79cd8727b5d0529930b8675ebc8f4  node-v8.1.4-linux-ppc64le.tar.xz
a633f5125aa701a74d59bcc0b380eba7d1eca9dead1b37cbf4e77ae36d6c1405  node-v8.1.4-linux-ppc64.tar.gz
7fdf348cdf0ff16232594bb6af441a2d7907493c0256bc014d26059523fb935c  node-v8.1.4-linux-ppc64.tar.xz
405cc635d1c2851de61a1836863687dc0e901820d4f06e8234ffa1df79bf655e  node-v8.1.4-linux-s390x.tar.gz
283f144c12d018e43a9612245b40961a37c3a2b92e9c603e7e31dd62691cbecf  node-v8.1.4-linux-s390x.tar.xz
618f1a4eabc67de7372b68427c925274ba6b54c3951235077bca5d7e1d87e422  node-v8.1.4-linux-x64.tar.gz
d82fe7ef7e0f8ca1c343f00e3e490996553507ec7d42034f5df034cc7908caaf  node-v8.1.4-linux-x64.tar.xz
d0ab1676d4f3bf4b9f35c0429368959df0094627a8d32bbbf49bd685359a2443  node-v8.1.4-linux-x86.tar.gz
169b9497b4ac51c1914363c98d43a92781f323910eeca7b65244d2a23a897d60  node-v8.1.4-linux-x86.tar.xz
9c0a1fb2895f3d7865793fd64964ddfc8f3655e5b2beea28f35f78d782369d7a  node-v8.1.4.pkg
53a9bf00a0e2993986dcd4e9c07afc39d691a421b5e8bc6ad611b4b5619fc210  node-v8.1.4-sunos-x64.tar.gz
c4d291fda75cc88209cff38862c0c9aff4ceb02fbcda95632ba7b58e46eee975  node-v8.1.4-sunos-x64.tar.xz
f7fa950c744f579f9825acc0a3a196b8841ad809030b70e07cf8fcc007b5cbc8  node-v8.1.4-sunos-x86.tar.gz
f47273cac8609dafe25e2b7c49bbf63a3a99091ab5c33a5307b68d1ef9849b77  node-v8.1.4-sunos-x86.tar.xz
5d54960fb3c5e794b784d15e9e85e3853e1189e5ae840f314bf2fc091fbb5c12  node-v8.1.4.tar.gz
a83c86445f79749c46fd4f2c4e681a3e5bb51b2bde5dc7aed1dc38e4e242c301  node-v8.1.4.tar.xz
2b3fb42499480f2c1633f9b0e14a9efdb37d8fad0c06642fe9a9ef13f943847b  node-v8.1.4-win-x64.7z
cc3689ffefc738f6256aab1713343c3c64c24ec997600c0a48243fb26f5e0bce  node-v8.1.4-win-x64.zip
98d0ffc55c53084d095255242b42378b94260e3fb8410e1a6ec43d5df726a471  node-v8.1.4-win-x86.7z
da35dc36e3e209996b72284e7dbba348356868a022b755901a5d625e72c480e8  node-v8.1.4-win-x86.zip
5a98b1c72bb475e90c8f45aea171b3c8a778f8d75eae113301c2b9f234787f9e  node-v8.1.4-x64.msi
1b16cbe14e77629adad8da8f426babe75aac98a1b0e4780e927b1d6007f1587c  node-v8.1.4-x86.msi
da09b054b8ec8ed91fc5794d82ba0dc70577b67e4a454b4fceff77d81ec30586  win-x64/node.exe
67d11594a926692de413f47492c550fdfc77d717076d2674cabba2461f15d26e  win-x64/node.lib
f5a2e23ada65fb6cb09c4c96a76a18d0c72a977afb55b02499e8bfd523f3d8ca  win-x64/node_pdb.7z
005e0363321b23a9e58ec46fe798bcc2f6a61232ab3c90376fef4204a6ffbe72  win-x64/node_pdb.zip
1e4d45117b81da2896e2926eec2e72fa1c03ddc2a8bf55afc92fbe14b7fab414  win-x86/node.exe
92059acfe350f1d3f3e89371ff5753a3da9efa4cf1550cd38f7261ea9144115d  win-x86/node.lib
afbebbd4c8c3da8555c235b509550eb93602ff3b2b008e7d177b7b66252cd8f4  win-x86/node_pdb.7z
e0f5dd569fa340cb271b86800a565efb3561dd08abf306fa0f7cdf28cb390bd7  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----
Comment: GPGTools - https://gpgtools.org

iQIcBAEBCAAGBQJZZQHMAAoJELY7U1pMIGypIBEQANaX52bbCBq1R6+IRTf+qZQI
VwqWuIM3FGWTakLOYD5qRcq7vgCrAYm8u+ediWuO1/tTBBlsElkWkGnzSijnWi0s
JLS6zV0W55aauDq+qysnmP7Wqg9XCpH0iatymUXQhxSYC7NMnmijSatZK4QlDjWv
cRLmNgh6AkBrUIrSMfa0X4Z3Etc/o66iUjUcSztr47b8wxSWG4FPUDYnH7zrZvW7
bZlWkPZ5wdSTTRnaVXIMDxQ35BGs9z4/pJQb/sbdWalqwX+iiobyH013mM+yyPPC
8BBRpdyafvZ09+++n5y+uLyIP1ob7BwoODy6XNLsj04/FrJa1Y7eoOBmLJj9Ki+5
sP1dY7bV3XE2yrRpjEpO7AxYbc2m3HtLiZkQ6vn+X6NqvkiZeMYuSqIh2NhmepiL
kHnAxC7jLPpO91fNHsbDOLVlex5kdpZnTYx54PQjKHVzEFauYP6XYIyUPUNn2lzO
HQINYD/ZXa8HBpRO8fddS/kU0RSHZVur2zWK3mDYzhYUR9Jp/FuplCKejb0HPjuQ
e/WcBBUxwvtuQUpKxwOljofdlwmEtLE+sdstfQSQjACyXZrZYFhgpceqVBrRVTUD
h0gbHHiwsPLuWLICqDn46zm3r5QaXUs/LiZi8p6tJa/EJaMZPGhg+6u/IhG6/Eoz
8MKljLCqXeCY1oc6Fuom
=TFpN
-----END PGP SIGNATURE-----

```
