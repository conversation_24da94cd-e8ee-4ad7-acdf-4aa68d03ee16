---
date: '2012-09-18T01:25:53.000Z'
category: release
title: Version 0.9.2 (Unstable)
layout: blog-post
author: The Node.js Project
---

2012.09.17, Version 0.9.2 (Unstable)

- http_parser: upgrade to ad3b631

- openssl: upgrade 1.0.1c

- <PERSON><PERSON><PERSON>: use FSEvents to watch directory changes (<PERSON>or <PERSON>du<PERSON>)

- unix: support missing API on NetBSD (Shi<PERSON><PERSON>)

- unix: fix EMFILE busy loop (<PERSON>)

- windows: un-break writable tty handles (<PERSON>)

- windows: map WSAESHUTDOWN to UV_EPIPE (<PERSON>)

- windows: make spawn with custom environment work again (<PERSON>)

- windows: map ERROR_DIRECTORY to UV_ENOENT (<PERSON>)

- tls, https: validate server certificate by default (<PERSON>)

- tls, https: throw exception on missing key/cert (<PERSON>)

- tls: async session storage (<PERSON>or Indutny)

- installer: don't install header files (<PERSON>)

- buffer: implement Buffer.prototype.toJSON() (<PERSON>)

- buffer: added support for writing NaN and Infinity (koichik)

- http: make http.ServerResponse emit 'end' (<PERSON>)

- build: ./configure --ninja (<PERSON>ord<PERSON>s, <PERSON> J <PERSON>ontaine)

- installer: fix --without-npm (Ben Noordhuis)

- cli: make -p equivalent to -pe (Ben Noordhuis)

- url: Go much faster by using Url class (isaacs)

Source Code: https://nodejs.org/dist/v0.9.2/node-v0.9.2.tar.gz

Macintosh Installer (Universal): https://nodejs.org/dist/v0.9.2/node-v0.9.2.pkg

Windows Installer: https://nodejs.org/dist/v0.9.2/node-v0.9.2-x86.msi

Windows x64 Installer: https://nodejs.org/dist/v0.9.2/x64/node-v0.9.2-x64.msi

Windows x64 Files: https://nodejs.org/dist/v0.9.2/x64/

Linux 32-bit Binary: https://nodejs.org/dist/v0.9.2/node-v0.9.2-linux-x86.tar.gz

Linux 64-bit Binary: https://nodejs.org/dist/v0.9.2/node-v0.9.2-linux-x64.tar.gz

Solaris 32-bit Binary: https://nodejs.org/dist/v0.9.2/node-v0.9.2-sunos-x86.tar.gz

Solaris 64-bit Binary: https://nodejs.org/dist/v0.9.2/node-v0.9.2-sunos-x64.tar.gz

Other release files: https://nodejs.org/dist/v0.9.2/

Website: https://nodejs.org/docs/v0.9.2/

Documentation: https://nodejs.org/docs/v0.9.2/api/

Shasums:

```
3d1bb82013cbefd199abedaf0fe91d18579939b5  node-v0.9.2-darwin-x64.tar.gz
0a8e43e13ae6ce13ee3c8bb281d9a26aacc3ba3c  node-v0.9.2-darwin-x86.tar.gz
c5c2708d796d0e37c5a401af39bfa17c9c3b67c0  node-v0.9.2-linux-x64.tar.gz
0d0a09b34b74f2c9f310f044cd142d0b80885c22  node-v0.9.2-linux-x86.tar.gz
0bcc9a23e601cd8bef42a2e250d918ccb03b3f5b  node-v0.9.2-sunos-x64.tar.gz
7827998ec5ca442f26f824f46b51f33d0c9bbbb7  node-v0.9.2-sunos-x86.tar.gz
ed1c7d8a966c786969e3b4f68081cc5b3c753a03  node-v0.9.2-x86.msi
e1a1270d3f2a398738a363c68e8f30fd47dd5eb6  node-v0.9.2.pkg
09dd5e2135ab67ee9544f2b6ce0c5ab0e95b02b8  node-v0.9.2.tar.gz
8bfd33550b0115f71aed15a0c114a13cd573e10b  node.exe
3484b1c61442bd2ca7e00d06c61ad7fa6f61acad  node.exp
37dcaa77234b63a3ce3cecdea27041b7565698cc  node.lib
52abfb1b09f6facb0cda7c2e27b1bb48f8e75404  node.pdb
c4c3f87c5dd7a0d36d682fb449cc6a8a245a4789  x64/node-v0.9.2-x64.msi
d60fb389deada9eb753948bedb84b2f797424a93  x64/node.exe
04190c31210b2bc7ee80679167920567c166db75  x64/node.exp
dfb5a231905da844279bf86984813440e152becf  x64/node.lib
d5c5e26395e5c714efd4855505dcf0a31c57e22e  x64/node.pdb
```
