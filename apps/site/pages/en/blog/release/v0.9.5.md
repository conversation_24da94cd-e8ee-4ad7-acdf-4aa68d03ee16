---
date: '2012-12-30T01:19:51.000Z'
category: release
title: Node v0.9.5 (Unstable)
layout: blog-post
author: The Node.js Project
---

2012.12.30, Version 0.9.5 (Unstable)

- assert: improve support for new execution contexts (lukebayes)

- domain: use camelCase instead of snake_case (isaacs)

- domain: Do not use uncaughtException handler (isaacs)

- fs: make 'end' work with ReadStream without 'start' (<PERSON>)

- https: optimize createConnection() (Ryunosuke SATO)

- buffer: speed up base64 encoding by 20% (<PERSON>)

- doc: Colorize API stabilitity index headers in docs (<PERSON>)

- net: socket.readyState corrections (bentaber)

- http: Performance enhancements for http under streams2 (isaacs)

- stream: fix to emit end event on http.ClientResponse (Shigeki Ohtsu)

- stream: fix event handler leak in readstream pipe and unpipe (<PERSON>)

- build: Support ./configure --tag switch (<PERSON><PERSON><PERSON>)

- repl: don't touch `require.cache` (<PERSON>)

- node: Emit 'exit' event when exiting for an uncaught exception (isaacs)

Source Code: https://nodejs.org/dist/v0.9.5/node-v0.9.5.tar.gz

Macintosh Installer (Universal): https://nodejs.org/dist/v0.9.5/node-v0.9.5.pkg

Windows Installer: https://nodejs.org/dist/v0.9.5/node-v0.9.5-x86.msi

Windows x64 Installer: https://nodejs.org/dist/v0.9.5/x64/node-v0.9.5-x64.msi

Windows x64 Files: https://nodejs.org/dist/v0.9.5/x64/

Linux 32-bit Binary: https://nodejs.org/dist/v0.9.5/node-v0.9.5-linux-x86.tar.gz

Linux 64-bit Binary: https://nodejs.org/dist/v0.9.5/node-v0.9.5-linux-x64.tar.gz

Solaris 32-bit Binary: https://nodejs.org/dist/v0.9.5/node-v0.9.5-sunos-x86.tar.gz

Solaris 64-bit Binary: https://nodejs.org/dist/v0.9.5/node-v0.9.5-sunos-x64.tar.gz

Other release files: https://nodejs.org/dist/v0.9.5/

Website: https://nodejs.org/docs/v0.9.5/

Documentation: https://nodejs.org/docs/v0.9.5/api/

Shasums:

```
d44535e086c3f0fe47152b9323189c18a488d995  node-v0.9.5-darwin-x64.tar.gz
b588332e38e3ddd0424b18d14a3c6c65c5cad6f6  node-v0.9.5-darwin-x86.tar.gz
17b07641b59bc56582e68db6bd52c1b41bb06a5e  node-v0.9.5-linux-x64.tar.gz
89656e3dc094a6fa4244a03ac85e9b08b7443a50  node-v0.9.5-linux-x86.tar.gz
bf84b80ea80b4cb20767d4b3f276979f0f3d8866  node-v0.9.5-sunos-x64.tar.gz
4f98560e55db63f323b6f75b22538d96b199892c  node-v0.9.5-sunos-x86.tar.gz
7ed38be34e5377f45e8906c4d887de262c1925e2  node-v0.9.5-x86.msi
a1f1322fcaa5535ae830f3242bcdd213388357cc  node-v0.9.5.tar.gz
94c22b744945e9bc8b3f9fd31991cb6b87f727ce  node.exe
e809779c8071abf05650fbb47e207140829dc62c  node.exp
cec09944297546185070c809a78931839e3c2695  node.lib
5a3a4f3de4b8bf4209ecc2e6d40418d6a7e8fcac  node.pdb
55c986c14a72081757002f8df43272b20f024c57  x64/node-v0.9.5-x64.msi
55514dd925a9b24cb3925f89facbec5efb2a99aa  x64/node.exe
920e2d3319eac8a3356023a1cb8c61f139f80398  x64/node.exp
e072006328dacce920e4951596e457e58e6310d0  x64/node.lib
633816d865155148129ca21399e411f1240b2cce  x64/node.pdb
```
