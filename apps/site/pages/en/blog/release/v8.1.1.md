---
date: '2017-06-13T22:33:22.986Z'
category: release
title: Node v8.1.1 (Current)
layout: blog-post
author: <PERSON>
---

### Notable changes

- **Child processes**

  - `stdout` and `stderr` are now available on the error output of a
    failed call to the `util.promisify()`ed version of
    `child_process.exec`.
    [[`d66d4fc94c`](https://github.com/nodejs/node/commit/d66d4fc94c)]
    [#13388](https://github.com/nodejs/node/pull/13388)

- **HTTP**

  - A regression that broke certain scenarios in which HTTP is used together
    with the `cluster` module has been fixed.
    [[`fff8a56d6f`](https://github.com/nodejs/node/commit/fff8a56d6f)]
    [#13578](https://github.com/nodejs/node/pull/13578)

- **HTTPS**

  - The `rejectUnauthorized` option now works properly for unix sockets.
    [[`c4cbd99d37`](https://github.com/nodejs/node/commit/c4cbd99d37)]
    [#13505](https://github.com/nodejs/node/pull/13505)

- **Readline**
  - A change that broke `npm init` and other code which uses `readline`
    multiple times on the same input stream is reverted.
    [[`0df6c0b5f0`](https://github.com/nodejs/node/commit/0df6c0b5f0)]
    [#13560](https://github.com/nodejs/node/pull/13560)

### Commits

- [[`61c73085ba`](https://github.com/nodejs/node/commit/61c73085ba)] - **async_hooks**: minor refactor to callback invocation (Anna Henningsen) [#13419](https://github.com/nodejs/node/pull/13419)
- [[`bf61d97742`](https://github.com/nodejs/node/commit/bf61d97742)] - **async_hooks**: make sure `.{en|dis}able() === this` (Anna Henningsen) [#13418](https://github.com/nodejs/node/pull/13418)
- [[`32c87ac6f3`](https://github.com/nodejs/node/commit/32c87ac6f3)] - **benchmark**: fix some RegExp nits (Vse Mozhet Byt) [#13551](https://github.com/nodejs/node/pull/13551)
- [[`b967b4cbc5`](https://github.com/nodejs/node/commit/b967b4cbc5)] - **build**: merge test suite groups (Refael Ackermann) [#13378](https://github.com/nodejs/node/pull/13378)
- [[`00d2f7c818`](https://github.com/nodejs/node/commit/00d2f7c818)] - **build,windows**: check for VS version and arch (Refael Ackermann) [#13485](https://github.com/nodejs/node/pull/13485)
- [[`d66d4fc94c`](https://github.com/nodejs/node/commit/d66d4fc94c)] - **child_process**: promisify includes stdio in error (Gil Tayar) [#13388](https://github.com/nodejs/node/pull/13388)
- [[`0ca4bd1e18`](https://github.com/nodejs/node/commit/0ca4bd1e18)] - **child_process**: reduce nextTick() usage (Brian White) [#13459](https://github.com/nodejs/node/pull/13459)
- [[`d1fa59fbb7`](https://github.com/nodejs/node/commit/d1fa59fbb7)] - **child_process**: simplify send() result handling (Brian White) [#13459](https://github.com/nodejs/node/pull/13459)
- [[`d51b1c2e6f`](https://github.com/nodejs/node/commit/d51b1c2e6f)] - **cluster, dns, repl, tls, util**: fix RegExp nits (Vse Mozhet Byt) [#13536](https://github.com/nodejs/node/pull/13536)
- [[`68c0518e48`](https://github.com/nodejs/node/commit/68c0518e48)] - **doc**: fix links and typos in fs.md (Vse Mozhet Byt) [#13573](https://github.com/nodejs/node/pull/13573)
- [[`70432f2111`](https://github.com/nodejs/node/commit/70432f2111)] - **doc**: fix incorrect fs.utimes() link (Justin Beckwith) [#13608](https://github.com/nodejs/node/pull/13608)
- [[`26d76307d5`](https://github.com/nodejs/node/commit/26d76307d5)] - **doc**: fs constants for Node \< v6.3.0 in fs.md (Anshul Guleria) [#12690](https://github.com/nodejs/node/pull/12690)
- [[`52f5e3f804`](https://github.com/nodejs/node/commit/52f5e3f804)] - **doc**: use HTTPS URL for suggested upstream remote (Nikolai Vavilov) [#13602](https://github.com/nodejs/node/pull/13602)
- [[`2c1133d5fe`](https://github.com/nodejs/node/commit/2c1133d5fe)] - **doc**: add readline.emitKeypressEvents note (Samuel Reed) [#9447](https://github.com/nodejs/node/pull/9447)
- [[`53ec50d971`](https://github.com/nodejs/node/commit/53ec50d971)] - **doc**: fix napi*create*\*\_error signatures in n-api (Jamen Marzonie) [#13544](https://github.com/nodejs/node/pull/13544)
- [[`98d7f25181`](https://github.com/nodejs/node/commit/98d7f25181)] - **doc**: fix out of date sections in n-api doc (Michael Dawson) [#13508](https://github.com/nodejs/node/pull/13508)
- [[`85cac4ed53`](https://github.com/nodejs/node/commit/85cac4ed53)] - **doc**: update new CTC members (Refael Ackermann) [#13534](https://github.com/nodejs/node/pull/13534)
- [[`8c5407d321`](https://github.com/nodejs/node/commit/8c5407d321)] - **doc**: corrects reference to tlsClientError (Tarun) [#13533](https://github.com/nodejs/node/pull/13533)
- [[`3d12e1b455`](https://github.com/nodejs/node/commit/3d12e1b455)] - **doc**: emphasize Collaborators in GOVERNANCE.md (Rich Trott) [#13423](https://github.com/nodejs/node/pull/13423)
- [[`a9be8fff58`](https://github.com/nodejs/node/commit/a9be8fff58)] - **doc**: minimal documentation for Emeritus status (Rich Trott) [#13421](https://github.com/nodejs/node/pull/13421)
- [[`2778256680`](https://github.com/nodejs/node/commit/2778256680)] - **doc**: remove note highlighting in GOVERNANCE doc (Rich Trott) [#13420](https://github.com/nodejs/node/pull/13420)
- [[`2cb6f2b281`](https://github.com/nodejs/node/commit/2cb6f2b281)] - **http**: fix timeout reset after keep-alive timeout (Alexey Orlenko) [#13549](https://github.com/nodejs/node/pull/13549)
- [[`fff8a56d6f`](https://github.com/nodejs/node/commit/fff8a56d6f)] - **http**: handle cases where socket.server is null (Luigi Pinca) [#13578](https://github.com/nodejs/node/pull/13578)
- [[`c4cbd99d37`](https://github.com/nodejs/node/commit/c4cbd99d37)] - **https**: support rejectUnauthorized for unix sockets (cjihrig) [#13505](https://github.com/nodejs/node/pull/13505)
- [[`6a696d15ff`](https://github.com/nodejs/node/commit/6a696d15ff)] - **inspector**: fix crash on exception (Nikolai Vavilov) [#13455](https://github.com/nodejs/node/pull/13455)
- [[`50e1f931a9`](https://github.com/nodejs/node/commit/50e1f931a9)] - **profiler**: declare missing `printErr` (Fedor Indutny) [#13590](https://github.com/nodejs/node/pull/13590)
- [[`0df6c0b5f0`](https://github.com/nodejs/node/commit/0df6c0b5f0)] - **_Revert_** "**readline**: clean up event listener in onNewListener" (Anna Henningsen) [#13560](https://github.com/nodejs/node/pull/13560)
- [[`a5f415fe83`](https://github.com/nodejs/node/commit/a5f415fe83)] - **src**: merge `fn_name` in NODE_SET_PROTOTYPE_METHOD (XadillaX) [#13547](https://github.com/nodejs/node/pull/13547)
- [[`4a96ed4896`](https://github.com/nodejs/node/commit/4a96ed4896)] - **src**: check whether inspector is doing io (Sam Roberts) [#13504](https://github.com/nodejs/node/pull/13504)
- [[`f134c9d147`](https://github.com/nodejs/node/commit/f134c9d147)] - **src**: correct indentation for X509ToObject (Daniel Bevenius) [#13543](https://github.com/nodejs/node/pull/13543)
- [[`dd158b096f`](https://github.com/nodejs/node/commit/dd158b096f)] - **src**: make IsConstructCall checks consistent (Daniel Bevenius) [#13473](https://github.com/nodejs/node/pull/13473)
- [[`bf065344cf`](https://github.com/nodejs/node/commit/bf065344cf)] - **stream**: ensure that instanceof fast-path is hit. (Benedikt Meurer) [#13403](https://github.com/nodejs/node/pull/13403)
- [[`e713482147`](https://github.com/nodejs/node/commit/e713482147)] - **test**: fix typo in test-cli-node-options.js (Vse Mozhet Byt) [#13558](https://github.com/nodejs/node/pull/13558)
- [[`4c5457fae5`](https://github.com/nodejs/node/commit/4c5457fae5)] - **test**: fix flaky test-http-client-get-url (Sebastian Plesciuc) [#13516](https://github.com/nodejs/node/pull/13516)
- [[`812e0b0fbf`](https://github.com/nodejs/node/commit/812e0b0fbf)] - **test**: refactor async-hooks test-callback-error (Rich Trott) [#13554](https://github.com/nodejs/node/pull/13554)
- [[`2ea529b797`](https://github.com/nodejs/node/commit/2ea529b797)] - **test**: add regression test for 13557 (Anna Henningsen) [#13560](https://github.com/nodejs/node/pull/13560)
- [[`4d27930faf`](https://github.com/nodejs/node/commit/4d27930faf)] - **test**: fix flaky test-tls-socket-close (Rich Trott) [#13529](https://github.com/nodejs/node/pull/13529)
- [[`3da56ac9fb`](https://github.com/nodejs/node/commit/3da56ac9fb)] - **test**: harden test-dgram-bind-shared-ports (Refael Ackermann) [#13100](https://github.com/nodejs/node/pull/13100)
- [[`f686f73465`](https://github.com/nodejs/node/commit/f686f73465)] - **test**: add coverage for AsyncResource constructor (Gergely Nemeth) [#13327](https://github.com/nodejs/node/pull/13327)
- [[`12036a1d73`](https://github.com/nodejs/node/commit/12036a1d73)] - **test**: exercise once() with varying arguments (cjihrig) [#13524](https://github.com/nodejs/node/pull/13524)
- [[`1f88cbd620`](https://github.com/nodejs/node/commit/1f88cbd620)] - **test**: refactor test-http-server-keep-alive-timeout (realwakka) [#13448](https://github.com/nodejs/node/pull/13448)
- [[`bdbeb33dcb`](https://github.com/nodejs/node/commit/bdbeb33dcb)] - **test**: add hijackStdout and hijackStderr (XadillaX) [#13439](https://github.com/nodejs/node/pull/13439)
- [[`1c7f9171c0`](https://github.com/nodejs/node/commit/1c7f9171c0)] - **test**: add coverage for napi_property_descriptor (Michael Dawson) [#13510](https://github.com/nodejs/node/pull/13510)
- [[`c8db0475e0`](https://github.com/nodejs/node/commit/c8db0475e0)] - **test**: refactor test-fs-read-\* (Rich Trott) [#13501](https://github.com/nodejs/node/pull/13501)
- [[`ad07c46b00`](https://github.com/nodejs/node/commit/ad07c46b00)] - **test**: refactor domain tests (Rich Trott) [#13480](https://github.com/nodejs/node/pull/13480)
- [[`fe5ea3feb0`](https://github.com/nodejs/node/commit/fe5ea3feb0)] - **test**: check callback not invoked on lookup error (Rich Trott) [#13456](https://github.com/nodejs/node/pull/13456)
- [[`216cb3f6e9`](https://github.com/nodejs/node/commit/216cb3f6e9)] - **test,benchmark**: stabilize child-process (Refael Ackermann) [#13457](https://github.com/nodejs/node/pull/13457)
- [[`a0f8faa3a4`](https://github.com/nodejs/node/commit/a0f8faa3a4)] - **v8**: fix debug builds on Windows (Bartosz Sosnowski) [#13634](https://github.com/nodejs/node/pull/13634)
- [[`38a1cfb5e6`](https://github.com/nodejs/node/commit/38a1cfb5e6)] - **v8**: add a js class for Serializer/Dserializer (Rajaram Gaunker) [#13541](https://github.com/nodejs/node/pull/13541)

Windows 32-bit Installer: https://nodejs.org/dist/v8.1.1/node-v8.1.1-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v8.1.1/node-v8.1.1-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v8.1.1/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v8.1.1/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v8.1.1/node-v8.1.1.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v8.1.1/node-v8.1.1-darwin-x64.tar.gz \
Linux 32-bit Binary: https://nodejs.org/dist/v8.1.1/node-v8.1.1-linux-x86.tar.xz \
Linux 64-bit Binary: https://nodejs.org/dist/v8.1.1/node-v8.1.1-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v8.1.1/node-v8.1.1-linux-ppc64le.tar.xz \
Linux PPC BE 64-bit Binary: https://nodejs.org/dist/v8.1.1/node-v8.1.1-linux-ppc64.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v8.1.1/node-v8.1.1-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v8.1.1/node-v8.1.1-aix-ppc64.tar.gz \
SmartOS 32-bit Binary: https://nodejs.org/dist/v8.1.1/node-v8.1.1-sunos-x86.tar.xz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v8.1.1/node-v8.1.1-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v8.1.1/node-v8.1.1-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v8.1.1/node-v8.1.1-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v8.1.1/node-v8.1.1-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v8.1.1/node-v8.1.1.tar.gz \
Other release files: https://nodejs.org/dist/v8.1.1/ \
Documentation: https://nodejs.org/docs/v8.1.1/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

3f39c72b597a5810e4b5a510feec11a78f943e4c06c73e160558f7e7614362c9  node-v8.1.1-aix-ppc64.tar.gz
2f67890a5a46564672cfd4522cc00c7ac04d307e6a942ae1ad38b6aee94c29e2  node-v8.1.1-darwin-x64.tar.gz
32c741801df7d2b3a3d16b9808c19bb1d1c0c639eea4f4bac64d111a607af5b0  node-v8.1.1-darwin-x64.tar.xz
a1f8cb4dbb6322f684ac802f9b27535f2456bca1a8ccf8f75bf9ba96d73f4556  node-v8.1.1-headers.tar.gz
f72707d83ff64036c96df83f9151b28a09f91994eda41830ad5ed24ab5abd083  node-v8.1.1-headers.tar.xz
69cc375ef84ea79736b491ff779e8ed07359cf27c943dcae41292094e0b0b79f  node-v8.1.1-linux-arm64.tar.gz
3971543f9d29f77ddb8f47a54e4b99422a822173599748ab7fcd9c35c8e25124  node-v8.1.1-linux-arm64.tar.xz
48a249314bed9f0c4837464696d171bba47c787eff0bcdb452d2b8e2bdbcfcb2  node-v8.1.1-linux-ppc64le.tar.gz
2986470436c07dd600f87a426e81425bd1c3d8f5ca8865eaf6ff0d844e1606ec  node-v8.1.1-linux-ppc64le.tar.xz
4f950720886667976ced945a46b169e8775fd6e108008184567bb648f46823a8  node-v8.1.1-linux-ppc64.tar.gz
f0f148f264427d59058ce57dd76c909ce10c5cf34b65ddad021ed488c5ac27b3  node-v8.1.1-linux-ppc64.tar.xz
4041a25373c5fe68a660800785ab914a6971e1caafd22dfb8869ecd01c338081  node-v8.1.1-linux-s390x.tar.gz
e972942b7b916456ce7339f6b7cbf0a5efffe486bc9a6e6fe853a046b4a3beda  node-v8.1.1-linux-s390x.tar.xz
7717495688c8e332b916cfc51fdb4773d468018ccd0b104ae524ae5050426d4d  node-v8.1.1-linux-x64.tar.gz
6a735e77bdd21c92fe85ea5f9f567d0d6930fa33e0e111946b17cdb7efefb8d5  node-v8.1.1-linux-x64.tar.xz
a1f6c650b9c2d69a1b4dfec4ea4ead39a62867c413c479cfe70fb3152206bea4  node-v8.1.1-linux-x86.tar.gz
2b912178ab071a7aff7862c0fc3eaafeb7580c15cf163d926cd1bc99990c696e  node-v8.1.1-linux-x86.tar.xz
9414408e6dfe230316c7c4ea35aa8ddca652f0165be5624113d9579102ad750c  node-v8.1.1.pkg
1f6f9d990ca82920e00ed221391013c87de86b56417d4871dd43424842a64267  node-v8.1.1-sunos-x64.tar.gz
6c649767625069fb6e8db488f33a48efdc568590503f374268b7c09d43df14c4  node-v8.1.1-sunos-x64.tar.xz
5a1ca06a07354bff17c618688ebc2950ed79236bff256d23df2952f181c94ece  node-v8.1.1-sunos-x86.tar.gz
de9fff4f6dc221d86cef6a221988e222c56f704b3ee03b30d6d742c6f123f866  node-v8.1.1-sunos-x86.tar.xz
4794bce3da8d94f07febebe0609106c4b50debf65695c4ecb0ad727a0f202cb5  node-v8.1.1.tar.gz
7b0d176dc4a1db37789e009825ba03d2e3f834227e5efd296167e7ef6b287847  node-v8.1.1.tar.xz
bd9b864368c035a90bc386e63a70abcceaaad20bdda568765ee02fa4e1360056  node-v8.1.1-win-x64.7z
459f3b62e58f4fac3b9b5e49694855f338f3dcf2fcf955299ee2a47f7687625a  node-v8.1.1-win-x64.zip
74d5b87313195aeee0613d479a6505363b6b31f8fd966fc0157554779460d705  node-v8.1.1-win-x86.7z
36c625dd804580ca0865e1ccb77a3368b9c09213e57c09ed5528534613beed57  node-v8.1.1-win-x86.zip
8f83ab727c26c78b520e80fd53d0bd66cfa35e6770528b05a4885ecee5aef779  node-v8.1.1-x64.msi
1cdb4faf6b06f24ad3be0347027e0b76abac2d36f690506b1ee4e991f1268a51  node-v8.1.1-x86.msi
1421bd1d15c123ed88fd48655307708c391c9a45d5d7e6f98ec196157c7ac0c4  win-x64/node.exe
2206985c8b40db70a49406ff702501fc7b007bfbca0a4c01568a7e5d6bf74ed0  win-x64/node.lib
ce97786fd9ae18e8717275eab6a35110793a538e23b150471c8231928b6b42c0  win-x64/node_pdb.7z
3c280b5c0c991501996c600b37f3add66954f6b8060b8e92383812d55ca5266e  win-x64/node_pdb.zip
67c6aea3bd26389df72521677772f83f68e8cb7fe40db04f071a0b6ed55adbca  win-x86/node.exe
fb7290712066afb3194820c17aa601c3add4e88515f563e0331ceed05ae4045f  win-x86/node.lib
2c7e4f2cb1dfad1171786f80c6417f14ca41e356690ee65bb8594928e89aa657  win-x86/node_pdb.7z
d1c100fb2422284f6c3187634eb60151cc5f0566a78b420e74db8a8e4a27c178  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQEcBAEBCAAGBQJZQGYtAAoJEHNBsVwHCHesWdcH/AovvxBvdDvWPbiFj3I7SBxa
azZmlK2TJLzctieOStVVfdOasO4o8u7g6E9HnkrrFl0z92HDEsBKQZqpDH/x07BM
qKKkxMlMeXloLtDd0BOdIGwSBK7ClqSe2BYv3zDOwo8et5wnChPKAm0IvJHAySZK
9rymYM/j2fJZRjbHRtvB0WQulSkwYDhxV7xlQylqXl+kQi52wfrbgbuhh+6wqJ4o
zM4xN7jDb4X/wjQ+HDCF9YY7XXVOaEzqJjHotbIS9eIPybvW1Kx9icQ0N7f/u2qu
EptaUUvw9F7OT57O3MravtvzJCQoSQLzXI17cu/PY2ruOqS3R6l9mxt9II2E3Bc=
=xsHI
-----END PGP SIGNATURE-----

```
