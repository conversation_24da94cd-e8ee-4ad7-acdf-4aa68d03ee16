---
date: '2022-03-16T02:38:10.174Z'
category: release
title: Node v16.14.1 (LTS)
layout: blog-post
author: <PERSON>
---

### Notable changes

- **doc**:
  - add release key for Bryan <PERSON> (<PERSON>) [#42102](https://github.com/nodejs/node/pull/42102)

### Commits

- \[[`2a24e763d5`](https://github.com/nodejs/node/commit/2a24e763d5)] - **async_hooks**: fix imports in context example (<PERSON><PERSON>) [#39229](https://github.com/nodejs/node/pull/39229)
- \[[`c4a296f59b`](https://github.com/nodejs/node/commit/c4a296f59b)] - **benchmark**: enable no-empty ESLint rule (<PERSON> Trott) [#41831](https://github.com/nodejs/node/pull/41831)
- \[[`abe2eb9fc0`](https://github.com/nodejs/node/commit/abe2eb9fc0)] - **benchmark**: avoid input param manipulation (<PERSON><PERSON><PERSON>) [#41741](https://github.com/nodejs/node/pull/41741)
- \[[`2c566a9830`](https://github.com/nodejs/node/commit/2c566a9830)] - **benchmark**: use Object.hasOwn() instead of hasOwnProperty() (Rich Trott) [#41769](https://github.com/nodejs/node/pull/41769)
- \[[`b77e72ab12`](https://github.com/nodejs/node/commit/b77e72ab12)] - **benchmark**: replace hasOwnProperty() with Object.hasOwn() (Rich Trott) [#41724](https://github.com/nodejs/node/pull/41724)
- \[[`ec72cb4019`](https://github.com/nodejs/node/commit/ec72cb4019)] - **benchmark**: remove unreachable code from crypto/hash-stream-creation (Rich Trott) [#41535](https://github.com/nodejs/node/pull/41535)
- \[[`14bb6f97f0`](https://github.com/nodejs/node/commit/14bb6f97f0)] - **buffer**: fix atob/btoa no-arg case (Benjamin Gruenbaum) [#41478](https://github.com/nodejs/node/pull/41478)
- \[[`79e2ab2a67`](https://github.com/nodejs/node/commit/79e2ab2a67)] - **build**: remove windows-2022 from v16.x actions (Danielle Adams) [#42299](https://github.com/nodejs/node/pull/42299)
- \[[`2893b4c85a`](https://github.com/nodejs/node/commit/2893b4c85a)] - **build**: check if python is a executable program (himself65) [#36696](https://github.com/nodejs/node/pull/36696)
- \[[`5e4fc04821`](https://github.com/nodejs/node/commit/5e4fc04821)] - **build**: enable zoslib installation on z/OS (alexcfyung) [#41493](https://github.com/nodejs/node/pull/41493)
- \[[`1e3c9ebaae`](https://github.com/nodejs/node/commit/1e3c9ebaae)] - **build**: fix libuv builds for android aarch64 (Darshan Sen) [#41555](https://github.com/nodejs/node/pull/41555)
- \[[`46f714f92a`](https://github.com/nodejs/node/commit/46f714f92a)] - **crypto**: check return code from EVP_DigestUpdate (Michael Dawson) [#41800](https://github.com/nodejs/node/pull/41800)
- \[[`33abbf9f21`](https://github.com/nodejs/node/commit/33abbf9f21)] - **crypto**: fix `webcrypto.subtle` signature (Antoine du Hamel) [#41761](https://github.com/nodejs/node/pull/41761)
- \[[`faceae486b`](https://github.com/nodejs/node/commit/faceae486b)] - **crypto**: revise variables for const use instead of let (Rich Trott) [#41614](https://github.com/nodejs/node/pull/41614)
- \[[`fe0f9dc611`](https://github.com/nodejs/node/commit/fe0f9dc611)] - **crypto**: remove wildcard options for checkEmail (Tobias Nießen) [#41599](https://github.com/nodejs/node/pull/41599)
- \[[`188c3ab918`](https://github.com/nodejs/node/commit/188c3ab918)] - **crypto**: adjust types for getRandomValues (LiviaMedeiros) [#41481](https://github.com/nodejs/node/pull/41481)
- \[[`1ef28f1a3d`](https://github.com/nodejs/node/commit/1ef28f1a3d)] - **crypto**: remove checkIP options argument (Tobias Nießen) [#41571](https://github.com/nodejs/node/pull/41571)
- \[[`74c0464572`](https://github.com/nodejs/node/commit/74c0464572)] - **deps**: upgrade npm to 8.5.0 (npm-robot) [#41925](https://github.com/nodejs/node/pull/41925)
- \[[`b5783288d1`](https://github.com/nodejs/node/commit/b5783288d1)] - **deps**: upgrade npm to 8.4.1 (npm-robot) [#41836](https://github.com/nodejs/node/pull/41836)
- \[[`2b7c4b4afe`](https://github.com/nodejs/node/commit/2b7c4b4afe)] - **deps**: upgrade npm to 8.3.2 (npm team) [#41621](https://github.com/nodejs/node/pull/41621)
- \[[`906247933c`](https://github.com/nodejs/node/commit/906247933c)] - **dgram**: remove unreachable connectState assign (Rongjian Zhang) [#38590](https://github.com/nodejs/node/pull/38590)
- \[[`330c1bc518`](https://github.com/nodejs/node/commit/330c1bc518)] - **doc**: add comments to empty blocks in worker_threads text (Rich Trott) [#41831](https://github.com/nodejs/node/pull/41831)
- \[[`125ed0c6b0`](https://github.com/nodejs/node/commit/125ed0c6b0)] - **doc**: remove empty block from console.timeEnd() example (Rich Trott) [#41831](https://github.com/nodejs/node/pull/41831)
- \[[`34d6f8e793`](https://github.com/nodejs/node/commit/34d6f8e793)] - **doc**: use the same case as the section heading (Mestery) [#41876](https://github.com/nodejs/node/pull/41876)
- \[[`fd28d252fa`](https://github.com/nodejs/node/commit/fd28d252fa)] - **doc**: use Oxford comma in crypto docs (Tobias Nießen) [#41875](https://github.com/nodejs/node/pull/41875)
- \[[`bf99ef8b57`](https://github.com/nodejs/node/commit/bf99ef8b57)] - **doc**: use sentence case in readme introduction (Mestery) [#41874](https://github.com/nodejs/node/pull/41874)
- \[[`b15d9c2cc6`](https://github.com/nodejs/node/commit/b15d9c2cc6)] - **doc**: add missing space before hyphen (Mestery) [#41873](https://github.com/nodejs/node/pull/41873)
- \[[`77685d5ab0`](https://github.com/nodejs/node/commit/77685d5ab0)] - **doc**: add stream pipelining note on Http usage (Rafael Silva) [#41796](https://github.com/nodejs/node/pull/41796)
- \[[`c7bae97755`](https://github.com/nodejs/node/commit/c7bae97755)] - **doc**: improve SSL_OP_PRIORITIZE_CHACHA description (Tobias Nießen) [#41866](https://github.com/nodejs/node/pull/41866)
- \[[`5cd38a4ff5`](https://github.com/nodejs/node/commit/5cd38a4ff5)] - **doc**: add missing commas in cluster docs (Tobias Nießen) [#41865](https://github.com/nodejs/node/pull/41865)
- \[[`cf6b5e0e33`](https://github.com/nodejs/node/commit/cf6b5e0e33)] - **doc**: add history information for Corepack (Antoine du Hamel) [#41813](https://github.com/nodejs/node/pull/41813)
- \[[`c742a1cc4d`](https://github.com/nodejs/node/commit/c742a1cc4d)] - **doc**: feature management proposal (Michael Dawson) [#41420](https://github.com/nodejs/node/pull/41420)
- \[[`3f000a2627`](https://github.com/nodejs/node/commit/3f000a2627)] - **doc**: add overhead hints for heap snapshot generation (Gerhard Stöbich) [#41822](https://github.com/nodejs/node/pull/41822)
- \[[`42c0a8353e`](https://github.com/nodejs/node/commit/42c0a8353e)] - **doc**: fix X509 CA acronym capitalization (Tobias Nießen) [#41841](https://github.com/nodejs/node/pull/41841)
- \[[`f5b0a3be12`](https://github.com/nodejs/node/commit/f5b0a3be12)] - **doc**: use sentence case for X509 error codes header (Tobias Nießen) [#41829](https://github.com/nodejs/node/pull/41829)
- \[[`20d8fd1a83`](https://github.com/nodejs/node/commit/20d8fd1a83)] - **doc**: add initial version of maintaining-http.md (Michael Dawson) [#41798](https://github.com/nodejs/node/pull/41798)
- \[[`077fcee008`](https://github.com/nodejs/node/commit/077fcee008)] - **doc**: add registry numbers for Electron 19 and 20 (Keeley Hammond) [#41814](https://github.com/nodejs/node/pull/41814)
- \[[`44b6927179`](https://github.com/nodejs/node/commit/44b6927179)] - **doc**: add note about resource type in async_hooks (Tony Gorez) [#41797](https://github.com/nodejs/node/pull/41797)
- \[[`1be701c9ca`](https://github.com/nodejs/node/commit/1be701c9ca)] - **doc**: use example.com for examples (Ateş Göral) [#41827](https://github.com/nodejs/node/pull/41827)
- \[[`4660c1fa7b`](https://github.com/nodejs/node/commit/4660c1fa7b)] - **doc**: align tls port types with net port types (Tobias Nießen) [#41799](https://github.com/nodejs/node/pull/41799)
- \[[`5cd8bdc4d7`](https://github.com/nodejs/node/commit/5cd8bdc4d7)] - **doc**: use UDPv4/UDPv6 consistently with TCPv4/TCPv6 (Tobias Nießen) [#41824](https://github.com/nodejs/node/pull/41824)
- \[[`3ef05a0216`](https://github.com/nodejs/node/commit/3ef05a0216)] - **doc**: improve wording surrounding TLS 1.3 ciphers (Tobias Nießen) [#41778](https://github.com/nodejs/node/pull/41778)
- \[[`51d955368e`](https://github.com/nodejs/node/commit/51d955368e)] - **doc**: add format-md step to release guide (Danielle Adams) [#41809](https://github.com/nodejs/node/pull/41809)
- \[[`8f00e5dcf7`](https://github.com/nodejs/node/commit/8f00e5dcf7)] - **doc**: add v16 changelog link to iojs changelog (Danielle Adams) [#41808](https://github.com/nodejs/node/pull/41808)
- \[[`4f194f3094`](https://github.com/nodejs/node/commit/4f194f3094)] - **doc**: add security-steward rotation information (Michael Dawson) [#41707](https://github.com/nodejs/node/pull/41707)
- \[[`14ea8fcba8`](https://github.com/nodejs/node/commit/14ea8fcba8)] - **doc**: use Object.hasOwn() in util doc (Rich Trott) [#41780](https://github.com/nodejs/node/pull/41780)
- \[[`9f77692491`](https://github.com/nodejs/node/commit/9f77692491)] - **doc**: remove section on "recent" ECDH changes (Tobias Nießen) [#41773](https://github.com/nodejs/node/pull/41773)
- \[[`211a3c4c4c`](https://github.com/nodejs/node/commit/211a3c4c4c)] - **doc**: clarify that import also uses main (Ben McCann) [#41720](https://github.com/nodejs/node/pull/41720)
- \[[`20d9c4a2c5`](https://github.com/nodejs/node/commit/20d9c4a2c5)] - **doc**: update modules.md wording (Tobias Hernstig) [#41728](https://github.com/nodejs/node/pull/41728)
- \[[`e209f53ba2`](https://github.com/nodejs/node/commit/e209f53ba2)] - **doc**: update Mesteery email (Mestery) [#41683](https://github.com/nodejs/node/pull/41683)
- \[[`db1ce43173`](https://github.com/nodejs/node/commit/db1ce43173)] - **doc**: avoid incomplete sentence in cluster docs (Tobias Nießen) [#41701](https://github.com/nodejs/node/pull/41701)
- \[[`ee79e53821`](https://github.com/nodejs/node/commit/ee79e53821)] - **doc**: fix typo in contributing guides (Yoshiki Kurihara) [#41723](https://github.com/nodejs/node/pull/41723)
- \[[`9616fd5913`](https://github.com/nodejs/node/commit/9616fd5913)] - **doc**: improve docs to give descriptive info for the platform property (Harshil jain) [#41650](https://github.com/nodejs/node/pull/41650)
- \[[`4d8ee8e3cd`](https://github.com/nodejs/node/commit/4d8ee8e3cd)] - **doc**: fix link to npm documentation (Antoine du Hamel) [#41712](https://github.com/nodejs/node/pull/41712)
- \[[`018ec32535`](https://github.com/nodejs/node/commit/018ec32535)] - **doc**: clarify treatment of non-string base in URL() (Rich Trott) [#41685](https://github.com/nodejs/node/pull/41685)
- \[[`92e6cf03fe`](https://github.com/nodejs/node/commit/92e6cf03fe)] - **doc**: fix typo in `technical-priorities.md` (Akhil Marsonya) [#41694](https://github.com/nodejs/node/pull/41694)
- \[[`071fef50e5`](https://github.com/nodejs/node/commit/071fef50e5)] - **doc**: remove unadvisable cluster example (Tobias Nießen) [#41668](https://github.com/nodejs/node/pull/41668)
- \[[`b63fb0ffb8`](https://github.com/nodejs/node/commit/b63fb0ffb8)] - **doc**: document flow for supporting type generation (Michael Dawson) [#41464](https://github.com/nodejs/node/pull/41464)
- \[[`364811aa8a`](https://github.com/nodejs/node/commit/364811aa8a)] - **doc**: clarify parameter for napi_get_cb_info (Michael Dawson) [#41635](https://github.com/nodejs/node/pull/41635)
- \[[`1bd286e978`](https://github.com/nodejs/node/commit/1bd286e978)] - **doc**: revise url.resolve() text (Rich Trott) [#41661](https://github.com/nodejs/node/pull/41661)
- \[[`59f95fe4dc`](https://github.com/nodejs/node/commit/59f95fe4dc)] - **doc**: clarify treatment of non-string argument to new URL() (Rich Trott) [#41658](https://github.com/nodejs/node/pull/41658)
- \[[`3e93cc392e`](https://github.com/nodejs/node/commit/3e93cc392e)] - **doc**: fix documentation for `MODULE_NOT_FOUND` and `ERR_MODULE_NOT_FOUND` (Antoine du Hamel) [#41645](https://github.com/nodejs/node/pull/41645)
- \[[`b9d1cb7f8a`](https://github.com/nodejs/node/commit/b9d1cb7f8a)] - **doc**: improve TLS/SSL introduction (Tobias Nießen) [#41649](https://github.com/nodejs/node/pull/41649)
- \[[`5d9c83e2e9`](https://github.com/nodejs/node/commit/5d9c83e2e9)] - **doc**: modernize and simplify cluster example (Tobias Nießen) [#41626](https://github.com/nodejs/node/pull/41626)
- \[[`d5efecd64d`](https://github.com/nodejs/node/commit/d5efecd64d)] - **doc**: simplify readline/stdin text (Rich Trott) [#41583](https://github.com/nodejs/node/pull/41583)
- \[[`931be52589`](https://github.com/nodejs/node/commit/931be52589)] - **doc**: suggest worker threads in cluster docs (Tobias Nießen) [#41616](https://github.com/nodejs/node/pull/41616)
- \[[`b2a4614a0d`](https://github.com/nodejs/node/commit/b2a4614a0d)] - **doc**: add 16 and 17 to previous versions (Antoine du Hamel) [#41646](https://github.com/nodejs/node/pull/41646)
- \[[`5f0a017a02`](https://github.com/nodejs/node/commit/5f0a017a02)] - **doc**: improve `'hex'` Buffer decoding description and examples (Giora Guttsait) [#41598](https://github.com/nodejs/node/pull/41598)
- \[[`0805068add`](https://github.com/nodejs/node/commit/0805068add)] - **doc**: add note for handling signal events in trace events (Gabriel Trujillo) [#41438](https://github.com/nodejs/node/pull/41438)
- \[[`0388b9afc3`](https://github.com/nodejs/node/commit/0388b9afc3)] - **doc**: demonstrate dangers of `buffer.slice()` (Shalvah) [#41628](https://github.com/nodejs/node/pull/41628)
- \[[`3cdd1d634b`](https://github.com/nodejs/node/commit/3cdd1d634b)] - **doc**: add missing word in cluster.workers details (Tobias Nießen) [#41624](https://github.com/nodejs/node/pull/41624)
- \[[`5d94bc676e`](https://github.com/nodejs/node/commit/5d94bc676e)] - **doc**: fix async_hooks example in api docs (Akhil Marsonya) [#41609](https://github.com/nodejs/node/pull/41609)
- \[[`39f52e1130`](https://github.com/nodejs/node/commit/39f52e1130)] - **doc**: fix deprecated alias description in cluster (Tobias Nießen) [#41618](https://github.com/nodejs/node/pull/41618)
- \[[`55714cc777`](https://github.com/nodejs/node/commit/55714cc777)] - **doc**: update timingSafeEqual error case (Alex Agranov) [#41507](https://github.com/nodejs/node/pull/41507)
- \[[`9f8e442dc7`](https://github.com/nodejs/node/commit/9f8e442dc7)] - **doc**: simplify util.TextDecoder example (Rich Trott) [#41574](https://github.com/nodejs/node/pull/41574)
- \[[`57dc5956b1`](https://github.com/nodejs/node/commit/57dc5956b1)] - **doc**: move Mesteery to collaborators (Tobias Nießen) [#41597](https://github.com/nodejs/node/pull/41597)
- \[[`10320c2965`](https://github.com/nodejs/node/commit/10320c2965)] - **doc**: fix cjs example code for process.arch (Job) [#41593](https://github.com/nodejs/node/pull/41593)
- \[[`f33e831fe3`](https://github.com/nodejs/node/commit/f33e831fe3)] - **doc**: remove redunant `await` calls from stream docs (Giora Guttsait) [#41592](https://github.com/nodejs/node/pull/41592)
- \[[`1cf74beb57`](https://github.com/nodejs/node/commit/1cf74beb57)] - **doc**: make contributing info more discoverable (Michael Dawson) [#41408](https://github.com/nodejs/node/pull/41408)
- \[[`214cf17db9`](https://github.com/nodejs/node/commit/214cf17db9)] - **doc**: recommend package exports instead of requiring folders (Antoine du Hamel) [#41381](https://github.com/nodejs/node/pull/41381)
- \[[`5c387a0d75`](https://github.com/nodejs/node/commit/5c387a0d75)] - **doc**: edit async_context context loss text (Rich Trott) [#41550](https://github.com/nodejs/node/pull/41550)
- \[[`01283f6b25`](https://github.com/nodejs/node/commit/01283f6b25)] - **doc**: use sentence case for Web Crypto headers (Tobias Nießen) [#41577](https://github.com/nodejs/node/pull/41577)
- \[[`6b6d0c4914`](https://github.com/nodejs/node/commit/6b6d0c4914)] - **doc**: make Web Crypto example spec compliant (Tobias Nießen) [#41556](https://github.com/nodejs/node/pull/41556)
- \[[`8772d332d7`](https://github.com/nodejs/node/commit/8772d332d7)] - **doc**: do not reference SSL when discussing SNI (Tobias Nießen) [#41549](https://github.com/nodejs/node/pull/41549)
- \[[`82042d0094`](https://github.com/nodejs/node/commit/82042d0094)] - **doc**: fix typos in esm.md (Yu) [#41499](https://github.com/nodejs/node/pull/41499)
- \[[`ff0069dc3e`](https://github.com/nodejs/node/commit/ff0069dc3e)] - **doc**: adjust assignment in condition in stream doc (Rich Trott) [#41510](https://github.com/nodejs/node/pull/41510)
- \[[`1128b1c216`](https://github.com/nodejs/node/commit/1128b1c216)] - **doc**: improve Web Crypto headings related to ECC (Tobias Nießen) [#41542](https://github.com/nodejs/node/pull/41542)
- \[[`a6758d12e3`](https://github.com/nodejs/node/commit/a6758d12e3)] - **doc**: clarify module system selection (Antoine du Hamel) [#41383](https://github.com/nodejs/node/pull/41383)
- \[[`db17a529a8`](https://github.com/nodejs/node/commit/db17a529a8)] - **doc**: add release key for Bryan English (Bryan English) [#42102](https://github.com/nodejs/node/pull/42102)
- \[[`f2ca172a08`](https://github.com/nodejs/node/commit/f2ca172a08)] - **doc**: remove statement about (EC)DHE performance (Tobias Nießen) [#41528](https://github.com/nodejs/node/pull/41528)
- \[[`227dea8dc1`](https://github.com/nodejs/node/commit/227dea8dc1)] - **domain**: pass opts to `EventEmitter.init` (Chen Gang) [#41414](https://github.com/nodejs/node/pull/41414)
- \[[`bd717064b0`](https://github.com/nodejs/node/commit/bd717064b0)] - **esm**: improve validation of resolved URLs (Jacob Smith) [#41446](https://github.com/nodejs/node/pull/41446)
- \[[`e747ef5e45`](https://github.com/nodejs/node/commit/e747ef5e45)] - **http2**: fix pseudo-headers order (ofir) [#41735](https://github.com/nodejs/node/pull/41735)
- \[[`2efe9cbd01`](https://github.com/nodejs/node/commit/2efe9cbd01)] - **http2**: fix no response event on continue request (ofirbarak) [#41739](https://github.com/nodejs/node/pull/41739)
- \[[`7bf2be51b3`](https://github.com/nodejs/node/commit/7bf2be51b3)] - **http2**: fix memory leak on nghttp2 hd threshold (Rafael Silva) [#41502](https://github.com/nodejs/node/pull/41502)
- \[[`acd8768802`](https://github.com/nodejs/node/commit/acd8768802)] - **lib**: add comments to empty catch statements (Rich Trott) [#41831](https://github.com/nodejs/node/pull/41831)
- \[[`c90bb7cd93`](https://github.com/nodejs/node/commit/c90bb7cd93)] - **lib**: refactor to use `validateObject()` validator (Mohammed Keyvanzadeh) [#41845](https://github.com/nodejs/node/pull/41845)
- \[[`c93a9af82b`](https://github.com/nodejs/node/commit/c93a9af82b)] - **lib**: refactor source map stack trace prepare (Mohammed Keyvanzadeh) [#41698](https://github.com/nodejs/node/pull/41698)
- \[[`0f3287dc44`](https://github.com/nodejs/node/commit/0f3287dc44)] - **lib**: fix consistency of methods that emit warnings (Yoshiki Kurihara) [#41249](https://github.com/nodejs/node/pull/41249)
- \[[`7ee3cdf60a`](https://github.com/nodejs/node/commit/7ee3cdf60a)] - **lib**: remove erroneous JSDoc entry (Rich Trott) [#41604](https://github.com/nodejs/node/pull/41604)
- \[[`70f6554403`](https://github.com/nodejs/node/commit/70f6554403)] - **meta**: update AUTHORS (Node.js GitHub Bot) [#41868](https://github.com/nodejs/node/pull/41868)
- \[[`a44a8ff767`](https://github.com/nodejs/node/commit/a44a8ff767)] - **meta**: update AUTHORS (Node.js GitHub Bot) [#41763](https://github.com/nodejs/node/pull/41763)
- \[[`ba0ba7c4b2`](https://github.com/nodejs/node/commit/ba0ba7c4b2)] - **meta**: update mailmap/AUTHORS info for existing collaborator (Rich Trott) [#41750](https://github.com/nodejs/node/pull/41750)
- \[[`30e3327b46`](https://github.com/nodejs/node/commit/30e3327b46)] - **meta**: adjust mailmap/AUTHORS to reflect README change (Rich Trott) [#41751](https://github.com/nodejs/node/pull/41751)
- \[[`6d268fd32e`](https://github.com/nodejs/node/commit/6d268fd32e)] - **meta**: update AUTHORS (Node.js GitHub Bot) [#41659](https://github.com/nodejs/node/pull/41659)
- \[[`18e6316bf1`](https://github.com/nodejs/node/commit/18e6316bf1)] - **meta**: update AUTHORS (Node.js GitHub Bot) [#41548](https://github.com/nodejs/node/pull/41548)
- \[[`e1e059a698`](https://github.com/nodejs/node/commit/e1e059a698)] - **perf_hooks**: remove useless calls in Histogram (Michael Dawson) [#41579](https://github.com/nodejs/node/pull/41579)
- \[[`08b3bd2fc5`](https://github.com/nodejs/node/commit/08b3bd2fc5)] - **policy**: revise manifest.js to avoid empty blocks (Rich Trott) [#41831](https://github.com/nodejs/node/pull/41831)
- \[[`33f3391a8f`](https://github.com/nodejs/node/commit/33f3391a8f)] - **policy**: check for null instead of falsy in loop (Rich Trott) [#41614](https://github.com/nodejs/node/pull/41614)
- \[[`b8b8e0bce6`](https://github.com/nodejs/node/commit/b8b8e0bce6)] - **policy**: replace entries with keys (Mohammed Keyvanzadeh) [#41482](https://github.com/nodejs/node/pull/41482)
- \[[`ee61bc74b7`](https://github.com/nodejs/node/commit/ee61bc74b7)] - **process**: unhandledRejection support more errors (Benjamin Gruenbaum) [#41682](https://github.com/nodejs/node/pull/41682)
- \[[`f066246729`](https://github.com/nodejs/node/commit/f066246729)] - **process**: check for null instead of falsy in while loop (Rich Trott) [#41614](https://github.com/nodejs/node/pull/41614)
- \[[`77cb604c0d`](https://github.com/nodejs/node/commit/77cb604c0d)] - **process**: use validateString validator (Mohammed Keyvanzadeh) [#41595](https://github.com/nodejs/node/pull/41595)
- \[[`76281f9a81`](https://github.com/nodejs/node/commit/76281f9a81)] - **process**: ignore asyncId 0 in exception handler (Anatoli Papirovski) [#41424](https://github.com/nodejs/node/pull/41424)
- \[[`dacffd3e9c`](https://github.com/nodejs/node/commit/dacffd3e9c)] - **repl**: check for precise values rather than falsy in loops (Rich Trott) [#41614](https://github.com/nodejs/node/pull/41614)
- \[[`5e595683ce`](https://github.com/nodejs/node/commit/5e595683ce)] - **src**: slightly simplify URLHost::ToString (Anna Henningsen) [#41747](https://github.com/nodejs/node/pull/41747)
- \[[`206c370d03`](https://github.com/nodejs/node/commit/206c370d03)] - **src**: slightly simplify V8CoverageConnection::GetFilename (Anna Henningsen) [#41748](https://github.com/nodejs/node/pull/41748)
- \[[`1cfc63ebe3`](https://github.com/nodejs/node/commit/1cfc63ebe3)] - **src**: fix typo in js_native_api_v8.cc (Caio Agiani) [#41764](https://github.com/nodejs/node/pull/41764)
- \[[`aebd82ea7c`](https://github.com/nodejs/node/commit/aebd82ea7c)] - **stream**: remove empty block (Rich Trott) [#41831](https://github.com/nodejs/node/pull/41831)
- \[[`46ed078607`](https://github.com/nodejs/node/commit/46ed078607)] - **stream**: resume stream on drain (Robert Nagy) [#41848](https://github.com/nodejs/node/pull/41848)
- \[[`363c760c85`](https://github.com/nodejs/node/commit/363c760c85)] - **stream**: check for null instead of falsy in loops (Rich Trott) [#41614](https://github.com/nodejs/node/pull/41614)
- \[[`4f4fec4b22`](https://github.com/nodejs/node/commit/4f4fec4b22)] - **stream**: rename unknown primordial (Mohammed Keyvanzadeh) [#40622](https://github.com/nodejs/node/pull/40622)
- \[[`1425e75093`](https://github.com/nodejs/node/commit/1425e75093)] - **stream**: avoid function call where possible (Rich Trott) [#41534](https://github.com/nodejs/node/pull/41534)
- \[[`ecb52636a4`](https://github.com/nodejs/node/commit/ecb52636a4)] - **test**: renew certificates for specific test (Luigi Pinca) [#42342](https://github.com/nodejs/node/pull/42342)
- \[[`c8e59cbf9e`](https://github.com/nodejs/node/commit/c8e59cbf9e)] - **test**: enable no-empty ESLint rule (Rich Trott) [#41831](https://github.com/nodejs/node/pull/41831)
- \[[`20ec77688f`](https://github.com/nodejs/node/commit/20ec77688f)] - **test**: remove eslint-disable comments from fixtures (Rich Trott) [#41859](https://github.com/nodejs/node/pull/41859)
- \[[`a8e41837cc`](https://github.com/nodejs/node/commit/a8e41837cc)] - **test**: remove test-worker-memory flaky designation (Rich Trott) [#41867](https://github.com/nodejs/node/pull/41867)
- \[[`673c1fd5ae`](https://github.com/nodejs/node/commit/673c1fd5ae)] - **test**: avoid using Object.prototype methods directly on objects (Rich Trott) [#41801](https://github.com/nodejs/node/pull/41801)
- \[[`3690d3402d`](https://github.com/nodejs/node/commit/3690d3402d)] - **test**: exclude ibm i tests until we resolve (Michael Dawson) [#41812](https://github.com/nodejs/node/pull/41812)
- \[[`1f65620543`](https://github.com/nodejs/node/commit/1f65620543)] - **test**: make worker-take-heapsnapshot non-flaky (Michael Dawson) [#41684](https://github.com/nodejs/node/pull/41684)
- \[[`badab79527`](https://github.com/nodejs/node/commit/badab79527)] - **test**: mark test-fs-rmdir-recursive flaky on win (Michael Dawson) [#41533](https://github.com/nodejs/node/pull/41533)
- \[[`951d299aee`](https://github.com/nodejs/node/commit/951d299aee)] - **test**: make fs watch test more stable (Benjamin Gruenbaum) [#41715](https://github.com/nodejs/node/pull/41715)
- \[[`acea61ba8e`](https://github.com/nodejs/node/commit/acea61ba8e)] - **test**: fix typo in MessageChannel test (Tobias Nießen) [#41746](https://github.com/nodejs/node/pull/41746)
- \[[`081989b6b7`](https://github.com/nodejs/node/commit/081989b6b7)] - **test**: replace commented out expectations with tests (Darshan Sen) [#41667](https://github.com/nodejs/node/pull/41667)
- \[[`639130e635`](https://github.com/nodejs/node/commit/639130e635)] - **test**: use Object.hasOwn() where applicable (Rich Trott) [#41664](https://github.com/nodejs/node/pull/41664)
- \[[`cb362a3748`](https://github.com/nodejs/node/commit/cb362a3748)] - **test**: remove unneeded test statement (Rich Trott) [#41663](https://github.com/nodejs/node/pull/41663)
- \[[`2b87f9784f`](https://github.com/nodejs/node/commit/2b87f9784f)] - **test**: remove error allowance in debugger test (Jithil P Ponnan) [#41640](https://github.com/nodejs/node/pull/41640)
- \[[`55fce66af6`](https://github.com/nodejs/node/commit/55fce66af6)] - **test**: simplify test-gc-http-client (Luigi Pinca) [#41620](https://github.com/nodejs/node/pull/41620)
- \[[`b06c33b14b`](https://github.com/nodejs/node/commit/b06c33b14b)] - **test**: prepare tests for no-cond-assign ESLint rule (Rich Trott) [#41614](https://github.com/nodejs/node/pull/41614)
- \[[`950648db48`](https://github.com/nodejs/node/commit/950648db48)] - **test**: move test-gc-http-client-onerror to sequential (Luigi Pinca) [#41619](https://github.com/nodejs/node/pull/41619)
- \[[`1d3ef115ca`](https://github.com/nodejs/node/commit/1d3ef115ca)] - **test**: improve test coverage of internal/worker/io (Yoshiki Kurihara) [#41511](https://github.com/nodejs/node/pull/41511)
- \[[`122eb51c98`](https://github.com/nodejs/node/commit/122eb51c98)] - **test**: add DataView test entry for whatwg (Mohammed Keyvanzadeh) [#40622](https://github.com/nodejs/node/pull/40622)
- \[[`2c813d825f`](https://github.com/nodejs/node/commit/2c813d825f)] - **test**: improve util-format code coverage (Rich Trott) [#41572](https://github.com/nodejs/node/pull/41572)
- \[[`fab831a3fe`](https://github.com/nodejs/node/commit/fab831a3fe)] - **test**: fix typo in test_js_native_api_v8 (Tobias Nießen) [#41584](https://github.com/nodejs/node/pull/41584)
- \[[`9e7cfbbcd9`](https://github.com/nodejs/node/commit/9e7cfbbcd9)] - **test**: add missing await in fs-rm/fs-rmdir tests (Benjamin Coe) [#41545](https://github.com/nodejs/node/pull/41545)
- \[[`a8558ecfcf`](https://github.com/nodejs/node/commit/a8558ecfcf)] - **test**: add coverage for util.inspect() (Rich Trott) [#41527](https://github.com/nodejs/node/pull/41527)
- \[[`23fc205586`](https://github.com/nodejs/node/commit/23fc205586)] - **test**: avoid deep comparisons with literals (Tobias Nießen) [#40634](https://github.com/nodejs/node/pull/40634)
- \[[`63a67f8dad`](https://github.com/nodejs/node/commit/63a67f8dad)] - **timers**: check for nullish instead of falsy in loops (Rich Trott) [#41614](https://github.com/nodejs/node/pull/41614)
- \[[`788e77cb37`](https://github.com/nodejs/node/commit/788e77cb37)] - **tools**: enable no-empty ESLint rule (Rich Trott) [#41831](https://github.com/nodejs/node/pull/41831)
- \[[`10e6c70d14`](https://github.com/nodejs/node/commit/10e6c70d14)] - **tools**: update lint-md-dependencies to rollup\@2.67.0 (Node.js GitHub Bot) [#41737](https://github.com/nodejs/node/pull/41737)
- \[[`20cdf78fd8`](https://github.com/nodejs/node/commit/20cdf78fd8)] - **tools**: update doc to rehype-stringify\@9.0.3 (Node.js GitHub Bot) [#41854](https://github.com/nodejs/node/pull/41854)
- \[[`2eabfdd066`](https://github.com/nodejs/node/commit/2eabfdd066)] - **tools**: update eslint to 8.8.0 (Node.js GitHub Bot) [#41738](https://github.com/nodejs/node/pull/41738)
- \[[`9d23a27268`](https://github.com/nodejs/node/commit/9d23a27268)] - **tools**: use Set instead of { \[key]: true } object (Tobias Nießen) [#41695](https://github.com/nodejs/node/pull/41695)
- \[[`7e4d455fe4`](https://github.com/nodejs/node/commit/7e4d455fe4)] - **tools**: add compile_commands to ignore file (Yash Ladha) [#41580](https://github.com/nodejs/node/pull/41580)
- \[[`1cbdc984fb`](https://github.com/nodejs/node/commit/1cbdc984fb)] - **tools**: use Set instead of { \[key]: true } object (Tobias Nießen) [#41675](https://github.com/nodejs/node/pull/41675)
- \[[`dc854c4f38`](https://github.com/nodejs/node/commit/dc854c4f38)] - **tools**: fix typo in `tools/code_cache/README.md` (Tobias Nießen) [#41657](https://github.com/nodejs/node/pull/41657)
- \[[`b17aa25f12`](https://github.com/nodejs/node/commit/b17aa25f12)] - **tools**: enable no-cond-assign-ESLint rule (Rich Trott) [#41614](https://github.com/nodejs/node/pull/41614)
- \[[`9601b8ddd6`](https://github.com/nodejs/node/commit/9601b8ddd6)] - **tools**: update lint-md-dependencies to rollup\@2.65.0 (Node.js GitHub Bot) [#41638](https://github.com/nodejs/node/pull/41638)
- \[[`cdbe291e5b`](https://github.com/nodejs/node/commit/cdbe291e5b)] - **tools**: increase maximum line length to 120 characters (Rich Trott) [#41586](https://github.com/nodejs/node/pull/41586)
- \[[`7cbc472ed5`](https://github.com/nodejs/node/commit/7cbc472ed5)] - **tools**: add missing `.PHONY` and `.NOTPARALLEL` targets in `Makefile` (Antoine du Hamel) [#41515](https://github.com/nodejs/node/pull/41515)
- \[[`6fccd66b34`](https://github.com/nodejs/node/commit/6fccd66b34)] - **tools**: update lint-md-dependencies (Node.js GitHub Bot) [#41440](https://github.com/nodejs/node/pull/41440)
- \[[`3163bd1ea0`](https://github.com/nodejs/node/commit/3163bd1ea0)] - **tools**: bump eslint from 8.6.0 to 8.7.0 (Rich Trott) [#41570](https://github.com/nodejs/node/pull/41570)
- \[[`e439f32a4b`](https://github.com/nodejs/node/commit/e439f32a4b)] - **tools**: update doc to highlight.js\@11.4.0 to-vfile\@7.2.3 (Node.js GitHub Bot) [#41441](https://github.com/nodejs/node/pull/41441)
- \[[`66120564b2`](https://github.com/nodejs/node/commit/66120564b2)] - **tools,test**: enable no-prototype-builtins (Rich Trott) [#41801](https://github.com/nodejs/node/pull/41801)
- \[[`4aee98b03c`](https://github.com/nodejs/node/commit/4aee98b03c)] - **util**: use hasOwnProperty() primordial (Rich Trott) [#41692](https://github.com/nodejs/node/pull/41692)
- \[[`8218bab51d`](https://github.com/nodejs/node/commit/8218bab51d)] - **util**: remove unused fast path in internal debuglog (Rich Trott) [#41605](https://github.com/nodejs/node/pull/41605)
- \[[`a4ad26d4dc`](https://github.com/nodejs/node/commit/a4ad26d4dc)] - **util**: check for null instead of flasy in loop (Rich Trott) [#41614](https://github.com/nodejs/node/pull/41614)

Windows 32-bit Installer: https://nodejs.org/dist/v16.14.1/node-v16.14.1-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v16.14.1/node-v16.14.1-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v16.14.1/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v16.14.1/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v16.14.1/node-v16.14.1.pkg \
macOS Apple Silicon 64-bit Binary: https://nodejs.org/dist/v16.14.1/node-v16.14.1-darwin-arm64.tar.gz \
macOS Intel 64-bit Binary: https://nodejs.org/dist/v16.14.1/node-v16.14.1-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v16.14.1/node-v16.14.1-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v16.14.1/node-v16.14.1-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v16.14.1/node-v16.14.1-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v16.14.1/node-v16.14.1-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v16.14.1/node-v16.14.1-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v16.14.1/node-v16.14.1-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v16.14.1/node-v16.14.1.tar.gz \
Other release files: https://nodejs.org/dist/v16.14.1/ \
Documentation: https://nodejs.org/docs/v16.14.1/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

b7bb8e48e0d84cf63c0a024b0867f4a62ec5d4a48753015b60789ee70012c3c8  node-v16.14.1-aix-ppc64.tar.gz
8f6d45796f3d996484dcf53bb0e53cd019cd0ef7a1a247bd0178ebaa7e63a184  node-v16.14.1-darwin-arm64.tar.gz
5e0929ff5c45571a9c6d9c2671d758d85a903c7c7b8104a00e2284fff3a7d4ca  node-v16.14.1-darwin-arm64.tar.xz
af35abd727b051c8cdb8dcda9815ae93f96ef2c224d71f4ec52034a2ab5d8b61  node-v16.14.1-darwin-x64.tar.gz
dabd549c637b2d46c5e93482012972b40892e1aa537e7cf74e3308c1c7080b45  node-v16.14.1-darwin-x64.tar.xz
03a7262cb7ed8583bc344318557c69fd0c1acb8d816aaaef475f1dc5babe335a  node-v16.14.1-headers.tar.gz
90cff8d0f107ff359acd738c2e0b8641a1f1414b88ed1e61954270387101efdd  node-v16.14.1-headers.tar.xz
53aeda7118cd1991424b457907790033326f432ee6c2908a7693920124622cf4  node-v16.14.1-linux-arm64.tar.gz
2d45cdfd1c2e24f8b3d8e75df40a76d73cad9a56c82f637962d931a7c937cd31  node-v16.14.1-linux-arm64.tar.xz
4b88af3d7cb90e25b9f28394581d3f050be0717852226476d335e3073b2493a1  node-v16.14.1-linux-armv7l.tar.gz
467aba9eacad85e65836eec52d5a9f88e699c90395b64356d13b678fb5eee2a7  node-v16.14.1-linux-armv7l.tar.xz
e8e97f0161b0bc1aedcea12a36063974a9042f5eda5024980cd037f9984c4deb  node-v16.14.1-linux-ppc64le.tar.gz
56036c60f925193aa273bf993e0524e9b7dbbd654e77f67e55c3a81dc2484d19  node-v16.14.1-linux-ppc64le.tar.xz
23f913fee34bc7b4ef8e4a9e3c8014ca19c8da724d3c29cddd50bc621dc1d455  node-v16.14.1-linux-s390x.tar.gz
ebcad8a4c59c1454d08b6232a96ca0d27b70a83cc0a9d00f067bcdc038825568  node-v16.14.1-linux-s390x.tar.xz
8db3d6d8ecfc2af932320fb12449de2b5b76f946ac72b47c6a9074afe82737ff  node-v16.14.1-linux-x64.tar.gz
acaa44a1a224265b238732ce5255d91429b25654de5be70f84a382cc5b6301c2  node-v16.14.1-linux-x64.tar.xz
f52f86b3ea2014290ee645e82ca09f114d20ef519df584364e64e5ed0aa07d57  node-v16.14.1.pkg
87eb7a2424a1f7b7c1b52459700e93b9a70506a17027dfd9735f97dce3a45643  node-v16.14.1.tar.gz
e1687d16582134d9aef2b4e26e83ce3c253184e63061d5b5d334a11ddc95b763  node-v16.14.1.tar.xz
40e94c692fa3280bce47607787bc7563422c4efb03a0033c8ecbcedfe259a34d  node-v16.14.1-win-x64.7z
663b198c766f2ff60a34710c8e8f29f428f3920d8e268a26bed2591eb4020e2c  node-v16.14.1-win-x64.zip
95149b683a60eccb87558338a0ff4549bdedecbd72a2b3244d49fb3243ad7d9d  node-v16.14.1-win-x86.7z
1abdcfbe31a72f7757b6c7480a22a8e3bca257c721cae889ec46ddcd36e60e8f  node-v16.14.1-win-x86.zip
e14febf43b622c963b4d149057c4252548ab0807fb3d90f8734ab0a092b9a2b7  node-v16.14.1-x64.msi
d9056d91a98a89d94c6f467113845c0bb88c2483f561a345d0817873d4207d45  node-v16.14.1-x86.msi
15901541912bd8eb5b4b6b5db64fcf453915ea1fe931fc687582ed8ac4ac6ccc  win-x64/node.exe
4b083c771184555dcc23bca56986e0761cf58ebce6ea2b27a81a524a18d5d7ee  win-x64/node.lib
d4c6098b185d5b5afbd5904c93631098674976567f86cbde04e4813b14ad3e77  win-x64/node_pdb.7z
aadd47ae147c42c96f2308e2643d412a4e0dc4442c62214699c1a2a0741e04c5  win-x64/node_pdb.zip
1bd33aac5647371310a1ff11640174460faa537d82cddc8d8f5ea8471f30a5f4  win-x86/node.exe
838696e87e61ac96bedbf45331be7e22f4cf5ad1fec1aef75b8dbbd49efa82a2  win-x86/node.lib
02ac0efde6d71c39b1321d27df0b11093cc1054c4c1a5d6fd04c490f3ee7741a  win-x86/node_pdb.7z
24b54752b21b7dade5a4caeebc760221a820ba3c0622fb5cc0872e93c70fbc20  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEdPEmArbxxOkT+qN606iWE2Q7YgEFAmIxTQwACgkQ06iWE2Q7
YgHlNg/+Nhd2u9pNyxzmaz3dKQ2ATqoM5sQmFZQxCr6VAdszuZK2GALITHyIezLV
Ms9nhkaVsamIgMmCB61utlmtRGudZyy/DmyUwfDfveZb3GpKhLdPYvkMg4cSPphe
vl/nUUa4HJWOhD14ON7xSRDll+z2JO4iXYH9XLlC2fjI6t3uBvFu+85cOz4hvx4p
o/rU33S5hfiFuh05dRctI1sWgR2mgNSWY4k5qrDcfwgQXqcPvo+2owb05TZgmCOr
DmXW7/zpW/B4vTUYXimGRYbP3c0276KigiCTmRcYW9SMvxvBMcm0AH/VOZGmPKzm
N6YUEqe5EVcR6ZZl35UfKKZ/gOuW/ku3lVVB0qEO5EpDgjATpS/GCuFJnUChQ/2a
11IKmmm9btns1xw/fwMuJXjLBQY8tTgVh/cydG5UdEiJXoVWypQKNpYx3aDQcwqK
tCYKe0OX+i0i/++DrtDqjMSBLxS8fLajYpLo2Egdnc6Sc4/XfOW1EprojM2cWWsj
FvWjtVaujAshkDa/+DSqj4Qv/61FqWUnYCCgFjdE5Nfi1ttIHDgua8/xvVoQC/QN
ml/rZsNCRHfW1XmIuSJUsIXxWQQ9wud87IFtND+P4BsUz8tXQO8RV2bp+rNYM5e/
VxC4Qy4w+NlfHzovlFQi6aZTc4mBVmDtB9aeAl2uQW6dV48L+pw=
=VgPC
-----END PGP SIGNATURE-----

```
