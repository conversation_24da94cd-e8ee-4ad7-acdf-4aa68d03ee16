---
date: '2020-03-04T17:55:34.192Z'
category: release
title: Node v13.10.0 (Current)
layout: blog-post
author: <PERSON>
---

### Notable Changes

- **async_hooks**
  - introduce async-context API (v<PERSON><PERSON><PERSON><PERSON>) [#26540](https://github.com/nodejs/node/pull/26540)
- **stream**
  - support passing generator functions into pipeline() (<PERSON>) [#31223](https://github.com/nodejs/node/pull/31223)
- **tls**
  - expose SSL_export_keying_material (simon) [#31814](https://github.com/nodejs/node/pull/31814)
- **vm**
  - implement vm.measureMemory() for per-context memory measurement (<PERSON><PERSON>) [#31824](https://github.com/nodejs/node/pull/31824)

### Commits

- [[`f71fc9044a`](https://github.com/nodejs/node/commit/f71fc9044a)] - **async_hooks**: add store arg in AsyncLocalStorage (<PERSON><PERSON>) [#31930](https://github.com/nodejs/node/pull/31930)
- [[`6af9e7e0c3`](https://github.com/nodejs/node/commit/6af9e7e0c3)] - **async_hooks**: executionAsyncResource matches in hooks (Gerhard Stoebich) [#31821](https://github.com/nodejs/node/pull/31821)
- [[`877ab97286`](https://github.com/nodejs/node/commit/877ab97286)] - **(SEMVER-MINOR)** **async_hooks**: introduce async-context API (vdeturckheim) [#26540](https://github.com/nodejs/node/pull/26540)
- [[`9a41ced0d1`](https://github.com/nodejs/node/commit/9a41ced0d1)] - **build**: only lint markdown files that have changed (POSIX-only) (Rich Trott) [#31923](https://github.com/nodejs/node/pull/31923)
- [[`ca4407105e`](https://github.com/nodejs/node/commit/ca4407105e)] - **build**: add missing comma in node.gyp (cjihrig) [#31959](https://github.com/nodejs/node/pull/31959)
- [[`4dffd0437d`](https://github.com/nodejs/node/commit/4dffd0437d)] - **cli**: --perf-prof only works on Linux (Shelley Vohr) [#31892](https://github.com/nodejs/node/pull/31892)
- [[`4d05508aa8`](https://github.com/nodejs/node/commit/4d05508aa8)] - **crypto**: turn impossible DH errors into assertions (Tobias Nießen) [#31934](https://github.com/nodejs/node/pull/31934)
- [[`d0e94fc77e`](https://github.com/nodejs/node/commit/d0e94fc77e)] - **crypto**: fix ieee-p1363 for createVerify (Tobias Nießen) [#31876](https://github.com/nodejs/node/pull/31876)
- [[`fbaab7d854`](https://github.com/nodejs/node/commit/fbaab7d854)] - **deps**: openssl: cherry-pick 4dcb150ea30f (Adam Majer) [#32002](https://github.com/nodejs/node/pull/32002)
- [[`e6125cd53b`](https://github.com/nodejs/node/commit/e6125cd53b)] - **deps**: V8: backport f7771e5b0cc4 (Matheus Marchini) [#31957](https://github.com/nodejs/node/pull/31957)
- [[`c27f0d10c4`](https://github.com/nodejs/node/commit/c27f0d10c4)] - **deps**: update zlib to upstream d7f3ca9 (Sam Roberts) [#31800](https://github.com/nodejs/node/pull/31800)
- [[`b30a6981d3`](https://github.com/nodejs/node/commit/b30a6981d3)] - **deps**: move zlib maintenance info to guides (Sam Roberts) [#31800](https://github.com/nodejs/node/pull/31800)
- [[`cd30dbb0d6`](https://github.com/nodejs/node/commit/cd30dbb0d6)] - **doc**: revise --zero-fill-buffers text in buffer.md (Rich Trott) [#32019](https://github.com/nodejs/node/pull/32019)
- [[`166579f84b`](https://github.com/nodejs/node/commit/166579f84b)] - **doc**: add link to sem-ver info (unknown) [#31985](https://github.com/nodejs/node/pull/31985)
- [[`e3258fd148`](https://github.com/nodejs/node/commit/e3258fd148)] - **doc**: update zlib doc (James M Snell) [#31665](https://github.com/nodejs/node/pull/31665)
- [[`8516602ba0`](https://github.com/nodejs/node/commit/8516602ba0)] - **doc**: clarify http2.connect authority details (James M Snell) [#31828](https://github.com/nodejs/node/pull/31828)
- [[`c5acf0a13b`](https://github.com/nodejs/node/commit/c5acf0a13b)] - **doc**: updated YAML version representation in readline.md (Rich Trott) [#31924](https://github.com/nodejs/node/pull/31924)
- [[`4c6343fdea`](https://github.com/nodejs/node/commit/4c6343fdea)] - **doc**: describe how to update zlib (Sam Roberts) [#31800](https://github.com/nodejs/node/pull/31800)
- [[`a46839279f`](https://github.com/nodejs/node/commit/a46839279f)] - **doc**: update releases guide re pushing tags (Myles Borins) [#31855](https://github.com/nodejs/node/pull/31855)
- [[`15cc9b0126`](https://github.com/nodejs/node/commit/15cc9b0126)] - **doc**: update assert.rejects() docs with a validation function example (Eric Eastwood) [#31271](https://github.com/nodejs/node/pull/31271)
- [[`2046652b4e`](https://github.com/nodejs/node/commit/2046652b4e)] - **doc**: fix anchor for ERR_TLS_INVALID_CONTEXT (Tobias Nießen) [#31915](https://github.com/nodejs/node/pull/31915)
- [[`091b4bfe2d`](https://github.com/nodejs/node/commit/091b4bfe2d)] - **doc**: add note about ssh key to releases (Shelley Vohr) [#31856](https://github.com/nodejs/node/pull/31856)
- [[`3438937a37`](https://github.com/nodejs/node/commit/3438937a37)] - **doc**: fix notable changes for v13.9.0 (Shelley Vohr) [#31857](https://github.com/nodejs/node/pull/31857)
- [[`672f76d6bd`](https://github.com/nodejs/node/commit/672f76d6bd)] - **doc**: reword possessive form of Node.js in adding-new-napi-api.md (Rich Trott) [#31748](https://github.com/nodejs/node/pull/31748)
- [[`3eaf37767e`](https://github.com/nodejs/node/commit/3eaf37767e)] - **doc**: reword possessive form of Node.js in http.md (Rich Trott) [#31748](https://github.com/nodejs/node/pull/31748)
- [[`cb210e6b16`](https://github.com/nodejs/node/commit/cb210e6b16)] - **doc**: reword possessive form of Node.js in process.md (Rich Trott) [#31748](https://github.com/nodejs/node/pull/31748)
- [[`3969af43b4`](https://github.com/nodejs/node/commit/3969af43b4)] - **doc**: reword possessive form of Node.js in debugger.md (Rich Trott) [#31748](https://github.com/nodejs/node/pull/31748)
- [[`f9526057b3`](https://github.com/nodejs/node/commit/f9526057b3)] - **doc**: move gireeshpunathil to TSC emeritus (Gireesh Punathil) [#31770](https://github.com/nodejs/node/pull/31770)
- [[`b07175853f`](https://github.com/nodejs/node/commit/b07175853f)] - **doc**: pronouns for @Fishrock123 (Jeremiah Senkpiel) [#31725](https://github.com/nodejs/node/pull/31725)
- [[`7f4d6ee8ea`](https://github.com/nodejs/node/commit/7f4d6ee8ea)] - **doc**: move @Fishrock123 to TSC Emeriti (Jeremiah Senkpiel) [#31725](https://github.com/nodejs/node/pull/31725)
- [[`b177bba555`](https://github.com/nodejs/node/commit/b177bba555)] - **doc**: move @Fishrock123 to a previous releaser (Jeremiah Senkpiel) [#31725](https://github.com/nodejs/node/pull/31725)
- [[`9e4aad705f`](https://github.com/nodejs/node/commit/9e4aad705f)] - **doc**: fix typos in doc/api/https.md (Jeff) [#31793](https://github.com/nodejs/node/pull/31793)
- [[`eb2dce8342`](https://github.com/nodejs/node/commit/eb2dce8342)] - **doc**: claim ABI version 82 for Electron 10 (Samuel Attard) [#31778](https://github.com/nodejs/node/pull/31778)
- [[`db291aaf06`](https://github.com/nodejs/node/commit/db291aaf06)] - **doc**: guide - using valgrind to debug memory leaks (Michael Dawson) [#31501](https://github.com/nodejs/node/pull/31501)
- [[`aa16d80c05`](https://github.com/nodejs/node/commit/aa16d80c05)] - **doc,crypto**: re-document oaepLabel option (Ben Noordhuis) [#31825](https://github.com/nodejs/node/pull/31825)
- [[`9079bb42ea`](https://github.com/nodejs/node/commit/9079bb42ea)] - **http2**: make compat finished match http/1 (Robert Nagy) [#24347](https://github.com/nodejs/node/pull/24347)
- [[`3bd8feac0c`](https://github.com/nodejs/node/commit/3bd8feac0c)] - **meta**: move aqrln to emeritus (Rich Trott) [#31997](https://github.com/nodejs/node/pull/31997)
- [[`c801045fcd`](https://github.com/nodejs/node/commit/c801045fcd)] - **meta**: move jbergstroem to emeritus (Rich Trott) [#31996](https://github.com/nodejs/node/pull/31996)
- [[`ded3890bec`](https://github.com/nodejs/node/commit/ded3890bec)] - **meta**: move maclover7 to Emeritus (Rich Trott) [#31994](https://github.com/nodejs/node/pull/31994)
- [[`91ce69a554`](https://github.com/nodejs/node/commit/91ce69a554)] - **meta**: move Glen Keane to Collaborator Emeritus (Rich Trott) [#31993](https://github.com/nodejs/node/pull/31993)
- [[`b74c40eda6`](https://github.com/nodejs/node/commit/b74c40eda6)] - **meta**: move not-an-aardvark to emeritus (Rich Trott) [#31928](https://github.com/nodejs/node/pull/31928)
- [[`61a0d8b6cd`](https://github.com/nodejs/node/commit/61a0d8b6cd)] - **meta**: move julianduque to emeritus (Rich Trott) [#31863](https://github.com/nodejs/node/pull/31863)
- [[`94a471a422`](https://github.com/nodejs/node/commit/94a471a422)] - **meta**: move eljefedelrodeodeljefe to emeritus (Rich Trott) [#31735](https://github.com/nodejs/node/pull/31735)
- [[`9e3e6763fa`](https://github.com/nodejs/node/commit/9e3e6763fa)] - **module**: port source map sort logic from chromium (bcoe) [#31927](https://github.com/nodejs/node/pull/31927)
- [[`b9f3bfe6c8`](https://github.com/nodejs/node/commit/b9f3bfe6c8)] - **module**: disable conditional exports, self resolve warnings (Guy Bedford) [#31845](https://github.com/nodejs/node/pull/31845)
- [[`bbb6cc733c`](https://github.com/nodejs/node/commit/bbb6cc733c)] - **module**: package "exports" error refinements (Guy Bedford) [#31625](https://github.com/nodejs/node/pull/31625)
- [[`6adbfac9b0`](https://github.com/nodejs/node/commit/6adbfac9b0)] - **repl**: eager-evaluate input in parens (Shelley Vohr) [#31943](https://github.com/nodejs/node/pull/31943)
- [[`6a35b0d102`](https://github.com/nodejs/node/commit/6a35b0d102)] - **src**: don't run bootstrapper in CreateEnvironment (Shelley Vohr) [#31910](https://github.com/nodejs/node/pull/31910)
- [[`3497370d66`](https://github.com/nodejs/node/commit/3497370d66)] - **src**: move InternalCallbackScope to StartExecution (Shelley Vohr) [#31944](https://github.com/nodejs/node/pull/31944)
- [[`f62967c827`](https://github.com/nodejs/node/commit/f62967c827)] - **src**: enable `StreamPipe` for generic `StreamBase`s (Anna Henningsen) [#31869](https://github.com/nodejs/node/pull/31869)
- [[`776f379124`](https://github.com/nodejs/node/commit/776f379124)] - **src**: include large pages source unconditionally (Gabriel Schulhof) [#31904](https://github.com/nodejs/node/pull/31904)
- [[`9f68e14052`](https://github.com/nodejs/node/commit/9f68e14052)] - **src**: elevate v8 namespaces (Harshitha KP) [#31901](https://github.com/nodejs/node/pull/31901)
- [[`8fa6373e62`](https://github.com/nodejs/node/commit/8fa6373e62)] - **src**: allow unique_ptrs with custom deleter in memory tracker (Anna Henningsen) [#31870](https://github.com/nodejs/node/pull/31870)
- [[`88ccb444e3`](https://github.com/nodejs/node/commit/88ccb444e3)] - **src**: move BaseObject subclass dtors/ctors out of node_crypto.h (Anna Henningsen) [#31872](https://github.com/nodejs/node/pull/31872)
- [[`98d262e5f3`](https://github.com/nodejs/node/commit/98d262e5f3)] - **src**: inform callback scopes about exceptions in HTTP parser (Anna Henningsen) [#31801](https://github.com/nodejs/node/pull/31801)
- [[`57302f866e`](https://github.com/nodejs/node/commit/57302f866e)] - **src**: prefer 3-argument Array::New() (Anna Henningsen) [#31775](https://github.com/nodejs/node/pull/31775)
- [[`8a2b62e4cd`](https://github.com/nodejs/node/commit/8a2b62e4cd)] - **stream**: ensure pipeline always destroys streams (Robert Nagy) [#31940](https://github.com/nodejs/node/pull/31940)
- [[`313ecaabe5`](https://github.com/nodejs/node/commit/313ecaabe5)] - **stream**: fix broken pipeline error propagation (Robert Nagy) [#31835](https://github.com/nodejs/node/pull/31835)
- [[`8ad64b8e53`](https://github.com/nodejs/node/commit/8ad64b8e53)] - **(SEMVER-MINOR)** **stream**: support passing generator functions into pipeline() (Robert Nagy) [#31223](https://github.com/nodejs/node/pull/31223)
- [[`d0a00711f8`](https://github.com/nodejs/node/commit/d0a00711f8)] - **stream**: invoke buffered write callbacks on error (Robert Nagy) [#30596](https://github.com/nodejs/node/pull/30596)
- [[`1bca7b6c70`](https://github.com/nodejs/node/commit/1bca7b6c70)] - **test**: move test-inspector-module to parallel (Rich Trott) [#32025](https://github.com/nodejs/node/pull/32025)
- [[`932563473c`](https://github.com/nodejs/node/commit/932563473c)] - **test**: improve disable AsyncLocalStorage test (Andrey Pechkurov) [#31998](https://github.com/nodejs/node/pull/31998)
- [[`49864d161e`](https://github.com/nodejs/node/commit/49864d161e)] - **test**: fix flaky test-dns-any.js (Rich Trott) [#32017](https://github.com/nodejs/node/pull/32017)
- [[`38494746a6`](https://github.com/nodejs/node/commit/38494746a6)] - **test**: fix flaky test-gc-net-timeout (Robert Nagy) [#31918](https://github.com/nodejs/node/pull/31918)
- [[`b6d33f671a`](https://github.com/nodejs/node/commit/b6d33f671a)] - **test**: change test to not be sensitive to buffer send size (Rusty Conover) [#31499](https://github.com/nodejs/node/pull/31499)
- [[`cef5502055`](https://github.com/nodejs/node/commit/cef5502055)] - **test**: remove sequential/test-https-keep-alive-large-write.js (Rusty Conover) [#31499](https://github.com/nodejs/node/pull/31499)
- [[`f1e76488a7`](https://github.com/nodejs/node/commit/f1e76488a7)] - **test**: validate common property usage (Denys Otrishko) [#31933](https://github.com/nodejs/node/pull/31933)
- [[`ab8f060159`](https://github.com/nodejs/node/commit/ab8f060159)] - **test**: fix usage of invalid common properties (Denys Otrishko) [#31933](https://github.com/nodejs/node/pull/31933)
- [[`49c959d636`](https://github.com/nodejs/node/commit/49c959d636)] - **test**: increase timeout in vm-timeout-escape-queuemicrotask (Denys Otrishko) [#31966](https://github.com/nodejs/node/pull/31966)
- [[`04eda02d87`](https://github.com/nodejs/node/commit/04eda02d87)] - **test**: add documentation for common.enoughTestCpu (Rich Trott) [#31931](https://github.com/nodejs/node/pull/31931)
- [[`918c2b67cc`](https://github.com/nodejs/node/commit/918c2b67cc)] - **test**: fix typo in common/index.js (Rich Trott) [#31931](https://github.com/nodejs/node/pull/31931)
- [[`f89fb2751b`](https://github.com/nodejs/node/commit/f89fb2751b)] - **test**: mark empty udp tests flaky on OS X (Sam Roberts) [#31936](https://github.com/nodejs/node/pull/31936)
- [[`e08fef1fda`](https://github.com/nodejs/node/commit/e08fef1fda)] - **test**: add secp224k1 check in crypto-dh-stateless (Daniel Bevenius) [#31715](https://github.com/nodejs/node/pull/31715)
- [[`4fe9e043ef`](https://github.com/nodejs/node/commit/4fe9e043ef)] - **test**: remove common.PORT from assorted pummel tests (Rich Trott) [#31897](https://github.com/nodejs/node/pull/31897)
- [[`7d5776e119`](https://github.com/nodejs/node/commit/7d5776e119)] - **test**: remove flaky designation for test-net-connect-options-port (Rich Trott) [#31841](https://github.com/nodejs/node/pull/31841)
- [[`1933efa62f`](https://github.com/nodejs/node/commit/1933efa62f)] - **test**: remove common.PORT from test-net-write-callbacks.js (Rich Trott) [#31839](https://github.com/nodejs/node/pull/31839)
- [[`87e9014764`](https://github.com/nodejs/node/commit/87e9014764)] - **test**: remove common.PORT from test-net-pause (Rich Trott) [#31749](https://github.com/nodejs/node/pull/31749)
- [[`3fbd5ab265`](https://github.com/nodejs/node/commit/3fbd5ab265)] - **test**: remove common.PORT from test-tls-server-large-request (Rich Trott) [#31749](https://github.com/nodejs/node/pull/31749)
- [[`e76ac1d2c9`](https://github.com/nodejs/node/commit/e76ac1d2c9)] - **test**: remove common.PORT from test-net-throttle (Rich Trott) [#31749](https://github.com/nodejs/node/pull/31749)
- [[`724bf3105b`](https://github.com/nodejs/node/commit/724bf3105b)] - **test**: remove common.PORT from test-net-timeout (Rich Trott) [#31749](https://github.com/nodejs/node/pull/31749)
- [[`60c71dcad2`](https://github.com/nodejs/node/commit/60c71dcad2)] - **test**: add known issue test for sync writable callback (James M Snell) [#31756](https://github.com/nodejs/node/pull/31756)
- [[`2c0b249098`](https://github.com/nodejs/node/commit/2c0b249098)] - **tls**: reduce memory copying and number of BIO buffer allocations (Rusty Conover) [#31499](https://github.com/nodejs/node/pull/31499)
- [[`acb3aff674`](https://github.com/nodejs/node/commit/acb3aff674)] - **(SEMVER-MINOR)** **tls**: expose SSL_export_keying_material (simon) [#31814](https://github.com/nodejs/node/pull/31814)
- [[`f293dcf6de`](https://github.com/nodejs/node/commit/f293dcf6de)] - **tools**: add NODE_TEST_NO_INTERNET to the doc builder (Joyee Cheung) [#31849](https://github.com/nodejs/node/pull/31849)
- [[`79b1f04b15`](https://github.com/nodejs/node/commit/79b1f04b15)] - **tools**: sync gyp code base with node-gyp repo (Michaël Zasso) [#30563](https://github.com/nodejs/node/pull/30563)
- [[`f858f2366c`](https://github.com/nodejs/node/commit/f858f2366c)] - **tools**: update lint-md task to lint for possessives of Node.js (Rich Trott) [#31862](https://github.com/nodejs/node/pull/31862)
- [[`ae3929e958`](https://github.com/nodejs/node/commit/ae3929e958)] - **(SEMVER-MINOR)** **vm**: implement vm.measureMemory() for per-context memory measurement (Joyee Cheung) [#31824](https://github.com/nodejs/node/pull/31824)
- [[`a86cb0e480`](https://github.com/nodejs/node/commit/a86cb0e480)] - **vm**: lazily initialize primordials for vm contexts (Joyee Cheung) [#31738](https://github.com/nodejs/node/pull/31738)
- [[`f2389eba99`](https://github.com/nodejs/node/commit/f2389eba99)] - **worker**: emit runtime error on loop creation failure (Harshitha KP) [#31621](https://github.com/nodejs/node/pull/31621)
- [[`f87ac90849`](https://github.com/nodejs/node/commit/f87ac90849)] - **worker**: unroll file extension regexp (Anna Henningsen) [#31779](https://github.com/nodejs/node/pull/31779)

Windows 32-bit Installer: https://nodejs.org/dist/v13.10.0/node-v13.10.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v13.10.0/node-v13.10.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v13.10.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v13.10.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v13.10.0/node-v13.10.0.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v13.10.0/node-v13.10.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v13.10.0/node-v13.10.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v13.10.0/node-v13.10.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v13.10.0/node-v13.10.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v13.10.0/node-v13.10.0-aix-ppc64.tar.gz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v13.10.0/node-v13.10.0-sunos-x64.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v13.10.0/node-v13.10.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v13.10.0/node-v13.10.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v13.10.0/node-v13.10.0.tar.gz \
Other release files: https://nodejs.org/dist/v13.10.0/ \
Documentation: https://nodejs.org/docs/v13.10.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

a5037e8b3ace4ece087fdd89bf7a652b3e9dc8bda9579f2e1cbbcbd5d58b11c9  node-v13.10.0-aix-ppc64.tar.gz
67269fb9061402e446bb61776be2e5d0ec330b5274326df77979698d05f503da  node-v13.10.0-darwin-x64.tar.gz
8cf09b0ed78d9a9284639faff5692af9e2312ec1cc2dcb7524f9344769dbad47  node-v13.10.0-darwin-x64.tar.xz
f8e636f156aa70ff01db6a909b8c3aecfac4768b52b6e103490235fa542da854  node-v13.10.0-headers.tar.gz
4781228549e384de427ffbc7aa9d8e43cb84eb30f759c0513c4f0af192200f8e  node-v13.10.0-headers.tar.xz
48b18003d75abb10acee432a9bfa2de8bc8e2ec4c8a3cf08a69ff7f9c2afc1ea  node-v13.10.0-linux-arm64.tar.gz
adb145535d2e03fe508fa7a34897a130ba903d6f718a21cd29d1760e298f715b  node-v13.10.0-linux-arm64.tar.xz
75e17612b8b145248c8966affb5d594d8bb795b673c3339354c091e1bdba3b4b  node-v13.10.0-linux-armv7l.tar.gz
7df7bacb9c015be27b8bafc23614b48a754525c0b730fa32b03ff547dbec1032  node-v13.10.0-linux-armv7l.tar.xz
6e1cc0c441ca8cf9107e1de212977b2d6c04f9095bc8088652eb72e7f2b0ab7d  node-v13.10.0-linux-ppc64le.tar.gz
cea4e4c730281548e4e516db770eb590f076af076c9e4e17e672e1e8c211863a  node-v13.10.0-linux-ppc64le.tar.xz
341c9c77e100ca0367b809f0c2d8e67157a43ee5fdb1162602fd6f83f67929e7  node-v13.10.0-linux-s390x.tar.gz
f79c9d191126f6ffb314477a62c57b1929b1fdff65757841b4b718afa2154421  node-v13.10.0-linux-s390x.tar.xz
83cb0b1060830fd18b702462ccb6935d5346b33bfc1cad5fc59cb52686374e3a  node-v13.10.0-linux-x64.tar.gz
62081af005257d3db7ebd5a64b43f1a8e4a57bafd229be3acd7ce2704607eaac  node-v13.10.0-linux-x64.tar.xz
94ab255f3b8ed9462dced4f1ab46546758b1bd9a7f52566d56381eb28b9b0cc2  node-v13.10.0.pkg
eb7fe168294e7fb69fb239f710813a5449f522b3a73b226293518e7ed3caf8e6  node-v13.10.0-sunos-x64.tar.gz
3f6e5546e0c2a13c9cd17e15130e58ac3e3a75a84a1429b755f87d845bcd1557  node-v13.10.0-sunos-x64.tar.xz
b0497de9cae9b6e3a841a0d365bc90ddf9b39e868cb649bded72363c808f1bc7  node-v13.10.0.tar.gz
12d674cdaefdcdd3bee3e0476a46cd834649ad89fd2c7b1d38b9b61175d4a886  node-v13.10.0.tar.xz
d904d8b3882f4804a56410157f712611674eb33f5a2cb9d64f9d31db8bb23317  node-v13.10.0-win-x64.7z
d428b6d3e127716191fa6df630d03a25d3186fda1ede04a9a8d5c07e526dbb9e  node-v13.10.0-win-x64.zip
436657f0b1495288b846ff1da8bbfaff9a262d08098d8d7e3026a86556a88486  node-v13.10.0-win-x86.7z
8a2bcd89d11d57a39e66eca27e3ee247d8b4acfe64f7d1a1972587de8e05e80a  node-v13.10.0-win-x86.zip
7630def80db33e01913226dd1718de2cab3c937e51954d9e052d8598c575a6b9  node-v13.10.0-x64.msi
9aa6d6d80fd51c1e398a4c04b8755990647eecd63306a2e9884b38fe6bb12102  node-v13.10.0-x86.msi
c0980959696f99cf0ba9525636e673433349a9a90db28ad8f26ec3c4ab391085  win-x64/node.exe
1f21d29b063cd16bb401331f0f62efae7682a70147074ef0f316750241ed5cba  win-x64/node.lib
781039336c0bca7083fe0c7598b72e61574e3db3c307f7c96aba464b975a9085  win-x64/node_pdb.7z
03e44c1b4f6fe787b3693728b1d9bf59bc6e4c2237efd1352efe41f1c6b0e733  win-x64/node_pdb.zip
a878d63f5728deb22dd9231308ff157ac15007cd91f60229b462a4a7b8eb8f77  win-x86/node.exe
2d07645620eaa61500d5e06603a5dd0595321950b4c5fd9e8e3fd5bf82891483  win-x86/node.lib
64274f875cb8064f59d58faf64127b0f8ebcaeae677e9f4c206202a842c76049  win-x86/node_pdb.7z
282e15354efed41a28879ce6e79cddc28faa6c806a87092bdca4181dee0619bf  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEueL1mBqm4M0oFg2f8TmTp1WZZTwFAl5f610ACgkQ8TmTp1WZ
ZTz3+w//ZC9jWBi808jnq74uHSLDabi2mxIq5l7GGcpgQb2K64A8PI+smXDClJEi
6PZFWS5X3vuUvwB1zH5c5dZ8gTogGay6wWwPeHSfEFhMxAAZANxWvTVjRYUa4/UD
WwzwDe6HI5iXuDO9pYyyGZjKiW/5J4ihdqGdJG1Ntt8ssmBgymxtZIgpbukMVJ1Z
wOb9wvzQUqLrFqJHELG+Gh5h4VLCCwtpbI8G2EbeH/spKPHUYFr9LVtE9bPgec7e
sDmBElGumv/Y3ybo93ZsEiz9dE2FfpIw5ipEesugD/8pz91XsPCHrdRZCFRn/iIm
f5viHGgvW6zk60eMthnZ1zBzBeUnayFIfpWfcnyzGEyzCHNoC4DGAtLb+7RQ5Wvg
FEOCMNW1OcdEo3VdjYpCRA1ic8PdUTeyPEC/xxNUShEjIeZDCFTgdujNWHH+0hBl
pgwy9zhnj9nJbYEeBtX5kookz+3vsTluFRUTsLCr3+NJPsNS5eeAdJpfAld1a2Hx
d+LzYG6/VInaSkb+owRJk4v7YlpCNQyrpTbQuZM10XMU1OwPxLfKfmYZFNg+70ia
e5OlGt8I3HF+jYNyTAXboCVMMmR3d3XoJaLEbp7EUg6AbjsAzLgtCMoxvNFIPBiF
1lJSlgZGLO9KxwIdRntT2SSxO3VthEXbPxV43VCEJ3k06w01FkM=
=p3qq
-----END PGP SIGNATURE-----

```
