---
date: '2016-03-16T21:34:26.150Z'
category: release
title: Node v5.9.0 (Current)
layout: blog-post
author: <PERSON>
---

### Notable changes

- **contextify**: Fixed a memory consumption issue related to heavy use of `vm.createContext` and `vm.runInNewContext`. (<PERSON>)
  https://github.com/nodejs/node/pull/5392
- **governance**: The following members have been added as collaborators:
  - <PERSON> (@AndreasM<PERSON>en)
  - <PERSON> (@benjamingr)
  - <PERSON> (@claudiorodriguez)
  - <PERSON> (@thekemkid)
  - <PERSON> (@whitlockjc)
  - <PERSON> (@matthewloring)
  - <PERSON> (@phillipj)
- **lib**: copy arguments object instead of leaking it (<PERSON>)
  https://github.com/nodejs/node/pull/4361
- **src**: allow both -i and -e flags to be used at the same time (<PERSON>)
  https://github.com/nodejs/node/pull/5655
- **timers**: Internal Node.js timeouts now use the same logic path as those created with `setTimeout()` (<PERSON>) [#4007](https://github.com/nodejs/node/pull/4007)
  - This may cause a slightly different performance profile in some situations. So far, it has shown to be positive in most cases.
- **v8**: backport fb4ccae from v8 upstream (Vladimir Krivosheev) #4231
  - breakout events from v8 to offer better support for external debuggers
- **zlib**: add support for concatenated members (Kári Tristan Helgason)
  https://github.com/nodejs/node/pull/5120
  - Previously, if multiple members were in the same archive, only the first would be read. The others are no longer thrown away.

### Commits

- [[`03b99bf8b9`](https://github.com/nodejs/node/commit/03b99bf8b9)] - **build**: don't install github templates (Johan Bergström) [#5612](https://github.com/nodejs/node/pull/5612)
- [[`a7819da15a`](https://github.com/nodejs/node/commit/a7819da15a)] - **_Revert_** "**build**: run lint before tests" (Rich Trott) [#5602](https://github.com/nodejs/node/pull/5602)
- [[`5e9cac4333`](https://github.com/nodejs/node/commit/5e9cac4333)] - **console**: check that stderr is writable (Rich Trott) [#5635](https://github.com/nodejs/node/pull/5635)
- [[`0662fcf209`](https://github.com/nodejs/node/commit/0662fcf209)] - **contextify**: cache sandbox and context in locals (Ali Ijaz Sheikh) [#5392](https://github.com/nodejs/node/pull/5392)
- [[`4f2c839d46`](https://github.com/nodejs/node/commit/4f2c839d46)] - **contextify**: replace deprecated SetWeak usage (Ali Ijaz Sheikh) [#5392](https://github.com/nodejs/node/pull/5392)
- [[`bfff07b4dd`](https://github.com/nodejs/node/commit/bfff07b4dd)] - **contextify**: cleanup weak ref for sandbox (Ali Ijaz Sheikh) [#5392](https://github.com/nodejs/node/pull/5392)
- [[`93f60cdc54`](https://github.com/nodejs/node/commit/93f60cdc54)] - **contextify**: cleanup weak ref for global proxy (Ali Ijaz Sheikh) [#5392](https://github.com/nodejs/node/pull/5392)
- [[`b6c355de0d`](https://github.com/nodejs/node/commit/b6c355de0d)] - **(SEMVER-MINOR)** **deps**: backport fb4ccae from v8 upstream (develar) [#4231](https://github.com/nodejs/node/pull/4231)
- [[`29510aa4fd`](https://github.com/nodejs/node/commit/29510aa4fd)] - **deps**: update openssl config (Shigeki Ohtsu) [#5630](https://github.com/nodejs/node/pull/5630)
- [[`532d1bf9ce`](https://github.com/nodejs/node/commit/532d1bf9ce)] - **deps**: sync deps/http_parser with nodejs/http_parser (James M Snell) [#5600](https://github.com/nodejs/node/pull/5600)
- [[`d5d64c327b`](https://github.com/nodejs/node/commit/d5d64c327b)] - **doc**: clarify commit message rules (Wyatt Preul) [#5661](https://github.com/nodejs/node/pull/5661)
- [[`8c4c84fe5b`](https://github.com/nodejs/node/commit/8c4c84fe5b)] - **doc**: add Testing WG (Rich Trott) [#5461](https://github.com/nodejs/node/pull/5461)
- [[`434af03825`](https://github.com/nodejs/node/commit/434af03825)] - **doc**: Add note about use of JSON.stringify() (Mithun Patel) [#5723](https://github.com/nodejs/node/pull/5723)
- [[`62926d85bd`](https://github.com/nodejs/node/commit/62926d85bd)] - **doc**: clarify type of first argument in zlib (Kirill Fomichev) [#5685](https://github.com/nodejs/node/pull/5685)
- [[`eb73574349`](https://github.com/nodejs/node/commit/eb73574349)] - **doc**: clarify when writable.write callback is called (Kevin Locke) [#4810](https://github.com/nodejs/node/pull/4810)
- [[`c579507034`](https://github.com/nodejs/node/commit/c579507034)] - **doc**: fix typo in api/addons (Daijiro Wachi) [#5678](https://github.com/nodejs/node/pull/5678)
- [[`8e45c9d9ea`](https://github.com/nodejs/node/commit/8e45c9d9ea)] - **doc**: fix typo in api/dgram (Daijiro Wachi) [#5678](https://github.com/nodejs/node/pull/5678)
- [[`44a9b100c5`](https://github.com/nodejs/node/commit/44a9b100c5)] - **doc**: fix typo in api/fs (Daijiro Wachi) [#5678](https://github.com/nodejs/node/pull/5678)
- [[`b667573bcb`](https://github.com/nodejs/node/commit/b667573bcb)] - **doc**: update fansworld-claudio username on README (Claudio Rodriguez) [#5680](https://github.com/nodejs/node/pull/5680)
- [[`9794abb5d1`](https://github.com/nodejs/node/commit/9794abb5d1)] - **doc**: add onboarding resources (Jeremiah Senkpiel) [#3726](https://github.com/nodejs/node/pull/3726)
- [[`31e39fbd7a`](https://github.com/nodejs/node/commit/31e39fbd7a)] - **doc**: remove non-standard use of hyphens (Stefano Vozza)
- [[`f3e9daa825`](https://github.com/nodejs/node/commit/f3e9daa825)] - **doc**: add clarification on birthtime in fs stat (Kári Tristan Helgason) [#5479](https://github.com/nodejs/node/pull/5479)
- [[`c379ec6522`](https://github.com/nodejs/node/commit/c379ec6522)] - **doc**: move build instructions to a new document (Johan Bergström) [#5634](https://github.com/nodejs/node/pull/5634)
- [[`2a442b3dfc`](https://github.com/nodejs/node/commit/2a442b3dfc)] - **doc**: update removeListener behaviour (Vaibhav) [#5201](https://github.com/nodejs/node/pull/5201)
- [[`f6ee0996e0`](https://github.com/nodejs/node/commit/f6ee0996e0)] - **doc**: fix typo in child_process docs (Benjamin Gruenbaum) [#5681](https://github.com/nodejs/node/pull/5681)
- [[`dd12661173`](https://github.com/nodejs/node/commit/dd12661173)] - **doc**: include typo in 'unhandledRejection' example (Robert C Jensen) [#5654](https://github.com/nodejs/node/pull/5654)
- [[`f7aecd6e94`](https://github.com/nodejs/node/commit/f7aecd6e94)] - **doc**: add thekemkid to collaborators (Glen Keane) [#5667](https://github.com/nodejs/node/pull/5667)
- [[`b81711acfb`](https://github.com/nodejs/node/commit/b81711acfb)] - **doc**: add phillipj to collaborators (Phillip Johnsen) [#5663](https://github.com/nodejs/node/pull/5663)
- [[`a33f2486f0`](https://github.com/nodejs/node/commit/a33f2486f0)] - **doc**: add fansworld-claudio to collaborators (Claudio Rodriguez) [#5668](https://github.com/nodejs/node/pull/5668)
- [[`285d5e7ba6`](https://github.com/nodejs/node/commit/285d5e7ba6)] - **doc**: add AndreasMadsen to collaborators (Andreas Madsen) [#5666](https://github.com/nodejs/node/pull/5666)
- [[`8e1f6706e3`](https://github.com/nodejs/node/commit/8e1f6706e3)] - **doc**: add benjamingr to collaborator list (Benjamin Gruenbaum) [#5664](https://github.com/nodejs/node/pull/5664)
- [[`f7842cbb24`](https://github.com/nodejs/node/commit/f7842cbb24)] - **doc**: add whitlockjc to collaborators (Jeremy Whitlock) [#5665](https://github.com/nodejs/node/pull/5665)
- [[`dd6f4ec2e4`](https://github.com/nodejs/node/commit/dd6f4ec2e4)] - **doc**: add mattloring to collaborators (Matt Loring) [#5662](https://github.com/nodejs/node/pull/5662)
- [[`9ebd559a55`](https://github.com/nodejs/node/commit/9ebd559a55)] - **doc**: fix markdown links (Steve Mao) [#5641](https://github.com/nodejs/node/pull/5641)
- [[`62d267e1ff`](https://github.com/nodejs/node/commit/62d267e1ff)] - **doc**: fix dns.resolveCname description typo (axvm) [#5622](https://github.com/nodejs/node/pull/5622)
- [[`9f8e2e2979`](https://github.com/nodejs/node/commit/9f8e2e2979)] - **doc**: update release tweet template (Jeremiah Senkpiel) [#5628](https://github.com/nodejs/node/pull/5628)
- [[`4d6fe300fe`](https://github.com/nodejs/node/commit/4d6fe300fe)] - **doc**: fix v5.8.0 changelog heading (Jeremiah Senkpiel) [#5559](https://github.com/nodejs/node/pull/5559)
- [[`4c1fdaeb2a`](https://github.com/nodejs/node/commit/4c1fdaeb2a)] - **docs**: update link to iojs+release ci job (Myles Borins) [#5632](https://github.com/nodejs/node/pull/5632)
- [[`205bed0bec`](https://github.com/nodejs/node/commit/205bed0bec)] - **lib**: copy arguments object instead of leaking it (Nathan Woltman) [#4361](https://github.com/nodejs/node/pull/4361)
- [[`b16f67a0b9`](https://github.com/nodejs/node/commit/b16f67a0b9)] - **net**: make `isIPv4` and `isIPv6` more efficient (Vladimir Kurchatkin) [#5478](https://github.com/nodejs/node/pull/5478)
- [[`4ecd996baa`](https://github.com/nodejs/node/commit/4ecd996baa)] - **(SEMVER-MINOR)** **src**: allow combination of -i and -e cli flags (Rich Trott) [#5655](https://github.com/nodejs/node/pull/5655)
- [[`f225459496`](https://github.com/nodejs/node/commit/f225459496)] - **test**: improve test-npm-install (Santiago Gimeno) [#5613](https://github.com/nodejs/node/pull/5613)
- [[`cceae5ae78`](https://github.com/nodejs/node/commit/cceae5ae78)] - **test**: eval a strict function (Kári Tristan Helgason) [#5250](https://github.com/nodejs/node/pull/5250)
- [[`9a44c8c337`](https://github.com/nodejs/node/commit/9a44c8c337)] - **test**: add batch of known issue tests (cjihrig) [#5653](https://github.com/nodejs/node/pull/5653)
- [[`1b7b1ed2c9`](https://github.com/nodejs/node/commit/1b7b1ed2c9)] - **timers**: greatly improve code comments (Jeremiah Senkpiel) [#4007](https://github.com/nodejs/node/pull/4007)
- [[`769254b0ba`](https://github.com/nodejs/node/commit/769254b0ba)] - **timers**: refactor timers (Jeremiah Senkpiel) [#4007](https://github.com/nodejs/node/pull/4007)
- [[`0b545fb3f8`](https://github.com/nodejs/node/commit/0b545fb3f8)] - **win,build**: support Visual C++ Build Tools 2015 (João Reis) [#5627](https://github.com/nodejs/node/pull/5627)
- [[`ef774ff9a8`](https://github.com/nodejs/node/commit/ef774ff9a8)] - **(SEMVER-MINOR)** **zlib**: add support for concatenated members (Kári Tristan Helgason) [#5120](https://github.com/nodejs/node/pull/5120)

Windows 32-bit Installer: https://nodejs.org/dist/v5.9.0/node-v5.9.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v5.9.0/node-v5.9.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v5.9.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v5.9.0/win-x64/node.exe \
Mac OS X 64-bit Installer: https://nodejs.org/dist/v5.9.0/node-v5.9.0.pkg \
Mac OS X 64-bit Binary: https://nodejs.org/dist/v5.9.0/node-v5.9.0-darwin-x64.tar.gz \
Linux 32-bit Binary: https://nodejs.org/dist/v5.9.0/node-v5.9.0-linux-x86.tar.xz \
Linux 64-bit Binary: https://nodejs.org/dist/v5.9.0/node-v5.9.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v5.9.0/node-v5.9.0-linux-ppc64le.tar.xz \
SmartOS 32-bit Binary: https://nodejs.org/dist/v5.9.0/node-v5.9.0-sunos-x86.tar.xz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v5.9.0/node-v5.9.0-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v5.9.0/node-v5.9.0-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v5.9.0/node-v5.9.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v5.9.0/node-v5.9.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v5.9.0/node-v5.9.0.tar.gz \
Other release files: https://nodejs.org/dist/v5.9.0/ \
Documentation: https://nodejs.org/docs/v5.9.0/api/

Shasums (GPG signing hash: SHA512, file hash: SHA256):

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA512

6417022026c30cfb1b8af92f1434c1b202548076ada636708e1874d8af78197b  node-v5.9.0-darwin-x64.tar.gz
df58c7ec89849a0752f75b9a2e00c7c943089011d7c636920a1e1267d91671c0  node-v5.9.0-darwin-x64.tar.xz
546c6b43f193ad57638cadded1eb5ee4630b585f4f966e228cd2ae06f44b6c76  node-v5.9.0-headers.tar.gz
e77ceab9d39bf1086ca12214397a7dd4d2141b8aecbb86f95e60ee369c94445c  node-v5.9.0-headers.tar.xz
8ce0653a98a7507dc15bd7425154af1113685d054b6dee2c9701fed401feb12a  node-v5.9.0-linux-arm64.tar.gz
2b8b875022906ed5beabb90de8cc16979f85fba76930c192b80fafd2e72c8831  node-v5.9.0-linux-arm64.tar.xz
a69cc76e388b44a4c6de8007e0fad67f1308b32284894b4c483180c4aaf10cb4  node-v5.9.0-linux-armv6l.tar.gz
7f48846c39208ddeff0c3756378c71abf36f4910b3424f870c1e4f0c06e7c907  node-v5.9.0-linux-armv6l.tar.xz
b7ac2bf673eed236ac636d755874d90aa3563b55caed84730c8342cbdffc4ce3  node-v5.9.0-linux-armv7l.tar.gz
2cf5136d0342eeb833b0a24588f04d89387925149031886cd2d65ba29bed4388  node-v5.9.0-linux-armv7l.tar.xz
a2996dc3323579f2d9f306cb91a21a9ed6ec534791003eeed3e9114a0a1b27dd  node-v5.9.0-linux-ppc64le.tar.gz
45696542c11e0215e39f1a1f8c11246f93dd4b2ea00d1a77edff97646e8d849e  node-v5.9.0-linux-ppc64le.tar.xz
99c4136cf61761fac5ac57f80544140a3793b63e00a65d4a0e528c9db328bf40  node-v5.9.0-linux-x64.tar.gz
fc8e01886b0c4ce656163cd5c2d98fb624844243ea0c9fa9fb7114926dff541a  node-v5.9.0-linux-x64.tar.xz
fca2df390ff964951cc2d18150d17dfe474e5b59864415976b8bad9452f725eb  node-v5.9.0-linux-x86.tar.gz
43b0564c86812e629c6ede7316cf49e1b0a5e61d04f4e5dd2bc600c3265b4352  node-v5.9.0-linux-x86.tar.xz
64f0c8b3ba981c1b6569c993423956e7b2d1ee18f982bca88a85b9f445742010  node-v5.9.0.pkg
47a45169838b74f62c0dbb31643af2f2c2e86bae0920d030113b3aeb0f29275b  node-v5.9.0-sunos-x64.tar.gz
f2175f16307b458256d3b4c482c643e9487217e9e2a93cf949284526cae62bed  node-v5.9.0-sunos-x64.tar.xz
835f6d8ec09df20f5280851da0eec44f97d64a2f163a213efe2eac668d0a852a  node-v5.9.0-sunos-x86.tar.gz
0b0a97caf81901f00a463f83d31a0ea18e4661c0ad486afcff2687f135e5acc9  node-v5.9.0-sunos-x86.tar.xz
e5175a66481fd9389ef74ef8e8c164d052f139664deffc9e79241d6234760f3e  node-v5.9.0.tar.gz
9bdf2f8e44a721df459dfad0072a8ac46f11c05d25fc33dc0dc9c522827e34bb  node-v5.9.0.tar.xz
f53fac35122dfaec568f998fc210d4ead423c7939c024cd51e240879efd6845b  node-v5.9.0-x64.msi
38e74ba6feced696f5c3de1b12fb5b2f18839abe8e177d895cf5e5436d1362be  node-v5.9.0-x86.msi
ded3eb5f3e08831d7fc020dd86232ea11400468e3303355a03fa167175ab26b1  win-x64/node.exe
44e93b651d6b329143d5b708784cd99587e258790cf14f81abdd8cef3cedb422  win-x64/node.lib
279b6fe04661a6c9430b01535664d0e85998a74ff4c7c76804dfc318a201050a  win-x86/node.exe
82feb73750de3086db7fea6b933596d45a24019db4eb6f1e424e1036a1929be1  win-x86/node.lib
-----BEGIN PGP SIGNATURE-----
Comment: GPGTools - https://gpgtools.org

iQIcBAEBCgAGBQJW6dEeAAoJELY7U1pMIGypIUMP/1DawXNIxOw67Q/IEU9AUVQi
Z7laXU8RKewNbr6pGJ+E8Ionr6on5GBujyuFh/Z05t3zZ8vy/ffUfPpxoe+KluF9
/LafmG0VHDWyL95ZBlLMcQHT7ucoLxRKr444t5VNScwGwrcAFVhUfh9TR/7n+4qe
TgHPnfVVan9BIPE3l9tYQMoOcDApJTseXhmBBvk1MpXk2/lwEjC9O9idm7ewqmyL
fZ+gTtEFeLlqpjwoPUmd3Wedpjlezxa2ha2fJY0S7wle9tkbTWPoHSpOUbcDW5UH
bhRXR6BgCdcuQKwDoXSQKCDaHDQbKCuxGxox6UmPGBs8iKLZ44CDw5T9LEH9T9vo
KZV8biGRS3ELHLmREcAJtp+wktrv86F/7ylRiQSQ/GmGCxKqtTh3XoE8M2FI3GPd
vaYpDFz9j75ujpf4PVWiov0uZBnBWia1vjYmW4XezbztmjyjasqfpOgj9+W1+EV+
351gep52CmRZl0ZbBlgrfybUiKGn43w7COewAljfISs2f4wa3kq8r+hefSFZ5Gkq
ZRXJ1SVzuCayLskImfGfIPe0TuFnrunPM+CI2gdHIDqM0W4jHeDtaPmEd42f4LQ3
AB5Ee1GwHlnpPkCO6f+UsLz+UbbcK+AMgddnRn1ieA8wFL4FIl/XMtOqbEps+ZZq
K+iBWxsEuGMFBw3heC8s
=9sLm
-----END PGP SIGNATURE-----

```
