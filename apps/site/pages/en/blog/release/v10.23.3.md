---
date: '2021-02-09T23:05:55.397Z'
category: release
title: Node v10.23.3 (LTS)
layout: blog-post
author: <PERSON>
---

### Notable changes

The update to npm 6.14.11 has been relanded so that npm correctly reports its version.

### Commits

- [[`953a85035d`](https://github.com/nodejs/node/commit/953a85035d)] - **crypto**: fix crash when calling digest after piping (<PERSON>) [#28251](https://github.com/nodejs/node/pull/28251)
- [[`fe2c98003e`](https://github.com/nodejs/node/commit/fe2c98003e)] - **deps**: upgrade npm to 6.14.11 (Ruy <PERSON>) [#37173](https://github.com/nodejs/node/pull/37173)
- [[`7b7fb43b8a`](https://github.com/nodejs/node/commit/7b7fb43b8a)] - **_<PERSON>ert_** "**deps**: upgrade npm to 6.14.11" (<PERSON>) [#37278](https://github.com/nodejs/node/pull/37278)
- [[`1c6fbd6ffe`](https://github.com/nodejs/node/commit/1c6fbd6ffe)] - **test**: add test that verifies crypto stream pipeline (Evan Lucas) [#37009](https://github.com/nodejs/node/pull/37009)

Windows 32-bit Installer: https://nodejs.org/dist/v10.23.3/node-v10.23.3-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v10.23.3/node-v10.23.3-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v10.23.3/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v10.23.3/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v10.23.3/node-v10.23.3.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v10.23.3/node-v10.23.3-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v10.23.3/node-v10.23.3-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v10.23.3/node-v10.23.3-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v10.23.3/node-v10.23.3-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v10.23.3/node-v10.23.3-aix-ppc64.tar.gz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v10.23.3/node-v10.23.3-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v10.23.3/node-v10.23.3-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v10.23.3/node-v10.23.3-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v10.23.3/node-v10.23.3-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v10.23.3/node-v10.23.3.tar.gz \
Other release files: https://nodejs.org/dist/v10.23.3/ \
Documentation: https://nodejs.org/docs/v10.23.3/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

b9ad6ba0f7377fc5010b7aa3bb852385e3a54d6a910d294a76b23419a3eeb297  node-v10.23.3-aix-ppc64.tar.gz
f33d88fe2bf93c1b1f6312cb849a56185d3c8371517119d48245fa322b82d96e  node-v10.23.3-darwin-x64.tar.gz
2a375f2f25676100f5f5235d01e5ff6d722c87bb12e45e0caa1c8e6ee2c0247d  node-v10.23.3-darwin-x64.tar.xz
e11f10d58e7243e7da74213723093a47739b31ed0d9fe9074964806e3f515f79  node-v10.23.3-headers.tar.gz
501e99629521487a60add061d6fc6a99695ff038f7b899ab448c6d82bf5bafd9  node-v10.23.3-headers.tar.xz
483a4b609fe406b87da290bc0aa582b863e725321d71c6207f050ebe06baec8d  node-v10.23.3-linux-arm64.tar.gz
d559e61a043c56a3202baf670e4c3c973dafcfadc1109e4523ce1c5b2d80c1d8  node-v10.23.3-linux-arm64.tar.xz
24fcc84e890eb0fae3f75af4ec08f207230b41e1012a2ecdd8f006628be3353d  node-v10.23.3-linux-armv6l.tar.gz
39968109857b4d14427e717c76295a8703523d2942bd9704e69260ef1c9e6399  node-v10.23.3-linux-armv6l.tar.xz
2629053bba51065326bed825460e4b794ec9daf0be00c15daf6ff017c144eecb  node-v10.23.3-linux-armv7l.tar.gz
d3f9641f33cdf2c851b473de8dba993821d3f05eaeb51e7da6cafa160502d3f8  node-v10.23.3-linux-armv7l.tar.xz
b199aecee44c9251854773879ed7c6b707b1f6a7e5aa36021bdde853391d7ee8  node-v10.23.3-linux-ppc64le.tar.gz
6ee49bf9ffa6180ff3263ee153123643df892365290de8d775d744f6e979a3f9  node-v10.23.3-linux-ppc64le.tar.xz
f107548b47ba9e911b68dcfd73065e21b2d32d7cd00908401e0c2eed554fc51a  node-v10.23.3-linux-s390x.tar.gz
5a9a778fc981763ab18076bf7af645c61fd8799d71b54218bebec84f43995a6f  node-v10.23.3-linux-s390x.tar.xz
08e225a3581ca45b8c00d5561cf68ec7c53fe9022a30a1d167b9544789477f5b  node-v10.23.3-linux-x64.tar.gz
245cbb538c758f27efd2fa49b5ded81c924e2dc4d5b0d140855465366dea7f17  node-v10.23.3-linux-x64.tar.xz
689b768ccfe15e27a677662cea38626068b2f57ccf2ed178be700d9c4eac2c5d  node-v10.23.3.pkg
63d6910af4e87ab65624652e727c7c0941c28512418192e4f2c5f3dff45c33d7  node-v10.23.3-sunos-x64.tar.gz
fb72ab18ce155be6f549dcab583bfcfd115f6b7d93f00c95fc9b2f9e3b92ec8a  node-v10.23.3-sunos-x64.tar.xz
517db39eefccb03cb9ec1de67ddc917be943a755b290bff309b2e4b1bd78ffaf  node-v10.23.3.tar.gz
515bd7f2a1c3653c4f45eb0823479e812a24050da467dfd90b679ef09701ea8f  node-v10.23.3.tar.xz
9a94936e826a73130f030345bacd09bc39e3bd5727dd0b708e455b91ab1a84b5  node-v10.23.3-win-x64.7z
3d9a51c8dc073a2f36efe084abf08122c9d1a10e61fa440d6e1fbf5751cbe64b  node-v10.23.3-win-x64.zip
e9a78f452bbacc4ba9f40b0c48e7997ad13db4f976ec006ef99837b7a27d009a  node-v10.23.3-win-x86.7z
fa2456c7ea3a74922168f9e1c8168437babf2a6293aec2dd0f697754e3ad1fbd  node-v10.23.3-win-x86.zip
1cc375f5af02ce8db295a6fd932b2645122cc6dd527290f947ad6da2a1bb4d15  node-v10.23.3-x64.msi
68325cae566ad54ae076bf1160d47378f0a4a57ca65d3dc30d27fb2d5f9477e0  node-v10.23.3-x86.msi
1c2784462001f250a158be98cd93d3ed3ec40d52fbba0cd3a241ea9b6ce14d05  win-x64/node.exe
7688ed23318d253aa98ee198f94983e4b563fab188e6fd9dd32955e77111096a  win-x64/node.lib
c8b79a94be1fd400003067ff9a376f1c602d051a5547f6e9eeeddfc5c1f0207e  win-x64/node_pdb.7z
f81c67014590af935c860085eb915ef8e6ca9a6b45be49cc831162f21ef9c545  win-x64/node_pdb.zip
56136c34f7a765cc784adea093e32da1b295596a443f837c04cac951d86b7152  win-x86/node.exe
de1f3445597cbbee2e5eac435651f5dcab049a2d8bd3636877ab5803a87e269e  win-x86/node.lib
84057dadd63ccda17af28b7431adc3e4f56e7463ae3ee507c035b28060343127  win-x86/node_pdb.7z
5dd040cdd8888fd26aee9c671acc2f7948a32fc1cbf27725f01d96c01a8da93f  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEyC+jrhy+3Gvka5NgxDzsRcF6uTwFAmAjFLsACgkQxDzsRcF6
uTz5+Q/9FUplHtPUB3JH4mqCVQEyJMAuv++quwqnYXbjRicIuVsgucq0yAvONJtw
0Ux4/zwsXqVM8j3K8Po+LlJrAS6xvPnrdKpwpSiXjM3NqqLpWjG6f2o+6MNz0Dwj
fA9X2WO1mer4hH/hVMFxInZRpv3isPy079eGDyuf5ZvyCKxf9xq1aEeKetviJOys
PFzqhJrYz0jQjSs3GuleEpLxijXc7Gom2nb1nEIvjqF5MoFXbBq9F6Y+zDvKwEq8
2CPgvE4RLUEqw1Zpkt0M4BPlC3xyRECQTK3DWujOb7taT3kNEXOOkJPUdBfj64gh
YJlngPWuVgCJ6XrTY9JziC5q/pQNBEwctSwLqpTI0xin41h4Vjz3Qjl8QS7f1BWB
EZBMfuK+bC+ltT6a6y5HiQ9CB94K+Fp/2oFC6D0QU2wBKPGuU4Gv4eSrJOHmC0sW
dQW4lyz1vOw8bIOxhqIYrg9Ica0pAw4bda61sKyE32NVeM+DWNiZUQ9G238MHhu2
1vOlg9Dz81OMChrJqClaqOkBuRc5JMPcyKCo7dB3ylPc9Zd3JkYQp+R6UICByK3g
uJbC3kLhzoIKuZxPF0U+c+ehyEdsIyRr3aUQ8slLU+6OeFRhb0aOkji5kjrmJivE
knBEQAWfDR5m9Rnouag5DBqD+RP1oaGQXAesVEv7gNj2S4mdT5w=
=xqTD
-----END PGP SIGNATURE-----

```
