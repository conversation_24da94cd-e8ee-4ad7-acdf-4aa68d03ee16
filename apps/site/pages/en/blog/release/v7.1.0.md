---
date: '2016-11-08T21:17:10.895Z'
category: release
title: Node v7.1.0 (Current)
layout: blog-post
author: <PERSON>
---

### Notable changes

- **buffer**: add buffer.transcode to transcode a buffer's content from one encoding to another primarily using ICU (<PERSON>) [#9038](https://github.com/nodejs/node/pull/9038)
- **child_process**: add public API for IPC channel (cjihrig) [#9322](https://github.com/nodejs/node/pull/9322)
- **icu**:
  - Upgraded to ICU 58 - small icu (<PERSON>) [#9234](https://github.com/nodejs/node/pull/9234)
  - Add `cldr`, `tz`, and `unicode` to `process.versions` (<PERSON>) [#9266](https://github.com/nodejs/node/pull/9266)
- **lib**: make `String(global) === '[object global]'` (<PERSON>) [#9279](https://github.com/nodejs/node/pull/9279)
- **libuv**: Upgraded to 1.10.0 (cjihrig) [#9267](https://github.com/nodejs/node/pull/9267)
- **readline**: use icu based string width calculation (James M Snell) [#9040](https://github.com/nodejs/node/pull/9040)
- **src**:
  - add NODE_PRESERVE_SYMLINKS environment variable that has the same effect as the `--preserve-symlinks` flag (Marc Udoff) [#8749](https://github.com/nodejs/node/pull/8749)
  - Fix `String#toLocaleUpperCase()` and `String#toLocaleLowerCase()` (Steven R. Loomis)

### Commits

- [[`dafdb7b069`](https://github.com/nodejs/node/commit/dafdb7b069)] - **benchmark**: add trailing newline for consistency (Roman Reiss) [#9410](https://github.com/nodejs/node/pull/9410)
- [[`fab8eb660f`](https://github.com/nodejs/node/commit/fab8eb660f)] - **benchmark**: add microbenchmarks for ES Map (Rod Vagg) [#7581](https://github.com/nodejs/node/pull/7581)
- [[`44792f83bf`](https://github.com/nodejs/node/commit/44792f83bf)] - **benchmark,lib,test,tools**: remove unneeded . escape (Rich Trott) [#9449](https://github.com/nodejs/node/pull/9449)
- [[`c70c96a3e2`](https://github.com/nodejs/node/commit/c70c96a3e2)] - **buffer**: coerce offset using Math.trunc() (cjihrig) [#9341](https://github.com/nodejs/node/pull/9341)
- [[`212da12f45`](https://github.com/nodejs/node/commit/212da12f45)] - **buffer**: use correct name for custom inspect symbol (Charmander) [#9289](https://github.com/nodejs/node/pull/9289)
- [[`0939edd4ed`](https://github.com/nodejs/node/commit/0939edd4ed)] - **(SEMVER-MINOR)** **buffer**: add buffer.transcode (James M Snell) [#9038](https://github.com/nodejs/node/pull/9038)
- [[`ceec520aef`](https://github.com/nodejs/node/commit/ceec520aef)] - **build**: add MAKEFLAGS="-j1" to node-gyp (Daniel Bevenius) [#9450](https://github.com/nodejs/node/pull/9450)
- [[`1109d0b244`](https://github.com/nodejs/node/commit/1109d0b244)] - **build**: reduce noise from doc target (Daniel Bevenius) [#9457](https://github.com/nodejs/node/pull/9457)
- [[`90aac7ca28`](https://github.com/nodejs/node/commit/90aac7ca28)] - **build**: start comments at beginning of line (Sakthipriyan Vairamani (thefourtheye)) [#9375](https://github.com/nodejs/node/pull/9375)
- [[`b51db7120e`](https://github.com/nodejs/node/commit/b51db7120e)] - **build**: make node-gyp output silent (Sakthipriyan Vairamani (thefourtheye)) [#8990](https://github.com/nodejs/node/pull/8990)
- [[`d8eaa14c2d`](https://github.com/nodejs/node/commit/d8eaa14c2d)] - **build**: prioritise --shared-X-Y over pkg-config (Rod Vagg) [#9368](https://github.com/nodejs/node/pull/9368)
- [[`f7d8481ee2`](https://github.com/nodejs/node/commit/f7d8481ee2)] - **build**: use wxneeded on openbsd (Aaron Bieber) [#9232](https://github.com/nodejs/node/pull/9232)
- [[`7b0e93738b`](https://github.com/nodejs/node/commit/7b0e93738b)] - **(SEMVER-MINOR)** **child_process**: add public API for IPC channel (cjihrig) [#9322](https://github.com/nodejs/node/pull/9322)
- [[`4e3731c7e7`](https://github.com/nodejs/node/commit/4e3731c7e7)] - **child_process**: remove unreachable code (cjihrig) [#9307](https://github.com/nodejs/node/pull/9307)
- [[`d573acf96f`](https://github.com/nodejs/node/commit/d573acf96f)] - **child_process**: remove unreachable execSync() code (cjihrig) [#9209](https://github.com/nodejs/node/pull/9209)
- [[`f1f00df9bf`](https://github.com/nodejs/node/commit/f1f00df9bf)] - **deps**: upgrade npm to 3.10.9 (Kat Marchán) [#9286](https://github.com/nodejs/node/pull/9286)
- [[`3d1766f492`](https://github.com/nodejs/node/commit/3d1766f492)] - **(SEMVER-MINOR)** **deps**: Intl: ICU 58 bump - small icu (BIG COMMIT) (Steven R. Loomis) [#9234](https://github.com/nodejs/node/pull/9234)
- [[`827000ee62`](https://github.com/nodejs/node/commit/827000ee62)] - **(SEMVER-MINOR)** **deps**: Intl: ICU 58 bump: configure/LICENSE/docs (Steven R. Loomis) [#9234](https://github.com/nodejs/node/pull/9234)
- [[`0f871e1087`](https://github.com/nodejs/node/commit/0f871e1087)] - **deps**: back port OpenBSD fix in c-ares/c-ares (Aaron Bieber) [#9232](https://github.com/nodejs/node/pull/9232)
- [[`106d71914c`](https://github.com/nodejs/node/commit/106d71914c)] - **deps**: upgrade libuv to 1.10.0 (cjihrig) [#9267](https://github.com/nodejs/node/pull/9267)
- [[`4c4132e5d3`](https://github.com/nodejs/node/commit/4c4132e5d3)] - **doc**: update minute-taking procedure for CTC (Rich Trott) [#9425](https://github.com/nodejs/node/pull/9425)
- [[`ed8df17135`](https://github.com/nodejs/node/commit/ed8df17135)] - **doc**: note that tests should include a description (Gibson Fahnestock) [#9415](https://github.com/nodejs/node/pull/9415)
- [[`bc2d1c9d91`](https://github.com/nodejs/node/commit/bc2d1c9d91)] - **doc**: do not link in the headings (Sakthipriyan Vairamani (thefourtheye)) [#9416](https://github.com/nodejs/node/pull/9416)
- [[`4bb9d21d01`](https://github.com/nodejs/node/commit/4bb9d21d01)] - **doc**: update GOVERNANCE.md to use "meeting chair" (Rich Trott) [#9432](https://github.com/nodejs/node/pull/9432)
- [[`c2fab3c600`](https://github.com/nodejs/node/commit/c2fab3c600)] - **doc**: add Sakthipriyan to the CTC (Rod Vagg) [#9427](https://github.com/nodejs/node/pull/9427)
- [[`a8295d86d9`](https://github.com/nodejs/node/commit/a8295d86d9)] - **doc**: update Diagnostics WG info (Josh Gavant) [#9329](https://github.com/nodejs/node/pull/9329)
- [[`3af9453019`](https://github.com/nodejs/node/commit/3af9453019)] - **doc**: move stray sentences in zlib doc (Rich Trott) [#9365](https://github.com/nodejs/node/pull/9365)
- [[`d4b509584f`](https://github.com/nodejs/node/commit/d4b509584f)] - **doc**: use 'an' over 'a', remove redundant sentence (Zeke Sikelianos) [#9345](https://github.com/nodejs/node/pull/9345)
- [[`ff69e38070`](https://github.com/nodejs/node/commit/ff69e38070)] - **doc**: add more internal links to fs.Stats object (Zeke Sikelianos) [#9345](https://github.com/nodejs/node/pull/9345)
- [[`c554f090df`](https://github.com/nodejs/node/commit/c554f090df)] - **doc**: fix outdate ninja link (Yangyang Liu) [#9278](https://github.com/nodejs/node/pull/9278)
- [[`3d4a829d85`](https://github.com/nodejs/node/commit/3d4a829d85)] - **doc**: fix broken links to Buffer.from(string) (Jesse McCarthy) [#9294](https://github.com/nodejs/node/pull/9294)
- [[`225a9dfb00`](https://github.com/nodejs/node/commit/225a9dfb00)] - **doc**: fs: fix link to mkdtemp (coderaiser) [#9379](https://github.com/nodejs/node/pull/9379)
- [[`dbeadd363c`](https://github.com/nodejs/node/commit/dbeadd363c)] - **doc**: update OpenSSL links (kobelb) [#9338](https://github.com/nodejs/node/pull/9338)
- [[`eeabab3827`](https://github.com/nodejs/node/commit/eeabab3827)] - **doc**: add 2016-10-26 CTC meeting minutes (Rich Trott) [#9348](https://github.com/nodejs/node/pull/9348)
- [[`31690a690c`](https://github.com/nodejs/node/commit/31690a690c)] - **doc**: add 2016-10-05 CTC meeting minutes (Josh Gavant) [#9326](https://github.com/nodejs/node/pull/9326)
- [[`7f1a40dbcf`](https://github.com/nodejs/node/commit/7f1a40dbcf)] - **doc**: add 2016-09-28 CTC meeting minutes (Josh Gavant) [#9325](https://github.com/nodejs/node/pull/9325)
- [[`edd89265ba`](https://github.com/nodejs/node/commit/edd89265ba)] - **doc**: update CONTRIBUTING.md to address editing PRs (Gibson Fahnestock) [#9259](https://github.com/nodejs/node/pull/9259)
- [[`c7458909a7`](https://github.com/nodejs/node/commit/c7458909a7)] - **doc**: reference signal(7) for the list of signals (Emanuele DelBono) [#9323](https://github.com/nodejs/node/pull/9323)
- [[`a3f6854724`](https://github.com/nodejs/node/commit/a3f6854724)] - **doc**: more realistic custom inspect example (Ryan Scheel (Havvy)) [#8875](https://github.com/nodejs/node/pull/8875)
- [[`a0074e2232`](https://github.com/nodejs/node/commit/a0074e2232)] - **doc**: clarify buffer toString docs. (Olan Byrne) [#8984](https://github.com/nodejs/node/pull/8984)
- [[`3f90481e20`](https://github.com/nodejs/node/commit/3f90481e20)] - **doc**: clarify relation between a file and a module (marzelin) [#9026](https://github.com/nodejs/node/pull/9026)
- [[`82119049ef`](https://github.com/nodejs/node/commit/82119049ef)] - **doc**: fix typo in http.md (anu0012) [#9144](https://github.com/nodejs/node/pull/9144)
- [[`d2e7882723`](https://github.com/nodejs/node/commit/d2e7882723)] - **doc**: add 2016-10-19 CTC meeting minutes (Josh Gavant) [#9193](https://github.com/nodejs/node/pull/9193)
- [[`ce00a9d2b6`](https://github.com/nodejs/node/commit/ce00a9d2b6)] - **doc**: add performance warning to require.extensions (Ben Noordhuis) [#9196](https://github.com/nodejs/node/pull/9196)
- [[`d1c32aa335`](https://github.com/nodejs/node/commit/d1c32aa335)] - **doc**: mention case-insensitive env on windows (Oliver Salzburg) [#9166](https://github.com/nodejs/node/pull/9166)
- [[`c6e429a6bc`](https://github.com/nodejs/node/commit/c6e429a6bc)] - **doc**: add CTC meeting minutes for 2016-10-12 (Michael Dawson) [#9070](https://github.com/nodejs/node/pull/9070)
- [[`355041960d`](https://github.com/nodejs/node/commit/355041960d)] - **events**: remove unnecessary checks (cjihrig) [#9330](https://github.com/nodejs/node/pull/9330)
- [[`0ce0abf6cb`](https://github.com/nodejs/node/commit/0ce0abf6cb)] - **events,test**: fix TypeError in EventEmitter warning (jseagull) [#9021](https://github.com/nodejs/node/pull/9021)
- [[`6f35e4421a`](https://github.com/nodejs/node/commit/6f35e4421a)] - **http**: add debug message for invalid header value (Evan Lucas) [#9195](https://github.com/nodejs/node/pull/9195)
- [[`173b088e1a`](https://github.com/nodejs/node/commit/173b088e1a)] - **inspector**: do not prompt to use localhost (Eugene Ostroukhov) [#9451](https://github.com/nodejs/node/pull/9451)
- [[`939d1023c2`](https://github.com/nodejs/node/commit/939d1023c2)] - **inspector**: switch to new inspector APIs (Eugene Ostroukhov) [#9028](https://github.com/nodejs/node/pull/9028)
- [[`2e7b078e7b`](https://github.com/nodejs/node/commit/2e7b078e7b)] - **inspector**: fix request path nullptr dereference (Ben Noordhuis) [#9184](https://github.com/nodejs/node/pull/9184)
- [[`9940666c1b`](https://github.com/nodejs/node/commit/9940666c1b)] - **(SEMVER-MINOR)** **intl**: Add more versions from ICU (Steven R. Loomis) [#9266](https://github.com/nodejs/node/pull/9266)
- [[`5bfefa6063`](https://github.com/nodejs/node/commit/5bfefa6063)] - **lib**: change == to === in linkedlist (jedireza) [#9362](https://github.com/nodejs/node/pull/9362)
- [[`d24bd20d2b`](https://github.com/nodejs/node/commit/d24bd20d2b)] - **lib**: make `String(global) === '[object global]'` (Anna Henningsen) [#9279](https://github.com/nodejs/node/pull/9279)
- [[`9372aee4a3`](https://github.com/nodejs/node/commit/9372aee4a3)] - **lib**: fix beforeExit not working with -e (Ben Noordhuis) [#8821](https://github.com/nodejs/node/pull/8821)
- [[`c231130e06`](https://github.com/nodejs/node/commit/c231130e06)] - **module**: skip directories known not to exist (Ben Noordhuis) [#9196](https://github.com/nodejs/node/pull/9196)
- [[`d09eb9c6b2`](https://github.com/nodejs/node/commit/d09eb9c6b2)] - **net**: name anonymous functions (Pedro Victor) [#9357](https://github.com/nodejs/node/pull/9357)
- [[`a5c62cb4f2`](https://github.com/nodejs/node/commit/a5c62cb4f2)] - **(SEMVER-MINOR)** **readline**: use icu based string width calculation (James M Snell) [#9040](https://github.com/nodejs/node/pull/9040)
- [[`60461d2d90`](https://github.com/nodejs/node/commit/60461d2d90)] - **repl**: refactor lib/repl.js (Rich Trott) [#9374](https://github.com/nodejs/node/pull/9374)
- [[`071836aa42`](https://github.com/nodejs/node/commit/071836aa42)] - **repl**: name anonymous functions (Pedro Victor) [#9356](https://github.com/nodejs/node/pull/9356)
- [[`0b9d80a037`](https://github.com/nodejs/node/commit/0b9d80a037)] - **repl**: don’t write to input stream in editor mode (Anna Henningsen) [#9207](https://github.com/nodejs/node/pull/9207)
- [[`1c59cefc44`](https://github.com/nodejs/node/commit/1c59cefc44)] - **repl**: make `key` of `repl.write()` optional always (Anna Henningsen) [#9207](https://github.com/nodejs/node/pull/9207)
- [[`b1ef638de3`](https://github.com/nodejs/node/commit/b1ef638de3)] - **(SEMVER-MINOR)** **src**: default --icu_case_mapping on as a v8 option (Steven R. Loomis) [#9454](https://github.com/nodejs/node/pull/9454)
- [[`0c236d1d36`](https://github.com/nodejs/node/commit/0c236d1d36)] - **src**: replace SetNamedPropertyHandler() (AnnaMag) [#9062](https://github.com/nodejs/node/pull/9062)
- [[`5ab172ee8f`](https://github.com/nodejs/node/commit/5ab172ee8f)] - **src**: fix use of uninitialized variable (James M Snell) [#9281](https://github.com/nodejs/node/pull/9281)
- [[`57c0a9b5dc`](https://github.com/nodejs/node/commit/57c0a9b5dc)] - **src**: remove unused function (Brian White) [#9243](https://github.com/nodejs/node/pull/9243)
- [[`08e12c7809`](https://github.com/nodejs/node/commit/08e12c7809)] - **src**: remove superfluous env_string string (Ben Noordhuis) [#9213](https://github.com/nodejs/node/pull/9213)
- [[`c342bda49e`](https://github.com/nodejs/node/commit/c342bda49e)] - **src**: make cross-context MakeCallback() calls work (Ben Noordhuis) [#9221](https://github.com/nodejs/node/pull/9221)
- [[`60a5b515b8`](https://github.com/nodejs/node/commit/60a5b515b8)] - **(SEMVER-MINOR)** **src**: add NODE_PRESERVE_SYMLINKS environment variable (Marc Udoff) [#8749](https://github.com/nodejs/node/pull/8749)
- [[`f2a3b24611`](https://github.com/nodejs/node/commit/f2a3b24611)] - **src**: clean up program/isolate/env init logic (Ben Noordhuis) [#9224](https://github.com/nodejs/node/pull/9224)
- [[`9e753ba782`](https://github.com/nodejs/node/commit/9e753ba782)] - **src**: simplify code, remove NodeInstanceData (Ben Noordhuis) [#9224](https://github.com/nodejs/node/pull/9224)
- [[`8b53f3c41c`](https://github.com/nodejs/node/commit/8b53f3c41c)] - **src**: speed up module loading, don't resize buffer (Ben Noordhuis) [#9132](https://github.com/nodejs/node/pull/9132)
- [[`362c307f38`](https://github.com/nodejs/node/commit/362c307f38)] - **src**: speed up module loading, skip EOF read (Ben Noordhuis) [#9132](https://github.com/nodejs/node/pull/9132)
- [[`85a9295813`](https://github.com/nodejs/node/commit/85a9295813)] - **src,tools**: speed up startup by 2.5% (Ben Noordhuis) [#5458](https://github.com/nodejs/node/pull/5458)
- [[`6e1eb59fee`](https://github.com/nodejs/node/commit/6e1eb59fee)] - **test**: improve test-debugger-util-regression (Santiago Gimeno) [#9490](https://github.com/nodejs/node/pull/9490)
- [[`6eb6816e22`](https://github.com/nodejs/node/commit/6eb6816e22)] - **test**: fix flaky test-net-GH-5504 (Santiago Gimeno) [#9461](https://github.com/nodejs/node/pull/9461)
- [[`f640bafc58`](https://github.com/nodejs/node/commit/f640bafc58)] - **test**: fix flaky test-force-repl-with-eval (Santiago Gimeno) [#9460](https://github.com/nodejs/node/pull/9460)
- [[`675a4b20b6`](https://github.com/nodejs/node/commit/675a4b20b6)] - **test**: update http-header-obstext (Gibson Fahnestock) [#9415](https://github.com/nodejs/node/pull/9415)
- [[`9d9ea8127e`](https://github.com/nodejs/node/commit/9d9ea8127e)] - **test**: move timer-dependent test to sequential (Rich Trott) [#9431](https://github.com/nodejs/node/pull/9431)
- [[`1c3487b5c3`](https://github.com/nodejs/node/commit/1c3487b5c3)] - **test**: remove timers from streams test (Anna Henningsen)
- [[`e696bc33eb`](https://github.com/nodejs/node/commit/e696bc33eb)] - **test**: increase test coverage for lib/zlib.js (Rich Trott) [#9366](https://github.com/nodejs/node/pull/9366)
- [[`ed3f80a988`](https://github.com/nodejs/node/commit/ed3f80a988)] - **test**: add test for HTTP client "aborted" event (Kyle E. Mitchell) [#7376](https://github.com/nodejs/node/pull/7376)
- [[`d12ed29f6a`](https://github.com/nodejs/node/commit/d12ed29f6a)] - **test**: remove timer in test-dgram-send-empty-array (Rich Trott) [#9361](https://github.com/nodejs/node/pull/9361)
- [[`e451022dd9`](https://github.com/nodejs/node/commit/e451022dd9)] - **test**: refactor test-http-client-readable (Rich Trott) [#9344](https://github.com/nodejs/node/pull/9344)
- [[`01b626a45a`](https://github.com/nodejs/node/commit/01b626a45a)] - **test**: clean up dgram-broadcast-multi-process test (Isobel Redelmeier) [#9308](https://github.com/nodejs/node/pull/9308)
- [[`411b1339bc`](https://github.com/nodejs/node/commit/411b1339bc)] - **test**: fix freebsd10-64 CI failures (Rich Trott) [#9317](https://github.com/nodejs/node/pull/9317)
- [[`1037463604`](https://github.com/nodejs/node/commit/1037463604)] - **test**: add child_process customFds test (cjihrig) [#9307](https://github.com/nodejs/node/pull/9307)
- [[`bd9cb40977`](https://github.com/nodejs/node/commit/bd9cb40977)] - **test**: run all of test-timers-blocking-callback (Rich Trott) [#9305](https://github.com/nodejs/node/pull/9305)
- [[`8b7ce8bd11`](https://github.com/nodejs/node/commit/8b7ce8bd11)] - **test**: fix flaky test-fs-watch-recursive on OS X (Rich Trott) [#9303](https://github.com/nodejs/node/pull/9303)
- [[`6c9e4fddf0`](https://github.com/nodejs/node/commit/6c9e4fddf0)] - **test**: refactor test-async-wrap-check-providers (Gerges Beshay) [#9297](https://github.com/nodejs/node/pull/9297)
- [[`0ab008e50d`](https://github.com/nodejs/node/commit/0ab008e50d)] - **test**: fix lint error regarding unused commons const (Daniel Bevenius) [#9334](https://github.com/nodejs/node/pull/9334)
- [[`c9b67c6a91`](https://github.com/nodejs/node/commit/c9b67c6a91)] - **test**: writable stream needDrain state (Italo A. Casas) [#8799](https://github.com/nodejs/node/pull/8799)
- [[`248a3200b2`](https://github.com/nodejs/node/commit/248a3200b2)] - **test**: writable stream ending state (Italo A. Casas) [#8707](https://github.com/nodejs/node/pull/8707)
- [[`d6f688ae0d`](https://github.com/nodejs/node/commit/d6f688ae0d)] - **test**: writable stream finished state (Italo A. Casas) [#8791](https://github.com/nodejs/node/pull/8791)
- [[`d49d990c42`](https://github.com/nodejs/node/commit/d49d990c42)] - **test**: prevent workers outliving parent (Sam Roberts) [#9257](https://github.com/nodejs/node/pull/9257)
- [[`2ad81ed0e6`](https://github.com/nodejs/node/commit/2ad81ed0e6)] - **test**: refactor /parallel/test-cluster-uncaught-exception.js to ES6 (Deverick) [#9239](https://github.com/nodejs/node/pull/9239)
- [[`f39eb05946`](https://github.com/nodejs/node/commit/f39eb05946)] - **test**: use strict assertions in module loader test (Ben Noordhuis) [#9263](https://github.com/nodejs/node/pull/9263)
- [[`fc9e6a37fa`](https://github.com/nodejs/node/commit/fc9e6a37fa)] - **test**: remove err timer from test-http-set-timeout (BethGriggs) [#9264](https://github.com/nodejs/node/pull/9264)
- [[`53520f06f3`](https://github.com/nodejs/node/commit/53520f06f3)] - **test**: clean up `test-child-process-exec-cwd.js` (Jeena Lee) [#9231](https://github.com/nodejs/node/pull/9231)
- [[`deef2f6079`](https://github.com/nodejs/node/commit/deef2f6079)] - **test**: add child_process.exec() timeout coverage (cjihrig) [#9208](https://github.com/nodejs/node/pull/9208)
- [[`5e138fe768`](https://github.com/nodejs/node/commit/5e138fe768)] - **test**: skip whatwg url parse and setter tests when icu is missing (James M Snell) [#9246](https://github.com/nodejs/node/pull/9246)
- [[`a39b98ef73`](https://github.com/nodejs/node/commit/a39b98ef73)] - **test**: add common.hasIntl (James M Snell) [#9246](https://github.com/nodejs/node/pull/9246)
- [[`efb62aa146`](https://github.com/nodejs/node/commit/efb62aa146)] - **test**: fix flaky test by removing timer (Evan Lucas) [#9199](https://github.com/nodejs/node/pull/9199)
- [[`44427cc1f7`](https://github.com/nodejs/node/commit/44427cc1f7)] - **test**: case sensitivity of env variables (Oliver Salzburg) [#9166](https://github.com/nodejs/node/pull/9166)
- [[`63ef0990f3`](https://github.com/nodejs/node/commit/63ef0990f3)] - **test**: add coverage for execFileSync() errors (cjihrig) [#9211](https://github.com/nodejs/node/pull/9211)
- [[`06b414078e`](https://github.com/nodejs/node/commit/06b414078e)] - **test**: remove test-v8-inspector-json-protocol test (Ben Noordhuis) [#9184](https://github.com/nodejs/node/pull/9184)
- [[`21ba3e3b89`](https://github.com/nodejs/node/commit/21ba3e3b89)] - **test**: add more module loader test coverage (Ben Noordhuis) [#9196](https://github.com/nodejs/node/pull/9196)
- [[`204461925b`](https://github.com/nodejs/node/commit/204461925b)] - **test**: make flaky pummel test more reliable (Ben Noordhuis) [#9241](https://github.com/nodejs/node/pull/9241)
- [[`4be1ba582a`](https://github.com/nodejs/node/commit/4be1ba582a)] - **test**: move flaky test to test/pummel (Ben Noordhuis) [#9241](https://github.com/nodejs/node/pull/9241)
- [[`032533954b`](https://github.com/nodejs/node/commit/032533954b)] - **tools**: use long format for gpg fingerprint (Myles Borins) [#9258](https://github.com/nodejs/node/pull/9258)
- [[`aac4af2b26`](https://github.com/nodejs/node/commit/aac4af2b26)] - **tools**: enable final newline in .editorconfig (Roman Reiss) [#9410](https://github.com/nodejs/node/pull/9410)
- [[`513da404cb`](https://github.com/nodejs/node/commit/513da404cb)] - **tools**: enforce function name matching in linter (Rich Trott) [#9408](https://github.com/nodejs/node/pull/9408)
- [[`c23ece7056`](https://github.com/nodejs/node/commit/c23ece7056)] - **tools**: remove dangling eslint symlink (Sam Roberts) [#9299](https://github.com/nodejs/node/pull/9299)
- [[`bdad1e28fd`](https://github.com/nodejs/node/commit/bdad1e28fd)] - **tools**: make --repeat work with -j in test.py (Rich Trott) [#9249](https://github.com/nodejs/node/pull/9249)
- [[`4f0596fb03`](https://github.com/nodejs/node/commit/4f0596fb03)] - **util**: use template strings (Alejandro Oviedo Garcia) [#9120](https://github.com/nodejs/node/pull/9120)
- [[`b083086ff2`](https://github.com/nodejs/node/commit/b083086ff2)] - **vm**: name anonymous functions (solebox) [#9388](https://github.com/nodejs/node/pull/9388)

Windows 32-bit Installer: https://nodejs.org/dist/v7.1.0/node-v7.1.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v7.1.0/node-v7.1.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v7.1.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v7.1.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v7.1.0/node-v7.1.0.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v7.1.0/node-v7.1.0-darwin-x64.tar.gz \
Linux 32-bit Binary: https://nodejs.org/dist/v7.1.0/node-v7.1.0-linux-x86.tar.xz \
Linux 64-bit Binary: https://nodejs.org/dist/v7.1.0/node-v7.1.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v7.1.0/node-v7.1.0-linux-ppc64le.tar.xz \
Linux PPC BE 64-bit Binary: https://nodejs.org/dist/v7.1.0/node-v7.1.0-linux-ppc64.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v7.1.0/node-v7.1.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v7.1.0/node-v7.1.0-aix-ppc64.tar.gz \
SmartOS 32-bit Binary: https://nodejs.org/dist/v7.1.0/node-v7.1.0-sunos-x86.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v7.1.0/node-v7.1.0-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v7.1.0/node-v7.1.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v7.1.0/node-v7.1.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v7.1.0/node-v7.1.0.tar.gz \
Other release files: https://nodejs.org/dist/v7.1.0/ \
Documentation: https://nodejs.org/docs/v7.1.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

5ef780ab1d04674c7ec528eeb112d37a42a58f00c9f15954655929c5adaf1bbe  node-v7.1.0-aix-ppc64.tar.gz
b4615c79eac53f1a660b94e047d1dc64d797bf365f305ac9e0c2ac7304340171  node-v7.1.0-darwin-x64.tar.gz
8d597ef383eaef91f7da7fdedc0547b7ce6d2a8806f259b74ebb88bde2679df1  node-v7.1.0-darwin-x64.tar.xz
b254b8ff711e94ff59895f820ff61d9054a60c2055b5491c042d456af1f70a83  node-v7.1.0-headers.tar.gz
020f9a74cc191cb6c2ce117fda9035fe7b7dfe1f2a003cff190a04c585a450db  node-v7.1.0-headers.tar.xz
fcaa2281b2ea4e0a6b598fae4fbee3708aded5b1b0b2397f8fab18c13e3ea3be  node-v7.1.0-linux-arm64.tar.gz
88d3ceb52e5ab5fb28f0aed6078fb8a493a8ed570b174eb98db7b7abed16276e  node-v7.1.0-linux-arm64.tar.xz
2e87c4249ebd29e6022aa289ec8c7632e92ba7a5c61a5e035c0acefa8a143772  node-v7.1.0-linux-armv6l.tar.gz
69e6cc3488f48a484def2cdab3cfc4da2153e92e4883b1c13fe46cc0be6ccd78  node-v7.1.0-linux-armv6l.tar.xz
8899d4340c23282ec4dc28b6a04bb20279da46bf92db58abce50a616877cd811  node-v7.1.0-linux-armv7l.tar.gz
218d83c0dc58e94d5c3aa866495eaa39a9b5e57662fa0f296e4ea36c5a6de681  node-v7.1.0-linux-armv7l.tar.xz
6cc4ccb25c21bd2331b30aa4193c21c4d3ac2d9d1fd7300157285383c288ec29  node-v7.1.0-linux-ppc64le.tar.gz
61fcab2aa00dc5100908114cf665526921843246d36ee3aa919adc8a4c82a533  node-v7.1.0-linux-ppc64le.tar.xz
662e3806f9ce46d0f92a347db55b50c3161406439317976c5fdb51e1dd189861  node-v7.1.0-linux-ppc64.tar.gz
e7e84c6c1beff9198725b7fefe2e47933a6ccb25760345bc0f6be9e72fa893a9  node-v7.1.0-linux-ppc64.tar.xz
c642a97c368644e8808cb006c2ad5822c917fa36bce3e8f41a01a4a2bb84740c  node-v7.1.0-linux-s390x.tar.gz
b053d8e671544330fe8d55a4a0691ec5d71302c755ca003e405995c9bacc16f4  node-v7.1.0-linux-s390x.tar.xz
0d2f13477ba991950bd9938e38c8d943b9bf2e899adcd4a28e98532f029e9910  node-v7.1.0-linux-x64.tar.gz
312f24d410e60f805db927d31d3b4547f69f88f4b2176c2eeca2f042f33624ce  node-v7.1.0-linux-x64.tar.xz
f6bcb57e7e693b25ad448faf18300bb9f823bc114aa0b050b7953978511e9a1d  node-v7.1.0-linux-x86.tar.gz
310252fc6295a387e3172efad2efebbd9e32b3eba137d2ee314cbd7f3238e110  node-v7.1.0-linux-x86.tar.xz
1d73a93e60b0021f616cdf44062717f67ed6fcfaf15f1efd0fa76491cb7623d0  node-v7.1.0.pkg
3dff7316291080f351349977d431f4ee0581c6d8961ec8b4ea747c8a79edd4ab  node-v7.1.0-sunos-x86.tar.gz
131b85395c7af612efbecd215e2f9b35b3359fc5200afb60b56acf824f334b11  node-v7.1.0-sunos-x86.tar.xz
595e7e2a37d1e0573044a90077bb12c0f750e5d8851899ffa74038238da9a983  node-v7.1.0.tar.gz
55ae831a2090e3af71b8f071dc5599922e5c752a2e40d77cbfd4a39d22cf4981  node-v7.1.0.tar.xz
6261e1f4392a1e87bf4e51f94e38fdf793d111b9291e81c9fdd9c79a4954fb4e  node-v7.1.0-win-x64.7z
b86f7a789b437a801dc675ef530d7728840f51601dd8e7734b418516fca6061b  node-v7.1.0-win-x64.zip
a4f8677f716e69eea5cfe870093770aae2809d90684708ac790796562f958a3e  node-v7.1.0-win-x86.7z
bb5b52aee2b3fd2506dc825d56c1d5ced65c9fe8b6a62e5e810b73dda78629bf  node-v7.1.0-win-x86.zip
853936fe0aa946e16bbab10d1c7e964bbf7a1820d12adbfd748d7cf9f8059fa3  node-v7.1.0-x64.msi
5d95a909788239b4ed97c5f79b2d16837ea577a1a68e34e014d0a45de7f27b1d  node-v7.1.0-x86.msi
d79a95cbf4ae761ff8cb16ef93582e2f3183a2bbc09996c2f9d97829bad9f581  win-x64/node.exe
87357f758d9b522056ee458deba2316250012676b876ab7c59543be22ed037ea  win-x64/node.lib
2f76f27013dabf035ee3660a15f27a221a28c56d5c7f8b39d42c68adbf59dc37  win-x64/node_pdb.7z
bf31dc33d9a0f9da70b5b1b08ad85097d2cb2badcd661d7830e3933ce2052e21  win-x64/node_pdb.zip
5b5c5d68356c03789d782ebb0035d969495b4c1ba241dc095f8e44cb7e84b3d5  win-x86/node.exe
d50d4489ab29355dea0a53d4645662aa09ede229c252011fa811b9ff9a0db9ed  win-x86/node.lib
3fe9dd91eda7b41c5bc5791683ea9cfd63c8285e97106177efc078e578f61908  win-x86/node_pdb.7z
2cb36424fa20b0c3aa294ffbb9744cb52fdbac4333a2b80bc538f6a5ec792da6  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----
Comment: GPGTools - https://gpgtools.org

iQIcBAEBCAAGBQJYIkCFAAoJELY7U1pMIGypwqsQALEg3TrBH4K2XmjaDotp95c7
IHCGv34HEkRm+VfBSquPELQv69aTWrJMcGt//pxN6vElBSAk1IKKOpQCvh6B2peW
tSnrlbX4HIGEvoW9CuEElyGNQ8bR/VWH5fa1LISV3d8MrYXrE4GGh+dujiF/+krp
b5+WM8QRvRVOQ798t1y4L73HH/HEdO+eeqrWykayUKYyy+4q85liaWVlsGA2zWqF
g8u+vOHM0MniId1zKmSWSuathRQhUTeaulCx++ZB1nlOKOUTiNDXBrNw2kjuq2Hn
v/f7OmnJLpi8Cgt6GIQ7K/i7F2DkmTZ2USSN6pE1Ic8YZD+z21qH/+AsDQukxUr7
YSkLzt75oXonEKDPZ61GwRG6pNe/yRj4tlAXCyvbHst9P/1FbtgSAPKoa2IKaBFu
FQ+GnH+ylba1WwEqVdb9SMLiAN1SbHBSFSanxV2xUiyfWFyqYVyHFUv+o/Cvcq/g
lmOyJIl3sP24YOw4RueLNle3vBf3SCOFAfOmEHP6ciQctV7c4dAr1s9m1NzbHDSR
PM/J4VX2x6/xmIgLGZHmhys9w71IkMpj6fVSOtceS/D/EzCl7rbf8sTV2rbLE+H8
9uqytWANa3mkM29poedRCfE7tphJiSrXCaJyBArci/yA4BTpRgWdUbTeY0lWzWf/
wVrzWajHNhSEaJBXDlnT
=JGy6
-----END PGP SIGNATURE-----

```
