---
date: '2024-12-19T18:57:29.689Z'
category: release
title: Node v23.5.0 (Current)
layout: blog-post
author: <PERSON>
---

## 2024-12-19, Version 23.5.0 (Current), @aduh95

### Notable Changes

#### WebCryptoAPI Ed25519 and X25519 algorithms are now stable

Following the merge of Curve25519 into the
[Web Cryptography API Editor's Draft](https://w3c.github.io/webcrypto/) the
`Ed25519` and `X25519` algorithm identifiers are now stable and will no longer
emit an ExperimentalWarning upon use.

Contributed by <PERSON><PERSON> in [#56142](https://github.com/nodejs/node/pull/56142).

#### On-thread hooks are back

This release introduces `module.registerHooks()` for registering module loader
customization hooks that are run for all modules loaded by `require()`, `import`,
and functions returned by `createRequire()` in the same thread, which makes them
easier for CJS monkey-patchers to migrate to.

```mjs
import assert from 'node:assert';
import { registerHooks, createRequire } from 'node:module';
import { writeFileSync } from 'node:fs';

writeFileSync('./bar.js', 'export const id = 123;', 'utf8');

registerHooks({
  resolve(specifier, context, nextResolve) {
    const replaced = specifier.replace('foo', 'bar');
    return nextResolve(replaced, context);
  },
  load(url, context, nextLoad) {
    const result = nextLoad(url, context);
    return {
      ...result,
      source: result.source.toString().replace('123', '456'),
    };
  },
});

// Checks that it works with require.
const require = createRequire(import.meta.url);
const required = require('./foo.js'); // Redirected by resolve hook to bar.js
assert.strictEqual(required.id, 456); // Replaced by load hook to 456

// Checks that it works with import.
const imported = await import('./foo.js'); // Redirected by resolve hook to bar.js
assert.strictEqual(imported.id, 456); // Replaced by load hook to 456
```

This complements the `module.register()` hooks - the new hooks fit better
internally and cover all corners in the module graph; whereas
`module.register()` previously could not cover `require()` while it was
on-thread, and still cannot cover `createRequire()` after being moved
off-thread.

They are also run in the same thread as the modules being loaded and where the
hooks are registered, which means they are easier to debug (no more
`console.log()` getting lost) and do not have the many deadlock issues haunting
the `module.register()` hooks. The new API also takes functions directly so that
it's easier for intermediate loader packages to take user options from files
that the hooks can't be aware of, like many existing CJS monkey-patchers do.

Contributed by Joyee Cheung in [#55698](https://github.com/nodejs/node/pull/55698).

#### Other notable changes

- \[[`59cae91465`](https://github.com/nodejs/node/commit/59cae91465)] - **(SEMVER-MINOR)** **dgram**: support blocklist in udp (theanarkh) [#56087](https://github.com/nodejs/node/pull/56087)
- \[[`72f79b44ed`](https://github.com/nodejs/node/commit/72f79b44ed)] - **doc**: stabilize util.styleText (Rafael Gonzaga) [#56265](https://github.com/nodejs/node/pull/56265)
- \[[`b5a2c0777d`](https://github.com/nodejs/node/commit/b5a2c0777d)] - **(SEMVER-MINOR)** **module**: add prefix-only modules to `module.builtinModules` (Jordan Harband) [#56185](https://github.com/nodejs/node/pull/56185)
- \[[`9863d27566`](https://github.com/nodejs/node/commit/9863d27566)] - **(SEMVER-MINOR)** **module**: only emit require(esm) warning under --trace-require-module (Joyee Cheung) [#56194](https://github.com/nodejs/node/pull/56194)
- \[[`8e780bc5ae`](https://github.com/nodejs/node/commit/8e780bc5ae)] - **(SEMVER-MINOR)** **module**: use synchronous hooks for preparsing in import(cjs) (Joyee Cheung) [#55698](https://github.com/nodejs/node/pull/55698)
- \[[`65bc8e847f`](https://github.com/nodejs/node/commit/65bc8e847f)] - **(SEMVER-MINOR)** **report**: fix typos in report keys and bump the version (Yuan-Ming Hsu) [#56068](https://github.com/nodejs/node/pull/56068)
- \[[`0ab36e1937`](https://github.com/nodejs/node/commit/0ab36e1937)] - **(SEMVER-MINOR)** **sqlite**: aggregate constants in a single property (Edigleysson Silva (Edy)) [#56213](https://github.com/nodejs/node/pull/56213)
- \[[`efcc5d90c5`](https://github.com/nodejs/node/commit/efcc5d90c5)] - **(SEMVER-MINOR)** **src,lib**: stabilize permission model (Rafael Gonzaga) [#56201](https://github.com/nodejs/node/pull/56201)

### Commits

- \[[`2314e4916e`](https://github.com/nodejs/node/commit/2314e4916e)] - **assert**: make Maps be partially compared in partialDeepStrictEqual (Giovanni Bucci) [#56195](https://github.com/nodejs/node/pull/56195)
- \[[`cfbdff7b45`](https://github.com/nodejs/node/commit/cfbdff7b45)] - **assert**: make partialDeepStrictEqual work with ArrayBuffers (Giovanni Bucci) [#56098](https://github.com/nodejs/node/pull/56098)
- \[[`f264dd6d20`](https://github.com/nodejs/node/commit/f264dd6d20)] - **buffer**: document concat zero-fill (Duncan) [#55562](https://github.com/nodejs/node/pull/55562)
- \[[`4831b87d83`](https://github.com/nodejs/node/commit/4831b87d83)] - **build**: set DESTCPU correctly for 'make binary' on loongarch64 (吴小白) [#56271](https://github.com/nodejs/node/pull/56271)
- \[[`1497bb405e`](https://github.com/nodejs/node/commit/1497bb405e)] - **build**: fix missing fp16 dependency in d8 builds (Joyee Cheung) [#56266](https://github.com/nodejs/node/pull/56266)
- \[[`445c8c7489`](https://github.com/nodejs/node/commit/445c8c7489)] - **build**: add major release action (Rafael Gonzaga) [#56199](https://github.com/nodejs/node/pull/56199)
- \[[`f4faedfa69`](https://github.com/nodejs/node/commit/f4faedfa69)] - **build**: fix C string encoding for `PRODUCT_DIR_ABS` (Anna Henningsen) [#56111](https://github.com/nodejs/node/pull/56111)
- \[[`6f49c8006c`](https://github.com/nodejs/node/commit/6f49c8006c)] - **build**: use variable for simdutf path (Shelley Vohr) [#56196](https://github.com/nodejs/node/pull/56196)
- \[[`fcaa2c82a6`](https://github.com/nodejs/node/commit/fcaa2c82a6)] - **build**: fix GN build on macOS (Joyee Cheung) [#56141](https://github.com/nodejs/node/pull/56141)
- \[[`08e5309f4f`](https://github.com/nodejs/node/commit/08e5309f4f)] - _**Revert**_ "**build**: avoid compiling with VS v17.12" (Gerhard Stöbich) [#56151](https://github.com/nodejs/node/pull/56151)
- \[[`c2fb38cfdf`](https://github.com/nodejs/node/commit/c2fb38cfdf)] - **crypto**: graduate WebCryptoAPI Ed25519 and X25519 algorithms as stable (Filip Skokan) [#56142](https://github.com/nodejs/node/pull/56142)
- \[[`8658833884`](https://github.com/nodejs/node/commit/8658833884)] - **deps**: update nghttp3 to 1.6.0 (Node.js GitHub Bot) [#56258](https://github.com/nodejs/node/pull/56258)
- \[[`7c941d4610`](https://github.com/nodejs/node/commit/7c941d4610)] - **deps**: update simdutf to 5.6.4 (Node.js GitHub Bot) [#56255](https://github.com/nodejs/node/pull/56255)
- \[[`4e9113eada`](https://github.com/nodejs/node/commit/4e9113eada)] - **deps**: update libuv to 1.49.2 (Luigi Pinca) [#56224](https://github.com/nodejs/node/pull/56224)
- \[[`db6aba12e4`](https://github.com/nodejs/node/commit/db6aba12e4)] - **deps**: update c-ares to v1.34.4 (Node.js GitHub Bot) [#56256](https://github.com/nodejs/node/pull/56256)
- \[[`25bb462bc2`](https://github.com/nodejs/node/commit/25bb462bc2)] - **deps**: define V8_PRESERVE_MOST as no-op on Windows (Stefan Stojanovic) [#56238](https://github.com/nodejs/node/pull/56238)
- \[[`54308c51bb`](https://github.com/nodejs/node/commit/54308c51bb)] - **deps**: update sqlite to 3.47.2 (Node.js GitHub Bot) [#56178](https://github.com/nodejs/node/pull/56178)
- \[[`59cae91465`](https://github.com/nodejs/node/commit/59cae91465)] - **(SEMVER-MINOR)** **dgram**: support blocklist in udp (theanarkh) [#56087](https://github.com/nodejs/node/pull/56087)
- \[[`52c18e605e`](https://github.com/nodejs/node/commit/52c18e605e)] - **doc**: fix color contrast issue in light mode (Rich Trott) [#56272](https://github.com/nodejs/node/pull/56272)
- \[[`72f79b44ed`](https://github.com/nodejs/node/commit/72f79b44ed)] - **doc**: stabilize util.styleText (Rafael Gonzaga) [#56265](https://github.com/nodejs/node/pull/56265)
- \[[`0d08756d0c`](https://github.com/nodejs/node/commit/0d08756d0c)] - **doc**: clarify util.aborted resource usage (Kunal Kumar) [#55780](https://github.com/nodejs/node/pull/55780)
- \[[`f94f21080b`](https://github.com/nodejs/node/commit/f94f21080b)] - **doc**: add esm examples to node:repl (Alfredo González) [#55432](https://github.com/nodejs/node/pull/55432)
- \[[`7a10ef88d9`](https://github.com/nodejs/node/commit/7a10ef88d9)] - **doc**: add esm examples to node:readline (Alfredo González) [#55335](https://github.com/nodejs/node/pull/55335)
- \[[`cc7a7c391b`](https://github.com/nodejs/node/commit/cc7a7c391b)] - **doc**: fix 'which' to 'that' and add commas (Selveter Senitro) [#56216](https://github.com/nodejs/node/pull/56216)
- \[[`c5b086250e`](https://github.com/nodejs/node/commit/c5b086250e)] - **doc**: fix winget config path (Alex Yang) [#56233](https://github.com/nodejs/node/pull/56233)
- \[[`71c38a24d4`](https://github.com/nodejs/node/commit/71c38a24d4)] - **doc**: add esm examples to node:tls (Alfredo González) [#56229](https://github.com/nodejs/node/pull/56229)
- \[[`394fffbbde`](https://github.com/nodejs/node/commit/394fffbbde)] - **doc**: add esm examples to node:perf_hooks (Alfredo González) [#55257](https://github.com/nodejs/node/pull/55257)
- \[[`7b2a6ee61e`](https://github.com/nodejs/node/commit/7b2a6ee61e)] - **doc**: `sea.getRawAsset(key)` always returns an ArrayBuffer (沈鸿飞) [#56206](https://github.com/nodejs/node/pull/56206)
- \[[`8092dcf27e`](https://github.com/nodejs/node/commit/8092dcf27e)] - **doc**: update announce documentation for releases (Rafael Gonzaga) [#56200](https://github.com/nodejs/node/pull/56200)
- \[[`2974667815`](https://github.com/nodejs/node/commit/2974667815)] - **doc**: update blog link to /vulnerability (Rafael Gonzaga) [#56198](https://github.com/nodejs/node/pull/56198)
- \[[`f3b3ff85e0`](https://github.com/nodejs/node/commit/f3b3ff85e0)] - **doc**: call out import.meta is only supported in ES modules (Anton Kastritskii) [#56186](https://github.com/nodejs/node/pull/56186)
- \[[`a9e67280e7`](https://github.com/nodejs/node/commit/a9e67280e7)] - **doc**: add ambassador message - benefits of Node.js (Michael Dawson) [#56085](https://github.com/nodejs/node/pull/56085)
- \[[`e4922ab15f`](https://github.com/nodejs/node/commit/e4922ab15f)] - **doc**: fix incorrect link to style guide (Yuan-Ming Hsu) [#56181](https://github.com/nodejs/node/pull/56181)
- \[[`114a3e5a05`](https://github.com/nodejs/node/commit/114a3e5a05)] - **doc**: fix c++ addon hello world sample (Edigleysson Silva (Edy)) [#56172](https://github.com/nodejs/node/pull/56172)
- \[[`f1c2d2f65e`](https://github.com/nodejs/node/commit/f1c2d2f65e)] - **doc**: update blog release-post link (Ruy Adorno) [#56123](https://github.com/nodejs/node/pull/56123)
- \[[`d48b5224c0`](https://github.com/nodejs/node/commit/d48b5224c0)] - **doc**: fix module.md headings (Chengzhong Wu) [#56131](https://github.com/nodejs/node/pull/56131)
- \[[`4cc0493a0b`](https://github.com/nodejs/node/commit/4cc0493a0b)] - **fs**: make mutating `options` in Callback `readdir()` not affect results (LiviaMedeiros) [#56057](https://github.com/nodejs/node/pull/56057)
- \[[`8d485f1c09`](https://github.com/nodejs/node/commit/8d485f1c09)] - **fs**: make mutating `options` in Promises `readdir()` not affect results (LiviaMedeiros) [#56057](https://github.com/nodejs/node/pull/56057)
- \[[`595851b5ed`](https://github.com/nodejs/node/commit/595851b5ed)] - **fs,win**: fix readdir for named pipe (Hüseyin Açacak) [#56110](https://github.com/nodejs/node/pull/56110)
- \[[`075b36b7b4`](https://github.com/nodejs/node/commit/075b36b7b4)] - **http**: add setDefaultHeaders option to http.request (Tim Perry) [#56112](https://github.com/nodejs/node/pull/56112)
- \[[`febd969c46`](https://github.com/nodejs/node/commit/febd969c46)] - **http2**: remove duplicate codeblock (Vitaly Aminev) [#55915](https://github.com/nodejs/node/pull/55915)
- \[[`b0ebd23e52`](https://github.com/nodejs/node/commit/b0ebd23e52)] - **http2**: support ALPNCallback option (ZYSzys) [#56187](https://github.com/nodejs/node/pull/56187)
- \[[`f10239fde7`](https://github.com/nodejs/node/commit/f10239fde7)] - **lib**: remove redundant global regexps (Gürgün Dayıoğlu) [#56182](https://github.com/nodejs/node/pull/56182)
- \[[`fd55d3cbdd`](https://github.com/nodejs/node/commit/fd55d3cbdd)] - **lib**: clean up persisted signals when they are settled (Edigleysson Silva (Edy)) [#56001](https://github.com/nodejs/node/pull/56001)
- \[[`889094fdbc`](https://github.com/nodejs/node/commit/889094fdbc)] - **lib**: handle Float16Array in node:v8 serdes (Bartek Iwańczuk) [#55996](https://github.com/nodejs/node/pull/55996)
- \[[`5aec513207`](https://github.com/nodejs/node/commit/5aec513207)] - **lib**: disable default memory leak warning for AbortSignal (Lenz Weber-Tronic) [#55816](https://github.com/nodejs/node/pull/55816)
- \[[`b5a2c0777d`](https://github.com/nodejs/node/commit/b5a2c0777d)] - **(SEMVER-MINOR)** **module**: add prefix-only modules to `module.builtinModules` (Jordan Harband) [#56185](https://github.com/nodejs/node/pull/56185)
- \[[`9863d27566`](https://github.com/nodejs/node/commit/9863d27566)] - **(SEMVER-MINOR)** **module**: only emit require(esm) warning under --trace-require-module (Joyee Cheung) [#56194](https://github.com/nodejs/node/pull/56194)
- \[[`5665e86da6`](https://github.com/nodejs/node/commit/5665e86da6)] - **module**: prevent main thread exiting before esm worker ends (Shima Ryuhei) [#56183](https://github.com/nodejs/node/pull/56183)
- \[[`8e780bc5ae`](https://github.com/nodejs/node/commit/8e780bc5ae)] - **(SEMVER-MINOR)** **module**: use synchronous hooks for preparsing in import(cjs) (Joyee Cheung) [#55698](https://github.com/nodejs/node/pull/55698)
- \[[`e5bb6c2303`](https://github.com/nodejs/node/commit/e5bb6c2303)] - **(SEMVER-MINOR)** **module**: implement module.registerHooks() (Joyee Cheung) [#55698](https://github.com/nodejs/node/pull/55698)
- \[[`f883bedceb`](https://github.com/nodejs/node/commit/f883bedceb)] - **node-api**: allow napi_delete_reference in finalizers (Chengzhong Wu) [#55620](https://github.com/nodejs/node/pull/55620)
- \[[`65bc8e847f`](https://github.com/nodejs/node/commit/65bc8e847f)] - **(SEMVER-MINOR)** **report**: fix typos in report keys and bump the version (Yuan-Ming Hsu) [#56068](https://github.com/nodejs/node/pull/56068)
- \[[`a6f0cfa468`](https://github.com/nodejs/node/commit/a6f0cfa468)] - **sea**: only assert snapshot main function for main threads (Joyee Cheung) [#56120](https://github.com/nodejs/node/pull/56120)
- \[[`0ab36e1937`](https://github.com/nodejs/node/commit/0ab36e1937)] - **(SEMVER-MINOR)** **sqlite**: aggregate constants in a single property (Edigleysson Silva (Edy)) [#56213](https://github.com/nodejs/node/pull/56213)
- \[[`4745798225`](https://github.com/nodejs/node/commit/4745798225)] - **sqlite**: add support for custom functions (Colin Ihrig) [#55985](https://github.com/nodejs/node/pull/55985)
- \[[`53cc0cc744`](https://github.com/nodejs/node/commit/53cc0cc744)] - **sqlite**: support `db.loadExtension` (Alex Yang) [#53900](https://github.com/nodejs/node/pull/53900)
- \[[`3968599702`](https://github.com/nodejs/node/commit/3968599702)] - **src**: fix outdated js2c.cc references (Chengzhong Wu) [#56133](https://github.com/nodejs/node/pull/56133)
- \[[`efcc5d90c5`](https://github.com/nodejs/node/commit/efcc5d90c5)] - **(SEMVER-MINOR)** **src,lib**: stabilize permission model (Rafael Gonzaga) [#56201](https://github.com/nodejs/node/pull/56201)
- \[[`a4a83613cb`](https://github.com/nodejs/node/commit/a4a83613cb)] - **stream**: commit pull-into descriptors after filling from queue (Mattias Buelens) [#56072](https://github.com/nodejs/node/pull/56072)
- \[[`3298ef4891`](https://github.com/nodejs/node/commit/3298ef4891)] - **test**: remove test-sqlite-statement-sync flaky designation (Luigi Pinca) [#56051](https://github.com/nodejs/node/pull/56051)
- \[[`1d8cc6179d`](https://github.com/nodejs/node/commit/1d8cc6179d)] - **test**: use --permission over --experimental-permission (Rafael Gonzaga) [#56239](https://github.com/nodejs/node/pull/56239)
- \[[`5d252b7a67`](https://github.com/nodejs/node/commit/5d252b7a67)] - **test**: remove exludes for sea tests on PPC (Michael Dawson) [#56217](https://github.com/nodejs/node/pull/56217)
- \[[`8288f57724`](https://github.com/nodejs/node/commit/8288f57724)] - **test**: fix test-abortsignal-drop-settled-signals flakiness (Edigleysson Silva (Edy)) [#56197](https://github.com/nodejs/node/pull/56197)
- \[[`683cc15796`](https://github.com/nodejs/node/commit/683cc15796)] - **test**: move localizationd data from `test-icu-env` to external file (Livia Medeiros) [#55618](https://github.com/nodejs/node/pull/55618)
- \[[`a0c4a5f122`](https://github.com/nodejs/node/commit/a0c4a5f122)] - **test**: update WPT for url to 6fa3fe8a92 (Node.js GitHub Bot) [#56136](https://github.com/nodejs/node/pull/56136)
- \[[`a0e3926285`](https://github.com/nodejs/node/commit/a0e3926285)] - **test**: remove `hasOpenSSL3x` utils (Antoine du Hamel) [#56164](https://github.com/nodejs/node/pull/56164)
- \[[`041a49094e`](https://github.com/nodejs/node/commit/041a49094e)] - **test**: update streams wpt (Mattias Buelens) [#56072](https://github.com/nodejs/node/pull/56072)
- \[[`ea9a675f56`](https://github.com/nodejs/node/commit/ea9a675f56)] - **test_runner**: exclude test files from coverage by default (Pietro Marchini) [#56060](https://github.com/nodejs/node/pull/56060)
- \[[`118cd9998f`](https://github.com/nodejs/node/commit/118cd9998f)] - **tools**: fix `node:` enforcement for docs (Antoine du Hamel) [#56284](https://github.com/nodejs/node/pull/56284)
- \[[`c4c56daae8`](https://github.com/nodejs/node/commit/c4c56daae8)] - **tools**: update github_reporter to 1.7.2 (Node.js GitHub Bot) [#56205](https://github.com/nodejs/node/pull/56205)
- \[[`78743b1533`](https://github.com/nodejs/node/commit/78743b1533)] - **tools**: add REPLACEME check to workflow (Mert Can Altin) [#56251](https://github.com/nodejs/node/pull/56251)
- \[[`002ee71d9b`](https://github.com/nodejs/node/commit/002ee71d9b)] - **tools**: use `github.actor` instead of bot username for release proposals (Antoine du Hamel) [#56232](https://github.com/nodejs/node/pull/56232)
- \[[`d25d16efeb`](https://github.com/nodejs/node/commit/d25d16efeb)] - _**Revert**_ "**tools**: disable automated libuv updates" (Luigi Pinca) [#56223](https://github.com/nodejs/node/pull/56223)
- \[[`b395e0c8c9`](https://github.com/nodejs/node/commit/b395e0c8c9)] - **tools**: update gyp-next to 0.19.1 (Anna Henningsen) [#56111](https://github.com/nodejs/node/pull/56111)
- \[[`a5aaf31c50`](https://github.com/nodejs/node/commit/a5aaf31c50)] - **tools**: fix release proposal linter to support more than 1 folk preparing (Antoine du Hamel) [#56203](https://github.com/nodejs/node/pull/56203)
- \[[`fa667d609e`](https://github.com/nodejs/node/commit/fa667d609e)] - **tools**: remove has_absl_stringify from gyp file (Michaël Zasso) [#56157](https://github.com/nodejs/node/pull/56157)
- \[[`65b541e70e`](https://github.com/nodejs/node/commit/65b541e70e)] - **tools**: enable linter for `tools/icu/**` (Livia Medeiros) [#56176](https://github.com/nodejs/node/pull/56176)
- \[[`28a4b6ff58`](https://github.com/nodejs/node/commit/28a4b6ff58)] - **tools**: use commit title as PR title when creating release proposal (Antoine du Hamel) [#56165](https://github.com/nodejs/node/pull/56165)
- \[[`e20eef659f`](https://github.com/nodejs/node/commit/e20eef659f)] - **tools**: update gyp-next to 0.19.0 (Node.js GitHub Bot) [#56158](https://github.com/nodejs/node/pull/56158)
- \[[`efcc829085`](https://github.com/nodejs/node/commit/efcc829085)] - **tools**: bump the eslint group in /tools/eslint with 4 updates (dependabot\[bot]) [#56099](https://github.com/nodejs/node/pull/56099)
- \[[`5620b2be8a`](https://github.com/nodejs/node/commit/5620b2be8a)] - **tools**: improve release proposal PR opening (Antoine du Hamel) [#56161](https://github.com/nodejs/node/pull/56161)
- \[[`3e17a8e78e`](https://github.com/nodejs/node/commit/3e17a8e78e)] - **util**: harden more built-in classes against prototype pollution (Antoine du Hamel) [#56225](https://github.com/nodejs/node/pull/56225)
- \[[`13815417c7`](https://github.com/nodejs/node/commit/13815417c7)] - **util**: fix Latin1 decoding to return string output (Mert Can Altin) [#56222](https://github.com/nodejs/node/pull/56222)
- \[[`77397c5013`](https://github.com/nodejs/node/commit/77397c5013)] - **util**: do not rely on mutable `Object` and `Function`' `constructor` prop (Antoine du Hamel) [#56188](https://github.com/nodejs/node/pull/56188)
- \[[`84f98e0a74`](https://github.com/nodejs/node/commit/84f98e0a74)] - **v8,tools**: expose experimental wasm revectorize feature (Yolanda-Chen) [#54896](https://github.com/nodejs/node/pull/54896)
- \[[`8325fa5c04`](https://github.com/nodejs/node/commit/8325fa5c04)] - **worker**: fix crash when a worker joins after exit (Stephen Belanger) [#56191](https://github.com/nodejs/node/pull/56191)

Windows 64-bit Installer: https://nodejs.org/dist/v23.5.0/node-v23.5.0-x64.msi \
Windows ARM 64-bit Installer: https://nodejs.org/dist/v23.5.0/node-v23.5.0-arm64.msi \
Windows 64-bit Binary: https://nodejs.org/dist/v23.5.0/win-x64/node.exe \
Windows ARM 64-bit Binary: https://nodejs.org/dist/v23.5.0/win-arm64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v23.5.0/node-v23.5.0.pkg \
macOS Apple Silicon 64-bit Binary: https://nodejs.org/dist/v23.5.0/node-v23.5.0-darwin-arm64.tar.gz \
macOS Intel 64-bit Binary: https://nodejs.org/dist/v23.5.0/node-v23.5.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v23.5.0/node-v23.5.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v23.5.0/node-v23.5.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v23.5.0/node-v23.5.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v23.5.0/node-v23.5.0-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v23.5.0/node-v23.5.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v23.5.0/node-v23.5.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v23.5.0/node-v23.5.0.tar.gz \
Other release files: https://nodejs.org/dist/v23.5.0/ \
Documentation: https://nodejs.org/docs/v23.5.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

5b487cdc9b490fb617201e4bc8b5ac247c1871fa8108857e6868e12527d6df19  node-v23.5.0-aix-ppc64.tar.gz
47b9aa8724b8a498c631dcede71515e450f6486734d3cf42a573f9aa682cf6a4  node-v23.5.0-arm64.msi
71b1ca0e4d776b9b7648781303d8c5eb12be32c5ed0df4f97a26fd779bc7ef80  node-v23.5.0-darwin-arm64.tar.gz
14bcad06df6a19b327dc8329e91c280cc5025be12e5af6cabac1b4731c24b06f  node-v23.5.0-darwin-arm64.tar.xz
887506572511e4b90e4aab044db491ac4836c014904ae7537a9ca5c11b47c26d  node-v23.5.0-darwin-x64.tar.gz
9da8d10ed0b910aae01c08c78950c6c53a04bf3f89bbf288d665b2ad85ddf54a  node-v23.5.0-darwin-x64.tar.xz
7d711273edc69f59e64965adbee97ef14c419192ea2b3537fadc1fa2aa2da48d  node-v23.5.0-headers.tar.gz
2a40c0fd34bdb8ecc9ecc3cfd6bc4abfffd98f42448ee482fb1dc87eabc9da95  node-v23.5.0-headers.tar.xz
209018ce7a867f5082cbdfdc91f379f49d021a58252cc65c5488c591004a750c  node-v23.5.0-linux-arm64.tar.gz
99087f807262b44a39836d41a95dd132c805034c779121580099e964b8792522  node-v23.5.0-linux-arm64.tar.xz
599411676cdb5ba7fb472a74b37957eec0ef8f97269ba8ca77ce36f14f0c3554  node-v23.5.0-linux-armv7l.tar.gz
cc6c7e5ff2a8bf3972a01bb3197292dc086635ccf4e4189e38c4d53cb969fa94  node-v23.5.0-linux-armv7l.tar.xz
21d158927d10d23d31cfa2673667be238a23520daedfd855a8978ee78df511f3  node-v23.5.0-linux-ppc64le.tar.gz
216669da7486cc40dbf82b08ceb97bd589064b6b8b5affa17e115f13d51062e7  node-v23.5.0-linux-ppc64le.tar.xz
13aea5d8d4ac3fd0aebcfaa762760415ab57eb1e92d8e7f47d399bae23211b71  node-v23.5.0-linux-s390x.tar.gz
11777040ffeaff5597e82b698391626e9dd069eaa690773c63dc294e74a149f8  node-v23.5.0-linux-s390x.tar.xz
72ca246aafffeac41486bf5594b0223f9a28e7c3d9081a07884b75cc21cf9e8a  node-v23.5.0-linux-x64.tar.gz
f3c02df735945267f886f3ea6f7e28d4bb33fe36e12bec77d10b9fa6a12c6279  node-v23.5.0-linux-x64.tar.xz
b390fbdfcac5cbe541f8b1dcbd6f71f074d235084f72d75571278e172ac3e460  node-v23.5.0-win-arm64.7z
8e8f2035a72fdb27a93c4e2275e89201682d386b0026ce2b78a50f650602ff0c  node-v23.5.0-win-arm64.zip
4d86167dd98801c723a13e65519822d409b603acbec9b0d1107a4ad8578a7c53  node-v23.5.0-win-x64.7z
e997a1fa51e4555632f54f205e121104f328db2ba058274a3f9da26fe3af3a6f  node-v23.5.0-win-x64.zip
52f7d56901398827ed83ea282eefc49becf139737df0f47c3c78fd048d193eed  node-v23.5.0-x64.msi
e064e69b5b9fa0ef61fe09ed925b4a8a3ac19ff96ae1b987147917ab0b23dbef  node-v23.5.0.pkg
d6c29133361faa7dd7dfda353cb15f150683a2f5c6fe6a8d19c4dcb1594c80e4  node-v23.5.0.tar.gz
32e77b36c0774c68baab41bc7c2acc58663ca0a2b7c4d3e9bec6f761c15fdac0  node-v23.5.0.tar.xz
4308e6d90e22c6545fe97f8fc1537f2b5ffae5360f4672ebe4957dc74770649b  win-arm64/node.exe
46a85ee5432885387948e02ced2499975b34cc2080ea3ed4e99b6c7323d38d74  win-arm64/node.lib
65b3e9c20a708d30ce3246d0ca7034db99dfe039ad375dfe8820851940b2b02e  win-arm64/node_pdb.7z
0ddf817017304898f9330425b95a15eb4f3810f0e16a843e4565d37265fa7f7b  win-arm64/node_pdb.zip
0f91994f833bc232c990fee8694ca00d4d60c67f6cd0f7f0633c9abc34c91888  win-x64/node.exe
4a54d5aeab30bb14dbad9260f6430c81f1e1afb430c68c40f6e5fec7ce288e2e  win-x64/node.lib
9f256078ae21ed3bc7da3866181b8c75ed67efd718f4a13f26657a22af8af0c4  win-x64/node_pdb.7z
76a85bea6ba187c384c13b4708e5b8c3a74b3994fd291ecee161c08c05c71d0e  win-x64/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEwNYkhDnx1WBKr/tAIdkA/9sjN1YFAmdka/cACgkQIdkA/9sj
N1ZnaBAAqCD1nSAaPHR1U7dv7cHhiTSfzz6bVm+jWJ0DGRv+idSa48ezVnGcd22a
lIomL168Dl6BzdlMrcppLMIxO2FiV3hbbkOc50kBUzp0AsaQfbGgdJ2AhvMbaawx
8U8Pzxnp3KZ6WVM+3my4+jzz0/74aPY9AeUQWqL0fT7jCupAKvUu9680yGiA0UOB
DcHEYEhDWVzPmdkIeoSsd2IWMMtAUpwRuXmx48GZ7dMDmSCHY2JrRxkffD3PEG3t
eMyYdQYaQZMQ0p/PScjaUqT1SB5g0nZ9h9z67ToxJJg/fTlyNrwzyoNOCr9kfyP/
dl6b5ZyypIkkl2qpaVd+a+L/u5Q5L9RWBfI56x5iC7UTg5BG5EcXrl6KWcUC+qmC
O0NPBmHZlPq0uc5epgaujuCvryAyF1C6PD3JJfJUQdnrBg/7MZ0uojANfO9ad+ne
Di10c5VBPBq6WHG+UDUW3WrEDqUzC7aCVEiPjIe3fHlqF7/RD7T0+In6U+RmwSrB
jt4dkCqc4FNNkrVVXO80NS2N2MQtmwo0jIgZoP5VWnMGQwuwjbvwKrL+YWM+zTZZ
WrJeP03CHERqc8xzV4/MuXDqlVi/1cTIHSHCPH7j2nF3uiZh2+vtNZjgF4SKY29t
9+mLttox196OyaBoK1vhcuPWunYMDz72xIBI+9RndbjHabWWaNA=
=4PF6
-----END PGP SIGNATURE-----
```
