---
date: '2016-03-02T23:23:06.216Z'
category: release
title: Node v5.7.1 (Current)
layout: blog-post
author: <PERSON>
---

### Notable changes

- **governance**: The Core Technical Committee (CTC) added four new members to help guide Node.js core development: <PERSON>, <PERSON>, <PERSON> and <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Ни<PERSON><PERSON><PERSON><PERSON> (<PERSON><PERSON>).
- **openssl**: Upgrade from 1.0.2f to 1.0.2g (<PERSON>) [#5507](https://github.com/nodejs/node/pull/5507).
  - Fix a double-free defect in parsing malformed DSA keys that may potentially be used for DoS or memory corruption attacks. It is likely to be very difficult to use this defect for a practical attack and is therefore considered low severity for Node.js users. More info is available at [CVE-2016-0705](https://www.openssl.org/news/vulnerabilities.html#2016-0705).
  - Fix a defect that can cause memory corruption in certain very rare cases relating to the internal `BN_hex2bn()` and `BN_dec2bn()` functions. It is believed that Node.js is not invoking the code paths that use these functions so practical attacks via Node.js using this defect are _unlikely_ to be possible. More info is available at [CVE-2016-0797](https://www.openssl.org/news/vulnerabilities.html#2016-0797).
  - Fix a defect that makes the _[CacheBleed Attack](https://ssrg.nicta.com.au/projects/TS/cachebleed/)_ possible. This defect enables attackers to execute side-channel attacks leading to the potential recovery of entire RSA private keys. It only affects the Intel Sandy Bridge (and possibly older) microarchitecture when using hyper-threading. Newer microarchitectures, including Haswell, are unaffected. More info is available at [CVE-2016-0702](https://www.openssl.org/news/vulnerabilities.html#2016-0702).
- Fixed several regressions that appeared in v5.7.0:
  - **`path.relative()`**:
    - Output is no longer unnecessarily verbose (Brian White) [#5389](https://github.com/nodejs/node/pull/5389).
    - Resolving UNC paths on Windows now works correctly (Owen Smith) [#5456](https://github.com/nodejs/node/pull/5456).
    - Resolving paths with prefixes now works correctly from the root directory (Owen Smith) [#5490](https://github.com/nodejs/node/pull/5490).
  - **url**: Fixed an off-by-one error with `parse()` (Brian White) [#5394](https://github.com/nodejs/node/pull/5394).
  - **dgram**: Now correctly handles a default address case when offset and length are specified (Matteo Collina) [#5407](https://github.com/nodejs/node/pull/5407).

### Commits

- [[`7cae774d9b`](https://github.com/nodejs/node/commit/7cae774d9b)] - **benchmark**: refactor to eliminate redeclared vars (Rich Trott) [#5468](https://github.com/nodejs/node/pull/5468)
- [[`6aebe16669`](https://github.com/nodejs/node/commit/6aebe16669)] - **benchmark**: add benchmark for buf.compare() (Rich Trott) [#5441](https://github.com/nodejs/node/pull/5441)
- [[`00660f55c8`](https://github.com/nodejs/node/commit/00660f55c8)] - **benchmark**: move string-decoder to its own category (Andreas Madsen) [#5177](https://github.com/nodejs/node/pull/5177)
- [[`4650cb3818`](https://github.com/nodejs/node/commit/4650cb3818)] - **benchmark**: fix configuation parameters (Andreas Madsen) [#5177](https://github.com/nodejs/node/pull/5177)
- [[`3ccb275139`](https://github.com/nodejs/node/commit/3ccb275139)] - **benchmark**: merge url.js with url-resolve.js (Andreas Madsen) [#5177](https://github.com/nodejs/node/pull/5177)
- [[`c1e7dbffaa`](https://github.com/nodejs/node/commit/c1e7dbffaa)] - **benchmark**: move misc to categorized directories (Andreas Madsen) [#5177](https://github.com/nodejs/node/pull/5177)
- [[`2f9fee6e8e`](https://github.com/nodejs/node/commit/2f9fee6e8e)] - **benchmark**: use strict mode (Rich Trott) [#5336](https://github.com/nodejs/node/pull/5336)
- [[`4c09e7f359`](https://github.com/nodejs/node/commit/4c09e7f359)] - **build**: remove --quiet from eslint invocation (firedfox) [#5519](https://github.com/nodejs/node/pull/5519)
- [[`2c619f2012`](https://github.com/nodejs/node/commit/2c619f2012)] - **build**: run lint before tests (Rich Trott) [#5470](https://github.com/nodejs/node/pull/5470)
- [[`f349a9a2cf`](https://github.com/nodejs/node/commit/f349a9a2cf)] - **build**: update Node.js logo on OSX installer (Rod Vagg) [#5401](https://github.com/nodejs/node/pull/5401)
- [[`88f393588a`](https://github.com/nodejs/node/commit/88f393588a)] - **crypto**: PBKDF2 works with `int` not `ssize_t` (Fedor Indutny) [#5397](https://github.com/nodejs/node/pull/5397)
- [[`1e86804503`](https://github.com/nodejs/node/commit/1e86804503)] - **deps**: upgrade openssl to 1.0.2g (Ben Noordhuis) [#5507](https://github.com/nodejs/node/pull/5507)
- [[`d3f9b84be8`](https://github.com/nodejs/node/commit/d3f9b84be8)] - **dgram**: handle default address case when offset and length are specified (Matteo Collina)
- [[`f1f3832934`](https://github.com/nodejs/node/commit/f1f3832934)] - **doc**: update NAN urls in ROADMAP.md and doc/releases.md (ronkorving) [#5472](https://github.com/nodejs/node/pull/5472)
- [[`51bc062dab`](https://github.com/nodejs/node/commit/51bc062dab)] - **doc**: add CTC meeting minutes 2016-02-17 (Rod Vagg) [#5410](https://github.com/nodejs/node/pull/5410)
- [[`795c85ba1c`](https://github.com/nodejs/node/commit/795c85ba1c)] - **doc**: fix typo in child_process documentation (Evan Lucas) [#5474](https://github.com/nodejs/node/pull/5474)
- [[`0a56e9690b`](https://github.com/nodejs/node/commit/0a56e9690b)] - **doc**: add note for binary safe string reading (Anton Andesen) [#5155](https://github.com/nodejs/node/pull/5155)
- [[`ea8331e15f`](https://github.com/nodejs/node/commit/ea8331e15f)] - **doc**: improvements to crypto.markdown copy (Alexander Makarenko) [#5230](https://github.com/nodejs/node/pull/5230)
- [[`378a772034`](https://github.com/nodejs/node/commit/378a772034)] - **doc**: `require` behavior on case-insensitive systems (Hugo Wood)
- [[`06b7eb6636`](https://github.com/nodejs/node/commit/06b7eb6636)] - **doc**: document base64url encoding support (Tristan Slominski) [#5243](https://github.com/nodejs/node/pull/5243)
- [[`8ec3d904cb`](https://github.com/nodejs/node/commit/8ec3d904cb)] - **doc**: improve httpVersionMajor / httpVersionMajor (Jackson Tian) [#5296](https://github.com/nodejs/node/pull/5296)
- [[`534e88f56c`](https://github.com/nodejs/node/commit/534e88f56c)] - **doc**: fix relative links in net docs (Evan Lucas) [#5358](https://github.com/nodejs/node/pull/5358)
- [[`7b98a30976`](https://github.com/nodejs/node/commit/7b98a30976)] - **doc**: fix crypto function indentation level (Brian White) [#5460](https://github.com/nodejs/node/pull/5460)
- [[`c0fd802cc2`](https://github.com/nodejs/node/commit/c0fd802cc2)] - **doc**: link to man pages (<EMAIL>) [#5073](https://github.com/nodejs/node/pull/5073)
- [[`f8c6701e22`](https://github.com/nodejs/node/commit/f8c6701e22)] - **doc**: add missing property in cluster example (Rafael Cepeda) [#5305](https://github.com/nodejs/node/pull/5305)
- [[`3bfe0483f0`](https://github.com/nodejs/node/commit/3bfe0483f0)] - **doc**: corrected name of argument in socket.send (Chris Dew) [#5449](https://github.com/nodejs/node/pull/5449)
- [[`c8725f5e95`](https://github.com/nodejs/node/commit/c8725f5e95)] - **doc**: fix links in tls, cluster docs (Alexander Makarenko) [#5364](https://github.com/nodejs/node/pull/5364)
- [[`7f2cf9af5c`](https://github.com/nodejs/node/commit/7f2cf9af5c)] - **doc**: explicit about VS 2015 support in readme (Phillip Johnsen) [#5406](https://github.com/nodejs/node/pull/5406)
- [[`12d3cdbfea`](https://github.com/nodejs/node/commit/12d3cdbfea)] - **doc**: remove out-of-date matter from internal docs (Rich Trott) [#5421](https://github.com/nodejs/node/pull/5421)
- [[`43853679f7`](https://github.com/nodejs/node/commit/43853679f7)] - **doc**: copyedit util doc (Rich Trott) [#5399](https://github.com/nodejs/node/pull/5399)
- [[`903e8d09e1`](https://github.com/nodejs/node/commit/903e8d09e1)] - **doc**: fix typo in pbkdf2Sync code sample (Marc Cuva) [#5306](https://github.com/nodejs/node/pull/5306)
- [[`79b1c22c9f`](https://github.com/nodejs/node/commit/79b1c22c9f)] - **doc**: fix buf.readInt16LE output (Chinedu Francis Nwafili) [#5282](https://github.com/nodejs/node/pull/5282)
- [[`e46915f2f3`](https://github.com/nodejs/node/commit/e46915f2f3)] - **doc**: note util.isError() @@toStringTag limitations (cjihrig) [#5414](https://github.com/nodejs/node/pull/5414)
- [[`935fd21fff`](https://github.com/nodejs/node/commit/935fd21fff)] - **doc**: clarify error handling in net.createServer (Dirceu Pereira Tiegs) [#5353](https://github.com/nodejs/node/pull/5353)
- [[`93dce6d4fe`](https://github.com/nodejs/node/commit/93dce6d4fe)] - **doc**: document fs.datasync(Sync) (Ron Korving) [#5402](https://github.com/nodejs/node/pull/5402)
- [[`96daf51358`](https://github.com/nodejs/node/commit/96daf51358)] - **doc**: add Evan Lucas to the CTC (Rod Vagg) [#5275](https://github.com/nodejs/node/pull/5275)
- [[`31b405d0cf`](https://github.com/nodejs/node/commit/31b405d0cf)] - **doc**: add Rich Trott to the CTC (Rod Vagg) [#5276](https://github.com/nodejs/node/pull/5276)
- [[`bcd154e402`](https://github.com/nodejs/node/commit/bcd154e402)] - **doc**: add Ali Ijaz Sheikh to the CTC (Rod Vagg) [#5277](https://github.com/nodejs/node/pull/5277)
- [[`9d0330c804`](https://github.com/nodejs/node/commit/9d0330c804)] - **doc**: add Сковорода Никита Андреевич to the CTC (Rod Vagg) [#5278](https://github.com/nodejs/node/pull/5278)
- [[`365cc63783`](https://github.com/nodejs/node/commit/365cc63783)] - **doc**: add "building node with ninja" guide (Jeremiah Senkpiel) [#4767](https://github.com/nodejs/node/pull/4767)
- [[`2b00c315e1`](https://github.com/nodejs/node/commit/2b00c315e1)] - **doc**: mention prototype check in deepStrictEqual() (cjihrig) [#5367](https://github.com/nodejs/node/pull/5367)
- [[`ff988b3ee6`](https://github.com/nodejs/node/commit/ff988b3ee6)] - **doc,tools,test**: lint doc-based addon tests (Rich Trott) [#5427](https://github.com/nodejs/node/pull/5427)
- [[`d77c3bf204`](https://github.com/nodejs/node/commit/d77c3bf204)] - **http_parser**: use `MakeCallback` (Trevor Norris) [#5419](https://github.com/nodejs/node/pull/5419)
- [[`e3421ac296`](https://github.com/nodejs/node/commit/e3421ac296)] - **lib**: freelist: use .pop() for allocation (Anton Khlynovskiy) [#2174](https://github.com/nodejs/node/pull/2174)
- [[`91d218d096`](https://github.com/nodejs/node/commit/91d218d096)] - **path**: fix path.relative() for prefixes at root (Owen Smith) [#5490](https://github.com/nodejs/node/pull/5490)
- [[`ef7a088906`](https://github.com/nodejs/node/commit/ef7a088906)] - **path**: fix win32 parse() (Zheng Chaoping) [#5484](https://github.com/nodejs/node/pull/5484)
- [[`871396ce8f`](https://github.com/nodejs/node/commit/871396ce8f)] - **path**: fix win32 relative() for UNC paths (Owen Smith) [#5456](https://github.com/nodejs/node/pull/5456)
- [[`91782f1888`](https://github.com/nodejs/node/commit/91782f1888)] - **path**: fix win32 relative() when "to" is a prefix (Owen Smith) [#5456](https://github.com/nodejs/node/pull/5456)
- [[`30cec18eeb`](https://github.com/nodejs/node/commit/30cec18eeb)] - **path**: fix verbose relative() output (Brian White) [#5389](https://github.com/nodejs/node/pull/5389)
- [[`2b88523836`](https://github.com/nodejs/node/commit/2b88523836)] - **repl**: fix stack trace column number in strict mode (Prince J Wesley) [#5416](https://github.com/nodejs/node/pull/5416)
- [[`51db48f741`](https://github.com/nodejs/node/commit/51db48f741)] - **src,tools**: remove null sentinel from source array (Ben Noordhuis) [#5418](https://github.com/nodejs/node/pull/5418)
- [[`03a5daba55`](https://github.com/nodejs/node/commit/03a5daba55)] - **src,tools**: drop nul byte from built-in source code (Ben Noordhuis) [#5418](https://github.com/nodejs/node/pull/5418)
- [[`17d14f3346`](https://github.com/nodejs/node/commit/17d14f3346)] - **src,tools**: allow utf-8 in built-in js source code (Ben Noordhuis) [#5418](https://github.com/nodejs/node/pull/5418)
- [[`12ae6abc69`](https://github.com/nodejs/node/commit/12ae6abc69)] - **test**: increase timeout for test-tls-fast-writing (Rich Trott) [#5466](https://github.com/nodejs/node/pull/5466)
- [[`81348e8855`](https://github.com/nodejs/node/commit/81348e8855)] - **test**: apply Linux workaround to Linux only (Rich Trott) [#5471](https://github.com/nodejs/node/pull/5471)
- [[`c4d9cdb7d0`](https://github.com/nodejs/node/commit/c4d9cdb7d0)] - **test**: allow options for v8 testing (Michael Dawson) [#5502](https://github.com/nodejs/node/pull/5502)
- [[`d1a82c6824`](https://github.com/nodejs/node/commit/d1a82c6824)] - **test**: retry on known SmartOS bug (Rich Trott) [#5454](https://github.com/nodejs/node/pull/5454)
- [[`c7f8a13043`](https://github.com/nodejs/node/commit/c7f8a13043)] - **test**: remove unneeded bind() and related comments (Aayush Naik) [#5023](https://github.com/nodejs/node/pull/5023)
- [[`cc4cbb10df`](https://github.com/nodejs/node/commit/cc4cbb10df)] - **test**: fix flaky child-process-fork-regr-gh-2847 (Santiago Gimeno) [#5422](https://github.com/nodejs/node/pull/5422)
- [[`0ebbf6cd53`](https://github.com/nodejs/node/commit/0ebbf6cd53)] - **test**: remove flaky designation from fixed tests (Rich Trott) [#5459](https://github.com/nodejs/node/pull/5459)
- [[`c83725c604`](https://github.com/nodejs/node/commit/c83725c604)] - **test**: add test-cases for posix path.relative() (Owen Smith) [#5456](https://github.com/nodejs/node/pull/5456)
- [[`22bb7c9d27`](https://github.com/nodejs/node/commit/22bb7c9d27)] - **test**: fix test runner arg regression (Stefan Budeanu) [#5446](https://github.com/nodejs/node/pull/5446)
- [[`8c67b94b11`](https://github.com/nodejs/node/commit/8c67b94b11)] - **test**: refactor test-dgram-send-callback-recursive (Santiago Gimeno) [#5079](https://github.com/nodejs/node/pull/5079)
- [[`2c21d34a2f`](https://github.com/nodejs/node/commit/2c21d34a2f)] - **test**: refactor test-dgram-udp4 (Santiago Gimeno) [#5339](https://github.com/nodejs/node/pull/5339)
- [[`479a43c876`](https://github.com/nodejs/node/commit/479a43c876)] - **test**: allow passing args to executable (Stefan Budeanu) [#5376](https://github.com/nodejs/node/pull/5376)
- [[`ff75023812`](https://github.com/nodejs/node/commit/ff75023812)] - **test**: fix test-timers.reliability on OS X (Rich Trott) [#5379](https://github.com/nodejs/node/pull/5379)
- [[`991f82b4bd`](https://github.com/nodejs/node/commit/991f82b4bd)] - **test**: mitigate flaky test-http-agent (Rich Trott) [#5346](https://github.com/nodejs/node/pull/5346)
- [[`0f54553a99`](https://github.com/nodejs/node/commit/0f54553a99)] - **test**: increase timeouts on some unref timers tests (Jeremiah Senkpiel) [#5352](https://github.com/nodejs/node/pull/5352)
- [[`25c01cd779`](https://github.com/nodejs/node/commit/25c01cd779)] - **tls**: fix assert in context.\_external accessor (Ben Noordhuis) [#5521](https://github.com/nodejs/node/pull/5521)
- [[`5ffd7430d1`](https://github.com/nodejs/node/commit/5ffd7430d1)] - **tools**: apply custom buffer lint rule to /lib only (Rich Trott) [#5371](https://github.com/nodejs/node/pull/5371)
- [[`fa5d28f246`](https://github.com/nodejs/node/commit/fa5d28f246)] - **tools**: enable additional lint rules (Rich Trott) [#5357](https://github.com/nodejs/node/pull/5357)
- [[`b44b701e5b`](https://github.com/nodejs/node/commit/b44b701e5b)] - **tools,benchmark**: increase lint compliance (Rich Trott) [#5429](https://github.com/nodejs/node/pull/5429)
- [[`9424fa5732`](https://github.com/nodejs/node/commit/9424fa5732)] - **url**: group slashed protocols by protocol name (nettofarah) [#5380](https://github.com/nodejs/node/pull/5380)
- [[`dfe45f13e7`](https://github.com/nodejs/node/commit/dfe45f13e7)] - **url**: fix off-by-one error with parse() (Brian White) [#5394](https://github.com/nodejs/node/pull/5394)

Windows 32-bit Installer: https://nodejs.org/dist/v5.7.1/node-v5.7.1-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v5.7.1/node-v5.7.1-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v5.7.1/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v5.7.1/win-x64/node.exe \
Mac OS X 64-bit Installer: https://nodejs.org/dist/v5.7.1/node-v5.7.1.pkg \
Mac OS X 64-bit Binary: https://nodejs.org/dist/v5.7.1/node-v5.7.1-darwin-x64.tar.gz \
Linux 32-bit Binary: https://nodejs.org/dist/v5.7.1/node-v5.7.1-linux-x86.tar.xz \
Linux 64-bit Binary: https://nodejs.org/dist/v5.7.1/node-v5.7.1-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v5.7.1/node-v5.7.1-linux-ppc64le.tar.xz \
SmartOS 32-bit Binary: https://nodejs.org/dist/v5.7.1/node-v5.7.1-sunos-x86.tar.xz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v5.7.1/node-v5.7.1-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v5.7.1/node-v5.7.1-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v5.7.1/node-v5.7.1-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v5.7.1/node-v5.7.1-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v5.7.1/node-v5.7.1.tar.gz \
Other release files: https://nodejs.org/dist/v5.7.1/ \
Documentation: https://nodejs.org/docs/v5.7.1/api/

Shasums (GPG signing hash: SHA512, file hash: SHA256):

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA512

25627633163e6ad47e62cd9aaab04e47707b51ecc5aaa05f35a2d6419dbe054c  node-v5.7.1-darwin-x64.tar.gz
7d23cb68d8587a6baa8f9023bf326430ca50d669817bac2cd6129cdb435ac0c3  node-v5.7.1-darwin-x64.tar.xz
6d2241bc1e5049de3651873d63b84c37f8684027b1a34928ce6bbe745057ca70  node-v5.7.1-headers.tar.gz
655cbc2da0808edbb965fbb698713ea022e9e7d311604e0ae73904de59df528c  node-v5.7.1-headers.tar.xz
b075aa249eb1e00e1e84e6f5964d4f93c39aa6d817c25280bf885bfcf906c7fc  node-v5.7.1-linux-arm64.tar.gz
61f411e5352d10533cfde00ff2e5b3bc0c31326d46e4e76a0736cf44e2f031ea  node-v5.7.1-linux-arm64.tar.xz
ddde92eb9b80e58efec88138fa7351e21000b22414ebff754c7ab38dedbab89f  node-v5.7.1-linux-armv6l.tar.gz
dd1ade05801824f0386a43890c1500715e628ac4a78b85eb6f2949edad5eaa7c  node-v5.7.1-linux-armv6l.tar.xz
2370e6ad65dedc1eadcc1b3a481e922a8b5afacee9e90eb6700fee876eb59a5b  node-v5.7.1-linux-armv7l.tar.gz
4617186ac4e615f2166dbbd0ef923971d37af630f4aad85cd93bacba74c56fb9  node-v5.7.1-linux-armv7l.tar.xz
f3de80e67a3f055bb3e2ecdb9e4b6cc45183a6cbee2b728f243d46a6fd94f542  node-v5.7.1-linux-ppc64le.tar.gz
81b0e67145e317f4c6f9be58a3485990285fdd866f2c11509ffc68927d2e75d4  node-v5.7.1-linux-ppc64le.tar.xz
fcded78b45549e2195eecb36138ba29b6f353d0d136d4e8b80648770418f1e5b  node-v5.7.1-linux-x64.tar.gz
5584895f87a57ee26a75bf8505f6a2350a3ab219accf1df44c2f6406ddfad6ed  node-v5.7.1-linux-x64.tar.xz
4382b7366f3448d42f09d63f7dfccf45bf67f8be94d2bfe7203bdc79d0ae64e8  node-v5.7.1-linux-x86.tar.gz
6a9063fd23bf4e14d966f18b3cf092192f2a05daf8e6253b92adc2be422959e1  node-v5.7.1-linux-x86.tar.xz
d96c04a7df03f630e66fdf5173912cfb1e412bba4453aad67e007d5bb59d16b4  node-v5.7.1.pkg
0ebab64c25a417b741f666db1765c7358e413d895d4dd0ca9e967fdbb1770a72  node-v5.7.1-sunos-x64.tar.gz
8f7bc96926e75971ade13c183378929442021eca5055752b0b47601b60b1a474  node-v5.7.1-sunos-x64.tar.xz
1deb8d1eee46dfdb15c01fea1b283cc9115d78d2e4757ab6192b80bf52d61f99  node-v5.7.1-sunos-x86.tar.gz
43221b7de7c0e684d65e1898244f876deb25c3e0d614e7f01261a6af5e39f3b7  node-v5.7.1-sunos-x86.tar.xz
ecd4956335c6b86f1b72fd203abd9a833717e6800edb12208d0e3ce625330198  node-v5.7.1.tar.gz
029ed8aeb997c68d1a901cd837af02d24b6b5e20a4ff93bed090fd289f91a9a1  node-v5.7.1.tar.xz
d453670c8edf551fc319fcb997cae8e19180aa09593d0228730fc8997c02316c  node-v5.7.1-x64.msi
f486032411c50a6ea40d361067eb78d2e383737f72d0c14e081e04aff23e6991  node-v5.7.1-x86.msi
de17f951cc28bcd90015703b23e153d19673ee476016d923ff9e5588a6f17593  win-x64/node.exe
752e5b5ea84e7bc828582bceb5ff2faade62234d63f4924ac935c0cc0d2c1bdf  win-x64/node.lib
249614b8ab6106d91229c1b3c2732b0985334e9b3c28c25bff2d681ab5730a56  win-x86/node.exe
4237fb7203656e3caffb4af4ea7875957a4d26860576de52767abfed88f5f7b8  win-x86/node.lib
-----BEGIN PGP SIGNATURE-----
Comment: GPGTools - https://gpgtools.org

iQIcBAEBCgAGBQJW13WxAAoJEEX17r2BPa6OTokP/At3k4PP5DuSviM3hWGINJUB
FHgtwN5ZhV2L07mLwOsD2uogvjBIe8FnNPtLiB1On80E17lMyvUC8GfYmaxG1QWx
fYIMA/vDPtEE9ckUJ0zF800z9nECjLbFQ1aJ2iCz5yPS7rg/1RsMokpGa5+PbrRB
L5DPBU+I9ZImiQKVF4cscIPfCdBd9itPwVSBLNn9silnAdX4s8kMcIg0O3uXI2S5
k3A3b7tsUDEWhdg3uq4R3t/Y8uzsjpywYRbJQMqQ67RBFE64N3eRRygUWKOgmyob
rPZNAsARu0k3ORn48gfauCTaxxqsdVd3Wrp2thFD5KOs8/TVXKjIrKkoNynGNgTE
S+iWCWCj7n7inGMslq6hHCzxwgbhtRQKMyQW7uGJak4OhrlNsn/7GW9HDTQDxSof
SQgt62++j3wJ4U9Ywl18ZB790BmjxIzH6K5qxK+sW6cADHXRobeYWX/IU5c2y+Gz
wKMMHejQlSPZM4aZYJlBuUcWlkex7o5Mhkpu5hq+OWIN9c2uPJJ35Jh5m/ZGL8AL
ZmlrFU2esrO0a7R2PV0+9ESG7x8aizvY7vquuVdF3uMn3CrfWG0ssp/xWpAHWorK
qUUdd4fdX57rdxuyWj58t7Iq2ri6RxgNf2nnZiFyUyfGdsOzyrNT6ES5exnjdzxw
K5Jnwx41hOBpEJ1XMwRn
=8dum
-----END PGP SIGNATURE-----

```
