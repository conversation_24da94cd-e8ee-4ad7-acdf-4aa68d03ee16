---
date: '2023-08-09T17:55:32.535Z'
category: release
title: Node v20.5.1 (Current)
layout: blog-post
author: <PERSON>
---

### Notable Changes

The following CVEs are fixed in this release:

- [CVE-2023-32002](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2023-32002): Policies can be bypassed via Module.\_load (High)
- [CVE-2023-32558](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2023-32558): process.binding() can bypass the permission model through path traversal (High)
- [CVE-2023-32004](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2023-32004): Permission model can be bypassed by specifying a path traversal sequence in a Buffer (High)
- [CVE-2023-32006](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2023-32006): Policies can be bypassed by module.constructor.createRequire (Medium)
- [CVE-2023-32559](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2023-32559): Policies can be bypassed via process.binding (Medium)
- [CVE-2023-32005](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2023-32005): fs.statfs can bypass the permission model (Low)
- [CVE-2023-32003](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2023-32003): fs.mkdtemp() and fs.mkdtempSync() can bypass the permission model (Low)
- OpenSSL Security Releases
  - [OpenSSL security advisory 14th July](https://mta.openssl.org/pipermail/openssl-announce/2023-July/000264.html).
  - [OpenSSL security advisory 19th July](https://mta.openssl.org/pipermail/openssl-announce/2023-July/000265.html).
  - [OpenSSL security advisory 31st July](https://mta.openssl.org/pipermail/openssl-announce/2023-July/000267.html)

More detailed information on each of the vulnerabilities can be found in [August 2023 Security Releases](/blog/vulnerability/august-2023-security-releases/) blog post.

### Commits

- \[[`92300b51b4`](https://github.com/nodejs/node/commit/92300b51b4)] - **deps**: update archs files for openssl-3.0.10+quic1 (Node.js GitHub Bot) [#49036](https://github.com/nodejs/node/pull/49036)
- \[[`559698abf2`](https://github.com/nodejs/node/commit/559698abf2)] - **deps**: upgrade openssl sources to quictls/openssl-3.0.10+quic1 (Node.js GitHub Bot) [#49036](https://github.com/nodejs/node/pull/49036)
- \[[`1bf3429e8e`](https://github.com/nodejs/node/commit/1bf3429e8e)] - **lib,permission**: restrict process.binding when pm is enabled (RafaelGSS) [nodejs-private/node-private#438](https://github.com/nodejs-private/node-private/pull/438)
- \[[`98a83a67e6`](https://github.com/nodejs/node/commit/98a83a67e6)] - **permission**: ensure to resolve path when calling mkdtemp (RafaelGSS) [nodejs-private/node-private#464](https://github.com/nodejs-private/node-private/pull/464)
- \[[`1f0cde466b`](https://github.com/nodejs/node/commit/1f0cde466b)] - **permission**: handle buffer path on fs calls (RafaelGSS) [nodejs-private/node-private#439](https://github.com/nodejs-private/node-private/pull/439)
- \[[`bd094d60ea`](https://github.com/nodejs/node/commit/bd094d60ea)] - **permission**: handle fstatfs and add pm supported list (RafaelGSS) [nodejs-private/node-private#441](https://github.com/nodejs-private/node-private/pull/441)
- \[[`7337d21484`](https://github.com/nodejs/node/commit/7337d21484)] - **policy**: handle Module.constructor and main.extensions bypass (RafaelGSS) [nodejs-private/node-private#417](https://github.com/nodejs-private/node-private/pull/417)
- \[[`cf348ec640`](https://github.com/nodejs/node/commit/cf348ec640)] - **policy**: disable process.binding() when enabled (Tobias Nießen) [nodejs-private/node-private#397](https://github.com/nodejs-private/node-private/pull/397)

Windows 32-bit Installer: https://nodejs.org/dist/v20.5.1/node-v20.5.1-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v20.5.1/node-v20.5.1-x64.msi \
Windows ARM 64-bit Installer: https://nodejs.org/dist/v20.5.1/node-v20.5.1-arm64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v20.5.1/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v20.5.1/win-x64/node.exe \
Windows ARM 64-bit Binary: https://nodejs.org/dist/v20.5.1/win-arm64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v20.5.1/node-v20.5.1.pkg \
macOS Apple Silicon 64-bit Binary: https://nodejs.org/dist/v20.5.1/node-v20.5.1-darwin-arm64.tar.gz \
macOS Intel 64-bit Binary: https://nodejs.org/dist/v20.5.1/node-v20.5.1-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v20.5.1/node-v20.5.1-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v20.5.1/node-v20.5.1-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v20.5.1/node-v20.5.1-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v20.5.1/node-v20.5.1-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v20.5.1/node-v20.5.1-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v20.5.1/node-v20.5.1-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v20.5.1/node-v20.5.1.tar.gz \
Other release files: https://nodejs.org/dist/v20.5.1/ \
Documentation: https://nodejs.org/docs/v20.5.1/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

4b8da521c27a38131ce50ca3dbba5dd72854877e3f0b13c9252f075cecd3f37a  node-v20.5.1-aix-ppc64.tar.gz
fc0074fd610de6ba35cc54c80e006f0352841332376c77f02a8ff16ffaa36793  node-v20.5.1-arm64.msi
9cc794517788aee103dfcffa04d0b90ac33854b0d10eb11a26ba4be38403f731  node-v20.5.1-darwin-arm64.tar.gz
31211bf89670c1bf59082e0570cb952f7ea8c45780c9946bebf7014ecfd0d62e  node-v20.5.1-darwin-arm64.tar.xz
7a451dd07551a14ce64afdcc5bf8a71df12558edc19a2a9446e37cefe1e572fb  node-v20.5.1-darwin-x64.tar.gz
98a14ba1e4b09543756deabf675c12bb395c5dbf2d1a3e7aba5926934d5202b4  node-v20.5.1-darwin-x64.tar.xz
66910f6a973a87a37b88e09b660d6d62b0ccba638f4f87d2619291d12fdd547e  node-v20.5.1-headers.tar.gz
22a9ff89f0d9ad942e261826cc4d7593f4b3bf1d284d2807b1e6bb89fe88f9b0  node-v20.5.1-headers.tar.xz
bb7321b4555abdf9fcd62426a23993f3610ac4c18d6fb843e35c2f8add631510  node-v20.5.1-linux-arm64.tar.gz
0487f733a6669cf2a414bd3e4baf153451f37120b81ac8c10922a289b3c1b3d4  node-v20.5.1-linux-arm64.tar.xz
88854069784b941f180b802ea0467fa9ef5cca07e1a2ba6f86e3df639c5fa6c9  node-v20.5.1-linux-armv7l.tar.gz
13005a73465330c5a0f300da6ed19fbf5d5541f29a861439064656359f1b0cc6  node-v20.5.1-linux-armv7l.tar.xz
97ade92e3ebb6686f7d253ada405ae47a2afca6ebc3d5ed2f40e2aa5f3a17053  node-v20.5.1-linux-ppc64le.tar.gz
f525b951868ae8228d084d6b91472492ff6c2f16bba9f71e5f043995a105d66d  node-v20.5.1-linux-ppc64le.tar.xz
ba42c119285ccd730c562574cd6addaec6012d9f81fad8a1f24db6304307067b  node-v20.5.1-linux-s390x.tar.gz
e49c735f347656c1beb9d02640d945e4c71793b46aa98ada015166f8a9d4f73c  node-v20.5.1-linux-s390x.tar.xz
a8678ae00425acdf692e943e3f1cea11a4c46281e4257b82886423bd4ef6f2b5  node-v20.5.1-linux-x64.tar.gz
a4a700bbca51ac26538eda2250e449955a9cc49638a45b38d5501e97f5b020b4  node-v20.5.1-linux-x64.tar.xz
5c6aaefad5cc0dd52d393c73e32fde2d27d6f0c3347b22100230fb08de1205e5  node-v20.5.1.pkg
7e07a56c414a8cbb5ab788e7fe8828902af9e61aaaf7c53beff0688b59c75f83  node-v20.5.1.tar.gz
439c71aa2f38c2861657bfa538e99191a571258066cbfd4548586049c8134190  node-v20.5.1.tar.xz
2fa1a553081268faeb1f08c497767a0460f1e2cfbea51e1923387da5d6c86ac7  node-v20.5.1-win-arm64.7z
804229e23a76ddd11d9a49adcecb0fdb55193e3ba363a8fb24991cb6f1566d6c  node-v20.5.1-win-arm64.zip
8ae5e54bdfbd91b4a519159b629199bc0fd610c2e83168a5aab2ae245d85c693  node-v20.5.1-win-x64.7z
5d2596a00699fadf0ffa8e651f47ff5d719991014b920544d59c80d78569d42f  node-v20.5.1-win-x64.zip
80ec22ac47c21a938987281681f1b2cb0c9e2ff3559ffa30b0ef4afc7f164cca  node-v20.5.1-win-x86.7z
d5681e7b699e939b13ffab4878399ef9d0002692d9839cecaf1c0eeee1a643b8  node-v20.5.1-win-x86.zip
9944eb363ac958a667d2c709fcd8bac8c93b9263c7ea50a2d381860ac6c7de85  node-v20.5.1-x64.msi
4cbca3c6741ba99bc8f6d33e772af5c96a2aa4df2eb3222def317b22f5cf414c  node-v20.5.1-x86.msi
95ea0a5a2c2711b5584e1a75533b4f6afcb134ed91b921d41eab17c9b81f6ccb  win-arm64/node.exe
f9443121897f62db031af6493faf122b97ed73e08fe147fbc41db9d2d3ce05bb  win-arm64/node.lib
33c95cd554cff643ecc30e5492ea6dbe0ea080e166017f36a35fa95daf0e4123  win-arm64/node_pdb.7z
96ef6853e64daefbd478d017e471b5068aae6f5aa3f885ca00a0b90c7b67ddfc  win-arm64/node_pdb.zip
3f6ac39cd107ba6cbd691cc34862cf9977b25ed1297c643d78c07d934bb4c19e  win-x64/node.exe
2d5b066b768af52f08540adacea5d75864c13a65f3af30da935591d25401615f  win-x64/node.lib
10e1b0b4f14d9d1db0263bc7a2dda0c764f875456bfa450ec9d3fff86de7db8a  win-x64/node_pdb.7z
2d288213194122b56cb1420f7b8f208296922122c0d99477e33523b3a216adc6  win-x64/node_pdb.zip
a70ccd7a4e55a7101b87e455274d45e8072971c1d8bbd4b4bb66123956e14eb9  win-x86/node.exe
019308f8ae206cac79b080d7833d10b552cf443f48fc7fe5486ccdc2438018e5  win-x86/node.lib
9878620a9605ad027a8b7c0db66030f3f425b9cfbf6c59372729cd2ccab3ba85  win-x86/node_pdb.7z
04d32d8b68f3f96e02b2586c063b2591545beb250d77b23e7cb8358c069288aa  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQGzBAEBCAAdFiEEiQwI24V5Fi/uDfnbi+q0389VXvQFAmTTzEMACgkQi+q0389V
XvQ7dAv+LTCeD3VXVlhtGaalfE4uJppBxO7VIwcHEfsHcuOvCtiKAVyRwsOsQ68Z
YpIBgNG+ww00JlNs/Ar80Q4Kae/OIU8ZmL6iYGyOEU1IUjwthOjdww/NvrIE0GbT
OvTLneEiogXUGeLTmZpYIBTHrqVAiR1rpa1cONy+m1dYLvZCOMLBsdqzcg+N3bbp
AV+nsApd0WFLcS8QIKVaZiUKL+oXOQzTXjxeEWHAx0QGnLTtX40WvbgOr3apr08d
Dq/tcOh9xabvO0Rsg5HdY5WsRQgJTKypu9Twy1FMFUgdCi+x5lKdv/2oLVWMUu5E
6/o+PeezhZXgCcgbxdBAHU8sjvZnlD+6ShG7IAyVreMLZJbdcQCrCJ0efxSC5eHN
SCijwF3CUhYq2S3ZbTz6q9asTzHdi8gZIND2SSGuzUnfDPBT10jck+lsmKDN4iK8
zqK0Y4mxI1s8FBFdkxwrX9HzmCrCDULDQ3Ibv9hxpE5bz4vUy1q5DKp39LFwxz4V
vAPKMi+5
=FJQN
-----END PGP SIGNATURE-----

```
