---
date: '2023-12-05T09:32:52.032Z'
category: release
title: Node v21.4.0 (Current)
layout: blog-post
author: <PERSON><PERSON><PERSON>
---

## 2023-12-05, Version 21.4.0 (Current), @targos

### Notable Changes

This release fixes a regression introduced in v21.3.0 that caused the `fs.writeFileSync`
method to throw when called with `'utf8'` encoding, no flag option, and if the target file didn't exist yet.

- \[[`32acafeeb6`](https://github.com/nodejs/node/commit/32acafeeb6)] - **(SEMVER-MINOR)** **fs**: introduce `dirent.parentPath` (<PERSON>) [#50976](https://github.com/nodejs/node/pull/50976)
- \[[`724548674d`](https://github.com/nodejs/node/commit/724548674d)] - **fs**: use default w flag for writeFileSync with utf8 encoding (<PERSON><PERSON><PERSON>) [#50990](https://github.com/nodejs/node/pull/50990)

### Commits

- \[[`b24ee15fb2`](https://github.com/nodejs/node/commit/b24ee15fb2)] - **benchmark**: update iterations in benchmark/crypto/hkdf.js (Lei Shi) [#50866](https://github.com/nodejs/node/pull/50866)
- \[[`f79b54e60e`](https://github.com/nodejs/node/commit/f79b54e60e)] - **benchmark**: update iterations in benchmark/crypto/get-ciphers.js (Lei Shi) [#50863](https://github.com/nodejs/node/pull/50863)
- \[[`dc049acbbb`](https://github.com/nodejs/node/commit/dc049acbbb)] - **benchmark**: update number of iterations for `util.inspect` (kylo5aby) [#50651](https://github.com/nodejs/node/pull/50651)
- \[[`d7c562ae38`](https://github.com/nodejs/node/commit/d7c562ae38)] - **deps**: update googletest to 76bb2af (Node.js GitHub Bot) [#50555](https://github.com/nodejs/node/pull/50555)
- \[[`59a45ddbef`](https://github.com/nodejs/node/commit/59a45ddbef)] - **deps**: update googletest to b10fad3 (Node.js GitHub Bot) [#50555](https://github.com/nodejs/node/pull/50555)
- \[[`099ebdb781`](https://github.com/nodejs/node/commit/099ebdb781)] - **deps**: update undici to 5.28.1 (Node.js GitHub Bot) [#50975](https://github.com/nodejs/node/pull/50975)
- \[[`4b1bed04f7`](https://github.com/nodejs/node/commit/4b1bed04f7)] - **deps**: update undici to 5.28.0 (Node.js GitHub Bot) [#50915](https://github.com/nodejs/node/pull/50915)
- \[[`b281e98b1e`](https://github.com/nodejs/node/commit/b281e98b1e)] - **doc**: add additional details about `--input-type` (Shubham Pandey) [#50796](https://github.com/nodejs/node/pull/50796)
- \[[`b7036f2028`](https://github.com/nodejs/node/commit/b7036f2028)] - **doc**: add procedure when CVEs don't get published (Rafael Gonzaga) [#50945](https://github.com/nodejs/node/pull/50945)
- \[[`7adf239af0`](https://github.com/nodejs/node/commit/7adf239af0)] - **doc**: fix some errors in esm resolution algorithms (Christopher Jeffrey (JJ)) [#50898](https://github.com/nodejs/node/pull/50898)
- \[[`759ebcaead`](https://github.com/nodejs/node/commit/759ebcaead)] - **doc**: reserve 121 for Electron 29 (Shelley Vohr) [#50957](https://github.com/nodejs/node/pull/50957)
- \[[`cedc3427fa`](https://github.com/nodejs/node/commit/cedc3427fa)] - **doc**: run license-builder (github-actions\[bot]) [#50926](https://github.com/nodejs/node/pull/50926)
- \[[`30a6f19769`](https://github.com/nodejs/node/commit/30a6f19769)] - **doc**: document non-node_modules-only runtime deprecation (Joyee Cheung) [#50748](https://github.com/nodejs/node/pull/50748)
- \[[`eecab883f0`](https://github.com/nodejs/node/commit/eecab883f0)] - **doc**: add doc for Unix abstract socket (theanarkh) [#50904](https://github.com/nodejs/node/pull/50904)
- \[[`ec74b93b38`](https://github.com/nodejs/node/commit/ec74b93b38)] - **doc**: remove flicker on page load on dark theme (Dima Demakov) [#50942](https://github.com/nodejs/node/pull/50942)
- \[[`724548674d`](https://github.com/nodejs/node/commit/724548674d)] - **fs**: use default w flag for writeFileSync with utf8 encoding (Murilo Kakazu) [#50990](https://github.com/nodejs/node/pull/50990)
- \[[`32acafeeb6`](https://github.com/nodejs/node/commit/32acafeeb6)] - **(SEMVER-MINOR)** **fs**: introduce `dirent.parentPath` (Antoine du Hamel) [#50976](https://github.com/nodejs/node/pull/50976)
- \[[`c1ee506454`](https://github.com/nodejs/node/commit/c1ee506454)] - **fs**: remove workaround for `esm` package (Yagiz Nizipli) [#50907](https://github.com/nodejs/node/pull/50907)
- \[[`1cf087dfb3`](https://github.com/nodejs/node/commit/1cf087dfb3)] - **lib**: refactor to use validateFunction in diagnostics_channel (Deokjin Kim) [#50955](https://github.com/nodejs/node/pull/50955)
- \[[`c37d18d5e1`](https://github.com/nodejs/node/commit/c37d18d5e1)] - **lib**: streamline process.binding() handling (Joyee Cheung) [#50773](https://github.com/nodejs/node/pull/50773)
- \[[`246cf73631`](https://github.com/nodejs/node/commit/246cf73631)] - **lib,src**: replace toUSVString with `toWellFormed()` (Yagiz Nizipli) [#47342](https://github.com/nodejs/node/pull/47342)
- \[[`9bc79173a0`](https://github.com/nodejs/node/commit/9bc79173a0)] - **loader**: speed up line length calc used by moduleProvider (Mudit) [#50969](https://github.com/nodejs/node/pull/50969)
- \[[`812ab9e4f8`](https://github.com/nodejs/node/commit/812ab9e4f8)] - **meta**: bump step-security/harden-runner from 2.6.0 to 2.6.1 (dependabot\[bot]) [#50999](https://github.com/nodejs/node/pull/50999)
- \[[`1dbe1af19a`](https://github.com/nodejs/node/commit/1dbe1af19a)] - **meta**: bump github/codeql-action from 2.22.5 to 2.22.8 (dependabot\[bot]) [#50998](https://github.com/nodejs/node/pull/50998)
- \[[`bed1b93f8a`](https://github.com/nodejs/node/commit/bed1b93f8a)] - **meta**: move one or more collaborators to emeritus (Node.js GitHub Bot) [#50931](https://github.com/nodejs/node/pull/50931)
- \[[`1e7d101428`](https://github.com/nodejs/node/commit/1e7d101428)] - **src**: make ModifyCodeGenerationFromStrings more robust (Joyee Cheung) [#50763](https://github.com/nodejs/node/pull/50763)
- \[[`709ac479eb`](https://github.com/nodejs/node/commit/709ac479eb)] - **src**: disable uncaught exception abortion for ESM syntax detection (Yagiz Nizipli) [#50987](https://github.com/nodejs/node/pull/50987)
- \[[`f6ff11c9f9`](https://github.com/nodejs/node/commit/f6ff11c9f9)] - **src**: fix backtrace with tail \[\[noreturn]] abort (Chengzhong Wu) [#50849](https://github.com/nodejs/node/pull/50849)
- \[[`74f5a1cbc9`](https://github.com/nodejs/node/commit/74f5a1cbc9)] - **src**: print MKSNAPSHOT debug logs to stderr (Joyee Cheung) [#50759](https://github.com/nodejs/node/pull/50759)
- \[[`3a1c664a97`](https://github.com/nodejs/node/commit/3a1c664a97)] - **test**: replace forEach to for.. test-webcrypto-export-import-cfrg.js (Angelo Parziale) [#50785](https://github.com/nodejs/node/pull/50785)
- \[[`ac3a6eefe3`](https://github.com/nodejs/node/commit/ac3a6eefe3)] - **test**: log more information in SEA tests (Joyee Cheung) [#50759](https://github.com/nodejs/node/pull/50759)
- \[[`94462d42f5`](https://github.com/nodejs/node/commit/94462d42f5)] - **test**: consolidate utf8 text fixtures in tests (Joyee Cheung) [#50732](https://github.com/nodejs/node/pull/50732)
- \[[`8e1a70a347`](https://github.com/nodejs/node/commit/8e1a70a347)] - **tools**: add triggers to update release links workflow (Moshe Atlow) [#50974](https://github.com/nodejs/node/pull/50974)
- \[[`ca10cbb774`](https://github.com/nodejs/node/commit/ca10cbb774)] - **tools**: update lint-md-dependencies to rollup\@4.5.2 (Node.js GitHub Bot) [#50913](https://github.com/nodejs/node/pull/50913)
- \[[`1e40c4a366`](https://github.com/nodejs/node/commit/1e40c4a366)] - **tools**: fix current version check (Marco Ippolito) [#50951](https://github.com/nodejs/node/pull/50951)
- \[[`3faed331e1`](https://github.com/nodejs/node/commit/3faed331e1)] - **typings**: fix JSDoc in `internal/modules/esm/hooks` (Alex Yang) [#50887](https://github.com/nodejs/node/pull/50887)
- \[[`6a087ceffa`](https://github.com/nodejs/node/commit/6a087ceffa)] - **url**: throw error if argument length of revokeObjectURL is 0 (DylanTet) [#50433](https://github.com/nodejs/node/pull/50433)

Windows 32-bit Installer: https://nodejs.org/dist/v21.4.0/node-v21.4.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v21.4.0/node-v21.4.0-x64.msi \
Windows ARM 64-bit Installer: https://nodejs.org/dist/v21.4.0/node-v21.4.0-arm64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v21.4.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v21.4.0/win-x64/node.exe \
Windows ARM 64-bit Binary: https://nodejs.org/dist/v21.4.0/win-arm64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v21.4.0/node-v21.4.0.pkg \
macOS Apple Silicon 64-bit Binary: https://nodejs.org/dist/v21.4.0/node-v21.4.0-darwin-arm64.tar.gz \
macOS Intel 64-bit Binary: https://nodejs.org/dist/v21.4.0/node-v21.4.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v21.4.0/node-v21.4.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v21.4.0/node-v21.4.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v21.4.0/node-v21.4.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v21.4.0/node-v21.4.0-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v21.4.0/node-v21.4.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v21.4.0/node-v21.4.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v21.4.0/node-v21.4.0.tar.gz \
Other release files: https://nodejs.org/dist/v21.4.0/ \
Documentation: https://nodejs.org/docs/v21.4.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

91f69ef2501efe9908b5adddc740c1b614054551abe1087a475b3f8e8bcd4f51  node-v21.4.0-aix-ppc64.tar.gz
4b4e57ff8f70fbdf49e4917bed65a4c7c896af2b3af5932d1069e9c637bea581  node-v21.4.0-arm64.msi
459a23a6044003c6cb5d24867da0c473bd8c2e4cb5689517051be182eed46c2b  node-v21.4.0-darwin-arm64.tar.gz
6b64cfd77fe1c09b96e6f57643d73a0bba22f270d58ab17eb23ffb62037f55b8  node-v21.4.0-darwin-arm64.tar.xz
438cf09d2b48116170f1b2bf351f19a77e62c85b939524d4cf3c0a5b7bd0dba9  node-v21.4.0-darwin-x64.tar.gz
3c789c5907a417f4e51c95b282af5b2d89442a51a60fe7144cc2e98e00353ccd  node-v21.4.0-darwin-x64.tar.xz
f16010627115873b53ec90bb3b896672602443c450dd390bbe59c64d619f2f40  node-v21.4.0-headers.tar.gz
3da216a3129be17a21f7f6e4f488cd350b45acc2025eb65d078f88fd1071884e  node-v21.4.0-headers.tar.xz
071b90b14c2e74f8400c48683c21250491951faf9ae54756a2b53340c1574a27  node-v21.4.0-linux-arm64.tar.gz
a3ae9aa0ce883cb6296f83e77602f39291ff2cc6136be9e6647330df748374be  node-v21.4.0-linux-arm64.tar.xz
48b0d4f52f421a6baa5fbb54339fb03a86cce976fd3b647271309d0a1844563d  node-v21.4.0-linux-armv7l.tar.gz
fe55e583f52c1685403d2834a079869e7a3c7484278b9ab181985a25936e61cf  node-v21.4.0-linux-armv7l.tar.xz
26e9de097745eb150739cf9f7664bfe6c61ebafac1954b5dd97f52c673212283  node-v21.4.0-linux-ppc64le.tar.gz
ed1f88de3fc995060571821684ae1029081c525f693e8cb02fb65b27d73a2c47  node-v21.4.0-linux-ppc64le.tar.xz
902a00cd9c2d15d2780704a553c70eaa64ee59443417f527f0da76e494d38179  node-v21.4.0-linux-s390x.tar.gz
8e3e7eb7a29144ed8f7adea41687929570e3b0113b773b00f350b8799cdedcbf  node-v21.4.0-linux-s390x.tar.xz
d8cd0ec0b78bcbc591e7a4655a92c1c667e64bc434e7a895904dc1fe9442af1d  node-v21.4.0-linux-x64.tar.gz
9f3b4447a689c1c0f6c08f61967f2a8e50d2c8187805e85ad67701ef84c0095c  node-v21.4.0-linux-x64.tar.xz
4ae54c36a6891b20e1de4596d8991bccc32eb418a8b98448057762941a9efe5e  node-v21.4.0.pkg
e7b79431a3ec8c8193d30dbac92ed08777739417a9c2820acbde1eae2314eb96  node-v21.4.0.tar.gz
7a80f6527654602d7358c5be2eefc4f80a64c8901630a83977b073c34f25479c  node-v21.4.0.tar.xz
be3646b1ed2d202cae5558d374856b32384f3cc7169c709ec88992e6d7c58a54  node-v21.4.0-win-arm64.7z
519e98ce05cc395ce0292a34e0a225bae39d0b06652a133626be1723a5571588  node-v21.4.0-win-arm64.zip
54d94594b012c9a5d0cb9ee752831494d1d97b62a7b4e9e228f358e892ff4172  node-v21.4.0-win-x64.7z
8e7a1c04ff9d1b2680887a31b9eea1392913b91af29f928233ef832d879847de  node-v21.4.0-win-x64.zip
3eda30ea8831f2e39dffec848913de14b38a828d254b40a8a7a4969a878f7eba  node-v21.4.0-win-x86.7z
c1b94300c54c22ceb30086a8b50f0df7511ed456c3be54fc03a04b5d4c18820c  node-v21.4.0-win-x86.zip
2f040b8180f01d97d03d8b4e44601de22956947651f6724b38fdb7c4618e251e  node-v21.4.0-x64.msi
6c86dad32eed707f7aedfc7f986ac5a7dfa32b5d4474095dba8b3fb01f38037e  node-v21.4.0-x86.msi
57f326d3aa8fd777e75850e5df4a0b0931b7e154e349d8b5915772f4ae6b1f65  win-arm64/node.exe
fe2dae50bdfba3f151248d0f8b87d621263a627099df6aa1611ccd94387464fe  win-arm64/node.lib
b72f06e1a57298661271f4d87cc841c9212c4123f159a356d6f9970edd0585d7  win-arm64/node_pdb.7z
c8f034ab54a80af486083c858b1d538f2deeab5d3318f04b0843f1b60a53d28d  win-arm64/node_pdb.zip
84487781d837df4005223011a8528d8a74f6728027dd2d99038a5f8f02959b0d  win-x64/node.exe
d0310658ca8dfdfaab078ef2a017cc7347a30ce51f123e38ac4fcac74706f136  win-x64/node.lib
1918a98f41c5dfa812b6e85f3b3502fcbbb09cf6be45ae481792e3c63184c8c9  win-x64/node_pdb.7z
26412a3159a005efdbb1f899ce97ad090918d7a42708cb481d6a568f1a39b12b  win-x64/node_pdb.zip
36f6cd67be2b41b05bbcb5440e63d10ce2804edf7c4873a8fee8492123b667f7  win-x86/node.exe
9395c4286ec710c0410fd4e7346f1b4a5ff0ad4f5c8501f716c0a8a20f79398e  win-x86/node.lib
b82c6cd16d8929b8f10a26916fd4ef821f1b9f71e21a192a50c4195c8eaf184b  win-x86/node_pdb.7z
ba7fbaa18c713206f4a129fd4a7e87ae5cd214d094d7758f12b833258376bd6d  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEj8yhP+8dDC6RAI4Jdw96mlrhVgAFAmVu7J0ACgkQdw96mlrh
VgCXyQ//Rv+VyTOwtblsYJfkn3EaQkfSDnS+JApyu/FozHqoDPYBendhPauwbvV/
WHSHoYsvMJlR16SfXxmnxOwe5wON72C6skAas9DjUxHV35DpxQJVOdhHkDHsNbf9
uisFpZht8blqurUvLIVG/O95BCu6/+rVdvodkZbVXqwkPyRob0jED3i649SeWIt9
/r+9Z4y0zI5FpzWg3ps11U/Twa7N0ErvWxHBE+oGtdIW4Gxq9zfLF2M6XHsuZIdJ
qDERw+Q7aGuKQMNVZCbS+BVJU/kxXVt7/XdTVs929BiaClFII3oNET4XYYDeqqrg
MmJXVJ1X44YrYS6bOaTGkrQDeLnZoGVFn7UeH96nWhDXEh5UIo21+LLT8yM3v5Bm
FBGY+7OV703nRjV+AQBUXBDxLDAkLxClbh174ygMseIg+bt95O33VTbQyAlggBKf
b4Qnh/2s039U1pOUGF3m0MCMq3mwRkNmJX5mOJOeZ1HIzgbeqNDerYdv2Ffj7zaa
LFG3ulco0xwC0KP7+b7nEwhZ+5BOi4ESrJVfm3VTiT8sY5/yoR6EMq0vrKBjGnhE
VYPoCXRCUO6q/dk0dqWOnR3GJ5u91iwFFcLkDeBB7W0gR9JPykQ/+CAtmhWlmPmX
+cQor5PHAfU0nTEzUOVgj81kTyBZ0iOOIMqP+2ATa3BCqegw3oU=
=oeMW
-----END PGP SIGNATURE-----
```
