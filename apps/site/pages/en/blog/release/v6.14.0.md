---
date: '2018-03-28T16:31:37.671Z'
category: release
title: Node v6.14.0 (LTS)
layout: blog-post
author: <PERSON><PERSON>
---

### Notable Changes

- **Upgrade to OpenSSL 1.0.2o**: Does not contain any security fixes that are known to impact Node.js.
- **Fix for inspector DNS rebinding vulnerability (CVE-2018-7160)**: A malicious website could use a DNS rebinding attack to trick a web browser to bypass same-origin-policy checks and allow HTTP connections to localhost or to hosts on the local network, potentially to an open inspector port as a debugger, therefore gaining full code execution access. The inspector now only allows connections that have a browser `Host` value that is either not subject to DNS resolution or matches `localhost` or `localhost6`.
- **Fix for `'path'` module regular expression denial of service (CVE-2018-7158)**: A regular expression used for parsing POSIX paths could be used to cause a denial of service if an attacker were able to have a specially crafted path string passed through one of the impacted `'path'` module functions.
- **Reject spaces in HTTP `Content-Length` header values (CVE-2018-7159)**: The Node.js HTTP parser allowed for spaces inside `Content-Length` header values. Such values now lead to rejected connections in the same way as non-numeric values.
- **Update root certificates**: 5 additional root certificates have been added to the Node.js binary and 30 have been removed.

### Commits

- [[`ac21bdc149`](https://github.com/nodejs/node/commit/ac21bdc149)] - **crypto**: update root certificates (Ben Noordhuis) [#19322](https://github.com/nodejs/node/pull/19322)
- [[`3c99e41427`](https://github.com/nodejs/node/commit/3c99e41427)] - **deps**: add -no_rand_screen to openssl s_client (Shigeki Ohtsu) [nodejs/io.js#1836](https://github.com/nodejs/io.js/pull/1836)
- [[`d775057090`](https://github.com/nodejs/node/commit/d775057090)] - **deps**: fix asm build error of openssl in x86_win32 (Shigeki Ohtsu) [iojs/io.js#1389](https://github.com/iojs/io.js/pull/1389)
- [[`982012b96d`](https://github.com/nodejs/node/commit/982012b96d)] - **deps**: fix openssl assembly error on ia32 win32 (Fedor Indutny) [iojs/io.js#1389](https://github.com/iojs/io.js/pull/1389)
- [[`1aa83f7707`](https://github.com/nodejs/node/commit/1aa83f7707)] - **deps**: copy all openssl header files to include dir (Shigeki Ohtsu) [#19638](https://github.com/nodejs/node/pull/19638)
- [[`05de6f4af7`](https://github.com/nodejs/node/commit/05de6f4af7)] - **deps**: upgrade openssl sources to 1.0.2o (Shigeki Ohtsu) [#19638](https://github.com/nodejs/node/pull/19638)
- [[`ed64cc2be7`](https://github.com/nodejs/node/commit/ed64cc2be7)] - **deps**: reject interior blanks in Content-Length (Ben Noordhuis) [nodejs-private/http-parser-private#1](https://github.com/nodejs-private/http-parser-private/pull/1)
- [[`d786d21f92`](https://github.com/nodejs/node/commit/d786d21f92)] - **deps**: upgrade http-parser to v2.8.0 (Ben Noordhuis) [nodejs-private/http-parser-private#1](https://github.com/nodejs-private/http-parser-private/pull/1)
- [[`4947b0e26e`](https://github.com/nodejs/node/commit/4947b0e26e)] - **inspector**: minor adjustments (Eugene Ostroukhov)
- [[`e3950d1a40`](https://github.com/nodejs/node/commit/e3950d1a40)] - **inspector**: check Host header (Ali Ijaz Sheikh)
- [[`ef32e06a6e`](https://github.com/nodejs/node/commit/ef32e06a6e)] - **openssl**: fix keypress requirement in apps on win32 (Shigeki Ohtsu) [iojs/io.js#1389](https://github.com/iojs/io.js/pull/1389)
- [[`1dba2f4950`](https://github.com/nodejs/node/commit/1dba2f4950)] - **src**: drop CNNIC+StartCom certificate whitelisting (Ben Noordhuis) [#19322](https://github.com/nodejs/node/pull/19322)
- [[`bdfeb1c739`](https://github.com/nodejs/node/commit/bdfeb1c739)] - **tools**: update certdata.txt (Ben Noordhuis) [#19322](https://github.com/nodejs/node/pull/19322)

Windows 32-bit Installer: https://nodejs.org/dist/v6.14.0/node-v6.14.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v6.14.0/node-v6.14.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v6.14.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v6.14.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v6.14.0/node-v6.14.0.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v6.14.0/node-v6.14.0-darwin-x64.tar.gz \
Linux 32-bit Binary: https://nodejs.org/dist/v6.14.0/node-v6.14.0-linux-x86.tar.xz \
Linux 64-bit Binary: https://nodejs.org/dist/v6.14.0/node-v6.14.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v6.14.0/node-v6.14.0-linux-ppc64le.tar.xz \
Linux PPC BE 64-bit Binary: https://nodejs.org/dist/v6.14.0/node-v6.14.0-linux-ppc64.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v6.14.0/node-v6.14.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v6.14.0/node-v6.14.0-aix-ppc64.tar.gz \
SmartOS 32-bit Binary: https://nodejs.org/dist/v6.14.0/node-v6.14.0-sunos-x86.tar.xz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v6.14.0/node-v6.14.0-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v6.14.0/node-v6.14.0-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v6.14.0/node-v6.14.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v6.14.0/node-v6.14.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v6.14.0/node-v6.14.0.tar.gz \
Other release files: https://nodejs.org/dist/v6.14.0/ \
Documentation: https://nodejs.org/docs/v6.14.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

d0faec6a64ced7d9277b49d9236ccb0d071aecd5de05bc2ca97f62b285da68b4  node-v6.14.0-aix-ppc64.tar.gz
56f87293335537d0f80032f863a9b7ef7db4c325a0e3c203b30be3be4b30ec2d  node-v6.14.0-darwin-x64.tar.gz
94120168715853dbf3960f8d2f30dff870a1db37280b99c88eb01dc20470a21b  node-v6.14.0-darwin-x64.tar.xz
739db0a458b2a02b8b3e26791e82fdfa233f6ba6b91a22aaf6fb0bed064d673c  node-v6.14.0-headers.tar.gz
fc9a21679a43115803b2f2471e3871e7070bbf4fe52120cb5f8a6d50eb369463  node-v6.14.0-headers.tar.xz
8a3ff08a103b74bb25bce8ec549157945a4c800e02d1fa2a38e13665facbc834  node-v6.14.0-linux-arm64.tar.gz
a8ca07c0f6af9e4ef0ca364f336ad49c4144d2cc4ba4290517ef14a91bfdf5ab  node-v6.14.0-linux-arm64.tar.xz
600aeb9a3573b2782bb709762cda597c683d4850252999da3b0386d3853e06df  node-v6.14.0-linux-armv6l.tar.gz
9c91ce90b89a6716a5c2cd535ecda211c9bb683e320748b337fe387b07ccc911  node-v6.14.0-linux-armv6l.tar.xz
cbaddbc77ba8bebe625caf8c3dadd5a69b15d7d5a82af69ea48ec2f5157f7e05  node-v6.14.0-linux-armv7l.tar.gz
302e6869021317d9f3c348d9c546564d6310de8c294355e98d2cb5cce5f3a768  node-v6.14.0-linux-armv7l.tar.xz
71b6a22f3e0df6d5f952717cbf98e25fcc06695534955d23dbe610681ee41107  node-v6.14.0-linux-ppc64le.tar.gz
25c630db43d9a9c7228122f3a95f0a3b35098ce748a29943e88682591e3190df  node-v6.14.0-linux-ppc64le.tar.xz
21da9ea343a6130433607880032f78984f9be3bc91063065b8a5fa7780bb76ee  node-v6.14.0-linux-ppc64.tar.gz
2d192974800db92bd3e5313281a41664ba7564d294aa00669491bf32e5084194  node-v6.14.0-linux-ppc64.tar.xz
382156862af3ee2712d4bc78af360f7b1321dca259db119d647b1596c1382319  node-v6.14.0-linux-s390x.tar.gz
0adf90a2a2f9bcf8a9f1ede8cfc0397a61e620866754ac50f4e2ba6ca2ce0840  node-v6.14.0-linux-s390x.tar.xz
272bb9a1937a8372420a2bd98bea4d1c152b961c1b3fb3493f626a9de162d4bd  node-v6.14.0-linux-x64.tar.gz
89350276fcd079359af417f7b745cab714114d34eed52c44c96214f5928772dc  node-v6.14.0-linux-x64.tar.xz
f460b02a2046f68f0db32405094879a45d2447a2aa3e6c18668ce4ea63a8d99c  node-v6.14.0-linux-x86.tar.gz
d0de10751f19b862882c08d2118e9d5c725e3b4500bdee354c3b41ca5415593e  node-v6.14.0-linux-x86.tar.xz
094aafde1d7f76f1cf1723dcc78704048a8a51b55c6c4a695480a4369ee54f89  node-v6.14.0.pkg
ee78d79dc4c40e98675f9eb97a7a5e85e96b8c92fd6f0cbb96618c7e56112a3f  node-v6.14.0-sunos-x64.tar.gz
ba87f0ebd820fcd3fd78f32c50622d2465a364f17b80bdab14928103b4da5b38  node-v6.14.0-sunos-x64.tar.xz
ac4661e8748a475d6daf56e46fb78f50766b0ef11e8bf38affc681f8f36cc9d8  node-v6.14.0-sunos-x86.tar.gz
6d3741cb609c2ee0e9dbde6cab3a425bc70ccae735656fc1e0cbbbbcf704996e  node-v6.14.0-sunos-x86.tar.xz
87892f42c5c48bb6367e0299ec81e1d18e560e401eddc50dd746acc4c896bd7c  node-v6.14.0.tar.gz
21ab08323dfd082e60fefa5e1af99b086c6154a6675ad265a42462621c35d599  node-v6.14.0.tar.xz
20d0b8354898427e97cafe53e014cf9165fb0e305dd152d039110c5f46693afe  node-v6.14.0-win-x64.7z
161af253a5c38a90ff128f1e74c992b0c7d82f6effa97b1b6496d100852c1849  node-v6.14.0-win-x64.zip
2ddc70ba3dfbd987c94caa5d2e66313226888a337a3fa5e11c92889ca6acaaca  node-v6.14.0-win-x86.7z
f639d832ab850a5b51a17a3a216dd1227a08acf49522de774d6376a12d9e8106  node-v6.14.0-win-x86.zip
c6e9bc96367e84af9a734bc8bceb51cff54dd690b85ec4b824accb1b314c8d17  node-v6.14.0-x64.msi
6f590e033781dbdedc6cde3ce6ef323adb2397adb867d6f0b81fc228409fa982  node-v6.14.0-x86.msi
debf8b1d6092b72c546f1022f8c6e86550825054e424ffd051f67efda6b0cba0  win-x64/node.exe
c246cc703aa032b2ec38bb37aa4e2a014393f865e614193dfbdc6950618d356e  win-x64/node.lib
1db26911004b4ef750b2a84438649fd6d5742fb695279d5aca3ad0c446ebfc5f  win-x64/node_pdb.7z
245a1fb13b2f539961cfaa5dfee0b6b9780fc18c214f02f8a80b9d85da00c554  win-x64/node_pdb.zip
0a7c3286536d3db95b507d80f8af3309de394fd9093ff5d178c8f381af1f0c4a  win-x86/node.exe
f81f3215bcd0ef055a7a5116e43d7e03eb7d538df451126399b34e16ece03074  win-x86/node.lib
fb9b391cd9dcc8aba3b0ad6099c5d39ead1e19e2cd71fe76c9f4a7b2824e8c87  win-x86/node_pdb.7z
66718d8d0cd52ad658c8c6bfe06fb233537ba0bebfbc4d977c13e897ef154439  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQEzBAEBCAAdFiEEDv/hvO/ZyE49CYFSkzsB9AtcqUYFAlq7whgACgkQkzsB9Atc
qUariQf7BnygWjJL1CkDflkJ3gOobuRxpM3fwgh6c97nwOi1iYfuCSBpYEx8xNYz
SZAqHOsTqnrAxWB9yItzwE1wfvofqLYf08cdpUxgvV9dJ/XnTrz13ynad6d8NB5b
Il5LnagpAOKxqXGJZHNsQiqUNn+eTjM2OceLid9cq4o7L6rLcnXcVjWJTbvwOrsd
rdKs4uG2z0vnzrVM3js8hBfE//RaqCz5n8YmiMVI6FBbDRhE02ZolQnNZEYRHht1
L3JIl5VIaIUDA0kBfO2yop/HhbebCV4t8kZbUnwDCmG3Buz6D7MuvflN4p+Ohr6N
iKBvsmZwIZX/tpsIhs2BvvMZ2HGBBA==
=aTKq
-----END PGP SIGNATURE-----

```
