---
date: '2019-02-28T12:47:28.951Z'
category: release
title: Node v8.15.1 (LTS)
layout: blog-post
author: <PERSON>
---

**This is a security release**. All Node.js users should consult the security release summary at /blog/vulnerability/february-2019-security-releases/ for details on patched vulnerabilities.

Fixes for the following CVEs are included in this release:

- Node.js: Slowloris HTTP Denial of Service with keep-alive (CVE-2019-5737)
- OpenSSL: 0-byte record padding oracle (CVE-2019-1559)

### Notable Changes

- **deps**: OpenSSL has been upgraded to 1.0.2r which contains a fix for [CVE-2019-1559](https://www.openssl.org/news/secadv/20190226.txt). Under certain circumstances, a TLS server can be forced to respond differently to a client if a zero-byte record is received with an invalid _padding_ compared to a zero-byte record with an invalid _MAC_. This can be used as the basis of a padding oracle attack to decrypt data.
- **http**: Further prevention of "Slowloris" attacks on HTTP and HTTPS connections by consistently applying the receive timeout set by `server.headersTimeout` to connections in keep-alive mode. Reported by <PERSON> ([Voxnest](https://voxnest.com)). (CVE-2019-5737 / <PERSON> Collina)

### Commits

- [[`61980dcbf9`](https://github.com/nodejs/node/commit/61980dcbf9)] - **deps**: add -no_rand_screen to openssl s_client (Shigeki Ohtsu) [nodejs/io.js#1836](https://github.com/nodejs/io.js/pull/1836)
- [[`bf287faf21`](https://github.com/nodejs/node/commit/bf287faf21)] - **deps**: fix asm build error of openssl in x86_win32 (Shigeki Ohtsu) [iojs/io.js#1389](https://github.com/iojs/io.js/pull/1389)
- [[`22ec5feff0`](https://github.com/nodejs/node/commit/22ec5feff0)] - **deps**: fix openssl assembly error on ia32 win32 (Fedor Indutny) [iojs/io.js#1389](https://github.com/iojs/io.js/pull/1389)
- [[`2cc5e7f534`](https://github.com/nodejs/node/commit/2cc5e7f534)] - **deps**: copy all openssl header files to include dir (Shigeki Ohtsu)
- [[`d71517fd43`](https://github.com/nodejs/node/commit/d71517fd43)] - **deps**: upgrade openssl sources to 1.0.2r (Shigeki Ohtsu)
- [[`76d52c508a`](https://github.com/nodejs/node/commit/76d52c508a)] - **http**: prevent slowloris with keepalive connections (Matteo Collina) [nodejs-private/node-private#162](https://github.com/nodejs-private/node-private/pull/162)
- [[`6969fad7d6`](https://github.com/nodejs/node/commit/6969fad7d6)] - **openssl**: fix keypress requirement in apps on win32 (Shigeki Ohtsu) [iojs/io.js#1389](https://github.com/iojs/io.js/pull/1389)

Windows 32-bit Installer: https://nodejs.org/dist/v8.15.1/node-v8.15.1-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v8.15.1/node-v8.15.1-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v8.15.1/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v8.15.1/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v8.15.1/node-v8.15.1.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v8.15.1/node-v8.15.1-darwin-x64.tar.gz \
Linux 32-bit Binary: https://nodejs.org/dist/v8.15.1/node-v8.15.1-linux-x86.tar.xz \
Linux 64-bit Binary: https://nodejs.org/dist/v8.15.1/node-v8.15.1-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v8.15.1/node-v8.15.1-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v8.15.1/node-v8.15.1-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v8.15.1/node-v8.15.1-aix-ppc64.tar.gz \
SmartOS 32-bit Binary: https://nodejs.org/dist/v8.15.1/node-v8.15.1-sunos-x86.tar.xz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v8.15.1/node-v8.15.1-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v8.15.1/node-v8.15.1-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v8.15.1/node-v8.15.1-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v8.15.1/node-v8.15.1-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v8.15.1/node-v8.15.1.tar.gz \
Other release files: https://nodejs.org/dist/v8.15.1/ \
Documentation: https://nodejs.org/docs/v8.15.1/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

9156fc8929f545981bfbafa4fe8ea3b8afeed9dacfb6e4bbc145ac86705c783e  node-v8.15.1-aix-ppc64.tar.gz
f3da0b4397150226c008a86c99d77dbb835dc62219d863654913a78332ab19a5  node-v8.15.1-darwin-x64.tar.gz
aacdc9d5d8bbeaf47c398815147e052aac53cf19319f4c140c1798a82d419e65  node-v8.15.1-darwin-x64.tar.xz
6254529411abf790030fc72d43fe23f365799c198550e30d2fdc683f9407c299  node-v8.15.1-headers.tar.gz
6729d58fad4960259ea5233a87ef913e07b7a5118d4c6e3fecc4b3595c817345  node-v8.15.1-headers.tar.xz
0fcb30bc508097c0a13e7001a55f410802eda155c070cd5d125cd321332cc9f1  node-v8.15.1-linux-arm64.tar.gz
69e000d78342c3d39583922c57947a906ad723789d6294951deb10cbe8709605  node-v8.15.1-linux-arm64.tar.xz
a4b0ca0cd8b21224f676f05f6b4760d368935eed21e8ab96ceedb454e70770b4  node-v8.15.1-linux-armv6l.tar.gz
2bca1485fdbfd2a905d28409450b512eeaeb020ea50b5027d697f15e70bffa95  node-v8.15.1-linux-armv6l.tar.xz
e1fded2ef39967deef4f6a6921f86a66092c4bda1e9d207126fc7676797de98a  node-v8.15.1-linux-armv7l.tar.gz
4be3cbf3bd0d0f30c8c2a3a4396fa1b78e6c5defc21dc176bf5da16782c4e1fa  node-v8.15.1-linux-armv7l.tar.xz
6d122196856e633a645a07da25ada68ae8a841b8cdb962a69f6e9ec6110ee1bb  node-v8.15.1-linux-ppc64le.tar.gz
efca9b7ca623223ba97dafd16627461075d9fbc9cc9958e91d9a1ff0feb92dc2  node-v8.15.1-linux-ppc64le.tar.xz
6dd32604a69cb3bb22583842e9a39f88d1ebaf9275fc0c8a870bd13bca0a872b  node-v8.15.1-linux-s390x.tar.gz
11e78c00c62e588947eff4a658ff9d1a8ad5c3540d387b9a3b28ef11838a8748  node-v8.15.1-linux-s390x.tar.xz
16e203f2440cffe90522f1e1855d5d7e2e658e759057db070a3dafda445d6d1f  node-v8.15.1-linux-x64.tar.gz
5643b54c583eebaa40c1623b16cba4e3955ff5dfdd44036f6bafd761160c993d  node-v8.15.1-linux-x64.tar.xz
d7cb82569278baf46cac88128c0677e0499b066cd2dfe0223af82162e2a17185  node-v8.15.1-linux-x86.tar.gz
ca5b9ed2377fca5e66f66fa4e9ce4b0ffce6e065651d1c6398989b79d1d8c829  node-v8.15.1-linux-x86.tar.xz
b87fba0aade8caf51182e3ec3f6293cf8556b4368d7fb5f30e4679188c3b808a  node-v8.15.1.pkg
bba1611a98486958eaa6bd1b0e62f9e41bdafa12344482becb47bda34eff0357  node-v8.15.1-sunos-x64.tar.gz
8cefe86dbd5de7828a0fdd3f6217625dce783c8d5ba52b25fa4cd6c4dc22b758  node-v8.15.1-sunos-x64.tar.xz
f2e4d7506f63268fba28583679ab0d0454bdd3c6826aa2f3f8fbd98914bfa1ef  node-v8.15.1-sunos-x86.tar.gz
669b2a959b13ecdba7c722fbe21277dda4fca2b26b09899db493251986fe2060  node-v8.15.1-sunos-x86.tar.xz
413e0086bd3abde2dfdd3a905c061a6188cc0faceb819768a53ca9c6422418b4  node-v8.15.1.tar.gz
6b6486a3f452624941f6e11dd5f878c298d43e9c21b5f43ca1721dc7ce25add1  node-v8.15.1.tar.xz
539457de89d5c01f2b69452d0d6ddba812b0d321465df22c85aa39ef4a41da8b  node-v8.15.1-win-x64.7z
f636fa578dc079bacc6c4bef13284ddb893c99f7640b96701c2690bd9c1431f5  node-v8.15.1-win-x64.zip
1b65532abb5e1c78ef3b6766fbe849b44e33bbcc8da4a4340c3530eca77adc29  node-v8.15.1-win-x86.7z
ce38c64c7f2921b1aa7f8bd4d2e89944f731b000fc8b7fc4930e957c75b04ea4  node-v8.15.1-win-x86.zip
a2c41856db0c5663e967e5ef95eec2f968317a6dc7f0c0695c9d231c676c1c4f  node-v8.15.1-x64.msi
c6e9b1fc0d2defcec084abece599c173b88755c9968cd4cd4e462f2b7cdff166  node-v8.15.1-x86.msi
88c916e702e63de0530cdaacaa5f084d07dda590840c2adb297cdb470c7ddeaa  win-x64/node.exe
6aee1f00180ccd12f48027d0ee531de0eef2ef391028800352dde77a1d87d161  win-x64/node.lib
0d2b0fa8c2432586dc1738ebe81126c7ca5a96e8acffc271859351714d0ec195  win-x64/node_pdb.7z
3add728eba3cd329819233604073457b9752b7dab6bbce420f1f7ccc0dd038dc  win-x64/node_pdb.zip
c21d787cbd585c0db2a02267bb56dd3e36e6d06f7388cdd01c96b60a49642338  win-x86/node.exe
3de0c35989d92515996dc42ed029716954706a01c728ccd5ebe37dc489483d60  win-x86/node.lib
b046a83c3b4d5d029c929f3a739c2f97917e08547c16a3f9e6695d1f40c36ab4  win-x86/node_pdb.7z
bb67de498e64e068110b7c36f657d1bd6b8d956d7821a323946d2af365503a3e  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQEzBAEBCAAdFiEE3Y8jOLrnUB491ax4wnN5L32DVF0FAlx44TkACgkQwnN5L32D
VF1Apgf/VrQvCUbc5zz7RAlFvZ3GAWyPTkacVuMRaC/8fu1aG7Bz2RcnBjQvRpv2
J2n4HEZ1waSN4gw6zup0Oxypl2fx9pi48tG2UqG390y6Ace6rv3JIZD19O4y65ZE
rPWAvvLgNlB1/ctxD3sgkUHhAyA7Mz6eoRIq3DS0Jv49cLnHalelhGlcPNmFYpzK
yagFEgj8Gy7EJzRMOJLOB5RypBXFt31gIreSy96wJz78Bwf3FJ1RhXRwMLcb8R53
fDUJihP/cpP+LOTIPH8b4+ojxPwBuePBIpEogDtPYnEEL6DbYk+G8m+fxv0sNAFc
TzfNSZcE++oahwsV+FxCfQgFWga62g==
=y2yr
-----END PGP SIGNATURE-----
```
