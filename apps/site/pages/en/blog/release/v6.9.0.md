---
date: '2016-10-18T17:25:38.894Z'
category: release
title: Node v6.9.0 (LTS)
layout: blog-post
author: <PERSON>
---

_This is a security release. All Node.js users should consult the security release summary at /blog/vulnerability/october-2016-security-releases/ for details on patched vulnerabilities._

### A New LTS Release Line

v6.9.0 marks the transition of Node.js v6 into Long Term Support (LTS) with the codename **"Boron"**. The v6 release line now moves in to "Active LTS" and will remain so until April 2018. After that time it will move in to "Maintenance" until end of life in April 2019.

### LTS Migration

Some highlights for users migrating from Node.js v4 LTS "Argon" to Node.js v6 LTS "Boron":

- The `Buffer()` constructor has been deprecated in the documentation in favour of the new `Buffer.from()`, `Buffer.alloc()` and `Buffer.allocUnsafe()` for security and safety. See the [Buffer documentation](https://nodejs.org/api/buffer.html) for full details. As a documentation-only deprecation there will be no warnings printed to standard error, this is the first phase in a long deprecation cycle.
- Support has been dropped for Windows Vista and earlier and macOS 10.7 and earlier.
- Many warning messages and error messages have been cleaned up and made more consistent.
- The [`--prof-process`](https://nodejs.org/api/cli.html#cli_prof_process) command line argument can be used to process output files created when using the V8 `--prof` command line argument.
- A new [`EventEmitter#eventNames()`](https://nodejs.org/api/events.html#events_emitter_eventnames) method can be used to list all events currently being listened to on an `EventEmitter`.
- [`fs.mkdtemp()`](https://nodejs.org/api/fs.html#fs_fs_mkdtemp_prefix_options_callback) is a fast and safe way to make a unique temporary directory using operating system primitives.
- [`process.cpuUsage()`](https://nodejs.org/api/process.html#process_process_cpuusage_previousvalue) will allow insight into CPU resources being consumed by the current process.
- Very large arrays are now truncated when passed through `util.inspect()`, this also applies to `console.log()` and friends.
- When a native `Promise` incurs a rejection but there is no handler to receive it, a warning will be printed to standard error.
- A new experimental debugging protocol can be activated with the `--inspector` command line argument. This uses the "v8_inspector" protocol and can be consumed directly by Chrome DevTools, Visual Studio Code and others.

While there are some breaking API changes appearing in both [v5.0.0](https://direct.nodejs.org/en/blog/release/v5.0.0/) and [v6.0.0](https://direct.nodejs.org/en/blog/release/v6.0.0/), they are relatively minor and should not have significant impact on most users.

In addition to Node.js changes, we also have a number of V8 upgrades between Node.js v4 LTS and this new v6 LTS release, including these JavaScript language enhancements:

- The [spread operator](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Operators/Spread_operator) for arrays and function calls
- [Rest parameters](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Functions/rest_parameters)
- [Default function parameters](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Functions/Default_parameters)
- [Destructuring](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Operators/Destructuring_assignment)
- [Proxies](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Proxy)
- [`Array#includes()`](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/includes)

It's time to start planning your migration from Node.js v4 LTS "Argon" to Node.js v6 "Boron". Argon remains in _Active LTS_ until April, 2017 and then moves in to _Maintenance_ until April 2018 when support will cease. These details and more can be found in the Node.js LTS plan, located at <https://github.com/nodejs/LTS>.

- If you would like help with Node.js, please open an issue at https://github.com/nodejs/help
- If you would like to report a bug with Node.js, please open an issue at https://github.com/nodejs/node

## Node.js v6.9.0 LTS "Boron"

### Notable changes

- **crypto**: Don't automatically attempt to load an OpenSSL configuration file, from the `OPENSSL_CONF` environment variable or from the default location for the current platform. Always triggering a configuration file load attempt may allow an attacker to load compromised OpenSSL configuration into a Node.js process if they are able to place a file in a default location. (Fedor Indutny, Rod Vagg)
- **node**: Introduce the `process.release.lts` property, set to `"Boron"`. This value is `"Argon"` for v4 LTS releases and `undefined` for all other releases. (Rod Vagg)
- **V8**: Backport fix for CVE-2016-5172, an arbitrary memory read. The parser in V8 mishandled scopes, potentially allowing an attacker to obtain sensitive information from arbitrary memory locations via crafted JavaScript code. This vulnerability would require an attacker to be able to execute arbitrary JavaScript code in a Node.js process. (Rod Vagg)
- **v8_inspector**: Generate a UUID for each execution of the inspector. This provides additional security to prevent unauthorized clients from connecting to the Node.js process via the v8_inspector port when running with `--inspect`. Since the debugging protocol allows extensive access to the internals of a running process, and the execution of arbitrary code, it is important to limit connections to authorized tools only. Vulnerability originally reported by Jann Horn. (Eugene Ostroukhov)

### Commits

- [[`99e4eee8ef`](https://github.com/nodejs/node/commit/99e4eee8ef)] - **build**: do not define ZLIB_CONST (Bradley T. Hughes) [#9122](https://github.com/nodejs/node/pull/9122)
- [[`cae9eb35f0`](https://github.com/nodejs/node/commit/cae9eb35f0)] - **crypto**: fix openssl.cnf FIPS handling & testing (Rod Vagg) [nodejs/node-private#82](https://github.com/nodejs/node-private/pull/82)
- [[`c947d448da`](https://github.com/nodejs/node/commit/c947d448da)] - **deps**: cherry-pick 0e14baf712 from V8 upstream (Rod Vagg) [nodejs/node-private#80](https://github.com/nodejs/node-private/pull/80)
- [[`647afe9d9a`](https://github.com/nodejs/node/commit/647afe9d9a)] - **inspector**: generate UUID for debug targets (Eugene Ostroukhov) [nodejs/node-private#79](https://github.com/nodejs/node-private/pull/79)
- [[`1ea0358a91`](https://github.com/nodejs/node/commit/1ea0358a91)] - **node**: --openssl-config cli argument (Fedor Indutny) [nodejs/node-private#78](https://github.com/nodejs/node-private/pull/78)
- [[`455272ad33`](https://github.com/nodejs/node/commit/455272ad33)] - **(SEMVER-MINOR)** **src**: add process.release.lts property (Rod Vagg) [#3212](https://github.com/nodejs/node/pull/3212)
- [[`9ace073949`](https://github.com/nodejs/node/commit/9ace073949)] - **win,build**: try multiple timeservers when signing (Rod Vagg) [#9155](https://github.com/nodejs/node/pull/9155)

Windows 32-bit Installer: https://nodejs.org/dist/v6.9.0/node-v6.9.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v6.9.0/node-v6.9.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v6.9.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v6.9.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v6.9.0/node-v6.9.0.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v6.9.0/node-v6.9.0-darwin-x64.tar.gz \
Linux 32-bit Binary: https://nodejs.org/dist/v6.9.0/node-v6.9.0-linux-x86.tar.xz \
Linux 64-bit Binary: https://nodejs.org/dist/v6.9.0/node-v6.9.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v6.9.0/node-v6.9.0-linux-ppc64le.tar.xz \
SmartOS 32-bit Binary: https://nodejs.org/dist/v6.9.0/node-v6.9.0-sunos-x86.tar.xz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v6.9.0/node-v6.9.0-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v6.9.0/node-v6.9.0-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v6.9.0/node-v6.9.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v6.9.0/node-v6.9.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v6.9.0/node-v6.9.0.tar.gz \
Other release files: https://nodejs.org/dist/v6.9.0/ \
Documentation: https://nodejs.org/docs/v6.9.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

a5738a8061e5964d9851047355bab10c02046997adad0592d3e0c834c19c9599  node-v6.9.0-aix-ppc64.tar.gz
fd0f58487cd72d78e857bfc24061cfe77353e1571a17182b4e38273782648edf  node-v6.9.0-darwin-x64.tar.gz
fb512f229ea2b9d55a67ec3244a37b001a4f81c0ef449df2bf4fd823c06b8571  node-v6.9.0-darwin-x64.tar.xz
c2848e6ef6de3d771d3523ca8da2de6c949729476b3723cabf82836af4b723b8  node-v6.9.0-headers.tar.gz
1f8ebe4f1bc35b4e0399c05c208e79b497f84a18de5b46b10348948e9d3241d3  node-v6.9.0-headers.tar.xz
e9ff08e622436007594dcbff1b528023aaa2397c38fdc961d130730b90fdc814  node-v6.9.0-linux-arm64.tar.gz
b118ce433fa64868a46b5bf482e5c5c698df435ea40b1ec8429b7aae828b583e  node-v6.9.0-linux-arm64.tar.xz
ce870c8839ce5d46a36d11ae882e59cff6e361c46714428da3464504a3f0769a  node-v6.9.0-linux-armv6l.tar.gz
b1db436ec7ab97b2c3cf7b495b1ac57191afd1a71dc3d939a535c7cbbbf329d0  node-v6.9.0-linux-armv6l.tar.xz
97aaf1dba42c7544f8a2ad1e34e0f032d7645227e60928b9b522e257bd219256  node-v6.9.0-linux-armv7l.tar.gz
5fedf1319d4946fda7a20f8452f5ded4fb9707ce1661710e69616884e830f96c  node-v6.9.0-linux-armv7l.tar.xz
325b89f57374627d19de246dd1148699ea88b02139cecbdbe218b519fc8a328c  node-v6.9.0-linux-ppc64le.tar.gz
5fe38d010a3bfb179c2e3394937e7eb64b0a3076aff26d776fa1c5d67d9672b9  node-v6.9.0-linux-ppc64le.tar.xz
bf21e314c2bcf0ea379d2a6b2c017b0446ad8fb822dbc3c2fc04d594b727b23a  node-v6.9.0-linux-ppc64.tar.gz
44c3c0f3cb84216ef73d47acf886ca2aed2765640b76bbabf9a4fd8dba4961c1  node-v6.9.0-linux-ppc64.tar.xz
4aa6e76334e3bba1919c65a2a0dcabd28c48a282402b2f523807f31a588896e8  node-v6.9.0-linux-s390x.tar.gz
a2069e6d13c8ed1ffd80eaf039689abf82b300463125f1753e5467ecdef1ab6d  node-v6.9.0-linux-s390x.tar.xz
a9aafa2499097b315e1554b882923a6e2f9c446d24eaea53630f0fdbe075b226  node-v6.9.0-linux-x64.tar.gz
1fec8901f9158061eeeba407103c73f627d03c856cdaeb995c274d200c004a6f  node-v6.9.0-linux-x64.tar.xz
5520dc47e11b377064ce99a60e640dfdf9abc29916bcd110307474463af9efb1  node-v6.9.0-linux-x86.tar.gz
b15ed03aa7d113b6803e7e7ec7adebd310d08e6c6c807d9a2776da7421d502e6  node-v6.9.0-linux-x86.tar.xz
c91da719ec4db0f4397f93dbf604d4d77f3424e7ac5778ca120f61d5dd0bac9e  node-v6.9.0.pkg
e623e9bd2dd3554fdb9ec53ca3fe6e0d1a3715c05766bfc9a002d3289adbbe39  node-v6.9.0-sunos-x64.tar.gz
b748b5c427acbe1e8ac0a7a2a0af88d11120fe9019509841664ce7af2111a46b  node-v6.9.0-sunos-x64.tar.xz
2e48eb1506f54549df5e7c5727cd5c171aaf838f22caa848aeb2a3011db7f171  node-v6.9.0-sunos-x86.tar.gz
148f45f8e6ccf13e477d4ad0fee91fd41fe10af1d32ff6fc48ed3271bff26e1f  node-v6.9.0-sunos-x86.tar.xz
2e2657d2ece89782ca9e2cc0300f9119998e73382caa7ad2995ab81cc26ad923  node-v6.9.0.tar.gz
656342ed8a84c95a36af902f309aeeca7103b16d61c02925bd37bd47d2194915  node-v6.9.0.tar.xz
cbf07f7e472a3e7a574aac2350b7cac42e53e71b80fe4f910b32de452ce36572  node-v6.9.0-win-x64.7z
7c1c970d957c855c37ad16843ee5eb0f5369fbc42b24a5cd3dce18597dda087d  node-v6.9.0-win-x64.zip
8547a1a8ec2cc73194f7c670fa250a1357b20d97e1d76f256964dc95d9af9008  node-v6.9.0-win-x86.7z
ce35aeea6832fec2c5237e8958953acad59fafe89424335d1d882fc23199203f  node-v6.9.0-win-x86.zip
c20bce1c2dd7b0b30ff86e6ac32b4a79357165f1a3613ef111a26a0f5c621895  node-v6.9.0-x64.msi
e73dba32de3166b5de6f15f8677b1a8f8d67fa0983e660408518a8b259a3151b  node-v6.9.0-x86.msi
9ac737d4329515114d000766bd1e7ab8803a62ad9cddcf3629b1fc65b785cc7c  win-x64/node.exe
24fed33afb5c91284aea7d1c446687bae152ab915d4394af0752c32d75649ed2  win-x64/node.lib
892723c55b31b75e87356db0915e8bb09e449d7f8c062b64ea7d21c7f9283c26  win-x64/node_pdb.7z
740bb0435e8f6ca410ea3cad0f7e52e3dc788b870d3e05580159c81b600b0cd9  win-x64/node_pdb.zip
70dfa76f7e8b59e401002cae7d23565e9fde20ede65d47a900077608d5f3b347  win-x86/node.exe
1e2b080e083684e4d1ddf2a0bdf7415006e0c5bf1bae82cc35383c8642196735  win-x86/node.lib
c1da9b46e96bfacb267cb70932069eaf1c8ed4f1a6de7920bef2ccd7a73d7b3b  win-x86/node_pdb.7z
1b22b2f42b00af17ceacc0463120dec16a9d1582a537d0865e252a51dd8bef3e  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----
Version: GnuPG v1

iQEcBAEBCAAGBQJYBlkOAAoJEMJzeS99g1RdBq8IAME4kDcDjbbGbw2iviVXiEm1
IzBfYYHuxc3AUzxB9kEOkoYO++lR9Uo6pbhQTzYr2cTCQ46IhrAL+xSVX+Awhv2T
BSDwGTXCDAi5jTIQFNjJXfunSIdt9qGQKI/eLQ48Q00Cz9xvrFWgyZO97wwfUXCw
S3+y5fVDDgvUcrTybr3FWbIu6cjL6UQ8rW9E/clpa3KJehPjWBLkXoFmOrhL8e5/
yKvSJLID+z8PnDceFvwK8IdEQ/cZhh6XkbE2hZfM45QPyELrAhlHompv1yDFItqm
LOnBqVa0ZyoArPeQolTy/Gt8K0K44aC7fWzGnFWc0xcuso9c5RWgWR5iNNlzUpI=
=biwr
-----END PGP SIGNATURE-----

```
