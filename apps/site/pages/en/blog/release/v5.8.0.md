---
date: '2016-03-09T15:57:36.574Z'
category: release
title: Node v5.8.0 (Current)
layout: blog-post
author: <PERSON>
---

### Notable changes

- **child_process**: `send()` now accepts an options parameter (cjihrig) [#5283](https://github.com/nodejs/node/pull/5283).
  - Currently the only option is `keepOpen`, which keeps the underlying socket open after the message is sent.
- **constants**: `ENGINE_METHOD_RSA` is now correctly exposed (<PERSON>) [#5463](https://github.com/nodejs/node/pull/5463).
- Fixed two regressions which originated in v5.7.0:
  - **http**: Errors inside of http client callbacks now propagate correctly (<PERSON>) [#5591](https://github.com/nodejs/node/pull/5591).
  - **path**: Fixed normalization of absolute paths (<PERSON>) [#5589](https://github.com/nodejs/node/pull/5589).
- **repl**: `start()` no longer requires an options parameter (cjihrig) [#5388](https://github.com/nodejs/node/pull/5388).
- **util**: Improved `format()` performance 50-300% (<PERSON>) [#5360](https://github.com/nodejs/node/pull/5360).

### Commits

- [[`12ca84fc7f`](https://github.com/nodejs/node/commit/12ca84fc7f)] - **benchmark**: add util.format benchmark (Evan Lucas) [#5360](https://github.com/nodejs/node/pull/5360)
- [[`b955d02266`](https://github.com/nodejs/node/commit/b955d02266)] - **benchmark**: fix lint errors (Rich Trott) [#5517](https://github.com/nodejs/node/pull/5517)
- [[`2abf866b6e`](https://github.com/nodejs/node/commit/2abf866b6e)] - **build**: update Node.js logo on Win installer (Robert Jefe Lindstaedt) [#5531](https://github.com/nodejs/node/pull/5531)
- [[`86900f8f2b`](https://github.com/nodejs/node/commit/86900f8f2b)] - **build**: correctly detect clang version (Stefan Budeanu) [#5553](https://github.com/nodejs/node/pull/5553)
- [[`a3017992e4`](https://github.com/nodejs/node/commit/a3017992e4)] - **(SEMVER-MINOR)** **child_process**: add keepOpen option to send() (cjihrig) [#5283](https://github.com/nodejs/node/pull/5283)
- [[`6d4887ccc2`](https://github.com/nodejs/node/commit/6d4887ccc2)] - **(SEMVER-MINOR)** **child_process**: support options in send() (cjihrig) [#5283](https://github.com/nodejs/node/pull/5283)
- [[`9db827c7aa`](https://github.com/nodejs/node/commit/9db827c7aa)] - **(SEMVER-MINOR)** **constants**: define ENGINE_METHOD_RSA (Sam Roberts) [#5463](https://github.com/nodejs/node/pull/5463)
- [[`85013456cd`](https://github.com/nodejs/node/commit/85013456cd)] - **deps**: upgrade to npm 3.7.3 (Kat Marchán) [#5369](https://github.com/nodejs/node/pull/5369)
- [[`67e9f65958`](https://github.com/nodejs/node/commit/67e9f65958)] - **dgram**: default send address to 127.0.0.1 or ::1 (Matteo Collina) [#5493](https://github.com/nodejs/node/pull/5493)
- [[`3c92352c8c`](https://github.com/nodejs/node/commit/3c92352c8c)] - **doc**: document directories in test directory (Michael Barrett) [#5557](https://github.com/nodejs/node/pull/5557)
- [[`7be726f86a`](https://github.com/nodejs/node/commit/7be726f86a)] - **doc**: add info to docs on how to submit docs patch (Sequoia McDowell) [#4591](https://github.com/nodejs/node/pull/4591)
- [[`eb5a95e04a`](https://github.com/nodejs/node/commit/eb5a95e04a)] - **doc**: fix typo in fs.symlink (Michaël Zasso) [#5560](https://github.com/nodejs/node/pull/5560)
- [[`9ad901ef44`](https://github.com/nodejs/node/commit/9ad901ef44)] - **doc**: improve unhandledException doc copy (James M Snell) [#5287](https://github.com/nodejs/node/pull/5287)
- [[`3bd96fdb0f`](https://github.com/nodejs/node/commit/3bd96fdb0f)] - **doc**: update link green to match homepage (silverwind) [#5548](https://github.com/nodejs/node/pull/5548)
- [[`cb7e4fbac9`](https://github.com/nodejs/node/commit/cb7e4fbac9)] - **doc**: update V8 URL (Craig Akimoto) [#5530](https://github.com/nodejs/node/pull/5530)
- [[`b54a26fa61`](https://github.com/nodejs/node/commit/b54a26fa61)] - **(SEMVER-MINOR)** **doc**: correct name of engine methods (Sam Roberts) [#5463](https://github.com/nodejs/node/pull/5463)
- [[`f3971f5817`](https://github.com/nodejs/node/commit/f3971f5817)] - **path**: fix normalize for absolutes (Evan Lucas) [#5589](https://github.com/nodejs/node/pull/5589)
- [[`e572e421b4`](https://github.com/nodejs/node/commit/e572e421b4)] - **(SEMVER-MINOR)** **repl**: accept no arguments to start() (cjihrig) [#5388](https://github.com/nodejs/node/pull/5388)
- [[`5e6d706758`](https://github.com/nodejs/node/commit/5e6d706758)] - **src,http**: fix uncaughtException miss in http (Trevor Norris) [#5591](https://github.com/nodejs/node/pull/5591)
- [[`9dc94d7b09`](https://github.com/nodejs/node/commit/9dc94d7b09)] - **test**: add test-npm-install to parallel tests suite (Myles Borins) [#5166](https://github.com/nodejs/node/pull/5166)
- [[`4f20f31b3e`](https://github.com/nodejs/node/commit/4f20f31b3e)] - **test**: remove broken debugger scenarios (Rich Trott) [#5532](https://github.com/nodejs/node/pull/5532)
- [[`29e26b38c5`](https://github.com/nodejs/node/commit/29e26b38c5)] - **test**: bug repro for vm function redefinition (cjihrig) [#5528](https://github.com/nodejs/node/pull/5528)
- [[`e6210d5f50`](https://github.com/nodejs/node/commit/e6210d5f50)] - **test**: prevent flakey test on pi2 (Trevor Norris) [#5537](https://github.com/nodejs/node/pull/5537)
- [[`40b36baa2f`](https://github.com/nodejs/node/commit/40b36baa2f)] - **test**: check memoryUsage properties (Wyatt Preul) [#5546](https://github.com/nodejs/node/pull/5546)
- [[`048c0f4738`](https://github.com/nodejs/node/commit/048c0f4738)] - **tools**: reduce verbosity of cpplint (Sakthipriyan Vairamani) [#5578](https://github.com/nodejs/node/pull/5578)
- [[`7965c897e0`](https://github.com/nodejs/node/commit/7965c897e0)] - **tools**: enable no-self-assign ESLint rule (Rich Trott) [#5552](https://github.com/nodejs/node/pull/5552)
- [[`5aa17dc136`](https://github.com/nodejs/node/commit/5aa17dc136)] - **tools**: support testing known issues (cjihrig) [#5528](https://github.com/nodejs/node/pull/5528)
- [[`9a3e87e9a8`](https://github.com/nodejs/node/commit/9a3e87e9a8)] - **tools**: enable linting for benchmarks (Rich Trott) [#5517](https://github.com/nodejs/node/pull/5517)
- [[`c4fa2a6715`](https://github.com/nodejs/node/commit/c4fa2a6715)] - **tools**: enable no-extra-parens in ESLint (Rich Trott) [#5512](https://github.com/nodejs/node/pull/5512)
- [[`971edde0cb`](https://github.com/nodejs/node/commit/971edde0cb)] - **util**: improve format() performance further (Brian White) [#5360](https://github.com/nodejs/node/pull/5360)
- [[`c32d460747`](https://github.com/nodejs/node/commit/c32d460747)] - **util**: improve util.format performance (Evan Lucas) [#5360](https://github.com/nodejs/node/pull/5360)

Windows 32-bit Installer: https://nodejs.org/dist/v5.8.0/node-v5.8.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v5.8.0/node-v5.8.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v5.8.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v5.8.0/win-x64/node.exe \
Mac OS X 64-bit Installer: https://nodejs.org/dist/v5.8.0/node-v5.8.0.pkg \
Mac OS X 64-bit Binary: https://nodejs.org/dist/v5.8.0/node-v5.8.0-darwin-x64.tar.gz \
Linux 32-bit Binary: https://nodejs.org/dist/v5.8.0/node-v5.8.0-linux-x86.tar.xz \
Linux 64-bit Binary: https://nodejs.org/dist/v5.8.0/node-v5.8.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v5.8.0/node-v5.8.0-linux-ppc64le.tar.xz \
SmartOS 32-bit Binary: https://nodejs.org/dist/v5.8.0/node-v5.8.0-sunos-x86.tar.xz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v5.8.0/node-v5.8.0-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v5.8.0/node-v5.8.0-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v5.8.0/node-v5.8.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v5.8.0/node-v5.8.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v5.8.0/node-v5.8.0.tar.gz \
Other release files: https://nodejs.org/dist/v5.8.0/ \
Documentation: https://nodejs.org/docs/v5.8.0/api/

Shasums (GPG signing hash: SHA512, file hash: SHA256):

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA512

8c16f21a1c8882ba5875d0da617c817aa5005e514bd460dbf32aaeb3ffa477fe  node-v5.8.0-darwin-x64.tar.gz
c44e84f6d38e4e71f5d47da29a89ee1501e3006198fa44e1f6ebef37fa69ed74  node-v5.8.0-darwin-x64.tar.xz
1f8501149f7160d30341df82a9634e0e65256d096f092c68ad820bcd435000eb  node-v5.8.0-headers.tar.gz
828d525ca284a7a49f02486775173c6b19f2a12587acef4175a7a327ac05732d  node-v5.8.0-headers.tar.xz
0c2c0fa859c5be13cd1404f3fb14d37e38a67fb2fc075c7a37d4ae70374544bf  node-v5.8.0-linux-arm64.tar.gz
7fb3085dcd68da5b272452254e2284d53aa9b42fe4b92ec867448b8c4e66278a  node-v5.8.0-linux-arm64.tar.xz
54c362af9bc80b9e283bee7807fb2b1e9207ac77e61207b13fcf9f9acecd293f  node-v5.8.0-linux-armv6l.tar.gz
814f7589b932cb88081226ee02212e3593e016945f18940cfdc576c9b15a0575  node-v5.8.0-linux-armv6l.tar.xz
cdb0355fc41ac7010926f1a35f07f0dfe94020572d70296f91e60d18ab4873f5  node-v5.8.0-linux-armv7l.tar.gz
fe40ba09733fd754dee864daee4cda9007d4ee8c48c1eb0343dd72ea007e8e8b  node-v5.8.0-linux-armv7l.tar.xz
59f4745346786fdad8d58022b5eefc7cd5ec143d5cce03c66c1243e531fd10e8  node-v5.8.0-linux-ppc64le.tar.gz
39f651f94be359f759d8a0634ef1a8afef8b86316d6d86518a7a898bd4536c8b  node-v5.8.0-linux-ppc64le.tar.xz
3d33efa2421ed0769cc23ac172203b7db9fe4914a186009ddbac28c9aadb0896  node-v5.8.0-linux-x64.tar.gz
503255baf9517344dc277e6ca4a495defeb4c52e2b5628e67b3522ec6eb5e318  node-v5.8.0-linux-x64.tar.xz
b302ce9d5d156857576d892cfe8108363b30c20bd3133011fb2050d56bb238e8  node-v5.8.0-linux-x86.tar.gz
6b324b7568d01ef273223d36d11e1323cbdb43f7fea89a945c132a4163f9fdf6  node-v5.8.0-linux-x86.tar.xz
e407dd96cfee06f1a79eaa2d26c1745afd7224edb9fe62076a15622798df8479  node-v5.8.0.pkg
235783950678e3f343c5ce8e567a4600141ff9ad87aca0364bea659a82563f6c  node-v5.8.0-sunos-x64.tar.gz
736cfacd40a64d2c0036a9a8ddd705978c39059635584f1fd196d2cf8e6b6726  node-v5.8.0-sunos-x64.tar.xz
5edb23172d0a2b064dddb090ed5dae06a12db50dcc71f652748076d994f607ba  node-v5.8.0-sunos-x86.tar.gz
76a12b6141b89460fc752a7a726fea50526df6ae70341f6cfad5d48acdf575e7  node-v5.8.0-sunos-x86.tar.xz
e8cd580cec1c62053e6357633b9e46d49df1fd5b9fc5c804ca59ba80d722b898  node-v5.8.0.tar.gz
c1246d473d6650ca02ab168eaf840e9edda82a4190ff08165fc44a7b381a9361  node-v5.8.0.tar.xz
be2d055f593c080f2c48e8b9e3a7f864b34bc8763d8903d4b1470bb153bdaec0  node-v5.8.0-x64.msi
10a9c0445926e2a2544d426954c6810ef25fdb9f3f16a4a9921e5ba66b5dbb48  node-v5.8.0-x86.msi
638dc7e840206d03855d940d18e4ed92855034683290ef30ef505a3d1395b88e  win-x64/node.exe
5cfc85c3ea25fec18abfe3f74d6fc89956a8cce6c6cb1c2a54808cc1213d5b03  win-x64/node.lib
3b94153c8601764b8ca5b8836270123257c17f1f02c2850540540c3151089d84  win-x86/node.exe
f274baee290a22510a591595572a4fa7f465a50a4fa94ade3cab72a934558aac  win-x86/node.lib
-----BEGIN PGP SIGNATURE-----
Comment: GPGTools - https://gpgtools.org

iQIcBAEBCgAGBQJW4EN5AAoJEEX17r2BPa6OzqEP/jqXzIjGG+4RWjvnZrsjOUrP
fWhr3ppECdrHRw20J5aXbUSF8k2mLrDfx8pNvRXbS+aQkkgXCXWnlSnP8uysoEMr
fgdecsh+gxqEQ9xoC+ZX7r1h3Uog/6xA8iXUiB8R7r1SFQf27UNt0y0EFOjHls3M
EfblKJ87dK0qRef9adtLqXrRh6Nymwbp4XIf2TtsZwpgwffv72igh96XdptaeDs8
Bdp7VaeYFfWl/I8cxlbLY4gMNbMkrp3OZAus1hcTFG4pz9X3PD+EPTuMVnOAmSnK
s23hExZ6HFlR2qi40f/e7wFXRlkbBrvNFvK5/8AV9KdEEB9KWCzjZVIn/SCPpvxE
OKeStQqai5j2pB+w2nTv/yR/kMz6zagnycqsn3N6Di+r5E1rlE1P6fV41QGBqJZK
M6gG9Eol+AUSy7r8kW2/DFA96I7LlOGAb4EgPS17/EG9weeLTQqIuAwqGDTnF5HZ
X8g4nynrHqHwfVNW7t74pbZSVUGYPklI/87J419HPUKpMNhNSQVuwfXdk1r0PbGP
jpE/SiK29/vah7i4vDqIuCjTSmOsaSvqst1NOEUye9sgos+D91o19cSB8hF5QxDw
lzBmadMQq7i+uc3L7RVXOZ8jTPHlPySuwvOQOQvOH8veL4tITKWnyACYIlr+ED5I
5DyqJwkBGgB5A3gWOpu7
=Giqy
-----END PGP SIGNATURE-----

```
