---
date: '2019-02-28T12:47:20.636Z'
category: release
title: Node v6.17.0 (LTS)
layout: blog-post
author: <PERSON>
---

**This is a security release**. All Node.js users should consult the security release summary at /blog/vulnerability/february-2019-security-releases/ for details on patched vulnerabilities.

Fixes for the following CVEs are included in this release:

- Node.js: Denial of Service with keep-alive HTTP connections (CVE-2019-5739)
- Node.js: Slowloris HTTP Denial of Service with keep-alive (CVE-2019-5737)
- OpenSSL: 0-byte record padding oracle (CVE-2019-1559)

### Notable Changes

- **deps**: OpenSSL has been upgraded to 1.0.2r which contains a fix for [CVE-2019-1559](https://www.openssl.org/news/secadv/20190226.txt). Under certain circumstances, a TLS server can be forced to respond differently to a client if a zero-byte record is received with an invalid _padding_ compared to a zero-byte record with an invalid _MAC_. This can be used as the basis of a padding oracle attack to decrypt data.
- **http**:
  - Backport `server.keepAliveTimeout` to prevent keep-alive HTTP and HTTPS connections remaining open and inactive for an extended period of time, leading to a potential Denial of Service (DoS). (CVE-2019-5739 / [Timur Shemsedinov](https://github.com/tshemsedinov), [Matteo Collina](https://twitter.com/matteocollina))
  - Further prevention of "Slowloris" attacks on HTTP and HTTPS connections by consistently applying the receive timeout set by `server.headersTimeout` to connections in keep-alive mode. Reported by Marco Pracucci ([Voxnest](https://voxnest.com)). (CVE-2019-5737 / Matteo Collina)

### Commits

- [[`b282c68ce8`](https://github.com/nodejs/node/commit/b282c68ce8)] - **deps**: add -no_rand_screen to openssl s_client (Shigeki Ohtsu) [nodejs/io.js#1836](https://github.com/nodejs/io.js/pull/1836)
- [[`a80ef49dcf`](https://github.com/nodejs/node/commit/a80ef49dcf)] - **deps**: fix asm build error of openssl in x86_win32 (Shigeki Ohtsu) [iojs/io.js#1389](https://github.com/iojs/io.js/pull/1389)
- [[`1d3c412101`](https://github.com/nodejs/node/commit/1d3c412101)] - **deps**: fix openssl assembly error on ia32 win32 (Fedor Indutny) [iojs/io.js#1389](https://github.com/iojs/io.js/pull/1389)
- [[`661fd61c3a`](https://github.com/nodejs/node/commit/661fd61c3a)] - **deps**: copy all openssl header files to include dir (Shigeki Ohtsu)
- [[`da12284235`](https://github.com/nodejs/node/commit/da12284235)] - **deps**: upgrade openssl sources to 1.0.2r (Shigeki Ohtsu)
- [[`b13b4a9ffb`](https://github.com/nodejs/node/commit/b13b4a9ffb)] - **http**: prevent slowloris with keepalive connections (Matteo Collina) [nodejs-private/node-private#162](https://github.com/nodejs-private/node-private/pull/162)
- [[`e9ae4aaaad`](https://github.com/nodejs/node/commit/e9ae4aaaad)] - **http**: fix timeout reset after keep-alive timeout (Alexey Orlenko) [#13549](https://github.com/nodejs/node/pull/13549)
- [[`f23b3b6bad`](https://github.com/nodejs/node/commit/f23b3b6bad)] - **(SEMVER-MINOR)** **http**: destroy sockets after keepAliveTimeout (Timur Shemsedinov) [#2534](https://github.com/nodejs/node/pull/2534)
- [[`190894448b`](https://github.com/nodejs/node/commit/190894448b)] - **openssl**: fix keypress requirement in apps on win32 (Shigeki Ohtsu) [iojs/io.js#1389](https://github.com/iojs/io.js/pull/1389)
- [[`06a208d316`](https://github.com/nodejs/node/commit/06a208d316)] - **test**: refactor test-http-server-keep-alive-timeout (realwakka) [#13448](https://github.com/nodejs/node/pull/13448)
- [[`1c7fbdc53b`](https://github.com/nodejs/node/commit/1c7fbdc53b)] - **test**: improve test-https-server-keep-alive-timeout (Rich Trott) [#13312](https://github.com/nodejs/node/pull/13312)

Windows 32-bit Installer: https://nodejs.org/dist/v6.17.0/node-v6.17.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v6.17.0/node-v6.17.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v6.17.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v6.17.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v6.17.0/node-v6.17.0.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v6.17.0/node-v6.17.0-darwin-x64.tar.gz \
Linux 32-bit Binary: https://nodejs.org/dist/v6.17.0/node-v6.17.0-linux-x86.tar.xz \
Linux 64-bit Binary: https://nodejs.org/dist/v6.17.0/node-v6.17.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v6.17.0/node-v6.17.0-linux-ppc64le.tar.xz \
Linux PPC BE 64-bit Binary: https://nodejs.org/dist/v6.17.0/node-v6.17.0-linux-ppc64.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v6.17.0/node-v6.17.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v6.17.0/node-v6.17.0-aix-ppc64.tar.gz \
SmartOS 32-bit Binary: https://nodejs.org/dist/v6.17.0/node-v6.17.0-sunos-x86.tar.xz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v6.17.0/node-v6.17.0-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v6.17.0/node-v6.17.0-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v6.17.0/node-v6.17.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v6.17.0/node-v6.17.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v6.17.0/node-v6.17.0.tar.gz \
Other release files: https://nodejs.org/dist/v6.17.0/ \
Documentation: https://nodejs.org/docs/v6.17.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

9eb3b9f5e754d803b5e0cdcd406cc2ef8e6656f6bdcf1a3bb1eab14b3ffd9cb2  node-v6.17.0-aix-ppc64.tar.gz
1d4f0a24c09e8e3ed1ac0e4b62aba0cb86e9a293eef6c7a63876ba120b760583  node-v6.17.0-darwin-x64.tar.gz
cc72836d5c351dd1c47ac431f496fcaac4185aefa9f35a994716dd0d79f602cc  node-v6.17.0-darwin-x64.tar.xz
a1051c5529511b343acfc9cfb02515b7cd40e820fec571cff70d9afd4c48435e  node-v6.17.0-headers.tar.gz
b53e3886514be3674125617caf5c761b849f197f0b64aeabb2821b2de30a241c  node-v6.17.0-headers.tar.xz
443f7656c541ba66b4aa50a5fa81e34e38714851006194ed63fee9cf48936c73  node-v6.17.0-linux-arm64.tar.gz
427f6c0ad02d2ad8e203a2c4959fadeaaa6043ba8caf2e1978c4a4d5b38737ab  node-v6.17.0-linux-arm64.tar.xz
40b3791472e581e7caa6f0d19cfe01d94654b7d0f2300302376f071f8fef468c  node-v6.17.0-linux-armv6l.tar.gz
94b8cac4800b7a0000548b69d38421398d222ee939a94af4add4a56369ba1158  node-v6.17.0-linux-armv6l.tar.xz
ad3ea3925fb4d74c2d7d3fa7b23d48b97b01846e31ac6bf0a70ab6f2eaa67f65  node-v6.17.0-linux-armv7l.tar.gz
a3fea75b0b052327a8f7b1042b794d797a54b7f39280194037f82b78e8624570  node-v6.17.0-linux-armv7l.tar.xz
2a959dd29f8a0fe52bbe3a0e287dfe306fb6ed4a90d4bb61b3bf856ec11d4c33  node-v6.17.0-linux-ppc64le.tar.gz
7d832b04af7eb3cd233e686ed3fe40fd96b116b8dc9d3a0961a64572cfa8c0f4  node-v6.17.0-linux-ppc64le.tar.xz
53f5995fe09548eaef4869b26fe3c0415236a2e07b312cb4082bd6aa84868e14  node-v6.17.0-linux-ppc64.tar.gz
85a6cab4354ca4e62f4bc5ff1ef14111e3d193444fb6cf97cb5e918b8ede90fb  node-v6.17.0-linux-ppc64.tar.xz
b3ae1a97ea093490c74c5a0ff8f0d8a9591c33545e06b176d3bce875e2a5ba9f  node-v6.17.0-linux-s390x.tar.gz
b62dec10c48bc68786611788998bcfa4f2e7df4cf54e0188e601c647871a0fa3  node-v6.17.0-linux-s390x.tar.xz
547422724489a5391d4a137cd130020c73f8f8c721754c67c7692003fe2b6015  node-v6.17.0-linux-x64.tar.gz
a5b2be71bcb1f182e143748007ee2f7cae624eb3b84263d7d7ea004b33b27399  node-v6.17.0-linux-x64.tar.xz
5e0ac63b07c5dcf34c78d25400f1e6a3b6f41eae94e0a6a0571f60369d2e5534  node-v6.17.0-linux-x86.tar.gz
a09c05f8ea60406f44ef4a140d182c353c5679f693c08a5247811deac678f6eb  node-v6.17.0-linux-x86.tar.xz
fa7a4f497bb81ecdb4e077d0bf429845649376581ffab89ab07b82c211c04832  node-v6.17.0.pkg
94b168a55c32e466407fa459df7f245f7ef7fefaf42cfeed9326e936716e308f  node-v6.17.0-sunos-x64.tar.gz
4c4928e84afbbcf7ffb731b74f7d1dfa73f8f6cb17c0023e7f87cc009b1f4f08  node-v6.17.0-sunos-x64.tar.xz
707134900d321b331cea8c76959bf07239fed62998f04f8aa1e3fa76ca785b32  node-v6.17.0-sunos-x86.tar.gz
044f1e1226c7830af19d156090f9593269d9e2d755c842107407b3c1be39d072  node-v6.17.0-sunos-x86.tar.xz
ef7b5cdffd110c194ef25fe14e455e0dea0fd894550ba33aadb90b0da8e14be2  node-v6.17.0.tar.gz
c1dac78ea71c2e622cea6f94ba97a4be49329a1d36cd05945a1baf1ae8652748  node-v6.17.0.tar.xz
4757a015153d9beae528417a3259688be725dbffc9e0eb6458118845427d213b  node-v6.17.0-win-x64.7z
c03cb9ff76c34c41d3e193ab5aa589d40dfaeb80cb0ee24662e547e1a35b1e3c  node-v6.17.0-win-x64.zip
65108ba51eb52dcdca98f7d7846869a460926644ebcf377ec87bd55f9ae704c4  node-v6.17.0-win-x86.7z
e8f235072b90f374b6d46fe38cfc5ffd8f317aa0d42c4c9a31cc7b5c9f6dd240  node-v6.17.0-win-x86.zip
3a9171ab366ba45accc2ec898b50390d0020797b1cf16a16e9f3e105fcfdc95a  node-v6.17.0-x64.msi
e3478fcf22e3395a41f34cf7e64527e6cc532c109e5a8d67ab1de213e53f780c  node-v6.17.0-x86.msi
fdccb67349c2c558349097c204522297478acdaa50f2b916393d1ea4b23b4f23  win-x64/node.exe
858cd16b0124543466d663df5058abe3aeaa08f0e396a0619af749f8880f3f3b  win-x64/node.lib
a2785a1878a13b0a4945ce1ec6db409164c3c50d8c8315d710b8b7b89c8205e8  win-x64/node_pdb.7z
c25956040e9b7717976d61896cfdadaa56dfee26a6066a83b3f4dc566a99ccf0  win-x64/node_pdb.zip
3f877b57a1e1384a64888ad30e92ae57eedc8bc83563eefa06ab6af37846744d  win-x86/node.exe
3ab1236a78694e3e66dbf24d25016d03afefbce47be0d82cc80e454a0b089724  win-x86/node.lib
39306b9f993ebd672064b2c305eb48f003ab0ab5bfe9dfa62259ed98f80f33c2  win-x86/node_pdb.7z
c3f05efcf553c326e644c079efbbacffe704012109584527a052ae6dab04a610  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQEzBAEBCAAdFiEE3Y8jOLrnUB491ax4wnN5L32DVF0FAlx44SAACgkQwnN5L32D
VF2X6gf+NxTSCd/RtjL+tBbJAeNBHg4/s8nn9kngEgq+q/ybbABub06BhnyoZUeZ
U6JKXM79586vKi0EV9whiVWGDGcenRNBHIwWcbHMFtsl0QrDiz6UdXvWniNN43cL
+Vx7Tmqil2J641UaicEM8h8A0xG60YNEOcWXMzLIuVF21mF+9qcvPKMSR0jDYy+6
CXJqerft43JpRR9AeN9Ol3kgiwA8mZ0tQRS7vPgjKCIcHfCz6hHEYSqWLcOuqAFN
eotHEXfLmXgprcqz/Z7It3pUPramtLYXMqM7PEX810hBWbGb3g0+MSD92stz2BLU
1B4cp5rS5mia2iaAbrw/bcPbdQVHbQ==
=bIfA
-----END PGP SIGNATURE-----
```
