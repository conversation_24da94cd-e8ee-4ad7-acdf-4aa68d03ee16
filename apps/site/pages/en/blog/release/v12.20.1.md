---
date: '2021-01-04T18:23:08.344Z'
category: release
title: Node v12.20.1 (LTS)
layout: blog-post
author: <PERSON>
---

### Notable changes

This is a security release.

Vulnerabilities fixed:

- **CVE-2020-8265**: use-after-free in TLSWrap (High)
  Affected Node.js versions are vulnerable to a use-after-free bug in its
  TLS implementation. When writing to a TLS enabled socket,
  node::StreamBase::Write calls node::TLSWrap::DoWrite with a freshly
  allocated WriteWrap object as first argument. If the DoWrite method does
  not return an error, this object is passed back to the caller as part of
  a StreamWriteResult structure. This may be exploited to corrupt memory
  leading to a Denial of Service or potentially other exploits
- **CVE-2020-8287**: HTTP Request Smuggling in nodejs
  Affected versions of Node.js allow two copies of a header field in a
  http request. For example, two Transfer-Encoding header fields. In this
  case Node.js identifies the first header field and ignores the second.
  This can lead to HTTP Request Smuggling
  (https://cwe.mitre.org/data/definitions/444.html).
- **CVE-2020-1971**: OpenSSL - EDIPARTYNAME NULL pointer de-reference (High)
  This is a vulnerability in OpenSSL which may be exploited through Node.js.
  You can read more about it in
  https://www.openssl.org/news/secadv/20201208.txt

### Commits

- [[`5de5354918`](https://github.com/nodejs/node/commit/5de5354918)] - **deps**: update http-parser to http-parser@ec8b5ee63f (Richard Lau) [nodejs-private/node-private#236](https://github.com/nodejs-private/node-private/pull/236)
- [[`2eacfbec68`](https://github.com/nodejs/node/commit/2eacfbec68)] - **deps**: upgrade npm to 6.14.10 (Ruy Adorno) [#36571](https://github.com/nodejs/node/pull/36571)
- [[`96ec482d90`](https://github.com/nodejs/node/commit/96ec482d90)] - **deps**: update archs files for OpenSSL-1.1.1i (Myles Borins) [#36521](https://github.com/nodejs/node/pull/36521)
- [[`7ec0eb408b`](https://github.com/nodejs/node/commit/7ec0eb408b)] - **deps**: upgrade openssl sources to 1.1.1i (Myles Borins) [#36521](https://github.com/nodejs/node/pull/36521)
- [[`76ea9c5a7a`](https://github.com/nodejs/node/commit/76ea9c5a7a)] - **deps**: upgrade npm to 6.14.9 (Myles Borins) [#36450](https://github.com/nodejs/node/pull/36450)
- [[`420244e4d9`](https://github.com/nodejs/node/commit/420244e4d9)] - **http**: unset `F_CHUNKED` on new `Transfer-Encoding` (Matteo Collina) [nodejs-private/node-private#236](https://github.com/nodejs-private/node-private/pull/236)
- [[`4a30ac8c75`](https://github.com/nodejs/node/commit/4a30ac8c75)] - **http**: add test for http transfer encoding smuggling (Richard Lau) [nodejs-private/node-private#236](https://github.com/nodejs-private/node-private/pull/236)
- [[`92d430917a`](https://github.com/nodejs/node/commit/92d430917a)] - **http**: unset `F_CHUNKED` on new `Transfer-Encoding` (Fedor Indutny) [nodejs-private/node-private#236](https://github.com/nodejs-private/node-private/pull/236)
- [[`5b00de7d67`](https://github.com/nodejs/node/commit/5b00de7d67)] - **src**: retain pointers to WriteWrap/ShutdownWrap (James M Snell) [nodejs-private/node-private#230](https://github.com/nodejs-private/node-private/pull/230)

Windows 32-bit Installer: https://nodejs.org/dist/v12.20.1/node-v12.20.1-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v12.20.1/node-v12.20.1-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v12.20.1/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v12.20.1/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v12.20.1/node-v12.20.1.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v12.20.1/node-v12.20.1-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v12.20.1/node-v12.20.1-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v12.20.1/node-v12.20.1-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v12.20.1/node-v12.20.1-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v12.20.1/node-v12.20.1-aix-ppc64.tar.gz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v12.20.1/node-v12.20.1-sunos-x64.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v12.20.1/node-v12.20.1-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v12.20.1/node-v12.20.1-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v12.20.1/node-v12.20.1.tar.gz \
Other release files: https://nodejs.org/dist/v12.20.1/ \
Documentation: https://nodejs.org/docs/v12.20.1/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

85d6b2fb4517fdc2540eba89a70a6256541f89cb9970aa5176c10294c2d595ee  node-v12.20.1-aix-ppc64.tar.gz
da5d32de2e0f3e82b4bc4a33754a9ceedb3c031f8804e984de89d82074897795  node-v12.20.1-darwin-x64.tar.gz
9be4e4aefc1a9373d1472c335c0b85fa2c307e9a11e5df2901e12d8babd797b7  node-v12.20.1-darwin-x64.tar.xz
3e92fed328aca66d651160bf8e258a9c1ace2ae7a3ae61e63e66b8c1b061daa2  node-v12.20.1-headers.tar.gz
3b88ca1472bd7acbd5bafaa763dee0cc9641ba634dfdb1d4589e72928f80e8c9  node-v12.20.1-headers.tar.xz
3154628c02f2c920fed77e8dce1a8ae32333260666ebaaa7a3cd230f45d13e42  node-v12.20.1-linux-arm64.tar.gz
3c1dff2a7070214fa3947f8b7331c592e2d7c7347693da927b56cfd51ed70917  node-v12.20.1-linux-arm64.tar.xz
7283ced5d7c0cc036a35bc2e64b23e7d4b348848170567880edabcf5279f4f8a  node-v12.20.1-linux-armv7l.tar.gz
d4b34dc939b34e0a888d69e01713c5ba42b5718bccf72e816eb4bd644cf6240e  node-v12.20.1-linux-armv7l.tar.xz
0722467ef8361fa0a7562154b8907c4769a7ba96a6631e5a5212d9b0ca3edba9  node-v12.20.1-linux-ppc64le.tar.gz
c557bc21fd7b5e2150a946c9821462d70f86b62ce5d8e8f22fbc9951c0b51dca  node-v12.20.1-linux-ppc64le.tar.xz
68ded9db9837ea819881fb5b232c226a9924477e2fa901edecd32d3af2cfeb54  node-v12.20.1-linux-s390x.tar.gz
409e7b1b99ceca82c3fa36785de38bac80acf40189a6052e4226299b690113a6  node-v12.20.1-linux-s390x.tar.xz
c4d45bf46d4ef4b6a72384dfb0ab6c07aed5750bcd1c2fc9f29c0aaccc6a4363  node-v12.20.1-linux-x64.tar.gz
313014c7e0abe808ec8453d78f7892c430e1b282a6d3faf9904fcb72c79e8db6  node-v12.20.1-linux-x64.tar.xz
5d1a4c117ee3ac7aa98818d304a4a2b39427ce588147cf437a7c5beb7b45de21  node-v12.20.1.pkg
a166ccbfd282ab3caf99f10f5c8a91d4696c0ad163622ec81bbcef5e9f8c9092  node-v12.20.1-sunos-x64.tar.gz
e927caa2208b7f491d80edb2a051951d37770992c1de605df4392e55b052418d  node-v12.20.1-sunos-x64.tar.xz
5318a5db1484050430371b77dece281a5b078a14e6962e105aa5790bdb3c3bed  node-v12.20.1.tar.gz
e00eee325d705b2bfa9929b7d061eb2315402d7e8548945eac9870bf84321853  node-v12.20.1.tar.xz
90c9f86fc50f5320572562483f6f9e6d303f8c3bae26f14870c92b750d1d20b3  node-v12.20.1-win-x64.7z
63cb0ccb17d6071e5418661b9755dc6a47f89db6e0945cb92dafbd000d9cc654  node-v12.20.1-win-x64.zip
d9ed3fd1ef7787545e8132ac592278e9895c7e817d6545d1dac2d37e509e56bf  node-v12.20.1-win-x86.7z
6dc79f89cc7d0e2d6f12532bcb010fcedf32604d0f0d718f9c88d28696a98a13  node-v12.20.1-win-x86.zip
48c7e37bcee7847549ef186e4762b924ede44bb34bfcb77c4aa5eadaea31a7fa  node-v12.20.1-x64.msi
ded3789cef6bf338e472187c17c28fcd14ead36e2cc440981616228e78ad780a  node-v12.20.1-x86.msi
45d3ccc859462d16ef29e6a007df43fb94524a3deac3e8539920089b4a7a5895  win-x64/node.exe
2662b79e36ee678661554d290a3b8277c4c92cc74dffcda37a9f8f8e83287c73  win-x64/node.lib
b5e11e03d047f9a13e8d247eb15f75fb1df8a60f34464ac3bad828a2830d2f0a  win-x64/node_pdb.7z
a0047d62b0dc5ed2eca5a6125485d7778b60a2017ceaeffa12abab34fd7c0b0d  win-x64/node_pdb.zip
8dc5ce44c3d2e91b22ac8a081cae13df7c331615f0db3db4f076ee590a28ebfc  win-x86/node.exe
794dd4c597af2483d162426a37c99746d319aaa358219ace7bb179140f16d5f2  win-x86/node.lib
73f6208141f666286de13746322d88940fd126fdf97833948728f6829846339d  win-x86/node_pdb.7z
8fd3ce5355a9d6004bf3e99b9ac9fd0c33fa853ad4c0d243afc1cfe4c6642c40  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEyC+jrhy+3Gvka5NgxDzsRcF6uTwFAl/zWq8ACgkQxDzsRcF6
uTx3YQ//ZB73eV5+HdivoCetzzPk5IPdHKt4Axh1Q7SOpDRHd2Ne1Yac7VFHAfo+
SszTFc874tHVPpEJMCUISIjs1Zim2q9cUsCMYwyayEJd7SFbySGewXreb029oBJ5
VUQfY6ItqQmbPDK3SGw+dF4rmJO0BczDobJ3yelrAMJmKCokyAtSTkEHqsFTAMCd
IhxGHshZonyzfaffn7tfN4/JHeWv63WX8KWcNpcMDLJkZe3Giizgia17N2KtnruO
wN81yuqpCNcZEpHHqB9vj6WGTICcc7POdx0YtHmuMkEOBNQd0qBjoHPoxS/733R9
nifYtD3Mr+kO5aLnVMOUuA2rzDdRCOyBsIbx0l3GEZkOs4z3Y0zBw8vTI+UoC8Vo
Wtc8hh9gj8y3YKbgcfcZU8lvv6YuSsvCJ58RSwQ83tokk4xH+Q+caAW0YQNUQa21
OkcbDrc4naCF5THDh7ckSdhJDhXVW0A5U7Eg9m8iVS0l6JXToLilkcTQNRemlEnX
YvFKb8OmH+8Z9wDulbrIX3KVQzuU6+D5iY5FEDmSCc/7EQWp7ca3zNFn47DbVlEI
rhk85HhHqrO5ymoV93xakw/ICXGYSTTwbevpgi1J/u88KCA/P1KuohU5j3BSanlF
0Gw7X6gXmKZvwCbg+zWTWZH3ND9F7gGJ0s2v/lfE8aBsVWtjtL8=
=Ji2U
-----END PGP SIGNATURE-----

```
