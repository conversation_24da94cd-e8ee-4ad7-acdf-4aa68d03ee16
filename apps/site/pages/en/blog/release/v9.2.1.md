---
date: '2017-12-08T16:23:09.389Z'
category: release
title: Node v9.2.1 (Current)
layout: blog-post
author: <PERSON>
---

### Notable Changes

- **buffer**:
  - buffer allocated with an invalid content will now be zero filled (<PERSON>) [#17428](https://github.com/nodejs/node/pull/17428)
- **deps**:
  - openssl updated to 1.0.2n (<PERSON><PERSON><PERSON>) [#17526](https://github.com/nodejs/node/pull/17526)

### Commits

- [[`15bf640668`](https://github.com/nodejs/node/commit/15bf640668)] - **buffer**: zero-fill buffer allocated with invalid content (<PERSON>) [#17428](https://github.com/nodejs/node/pull/17428)
- [[`c0954f4ba1`](https://github.com/nodejs/node/commit/c0954f4ba1)] - **deps**: update openssl asm and asm_obsolete files (<PERSON><PERSON><PERSON>) [#17526](https://github.com/nodejs/node/pull/17526)
- [[`dfd7cd3038`](https://github.com/nodejs/node/commit/dfd7cd3038)] - **deps**: add -no_rand_screen to openssl s_client (Shigeki Ohtsu) [nodejs/io.js#1836](https://github.com/nodejs/io.js/pull/1836)
- [[`76e7ff2915`](https://github.com/nodejs/node/commit/76e7ff2915)] - **deps**: fix asm build error of openssl in x86_win32 (Shigeki Ohtsu) [iojs/io.js#1389](https://github.com/iojs/io.js/pull/1389)
- [[`593f21ee9c`](https://github.com/nodejs/node/commit/593f21ee9c)] - **deps**: fix openssl assembly error on ia32 win32 (Fedor Indutny) [iojs/io.js#1389](https://github.com/iojs/io.js/pull/1389)
- [[`235c78f968`](https://github.com/nodejs/node/commit/235c78f968)] - **deps**: copy all openssl header files to include dir (Shigeki Ohtsu) [#17526](https://github.com/nodejs/node/pull/17526)
- [[`b0ebe5cb4b`](https://github.com/nodejs/node/commit/b0ebe5cb4b)] - **deps**: upgrade openssl sources to 1.0.2n (Shigeki Ohtsu) [#17526](https://github.com/nodejs/node/pull/17526)
- [[`99fc75e9bc`](https://github.com/nodejs/node/commit/99fc75e9bc)] - **doc**: warn against filling buffer with invalid data (Anna Henningsen) [#17428](https://github.com/nodejs/node/pull/17428)
- [[`f0f9e1abf0`](https://github.com/nodejs/node/commit/f0f9e1abf0)] - **http2**: use correct connect event for TLS Socket (James M Snell) [#17328](https://github.com/nodejs/node/pull/17328)
- [[`65f209ccf1`](https://github.com/nodejs/node/commit/65f209ccf1)] - **http2**: use 'close' event instead of 'streamClosed' (James M Snell) [#17328](https://github.com/nodejs/node/pull/17328)
- [[`d3e2bf0c8d`](https://github.com/nodejs/node/commit/d3e2bf0c8d)] - **http2**: general cleanups in core.js (James M Snell) [#17209](https://github.com/nodejs/node/pull/17209)
- [[`6a76097fad`](https://github.com/nodejs/node/commit/6a76097fad)] - **http2**: major update to internals (James M Snell) [#17105](https://github.com/nodejs/node/pull/17105)
- [[`e14c0babe0`](https://github.com/nodejs/node/commit/e14c0babe0)] - **openssl**: fix keypress requirement in apps on win32 (Shigeki Ohtsu) [iojs/io.js#1389](https://github.com/iojs/io.js/pull/1389)

Windows 32-bit Installer: https://nodejs.org/dist/v9.2.1/node-v9.2.1-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v9.2.1/node-v9.2.1-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v9.2.1/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v9.2.1/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v9.2.1/node-v9.2.1.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v9.2.1/node-v9.2.1-darwin-x64.tar.gz \
Linux 32-bit Binary: https://nodejs.org/dist/v9.2.1/node-v9.2.1-linux-x86.tar.xz \
Linux 64-bit Binary: https://nodejs.org/dist/v9.2.1/node-v9.2.1-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v9.2.1/node-v9.2.1-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v9.2.1/node-v9.2.1-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v9.2.1/node-v9.2.1-aix-ppc64.tar.gz \
SmartOS 32-bit Binary: https://nodejs.org/dist/v9.2.1/node-v9.2.1-sunos-x86.tar.xz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v9.2.1/node-v9.2.1-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v9.2.1/node-v9.2.1-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v9.2.1/node-v9.2.1-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v9.2.1/node-v9.2.1-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v9.2.1/node-v9.2.1.tar.gz \
Other release files: https://nodejs.org/dist/v9.2.1/ \
Documentation: https://nodejs.org/docs/v9.2.1/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

8f8017bf8e4f953cf6596bd6b08f66df39fb2d791abee1254881b7c04bc1819d  node-v9.2.1-aix-ppc64.tar.gz
4263f3da0280e2decd3a2ab97672e6f718acdf704664a3f1251e4ea267ccc971  node-v9.2.1-darwin-x64.tar.gz
dbe3f00eed8ad35deb2b51318179e15156cb0970c00307c11f77369d32ee880e  node-v9.2.1-darwin-x64.tar.xz
73a8884866105a304723db0e4dbe6022791355e53307f7cebea69db3990338f4  node-v9.2.1-headers.tar.gz
1c0be12e2d485b7fd250205b73b12539a21f4ad0c469513f267c8d3f2fd22989  node-v9.2.1-headers.tar.xz
6731cc1cae080f98f0cace9922b1d019d63b3eed10aa99e8eec52fdda01432b0  node-v9.2.1-linux-arm64.tar.gz
09d362e2ed5f4af5e5dc2253bb4523d7bcb92135bcc11d9eb89ad8336229b756  node-v9.2.1-linux-arm64.tar.xz
6f0f39fcef67dece4a04700b90d30175127f588f1c70e59c697d8ca3b2c7db6d  node-v9.2.1-linux-armv6l.tar.gz
4b8e815924adad009edb658f606c3eb42ac60710fb6cc8926d4e475c62d8f74f  node-v9.2.1-linux-armv6l.tar.xz
655ce98427a2669a8bcc759761a767aada5407b97224d675162815cd3fd3e85f  node-v9.2.1-linux-armv7l.tar.gz
e8ccec3d393fe3a43dbcc69bb0c225681888bd869799ecbd75ce7e96453359a5  node-v9.2.1-linux-armv7l.tar.xz
81f59cea4463f8015488cb19a75a0bc8827a0e6b1eec18491f68e9cceb4b45c4  node-v9.2.1-linux-ppc64le.tar.gz
576050ddf2f804b0a7907ead3f8148b40c2072281e70c7909eada2da9944b45b  node-v9.2.1-linux-ppc64le.tar.xz
918786a5d907e672bd2cf84b10a9afc06be86d037f69235ca581e0c4c3a3fcb2  node-v9.2.1-linux-s390x.tar.gz
e5833f9a0a1d3e3a5f70848d0ee9c86d81562cde347033673d2bbe294ef63a2c  node-v9.2.1-linux-s390x.tar.xz
b8507b17277b1582320667605acde79a00a2a947182db2db9614ae0917235686  node-v9.2.1-linux-x64.tar.gz
548d2959939235ca56c98740f64b64058e43d1499d760603b7941b7c37ad10fe  node-v9.2.1-linux-x64.tar.xz
ac374441ea085e7cf82f244ac8206d4e1b338aacfb5c4532fa8f36f22b04e2b9  node-v9.2.1-linux-x86.tar.gz
b65604ea39f03de0c0ba7ab7656bfb67661ea2b4b860c202be69ed4d037fa099  node-v9.2.1-linux-x86.tar.xz
74954f93b2b308e884abf98b644da2cc2eeb3c62698bc3ea9fbea8d9af5e22a7  node-v9.2.1.pkg
0922b9cd2290ea44c9316cacbf62067271c2d2480a903e08d92f950a2fe147ef  node-v9.2.1-sunos-x64.tar.gz
94b0bbb53703cdef4e12593232888a21f2867e267fd57bf6d118e4b59db7bb79  node-v9.2.1-sunos-x64.tar.xz
3de9e75c76d475e5cf065ac1b8f314ee0698d85aa1262a64d3ac3f2efe150962  node-v9.2.1-sunos-x86.tar.gz
8c5aa10a3283dd045c112625b0dc26d82468aaefe8a468b17b65cca7e4ce82b3  node-v9.2.1-sunos-x86.tar.xz
f6e95f539c6501c5951084f6c3ac2a735ae76c268378a80a9a773527670dc7ad  node-v9.2.1.tar.gz
200de3c145e79d5da5361ab079df199cec13dbd10902df9cf9a75c6546cd4582  node-v9.2.1.tar.xz
90adffc98e023caaeaebfc7af5afdefc3a5e2441bb102ec2e598223d6c4c4b0c  node-v9.2.1-win-x64.7z
7b48ef7c718fe5748844f93101f9276a88ae1cf3c4c228f85306a6a266471b5b  node-v9.2.1-win-x64.zip
6f81daf00d1a84530b286ecc1f2e296cb423b9076debdda25b351c78d36b66c1  node-v9.2.1-win-x86.7z
f64514e77ee44274bf4cc2dd876cabeae977650464efb76d816d933ff772c79e  node-v9.2.1-win-x86.zip
57bc46262bcf52535c95786c608b0303ac034a8c3089ee6015f7ec74941e42dc  node-v9.2.1-x64.msi
9d622b2be2715fe7ca277224117983d0bed812958f0dc537fa7456b6c071d327  node-v9.2.1-x86.msi
04216d63b2934dd299c0ecdc7fc7fe4609d574d9893be564595378398a061a8f  win-x64/node.exe
71b1ccc1905a38dfb43dccd1ba063d7def7b8f7a7e6d05e1d7511205d091c158  win-x64/node.lib
0495fc9d7ffcb476cd7351656b294f0550abcccf2069d2ec2800b8af2a0a6592  win-x64/node_pdb.7z
3a4094caf793212ce7cd462c1a6b98c1a2436529183caedab3bc00473ba8f3f7  win-x64/node_pdb.zip
a3fb3f1421e2f516745aa51ac9773a29265ee66315ab73ffaad134e8ef2521d9  win-x86/node.exe
8e252ee42a19e2ab4410bf854285eb6ea7b77b20bc2a780acbf38d4c2be9b3f6  win-x86/node.lib
938cda3b0ba178638e82e10c84799df89f66f9c7fbf678e2e26693ecfcf2d0b2  win-x86/node_pdb.7z
239e2b45bfd4fc861753ecf33ffa8800280196527742a6bce63e9c7bb1178590  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEua6ZBf/XgD8lcUZhtjtTWkwgbKkFAloqurIACgkQtjtTWkwg
bKl6lhAAjrHjS0pVy9OxIBlGiCSWxI5wETKgf+U82fmr0sypR30us6DJf1wAGzMY
srsxo39CcciehiClWinmGSfTsx1ZvhgrpemD0mgxuMt9UUvHGdL2NCmA45Ze9fgX
tHwKM6pm0ld3mGRTGWpXsyCiOqUrBD0fVusqsCLROCIYnoSjD18mJNp4vNoiAcyH
8IOsBsxk2TrOa8mVEnYcT9ohmqo/Pok8CjvrNqflAJEND9myqbNpSPi1e2cIWX1J
5j5/xQAa1ldmA10wc6ClNriTsRH5D7rkEcbJB1PLVQEa8Bk+WG2PaFYdknbuaXmX
5STSCrH+dBxHQdhYvcDcva74xIKuKVEJyfqYpEcRZzW9yxtv62T4VnbkuBFvDVpc
sa5JJsv/nOEZw+iWkFfg5cI3RjwiH4PPLpXdsknU/UtmapbC8AM757pWSiZeUq2F
eySlK658hLqumGdSzdnsjdUQhZfmrrgSpIF7AoQQ+hk3kCSpd/u9/nca8TsJGeRZ
4cxNTb97Fe5Qwqy91ymZ9l0yruPyjKieFCTDz/2P803qWp3YSqNiuEeD6GoW00WB
OcDoB3I6MLyEEMw0dtfDNwbz0b4ifcWi7+PVkz7uMDH4NZoqkMK7KDcxPQTc1aKq
J26TmfC11zYLAXNPE4x80OWkJTZ6peLsoAoOoCD6/pRv8SVgHUo=
=mlpE
-----END PGP SIGNATURE-----

```
