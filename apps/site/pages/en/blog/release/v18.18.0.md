---
date: '2023-09-18T21:46:26.210Z'
category: release
title: Node v18.18.0 (LTS)
layout: blog-post
author: <PERSON><PERSON>
---

### Notable Changes

- \[[`7dc731d4bf`](https://github.com/nodejs/node/commit/7dc731d4bf)] - **build**: sync libuv header change (Jiawen Geng) [#48078](https://github.com/nodejs/node/pull/48078)
- \[[`490fc004b0`](https://github.com/nodejs/node/commit/490fc004b0)] - **crypto**: update root certificates to NSS 3.93 (Node.js GitHub Bot) [#49341](https://github.com/nodejs/node/pull/49341)
- \[[`dd8cd97d4d`](https://github.com/nodejs/node/commit/dd8cd97d4d)] - **crypto**: update root certificates to NSS 3.90 (Node.js GitHub Bot) [#48416](https://github.com/nodejs/node/pull/48416)
- \[[`ea23870bec`](https://github.com/nodejs/node/commit/ea23870bec)] - **deps**: add missing thread-common.c in uv.gyp (Santiago Gimeno) [#48078](https://github.com/nodejs/node/pull/48078)
- \[[`88855e0b1b`](https://github.com/nodejs/node/commit/88855e0b1b)] - **deps**: upgrade to libuv 1.46.0 (Santiago Gimeno) [#48078](https://github.com/nodejs/node/pull/48078)
- \[[`fb2b80fca0`](https://github.com/nodejs/node/commit/fb2b80fca0)] - **deps**: upgrade to libuv 1.45.0 (Santiago Gimeno) [#48078](https://github.com/nodejs/node/pull/48078)
- \[[`249879e46c`](https://github.com/nodejs/node/commit/249879e46c)] - **doc**: add atlowChemi to collaborators (atlowChemi) [#48757](https://github.com/nodejs/node/pull/48757)
- \[[`e8dc7bde6a`](https://github.com/nodejs/node/commit/e8dc7bde6a)] - **doc**: add vmoroz to collaborators (Vladimir Morozov) [#48527](https://github.com/nodejs/node/pull/48527)
- \[[`a30f2fbcc1`](https://github.com/nodejs/node/commit/a30f2fbcc1)] - **doc**: add kvakil to collaborators (Keyhan Vakil) [#48449](https://github.com/nodejs/node/pull/48449)
- \[[`c39b7c240e`](https://github.com/nodejs/node/commit/c39b7c240e)] - **(SEMVER-MINOR)** **esm**: add `--import` flag (Moshe Atlow) [#43942](https://github.com/nodejs/node/pull/43942)
- \[[`a68a67f54d`](https://github.com/nodejs/node/commit/a68a67f54d)] - **(SEMVER-MINOR)** **events**: allow safely adding listener to abortSignal (Chemi Atlow) [#48596](https://github.com/nodejs/node/pull/48596)
- \[[`3a8586bee2`](https://github.com/nodejs/node/commit/3a8586bee2)] - **fs, stream**: initial `Symbol.dispose` and `Symbol.asyncDispose` support (Moshe Atlow) [#48518](https://github.com/nodejs/node/pull/48518)
- \[[`863bdb785d`](https://github.com/nodejs/node/commit/863bdb785d)] - **net**: add autoSelectFamily global getter and setter (Paolo Insogna) [#45777](https://github.com/nodejs/node/pull/45777)
- \[[`c59ae86ba0`](https://github.com/nodejs/node/commit/c59ae86ba0)] - **(SEMVER-MINOR)** **url**: add value argument to has and delete methods (Sankalp Shubham) [#47885](https://github.com/nodejs/node/pull/47885)

### Commits

- \[[`d1f43317ea`](https://github.com/nodejs/node/commit/d1f43317ea)] - **benchmark**: add bar.R (Rafael Gonzaga) [#47729](https://github.com/nodejs/node/pull/47729)
- \[[`4f74be3c92`](https://github.com/nodejs/node/commit/4f74be3c92)] - **benchmark**: refactor crypto oneshot (Filip Skokan) [#48267](https://github.com/nodejs/node/pull/48267)
- \[[`fe9da9df0f`](https://github.com/nodejs/node/commit/fe9da9df0f)] - **benchmark**: add crypto.create\*Key (Filip Skokan) [#48284](https://github.com/nodejs/node/pull/48284)
- \[[`9cb18b3e9d`](https://github.com/nodejs/node/commit/9cb18b3e9d)] - **build**: do not pass target toolchain flags to host toolchain (Ivan Trubach) [#48597](https://github.com/nodejs/node/pull/48597)
- \[[`7dc731d4bf`](https://github.com/nodejs/node/commit/7dc731d4bf)] - **build**: sync libuv header change (Jiawen Geng) [#48078](https://github.com/nodejs/node/pull/48078)
- \[[`211a4f88a9`](https://github.com/nodejs/node/commit/211a4f88a9)] - **build**: update action to close stale PRs (Michael Dawson) [#48196](https://github.com/nodejs/node/pull/48196)
- \[[`cc33a1864b`](https://github.com/nodejs/node/commit/cc33a1864b)] - **child_process**: harden against prototype pollution (Livia Medeiros) [#48726](https://github.com/nodejs/node/pull/48726)
- \[[`b5df084e1e`](https://github.com/nodejs/node/commit/b5df084e1e)] - **child_process**: use addAbortListener (atlowChemi) [#48550](https://github.com/nodejs/node/pull/48550)
- \[[`611db8df1a`](https://github.com/nodejs/node/commit/611db8df1a)] - **child_process**: support `Symbol.dispose` (Moshe Atlow) [#48551](https://github.com/nodejs/node/pull/48551)
- \[[`490fc004b0`](https://github.com/nodejs/node/commit/490fc004b0)] - **crypto**: update root certificates to NSS 3.93 (Node.js GitHub Bot) [#49341](https://github.com/nodejs/node/pull/49341)
- \[[`dd8cd97d4d`](https://github.com/nodejs/node/commit/dd8cd97d4d)] - **crypto**: update root certificates to NSS 3.90 (Node.js GitHub Bot) [#48416](https://github.com/nodejs/node/pull/48416)
- \[[`b2bc839d4c`](https://github.com/nodejs/node/commit/b2bc839d4c)] - **crypto**: remove OPENSSL_FIPS guard for OpenSSL 3 (Richard Lau) [#48392](https://github.com/nodejs/node/pull/48392)
- \[[`c8da8c80b9`](https://github.com/nodejs/node/commit/c8da8c80b9)] - **deps**: update nghttp2 to 1.55.0 (Node.js GitHub Bot) [#48746](https://github.com/nodejs/node/pull/48746)
- \[[`7e04242dcb`](https://github.com/nodejs/node/commit/7e04242dcb)] - **deps**: update minimatch to 9.0.3 (Node.js GitHub Bot) [#48704](https://github.com/nodejs/node/pull/48704)
- \[[`ea23870bec`](https://github.com/nodejs/node/commit/ea23870bec)] - **deps**: add missing thread-common.c in uv.gyp (Santiago Gimeno) [#48078](https://github.com/nodejs/node/pull/48078)
- \[[`88855e0b1b`](https://github.com/nodejs/node/commit/88855e0b1b)] - **deps**: upgrade to libuv 1.46.0 (Santiago Gimeno) [#48078](https://github.com/nodejs/node/pull/48078)
- \[[`fb2b80fca0`](https://github.com/nodejs/node/commit/fb2b80fca0)] - **deps**: upgrade to libuv 1.45.0 (Santiago Gimeno) [#48078](https://github.com/nodejs/node/pull/48078)
- \[[`59fca4e09a`](https://github.com/nodejs/node/commit/59fca4e09a)] - **deps**: update acorn to 8.10.0 (Node.js GitHub Bot) [#48713](https://github.com/nodejs/node/pull/48713)
- \[[`bcb255d5a8`](https://github.com/nodejs/node/commit/bcb255d5a8)] - **deps**: V8: cherry-pick cb00db4dba6c (Keyhan Vakil) [#48671](https://github.com/nodejs/node/pull/48671)
- \[[`65a6c90fc6`](https://github.com/nodejs/node/commit/65a6c90fc6)] - **deps**: update acorn to 8.9.0 (Node.js GitHub Bot) [#48484](https://github.com/nodejs/node/pull/48484)
- \[[`6b6d5d91e9`](https://github.com/nodejs/node/commit/6b6d5d91e9)] - **deps**: update zlib to 1.2.13.1-motley-f81f385 (Node.js GitHub Bot) [#48541](https://github.com/nodejs/node/pull/48541)
- \[[`56249b0770`](https://github.com/nodejs/node/commit/56249b0770)] - **deps**: update googletest to ec4fed9 (Node.js GitHub Bot) [#48538](https://github.com/nodejs/node/pull/48538)
- \[[`8914a5204a`](https://github.com/nodejs/node/commit/8914a5204a)] - **deps**: update minimatch to 9.0.2 (Node.js GitHub Bot) [#48542](https://github.com/nodejs/node/pull/48542)
- \[[`1b960d9988`](https://github.com/nodejs/node/commit/1b960d9988)] - **deps**: update icu to 73.2 (Node.js GitHub Bot) [#48502](https://github.com/nodejs/node/pull/48502)
- \[[`f0e2e3c549`](https://github.com/nodejs/node/commit/f0e2e3c549)] - **deps**: update zlib to 1.2.13.1-motley-3ca9f16 (Node.js GitHub Bot) [#48413](https://github.com/nodejs/node/pull/48413)
- \[[`9cf8fe6b93`](https://github.com/nodejs/node/commit/9cf8fe6b93)] - **deps**: upgrade npm to 9.8.1 (npm team) [#48838](https://github.com/nodejs/node/pull/48838)
- \[[`d9ff473ff3`](https://github.com/nodejs/node/commit/d9ff473ff3)] - **deps**: upgrade npm to 9.8.0 (npm team) [#48665](https://github.com/nodejs/node/pull/48665)
- \[[`4a6177daad`](https://github.com/nodejs/node/commit/4a6177daad)] - **deps**: upgrade npm to 9.7.2 (npm team) [#48514](https://github.com/nodejs/node/pull/48514)
- \[[`104b58feb1`](https://github.com/nodejs/node/commit/104b58feb1)] - **deps**: update ada to 2.6.0 (Node.js GitHub Bot) [#48896](https://github.com/nodejs/node/pull/48896)
- \[[`7f7a125d78`](https://github.com/nodejs/node/commit/7f7a125d78)] - **deps**: update corepack to 0.19.0 (Node.js GitHub Bot) [#48540](https://github.com/nodejs/node/pull/48540)
- \[[`5e1eb451d1`](https://github.com/nodejs/node/commit/5e1eb451d1)] - **deps**: update corepack to 0.18.1 (Node.js GitHub Bot) [#48483](https://github.com/nodejs/node/pull/48483)
- \[[`3be53358bc`](https://github.com/nodejs/node/commit/3be53358bc)] - **deps**: add loong64 config into openssl gypi (Shi Pujin) [#48043](https://github.com/nodejs/node/pull/48043)
- \[[`555982c59e`](https://github.com/nodejs/node/commit/555982c59e)] - **deps**: upgrade npm to 9.7.1 (npm team) [#48378](https://github.com/nodejs/node/pull/48378)
- \[[`3c03ec0832`](https://github.com/nodejs/node/commit/3c03ec0832)] - **deps**: update simdutf to 3.2.14 (Node.js GitHub Bot) [#48344](https://github.com/nodejs/node/pull/48344)
- \[[`a2964a4583`](https://github.com/nodejs/node/commit/a2964a4583)] - **deps**: update ada to 2.5.1 (Node.js GitHub Bot) [#48319](https://github.com/nodejs/node/pull/48319)
- \[[`38f6e0d8cd`](https://github.com/nodejs/node/commit/38f6e0d8cd)] - **deps**: update zlib to 982b036 (Node.js GitHub Bot) [#48327](https://github.com/nodejs/node/pull/48327)
- \[[`f4617a4f81`](https://github.com/nodejs/node/commit/f4617a4f81)] - **deps**: add loongarch64 into openssl Makefile and gen openssl-loongarch64 (Shi Pujin) [#46401](https://github.com/nodejs/node/pull/46401)
- \[[`573eb4be12`](https://github.com/nodejs/node/commit/573eb4be12)] - **dgram**: socket add `asyncDispose` (atlowChemi) [#48717](https://github.com/nodejs/node/pull/48717)
- \[[`f3c4300e00`](https://github.com/nodejs/node/commit/f3c4300e00)] - **dgram**: use addAbortListener (atlowChemi) [#48550](https://github.com/nodejs/node/pull/48550)
- \[[`d3041df738`](https://github.com/nodejs/node/commit/d3041df738)] - **doc**: expand on squashing and rebasing to land a PR (Chengzhong Wu) [#48751](https://github.com/nodejs/node/pull/48751)
- \[[`249879e46c`](https://github.com/nodejs/node/commit/249879e46c)] - **doc**: add atlowChemi to collaborators (atlowChemi) [#48757](https://github.com/nodejs/node/pull/48757)
- \[[`42ecd46d1f`](https://github.com/nodejs/node/commit/42ecd46d1f)] - **doc**: fix ambiguity in http.md and https.md (an5er) [#48692](https://github.com/nodejs/node/pull/48692)
- \[[`e78824e053`](https://github.com/nodejs/node/commit/e78824e053)] - **doc**: add release key for Ulises Gascon (Ulises Gascón) [#49196](https://github.com/nodejs/node/pull/49196)
- \[[`1aa798d69f`](https://github.com/nodejs/node/commit/1aa798d69f)] - **doc**: clarify transform.\_transform() callback argument logic (Rafael Sofi-zada) [#48680](https://github.com/nodejs/node/pull/48680)
- \[[`d723e870a2`](https://github.com/nodejs/node/commit/d723e870a2)] - **doc**: mention git node release prepare (Rafael Gonzaga) [#48644](https://github.com/nodejs/node/pull/48644)
- \[[`a9a1394388`](https://github.com/nodejs/node/commit/a9a1394388)] - **doc**: fix options order (Luigi Pinca) [#48617](https://github.com/nodejs/node/pull/48617)
- \[[`989ea6858f`](https://github.com/nodejs/node/commit/989ea6858f)] - **doc**: update security release stewards (Rafael Gonzaga) [#48569](https://github.com/nodejs/node/pull/48569)
- \[[`f436ac1803`](https://github.com/nodejs/node/commit/f436ac1803)] - **doc**: update return type for describe (Shrujal Shah) [#48572](https://github.com/nodejs/node/pull/48572)
- \[[`fbe89e6320`](https://github.com/nodejs/node/commit/fbe89e6320)] - **doc**: run license-builder (github-actions\[bot]) [#48552](https://github.com/nodejs/node/pull/48552)
- \[[`f18b287bc3`](https://github.com/nodejs/node/commit/f18b287bc3)] - **doc**: add description of autoAllocateChunkSize in ReadableStream (Debadree Chatterjee) [#48004](https://github.com/nodejs/node/pull/48004)
- \[[`e2f3ed1444`](https://github.com/nodejs/node/commit/e2f3ed1444)] - **doc**: fix `filename` type in `watch` result (Dmitry Semigradsky) [#48032](https://github.com/nodejs/node/pull/48032)
- \[[`1fe75dc2b0`](https://github.com/nodejs/node/commit/1fe75dc2b0)] - **doc**: unnest `mime` and `MIMEParams` from MIMEType constructor (Dmitry Semigradsky) [#47950](https://github.com/nodejs/node/pull/47950)
- \[[`e1339d58e8`](https://github.com/nodejs/node/commit/e1339d58e8)] - **doc**: update security-release-process.md (Rafael Gonzaga) [#48504](https://github.com/nodejs/node/pull/48504)
- \[[`e8dc7bde6a`](https://github.com/nodejs/node/commit/e8dc7bde6a)] - **doc**: add vmoroz to collaborators (Vladimir Morozov) [#48527](https://github.com/nodejs/node/pull/48527)
- \[[`f8ba672c7b`](https://github.com/nodejs/node/commit/f8ba672c7b)] - **doc**: link to Runtime Keys in export conditions (Jacob Hummer) [#48408](https://github.com/nodejs/node/pull/48408)
- \[[`0056cb93e9`](https://github.com/nodejs/node/commit/0056cb93e9)] - **doc**: update fs flags documentation (sinkhaha) [#48463](https://github.com/nodejs/node/pull/48463)
- \[[`3cf3fb9479`](https://github.com/nodejs/node/commit/3cf3fb9479)] - **doc**: revise `error.md` introduction (Antoine du Hamel) [#48423](https://github.com/nodejs/node/pull/48423)
- \[[`7575d8b90e`](https://github.com/nodejs/node/commit/7575d8b90e)] - **doc**: add preveen-stack to triagers (Preveen P) [#48387](https://github.com/nodejs/node/pull/48387)
- \[[`820aa550a4`](https://github.com/nodejs/node/commit/820aa550a4)] - **doc**: refine when file is undefined in test events (Moshe Atlow) [#48451](https://github.com/nodejs/node/pull/48451)
- \[[`a30f2fbcc1`](https://github.com/nodejs/node/commit/a30f2fbcc1)] - **doc**: add kvakil to collaborators (Keyhan Vakil) [#48449](https://github.com/nodejs/node/pull/48449)
- \[[`239b4ea66f`](https://github.com/nodejs/node/commit/239b4ea66f)] - **doc**: mark `--import` as experimental (Moshe Atlow) [#44067](https://github.com/nodejs/node/pull/44067)
- \[[`2a561aefe2`](https://github.com/nodejs/node/commit/2a561aefe2)] - **doc**: add additional info on TSFN dispatch (Michael Dawson) [#48367](https://github.com/nodejs/node/pull/48367)
- \[[`5cc6eee30d`](https://github.com/nodejs/node/commit/5cc6eee30d)] - **doc**: add link for news from security wg (Michael Dawson) [#48396](https://github.com/nodejs/node/pull/48396)
- \[[`ffece88452`](https://github.com/nodejs/node/commit/ffece88452)] - **doc**: fix typo in events.md (Darshan Sen) [#48436](https://github.com/nodejs/node/pull/48436)
- \[[`06513585dc`](https://github.com/nodejs/node/commit/06513585dc)] - **doc**: run license-builder (github-actions\[bot]) [#48336](https://github.com/nodejs/node/pull/48336)
- \[[`d9a800ee5c`](https://github.com/nodejs/node/commit/d9a800ee5c)] - **esm**: fix emit deprecation on legacy main resolve (Antoine du Hamel) [#48664](https://github.com/nodejs/node/pull/48664)
- \[[`c39b7c240e`](https://github.com/nodejs/node/commit/c39b7c240e)] - **(SEMVER-MINOR)** **esm**: add `--import` flag (Moshe Atlow) [#43942](https://github.com/nodejs/node/pull/43942)
- \[[`a00464ee06`](https://github.com/nodejs/node/commit/a00464ee06)] - **esm**: fix specifier resolution and symlinks (Zack Newsham) [#47674](https://github.com/nodejs/node/pull/47674)
- \[[`3b8ec348b0`](https://github.com/nodejs/node/commit/3b8ec348b0)] - **events**: fix bug listenerCount don't compare wrapped listener (yuzheng14) [#48592](https://github.com/nodejs/node/pull/48592)
- \[[`a68a67f54d`](https://github.com/nodejs/node/commit/a68a67f54d)] - **(SEMVER-MINOR)** **events**: allow safely adding listener to abortSignal (Chemi Atlow) [#48596](https://github.com/nodejs/node/pull/48596)
- \[[`5354af3dab`](https://github.com/nodejs/node/commit/5354af3dab)] - **fs**: call the callback with an error if writeSync fails (killa) [#47949](https://github.com/nodejs/node/pull/47949)
- \[[`c3a27d1d3d`](https://github.com/nodejs/node/commit/c3a27d1d3d)] - **fs**: remove unneeded return statement (Luigi Pinca) [#48526](https://github.com/nodejs/node/pull/48526)
- \[[`3a8586bee2`](https://github.com/nodejs/node/commit/3a8586bee2)] - **fs, stream**: initial `Symbol.dispose` and `Symbol.asyncDispose` support (Moshe Atlow) [#48518](https://github.com/nodejs/node/pull/48518)
- \[[`01746c71df`](https://github.com/nodejs/node/commit/01746c71df)] - **http**: null the joinDuplicateHeaders property on cleanup (Luigi Pinca) [#48608](https://github.com/nodejs/node/pull/48608)
- \[[`d47eb73a85`](https://github.com/nodejs/node/commit/d47eb73a85)] - **http**: remove useless ternary in test (geekreal) [#48481](https://github.com/nodejs/node/pull/48481)
- \[[`977e9a38b4`](https://github.com/nodejs/node/commit/977e9a38b4)] - **http**: fix for handling on boot timers headers and request (Franciszek Koltuniuk) [#48291](https://github.com/nodejs/node/pull/48291)
- \[[`be88f7cd22`](https://github.com/nodejs/node/commit/be88f7cd22)] - **http2**: use addAbortListener (atlowChemi) [#48550](https://github.com/nodejs/node/pull/48550)
- \[[`7c7230a85c`](https://github.com/nodejs/node/commit/7c7230a85c)] - **http2**: send RST code 8 on AbortController signal (Devraj Mehta) [#48573](https://github.com/nodejs/node/pull/48573)
- \[[`f74c2fc72a`](https://github.com/nodejs/node/commit/f74c2fc72a)] - **lib**: use addAbortListener (atlowChemi) [#48550](https://github.com/nodejs/node/pull/48550)
- \[[`db355d1f37`](https://github.com/nodejs/node/commit/db355d1f37)] - **lib**: add option to force handling stopped events (Chemi Atlow) [#48301](https://github.com/nodejs/node/pull/48301)
- \[[`5d682c55a5`](https://github.com/nodejs/node/commit/5d682c55a5)] - **lib**: reduce url getters on `makeRequireFunction` (Yagiz Nizipli) [#48492](https://github.com/nodejs/node/pull/48492)
- \[[`5260f53e55`](https://github.com/nodejs/node/commit/5260f53e55)] - **lib**: add support for inherited custom inspection methods (Antoine du Hamel) [#48306](https://github.com/nodejs/node/pull/48306)
- \[[`69aaf8b1d1`](https://github.com/nodejs/node/commit/69aaf8b1d1)] - **lib**: remove invalid parameter to toASCII (Yagiz Nizipli) [#48878](https://github.com/nodejs/node/pull/48878)
- \[[`51863b80e4`](https://github.com/nodejs/node/commit/51863b80e4)] - **meta**: bump actions/checkout from 3.5.2 to 3.5.3 (dependabot\[bot]) [#48625](https://github.com/nodejs/node/pull/48625)
- \[[`7ec370991d`](https://github.com/nodejs/node/commit/7ec370991d)] - **meta**: bump step-security/harden-runner from 2.4.0 to 2.4.1 (dependabot\[bot]) [#48626](https://github.com/nodejs/node/pull/48626)
- \[[`34b8e980d4`](https://github.com/nodejs/node/commit/34b8e980d4)] - **meta**: bump ossf/scorecard-action from 2.1.3 to 2.2.0 (dependabot\[bot]) [#48628](https://github.com/nodejs/node/pull/48628)
- \[[`dfed9a7da9`](https://github.com/nodejs/node/commit/dfed9a7da9)] - **meta**: bump github/codeql-action from 2.3.6 to 2.20.1 (dependabot\[bot]) [#48627](https://github.com/nodejs/node/pull/48627)
- \[[`071eaadc5a`](https://github.com/nodejs/node/commit/071eaadc5a)] - **module**: add SourceMap.findOrigin (Isaac Z. Schlueter) [#47790](https://github.com/nodejs/node/pull/47790)
- \[[`bf1525c549`](https://github.com/nodejs/node/commit/bf1525c549)] - **module**: reduce url invocations in esm/load.js (Yagiz Nizipli) [#48337](https://github.com/nodejs/node/pull/48337)
- \[[`f8921630a2`](https://github.com/nodejs/node/commit/f8921630a2)] - **net**: server add `asyncDispose` (atlowChemi) [#48717](https://github.com/nodejs/node/pull/48717)
- \[[`b5f53d9a0b`](https://github.com/nodejs/node/commit/b5f53d9a0b)] - **net**: fix family autoselection SSL connection handling (Paolo Insogna) [#48189](https://github.com/nodejs/node/pull/48189)
- \[[`267439fc34`](https://github.com/nodejs/node/commit/267439fc34)] - **net**: rework autoSelectFamily implementation (Paolo Insogna) [#46587](https://github.com/nodejs/node/pull/46587)
- \[[`d3637cdbbf`](https://github.com/nodejs/node/commit/d3637cdbbf)] - **net**: fix address iteration with autoSelectFamily (Fedor Indutny) [#48258](https://github.com/nodejs/node/pull/48258)
- \[[`e8289a83f1`](https://github.com/nodejs/node/commit/e8289a83f1)] - **net**: fix family autoselection timeout handling (Paolo Insogna) [#47860](https://github.com/nodejs/node/pull/47860)
- \[[`863bdb785d`](https://github.com/nodejs/node/commit/863bdb785d)] - **net**: add autoSelectFamily global getter and setter (Paolo Insogna) [#45777](https://github.com/nodejs/node/pull/45777)
- \[[`04dc090bfa`](https://github.com/nodejs/node/commit/04dc090bfa)] - **node-api**: provide napi_define_properties fast path (Gabriel Schulhof) [#48440](https://github.com/nodejs/node/pull/48440)
- \[[`feb6a54dc3`](https://github.com/nodejs/node/commit/feb6a54dc3)] - **node-api**: implement external strings (Gabriel Schulhof) [#48339](https://github.com/nodejs/node/pull/48339)
- \[[`121f74c463`](https://github.com/nodejs/node/commit/121f74c463)] - **perf_hooks**: convert maxSize to IDL value in setResourceTimingBufferSize (Chengzhong Wu) [#44902](https://github.com/nodejs/node/pull/44902)
- \[[`804d880589`](https://github.com/nodejs/node/commit/804d880589)] - **permission**: fix data types in PrintTree (Tobias Nießen) [#48770](https://github.com/nodejs/node/pull/48770)
- \[[`7aaecce9bf`](https://github.com/nodejs/node/commit/7aaecce9bf)] - **permission**: add debug log when inserting fs nodes (Rafael Gonzaga) [#48677](https://github.com/nodejs/node/pull/48677)
- \[[`cb51ef2905`](https://github.com/nodejs/node/commit/cb51ef2905)] - **readline**: use addAbortListener (atlowChemi) [#48550](https://github.com/nodejs/node/pull/48550)
- \[[`07065d0814`](https://github.com/nodejs/node/commit/07065d0814)] - **report**: disable js stack when no context is entered (Chengzhong Wu) [#48495](https://github.com/nodejs/node/pull/48495)
- \[[`572b82ffef`](https://github.com/nodejs/node/commit/572b82ffef)] - **src**: make BaseObject iteration order deterministic (Joyee Cheung) [#48702](https://github.com/nodejs/node/pull/48702)
- \[[`3f65598a41`](https://github.com/nodejs/node/commit/3f65598a41)] - **src**: remove kEagerCompile for CompileFunction (Keyhan Vakil) [#48671](https://github.com/nodejs/node/pull/48671)
- \[[`f43eacac9b`](https://github.com/nodejs/node/commit/f43eacac9b)] - **src**: deduplicate X509 getter implementations (Tobias Nießen) [#48563](https://github.com/nodejs/node/pull/48563)
- \[[`0c19621bdc`](https://github.com/nodejs/node/commit/0c19621bdc)] - **src**: fix uninitialized field access in AsyncHooks (Jan Olaf Krems) [#48566](https://github.com/nodejs/node/pull/48566)
- \[[`0c38184d62`](https://github.com/nodejs/node/commit/0c38184d62)] - **src**: fix Coverity issue regarding unnecessary copy (Yagiz Nizipli) [#48565](https://github.com/nodejs/node/pull/48565)
- \[[`0d73009ba3`](https://github.com/nodejs/node/commit/0d73009ba3)] - **src**: refactor `SplitString` in util (Yagiz Nizipli) [#48491](https://github.com/nodejs/node/pull/48491)
- \[[`6c72622df9`](https://github.com/nodejs/node/commit/6c72622df9)] - **src**: handle wasm out of bound in osx will raise SIGBUS correctly (Congcong Cai) [#46561](https://github.com/nodejs/node/pull/46561)
- \[[`e4261809b0`](https://github.com/nodejs/node/commit/e4261809b0)] - **src**: replace idna functions with ada::idna (Yagiz Nizipli) [#47735](https://github.com/nodejs/node/pull/47735)
- \[[`3dd82b1820`](https://github.com/nodejs/node/commit/3dd82b1820)] - **stream**: use addAbortListener (atlowChemi) [#48550](https://github.com/nodejs/node/pull/48550)
- \[[`786fbdb824`](https://github.com/nodejs/node/commit/786fbdb824)] - **stream**: fix premature pipeline end (Robert Nagy) [#48435](https://github.com/nodejs/node/pull/48435)
- \[[`c224e1b255`](https://github.com/nodejs/node/commit/c224e1b255)] - **stream**: fix deadlock when pipeing to full sink (Robert Nagy) [#48691](https://github.com/nodejs/node/pull/48691)
- \[[`2c75b9ece2`](https://github.com/nodejs/node/commit/2c75b9ece2)] - **test**: fix flaky test-string-decode.js on x86 (Stefan Stojanovic) [#48750](https://github.com/nodejs/node/pull/48750)
- \[[`279c4f64c1`](https://github.com/nodejs/node/commit/279c4f64c1)] - **test**: mark test-http-regr-gh-2928 as flaky (Joyee Cheung) [#49565](https://github.com/nodejs/node/pull/49565)
- \[[`01eacccd9a`](https://github.com/nodejs/node/commit/01eacccd9a)] - **test**: deflake test-net-throttle (Luigi Pinca) [#48599](https://github.com/nodejs/node/pull/48599)
- \[[`33886b271c`](https://github.com/nodejs/node/commit/33886b271c)] - **test**: move test-net-throttle to parallel (Luigi Pinca) [#48599](https://github.com/nodejs/node/pull/48599)
- \[[`a79112b5f4`](https://github.com/nodejs/node/commit/a79112b5f4)] - _**Revert**_ "**test**: remove test-crypto-keygen flaky designation" (Luigi Pinca) [#48652](https://github.com/nodejs/node/pull/48652)
- \[[`6ec57984db`](https://github.com/nodejs/node/commit/6ec57984db)] - **test**: add missing assertions to test-runner-cli (Moshe Atlow) [#48593](https://github.com/nodejs/node/pull/48593)
- \[[`dd1805e802`](https://github.com/nodejs/node/commit/dd1805e802)] - **test**: remove test-crypto-keygen flaky designation (Luigi Pinca) [#48575](https://github.com/nodejs/node/pull/48575)
- \[[`df9a9afc99`](https://github.com/nodejs/node/commit/df9a9afc99)] - **test**: remove test-timers-immediate-queue flaky designation (Luigi Pinca) [#48575](https://github.com/nodejs/node/pull/48575)
- \[[`3ae96ae380`](https://github.com/nodejs/node/commit/3ae96ae380)] - **test**: make IsolateData per-isolate in cctest (Joyee Cheung) [#48450](https://github.com/nodejs/node/pull/48450)
- \[[`f2ce8e0c06`](https://github.com/nodejs/node/commit/f2ce8e0c06)] - **test**: define NAPI_VERSION before including node_api.h (Chengzhong Wu) [#48376](https://github.com/nodejs/node/pull/48376)
- \[[`13ac0a5e26`](https://github.com/nodejs/node/commit/13ac0a5e26)] - **test**: remove unnecessary noop function args to `mustNotCall()` (Antoine du Hamel) [#48513](https://github.com/nodejs/node/pull/48513)
- \[[`8fdd4c55b3`](https://github.com/nodejs/node/commit/8fdd4c55b3)] - **test**: skip test-runner-watch-mode on IBMi (Moshe Atlow) [#48473](https://github.com/nodejs/node/pull/48473)
- \[[`9d90409241`](https://github.com/nodejs/node/commit/9d90409241)] - **test**: fix flaky test-watch-mode (Moshe Atlow) [#48147](https://github.com/nodejs/node/pull/48147)
- \[[`27a4bc7c32`](https://github.com/nodejs/node/commit/27a4bc7c32)] - **test**: add missing \<algorithm> include for std::find (Sam James) [#48380](https://github.com/nodejs/node/pull/48380)
- \[[`cb92c4b9fe`](https://github.com/nodejs/node/commit/cb92c4b9fe)] - **test**: update url web-platform tests (Yagiz Nizipli) [#48319](https://github.com/nodejs/node/pull/48319)
- \[[`f35c4d3190`](https://github.com/nodejs/node/commit/f35c4d3190)] - **test**: ignore the copied entry_point.c (Luigi Pinca) [#48297](https://github.com/nodejs/node/pull/48297)
- \[[`41d1e6888f`](https://github.com/nodejs/node/commit/41d1e6888f)] - **test**: refactor test-gc-http-client-timeout (Luigi Pinca) [#48292](https://github.com/nodejs/node/pull/48292)
- \[[`125bca621a`](https://github.com/nodejs/node/commit/125bca621a)] - **test**: update encoding web-platform tests (Yagiz Nizipli) [#48320](https://github.com/nodejs/node/pull/48320)
- \[[`e9ac111d02`](https://github.com/nodejs/node/commit/e9ac111d02)] - **test**: update FileAPI web-platform tests (Yagiz Nizipli) [#48322](https://github.com/nodejs/node/pull/48322)
- \[[`3da57d17f5`](https://github.com/nodejs/node/commit/3da57d17f5)] - **test**: update user-timing web-platform tests (Yagiz Nizipli) [#48321](https://github.com/nodejs/node/pull/48321)
- \[[`c728b8a29b`](https://github.com/nodejs/node/commit/c728b8a29b)] - **test**: fix `test-net-autoselectfamily` for kernel without IPv6 support (Livia Medeiros) [#45856](https://github.com/nodejs/node/pull/45856)
- \[[`6de7aa1d19`](https://github.com/nodejs/node/commit/6de7aa1d19)] - **test**: move `test-tls-autoselectfamily-servername` to `test/internet` (Antoine du Hamel) [#47029](https://github.com/nodejs/node/pull/47029)
- \[[`2de9868292`](https://github.com/nodejs/node/commit/2de9868292)] - **test**: validate host with commas on url.parse (Yagiz Nizipli) [#48878](https://github.com/nodejs/node/pull/48878)
- \[[`e7d2e8ef2a`](https://github.com/nodejs/node/commit/e7d2e8ef2a)] - **test**: delete test-net-bytes-per-incoming-chunk-overhead (Michaël Zasso) [#48811](https://github.com/nodejs/node/pull/48811)
- \[[`f5494fa1b0`](https://github.com/nodejs/node/commit/f5494fa1b0)] - **test_runner**: fixed `test` shorthands return type (Shocker) [#48555](https://github.com/nodejs/node/pull/48555)
- \[[`7051cafdfa`](https://github.com/nodejs/node/commit/7051cafdfa)] - **test_runner**: make `--test-name-pattern` recursive (Moshe Atlow) [#48382](https://github.com/nodejs/node/pull/48382)
- \[[`f302286442`](https://github.com/nodejs/node/commit/f302286442)] - **test_runner**: refactor coverage report output for readability (Damien Seguin) [#47791](https://github.com/nodejs/node/pull/47791)
- \[[`7822a541e5`](https://github.com/nodejs/node/commit/7822a541e5)] - **timers**: support Symbol.dispose (Moshe Atlow) [#48633](https://github.com/nodejs/node/pull/48633)
- \[[`3eeca52db1`](https://github.com/nodejs/node/commit/3eeca52db1)] - **tls**: fix bugs of double TLS (rogertyang) [#48969](https://github.com/nodejs/node/pull/48969)
- \[[`4826379516`](https://github.com/nodejs/node/commit/4826379516)] - **tools**: run fetch_deps.py with Python 3 (Richard Lau) [#48729](https://github.com/nodejs/node/pull/48729)
- \[[`e2688c8d79`](https://github.com/nodejs/node/commit/e2688c8d79)] - **tools**: update doc to unist-util-select\@5.0.0 unist-util-visit\@5.0.0 (Node.js GitHub Bot) [#48714](https://github.com/nodejs/node/pull/48714)
- \[[`7399481096`](https://github.com/nodejs/node/commit/7399481096)] - **tools**: update lint-md-dependencies to rollup\@3.26.2 (Node.js GitHub Bot) [#48705](https://github.com/nodejs/node/pull/48705)
- \[[`31c07153ce`](https://github.com/nodejs/node/commit/31c07153ce)] - **tools**: update eslint to 8.44.0 (Node.js GitHub Bot) [#48632](https://github.com/nodejs/node/pull/48632)
- \[[`4e53f51e24`](https://github.com/nodejs/node/commit/4e53f51e24)] - **tools**: update lint-md-dependencies to rollup\@3.26.0 (Node.js GitHub Bot) [#48631](https://github.com/nodejs/node/pull/48631)
- \[[`7d52950a96`](https://github.com/nodejs/node/commit/7d52950a96)] - **tools**: update lint-md-dependencies (Node.js GitHub Bot) [#48544](https://github.com/nodejs/node/pull/48544)
- \[[`e168eab3ee`](https://github.com/nodejs/node/commit/e168eab3ee)] - **tools**: update lint-md-dependencies (Node.js GitHub Bot) [#48486](https://github.com/nodejs/node/pull/48486)
- \[[`9711bc24f6`](https://github.com/nodejs/node/commit/9711bc24f6)] - **tools**: replace sed with perl (Luigi Pinca) [#48499](https://github.com/nodejs/node/pull/48499)
- \[[`9c1937c0a7`](https://github.com/nodejs/node/commit/9c1937c0a7)] - **tools**: update eslint to 8.43.0 (Node.js GitHub Bot) [#48487](https://github.com/nodejs/node/pull/48487)
- \[[`9449f05ab1`](https://github.com/nodejs/node/commit/9449f05ab1)] - **tools**: update doc to to-vfile\@8.0.0 (Node.js GitHub Bot) [#48485](https://github.com/nodejs/node/pull/48485)
- \[[`79dcd968b1`](https://github.com/nodejs/node/commit/79dcd968b1)] - **tools**: prepare tools/doc for to-vfile 8.0.0 (Rich Trott) [#48485](https://github.com/nodejs/node/pull/48485)
- \[[`538f388ac0`](https://github.com/nodejs/node/commit/538f388ac0)] - **tools**: update lint-md-dependencies (Node.js GitHub Bot) [#48417](https://github.com/nodejs/node/pull/48417)
- \[[`01bc10dcd5`](https://github.com/nodejs/node/commit/01bc10dcd5)] - **tools**: update create-or-update-pull-request-action (Richard Lau) [#48398](https://github.com/nodejs/node/pull/48398)
- \[[`590a072657`](https://github.com/nodejs/node/commit/590a072657)] - **tools**: update eslint-plugin-jsdoc (Richard Lau) [#48393](https://github.com/nodejs/node/pull/48393)
- \[[`6a5805491e`](https://github.com/nodejs/node/commit/6a5805491e)] - **tools**: update eslint to 8.42.0 (Node.js GitHub Bot) [#48328](https://github.com/nodejs/node/pull/48328)
- \[[`2eb13e3986`](https://github.com/nodejs/node/commit/2eb13e3986)] - **tools**: disable jsdoc/no-defaults rule (Luigi Pinca) [#48328](https://github.com/nodejs/node/pull/48328)
- \[[`3363cfa6c7`](https://github.com/nodejs/node/commit/3363cfa6c7)] - **typings**: remove unused primordials (Yagiz Nizipli) [#48509](https://github.com/nodejs/node/pull/48509)
- \[[`c59ae86ba0`](https://github.com/nodejs/node/commit/c59ae86ba0)] - **(SEMVER-MINOR)** **url**: add value argument to has and delete methods (Sankalp Shubham) [#47885](https://github.com/nodejs/node/pull/47885)
- \[[`f59c9636f4`](https://github.com/nodejs/node/commit/f59c9636f4)] - **url**: conform to origin getter spec changes (Yagiz Nizipli) [#48319](https://github.com/nodejs/node/pull/48319)
- \[[`0beb5ab93d`](https://github.com/nodejs/node/commit/0beb5ab93d)] - **url**: ensure getter access do not mutate observable symbols (Antoine du Hamel) [#48897](https://github.com/nodejs/node/pull/48897)
- \[[`0a022c496d`](https://github.com/nodejs/node/commit/0a022c496d)] - **util**: use `primordials.ArrayPrototypeIndexOf` instead of mutable method (DaisyDogs07) [#48586](https://github.com/nodejs/node/pull/48586)

Windows 32-bit Installer: https://nodejs.org/dist/v18.18.0/node-v18.18.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v18.18.0/node-v18.18.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v18.18.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v18.18.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v18.18.0/node-v18.18.0.pkg \
macOS Apple Silicon 64-bit Binary: https://nodejs.org/dist/v18.18.0/node-v18.18.0-darwin-arm64.tar.gz \
macOS Intel 64-bit Binary: https://nodejs.org/dist/v18.18.0/node-v18.18.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v18.18.0/node-v18.18.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v18.18.0/node-v18.18.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v18.18.0/node-v18.18.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v18.18.0/node-v18.18.0-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v18.18.0/node-v18.18.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v18.18.0/node-v18.18.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v18.18.0/node-v18.18.0.tar.gz \
Other release files: https://nodejs.org/dist/v18.18.0/ \
Documentation: https://nodejs.org/docs/v18.18.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

3838b541b2c61d6a3b7aeceab691a1c42899802f45022d82de1842c4662c00e6  node-v18.18.0-aix-ppc64.tar.gz
b21a2e8a3e75f81eb6124ea463d67cf4648a04dce9062e8d5a1bc44c99606a15  node-v18.18.0-darwin-arm64.tar.gz
8e58a93fdec75a4a56f76135314565d92b647c85073a0cfd3f49df6e59f3727e  node-v18.18.0-darwin-arm64.tar.xz
993e43ac4750609437a8523937a42663e5a7aa7973acac3f26b8d478aca1285d  node-v18.18.0-darwin-x64.tar.gz
17043ddcb7a08a51d8bffa7bfc130bd3e75d539f5b400b4111c7a1630ca37fde  node-v18.18.0-darwin-x64.tar.xz
dcdc0335426e3e24f8e1ff69d6ddd2172c3e4396e86dbf4b7a2c973d72de4785  node-v18.18.0-headers.tar.gz
ae2c8f96bdbcb2b7465467f2ddcb2cacc6be74f9a2a2fd87792c1930fdcf5447  node-v18.18.0-headers.tar.xz
54037e72a2676db610f0a9d66055a23241adf07f336cd0c9289a7b301eae9060  node-v18.18.0-linux-arm64.tar.gz
e2931643cc3ee37375ae5c6dc2028ff526948a227d9fd5d481316240de6e58a5  node-v18.18.0-linux-arm64.tar.xz
a84bf4ae2cd25a4d6aa5d562b6f7a62a8270221f94a5bbc9e4380494f0ee474d  node-v18.18.0-linux-armv7l.tar.gz
afcc9e01345243a19fbc05ce662b693568f31925679e8e0e7067d752296831f1  node-v18.18.0-linux-armv7l.tar.xz
f2ca5c856a4bb6a26e14d24ef69e249b078c8057ee92b6a268adbf6d2ef3b095  node-v18.18.0-linux-ppc64le.tar.gz
121204a93ac931146b3325a1748ae06d8b4d75db37566a792032e1c859bc8911  node-v18.18.0-linux-ppc64le.tar.xz
a61865c5ad92a9affab43279db6a2fefbeb6f3a57732cc72354fb8a9edd66364  node-v18.18.0-linux-s390x.tar.gz
8d3f21b77618d3c3d3ede230a65a89c9b1d5b942b79a6cea184d027b938de2a2  node-v18.18.0-linux-s390x.tar.xz
8aae62b6b3a5d659459c35c51e4373b950d11595a273db16c6162c712c5533a2  node-v18.18.0-linux-x64.tar.gz
3008408e9098f2462f7b1a0f6a48b8a46079beb1c92b6ec43b04713265c96978  node-v18.18.0-linux-x64.tar.xz
3bd071f842ebaf4b5510126babc6e396165834ddd887d0da410abddaf5ada07e  node-v18.18.0.pkg
5c5f4d7ee0b65f6e31788570d939a688ac7c9e47b007f14592e899b51cdca47c  node-v18.18.0.tar.gz
e4d4dbac3634d99f892f00db47da78f98493c339582e8a95fb2dd59f5cfe0f90  node-v18.18.0.tar.xz
029fbc17df16fd24386fd61064c9e83d4b58c3363e1567a9a94b9be5b07d30cf  node-v18.18.0-win-x64.7z
ae45bc05f4fcc02a17c724670534dc928a2ff4287a14b40f17afa8172601e790  node-v18.18.0-win-x64.zip
8a27b4fd7b8c0c0cf02049960e9892b5706a325c721b1ac0d8ad8ec9fb2d1a97  node-v18.18.0-win-x86.7z
d00986af4737d70045ff0d444c34b6195c82f62955cb11c827afcf6a358d46ac  node-v18.18.0-win-x86.zip
df0fb71f6530b70ed48b385cb13887ee4076ef40f736cb1eebe56328ef81d6fd  node-v18.18.0-x64.msi
d760e1c629ab870d13c07137d5d83856d5193d20aacbf19f7ad4c3cb1b3e7556  node-v18.18.0-x86.msi
0c3609dc301fb33d75ab933a527e7788cb3b2df15720326e55ac9c54c708466e  win-x64/node.exe
661c716215b8b12c061b209bff6228df68ce46e9aea806c06b1c5d4b3cfa1bd2  win-x64/node.lib
534c0caa1d5e0d5ad25050d82ce3c916ed53d0f47c4020aa42d48760984cd18a  win-x64/node_pdb.7z
db6634523e58073a4d9bf5c870f953a519a2fda58b9a8945b408ddaf275a2f55  win-x64/node_pdb.zip
177e0f1407880d85db8521e91479774efa7c29df25454dbda7630714bd42547c  win-x86/node.exe
a636d0881078c6a9b8fadb9a021641b04b043163affd2b15bd792fadaa01df92  win-x86/node.lib
b926dca24d376d2c0baa7f5a12c001ddc8c1a8c02ba6863afae315d351b583eb  win-x86/node_pdb.7z
3af40e0373405649e0ae2425c39d78de0289f372b580d88ca0765668a86a68a1  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEEI9StI21e7DMQ5spl7AUGb2S+AoFAmUIxOcACgkQl7AUGb2S
+AovDQ/9F6xu7CJOzyz4AZV68Ph8jTlEkLmHh8ZJJ4vuDN2E3w7vWbJRx78lSsK7
xt+GMtnf2Ep7JRzha9za4usoVRBmtuZWm0ZLHgXCoEleP4naW/o0W7C+rAPF5g3w
mcvmbvMIqmX4nZxib4y0/RlFM1f1ksTTVCvE2RtHCQG8Q58oHwNkXNM8fN9rZbLQ
iTvnNCw7ytIOtcMbdyXaO3pkfyyG8GxWAfeHxTqHZWjBZZeVxYbZVmswk+/VEWm2
Hkqccy73evEkHHowakQvqzuSe0CduNe4kEep2N1uPsCw7adSbN5iNJEZ/OeNCzp4
klPy1ibyrYw6CqXSVFrnD4itn3HRGWPPkZIJlMSS4LudGC3XLwZIvt7Eon3Y3S8j
XOVmOUSQbceMRCJEx1Ux9DJVW+jIVXGEnwVz5RfvAlBnzI94MWLSl02yi8nhNHTD
8LLb1nhMDq6P+gGYLckT8CDLMI8oN+aE/MvKxuJB2uguttVNxjoedqRC+ZEuv1el
FU3ReMAYe9fmyIs5zZTy7M6yjtUYMwZEP8Co33lHurz8477qzZnDs/yLPbsEGKbA
rIjBGHU1LKX/x65+RDK5ecdOhs2jejSzHO8EKTpi6vy3bbaJ9OIq632tUDenSloG
0IzfKyk5FJ4puhWhUMm+gh1EsuqGAeAgqgmbt3Kn1iVcZhP2TC0=
=KFh+
-----END PGP SIGNATURE-----

```
