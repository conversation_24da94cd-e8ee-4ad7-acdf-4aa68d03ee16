---
date: '2015-09-17T05:30:00.969Z'
category: release
title: Node v4.1.0 (Current)
layout: blog-post
author: The Node.js Project
---

## 2015-09-17, Version 4.1.0 (Current), @Fishrock123

### Notable changes

- **buffer**:
  - Buffers are now created in JavaScript, rather than C++. This increases the speed of buffer creation (<PERSON>) [#2866](https://github.com/nodejs/node/pull/2866).
  - `Buffer#slice()` now uses `Uint8Array#subarray()` internally, increasing `slice()` performance (<PERSON>) [#2777](https://github.com/nodejs/node/pull/2777).
- **fs**:
  - `fs.utimes()` now properly converts numeric strings, `NaN`, and `Infinity` (<PERSON><PERSON><PERSON>) [#2387](https://github.com/nodejs/node/pull/2387).
  - `fs.WriteStream` now implements `_writev`, allowing for super-fast bulk writes (<PERSON>) [#2167](https://github.com/nodejs/node/pull/2167).
- **http**: Fixed an issue with certain `write()` sizes causing errors when using `http.request()` (<PERSON><PERSON>dutny) [#2824](https://github.com/nodejs/node/pull/2824).
- **npm**: Upgrade to version 2.14.3, see https://github.com/npm/npm/releases/tag/v2.14.3 for more details (Kat Marchán) [#2822](https://github.com/nodejs/node/pull/2822).
- **src**: V8 cpu profiling no longer erroneously shows idle time (Oleksandr Chekhovskyi) [#2324](https://github.com/nodejs/node/pull/2324).
- **timers**: `#ref()` and `#unref()` now return the timer they belong to (Sam Roberts) [#2905](https://github.com/nodejs/node/pull/2905).
- **v8**: Lateral upgrade to ********** from 4.5.103.30, contains minor fixes (Ali Ijaz Sheikh) [#2870](https://github.com/nodejs/node/pull/2870).
  - This fixes a previously known bug where some computed object shorthand properties did not work correctly ([#2507](https://github.com/nodejs/node/issues/2507)).

### Known issues

See https://github.com/nodejs/node/labels/confirmed-bug for complete and current list of known issues.

- Some problems with unreferenced timers running during `beforeExit` are still to be resolved. See [#1264](https://github.com/nodejs/node/issues/1264).
- Surrogate pair in REPL can freeze terminal. [#690](https://github.com/nodejs/node/issues/690)
- Calling `dns.setServers()` while a DNS query is in progress can cause the process to crash on a failed assertion. [#894](https://github.com/nodejs/node/issues/894)
- `url.resolve` may transfer the auth portion of the url when resolving between two full hosts, see [#1435](https://github.com/nodejs/node/issues/1435).

### Commits

- [[`b1abe812cd`](https://github.com/nodejs/node/commit/b1abe812cd)] - Working on 4.0.1 (Rod Vagg)
- [[`f9f8378853`](https://github.com/nodejs/node/commit/f9f8378853)] - 2015-09-08, Version 4.0.0 (Current) Release (Rod Vagg)
- [[`9683e5df51`](https://github.com/nodejs/node/commit/9683e5df51)] - **bindings**: close after reading module struct (Fedor Indutny) [#2792](https://github.com/nodejs/node/pull/2792)
- [[`4b4cfa2d44`](https://github.com/nodejs/node/commit/4b4cfa2d44)] - **buffer**: always allocate typed arrays outside heap (Trevor Norris) [#2893](https://github.com/nodejs/node/pull/2893)
- [[`7df018a29b`](https://github.com/nodejs/node/commit/7df018a29b)] - **buffer**: construct Uint8Array in JS (Trevor Norris) [#2866](https://github.com/nodejs/node/pull/2866)
- [[`43397b204e`](https://github.com/nodejs/node/commit/43397b204e)] - **(SEMVER-MINOR)** **build**: Updates to enable AIX support (Michael Dawson) [#2364](https://github.com/nodejs/node/pull/2364)
- [[`e35b1fd610`](https://github.com/nodejs/node/commit/e35b1fd610)] - **build**: clean up the generated tap file (Sakthipriyan Vairamani) [#2837](https://github.com/nodejs/node/pull/2837)
- [[`96670ebe37`](https://github.com/nodejs/node/commit/96670ebe37)] - **deps**: backport 6d32be2 from v8's upstream (Michaël Zasso) [#2916](https://github.com/nodejs/node/pull/2916)
- [[`94972d5b13`](https://github.com/nodejs/node/commit/94972d5b13)] - **deps**: backport 0d01728 from v8's upstream (Fedor Indutny) [#2912](https://github.com/nodejs/node/pull/2912)
- [[`7ebd881c29`](https://github.com/nodejs/node/commit/7ebd881c29)] - **deps**: upgrade V8 to ********** (Ali Ijaz Sheikh) [#2870](https://github.com/nodejs/node/pull/2870)
- [[`ed47ab6e44`](https://github.com/nodejs/node/commit/ed47ab6e44)] - **deps**: upgraded to node-gyp@3.0.3 in npm (Kat Marchán) [#2822](https://github.com/nodejs/node/pull/2822)
- [[`f4641ae875`](https://github.com/nodejs/node/commit/f4641ae875)] - **deps**: upgrade to npm 2.14.3 (Kat Marchán) [#2822](https://github.com/nodejs/node/pull/2822)
- [[`8119693a3d`](https://github.com/nodejs/node/commit/8119693a3d)] - **deps**: update libuv to version 1.7.4 (Saúl Ibarra Corretgé) [#2817](https://github.com/nodejs/node/pull/2817)
- [[`6098504685`](https://github.com/nodejs/node/commit/6098504685)] - **deps**: cherry-pick 6da51b4 from v8's upstream (Fedor Indutny) [#2801](https://github.com/nodejs/node/pull/2801)
- [[`bf42cc8dba`](https://github.com/nodejs/node/commit/bf42cc8dba)] - **doc**: process exit event is not guaranteed to fire (Rich Trott) [#2861](https://github.com/nodejs/node/pull/2861)
- [[`bb0f869f67`](https://github.com/nodejs/node/commit/bb0f869f67)] - **doc**: remove incorrect reference to TCP in net docs (Sam Roberts) [#2903](https://github.com/nodejs/node/pull/2903)
- [[`302d59dce8`](https://github.com/nodejs/node/commit/302d59dce8)] - **doc**: correct buffer.slice arg syntax (Sam Roberts) [#2903](https://github.com/nodejs/node/pull/2903)
- [[`74db9637b7`](https://github.com/nodejs/node/commit/74db9637b7)] - **doc**: describe spawn option.detached (Sam Roberts) [#2903](https://github.com/nodejs/node/pull/2903)
- [[`a7bd897273`](https://github.com/nodejs/node/commit/a7bd897273)] - **doc**: rename from iojs(1) to node(1) in benchmarks (Dmitry Vasilyev) [#2884](https://github.com/nodejs/node/pull/2884)
- [[`cd643d7c37`](https://github.com/nodejs/node/commit/cd643d7c37)] - **doc**: add missing backtick in buffer.markdown (Sven Slootweg) [#2881](https://github.com/nodejs/node/pull/2881)
- [[`e8a206e802`](https://github.com/nodejs/node/commit/e8a206e802)] - **doc**: fix broken link in repl.markdown (Danny Nemer) [#2827](https://github.com/nodejs/node/pull/2827)
- [[`7ee36d61f7`](https://github.com/nodejs/node/commit/7ee36d61f7)] - **doc**: fix typos in README (Ionică Bizău) [#2852](https://github.com/nodejs/node/pull/2852)
- [[`4d1ae26196`](https://github.com/nodejs/node/commit/4d1ae26196)] - **doc**: add tunniclm as a collaborator (Mike Tunnicliffe) [#2826](https://github.com/nodejs/node/pull/2826)
- [[`2d77d03643`](https://github.com/nodejs/node/commit/2d77d03643)] - **doc**: fix two doc errors in stream and process (Jeremiah Senkpiel) [#2549](https://github.com/nodejs/node/pull/2549)
- [[`55ac24f721`](https://github.com/nodejs/node/commit/55ac24f721)] - **doc**: fixed io.js references in process.markdown (Tristian Flanagan) [#2846](https://github.com/nodejs/node/pull/2846)
- [[`cd1297fb57`](https://github.com/nodejs/node/commit/cd1297fb57)] - **doc**: use "Calls" over "Executes" for consistency (Minwoo Jung) [#2800](https://github.com/nodejs/node/pull/2800)
- [[`d664b95581`](https://github.com/nodejs/node/commit/d664b95581)] - **doc**: use US English for consistency (Anne-Gaelle Colom) [#2784](https://github.com/nodejs/node/pull/2784)
- [[`82ba1839fb`](https://github.com/nodejs/node/commit/82ba1839fb)] - **doc**: use 3rd person singular for consistency (Anne-Gaelle Colom) [#2765](https://github.com/nodejs/node/pull/2765)
- [[`432cce6e95`](https://github.com/nodejs/node/commit/432cce6e95)] - **doc**: describe process API for IPC (Sam Roberts) [#1978](https://github.com/nodejs/node/pull/1978)
- [[`1d75012b9d`](https://github.com/nodejs/node/commit/1d75012b9d)] - **doc**: fix comma splice in Assertion Testing doc (Rich Trott) [#2728](https://github.com/nodejs/node/pull/2728)
- [[`6108ea9bb4`](https://github.com/nodejs/node/commit/6108ea9bb4)] - **fs**: consider NaN/Infinity in toUnixTimestamp (Yazhong Liu) [#2387](https://github.com/nodejs/node/pull/2387)
- [[`2b6aa9415f`](https://github.com/nodejs/node/commit/2b6aa9415f)] - **(SEMVER-MINOR)** **fs**: implemented WriteStream#writev (Ron Korving) [#2167](https://github.com/nodejs/node/pull/2167)
- [[`431bf74c55`](https://github.com/nodejs/node/commit/431bf74c55)] - **http**: default Agent.getName to 'localhost' (Malcolm Ahoy) [#2825](https://github.com/nodejs/node/pull/2825)
- [[`ea15d71c16`](https://github.com/nodejs/node/commit/ea15d71c16)] - **http_server**: fix resume after socket close (Fedor Indutny) [#2824](https://github.com/nodejs/node/pull/2824)
- [[`8e5843405b`](https://github.com/nodejs/node/commit/8e5843405b)] - **src**: null env\_ field from constructor (Trevor Norris) [#2913](https://github.com/nodejs/node/pull/2913)
- [[`0a5f80a11f`](https://github.com/nodejs/node/commit/0a5f80a11f)] - **src**: use subarray() in Buffer#slice() for speedup (Karl Skomski) [#2777](https://github.com/nodejs/node/pull/2777)
- [[`57707e2490`](https://github.com/nodejs/node/commit/57707e2490)] - **src**: use ZCtxt as a source for v8::Isolates (Roman Klauke) [#2547](https://github.com/nodejs/node/pull/2547)
- [[`b0df2273ab`](https://github.com/nodejs/node/commit/b0df2273ab)] - **src**: fix v8::CpuProfiler idle sampling (Oleksandr Chekhovskyi) [#2324](https://github.com/nodejs/node/pull/2324)
- [[`eaa8e60b91`](https://github.com/nodejs/node/commit/eaa8e60b91)] - **streams**: refactor LazyTransform to internal/ (Brendan Ashworth) [#2566](https://github.com/nodejs/node/pull/2566)
- [[`648c003e14`](https://github.com/nodejs/node/commit/648c003e14)] - **test**: use tmp directory in chdir test (Sakthipriyan Vairamani) [#2589](https://github.com/nodejs/node/pull/2589)
- [[`079a2173d4`](https://github.com/nodejs/node/commit/079a2173d4)] - **test**: fix Buffer OOM error message (Trevor Norris) [#2915](https://github.com/nodejs/node/pull/2915)
- [[`52019a1b21`](https://github.com/nodejs/node/commit/52019a1b21)] - **test**: fix default value for additional param (Sakthipriyan Vairamani) [#2553](https://github.com/nodejs/node/pull/2553)
- [[`5df5d0423a`](https://github.com/nodejs/node/commit/5df5d0423a)] - **test**: remove disabled test (Rich Trott) [#2841](https://github.com/nodejs/node/pull/2841)
- [[`9e5f0995bd`](https://github.com/nodejs/node/commit/9e5f0995bd)] - **test**: split up internet dns tests (Rich Trott) [#2802](https://github.com/nodejs/node/pull/2802)
- [[`41f2dde51a`](https://github.com/nodejs/node/commit/41f2dde51a)] - **test**: increase dgram timeout for armv6 (Rich Trott) [#2808](https://github.com/nodejs/node/pull/2808)
- [[`6e2fe1c21a`](https://github.com/nodejs/node/commit/6e2fe1c21a)] - **test**: remove valid hostname check in test-dns.js (Rich Trott) [#2785](https://github.com/nodejs/node/pull/2785)
- [[`779e14f1a7`](https://github.com/nodejs/node/commit/779e14f1a7)] - **test**: expect error for test_lookup_ipv6_hint on FreeBSD (Rich Trott) [#2724](https://github.com/nodejs/node/pull/2724)
- [[`f931b9dd95`](https://github.com/nodejs/node/commit/f931b9dd95)] - **(SEMVER-MINOR)** **timer**: ref/unref return self (Sam Roberts) [#2905](https://github.com/nodejs/node/pull/2905)
- [[`59d03738cc`](https://github.com/nodejs/node/commit/59d03738cc)] - **tools**: enable arrow functions in .eslintrc (Sakthipriyan Vairamani) [#2840](https://github.com/nodejs/node/pull/2840)
- [[`69e7b875a2`](https://github.com/nodejs/node/commit/69e7b875a2)] - **tools**: open `test.tap` file in write-binary mode (Sakthipriyan Vairamani) [#2837](https://github.com/nodejs/node/pull/2837)
- [[`ff6d30d784`](https://github.com/nodejs/node/commit/ff6d30d784)] - **tools**: add missing tick processor polyfill (Matt Loring) [#2694](https://github.com/nodejs/node/pull/2694)
- [[`519caba021`](https://github.com/nodejs/node/commit/519caba021)] - **tools**: fix flakiness in test-tick-processor (Matt Loring) [#2694](https://github.com/nodejs/node/pull/2694)
- [[`ac004b8555`](https://github.com/nodejs/node/commit/ac004b8555)] - **tools**: remove hyphen in TAP result (Sakthipriyan Vairamani) [#2718](https://github.com/nodejs/node/pull/2718)
- [[`ba47511976`](https://github.com/nodejs/node/commit/ba47511976)] - **tsc**: adjust TSC membership for IBM+StrongLoop (James M Snell) [#2858](https://github.com/nodejs/node/pull/2858)
- [[`e035266805`](https://github.com/nodejs/node/commit/e035266805)] - **win,msi**: fix documentation shortcut url (Brian White) [#2781](https://github.com/nodejs/node/pull/2781)

Windows 32-bit Installer: https://nodejs.org/dist/v4.1.0/node-v4.1.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v4.1.0/node-v4.1.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v4.1.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v4.1.0/win-x64/node.exe \
Mac OS X 64-bit Installer: https://nodejs.org/dist/v4.1.0/node-v4.1.0.pkg \
Mac OS X 64-bit Binary: https://nodejs.org/dist/v4.1.0/node-v4.1.0-darwin-x64.tar.gz \
Linux 32-bit Binary: https://nodejs.org/dist/v4.1.0/node-v4.1.0-linux-x86.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v4.1.0/node-v4.1.0-linux-x64.tar.gz \
SmartOS 32-bit Binary: https://nodejs.org/dist/v4.1.0/node-v4.1.0-sunos-x86.tar.gz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v4.1.0/node-v4.1.0-sunos-x64.tar.gz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v4.1.0/node-v4.1.0-linux-armv6l.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v4.1.0/node-v4.1.0-linux-armv7.tar.gz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v4.1.0/node-v4.1.0-linux-arm64.tar.gz \
Source Code: https://nodejs.org/dist/v4.1.0/node-v4.1.0.tar.gz \
Other release files: https://nodejs.org/dist/v4.1.0/ \
Documentation: https://nodejs.org/docs/v4.1.0/api/

Shasums (GPG signing hash: SHA512, file hash: SHA256):

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA512

ff1c4b13a10c0789709cd73980a496b27e434207802989377ec4cb56302a1ebc  node-v4.1.0-darwin-x64.tar.gz
11d8d7367a7a7c91eb0ea7d8bdca7d24a9b944bf7a9c84389fbc3d3d35cb31a2  node-v4.1.0-darwin-x64.tar.xz
1ba579ead4cc6a70b1d74bbc3591ca9c9c46e4254ff6cebc9dedfaf98e1657aa  node-v4.1.0-headers.tar.gz
550a3023531f8387dcd7b632ceda6b613e0464a78aba0c4ee30dcb69026829dd  node-v4.1.0-headers.tar.xz
d27001f51d75c43cc1f444eab8aef0ced4ac4d162598be7eccf58790127e5368  node-v4.1.0-linux-arm64.tar.gz
8c0abe5f89ae9530751188cfef9640fa774c9812c15f7e168207d0adf51d8fff  node-v4.1.0-linux-arm64.tar.xz
3b728cdb93d155cf532a8a7f57f5a86f6f67a73e57a7abe657ff88166995e99e  node-v4.1.0-linux-armv7.tar.gz
3e1618a9c276fdcef45993ede68221667ef39c39356e8bc6f91c9ebf7310e8db  node-v4.1.0-linux-armv7.tar.xz
7c6055e08127143d9a8f779aa56f3fe42035fff8843c2652b0b2726204556382  node-v4.1.0-linux-x64.tar.gz
0f062984d1f7ce40cd902120b40b916b50df8041adb851373dce178f5ea959a3  node-v4.1.0-linux-x64.tar.xz
2ceaf5e66993da50e4514ddc5c270dc15ee712d00c29a2108655d9d259b3b9bb  node-v4.1.0-linux-x86.tar.gz
ac21e4c3628acfcd7df14cb4127691a4c55b9c7dbd5b1cba73c592f848e61cac  node-v4.1.0-linux-x86.tar.xz
9df8830c12bd28a5c1a0e1355cad9f50546e40ce681061d62f7a16e351272764  node-v4.1.0.pkg
b9b176f4e54422ab73458dcc36eec34149ffb298a9f5a5aa042c4b7e59860113  node-v4.1.0-sunos-x64.tar.gz
dfdead90e1c0e9760aeef0acf198fd1450b9c2b7dd32b42bae6f9ee6e0046566  node-v4.1.0-sunos-x64.tar.xz
ed6c5163b3cb2e452e8e9bf4e3857af9e65eccd7fce0959fb97dcf03547c726a  node-v4.1.0-sunos-x86.tar.gz
856e7478a237495ec1586054722b853a1b1f21dd29442fade9f8d2259967c214  node-v4.1.0-sunos-x86.tar.xz
453005f64ee529f7dcf1237eb27ee2fa2415c49f5c9e7463e8b71fba61c5b408  node-v4.1.0.tar.gz
2e153aaa72d73578e2ef68bd943e629904f00897769c2a2992ff4561d742772d  node-v4.1.0.tar.xz
ebf934575177def034130e2adea292567956be7ef4ed6c11fe645bc59a6ace06  node-v4.1.0-x64.msi
4c6fae187c23b15ec1689d25ac0000d2902d8d5f6c64710ed4ad9e2166f6580f  node-v4.1.0-x86.msi
95199ec14462edc982e01538d81ecd9af35ae3f9ef18c33be53102a5f93053e0  win-x64/node.exe
a00d40f053c7926f0999279c987cbd293a630853bb5565b0a07e61bece80fe34  win-x64/node.lib
62dca0c50648c3b87d9af1e469f290e82ab009f60892b164e1d1f6655c7310e3  win-x86/node.exe
6f84367eb539f230eef02714703d8a817b1c956ad302def07034f3cd310461df  win-x86/node.lib
-----BEGIN PGP SIGNATURE-----
Comment: GPGTools - https://gpgtools.org

iQIcBAEBCgAGBQJV+k2+AAoJEEX17r2BPa6OJjQP/1IAMo0X/9rSMwy8grbaj5Iy
E5sZTrTVZ7Xddzqu3c6zn/M9poK28aQUEPAkgXrICTXrmD6zM8In2wrmRMmcQkOn
iuS9rl7wsSLzfha2QnBbnl9XxEvSlf7csXsWuHKOk/MSPTqdowGbcWZhs2wsHpgX
Wv7yhaygvWZtboeZ3KTJsh48AcEru+Azm2jjU6/toBRCTTLWwXIYU8v1QdYhVVbM
A5nufi2tlpY4cBMZBOeEN/rFfCxoXvk8t0kR82qIGz28292p9anYt1I0MDMbK6Dq
N+ex02SQNcA+4+PMMBP5IyQXXIvi+jBzbDIxAHI/B3b+k5ZQXtV44sB+kiPS1H9Z
KHaC7ccom9Oo9lvU9KElTRJGD9JWdLvAENvsJO9CUFbR+NCcfk+amCcqK/UZxQeq
4s7cqnyM99oacX1fqqj+dMh8YxWB10lqz4ttdgkGX4yZtJQdp1OZ6F1NQuu839e7
TNUMa4MI0eZ/BjnocqlKnlCyMWQmWBPRIzfFXnRu+Q3Mq+3tpi6qKhE1Zda/E4Ow
+zUgRBDd94rE84rQNp9QLuEY0yhWWI2QQOOnBIRlfZPfDLcr12eSVm41yOYbL9pZ
wbF5+3DHMvjCC0IjcnFAja16joTps27eBsn2dNI72wyQod06W2qLZFS+Xt+AO6Tb
cFwYASzhWAmlr+3T6kFK
=VJ63
-----END PGP SIGNATURE-----

```
