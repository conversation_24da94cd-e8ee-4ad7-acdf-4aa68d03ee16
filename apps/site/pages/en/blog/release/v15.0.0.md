---
date: '2020-10-20T14:47:52.149Z'
category: release
title: Node v15.0.0 (Current)
layout: blog-post
author: <PERSON>
---

### Notable Changes

#### Deprecations and Removals

- [[`a11788736a`](https://github.com/nodejs/node/commit/a11788736a)] - **(SEMVER-MAJOR)** **build**: remove --build-v8-with-gn configure option (<PERSON>) [#27576](https://github.com/nodejs/node/pull/27576)
- [[`89428c7a2d`](https://github.com/nodejs/node/commit/89428c7a2d)] - **(SEMVER-MAJOR)** **build**: drop support for VS2017 (<PERSON><PERSON><PERSON>) [#33694](https://github.com/nodejs/node/pull/33694)
- [[`c25cf34ac1`](https://github.com/nodejs/node/commit/c25cf34ac1)] - **(SEMVER-<PERSON>JOR)** **doc**: move DEP0018 to End-of-Life (<PERSON>) [#35316](https://github.com/nodejs/node/pull/35316)
- [[`2002d90abd`](https://github.com/nodejs/node/commit/2002d90abd)] - **(SEMVER-MAJOR)** **fs**: deprecation warning on recursive rmdir (Ian Sutherland) [#35562](https://github.com/nodejs/node/pull/35562)
- [[`eee522ac29`](https://github.com/nodejs/node/commit/eee522ac29)] - **(SEMVER-MAJOR)** **lib**: add EventTarget-related browser globals (Anna Henningsen) [#35496](https://github.com/nodejs/node/pull/35496)
- [[`41796ebd30`](https://github.com/nodejs/node/commit/41796ebd30)] - **(SEMVER-MAJOR)** **net**: remove long deprecated server.connections property (James M Snell) [#33647](https://github.com/nodejs/node/pull/33647)
- [[`a416692e93`](https://github.com/nodejs/node/commit/a416692e93)] - **(SEMVER-MAJOR)** **repl**: remove deprecated repl.memory function (Ruben Bridgewater) [#33286](https://github.com/nodejs/node/pull/33286)
- [[`f217b2dfb0`](https://github.com/nodejs/node/commit/f217b2dfb0)] - **(SEMVER-MAJOR)** **repl**: remove deprecated repl.turnOffEditorMode() function (Ruben Bridgewater) [#33286](https://github.com/nodejs/node/pull/33286)
- [[`a1bcad8dc0`](https://github.com/nodejs/node/commit/a1bcad8dc0)] - **(SEMVER-MAJOR)** **repl**: remove deprecated repl.parseREPLKeyword() function (Ruben Bridgewater) [#33286](https://github.com/nodejs/node/pull/33286)
- [[`4ace010b53`](https://github.com/nodejs/node/commit/4ace010b53)] - **(SEMVER-MAJOR)** **repl**: remove deprecated bufferedCommand property (Ruben Bridgewater) [#33286](https://github.com/nodejs/node/pull/33286)
- [[`37524307fe`](https://github.com/nodejs/node/commit/37524307fe)] - **(SEMVER-MAJOR)** **repl**: remove deprecated .rli (Ruben Bridgewater) [#33286](https://github.com/nodejs/node/pull/33286)
- [[`a85ce885bd`](https://github.com/nodejs/node/commit/a85ce885bd)] - **(SEMVER-MAJOR)** **src**: remove deprecated node debug command (James M Snell) [#33648](https://github.com/nodejs/node/pull/33648)
- [[`a8904e8eee`](https://github.com/nodejs/node/commit/a8904e8eee)] - **(SEMVER-MAJOR)** **timers**: introduce timers/promises (James M Snell) [#33950](https://github.com/nodejs/node/pull/33950)
- [[`1211b9a72f`](https://github.com/nodejs/node/commit/1211b9a72f)] - **(SEMVER-MAJOR)** **util**: change default value of `maxStringLength` to 10000 (unknown) [#32744](https://github.com/nodejs/node/pull/32744)
- [[`ca8f3ef2e5`](https://github.com/nodejs/node/commit/ca8f3ef2e5)] - **(SEMVER-MAJOR)** **wasi**: drop --experimental-wasm-bigint requirement (Colin Ihrig) [#35415](https://github.com/nodejs/node/pull/35415)

#### npm 7 - [#35631](https://github.com/nodejs/node/pull/35631)

Node.js 15 comes with a new major release of npm, npm 7. npm 7 comes with many new features - including npm workspaces and a new package-lock.json format. npm 7 also includes yarn.lock file support. One of the big changes in npm 7 is that peer dependencies are now installed by default.

#### Throw On Unhandled Rejections - [#33021](https://github.com/nodejs/node/pull/33021)

As of Node.js 15, the default mode for `unhandledRejection` is changed to `throw` (from `warn`). In `throw` mode, if an `unhandledRejection` hook is not set, the `unhandledRejection` is raised as an uncaught exception. Users that have an `unhandledRejection` hook should see no change in behavior, and it’s still possible to switch modes using the `--unhandled-rejections=mode` process flag.

#### QUIC - [#32379](https://github.com/nodejs/node/pull/32379)

Node.js 15 comes with experimental support QUIC, which can be enabled by compiling Node.js with the `--experimental-quic` configuration flag. The Node.js QUIC implementation is exposed by the core `net` module.

#### V8 8.6 - [#35415](https://github.com/nodejs/node/pull/35415)

The V8 JavaScript engine has been updated to V8 8.6 (V8 8.4 is the latest available in Node.js 14). Along with performance tweaks and improvements the V8 update also brings the following language features:

- `Promise.any()` (from V8 8.5)
- `AggregateError` (from V8 8.5)
- `String.prototype.replaceAll()` (from V8 8.5)
- Logical assignment operators `&&=`, `||=`, and `??=` (from V8 8.5)

#### Other Notable Changes

- [[`50228cf6ff`](https://github.com/nodejs/node/commit/50228cf6ff)] - **(SEMVER-MAJOR)** **assert**: add `assert/strict` alias module (ExE Boss) [#34001](https://github.com/nodejs/node/pull/34001)
- [[`039cd00a9a`](https://github.com/nodejs/node/commit/039cd00a9a)] - **(SEMVER-MAJOR)** **dns**: add dns/promises alias (shisama) [#32953](https://github.com/nodejs/node/pull/32953)
- [[`54b36e401d`](https://github.com/nodejs/node/commit/54b36e401d)] - **(SEMVER-MAJOR)** **fs**: reimplement read and write streams using stream.construct (Robert Nagy) [#29656](https://github.com/nodejs/node/pull/29656)
- [[`f5c0e282cc`](https://github.com/nodejs/node/commit/f5c0e282cc)] - **(SEMVER-MAJOR)** **http2**: allow Host in HTTP/2 requests (Alba Mendez) [#34664](https://github.com/nodejs/node/pull/34664)
- [[`eee522ac29`](https://github.com/nodejs/node/commit/eee522ac29)] - **(SEMVER-MAJOR)** **lib**: add EventTarget-related browser globals (Anna Henningsen) [#35496](https://github.com/nodejs/node/pull/35496)
- [[`a8b26d72c5`](https://github.com/nodejs/node/commit/a8b26d72c5)] - **(SEMVER-MAJOR)** **lib**: unflag AbortController (James M Snell) [#33527](https://github.com/nodejs/node/pull/33527)
- [[`74ca960aac`](https://github.com/nodejs/node/commit/74ca960aac)] - **(SEMVER-MAJOR)** **lib**: initial experimental AbortController implementation (James M Snell) [#33527](https://github.com/nodejs/node/pull/33527)
- [[`efefdd668d`](https://github.com/nodejs/node/commit/efefdd668d)] - **(SEMVER-MAJOR)** **net**: autoDestroy Socket (Robert Nagy) [#31806](https://github.com/nodejs/node/pull/31806)
- [[`0fb91acedf`](https://github.com/nodejs/node/commit/0fb91acedf)] - **(SEMVER-MAJOR)** **src**: disallow JS execution inside FreeEnvironment (Anna Henningsen) [#33874](https://github.com/nodejs/node/pull/33874)
- [[`21782277c2`](https://github.com/nodejs/node/commit/21782277c2)] - **(SEMVER-MAJOR)** **src**: use node:moduleName as builtin module filename (Michaël Zasso) [#35498](https://github.com/nodejs/node/pull/35498)
- [[`fb8cc72e73`](https://github.com/nodejs/node/commit/fb8cc72e73)] - **(SEMVER-MAJOR)** **stream**: construct (Robert Nagy) [#29656](https://github.com/nodejs/node/pull/29656)
- [[`705d888387`](https://github.com/nodejs/node/commit/705d888387)] - **(SEMVER-MAJOR)** **worker**: make MessageEvent class more Web-compatible (Anna Henningsen) [#35496](https://github.com/nodejs/node/pull/35496)

### Semver-Major Commits

- [[`50228cf6ff`](https://github.com/nodejs/node/commit/50228cf6ff)] - **(SEMVER-MAJOR)** **assert**: add `assert/strict` alias module (ExE Boss) [#34001](https://github.com/nodejs/node/pull/34001)
- [[`d701247165`](https://github.com/nodejs/node/commit/d701247165)] - **(SEMVER-MAJOR)** **build**: reset embedder string to "-node.0" (Michaël Zasso) [#35415](https://github.com/nodejs/node/pull/35415)
- [[`a11788736a`](https://github.com/nodejs/node/commit/a11788736a)] - **(SEMVER-MAJOR)** **build**: remove --build-v8-with-gn configure option (Yang Guo) [#27576](https://github.com/nodejs/node/pull/27576)
- [[`89428c7a2d`](https://github.com/nodejs/node/commit/89428c7a2d)] - **(SEMVER-MAJOR)** **build**: drop support for VS2017 (Michaël Zasso) [#33694](https://github.com/nodejs/node/pull/33694)
- [[`dae283d96f`](https://github.com/nodejs/node/commit/dae283d96f)] - **(SEMVER-MAJOR)** **crypto**: refactoring internals, add WebCrypto (James M Snell) [#35093](https://github.com/nodejs/node/pull/35093)
- [[`ba77dc8597`](https://github.com/nodejs/node/commit/ba77dc8597)] - **(SEMVER-MAJOR)** **crypto**: move node_crypto files to src/crypto (James M Snell) [#35093](https://github.com/nodejs/node/pull/35093)
- [[`9378070da0`](https://github.com/nodejs/node/commit/9378070da0)] - **(SEMVER-MAJOR)** **deps**: V8: cherry-pick d76abfed3512 (Michaël Zasso) [#35415](https://github.com/nodejs/node/pull/35415)
- [[`efee8341ad`](https://github.com/nodejs/node/commit/efee8341ad)] - **(SEMVER-MAJOR)** **deps**: V8: cherry-pick 717543bbf0ef (Michaël Zasso) [#35415](https://github.com/nodejs/node/pull/35415)
- [[`b006fa8730`](https://github.com/nodejs/node/commit/b006fa8730)] - **(SEMVER-MAJOR)** **deps**: V8: cherry-pick 6be2f6e26e8d (Michaël Zasso) [#35415](https://github.com/nodejs/node/pull/35415)
- [[`3c23af4cb7`](https://github.com/nodejs/node/commit/3c23af4cb7)] - **(SEMVER-MAJOR)** **deps**: fix V8 build issue with inline methods (Jiawen Geng) [#35415](https://github.com/nodejs/node/pull/35415)
- [[`b803b3f48b`](https://github.com/nodejs/node/commit/b803b3f48b)] - **(SEMVER-MAJOR)** **deps**: fix platform-embedded-file-writer-win for ARM64 (Michaël Zasso) [#35415](https://github.com/nodejs/node/pull/35415)
- [[`47cb9f14e8`](https://github.com/nodejs/node/commit/47cb9f14e8)] - **(SEMVER-MAJOR)** **deps**: update V8 postmortem metadata script (Colin Ihrig) [#35415](https://github.com/nodejs/node/pull/35415)
- [[`a1d639ba5d`](https://github.com/nodejs/node/commit/a1d639ba5d)] - **(SEMVER-MAJOR)** **deps**: update V8 to 8.6.395 (Michaël Zasso) [#35415](https://github.com/nodejs/node/pull/35415)
- [[`3ddcad55fb`](https://github.com/nodejs/node/commit/3ddcad55fb)] - **(SEMVER-MAJOR)** **deps**: upgrade npm to 7.0.0 (Myles Borins) [#35631](https://github.com/nodejs/node/pull/35631)
- [[`2e54524955`](https://github.com/nodejs/node/commit/2e54524955)] - **(SEMVER-MAJOR)** **deps**: update npm to 7.0.0-rc.3 (Myles Borins) [#35474](https://github.com/nodejs/node/pull/35474)
- [[`e983b1cece`](https://github.com/nodejs/node/commit/e983b1cece)] - **(SEMVER-MAJOR)** **deps**: V8: cherry-pick 0d6debcc5f08 (Gus Caplan) [#33600](https://github.com/nodejs/node/pull/33600)
- [[`039cd00a9a`](https://github.com/nodejs/node/commit/039cd00a9a)] - **(SEMVER-MAJOR)** **dns**: add dns/promises alias (shisama) [#32953](https://github.com/nodejs/node/pull/32953)
- [[`c25cf34ac1`](https://github.com/nodejs/node/commit/c25cf34ac1)] - **(SEMVER-MAJOR)** **doc**: move DEP0018 to End-of-Life (Rich Trott) [#35316](https://github.com/nodejs/node/pull/35316)
- [[`8bf37ee496`](https://github.com/nodejs/node/commit/8bf37ee496)] - **(SEMVER-MAJOR)** **doc**: update support macos version for 15.x (Ash Cripps) [#35022](https://github.com/nodejs/node/pull/35022)
- [[`2002d90abd`](https://github.com/nodejs/node/commit/2002d90abd)] - **(SEMVER-MAJOR)** **fs**: deprecation warning on recursive rmdir (Ian Sutherland) [#35562](https://github.com/nodejs/node/pull/35562)
- [[`54b36e401d`](https://github.com/nodejs/node/commit/54b36e401d)] - **(SEMVER-MAJOR)** **fs**: reimplement read and write streams using stream.construct (Robert Nagy) [#29656](https://github.com/nodejs/node/pull/29656)
- [[`32b641e528`](https://github.com/nodejs/node/commit/32b641e528)] - **(SEMVER-MAJOR)** **http**: fixed socket.setEncoding fatal error (iskore) [#33405](https://github.com/nodejs/node/pull/33405)
- [[`8a6fab02ad`](https://github.com/nodejs/node/commit/8a6fab02ad)] - **(SEMVER-MAJOR)** **http**: emit 'error' on aborted server request (Robert Nagy) [#33172](https://github.com/nodejs/node/pull/33172)
- [[`d005f490a8`](https://github.com/nodejs/node/commit/d005f490a8)] - **(SEMVER-MAJOR)** **http**: cleanup end argument handling (Robert Nagy) [#31818](https://github.com/nodejs/node/pull/31818)
- [[`f5c0e282cc`](https://github.com/nodejs/node/commit/f5c0e282cc)] - **(SEMVER-MAJOR)** **http2**: allow Host in HTTP/2 requests (Alba Mendez) [#34664](https://github.com/nodejs/node/pull/34664)
- [[`1e4187fcf4`](https://github.com/nodejs/node/commit/1e4187fcf4)] - **(SEMVER-MAJOR)** **http2**: add `invalidheaders` test (Pranshu Srivastava) [#33161](https://github.com/nodejs/node/pull/33161)
- [[`d79c330186`](https://github.com/nodejs/node/commit/d79c330186)] - **(SEMVER-MAJOR)** **http2**: refactor state code validation for the http2Stream class (rickyes) [#33535](https://github.com/nodejs/node/pull/33535)
- [[`df31f71f1e`](https://github.com/nodejs/node/commit/df31f71f1e)] - **(SEMVER-MAJOR)** **http2**: header field valid checks (Pranshu Srivastava) [#33193](https://github.com/nodejs/node/pull/33193)
- [[`1428db8a1f`](https://github.com/nodejs/node/commit/1428db8a1f)] - **(SEMVER-MAJOR)** **lib**: refactor Socket.\_getpeername and Socket.\_getsockname (himself65) [#32969](https://github.com/nodejs/node/pull/32969)
- [[`eee522ac29`](https://github.com/nodejs/node/commit/eee522ac29)] - **(SEMVER-MAJOR)** **lib**: add EventTarget-related browser globals (Anna Henningsen) [#35496](https://github.com/nodejs/node/pull/35496)
- [[`c66e6471e7`](https://github.com/nodejs/node/commit/c66e6471e7)] - **(SEMVER-MAJOR)** **lib**: remove ERR_INVALID_OPT_VALUE and ERR_INVALID_OPT_VALUE_ENCODING (Denys Otrishko) [#34682](https://github.com/nodejs/node/pull/34682)
- [[`b546a2b469`](https://github.com/nodejs/node/commit/b546a2b469)] - **(SEMVER-MAJOR)** **lib**: handle one of args case in ERR_MISSING_ARGS (Denys Otrishko) [#34022](https://github.com/nodejs/node/pull/34022)
- [[`a86a295fd7`](https://github.com/nodejs/node/commit/a86a295fd7)] - **(SEMVER-MAJOR)** **lib**: remove NodeError from the prototype of errors with code (Michaël Zasso) [#33857](https://github.com/nodejs/node/pull/33857)
- [[`a8b26d72c5`](https://github.com/nodejs/node/commit/a8b26d72c5)] - **(SEMVER-MAJOR)** **lib**: unflag AbortController (James M Snell) [#33527](https://github.com/nodejs/node/pull/33527)
- [[`74ca960aac`](https://github.com/nodejs/node/commit/74ca960aac)] - **(SEMVER-MAJOR)** **lib**: initial experimental AbortController implementation (James M Snell) [#33527](https://github.com/nodejs/node/pull/33527)
- [[`78ca61e2cf`](https://github.com/nodejs/node/commit/78ca61e2cf)] - **(SEMVER-MAJOR)** **net**: check args in net.connect() and socket.connect() calls (Denys Otrishko) [#34022](https://github.com/nodejs/node/pull/34022)
- [[`41796ebd30`](https://github.com/nodejs/node/commit/41796ebd30)] - **(SEMVER-MAJOR)** **net**: remove long deprecated server.connections property (James M Snell) [#33647](https://github.com/nodejs/node/pull/33647)
- [[`efefdd668d`](https://github.com/nodejs/node/commit/efefdd668d)] - **(SEMVER-MAJOR)** **net**: autoDestroy Socket (Robert Nagy) [#31806](https://github.com/nodejs/node/pull/31806)
- [[`6cfba9f7f6`](https://github.com/nodejs/node/commit/6cfba9f7f6)] - **(SEMVER-MAJOR)** **process**: update v8 fast api calls usage (Maya Lekova) [#35415](https://github.com/nodejs/node/pull/35415)
- [[`3b10f7f933`](https://github.com/nodejs/node/commit/3b10f7f933)] - **(SEMVER-MAJOR)** **process**: change default --unhandled-rejections=throw (Dan Fabulich) [#33021](https://github.com/nodejs/node/pull/33021)
- [[`d8eef83757`](https://github.com/nodejs/node/commit/d8eef83757)] - **(SEMVER-MAJOR)** **process**: use v8 fast api calls for hrtime (Gus Caplan) [#33600](https://github.com/nodejs/node/pull/33600)
- [[`49745cdef0`](https://github.com/nodejs/node/commit/49745cdef0)] - **(SEMVER-MAJOR)** **process**: delay throwing an error using `throwDeprecation` (Ruben Bridgewater) [#32312](https://github.com/nodejs/node/pull/32312)
- [[`a416692e93`](https://github.com/nodejs/node/commit/a416692e93)] - **(SEMVER-MAJOR)** **repl**: remove deprecated repl.memory function (Ruben Bridgewater) [#33286](https://github.com/nodejs/node/pull/33286)
- [[`f217b2dfb0`](https://github.com/nodejs/node/commit/f217b2dfb0)] - **(SEMVER-MAJOR)** **repl**: remove deprecated repl.turnOffEditorMode() function (Ruben Bridgewater) [#33286](https://github.com/nodejs/node/pull/33286)
- [[`a1bcad8dc0`](https://github.com/nodejs/node/commit/a1bcad8dc0)] - **(SEMVER-MAJOR)** **repl**: remove deprecated repl.parseREPLKeyword() function (Ruben Bridgewater) [#33286](https://github.com/nodejs/node/pull/33286)
- [[`4ace010b53`](https://github.com/nodejs/node/commit/4ace010b53)] - **(SEMVER-MAJOR)** **repl**: remove deprecated bufferedCommand property (Ruben Bridgewater) [#33286](https://github.com/nodejs/node/pull/33286)
- [[`37524307fe`](https://github.com/nodejs/node/commit/37524307fe)] - **(SEMVER-MAJOR)** **repl**: remove deprecated .rli (Ruben Bridgewater) [#33286](https://github.com/nodejs/node/pull/33286)
- [[`b65e5aeaa7`](https://github.com/nodejs/node/commit/b65e5aeaa7)] - **(SEMVER-MAJOR)** **src**: implement NodePlatform::PostJob (Clemens Backes) [#35415](https://github.com/nodejs/node/pull/35415)
- [[`b1e8e0e604`](https://github.com/nodejs/node/commit/b1e8e0e604)] - **(SEMVER-MAJOR)** **src**: update NODE_MODULE_VERSION to 88 (Michaël Zasso) [#35415](https://github.com/nodejs/node/pull/35415)
- [[`eeb6b473fd`](https://github.com/nodejs/node/commit/eeb6b473fd)] - **(SEMVER-MAJOR)** **src**: error reporting on CPUUsage (Yash Ladha) [#34762](https://github.com/nodejs/node/pull/34762)
- [[`21782277c2`](https://github.com/nodejs/node/commit/21782277c2)] - **(SEMVER-MAJOR)** **src**: use node:moduleName as builtin module filename (Michaël Zasso) [#35498](https://github.com/nodejs/node/pull/35498)
- [[`05771279af`](https://github.com/nodejs/node/commit/05771279af)] - **(SEMVER-MAJOR)** **src**: enable wasm trap handler on windows (Gus Caplan) [#35033](https://github.com/nodejs/node/pull/35033)
- [[`b7cf823410`](https://github.com/nodejs/node/commit/b7cf823410)] - **(SEMVER-MAJOR)** **src**: update NODE_MODULE_VERSION to 86 (Michaël Zasso) [#33579](https://github.com/nodejs/node/pull/33579)
- [[`0fb91acedf`](https://github.com/nodejs/node/commit/0fb91acedf)] - **(SEMVER-MAJOR)** **src**: disallow JS execution inside FreeEnvironment (Anna Henningsen) [#33874](https://github.com/nodejs/node/pull/33874)
- [[`53fb2b6b41`](https://github.com/nodejs/node/commit/53fb2b6b41)] - **(SEMVER-MAJOR)** **src**: remove \_third_party_main support (Anna Henningsen) [#33971](https://github.com/nodejs/node/pull/33971)
- [[`a85ce885bd`](https://github.com/nodejs/node/commit/a85ce885bd)] - **(SEMVER-MAJOR)** **src**: remove deprecated node debug command (James M Snell) [#33648](https://github.com/nodejs/node/pull/33648)
- [[`ac3714637e`](https://github.com/nodejs/node/commit/ac3714637e)] - **(SEMVER-MAJOR)** **src**: remove unused CancelPendingDelayedTasks (Anna Henningsen) [#32859](https://github.com/nodejs/node/pull/32859)
- [[`a65218f5e8`](https://github.com/nodejs/node/commit/a65218f5e8)] - **(SEMVER-MAJOR)** **stream**: try to wait for flush to complete before 'finish' (Robert Nagy) [#34314](https://github.com/nodejs/node/pull/34314)
- [[`4e3f6f355b`](https://github.com/nodejs/node/commit/4e3f6f355b)] - **(SEMVER-MAJOR)** **stream**: cleanup and fix Readable.wrap (Robert Nagy) [#34204](https://github.com/nodejs/node/pull/34204)
- [[`527e2147af`](https://github.com/nodejs/node/commit/527e2147af)] - **(SEMVER-MAJOR)** **stream**: add promises version to utility functions (rickyes) [#33991](https://github.com/nodejs/node/pull/33991)
- [[`c7e55c6b72`](https://github.com/nodejs/node/commit/c7e55c6b72)] - **(SEMVER-MAJOR)** **stream**: fix writable.end callback behavior (Robert Nagy) [#34101](https://github.com/nodejs/node/pull/34101)
- [[`fb8cc72e73`](https://github.com/nodejs/node/commit/fb8cc72e73)] - **(SEMVER-MAJOR)** **stream**: construct (Robert Nagy) [#29656](https://github.com/nodejs/node/pull/29656)
- [[`4bc7025309`](https://github.com/nodejs/node/commit/4bc7025309)] - **(SEMVER-MAJOR)** **stream**: write should throw on unknown encoding (Robert Nagy) [#33075](https://github.com/nodejs/node/pull/33075)
- [[`ea87809bb6`](https://github.com/nodejs/node/commit/ea87809bb6)] - **(SEMVER-MAJOR)** **stream**: fix \_final and 'prefinish' timing (Robert Nagy) [#32780](https://github.com/nodejs/node/pull/32780)
- [[`0bd5595509`](https://github.com/nodejs/node/commit/0bd5595509)] - **(SEMVER-MAJOR)** **stream**: simplify Transform stream implementation (Robert Nagy) [#32763](https://github.com/nodejs/node/pull/32763)
- [[`8f86986985`](https://github.com/nodejs/node/commit/8f86986985)] - **(SEMVER-MAJOR)** **stream**: use callback to properly propagate error (Robert Nagy) [#29179](https://github.com/nodejs/node/pull/29179)
- [[`94dd7b9f94`](https://github.com/nodejs/node/commit/94dd7b9f94)] - **(SEMVER-MAJOR)** **test**: update tests after increasing typed array size to 4GB (Kim-Anh Tran) [#35415](https://github.com/nodejs/node/pull/35415)
- [[`d9e98df01b`](https://github.com/nodejs/node/commit/d9e98df01b)] - **(SEMVER-MAJOR)** **test**: fix tests for npm 7.0.0 (Myles Borins) [#35631](https://github.com/nodejs/node/pull/35631)
- [[`c87641aa97`](https://github.com/nodejs/node/commit/c87641aa97)] - **(SEMVER-MAJOR)** **test**: fix test suite to work with npm 7 (Myles Borins) [#35474](https://github.com/nodejs/node/pull/35474)
- [[`eb9d7a437e`](https://github.com/nodejs/node/commit/eb9d7a437e)] - **(SEMVER-MAJOR)** **test**: update WPT harness and tests (Michaël Zasso) [#33770](https://github.com/nodejs/node/pull/33770)
- [[`a8904e8eee`](https://github.com/nodejs/node/commit/a8904e8eee)] - **(SEMVER-MAJOR)** **timers**: introduce timers/promises (James M Snell) [#33950](https://github.com/nodejs/node/pull/33950)
- [[`c55f661551`](https://github.com/nodejs/node/commit/c55f661551)] - **(SEMVER-MAJOR)** **tools**: disable x86 safe exception handlers in V8 (Michaël Zasso) [#35415](https://github.com/nodejs/node/pull/35415)
- [[`80e8aec4a5`](https://github.com/nodejs/node/commit/80e8aec4a5)] - **(SEMVER-MAJOR)** **tools**: update V8 gypfiles for 8.6 (Ujjwal Sharma) [#35415](https://github.com/nodejs/node/pull/35415)
- [[`faeb9607c6`](https://github.com/nodejs/node/commit/faeb9607c6)] - **(SEMVER-MAJOR)** **tools**: update V8 gypfiles for 8.5 (Ujjwal Sharma) [#35415](https://github.com/nodejs/node/pull/35415)
- [[`bb62f4ad9e`](https://github.com/nodejs/node/commit/bb62f4ad9e)] - **(SEMVER-MAJOR)** **url**: file URL path normalization (Daijiro Wachi) [#35477](https://github.com/nodejs/node/pull/35477)
- [[`69ef4c2375`](https://github.com/nodejs/node/commit/69ef4c2375)] - **(SEMVER-MAJOR)** **url**: verify domain is not empty after "ToASCII" (Michaël Zasso) [#33770](https://github.com/nodejs/node/pull/33770)
- [[`4831278a16`](https://github.com/nodejs/node/commit/4831278a16)] - **(SEMVER-MAJOR)** **url**: remove U+0000 case in the fragment state (Michaël Zasso) [#33770](https://github.com/nodejs/node/pull/33770)
- [[`0d08d5ae7c`](https://github.com/nodejs/node/commit/0d08d5ae7c)] - **(SEMVER-MAJOR)** **url**: remove gopher from special schemes (Michaël Zasso) [#33325](https://github.com/nodejs/node/pull/33325)
- [[`9be51ee9a1`](https://github.com/nodejs/node/commit/9be51ee9a1)] - **(SEMVER-MAJOR)** **url**: forbid lt and gt in url host code point (Yash Ladha) [#33328](https://github.com/nodejs/node/pull/33328)
- [[`1211b9a72f`](https://github.com/nodejs/node/commit/1211b9a72f)] - **(SEMVER-MAJOR)** **util**: change default value of `maxStringLength` to 10000 (unknown) [#32744](https://github.com/nodejs/node/pull/32744)
- [[`ca8f3ef2e5`](https://github.com/nodejs/node/commit/ca8f3ef2e5)] - **(SEMVER-MAJOR)** **wasi**: drop --experimental-wasm-bigint requirement (Colin Ihrig) [#35415](https://github.com/nodejs/node/pull/35415)
- [[`abd8cdfc4e`](https://github.com/nodejs/node/commit/abd8cdfc4e)] - **(SEMVER-MAJOR)** **win, child_process**: sanitize env variables (Bartosz Sosnowski) [#35210](https://github.com/nodejs/node/pull/35210)
- [[`705d888387`](https://github.com/nodejs/node/commit/705d888387)] - **(SEMVER-MAJOR)** **worker**: make MessageEvent class more Web-compatible (Anna Henningsen) [#35496](https://github.com/nodejs/node/pull/35496)
- [[`7603c7e50c`](https://github.com/nodejs/node/commit/7603c7e50c)] - **(SEMVER-MAJOR)** **worker**: set trackUnmanagedFds to true by default (Anna Henningsen) [#34394](https://github.com/nodejs/node/pull/34394)
- [[`5ef5116311`](https://github.com/nodejs/node/commit/5ef5116311)] - **(SEMVER-MAJOR)** **worker**: rename error code to be more accurate (Anna Henningsen) [#33872](https://github.com/nodejs/node/pull/33872)

### Semver-Minor Commits

- [[`1d5fa88eb8`](https://github.com/nodejs/node/commit/1d5fa88eb8)] - **(SEMVER-MINOR)** **cli**: add --node-memory-debug option (Anna Henningsen) [#35537](https://github.com/nodejs/node/pull/35537)
- [[`095be6a01f`](https://github.com/nodejs/node/commit/095be6a01f)] - **(SEMVER-MINOR)** **crypto**: add getCipherInfo method (James M Snell) [#35368](https://github.com/nodejs/node/pull/35368)
- [[`df1023bb22`](https://github.com/nodejs/node/commit/df1023bb22)] - **(SEMVER-MINOR)** **events**: allow use of AbortController with on (James M Snell) [#34912](https://github.com/nodejs/node/pull/34912)
- [[`883fc779b6`](https://github.com/nodejs/node/commit/883fc779b6)] - **(SEMVER-MINOR)** **events**: allow use of AbortController with once (James M Snell) [#34911](https://github.com/nodejs/node/pull/34911)
- [[`e876c0c308`](https://github.com/nodejs/node/commit/e876c0c308)] - **(SEMVER-MINOR)** **http2**: add support for sensitive headers (Anna Henningsen) [#34145](https://github.com/nodejs/node/pull/34145)
- [[`6f34498148`](https://github.com/nodejs/node/commit/6f34498148)] - **(SEMVER-MINOR)** **net**: add support for resolving DNS CAA records (Danny Sonnenschein) [#35466](https://github.com/nodejs/node/pull/35466)
- [[`37a8179673`](https://github.com/nodejs/node/commit/37a8179673)] - **(SEMVER-MINOR)** **net**: make blocklist family case insensitive (James M Snell) [#34864](https://github.com/nodejs/node/pull/34864)
- [[`1f9b20b637`](https://github.com/nodejs/node/commit/1f9b20b637)] - **(SEMVER-MINOR)** **net**: introduce net.BlockList (James M Snell) [#34625](https://github.com/nodejs/node/pull/34625)
- [[`278d38f4cf`](https://github.com/nodejs/node/commit/278d38f4cf)] - **(SEMVER-MINOR)** **src**: add maybe versions of EmitExit and EmitBeforeExit (Anna Henningsen) [#35486](https://github.com/nodejs/node/pull/35486)
- [[`2310f679a1`](https://github.com/nodejs/node/commit/2310f679a1)] - **(SEMVER-MINOR)** **src**: move node_binding to modern THROW_ERR\* (James M Snell) [#35469](https://github.com/nodejs/node/pull/35469)
- [[`744a284ccc`](https://github.com/nodejs/node/commit/744a284ccc)] - **(SEMVER-MINOR)** **stream**: support async for stream impl functions (James M Snell) [#34416](https://github.com/nodejs/node/pull/34416)
- [[`bfbdc84738`](https://github.com/nodejs/node/commit/bfbdc84738)] - **(SEMVER-MINOR)** **timers**: allow promisified timeouts/immediates to be canceled (James M Snell) [#33833](https://github.com/nodejs/node/pull/33833)
- [[`a8971f87d3`](https://github.com/nodejs/node/commit/a8971f87d3)] - **(SEMVER-MINOR)** **url**: support non-special URLs (Daijiro Wachi) [#34925](https://github.com/nodejs/node/pull/34925)

### Semver-Patch Commits

- [[`d10c59fc60`](https://github.com/nodejs/node/commit/d10c59fc60)] - **benchmark,test**: remove output from readable-async-iterator benchmark (Rich Trott) [#34411](https://github.com/nodejs/node/pull/34411)
- [[`8a12e9994f`](https://github.com/nodejs/node/commit/8a12e9994f)] - **bootstrap**: use file URL instead of relative url (Daijiro Wachi) [#35622](https://github.com/nodejs/node/pull/35622)
- [[`f8bde7ce06`](https://github.com/nodejs/node/commit/f8bde7ce06)] - **bootstrap**: build fast APIs in pre-execution (Joyee Cheung) [#32984](https://github.com/nodejs/node/pull/32984)
- [[`b18651bcd2`](https://github.com/nodejs/node/commit/b18651bcd2)] - **build**: do not pass mode option to test-v8 command (Michaël Zasso) [#35705](https://github.com/nodejs/node/pull/35705)
- [[`bb2945ed6b`](https://github.com/nodejs/node/commit/bb2945ed6b)] - **build**: add GitHub Action for code coverage (Benjamin Coe) [#35653](https://github.com/nodejs/node/pull/35653)
- [[`cfbbeea4a1`](https://github.com/nodejs/node/commit/cfbbeea4a1)] - **build**: use GITHUB_ENV file to set env variables (Michaël Zasso) [#35638](https://github.com/nodejs/node/pull/35638)
- [[`8a93b371a3`](https://github.com/nodejs/node/commit/8a93b371a3)] - **build**: do not install jq in workflows (Michaël Zasso) [#35638](https://github.com/nodejs/node/pull/35638)
- [[`ccbd1d5efa`](https://github.com/nodejs/node/commit/ccbd1d5efa)] - **build**: add quic to github action (gengjiawen) [#34336](https://github.com/nodejs/node/pull/34336)
- [[`f4f191bbc2`](https://github.com/nodejs/node/commit/f4f191bbc2)] - **build**: define NODE_EXPERIMENTAL_QUIC in mkcodecache and node_mksnapshot (Joyee Cheung) [#34454](https://github.com/nodejs/node/pull/34454)
- [[`5b2c263ba8`](https://github.com/nodejs/node/commit/5b2c263ba8)] - **deps**: fix typo in zlib.gyp that break arm-fpu-neon build (lucasg) [#35659](https://github.com/nodejs/node/pull/35659)
- [[`5b9593f727`](https://github.com/nodejs/node/commit/5b9593f727)] - **deps**: upgrade npm to 7.0.2 (Myles Borins) [#35667](https://github.com/nodejs/node/pull/35667)
- [[`dabc6ddddc`](https://github.com/nodejs/node/commit/dabc6ddddc)] - **deps**: upgrade npm to 7.0.0-rc.4 (Myles Borins) [#35576](https://github.com/nodejs/node/pull/35576)
- [[`757bac6711`](https://github.com/nodejs/node/commit/757bac6711)] - **deps**: update nghttp3 (James M Snell) [#34752](https://github.com/nodejs/node/pull/34752)
- [[`c788be2e6e`](https://github.com/nodejs/node/commit/c788be2e6e)] - **deps**: update ngtcp2 (James M Snell) [#34752](https://github.com/nodejs/node/pull/34752)
- [[`7816e5f7b9`](https://github.com/nodejs/node/commit/7816e5f7b9)] - **deps**: fix indenting of sources in ngtcp2.gyp (James M Snell) [#34033](https://github.com/nodejs/node/pull/34033)
- [[`f5343d1b40`](https://github.com/nodejs/node/commit/f5343d1b40)] - **deps**: re-enable OPENSSL_NO_QUIC guards (James M Snell) [#34033](https://github.com/nodejs/node/pull/34033)
- [[`9de95f494e`](https://github.com/nodejs/node/commit/9de95f494e)] - **deps**: temporary fixup for ngtcp2 to build on windows (James M Snell) [#34033](https://github.com/nodejs/node/pull/34033)
- [[`ec7ad1d0ec`](https://github.com/nodejs/node/commit/ec7ad1d0ec)] - **deps**: cherry-pick akamai/openssl/commit/bf4b08ecfbb7a26ca4b0b9ecaee3b31d18d7bda9 (Tatsuhiro Tsujikawa) [#34033](https://github.com/nodejs/node/pull/34033)
- [[`c3d85b7637`](https://github.com/nodejs/node/commit/c3d85b7637)] - **deps**: cherry-pick akamai/openssl/commit/a5a08cb8050bb69120e833456e355f482e392456 (Benjamin Kaduk) [#34033](https://github.com/nodejs/node/pull/34033)
- [[`bad1a150ea`](https://github.com/nodejs/node/commit/bad1a150ea)] - **deps**: cherry-pick akamai/openssl/commit/d5a13ca6e29f3ff85c731770ab0ee2f2487bf8b3 (Benjamin Kaduk) [#34033](https://github.com/nodejs/node/pull/34033)
- [[`74cbfd3f36`](https://github.com/nodejs/node/commit/74cbfd3f36)] - **deps**: cherry-pick akamai/openssl/commit/a6282c566d88db11300c82abc3c84a4e2e9ea568 (Benjamin Kaduk) [#34033](https://github.com/nodejs/node/pull/34033)
- [[`8a9763a8ea`](https://github.com/nodejs/node/commit/8a9763a8ea)] - **deps**: update nghttp3 (James M Snell) [#34033](https://github.com/nodejs/node/pull/34033)
- [[`6b27d07779`](https://github.com/nodejs/node/commit/6b27d07779)] - **deps**: update ngtcp2 (James M Snell) [#34033](https://github.com/nodejs/node/pull/34033)
- [[`a041723774`](https://github.com/nodejs/node/commit/a041723774)] - **deps**: fix indentation for sources in nghttp3.gyp (Daniel Bevenius) [#33942](https://github.com/nodejs/node/pull/33942)
- [[`a0cbd676e7`](https://github.com/nodejs/node/commit/a0cbd676e7)] - **deps**: add defines to nghttp3/ngtcp2 gyp configs (Daniel Bevenius) [#33942](https://github.com/nodejs/node/pull/33942)
- [[`bccb514936`](https://github.com/nodejs/node/commit/bccb514936)] - **deps**: maintaining ngtcp2 and nghttp3 (James M Snell) [#32379](https://github.com/nodejs/node/pull/32379)
- [[`834fa8f23f`](https://github.com/nodejs/node/commit/834fa8f23f)] - **deps**: add ngtcp2 and nghttp3 (James M Snell) [#32379](https://github.com/nodejs/node/pull/32379)
- [[`f96b981528`](https://github.com/nodejs/node/commit/f96b981528)] - **deps**: details for updating openssl quic support (James M Snell) [#32379](https://github.com/nodejs/node/pull/32379)
- [[`98c8498552`](https://github.com/nodejs/node/commit/98c8498552)] - **deps**: update archs files for OpenSSL-1.1.0 (James M Snell) [#32379](https://github.com/nodejs/node/pull/32379)
- [[`2c549e505e`](https://github.com/nodejs/node/commit/2c549e505e)] - **deps**: add support for BoringSSL QUIC APIs (Todd Short) [#32379](https://github.com/nodejs/node/pull/32379)
- [[`1103b15af6`](https://github.com/nodejs/node/commit/1103b15af6)] - **doc**: fix YAML lint error on master (Rich Trott) [#35709](https://github.com/nodejs/node/pull/35709)
- [[`7798e59e98`](https://github.com/nodejs/node/commit/7798e59e98)] - **doc**: upgrade stability status of report API (Gireesh Punathil) [#35654](https://github.com/nodejs/node/pull/35654)
- [[`ce03a182cf`](https://github.com/nodejs/node/commit/ce03a182cf)] - **doc**: clarify experimental API elements in vm.md (Rich Trott) [#35594](https://github.com/nodejs/node/pull/35594)
- [[`89defff3b9`](https://github.com/nodejs/node/commit/89defff3b9)] - **doc**: correct order of metadata for deprecation (Rich Trott) [#35668](https://github.com/nodejs/node/pull/35668)
- [[`ee85eb9f8a`](https://github.com/nodejs/node/commit/ee85eb9f8a)] - **doc**: importModuleDynamically gets Script, not Module (Simen Bekkhus) [#35593](https://github.com/nodejs/node/pull/35593)
- [[`9e5a27a9d3`](https://github.com/nodejs/node/commit/9e5a27a9d3)] - **doc**: fix EventEmitter examples (Sourav Shaw) [#33513](https://github.com/nodejs/node/pull/33513)
- [[`2c2c87e291`](https://github.com/nodejs/node/commit/2c2c87e291)] - **doc**: fix stability indicator in webcrypto doc (Rich Trott) [#35672](https://github.com/nodejs/node/pull/35672)
- [[`f59d4e05a2`](https://github.com/nodejs/node/commit/f59d4e05a2)] - **doc**: add example code for process.getgroups() (Pooja D.P) [#35625](https://github.com/nodejs/node/pull/35625)
- [[`8a3808dc37`](https://github.com/nodejs/node/commit/8a3808dc37)] - **doc**: use kbd element in tty doc (Rich Trott) [#35613](https://github.com/nodejs/node/pull/35613)
- [[`4079bfd462`](https://github.com/nodejs/node/commit/4079bfd462)] - **doc**: Remove reference to io.js (Hussaina Begum Nandyala) [#35618](https://github.com/nodejs/node/pull/35618)
- [[`e6d5af3c95`](https://github.com/nodejs/node/commit/e6d5af3c95)] - **doc**: fix typos in quic.md (Luigi Pinca) [#35444](https://github.com/nodejs/node/pull/35444)
- [[`524123fbf0`](https://github.com/nodejs/node/commit/524123fbf0)] - **doc**: update releaser in v12.18.4 changelog (Beth Griggs) [#35217](https://github.com/nodejs/node/pull/35217)
- [[`ccdd1bd82a`](https://github.com/nodejs/node/commit/ccdd1bd82a)] - **doc**: fix incorrectly marked Buffer in quic.md (Rich Trott) [#35075](https://github.com/nodejs/node/pull/35075)
- [[`cc754f2985`](https://github.com/nodejs/node/commit/cc754f2985)] - **doc**: make AbortSignal text consistent in events.md (Rich Trott) [#35005](https://github.com/nodejs/node/pull/35005)
- [[`f9c362ff6c`](https://github.com/nodejs/node/commit/f9c362ff6c)] - **doc**: revise AbortSignal text and example using events.once() (Rich Trott) [#35005](https://github.com/nodejs/node/pull/35005)
- [[`7aeff6b8c8`](https://github.com/nodejs/node/commit/7aeff6b8c8)] - **doc**: claim ABI version for Electron v12 (Shelley Vohr) [#34816](https://github.com/nodejs/node/pull/34816)
- [[`7a1220a1d7`](https://github.com/nodejs/node/commit/7a1220a1d7)] - **doc**: fix headings in quic.md (Anna Henningsen) [#34717](https://github.com/nodejs/node/pull/34717)
- [[`d5c7aec3cb`](https://github.com/nodejs/node/commit/d5c7aec3cb)] - **doc**: use \_can\_ to describe actions in quic.md (Rich Trott) [#34613](https://github.com/nodejs/node/pull/34613)
- [[`319c275b26`](https://github.com/nodejs/node/commit/319c275b26)] - **doc**: use \_can\_ to describe actions in quic.md (Rich Trott) [#34613](https://github.com/nodejs/node/pull/34613)
- [[`2c30920886`](https://github.com/nodejs/node/commit/2c30920886)] - **doc**: use sentence-case in quic.md headers (Rich Trott) [#34453](https://github.com/nodejs/node/pull/34453)
- [[`8ada27510d`](https://github.com/nodejs/node/commit/8ada27510d)] - **doc**: add missing backticks in timers.md (vsemozhetbyt) [#34030](https://github.com/nodejs/node/pull/34030)
- [[`862d005e60`](https://github.com/nodejs/node/commit/862d005e60)] - **doc**: make globals Extends usage consistent (Colin Ihrig) [#33777](https://github.com/nodejs/node/pull/33777)
- [[`85dbd17bde`](https://github.com/nodejs/node/commit/85dbd17bde)] - **doc**: make perf_hooks Extends usage consistent (Colin Ihrig) [#33777](https://github.com/nodejs/node/pull/33777)
- [[`2e49010bc8`](https://github.com/nodejs/node/commit/2e49010bc8)] - **doc**: make events Extends usage consistent (Colin Ihrig) [#33777](https://github.com/nodejs/node/pull/33777)
- [[`680fb8fc62`](https://github.com/nodejs/node/commit/680fb8fc62)] - **doc**: fix deprecation "End-of-Life" capitalization (Colin Ihrig) [#33691](https://github.com/nodejs/node/pull/33691)
- [[`458677f5ef`](https://github.com/nodejs/node/commit/458677f5ef)] - **errors**: print original exception context (Benjamin Coe) [#33491](https://github.com/nodejs/node/pull/33491)
- [[`b1831fed3a`](https://github.com/nodejs/node/commit/b1831fed3a)] - **events**: simplify event target agnostic logic in on and once (Denys Otrishko) [#34997](https://github.com/nodejs/node/pull/34997)
- [[`7f25fe8b67`](https://github.com/nodejs/node/commit/7f25fe8b67)] - **fs**: remove unused assignment (Rich Trott) [#35642](https://github.com/nodejs/node/pull/35642)
- [[`2c4f30deea`](https://github.com/nodejs/node/commit/2c4f30deea)] - **fs**: fix when path is buffer on fs.symlinkSync (himself65) [#34540](https://github.com/nodejs/node/pull/34540)
- [[`db0e991d52`](https://github.com/nodejs/node/commit/db0e991d52)] - **fs**: remove custom Buffer pool for streams (Robert Nagy) [#33981](https://github.com/nodejs/node/pull/33981)
- [[`51a2df4439`](https://github.com/nodejs/node/commit/51a2df4439)] - **fs**: document why isPerformingIO is required (Robert Nagy) [#33982](https://github.com/nodejs/node/pull/33982)
- [[`999e7d7b44`](https://github.com/nodejs/node/commit/999e7d7b44)] - **gyp,build**: consistent shared library location (Rod Vagg) [#35635](https://github.com/nodejs/node/pull/35635)
- [[`30cc54275d`](https://github.com/nodejs/node/commit/30cc54275d)] - **http**: don't emit error after close (Robert Nagy) [#33654](https://github.com/nodejs/node/pull/33654)
- [[`ddff2b2b22`](https://github.com/nodejs/node/commit/ddff2b2b22)] - **lib**: honor setUncaughtExceptionCaptureCallback (Gireesh Punathil) [#35595](https://github.com/nodejs/node/pull/35595)
- [[`a8806535d9`](https://github.com/nodejs/node/commit/a8806535d9)] - **lib**: use Object static properties from primordials (Michaël Zasso) [#35380](https://github.com/nodejs/node/pull/35380)
- [[`11f1ad939f`](https://github.com/nodejs/node/commit/11f1ad939f)] - **module**: only try to enrich CJS syntax errors (Michaël Zasso) [#35691](https://github.com/nodejs/node/pull/35691)
- [[`aaf225a2a0`](https://github.com/nodejs/node/commit/aaf225a2a0)] - **module**: add setter for module.parent (Antoine du Hamel) [#35522](https://github.com/nodejs/node/pull/35522)
- [[`109a296e2a`](https://github.com/nodejs/node/commit/109a296e2a)] - **quic**: fix typo in code comment (Ikko Ashimine) [#35308](https://github.com/nodejs/node/pull/35308)
- [[`186230527b`](https://github.com/nodejs/node/commit/186230527b)] - **quic**: fix error message on invalid connection ID (Rich Trott) [#35026](https://github.com/nodejs/node/pull/35026)
- [[`e5116b304f`](https://github.com/nodejs/node/commit/e5116b304f)] - **quic**: remove unused function arguments (Rich Trott) [#35010](https://github.com/nodejs/node/pull/35010)
- [[`449f73e05f`](https://github.com/nodejs/node/commit/449f73e05f)] - **quic**: remove undefined variable (Rich Trott) [#35007](https://github.com/nodejs/node/pull/35007)
- [[`44e6a6af67`](https://github.com/nodejs/node/commit/44e6a6af67)] - **quic**: use qlog fin flag (James M Snell) [#34752](https://github.com/nodejs/node/pull/34752)
- [[`2a80737278`](https://github.com/nodejs/node/commit/2a80737278)] - **quic**: fixups after ngtcp2/nghttp3 update (James M Snell) [#34752](https://github.com/nodejs/node/pull/34752)
- [[`c855c3e8ca`](https://github.com/nodejs/node/commit/c855c3e8ca)] - **quic**: use net.BlockList for limiting access to a QuicSocket (James M Snell) [#34741](https://github.com/nodejs/node/pull/34741)
- [[`bfc35354c1`](https://github.com/nodejs/node/commit/bfc35354c1)] - **quic**: consolidate stats collecting in QuicSession (James M Snell) [#34741](https://github.com/nodejs/node/pull/34741)
- [[`94aa291348`](https://github.com/nodejs/node/commit/94aa291348)] - **quic**: clarify TODO statements (James M Snell) [#34741](https://github.com/nodejs/node/pull/34741)
- [[`19e712b9b2`](https://github.com/nodejs/node/commit/19e712b9b2)] - **quic**: resolve InitializeSecureContext TODO comment (James M Snell) [#34741](https://github.com/nodejs/node/pull/34741)
- [[`240592228b`](https://github.com/nodejs/node/commit/240592228b)] - **quic**: fixup session ticket app data todo comments (James M Snell) [#34741](https://github.com/nodejs/node/pull/34741)
- [[`c17eaa3f3f`](https://github.com/nodejs/node/commit/c17eaa3f3f)] - **quic**: add natRebinding argument to docs (James M Snell) [#34669](https://github.com/nodejs/node/pull/34669)
- [[`442968c92a`](https://github.com/nodejs/node/commit/442968c92a)] - **quic**: check setSocket natRebinding argument, extend test (James M Snell) [#34669](https://github.com/nodejs/node/pull/34669)
- [[`10d5047a4f`](https://github.com/nodejs/node/commit/10d5047a4f)] - **quic**: fixup set_socket, fix skipped test (James M Snell) [#34669](https://github.com/nodejs/node/pull/34669)
- [[`344c5e4e50`](https://github.com/nodejs/node/commit/344c5e4e50)] - **quic**: limit push check to http/3 (James M Snell) [#34655](https://github.com/nodejs/node/pull/34655)
- [[`34165f03aa`](https://github.com/nodejs/node/commit/34165f03aa)] - **quic**: resolve some minor TODOs (James M Snell) [#34655](https://github.com/nodejs/node/pull/34655)
- [[`1e6e5c3ef3`](https://github.com/nodejs/node/commit/1e6e5c3ef3)] - **quic**: resolve minor TODO in QuicSocket (James M Snell) [#34655](https://github.com/nodejs/node/pull/34655)
- [[`ba5c64bf45`](https://github.com/nodejs/node/commit/ba5c64bf45)] - **quic**: use AbortController with correct name/message (Anna Henningsen) [#34763](https://github.com/nodejs/node/pull/34763)
- [[`a7477704c4`](https://github.com/nodejs/node/commit/a7477704c4)] - **quic**: prefer modernize-make-unique (gengjiawen) [#34692](https://github.com/nodejs/node/pull/34692)
- [[`5b6cd6fa1a`](https://github.com/nodejs/node/commit/5b6cd6fa1a)] - **quic**: use the SocketAddressLRU to track validation status (James M Snell) [#34618](https://github.com/nodejs/node/pull/34618)
- [[`f75e69a94b`](https://github.com/nodejs/node/commit/f75e69a94b)] - **quic**: use SocketAddressLRU to track known SocketAddress info (James M Snell) [#34618](https://github.com/nodejs/node/pull/34618)
- [[`6b0b33cd4c`](https://github.com/nodejs/node/commit/6b0b33cd4c)] - **quic**: cleanup some outstanding todo items (James M Snell) [#34618](https://github.com/nodejs/node/pull/34618)
- [[`6e65f26b73`](https://github.com/nodejs/node/commit/6e65f26b73)] - **quic**: use QuicCallbackScope consistently for QuicSession (James M Snell) [#34541](https://github.com/nodejs/node/pull/34541)
- [[`d96083bad5`](https://github.com/nodejs/node/commit/d96083bad5)] - **quic**: introduce QuicCallbackScope (James M Snell) [#34541](https://github.com/nodejs/node/pull/34541)
- [[`4b0275ab87`](https://github.com/nodejs/node/commit/4b0275ab87)] - **quic**: refactor clientHello (James M Snell) [#34541](https://github.com/nodejs/node/pull/34541)
- [[`a97b5f9c6a`](https://github.com/nodejs/node/commit/a97b5f9c6a)] - **quic**: use OpenSSL built-in cert and hostname validation (James M Snell) [#34533](https://github.com/nodejs/node/pull/34533)
- [[`7a5fbafe96`](https://github.com/nodejs/node/commit/7a5fbafe96)] - **quic**: fix build for macOS (gengjiawen) [#34336](https://github.com/nodejs/node/pull/34336)
- [[`1f94b89309`](https://github.com/nodejs/node/commit/1f94b89309)] - **quic**: refactor ocsp to use async function rather than event/callback (James M Snell) [#34498](https://github.com/nodejs/node/pull/34498)
- [[`06664298fa`](https://github.com/nodejs/node/commit/06664298fa)] - **quic**: remove no-longer relevant TODO statements (James M Snell) [#34498](https://github.com/nodejs/node/pull/34498)
- [[`2fb92f4cc6`](https://github.com/nodejs/node/commit/2fb92f4cc6)] - **quic**: remove extraneous unused debug property (James M Snell) [#34498](https://github.com/nodejs/node/pull/34498)
- [[`b06fe33de1`](https://github.com/nodejs/node/commit/b06fe33de1)] - **quic**: use async \_construct for QuicStream (James M Snell) [#34351](https://github.com/nodejs/node/pull/34351)
- [[`8bd61d4c38`](https://github.com/nodejs/node/commit/8bd61d4c38)] - **quic**: documentation updates (James M Snell) [#34351](https://github.com/nodejs/node/pull/34351)
- [[`086c916997`](https://github.com/nodejs/node/commit/086c916997)] - **quic**: extensive refactoring of QuicStream lifecycle (James M Snell) [#34351](https://github.com/nodejs/node/pull/34351)
- [[`cf28f8a7dd`](https://github.com/nodejs/node/commit/cf28f8a7dd)] - **quic**: gitignore qlog files (James M Snell) [#34351](https://github.com/nodejs/node/pull/34351)
- [[`83bf0d7e8c`](https://github.com/nodejs/node/commit/83bf0d7e8c)] - **quic**: remove unneeded quicstream.aborted and fixup docs (James M Snell) [#34351](https://github.com/nodejs/node/pull/34351)
- [[`a65296db2c`](https://github.com/nodejs/node/commit/a65296db2c)] - **quic**: remove stream pending code (James M Snell) [#34351](https://github.com/nodejs/node/pull/34351)
- [[`da20287e1a`](https://github.com/nodejs/node/commit/da20287e1a)] - **quic**: simplify QuicStream construction logic (James M Snell) [#34351](https://github.com/nodejs/node/pull/34351)
- [[`6e30fe7a7f`](https://github.com/nodejs/node/commit/6e30fe7a7f)] - **quic**: convert openStream to Promise (James M Snell) [#34351](https://github.com/nodejs/node/pull/34351)
- [[`89453cfc08`](https://github.com/nodejs/node/commit/89453cfc08)] - **quic**: fixup quic.md (James M Snell) [#34283](https://github.com/nodejs/node/pull/34283)
- [[`4523d4a813`](https://github.com/nodejs/node/commit/4523d4a813)] - **quic**: fixup closing/draining period timing (James M Snell) [#34283](https://github.com/nodejs/node/pull/34283)
- [[`ed4882241c`](https://github.com/nodejs/node/commit/ed4882241c)] - **quic**: properly pass readable/writable constructor options (James M Snell) [#34283](https://github.com/nodejs/node/pull/34283)
- [[`57c1129508`](https://github.com/nodejs/node/commit/57c1129508)] - **quic**: implement QuicSession close as promise (James M Snell) [#34283](https://github.com/nodejs/node/pull/34283)
- [[`8e5c5b16ab`](https://github.com/nodejs/node/commit/8e5c5b16ab)] - **quic**: cleanup QuicClientSession constructor (James M Snell) [#34283](https://github.com/nodejs/node/pull/34283)
- [[`fe4e7e4598`](https://github.com/nodejs/node/commit/fe4e7e4598)] - **quic**: use promisified dns lookup (James M Snell) [#34283](https://github.com/nodejs/node/pull/34283)
- [[`346aeaf874`](https://github.com/nodejs/node/commit/346aeaf874)] - **quic**: eliminate "ready"/"not ready" states for QuicSession (James M Snell) [#34283](https://github.com/nodejs/node/pull/34283)
- [[`6665dda9f6`](https://github.com/nodejs/node/commit/6665dda9f6)] - **quic**: implement QuicSocket Promise API, part 2 (James M Snell) [#34283](https://github.com/nodejs/node/pull/34283)
- [[`79c0e892dd`](https://github.com/nodejs/node/commit/79c0e892dd)] - **quic**: implement QuicSocket Promise API, part 1 (James M Snell) [#34283](https://github.com/nodejs/node/pull/34283)
- [[`53b12f0c7b`](https://github.com/nodejs/node/commit/53b12f0c7b)] - **quic**: implement QuicEndpoint Promise API (James M Snell) [#34283](https://github.com/nodejs/node/pull/34283)
- [[`16b32eae3e`](https://github.com/nodejs/node/commit/16b32eae3e)] - **quic**: handle unhandled rejections on QuicSession (James M Snell) [#34283](https://github.com/nodejs/node/pull/34283)
- [[`e5d963e24d`](https://github.com/nodejs/node/commit/e5d963e24d)] - **quic**: fixup kEndpointClose (James M Snell) [#34283](https://github.com/nodejs/node/pull/34283)
- [[`9f552df5b4`](https://github.com/nodejs/node/commit/9f552df5b4)] - **quic**: fix endpointClose error handling, document (James M Snell) [#34283](https://github.com/nodejs/node/pull/34283)
- [[`b80108c033`](https://github.com/nodejs/node/commit/b80108c033)] - **quic**: restrict addEndpoint to before QuicSocket bind (James M Snell) [#34283](https://github.com/nodejs/node/pull/34283)
- [[`81c01bbdba`](https://github.com/nodejs/node/commit/81c01bbdba)] - **quic**: use a getter for stream options (James M Snell) [#34283](https://github.com/nodejs/node/pull/34283)
- [[`b8945ba2ab`](https://github.com/nodejs/node/commit/b8945ba2ab)] - **quic**: clarifying code comments (James M Snell) [#34283](https://github.com/nodejs/node/pull/34283)
- [[`429ab1dce6`](https://github.com/nodejs/node/commit/429ab1dce6)] - **quic**: minor reduction in code duplication (James M Snell) [#34283](https://github.com/nodejs/node/pull/34283)
- [[`aafdc2fcad`](https://github.com/nodejs/node/commit/aafdc2fcad)] - **quic**: replace ipv6Only option with `'udp6-only'` type (James M Snell) [#34283](https://github.com/nodejs/node/pull/34283)
- [[`fbc38ee134`](https://github.com/nodejs/node/commit/fbc38ee134)] - **quic**: clear clang warning (gengjiawen) [#34335](https://github.com/nodejs/node/pull/34335)
- [[`c176d5fac2`](https://github.com/nodejs/node/commit/c176d5fac2)] - **quic**: set destroyed at timestamps for duration calculation (James M Snell) [#34262](https://github.com/nodejs/node/pull/34262)
- [[`48a349efd9`](https://github.com/nodejs/node/commit/48a349efd9)] - **quic**: use Number instead of BigInt for more stats (James M Snell) [#34262](https://github.com/nodejs/node/pull/34262)
- [[`5e769b2eaf`](https://github.com/nodejs/node/commit/5e769b2eaf)] - **quic**: use less specific error codes (James M Snell) [#34262](https://github.com/nodejs/node/pull/34262)
- [[`26493c02a2`](https://github.com/nodejs/node/commit/26493c02a2)] - **quic**: remove no longer valid CHECK (James M Snell) [#34247](https://github.com/nodejs/node/pull/34247)
- [[`458d243f20`](https://github.com/nodejs/node/commit/458d243f20)] - **quic**: proper custom inspect for QuicStream (James M Snell) [#34247](https://github.com/nodejs/node/pull/34247)
- [[`0860b11655`](https://github.com/nodejs/node/commit/0860b11655)] - **quic**: proper custom inspect for QuicSession (James M Snell) [#34247](https://github.com/nodejs/node/pull/34247)
- [[`b047930d76`](https://github.com/nodejs/node/commit/b047930d76)] - **quic**: proper custom inspect for QuicSocket (James M Snell) [#34247](https://github.com/nodejs/node/pull/34247)
- [[`511f8c1138`](https://github.com/nodejs/node/commit/511f8c1138)] - **quic**: proper custom inspect for QuicEndpoint (James M Snell) [#34247](https://github.com/nodejs/node/pull/34247)
- [[`fe11f6bf7c`](https://github.com/nodejs/node/commit/fe11f6bf7c)] - **quic**: cleanup QuicSocketFlags, used shared state struct (James M Snell) [#34247](https://github.com/nodejs/node/pull/34247)
- [[`d08e99de24`](https://github.com/nodejs/node/commit/d08e99de24)] - **quic**: use getter/setter for stateless reset toggle (James M Snell) [#34247](https://github.com/nodejs/node/pull/34247)
- [[`f2753c7695`](https://github.com/nodejs/node/commit/f2753c7695)] - **quic**: unref timers again (Anna Henningsen) [#34247](https://github.com/nodejs/node/pull/34247)
- [[`71236097d0`](https://github.com/nodejs/node/commit/71236097d0)] - **quic**: use Number() instead of bigint for QuicSocket stats (James M Snell) [#34247](https://github.com/nodejs/node/pull/34247)
- [[`94372b124a`](https://github.com/nodejs/node/commit/94372b124a)] - **quic**: refactor/improve/document QuicSocket listening event (James M Snell) [#34247](https://github.com/nodejs/node/pull/34247)
- [[`afc9390ae5`](https://github.com/nodejs/node/commit/afc9390ae5)] - **quic**: refactor/improve QuicSocket ready event handling (James M Snell) [#34247](https://github.com/nodejs/node/pull/34247)
- [[`e3813261b8`](https://github.com/nodejs/node/commit/e3813261b8)] - **quic**: add tests confirming error handling for QuicSocket close event (James M Snell) [#34247](https://github.com/nodejs/node/pull/34247)
- [[`cc89aac5f7`](https://github.com/nodejs/node/commit/cc89aac5f7)] - **quic**: refactor/improve error handling for busy event (James M Snell) [#34247](https://github.com/nodejs/node/pull/34247)
- [[`edc71ef008`](https://github.com/nodejs/node/commit/edc71ef008)] - **quic**: handle errors thrown / rejections in the session event (James M Snell) [#34247](https://github.com/nodejs/node/pull/34247)
- [[`bcde849be9`](https://github.com/nodejs/node/commit/bcde849be9)] - **quic**: remove unnecessary bool conversion (James M Snell) [#34247](https://github.com/nodejs/node/pull/34247)
- [[`c535131627`](https://github.com/nodejs/node/commit/c535131627)] - **quic**: additional minor cleanups in node_quic_session.h (James M Snell) [#34247](https://github.com/nodejs/node/pull/34247)
- [[`0f97d6066a`](https://github.com/nodejs/node/commit/0f97d6066a)] - **quic**: use TimerWrap for idle and retransmit timers (James M Snell) [#34186](https://github.com/nodejs/node/pull/34186)
- [[`1b1e985478`](https://github.com/nodejs/node/commit/1b1e985478)] - **quic**: add missing memory tracker fields (James M Snell) [#34160](https://github.com/nodejs/node/pull/34160)
- [[`5a87e9b0a5`](https://github.com/nodejs/node/commit/5a87e9b0a5)] - **quic**: cleanup timers if they haven't been already (James M Snell) [#34160](https://github.com/nodejs/node/pull/34160)
- [[`3837d9cf1f`](https://github.com/nodejs/node/commit/3837d9cf1f)] - **quic**: fixup lint issues (James M Snell) [#34160](https://github.com/nodejs/node/pull/34160)
- [[`7b062ca015`](https://github.com/nodejs/node/commit/7b062ca015)] - **quic**: refactor qlog handling (James M Snell) [#34160](https://github.com/nodejs/node/pull/34160)
- [[`e4d369e96e`](https://github.com/nodejs/node/commit/e4d369e96e)] - **quic**: remove onSessionDestroy callback (James M Snell) [#34160](https://github.com/nodejs/node/pull/34160)
- [[`3acdd6aac7`](https://github.com/nodejs/node/commit/3acdd6aac7)] - **quic**: refactor QuicSession shared state to use AliasedStruct (James M Snell) [#34160](https://github.com/nodejs/node/pull/34160)
- [[`f9c2245fb5`](https://github.com/nodejs/node/commit/f9c2245fb5)] - **quic**: refactor QuicSession close/destroy flow (James M Snell) [#34160](https://github.com/nodejs/node/pull/34160)
- [[`f7510ca439`](https://github.com/nodejs/node/commit/f7510ca439)] - **quic**: additional cleanups on the c++ side (James M Snell) [#34160](https://github.com/nodejs/node/pull/34160)
- [[`b5bf5bb20f`](https://github.com/nodejs/node/commit/b5bf5bb20f)] - **quic**: refactor native object flags for better readability (James M Snell) [#34160](https://github.com/nodejs/node/pull/34160)
- [[`b1750a4d53`](https://github.com/nodejs/node/commit/b1750a4d53)] - **quic**: continued refactoring for quic_stream/quic_session (James M Snell) [#34160](https://github.com/nodejs/node/pull/34160)
- [[`31d6d9d0f7`](https://github.com/nodejs/node/commit/31d6d9d0f7)] - **quic**: reduce duplication of code (James M Snell) [#34137](https://github.com/nodejs/node/pull/34137)
- [[`b5fe31ef19`](https://github.com/nodejs/node/commit/b5fe31ef19)] - **quic**: avoid using private JS fields for now (James M Snell) [#34137](https://github.com/nodejs/node/pull/34137)
- [[`2afc1abd05`](https://github.com/nodejs/node/commit/2afc1abd05)] - **quic**: fixup constant exports, export all protocol error codes (James M Snell) [#34137](https://github.com/nodejs/node/pull/34137)
- [[`b1fab88ff0`](https://github.com/nodejs/node/commit/b1fab88ff0)] - **quic**: remove unused callback function (James M Snell) [#34137](https://github.com/nodejs/node/pull/34137)
- [[`3bae2d5073`](https://github.com/nodejs/node/commit/3bae2d5073)] - **quic**: consolidate onSessionClose and onSessionSilentClose (James M Snell) [#34137](https://github.com/nodejs/node/pull/34137)
- [[`def8e76999`](https://github.com/nodejs/node/commit/def8e76999)] - **quic**: fixup set_final_size (James M Snell) [#34137](https://github.com/nodejs/node/pull/34137)
- [[`d6034186d6`](https://github.com/nodejs/node/commit/d6034186d6)] - **quic**: cleanups for QuicSocket (James M Snell) [#34137](https://github.com/nodejs/node/pull/34137)
- [[`73a51bb9dc`](https://github.com/nodejs/node/commit/73a51bb9dc)] - **quic**: cleanups in JS API (James M Snell) [#34137](https://github.com/nodejs/node/pull/34137)
- [[`204f20f2d1`](https://github.com/nodejs/node/commit/204f20f2d1)] - **quic**: minor cleanups in quic_buffer (James M Snell) [#34087](https://github.com/nodejs/node/pull/34087)
- [[`68634d2592`](https://github.com/nodejs/node/commit/68634d2592)] - **quic**: remove redundant cast (gengjiawen) [#34086](https://github.com/nodejs/node/pull/34086)
- [[`213cac0b94`](https://github.com/nodejs/node/commit/213cac0b94)] - **quic**: temporarily skip quic-ipv6only test (James M Snell) [#34033](https://github.com/nodejs/node/pull/34033)
- [[`99f7c4bb5e`](https://github.com/nodejs/node/commit/99f7c4bb5e)] - **quic**: possibly resolve flaky assertion failure in ipv6only test (James M Snell) [#34033](https://github.com/nodejs/node/pull/34033)
- [[`2a5922e483`](https://github.com/nodejs/node/commit/2a5922e483)] - **quic**: temporarily disable packetloss tests (James M Snell) [#34033](https://github.com/nodejs/node/pull/34033)
- [[`86e67aaa69`](https://github.com/nodejs/node/commit/86e67aaa69)] - **quic**: updates to implement for h3-29 (James M Snell) [#34033](https://github.com/nodejs/node/pull/34033)
- [[`adf14e2617`](https://github.com/nodejs/node/commit/adf14e2617)] - **quic**: fix lint error in node_quic_crypto (Daniel Bevenius) [#34019](https://github.com/nodejs/node/pull/34019)
- [[`9f2e00fb99`](https://github.com/nodejs/node/commit/9f2e00fb99)] - **quic**: temporarily disable preferred address tests (James M Snell) [#33934](https://github.com/nodejs/node/pull/33934)
- [[`0e7c8bdc0c`](https://github.com/nodejs/node/commit/0e7c8bdc0c)] - **quic**: return 0 from SSL_CTX_sess_set_new_cb callback (Anna Henningsen) [#33931](https://github.com/nodejs/node/pull/33931)
- [[`c7d859e756`](https://github.com/nodejs/node/commit/c7d859e756)] - **quic**: refactor and improve ipv6Only (James M Snell) [#33935](https://github.com/nodejs/node/pull/33935)
- [[`1b7434dfc0`](https://github.com/nodejs/node/commit/1b7434dfc0)] - **quic**: set up FunctionTemplates more cleanly (Anna Henningsen) [#33968](https://github.com/nodejs/node/pull/33968)
- [[`8ef86a920c`](https://github.com/nodejs/node/commit/8ef86a920c)] - **quic**: fix clang warning (gengjiawen) [#33963](https://github.com/nodejs/node/pull/33963)
- [[`013cd1ac6f`](https://github.com/nodejs/node/commit/013cd1ac6f)] - **quic**: use Check instead of FromJust in node_quic.cc (Daniel Bevenius) [#33937](https://github.com/nodejs/node/pull/33937)
- [[`09330fc155`](https://github.com/nodejs/node/commit/09330fc155)] - **quic**: fix clang-tidy performance-faster-string-find issue (gengjiawen) [#33975](https://github.com/nodejs/node/pull/33975)
- [[`9743624c0b`](https://github.com/nodejs/node/commit/9743624c0b)] - **quic**: fix typo in comments (gengjiawen) [#33975](https://github.com/nodejs/node/pull/33975)
- [[`88ef15812c`](https://github.com/nodejs/node/commit/88ef15812c)] - **quic**: remove unused string include http3_application (Daniel Bevenius) [#33926](https://github.com/nodejs/node/pull/33926)
- [[`1bd88a3ac6`](https://github.com/nodejs/node/commit/1bd88a3ac6)] - **quic**: fix up node_quic_stream includes (Daniel Bevenius) [#33921](https://github.com/nodejs/node/pull/33921)
- [[`d7d79f2163`](https://github.com/nodejs/node/commit/d7d79f2163)] - **quic**: avoid memory fragmentation issue (James M Snell) [#33912](https://github.com/nodejs/node/pull/33912)
- [[`16116f5f5f`](https://github.com/nodejs/node/commit/16116f5f5f)] - **quic**: remove noop code (Robert Nagy) [#33914](https://github.com/nodejs/node/pull/33914)
- [[`272b46e04d`](https://github.com/nodejs/node/commit/272b46e04d)] - **quic**: skip test-quic-preferred-address-ipv6.js when no ipv6 (James M Snell) [#33919](https://github.com/nodejs/node/pull/33919)
- [[`4b70f95d64`](https://github.com/nodejs/node/commit/4b70f95d64)] - **quic**: use Check instead of FromJust in QuicStream (Daniel Bevenius) [#33909](https://github.com/nodejs/node/pull/33909)
- [[`133a97f60d`](https://github.com/nodejs/node/commit/133a97f60d)] - **quic**: always copy stateless reset token (Anna Henningsen) [#33917](https://github.com/nodejs/node/pull/33917)
- [[`14d012ef96`](https://github.com/nodejs/node/commit/14d012ef96)] - **quic**: fix minor linting issue (James M Snell) [#33913](https://github.com/nodejs/node/pull/33913)
- [[`55360443ce`](https://github.com/nodejs/node/commit/55360443ce)] - **quic**: initial QUIC implementation (James M Snell) [#32379](https://github.com/nodejs/node/pull/32379)
- [[`a12a2d892f`](https://github.com/nodejs/node/commit/a12a2d892f)] - **repl**: update deprecation codes (Antoine du HAMEL) [#33430](https://github.com/nodejs/node/pull/33430)
- [[`2b3acc44f0`](https://github.com/nodejs/node/commit/2b3acc44f0)] - **src**: large pages support in illumos/solaris systems (David Carlier) [#34320](https://github.com/nodejs/node/pull/34320)
- [[`84a7880749`](https://github.com/nodejs/node/commit/84a7880749)] - **src**: minor cleanup and simplification of crypto::Hash (James M Snell) [#35651](https://github.com/nodejs/node/pull/35651)
- [[`bfc906906f`](https://github.com/nodejs/node/commit/bfc906906f)] - **src**: combine TLSWrap/SSLWrap (James M Snell) [#35552](https://github.com/nodejs/node/pull/35552)
- [[`9fd6122659`](https://github.com/nodejs/node/commit/9fd6122659)] - **src**: add embedding helpers to reduce boilerplate code (Anna Henningsen) [#35597](https://github.com/nodejs/node/pull/35597)
- [[`f7ed5f4ae3`](https://github.com/nodejs/node/commit/f7ed5f4ae3)] - **src**: remove toLocalChecked in crypto_context (James M Snell) [#35509](https://github.com/nodejs/node/pull/35509)
- [[`17d5d94921`](https://github.com/nodejs/node/commit/17d5d94921)] - **src**: replace more toLocalCheckeds in crypto\_\* (James M Snell) [#35509](https://github.com/nodejs/node/pull/35509)
- [[`83eaaf9731`](https://github.com/nodejs/node/commit/83eaaf9731)] - **src**: remove unused AsyncWrapObject (James M Snell) [#35511](https://github.com/nodejs/node/pull/35511)
- [[`ee5f849fda`](https://github.com/nodejs/node/commit/ee5f849fda)] - **src**: fix compiler warning in env.cc (Anna Henningsen) [#35547](https://github.com/nodejs/node/pull/35547)
- [[`40364b181d`](https://github.com/nodejs/node/commit/40364b181d)] - **src**: add check against non-weak BaseObjects at process exit (Anna Henningsen) [#35490](https://github.com/nodejs/node/pull/35490)
- [[`bc0c094b74`](https://github.com/nodejs/node/commit/bc0c094b74)] - **src**: unset NODE_VERSION_IS_RELEASE from master (Antoine du Hamel) [#35531](https://github.com/nodejs/node/pull/35531)
- [[`fdf0a84e82`](https://github.com/nodejs/node/commit/fdf0a84e82)] - **src**: move all base64.h inline methods into -inl.h header file (Anna Henningsen) [#35432](https://github.com/nodejs/node/pull/35432)
- [[`ff4cf817a3`](https://github.com/nodejs/node/commit/ff4cf817a3)] - **src**: create helper for reading Uint32BE (Juan José Arboleda) [#34944](https://github.com/nodejs/node/pull/34944)
- [[`c6e1edcc28`](https://github.com/nodejs/node/commit/c6e1edcc28)] - **src**: add Update(const sockaddr\*) variant (James M Snell) [#34752](https://github.com/nodejs/node/pull/34752)
- [[`1c14810edc`](https://github.com/nodejs/node/commit/1c14810edc)] - **src**: allow instances of net.BlockList to be created internally (James M Snell) [#34741](https://github.com/nodejs/node/pull/34741)
- [[`6d1f0aed52`](https://github.com/nodejs/node/commit/6d1f0aed52)] - **src**: add SocketAddressLRU Utility (James M Snell) [#34618](https://github.com/nodejs/node/pull/34618)
- [[`feb93c4e84`](https://github.com/nodejs/node/commit/feb93c4e84)] - **src**: guard against nullptr deref in TimerWrapHandle::Stop (Anna Henningsen) [#34460](https://github.com/nodejs/node/pull/34460)
- [[`7a447bcd54`](https://github.com/nodejs/node/commit/7a447bcd54)] - **src**: snapshot node (Joyee Cheung) [#32984](https://github.com/nodejs/node/pull/32984)
- [[`c943cb4809`](https://github.com/nodejs/node/commit/c943cb4809)] - **src**: reset zero fill toggle at pre-execution (Joyee Cheung) [#32984](https://github.com/nodejs/node/pull/32984)
- [[`0b8ae5f2cd`](https://github.com/nodejs/node/commit/0b8ae5f2cd)] - **src**: snapshot loaders (Joyee Cheung) [#32984](https://github.com/nodejs/node/pull/32984)
- [[`7ecb285842`](https://github.com/nodejs/node/commit/7ecb285842)] - **src**: make code cache test work with snapshots (Joyee Cheung) [#32984](https://github.com/nodejs/node/pull/32984)
- [[`1faf6f459f`](https://github.com/nodejs/node/commit/1faf6f459f)] - **src**: snapshot Environment upon instantiation (Joyee Cheung) [#32984](https://github.com/nodejs/node/pull/32984)
- [[`ef9964f4c1`](https://github.com/nodejs/node/commit/ef9964f4c1)] - **src**: add an ExternalReferenceRegistry class (Joyee Cheung) [#32984](https://github.com/nodejs/node/pull/32984)
- [[`404302fff5`](https://github.com/nodejs/node/commit/404302fff5)] - **src**: split the main context initialization from Environemnt ctor (Joyee Cheung) [#32984](https://github.com/nodejs/node/pull/32984)
- [[`874460a1d1`](https://github.com/nodejs/node/commit/874460a1d1)] - **src**: refactor TimerWrap lifetime management (Anna Henningsen) [#34252](https://github.com/nodejs/node/pull/34252)
- [[`e2f9dc6e5a`](https://github.com/nodejs/node/commit/e2f9dc6e5a)] - **src**: remove user_data from TimerWrap (Anna Henningsen) [#34252](https://github.com/nodejs/node/pull/34252)
- [[`e19a251824`](https://github.com/nodejs/node/commit/e19a251824)] - **src**: replace InspectorTimer with TimerWrap utility (James M Snell) [#34186](https://github.com/nodejs/node/pull/34186)
- [[`d4f69002b4`](https://github.com/nodejs/node/commit/d4f69002b4)] - **src**: add TimerWrap utility (James M Snell) [#34186](https://github.com/nodejs/node/pull/34186)
- [[`52de4cb107`](https://github.com/nodejs/node/commit/52de4cb107)] - **src**: minor updates to FastHrtime (Anna Henningsen) [#33851](https://github.com/nodejs/node/pull/33851)
- [[`4678e44bb2`](https://github.com/nodejs/node/commit/4678e44bb2)] - **src**: perform bounds checking on error source line (Anna Henningsen) [#33645](https://github.com/nodejs/node/pull/33645)
- [[`7232c2a160`](https://github.com/nodejs/node/commit/7232c2a160)] - **src**: use getauxval in node_main.cc (Daniel Bevenius) [#33693](https://github.com/nodejs/node/pull/33693)
- [[`6be80e1893`](https://github.com/nodejs/node/commit/6be80e1893)] - **stream**: fix legacy pipe error handling (Robert Nagy) [#35257](https://github.com/nodejs/node/pull/35257)
- [[`2b9003b165`](https://github.com/nodejs/node/commit/2b9003b165)] - **stream**: don't destroy on async iterator success (Robert Nagy) [#35122](https://github.com/nodejs/node/pull/35122)
- [[`9c62e0e384`](https://github.com/nodejs/node/commit/9c62e0e384)] - **stream**: move to internal/streams (Matteo Collina) [#35239](https://github.com/nodejs/node/pull/35239)
- [[`e0d3b758a0`](https://github.com/nodejs/node/commit/e0d3b758a0)] - **stream**: improve Writable.destroy performance (Robert Nagy) [#35067](https://github.com/nodejs/node/pull/35067)
- [[`02c4869bee`](https://github.com/nodejs/node/commit/02c4869bee)] - **stream**: fix Duplex.\_construct race (Robert Nagy) [#34456](https://github.com/nodejs/node/pull/34456)
- [[`5aeaff6499`](https://github.com/nodejs/node/commit/5aeaff6499)] - **stream**: refactor lazyLoadPromises (rickyes) [#34354](https://github.com/nodejs/node/pull/34354)
- [[`a55b77d2d3`](https://github.com/nodejs/node/commit/a55b77d2d3)] - **stream**: finished on closed OutgoingMessage (Robert Nagy) [#34313](https://github.com/nodejs/node/pull/34313)
- [[`e10e292c5e`](https://github.com/nodejs/node/commit/e10e292c5e)] - **stream**: remove unused \_transformState (Robert Nagy) [#33105](https://github.com/nodejs/node/pull/33105)
- [[`f5c11a1a0a`](https://github.com/nodejs/node/commit/f5c11a1a0a)] - **stream**: don't emit finish after close (Robert Nagy) [#32933](https://github.com/nodejs/node/pull/32933)
- [[`089d654dd8`](https://github.com/nodejs/node/commit/089d654dd8)] - **test**: fix addons/dlopen-ping-pong for npm 7.0.1 (Myles Borins) [#35667](https://github.com/nodejs/node/pull/35667)
- [[`9ce5a03148`](https://github.com/nodejs/node/commit/9ce5a03148)] - **test**: add test for listen callback runtime binding (H Adinarayana) [#35657](https://github.com/nodejs/node/pull/35657)
- [[`a3731309cc`](https://github.com/nodejs/node/commit/a3731309cc)] - **test**: refactor test-https-host-headers (himself65) [#32805](https://github.com/nodejs/node/pull/32805)
- [[`30fb4a015d`](https://github.com/nodejs/node/commit/30fb4a015d)] - **test**: add common.mustSucceed (Tobias Nießen) [#35086](https://github.com/nodejs/node/pull/35086)
- [[`c143266b55`](https://github.com/nodejs/node/commit/c143266b55)] - **test**: add a few uncovered url tests from wpt (Daijiro Wachi) [#35636](https://github.com/nodejs/node/pull/35636)
- [[`6751b6dc3d`](https://github.com/nodejs/node/commit/6751b6dc3d)] - **test**: check for AbortController existence (James M Snell) [#35616](https://github.com/nodejs/node/pull/35616)
- [[`9f2e19fa30`](https://github.com/nodejs/node/commit/9f2e19fa30)] - **test**: update url test for win (Daijiro Wachi) [#35622](https://github.com/nodejs/node/pull/35622)
- [[`c88d845db3`](https://github.com/nodejs/node/commit/c88d845db3)] - **test**: update wpt status for url (Daijiro Wachi) [#35335](https://github.com/nodejs/node/pull/35335)
- [[`589dbf1392`](https://github.com/nodejs/node/commit/589dbf1392)] - **test**: update wpt tests for url (Daijiro Wachi) [#35329](https://github.com/nodejs/node/pull/35329)
- [[`46bef7b771`](https://github.com/nodejs/node/commit/46bef7b771)] - **test**: add Actions annotation output (Mary Marchini) [#34590](https://github.com/nodejs/node/pull/34590)
- [[`a9c5b873ca`](https://github.com/nodejs/node/commit/a9c5b873ca)] - **test**: move buffer-as-path symlink test to its own test file (Rich Trott) [#34569](https://github.com/nodejs/node/pull/34569)
- [[`31ba9a20bd`](https://github.com/nodejs/node/commit/31ba9a20bd)] - **test**: run test-benchmark-napi on arm (Rich Trott) [#34502](https://github.com/nodejs/node/pull/34502)
- [[`2c4ebe0426`](https://github.com/nodejs/node/commit/2c4ebe0426)] - **test**: use `.then(common.mustCall())` for all async IIFEs (Anna Henningsen) [#34363](https://github.com/nodejs/node/pull/34363)
- [[`772fdb0cd3`](https://github.com/nodejs/node/commit/772fdb0cd3)] - **test**: fix flaky test-fs-stream-construct (Rich Trott) [#34203](https://github.com/nodejs/node/pull/34203)
- [[`9b8d317d99`](https://github.com/nodejs/node/commit/9b8d317d99)] - **test**: fix flaky test-http2-invalidheaderfield (Rich Trott) [#34173](https://github.com/nodejs/node/pull/34173)
- [[`2ccf15b2bf`](https://github.com/nodejs/node/commit/2ccf15b2bf)] - **test**: ensure finish is emitted before destroy (Robert Nagy) [#33137](https://github.com/nodejs/node/pull/33137)
- [[`27f3530da3`](https://github.com/nodejs/node/commit/27f3530da3)] - **test**: remove unnecessary eslint-disable comment (Rich Trott) [#34000](https://github.com/nodejs/node/pull/34000)
- [[`326a79ebb9`](https://github.com/nodejs/node/commit/326a79ebb9)] - **test**: fix typo in test-quic-client-empty-preferred-address.js (gengjiawen) [#33976](https://github.com/nodejs/node/pull/33976)
- [[`b0b268f5a2`](https://github.com/nodejs/node/commit/b0b268f5a2)] - **test**: fix flaky fs-construct test (Robert Nagy) [#33625](https://github.com/nodejs/node/pull/33625)
- [[`cbe955c227`](https://github.com/nodejs/node/commit/cbe955c227)] - **test**: add net regression test (Robert Nagy) [#32794](https://github.com/nodejs/node/pull/32794)
- [[`5d179cb2ec`](https://github.com/nodejs/node/commit/5d179cb2ec)] - **timers**: use AbortController with correct name/message (Anna Henningsen) [#34763](https://github.com/nodejs/node/pull/34763)
- [[`64d22c320c`](https://github.com/nodejs/node/commit/64d22c320c)] - **timers**: fix multipleResolves in promisified timeouts/immediates (Denys Otrishko) [#33949](https://github.com/nodejs/node/pull/33949)
- [[`fbe33aa52e`](https://github.com/nodejs/node/commit/fbe33aa52e)] - **tools**: bump remark-lint-preset-node to 1.17.1 (Rich Trott) [#35668](https://github.com/nodejs/node/pull/35668)
- [[`35a6946193`](https://github.com/nodejs/node/commit/35a6946193)] - **tools**: update gyp-next to v0.6.2 (Michaël Zasso) [#35690](https://github.com/nodejs/node/pull/35690)
- [[`be80faa0c8`](https://github.com/nodejs/node/commit/be80faa0c8)] - **tools**: update gyp-next to v0.6.0 (Ujjwal Sharma) [#35635](https://github.com/nodejs/node/pull/35635)
- [[`2d83e743d9`](https://github.com/nodejs/node/commit/2d83e743d9)] - **tools**: update ESLint to 7.11.0 (Colin Ihrig) [#35578](https://github.com/nodejs/node/pull/35578)
- [[`0eca660948`](https://github.com/nodejs/node/commit/0eca660948)] - **tools**: update ESLint to 7.7.0 (Colin Ihrig) [#34783](https://github.com/nodejs/node/pull/34783)
- [[`77b68f9a29`](https://github.com/nodejs/node/commit/77b68f9a29)] - **tools**: add linting rule for async IIFEs (Anna Henningsen) [#34363](https://github.com/nodejs/node/pull/34363)
- [[`f04538761f`](https://github.com/nodejs/node/commit/f04538761f)] - **tools**: enable Node.js command line flags in node_mksnapshot (Joyee Cheung) [#32984](https://github.com/nodejs/node/pull/32984)
- [[`b0d4eb37c7`](https://github.com/nodejs/node/commit/b0d4eb37c7)] - **tools**: update ESLint to 7.4.0 (Colin Ihrig) [#34205](https://github.com/nodejs/node/pull/34205)
- [[`076e4ed2d1`](https://github.com/nodejs/node/commit/076e4ed2d1)] - **tools**: update ESLint from 7.2.0 to 7.3.1 (Rich Trott) [#34000](https://github.com/nodejs/node/pull/34000)
- [[`7afe3af200`](https://github.com/nodejs/node/commit/7afe3af200)] - **url**: fix file url reparse (Daijiro Wachi) [#35671](https://github.com/nodejs/node/pull/35671)

Windows 32-bit Installer: https://nodejs.org/dist/v15.0.0/node-v15.0.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v15.0.0/node-v15.0.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v15.0.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v15.0.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v15.0.0/node-v15.0.0.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v15.0.0/node-v15.0.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v15.0.0/node-v15.0.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v15.0.0/node-v15.0.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v15.0.0/node-v15.0.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v15.0.0/node-v15.0.0-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v15.0.0/node-v15.0.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v15.0.0/node-v15.0.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v15.0.0/node-v15.0.0.tar.gz \
Other release files: https://nodejs.org/dist/v15.0.0/ \
Documentation: https://nodejs.org/docs/v15.0.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

3d56e708ba3283a4c087c6265936e37c728568aa3636d3487553a728e96750d9  node-v15.0.0-aix-ppc64.tar.gz
f9eee1f659d96991bc629ec1cec986f504242fc0f046f4487d2fe13b9ab37c99  node-v15.0.0-darwin-x64.tar.gz
7d031b7921b1331acfe38ea5b48830dd3aa09976c14ada5544eea08965948b63  node-v15.0.0-darwin-x64.tar.xz
92b041e298e2b4a8d086349d99bfd8fdf03d75f8556d0e94977a4b09f8a460a0  node-v15.0.0-headers.tar.gz
282c87c6c71f459cc1c17faec509f0eb945ee8a39fafd8d287a8870a5785ee6a  node-v15.0.0-headers.tar.xz
2127a2627e3efe839c09d61f99cd99a58a9037dbb668abd21c279c25697522eb  node-v15.0.0-linux-arm64.tar.gz
52aba1b117499680adea5fec43083fcb94a3483eda182ea82ca47f5d7c9eec97  node-v15.0.0-linux-arm64.tar.xz
6bbbdd866ebed81e04102dcdd64dc547131798ed57823197880a526454018af1  node-v15.0.0-linux-armv7l.tar.gz
8db4d51506556c373f21abff52fdace36d95623cbf3932cd63d073becf43117d  node-v15.0.0-linux-armv7l.tar.xz
1e09fdbc90a36977233cdd35f4fb54f63c8a989ad79de3fb5555db30464df977  node-v15.0.0-linux-ppc64le.tar.gz
b3a0da54a16fff1d5e69bd99387543bb23fa0b454b176090895c9fadb34db0f2  node-v15.0.0-linux-ppc64le.tar.xz
56828910f07f0cc2b7b2e4e08f6b8fc8f83877c5a3dfd9510f5d7837d13a154f  node-v15.0.0-linux-s390x.tar.gz
7c916b1f3729a66701a0fc0ffe0afde05e401ccc3c53e7114db37cfbf4604f27  node-v15.0.0-linux-s390x.tar.xz
405cb1bdde623fe568608a4ed35f876762c65ae4ccbd8ed75de3cf170733fc33  node-v15.0.0-linux-x64.tar.gz
054c1c20ee237614e12ee2baab1ec96bfafc835a2d36fb2b860fdf10be0777b0  node-v15.0.0-linux-x64.tar.xz
dd744821b9c924b6027e1c89e8b146b88e05cb88a6d2537712064eb78b4dbe95  node-v15.0.0.pkg
72f6234594877ab212c43a76f2f5b05d409d7c025e0eb36d675d9acfd0d3f2d8  node-v15.0.0.tar.gz
f6138a17060733c986e43a4eaa2f34879db6b7efee3f4ca4c740968573ff347b  node-v15.0.0.tar.xz
0f1f3d970ac2786fdc64d42b6c31cd6837b92b844fc6d861b1dcddca58a34815  node-v15.0.0-win-x64.7z
fe8358e18a1f5a44849420d9d76c755e7489f92e0933c8d780107cd688e1d3d6  node-v15.0.0-win-x64.zip
ea9f1beb8dcd9d0514a293d956613a3d42d480c06d28410163537e44408da567  node-v15.0.0-win-x86.7z
594e3d218df64b91b1412895310a60d48574c76b4fa826a33416ab156606cb77  node-v15.0.0-win-x86.zip
58d9f5bb9ba5e7d03b097d712e174abc54dd9fe7b0829000faceee9a13cebd78  node-v15.0.0-x64.msi
8cd6357e5d5937429d39f55f8a37140433958bfc3219a52739c5111f8c5c4144  node-v15.0.0-x86.msi
c512735a1ce282f6820162de87d233d4d9cc76b34e3184542c9c35202d909fe5  win-x64/node.exe
0668ebb22765d3e797c4401f36587f5cc4ada69611e2793cb79209625aca0931  win-x64/node.lib
b1c8f41b7425f61db44f3eecd1877c32da4c70f2f854756269d702bbfafe743a  win-x64/node_pdb.7z
99a72a9170bd68fa3802407029cf5a7b533956c619ba959cdb11cab404f5db69  win-x64/node_pdb.zip
9b5f2948501568811f96200ff18fb8d60167c9ce333021a497a5cc70257f44dc  win-x86/node.exe
3cbb6fca39edc95246ed5169ac8fb1c7e575e45be873dbae255577f66f4d2b2b  win-x86/node.lib
cd5ec6e5609e77316c68312db441bcd1123adf32d8967a13435cb518469de48a  win-x86/node_pdb.7z
1b625ab7ea349f652618d8b06eec3225b9420d64c170589cf91125f40fec6a13  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQEzBAEBCAAdFiEETtd49TnjY0x3nIfG1wYoSKGrAFwFAl+O+D4ACgkQ1wYoSKGr
AFwB2Qf8C4uHMyvZd5+nbX3KjWLGqhUN7II+T0NRXaVCO+5PH/UEWn3TpGYFp1it
CqsP5EvO9a5bYRZYngdFr5H93+NLIH/sMV9zTDNt4yfEWqXiyGri4fU0eWxGiffK
Fl7M2vGZZUwLwEd1X0WrMSexHYjSB7iI5bISoi6ikeo7K+boE9fdQBume23Q5Fxr
8sIp4oRG3/OfkU+v2j64YjVlf7FAQlfPBiTQg/hAIVY5l6MwERr4mMQeVO3/XLej
oYu+roSIIF4XvQFIGwWKQdTF9+LeQBKqws06pED55aMAhd+LnNnD1CLXZP7RSgqW
QfO61OYu8neqB7i5DqHvC2Bbiwowdw==
=MwTe
-----END PGP SIGNATURE-----

```
