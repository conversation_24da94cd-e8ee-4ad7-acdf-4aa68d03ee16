---
date: '2018-02-01T00:11:27.951Z'
category: release
title: Node v9.5.0 (Current)
layout: blog-post
author: <PERSON>
---

### Notable Changes

- **cluster**
  - add cwd to cluster.settings (cjihrig) [#18399](https://github.com/nodejs/node/pull/18399)
- **deps**
  - upgrade libuv to 1.19.1 (cjihrig) [#18260](https://github.com/nodejs/node/pull/18260)
- **meta**
  - add <PERSON><PERSON> to collaborators (<PERSON><PERSON>) [#18117](https://github.com/nodejs/node/pull/18117)
  - add v<PERSON><PERSON><PERSON><PERSON> as collaborator (v<PERSON><PERSON><PERSON><PERSON>) [#18432](https://github.com/nodejs/node/pull/18432)
- **n-api**
  - expose n-api version in process.versions (<PERSON>) [#18067](https://github.com/nodejs/node/pull/18067)
- **perf_hooks**
  - add performance.clear() (<PERSON>) [#18046](https://github.com/nodejs/node/pull/18046)
- **stream**
  - avoid writeAfterEnd() while ending (陈刚) [#18170](https://github.com/nodejs/node/pull/18170)

### Commits

- [[`0a68018ad0`](https://github.com/nodejs/node/commit/0a68018ad0)] - **async_hooks**: update defaultTriggerAsyncIdScope for perf (Anatoli Papirovski) [#18004](https://github.com/nodejs/node/pull/18004)
- [[`dd56bd1591`](https://github.com/nodejs/node/commit/dd56bd1591)] - **async_hooks**: use typed array stack as fast path (Anna Henningsen) [#17780](https://github.com/nodejs/node/pull/17780)
- [[`a880e272ff`](https://github.com/nodejs/node/commit/a880e272ff)] - **async_hooks**: use scope for defaultTriggerAsyncId (Andreas Madsen) [#17273](https://github.com/nodejs/node/pull/17273)
- [[`f56eb2a41e`](https://github.com/nodejs/node/commit/f56eb2a41e)] - **async_hooks**: separate missing from default context (Andreas Madsen) [#17273](https://github.com/nodejs/node/pull/17273)
- [[`2a4f849c39`](https://github.com/nodejs/node/commit/2a4f849c39)] - **async_hooks**: rename initTriggerId (Andreas Madsen) [#17273](https://github.com/nodejs/node/pull/17273)
- [[`ac2f98d6a6`](https://github.com/nodejs/node/commit/ac2f98d6a6)] - **(SEMVER-MINOR)** **async_hooks,http**: set HTTPParser trigger to socket (Andreas Madsen) [#18003](https://github.com/nodejs/node/pull/18003)
- [[`e9397d67a3`](https://github.com/nodejs/node/commit/e9397d67a3)] - **async_hooks,test**: only use IPv6 in http test (Andreas Madsen) [#18143](https://github.com/nodejs/node/pull/18143)
- [[`2efa7d1bfd`](https://github.com/nodejs/node/commit/2efa7d1bfd)] - **benchmark**: implement duration in http test double (Joyee Cheung) [#18380](https://github.com/nodejs/node/pull/18380)
- [[`b5ec6ea3d0`](https://github.com/nodejs/node/commit/b5ec6ea3d0)] - **benchmark**: make compare.R easier to understand (Andreas Madsen) [#18373](https://github.com/nodejs/node/pull/18373)
- [[`ea19f7db0d`](https://github.com/nodejs/node/commit/ea19f7db0d)] - **benchmark**: use destructuring (Ruben Bridgewater) [#18250](https://github.com/nodejs/node/pull/18250)
- [[`cd9bc8bc50`](https://github.com/nodejs/node/commit/cd9bc8bc50)] - **benchmark**: (dgram) use destructuring (Ruben Bridgewater) [#18250](https://github.com/nodejs/node/pull/18250)
- [[`e19c77b14e`](https://github.com/nodejs/node/commit/e19c77b14e)] - **benchmark**: (child_process) use destructuring (Ruben Bridgewater) [#18250](https://github.com/nodejs/node/pull/18250)
- [[`5cf5ab154e`](https://github.com/nodejs/node/commit/5cf5ab154e)] - **benchmark**: (buffers) use destructuring (Ruben Bridgewater) [#18250](https://github.com/nodejs/node/pull/18250)
- [[`71faa5c1b4`](https://github.com/nodejs/node/commit/71faa5c1b4)] - **benchmark**: (events) use destructuring (Ruben Bridgewater) [#18250](https://github.com/nodejs/node/pull/18250)
- [[`c25d4d66dc`](https://github.com/nodejs/node/commit/c25d4d66dc)] - **benchmark**: (es) use destructuring (Ruben Bridgewater) [#18250](https://github.com/nodejs/node/pull/18250)
- [[`59271c8f7f`](https://github.com/nodejs/node/commit/59271c8f7f)] - **benchmark**: (fs) use destructuring (Ruben Bridgewater) [#18250](https://github.com/nodejs/node/pull/18250)
- [[`4e19cbef86`](https://github.com/nodejs/node/commit/4e19cbef86)] - **benchmark**: (http) use destructuring (Ruben Bridgewater) [#18250](https://github.com/nodejs/node/pull/18250)
- [[`e9c426b35b`](https://github.com/nodejs/node/commit/e9c426b35b)] - **benchmark**: (misc) use destructuring (Ruben Bridgewater) [#18250](https://github.com/nodejs/node/pull/18250)
- [[`d13d900eee`](https://github.com/nodejs/node/commit/d13d900eee)] - **benchmark**: (http2) use destructuring (Ruben Bridgewater) [#18250](https://github.com/nodejs/node/pull/18250)
- [[`97e882061d`](https://github.com/nodejs/node/commit/97e882061d)] - **benchmark**: (string_decoder) use destructuring (Ruben Bridgewater) [#18250](https://github.com/nodejs/node/pull/18250)
- [[`5b0e3b9860`](https://github.com/nodejs/node/commit/5b0e3b9860)] - **benchmark**: (path) use destructuring (Ruben Bridgewater) [#18250](https://github.com/nodejs/node/pull/18250)
- [[`7bc5bad74f`](https://github.com/nodejs/node/commit/7bc5bad74f)] - **benchmark**: (os) use destructuring (Ruben Bridgewater) [#18250](https://github.com/nodejs/node/pull/18250)
- [[`cf666d8529`](https://github.com/nodejs/node/commit/cf666d8529)] - **benchmark**: (net) use destructuring (Ruben Bridgewater) [#18250](https://github.com/nodejs/node/pull/18250)
- [[`88f4bf219d`](https://github.com/nodejs/node/commit/88f4bf219d)] - **benchmark**: (process) use destructuring (Ruben Bridgewater) [#18250](https://github.com/nodejs/node/pull/18250)
- [[`f4918289e7`](https://github.com/nodejs/node/commit/f4918289e7)] - **benchmark**: (querystring) use destructuring (Ruben Bridgewater) [#18250](https://github.com/nodejs/node/pull/18250)
- [[`81abea592f`](https://github.com/nodejs/node/commit/81abea592f)] - **benchmark**: (streams) use destructuring (Ruben Bridgewater) [#18250](https://github.com/nodejs/node/pull/18250)
- [[`11d6458fd7`](https://github.com/nodejs/node/commit/11d6458fd7)] - **benchmark**: (timers) use destructuring (Ruben Bridgewater) [#18250](https://github.com/nodejs/node/pull/18250)
- [[`3e3254a2e7`](https://github.com/nodejs/node/commit/3e3254a2e7)] - **benchmark**: (tls) use destructuring (Ruben Bridgewater) [#18250](https://github.com/nodejs/node/pull/18250)
- [[`c0707c54a5`](https://github.com/nodejs/node/commit/c0707c54a5)] - **benchmark**: (util/v8/vm) use destructuring (Ruben Bridgewater) [#18250](https://github.com/nodejs/node/pull/18250)
- [[`76f671b84e`](https://github.com/nodejs/node/commit/76f671b84e)] - **benchmark**: (zlib) use destructuring (Ruben Bridgewater) [#18250](https://github.com/nodejs/node/pull/18250)
- [[`aa47fe0ef9`](https://github.com/nodejs/node/commit/aa47fe0ef9)] - **benchmark**: (url) use destructuring (Ruben Bridgewater) [#18250](https://github.com/nodejs/node/pull/18250)
- [[`e00dac7b06`](https://github.com/nodejs/node/commit/e00dac7b06)] - **benchmark**: (assert) use destructuring (Ruben Bridgewater) [#18250](https://github.com/nodejs/node/pull/18250)
- [[`3543458988`](https://github.com/nodejs/node/commit/3543458988)] - **benchmark**: (arrays) use destructuring (Ruben Bridgewater) [#18250](https://github.com/nodejs/node/pull/18250)
- [[`aa21d55403`](https://github.com/nodejs/node/commit/aa21d55403)] - **benchmark**: remove redundant + (sreepurnajasti) [#17803](https://github.com/nodejs/node/pull/17803)
- [[`a4ba791566`](https://github.com/nodejs/node/commit/a4ba791566)] - **benchmark**: add JSStreamWrap benchmark (Anna Henningsen) [#17983](https://github.com/nodejs/node/pull/17983)
- [[`deac028cb6`](https://github.com/nodejs/node/commit/deac028cb6)] - **build**: fix rm commands in tarball rule (Ben Noordhuis) [#18332](https://github.com/nodejs/node/pull/18332)
- [[`2a9afc4c0e`](https://github.com/nodejs/node/commit/2a9afc4c0e)] - **build**: make lint-js independent of local node (Joyee Cheung) [#18272](https://github.com/nodejs/node/pull/18272)
- [[`ce1eb0be7e`](https://github.com/nodejs/node/commit/ce1eb0be7e)] - **build**: make lint-md independent of local node (Joyee Cheung) [#18272](https://github.com/nodejs/node/pull/18272)
- [[`f050521a71`](https://github.com/nodejs/node/commit/f050521a71)] - **build**: define NOMINMAX on windows (Ben Noordhuis) [#18216](https://github.com/nodejs/node/pull/18216)
- [[`70d6fda9f3`](https://github.com/nodejs/node/commit/70d6fda9f3)] - **build**: remove unused vars from configure (Ben Noordhuis) [#18206](https://github.com/nodejs/node/pull/18206)
- [[`f81c62246d`](https://github.com/nodejs/node/commit/f81c62246d)] - **build**: refine static and shared lib build (Yihong Wang) [#17604](https://github.com/nodejs/node/pull/17604)
- [[`1506eb5f25`](https://github.com/nodejs/node/commit/1506eb5f25)] - **build**: remove bench-\* targets (Joyee Cheung) [#18150](https://github.com/nodejs/node/pull/18150)
- [[`969c89bf55`](https://github.com/nodejs/node/commit/969c89bf55)] - **build**: fix Makefile wrt finding node executable (Yang Guo) [#18040](https://github.com/nodejs/node/pull/18040)
- [[`dd72f9c9b7`](https://github.com/nodejs/node/commit/dd72f9c9b7)] - **build**: fix cctest target with --enable-static (Qingyan Li) [#17992](https://github.com/nodejs/node/pull/17992)
- [[`2c4e0216de`](https://github.com/nodejs/node/commit/2c4e0216de)] - **build,win**: update lint-cpp on Windows (Kyle Farnung) [#18012](https://github.com/nodejs/node/pull/18012)
- [[`d8ac817cb6`](https://github.com/nodejs/node/commit/d8ac817cb6)] - **build,win**: restore vcbuild TAG functionality (Rod Vagg) [#18031](https://github.com/nodejs/node/pull/18031)
- [[`799fd24acb`](https://github.com/nodejs/node/commit/799fd24acb)] - **(SEMVER-MINOR)** **cluster**: add cwd to cluster.settings (cjihrig) [#18399](https://github.com/nodejs/node/pull/18399)
- [[`6b687cf3c9`](https://github.com/nodejs/node/commit/6b687cf3c9)] - **cluster**: resolve relative unix socket paths (laino) [#16749](https://github.com/nodejs/node/pull/16749)
- [[`693159e627`](https://github.com/nodejs/node/commit/693159e627)] - **(SEMVER-MINOR)** **deps**: upgrade libuv to 1.19.1 (cjihrig) [#18260](https://github.com/nodejs/node/pull/18260)
- [[`506d85bfba`](https://github.com/nodejs/node/commit/506d85bfba)] - **deps**: cherry-pick c3458a8 from upstream V8 (Michaël Zasso) [#18060](https://github.com/nodejs/node/pull/18060)
- [[`45051fa48c`](https://github.com/nodejs/node/commit/45051fa48c)] - **doc**: add vdeturckheim as collaborator (vdeturckheim) [#18432](https://github.com/nodejs/node/pull/18432)
- [[`03cb06944b`](https://github.com/nodejs/node/commit/03cb06944b)] - **doc**: unify type linkification (Vse Mozhet Byt) [#18407](https://github.com/nodejs/node/pull/18407)
- [[`d829237b92`](https://github.com/nodejs/node/commit/d829237b92)] - **doc**: fix typo in REPL docs (Adam Engebretson) [#18404](https://github.com/nodejs/node/pull/18404)
- [[`6ae7bb143a`](https://github.com/nodejs/node/commit/6ae7bb143a)] - **doc**: fix e.g., to e.g. in docs (sreepurnajasti) [#18369](https://github.com/nodejs/node/pull/18369)
- [[`574d3b9ce8`](https://github.com/nodejs/node/commit/574d3b9ce8)] - **doc**: fix documentation of http2Stream.pushstream() (Peter Dalgaard-Jensen) [#18258](https://github.com/nodejs/node/pull/18258)
- [[`4d3121b6ed`](https://github.com/nodejs/node/commit/4d3121b6ed)] - **doc**: fix return value for require.resolve.paths() (Peter Dalgaard-Jensen) [#18350](https://github.com/nodejs/node/pull/18350)
- [[`987480c232`](https://github.com/nodejs/node/commit/987480c232)] - **doc**: add missing word in modules.md (Robert Adamian) [#18343](https://github.com/nodejs/node/pull/18343)
- [[`224cc64d0c`](https://github.com/nodejs/node/commit/224cc64d0c)] - **doc**: add doc for performance.clearGC() (Antony Tran) [#18331](https://github.com/nodejs/node/pull/18331)
- [[`e5f6159958`](https://github.com/nodejs/node/commit/e5f6159958)] - **doc**: document the collaborator nomination process (Joyee Cheung) [#18268](https://github.com/nodejs/node/pull/18268)
- [[`c9e09adc8d`](https://github.com/nodejs/node/commit/c9e09adc8d)] - **doc**: improve the instructions of onboarding PR (Joyee Cheung) [#18268](https://github.com/nodejs/node/pull/18268)
- [[`b055c9efe5`](https://github.com/nodejs/node/commit/b055c9efe5)] - **doc**: split CONTRIBUTING.md (Joyee Cheung) [#18271](https://github.com/nodejs/node/pull/18271)
- [[`485d60eea2`](https://github.com/nodejs/node/commit/485d60eea2)] - **doc**: fix typos in async_hooks (Matthew Turner) [#18314](https://github.com/nodejs/node/pull/18314)
- [[`e3cc0919f6`](https://github.com/nodejs/node/commit/e3cc0919f6)] - **doc**: add missing URL argument types in fs.md (Vse Mozhet Byt) [#18309](https://github.com/nodejs/node/pull/18309)
- [[`1efb9cd271`](https://github.com/nodejs/node/commit/1efb9cd271)] - **doc**: remove confusing signature in fs.md (Vse Mozhet Byt) [#18310](https://github.com/nodejs/node/pull/18310)
- [[`195bed21eb`](https://github.com/nodejs/node/commit/195bed21eb)] - **doc**: use PBKDF2 in text (Tobias Nießen) [#18279](https://github.com/nodejs/node/pull/18279)
- [[`17ef69e6e2`](https://github.com/nodejs/node/commit/17ef69e6e2)] - **doc**: fix typo in async_hooks.md (Matthew Turner) [#18286](https://github.com/nodejs/node/pull/18286)
- [[`01599e2959`](https://github.com/nodejs/node/commit/01599e2959)] - **doc**: Add example of null to assert.ifError (Leko) [#18236](https://github.com/nodejs/node/pull/18236)
- [[`5c5aa4969c`](https://github.com/nodejs/node/commit/5c5aa4969c)] - **doc**: improve process.platform (Mars Wong) [#18057](https://github.com/nodejs/node/pull/18057)
- [[`61df843c95`](https://github.com/nodejs/node/commit/61df843c95)] - **doc**: cjs format is now commonjs (Gus Caplan) [#18165](https://github.com/nodejs/node/pull/18165)
- [[`361fd33709`](https://github.com/nodejs/node/commit/361fd33709)] - **doc**: V8 branch used in 8.x not active anymore (Franziska Hinkelmann) [#18155](https://github.com/nodejs/node/pull/18155)
- [[`b553daa29b`](https://github.com/nodejs/node/commit/b553daa29b)] - **doc**: add change info for async_hooks.executionAsyncId() (Stephen Belanger) [#17813](https://github.com/nodejs/node/pull/17813)
- [[`4b918d79df`](https://github.com/nodejs/node/commit/4b918d79df)] - **doc**: remove uannecessary Require (Michael Dawson) [#18184](https://github.com/nodejs/node/pull/18184)
- [[`926467ab80`](https://github.com/nodejs/node/commit/926467ab80)] - **doc**: add builtin module in building.md (Suixinlei) [#17705](https://github.com/nodejs/node/pull/17705)
- [[`1ef8f4e22e`](https://github.com/nodejs/node/commit/1ef8f4e22e)] - **doc**: warn users about non-ASCII paths on build (Matheus Marchini) [#16735](https://github.com/nodejs/node/pull/16735)
- [[`a1096a6b05`](https://github.com/nodejs/node/commit/a1096a6b05)] - **doc**: simplify sentences that use "considered" (Rich Trott) [#18095](https://github.com/nodejs/node/pull/18095)
- [[`1d74c33148`](https://github.com/nodejs/node/commit/1d74c33148)] - **doc**: update sample output for process.versions (Michael Dawson) [#18167](https://github.com/nodejs/node/pull/18167)
- [[`2fb5f19894`](https://github.com/nodejs/node/commit/2fb5f19894)] - **doc**: fix typo in TextEncoding section (Yosuke Furukawa) [#18201](https://github.com/nodejs/node/pull/18201)
- [[`b4e7260d3e`](https://github.com/nodejs/node/commit/b4e7260d3e)] - **doc**: fix typo in http2stream.close param default (Moritz Peters) [#18166](https://github.com/nodejs/node/pull/18166)
- [[`b05f09a587`](https://github.com/nodejs/node/commit/b05f09a587)] - **doc**: suggest not to throw JS errors from C++ (Joyee Cheung) [#18149](https://github.com/nodejs/node/pull/18149)
- [[`5a95905d91`](https://github.com/nodejs/node/commit/5a95905d91)] - **doc**: napi: make header style consistent (Ali Ijaz Sheikh) [#18122](https://github.com/nodejs/node/pull/18122)
- [[`990abbf06c`](https://github.com/nodejs/node/commit/990abbf06c)] - **doc**: napi: fix unbalanced emphasis (Ali Ijaz Sheikh) [#18122](https://github.com/nodejs/node/pull/18122)
- [[`f8f809b7fa`](https://github.com/nodejs/node/commit/f8f809b7fa)] - **doc**: add documentation for deprecation properties (Jon Moss) [#16539](https://github.com/nodejs/node/pull/16539)
- [[`0e8596e2a6`](https://github.com/nodejs/node/commit/0e8596e2a6)] - **doc**: prefer make test-only when verifying the build (Joyee Cheung) [#18061](https://github.com/nodejs/node/pull/18061)
- [[`bbdc3c4ae8`](https://github.com/nodejs/node/commit/bbdc3c4ae8)] - **doc**: add Leko to collaborators (Leko) [#18117](https://github.com/nodejs/node/pull/18117)
- [[`afc30a56e3`](https://github.com/nodejs/node/commit/afc30a56e3)] - **doc**: decapitalize primitive types (Vse Mozhet Byt) [#18110](https://github.com/nodejs/node/pull/18110)
- [[`30e2221a15`](https://github.com/nodejs/node/commit/30e2221a15)] - **doc**: fix s/rstStream/close in example (James M Snell) [#18088](https://github.com/nodejs/node/pull/18088)
- [[`1c81a055df`](https://github.com/nodejs/node/commit/1c81a055df)] - **doc**: update pushStream docs to use err first (James M Snell) [#18088](https://github.com/nodejs/node/pull/18088)
- [[`de70a363eb`](https://github.com/nodejs/node/commit/de70a363eb)] - **doc**: be less tentative about undefined behavior (Rich Trott) [#18091](https://github.com/nodejs/node/pull/18091)
- [[`5ebd0178a6`](https://github.com/nodejs/node/commit/5ebd0178a6)] - **doc**: add descriptions of state properties (James M Snell) [#18044](https://github.com/nodejs/node/pull/18044)
- [[`7911b9b493`](https://github.com/nodejs/node/commit/7911b9b493)] - **doc**: examples for fast-tracking regression fixes (Refael Ackermann) [#17379](https://github.com/nodejs/node/pull/17379)
- [[`f0a0fdd83a`](https://github.com/nodejs/node/commit/f0a0fdd83a)] - **doc**: multiple updates to BUILDING.md (Rich Trott) [#17985](https://github.com/nodejs/node/pull/17985)
- [[`278450fc72`](https://github.com/nodejs/node/commit/278450fc72)] - **doc**: multiple updates to child_process.md (Rich Trott) [#17990](https://github.com/nodejs/node/pull/17990)
- [[`722fe464bc`](https://github.com/nodejs/node/commit/722fe464bc)] - **_Revert_** "**doc**: import() is supported now" (Myles Borins) [#18141](https://github.com/nodejs/node/pull/18141)
- [[`39970e9caf`](https://github.com/nodejs/node/commit/39970e9caf)] - **doc**: un-mark Socket#write “removal” as notable change (Anna Henningsen) [#18083](https://github.com/nodejs/node/pull/18083)
- [[`df8cb401a0`](https://github.com/nodejs/node/commit/df8cb401a0)] - **errors**: remove ERR_OUTOFMEMORY (Tobias Nießen) [#17877](https://github.com/nodejs/node/pull/17877)
- [[`230a102647`](https://github.com/nodejs/node/commit/230a102647)] - **fs**: cleanup fd lchown and lchownSync (James M Snell) [#18329](https://github.com/nodejs/node/pull/18329)
- [[`778d57c2c2`](https://github.com/nodejs/node/commit/778d57c2c2)] - **fs**: fix options.end of fs.ReadStream() (陈刚) [#18121](https://github.com/nodejs/node/pull/18121)
- [[`7fc395a0d7`](https://github.com/nodejs/node/commit/7fc395a0d7)] - **http**: there is no `corked` property of `stream` (Fedor Indutny) [#18325](https://github.com/nodejs/node/pull/18325)
- [[`b87939cf53`](https://github.com/nodejs/node/commit/b87939cf53)] - **http**: use strict comparison (leeseean) [#17011](https://github.com/nodejs/node/pull/17011)
- [[`0250e1b9c0`](https://github.com/nodejs/node/commit/0250e1b9c0)] - **http**: free the parser before emitting 'upgrade' (Luigi Pinca) [#18209](https://github.com/nodejs/node/pull/18209)
- [[`155622847f`](https://github.com/nodejs/node/commit/155622847f)] - **http**: fix parsing of binary upgrade response body (Ben Noordhuis) [#17806](https://github.com/nodejs/node/pull/17806)
- [[`8e084d8bfb`](https://github.com/nodejs/node/commit/8e084d8bfb)] - **http**: simplify parser lifetime tracking (Anna Henningsen) [#18135](https://github.com/nodejs/node/pull/18135)
- [[`ee6217a4c7`](https://github.com/nodejs/node/commit/ee6217a4c7)] - **http2**: add checks for server close callback (James M Snell) [#18182](https://github.com/nodejs/node/pull/18182)
- [[`b3332cce46`](https://github.com/nodejs/node/commit/b3332cce46)] - **http2**: refactor read mechanism (Anna Henningsen) [#18030](https://github.com/nodejs/node/pull/18030)
- [[`eee40c71c9`](https://github.com/nodejs/node/commit/eee40c71c9)] - **http2**: remember sent headers (James M Snell) [#18045](https://github.com/nodejs/node/pull/18045)
- [[`39612a8657`](https://github.com/nodejs/node/commit/39612a8657)] - **http2,perf_hooks**: perf state using AliasedBuffer (Kyle Farnung) [#18300](https://github.com/nodejs/node/pull/18300)
- [[`14f7f607f6`](https://github.com/nodejs/node/commit/14f7f607f6)] - **(SEMVER-MINOR)** **lib**: add internal removeColors helper (Ruben Bridgewater) [#17615](https://github.com/nodejs/node/pull/17615)
- [[`74c1f4ef78`](https://github.com/nodejs/node/commit/74c1f4ef78)] - **lib**: fix typo in trace_events_async_hooks.js (Gilles De Mey) [#18280](https://github.com/nodejs/node/pull/18280)
- [[`485d656013`](https://github.com/nodejs/node/commit/485d656013)] - **lib**: use american spelling as per style guide (sreepurnajasti) [#18226](https://github.com/nodejs/node/pull/18226)
- [[`dcdb646ada`](https://github.com/nodejs/node/commit/dcdb646ada)] - **lib**: fix spelling in comments (Tobias Nießen) [#18018](https://github.com/nodejs/node/pull/18018)
- [[`8f8e7479cb`](https://github.com/nodejs/node/commit/8f8e7479cb)] - **lib**: remove queue implementation from JSStreamWrap (Anna Henningsen) [#17918](https://github.com/nodejs/node/pull/17918)
- [[`9edf023694`](https://github.com/nodejs/node/commit/9edf023694)] - **n-api**: throw RangeError napi_create_typedarray() (Jinho Bang) [#18037](https://github.com/nodejs/node/pull/18037)
- [[`0668a75c39`](https://github.com/nodejs/node/commit/0668a75c39)] - **(SEMVER-MINOR)** **n-api**: expose n-api version in process.versions (Michael Dawson) [#18067](https://github.com/nodejs/node/pull/18067)
- [[`f693e81ee5`](https://github.com/nodejs/node/commit/f693e81ee5)] - **n-api**: throw RangeError in napi_create_dataview() with invalid range (Jinho Bang) [#17869](https://github.com/nodejs/node/pull/17869)
- [[`470832f203`](https://github.com/nodejs/node/commit/470832f203)] - **path**: fix path.normalize for relative paths (Weijia Wang) [#17974](https://github.com/nodejs/node/pull/17974)
- [[`645be73b9d`](https://github.com/nodejs/node/commit/645be73b9d)] - **(SEMVER-MINOR)** **perf_hooks,http2**: add performance.clear() (James M Snell) [#18046](https://github.com/nodejs/node/pull/18046)
- [[`11982aecd4`](https://github.com/nodejs/node/commit/11982aecd4)] - **process**: JS fast path for bindings (Anatoli Papirovski) [#18365](https://github.com/nodejs/node/pull/18365)
- [[`ce7ce9d1ee`](https://github.com/nodejs/node/commit/ce7ce9d1ee)] - **process**: clean up signal handler setup (Anatoli Papirovski) [#18330](https://github.com/nodejs/node/pull/18330)
- [[`a5b35db5d2`](https://github.com/nodejs/node/commit/a5b35db5d2)] - **process**: remove dead code (Anatoli Papirovski) [#18330](https://github.com/nodejs/node/pull/18330)
- [[`56a9ae7773`](https://github.com/nodejs/node/commit/56a9ae7773)] - **readline**: update references to archived repository (Tobias Nießen) [#17924](https://github.com/nodejs/node/pull/17924)
- [[`144cfb4b99`](https://github.com/nodejs/node/commit/144cfb4b99)] - **src**: remove outdated domain reference (Anatoli Papirovski) [#18291](https://github.com/nodejs/node/pull/18291)
- [[`3ab391d3d3`](https://github.com/nodejs/node/commit/3ab391d3d3)] - **src**: remove unnecessary block scope (Anatoli Papirovski) [#18291](https://github.com/nodejs/node/pull/18291)
- [[`84f8e62f97`](https://github.com/nodejs/node/commit/84f8e62f97)] - **src**: DRY ip address parsing code in cares_wrap.cc (Ben Noordhuis) [#18398](https://github.com/nodejs/node/pull/18398)
- [[`ecf5bea485`](https://github.com/nodejs/node/commit/ecf5bea485)] - **src**: remove unused variable (cjihrig) [#18385](https://github.com/nodejs/node/pull/18385)
- [[`1c8df28752`](https://github.com/nodejs/node/commit/1c8df28752)] - **src**: fix -Wimplicit-fallthrough warning (Ben Noordhuis) [#18205](https://github.com/nodejs/node/pull/18205)
- [[`4513cbb4fe`](https://github.com/nodejs/node/commit/4513cbb4fe)] - **src**: refactor callback #defines into C++ templates (Anna Henningsen) [#18133](https://github.com/nodejs/node/pull/18133)
- [[`077bcbd202`](https://github.com/nodejs/node/commit/077bcbd202)] - **src**: introduce internal buffer slice constructor (Anna Henningsen) [#18030](https://github.com/nodejs/node/pull/18030)
- [[`87e3d3db89`](https://github.com/nodejs/node/commit/87e3d3db89)] - **src**: fix code coverage cleanup (Michael Dawson) [#18081](https://github.com/nodejs/node/pull/18081)
- [[`15aaf18b72`](https://github.com/nodejs/node/commit/15aaf18b72)] - **src**: remove declarations for missing functions (Anna Henningsen) [#18134](https://github.com/nodejs/node/pull/18134)
- [[`ac0a0a6775`](https://github.com/nodejs/node/commit/ac0a0a6775)] - **src**: harden JSStream callbacks (Anna Henningsen) [#18028](https://github.com/nodejs/node/pull/18028)
- [[`217ddd8ba2`](https://github.com/nodejs/node/commit/217ddd8ba2)] - **src,doc,test**: Fix common misspellings (Roman Reiss) [#18151](https://github.com/nodejs/node/pull/18151)
- [[`c4abdcdc30`](https://github.com/nodejs/node/commit/c4abdcdc30)] - **(SEMVER-MINOR)** **stream**: avoid writeAfterEnd() while ending (陈刚) [#18170](https://github.com/nodejs/node/pull/18170)
- [[`25bebae61c`](https://github.com/nodejs/node/commit/25bebae61c)] - **stream**: simplify `src._readableState` to `state` (陈刚) [#18264](https://github.com/nodejs/node/pull/18264)
- [[`f7d57d039a`](https://github.com/nodejs/node/commit/f7d57d039a)] - **stream**: remove unreachable code (Luigi Pinca) [#18239](https://github.com/nodejs/node/pull/18239)
- [[`117b20e621`](https://github.com/nodejs/node/commit/117b20e621)] - **test**: adds tests for vm invalid arguments (Gilles De Mey) [#18282](https://github.com/nodejs/node/pull/18282)
- [[`c84dd03120`](https://github.com/nodejs/node/commit/c84dd03120)] - **test**: refactor addons-napi/test_exception/test.js (Rich Trott) [#18340](https://github.com/nodejs/node/pull/18340)
- [[`1458e51d2f`](https://github.com/nodejs/node/commit/1458e51d2f)] - **test**: fix test-tls-server-verify.js on Windows CI (Rich Trott) [#18382](https://github.com/nodejs/node/pull/18382)
- [[`7d27228e90`](https://github.com/nodejs/node/commit/7d27228e90)] - **test**: use correct size in test-stream-buffer-list (Luigi Pinca) [#18239](https://github.com/nodejs/node/pull/18239)
- [[`5855a57d52`](https://github.com/nodejs/node/commit/5855a57d52)] - **test**: change assert message to default (ryanmahan) [#18259](https://github.com/nodejs/node/pull/18259)
- [[`fc89cea5cd`](https://github.com/nodejs/node/commit/fc89cea5cd)] - **test**: use countdown timer (Mandeep Singh) [#17326](https://github.com/nodejs/node/pull/17326)
- [[`761f26eb12`](https://github.com/nodejs/node/commit/761f26eb12)] - **test**: make async-wrap-getasyncid parallelizable (Joyee Cheung) [#18245](https://github.com/nodejs/node/pull/18245)
- [[`506c6e841c`](https://github.com/nodejs/node/commit/506c6e841c)] - **test**: refactor test-http-parser (Jon Moss) [#18219](https://github.com/nodejs/node/pull/18219)
- [[`5b5f5b1b32`](https://github.com/nodejs/node/commit/5b5f5b1b32)] - **test**: add assertions for TextEncoder/Decoder (Sho Miyamoto) [#18132](https://github.com/nodejs/node/pull/18132)
- [[`3299a1a19b`](https://github.com/nodejs/node/commit/3299a1a19b)] - **test**: remove trivial buffer imports (sreepurnajasti) [#18034](https://github.com/nodejs/node/pull/18034)
- [[`78e05da071`](https://github.com/nodejs/node/commit/78e05da071)] - **test**: use shorthand properties (Tobias Nießen) [#18105](https://github.com/nodejs/node/pull/18105)
- [[`63be0d6daa`](https://github.com/nodejs/node/commit/63be0d6daa)] - **test**: simplify loadDHParam in TLS test (Tobias Nießen) [#18103](https://github.com/nodejs/node/pull/18103)
- [[`1dcae5756e`](https://github.com/nodejs/node/commit/1dcae5756e)] - **test**: improve to use template string (sreepurnajasti) [#18097](https://github.com/nodejs/node/pull/18097)
- [[`0c8b5d5bfb`](https://github.com/nodejs/node/commit/0c8b5d5bfb)] - **test**: fixed typos in napi test (furstenheim) [#18148](https://github.com/nodejs/node/pull/18148)
- [[`2aeb025999`](https://github.com/nodejs/node/commit/2aeb025999)] - **test**: add common.crashOnUnhandledRejection to addons/callback-scope (Sho Miyamoto) [#18076](https://github.com/nodejs/node/pull/18076)
- [[`7706e5f1ea`](https://github.com/nodejs/node/commit/7706e5f1ea)] - **test**: remove orphaned entries from status (Kyle Farnung) [#18092](https://github.com/nodejs/node/pull/18092)
- [[`5fccb6ea3a`](https://github.com/nodejs/node/commit/5fccb6ea3a)] - **test**: fix spelling in test case comments (Tobias Nießen) [#18018](https://github.com/nodejs/node/pull/18018)
- [[`3456e61b44`](https://github.com/nodejs/node/commit/3456e61b44)] - **test**: use smaller input file for test-zlib.js (Rich Trott) [#17988](https://github.com/nodejs/node/pull/17988)
- [[`733df362fa`](https://github.com/nodejs/node/commit/733df362fa)] - **test**: update references to archived repository (Tobias Nießen) [#17924](https://github.com/nodejs/node/pull/17924)
- [[`2eb1aa81fa`](https://github.com/nodejs/node/commit/2eb1aa81fa)] - **test**: move common.fires() to inspector-helper (Rich Trott) [#17401](https://github.com/nodejs/node/pull/17401)
- [[`167e9c6dcd`](https://github.com/nodejs/node/commit/167e9c6dcd)] - **test**: refactor test-repl (Anna Henningsen) [#17926](https://github.com/nodejs/node/pull/17926)
- [[`7b73e704ca`](https://github.com/nodejs/node/commit/7b73e704ca)] - **timers**: attach listOnTimeout function to TimerWrap (Matteo Collina) [#18388](https://github.com/nodejs/node/pull/18388)
- [[`96b072233a`](https://github.com/nodejs/node/commit/96b072233a)] - **tls**: refactor write queues away (Anna Henningsen) [#17883](https://github.com/nodejs/node/pull/17883)
- [[`be9958afb6`](https://github.com/nodejs/node/commit/be9958afb6)] - **tools**: use babel-eslint as ESLint parser (Michaël Zasso) [#17820](https://github.com/nodejs/node/pull/17820)
- [[`715e673d06`](https://github.com/nodejs/node/commit/715e673d06)] - **tools**: add babel-eslint (Michaël Zasso) [#17820](https://github.com/nodejs/node/pull/17820)
- [[`d349fcae11`](https://github.com/nodejs/node/commit/d349fcae11)] - **tools**: update ESLint to 4.15.0 (Michaël Zasso) [#17820](https://github.com/nodejs/node/pull/17820)
- [[`4bc4d004b1`](https://github.com/nodejs/node/commit/4bc4d004b1)] - **tools**: move eslint from tools to tools/node_modules (Michaël Zasso) [#17820](https://github.com/nodejs/node/pull/17820)

Windows 32-bit Installer: https://nodejs.org/dist/v9.5.0/node-v9.5.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v9.5.0/node-v9.5.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v9.5.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v9.5.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v9.5.0/node-v9.5.0.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v9.5.0/node-v9.5.0-darwin-x64.tar.gz \
Linux 32-bit Binary: https://nodejs.org/dist/v9.5.0/node-v9.5.0-linux-x86.tar.xz \
Linux 64-bit Binary: https://nodejs.org/dist/v9.5.0/node-v9.5.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v9.5.0/node-v9.5.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v9.5.0/node-v9.5.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v9.5.0/node-v9.5.0-aix-ppc64.tar.gz \
SmartOS 32-bit Binary: https://nodejs.org/dist/v9.5.0/node-v9.5.0-sunos-x86.tar.xz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v9.5.0/node-v9.5.0-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v9.5.0/node-v9.5.0-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v9.5.0/node-v9.5.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v9.5.0/node-v9.5.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v9.5.0/node-v9.5.0.tar.gz \
Other release files: https://nodejs.org/dist/v9.5.0/ \
Documentation: https://nodejs.org/docs/v9.5.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

12b97b006a49248b65d679506ce17309ef2668f1887352f95eb74447a44657ed  node-v9.5.0-aix-ppc64.tar.gz
bc5c6eb21a90dbcaf90479838416c90524fe6d47e92c928ef57d7e96a54eb5fe  node-v9.5.0-darwin-x64.tar.gz
af09d9bac919c06810d0194a508bc93261f676aa98b27d384cb980727cb8c1cd  node-v9.5.0-darwin-x64.tar.xz
e689ae70a12b4b8b6d4c451d79232996370b5b44d984846ddc392ee4d4833caa  node-v9.5.0-headers.tar.gz
df2d09eaf8209552bb0e961d856e54d9c2ac5b044b81fec4bb9ef51e4ac5f9a5  node-v9.5.0-headers.tar.xz
08924ad820d6322e17cc0fbbc365000b76408a4f17c3ed3169b44d8c7448a617  node-v9.5.0-linux-arm64.tar.gz
d6a98a51c032963014f606acc80babb456f3dc4c041534ca565f573a2382b253  node-v9.5.0-linux-arm64.tar.xz
c386a52c5c1a050c0b639ba9f979c0a1f418ed02855227358fcff4479f2348dc  node-v9.5.0-linux-armv6l.tar.gz
8c098587b2c9c9a85ddea52f51c9526118263f1b0cc80cc32230cf2b7ddbaae9  node-v9.5.0-linux-armv6l.tar.xz
b990ffdb862543a9a48ea28929272d66606b84899f124b40c5b100e0a3560bf7  node-v9.5.0-linux-armv7l.tar.gz
0f9ca7a2eeb45243d035bf30597488d758b495ab2ef6687b2773297b0389dbe5  node-v9.5.0-linux-armv7l.tar.xz
fa9229b6cd6d240c62183c1f109f533679f6cf142a580712cf5fe84f989ef584  node-v9.5.0-linux-ppc64le.tar.gz
3c2fd37534de95ff806b491f4fa9f8589904c6b71f387425dc12291aac83e0f3  node-v9.5.0-linux-ppc64le.tar.xz
32765c6f357692fbe3d495bc0fe1bd1bdea88c243bb6244fe943a95590cc4e29  node-v9.5.0-linux-s390x.tar.gz
affe4ec41b5a55f1c417168a6f68783bad6927247a1a3325ce9129284714330e  node-v9.5.0-linux-s390x.tar.xz
1002312e51cc8dcae788962d1971355f559bfbf7caef36cafda84339c0cd3dc6  node-v9.5.0-linux-x64.tar.gz
76dd38bb5a16a610894353228ef020653f81209a2e509d38aee78ace4410599e  node-v9.5.0-linux-x64.tar.xz
d262b88071a2d5e6e0123914275353cec61e296e9b91925f3ef4a498b92dcc81  node-v9.5.0-linux-x86.tar.gz
672e9d9aa7e8edb5529069e9787c1dec290963e600d08fdfbe404b8f8ac39a44  node-v9.5.0-linux-x86.tar.xz
b7b69958bc073099ab560635646b2efcd26a561216ea1b922722fd760eef9e98  node-v9.5.0.pkg
2183de79b533fd3e23ca2d703eaddc649bda723c7c0ad41f56587e1bb786686c  node-v9.5.0-sunos-x64.tar.gz
72fe17f45b67911355944913ee39fc1ba9bd43dbe0cf16478e19cc892cdbc2ee  node-v9.5.0-sunos-x64.tar.xz
710345eb725c80e84ed3501ad7f2a8d2ecfad155addf89823e01430cb841e4b6  node-v9.5.0-sunos-x86.tar.gz
274e01b1b5c2a9b55d4afec16c55b6bb2bd52095b20ab905599cdf42cfcde7bf  node-v9.5.0-sunos-x86.tar.xz
039d4f6fa5b5855f44a662297c0ac515ef49047af721201e9f53c5d16fd3824c  node-v9.5.0.tar.gz
666b463a378b65bb83afb8f2c271865e03804d5930c95688ab4020c5dcd5146d  node-v9.5.0.tar.xz
ccd91770352325793b86b2287a01a801467de1bcdb0fd04364a79474292e7683  node-v9.5.0-win-x64.7z
2fd1c3865a34827af6b5d799aee68e011a16a637276cbf71e923c8bedd010ee8  node-v9.5.0-win-x64.zip
3e0aca4c1f03f7903975f001df4bfed9a51fa4f4fd2a195ed724b2daef51cc84  node-v9.5.0-win-x86.7z
f1d520a9bf2476952eccd393ab5b329342c5bc1e19145ce29af67eda48e7f756  node-v9.5.0-win-x86.zip
40686bed53ec793f2c9513ebdabd49b654cbf32742226534e71398f0c48c94de  node-v9.5.0-x64.msi
139e45ad8206893c9ea9e1d2d5a04e75790bc16ce4a71b955bcfb2f1405b04a4  node-v9.5.0-x86.msi
dd2d3d62d9ade4beaec9853073ee9adae035c66a8163a0c4281471ece44058f2  win-x64/node.exe
3c9d700a4e7a2057890370579bfbf08f2f0664549260c8eb63d5391b9847d2bb  win-x64/node.lib
dcc32f9b5bb97628069677b940947e901089301fc457280d0641769a3a1f683c  win-x64/node_pdb.7z
0760280f0ec46dfc2aaf56db4da661e0e02f6fb0455301cf2e7913f716e7a827  win-x64/node_pdb.zip
f1547071b98c980758c3421ab7fa53255fa6a2fa14d5cf04326d3c9a896601f2  win-x86/node.exe
432893fd32939598a3bf9d2fa34da5aee3f6000d54e8637913a459a085a0213b  win-x86/node.lib
33ad98ae51d90605cc87a4f3abc2851471dea3bf8da9b371cb8ebbd0d44ded42  win-x86/node_pdb.7z
03bb078bb7ca9b40c50a0dbc05dd457e06dc37bbab586231312f0a4a04315467  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEua6ZBf/XgD8lcUZhtjtTWkwgbKkFAlpyWYcACgkQtjtTWkwg
bKmefRAAwYQL8Z2KNGqO7IDwgFKZvXl5j5d2JboAy3LyHqXiZu8EbKmsXxsV0Mju
ZIxzLCpD3qPujT4hhbFfELNxYXZleLpAR/opqn9ao2QDDdTS+4eEClHK67IHQA6Y
JKT6A1gemSHZy5s4si8osEAlmehys4FpWOkCOJavaD8veh1Rcu0/yTwIO2x5aBVn
JgrQk8a1SiKydNIcJv/9LEE54hHc/OSs825+wc/tsNOTH2HwliM7PY28xVI25xue
STBoB+Du+MoKaC2k9WmG5a6DEMRrSFS6nX8Vv6d92UvZ37s45pYbU5UBMK+PDURe
y2mf1eaVCDdGAWG5qnaofdRP5S4WRAvpC9ea6MG6YeQqGw9lTPz9fwkIFg55qPxt
50dJMoEbX7a4iIuWWmljF71sz9FHiNVPrfE/t0iYgX3hZ+W1TEWpsIrMuymUWuSO
IfakBlcr6FUNIhkHIYotD1WC/xh8rwA77OkXHZkerUIWkC2BYuKk2p7aB04YiuA9
nCBftZmh6X3sABVc0nBgta9CIJd+Eoj+nhupAYx9U7pQcFJXCLArZoz4PYSHy0Fv
oBHERanSeJ6P/DNYkKeW1j0uhUgPYwmdVHvWyAhEz64MofCX/6XFq8A/w4KJ6H9z
trS5Rd+iOx+seB/+qA6pm2zWeFY1OApoiszuutWbCvZi8ySabAk=
=571M
-----END PGP SIGNATURE-----

```
