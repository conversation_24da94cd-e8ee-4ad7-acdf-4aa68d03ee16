---
date: '2020-07-21T16:01:23.998Z'
category: release
title: Node v10.22.0 (LTS)
layout: blog-post
author: <PERSON>
---

This release was prepared by <PERSON> ([@richard<PERSON>](https://github.com/richardlau)) and promoted by <PERSON> ([@Beth<PERSON>riggs](https://github.com/bethgriggs)).

### Notable changes

- **deps**:
  - upgrade npm to 6.14.6 (claudiahdz) [#34246](https://github.com/nodejs/node/pull/34246)
  - upgrade openssl sources to 1.1.1g (<PERSON><PERSON><PERSON>) [#32982](https://github.com/nodejs/node/pull/32982)
- **n-api**:
  - add `napi_detach_arraybuffer` (legendecas) [#29768](https://github.com/nodejs/node/pull/29768)

### Commits

- [[`9915774d18`](https://github.com/nodejs/node/commit/9915774d18)] - **build**: log detected compilers in --verbose mode (<PERSON>) [#32715](https://github.com/nodejs/node/pull/32715)
- [[`145dcc2c1c`](https://github.com/nodejs/node/commit/145dcc2c1c)] - **build**: move doc versions JSON file out of out/doc (Richard Lau) [#32728](https://github.com/nodejs/node/pull/32728)
- [[`24b927ab66`](https://github.com/nodejs/node/commit/24b927ab66)] - **build**: allow clang 10+ in configure.py (Kamil Rytarowski) [#29541](https://github.com/nodejs/node/pull/29541)
- [[`97b59527c7`](https://github.com/nodejs/node/commit/97b59527c7)] - **deps**: upgrade npm to 6.14.6 (claudiahdz) [#34246](https://github.com/nodejs/node/pull/34246)
- [[`84fca3c691`](https://github.com/nodejs/node/commit/84fca3c691)] - **deps**: upgrade npm to 6.14.5 (Ruy Adorno) [#33239](https://github.com/nodejs/node/pull/33239)
- [[`745b329260`](https://github.com/nodejs/node/commit/745b329260)] - **deps**: update archs files for OpenSSL-1.1.1g (Hassaan Pasha) [#32982](https://github.com/nodejs/node/pull/32982)
- [[`94702c1560`](https://github.com/nodejs/node/commit/94702c1560)] - **deps**: upgrade openssl sources to 1.1.1g (Hassaan Pasha) [#32982](https://github.com/nodejs/node/pull/32982)
- [[`ef9413be1a`](https://github.com/nodejs/node/commit/ef9413be1a)] - **deps**: upgrade openssl sources to 1.1.1f (Hassaan Pasha) [#32583](https://github.com/nodejs/node/pull/32583)
- [[`3acc89f8f2`](https://github.com/nodejs/node/commit/3acc89f8f2)] - **deps**: V8: backport cd21f71f9cb5 (Michaël Zasso) [#33862](https://github.com/nodejs/node/pull/33862)
- [[`89a306bca9`](https://github.com/nodejs/node/commit/89a306bca9)] - **deps**: fix V8 compiler error with clang++-11 (Sam Roberts) [#33094](https://github.com/nodejs/node/pull/33094)
- [[`00f04e3b79`](https://github.com/nodejs/node/commit/00f04e3b79)] - **doc**: fix quotes in tls.md (Sparsh Garg) [#33641](https://github.com/nodejs/node/pull/33641)
- [[`193d1d0e84`](https://github.com/nodejs/node/commit/193d1d0e84)] - **doc**: document fs.watchFile() bigint option (cjihrig) [#32128](https://github.com/nodejs/node/pull/32128)
- [[`5dab101b03`](https://github.com/nodejs/node/commit/5dab101b03)] - **doc,n-api**: mark napi_detach_arraybuffer as experimental (legendecas) [#30703](https://github.com/nodejs/node/pull/30703)
- [[`069b6e14a4`](https://github.com/nodejs/node/commit/069b6e14a4)] - **http**: disable headersTimeout check when set to zero (Paolo Insogna) [#33307](https://github.com/nodejs/node/pull/33307)
- [[`aaf2f827c6`](https://github.com/nodejs/node/commit/aaf2f827c6)] - **inspector**: more conservative minimum stack size (Ben Noordhuis) [#27855](https://github.com/nodejs/node/pull/27855)
- [[`b744ffd586`](https://github.com/nodejs/node/commit/b744ffd586)] - **(SEMVER-MINOR)** **n-api**: implement napi_is_detached_arraybuffer (Denys Otrishko) [#30613](https://github.com/nodejs/node/pull/30613)
- [[`961598b9be`](https://github.com/nodejs/node/commit/961598b9be)] - **(SEMVER-MINOR)** **n-api**: add `napi_detach_arraybuffer` (legendecas) [#29768](https://github.com/nodejs/node/pull/29768)
- [[`7a109febc4`](https://github.com/nodejs/node/commit/7a109febc4)] - **test**: remove timers-blocking-callback (Jeremiah Senkpiel) [#32870](https://github.com/nodejs/node/pull/32870)
- [[`3dbd8cd3a9`](https://github.com/nodejs/node/commit/3dbd8cd3a9)] - **_Revert_** "**test**: mark empty udp tests flaky on OS X" (Luigi Pinca) [#32489](https://github.com/nodejs/node/pull/32489)
- [[`543656928c`](https://github.com/nodejs/node/commit/543656928c)] - **test**: flaky test-stdout-close-catch on freebsd (Sam Roberts) [#32849](https://github.com/nodejs/node/pull/32849)
- [[`74b00cca64`](https://github.com/nodejs/node/commit/74b00cca64)] - **tls**: allow empty subject even with altNames defined (Jason Macgowan) [#22906](https://github.com/nodejs/node/pull/22906)

Windows 32-bit Installer: https://nodejs.org/dist/v10.22.0/node-v10.22.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v10.22.0/node-v10.22.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v10.22.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v10.22.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v10.22.0/node-v10.22.0.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v10.22.0/node-v10.22.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v10.22.0/node-v10.22.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v10.22.0/node-v10.22.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v10.22.0/node-v10.22.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v10.22.0/node-v10.22.0-aix-ppc64.tar.gz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v10.22.0/node-v10.22.0-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v10.22.0/node-v10.22.0-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v10.22.0/node-v10.22.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v10.22.0/node-v10.22.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v10.22.0/node-v10.22.0.tar.gz \
Other release files: https://nodejs.org/dist/v10.22.0/ \
Documentation: https://nodejs.org/docs/v10.22.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

15fc4672df24d28e606acb9f4d0c5dd72e11cefca880107167d661b6c7a6a455  node-v10.22.0-aix-ppc64.tar.gz
c7583a297ba9c6cfc03688a32776155d02fabf9ff45847c63b12a68d400f1dc1  node-v10.22.0-darwin-x64.tar.gz
55dd341163e0a907e5effa683f8c90082341aaefcdf9ff07f7382bff4c8214bc  node-v10.22.0-darwin-x64.tar.xz
2c493a04e5bfbf07545c60226a50cea32148919f3593a003cba32131f4621396  node-v10.22.0-headers.tar.gz
c51022996822368a02af76d92148b294297efc7923328a85db5fbf35c034fd58  node-v10.22.0-headers.tar.xz
8e59eb6865f704785a9aa53ccf9f4cb10412caaf778cee617241a0d0684e008d  node-v10.22.0-linux-arm64.tar.gz
abacc6f37e8dfbe398843c7dc7b9bb7153ff6e653ad50e85d73d86088da48372  node-v10.22.0-linux-arm64.tar.xz
5701c7923062b04276005c77e7cf8e99e3bb5a1d1b8cdf6a4f512b1359136470  node-v10.22.0-linux-armv6l.tar.gz
d305111b06b0e90fd3e2babc2f2130855d2514a220e1f48318e536d15541690e  node-v10.22.0-linux-armv6l.tar.xz
c99179db48adfd77f369878573eb9b96b13007cda5af86653cd0f5a8d772fc90  node-v10.22.0-linux-armv7l.tar.gz
fcd95bf12340d15277cd2a35bc134eb417f41b0123733729d291ecbd668fe3f4  node-v10.22.0-linux-armv7l.tar.xz
68663f1099fe41451c8f97ef58a4a5c9a73a6dea4827c01167451ddc6188b135  node-v10.22.0-linux-ppc64le.tar.gz
bb332f4c9a3b694cc22d06cc1129f5d0e70361cb8c0e15e203a8a85dc53b9f75  node-v10.22.0-linux-ppc64le.tar.xz
92f5802394ce506a9dfa962f2cdb3397cbaa5e50029cd0f54576e31365a1e377  node-v10.22.0-linux-s390x.tar.gz
d28d22c560deeae02a196f87affbec99702df2e170de83d53bfbafb75f502973  node-v10.22.0-linux-s390x.tar.xz
aa7e9e1d8abcc169119bf5c56ede515689f2644ccc4d40ca0fc33756a3deb1f7  node-v10.22.0-linux-x64.tar.gz
ddf33e038c593d6df36b1dd4b25c1b6fa8230c615e6312ad33e80ef863e4a74f  node-v10.22.0-linux-x64.tar.xz
2c005a4903db6f17ff181d58b93a09a4b9174531bed3bc71c24aea61fc9c74a6  node-v10.22.0.pkg
7dad1490bc260c4963d3f0150e2c4cfbcaddd2427f34e9fd26fc8273e93c587c  node-v10.22.0-sunos-x64.tar.gz
6ac09102a6ea36b6639c8c27197947ba5e74882b22948ce8a6e1138e73e5ee68  node-v10.22.0-sunos-x64.tar.xz
8a77f883a9cba5451cef547f737e590a32c9840a4ab421a048f2fadda799ba41  node-v10.22.0.tar.gz
e07575455cbb6670e3f30d2f846d3078ef2c181255ff0932089182529443e1db  node-v10.22.0.tar.xz
50583765a014957d02c33c35c290233eadf3e716f94ab36684583f665463bd0d  node-v10.22.0-win-x64.7z
931c2907450790f89aa178fa84c1adbd1f7cb7ab0a34f8bfb4af25640e8d4e06  node-v10.22.0-win-x64.zip
0a2aed768fe8b0f9c88bb9911540b17348a57202fc82d3a3853babc552b7e0f0  node-v10.22.0-win-x86.7z
323b80a1c29d0bcd86c6d03b3229a13819f84718331a61414914755afa46089d  node-v10.22.0-win-x86.zip
0d9c8dc187fa0de6cc11bf31fe8ad9786d8f2a7e1b4242f95239885577cffef8  node-v10.22.0-x64.msi
643c04e90adf7722f1b7ddbf67814e6355299f6c81b97ac5a66e1b45b88ac113  node-v10.22.0-x86.msi
d216e659bd75e2bfe1f7f379d17690080ec4617fab3e3f6833be10981ff761fe  win-x64/node.exe
d5319c35505d417dbd608b9e6070aa04f33c3361d747745ffb220216a8e477a4  win-x64/node.lib
2fb6e3e9f9529ab65ebeb971e3bd9f03aea05cb84e4b7cfaa888fd9687f21293  win-x64/node_pdb.7z
44718139a81112fc6d2f9687ba2c3ce71250c5cbe8a4fdccbe18de5e59283760  win-x64/node_pdb.zip
c956572590ab15c8985bd3303b219551f259dcd3ed26d25a83ba6acafc66df40  win-x86/node.exe
879c6fa995dc49b6ef41b01874cb4fccdb31c47cff07b5e96dd03541c48f2d5c  win-x86/node.lib
81f5b7d72e3a114ac4282c90c037901cf658bad6630e2d27e5f143183a97d061  win-x86/node_pdb.7z
bfb2df431d95f096d3ab5bf4c2e5ee3edd31457c826b11501d469ec8edfd8705  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQEzBAEBCAAdFiEETtd49TnjY0x3nIfG1wYoSKGrAFwFAl8XECkACgkQ1wYoSKGr
AFxzDAf/c7mnQkKKAipVOXbQgKX5tDS3tuoRINco3mpPSv5megNLmZdQE2j0RGm9
pLYUvIw/kMLikkxsv/1xLIwCKhwwGz+AutaFFn76hsYc/TtmYZgAlFl3fnB3kmZm
Gu+x8nLkXLJ38a8nrlDLNXIIYWGHRpqZFjwaKfg5UBGpSg//LA/xGMc32Uf+5HN4
IlOfziYQtDEzqq1UdfpokZtfQ31MadNvlI1MRag9Dw/WcTSgCKIB512v9XnpjwFm
dS1GHfU20g8Ja0j6McvWyGfjwJFToNaD93mOOrRRGQLcvD2PJ/XAoYmo5Fnsolix
HO3J077xOc19es9dztDhebbH6iZ37A==
=gmEq
-----END PGP SIGNATURE-----

```
