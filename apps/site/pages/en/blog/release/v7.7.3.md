---
date: '2017-03-14T22:42:24.967Z'
category: release
title: Node v7.7.3 (Current)
layout: blog-post
author: <PERSON><PERSON> <PERSON><PERSON>
---

### Notable changes

- **module**: The [module loading global fallback](https://nodejs.org/dist/latest-v6.x/docs/api/modules.html#modules_loading_from_the_global_folders) to the Node executable's directory now works correctly on Windows. (<PERSON>) [#9283](https://github.com/nodejs/node/pull/9283)
- **net**: `Socket.prototype.connect` now once again functions without a callback. (<PERSON><PERSON>) [#11762](https://github.com/nodejs/node/pull/11762)
- **url**: `URL.prototype.origin` now properly specified an opaque return of `'null'` for `file://` URLs. (<PERSON>) [#11691](https://github.com/nodejs/node/pull/11691)

### Commits

- [[`542a3735a7`](https://github.com/nodejs/node/commit/542a3735a7)] - **build**: add node_use_openssl check to install.py (<PERSON>) [#11766](https://github.com/nodejs/node/pull/11766)
- [[`2fcefeeda0`](https://github.com/nodejs/node/commit/2fcefeeda0)] - **dgram**: refactor dgram to module.exports (Claudio Rodriguez) [#11696](https://github.com/nodejs/node/pull/11696)
- [[`dd3e6adaa7`](https://github.com/nodejs/node/commit/dd3e6adaa7)] - **doc**: add missing changelog heading for 7.7.2 (Evan Lucas) [#11823](https://github.com/nodejs/node/pull/11823)
- [[`b543fd441c`](https://github.com/nodejs/node/commit/b543fd441c)] - **doc**: update to current V8 versions (Franziska Hinkelmann) [#11787](https://github.com/nodejs/node/pull/11787)
- [[`6cc7b30c62`](https://github.com/nodejs/node/commit/6cc7b30c62)] - **doc**: improve child_process `maxBuffer` text (Rich Trott) [#11791](https://github.com/nodejs/node/pull/11791)
- [[`188cbc6eea`](https://github.com/nodejs/node/commit/188cbc6eea)] - **doc**: package main can be directory with an index (Bradley Farias) [#11581](https://github.com/nodejs/node/pull/11581)
- [[`a20aa0ee48`](https://github.com/nodejs/node/commit/a20aa0ee48)] - **doc**: http cleanup and missing argument types (Amelia Clarke) [#11681](https://github.com/nodejs/node/pull/11681)
- [[`8a1b2b4417`](https://github.com/nodejs/node/commit/8a1b2b4417)] - **doc**: reduce font size on smaller screens (Gibson Fahnestock) [#11695](https://github.com/nodejs/node/pull/11695)
- [[`5bea8b42d9`](https://github.com/nodejs/node/commit/5bea8b42d9)] - **doc**: fix occurences of "the the" (Jeroen Mandersloot) [#11711](https://github.com/nodejs/node/pull/11711)
- [[`517c3af21a`](https://github.com/nodejs/node/commit/517c3af21a)] - **doc**: fix process links to console.log/error (Sam Roberts) [#11718](https://github.com/nodejs/node/pull/11718)
- [[`108449b6ff`](https://github.com/nodejs/node/commit/108449b6ff)] - **doc**: add Franziska Hinkelmann to the CTC (Rod Vagg) [#11488](https://github.com/nodejs/node/pull/11488)
- [[`9c3cf13cbc`](https://github.com/nodejs/node/commit/9c3cf13cbc)] - **doc**: argument types for https methods (Amelia Clarke) [#11681](https://github.com/nodejs/node/pull/11681)
- [[`103458772a`](https://github.com/nodejs/node/commit/103458772a)] - **module**: fix loading from global folders on Windows (Richard Lau) [#9283](https://github.com/nodejs/node/pull/9283)
- [[`1dff218cd1`](https://github.com/nodejs/node/commit/1dff218cd1)] - **net**: allow missing callback for Socket.connect (Juwan Yoo) [#11762](https://github.com/nodejs/node/pull/11762)
- [[`52f0092f54`](https://github.com/nodejs/node/commit/52f0092f54)] - **s390**: enable march=z196 (Junliang Yan) [#11730](https://github.com/nodejs/node/pull/11730)
- [[`032becdc28`](https://github.com/nodejs/node/commit/032becdc28)] - **src**: add missing #include \<unicode/ustring.h\> (Steven R. Loomis) [#11754](https://github.com/nodejs/node/issues/11754)
- [[`1da2afcc26`](https://github.com/nodejs/node/commit/1da2afcc26)] - **src**: drop the NODE_ISOLATE_SLOT macro (Anna Henningsen) [#11692](https://github.com/nodejs/node/pull/11692)
- [[`734ddbe77b`](https://github.com/nodejs/node/commit/734ddbe77b)] - **test**: fix flaky test-http-set-timeout-server (Santiago Gimeno) [#11790](https://github.com/nodejs/node/pull/11790)
- [[`aaf8536dbc`](https://github.com/nodejs/node/commit/aaf8536dbc)] - **test**: add test for loading from global folders (Richard Lau) [#9283](https://github.com/nodejs/node/pull/9283)
- [[`c01c7a490a`](https://github.com/nodejs/node/commit/c01c7a490a)] - **test**: add script to create 0-dns-cert.pem (Shigeki Ohtsu) [#11579](https://github.com/nodejs/node/pull/11579)
- [[`4477e15217`](https://github.com/nodejs/node/commit/4477e15217)] - **test**: add regex in test_cyclic_link_protection (Clarence Dimitri CHARLES) [#11622](https://github.com/nodejs/node/pull/11622)
- [[`3d55cf06b1`](https://github.com/nodejs/node/commit/3d55cf06b1)] - **test**: add more WHATWG URL origin tests (Brian White) [#11691](https://github.com/nodejs/node/pull/11691)
- [[`a98d963082`](https://github.com/nodejs/node/commit/a98d963082)] - **test**: increase coverage of console (DavidCai) [#11653](https://github.com/nodejs/node/pull/11653)
- [[`1af0fa4b84`](https://github.com/nodejs/node/commit/1af0fa4b84)] - **test**: test buffer behavior when zeroFill undefined (Rich Trott) [#11706](https://github.com/nodejs/node/pull/11706)
- [[`1e52ba3b3d`](https://github.com/nodejs/node/commit/1e52ba3b3d)] - **test**: limit lint rule disabling in message test (Rich Trott) [#11724](https://github.com/nodejs/node/pull/11724)
- [[`5e7baa5a72`](https://github.com/nodejs/node/commit/5e7baa5a72)] - **tools**: add links to the stability index reference (Michael Cox) [#11664](https://github.com/nodejs/node/pull/11664)
- [[`c5874d1bd4`](https://github.com/nodejs/node/commit/c5874d1bd4)] - **url**: remove invalid file protocol check (Brian White) [#11691](https://github.com/nodejs/node/pull/11691)

Windows 32-bit Installer: https://nodejs.org/dist/v7.7.3/node-v7.7.3-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v7.7.3/node-v7.7.3-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v7.7.3/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v7.7.3/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v7.7.3/node-v7.7.3.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v7.7.3/node-v7.7.3-darwin-x64.tar.gz \
Linux 32-bit Binary: https://nodejs.org/dist/v7.7.3/node-v7.7.3-linux-x86.tar.xz \
Linux 64-bit Binary: https://nodejs.org/dist/v7.7.3/node-v7.7.3-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v7.7.3/node-v7.7.3-linux-ppc64le.tar.xz \
Linux PPC BE 64-bit Binary: https://nodejs.org/dist/v7.7.3/node-v7.7.3-linux-ppc64.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v7.7.3/node-v7.7.3-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v7.7.3/node-v7.7.3-aix-ppc64.tar.gz \
SmartOS 32-bit Binary: https://nodejs.org/dist/v7.7.3/node-v7.7.3-sunos-x86.tar.xz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v7.7.3/node-v7.7.3-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v7.7.3/node-v7.7.3-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v7.7.3/node-v7.7.3-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v7.7.3/node-v7.7.3-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v7.7.3/node-v7.7.3.tar.gz \
Other release files: https://nodejs.org/dist/v7.7.3/ \
Documentation: https://nodejs.org/docs/v7.7.3/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

c102f23a70ffd302bc6233c4dc33a1a230ad49046bd0b003c405a9a1a9bb06b8  node-v7.7.3-aix-ppc64.tar.gz
1128c62e99f3940e57e6d52ec14adad1213b1fb20110235a8d5b15bbdf15de95  node-v7.7.3-darwin-x64.tar.gz
b0697e635f85bc363073125b72d65686fa60fe8557450bacac11f149dc70f89c  node-v7.7.3-darwin-x64.tar.xz
23a5314e2f941ca77629922413c56b24dffb0dbb43529420b93dda40f7116c06  node-v7.7.3-headers.tar.gz
ccc084fba75d31b5435931c6306b157fcc7a29120005821d9c01f331d31a37ca  node-v7.7.3-headers.tar.xz
e4c65c664ff074a6ddd07ffc41ac0fe01b421a31164713d668ebb46519b04c88  node-v7.7.3-linux-arm64.tar.gz
217c172d0e4f3fcb5584316eb08e4b5c9aa15644b791a43a8da07e63b2dde3b9  node-v7.7.3-linux-arm64.tar.xz
b195d74e4efa3b3d28f79381b8e8fd6fd8dc9659cebf5a726c534535d60e09d7  node-v7.7.3-linux-armv6l.tar.gz
ae3038c0611d2469e73a070f494681c4444587ed71ef0ec17a4c9c2bfc9984d0  node-v7.7.3-linux-armv6l.tar.xz
db11e371edea1bc165773a6c8074af32d00b2bd30ce7af625c4177b5406c1230  node-v7.7.3-linux-armv7l.tar.gz
0df36a3920827aff7d7e2cce26471634c5e123c1104a6ef10165e72483f92eab  node-v7.7.3-linux-armv7l.tar.xz
84c10c9d65f386b7fb36c747df7364099ae648b1c6917d96fd2080c235a95928  node-v7.7.3-linux-ppc64le.tar.gz
f4e25968e9514b60c990e8e93a7228f35aeefe0bfb420077bf29f7ce692b64ad  node-v7.7.3-linux-ppc64le.tar.xz
bc840e3dd652faef8d37dd758922171edf18fc6b9d99c43684bc96e56084fc82  node-v7.7.3-linux-ppc64.tar.gz
759159b91f4cfcbf19ac52317b5b3100e137fed0570e4bfc590e5aabb625f880  node-v7.7.3-linux-ppc64.tar.xz
3f362933348f5fd7f3315a6d94262f01048fb361a4928371ecf2699ee37acbec  node-v7.7.3-linux-s390x.tar.gz
ef28085cc72d799e13dabb079f7730ac5836933233eb72cdcb53d6a9a90b27fc  node-v7.7.3-linux-s390x.tar.xz
e53409d3104eaa4a9129dce043b3c2f9c4dceb85ab0ca7ebeaf7ee1385abc875  node-v7.7.3-linux-x64.tar.gz
ff4a4d486fe8ce712735cd94b5e723451923c3763eb1170821f62b16c4d70594  node-v7.7.3-linux-x64.tar.xz
f93c9e1be556728884d480a8fa1393df57472d4202ceb5d52619536339147db5  node-v7.7.3-linux-x86.tar.gz
00c9d8d1a86e2130dac76d8e5bf5af2bbc840de1c29a41625f7d93ba0401658e  node-v7.7.3-linux-x86.tar.xz
95e1762c7d4297cac3c79c85cd689ca9fa52e059e1d19ec34d8557bb030e894e  node-v7.7.3.pkg
a991c2e6e698c19544270f30b12774095b8da4647a60727a67708f2b6c89b4fb  node-v7.7.3-sunos-x64.tar.gz
e6c9d6714a04a0f9eced32e1ae0dfe7a701740bc145cd90142170fe44c9aed0b  node-v7.7.3-sunos-x64.tar.xz
f0590ec3a2ffbf5181f40077c85065792b655c624fdd0e38119ebe727156d327  node-v7.7.3-sunos-x86.tar.gz
f8521223851fb03f8936f9eec9a3fdd2040fdbc7bd360cd86a6ec320b399e24a  node-v7.7.3-sunos-x86.tar.xz
81edeed9e3daa34a8962339357e2b9c5f7d9248e025ec872b6a55a519d3589b9  node-v7.7.3.tar.gz
5441daf11f743b5508ddf31a16b4f268835fbceb59ef709b44e85b03fece0edf  node-v7.7.3.tar.xz
ad3d505ed1eaabf12e21a8a8bdea44d1e99054a75d06a4d1f610d64fc10ef2cf  node-v7.7.3-win-x64.7z
5d24781262a84adca35ed7a854076cf357e8ba9f37e5bcc2849f678b538302c7  node-v7.7.3-win-x64.zip
dadc0de9bc5be5fbd5f951a09c029e222ff55709c5e2a68f6dc9d94f3198c155  node-v7.7.3-win-x86.7z
d2cd22cb85114dcd4f79d82837a56aed11bfaf776073cb30f724f254a57d5ea2  node-v7.7.3-win-x86.zip
755128b0edfc619b6655cb2a3dbe704504e8e32b775c63def6b0049b3e322ae7  node-v7.7.3-x64.msi
28a2b3cec0fd883ef203d5d1bfdc22f4e87b581145825c8cebaae635117849d3  node-v7.7.3-x86.msi
dac3da6cd18ea667826d4ab24b53914a8197343f2ef0bc323263eec95c76e5ed  win-x64/node.exe
03320b2d43cba111a97a2fdded3a347b83f7b5fd86653cdd43aa3cbc1a730835  win-x64/node.lib
2ac94d2aed85f0164b8dc833f4b091e9ccbf5c733d029a73c4758a4737686472  win-x64/node_pdb.7z
2e6298b092ad4a8f963d5d601a51e1abff762e1a655f5a3a485795cf53b2d706  win-x64/node_pdb.zip
8ff058cd8269243ba0a0cd210993e1383c4894d407e8a99b3175c4d3c0c7f166  win-x86/node.exe
a364ea069f9ba94dba3f58dc3c3370fdc9b25ce3f60e4dd4dcc8df5e44ea8a64  win-x86/node.lib
941d6ab99af057f0b2570dedc18321119fbec9697d59c042b43ab1db74cca603  win-x86/node_pdb.7z
877541601f0fb7ffc65c718c633db175e8bc03d24b2e1ae5cd3598cc45288869  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----
Version: GnuPG v2

iQIcBAEBCAAGBQJYyHGEAAoJECPv7+k8TP/+GmMQALP+mvW4b5TdTJZx7V4lGrv8
734KzNcOMqhjWgOzVw/wt9MsVCAHbF16PxFAhvlf3E+SnNLxCLEDg3YpbcblGpgt
BYRUB8EwNBjV7MZGDypuSV1fQJ/DuCSFW8t4B5lFMQ9y9FRLWVZR52iu1j6vCaQI
fzthq9lTrM2pEPlxBObcSQSro3VW+yASaig7qIqqC7Je1+YC+X9jglwycVnQ8J2V
XNm5ot7T8d4uMuNZkGHOiZWfSRiTJ0m8+3YQK0+7dHDm1w6mivOWtmZpCL8M7WDE
BMJ80eoud8DS8Cz7lq//Eiz4nFjkuynR4yHYGL3hGkUbEFPeFUvQU3HJDj77xugB
jx8emukzLJ+AbEOoZNuWsoFqx+hdY+lnrH+XohrIcnbLU+RL0rO47fzOE0SneqZx
z/Zk/zlzHuyMh8I61v0nXZoR47iKHmoEP6Ujrt2RazTXJPcb7G9Gf70ThqZbrDsz
tIDU212c/DBS/ypmXK7tJDPJCh+D8HxVIGgZipdF3+Ja1oWfr4bzx7/1KqqOblSA
ykNA24m012Pr/gpaUhnFkCFIo7uAixrExUESj4KRmNYh3kY0EzO2D98hXYwO5n9n
aJ4AqdRkOJMZWRbyQKn/BU0reEuiW3al1ToX0p/O2C+6ZtxJikbdzQqmZbUtGlqH
E+Xxr9aGzH6m+M7fS42f
=l3U1
-----END PGP SIGNATURE-----

```
