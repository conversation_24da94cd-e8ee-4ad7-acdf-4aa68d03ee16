---
date: '2019-10-23T10:38:39.992Z'
category: release
title: Node v13.0.1 (Current)
layout: blog-post
author: <PERSON><PERSON><PERSON>
---

### Notable Changes

- **deps**:
  - Fixed a bug in npm 6.12.0 where warnings are emitted on Node.js 13.x (<PERSON>) [#30079](https://github.com/nodejs/node/pull/30079).
- **esm**:
  - Changed file extension resolution order of `--es-module-specifier-resolution=node`
    to match that of the CommonJS loader (<PERSON><PERSON>) [#29974](https://github.com/nodejs/node/pull/29974).

### Commits

- [[`19a983c615`](https://github.com/nodejs/node/commit/19a983c615)] - **build**: make linter failures fail `test-doc` target (<PERSON>) [#30012](https://github.com/nodejs/node/pull/30012)
- [[`13f3d6c680`](https://github.com/nodejs/node/commit/13f3d6c680)] - **build**: log the found compiler version if too old (<PERSON>) [#30028](https://github.com/nodejs/node/pull/30028)
- [[`a25d2fcf8b`](https://github.com/nodejs/node/commit/a25d2fcf8b)] - **build**: make configure --without-snapshot a no-op (Micha<PERSON> Zasso) [#30021](https://github.com/nodejs/node/pull/30021)
- [[`e04d0584a5`](https://github.com/nodejs/node/commit/e04d0584a5)] - **build**: default Windows build to Visual Studio 2019 (Michaël Zasso) [#30022](https://github.com/nodejs/node/pull/30022)
- [[`ccf58835c7`](https://github.com/nodejs/node/commit/ccf58835c7)] - **build**: use python3 to build and test on Travis (Christian Clauss) [#29451](https://github.com/nodejs/node/pull/29451)
- [[`b92afcd90c`](https://github.com/nodejs/node/commit/b92afcd90c)] - **build**: fix version checks in configure.py (Michaël Zasso) [#29965](https://github.com/nodejs/node/pull/29965)
- [[`2dc4da0d8b`](https://github.com/nodejs/node/commit/2dc4da0d8b)] - **build**: build benchmark addons like test addons (Richard Lau) [#29995](https://github.com/nodejs/node/pull/29995)
- [[`2f36976594`](https://github.com/nodejs/node/commit/2f36976594)] - **deps**: npm: patch support for 13.x (Jordan Harband) [#30079](https://github.com/nodejs/node/pull/30079)
- [[`9d332ab4ce`](https://github.com/nodejs/node/commit/9d332ab4ce)] - **deps**: upgrade to libuv 1.33.1 (Colin Ihrig) [#29996](https://github.com/nodejs/node/pull/29996)
- [[`89b9115c4d`](https://github.com/nodejs/node/commit/89b9115c4d)] - **doc**: --enable-source-maps and prepareStackTrace are incompatible (Benjamin Coe) [#30046](https://github.com/nodejs/node/pull/30046)
- [[`35bffcdd9d`](https://github.com/nodejs/node/commit/35bffcdd9d)] - **doc**: join parts of disrupt section in cli.md (vsemozhetbyt) [#30038](https://github.com/nodejs/node/pull/30038)
- [[`0299767508`](https://github.com/nodejs/node/commit/0299767508)] - **doc**: update collaborator email address (Minwoo Jung) [#30007](https://github.com/nodejs/node/pull/30007)
- [[`ff4f2999e6`](https://github.com/nodejs/node/commit/ff4f2999e6)] - **doc**: fix tls version typo (akitsu-sanae) [#29984](https://github.com/nodejs/node/pull/29984)
- [[`62b4ca6e32`](https://github.com/nodejs/node/commit/62b4ca6e32)] - **doc**: clarify readable.unshift null/EOF (Robert Nagy) [#29950](https://github.com/nodejs/node/pull/29950)
- [[`dc83ff9056`](https://github.com/nodejs/node/commit/dc83ff9056)] - **doc**: remove unused Markdown reference links (Nick Schonning) [#29961](https://github.com/nodejs/node/pull/29961)
- [[`d80ece68ac`](https://github.com/nodejs/node/commit/d80ece68ac)] - **doc**: re-enable passing remark-lint rule (Nick Schonning) [#29961](https://github.com/nodejs/node/pull/29961)
- [[`828e171107`](https://github.com/nodejs/node/commit/828e171107)] - **doc**: add server header into the discarded list of http message.headers (Huachao Mao) [#29962](https://github.com/nodejs/node/pull/29962)
- [[`9729c5da8a`](https://github.com/nodejs/node/commit/9729c5da8a)] - **esm**: modify resolution order for specifier flag (Myles Borins) [#29974](https://github.com/nodejs/node/pull/29974)
- [[`cfd45ebf94`](https://github.com/nodejs/node/commit/cfd45ebf94)] - **module**: refactor modules bootstrap (Bradley Farias) [#29937](https://github.com/nodejs/node/pull/29937)
- [[`d561321e4a`](https://github.com/nodejs/node/commit/d561321e4a)] - **src**: remove unnecessary std::endl usage (Daniel Bevenius) [#30003](https://github.com/nodejs/node/pull/30003)
- [[`ed80c233cd`](https://github.com/nodejs/node/commit/ed80c233cd)] - **src**: make implementing CancelPendingDelayedTasks for platform optional (Anna Henningsen) [#30034](https://github.com/nodejs/node/pull/30034)
- [[`8fcc039de9`](https://github.com/nodejs/node/commit/8fcc039de9)] - **src**: expose ListNode\<T\>::prev\_ on postmortem metadata (legendecas) [#30027](https://github.com/nodejs/node/pull/30027)
- [[`0c88dc1932`](https://github.com/nodejs/node/commit/0c88dc1932)] - **src**: fewer uses of NODE_USE_V8_PLATFORM (Shelley Vohr) [#30029](https://github.com/nodejs/node/pull/30029)
- [[`972144073b`](https://github.com/nodejs/node/commit/972144073b)] - **src**: remove unused iomanip include (Daniel Bevenius) [#30004](https://github.com/nodejs/node/pull/30004)
- [[`b019ccd59d`](https://github.com/nodejs/node/commit/b019ccd59d)] - **src**: initialize openssl only once (Sam Roberts) [#29999](https://github.com/nodejs/node/pull/29999)
- [[`3eae670470`](https://github.com/nodejs/node/commit/3eae670470)] - **src**: refine maps parsing for large pages (Gabriel Schulhof) [#29973](https://github.com/nodejs/node/pull/29973)
- [[`f3712dfe83`](https://github.com/nodejs/node/commit/f3712dfe83)] - **stream**: simplify uint8ArrayToBuffer helper (Luigi Pinca) [#30041](https://github.com/nodejs/node/pull/30041)
- [[`46aa4810ad`](https://github.com/nodejs/node/commit/46aa4810ad)] - **stream**: remove dead code (Luigi Pinca) [#30041](https://github.com/nodejs/node/pull/30041)
- [[`f155dfeecb`](https://github.com/nodejs/node/commit/f155dfeecb)] - **test**: expand Worker test for non-shared ArrayBuffer (Anna Henningsen) [#30044](https://github.com/nodejs/node/pull/30044)
- [[`e110d81b17`](https://github.com/nodejs/node/commit/e110d81b17)] - **test**: fix test runner for Python 3 on Windows (Michaël Zasso) [#30023](https://github.com/nodejs/node/pull/30023)
- [[`c096f251e4`](https://github.com/nodejs/node/commit/c096f251e4)] - **test**: remove common.skipIfInspectorEnabled() (Rich Trott) [#29993](https://github.com/nodejs/node/pull/29993)
- [[`b1b8663a23`](https://github.com/nodejs/node/commit/b1b8663a23)] - **test**: add cb error test for fs.close() (Matteo Rossi) [#29970](https://github.com/nodejs/node/pull/29970)

Windows 32-bit Installer: https://nodejs.org/dist/v13.0.1/node-v13.0.1-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v13.0.1/node-v13.0.1-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v13.0.1/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v13.0.1/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v13.0.1/node-v13.0.1.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v13.0.1/node-v13.0.1-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v13.0.1/node-v13.0.1-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v13.0.1/node-v13.0.1-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v13.0.1/node-v13.0.1-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v13.0.1/node-v13.0.1-aix-ppc64.tar.gz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v13.0.1/node-v13.0.1-sunos-x64.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v13.0.1/node-v13.0.1-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v13.0.1/node-v13.0.1-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v13.0.1/node-v13.0.1.tar.gz \
Other release files: https://nodejs.org/dist/v13.0.1/ \
Documentation: https://nodejs.org/docs/v13.0.1/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

86d3a93abc03aeb43c9a56db56eac591276a66f0723cd3c32f20d427b21fb3b9  node-v13.0.1-aix-ppc64.tar.gz
25621359a51ff218ecf4bb2ffc657815154230a967224f22b722840a2b9ad061  node-v13.0.1-darwin-x64.tar.gz
82d778db08f354242d1114fb98670a0b03bd81d30c7007a12c78ddea931cbcd0  node-v13.0.1-darwin-x64.tar.xz
28b2076f9dc568add5e30773d756d14dfb28d5438f7f42cfebfb703b85f2f41f  node-v13.0.1-headers.tar.gz
0b7747a4789937f9c68d7379f5b4b9d446d3ddd2a8d45d09db03ea39abdf0555  node-v13.0.1-headers.tar.xz
437dc656d94e295d9200425b0d0dd000eed67fbc090334a5da51c77a8895b136  node-v13.0.1-linux-arm64.tar.gz
ad6e051aede160d8be5cbab9c60a155b06a5696e8088d895e6eff3dd36f9a688  node-v13.0.1-linux-arm64.tar.xz
1ac9b16adf01069170bf685dc0497d83d7f690492f83cc29a1c6a5950b914661  node-v13.0.1-linux-armv7l.tar.gz
8dcf2e450b922eebd3a9f1c2c2aeb8146c4c36383a55dae427e861c1c0391271  node-v13.0.1-linux-armv7l.tar.xz
f67cd2f3ea44370a5db5bc5b55e9c64397fdf09b85d038d7b16687b5fc3f04d4  node-v13.0.1-linux-ppc64le.tar.gz
cdeb316e8b6cefce06157d49d8df2e4ed58f07ed5b70f95cc7feb92432f5eacf  node-v13.0.1-linux-ppc64le.tar.xz
279a669766fac5bdd6f4a4feb1357f6090657fec9a86dd9e2c4a74784c0636da  node-v13.0.1-linux-s390x.tar.gz
9b77dd44dd89adf05918b666e57b68e9ead5c5fb6c20381fb23282a8206f2751  node-v13.0.1-linux-s390x.tar.xz
7476f43e45a896c95c5995c6f904aa5fb5d7347a25eaa95ce80043892b3926a4  node-v13.0.1-linux-x64.tar.gz
d5657c19bb30b267bf2e0f2b61f6a96d8955aa30b69240f22d3fd2c65e123cf7  node-v13.0.1-linux-x64.tar.xz
8e4b9930ac1a624bf6d35b12400a638e302e4cca7c47658e43fe562c6126459f  node-v13.0.1.pkg
c8b782672f14e7d13a7cd27983eb78364f88d476d98eddd939d7a9ec1d0aec00  node-v13.0.1-sunos-x64.tar.gz
072d11f994404fbb76330fd9ef379559bcd49fde9b5affad060491e2e031c04f  node-v13.0.1-sunos-x64.tar.xz
23566f3a97d917b4da7a04a72e7b39edbe01da0384ca7addba69736c14fab91c  node-v13.0.1.tar.gz
791b984cc896057f7b224da300184d408c8428c3b3f1401490e2b5dff60c3cd9  node-v13.0.1.tar.xz
129640d5c4e2bf6ae4ec2f1e1a4163e92adde783bf97cfffc07c004c04fb28dc  node-v13.0.1-win-x64.7z
1a2552b630651e08e9027339f71a65fbe3944540f0875563ea25639a091e1f33  node-v13.0.1-win-x64.zip
da8805e75192d4499f9dbfa91dd46c6db7d9cdd771242b03885a944f00dacb31  node-v13.0.1-win-x86.7z
11ac1bb58a22356900873a1593230bcb12354c9118755cb17d1485e2b32b8660  node-v13.0.1-win-x86.zip
46e19c21728428c7baba7306fe9d4eff079c50c1399c08d9621e310b37fae741  node-v13.0.1-x64.msi
1a8b0541c2bb655ab9d2ce8fb30e749d54a631d3a71cf3a16c0f5d000d1b1900  node-v13.0.1-x86.msi
2d310c2edddfd0b1959c04f471456145f4c163a46f6c397be30a1dbd243fca0f  win-x64/node.exe
bce42f4028350c041a49fe8caca403894ec70a8c5af633d25d143910bdd37a14  win-x64/node.lib
2d509f1137f90a101efd1076b68a838cdf7a39ad2e05cc739a068c1b77dc1bcc  win-x64/node_pdb.7z
77e45457e22a5699d0c315763f921a875d6e34a581456bf25f34d580612ff38e  win-x64/node_pdb.zip
03547b1b59cf31e67b52e4e2e8ad245dee4313ef6d735c2cf090b9a7eb6248c6  win-x86/node.exe
12043c2fc609bbb6f713098fe43d6b27c4e793f7b9162a08e651874cfbeeb653  win-x86/node.lib
64e5fcbda71ebdb601b782516c2c4fd50b6e173f3de36bd78259dd8b460b7cd8  win-x86/node_pdb.7z
f30e349fa65ba3294c4b8888233da4b5331d93355c7092f8f6382c72325a0534  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEj8yhP+8dDC6RAI4Jdw96mlrhVgAFAl2wLNsACgkQdw96mlrh
VgC6nBAAg0quOQijKz0A9k1mUDoFNSej1l20G+ficFi1iUqzk5qFcudXjrkbKTJy
Apitj2xlkimHax8aCdjXrUzfovBTuiUU7UcT35yCh51NPYxz7X9exYGlv/XPlKp7
kMndQ6Nq+1yVVphG5KoSv6wjsK865BmroqUKDwzpB9UGY08dV/IW9p6yi+DuaMiZ
yJUR0QpbqbPZp8NU9z9IeIuxIEP+m3bi+bSKFjNj//NfHySo+Pf2NT+x3Z+SsPdS
s3meLNWbWfctSCRmWYPyMKomtHzRFbvSU1RbP+8OFyN3OpgbFyqofsoswnzN8FS6
kg9CENJvjVFAQ8xpdj9dlhlpuHKTCTOl8dW+ymoQ2wIKprLK4W21eoDtL9YYS9bw
v45GJ+AYNwnIv0nb9ZUuHqQf15mXteQAqtLc8x8dWBCKTuzkUHh1+gzUNTJlEGOJ
H5QH3hABdI6bPD8JTcJymkDNtRONgML4tadAtdgkOj5BeBzhzyWXz78zB3+/zMSd
urnYsVEfBsKnT2RxgiiS0inP77nOhFC+uoqYT5KIab4vVP3/RTnPl48DpCQGHtxq
1ofYA/N5lG5RsuSlN9BfncPi9y/3Vvmz/HmYGRhyGcnGYFESNjDKNUf8PLFQc5b4
2apfKXocpFL9lxhMHL3Rl9RB0DbLgAAKNzVQZXB29osGKmE6zdY=
=XjeS
-----END PGP SIGNATURE-----

```
