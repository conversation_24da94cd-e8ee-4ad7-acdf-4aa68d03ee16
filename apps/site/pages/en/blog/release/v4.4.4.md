---
date: '2016-05-06T14:50:41.800Z'
category: release
title: Node v4.4.4 (LTS)
layout: blog-post
author: <PERSON><PERSON>
---

### Notable changes

- **deps**:
  - update openssl to 1.0.2h. (<PERSON><PERSON><PERSON>) [#6551](https://github.com/nodejs/node/pull/6551)
    - Please see our [blog post](/blog/vulnerability/openssl-may-2016/) for more info on the security contents of this release.

### Commits

- [[`f46952e727`](https://github.com/nodejs/node/commit/f46952e727)] - **buffer**: safeguard against accidental kN<PERSON><PERSON><PERSON>Fill (Сковорода Никита Андреевич) [nodejs/node-private#30](https://github.com/nodejs/node-private/pull/30)
- [[`4f1c82f995`](https://github.com/nodejs/node/commit/4f1c82f995)] - **streams**: support unlimited synchronous cork/uncork cycles (<PERSON>) [#6164](https://github.com/nodejs/node/pull/6164)
- [[`1efd96c767`](https://github.com/nodejs/node/commit/1efd96c767)] - **deps**: update openssl asm and asm_obsolete files (Shigeki Ohtsu) [#6551](https://github.com/nodejs/node/pull/6551)
- [[`c450f4a293`](https://github.com/nodejs/node/commit/c450f4a293)] - **deps**: add -no_rand_screen to openssl s_client (Shigeki Ohtsu) [nodejs/io.js#1836](https://github.com/nodejs/io.js/pull/1836)
- [[`baedfbae6a`](https://github.com/nodejs/node/commit/baedfbae6a)] - **openssl**: fix keypress requirement in apps on win32 (Shigeki Ohtsu) [iojs/io.js#1389](https://github.com/iojs/io.js/pull/1389)
- [[`ff3045e40b`](https://github.com/nodejs/node/commit/ff3045e40b)] - **deps**: fix asm build error of openssl in x86_win32 (Shigeki Ohtsu) [iojs/io.js#1389](https://github.com/iojs/io.js/pull/1389)
- [[`dc8dc97db3`](https://github.com/nodejs/node/commit/dc8dc97db3)] - **deps**: fix openssl assembly error on ia32 win32 (Fedor Indutny) [iojs/io.js#1389](https://github.com/iojs/io.js/pull/1389)
- [[`2dfeb01213`](https://github.com/nodejs/node/commit/2dfeb01213)] - **deps**: copy all openssl header files to include dir (Shigeki Ohtsu) [#6551](https://github.com/nodejs/node/pull/6551)
- [[`72f9952516`](https://github.com/nodejs/node/commit/72f9952516)] - **deps**: upgrade openssl sources to 1.0.2h (Shigeki Ohtsu) [#6551](https://github.com/nodejs/node/pull/6551)

Windows 32-bit Installer: https://nodejs.org/dist/v4.4.4/node-v4.4.4-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v4.4.4/node-v4.4.4-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v4.4.4/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v4.4.4/win-x64/node.exe \
Mac OS X 64-bit Installer: https://nodejs.org/dist/v4.4.4/node-v4.4.4.pkg \
Mac OS X 64-bit Binary: https://nodejs.org/dist/v4.4.4/node-v4.4.4-darwin-x64.tar.gz \
Linux 32-bit Binary: https://nodejs.org/dist/v4.4.4/node-v4.4.4-linux-x86.tar.xz \
Linux 64-bit Binary: https://nodejs.org/dist/v4.4.4/node-v4.4.4-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v4.4.4/node-v4.4.4-linux-ppc64le.tar.xz \
SmartOS 32-bit Binary: https://nodejs.org/dist/v4.4.4/node-v4.4.4-sunos-x86.tar.xz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v4.4.4/node-v4.4.4-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v4.4.4/node-v4.4.4-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v4.4.4/node-v4.4.4-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v4.4.4/node-v4.4.4-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v4.4.4/node-v4.4.4.tar.gz \
Other release files: https://nodejs.org/dist/v4.4.4/ \
Documentation: https://nodejs.org/docs/v4.4.4/api/

Shasums (GPG signing hash: SHA512, file hash: SHA256):

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA1

3d240db764391faca06df67db1eefd0b20989153edb0a99cbaf16e441e734a4d  node-v4.4.4-darwin-x64.tar.gz
e6a521781d212a760ef63ca139a12a86b8a312f730abc8deb0d8fa8f18d9f357  node-v4.4.4-darwin-x64.tar.xz
a5abc953d1ee4295cd3dcfdc116797e4b807e0b2d5c888aa31631fa1de9a9200  node-v4.4.4-headers.tar.gz
7a3521608274a32b5e609bf76581d6cec838ec369f1b346f73aca58588515fb0  node-v4.4.4-headers.tar.xz
4d7336411a61e92eb4815dc5b9042cae92ed49d3bc472da153aa13fd4e812b99  node-v4.4.4-linux-arm64.tar.gz
e50d47bdd5fe26a249449059244222e06f98513209027bf557bf809e24e17400  node-v4.4.4-linux-arm64.tar.xz
618efa1636db2d8acd7f28902200cbb42fd0f1d71ef482b6e99a027372584581  node-v4.4.4-linux-armv6l.tar.gz
c1a67dc4c7080b4082d165861d44a1f3279adec18279edc927329d153ab5b657  node-v4.4.4-linux-armv6l.tar.xz
64af655f048fc1ba70591076bbdeb61195347942ceef1ea18157467e5a01f253  node-v4.4.4-linux-armv7l.tar.gz
9df9d2bb7335a3118ed20a8332df40659294bfe04896c49d7e4890d7f667c495  node-v4.4.4-linux-armv7l.tar.xz
939cead87316367dd88430807ad335b9bdab4812fe0cd18adf62887c699dd4e7  node-v4.4.4-linux-ppc64le.tar.gz
23cedc2304c1709450689e5a3afe01b749dcb7a5b4f817bf59e97a64b22b60b3  node-v4.4.4-linux-ppc64le.tar.xz
0881eb010c8a4a0e746a1852fe48416d9c21b5f19a20d418cb02c7197fa55576  node-v4.4.4-linux-x64.tar.gz
c8b4e3c6e07e51593dddbf1d2ec3cf0ec09d5c6b8c5258b37b3816cc6b7e9fe3  node-v4.4.4-linux-x64.tar.xz
401fcef7908b3620bd28300bac06ad4fe774c95e10167c8617dde18befd78fcd  node-v4.4.4-linux-x86.tar.gz
7c0771b0153dd1c6609197b7b56840085df5d0752862fe4e98569d8d7bcb8682  node-v4.4.4-linux-x86.tar.xz
0e26458c8490af8f7d1aa4db8ca2792e2089fc0065517225cb30d4f22778b288  node-v4.4.4.pkg
5748ececfb65a5dfa5c7eb87c31e890aff7a32aeea6ae440e382eca34ec16eba  node-v4.4.4-sunos-x64.tar.gz
a2aea1b73dc559db924b4a00884f1ae26c5bea84956e506c7a1b7fbe04b36f5a  node-v4.4.4-sunos-x64.tar.xz
1da8b38983ba9fd8878c2127225396991c8189934e578760f0860ed15302cdd6  node-v4.4.4-sunos-x86.tar.gz
b542392497c7bd03d83cba378f0056b9d1e2c2feb71ba64196ab64a2a8ae67ba  node-v4.4.4-sunos-x86.tar.xz
53c694c203ee18e7cd393612be08c61ed6ab8b2a165260984a99c014d1741414  node-v4.4.4.tar.gz
2c9cedc401145a6648877ebd3fa9090c874dfced8bf7a6e7d9d8e9c21827b114  node-v4.4.4.tar.xz
3bfa87c9feb0107800740fd0a7f950b9259ba73a353274ba60a91d7b4a5279c5  node-v4.4.4-x64.msi
2198827fcc8ee3df77ec7551107409cf2eea02cdd98fed3388242e9f493d8467  node-v4.4.4-x86.msi
7b9884e09881ab6e3ef942f641e766c74ec33dac20c08914aa8778508d4c116b  win-x64/node.exe
a1736c89bf3510dab521fac288228ab3464eca1ee2d5449ff60de2ca6d17a38f  win-x64/node.lib
02565551c3645760e6c466190a94badf8755f4ad789e61e8e40f40c3483590fc  win-x86/node.exe
77bf8aa73cdf0d45b99a7844842e58ca6c4756531e759ea6581d92be8371c139  win-x86/node.lib
-----BEGIN PGP SIGNATURE-----

iQEcBAEBAgAGBQJXLK8GAAoJEJM7AfQLXKlGVKUH/jYIvgLWqaQmpYJbgAupUmuo
Av7DB+iKGWFK0Tn2vmeQsWAK7XzIaXHFimldQ8x6lwt5hjH5L4MjA8wQ6zblgT7S
FK/VHWBHd+7T8eTE6D+42g7Umo4lL2D8PbB/ePKuDYYNtzFqllE0ThX+IkNnMRJs
X8xqmzXa7EvEQMiOGzQF0B2gWqovW6m7d0Skm72szT0ldayUUBwQ9z1zE1RVGmlc
lNMAMinZhOdbtCQChOTYRu6dhjdf/MOwHMMrG2AHiasjgKvrMjHBx9kbP/DOrtM4
zIarizzJGPoTU0JEb63JE5OmeR5q1vhblnT/tTo0VB1oeaC7T72vZqxvvyYvekU=
=RKEg
-----END PGP SIGNATURE-----

```
