---
date: '2017-12-08T16:14:01.501Z'
category: release
title: Node v4.8.7 (Maintenance)
layout: blog-post
author: <PERSON><PERSON>
---

### Notable Changes

- **deps**:
  - openssl updated to 1.0.2n (<PERSON><PERSON><PERSON>) [#17526](https://github.com/nodejs/node/pull/17526)

### Commits

- [[`4f8fae3493`](https://github.com/nodejs/node/commit/4f8fae3493)] - **deps**: update openssl asm and asm_obsolete files (Shige<PERSON>) [#17526](https://github.com/nodejs/node/pull/17526)
- [[`eacd090e7b`](https://github.com/nodejs/node/commit/eacd090e7b)] - **deps**: add -no_rand_screen to openssl s_client (Shige<PERSON>) [nodejs/io.js#1836](https://github.com/nodejs/io.js/pull/1836)
- [[`3e6b0b0d13`](https://github.com/nodejs/node/commit/3e6b0b0d13)] - **deps**: fix asm build error of openssl in x86_win32 (Shigeki Ohtsu) [iojs/io.js#1389](https://github.com/iojs/io.js/pull/1389)
- [[`b0ed4c52af`](https://github.com/nodejs/node/commit/b0ed4c52af)] - **deps**: fix openssl assembly error on ia32 win32 (Fedor Indutny) [iojs/io.js#1389](https://github.com/iojs/io.js/pull/1389)
- [[`dd6a2dff1e`](https://github.com/nodejs/node/commit/dd6a2dff1e)] - **deps**: copy all openssl header files to include dir (Shigeki Ohtsu) [#17526](https://github.com/nodejs/node/pull/17526)
- [[`b3afedfbe9`](https://github.com/nodejs/node/commit/b3afedfbe9)] - **deps**: upgrade openssl sources to 1.0.2n (Shigeki Ohtsu) [#17526](https://github.com/nodejs/node/pull/17526)
- [[`f7eb162d0d`](https://github.com/nodejs/node/commit/f7eb162d0d)] - **openssl**: fix keypress requirement in apps on win32 (Shigeki Ohtsu) [iojs/io.js#1389](https://github.com/iojs/io.js/pull/1389)

Windows 32-bit Installer: https://nodejs.org/dist/v4.8.7/node-v4.8.7-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v4.8.7/node-v4.8.7-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v4.8.7/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v4.8.7/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v4.8.7/node-v4.8.7.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v4.8.7/node-v4.8.7-darwin-x64.tar.gz \
Linux 32-bit Binary: https://nodejs.org/dist/v4.8.7/node-v4.8.7-linux-x86.tar.xz \
Linux 64-bit Binary: https://nodejs.org/dist/v4.8.7/node-v4.8.7-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v4.8.7/node-v4.8.7-linux-ppc64le.tar.xz \
Linux PPC BE 64-bit Binary: https://nodejs.org/dist/v4.8.7/node-v4.8.7-linux-ppc64.tar.xz \
SmartOS 32-bit Binary: https://nodejs.org/dist/v4.8.7/node-v4.8.7-sunos-x86.tar.xz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v4.8.7/node-v4.8.7-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v4.8.7/node-v4.8.7-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v4.8.7/node-v4.8.7-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v4.8.7/node-v4.8.7-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v4.8.7/node-v4.8.7.tar.gz \
Other release files: https://nodejs.org/dist/v4.8.7/ \
Documentation: https://nodejs.org/docs/v4.8.7/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

c8c1efb41d84c81a8f97791dc8179a03d0f90cd32f2fd8471d9088be74cbffb6  node-v4.8.7-darwin-x64.tar.gz
c9f2ab943b5d715feb54ef2f1b8878278d571144f875f16085fe18973bfe7c9d  node-v4.8.7-darwin-x64.tar.xz
071e9c06270bcc8f7a7b1727840d01e97bde6feaf78459298095888eef078334  node-v4.8.7-headers.tar.gz
e5ed54d5ab8d5859c2e59860484fe130cb0b82c8d2a8218c5424402d2327c0d6  node-v4.8.7-headers.tar.xz
2a81bc51d0973c22fe04276cc965dddd162a7e1287b9a1c59306be9c24f37c0f  node-v4.8.7-linux-arm64.tar.gz
50d464564eb210b1514ad5e17158ce96e0e7680a4489997413cfb26cf24d3ad2  node-v4.8.7-linux-arm64.tar.xz
6c1ca16d34af3654c94052c65460c6d2e929627c733d5cb4c76400951d20014a  node-v4.8.7-linux-armv6l.tar.gz
016aa391a649f8285ab530d3bdd7eac6291f227b80415b163b8d59c1741ff4c7  node-v4.8.7-linux-armv6l.tar.xz
a106cc2bc30ebbcc33d89abe305447760e8bfdaaf8dc468805daa8d68d335d32  node-v4.8.7-linux-armv7l.tar.gz
01541a5a1eb480ef10441a09cef4e33a125cb9111443cc6fe611afa446ea89cb  node-v4.8.7-linux-armv7l.tar.xz
1873ab2eda9c3696c749fbefbdd10ba14e96c6cec8b6ca853b8557f41292bc6e  node-v4.8.7-linux-ppc64le.tar.gz
36af9933b3401d61bfd83ca892275c47f1753471e7cc8233b84af94805301a23  node-v4.8.7-linux-ppc64le.tar.xz
a6732f30732d577eaf2439ac50c10b34c883504497caf2eeb9b8434254987923  node-v4.8.7-linux-ppc64.tar.gz
5b137309311e799318a7b971301ebffe4f5119a68ef3f9f1bc8f7f0a2fc01a26  node-v4.8.7-linux-ppc64.tar.xz
d3a1501a426a8d8bec3fa4ddd7364a5d160d50237527fbfce5e76a97b55d3f75  node-v4.8.7-linux-x64.tar.gz
36250dc02ee8d4006a217394a32b72f3dc51682d2e862bc33c3388eb1c8faf67  node-v4.8.7-linux-x64.tar.xz
d68a0237077e37f65283458228d932e2e28cc03404fef10222f498cee839ca58  node-v4.8.7-linux-x86.tar.gz
e58f863ad8d36ac1927b96553f52d69058d702bb3ecdf238d304215918430f71  node-v4.8.7-linux-x86.tar.xz
bbcda4c0bd89f25ed59a08b025e7b7358997de36cb38033836948a00788f4858  node-v4.8.7.pkg
8dd754801aafa157386573d3f1ce2fca400534ef38d78bc7e7dec84c8d288c2f  node-v4.8.7-sunos-x64.tar.gz
70564fcb776cf3711e34316d2537a7c4a6312b5a1ffaded12b6916774bbbcbe2  node-v4.8.7-sunos-x64.tar.xz
f7be90897fe88e820235ce40864e7327a0909eac4b4fc9e4270a9df5ed7f1380  node-v4.8.7-sunos-x86.tar.gz
af5058f03664fd935b77ae8c5d47d3d6f1cfa30ae42a370d94f9b78d8acda35a  node-v4.8.7-sunos-x86.tar.xz
1aa461ad787cd53952ca9e32266fa48c9cf8223c87c802af3bce5f55ed3db39e  node-v4.8.7.tar.gz
03479a8ce6affedde75d80a6c8c351a7afb5a85b8d7e5119ab6f349100e641f8  node-v4.8.7.tar.xz
f02df5512214e9e45a1b656c93373ed43bcf6482bec8488214d3532f88baae77  node-v4.8.7-win-x64.7z
89b98f4cc7a2c1011e1a19ec1d1c7c8d26e29ad9c1b4441196ece6b1e398235a  node-v4.8.7-win-x64.zip
49cf944931bb5f33fa01a22f6d5632ee60eead51b2711b58228e206d82d2cfbb  node-v4.8.7-win-x86.7z
02230b030f8bee1828000cb64975a528cd3fe7a74fe0af5463004b8a66a1fce0  node-v4.8.7-win-x86.zip
244d272c87ecef9343a4b235ee08d1c3206bb2f18e09464b5cdad57bf1e6eb54  node-v4.8.7-x64.msi
db1f61e59444fd1178b8a12bcc15bb7d2f935d447b9d50b7a18ae9eb6c1a573a  node-v4.8.7-x86.msi
d10f9d952be33a41cee5c28eaa0f502f567c9371c8c0a8377b2e89b9d546dfa2  win-x64/node.exe
72af2b357cf6e0d1f2da6305c4aa62c64a925703e994948ed2d7b1e310ca2e87  win-x64/node.lib
75a2ce065eba63ca5c3341bb04e8494c89c7ae640f890191c4e67bf69685e798  win-x64/node_pdb.7z
6ec456267aa0b244932edab29a0a935f1aca3a28f063e9a592ace07a994b8bdc  win-x64/node_pdb.zip
07b1ae68b16d720b757c3b6c982f459d64024c874e9042830f9598cd31a1d01b  win-x86/node.exe
dc8831e8bf047b0b647ef5e4a56b3c80c72e8f2c209d99391aed0076941d473d  win-x86/node.lib
cb17beaf0c6ff2a70a2961d36f802840a8c9dd527f01246029ba4de46f654e48  win-x86/node_pdb.7z
72572b560d86e4a3aa9c8af42d71fe2534bcfc0863137d9fa2099feb29e8029c  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQEzBAEBCAAdFiEEDv/hvO/ZyE49CYFSkzsB9AtcqUYFAloqt14ACgkQkzsB9Atc
qUZWKgf/bxo9cO2avrWx1apTrmRAaAMKmjc2LY1zkgBhIuWvcx1NJaW3SSYvi1KH
uJ/nQm4eWyOdvu8f51X854X0tLfRk2M0BUnQ/MjgBoxTm+jJ+y+C3J8WlU+MCLza
3FMycM1WVwm71YmlErO2PSG9FYWwWtkaQhTRgJN3PWHvpopwp/Z1pvAA+RK7JM1B
Bnreg7cLJGA3fSYUTKn3x8b+IBrbhFU1v9KbQ2gDI8sZCxLm63TRNn2vqAxNRTW/
RUesocH5MtrcfQb3TLcrANARmUNsgalk9zYYjbILTZNkk13Z1eEMEcsZ5mzvmN9Y
AKl6cPEig0aBZMzJH6n1IJojDM1AtA==
=eYzj
-----END PGP SIGNATURE-----

```
