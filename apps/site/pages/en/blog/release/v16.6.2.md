---
date: '2021-08-11T16:42:12.098Z'
category: release
title: Node v16.6.2 (Current)
layout: blog-post
author: <PERSON>
---

### Notable Changes

- **CVE-2021-3672/CVE-2021-22931**: Improper handling of untypical characters in domain names (High)
  - Node.js was vulnerable to Remote Code Execution, XSS, application crashes due to missing input validation of hostnames returned by Domain Name Servers in the Node.js DNS library which can lead to the output of wrong hostnames (leading to Domain Hijacking) and injection vulnerabilities in applications using the library. You can read more about it at https://nvd.nist.gov/vuln/detail/CVE-2021-22931.
- **CVE-2021-22940**: Use after free on close http2 on stream canceling (High)
  - Node.js was vulnerable to a use after free attack where an attacker might be able to exploit memory corruption to change process behavior. This release includes a follow-up fix for CVE-2021-22930 as the issue was not completely resolved by the previous fix. You can read more about it at https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2021-22940.
- **CVE-2021-22939**: Incomplete validation of rejectUnauthorized parameter (Low)
  - If the Node.js HTTPS API was used incorrectly and "undefined" was in passed for the "rejectUnauthorized" parameter, no error was returned and connections to servers with an expired certificate would have been accepted. You can read more about it at https://nvd.nist.gov/vuln/detail/CVE-2021-22939.

### Commits

- [[`054537cdc2`](https://github.com/nodejs/node/commit/054537cdc2)] - **deps**: update c-ares to 1.17.2 (Beth Griggs) [#39724](https://github.com/nodejs/node/pull/39724)
- [[`ac544905b6`](https://github.com/nodejs/node/commit/ac544905b6)] - **deps**: reflect c-ares source tree (Beth Griggs) [#39653](https://github.com/nodejs/node/pull/39653)
- [[`a914b23cbc`](https://github.com/nodejs/node/commit/a914b23cbc)] - **deps**: apply missed updates from c-ares 1.17.1 (Beth Griggs) [#39653](https://github.com/nodejs/node/pull/39653)
- [[`31d5773654`](https://github.com/nodejs/node/commit/31d5773654)] - **http2**: add tests for cancel event while client is paused reading (Akshay K) [#39622](https://github.com/nodejs/node/pull/39622)
- [[`a3c33d4ce7`](https://github.com/nodejs/node/commit/a3c33d4ce7)] - **http2**: update handling of rst_stream with error code NGHTTP2_CANCEL (Akshay K) [#39622](https://github.com/nodejs/node/pull/39622)
- [[`6c7fff6f1d`](https://github.com/nodejs/node/commit/6c7fff6f1d)] - **tls**: validate "rejectUnauthorized: undefined" (Matteo Collina) [nodejs-private/node-private#276](https://github.com/nodejs-private/node-private/pull/276)

Windows 32-bit Installer: https://nodejs.org/dist/v16.6.2/node-v16.6.2-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v16.6.2/node-v16.6.2-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v16.6.2/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v16.6.2/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v16.6.2/node-v16.6.2.pkg \
macOS Apple Silicon 64-bit Binary: https://nodejs.org/dist/v16.6.2/node-v16.6.2-darwin-arm64.tar.gz \
macOS Intel 64-bit Binary: https://nodejs.org/dist/v16.6.2/node-v16.6.2-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v16.6.2/node-v16.6.2-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v16.6.2/node-v16.6.2-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v16.6.2/node-v16.6.2-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v16.6.2/node-v16.6.2-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v16.6.2/node-v16.6.2-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v16.6.2/node-v16.6.2-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v16.6.2/node-v16.6.2.tar.gz \
Other release files: https://nodejs.org/dist/v16.6.2/ \
Documentation: https://nodejs.org/docs/v16.6.2/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

2a51635501451a88f6addc192a79b6d36cc40dcf3a198a54037ab26fa6305043  node-v16.6.2-aix-ppc64.tar.gz
29e46e83f6837ff1c815c49f590c25fa51b0811a6590c62120a9d464ba431fc6  node-v16.6.2-darwin-arm64.tar.gz
befbfdec7c2118689544ef596e990aae2fcd1227707c6a8475056be14ce2ee8d  node-v16.6.2-darwin-arm64.tar.xz
74e95aca0ea88ed2d9270dccc1e3e62500912be5fef1528bb11f178c468f312c  node-v16.6.2-darwin-x64.tar.gz
21c9417c38d9bee140c659f7cf11806ec866af3f7053bd17ec45757a902c9956  node-v16.6.2-darwin-x64.tar.xz
7764da71e22d57746d65eb408dc80cbd7f6eed7f38b558684fa60571d1c69b26  node-v16.6.2-headers.tar.gz
e4bf9c8db91d149d9f3cebd79621571079ffb9d92dca10e7d260120ff99b428e  node-v16.6.2-headers.tar.xz
c51a94f28a29c390d20445d9b334a9808d3166bd244ebc03852d23c0b17a93ca  node-v16.6.2-linux-arm64.tar.gz
d885ffcef367a010e2b21a283ec96721e92b29f222de5d943bc7188e48f30349  node-v16.6.2-linux-arm64.tar.xz
1ba5287c941cef2da53c0d80db7db7124971b1c933f222ca7f2eb833e1817f35  node-v16.6.2-linux-armv7l.tar.gz
9756e763910ab7277346307fb5c4d34370ab3bb7d957129f58a65bf69f2af93b  node-v16.6.2-linux-armv7l.tar.xz
a966ea0d258c0e4a2c23b77f49f85bd7a4a4ff674fcd0d625a7fa48370d14d15  node-v16.6.2-linux-ppc64le.tar.gz
c2ee7961f1f217b0cc57a15efc3372b0495f6e1775a3e7f50b153b9db7c46be9  node-v16.6.2-linux-ppc64le.tar.xz
31e27413ff29607af54fcf842105a5290f2556add1b009b8e28240f96f742638  node-v16.6.2-linux-s390x.tar.gz
c3d6b4f7bf055f257abc07862743b614bdba00fd096a863eaadc700ae0939c98  node-v16.6.2-linux-s390x.tar.xz
913913f62416b96dee5f463b54e1adebaf669dd2ff3b047d6290deadc3003d97  node-v16.6.2-linux-x64.tar.gz
90c88cf6ca06dcd6d20c2b6dba5ff84d1f831446c25ef650f86e86bb94239353  node-v16.6.2-linux-x64.tar.xz
5bb14dafbac87efb74d3e050a90de68eb407ecadd52f12a1b4e937ec59884792  node-v16.6.2.pkg
e8df4a0084c379a37c11b315b7d068760a38598119d7ca9262977adcbbb58933  node-v16.6.2.tar.gz
8794cba1f971e4200a38690c76d7cc0a3bd1cba96fbf4305dfbe21fc459d79eb  node-v16.6.2.tar.xz
152e36fe0493f37d3be939c7f9c3a975c9f39a3346d66787b59e2db28ed2eeb6  node-v16.6.2-win-x64.7z
e7e05eb133fce48b76b4db6714d80aea90872afec176599585bc1aa457fb41b9  node-v16.6.2-win-x64.zip
b40c0f3bf401ff56c558de3a24b33101273c622e664e1e5df4d08444aa4ae7df  node-v16.6.2-win-x86.7z
b7324b70ed37e14878cde39cd69099368513068495b25d97f1423591c0206685  node-v16.6.2-win-x86.zip
6cb05e722749c98cc9d0d1b2ef0c3a4c5c05da83a00b4ef04cee0bd4a3cfbbc7  node-v16.6.2-x64.msi
22b21336d6ae8d16a1e45d38bd198831aa27ddf8a61d52831cfbd3ba5d2866e0  node-v16.6.2-x86.msi
9c99a5255dabc044bd262df07ec8e6ba3351e38d003121ed8739906bf5f0eb42  win-x64/node.exe
e7484e4552df7e992e5d409e518aa467555769dc85917da408d41f85c4f2823d  win-x64/node.lib
a44ba1e9c42f84d80a9c29871a22c687e7f35ae358a898813d50b02866b8d6e9  win-x64/node_pdb.7z
bfdfedc1829855dcb1545661963dcb98d72ea517a4be3926a8c35f5120a72637  win-x64/node_pdb.zip
5dc410d110cb86a0e9fd58f30b5c1208915e733e3ab222e71fbdb0aa1a3755f3  win-x86/node.exe
7821ab6af3cef68eee042ef6b7fb2185eafff8bd4e4c988c8fc6c1d0242d2bcd  win-x86/node.lib
cee7fe2c53d99e9c7bca0e7485a35c49d0f01ad131a790199cdb68e1383b946d  win-x86/node_pdb.7z
32f1c9673025980c1563bf173c02bf3ae81a0a8c0e30673a3e906b3adff0519a  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQEzBAEBCAAdFiEETtd49TnjY0x3nIfG1wYoSKGrAFwFAmET+3QACgkQ1wYoSKGr
AFxCGAgAs2V2hTNMfXb7C82V45tyi5tg2CIWtv98Ewc5py0tuVPbWmUO8enPP0AO
u4VAidwH3q4FT1xlVakC3QQL9qf13yi5fdLyHxfToCu2TP4U1avV8CWymdG/gPYz
wn6IBurWn/a3RRnva+euUEUubSAvRk5QrujAgkmcuAkXXQAo1pjbKjeKJ2NLnVeC
9dUISRz7XtmYEmAO8Fyszv/7JbZuNthd/VITDY9taWfN46nlcYwLSA54vqYj5nG9
AJoCKd5t/T5CMGOAc1/rtytFrXVBiCcSQLilFFy7In0oVQeT3EvtQmQENfdbwr3r
uCe9lxACJp/+lq2VZXlKFQpkzDxong==
=6UuH
-----END PGP SIGNATURE-----

```
