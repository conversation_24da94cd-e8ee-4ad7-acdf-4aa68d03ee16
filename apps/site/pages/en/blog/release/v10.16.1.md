---
date: '2019-07-31T19:16:37.632Z'
category: release
title: Node v10.16.1 (LTS)
layout: blog-post
author: <PERSON>
---

### Notable changes

- **deps**: upgrade openssl sources to 1.1.1c (<PERSON>) [#28212](https://github.com/nodejs/node/pull/28212)
- **stream**: do not unconditionally call `_read()` on `resume()` (<PERSON>) [#26965](https://github.com/nodejs/node/pull/26965)
- **worker**: fix nullptr deref after MessagePort deser failure (<PERSON>) [#25076](https://github.com/nodejs/node/pull/25076)

### Commits

- [[`65ef26fdcb`](https://github.com/nodejs/node/commit/65ef26fdcb)] - **async_hooks**: avoid double-destroy HTTPParser (<PERSON>) [#27477](https://github.com/nodejs/node/pull/27477)
- [[`8f5d6cf5f5`](https://github.com/nodejs/node/commit/8f5d6cf5f5)] - **deps**: update archs files for OpenSSL-1.1.1c (Sam <PERSON>) [#28212](https://github.com/nodejs/node/pull/28212)
- [[`9e62852724`](https://github.com/nodejs/node/commit/9e62852724)] - **deps**: upgrade openssl sources to 1.1.1c (Sam Roberts) [#28212](https://github.com/nodejs/node/pull/28212)
- [[`c59e0c256d`](https://github.com/nodejs/node/commit/c59e0c256d)] - **deps**: updated openssl upgrade instructions (Sam Roberts) [#28212](https://github.com/nodejs/node/pull/28212)
- [[`609d2b9ea4`](https://github.com/nodejs/node/commit/609d2b9ea4)] - **deps**: V8: backport f27ac28 (Michaël Zasso) [#28061](https://github.com/nodejs/node/pull/28061)
- [[`8f780e8f99`](https://github.com/nodejs/node/commit/8f780e8f99)] - **deps**: cherry-pick 88f8fe1 from upstream V8 (Yang Guo) [#24514](https://github.com/nodejs/node/pull/24514)
- [[`ad588eb5fc`](https://github.com/nodejs/node/commit/ad588eb5fc)] - **doc**: adjust TOC margins (Roman Reiss) [#28075](https://github.com/nodejs/node/pull/28075)
- [[`b3d8a1b1d0`](https://github.com/nodejs/node/commit/b3d8a1b1d0)] - **doc**: add missing changes entry (Ruben Bridgewater) [#24758](https://github.com/nodejs/node/pull/24758)
- [[`819a647d8f`](https://github.com/nodejs/node/commit/819a647d8f)] - **esm**: fix esm load bug (ZYSzys) [#25491](https://github.com/nodejs/node/pull/25491)
- [[`f34bb968c4`](https://github.com/nodejs/node/commit/f34bb968c4)] - **process**: make stdout and stderr emit 'close' on destroy (Matteo Collina) [#26691](https://github.com/nodejs/node/pull/26691)
- [[`0339fba1bb`](https://github.com/nodejs/node/commit/0339fba1bb)] - **src**: handle empty Maybe in uv binding initialize (Anna Henningsen) [#25079](https://github.com/nodejs/node/pull/25079)
- [[`f9e8e8856a`](https://github.com/nodejs/node/commit/f9e8e8856a)] - **src**: fix Get() usage in tls_wrap.cc (cjihrig) [#24060](https://github.com/nodejs/node/pull/24060)
- [[`b689008dea`](https://github.com/nodejs/node/commit/b689008dea)] - **src**: in-source comments and minor TLS cleanups (Sam Roberts) [#25713](https://github.com/nodejs/node/pull/25713)
- [[`76af23a32b`](https://github.com/nodejs/node/commit/76af23a32b)] - **src**: remove internalBinding('config').warningFile (Joyee Cheung) [#24959](https://github.com/nodejs/node/pull/24959)
- [[`b7dbc1c537`](https://github.com/nodejs/node/commit/b7dbc1c537)] - **src**: fix warning in cares_wrap.cc (cjihrig) [#25230](https://github.com/nodejs/node/pull/25230)
- [[`a8f78f02cb`](https://github.com/nodejs/node/commit/a8f78f02cb)] - **src**: fulfill Maybe contract in InlineDecoder (Anna Henningsen) [#25140](https://github.com/nodejs/node/pull/25140)
- [[`0dee607409`](https://github.com/nodejs/node/commit/0dee607409)] - **src**: extract common Bind method (Jon Moss) [#22315](https://github.com/nodejs/node/pull/22315)
- [[`08a32fbf57`](https://github.com/nodejs/node/commit/08a32fbf57)] - **src**: elevate v8 namespaces for node_process.cc (Jayasankar) [#24578](https://github.com/nodejs/node/pull/24578)
- [[`f3841c6750`](https://github.com/nodejs/node/commit/f3841c6750)] - **stream**: convert existing buffer when calling .setEncoding (Anna Henningsen) [#27936](https://github.com/nodejs/node/pull/27936)
- [[`274b97c4ea`](https://github.com/nodejs/node/commit/274b97c4ea)] - **stream**: do not unconditionally call `_read()` on `resume()` (Anna Henningsen) [#26965](https://github.com/nodejs/node/pull/26965)
- [[`044e753aaf`](https://github.com/nodejs/node/commit/044e753aaf)] - **stream**: make \_read() be called indefinitely if the user wants so (Matteo Collina) [#26135](https://github.com/nodejs/node/pull/26135)
- [[`f332265cda`](https://github.com/nodejs/node/commit/f332265cda)] - **test**: remove `util.inherits()` usage (ZYSzys) [#25245](https://github.com/nodejs/node/pull/25245)
- [[`ada0ed55d1`](https://github.com/nodejs/node/commit/ada0ed55d1)] - **test**: fix pty test hangs on aix (Ben Noordhuis) [#28600](https://github.com/nodejs/node/pull/28600)
- [[`2ae99160e5`](https://github.com/nodejs/node/commit/2ae99160e5)] - **test**: skip stringbytes-external-exceed-max on AIX (Sam Roberts) [#28516](https://github.com/nodejs/node/pull/28516)
- [[`39637cb95f`](https://github.com/nodejs/node/commit/39637cb95f)] - **test**: skip tests related to CI failures on AIX (Sam Roberts) [#28469](https://github.com/nodejs/node/pull/28469)
- [[`35be08a16f`](https://github.com/nodejs/node/commit/35be08a16f)] - **test**: clean up build files (Gabriel Schulhof) [#28297](https://github.com/nodejs/node/pull/28297)
- [[`cc3ca08046`](https://github.com/nodejs/node/commit/cc3ca08046)] - **test**: clearing require cache crashes esm loader (Antoine du HAMEL) [#25491](https://github.com/nodejs/node/pull/25491)
- [[`75052cadaa`](https://github.com/nodejs/node/commit/75052cadaa)] - **tls**: add debugging to native TLS code (Anna Henningsen) [#26843](https://github.com/nodejs/node/pull/26843)
- [[`99dad28ebf`](https://github.com/nodejs/node/commit/99dad28ebf)] - **tls**: add CHECK for impossible condition (Anna Henningsen) [#26843](https://github.com/nodejs/node/pull/26843)
- [[`5ffe04753e`](https://github.com/nodejs/node/commit/5ffe04753e)] - **tls**: renegotiate should take care of its own state (Sam Roberts) [#25997](https://github.com/nodejs/node/pull/25997)
- [[`4a607fab49`](https://github.com/nodejs/node/commit/4a607fab49)] - **tools**: replace rollup with ncc (Rich Trott) [#24813](https://github.com/nodejs/node/pull/24813)
- [[`14090b59fc`](https://github.com/nodejs/node/commit/14090b59fc)] - **worker**: fix nullptr deref after MessagePort deser failure (Anna Henningsen) [#25076](https://github.com/nodejs/node/pull/25076)

Windows 32-bit Installer: https://nodejs.org/dist/v10.16.1/node-v10.16.1-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v10.16.1/node-v10.16.1-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v10.16.1/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v10.16.1/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v10.16.1/node-v10.16.1.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v10.16.1/node-v10.16.1-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v10.16.1/node-v10.16.1-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v10.16.1/node-v10.16.1-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v10.16.1/node-v10.16.1-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v10.16.1/node-v10.16.1-aix-ppc64.tar.gz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v10.16.1/node-v10.16.1-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v10.16.1/node-v10.16.1-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v10.16.1/node-v10.16.1-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v10.16.1/node-v10.16.1-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v10.16.1/node-v10.16.1.tar.gz \
Other release files: https://nodejs.org/dist/v10.16.1/ \
Documentation: https://nodejs.org/docs/v10.16.1/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

f9afd5285438e2f6b72c5c529a9f28a80f0fcd9cdc003e477d6e95177d89fdc2  node-v10.16.1-aix-ppc64.tar.gz
328e61fdacfe2f6f1a049d57e248b3eafc0345747831323a14fe1edf98d9b3bb  node-v10.16.1-darwin-x64.tar.gz
c77d44ad057e1eea84b05b02e8392bb8f1c5b6392b006ecf76734e2d19c8e4c0  node-v10.16.1-darwin-x64.tar.xz
2589cafd5f11241fbfae3592b1337e97be86e436ad7a1db00d12586506f753ed  node-v10.16.1-headers.tar.gz
e93ef520f3e750d8da1d48be3087a83c12671ade0261160bbee8a8c6e309b4af  node-v10.16.1-headers.tar.xz
c5f1df1ae559a9e40fc7216f4c82379d4e8ce64a96921ab0bed216c82cf9a1f3  node-v10.16.1-linux-arm64.tar.gz
c46ba1a8e9b9c0490ae81f8b2b13d332ed6f5c86d172fa817f749042ab329b93  node-v10.16.1-linux-arm64.tar.xz
a783a7d1ba8c994616e3aa3b71f932fdaa35953e6461f1bbe2f48f55bc98cdb5  node-v10.16.1-linux-armv6l.tar.gz
de6055a51019b3f4abc7d53dbbacc34134471726de63b170dc7ff6a9eb0f3efd  node-v10.16.1-linux-armv6l.tar.xz
7977c13f29827ef9177fe79948c39e2f000fa2ae10aa6c4b4de9f47d756354aa  node-v10.16.1-linux-armv7l.tar.gz
ac05af22142d36fdab28c6304c98043341f5cdff715494ef1c2d2a94a3432dd6  node-v10.16.1-linux-armv7l.tar.xz
c1df7db6455f6f985c246b6d9acadb75d242ab0eb1ea4b7b8a51b3343c6aae74  node-v10.16.1-linux-ppc64le.tar.gz
09c543051096dbca046becde26c0e535a242e20d315f3d80fcb14f1ceb013985  node-v10.16.1-linux-ppc64le.tar.xz
388ed445df5a6560b0174fb14491be018b0e80b56a8c392123a8b21b24b533d0  node-v10.16.1-linux-s390x.tar.gz
fc8193c8f72ce4ccd6af9cff7d073455dc200ef183964abd50ba63a6a0b69f9e  node-v10.16.1-linux-s390x.tar.xz
32db9700d2ba926e774c17e7cd8952499e64e241b095d22e05d3d62ebe4cb6d4  node-v10.16.1-linux-x64.tar.gz
127d9b2f485523805208f867fc513842570b29b4d6831773eeb6dd403a22ba9d  node-v10.16.1-linux-x64.tar.xz
081cdcb0d43d141411cb308fd4ed7038cb15390a6d649974b8d85acf2c53e139  node-v10.16.1.pkg
3b7b2feb98380b082d9774b01081e815d6b459dc4125855d9f2dea9559c3d580  node-v10.16.1-sunos-x64.tar.gz
e3c2582645333ba62d81464221d40cdbc811aed793c45c7418f87221f37b0a69  node-v10.16.1-sunos-x64.tar.xz
98c92edcfced73b572917d01a53aa9deefec85d8a2fe96c46fe10ee1d0a7763d  node-v10.16.1.tar.gz
6268319c8dddfdaa7886ea3f253ac5c2d133a6c682c00786296227e03f322009  node-v10.16.1.tar.xz
70b08ba7b488dee2b135b6d5c02ac5ccd6954fd5623da5b3408dec63ce60e6f9  node-v10.16.1-win-x64.7z
1ff8b26670e1b935b51f4b795728f29845c31a3c38b3220b4fe71fb139bc5623  node-v10.16.1-win-x64.zip
ba46e3bd5be77d5c13ac41efacb211508bef6da6cff894a2d0e99ddee79a244e  node-v10.16.1-win-x86.7z
57c2b141c8122d5bfc8ebdaaf5c81b382e3f6fdd3fa7b936a210d8e8e278e4d6  node-v10.16.1-win-x86.zip
dc99f8c0be1e8bb1abfaa194113712ba85cc749bd32990f84cdcdd3b619f6a1c  node-v10.16.1-x64.msi
63d03dff4c314f0adb08ef22f2c0efb05cd425db6a83ccf6a6a035050db9cc47  node-v10.16.1-x86.msi
bd4ce2f0543c2ca31c7a573bda7b816b51b5946e6dc8b7a4e0041b8ab0f758e7  win-x64/node.exe
4c0f7168444b6ef0d469c9303d132520c56e6950cbe9616c2f86c08a498e3632  win-x64/node.lib
835c121df473bcf58ff85d8f760e174f0dc11913aafa01a544b7dca7817d3794  win-x64/node_pdb.7z
b84a1d4bc9b936958074f56802d6f50fa98efc1fbbce42c7288687f2f114a506  win-x64/node_pdb.zip
4c69678620cc2ac6f2421a9585a01a7843499fce492e74e17545a46f1f810b2b  win-x86/node.exe
831fb788501920391120a99d20f443a1f32b6d6446870efcc35a50cd1e485f2b  win-x86/node.lib
91ccb69ae2bb425c2646f22c2476adc02d4ccd51d36f5f598d7ca60f43526f2c  win-x86/node_pdb.7z
0f2b9d30a0c5ed61e86da5fd33e2fdd13cf0deed411ee787bfd9e0b522ba47b4  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQEzBAEBCAAdFiEETtd49TnjY0x3nIfG1wYoSKGrAFwFAl1B57oACgkQ1wYoSKGr
AFxCbgf+IhPuCmednmjsJa20WKTZsZWy0358QMiGEszIjHH/Yxew+Hn1LG89LK5o
gunSrZJoCO+cxx3cpGDjFM2OB3Ro3JK366qamvKU6d+sivOdl01nsmSzFXdi90Z8
uw+NE46I1dW85deEros5E5RnzUuMvSglL+IJTuX/RcwVxns4PDwYvzbmYUFx5sGV
Gr85/C/BbDGzmJY/L9QqqBZsXfscLKQ+OJ3olvJRXOlnw1tZ5fEF/TYrButurUcW
FV9oVO/8KF9d+7DkC76kLJvzFO9iRuRD/7Qk75e2VAujImXgd1yEnWGJGG2TVnbk
tZqdEL8bN4yRZRDgT1vWUnA8DUYLMw==
=0/u3
-----END PGP SIGNATURE-----

```
