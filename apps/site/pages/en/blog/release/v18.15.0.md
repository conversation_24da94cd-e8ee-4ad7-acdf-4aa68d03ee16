---
date: '2023-03-07T20:12:11.827Z'
category: release
title: Node v18.15.0 (LTS)
layout: blog-post
author: '<PERSON> & <PERSON>'
---

### Notable Changes

- \[[`63563f8a7a`](https://github.com/nodejs/node/commit/63563f8a7a)] - **doc,lib,src,test**: rename --test-coverage (<PERSON>) [#46017](https://github.com/nodejs/node/pull/46017)
- \[[`28a775b32f`](https://github.com/nodejs/node/commit/28a775b32f)] - **test_runner**: add initial code coverage support (<PERSON>) [#46017](https://github.com/nodejs/node/pull/46017)
- \[[`4d50db14b3`](https://github.com/nodejs/node/commit/4d50db14b3)] - **(SEMVER-MINOR)** **test_runner**: add reporters (<PERSON><PERSON>) [#45712](https://github.com/nodejs/node/pull/45712)
- \[[`643545ab79`](https://github.com/nodejs/node/commit/643545ab79)] - **(SEMVER-MINOR)** **fs**: add statfs() functions (Colin Ihrig) [#46358](https://github.com/nodejs/node/pull/46358)
- \[[`110ead9abb`](https://github.com/nodejs/node/commit/110ead9abb)] - **(SEMVER-MINOR)** **vm**: expose cachedDataRejected for vm.compileFunction (Anna Henningsen) [#46320](https://github.com/nodejs/node/pull/46320)
- \[[`02632b42cf`](https://github.com/nodejs/node/commit/02632b42cf)] - **(SEMVER-MINOR)** **v8**: support gc profile (theanarkh) [#46255](https://github.com/nodejs/node/pull/46255)
- \[[`f09b838408`](https://github.com/nodejs/node/commit/f09b838408)] - **(SEMVER-MINOR)** **src,lib**: add constrainedMemory API for process (theanarkh) [#46218](https://github.com/nodejs/node/pull/46218)
- \[[`cb5bb12422`](https://github.com/nodejs/node/commit/cb5bb12422)] - **(SEMVER-MINOR)** **buffer**: add isAscii method (Yagiz Nizipli) [#46046](https://github.com/nodejs/node/pull/46046)

### Commits

- \[[`6f91c8e2ae`](https://github.com/nodejs/node/commit/6f91c8e2ae)] - **benchmark**: add trailing commas (Antoine du Hamel) [#46370](https://github.com/nodejs/node/pull/46370)
- \[[`d0b9be21eb`](https://github.com/nodejs/node/commit/d0b9be21eb)] - **benchmark**: remove buffer benchmarks redundancy (Brian White) [#45735](https://github.com/nodejs/node/pull/45735)
- \[[`6468f30d0d`](https://github.com/nodejs/node/commit/6468f30d0d)] - **benchmark**: introduce benchmark combination filtering (Brian White) [#45735](https://github.com/nodejs/node/pull/45735)
- \[[`cb5bb12422`](https://github.com/nodejs/node/commit/cb5bb12422)] - **(SEMVER-MINOR)** **buffer**: add isAscii method (Yagiz Nizipli) [#46046](https://github.com/nodejs/node/pull/46046)
- \[[`ec61bb04c0`](https://github.com/nodejs/node/commit/ec61bb04c0)] - **build**: export more OpenSSL symbols on Windows (Mohamed Akram) [#45486](https://github.com/nodejs/node/pull/45486)
- \[[`7bae4333ce`](https://github.com/nodejs/node/commit/7bae4333ce)] - **build**: fix MSVC 2022 Release compilation (Vladimir Morozov (REDMOND)) [#46228](https://github.com/nodejs/node/pull/46228)
- \[[`0f5f2d4470`](https://github.com/nodejs/node/commit/0f5f2d4470)] - **crypto**: include `hmac.h` in `crypto_util.h` (Adam Langley) [#46279](https://github.com/nodejs/node/pull/46279)
- \[[`91ece4161b`](https://github.com/nodejs/node/commit/91ece4161b)] - **crypto**: avoid hang when no algorithm available (Richard Lau) [#46237](https://github.com/nodejs/node/pull/46237)
- \[[`492fc95bdf`](https://github.com/nodejs/node/commit/492fc95bdf)] - **deps**: V8: cherry-pick 90be99fab31c (Michaël Zasso) [#46646](https://github.com/nodejs/node/pull/46646)
- \[[`732c77e3d9`](https://github.com/nodejs/node/commit/732c77e3d9)] - **deps**: update acorn to 8.8.2 (Node.js GitHub Bot) [#46363](https://github.com/nodejs/node/pull/46363)
- \[[`8582f99ffb`](https://github.com/nodejs/node/commit/8582f99ffb)] - **deps**: update to uvwasi 0.0.15 (Colin Ihrig) [#46253](https://github.com/nodejs/node/pull/46253)
- \[[`5453cd9940`](https://github.com/nodejs/node/commit/5453cd9940)] - **deps**: V8: cherry-pick bf0bd4868dde (Michaël Zasso) [#45908](https://github.com/nodejs/node/pull/45908)
- \[[`3ea53c5dc8`](https://github.com/nodejs/node/commit/3ea53c5dc8)] - **deps**: V8: cherry-pick c875e86df1d7 (sepehrst) [#46501](https://github.com/nodejs/node/pull/46501)
- \[[`c04808de4b`](https://github.com/nodejs/node/commit/c04808de4b)] - **doc**: correct the `sed` command for macOS in release process docs (Juan José) [#46397](https://github.com/nodejs/node/pull/46397)
- \[[`8113220690`](https://github.com/nodejs/node/commit/8113220690)] - **doc**: pass string to `textEncoder.encode` as input (Deokjin Kim) [#46421](https://github.com/nodejs/node/pull/46421)
- \[[`129dccf5d2`](https://github.com/nodejs/node/commit/129dccf5d2)] - **doc**: add tip for session.post function (theanarkh) [#46354](https://github.com/nodejs/node/pull/46354)
- \[[`919e581732`](https://github.com/nodejs/node/commit/919e581732)] - **doc**: add documentation for socket.destroySoon() (Luigi Pinca) [#46337](https://github.com/nodejs/node/pull/46337)
- \[[`fc15ac95a5`](https://github.com/nodejs/node/commit/fc15ac95a5)] - **doc**: fix commit message using test instead of deps (Tony Gorez) [#46313](https://github.com/nodejs/node/pull/46313)
- \[[`d153a93200`](https://github.com/nodejs/node/commit/d153a93200)] - **doc**: add v8 fast api contribution guidelines (Yagiz Nizipli) [#46199](https://github.com/nodejs/node/pull/46199)
- \[[`dbf082d082`](https://github.com/nodejs/node/commit/dbf082d082)] - **doc**: fix small typo error (0xflotus) [#46186](https://github.com/nodejs/node/pull/46186)
- \[[`94421b4cfe`](https://github.com/nodejs/node/commit/94421b4cfe)] - **doc**: mark some parameters as optional in webstreams (Deokjin Kim) [#46269](https://github.com/nodejs/node/pull/46269)
- \[[`5adb743511`](https://github.com/nodejs/node/commit/5adb743511)] - **doc**: update output of example in `events.getEventListeners` (Deokjin Kim) [#46268](https://github.com/nodejs/node/pull/46268)
- \[[`63563f8a7a`](https://github.com/nodejs/node/commit/63563f8a7a)] - **doc,lib,src,test**: rename --test-coverage (Colin Ihrig) [#46017](https://github.com/nodejs/node/pull/46017)
- \[[`4e88c7c813`](https://github.com/nodejs/node/commit/4e88c7c813)] - **esm**: delete preload mock test (Geoffrey Booth) [#46402](https://github.com/nodejs/node/pull/46402)
- \[[`643545ab79`](https://github.com/nodejs/node/commit/643545ab79)] - **(SEMVER-MINOR)** **fs**: add statfs() functions (Colin Ihrig) [#46358](https://github.com/nodejs/node/pull/46358)
- \[[`5019b5473f`](https://github.com/nodejs/node/commit/5019b5473f)] - **http**: res.setHeaders first implementation (Marco Ippolito) [#46109](https://github.com/nodejs/node/pull/46109)
- \[[`76622c4c60`](https://github.com/nodejs/node/commit/76622c4c60)] - **inspector**: allow opening inspector when `NODE_V8_COVERAGE` is set (Moshe Atlow) [#46113](https://github.com/nodejs/node/pull/46113)
- \[[`92f0747e03`](https://github.com/nodejs/node/commit/92f0747e03)] - **meta**: update AUTHORS (Node.js GitHub Bot) [#46399](https://github.com/nodejs/node/pull/46399)
- \[[`795251bc6f`](https://github.com/nodejs/node/commit/795251bc6f)] - **meta**: update AUTHORS (Node.js GitHub Bot) [#46303](https://github.com/nodejs/node/pull/46303)
- \[[`8865424c31`](https://github.com/nodejs/node/commit/8865424c31)] - **meta**: add .mailmap entry (Rich Trott) [#46303](https://github.com/nodejs/node/pull/46303)
- \[[`5ed679407b`](https://github.com/nodejs/node/commit/5ed679407b)] - **meta**: move evanlucas to emeritus (Evan Lucas) [#46274](https://github.com/nodejs/node/pull/46274)
- \[[`403df210ac`](https://github.com/nodejs/node/commit/403df210ac)] - **module**: move test reporter loading (Geoffrey Booth) [#45923](https://github.com/nodejs/node/pull/45923)
- \[[`2f7319e387`](https://github.com/nodejs/node/commit/2f7319e387)] - **readline**: fix detection of carriage return (Antoine du Hamel) [#46306](https://github.com/nodejs/node/pull/46306)
- \[[`73a8f46c4d`](https://github.com/nodejs/node/commit/73a8f46c4d)] - _**Revert**_ "**src**: let http2 streams end after session close" (Santiago Gimeno) [#46721](https://github.com/nodejs/node/pull/46721)
- \[[`30d783f91a`](https://github.com/nodejs/node/commit/30d783f91a)] - **src**: stop tracing agent before shutting down libuv (Santiago Gimeno) [#46380](https://github.com/nodejs/node/pull/46380)
- \[[`1508d90fda`](https://github.com/nodejs/node/commit/1508d90fda)] - **src**: get rid of fp arithmetic in ParseIPv4Host (Tobias Nießen) [#46326](https://github.com/nodejs/node/pull/46326)
- \[[`bdb793a082`](https://github.com/nodejs/node/commit/bdb793a082)] - **src**: use UNREACHABLE instead of CHECK(falsy) (Tobias Nießen) [#46317](https://github.com/nodejs/node/pull/46317)
- \[[`116a33649b`](https://github.com/nodejs/node/commit/116a33649b)] - **src**: add support for ETW stack walking (José Dapena Paz) [#46203](https://github.com/nodejs/node/pull/46203)
- \[[`b06298c98e`](https://github.com/nodejs/node/commit/b06298c98e)] - **src**: refactor EndsInANumber in node_url.cc and adds IsIPv4NumberValid (Miguel Teixeira) [#46227](https://github.com/nodejs/node/pull/46227)
- \[[`26f41b041c`](https://github.com/nodejs/node/commit/26f41b041c)] - **src**: fix c++ exception on bad command line arg (Ben Noordhuis) [#46290](https://github.com/nodejs/node/pull/46290)
- \[[`14da89f41a`](https://github.com/nodejs/node/commit/14da89f41a)] - **src**: remove unreachable UNREACHABLE (Tobias Nießen) [#46281](https://github.com/nodejs/node/pull/46281)
- \[[`18c4dd004b`](https://github.com/nodejs/node/commit/18c4dd004b)] - **src**: replace custom ASCII validation with simdutf one (Anna Henningsen) [#46271](https://github.com/nodejs/node/pull/46271)
- \[[`cde375510f`](https://github.com/nodejs/node/commit/cde375510f)] - **src**: replace unreachable code with static_assert (Tobias Nießen) [#46250](https://github.com/nodejs/node/pull/46250)
- \[[`f389b2f3fc`](https://github.com/nodejs/node/commit/f389b2f3fc)] - **src**: use explicit C++17 fallthrough (Tobias Nießen) [#46251](https://github.com/nodejs/node/pull/46251)
- \[[`8adaa1333c`](https://github.com/nodejs/node/commit/8adaa1333c)] - **src**: use CreateEnvironment instead of inlining its code where possible (Anna Henningsen) [#45886](https://github.com/nodejs/node/pull/45886)
- \[[`f09b838408`](https://github.com/nodejs/node/commit/f09b838408)] - **(SEMVER-MINOR)** **src,lib**: add constrainedMemory API for process (theanarkh) [#46218](https://github.com/nodejs/node/pull/46218)
- \[[`63e92eae63`](https://github.com/nodejs/node/commit/63e92eae63)] - **stream**: remove brandchecks from stream duplexify (Debadree Chatterjee) [#46315](https://github.com/nodejs/node/pull/46315)
- \[[`3acfe9bf92`](https://github.com/nodejs/node/commit/3acfe9bf92)] - **stream**: fix readable stream as async iterator function (Erick Wendel) [#46147](https://github.com/nodejs/node/pull/46147)
- \[[`de64315ccb`](https://github.com/nodejs/node/commit/de64315ccb)] - **test**: fix WPT title when no META title is present (Filip Skokan) [#46804](https://github.com/nodejs/node/pull/46804)
- \[[`162e3400ff`](https://github.com/nodejs/node/commit/162e3400ff)] - **test**: fix default WPT titles (Filip Skokan) [#46778](https://github.com/nodejs/node/pull/46778)
- \[[`5f422c4d70`](https://github.com/nodejs/node/commit/5f422c4d70)] - **test**: add WPTRunner support for variants and generating WPT reports (Filip Skokan) [#46498](https://github.com/nodejs/node/pull/46498)
- \[[`4f5aff2557`](https://github.com/nodejs/node/commit/4f5aff2557)] - **test**: fix tap parser fails if a test logs a number (Pulkit Gupta) [#46056](https://github.com/nodejs/node/pull/46056)
- \[[`32b020cf84`](https://github.com/nodejs/node/commit/32b020cf84)] - **test**: fix tap escaping with and without --test (Pulkit Gupta) [#46311](https://github.com/nodejs/node/pull/46311)
- \[[`f2bba1bcdb`](https://github.com/nodejs/node/commit/f2bba1bcdb)] - **test**: add trailing commas in `test/node-api` (Antoine du Hamel) [#46384](https://github.com/nodejs/node/pull/46384)
- \[[`f2ebe66fda`](https://github.com/nodejs/node/commit/f2ebe66fda)] - **test**: add trailing commas in `test/message` (Antoine du Hamel) [#46372](https://github.com/nodejs/node/pull/46372)
- \[[`ed564a9985`](https://github.com/nodejs/node/commit/ed564a9985)] - **test**: add trailing commas in `test/pseudo-tty` (Antoine du Hamel) [#46371](https://github.com/nodejs/node/pull/46371)
- \[[`e4437dd409`](https://github.com/nodejs/node/commit/e4437dd409)] - **test**: set common.bits to 64 for loong64 (Shi Pujin) [#45383](https://github.com/nodejs/node/pull/45383)
- \[[`9d40aef736`](https://github.com/nodejs/node/commit/9d40aef736)] - **test**: s390x zlib test case fixes (Adam Majer) [#46367](https://github.com/nodejs/node/pull/46367)
- \[[`ed3fb52716`](https://github.com/nodejs/node/commit/ed3fb52716)] - **test**: fix logInTimeout is not function (theanarkh) [#46348](https://github.com/nodejs/node/pull/46348)
- \[[`d05b0771be`](https://github.com/nodejs/node/commit/d05b0771be)] - **test**: avoid trying to call sysctl directly (Adam Majer) [#46366](https://github.com/nodejs/node/pull/46366)
- \[[`041aac3bbd`](https://github.com/nodejs/node/commit/041aac3bbd)] - **test**: avoid left behind child processes (Richard Lau) [#46276](https://github.com/nodejs/node/pull/46276)
- \[[`837ddcb322`](https://github.com/nodejs/node/commit/837ddcb322)] - **test**: add failing test for readline with carriage return (Alec Mev) [#46075](https://github.com/nodejs/node/pull/46075)
- \[[`75b8db41c6`](https://github.com/nodejs/node/commit/75b8db41c6)] - **test**: reduce `fs-write-optional-params` flakiness (LiviaMedeiros) [#46238](https://github.com/nodejs/node/pull/46238)
- \[[`c0d3fdaf63`](https://github.com/nodejs/node/commit/c0d3fdaf63)] - **test,crypto**: add CFRG curve vectors to wrap/unwrap tests (Filip Skokan) [#46406](https://github.com/nodejs/node/pull/46406)
- \[[`f328f7b15e`](https://github.com/nodejs/node/commit/f328f7b15e)] - **test,crypto**: update WebCryptoAPI WPT (Filip Skokan) [#46267](https://github.com/nodejs/node/pull/46267)
- \[[`1ef3c53e24`](https://github.com/nodejs/node/commit/1ef3c53e24)] - **test_runner**: better handle async bootstrap errors (Colin Ihrig) [#46720](https://github.com/nodejs/node/pull/46720)
- \[[`0a690efb76`](https://github.com/nodejs/node/commit/0a690efb76)] - **test_runner**: add `describe.only` and `it.only` shorthands (Richie McColl) [#46604](https://github.com/nodejs/node/pull/46604)
- \[[`28a1317efe`](https://github.com/nodejs/node/commit/28a1317efe)] - **test_runner**: bootstrap reporters before running tests (Moshe Atlow) [#46737](https://github.com/nodejs/node/pull/46737)
- \[[`cd3aaa8fac`](https://github.com/nodejs/node/commit/cd3aaa8fac)] - **test_runner**: emit test-only diagnostic warning (Richie McColl) [#46540](https://github.com/nodejs/node/pull/46540)
- \[[`c19fa45a65`](https://github.com/nodejs/node/commit/c19fa45a65)] - **test_runner**: centralize CLI option handling (Colin Ihrig) [#46707](https://github.com/nodejs/node/pull/46707)
- \[[`0898145e37`](https://github.com/nodejs/node/commit/0898145e37)] - **test_runner**: display skipped tests in spec reporter output (Richie McColl) [#46651](https://github.com/nodejs/node/pull/46651)
- \[[`894d7117fa`](https://github.com/nodejs/node/commit/894d7117fa)] - **test_runner**: parse non-ascii character correctly (Mert Can Altın) [#45736](https://github.com/nodejs/node/pull/45736)
- \[[`5b3c606626`](https://github.com/nodejs/node/commit/5b3c606626)] - **test_runner**: flatten TAP output when running using `--test` (Moshe Atlow) [#46440](https://github.com/nodejs/node/pull/46440)
- \[[`391ff0dba4`](https://github.com/nodejs/node/commit/391ff0dba4)] - **test_runner**: allow nesting test within describe (Moshe Atlow) [#46544](https://github.com/nodejs/node/pull/46544)
- \[[`ba784e87b4`](https://github.com/nodejs/node/commit/ba784e87b4)] - **test_runner**: fix missing test diagnostics (Moshe Atlow) [#46450](https://github.com/nodejs/node/pull/46450)
- \[[`c5f16fb5fb`](https://github.com/nodejs/node/commit/c5f16fb5fb)] - **test_runner**: top-level diagnostics not ommited when running with --test (Pulkit Gupta) [#46441](https://github.com/nodejs/node/pull/46441)
- \[[`28a775b32f`](https://github.com/nodejs/node/commit/28a775b32f)] - **test_runner**: add initial code coverage support (Colin Ihrig) [#46017](https://github.com/nodejs/node/pull/46017)
- \[[`0d999e373a`](https://github.com/nodejs/node/commit/0d999e373a)] - **test_runner**: make built in reporters internal (Colin Ihrig) [#46092](https://github.com/nodejs/node/pull/46092)
- \[[`79f4b426fe`](https://github.com/nodejs/node/commit/79f4b426fe)] - **test_runner**: report `file` in test runner events (Moshe Atlow) [#46030](https://github.com/nodejs/node/pull/46030)
- \[[`4d50db14b3`](https://github.com/nodejs/node/commit/4d50db14b3)] - **(SEMVER-MINOR)** **test_runner**: add reporters (Moshe Atlow) [#45712](https://github.com/nodejs/node/pull/45712)
- \[[`5fdf374c74`](https://github.com/nodejs/node/commit/5fdf374c74)] - **test_runner**: avoid swallowing of asynchronously thrown errors (MURAKAMI Masahiko) [#45264](https://github.com/nodejs/node/pull/45264)
- \[[`23b875806c`](https://github.com/nodejs/node/commit/23b875806c)] - **test_runner**: update comment to comply with eslint no-fallthrough rule (Antoine du Hamel) [#46258](https://github.com/nodejs/node/pull/46258)
- \[[`00c5495aa3`](https://github.com/nodejs/node/commit/00c5495aa3)] - **tools**: update eslint to 8.33.0 (Node.js GitHub Bot) [#46400](https://github.com/nodejs/node/pull/46400)
- \[[`37a6ce1120`](https://github.com/nodejs/node/commit/37a6ce1120)] - **tools**: update doc to unist-util-select\@4.0.3 unist-util-visit\@4.1.2 (Node.js GitHub Bot) [#46364](https://github.com/nodejs/node/pull/46364)
- \[[`1eaafc7db4`](https://github.com/nodejs/node/commit/1eaafc7db4)] - **tools**: update lint-md-dependencies to rollup\@3.12.0 (Node.js GitHub Bot) [#46398](https://github.com/nodejs/node/pull/46398)
- \[[`a97774603b`](https://github.com/nodejs/node/commit/a97774603b)] - **tools**: require more trailing commas (Antoine du Hamel) [#46346](https://github.com/nodejs/node/pull/46346)
- \[[`03e244a59b`](https://github.com/nodejs/node/commit/03e244a59b)] - **tools**: update lint-md-dependencies (Node.js GitHub Bot) [#46302](https://github.com/nodejs/node/pull/46302)
- \[[`60d714e0c3`](https://github.com/nodejs/node/commit/60d714e0c3)] - **tools**: allow icutrim.py to run on python2 (Michael Dawson) [#46263](https://github.com/nodejs/node/pull/46263)
- \[[`b7950f50de`](https://github.com/nodejs/node/commit/b7950f50de)] - **tools**: update eslint to 8.32.0 (Node.js GitHub Bot) [#46258](https://github.com/nodejs/node/pull/46258)
- \[[`08bafc84f6`](https://github.com/nodejs/node/commit/08bafc84f6)] - **url**: refactor to use more primordials (Antoine du Hamel) [#45966](https://github.com/nodejs/node/pull/45966)
- \[[`02632b42cf`](https://github.com/nodejs/node/commit/02632b42cf)] - **(SEMVER-MINOR)** **v8**: support gc profile (theanarkh) [#46255](https://github.com/nodejs/node/pull/46255)
- \[[`110ead9abb`](https://github.com/nodejs/node/commit/110ead9abb)] - **(SEMVER-MINOR)** **vm**: expose cachedDataRejected for vm.compileFunction (Anna Henningsen) [#46320](https://github.com/nodejs/node/pull/46320)

Windows 32-bit Installer: https://nodejs.org/dist/v18.15.0/node-v18.15.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v18.15.0/node-v18.15.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v18.15.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v18.15.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v18.15.0/node-v18.15.0.pkg \
macOS Apple Silicon 64-bit Binary: https://nodejs.org/dist/v18.15.0/node-v18.15.0-darwin-arm64.tar.gz \
macOS Intel 64-bit Binary: https://nodejs.org/dist/v18.15.0/node-v18.15.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v18.15.0/node-v18.15.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v18.15.0/node-v18.15.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v18.15.0/node-v18.15.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v18.15.0/node-v18.15.0-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v18.15.0/node-v18.15.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v18.15.0/node-v18.15.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v18.15.0/node-v18.15.0.tar.gz \
Other release files: https://nodejs.org/dist/v18.15.0/ \
Documentation: https://nodejs.org/docs/v18.15.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

b5a7cf75c6d22536b8c364a4414bf6df88efea54429e185e2a0c819b90c491ed  node-v18.15.0-aix-ppc64.tar.gz
bd302a689c3c34e2b61d86b97de66d26a335881a17af09b6a0a4bb1019df56e4  node-v18.15.0-darwin-arm64.tar.gz
5719959a463a265509d27ebc98cab0f6a09c3e1f6827859cc1d7c299ae56a8bc  node-v18.15.0-darwin-arm64.tar.xz
76add174d2d3f98da08907412e82add7352b8cb6f639324d352a65c084b99c7e  node-v18.15.0-darwin-x64.tar.gz
f257c015bc3a5ec5d8a552c87b3ac00f7763bead0061cd1f62125cce6285c0b6  node-v18.15.0-darwin-x64.tar.xz
5ada31143933f0443d20769ae5942c4b1c5c80fe9f8a8221dae12ed9bec2ad2d  node-v18.15.0-headers.tar.gz
92c82571b32043a0ffc6e19cdd6d31503960b7062a90455bb9071a1a0ecd97b3  node-v18.15.0-headers.tar.xz
8ef7aa7a679360ddbf0c7c8511881030b3de9d1f54451d92ba5c8d59a91c7073  node-v18.15.0-linux-arm64.tar.gz
98ea6ed0a1ae55334ab2c03c34d5e52c6dc3dee8f236c0afc08ab1c964506633  node-v18.15.0-linux-arm64.tar.xz
ca2186313d3cbe5c67d0c08e931a6d290906f4f13c584e63fefa05a04dee9c58  node-v18.15.0-linux-armv7l.tar.gz
baad3cdf1365f46bf837635554b10bc3e320799e69ac26e07df1fcde0c1738c7  node-v18.15.0-linux-armv7l.tar.xz
da9ecfc33bc1162332a572f36a9cf2d71589eab6c7cf265d0a381ffc63148588  node-v18.15.0-linux-ppc64le.tar.gz
c9215ed3e9817dd42253b87a34997b8846570f7fdaaf3bf12fb4df6e731c12e2  node-v18.15.0-linux-ppc64le.tar.xz
d4c9d57db2f0b90c69af95e036014f86824b597642309811cd958f04866fb694  node-v18.15.0-linux-s390x.tar.gz
029216909814bc66caf64f3bb2dbdb961f10db0c6ec95f6039912248da15cfec  node-v18.15.0-linux-s390x.tar.xz
b298a73a9fc07badfa9e4a2e86ed48824fc9201327cdc43e3f3f58b273c535e7  node-v18.15.0-linux-x64.tar.gz
c8c5fa53ce0c0f248e45983e86368e0b1daf84b77e88b310f769c3cfc12682ef  node-v18.15.0-linux-x64.tar.xz
ec94f5f15539c4b7f61393494916f3877583957f439d43f69a3fb9df1480b588  node-v18.15.0.pkg
d65c4c3ef3c8815bccda9502081a29458c7c80797db0763f8752f270a824ac2b  node-v18.15.0.tar.gz
8e44d65018ff973284195c23186469a0ea4082e97ec4200e5f5706d5584daa37  node-v18.15.0.tar.xz
cad3cc0910dc216e8b6dcfc3c5b3be0a619c2d4a4b29f2e674820b70e4f374dd  node-v18.15.0-win-x64.7z
118fbcae58bc8c53cbe97a10c019734ed90685da8dda98aa0b0f4aeead42a647  node-v18.15.0-win-x64.zip
370dbe4ac8fa516a33540821d409153035c50a449ca12f74f78bcf0b634d1957  node-v18.15.0-win-x86.7z
948b54f929375803156deb1f2c691db42c298d0c69392a82d10cca1177e40545  node-v18.15.0-win-x86.zip
36de8d8fecd0920175ed6e61266934f3469888990f0cc54238e50437490515d5  node-v18.15.0-x64.msi
f7a2096bf8c34db99251e9cd9ce8ea4a7369be4e9f57daaa13b0f32c98de94b6  node-v18.15.0-x86.msi
17fd75d8a41bf9b4c475143e19ff2808afa7a92f7502ede731537d9da674d5e8  win-x64/node.exe
ef9630aefd29b4ed2fb31340d76a6936a69b869c6c9f18f4d0b5c26e866f738b  win-x64/node.lib
acecef677661f5d93c3bbcef56cfd02c03cd684df70fbb5a01003bc364501e8d  win-x64/node_pdb.7z
699d649ee46bddd532803f5e4d14a413d708a5b58840475f5fe7e5b40ccc1b0b  win-x64/node_pdb.zip
d78b2f981465a40a23b964b2db32a390db1970a0dd5371682e121ae2b7422697  win-x86/node.exe
8c4ccf0f1e4fcecfb6956f4b8dcb226ca02ea574cd427a20c9186ea9eacccb87  win-x86/node.lib
fa430e027bf61c42f107b39c0b8cac23313671762ce11c5007729bb48c5abff4  win-x86/node_pdb.7z
f3400699d618d59a17f6622b4b386459dc9e8fab01a12d6413b939a360e990eb  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQEzBAEBCAAdFiEETtd49TnjY0x3nIfG1wYoSKGrAFwFAmQHmDUACgkQ1wYoSKGr
AFyX/wgAkPehwP+iH1nYUDVoQ+Xs1EcQbEG8WFGEshZnIiHIFqO1AmXo0t5/iUOK
yjzBBUD1YX05h8HPxX0bAtOd1Gj6F/IhQrG30SxN59IMyAa3/8trEbg20qGUbcGf
yoZ8oAe96w/xLcrESm4rvWIQzBNA2AbU8zRRhGSek8JRiKNdjj7APzVLJU4Qikv1
y+nW03hBm3TDm0m0YeBOic793LXU/mCvk3x4WxEQbPFUh3qlj+ulYNPY2/vMzYPT
9KqRgI2PiEmNHS69/xGt1p3sxUQ43I0cjYC4fw8evbVXR1FVDTE/rd/NgZ4Fv6Tr
dPZ8t4Y7vIO+mYnhp2KNyDpBfIdWdQ==
=wfUF
-----END PGP SIGNATURE-----

```
