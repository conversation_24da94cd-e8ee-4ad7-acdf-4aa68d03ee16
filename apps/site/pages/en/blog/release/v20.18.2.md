---
date: '2025-01-21T17:05:40.198Z'
category: release
title: Node v20.18.2 (LTS)
layout: blog-post
author: <PERSON>
---

## 2025-01-21, Version 20.18.2 'Iron' (LTS), @RafaelGSS

This is a security release.

### Notable Changes

- CVE-2025-23083 - throw on InternalWorker use when permission model is enabled (High)
- CVE-2025-23085 - src: fix HTTP2 mem leak on premature close and ERR_PROTO (Medium)
- CVE-2025-23084 - path: fix path traversal in normalize() on Windows (Medium)

Dependency update:

- CVE-2025-22150 - Use of Insufficiently Random Values in undici fetch() (Medium)

### Commits

- \[[`df8b9f2c3e`](https://github.com/nodejs/node/commit/df8b9f2c3e)] - **(CVE-2025-22150)** **deps**: update undici to v6.21.1 (<PERSON>) [nodejs-private/node-private#663](https://github.com/nodejs-private/node-private/pull/663)
- \[[`42d5821873`](https://github.com/nodejs/node/commit/42d5821873)] - **(CVE-2025-23084)** **path**: fix path traversal in normalize() on Windows (Tobias Nießen) [nodejs-private/node-private#555](https://github.com/nodejs-private/node-private/pull/555)
- \[[`8187a4b9bb`](https://github.com/nodejs/node/commit/8187a4b9bb)] - **src**: fix HTTP2 mem leak on premature close and ERR_PROTO (RafaelGSS)
- \[[`389f239a28`](https://github.com/nodejs/node/commit/389f239a28)] - **(CVE-2025-23083)** **src,loader,permission**: throw on InternalWorker use (RafaelGSS) [nodejs-private/node-private#652](https://github.com/nodejs-private/node-private/pull/652)

Windows 32-bit Installer: https://nodejs.org/dist/v20.18.2/node-v20.18.2-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v20.18.2/node-v20.18.2-x64.msi \
Windows ARM 64-bit Installer: https://nodejs.org/dist/v20.18.2/node-v20.18.2-arm64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v20.18.2/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v20.18.2/win-x64/node.exe \
Windows ARM 64-bit Binary: https://nodejs.org/dist/v20.18.2/win-arm64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v20.18.2/node-v20.18.2.pkg \
macOS Apple Silicon 64-bit Binary: https://nodejs.org/dist/v20.18.2/node-v20.18.2-darwin-arm64.tar.gz \
macOS Intel 64-bit Binary: https://nodejs.org/dist/v20.18.2/node-v20.18.2-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v20.18.2/node-v20.18.2-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v20.18.2/node-v20.18.2-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v20.18.2/node-v20.18.2-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v20.18.2/node-v20.18.2-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v20.18.2/node-v20.18.2-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v20.18.2/node-v20.18.2-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v20.18.2/node-v20.18.2.tar.gz \
Other release files: https://nodejs.org/dist/v20.18.2/ \
Documentation: https://nodejs.org/docs/v20.18.2/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

efcddeb91b189b02706d1a75a67b4a111253ec8f64cc30cc3dc4649744abd52b  node-v20.18.2-aix-ppc64.tar.gz
40c5a72564b8667342bec84aab50d2af1503af2b274f1a7a09d2d929461988b6  node-v20.18.2-arm64.msi
fa76d5b5340f14070ebaa88ef8faa28c1e9271502725e830cb52f0cf5b6493de  node-v20.18.2-darwin-arm64.tar.gz
32dc17147054df9cdf96d03103f4661b4cb0bb9b4ca4b70e34fe632f1bab189c  node-v20.18.2-darwin-arm64.tar.xz
00a16bb0a82a2ad5d00d66b466ae1afa678482283747c27e9bce96668f334744  node-v20.18.2-darwin-x64.tar.gz
184c9b8e246a3fd139caf2456510dc99ec548ad2e5203fbc5fc56ba48104e8eb  node-v20.18.2-darwin-x64.tar.xz
d74c718976adc308991fb8784f0b3f82845436bf8f04d2c982ab5cab5115289f  node-v20.18.2-headers.tar.gz
05819d72dcc0aa788baab1066e18ede5f1ab6730a1925cd6b15c131b55fd4272  node-v20.18.2-headers.tar.xz
319789e8a055ff80793a05e633c8c5c9226050144a09da3747225b4ec56a2a99  node-v20.18.2-linux-arm64.tar.gz
5c1437aa16e7e6a2e0687a42c4d3f0a8f8a2039cda8880cb3be8cd983aeefb44  node-v20.18.2-linux-arm64.tar.xz
65397a4a63960bda94718099698d2961623e9ef400f60f4c3a71add2268bccfb  node-v20.18.2-linux-armv7l.tar.gz
63d4df56fb2e34a5077345f78941094204d2223ce03b8ebc9c1500e6e2aae68d  node-v20.18.2-linux-armv7l.tar.xz
9b2f0fd3b02d8b59bde3e2a251e4df501e755c99cfc4886b0bdf85fa4d0bc538  node-v20.18.2-linux-ppc64le.tar.gz
828a2635261ca225cd4a8a4b1a914003cdc7b30656c2e9092ac7aab02ac361db  node-v20.18.2-linux-ppc64le.tar.xz
7e52e03823feaa2483a7cbcf85767790776f87a2c7112d87600c3d9d3b1ae6e9  node-v20.18.2-linux-s390x.tar.gz
bcf3680e111f1d24e403db3d5600315266ae1f8d9d1f69f39c61dbf8d8c9036e  node-v20.18.2-linux-s390x.tar.xz
eb5b031bdd728871c3b9a82655dbfa533bc262c0b6da1d09a86842430cef07d4  node-v20.18.2-linux-x64.tar.gz
4e50f727ae09bdafecf2322c72faf7cd82bf3b8851a16b8bb63974e0d8d6eceb  node-v20.18.2-linux-x64.tar.xz
87d10db681bca2a39fcadcc908d5e5b2c7effa16370c4ca555373b85e25275b1  node-v20.18.2.pkg
cf3ef49fafbfee3cdcd936a0d6031341b73bfa6b26a484ea0a4936c26d24b829  node-v20.18.2.tar.gz
69bf81b70f3a95ae0763459f02860c282d7e3a47567c8afaf126cc778176a882  node-v20.18.2.tar.xz
d28d21e000ebed8b6131201b727d1998d4dbc4dbdb6e5ad07679552e4c75fa4d  node-v20.18.2-win-arm64.7z
b89d196a2d9dc3dac87c268aac9a983fa2fd1881c14884bc848312783ccf7d2f  node-v20.18.2-win-arm64.zip
06e72c0f78cc1bf1819eb0a0a37001d2917f19ad46a149c2f923c901f599ba52  node-v20.18.2-win-x64.7z
ed790b94570518a7dce67b62485e16bc4bffecee4ec3b6df35ed220ae91117a5  node-v20.18.2-win-x64.zip
fa561ebff3f52667228f9fcd9e67ce22a86e5c28c8e3782e01a95c90b6ed114d  node-v20.18.2-win-x86.7z
25f00a77843accc098561a35ce3ed923357f0127b8e5db594cb62188e3290b88  node-v20.18.2-win-x86.zip
f3ad2d799e1645281d22d71b447f3899e569da87fea78bef9571b0c2b53288d6  node-v20.18.2-x64.msi
783c4041ceb69226184a1b26177b5d9dc85e502d0f124c64d2b2c6f8ab12e5d5  node-v20.18.2-x86.msi
83e7ad1b8c4d4d9c5e06849c3e8f3a5948a5eb6aa34c5bd973ba700e0386f42c  win-arm64/node.exe
58795bcd44e8023ff443dedabf7f9af928732a51befc5324082aafe56e0f5eb0  win-arm64/node.lib
83fdda5fb5869c18f5d5d3dc4d0479f6bdad16f0888c95b8008f03654593afdd  win-arm64/node_pdb.7z
4049c1e7c2fc82c4d43c9d8567e7d20f20c0d360c281fcb924ce9cd4b9ce8dc3  win-arm64/node_pdb.zip
8487a277e92282904dfe0f860dbd5d229543e97a858a223fbe9c9b8670bbe170  win-x64/node.exe
5a16801c62c34c8056744ac339950c970b2b76f39b2d02afef4112ff51b74f1a  win-x64/node.lib
6ff19d51a762405717f7dff33811ba6371334de95946efbccf6f8dd786ec93e8  win-x64/node_pdb.7z
07ef9641b5a339de2f43f698dc3b1aeb321e851645b199cbeb0f378674263bf1  win-x64/node_pdb.zip
ab4b6beaaa170cfed83a2c9c71d8d5032ac514a5ebd7a5aa0553731267964f5e  win-x86/node.exe
fcc6ab34ebd4ad3a44de12376c3822c2ebc41febaa1ed4c4221ddc239f79f61c  win-x86/node.lib
ab74677f28b517eee9f745930541d02a870ae2d3f29a5ac91fe630813a1cd987  win-x86/node_pdb.7z
6080ab7b513194510c8938c276b7fd4379eb0ed69cfa09dbb21da8a4eeddd75f  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQGzBAEBCAAdFiEEiQwI24V5Fi/uDfnbi+q0389VXvQFAmeP0VcACgkQi+q0389V
XvSVVAv9Gqyi4i2K2vHIKo7Pe/ZQB4BLg5nmzDtd6SNqtcSgTM+oR3u1vOeFdeTL
tXpIFaqUh0uYTYBpE4aJWajwXPnKWYu+uJO8Z1PtfUitM9PiZT5w2Ko69QPxIt7K
4BmRxFhCVAH87R2SKiXWWzhDBQDQKboQFBtVG4G16HfOXp9leKSxUTKQ8B0fU+4L
qbiCk9EvaBAmEJdrzT8Od/GjMdcBFXzllNRlROVu5hhAv6+HHlMuxR3RgaN5wml+
zFFQeQk+lY1YZeA0DpslwB5raCrsLehkEywlXsJHF3wmRKOhsrFaQlTDLtzZuyoN
BY6LQqkmY5hJGiCOeiXSCNHAXUpqmOLvUJQgXSW0jaEh41o66yXk7MGthUegYo5o
AekiwYEZE50K4QLE9xdIFjQ5GeSp0iK81wxGgPLjVS0DBe0sw+sDWMep+yisHmhP
Ttf0cY501JvjbdaBB40ug5LFpABuKQJgzmbTmrU8Edf+apbJa35OZRt2bNg7xoYs
SzUkh4yR
=eALp
-----END PGP SIGNATURE-----
```
