---
date: '2022-07-07T16:08:13.685Z'
category: release
title: Node v18.5.0 (Current)
layout: blog-post
author: <PERSON>
---

### Notable Changes

- \[[`3f0c3e142d`](https://github.com/nodejs/node/commit/3f0c3e142d)] - **(SEMVER-MAJOR)** **src,deps,build,test**: add OpenSSL config appname (<PERSON>) [#43124](https://github.com/nodejs/node/pull/43124)
- \[[`9578158ff8`](https://github.com/nodejs/node/commit/9578158ff8)] - **(SEMVER-MAJOR)** **src,doc,test**: add --openssl-shared-config option (<PERSON>) [#43124](https://github.com/nodejs/node/pull/43124)
  - Node.js now reads `nodejs_conf` section in the `openssl` config
- \[[`dc7af13486`](https://github.com/nodejs/node/commit/dc7af13486)] - **deps**: update archs files for quictls/openssl-3.0.5+quic (RafaelGSS) [#43693](https://github.com/nodejs/node/pull/43693)
- \[[`fa72c534eb`](https://github.com/nodejs/node/commit/fa72c534eb)] - **deps**: upgrade openssl sources to quictls/openssl-3.0.5+quic (RafaelGSS) [#43693](https://github.com/nodejs/node/pull/43693)

### Commits

- \[[`dc7af13486`](https://github.com/nodejs/node/commit/dc7af13486)] - **deps**: update archs files for quictls/openssl-3.0.5+quic (RafaelGSS) [#43693](https://github.com/nodejs/node/pull/43693)
- \[[`fa72c534eb`](https://github.com/nodejs/node/commit/fa72c534eb)] - **deps**: upgrade openssl sources to quictls/openssl-3.0.5+quic (RafaelGSS) [#43693](https://github.com/nodejs/node/pull/43693)
- \[[`a5fc2deb43`](https://github.com/nodejs/node/commit/a5fc2deb43)] - **deps**: update default openssl.cnf directory (Michael Dawson) [nodejs-private/node-private#335](https://github.com/nodejs-private/node-private/pull/335)
- \[[`f2407748e3`](https://github.com/nodejs/node/commit/f2407748e3)] - **http**: stricter Transfer-Encoding and header separator parsing (Paolo Insogna) [nodejs-private/node-private#315](https://github.com/nodejs-private/node-private/pull/315)
- \[[`e4af5eba95`](https://github.com/nodejs/node/commit/e4af5eba95)] - **src**: fix IPv4 validation in inspector_socket (Tobias Nießen) [nodejs-private/node-private#320](https://github.com/nodejs-private/node-private/pull/320)
- \[[`3f0c3e142d`](https://github.com/nodejs/node/commit/3f0c3e142d)] - **(SEMVER-MAJOR)** **src,deps,build,test**: add OpenSSL config appname (Daniel Bevenius) [#43124](https://github.com/nodejs/node/pull/43124)
- \[[`9578158ff8`](https://github.com/nodejs/node/commit/9578158ff8)] - **(SEMVER-MAJOR)** **src,doc,test**: add --openssl-shared-config option (Daniel Bevenius) [#43124](https://github.com/nodejs/node/pull/43124)

Windows 32-bit Installer: https://nodejs.org/dist/v18.5.0/node-v18.5.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v18.5.0/node-v18.5.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v18.5.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v18.5.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v18.5.0/node-v18.5.0.pkg \
macOS Apple Silicon 64-bit Binary: https://nodejs.org/dist/v18.5.0/node-v18.5.0-darwin-arm64.tar.gz \
macOS Intel 64-bit Binary: https://nodejs.org/dist/v18.5.0/node-v18.5.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v18.5.0/node-v18.5.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v18.5.0/node-v18.5.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v18.5.0/node-v18.5.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v18.5.0/node-v18.5.0-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v18.5.0/node-v18.5.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v18.5.0/node-v18.5.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v18.5.0/node-v18.5.0.tar.gz \
Other release files: https://nodejs.org/dist/v18.5.0/ \
Documentation: https://nodejs.org/docs/v18.5.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

32959422d032244fc5bed1ff8ddbaddacb12b4d333b3e1ae20dc0f2e6bac64f8  node-v18.5.0-aix-ppc64.tar.gz
8d4012c137ba18d8a3e650c01f83d995235dcef87a65d2be55471594b33be52f  node-v18.5.0-darwin-arm64.tar.gz
451c21b6ce54e855aad93709e607fc60ab2e3f68e3da39c0739849e6c3be6116  node-v18.5.0-darwin-arm64.tar.xz
bb72c852c249fde250418cd4dcef633d932e49414194aa25d199168e64207b77  node-v18.5.0-darwin-x64.tar.gz
3b3a8775397c8a7c7c2975801d15e436c628811cbe7344a0cd218ae0d3c3f368  node-v18.5.0-darwin-x64.tar.xz
aeb303096c6ebfad9188128ed5e1d75abc4640143ca3d6f47d51d926dc0db374  node-v18.5.0-headers.tar.gz
0c3e42f0ed4dfae4d4c3003f23c0f95c24d95ad0cd60b5cba48403074e3954c6  node-v18.5.0-headers.tar.xz
fab94abe6f88538b3a53d68582bffee37e181fbb8ac717b0a2abc25851e6616f  node-v18.5.0-linux-arm64.tar.gz
cb16406a059882928de74359c20eb1daa272efcb8160495ea02c9a162f3ce33c  node-v18.5.0-linux-arm64.tar.xz
e0d8ae98b881238a441672c84f3baa6f026fc11694ad50028291a0062607b6cf  node-v18.5.0-linux-armv7l.tar.gz
ed31d680a918437e4ca0b9137af03d96b0b984048eac456f9611fe110df9eaf8  node-v18.5.0-linux-armv7l.tar.xz
14d16808b6a5ae0e6ed2a426f8921e7e16c1dae0b7e43952d75675a929a8674e  node-v18.5.0-linux-ppc64le.tar.gz
0f304ad3d923c133ee6817830aba185543d807f752a4353bc6d76f57ae9a6c76  node-v18.5.0-linux-ppc64le.tar.xz
005a20ab266f1d0a3057da23497df5235bd51f4d294745a2dee17f755fdea366  node-v18.5.0-linux-s390x.tar.gz
ad39b6ff5d7b0ee2a182dce53f78fd9f92c847a59a96b160a1deff7816f98d4a  node-v18.5.0-linux-s390x.tar.xz
deb4b0b8b82354a1b5087b724ab0d5861081302a12c0b204d799b31fea527eda  node-v18.5.0-linux-x64.tar.gz
3a64a0d2f86831d56fbfd9b59f61fe84f639d24772c5eaba0bfac23219a6d74d  node-v18.5.0-linux-x64.tar.xz
d27c6f1a4c9a3bc84e867421f9c8d86746ed560e81e274b92ce54c7df9bdac63  node-v18.5.0-win-x64.7z
a87088d54437e12a95831c3968a305eb3ac7a2be448de56cdc58af045dc89b26  node-v18.5.0-win-x64.zip
964e8000730c61899010452ad6177c9a0d21b76980f6865c65310f6929a88d02  node-v18.5.0-win-x86.7z
e1018ec46ebb5be31fe8ed8ff470bccfb152e5b59c95b58ea4aeb7da3e3a7eec  node-v18.5.0-win-x86.zip
5ab1ed0db4b52037b14d3591757a0e697e9a31abea05e4ef6e78ddc6ddb95742  node-v18.5.0-x64.msi
4bca49ae9b422086f745158eab17d42dcb0c20e7707b1c0ffe3246a0bac801b5  node-v18.5.0-x86.msi
44683cb8d23dc33bb109d17f1729d0cb44929008300014ad008e11801b375b9a  node-v18.5.0.pkg
47843654ffa6a59557254fdfe6d74c1fa21e314bcc4bc203727acc058b7780dd  node-v18.5.0.tar.gz
368b5694e380b05d436369484754659b2f040cfe13fed011ebab5e5198f1a030  node-v18.5.0.tar.xz
49838cdda5df3faf26dae0056af451957d0f04d414fbbba64bddea7ba955efb3  win-x64/node.exe
8dd17e07475a098640979532b8ad77896812605b13c4d715a8aa8804833494b4  win-x64/node.lib
f778ad096514854a390bcda5e0518d2d6d0ed904f398cab598a095b83c6aba50  win-x64/node_pdb.7z
059d7f50c8b7cbe5e37faad45e2f8e00c6102bdaa8253df23429b1ddca283108  win-x64/node_pdb.zip
95dbc929ad16b8de0952434cf1b356f755e7a6733d3d4153c6aa948fb71e8f9a  win-x86/node.exe
333bd4a83c23547ac8f5f250e6ff171e03919cfa3293b438de0e0baa5976767d  win-x86/node.lib
0b7105e18d4dc631dd116e2cd2a29339a4391369508aa08ac97c9310743e9b38  win-x86/node_pdb.7z
a2c66261fcaf0a965877dfbbb2494c75ce4d45087b540f77fd0c3fca8d141708  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQGzBAEBCAAdFiEEiQwI24V5Fi/uDfnbi+q0389VXvQFAmLG+vcACgkQi+q0389V
XvTThgv/VeDKuleng2S4t5khjkvLuZoelc+9jQFfamNaLn7iPskjGgBQfyGtgfPM
PHKly3I6nvx4ZsfaDttSm1HyV7r2/m2MmBoUANFVNcZN9gsys88Stx/4QqJ3OWDo
BhAxdJWjgOLy2m6J3v7ChAYn7/y6IK8QnH0e52jY2R1X5C9xGvCfWhxbimSvXn4C
JQIHkrBMUVHy9TlGApw7y/NClYa9CPNsDjkn/E3k/AvIvfJQqe7AK5bAFk96zVY1
+4/PCFv3BroaVgUnRmSDeTNpFeTHEAm7g9e3CUoJ1Oic9CT0JCZ0H1Kq+sojLq6n
T/T6Hr4h9j7xZFsZEpJb7sTcZJAEtat+8tnB5zd3j9M/Llwi1sT73GLBhIGx8m18
bVjfQY8U1VHe3XQtfTaxNYLI6aXqbDt63VZJJoY6BmusFYVtGha/QOf3F/jFBlLg
nOd1hTqceJDCEq81ehnj0intyzIQTUaubEDwDbiNe3p22HFDW873iE6Z0ay7wuE1
kvb3e5+V
=FTfy
-----END PGP SIGNATURE-----

```
