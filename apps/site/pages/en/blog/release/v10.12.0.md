---
date: '2018-10-10T21:26:27.698Z'
category: release
title: Node v10.12.0 (Current)
layout: blog-post
author: <PERSON><PERSON><PERSON>
---

### Notable changes

- **assert**
  - The diff output is now a tiny bit improved by sorting object properties when
    inspecting the values that are compared with each other. [#22788](https://github.com/nodejs/node/pull/22788)
- **cli**
  - The options parser now normalizes `_` to `-` in all multi-word command-line
    flags, e.g. `--no_warnings` has the same effect as `--no-warnings`. [#23020](https://github.com/nodejs/node/pull/23020)
  - Added bash completion for the `node` binary. To generate a bash completion
    script, run `node --completion-bash`. The output can be saved to a file
    which can be sourced to enable completion. [#20713](https://github.com/nodejs/node/pull/20713)
- **crypto**
  - Added support for PEM-level encryption. [#23151](https://github.com/nodejs/node/pull/23151)
  - Added an API asymmetric key pair generation. The new methods
    `crypto.generateKeyPair` and `crypto.generateKeyPairSync` can be used to
    generate public and private key pairs. The API supports RSA, DSA and EC and
    a variety of key encodings (both PEM and DER). [#22660](https://github.com/nodejs/node/pull/22660)
- **fs**
  - Added a `recursive` option to `fs.mkdir` and `fs.mkdirSync`. If this option
    is set to true, non-existing parent folders will be automatically created. [#21875](https://github.com/nodejs/node/pull/21875)
- **http2**
  - Added a `'ping'` event to `Http2Session` that is emitted whenever a non-ack
    `PING` is received. [#23009](https://github.com/nodejs/node/pull/23009)
  - Added support for the `ORIGIN` frame. [#22956](https://github.com/nodejs/node/pull/22956)
  - Updated nghttp2 to 1.34.0. This adds RFC 8441 extended connect protocol
    support to allow use of WebSockets over HTTP/2. [#23284](https://github.com/nodejs/node/pull/23284)
- **module**
  - Added `module.createRequireFromPath(filename)`. This new method can be used
    to create a custom require function that will resolve modules relative to
    the filename path. [#19360](https://github.com/nodejs/node/pull/19360)
- **process**
  - Added a `'multipleResolves'` process event that is emitted whenever a
    `Promise` is attempted to be resolved multiple times, e.g. if the `resolve`
    and `reject` functions are both called in a `Promise` executor. [#22218](https://github.com/nodejs/node/pull/22218)
- **url**
  - Added `url.fileURLToPath(url)` and `url.pathToFileURL(path)`. These methods
    can be used to correctly convert between file: URLs and absolute paths. [#22506](https://github.com/nodejs/node/pull/22506)
- **util**
  - Added the `sorted` option to `util.inspect()`. If set to `true`, all
    properties of an object and Set and Map entries will be sorted in the
    returned string. If set to a function, it is used as a compare function. [#22788](https://github.com/nodejs/node/pull/22788)
  - The `util.inspect.custom` symbol is now defined in the global symbol
    registry as `Symbol.for('nodejs.util.inspect.custom')`. [#20857](https://github.com/nodejs/node/pull/20857)
  - Added support for `BigInt` numbers in `util.format()`. [#22097](https://github.com/nodejs/node/pull/22097)
- **V8 API**
  - A number of V8 C++ APIs have been marked as deprecated since they have been
    removed in the upstream repository. Replacement APIs are added where
    necessary. [#23159](https://github.com/nodejs/node/pull/23159)
- **Windows**
  - The Windows msi installer now provides an option to automatically install
    the tools required to build native modules. [#22645](https://github.com/nodejs/node/pull/22645)
- **Workers**
  - Debugging support for Workers using the DevTools protocol has been
    implemented. [#21364](https://github.com/nodejs/node/pull/21364)
  - The public `inspector` module is now enabled in Workers. [#22769](https://github.com/nodejs/node/pull/22769)
- **Added new collaborators**:
  - [digitalinfinity](https://github.com/digitalinfinity) - Hitesh Kanwathirtha

### Commits

- [[`12ff395e35`](https://github.com/nodejs/node/commit/12ff395e35)] - **assert**: remove internal errorCache property (Rich Trott) [#23304](https://github.com/nodejs/node/pull/23304)
- [[`efdb32603c`](https://github.com/nodejs/node/commit/efdb32603c)] - **(SEMVER-MINOR)** **assert**: improve diff output (Ruben Bridgewater) [#22788](https://github.com/nodejs/node/pull/22788)
- [[`9749d48729`](https://github.com/nodejs/node/commit/9749d48729)] - **benchmark**: increase lint compliance (Rich Trott) [#23305](https://github.com/nodejs/node/pull/23305)
- [[`90bbab6f71`](https://github.com/nodejs/node/commit/90bbab6f71)] - **benchmark**: refactor util benchmarks (Ruben Bridgewater) [#22503](https://github.com/nodejs/node/pull/22503)
- [[`2d7e4e0116`](https://github.com/nodejs/node/commit/2d7e4e0116)] - **benchmark,doc,lib,src,test,tools**: fix typos (Brandon Smith) [#23302](https://github.com/nodejs/node/pull/23302)
- [[`e6484c2c11`](https://github.com/nodejs/node/commit/e6484c2c11)] - **build**: restore js2c direct dependency on config.gypi (Refael Ackermann) [#23355](https://github.com/nodejs/node/pull/23355)
- [[`c4aa0331c1`](https://github.com/nodejs/node/commit/c4aa0331c1)] - **build**: make configure script verbose by default (Michaël Zasso) [#23408](https://github.com/nodejs/node/pull/23408)
- [[`cf17759113`](https://github.com/nodejs/node/commit/cf17759113)] - **build**: toggle lint-cpp using verbose (V) variable (Daniel Bevenius) [#23217](https://github.com/nodejs/node/pull/23217)
- [[`b0dc0ca9a9`](https://github.com/nodejs/node/commit/b0dc0ca9a9)] - **build**: make lint-addon-docs quiet (Daniel Bevenius) [#23217](https://github.com/nodejs/node/pull/23217)
- [[`0f236c8d42`](https://github.com/nodejs/node/commit/0f236c8d42)] - **build**: add pgo specific variables to common.gypi (Denys Otrishko) [#23102](https://github.com/nodejs/node/pull/23102)
- [[`de4d688d1f`](https://github.com/nodejs/node/commit/de4d688d1f)] - **build**: cleanup in .gitignore (Refael Ackermann) [#23180](https://github.com/nodejs/node/pull/23180)
- [[`49b0ec4fe7`](https://github.com/nodejs/node/commit/49b0ec4fe7)] - **build**: add loader path to rpath for cctest (Sam Ruby) [#23168](https://github.com/nodejs/node/pull/23168)
- [[`7d21cc2177`](https://github.com/nodejs/node/commit/7d21cc2177)] - **build**: reduce chance of unneeded rebuild (Refael Ackermann) [#23156](https://github.com/nodejs/node/pull/23156)
- [[`aae0eceea0`](https://github.com/nodejs/node/commit/aae0eceea0)] - **build**: encapsulate node/inspector gyp scafolding (Refael Ackermann) [#23156](https://github.com/nodejs/node/pull/23156)
- [[`180099a5ac`](https://github.com/nodejs/node/commit/180099a5ac)] - **build**: enabling pgo at configure (Octavian Soldea) [#21596](https://github.com/nodejs/node/pull/21596)
- [[`f4cffffc96`](https://github.com/nodejs/node/commit/f4cffffc96)] - **build**: add --quiet to lint-cpp (Daniel Bevenius) [#23075](https://github.com/nodejs/node/pull/23075)
- [[`d572f6001a`](https://github.com/nodejs/node/commit/d572f6001a)] - **build**: remove unnecessary Makefile output (Rich Trott) [#23129](https://github.com/nodejs/node/pull/23129)
- [[`fb03faa835`](https://github.com/nodejs/node/commit/fb03faa835)] - **build**: move addons message in Makefile (Rich Trott) [#23114](https://github.com/nodejs/node/pull/23114)
- [[`948dc71664`](https://github.com/nodejs/node/commit/948dc71664)] - **build**: make config verbose on CI (Refael Ackermann) [#22935](https://github.com/nodejs/node/pull/22935)
- [[`b69ed9c80c`](https://github.com/nodejs/node/commit/b69ed9c80c)] - **build**: stop printing execution of lint-md command (Ruben Bridgewater) [#22904](https://github.com/nodejs/node/pull/22904)
- [[`2b8f569388`](https://github.com/nodejs/node/commit/2b8f569388)] - **build,deps**: refactor and fix v8.gyp (Refael Ackermann) [#23182](https://github.com/nodejs/node/pull/23182)
- [[`4db9e36b57`](https://github.com/nodejs/node/commit/4db9e36b57)] - **build,doc**: remove outdated `lint-md-build` (Michaël Zasso) [#22991](https://github.com/nodejs/node/pull/22991)
- [[`c29e5ac5be`](https://github.com/nodejs/node/commit/c29e5ac5be)] - **(SEMVER-MINOR)** **cli**: normalize `_` → `-` when parsing options (Anna Henningsen) [#23020](https://github.com/nodejs/node/pull/23020)
- [[`54ca0e159f`](https://github.com/nodejs/node/commit/54ca0e159f)] - **cluster**: move handle tracking out of utils (cjihrig) [#23131](https://github.com/nodejs/node/pull/23131)
- [[`cb0d8239b7`](https://github.com/nodejs/node/commit/cb0d8239b7)] - **cluster**: use Map to track handles in master (cjihrig) [#23125](https://github.com/nodejs/node/pull/23125)
- [[`0f133eb5a3`](https://github.com/nodejs/node/commit/0f133eb5a3)] - **cluster**: use Map to track handles in cluster child (cjihrig) [#23125](https://github.com/nodejs/node/pull/23125)
- [[`2dd157fbf3`](https://github.com/nodejs/node/commit/2dd157fbf3)] - **cluster**: use Map to track indexes (cjihrig) [#23125](https://github.com/nodejs/node/pull/23125)
- [[`64f840a767`](https://github.com/nodejs/node/commit/64f840a767)] - **cluster**: use Map to track round robin workers (cjihrig) [#23125](https://github.com/nodejs/node/pull/23125)
- [[`22f51a6a83`](https://github.com/nodejs/node/commit/22f51a6a83)] - **cluster**: use Map to track callbacks (cjihrig) [#23125](https://github.com/nodejs/node/pull/23125)
- [[`26c36efa2f`](https://github.com/nodejs/node/commit/26c36efa2f)] - **crypto**: remove node::crypto::CheckResult (Tobias Nießen) [#23225](https://github.com/nodejs/node/pull/23225)
- [[`5f450f3f92`](https://github.com/nodejs/node/commit/5f450f3f92)] - **crypto**: replace goto SSL_CTX_use_certificate_chain (Daniel Bevenius) [#23113](https://github.com/nodejs/node/pull/23113)
- [[`db8d99dbc2`](https://github.com/nodejs/node/commit/db8d99dbc2)] - **crypto**: add virtual dtor to KeyPairGenerationConfig (Daniel Bevenius) [#23215](https://github.com/nodejs/node/pull/23215)
- [[`f98d441461`](https://github.com/nodejs/node/commit/f98d441461)] - **crypto**: extract throwInvalidArgType function (Daniel Bevenius) [#22947](https://github.com/nodejs/node/pull/22947)
- [[`1a21cf13cb`](https://github.com/nodejs/node/commit/1a21cf13cb)] - **crypto**: make PEM parsing RFC7468-compliant (Tobias Nießen) [#23164](https://github.com/nodejs/node/pull/23164)
- [[`9c96573124`](https://github.com/nodejs/node/commit/9c96573124)] - **(SEMVER-MINOR)** **crypto**: add support for PEM-level encryption (Tobias Nießen) [#23151](https://github.com/nodejs/node/pull/23151)
- [[`398c0e03e4`](https://github.com/nodejs/node/commit/398c0e03e4)] - **crypto**: replace gotos (Tobias Nießen) [#23132](https://github.com/nodejs/node/pull/23132)
- [[`a51d839a31`](https://github.com/nodejs/node/commit/a51d839a31)] - **crypto**: remove unnecessary calls to TLS_method() (Daniel Bevenius) [#23077](https://github.com/nodejs/node/pull/23077)
- [[`074b7af7ef`](https://github.com/nodejs/node/commit/074b7af7ef)] - **crypto**: enable auto cert chaining for BoringSSL (Jeremy Apthorp) [#22110](https://github.com/nodejs/node/pull/22110)
- [[`2888f809e3`](https://github.com/nodejs/node/commit/2888f809e3)] - **crypto**: deduplicate cipher initialization code (Tobias Nießen) [#23011](https://github.com/nodejs/node/pull/23011)
- [[`0bc4529a07`](https://github.com/nodejs/node/commit/0bc4529a07)] - **crypto**: remove unnecessary usage of goto (Tobias Nießen) [#23018](https://github.com/nodejs/node/pull/23018)
- [[`cc8219433c`](https://github.com/nodejs/node/commit/cc8219433c)] - **(SEMVER-MINOR)** **crypto**: allow promisifying generateKeyPair (Tobias Nießen) [#22660](https://github.com/nodejs/node/pull/22660)
- [[`421909394c`](https://github.com/nodejs/node/commit/421909394c)] - **(SEMVER-MINOR)** **crypto**: add API for key pair generation (Tobias Nießen) [#22660](https://github.com/nodejs/node/pull/22660)
- [[`76cb52ca11`](https://github.com/nodejs/node/commit/76cb52ca11)] - **deps**: upgrade to libuv 1.23.2 (cjihrig) [#23336](https://github.com/nodejs/node/pull/23336)
- [[`95bdf37265`](https://github.com/nodejs/node/commit/95bdf37265)] - **(SEMVER-MINOR)** **deps**: update nghttp2 to 1.34.0 (James M Snell) [#23284](https://github.com/nodejs/node/pull/23284)
- [[`46c7d0d21f`](https://github.com/nodejs/node/commit/46c7d0d21f)] - **(SEMVER-MINOR)** **deps**: increase V8 deprecation levels (Anna Henningsen) [#23159](https://github.com/nodejs/node/pull/23159)
- [[`e3550f2366`](https://github.com/nodejs/node/commit/e3550f2366)] - **deps**: backport 958b761 from upstream V8 (Matheus Marchini) [#22914](https://github.com/nodejs/node/pull/22914)
- [[`f08373f18d`](https://github.com/nodejs/node/commit/f08373f18d)] - **deps**: cherry-pick 64-bit hash seed commits from V8 (Yang Guo) [#23260](https://github.com/nodejs/node/pull/23260)
- [[`e93c94c327`](https://github.com/nodejs/node/commit/e93c94c327)] - **deps**: add no-strict-aliasing to ICU cflags (Daniel Bevenius) [#23112](https://github.com/nodejs/node/pull/23112)
- [[`5d70652d86`](https://github.com/nodejs/node/commit/5d70652d86)] - **deps**: fix Array.prototype.forEach on v8 6.8 (Mike Stanton) [#22899](https://github.com/nodejs/node/pull/22899)
- [[`e668815a24`](https://github.com/nodejs/node/commit/e668815a24)] - **deps**: cherry-pick dbfcc48 from upstream V8 (Alexey Kozyatinskiy) [#22251](https://github.com/nodejs/node/pull/22251)
- [[`e5efdba75c`](https://github.com/nodejs/node/commit/e5efdba75c)] - **deps**: upgrade to libuv 1.23.1 (cjihrig) [#22997](https://github.com/nodejs/node/pull/22997)
- [[`39d7699a87`](https://github.com/nodejs/node/commit/39d7699a87)] - **deps**: cherry-pick d48bd16 from upstream V8 (Junliang Yan) [#22909](https://github.com/nodejs/node/pull/22909)
- [[`62a2c81214`](https://github.com/nodejs/node/commit/62a2c81214)] - **doc**: simplify and clarify README language (Rich Trott) [#23322](https://github.com/nodejs/node/pull/23322)
- [[`7c0d6ac0bd`](https://github.com/nodejs/node/commit/7c0d6ac0bd)] - **doc**: simplify governance info in README intro (Rich Trott) [#23320](https://github.com/nodejs/node/pull/23320)
- [[`5ff43006d1`](https://github.com/nodejs/node/commit/5ff43006d1)] - **doc**: add link to ABI guide (Gabriel Schulhof)
- [[`9dd47bcf99`](https://github.com/nodejs/node/commit/9dd47bcf99)] - **doc**: fix minor typo in streams.md (Rich Trott) [#23306](https://github.com/nodejs/node/pull/23306)
- [[`a0e8e7fea6`](https://github.com/nodejs/node/commit/a0e8e7fea6)] - **doc**: standardize versions in stream module doc (Rich Trott) [#23306](https://github.com/nodejs/node/pull/23306)
- [[`eee71d6d16`](https://github.com/nodejs/node/commit/eee71d6d16)] - **doc**: add util.inspect() legacy signature (siddhant) [#23216](https://github.com/nodejs/node/pull/23216)
- [[`fbbb25b901`](https://github.com/nodejs/node/commit/fbbb25b901)] - **doc**: edit building-node text (Rich Trott) [#23335](https://github.com/nodejs/node/pull/23335)
- [[`037063c6ee`](https://github.com/nodejs/node/commit/037063c6ee)] - **doc**: remove 72-hour mentions in pull-requests.md (Rich Trott) [#23309](https://github.com/nodejs/node/pull/23309)
- [[`4c54f897f8`](https://github.com/nodejs/node/commit/4c54f897f8)] - **doc**: fix minor typo in n-api.md (Aleksey Chemakin) [#23310](https://github.com/nodejs/node/pull/23310)
- [[`f1cb8ab4bf`](https://github.com/nodejs/node/commit/f1cb8ab4bf)] - **doc**: remove ABI guide (Gabriel Schulhof) [#23303](https://github.com/nodejs/node/pull/23303)
- [[`39e3ef7739`](https://github.com/nodejs/node/commit/39e3ef7739)] - **doc**: Replace vague 'may not' with definitive 'will not' (Mike MacCana) [#23143](https://github.com/nodejs/node/pull/23143)
- [[`11c674549b`](https://github.com/nodejs/node/commit/11c674549b)] - **doc**: update author-ready label terms (Vse Mozhet Byt) [#23249](https://github.com/nodejs/node/pull/23249)
- [[`33e3eb44f7`](https://github.com/nodejs/node/commit/33e3eb44f7)] - **doc**: update onboarding task (Rich Trott) [#23300](https://github.com/nodejs/node/pull/23300)
- [[`df4ade7dc7`](https://github.com/nodejs/node/commit/df4ade7dc7)] - **doc**: use backticks around file names in README.md (Rich Trott) [#23299](https://github.com/nodejs/node/pull/23299)
- [[`80964d36b7`](https://github.com/nodejs/node/commit/80964d36b7)] - **doc**: improve API Documentation text in README (Rich Trott) [#23268](https://github.com/nodejs/node/pull/23268)
- [[`ef0f7e613a`](https://github.com/nodejs/node/commit/ef0f7e613a)] - **doc**: shorten pull request wait time to 48 hours (Rich Trott) [#23082](https://github.com/nodejs/node/pull/23082)
- [[`5b76313059`](https://github.com/nodejs/node/commit/5b76313059)] - **doc**: improve instructions for verifying binaries (Rich Trott) [#23248](https://github.com/nodejs/node/pull/23248)
- [[`6943fa9fc7`](https://github.com/nodejs/node/commit/6943fa9fc7)] - **doc**: shorten intro of README.md (Rich Trott) [#23073](https://github.com/nodejs/node/pull/23073)
- [[`e5bfab0fb6`](https://github.com/nodejs/node/commit/e5bfab0fb6)] - **doc**: add guide about abi stability (Gabriel Schulhof) [#23229](https://github.com/nodejs/node/pull/23229)
- [[`e283206047`](https://github.com/nodejs/node/commit/e283206047)] - **doc**: improve `stream.Writable` ctor encoding option docs (Anna Henningsen) [#23246](https://github.com/nodejs/node/pull/23246)
- [[`bd59d4efbf`](https://github.com/nodejs/node/commit/bd59d4efbf)] - **doc**: fix code snippets in tls.md (Ouyang Yadong) [#23239](https://github.com/nodejs/node/pull/23239)
- [[`27c5e96ffe`](https://github.com/nodejs/node/commit/27c5e96ffe)] - **doc**: leave pull requests open for 72 hours (Rich Trott) [#22275](https://github.com/nodejs/node/pull/22275)
- [[`5836b9fcc8`](https://github.com/nodejs/node/commit/5836b9fcc8)] - **doc**: specify cluster worker.kill() caveat (cjihrig) [#23165](https://github.com/nodejs/node/pull/23165)
- [[`ed01b38295`](https://github.com/nodejs/node/commit/ed01b38295)] - **doc**: use stronger language about security of vm (Gus Caplan) [#23198](https://github.com/nodejs/node/pull/23198)
- [[`eb8721977f`](https://github.com/nodejs/node/commit/eb8721977f)] - **doc**: improve Download section of README (Rich Trott) [#23212](https://github.com/nodejs/node/pull/23212)
- [[`003d85d2d9`](https://github.com/nodejs/node/commit/003d85d2d9)] - **doc**: remove GA tracking (Ben Noordhuis) [#23083](https://github.com/nodejs/node/pull/23083)
- [[`6912376562`](https://github.com/nodejs/node/commit/6912376562)] - **doc**: move gibfahn to TSC Emeritus (Gibson Fahnestock) [#23238](https://github.com/nodejs/node/pull/23238)
- [[`1553e21007`](https://github.com/nodejs/node/commit/1553e21007)] - **doc**: clarify assigning issues to the TSC (Franziska Hinkelmann) [#22759](https://github.com/nodejs/node/pull/22759)
- [[`71901d6b30`](https://github.com/nodejs/node/commit/71901d6b30)] - **doc**: improve Release Types text in README (Rich Trott) [#23190](https://github.com/nodejs/node/pull/23190)
- [[`8191bee313`](https://github.com/nodejs/node/commit/8191bee313)] - **doc**: simplify support section of README (Rich Trott) [#23170](https://github.com/nodejs/node/pull/23170)
- [[`548934d412`](https://github.com/nodejs/node/commit/548934d412)] - **doc**: fix incorrect anchoring (#vcbuild.bat -\> #vcbuildbat) (Justin Lee) [#23211](https://github.com/nodejs/node/pull/23211)
- [[`ce006eb68d`](https://github.com/nodejs/node/commit/ce006eb68d)] - **doc**: fix minor typo (to early -\> too early) (Justin Lee) [#23211](https://github.com/nodejs/node/pull/23211)
- [[`21490c2a87`](https://github.com/nodejs/node/commit/21490c2a87)] - **doc**: remove recommendation to use node-eps (Richard Lau) [#23148](https://github.com/nodejs/node/pull/23148)
- [[`e71a72fbf2`](https://github.com/nodejs/node/commit/e71a72fbf2)] - **doc**: add contents table to CONTRIBUTING.md (ZYSzys) [#23140](https://github.com/nodejs/node/pull/23140)
- [[`818db4036b`](https://github.com/nodejs/node/commit/818db4036b)] - **doc**: move perf tools and APIs to Tier 3 (Matheus Marchini) [#22915](https://github.com/nodejs/node/pull/22915)
- [[`e791abe5ef`](https://github.com/nodejs/node/commit/e791abe5ef)] - **doc**: formalize `auto` usage in C++ style guide (Anna Henningsen) [#23028](https://github.com/nodejs/node/pull/23028)
- [[`310109691b`](https://github.com/nodejs/node/commit/310109691b)] - **doc**: fix casing in stream.md (Sintendo) [#23166](https://github.com/nodejs/node/pull/23166)
- [[`bb5c6892ee`](https://github.com/nodejs/node/commit/bb5c6892ee)] - **doc**: add table of contents in BUILDING.md (ZYSzys) [#23147](https://github.com/nodejs/node/pull/23147)
- [[`cbcf5f88cd`](https://github.com/nodejs/node/commit/cbcf5f88cd)] - **doc**: deeper link to downloads site (Refael Ackermann) [#23084](https://github.com/nodejs/node/pull/23084)
- [[`9109187948`](https://github.com/nodejs/node/commit/9109187948)] - **doc**: update guide for assert team (Rich Trott) [#23085](https://github.com/nodejs/node/pull/23085)
- [[`2731d08c33`](https://github.com/nodejs/node/commit/2731d08c33)] - **doc**: add links for fs.createWriteStream() (Rich Trott) [#23104](https://github.com/nodejs/node/pull/23104)
- [[`9fa3813845`](https://github.com/nodejs/node/commit/9fa3813845)] - **doc**: edit fast-tracking section (cjihrig) [#23059](https://github.com/nodejs/node/pull/23059)
- [[`14327aea7b`](https://github.com/nodejs/node/commit/14327aea7b)] - **doc**: improve instruction to purple merge (Refael Ackermann) [#23007](https://github.com/nodejs/node/pull/23007)
- [[`87565c763a`](https://github.com/nodejs/node/commit/87565c763a)] - **doc**: require two approvals to land changes (Rich Trott) [#22255](https://github.com/nodejs/node/pull/22255)
- [[`e7be1edc49`](https://github.com/nodejs/node/commit/e7be1edc49)] - **doc**: fix optional parameters in n-api.md (Lars-Magnus Skog) [#22998](https://github.com/nodejs/node/pull/22998)
- [[`24073cef6b`](https://github.com/nodejs/node/commit/24073cef6b)] - **doc**: add callback parameters of worker.terminate() (Denis Fäcke) [#23002](https://github.com/nodejs/node/pull/23002)
- [[`6b2e2ff036`](https://github.com/nodejs/node/commit/6b2e2ff036)] - **doc**: improve metadata for http.request (Tobias Nießen) [#22949](https://github.com/nodejs/node/pull/22949)
- [[`91b410259e`](https://github.com/nodejs/node/commit/91b410259e)] - **doc**: add missing metadata for recursive mkdir (Tobias Nießen) [#22949](https://github.com/nodejs/node/pull/22949)
- [[`15c7c57a78`](https://github.com/nodejs/node/commit/15c7c57a78)] - **doc**: add missing metadata for dns.lookup (Tobias Nießen) [#22949](https://github.com/nodejs/node/pull/22949)
- [[`05196893b9`](https://github.com/nodejs/node/commit/05196893b9)] - **doc**: fix heading levels in C++ style guide (Anna Henningsen) [#23061](https://github.com/nodejs/node/pull/23061)
- [[`29a9e8498a`](https://github.com/nodejs/node/commit/29a9e8498a)] - **doc**: remove outdated notes on stdio in workers (Anna Henningsen) [#23054](https://github.com/nodejs/node/pull/23054)
- [[`d3bc862d88`](https://github.com/nodejs/node/commit/d3bc862d88)] - **doc**: match program and console output in synopsis.md (Mohammed Essehemy) [#23006](https://github.com/nodejs/node/pull/23006)
- [[`15b91b9eb8`](https://github.com/nodejs/node/commit/15b91b9eb8)] - **doc**: add links for repl.ReplServer (Rich Trott) [#23005](https://github.com/nodejs/node/pull/23005)
- [[`b0e86ea8d0`](https://github.com/nodejs/node/commit/b0e86ea8d0)] - **doc**: update maintaining V8 guide (Michaël Zasso) [#22913](https://github.com/nodejs/node/pull/22913)
- [[`00dd9738ee`](https://github.com/nodejs/node/commit/00dd9738ee)] - **doc**: specify fast-tracking (Ruben Bridgewater) [#22929](https://github.com/nodejs/node/pull/22929)
- [[`ef5d90dfdc`](https://github.com/nodejs/node/commit/ef5d90dfdc)] - **doc**: add digitalinfinity to collaborators (Hitesh Kanwathirtha) [#22984](https://github.com/nodejs/node/pull/22984)
- [[`b48dc0b667`](https://github.com/nodejs/node/commit/b48dc0b667)] - **doc,test**: fix inspect's sorted compare function (Michaël Zasso) [#22992](https://github.com/nodejs/node/pull/22992)
- [[`d9d9d23191`](https://github.com/nodejs/node/commit/d9d9d23191)] - **errors**: fix ERR_SOCKET_BAD_PORT message (Giovanny Andres Gongora Granada (Gioyik)) [#23015](https://github.com/nodejs/node/pull/23015)
- [[`bb6530b31b`](https://github.com/nodejs/node/commit/bb6530b31b)] - **fs**: consistently return symlink type from readdir (Klaus Meinhardt) [#22808](https://github.com/nodejs/node/pull/22808)
- [[`7e45daf494`](https://github.com/nodejs/node/commit/7e45daf494)] - **(SEMVER-MINOR)** **fs**: implement mkdir recursive (mkdirp) (Benjamin Coe) [#21875](https://github.com/nodejs/node/pull/21875)
- [[`c29734c9d6`](https://github.com/nodejs/node/commit/c29734c9d6)] - **fs**: improve fs.watch ENOSPC error message (Anna Henningsen) [#21846](https://github.com/nodejs/node/pull/21846)
- [[`7b327ea909`](https://github.com/nodejs/node/commit/7b327ea909)] - **(SEMVER-MINOR)** **http2**: add RFC 8441 extended connect protocol support (James M Snell) [#23284](https://github.com/nodejs/node/pull/23284)
- [[`001881f33e`](https://github.com/nodejs/node/commit/001881f33e)] - **http2**: set nghttp2_option_set_no_closed_streams (David Halls) [#23134](https://github.com/nodejs/node/pull/23134)
- [[`8fe62f8d38`](https://github.com/nodejs/node/commit/8fe62f8d38)] - **http2**: don't send trailers on a closed connection (André Cruz) [#23146](https://github.com/nodejs/node/pull/23146)
- [[`d1826fed41`](https://github.com/nodejs/node/commit/d1826fed41)] - **http2**: close fd in doSendFileFD() (cjihrig) [#23047](https://github.com/nodejs/node/pull/23047)
- [[`8bf004b96d`](https://github.com/nodejs/node/commit/8bf004b96d)] - **(SEMVER-MINOR)** **http2**: add ping event (James M Snell) [#23009](https://github.com/nodejs/node/pull/23009)
- [[`badc38f305`](https://github.com/nodejs/node/commit/badc38f305)] - **http2**: do not falsely emit 'aborted' on push (Anatoli Papirovski) [#22878](https://github.com/nodejs/node/pull/22878)
- [[`24675a4306`](https://github.com/nodejs/node/commit/24675a4306)] - **(SEMVER-MINOR)** **http2**: add origin frame support (James M Snell) [#22956](https://github.com/nodejs/node/pull/22956)
- [[`89fe9edd08`](https://github.com/nodejs/node/commit/89fe9edd08)] - **http2**: check if stream is not destroyed before sending trailers (Matteo Collina) [#22896](https://github.com/nodejs/node/pull/22896)
- [[`aa48192f9d`](https://github.com/nodejs/node/commit/aa48192f9d)] - **inspector**: add virtual destructor to WorkerDelegate (Daniel Bevenius) [#23215](https://github.com/nodejs/node/pull/23215)
- [[`16f7f52b24`](https://github.com/nodejs/node/commit/16f7f52b24)] - **inspector**: workers debugging (Eugene Ostroukhov) [#21364](https://github.com/nodejs/node/pull/21364)
- [[`f66e9abcb3`](https://github.com/nodejs/node/commit/f66e9abcb3)] - **inspector**: implemented V8InspectorClient::resourceNameToUrl (Alexey Kozyatinskiy) [#22251](https://github.com/nodejs/node/pull/22251)
- [[`1c3a2ebfcf`](https://github.com/nodejs/node/commit/1c3a2ebfcf)] - **inspector**: enable Inspector JS API in workers (Eugene Ostroukhov) [#22769](https://github.com/nodejs/node/pull/22769)
- [[`c40e2dd6c9`](https://github.com/nodejs/node/commit/c40e2dd6c9)] - **lib**: reword help text for clarity (Gireesh Punathil) [#23016](https://github.com/nodejs/node/pull/23016)
- [[`f38eff29e7`](https://github.com/nodejs/node/commit/f38eff29e7)] - **lib**: change abstract equal to strict equal (ZYSzys) [#22974](https://github.com/nodejs/node/pull/22974)
- [[`0140a98e05`](https://github.com/nodejs/node/commit/0140a98e05)] - **lib**: make DOMException attributes configurable and enumerable (Joyee Cheung) [#22550](https://github.com/nodejs/node/pull/22550)
- [[`5e7b1082d9`](https://github.com/nodejs/node/commit/5e7b1082d9)] - **lib**: set Symbol.toStringTag of DOMException (Joyee Cheung) [#22933](https://github.com/nodejs/node/pull/22933)
- [[`a7f4d5e134`](https://github.com/nodejs/node/commit/a7f4d5e134)] - **lib**: refactor variable declarations (ZYSzys) [#22643](https://github.com/nodejs/node/pull/22643)
- [[`fb68ef2e74`](https://github.com/nodejs/node/commit/fb68ef2e74)] - **lib**: added common.restoreStderr(); to end of file (Mark Abel) [#22487](https://github.com/nodejs/node/pull/22487)
- [[`600c225439`](https://github.com/nodejs/node/commit/600c225439)] - **(SEMVER-MINOR)** **module**: add createRequireFunction method (Gus Caplan) [#19360](https://github.com/nodejs/node/pull/19360)
- [[`a65bb42551`](https://github.com/nodejs/node/commit/a65bb42551)] - **net**: use connect() instead of connect.call() (Jackson Tian) [#23289](https://github.com/nodejs/node/pull/23289)
- [[`5a306748e9`](https://github.com/nodejs/node/commit/5a306748e9)] - **process**: allow reading from stdout/stderr sockets (Anna Henningsen) [#23053](https://github.com/nodejs/node/pull/23053)
- [[`66484b82c4`](https://github.com/nodejs/node/commit/66484b82c4)] - **(SEMVER-MINOR)** **process**: add `multipleResolves` event (Ruben Bridgewater) [#22218](https://github.com/nodejs/node/pull/22218)
- [[`e16dd6d165`](https://github.com/nodejs/node/commit/e16dd6d165)] - **repl**: refactor ERR_SCRIPT_EXECUTION_INTERRUPTED stack handling (Ruben Bridgewater) [#22436](https://github.com/nodejs/node/pull/22436)
- [[`b1ffda6c17`](https://github.com/nodejs/node/commit/b1ffda6c17)] - **repl**: improve error output (Ruben Bridgewater) [#22436](https://github.com/nodejs/node/pull/22436)
- [[`cd69e1b6c3`](https://github.com/nodejs/node/commit/cd69e1b6c3)] - **src**: fix ToObject() usage in node_http_parser.cc (cjihrig) [#23314](https://github.com/nodejs/node/pull/23314)
- [[`5228ec4410`](https://github.com/nodejs/node/commit/5228ec4410)] - **src**: fix ToObject() usage in exceptions.cc (cjihrig) [#23314](https://github.com/nodejs/node/pull/23314)
- [[`4d761d4224`](https://github.com/nodejs/node/commit/4d761d4224)] - **src**: reduce variable scope in stream_base.cc (cjihrig) [#23297](https://github.com/nodejs/node/pull/23297)
- [[`740741b279`](https://github.com/nodejs/node/commit/740741b279)] - **src**: reduce variable scope in node_worker.cc (cjihrig) [#23297](https://github.com/nodejs/node/pull/23297)
- [[`56c2f5702f`](https://github.com/nodejs/node/commit/56c2f5702f)] - **src**: reduce variable scope in node_trace_writer.cc (cjihrig) [#23297](https://github.com/nodejs/node/pull/23297)
- [[`046fd987e4`](https://github.com/nodejs/node/commit/046fd987e4)] - **src**: reduce variable scope in node_url.cc (cjihrig) [#23297](https://github.com/nodejs/node/pull/23297)
- [[`36c430796b`](https://github.com/nodejs/node/commit/36c430796b)] - **src**: remove unneeded variables in node_crypto.cc (cjihrig) [#23297](https://github.com/nodejs/node/pull/23297)
- [[`902ba0ecb4`](https://github.com/nodejs/node/commit/902ba0ecb4)] - **src**: reduce variable scope in module_wrap.cc (cjihrig) [#23297](https://github.com/nodejs/node/pull/23297)
- [[`402867c0a9`](https://github.com/nodejs/node/commit/402867c0a9)] - **src**: reduce variable scope in cares_wrap.cc (cjihrig) [#23297](https://github.com/nodejs/node/pull/23297)
- [[`315bf257e5`](https://github.com/nodejs/node/commit/315bf257e5)] - **src**: fix ToObject() usage in node_crypto.cc (cjihrig) [#23298](https://github.com/nodejs/node/pull/23298)
- [[`950ccee386`](https://github.com/nodejs/node/commit/950ccee386)] - **src**: name EmbededderGraph edges and use class names for nodes (Joyee Cheung) [#23072](https://github.com/nodejs/node/pull/23072)
- [[`390fc85ff0`](https://github.com/nodejs/node/commit/390fc85ff0)] - **src**: implement the new EmbedderGraph::AddEdge() (Joyee Cheung) [#22106](https://github.com/nodejs/node/pull/22106)
- [[`5a8396796d`](https://github.com/nodejs/node/commit/5a8396796d)] - **src**: use JS inheritance for `AsyncWrap` (Anna Henningsen) [#23094](https://github.com/nodejs/node/pull/23094)
- [[`894210ec12`](https://github.com/nodejs/node/commit/894210ec12)] - **src**: add virtual desctructor to Options class (Daniel Bevenius) [#23215](https://github.com/nodejs/node/pull/23215)
- [[`8f5fb6f90c`](https://github.com/nodejs/node/commit/8f5fb6f90c)] - **src**: clean up zlib write code (Anna Henningsen) [#23183](https://github.com/nodejs/node/pull/23183)
- [[`2da6f622dc`](https://github.com/nodejs/node/commit/2da6f622dc)] - **(SEMVER-MINOR)** **src**: deprecate `UVException()` without `Isolate*` (Anna Henningsen) [#23175](https://github.com/nodejs/node/pull/23175)
- [[`e9a0cffbd6`](https://github.com/nodejs/node/commit/e9a0cffbd6)] - **(SEMVER-MINOR)** **src**: deprecate V8 date conversion helpers (Anna Henningsen) [#23179](https://github.com/nodejs/node/pull/23179)
- [[`a2c1ce24b5`](https://github.com/nodejs/node/commit/a2c1ce24b5)] - **src**: fix indentation for `AsyncResource` (Anna Henningsen) [#23177](https://github.com/nodejs/node/pull/23177)
- [[`64689edf76`](https://github.com/nodejs/node/commit/64689edf76)] - **src**: remove unused using declarations (Daniel Bevenius) [#23120](https://github.com/nodejs/node/pull/23120)
- [[`0202c6c808`](https://github.com/nodejs/node/commit/0202c6c808)] - **src**: remove unused locale.h (Daniel Bevenius) [#23120](https://github.com/nodejs/node/pull/23120)
- [[`20a4f14c57`](https://github.com/nodejs/node/commit/20a4f14c57)] - **src**: make req_wrap a unique_ptr in AfterConnect (Daniel Bevenius) [#23115](https://github.com/nodejs/node/pull/23115)
- [[`ce7fad5b79`](https://github.com/nodejs/node/commit/ce7fad5b79)] - **src**: use unique_ptr for obj in SetWeak lambda (Daniel Bevenius) [#23117](https://github.com/nodejs/node/pull/23117)
- [[`7d7dc16240`](https://github.com/nodejs/node/commit/7d7dc16240)] - **src**: unique_ptrs in few lambdas (Gireesh Punathil) [#23124](https://github.com/nodejs/node/pull/23124)
- [[`4bd3b6e332`](https://github.com/nodejs/node/commit/4bd3b6e332)] - **src**: refactor crypto code with RAII cleanup (Gireesh Punathil) [#23014](https://github.com/nodejs/node/pull/23014)
- [[`f3d09b6e4f`](https://github.com/nodejs/node/commit/f3d09b6e4f)] - **src**: simplify `MessagePort` construction code a bit (Anna Henningsen) [#23036](https://github.com/nodejs/node/pull/23036)
- [[`4d61c34b9e`](https://github.com/nodejs/node/commit/4d61c34b9e)] - **src**: fix a typo in the comment (Gireesh Punathil) [#23078](https://github.com/nodejs/node/pull/23078)
- [[`fa833828b2`](https://github.com/nodejs/node/commit/fa833828b2)] - **src**: added URL::FromFilePath method (Alexey Kozyatinskiy) [#22251](https://github.com/nodejs/node/pull/22251)
- [[`109aa63910`](https://github.com/nodejs/node/commit/109aa63910)] - **src**: cache and resue isolate and contex pointers (Gireesh Punathil) [#23024](https://github.com/nodejs/node/pull/23024)
- [[`2f659a3d74`](https://github.com/nodejs/node/commit/2f659a3d74)] - **src**: use RAII cleanup in node_i18n.cc (Anna Henningsen) [#23021](https://github.com/nodejs/node/pull/23021)
- [[`90f1200be9`](https://github.com/nodejs/node/commit/90f1200be9)] - **src**: define zlib constants in node_zlib.cc (Anna Henningsen) [#23019](https://github.com/nodejs/node/pull/23019)
- [[`d72867ec0c`](https://github.com/nodejs/node/commit/d72867ec0c)] - **src**: make `ZCtx::Init()` non-static (Anna Henningsen) [#23019](https://github.com/nodejs/node/pull/23019)
- [[`56b1a3cf6e`](https://github.com/nodejs/node/commit/56b1a3cf6e)] - **src**: refactor zlib dictionary to STL vector (Anna Henningsen) [#23019](https://github.com/nodejs/node/pull/23019)
- [[`76453f1878`](https://github.com/nodejs/node/commit/76453f1878)] - **src**: replace deprecated uses of FunctionTemplate::GetFunction (Andreas Haas) [#22993](https://github.com/nodejs/node/pull/22993)
- [[`cb3062aa42`](https://github.com/nodejs/node/commit/cb3062aa42)] - **src**: remove calls to SetWrapperClassId() (Joyee Cheung) [#22975](https://github.com/nodejs/node/pull/22975)
- [[`ab032e4ff4`](https://github.com/nodejs/node/commit/ab032e4ff4)] - **src**: refactor win32 `DebugProcess()` to use RAII cleanup (Anna Henningsen) [#22981](https://github.com/nodejs/node/pull/22981)
- [[`b6cd18517c`](https://github.com/nodejs/node/commit/b6cd18517c)] - **src**: add CheckOptions to Options classes (Daniel Bevenius) [#22943](https://github.com/nodejs/node/pull/22943)
- [[`ace6e07f0e`](https://github.com/nodejs/node/commit/ace6e07f0e)] - **src**: initialize pid variable before goto (Jeremy Apthorp) [#22961](https://github.com/nodejs/node/pull/22961)
- [[`25bf1f5e28`](https://github.com/nodejs/node/commit/25bf1f5e28)] - **stream**: improve buffer list inspection (Ruben Bridgewater) [#23109](https://github.com/nodejs/node/pull/23109)
- [[`2b77b94c05`](https://github.com/nodejs/node/commit/2b77b94c05)] - **streams**: refactor ReadableStream asyncIterator creation and a few fixes (Gus Caplan) [#23042](https://github.com/nodejs/node/pull/23042)
- [[`df54db667c`](https://github.com/nodejs/node/commit/df54db667c)] - **test**: remove internal errorCache property (Rich Trott) [#23304](https://github.com/nodejs/node/pull/23304)
- [[`70abcf2d10`](https://github.com/nodejs/node/commit/70abcf2d10)] - **test**: remove eslint-disable from fixtures (Rich Trott) [#23345](https://github.com/nodejs/node/pull/23345)
- [[`b2d3445644`](https://github.com/nodejs/node/commit/b2d3445644)] - **test**: read() on dir on AIX does not return EISDIR (Ben Noordhuis) [#23330](https://github.com/nodejs/node/pull/23330)
- [[`b523f13fa2`](https://github.com/nodejs/node/commit/b523f13fa2)] - **test**: add module require tests for certain package.json errors (Tom White) [#23285](https://github.com/nodejs/node/pull/23285)
- [[`45e0080cf1`](https://github.com/nodejs/node/commit/45e0080cf1)] - **test**: remove flaky designation for test (Rich Trott) [#22856](https://github.com/nodejs/node/pull/22856)
- [[`085de6fe18`](https://github.com/nodejs/node/commit/085de6fe18)] - **test**: swap arguments in strictEqual() (Duarte David) [#23204](https://github.com/nodejs/node/pull/23204)
- [[`a070842e97`](https://github.com/nodejs/node/commit/a070842e97)] - **test**: remove obsolete domain test (Julien Gilli)
- [[`cb68188805`](https://github.com/nodejs/node/commit/cb68188805)] - **test**: add stdin writable regression test (Anna Henningsen) [#23053](https://github.com/nodejs/node/pull/23053)
- [[`06b5ef3868`](https://github.com/nodejs/node/commit/06b5ef3868)] - **test**: terminate cluster worker in infinite loop (cjihrig) [#23165](https://github.com/nodejs/node/pull/23165)
- [[`9352d9d596`](https://github.com/nodejs/node/commit/9352d9d596)] - **test**: harden test-gc-http-client-timeout (Denys Otrishko) [#23184](https://github.com/nodejs/node/pull/23184)
- [[`748d9d22fa`](https://github.com/nodejs/node/commit/748d9d22fa)] - **test**: add process.stdin.end() TTY regression test (Matteo Collina) [#23051](https://github.com/nodejs/node/pull/23051)
- [[`44db98a7b6`](https://github.com/nodejs/node/commit/44db98a7b6)] - **test**: add more descriptive err message to assert (Josh Broomfield) [#23118](https://github.com/nodejs/node/pull/23118)
- [[`b2a1cf3793`](https://github.com/nodejs/node/commit/b2a1cf3793)] - **test**: mark some flakes (Refael Ackermann) [#23208](https://github.com/nodejs/node/pull/23208)
- [[`9b30a635e0`](https://github.com/nodejs/node/commit/9b30a635e0)] - **test**: improve test-gc-http-client (Rich Trott) [#23145](https://github.com/nodejs/node/pull/23145)
- [[`23525b0547`](https://github.com/nodejs/node/commit/23525b0547)] - **test**: replace localhost with os.hostname in fs-readfilesync (Denys Otrishko) [#23101](https://github.com/nodejs/node/pull/23101)
- [[`bebd7b2cdc`](https://github.com/nodejs/node/commit/bebd7b2cdc)] - **test**: fix flaky test-gc-net-timeout (Rich Trott) [#23139](https://github.com/nodejs/node/pull/23139)
- [[`efeb49d224`](https://github.com/nodejs/node/commit/efeb49d224)] - **test**: increase test coverage for fs.promises read (Jennifer Bland) [#22800](https://github.com/nodejs/node/pull/22800)
- [[`39c2a3f1c1`](https://github.com/nodejs/node/commit/39c2a3f1c1)] - **test**: improve test-gc-http-client-connaborted (Rich Trott) [#23091](https://github.com/nodejs/node/pull/23091)
- [[`48c1c428f9`](https://github.com/nodejs/node/commit/48c1c428f9)] - **test**: improve debugging information for http2 test (Rich Trott) [#23058](https://github.com/nodejs/node/pull/23058)
- [[`f486186e25`](https://github.com/nodejs/node/commit/f486186e25)] - **test**: remove setImmediate from timeout test (Rich Trott) [#23058](https://github.com/nodejs/node/pull/23058)
- [[`85c4ecb8a5`](https://github.com/nodejs/node/commit/85c4ecb8a5)] - **test**: test undefined in util (ZYSzys) [#22741](https://github.com/nodejs/node/pull/22741)
- [[`3e4af49157`](https://github.com/nodejs/node/commit/3e4af49157)] - **test**: add dns.onlookupall() to increase coverage (Masashi Hirano) [#22985](https://github.com/nodejs/node/pull/22985)
- [[`d68dfa9243`](https://github.com/nodejs/node/commit/d68dfa9243)] - **test**: console.table when `null` in data (ZYSzys) [#22974](https://github.com/nodejs/node/pull/22974)
- [[`4cda83dae2`](https://github.com/nodejs/node/commit/4cda83dae2)] - **test**: improve test-gc-http-client-onerror (Rich Trott) [#23090](https://github.com/nodejs/node/pull/23090)
- [[`52c6ee789b`](https://github.com/nodejs/node/commit/52c6ee789b)] - **test**: improve test-gc-http-client-timeout (Rich Trott) [#23088](https://github.com/nodejs/node/pull/23088)
- [[`69d3c08be1`](https://github.com/nodejs/node/commit/69d3c08be1)] - **test**: improve reliability of test-gc-http-client (Rich Trott) [#23087](https://github.com/nodejs/node/pull/23087)
- [[`a566f0f43e`](https://github.com/nodejs/node/commit/a566f0f43e)] - **test**: improve reliability of test-gc-net-timeout (Rich Trott) [#23086](https://github.com/nodejs/node/pull/23086)
- [[`c54e42362c`](https://github.com/nodejs/node/commit/c54e42362c)] - **test**: remove unnecessary assertions (Anna Henningsen) [#23040](https://github.com/nodejs/node/pull/23040)
- [[`adaf530cdb`](https://github.com/nodejs/node/commit/adaf530cdb)] - **test**: check parameter type of fs.mkdir() (Masashi Hirano) [#22616](https://github.com/nodejs/node/pull/22616)
- [[`a3b3485b34`](https://github.com/nodejs/node/commit/a3b3485b34)] - **test**: add comment describing test-fs-mkdir (Jennifer Bland) [#22424](https://github.com/nodejs/node/pull/22424)
- [[`5d4bec3e0f`](https://github.com/nodejs/node/commit/5d4bec3e0f)] - **test**: fix flaky sequential/test-fs-watch-system-limit (Anna Henningsen) [#23038](https://github.com/nodejs/node/pull/23038)
- [[`9de6b26a0c`](https://github.com/nodejs/node/commit/9de6b26a0c)] - **test**: remove string literals for strictEquals/notStrictEquals (MaleDong) [#22891](https://github.com/nodejs/node/pull/22891)
- [[`43e3cf93e8`](https://github.com/nodejs/node/commit/43e3cf93e8)] - **test**: do not export common.leakedGlobals() (Rich Trott) [#22965](https://github.com/nodejs/node/pull/22965)
- [[`70ccec2d91`](https://github.com/nodejs/node/commit/70ccec2d91)] - **test**: increase coverage for worker_threads (Rich Trott) [#22942](https://github.com/nodejs/node/pull/22942)
- [[`83278b23a7`](https://github.com/nodejs/node/commit/83278b23a7)] - **test**: fix flaky key pair generation test (Tobias Nießen) [#22980](https://github.com/nodejs/node/pull/22980)
- [[`d38ce82756`](https://github.com/nodejs/node/commit/d38ce82756)] - **test**: remove common.hasSmallICU (Rich Trott) [#22937](https://github.com/nodejs/node/pull/22937)
- [[`dcce2d67ae`](https://github.com/nodejs/node/commit/dcce2d67ae)] - **test**: add tests to check error in dns.lookupService. (Masashi Hirano) [#22908](https://github.com/nodejs/node/pull/22908)
- [[`079760f28b`](https://github.com/nodejs/node/commit/079760f28b)] - **test**: mark some tests as flaky (João Reis) [#22941](https://github.com/nodejs/node/pull/22941)
- [[`4436a3d660`](https://github.com/nodejs/node/commit/4436a3d660)] - **timers**: use custom inspection for linked lists (Ruben Bridgewater) [#23108](https://github.com/nodejs/node/pull/23108)
- [[`4b976df9e2`](https://github.com/nodejs/node/commit/4b976df9e2)] - **tls**: fix DEP0083 after upgrading to OpenSSL 1.1.0 (Tobias Nießen) [#22953](https://github.com/nodejs/node/pull/22953)
- [[`acc4a761b7`](https://github.com/nodejs/node/commit/acc4a761b7)] - **tools**: make Travis commit linting more robust (Rich Trott) [#23397](https://github.com/nodejs/node/pull/23397)
- [[`e2a6e051b3`](https://github.com/nodejs/node/commit/e2a6e051b3)] - **tools**: apply linting to first commit in PRs (Rich Trott) [#22452](https://github.com/nodejs/node/pull/22452)
- [[`42e67a4e68`](https://github.com/nodejs/node/commit/42e67a4e68)] - **tools**: remove useless assignment from configure.py (Gus Caplan) [#23200](https://github.com/nodejs/node/pull/23200)
- [[`1010ef5df7`](https://github.com/nodejs/node/commit/1010ef5df7)] - **tools**: fix ICU shrinker and docs (Steven R. Loomis) [#23266](https://github.com/nodejs/node/pull/23266)
- [[`d7031df415`](https://github.com/nodejs/node/commit/d7031df415)] - **tools**: allow input for TTY tests (Anna Henningsen) [#23053](https://github.com/nodejs/node/pull/23053)
- [[`1a92335543`](https://github.com/nodejs/node/commit/1a92335543)] - **tools**: unify .editorconfig rules for 2-space (Roman Reiss) [#23163](https://github.com/nodejs/node/pull/23163)
- [[`ef00e5c967`](https://github.com/nodejs/node/commit/ef00e5c967)] - **tools**: fix cpplint --quiet option (Daniel Bevenius) [#23075](https://github.com/nodejs/node/pull/23075)
- [[`1cbc2ee034`](https://github.com/nodejs/node/commit/1cbc2ee034)] - **tools**: update ESLint to 5.6.1 (cjihrig) [#23149](https://github.com/nodejs/node/pull/23149)
- [[`f6fc7e7aa5`](https://github.com/nodejs/node/commit/f6fc7e7aa5)] - **tools**: .eslintrc.js messages "default" typo style (Lovingly) [#22868](https://github.com/nodejs/node/pull/22868)
- [[`a22485d8f1`](https://github.com/nodejs/node/commit/a22485d8f1)] - **(SEMVER-MINOR)** **tools**: add bash completion for node (Daniel Bevenius) [#20713](https://github.com/nodejs/node/pull/20713)
- [[`a6562685c3`](https://github.com/nodejs/node/commit/a6562685c3)] - **_Revert_** "**tools,gyp**: don't force build actions with multiple outputs" (Sakthipriyan Vairamani (thefourtheye)) [#23257](https://github.com/nodejs/node/pull/23257)
- [[`73917387f4`](https://github.com/nodejs/node/commit/73917387f4)] - **tools,gyp**: cosmetic change to GYP `make` generator outputs (Refael Ackermann) [#23156](https://github.com/nodejs/node/pull/23156)
- [[`d8cedf545e`](https://github.com/nodejs/node/commit/d8cedf545e)] - **tools,gyp**: don't force build actions with multiple outputs (Refael Ackermann) [#23156](https://github.com/nodejs/node/pull/23156)
- [[`bf17708aa2`](https://github.com/nodejs/node/commit/bf17708aa2)] - **tty**: document `WriteStream.cursorTo()` and others (Umang Raghuvanshi) [#22893](https://github.com/nodejs/node/pull/22893)
- [[`9c36827e94`](https://github.com/nodejs/node/commit/9c36827e94)] - **(SEMVER-MINOR)** **tty**: make `readStream.setRawMode()` return `this` (Alexander Mills) [#22950](https://github.com/nodejs/node/pull/22950)
- [[`36028caf8c`](https://github.com/nodejs/node/commit/36028caf8c)] - **tty**: handle setRawMode errors (Nikolai Vavilov) [#22886](https://github.com/nodejs/node/pull/22886)
- [[`d22ee2c698`](https://github.com/nodejs/node/commit/d22ee2c698)] - **url**: use foreach-style C++ loop (Tobias Nießen) [#23138](https://github.com/nodejs/node/pull/23138)
- [[`f1b1b73c2b`](https://github.com/nodejs/node/commit/f1b1b73c2b)] - **(SEMVER-MINOR)** **url**: provide pathToFileURL and fileURLToPath (guybedford) [#22506](https://github.com/nodejs/node/pull/22506)
- [[`384221e75c`](https://github.com/nodejs/node/commit/384221e75c)] - **(SEMVER-MINOR)** **util**: support BigInt in util.format (Masashi Hirano) [#22097](https://github.com/nodejs/node/pull/22097)
- [[`77de1beaf0`](https://github.com/nodejs/node/commit/77de1beaf0)] - **util**: move inspect in separate file (Ruben Bridgewater) [#22845](https://github.com/nodejs/node/pull/22845)
- [[`f2dfebb7b4`](https://github.com/nodejs/node/commit/f2dfebb7b4)] - **util**: update definition of DISALLOW_COPY_AND_ASSIGN macro (Nitish Sakhawalkar) [#23092](https://github.com/nodejs/node/pull/23092)
- [[`68eaa8789a`](https://github.com/nodejs/node/commit/68eaa8789a)] - **(SEMVER-MINOR)** **util**: add order option to `.inspect()` (Ruben Bridgewater) [#22788](https://github.com/nodejs/node/pull/22788)
- [[`a2a1ebfecf`](https://github.com/nodejs/node/commit/a2a1ebfecf)] - **(SEMVER-MINOR)** **util**: use a shared symbol for util.inspect.custom (chocolateboy) [#20857](https://github.com/nodejs/node/pull/20857)
- [[`077e7e0ce7`](https://github.com/nodejs/node/commit/077e7e0ce7)] - **util**: fix indentationLvl when exceeding max call stack size (Ruben Bridgewater) [#22787](https://github.com/nodejs/node/pull/22787)
- [[`5a13e66088`](https://github.com/nodejs/node/commit/5a13e66088)] - **util**: improve inspect performance (Ruben Bridgewater) [#22503](https://github.com/nodejs/node/pull/22503)
- [[`92e0f38a65`](https://github.com/nodejs/node/commit/92e0f38a65)] - **util**: remove outdated TODO (Ruben Bridgewater) [#22503](https://github.com/nodejs/node/pull/22503)
- [[`3b895d1258`](https://github.com/nodejs/node/commit/3b895d1258)] - **win,msi**: display license notes before installing tools (João Reis) [#23044](https://github.com/nodejs/node/pull/23044)
- [[`cf284c80a9`](https://github.com/nodejs/node/commit/cf284c80a9)] - **win,msi**: install Boxstarter from elevated shell (João Reis) [#22988](https://github.com/nodejs/node/pull/22988)
- [[`2b7e18dec5`](https://github.com/nodejs/node/commit/2b7e18dec5)] - **win,msi**: highlight installation of 3rd-party tools (Tobias Nießen) [#23003](https://github.com/nodejs/node/pull/23003)
- [[`ebf36cd180`](https://github.com/nodejs/node/commit/ebf36cd180)] - **(SEMVER-MINOR)** **win,msi**: install tools for native modules (João Reis) [#22645](https://github.com/nodejs/node/pull/22645)
- [[`c34db7a9c2`](https://github.com/nodejs/node/commit/c34db7a9c2)] - **worker**: reduce `MessagePort` prototype to documented API (Anna Henningsen) [#23037](https://github.com/nodejs/node/pull/23037)
- [[`2e30a6897d`](https://github.com/nodejs/node/commit/2e30a6897d)] - **worker**: hide MessagePort init function behind symbol (Anna Henningsen) [#23037](https://github.com/nodejs/node/pull/23037)
- [[`b5889d0b84`](https://github.com/nodejs/node/commit/b5889d0b84)] - **worker**: only stop inspector if started (Anna Henningsen) [#22927](https://github.com/nodejs/node/pull/22927)
- [[`7639390032`](https://github.com/nodejs/node/commit/7639390032)] - **worker,coverage**: support V8 coverage generation (Anna Henningsen) [#22928](https://github.com/nodejs/node/pull/22928)
- [[`18cbde5ade`](https://github.com/nodejs/node/commit/18cbde5ade)] - **zlib**: simplify flushing mechanism (Anna Henningsen) [#23186](https://github.com/nodejs/node/pull/23186)
- [[`e749a28c55`](https://github.com/nodejs/node/commit/e749a28c55)] - **zlib**: use common owner symbol to access JS wrapper (Anna Henningsen) [#23189](https://github.com/nodejs/node/pull/23189)
- [[`a6b55c73b0`](https://github.com/nodejs/node/commit/a6b55c73b0)] - **zlib**: move, rename, document internal params() cb (Anna Henningsen) [#23187](https://github.com/nodejs/node/pull/23187)

Windows 32-bit Installer: https://nodejs.org/dist/v10.12.0/node-v10.12.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v10.12.0/node-v10.12.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v10.12.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v10.12.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v10.12.0/node-v10.12.0.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v10.12.0/node-v10.12.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v10.12.0/node-v10.12.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v10.12.0/node-v10.12.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v10.12.0/node-v10.12.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v10.12.0/node-v10.12.0-aix-ppc64.tar.gz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v10.12.0/node-v10.12.0-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v10.12.0/node-v10.12.0-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v10.12.0/node-v10.12.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v10.12.0/node-v10.12.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v10.12.0/node-v10.12.0.tar.gz \
Other release files: https://nodejs.org/dist/v10.12.0/ \
Documentation: https://nodejs.org/docs/v10.12.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

eb8cb1aa1f3ee9bc376d3654ce6410980e9c42b5d09eddca73f5d624bec22861  node-v10.12.0-aix-ppc64.tar.gz
f275c901b9aeaacea2bf22648329c2e9ade5e1ff63a446b83446d5d4e19464cc  node-v10.12.0-darwin-x64.tar.gz
4ae6608a149a3005864a31fa4f462d49cd833814c561aa3ab22757519f62d5c1  node-v10.12.0-darwin-x64.tar.xz
903d0a0d28c82907bcc0bf500384096dcf330f55a78589a532658fd43dd92cc2  node-v10.12.0-headers.tar.gz
9432aa34583a3b19ca9be8229676ff138223872e4c2412976d00fc36e4b54f34  node-v10.12.0-headers.tar.xz
35108e762de4d449ae012c69c5927023806b2e447070d712630e78ab1f1d2cd5  node-v10.12.0-linux-arm64.tar.gz
b327deca4e380559c8c1fbacb9e668b49127ea998de4c8fe83912838cf786786  node-v10.12.0-linux-arm64.tar.xz
b051ddfb00fa949c0201821e36f963be9f2b763e240dbdcaa4aa03cfa1ef8a85  node-v10.12.0-linux-armv6l.tar.gz
88b507572b151fe02b450d4b6fb1fa7b54adb65392845045dd247a0c012f56c8  node-v10.12.0-linux-armv6l.tar.xz
09125bb4a8e80a4b04f6723014a7e33d334fee83bf8e187e25352279dcc0f0a7  node-v10.12.0-linux-armv7l.tar.gz
8497633db68a26ae0cc57791dc06a60e2d6a9319b19cfa717d2eb822952afc7a  node-v10.12.0-linux-armv7l.tar.xz
8731c5b1fe53874378f8888e9b75196a8c22a5d7e67be9be459cae92a784c7b6  node-v10.12.0-linux-ppc64le.tar.gz
9a0558fcbd63bf582125a402ca3b31ae0ac13261ba66eb34c5fe0755ae0a90d6  node-v10.12.0-linux-ppc64le.tar.xz
1da89b1617a03cec8b6c497352afb9eb2578b5ae539433bc945d0eb642f7119f  node-v10.12.0-linux-s390x.tar.gz
39bcdc9a46245bfdf651714a49e840ed5505aedc5f1f9cc80e4d2afecd3ac9b3  node-v10.12.0-linux-s390x.tar.xz
8d13d57aaf95177e97d29c0944d79a17de8c3a31ba3fe88d1846cfd907e52111  node-v10.12.0-linux-x64.tar.gz
4eba2e9a6db95745b769915d58e57df6ca6724ec1f023f76556fce30ceca2367  node-v10.12.0-linux-x64.tar.xz
a22893b99cef0dd8718dc471d0a54b768f5c5da0135f004d0d222b186b237ba3  node-v10.12.0.pkg
d459688a5539bfc65977657d198615ffd4da21bdd6a53ec92ee301943a33c53a  node-v10.12.0-sunos-x64.tar.gz
d308134ff457fe781bc60c8a277ac39eec4f6514ad08ba4de36f0ec49eaace0b  node-v10.12.0-sunos-x64.tar.xz
c6552b95062f5e9f3a27da6fdb57914ab4b27a9aa2e783fb050791166554d059  node-v10.12.0.tar.gz
d9cd890d6c3b060f7a5497a522564328fe73ec39dda082f41c4141a73ac30ae4  node-v10.12.0.tar.xz
2d6f1b952d48a89d34a18b05cdef0af4f7ab13ba8c4f8dc42a458ea15023f645  node-v10.12.0-win-x64.7z
44c938cf1409562d25d23b7c6bce110b53cd2a73774bed573b2979f0d88f51f8  node-v10.12.0-win-x64.zip
c376c7e3fe5de91c5e71fd4c7324f705679eb9899850851b93c2292eb85b4594  node-v10.12.0-win-x86.7z
2cb4521b904f0e711ca76fcee70e40d4c23a48ce84cf09a867e65a01deff4f4a  node-v10.12.0-win-x86.zip
21fcc88b9af133b0ecdf03a0cdf965ad5f2a547759096fb3b050be4ca871de8f  node-v10.12.0-x64.msi
a12d102d85a6d317a7462a2e453ddbc934e0b5255368493535f49ee64ffbcbc7  node-v10.12.0-x86.msi
f52da19ae108f7113808698156dd8a9153ae5af5fba8005fdebd437788e2eea5  win-x64/node.exe
23143ab50dba73be1ac5322e748f94e491fa7f99b04102abeb6a59a593544157  win-x64/node.lib
557b9213079a6f6052ff8eb2800b52224a4b540a148f02a0c0ee3c7c7604a2c5  win-x64/node_pdb.7z
ac43b58e67552ff1d7dde41e6f8bf4cdc88112d71ebf3d6a585c9d2f744e8814  win-x64/node_pdb.zip
9aa54215250da53f9e6e1099eff29ad6d6428616b5acd8f6a501ed78375bb99e  win-x86/node.exe
531df749aaeb0f90c7c6fff85d65c6a7ee213fb32346c95030ee25c168a7ab68  win-x86/node.lib
ed07d34aec38f0db8e46527a1ad0dda888e3225cbde1f7e601628a13da2cc7ab  win-x86/node_pdb.7z
56bbff589740b76c3dc34f5d7adce04d2a1f5c15846b95af5391c779b1408bb1  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEj8yhP+8dDC6RAI4Jdw96mlrhVgAFAlu+bbIACgkQdw96mlrh
VgB6TxAAjlUJuo7kC2Gh8wnRT3Fz+6U2pU33qR5aZef5nsBVlUNtlF+2x5UuEAPA
ytyMNlPxHEfaJ0CwN29nBCsZEjQ+Jz2gZdf7xsER0zeifrYyY6uoM+ioNt4GQnMb
Gn6wHrkdhx4+3a77Hsqtf5DGtgGM+whe1jt3UEei9qWT+fS+O8YHJzCi5wgbN35m
yyZBlfNwd+7dsk5JbtnKhIDCZdka4CBSo0dMEG0LN7nJGE2m20+KTkvdPJpNuWhH
vPXxXA9wDd/X/NFZwZEPcbe/Liog+wn/EtxcN7ageYZ+NfMyZVbyGWY9vZxnj5Ql
l6ZgXoO6bp7I2EVoPXCCeH0Qslfnsvq6vxLa8LOG3jyAGB+dp786pFMc+sqgr4EF
rSFvQBOCMZYHSo5368Z2dX4LzxTAyV//gdVjjyJ+1McXwBG4khQBwO1HMZ9JWqQO
dygCI7myJKtC/qh+k3hUt7EQCUSyrAn3PsRWhG5r0yjNe8oUV9pPxdqC65DSU0/7
vtdHXwbJs1ogsrhYmXy09EI+gZ/dWN5jkXBLpBlE0LOFm+qTGhA9vcQgd5MlWYLJ
/dp4ayQ6tOXOXntDCl6+JpmwzHHE/oaiIDY0CwfOGBrqI/LL0qrSs8x+NABstBc5
/DnoqNE/y6BvlB0ioU+aT9yISxws+PNxG213MFAW7g3iaC5WK4k=
=24k0
-----END PGP SIGNATURE-----

```
