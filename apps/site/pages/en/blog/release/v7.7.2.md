---
date: '2017-03-08T22:27:59.539Z'
category: release
title: Node v7.7.2 (Current)
layout: blog-post
author: <PERSON>
---

### Notable changes

- **doc**: add `<PERSON><PERSON><PERSON>` to collaborators (<PERSON><PERSON><PERSON>) [#11676](https://github.com/nodejs/node/pull/11676)
- **tty**: add ref() so process.stdin.ref() etc. work (<PERSON>) [#7360](https://github.com/nodejs/node/pull/7360)
- **util**: fix inspecting symbol key in string (Ali BARIN) [#11672](https://github.com/nodejs/node/pull/11672)

### Commits

- [[`f56ca30bf0`](https://github.com/nodejs/node/commit/f56ca30bf0)] - **benchmark,build,doc,lib,src,test**: correct typos (<PERSON>) [#11189](https://github.com/nodejs/node/pull/11189)
- [[`02dbae6b3f`](https://github.com/nodejs/node/commit/02dbae6b3f)] - **buffer**: refactor Buffer.prototype.inspect() (Rich Trott) [#11600](https://github.com/nodejs/node/pull/11600)
- [[`e5b530cb62`](https://github.com/nodejs/node/commit/e5b530cb62)] - **build**: fix llvm version detection in freebsd-10 (Shigeki Ohtsu) [#11668](https://github.com/nodejs/node/pull/11668)
- [[`ed6d7412a7`](https://github.com/nodejs/node/commit/ed6d7412a7)] - **deps**: fix CLEAR_HASH macro to be usable as a single statement (Sam Roberts) [#11616](https://github.com/nodejs/node/pull/11616)
- [[`039a1a97d8`](https://github.com/nodejs/node/commit/039a1a97d8)] - **dns**: minor refactor of dns module (James M Snell) [#11597](https://github.com/nodejs/node/pull/11597)
- [[`3b27b8da9d`](https://github.com/nodejs/node/commit/3b27b8da9d)] - **doc**: fixed readable.isPaused() version annotation (Laurent Fortin) [#11677](https://github.com/nodejs/node/pull/11677)
- [[`84028888db`](https://github.com/nodejs/node/commit/84028888db)] - **doc**: fix broken URL to event loop guide (Poker) [#11670](https://github.com/nodejs/node/pull/11670)
- [[`d5c436311c`](https://github.com/nodejs/node/commit/d5c436311c)] - **doc**: remove Locked from stability index (Rich Trott) [#11661](https://github.com/nodejs/node/pull/11661)
- [[`986d391066`](https://github.com/nodejs/node/commit/986d391066)] - **doc**: unlock module (Rich Trott) [#11661](https://github.com/nodejs/node/pull/11661)
- [[`d06dbf03cc`](https://github.com/nodejs/node/commit/d06dbf03cc)] - **doc**: fix misleading ASCII comments (Rahat Ahmed) [#11657](https://github.com/nodejs/node/pull/11657)
- [[`98d33282d9`](https://github.com/nodejs/node/commit/98d33282d9)] - **doc**: add `Daijiro Wachi` to collaborators (Daijiro Wachi) [#11676](https://github.com/nodejs/node/pull/11676)
- [[`3e79dffd2c`](https://github.com/nodejs/node/commit/3e79dffd2c)] - **doc**: fix WHATWG URL url.protocol example (Richard Lau) [#11647](https://github.com/nodejs/node/pull/11647)
- [[`e468cd3ee7`](https://github.com/nodejs/node/commit/e468cd3ee7)] - **doc**: argument types for console methods (Amelia Clarke) [#11554](https://github.com/nodejs/node/pull/11554)
- [[`83c7b245e2`](https://github.com/nodejs/node/commit/83c7b245e2)] - **doc**: fix typo in stream doc (Bradley Curran) [#11560](https://github.com/nodejs/node/pull/11560)
- [[`a0c117ba95`](https://github.com/nodejs/node/commit/a0c117ba95)] - **doc**: fixup errors.md (Vse Mozhet Byt) [#11566](https://github.com/nodejs/node/pull/11566)
- [[`b116830d64`](https://github.com/nodejs/node/commit/b116830d64)] - **doc**: add link to references in net.Socket (Joyee Cheung) [#11625](https://github.com/nodejs/node/pull/11625)
- [[`b968491dc2`](https://github.com/nodejs/node/commit/b968491dc2)] - **doc**: document WHATWG IDNA methods' error handling (Timothy Gu) [#11549](https://github.com/nodejs/node/pull/11549)
- [[`d329abf1c6`](https://github.com/nodejs/node/commit/d329abf1c6)] - **doc**: use common malformed instead of misformatted (James Sumners) [#11518](https://github.com/nodejs/node/pull/11518)
- [[`11aea2662f`](https://github.com/nodejs/node/commit/11aea2662f)] - **doc**: fix typo in STYLE_GUIDE.md (Nikolai Vavilov) [#11615](https://github.com/nodejs/node/pull/11615)
- [[`f972bd81c6`](https://github.com/nodejs/node/commit/f972bd81c6)] - **inspector**: libuv notification on incoming message (Eugene Ostroukhov) [#11617](https://github.com/nodejs/node/pull/11617)
- [[`a7eba9c71c`](https://github.com/nodejs/node/commit/a7eba9c71c)] - **meta**: move WORKING_GROUPS.md to CTC repo (James M Snell) [#11555](https://github.com/nodejs/node/pull/11555)
- [[`5963566367`](https://github.com/nodejs/node/commit/5963566367)] - **meta**: remove out of date ROADMAP.md file (James M Snell) [#11556](https://github.com/nodejs/node/pull/11556)
- [[`b56e851c48`](https://github.com/nodejs/node/commit/b56e851c48)] - **net**: refactor overloaded argument handling (Joyee Cheung) [#11667](https://github.com/nodejs/node/pull/11667)
- [[`13cb8a69e4`](https://github.com/nodejs/node/commit/13cb8a69e4)] - **net**: remove misleading comment (Ben Noordhuis) [#11573](https://github.com/nodejs/node/pull/11573)
- [[`e2133f3e57`](https://github.com/nodejs/node/commit/e2133f3e57)] - **os**: improve cpus() performance (Brian White) [#11564](https://github.com/nodejs/node/pull/11564)
- [[`821d713a38`](https://github.com/nodejs/node/commit/821d713a38)] - **src**: remove outdated FIXME in node_crypto.cc (Daniel Bevenius) [#11669](https://github.com/nodejs/node/pull/11669)
- [[`1b6ba9effb`](https://github.com/nodejs/node/commit/1b6ba9effb)] - **src**: do not ignore IDNA conversion error (Timothy Gu) [#11549](https://github.com/nodejs/node/pull/11549)
- [[`fdb4a6c796`](https://github.com/nodejs/node/commit/fdb4a6c796)] - **test**: skip the test with proper TAP message (Sakthipriyan Vairamani (thefourtheye)) [#11584](https://github.com/nodejs/node/pull/11584)
- [[`5df9110178`](https://github.com/nodejs/node/commit/5df9110178)] - **test**: check the origin of the blob URLs (Daijiro Wachi) [#11426](https://github.com/nodejs/node/pull/11426)
- [[`b4dcb26681`](https://github.com/nodejs/node/commit/b4dcb26681)] - **test**: changed test1 of test-vm-timeout.js (maurice_hayward) [#11590](https://github.com/nodejs/node/pull/11590)
- [[`f69685be65`](https://github.com/nodejs/node/commit/f69685be65)] - **test**: remove obsolete eslint-disable comment (Rich Trott) [#11643](https://github.com/nodejs/node/pull/11643)
- [[`a4d14363a9`](https://github.com/nodejs/node/commit/a4d14363a9)] - **test**: fix args in parallel/test-fs-null-bytes.js (Vse Mozhet Byt) [#11601](https://github.com/nodejs/node/pull/11601)
- [[`8377374754`](https://github.com/nodejs/node/commit/8377374754)] - **test**: fix tests when npn feature is disabled. (Shigeki Ohtsu) [#11655](https://github.com/nodejs/node/pull/11655)
- [[`1445e282c3`](https://github.com/nodejs/node/commit/1445e282c3)] - **test**: add test-buffer-prototype-inspect (Rich Trott) [#11600](https://github.com/nodejs/node/pull/11600)
- [[`00dd20c173`](https://github.com/nodejs/node/commit/00dd20c173)] - **test**: fix flaky test-https-agent-create-connection (Santiago Gimeno) [#11649](https://github.com/nodejs/node/pull/11649)
- [[`91a222de99`](https://github.com/nodejs/node/commit/91a222de99)] - **test**: enable max-len for test-repl (Rich Trott) [#11559](https://github.com/nodejs/node/pull/11559)
- [[`924b785d50`](https://github.com/nodejs/node/commit/924b785d50)] - **test**: fix test-internal-util-assertCrypto regex (Daniel Bevenius) [#11620](https://github.com/nodejs/node/pull/11620)
- [[`cdee945307`](https://github.com/nodejs/node/commit/cdee945307)] - **test**: improve https coverage to check create connection (chiaki-yokoo) [#11435](https://github.com/nodejs/node/pull/11435)
- [[`4f9253686d`](https://github.com/nodejs/node/commit/4f9253686d)] - **test**: apply strict mode in test-repl (Rich Trott) [#11575](https://github.com/nodejs/node/pull/11575)
- [[`2601c06486`](https://github.com/nodejs/node/commit/2601c06486)] - **test**: skip tests with common.skip (Sakthipriyan Vairamani (thefourtheye)) [#11585](https://github.com/nodejs/node/pull/11585)
- [[`6a5d96164a`](https://github.com/nodejs/node/commit/6a5d96164a)] - **test**: more comprehensive IDNA test cases (Timothy Gu) [#11549](https://github.com/nodejs/node/pull/11549)
- [[`163d2d1624`](https://github.com/nodejs/node/commit/163d2d1624)] - **timers**: unlock the timers API (Rich Trott) [#11580](https://github.com/nodejs/node/pull/11580)
- [[`d6ac192fa3`](https://github.com/nodejs/node/commit/d6ac192fa3)] - **tls**: fix macro to check NPN feature (Shigeki Ohtsu) [#11655](https://github.com/nodejs/node/pull/11655)
- [[`ac3deb1481`](https://github.com/nodejs/node/commit/ac3deb1481)] - **tools**: remove NODE_PATH from environment for tests (Rich Trott) [#11612](https://github.com/nodejs/node/pull/11612)
- [[`3c54f8199c`](https://github.com/nodejs/node/commit/3c54f8199c)] - **tty**: add ref() so process.stdin.ref() etc. work (Ben Schmidt) [#7360](https://github.com/nodejs/node/pull/7360)
- [[`24e6fcce8b`](https://github.com/nodejs/node/commit/24e6fcce8b)] - **url**: use `hasIntl` instead of `try-catch` (Daijiro Wachi) [#11571](https://github.com/nodejs/node/pull/11571)
- [[`7b84363636`](https://github.com/nodejs/node/commit/7b84363636)] - **util**: fix inspecting symbol key in string (Ali BARIN) [#11672](https://github.com/nodejs/node/pull/11672)

Windows 32-bit Installer: https://nodejs.org/dist/v7.7.2/node-v7.7.2-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v7.7.2/node-v7.7.2-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v7.7.2/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v7.7.2/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v7.7.2/node-v7.7.2.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v7.7.2/node-v7.7.2-darwin-x64.tar.gz \
Linux 32-bit Binary: https://nodejs.org/dist/v7.7.2/node-v7.7.2-linux-x86.tar.xz \
Linux 64-bit Binary: https://nodejs.org/dist/v7.7.2/node-v7.7.2-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v7.7.2/node-v7.7.2-linux-ppc64le.tar.xz \
Linux PPC BE 64-bit Binary: https://nodejs.org/dist/v7.7.2/node-v7.7.2-linux-ppc64.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v7.7.2/node-v7.7.2-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v7.7.2/node-v7.7.2-aix-ppc64.tar.gz \
SmartOS 32-bit Binary: https://nodejs.org/dist/v7.7.2/node-v7.7.2-sunos-x86.tar.xz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v7.7.2/node-v7.7.2-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v7.7.2/node-v7.7.2-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v7.7.2/node-v7.7.2-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v7.7.2/node-v7.7.2-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v7.7.2/node-v7.7.2.tar.gz \
Other release files: https://nodejs.org/dist/v7.7.2/ \
Documentation: https://nodejs.org/docs/v7.7.2/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

71445192382115e16be153f5bb4fbd5cd3638d68089acf15421fe736c04042aa  node-v7.7.2-aix-ppc64.tar.gz
56ec8f9030e3fda664f9908afe835669d4c3a621f81403ec1e36911c05598583  node-v7.7.2-darwin-x64.tar.gz
dd29a543fd7b825af74c2a28033f831e9836fab717a7216544bb525d07da0396  node-v7.7.2-darwin-x64.tar.xz
47566a8c247b6fdd7f1cb97cd4726b239e2fb49e6d4eca63deba9d2b856720ca  node-v7.7.2-headers.tar.gz
b73118d85017bb62b076707fbb6ef49dd1be0835f9a8316fc77266fc95f90cda  node-v7.7.2-headers.tar.xz
586f012550d7c7c415267656477648d38595af75f9a4218000fece98e01c4a65  node-v7.7.2-linux-arm64.tar.gz
d2e39fdfd1253c822cd124e13f937f0857c2ec1958560c4b1a3b7f643329c7eb  node-v7.7.2-linux-arm64.tar.xz
91cc13abe4142b552742ff6b6b493f3a93d0abe015ac88508ad2180b54002736  node-v7.7.2-linux-armv6l.tar.gz
daa8ef936c0729552f2496d63f61106b8464b781692dca070b24a36c53887c39  node-v7.7.2-linux-armv6l.tar.xz
218c689759d80d57280071a5db4b33143115775e53beb150032e648f6f365991  node-v7.7.2-linux-armv7l.tar.gz
ec35b7b973a29a5a6bb858780384d6f6670c0727ff8473d75bdc10aa936338a0  node-v7.7.2-linux-armv7l.tar.xz
9342621099a9127bf94705f254d9582ffb9418e001079fca6d8ee7eb47571f2d  node-v7.7.2-linux-ppc64le.tar.gz
1e00d8adfeffb2ca539fbd5e8e76cf7db976a6861250863a1b26fbf03e4c89b7  node-v7.7.2-linux-ppc64le.tar.xz
ccb400f3575e2e168d0193292d05d47f37e8335457646f363f28e44af349957e  node-v7.7.2-linux-ppc64.tar.gz
6882302704893603735c036d176b78ee11c9b5ac490756d5dc344102510b7b06  node-v7.7.2-linux-ppc64.tar.xz
bea94acf05fdc144cc19a58b0da3e5c22f72423e3d83cb20116525dbdd9a9c15  node-v7.7.2-linux-s390x.tar.gz
073f197466a00f6e628add54f91bfbc73c70a6da78d61cb4321865e47d014e51  node-v7.7.2-linux-s390x.tar.xz
ecd653c9bfb1f95f12135b20ddfe5cdbc203e7a329ca82b6f7b35b6154836c66  node-v7.7.2-linux-x64.tar.gz
3213a62818fb51696f9d958a72390c375fc9eb2ebef5d0270ff9fa52fa98fb7b  node-v7.7.2-linux-x64.tar.xz
ba4287cf7496ed9512be67e35140048462a91408680f2b54956bd5c0e8a45a3b  node-v7.7.2-linux-x86.tar.gz
4a18b8f720bbf9890506734722e6cd2e6b9a0246d0c74fa98f774eb367c51a36  node-v7.7.2-linux-x86.tar.xz
b1c7e45195989287a4ccce692ca85ff41822ccf005510a8d44a9f436f85a2612  node-v7.7.2.pkg
2afad21c7f7e32dd7dba31addd2cfb05fec7c4162299195e186ddc2a8c3867aa  node-v7.7.2-sunos-x64.tar.gz
57597cf7a5616ab542ca79e2ffb6e40fc8e8bc3137ce5a3ea5a7d74a64ee80af  node-v7.7.2-sunos-x64.tar.xz
689ac77429a490a6a2019265dccc757e9fe08cd3c978e234e6ee2af84cfb596f  node-v7.7.2-sunos-x86.tar.gz
1734af639e1de959aaee71f7b384a2ea6b6485526fed05a550a5d351dc84de38  node-v7.7.2-sunos-x86.tar.xz
a6a1041337bae3e3b5645316672cd3b3867fdfc569f561aec824c0ea622fff9c  node-v7.7.2.tar.gz
e7448d8e44d32c2c63347c822508cf5c87b9662c07db290056fec66d37c4f584  node-v7.7.2.tar.xz
861526776eaa0773ea75f94a11112659e3b9c75fe557dadb95598792bd9347a2  node-v7.7.2-win-x64.7z
94b544f8ce4b9e1cbb8c27ad3fccddc5880496ddddbff4137736d0c34dc67328  node-v7.7.2-win-x64.zip
8f1c859aa21878918da9b065bade5db02cdd1d893a4e1830dd4acbc79b99d3e6  node-v7.7.2-win-x86.7z
7a5b07cfb5c129105df36e35b7f1464c3e66db3618fec97f2da3da43422928a9  node-v7.7.2-win-x86.zip
3d5a537a2d14615af8fb4e569033b18129c72380223beaa5a902832ceaa863fb  node-v7.7.2-x64.msi
d7b240210a842f12793e7972eb2511d7cdcea5cbebebf7dd6b2bbeee68b70b52  node-v7.7.2-x86.msi
5c88feec79f84deabeca23ad345367b8b9d4254bdb7605babd1fe66b35e44f6f  win-x64/node.exe
d66cb15ddb40c7f1e6ce667943fc39b611ba27e9531326b5d198001d530165dd  win-x64/node.lib
a808c63f8d079761db6c52c544b88ce128b3fed3c86ff03342afb8e54904c911  win-x64/node_pdb.7z
9a86b2e1b2264f1b5b9bb346c93bdbb01580fa45661982c5aacb625c950434df  win-x64/node_pdb.zip
ac7c2793603fc6b0468722be60c83ac6ea32bff08f2f6a1a954959a3f147e9fa  win-x86/node.exe
a1bdecdb1993eb85ed3799d85f704605dc90aac20efa3479718a61db3d86b1b7  win-x86/node.lib
ba97c7e821f2f7698030ef2761aef1d27c7af4db7e35564d2a82fffcf68e2648  win-x86/node_pdb.7z
d93d00e72a6737a0b0977016d087cc6fd6cad4855689234f48d7a1a244522295  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----
Comment: GPGTools - https://gpgtools.org

iQIcBAEBCAAGBQJYwILOAAoJELY7U1pMIGypQiwP/0zpGHPON19Z/XssUy6dwc3c
oagu3cgSAaFqe75c125PbaLGrCHPOPrkYB1NkRAUgXxsa3zMPK9cpWLZwCpoHtZG
ur/n2nUS9eyl6vBRu9kc8DPDnGoquxEP4Iyp3eJwCZV/XaEukcWp2Lb4NykgvTJ7
8rIIHE5+ns52AXPzOsKCB12OQoWew4ajVGKz/nlPxw+oqs/lJNZ83WWVd/zAngsc
pLo8/0TBnaLo85O9xOApFvIe0cKbcdOB2rjOKuVdsl2yi7DMrIB7XNDQE4rF7+FR
TWeY1fXxkT9pmsetV+Jz7L3k4nSX31eG+yjaagGd2cywtfUFgxud8p2ThXmssZJ3
RrQxb0iKX+0qQPaZaI5LPmCFyuJa0wqP+meJE5rD4Qq5nzchT7e34+0VidxuJy6J
UORSPZUy7+XAwEcv0KhCBpRdGFaV+T72fnbtdqIzXLJ5E0bp/zkVwqwXpp/U4uJ1
VkJ68yKPs7vBgnoxf/vYDRCBR/RSPa7s+0gxCJ+yOf+M1iNvgPvxCkz/Pm7R0ooj
MdEb2Ig1vCC+aWEYsMLiIFJd8M3cg0R7j0NQ/HTLkcIwa7DbO4gSrpacMj5pmnvl
azdIQgeaon2o+mI0qlqlVSNjBpxLqkDb3xys2uwaovxYz6VhBp8chfVu640jSgWc
bed7RtmZ6x2Ff+Al6oJU
=jQPf
-----END PGP SIGNATURE-----

```
