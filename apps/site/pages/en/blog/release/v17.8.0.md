---
date: '2022-03-22T14:44:40.137Z'
category: release
title: Node v17.8.0 (Current)
layout: blog-post
author: <PERSON> English
---

### Notable Changes

- \[[`3bd0078457`](https://github.com/nodejs/node/commit/3bd0078457)] - **doc**: add @ShogunPanda to collaborators (Shogun) [#42362](https://github.com/nodejs/node/pull/42362)
- \[[`23354673be`](https://github.com/nodejs/node/commit/23354673be)] - **doc**: deprecate string coercion in `fs.write`, `fs.writeFileSync` (Livia <PERSON>) [#42149](https://github.com/nodejs/node/pull/42149)
- \[[`da42ffb85e`](https://github.com/nodejs/node/commit/da42ffb85e)] - **(SEMVER-MINOR)** **http**: trace http client by perf_hooks (theanarkh) [#42345](https://github.com/nodejs/node/pull/42345)
- \[[`84fd6e54b0`](https://github.com/nodejs/node/commit/84fd6e54b0)] - **deps**: upgrade npm to 8.5.5 (npm team) [#42382](https://github.com/nodejs/node/pull/42382)
- \[[`b60262ee9f`](https://github.com/nodejs/node/commit/b60262ee9f)] - **deps**: update undici to 4.15.1 (Michaël Zasso) [#42246](https://github.com/nodejs/node/pull/42246)

### Commits

- \[[`1796f035c7`](https://github.com/nodejs/node/commit/1796f035c7)] - **build**: rename tools workflow and add undici to it (Michaël Zasso) [#42246](https://github.com/nodejs/node/pull/42246)
- \[[`f27bcec2ea`](https://github.com/nodejs/node/commit/f27bcec2ea)] - **build**: use ccache in make-v8.sh on ppc64le and s390x (Richard Lau) [#42204](https://github.com/nodejs/node/pull/42204)
- \[[`f48c3baf5a`](https://github.com/nodejs/node/commit/f48c3baf5a)] - **crypto**: fix auth tag length error when mode != GCM (Tobias Nießen) [#42383](https://github.com/nodejs/node/pull/42383)
- \[[`1d0468f749`](https://github.com/nodejs/node/commit/1d0468f749)] - **crypto**: fix fingerprint string size calculation (Tobias Nießen) [#42175](https://github.com/nodejs/node/pull/42175)
- \[[`a4632a3dc2`](https://github.com/nodejs/node/commit/a4632a3dc2)] - **crypto**: add CHECKs to remaining BIO_s_mem allocs (Tobias Nießen) [#42155](https://github.com/nodejs/node/pull/42155)
- \[[`3b55946452`](https://github.com/nodejs/node/commit/3b55946452)] - **debugger**: correct typo in inspect_repl.js (Kohei Ueno) [#42267](https://github.com/nodejs/node/pull/42267)
- \[[`84fd6e54b0`](https://github.com/nodejs/node/commit/84fd6e54b0)] - **deps**: upgrade npm to 8.5.5 (npm team) [#42382](https://github.com/nodejs/node/pull/42382)
- \[[`f2178fcc1a`](https://github.com/nodejs/node/commit/f2178fcc1a)] - **deps**: cares: cherry-pick b5a3d96 (bradh352) [#42216](https://github.com/nodejs/node/pull/42216)
- \[[`063ff08cb1`](https://github.com/nodejs/node/commit/063ff08cb1)] - **deps**: V8: cherry-pick c6f6626deb14 (Lu Yahan) [#42240](https://github.com/nodejs/node/pull/42240)
- \[[`b60262ee9f`](https://github.com/nodejs/node/commit/b60262ee9f)] - **deps**: update undici to 4.15.1 (Michaël Zasso) [#42246](https://github.com/nodejs/node/pull/42246)
- \[[`70c0758308`](https://github.com/nodejs/node/commit/70c0758308)] - **deps**: upgrade npm to 8.5.3 (npm team) [#42205](https://github.com/nodejs/node/pull/42205)
- \[[`fd51e78963`](https://github.com/nodejs/node/commit/fd51e78963)] - **doc**: fix version history for `net.Socket` and `net.Server` (Antoine du Hamel) [#42268](https://github.com/nodejs/node/pull/42268)
- \[[`db83c4d6dc`](https://github.com/nodejs/node/commit/db83c4d6dc)] - **doc**: improve README.md usability (Rich Trott) [#42378](https://github.com/nodejs/node/pull/42378)
- \[[`88d3401329`](https://github.com/nodejs/node/commit/88d3401329)] - **doc**: add that chacha20-poly1305 is IETF version (Tobias Nießen) [#42370](https://github.com/nodejs/node/pull/42370)
- \[[`04a7c0061b`](https://github.com/nodejs/node/commit/04a7c0061b)] - **doc**: update instructions for openssl updates (Michael Dawson) [#42353](https://github.com/nodejs/node/pull/42353)
- \[[`78b858dd4b`](https://github.com/nodejs/node/commit/78b858dd4b)] - **doc**: document goal to have examples (Michael Dawson) [#42274](https://github.com/nodejs/node/pull/42274)
- \[[`a5e42f0113`](https://github.com/nodejs/node/commit/a5e42f0113)] - **doc**: fix Embedder's Guide link to V8 official docs (Aroyan) [#42373](https://github.com/nodejs/node/pull/42373)
- \[[`6c265e7243`](https://github.com/nodejs/node/commit/6c265e7243)] - **doc**: remove unneeded lint disable comment (Rich Trott) [#42374](https://github.com/nodejs/node/pull/42374)
- \[[`46d3d23e64`](https://github.com/nodejs/node/commit/46d3d23e64)] - **doc**: revise async_hooks docs (Rich Trott) [#42337](https://github.com/nodejs/node/pull/42337)
- \[[`3bd0078457`](https://github.com/nodejs/node/commit/3bd0078457)] - **doc**: add @ShogunPanda to collaborators (Shogun) [#42362](https://github.com/nodejs/node/pull/42362)
- \[[`e7e8eb9f03`](https://github.com/nodejs/node/commit/e7e8eb9f03)] - **doc**: update base branch name for `nodejs/nodejs.org` (Danielle Adams) [#42355](https://github.com/nodejs/node/pull/42355)
- \[[`fd7e4ab654`](https://github.com/nodejs/node/commit/fd7e4ab654)] - **doc**: fix async iterable pipeline signal examples (Randall Leeds) [#42258](https://github.com/nodejs/node/pull/42258)
- \[[`96dc591b55`](https://github.com/nodejs/node/commit/96dc591b55)] - **doc**: clarify path search in `child_process.spawn` (Damjan Cvetko) [#41418](https://github.com/nodejs/node/pull/41418)
- \[[`72dd50016a`](https://github.com/nodejs/node/commit/72dd50016a)] - **doc**: clarify the meaning of legacy status (Darshan Sen) [#42269](https://github.com/nodejs/node/pull/42269)
- \[[`8b99099063`](https://github.com/nodejs/node/commit/8b99099063)] - **doc**: improve pipe description (Mikael Finstad) [#42295](https://github.com/nodejs/node/pull/42295)
- \[[`701dc14fdf`](https://github.com/nodejs/node/commit/701dc14fdf)] - **doc**: remove outdated timeout.unref content (Xuguang Mei) [#42241](https://github.com/nodejs/node/pull/42241)
- \[[`23354673be`](https://github.com/nodejs/node/commit/23354673be)] - **doc**: deprecate string coercion in `fs.write`, `fs.writeFileSync` (Livia Medeiros) [#42149](https://github.com/nodejs/node/pull/42149)
- \[[`f3c6c00963`](https://github.com/nodejs/node/commit/f3c6c00963)] - **doc**: remove refs to old OpenSSL list-\* commands (Tobias Nießen) [#42235](https://github.com/nodejs/node/pull/42235)
- \[[`19851f8d2d`](https://github.com/nodejs/node/commit/19851f8d2d)] - **doc**: readline `'line'` event emits final line (Matt Probert) [#42214](https://github.com/nodejs/node/pull/42214)
- \[[`e55283b978`](https://github.com/nodejs/node/commit/e55283b978)] - **esm**: make extension-less errors in type:module actionable (Bradley Farias) [#42301](https://github.com/nodejs/node/pull/42301)
- \[[`e17db8f0fa`](https://github.com/nodejs/node/commit/e17db8f0fa)] - **esm**: improve typings and code coverage (Bradley Farias) [#42305](https://github.com/nodejs/node/pull/42305)
- \[[`4829a1047f`](https://github.com/nodejs/node/commit/4829a1047f)] - **esm**: add runtime warning for specifier resolution flag (Geoffrey Booth) [#42252](https://github.com/nodejs/node/pull/42252)
- \[[`da42ffb85e`](https://github.com/nodejs/node/commit/da42ffb85e)] - **(SEMVER-MINOR)** **http**: trace http client by perf_hooks (theanarkh) [#42345](https://github.com/nodejs/node/pull/42345)
- \[[`88dee3c6b5`](https://github.com/nodejs/node/commit/88dee3c6b5)] - **http2**: fix potential integer overflow (Michael Dawson) [#42248](https://github.com/nodejs/node/pull/42248)
- \[[`1fe0b69c31`](https://github.com/nodejs/node/commit/1fe0b69c31)] - **lib**: refactor to use primordials in `lib/assert.js` (Akhil Marsonya) [#41702](https://github.com/nodejs/node/pull/41702)
- \[[`69a3792540`](https://github.com/nodejs/node/commit/69a3792540)] - **lib**: fix AsyncResource.bind not using 'this' from the caller by default (Roch Devost) [#42177](https://github.com/nodejs/node/pull/42177)
- \[[`1c87ce6a32`](https://github.com/nodejs/node/commit/1c87ce6a32)] - **meta**: update AUTHORS (Node.js GitHub Bot) [#42404](https://github.com/nodejs/node/pull/42404)
- \[[`e7b8d83acd`](https://github.com/nodejs/node/commit/e7b8d83acd)] - **meta**: update AUTHORS (Node.js GitHub Bot) [#42317](https://github.com/nodejs/node/pull/42317)
- \[[`7fc4b9f08d`](https://github.com/nodejs/node/commit/7fc4b9f08d)] - **meta**: add dependencies label to label-pr-config (Mestery) [#42129](https://github.com/nodejs/node/pull/42129)
- \[[`e96042442b`](https://github.com/nodejs/node/commit/e96042442b)] - **src**: convert hex2bin() into a regular function (Darshan Sen) [#42321](https://github.com/nodejs/node/pull/42321)
- \[[`21198c1407`](https://github.com/nodejs/node/commit/21198c1407)] - **src**: fix coverity warnings in node_file.cc (Michael Dawson) [#42272](https://github.com/nodejs/node/pull/42272)
- \[[`846b074075`](https://github.com/nodejs/node/commit/846b074075)] - **src**: check EC_POINT_get_affine_coordinates result (Tobias Nießen) [#42304](https://github.com/nodejs/node/pull/42304)
- \[[`8b84e68cbd`](https://github.com/nodejs/node/commit/8b84e68cbd)] - **src**: simplify bound check in ParseArrayIndex (Tobias Nießen) [#42306](https://github.com/nodejs/node/pull/42306)
- \[[`9500e5862e`](https://github.com/nodejs/node/commit/9500e5862e)] - **src**: avoid returning invalid value from hex2bin (Tobias Nießen) [#42307](https://github.com/nodejs/node/pull/42307)
- \[[`08e2d8ab86`](https://github.com/nodejs/node/commit/08e2d8ab86)] - **src**: check return value of HMAC_Final (Tobias Nießen) [#42303](https://github.com/nodejs/node/pull/42303)
- \[[`9fc4b9b04e`](https://github.com/nodejs/node/commit/9fc4b9b04e)] - **src**: include internal/options in the snapshot (Joyee Cheung) [#42203](https://github.com/nodejs/node/pull/42203)
- \[[`e43aa30982`](https://github.com/nodejs/node/commit/e43aa30982)] - **src**: remove redundant buffer size check (Tobias Nießen) [#42257](https://github.com/nodejs/node/pull/42257)
- \[[`d06e92dba0`](https://github.com/nodejs/node/commit/d06e92dba0)] - **src**: perform minor cleanups on zlib code (Anna Henningsen) [#42247](https://github.com/nodejs/node/pull/42247)
- \[[`9af908305d`](https://github.com/nodejs/node/commit/9af908305d)] - **src**: use `emplace_back` instead of `push_back` (Yash Ladha) [#42159](https://github.com/nodejs/node/pull/42159)
- \[[`62d9a7f5db`](https://github.com/nodejs/node/commit/62d9a7f5db)] - **src**: fix unchecked return warning from coverity (Michael Dawson) [#42176](https://github.com/nodejs/node/pull/42176)
- \[[`58763d7f9d`](https://github.com/nodejs/node/commit/58763d7f9d)] - **src,crypto**: avoid tristate Maybe\<bool> in ExportJWKEcKey() (Darshan Sen) [#42223](https://github.com/nodejs/node/pull/42223)
- \[[`5367002bc8`](https://github.com/nodejs/node/commit/5367002bc8)] - **stream**: do cleanup when iterator is destroyed (Khoo Hao Yit) [#42320](https://github.com/nodejs/node/pull/42320)
- \[[`3492a0eb1e`](https://github.com/nodejs/node/commit/3492a0eb1e)] - **string_decoder**: fix crash when calling \_\_proto\_\_.write() (Darshan Sen) [#42062](https://github.com/nodejs/node/pull/42062)
- \[[`d9a5c2b284`](https://github.com/nodejs/node/commit/d9a5c2b284)] - **test**: give slow tests more time on Rasberry PIs (Michael Dawson) [#42380](https://github.com/nodejs/node/pull/42380)
- \[[`b82bac09ff`](https://github.com/nodejs/node/commit/b82bac09ff)] - **test**: improve https_renew_cert.sh script (Tobias Nießen) [#42343](https://github.com/nodejs/node/pull/42343)
- \[[`dfdce7c182`](https://github.com/nodejs/node/commit/dfdce7c182)] - **test**: improve \_http_incoming.js coverage (Yoshiki Kurihara) [#42211](https://github.com/nodejs/node/pull/42211)
- \[[`4941791f29`](https://github.com/nodejs/node/commit/4941791f29)] - **test**: improve \_http_outgoing coverage (Yoshiki Kurihara) [#42213](https://github.com/nodejs/node/pull/42213)
- \[[`94e5eaa7e9`](https://github.com/nodejs/node/commit/94e5eaa7e9)] - **test**: add test case for reverted 17.7 regression (Rich Trott) [#42283](https://github.com/nodejs/node/pull/42283)
- \[[`a4aa9eb97f`](https://github.com/nodejs/node/commit/a4aa9eb97f)] - **test**: use global webcrypto for WPT tests (Antoine du Hamel) [#42236](https://github.com/nodejs/node/pull/42236)
- \[[`26d4a2d489`](https://github.com/nodejs/node/commit/26d4a2d489)] - **test,crypto**: add and update empty passphrase regression tests (Darshan Sen) [#42319](https://github.com/nodejs/node/pull/42319)
- \[[`4fd2aff42e`](https://github.com/nodejs/node/commit/4fd2aff42e)] - **tools**: make update-undici script executable (Michaël Zasso) [#42406](https://github.com/nodejs/node/pull/42406)
- \[[`38e7681ac7`](https://github.com/nodejs/node/commit/38e7681ac7)] - **tools**: update lint-md-dependencies to rollup\@2.70.1 (Node.js GitHub Bot) [#42403](https://github.com/nodejs/node/pull/42403)
- \[[`b7a4b4b1fd`](https://github.com/nodejs/node/commit/b7a4b4b1fd)] - **tools**: update doc to highlight.js\@11.5.0 unified\@10.1.2 (Node.js GitHub Bot) [#42315](https://github.com/nodejs/node/pull/42315)
- \[[`30ea1889d5`](https://github.com/nodejs/node/commit/30ea1889d5)] - **tools**: update lint-md-dependencies to rollup\@2.70.0 unified\@10.1.2 (Node.js GitHub Bot) [#42316](https://github.com/nodejs/node/pull/42316)
- \[[`eb0e1a1147`](https://github.com/nodejs/node/commit/eb0e1a1147)] - **tools**: update eslint to 8.11.0 (Node.js GitHub Bot) [#42318](https://github.com/nodejs/node/pull/42318)
- \[[`e95426fd3a`](https://github.com/nodejs/node/commit/e95426fd3a)] - **tools**: fix web streams API links (Brian White) [#42153](https://github.com/nodejs/node/pull/42153)
- \[[`fe01940f35`](https://github.com/nodejs/node/commit/fe01940f35)] - **url**: preserve null char in WHATWG URL errors (Rich Trott) [#42263](https://github.com/nodejs/node/pull/42263)
- \[[`b89f4d5c17`](https://github.com/nodejs/node/commit/b89f4d5c17)] - **url**: trim leading and trailing C0 control chars (Rich Trott) [#42196](https://github.com/nodejs/node/pull/42196)
- \[[`229fb40edc`](https://github.com/nodejs/node/commit/229fb40edc)] - **worker**: do not send message if port is closing (Rich Trott) [#42357](https://github.com/nodejs/node/pull/42357)

Windows 32-bit Installer: https://nodejs.org/dist/v17.8.0/node-v17.8.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v17.8.0/node-v17.8.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v17.8.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v17.8.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v17.8.0/node-v17.8.0.pkg \
macOS Apple Silicon 64-bit Binary: https://nodejs.org/dist/v17.8.0/node-v17.8.0-darwin-arm64.tar.gz \
macOS Intel 64-bit Binary: https://nodejs.org/dist/v17.8.0/node-v17.8.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v17.8.0/node-v17.8.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v17.8.0/node-v17.8.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v17.8.0/node-v17.8.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v17.8.0/node-v17.8.0-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v17.8.0/node-v17.8.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v17.8.0/node-v17.8.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v17.8.0/node-v17.8.0.tar.gz \
Other release files: https://nodejs.org/dist/v17.8.0/ \
Documentation: https://nodejs.org/docs/v17.8.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

d3b95026bd938623491f72285df414fe4e77db6cb724d4af9c18a63e698834aa  node-v17.8.0-aix-ppc64.tar.gz
b0bdcddc070a559018f876e0810a678415f99d69ed6e4df15fd1c7cf5fc2e45f  node-v17.8.0-darwin-arm64.tar.gz
e25cdddbc64fd4f072925c55423a9d0c95adf0188301eb29925e9dbc1167a37f  node-v17.8.0-darwin-arm64.tar.xz
f253b705284f35f3ccea03ed7b97b8d5bd8002cfea3bb734289e2e9b38d0844b  node-v17.8.0-darwin-x64.tar.gz
0110f451fae0e079915d1c9dcea9cb8843b27255a0356f124969f452b9a04f09  node-v17.8.0-darwin-x64.tar.xz
a1249323a289305036ea2b08dd17f679b17b1d971557e251845e082c994102e6  node-v17.8.0-headers.tar.gz
434f4f06a11dabfcbb341444683ea66f06cd24cb7fb09fd1e4b3e87a6dec84f8  node-v17.8.0-headers.tar.xz
591d5c75b036fa3ce6f8d633e301c5c88124ee62eae1276b6eab9f27a53e1059  node-v17.8.0-linux-arm64.tar.gz
32dbba01ed4b62e1ee571c00dfd3efdf6dca637ee42d71f489bd8483402d2bc6  node-v17.8.0-linux-arm64.tar.xz
6315975508bda9e63c730e19e7140096a0f3eef6f8e8b525eeb59eb4aa850c5f  node-v17.8.0-linux-armv7l.tar.gz
fe167cd6a0af4410d995da4e59e92e7e980c1d1772c346c190d528a03d27226c  node-v17.8.0-linux-armv7l.tar.xz
92f22cb69dd52d8e10ca91d7db1ad80a8197a10a47400e041fb53cdf7253148c  node-v17.8.0-linux-ppc64le.tar.gz
1aebc8fcee584bf0ff79e007e9db0af6fcab77a771dc30a4b2243cf5fc81e97f  node-v17.8.0-linux-ppc64le.tar.xz
317fd87e96964c0fde2d004dfaf783a7860b7c1ab094916bb3f8e110358802d3  node-v17.8.0-linux-s390x.tar.gz
5ea3aaab6d52f4ee9f47ce35d1a6a56ad15cc41ff488643c85028885337cba3f  node-v17.8.0-linux-s390x.tar.xz
02d3e21362ae3cf670fa4b12c6b982e2544a815a007ea96f881b89f305843dfe  node-v17.8.0-linux-x64.tar.gz
715ec5fc09b843d5b57f60cb91b70b05a7b39b72c08bc17f983e97dcc3d3cdd7  node-v17.8.0-linux-x64.tar.xz
6870bcec38cebf14ae0001ceb2488b5e371a7adf3a87a17504a0d0b28aa44888  node-v17.8.0.pkg
3cf4ef8ede328389a6b0150eb94480fce9bdd00e23051d7bcada5af7473b28b1  node-v17.8.0.tar.gz
7981144faf674292eff7e075f640a7084ebd927be11d1af78ff76741b6364e4b  node-v17.8.0.tar.xz
7e6704c82ed3d1f3d2bc52980a19974562a266efc94a3443ab65bc8362c62b79  node-v17.8.0-win-x64.7z
fcb3a52c14e70ffdc5364952e1a6c40d4d02431667183604d62031790a9803d8  node-v17.8.0-win-x64.zip
83af19d419b89266d6aed33092229e86bdce8136290c1ee4e8451f31543a7841  node-v17.8.0-win-x86.7z
cc88a8d701a4edb74ca8507215293b2518f23034586c0cb8c764b356dc15c7e2  node-v17.8.0-win-x86.zip
5f78dc477560b4faf32afcb5f51946d8b9497c0af076c45129f556cd061e8be2  node-v17.8.0-x64.msi
ab3c4cbdd494b44c50b4d4accc187dbc5ce64060e9bdc5243ab93c473045d2f8  node-v17.8.0-x86.msi
3bb320b50a207fd8b7043aaeea2cb3bfe1d88f73a2f010943ea3cff693a5f8ad  win-x64/node.exe
998cc6721961278e2baa41598ea68ab7c04e8ab032bd3b36ed94e1d60d462c42  win-x64/node.lib
bd4616f79bb192c9c4ad8c14bb28fd3e1b66d046396a4d9c37f842c9ec00b6f6  win-x64/node_pdb.7z
4ff0aa24c2dcf62ed3dd7b1b8b37771d13653438f64937f9923058d502a53b63  win-x64/node_pdb.zip
33550b7d17ab56078dcc600e13f6e31c2d401d7759569d5d4c487813c8b0e600  win-x86/node.exe
4dd1b950bfa0bf82923b12fa5b67912aa4d931e774593755aeb7ec1191d6579b  win-x86/node.lib
195a13eb050f21ca4b2c7e7f71de625a0d5c6d23ecd6cb6b7eff456080910556  win-x86/node_pdb.7z
730d9fe7a6ac48f81a238b2e0e97a003900563a4b46b5f00be3f615f7350ae36  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEFB8HWVt7P/50MJqTdAVTO+V8fVcFAmI534IACgkQdAVTO+V8
fVekNhAAyTmOnUA6bPvHrbLQ4JdYHvQhSXKNCDBG9dgUB1RjBW+CuP89QcOKcbXX
nQvp6oJ24urU0Q7OcDxjdefxIoriG7sZy+fJFy+rI3aA0IpEVsz4RHDVQp1a8mCM
kmxArhRtB93DAGCnZYNP77Wn7QGeRR2M/xnKw93x7I4OSrFh28KU+IKokXa0VGql
cXn0NUQ0iPexUG6vkgRwTyAqSeArUueuHE2E8a38spvIAJfSf3B1vW/Q6rXAa1gP
jiUjrG2rAneHqwGO/pX+/J3aJAMdgwWAstkMlKGV1PcsJA5u6tgKjnai3TmLwe5n
EQpCfTL94/m0Sx4us9JGRHQZVazJBtkL8W5Rzo2VzQMv9n978zmRC5eO4gpwOcjX
8xdTnBS/A3YHbTB0advXvdR4ijP5+lo4bcV/lIu8pWB9p6shgfcjQ+zl5X4kjKbK
MICOmOgQxh/XwsojUWc/HwzuZ+l+unE/zi3DQNqayzl1uQsuVGPx1WKTlXK6cTWE
HfpZdWw4sPo57edxb/OTAkuRWbObyQ29wDNUaDxgjH6lxSFrJXU9QSrkmaEvshzp
drxTYMzFvKqn5IywdVOjjs6cyqtZzC6RRyHw8mFiV/NT05xPBtNV+giplidmdlo9
yqXqc93FV5PEovvjAWK6UG3COnq9vQwyIsOUWRFZRj6WblW8Bv4=
=XQl6
-----END PGP SIGNATURE-----

```
