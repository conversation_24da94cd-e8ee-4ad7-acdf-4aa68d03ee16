---
date: '2019-08-20T19:40:04.120Z'
category: release
title: Node v12.9.0 (Current)
layout: blog-post
author: <PERSON><PERSON><PERSON>
---

### Notable changes

- **crypto**:
  - Added an oaepHash option to asymmetric encryption which allows users to specify a hash function when using OAEP padding (<PERSON>) [#28335](https://github.com/nodejs/node/pull/28335).
- **deps**:
  - Updated V8 to 7.6.303.29 (<PERSON><PERSON><PERSON>) [#28955](https://github.com/nodejs/node/pull/28955).
    - Improves the performance of various APIs such as `JSON.parse` and methods
      called on frozen arrays.
    - Adds the [`Promise.allSettled`](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise/allSettled) method.
    - Improves support of `BigInt` in `Intl` methods.
    - For more information: https://v8.dev/blog/v8-release-76
  - Updated libuv to 1.31.0 (cji<PERSON>ig) [#29070](https://github.com/nodejs/node/pull/29070).
    - `UV_FS_O_FILEMAP` has been added for faster access to memory mapped files on Windows.
    - `uv_fs_mkdir()` now returns `UV_EINVAL` for invalid filenames on Windows. It previously returned `UV_ENOENT`.
    - The `uv_fs_statfs()` API has been added.
    - The `uv_os_environ()` and `uv_os_free_environ()` APIs have been added.
- **fs**:
  - Added `fs.writev`, `fs.writevSync` and `filehandle.writev` (promise version) methods. They allow to write an array of `ArrayBufferView`s to a file descriptor (Anas Aboureada) [#25925](https://github.com/nodejs/node/pull/25925), (cjihrig) [#29186](https://github.com/nodejs/node/pull/29186).
- **http**:
  - Added three properties to `OutgoingMessage.prototype`: `writableObjectMode`, `writableLength` and `writableHighWaterMark` [#29018](https://github.com/nodejs/node/pull/29018).
- **stream**:
  - Added an new property `readableEnded` to readable streams. Its value is set to `true` when the `'end'` event is emitted. (Robert Nagy) [#28814](https://github.com/nodejs/node/pull/28814).
  - Added an new property `writableEnded` to writable streams. Its value is set to `true` after `writable.end()` has been called. (Robert Nagy) [#28934](https://github.com/nodejs/node/pull/28934).

### Commits

- [[`5008b46159`](https://github.com/nodejs/node/commit/5008b46159)] - **benchmark**: allow easy passing of process flags (Brian White) [#28986](https://github.com/nodejs/node/pull/28986)
- [[`9057814206`](https://github.com/nodejs/node/commit/9057814206)] - **buffer**: improve copy() performance (Brian White) [#29066](https://github.com/nodejs/node/pull/29066)
- [[`c7a4525bbe`](https://github.com/nodejs/node/commit/c7a4525bbe)] - **buffer**: improve ERR_BUFFER_OUT_OF_BOUNDS default (cjihrig) [#29098](https://github.com/nodejs/node/pull/29098)
- [[`f42eb01d1d`](https://github.com/nodejs/node/commit/f42eb01d1d)] - **build**: enable linux large pages LLVM lld linkage support (David Carlier) [#28938](https://github.com/nodejs/node/pull/28938)
- [[`5c5ef4e858`](https://github.com/nodejs/node/commit/5c5ef4e858)] - **build**: remove unused option (Rich Trott) [#29173](https://github.com/nodejs/node/pull/29173)
- [[`fbe25c7fb7`](https://github.com/nodejs/node/commit/fbe25c7fb7)] - **build**: remove unnecessary Python semicolon (MattIPv4) [#29170](https://github.com/nodejs/node/pull/29170)
- [[`e51f924964`](https://github.com/nodejs/node/commit/e51f924964)] - **build**: add a testclean target (Daniel Bevenius) [#29094](https://github.com/nodejs/node/pull/29094)
- [[`0a63e2d9ff`](https://github.com/nodejs/node/commit/0a63e2d9ff)] - **build**: support py3 for configure.py (cclauss) [#29106](https://github.com/nodejs/node/pull/29106)
- [[`b04f9e1f57`](https://github.com/nodejs/node/commit/b04f9e1f57)] - **build**: reset embedder string to "-node.0" (Michaël Zasso) [#28955](https://github.com/nodejs/node/pull/28955)
- [[`b085b94fce`](https://github.com/nodejs/node/commit/b085b94fce)] - **console**: minor timeLogImpl() refactor (cjihrig) [#29100](https://github.com/nodejs/node/pull/29100)
- [[`54197eac4d`](https://github.com/nodejs/node/commit/54197eac4d)] - **(SEMVER-MINOR)** **crypto**: extend RSA-OAEP support with oaepHash (Tobias Nießen) [#28335](https://github.com/nodejs/node/pull/28335)
- [[`a17d398989`](https://github.com/nodejs/node/commit/a17d398989)] - **deps**: V8: cherry-pick e3d7f8a (cclauss) [#29105](https://github.com/nodejs/node/pull/29105)
- [[`2c91c65961`](https://github.com/nodejs/node/commit/2c91c65961)] - **deps**: upgrade to libuv 1.31.0 (cjihrig) [#29070](https://github.com/nodejs/node/pull/29070)
- [[`53c7fac310`](https://github.com/nodejs/node/commit/53c7fac310)] - **deps**: patch V8 to be API/ABI compatible with 7.4 (from 7.6) (Michaël Zasso) [#28955](https://github.com/nodejs/node/pull/28955)
- [[`7b8eb83895`](https://github.com/nodejs/node/commit/7b8eb83895)] - **(SEMVER-MINOR)** **deps**: patch V8 to be API/ABI compatible with 7.4 (from 7.5) (Michaël Zasso) [#28005](https://github.com/nodejs/node/pull/28005)
- [[`7d411f47b2`](https://github.com/nodejs/node/commit/7d411f47b2)] - **deps**: V8: backport b33af60 (Gus Caplan) [#28671](https://github.com/nodejs/node/pull/28671)
- [[`492b7cb8c3`](https://github.com/nodejs/node/commit/492b7cb8c3)] - **(SEMVER-MINOR)** **deps**: V8: cherry-pick d2ccc59 (Michaël Zasso) [#28016](https://github.com/nodejs/node/pull/28016)
- [[`945955ff86`](https://github.com/nodejs/node/commit/945955ff86)] - **deps**: cherry-pick 13a04aba from V8 upstream (Jon Kunkee) [#28602](https://github.com/nodejs/node/pull/28602)
- [[`9b3c115efb`](https://github.com/nodejs/node/commit/9b3c115efb)] - **(SEMVER-MINOR)** **deps**: V8: cherry-pick 3b8c624 (Michaël Zasso) [#28016](https://github.com/nodejs/node/pull/28016)
- [[`533b2d4a08`](https://github.com/nodejs/node/commit/533b2d4a08)] - **(SEMVER-MINOR)** **deps**: V8: fix linking issue for MSVS (Refael Ackermann) [#28016](https://github.com/nodejs/node/pull/28016)
- [[`c9f8d28f71`](https://github.com/nodejs/node/commit/c9f8d28f71)] - **(SEMVER-MINOR)** **deps**: V8: fix BUILDING_V8_SHARED issues (Refael Ackermann) [#27375](https://github.com/nodejs/node/pull/27375)
- [[`f24caeff2f`](https://github.com/nodejs/node/commit/f24caeff2f)] - **(SEMVER-MINOR)** **deps**: V8: add workaround for MSVC optimizer bug (Refael Ackermann) [#28016](https://github.com/nodejs/node/pull/28016)
- [[`94c8d068f8`](https://github.com/nodejs/node/commit/94c8d068f8)] - **(SEMVER-MINOR)** **deps**: V8: use ATOMIC_VAR_INIT instead of std::atomic_init (Refael Ackermann) [#27375](https://github.com/nodejs/node/pull/27375)
- [[`d940403c20`](https://github.com/nodejs/node/commit/d940403c20)] - **(SEMVER-MINOR)** **deps**: V8: forward declaration of `Rtl*FunctionTable` (Refael Ackermann) [#27375](https://github.com/nodejs/node/pull/27375)
- [[`ac0c075cad`](https://github.com/nodejs/node/commit/ac0c075cad)] - **(SEMVER-MINOR)** **deps**: V8: patch register-arm64.h (Refael Ackermann) [#27375](https://github.com/nodejs/node/pull/27375)
- [[`eefbc0773f`](https://github.com/nodejs/node/commit/eefbc0773f)] - **(SEMVER-MINOR)** **deps**: V8: update postmortem metadata generation script (cjihrig) [#28016](https://github.com/nodejs/node/pull/28016)
- [[`33f3e383da`](https://github.com/nodejs/node/commit/33f3e383da)] - **(SEMVER-MINOR)** **deps**: V8: silence irrelevant warning (Michaël Zasso) [#26685](https://github.com/nodejs/node/pull/26685)
- [[`434c127651`](https://github.com/nodejs/node/commit/434c127651)] - **(SEMVER-MINOR)** **deps**: V8: un-cherry-pick bd019bd (Refael Ackermann) [#26685](https://github.com/nodejs/node/pull/26685)
- [[`040e7dabe4`](https://github.com/nodejs/node/commit/040e7dabe4)] - **(SEMVER-MINOR)** **deps**: V8: fix filename manipulation for Windows (Refael Ackermann) [#28016](https://github.com/nodejs/node/pull/28016)
- [[`cfe2484e04`](https://github.com/nodejs/node/commit/cfe2484e04)] - **deps**: update V8 to 7.6.303.29 (Michaël Zasso) [#28955](https://github.com/nodejs/node/pull/28955)
- [[`6f7b561295`](https://github.com/nodejs/node/commit/6f7b561295)] - **dns**: update lookupService() first arg name (cjihrig) [#29040](https://github.com/nodejs/node/pull/29040)
- [[`ad28f555a1`](https://github.com/nodejs/node/commit/ad28f555a1)] - **doc**: improve example single-test command (David Guttman) [#29171](https://github.com/nodejs/node/pull/29171)
- [[`edbe38d52d`](https://github.com/nodejs/node/commit/edbe38d52d)] - **doc**: mention N-API as recommended approach (Michael Dawson) [#28922](https://github.com/nodejs/node/pull/28922)
- [[`ebfe6367f0`](https://github.com/nodejs/node/commit/ebfe6367f0)] - **doc**: fix introduced_in note in querystring.md (Ben Noordhuis) [#29014](https://github.com/nodejs/node/pull/29014)
- [[`9ead4eceeb`](https://github.com/nodejs/node/commit/9ead4eceeb)] - **doc**: note that stream error can close stream (Robert Nagy) [#29082](https://github.com/nodejs/node/pull/29082)
- [[`5ea9237d2b`](https://github.com/nodejs/node/commit/5ea9237d2b)] - **doc**: clarify async iterator leak (Robert Nagy) [#28997](https://github.com/nodejs/node/pull/28997)
- [[`1b6d7c0d00`](https://github.com/nodejs/node/commit/1b6d7c0d00)] - **doc**: fix nits in child_process.md (Vse Mozhet Byt) [#29024](https://github.com/nodejs/node/pull/29024)
- [[`375d1ee58b`](https://github.com/nodejs/node/commit/375d1ee58b)] - **doc**: improve UV_THREADPOOL_SIZE description (Tobias Nießen) [#29033](https://github.com/nodejs/node/pull/29033)
- [[`7b7b8f21cb`](https://github.com/nodejs/node/commit/7b7b8f21cb)] - **doc**: documented default statusCode (Natalie Fearnley) [#28982](https://github.com/nodejs/node/pull/28982)
- [[`17d9495afa`](https://github.com/nodejs/node/commit/17d9495afa)] - **doc**: make unshift doc compliant with push doc (EduardoRFS) [#28953](https://github.com/nodejs/node/pull/28953)
- [[`3bfca0b7e4`](https://github.com/nodejs/node/commit/3bfca0b7e4)] - **doc, lib, src, test, tools**: fix assorted typos (XhmikosR) [#29075](https://github.com/nodejs/node/pull/29075)
- [[`b7696b41e5`](https://github.com/nodejs/node/commit/b7696b41e5)] - **events**: give subclass name in unhandled 'error' message (Anna Henningsen) [#28952](https://github.com/nodejs/node/pull/28952)
- [[`d79d142e0c`](https://github.com/nodejs/node/commit/d79d142e0c)] - **fs**: use fs.writev() internally (Robert Nagy) [#29189](https://github.com/nodejs/node/pull/29189)
- [[`82eeadb216`](https://github.com/nodejs/node/commit/82eeadb216)] - **fs**: use consistent buffer array validation (cjihrig) [#29186](https://github.com/nodejs/node/pull/29186)
- [[`81f3eb5bb1`](https://github.com/nodejs/node/commit/81f3eb5bb1)] - **fs**: add writev() promises version (cjihrig) [#29186](https://github.com/nodejs/node/pull/29186)
- [[`435683610b`](https://github.com/nodejs/node/commit/435683610b)] - **fs**: validate writev fds consistently (cjihrig) [#29185](https://github.com/nodejs/node/pull/29185)
- [[`bb19d8212a`](https://github.com/nodejs/node/commit/bb19d8212a)] - **(SEMVER-MINOR)** **fs**: add fs.writev() which exposes syscall writev() (Anas Aboureada) [#25925](https://github.com/nodejs/node/pull/25925)
- [[`178caa56ae`](https://github.com/nodejs/node/commit/178caa56ae)] - **fs**: add default options for \*stat() (Tony Brix) [#29114](https://github.com/nodejs/node/pull/29114)
- [[`f194626ffb`](https://github.com/nodejs/node/commit/f194626ffb)] - **fs**: validate fds as int32s (cjihrig) [#28984](https://github.com/nodejs/node/pull/28984)
- [[`c5edeb9776`](https://github.com/nodejs/node/commit/c5edeb9776)] - **http**: simplify drain() (Robert Nagy) [#29081](https://github.com/nodejs/node/pull/29081)
- [[`6b9e372198`](https://github.com/nodejs/node/commit/6b9e372198)] - **http**: follow symbol naming convention (Robert Nagy) [#29091](https://github.com/nodejs/node/pull/29091)
- [[`2e5008848e`](https://github.com/nodejs/node/commit/2e5008848e)] - **http**: remove redundant condition (Luigi Pinca) [#29078](https://github.com/nodejs/node/pull/29078)
- [[`13a497912d`](https://github.com/nodejs/node/commit/13a497912d)] - **http**: remove duplicate check (Robert Nagy) [#29022](https://github.com/nodejs/node/pull/29022)
- [[`16e001112c`](https://github.com/nodejs/node/commit/16e001112c)] - **(SEMVER-MINOR)** **http**: add missing stream-like properties to OutgoingMessage (Robert Nagy) [#29018](https://github.com/nodejs/node/pull/29018)
- [[`c396b2a5b2`](https://github.com/nodejs/node/commit/c396b2a5b2)] - **http**: buffer writes even while no socket assigned (Robert Nagy) [#29019](https://github.com/nodejs/node/pull/29019)
- [[`f9b61d2bc7`](https://github.com/nodejs/node/commit/f9b61d2bc7)] - **(SEMVER-MINOR)** **http,stream**: add writableEnded (Robert Nagy) [#28934](https://github.com/nodejs/node/pull/28934)
- [[`964dff8a9e`](https://github.com/nodejs/node/commit/964dff8a9e)] - **http2**: remove unused FlushData() function (Anna Henningsen) [#29145](https://github.com/nodejs/node/pull/29145)
- [[`66249bbcad`](https://github.com/nodejs/node/commit/66249bbcad)] - **inspector**: use const for contextGroupId (gengjiawen) [#29076](https://github.com/nodejs/node/pull/29076)
- [[`cb162298eb`](https://github.com/nodejs/node/commit/cb162298eb)] - **module**: pkg exports validations and fallbacks (Guy Bedford) [#28949](https://github.com/nodejs/node/pull/28949)
- [[`491b46d605`](https://github.com/nodejs/node/commit/491b46d605)] - **module**: refine package name validation (Guy Bedford) [#28965](https://github.com/nodejs/node/pull/28965)
- [[`925e40cfa7`](https://github.com/nodejs/node/commit/925e40cfa7)] - **module**: add warning when import,export is detected in CJS context (Giorgos Ntemiris) [#28950](https://github.com/nodejs/node/pull/28950)
- [[`17319e7f44`](https://github.com/nodejs/node/commit/17319e7f44)] - **net**: use callback to properly propagate error (Robert Nagy) [#29178](https://github.com/nodejs/node/pull/29178)
- [[`7f11c72cc5`](https://github.com/nodejs/node/commit/7f11c72cc5)] - **readline**: close dumb terminals on Control+D (cjihrig) [#29149](https://github.com/nodejs/node/pull/29149)
- [[`3c346b8bad`](https://github.com/nodejs/node/commit/3c346b8bad)] - **readline**: close dumb terminals on Control+C (cjihrig) [#29149](https://github.com/nodejs/node/pull/29149)
- [[`e474c6776c`](https://github.com/nodejs/node/commit/e474c6776c)] - **(SEMVER-MINOR)** **readline**: establish y in cursorTo as optional (Gerhard Stoebich) [#29128](https://github.com/nodejs/node/pull/29128)
- [[`2528dee674`](https://github.com/nodejs/node/commit/2528dee674)] - **report**: list envvars using uv_os_environ() (cjihrig) [#28963](https://github.com/nodejs/node/pull/28963)
- [[`a6b9299039`](https://github.com/nodejs/node/commit/a6b9299039)] - **src**: rename --security-reverts to ...-revert (Sam Roberts) [#29153](https://github.com/nodejs/node/pull/29153)
- [[`62a0e91d8c`](https://github.com/nodejs/node/commit/62a0e91d8c)] - **src**: simplify UnionBytes (Ben Noordhuis) [#29116](https://github.com/nodejs/node/pull/29116)
- [[`b2936cff5d`](https://github.com/nodejs/node/commit/b2936cff5d)] - **src**: organize imports in inspector_profiler.cc (pi1024e) [#29073](https://github.com/nodejs/node/pull/29073)
- [[`a9f8b62b47`](https://github.com/nodejs/node/commit/a9f8b62b47)] - **(SEMVER-MINOR)** **stream**: add readableEnded (Robert Nagy) [#28814](https://github.com/nodejs/node/pull/28814)
- [[`1e3e6da9b9`](https://github.com/nodejs/node/commit/1e3e6da9b9)] - **stream**: simplify howMuchToRead() (Robert Nagy) [#29155](https://github.com/nodejs/node/pull/29155)
- [[`e2a2a3f4dd`](https://github.com/nodejs/node/commit/e2a2a3f4dd)] - **stream**: use lazy registration for drain for fast destinations (Robert Nagy) [#29095](https://github.com/nodejs/node/pull/29095)
- [[`2a844599db`](https://github.com/nodejs/node/commit/2a844599db)] - **stream**: improve read() performance further (Brian White) [#29077](https://github.com/nodejs/node/pull/29077)
- [[`e543d35f35`](https://github.com/nodejs/node/commit/e543d35f35)] - **stream**: inline and simplify onwritedrain (Robert Nagy) [#29037](https://github.com/nodejs/node/pull/29037)
- [[`6bc4620d8b`](https://github.com/nodejs/node/commit/6bc4620d8b)] - **stream**: improve read() performance more (Brian White) [#28989](https://github.com/nodejs/node/pull/28989)
- [[`647f3a8d01`](https://github.com/nodejs/node/commit/647f3a8d01)] - **stream**: encapsulate buffer-list (Robert Nagy) [#28974](https://github.com/nodejs/node/pull/28974)
- [[`000999c06c`](https://github.com/nodejs/node/commit/000999c06c)] - **stream**: improve read() performance (Brian White) [#28961](https://github.com/nodejs/node/pull/28961)
- [[`6bafd35369`](https://github.com/nodejs/node/commit/6bafd35369)] - **test**: deflake test-tls-passphrase (Luigi Pinca) [#29134](https://github.com/nodejs/node/pull/29134)
- [[`aff1ef9d27`](https://github.com/nodejs/node/commit/aff1ef9d27)] - **test**: add required settings to test-benchmark-buffer (Rich Trott) [#29163](https://github.com/nodejs/node/pull/29163)
- [[`18b711366a`](https://github.com/nodejs/node/commit/18b711366a)] - **test**: make exported method static (Rainer Poisel) [#29102](https://github.com/nodejs/node/pull/29102)
- [[`0aa339e5e1`](https://github.com/nodejs/node/commit/0aa339e5e1)] - **test**: skip test-fs-access if root (Daniel Bevenius) [#29092](https://github.com/nodejs/node/pull/29092)
- [[`e3b1243ea2`](https://github.com/nodejs/node/commit/e3b1243ea2)] - **test**: unskip tests that now pass on AIX (Sam Roberts) [#29054](https://github.com/nodejs/node/pull/29054)
- [[`f9ed5f351b`](https://github.com/nodejs/node/commit/f9ed5f351b)] - **test**: assert: add failing deepEqual test for faked boxed primitives (Jordan Harband) [#29029](https://github.com/nodejs/node/pull/29029)
- [[`43e444b07a`](https://github.com/nodejs/node/commit/43e444b07a)] - **test**: refactor test-sync-io-option (Anna Henningsen) [#29020](https://github.com/nodejs/node/pull/29020)
- [[`82edebfebf`](https://github.com/nodejs/node/commit/82edebfebf)] - **(SEMVER-MINOR)** **test**: add test for OAEP hash mismatch (Tobias Nießen) [#28335](https://github.com/nodejs/node/pull/28335)
- [[`f08f154fd6`](https://github.com/nodejs/node/commit/f08f154fd6)] - **(SEMVER-MINOR)** **test**: update postmortem metadata test for V8 7.6 (cjihrig) [#28016](https://github.com/nodejs/node/pull/28016)
- [[`5b892c44d6`](https://github.com/nodejs/node/commit/5b892c44d6)] - **tls**: allow client-side sockets to be half-opened (Luigi Pinca) [#27836](https://github.com/nodejs/node/pull/27836)
- [[`c4f6077994`](https://github.com/nodejs/node/commit/c4f6077994)] - **tools**: make code cache and snapshot deterministic (Ben Noordhuis) [#29142](https://github.com/nodejs/node/pull/29142)
- [[`acdc21b832`](https://github.com/nodejs/node/commit/acdc21b832)] - **tools**: make pty_helper.py python3-compatible (Ben Noordhuis) [#29167](https://github.com/nodejs/node/pull/29167)
- [[`31c50e5c17`](https://github.com/nodejs/node/commit/31c50e5c17)] - **tools**: make nodedownload.py Python 3 compatible (cclauss) [#29104](https://github.com/nodejs/node/pull/29104)
- [[`1011a1792c`](https://github.com/nodejs/node/commit/1011a1792c)] - **tools**: allow single JS file for --link-module (Daniel Bevenius) [#28443](https://github.com/nodejs/node/pull/28443)
- [[`4d7a7957fc`](https://github.com/nodejs/node/commit/4d7a7957fc)] - **(SEMVER-MINOR)** **tools**: sync gypfiles with V8 7.6 (Michaël Zasso) [#28016](https://github.com/nodejs/node/pull/28016)
- [[`a4e2549b87`](https://github.com/nodejs/node/commit/a4e2549b87)] - **util**: improve debuglog performance (Brian White) [#28956](https://github.com/nodejs/node/pull/28956)
- [[`112ec73c95`](https://github.com/nodejs/node/commit/112ec73c95)] - **util**: isEqualBoxedPrimitive: ensure both values are actual boxed Symbols (Jordan Harband) [#29029](https://github.com/nodejs/node/pull/29029)
- [[`8426077898`](https://github.com/nodejs/node/commit/8426077898)] - **util**: assert: fix deepEqual comparing fake-boxed to real boxed primitive (Jordan Harband) [#29029](https://github.com/nodejs/node/pull/29029)
- [[`d4e397a900`](https://github.com/nodejs/node/commit/d4e397a900)] - **worker**: fix crash when SharedArrayBuffer outlives creating thread (Anna Henningsen) [#29190](https://github.com/nodejs/node/pull/29190)

Windows 32-bit Installer: https://nodejs.org/dist/v12.9.0/node-v12.9.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v12.9.0/node-v12.9.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v12.9.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v12.9.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v12.9.0/node-v12.9.0.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v12.9.0/node-v12.9.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v12.9.0/node-v12.9.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v12.9.0/node-v12.9.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v12.9.0/node-v12.9.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v12.9.0/node-v12.9.0-aix-ppc64.tar.gz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v12.9.0/node-v12.9.0-sunos-x64.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v12.9.0/node-v12.9.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v12.9.0/node-v12.9.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v12.9.0/node-v12.9.0.tar.gz \
Other release files: https://nodejs.org/dist/v12.9.0/ \
Documentation: https://nodejs.org/docs/v12.9.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

c690810b11e286778c0eacb8f1f56af1973d71b1788c54040f25380fc3ff6bcd  node-v12.9.0-aix-ppc64.tar.gz
24c1f0c93e485961446814662db942f1b309d843fb4ecbe50466d9857a51b343  node-v12.9.0-darwin-x64.tar.gz
3e3dcfbfb78c55e582bb16ad6d995edfa93fdb3c4fe2020981e4f0adae255821  node-v12.9.0-darwin-x64.tar.xz
4dfb4147c8e41bacf7ac2bfcc844ec5ff4721cd47ace4b8a7e8ef07548d44773  node-v12.9.0-headers.tar.gz
4baa0822d0321522cdcedfc2025e9395c3a34ea6ef4af53a3147a66f23778219  node-v12.9.0-headers.tar.xz
703134352854e3501023cda8eba68f1f992a426f1cda649dc6b38e248b07fcf1  node-v12.9.0-linux-arm64.tar.gz
f7f6f102d097d64eba26f84f2760597f9831886ef7d0db3cba88459847f2743d  node-v12.9.0-linux-arm64.tar.xz
05e3f81f17528d24409028bc66cb03fa18892af4d63e7cddaeb1189fe28ed005  node-v12.9.0-linux-armv7l.tar.gz
689d9e0e180bceb268876d95c86c3d8e64eb0440a90cd2e57c61899209d05acb  node-v12.9.0-linux-armv7l.tar.xz
a5b0ac5557df1588d2b74a4ec33a6cf4feb0586d899b048d1eb9befd5de8a738  node-v12.9.0-linux-ppc64le.tar.gz
d5b1f14753eb8c084a22e709f38d024cf7d974cee67ed095a78665d0b62a6b69  node-v12.9.0-linux-ppc64le.tar.xz
149243fed728766eb31eb51534dba53a8eb25a58d08d6b1a354dea8deeade987  node-v12.9.0-linux-s390x.tar.gz
0c4b933cbce5f7153effed002e7aa58abde650256aae05ae951b1e6cfde1c4e6  node-v12.9.0-linux-s390x.tar.xz
3991e5b65fbaab8d70cda813b9bc73dd3b4201aeef42bcb1c000a03869aa0610  node-v12.9.0-linux-x64.tar.gz
7110bdd16e397870142ff0e8d92d4a4502d43ec047d970c843a9a4e5f9e79283  node-v12.9.0-linux-x64.tar.xz
7a18632a187137f21f88dbc0ae85c8a34d20fccb4b7c26be665fdc56a929c3fc  node-v12.9.0.pkg
b0260415bf006330ca282a370dc56fcd018981a0024512785ea545fc49fd1d0f  node-v12.9.0-sunos-x64.tar.gz
d8ad03d2c9f09d605607fc211749f53c708f8ff51a254f6731d9544c95d9112d  node-v12.9.0-sunos-x64.tar.xz
88cb425086a87323288c8389e974e8c1001b81fe11c529e64592e8feb2d12f93  node-v12.9.0.tar.gz
e05fe8ba1bdf58e51cdb5d946ebb7a2dec567b3680da2752e120a763293a41c5  node-v12.9.0.tar.xz
2c25b307d7acbeeb1fb618587ceb6769391a5cfd8ee9be805e1ac5344b9f3a2f  node-v12.9.0-win-x64.7z
b5f05deb31ac04b9c3a487542daf151e01c05017403a56ba443da623f36b153b  node-v12.9.0-win-x64.zip
87adc248df2b1be102e5025b571849638ac337f595ce6c5dbec3d8cea9d374ce  node-v12.9.0-win-x86.7z
a0d680846dff00222b886164e1428d257aa8031a63b52cc21de6540386a4e2d0  node-v12.9.0-win-x86.zip
7e6c6855fc4eb44b95a033f4ad8d73d7823388dd01ceb98a91eb68a7625323f3  node-v12.9.0-x64.msi
cdf09fc5223c4c47e5182803164d641365ef54eaa6dc3fd4c580fe1255faa433  node-v12.9.0-x86.msi
d5e9555256523c188af94ad9736c3a4492578842ccdfe8318ccf9caec2977bfd  win-x64/node.exe
654cb8682984cbed2ca698e5b3ebd05edaa60174d964432143bdfc0c38254aa7  win-x64/node.lib
7fb28adbb18c03ecd8a8fc4e5da6a715d7adf3b79a8c626638d0c07faad6e47b  win-x64/node_pdb.7z
417b7393c519c70ed96377a50323150714c0c931a571a97789c0f0ba352d2d73  win-x64/node_pdb.zip
cc2f13a4028a5c72aef8d03cb503b76036b87e3dcf400b7e000a30ae39e7f652  win-x86/node.exe
df075eff064bd3e081d848484b4b7c99b3a022a8a8e5988b2d17dbf4d0489a56  win-x86/node.lib
f2b5001a3d5bf3682c5feec610844a4f112d274e6c9b796d38dfde16ff7b0991  win-x86/node_pdb.7z
5f180908b937afbef929f4ad07f7b3355c2510d50e88ec053798b3bdbc57be3b  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEj8yhP+8dDC6RAI4Jdw96mlrhVgAFAl1cTCgACgkQdw96mlrh
VgBjJA/9G6n17aIguQqqe9lmAstCDgx5zhCTnVI9vE6CtYB50HIUFQKHlc8+ijXy
QC667tTWcyoLleSEuERnWrymf1Pv+6kVaD+stQnl6nT8ioMKoJ1u16c9NOnErVoj
+AtAR16d9aXXVLTXWO1iy9jOxrXrReUpRh8tgHZ2cBUBrBY3yZZhWaeFSU0fPa1E
N/WxKeyfByLnnOc817QH6a7BUQikx2H6ZRiWis36eGMN/tTZCvh8Bw/mgGg+0jvd
YQhsj784ZoKnUdzEnCceq5WyEwFr/nFsTsrBwbSR109iysv+eQZOaqXaOI2+4Hsa
BQj/2DXuFGog79B9WO7BetkD9LqBPOP6mLXRP70Mbjnm+7GayYi1wblzYpiqekEJ
oyci1ph/AKAXkppzVo/dcIKhFvkJWKAhme8RuRSnc5e7w4uB0x3hrVZBSHrmOUgF
Miu+//N8Sh86Pww1wfmWYy253wC1sbCPgWf4whOjZ1YLf56TbsG223PjZXebI49E
NhX7VqlQC1u9JV3VvmGVHBrL0X0w3+jMcm32tfWlT8Dgzbo8jYO0eUEFNuGqa5uL
kbvyB0W2nFpK5uOPR7JOO32jt+k2y2TEYcbfnL7sYuYL95tP/UNPvjYO0AN9Rk02
dAqybpZw4nLwmge89LYTFo4/YRt1NJZ86c6sN86fNck7HHKYIms=
=o1jr
-----END PGP SIGNATURE-----

```
