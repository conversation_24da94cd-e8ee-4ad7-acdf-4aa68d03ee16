---
date: '2021-01-15T01:08:39.094Z'
category: release
title: Node v15.6.0 (Current)
layout: blog-post
author: <PERSON>
---

### Notable Changes

- **child_process**:
  - add 'overlapped' stdio flag (<PERSON><PERSON><PERSON>) [#29412](https://github.com/nodejs/node/pull/29412)
  - support AbortSignal in fork (<PERSON>) [#36603](https://github.com/nodejs/node/pull/36603)
- **crypto**:
  - implement basic secure heap support (<PERSON>) [#36779](https://github.com/nodejs/node/pull/36779)
  - fixup bug in keygen error handling (<PERSON>) [#36779](https://github.com/nodejs/node/pull/36779)
  - introduce X509Certificate API (<PERSON>) [#36804](https://github.com/nodejs/node/pull/36804)
  - implement randomuuid (<PERSON>) [#36729](https://github.com/nodejs/node/pull/36729)
- **doc**:
  - update release key for <PERSON> (<PERSON>) [#36793](https://github.com/nodejs/node/pull/36793)
  - add dnlup to collaborators (Daniele Belardi) [#36849](https://github.com/nodejs/node/pull/36849)
  - add panva to collaborators (Filip Skokan) [#36802](https://github.com/nodejs/node/pull/36802)
  - add yashLadha to collaborator (Yash Ladha) [#36666](https://github.com/nodejs/node/pull/36666)
- **http**:
  - set lifo as the default scheduling strategy in Agent (Matteo Collina) [#36685](https://github.com/nodejs/node/pull/36685)
- **net**:
  - support abortSignal in server.listen (Nitzan Uziely) [#36623](https://github.com/nodejs/node/pull/36623)
- **process**:
  - add direct access to rss without iterating pages (Adrien Maret) [#34291](https://github.com/nodejs/node/pull/34291)
- **v8**:
  - fix native `serdes` constructors (ExE Boss) [#36549](https://github.com/nodejs/node/pull/36549)

### Commits

- [[`3ca7a786c5`](https://github.com/nodejs/node/commit/3ca7a786c5)] - **benchmark**: fix http2 benchmarks (Rich Trott) [#36871](https://github.com/nodejs/node/pull/36871)
- [[`4601886d7c`](https://github.com/nodejs/node/commit/4601886d7c)] - **benchmark**: fix http/headers.js with test-double (Rich Trott) [#36794](https://github.com/nodejs/node/pull/36794)
- [[`7aedda9dcd`](https://github.com/nodejs/node/commit/7aedda9dcd)] - **benchmark**: add simple https benchmark (Andrey Pechkurov) [#36612](https://github.com/nodejs/node/pull/36612)
- [[`822ac48272`](https://github.com/nodejs/node/commit/822ac48272)] - **buffer**: make FastBuffer safe to construct (Antoine du Hamel) [#36587](https://github.com/nodejs/node/pull/36587)
- [[`21f329532f`](https://github.com/nodejs/node/commit/21f329532f)] - **build**: refactor Makefile (raisinten) [#36759](https://github.com/nodejs/node/pull/36759)
- [[`857b98eed9`](https://github.com/nodejs/node/commit/857b98eed9)] - **build**: fix unknown warning option (raisinten) [#36629](https://github.com/nodejs/node/pull/36629)
- [[`ffaa8c1735`](https://github.com/nodejs/node/commit/ffaa8c1735)] - **build**: do not "exit" a script meant to be "source"d (François-Denis Gonthier) [#35520](https://github.com/nodejs/node/pull/35520)
- [[`9bc2cec848`](https://github.com/nodejs/node/commit/9bc2cec848)] - **(SEMVER-MINOR)** **child_process**: add 'overlapped' stdio flag (Thiago Padilha) [#29412](https://github.com/nodejs/node/pull/29412)
- [[`b98cc51be2`](https://github.com/nodejs/node/commit/b98cc51be2)] - **child_process**: reduce abort handler code duplication (Rich Trott) [#36644](https://github.com/nodejs/node/pull/36644)
- [[`78d4d91e54`](https://github.com/nodejs/node/commit/78d4d91e54)] - **child_process**: treat already-aborted controller as aborting (Rich Trott) [#36644](https://github.com/nodejs/node/pull/36644)
- [[`a8a427f646`](https://github.com/nodejs/node/commit/a8a427f646)] - **(SEMVER-MINOR)** **child_process**: support AbortSignal in fork (Benjamin Gruenbaum) [#36603](https://github.com/nodejs/node/pull/36603)
- [[`7134d49e56`](https://github.com/nodejs/node/commit/7134d49e56)] - **child_process**: clean event listener correctly (Benjamin Gruenbaum) [#36424](https://github.com/nodejs/node/pull/36424)
- [[`54bd4ab855`](https://github.com/nodejs/node/commit/54bd4ab855)] - **cluster**: fix edge cases that throw ERR_INTERNAL_ASSERTION (Ouyang Yadong) [#36764](https://github.com/nodejs/node/pull/36764)
- [[`0c11a17d82`](https://github.com/nodejs/node/commit/0c11a17d82)] - **console**: refactor to avoid unsafe array iteration (Antoine du Hamel) [#36753](https://github.com/nodejs/node/pull/36753)
- [[`53cf996270`](https://github.com/nodejs/node/commit/53cf996270)] - **(SEMVER-MINOR)** **crypto**: implement basic secure heap support (James M Snell) [#36779](https://github.com/nodejs/node/pull/36779)
- [[`42aca13953`](https://github.com/nodejs/node/commit/42aca13953)] - **(SEMVER-MINOR)** **crypto**: fixup bug in keygen error handling (James M Snell) [#36779](https://github.com/nodejs/node/pull/36779)
- [[`c4ad50e0ff`](https://github.com/nodejs/node/commit/c4ad50e0ff)] - **(SEMVER-MINOR)** **crypto**: introduce X509Certificate API (James M Snell) [#36804](https://github.com/nodejs/node/pull/36804)
- [[`4e4deca90d`](https://github.com/nodejs/node/commit/4e4deca90d)] - **(SEMVER-MINOR)** **crypto**: implement randomuuid (James M Snell) [#36729](https://github.com/nodejs/node/pull/36729)
- [[`1c9ec2529e`](https://github.com/nodejs/node/commit/1c9ec2529e)] - **deps**: upgrade npm to 7.4.0 (Ruy Adorno) [#36829](https://github.com/nodejs/node/pull/36829)
- [[`ff5bd04900`](https://github.com/nodejs/node/commit/ff5bd04900)] - **deps**: update nghttp2 to 1.42.0 (Michaël Zasso) [#36842](https://github.com/nodejs/node/pull/36842)
- [[`578fa0fedf`](https://github.com/nodejs/node/commit/578fa0fedf)] - **deps**: V8: cherry-pick dfcdf7837e23 (Benjamin Coe) [#36573](https://github.com/nodejs/node/pull/36573)
- [[`05f34c6963`](https://github.com/nodejs/node/commit/05f34c6963)] - **doc**: define "browser", "production", "development" (Guy Bedford) [#36856](https://github.com/nodejs/node/pull/36856)
- [[`e8bb1f7350`](https://github.com/nodejs/node/commit/e8bb1f7350)] - **doc**: clarify event.isTrusted text (Rich Trott) [#36827](https://github.com/nodejs/node/pull/36827)
- [[`153be6c80e`](https://github.com/nodejs/node/commit/153be6c80e)] - **doc**: fix module syncBuiltinESMExports example (Bruce A. MacNaughton) [#34284](https://github.com/nodejs/node/pull/34284)
- [[`3b64b38142`](https://github.com/nodejs/node/commit/3b64b38142)] - **doc**: os.uptime() temporary bug notice (Nicholas Schamberg) [#36503](https://github.com/nodejs/node/pull/36503)
- [[`da49624a46`](https://github.com/nodejs/node/commit/da49624a46)] - **doc**: update release key for Danielle Adams (Danielle Adams) [#36793](https://github.com/nodejs/node/pull/36793)
- [[`2d8423da3c`](https://github.com/nodejs/node/commit/2d8423da3c)] - **doc**: clarify child_process.exec inherits cwd (ugultopu) [#36809](https://github.com/nodejs/node/pull/36809)
- [[`1a4d34ebd0`](https://github.com/nodejs/node/commit/1a4d34ebd0)] - **doc**: clarify descriptions of \_writev chunks argument (James M Snell) [#36822](https://github.com/nodejs/node/pull/36822)
- [[`7c7180a6f7`](https://github.com/nodejs/node/commit/7c7180a6f7)] - **doc**: document buffer's "Uint" aliases clearly (Michaël Zasso) [#36796](https://github.com/nodejs/node/pull/36796)
- [[`ff6edbc6b2`](https://github.com/nodejs/node/commit/ff6edbc6b2)] - **doc**: add dnlup to collaborators (Daniele Belardi) [#36849](https://github.com/nodejs/node/pull/36849)
- [[`835bdf0e50`](https://github.com/nodejs/node/commit/835bdf0e50)] - **doc**: improve crypto.randomUUID() text (Rich Trott) [#36830](https://github.com/nodejs/node/pull/36830)
- [[`d4bcb3689d`](https://github.com/nodejs/node/commit/d4bcb3689d)] - **doc**: clarify subprocess.stdout/in/err/io properties (James M Snell) [#36784](https://github.com/nodejs/node/pull/36784)
- [[`a956fb3fdd`](https://github.com/nodejs/node/commit/a956fb3fdd)] - **doc**: add dark mode (Ajay Poshak) [#36313](https://github.com/nodejs/node/pull/36313)
- [[`757b9664cd`](https://github.com/nodejs/node/commit/757b9664cd)] - **doc**: revise method text in async_hooks.md (Rich Trott) [#36736](https://github.com/nodejs/node/pull/36736)
- [[`b4091ea59b`](https://github.com/nodejs/node/commit/b4091ea59b)] - **doc**: clarify when messageerror is emitted (James M Snell) [#36780](https://github.com/nodejs/node/pull/36780)
- [[`61b039365c`](https://github.com/nodejs/node/commit/61b039365c)] - **doc**: avoid memory leak warning in async_hooks example (James M Snell) [#36783](https://github.com/nodejs/node/pull/36783)
- [[`a7bb4da55e`](https://github.com/nodejs/node/commit/a7bb4da55e)] - **doc**: clarify that --require only supports cjs (James M Snell) [#36806](https://github.com/nodejs/node/pull/36806)
- [[`c6eb2b4fec`](https://github.com/nodejs/node/commit/c6eb2b4fec)] - **doc**: clarify Buffer.from when using ArrayBuffer (James M Snell) [#36785](https://github.com/nodejs/node/pull/36785)
- [[`ad1d8fba9f`](https://github.com/nodejs/node/commit/ad1d8fba9f)] - **doc**: fix broken link for ChildProcess (James M Snell) [#36788](https://github.com/nodejs/node/pull/36788)
- [[`ef628891f7`](https://github.com/nodejs/node/commit/ef628891f7)] - **doc**: revise exit() and run() text in async_hooks.md (Rich Trott) [#36738](https://github.com/nodejs/node/pull/36738)
- [[`ff39464559`](https://github.com/nodejs/node/commit/ff39464559)] - **doc**: add OpenSSL CVE fix to notable changes in v15.5.0 (Beth Griggs) [#36798](https://github.com/nodejs/node/pull/36798)
- [[`6db465a99f`](https://github.com/nodejs/node/commit/6db465a99f)] - **doc**: clarify that N-API addons are context-aware (Alba Mendez) [#36640](https://github.com/nodejs/node/pull/36640)
- [[`fad07d5439`](https://github.com/nodejs/node/commit/fad07d5439)] - **doc**: fix typo in esm documentation (Mohamed Kamagate) [#36800](https://github.com/nodejs/node/pull/36800)
- [[`67dd48ed05`](https://github.com/nodejs/node/commit/67dd48ed05)] - **doc**: add panva to collaborators (Filip Skokan) [#36802](https://github.com/nodejs/node/pull/36802)
- [[`b2c1aeb694`](https://github.com/nodejs/node/commit/b2c1aeb694)] - **doc**: revise process.memoryUsage() text (Rich Trott) [#36757](https://github.com/nodejs/node/pull/36757)
- [[`8f672ebbd6`](https://github.com/nodejs/node/commit/8f672ebbd6)] - **doc**: add YAML metadata for process.memoryUsage.rss (Gerhard Stoebich) [#36781](https://github.com/nodejs/node/pull/36781)
- [[`fa54f012b8`](https://github.com/nodejs/node/commit/fa54f012b8)] - **doc**: reduce abbreviations in async_hooks.md (Rich Trott) [#36737](https://github.com/nodejs/node/pull/36737)
- [[`56c00d7b2f`](https://github.com/nodejs/node/commit/56c00d7b2f)] - **doc**: simplify pull request template (Rich Trott) [#36739](https://github.com/nodejs/node/pull/36739)
- [[`214dbac8ff`](https://github.com/nodejs/node/commit/214dbac8ff)] - **doc**: clarify undocumented stream properties (James M Snell) [#36715](https://github.com/nodejs/node/pull/36715)
- [[`242ce19346`](https://github.com/nodejs/node/commit/242ce19346)] - **doc**: document common warning types (James M Snell) [#36713](https://github.com/nodejs/node/pull/36713)
- [[`d3dc124575`](https://github.com/nodejs/node/commit/d3dc124575)] - **doc**: update emitClose default for fs streams (Kevin Locke) [#36653](https://github.com/nodejs/node/pull/36653)
- [[`181bd0510f`](https://github.com/nodejs/node/commit/181bd0510f)] - **doc**: improve ALS.enterWith and exit descriptions (Andrey Pechkurov) [#36705](https://github.com/nodejs/node/pull/36705)
- [[`edf8c6de5a`](https://github.com/nodejs/node/commit/edf8c6de5a)] - **doc**: add note about uncloneable objects (James M Snell) [#36534](https://github.com/nodejs/node/pull/36534)
- [[`651e7d27b7`](https://github.com/nodejs/node/commit/651e7d27b7)] - **doc**: document http.IncomingMessage behaviour change (Dr) [#36641](https://github.com/nodejs/node/pull/36641)
- [[`72b0ab0739`](https://github.com/nodejs/node/commit/72b0ab0739)] - **doc**: add yashLadha to collaborator (Yash Ladha) [#36666](https://github.com/nodejs/node/pull/36666)
- [[`8a0cdb3b4e`](https://github.com/nodejs/node/commit/8a0cdb3b4e)] - **doc**: alphabetize http response properties (Rich Trott) [#36631](https://github.com/nodejs/node/pull/36631)
- [[`ff4674b033`](https://github.com/nodejs/node/commit/ff4674b033)] - **doc**: correct callback parameter type for createPushResponse() (Rich Trott) [#36631](https://github.com/nodejs/node/pull/36631)
- [[`f623d5d377`](https://github.com/nodejs/node/commit/f623d5d377)] - **doc**: use \_code name\_ rather than \_codename\_ (Rich Trott) [#36611](https://github.com/nodejs/node/pull/36611)
- [[`1ed517c176`](https://github.com/nodejs/node/commit/1ed517c176)] - **doc**: document return value of https.request (Michael Chen) [#36370](https://github.com/nodejs/node/pull/36370)
- [[`5645b21e23`](https://github.com/nodejs/node/commit/5645b21e23)] - **doc**: document "http: lazy create IncomingMessage.headers" (ExE Boss) [#36601](https://github.com/nodejs/node/pull/36601)
- [[`3ee4cfc7d7`](https://github.com/nodejs/node/commit/3ee4cfc7d7)] - **doc**: fix bugs in \_construct() example (Maksym Baranovskyi) [#36509](https://github.com/nodejs/node/pull/36509)
- [[`93237c5999`](https://github.com/nodejs/node/commit/93237c5999)] - **doc**: remove replication of GitHub template (Rich Trott) [#36590](https://github.com/nodejs/node/pull/36590)
- [[`538f226f6d`](https://github.com/nodejs/node/commit/538f226f6d)] - **doc**: remove "Related Issues" from pull request template (Rich Trott) [#36590](https://github.com/nodejs/node/pull/36590)
- [[`dcc93d3dce`](https://github.com/nodejs/node/commit/dcc93d3dce)] - **doc**: expand openssl instructions (Michael Dawson) [#36554](https://github.com/nodejs/node/pull/36554)
- [[`41e278bf61`](https://github.com/nodejs/node/commit/41e278bf61)] - **docs**: add references to punycode.md (Isaac Levy) [#36761](https://github.com/nodejs/node/pull/36761)
- [[`9b9b6d5fc5`](https://github.com/nodejs/node/commit/9b9b6d5fc5)] - **domain**: make node resilient to Array prototype tempering (Antoine du Hamel) [#36676](https://github.com/nodejs/node/pull/36676)
- [[`f0a9c53bec`](https://github.com/nodejs/node/commit/f0a9c53bec)] - **errors**: refactor to use more primordials (Antoine du Hamel) [#36651](https://github.com/nodejs/node/pull/36651)
- [[`c844d22b72`](https://github.com/nodejs/node/commit/c844d22b72)] - **errors**: eliminate all overhead for hidden calls (Momtchil Momtchev) [#35644](https://github.com/nodejs/node/pull/35644)
- [[`3fa470a3c9`](https://github.com/nodejs/node/commit/3fa470a3c9)] - **events**: refactor to use optional chaining (ZiJian Liu) [#36763](https://github.com/nodejs/node/pull/36763)
- [[`82393aefff`](https://github.com/nodejs/node/commit/82393aefff)] - **events**: refactor to use more primordials (Antoine du Hamel) [#36304](https://github.com/nodejs/node/pull/36304)
- [[`e3a091d9f3`](https://github.com/nodejs/node/commit/e3a091d9f3)] - **fs**: refactor to avoid unsafe array iteration (Antoine du Hamel) [#36699](https://github.com/nodejs/node/pull/36699)
- [[`d5e1b82125`](https://github.com/nodejs/node/commit/d5e1b82125)] - **fs**: accept non-32-bit length in writeBuffer (raisinten) [#36667](https://github.com/nodejs/node/pull/36667)
- [[`d858c9576a`](https://github.com/nodejs/node/commit/d858c9576a)] - **http**: remove dead code from internal/http.js (ZiJian Liu) [#36630](https://github.com/nodejs/node/pull/36630)
- [[`7e3ad1be32`](https://github.com/nodejs/node/commit/7e3ad1be32)] - **_Revert_** "**http**: remove dead code from internal/http.js" (ZiJian Liu) [#36890](https://github.com/nodejs/node/pull/36890)
- [[`a9a2dd32e3`](https://github.com/nodejs/node/commit/a9a2dd32e3)] - **http**: don't cork noop .end() (Robert Nagy) [#36633](https://github.com/nodejs/node/pull/36633)
- [[`dfc962f67a`](https://github.com/nodejs/node/commit/dfc962f67a)] - **http**: add test case for req-res close ordering (Daniele Belardi) [#36645](https://github.com/nodejs/node/pull/36645)
- [[`cc28d2f541`](https://github.com/nodejs/node/commit/cc28d2f541)] - **(SEMVER-MINOR)** **http**: set lifo as the default scheduling strategy in Agent (Matteo Collina) [#36685](https://github.com/nodejs/node/pull/36685)
- [[`954a36947d`](https://github.com/nodejs/node/commit/954a36947d)] - **http**: make HEAD method to work with keep-alive (Joseph Hackman) [#34231](https://github.com/nodejs/node/pull/34231)
- [[`9156f430b5`](https://github.com/nodejs/node/commit/9156f430b5)] - **http**: remove dead code from internal/http.js (ZiJian Liu) [#36630](https://github.com/nodejs/node/pull/36630)
- [[`5e499c490e`](https://github.com/nodejs/node/commit/5e499c490e)] - **http**: refactor to use more primordials (Antoine du Hamel) [#36194](https://github.com/nodejs/node/pull/36194)
- [[`c784f15588`](https://github.com/nodejs/node/commit/c784f15588)] - **_Revert_** "**http**: use `autoDestroy: true` in incoming message" (Daniele Belardi) [#36647](https://github.com/nodejs/node/pull/36647)
- [[`a38ad0709c`](https://github.com/nodejs/node/commit/a38ad0709c)] - **http2**: refactor to use primordials instead of \<string\>.indexOf (Rohan Chougule) [#36679](https://github.com/nodejs/node/pull/36679)
- [[`e85fbb778d`](https://github.com/nodejs/node/commit/e85fbb778d)] - **http2**: fix typos in core.js (Pranshu Jethmalani) [#36719](https://github.com/nodejs/node/pull/36719)
- [[`a4d64f967a`](https://github.com/nodejs/node/commit/a4d64f967a)] - **https**: refactor to use more primordials (Antoine du Hamel) [#36195](https://github.com/nodejs/node/pull/36195)
- [[`1db3772c95`](https://github.com/nodejs/node/commit/1db3772c95)] - **lib**: simplify `primordials.uncurryThis` (ExE Boss) [#36866](https://github.com/nodejs/node/pull/36866)
- [[`95219eac08`](https://github.com/nodejs/node/commit/95219eac08)] - **lib**: refactor to use mapping in cluster master (Yash Ladha) [#36250](https://github.com/nodejs/node/pull/36250)
- [[`b764269437`](https://github.com/nodejs/node/commit/b764269437)] - **lib**: remove v8_prof_polyfill from eslint ignore list (Antoine du Hamel) [#36537](https://github.com/nodejs/node/pull/36537)
- [[`eb6b38639a`](https://github.com/nodejs/node/commit/eb6b38639a)] - **lib**: remove unused code (Brian White) [#36632](https://github.com/nodejs/node/pull/36632)
- [[`7fe1b5ef5a`](https://github.com/nodejs/node/commit/7fe1b5ef5a)] - **lib**: refactor to use validateCallback (ZiJian Liu) [#36609](https://github.com/nodejs/node/pull/36609)
- [[`bb4f8c8732`](https://github.com/nodejs/node/commit/bb4f8c8732)] - **lib**: use more primordials in shared validators (Pooja D P) [#36552](https://github.com/nodejs/node/pull/36552)
- [[`181bad58d3`](https://github.com/nodejs/node/commit/181bad58d3)] - **lib**: add primordials.SafeArrayIterator (Antoine du Hamel) [#36532](https://github.com/nodejs/node/pull/36532)
- [[`6e338dac3c`](https://github.com/nodejs/node/commit/6e338dac3c)] - **lib**: refactor to use more primordials in internal/encoding.js (raisinten) [#36480](https://github.com/nodejs/node/pull/36480)
- [[`ec3e841f59`](https://github.com/nodejs/node/commit/ec3e841f59)] - **lib**: refactor to use primordials in internal/priority_queue.js (ZiJian Liu) [#36560](https://github.com/nodejs/node/pull/36560)
- [[`8ac2016229`](https://github.com/nodejs/node/commit/8ac2016229)] - **lib**: add primordials.SafeStringIterator (Antoine du Hamel) [#36526](https://github.com/nodejs/node/pull/36526)
- [[`56af1250fe`](https://github.com/nodejs/node/commit/56af1250fe)] - **lib**: make safe primordials safe to construct (Antoine du Hamel) [#36428](https://github.com/nodejs/node/pull/36428)
- [[`d20235b6cb`](https://github.com/nodejs/node/commit/d20235b6cb)] - **lib**: fix diagnostics_channel hasSubscribers error (ZiJian Liu) [#36599](https://github.com/nodejs/node/pull/36599)
- [[`63091f8440`](https://github.com/nodejs/node/commit/63091f8440)] - **lib**: refactor to use more primordials in internal/histogram.js (raisinten) [#36455](https://github.com/nodejs/node/pull/36455)
- [[`eca2df0909`](https://github.com/nodejs/node/commit/eca2df0909)] - **meta**: notify slack when someone force pushes (Mary Marchini) [#35131](https://github.com/nodejs/node/pull/35131)
- [[`01213c71b9`](https://github.com/nodejs/node/commit/01213c71b9)] - **module**: fix Windows folder exports deprecation warning (Guy Bedford) [#36859](https://github.com/nodejs/node/pull/36859)
- [[`302be57be4`](https://github.com/nodejs/node/commit/302be57be4)] - **module**: refactor to avoid unsafe array iteration (Antoine du Hamel) [#36680](https://github.com/nodejs/node/pull/36680)
- [[`24246a29d7`](https://github.com/nodejs/node/commit/24246a29d7)] - **net**: throw ERR_OUT_OF_RANGE if blockList.addSubnet prefix is NaN (ZiJian Liu) [#36732](https://github.com/nodejs/node/pull/36732)
- [[`02dbcc4317`](https://github.com/nodejs/node/commit/02dbcc4317)] - **(SEMVER-MINOR)** **net**: support abortSignal in server.listen (Nitzan Uziely) [#36623](https://github.com/nodejs/node/pull/36623)
- [[`a258bc9b70`](https://github.com/nodejs/node/commit/a258bc9b70)] - **perf_hooks**: refactor to avoid unsafe array iteration (Antoine du Hamel) [#36723](https://github.com/nodejs/node/pull/36723)
- [[`94afc3e712`](https://github.com/nodejs/node/commit/94afc3e712)] - **process**: passing -1 to setuid/setgid should not abort (James M Snell) [#36786](https://github.com/nodejs/node/pull/36786)
- [[`92af50327e`](https://github.com/nodejs/node/commit/92af50327e)] - **(SEMVER-MINOR)** **process**: add direct access to rss without iterating pages (Adrien Maret) [#34291](https://github.com/nodejs/node/pull/34291)
- [[`8b7336b072`](https://github.com/nodejs/node/commit/8b7336b072)] - **quic,timers**: refactor to use validateAbortSignal (ZiJian Liu) [#36604](https://github.com/nodejs/node/pull/36604)
- [[`b17130a55a`](https://github.com/nodejs/node/commit/b17130a55a)] - **readline**: fix behaviour of Interface plugged to a non-terminal output (Antoine du Hamel) [#36774](https://github.com/nodejs/node/pull/36774)
- [[`d70824f567`](https://github.com/nodejs/node/commit/d70824f567)] - **src**: fix typo in crypto_aes.cc (Ikko Ashimine) [#36717](https://github.com/nodejs/node/pull/36717)
- [[`8b43388903`](https://github.com/nodejs/node/commit/8b43388903)] - **src**: reduce duplicated boilerplate with new env utility fn (James M Snell) [#36536](https://github.com/nodejs/node/pull/36536)
- [[`a53997e6c0`](https://github.com/nodejs/node/commit/a53997e6c0)] - **src**: fix leading backslash bug in URL (raisinten) [#36613](https://github.com/nodejs/node/pull/36613)
- [[`abae61e230`](https://github.com/nodejs/node/commit/abae61e230)] - **stream**: finished waits for 'close' on OutgoingMessage (Robert Nagy) [#36648](https://github.com/nodejs/node/pull/36648)
- [[`4c819d65f9`](https://github.com/nodejs/node/commit/4c819d65f9)] - **stream**: fix .end() error propagation (Robert Nagy) [#36817](https://github.com/nodejs/node/pull/36817)
- [[`cb0b53edb1`](https://github.com/nodejs/node/commit/cb0b53edb1)] - **stream**: lazy read ReadStream (Momtchil Momtchev) [#36823](https://github.com/nodejs/node/pull/36823)
- [[`b996e3b4b5`](https://github.com/nodejs/node/commit/b996e3b4b5)] - **stream**: do not use \_stream\_\* anymore (Matteo Collina) [#36684](https://github.com/nodejs/node/pull/36684)
- [[`190ddced46`](https://github.com/nodejs/node/commit/190ddced46)] - **stream**: only use legacy close listeners if not willEmitClose (Robert Nagy) [#36649](https://github.com/nodejs/node/pull/36649)
- [[`1fc30a84ac`](https://github.com/nodejs/node/commit/1fc30a84ac)] - **stream,zlib**: do not use \_stream\_\* anymore (Matteo Collina) [#36618](https://github.com/nodejs/node/pull/36618)
- [[`d2b9e7cb01`](https://github.com/nodejs/node/commit/d2b9e7cb01)] - **string_decoder**: throw ERR_STRING_TOO_LONG for UTF-8 (Michaël Zasso) [#36661](https://github.com/nodejs/node/pull/36661)
- [[`abc2ff47c2`](https://github.com/nodejs/node/commit/abc2ff47c2)] - **test**: disable test-crypto-secure-heap with asan (James M Snell) [#36900](https://github.com/nodejs/node/pull/36900)
- [[`17a52337c4`](https://github.com/nodejs/node/commit/17a52337c4)] - **test**: http complete response after socket double end (Dimitris Halatsis) [#36633](https://github.com/nodejs/node/pull/36633)
- [[`cc37ff24dc`](https://github.com/nodejs/node/commit/cc37ff24dc)] - **test**: use faster variant for rss in test-crypto-dh-leak (Pooja D P) [#36766](https://github.com/nodejs/node/pull/36766)
- [[`daad0ab1cc`](https://github.com/nodejs/node/commit/daad0ab1cc)] - **test**: use faster variant for rss in test-vm-memleak.js (Pooja D P) [#36769](https://github.com/nodejs/node/pull/36769)
- [[`9d25d25cfd`](https://github.com/nodejs/node/commit/9d25d25cfd)] - **test**: mark test-cluster-bind-privileged-port flaky on arm (James M Snell) [#36850](https://github.com/nodejs/node/pull/36850)
- [[`c64db20fdd`](https://github.com/nodejs/node/commit/c64db20fdd)] - **test**: use faster variant for rss test-memoryusage-emfile (Pooja D P) [#36768](https://github.com/nodejs/node/pull/36768)
- [[`d48e00e5a3`](https://github.com/nodejs/node/commit/d48e00e5a3)] - **test**: fix test-memory-usage.js for IBMi (Rich Trott) [#36758](https://github.com/nodejs/node/pull/36758)
- [[`9b7d2c2523`](https://github.com/nodejs/node/commit/9b7d2c2523)] - **test**: guard large string decoder allocation (Michaël Zasso) [#36795](https://github.com/nodejs/node/pull/36795)
- [[`5bc130bd9e`](https://github.com/nodejs/node/commit/5bc130bd9e)] - **test**: increase coverage for events (ZiJian Liu) [#36668](https://github.com/nodejs/node/pull/36668)
- [[`9f7fbcc64d`](https://github.com/nodejs/node/commit/9f7fbcc64d)] - **test**: add coverage for breakLength one-column array (Rich Trott) [#36657](https://github.com/nodejs/node/pull/36657)
- [[`9eff709c23`](https://github.com/nodejs/node/commit/9eff709c23)] - **test**: update wpt interfaces (Daijiro Wachi) [#36659](https://github.com/nodejs/node/pull/36659)
- [[`a7f743f5cc`](https://github.com/nodejs/node/commit/a7f743f5cc)] - **test**: update wpt resources (Daijiro Wachi) [#36659](https://github.com/nodejs/node/pull/36659)
- [[`4acc2732f9`](https://github.com/nodejs/node/commit/4acc2732f9)] - **test**: update wpt encoding (Daijiro Wachi) [#36659](https://github.com/nodejs/node/pull/36659)
- [[`986d5aca44`](https://github.com/nodejs/node/commit/986d5aca44)] - **test**: update wpt url (Daijiro Wachi) [#36659](https://github.com/nodejs/node/pull/36659)
- [[`833e614682`](https://github.com/nodejs/node/commit/833e614682)] - **test**: increase coverage for diagnostics_channel (ZiJian Liu) [#36602](https://github.com/nodejs/node/pull/36602)
- [[`f0dfe57bd1`](https://github.com/nodejs/node/commit/f0dfe57bd1)] - **test**: add already-aborted-controller test for spawn() (Rich Trott) [#36644](https://github.com/nodejs/node/pull/36644)
- [[`d5d56ec3d4`](https://github.com/nodejs/node/commit/d5d56ec3d4)] - **test**: add test for reused AbortController with execfile() (Rich Trott) [#36644](https://github.com/nodejs/node/pull/36644)
- [[`f81556563a`](https://github.com/nodejs/node/commit/f81556563a)] - **test**: increase coverage for internal/error_serdes.js (ZiJian Liu) [#36628](https://github.com/nodejs/node/pull/36628)
- [[`34d1d791e5`](https://github.com/nodejs/node/commit/34d1d791e5)] - **test**: improve coverage for util.inspect() with classes (Rich Trott) [#36625](https://github.com/nodejs/node/pull/36625)
- [[`1f3bc5ed73`](https://github.com/nodejs/node/commit/1f3bc5ed73)] - **test**: increase runInAsyncScope() coverage (Rich Trott) [#36624](https://github.com/nodejs/node/pull/36624)
- [[`863bfc44d2`](https://github.com/nodejs/node/commit/863bfc44d2)] - **test**: redirect stderr EnvironmentWithNoESMLoader (Daniel Bevenius) [#36548](https://github.com/nodejs/node/pull/36548)
- [[`8e8b16ff7e`](https://github.com/nodejs/node/commit/8e8b16ff7e)] - **timers**: refactor to use optional chaining (ZiJian Liu) [#36767](https://github.com/nodejs/node/pull/36767)
- [[`c23cca2de9`](https://github.com/nodejs/node/commit/c23cca2de9)] - **tls**: refactor to avoid unsafe array iteration (Antoine du Hamel) [#36772](https://github.com/nodejs/node/pull/36772)
- [[`37becfda8c`](https://github.com/nodejs/node/commit/37becfda8c)] - **tools**: update all lint-md rollup dependencies (Michaël Zasso) [#36843](https://github.com/nodejs/node/pull/36843)
- [[`cfdbb79ccf`](https://github.com/nodejs/node/commit/cfdbb79ccf)] - **tools**: update doc tool dependencies (Michaël Zasso) [#36844](https://github.com/nodejs/node/pull/36844)
- [[`1f2a198c32`](https://github.com/nodejs/node/commit/1f2a198c32)] - **tools**: fix md5 hash for ICU 68.1 src (Richard Lau) [#36777](https://github.com/nodejs/node/pull/36777)
- [[`4e0995bc60`](https://github.com/nodejs/node/commit/4e0995bc60)] - **tools**: update ESLint to 7.17.0 (Colin Ihrig) [#36726](https://github.com/nodejs/node/pull/36726)
- [[`8ad3455ae3`](https://github.com/nodejs/node/commit/8ad3455ae3)] - **tools**: revise install.py for minor improvements (Rich Trott) [#36626](https://github.com/nodejs/node/pull/36626)
- [[`b367d5a61d`](https://github.com/nodejs/node/commit/b367d5a61d)] - **tools**: update gyp-next to v0.7.0 (Michaël Zasso) [#36580](https://github.com/nodejs/node/pull/36580)
- [[`10f1c893c8`](https://github.com/nodejs/node/commit/10f1c893c8)] - **tools**: correct usage message for genv8constants.py (Rich Trott) [#36606](https://github.com/nodejs/node/pull/36606)
- [[`37b39a2d6b`](https://github.com/nodejs/node/commit/37b39a2d6b)] - **tools**: call close() explicitly in genv8constants.py (Rich Trott) [#36606](https://github.com/nodejs/node/pull/36606)
- [[`7664f3678c`](https://github.com/nodejs/node/commit/7664f3678c)] - **tools**: use `is None` consistently in Python (Rich Trott) [#36606](https://github.com/nodejs/node/pull/36606)
- [[`cb7f73c9d4`](https://github.com/nodejs/node/commit/cb7f73c9d4)] - **tools**: revise line in configure.py for clarity (Rich Trott) [#36551](https://github.com/nodejs/node/pull/36551)
- [[`258aa50986`](https://github.com/nodejs/node/commit/258aa50986)] - **tty**: refactor to avoid unsafe array iteration (Antoine du Hamel) [#36771](https://github.com/nodejs/node/pull/36771)
- [[`5cb8b16452`](https://github.com/nodejs/node/commit/5cb8b16452)] - **url**: fix url.format with ipv6 hostname (ZiJian Liu) [#36665](https://github.com/nodejs/node/pull/36665)
- [[`b1c6a44caf`](https://github.com/nodejs/node/commit/b1c6a44caf)] - **url**: refactor to use more primordials (Antoine du Hamel) [#36316](https://github.com/nodejs/node/pull/36316)
- [[`baa8064bd0`](https://github.com/nodejs/node/commit/baa8064bd0)] - **util**: refactor inspect.js to use more primodials (Rohan Chougule) [#36730](https://github.com/nodejs/node/pull/36730)
- [[`bff201a66d`](https://github.com/nodejs/node/commit/bff201a66d)] - **util**: remove unreachable defensive coding (Rich Trott) [#36744](https://github.com/nodejs/node/pull/36744)
- [[`64bf2f229e`](https://github.com/nodejs/node/commit/64bf2f229e)] - **util**: refactor to use more primordials (Antoine du Hamel) [#36265](https://github.com/nodejs/node/pull/36265)
- [[`2dd2ec3836`](https://github.com/nodejs/node/commit/2dd2ec3836)] - **v8**: refactor to use more primordials (Antoine du Hamel) [#36527](https://github.com/nodejs/node/pull/36527)
- [[`3170636a8e`](https://github.com/nodejs/node/commit/3170636a8e)] - **(SEMVER-MINOR)** **v8**: fix native `serdes` constructors (ExE Boss) [#36549](https://github.com/nodejs/node/pull/36549)
- [[`d5a9799e76`](https://github.com/nodejs/node/commit/d5a9799e76)] - **wasi**: refactor to avoid unsafe array iteration (Antoine du Hamel) [#36724](https://github.com/nodejs/node/pull/36724)
- [[`b6f74b0b09`](https://github.com/nodejs/node/commit/b6f74b0b09)] - **zlib**: refactor to use primordial instead of \<string\>.startsWith (Rohan Chougule) [#36718](https://github.com/nodejs/node/pull/36718)

Windows 32-bit Installer: https://nodejs.org/dist/v15.6.0/node-v15.6.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v15.6.0/node-v15.6.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v15.6.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v15.6.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v15.6.0/node-v15.6.0.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v15.6.0/node-v15.6.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v15.6.0/node-v15.6.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v15.6.0/node-v15.6.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v15.6.0/node-v15.6.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v15.6.0/node-v15.6.0-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v15.6.0/node-v15.6.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v15.6.0/node-v15.6.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v15.6.0/node-v15.6.0.tar.gz \
Other release files: https://nodejs.org/dist/v15.6.0/ \
Documentation: https://nodejs.org/docs/v15.6.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

166985455aaf6a16c570045c3381bc7d88a1dfa3f98cf4bb67a699b7240c6bed  node-v15.6.0-aix-ppc64.tar.gz
e79baddd344fd19c68104591964263b14ddc57fb98051bebb744151f21a83667  node-v15.6.0-darwin-x64.tar.gz
489f1072dbded246c8f3727844409ee7d835a46479f354e39b080ad5a8416cc0  node-v15.6.0-darwin-x64.tar.xz
73687e7b2afa53dc35bc331534dcdca25137a5b1a543cd7c8c35b74ef53f4ddf  node-v15.6.0-headers.tar.gz
4883ecad8ef4d6c3028252b2dae014e2a9f5a09b44cdf2609b497be8839a1ccc  node-v15.6.0-headers.tar.xz
b0660398fe590f8588431a787e9b032c7271a2fa88306c7a26e751571df998e4  node-v15.6.0-linux-arm64.tar.gz
533a5cbda0dc4f42f0cd2967db3f40a5186e64806ee981b791e7c37bb2780fc3  node-v15.6.0-linux-arm64.tar.xz
234871415c54174f91764f332a72631519a6af7b1a87797ad7c729855182f9cd  node-v15.6.0-linux-armv7l.tar.gz
76f9e0c89da946a834a0711855b75c84e64c5440cf78f4b81b7d27e2791b1710  node-v15.6.0-linux-armv7l.tar.xz
7c45fa66e8c5f3c8d6bf82785717776cb3b2bc4e78c2d5846123dfcded06bba3  node-v15.6.0-linux-ppc64le.tar.gz
f6dd89aad601c3cfdaa75f3673b03a37314b2b6cf50b51e61c039d0c8c1ddd64  node-v15.6.0-linux-ppc64le.tar.xz
f9c28fec6a4215ca65cb42c3a317e13e4b81f88609a49507d810d52334f85ffe  node-v15.6.0-linux-s390x.tar.gz
94a350a4cbd1b46789b9425610e25dffd560b78d4e711708df06565b361df3fa  node-v15.6.0-linux-s390x.tar.xz
a8b42f6f174f857b9369858b63ff136ed5b9072336e6df9f0208eddde13897dc  node-v15.6.0-linux-x64.tar.gz
e8ae7a715c1886c1ca4ec401c401bc832c8fac586c7aa0e7cc1db9bda4e785a8  node-v15.6.0-linux-x64.tar.xz
948ff411a76eb7adc317cd18c6fe75e8d10009937771786ac25b68364feadb04  node-v15.6.0.pkg
1dd3681e85bb9c8205a331bfac5121050893defb5ad9d04698239aeef4e736b3  node-v15.6.0.tar.gz
39df81ddcd9ca0b99b179aeb61d1b01257a1579e257764a93b0386ff029c38d2  node-v15.6.0.tar.xz
e913e4e4597374da71e649c94b7273a834c365cb996bde1755ed18c45764147c  node-v15.6.0-win-x64.7z
83d1aafbe869c8e7064b8ad97894ce8428d8d66b8fa38f6d5364dc3e9ffb3813  node-v15.6.0-win-x64.zip
ac25fc5e7e195f2c743d0d2d7fef73d24d9ee02df867049d51f8ab6a92d749d1  node-v15.6.0-win-x86.7z
f0d5efd49afe4e418edbb415f9d9df11bf052d4eae0915a8a808b8506415c64a  node-v15.6.0-win-x86.zip
3936fc5c9e7b8e5a9824ea2f1dfefad7a4b0bc94db94ef9efeb5b38cb24691d5  node-v15.6.0-x64.msi
ecb4c5a259b161504736193ffb6121c76cf71d77e4151432104ad79c3540c900  node-v15.6.0-x86.msi
1ba33091e6edf2636e1f5088d8753ef765217cdab2233fd82874a87d968e18f8  win-x64/node.exe
c0f8428d8179b9d99ddc564f722ad7022075e72823305edbe48ec5366fad40b2  win-x64/node.lib
9e3fd87f4c19a84cc1f3dc7260bbcf12749fd561f771e7596d87dfde15f8c6b4  win-x64/node_pdb.7z
8aa9f8db544b49ab6abb3c6fda822c703f17d4a3ac51f87d35ad2e9cb395ab8a  win-x64/node_pdb.zip
acea7cb81f2078d74fa506bd32d8ffbea0ebc80abd4e7fbbd0199526098456b3  win-x86/node.exe
f2144ca5323061754edfdf630af6c3495b0a87d23303c18438527dfa1356d730  win-x86/node.lib
c6acd5a72660389899aedd935ba6fb4dbf246bcfd4ae9a5ef2cd28c3544fbb5a  win-x86/node_pdb.7z
4bc9524beb622c589c53823eef47daf6f98d73cf784f6ec73a581f577645c7fa  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEdPEmArbxxOkT+qN606iWE2Q7YgEFAmAA6XIACgkQ06iWE2Q7
YgEhXg//Yo/MLY9+sVtbNn3yiZa/KXI/MYKMqO6ZdyAYiVX9/XF5C2/cCN0H2BbB
i4tSMx2gO7zjuEJ+mMu0m3Mjf6sIzgs9RhhGJfTW5gyLHfZtcMy+ozZmEzTE23oQ
HMlr2jbyd9UUhfcMv4wAJF+tFgQ9nys1nEng/j8hvdf8Jj1wSbzFh280nDa34h97
YAOlIS83neX9rQWpcPMf1KKYyo4WxFr3nJEDCdd3rB0VandfNVg2F79kQDYgJrLT
I3nK/3kSYkW+m3p8JwLfWFjhx6A/9nHyH0x2JuF/Or0lBGs6sCs8GtPw++dNr9NZ
p62u6anbVz3J+R7W68ETdrcBvFifmsEhOpYMpGTGYVAEgEGbReqlFj/Dn5f3t+Y0
8H+Ua3q8ISitWZmtvJKWoUscbPJc3G+PZKwqE7lDIRlCuj+csttlMkZFJoLiAEiO
eD6whkmcuDznSbFOmmfvwTf6lI7gj5NqXBczlsw6DDWzO9SHTesDU90sX9ooeWr/
YHImDZD1kE21FD2xuULah6aHrtGvtj0p9cB+JtHWzGdszdKsroMun2VI2uCGzfSH
I8xDU9Kp3RJV63bVpEulJM4TIkuITeBgWAmhkgv4xNdzXaRM3Gei4XA0cwGQgqQj
Qa3WI2loQqEc/Xn/m1GrcvJqfQ7xzFKNdf8IShkrYt6QvUNR+wI=
=t9Po
-----END PGP SIGNATURE-----

```
