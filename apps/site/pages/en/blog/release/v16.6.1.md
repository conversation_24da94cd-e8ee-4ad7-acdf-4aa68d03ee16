---
date: '2021-08-03T08:07:46.962Z'
category: release
title: Node v16.6.1 (Current)
layout: blog-post
author: <PERSON><PERSON><PERSON>
---

### Notable Changes

- Updated npm to 7.20.3 (npm team) [#39579](https://github.com/nodejs/node/pull/39579)
- Reverted an ABI-breaking change from V8 9.2 that could impact some native modules (<PERSON><PERSON><PERSON>) [#39624](https://github.com/nodejs/node/pull/39624)
- Fixed a bug in error handling known to affect at least Webpack and Jest (<PERSON>) [#39593](https://github.com/nodejs/node/pull/39593)

### Commits

- [[`6c769ccedf`](https://github.com/nodejs/node/commit/6c769ccedf)] - **build**: override python executable path on configure (legendecas) [#39465](https://github.com/nodejs/node/pull/39465)
- [[`cbf6a01c17`](https://github.com/nodejs/node/commit/cbf6a01c17)] - **crypto**: fix `generateKeyPair` with encoding 'jwk' (himself65) [#39319](https://github.com/nodejs/node/pull/39319)
- [[`3091295609`](https://github.com/nodejs/node/commit/3091295609)] - **deps**: revert ABI-breaking change from V8 9.2 (Michaël Zasso) [#39624](https://github.com/nodejs/node/pull/39624)
- [[`06d7b8e8c8`](https://github.com/nodejs/node/commit/06d7b8e8c8)] - **deps**: upgrade npm to 7.20.3 (npm team) [#39579](https://github.com/nodejs/node/pull/39579)
- [[`7b612fadc2`](https://github.com/nodejs/node/commit/7b612fadc2)] - **doc**: fix crypto.hkdf callback derivedKey type (Filip Skokan) [#39453](https://github.com/nodejs/node/pull/39453)
- [[`7a731efd97`](https://github.com/nodejs/node/commit/7a731efd97)] - **doc,lib,test**: rename HKDF 'key' argument (Tobias Nießen) [#39474](https://github.com/nodejs/node/pull/39474)
- [[`93bbaa0ce9`](https://github.com/nodejs/node/commit/93bbaa0ce9)] - **module**: fix ERR_REQUIRE_ESM error for null frames (Guy Bedford) [#39593](https://github.com/nodejs/node/pull/39593)
- [[`e13162de09`](https://github.com/nodejs/node/commit/e13162de09)] - **module**: refine `enrichCJSError` (Antoine du Hamel) [#39507](https://github.com/nodejs/node/pull/39507)
- [[`815fbec6f1`](https://github.com/nodejs/node/commit/815fbec6f1)] - **repl**: do not include legacy getter/setter methods in completion (Anna Henningsen) [#39576](https://github.com/nodejs/node/pull/39576)
- [[`0405c8d3f0`](https://github.com/nodejs/node/commit/0405c8d3f0)] - **zlib**: avoid converting `Uint8Array` instances to `Buffer` (Antoine du Hamel) [#39492](https://github.com/nodejs/node/pull/39492)

Windows 32-bit Installer: https://nodejs.org/dist/v16.6.1/node-v16.6.1-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v16.6.1/node-v16.6.1-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v16.6.1/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v16.6.1/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v16.6.1/node-v16.6.1.pkg \
macOS Apple Silicon 64-bit Binary: https://nodejs.org/dist/v16.6.1/node-v16.6.1-darwin-arm64.tar.gz \
macOS Intel 64-bit Binary: https://nodejs.org/dist/v16.6.1/node-v16.6.1-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v16.6.1/node-v16.6.1-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v16.6.1/node-v16.6.1-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v16.6.1/node-v16.6.1-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v16.6.1/node-v16.6.1-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v16.6.1/node-v16.6.1-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v16.6.1/node-v16.6.1-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v16.6.1/node-v16.6.1.tar.gz \
Other release files: https://nodejs.org/dist/v16.6.1/ \
Documentation: https://nodejs.org/docs/v16.6.1/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

02581dbbfc2886dec326c3fca70c0f17e82491563057889dc630e5c055754c02  node-v16.6.1-aix-ppc64.tar.gz
8b766a2bcc686f968146b09892f24cfbeaebb547a4d50744d9af80def5221613  node-v16.6.1-darwin-arm64.tar.gz
3565e5c86067325db710490a6d2a41d1044e944d9346046e813543ec92ee7a4e  node-v16.6.1-darwin-arm64.tar.xz
bca84deb7bf6c57537b3af44997d985045c95b5048fc5665cdc7f54d5c147516  node-v16.6.1-darwin-x64.tar.gz
711fea396b0a1c564b519c909be3afc938f75ad95d3ea9125e2187eb7e3ce1bf  node-v16.6.1-darwin-x64.tar.xz
a87003ca6f43a23d611637b4a39b5d255f1a0ed5ad9423134fc9ed61a6fca364  node-v16.6.1-headers.tar.gz
3c9bfa2d4594ff8f4baee01652ceb2a05c5d5e44100afd94a7ee6b347721e1e0  node-v16.6.1-headers.tar.xz
ecab5720035b6bb97564b05df527d49a37489dcba6a244c0f1c7c801bb2755f7  node-v16.6.1-linux-arm64.tar.gz
59867dccc1f392416e9301a94b9df19faa38d0b0d1d2f1cea174835dff1a1cb0  node-v16.6.1-linux-arm64.tar.xz
04268fda50beb3aa116e150adcd3175cde17166b1f40079765da281fa73cc6cb  node-v16.6.1-linux-armv7l.tar.gz
903e30b38f67bb128d2e1ff520fc2fdabede1029338010b1304c9c081236859e  node-v16.6.1-linux-armv7l.tar.xz
9830281e2d553590c4a53d5d8327a2887df5b65452f5fa55ccd9ae597fcc247b  node-v16.6.1-linux-ppc64le.tar.gz
9392e583f24d41c1e5f5625171d963fa01e924b3b38cdd7632f249e8e491e958  node-v16.6.1-linux-ppc64le.tar.xz
fec6745555b477fbb62440b0306c8b4717980778bf8f0d5fd497915508775bd0  node-v16.6.1-linux-s390x.tar.gz
878cb8dfe48312fe40ab9fc320395e8322e8d9e1db4821539dcc6e2b006d8616  node-v16.6.1-linux-s390x.tar.xz
e7e4149626ccd0653783ee8aef81eb50fa7ada2f9f7cbc031969b3b1ac3ffa6b  node-v16.6.1-linux-x64.tar.gz
ff95e86c3161859251cf659a76be63d99fc45e2380addf634812e5afebac961a  node-v16.6.1-linux-x64.tar.xz
1ab44e48c45c9fdc8a91b7339f610f7e0d87801640769c041d8649d00157d79f  node-v16.6.1.pkg
36467b8a4e7e3bacc2f4f1709a83b0506429d1999bc461e5e363bc91d3437c09  node-v16.6.1.tar.gz
79b1ea058cc67f2a69462cd5f2467a1efe08c64299c053da70384ce1a0e3e557  node-v16.6.1.tar.xz
5b85e20f14c0c9a9c4ac6f867f8bcf47a09c645930707f7b766322065e719ed3  node-v16.6.1-win-x64.7z
ec5dce1e182172cc7edc8d56c377f4d106232b2b14127bd2a1565497448504e9  node-v16.6.1-win-x64.zip
a798038fcae1c02e831d42e5d7ca76b643905fd9716bf11a4357cfa903148fea  node-v16.6.1-win-x86.7z
395ce9ac6c7204ccac8f16dfa158306ec42cf1a1ffc29f40076dcdafe78bd328  node-v16.6.1-win-x86.zip
b3f74a4b6c8d31bb33b08228c6797b1f6d10b0bcf096f2d7287bcc225dfb237d  node-v16.6.1-x64.msi
7a5a849bc890aee91a2ff0285fe90e6ddc793b4f265792ce70b50595f6e0543b  node-v16.6.1-x86.msi
cb88167e067634cc8053a5204364fbb3fcabe5f6b3b1a67b38f67c21ad72acb3  win-x64/node.exe
e7484e4552df7e992e5d409e518aa467555769dc85917da408d41f85c4f2823d  win-x64/node.lib
0eae256767c33fb44eab2c02972d1d835bcf6e6a430b6698e9120aeb4c49124f  win-x64/node_pdb.7z
a40ddd793650ff3801aa149523ce98a9a4c66bbf9fc69d0cea0c9eaaba635d28  win-x64/node_pdb.zip
c0bb2eb9118c32a2ec9c3dffc7155adbb29962d5ec88d337e30488e6e983993c  win-x86/node.exe
7821ab6af3cef68eee042ef6b7fb2185eafff8bd4e4c988c8fc6c1d0242d2bcd  win-x86/node.lib
b83ed322a47a81b56200e744457e286a07d7c48e15b8a71a63a9c0fc73276ce5  win-x86/node_pdb.7z
ea5cfb3d53b8614d5f4941ffac6f8277e6998cc2f85f3a26e0838db533b21c61  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEj8yhP+8dDC6RAI4Jdw96mlrhVgAFAmEI+QAACgkQdw96mlrh
VgBSxw/+NGa6SVZ7iXM6FWFVq+nfuiCy6bbUov6sbTnk9h1XXN4nHdPfVoJU50Jh
Act+AVa2DOrf60JXl8YhATxkx5H3x4ksB9u6lVByhwdGFXnhw6tgtMNKPoHvakEb
hOdXWLCi6KIQpfhR7gpuxvUTjpn3fHJxMfb/YXnJHX0DEct+7XnwAjpuY3XtldUr
sfDTBtQQVH/7CpevnTHjsARoWnRC8EZ2kZ2QD8DpdQPTkfcwQElashnuHLeFCYSV
oPQtFn0moKDvfMl7+AHARBSYPtloJ1ySfaYZk/0DA+cDVmAhlbmmIem7E0vyn3lf
MWdEiEegDOZk8kSymQIg4jpeJxLkAhipbvOsKhFuIyBIk5nPMlZE5kWa/VsQUZfc
ruhQB8uDyHJB2QEixfwBl8UV4Zbe4uaawVgeykAo6qBkONGv5NdKUWO7wHVOYtgT
dAK3ggfAR2BWF75Qn4f44PBHOOiZDwoBolAGN5Xa5x/TwOL7hmAAGFNU4m3yy/8Q
60lKs9HrW1t+FJT/vLR/sbMB1hvdTWvaK06knrCpZ+n4jjxwWK6oYkVjZD94TSlz
aBbj+OOcO2MG9nGzqVDseAK8pwpHo9okiHd7xbuSNXRVsh28cOVEWVYpRkRWPu1f
/YUnd9apXKduE74AcYx7xMPLJinHwwGhd1BPeP/WrF3bQ3/58EQ=
=MTC/
-----END PGP SIGNATURE-----

```
