---
date: '2019-04-23T17:01:07.251Z'
category: release
title: Node v12.0.0 (Current)
layout: blog-post
author: <PERSON>
---

### Notable Changes

- **assert**:
  - validate required arguments (<PERSON><PERSON>) [#26641](https://github.com/nodejs/node/pull/26641)
  - adjust loose assertions (<PERSON><PERSON>) [#25008](https://github.com/nodejs/node/pull/25008)
- **async_hooks**:
  - remove deprecated `emitBefore` and `emitAfter` (<PERSON>) [#26530](https://github.com/nodejs/node/pull/26530)
  - remove promise object from resource (<PERSON>) [#23443](https://github.com/nodejs/node/pull/23443)
- **bootstrap**: make Buffer and process non-enumerable (<PERSON><PERSON>) [#24874](https://github.com/nodejs/node/pull/24874)
- **buffer**:
  - use stricter range checks (<PERSON><PERSON>) [#27045](https://github.com/nodejs/node/pull/27045)
  - harden `SlowBuffer` creation (ZYSzys) [#26272](https://github.com/nodejs/node/pull/26272)
  - harden validation of buffer allocation size (ZYSzys) [#26162](https://github.com/nodejs/node/pull/26162)
  - do proper error propagation in addon methods (Anna Henningsen) [#23939](https://github.com/nodejs/node/pull/23939)
- **child_process**:
  - remove `options.customFds` (cjihrig) [#25279](https://github.com/nodejs/node/pull/25279)
  - harden fork arguments validation (ZYSzys) [#27039](https://github.com/nodejs/node/pull/27039)
  - use non-infinite `maxBuffer` defaults (kohta ito) [#23027](https://github.com/nodejs/node/pull/23027)
- **console**: don't use ANSI escape codes when `TERM=dumb` (Vladislav Kaminsky) [#26261](https://github.com/nodejs/node/pull/26261)
- **crypto**:
  - remove legacy native handles (Tobias Nießen) [#27011](https://github.com/nodejs/node/pull/27011)
  - decode missing passphrase errors (Tobias Nießen) [#25208](https://github.com/nodejs/node/pull/25208)
  - remove `Cipher.setAuthTag()` and `Decipher.getAuthTag()` (Tobias Nießen) [#26249](https://github.com/nodejs/node/pull/26249)
  - remove deprecated `crypto._toBuf()` (Tobias Nießen) [#25338](https://github.com/nodejs/node/pull/25338)
  - set `DEFAULT_ENCODING` property to non-enumerable (Antoine du Hamel) [#23222](https://github.com/nodejs/node/pull/23222)
- **deps**:
  - update V8 to 7.4.288.13 (Michaël Zasso, cjihrig, Refael Ackermann, Anna Henningsen, Ujjwal Sharma) [#26685](https://github.com/nodejs/node/pull/26685)
  - bump minimum icu version to 63 (Ujjwal Sharma) [#25852](https://github.com/nodejs/node/pull/25852)
  - update OpenSSL to 1.1.1b (Sam Roberts, Shigeki Ohtsu) [#26327](https://github.com/nodejs/node/pull/26327)
- **errors**: update error name (Ruben Bridgewater) [#26738](https://github.com/nodejs/node/pull/26738)
- **fs**:
  - use proper .destroy() implementation for SyncWriteStream (Matteo Collina) [#26690](https://github.com/nodejs/node/pull/26690)
  - improve mode validation (Ruben Bridgewater) [#26575](https://github.com/nodejs/node/pull/26575)
  - harden validation of start option in `createWriteStream()` (ZYSzys) [#25579](https://github.com/nodejs/node/pull/25579)
  - make writeFile consistent with readFile wrt fd (Sakthipriyan Vairamani (thefourtheye)) [#23709](https://github.com/nodejs/node/pull/23709)
- **http**:
  - validate timeout in `ClientRequest()` (cjihrig) [#26214](https://github.com/nodejs/node/pull/26214)
  - return HTTP 431 on `HPE_HEADER_OVERFLOW` error (Albert Still) [#25605](https://github.com/nodejs/node/pull/25605)
  - switch default parser to llhttp (Anna Henningsen) [#24870](https://github.com/nodejs/node/pull/24870)
  - Runtime-deprecate `outgoingMessage._headers` and `outgoingMessage._headerNames` (Morgan Roderick) [#24167](https://github.com/nodejs/node/pull/24167)
- **lib**:
  - remove `Atomics.wake()` (Gus Caplan) [#27033](https://github.com/nodejs/node/pull/27033)
  - move DTRACE\_\* probes out of global scope (James M Snell) [#26541](https://github.com/nodejs/node/pull/26541)
  - deprecate `_stream_wrap` (Sam Roberts) [#26245](https://github.com/nodejs/node/pull/26245)
  - use ES6 class inheritance style (Ruben Bridgewater) [#24755](https://github.com/nodejs/node/pull/24755)
- **module**:
  - remove unintended access to deps/ (Anna Henningsen) [#25138](https://github.com/nodejs/node/pull/25138)
  - improve error message for MODULE_NOT_FOUND (Ali Ijaz Sheikh) [#25690](https://github.com/nodejs/node/pull/25690)
  - requireStack property for MODULE_NOT_FOUND (Ali Ijaz Sheikh) [#25690](https://github.com/nodejs/node/pull/25690)
  - remove dead code (Ruben Bridgewater) [#26983](https://github.com/nodejs/node/pull/26983)
  - make `require('.')` never resolve outside the current directory (Ruben Bridgewater) [#26973](https://github.com/nodejs/node/pull/26973)
  - throw an error for invalid package.json main entries (Ruben Bridgewater) [#26823](https://github.com/nodejs/node/pull/26823)
  - don't search in `require.resolve.paths` (cjihrig) [#23683](https://github.com/nodejs/node/pull/23683)
- **net**:
  - remove `Server.listenFD()` (cjihrig) [#27127](https://github.com/nodejs/node/pull/27127)
  - do not add `.host` and `.port` properties to DNS error (Ruben Bridgewater) [#26751](https://github.com/nodejs/node/pull/26751)
  - emit "write after end" errors in the next tick (Ouyang Yadong) [#24457](https://github.com/nodejs/node/pull/24457)
  - deprecate `_setSimultaneousAccepts()` undocumented function (James M Snell) [#23760](https://github.com/nodejs/node/pull/23760)
- **os**:
  - implement `os.type()` using `uv_os_uname()` (cjihrig) [#25659](https://github.com/nodejs/node/pull/25659)
  - remove `os.getNetworkInterfaces()` (cjihrig) [#25280](https://github.com/nodejs/node/pull/25280)
- **process**:
  - make global.process, global.Buffer getters (Guy Bedford) [#26882](https://github.com/nodejs/node/pull/26882)
  - move DEP0062 (node --debug) to end-of-life (Joyee Cheung) [#25828](https://github.com/nodejs/node/pull/25828)
  - exit on --debug and --debug-brk after option parsing (Joyee Cheung) [#25828](https://github.com/nodejs/node/pull/25828)
  - improve `--redirect-warnings` handling (Ruben Bridgewater) [#24965](https://github.com/nodejs/node/pull/24965)
- **readline**: support TERM=dumb (Vladislav Kaminsky) [#26261](https://github.com/nodejs/node/pull/26261)
- **repl**:
  - add welcome message (gengjiawen) [#25947](https://github.com/nodejs/node/pull/25947)
  - fix terminal default setting (Ruben Bridgewater) [#26518](https://github.com/nodejs/node/pull/26518)
  - check colors with `.getColorDepth()` (Vladislav Kaminsky) [#26261](https://github.com/nodejs/node/pull/26261)
  - deprecate REPLServer.rli (Ruben Bridgewater) [#26260](https://github.com/nodejs/node/pull/26260)
- **src**:
  - remove unused `INT_MAX` constant (Sam Roberts) [#27078](https://github.com/nodejs/node/pull/27078)
  - update `NODE_MODULE_VERSION` to 72 (Ujjwal Sharma) [#26685](https://github.com/nodejs/node/pull/26685)
  - remove `AddPromiseHook()` (Anna Henningsen) [#26574](https://github.com/nodejs/node/pull/26574)
  - clean up `MultiIsolatePlatform` interface (Anna Henningsen) [#26384](https://github.com/nodejs/node/pull/26384)
  - properly configure default heap limits (Ali Ijaz Sheikh) [#25576](https://github.com/nodejs/node/pull/25576)
  - remove `icuDataDir` from node config (GauthamBanasandra) [#24780](https://github.com/nodejs/node/pull/24780)
- **tls**:
  - support TLSv1.3 (Sam Roberts) [#26209](https://github.com/nodejs/node/pull/26209)
  - return correct version from `getCipher()` (Sam Roberts) [#26625](https://github.com/nodejs/node/pull/26625)
  - check arg types of renegotiate() (Sam Roberts) [#25876](https://github.com/nodejs/node/pull/25876)
  - add code for `ERR_TLS_INVALID_PROTOCOL_METHOD` (Sam Roberts) [#24729](https://github.com/nodejs/node/pull/24729)
  - emit a warning when servername is an IP address (Rodger Combs) [#23329](https://github.com/nodejs/node/pull/23329)
  - disable TLS v1.0 and v1.1 by default (Ben Noordhuis) [#23814](https://github.com/nodejs/node/pull/23814)
  - remove unused arg to createSecureContext() (Sam Roberts) [#24241](https://github.com/nodejs/node/pull/24241)
  - deprecate `Server.prototype.setOptions()` (cjihrig) [#23820](https://github.com/nodejs/node/pull/23820)
  - load `NODE_EXTRA_CA_CERTS` at startup (Ouyang Yadong) [#23354](https://github.com/nodejs/node/pull/23354)
- **util**:
  - remove `util.print()`, `util.puts()`, `util.debug()` and `util.error()` (cjihrig) [#25377](https://github.com/nodejs/node/pull/25377)
  - change inspect compact and breakLength default (Ruben Bridgewater) [#27109](https://github.com/nodejs/node/pull/27109)
  - improve inspect edge cases (Ruben Bridgewater) [#27109](https://github.com/nodejs/node/pull/27109)
  - only the first line of the error message (Simon Zünd) [#26685](https://github.com/nodejs/node/pull/26685)
  - don't set the prototype of callbackified functions (Ruben Bridgewater) [#26893](https://github.com/nodejs/node/pull/26893)
  - rename callbackified function (Ruben Bridgewater) [#26893](https://github.com/nodejs/node/pull/26893)
  - increase function length when using `callbackify()` (Ruben Bridgewater) [#26893](https://github.com/nodejs/node/pull/26893)
  - prevent tampering with internals in `inspect()` (Ruben Bridgewater) [#26577](https://github.com/nodejs/node/pull/26577)
  - prevent Proxy traps being triggered by `.inspect()` (Ruben Bridgewater) [#26241](https://github.com/nodejs/node/pull/26241)
  - prevent leaking internal properties (Ruben Bridgewater) [#24971](https://github.com/nodejs/node/pull/24971)
  - protect against monkeypatched Object prototype for inspect() (Rich Trott) [#25953](https://github.com/nodejs/node/pull/25953)
  - treat format arguments equally (Roman Reiss) [#23162](https://github.com/nodejs/node/pull/23162)
- **win, fs**: detect if symlink target is a directory (Bartosz Sosnowski) [#23724](https://github.com/nodejs/node/pull/23724)
- **zlib**:
  - throw TypeError if callback is missing (Anna Henningsen) [#24929](https://github.com/nodejs/node/pull/24929)
  - make “bare” constants un-enumerable (Anna Henningsen) [#24824](https://github.com/nodejs/node/pull/24824)

### Semver-Major Commits

- [[`afce912193`](https://github.com/nodejs/node/commit/afce912193)] - **(SEMVER-MAJOR)** **assert**: improve performance to instantiate errors (Ruben Bridgewater) [#26738](https://github.com/nodejs/node/pull/26738)
- [[`5a3623af74`](https://github.com/nodejs/node/commit/5a3623af74)] - **(SEMVER-MAJOR)** **assert**: validate required arguments (Ruben Bridgewater) [#26641](https://github.com/nodejs/node/pull/26641)
- [[`7493db21b6`](https://github.com/nodejs/node/commit/7493db21b6)] - **(SEMVER-MAJOR)** **assert**: adjust loose assertions (Ruben Bridgewater) [#25008](https://github.com/nodejs/node/pull/25008)
- [[`9d064439e5`](https://github.com/nodejs/node/commit/9d064439e5)] - **(SEMVER-MAJOR)** **async_hooks**: remove deprecated emitBefore and emitAfter (Matteo Collina) [#26530](https://github.com/nodejs/node/pull/26530)
- [[`1a2cf6696f`](https://github.com/nodejs/node/commit/1a2cf6696f)] - **(SEMVER-MAJOR)** **async_hooks**: remove promise object from resource (Andreas Madsen) [#23443](https://github.com/nodejs/node/pull/23443)
- [[`c992639fbd`](https://github.com/nodejs/node/commit/c992639fbd)] - **(SEMVER-MAJOR)** **bootstrap**: make Buffer and process non-enumerable (Ruben Bridgewater) [#24874](https://github.com/nodejs/node/pull/24874)
- [[`693401d0dd`](https://github.com/nodejs/node/commit/693401d0dd)] - **(SEMVER-MAJOR)** **buffer**: use stricter range checks (Ruben Bridgewater) [#27045](https://github.com/nodejs/node/pull/27045)
- [[`6113ba96cb`](https://github.com/nodejs/node/commit/6113ba96cb)] - **(SEMVER-MAJOR)** **buffer**: harden SlowBuffer creation (ZYSzys) [#26272](https://github.com/nodejs/node/pull/26272)
- [[`6fb7baf935`](https://github.com/nodejs/node/commit/6fb7baf935)] - **(SEMVER-MAJOR)** **buffer**: harden validation of buffer allocation size (ZYSzys) [#26162](https://github.com/nodejs/node/pull/26162)
- [[`c6d29ccf5a`](https://github.com/nodejs/node/commit/c6d29ccf5a)] - **(SEMVER-MAJOR)** **buffer**: do proper error propagation in addon methods (Anna Henningsen) [#23939](https://github.com/nodejs/node/pull/23939)
- [[`a7d7d4dfb7`](https://github.com/nodejs/node/commit/a7d7d4dfb7)] - **(SEMVER-MAJOR)** **build**: increase MACOS_DEPLOYMENT_TARGET to 10.10 (Rod Vagg) [#27275](https://github.com/nodejs/node/pull/27275)
- [[`561327702d`](https://github.com/nodejs/node/commit/561327702d)] - **(SEMVER-MAJOR)** **build**: reset embedder string to "-node.0" (Ujjwal Sharma) [#26685](https://github.com/nodejs/node/pull/26685)
- [[`dfcc918e65`](https://github.com/nodejs/node/commit/dfcc918e65)] - **(SEMVER-MAJOR)** **build**: reset embedder string to "-node.0" (Michaël Zasso) [#25852](https://github.com/nodejs/node/pull/25852)
- [[`9334e45aa0`](https://github.com/nodejs/node/commit/9334e45aa0)] - **(SEMVER-MAJOR)** **build**: remove mips support (Ben Noordhuis) [#26192](https://github.com/nodejs/node/pull/26192)
- [[`bb564a3688`](https://github.com/nodejs/node/commit/bb564a3688)] - **(SEMVER-MAJOR)** **build**: update prerequisites on progress towards Python 3 (cclauss) [#25766](https://github.com/nodejs/node/pull/25766)
- [[`3c332abe28`](https://github.com/nodejs/node/commit/3c332abe28)] - **(SEMVER-MAJOR)** **build**: reset embedder string to "-node.0" (Michaël Zasso) [#23423](https://github.com/nodejs/node/pull/23423)
- [[`765766be64`](https://github.com/nodejs/node/commit/765766be64)] - **(SEMVER-MAJOR)** **build**: add common `defines` (Refael Ackermann) [#23426](https://github.com/nodejs/node/pull/23426)
- [[`3b5773fee3`](https://github.com/nodejs/node/commit/3b5773fee3)] - **(SEMVER-MAJOR)** **build,deps**: move gypfiles out 2/2 - moving (Refael Ackermann) [#26685](https://github.com/nodejs/node/pull/26685)
- [[`3531fe9320`](https://github.com/nodejs/node/commit/3531fe9320)] - **(SEMVER-MAJOR)** **build,deps**: add `NOMINMAX` to V8 Windows builds (Refael Ackermann) [#25852](https://github.com/nodejs/node/pull/25852)
- [[`ff5d632a83`](https://github.com/nodejs/node/commit/ff5d632a83)] - **(SEMVER-MAJOR)** **build,deps**: fix V8 snapshot gyp dependencies (Refael Ackermann) [#25852](https://github.com/nodejs/node/pull/25852)
- [[`ecf98b0839`](https://github.com/nodejs/node/commit/ecf98b0839)] - **(SEMVER-MAJOR)** **build,meta**: quiet/pretty make output by default (Refael Ackermann) [#26740](https://github.com/nodejs/node/pull/26740)
- [[`2f477bd34d`](https://github.com/nodejs/node/commit/2f477bd34d)] - **(SEMVER-MAJOR)** **build,win**: mark x86 image as not SAFESEH (Refael Ackermann) [#25852](https://github.com/nodejs/node/pull/25852)
- [[`652877e3a9`](https://github.com/nodejs/node/commit/652877e3a9)] - **(SEMVER-MAJOR)** **child_process**: change the defaults maxBuffer size (kohta ito) [#27179](https://github.com/nodejs/node/pull/27179)
- [[`9ad5106934`](https://github.com/nodejs/node/commit/9ad5106934)] - **(SEMVER-MAJOR)** **child_process**: harden fork arguments validation (ZYSzys) [#27039](https://github.com/nodejs/node/pull/27039)
- [[`eb8a51a35c`](https://github.com/nodejs/node/commit/eb8a51a35c)] - **(SEMVER-MAJOR)** **child_process**: use non-infinite maxBuffer defaults (kohta ito) [#23027](https://github.com/nodejs/node/pull/23027)
- [[`99523758dc`](https://github.com/nodejs/node/commit/99523758dc)] - **(SEMVER-MAJOR)** **console**: don't use ANSI escape codes when TERM=dumb (Vladislav Kaminsky) [#26261](https://github.com/nodejs/node/pull/26261)
- [[`2f1ed5c063`](https://github.com/nodejs/node/commit/2f1ed5c063)] - **(SEMVER-MAJOR)** **crypto**: remove legacy native handles (Tobias Nießen) [#27011](https://github.com/nodejs/node/pull/27011)
- [[`2e2c015422`](https://github.com/nodejs/node/commit/2e2c015422)] - **(SEMVER-MAJOR)** **crypto**: decode missing passphrase errors (Tobias Nießen) [#25208](https://github.com/nodejs/node/pull/25208)
- [[`b8018f407b`](https://github.com/nodejs/node/commit/b8018f407b)] - **(SEMVER-MAJOR)** **crypto**: move DEP0113 to End-of-Life (Tobias Nießen) [#26249](https://github.com/nodejs/node/pull/26249)
- [[`bf3cb3f9b1`](https://github.com/nodejs/node/commit/bf3cb3f9b1)] - **(SEMVER-MAJOR)** **crypto**: remove deprecated crypto.\_toBuf (Tobias Nießen) [#25338](https://github.com/nodejs/node/pull/25338)
- [[`0f63d84f80`](https://github.com/nodejs/node/commit/0f63d84f80)] - **(SEMVER-MAJOR)** **crypto**: set `DEFAULT_ENCODING` property to non-enumerable (Antoine du Hamel) [#23222](https://github.com/nodejs/node/pull/23222)
- [[`95e779a6e9`](https://github.com/nodejs/node/commit/95e779a6e9)] - **(SEMVER-MAJOR)** **deps**: silence irrelevant V8 warning (Michaël Zasso) [#26685](https://github.com/nodejs/node/pull/26685)
- [[`08efd3060d`](https://github.com/nodejs/node/commit/08efd3060d)] - **(SEMVER-MAJOR)** **deps**: update postmortem metadata generation script (cjihrig) [#26685](https://github.com/nodejs/node/pull/26685)
- [[`0da7e99f98`](https://github.com/nodejs/node/commit/0da7e99f98)] - **(SEMVER-MAJOR)** **deps**: V8: un-cherry-pick bd019bd (Refael Ackermann) [#26685](https://github.com/nodejs/node/pull/26685)
- [[`b1015e0de8`](https://github.com/nodejs/node/commit/b1015e0de8)] - **(SEMVER-MAJOR)** **deps**: V8: cherry-pick 6 commits (Michaël Zasso) [#26685](https://github.com/nodejs/node/pull/26685)
- [[`8181811d73`](https://github.com/nodejs/node/commit/8181811d73)] - **(SEMVER-MAJOR)** **deps**: V8: cherry-pick d82c9af (Anna Henningsen) [#26685](https://github.com/nodejs/node/pull/26685)
- [[`1f03fb4d49`](https://github.com/nodejs/node/commit/1f03fb4d49)] - **(SEMVER-MAJOR)** **deps**: V8: cherry-pick e5f01ba (Anna Henningsen) [#26685](https://github.com/nodejs/node/pull/26685)
- [[`e6af2207a9`](https://github.com/nodejs/node/commit/e6af2207a9)] - **(SEMVER-MAJOR)** **deps**: V8: cherry-pick d5f08e4 (Anna Henningsen) [#26685](https://github.com/nodejs/node/pull/26685)
- [[`963061bc02`](https://github.com/nodejs/node/commit/963061bc02)] - **(SEMVER-MAJOR)** **deps**: V8: cherry-pick 6b09d21 (Anna Henningsen) [#26685](https://github.com/nodejs/node/pull/26685)
- [[`b7338b700f`](https://github.com/nodejs/node/commit/b7338b700f)] - **(SEMVER-MAJOR)** **deps**: V8: cherry-pick f0bb5d2 (Anna Henningsen) [#26685](https://github.com/nodejs/node/pull/26685)
- [[`02171949a0`](https://github.com/nodejs/node/commit/02171949a0)] - **(SEMVER-MAJOR)** **deps**: V8: cherry-pick 5b0510d (Anna Henningsen) [#26685](https://github.com/nodejs/node/pull/26685)
- [[`bf572c7831`](https://github.com/nodejs/node/commit/bf572c7831)] - **(SEMVER-MAJOR)** **deps**: V8: cherry-pick 91f0cd0 (Anna Henningsen) [#26685](https://github.com/nodejs/node/pull/26685)
- [[`09f134fccf`](https://github.com/nodejs/node/commit/09f134fccf)] - **(SEMVER-MAJOR)** **deps**: V8: cherry-pick 392316d (Anna Henningsen) [#26685](https://github.com/nodejs/node/pull/26685)
- [[`53ea813d5c`](https://github.com/nodejs/node/commit/53ea813d5c)] - **(SEMVER-MAJOR)** **deps**: V8: cherry-pick 2f79d68 (Anna Henningsen) [#26685](https://github.com/nodejs/node/pull/26685)
- [[`cc75ba3f14`](https://github.com/nodejs/node/commit/cc75ba3f14)] - **(SEMVER-MAJOR)** **deps**: sync V8 gypfiles with 7.4 (Ujjwal Sharma) [#26685](https://github.com/nodejs/node/pull/26685)
- [[`f579e11940`](https://github.com/nodejs/node/commit/f579e11940)] - **(SEMVER-MAJOR)** **deps**: update V8 to 7.4.288.13 (Ujjwal Sharma) [#26685](https://github.com/nodejs/node/pull/26685)
- [[`e0b3de1e90`](https://github.com/nodejs/node/commit/e0b3de1e90)] - **(SEMVER-MAJOR)** **deps**: bump minimum icu version to 63 (Ujjwal Sharma) [#25852](https://github.com/nodejs/node/pull/25852)
- [[`1c494b0a95`](https://github.com/nodejs/node/commit/1c494b0a95)] - **(SEMVER-MAJOR)** **deps**: silence irrelevant V8 warnings (Michaël Zasso) [#25852](https://github.com/nodejs/node/pull/25852)
- [[`cec35a5eb9`](https://github.com/nodejs/node/commit/cec35a5eb9)] - **(SEMVER-MAJOR)** **deps**: V8: cherry-pick 7803fa6 (Jon Kunkee) [#25852](https://github.com/nodejs/node/pull/25852)
- [[`0d4d6b39a7`](https://github.com/nodejs/node/commit/0d4d6b39a7)] - **(SEMVER-MAJOR)** **deps**: V8: cherry-pick 58cefed (Jon Kunkee) [#25852](https://github.com/nodejs/node/pull/25852)
- [[`bea1a386a3`](https://github.com/nodejs/node/commit/bea1a386a3)] - **(SEMVER-MAJOR)** **deps**: V8: cherry-pick d3308d0 (Michaël Zasso) [#25852](https://github.com/nodejs/node/pull/25852)
- [[`cf649c9b02`](https://github.com/nodejs/node/commit/cf649c9b02)] - **(SEMVER-MAJOR)** **deps**: V8: cherry-pick 74571c8 (Michaël Zasso) [#25852](https://github.com/nodejs/node/pull/25852)
- [[`44d5401b8d`](https://github.com/nodejs/node/commit/44d5401b8d)] - **(SEMVER-MAJOR)** **deps**: cherry-pick fc0ddf5 from upstream V8 (Anna Henningsen) [#25852](https://github.com/nodejs/node/pull/25852)
- [[`cefb8029cd`](https://github.com/nodejs/node/commit/cefb8029cd)] - **(SEMVER-MAJOR)** **deps**: sync V8 gypfiles with 7.3 (Ujjwal Sharma) [#25852](https://github.com/nodejs/node/pull/25852)
- [[`d266e3e2cf`](https://github.com/nodejs/node/commit/d266e3e2cf)] - **(SEMVER-MAJOR)** **deps**: sync V8 gypfiles with 7.2 (Michaël Zasso) [#25852](https://github.com/nodejs/node/pull/25852)
- [[`7b48713334`](https://github.com/nodejs/node/commit/7b48713334)] - **(SEMVER-MAJOR)** **deps**: update V8 to 7.3.492.25 (Michaël Zasso) [#25852](https://github.com/nodejs/node/pull/25852)
- [[`6df7bd6c3b`](https://github.com/nodejs/node/commit/6df7bd6c3b)] - **(SEMVER-MAJOR)** **deps**: add s390 asm rules for OpenSSL-1.1.1 (Shigeki Ohtsu) [#19794](https://github.com/nodejs/node/pull/19794)
- [[`5620727f30`](https://github.com/nodejs/node/commit/5620727f30)] - **(SEMVER-MAJOR)** **deps**: sync V8 gypfiles with 7.1 (Refael Ackermann) [#23423](https://github.com/nodejs/node/pull/23423)
- [[`9b4bf7de6c`](https://github.com/nodejs/node/commit/9b4bf7de6c)] - **(SEMVER-MAJOR)** **deps**: update V8 to 7.1.302.28 (Michaël Zasso) [#23423](https://github.com/nodejs/node/pull/23423)
- [[`3d8b844112`](https://github.com/nodejs/node/commit/3d8b844112)] - **(SEMVER-MAJOR)** **deps,build**: move gypfiles out 1/2 - required changes (Refael Ackermann) [#26685](https://github.com/nodejs/node/pull/26685)
- [[`fff922afee`](https://github.com/nodejs/node/commit/fff922afee)] - **(SEMVER-MAJOR)** **deps,build**: compute torque_outputs in v8.gyp (Refael Ackermann) [#26685](https://github.com/nodejs/node/pull/26685)
- [[`4507246adc`](https://github.com/nodejs/node/commit/4507246adc)] - **(SEMVER-MAJOR)** **deps,build**: refactor v8 gypfiles (Refael Ackermann) [#26685](https://github.com/nodejs/node/pull/26685)
- [[`b581d59655`](https://github.com/nodejs/node/commit/b581d59655)] - **(SEMVER-MAJOR)** **doc**: update supported platforms for Node.js 12 (Rod Vagg) [#26714](https://github.com/nodejs/node/pull/26714)
- [[`309e7723ea`](https://github.com/nodejs/node/commit/309e7723ea)] - **(SEMVER-MAJOR)** **doc**: update behaviour of fs.writeFile (Sakthipriyan Vairamani (thefourtheye)) [#25080](https://github.com/nodejs/node/pull/25080)
- [[`89740a4f0e`](https://github.com/nodejs/node/commit/89740a4f0e)] - **(SEMVER-MAJOR)** **doc**: add internal functionality details of util.inherits (Ruben Bridgewater) [#24755](https://github.com/nodejs/node/pull/24755)
- [[`1ed3c54ecb`](https://github.com/nodejs/node/commit/1ed3c54ecb)] - **(SEMVER-MAJOR)** **errors**: update error name (Ruben Bridgewater) [#26738](https://github.com/nodejs/node/pull/26738)
- [[`abafd38c8d`](https://github.com/nodejs/node/commit/abafd38c8d)] - **(SEMVER-MAJOR)** **fs**: use proper .destroy() implementation for SyncWriteStream (Matteo Collina) [#26690](https://github.com/nodejs/node/pull/26690)
- [[`1cdeb9f956`](https://github.com/nodejs/node/commit/1cdeb9f956)] - **(SEMVER-MAJOR)** **fs**: improve mode validation (Ruben Bridgewater) [#26575](https://github.com/nodejs/node/pull/26575)
- [[`70f4f08a9f`](https://github.com/nodejs/node/commit/70f4f08a9f)] - **(SEMVER-MAJOR)** **fs**: harden validation of start option in createWriteStream (ZYSzys) [#25579](https://github.com/nodejs/node/pull/25579)
- [[`8f4b924f4a`](https://github.com/nodejs/node/commit/8f4b924f4a)] - **(SEMVER-MAJOR)** **fs**: make writeFile consistent with readFile wrt fd (Sakthipriyan Vairamani (thefourtheye)) [#23709](https://github.com/nodejs/node/pull/23709)
- [[`907941d48e`](https://github.com/nodejs/node/commit/907941d48e)] - **(SEMVER-MAJOR)** **http**: validate timeout in ClientRequest() (cjihrig) [#26214](https://github.com/nodejs/node/pull/26214)
- [[`bcf2886a84`](https://github.com/nodejs/node/commit/bcf2886a84)] - **(SEMVER-MAJOR)** **http**: return HTTP 431 on HPE_HEADER_OVERFLOW error (Albert Still) [#25605](https://github.com/nodejs/node/pull/25605)
- [[`2cb8f24751`](https://github.com/nodejs/node/commit/2cb8f24751)] - **(SEMVER-MAJOR)** **http**: switch default parser to llhttp (Anna Henningsen) [#24870](https://github.com/nodejs/node/pull/24870)
- [[`91748dd89c`](https://github.com/nodejs/node/commit/91748dd89c)] - **(SEMVER-MAJOR)** **http**: change DEP0066 to a runtime deprecation (Morgan Roderick) [#24167](https://github.com/nodejs/node/pull/24167)
- [[`f3b49cfa7b`](https://github.com/nodejs/node/commit/f3b49cfa7b)] - **(SEMVER-MAJOR)** **http**: else case is not reachable (szabolcsit) [#24176](https://github.com/nodejs/node/pull/24176)
- [[`bd9109c241`](https://github.com/nodejs/node/commit/bd9109c241)] - **(SEMVER-MAJOR)** **lib**: move DEP0021 to end of life (cjihrig) [#27127](https://github.com/nodejs/node/pull/27127)
- [[`15c0947fee`](https://github.com/nodejs/node/commit/15c0947fee)] - **(SEMVER-MAJOR)** **lib**: remove Atomics.wake (Gus Caplan) [#27033](https://github.com/nodejs/node/pull/27033)
- [[`3fe1e80896`](https://github.com/nodejs/node/commit/3fe1e80896)] - **(SEMVER-MAJOR)** **lib**: validate Error.captureStackTrace() calls (Ruben Bridgewater) [#26738](https://github.com/nodejs/node/pull/26738)
- [[`bfbce289c3`](https://github.com/nodejs/node/commit/bfbce289c3)] - **(SEMVER-MAJOR)** **lib**: refactor Error.captureStackTrace() usage (Ruben Bridgewater) [#26738](https://github.com/nodejs/node/pull/26738)
- [[`f9ddbb6b2f`](https://github.com/nodejs/node/commit/f9ddbb6b2f)] - **(SEMVER-MAJOR)** **lib**: move DTRACE\_\* probes out of global scope (James M Snell) [#26541](https://github.com/nodejs/node/pull/26541)
- [[`c7e628f8b3`](https://github.com/nodejs/node/commit/c7e628f8b3)] - **(SEMVER-MAJOR)** **lib**: deprecate \_stream_wrap (Sam Roberts) [#26245](https://github.com/nodejs/node/pull/26245)
- [[`be78266fb3`](https://github.com/nodejs/node/commit/be78266fb3)] - **(SEMVER-MAJOR)** **lib**: don't use `util.inspect()` internals (Ruben Bridgewater) [#24971](https://github.com/nodejs/node/pull/24971)
- [[`a02e3e2d5f`](https://github.com/nodejs/node/commit/a02e3e2d5f)] - **(SEMVER-MAJOR)** **lib**: improve error message for MODULE_NOT_FOUND (Ali Ijaz Sheikh) [#25690](https://github.com/nodejs/node/pull/25690)
- [[`05cd1a0929`](https://github.com/nodejs/node/commit/05cd1a0929)] - **(SEMVER-MAJOR)** **lib**: requireStack property for MODULE_NOT_FOUND (Ali Ijaz Sheikh) [#25690](https://github.com/nodejs/node/pull/25690)
- [[`29d3d1ea13`](https://github.com/nodejs/node/commit/29d3d1ea13)] - **(SEMVER-MAJOR)** **lib**: move DEP0029 to end of life (cjihrig) [#25377](https://github.com/nodejs/node/pull/25377)
- [[`a665d13ad9`](https://github.com/nodejs/node/commit/a665d13ad9)] - **(SEMVER-MAJOR)** **lib**: move DEP0028 to end of life (cjihrig) [#25377](https://github.com/nodejs/node/pull/25377)
- [[`10df21b071`](https://github.com/nodejs/node/commit/10df21b071)] - **(SEMVER-MAJOR)** **lib**: move DEP0027 to end of life (cjihrig) [#25377](https://github.com/nodejs/node/pull/25377)
- [[`2d578ad996`](https://github.com/nodejs/node/commit/2d578ad996)] - **(SEMVER-MAJOR)** **lib**: move DEP0026 to end of life (cjihrig) [#25377](https://github.com/nodejs/node/pull/25377)
- [[`853bee0acf`](https://github.com/nodejs/node/commit/853bee0acf)] - **(SEMVER-MAJOR)** **lib**: move DEP0023 to end of life (cjihrig) [#25280](https://github.com/nodejs/node/pull/25280)
- [[`d4934ae6f2`](https://github.com/nodejs/node/commit/d4934ae6f2)] - **(SEMVER-MAJOR)** **lib**: move DEP0006 to end of life (cjihrig) [#25279](https://github.com/nodejs/node/pull/25279)
- [[`4100001624`](https://github.com/nodejs/node/commit/4100001624)] - **(SEMVER-MAJOR)** **lib**: remove unintended access to deps/ (Anna Henningsen) [#25138](https://github.com/nodejs/node/pull/25138)
- [[`b416dafb87`](https://github.com/nodejs/node/commit/b416dafb87)] - **(SEMVER-MAJOR)** **lib**: move DEP0120 to end of life (cjihrig) [#24862](https://github.com/nodejs/node/pull/24862)
- [[`59257543c3`](https://github.com/nodejs/node/commit/59257543c3)] - **(SEMVER-MAJOR)** **lib**: use ES6 class inheritance style (Ruben Bridgewater) [#24755](https://github.com/nodejs/node/pull/24755)
- [[`dcc82b37b6`](https://github.com/nodejs/node/commit/dcc82b37b6)] - **(SEMVER-MAJOR)** **lib**: remove `inherits()` usage (Ruben Bridgewater) [#24755](https://github.com/nodejs/node/pull/24755)
- [[`d11c4beb4b`](https://github.com/nodejs/node/commit/d11c4beb4b)] - **(SEMVER-MAJOR)** **module**: remove dead code (Ruben Bridgewater) [#26983](https://github.com/nodejs/node/pull/26983)
- [[`75007d64c0`](https://github.com/nodejs/node/commit/75007d64c0)] - **(SEMVER-MAJOR)** **module**: mark DEP0019 as End-of-Life (Ruben Bridgewater) [#26973](https://github.com/nodejs/node/pull/26973)
- [[`115f0f5a57`](https://github.com/nodejs/node/commit/115f0f5a57)] - **(SEMVER-MAJOR)** **module**: throw an error for invalid package.json main entries (Ruben Bridgewater) [#26823](https://github.com/nodejs/node/pull/26823)
- [[`60ce2fd827`](https://github.com/nodejs/node/commit/60ce2fd827)] - **(SEMVER-MAJOR)** **module**: don't search in require.resolve.paths (cjihrig) [#23683](https://github.com/nodejs/node/pull/23683)
- [[`f0f26cedcc`](https://github.com/nodejs/node/commit/f0f26cedcc)] - **(SEMVER-MAJOR)** **n-api**: remove code from error name (Ruben Bridgewater) [#26738](https://github.com/nodejs/node/pull/26738)
- [[`96204c3c71`](https://github.com/nodejs/node/commit/96204c3c71)] - **(SEMVER-MAJOR)** **net**: do not manipulate potential user code (Ruben Bridgewater) [#26751](https://github.com/nodejs/node/pull/26751)
- [[`9389b464ea`](https://github.com/nodejs/node/commit/9389b464ea)] - **(SEMVER-MAJOR)** **net**: emit "write after end" errors in the next tick (Ouyang Yadong) [#24457](https://github.com/nodejs/node/pull/24457)
- [[`1523111250`](https://github.com/nodejs/node/commit/1523111250)] - **(SEMVER-MAJOR)** **net**: deprecate \_setSimultaneousAccepts() undocumented function (James M Snell) [#23760](https://github.com/nodejs/node/pull/23760)
- [[`802ea05a37`](https://github.com/nodejs/node/commit/802ea05a37)] - **(SEMVER-MAJOR)** **net,http2**: merge setTimeout code (ZYSzys) [#25084](https://github.com/nodejs/node/pull/25084)
- [[`16e4cd19f2`](https://github.com/nodejs/node/commit/16e4cd19f2)] - **(SEMVER-MAJOR)** **os**: implement os.type() using uv_os_uname() (cjihrig) [#25659](https://github.com/nodejs/node/pull/25659)
- [[`53ebd3311d`](https://github.com/nodejs/node/commit/53ebd3311d)] - **(SEMVER-MAJOR)** **process**: global.process, global.Buffer getters (Guy Bedford) [#26882](https://github.com/nodejs/node/pull/26882)
- [[`fa5e097530`](https://github.com/nodejs/node/commit/fa5e097530)] - **(SEMVER-MAJOR)** **process**: move DEP0062 (node --debug) to end-of-life (Joyee Cheung) [#25828](https://github.com/nodejs/node/pull/25828)
- [[`154efc9bde`](https://github.com/nodejs/node/commit/154efc9bde)] - **(SEMVER-MAJOR)** **process**: exit on --debug and --debug-brk after option parsing (Joyee Cheung) [#25828](https://github.com/nodejs/node/pull/25828)
- [[`3439c955ab`](https://github.com/nodejs/node/commit/3439c955ab)] - **(SEMVER-MAJOR)** **process**: improve `--redirect-warnings` handling (Ruben Bridgewater) [#24965](https://github.com/nodejs/node/pull/24965)
- [[`d3a62fe7fc`](https://github.com/nodejs/node/commit/d3a62fe7fc)] - **(SEMVER-MAJOR)** **readline**: support TERM=dumb (Vladislav Kaminsky) [#26261](https://github.com/nodejs/node/pull/26261)
- [[`fe963149f6`](https://github.com/nodejs/node/commit/fe963149f6)] - **(SEMVER-MAJOR)** **repl**: add welcome message (gengjiawen) [#25947](https://github.com/nodejs/node/pull/25947)
- [[`97737fd5fb`](https://github.com/nodejs/node/commit/97737fd5fb)] - **(SEMVER-MAJOR)** **repl**: fix terminal default setting (Ruben Bridgewater) [#26518](https://github.com/nodejs/node/pull/26518)
- [[`82b3ee776b`](https://github.com/nodejs/node/commit/82b3ee776b)] - **(SEMVER-MAJOR)** **repl**: check colors with .getColorDepth() (Vladislav Kaminsky) [#26261](https://github.com/nodejs/node/pull/26261)
- [[`584305841d`](https://github.com/nodejs/node/commit/584305841d)] - **(SEMVER-MAJOR)** **repl**: deprecate REPLServer.rli (Ruben Bridgewater) [#26260](https://github.com/nodejs/node/pull/26260)
- [[`bf766c1b44`](https://github.com/nodejs/node/commit/bf766c1b44)] - **(SEMVER-MAJOR)** **src**: remove unused INT_MAX constant (Sam Roberts) [#27078](https://github.com/nodejs/node/pull/27078)
- [[`7df9e77236`](https://github.com/nodejs/node/commit/7df9e77236)] - **(SEMVER-MAJOR)** **src**: update NODE_MODULE_VERSION to 72 (Ujjwal Sharma) [#26685](https://github.com/nodejs/node/pull/26685)
- [[`96c3224de0`](https://github.com/nodejs/node/commit/96c3224de0)] - **(SEMVER-MAJOR)** **src**: remove `AddPromiseHook()` (Anna Henningsen) [#26574](https://github.com/nodejs/node/pull/26574)
- [[`9577f7724d`](https://github.com/nodejs/node/commit/9577f7724d)] - **(SEMVER-MAJOR)** **src**: update NODE_MODULE_VERSION to 71 (Michaël Zasso) [#25852](https://github.com/nodejs/node/pull/25852)
- [[`6d9aa73b1f`](https://github.com/nodejs/node/commit/6d9aa73b1f)] - **(SEMVER-MAJOR)** **src**: clean up MultiIsolatePlatform interface (Anna Henningsen) [#26384](https://github.com/nodejs/node/pull/26384)
- [[`1d996f58af`](https://github.com/nodejs/node/commit/1d996f58af)] - **(SEMVER-MAJOR)** **src**: properly configure default heap limits (Ali Ijaz Sheikh) [#25576](https://github.com/nodejs/node/pull/25576)
- [[`9021b0d3fc`](https://github.com/nodejs/node/commit/9021b0d3fc)] - **(SEMVER-MAJOR)** **src**: remove icuDataDir from node config (GauthamBanasandra) [#24780](https://github.com/nodejs/node/pull/24780)
- [[`a6f69ebc05`](https://github.com/nodejs/node/commit/a6f69ebc05)] - **(SEMVER-MAJOR)** **src**: explicitly allow JS in ReadHostObject (Yang Guo) [#23423](https://github.com/nodejs/node/pull/23423)
- [[`3d25544148`](https://github.com/nodejs/node/commit/3d25544148)] - **(SEMVER-MAJOR)** **src**: update postmortem constant (cjihrig) [#23423](https://github.com/nodejs/node/pull/23423)
- [[`23603447ad`](https://github.com/nodejs/node/commit/23603447ad)] - **(SEMVER-MAJOR)** **src**: update NODE_MODULE_VERSION to 68 (Michaël Zasso) [#23423](https://github.com/nodejs/node/pull/23423)
- [[`afad3b443e`](https://github.com/nodejs/node/commit/afad3b443e)] - **(SEMVER-MAJOR)** **test**: update postmortem metadata test for V8 7.4 (cjihrig) [#26685](https://github.com/nodejs/node/pull/26685)
- [[`e96e3f9eb0`](https://github.com/nodejs/node/commit/e96e3f9eb0)] - **(SEMVER-MAJOR)** **test**: remove redundant common.mustCall (Ruben Bridgewater) [#26738](https://github.com/nodejs/node/pull/26738)
- [[`01b112a031`](https://github.com/nodejs/node/commit/01b112a031)] - **(SEMVER-MAJOR)** **test**: update postmortem metadata test for V8 7.3 (cjihrig) [#25852](https://github.com/nodejs/node/pull/25852)
- [[`38ad285a2e`](https://github.com/nodejs/node/commit/38ad285a2e)] - **(SEMVER-MAJOR)** **test**: fix tests after V8 update (Michaël Zasso) [#25852](https://github.com/nodejs/node/pull/25852)
- [[`260d5f8c3b`](https://github.com/nodejs/node/commit/260d5f8c3b)] - **(SEMVER-MAJOR)** **test**: update test-v8-stats (Michaël Zasso) [#25852](https://github.com/nodejs/node/pull/25852)
- [[`78c8491a7e`](https://github.com/nodejs/node/commit/78c8491a7e)] - **(SEMVER-MAJOR)** **test**: remove apply calls over 65534 arg limit (Peter Marshall) [#25852](https://github.com/nodejs/node/pull/25852)
- [[`22a9fe3552`](https://github.com/nodejs/node/commit/22a9fe3552)] - **(SEMVER-MAJOR)** **test**: add test for net-socket-setTimeout callback (ZYSzys) [#25084](https://github.com/nodejs/node/pull/25084)
- [[`379bf1aa8e`](https://github.com/nodejs/node/commit/379bf1aa8e)] - **(SEMVER-MAJOR)** **test**: update postmortem metadata test for V8 7.1 (cjihrig) [#23423](https://github.com/nodejs/node/pull/23423)
- [[`624a242b05`](https://github.com/nodejs/node/commit/624a242b05)] - **(SEMVER-MAJOR)** **test**: simplify regression test for SEGV (Sam Roberts) [#24241](https://github.com/nodejs/node/pull/24241)
- [[`42dbaed460`](https://github.com/nodejs/node/commit/42dbaed460)] - **(SEMVER-MAJOR)** **tls**: support TLSv1.3 (Sam Roberts) [#26209](https://github.com/nodejs/node/pull/26209)
- [[`0f745bf9bd`](https://github.com/nodejs/node/commit/0f745bf9bd)] - **(SEMVER-MAJOR)** **tls**: return correct version from getCipher() (Sam Roberts) [#26625](https://github.com/nodejs/node/pull/26625)
- [[`6b7c402518`](https://github.com/nodejs/node/commit/6b7c402518)] - **(SEMVER-MAJOR)** **tls**: check arg types of renegotiate() (Sam Roberts) [#25876](https://github.com/nodejs/node/pull/25876)
- [[`b05b330025`](https://github.com/nodejs/node/commit/b05b330025)] - **(SEMVER-MAJOR)** **tls**: add code for ERR_TLS_INVALID_PROTOCOL_METHOD (Sam Roberts) [#24729](https://github.com/nodejs/node/pull/24729)
- [[`9b2ffff62c`](https://github.com/nodejs/node/commit/9b2ffff62c)] - **(SEMVER-MAJOR)** **tls**: emit a warning when servername is an IP address (Rodger Combs) [#23329](https://github.com/nodejs/node/pull/23329)
- [[`60eca6a5d4`](https://github.com/nodejs/node/commit/60eca6a5d4)] - **(SEMVER-MAJOR)** **tls**: disable TLS v1.0 and v1.1 by default (Ben Noordhuis) [#23814](https://github.com/nodejs/node/pull/23814)
- [[`3b4159c8ed`](https://github.com/nodejs/node/commit/3b4159c8ed)] - **(SEMVER-MAJOR)** **tls**: remove unused arg to createSecureContext() (Sam Roberts) [#24241](https://github.com/nodejs/node/pull/24241)
- [[`246a6fc107`](https://github.com/nodejs/node/commit/246a6fc107)] - **(SEMVER-MAJOR)** **tls**: deprecate Server.prototype.setOptions() (cjihrig) [#23820](https://github.com/nodejs/node/pull/23820)
- [[`87719d792b`](https://github.com/nodejs/node/commit/87719d792b)] - **(SEMVER-MAJOR)** **tls**: load NODE_EXTRA_CA_CERTS at startup (Ouyang Yadong) [#23354](https://github.com/nodejs/node/pull/23354)
- [[`c9fece38c8`](https://github.com/nodejs/node/commit/c9fece38c8)] - **(SEMVER-MAJOR)** **util**: change inspect compact and breakLength default (Ruben Bridgewater) [#27109](https://github.com/nodejs/node/pull/27109)
- [[`892c51f330`](https://github.com/nodejs/node/commit/892c51f330)] - **(SEMVER-MAJOR)** **util**: improve inspect edge cases (Ruben Bridgewater) [#27109](https://github.com/nodejs/node/pull/27109)
- [[`63e13fd220`](https://github.com/nodejs/node/commit/63e13fd220)] - **(SEMVER-MAJOR)** **util**: only the first line of the error message (Simon Zünd) [#26685](https://github.com/nodejs/node/pull/26685)
- [[`b5ea925c8e`](https://github.com/nodejs/node/commit/b5ea925c8e)] - **(SEMVER-MAJOR)** **util**: don't set the prototype of callbackified functions (Ruben Bridgewater) [#26893](https://github.com/nodejs/node/pull/26893)
- [[`46bf0d0f4f`](https://github.com/nodejs/node/commit/46bf0d0f4f)] - **(SEMVER-MAJOR)** **util**: rename callbackified function (Ruben Bridgewater) [#26893](https://github.com/nodejs/node/pull/26893)
- [[`61d1334e5b`](https://github.com/nodejs/node/commit/61d1334e5b)] - **(SEMVER-MAJOR)** **util**: increase function length when using `callbackify()` (Ruben Bridgewater) [#26893](https://github.com/nodejs/node/pull/26893)
- [[`5672ab7668`](https://github.com/nodejs/node/commit/5672ab7668)] - **(SEMVER-MAJOR)** **util**: prevent tampering with internals in `inspect()` (Ruben Bridgewater) [#26577](https://github.com/nodejs/node/pull/26577)
- [[`a32cbe1597`](https://github.com/nodejs/node/commit/a32cbe1597)] - **(SEMVER-MAJOR)** **util**: fix proxy inspection (Ruben Bridgewater) [#26241](https://github.com/nodejs/node/pull/26241)
- [[`7b674697d8`](https://github.com/nodejs/node/commit/7b674697d8)] - **(SEMVER-MAJOR)** **util**: prevent leaking internal properties (Ruben Bridgewater) [#24971](https://github.com/nodejs/node/pull/24971)
- [[`1847696f4b`](https://github.com/nodejs/node/commit/1847696f4b)] - **(SEMVER-MAJOR)** **util**: protect against monkeypatched Object prototype for inspect() (Rich Trott) [#25953](https://github.com/nodejs/node/pull/25953)
- [[`c1b9be53c8`](https://github.com/nodejs/node/commit/c1b9be53c8)] - **(SEMVER-MAJOR)** **util**: treat format arguments equally (Roman Reiss) [#23162](https://github.com/nodejs/node/pull/23162)
- [[`cda6b20816`](https://github.com/nodejs/node/commit/cda6b20816)] - **(SEMVER-MAJOR)** **win, fs**: detect if symlink target is a directory (Bartosz Sosnowski) [#23724](https://github.com/nodejs/node/pull/23724)
- [[`9a2654601e`](https://github.com/nodejs/node/commit/9a2654601e)] - **(SEMVER-MAJOR)** **zlib**: throw TypeError if callback is missing (Anna Henningsen) [#24929](https://github.com/nodejs/node/pull/24929)
- [[`4eee55d354`](https://github.com/nodejs/node/commit/4eee55d354)] - **(SEMVER-MAJOR)** **zlib**: make “bare” constants un-enumerable (Anna Henningsen) [#24824](https://github.com/nodejs/node/pull/24824)

### Semver-Minor Commits

- [[`3d8532f851`](https://github.com/nodejs/node/commit/3d8532f851)] - **(SEMVER-MINOR)** **buffer**: add {read|write}Big\[U\]Int64{BE|LE} methods (Nikolai Vavilov) [#19691](https://github.com/nodejs/node/pull/19691)
- [[`969bd1eb7b`](https://github.com/nodejs/node/commit/969bd1eb7b)] - **(SEMVER-MINOR)** **crypto**: add support for RSA-PSS keys (Tobias Nießen) [#26960](https://github.com/nodejs/node/pull/26960)
- [[`7d0e50dcfe`](https://github.com/nodejs/node/commit/7d0e50dcfe)] - **(SEMVER-MINOR)** **crypto**: add crypto.sign() and crypto.verify() (Brian White) [#26611](https://github.com/nodejs/node/pull/26611)
- [[`bcbd35a48d`](https://github.com/nodejs/node/commit/bcbd35a48d)] - **(SEMVER-MINOR)** **crypto**: add openssl specific error properties (Sam Roberts) [#26868](https://github.com/nodejs/node/pull/26868)
- [[`85fda7e848`](https://github.com/nodejs/node/commit/85fda7e848)] - **(SEMVER-MINOR)** **crypto**: add support for x25119 and x448 KeyObjects (Filip Skokan) [#26774](https://github.com/nodejs/node/pull/26774)
- [[`3a9592496c`](https://github.com/nodejs/node/commit/3a9592496c)] - **(SEMVER-MINOR)** **crypto**: add support for EdDSA key pair generation (Tobias Nießen) [#26554](https://github.com/nodejs/node/pull/26554)
- [[`4895927a0a`](https://github.com/nodejs/node/commit/4895927a0a)] - **(SEMVER-MINOR)** **crypto**: add KeyObject.asymmetricKeySize (Patrick Gansterer) [#26387](https://github.com/nodejs/node/pull/26387)
- [[`2161690024`](https://github.com/nodejs/node/commit/2161690024)] - **(SEMVER-MINOR)** **deps**: update nghttp2 to 1.38.0 (gengjiawen) [#27295](https://github.com/nodejs/node/pull/27295)
- [[`ffd2df063c`](https://github.com/nodejs/node/commit/ffd2df063c)] - **(SEMVER-MINOR)** **doc**: update util colors (Ruben Bridgewater) [#27052](https://github.com/nodejs/node/pull/27052)
- [[`b1094dbe19`](https://github.com/nodejs/node/commit/b1094dbe19)] - **(SEMVER-MINOR)** **esm**: phase two of new esm implementation (guybedford) [#26745](https://github.com/nodejs/node/pull/26745)
- [[`e0e3084482`](https://github.com/nodejs/node/commit/e0e3084482)] - **(SEMVER-MINOR)** **inspector**: implement --cpu-prof\[-path\] (Joyee Cheung) [#27147](https://github.com/nodejs/node/pull/27147)
- [[`9f1282d536`](https://github.com/nodejs/node/commit/9f1282d536)] - **(SEMVER-MINOR)** **lib**: move queueMicrotask to stable (Gus Caplan) [#25594](https://github.com/nodejs/node/pull/25594)
- [[`9b6b567bc4`](https://github.com/nodejs/node/commit/9b6b567bc4)] - **(SEMVER-MINOR)** **lib,src,doc**: add --heapsnapshot-signal CLI flag (cjihrig) [#27133](https://github.com/nodejs/node/pull/27133)
- [[`9dcc9b6a6b`](https://github.com/nodejs/node/commit/9dcc9b6a6b)] - **(SEMVER-MINOR)** **process**: add --unhandled-rejections flag (Ruben Bridgewater) [#26599](https://github.com/nodejs/node/pull/26599)
- [[`ece507394a`](https://github.com/nodejs/node/commit/ece507394a)] - **(SEMVER-MINOR)** **src**: do not reuse async resource in http parsers (Daniel Beckert) [#25094](https://github.com/nodejs/node/pull/25094)
- [[`2755471bf3`](https://github.com/nodejs/node/commit/2755471bf3)] - **(SEMVER-MINOR)** **src**: print error before aborting (Ruben Bridgewater) [#26599](https://github.com/nodejs/node/pull/26599)
- [[`ca9c0c90c2`](https://github.com/nodejs/node/commit/ca9c0c90c2)] - **(SEMVER-MINOR)** **src**: add .code and SSL specific error properties (Sam Roberts) [#25093](https://github.com/nodejs/node/pull/25093)
- [[`8c69e06972`](https://github.com/nodejs/node/commit/8c69e06972)] - **(SEMVER-MINOR)** **tls**: return an OpenSSL error from renegotiate (Sam Roberts) [#26868](https://github.com/nodejs/node/pull/26868)
- [[`90e958aa4d`](https://github.com/nodejs/node/commit/90e958aa4d)] - **(SEMVER-MINOR)** **util**: only sort weak entries once (Ruben Bridgewater) [#27052](https://github.com/nodejs/node/pull/27052)
- [[`1940114ac3`](https://github.com/nodejs/node/commit/1940114ac3)] - **(SEMVER-MINOR)** **util**: highlight stack frames (Ruben Bridgewater) [#27052](https://github.com/nodejs/node/pull/27052)

### Semver-Patch Commits

- [[`75463a9004`](https://github.com/nodejs/node/commit/75463a9004)] - **assert**: fix rejects stack trace and operator (Ruben Bridgewater) [#27047](https://github.com/nodejs/node/pull/27047)
- [[`d3d4e10107`](https://github.com/nodejs/node/commit/d3d4e10107)] - **async_hooks**: improve AsyncResource performance (Anatoli Papirovski) [#27032](https://github.com/nodejs/node/pull/27032)
- [[`3973354951`](https://github.com/nodejs/node/commit/3973354951)] - **benchmark**: fix buffer-base64-decode.js (Rich Trott) [#27260](https://github.com/nodejs/node/pull/27260)
- [[`f98679f3b2`](https://github.com/nodejs/node/commit/f98679f3b2)] - **benchmark**: add benchmark for dns.promises.lookup() (Rich Trott) [#27249](https://github.com/nodejs/node/pull/27249)
- [[`29d0b43426`](https://github.com/nodejs/node/commit/29d0b43426)] - **benchmark**: fix http headers benchmark (Anatoli Papirovski) [#27021](https://github.com/nodejs/node/pull/27021)
- [[`77dee25efd`](https://github.com/nodejs/node/commit/77dee25efd)] - **benchmark**: remove deprecated argument (Rich Trott) [#27091](https://github.com/nodejs/node/pull/27091)
- [[`b08a867d60`](https://github.com/nodejs/node/commit/b08a867d60)] - **benchmark,doc,lib**: capitalize more comments (Ruben Bridgewater) [#26849](https://github.com/nodejs/node/pull/26849)
- [[`d834275a48`](https://github.com/nodejs/node/commit/d834275a48)] - **buffer**: fix custom inspection with extra properties (Ruben Bridgewater) [#27074](https://github.com/nodejs/node/pull/27074)
- [[`75eaf25e78`](https://github.com/nodejs/node/commit/75eaf25e78)] - **buffer**: use stricter `from()` input validation (Ruben Bridgewater) [#26825](https://github.com/nodejs/node/pull/26825)
- [[`5aaf666b3b`](https://github.com/nodejs/node/commit/5aaf666b3b)] - **build**: improve embedded code-cache detection (Refael Ackermann) [#27311](https://github.com/nodejs/node/pull/27311)
- [[`d17dfc7bb1`](https://github.com/nodejs/node/commit/d17dfc7bb1)] - **build**: remove redundant pyenv call in Travis build (Richard Lau) [#27247](https://github.com/nodejs/node/pull/27247)
- [[`14df42fd00`](https://github.com/nodejs/node/commit/14df42fd00)] - **build**: run `mkcodecache` as an action (Refael Ackermann) [#27161](https://github.com/nodejs/node/pull/27161)
- [[`b468a1dfc3`](https://github.com/nodejs/node/commit/b468a1dfc3)] - **build**: pin Python version in Travis (Richard Lau) [#27166](https://github.com/nodejs/node/pull/27166)
- [[`7b0d867389`](https://github.com/nodejs/node/commit/7b0d867389)] - **build**: fix test failures not failing Travis builds (Richard Lau) [#27176](https://github.com/nodejs/node/pull/27176)
- [[`56354d480d`](https://github.com/nodejs/node/commit/56354d480d)] - **build**: run flaky tests in Travis (Anna Henningsen) [#27158](https://github.com/nodejs/node/pull/27158)
- [[`72f4a830d7`](https://github.com/nodejs/node/commit/72f4a830d7)] - **build**: tidy up additional libraries on Windows (Richard Lau) [#27138](https://github.com/nodejs/node/pull/27138)
- [[`af03de48d8`](https://github.com/nodejs/node/commit/af03de48d8)] - **build**: don't use lint-ci on Travis (Richard Lau) [#27062](https://github.com/nodejs/node/pull/27062)
- [[`41ba699973`](https://github.com/nodejs/node/commit/41ba699973)] - **build**: update configure for Node.js 12 (Richard Lau) [#26719](https://github.com/nodejs/node/pull/26719)
- [[`20a917c571`](https://github.com/nodejs/node/commit/20a917c571)] - **build**: move optimizing link directives to node.exe target (Refael Ackermann) [#25931](https://github.com/nodejs/node/pull/25931)
- [[`4698757610`](https://github.com/nodejs/node/commit/4698757610)] - **build,deps**: remove cygwin configuration which is not supported (Refael Ackermann) [#25931](https://github.com/nodejs/node/pull/25931)
- [[`cd5c7bf240`](https://github.com/nodejs/node/commit/cd5c7bf240)] - **build,deps**: use PCH also for v8_initializers (Refael Ackermann) [#25931](https://github.com/nodejs/node/pull/25931)
- [[`6608cf286d`](https://github.com/nodejs/node/commit/6608cf286d)] - **build,deps,v8**: tie up loose ends (Refael Ackermann) [#26666](https://github.com/nodejs/node/pull/26666)
- [[`6ac80f0e2b`](https://github.com/nodejs/node/commit/6ac80f0e2b)] - **build,src**: add PCH to node.gypi (Refael Ackermann) [#25931](https://github.com/nodejs/node/pull/25931)
- [[`f216d5bbb1`](https://github.com/nodejs/node/commit/f216d5bbb1)] - **build,test**: fail `coverage` target if tests fail (Refael Ackermann) [#25432](https://github.com/nodejs/node/pull/25432)
- [[`82b798907d`](https://github.com/nodejs/node/commit/82b798907d)] - **build,tools**: add more headers to V8 PCH file (Refael Ackermann) [#25931](https://github.com/nodejs/node/pull/25931)
- [[`d66c7e3470`](https://github.com/nodejs/node/commit/d66c7e3470)] - **build,win**: deprecate `vcbuild test-ci` (Refael Ackermann) [#27231](https://github.com/nodejs/node/pull/27231)
- [[`0fc27f6bc0`](https://github.com/nodejs/node/commit/0fc27f6bc0)] - **build,win**: bail vcbuild if mklink fails (Refael Ackermann) [#27216](https://github.com/nodejs/node/pull/27216)
- [[`88beaf01f1`](https://github.com/nodejs/node/commit/88beaf01f1)] - **build,win**: rename node.lib to libnode.lib (Refael Ackermann) [#27150](https://github.com/nodejs/node/pull/27150)
- [[`25df3c10f4`](https://github.com/nodejs/node/commit/25df3c10f4)] - **build,win**: put all compilation artifacts into `out` (Refael Ackermann) [#27149](https://github.com/nodejs/node/pull/27149)
- [[`06c10cdc4c`](https://github.com/nodejs/node/commit/06c10cdc4c)] - **build,win**: teach GYP MSVS generator about MARMASM (Jon Kunkee) [#26020](https://github.com/nodejs/node/pull/26020)
- [[`2ffd20bb91`](https://github.com/nodejs/node/commit/2ffd20bb91)] - **build,win**: emit MSBuild summary (Refael Ackermann) [#25931](https://github.com/nodejs/node/pull/25931)
- [[`ff4adab78c`](https://github.com/nodejs/node/commit/ff4adab78c)] - **build,win**: always build with PCH (Refael Ackermann) [#25931](https://github.com/nodejs/node/pull/25931)
- [[`28e2c3771d`](https://github.com/nodejs/node/commit/28e2c3771d)] - **child_process**: rename \_validateStdtio to getValidStdio (Ruben Bridgewater) [#26809](https://github.com/nodejs/node/pull/26809)
- [[`091902ae00`](https://github.com/nodejs/node/commit/091902ae00)] - **console**: remove trace frame (Ruben Bridgewater) [#27159](https://github.com/nodejs/node/pull/27159)
- [[`a8eac78f8d`](https://github.com/nodejs/node/commit/a8eac78f8d)] - **_Revert_** "**console**: use consolePropAttributes for k-bind properties in constructor" (Daniel Bevenius) [#26943](https://github.com/nodejs/node/pull/26943)
- [[`ed5e69d7e6`](https://github.com/nodejs/node/commit/ed5e69d7e6)] - **console**: use consolePropAttributes for k-bind properties in constructor (Beni von Cheni) [#26850](https://github.com/nodejs/node/pull/26850)
- [[`69140bc7f8`](https://github.com/nodejs/node/commit/69140bc7f8)] - **crypto**: do not abort when setting throws (Sam Roberts) [#27157](https://github.com/nodejs/node/pull/27157)
- [[`0911e88056`](https://github.com/nodejs/node/commit/0911e88056)] - **crypto**: fix rsa key gen with non-default exponent (Sam Roberts) [#27092](https://github.com/nodejs/node/pull/27092)
- [[`fadcb2d850`](https://github.com/nodejs/node/commit/fadcb2d850)] - **crypto**: simplify missing passphrase detection (Tobias Nießen) [#27089](https://github.com/nodejs/node/pull/27089)
- [[`73bca57988`](https://github.com/nodejs/node/commit/73bca57988)] - **crypto**: fail early if passphrase is too long (Tobias Nießen) [#27010](https://github.com/nodejs/node/pull/27010)
- [[`05bd6071a6`](https://github.com/nodejs/node/commit/05bd6071a6)] - **crypto**: use EVP_PKEY_X448 in GetEphemeralKeyInfo (cjihrig) [#26988](https://github.com/nodejs/node/pull/26988)
- [[`6ac692a3db`](https://github.com/nodejs/node/commit/6ac692a3db)] - **crypto**: use EVP_PKEY_X25519 in GetEphemeralKeyInfo (cjihrig) [#26988](https://github.com/nodejs/node/pull/26988)
- [[`7c1fc93e30`](https://github.com/nodejs/node/commit/7c1fc93e30)] - **crypto**: don't crash on unknown asymmetricKeyType (Filip Skokan) [#26786](https://github.com/nodejs/node/pull/26786)
- [[`df1c9eb975`](https://github.com/nodejs/node/commit/df1c9eb975)] - **crypto**: rename generateKeyPairEdDSA (Tobias Nießen) [#26900](https://github.com/nodejs/node/pull/26900)
- [[`751c92d972`](https://github.com/nodejs/node/commit/751c92d972)] - **crypto**: remove obsolete encoding check (Ruben Bridgewater) [#26809](https://github.com/nodejs/node/pull/26809)
- [[`6f77af541e`](https://github.com/nodejs/node/commit/6f77af541e)] - **_Revert_** "**crypto**: add KeyObject.asymmetricKeySize" (Tobias Nießen) [#26636](https://github.com/nodejs/node/pull/26636)
- [[`247c14c040`](https://github.com/nodejs/node/commit/247c14c040)] - **crypto**: fix EdDSA support for KeyObject (Brian White) [#26319](https://github.com/nodejs/node/pull/26319)
- [[`90cf2d5f00`](https://github.com/nodejs/node/commit/90cf2d5f00)] - **deps**: use nghttp2's config.h on all platforms (Sam Roberts) [#27283](https://github.com/nodejs/node/pull/27283)
- [[`aec2ce4ee1`](https://github.com/nodejs/node/commit/aec2ce4ee1)] - **deps**: upgrade to libuv 1.28.0 (cjihrig) [#27241](https://github.com/nodejs/node/pull/27241)
- [[`7f29117de3`](https://github.com/nodejs/node/commit/7f29117de3)] - **deps**: patch V8 to 7.4.288.21 (Matheus Marchini) [#27265](https://github.com/nodejs/node/pull/27265)
- [[`033f6b566e`](https://github.com/nodejs/node/commit/033f6b566e)] - **deps**: upgrade npm to 6.9.0 (Kat Marchán) [#26244](https://github.com/nodejs/node/pull/26244)
- [[`135b79a31d`](https://github.com/nodejs/node/commit/135b79a31d)] - **deps**: patch V8 to 7.4.288.18 (Michaël Zasso) [#27066](https://github.com/nodejs/node/pull/27066)
- [[`c1d61f2b4b`](https://github.com/nodejs/node/commit/c1d61f2b4b)] - **deps**: patch V8 to 7.4.288.17 (Michaël Zasso) [#27066](https://github.com/nodejs/node/pull/27066)
- [[`5b8434eebc`](https://github.com/nodejs/node/commit/5b8434eebc)] - **deps**: V8: cherry-pick 0188634 (Michaël Zasso) [#27013](https://github.com/nodejs/node/pull/27013)
- [[`8cc181c8ee`](https://github.com/nodejs/node/commit/8cc181c8ee)] - **deps**: V8: cherry-pick c8785d1 (Michaël Zasso) [#27013](https://github.com/nodejs/node/pull/27013)
- [[`2ea9de2e85`](https://github.com/nodejs/node/commit/2ea9de2e85)] - **deps**: V8: cherry-pick f4b860d (Michaël Zasso) [#27013](https://github.com/nodejs/node/pull/27013)
- [[`ddbb7d7777`](https://github.com/nodejs/node/commit/ddbb7d7777)] - **deps**: cherry-pick 56f6a76 from upstream V8 (Ruben Bridgewater) [#25269](https://github.com/nodejs/node/pull/25269)
- [[`59fa7f1257`](https://github.com/nodejs/node/commit/59fa7f1257)] - **deps**: cherry-pick 26b145a from upstream V8 (Sam Roberts) [#25148](https://github.com/nodejs/node/pull/25148)
- [[`a9812142ca`](https://github.com/nodejs/node/commit/a9812142ca)] - **deps**: patch V8 to 7.1.302.33 (Ruben Bridgewater) [#25101](https://github.com/nodejs/node/pull/25101)
- [[`f0e460968e`](https://github.com/nodejs/node/commit/f0e460968e)] - **deps**: remove test-related GYP files (Michaël Zasso) [#25097](https://github.com/nodejs/node/pull/25097)
- [[`323a365766`](https://github.com/nodejs/node/commit/323a365766)] - **deps**: float 26d7fce1 from openssl (Rod Vagg) [#24353](https://github.com/nodejs/node/pull/24353)
- [[`d8fb81fab3`](https://github.com/nodejs/node/commit/d8fb81fab3)] - **deps**: float 99540ec from openssl (CVE-2018-0735) (Rod Vagg) [#23950](https://github.com/nodejs/node/pull/23950)
- [[`213c7d2d64`](https://github.com/nodejs/node/commit/213c7d2d64)] - **deps**: float a9cfb8c2 from openssl (CVE-2018-0734) (Rod Vagg) [#23965](https://github.com/nodejs/node/pull/23965)
- [[`e2260e901d`](https://github.com/nodejs/node/commit/e2260e901d)] - **deps**: float 415c3356 from openssl (DSA vulnerability) (Rod Vagg) [#23965](https://github.com/nodejs/node/pull/23965)
- [[`e356807a79`](https://github.com/nodejs/node/commit/e356807a79)] - **deps,test**: bump googletest to 39f72ea6f5 (Refael Ackermann) [#27231](https://github.com/nodejs/node/pull/27231)
- [[`8e308e8b28`](https://github.com/nodejs/node/commit/8e308e8b28)] - **deps,v8**: cherry-pick 385aa80 (Refael Ackermann) [#26702](https://github.com/nodejs/node/pull/26702)
- [[`d1b7193428`](https://github.com/nodejs/node/commit/d1b7193428)] - **deps,v8**: silence V8 self-deprecation warnings (Refael Ackermann) [#25394](https://github.com/nodejs/node/pull/25394)
- [[`9e960175d1`](https://github.com/nodejs/node/commit/9e960175d1)] - **dgram**: add support for UDP connected sockets (Santiago Gimeno) [#26871](https://github.com/nodejs/node/pull/26871)
- [[`09cdc37824`](https://github.com/nodejs/node/commit/09cdc37824)] - **dns**: do not indicate invalid IPs are IPv6 (Rich Trott) [#27081](https://github.com/nodejs/node/pull/27081)
- [[`bc2d258a3e`](https://github.com/nodejs/node/commit/bc2d258a3e)] - **dns**: refactor internal/dns/promises.js (Rich Trott) [#27081](https://github.com/nodejs/node/pull/27081)
- [[`72308a5deb`](https://github.com/nodejs/node/commit/72308a5deb)] - **doc**: simplify nomination process text (Rich Trott) [#27317](https://github.com/nodejs/node/pull/27317)
- [[`290faec0e7`](https://github.com/nodejs/node/commit/290faec0e7)] - **doc**: fix extname with the correct description (himself65) [#27303](https://github.com/nodejs/node/pull/27303)
- [[`d4dae5e1ca`](https://github.com/nodejs/node/commit/d4dae5e1ca)] - **doc**: simplify bullet points in GOVERNANCE.md (Rich Trott) [#27284](https://github.com/nodejs/node/pull/27284)
- [[`ba74e42000`](https://github.com/nodejs/node/commit/ba74e42000)] - **doc**: revise Collaborator Nominations introduction (Rich Trott) [#27237](https://github.com/nodejs/node/pull/27237)
- [[`c61c722c8c`](https://github.com/nodejs/node/commit/c61c722c8c)] - **doc**: add ABI version registry (Rod Vagg) [#24114](https://github.com/nodejs/node/pull/24114)
- [[`7938238b31`](https://github.com/nodejs/node/commit/7938238b31)] - **doc**: add internal documentation (Aymen Naghmouchi) [#26665](https://github.com/nodejs/node/pull/26665)
- [[`82e6c3378f`](https://github.com/nodejs/node/commit/82e6c3378f)] - **doc**: revise TSC Meetings material in GOVERNANCE.md (Rich Trott) [#27204](https://github.com/nodejs/node/pull/27204)
- [[`d5f9cf81e3`](https://github.com/nodejs/node/commit/d5f9cf81e3)] - **doc**: fix some links (Vse Mozhet Byt) [#27141](https://github.com/nodejs/node/pull/27141)
- [[`7b854959e7`](https://github.com/nodejs/node/commit/7b854959e7)] - **doc**: revise TSC text in GOVERNANCE.md (Rich Trott) [#27169](https://github.com/nodejs/node/pull/27169)
- [[`9b859f50d5`](https://github.com/nodejs/node/commit/9b859f50d5)] - **doc**: add missing n-api version indicator (Michael Dawson) [#27155](https://github.com/nodejs/node/pull/27155)
- [[`41d5666aaa`](https://github.com/nodejs/node/commit/41d5666aaa)] - **doc**: consolidate Collaborator status in GOVERNANCE (Rich Trott) [#27128](https://github.com/nodejs/node/pull/27128)
- [[`1656cd2edb`](https://github.com/nodejs/node/commit/1656cd2edb)] - **doc**: remove outdated link (cjihrig) [#27126](https://github.com/nodejs/node/pull/27126)
- [[`643a2fa447`](https://github.com/nodejs/node/commit/643a2fa447)] - **doc**: specify return type for tty.isatty() (Mykola Bilochub) [#27154](https://github.com/nodejs/node/pull/27154)
- [[`557bd861aa`](https://github.com/nodejs/node/commit/557bd861aa)] - **doc**: revise Collaborator material in GOVERNANCE.md (Rich Trott) [#27103](https://github.com/nodejs/node/pull/27103)
- [[`1afec97130`](https://github.com/nodejs/node/commit/1afec97130)] - **doc**: link bigint type to MDN instead of proposal (Vse Mozhet Byt) [#27101](https://github.com/nodejs/node/pull/27101)
- [[`21b739fb69`](https://github.com/nodejs/node/commit/21b739fb69)] - **doc**: add missing step to npm release process (Myles Borins) [#27105](https://github.com/nodejs/node/pull/27105)
- [[`181052d7c2`](https://github.com/nodejs/node/commit/181052d7c2)] - **doc**: revise Collaborator description in GOVERNANCE.md (Rich Trott) [#27071](https://github.com/nodejs/node/pull/27071)
- [[`10eaf6a09f`](https://github.com/nodejs/node/commit/10eaf6a09f)] - **doc**: fix section sorting, add link reference (Vse Mozhet Byt) [#27075](https://github.com/nodejs/node/pull/27075)
- [[`d989e20717`](https://github.com/nodejs/node/commit/d989e20717)] - **doc**: describe tls.DEFAULT_MIN_VERSION/\_MAX_VERSION (Sam Roberts) [#26821](https://github.com/nodejs/node/pull/26821)
- [[`0622ce6e7f`](https://github.com/nodejs/node/commit/0622ce6e7f)] - **doc**: fix changelog date typo (Jesse McCarthy) [#26831](https://github.com/nodejs/node/pull/26831)
- [[`cd9898a52a`](https://github.com/nodejs/node/commit/cd9898a52a)] - **doc**: add missing pr-url (cjihrig) [#26753](https://github.com/nodejs/node/pull/26753)
- [[`06879aafee`](https://github.com/nodejs/node/commit/06879aafee)] - **doc**: fix year in changelog (Colin Prince) [#26584](https://github.com/nodejs/node/pull/26584)
- [[`7e0ddf66b9`](https://github.com/nodejs/node/commit/7e0ddf66b9)] - **doc**: fix deprecation "End-of-Life" capitalization (Tobias Nießen) [#26251](https://github.com/nodejs/node/pull/26251)
- [[`3d4db3a7bf`](https://github.com/nodejs/node/commit/3d4db3a7bf)] - **doc**: fix metadata of DEP0114 (Tobias Nießen) [#26250](https://github.com/nodejs/node/pull/26250)
- [[`ccf37b3a84`](https://github.com/nodejs/node/commit/ccf37b3a84)] - **doc**: fix deprecations metadata (Richard Lau) [#25434](https://github.com/nodejs/node/pull/25434)
- [[`3614157b78`](https://github.com/nodejs/node/commit/3614157b78)] - **doc**: fix lint in CHANGELOG_V6 (Myles Borins) [#25233](https://github.com/nodejs/node/pull/25233)
- [[`928f776385`](https://github.com/nodejs/node/commit/928f776385)] - **doc**: add missing pr-url (cjihrig) [#25091](https://github.com/nodejs/node/pull/25091)
- [[`43273262e5`](https://github.com/nodejs/node/commit/43273262e5)] - **doc**: describe secureProtocol and CLI interaction (Sam Roberts) [#24386](https://github.com/nodejs/node/pull/24386)
- [[`34eccb2a1b`](https://github.com/nodejs/node/commit/34eccb2a1b)] - **doc**: fix missing PR id of 23329 (Ouyang Yadong) [#24458](https://github.com/nodejs/node/pull/24458)
- [[`db2ac1dbd9`](https://github.com/nodejs/node/commit/db2ac1dbd9)] - **doc**: fix headings for CHANGELOG_v10.md (Myles Borins) [#23973](https://github.com/nodejs/node/pull/23973)
- [[`c99026bdd7`](https://github.com/nodejs/node/commit/c99026bdd7)] - **doc**: update missing deprecation (cjihrig) [#23883](https://github.com/nodejs/node/pull/23883)
- [[`4afd503465`](https://github.com/nodejs/node/commit/4afd503465)] - **doc,test,repl**: fix deprecation code (cjihrig) [#26368](https://github.com/nodejs/node/pull/26368)
- [[`3b044962c4`](https://github.com/nodejs/node/commit/3b044962c4)] - **errors**: add more information in case of invalid callbacks (Ruben Bridgewater) [#27048](https://github.com/nodejs/node/pull/27048)
- [[`96e46d37c4`](https://github.com/nodejs/node/commit/96e46d37c4)] - **esm**: replace --entry-type with --input-type (Geoffrey Booth) [#27184](https://github.com/nodejs/node/pull/27184)
- [[`5e98f875b9`](https://github.com/nodejs/node/commit/5e98f875b9)] - **esm**: fix typos (Geoffrey Booth) [#27067](https://github.com/nodejs/node/pull/27067)
- [[`7a547098d5`](https://github.com/nodejs/node/commit/7a547098d5)] - **esm**: use primordials (Myles Borins) [#26954](https://github.com/nodejs/node/pull/26954)
- [[`2400cbcf7c`](https://github.com/nodejs/node/commit/2400cbcf7c)] - **fs**: fix infinite loop with async recursive mkdir on Windows (Richard Lau) [#27207](https://github.com/nodejs/node/pull/27207)
- [[`b925379f50`](https://github.com/nodejs/node/commit/b925379f50)] - **fs**: warn on non-portable mkdtemp() templates (cjihrig) [#26980](https://github.com/nodejs/node/pull/26980)
- [[`eb2d4161f5`](https://github.com/nodejs/node/commit/eb2d4161f5)] - **fs**: improve readFile performance (Ruben Bridgewater) [#27063](https://github.com/nodejs/node/pull/27063)
- [[`92db780d9e`](https://github.com/nodejs/node/commit/92db780d9e)] - **http2**: rename function for clarity (Ruben Bridgewater) [#26809](https://github.com/nodejs/node/pull/26809)
- [[`ce265908eb`](https://github.com/nodejs/node/commit/ce265908eb)] - **http2**: remove side effects from validateSettings (Ruben Bridgewater) [#26809](https://github.com/nodejs/node/pull/26809)
- [[`cd3a9eebca`](https://github.com/nodejs/node/commit/cd3a9eebca)] - **https**: remove usage of public require('util') (dnlup) [#26772](https://github.com/nodejs/node/pull/26772)
- [[`49d3d11ba7`](https://github.com/nodejs/node/commit/49d3d11ba7)] - **inspector**: split --cpu-prof-path to --cpu-prof-dir and --cpu-prof-name (Joyee Cheung) [#27306](https://github.com/nodejs/node/pull/27306)
- [[`94adfe9831`](https://github.com/nodejs/node/commit/94adfe9831)] - **lib**: replace --diagnostic-report-\* with --report-\* (Joyee Cheung) [#27312](https://github.com/nodejs/node/pull/27312)
- [[`49ee010005`](https://github.com/nodejs/node/commit/49ee010005)] - **lib**: use getOptionValue instead of process underscore aliases (Joyee Cheung) [#27278](https://github.com/nodejs/node/pull/27278)
- [[`a38e9c438a`](https://github.com/nodejs/node/commit/a38e9c438a)] - **lib**: require globals instead of using the global proxy (Joyee Cheung) [#27112](https://github.com/nodejs/node/pull/27112)
- [[`914d6c9ab8`](https://github.com/nodejs/node/commit/914d6c9ab8)] - **lib**: use primordials in domexception.js (Joyee Cheung) [#27171](https://github.com/nodejs/node/pull/27171)
- [[`3da36d0e94`](https://github.com/nodejs/node/commit/3da36d0e94)] - **lib**: create primordials in every context (Joyee Cheung) [#27171](https://github.com/nodejs/node/pull/27171)
- [[`908292cf1f`](https://github.com/nodejs/node/commit/908292cf1f)] - **lib**: enforce the use of Object from primordials (Michaël Zasso) [#27146](https://github.com/nodejs/node/pull/27146)
- [[`47f5cc1ad1`](https://github.com/nodejs/node/commit/47f5cc1ad1)] - **lib**: faster FreeList (Anatoli Papirovski) [#27021](https://github.com/nodejs/node/pull/27021)
- [[`5b9e57012a`](https://github.com/nodejs/node/commit/5b9e57012a)] - **lib**: add signal name validator (cjihrig) [#27137](https://github.com/nodejs/node/pull/27137)
- [[`112cc7c275`](https://github.com/nodejs/node/commit/112cc7c275)] - **lib**: use safe methods from primordials (Michaël Zasso) [#27096](https://github.com/nodejs/node/pull/27096)
- [[`5a8c55f078`](https://github.com/nodejs/node/commit/5a8c55f078)] - **lib**: fix outdated comment (Vse Mozhet Byt) [#27122](https://github.com/nodejs/node/pull/27122)
- [[`de23055536`](https://github.com/nodejs/node/commit/de23055536)] - **lib**: remove `env: node` in eslint config for lib files (Joyee Cheung) [#27082](https://github.com/nodejs/node/pull/27082)
- [[`2c49e8b537`](https://github.com/nodejs/node/commit/2c49e8b537)] - **lib**: make queueMicrotask faster (Anatoli Papirovski) [#27032](https://github.com/nodejs/node/pull/27032)
- [[`0817840f77`](https://github.com/nodejs/node/commit/0817840f77)] - **lib**: force using primordials for JSON, Math and Reflect (Michaël Zasso) [#27027](https://github.com/nodejs/node/pull/27027)
- [[`7bddfcc61a`](https://github.com/nodejs/node/commit/7bddfcc61a)] - **lib**: consolidate arrayBufferView validation (Ruben Bridgewater) [#26809](https://github.com/nodejs/node/pull/26809)
- [[`6c913fb028`](https://github.com/nodejs/node/commit/6c913fb028)] - **lib**: remove return values from validation functions (Ruben Bridgewater) [#26809](https://github.com/nodejs/node/pull/26809)
- [[`50a3fe20ea`](https://github.com/nodejs/node/commit/50a3fe20ea)] - **lib**: rename validateMode to parseMode (Ruben Bridgewater) [#26809](https://github.com/nodejs/node/pull/26809)
- [[`76e67e9884`](https://github.com/nodejs/node/commit/76e67e9884)] - **lib**: assign missed deprecation code (Anna Henningsen) [#26492](https://github.com/nodejs/node/pull/26492)
- [[`f3b5cc0807`](https://github.com/nodejs/node/commit/f3b5cc0807)] - **meta**: travis: run compilation jobs first (Refael Ackermann) [#27205](https://github.com/nodejs/node/pull/27205)
- [[`7c816b7588`](https://github.com/nodejs/node/commit/7c816b7588)] - **module**: explicitly initialize CJS loader (Joyee Cheung) [#27313](https://github.com/nodejs/node/pull/27313)
- [[`d6317d0a45`](https://github.com/nodejs/node/commit/d6317d0a45)] - **module**: remove usage of require('util') (dnlup) [#26803](https://github.com/nodejs/node/pull/26803)
- [[`ff89670902`](https://github.com/nodejs/node/commit/ff89670902)] - **n-api**: reduce gc finalization stress (Michael Dawson) [#27085](https://github.com/nodejs/node/pull/27085)
- [[`655c90b287`](https://github.com/nodejs/node/commit/655c90b287)] - **net**: inline maybeDestroy() (Luigi Pinca) [#27136](https://github.com/nodejs/node/pull/27136)
- [[`f0b3855a90`](https://github.com/nodejs/node/commit/f0b3855a90)] - **net**: remove usage of require('util') (dnlup) [#26920](https://github.com/nodejs/node/pull/26920)
- [[`9946c59707`](https://github.com/nodejs/node/commit/9946c59707)] - **path**: simplify normalizeString (Ruben Bridgewater) [#27240](https://github.com/nodejs/node/pull/27240)
- [[`9dba96dc1a`](https://github.com/nodejs/node/commit/9dba96dc1a)] - **process**: patch more process properties during pre-execution (Joyee Cheung) [#26945](https://github.com/nodejs/node/pull/26945)
- [[`d4eda4d876`](https://github.com/nodejs/node/commit/d4eda4d876)] - **process**: remove protection for SyncWriteStream destroy in stdio (Matteo Collina) [#26902](https://github.com/nodejs/node/pull/26902)
- [[`2701f5538f`](https://github.com/nodejs/node/commit/2701f5538f)] - **readline**: remove usage of require('util') (dnlup) [#26818](https://github.com/nodejs/node/pull/26818)
- [[`415a825dc0`](https://github.com/nodejs/node/commit/415a825dc0)] - **repl**: remove usage of require('util') in `repl.js` (dnlup) [#26820](https://github.com/nodejs/node/pull/26820)
- [[`af35d4044f`](https://github.com/nodejs/node/commit/af35d4044f)] - **report**: use uv_gettimeofday for dumpEventTimeStamp (cjihrig) [#27029](https://github.com/nodejs/node/pull/27029)
- [[`44a3acb627`](https://github.com/nodejs/node/commit/44a3acb627)] - **report**: improve signal name validation (cjihrig) [#27137](https://github.com/nodejs/node/pull/27137)
- [[`e3032708e0`](https://github.com/nodejs/node/commit/e3032708e0)] - **report**: add support for UDP connected sockets (Richard Lau) [#27072](https://github.com/nodejs/node/pull/27072)
- [[`8e1e9946a9`](https://github.com/nodejs/node/commit/8e1e9946a9)] - **src**: use uv_gettimeofday() to get microseconds (cjihrig) [#27029](https://github.com/nodejs/node/pull/27029)
- [[`8eaf31181a`](https://github.com/nodejs/node/commit/8eaf31181a)] - **src**: apply modernize-use-nullptr in node_win32_etw_provider.cc (gengjiawen) [#27263](https://github.com/nodejs/node/pull/27263)
- [[`19e3e02a2d`](https://github.com/nodejs/node/commit/19e3e02a2d)] - **src**: move SIGINT watchdog utils to the contextify binding (Joyee Cheung) [#27290](https://github.com/nodejs/node/pull/27290)
- [[`5356b4a675`](https://github.com/nodejs/node/commit/5356b4a675)] - **src**: split per-process initialization and teardown routines (Joyee Cheung) [#27276](https://github.com/nodejs/node/pull/27276)
- [[`8d901bb44e`](https://github.com/nodejs/node/commit/8d901bb44e)] - **src**: move guessHandleType in the util binding (Joyee Cheung) [#27289](https://github.com/nodejs/node/pull/27289)
- [[`758191033f`](https://github.com/nodejs/node/commit/758191033f)] - **src**: fix performance-faster-string-find in node_report.cc (gengjiawen) [#27262](https://github.com/nodejs/node/pull/27262)
- [[`dc8b57fdc1`](https://github.com/nodejs/node/commit/dc8b57fdc1)] - **src**: use ArrayBufferAllocator::Create in node_worker.cc (Anna Henningsen) [#27251](https://github.com/nodejs/node/pull/27251)
- [[`f9da3f0cce`](https://github.com/nodejs/node/commit/f9da3f0cce)] - **src**: enable non-nestable V8 platform tasks (Anna Henningsen) [#27252](https://github.com/nodejs/node/pull/27252)
- [[`3ef1512f9e`](https://github.com/nodejs/node/commit/3ef1512f9e)] - **src**: allows escaping NODE_OPTIONS with backslashes (Maël Nison) [#24065](https://github.com/nodejs/node/pull/24065)
- [[`cdba9f23ec`](https://github.com/nodejs/node/commit/cdba9f23ec)] - **src**: handle fatal error when Environment is not assigned to context (Joyee Cheung) [#27236](https://github.com/nodejs/node/pull/27236)
- [[`83d1ca7de9`](https://github.com/nodejs/node/commit/83d1ca7de9)] - **src**: disallow calling env-dependent methods during bootstrap (Joyee Cheung) [#27234](https://github.com/nodejs/node/pull/27234)
- [[`cab1dc5bb3`](https://github.com/nodejs/node/commit/cab1dc5bb3)] - **src**: use RAII to manage the main isolate data (Joyee Cheung) [#27220](https://github.com/nodejs/node/pull/27220)
- [[`1e7823dd4e`](https://github.com/nodejs/node/commit/1e7823dd4e)] - **src**: remove redundant call in node_options-inl.h (gengjiawen) [#26959](https://github.com/nodejs/node/pull/26959)
- [[`73471236d8`](https://github.com/nodejs/node/commit/73471236d8)] - **src**: remove unimplemented method in TracingAgent (gengjiawen) [#26959](https://github.com/nodejs/node/pull/26959)
- [[`427fce711f`](https://github.com/nodejs/node/commit/427fce711f)] - **src**: fix check for accepting Buffers into Node’s allocator (Anna Henningsen) [#27174](https://github.com/nodejs/node/pull/27174)
- [[`dfd7e99425`](https://github.com/nodejs/node/commit/dfd7e99425)] - **src**: make a Environment-independent proxy class for NativeModuleLoader (Joyee Cheung) [#27160](https://github.com/nodejs/node/pull/27160)
- [[`060d901f87`](https://github.com/nodejs/node/commit/060d901f87)] - **src**: replace FromJust() with Check() when possible (Sam Roberts) [#27162](https://github.com/nodejs/node/pull/27162)
- [[`ee7daf76c0`](https://github.com/nodejs/node/commit/ee7daf76c0)] - **src**: remove redundant string initialization (gengjiawen) [#27152](https://github.com/nodejs/node/pull/27152)
- [[`845a6214f8`](https://github.com/nodejs/node/commit/845a6214f8)] - **src**: use macro instead of magic number for fd (gengjiawen) [#27152](https://github.com/nodejs/node/pull/27152)
- [[`547576f530`](https://github.com/nodejs/node/commit/547576f530)] - **src**: always use diagnostic file sequence number (cjihrig) [#27142](https://github.com/nodejs/node/pull/27142)
- [[`c1e03eda07`](https://github.com/nodejs/node/commit/c1e03eda07)] - **src**: use SealHandleScope for inspector tasks (Anna Henningsen) [#27116](https://github.com/nodejs/node/pull/27116)
- [[`a3f30a48c2`](https://github.com/nodejs/node/commit/a3f30a48c2)] - **src**: unify crypto constant setup (Sam Roberts) [#27077](https://github.com/nodejs/node/pull/27077)
- [[`97c0a34935`](https://github.com/nodejs/node/commit/97c0a34935)] - **src**: don't point to out of scope variable (cjihrig) [#27070](https://github.com/nodejs/node/pull/27070)
- [[`864860e9f3`](https://github.com/nodejs/node/commit/864860e9f3)] - **src**: port coverage serialization to C++ (Joyee Cheung) [#26874](https://github.com/nodejs/node/pull/26874)
- [[`d0e2650d03`](https://github.com/nodejs/node/commit/d0e2650d03)] - **src**: add NOLINT to js_native\_.\* (gengjiawen) [#26884](https://github.com/nodejs/node/pull/26884)
- [[`eb2dccb17a`](https://github.com/nodejs/node/commit/eb2dccb17a)] - **src**: move AsyncResource impl out of public header (Ben Noordhuis) [#26348](https://github.com/nodejs/node/pull/26348)
- [[`e1d55a0cbc`](https://github.com/nodejs/node/commit/e1d55a0cbc)] - **src**: port bootstrap/cache.js to C++ (Joyee Cheung) [#27046](https://github.com/nodejs/node/pull/27046)
- [[`f59ec2abee`](https://github.com/nodejs/node/commit/f59ec2abee)] - **src**: implement MemoryRetainer in Environment (Joyee Cheung) [#27018](https://github.com/nodejs/node/pull/27018)
- [[`1087805eeb`](https://github.com/nodejs/node/commit/1087805eeb)] - **src**: check return value, silence coverity warning (Ben Noordhuis) [#26997](https://github.com/nodejs/node/pull/26997)
- [[`bb98f27181`](https://github.com/nodejs/node/commit/bb98f27181)] - **src**: check uv_fs_close() return value (cjihrig) [#26967](https://github.com/nodejs/node/pull/26967)
- [[`8bc7d2a5be`](https://github.com/nodejs/node/commit/8bc7d2a5be)] - **src**: fix data type when using uv_get_total_memory() (gengjiawen) [#26886](https://github.com/nodejs/node/pull/26886)
- [[`c0f031c5bd`](https://github.com/nodejs/node/commit/c0f031c5bd)] - **src**: remove unused variable (cjihrig) [#26879](https://github.com/nodejs/node/pull/26879)
- [[`1935625df4`](https://github.com/nodejs/node/commit/1935625df4)] - **src**: disallow constructor behaviour for native methods (Anna Henningsen) [#26700](https://github.com/nodejs/node/pull/26700)
- [[`f091d4e840`](https://github.com/nodejs/node/commit/f091d4e840)] - **src**: apply clang-tidy rule modernize-use-emplace (gengjiawen) [#26564](https://github.com/nodejs/node/pull/26564)
- [[`f47adfbda5`](https://github.com/nodejs/node/commit/f47adfbda5)] - **src**: fix DTrace GC callbacks DCHECKs and add cleanup (Joyee Cheung) [#26742](https://github.com/nodejs/node/pull/26742)
- [[`0752a18b88`](https://github.com/nodejs/node/commit/0752a18b88)] - **src**: fix warning in node_messaging (ZYSzys) [#26682](https://github.com/nodejs/node/pull/26682)
- [[`b200a46bef`](https://github.com/nodejs/node/commit/b200a46bef)] - **src**: remove `process.binding('config').debugOptions` (Joyee Cheung) [#25999](https://github.com/nodejs/node/pull/25999)
- [[`c2d374fccc`](https://github.com/nodejs/node/commit/c2d374fccc)] - **src**: remove unused method in env.h (gengjiawen) [#25934](https://github.com/nodejs/node/pull/25934)
- [[`55569759b3`](https://github.com/nodejs/node/commit/55569759b3)] - **src**: pass along errors from PromiseWrap instantiation (Anna Henningsen) [#25734](https://github.com/nodejs/node/pull/25734)
- [[`24e6b709ea`](https://github.com/nodejs/node/commit/24e6b709ea)] - **src**: use isolate version of BooleanValue() (cjihrig) [#24883](https://github.com/nodejs/node/pull/24883)
- [[`b0089a580f`](https://github.com/nodejs/node/commit/b0089a580f)] - **src**: make model counter in `GetCPUInfo()` unsigned (Anna Henningsen) [#23880](https://github.com/nodejs/node/pull/23880)
- [[`53e0f632db`](https://github.com/nodejs/node/commit/53e0f632db)] - **stream**: inline onwriteStateUpdate() (Luigi Pinca) [#27203](https://github.com/nodejs/node/pull/27203)
- [[`1a67c9948c`](https://github.com/nodejs/node/commit/1a67c9948c)] - **stream**: remove dead code (Marcos Casagrande) [#27125](https://github.com/nodejs/node/pull/27125)
- [[`a3d1922958`](https://github.com/nodejs/node/commit/a3d1922958)] - **test**: unskip copyfile permission test (cjihrig) [#27241](https://github.com/nodejs/node/pull/27241)
- [[`b368571fba`](https://github.com/nodejs/node/commit/b368571fba)] - **test**: move known issue test to parallel (cjihrig) [#27241](https://github.com/nodejs/node/pull/27241)
- [[`528d100394`](https://github.com/nodejs/node/commit/528d100394)] - **test**: mark some known flakes (Refael Ackermann) [#27225](https://github.com/nodejs/node/pull/27225)
- [[`e37eee2b1e`](https://github.com/nodejs/node/commit/e37eee2b1e)] - **test**: remove flaky designation for test-cli-node-options (Rich Trott) [#27305](https://github.com/nodejs/node/pull/27305)
- [[`7167eb2f12`](https://github.com/nodejs/node/commit/7167eb2f12)] - **test**: increase coverage for dns.promises.lookup() (Rich Trott) [#27299](https://github.com/nodejs/node/pull/27299)
- [[`b66f01d903`](https://github.com/nodejs/node/commit/b66f01d903)] - **test**: skip test-cpu-prof in debug builds with code cache (Joyee Cheung) [#27308](https://github.com/nodejs/node/pull/27308)
- [[`57ab3b56fc`](https://github.com/nodejs/node/commit/57ab3b56fc)] - **test**: allow leaked global check to be skipped (cjihrig) [#27239](https://github.com/nodejs/node/pull/27239)
- [[`02885dad5a`](https://github.com/nodejs/node/commit/02885dad5a)] - **test**: add ability to skip common flag checks (Anna Henningsen) [#27254](https://github.com/nodejs/node/pull/27254)
- [[`ed893111b9`](https://github.com/nodejs/node/commit/ed893111b9)] - **test**: do not strip left whitespace in pseudo-tty tests (Ruben Bridgewater) [#27244](https://github.com/nodejs/node/pull/27244)
- [[`8712edf53a`](https://github.com/nodejs/node/commit/8712edf53a)] - **test**: fix postmortem metadata test (Matheus Marchini) [#27265](https://github.com/nodejs/node/pull/27265)
- [[`d5bb500d0f`](https://github.com/nodejs/node/commit/d5bb500d0f)] - **test**: fix test-benchmark-buffer (Rich Trott) [#27260](https://github.com/nodejs/node/pull/27260)
- [[`4f8b497991`](https://github.com/nodejs/node/commit/4f8b497991)] - **test**: try to stabalize test-child-process-fork-exec-path.js (Refael Ackermann) [#27277](https://github.com/nodejs/node/pull/27277)
- [[`c6c37e9e85`](https://github.com/nodejs/node/commit/c6c37e9e85)] - **test**: use assert.rejects() and assert.throws() (Richard Lau) [#27207](https://github.com/nodejs/node/pull/27207)
- [[`f85ef977e6`](https://github.com/nodejs/node/commit/f85ef977e6)] - **test**: log errors in test-fs-readfile-tostring-fail (Richard Lau) [#27058](https://github.com/nodejs/node/pull/27058)
- [[`de463f1490`](https://github.com/nodejs/node/commit/de463f1490)] - **test**: ec2 generateKeyPairSync invalid parameter encoding (Ruwan Geeganage) [#27212](https://github.com/nodejs/node/pull/27212)
- [[`2fed83dee8`](https://github.com/nodejs/node/commit/2fed83dee8)] - **test**: test privateEncrypt/publicDecrypt + padding (Ben Noordhuis) [#27188](https://github.com/nodejs/node/pull/27188)
- [[`f6bd3b27ee`](https://github.com/nodejs/node/commit/f6bd3b27ee)] - **test**: fix test-dns-idna2008.js (Rich Trott) [#27208](https://github.com/nodejs/node/pull/27208)
- [[`8cf3af1486`](https://github.com/nodejs/node/commit/8cf3af1486)] - **test**: optimize total Travis run time (Refael Ackermann) [#27182](https://github.com/nodejs/node/pull/27182)
- [[`abe4183d41`](https://github.com/nodejs/node/commit/abe4183d41)] - **test**: mark some known flakes (Refael Ackermann) [#27193](https://github.com/nodejs/node/pull/27193)
- [[`06c803d9b9`](https://github.com/nodejs/node/commit/06c803d9b9)] - **test**: pass null params to napi_create_function() (Octavian Soldea) [#26998](https://github.com/nodejs/node/pull/26998)
- [[`2a51ae424a`](https://github.com/nodejs/node/commit/2a51ae424a)] - **test**: cover thenables when we check for promises (szabolcsit) [#24219](https://github.com/nodejs/node/pull/24219)
- [[`3a6eba3de6`](https://github.com/nodejs/node/commit/3a6eba3de6)] - **test**: use assert.rejects (Ruben Bridgewater) [#27123](https://github.com/nodejs/node/pull/27123)
- [[`3d6533ea02`](https://github.com/nodejs/node/commit/3d6533ea02)] - **test**: simplify vm-module-errors test (Ruben Bridgewater) [#27123](https://github.com/nodejs/node/pull/27123)
- [[`d1413305e0`](https://github.com/nodejs/node/commit/d1413305e0)] - **test**: print child stderr in test-http-chunk-problem (Anna Henningsen) [#27117](https://github.com/nodejs/node/pull/27117)
- [[`f96a6608eb`](https://github.com/nodejs/node/commit/f96a6608eb)] - **test**: fix test-worker-memory.js for large cpu #s (Michael Dawson) [#27090](https://github.com/nodejs/node/pull/27090)
- [[`93df085386`](https://github.com/nodejs/node/commit/93df085386)] - **test**: fix this scope bug in test-stream2-writable.js (gengjiawen) [#27111](https://github.com/nodejs/node/pull/27111)
- [[`58aaf58406`](https://github.com/nodejs/node/commit/58aaf58406)] - **test**: fix test-repl-require-after-write (Michaël Zasso) [#27088](https://github.com/nodejs/node/pull/27088)
- [[`baa54a5ae7`](https://github.com/nodejs/node/commit/baa54a5ae7)] - **test**: cover napi_get/set/has_named_property() (Gabriel Schulhof) [#26947](https://github.com/nodejs/node/pull/26947)
- [[`c86883cfac`](https://github.com/nodejs/node/commit/c86883cfac)] - **test**: fix test-benchmark-module (Rich Trott) [#27094](https://github.com/nodejs/node/pull/27094)
- [[`f13733d12d`](https://github.com/nodejs/node/commit/f13733d12d)] - **test**: test vm.runInNewContext() filename option (Ben Noordhuis) [#27056](https://github.com/nodejs/node/pull/27056)
- [[`666c67e078`](https://github.com/nodejs/node/commit/666c67e078)] - **test**: simplify date inspection tests (Ruben Bridgewater) [#26922](https://github.com/nodejs/node/pull/26922)
- [[`1375af204a`](https://github.com/nodejs/node/commit/1375af204a)] - **test**: revert fail `coverage` target if tests fail" (Michael Dawson) [#25543](https://github.com/nodejs/node/pull/25543)
- [[`3235d318dc`](https://github.com/nodejs/node/commit/3235d318dc)] - **test**: add test for \_setSimultaneousAccepts() (Andrey Melikhov) [#24180](https://github.com/nodejs/node/pull/24180)
- [[`9e8c9be3ff`](https://github.com/nodejs/node/commit/9e8c9be3ff)] - **timers**: rename validateTimerDuration to getTimerDuration (Ruben Bridgewater) [#26809](https://github.com/nodejs/node/pull/26809)
- [[`a1d05e49fe`](https://github.com/nodejs/node/commit/a1d05e49fe)] - **timers**: support name in validateTimerDuration() (cjihrig) [#26215](https://github.com/nodejs/node/pull/26215)
- [[`2d5387e143`](https://github.com/nodejs/node/commit/2d5387e143)] - **tls**: add debugging to native TLS code (Anna Henningsen) [#26843](https://github.com/nodejs/node/pull/26843)
- [[`f87b3a72cd`](https://github.com/nodejs/node/commit/f87b3a72cd)] - **tls**: add CHECK for impossible condition (Anna Henningsen) [#26843](https://github.com/nodejs/node/pull/26843)
- [[`a1330af6a3`](https://github.com/nodejs/node/commit/a1330af6a3)] - **tls**: remove usage of public require('util') (dnlup) [#26747](https://github.com/nodejs/node/pull/26747)
- [[`00d49ad673`](https://github.com/nodejs/node/commit/00d49ad673)] - **tls**: null not valid as a renegotiate callback (Sam Roberts) [#25929](https://github.com/nodejs/node/pull/25929)
- [[`54b4beb506`](https://github.com/nodejs/node/commit/54b4beb506)] - **tls**: support TLS_client_method, TLS_server_method (Sam Roberts) [#24386](https://github.com/nodejs/node/pull/24386)
- [[`5ac0308af9`](https://github.com/nodejs/node/commit/5ac0308af9)] - **tools**: refactor mkcodecache (Refael Ackermann) [#27161](https://github.com/nodejs/node/pull/27161)
- [[`4fd7193579`](https://github.com/nodejs/node/commit/4fd7193579)] - **tools**: implement mkcodecache as an executable (Joyee Cheung) [#27161](https://github.com/nodejs/node/pull/27161)
- [[`d4e743169e`](https://github.com/nodejs/node/commit/d4e743169e)] - **tools**: update js-yaml to 3.13.1 for lint-md.js (Rich Trott) [#27195](https://github.com/nodejs/node/pull/27195)
- [[`1fc4255221`](https://github.com/nodejs/node/commit/1fc4255221)] - **tools**: python: ignore instead of select flake8 rules (Refael Ackermann) [#25614](https://github.com/nodejs/node/pull/25614)
- [[`a16a0fe962`](https://github.com/nodejs/node/commit/a16a0fe962)] - **tools**: python: activate more flake8 rules (Refael Ackermann) [#25614](https://github.com/nodejs/node/pull/25614)
- [[`0befda6970`](https://github.com/nodejs/node/commit/0befda6970)] - **tools**: python: update flake8 rules (Refael Ackermann) [#25614](https://github.com/nodejs/node/pull/25614)
- [[`0a25ace9c3`](https://github.com/nodejs/node/commit/0a25ace9c3)] - **tools**: move cpplint configuration to .cpplint (Refael Ackermann) [#27098](https://github.com/nodejs/node/pull/27098)
- [[`cd2987f83f`](https://github.com/nodejs/node/commit/cd2987f83f)] - **tools**: refloat 4 Node.js patches to cpplint.py (Refael Ackermann) [#27098](https://github.com/nodejs/node/pull/27098)
- [[`1302e0174a`](https://github.com/nodejs/node/commit/1302e0174a)] - **tools**: bump cpplint.py to 1.4.4 (Refael Ackermann) [#27098](https://github.com/nodejs/node/pull/27098)
- [[`dd89a1182f`](https://github.com/nodejs/node/commit/dd89a1182f)] - **tools**: print a better message for unexpected use of globals (Michaël Zasso) [#27083](https://github.com/nodejs/node/pull/27083)
- [[`39141426d4`](https://github.com/nodejs/node/commit/39141426d4)] - **tools**: update capitalize-comments eslint rule (Ruben Bridgewater) [#26849](https://github.com/nodejs/node/pull/26849)
- [[`964174e339`](https://github.com/nodejs/node/commit/964174e339)] - **tools,doc**: fix 404 broken links in docs (Gerson Niño) [#27168](https://github.com/nodejs/node/pull/27168)
- [[`bbfa93af3d`](https://github.com/nodejs/node/commit/bbfa93af3d)] - **url**: refactor validateHostname (Ruben Bridgewater) [#26809](https://github.com/nodejs/node/pull/26809)
- [[`2e4ceb5747`](https://github.com/nodejs/node/commit/2e4ceb5747)] - **util**: access process states lazily in debuglog (Joyee Cheung) [#27281](https://github.com/nodejs/node/pull/27281)
- [[`2948e96afd`](https://github.com/nodejs/node/commit/2948e96afd)] - **util**: fix wrong usage of Error.prepareStackTrace (Simon Zünd) [#27250](https://github.com/nodejs/node/pull/27250)
- [[`a9bf6652b5`](https://github.com/nodejs/node/commit/a9bf6652b5)] - **util**: use minimal object inspection with %s specifier (Ruben Bridgewater) [#26927](https://github.com/nodejs/node/pull/26927)
- [[`f7c96856f9`](https://github.com/nodejs/node/commit/f7c96856f9)] - **util**: improve error property inspection (Ruben Bridgewater) [#26984](https://github.com/nodejs/node/pull/26984)
- [[`14b2db0145`](https://github.com/nodejs/node/commit/14b2db0145)] - **util**: improve `inspect()` compact number mode (Ruben Bridgewater) [#26984](https://github.com/nodejs/node/pull/26984)
- [[`0f58ae392b`](https://github.com/nodejs/node/commit/0f58ae392b)] - **util**: `format()` now formats bigint and booleans (Ruben Bridgewater) [#25046](https://github.com/nodejs/node/pull/25046)
- [[`9752fce34d`](https://github.com/nodejs/node/commit/9752fce34d)] - **util**: improve format performance (Ruben Bridgewater) [#24981](https://github.com/nodejs/node/pull/24981)
- [[`e9fb92dc42`](https://github.com/nodejs/node/commit/e9fb92dc42)] - **vm**: remove require('util') from lib/vm/source_text_module.js (freestraws) [#27285](https://github.com/nodejs/node/pull/27285)
- [[`002dacb7f7`](https://github.com/nodejs/node/commit/002dacb7f7)] - **worker**: handle exception when creating execArgv errors (Anna Henningsen) [#27245](https://github.com/nodejs/node/pull/27245)
- [[`d070f5d965`](https://github.com/nodejs/node/commit/d070f5d965)] - **worker**: improve coverage (Ruben Bridgewater) [#27230](https://github.com/nodejs/node/pull/27230)
- [[`5450e48f69`](https://github.com/nodejs/node/commit/5450e48f69)] - **worker**: simplify filename checks (Ruben Bridgewater) [#27233](https://github.com/nodejs/node/pull/27233)

Windows 32-bit Installer: https://nodejs.org/dist/v12.0.0/node-v12.0.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v12.0.0/node-v12.0.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v12.0.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v12.0.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v12.0.0/node-v12.0.0.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v12.0.0/node-v12.0.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v12.0.0/node-v12.0.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v12.0.0/node-v12.0.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v12.0.0/node-v12.0.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v12.0.0/node-v12.0.0-aix-ppc64.tar.gz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v12.0.0/node-v12.0.0-sunos-x64.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v12.0.0/node-v12.0.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v12.0.0/node-v12.0.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v12.0.0/node-v12.0.0.tar.gz \
Other release files: https://nodejs.org/dist/v12.0.0/ \
Documentation: https://nodejs.org/docs/v12.0.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

84e9f3f274a89ff82f9f5890bc009a2d697bfca0312fb3dbc248212844bb7e20  node-v12.0.0-aix-ppc64.tar.gz
92c81a284e909424b50dd01e175260b75bbbdb487fdfe1885229817187ea76bc  node-v12.0.0-darwin-x64.tar.gz
e9669f62977504c9f8b683c044cc13cb31da01a0efd16d5ca7cd264ed6ad5ae5  node-v12.0.0-darwin-x64.tar.xz
f09ee18fc6f272a678c39696d064d057b009f9862b795f2b70eca20208a88039  node-v12.0.0-headers.tar.gz
831fb078e3f119c070f9ee355ca749f36dbf55d68edb1a90a05a43d7d6d7c1dc  node-v12.0.0-headers.tar.xz
835265539497708b4daf68175614fbe57ed21374f3717b4754971551a06c5efb  node-v12.0.0-linux-arm64.tar.gz
2ac36aa51f76654a8914d41a32bd5c3d3213cba14eea84abd9919d022b5694b4  node-v12.0.0-linux-arm64.tar.xz
46a32363a1aed09e52fb44daab9fbe621d9e0fdf1eb33955233dd5e1f133259a  node-v12.0.0-linux-armv7l.tar.gz
6d4432826e2b5efc35485b457b59d012cdb3c5a2641e02c7fd7d23f094feb525  node-v12.0.0-linux-armv7l.tar.xz
85e79f1282a6a0b048cc6370ca7427583ef05ce2e7f33b0323fd6d14fc1d4348  node-v12.0.0-linux-ppc64le.tar.gz
aeb012ae996ebc3ec070c285a861894727a18cdcd0e85e22165f02507fdf57cd  node-v12.0.0-linux-ppc64le.tar.xz
7c9f5328c3ac5fd428fc3cd1453621be85ef356898c5dd6f900cb48285432cd8  node-v12.0.0-linux-s390x.tar.gz
b0db545607686e61bf2e86322b30d394746bdb2b86c42797369aa06157b1f066  node-v12.0.0-linux-s390x.tar.xz
3268466af05464a7aa698df7cc8bbeec0b8728d8779e12130ef441bb730a8059  node-v12.0.0-linux-x64.tar.gz
7a5609167265954cfb912e4dc8f36e5469335b6be1bb11d60d8427f92c03f5f9  node-v12.0.0-linux-x64.tar.xz
cc8e8c49eeafa5294897c18ad985fa569e2bdd5265e2897a04056c57899d1133  node-v12.0.0.pkg
252b7172706a7b31d4e8468c0f51f8ed1e615d4acf1d286945ddf94ac195a9bf  node-v12.0.0-sunos-x64.tar.gz
de348c92711db33de6ad9def3a81d52249e0141f56567cf607b12d5a6f274b79  node-v12.0.0-sunos-x64.tar.xz
ef7a25d25370a0c618d50ea72f2e78b8777e015160694d7b7cac05188cc0db65  node-v12.0.0.tar.gz
93c48550e5da71cf3809ba765e8f8c01124a6b5f8b69869fe3ea8f168fcbfc78  node-v12.0.0.tar.xz
869949e29cf04e692bbebd81ab4dc8b29d1b80c60cc5c32622b975bf68dae0d3  node-v12.0.0-win-x64.7z
96ab5f9f61d75daf3fb1072930b6e9249f15b77bc3b3dca516ae8439dc1500fb  node-v12.0.0-win-x64.zip
0d9acb18baf204a1588c17abc319b98d75862b6d0930d792e4c2a1216c82dc98  node-v12.0.0-win-x86.7z
989e49c8ae3c106a800c5f54d144b50c7a6934cc4c1a6b9270096e3ff969d610  node-v12.0.0-win-x86.zip
ac3d5de879589a41b9031ee32e82b3933faa07daba280e159e19022f28833c02  node-v12.0.0-x64.msi
329157f4da523e164f2687544632e90c12dc4a01678326792ca1fc698dd633b9  node-v12.0.0-x86.msi
fcf2ce4daed35c443ed70707d5b3ea4bcde76beceab15de7b0a99c51a86d524a  win-x64/node.exe
d7b2efaef9c554e1fce272cbf6d7a9bd1c627e231cf788b0f031a6473b9d4ff9  win-x64/node.lib
2b8dce71399a29800ec85f9ffc181f1cc9bdbe0c520014a3739800ed2430f1e8  win-x64/node_pdb.7z
c95c8c00e7af4c54dc7be0a397b3d121a5619368e859744a0980f4bde1d81c90  win-x64/node_pdb.zip
3cb7e6def137206373699e53bf4966382e68c0be0d4b74ad543d618560dad9d5  win-x86/node.exe
ce0db24d69da39f016d7c001edb7cea010850811622f8aa75c485594e6e15c4e  win-x86/node.lib
f7ba5e14e5bc1ca646975e30b4a0fb702225834be3c7bdcff3d2120e49bc9d8b  win-x86/node_pdb.7z
48dcb7f9f36558d1dbf067b44b0f13c9f6b6fab11e27c3bf1320bb77590b9146  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQEzBAEBCAAdFiEETtd49TnjY0x3nIfG1wYoSKGrAFwFAly/NBMACgkQ1wYoSKGr
AFzoOAgAmBqMfKmMxSL5FJ1xIuayUtaNkvvu7mTrmq35GRaRfIoF8+ymaC6r6K5A
yhwMdjWtl1ceAqKmFHmJ3A5HxM/7tTEs2cW2GGGprYcRQLqZbt8DZXmXcnzjxDyM
4e3ioLezXaWyXxbX+Jf17WT1RSDguxSI6+0aXQCWWKRTdYc6PPK91pMmgchzEupM
j1y/uEtBQ8jg6sCc3Ee8miDnEPa8HyKGOugeiaP+HDyAGZYXJNGf00xqTvGISr8U
jovOtvgzKDLfVZFVAWoaBdabsxJqn/5gXQouRSeSG0MWYOlL/BptLnhYyQi2tpln
79gtVqxGdIQwT5eX+/v+HHdLVE1AMg==
=AMuQ
-----END PGP SIGNATURE-----

```
