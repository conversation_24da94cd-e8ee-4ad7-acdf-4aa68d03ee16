---
date: '2021-04-20T16:15:46.539Z'
category: release
title: Node v16.0.0 (Current)
layout: blog-post
author: <PERSON>
---

### Notable Changes

#### Deprecations and Removals

- **(SEMVER-MAJOR)** **fs**: remove permissive rmdir recursive (<PERSON>) [#37216](https://github.com/nodejs/node/pull/37216)
- **(SEMVER-MAJOR)** **fs**: runtime deprecate rmdir recursive option (<PERSON>) [#37302](https://github.com/nodejs/node/pull/37302)
- **(SEMVER-MAJOR)** **lib**: runtime deprecate access to process.binding('http_parser') (<PERSON>) [#37813](https://github.com/nodejs/node/pull/37813)
- **(SEMVER-MAJOR)** **lib**: runtime deprecate access to process.binding('url') (<PERSON>) [#37799](https://github.com/nodejs/node/pull/37799)
- **(SEMVER-MAJOR)** **lib**: make process.binding('util') return only type checkers (Anna Henningsen) [#37819](https://github.com/nodejs/node/pull/37819)
- **(SEMVER-MAJOR)** **lib**: runtime deprecate access to process.binding('crypto') (James M Snell) [#37790](https://github.com/nodejs/node/pull/37790)
- **(SEMVER-MAJOR)** **lib**: runtime deprecate access to process.binding('signal_wrap') (James M Snell) [#37800](https://github.com/nodejs/node/pull/37800)
- **(SEMVER-MAJOR)** **lib**: runtime deprecate access to process.binding('v8') (James M Snell) [#37789](https://github.com/nodejs/node/pull/37789)
- **(SEMVER-MAJOR)** **lib**: runtime deprecate access to process.binding('async_wrap') (James M Snell) [#37576](https://github.com/nodejs/node/pull/37576)
- **(SEMVER-MAJOR)** **module**: remove module.createRequireFromPath (Antoine du Hamel) [#37201](https://github.com/nodejs/node/pull/37201)
- **(SEMVER-MAJOR)** **module**: runtime deprecate subpath folder mappings (Antoine du Hamel) [#37215](https://github.com/nodejs/node/pull/37215)
- **(SEMVER-MAJOR)** **module**: runtime deprecate "main" index and extension lookups (Antoine du Hamel) [#37206](https://github.com/nodejs/node/pull/37206)
- **(SEMVER-MAJOR)** **module**: runtime deprecate invalid package.json main entries (Antoine du Hamel) [#37204](https://github.com/nodejs/node/pull/37204)
- **(SEMVER-MAJOR)** **process**: runtime deprecate changing process.config (James M Snell) [#36902](https://github.com/nodejs/node/pull/36902)

#### Stable Timers Promises API

The Timers Promises API provides an alternative set of timer functions that return Promise objects. Added in Node.js v15.0.0, in this release they graduate from experimental status to stable.

Contributed by James Snell - [#38112](https://github.com/nodejs/node/pull/38112)

#### Toolchain and Compiler Upgrades

Node.js v16.0.0 will be the first release where we ship prebuilt binaries for Apple Silicon. While we’ll be providing separate tarballs for the Intel (`darwin-x64`) and ARM (`darwin-arm64`) architectures the macOS installer (`.pkg`) will be shipped as a ‘fat’ (multi-architecture) binary.

- **(SEMVER-MAJOR)** **build**: remove support for Python 2 (Christian Clauss) [#36691](https://github.com/nodejs/node/pull/36691)
- **(SEMVER-MAJOR)** **build**: default PYTHON to python3 in Makefile (Michaël Zasso) [#37764](https://github.com/nodejs/node/pull/37764)
- **build**: update Makefile to support fat binary (Ash Cripps) [#37861](https://github.com/nodejs/node/pull/37861)
- **(SEMVER-MAJOR)** **build**: enable ASLR (PIE) on OS X (woodfairy) [#35704](https://github.com/nodejs/node/pull/35704)
- **build**: warn for gcc versions earlier than 8.3.0 (Richard Lau) [#37935](https://github.com/nodejs/node/pull/37935)
- **(SEMVER-MAJOR)** **doc**: update minimum supported Xcode to 11 (Michaël Zasso) [#37872](https://github.com/nodejs/node/pull/37872)
- **(SEMVER-MAJOR)** **doc**: update minimum supported GCC to 8.3 (Michaël Zasso) [#37871](https://github.com/nodejs/node/pull/37871)
- **(SEMVER-MAJOR)** **doc**: update AIX to GCC8 for v16.x (Ash Cripps) [#37677](https://github.com/nodejs/node/pull/37677)
- **tools**: set arch in Distribution.xml (Ash Cripps) [#38261](https://github.com/nodejs/node/pull/38261)

#### V8 9.0

The V8 JavaScript engine is updated to V8 9.0, including performance tweaks and improvements.

This update also brings the ECMAScript RegExp Match Indices, which provide the start and end indices of the captured string. The indices array is available via the `.indices` property on match objects when the regular expression has the `/d` flag.

Contributed by Michaël Zasso - [#37587](https://github.com/nodejs/node/pull/37587)

#### Other Notable Changes

- **(SEMVER-MINOR)** **assert**: graduate assert.match and assert.doesNotMatch (James M Snell) [#38111](https://github.com/nodejs/node/pull/38111)
- **(SEMVER-MAJOR)** **buffer**: expose btoa and atob as globals (James M Snell) [#37786](https://github.com/nodejs/node/pull/37786)
- **(SEMVER-MAJOR)** **deps**: bump minimum ICU version to 68 (Michaël Zasso) [#37330](https://github.com/nodejs/node/pull/37330)
- **deps**: update ICU to 69.1 (Michaël Zasso) [#38178](https://github.com/nodejs/node/pull/38178)
- **deps**: update llhttp to 6.0.0 (Fedor Indutny) [#38277](https://github.com/nodejs/node/pull/38277)
- **deps**: upgrade npm to 7.10.0 (Ruy Adorno) [#38254](https://github.com/nodejs/node/pull/38254)
- **(SEMVER-MINOR)** **http**: add http.ClientRequest.getRawHeaderNames() (simov) [#37660](https://github.com/nodejs/node/pull/37660)
- **(SEMVER-MAJOR)** **lib,src**: update cluster to use Parent (Michael Dawson) [#36478](https://github.com/nodejs/node/pull/36478)
- **(SEMVER-MINOR)** **module**: add support for `node:`‑prefixed `require(…)` calls (ExE Boss) [#37246](https://github.com/nodejs/node/pull/37246)
- **(SEMVER-MINOR)** **perf_hooks**: add histogram option to timerify (James M Snell) [#37475](https://github.com/nodejs/node/pull/37475)
- **(SEMVER-MINOR)** **repl**: add auto‑completion for `node:`‑prefixed `require(…)` calls (ExE Boss) [#37246](https://github.com/nodejs/node/pull/37246)
- **(SEMVER-MINOR)** **util**: add getSystemErrorMap() impl (eladkeyshawn) [#38101](https://github.com/nodejs/node/pull/38101)

### Semver-Major Commits

- [[`324a6c235a`](https://github.com/nodejs/node/commit/324a6c235a)] - **(SEMVER-MAJOR)** **async_hooks**: add thisArg to AsyncResource.bind (James M Snell) [#36782](https://github.com/nodejs/node/pull/36782)
- [[`d1e2184c8e`](https://github.com/nodejs/node/commit/d1e2184c8e)] - **(SEMVER-MAJOR)** **buffer**: expose btoa and atob as globals (James M Snell) [#37786](https://github.com/nodejs/node/pull/37786)
- [[`4268fae04a`](https://github.com/nodejs/node/commit/4268fae04a)] - **(SEMVER-MAJOR)** **build**: remove support for Python 2 (Christian Clauss) [#36691](https://github.com/nodejs/node/pull/36691)
- [[`c3a5e15ebe`](https://github.com/nodejs/node/commit/c3a5e15ebe)] - **(SEMVER-MAJOR)** **build**: default PYTHON to python3 in Makefile (Michaël Zasso) [#37764](https://github.com/nodejs/node/pull/37764)
- [[`1d8c022544`](https://github.com/nodejs/node/commit/1d8c022544)] - **(SEMVER-MAJOR)** **build**: update Makefile to support fat binary (Ash Cripps) [#37861](https://github.com/nodejs/node/pull/37861)
- [[`38f32386c1`](https://github.com/nodejs/node/commit/38f32386c1)] - **(SEMVER-MAJOR)** **build**: include minimal V8 headers in distribution (Michaël Zasso) [#37570](https://github.com/nodejs/node/pull/37570)
- [[`a19af5ee71`](https://github.com/nodejs/node/commit/a19af5ee71)] - **(SEMVER-MAJOR)** **build**: use C++11 ABI with libstdc++ (Anna Henningsen) [#36634](https://github.com/nodejs/node/pull/36634)
- [[`8d6b74d347`](https://github.com/nodejs/node/commit/8d6b74d347)] - **(SEMVER-MAJOR)** **build**: enable ASLR (PIE) on OS X (woodfairy) [#35704](https://github.com/nodejs/node/pull/35704)
- [[`732ad99e47`](https://github.com/nodejs/node/commit/732ad99e47)] - **(SEMVER-MAJOR)** **deps**: update V8 to 9.0.257.11 (Michaël Zasso) [#37587](https://github.com/nodejs/node/pull/37587)
- [[`43cc8e4b2e`](https://github.com/nodejs/node/commit/43cc8e4b2e)] - **(SEMVER-MAJOR)** **deps**: bump minimum ICU version to 68 (Michaël Zasso) [#37330](https://github.com/nodejs/node/pull/37330)
- [[`c5ff019a4e`](https://github.com/nodejs/node/commit/c5ff019a4e)] - **(SEMVER-MAJOR)** **deps**: update V8 to 8.9.255.19 (Michaël Zasso) [#37330](https://github.com/nodejs/node/pull/37330)
- [[`c7b3292251`](https://github.com/nodejs/node/commit/c7b3292251)] - **(SEMVER-MAJOR)** **deps**: update V8 to 8.8.278.17 (Michaël Zasso) [#36139](https://github.com/nodejs/node/pull/36139)
- [[`48db20f6f5`](https://github.com/nodejs/node/commit/48db20f6f5)] - **(SEMVER-MAJOR)** **deps**: update V8 to 8.7.220 (Michaël Zasso) [#35700](https://github.com/nodejs/node/pull/35700)
- [[`d85e1f0703`](https://github.com/nodejs/node/commit/d85e1f0703)] - **(SEMVER-MAJOR)** **dns**: use url module instead of punycode for IDNA (Antoine du Hamel) [#35091](https://github.com/nodejs/node/pull/35091)
- [[`290c158018`](https://github.com/nodejs/node/commit/290c158018)] - **(SEMVER-MAJOR)** **doc**: update minimum supported Xcode to 11 (Michaël Zasso) [#37872](https://github.com/nodejs/node/pull/37872)
- [[`1ff2918d80`](https://github.com/nodejs/node/commit/1ff2918d80)] - **(SEMVER-MAJOR)** **doc**: update minimum supported GCC to 8.3 (Michaël Zasso) [#37871](https://github.com/nodejs/node/pull/37871)
- [[`2706e67116`](https://github.com/nodejs/node/commit/2706e67116)] - **(SEMVER-MAJOR)** **doc**: update AIX to GCC8 for v16.x (Ash Cripps) [#37677](https://github.com/nodejs/node/pull/37677)
- [[`5ae5ca90ef`](https://github.com/nodejs/node/commit/5ae5ca90ef)] - **(SEMVER-MAJOR)** **doc**: add http.IncomingMessage#connection (Pranshu Srivastava) [#33768](https://github.com/nodejs/node/pull/33768)
- [[`83d6e63aee`](https://github.com/nodejs/node/commit/83d6e63aee)] - **(SEMVER-MAJOR)** **events**: change EventTarget handler exception behavior (Nitzan Uziely) [#37237](https://github.com/nodejs/node/pull/37237)
- [[`9948036ee0`](https://github.com/nodejs/node/commit/9948036ee0)] - **(SEMVER-MAJOR)** **fs**: remove permissive rmdir recursive (Antoine du Hamel) [#37216](https://github.com/nodejs/node/pull/37216)
- [[`d4693ff430`](https://github.com/nodejs/node/commit/d4693ff430)] - **(SEMVER-MAJOR)** **fs**: add validation for fd and path (Dylan Elliott) [#35187](https://github.com/nodejs/node/pull/35187)
- [[`0ddd75bcd8`](https://github.com/nodejs/node/commit/0ddd75bcd8)] - **(SEMVER-MAJOR)** **fs**: runtime deprecate rmdir recursive option (Antoine du Hamel) [#37302](https://github.com/nodejs/node/pull/37302)
- [[`da217d0773`](https://github.com/nodejs/node/commit/da217d0773)] - **(SEMVER-MAJOR)** **fs**: fix flag and mode validation (James M Snell) [#37480](https://github.com/nodejs/node/pull/37480)
- [[`2ef9a76ece`](https://github.com/nodejs/node/commit/2ef9a76ece)] - **(SEMVER-MAJOR)** **http**: use objects with null prototype in Agent (Michaël Zasso) [#36409](https://github.com/nodejs/node/pull/36409)
- [[`25e30005b8`](https://github.com/nodejs/node/commit/25e30005b8)] - **(SEMVER-MAJOR)** **lib**: runtime deprecate access to process.binding('http_parser') (James M Snell) [#37813](https://github.com/nodejs/node/pull/37813)
- [[`8bb4e048af`](https://github.com/nodejs/node/commit/8bb4e048af)] - **(SEMVER-MAJOR)** **lib**: runtime deprecate access to process.binding('url') (James M Snell) [#37799](https://github.com/nodejs/node/pull/37799)
- [[`fe73e4d578`](https://github.com/nodejs/node/commit/fe73e4d578)] - **(SEMVER-MAJOR)** **lib**: make process.binding('util') return only type checkers (Anna Henningsen) [#37819](https://github.com/nodejs/node/pull/37819)
- [[`3bee6d8aad`](https://github.com/nodejs/node/commit/3bee6d8aad)] - **(SEMVER-MAJOR)** **lib**: runtime deprecate access to process.binding('crypto') (James M Snell) [#37790](https://github.com/nodejs/node/pull/37790)
- [[`ac00df112e`](https://github.com/nodejs/node/commit/ac00df112e)] - **(SEMVER-MAJOR)** **lib**: runtime deprecate access to process.binding('signal_wrap') (James M Snell) [#37800](https://github.com/nodejs/node/pull/37800)
- [[`ae595d76e3`](https://github.com/nodejs/node/commit/ae595d76e3)] - **(SEMVER-MAJOR)** **lib**: runtime deprecate access to process.binding('v8') (James M Snell) [#37789](https://github.com/nodejs/node/pull/37789)
- [[`104dac79cc`](https://github.com/nodejs/node/commit/104dac79cc)] - **(SEMVER-MAJOR)** **lib**: aggregate errors to avoid error swallowing (Antoine du Hamel) [#37460](https://github.com/nodejs/node/pull/37460)
- [[`1468c9ff7c`](https://github.com/nodejs/node/commit/1468c9ff7c)] - **(SEMVER-MAJOR)** **lib**: runtime deprecate access to process.binding('async_wrap') (James M Snell) [#37576](https://github.com/nodejs/node/pull/37576)
- [[`295e766c27`](https://github.com/nodejs/node/commit/295e766c27)] - **(SEMVER-MAJOR)** **lib**: remove usage of url.parse (raisinten) [#36853](https://github.com/nodejs/node/pull/36853)
- [[`cb3020d824`](https://github.com/nodejs/node/commit/cb3020d824)] - **(SEMVER-MAJOR)** **lib**: add error handling for input stream (rexagod) [#31603](https://github.com/nodejs/node/pull/31603)
- [[`15164cebce`](https://github.com/nodejs/node/commit/15164cebce)] - **(SEMVER-MAJOR)** **lib,src**: update cluster to use Parent (Michael Dawson) [#36478](https://github.com/nodejs/node/pull/36478)
- [[`3cc9aec988`](https://github.com/nodejs/node/commit/3cc9aec988)] - **(SEMVER-MAJOR)** **module**: runtime deprecate subpath folder mappings (Antoine du Hamel) [#37215](https://github.com/nodejs/node/pull/37215)
- [[`9fab73c73b`](https://github.com/nodejs/node/commit/9fab73c73b)] - **(SEMVER-MAJOR)** **module**: runtime deprecate "main" index and extension lookups (Antoine du Hamel) [#37206](https://github.com/nodejs/node/pull/37206)
- [[`76a073b67e`](https://github.com/nodejs/node/commit/76a073b67e)] - **(SEMVER-MAJOR)** **module**: runtime deprecate invalid package.json main entries (Antoine du Hamel) [#37204](https://github.com/nodejs/node/pull/37204)
- [[`674614b3f5`](https://github.com/nodejs/node/commit/674614b3f5)] - **(SEMVER-MAJOR)** **module**: remove module.createRequireFromPath (Antoine du Hamel) [#37201](https://github.com/nodejs/node/pull/37201)
- [[`aecd5ebf49`](https://github.com/nodejs/node/commit/aecd5ebf49)] - **(SEMVER-MAJOR)** **module**: only set cache when finding module succeeds (Yongsheng Zhang) [#36642](https://github.com/nodejs/node/pull/36642)
- [[`f0bf373176`](https://github.com/nodejs/node/commit/f0bf373176)] - **(SEMVER-MAJOR)** **perf_hooks**: make performance a global (James M Snell) [#37970](https://github.com/nodejs/node/pull/37970)
- [[`f3eb224c83`](https://github.com/nodejs/node/commit/f3eb224c83)] - **(SEMVER-MAJOR)** **perf_hooks**: complete overhaul of the implementation (James M Snell) [#37136](https://github.com/nodejs/node/pull/37136)
- [[`f1753d4c76`](https://github.com/nodejs/node/commit/f1753d4c76)] - **(SEMVER-MAJOR)** **process**: disallow adding options to process.allowedNodeEnvironmentFlags (Antoine du Hamel) [#36660](https://github.com/nodejs/node/pull/36660)
- [[`96f3977ded`](https://github.com/nodejs/node/commit/96f3977ded)] - **(SEMVER-MAJOR)** **process**: runtime deprecate changing process.config (James M Snell) [#36902](https://github.com/nodejs/node/pull/36902)
- [[`45dbcbef90`](https://github.com/nodejs/node/commit/45dbcbef90)] - **(SEMVER-MAJOR)** **readline**: cursorTo throw error on NaN (Zijian Liu) [#36379](https://github.com/nodejs/node/pull/36379)
- [[`bf79987433`](https://github.com/nodejs/node/commit/bf79987433)] - **(SEMVER-MAJOR)** **src**: mark internally exported functions as explicitly internal (Tyler Ang-Wanek) [#37000](https://github.com/nodejs/node/pull/37000)
- [[`1fe571aa0c`](https://github.com/nodejs/node/commit/1fe571aa0c)] - **(SEMVER-MAJOR)** **src**: inline AsyncCleanupHookHandle in headers (Tyler Ang-Wanek) [#37000](https://github.com/nodejs/node/pull/37000)
- [[`dfc288e7fd`](https://github.com/nodejs/node/commit/dfc288e7fd)] - **(SEMVER-MAJOR)** **src**: clean up embedder API (Anna Henningsen) [#35897](https://github.com/nodejs/node/pull/35897)
- [[`65e8864fa3`](https://github.com/nodejs/node/commit/65e8864fa3)] - **(SEMVER-MAJOR)** **worker**: send correct error status for worker init (Yash Ladha) [#36242](https://github.com/nodejs/node/pull/36242)

### Semver-Minor Commits

- [[`944a956087`](https://github.com/nodejs/node/commit/944a956087)] - **(SEMVER-MINOR)** **assert**: graduate assert.match and assert.doesNotMatch (James M Snell) [#38111](https://github.com/nodejs/node/pull/38111)
- [[`6a1986d50a`](https://github.com/nodejs/node/commit/6a1986d50a)] - **(SEMVER-MINOR)** **deps**: update llhttp to 5.1.0 (Fedor Indutny) [#38146](https://github.com/nodejs/node/pull/38146)
- [[`069b5df4f6`](https://github.com/nodejs/node/commit/069b5df4f6)] - **(SEMVER-MINOR)** **module**: add support for `node:`‑prefixed `require(…)` calls (ExE Boss) [#37246](https://github.com/nodejs/node/pull/37246)
- [[`b803bca4fa`](https://github.com/nodejs/node/commit/b803bca4fa)] - **(SEMVER-MINOR)** **perf_hooks**: add histogram option to timerify (James M Snell) [#37475](https://github.com/nodejs/node/pull/37475)
- [[`95391fe689`](https://github.com/nodejs/node/commit/95391fe689)] - **(SEMVER-MINOR)** **repl**: add auto‑completion for `node:`‑prefixed `require(…)` calls (ExE Boss) [#37246](https://github.com/nodejs/node/pull/37246)
- [[`15b8e6b1c4`](https://github.com/nodejs/node/commit/15b8e6b1c4)] - **(SEMVER-MINOR)** **timers**: graduate awaitable timers and improve docs (James M Snell) [#38112](https://github.com/nodejs/node/pull/38112)
- [[`802171057f`](https://github.com/nodejs/node/commit/802171057f)] - **(SEMVER-MINOR)** **util**: add getSystemErrorMap() impl (eladkeyshawn) [#38101](https://github.com/nodejs/node/pull/38101)

### Semver-Patch Commits

- [[`8930eba199`](https://github.com/nodejs/node/commit/8930eba199)] - **assert**: change status of legacy asserts (James M Snell) [#38113](https://github.com/nodejs/node/pull/38113)
- [[`0180fc5b9b`](https://github.com/nodejs/node/commit/0180fc5b9b)] - **benchmark**: improve compare.R output (Brian White) [#38118](https://github.com/nodejs/node/pull/38118)
- [[`8d9d8236b7`](https://github.com/nodejs/node/commit/8d9d8236b7)] - **bootstrap**: mksnapshot should show JS error (Bradley Meck) [#38174](https://github.com/nodejs/node/pull/38174)
- [[`6cb314bbe5`](https://github.com/nodejs/node/commit/6cb314bbe5)] - **bootstrap**: print information for snapshot at environment exit in debug (Joyee Cheung) [#37967](https://github.com/nodejs/node/pull/37967)
- [[`14aed60941`](https://github.com/nodejs/node/commit/14aed60941)] - **buffer,errors**: add missing n literal in range error string (Cactysman) [#37750](https://github.com/nodejs/node/pull/37750)
- [[`049b703a28`](https://github.com/nodejs/node/commit/049b703a28)] - **build**: sync generation of `v8\_build\_config.json` (Richard Lau) [#38263](https://github.com/nodejs/node/pull/38263)
- [[`1d21a8d140`](https://github.com/nodejs/node/commit/1d21a8d140)] - **build**: add riscv64 configure (luyahan) [#37980](https://github.com/nodejs/node/pull/37980)
- [[`f5eea1744d`](https://github.com/nodejs/node/commit/f5eea1744d)] - **build**: don't run test workflow on doc dir on macOS (ycjcl868) [#37999](https://github.com/nodejs/node/pull/37999)
- [[`2853b76e20`](https://github.com/nodejs/node/commit/2853b76e20)] - **build**: add pummel tests to ci runs (Rich Trott) [#34289](https://github.com/nodejs/node/pull/34289)
- [[`24426cd8c4`](https://github.com/nodejs/node/commit/24426cd8c4)] - **build**: prepare Windows coverage GitHub Action for pummel tests (Rich Trott) [#34289](https://github.com/nodejs/node/pull/34289)
- [[`7df0fc5c5c`](https://github.com/nodejs/node/commit/7df0fc5c5c)] - **build**: move OPENSSL_API_COMPAT to else clause (Daniel Bevenius) [#38126](https://github.com/nodejs/node/pull/38126)
- [[`9cfb418e1f`](https://github.com/nodejs/node/commit/9cfb418e1f)] - **build**: package release changelog for releases (Richard Lau) [#38033](https://github.com/nodejs/node/pull/38033)
- [[`558d1e6c22`](https://github.com/nodejs/node/commit/558d1e6c22)] - **build**: warn for gcc versions earlier than 8.3.0 (Richard Lau) [#37935](https://github.com/nodejs/node/pull/37935)
- [[`a572a4e34e`](https://github.com/nodejs/node/commit/a572a4e34e)] - **build**: reset embedder string to "-node.0" (Michaël Zasso) [#37587](https://github.com/nodejs/node/pull/37587)
- [[`f3c7078245`](https://github.com/nodejs/node/commit/f3c7078245)] - **build**: reset embedder string to "-node.0" (Michaël Zasso) [#37330](https://github.com/nodejs/node/pull/37330)
- [[`842389839b`](https://github.com/nodejs/node/commit/842389839b)] - **build**: reset embedder string to "-node.0" (Michaël Zasso) [#36139](https://github.com/nodejs/node/pull/36139)
- [[`98d1ae47cf`](https://github.com/nodejs/node/commit/98d1ae47cf)] - **build**: reset embedder string to "-node.0" (Michaël Zasso) [#35700](https://github.com/nodejs/node/pull/35700)
- [[`993ed19f9c`](https://github.com/nodejs/node/commit/993ed19f9c)] - **crypto**: reduce range of size to int max (Qingyu Deng) [#38096](https://github.com/nodejs/node/pull/38096)
- [[`896dc39951`](https://github.com/nodejs/node/commit/896dc39951)] - **crypto**: fix webcrypto derive(Bits|Key) resolve values and docs (Filip Skokan) [#38148](https://github.com/nodejs/node/pull/38148)
- [[`d2f116c6bb`](https://github.com/nodejs/node/commit/d2f116c6bb)] - **crypto**: fixup randomFill size and offset handling (James M Snell) [#38138](https://github.com/nodejs/node/pull/38138)
- [[`dfe3f952a3`](https://github.com/nodejs/node/commit/dfe3f952a3)] - **crypto**: fix crash in CCM mode without data (Tobias Nießen) [#38102](https://github.com/nodejs/node/pull/38102)
- [[`e8cb6446ef`](https://github.com/nodejs/node/commit/e8cb6446ef)] - **crypto**: reconcile oneshot sign/verify sync and async implementations (Filip Skokan) [#37816](https://github.com/nodejs/node/pull/37816)
- [[`1e4a2bcbee`](https://github.com/nodejs/node/commit/1e4a2bcbee)] - **crypto**: remove check for condition that is always true (Rich Trott) [#38072](https://github.com/nodejs/node/pull/38072)
- [[`64d5be25ab`](https://github.com/nodejs/node/commit/64d5be25ab)] - **deps**: V8: cherry-pick 1648e050cade (Michaël Zasso) [#37587](https://github.com/nodejs/node/pull/37587)
- [[`621b544909`](https://github.com/nodejs/node/commit/621b544909)] - **deps**: silence irrelevant V8 warnings (Michaël Zasso) [#37587](https://github.com/nodejs/node/pull/37587)
- [[`0d78bc3101`](https://github.com/nodejs/node/commit/0d78bc3101)] - **deps**: fix V8 build issue with inline methods (Jiawen Geng) [#35415](https://github.com/nodejs/node/pull/35415)
- [[`5214918856`](https://github.com/nodejs/node/commit/5214918856)] - **deps**: make v8.h compatible with VS2015 (Joao Reis) [#32116](https://github.com/nodejs/node/pull/32116)
- [[`6b3caf77b2`](https://github.com/nodejs/node/commit/6b3caf77b2)] - **deps**: V8: forward declaration of `Rtl\*FunctionTable` (Refael Ackermann) [#32116](https://github.com/nodejs/node/pull/32116)
- [[`d0a032fafb`](https://github.com/nodejs/node/commit/d0a032fafb)] - **deps**: V8: patch register-arm64.h (Refael Ackermann) [#32116](https://github.com/nodejs/node/pull/32116)
- [[`c8b2fa642e`](https://github.com/nodejs/node/commit/c8b2fa642e)] - **deps**: V8: un-cherry-pick bd019bd (Refael Ackermann) [#32116](https://github.com/nodejs/node/pull/32116)
- [[`8eeecc19ae`](https://github.com/nodejs/node/commit/8eeecc19ae)] - **deps**: V8: cherry-pick 8957d4677aa7 (Michaël Zasso) [#37330](https://github.com/nodejs/node/pull/37330)
- [[`b186142a0b`](https://github.com/nodejs/node/commit/b186142a0b)] - **deps**: V8: backport a11395433dbd (Michaël Zasso) [#37330](https://github.com/nodejs/node/pull/37330)
- [[`290f2d8d3e`](https://github.com/nodejs/node/commit/290f2d8d3e)] - **deps**: V8: cherry-pick deb0813166f3 (Michaël Zasso) [#36139](https://github.com/nodejs/node/pull/36139)
- [[`63ed0b8bfe`](https://github.com/nodejs/node/commit/63ed0b8bfe)] - **deps**: V8: cherry-pick 9a6a22874c81 (Michaël Zasso) [#36139](https://github.com/nodejs/node/pull/36139)
- [[`47f1c5257a`](https://github.com/nodejs/node/commit/47f1c5257a)] - **deps**: silence irrelevant V8 warning (Michaël Zasso) [#37330](https://github.com/nodejs/node/pull/37330)
- [[`19d975241f`](https://github.com/nodejs/node/commit/19d975241f)] - **deps**: workaround stod() limitations on SmartOS (Colin Ihrig) [#37330](https://github.com/nodejs/node/pull/37330)
- [[`70f928c6a6`](https://github.com/nodejs/node/commit/70f928c6a6)] - **deps**: fix V8 build issue with inline methods (Jiawen Geng) [#35415](https://github.com/nodejs/node/pull/35415)
- [[`b045e39513`](https://github.com/nodejs/node/commit/b045e39513)] - **deps**: patch V8 to run on Xcode 8 (Mary Marchini) [#32116](https://github.com/nodejs/node/pull/32116)
- [[`32725d2224`](https://github.com/nodejs/node/commit/32725d2224)] - **deps**: make v8.h compatible with VS2015 (Joao Reis) [#32116](https://github.com/nodejs/node/pull/32116)
- [[`fe3cee7b37`](https://github.com/nodejs/node/commit/fe3cee7b37)] - **deps**: V8: forward declaration of `Rtl\*FunctionTable` (Refael Ackermann) [#32116](https://github.com/nodejs/node/pull/32116)
- [[`b2d05f7349`](https://github.com/nodejs/node/commit/b2d05f7349)] - **deps**: V8: patch register-arm64.h (Refael Ackermann) [#32116](https://github.com/nodejs/node/pull/32116)
- [[`c7a0ab4e3d`](https://github.com/nodejs/node/commit/c7a0ab4e3d)] - **deps**: patch V8 to run on older XCode versions (Ujjwal Sharma) [#32116](https://github.com/nodejs/node/pull/32116)
- [[`60b623ee90`](https://github.com/nodejs/node/commit/60b623ee90)] - **deps**: V8: un-cherry-pick bd019bd (Refael Ackermann) [#32116](https://github.com/nodejs/node/pull/32116)
- [[`577ff9fee5`](https://github.com/nodejs/node/commit/577ff9fee5)] - **deps**: V8: cherry-pick deb0813166f3 (Michaël Zasso) [#36139](https://github.com/nodejs/node/pull/36139)
- [[`00e1c7ea83`](https://github.com/nodejs/node/commit/00e1c7ea83)] - **deps**: V8: cherry-pick 9a6a22874c81 (Michaël Zasso) [#36139](https://github.com/nodejs/node/pull/36139)
- [[`ee01d6b7fc`](https://github.com/nodejs/node/commit/ee01d6b7fc)] - **deps**: V8: cherry-pick 2059ee813359 (Michaël Zasso) [#36139](https://github.com/nodejs/node/pull/36139)
- [[`2dad8d43cc`](https://github.com/nodejs/node/commit/2dad8d43cc)] - **deps**: V8: cherry-pick bde7ee5473d6 (Michaël Zasso) [#36139](https://github.com/nodejs/node/pull/36139)
- [[`3046131ea0`](https://github.com/nodejs/node/commit/3046131ea0)] - **deps**: V8: cherry-pick 9a712984025e (Michaël Zasso) [#36139](https://github.com/nodejs/node/pull/36139)
- [[`d178d0738f`](https://github.com/nodejs/node/commit/d178d0738f)] - **deps**: V8: cherry-pick 0b96e5b0bfb2 (Michaël Zasso) [#36139](https://github.com/nodejs/node/pull/36139)
- [[`5c71ea151a`](https://github.com/nodejs/node/commit/5c71ea151a)] - **deps**: V8: cherry-pick fbb28902e049 (Michaël Zasso) [#36139](https://github.com/nodejs/node/pull/36139)
- [[`c8e15cd2c6`](https://github.com/nodejs/node/commit/c8e15cd2c6)] - **deps**: V8: cherry-pick 821fb3883a8e (Michaël Zasso) [#35700](https://github.com/nodejs/node/pull/35700)
- [[`b0d67426af`](https://github.com/nodejs/node/commit/b0d67426af)] - **deps**: workaround stod() limitations on SmartOS (Colin Ihrig) [#36139](https://github.com/nodejs/node/pull/36139)
- [[`c8a658ac53`](https://github.com/nodejs/node/commit/c8a658ac53)] - **deps**: fix V8 build issue with inline methods (Jiawen Geng) [#35415](https://github.com/nodejs/node/pull/35415)
- [[`153b8cea36`](https://github.com/nodejs/node/commit/153b8cea36)] - **deps**: patch V8 to run on Xcode 8 (Mary Marchini) [#32116](https://github.com/nodejs/node/pull/32116)
- [[`a785984133`](https://github.com/nodejs/node/commit/a785984133)] - **deps**: V8: silence irrelevant warnings (Michaël Zasso) [#32116](https://github.com/nodejs/node/pull/32116)
- [[`246c9b8c31`](https://github.com/nodejs/node/commit/246c9b8c31)] - **deps**: make v8.h compatible with VS2015 (Joao Reis) [#32116](https://github.com/nodejs/node/pull/32116)
- [[`96a567f9e9`](https://github.com/nodejs/node/commit/96a567f9e9)] - **deps**: V8: forward declaration of `Rtl\*FunctionTable` (Refael Ackermann) [#32116](https://github.com/nodejs/node/pull/32116)
- [[`e74383cecb`](https://github.com/nodejs/node/commit/e74383cecb)] - **deps**: V8: patch register-arm64.h (Refael Ackermann) [#32116](https://github.com/nodejs/node/pull/32116)
- [[`732847f1eb`](https://github.com/nodejs/node/commit/732847f1eb)] - **deps**: patch V8 to run on older XCode versions (Ujjwal Sharma) [#32116](https://github.com/nodejs/node/pull/32116)
- [[`70171d186f`](https://github.com/nodejs/node/commit/70171d186f)] - **deps**: V8: un-cherry-pick bd019bd (Refael Ackermann) [#32116](https://github.com/nodejs/node/pull/32116)
- [[`15c91c6dd5`](https://github.com/nodejs/node/commit/15c91c6dd5)] - **deps**: V8: cherry-pick 821fb3883a8e (Michaël Zasso) [#35700](https://github.com/nodejs/node/pull/35700)
- [[`40b2fa4832`](https://github.com/nodejs/node/commit/40b2fa4832)] - **deps**: V8: cherry-pick 45e49775f5a3 (Michaël Zasso) [#35700](https://github.com/nodejs/node/pull/35700)
- [[`cd91ab5865`](https://github.com/nodejs/node/commit/cd91ab5865)] - **deps**: V8: cherry-pick 7b3a27b7ae65 (Michaël Zasso) [#35700](https://github.com/nodejs/node/pull/35700)
- [[`f4fc099080`](https://github.com/nodejs/node/commit/f4fc099080)] - **deps**: V8: cherry-pick d76abfed3512 (Michaël Zasso) [#35415](https://github.com/nodejs/node/pull/35415)
- [[`6200176ef0`](https://github.com/nodejs/node/commit/6200176ef0)] - **deps**: fix V8 build issue with inline methods (Jiawen Geng) [#35415](https://github.com/nodejs/node/pull/35415)
- [[`bd5642deb9`](https://github.com/nodejs/node/commit/bd5642deb9)] - **deps**: update V8 postmortem metadata script (Colin Ihrig) [#35415](https://github.com/nodejs/node/pull/35415)
- [[`9ae7159216`](https://github.com/nodejs/node/commit/9ae7159216)] - **deps**: update V8 postmortem metadata script (Colin Ihrig) [#33579](https://github.com/nodejs/node/pull/33579)
- [[`f4b4e21b2f`](https://github.com/nodejs/node/commit/f4b4e21b2f)] - **deps**: patch V8 to run on Xcode 8 (Mary Marchini) [#32116](https://github.com/nodejs/node/pull/32116)
- [[`f6a84540d8`](https://github.com/nodejs/node/commit/f6a84540d8)] - **deps**: V8: silence irrelevant warnings (Michaël Zasso) [#32116](https://github.com/nodejs/node/pull/32116)
- [[`bbc3f46572`](https://github.com/nodejs/node/commit/bbc3f46572)] - **deps**: make v8.h compatible with VS2015 (Joao Reis) [#32116](https://github.com/nodejs/node/pull/32116)
- [[`0c988642dc`](https://github.com/nodejs/node/commit/0c988642dc)] - **deps**: V8: forward declaration of `Rtl\*FunctionTable` (Refael Ackermann) [#32116](https://github.com/nodejs/node/pull/32116)
- [[`703bf933d4`](https://github.com/nodejs/node/commit/703bf933d4)] - **deps**: V8: patch register-arm64.h (Refael Ackermann) [#32116](https://github.com/nodejs/node/pull/32116)
- [[`5451975b18`](https://github.com/nodejs/node/commit/5451975b18)] - **deps**: patch V8 to run on older XCode versions (Ujjwal Sharma) [#32116](https://github.com/nodejs/node/pull/32116)
- [[`c460f7af4d`](https://github.com/nodejs/node/commit/c460f7af4d)] - **deps**: V8: un-cherry-pick bd019bd (Refael Ackermann) [#32116](https://github.com/nodejs/node/pull/32116)
- [[`bfee9daaa5`](https://github.com/nodejs/node/commit/bfee9daaa5)] - **deps**: update llhttp to 6.0.0 (Fedor Indutny) [#38277](https://github.com/nodejs/node/pull/38277)
- [[`94405650ae`](https://github.com/nodejs/node/commit/94405650ae)] - **deps**: upgrade npm to 7.10.0 (Ruy Adorno) [#38254](https://github.com/nodejs/node/pull/38254)
- [[`8e80fc7ff8`](https://github.com/nodejs/node/commit/8e80fc7ff8)] - **deps**: patch V8 to 9.0.257.17 (Michaël Zasso) [#38237](https://github.com/nodejs/node/pull/38237)
- [[`5b358d57e1`](https://github.com/nodejs/node/commit/5b358d57e1)] - **deps**: patch V8 to 9.0.257.16 (Michaël Zasso) [#38218](https://github.com/nodejs/node/pull/38218)
- [[`ee669a0d29`](https://github.com/nodejs/node/commit/ee669a0d29)] - **deps**: update ICU to 69.1 (Michaël Zasso) [#38178](https://github.com/nodejs/node/pull/38178)
- [[`2468e4ed3e`](https://github.com/nodejs/node/commit/2468e4ed3e)] - **deps**: V8: backport d59db06bf542 (Antoine du Hamel) [#38162](https://github.com/nodejs/node/pull/38162)
- [[`c748668704`](https://github.com/nodejs/node/commit/c748668704)] - **deps**: upgrade npm to 7.9.0 (Ruy Adorno) [#38156](https://github.com/nodejs/node/pull/38156)
- [[`ca13f7aaf3`](https://github.com/nodejs/node/commit/ca13f7aaf3)] - **deps**: V8: cherry-pick 501482cbc704 (Colin Ihrig) [#38121](https://github.com/nodejs/node/pull/38121)
- [[`bc531d1860`](https://github.com/nodejs/node/commit/bc531d1860)] - **deps**: upgrade npm to 7.8.0 (Darcy Clarke) [#38030](https://github.com/nodejs/node/pull/38030)
- [[`d639321acd`](https://github.com/nodejs/node/commit/d639321acd)] - **deps**: patch V8 to 9.0.257.13 (Michaël Zasso) [#37830](https://github.com/nodejs/node/pull/37830)
- [[`bc31dc0e0f`](https://github.com/nodejs/node/commit/bc31dc0e0f)] - **dns**: refactor cares_wrap internals (James M Snell) [#38172](https://github.com/nodejs/node/pull/38172)
- [[`36decec87f`](https://github.com/nodejs/node/commit/36decec87f)] - **doc**: remove superfluous await from fsPromises.readdir example (Michael Rommel) [#38293](https://github.com/nodejs/node/pull/38293)
- [[`ac2c8c530d`](https://github.com/nodejs/node/commit/ac2c8c530d)] - **doc**: fixup http.IncomingMessage deprecation code (Guy Bedford) [#36917](https://github.com/nodejs/node/pull/36917)
- [[`767643fc19`](https://github.com/nodejs/node/commit/767643fc19)] - **doc**: restore minimum Xcode version for macOS (Richard Lau) [#38266](https://github.com/nodejs/node/pull/38266)
- [[`e541032276`](https://github.com/nodejs/node/commit/e541032276)] - **doc**: fix typo in repl.md (Arkerone) [#38244](https://github.com/nodejs/node/pull/38244)
- [[`fb93b71307`](https://github.com/nodejs/node/commit/fb93b71307)] - **doc**: fix typo in buffer.md (Arkerone) [#38243](https://github.com/nodejs/node/pull/38243)
- [[`7d688d4b36`](https://github.com/nodejs/node/commit/7d688d4b36)] - **doc**: fix missing backtick in fs.md (Siddharth) [#38260](https://github.com/nodejs/node/pull/38260)
- [[`6d04cc6849`](https://github.com/nodejs/node/commit/6d04cc6849)] - **doc**: change "oject" to "object" (Arkerone) [#38256](https://github.com/nodejs/node/pull/38256)
- [[`b4363f726c`](https://github.com/nodejs/node/commit/b4363f726c)] - **doc**: revise TLS minVersion/maxVersion text (Rich Trott) [#38202](https://github.com/nodejs/node/pull/38202)
- [[`98c2067f13`](https://github.com/nodejs/node/commit/98c2067f13)] - **doc**: update BUILDING.md for Apple Silicon (Ash Cripps) [#38227](https://github.com/nodejs/node/pull/38227)
- [[`4def7c4418`](https://github.com/nodejs/node/commit/4def7c4418)] - **doc**: standardize on pseudorandom (Rich Trott) [#38196](https://github.com/nodejs/node/pull/38196)
- [[`f1027ecf29`](https://github.com/nodejs/node/commit/f1027ecf29)] - **doc**: standardize command flag notes (Ferdi) [#38199](https://github.com/nodejs/node/pull/38199)
- [[`756d2e48d8`](https://github.com/nodejs/node/commit/756d2e48d8)] - **doc**: update `buffer.constants.MAX\_LENGTH` (Qingyu Deng) [#38109](https://github.com/nodejs/node/pull/38109)
- [[`474fbb5f6e`](https://github.com/nodejs/node/commit/474fbb5f6e)] - **doc**: clarify child_process close event (Nitzan Uziely) [#38181](https://github.com/nodejs/node/pull/38181)
- [[`eee2c331ef`](https://github.com/nodejs/node/commit/eee2c331ef)] - **doc**: add command flag to import.meta.resolve (Ferdi) [#38171](https://github.com/nodejs/node/pull/38171)
- [[`f46d29360c`](https://github.com/nodejs/node/commit/f46d29360c)] - **doc**: advise against using randomFill on floats (Tobias Nießen) [#38150](https://github.com/nodejs/node/pull/38150)
- [[`5823fc79ba`](https://github.com/nodejs/node/commit/5823fc79ba)] - **doc**: update links in ICU guide (Michaël Zasso) [#38177](https://github.com/nodejs/node/pull/38177)
- [[`993a1da47c`](https://github.com/nodejs/node/commit/993a1da47c)] - **doc**: mention cryptographic prng in description of randomUUID (Serkan Özel) [#38074](https://github.com/nodejs/node/pull/38074)
- [[`5ba5cc8619`](https://github.com/nodejs/node/commit/5ba5cc8619)] - **doc**: fix typos in doc/api/cli.md (Arkerone) [#38163](https://github.com/nodejs/node/pull/38163)
- [[`6a2314acd7`](https://github.com/nodejs/node/commit/6a2314acd7)] - **doc**: add link to V8 (Voltrex) [#38144](https://github.com/nodejs/node/pull/38144)
- [[`093b527b25`](https://github.com/nodejs/node/commit/093b527b25)] - **doc**: fix typo in assert.md (Arkerone) [#38152](https://github.com/nodejs/node/pull/38152)
- [[`0fa579ac2a`](https://github.com/nodejs/node/commit/0fa579ac2a)] - **doc**: add missing comma in crypto doc (Tobias Nießen) [#38142](https://github.com/nodejs/node/pull/38142)
- [[`4bc8f7542f`](https://github.com/nodejs/node/commit/4bc8f7542f)] - **doc**: fix typo in crypto (Arkerone) [#38130](https://github.com/nodejs/node/pull/38130)
- [[`005ebafbd1`](https://github.com/nodejs/node/commit/005ebafbd1)] - **doc**: improve security text in collaborators guide (Rich Trott) [#38107](https://github.com/nodejs/node/pull/38107)
- [[`54322b8d8b`](https://github.com/nodejs/node/commit/54322b8d8b)] - **doc**: apply consistent punctuation to header contributing guide (Akhil Marsonya) [#38047](https://github.com/nodejs/node/pull/38047)
- [[`0d34767c4c`](https://github.com/nodejs/node/commit/0d34767c4c)] - **doc**: sending http request to localhost to avoid https redirect (Hassaan Pasha) [#38036](https://github.com/nodejs/node/pull/38036)
- [[`f851efd2e1`](https://github.com/nodejs/node/commit/f851efd2e1)] - **doc**: apply sentence case to backporting-to-release-lines.md headers (marsonya) [#37617](https://github.com/nodejs/node/pull/37617)
- [[`36bc8b905c`](https://github.com/nodejs/node/commit/36bc8b905c)] - **doc**: fix typo in fs.md (Antoine du Hamel) [#38100](https://github.com/nodejs/node/pull/38100)
- [[`f52c92134c`](https://github.com/nodejs/node/commit/f52c92134c)] - **doc**: internal/test/binding for testing (Bradley Meck) [#38026](https://github.com/nodejs/node/pull/38026)
- [[`ab42ef3930`](https://github.com/nodejs/node/commit/ab42ef3930)] - **doc**: add parentheses to function and move reference (Rich Trott) [#38066](https://github.com/nodejs/node/pull/38066)
- [[`2861778ecd`](https://github.com/nodejs/node/commit/2861778ecd)] - **doc**: change wording in doc/api/domain.md comment (Akhil Marsonya) [#38044](https://github.com/nodejs/node/pull/38044)
- [[`361632dab1`](https://github.com/nodejs/node/commit/361632dab1)] - **doc**: fix lint error in modules.md (Rich Trott) [#37811](https://github.com/nodejs/node/pull/37811)
- [[`b3f35e2c70`](https://github.com/nodejs/node/commit/b3f35e2c70)] - **doc,lib**: add missing deprecation code (Colin Ihrig) [#37541](https://github.com/nodejs/node/pull/37541)
- [[`cbe3b27166`](https://github.com/nodejs/node/commit/cbe3b27166)] - **doc,tools**: allow stability table to be updated (Richard Lau) [#38048](https://github.com/nodejs/node/pull/38048)
- [[`8dd06850ae`](https://github.com/nodejs/node/commit/8dd06850ae)] - **esm**: use correct URL for error decoration (Bradley Meck) [#37854](https://github.com/nodejs/node/pull/37854)
- [[`6bbe28552c`](https://github.com/nodejs/node/commit/6bbe28552c)] - **fs**: use byteLength to handle ArrayBuffer views (Michaël Zasso) [#38187](https://github.com/nodejs/node/pull/38187)
- [[`8e76397fab`](https://github.com/nodejs/node/commit/8e76397fab)] - **fs**: validate encoding to binding.writeString() (Colin Ihrig) [#38183](https://github.com/nodejs/node/pull/38183)
- [[`24fd791184`](https://github.com/nodejs/node/commit/24fd791184)] - **fs**: move constants to internal/fs/utils.js (Darshan Sen) [#38061](https://github.com/nodejs/node/pull/38061)
- [[`40ace47396`](https://github.com/nodejs/node/commit/40ace47396)] - **http**: fixup perf regression (James M Snell) [#38110](https://github.com/nodejs/node/pull/38110)
- [[`f4d3d12327`](https://github.com/nodejs/node/commit/f4d3d12327)] - **http**: use CRLF conistently in \_http_outgoing.js (Daniel Bevenius) [#37851](https://github.com/nodejs/node/pull/37851)
- [[`ee9e2a2eb6`](https://github.com/nodejs/node/commit/ee9e2a2eb6)] - **lib**: revert primordials in a hot path (Antoine du Hamel) [#38248](https://github.com/nodejs/node/pull/38248)
- [[`d756d2b99c`](https://github.com/nodejs/node/commit/d756d2b99c)] - **lib**: enforce using `primordials.globalThis` instead of `global` (Antoine du Hamel) [#38230](https://github.com/nodejs/node/pull/38230)
- [[`09c9e5dea4`](https://github.com/nodejs/node/commit/09c9e5dea4)] - **lib**: avoid mutating `Error.stackTraceLimit` when it is not writable (Antoine du Hamel) [#38215](https://github.com/nodejs/node/pull/38215)
- [[`23d2c54bab`](https://github.com/nodejs/node/commit/23d2c54bab)] - **lib**: add `globalThis` to primordials (Antoine du Hamel) [#38211](https://github.com/nodejs/node/pull/38211)
- [[`78343bbdc5`](https://github.com/nodejs/node/commit/78343bbdc5)] - **lib**: add `WeakRef` and `FinalizationRegistry` to `primordials` (ExE Boss) [#37263](https://github.com/nodejs/node/pull/37263)
- [[`656fb4657a`](https://github.com/nodejs/node/commit/656fb4657a)] - **lib**: add tsconfig for code completions (Bradley Meck) [#38042](https://github.com/nodejs/node/pull/38042)
- [[`d86132488d`](https://github.com/nodejs/node/commit/d86132488d)] - **lib**: properly process JavaScript exceptions on async_hooks fatal error (legendecas) [#38106](https://github.com/nodejs/node/pull/38106)
- [[`a9332e84bf`](https://github.com/nodejs/node/commit/a9332e84bf)] - **lib**: refactor to use primordials in lib/internal/cli_table (Akhil Marsonya) [#38046](https://github.com/nodejs/node/pull/38046)
- [[`8d78d9ef27`](https://github.com/nodejs/node/commit/8d78d9ef27)] - **lib**: load v8_prof_processor dependencies as ESM (Michaël Zasso) [#37587](https://github.com/nodejs/node/pull/37587)
- [[`7b2bad4005`](https://github.com/nodejs/node/commit/7b2bad4005)] - **module**: clarify CJS global-like variables not defined error message (Antoine du Hamel) [#37852](https://github.com/nodejs/node/pull/37852)
- [[`7869761c2e`](https://github.com/nodejs/node/commit/7869761c2e)] - **net**: fix typo (Luigi Pinca) [#38127](https://github.com/nodejs/node/pull/38127)
- [[`4afcd55274`](https://github.com/nodejs/node/commit/4afcd55274)] - **node-api**: make reference weak parameter an indirect link to references (Chengzhong Wu) [#38000](https://github.com/nodejs/node/pull/38000)
- [[`e38d62a8c9`](https://github.com/nodejs/node/commit/e38d62a8c9)] - **path**: fix POSIX path.resolve() perf regression (Brian White) [#38064](https://github.com/nodejs/node/pull/38064)
- [[`b0d5e036d8`](https://github.com/nodejs/node/commit/b0d5e036d8)] - **path**: fix posix.relative() on Windows (Rich Trott) [#37747](https://github.com/nodejs/node/pull/37747)
- [[`548cbf0625`](https://github.com/nodejs/node/commit/548cbf0625)] - **perf_hooks**: fix loop delay resolution validation (Antoine du Hamel) [#38166](https://github.com/nodejs/node/pull/38166)
- [[`13c931a9dc`](https://github.com/nodejs/node/commit/13c931a9dc)] - **process**: add range validation to debugPort (Colin Ihrig) [#38205](https://github.com/nodejs/node/pull/38205)
- [[`8dd5dd8a4b`](https://github.com/nodejs/node/commit/8dd5dd8a4b)] - **process**: do not lazily load AsyncResource (Michaël Zasso) [#38041](https://github.com/nodejs/node/pull/38041)
- [[`4e833b6059`](https://github.com/nodejs/node/commit/4e833b6059)] - **process,doc**: add missing deprecation code (Colin Ihrig) [#37091](https://github.com/nodejs/node/pull/37091)
- [[`d6669645c0`](https://github.com/nodejs/node/commit/d6669645c0)] - **repl**: fix declaring a variable with the name `util` (eladkeyshawn) [#38141](https://github.com/nodejs/node/pull/38141)
- [[`e7391967c2`](https://github.com/nodejs/node/commit/e7391967c2)] - **repl**: fix error message printing (Anna Henningsen) [#38209](https://github.com/nodejs/node/pull/38209)
- [[`4e9212bb7b`](https://github.com/nodejs/node/commit/4e9212bb7b)] - **src**: cache some context in locals (Khaidi Chu) [#37473](https://github.com/nodejs/node/pull/37473)
- [[`fc20e833ca`](https://github.com/nodejs/node/commit/fc20e833ca)] - **src**: fix finalization crash (James M Snell) [#38250](https://github.com/nodejs/node/pull/38250)
- [[`6c9b19a7af`](https://github.com/nodejs/node/commit/6c9b19a7af)] - **src**: refactor SecureContext Initialization (James M Snell) [#38116](https://github.com/nodejs/node/pull/38116)
- [[`8d63aa828e`](https://github.com/nodejs/node/commit/8d63aa828e)] - **src**: fix typo for initialization (Yash Ladha) [#37974](https://github.com/nodejs/node/pull/37974)
- [[`66c8f76c2c`](https://github.com/nodejs/node/commit/66c8f76c2c)] - **src**: remove KeyObjectData::CreateSecret overload (Tobias Nießen) [#38067](https://github.com/nodejs/node/pull/38067)
- [[`87dc152229`](https://github.com/nodejs/node/commit/87dc152229)] - **src**: fix node version (Richard Lau) [#36460](https://github.com/nodejs/node/pull/36460)
- [[`e929d1f2c8`](https://github.com/nodejs/node/commit/e929d1f2c8)] - **src**: fix node version (Brian White) [#36385](https://github.com/nodejs/node/pull/36385)
- [[`8e8dea36cc`](https://github.com/nodejs/node/commit/8e8dea36cc)] - **src**: use non-deprecated GetCreationContext from V8 (Michaël Zasso) [#37587](https://github.com/nodejs/node/pull/37587)
- [[`b1c1c4695c`](https://github.com/nodejs/node/commit/b1c1c4695c)] - **src**: remove V8_FT_ADAPTOR for V8 update (Colin Ihrig) [#37587](https://github.com/nodejs/node/pull/37587)
- [[`8f5cce6862`](https://github.com/nodejs/node/commit/8f5cce6862)] - **src**: use non-deprecated V8 module APIs (Michaël Zasso) [#37587](https://github.com/nodejs/node/pull/37587)
- [[`497f6ca5b4`](https://github.com/nodejs/node/commit/497f6ca5b4)] - **src**: update NODE_MODULE_VERSION to 93 (Michaël Zasso) [#37587](https://github.com/nodejs/node/pull/37587)
- [[`001dc16cf1`](https://github.com/nodejs/node/commit/001dc16cf1)] - **src**: use non-deprecated V8 module and script APIs (Michaël Zasso) [#37330](https://github.com/nodejs/node/pull/37330)
- [[`47a90d9f37`](https://github.com/nodejs/node/commit/47a90d9f37)] - **src**: update NODE_MODULE_VERSION to 92 (Michaël Zasso) [#37330](https://github.com/nodejs/node/pull/37330)
- [[`5259d17309`](https://github.com/nodejs/node/commit/5259d17309)] - **src**: update NODE_MODULE_VERSION to 91 (Michaël Zasso) [#36139](https://github.com/nodejs/node/pull/36139)
- [[`6f9cbcf6a6`](https://github.com/nodejs/node/commit/6f9cbcf6a6)] - **src**: fix v8 api deprecation (Jiawen Geng) [#35700](https://github.com/nodejs/node/pull/35700)
- [[`9d4d55bd94`](https://github.com/nodejs/node/commit/9d4d55bd94)] - **src**: update NODE_MODULE_VERSION to 90 (Michaël Zasso) [#35700](https://github.com/nodejs/node/pull/35700)
- [[`369f239503`](https://github.com/nodejs/node/commit/369f239503)] - **stream**: fix multiple Writable.destroy() calls (Robert Nagy) [#38221](https://github.com/nodejs/node/pull/38221)
- [[`4ad46e2fef`](https://github.com/nodejs/node/commit/4ad46e2fef)] - **stream**: refactor to avoid unsafe array iteration (Antoine du Hamel) [#37126](https://github.com/nodejs/node/pull/37126)
- [[`419686cdfb`](https://github.com/nodejs/node/commit/419686cdfb)] - **stream**: refactor to use more primordials (Antoine du Hamel) [#36346](https://github.com/nodejs/node/pull/36346)
- [[`c704faa0f9`](https://github.com/nodejs/node/commit/c704faa0f9)] - **test**: fix flaky test-dns and test-dns-lookup (Rich Trott) [#38282](https://github.com/nodejs/node/pull/38282)
- [[`5e588c1c7c`](https://github.com/nodejs/node/commit/5e588c1c7c)] - **test**: fixup failing test/internet/test-dns.js (James M Snell) [#38241](https://github.com/nodejs/node/pull/38241)
- [[`18c9913ce1`](https://github.com/nodejs/node/commit/18c9913ce1)] - **test**: add tests for missing https agent options (Rich Trott) [#38202](https://github.com/nodejs/node/pull/38202)
- [[`4ad8e83a3d`](https://github.com/nodejs/node/commit/4ad8e83a3d)] - **test**: fix test-https-agent-additional-options.js (Rich Trott) [#38202](https://github.com/nodejs/node/pull/38202)
- [[`05df701e70`](https://github.com/nodejs/node/commit/05df701e70)] - **test**: remove common.disableCrashOnUnhandledRejection (Michaël Zasso) [#38210](https://github.com/nodejs/node/pull/38210)
- [[`8f4850d5c7`](https://github.com/nodejs/node/commit/8f4850d5c7)] - **test**: fix typo in comment in binding.c (Tobias Nießen) [#38220](https://github.com/nodejs/node/pull/38220)
- [[`9498e97015`](https://github.com/nodejs/node/commit/9498e97015)] - **test**: fix typo in gtest-all.cc (Ikko Ashimine) [#38224](https://github.com/nodejs/node/pull/38224)
- [[`c8bbd83ab2`](https://github.com/nodejs/node/commit/c8bbd83ab2)] - **test**: add undefined fatalException exit code test (Nitzan Uziely) [#38119](https://github.com/nodejs/node/pull/38119)
- [[`db9cf52dcf`](https://github.com/nodejs/node/commit/db9cf52dcf)] - **test**: check the different error code on IBM i (Xu Meng) [#38159](https://github.com/nodejs/node/pull/38159)
- [[`95ca351fd8`](https://github.com/nodejs/node/commit/95ca351fd8)] - **test**: skip fs.watch() test on IBMi (Rich Trott) [#38192](https://github.com/nodejs/node/pull/38192)
- [[`8cee28465c`](https://github.com/nodejs/node/commit/8cee28465c)] - **test**: fix test-dh-regr for OpenSSL 3 (Rich Trott) [#34289](https://github.com/nodejs/node/pull/34289)
- [[`213ae4f4c6`](https://github.com/nodejs/node/commit/213ae4f4c6)] - **test**: skip test-vm-memleak in ASAN (Rich Trott) [#34289](https://github.com/nodejs/node/pull/34289)
- [[`50208915a0`](https://github.com/nodejs/node/commit/50208915a0)] - **test**: skip test-hash-seed on armv6 and armv7 (Rich Trott) [#34289](https://github.com/nodejs/node/pull/34289)
- [[`7216eb67df`](https://github.com/nodejs/node/commit/7216eb67df)] - **test**: update OpenSSL 3.x expected error message (Daniel Bevenius) [#38164](https://github.com/nodejs/node/pull/38164)
- [[`7e516aaac0`](https://github.com/nodejs/node/commit/7e516aaac0)] - **test**: remove unneeded m flag on regular expressions (Rich Trott) [#38124](https://github.com/nodejs/node/pull/38124)
- [[`269f5132cc`](https://github.com/nodejs/node/commit/269f5132cc)] - **test**: skip different params test for OpenSSL 3.x (Daniel Bevenius) [#38165](https://github.com/nodejs/node/pull/38165)
- [[`f96dffb7ae`](https://github.com/nodejs/node/commit/f96dffb7ae)] - **test**: fix flaky test-zlib-unused-weak.js (Ouyang Yadong) [#38149](https://github.com/nodejs/node/pull/38149)
- [[`e96773b94b`](https://github.com/nodejs/node/commit/e96773b94b)] - **test**: add regression test for serdes readDouble() (Colin Ihrig) [#38121](https://github.com/nodejs/node/pull/38121)
- [[`cc4ee6cba8`](https://github.com/nodejs/node/commit/cc4ee6cba8)] - **test**: deflake test-http-many-ended-pipelines (Luigi Pinca) [#38018](https://github.com/nodejs/node/pull/38018)
- [[`098a4d6551`](https://github.com/nodejs/node/commit/098a4d6551)] - **test**: skip test-crypto-dh-keys on armv6 and armv7 (Rich Trott) [#38076](https://github.com/nodejs/node/pull/38076)
- [[`f9b63b8530`](https://github.com/nodejs/node/commit/f9b63b8530)] - **test**: update parallel/test-crypto-keygen for OpenSSL 3 (Richard Lau) [#38136](https://github.com/nodejs/node/pull/38136)
- [[`6a6cdfad03`](https://github.com/nodejs/node/commit/6a6cdfad03)] - **test**: fix skip message for test-macos-app-sandbox (Tobias Nießen) [#38114](https://github.com/nodejs/node/pull/38114)
- [[`e155b1f2f7`](https://github.com/nodejs/node/commit/e155b1f2f7)] - **test**: correct test comment (Evan Lucas) [#38095](https://github.com/nodejs/node/pull/38095)
- [[`d61977f03e`](https://github.com/nodejs/node/commit/d61977f03e)] - **test**: remove dead code (Luigi Pinca) [#38016](https://github.com/nodejs/node/pull/38016)
- [[`8b05e32519`](https://github.com/nodejs/node/commit/8b05e32519)] - **test**: fix flaky test-net-timeout (Rich Trott) [#38060](https://github.com/nodejs/node/pull/38060)
- [[`a0492ba391`](https://github.com/nodejs/node/commit/a0492ba391)] - **test**: fix test-vm-memleak for high baseline platforms (Rich Trott) [#38062](https://github.com/nodejs/node/pull/38062)
- [[`30d7f05fef`](https://github.com/nodejs/node/commit/30d7f05fef)] - **test**: improve code coverage in webcrypto API (Juan José Arboleda) [#38052](https://github.com/nodejs/node/pull/38052)
- [[`d75543d8b5`](https://github.com/nodejs/node/commit/d75543d8b5)] - **test**: fix flaky timeout-delayed-body and headers tests (Nitzan Uziely) [#38045](https://github.com/nodejs/node/pull/38045)
- [[`4f387c25cb`](https://github.com/nodejs/node/commit/4f387c25cb)] - **test**: fix flaky test-vm-memleak (Rich Trott) [#38054](https://github.com/nodejs/node/pull/38054)
- [[`330f25ef82`](https://github.com/nodejs/node/commit/330f25ef82)] - **test**: prepare for consistent comma-dangle lint rule (Rich Trott) [#37930](https://github.com/nodejs/node/pull/37930)
- [[`31fe3b215f`](https://github.com/nodejs/node/commit/31fe3b215f)] - **test**: make sure http pipelining does not emit a warning (Matteo Collina) [#37964](https://github.com/nodejs/node/pull/37964)
- [[`978bbf987c`](https://github.com/nodejs/node/commit/978bbf987c)] - **test**: fix flaky test-http2-pack-end-stream-flag (James M Snell) [#37814](https://github.com/nodejs/node/pull/37814)
- [[`ecc584251e`](https://github.com/nodejs/node/commit/ecc584251e)] - **test**: fixup flaky test-performance-function-async test (James M Snell) [#37493](https://github.com/nodejs/node/pull/37493)
- [[`32482a828b`](https://github.com/nodejs/node/commit/32482a828b)] - **test**: remove FLAKY for test-domain-error-types (Rich Trott) [#37458](https://github.com/nodejs/node/pull/37458)
- [[`501ae0e6e3`](https://github.com/nodejs/node/commit/501ae0e6e3)] - **test**: remove outdated V8 flag (Michaël Zasso) [#37151](https://github.com/nodejs/node/pull/37151)
- [[`fa3997d75a`](https://github.com/nodejs/node/commit/fa3997d75a)] - **test**: mark test-return-on-exit as flaky (Michaël Zasso) [#36139](https://github.com/nodejs/node/pull/36139)
- [[`896ae96a15`](https://github.com/nodejs/node/commit/896ae96a15)] - **test**: mark WASI's test-return-on-exit as flaky (Colin Ihrig) [#36139](https://github.com/nodejs/node/pull/36139)
- [[`0da7a11e54`](https://github.com/nodejs/node/commit/0da7a11e54)] - **test,http**: check that http server is robust from handler abuse (Rich Trott) [#37958](https://github.com/nodejs/node/pull/37958)
- [[`a0261d231c`](https://github.com/nodejs/node/commit/a0261d231c)] - **_Revert_** "**timers**: refactor to use optional chaining" (Matteo Collina) [#38245](https://github.com/nodejs/node/pull/38245)
- [[`3da003cc1c`](https://github.com/nodejs/node/commit/3da003cc1c)] - **tls**: fix session and keylog add listener segfault (Nitzan Uziely) [#38180](https://github.com/nodejs/node/pull/38180)
- [[`eb20447407`](https://github.com/nodejs/node/commit/eb20447407)] - **tls**: extract out SecureContext configuration (James M Snell) [#38116](https://github.com/nodejs/node/pull/38116)
- [[`b16e79e05b`](https://github.com/nodejs/node/commit/b16e79e05b)] - **tls**: fix typo (Arkerone) [#38129](https://github.com/nodejs/node/pull/38129)
- [[`d4f33f109e`](https://github.com/nodejs/node/commit/d4f33f109e)] - **tools**: skip macOS GitHub Actions test on doc-only changes (Rich Trott) [#38296](https://github.com/nodejs/node/pull/38296)
- [[`13d0de5954`](https://github.com/nodejs/node/commit/13d0de5954)] - **tools**: set arch in Distribution.xml (Ash Cripps) [#38261](https://github.com/nodejs/node/pull/38261)
- [[`28bca33f28`](https://github.com/nodejs/node/commit/28bca33f28)] - **tools**: update ESLint to 7.24.0 (Colin Ihrig) [#38179](https://github.com/nodejs/node/pull/38179)
- [[`038608d401`](https://github.com/nodejs/node/commit/038608d401)] - **tools**: relax max-len lint rule for template strings (Rich Trott) [#38097](https://github.com/nodejs/node/pull/38097)
- [[`e67fb569f4`](https://github.com/nodejs/node/commit/e67fb569f4)] - **tools**: apply consistent comma-dangle lint rule (Rich Trott) [#37930](https://github.com/nodejs/node/pull/37930)
- [[`9843361c07`](https://github.com/nodejs/node/commit/9843361c07)] - **tools**: update V8 gypfiles for 9.0 (Michaël Zasso) [#37587](https://github.com/nodejs/node/pull/37587)
- [[`017661768a`](https://github.com/nodejs/node/commit/017661768a)] - **tools**: update V8 gypfiles for 8.9 (Michaël Zasso) [#37330](https://github.com/nodejs/node/pull/37330)
- [[`79da253473`](https://github.com/nodejs/node/commit/79da253473)] - **tools**: update V8 gypfiles for 8.8 (Michaël Zasso) [#36139](https://github.com/nodejs/node/pull/36139)
- [[`770d9e2542`](https://github.com/nodejs/node/commit/770d9e2542)] - **tools**: update V8 gypfiles for 8.7 (Michaël Zasso) [#35700](https://github.com/nodejs/node/pull/35700)
- [[`b87f1be92d`](https://github.com/nodejs/node/commit/b87f1be92d)] - **typings**: add types for "http_parser" and "options" bindings (Michaël Zasso) [#38239](https://github.com/nodejs/node/pull/38239)
- [[`1c8b2956d1`](https://github.com/nodejs/node/commit/1c8b2956d1)] - **typings**: add types for internalBinding('serdes') (Michaël Zasso) [#38204](https://github.com/nodejs/node/pull/38204)
- [[`d97787fccc`](https://github.com/nodejs/node/commit/d97787fccc)] - **typings**: add JSDoc to os module functions (David Brownman) [#38197](https://github.com/nodejs/node/pull/38197)
- [[`8acfe5c2a4`](https://github.com/nodejs/node/commit/8acfe5c2a4)] - **typings**: add JSDoc Types to lib/querystring (Simon Knott) [#38185](https://github.com/nodejs/node/pull/38185)
- [[`d3162da8dd`](https://github.com/nodejs/node/commit/d3162da8dd)] - **typings**: add JSDoc typings for http (Voltrex) [#38191](https://github.com/nodejs/node/pull/38191)
- [[`82d59882b1`](https://github.com/nodejs/node/commit/82d59882b1)] - **typings**: add JSDoc typings for assert (Voltrex) [#38188](https://github.com/nodejs/node/pull/38188)
- [[`f1a21e5c91`](https://github.com/nodejs/node/commit/f1a21e5c91)] - **typings**: add JSDoc types to lib/path (Simon Knott) [#38186](https://github.com/nodejs/node/pull/38186)
- [[`3377eb9641`](https://github.com/nodejs/node/commit/3377eb9641)] - **typings**: add types for internalBinding('util') (Michaël Zasso) [#38200](https://github.com/nodejs/node/pull/38200)
- [[`cb2bdc632a`](https://github.com/nodejs/node/commit/cb2bdc632a)] - **typings**: add types for internalBinding('fs') (Michaël Zasso) [#38198](https://github.com/nodejs/node/pull/38198)
- [[`26eed3e0ed`](https://github.com/nodejs/node/commit/26eed3e0ed)] - **vm**: add import assertion support (Gus Caplan) [#37176](https://github.com/nodejs/node/pull/37176)
- [[`6986fa07eb`](https://github.com/nodejs/node/commit/6986fa07eb)] - **worker**: fix exit code for error thrown in handler (Nitzan Uziely) [#38012](https://github.com/nodejs/node/pull/38012)

Windows 32-bit Installer: https://nodejs.org/dist/v16.0.0/node-v16.0.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v16.0.0/node-v16.0.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v16.0.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v16.0.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v16.0.0/node-v16.0.0.pkg \
macOS Apple Silicon 64-bit Binary: https://nodejs.org/dist/v16.0.0/node-v16.0.0-darwin-arm64.tar.gz \
macOS Intel 64-bit Binary: https://nodejs.org/dist/v16.0.0/node-v16.0.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v16.0.0/node-v16.0.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v16.0.0/node-v16.0.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v16.0.0/node-v16.0.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v16.0.0/node-v16.0.0-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v16.0.0/node-v16.0.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v16.0.0/node-v16.0.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v16.0.0/node-v16.0.0.tar.gz \
Other release files: https://nodejs.org/dist/v16.0.0/ \
Documentation: https://nodejs.org/docs/v16.0.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

a6aee31e1fd8f55dc78007de2e4ac0d8e0dadd36beacfbabbaf9ab27a5f1f2f4  node-v16.0.0-aix-ppc64.tar.gz
2d6d412abcf7c9375f19fde14086a6423e5bb9415eeca1ccad49638ffc476ea3  node-v16.0.0-darwin-arm64.tar.gz
f8710a83738b4408da82fe81b7934373e4d2f84d40c8c1217676119fd3c77c7e  node-v16.0.0-darwin-arm64.tar.xz
b00457dd7da6cc00d0248dc57b4ddd01a71eed6009ddadd8c854678232091dfb  node-v16.0.0-darwin-x64.tar.gz
66ecffa48b98cf1ca4d038b42b74f05bfc4d31681e2aa43a1ba50919ea23823b  node-v16.0.0-darwin-x64.tar.xz
a4d665582e492bf013ce67b1fadb7db9cb8fd46e7d02a30f5e473373d452e377  node-v16.0.0-headers.tar.gz
f5f178e75d78bd050d1a85ea56189bae6038d9d21d032e7889dbb22fa54da71d  node-v16.0.0-headers.tar.xz
22e7d326b21195c4a0df92a7af7cfdf1743cd46fcc50e335e4086a1c1f2a9a13  node-v16.0.0-linux-arm64.tar.gz
c6dc688de6373049f21cb1ca4f2ceefe80a5d711e301b8d54fd0a7c36a406b03  node-v16.0.0-linux-arm64.tar.xz
d4e2965224ca0667732836be249ec32ad899f7f01d932121daca76cbf38e75f1  node-v16.0.0-linux-armv7l.tar.gz
1cb4bf1bac74f492f9182e44422e245cc2a971889e34f4e554b7c45eb080304c  node-v16.0.0-linux-armv7l.tar.xz
bc28902e8e1453531bb38001cf705dff2456cdf5b856a37dac2f2d3d771b02c1  node-v16.0.0-linux-ppc64le.tar.gz
10bc1b3c18a05811a4497aa77b7951d963baecf033aa436358e28ba3cde28090  node-v16.0.0-linux-ppc64le.tar.xz
3cdfafc6425aace2ab24a31dcac26564a494094c7521b50dc41f3c538b3700ec  node-v16.0.0-linux-s390x.tar.gz
27a5a70178cd765c8b37aa49d18d05e7338c9b043b3195d4cbf28955ca3c9aa2  node-v16.0.0-linux-s390x.tar.xz
9268cdb3c71cec4f3dc3bef98994f310c3bef259fae8c68e3f1c605c5dfcbc58  node-v16.0.0-linux-x64.tar.gz
1736446bb102e19942addce29f6a12b157ca71f38b9159d0446f51ba69618b8d  node-v16.0.0-linux-x64.tar.xz
fe1d4f458a8b3e85c7c927c5a342d09407915b77ade5303fc98b0deeec89a3db  node-v16.0.0.pkg
ef4928ed381dcb8f5eca9c521b3ffa4a384c75cc76656999e16f5d1c171d8e7b  node-v16.0.0.tar.gz
47cb90111e8c3dc42dc538464789415354f0d933587fc89fff71f9bd816aaa02  node-v16.0.0.tar.xz
8b78d362582746c5157b9e703bdd16c3da54c51efa12bed8fdf0e30e2bfdbce6  node-v16.0.0-win-x64.7z
99c2b01afb8d966fc876ec30ac7dfdbd9da9b17a3daeda92c19ce657ab9bea61  node-v16.0.0-win-x64.zip
04859c6d5a1d5054e57d1c1eb8f58a13d9d6e0ea079fe83d9b79d3a9aa401cc5  node-v16.0.0-win-x86.7z
0600dffb5331b6f49e6ff4fa97770811746e0e2ecaf53de6deaafff277a644b4  node-v16.0.0-win-x86.zip
9309bda5a68c353145acc2fa9fbe3ec98a0234b3946a9861997f60b4b89b83a7  node-v16.0.0-x64.msi
6d7404b6e6f0c2a9cd396ce56eb68d2e0d2e5df434554345e075707bff7bc384  node-v16.0.0-x86.msi
f5d19a86afc817068ab7120919a4f96b43e60a7abe3282c3797a50f1cc723930  win-x64/node.exe
32063b59c6df338e1d367eea513dc04abcc1768f4af5ba2bb764dfd1af41e6cc  win-x64/node.lib
f369ce51bda686c451740c1805fa692554568dbc55992026bb17346f5ada6f7e  win-x64/node_pdb.7z
aa12acfbc081eea9a5d625471ce93ebd711c9c6785a76d940b442b672a1d2025  win-x64/node_pdb.zip
eab4525927aadf29b0e257a96a0c7afab1d42a52680622b6bf366690a6fc4d38  win-x86/node.exe
3130ffd2b70c7b3b227f62d97090d3204bb64a319a7257821ff61eb86b645d61  win-x86/node.lib
2d7feeb1a4bb7b2a7e0fe45dc39550d5913d96ff34f10f48d747f2e90b143745  win-x86/node_pdb.7z
47a135fcf66526de3fae114a554ff810567fd837d9f764527e307acc076f1384  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQEzBAEBCAAdFiEETtd49TnjY0x3nIfG1wYoSKGrAFwFAmB++dEACgkQ1wYoSKGr
AFzs1Af/T7bep8whLJueuaJzRhh7BGX/nzPEHU7GP215nNqbN7Simg1Xj+5QCANb
AQYjNe86Fff8JaIp6sQV40qeSEC2PNGx6mp0Rjq8SogqT5NXmRs74VVLZ+H1YERf
0Zy19USOlpSMsK4LJdhU5paShzl9xsw1Lpk7e3XDhANmL2Fd+OWiV546z/dIoKN4
v7e2cbdiYrCYEjQbY6EFyPi/As+r9MjnX7ggXQ8ZD7hRshv7dxYFSRSaIkcUNBZn
J6qRFwbVyAdFzmbUNJREt8ky2ZpwU1p2Cdl/jkWGCjxl1fUSN4/V+9bMSzRaQW/+
t/e5lo+lKhleYXFEK7B5h1Ss6F2MpA==
=v9uW
-----END PGP SIGNATURE-----

```
