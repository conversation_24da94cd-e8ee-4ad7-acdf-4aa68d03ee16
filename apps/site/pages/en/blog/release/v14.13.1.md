---
date: '2020-10-07T21:37:03.065Z'
category: release
title: Node v14.13.1 (Current)
layout: blog-post
author: <PERSON>
---

### Notable Changes

- **fs**:
  - remove experimental from rmdir recursive (<PERSON>) [#35171](https://github.com/nodejs/node/pull/35171)

### Commits

- [[`f36476d9d6`](https://github.com/nodejs/node/commit/f36476d9d6)] - **build**: fix CQ after latest ncu release (<PERSON>) [#35468](https://github.com/nodejs/node/pull/35468)
- [[`7dc6b2fac7`](https://github.com/nodejs/node/commit/7dc6b2fac7)] - **build**: add support for section ordering (<PERSON>) [#35272](https://github.com/nodejs/node/pull/35272)
- [[`05e08bdf13`](https://github.com/nodejs/node/commit/05e08bdf13)] - **console**: add Symbol.toStringTag property (Leko) [#35399](https://github.com/nodejs/node/pull/35399)
- [[`a01154e3fd`](https://github.com/nodejs/node/commit/a01154e3fd)] - **crypto**: fix KeyObject garbage collection (Anna Henningsen) [#35481](https://github.com/nodejs/node/pull/35481)
- [[`1f15e34dc8`](https://github.com/nodejs/node/commit/1f15e34dc8)] - **crypto**: set env values in KeyObject Deserialize method (ThakurKarthik) [#35416](https://github.com/nodejs/node/pull/35416)
- [[`85062b3aad`](https://github.com/nodejs/node/commit/85062b3aad)] - **deps**: update llhttp to 2.1.3 (Fedor Indutny) [#35435](https://github.com/nodejs/node/pull/35435)
- [[`a995dd7a89`](https://github.com/nodejs/node/commit/a995dd7a89)] - **doc**: revise introductory child_process text (Rich Trott) [#35344](https://github.com/nodejs/node/pull/35344)
- [[`6585b161ba`](https://github.com/nodejs/node/commit/6585b161ba)] - **doc**: improve eventLoopUtilization documentation (Andrey Pechkurov) [#35479](https://github.com/nodejs/node/pull/35479)
- [[`f08577c4a6`](https://github.com/nodejs/node/commit/f08577c4a6)] - **doc**: update AUTHORS list (Anna Henningsen) [#35280](https://github.com/nodejs/node/pull/35280)
- [[`91ef862365`](https://github.com/nodejs/node/commit/91ef862365)] - **doc**: mention adding YAML for APIs in PR contributing guide (Denys Otrishko) [#35459](https://github.com/nodejs/node/pull/35459)
- [[`885840b543`](https://github.com/nodejs/node/commit/885840b543)] - **doc**: adopt MDN style for kbd elements (Rich Trott) [#35460](https://github.com/nodejs/node/pull/35460)
- [[`1313c8c33a`](https://github.com/nodejs/node/commit/1313c8c33a)] - **doc**: update sxa's email address to Red Hat from IBM (Stewart X Addison) [#35442](https://github.com/nodejs/node/pull/35442)
- [[`3f95440334`](https://github.com/nodejs/node/commit/3f95440334)] - **doc**: fix conditional exports flag removal version (Antoine du Hamel) [#35428](https://github.com/nodejs/node/pull/35428)
- [[`e40876a5e5`](https://github.com/nodejs/node/commit/e40876a5e5)] - **doc**: specify how to detect EOF (Luigi Pinca) [#35445](https://github.com/nodejs/node/pull/35445)
- [[`541ce17386`](https://github.com/nodejs/node/commit/541ce17386)] - **doc**: add entry to console.timeEnd() changes array (Luigi Pinca) [#35441](https://github.com/nodejs/node/pull/35441)
- [[`38a5715c1a`](https://github.com/nodejs/node/commit/38a5715c1a)] - **doc**: avoid using deprecated connection property (Luigi Pinca) [#35439](https://github.com/nodejs/node/pull/35439)
- [[`862b75d35e`](https://github.com/nodejs/node/commit/862b75d35e)] - **doc**: added details around console.timeEnd changes (Yash Ladha) [#35027](https://github.com/nodejs/node/pull/35027)
- [[`4dbb3a91fe`](https://github.com/nodejs/node/commit/4dbb3a91fe)] - **doc**: copyedit packages.md (Rich Trott) [#35427](https://github.com/nodejs/node/pull/35427)
- [[`368a3ae415`](https://github.com/nodejs/node/commit/368a3ae415)] - **doc**: update contact information for @BethGriggs (Beth Griggs) [#35451](https://github.com/nodejs/node/pull/35451)
- [[`a11ddee8b9`](https://github.com/nodejs/node/commit/a11ddee8b9)] - **doc**: update contact information for richardlau (Richard Lau) [#35450](https://github.com/nodejs/node/pull/35450)
- [[`35b62ef0a7`](https://github.com/nodejs/node/commit/35b62ef0a7)] - **doc**: importable node protocol URLs (Bradley Meck) [#35434](https://github.com/nodejs/node/pull/35434)
- [[`95c62a1dca`](https://github.com/nodejs/node/commit/95c62a1dca)] - **doc**: copyedit esm.md (Rich Trott) [#35414](https://github.com/nodejs/node/pull/35414)
- [[`86f2f83a2f`](https://github.com/nodejs/node/commit/86f2f83a2f)] - **doc**: implement fancier rendering for keys (Rich Trott) [#35400](https://github.com/nodejs/node/pull/35400)
- [[`d6427886b8`](https://github.com/nodejs/node/commit/d6427886b8)] - **doc**: unhide resolver spec (Guy Bedford) [#35358](https://github.com/nodejs/node/pull/35358)
- [[`5a730b5def`](https://github.com/nodejs/node/commit/5a730b5def)] - **doc**: sort md references in ASCII order (Antoine du Hamel) [#35191](https://github.com/nodejs/node/pull/35191)
- [[`ce789f1ca4`](https://github.com/nodejs/node/commit/ce789f1ca4)] - **doc**: use .md extension for internal links (Antoine du Hamel) [#35191](https://github.com/nodejs/node/pull/35191)
- [[`79e3e5008d`](https://github.com/nodejs/node/commit/79e3e5008d)] - **doc**: update example ICU URL (Daijiro Wachi) [#35373](https://github.com/nodejs/node/pull/35373)
- [[`998b24ef17`](https://github.com/nodejs/node/commit/998b24ef17)] - **doc**: align to function signature (anlex N) [#34930](https://github.com/nodejs/node/pull/34930)
- [[`af360ace37`](https://github.com/nodejs/node/commit/af360ace37)] - **doc**: make minor edits for consistency (Rich Trott) [#35377](https://github.com/nodejs/node/pull/35377)
- [[`53e27b3f67`](https://github.com/nodejs/node/commit/53e27b3f67)] - **doc,esm**: add history support info (Antoine du Hamel) [#35395](https://github.com/nodejs/node/pull/35395)
- [[`91b820e939`](https://github.com/nodejs/node/commit/91b820e939)] - **esm**: use "node:" namespace for builtins (Guy Bedford) [#35387](https://github.com/nodejs/node/pull/35387)
- [[`541be526c3`](https://github.com/nodejs/node/commit/541be526c3)] - **fs**: do not throw exception after creating FSReqCallback (Anna Henningsen) [#35487](https://github.com/nodejs/node/pull/35487)
- [[`f29451dece`](https://github.com/nodejs/node/commit/f29451dece)] - **fs**: simplify realpathSync (himself65) [#35413](https://github.com/nodejs/node/pull/35413)
- [[`fcbdb0686d`](https://github.com/nodejs/node/commit/fcbdb0686d)] - **fs**: remove experimental from rmdir recursive (Benjamin Coe) [#35171](https://github.com/nodejs/node/pull/35171)
- [[`e4a4f81d19`](https://github.com/nodejs/node/commit/e4a4f81d19)] - **fs**: use Promise.resolve from primordials (Michaël Zasso) [#35379](https://github.com/nodejs/node/pull/35379)
- [[`8e3a81eed9`](https://github.com/nodejs/node/commit/8e3a81eed9)] - **http2,tls**: store WriteWrap using BaseObjectPtr (Anna Henningsen) [#35488](https://github.com/nodejs/node/pull/35488)
- [[`62ddc77a5b`](https://github.com/nodejs/node/commit/62ddc77a5b)] - **meta**: add nodejs/streams to CODEOWNERS (Matteo Collina) [#35411](https://github.com/nodejs/node/pull/35411)
- [[`23022066ec`](https://github.com/nodejs/node/commit/23022066ec)] - **meta**: add startup team in CODEOWNERS (Joyee Cheung) [#35406](https://github.com/nodejs/node/pull/35406)
- [[`0ac5fa703e`](https://github.com/nodejs/node/commit/0ac5fa703e)] - **module**: update to cjs-module-lexer@0.4.0 (Guy Bedford) [#35501](https://github.com/nodejs/node/pull/35501)
- [[`5c879a97e6`](https://github.com/nodejs/node/commit/5c879a97e6)] - **module**: fix builtin reexport tracing (Guy Bedford) [#35500](https://github.com/nodejs/node/pull/35500)
- [[`f23a0e250c`](https://github.com/nodejs/node/commit/f23a0e250c)] - **module**: refine module type mismatch error cases (Guy Bedford) [#35426](https://github.com/nodejs/node/pull/35426)
- [[`3f62f997a2`](https://github.com/nodejs/node/commit/3f62f997a2)] - **src**: more idiomatic error pattern in node_wasi (James M Snell) [#35493](https://github.com/nodejs/node/pull/35493)
- [[`392c8815fe`](https://github.com/nodejs/node/commit/392c8815fe)] - **src**: use env-\>ThrowUVException in pipe_wrap (James M Snell) [#35493](https://github.com/nodejs/node/pull/35493)
- [[`e09f7f023a`](https://github.com/nodejs/node/commit/e09f7f023a)] - **src**: limit GetProcessTitle() result to 1MB (Anna Henningsen) [#35492](https://github.com/nodejs/node/pull/35492)
- [[`fbb9dd9ac9`](https://github.com/nodejs/node/commit/fbb9dd9ac9)] - **src**: fix aliased buffer import warning in env.h (Yash Ladha) [#35436](https://github.com/nodejs/node/pull/35436)
- [[`7bbf8ee256`](https://github.com/nodejs/node/commit/7bbf8ee256)] - **src**: remove invalid ToLocalChecked in EmitBeforeExit (Anna Henningsen) [#35484](https://github.com/nodejs/node/pull/35484)
- [[`5a85d4f2c6`](https://github.com/nodejs/node/commit/5a85d4f2c6)] - **src**: make MakeCallback() check can_call_into_js before getting method (Anna Henningsen) [#35424](https://github.com/nodejs/node/pull/35424)
- [[`4aed176b68`](https://github.com/nodejs/node/commit/4aed176b68)] - **src**: document required else block at src/node_platform.cc (Juan José Arboleda) [#34688](https://github.com/nodejs/node/pull/34688)
- [[`552ebafd06`](https://github.com/nodejs/node/commit/552ebafd06)] - **test**: update wpt tests for encoding (Daijiro Wachi) [#35330](https://github.com/nodejs/node/pull/35330)
- [[`27cd99b217`](https://github.com/nodejs/node/commit/27cd99b217)] - **test**: improve test coverage for eventtarget (Juan José Arboleda) [#33733](https://github.com/nodejs/node/pull/33733)
- [[`5790c40c32`](https://github.com/nodejs/node/commit/5790c40c32)] - **tools**: add missing uv_setup_argv() calls (Anna Henningsen) [#35491](https://github.com/nodejs/node/pull/35491)
- [[`f499302ac0`](https://github.com/nodejs/node/commit/f499302ac0)] - **tools**: fix typo in error message (Antoine du Hamel) [#35417](https://github.com/nodejs/node/pull/35417)
- [[`5f1d792a09`](https://github.com/nodejs/node/commit/5f1d792a09)] - **tools**: update gyp to v0.5.0 (Ujjwal Sharma) [#32698](https://github.com/nodejs/node/pull/32698)
- [[`ad8fce6e61`](https://github.com/nodejs/node/commit/ad8fce6e61)] - **tools**: update gyp to v0.4.0 (Ujjwal Sharma) [#32698](https://github.com/nodejs/node/pull/32698)
- [[`3e75907dea`](https://github.com/nodejs/node/commit/3e75907dea)] - **tools**: update gyp-next to v0.3.0 (Ujjwal Sharma) [#32698](https://github.com/nodejs/node/pull/32698)
- [[`7361a3fd1a`](https://github.com/nodejs/node/commit/7361a3fd1a)] - **tools**: update gyp-next to v0.2.1 (Ujjwal Sharma) [#32698](https://github.com/nodejs/node/pull/32698)
- [[`190d46bdde`](https://github.com/nodejs/node/commit/190d46bdde)] - **tools**: update gyp to 0.2.0 (Ujjwal Sharma) [#32698](https://github.com/nodejs/node/pull/32698)
- [[`166f14ac98`](https://github.com/nodejs/node/commit/166f14ac98)] - **tools**: check links in api docs (Antoine du Hamel) [#35191](https://github.com/nodejs/node/pull/35191)
- [[`a4e5a3af85`](https://github.com/nodejs/node/commit/a4e5a3af85)] - **tools**: exclude gyp from markdown link checker (Michaël Zasso) [#35423](https://github.com/nodejs/node/pull/35423)
- [[`4d29fb56f2`](https://github.com/nodejs/node/commit/4d29fb56f2)] - **tools**: add pythonenv to .gitignore (Michaël Zasso) [#35389](https://github.com/nodejs/node/pull/35389)
- [[`6e9e5c3381`](https://github.com/nodejs/node/commit/6e9e5c3381)] - **tools,doc**: fix broken link in module.html (Rich Trott) [#35446](https://github.com/nodejs/node/pull/35446)

Windows 32-bit Installer: https://nodejs.org/dist/v14.13.1/node-v14.13.1-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v14.13.1/node-v14.13.1-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v14.13.1/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v14.13.1/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v14.13.1/node-v14.13.1.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v14.13.1/node-v14.13.1-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v14.13.1/node-v14.13.1-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v14.13.1/node-v14.13.1-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v14.13.1/node-v14.13.1-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v14.13.1/node-v14.13.1-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v14.13.1/node-v14.13.1-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v14.13.1/node-v14.13.1-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v14.13.1/node-v14.13.1.tar.gz \
Other release files: https://nodejs.org/dist/v14.13.1/ \
Documentation: https://nodejs.org/docs/v14.13.1/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

1a73710f119692effb1d05307fba02f1834895460935b735af4eb6e2693f987a  node-v14.13.1-aix-ppc64.tar.gz
d7b42f35470e07d27f3c5d9a58ac75de60a2baeb38cdf46831880204fa8b479d  node-v14.13.1-darwin-x64.tar.gz
fe1d877ed5ee4b052c291fc1949c40944ca9519505d06b4c7fb1f4b06dcfa06f  node-v14.13.1-darwin-x64.tar.xz
05650e180dd4f4665cdc852527b6c95182e48689e9a7adaf951c6d5905a57b16  node-v14.13.1-headers.tar.gz
acc692319eda8ac0501a043e990d4ee0fbe3612ab2358caf0818d3e1b74307c4  node-v14.13.1-headers.tar.xz
5ee6da3c86591763644f40babd2bef5a2476e98ddd6f7f1c5121fc2c81d1d613  node-v14.13.1-linux-arm64.tar.gz
ddc90cbd4edcfa25004d2a01d9fefee916b1e1cf1854549dab3a973d492df72e  node-v14.13.1-linux-arm64.tar.xz
b6530873b6787049f155e4434cb0a2f6425928b2c57247419bb103fa81a10a46  node-v14.13.1-linux-armv7l.tar.gz
d314982b8977c5e469b5fb17a67353ab12e7f45989dba54f7a8073407d96761c  node-v14.13.1-linux-armv7l.tar.xz
36cd4da53722f36708fe6ee1acd10ad17fe0d81d29e08c80bbbcfa5d823d99d6  node-v14.13.1-linux-ppc64le.tar.gz
49a5adcdd7126e8b18d3561092245e38be7606760346f89c45d0ab238abd98b1  node-v14.13.1-linux-ppc64le.tar.xz
5eecab3b579daf28e5639eaca5c262ab6fb8d9318d9c8bd207d3c6c1a0a97dc5  node-v14.13.1-linux-s390x.tar.gz
95ad9f6f5fcf9e761bcc8a46cc130e1ba2d848e32d3a238fb09f58cca0a1fd33  node-v14.13.1-linux-s390x.tar.xz
872b8cf72b94109276c61182f7366c8ffdfb58986428c0f57af38cf10a5194a4  node-v14.13.1-linux-x64.tar.gz
d0a87b107f665553ae0665f6f6f1c81187aa077c596f253866b09e0e1e48f981  node-v14.13.1-linux-x64.tar.xz
8c044364eda9282b1ca5ebff6bc289fa23125501ab3648ee4a9fd9d8c34897bf  node-v14.13.1.pkg
f0080d3284ea1585e255a3f459ce151e8106a33f4ce8bed0da15ff99c6a082a5  node-v14.13.1.tar.gz
6061bd1e218c1e1f0a9fbc1643f501e8971d546a37028a26a1ee8ea93f6c15f6  node-v14.13.1.tar.xz
3924cbefe865711bf7f3054141329999ff1cde822a9bcc91ef0031ea62c4d20c  node-v14.13.1-win-x64.7z
a9d590f54a8e384c250e1c40f291b7367e1fdabfed4e9112eece0f8bec97609e  node-v14.13.1-win-x64.zip
9f22d28c22b589eae83cb12d260b315057cc9c2af390491fd8d544f5b830f9d0  node-v14.13.1-win-x86.7z
2d7235a9d5956312d800c19e0603f2ae5689f709d8909481a08b40a944bfd836  node-v14.13.1-win-x86.zip
e1f62afd3c56b529bc85da3f4cebc0cac8ed2b46d4620226f018b47df3772f17  node-v14.13.1-x64.msi
49d9b8ec73340b3de2b39b70eefbcd63f5cd3bcf48a5c5a0369157f475c27dfc  node-v14.13.1-x86.msi
9e9d0466eeb63e6da6b4849165db93408624ddd38c22bd7dcfac77dfd598fa84  win-x64/node.exe
a30524920387674e522728cdb0f34db589889da98ef9bfd769235dd73552cc9c  win-x64/node.lib
8ebdc757a84889ed8004ebaccdac329603076a1bfc744f22aa6c29db03d33a74  win-x64/node_pdb.7z
4c79f69a89c7459d04cb0e2f9c8193ddc75e5f926c800731f1c2d5b8ca2b9f57  win-x64/node_pdb.zip
8402d9cb6efe632db37323562f3d46fb923d315c3bdea8c394b43a4833e44ad2  win-x86/node.exe
831a9165fe3d63afc1d5b5ac2238235f3227cf1bc19b46b5f45e8c4d670c29e7  win-x86/node.lib
2204a17562cdd20cecdd138e4d9cb7fa24305d163a851aafd4497d4eb304492e  win-x86/node_pdb.7z
ea3671230a3829561a99dd31954dc10cae15edc86e829f2d290a4bd8be1f2850  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQEzBAEBCAAdFiEETtd49TnjY0x3nIfG1wYoSKGrAFwFAl9+M/0ACgkQ1wYoSKGr
AFzb7gf+JBrRsyQhbmfnIFqs3BMiZXBvDlyA6B9epOsX2nS9uHOFWlJY5DwOHzxo
PVc4xSmXHYI5GOCQYNSXy/bMG3RTyIa4dCOWjz34QfIMcv6VcTTRLwTrajXflHLL
IfqC3pOJHNT3P37Ski0QD6N82naz56uCh5xXeyGHE5nB3puR4VqcUxK605vQSdKQ
XzTK1s7DHuVMp/JaHuYw1BYu3ZscSXIm3tPZdfA6J6Vcd0sqrKBx0SLrBOALQdzy
HGeZaVqZIRxPYxWF2naNG4fHLV/KgpacPzku2mYc/dAsa68quIVTtwAa7hgSGp12
kYSYqOgSUCSnFfAkGFZtPVPLDDbQiw==
=EqG/
-----END PGP SIGNATURE-----

```
