---
date: '2020-08-27T14:50:50.887Z'
category: release
title: Node v14.9.0 (Current)
layout: blog-post
author: <PERSON>
---

### Notable Changes

- **build**: set --v8-enable-object-print by default (<PERSON>) [#34705](https://github.com/nodejs/node/pull/34705)
- **deps**:
  - upgrade to libuv 1.39.0 (cjihrig) [#34915](https://github.com/nodejs/node/pull/34915)
  - upgrade npm to 6.14.8 (<PERSON><PERSON>) [#34834](https://github.com/nodejs/node/pull/34834)
  - V8: cherry-pick e06ace6b5cdb (<PERSON>) [#34673](https://github.com/nodejs/node/pull/34673)
- **n-api**: handle weak no-finalizer refs correctly (<PERSON>) [#34839](https://github.com/nodejs/node/pull/34839)
- **tools**: add debug entitlements for macOS 10.15+ (<PERSON><PERSON>) [#34378](https://github.com/nodejs/node/pull/34378)

### Commits

- [[`aaa6e43d3c`](https://github.com/nodejs/node/commit/aaa6e43d3c)] - Forces Powershell to use tls1.2 (Bartosz Sosnowski) [#33609](https://github.com/nodejs/node/pull/33609)
- [[`8de6b72efa`](https://github.com/nodejs/node/commit/8de6b72efa)] - **benchmark**: add benchmark script for resourceUsage (Yash Ladha) [#34691](https://github.com/nodejs/node/pull/34691)
- [[`e4450a199f`](https://github.com/nodejs/node/commit/e4450a199f)] - **benchmark**: update function_args addon code (Anna Henningsen) [#34725](https://github.com/nodejs/node/pull/34725)
- [[`332e38433b`](https://github.com/nodejs/node/commit/332e38433b)] - **(SEMVER-MINOR)** **buffer**: alias UInt ➡️ Uint in buffer methods (Anna Henningsen) [#34729](https://github.com/nodejs/node/pull/34729)
- [[`7f0869f963`](https://github.com/nodejs/node/commit/7f0869f963)] - **build**: run link checker in linter workflow (Richard Lau) [#34810](https://github.com/nodejs/node/pull/34810)
- [[`9ca4b2ad5c`](https://github.com/nodejs/node/commit/9ca4b2ad5c)] - **build**: add CODEOWNERS linter action (Mary Marchini) [#34739](https://github.com/nodejs/node/pull/34739)
- [[`bdf26aebb4`](https://github.com/nodejs/node/commit/bdf26aebb4)] - **(SEMVER-MINOR)** **build**: add build flag for OSS-Fuzz integration (davkor) [#34761](https://github.com/nodejs/node/pull/34761)
- [[`d89a83c62c`](https://github.com/nodejs/node/commit/d89a83c62c)] - **build**: move compiling for Windows ARM64 to Tier 2 (João Reis) [#34721](https://github.com/nodejs/node/pull/34721)
- [[`aed82379dd`](https://github.com/nodejs/node/commit/aed82379dd)] - **build**: implement a Commit Queue in Actions (Mary Marchini) [#34112](https://github.com/nodejs/node/pull/34112)
- [[`15c92083b5`](https://github.com/nodejs/node/commit/15c92083b5)] - **build**: set --v8-enable-object-print by default (Mary Marchini) [#34705](https://github.com/nodejs/node/pull/34705)
- [[`201d3d7074`](https://github.com/nodejs/node/commit/201d3d7074)] - **build**: cover all benchmark addons with C++ linter (Anna Henningsen) [#34725](https://github.com/nodejs/node/pull/34725)
- [[`2abc98e9ff`](https://github.com/nodejs/node/commit/2abc98e9ff)] - **build**: add flag to build V8 with OBJECT_PRINT (Mary Marchini) [#32834](https://github.com/nodejs/node/pull/32834)
- [[`6048421726`](https://github.com/nodejs/node/commit/6048421726)] - **build,win**: use x64 Node when building for ARM64 (Dennis Ameling) [#34009](https://github.com/nodejs/node/pull/34009)
- [[`69bcca122e`](https://github.com/nodejs/node/commit/69bcca122e)] - **crypto**: avoid unitializing ECDH objects on error (Tobias Nießen) [#34302](https://github.com/nodejs/node/pull/34302)
- [[`cf348542c6`](https://github.com/nodejs/node/commit/cf348542c6)] - **deps**: upgrade to libuv 1.39.0 (cjihrig) [#34915](https://github.com/nodejs/node/pull/34915)
- [[`68b7a8db6f`](https://github.com/nodejs/node/commit/68b7a8db6f)] - **deps**: upgrade npm to 6.14.8 (Ruy Adorno) [#34834](https://github.com/nodejs/node/pull/34834)
- [[`9527a2a8a7`](https://github.com/nodejs/node/commit/9527a2a8a7)] - **deps**: V8: cherry-pick e06ace6b5cdb (Anna Henningsen) [#34673](https://github.com/nodejs/node/pull/34673)
- [[`cd32522c92`](https://github.com/nodejs/node/commit/cd32522c92)] - **doc**: add missing DEP ID for 'new crypto.Certificate()' (Beth Griggs) [#34940](https://github.com/nodejs/node/pull/34940)
- [[`ff15c92a7f`](https://github.com/nodejs/node/commit/ff15c92a7f)] - **doc**: improve fs doc intro (James M Snell) [#34843](https://github.com/nodejs/node/pull/34843)
- [[`dae93ca0cb`](https://github.com/nodejs/node/commit/dae93ca0cb)] - **doc**: indicate the format of process.version (Danny Guo) [#34872](https://github.com/nodejs/node/pull/34872)
- [[`bf7f492cb6`](https://github.com/nodejs/node/commit/bf7f492cb6)] - **doc**: rename module pages (Antoine du HAMEL) [#34663](https://github.com/nodejs/node/pull/34663)
- [[`f2c2f42195`](https://github.com/nodejs/node/commit/f2c2f42195)] - **doc**: improve wording in deprecations.md (Rich Trott) [#34860](https://github.com/nodejs/node/pull/34860)
- [[`4b3b0e3f98`](https://github.com/nodejs/node/commit/4b3b0e3f98)] - **doc**: fix ESM/CJS wrapper example (Maksim Sinik) [#34853](https://github.com/nodejs/node/pull/34853)
- [[`d6bb2ad5ea`](https://github.com/nodejs/node/commit/d6bb2ad5ea)] - **doc**: adopt Microsoft Style Guide officially (Rich Trott) [#34821](https://github.com/nodejs/node/pull/34821)
- [[`e4679bd45d`](https://github.com/nodejs/node/commit/e4679bd45d)] - **doc**: use 'console' info string for console output (Rich Trott) [#34837](https://github.com/nodejs/node/pull/34837)
- [[`b1c3fb73fc`](https://github.com/nodejs/node/commit/b1c3fb73fc)] - **doc**: fix bulleted list punctuation in BUILDING.md (Rich Trott) [#34849](https://github.com/nodejs/node/pull/34849)
- [[`ef41ddf5cb`](https://github.com/nodejs/node/commit/ef41ddf5cb)] - **doc**: sort references lexically (Rich Trott) [#34848](https://github.com/nodejs/node/pull/34848)
- [[`3133b75b68`](https://github.com/nodejs/node/commit/3133b75b68)] - **doc**: move addaleax to TSC emeritus (Anna Henningsen) [#34809](https://github.com/nodejs/node/pull/34809)
- [[`5214de78cd`](https://github.com/nodejs/node/commit/5214de78cd)] - **doc**: remove space above version picker (Justice Almanzar) [#34768](https://github.com/nodejs/node/pull/34768)
- [[`34430abd71`](https://github.com/nodejs/node/commit/34430abd71)] - **doc**: move module core module doc to separate page (Antoine du HAMEL) [#34747](https://github.com/nodejs/node/pull/34747)
- [[`b356b79ca4`](https://github.com/nodejs/node/commit/b356b79ca4)] - **doc**: reorder deprecated tls docs (Jerome T.K. Covington) [#34687](https://github.com/nodejs/node/pull/34687)
- [[`5c987ffc96`](https://github.com/nodejs/node/commit/5c987ffc96)] - **doc**: fix file name to main.mjs and not main.js in esm.md (Frank Lemanschik) [#34786](https://github.com/nodejs/node/pull/34786)
- [[`969fb1c5e3`](https://github.com/nodejs/node/commit/969fb1c5e3)] - **doc**: improve async_hooks snippets (Andrey Pechkurov) [#34829](https://github.com/nodejs/node/pull/34829)
- [[`3360dcbfab`](https://github.com/nodejs/node/commit/3360dcbfab)] - **doc**: fix some typos and grammar mistakes (Hilla Shahrabani) [#34800](https://github.com/nodejs/node/pull/34800)
- [[`47f2f45dd8`](https://github.com/nodejs/node/commit/47f2f45dd8)] - **doc**: deprecate (doc-only) crypto.Certificate() (Rich Trott) [#34697](https://github.com/nodejs/node/pull/34697)
- [[`3bfe199c28`](https://github.com/nodejs/node/commit/3bfe199c28)] - **doc**: remove "is recommended from crypto legacy API text (Rich Trott) [#34697](https://github.com/nodejs/node/pull/34697)
- [[`258f64f578`](https://github.com/nodejs/node/commit/258f64f578)] - **doc**: edit filehandle.close() entry in fs.md (Rich Trott) [#34782](https://github.com/nodejs/node/pull/34782)
- [[`e54a6842e0`](https://github.com/nodejs/node/commit/e54a6842e0)] - **doc**: fix broken links in commit-queue.md (Luigi Pinca) [#34789](https://github.com/nodejs/node/pull/34789)
- [[`3925fd6550`](https://github.com/nodejs/node/commit/3925fd6550)] - **doc**: avoid \_may\_ in collaborator guide (Rich Trott) [#34749](https://github.com/nodejs/node/pull/34749)
- [[`cb0960635b`](https://github.com/nodejs/node/commit/cb0960635b)] - **doc**: use sentence-casing for headers in collaborator guide (Rich Trott) [#34713](https://github.com/nodejs/node/pull/34713)
- [[`8b5690287c`](https://github.com/nodejs/node/commit/8b5690287c)] - **doc**: edit (general) collaborator guide (Rich Trott) [#34712](https://github.com/nodejs/node/pull/34712)
- [[`b933eef1f3`](https://github.com/nodejs/node/commit/b933eef1f3)] - **doc**: reduce repetitiveness on Consensus Seeking (Mary Marchini) [#34702](https://github.com/nodejs/node/pull/34702)
- [[`f7563f811a`](https://github.com/nodejs/node/commit/f7563f811a)] - **doc**: remove typo in crypto.md (Rich Trott) [#34698](https://github.com/nodejs/node/pull/34698)
- [[`ea98122a51`](https://github.com/nodejs/node/commit/ea98122a51)] - **doc**: n-api environment life cycle APIs are stable (Jim Schlight) [#34641](https://github.com/nodejs/node/pull/34641)
- [[`b00f71b660`](https://github.com/nodejs/node/commit/b00f71b660)] - **doc**: add padding in the sidebar column (Antoine du HAMEL) [#34665](https://github.com/nodejs/node/pull/34665)
- [[`91f53245ae`](https://github.com/nodejs/node/commit/91f53245ae)] - **doc**: use semantically appropriate tag for lines (Antoine du HAMEL) [#34660](https://github.com/nodejs/node/pull/34660)
- [[`230bcaf276`](https://github.com/nodejs/node/commit/230bcaf276)] - **doc**: add HPE_UNEXPECTED_CONTENT_LENGTH error description (Nikolay Krashnikov) [#34596](https://github.com/nodejs/node/pull/34596)
- [[`d29b805569`](https://github.com/nodejs/node/commit/d29b805569)] - **doc**: update http server response 'close' event (Renato Mariscal) [#34472](https://github.com/nodejs/node/pull/34472)
- [[`b93ba07fa5`](https://github.com/nodejs/node/commit/b93ba07fa5)] - **doc**: add writable and readable options to Duplex docs (Priyank Singh) [#34383](https://github.com/nodejs/node/pull/34383)
- [[`7cde699115`](https://github.com/nodejs/node/commit/7cde699115)] - **doc**: harden policy around objections (Mary Marchini) [#34639](https://github.com/nodejs/node/pull/34639)
- [[`7d0970ca66`](https://github.com/nodejs/node/commit/7d0970ca66)] - **doc,lib**: remove unused error code (Rich Trott) [#34792](https://github.com/nodejs/node/pull/34792)
- [[`9ebae0a758`](https://github.com/nodejs/node/commit/9ebae0a758)] - **doc,n-api**: add link to n-api tutorial website (Jim Schlight) [#34870](https://github.com/nodejs/node/pull/34870)
- [[`cdd4540124`](https://github.com/nodejs/node/commit/cdd4540124)] - **doc,tools**: annotate broken links in actions workflow (Richard Lau) [#34810](https://github.com/nodejs/node/pull/34810)
- [[`dbcb36d553`](https://github.com/nodejs/node/commit/dbcb36d553)] - **errors**: improve ERR_INVALID_OPT_VALUE error (Denys Otrishko) [#34671](https://github.com/nodejs/node/pull/34671)
- [[`8f38c19c08`](https://github.com/nodejs/node/commit/8f38c19c08)] - **esm**: improve error message of ERR_UNSUPPORTED_ESM_URL_SCHEME (Denys Otrishko) [#34795](https://github.com/nodejs/node/pull/34795)
- [[`7ef5591d06`](https://github.com/nodejs/node/commit/7ef5591d06)] - **fs**: guard against undefined behavior (Robert Nagy) [#34746](https://github.com/nodejs/node/pull/34746)
- [[`952f233e39`](https://github.com/nodejs/node/commit/952f233e39)] - **http**: add RFC references for each status code (Voltra) [#33671](https://github.com/nodejs/node/pull/33671)
- [[`cc7258469c`](https://github.com/nodejs/node/commit/cc7258469c)] - **http2**: fix Http2Response.sendDate (João Lucas Lucchetta) [#34850](https://github.com/nodejs/node/pull/34850)
- [[`9e0d18fd3f`](https://github.com/nodejs/node/commit/9e0d18fd3f)] - **http2**: use and support non-empty DATA frame with END_STREAM flag (Carlos Lopez) [#33875](https://github.com/nodejs/node/pull/33875)
- [[`6ee2578427`](https://github.com/nodejs/node/commit/6ee2578427)] - **http2**: add maxHeaderSize option to http2 (Priyank Singh) [#33636](https://github.com/nodejs/node/pull/33636)
- [[`04defbaacd`](https://github.com/nodejs/node/commit/04defbaacd)] - **lib**: allow to validate enums with validateOneOf (Denys Otrishko) [#34070](https://github.com/nodejs/node/pull/34070)
- [[`1a9496a79d`](https://github.com/nodejs/node/commit/1a9496a79d)] - **lib**: add UNC support to url.pathToFileURL() (Matthew McEachen) [#34743](https://github.com/nodejs/node/pull/34743)
- [[`124a01d487`](https://github.com/nodejs/node/commit/124a01d487)] - **lib**: use full URL to GitHub issues in comments (Rich Trott) [#34686](https://github.com/nodejs/node/pull/34686)
- [[`756c058c45`](https://github.com/nodejs/node/commit/756c058c45)] - **meta**: fix codeowners docs path (Mary Marchini) [#34811](https://github.com/nodejs/node/pull/34811)
- [[`2781f646c9`](https://github.com/nodejs/node/commit/2781f646c9)] - **meta**: add TSC as owner of governance-related docs (Mary Marchini) [#34737](https://github.com/nodejs/node/pull/34737)
- [[`a69d30eb3f`](https://github.com/nodejs/node/commit/a69d30eb3f)] - **module**: drop `-u` alias for `--conditions` (Richard Lau) [#34935](https://github.com/nodejs/node/pull/34935)
- [[`e4a0e5bc1a`](https://github.com/nodejs/node/commit/e4a0e5bc1a)] - **module**: fix check for package.json at volume root (Derek Lewis) [#34595](https://github.com/nodejs/node/pull/34595)
- [[`698cae7625`](https://github.com/nodejs/node/commit/698cae7625)] - **module**: share CJS/ESM resolver fns, refactoring (Guy Bedford) [#34744](https://github.com/nodejs/node/pull/34744)
- [[`6929649793`](https://github.com/nodejs/node/commit/6929649793)] - **module**: custom --conditions flag option (Guy Bedford) [#34637](https://github.com/nodejs/node/pull/34637)
- [[`9a7c87df37`](https://github.com/nodejs/node/commit/9a7c87df37)] - **module**: use cjsCache over esm injection (Guy Bedford) [#34605](https://github.com/nodejs/node/pull/34605)
- [[`98f7d8ec81`](https://github.com/nodejs/node/commit/98f7d8ec81)] - **n-api**: handle weak no-finalizer refs correctly (Gabriel Schulhof) [#34839](https://github.com/nodejs/node/pull/34839)
- [[`90abdd3dd4`](https://github.com/nodejs/node/commit/90abdd3dd4)] - **net**: validate custom lookup() output (cjihrig) [#34813](https://github.com/nodejs/node/pull/34813)
- [[`84031183bc`](https://github.com/nodejs/node/commit/84031183bc)] - **policy**: support conditions for redirects (Bradley Farias) [#34414](https://github.com/nodejs/node/pull/34414)
- [[`a16f0f427e`](https://github.com/nodejs/node/commit/a16f0f427e)] - **process**: correctly parse Unicode in NODE_OPTIONS (Bartosz Sosnowski) [#34476](https://github.com/nodejs/node/pull/34476)
- [[`fff1e7f86c`](https://github.com/nodejs/node/commit/fff1e7f86c)] - **src**: fix abort on uv_loop_init() failure (Ben Noordhuis) [#34874](https://github.com/nodejs/node/pull/34874)
- [[`7666d95c7d`](https://github.com/nodejs/node/commit/7666d95c7d)] - **src**: usage of modernize-use-equals-default (Yash Ladha) [#34807](https://github.com/nodejs/node/pull/34807)
- [[`3022e0d614`](https://github.com/nodejs/node/commit/3022e0d614)] - **src**: prefer C++ empty() in boolean expressions (Tobias Nießen) [#34432](https://github.com/nodejs/node/pull/34432)
- [[`e16b3e72f9`](https://github.com/nodejs/node/commit/e16b3e72f9)] - **test**: fix test-cluster-net-listen-relative-path.js to run in / (Rich Trott) [#34820](https://github.com/nodejs/node/pull/34820)
- [[`2a78c33445`](https://github.com/nodejs/node/commit/2a78c33445)] - **test**: run REPL preview test regardless of terminal type (Rich Trott) [#34798](https://github.com/nodejs/node/pull/34798)
- [[`6b45bf3475`](https://github.com/nodejs/node/commit/6b45bf3475)] - **test**: modernize test-cluster-master-error (Anna Henningsen) [#34685](https://github.com/nodejs/node/pull/34685)
- [[`c080fc590d`](https://github.com/nodejs/node/commit/c080fc590d)] - **test**: move test-inspector-already-activated-cli to parallel (Rich Trott) [#34755](https://github.com/nodejs/node/pull/34755)
- [[`7ed7ef7ad8`](https://github.com/nodejs/node/commit/7ed7ef7ad8)] - **test**: move execution of WPT to worker threads (Michaël Zasso) [#34796](https://github.com/nodejs/node/pull/34796)
- [[`e8eed5c426`](https://github.com/nodejs/node/commit/e8eed5c426)] - **test**: convert assertion that always fails to assert.fail() (Rich Trott) [#34793](https://github.com/nodejs/node/pull/34793)
- [[`c458e8406e`](https://github.com/nodejs/node/commit/c458e8406e)] - **test**: remove common.rootDir (Rich Trott) [#34772](https://github.com/nodejs/node/pull/34772)
- [[`1c324d5939`](https://github.com/nodejs/node/commit/1c324d5939)] - **test**: allow ENOENT in test-worker-init-failure (Rich Trott) [#34769](https://github.com/nodejs/node/pull/34769)
- [[`88919e584b`](https://github.com/nodejs/node/commit/88919e584b)] - **test**: allow ENFILE in test-worker-init-failure (Rich Trott) [#34769](https://github.com/nodejs/node/pull/34769)
- [[`a78c638fc3`](https://github.com/nodejs/node/commit/a78c638fc3)] - **test**: use process.env.PYTHON to spawn python (Anna Henningsen) [#34700](https://github.com/nodejs/node/pull/34700)
- [[`9a790203ed`](https://github.com/nodejs/node/commit/9a790203ed)] - **test**: remove error message checking in test-worker-init-failure (Rich Trott) [#34727](https://github.com/nodejs/node/pull/34727)
- [[`0472d1629a`](https://github.com/nodejs/node/commit/0472d1629a)] - **test**: skip node-api/test_worker_terminate_finalization (Anna Henningsen) [#34732](https://github.com/nodejs/node/pull/34732)
- [[`8e91f3ec0a`](https://github.com/nodejs/node/commit/8e91f3ec0a)] - **test**: fix test_worker_terminate_finalization (Anna Henningsen) [#34726](https://github.com/nodejs/node/pull/34726)
- [[`fd5153c822`](https://github.com/nodejs/node/commit/fd5153c822)] - **test**: split test-crypto-dh-hash (Rich Trott) [#34631](https://github.com/nodejs/node/pull/34631)
- [[`9f0917e656`](https://github.com/nodejs/node/commit/9f0917e656)] - **test**: use block-scoping in test/pummel/test-timers.js (Rich Trott) [#34630](https://github.com/nodejs/node/pull/34630)
- [[`b261895d2b`](https://github.com/nodejs/node/commit/b261895d2b)] - **test**: remove test-child-process-fork-args flaky designation (Rich Trott) [#34684](https://github.com/nodejs/node/pull/34684)
- [[`27c0653517`](https://github.com/nodejs/node/commit/27c0653517)] - **test**: add vm crash regression test (Anna Henningsen) [#34673](https://github.com/nodejs/node/pull/34673)
- [[`093a4b0ae4`](https://github.com/nodejs/node/commit/093a4b0ae4)] - **test**: add tests for validateNumber/validateString (Denys Otrishko) [#34672](https://github.com/nodejs/node/pull/34672)
- [[`5009d82b0c`](https://github.com/nodejs/node/commit/5009d82b0c)] - **test,doc**: add missing uv_setup_args() calls (cjihrig) [#34751](https://github.com/nodejs/node/pull/34751)
- [[`cca0372022`](https://github.com/nodejs/node/commit/cca0372022)] - **(SEMVER-MINOR)** **timers**: allow timers to be used as primitives (Denys Otrishko) [#34017](https://github.com/nodejs/node/pull/34017)
- [[`e90cb49390`](https://github.com/nodejs/node/commit/e90cb49390)] - **tls**: enable renegotiation when using BoringSSL (Jeremy Rose) [#34832](https://github.com/nodejs/node/pull/34832)
- [[`8766b5bfd5`](https://github.com/nodejs/node/commit/8766b5bfd5)] - **tools**: add debug entitlements for macOS 10.15+ (Gabriele Greco) [#34378](https://github.com/nodejs/node/pull/34378)
- [[`77bbd73919`](https://github.com/nodejs/node/commit/77bbd73919)] - **util**: add debug and debuglog.enabled (Bradley Farias) [#33424](https://github.com/nodejs/node/pull/33424)
- [[`513ab0e02f`](https://github.com/nodejs/node/commit/513ab0e02f)] - **worker**: fix --abort-on-uncaught-exception handling (Anna Henningsen) [#34724](https://github.com/nodejs/node/pull/34724)
- [[`03d601344a`](https://github.com/nodejs/node/commit/03d601344a)] - **worker**: do not crash when JSTransferable lists untransferable value (Anna Henningsen) [#34766](https://github.com/nodejs/node/pull/34766)
- [[`b73943e476`](https://github.com/nodejs/node/commit/b73943e476)] - **workers**: add support for data: URLs (Antoine du HAMEL) [#34584](https://github.com/nodejs/node/pull/34584)

Windows 32-bit Installer: https://nodejs.org/dist/v14.9.0/node-v14.9.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v14.9.0/node-v14.9.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v14.9.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v14.9.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v14.9.0/node-v14.9.0.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v14.9.0/node-v14.9.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v14.9.0/node-v14.9.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v14.9.0/node-v14.9.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v14.9.0/node-v14.9.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v14.9.0/node-v14.9.0-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v14.9.0/node-v14.9.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v14.9.0/node-v14.9.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v14.9.0/node-v14.9.0.tar.gz \
Other release files: https://nodejs.org/dist/v14.9.0/ \
Documentation: https://nodejs.org/docs/v14.9.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

d17443e0fa87e1edf441eb134bec95254e2f329c079a29fa8989efa4b3632452  node-v14.9.0-aix-ppc64.tar.gz
8427e07e3ca70d6ccf5274dde535c9a42b7f873f5a086323eaf2406cdb324daf  node-v14.9.0-darwin-x64.tar.gz
e382aecabda1bbb92e87b9797e8edfee9480a1dd1580d2b79b09a6b70da73efc  node-v14.9.0-darwin-x64.tar.xz
5f035b4cf39630d3dc31ad88d88adf31a3e9d770e019aeeb2ba07095a0b12a25  node-v14.9.0-headers.tar.gz
9d915fbd3a6c331e828126f418b82f64817e9f6cffa8b680859e6c56a678f887  node-v14.9.0-headers.tar.xz
6619a69ffe95c602105484bdecbdccb319e1c0db861203bffb9b6aedfae2c2df  node-v14.9.0-linux-arm64.tar.gz
f87573c3c44415b8c8333d37d59adebe762608abc81c2fc42a44fe5fdca16abd  node-v14.9.0-linux-arm64.tar.xz
b7fb1759653a510b62adf4b616adc853cd60c872588365a0bbb81ab192bc06ba  node-v14.9.0-linux-armv7l.tar.gz
85f48b539c2785c56c2aca82b398c9332991dd4b5556e5d52b9a382e1b5306eb  node-v14.9.0-linux-armv7l.tar.xz
a51b5f041af515effb9766b3f431938ed4969b73edec73f55b85513dfd6497be  node-v14.9.0-linux-ppc64le.tar.gz
d4a405866da2fd0412992dde2c37bed8b6858086958292e772d651a79ec3a486  node-v14.9.0-linux-ppc64le.tar.xz
83ee502df2ef046579391054b791b9e07f21f728a5724d4c261a0f5b33a2f858  node-v14.9.0-linux-s390x.tar.gz
5444e2457dfe797968db1db05fc59d43b1b0f0a35ceb48d9f7c955136f113c1c  node-v14.9.0-linux-s390x.tar.xz
78b9e06c40a34ae1b7e0540bc3667459ed6439bbc4deff0bbe13f32817e8ac9c  node-v14.9.0-linux-x64.tar.gz
ded70899f43cf8138f88b838aecff5045e763bcab91c4b7f57fe5b69c6722df4  node-v14.9.0-linux-x64.tar.xz
099f4fdc95385f3723a70728aac669a676552cb8c165d161b6d2e73b7dd598ff  node-v14.9.0.pkg
413d91191bd69ce1cfdb956116461db4f70f2019f10f78802db545ad3e341b39  node-v14.9.0.tar.gz
012ef6b715306a56183696a878a4803c2edab4f25f1bf3f40425320cf28e6ef6  node-v14.9.0.tar.xz
1c42cf28308fa04acd25d5059f2a4b8f3713b1f78766967f41c906be66b899ec  node-v14.9.0-win-x64.7z
bcd3fc61739e7ac9a4b6103da3fe5f8c9e310b7b0f1b1f0200d5a4b5dd65d723  node-v14.9.0-win-x64.zip
d92add01eb8e92d8dcd1e3c5998ce13179c117106a4a223a906196bf4316fdbb  node-v14.9.0-win-x86.7z
9dfc89b3088e4c5dbd42b032dfe2a8ce26a4a39140a58a399284f6e41ea39ba5  node-v14.9.0-win-x86.zip
d0a4715418f36e0e19abc69393152c296960defa880a037d5f5525ae755e6dd7  node-v14.9.0-x64.msi
9ab8a145b16c8380224390c7817c07a9a728a7ccfe0d5deda1fab18410282096  node-v14.9.0-x86.msi
e02de67844c9807e4c2fd6b1a3e1ffb4bc47177543d959bdd0c6e23c4370eaf1  win-x64/node.exe
a1968370c38c54d5eb66996c077c6b450cb8c3abde37b75148fe5f0ca632c27e  win-x64/node.lib
f3251dea864229161cb7935673d40c613f91ca2ab4e0cd83bac8d26892166206  win-x64/node_pdb.7z
36d5ab08019997d2a4e8f2a209337215f5fc6b61eb26d793e461362b28a9bd23  win-x64/node_pdb.zip
8635d20d4ce64fef848cc37350c75ce3e1a648cc3512bc15125a0fa018e05219  win-x86/node.exe
96d8a893d60b725a41477f9d5448adaf84d1be56ab8c05ca32605ffcf6c65c48  win-x86/node.lib
e1dd69e24bb44552b2662438bb125ba2f582033937996719332a89218f67f917  win-x86/node_pdb.7z
a194308781f8e10266b0905edda03038c42092506a3cf6c11b1fc6c6ba27df23  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQEzBAEBCAAdFiEETtd49TnjY0x3nIfG1wYoSKGrAFwFAl9HyAsACgkQ1wYoSKGr
AFw/8ggAuFnu0O9HKa+kNsNciSPO7zYxPElGJh+yUuX9HW53lOgGPdaOGSMLwZjF
rkoMfkzdXkEn+M7EuiMRK3tPlqIq3fBOENQUHEsVm64kt+jp0/XUE17i6OR44+g2
2Sz9QMCYKglZbSqO8Mv40fEfmkx62dm7I33Z+E2NDx8NWG8CNsoKdSWtFA9u+tu2
+BFwzTuYkJpISqk/t47k6S5vKl6Hr8nrvkx57jaRFHE3S/NpHUNhETdTo/ASlM1a
2NPqJrrSdcsAH3Tb87VxvysRY7pVm2BGFeLvHRUxmucb0fGAUDZo8umB/46SMXWr
E94jllOwguStZ6aQcm2HV5njZ+bYug==
=pcZo
-----END PGP SIGNATURE-----

```
