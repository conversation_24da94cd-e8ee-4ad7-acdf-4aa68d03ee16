---
date: '2018-06-20T18:48:28.677Z'
category: release
title: Node v10.5.0 (Current)
layout: blog-post
author: <PERSON><PERSON><PERSON>
---

### Notable Changes

- **crypto**:
  - Support for `crypto.scrypt()` has been added. [#20816](https://github.com/nodejs/node/pull/20816)
- **fs**:
  - BigInt support has been added to `fs.stat` and `fs.watchFile`. [#20220](https://github.com/nodejs/node/pull/20220)
  - APIs that take `mode` as arguments no longer throw on values larger than
    `0o777`. [#20636](https://github.com/nodejs/node/pull/20636) [#20975](https://github.com/nodejs/node/pull/20975) (Fixes: [#20498](https://github.com/nodejs/node/issues/20498))
  - Fix crashes in closed event watchers. [#20985](https://github.com/nodejs/node/pull/20985) (Fixes: [#20297](https://github.com/nodejs/node/issues/20297))
- **Worker Threads**:
  - Support for multi-threading has been added behind the
    `--experimental-worker` flag in the `worker_threads` module. This feature
    is _experimental_ and may receive breaking changes at any time. [#20876](https://github.com/nodejs/node/pull/20876)

### Commits

- [[`a6986fe8b6`](https://github.com/nodejs/node/commit/a6986fe8b6)] - **async_hooks**: remove deprecated example (Mathias Buus) [#20998](https://github.com/nodejs/node/pull/20998)
- [[`4b9817bf1e`](https://github.com/nodejs/node/commit/4b9817bf1e)] - **benchmark**: disable only the ESLint rule needing it (Rich Trott) [#21133](https://github.com/nodejs/node/pull/21133)
- [[`ecba1c57b1`](https://github.com/nodejs/node/commit/ecba1c57b1)] - **(SEMVER-MINOR)** **benchmark**: port cluster/echo to worker (Timothy Gu) [#20876](https://github.com/nodejs/node/pull/20876)
- [[`02adb2d62c`](https://github.com/nodejs/node/commit/02adb2d62c)] - **(SEMVER-MINOR)** **build**: expose openssl scrypt functions to addons (Ben Noordhuis) [#20816](https://github.com/nodejs/node/pull/20816)
- [[`c3fbac432f`](https://github.com/nodejs/node/commit/c3fbac432f)] - **build**: install markdown linter for travis (Richard Lau) [#21215](https://github.com/nodejs/node/pull/21215)
- [[`896017b134`](https://github.com/nodejs/node/commit/896017b134)] - **build**: build addon tests in parallel (Anna Henningsen) [#21155](https://github.com/nodejs/node/pull/21155)
- [[`76927fc734`](https://github.com/nodejs/node/commit/76927fc734)] - **build**: stop distclean from deleting v8 files (Ujjwal Sharma) [#21164](https://github.com/nodejs/node/pull/21164)
- [[`b044256f2a`](https://github.com/nodejs/node/commit/b044256f2a)] - **build**: use LC_ALL of C for maximum compatibility (Rich Trott) [#21222](https://github.com/nodejs/node/pull/21222)
- [[`78c7d666fb`](https://github.com/nodejs/node/commit/78c7d666fb)] - **build**: don't change locale on smartos (Refael Ackermann) [#21220](https://github.com/nodejs/node/pull/21220)
- [[`c688a00a6d`](https://github.com/nodejs/node/commit/c688a00a6d)] - **build**: fix 'gas_version' check on localized environments (Evandro Oliveira) [#20394](https://github.com/nodejs/node/pull/20394)
- [[`79b3423fb5`](https://github.com/nodejs/node/commit/79b3423fb5)] - **build**: initial .travis.yml implementation (Anna Henningsen) [#21059](https://github.com/nodejs/node/pull/21059)
- [[`ea4be72f22`](https://github.com/nodejs/node/commit/ea4be72f22)] - **child_process**: swallow errors in internal communication (Anatoli Papirovski) [#21108](https://github.com/nodejs/node/pull/21108)
- [[`9981220e2a`](https://github.com/nodejs/node/commit/9981220e2a)] - **crypto**: fix behavior of createCipher in wrap mode (Tobias Nießen) [#21287](https://github.com/nodejs/node/pull/21287)
- [[`d0cb9cbb35`](https://github.com/nodejs/node/commit/d0cb9cbb35)] - **(SEMVER-MINOR)** **crypto**: drop Math.pow(), use static exponentation (Ben Noordhuis) [#20816](https://github.com/nodejs/node/pull/20816)
- [[`2d9c3cc89d`](https://github.com/nodejs/node/commit/2d9c3cc89d)] - **(SEMVER-MINOR)** **crypto**: refactor randomBytes() (Ben Noordhuis) [#20816](https://github.com/nodejs/node/pull/20816)
- [[`6262fa44d6`](https://github.com/nodejs/node/commit/6262fa44d6)] - **(SEMVER-MINOR)** **crypto**: refactor pbkdf2() and pbkdf2Sync() methods (Ben Noordhuis) [#20816](https://github.com/nodejs/node/pull/20816)
- [[`c9b4592dbf`](https://github.com/nodejs/node/commit/c9b4592dbf)] - **(SEMVER-MINOR)** **crypto**: add scrypt() and scryptSync() methods (Ben Noordhuis) [#20816](https://github.com/nodejs/node/pull/20816)
- [[`495756264a`](https://github.com/nodejs/node/commit/495756264a)] - **(SEMVER-MINOR)** **crypto**: DRY type checking (Ben Noordhuis) [#20816](https://github.com/nodejs/node/pull/20816)
- [[`e4a7e0d28b`](https://github.com/nodejs/node/commit/e4a7e0d28b)] - **deps**: float ea7abee from openssl / CVE-2018-0732 (Rod Vagg) [#21282](https://github.com/nodejs/node/pull/21282)
- [[`0b90b071c4`](https://github.com/nodejs/node/commit/0b90b071c4)] - **deps**: Upgrade node-inspect to 1.11.5 (Jan Krems) [#21055](https://github.com/nodejs/node/pull/21055)
- [[`ffc29c12da`](https://github.com/nodejs/node/commit/ffc29c12da)] - **deps**: patch V8 to 6.7.288.46 (Myles Borins) [#21260](https://github.com/nodejs/node/pull/21260)
- [[`14bb905d18`](https://github.com/nodejs/node/commit/14bb905d18)] - **deps**: V8: cherry-pick a440efb27f from upstream (Yang Guo) [#21022](https://github.com/nodejs/node/pull/21022)
- [[`65b9c427ac`](https://github.com/nodejs/node/commit/65b9c427ac)] - **dns**: improve setServers() errors and performance (Jamie Davis) [#20445](https://github.com/nodejs/node/pull/20445)
- [[`bc20ec0c0f`](https://github.com/nodejs/node/commit/bc20ec0c0f)] - **doc**: eliminate \_you\_ from N-API doc (Rich Trott) [#21382](https://github.com/nodejs/node/pull/21382)
- [[`318d6831bf`](https://github.com/nodejs/node/commit/318d6831bf)] - **doc**: use imperative in COLLABORATOR_GUIDE (Rich Trott) [#21340](https://github.com/nodejs/node/pull/21340)
- [[`177a7c06a8`](https://github.com/nodejs/node/commit/177a7c06a8)] - **doc**: remove obsolete wiki references from BUILDING (Rich Trott) [#21369](https://github.com/nodejs/node/pull/21369)
- [[`15023df050`](https://github.com/nodejs/node/commit/15023df050)] - **doc**: add davisjam to collaborators (Jamie Davis) [#21273](https://github.com/nodejs/node/pull/21273)
- [[`17c21b67ac`](https://github.com/nodejs/node/commit/17c21b67ac)] - **doc**: fix indentation in console.md (Vse Mozhet Byt) [#21367](https://github.com/nodejs/node/pull/21367)
- [[`ef74368416`](https://github.com/nodejs/node/commit/ef74368416)] - **doc**: fix heading of optional console method args (Michaël Zasso) [#21311](https://github.com/nodejs/node/pull/21311)
- [[`4f17841c20`](https://github.com/nodejs/node/commit/4f17841c20)] - **doc**: use Class Method label consistently (Rich Trott) [#21357](https://github.com/nodejs/node/pull/21357)
- [[`4566ebacf4`](https://github.com/nodejs/node/commit/4566ebacf4)] - **doc**: wrap style guide at 80 characters (Rich Trott) [#21361](https://github.com/nodejs/node/pull/21361)
- [[`6c41f33571`](https://github.com/nodejs/node/commit/6c41f33571)] - **doc**: wrap pull-requests.md at 80 characters (Rich Trott) [#21361](https://github.com/nodejs/node/pull/21361)
- [[`b8213f17cc`](https://github.com/nodejs/node/commit/b8213f17cc)] - **doc**: remove linking of url text to url (Rich Trott) [#21361](https://github.com/nodejs/node/pull/21361)
- [[`3f78220c2b`](https://github.com/nodejs/node/commit/3f78220c2b)] - **doc**: correct styling of \_GitHub\_ in onboarding doc (Rich Trott) [#21361](https://github.com/nodejs/node/pull/21361)
- [[`9e994cb119`](https://github.com/nodejs/node/commit/9e994cb119)] - **doc**: wrap releases.md at 80 chars (Rich Trott) [#21361](https://github.com/nodejs/node/pull/21361)
- [[`e00e5e6d5d`](https://github.com/nodejs/node/commit/e00e5e6d5d)] - **doc**: switch the order of Writable and Readable (Joseph Gordon) [#21333](https://github.com/nodejs/node/pull/21333)
- [[`e1b571d6b7`](https://github.com/nodejs/node/commit/e1b571d6b7)] - **doc**: make Deprecation cycle explanation more brief (Rich Trott) [#21303](https://github.com/nodejs/node/pull/21303)
- [[`df0f7a3b4d`](https://github.com/nodejs/node/commit/df0f7a3b4d)] - **doc**: clarify async execute callback usage (Michael Dawson) [#21217](https://github.com/nodejs/node/pull/21217)
- [[`c5a65594ef`](https://github.com/nodejs/node/commit/c5a65594ef)] - **doc**: move 5 collaborators to emeritus status (Rich Trott) [#21272](https://github.com/nodejs/node/pull/21272)
- [[`c1d53f86f8`](https://github.com/nodejs/node/commit/c1d53f86f8)] - **doc**: update NODE_OPTIONS section in cli.md (Vse Mozhet Byt) [#21229](https://github.com/nodejs/node/pull/21229)
- [[`13fd09bfa7`](https://github.com/nodejs/node/commit/13fd09bfa7)] - **doc**: add build wg info to releases.md (Jon Moss) [#21275](https://github.com/nodejs/node/pull/21275)
- [[`0da910f9a5`](https://github.com/nodejs/node/commit/0da910f9a5)] - **doc**: move Italo A. Casas to Release Emeritus (Myles Borins) [#21315](https://github.com/nodejs/node/pull/21315)
- [[`6f7de0b8d9`](https://github.com/nodejs/node/commit/6f7de0b8d9)] - **doc**: trim deprecation level definition text (Rich Trott) [#21241](https://github.com/nodejs/node/pull/21241)
- [[`dd2fc90dcf`](https://github.com/nodejs/node/commit/dd2fc90dcf)] - **doc**: fix reference to workerData in worker_threads (Jeremiah Senkpiel) [#21180](https://github.com/nodejs/node/pull/21180)
- [[`5e46c16371`](https://github.com/nodejs/node/commit/5e46c16371)] - **doc**: fix type in stream doc (Aliaksei Tuzik) [#21178](https://github.com/nodejs/node/pull/21178)
- [[`85dc9ac418`](https://github.com/nodejs/node/commit/85dc9ac418)] - **doc**: add Michaël Zasso to Release team (Michaël Zasso) [#21114](https://github.com/nodejs/node/pull/21114)
- [[`5fa5ab6c48`](https://github.com/nodejs/node/commit/5fa5ab6c48)] - **doc**: naming function as suggested in addon docs (Tommaso Allevi) [#21067](https://github.com/nodejs/node/pull/21067)
- [[`fe5d35123b`](https://github.com/nodejs/node/commit/fe5d35123b)] - **(SEMVER-MINOR)** **doc**: document BigInt support in fs.Stats (Joyee Cheung) [#20220](https://github.com/nodejs/node/pull/20220)
- [[`2c4f80ffba`](https://github.com/nodejs/node/commit/2c4f80ffba)] - **doc**: remove spaces around slashes (Rich Trott) [#21140](https://github.com/nodejs/node/pull/21140)
- [[`72e7e1da2d`](https://github.com/nodejs/node/commit/72e7e1da2d)] - **doc**: alphabetize tls options (Rich Trott) [#21139](https://github.com/nodejs/node/pull/21139)
- [[`06ac81e786`](https://github.com/nodejs/node/commit/06ac81e786)] - **doc**: streamline errors.md introductory material (Rich Trott) [#21138](https://github.com/nodejs/node/pull/21138)
- [[`73b8975b41`](https://github.com/nodejs/node/commit/73b8975b41)] - **doc**: simplify deprecation language (Rich Trott) [#21136](https://github.com/nodejs/node/pull/21136)
- [[`6caa354377`](https://github.com/nodejs/node/commit/6caa354377)] - **(SEMVER-MINOR)** **doc**: explain Worker semantics in async_hooks.md (Anna Henningsen) [#20876](https://github.com/nodejs/node/pull/20876)
- [[`9f9355d6d2`](https://github.com/nodejs/node/commit/9f9355d6d2)] - **doc**: fix inconsistent documentation (host vs hostname) (Davis Okoth) [#20933](https://github.com/nodejs/node/pull/20933)
- [[`a5c571424a`](https://github.com/nodejs/node/commit/a5c571424a)] - **doc**: document file mode caveats on Windows (Joyee Cheung) [#20636](https://github.com/nodejs/node/pull/20636)
- [[`a75e44d135`](https://github.com/nodejs/node/commit/a75e44d135)] - **esm**: ensure require.main for CJS top-level loads (Guy Bedford) [#21150](https://github.com/nodejs/node/pull/21150)
- [[`04e8f0749e`](https://github.com/nodejs/node/commit/04e8f0749e)] - **(SEMVER-MINOR)** **fs**: support BigInt in fs.\*stat and fs.watchFile (Joyee Cheung) [#20220](https://github.com/nodejs/node/pull/20220)
- [[`c09bfd81b7`](https://github.com/nodejs/node/commit/c09bfd81b7)] - **fs**: do not crash when using a closed fs event watcher (Joyee Cheung) [#20985](https://github.com/nodejs/node/pull/20985)
- [[`bacb2cb550`](https://github.com/nodejs/node/commit/bacb2cb550)] - **fs**: refactor fs module (James M Snell) [#20764](https://github.com/nodejs/node/pull/20764)
- [[`db0bb5214a`](https://github.com/nodejs/node/commit/db0bb5214a)] - **fs**: improve fchmod{Sync} validation (cjihrig) [#20588](https://github.com/nodejs/node/pull/20588)
- [[`2ffb9d6b5c`](https://github.com/nodejs/node/commit/2ffb9d6b5c)] - **fs**: drop duplicate API in promises mode (Сковорода Никита Андреевич) [#20559](https://github.com/nodejs/node/pull/20559)
- [[`fc0b3610e2`](https://github.com/nodejs/node/commit/fc0b3610e2)] - **fs**: don't limit ftruncate() length to 32 bits (cjihrig) [#20851](https://github.com/nodejs/node/pull/20851)
- [[`469baa062e`](https://github.com/nodejs/node/commit/469baa062e)] - **fs**: add length validation to fs.truncate() (cjihrig) [#20851](https://github.com/nodejs/node/pull/20851)
- [[`6aade4a765`](https://github.com/nodejs/node/commit/6aade4a765)] - **http**: remove a pair of outdated comments (Mark S. Everitt) [#21214](https://github.com/nodejs/node/pull/21214)
- [[`bcaf59c739`](https://github.com/nodejs/node/commit/bcaf59c739)] - **http2**: fix memory leak for uncommon headers (Anna Henningsen) [#21336](https://github.com/nodejs/node/pull/21336)
- [[`dee250fd77`](https://github.com/nodejs/node/commit/dee250fd77)] - **http2**: safer Http2Session destructor (Anatoli Papirovski) [#21194](https://github.com/nodejs/node/pull/21194)
- [[`296fd57324`](https://github.com/nodejs/node/commit/296fd57324)] - **inspector**: stop dragging platform pointer (Eugene Ostroukhov)
- [[`fb71337bdf`](https://github.com/nodejs/node/commit/fb71337bdf)] - **(SEMVER-MINOR)** **lib**: rename checkIsArrayBufferView() (Ben Noordhuis) [#20816](https://github.com/nodejs/node/pull/20816)
- [[`f3570f201b`](https://github.com/nodejs/node/commit/f3570f201b)] - **(SEMVER-MINOR)** **lib**: replace checkUint() with validateInt32() (Ben Noordhuis) [#20816](https://github.com/nodejs/node/pull/20816)
- [[`b4b7d368be`](https://github.com/nodejs/node/commit/b4b7d368be)] - **lib**: unmask mode_t values with 0o777 (Joyee Cheung) [#20975](https://github.com/nodejs/node/pull/20975)
- [[`36e5100a39`](https://github.com/nodejs/node/commit/36e5100a39)] - **lib**: support ranges in validateInt32() (cjihrig) [#20588](https://github.com/nodejs/node/pull/20588)
- [[`2fe88d2218`](https://github.com/nodejs/node/commit/2fe88d2218)] - **lib**: mask mode_t type of arguments with 0o777 (Joyee Cheung) [#20636](https://github.com/nodejs/node/pull/20636)
- [[`a0cfb0c9d4`](https://github.com/nodejs/node/commit/a0cfb0c9d4)] - **lib**: add validateInteger() validator (cjihrig) [#20851](https://github.com/nodejs/node/pull/20851)
- [[`740d9f1a0e`](https://github.com/nodejs/node/commit/740d9f1a0e)] - **lib,src**: make `StatWatcher` a `HandleWrap` (Anna Henningsen) [#21244](https://github.com/nodejs/node/pull/21244)
- [[`a657984109`](https://github.com/nodejs/node/commit/a657984109)] - **lib,src**: remove openssl feature conditionals (Ben Noordhuis) [#21094](https://github.com/nodejs/node/pull/21094)
- [[`653b20b26d`](https://github.com/nodejs/node/commit/653b20b26d)] - **loader**: remove unused error code in module_job (Gus Caplan) [#21354](https://github.com/nodejs/node/pull/21354)
- [[`5d3dfedca2`](https://github.com/nodejs/node/commit/5d3dfedca2)] - **meta**: remove CODEOWNERS (Rich Trott) [#21161](https://github.com/nodejs/node/pull/21161)
- [[`169bff3e9e`](https://github.com/nodejs/node/commit/169bff3e9e)] - **n-api**: name CallbackBundle function fields (Anna Henningsen) [#21240](https://github.com/nodejs/node/pull/21240)
- [[`1dc9330b3a`](https://github.com/nodejs/node/commit/1dc9330b3a)] - **n-api**: improve runtime perf of n-api func call (Kenny Yuan) [#21072](https://github.com/nodejs/node/pull/21072)
- [[`9047c8182c`](https://github.com/nodejs/node/commit/9047c8182c)] - **n-api**: remove unused napi_env member (Gabriel Schulhof) [#21127](https://github.com/nodejs/node/pull/21127)
- [[`18c057ab26`](https://github.com/nodejs/node/commit/18c057ab26)] - **net**: emit 'close' when socket ends before connect (Brett Kiefer) [#21290](https://github.com/nodejs/node/pull/21290)
- [[`a3fd1cd8ea`](https://github.com/nodejs/node/commit/a3fd1cd8ea)] - **perf_hooks**: remove less useful bootstrap marks (James M Snell) [#21247](https://github.com/nodejs/node/pull/21247)
- [[`8fddf591c5`](https://github.com/nodejs/node/commit/8fddf591c5)] - **perf_hooks**: set bootstrap complete in only one place (James M Snell) [#21247](https://github.com/nodejs/node/pull/21247)
- [[`fc2956d37a`](https://github.com/nodejs/node/commit/fc2956d37a)] - **process**: backport process/methods file (Michaël Zasso) [#21172](https://github.com/nodejs/node/pull/21172)
- [[`78ad4e9dde`](https://github.com/nodejs/node/commit/78ad4e9dde)] - **src**: remove unused argc var in node_stat_watcher (Daniel Bevenius) [#21337](https://github.com/nodejs/node/pull/21337)
- [[`7fa1344143`](https://github.com/nodejs/node/commit/7fa1344143)] - **src**: use `%zx` in printf for size_t (Anna Henningsen) [#21323](https://github.com/nodejs/node/pull/21323)
- [[`671346ee8f`](https://github.com/nodejs/node/commit/671346ee8f)] - **src**: do proper error checking in `AsyncWrap::MakeCallback` (Anna Henningsen) [#21189](https://github.com/nodejs/node/pull/21189)
- [[`aa468abc4c`](https://github.com/nodejs/node/commit/aa468abc4c)] - **src**: unify native symbol inspection code (Anna Henningsen) [#21238](https://github.com/nodejs/node/pull/21238)
- [[`e92b89a75d`](https://github.com/nodejs/node/commit/e92b89a75d)] - **src**: fix http2 typos (Anatoli Papirovski) [#21194](https://github.com/nodejs/node/pull/21194)
- [[`4f01168414`](https://github.com/nodejs/node/commit/4f01168414)] - **src**: do not persist fs_poll handle in stat_watcher (Anatoli Papirovski) [#21093](https://github.com/nodejs/node/pull/21093)
- [[`685b9b2a6a`](https://github.com/nodejs/node/commit/685b9b2a6a)] - **src**: do not persist timer handle in cares_wrap (Anatoli Papirovski) [#21093](https://github.com/nodejs/node/pull/21093)
- [[`4757771db3`](https://github.com/nodejs/node/commit/4757771db3)] - **src**: add consistency check to node_platform.cc (Anna Henningsen) [#21156](https://github.com/nodejs/node/pull/21156)
- [[`8e2e16721b`](https://github.com/nodejs/node/commit/8e2e16721b)] - **src**: add node_encoding.cc (James M Snell) [#21112](https://github.com/nodejs/node/pull/21112)
- [[`39b38754eb`](https://github.com/nodejs/node/commit/39b38754eb)] - **src**: cleanup beforeExit for consistency (James M Snell) [#21113](https://github.com/nodejs/node/pull/21113)
- [[`314b47d1cf`](https://github.com/nodejs/node/commit/314b47d1cf)] - **(SEMVER-MINOR)** **src**: add Env::profiler_idle_notifier_started() (Timothy Gu) [#20876](https://github.com/nodejs/node/pull/20876)
- [[`5209ff9562`](https://github.com/nodejs/node/commit/5209ff9562)] - **(SEMVER-MINOR)** **src**: remove unused fields msg\_ and env\_ (Daniel Bevenius) [#20876](https://github.com/nodejs/node/pull/20876)
- [[`9a734132f9`](https://github.com/nodejs/node/commit/9a734132f9)] - **(SEMVER-MINOR)** **src**: make handle onclose property a Symbol (Anna Henningsen) [#20876](https://github.com/nodejs/node/pull/20876)
- [[`e6f06807b1`](https://github.com/nodejs/node/commit/e6f06807b1)] - **(SEMVER-MINOR)** **src**: simplify handle closing (Anna Henningsen) [#20876](https://github.com/nodejs/node/pull/20876)
- [[`65924c70e8`](https://github.com/nodejs/node/commit/65924c70e8)] - **(SEMVER-MINOR)** **src**: remove unused fields isolate\_ (Daniel Bevenius) [#20876](https://github.com/nodejs/node/pull/20876)
- [[`de7403f813`](https://github.com/nodejs/node/commit/de7403f813)] - **(SEMVER-MINOR)** **src**: cleanup per-isolate state on platform on isolate unregister (Anna Henningsen) [#20876](https://github.com/nodejs/node/pull/20876)
- [[`ba17c9e46b`](https://github.com/nodejs/node/commit/ba17c9e46b)] - **src**: refactor bootstrap to use bootstrap object (James M Snell) [#20917](https://github.com/nodejs/node/pull/20917)
- [[`cbdc1fdf44`](https://github.com/nodejs/node/commit/cbdc1fdf44)] - **src, tools**: add check for left leaning pointers (Daniel Bevenius) [#21010](https://github.com/nodejs/node/pull/21010)
- [[`935309325b`](https://github.com/nodejs/node/commit/935309325b)] - **test**: fix deprecation warning due to util.print (Tobias Nießen) [#21265](https://github.com/nodejs/node/pull/21265)
- [[`d7ba75f8aa`](https://github.com/nodejs/node/commit/d7ba75f8aa)] - **test**: add test to check colorMode type of Console (Masashi Hirano) [#21248](https://github.com/nodejs/node/pull/21248)
- [[`0b00172df8`](https://github.com/nodejs/node/commit/0b00172df8)] - **test**: removing unnecessary parameter from assert call (djmgit) [#21307](https://github.com/nodejs/node/pull/21307)
- [[`dea3ac7bff`](https://github.com/nodejs/node/commit/dea3ac7bff)] - **test**: improve statwatcher async_hooks test (Anna Henningsen) [#21244](https://github.com/nodejs/node/pull/21244)
- [[`792335f712`](https://github.com/nodejs/node/commit/792335f712)] - **test**: add workerdata-sharedarraybuffer test (Jeremiah Senkpiel) [#21180](https://github.com/nodejs/node/pull/21180)
- [[`e8d15cb149`](https://github.com/nodejs/node/commit/e8d15cb149)] - **test**: mark test-inspector-port-zero-cluster flaky (Rich Trott) [#21251](https://github.com/nodejs/node/pull/21251)
- [[`688bdfef7f`](https://github.com/nodejs/node/commit/688bdfef7f)] - **test**: add crypto check to test-http2-debug (Daniel Bevenius) [#21205](https://github.com/nodejs/node/pull/21205)
- [[`2270ab2a12`](https://github.com/nodejs/node/commit/2270ab2a12)] - **test**: remove string literals from assert.strictEqual() calls (James Kylstra) [#21211](https://github.com/nodejs/node/pull/21211)
- [[`187951c0fc`](https://github.com/nodejs/node/commit/187951c0fc)] - **test**: move inspector-stress-http to sequential (Rich Trott) [#21227](https://github.com/nodejs/node/pull/21227)
- [[`bda34ea203`](https://github.com/nodejs/node/commit/bda34ea203)] - **test**: check gc does not resurrect the loop (Anatoli Papirovski) [#21093](https://github.com/nodejs/node/pull/21093)
- [[`4d782c4720`](https://github.com/nodejs/node/commit/4d782c4720)] - **test**: improve assert error messages (Hristijan Gjorgjievski) [#21160](https://github.com/nodejs/node/pull/21160)
- [[`2655c7b194`](https://github.com/nodejs/node/commit/2655c7b194)] - **test**: mark fs-readfile-tostring-fail flaky for all (Rich Trott) [#21177](https://github.com/nodejs/node/pull/21177)
- [[`17954c2b01`](https://github.com/nodejs/node/commit/17954c2b01)] - **test**: improve internal/buffer.js test coverage (Masashi Hirano) [#21061](https://github.com/nodejs/node/pull/21061)
- [[`2ff4704447`](https://github.com/nodejs/node/commit/2ff4704447)] - **test**: move test-readuint to test-buffer-readuint (Michaël Zasso) [#21170](https://github.com/nodejs/node/pull/21170)
- [[`9c3a7bf076`](https://github.com/nodejs/node/commit/9c3a7bf076)] - **test**: make url-util-format engine agnostic (Rich Trott) [#21141](https://github.com/nodejs/node/pull/21141)
- [[`3d8ec8f85c`](https://github.com/nodejs/node/commit/3d8ec8f85c)] - **test**: make url-parse-invalid-input engine agnostic (Rich Trott) [#21132](https://github.com/nodejs/node/pull/21132)
- [[`0b0370f884`](https://github.com/nodejs/node/commit/0b0370f884)] - **test**: remove unref in http2 test (Anatoli Papirovski) [#21145](https://github.com/nodejs/node/pull/21145)
- [[`14a017cf8d`](https://github.com/nodejs/node/commit/14a017cf8d)] - **test**: apply promises API to fourth appendFile test (Rich Trott) [#21131](https://github.com/nodejs/node/pull/21131)
- [[`aa9dbf666b`](https://github.com/nodejs/node/commit/aa9dbf666b)] - **test**: apply promises API to fourth appendFile test (Rich Trott) [#21131](https://github.com/nodejs/node/pull/21131)
- [[`185b9e45d3`](https://github.com/nodejs/node/commit/185b9e45d3)] - **test**: apply promises API to third appendFile test (Rich Trott) [#21131](https://github.com/nodejs/node/pull/21131)
- [[`c400448e85`](https://github.com/nodejs/node/commit/c400448e85)] - **test**: improve debug output in trace-events test (Rich Trott) [#21120](https://github.com/nodejs/node/pull/21120)
- [[`a4ad9891e3`](https://github.com/nodejs/node/commit/a4ad9891e3)] - **test**: add test for Linux perf (Matheus Marchini) [#20783](https://github.com/nodejs/node/pull/20783)
- [[`e16036c462`](https://github.com/nodejs/node/commit/e16036c462)] - **test**: create new directory v8-updates (Matheus Marchini) [#20783](https://github.com/nodejs/node/pull/20783)
- [[`93ce63c89f`](https://github.com/nodejs/node/commit/93ce63c89f)] - **(SEMVER-MINOR)** **test**: add test against unsupported worker features (Timothy Gu) [#20876](https://github.com/nodejs/node/pull/20876)
- [[`94dcdfb898`](https://github.com/nodejs/node/commit/94dcdfb898)] - **test**: increase coverage for fs.promises.truncate (Masashi Hirano) [#20638](https://github.com/nodejs/node/pull/20638)
- [[`c9cee63179`](https://github.com/nodejs/node/commit/c9cee63179)] - **test,tools**: refactor custom ESLint for readability (Rich Trott) [#21134](https://github.com/nodejs/node/pull/21134)
- [[`ed05d9a821`](https://github.com/nodejs/node/commit/ed05d9a821)] - **(SEMVER-MINOR)** **test,tools**: enable running tests under workers (Anna Henningsen) [#20876](https://github.com/nodejs/node/pull/20876)
- [[`6285fe94f6`](https://github.com/nodejs/node/commit/6285fe94f6)] - **tools**: do not disable `quotes` rule in .eslintrc.js (Rich Trott) [#21338](https://github.com/nodejs/node/pull/21338)
- [[`98346de08c`](https://github.com/nodejs/node/commit/98346de08c)] - **tools**: lint doc/\*.md files (Rich Trott) [#21361](https://github.com/nodejs/node/pull/21361)
- [[`521f8f1d95`](https://github.com/nodejs/node/commit/521f8f1d95)] - **tools**: add BigInt64Array and BigUint64Array to globals (Joyee Cheung) [#21255](https://github.com/nodejs/node/pull/21255)
- [[`a5c386d1ba`](https://github.com/nodejs/node/commit/a5c386d1ba)] - **tools**: add option to use custom template with js2c.py (Shelley Vohr) [#21187](https://github.com/nodejs/node/pull/21187)
- [[`7f70fe83ef`](https://github.com/nodejs/node/commit/7f70fe83ef)] - **tools**: add BigInt to globals (Nikolai Vavilov) [#21237](https://github.com/nodejs/node/pull/21237)
- [[`4e742e379b`](https://github.com/nodejs/node/commit/4e742e379b)] - **tools**: update tooling to work with new macOS CLI … (Rich Trott) [#21173](https://github.com/nodejs/node/pull/21173)
- [[`ed2b57bcd5`](https://github.com/nodejs/node/commit/ed2b57bcd5)] - **tools**: remove unused global types from type-parser (Rich Trott) [#21135](https://github.com/nodejs/node/pull/21135)
- [[`d46446afc5`](https://github.com/nodejs/node/commit/d46446afc5)] - **v8**: replace Buffer with FastBuffer in deserialize (Ujjwal Sharma) [#21196](https://github.com/nodejs/node/pull/21196)
- [[`917960e0a1`](https://github.com/nodejs/node/commit/917960e0a1)] - **win, build**: add documentation support to vcbuild (Bartosz Sosnowski) [#19663](https://github.com/nodejs/node/pull/19663)
- [[`03fbc9e749`](https://github.com/nodejs/node/commit/03fbc9e749)] - **(SEMVER-MINOR)** **worker**: rename to worker_threads (Anna Henningsen) [#20876](https://github.com/nodejs/node/pull/20876)
- [[`9ad42b766e`](https://github.com/nodejs/node/commit/9ad42b766e)] - **(SEMVER-MINOR)** **worker**: improve error (de)serialization (Anna Henningsen) [#20876](https://github.com/nodejs/node/pull/20876)
- [[`6b1a887aa2`](https://github.com/nodejs/node/commit/6b1a887aa2)] - **(SEMVER-MINOR)** **worker**: enable stdio (Anna Henningsen) [#20876](https://github.com/nodejs/node/pull/20876)
- [[`c97fb91e55`](https://github.com/nodejs/node/commit/c97fb91e55)] - **(SEMVER-MINOR)** **worker**: restrict supported extensions (Timothy Gu) [#20876](https://github.com/nodejs/node/pull/20876)
- [[`109c92e8fa`](https://github.com/nodejs/node/commit/109c92e8fa)] - **(SEMVER-MINOR)** **worker**: initial implementation (Anna Henningsen) [#20876](https://github.com/nodejs/node/pull/20876)
- [[`d1f372f052`](https://github.com/nodejs/node/commit/d1f372f052)] - **(SEMVER-MINOR)** **worker**: add `SharedArrayBuffer` sharing (Anna Henningsen) [#20876](https://github.com/nodejs/node/pull/20876)
- [[`f447acd87b`](https://github.com/nodejs/node/commit/f447acd87b)] - **(SEMVER-MINOR)** **worker**: support MessagePort passing in messages (Anna Henningsen) [#20876](https://github.com/nodejs/node/pull/20876)
- [[`337be58ee6`](https://github.com/nodejs/node/commit/337be58ee6)] - **(SEMVER-MINOR)** **worker**: implement `MessagePort` and `MessageChannel` (Anna Henningsen) [#20876](https://github.com/nodejs/node/pull/20876)
- [[`4a54ebc3bd`](https://github.com/nodejs/node/commit/4a54ebc3bd)] - **worker,src**: display remaining handles if `uv_loop_close` fails (Anna Henningsen) [#21238](https://github.com/nodejs/node/pull/21238)
- [[`529d24e3e8`](https://github.com/nodejs/node/commit/529d24e3e8)] - **_Revert_** "**workers,trace_events**: set thread name for workers" (James M Snell) [#21363](https://github.com/nodejs/node/pull/21363)
- [[`dfb5cf6963`](https://github.com/nodejs/node/commit/dfb5cf6963)] - **workers,trace_events**: set thread name for workers (James M Snell) [#21246](https://github.com/nodejs/node/pull/21246)

Windows 32-bit Installer: https://nodejs.org/dist/v10.5.0/node-v10.5.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v10.5.0/node-v10.5.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v10.5.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v10.5.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v10.5.0/node-v10.5.0.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v10.5.0/node-v10.5.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v10.5.0/node-v10.5.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v10.5.0/node-v10.5.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v10.5.0/node-v10.5.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v10.5.0/node-v10.5.0-aix-ppc64.tar.gz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v10.5.0/node-v10.5.0-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v10.5.0/node-v10.5.0-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v10.5.0/node-v10.5.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v10.5.0/node-v10.5.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v10.5.0/node-v10.5.0.tar.gz \
Other release files: https://nodejs.org/dist/v10.5.0/ \
Documentation: https://nodejs.org/docs/v10.5.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

a4008530bf54dc760206e71d7548977137d488e83081e3153a2ffead9bd9549e  node-v10.5.0-aix-ppc64.tar.gz
a85bda6ab91da8595e71736944cbd77c61afe05092217defd0fb74d9f77109f0  node-v10.5.0-darwin-x64.tar.gz
10e7228ca2c2ef3f78262ba62f4a64c655a4fbe8f023e3fb544f6b1b45fdba9a  node-v10.5.0-darwin-x64.tar.xz
21ac19364d1fb2c52e61190112cd10573afa896ec87f0352a708dbc0a25354d9  node-v10.5.0-headers.tar.gz
b51795e77e8cef658ca967b995684bdeb9fafbeaf0d352da77019cf4be49e226  node-v10.5.0-headers.tar.xz
2708f77f12966cdf13046c7ac8513fc430be5cbeacc02711d242d65044580d91  node-v10.5.0-linux-arm64.tar.gz
28e6baa2a4ac5b0f0f5adf85489574038d0a4ca48efe76a4e5831b6b222652ba  node-v10.5.0-linux-arm64.tar.xz
9178e7b45a1910619c80218e436e6a40cfda77b2292825322fc59c3df761d199  node-v10.5.0-linux-armv6l.tar.gz
cf12d275e9a3034bf79dccb639382363e8596d92ef777a0580210ab9908bd271  node-v10.5.0-linux-armv6l.tar.xz
aa615ba40a931c90faaf468a132a08cc662bc12f64608241ee8892407821f8ac  node-v10.5.0-linux-armv7l.tar.gz
08107045cff33962cfeaf0fc18baab7675aa1328f1d56ef41a038f2a5a733424  node-v10.5.0-linux-armv7l.tar.xz
45c0aa657b0764564f79da8c8cab9a42b3f8e81d9f1c57f8cdb276a6f1d195b1  node-v10.5.0-linux-ppc64le.tar.gz
febd42beb6f11ac3cc24f4339276c95e42a9b2ddd6a785e037e5dc8ee3efca29  node-v10.5.0-linux-ppc64le.tar.xz
ca97bd536fdd88675bcf98daa0724622a7964f5da16bb444958284e37eeaaab6  node-v10.5.0-linux-s390x.tar.gz
a9614bbba6a210253cb23cf7f64fa34de9c4c130a16e27ead3c26a69a6d18be1  node-v10.5.0-linux-s390x.tar.xz
5d77d2c68c06404028f063dca0947315570ff5e52e46f67f93ef9f6cdcb1b4a8  node-v10.5.0-linux-x64.tar.gz
8b12be967f5962a8173dca235e1a6f642ee29dcf5fc27697004af898ffeca187  node-v10.5.0-linux-x64.tar.xz
109acb4ada9d96a16f0f5c01acd6485b60e4d5fd865d0be7dff98822251a8314  node-v10.5.0-sunos-x64.tar.gz
7df25dc1a08c06eb3894b786a7a833198c3bf4ffb7d87d7dd20023a61041081d  node-v10.5.0-sunos-x64.tar.xz
4c5b360a06d874ae3f7e20fb9aacfa8d2da651a1e27a617c29164e2107514313  node-v10.5.0-win-x64.7z
ce2b1f9976de64bcc0a5ce877edee0d29c4db1ebab5a7fd713afffd661e99e08  node-v10.5.0-win-x64.zip
a50a2853c1b882cdd8a545d5d735064d375725ab3766a8fff1a68c27f27998bd  node-v10.5.0-win-x86.7z
25b074ff7af71ad36f21954f67b04ffcf6a78ea32ec8855afbab466f4bbc9a9d  node-v10.5.0-win-x86.zip
baee3a34ecbe9040c6da8f01eb61bde563a0458a94401a80dd87229fc938add4  node-v10.5.0-x64.msi
74762c180eacc460769055b6fcfd6cf6168de8e3ae5622b99cc1c1bf59b99c1f  node-v10.5.0-x86.msi
6f3d061a0455a4e1b840bcfa0e2c0f5891d4c52bc42f304542e2ea0236b06a29  node-v10.5.0.pkg
2b61828fd32e79ed3e6cb4781dfa0d61d03739e30da2f68b3bab63d7f92a6d8d  node-v10.5.0.tar.gz
7b54c543745b0df9ee159571fe989d5bbea58a903c51f7d5ccba4105336b33bc  node-v10.5.0.tar.xz
7e9dff3948c2da1236610fca417416b68943c71a8acf7c88469a11cbc504fbe6  win-x64/node.exe
0e4a0c875401941aa47ceb4667684b70035517377d7258c2f1503630c36a9296  win-x64/node.lib
444c7c1c11856b9d7f8aa7d2b2442549b530489f441434896c26f0837b5bb483  win-x64/node_pdb.7z
bebb68e0c4e4cf251e2e51c5dcd51802e5dc5852400a040a009aaf1a88d598ec  win-x64/node_pdb.zip
254860635c1bece8ffaf469d446bed112cf78fb9976f3ea28d6335fe6920419a  win-x86/node.exe
1839f1081dc6de135458607edcfbaeb98cec6bae553ae929c3e9a5dbdaf7cea1  win-x86/node.lib
a1dfaa9a36e24e84e250d2aa5380ebb0ab25200b1f578b4c86c6fb217fc81a48  win-x86/node_pdb.7z
662e2e0e34fd2898f0dc1e195c4a3108f59bac43efc503a2eb3ac48b761b7d48  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----
Version: GnuPG v2

iQIcBAEBCAAGBQJbKqDoAAoJEHcPeppa4VYA7mYP/jDTupIoRcNlYMU49xT4q7jf
NvCK8nXPx+FMX6Ve7X/crldwSgoOfTiDWdhIDUarvX3qxVsgDLFgGVyDqjCzbord
QPS7lzhP827bn5Xc/pXkWLl7/u8JJXOxmh+eOK5/wRo/nAzy0lY80TLM9T3YLeQZ
4zINpUY5/yhZm8HQmzrAIIH81ztx/3MHMnxJwdknGwSG9dEEoSKM8kZvwRkyLIcY
xXvxJUejFnBXNuKfhzaQynjdxAr3zULwJSbxTzHaKrqBgQxYsGbrVihfrnOYe9S9
xw0e/GKIA5CRi4Gp1BobZCmZKx2IT6mtBeSQnhBfXW7A8iacwsraICg1xsyl5ek+
KgYPR5FwMZHLBEq4vCWlVdiOTla1yE4WgEVXncPo3dlsQw90eeFZdye1wQt3C70D
EsReqk51COJab21/fhV7lMTfOPurq2iQUl1UOQfLC50+3mu/rU0psQsbCal5hUsA
RNC5DzSdGaq/NuMvMfUlxRgp8FL+OS5tmBmhcLREW0hfNVfA8Gd86/tXwIswn8xr
owiLmSBIyxP4V11jqx/uKqyKEHAMNNBaZSqCSHl/jMfcNTEhHQPw5sMr6GwlYjlD
X977XRZl8ZUIglXTzN7VYsuV4YrtJKFjPjF9wsX9c7RtaNwSSl2THjned2WCyM1O
P3XRAL17ghsJZ7FJqU+Z
=D5+A
-----END PGP SIGNATURE-----

```
