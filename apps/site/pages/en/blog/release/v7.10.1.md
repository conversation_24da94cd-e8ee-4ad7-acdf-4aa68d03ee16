---
date: '2017-07-11T17:01:21.658Z'
category: release
title: Node v7.10.1 (Current)
layout: blog-post
author: <PERSON>
---

### Notable changes

- **build**:
  - Disable V8 snapshots - The hashseed embedded in the snapshot is
    currently the same for all runs of the binary. This opens node up to
    collision attacks which could result in a Denial of Service. We have
    temporarily disabled snapshots until a more robust solution is found
    (<PERSON>)
- **deps**:
  - CVE-2017-1000381 - The c-ares function ares_parse_naptr_reply(),
    which is used for parsing NAPTR responses, could be triggered to
    read memory outside of the given input buffer if the passed in DNS
    response packet was crafted in a particular way. This patch checks that
    there is enough data for the required elements of an NAPTR record (2
    int16, 3 bytes for string lengths) before processing a record. (<PERSON>)

### Commits

- [[`ff587deb54`](https://github.com/nodejs/node/commit/ff587deb54)] - **build**: disable V8 snapshots (<PERSON>) [nodejs/node-private#84](https://github.com/nodejs/node-private/pull/84)
- [[`8a82960e76`](https://github.com/nodejs/node/commit/8a82960e76)] - **deps**: cherry-pick 9478908a49 from cares upstream (David Drysdale) [nodejs/node-private#88](https://github.com/nodejs/node-private/pull/88)
- [[`b5bf5e8086`](https://github.com/nodejs/node/commit/b5bf5e8086)] - **test**: verify hash seed uniqueness (Ali Ijaz Sheikh) [nodejs/node-private#84](https://github.com/nodejs/node-private/pull/84)

Windows 32-bit Installer: https://nodejs.org/dist/v7.10.1/node-v7.10.1-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v7.10.1/node-v7.10.1-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v7.10.1/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v7.10.1/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v7.10.1/node-v7.10.1.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v7.10.1/node-v7.10.1-darwin-x64.tar.gz \
Linux 32-bit Binary: https://nodejs.org/dist/v7.10.1/node-v7.10.1-linux-x86.tar.xz \
Linux 64-bit Binary: https://nodejs.org/dist/v7.10.1/node-v7.10.1-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v7.10.1/node-v7.10.1-linux-ppc64le.tar.xz \
Linux PPC BE 64-bit Binary: https://nodejs.org/dist/v7.10.1/node-v7.10.1-linux-ppc64.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v7.10.1/node-v7.10.1-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v7.10.1/node-v7.10.1-aix-ppc64.tar.gz \
SmartOS 32-bit Binary: https://nodejs.org/dist/v7.10.1/node-v7.10.1-sunos-x86.tar.xz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v7.10.1/node-v7.10.1-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v7.10.1/node-v7.10.1-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v7.10.1/node-v7.10.1-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v7.10.1/node-v7.10.1-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v7.10.1/node-v7.10.1.tar.gz \
Other release files: https://nodejs.org/dist/v7.10.1/ \
Documentation: https://nodejs.org/docs/v7.10.1/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

9a8502e5ee6b68ed6d5fbdb52ae3228e9f80b628bf505d7a47bb41a815e50579  node-v7.10.1-aix-ppc64.tar.gz
a03512d8f17d8312c6fece68a9c20aaa8e2268de18edfea847aa6a35af3a95ba  node-v7.10.1-darwin-x64.tar.gz
d67d2eb9456aab925416ad58aa18b9680e66a4bcc243a89b22e646f7fffc4ff9  node-v7.10.1-darwin-x64.tar.xz
5f0f1a46c0a869d4ef09362b6698ea1f27d4922aee47ecfef43ddf8e8d9add46  node-v7.10.1-headers.tar.gz
86a8a564b0045a8098da01f330b873a6a6f720cd3223063eef86b92b99a684c3  node-v7.10.1-headers.tar.xz
157be398d666b0753d219b9f4cdd3517d4335ddb2c3800242d3934f191932920  node-v7.10.1-linux-arm64.tar.gz
20392330c15bcf856b7e73d52914ffba238e441f080e90800593c2d4a8300a73  node-v7.10.1-linux-arm64.tar.xz
f2e512a08057b5a8c75f8ba9bf407812315cba4c5f8fb35b6888818b241cc7e4  node-v7.10.1-linux-armv6l.tar.gz
9460575e9df34bf3afaa35f30f2803b8c40f4f7c65e7fcedd5b35a29f4f23126  node-v7.10.1-linux-armv6l.tar.xz
d4a3b5d1fd405b931558686141c058c3f55fd25821db89039edc099a64108353  node-v7.10.1-linux-armv7l.tar.gz
7e55603b721602b3fe651275060eae75eb7f7438a8259ae096dedf78175c32b0  node-v7.10.1-linux-armv7l.tar.xz
b5ec78b50e9399fcbcda6accbd67b2d0c2c07fa20c482a34b8b82928041465f1  node-v7.10.1-linux-ppc64le.tar.gz
fcc3d13b54aaf05329aeaf7fc0ca1f23696aa97c6ac09d0f47e82056043b96b9  node-v7.10.1-linux-ppc64le.tar.xz
dfdf4d9bc1253b148a53b23838e907c1a985698d997320f35a817c61d332d1a3  node-v7.10.1-linux-ppc64.tar.gz
c86c2b8d7ccf048420a27e596bacc8e113412fa470ef3a0bea4ea8da40d09157  node-v7.10.1-linux-ppc64.tar.xz
938c9449456099b6e8b66c8b61f7a229812d123d8fb41d7b0622d37866650c6f  node-v7.10.1-linux-s390x.tar.gz
69e159da5f3482852649dd777c5e9735824c1f1095a84f6575dc601ffdde4fad  node-v7.10.1-linux-s390x.tar.xz
3e3609a836257eb9be9b6425fc398239ed023f11aacbbea12fb4d291cf64a196  node-v7.10.1-linux-x64.tar.gz
7b0e9d1af945671a0365a64ee58a2b0d72b3632a1cebe6b5bd75094b93627bf3  node-v7.10.1-linux-x64.tar.xz
e6df274563cb9fd29c88d7905f3bd55f8eb52a728f6ec3dc6adf1f908ae07ce3  node-v7.10.1-linux-x86.tar.gz
f3594e32fa49144061a4276a7c834e3a26b6e3fad8e636b5481c18e50f8fffc3  node-v7.10.1-linux-x86.tar.xz
f248dcf3d7d83cd19bba8196c4707b0b2e1e72e523403b4641418516f444b7e7  node-v7.10.1.pkg
21ca039674fbaa2f4fdb12fe2c1060b43257fe4ee87df6d786e3933176a3796b  node-v7.10.1-sunos-x64.tar.gz
f8924c7de4d4b2409aee8e4772021f80825ee1b1fa7762013264eb44ec6bfd56  node-v7.10.1-sunos-x64.tar.xz
f1b23aacbf3a0e22d9b4e364bac9b1c5c290aec81227615bfbdb3468ea609666  node-v7.10.1-sunos-x86.tar.gz
5e12d6fb0a922878389bbe375e5b0df95316ef20fee6d3376de5d6b9b06961fd  node-v7.10.1-sunos-x86.tar.xz
baf060e5d3abb8fdebb8c2b28c4d8cde05d43acfd9fc687f21f4b7a3ff69745e  node-v7.10.1.tar.gz
654db852149a1cc59ece68ec573b0486907e8febe8353cee097dd29ea5a56cfa  node-v7.10.1.tar.xz
38be44aa73fd057d22b13f5b5adf55008add5b890d9c018cff416515af44ff08  node-v7.10.1-win-x64.7z
617590f06f9a0266ceecb3fd17120fc2fbf8669980974f339a83f3b56ed05f7b  node-v7.10.1-win-x64.zip
0568d134d189521a0a0c933082dc7192a0c07b753d2dca9a6c61ee219de7745e  node-v7.10.1-win-x86.7z
671ca94eba836c8c52409c8be5ddbac88682c558cc405032b5a44df6e8d87502  node-v7.10.1-win-x86.zip
2bfab9dbfab4e211bf247a9abfc4f7c09c4b8738cb2f95e546a86279e26a6c10  node-v7.10.1-x64.msi
f25e9139817cc0488696eadc5ef7e9babaf25f970e551a8114580a403c55dec0  node-v7.10.1-x86.msi
14d48dcc8fd1845dda208d2f4d9df9b9ebf436ab6cf638bc7d316f646b3aed3f  win-x64/node.exe
fc33ebf552041658e669293f4a1cb067e4df2f7ea41eb8effcb27c3f51331961  win-x64/node.lib
db4629bae5509c7053bc5e2ab058b0c5a2fee62184973cf5176b0b6783dc3f17  win-x64/node_pdb.7z
49a16587fd411ac097494e1d92372f74cd861bdf6109c2fa778d0afe66dd77f8  win-x64/node_pdb.zip
e41433e3036c16f841ad4ccf9189892bad0eb2b609f169a8374084bbb6e000cd  win-x86/node.exe
2699d8282963de7bf8e3621cd9f774b42d409f8b93e17486ad35477367e8cffc  win-x86/node.lib
b1a736aea808977aa3f7aba5762a3b2d73d57c37b1b60373ab91cecc1f76d7e1  win-x86/node_pdb.7z
23cd0e1adf61145656248b9281332bf1bc27ce479fe753db56c9967ef15cbc6b  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----
Comment: GPGTools - https://gpgtools.org

iQIcBAEBCAAGBQJZZQJKAAoJELY7U1pMIGypvSMQANvmt4YNjRx/Jhw+86ODWGWe
ufjurilI4bwPxY0mMsjV/4dnicwodJnYALSIAyc2GZUWNjt62vPF7Jupu9rYzmO+
H/1+qxpVx2E9ysFiWkj35Y11e4uXT4E409kKgogIv/w0luO2Z+L328WkNchqeLm6
KHGgj28L8DNFELoKWK0hENfUhoT9FJ//8CgcGlA4UY6MQirnCoqaHjxMQNiYlZh/
K1xzPbMy8f/rNgV19lvAP2C1fIHzflo86ZAZZ8LDi8VQMYUbKV+NVuMpIo1u6x5+
z5kbNfHulLjXUcxqLDNA4GWb3q9NJ6lO2t4ldxU5imPDfFpZPk1GiQu1JjR2XQXu
UUwxleclrnx5UlKlBs0mm4ECotd+2RQPIvP3bjW7YKGPh3Y+D9pomwWnC6yhaepC
JHbSTWTRfJBnhRvT4UPDhpW279e3un77f28OuGBbeqY7zN4+fTXvBhhEBYeGYr/C
NB4Obp3DjqvUxCBekNQfWWK0C310B2u7zs5HiM0TPDbb1sEKelRbHiv4JI+fsjBx
gDoq8RzuB+pCUJYIhCeOsUBVaFLqZskIy3FCqobBAg+rN8qRq8X3f28jDVn4jur5
+eDzvJuMsUt26ESsRUmMGu86glKHz1r5m/vwOYQHGo0HLKo4epUz8XQlzPMeI4WY
40OjnOqlNO8+Afs9Zpkg
=jhPJ
-----END PGP SIGNATURE-----

```
