---
date: '2016-02-17T17:34:38.789Z'
category: release
title: Node v4.3.1 (LTS)
layout: blog-post
author: <PERSON><PERSON>
---

### Notable changes

- **buffer**
  - make byte<PERSON>ength work with <PERSON><PERSON><PERSON> correctly (<PERSON>)
    - [#4738](https://github.com/nodejs/node/pull/4738)
- **debugger**
  - guard against call from non-node context (<PERSON>)
    - [#4328](https://github.com/nodejs/node/pull/4328)
    - fixes segfaults in debugger
  - do not incept debug context (<PERSON><PERSON>)
    - [#4819](https://github.com/nodejs/node/pull/4819)
    - fixes crash in debugger when using util methods
- **deps**
  - update to http-parser 2.5.2 (<PERSON>)
    - [#5238](https://github.com/nodejs/node/pull/5238)

### Commits

- [[`748d2b4de1`](https://github.com/nodejs/node/commit/748d2b4de1)] - **buffer**: make byteLength work with <PERSON><PERSON><PERSON> correctly (<PERSON>) [#4738](https://github.com/nodejs/node/pull/4738)
- [[`fb615bdaf4`](https://github.com/nodejs/node/commit/fb615bdaf4)] - **buffer**: remove unnecessary TODO comments (Peter Geiss) [#4719](https://github.com/nodejs/node/pull/4719)
- [[`b8213ba7e1`](https://github.com/nodejs/node/commit/b8213ba7e1)] - **cluster**: ignore queryServer msgs on disconnection (Santiago Gimeno) [#4465](https://github.com/nodejs/node/pull/4465)
- [[`f8a676ed59`](https://github.com/nodejs/node/commit/f8a676ed59)] - **cluster**: fix race condition setting suicide prop (Santiago Gimeno) [#4349](https://github.com/nodejs/node/pull/4349)
- [[`9d4a226dad`](https://github.com/nodejs/node/commit/9d4a226dad)] - **crypto**: clear error stack in ECDH::Initialize (Fedor Indutny) [#4689](https://github.com/nodejs/node/pull/4689)
- [[`583f3347d8`](https://github.com/nodejs/node/commit/583f3347d8)] - **debugger**: remove variable redeclarations (Rich Trott) [#4633](https://github.com/nodejs/node/pull/4633)
- [[`667f7a7ab3`](https://github.com/nodejs/node/commit/667f7a7ab3)] - **debugger**: guard against call from non-node context (Ben Noordhuis) [#4328](https://github.com/nodejs/node/pull/4328)
- [[`188cff3c31`](https://github.com/nodejs/node/commit/188cff3c31)] - **deps**: update to http-parser 2.5.2 (James Snell) [#5238](https://github.com/nodejs/node/pull/5238)
- [[`6e829b44e3`](https://github.com/nodejs/node/commit/6e829b44e3)] - **dgram**: prevent disabled optimization of bind() (Brian White) [#4613](https://github.com/nodejs/node/pull/4613)
- [[`c3956d05b1`](https://github.com/nodejs/node/commit/c3956d05b1)] - **doc**: update list of personal traits in CoC (Kat Marchán) [#4801](https://github.com/nodejs/node/pull/4801)
- [[`39cb69ca21`](https://github.com/nodejs/node/commit/39cb69ca21)] - **doc**: style fixes for the TOC (Roman Reiss) [#4748](https://github.com/nodejs/node/pull/4748)
- [[`cb5986da81`](https://github.com/nodejs/node/commit/cb5986da81)] - **doc**: add `servername` parameter docs (Alexander Makarenko) [#4729](https://github.com/nodejs/node/pull/4729)
- [[`91066b5f34`](https://github.com/nodejs/node/commit/91066b5f34)] - **doc**: update branch-diff arguments in release doc (Rod Vagg) [#4691](https://github.com/nodejs/node/pull/4691)
- [[`9ca24de41d`](https://github.com/nodejs/node/commit/9ca24de41d)] - **doc**: add docs for more stream options (zoubin) [#4639](https://github.com/nodejs/node/pull/4639)
- [[`437d0e336d`](https://github.com/nodejs/node/commit/437d0e336d)] - **doc**: mention that http.Server inherits from net.Server (Ryan Sobol) [#4455](https://github.com/nodejs/node/pull/4455)
- [[`393e569160`](https://github.com/nodejs/node/commit/393e569160)] - **doc**: copyedit setTimeout() documentation (Rich Trott) [#4434](https://github.com/nodejs/node/pull/4434)
- [[`e2a682ecc3`](https://github.com/nodejs/node/commit/e2a682ecc3)] - **doc**: fix formatting in process.markdown (Rich Trott) [#4433](https://github.com/nodejs/node/pull/4433)
- [[`75b0ea85bd`](https://github.com/nodejs/node/commit/75b0ea85bd)] - **doc**: add path property to Write/ReadStream in fs.markdown (Claudio Rodriguez) [#4368](https://github.com/nodejs/node/pull/4368)
- [[`48c2783421`](https://github.com/nodejs/node/commit/48c2783421)] - **doc**: add docs working group (Bryan English) [#4244](https://github.com/nodejs/node/pull/4244)
- [[`c0432e9f56`](https://github.com/nodejs/node/commit/c0432e9f56)] - **doc**: restore ICU third-party software licenses (Richard Lau) [#4762](https://github.com/nodejs/node/pull/4762)
- [[`36a4159dab`](https://github.com/nodejs/node/commit/36a4159dab)] - **doc**: rebuild LICENSE using tools/license-builder.sh (Rod Vagg) [#4194](https://github.com/nodejs/node/pull/4194)
- [[`a2998a1bce`](https://github.com/nodejs/node/commit/a2998a1bce)] - **gitignore**: never ignore debug module (Michaël Zasso) [#2286](https://github.com/nodejs/node/pull/2286)
- [[`661b2557d9`](https://github.com/nodejs/node/commit/661b2557d9)] - **http**: remove variable redeclaration (Rich Trott) [#4612](https://github.com/nodejs/node/pull/4612)
- [[`1bb2967d48`](https://github.com/nodejs/node/commit/1bb2967d48)] - **http**: fix non-string header value concatenation (Brian White) [#4460](https://github.com/nodejs/node/pull/4460)
- [[`15ed64e34c`](https://github.com/nodejs/node/commit/15ed64e34c)] - **lib**: fix style issues after eslint update (Michaël Zasso) [nodejs/io.js#2286](https://github.com/nodejs/io.js/pull/2286)
- [[`2e92a1a6b4`](https://github.com/nodejs/node/commit/2e92a1a6b4)] - **module**: move unnecessary work for early return (Andres Suarez) [#3579](https://github.com/nodejs/node/pull/3579)
- [[`40c8e6d75d`](https://github.com/nodejs/node/commit/40c8e6d75d)] - **net**: remove hot path comment from connect (Evan Lucas) [#4648](https://github.com/nodejs/node/pull/4648)
- [[`8ed0c1c22c`](https://github.com/nodejs/node/commit/8ed0c1c22c)] - **net**: fix dns lookup for android (Josh Dague) [#4580](https://github.com/nodejs/node/pull/4580)
- [[`15fa555204`](https://github.com/nodejs/node/commit/15fa555204)] - **net, doc**: fix line wrapping lint in net.js (James M Snell) [#4588](https://github.com/nodejs/node/pull/4588)
- [[`1b070e48e0`](https://github.com/nodejs/node/commit/1b070e48e0)] - **node_contextify**: do not incept debug context (Myles Borins) [#4815](https://github.com/nodejs/node/issues/4815)
- [[`4fbcb47fe9`](https://github.com/nodejs/node/commit/4fbcb47fe9)] - **readline**: Remove XXX and output debuglog (Kohei TAKATA) [#4690](https://github.com/nodejs/node/pull/4690)
- [[`26f02405d0`](https://github.com/nodejs/node/commit/26f02405d0)] - **repl**: make sure historyPath is trimmed (Evan Lucas) [#4539](https://github.com/nodejs/node/pull/4539)
- [[`5990ba2a0a`](https://github.com/nodejs/node/commit/5990ba2a0a)] - **src**: remove redeclarations of variables (Rich Trott) [#4605](https://github.com/nodejs/node/pull/4605)
- [[`c41ed59dbc`](https://github.com/nodejs/node/commit/c41ed59dbc)] - **src**: don't check failure with ERR_peek_error() (Ben Noordhuis) [#4731](https://github.com/nodejs/node/pull/4731)
- [[`d71f9992f9`](https://github.com/nodejs/node/commit/d71f9992f9)] - **stream**: remove useless if test in transform (zoubin) [#4617](https://github.com/nodejs/node/pull/4617)
- [[`f205e9920e`](https://github.com/nodejs/node/commit/f205e9920e)] - **test**: fix tls-no-rsa-key flakiness (Santiago Gimeno) [#4043](https://github.com/nodejs/node/pull/4043)
- [[`447347cd62`](https://github.com/nodejs/node/commit/447347cd62)] - **test**: fix issues for space-in-parens ESLint rule (Roman Reiss) [#4753](https://github.com/nodejs/node/pull/4753)
- [[`be8274508c`](https://github.com/nodejs/node/commit/be8274508c)] - **test**: improve test-cluster-disconnect-suicide-race (Rich Trott) [#4739](https://github.com/nodejs/node/pull/4739)
- [[`0178001163`](https://github.com/nodejs/node/commit/0178001163)] - **test**: make test-cluster-disconnect-leak reliable (Rich Trott) [#4736](https://github.com/nodejs/node/pull/4736)
- [[`d615757da2`](https://github.com/nodejs/node/commit/d615757da2)] - **test**: fix flaky test-net-socket-local-address (cjihrig) [#4650](https://github.com/nodejs/node/pull/4650)
- [[`baa0a3dff5`](https://github.com/nodejs/node/commit/baa0a3dff5)] - **test**: fix race in test-net-server-pause-on-connect (Rich Trott) [#4637](https://github.com/nodejs/node/pull/4637)
- [[`909b5167cb`](https://github.com/nodejs/node/commit/909b5167cb)] - **test**: remove 1 second delay from test (Rich Trott) [#4616](https://github.com/nodejs/node/pull/4616)
- [[`8ea76608ed`](https://github.com/nodejs/node/commit/8ea76608ed)] - **test**: move resource intensive tests to sequential (Rich Trott) [#4615](https://github.com/nodejs/node/pull/4615)
- [[`7afcdd358e`](https://github.com/nodejs/node/commit/7afcdd358e)] - **test**: require common module only once (Rich Trott) [#4611](https://github.com/nodejs/node/pull/4611)
- [[`0e02eb0bbe`](https://github.com/nodejs/node/commit/0e02eb0bbe)] - **test**: only include http module once (Rich Trott) [#4606](https://github.com/nodejs/node/pull/4606)
- [[`34d9e48bb6`](https://github.com/nodejs/node/commit/34d9e48bb6)] - **test**: fix `http-upgrade-client` flakiness (Santiago Gimeno) [#4602](https://github.com/nodejs/node/pull/4602)
- [[`556703d531`](https://github.com/nodejs/node/commit/556703d531)] - **test**: fix flaky unrefed timers test (Rich Trott) [#4599](https://github.com/nodejs/node/pull/4599)
- [[`3d5bc69796`](https://github.com/nodejs/node/commit/3d5bc69796)] - **test**: fix `http-upgrade-agent` flakiness (Santiago Gimeno) [#4520](https://github.com/nodejs/node/pull/4520)
- [[`ec24d3767b`](https://github.com/nodejs/node/commit/ec24d3767b)] - **test**: fix flaky test-cluster-shared-leak (Rich Trott) [#4510](https://github.com/nodejs/node/pull/4510)
- [[`a256790327`](https://github.com/nodejs/node/commit/a256790327)] - **test**: fix flaky cluster-net-send (Brian White) [#4444](https://github.com/nodejs/node/pull/4444)
- [[`6809c2be1a`](https://github.com/nodejs/node/commit/6809c2be1a)] - **test**: fix flaky child-process-fork-regr-gh-2847 (Brian White) [#4442](https://github.com/nodejs/node/pull/4442)
- [[`e6448aa36b`](https://github.com/nodejs/node/commit/e6448aa36b)] - **test**: use addon.md block headings as test dir names (Rod Vagg) [#4412](https://github.com/nodejs/node/pull/4412)
- [[`305d340fca`](https://github.com/nodejs/node/commit/305d340fca)] - **test**: test each block in addon.md contains js & cc (Rod Vagg) [#4411](https://github.com/nodejs/node/pull/4411)
- [[`f213406575`](https://github.com/nodejs/node/commit/f213406575)] - **test**: fix tls-multi-key race condition (Santiago Gimeno) [#3966](https://github.com/nodejs/node/pull/3966)
- [[`607f545568`](https://github.com/nodejs/node/commit/607f545568)] - **test**: fix style issues after eslint update (Michaël Zasso) [nodejs/io.js#2286](https://github.com/nodejs/io.js/pull/2286)
- [[`aefb20a94f`](https://github.com/nodejs/node/commit/aefb20a94f)] - **tls**: copy client CAs and cert store on CertCb (Fedor Indutny) [#3537](https://github.com/nodejs/node/pull/3537)
- [[`7821b3e305`](https://github.com/nodejs/node/commit/7821b3e305)] - **tls_legacy**: do not read on OpenSSL's stack (Fedor Indutny) [#4624](https://github.com/nodejs/node/pull/4624)
- [[`b66db49f94`](https://github.com/nodejs/node/commit/b66db49f94)] - **tools**: add support for subkeys in release tools (Myles Borins) [#4807](https://github.com/nodejs/node/pull/4807)
- [[`837ebd1985`](https://github.com/nodejs/node/commit/837ebd1985)] - **tools**: enable space-in-parens ESLint rule (Roman Reiss) [#4753](https://github.com/nodejs/node/pull/4753)
- [[`066d5e7da2`](https://github.com/nodejs/node/commit/066d5e7da2)] - **tools**: fix style issue after eslint update (Michaël Zasso) [nodejs/io.js#2286](https://github.com/nodejs/io.js/pull/2286)
- [[`b20ea69f46`](https://github.com/nodejs/node/commit/b20ea69f46)] - **tools**: update eslint config (Michaël Zasso) [nodejs/io.js#2286](https://github.com/nodejs/io.js/pull/2286)
- [[`2e0352d50c`](https://github.com/nodejs/node/commit/2e0352d50c)] - **tools**: update eslint to v1.10.3 (Michaël Zasso) [nodejs/io.js#2286](https://github.com/nodejs/io.js/pull/2286)
- [[`c96800a432`](https://github.com/nodejs/node/commit/c96800a432)] - **tools**: fix license-builder.sh for ICU (Richard Lau) [#4762](https://github.com/nodejs/node/pull/4762)
- [[`720b03dca7`](https://github.com/nodejs/node/commit/720b03dca7)] - **tools**: add license-builder.sh to construct LICENSE (Rod Vagg) [#4194](https://github.com/nodejs/node/pull/4194)

Windows 32-bit Installer: https://nodejs.org/dist/v4.3.1/node-v4.3.1-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v4.3.1/node-v4.3.1-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v4.3.1/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v4.3.1/win-x64/node.exe \
Mac OS X 64-bit Installer: https://nodejs.org/dist/v4.3.1/node-v4.3.1.pkg \
Mac OS X 64-bit Binary: https://nodejs.org/dist/v4.3.1/node-v4.3.1-darwin-x64.tar.gz \
Linux 32-bit Binary: https://nodejs.org/dist/v4.3.1/node-v4.3.1-linux-x86.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v4.3.1/node-v4.3.1-linux-x64.tar.gz \
SmartOS 32-bit Binary: https://nodejs.org/dist/v4.3.1/node-v4.3.1-sunos-x86.tar.gz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v4.3.1/node-v4.3.1-sunos-x64.tar.gz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v4.3.1/node-v4.3.1-linux-armv6l.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v4.3.1/node-v4.3.1-linux-armv7l.tar.gz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v4.3.1/node-v4.3.1-linux-arm64.tar.gz \
Source Code: https://nodejs.org/dist/v4.3.1/node-v4.3.1.tar.gz \
Other release files: https://nodejs.org/dist/v4.3.1/ \
Documentation: https://nodejs.org/docs/v4.3.1/api/

Shasums (GPG signing hash: SHA512, file hash: SHA256):

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA1

9c0751ee88a47c10269eb930d7ad7b103c2ba875c3a96204ca133dc52fc50826  node-v4.3.1-darwin-x64.tar.gz
264314ea7c696807c47aaa8d9d89425a4f2e7ed47dd1ea282426af56cc8227b0  node-v4.3.1-darwin-x64.tar.xz
8ba5c1e5eb5509e0f4f00d56e1916ac703fdd05cf353f119451f2b37c51987a5  node-v4.3.1-headers.tar.gz
951648b678b8ed7e1dbd6ce0800ffc66547b30e24b553b6574880fe2df4ff443  node-v4.3.1-headers.tar.xz
cd55ce4426f9dd9be878fb89d715cbaf589210162e4269ce2ccfd6b9674385e9  node-v4.3.1-linux-arm64.tar.gz
54e0fcea69a3111458d82e982574a2e9bd02af44435065a697d0669fbac8a5f2  node-v4.3.1-linux-arm64.tar.xz
c42383509b7ad8a400da5b5605e2f8663cbe7e214a7c2cfa94964c57b1842ad8  node-v4.3.1-linux-armv6l.tar.gz
8e0e8e80dcf69b009ff2730e07a672399cabd1de948980df77a20bcb897f3731  node-v4.3.1-linux-armv6l.tar.xz
8fcf8016b01137d6ccb6d048227af30240a7a01cc40126b0f88f9e99ee40a552  node-v4.3.1-linux-armv7l.tar.gz
99b82b14f40cab03070a67b57790b9693fffaf7ba97b6c77b2225d767418e5d7  node-v4.3.1-linux-armv7l.tar.xz
b3af1ed18a9150af42754e9a0385ecc4b4e9b493fcf32bf6ca0d7239d636254b  node-v4.3.1-linux-x64.tar.gz
1952d92af83b1bd7ffdb4735999f93a91e0d34ba1315ea1210f16f2e411125e4  node-v4.3.1-linux-x64.tar.xz
b0ac5a9ec4c03b8e119498addd70c3d3ab030abe8d9ca1f120627e6ee2673acc  node-v4.3.1-linux-x86.tar.gz
ff7c223cc1119a9ba8c32cdc7ef7c1a00b666ecb1e82d35259706e320c8a99bc  node-v4.3.1-linux-x86.tar.xz
75195a95819aeb8aad75c8ec3b9592eb42ca483d4d2a74d299c942eb25cdf61e  node-v4.3.1.pkg
f5d51b0d4324e88db468b20ddd3a3790d9dcaf10fe977e328e5d39976028573c  node-v4.3.1-sunos-x64.tar.gz
7975f71fb5eeb381572408d7eb39e50488d0005daba09c2c0e05cc0fcee52004  node-v4.3.1-sunos-x64.tar.xz
32d2369d00a50661c0ace7dd4d99105bda36430b27c8be129731bb44ac3064b0  node-v4.3.1-sunos-x86.tar.gz
298fd5c5a998312d9a4bb96b03fcbedcfe6e3a0fee76f8fa4c5d017f0b90c544  node-v4.3.1-sunos-x86.tar.xz
61e2d58e861b6c9dbf0ac7624b718198cbd59b88a9de31a39a05de95a32eee73  node-v4.3.1.tar.gz
9f2fb884655add686a0414800ec2a58279a973520d42f18362f492715625ffa8  node-v4.3.1.tar.xz
0da4c8de80fc753dafb032003ea1f5835928a8744855a20a0ad6ec9b1e034863  node-v4.3.1-x64.msi
af0fa21c6a6383a9eb619af7fe8f731ae32559dfefe7813c4a0ad515bfdb3149  node-v4.3.1-x86.msi
6064b088b0945ead21040f224e12a777f4a7c16328fbc5ce1f1f07d443fbe613  win-x64/node.exe
739b2a55f75b995b67b97030d389cecc3c439ea23a047b64e4e35db90adc4701  win-x64/node.lib
31c3a28d556ffe643d755887a5f9aa5263d370352d03f9201882234d1334148f  win-x86/node.exe
0b821b79a7f43f4897814dd70d853b5ab3e099a4b6833fc2b7ef04b7dc3e9cc2  win-x86/node.lib
-----BEGIN PGP SIGNATURE-----

iQEcBAEBAgAGBQJWxK8BAAoJEJM7AfQLXKlGUQEH/j/M/dDYdq8SFo7QX+rMVtXt
m3bXcl5sYB5jzXvvbJhWAaYmOUnIjwfb0tTH9pPqrMuZ6G/96RsYZJe522NVFGhV
LMfguS5qmXNrggUnYSZev8Hpu6Ce+6xSFr7VXgP0g27aS/3S5rpBwVf7dzMC51u4
CSAE7TZGqkEvv5tpSvjfxZuDC5mUnCOTl34e5dBjFsOopOb34VYD6jIMHOp1tlXl
wBUBqTaEM5cy37Iuq24ToAQBtUDz8RDzFQPn3iRAghXEfKEJRomNM5GxXV9fTmyW
t8Oy2PPaFCWqYeeJJvJECPOFNJtSTRNsoghgFFfsbupvOV5vAx4fca7+RjawlYk=
=yrZD
-----END PGP SIGNATURE-----

```
