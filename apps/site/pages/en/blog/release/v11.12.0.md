---
date: '2019-03-15T21:12:05.280Z'
category: release
title: Node v11.12.0 (Current)
layout: blog-post
author: <PERSON><PERSON>
---

### Notable Changes

- **bootstrap**:
  - Add experimental `--frozen-intrinsics` flag (<PERSON>) [#25685](https://github.com/nodejs/node/pull/25685)
- **build**:
  - Enable v8's siphash for hash seed creation (<PERSON>ag<PERSON>) [#26367](https://github.com/nodejs/node/pull/26367)
- **deps**:
  - Upgrade openssl to 1.1.1b (<PERSON>) [#26327](https://github.com/nodejs/node/pull/26327)
- **process**:
  - Make `process[Symbol.toStringTag]` writable again (<PERSON><PERSON>) [#26488](https://github.com/nodejs/node/pull/26488)
- **repl**:
  - Add `util.inspect.replDefaults` to customize the writer (<PERSON><PERSON>) [#26375](https://github.com/nodejs/node/pull/26375)
- **report**:
  - Rename `triggerReport()` to `writeReport()` (<PERSON>) [#26527](https://github.com/nodejs/node/pull/26527)

### Commits

- [[`142a92ffaf`](https://github.com/nodejs/node/commit/142a92ffaf)] - **benchmark**: refactor path benchmarks (Ruben Bridgewater) [#26359](https://github.com/nodejs/node/pull/26359)
- [[`52a0d76f32`](https://github.com/nodejs/node/commit/52a0d76f32)] - **benchmark,doc,lib,test**: capitalize comments (Ruben Bridgewater) [#26483](https://github.com/nodejs/node/pull/26483)
- [[`f79cf7067f`](https://github.com/nodejs/node/commit/f79cf7067f)] - **benchmark,lib**: add process.hrtime.bigint benchmark (Anna Henningsen) [#26381](https://github.com/nodejs/node/pull/26381)
- [[`3e54f90911`](https://github.com/nodejs/node/commit/3e54f90911)] - **(SEMVER-MINOR)** **bootstrap**: experimental --frozen-intrinsics flag (Guy Bedford) [#25685](https://github.com/nodejs/node/pull/25685)
- [[`68bb1e9fd8`](https://github.com/nodejs/node/commit/68bb1e9fd8)] - **buffer**: do not affect memory after target for utf16 write (Anna Henningsen) [#26432](https://github.com/nodejs/node/pull/26432)
- [[`9b1cb9da57`](https://github.com/nodejs/node/commit/9b1cb9da57)] - **build**: enable v8's siphash for hash seed creation (Rod Vagg) [#26367](https://github.com/nodejs/node/pull/26367)
- [[`b2e27a02b4`](https://github.com/nodejs/node/commit/b2e27a02b4)] - **_Revert_** "**build**: silence cpp lint by default" (Refael Ackermann) [#26358](https://github.com/nodejs/node/pull/26358)
- [[`240de933f4`](https://github.com/nodejs/node/commit/240de933f4)] - **build**: indicate that configure has done something (Richard Lau) [#26436](https://github.com/nodejs/node/pull/26436)
- [[`02faa1a50c`](https://github.com/nodejs/node/commit/02faa1a50c)] - **build,deps**: less warnings from V8 (Refael Ackermann) [#26405](https://github.com/nodejs/node/pull/26405)
- [[`c2471538ef`](https://github.com/nodejs/node/commit/c2471538ef)] - **build,win**: simplify new `msbuild_arg` option (Refael Ackermann) [#26431](https://github.com/nodejs/node/pull/26431)
- [[`8c864deaa4`](https://github.com/nodejs/node/commit/8c864deaa4)] - **child_process**: fire close event from stdio (kohta ito) [#22892](https://github.com/nodejs/node/pull/22892)
- [[`cba23ed92a`](https://github.com/nodejs/node/commit/cba23ed92a)] - **cluster**: refactor empty for in round_robin_handle.js (gengjiawen) [#26560](https://github.com/nodejs/node/pull/26560)
- [[`2a3cca7ec5`](https://github.com/nodejs/node/commit/2a3cca7ec5)] - **cluster**: improve for-loop (gengjiawen) [#26336](https://github.com/nodejs/node/pull/26336)
- [[`b9787fd5f3`](https://github.com/nodejs/node/commit/b9787fd5f3)] - **crypto**: check for invalid chacha20-poly1305 IVs (Sam Roberts) [#26537](https://github.com/nodejs/node/pull/26537)
- [[`991ea8add3`](https://github.com/nodejs/node/commit/991ea8add3)] - **crypto**: simplify GetPublicOrPrivateKeyFromJs (Tobias Nießen) [#26454](https://github.com/nodejs/node/pull/26454)
- [[`7155aafbab`](https://github.com/nodejs/node/commit/7155aafbab)] - **crypto**: don't call SSL_CTX_set_ciphersuites on boringssl (Jeremy Apthorp) [#26365](https://github.com/nodejs/node/pull/26365)
- [[`01e69f948d`](https://github.com/nodejs/node/commit/01e69f948d)] - **deps**: v8, backport 2d08967 (Benjamin) [#26413](https://github.com/nodejs/node/pull/26413)
- [[`28dc54bc56`](https://github.com/nodejs/node/commit/28dc54bc56)] - **deps**: update OpenSSL upgrade process (Sam Roberts) [#26378](https://github.com/nodejs/node/pull/26378)
- [[`58957264a5`](https://github.com/nodejs/node/commit/58957264a5)] - **deps**: openssl-1.1.1b no longer packages .gitignore (Sam Roberts) [#26327](https://github.com/nodejs/node/pull/26327)
- [[`88079caffa`](https://github.com/nodejs/node/commit/88079caffa)] - **deps**: update archs files for OpenSSL-1.1.1b (Sam Roberts) [#26327](https://github.com/nodejs/node/pull/26327)
- [[`71c4d75c08`](https://github.com/nodejs/node/commit/71c4d75c08)] - **deps**: upgrade openssl sources to 1.1.1b (Sam Roberts) [#26327](https://github.com/nodejs/node/pull/26327)
- [[`dd95d072af`](https://github.com/nodejs/node/commit/dd95d072af)] - **_Revert_** "**deps**: remove OpenSSL git and travis configuration" (Sam Roberts) [#26327](https://github.com/nodejs/node/pull/26327)
- [[`0fc975ddc2`](https://github.com/nodejs/node/commit/0fc975ddc2)] - **deps,tools**: include SipHash in LICENSE (Rod Vagg) [#26367](https://github.com/nodejs/node/pull/26367)
- [[`b9cfaa3c65`](https://github.com/nodejs/node/commit/b9cfaa3c65)] - **doc**: fix misleading sentence in http.md (Luigi Pinca) [#26465](https://github.com/nodejs/node/pull/26465)
- [[`6f685706a0`](https://github.com/nodejs/node/commit/6f685706a0)] - **doc**: fix typo in http2.md (TJKoury) [#26616](https://github.com/nodejs/node/pull/26616)
- [[`e2aaee0ffd`](https://github.com/nodejs/node/commit/e2aaee0ffd)] - **doc**: edit "Using git-node" section of Guide (Rich Trott) [#26580](https://github.com/nodejs/node/pull/26580)
- [[`667a4026e7`](https://github.com/nodejs/node/commit/667a4026e7)] - **doc**: add version for http.createServer() options addition (Ben Swinburne) [#25001](https://github.com/nodejs/node/pull/25001)
- [[`fdad4d2673`](https://github.com/nodejs/node/commit/fdad4d2673)] - **doc**: document diverging MessagePort.onmessage handling (Anna Henningsen) [#26487](https://github.com/nodejs/node/pull/26487)
- [[`5ad9929d12`](https://github.com/nodejs/node/commit/5ad9929d12)] - **doc**: add inspector API example for heapdump (Sam Roberts) [#26498](https://github.com/nodejs/node/pull/26498)
- [[`76c22f8f6f`](https://github.com/nodejs/node/commit/76c22f8f6f)] - **doc**: edit Landing Pull Requests (Rich Trott) [#26536](https://github.com/nodejs/node/pull/26536)
- [[`414ad11e2b`](https://github.com/nodejs/node/commit/414ad11e2b)] - **doc**: document fake ENOTFOUND as a system error (cjihrig) [#26495](https://github.com/nodejs/node/pull/26495)
- [[`7323ffb436`](https://github.com/nodejs/node/commit/7323ffb436)] - **doc**: add decode() & encode() methods into querystring.md (ZYSzys) [#23889](https://github.com/nodejs/node/pull/23889)
- [[`931174fd54`](https://github.com/nodejs/node/commit/931174fd54)] - **doc**: remove tsc-review (Rich Trott) [#26506](https://github.com/nodejs/node/pull/26506)
- [[`124203758f`](https://github.com/nodejs/node/commit/124203758f)] - **doc**: update partner communities link in releases.md (Beth Griggs) [#26475](https://github.com/nodejs/node/pull/26475)
- [[`693505b006`](https://github.com/nodejs/node/commit/693505b006)] - **doc**: fix nits in writing-tests.md (Vse Mozhet Byt) [#26543](https://github.com/nodejs/node/pull/26543)
- [[`5897bf4621`](https://github.com/nodejs/node/commit/5897bf4621)] - **doc**: edit "Involving the TSC" (Rich Trott) [#26481](https://github.com/nodejs/node/pull/26481)
- [[`e3d79550c7`](https://github.com/nodejs/node/commit/e3d79550c7)] - **doc**: add guidance on console output in tests (Sam Roberts) [#26456](https://github.com/nodejs/node/pull/26456)
- [[`2ee9a962d7`](https://github.com/nodejs/node/commit/2ee9a962d7)] - **doc**: add caveat and tradeoff example to readline (Vse Mozhet Byt) [#26472](https://github.com/nodejs/node/pull/26472)
- [[`9945c28b20`](https://github.com/nodejs/node/commit/9945c28b20)] - **doc**: standardize on End-of-Life capitalization (Rich Trott) [#26442](https://github.com/nodejs/node/pull/26442)
- [[`6cc559fbec`](https://github.com/nodejs/node/commit/6cc559fbec)] - **doc**: add missing https Agent maxCachedSessions (Nicolas Moteau) [#26433](https://github.com/nodejs/node/pull/26433)
- [[`ca2328d26a`](https://github.com/nodejs/node/commit/ca2328d26a)] - **doc**: edit deprecation section of Collaborator Guide (Rich Trott) [#26419](https://github.com/nodejs/node/pull/26419)
- [[`05b92c96a4`](https://github.com/nodejs/node/commit/05b92c96a4)] - **doc**: fix the example implementation of MemoryRetainer (Joyee Cheung) [#26262](https://github.com/nodejs/node/pull/26262)
- [[`8b8297d05b`](https://github.com/nodejs/node/commit/8b8297d05b)] - **doc**: clarify http.Agent constructor options (Luigi Pinca) [#26412](https://github.com/nodejs/node/pull/26412)
- [[`9299fb8856`](https://github.com/nodejs/node/commit/9299fb8856)] - **doc**: update AUTHORS list (Anna Henningsen) [#26383](https://github.com/nodejs/node/pull/26383)
- [[`d2e9e526c5`](https://github.com/nodejs/node/commit/d2e9e526c5)] - **doc**: hello addon example should return "world" (Geir Hauge) [#26328](https://github.com/nodejs/node/pull/26328)
- [[`7e40ce1e9f`](https://github.com/nodejs/node/commit/7e40ce1e9f)] - **doc**: fix nits in report docs (Vse Mozhet Byt) [#26461](https://github.com/nodejs/node/pull/26461)
- [[`e79f0c23ad`](https://github.com/nodejs/node/commit/e79f0c23ad)] - **doc**: fix up N-API support matrix (Michael Dawson) [#26377](https://github.com/nodejs/node/pull/26377)
- [[`56adebf789`](https://github.com/nodejs/node/commit/56adebf789)] - **domain**: set `.domain` non-enumerable on resources (Jordan Harband) [#26210](https://github.com/nodejs/node/pull/26210)
- [[`8b0164aa26`](https://github.com/nodejs/node/commit/8b0164aa26)] - **events**: improve for-loop (gengjiawen) [#26354](https://github.com/nodejs/node/pull/26354)
- [[`83fba1ebf2`](https://github.com/nodejs/node/commit/83fba1ebf2)] - **events**: onceWrapper returns target value (himself65) [#25818](https://github.com/nodejs/node/pull/25818)
- [[`16d908939d`](https://github.com/nodejs/node/commit/16d908939d)] - **http**: send connection: close when closing conn (Yann Hamon) [#26467](https://github.com/nodejs/node/pull/26467)
- [[`bf7a52b764`](https://github.com/nodejs/node/commit/bf7a52b764)] - **http**: improve for-loop readability in \_http_outgoing.js (gengjiawen) [#26408](https://github.com/nodejs/node/pull/26408)
- [[`c661d8c608`](https://github.com/nodejs/node/commit/c661d8c608)] - **http**: remove unused variable in \_http_server.js (gengjiawen) [#26407](https://github.com/nodejs/node/pull/26407)
- [[`4886fbfbee`](https://github.com/nodejs/node/commit/4886fbfbee)] - **http**: check for existance in resetHeadersTimeoutOnReqEnd (Matteo Collina) [#26402](https://github.com/nodejs/node/pull/26402)
- [[`6adcc6f574`](https://github.com/nodejs/node/commit/6adcc6f574)] - **http2**: `Http2ServerResponse.end()` should always return self (Robert Nagy) [#24346](https://github.com/nodejs/node/pull/24346)
- [[`529b0c04cf`](https://github.com/nodejs/node/commit/529b0c04cf)] - **http2**: refactor deprecated method in core.js (gengjiawen) [#26275](https://github.com/nodejs/node/pull/26275)
- [[`4b6c653d4d`](https://github.com/nodejs/node/commit/4b6c653d4d)] - **https**: add missing localPort while create socket (leeight) [#24554](https://github.com/nodejs/node/pull/24554)
- [[`6b004e0e02`](https://github.com/nodejs/node/commit/6b004e0e02)] - **lib**: refactor deprecated function in readline.js (gengjiawen) [#26494](https://github.com/nodejs/node/pull/26494)
- [[`f128008474`](https://github.com/nodejs/node/commit/f128008474)] - **lib**: import TextEncoder and TextDecoder from `internal/encoding` (Joyee Cheung) [#26547](https://github.com/nodejs/node/pull/26547)
- [[`fe6c419503`](https://github.com/nodejs/node/commit/fe6c419503)] - **lib**: migrate process.binding to internalBinding (Beni von Cheni) [#24952](https://github.com/nodejs/node/pull/24952)
- [[`9398d84735`](https://github.com/nodejs/node/commit/9398d84735)] - **lib,src**: remove usage of \_externalStream (Anna Henningsen) [#26510](https://github.com/nodejs/node/pull/26510)
- [[`1fa5004e81`](https://github.com/nodejs/node/commit/1fa5004e81)] - **lib,test**: improve faulty assert usage detection (Ruben Bridgewater) [#26569](https://github.com/nodejs/node/pull/26569)
- [[`8e7204ed96`](https://github.com/nodejs/node/commit/8e7204ed96)] - **n-api**: improve performance creating strings (Anthony Tuininga) [#26439](https://github.com/nodejs/node/pull/26439)
- [[`c14aa07b94`](https://github.com/nodejs/node/commit/c14aa07b94)] - **net**: use kHandle symbol for accessing native handle (Anna Henningsen) [#26491](https://github.com/nodejs/node/pull/26491)
- [[`275a8f9316`](https://github.com/nodejs/node/commit/275a8f9316)] - **process**: make Symbol.toStringTag writable (Ruben Bridgewater) [#26488](https://github.com/nodejs/node/pull/26488)
- [[`ceebbfb869`](https://github.com/nodejs/node/commit/ceebbfb869)] - **process**: add --pending-deprecation to `process.binding()` (Anna Henningsen) [#26500](https://github.com/nodejs/node/pull/26500)
- [[`1a0004d08e`](https://github.com/nodejs/node/commit/1a0004d08e)] - **repl**: eliminate var in function \_memory (gengjiawen) [#26496](https://github.com/nodejs/node/pull/26496)
- [[`788c57bdc4`](https://github.com/nodejs/node/commit/788c57bdc4)] - **repl**: simplify regex expression (gengjiawen) [#26496](https://github.com/nodejs/node/pull/26496)
- [[`2101371a8a`](https://github.com/nodejs/node/commit/2101371a8a)] - **repl**: remove redundant escape (gengjiawen) [#26496](https://github.com/nodejs/node/pull/26496)
- [[`a0b119182d`](https://github.com/nodejs/node/commit/a0b119182d)] - **(SEMVER-MINOR)** **repl**: add replDefaults to customize the writer (Ruben Bridgewater) [#26375](https://github.com/nodejs/node/pull/26375)
- [[`74ab1aa5d1`](https://github.com/nodejs/node/commit/74ab1aa5d1)] - **report**: rename triggerReport() to writeReport() (cjihrig) [#26527](https://github.com/nodejs/node/pull/26527)
- [[`ac81fd202c`](https://github.com/nodejs/node/commit/ac81fd202c)] - **report**: fix stdout/stderr output formatting (cjihrig) [#26522](https://github.com/nodejs/node/pull/26522)
- [[`2be9e800f1`](https://github.com/nodejs/node/commit/2be9e800f1)] - **report**: warn on process.report object access (cjihrig) [#26414](https://github.com/nodejs/node/pull/26414)
- [[`9f446a1cf4`](https://github.com/nodejs/node/commit/9f446a1cf4)] - **report**: refactor configuration management (cjihrig) [#26414](https://github.com/nodejs/node/pull/26414)
- [[`0abb724bbc`](https://github.com/nodejs/node/commit/0abb724bbc)] - **report**: support RUSAGE_SELF stats on Windows (cjihrig) [#26406](https://github.com/nodejs/node/pull/26406)
- [[`bc09d2f83d`](https://github.com/nodejs/node/commit/bc09d2f83d)] - **src**: fix SplitString to ignore white spaces (himself65) [#26545](https://github.com/nodejs/node/pull/26545)
- [[`5cbd11294d`](https://github.com/nodejs/node/commit/5cbd11294d)] - **src**: de-lint header usage (Refael Ackermann) [#26306](https://github.com/nodejs/node/pull/26306)
- [[`9768ec4ec4`](https://github.com/nodejs/node/commit/9768ec4ec4)] - **src**: remove unused variables (cjihrig) [#26590](https://github.com/nodejs/node/pull/26590)
- [[`8822df838b`](https://github.com/nodejs/node/commit/8822df838b)] - **src**: rename Init and Start overloads to something more distinctive (Joyee Cheung) [#26499](https://github.com/nodejs/node/pull/26499)
- [[`a99fb5419b`](https://github.com/nodejs/node/commit/a99fb5419b)] - **src**: apply clang-tidy various improvement (gengjiawen) [#26470](https://github.com/nodejs/node/pull/26470)
- [[`1d4fd218f2`](https://github.com/nodejs/node/commit/1d4fd218f2)] - **src**: guard against calling `Init()` multiple times (Anna Henningsen) [#26458](https://github.com/nodejs/node/pull/26458)
- [[`989fcef680`](https://github.com/nodejs/node/commit/989fcef680)] - **src**: delete unused method SetTemplateMethod (gengjiawen) [#26451](https://github.com/nodejs/node/pull/26451)
- [[`efadb10085`](https://github.com/nodejs/node/commit/efadb10085)] - **src**: delete unused method SetTemplateMethodNoSideEffect (gengjiawen) [#26451](https://github.com/nodejs/node/pull/26451)
- [[`a11cf3054c`](https://github.com/nodejs/node/commit/a11cf3054c)] - **src**: delete unused variable in env.h (gengjiawen) [#26451](https://github.com/nodejs/node/pull/26451)
- [[`edc4af0e7d`](https://github.com/nodejs/node/commit/edc4af0e7d)] - **src**: merge debug-only `SealHandleScope`s (Anna Henningsen) [#26459](https://github.com/nodejs/node/pull/26459)
- [[`12fb73963c`](https://github.com/nodejs/node/commit/12fb73963c)] - **src**: cleanup in all return paths in node::Start (Gireesh Punathil) [#26471](https://github.com/nodejs/node/pull/26471)
- [[`d688b8a132`](https://github.com/nodejs/node/commit/d688b8a132)] - **src**: remove templating from StreamBase (Jon Moss) [#25142](https://github.com/nodejs/node/pull/25142)
- [[`203fa63a2b`](https://github.com/nodejs/node/commit/203fa63a2b)] - **src**: remove redundant cast in util-inl.h (gengjiawen) [#26410](https://github.com/nodejs/node/pull/26410)
- [[`c7bd21cfff`](https://github.com/nodejs/node/commit/c7bd21cfff)] - **src**: make parameter name const reference in method TriggerNodeReport (gengjiawen) [#26397](https://github.com/nodejs/node/pull/26397)
- [[`bb374d405b`](https://github.com/nodejs/node/commit/bb374d405b)] - **src**: remove redundant call in inspector_io.cc (gengjiawen) [#26427](https://github.com/nodejs/node/pull/26427)
- [[`81c5382f86`](https://github.com/nodejs/node/commit/81c5382f86)] - **src**: remove redundant cast in string_search.h (gengjiawen) [#26426](https://github.com/nodejs/node/pull/26426)
- [[`2a2a4e69dc`](https://github.com/nodejs/node/commit/2a2a4e69dc)] - **src**: remove unused function in cares_wrap.cc (gengjiawen) [#26429](https://github.com/nodejs/node/pull/26429)
- [[`e21fa83dcd`](https://github.com/nodejs/node/commit/e21fa83dcd)] - **src**: fix wrong enum reference in node.cc (gengjiawen) [#26430](https://github.com/nodejs/node/pull/26430)
- [[`0d810b7ef0`](https://github.com/nodejs/node/commit/0d810b7ef0)] - **src**: use the config binding to carry --no-browser-globals (Joyee Cheung) [#26228](https://github.com/nodejs/node/pull/26228)
- [[`88fb7712a8`](https://github.com/nodejs/node/commit/88fb7712a8)] - **src**: fix build when NODE_USE_V8_PLATFORM is not defined (Nitish Sakhawalkar) [#26380](https://github.com/nodejs/node/pull/26380)
- [[`654f4d4338`](https://github.com/nodejs/node/commit/654f4d4338)] - **src**: remove unused variable in node_http2.cc (gengjiawen) [#26395](https://github.com/nodejs/node/pull/26395)
- [[`1d279ac269`](https://github.com/nodejs/node/commit/1d279ac269)] - **src**: remove unused variable in node_native_module.cc (gengjiawen) [#26411](https://github.com/nodejs/node/pull/26411)
- [[`dc2119a955`](https://github.com/nodejs/node/commit/dc2119a955)] - **src**: fix more extra-semi warnings (Jeremy Apthorp) [#26340](https://github.com/nodejs/node/pull/26340)
- [[`170e196205`](https://github.com/nodejs/node/commit/170e196205)] - **src**: forbid handle allocations from Platform tasks (Anna Henningsen) [#26376](https://github.com/nodejs/node/pull/26376)
- [[`9c277c04ad`](https://github.com/nodejs/node/commit/9c277c04ad)] - **src**: allow running tasks without `Environment` (Anna Henningsen) [#26376](https://github.com/nodejs/node/pull/26376)
- [[`622048d539`](https://github.com/nodejs/node/commit/622048d539)] - **src**: prefer to get `Environment` from `Context` (Anna Henningsen) [#26376](https://github.com/nodejs/node/pull/26376)
- [[`716ec00883`](https://github.com/nodejs/node/commit/716ec00883)] - **src**: refactor `Environment::GetCurrent(isolate)` usage (Anna Henningsen) [#26376](https://github.com/nodejs/node/pull/26376)
- [[`f99349d416`](https://github.com/nodejs/node/commit/f99349d416)] - **src**: fix if indent in node_http2.cc (gengjiawen) [#26396](https://github.com/nodejs/node/pull/26396)
- [[`b8abb81666`](https://github.com/nodejs/node/commit/b8abb81666)] - **src**: remove unused struct in test_inspector_socket.cc (gengjiawen) [#26284](https://github.com/nodejs/node/pull/26284)
- [[`da457a56be`](https://github.com/nodejs/node/commit/da457a56be)] - **src**: remove unused namespace (Aymen Naghmouchi) [#26318](https://github.com/nodejs/node/pull/26318)
- [[`b45c22bc87`](https://github.com/nodejs/node/commit/b45c22bc87)] - **src**: use object to pass `Environment` to functions (Anna Henningsen) [#26382](https://github.com/nodejs/node/pull/26382)
- [[`61baa45581`](https://github.com/nodejs/node/commit/61baa45581)] - **src**: document DoWrite() usage expectations (Sam Roberts) [#26339](https://github.com/nodejs/node/pull/26339)
- [[`82a68cebe3`](https://github.com/nodejs/node/commit/82a68cebe3)] - **stream**: ensure writable.destroy() emits error once (Luigi Pinca) [#26057](https://github.com/nodejs/node/pull/26057)
- [[`9e82ee926a`](https://github.com/nodejs/node/commit/9e82ee926a)] - **test**: fix test case in test-http2-respond-file-304.js (gengjiawen) [#26565](https://github.com/nodejs/node/pull/26565)
- [[`13253a3d08`](https://github.com/nodejs/node/commit/13253a3d08)] - **test**: use semicolon for clarity (gengjiawen) [#26566](https://github.com/nodejs/node/pull/26566)
- [[`adfbfc985c`](https://github.com/nodejs/node/commit/adfbfc985c)] - **test**: fix test by removing node-inspect/lib/\_inspect (Ruben Bridgewater) [#26619](https://github.com/nodejs/node/pull/26619)
- [[`e1a55e76b4`](https://github.com/nodejs/node/commit/e1a55e76b4)] - **test**: fix syntax error in test-dns-idna2008.js when failing (Refael Ackermann) [#26570](https://github.com/nodejs/node/pull/26570)
- [[`cccd3a3849`](https://github.com/nodejs/node/commit/cccd3a3849)] - **test**: fix compiler warning in test_string.c (Daniel Bevenius) [#26539](https://github.com/nodejs/node/pull/26539)
- [[`2c55282226`](https://github.com/nodejs/node/commit/2c55282226)] - **test**: mark test-worker-prof as flake on all platforms (Refael Ackermann) [#26600](https://github.com/nodejs/node/pull/26600)
- [[`0f8d8d6262`](https://github.com/nodejs/node/commit/0f8d8d6262)] - **test**: cover triggerReport() failure case (cjihrig) [#26524](https://github.com/nodejs/node/pull/26524)
- [[`5a0ed0b0b5`](https://github.com/nodejs/node/commit/5a0ed0b0b5)] - **test**: cover stdout/stderr usage in triggerReport() (cjihrig) [#26522](https://github.com/nodejs/node/pull/26522)
- [[`bf7836511d`](https://github.com/nodejs/node/commit/bf7836511d)] - **test**: mark `test-worker-prof` as Flaky on ARM (Refael Ackermann) [#26557](https://github.com/nodejs/node/pull/26557)
- [[`d590a458a6`](https://github.com/nodejs/node/commit/d590a458a6)] - **test**: rewrite ocsp test to run in parallel (Sam Roberts) [#26460](https://github.com/nodejs/node/pull/26460)
- [[`476dc7e612`](https://github.com/nodejs/node/commit/476dc7e612)] - **test**: de-flake test-dns-idna2008.js (Refael Ackermann) [#26473](https://github.com/nodejs/node/pull/26473)
- [[`78c4dbdc20`](https://github.com/nodejs/node/commit/78c4dbdc20)] - **test**: bump test-bootstrap-modules.js limit (Joyee Cheung) [#26520](https://github.com/nodejs/node/pull/26520)
- [[`153a29c1c3`](https://github.com/nodejs/node/commit/153a29c1c3)] - **test**: refactor test/report/test-report-signal.js (cjihrig) [#26446](https://github.com/nodejs/node/pull/26446)
- [[`71a4b24119`](https://github.com/nodejs/node/commit/71a4b24119)] - **test**: remove usage of `process.binding()` (Anna Henningsen) [#26304](https://github.com/nodejs/node/pull/26304)
- [[`2b2471b0fd`](https://github.com/nodejs/node/commit/2b2471b0fd)] - **test**: fix tests so they work in worker threads (Richard Lau) [#26453](https://github.com/nodejs/node/pull/26453)
- [[`a67fea52c4`](https://github.com/nodejs/node/commit/a67fea52c4)] - **test**: relax timer check in test-report-uv-handles.js (Richard Lau) [#26434](https://github.com/nodejs/node/pull/26434)
- [[`dbb7a029d5`](https://github.com/nodejs/node/commit/dbb7a029d5)] - **test**: improve code coverage in timers (Juan José Arboleda) [#26310](https://github.com/nodejs/node/pull/26310)
- [[`e1aa5106a7`](https://github.com/nodejs/node/commit/e1aa5106a7)] - **test**: remove flaky designation for test_threadsafe_function (Rich Trott) [#26403](https://github.com/nodejs/node/pull/26403)
- [[`143dbb3db8`](https://github.com/nodejs/node/commit/143dbb3db8)] - **timers**: remove dead code and simplify args check (Ruben Bridgewater) [#26555](https://github.com/nodejs/node/pull/26555)
- [[`1c8076ef58`](https://github.com/nodejs/node/commit/1c8076ef58)] - **tools**: fix cpplint.py header rules (Refael Ackermann) [#26306](https://github.com/nodejs/node/pull/26306)
- [[`a32c7492f2`](https://github.com/nodejs/node/commit/a32c7492f2)] - **tools**: update ESLint to 5.15.1 (cjihrig) [#26447](https://github.com/nodejs/node/pull/26447)
- [[`9d92887cde`](https://github.com/nodejs/node/commit/9d92887cde)] - **tools**: update to mdast-util-to-hast v3.0.2 (Sam Ruby) [#22140](https://github.com/nodejs/node/pull/22140)
- [[`3e2e779dc9`](https://github.com/nodejs/node/commit/3e2e779dc9)] - **tools**: update capitalized-comments rule (Ruben Bridgewater) [#26483](https://github.com/nodejs/node/pull/26483)
- [[`dcfdef5467`](https://github.com/nodejs/node/commit/dcfdef5467)] - **tools**: update generated lint-md.js (Refael Ackermann) [#26441](https://github.com/nodejs/node/pull/26441)
- [[`4835504d7c`](https://github.com/nodejs/node/commit/4835504d7c)] - **tools**: update `node-lint-md-cli-rollup` version 2 (Refael Ackermann) [#26441](https://github.com/nodejs/node/pull/26441)
- [[`972a0f9f3e`](https://github.com/nodejs/node/commit/972a0f9f3e)] - **tools**: use dmn@2.2.1 to remove unneeded files (Rich Trott) [#26462](https://github.com/nodejs/node/pull/26462)
- [[`9f1cc735ab`](https://github.com/nodejs/node/commit/9f1cc735ab)] - **tools**: update dmn to 2.2.1 in update scripts (Rich Trott) [#26462](https://github.com/nodejs/node/pull/26462)
- [[`b879c1e2e1`](https://github.com/nodejs/node/commit/b879c1e2e1)] - **tools**: fix test.py --shell (Yang Guo) [#26449](https://github.com/nodejs/node/pull/26449)
- [[`3b19cbfa3d`](https://github.com/nodejs/node/commit/3b19cbfa3d)] - **tools**: update remark-preset-lint-node to 1.5.0 (Rich Trott) [#26442](https://github.com/nodejs/node/pull/26442)
- [[`0a1537e4e6`](https://github.com/nodejs/node/commit/0a1537e4e6)] - **tools**: add no-var lint rule for tools directory (shisama) [#26398](https://github.com/nodejs/node/pull/26398)
- [[`57198f2b82`](https://github.com/nodejs/node/commit/57198f2b82)] - **tools**: replace var to let/const (Masashi Hirano) [#26398](https://github.com/nodejs/node/pull/26398)
- [[`55b830476a`](https://github.com/nodejs/node/commit/55b830476a)] - **tools**: add mailmap support for Co-authored-by tags (Anna Henningsen) [#26383](https://github.com/nodejs/node/pull/26383)
- [[`dc4258ad26`](https://github.com/nodejs/node/commit/dc4258ad26)] - **tools**: apply stricter linting to tools directory (Rich Trott) [#26394](https://github.com/nodejs/node/pull/26394)
- [[`580ae5672f`](https://github.com/nodejs/node/commit/580ae5672f)] - **tools**: refactor tools JS code (Rich Trott) [#26394](https://github.com/nodejs/node/pull/26394)
- [[`d841a89e47`](https://github.com/nodejs/node/commit/d841a89e47)] - **tools**: roll inspector_protocol to f67ec5 (Pavel Feldman) [#26303](https://github.com/nodejs/node/pull/26303)
- [[`c57510effa`](https://github.com/nodejs/node/commit/c57510effa)] - **tools**: rebuild lint-md.js (Rich Trott) [#26393](https://github.com/nodejs/node/pull/26393)
- [[`c2d12513f7`](https://github.com/nodejs/node/commit/c2d12513f7)] - **tools**: update node-lint-md-cli-rollup lockfile (Rich Trott) [#26393](https://github.com/nodejs/node/pull/26393)
- [[`5bdf71c8bf`](https://github.com/nodejs/node/commit/5bdf71c8bf)] - **tools**: update ESLint to 5.15.0 (cjihrig) [#26391](https://github.com/nodejs/node/pull/26391)
- [[`1de9e138aa`](https://github.com/nodejs/node/commit/1de9e138aa)] - **url**: require encodeStr from internal/querystring (ZYSzys) [#26538](https://github.com/nodejs/node/pull/26538)
- [[`3ad58f3e45`](https://github.com/nodejs/node/commit/3ad58f3e45)] - **win,build**: update Windows build documentation (Jon Kunkee) [#25995](https://github.com/nodejs/node/pull/25995)
- [[`e8f4096be1`](https://github.com/nodejs/node/commit/e8f4096be1)] - **win,build**: scope NASM warning to only x64 and x86 (Jon Kunkee) [#25995](https://github.com/nodejs/node/pull/25995)
- [[`7e4592e83f`](https://github.com/nodejs/node/commit/7e4592e83f)] - **win,build**: add ARM64 sections to common.gypi (Jon Kunkee) [#25995](https://github.com/nodejs/node/pull/25995)
- [[`8e60193aef`](https://github.com/nodejs/node/commit/8e60193aef)] - **win,build**: add ARM64 support to vcbuild.bat (Jon Kunkee) [#25995](https://github.com/nodejs/node/pull/25995)
- [[`d75cb919d0`](https://github.com/nodejs/node/commit/d75cb919d0)] - **win,build**: add arbitrary and binlog options (Jon Kunkee) [#25994](https://github.com/nodejs/node/pull/25994)
- [[`62801b9320`](https://github.com/nodejs/node/commit/62801b9320)] - **worker**: release native Worker object earlier (Anna Henningsen) [#26542](https://github.com/nodejs/node/pull/26542)
- [[`73370b4584`](https://github.com/nodejs/node/commit/73370b4584)] - **worker**: remove `ERR_CLOSED_MESSAGE_PORT` (Anna Henningsen) [#26487](https://github.com/nodejs/node/pull/26487)

Windows 32-bit Installer: https://nodejs.org/dist/v11.12.0/node-v11.12.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v11.12.0/node-v11.12.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v11.12.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v11.12.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v11.12.0/node-v11.12.0.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v11.12.0/node-v11.12.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v11.12.0/node-v11.12.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v11.12.0/node-v11.12.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v11.12.0/node-v11.12.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v11.12.0/node-v11.12.0-aix-ppc64.tar.gz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v11.12.0/node-v11.12.0-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v11.12.0/node-v11.12.0-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v11.12.0/node-v11.12.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v11.12.0/node-v11.12.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v11.12.0/node-v11.12.0.tar.gz \
Other release files: https://nodejs.org/dist/v11.12.0/ \
Documentation: https://nodejs.org/docs/v11.12.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

8d0860b866cff03b41cbb611e07962da087cfd238147ea13a66311590b5e72ef  node-v11.12.0-aix-ppc64.tar.gz
93d68c1af41d02b262b3383d69b46eb326707ec010b321ad5655b91c4956e783  node-v11.12.0-darwin-x64.tar.gz
32f0694f1b62c1872d86c310dfc17f33075dc8ad205c7cb9fa9a0967913bc3e5  node-v11.12.0-darwin-x64.tar.xz
817facbb757bc44478fb83bfd06dce4612b65145cb7876f3850d876874effd24  node-v11.12.0-headers.tar.gz
e57301a8f9cead5cc43e06843624b3048d4bf636f6af42969aba2cda2773b6e4  node-v11.12.0-headers.tar.xz
044360cc730d90b579fecea7d49861a23c326f058b39e9ac18c391d77073736d  node-v11.12.0-linux-arm64.tar.gz
9e89838e290c9bb478351bd403707d0b741ec56021c379c9f6da515a4cd1064a  node-v11.12.0-linux-arm64.tar.xz
9cc6a13407b77ba74a96470cb858c6a817373fb0f92f5fbf8139fe4615db40c1  node-v11.12.0-linux-armv6l.tar.gz
5e2165c303cafe716b17ea35022582f511cfa558759400b0b5ed3262feda8ee7  node-v11.12.0-linux-armv6l.tar.xz
b1ee1ac0fb55add0ba570a35e0109a1e2257392af9dd9c7fc53e1c63675a90b5  node-v11.12.0-linux-armv7l.tar.gz
d742e789dedea046762a343162c4fa0bb3e9facb85695e1b7e5fc43076e4edaf  node-v11.12.0-linux-armv7l.tar.xz
fb53dd8745ea850ebfe159b07228fda7fa9e24b86da284d8b72f3a7acdc0de5b  node-v11.12.0-linux-ppc64le.tar.gz
78f367b7889e149202ddb4dee4311eaef7135bc6cf95d32864dfd74ad586fe19  node-v11.12.0-linux-ppc64le.tar.xz
8666317bd1b165764e5f0b394895d8c418908c234c9f768455f189e5f386f139  node-v11.12.0-linux-s390x.tar.gz
5004703427870df0e56b1892ec17a210fa8f91c5add88e2a62ab1832e9cb25a4  node-v11.12.0-linux-s390x.tar.xz
58be8912097b93098bbbc3c1b536b2f9e70efdca64d63d7e4cdb4dbd40b3e751  node-v11.12.0-linux-x64.tar.gz
1c6bb93a24eda832708c1c10ec20316e1e4f30b3cfca9c5ee5d446762414b116  node-v11.12.0-linux-x64.tar.xz
e6318b65ec043ade9954ea98d9e5a2b74aea83038f804d0e8623169bd2738571  node-v11.12.0-sunos-x64.tar.gz
6ca1919968da456fc40751ae32031bb172ae663d00b5960e31962c2da00a03af  node-v11.12.0-sunos-x64.tar.xz
4891897f29be782320a77c7be060621351c69973b7a3d8a3122126dd6c270d95  node-v11.12.0-win-x64.7z
68e5bca1d6dd6b3de20870e7c593f9a890c48d2c9c83e15034baad6f7c0da426  node-v11.12.0-win-x64.zip
abd9baa1d8557309c06cb4fd548c83d90c5c389c53bc31bb8c2f7472a51befd9  node-v11.12.0-win-x86.7z
6b9d74bffc13a2001afd07a45889e1818cbc33c43a79f0e0b7244b96a71e8bec  node-v11.12.0-win-x86.zip
1f2de3ac2c6141158a2f72c2acfd692cb54d89eb37c487e110e975fc3632efb2  node-v11.12.0-x64.msi
f74d95c5fc429391168e2c9a382f6690d6f62001633542ee2c464851e2f66413  node-v11.12.0-x86.msi
660381c12d2c8d1cc44ddeb1ac7cac4d3a1951e51b38d87b27da231783d5cdb7  node-v11.12.0.pkg
7d408dcd485f7193ab37e1f36d0a38676d5dbc9c91329c775e5afe6c687393b8  node-v11.12.0.tar.gz
8429778f13eb0194768ff988ae94c34713656e21de569e8ffd92aa67c8e2178c  node-v11.12.0.tar.xz
fa2db42ce0c58bb18ca71348abf9893c2ded1503a3b6171635d43af51b622fbf  win-x64/node.exe
d22625cd40e6125aef15dc9804142ecf4c53f90b572c23b905d1c281c0ee8c4b  win-x64/node.lib
056721e94741209a5d4a95e1ea4b99acce25764beb3b4b401c8cafc8d3259609  win-x64/node_pdb.7z
ac23dc316e9763aab28d65c1c042f6dfd09efadc31521bf19a75ee19c271d444  win-x64/node_pdb.zip
68c89d78aa544b259ee05affed4834c5d880ac109625fb62e7b2f529e0afd855  win-x86/node.exe
4971766317503bfa2cb979d94a97493713a3c7ead91b1c927d6a92a0d7d5dcc2  win-x86/node.lib
a85e69edd29f10540cfba8fb30f27180a650e0322932000aa6b006736c5dabff  win-x86/node_pdb.7z
c7c590c544ea71c299701058c7b1d15ff1532cb49450df0ea20608399c5e8c55  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEpIwr7mgOhBYyzU5E8HSWs+s8F2IFAlyMFDkACgkQ8HSWs+s8
F2IAfhAAr88aQHo9cIaUxeTLYTdhXsytaKGZm/Rf0lW1FBHbxd4RdGqc8jD8vUFX
LVRBRSFZ0NTT0OZyiBnZvumdMs+RmzXHfDqkmmIMwg/Ww/QzR2UmkzDtklT6hKbc
wqd/J9ExTjzGdaS6rvzmAuMXjGYH2XWcJwmi8QuX32Fum1Sq9o+L1YbQMzVOXAuE
BShlVhu3opC1SjW1vovxMmXZ3riihbZa3AHhzrLmcO0JtzTQoUNQwHsyOvRcqTBX
wtyo9AH5wNNUSV7nI7IYKTOu/CJPHCPDo0Vi28F3MEmCA2ItwK0ZXfU/3x0Rdi7Y
7PmmxzPSHMIef6omyZG4ngdGYJb+RsVrEhQ3EnznHTZQomky8ZGKlUFnIgt22FA7
EtZnBIHAZducsfR406xUF9KgTuFRPxf8OD1tgJxpDDET/sn9qQd6XbN4MQMIRk82
P3dNwSE+v0mDkrluQ6hxr5Lufu7259XHV9Khl1SjxxlQG4Ki4khHNlB2vz/4l0qA
1JdTQ3SP/YLy6WZ5QsgJaqDshpr4klvxB12YSsnJj8Cz1cymq/G1W0xGJ+abPERy
OBH/6ClkYScKDQD2v9IsLcwf7LXMYrMSLo4abLSwijhWbPpZqON3BSpQs9z3MxXY
cu0jniNnkizoOjzR9JPCAgRI4/B2zhRsltIzLavqPVxoE3RkxVc=
=DOBi
-----END PGP SIGNATURE-----

```
