---
date: '2019-12-17T22:36:55.661Z'
category: release
title: Node v13.4.0 (Current)
layout: blog-post
author: <PERSON><PERSON>
---

### Notable Changes

- **deps**:
  - update npm to 6.13.4 [#30904](https://github.com/nodejs/node/pull/30904)
  - update uvwasi (<PERSON>) [#30745](https://github.com/nodejs/node/pull/30745)
  - upgrade to libuv 1.34.0 (<PERSON>) [#30783](https://github.com/nodejs/node/pull/30783)
- **doc**:
  - docs deprecate http finished (<PERSON>) [#28679](https://github.com/nodejs/node/pull/28679)
- **events**:
  - add captureRejection option (<PERSON>) [#27867](https://github.com/nodejs/node/pull/27867)
- **http**:
  - add captureRejection support (<PERSON>) [#27867](https://github.com/nodejs/node/pull/27867)
  - llhttp opt-in insecure HTTP header parsing (<PERSON>) [#30567](https://github.com/nodejs/node/pull/30567)
- **http2**:
  - implement capture rection for 'request' and 'stream' events (Matteo <PERSON>) [#27867](https://github.com/nodejs/node/pull/27867)
- **net**:
  - implement capture rejections for 'connection' event (Matteo Collina) [#27867](https://github.com/nodejs/node/pull/27867)
- **repl**:
  - support previews by eager evaluating input (Ruben Bridgewater) [#30811](https://github.com/nodejs/node/pull/30811)
- **stream**:
  - add support for captureRejection option (Matteo Collina) [#27867](https://github.com/nodejs/node/pull/27867)
- **tls**:
  - implement capture rejections for 'secureConnection' event (Matteo Collina) [#27867](https://github.com/nodejs/node/pull/27867)
  - expose IETF name for current cipher suite (Sam Roberts) [#30637](https://github.com/nodejs/node/pull/30637)
- **worker**:
  - add argv constructor option (legendecas) [#30559](https://github.com/nodejs/node/pull/30559)

### Commits

- [[`1c4b2f15d9`](https://github.com/nodejs/node/commit/1c4b2f15d9)] - **assert,util**: stricter type comparison using deep equal comparisons (Ruben Bridgewater) [#30764](https://github.com/nodejs/node/pull/30764)
- [[`78eaf50693`](https://github.com/nodejs/node/commit/78eaf50693)] - **benchmark**: improve `--filter` pattern matching (Matheus Marchini) [#29987](https://github.com/nodejs/node/pull/29987)
- [[`ad4d52d1b5`](https://github.com/nodejs/node/commit/ad4d52d1b5)] - **benchmark**: add more util inspect and format benchmarks (Ruben Bridgewater) [#30767](https://github.com/nodejs/node/pull/30767)
- [[`d90815d08e`](https://github.com/nodejs/node/commit/d90815d08e)] - **build**: on Android, use android log library to print stack traces (Giovanni Campagna) [#29388](https://github.com/nodejs/node/pull/29388)
- [[`d1c4fccde2`](https://github.com/nodejs/node/commit/d1c4fccde2)] - **build**: fix library version and compile flags on Android (Giovanni Campagna) [#29388](https://github.com/nodejs/node/pull/29388)
- [[`dfd3a4d6c1`](https://github.com/nodejs/node/commit/dfd3a4d6c1)] - **(SEMVER-MINOR)** **build**: add flag to enable pointer compression (Matteo Collina) [#30463](https://github.com/nodejs/node/pull/30463)
- [[`3d05d4beea`](https://github.com/nodejs/node/commit/3d05d4beea)] - **build**: ease DragonFlyBSD build (David Carlier) [#30201](https://github.com/nodejs/node/pull/30201)
- [[`43e947a155`](https://github.com/nodejs/node/commit/43e947a155)] - **build**: remove (almost) unused macros/constants (Benjamin Coe) [#30755](https://github.com/nodejs/node/pull/30755)
- [[`0379fb65c1`](https://github.com/nodejs/node/commit/0379fb65c1)] - **deps**: update npm to 6.13.4 (Isaac Z. Schlueter) [#30904](https://github.com/nodejs/node/pull/30904)
- [[`13fe9f7cc8`](https://github.com/nodejs/node/commit/13fe9f7cc8)] - **deps**: update uvwasi (Anna Henningsen) [#30745](https://github.com/nodejs/node/pull/30745)
- [[`ca47f72868`](https://github.com/nodejs/node/commit/ca47f72868)] - **(SEMVER-MINOR)** **deps**: upgrade to libuv 1.34.0 (Colin Ihrig) [#30783](https://github.com/nodejs/node/pull/30783)
- [[`458860691c`](https://github.com/nodejs/node/commit/458860691c)] - **deps**: fix OPENSSLDIR on Windows (Shigeki Ohtsu) [#29456](https://github.com/nodejs/node/pull/29456)
- [[`b3ae532392`](https://github.com/nodejs/node/commit/b3ae532392)] - **doc**: clarify build support text (Rich Trott) [#30899](https://github.com/nodejs/node/pull/30899)
- [[`8bf0da6c93`](https://github.com/nodejs/node/commit/8bf0da6c93)] - **doc**: edit colorMode information (Rich Trott) [#30887](https://github.com/nodejs/node/pull/30887)
- [[`df9df1883e`](https://github.com/nodejs/node/commit/df9df1883e)] - **doc**: fix argument type of setAAD (Tobias Nießen) [#30863](https://github.com/nodejs/node/pull/30863)
- [[`9d1c793ceb`](https://github.com/nodejs/node/commit/9d1c793ceb)] - **doc**: clarify Tier 2 implications in BUILDING.md (Rich Trott) [#30866](https://github.com/nodejs/node/pull/30866)
- [[`1cce00073e`](https://github.com/nodejs/node/commit/1cce00073e)] - **doc**: add code example to inspector.url() method (Juan José Arboleda) [#29496](https://github.com/nodejs/node/pull/29496)
- [[`93ca4f4098`](https://github.com/nodejs/node/commit/93ca4f4098)] - **doc**: deprecate http finished (Robert Nagy) [#28679](https://github.com/nodejs/node/pull/28679)
- [[`0022d7544a`](https://github.com/nodejs/node/commit/0022d7544a)] - **doc**: improve doc Http2Stream: FrameError, Timeout and Trailers (dev-313) [#30373](https://github.com/nodejs/node/pull/30373)
- [[`2123d53c28`](https://github.com/nodejs/node/commit/2123d53c28)] - **doc**: include line/cursor in readline documentation (Jeremy Albright) [#30667](https://github.com/nodejs/node/pull/30667)
- [[`1baa6ab075`](https://github.com/nodejs/node/commit/1baa6ab075)] - **doc**: improve napi formatting (Ruben Bridgewater) [#30772](https://github.com/nodejs/node/pull/30772)
- [[`1d5c4e21de`](https://github.com/nodejs/node/commit/1d5c4e21de)] - **doc**: add documentation about node_mksnapshot and mkcodecache (Joyee Cheung) [#30773](https://github.com/nodejs/node/pull/30773)
- [[`67823e8fc4`](https://github.com/nodejs/node/commit/67823e8fc4)] - **doc**: remove imprecise and redundant testing text (Rich Trott) [#30763](https://github.com/nodejs/node/pull/30763)
- [[`7cb84fdbe5`](https://github.com/nodejs/node/commit/7cb84fdbe5)] - **doc**: remove usage of "Node" in favor of "Node.js" (Rich Trott) [#30758](https://github.com/nodejs/node/pull/30758)
- [[`510eb3a6eb`](https://github.com/nodejs/node/commit/510eb3a6eb)] - **doc**: revise addons introduction for brevity and clarity (Rich Trott) [#30756](https://github.com/nodejs/node/pull/30756)
- [[`543bf9d8ea`](https://github.com/nodejs/node/commit/543bf9d8ea)] - **doc**: fix up N-API doc (NickNaso) [#30656](https://github.com/nodejs/node/pull/30656)
- [[`2c0f1edfd5`](https://github.com/nodejs/node/commit/2c0f1edfd5)] - **doc**: adds assert doc for strict mode with pointer to strict equality (Shobhit Chittora) [#30486](https://github.com/nodejs/node/pull/30486)
- [[`9428304d4a`](https://github.com/nodejs/node/commit/9428304d4a)] - **doc**: Buffer.toString(): add note about invalid data (Jan-Philip Gehrcke) [#30706](https://github.com/nodejs/node/pull/30706)
- [[`8369562757`](https://github.com/nodejs/node/commit/8369562757)] - **doc**: clarify text about using 'session' event for compatibility (Rich Trott) [#30746](https://github.com/nodejs/node/pull/30746)
- [[`145f881ff9`](https://github.com/nodejs/node/commit/145f881ff9)] - **doc**: update status of Python 3 support (Michael Dawson) [#30722](https://github.com/nodejs/node/pull/30722)
- [[`bbbba76f2c`](https://github.com/nodejs/node/commit/bbbba76f2c)] - **doc,benchmark**: move benchmark guide to benchmark directory (Rich Trott) [#30781](https://github.com/nodejs/node/pull/30781)
- [[`eb4f443a5a`](https://github.com/nodejs/node/commit/eb4f443a5a)] - **esm**: make specifier flag clearly experimental (Myles Borins) [#30678](https://github.com/nodejs/node/pull/30678)
- [[`220a6001c6`](https://github.com/nodejs/node/commit/220a6001c6)] - **(SEMVER-MINOR)** **events**: add captureRejection option (Matteo Collina) [#27867](https://github.com/nodejs/node/pull/27867)
- [[`6c07a72833`](https://github.com/nodejs/node/commit/6c07a72833)] - **fs**: synchronize close with other I/O for streams (Anna Henningsen) [#30837](https://github.com/nodejs/node/pull/30837)
- [[`18758ef183`](https://github.com/nodejs/node/commit/18758ef183)] - **fs**: retry unlink operations in rimraf (Colin Ihrig) [#30569](https://github.com/nodejs/node/pull/30569)
- [[`5e98de1751`](https://github.com/nodejs/node/commit/5e98de1751)] - **fs**: only operate on buffers in rimraf (Colin Ihrig) [#30569](https://github.com/nodejs/node/pull/30569)
- [[`7e1dee3347`](https://github.com/nodejs/node/commit/7e1dee3347)] - **fs**: reduce unnecessary sync rimraf retries (Colin Ihrig) [#30785](https://github.com/nodejs/node/pull/30785)
- [[`5523950b47`](https://github.com/nodejs/node/commit/5523950b47)] - **fs**: add synchronous retries to rimraf (Colin Ihrig) [#30785](https://github.com/nodejs/node/pull/30785)
- [[`60b1e1ad61`](https://github.com/nodejs/node/commit/60b1e1ad61)] - **fs**: fix existsSync for invalid symlink at win32 (Rongjian Zhang) [#30556](https://github.com/nodejs/node/pull/30556)
- [[`daca0780b1`](https://github.com/nodejs/node/commit/daca0780b1)] - **(SEMVER-MINOR)** **http**: llhttp opt-in insecure HTTP header parsing (Sam Roberts) [#30567](https://github.com/nodejs/node/pull/30567)
- [[`334d4f6256`](https://github.com/nodejs/node/commit/334d4f6256)] - **(SEMVER-MINOR)** **http**: add captureRejection support to OutgoingMessage (Matteo Collina) [#27867](https://github.com/nodejs/node/pull/27867)
- [[`33a6bf3a83`](https://github.com/nodejs/node/commit/33a6bf3a83)] - **(SEMVER-MINOR)** **http**: implement capture rejections for 'request' event (Matteo Collina) [#27867](https://github.com/nodejs/node/pull/27867)
- [[`822fb00dbe`](https://github.com/nodejs/node/commit/822fb00dbe)] - **http2**: forward debug message in debugStreamObj (Denys Otrishko) [#30840](https://github.com/nodejs/node/pull/30840)
- [[`d17ea8f584`](https://github.com/nodejs/node/commit/d17ea8f584)] - **http2**: track nghttp2-allocated memory in heap snapshot (Anna Henningsen) [#30745](https://github.com/nodejs/node/pull/30745)
- [[`8a9f57d0d5`](https://github.com/nodejs/node/commit/8a9f57d0d5)] - **http2**: use shared memory tracking implementation (Anna Henningsen) [#30745](https://github.com/nodejs/node/pull/30745)
- [[`71bb026e0c`](https://github.com/nodejs/node/commit/71bb026e0c)] - **http2**: streamline OnStreamRead streamline memory accounting (Denys Otrishko) [#30351](https://github.com/nodejs/node/pull/30351)
- [[`3840abed11`](https://github.com/nodejs/node/commit/3840abed11)] - **http2**: small clean up in OnStreamRead (Denys Otrishko) [#30351](https://github.com/nodejs/node/pull/30351)
- [[`c3ac4c85a5`](https://github.com/nodejs/node/commit/c3ac4c85a5)] - **(SEMVER-MINOR)** **http2**: implement capture rection for 'request' and 'stream' events (Matteo Collina) [#27867](https://github.com/nodejs/node/pull/27867)
- [[`d3f0dd2148`](https://github.com/nodejs/node/commit/d3f0dd2148)] - **inspector**: do not access queueMicrotask from global (Michaël Zasso) [#30732](https://github.com/nodejs/node/pull/30732)
- [[`71c6d44efa`](https://github.com/nodejs/node/commit/71c6d44efa)] - **lib**: enforce use of BigInt from primordials (Michaël Zasso) [#30882](https://github.com/nodejs/node/pull/30882)
- [[`64ab5c9c84`](https://github.com/nodejs/node/commit/64ab5c9c84)] - **lib**: replace Symbol.iterator by SymbolIterator (Sebastien Ahkrin) [#30859](https://github.com/nodejs/node/pull/30859)
- [[`39898a9db4`](https://github.com/nodejs/node/commit/39898a9db4)] - **lib**: replace every Symbol.for by SymbolFor primordials (Sebastien Ahkrin) [#30857](https://github.com/nodejs/node/pull/30857)
- [[`0a34fcb086`](https://github.com/nodejs/node/commit/0a34fcb086)] - **lib**: replace var with let/const (jens-cappelle) [#30384](https://github.com/nodejs/node/pull/30384)
- [[`af014170a7`](https://github.com/nodejs/node/commit/af014170a7)] - **lib**: replace Symbol global by the primordials Symbol (Sebastien Ahkrin) [#30737](https://github.com/nodejs/node/pull/30737)
- [[`2c439bb8ad`](https://github.com/nodejs/node/commit/2c439bb8ad)] - **lib**: add parent to ERR_UNKNOWN_FILE_EXTENSION (qualitymanifest) [#30728](https://github.com/nodejs/node/pull/30728)
- [[`d9d64754f9`](https://github.com/nodejs/node/commit/d9d64754f9)] - **lib**: add warning on dynamic import es modules (Juan José Arboleda) [#30720](https://github.com/nodejs/node/pull/30720)
- [[`325128e469`](https://github.com/nodejs/node/commit/325128e469)] - **lib**: delay access to CLI option to pre-execution (Joyee Cheung) [#30778](https://github.com/nodejs/node/pull/30778)
- [[`94f237e5ac`](https://github.com/nodejs/node/commit/94f237e5ac)] - **lib,test**: improves ERR_REQUIRE_ESM message (Juan José Arboleda) [#30694](https://github.com/nodejs/node/pull/30694)
- [[`e61f4ead93`](https://github.com/nodejs/node/commit/e61f4ead93)] - **module**: conditional exports import condition (Guy Bedford) [#30799](https://github.com/nodejs/node/pull/30799)
- [[`8e16093b64`](https://github.com/nodejs/node/commit/8e16093b64)] - **module**: fix require in node repl (Yongsheng Zhang) [#30835](https://github.com/nodejs/node/pull/30835)
- [[`d4aa656d57`](https://github.com/nodejs/node/commit/d4aa656d57)] - **module**: fix dynamic import from eval (Corey Farrell) [#30624](https://github.com/nodejs/node/pull/30624)
- [[`a7ec78f34e`](https://github.com/nodejs/node/commit/a7ec78f34e)] - **module**: fixup lint and test regressions (Guy Bedford) [#30802](https://github.com/nodejs/node/pull/30802)
- [[`bd2f1270f7`](https://github.com/nodejs/node/commit/bd2f1270f7)] - **module**: ignore resolution failures for inspect-brk (Maël Nison) [#30336](https://github.com/nodejs/node/pull/30336)
- [[`851f3135ab`](https://github.com/nodejs/node/commit/851f3135ab)] - **module**: add warnings for experimental flags (Rongjian Zhang) [#30617](https://github.com/nodejs/node/pull/30617)
- [[`123327d4c1`](https://github.com/nodejs/node/commit/123327d4c1)] - **net**: remove duplicate \_undestroy (Robert Nagy) [#30833](https://github.com/nodejs/node/pull/30833)
- [[`4eecee089d`](https://github.com/nodejs/node/commit/4eecee089d)] - **(SEMVER-MINOR)** **net**: implement capture rejections for 'connection' event (Matteo Collina) [#27867](https://github.com/nodejs/node/pull/27867)
- [[`2f1ae4f2bf`](https://github.com/nodejs/node/commit/2f1ae4f2bf)] - **readline**: eagerly load string_decoder (Ruben Bridgewater) [#30807](https://github.com/nodejs/node/pull/30807)
- [[`e551c169b8`](https://github.com/nodejs/node/commit/e551c169b8)] - **(SEMVER-MINOR)** **repl**: support previews by eager evaluating input (Ruben Bridgewater) [#30811](https://github.com/nodejs/node/pull/30811)
- [[`c440f3fa3d`](https://github.com/nodejs/node/commit/c440f3fa3d)] - **repl**: use better uncaught exceptions indicator (Ruben Bridgewater) [#29676](https://github.com/nodejs/node/pull/29676)
- [[`de368200f3`](https://github.com/nodejs/node/commit/de368200f3)] - **src**: accept single argument in getProxyDetails (Ruben Bridgewater) [#30858](https://github.com/nodejs/node/pull/30858)
- [[`60886036c9`](https://github.com/nodejs/node/commit/60886036c9)] - **src**: fix the false isatty() issue on IBMi (Xu Meng) [#30829](https://github.com/nodejs/node/pull/30829)
- [[`7ed867dddb`](https://github.com/nodejs/node/commit/7ed867dddb)] - **src**: improve checked uv loop close output (Anna Henningsen) [#30814](https://github.com/nodejs/node/pull/30814)
- [[`041daaa273`](https://github.com/nodejs/node/commit/041daaa273)] - **src**: port memory-tracking allocator from QUIC repo (Anna Henningsen) [#30745](https://github.com/nodejs/node/pull/30745)
- [[`ccf0917aef`](https://github.com/nodejs/node/commit/ccf0917aef)] - **src**: don't use deprecated OpenSSL APIs (Rosen Penev) [#30812](https://github.com/nodejs/node/pull/30812)
- [[`8ad53ab2b7`](https://github.com/nodejs/node/commit/8ad53ab2b7)] - **src**: free preopen memory in WASI::New() (Colin Ihrig) [#30809](https://github.com/nodejs/node/pull/30809)
- [[`e6e379ea41`](https://github.com/nodejs/node/commit/e6e379ea41)] - **src**: use checked allocations in WASI::New() (Colin Ihrig) [#30809](https://github.com/nodejs/node/pull/30809)
- [[`838ae10a9b`](https://github.com/nodejs/node/commit/838ae10a9b)] - **src**: delete redundant method in node_dir.h (gengjiawen) [#30747](https://github.com/nodejs/node/pull/30747)
- [[`66db8746c7`](https://github.com/nodejs/node/commit/66db8746c7)] - **src**: remove redundant cast in node_dir.cc (gengjiawen) [#30747](https://github.com/nodejs/node/pull/30747)
- [[`cb69ff47f6`](https://github.com/nodejs/node/commit/cb69ff47f6)] - **src**: improve node_crypto.cc memory allocation (Priyanka Kore) [#30751](https://github.com/nodejs/node/pull/30751)
- [[`b51b26ffef`](https://github.com/nodejs/node/commit/b51b26ffef)] - **src**: fix node_dir.cc memory allocation (Priyanka Kore) [#30750](https://github.com/nodejs/node/pull/30750)
- [[`89bc571490`](https://github.com/nodejs/node/commit/89bc571490)] - **(SEMVER-MINOR)** **stream**: add support for captureRejection option (Matteo Collina) [#27867](https://github.com/nodejs/node/pull/27867)
- [[`1b534d571a`](https://github.com/nodejs/node/commit/1b534d571a)] - **test**: work around ENOTEMPTY when cleaning tmp dir (Ben Noordhuis) [#30849](https://github.com/nodejs/node/pull/30849)
- [[`eb6e32c2fc`](https://github.com/nodejs/node/commit/eb6e32c2fc)] - **test**: disable colorMode in test-console-group (Rich Trott) [#30886](https://github.com/nodejs/node/pull/30886)
- [[`5f42b1fc6b`](https://github.com/nodejs/node/commit/5f42b1fc6b)] - **test**: assert: fix deepStrictEqual comparing a real array and fake array (Jordan Harband) [#30743](https://github.com/nodejs/node/pull/30743)
- [[`ce21fc7154`](https://github.com/nodejs/node/commit/ce21fc7154)] - **test**: wait for stream close before writing to file (Anna Henningsen) [#30836](https://github.com/nodejs/node/pull/30836)
- [[`cc4a6ed645`](https://github.com/nodejs/node/commit/cc4a6ed645)] - **test**: use fs rimraf to refresh tmpdir (Colin Ihrig) [#30569](https://github.com/nodejs/node/pull/30569)
- [[`5ae3a858f7`](https://github.com/nodejs/node/commit/5ae3a858f7)] - **test**: refactor test-accessor-properties (himself65) [#29943](https://github.com/nodejs/node/pull/29943)
- [[`97e0efeedf`](https://github.com/nodejs/node/commit/97e0efeedf)] - **test**: scale keepalive timeouts for slow machines (Ben Noordhuis) [#30834](https://github.com/nodejs/node/pull/30834)
- [[`305e45a041`](https://github.com/nodejs/node/commit/305e45a041)] - **test**: mark tests as flaky (João Reis) [#30848](https://github.com/nodejs/node/pull/30848)
- [[`4dc9d8db13`](https://github.com/nodejs/node/commit/4dc9d8db13)] - **test**: mark addons/openssl-bindings/test flaky on arm (Richard Lau) [#30838](https://github.com/nodejs/node/pull/30838)
- [[`25e3696a07`](https://github.com/nodejs/node/commit/25e3696a07)] - **test**: improve WASI options validation (Rich Trott) [#30800](https://github.com/nodejs/node/pull/30800)
- [[`a574cb0ab9`](https://github.com/nodejs/node/commit/a574cb0ab9)] - **test**: remove common.busyLoop() (Colin Ihrig) [#30787](https://github.com/nodejs/node/pull/30787)
- [[`3557659afb`](https://github.com/nodejs/node/commit/3557659afb)] - **test**: run more assert tests (Ruben Bridgewater) [#30764](https://github.com/nodejs/node/pull/30764)
- [[`5067463f3c`](https://github.com/nodejs/node/commit/5067463f3c)] - **test**: use callback arguments in getconnections test (Rich Trott) [#30775](https://github.com/nodejs/node/pull/30775)
- [[`30756e36e7`](https://github.com/nodejs/node/commit/30756e36e7)] - **test**: improve wasi test coverage (Rich Trott) [#30770](https://github.com/nodejs/node/pull/30770)
- [[`fb31ab52c0`](https://github.com/nodejs/node/commit/fb31ab52c0)] - **test**: simplify tmpdir import in wasi tests (Rich Trott) [#30770](https://github.com/nodejs/node/pull/30770)
- [[`55a270b583`](https://github.com/nodejs/node/commit/55a270b583)] - **test**: remove duplicate entries from root.status (Richard Lau) [#30769](https://github.com/nodejs/node/pull/30769)
- [[`54a266c878`](https://github.com/nodejs/node/commit/54a266c878)] - **test**: increase debugging information in subprocess test (Rich Trott) [#30761](https://github.com/nodejs/node/pull/30761)
- [[`a0fa327365`](https://github.com/nodejs/node/commit/a0fa327365)] - **test**: use block-scoping in test-net-server-address (Rich Trott) [#30754](https://github.com/nodejs/node/pull/30754)
- [[`9bd5c72104`](https://github.com/nodejs/node/commit/9bd5c72104)] - **test**: move test-child-process-fork-getconnections to parallel (Rich Trott) [#30749](https://github.com/nodejs/node/pull/30749)
- [[`50ab1fa013`](https://github.com/nodejs/node/commit/50ab1fa013)] - **test**: change common.PORT to arbitrary port (Rich Trott) [#30749](https://github.com/nodejs/node/pull/30749)
- [[`255cd7e572`](https://github.com/nodejs/node/commit/255cd7e572)] - **(SEMVER-MINOR)** **tls**: expose IETF name for current cipher suite (Sam Roberts) [#30637](https://github.com/nodejs/node/pull/30637)
- [[`5ad3efbfb3`](https://github.com/nodejs/node/commit/5ad3efbfb3)] - **(SEMVER-MINOR)** **tls**: implement capture rejections for 'secureConnection' event (Matteo Collina) [#27867](https://github.com/nodejs/node/pull/27867)
- [[`5203ffb2f4`](https://github.com/nodejs/node/commit/5203ffb2f4)] - **tools**: update link to google styleguide for cpplint (Daniel Bevenius) [#30876](https://github.com/nodejs/node/pull/30876)
- [[`1ed1a645f2`](https://github.com/nodejs/node/commit/1ed1a645f2)] - **tools**: use CC instead of CXX when pointing to gcc (Milad Farazmand) [#30817](https://github.com/nodejs/node/pull/30817)
- [[`2b687af852`](https://github.com/nodejs/node/commit/2b687af852)] - **tools**: update remark-preset-lint-node to 1.11.0 (Rich Trott) [#30789](https://github.com/nodejs/node/pull/30789)
- [[`0cb7720dd8`](https://github.com/nodejs/node/commit/0cb7720dd8)] - **tools**: update icu to 65.1 (Albert Wang) [#30232](https://github.com/nodejs/node/pull/30232)
- [[`7b9400ce63`](https://github.com/nodejs/node/commit/7b9400ce63)] - **tools**: update ESLint to 6.7.2 (Rich Trott) [#30762](https://github.com/nodejs/node/pull/30762)
- [[`5ab3ca4f96`](https://github.com/nodejs/node/commit/5ab3ca4f96)] - **url**: declare iterator inside loop (Trivikram Kamat) [#30509](https://github.com/nodejs/node/pull/30509)
- [[`dc69cbeb05`](https://github.com/nodejs/node/commit/dc69cbeb05)] - **util**: add internal sleep() function (Colin Ihrig) [#30787](https://github.com/nodejs/node/pull/30787)
- [[`3898b2387b`](https://github.com/nodejs/node/commit/3898b2387b)] - **util**: never trigger any proxy traps using `format()` (Ruben Bridgewater) [#30767](https://github.com/nodejs/node/pull/30767)
- [[`eeaeb51dcc`](https://github.com/nodejs/node/commit/eeaeb51dcc)] - **util**: improve performance inspecting proxies (Ruben Bridgewater) [#30767](https://github.com/nodejs/node/pull/30767)
- [[`608d720834`](https://github.com/nodejs/node/commit/608d720834)] - **(SEMVER-MINOR)** **util**: add more predefined color codes to inspect.colors (Ruben Bridgewater) [#30659](https://github.com/nodejs/node/pull/30659)
- [[`77ffd5482d`](https://github.com/nodejs/node/commit/77ffd5482d)] - **(SEMVER-MINOR)** **util**: improve inspect's customInspect performance (Ruben Bridgewater) [#30659](https://github.com/nodejs/node/pull/30659)
- [[`14269d15cf`](https://github.com/nodejs/node/commit/14269d15cf)] - **wasi**: use memory-tracking allocator (Anna Henningsen) [#30745](https://github.com/nodejs/node/pull/30745)
- [[`71d43a5569`](https://github.com/nodejs/node/commit/71d43a5569)] - **(SEMVER-MINOR)** **worker**: add argv constructor option (legendecas) [#30559](https://github.com/nodejs/node/pull/30559)

Windows 32-bit Installer: https://nodejs.org/dist/v13.4.0/node-v13.4.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v13.4.0/node-v13.4.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v13.4.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v13.4.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v13.4.0/node-v13.4.0.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v13.4.0/node-v13.4.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v13.4.0/node-v13.4.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v13.4.0/node-v13.4.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v13.4.0/node-v13.4.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v13.4.0/node-v13.4.0-aix-ppc64.tar.gz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v13.4.0/node-v13.4.0-sunos-x64.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v13.4.0/node-v13.4.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v13.4.0/node-v13.4.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v13.4.0/node-v13.4.0.tar.gz \
Other release files: https://nodejs.org/dist/v13.4.0/ \
Documentation: https://nodejs.org/docs/v13.4.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

149a3aa1c87ed82305f15fe0a0785aab61792e2b3a50e828efcc515b7c168173  node-v13.4.0-aix-ppc64.tar.gz
4de08a89054416595228d6ff40fcf20c375d00556f2e95dfde8602cbb42c0b6e  node-v13.4.0-darwin-x64.tar.gz
78bc637a3d0ed65def0ff3cfe895f0daf4c1b1589c8c15f12a03368b6fab3b00  node-v13.4.0-darwin-x64.tar.xz
a289c37204db4410d6756818a54d759261a0c2e763db2d838c0f55e5550157ed  node-v13.4.0-headers.tar.gz
0aa0eec357ea2bb771fb958c7a7f3eec834c3546585fcd38cdc6ef0480daf89e  node-v13.4.0-headers.tar.xz
bd0c4511126bafeadf46079c3bd3c9e143859a509d3d5e0ac119342391ff93ae  node-v13.4.0-linux-arm64.tar.gz
382d5fb2983ab55d495e42145e812b3859d2a311e426307fd373f47825d55fe9  node-v13.4.0-linux-arm64.tar.xz
0aaaed14375f26484f09a351ea50342b8c22f4ce8772c4b0a3d6e002aa2b5932  node-v13.4.0-linux-armv7l.tar.gz
39c291f96edb49f681da078b2d72bcd7d63e9ab3787c7fd498b012eb53d1ce04  node-v13.4.0-linux-armv7l.tar.xz
fc58a6fae0f7f0dffb5e73716f1f218ef0636f02a92924813126dda2059c78d9  node-v13.4.0-linux-ppc64le.tar.gz
ed4c2e15c832934a8e0ab2d36b8a808c134f4774d96e5ce7141b514a1020a920  node-v13.4.0-linux-ppc64le.tar.xz
41588453f586c532a4ba413b905d74537ca73d635b7a54c679b482080e9f6fa4  node-v13.4.0-linux-s390x.tar.gz
6696f9fa902a21a9193fa6216da811449c2aa76e5e125709bd1350fbd6ef4744  node-v13.4.0-linux-s390x.tar.xz
63411f61d4156b1f3ee6f088b855a1cebea3ab32a0cabc28419f8b6cc3ffa161  node-v13.4.0-linux-x64.tar.gz
5cab49240c521efb80efd0a0bb3ba1071e5498c55ecbebd723d78648c91b1cc8  node-v13.4.0-linux-x64.tar.xz
d9e196a28647f6548afae6c29d7a98d0a7aa3fbd41c92fd63bceb356242f2ec4  node-v13.4.0.pkg
58d7190b4d7809288e308b24639caed7cf20852fb8b6edbfadd5d2cee8e7fe6b  node-v13.4.0-sunos-x64.tar.gz
d334d9f00ca5e4e031a1aa804578df5ab6f2ee54f5c4f6ea1ba3f9d54aff7aff  node-v13.4.0-sunos-x64.tar.xz
039e9bfbb114fca44b14f317cdadf51fc875660e4e03106fa303503add7ae560  node-v13.4.0.tar.gz
ab0f1172d702cfea5e279e5a077f050b7ea839fb377cbac7b516bc44645acb9d  node-v13.4.0.tar.xz
285d3144ceb7fdf2a054c3868621868c45214f14291afe641f9ec76b1522344b  node-v13.4.0-win-x64.7z
56de9ed20332cdf22bb9b048c3fb3977662ef2de9d2e8ac2d2a27d28e9be276f  node-v13.4.0-win-x64.zip
2a6c6b60afc74cb1c3d2bfa522f357c3dcc0295bb6c4d07d190016955964d232  node-v13.4.0-win-x86.7z
c3faa1b8f6b6e0437c675ccd5bddf513c134006fb1d3f8e1f6d9b12b8f63dcaa  node-v13.4.0-win-x86.zip
ef85286d336fe90599bc990b8b769972872975ca43314a777d8049e66c8e7879  node-v13.4.0-x64.msi
6f1036be8f56626f90ac3f273c033f811f2cf792978c23c724db22da69c6c083  node-v13.4.0-x86.msi
4defd090a177ea27675b1127b6da27cac440affaa754e57e6834ae722e4d36d4  win-x64/node.exe
ba8f5016e1a965239d8ab055b646857de988cebcb0ec455dcd7d005aca422425  win-x64/node.lib
386737c20a0e29b47a53b516b21253e51aa49ac52bdb9d79b0086a459e5e3c1d  win-x64/node_pdb.7z
f28c41bf767c3ae280d7913770cae9490e12087d6d64d060d37612debb00c159  win-x64/node_pdb.zip
417f64dd933d063914f4553b554af4719272e2485ac6db450c90b4436e0d0312  win-x86/node.exe
5085f60ea641564c8bf22adc794fa30e7bb216b69551f1df5e6d200f3ad90160  win-x86/node.lib
e56311424c90225d1caed73a8879a68fbdc39a14097f9e0a403558e952097af5  win-x86/node_pdb.7z
5ce4b71d923e60875a5f0670c6100ebb5a0e0165edc556ec955feeb110c2e4d8  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQEzBAEBCAAdFiEEDv/hvO/ZyE49CYFSkzsB9AtcqUYFAl35WDEACgkQkzsB9Atc
qUZg9Qf/ZMW1Q0n27qxaiVhh1Vpq6thhzYcgjzvKwSEQ6o6y6XRUMhq/rvN4eAL1
5LRSwV31p06UBnzR6iiAPnI6U3ScF3zuAkprToQpTZxpTwA8YyvsuaJsba0HNAD9
da3inIBE/mLCxIDnGuEtONXaAHEWNGzDjxh7aRuLzLEjiySNdYCbfL9rdVMtaxvx
GWEAj3LLPIQH4G+LF5sGX6XN2CC6UVIqUKNqV4gOBh7+6CXaTf4nKFWDBOmj67eh
w43imIEPiB2xLPaa163zSGGq+P/bBglrjfoBd4AWOMQC0hiJLT6G2U+Uu/asRgVJ
nkTgbLs/0t+Aq8F9D0iUifEOdJlHow==
=MoF/
-----END PGP SIGNATURE-----

```
