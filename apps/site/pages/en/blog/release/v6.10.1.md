---
date: '2017-03-21T21:32:30.981Z'
category: release
title: Node v6.10.1 (LTS)
layout: blog-post
author: <PERSON><PERSON>
---

### Notable changes

- **performance**: The performance of several APIs has been improved.
  - `Buffer.compare()` is up to 35% faster on average. (<PERSON>) [#10927](https://github.com/nodejs/node/pull/10927)
  - `buffer.toJSON()` is up to 2859% faster on average. (<PERSON>) [#10895](https://github.com/nodejs/node/pull/10895)
  - `fs.*statSync()` functions are now up to 9.3% faster on average. (<PERSON>) [#11522](https://github.com/nodejs/node/pull/11522)
  - `os.loadavg` is up to 151% faster. (<PERSON>) [#11516](https://github.com/nodejs/node/pull/11516)
  - `process.memoryUsage()` is up to 34% faster. (<PERSON>) [#11497](https://github.com/nodejs/node/pull/11497)
  - `querystring.unescape()` for `Buffer`s is 15% faster on average. (<PERSON>) [#10837](https://github.com/nodejs/node/pull/10837)
  - `querystring.stringify()` is up to 7.8% faster on average. (Brian White) [#10852](https://github.com/nodejs/node/pull/10852)
  - `querystring.parse()` is up to 21% faster on average. (Brian White) [#10874](https://github.com/nodejs/node/pull/10874)
- **IPC**: Batched writes have been enabled for process IPC on platforms that support Unix Domain Sockets. (Alexey Orlenko) [#10677](https://github.com/nodejs/node/pull/10677)
  - Performance gains may be up to 40% for some workloads.
- **child_process**: `spawnSync` now returns a null `status` when child is terminated by a signal. (cjihrig) [#11288](https://github.com/nodejs/node/pull/11288)
  - This fixes the behavior to act like `spawn()` does.
- **http**:
  - Control characters are now always rejected when using `http.request()`. (Ben Noordhuis) [#8923](https://github.com/nodejs/node/pull/8923)
  - Debug messages have been added for cases when headers contain invalid values. (Evan Lucas) [#9195](https://github.com/nodejs/node/pull/9195)
- **node**: Heap statistics now support values larger than 4GB. (Ben Noordhuis) [#10186](https://github.com/nodejs/node/pull/10186)
- **timers**: Timer callbacks now always maintain order when interacting with domain error handling. (John Barboza) [#10522](https://github.com/nodejs/node/pull/10522)

### Commits

- [[`fb75bed078`](https://github.com/nodejs/node/commit/fb75bed078)] - **assert**: unlock the assert API (Rich Trott) [#11304](https://github.com/nodejs/node/pull/11304)
- [[`32b264c33b`](https://github.com/nodejs/node/commit/32b264c33b)] - **assert**: remove unneeded condition (Rich Trott) [#11314](https://github.com/nodejs/node/pull/11314)
- [[`a0c705ef79`](https://github.com/nodejs/node/commit/a0c705ef79)] - **assert**: apply minor refactoring (Rich Trott) [#11511](https://github.com/nodejs/node/pull/11511)
- [[`7ecfe4971a`](https://github.com/nodejs/node/commit/7ecfe4971a)] - **assert**: update comments (Kai Cataldo) [#10579](https://github.com/nodejs/node/pull/10579)
- [[`4d6fa8d040`](https://github.com/nodejs/node/commit/4d6fa8d040)] - **benchmark**: add more thorough timers benchmarks (Jeremiah Senkpiel) [#10925](https://github.com/nodejs/node/pull/10925)
- [[`406e623b13`](https://github.com/nodejs/node/commit/406e623b13)] - **benchmark**: add benchmark for object properties (Michaël Zasso) [#10949](https://github.com/nodejs/node/pull/10949)
- [[`7ee04c6015`](https://github.com/nodejs/node/commit/7ee04c6015)] - **benchmark**: don't lint autogenerated modules (Brian White) [#10756](https://github.com/nodejs/node/pull/10756)
- [[`d22d7cce7c`](https://github.com/nodejs/node/commit/d22d7cce7c)] - **benchmark**: move punycode benchmark out of net (Brian White) [#10446](https://github.com/nodejs/node/pull/10446)
- [[`6b361611c3`](https://github.com/nodejs/node/commit/6b361611c3)] - **benchmark**: move setImmediate benchmarks to timers (Joshua Colvin) [#11010](https://github.com/nodejs/node/pull/11010)
- [[`a469ce5826`](https://github.com/nodejs/node/commit/a469ce5826)] - **benchmark**: add assert.deep\[Strict\]Equal benchmarks (Joyee Cheung) [#11092](https://github.com/nodejs/node/pull/11092)
- [[`eca1e80722`](https://github.com/nodejs/node/commit/eca1e80722)] - **benchmark**: add dgram bind(+/- params) benchmark (Vse Mozhet Byt) [#11313](https://github.com/nodejs/node/pull/11313)
- [[`06c339dcce`](https://github.com/nodejs/node/commit/06c339dcce)] - **benchmark**: improve readability of net benchmarks (Brian White) [#10446](https://github.com/nodejs/node/pull/10446)
- [[`b4cf8c4036`](https://github.com/nodejs/node/commit/b4cf8c4036)] - **benchmark,lib,test**: adjust for linting (Rich Trott) [#10561](https://github.com/nodejs/node/pull/10561)
- [[`e397e6f94a`](https://github.com/nodejs/node/commit/e397e6f94a)] - **buffer**: improve compare() performance (Brian White) [#10927](https://github.com/nodejs/node/pull/10927)
- [[`2b52859535`](https://github.com/nodejs/node/commit/2b52859535)] - **buffer**: fix comments in bidirectionalIndexOf (<EMAIL>) [#10162](https://github.com/nodejs/node/pull/10162)
- [[`f7879d98f8`](https://github.com/nodejs/node/commit/f7879d98f8)] - **buffer**: improve toJSON() performance (Brian White) [#10895](https://github.com/nodejs/node/pull/10895)
- [[`f83d035c50`](https://github.com/nodejs/node/commit/f83d035c50)] - **buffer**: convert offset & length to int properly (Sakthipriyan Vairamani (thefourtheye)) [#11176](https://github.com/nodejs/node/pull/11176)
- [[`cda593774f`](https://github.com/nodejs/node/commit/cda593774f)] - **build**: sort sources alphabetically (Daniel Bevenius) [#10892](https://github.com/nodejs/node/pull/10892)
- [[`2d31fd8bf7`](https://github.com/nodejs/node/commit/2d31fd8bf7)] - **build**: move source files from headers section (Daniel Bevenius) [#10850](https://github.com/nodejs/node/pull/10850)
- [[`b7c5295437`](https://github.com/nodejs/node/commit/b7c5295437)] - **build**: don't squash signal handlers with --shared (Stewart X Addison) [#10539](https://github.com/nodejs/node/pull/10539)
- [[`6772b1d81c`](https://github.com/nodejs/node/commit/6772b1d81c)] - **build**: disable C4267 conversion compiler warning (Ben Noordhuis) [#11205](https://github.com/nodejs/node/pull/11205)
- [[`93416e9b7a`](https://github.com/nodejs/node/commit/93416e9b7a)] - **build**: fix newlines in addon build output (Brian White) [#11466](https://github.com/nodejs/node/pull/11466)
- [[`2d5cb3b870`](https://github.com/nodejs/node/commit/2d5cb3b870)] - **build**: fail on CI if leftover processes (Rich Trott) [#11269](https://github.com/nodejs/node/pull/11269)
- [[`edcca78f10`](https://github.com/nodejs/node/commit/edcca78f10)] - **build**: add rule to clean addon tests build (Joyee Cheung) [#11519](https://github.com/nodejs/node/pull/11519)
- [[`0200a5a74e`](https://github.com/nodejs/node/commit/0200a5a74e)] - **build**: fix node_g target (Daniel Bevenius) [#10153](https://github.com/nodejs/node/pull/10153)
- [[`f44c0a5d7a`](https://github.com/nodejs/node/commit/f44c0a5d7a)] - **build**: Don't regenerate node symlink (sxa555) [#9827](https://github.com/nodejs/node/pull/9827)
- [[`947d07bd87`](https://github.com/nodejs/node/commit/947d07bd87)] - **child_process**: exit spawnSync with null on signal (cjihrig) [#11288](https://github.com/nodejs/node/pull/11288)
- [[`4179c7050f`](https://github.com/nodejs/node/commit/4179c7050f)] - **child_process**: move anonymous class to top level (Jackson Tian) [#11147](https://github.com/nodejs/node/pull/11147)
- [[`818cef848e`](https://github.com/nodejs/node/commit/818cef848e)] - **child_process**: remove empty if condition (cjihrig) [#11427](https://github.com/nodejs/node/pull/11427)
- [[`c371fdcf34`](https://github.com/nodejs/node/commit/c371fdcf34)] - **child_process**: refactor internal/child_process.js (Arseniy Maximov) [#11366](https://github.com/nodejs/node/pull/11366)
- [[`b662c117cb`](https://github.com/nodejs/node/commit/b662c117cb)] - **crypto**: return the retval of HMAC_Update (Travis Meisenheimer) [#10891](https://github.com/nodejs/node/pull/10891)
- [[`44510197dd`](https://github.com/nodejs/node/commit/44510197dd)] - **crypto**: freelist_max_len is gone in OpenSSL 1.1.0 (Adam Langley) [#10859](https://github.com/nodejs/node/pull/10859)
- [[`34614af53b`](https://github.com/nodejs/node/commit/34614af53b)] - **crypto**: add cert check issued by StartCom/WoSign (Shigeki Ohtsu) [#9469](https://github.com/nodejs/node/pull/9469)
- [[`b4b3bb4c5d`](https://github.com/nodejs/node/commit/b4b3bb4c5d)] - **crypto**: Remove expired certs from CNNIC whitelist (Shigeki Ohtsu) [#9469](https://github.com/nodejs/node/pull/9469)
- [[`1f44922e34`](https://github.com/nodejs/node/commit/1f44922e34)] - **crypto**: use CHECK_NE instead of ABORT or abort (Sam Roberts) [#10413](https://github.com/nodejs/node/pull/10413)
- [[`ccb6045f2d`](https://github.com/nodejs/node/commit/ccb6045f2d)] - **crypto,tls**: fix mutability of return values (Rich Trott) [#10795](https://github.com/nodejs/node/pull/10795)
- [[`3ab070d4e1`](https://github.com/nodejs/node/commit/3ab070d4e1)] - **deps**: backport dfb8d33 from V8 upstream (Michaël Zasso) [#11483](https://github.com/nodejs/node/pull/11483)
- [[`3fc6a2247f`](https://github.com/nodejs/node/commit/3fc6a2247f)] - **deps**: cherry-pick a814b8a from upstream V8 (<EMAIL>) [#10733](https://github.com/nodejs/node/pull/10733)
- [[`254cb1cb77`](https://github.com/nodejs/node/commit/254cb1cb77)] - **deps**: back-port 73ee7943 from v8 upstream (Ben Noordhuis) [#9293](https://github.com/nodejs/node/pull/9293)
- [[`e774de1685`](https://github.com/nodejs/node/commit/e774de1685)] - **deps**: back-port 306c412c from v8 upstream (Ben Noordhuis) [#9293](https://github.com/nodejs/node/pull/9293)
- [[`e5d1e273d7`](https://github.com/nodejs/node/commit/e5d1e273d7)] - **dgram**: fix possibly deoptimizing use of arguments (Vse Mozhet Byt) [#11242](https://github.com/nodejs/node/pull/11242)
- [[`c7257e716f`](https://github.com/nodejs/node/commit/c7257e716f)] - **dgram**: remove this aliases (cjihrig) [#11243](https://github.com/nodejs/node/pull/11243)
- [[`227cc1e810`](https://github.com/nodejs/node/commit/227cc1e810)] - **doc**: restrict the ES.Next features usage in tests (DavidCai) [#11452](https://github.com/nodejs/node/pull/11452)
- [[`23246768fb`](https://github.com/nodejs/node/commit/23246768fb)] - **doc**: add missing entry in v6 changelog table (Luigi Pinca) [#11534](https://github.com/nodejs/node/pull/11534)
- [[`ff9a86a73e`](https://github.com/nodejs/node/commit/ff9a86a73e)] - **doc**: remove Chris Dickinson from active releasers (Ben Noordhuis) [#11011](https://github.com/nodejs/node/pull/11011)
- [[`313d1a3009`](https://github.com/nodejs/node/commit/313d1a3009)] - **doc**: for style, remove "isn't" contraction (Sam Roberts) [#10981](https://github.com/nodejs/node/pull/10981)
- [[`ab7587ed6c`](https://github.com/nodejs/node/commit/ab7587ed6c)] - **doc**: update http.md for consistency and clarity (Lance Ball) [#10715](https://github.com/nodejs/node/pull/10715)
- [[`21a94ab78c`](https://github.com/nodejs/node/commit/21a94ab78c)] - **doc**: clarify Buffer.indexOf/lastIndexOf edge cases (<EMAIL>) [#10162](https://github.com/nodejs/node/pull/10162)
- [[`8c487de736`](https://github.com/nodejs/node/commit/8c487de736)] - **doc**: document argument variant in the repl.md (Vse Mozhet Byt) [#10221](https://github.com/nodejs/node/pull/10221)
- [[`130710476b`](https://github.com/nodejs/node/commit/130710476b)] - **doc**: DEFAULT_ECDH_CURVE was added in 0.11.13 (Sam Roberts) [#10983](https://github.com/nodejs/node/pull/10983)
- [[`5118e05b15`](https://github.com/nodejs/node/commit/5118e05b15)] - **doc**: HTTP response getHeader doc fix (Faiz Halde) [#10817](https://github.com/nodejs/node/pull/10817)
- [[`243652abbe`](https://github.com/nodejs/node/commit/243652abbe)] - **doc**: remove duplicate properties bullet in readme (Javis Sullivan) [#10741](https://github.com/nodejs/node/pull/10741)
- [[`fa8a394e51`](https://github.com/nodejs/node/commit/fa8a394e51)] - **doc**: specify sorted requires in tests (Sam Roberts) [#10716](https://github.com/nodejs/node/pull/10716)
- [[`1660311056`](https://github.com/nodejs/node/commit/1660311056)] - **doc**: fix typo in http.md (Peter Mescalchin) [#10975](https://github.com/nodejs/node/pull/10975)
- [[`8936814a70`](https://github.com/nodejs/node/commit/8936814a70)] - **doc**: add who to CC list for dgram (cjihrig) [#11035](https://github.com/nodejs/node/pull/11035)
- [[`b934058128`](https://github.com/nodejs/node/commit/b934058128)] - **doc**: correct and complete dgram's Socket.bind docs (Alex Jordan) [#11025](https://github.com/nodejs/node/pull/11025)
- [[`faa55fbe09`](https://github.com/nodejs/node/commit/faa55fbe09)] - **doc**: edit CONTRIBUTING.md for clarity (Rich Trott) [#11045](https://github.com/nodejs/node/pull/11045)
- [[`c26258e1fd`](https://github.com/nodejs/node/commit/c26258e1fd)] - **doc**: fix confusing example in dns.md (Vse Mozhet Byt) [#11022](https://github.com/nodejs/node/pull/11022)
- [[`8bf7f9f202`](https://github.com/nodejs/node/commit/8bf7f9f202)] - **doc**: add personal pronouns option (Rich Trott) [#11089](https://github.com/nodejs/node/pull/11089)
- [[`7c22a52a74`](https://github.com/nodejs/node/commit/7c22a52a74)] - **doc**: clarify msg when doc/api/cli.md not updated (Stewart X Addison) [#10872](https://github.com/nodejs/node/pull/10872)
- [[`d404d8b673`](https://github.com/nodejs/node/commit/d404d8b673)] - **doc**: edit stability text for clarity and style (Rich Trott) [#11112](https://github.com/nodejs/node/pull/11112)
- [[`38938e1ba9`](https://github.com/nodejs/node/commit/38938e1ba9)] - **doc**: remove assertions about assert (Rich Trott) [#11113](https://github.com/nodejs/node/pull/11113)
- [[`89d30908f2`](https://github.com/nodejs/node/commit/89d30908f2)] - **doc**: fix "initial delay" link in http.md (Timo Tijhof) [#11108](https://github.com/nodejs/node/pull/11108)
- [[`c0072f8d71`](https://github.com/nodejs/node/commit/c0072f8d71)] - **doc**: typographical fixes in COLLABORATOR_GUIDE.md (Anna Henningsen) [#11163](https://github.com/nodejs/node/pull/11163)
- [[`207142d050`](https://github.com/nodejs/node/commit/207142d050)] - **doc**: add not-an-aardvark as ESLint contact (Rich Trott) [#11169](https://github.com/nodejs/node/pull/11169)
- [[`3746eee19d`](https://github.com/nodejs/node/commit/3746eee19d)] - **doc**: improve testing guide (Joyee Cheung) [#11150](https://github.com/nodejs/node/pull/11150)
- [[`6cadc7160f`](https://github.com/nodejs/node/commit/6cadc7160f)] - **doc**: remove extraneous paragraph from assert doc (Rich Trott) [#11174](https://github.com/nodejs/node/pull/11174)
- [[`d5d8a8d7b5`](https://github.com/nodejs/node/commit/d5d8a8d7b5)] - **doc**: fix typo in dgram doc (Rich Trott) [#11186](https://github.com/nodejs/node/pull/11186)
- [[`59a1d00906`](https://github.com/nodejs/node/commit/59a1d00906)] - **doc**: add and fix System Error properties (Daiki Arai) [#10986](https://github.com/nodejs/node/pull/10986)
- [[`72adba4317`](https://github.com/nodejs/node/commit/72adba4317)] - **doc**: add links between cork() and uncork() (Matteo Collina) [#11222](https://github.com/nodejs/node/pull/11222)
- [[`1cd526c253`](https://github.com/nodejs/node/commit/1cd526c253)] - **doc**: clarify the behavior of Buffer.byteLength (Nikolai Vavilov) [#11238](https://github.com/nodejs/node/pull/11238)
- [[`b1bda165ce`](https://github.com/nodejs/node/commit/b1bda165ce)] - **doc**: edit maxBuffer/Unicode paragraph for clarity (Rich Trott) [#11228](https://github.com/nodejs/node/pull/11228)
- [[`1150af00f7`](https://github.com/nodejs/node/commit/1150af00f7)] - **doc**: improve consistency in documentation titles (Vse Mozhet Byt) [#11230](https://github.com/nodejs/node/pull/11230)
- [[`ade39cdf9c`](https://github.com/nodejs/node/commit/ade39cdf9c)] - **doc**: drop "and io.js" from release section (Ben Noordhuis) [#11054](https://github.com/nodejs/node/pull/11054)
- [[`c79d9f95d1`](https://github.com/nodejs/node/commit/c79d9f95d1)] - **doc**: update email and add personal pronoun (JungMinu) [#11318](https://github.com/nodejs/node/pull/11318)
- [[`7df4ee8d49`](https://github.com/nodejs/node/commit/7df4ee8d49)] - **doc**: update link to V8 Embedder's guide (Franziska Hinkelmann) [#11336](https://github.com/nodejs/node/pull/11336)
- [[`8468d823a8`](https://github.com/nodejs/node/commit/8468d823a8)] - **doc**: update code examples in domain.md (Vse Mozhet Byt) [#11110](https://github.com/nodejs/node/pull/11110)
- [[`10a497cdcb`](https://github.com/nodejs/node/commit/10a497cdcb)] - **doc**: describe when stdout/err is sync (Sam Roberts) [#10884](https://github.com/nodejs/node/pull/10884)
- [[`53d5002ef9`](https://github.com/nodejs/node/commit/53d5002ef9)] - **doc**: dns examples implied string args were arrays (Sam Roberts) [#11350](https://github.com/nodejs/node/pull/11350)
- [[`42304de4f7`](https://github.com/nodejs/node/commit/42304de4f7)] - **doc**: change STYLE-GUIDE to STYLE_GUIDE (Dean Coakley) [#11460](https://github.com/nodejs/node/pull/11460)
- [[`13a9ba9523`](https://github.com/nodejs/node/commit/13a9ba9523)] - **doc**: add STYLE_GUIDE (moved from nodejs/docs) (Gibson Fahnestock) [#11321](https://github.com/nodejs/node/pull/11321)
- [[`0164d9263e`](https://github.com/nodejs/node/commit/0164d9263e)] - **doc**: improve test/README.md (Joyee Cheung) [#11237](https://github.com/nodejs/node/pull/11237)
- [[`e0868aa529`](https://github.com/nodejs/node/commit/e0868aa529)] - **doc**: add comment for net.Server's error event (QianJin2013) [#11136](https://github.com/nodejs/node/pull/11136)
- [[`9a684a1511`](https://github.com/nodejs/node/commit/9a684a1511)] - **doc**: note message event listeners ref IPC channels (Diego Rodríguez Baquero) [#11494](https://github.com/nodejs/node/pull/11494)
- [[`bfa3989584`](https://github.com/nodejs/node/commit/bfa3989584)] - **doc**: argument types for assert methods (Amelia Clarke) [#11548](https://github.com/nodejs/node/pull/11548)
- [[`fc41a1d34d`](https://github.com/nodejs/node/commit/fc41a1d34d)] - **doc**: document clientRequest.aborted (Zach Bjornson) [#11544](https://github.com/nodejs/node/pull/11544)
- [[`ff77425eba`](https://github.com/nodejs/node/commit/ff77425eba)] - **doc**: link to readable and writeable stream section (Sebastian Van Sande) [#11517](https://github.com/nodejs/node/pull/11517)
- [[`4850b503dd`](https://github.com/nodejs/node/commit/4850b503dd)] - **doc**: update TheAlphaNerd to MylesBorins (Myles Borins) [#10586](https://github.com/nodejs/node/pull/10586)
- [[`d04de226a1`](https://github.com/nodejs/node/commit/d04de226a1)] - **doc**: update examples in api/crypto.md (Vse Mozhet Byt) [#10909](https://github.com/nodejs/node/pull/10909)
- [[`a045af3b95`](https://github.com/nodejs/node/commit/a045af3b95)] - **doc**: update AUTHORS list to fix name (Noah Rose Ledesma) [#10945](https://github.com/nodejs/node/pull/10945)
- [[`d266759b99`](https://github.com/nodejs/node/commit/d266759b99)] - **doc**: add TimothyGu to collaborators (Timothy Gu) [#10954](https://github.com/nodejs/node/pull/10954)
- [[`42a5989b39`](https://github.com/nodejs/node/commit/42a5989b39)] - **doc**: mention moderation repo in onboarding doc (Anna Henningsen) [#10869](https://github.com/nodejs/node/pull/10869)
- [[`cdc981f6e1`](https://github.com/nodejs/node/commit/cdc981f6e1)] - **doc**: add edsadr to collaborators (Adrian Estrada) [#10883](https://github.com/nodejs/node/pull/10883)
- [[`787d4ec197`](https://github.com/nodejs/node/commit/787d4ec197)] - **doc**: clarifying variables in fs.write() (Jessica Quynh Tran) [#9792](https://github.com/nodejs/node/pull/9792)
- [[`f48c86ce48`](https://github.com/nodejs/node/commit/f48c86ce48)] - **doc**: add links for zlib convenience methods (Anna Henningsen) [#10829](https://github.com/nodejs/node/pull/10829)
- [[`1dbb366611`](https://github.com/nodejs/node/commit/1dbb366611)] - **doc**: add missing `added:` tag for `zlib.constants` (Anna Henningsen) [#10826](https://github.com/nodejs/node/pull/10826)
- [[`867b4d87dc`](https://github.com/nodejs/node/commit/867b4d87dc)] - **doc**: fix broken internal link in process.md (Anna Henningsen) [#10828](https://github.com/nodejs/node/pull/10828)
- [[`6d726c07aa`](https://github.com/nodejs/node/commit/6d726c07aa)] - **doc**: update writable.write return value (Nathan Phillip Brink) [#10582](https://github.com/nodejs/node/pull/10582)
- [[`1975f82168`](https://github.com/nodejs/node/commit/1975f82168)] - **doc**: edit writing-tests.md (Rich Trott) [#10585](https://github.com/nodejs/node/pull/10585)
- [[`494ee5163f`](https://github.com/nodejs/node/commit/494ee5163f)] - **doc**: fix misleading language in vm docs (Alexey Orlenko) [#10708](https://github.com/nodejs/node/pull/10708)
- [[`8e807f6552`](https://github.com/nodejs/node/commit/8e807f6552)] - **doc**: mention cc-ing nodejs/url team for reviews (Anna Henningsen) [#10652](https://github.com/nodejs/node/pull/10652)
- [[`f9bd4a5645`](https://github.com/nodejs/node/commit/f9bd4a5645)] - **doc**: sort require statements in tests (Sam Roberts) [#10616](https://github.com/nodejs/node/pull/10616)
- [[`032d73841d`](https://github.com/nodejs/node/commit/032d73841d)] - **doc**: handle backpressure when write() return false (Matteo Collina) [#10631](https://github.com/nodejs/node/pull/10631)
- [[`af991c7a98`](https://github.com/nodejs/node/commit/af991c7a98)] - **doc**: add test naming information to guide (Rich Trott) [#10584](https://github.com/nodejs/node/pull/10584)
- [[`b5fd61d77a`](https://github.com/nodejs/node/commit/b5fd61d77a)] - **doc**: fix missing negation in stream.md (Johannes Rieken) [#10712](https://github.com/nodejs/node/pull/10712)
- [[`7e5a59e6fc`](https://github.com/nodejs/node/commit/7e5a59e6fc)] - **doc**: "s/git apply/git am -3" in V8 guide (Myles Borins) [#10665](https://github.com/nodejs/node/pull/10665)
- [[`789bafd693`](https://github.com/nodejs/node/commit/789bafd693)] - **doc**: update LTS info for current releases (Evan Lucas) [#10720](https://github.com/nodejs/node/pull/10720)
- [[`fef978584a`](https://github.com/nodejs/node/commit/fef978584a)] - **doc**: update BUILDING.md (Lukasz Gasior) [#10669](https://github.com/nodejs/node/pull/10669)
- [[`f2ddc72b62`](https://github.com/nodejs/node/commit/f2ddc72b62)] - **doc**: document use of Refs: for references (Gibson Fahnestock) [#10670](https://github.com/nodejs/node/pull/10670)
- [[`0a1d15fba6`](https://github.com/nodejs/node/commit/0a1d15fba6)] - **doc**: clarify information about ABI version (Rich Trott) [#10419](https://github.com/nodejs/node/pull/10419)
- [[`22f3813b3e`](https://github.com/nodejs/node/commit/22f3813b3e)] - **doc**: clarify the statement in vm.createContext() (AnnaMag) [#10519](https://github.com/nodejs/node/pull/10519)
- [[`38d63e49eb`](https://github.com/nodejs/node/commit/38d63e49eb)] - **doc**: improve rinfo object documentation (Matt Crummey) [#10050](https://github.com/nodejs/node/pull/10050)
- [[`998fd1e7e1`](https://github.com/nodejs/node/commit/998fd1e7e1)] - **doc**: add tls.DEFAULT_ECDH_CURVE (Sam Roberts) [#10264](https://github.com/nodejs/node/pull/10264)
- [[`4995a819e0`](https://github.com/nodejs/node/commit/4995a819e0)] - **doc**: fix a wrong note in the buffer.md (Vse Mozhet Byt) [#9795](https://github.com/nodejs/node/pull/9795)
- [[`6d3c2d6212`](https://github.com/nodejs/node/commit/6d3c2d6212)] - **doc**: fix examples in buffer.md to avoid confusion (Vse Mozhet Byt) [#9795](https://github.com/nodejs/node/pull/9795)
- [[`020c90eb2d`](https://github.com/nodejs/node/commit/020c90eb2d)] - **doc**: remove a wrong remark in the buffer.md (Vse Mozhet Byt) [#9795](https://github.com/nodejs/node/pull/9795)
- [[`8af811f90e`](https://github.com/nodejs/node/commit/8af811f90e)] - **doc**: fix copy-paste artifacts in the buffer.md (Vse Mozhet Byt) [#9795](https://github.com/nodejs/node/pull/9795)
- [[`a2b40ad6a4`](https://github.com/nodejs/node/commit/a2b40ad6a4)] - **doc**: fix wrong function arguments in the buffer.md (Vse Mozhet Byt) [#9795](https://github.com/nodejs/node/pull/9795)
- [[`e94abaec1c`](https://github.com/nodejs/node/commit/e94abaec1c)] - **doc**: fix a syntax error in the buffer.md (Vse Mozhet Byt) [#9795](https://github.com/nodejs/node/pull/9795)
- [[`b36c315423`](https://github.com/nodejs/node/commit/b36c315423)] - **doc**: var =\> const/let in the buffer.md (Vse Mozhet Byt) [#9795](https://github.com/nodejs/node/pull/9795)
- [[`b503824b81`](https://github.com/nodejs/node/commit/b503824b81)] - **doc,test**: args to `buffer.copy` can be Uint8Arrays (Anna Henningsen) [#11486](https://github.com/nodejs/node/pull/11486)
- [[`c8d2ca7a78`](https://github.com/nodejs/node/commit/c8d2ca7a78)] - **fs**: improve performance for sync stat() functions (Brian White) [#11522](https://github.com/nodejs/node/pull/11522)
- [[`b4dc7a778f`](https://github.com/nodejs/node/commit/b4dc7a778f)] - **http**: make request.abort() destroy the socket (Luigi Pinca) [#10818](https://github.com/nodejs/node/pull/10818)
- [[`d777da27bc`](https://github.com/nodejs/node/commit/d777da27bc)] - **http**: reject control characters in http.request() (Ben Noordhuis) [#8923](https://github.com/nodejs/node/pull/8923)
- [[`bad0d9367e`](https://github.com/nodejs/node/commit/bad0d9367e)] - **http**: add debug message for invalid header value (Evan Lucas) [#9195](https://github.com/nodejs/node/pull/9195)
- [[`bde1a7e09e`](https://github.com/nodejs/node/commit/bde1a7e09e)] - **lib**: remove unnecessary parameter for assertCrypto() (Jackson Tian) [#10834](https://github.com/nodejs/node/pull/10834)
- [[`a2aa2f7de4`](https://github.com/nodejs/node/commit/a2aa2f7de4)] - **lib**: refactor bootstrap_node.js regular expression (Rich Trott) [#10749](https://github.com/nodejs/node/pull/10749)
- [[`797d9ee924`](https://github.com/nodejs/node/commit/797d9ee924)] - **lib**: refactor crypto cipher/hash/curve getters (Rich Trott) [#10682](https://github.com/nodejs/node/pull/10682)
- [[`69327f5e72`](https://github.com/nodejs/node/commit/69327f5e72)] - **lib**: rename kMaxCallbacksUntilQueueIsShortened (JungMinu) [#11473](https://github.com/nodejs/node/pull/11473)
- [[`a6b2dfa43c`](https://github.com/nodejs/node/commit/a6b2dfa43c)] - **lib**: add constant kMaxCallbacksUntilQueueIsShortened (Daniel Bevenius) [#11199](https://github.com/nodejs/node/pull/11199)
- [[`a3ad63b9b3`](https://github.com/nodejs/node/commit/a3ad63b9b3)] - **lib,src**: support values \> 4GB in heap statistics (Ben Noordhuis) [#10186](https://github.com/nodejs/node/pull/10186)
- [[`8b5dd35ae8`](https://github.com/nodejs/node/commit/8b5dd35ae8)] - **meta**: add explicit deprecation and semver-major policy (James M Snell) [#7964](https://github.com/nodejs/node/pull/7964)
- [[`4df850ba59`](https://github.com/nodejs/node/commit/4df850ba59)] - **meta**: remove Chris Dickinson from CTC (Chris Dickinson) [#11267](https://github.com/nodejs/node/pull/11267)
- [[`8863360a21`](https://github.com/nodejs/node/commit/8863360a21)] - **meta**: adding Italo A. Casas PGP Fingerprint (Italo A. Casas) [#11202](https://github.com/nodejs/node/pull/11202)
- [[`8287d03adf`](https://github.com/nodejs/node/commit/8287d03adf)] - **meta**: decharter the http working group (James M Snell) [#10604](https://github.com/nodejs/node/pull/10604)
- [[`742ec6213f`](https://github.com/nodejs/node/commit/742ec6213f)] - **net**: prefer === to == (Arseniy Maximov) [#11513](https://github.com/nodejs/node/pull/11513)
- [[`5bfa43d8f0`](https://github.com/nodejs/node/commit/5bfa43d8f0)] - **os**: improve loadavg() performance (Brian White) [#11516](https://github.com/nodejs/node/pull/11516)
- [[`b7088a9355`](https://github.com/nodejs/node/commit/b7088a9355)] - **process**: improve memoryUsage() performance (Brian White) [#11497](https://github.com/nodejs/node/pull/11497)
- [[`02e5f5c57e`](https://github.com/nodejs/node/commit/02e5f5c57e)] - **process**: fix typo in comments (levsthings) [#11503](https://github.com/nodejs/node/pull/11503)
- [[`db45bf850a`](https://github.com/nodejs/node/commit/db45bf850a)] - **querystring**: improve unescapeBuffer performance (Brian White) [#10837](https://github.com/nodejs/node/pull/10837)
- [[`32cdbca2dc`](https://github.com/nodejs/node/commit/32cdbca2dc)] - **querystring**: improve stringify() performance (Brian White) [#10852](https://github.com/nodejs/node/pull/10852)
- [[`23f3f20963`](https://github.com/nodejs/node/commit/23f3f20963)] - **querystring**: improve parse() performance (Brian White) [#10874](https://github.com/nodejs/node/pull/10874)
- [[`dc88b6572d`](https://github.com/nodejs/node/commit/dc88b6572d)] - **readline**: refactor construct Interface (Jackson Tian) [#4740](https://github.com/nodejs/node/pull/4740)
- [[`f7c6ad2df9`](https://github.com/nodejs/node/commit/f7c6ad2df9)] - **readline**: update 6 comparions to strict (Umair Ishaq) [#11078](https://github.com/nodejs/node/pull/11078)
- [[`b5a0d46c55`](https://github.com/nodejs/node/commit/b5a0d46c55)] - **src**: add NODE_NO_WARNINGS to --help output (cjihrig) [#10918](https://github.com/nodejs/node/pull/10918)
- [[`566e2fea48`](https://github.com/nodejs/node/commit/566e2fea48)] - **src**: remove unnecessary req_wrap_obj (Daniel Bevenius) [#10942](https://github.com/nodejs/node/pull/10942)
- [[`c7436df889`](https://github.com/nodejs/node/commit/c7436df889)] - **src**: add a missing space in node_os.cc (Alexey Orlenko) [#10931](https://github.com/nodejs/node/pull/10931)
- [[`4358c6096c`](https://github.com/nodejs/node/commit/4358c6096c)] - **src**: enable writev for pipe handles on Unix (Alexey Orlenko) [#10677](https://github.com/nodejs/node/pull/10677)
- [[`28102edbc8`](https://github.com/nodejs/node/commit/28102edbc8)] - **src**: unconsume stream fix in internal http impl (Roee Kasher) [#11015](https://github.com/nodejs/node/pull/11015)
- [[`587857e301`](https://github.com/nodejs/node/commit/587857e301)] - **src**: fix delete operator on vm context (Franziska Hinkelmann) [#11266](https://github.com/nodejs/node/pull/11266)
- [[`b7cbb8002c`](https://github.com/nodejs/node/commit/b7cbb8002c)] - **src**: support UTF-8 in compiled-in JS source files (Ben Noordhuis) [#11129](https://github.com/nodejs/node/pull/11129)
- [[`ce01372b68`](https://github.com/nodejs/node/commit/ce01372b68)] - **src**: remove unused typedef (Ben Noordhuis) [#11322](https://github.com/nodejs/node/pull/11322)
- [[`1dddfeccb2`](https://github.com/nodejs/node/commit/1dddfeccb2)] - **src**: remove usage of deprecated debug API (Yang Guo) [#11437](https://github.com/nodejs/node/pull/11437)
- [[`7f273c6f6e`](https://github.com/nodejs/node/commit/7f273c6f6e)] - **src**: update http-parser link (Daniel Bevenius) [#11477](https://github.com/nodejs/node/pull/11477)
- [[`214b514efe`](https://github.com/nodejs/node/commit/214b514efe)] - **src**: use ABORT() macro instead of abort() (Evan Lucas) [#9613](https://github.com/nodejs/node/pull/9613)
- [[`412f380903`](https://github.com/nodejs/node/commit/412f380903)] - **stream**: move legacy to lib/internal dir (yorkie) [#8197](https://github.com/nodejs/node/pull/8197)
- [[`336f1bd842`](https://github.com/nodejs/node/commit/336f1bd842)] - **test**: increase setMulticastLoopback() coverage (cjihrig) [#11277](https://github.com/nodejs/node/pull/11277)
- [[`b29165f249`](https://github.com/nodejs/node/commit/b29165f249)] - **test**: increase dgram ref()/unref() coverage (cjihrig) [#11240](https://github.com/nodejs/node/pull/11240)
- [[`22d4ed2484`](https://github.com/nodejs/node/commit/22d4ed2484)] - **test**: add an exception test to http-write-head (Yuta Hiroto) [#11034](https://github.com/nodejs/node/pull/11034)
- [[`9edd342e81`](https://github.com/nodejs/node/commit/9edd342e81)] - **test**: add known_issues test for #10223 (AnnaMag) [#11024](https://github.com/nodejs/node/pull/11024)
- [[`646f82520c`](https://github.com/nodejs/node/commit/646f82520c)] - **test**: guarantee test runs in test-readline-keys (Rich Trott) [#11023](https://github.com/nodejs/node/pull/11023)
- [[`d8eed12d31`](https://github.com/nodejs/node/commit/d8eed12d31)] - **test**: check error message in test-http-outgoing-proto (Alex Ling) [#10943](https://github.com/nodejs/node/pull/10943)
- [[`174bef182a`](https://github.com/nodejs/node/commit/174bef182a)] - **test**: increase coverage for stream's duplex (abouthiroppy) [#10963](https://github.com/nodejs/node/pull/10963)
- [[`8ff15a262d`](https://github.com/nodejs/node/commit/8ff15a262d)] - **test**: allow for slow hosts in spawnSync() test (Rich Trott) [#10998](https://github.com/nodejs/node/pull/10998)
- [[`62f6749cd6`](https://github.com/nodejs/node/commit/62f6749cd6)] - **test**: expand test coverage of fs.js (Vinícius do Carmo) [#10947](https://github.com/nodejs/node/pull/10947)
- [[`5cea2239d8`](https://github.com/nodejs/node/commit/5cea2239d8)] - **test**: expand test coverage of events.js (Vinícius do Carmo) [#10947](https://github.com/nodejs/node/pull/10947)
- [[`a1751864e2`](https://github.com/nodejs/node/commit/a1751864e2)] - **test**: check noAssert option in buf.write\*() (larissayvette) [#10790](https://github.com/nodejs/node/pull/10790)
- [[`0b5f2b45f9`](https://github.com/nodejs/node/commit/0b5f2b45f9)] - **test**: expand test coverage of fs.js (Vinícius do Carmo) [#10972](https://github.com/nodejs/node/pull/10972)
- [[`d9362efb6c`](https://github.com/nodejs/node/commit/d9362efb6c)] - **test**: enhance test-timers (Rich Trott) [#10960](https://github.com/nodejs/node/pull/10960)
- [[`b9615b3abc`](https://github.com/nodejs/node/commit/b9615b3abc)] - **test**: increase coverage for exec() functions (cjihrig) [#10919](https://github.com/nodejs/node/pull/10919)
- [[`b45280671a`](https://github.com/nodejs/node/commit/b45280671a)] - **test**: add process.assert's test (abouthiroppy) [#10911](https://github.com/nodejs/node/pull/10911)
- [[`6584ea0715`](https://github.com/nodejs/node/commit/6584ea0715)] - **test**: update Buffer.lastIndexOf (<EMAIL>) [#10162](https://github.com/nodejs/node/pull/10162)
- [[`0c60540014`](https://github.com/nodejs/node/commit/0c60540014)] - **test**: improve code in test-crypto-verify (Adrian Estrada) [#10845](https://github.com/nodejs/node/pull/10845)
- [[`2a52a68a96`](https://github.com/nodejs/node/commit/2a52a68a96)] - **test**: add dgram.Socket.prototype.bind's test (abouthiroppy) [#10894](https://github.com/nodejs/node/pull/10894)
- [[`2494d8ac68`](https://github.com/nodejs/node/commit/2494d8ac68)] - **test**: update V8 flag in test (Franziska Hinkelmann) [#10917](https://github.com/nodejs/node/pull/10917)
- [[`9ac22cdcaf`](https://github.com/nodejs/node/commit/9ac22cdcaf)] - **test**: increase coverage of string-decoder (abouthiroppy) [#10863](https://github.com/nodejs/node/pull/10863)
- [[`d766f5e0ad`](https://github.com/nodejs/node/commit/d766f5e0ad)] - **test**: improving coverage of dns-lookup (abouthiroppy) [#10844](https://github.com/nodejs/node/pull/10844)
- [[`8f984c3a8a`](https://github.com/nodejs/node/commit/8f984c3a8a)] - **test**: refactor test-fs-read-zero-length.js (abouthiroppy) [#10729](https://github.com/nodejs/node/pull/10729)
- [[`c0e24f9029`](https://github.com/nodejs/node/commit/c0e24f9029)] - **test**: improving coverage for dgram (abouthiroppy) [#10783](https://github.com/nodejs/node/pull/10783)
- [[`c91d873115`](https://github.com/nodejs/node/commit/c91d873115)] - **test**: improve code in test-console-instance (Adrian Estrada) [#10813](https://github.com/nodejs/node/pull/10813)
- [[`a434f451d9`](https://github.com/nodejs/node/commit/a434f451d9)] - **test**: improve code in test-domain-multi (Adrian Estrada) [#10798](https://github.com/nodejs/node/pull/10798)
- [[`b01db3a73f`](https://github.com/nodejs/node/commit/b01db3a73f)] - **test**: improve test-stream2-large-read-stall (stefan judis) [#10725](https://github.com/nodejs/node/pull/10725)
- [[`76f0556c4a`](https://github.com/nodejs/node/commit/76f0556c4a)] - **test**: improve code in test-http-host-headers (Adrian Estrada) [#10830](https://github.com/nodejs/node/pull/10830)
- [[`c740cb6667`](https://github.com/nodejs/node/commit/c740cb6667)] - **test**: add test case to test-http-response-statuscode.js (abouthiroppy) [#10808](https://github.com/nodejs/node/pull/10808)
- [[`872354563c`](https://github.com/nodejs/node/commit/872354563c)] - **test**: refactor cluster-preload.js (abouthiroppy) [#10701](https://github.com/nodejs/node/pull/10701)
- [[`04dc1cdfcb`](https://github.com/nodejs/node/commit/04dc1cdfcb)] - **test**: improve test-fs-write-file-sync (Adrian Estrada) [#10624](https://github.com/nodejs/node/pull/10624)
- [[`0d25d056a4`](https://github.com/nodejs/node/commit/0d25d056a4)] - **test**: test hmac binding robustness (Sam Roberts) [#10923](https://github.com/nodejs/node/pull/10923)
- [[`99a234c97e`](https://github.com/nodejs/node/commit/99a234c97e)] - **test**: refactor the code in test-fs-watch.js (sivaprasanna) [#10357](https://github.com/nodejs/node/pull/10357)
- [[`c13f01c94d`](https://github.com/nodejs/node/commit/c13f01c94d)] - **test**: reduce unmanaged parallelism in domain test (Joyee Cheung) [#10329](https://github.com/nodejs/node/pull/10329)
- [[`ed76b4a8e9`](https://github.com/nodejs/node/commit/ed76b4a8e9)] - **test**: add dgram.Socket.prototype.sendto's test (abouthiroppy) [#10901](https://github.com/nodejs/node/pull/10901)
- [[`5365501a2f`](https://github.com/nodejs/node/commit/5365501a2f)] - **test**: add regression test for V8 parse error (Michaël Zasso) [#11483](https://github.com/nodejs/node/pull/11483)
- [[`b5fb9f4098`](https://github.com/nodejs/node/commit/b5fb9f4098)] - **test**: increase timeout in break-on-uncaught (Sakthipriyan Vairamani (thefourtheye)) [#10822](https://github.com/nodejs/node/pull/10822)
- [[`443dd508d2`](https://github.com/nodejs/node/commit/443dd508d2)] - **test**: fix process.title expectation (Sakthipriyan Vairamani (thefourtheye)) [#10597](https://github.com/nodejs/node/pull/10597)
- [[`ae338daf06`](https://github.com/nodejs/node/commit/ae338daf06)] - **test**: refactor test-debugger-remote (Sakthipriyan Vairamani (thefourtheye)) [#10455](https://github.com/nodejs/node/pull/10455)
- [[`34e0bc6d16`](https://github.com/nodejs/node/commit/34e0bc6d16)] - **test**: fix and improve debugger-client test (Sakthipriyan Vairamani (thefourtheye)) [#10371](https://github.com/nodejs/node/pull/10371)
- [[`da874590a6`](https://github.com/nodejs/node/commit/da874590a6)] - **test**: improve test-assert (richnologies) [#10916](https://github.com/nodejs/node/pull/10916)
- [[`a15ecd269d`](https://github.com/nodejs/node/commit/a15ecd269d)] - **test**: increase coverage for punycode's decode (abouthiroppy) [#10940](https://github.com/nodejs/node/pull/10940)
- [[`98e32db207`](https://github.com/nodejs/node/commit/98e32db207)] - **test**: check fd 0,1,2 are used, not access mode (John Barboza) [#10339](https://github.com/nodejs/node/pull/10339)
- [[`e59697c695`](https://github.com/nodejs/node/commit/e59697c695)] - **test**: fix flaky test-regress-GH-897 (Rich Trott) [#10903](https://github.com/nodejs/node/pull/10903)
- [[`a08c7f6d87`](https://github.com/nodejs/node/commit/a08c7f6d87)] - **test**: don't connect to :: (use localhost instead) (Gibson Fahnestock) [#10854](https://github.com/nodejs/node/pull/10854)
- [[`ca53866333`](https://github.com/nodejs/node/commit/ca53866333)] - **test**: add message verification on assert.throws (Travis Meisenheimer) [#10890](https://github.com/nodejs/node/pull/10890)
- [[`38b123c918`](https://github.com/nodejs/node/commit/38b123c918)] - **test**: refactor test-repl-tab-complete (Rich Trott) [#10879](https://github.com/nodejs/node/pull/10879)
- [[`68fc4d3a1c`](https://github.com/nodejs/node/commit/68fc4d3a1c)] - **test**: simplify array initialization (Rich Trott) [#10860](https://github.com/nodejs/node/pull/10860)
- [[`a26d752e77`](https://github.com/nodejs/node/commit/a26d752e77)] - **test**: add http-common's test (abouthiroppy) [#10832](https://github.com/nodejs/node/pull/10832)
- [[`80e2ff9bff`](https://github.com/nodejs/node/commit/80e2ff9bff)] - **test**: tests for \_readableStream.awaitDrain (Mark) [#8914](https://github.com/nodejs/node/pull/8914)
- [[`e4e9f675d2`](https://github.com/nodejs/node/commit/e4e9f675d2)] - **test**: improve the code in test-process-cpuUsage (Adrian Estrada) [#10714](https://github.com/nodejs/node/pull/10714)
- [[`73c0c46cf2`](https://github.com/nodejs/node/commit/73c0c46cf2)] - **test**: increase test-crypto.js strictness (Rich Trott) [#10784](https://github.com/nodejs/node/pull/10784)
- [[`e316fafbd4`](https://github.com/nodejs/node/commit/e316fafbd4)] - **test**: delete duplicate test of noAssert in readUInt\* (larissayvette) [#10791](https://github.com/nodejs/node/pull/10791)
- [[`896fb63173`](https://github.com/nodejs/node/commit/896fb63173)] - **test**: add http_incoming's matchKnownFields test (abouthiroppy) [#10811](https://github.com/nodejs/node/pull/10811)
- [[`c086bdc2de`](https://github.com/nodejs/node/commit/c086bdc2de)] - **test**: check error msg test-writeint.js (Irene Li) [#10755](https://github.com/nodejs/node/pull/10755)
- [[`2eb0c25aa1`](https://github.com/nodejs/node/commit/2eb0c25aa1)] - **test**: no unused args test-fs-watch-file.js (istinson) [#10758](https://github.com/nodejs/node/pull/10758)
- [[`2f026f6668`](https://github.com/nodejs/node/commit/2f026f6668)] - **test**: improve tests in pummel/test-exec (Chase Starr) [#10757](https://github.com/nodejs/node/pull/10757)
- [[`93877c87cc`](https://github.com/nodejs/node/commit/93877c87cc)] - **test**: fix temp-dir option in tools/test.py (Gibson Fahnestock) [#10723](https://github.com/nodejs/node/pull/10723)
- [[`0f3677dd5d`](https://github.com/nodejs/node/commit/0f3677dd5d)] - **test**: use realpath for NODE_TEST_DIR in common.js (Gibson Fahnestock) [#10723](https://github.com/nodejs/node/pull/10723)
- [[`5d0cc617bb`](https://github.com/nodejs/node/commit/5d0cc617bb)] - **test**: move resource intensive test to sequential (Rich Trott) [#10744](https://github.com/nodejs/node/pull/10744)
- [[`cd4bb067ad`](https://github.com/nodejs/node/commit/cd4bb067ad)] - **test**: add test for noAssert option in buf.read\*() (larissayvette) [#10713](https://github.com/nodejs/node/pull/10713)
- [[`5b55689b2c`](https://github.com/nodejs/node/commit/5b55689b2c)] - **test**: refactor test-crypto-padding-aes256 (adelmann) [#10622](https://github.com/nodejs/node/pull/10622)
- [[`119e512db3`](https://github.com/nodejs/node/commit/119e512db3)] - **test**: refactor the code of test-keep-alive.js (sivaprasanna) [#10684](https://github.com/nodejs/node/pull/10684)
- [[`ef3d889ee7`](https://github.com/nodejs/node/commit/ef3d889ee7)] - **test**: validate 'expected' argument to mustCall() (Nathan Friedly) [#10692](https://github.com/nodejs/node/pull/10692)
- [[`21704a3b6b`](https://github.com/nodejs/node/commit/21704a3b6b)] - **test**: fix misplaced ) in http response statuscode test (Nathan Friedly) [#10692](https://github.com/nodejs/node/pull/10692)
- [[`8565a06b09`](https://github.com/nodejs/node/commit/8565a06b09)] - **test**: refactor test-doctool-html.js (abouthiroppy) [#10696](https://github.com/nodejs/node/pull/10696)
- [[`168f3e4bf8`](https://github.com/nodejs/node/commit/168f3e4bf8)] - **test**: improve the code in test-process-hrtime (Adrian Estrada) [#10667](https://github.com/nodejs/node/pull/10667)
- [[`9acc86f578`](https://github.com/nodejs/node/commit/9acc86f578)] - **test**: refactor test-watch-file.js (sivaprasanna) [#10679](https://github.com/nodejs/node/pull/10679)
- [[`86e39367d6`](https://github.com/nodejs/node/commit/86e39367d6)] - **test**: improve zlib-from-gzip-with-trailing-garbage (Michael Lefkowitz) [#10674](https://github.com/nodejs/node/pull/10674)
- [[`3135455cd9`](https://github.com/nodejs/node/commit/3135455cd9)] - **test**: refactor the code in test-child-process-spawn-loop.js (sivaprasanna) [#10605](https://github.com/nodejs/node/pull/10605)
- [[`f43a8765a2`](https://github.com/nodejs/node/commit/f43a8765a2)] - **test**: allow testing uid and gid separately (cjihrig) [#10647](https://github.com/nodejs/node/pull/10647)
- [[`2f1d231c0d`](https://github.com/nodejs/node/commit/2f1d231c0d)] - **test**: improve test-http-chunked-304 (Adrian Estrada) [#10462](https://github.com/nodejs/node/pull/10462)
- [[`ec8a9962ce`](https://github.com/nodejs/node/commit/ec8a9962ce)] - **test**: improve test-fs-readfile-zero-byte-liar (Adrian Estrada) [#10570](https://github.com/nodejs/node/pull/10570)
- [[`12746af524`](https://github.com/nodejs/node/commit/12746af524)] - **test**: refactor test-fs-utimes (Junshu Okamoto) [#9290](https://github.com/nodejs/node/pull/9290)
- [[`e81b1cc1ae`](https://github.com/nodejs/node/commit/e81b1cc1ae)] - **test**: provide duration/interval to timers (Rich Trott) [#9472](https://github.com/nodejs/node/pull/9472)
- [[`17a63e15e6`](https://github.com/nodejs/node/commit/17a63e15e6)] - **test**: improve test-event-emitter-modify-in-emit (Adrian Estrada) [#10600](https://github.com/nodejs/node/pull/10600)
- [[`50ee4e6dad`](https://github.com/nodejs/node/commit/50ee4e6dad)] - **test**: require handler to be run in sigwinch test (Rich Trott) [#11068](https://github.com/nodejs/node/pull/11068)
- [[`8cce29587c`](https://github.com/nodejs/node/commit/8cce29587c)] - **test**: add 2nd argument to throws in test-assert (Marlena Compton) [#11061](https://github.com/nodejs/node/pull/11061)
- [[`b14d7b3aa1`](https://github.com/nodejs/node/commit/b14d7b3aa1)] - **test**: improve error messages in test-npm-install (Gonen Dukas) [#11027](https://github.com/nodejs/node/pull/11027)
- [[`87488ba2ff`](https://github.com/nodejs/node/commit/87488ba2ff)] - **test**: add path.join's test (Yuta Hiroto) [#11063](https://github.com/nodejs/node/pull/11063)
- [[`232664a10d`](https://github.com/nodejs/node/commit/232664a10d)] - **test**: fix timing sensitivity in debugger test (Ali Ijaz Sheikh) [#11008](https://github.com/nodejs/node/pull/11008)
- [[`c16160418b`](https://github.com/nodejs/node/commit/c16160418b)] - **test**: improve coverage on removeListeners functions (matsuda-koushi) [#11140](https://github.com/nodejs/node/pull/11140)
- [[`898276b1b4`](https://github.com/nodejs/node/commit/898276b1b4)] - **test**: simplify output handling in repl tests (Rich Trott) [#11124](https://github.com/nodejs/node/pull/11124)
- [[`3248cdb2e6`](https://github.com/nodejs/node/commit/3248cdb2e6)] - **test**: improve crypto.setEngine coverage to check for errors (Sebastian Van Sande) [#11143](https://github.com/nodejs/node/pull/11143)
- [[`28111f9eb2`](https://github.com/nodejs/node/commit/28111f9eb2)] - **test**: increase specificity in dgram test (Rich Trott) [#11187](https://github.com/nodejs/node/pull/11187)
- [[`c5e8ccab63`](https://github.com/nodejs/node/commit/c5e8ccab63)] - **test**: remove obsolete comment from dgram test (ALJCepeda) [#8689](https://github.com/nodejs/node/pull/8689)
- [[`7aebc6907c`](https://github.com/nodejs/node/commit/7aebc6907c)] - **test**: improve checks in test-path-parse-format (cjihrig) [#11223](https://github.com/nodejs/node/pull/11223)
- [[`baec432c93`](https://github.com/nodejs/node/commit/baec432c93)] - **test**: add coverage for string array dgram send() (cjihrig) [#11247](https://github.com/nodejs/node/pull/11247)
- [[`6694c26420`](https://github.com/nodejs/node/commit/6694c26420)] - **test**: adapt test-debugger-pid to localized Windows (Vse Mozhet Byt) [#11270](https://github.com/nodejs/node/pull/11270)
- [[`2db4c3c453`](https://github.com/nodejs/node/commit/2db4c3c453)] - **test**: add vm module edge cases (Franziska Hinkelmann) [#11265](https://github.com/nodejs/node/pull/11265)
- [[`759604912a`](https://github.com/nodejs/node/commit/759604912a)] - **test**: refactor test-dgram-setBroadcast.js (cjihrig) [#11252](https://github.com/nodejs/node/pull/11252)
- [[`3185fa1249`](https://github.com/nodejs/node/commit/3185fa1249)] - **test**: querystring.escape with multibyte characters (Daijiro Wachi) [#11251](https://github.com/nodejs/node/pull/11251)
- [[`460a3e1f7a`](https://github.com/nodejs/node/commit/460a3e1f7a)] - **test**: improve test-assert.js (jobala) [#11193](https://github.com/nodejs/node/pull/11193)
- [[`1adfca4b5e`](https://github.com/nodejs/node/commit/1adfca4b5e)] - **test**: refactor test-repl-sigint (Rich Trott) [#11309](https://github.com/nodejs/node/pull/11309)
- [[`c539325d89`](https://github.com/nodejs/node/commit/c539325d89)] - **test**: improve punycode test coverage (Sebastian Van Sande) [#11144](https://github.com/nodejs/node/pull/11144)
- [[`8db3c770be`](https://github.com/nodejs/node/commit/8db3c770be)] - **test**: refactor test-repl-sigint-nested-eval (Rich Trott) [#11303](https://github.com/nodejs/node/pull/11303)
- [[`874ef9d312`](https://github.com/nodejs/node/commit/874ef9d312)] - **test**: add coverage for dgram \_createSocketHandle() (cjihrig) [#11291](https://github.com/nodejs/node/pull/11291)
- [[`92f6919532`](https://github.com/nodejs/node/commit/92f6919532)] - **test**: improve crypto coverage (Akito Ito) [#11280](https://github.com/nodejs/node/pull/11280)
- [[`d9deb1fb62`](https://github.com/nodejs/node/commit/d9deb1fb62)] - **test**: improve message in net-connect-local-error (Rich Trott) [#11393](https://github.com/nodejs/node/pull/11393)
- [[`6677c113aa`](https://github.com/nodejs/node/commit/6677c113aa)] - **test**: refactor test-dgram-membership (Rich Trott) [#11388](https://github.com/nodejs/node/pull/11388)
- [[`e7b7d7279c`](https://github.com/nodejs/node/commit/e7b7d7279c)] - **test**: cases to querystring related to empty string (Daijiro Wachi) [#11329](https://github.com/nodejs/node/pull/11329)
- [[`5a92fc25a1`](https://github.com/nodejs/node/commit/5a92fc25a1)] - **test**: consolidate buffer.read() in a file (larissayvette) [#11297](https://github.com/nodejs/node/pull/11297)
- [[`607158ab6e`](https://github.com/nodejs/node/commit/607158ab6e)] - **test**: improve crypto coverage (樋口　彰) [#11279](https://github.com/nodejs/node/pull/11279)
- [[`27f302d94f`](https://github.com/nodejs/node/commit/27f302d94f)] - **test**: remove unused args and comparison fix (Alexander) [#11396](https://github.com/nodejs/node/pull/11396)
- [[`8da156d68f`](https://github.com/nodejs/node/commit/8da156d68f)] - **test**: add coverage for utf8CheckIncomplete() (xiaoyu) [#11419](https://github.com/nodejs/node/pull/11419)
- [[`0ddad76813`](https://github.com/nodejs/node/commit/0ddad76813)] - **test**: fix over-dependence on native promise impl (Ali Ijaz Sheikh) [#11437](https://github.com/nodejs/node/pull/11437)
- [[`34444580f6`](https://github.com/nodejs/node/commit/34444580f6)] - **test**: add test cases for path (Yuta Hiroto) [#11453](https://github.com/nodejs/node/pull/11453)
- [[`4bcf1a0387`](https://github.com/nodejs/node/commit/4bcf1a0387)] - **test**: refactor test-http-response-splitting (Arseniy Maximov) [#11429](https://github.com/nodejs/node/pull/11429)
- [[`7836807178`](https://github.com/nodejs/node/commit/7836807178)] - **test**: add error checking in callback (Rich Trott) [#11446](https://github.com/nodejs/node/pull/11446)
- [[`13b7856444`](https://github.com/nodejs/node/commit/13b7856444)] - **test**: improve coverage in test-crypto.dh (Eric Christie) [#11253](https://github.com/nodejs/node/pull/11253)
- [[`b2f7e7a5ad`](https://github.com/nodejs/node/commit/b2f7e7a5ad)] - **test**: add regex check to test-module-loading (Tarang Hirani) [#11413](https://github.com/nodejs/node/pull/11413)
- [[`6bf936644e`](https://github.com/nodejs/node/commit/6bf936644e)] - **test**: increase coverage of vm (DavidCai) [#11377](https://github.com/nodejs/node/pull/11377)
- [[`6202f14583`](https://github.com/nodejs/node/commit/6202f14583)] - **test**: throw check in test-zlib-write-after-close (Jason Wilson) [#11482](https://github.com/nodejs/node/pull/11482)
- [[`f8884dd1b5`](https://github.com/nodejs/node/commit/f8884dd1b5)] - **test**: add cases for unescape & unescapeBuffer (Daijiro Wachi) [#11326](https://github.com/nodejs/node/pull/11326)
- [[`05909d045b`](https://github.com/nodejs/node/commit/05909d045b)] - **test**: fix flaky test-vm-timeout-rethrow (Kunal Pathak) [#11530](https://github.com/nodejs/node/pull/11530)
- [[`6e5f6e3c02`](https://github.com/nodejs/node/commit/6e5f6e3c02)] - **test**: favor assertions over console logging (Rich Trott) [#11547](https://github.com/nodejs/node/pull/11547)
- [[`2c4aa39021`](https://github.com/nodejs/node/commit/2c4aa39021)] - **test**: mark test-tty-wrap as flaky for AIX (Michael Dawson) [#10618](https://github.com/nodejs/node/pull/10618)
- [[`cb03e74037`](https://github.com/nodejs/node/commit/cb03e74037)] - **test**: improve test-fs-null-bytes (Adrian Estrada) [#10521](https://github.com/nodejs/node/pull/10521)
- [[`69b55f35f7`](https://github.com/nodejs/node/commit/69b55f35f7)] - **test**: refactor test-https-truncate (Rich Trott) [#10225](https://github.com/nodejs/node/pull/10225)
- [[`ada7166dfd`](https://github.com/nodejs/node/commit/ada7166dfd)] - **test**: simplify test-http-client-unescaped-path (Rod Vagg) [#9649](https://github.com/nodejs/node/pull/9649)
- [[`1b85989fb2`](https://github.com/nodejs/node/commit/1b85989fb2)] - **test**: move long-running test to sequential (Rich Trott) [#11176](https://github.com/nodejs/node/pull/11176)
- [[`87760cc346`](https://github.com/nodejs/node/commit/87760cc346)] - **test**: add new.target add-on regression test (Ben Noordhuis) [#9689](https://github.com/nodejs/node/pull/9689)
- [[`73283060ad`](https://github.com/nodejs/node/commit/73283060ad)] - **test,repl**: add coverage for repl .clear+useGlobal (Rich Trott) [#10777](https://github.com/nodejs/node/pull/10777)
- [[`4a87aee532`](https://github.com/nodejs/node/commit/4a87aee532)] - **test,util**: remove lint workarounds (Rich Trott) [#10785](https://github.com/nodejs/node/pull/10785)
- [[`3e9ce770f7`](https://github.com/nodejs/node/commit/3e9ce770f7)] - **test-console**: streamline arrow fn and refine regex (John Maguire) [#11039](https://github.com/nodejs/node/pull/11039)
- [[`b90a141cc7`](https://github.com/nodejs/node/commit/b90a141cc7)] - **timer**: remove duplicated word in comment (asafdav2) [#11323](https://github.com/nodejs/node/pull/11323)
- [[`d71ebb90ec`](https://github.com/nodejs/node/commit/d71ebb90ec)] - **timer,domain**: maintain order of timer callbacks (John Barboza) [#10522](https://github.com/nodejs/node/pull/10522)
- [[`2a168917cb`](https://github.com/nodejs/node/commit/2a168917cb)] - **tls**: do not crash on STARTTLS when OCSP requested (Fedor Indutny) [#10706](https://github.com/nodejs/node/pull/10706)
- [[`f33684ac5f`](https://github.com/nodejs/node/commit/f33684ac5f)] - **tools**: remove custom align-function-arguments rule (Rich Trott) [#10561](https://github.com/nodejs/node/pull/10561)
- [[`fb2f449acc`](https://github.com/nodejs/node/commit/fb2f449acc)] - **tools**: update ESLint to current version (Rich Trott) [#10561](https://github.com/nodejs/node/pull/10561)
- [[`83a3aef873`](https://github.com/nodejs/node/commit/83a3aef873)] - **tools**: rename eslintrc to an undeprecated format (Sakthipriyan Vairamani) [#7699](https://github.com/nodejs/node/pull/7699)
- [[`e4f7f5c630`](https://github.com/nodejs/node/commit/e4f7f5c630)] - **tools**: add lint rule to enforce timer arguments (Rich Trott) [#9472](https://github.com/nodejs/node/pull/9472)
- [[`a13bb54466`](https://github.com/nodejs/node/commit/a13bb54466)] - **tools**: add compile_commands.json gyp generator (Ben Noordhuis) [#7986](https://github.com/nodejs/node/pull/7986)
- [[`b38d8d6e06`](https://github.com/nodejs/node/commit/b38d8d6e06)] - **tools**: suggest python2 command in configure (Roman Reiss) [#11375](https://github.com/nodejs/node/pull/11375)
- [[`291346ea51`](https://github.com/nodejs/node/commit/291346ea51)] - **tools,doc**: add Google Analytics tracking. (Phillip Johnsen) [#6601](https://github.com/nodejs/node/pull/6601)
- [[`1ed47d3f33`](https://github.com/nodejs/node/commit/1ed47d3f33)] - **tty**: avoid oob warning in TTYWrap::GetWindowSize() (Dmitry Tsvettsikh) [#11454](https://github.com/nodejs/node/pull/11454)
- [[`9e6fcbb34c`](https://github.com/nodejs/node/commit/9e6fcbb34c)] - **url**: fix surrogate handling in encodeAuth() (Timothy Gu) [#11387](https://github.com/nodejs/node/pull/11387)
- [[`53213004eb`](https://github.com/nodejs/node/commit/53213004eb)] - **util**: improve readability of normalizeEncoding (Joyee Cheung) [#10439](https://github.com/nodejs/node/pull/10439)
- [[`e54b433c8d`](https://github.com/nodejs/node/commit/e54b433c8d)] - **util**: use ES2015+ Object.is to check negative zero (Shinnosuke Watanabe) [#11332](https://github.com/nodejs/node/pull/11332)
- [[`2e15d48447`](https://github.com/nodejs/node/commit/2e15d48447)] - **v8**: drop v8::FunctionCallbackInfo\<T\>::NewTarget() (Ben Noordhuis) [#9293](https://github.com/nodejs/node/pull/9293)
- [[`fd1ffe4f5a`](https://github.com/nodejs/node/commit/fd1ffe4f5a)] - **v8**: fix --always-opt bug (Ben Noordhuis) [#9293](https://github.com/nodejs/node/pull/9293)
- [[`a55af77fc5`](https://github.com/nodejs/node/commit/a55af77fc5)] - **vm**: refactor vm module (James M Snell) [#11392](https://github.com/nodejs/node/pull/11392)

Windows 32-bit Installer: https://nodejs.org/dist/v6.10.1/node-v6.10.1-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v6.10.1/node-v6.10.1-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v6.10.1/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v6.10.1/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v6.10.1/node-v6.10.1.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v6.10.1/node-v6.10.1-darwin-x64.tar.gz \
Linux 32-bit Binary: https://nodejs.org/dist/v6.10.1/node-v6.10.1-linux-x86.tar.xz \
Linux 64-bit Binary: https://nodejs.org/dist/v6.10.1/node-v6.10.1-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v6.10.1/node-v6.10.1-linux-ppc64le.tar.xz \
Linux PPC BE 64-bit Binary: https://nodejs.org/dist/v6.10.1/node-v6.10.1-linux-ppc64.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v6.10.1/node-v6.10.1-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v6.10.1/node-v6.10.1-aix-ppc64.tar.gz \
SmartOS 32-bit Binary: https://nodejs.org/dist/v6.10.1/node-v6.10.1-sunos-x86.tar.xz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v6.10.1/node-v6.10.1-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v6.10.1/node-v6.10.1-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v6.10.1/node-v6.10.1-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v6.10.1/node-v6.10.1-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v6.10.1/node-v6.10.1.tar.gz \
Other release files: https://nodejs.org/dist/v6.10.1/ \
Documentation: https://nodejs.org/docs/v6.10.1/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

fe3f3de8ac6789df4639e29dd2ee3f5b3b8c30445a6120f1caf46dd347229e4a  node-v6.10.1-aix-ppc64.tar.gz
5b61c8c15c4d79283e39ef5ccba396aed5031a4a33df2b9dd5b20f14b38bb4b9  node-v6.10.1-darwin-x64.tar.gz
96d355d0fede05889d404617946c86b9b06926a05bb93a19466cc0a408476ed8  node-v6.10.1-darwin-x64.tar.xz
00172a468520b4c7fb2db1d3c169ae548e421239c1e43c5cc7a28b303cad96ce  node-v6.10.1-headers.tar.gz
52d7713d3785e86a40155cd71af67dd1ec0802b4609a78e7d20228b6802f866c  node-v6.10.1-headers.tar.xz
8d3955523e2f25e8f62aefb1181f500a6e1458d4decbda95b147f3e3f4598081  node-v6.10.1-linux-arm64.tar.gz
c361ca0b508fcdfcd0e07e588b5cc38f32c547c94eb6a60e30980dfda2b35df9  node-v6.10.1-linux-arm64.tar.xz
e59a5558a6271385fddc5f58f85dfe7bf9b7c73d75ea14d0171266cf90bff830  node-v6.10.1-linux-armv6l.tar.gz
277a85ddd93581a48798e87ef568d5353d03e50bb2af04a9fb54ee4d115fb022  node-v6.10.1-linux-armv6l.tar.xz
d50572bfbbf8fb617fb8abe0bfd59974e7f8841c6b366ffdfc532f40039f4451  node-v6.10.1-linux-armv7l.tar.gz
aa16b41344fcdce55efff1a5b43d921f9721cdfc0eebe12ea3306cad832c5b90  node-v6.10.1-linux-armv7l.tar.xz
4ee596c38a271d1f32c614bd43dfb79b8dce5cba66166ae5c7d242d1d4444ff3  node-v6.10.1-linux-ppc64le.tar.gz
b4cf8cf2c1dd6d3fbdf632540d6820430b37802ca74fae4a9bc68a67de69d4a1  node-v6.10.1-linux-ppc64le.tar.xz
463ea8c16ecdbbdf05f60a6a3dd309bba0bab4c9741e5ca53cfb5581b4020c71  node-v6.10.1-linux-ppc64.tar.gz
c8a7049c9a087c28a0691b31670034fb6c96e3fb2bf475d479bb15c9c4d7f7ad  node-v6.10.1-linux-ppc64.tar.xz
995644d4fa2fb33a070bfe44b62a66b0272e25cb28ab14f2fec48e9843575006  node-v6.10.1-linux-s390x.tar.gz
f646d61b43412efad1ef8ab8137adfec05cd66fa1218cc0a846573bbfc494654  node-v6.10.1-linux-s390x.tar.xz
31ad1731f4375da2f3ee739f23b0d92c54402eefcc7f98595010395178dde047  node-v6.10.1-linux-x64.tar.gz
e8f100e9ee70eb63216b33cf39666a4dc6c4038f6ee4fbfc7751ab3a825e576c  node-v6.10.1-linux-x64.tar.xz
463c32dd47fe51e3eb694bfb019e17a9e1fe83f3292fb02c05103e20494323b9  node-v6.10.1-linux-x86.tar.gz
1e8be5c912cb31e7221836d3f988432e63a1564b2213a673259367f9f7222b1e  node-v6.10.1-linux-x86.tar.xz
14ce9adc6a08bbbce9005e4bbb0a788407afd280da93744ed1cb4c563bc97dcb  node-v6.10.1.pkg
e89d40e980caa52d59fcaa4fb77e6fdc45166ccc0e1093f33e69a0fa497ee444  node-v6.10.1-sunos-x64.tar.gz
a9f96424a4a2a347eb22f681d2ba9f2b5a834f84dc0cc47ebeb802a6654a5f56  node-v6.10.1-sunos-x64.tar.xz
e73d3a948fbb4024f3022af0530ee15e9f4950b2f463fa5c546e96d3d5ecf5bc  node-v6.10.1-sunos-x86.tar.gz
363d72c5f91970d33449350676ef009978af304c466b4f14890554b76b0a84dc  node-v6.10.1-sunos-x86.tar.xz
2c7a643b199c63390f4e33359e82f1449b84ec94d647c606fc0f1d1a2b5bdedd  node-v6.10.1.tar.gz
6547c1c25440bd71d75f8a23590c2cd6d1c00c0c72736b5de79e7152566fd4b2  node-v6.10.1.tar.xz
80e936dbece81dd1c292c67001c1e9989d98201d2015481282d02db32e298f3d  node-v6.10.1-win-x64.7z
28923f51691bb34dc399af4ceb567da487d7f4806aec5e6f0cfab1e6c3f2dd1c  node-v6.10.1-win-x64.zip
9e8bfa89c9db626507c4ecc4869816a3a1673e09d3069cb2901648dd09ae9a39  node-v6.10.1-win-x86.7z
87a94bc3ddf80f467408e3e6f95e425dd576e6d6cc6c0fad80203d52c786c621  node-v6.10.1-win-x86.zip
e9fd4ff49700ece923e6e0d9b9013abc1ce65c3ed3a5810d166a0e05a911c2d4  node-v6.10.1-x64.msi
868a96e012222ce95c732d4e2c97d0349f8fefbd889af690cf6d2278d6844932  node-v6.10.1-x86.msi
da55174c403fe35123bc78416c32c8fddd8515057f36a3587df3838f8759fc65  win-x64/node.exe
3396b0e4442d094c52f16a120c35ef290525454eacec866e4e835e3c73fbcb80  win-x64/node.lib
5b23d7e9192b7feab6f7f5d9caaa58436a1cf8a770194ded4bbba44cd834aecf  win-x64/node_pdb.7z
0eb4c5a34b32c6d1a4807be35ededf54e3cdd9478fff3d05f2d9f0b5ac53d895  win-x64/node_pdb.zip
dedd07471e0cd2f478ce283cdc18dca71cc3a3e8ff82972a2e162bce06516e21  win-x86/node.exe
af8a1032de2ab7637ce39280eec9d2fe71cadf6dc4b45c4fb01d1efae68b362d  win-x86/node.lib
587c0d669f565048a130c3147dad1a7e14115d930ea6d4e8f309ea7a4dbd272d  win-x86/node_pdb.7z
252f86bbcadfcc893a6097f847749eb2ebbe5d1d126726c375e11dce79707a06  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQEcBAEBCAAGBQJY0ZtBAAoJEJM7AfQLXKlGIhoH/izl87LyjwTHBO0ofruiEack
SOgm/OPkefxmcI636z2ooS5smGJgRjgdnXN7qwdZhXAvdrtjlBhdayu36gkqFYOm
fofjqvvzQ/6+LFg97F9c8E3E/Vc+4GBVYu6eJ+hggua+tPBE34gajdmJUJ9IEOiu
u2mNcC2J1eD4i2C8v0SRfTDA+54Tb5mzBl3fziciAEjfdKwVL+FSmPNMCYhI+Dh8
zuE2A3ckTO84jCiuDWO3ixcA77mToVjk1tn+uSZsGy+rt781sd/tQa9IswnWwZR6
FoZLPgzwv0eic9y3PveHB+QmejfMdhyANsnveHY5eJKFgyQnwUDOvaRNlPIJsCY=
=eMcy
-----END PGP SIGNATURE-----

```
