---
date: '2022-07-07T16:08:08.050Z'
category: release
title: Node v14.20.0 (LTS)
layout: blog-post
author: <PERSON>
---

### Notable Changes

- \[[`8e8aef836c`](https://github.com/nodejs/node/commit/8e8aef836c)] - **(SEMVER-MAJOR)** **src,deps,build,test**: add OpenSSL config appname (<PERSON>) [#43124](https://github.com/nodejs/node/pull/43124)
- \[[`98965b137d`](https://github.com/nodejs/node/commit/98965b137d)] - **deps**: upgrade openssl sources to 1.1.1q (RafaelGSS) [#43686](https://github.com/nodejs/node/pull/43686)

### Commits

- \[[`b93e048bf6`](https://github.com/nodejs/node/commit/b93e048bf6)] - **deps**: update archs files for OpenSSL-1.1.1q (RafaelGSS) [#43686](https://github.com/nodejs/node/pull/43686)
- \[[`98965b137d`](https://github.com/nodejs/node/commit/98965b137d)] - **deps**: upgrade openssl sources to 1.1.1q (RafaelGSS) [#43686](https://github.com/nodejs/node/pull/43686)
- \[[`837a1d803e`](https://github.com/nodejs/node/commit/837a1d803e)] - **deps**: update archs files for OpenSSL-1.1.1p (RafaelGSS) [#43527](https://github.com/nodejs/node/pull/43527)
- \[[`c5d9c9a49e`](https://github.com/nodejs/node/commit/c5d9c9a49e)] - **deps**: upgrade openssl sources to 1.1.1p (RafaelGSS) [#43527](https://github.com/nodejs/node/pull/43527)
- \[[`da0fda0fe8`](https://github.com/nodejs/node/commit/da0fda0fe8)] - **http**: stricter Transfer-Encoding and header separator parsing (Paolo Insogna) [#315](https://github.com/nodejs/node/pull/315)
- \[[`48c5aa5cab`](https://github.com/nodejs/node/commit/48c5aa5cab)] - **src**: fix IPv4 validation in inspector_socket (Tobias Nießen) [nodejs-private/node-private#320](https://github.com/nodejs-private/node-private/pull/320)
- \[[`8e8aef836c`](https://github.com/nodejs/node/commit/8e8aef836c)] - **(SEMVER-MAJOR)** **src,deps,build,test**: add OpenSSL config appname (Daniel Bevenius) [#43124](https://github.com/nodejs/node/pull/43124)

Windows 32-bit Installer: https://nodejs.org/dist/v14.20.0/node-v14.20.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v14.20.0/node-v14.20.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v14.20.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v14.20.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v14.20.0/node-v14.20.0.pkg \
macOS Intel 64-bit Binary: https://nodejs.org/dist/v14.20.0/node-v14.20.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v14.20.0/node-v14.20.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v14.20.0/node-v14.20.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v14.20.0/node-v14.20.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v14.20.0/node-v14.20.0-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v14.20.0/node-v14.20.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v14.20.0/node-v14.20.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v14.20.0/node-v14.20.0.tar.gz \
Other release files: https://nodejs.org/dist/v14.20.0/ \
Documentation: https://nodejs.org/docs/v14.20.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

ce1d48f43d7a0f1709e8ec1776ea67de10104038c4784ebd399fd4971b7cc53c  node-v14.20.0-aix-ppc64.tar.gz
4471111f666f1d1ec549b002e0d416a7222c0fd416aa62c90b19ff3930b07dba  node-v14.20.0-darwin-x64.tar.gz
31bb4cd3365abea60aabfb58fdcc26051e18d66d36455eaf1b466da2943ef2d5  node-v14.20.0-darwin-x64.tar.xz
e2a54a456b83f76e58004b4c8ba10134200a0cf1d2e52beca2ecb46770f735c4  node-v14.20.0-headers.tar.gz
0429b0ba613e61b8f36f2f914142c8242e1bf3716aae8d8d9d74bd7c9000ec78  node-v14.20.0-headers.tar.xz
d2d15363a2f3a0446983d51a90af7942fe8b1dd4a7f16128dfe718b3bf56dc07  node-v14.20.0-linux-arm64.tar.gz
8a31a4e78c5c0153d933538017139d0c6554d2b0bb0abe8b87b71a1e1d4bf65d  node-v14.20.0-linux-arm64.tar.xz
dee137c30616717a917e57ceb17594412d4ced1d6dd7d8bad31c2ff529143326  node-v14.20.0-linux-armv7l.tar.gz
a714e584d8d9140e4daf55df82260618d81756809d464760f6a58f37dc5ff8fd  node-v14.20.0-linux-armv7l.tar.xz
b61f6ab4ec04e8b607b692199203ee3f88a6344ffa027dc90aa023b47f3edd95  node-v14.20.0-linux-ppc64le.tar.gz
4f4f9cbdf8e31062e6eafea6ae7be6a14d9590810b96c02627ecf36d596f1043  node-v14.20.0-linux-ppc64le.tar.xz
5d1b24364d7de9ad7cc96250caa897949760ffd41398c1865577183a0b9e1cca  node-v14.20.0-linux-s390x.tar.gz
aa8266dcc04b7b145ced4cd30bbfec253898af657178ab3c66d52295250bdebe  node-v14.20.0-linux-s390x.tar.xz
921680e244b813d6ffe06a4c22f87f05f39be635973c79486b2ded12a946cb37  node-v14.20.0-linux-x64.tar.gz
c3722dc5c0bc90efc0872328f5bb7c602002958e4e7e97c15ec29a96e3ac3b98  node-v14.20.0-linux-x64.tar.xz
7c51582258e70274de42fb139fa061d19be2e75f6281981a17ba87e1cdf705f2  node-v14.20.0.pkg
53098eafccdd69120b9d9187eb3bbb872c91ee17bfa0ee33aaeb300803c113f5  node-v14.20.0.tar.gz
2b5098498889d1e6a9709d63f3d6f94e696a5ad8221618c5d51159cee363996a  node-v14.20.0.tar.xz
fa6fd2ff633d3a9d23975f92d6343f32b085e0c4cd5dc100bc163909d4b85fbf  node-v14.20.0-win-x64.7z
617eb0dc4c1e55501c091942f677f9aeadf52c120bc593e9454297e1eacb7e59  node-v14.20.0-win-x64.zip
ef9506a88105c4692000b39cbca5dfc2b607f1930f31369519ea18a1113dac01  node-v14.20.0-win-x86.7z
04b1f139a00d83b975c1a96da4920752cb233c40d7cf66fe9ef486280a681419  node-v14.20.0-win-x86.zip
66f0fd27d243df41802204aec3e81f8d674a459a68679c57e49e121bcaf3b367  node-v14.20.0-x64.msi
934981d1c03616f2010cfb3542aa233a0db700ed3fedc8fc4de8872330a0cb79  node-v14.20.0-x86.msi
ac38e3cc6ba00d9209b1890da4e224e6ae819e681a25cc933770c253d40cab46  win-x64/node.exe
a9dce83b4c853ed719e16fb1005b3b31d277909f6c44551b1177c9630cf16cae  win-x64/node.lib
c38e5d09fef9e1c9a65223f3fa2715a68660514710f6a1bfb6028d30b1607288  win-x64/node_pdb.7z
7335108bef1fe6e41b4f6386402b9def56b6288444e6df0f751ab701e393c543  win-x64/node_pdb.zip
2a960a34e2a1f7737644dbf805e62aa2875f0c4e2aa1c18049f8d4bd7c0ad562  win-x86/node.exe
5b45d6d0ccd3f7b14ad6df4fffe506f8ea96b9332f79e77b156a3b3dcd568e41  win-x86/node.lib
3f0f78154a071a66fb007c8175056518a7c021599f5a1bc8e49e39efe99a5a59  win-x86/node_pdb.7z
ee24817bf6119a1b68d25243043efe87e2dff53ceeb96ef1d42b8812c74f8a2e  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEdPEmArbxxOkT+qN606iWE2Q7YgEFAmLG9nEACgkQ06iWE2Q7
YgEO8hAAh+q7GWGoxiQjxVyuOyyRy+lJyF18MK8HmBVr4hV/DfQwh1Wc7H1eE5zk
fG+s/cg+dcG5nLMnuOtfjFBrt+bjZ0f9YQ3Xgue+utCvzen6floY7b+BotepmjC/
R8oXYuP0ldT5sn9ev7YVhu6n4NYGLBf8sBYDGyCNkK4BNKcUdSTnK7Qw64t56/3D
jRG51PqXcB+yBSwqVkEFjiu/69RUEJb/K+Jm3oIg1hkuVvpb7eoQfOCL44LsKLvQ
2xmMunG2EGTgPlsuKXBbG8f40g9O9+8kosUHQ2iowggB9htLvuAdRuDL+X/NN7FM
p06oErGDfdZasQZmj0GW6hw5ddU7swKOhuRRJH5j1uthCKv2uXL0WjmrfUEUget+
yjx81k9UEtocpJZ0VuFFJ0cP5cksajDhJ9+5TxLA/5Y7aqgjYvf9u+IWzEpqyKqu
Na1NHqwb+iDZHhov4rEPNcBmBJYfwV0mSBY1cH6GTGlD/xPZmNAlQXlSkQYmdxRz
KK5Wo+7OI2jCK34EcTe7WN5W/X4TZ/PVlH0Clvv6iFgp+nW6Z2GiKl/+8FAml3Lt
sozxZ5Jz5Mn2WHzlsVf0fE/ORJi0Hoi+2bz624VrNAliwUWq9Ej9+h+P+Qz6z46T
nJnOWOeNggfdQWSnPPkXJflVxkXy5LNgKkg/q89cQz3zOsFoV40=
=T0Ej
-----END PGP SIGNATURE-----

```
