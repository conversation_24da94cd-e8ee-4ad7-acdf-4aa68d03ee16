---
date: '2021-02-18T02:30:19.673Z'
category: release
title: Node v15.9.0 (Current)
layout: blog-post
author: <PERSON>
---

### Notable Changes

- **crypto**:
  - add keyObject.export() 'jwk' format option (<PERSON><PERSON>) [#37081](https://github.com/nodejs/node/pull/37081)
- **deps**:
  - upgrade to libuv 1.41.0 (<PERSON>) [#37360](https://github.com/nodejs/node/pull/37360)
- **doc**:
  - add dmabupt to collaborators (<PERSON>) [#37377](https://github.com/nodejs/node/pull/37377)
  - refactor fs docs structure (<PERSON>) [#37170](https://github.com/nodejs/node/pull/37170)
- **fs**:
  - add fsPromises.watch() (<PERSON>) [#37179](https://github.com/nodejs/node/pull/37179)
  - use a default callback for fs.close() (<PERSON>) [#37174](https://github.com/nodejs/node/pull/37174)
  - add AbortSignal support to watch (<PERSON>ruen<PERSON>) [#37190](https://github.com/nodejs/node/pull/37190)
- **perf_hooks**:
  - introduce createHistogram (James M Snell) [#37155](https://github.com/nodejs/node/pull/37155)
- **stream**:
  - improve Readable.from error handling (Benjamin Gruenbaum) [#37158](https://github.com/nodejs/node/pull/37158)
- **timers**:
  - introduce setInterval async iterator (linkgoron) [#37153](https://github.com/nodejs/node/pull/37153)
- **tls**:
  - add ability to get cert/peer cert as X509Certificate object (James M Snell) [#37070](https://github.com/nodejs/node/pull/37070)

### Commits

- [[`d0f1ff53ff`](https://github.com/nodejs/node/commit/d0f1ff53ff)] - **async_hooks**: set unhandledRejection async context (Sajal Khandelwal) [#37281](https://github.com/nodejs/node/pull/37281)
- [[`c160d88c9e`](https://github.com/nodejs/node/commit/c160d88c9e)] - **buffer**: add @@toStringTag to Blob (Colin Ihrig) [#37336](https://github.com/nodejs/node/pull/37336)
- [[`8487184457`](https://github.com/nodejs/node/commit/8487184457)] - **child_process**: fix bad abort signal leak (Nitzan Uziely) [#37257](https://github.com/nodejs/node/pull/37257)
- [[`e28ea89b1a`](https://github.com/nodejs/node/commit/e28ea89b1a)] - **crypto**: fix subtle.importKey JWK OKP public key import (Filip Skokan) [#37255](https://github.com/nodejs/node/pull/37255)
- [[`55fd6b6611`](https://github.com/nodejs/node/commit/55fd6b6611)] - **crypto**: avoid infinite loops in prime generation (Tobias Nießen) [#37212](https://github.com/nodejs/node/pull/37212)
- [[`9dac99a11a`](https://github.com/nodejs/node/commit/9dac99a11a)] - **crypto**: fix and simplify prime option validation (Tobias Nießen) [#37164](https://github.com/nodejs/node/pull/37164)
- [[`3e2746ff63`](https://github.com/nodejs/node/commit/3e2746ff63)] - **crypto**: remove webcrypto "DSA" JWK Key Type operations (Filip Skokan) [#37203](https://github.com/nodejs/node/pull/37203)
- [[`011910b424`](https://github.com/nodejs/node/commit/011910b424)] - **(SEMVER-MINOR)** **crypto**: add keyObject.export() 'jwk' format option (Filip Skokan) [#37081](https://github.com/nodejs/node/pull/37081)
- [[`c0eadef495`](https://github.com/nodejs/node/commit/c0eadef495)] - **deps**: upgrade to libuv 1.41.0 (Colin Ihrig) [#37360](https://github.com/nodejs/node/pull/37360)
- [[`50e81ba0b8`](https://github.com/nodejs/node/commit/50e81ba0b8)] - **deps**: V8: cherry-pick 0c8b6e415c30 (Matin Zadehdolatabad) [#37276](https://github.com/nodejs/node/pull/37276)
- [[`d1c1724c69`](https://github.com/nodejs/node/commit/d1c1724c69)] - **deps**: upgrade npm to 7.5.3 (Ruy Adorno) [#37283](https://github.com/nodejs/node/pull/37283)
- [[`20c65b00c2`](https://github.com/nodejs/node/commit/20c65b00c2)] - **deps**: V8: backport dfcf1e86fac0 (Michaël Zasso) [#37245](https://github.com/nodejs/node/pull/37245)
- [[`e63b380f76`](https://github.com/nodejs/node/commit/e63b380f76)] - **deps**: upgrade npm to 7.5.2 (Ruy Adorno) [#37191](https://github.com/nodejs/node/pull/37191)
- [[`d808db2732`](https://github.com/nodejs/node/commit/d808db2732)] - **doc**: add dmabupt to collaborators (Xu Meng) [#37377](https://github.com/nodejs/node/pull/37377)
- [[`dd054ca37f`](https://github.com/nodejs/node/commit/dd054ca37f)] - **doc**: optimize HTML rendering (Antoine du Hamel) [#37301](https://github.com/nodejs/node/pull/37301)
- [[`c188466a18`](https://github.com/nodejs/node/commit/c188466a18)] - **doc**: fix quotes in stream docs (Tobias Nießen) [#37269](https://github.com/nodejs/node/pull/37269)
- [[`f5e4625468`](https://github.com/nodejs/node/commit/f5e4625468)] - **doc**: fix backticks in crypto API docs (Tobias Nießen) [#37269](https://github.com/nodejs/node/pull/37269)
- [[`e2a2bab44e`](https://github.com/nodejs/node/commit/e2a2bab44e)] - **doc**: link PACKAGE_EXPORTS_RESOLVE to ESM section (Utku Gultopu) [#37135](https://github.com/nodejs/node/pull/37135)
- [[`1e99175e01`](https://github.com/nodejs/node/commit/1e99175e01)] - **doc**: alphabetize crypto.\* methods (Rich Trott) [#37353](https://github.com/nodejs/node/pull/37353)
- [[`392c86d38b`](https://github.com/nodejs/node/commit/392c86d38b)] - **doc**: use sentence case in benchmark doc (Rich Trott) [#37351](https://github.com/nodejs/node/pull/37351)
- [[`62b2648a96`](https://github.com/nodejs/node/commit/62b2648a96)] - **doc**: apply sentence-consistently in C++ style guide (Rich Trott) [#37350](https://github.com/nodejs/node/pull/37350)
- [[`189ce399da`](https://github.com/nodejs/node/commit/189ce399da)] - **doc**: apply sentence case to release doc headers (Rich Trott) [#37349](https://github.com/nodejs/node/pull/37349)
- [[`610b29b8bd`](https://github.com/nodejs/node/commit/610b29b8bd)] - **doc**: fix performanceEntry.flags style format (Cheng Liu) [#37274](https://github.com/nodejs/node/pull/37274)
- [[`85b1476f1d`](https://github.com/nodejs/node/commit/85b1476f1d)] - **doc**: fix typo in deprecations.md (marsonya) [#37282](https://github.com/nodejs/node/pull/37282)
- [[`f253cb9303`](https://github.com/nodejs/node/commit/f253cb9303)] - **doc**: fix typo in buffer.md (marsonya) [#37268](https://github.com/nodejs/node/pull/37268)
- [[`804e7ae713`](https://github.com/nodejs/node/commit/804e7ae713)] - **doc**: add version metadata for packages features (Antoine du Hamel) [#37289](https://github.com/nodejs/node/pull/37289)
- [[`cdd2fe5651`](https://github.com/nodejs/node/commit/cdd2fe5651)] - **doc**: fix typo in /api/dns.md (marsonya) [#37312](https://github.com/nodejs/node/pull/37312)
- [[`7d8fd3f576`](https://github.com/nodejs/node/commit/7d8fd3f576)] - **doc**: refactor fs docs structure (James M Snell) [#37170](https://github.com/nodejs/node/pull/37170)
- [[`facf3a5c23`](https://github.com/nodejs/node/commit/facf3a5c23)] - **doc**: fix description of hasSubscribers (Tobias Nießen) [#37324](https://github.com/nodejs/node/pull/37324)
- [[`3464c9f007`](https://github.com/nodejs/node/commit/3464c9f007)] - **doc**: discourage error event (Benjamin Gruenbaum) [#37264](https://github.com/nodejs/node/pull/37264)
- [[`85bed2ec26`](https://github.com/nodejs/node/commit/85bed2ec26)] - **doc**: fix misnamed SHASUMS256.txt name in README.md (marsonya) [#37260](https://github.com/nodejs/node/pull/37260)
- [[`cd50e93307`](https://github.com/nodejs/node/commit/cd50e93307)] - **doc**: warn about using strings as inputs in crypto (Tobias Nießen) [#37248](https://github.com/nodejs/node/pull/37248)
- [[`5a4288ebb6`](https://github.com/nodejs/node/commit/5a4288ebb6)] - **doc**: fix typo in crypto.md (marsonya) [#37279](https://github.com/nodejs/node/pull/37279)
- [[`0e887caf32`](https://github.com/nodejs/node/commit/0e887caf32)] - **doc**: fix typo in console.md (marsonya) [#37279](https://github.com/nodejs/node/pull/37279)
- [[`47c4f1fc54`](https://github.com/nodejs/node/commit/47c4f1fc54)] - **doc**: use sentence case in README headers (Rich Trott) [#37251](https://github.com/nodejs/node/pull/37251)
- [[`7da1c9b219`](https://github.com/nodejs/node/commit/7da1c9b219)] - **doc**: use sentence case for headers in BUILDING.md (Rich Trott) [#37250](https://github.com/nodejs/node/pull/37250)
- [[`ebf3597db1`](https://github.com/nodejs/node/commit/ebf3597db1)] - **doc**: rename N-API to Node-API (Gabriel Schulhof) [#37259](https://github.com/nodejs/node/pull/37259)
- [[`760f126adb`](https://github.com/nodejs/node/commit/760f126adb)] - **doc**: mark Certificate methods as static, add missing KeyObject.from (Filip Skokan) [#37198](https://github.com/nodejs/node/pull/37198)
- [[`aebe532967`](https://github.com/nodejs/node/commit/aebe532967)] - **doc**: consistent webcrypto `node.keyObject` format (Filip Skokan) [#37200](https://github.com/nodejs/node/pull/37200)
- [[`596bfb36a0`](https://github.com/nodejs/node/commit/596bfb36a0)] - **doc**: mention CryptoKey in port.postMessage() (Filip Skokan) [#37196](https://github.com/nodejs/node/pull/37196)
- [[`0702d60def`](https://github.com/nodejs/node/commit/0702d60def)] - **doc**: fix webcrypto HMAC generateKey example (Filip Skokan) [#37197](https://github.com/nodejs/node/pull/37197)
- [[`8a254058f5`](https://github.com/nodejs/node/commit/8a254058f5)] - **doc**: fix accommodate typos (Colin Ihrig) [#37229](https://github.com/nodejs/node/pull/37229)
- [[`5906e85ce2`](https://github.com/nodejs/node/commit/5906e85ce2)] - **doc**: fix version number for DEP006 (Antoine du Hamel) [#37231](https://github.com/nodejs/node/pull/37231)
- [[`52c40c7a48`](https://github.com/nodejs/node/commit/52c40c7a48)] - **doc**: fix CHANGELOG_ARCHIVE table of contents (Antoine du Hamel) [#37232](https://github.com/nodejs/node/pull/37232)
- [[`eb08afdf24`](https://github.com/nodejs/node/commit/eb08afdf24)] - **doc**: fix typo in globals.md (Darshan Sen) [#37228](https://github.com/nodejs/node/pull/37228)
- [[`b87c0d6c16`](https://github.com/nodejs/node/commit/b87c0d6c16)] - **doc**: fix typo in cli.md (Kalvin Vasconcellos) [#37214](https://github.com/nodejs/node/pull/37214)
- [[`3f815d93bf`](https://github.com/nodejs/node/commit/3f815d93bf)] - **doc**: fix pr-url for DEP0148 (Antoine du Hamel) [#37205](https://github.com/nodejs/node/pull/37205)
- [[`ff02e5e12c`](https://github.com/nodejs/node/commit/ff02e5e12c)] - **doc**: fix 404 links in module.md (Antoine du Hamel) [#37202](https://github.com/nodejs/node/pull/37202)
- [[`67c9a8e176`](https://github.com/nodejs/node/commit/67c9a8e176)] - **doc**: improve promise terminology (Benjamin Gruenbaum) [#37181](https://github.com/nodejs/node/pull/37181)
- [[`15804e0b3f`](https://github.com/nodejs/node/commit/15804e0b3f)] - **errors**: align source-map stacks with spec (Benjamin Coe) [#37252](https://github.com/nodejs/node/pull/37252)
- [[`88d3f74c85`](https://github.com/nodejs/node/commit/88d3f74c85)] - **(SEMVER-MINOR)** **fs**: add fsPromises.watch() (James M Snell) [#37179](https://github.com/nodejs/node/pull/37179)
- [[`c30245072a`](https://github.com/nodejs/node/commit/c30245072a)] - **fs**: allow passing negative zero fd (Darshan Sen) [#37123](https://github.com/nodejs/node/pull/37123)
- [[`655d19638a`](https://github.com/nodejs/node/commit/655d19638a)] - **(SEMVER-MINOR)** **fs**: use a default callback for fs.close() (James M Snell) [#37174](https://github.com/nodejs/node/pull/37174)
- [[`acd087dffb`](https://github.com/nodejs/node/commit/acd087dffb)] - **(SEMVER-MINOR)** **fs**: add AbortSignal support to watch (Benjamin Gruenbaum) [#37190](https://github.com/nodejs/node/pull/37190)
- [[`f5d1bf9d0e`](https://github.com/nodejs/node/commit/f5d1bf9d0e)] - **http**: explain the possibilty of refactor unused argument (Qingyu Deng) [#37275](https://github.com/nodejs/node/pull/37275)
- [[`d63ac28a9a`](https://github.com/nodejs/node/commit/d63ac28a9a)] - **http**: explain the unused argument in IncomingMessage.\_read (Qingyu Deng) [#37275](https://github.com/nodejs/node/pull/37275)
- [[`4cdc5ea823`](https://github.com/nodejs/node/commit/4cdc5ea823)] - **http**: fix ClientRequest unhandled errors (Robert Nagy) [#36970](https://github.com/nodejs/node/pull/36970)
- [[`c6198fddc7`](https://github.com/nodejs/node/commit/c6198fddc7)] - **lib**: simplify check in child_process (Darshan Sen) [#37367](https://github.com/nodejs/node/pull/37367)
- [[`f6f9af6a59`](https://github.com/nodejs/node/commit/f6f9af6a59)] - **lib**: fix WebIDL `object` and dictionary type conversion (ExE Boss) [#37047](https://github.com/nodejs/node/pull/37047)
- [[`acabe08b10`](https://github.com/nodejs/node/commit/acabe08b10)] - **lib**: add weak event handlers (Benjamin Gruenbaum) [#36607](https://github.com/nodejs/node/pull/36607)
- [[`3db1b30732`](https://github.com/nodejs/node/commit/3db1b30732)] - **meta**: update README releases section (Zuzana Svetlikova) [#37318](https://github.com/nodejs/node/pull/37318)
- [[`d96a97a2b9`](https://github.com/nodejs/node/commit/d96a97a2b9)] - **module**: make synthetic module evaluation steps return a Promise to support top level await (Daniel Clark) [#37300](https://github.com/nodejs/node/pull/37300)
- [[`a693baa0cb`](https://github.com/nodejs/node/commit/a693baa0cb)] - **module**: use optional chaining in cjs/loader.js (Darshan Sen) [#37238](https://github.com/nodejs/node/pull/37238)
- [[`061939d2f6`](https://github.com/nodejs/node/commit/061939d2f6)] - **(SEMVER-MINOR)** **node-api**: allow retrieval of add-on file name (Gabriel Schulhof) [#37195](https://github.com/nodejs/node/pull/37195)
- [[`c4faa39768`](https://github.com/nodejs/node/commit/c4faa39768)] - **(SEMVER-MINOR)** **perf_hooks**: introduce createHistogram (James M Snell) [#37155](https://github.com/nodejs/node/pull/37155)
- [[`799b2d5275`](https://github.com/nodejs/node/commit/799b2d5275)] - **policy**: fix cascade getting scope (Bradley Meck) [#37298](https://github.com/nodejs/node/pull/37298)
- [[`6d53e797d7`](https://github.com/nodejs/node/commit/6d53e797d7)] - **repl**: refactor to avoid unsafe array iteration (Antoine du Hamel) [#37345](https://github.com/nodejs/node/pull/37345)
- [[`3fee5b2219`](https://github.com/nodejs/node/commit/3fee5b2219)] - **repl**: add auto‑completion for dynamic import calls (ExE Boss) [#37178](https://github.com/nodejs/node/pull/37178)
- [[`c3778343aa`](https://github.com/nodejs/node/commit/c3778343aa)] - **repl**: refactor to avoid unsafe array iteration (Antoine du Hamel) [#37188](https://github.com/nodejs/node/pull/37188)
- [[`e28fa6c3fc`](https://github.com/nodejs/node/commit/e28fa6c3fc)] - **src**: fix return type of method in string_search.h (Darshan Sen) [#37167](https://github.com/nodejs/node/pull/37167)
- [[`42cc33cc48`](https://github.com/nodejs/node/commit/42cc33cc48)] - **src**: add mutex to ManagedEVPPKey class (Daniel Bevenius) [#36825](https://github.com/nodejs/node/pull/36825)
- [[`1a9bcdf1d9`](https://github.com/nodejs/node/commit/1a9bcdf1d9)] - **src**: refactor v8 binding (Joyee Cheung) [#37112](https://github.com/nodejs/node/pull/37112)
- [[`54d36b00af`](https://github.com/nodejs/node/commit/54d36b00af)] - **src**: rename binding_data_name to type_name in BindingData (Joyee Cheung) [#37112](https://github.com/nodejs/node/pull/37112)
- [[`3079a78428`](https://github.com/nodejs/node/commit/3079a78428)] - **src**: avoid implicit type conversions (Michaël Zasso) [#37149](https://github.com/nodejs/node/pull/37149)
- [[`a6053dc14a`](https://github.com/nodejs/node/commit/a6053dc14a)] - **src**: add context for TODO comment in env.cc (Yash Ladha) [#37140](https://github.com/nodejs/node/pull/37140)
- [[`354df9e8a1`](https://github.com/nodejs/node/commit/354df9e8a1)] - **src**: use make_shared for safe allocation (Yash Ladha) [#37139](https://github.com/nodejs/node/pull/37139)
- [[`337b4e7540`](https://github.com/nodejs/node/commit/337b4e7540)] - **src**: put (de)serialization code into node_snapshotable.h/cc (Joyee Cheung) [#37114](https://github.com/nodejs/node/pull/37114)
- [[`2a5f67b381`](https://github.com/nodejs/node/commit/2a5f67b381)] - **src**: refactor bookkeeping of bootstrap status (Joyee Cheung) [#37113](https://github.com/nodejs/node/pull/37113)
- [[`48ce1eb364`](https://github.com/nodejs/node/commit/48ce1eb364)] - **src**: fix warning in string_search.h (Darshan Sen) [#37146](https://github.com/nodejs/node/pull/37146)
- [[`bfe0b46d92`](https://github.com/nodejs/node/commit/bfe0b46d92)] - **src**: simplify calls to BN_bin2bn in prime gen (Tobias Nießen) [#37169](https://github.com/nodejs/node/pull/37169)
- [[`9946c1137e`](https://github.com/nodejs/node/commit/9946c1137e)] - **src**: read exactly two tokens from Linux THP sysfs config (James Addison) [#37065](https://github.com/nodejs/node/pull/37065)
- [[`1fea05149a`](https://github.com/nodejs/node/commit/1fea05149a)] - **(SEMVER-MINOR)** **stream**: improve Readable.from error handling (Benjamin Gruenbaum) [#37158](https://github.com/nodejs/node/pull/37158)
- [[`d2a487e640`](https://github.com/nodejs/node/commit/d2a487e640)] - **_Revert_** "**stream**: fix .end() error propagation" (Matteo Collina) [#37060](https://github.com/nodejs/node/pull/37060)
- [[`b5692b4b06`](https://github.com/nodejs/node/commit/b5692b4b06)] - **test**: fix test-doctool-html (Antoine du Hamel) [#37397](https://github.com/nodejs/node/pull/37397)
- [[`b09d21b06b`](https://github.com/nodejs/node/commit/b09d21b06b)] - **test**: enable no-restricted-syntax rule for test-timers-promisified (Rich Trott) [#37357](https://github.com/nodejs/node/pull/37357)
- [[`1fc8307138`](https://github.com/nodejs/node/commit/1fc8307138)] - **test**: re-implement promises.setInterval() test robustly (Rich Trott) [#37230](https://github.com/nodejs/node/pull/37230)
- [[`8483de4da8`](https://github.com/nodejs/node/commit/8483de4da8)] - **test**: only run prime test with supported OpenSSL (Tobias Nießen) [#37212](https://github.com/nodejs/node/pull/37212)
- [[`48a634e514`](https://github.com/nodejs/node/commit/48a634e514)] - **test**: rename n-api to node-api (Gabriel Schulhof) [#37217](https://github.com/nodejs/node/pull/37217)
- [[`51575252f5`](https://github.com/nodejs/node/commit/51575252f5)] - **test**: remove flaky designation for test-http2-large-file (Rich Trott) [#37156](https://github.com/nodejs/node/pull/37156)
- [[`13fe17c4ef`](https://github.com/nodejs/node/commit/13fe17c4ef)] - **test**: split heap snapshot limit tests (Rich Trott) [#37189](https://github.com/nodejs/node/pull/37189)
- [[`dc38dd2c6f`](https://github.com/nodejs/node/commit/dc38dd2c6f)] - **timers**: fix unsafe array iteration (Darshan Sen) [#37223](https://github.com/nodejs/node/pull/37223)
- [[`eb7ec1b257`](https://github.com/nodejs/node/commit/eb7ec1b257)] - **timers**: remove flaky setInterval test (Nitzan Uziely) [#37227](https://github.com/nodejs/node/pull/37227)
- [[`4ebe38b212`](https://github.com/nodejs/node/commit/4ebe38b212)] - **(SEMVER-MINOR)** **timers**: introduce setInterval async iterator (linkgoron) [#37153](https://github.com/nodejs/node/pull/37153)
- [[`dc84c181c3`](https://github.com/nodejs/node/commit/dc84c181c3)] - **(SEMVER-MINOR)** **tls**: add ability to get cert/peer cert as X509Certificate object (James M Snell) [#37070](https://github.com/nodejs/node/pull/37070)
- [[`2e1f1c6f3c`](https://github.com/nodejs/node/commit/2e1f1c6f3c)] - **tools**: refactor prefer-primordials (Antoine du Hamel) [#36018](https://github.com/nodejs/node/pull/36018)
- [[`b2b64113b1`](https://github.com/nodejs/node/commit/b2b64113b1)] - **tools**: update ESLint to 7.20.0 (Colin Ihrig) [#37339](https://github.com/nodejs/node/pull/37339)
- [[`a483c284f3`](https://github.com/nodejs/node/commit/a483c284f3)] - **tools**: fix lint-pr-url message (Antoine du Hamel) [#37304](https://github.com/nodejs/node/pull/37304)
- [[`1ff375beb3`](https://github.com/nodejs/node/commit/1ff375beb3)] - **tools**: avoid pending deprecation in doc generator (Michaël Zasso) [#37267](https://github.com/nodejs/node/pull/37267)
- [[`6db5e7958a`](https://github.com/nodejs/node/commit/6db5e7958a)] - **tools**: add GitHub Action linter for pr-url (Antoine du Hamel) [#37221](https://github.com/nodejs/node/pull/37221)
- [[`d8d851ac5c`](https://github.com/nodejs/node/commit/d8d851ac5c)] - **tools**: bump remark-present-lint-node from 2.0.0 to 2.0.1 (Rich Trott) [#37270](https://github.com/nodejs/node/pull/37270)
- [[`eb0daaedf9`](https://github.com/nodejs/node/commit/eb0daaedf9)] - **tools**: fix d8 macOS build (Michaël Zasso) [#37211](https://github.com/nodejs/node/pull/37211)
- [[`745aad73dc`](https://github.com/nodejs/node/commit/745aad73dc)] - **tools**: update ESLint to 7.19.0 (Colin Ihrig) [#37159](https://github.com/nodejs/node/pull/37159)
- [[`676f696a99`](https://github.com/nodejs/node/commit/676f696a99)] - **url**: fix definitions of `URL`/`SearchParams` methods and accessors (ExE Boss) [#36799](https://github.com/nodejs/node/pull/36799)
- [[`fbcab109de`](https://github.com/nodejs/node/commit/fbcab109de)] - **url**: move `URLSearchParams` method definitions (ExE Boss) [#36799](https://github.com/nodejs/node/pull/36799)
- [[`7c51cecbca`](https://github.com/nodejs/node/commit/7c51cecbca)] - **util**: use assert for unreachable code (Rich Trott) [#37249](https://github.com/nodejs/node/pull/37249)
- [[`66a14d3992`](https://github.com/nodejs/node/commit/66a14d3992)] - **vm**: add importModuleDynamically option to compileFunction (Gus Caplan) [#35431](https://github.com/nodejs/node/pull/35431)
- [[`05a16e7259`](https://github.com/nodejs/node/commit/05a16e7259)] - **worker**: refactor to avoid unsafe array iteration (Antoine du Hamel) [#37346](https://github.com/nodejs/node/pull/37346)

Windows 32-bit Installer: https://nodejs.org/dist/v15.9.0/node-v15.9.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v15.9.0/node-v15.9.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v15.9.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v15.9.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v15.9.0/node-v15.9.0.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v15.9.0/node-v15.9.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v15.9.0/node-v15.9.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v15.9.0/node-v15.9.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v15.9.0/node-v15.9.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v15.9.0/node-v15.9.0-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v15.9.0/node-v15.9.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v15.9.0/node-v15.9.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v15.9.0/node-v15.9.0.tar.gz \
Other release files: https://nodejs.org/dist/v15.9.0/ \
Documentation: https://nodejs.org/docs/v15.9.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

7d160d3accabb6c61a2d54aa0405f08060e0924e079ceccb31763c5666c72358  node-v15.9.0-aix-ppc64.tar.gz
55c6a84ce4d72b886914d4972007e09b72a34ee2483263a0d7218953ab716c70  node-v15.9.0-darwin-x64.tar.gz
aa8728706ff1474c086bd134c4c89c072b0a696934633c93e846489513759fd1  node-v15.9.0-darwin-x64.tar.xz
6738c98c79d6b85356453fa39e1fe8eb0a6ec66fca5981c31f33587afaf8c545  node-v15.9.0-headers.tar.gz
d9a1fd9459e0900c54d9e2d9d197ea548b455bf80322df84e880c740b75e6b39  node-v15.9.0-headers.tar.xz
65f3f889e6ac6952a7fa892e5d3ad19759d58771fbf4bc492d5e87117c2072d7  node-v15.9.0-linux-arm64.tar.gz
876ec8b8d2260408bfa781a8ca7cb2f4ff8cd2904a59ac53de946841f2c9348b  node-v15.9.0-linux-arm64.tar.xz
359614c3dcf7a5557ca9c9e6bcdcb6b9c6f0205aa87119335889e068a940c384  node-v15.9.0-linux-armv7l.tar.gz
610d4d56bd1430a4b8405d05cdd7832c0ac9a2cdddc03b752c354d84624627db  node-v15.9.0-linux-armv7l.tar.xz
0850c5cf32487980f0c93dfba6a2534ed138e9601f57c594561d68f52438c486  node-v15.9.0-linux-ppc64le.tar.gz
75540da591c4a529ada559368610e29c8698027e92e598d4b9f289fd05328e6b  node-v15.9.0-linux-ppc64le.tar.xz
f9fdfc66172ded7d2c8df80c46abca3f0fa01dff8a8118e723ff754234395f28  node-v15.9.0-linux-s390x.tar.gz
c632de7d132cd1b5cdf7ff63bdf76f46ece9e1d353eb7f33f9b26640fbaf939a  node-v15.9.0-linux-s390x.tar.xz
4e0488824dbdea2bd2db91e93aa08e250c655fedb35f06f4ca4373cbb198d428  node-v15.9.0-linux-x64.tar.gz
c57b1f33a1ee5957981df553f6a0e0cac302528f260140eda10fe5fdd24e1cbd  node-v15.9.0-linux-x64.tar.xz
302ee0469bab99d599aa02c5dbec50e94ca7ca9b4080b9db3069ee25109ce0fc  node-v15.9.0.pkg
5f41f4d15d7d592b170145cd994c9d7986536bb28ec151046da0632bd30e33bc  node-v15.9.0.tar.gz
bdd7961c003b4a2dbc8b54695dd1da2bf1f9b934aadb8fd4757e8e2fe437d8c3  node-v15.9.0.tar.xz
949a15edd5dc3a3c5cfa16ce8235f1290b7d4b2b89efb951f26ef4566e09d79f  node-v15.9.0-win-x64.7z
e9f8fc15566754bb4dac1e525beb2c9751a3ba750b98d4eae9f88711e505a2d6  node-v15.9.0-win-x64.zip
f909653ff31dcc1004bf3d8e056933f3513ff616fcf135cb720de7cb6d93c312  node-v15.9.0-win-x86.7z
28b9950221d826f0e3951d94ebc3b0ccf3021786839fd2fead56f0e308788417  node-v15.9.0-win-x86.zip
75cfbfabe1b9539602200853dfac45643ec92121332a00f59b1109d8038568eb  node-v15.9.0-x64.msi
624dc288c9ae9963f44fef4fbe9a38ed8652514c959c05eed0890de75dc221b7  node-v15.9.0-x86.msi
e1f84d3fad6adcf8ac0922982b713ee09c0f8edc9bb4e88dd021d6872c23d6e7  win-x64/node.exe
0692711edaf626282aaeff0a28cce749488d03a5d1db6490c7bb19866ec4aedd  win-x64/node.lib
55318a8cb06567cbea1cfc6b142fac72bf542a54a17a103e52224f9cb52e1dc6  win-x64/node_pdb.7z
1841543eb6428448b2f2b5d20c62b3a6417374dc71117bb0debb33e5a8ed338d  win-x64/node_pdb.zip
a1a1dbed3c71823859579c2d21736b183c15277c64ad3de73088b9c0746f38ab  win-x86/node.exe
6cec3adfe0cbf4f894d89fafe857fbda9dba14b61586d6abf145b3dd7775dad3  win-x86/node.lib
11820023b72160662f4b713023da513821d630bbc1ea36546d0652c4a41953fb  win-x86/node_pdb.7z
c5ffcc6b956475b92f8e8c08d36f7d1766b9ae7047c91c0eb96f1ca1a94a1515  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEdPEmArbxxOkT+qN606iWE2Q7YgEFAmAt0IkACgkQ06iWE2Q7
YgFDHw//ZA5gz6G3JcYq5nd5gAZpjqeh9oerGsqHpanknimc1KRD5S5M46jW3xFa
ZgqhxmG/hNsyOyGIwYldICLD8JZMfeoUaCj9wb/e9QZSWLXq359wypp4/YRaGqFm
LHLpquexgILJ0j2u6i0KtlmrQHP5d0VPhQJGnsqJig84pYpIeUOiI7BwC3GAD6uX
ScRoIkICW7cMm9wNcQl+3KFDYhRJNMFfG1TfMniPVqjrCX463unlUgQo5hFmkUjf
fsbOT81rhDcvcYi8HTCOauJiJReTYl9xgqiHAeD/ZIb/FWXijC8hG5HaHrJ5H4Na
MJ32hE+GLZ4CMz2F+RXmp1f2SpN2vH6/TvR0tSRyCuFyQwTtFyGdbZrOB/8Z2oWD
xXpGwIFMAhqOlMCitMcrq/mA6nzUz4dLZC45dOqSL6TWvd9+boECs/Fu5ykQU6RF
4O981aJUXQNCun9OgFTpW0nDAj57RnX4uz2EXpOnOfxpa9dAdk53hlOHDeCPG/NP
spSLn/wIwROsEOpDUVJFGwQiK3PW6UtOFO7GY3G1tZZJekNLLdH+P1oGQI7nDVzn
jZ+R/B2HxY4MFUuz/6DWJYq51ZVilv2agYEms/+04qYb5/gzcE+fIORhXj8xzMPg
aoyfOS3HP+uCUCqOukoU+VLVvSZWtrMFoPR+xHH5+ToHVbAC+Qk=
=TnR1
-----END PGP SIGNATURE-----

```
