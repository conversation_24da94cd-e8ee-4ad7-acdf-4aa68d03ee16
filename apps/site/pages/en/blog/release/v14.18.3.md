---
date: '2022-01-11T00:02:52.779Z'
category: release
title: Node v14.18.3 (LTS)
layout: blog-post
author: <PERSON>
---

### Notable changes

#### Improper handling of URI Subject Alternative Names (Medium)(CVE-2021-44531)

Accepting arbitrary Subject Alternative Name (SAN) types, unless a PKI is specifically defined to use a particular SAN type, can result in bypassing name-constrained intermediates. Node.js was accepting URI SAN types, which PKIs are often not defined to use. Additionally, when a protocol allows URI SANs, Node.js did not match the URI correctly.

Versions of Node.js with the fix for this disable the URI SAN type when checking a certificate against a hostname. This behavior can be reverted through the `--security-revert` command-line option.

More details will be available at [CVE-2021-44531](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2021-44531) after publication.

#### Certificate Verification Bypass via String Injection (Medium)(CVE-2021-44532)

Node.js converts SANs (Subject Alternative Names) to a string format. It uses this string to check peer certificates against hostnames when validating connections. The string format was subject to an injection vulnerability when name constraints were used within a certificate chain, allowing the bypass of these name constraints.

Versions of Node.js with the fix for this escape SANs containing the problematic characters in order to prevent the injection. This behavior can be reverted through the `--security-revert` command-line option.

More details will be available at [CVE-2021-44532](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2021-44532) after publication.

#### Incorrect handling of certificate subject and issuer fields (Medium)(CVE-2021-44533)

Node.js did not handle multi-value Relative Distinguished Names correctly. Attackers could craft certificate subjects containing a single-value Relative Distinguished Name that would be interpreted as a multi-value Relative Distinguished Name, for example, in order to inject a Common Name that would allow bypassing the certificate subject verification.

Affected versions of Node.js do not accept multi-value Relative Distinguished Names and are thus not vulnerable to such attacks themselves. However, third-party code that uses node's ambiguous presentation of certificate subjects may be vulnerable.

More details will be available at [CVE-2021-44533](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2021-44533) after publication.

#### Prototype pollution via `console.table` properties (Low)(CVE-2022-21824)

Due to the formatting logic of the `console.table()` function it was not safe to allow user controlled input to be passed to the `properties` parameter while simultaneously passing a plain object with at least one property as the first parameter, which could be `__proto__`. The prototype pollution has very limited control, in that it only allows an empty string to be assigned numerical keys of the object prototype.

Versions of Node.js with the fix for this use a null protoype for the object these properties are being assigned to.

More details will be available at [CVE-2022-21824](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2022-21824) after publication.

Thanks to Patrik Oldsberg (rugvip) for reporting this vulnerability.

### Commits

- \[[`e2a74f3c99`](https://github.com/nodejs/node/commit/e2a74f3c99)] - **console**: fix prototype pollution via console.table (Tobias Nießen) [nodejs-private/node-private#307](https://github.com/nodejs-private/node-private/pull/307)
- \[[`df1b2c33f6`](https://github.com/nodejs/node/commit/df1b2c33f6)] - **crypto,tls**: implement safe x509 GeneralName format (Tobias Nießen and Akshay Kumar) [nodejs-private/node-private#300](https://github.com/nodejs-private/node-private/pull/300)
- \[[`9f2c52617f`](https://github.com/nodejs/node/commit/9f2c52617f)] - **src**: add cve reverts and associated tests (Michael Dawson and Akshay Kumar) [nodejs-private/node-private#300](https://github.com/nodejs-private/node-private/pull/300)
- \[[`b14be42518`](https://github.com/nodejs/node/commit/b14be42518)] - **src**: remove unused x509 functions (Tobias Nießen and Akshay Kumar) [nodejs-private/node-private#300](https://github.com/nodejs-private/node-private/pull/300)
- \[[`83d8f880bb`](https://github.com/nodejs/node/commit/83d8f880bb)] - **tls**: fix handling of x509 subject and issuer (Tobias Nießen and Akshay Kumar) [nodejs-private/node-private#300](https://github.com/nodejs-private/node-private/pull/300)
- \[[`461a0c674b`](https://github.com/nodejs/node/commit/461a0c674b)] - **tls**: drop support for URI alternative names (Tobias Nießen and Akshay Kumar) [nodejs-private/node-private#300](https://github.com/nodejs-private/node-private/pull/300)

Windows 32-bit Installer: https://nodejs.org/dist/v14.18.3/node-v14.18.3-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v14.18.3/node-v14.18.3-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v14.18.3/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v14.18.3/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v14.18.3/node-v14.18.3.pkg \
macOS Intel 64-bit Binary: https://nodejs.org/dist/v14.18.3/node-v14.18.3-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v14.18.3/node-v14.18.3-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v14.18.3/node-v14.18.3-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v14.18.3/node-v14.18.3-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v14.18.3/node-v14.18.3-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v14.18.3/node-v14.18.3-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v14.18.3/node-v14.18.3-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v14.18.3/node-v14.18.3.tar.gz \
Other release files: https://nodejs.org/dist/v14.18.3/ \
Documentation: https://nodejs.org/docs/v14.18.3/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

4c5ff6f0ee670e6175d4b0b19da4d238b7ccb0bc551e61b296f38da5ef3fee7c  node-v14.18.3-aix-ppc64.tar.gz
623579faa9faf1148e42c84e36c7b701ddded220d1795d94d93ed7561b699407  node-v14.18.3-darwin-x64.tar.gz
709ee1c4b06cb9b34160ae89ad59eaebc546c59946d44cec67e97b1837d2ecaf  node-v14.18.3-darwin-x64.tar.xz
b98a702bd7ebaf77617c9137f213332603dae92a76e6c78581f9bd686ec98c1d  node-v14.18.3-headers.tar.gz
a15830bf2d1b95dabe417b1fafb029a3d331c2dedb6ea7338716a4ba6f41c7dd  node-v14.18.3-headers.tar.xz
2d071ca1bc1d0ea1eb259e79b81ebb4387237b2f77b3cf616806534e0030eaa8  node-v14.18.3-linux-arm64.tar.gz
ded5c233cc107c322f3a21935d53945880adfb5d7a0994fc94ee51fdf2e5a805  node-v14.18.3-linux-arm64.tar.xz
6f19aa4d9c1b1706d44742218c8a7742d3fa62033d953156095bdde09f8375e5  node-v14.18.3-linux-armv7l.tar.gz
188ed614a38e08cbe0572d6433123be797f0e3b8942fef4cb2610aaea8bdd0f0  node-v14.18.3-linux-armv7l.tar.xz
9de9658eb9d24a40f6351ccbf8fe6ab5edfc00c59b9f259c17b9ef60dd03f3ee  node-v14.18.3-linux-ppc64le.tar.gz
ccbb5b81ceb1be63d0c9f54acaa21f4efca4159290cbd1ae01335b02e8726c46  node-v14.18.3-linux-ppc64le.tar.xz
827d36855502ae27e80c7c88093ba377120ce03ea4dd658b9e08cd88a871069d  node-v14.18.3-linux-s390x.tar.gz
d9abaec44a64dfda72144cad1a23948e9741b66804c2519828d384a59928e57f  node-v14.18.3-linux-s390x.tar.xz
bd96f88e054801d1368787f7eaf77b49cd052b9543c56bd6bc0bfc90310e2756  node-v14.18.3-linux-x64.tar.gz
57c36dc742cd09a31b93f3e89af0c780349f8256c117976f9309c6f8dfcca867  node-v14.18.3-linux-x64.tar.xz
58b59f2507abc755808783f1f4870706f7899203351d4c89df5419d7965abd54  node-v14.18.3.pkg
96d51324e4eb9dd88082a1effe328d272a6568121930e51ec089db1b966f891a  node-v14.18.3.tar.gz
783ac443cd343dd6c68d2abcf7e59e7b978a6a428f6a6025f9b84918b769d608  node-v14.18.3.tar.xz
456f3d26f900dbf0221b311884e81926b4aea96d75e57b30ab6cb142d05e10d9  node-v14.18.3-win-x64.7z
9fd6acd826c499353b186d784ba8de7aa14a5764648ffdb19dc7edf9f688e940  node-v14.18.3-win-x64.zip
a7ec2f7d8a5973869eebae31f24d859bfb363bb9df8ad19404ff28b84784c0e1  node-v14.18.3-win-x86.7z
39512195eb68641c944c822a1b47802e02dda26487baeb109478bda54b0528bc  node-v14.18.3-win-x86.zip
bafd47f936ea6622fa2200254eccb96dcd7e1cbbf7db391519816d185450fa8e  node-v14.18.3-x64.msi
046c792b3ad6b2ef775bcc50508e99ef2fdbee0669b59de55c96e6dfdd3218b1  node-v14.18.3-x86.msi
a9e4396b4fc6da28a0f4850640432f0a2fbb2fa53560ffdeed4df694e2512b43  win-x64/node.exe
4becb6fe4518994e913e1a1934d41118e6e574874523ae668f5d8613da8d7418  win-x64/node.lib
96bca1393b55c9775e42177b19632aa8e73b264beaeaeac7811f2459ba3e703b  win-x64/node_pdb.7z
397c7d7b8d827c1c2d7ded90f311f57e8502a276247458b1d2e32a09e2cca7fe  win-x64/node_pdb.zip
22ed9728ac5ff4a795b4fc7d4a89e19dc580075bab80e54016df220e63a3ad30  win-x86/node.exe
3b6af4276e4399d6c42efcfa44b6cf1eddae585f128c64b65896f81c8d773d38  win-x86/node.lib
6d22d735cc3e71ce9140810263926ff2f922b07746e15699da31ac5a01609884  win-x86/node_pdb.7z
e8c0dd6570c100e4764dff6b79b962842b01a4837f58064023a2ce5098e8ee11  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEyC+jrhy+3Gvka5NgxDzsRcF6uTwFAmHcvioACgkQxDzsRcF6
uTzi8w/+LgAEp/VoTvIqY5LdzH+1uyuERvVmbs/4uncIrIgq1YLLigPRbIYogwhU
WqYKsIVPyntw3MZdQM6jZo+WZYBxNRnWEVfSplKh0dRSEoQK7gFb5hE81Uv0O7PZ
2ynkomC06EBBq5+wF5X20n8I1VSFEjc5wDJQ+ecwzYnok90iA0mDt9upvXau4cBm
LZXjRsnzSTJ8zRNQyFs8kGwuqEpPg1WI6zS0+n61xPzL9F7E7UezhXBONe/SWDPL
ZZVmer7GgKnHf50ffqgzXQnXsOF9yNifvvo2Rp6sKOv1ygBu9Aq7ukWnwMeX10kf
eqRGjZR5al+jFTUoeLPKIpWFQe7/g6P3i0iTyYlUMNmAxEPOGt1rXVlkFBbKRRfp
nfX17cjVIdUDw/qehz9iK25WXKdGfHwi7jKh+D5X2vwQ1lqFTqXrOm2DatLChd7I
p7jsA9rie+jcwPzy+ifABpdwWsrHNQgNq3D0gWwqja6UBbkfOjAP+9Mrr8eiVmTB
egixZx4qGrxklyZHtvMZEW8mo+vqvKSS9wCkO295K1YMWhNfMjP8vzsiRD0m2n1C
eN7h/31Gm3EA7iayZaZ4UAzOwwZMqf/WBFvzwhb2jUlfBdm+HDlS6FKsOe9FLRbi
42gpojnNPi9e6IDDwIyTSeCeG8ZktYPxysL8/sZAfSyzS17rA58=
=0U5+
-----END PGP SIGNATURE-----

```
