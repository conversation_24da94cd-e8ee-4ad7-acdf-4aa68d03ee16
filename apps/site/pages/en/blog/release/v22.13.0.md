---
date: '2025-01-07T19:45:45.672Z'
category: release
title: Node v22.13.0 (LTS)
layout: blog-post
author: <PERSON><PERSON>
---

## 2025-01-07, Version 22.13.0 'Jod' (LTS), @ruyadorno

### Notable Changes

#### Stabilize Permission Model

Upgrades the Permission Model status from Active Development to Stable.

Contributed by <PERSON> [#56201](https://github.com/nodejs/node/pull/56201)

#### Graduate WebCryptoAPI Ed25519 and X25519 algorithms as stable

Following the merge of Curve25519 into the [Web Cryptography API Editor's Draft](https://w3c.github.io/webcrypto/) the `Ed25519` and `X25519` algorithm identifiers are now stable and will no longer emit an ExperimentalWarning upon use.

Contributed by (<PERSON><PERSON>) [#56142](https://github.com/nodejs/node/pull/56142)

#### Other Notable Changes

- \[[`05d6227a88`](https://github.com/nodejs/node/commit/05d6227a88)] - **(SEMVER-MINOR)** **assert**: add partialDeepStrictEqual (<PERSON>ucci) [#54630](https://github.com/nodejs/node/pull/54630)
- \[[`a933103499`](https://github.com/nodejs/node/commit/a933103499)] - **(SEMVER-MINOR)** **cli**: implement --trace-env and --trace-env-\[js|native]-stack (Joyee Cheung) [#55604](https://github.com/nodejs/node/pull/55604)
- \[[`ba9d5397de`](https://github.com/nodejs/node/commit/ba9d5397de)] - **(SEMVER-MINOR)** **dgram**: support blocklist in udp (theanarkh) [#56087](https://github.com/nodejs/node/pull/56087)
- \[[`f6d0c01303`](https://github.com/nodejs/node/commit/f6d0c01303)] - **doc**: stabilize util.styleText (Rafael Gonzaga) [#56265](https://github.com/nodejs/node/pull/56265)
- \[[`34c68827af`](https://github.com/nodejs/node/commit/34c68827af)] - **doc**: move typescript support to active development (Marco Ippolito) [#55536](https://github.com/nodejs/node/pull/55536)
- \[[`dd14b80350`](https://github.com/nodejs/node/commit/dd14b80350)] - **doc**: add LJHarb to collaborators (Jordan Harband) [#56132](https://github.com/nodejs/node/pull/56132)
- \[[`5263086169`](https://github.com/nodejs/node/commit/5263086169)] - **(SEMVER-MINOR)** **doc**: add report version and history section (Chengzhong Wu) [#56130](https://github.com/nodejs/node/pull/56130)
- \[[`8cb3c2018d`](https://github.com/nodejs/node/commit/8cb3c2018d)] - **(SEMVER-MINOR)** **doc**: sort --report-exclude alphabetically (Rafael Gonzaga) [#55788](https://github.com/nodejs/node/pull/55788)
- \[[`55239a48b6`](https://github.com/nodejs/node/commit/55239a48b6)] - **(SEMVER-MINOR)** **doc,lib,src,test**: unflag sqlite module (Colin Ihrig) [#55890](https://github.com/nodejs/node/pull/55890)
- \[[`7cbe3de1d8`](https://github.com/nodejs/node/commit/7cbe3de1d8)] - **(SEMVER-MINOR)** **module**: only emit require(esm) warning under --trace-require-module (Joyee Cheung) [#56194](https://github.com/nodejs/node/pull/56194)
- \[[`6575b76042`](https://github.com/nodejs/node/commit/6575b76042)] - **(SEMVER-MINOR)** **module**: add module.stripTypeScriptTypes (Marco Ippolito) [#55282](https://github.com/nodejs/node/pull/55282)
- \[[`bacfe6d5c9`](https://github.com/nodejs/node/commit/bacfe6d5c9)] - **(SEMVER-MINOR)** **net**: support blocklist in net.connect (theanarkh) [#56075](https://github.com/nodejs/node/pull/56075)
- \[[`b47888d390`](https://github.com/nodejs/node/commit/b47888d390)] - **(SEMVER-MINOR)** **net**: support blocklist for net.Server (theanarkh) [#56079](https://github.com/nodejs/node/pull/56079)
- \[[`566f0a1d25`](https://github.com/nodejs/node/commit/566f0a1d25)] - **(SEMVER-MINOR)** **net**: add SocketAddress.parse (James M Snell) [#56076](https://github.com/nodejs/node/pull/56076)
- \[[`ed7eab1421`](https://github.com/nodejs/node/commit/ed7eab1421)] - **(SEMVER-MINOR)** **net**: add net.BlockList.isBlockList(value) (James M Snell) [#56078](https://github.com/nodejs/node/pull/56078)
- \[[`ea4891856d`](https://github.com/nodejs/node/commit/ea4891856d)] - **(SEMVER-MINOR)** **process**: deprecate `features.{ipv6,uv}` and `features.tls_*` (René) [#55545](https://github.com/nodejs/node/pull/55545)
- \[[`01eb308f26`](https://github.com/nodejs/node/commit/01eb308f26)] - **(SEMVER-MINOR)** **report**: fix typos in report keys and bump the version (Yuan-Ming Hsu) [#56068](https://github.com/nodejs/node/pull/56068)
- \[[`97c38352d0`](https://github.com/nodejs/node/commit/97c38352d0)] - **(SEMVER-MINOR)** **sqlite**: aggregate constants in a single property (Edigleysson Silva (Edy)) [#56213](https://github.com/nodejs/node/pull/56213)
- \[[`b4041e554a`](https://github.com/nodejs/node/commit/b4041e554a)] - **(SEMVER-MINOR)** **sqlite**: add `StatementSync.prototype.iterate` method (tpoisseau) [#54213](https://github.com/nodejs/node/pull/54213)
- \[[`2e3ca1bbdd`](https://github.com/nodejs/node/commit/2e3ca1bbdd)] - **(SEMVER-MINOR)** **src**: add cli option to preserve env vars on diagnostic reports (Rafael Gonzaga) [#55697](https://github.com/nodejs/node/pull/55697)
- \[[`bcfe9c80fc`](https://github.com/nodejs/node/commit/bcfe9c80fc)] - **(SEMVER-MINOR)** **util**: add sourcemap support to getCallSites (Marco Ippolito) [#55589](https://github.com/nodejs/node/pull/55589)

### Commits

- \[[`e9024779c0`](https://github.com/nodejs/node/commit/e9024779c0)] - **assert**: make Maps be partially compared in partialDeepStrictEqual (Giovanni Bucci) [#56195](https://github.com/nodejs/node/pull/56195)
- \[[`4c13d8e587`](https://github.com/nodejs/node/commit/4c13d8e587)] - **assert**: make partialDeepStrictEqual work with ArrayBuffers (Giovanni Bucci) [#56098](https://github.com/nodejs/node/pull/56098)
- \[[`a4fa31a86e`](https://github.com/nodejs/node/commit/a4fa31a86e)] - **assert**: optimize partial comparison of two `Set`s (Antoine du Hamel) [#55970](https://github.com/nodejs/node/pull/55970)
- \[[`05d6227a88`](https://github.com/nodejs/node/commit/05d6227a88)] - **(SEMVER-MINOR)** **assert**: add partialDeepStrictEqual (Giovanni Bucci) [#54630](https://github.com/nodejs/node/pull/54630)
- \[[`5e1321abd7`](https://github.com/nodejs/node/commit/5e1321abd7)] - **buffer**: document concat zero-fill (Duncan) [#55562](https://github.com/nodejs/node/pull/55562)
- \[[`be5ba7c648`](https://github.com/nodejs/node/commit/be5ba7c648)] - **build**: set DESTCPU correctly for 'make binary' on loongarch64 (吴小白) [#56271](https://github.com/nodejs/node/pull/56271)
- \[[`38cf37ee2d`](https://github.com/nodejs/node/commit/38cf37ee2d)] - **build**: fix missing fp16 dependency in d8 builds (Joyee Cheung) [#56266](https://github.com/nodejs/node/pull/56266)
- \[[`dbb7557455`](https://github.com/nodejs/node/commit/dbb7557455)] - **build**: add major release action (Rafael Gonzaga) [#56199](https://github.com/nodejs/node/pull/56199)
- \[[`27cc90f3be`](https://github.com/nodejs/node/commit/27cc90f3be)] - **build**: fix C string encoding for `PRODUCT_DIR_ABS` (Anna Henningsen) [#56111](https://github.com/nodejs/node/pull/56111)
- \[[`376561c2b4`](https://github.com/nodejs/node/commit/376561c2b4)] - **build**: use variable for simdutf path (Shelley Vohr) [#56196](https://github.com/nodejs/node/pull/56196)
- \[[`126ae15000`](https://github.com/nodejs/node/commit/126ae15000)] - **build**: allow overriding clang usage (Shelley Vohr) [#56016](https://github.com/nodejs/node/pull/56016)
- \[[`97bb8f7c76`](https://github.com/nodejs/node/commit/97bb8f7c76)] - **build**: remove defaults for create-release-proposal (Rafael Gonzaga) [#56042](https://github.com/nodejs/node/pull/56042)
- \[[`a8fb1a06f3`](https://github.com/nodejs/node/commit/a8fb1a06f3)] - **build**: set node_arch to target_cpu in GN (Shelley Vohr) [#55967](https://github.com/nodejs/node/pull/55967)
- \[[`9f48ca27f1`](https://github.com/nodejs/node/commit/9f48ca27f1)] - **build**: use variable for crypto dep path (Shelley Vohr) [#55928](https://github.com/nodejs/node/pull/55928)
- \[[`e47ccd2287`](https://github.com/nodejs/node/commit/e47ccd2287)] - **build**: fix GN build for sqlite (Cheng) [#55912](https://github.com/nodejs/node/pull/55912)
- \[[`8d70b99a5a`](https://github.com/nodejs/node/commit/8d70b99a5a)] - **build**: compile bundled simdutf conditionally (Jakub Jirutka) [#55886](https://github.com/nodejs/node/pull/55886)
- \[[`826fd35242`](https://github.com/nodejs/node/commit/826fd35242)] - **build**: compile bundled simdjson conditionally (Jakub Jirutka) [#55886](https://github.com/nodejs/node/pull/55886)
- \[[`1015b22085`](https://github.com/nodejs/node/commit/1015b22085)] - **build**: compile bundled ada conditionally (Jakub Jirutka) [#55886](https://github.com/nodejs/node/pull/55886)
- \[[`77e2869ca6`](https://github.com/nodejs/node/commit/77e2869ca6)] - **build**: use glob for dependencies of out/Makefile (Richard Lau) [#55789](https://github.com/nodejs/node/pull/55789)
- \[[`a933103499`](https://github.com/nodejs/node/commit/a933103499)] - **(SEMVER-MINOR)** **cli**: implement --trace-env and --trace-env-\[js|native]-stack (Joyee Cheung) [#55604](https://github.com/nodejs/node/pull/55604)
- \[[`72e8e0684e`](https://github.com/nodejs/node/commit/72e8e0684e)] - **crypto**: graduate WebCryptoAPI Ed25519 and X25519 algorithms as stable (Filip Skokan) [#56142](https://github.com/nodejs/node/pull/56142)
- \[[`fe2b344ddb`](https://github.com/nodejs/node/commit/fe2b344ddb)] - **crypto**: ensure CryptoKey usages and algorithm are cached objects (Filip Skokan) [#56108](https://github.com/nodejs/node/pull/56108)
- \[[`9ee9f524a7`](https://github.com/nodejs/node/commit/9ee9f524a7)] - **crypto**: allow non-multiple of 8 in SubtleCrypto.deriveBits (Filip Skokan) [#55296](https://github.com/nodejs/node/pull/55296)
- \[[`76f242d993`](https://github.com/nodejs/node/commit/76f242d993)] - **deps**: update nghttp3 to 1.6.0 (Node.js GitHub Bot) [#56258](https://github.com/nodejs/node/pull/56258)
- \[[`c7ff2ea6b5`](https://github.com/nodejs/node/commit/c7ff2ea6b5)] - **deps**: update simdutf to 5.6.4 (Node.js GitHub Bot) [#56255](https://github.com/nodejs/node/pull/56255)
- \[[`04230be1ef`](https://github.com/nodejs/node/commit/04230be1ef)] - **deps**: update libuv to 1.49.2 (Luigi Pinca) [#56224](https://github.com/nodejs/node/pull/56224)
- \[[`88589b85b7`](https://github.com/nodejs/node/commit/88589b85b7)] - **deps**: update c-ares to v1.34.4 (Node.js GitHub Bot) [#56256](https://github.com/nodejs/node/pull/56256)
- \[[`5c2e0618f3`](https://github.com/nodejs/node/commit/5c2e0618f3)] - **deps**: define V8_PRESERVE_MOST as no-op on Windows (Stefan Stojanovic) [#56238](https://github.com/nodejs/node/pull/56238)
- \[[`9f8f3c9658`](https://github.com/nodejs/node/commit/9f8f3c9658)] - **deps**: update sqlite to 3.47.2 (Node.js GitHub Bot) [#56178](https://github.com/nodejs/node/pull/56178)
- \[[`17b6931d3b`](https://github.com/nodejs/node/commit/17b6931d3b)] - **deps**: update ngtcp2 to 1.9.1 (Node.js GitHub Bot) [#56095](https://github.com/nodejs/node/pull/56095)
- \[[`22b453b619`](https://github.com/nodejs/node/commit/22b453b619)] - **deps**: upgrade npm to 10.9.2 (npm team) [#56135](https://github.com/nodejs/node/pull/56135)
- \[[`d7eb41b382`](https://github.com/nodejs/node/commit/d7eb41b382)] - **deps**: update sqlite to 3.47.1 (Node.js GitHub Bot) [#56094](https://github.com/nodejs/node/pull/56094)
- \[[`669c722aa9`](https://github.com/nodejs/node/commit/669c722aa9)] - **deps**: update zlib to 1.3.0.1-motley-82a5fec (Node.js GitHub Bot) [#55980](https://github.com/nodejs/node/pull/55980)
- \[[`f61a0454d2`](https://github.com/nodejs/node/commit/f61a0454d2)] - **deps**: update corepack to 0.30.0 (Node.js GitHub Bot) [#55977](https://github.com/nodejs/node/pull/55977)
- \[[`d98bf0b891`](https://github.com/nodejs/node/commit/d98bf0b891)] - **deps**: update ngtcp2 to 1.9.0 (Node.js GitHub Bot) [#55975](https://github.com/nodejs/node/pull/55975)
- \[[`fc362624bf`](https://github.com/nodejs/node/commit/fc362624bf)] - **deps**: update simdutf to 5.6.3 (Node.js GitHub Bot) [#55973](https://github.com/nodejs/node/pull/55973)
- \[[`f61dcc4df4`](https://github.com/nodejs/node/commit/f61dcc4df4)] - **deps**: upgrade npm to 10.9.1 (npm team) [#55951](https://github.com/nodejs/node/pull/55951)
- \[[`bfe7982491`](https://github.com/nodejs/node/commit/bfe7982491)] - **deps**: update zlib to 1.3.0.1-motley-7e2e4d7 (Node.js GitHub Bot) [#54432](https://github.com/nodejs/node/pull/54432)
- \[[`d714367ef8`](https://github.com/nodejs/node/commit/d714367ef8)] - **deps**: update simdjson to 3.10.1 (Node.js GitHub Bot) [#54678](https://github.com/nodejs/node/pull/54678)
- \[[`ccc9b105ec`](https://github.com/nodejs/node/commit/ccc9b105ec)] - **deps**: update simdutf to 5.6.2 (Node.js GitHub Bot) [#55889](https://github.com/nodejs/node/pull/55889)
- \[[`ba9d5397de`](https://github.com/nodejs/node/commit/ba9d5397de)] - **(SEMVER-MINOR)** **dgram**: support blocklist in udp (theanarkh) [#56087](https://github.com/nodejs/node/pull/56087)
- \[[`7ddbf94849`](https://github.com/nodejs/node/commit/7ddbf94849)] - **dgram**: check udp buffer size to avoid fd leak (theanarkh) [#56084](https://github.com/nodejs/node/pull/56084)
- \[[`360d68de0f`](https://github.com/nodejs/node/commit/360d68de0f)] - **doc**: fix color contrast issue in light mode (Rich Trott) [#56272](https://github.com/nodejs/node/pull/56272)
- \[[`f6d0c01303`](https://github.com/nodejs/node/commit/f6d0c01303)] - **doc**: stabilize util.styleText (Rafael Gonzaga) [#56265](https://github.com/nodejs/node/pull/56265)
- \[[`9436c3c949`](https://github.com/nodejs/node/commit/9436c3c949)] - **doc**: clarify util.aborted resource usage (Kunal Kumar) [#55780](https://github.com/nodejs/node/pull/55780)
- \[[`b1cec2cef9`](https://github.com/nodejs/node/commit/b1cec2cef9)] - **doc**: add esm examples to node:repl (Alfredo González) [#55432](https://github.com/nodejs/node/pull/55432)
- \[[`d6a84cf781`](https://github.com/nodejs/node/commit/d6a84cf781)] - **doc**: add esm examples to node:readline (Alfredo González) [#55335](https://github.com/nodejs/node/pull/55335)
- \[[`a11ac1c0f2`](https://github.com/nodejs/node/commit/a11ac1c0f2)] - **doc**: fix 'which' to 'that' and add commas (Selveter Senitro) [#56216](https://github.com/nodejs/node/pull/56216)
- \[[`5331df7911`](https://github.com/nodejs/node/commit/5331df7911)] - **doc**: fix winget config path (Alex Yang) [#56233](https://github.com/nodejs/node/pull/56233)
- \[[`7a8071b43c`](https://github.com/nodejs/node/commit/7a8071b43c)] - **doc**: add esm examples to node:tls (Alfredo González) [#56229](https://github.com/nodejs/node/pull/56229)
- \[[`7d8c1e72d5`](https://github.com/nodejs/node/commit/7d8c1e72d5)] - **doc**: add esm examples to node:perf_hooks (Alfredo González) [#55257](https://github.com/nodejs/node/pull/55257)
- \[[`ea53c4b1ae`](https://github.com/nodejs/node/commit/ea53c4b1ae)] - **doc**: `sea.getRawAsset(key)` always returns an ArrayBuffer (沈鸿飞) [#56206](https://github.com/nodejs/node/pull/56206)
- \[[`7a94100a3e`](https://github.com/nodejs/node/commit/7a94100a3e)] - **doc**: update announce documentation for releases (Rafael Gonzaga) [#56200](https://github.com/nodejs/node/pull/56200)
- \[[`44c4e57e32`](https://github.com/nodejs/node/commit/44c4e57e32)] - **doc**: update blog link to /vulnerability (Rafael Gonzaga) [#56198](https://github.com/nodejs/node/pull/56198)
- \[[`5e5b4b0cbd`](https://github.com/nodejs/node/commit/5e5b4b0cbd)] - **doc**: call out import.meta is only supported in ES modules (Anton Kastritskii) [#56186](https://github.com/nodejs/node/pull/56186)
- \[[`a83de32d35`](https://github.com/nodejs/node/commit/a83de32d35)] - **doc**: add ambassador message - benefits of Node.js (Michael Dawson) [#56085](https://github.com/nodejs/node/pull/56085)
- \[[`bb880dd21a`](https://github.com/nodejs/node/commit/bb880dd21a)] - **doc**: fix incorrect link to style guide (Yuan-Ming Hsu) [#56181](https://github.com/nodejs/node/pull/56181)
- \[[`39ce902e58`](https://github.com/nodejs/node/commit/39ce902e58)] - **doc**: fix c++ addon hello world sample (Edigleysson Silva (Edy)) [#56172](https://github.com/nodejs/node/pull/56172)
- \[[`19c72c4acc`](https://github.com/nodejs/node/commit/19c72c4acc)] - **doc**: update blog release-post link (Ruy Adorno) [#56123](https://github.com/nodejs/node/pull/56123)
- \[[`b667cc4669`](https://github.com/nodejs/node/commit/b667cc4669)] - **doc**: fix module.md headings (Chengzhong Wu) [#56131](https://github.com/nodejs/node/pull/56131)
- \[[`34c68827af`](https://github.com/nodejs/node/commit/34c68827af)] - **doc**: move typescript support to active development (Marco Ippolito) [#55536](https://github.com/nodejs/node/pull/55536)
- \[[`c4a97d810b`](https://github.com/nodejs/node/commit/c4a97d810b)] - **doc**: mention `-a` flag for the release script (Ruy Adorno) [#56124](https://github.com/nodejs/node/pull/56124)
- \[[`dd14b80350`](https://github.com/nodejs/node/commit/dd14b80350)] - **doc**: add LJHarb to collaborators (Jordan Harband) [#56132](https://github.com/nodejs/node/pull/56132)
- \[[`2feb0781ed`](https://github.com/nodejs/node/commit/2feb0781ed)] - **doc**: add create-release-action to process (Rafael Gonzaga) [#55993](https://github.com/nodejs/node/pull/55993)
- \[[`71f6263942`](https://github.com/nodejs/node/commit/71f6263942)] - **doc**: rename file to advocacy-ambassador-program.md (Tobias Nießen) [#56046](https://github.com/nodejs/node/pull/56046)
- \[[`8efa240500`](https://github.com/nodejs/node/commit/8efa240500)] - **doc**: remove unused import from sample code (Blended Bram) [#55570](https://github.com/nodejs/node/pull/55570)
- \[[`e64cef8bf4`](https://github.com/nodejs/node/commit/e64cef8bf4)] - **doc**: add FAQ to releases section (Rafael Gonzaga) [#55992](https://github.com/nodejs/node/pull/55992)
- \[[`4bb0f30f92`](https://github.com/nodejs/node/commit/4bb0f30f92)] - **doc**: move history entry to class description (Luigi Pinca) [#55991](https://github.com/nodejs/node/pull/55991)
- \[[`6d02bd6873`](https://github.com/nodejs/node/commit/6d02bd6873)] - **doc**: add history entry for textEncoder.encodeInto() (Luigi Pinca) [#55990](https://github.com/nodejs/node/pull/55990)
- \[[`e239382ed8`](https://github.com/nodejs/node/commit/e239382ed8)] - **doc**: improve GN build documentation a bit (Shelley Vohr) [#55968](https://github.com/nodejs/node/pull/55968)
- \[[`78b6aef6bc`](https://github.com/nodejs/node/commit/78b6aef6bc)] - **doc**: fix deprecation codes (Filip Skokan) [#56018](https://github.com/nodejs/node/pull/56018)
- \[[`474bf80a44`](https://github.com/nodejs/node/commit/474bf80a44)] - **doc**: remove confusing and outdated sentence (Luigi Pinca) [#55988](https://github.com/nodejs/node/pull/55988)
- \[[`57381076c5`](https://github.com/nodejs/node/commit/57381076c5)] - **doc**: deprecate passing invalid types in `fs.existsSync` (Carlos Espa) [#55892](https://github.com/nodejs/node/pull/55892)
- \[[`e529cf6b26`](https://github.com/nodejs/node/commit/e529cf6b26)] - **doc**: add doc for PerformanceObserver.takeRecords() (skyclouds2001) [#55786](https://github.com/nodejs/node/pull/55786)
- \[[`a6ef0f6f6e`](https://github.com/nodejs/node/commit/a6ef0f6f6e)] - **doc**: add vetted courses to the ambassador benefits (Matteo Collina) [#55934](https://github.com/nodejs/node/pull/55934)
- \[[`63526049f2`](https://github.com/nodejs/node/commit/63526049f2)] - **doc**: order `node:crypto` APIs alphabetically (Julian Gassner) [#55831](https://github.com/nodejs/node/pull/55831)
- \[[`36080b7b61`](https://github.com/nodejs/node/commit/36080b7b61)] - **doc**: doc how to add message for promotion (Michael Dawson) [#55843](https://github.com/nodejs/node/pull/55843)
- \[[`12b2ad4287`](https://github.com/nodejs/node/commit/12b2ad4287)] - **doc**: add esm example for zlib (Leonardo Peixoto) [#55946](https://github.com/nodejs/node/pull/55946)
- \[[`352daac296`](https://github.com/nodejs/node/commit/352daac296)] - **doc**: fix typo (Alex Yang) [#56125](https://github.com/nodejs/node/pull/56125)
- \[[`6e7e9a126d`](https://github.com/nodejs/node/commit/6e7e9a126d)] - **doc**: document approach for building wasm in deps (Michael Dawson) [#55940](https://github.com/nodejs/node/pull/55940)
- \[[`0b3ac05422`](https://github.com/nodejs/node/commit/0b3ac05422)] - **doc**: remove RedYetiDev from triagers team (Aviv Keller) [#55947](https://github.com/nodejs/node/pull/55947)
- \[[`20be5e2f80`](https://github.com/nodejs/node/commit/20be5e2f80)] - **doc**: add esm examples to node:timers (Alfredo González) [#55857](https://github.com/nodejs/node/pull/55857)
- \[[`3ba9b57436`](https://github.com/nodejs/node/commit/3ba9b57436)] - **doc**: fix relative path mention in --allow-fs (Rafael Gonzaga) [#55791](https://github.com/nodejs/node/pull/55791)
- \[[`3e6b3a9a8b`](https://github.com/nodejs/node/commit/3e6b3a9a8b)] - **doc**: include git node release --promote to steps (Rafael Gonzaga) [#55835](https://github.com/nodejs/node/pull/55835)
- \[[`5bdfde8dc6`](https://github.com/nodejs/node/commit/5bdfde8dc6)] - **doc**: add history entry for import assertion removal (Antoine du Hamel) [#55883](https://github.com/nodejs/node/pull/55883)
- \[[`c842146c05`](https://github.com/nodejs/node/commit/c842146c05)] - **doc**: add a note on console stream behavior (Gireesh Punathil) [#55616](https://github.com/nodejs/node/pull/55616)
- \[[`5263086169`](https://github.com/nodejs/node/commit/5263086169)] - **(SEMVER-MINOR)** **doc**: add report version and history section (Chengzhong Wu) [#56130](https://github.com/nodejs/node/pull/56130)
- \[[`8cb3c2018d`](https://github.com/nodejs/node/commit/8cb3c2018d)] - **(SEMVER-MINOR)** **doc**: sort --report-exclude alphabetically (Rafael Gonzaga) [#55788](https://github.com/nodejs/node/pull/55788)
- \[[`55239a48b6`](https://github.com/nodejs/node/commit/55239a48b6)] - **(SEMVER-MINOR)** **doc,lib,src,test**: unflag sqlite module (Colin Ihrig) [#55890](https://github.com/nodejs/node/pull/55890)
- \[[`04d7c7a349`](https://github.com/nodejs/node/commit/04d7c7a349)] - **fs**: make mutating `options` in Callback `readdir()` not affect results (LiviaMedeiros) [#56057](https://github.com/nodejs/node/pull/56057)
- \[[`92bcd528e7`](https://github.com/nodejs/node/commit/92bcd528e7)] - **fs**: make mutating `options` in Promises `readdir()` not affect results (LiviaMedeiros) [#56057](https://github.com/nodejs/node/pull/56057)
- \[[`3a55bd9448`](https://github.com/nodejs/node/commit/3a55bd9448)] - **fs**: lazily load ReadFileContext (Gürgün Dayıoğlu) [#55998](https://github.com/nodejs/node/pull/55998)
- \[[`0331b3fdd3`](https://github.com/nodejs/node/commit/0331b3fdd3)] - **fs,win**: fix readdir for named pipe (Hüseyin Açacak) [#56110](https://github.com/nodejs/node/pull/56110)
- \[[`79152b54e9`](https://github.com/nodejs/node/commit/79152b54e9)] - **http**: add setDefaultHeaders option to http.request (Tim Perry) [#56112](https://github.com/nodejs/node/pull/56112)
- \[[`19782855a8`](https://github.com/nodejs/node/commit/19782855a8)] - **http**: don't emit error after destroy (Robert Nagy) [#55457](https://github.com/nodejs/node/pull/55457)
- \[[`8494512c17`](https://github.com/nodejs/node/commit/8494512c17)] - **http2**: remove duplicate codeblock (Vitaly Aminev) [#55915](https://github.com/nodejs/node/pull/55915)
- \[[`d2f82223d1`](https://github.com/nodejs/node/commit/d2f82223d1)] - **http2**: support ALPNCallback option (ZYSzys) [#56187](https://github.com/nodejs/node/pull/56187)
- \[[`2616f1247a`](https://github.com/nodejs/node/commit/2616f1247a)] - **http2**: fix memory leak caused by premature listener removing (ywave620) [#55966](https://github.com/nodejs/node/pull/55966)
- \[[`598fe048f2`](https://github.com/nodejs/node/commit/598fe048f2)] - **lib**: remove redundant global regexps (Gürgün Dayıoğlu) [#56182](https://github.com/nodejs/node/pull/56182)
- \[[`a3c8739530`](https://github.com/nodejs/node/commit/a3c8739530)] - **lib**: clean up persisted signals when they are settled (Edigleysson Silva (Edy)) [#56001](https://github.com/nodejs/node/pull/56001)
- \[[`11144ab158`](https://github.com/nodejs/node/commit/11144ab158)] - **lib**: handle Float16Array in node:v8 serdes (Bartek Iwańczuk) [#55996](https://github.com/nodejs/node/pull/55996)
- \[[`81c94a32e4`](https://github.com/nodejs/node/commit/81c94a32e4)] - **lib**: disable default memory leak warning for AbortSignal (Lenz Weber-Tronic) [#55816](https://github.com/nodejs/node/pull/55816)
- \[[`68dda61420`](https://github.com/nodejs/node/commit/68dda61420)] - **lib**: add validation for options in compileFunction (Taejin Kim) [#56023](https://github.com/nodejs/node/pull/56023)
- \[[`d2007aec28`](https://github.com/nodejs/node/commit/d2007aec28)] - **lib**: fix `fs.readdir` recursive async (Rafael Gonzaga) [#56041](https://github.com/nodejs/node/pull/56041)
- \[[`0571d5556f`](https://github.com/nodejs/node/commit/0571d5556f)] - **lib**: avoid excluding symlinks in recursive fs.readdir with filetypes (Juan José) [#55714](https://github.com/nodejs/node/pull/55714)
- \[[`843943d0ce`](https://github.com/nodejs/node/commit/843943d0ce)] - **meta**: bump github/codeql-action from 3.27.0 to 3.27.5 (dependabot\[bot]) [#56103](https://github.com/nodejs/node/pull/56103)
- \[[`1529027f03`](https://github.com/nodejs/node/commit/1529027f03)] - **meta**: bump actions/checkout from 4.1.7 to 4.2.2 (dependabot\[bot]) [#56102](https://github.com/nodejs/node/pull/56102)
- \[[`8e265de9f5`](https://github.com/nodejs/node/commit/8e265de9f5)] - **meta**: bump step-security/harden-runner from 2.10.1 to 2.10.2 (dependabot\[bot]) [#56101](https://github.com/nodejs/node/pull/56101)
- \[[`0fba3a3b9b`](https://github.com/nodejs/node/commit/0fba3a3b9b)] - **meta**: bump actions/setup-node from 4.0.3 to 4.1.0 (dependabot\[bot]) [#56100](https://github.com/nodejs/node/pull/56100)
- \[[`2e3fdfdb19`](https://github.com/nodejs/node/commit/2e3fdfdb19)] - **meta**: add releasers as CODEOWNERS to proposal action (Rafael Gonzaga) [#56043](https://github.com/nodejs/node/pull/56043)
- \[[`7cbe3de1d8`](https://github.com/nodejs/node/commit/7cbe3de1d8)] - **(SEMVER-MINOR)** **module**: only emit require(esm) warning under --trace-require-module (Joyee Cheung) [#56194](https://github.com/nodejs/node/pull/56194)
- \[[`8a5429c9b3`](https://github.com/nodejs/node/commit/8a5429c9b3)] - **module**: prevent main thread exiting before esm worker ends (Shima Ryuhei) [#56183](https://github.com/nodejs/node/pull/56183)
- \[[`6575b76042`](https://github.com/nodejs/node/commit/6575b76042)] - **(SEMVER-MINOR)** **module**: add module.stripTypeScriptTypes (Marco Ippolito) [#55282](https://github.com/nodejs/node/pull/55282)
- \[[`0794861bc3`](https://github.com/nodejs/node/commit/0794861bc3)] - **module**: simplify ts under node_modules check (Marco Ippolito) [#55440](https://github.com/nodejs/node/pull/55440)
- \[[`28a11adf14`](https://github.com/nodejs/node/commit/28a11adf14)] - **module**: mark evaluation rejection in require(esm) as handled (Joyee Cheung) [#56122](https://github.com/nodejs/node/pull/56122)
- \[[`bacfe6d5c9`](https://github.com/nodejs/node/commit/bacfe6d5c9)] - **(SEMVER-MINOR)** **net**: support blocklist in net.connect (theanarkh) [#56075](https://github.com/nodejs/node/pull/56075)
- \[[`566f0a1d25`](https://github.com/nodejs/node/commit/566f0a1d25)] - **(SEMVER-MINOR)** **net**: add SocketAddress.parse (James M Snell) [#56076](https://github.com/nodejs/node/pull/56076)
- \[[`ed7eab1421`](https://github.com/nodejs/node/commit/ed7eab1421)] - **(SEMVER-MINOR)** **net**: add net.BlockList.isBlockList(value) (James M Snell) [#56078](https://github.com/nodejs/node/pull/56078)
- \[[`b47888d390`](https://github.com/nodejs/node/commit/b47888d390)] - **(SEMVER-MINOR)** **net**: support blocklist for net.Server (theanarkh) [#56079](https://github.com/nodejs/node/pull/56079)
- \[[`481770a38f`](https://github.com/nodejs/node/commit/481770a38f)] - **node-api**: allow napi_delete_reference in finalizers (Chengzhong Wu) [#55620](https://github.com/nodejs/node/pull/55620)
- \[[`2beb4f1f8c`](https://github.com/nodejs/node/commit/2beb4f1f8c)] - **permission**: ignore internalModuleStat on module loading (Rafael Gonzaga) [#55797](https://github.com/nodejs/node/pull/55797)
- \[[`ea4891856d`](https://github.com/nodejs/node/commit/ea4891856d)] - **(SEMVER-MINOR)** **process**: deprecate `features.{ipv6,uv}` and `features.tls_*` (René) [#55545](https://github.com/nodejs/node/pull/55545)
- \[[`c907b2f358`](https://github.com/nodejs/node/commit/c907b2f358)] - **quic**: update more QUIC implementation (James M Snell) [#55986](https://github.com/nodejs/node/pull/55986)
- \[[`43c25e2e0d`](https://github.com/nodejs/node/commit/43c25e2e0d)] - **quic**: multiple updates to quic impl (James M Snell) [#55971](https://github.com/nodejs/node/pull/55971)
- \[[`01eb308f26`](https://github.com/nodejs/node/commit/01eb308f26)] - **(SEMVER-MINOR)** **report**: fix typos in report keys and bump the version (Yuan-Ming Hsu) [#56068](https://github.com/nodejs/node/pull/56068)
- \[[`1cfa31fb82`](https://github.com/nodejs/node/commit/1cfa31fb82)] - **sea**: only assert snapshot main function for main threads (Joyee Cheung) [#56120](https://github.com/nodejs/node/pull/56120)
- \[[`97c38352d0`](https://github.com/nodejs/node/commit/97c38352d0)] - **(SEMVER-MINOR)** **sqlite**: aggregate constants in a single property (Edigleysson Silva (Edy)) [#56213](https://github.com/nodejs/node/pull/56213)
- \[[`2268c1ea8b`](https://github.com/nodejs/node/commit/2268c1ea8b)] - **sqlite**: add support for custom functions (Colin Ihrig) [#55985](https://github.com/nodejs/node/pull/55985)
- \[[`f5c6955722`](https://github.com/nodejs/node/commit/f5c6955722)] - **sqlite**: support `db.loadExtension` (Alex Yang) [#53900](https://github.com/nodejs/node/pull/53900)
- \[[`9a60bea6b7`](https://github.com/nodejs/node/commit/9a60bea6b7)] - **sqlite**: deps include `sqlite3ext.h` (Alex Yang) [#56010](https://github.com/nodejs/node/pull/56010)
- \[[`b4041e554a`](https://github.com/nodejs/node/commit/b4041e554a)] - **(SEMVER-MINOR)** **sqlite**: add `StatementSync.prototype.iterate` method (tpoisseau) [#54213](https://github.com/nodejs/node/pull/54213)
- \[[`2889e8da04`](https://github.com/nodejs/node/commit/2889e8da04)] - **src**: fix outdated js2c.cc references (Chengzhong Wu) [#56133](https://github.com/nodejs/node/pull/56133)
- \[[`5ce020b0c9`](https://github.com/nodejs/node/commit/5ce020b0c9)] - **src**: use spaceship operator in SocketAddress (James M Snell) [#56059](https://github.com/nodejs/node/pull/56059)
- \[[`a32fa30847`](https://github.com/nodejs/node/commit/a32fa30847)] - **src**: add missing qualifiers to env.cc (Yagiz Nizipli) [#56062](https://github.com/nodejs/node/pull/56062)
- \[[`974b7b61ef`](https://github.com/nodejs/node/commit/974b7b61ef)] - **src**: use std::string_view for process emit fns (Yagiz Nizipli) [#56086](https://github.com/nodejs/node/pull/56086)
- \[[`4559fac862`](https://github.com/nodejs/node/commit/4559fac862)] - **src**: remove dead code in async_wrap (Gerhard Stöbich) [#56065](https://github.com/nodejs/node/pull/56065)
- \[[`e42e4b20be`](https://github.com/nodejs/node/commit/e42e4b20be)] - **src**: avoid copy on getV8FastApiCallCount (Yagiz Nizipli) [#56081](https://github.com/nodejs/node/pull/56081)
- \[[`c188660e8b`](https://github.com/nodejs/node/commit/c188660e8b)] - **src**: fix check fd (theanarkh) [#56000](https://github.com/nodejs/node/pull/56000)
- \[[`d894cb76ff`](https://github.com/nodejs/node/commit/d894cb76ff)] - **src**: safely remove the last line from dotenv (Shima Ryuhei) [#55982](https://github.com/nodejs/node/pull/55982)
- \[[`2ca9f4b65a`](https://github.com/nodejs/node/commit/2ca9f4b65a)] - **src**: fix kill signal on Windows (Hüseyin Açacak) [#55514](https://github.com/nodejs/node/pull/55514)
- \[[`2e3ca1bbdd`](https://github.com/nodejs/node/commit/2e3ca1bbdd)] - **(SEMVER-MINOR)** **src**: add cli option to preserve env vars on dr (Rafael Gonzaga) [#55697](https://github.com/nodejs/node/pull/55697)
- \[[`359fff1c4e`](https://github.com/nodejs/node/commit/359fff1c4e)] - **src,build**: add no user defined deduction guides of CTAD check (Chengzhong Wu) [#56071](https://github.com/nodejs/node/pull/56071)
- \[[`57bb983215`](https://github.com/nodejs/node/commit/57bb983215)] - **(SEMVER-MINOR)** **src,lib**: stabilize permission model (Rafael Gonzaga) [#56201](https://github.com/nodejs/node/pull/56201)
- \[[`d352b0465a`](https://github.com/nodejs/node/commit/d352b0465a)] - **stream**: commit pull-into descriptors after filling from queue (Mattias Buelens) [#56072](https://github.com/nodejs/node/pull/56072)
- \[[`eef9bd1bf6`](https://github.com/nodejs/node/commit/eef9bd1bf6)] - **test**: remove test-sqlite-statement-sync flaky designation (Luigi Pinca) [#56051](https://github.com/nodejs/node/pull/56051)
- \[[`8718135a5d`](https://github.com/nodejs/node/commit/8718135a5d)] - **test**: use --permission over --experimental-permission (Rafael Gonzaga) [#56239](https://github.com/nodejs/node/pull/56239)
- \[[`9c68d4f180`](https://github.com/nodejs/node/commit/9c68d4f180)] - **test**: remove exludes for sea tests on PPC (Michael Dawson) [#56217](https://github.com/nodejs/node/pull/56217)
- \[[`c5d0472968`](https://github.com/nodejs/node/commit/c5d0472968)] - **test**: fix test-abortsignal-drop-settled-signals flakiness (Edigleysson Silva (Edy)) [#56197](https://github.com/nodejs/node/pull/56197)
- \[[`4adf518689`](https://github.com/nodejs/node/commit/4adf518689)] - **test**: move localizationd data from `test-icu-env` to external file (Livia Medeiros) [#55618](https://github.com/nodejs/node/pull/55618)
- \[[`02383b4267`](https://github.com/nodejs/node/commit/02383b4267)] - **test**: update WPT for url to 6fa3fe8a92 (Node.js GitHub Bot) [#56136](https://github.com/nodejs/node/pull/56136)
- \[[`0e24eebf24`](https://github.com/nodejs/node/commit/0e24eebf24)] - **test**: remove `hasOpenSSL3x` utils (Antoine du Hamel) [#56164](https://github.com/nodejs/node/pull/56164)
- \[[`381e705385`](https://github.com/nodejs/node/commit/381e705385)] - **test**: update streams wpt (Mattias Buelens) [#56072](https://github.com/nodejs/node/pull/56072)
- \[[`ad107ca0d9`](https://github.com/nodejs/node/commit/ad107ca0d9)] - **test**: remove test-fs-utimes flaky designation (Luigi Pinca) [#56052](https://github.com/nodejs/node/pull/56052)
- \[[`e15c5dab79`](https://github.com/nodejs/node/commit/e15c5dab79)] - **test**: ensure `cli.md` is in alphabetical order (Antoine du Hamel) [#56025](https://github.com/nodejs/node/pull/56025)
- \[[`d0302e7d2d`](https://github.com/nodejs/node/commit/d0302e7d2d)] - **test**: update WPT for WebCryptoAPI to 3e3374efde (Node.js GitHub Bot) [#56093](https://github.com/nodejs/node/pull/56093)
- \[[`a0b1e8f400`](https://github.com/nodejs/node/commit/a0b1e8f400)] - **test**: update WPT for WebCryptoAPI to 76dfa54e5d (Node.js GitHub Bot) [#56093](https://github.com/nodejs/node/pull/56093)
- \[[`211f058a12`](https://github.com/nodejs/node/commit/211f058a12)] - **test**: move test-worker-arraybuffer-zerofill to parallel (Luigi Pinca) [#56053](https://github.com/nodejs/node/pull/56053)
- \[[`c52bc5d71c`](https://github.com/nodejs/node/commit/c52bc5d71c)] - **test**: update WPT for url to 67880a4eb83ca9aa732eec4b35a1971ff5bf37ff (Node.js GitHub Bot) [#55999](https://github.com/nodejs/node/pull/55999)
- \[[`1a78bde8d4`](https://github.com/nodejs/node/commit/1a78bde8d4)] - **test**: make HTTP/1.0 connection test more robust (Arne Keller) [#55959](https://github.com/nodejs/node/pull/55959)
- \[[`ff7b1445a0`](https://github.com/nodejs/node/commit/ff7b1445a0)] - **test**: convert readdir test to use test runner (Thomas Chetwin) [#55750](https://github.com/nodejs/node/pull/55750)
- \[[`b296b5a4e4`](https://github.com/nodejs/node/commit/b296b5a4e4)] - **test**: make x509 crypto tests work with BoringSSL (Shelley Vohr) [#55927](https://github.com/nodejs/node/pull/55927)
- \[[`97458ad74b`](https://github.com/nodejs/node/commit/97458ad74b)] - **test**: fix determining lower priority (Livia Medeiros) [#55908](https://github.com/nodejs/node/pull/55908)
- \[[`bb4aa7a296`](https://github.com/nodejs/node/commit/bb4aa7a296)] - **test,crypto**: update WebCryptoAPI WPT (Filip Skokan) [#55997](https://github.com/nodejs/node/pull/55997)
- \[[`fb98fa4967`](https://github.com/nodejs/node/commit/fb98fa4967)] - **test_runner**: refactor Promise chain in run() (Colin Ihrig) [#55958](https://github.com/nodejs/node/pull/55958)
- \[[`18c94961f8`](https://github.com/nodejs/node/commit/18c94961f8)] - **test_runner**: refactor build Promise in Suite() (Colin Ihrig) [#55958](https://github.com/nodejs/node/pull/55958)
- \[[`bf3967fd3a`](https://github.com/nodejs/node/commit/bf3967fd3a)] - **test_runner**: simplify hook running logic (Colin Ihrig) [#55963](https://github.com/nodejs/node/pull/55963)
- \[[`8c065dc61e`](https://github.com/nodejs/node/commit/8c065dc61e)] - **test_runner**: mark context.plan() as stable (Colin Ihrig) [#55895](https://github.com/nodejs/node/pull/55895)
- \[[`8ff082cf48`](https://github.com/nodejs/node/commit/8ff082cf48)] - **test_runner**: mark snapshot testing as stable (Colin Ihrig) [#55897](https://github.com/nodejs/node/pull/55897)
- \[[`7ae125cef4`](https://github.com/nodejs/node/commit/7ae125cef4)] - **tools**: fix `node:` enforcement for docs (Antoine du Hamel) [#56284](https://github.com/nodejs/node/pull/56284)
- \[[`0b489116a3`](https://github.com/nodejs/node/commit/0b489116a3)] - **tools**: update github_reporter to 1.7.2 (Node.js GitHub Bot) [#56205](https://github.com/nodejs/node/pull/56205)
- \[[`5306819fac`](https://github.com/nodejs/node/commit/5306819fac)] - **tools**: add REPLACEME check to workflow (Mert Can Altin) [#56251](https://github.com/nodejs/node/pull/56251)
- \[[`4e3cab44cb`](https://github.com/nodejs/node/commit/4e3cab44cb)] - **tools**: use `github.actor` instead of bot username for release proposals (Antoine du Hamel) [#56232](https://github.com/nodejs/node/pull/56232)
- \[[`3e8938463a`](https://github.com/nodejs/node/commit/3e8938463a)] - _**Revert**_ "**tools**: disable automated libuv updates" (Luigi Pinca) [#56223](https://github.com/nodejs/node/pull/56223)
- \[[`98ea499e36`](https://github.com/nodejs/node/commit/98ea499e36)] - **tools**: update gyp-next to 0.19.1 (Anna Henningsen) [#56111](https://github.com/nodejs/node/pull/56111)
- \[[`2e76cd2a8b`](https://github.com/nodejs/node/commit/2e76cd2a8b)] - **tools**: fix release proposal linter to support more than 1 folk preparing (Antoine du Hamel) [#56203](https://github.com/nodejs/node/pull/56203)
- \[[`9fa0e41665`](https://github.com/nodejs/node/commit/9fa0e41665)] - **tools**: enable linter for `tools/icu/**` (Livia Medeiros) [#56176](https://github.com/nodejs/node/pull/56176)
- \[[`d6e1efcc59`](https://github.com/nodejs/node/commit/d6e1efcc59)] - **tools**: use commit title as PR title when creating release proposal (Antoine du Hamel) [#56165](https://github.com/nodejs/node/pull/56165)
- \[[`a88e4ce55e`](https://github.com/nodejs/node/commit/a88e4ce55e)] - **tools**: update gyp-next to 0.19.0 (Node.js GitHub Bot) [#56158](https://github.com/nodejs/node/pull/56158)
- \[[`bd0760efbc`](https://github.com/nodejs/node/commit/bd0760efbc)] - **tools**: bump the eslint group in /tools/eslint with 4 updates (dependabot\[bot]) [#56099](https://github.com/nodejs/node/pull/56099)
- \[[`c5b1cf4b12`](https://github.com/nodejs/node/commit/c5b1cf4b12)] - **tools**: improve release proposal PR opening (Antoine du Hamel) [#56161](https://github.com/nodejs/node/pull/56161)
- \[[`12baefb13d`](https://github.com/nodejs/node/commit/12baefb13d)] - **tools**: update `create-release-proposal` workflow (Antoine du Hamel) [#56054](https://github.com/nodejs/node/pull/56054)
- \[[`e6e1495f1a`](https://github.com/nodejs/node/commit/e6e1495f1a)] - **tools**: fix update-undici script (Michaël Zasso) [#56069](https://github.com/nodejs/node/pull/56069)
- \[[`ed635c90da`](https://github.com/nodejs/node/commit/ed635c90da)] - **tools**: allow dispatch of `tools.yml` from forks (Antoine du Hamel) [#56008](https://github.com/nodejs/node/pull/56008)
- \[[`1e628d1f37`](https://github.com/nodejs/node/commit/1e628d1f37)] - **tools**: fix nghttp3 updater script (Antoine du Hamel) [#56007](https://github.com/nodejs/node/pull/56007)
- \[[`1af3599b7e`](https://github.com/nodejs/node/commit/1af3599b7e)] - **tools**: filter release keys to reduce interactivity (Antoine du Hamel) [#55950](https://github.com/nodejs/node/pull/55950)
- \[[`1893be4a9c`](https://github.com/nodejs/node/commit/1893be4a9c)] - **tools**: update WPT updater (Antoine du Hamel) [#56003](https://github.com/nodejs/node/pull/56003)
- \[[`f89bd2ba8a`](https://github.com/nodejs/node/commit/f89bd2ba8a)] - **tools**: add WPT updater for specific subsystems (Mert Can Altin) [#54460](https://github.com/nodejs/node/pull/54460)
- \[[`61901372d5`](https://github.com/nodejs/node/commit/61901372d5)] - **tools**: use tokenless Codecov uploads (Michaël Zasso) [#55943](https://github.com/nodejs/node/pull/55943)
- \[[`312bb4dff8`](https://github.com/nodejs/node/commit/312bb4dff8)] - **tools**: lint js in `doc/**/*.md` (Livia Medeiros) [#55904](https://github.com/nodejs/node/pull/55904)
- \[[`7b476f637c`](https://github.com/nodejs/node/commit/7b476f637c)] - **tools**: add linter for release commit proposals (Antoine du Hamel) [#55923](https://github.com/nodejs/node/pull/55923)
- \[[`22d7017191`](https://github.com/nodejs/node/commit/22d7017191)] - **tools**: fix riscv64 build failed (Lu Yahan) [#52888](https://github.com/nodejs/node/pull/52888)
- \[[`f4f777f4d2`](https://github.com/nodejs/node/commit/f4f777f4d2)] - **tools**: bump cross-spawn from 7.0.3 to 7.0.5 in /tools/eslint (dependabot\[bot]) [#55894](https://github.com/nodejs/node/pull/55894)
- \[[`a648e4c44a`](https://github.com/nodejs/node/commit/a648e4c44a)] - **util**: harden more built-in classes against prototype pollution (Antoine du Hamel) [#56225](https://github.com/nodejs/node/pull/56225)
- \[[`4a1b51b5a9`](https://github.com/nodejs/node/commit/4a1b51b5a9)] - **util**: fix Latin1 decoding to return string output (Mert Can Altin) [#56222](https://github.com/nodejs/node/pull/56222)
- \[[`9e98e86604`](https://github.com/nodejs/node/commit/9e98e86604)] - **util**: do not rely on mutable `Object` and `Function`' `constructor` prop (Antoine du Hamel) [#56188](https://github.com/nodejs/node/pull/56188)
- \[[`374eb415fd`](https://github.com/nodejs/node/commit/374eb415fd)] - **util**: add fast path for Latin1 decoding (Mert Can Altin) [#55275](https://github.com/nodejs/node/pull/55275)
- \[[`bcfe9c80fc`](https://github.com/nodejs/node/commit/bcfe9c80fc)] - **(SEMVER-MINOR)** **util**: add sourcemap support to getCallSites (Marco Ippolito) [#55589](https://github.com/nodejs/node/pull/55589)
- \[[`2aa77c8a8f`](https://github.com/nodejs/node/commit/2aa77c8a8f)] - **v8,tools**: expose experimental wasm revectorize feature (Yolanda-Chen) [#54896](https://github.com/nodejs/node/pull/54896)
- \[[`bfd11d7661`](https://github.com/nodejs/node/commit/bfd11d7661)] - **worker**: fix crash when a worker joins after exit (Stephen Belanger) [#56191](https://github.com/nodejs/node/pull/56191)

Windows 32-bit Installer: https://nodejs.org/dist/v22.13.0/node-v22.13.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v22.13.0/node-v22.13.0-x64.msi \
Windows ARM 64-bit Installer: https://nodejs.org/dist/v22.13.0/node-v22.13.0-arm64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v22.13.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v22.13.0/win-x64/node.exe \
Windows ARM 64-bit Binary: https://nodejs.org/dist/v22.13.0/win-arm64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v22.13.0/node-v22.13.0.pkg \
macOS Apple Silicon 64-bit Binary: https://nodejs.org/dist/v22.13.0/node-v22.13.0-darwin-arm64.tar.gz \
macOS Intel 64-bit Binary: https://nodejs.org/dist/v22.13.0/node-v22.13.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v22.13.0/node-v22.13.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v22.13.0/node-v22.13.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v22.13.0/node-v22.13.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v22.13.0/node-v22.13.0-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v22.13.0/node-v22.13.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v22.13.0/node-v22.13.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v22.13.0/node-v22.13.0.tar.gz \
Other release files: https://nodejs.org/dist/v22.13.0/ \
Documentation: https://nodejs.org/docs/v22.13.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

24d17c7f6b9b18fe225ef4b70f030f86bc9e268b47847e5b4d78f82a377274bc  node-v22.13.0-aix-ppc64.tar.gz
ffb356d8fc3ec6a3bb1ee9333af9de77380c8c34b2697092fda5f497b2c50863  node-v22.13.0-arm64.msi
bc1e374e7393e2f4b20e5bbc157d02e9b1fb2c634b2f992136b38fb8ca2023b7  node-v22.13.0-darwin-arm64.tar.gz
71b0893ef6a55295994f38002fada15c9a76a3cedeb36745fde0403741d183c6  node-v22.13.0-darwin-arm64.tar.xz
cfaaf5edde585a15547f858f5b3b62a292cf5929a23707b6f1e36c29a32487be  node-v22.13.0-darwin-x64.tar.gz
186d3fa8691e688be431de80af9f0f41f17f091790b324df9efd51578ac20aeb  node-v22.13.0-darwin-x64.tar.xz
e4a0ba46c194eb9390c2f2177f4a6ab4f43f4c4029f6b0b239bf5cdb701c307d  node-v22.13.0-headers.tar.gz
6e4f8721c8e9b669f5c91e806e6d7238c4f95db78198f66da0476f54fb9986d4  node-v22.13.0-headers.tar.xz
e0cc088cb4fb2e945d3d5c416c601e1101a15f73e0f024c9529b964d9f6dce5b  node-v22.13.0-linux-arm64.tar.gz
5de54a12983d0de6ba23597d4d0194e64933e26b0f04a469db3be9c3e18b6b2b  node-v22.13.0-linux-arm64.tar.xz
6d1b640276bafc1a409886390bae6d20e07f18dae6904b860127a402409621e0  node-v22.13.0-linux-armv7l.tar.gz
308687472523deee7abacc342e753e9b402ff4b178025374668039996b5c9699  node-v22.13.0-linux-armv7l.tar.xz
90b96eb76faf409bdac018b2f7c91343983201f518382ac7f538b7758325ba47  node-v22.13.0-linux-ppc64le.tar.gz
3b0e5b0c2953b57385b936b415a1e3cd03ff63ceca6b7badc40790f0d4e25759  node-v22.13.0-linux-ppc64le.tar.xz
bef2a7077a3a6aa66bb0292d1fbaea929471aabcb1937741c8db50d6372b8da4  node-v22.13.0-linux-s390x.tar.gz
f0a19c73fc8410c8ee649b1ec7a18916a9d555d237d03f7a932481c808e5ebc1  node-v22.13.0-linux-s390x.tar.xz
9a33e89093a0d946c54781dcb3ccab4ccf7538a7135286528ca41ca055e9b38f  node-v22.13.0-linux-x64.tar.gz
3ff0d57063c33313d73d0bdcebc4c778ad6be948234584694a042c6fe57164f6  node-v22.13.0-linux-x64.tar.xz
c56028a0df18075ba3302cf1cc75ce2dde39676b251ddc3e9524e61bf9de8a0b  node-v22.13.0.pkg
38e15a1d0dd0e95c237bc4651d7c9930a78d3fe7954a7ee7693b403f2d879e46  node-v22.13.0.tar.gz
e50db6730716ba2ae953cf99d10c80295bd33bb72d3c829d9e99f6af56d626c7  node-v22.13.0.tar.xz
5f441e2f3da291051971345e1f8bf16bdc1314985fb2dac806217463d99d176a  node-v22.13.0-win-arm64.7z
8ca2c90ae0373d69e13301293306c31ea9afca2780b8325b6ca059319479e560  node-v22.13.0-win-arm64.zip
a81087ab40a4336908b7cfd3473b9571549b045f38fcdb7181cca979ceadd018  node-v22.13.0-win-x64.7z
b0feb09ebf41328628e7383f7a092fb7342ce1e05c867a90cf8f1379205a8429  node-v22.13.0-win-x64.zip
e7325b3da93357965f756b5473bbf3e8dccaaf72f5607ac58e2aae5eedcc0d84  node-v22.13.0-win-x86.7z
84678ec9e0a1d52a040187fc50c1932cc17bda146fb27b24ae9d368c2212c795  node-v22.13.0-win-x86.zip
5c5a0c9a1d2ca9fd342691312d859d26c78b83538720938c243a3269ce64b7af  node-v22.13.0-x64.msi
8c96b25e8b397d31319b0c0eacc564fa86e1fdae1d08246c4810a2281329ba91  node-v22.13.0-x86.msi
903ca2cad56d9fe18b90659e037d84c7518f555b8796dcbcb8fb53e2d6eb7904  win-arm64/node.exe
988eb8c60a5ade17e652dbdb60d56d3c6ad5e599a99ce04932b8c4c86583cdaf  win-arm64/node.lib
10d6eef5c30032941e807b8a869cbf90956ced1a46c0e18832ce846ef0af1545  win-arm64/node_pdb.7z
d1f556c3292c9a84b318866a01d622b1a4dea064aa11cf215c4d630ca308724e  win-arm64/node_pdb.zip
364dbc8442f8d5c04fd4226bcfcf8e60d3268627eb1d7be214a91bb7d74cdbb9  win-x64/node.exe
65e45757c026c93a170743a811ef1b921ae12d6d9dd62d258bbbca0626687626  win-x64/node.lib
9d4e041682b08b22549dc6d54960e292962f553d51c4a5c9cac0030667bc3c7d  win-x64/node_pdb.7z
a6361221cd937c4a0a333cbf38529053ade535047cd2aab7a53c99de72faa0f1  win-x64/node_pdb.zip
e98ff9f9f847f1040aee0858de49c993df69679adcf552262042cb2cc26bc948  win-x86/node.exe
79bae10059e833ce7fa4de05e5601034461327e2e7cb75c2144b87d4ab5ac547  win-x86/node.lib
06064b3fb03e0af9410d1a94a958952e9de82951ea84d835b2a1eb3dbdf8d275  win-x86/node_pdb.7z
9d6fa1bff9bf6aca45eec7f2cf16b0c387e960ee625596459390ea6ca43b362d  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEEI9StI21e7DMQ5spl7AUGb2S+AoFAmd9g/YACgkQl7AUGb2S
+ApwHRAAxTs6XCEUzUDZX6uJi1FOydrKhvgh1LB4EyrBzE+DY0f6escaVK6PkUw1
R0YKsmlRgG6gn/eLXCfqcMFcl7q0jOr9V6651oCjABsTCMdMYfn281KueafD0UNz
WKYfP8ZMCjCL1FXlD99KGCfEjBGgHdlHf5etng2IfzTbHd7OyFEN8xOCvyoWIfCw
Nj8qiIK5E9LuDl9hwPOdxQLSkloQKVxnE5lfcUw/jpPFxeTMv8Nx4G5us4q9x7Mn
X5sCSYhX0Dlqidy55g2KWAM+Lv6l8fQKaVi/5PcafnjLgeJmKYfvFdK5MpmhYOeX
qq+S6BTqe7QUnLDVlWxHZJcVAtbSNEA4X1TZci44dNA5jn8AZxFWBJPhqV0UQ9ql
6OgI4jTJ5MODEtCaSi3fchQk7cUlyFRWRywqLkgc/jIydpxK2lptDVWPqjouboXj
hAWcM8QFjDimvDnGYUe81jMaJZsNR6BqTE5g2+CqL5mCcJiwwsC3N3axuFokOz0a
46Tp+mz2ys5SIVtX0yolBLBJGsPGOnxD2C2t4Um0DID+LyEajUafFpcOca9g7280
7QDKg/EbNW1XTU3k2rl1JzB6qIctkKxf9wTi4gSjp7uvCEcAEtxx93cHgi/FFu+U
wb8N1cpuHelq5y7Oe0kw7dQE4tLwOi1aVHLk4lcpeJ2ff3uZsfE=
=f9Gb
-----END PGP SIGNATURE-----
```
