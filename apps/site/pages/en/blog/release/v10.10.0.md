---
date: '2018-09-06T22:01:43.456Z'
category: release
title: Node v10.10.0 (Current)
layout: blog-post
author: <PERSON><PERSON><PERSON>
---

### Notable Changes

- **child_process**:
  - `TypedArray` and `DataView` values are now accepted as input by
    `execFileSync` and `spawnSync`. [#22409](https://github.com/nodejs/node/pull/22409)
- **coverage**:
  - Native V8 code coverage information can now be output to disk by setting the
    environment variable `NODE_V8_COVERAGE` to a directory. [#22527](https://github.com/nodejs/node/pull/22527)
- **deps**:
  - The bundled npm was upgraded to version 6.4.1. [#22591](https://github.com/nodejs/node/pull/22591)
    - Changelogs:
      [6.3.0-next.0](https://github.com/npm/cli/releases/tag/v6.3.0-next.0)
      [6.3.0](https://github.com/npm/cli/releases/tag/v6.3.0)
      [6.4.0](https://github.com/npm/cli/releases/tag/v6.4.0)
      [6.4.1](https://github.com/npm/cli/releases/tag/v6.4.1)
- **fs**:
  - The methods `fs.read`, `fs.readSync`, `fs.write`, `fs.writeSync`,
    `fs.writeFile` and `fs.writeFileSync` now all accept `TypedArray` and
    `DataView` objects. [#22150](https://github.com/nodejs/node/pull/22150)
  - A new boolean option, `withFileTypes`, can be passed to `fs.readdir` and
    `fs.readdirSync`. If set to true, the methods return an array of directory
    entries. These are objects that can be used to determine the type of each
    entry and filter them based on that without calling `fs.stat`. [#22020](https://github.com/nodejs/node/pull/22020)
- **http2**:
  - The `http2` module is no longer experimental. [#22466](https://github.com/nodejs/node/pull/22466)
- **os**:
  - Added two new methods: `os.getPriority` and `os.setPriority`, allowing to
    manipulate the scheduling priority of processes. [#22407](https://github.com/nodejs/node/pull/22407)
- **process**:
  - Added `process.allowedNodeEnvironmentFlags`. This object can be used to
    programmatically validate and list flags that are allowed in the
    `NODE_OPTIONS` environment variable. [#19335](https://github.com/nodejs/node/pull/19335)
- **src**:
  - Deprecated option variables in public C++ API. [#22515](https://github.com/nodejs/node/pull/22515)
  - Refactored options parsing. [#22392](https://github.com/nodejs/node/pull/22392)
- **vm**:
  - Added `vm.compileFunction`, a method to create new JavaScript functions from
    a source body, with options similar to those of the other `vm` methods. [#21571](https://github.com/nodejs/node/pull/21571)
- **Added new collaborators**:
  - [lundibundi](https://github.com/lundibundi) - Denys Otrishko

### Commits

- [[`bdd3afbb87`](https://github.com/nodejs/node/commit/bdd3afbb87)] - **assert**: fix loose set and map comparison (Ruben Bridgewater) [#22495](https://github.com/nodejs/node/pull/22495)
- [[`e2a801a5e6`](https://github.com/nodejs/node/commit/e2a801a5e6)] - **async_hooks**: adding regression test case for async/await (Anto Aravinth) [#22374](https://github.com/nodejs/node/pull/22374)
- [[`48648f5194`](https://github.com/nodejs/node/commit/48648f5194)] - **benchmark**: add lines to scatter plots (Denys Otrishko) [#22074](https://github.com/nodejs/node/pull/22074)
- [[`9a10421f53`](https://github.com/nodejs/node/commit/9a10421f53)] - **build**: use arm64 as DESTCPU for aarch64 (Daniel Bevenius) [#22548](https://github.com/nodejs/node/pull/22548)
- [[`4862ce1816`](https://github.com/nodejs/node/commit/4862ce1816)] - **build**: use `0o` octal notation in configure (Anna Henningsen) [#22536](https://github.com/nodejs/node/pull/22536)
- [[`efe71e9e31`](https://github.com/nodejs/node/commit/efe71e9e31)] - **build**: Don't set `-fno-threadsafe-statics` on macOS (Kyle Fuller) [#22198](https://github.com/nodejs/node/pull/22198)
- [[`fc1259bf56`](https://github.com/nodejs/node/commit/fc1259bf56)] - **build**: use `npm ci` (Refael Ackermann) [#22399](https://github.com/nodejs/node/pull/22399)
- [[`660c515e60`](https://github.com/nodejs/node/commit/660c515e60)] - **build**: move available-node variable to top (Daniel Bevenius) [#22356](https://github.com/nodejs/node/pull/22356)
- [[`8f760c2476`](https://github.com/nodejs/node/commit/8f760c2476)] - **build**: touch tools/doc/node_modules after run (Daniel Bevenius) [#22350](https://github.com/nodejs/node/pull/22350)
- [[`fd6033c341`](https://github.com/nodejs/node/commit/fd6033c341)] - **build**: add test-doc to test target (Daniel Bevenius) [#22294](https://github.com/nodejs/node/pull/22294)
- [[`ed874e40d1`](https://github.com/nodejs/node/commit/ed874e40d1)] - **build**: use echo command instead of shell comments (Daniel Bevenius) [#22293](https://github.com/nodejs/node/pull/22293)
- [[`3915537c13`](https://github.com/nodejs/node/commit/3915537c13)] - **build,tools**: tweak the travis config (Refael Ackermann) [#22417](https://github.com/nodejs/node/pull/22417)
- [[`2f9295e68b`](https://github.com/nodejs/node/commit/2f9295e68b)] - **build,win**: remove unmatched `endlocal` statement (Refael Ackermann) [#22627](https://github.com/nodejs/node/pull/22627)
- [[`180bb0b7d8`](https://github.com/nodejs/node/commit/180bb0b7d8)] - **child_process**: fix handling of incorrect uid/gid in spawn (Denys Otrishko) [#22574](https://github.com/nodejs/node/pull/22574)
- [[`5321c312c2`](https://github.com/nodejs/node/commit/5321c312c2)] - **(SEMVER-MINOR)** **child_process**: allow typed arrays for input (Sarat Addepalli) [#22409](https://github.com/nodejs/node/pull/22409)
- [[`43092ebfa2`](https://github.com/nodejs/node/commit/43092ebfa2)] - **cli**: more flexible width when printing `--help` (Anna Henningsen) [#22637](https://github.com/nodejs/node/pull/22637)
- [[`18ce2b8911`](https://github.com/nodejs/node/commit/18ce2b8911)] - **cli**: generate --help text in JS (Anna Henningsen) [#22490](https://github.com/nodejs/node/pull/22490)
- [[`dec42b54f7`](https://github.com/nodejs/node/commit/dec42b54f7)] - **cli**: fix flags on help output (Gus Caplan) [#22271](https://github.com/nodejs/node/pull/22271)
- [[`9a0dad2097`](https://github.com/nodejs/node/commit/9a0dad2097)] - **(SEMVER-MINOR)** **coverage**: expose native V8 coverage (Benjamin Coe) [#22527](https://github.com/nodejs/node/pull/22527)
- [[`989fd73f1e`](https://github.com/nodejs/node/commit/989fd73f1e)] - **crypto**: fix incorrect use of INT_MAX in validation (Tobias Nießen) [#22581](https://github.com/nodejs/node/pull/22581)
- [[`c47c79e1ca`](https://github.com/nodejs/node/commit/c47c79e1ca)] - **crypto**: improve setAuthTag (Tobias Nießen) [#22538](https://github.com/nodejs/node/pull/22538)
- [[`ea34cc7b88`](https://github.com/nodejs/node/commit/ea34cc7b88)] - **crypto**: deduplicate public key parsing (Tobias Nießen) [#22553](https://github.com/nodejs/node/pull/22553)
- [[`59a6c60a92`](https://github.com/nodejs/node/commit/59a6c60a92)] - **crypto**: add support for OCB mode for AEAD (Tobias Nießen) [#21447](https://github.com/nodejs/node/pull/21447)
- [[`2c33dc36b1`](https://github.com/nodejs/node/commit/2c33dc36b1)] - **deps**: update to nghttp2 1.33.0 (Anna Henningsen) [#22649](https://github.com/nodejs/node/pull/22649)
- [[`212e6bb092`](https://github.com/nodejs/node/commit/212e6bb092)] - **deps**: cherry-pick 22116dd from upstream V8 (Marcel Laverdet) [#21992](https://github.com/nodejs/node/pull/21992)
- [[`f7295493c4`](https://github.com/nodejs/node/commit/f7295493c4)] - **deps**: backport a8f6869 from upstream V8 (Ben Newman) [#22122](https://github.com/nodejs/node/pull/22122)
- [[`c84c27f7de`](https://github.com/nodejs/node/commit/c84c27f7de)] - **deps**: cherry-pick bf5ea81 from upstream V8 (Ali Ijaz Sheikh) [#22114](https://github.com/nodejs/node/pull/22114)
- [[`a986abc529`](https://github.com/nodejs/node/commit/a986abc529)] - **deps**: fix V8 test regression (Michaël Zasso) [#22677](https://github.com/nodejs/node/pull/22677)
- [[`a5c0bc44ac`](https://github.com/nodejs/node/commit/a5c0bc44ac)] - **deps**: backport 4 CPU profiler commits from upstream V8 (Peter Marshall) [#22028](https://github.com/nodejs/node/pull/22028)
- [[`11c96987ff`](https://github.com/nodejs/node/commit/11c96987ff)] - **(SEMVER-MINOR)** **deps**: upgrade npm to 6.4.1 (Kat Marchán) [#22591](https://github.com/nodejs/node/pull/22591)
- [[`5f44ce8b8b`](https://github.com/nodejs/node/commit/5f44ce8b8b)] - **deps**: backport String::Utf8Length with isolate (Michaël Zasso) [#22531](https://github.com/nodejs/node/pull/22531)
- [[`d50e1ffa52`](https://github.com/nodejs/node/commit/d50e1ffa52)] - **deps**: backport String::Write{OneByte,Utf8} with isolate (Michaël Zasso) [#22531](https://github.com/nodejs/node/pull/22531)
- [[`3dc9cfc4af`](https://github.com/nodejs/node/commit/3dc9cfc4af)] - **deps**: backport StackFrame::GetFrame with isolate (Michaël Zasso) [#22531](https://github.com/nodejs/node/pull/22531)
- [[`1be23f7b95`](https://github.com/nodejs/node/commit/1be23f7b95)] - **deps**: sync V8 embedder string with master branch (Michaël Zasso) [#22573](https://github.com/nodejs/node/pull/22573)
- [[`aa22dc8d68`](https://github.com/nodejs/node/commit/aa22dc8d68)] - **deps**: import acorn@5.7.2 (Sam Ruby) [#22488](https://github.com/nodejs/node/pull/22488)
- [[`611f423e1b`](https://github.com/nodejs/node/commit/611f423e1b)] - **deps**: patch V8 to 6.8.275.30 (Michaël Zasso) [#22125](https://github.com/nodejs/node/pull/22125)
- [[`90e99dac86`](https://github.com/nodejs/node/commit/90e99dac86)] - **deps**: upgrade to libuv 1.23.0 (cjihrig) [#22365](https://github.com/nodejs/node/pull/22365)
- [[`eab377f681`](https://github.com/nodejs/node/commit/eab377f681)] - **deps**: fix CRLF in text file not present in upstream (Joyee Cheung) [#22340](https://github.com/nodejs/node/pull/22340)
- [[`c4ef170484`](https://github.com/nodejs/node/commit/c4ef170484)] - **doc**: remove usage of deprecated V8 APIs in addons.md (Michaël Zasso) [#22667](https://github.com/nodejs/node/pull/22667)
- [[`a448c8b779`](https://github.com/nodejs/node/commit/a448c8b779)] - **doc**: add blurb about implications of ABI stability (Gabriel Schulhof) [#22508](https://github.com/nodejs/node/pull/22508)
- [[`a3e3ae01fb`](https://github.com/nodejs/node/commit/a3e3ae01fb)] - **doc**: clarify Readable paused/flowing!==object mode (Chris White) [#22619](https://github.com/nodejs/node/pull/22619)
- [[`56e654a47f`](https://github.com/nodejs/node/commit/56e654a47f)] - **doc**: update a link in v8.md (lakamsani) [#22639](https://github.com/nodejs/node/pull/22639)
- [[`805875d33b`](https://github.com/nodejs/node/commit/805875d33b)] - **doc**: add personal pronoun for danbev (Daniel Bevenius) [#22670](https://github.com/nodejs/node/pull/22670)
- [[`71502f219c`](https://github.com/nodejs/node/commit/71502f219c)] - **doc**: improve ECDH example (Tobias Nießen) [#22607](https://github.com/nodejs/node/pull/22607)
- [[`a4545ad8dc`](https://github.com/nodejs/node/commit/a4545ad8dc)] - **doc**: indicate createSecureContext arg is optional (Rich Trott) [#22545](https://github.com/nodejs/node/pull/22545)
- [[`0e862da422`](https://github.com/nodejs/node/commit/0e862da422)] - **doc**: remove \_optional\_ designation for tls options (Rich Trott) [#22545](https://github.com/nodejs/node/pull/22545)
- [[`c7268c45bc`](https://github.com/nodejs/node/commit/c7268c45bc)] - **doc**: improve examples in buffer docs (pranshuchittora) [#22170](https://github.com/nodejs/node/pull/22170)
- [[`395ba7b046`](https://github.com/nodejs/node/commit/395ba7b046)] - **doc**: fix a typo in fs.md (Vse Mozhet Byt) [#22635](https://github.com/nodejs/node/pull/22635)
- [[`7d8ef42058`](https://github.com/nodejs/node/commit/7d8ef42058)] - **doc**: clarify fallback behavior of module require (TomCoded) [#22494](https://github.com/nodejs/node/pull/22494)
- [[`3cec988e79`](https://github.com/nodejs/node/commit/3cec988e79)] - **doc**: Remove 'dnt_helper.js' (MaleDong) [#22595](https://github.com/nodejs/node/pull/22595)
- [[`5c2a6d8dfb`](https://github.com/nodejs/node/commit/5c2a6d8dfb)] - **doc**: add section on how to build debug build (Troels Liebe Bentsen) [#22510](https://github.com/nodejs/node/pull/22510)
- [[`bfdb28e45a`](https://github.com/nodejs/node/commit/bfdb28e45a)] - **doc**: fix up warning text about character devices (Anna Henningsen) [#22569](https://github.com/nodejs/node/pull/22569)
- [[`56f73a1996`](https://github.com/nodejs/node/commit/56f73a1996)] - **doc**: add profiling APIs to the diagnostics support document (Matheus Marchini) [#22588](https://github.com/nodejs/node/pull/22588)
- [[`6f0e83ee03`](https://github.com/nodejs/node/commit/6f0e83ee03)] - **doc**: update Linux perf test status in our CI (Matheus Marchini) [#22588](https://github.com/nodejs/node/pull/22588)
- [[`ae934186df`](https://github.com/nodejs/node/commit/ae934186df)] - **doc**: make Stability Index more concise (Rich Trott) [#22544](https://github.com/nodejs/node/pull/22544)
- [[`c3a4cc4c16`](https://github.com/nodejs/node/commit/c3a4cc4c16)] - **doc**: unify deprecation wording (Tobias Nießen) [#22555](https://github.com/nodejs/node/pull/22555)
- [[`e24cd92b66`](https://github.com/nodejs/node/commit/e24cd92b66)] - **doc**: remove redundant 'Example:' and similar notes (Vse Mozhet Byt) [#22537](https://github.com/nodejs/node/pull/22537)
- [[`1d38399bcd`](https://github.com/nodejs/node/commit/1d38399bcd)] - **doc**: replace `1` by `process.stdout.fd` (Weijia Wang) [#22564](https://github.com/nodejs/node/pull/22564)
- [[`5e7c6518a3`](https://github.com/nodejs/node/commit/5e7c6518a3)] - **doc**: warn against streaming from character devices (Gireesh Punathil) [#21212](https://github.com/nodejs/node/pull/21212)
- [[`05d432c2a6`](https://github.com/nodejs/node/commit/05d432c2a6)] - **doc**: initial cut at support tiers for diag tools (Michael Dawson) [#21870](https://github.com/nodejs/node/pull/21870)
- [[`397235ec62`](https://github.com/nodejs/node/commit/397235ec62)] - **doc**: simplify http2 wording and formatting (Rich Trott) [#22541](https://github.com/nodejs/node/pull/22541)
- [[`81364a7e16`](https://github.com/nodejs/node/commit/81364a7e16)] - **doc**: clarify ERR_AMBIGUOUS_ARGUMENT (Rich Trott) [#22542](https://github.com/nodejs/node/pull/22542)
- [[`46063b8479`](https://github.com/nodejs/node/commit/46063b8479)] - **doc**: add GitHub email set up link to COLLABORATOR_GUIDE (Denys Otrishko) [#22525](https://github.com/nodejs/node/pull/22525)
- [[`9b4403dd7d`](https://github.com/nodejs/node/commit/9b4403dd7d)] - **doc**: clarify git config name/email requirements (Anna Henningsen) [#22433](https://github.com/nodejs/node/pull/22433)
- [[`2875f72c46`](https://github.com/nodejs/node/commit/2875f72c46)] - **doc**: document removed error codes (Sarat Addepalli) [#22100](https://github.com/nodejs/node/pull/22100)
- [[`c833d83d21`](https://github.com/nodejs/node/commit/c833d83d21)] - **doc**: support 'removed' field in doc YAML sections (Sarat Addepalli) [#22100](https://github.com/nodejs/node/pull/22100)
- [[`e2541303f3`](https://github.com/nodejs/node/commit/e2541303f3)] - **doc**: tweak macOS-firewall note position (ZYSzys) [#22440](https://github.com/nodejs/node/pull/22440)
- [[`6228433926`](https://github.com/nodejs/node/commit/6228433926)] - **doc**: add lundibundi to collaborators (Denys Otrishko) [#22491](https://github.com/nodejs/node/pull/22491)
- [[`2a849ba241`](https://github.com/nodejs/node/commit/2a849ba241)] - **doc**: state callback behavior on empty buffer (Ruben Verborgh) [#22461](https://github.com/nodejs/node/pull/22461)
- [[`f27a25472c`](https://github.com/nodejs/node/commit/f27a25472c)] - **doc**: make createPushResponse() more detailled (MaleDong) [#22366](https://github.com/nodejs/node/pull/22366)
- [[`282a45d042`](https://github.com/nodejs/node/commit/282a45d042)] - **doc**: update wrapping-related documentation (Gabriel Schulhof) [#22363](https://github.com/nodejs/node/pull/22363)
- [[`c17e980534`](https://github.com/nodejs/node/commit/c17e980534)] - **doc**: clarify fs.write\[Sync\]() descriptions (Vse Mozhet Byt) [#22402](https://github.com/nodejs/node/pull/22402)
- [[`1ebaa2af4a`](https://github.com/nodejs/node/commit/1ebaa2af4a)] - **doc**: unify optional arguments format in headings (Vse Mozhet Byt) [#22397](https://github.com/nodejs/node/pull/22397)
- [[`d86e615549`](https://github.com/nodejs/node/commit/d86e615549)] - **doc**: clarify documentation of pipes and zlib objects (Andreas Girgensohn) [#22354](https://github.com/nodejs/node/pull/22354)
- [[`e6440888b1`](https://github.com/nodejs/node/commit/e6440888b1)] - **doc**: add doc for --loader option (Sarat Addepalli) [#22104](https://github.com/nodejs/node/pull/22104)
- [[`9142935eb2`](https://github.com/nodejs/node/commit/9142935eb2)] - **doc**: clarify that new URL().port could be an empty string (Matteo Collina) [#22232](https://github.com/nodejs/node/pull/22232)
- [[`c894145e28`](https://github.com/nodejs/node/commit/c894145e28)] - **doc**: Windows building supported on x64 (Refael Ackermann) [#21443](https://github.com/nodejs/node/pull/21443)
- [[`797229810e`](https://github.com/nodejs/node/commit/797229810e)] - **doc**: clarify ServerResponse explanations (MaleDong) [#22305](https://github.com/nodejs/node/pull/22305)
- [[`2260bb9214`](https://github.com/nodejs/node/commit/2260bb9214)] - **(SEMVER-MINOR)** **fs**: update read to work with any TypedArray/DataView (Sarat Addepalli) [#22150](https://github.com/nodejs/node/pull/22150)
- [[`ad97314418`](https://github.com/nodejs/node/commit/ad97314418)] - **(SEMVER-MINOR)** **fs**: readdir optionally returning type information (Bryan English) [#22020](https://github.com/nodejs/node/pull/22020)
- [[`1e9d3e64cd`](https://github.com/nodejs/node/commit/1e9d3e64cd)] - **gyp**: muffle xcodebuild warnings (Ujjwal Sharma) [#21999](https://github.com/nodejs/node/pull/21999)
- [[`c07a065699`](https://github.com/nodejs/node/commit/c07a065699)] - **http**: adding doc and debug for calling empty string on write function (Anto Aravinth) [#22118](https://github.com/nodejs/node/pull/22118)
- [[`4cdecc5ebe`](https://github.com/nodejs/node/commit/4cdecc5ebe)] - **http2**: don't expose the original socket through the socket proxy (Szymon Marczak) [#22650](https://github.com/nodejs/node/pull/22650)
- [[`f77bbe8cab`](https://github.com/nodejs/node/commit/f77bbe8cab)] - **(SEMVER-MINOR)** **http2**: graduate from experimental (James M Snell) [#22466](https://github.com/nodejs/node/pull/22466)
- [[`a740145e1b`](https://github.com/nodejs/node/commit/a740145e1b)] - **http2**: throw better error when accessing unbound socket proxy (James M Snell) [#22486](https://github.com/nodejs/node/pull/22486)
- [[`d3ceaa1d41`](https://github.com/nodejs/node/commit/d3ceaa1d41)] - **http2**: emit timeout on compat request and response (James M Snell) [#22252](https://github.com/nodejs/node/pull/22252)
- [[`f0be05342b`](https://github.com/nodejs/node/commit/f0be05342b)] - **lib**: merge onread handlers for http2 streams & net.Socket (Ashok) [#22449](https://github.com/nodejs/node/pull/22449)
- [[`1eac11f626`](https://github.com/nodejs/node/commit/1eac11f626)] - **lib**: extract validateNumber validator (Jon Moss) [#22249](https://github.com/nodejs/node/pull/22249)
- [[`3f93782767`](https://github.com/nodejs/node/commit/3f93782767)] - **lib**: remove unused exec param (MaleDong) [#22274](https://github.com/nodejs/node/pull/22274)
- [[`46fbc23614`](https://github.com/nodejs/node/commit/46fbc23614)] - **lib,src**: standardize `owner_symbol` for handles (Anna Henningsen) [#22002](https://github.com/nodejs/node/pull/22002)
- [[`96213c8027`](https://github.com/nodejs/node/commit/96213c8027)] - **n-api**: clean up thread-safe function (Gabriel Schulhof) [#22259](https://github.com/nodejs/node/pull/22259)
- [[`609ae33bbe`](https://github.com/nodejs/node/commit/609ae33bbe)] - **n-api**: remove idle_running from TsFn (Lars-Magnus Skog) [#22520](https://github.com/nodejs/node/pull/22520)
- [[`ad0072abfa`](https://github.com/nodejs/node/commit/ad0072abfa)] - **os**: don't use getCheckedFunction() in userInfo() (cjihrig) [#22609](https://github.com/nodejs/node/pull/22609)
- [[`219da67e2e`](https://github.com/nodejs/node/commit/219da67e2e)] - **(SEMVER-MINOR)** **os**: add os.{get,set}Priority() (cjihrig) [#22407](https://github.com/nodejs/node/pull/22407)
- [[`30b22a676d`](https://github.com/nodejs/node/commit/30b22a676d)] - **os**: destructure ERR_SYSTEM_ERROR properly (cjihrig) [#22394](https://github.com/nodejs/node/pull/22394)
- [[`3b44053ce8`](https://github.com/nodejs/node/commit/3b44053ce8)] - **os**: improve networkInterfaces performance (Ruben Bridgewater) [#22359](https://github.com/nodejs/node/pull/22359)
- [[`107c8c0d4d`](https://github.com/nodejs/node/commit/107c8c0d4d)] - **perf_hooks**: move strings to env (James M Snell) [#22401](https://github.com/nodejs/node/pull/22401)
- [[`2bf46ae45e`](https://github.com/nodejs/node/commit/2bf46ae45e)] - **(SEMVER-MINOR)** **process**: add allowedNodeEnvironmentFlags property (Christopher Hiller) [#19335](https://github.com/nodejs/node/pull/19335)
- [[`5af6a89a73`](https://github.com/nodejs/node/commit/5af6a89a73)] - **process**: use owner_symbol for `_getActive*` (Anna Henningsen) [#22002](https://github.com/nodejs/node/pull/22002)
- [[`0b340ab5e7`](https://github.com/nodejs/node/commit/0b340ab5e7)] - **repl**: tab auto complete big arrays (Ruben Bridgewater) [#22408](https://github.com/nodejs/node/pull/22408)
- [[`1025868d5c`](https://github.com/nodejs/node/commit/1025868d5c)] - **src**: remove calls to deprecated V8 functions (Equals) (Michaël Zasso) [#22665](https://github.com/nodejs/node/pull/22665)
- [[`c637d41b9d`](https://github.com/nodejs/node/commit/c637d41b9d)] - **src**: remove calls to deprecated v8 functions (IntegerValue) (Ujjwal Sharma) [#22129](https://github.com/nodejs/node/pull/22129)
- [[`be86ddb7ec`](https://github.com/nodejs/node/commit/be86ddb7ec)] - **src**: promote v8 name spaces with using (Gireesh Punathil) [#22641](https://github.com/nodejs/node/pull/22641)
- [[`b1e5491ae9`](https://github.com/nodejs/node/commit/b1e5491ae9)] - **src**: remove calls to deprecated V8 functions (Int32Value) (Michaël Zasso) [#22662](https://github.com/nodejs/node/pull/22662)
- [[`e5e72e60f0`](https://github.com/nodejs/node/commit/e5e72e60f0)] - **src**: skip warnings for our own deprecated APIs (Anna Henningsen) [#22666](https://github.com/nodejs/node/pull/22666)
- [[`dbb8f37377`](https://github.com/nodejs/node/commit/dbb8f37377)] - **src**: remove editing leftovers from options help text (Anna Henningsen) [#22636](https://github.com/nodejs/node/pull/22636)
- [[`4e651983e5`](https://github.com/nodejs/node/commit/4e651983e5)] - **src**: allow UTF-16 in generic StringBytes decode call (Anna Henningsen) [#22622](https://github.com/nodejs/node/pull/22622)
- [[`f064d44fad`](https://github.com/nodejs/node/commit/f064d44fad)] - **src**: warn about odd UTF-16 decoding function signature (Anna Henningsen) [#22623](https://github.com/nodejs/node/pull/22623)
- [[`516d71af66`](https://github.com/nodejs/node/commit/516d71af66)] - **src**: fix a typo in the comment (Gireesh Punathil) [#22640](https://github.com/nodejs/node/pull/22640)
- [[`1edd47e0b7`](https://github.com/nodejs/node/commit/1edd47e0b7)] - **src**: disable debug options when inspector is unavailable (Anna Henningsen) [#22657](https://github.com/nodejs/node/pull/22657)
- [[`cfca8518f8`](https://github.com/nodejs/node/commit/cfca8518f8)] - **src**: add `NODE_EXTERN` to class definition (Anna Henningsen) [#22559](https://github.com/nodejs/node/pull/22559)
- [[`c8e586c859`](https://github.com/nodejs/node/commit/c8e586c859)] - **src**: add trace points to dns (Chin Huang) [#21840](https://github.com/nodejs/node/pull/21840)
- [[`b8299585bc`](https://github.com/nodejs/node/commit/b8299585bc)] - **src**: make CLI options programatically accesible (Anna Henningsen) [#22490](https://github.com/nodejs/node/pull/22490)
- [[`8930268382`](https://github.com/nodejs/node/commit/8930268382)] - **src**: fix node::FatalException (Tobias Nießen) [#22654](https://github.com/nodejs/node/pull/22654)
- [[`bac4c41328`](https://github.com/nodejs/node/commit/bac4c41328)] - **(SEMVER-MINOR)** **src**: deprecate option variables in public API (Anna Henningsen) [#22515](https://github.com/nodejs/node/pull/22515)
- [[`956502949b`](https://github.com/nodejs/node/commit/956502949b)] - **src**: remove calls to deprecated v8 functions (Uint32Value) (Ujjwal Sharma) [#22143](https://github.com/nodejs/node/pull/22143)
- [[`b2a955a269`](https://github.com/nodejs/node/commit/b2a955a269)] - **src**: rework (mostly internal) functions to use Maybes (Ujjwal Sharma) [#21935](https://github.com/nodejs/node/pull/21935)
- [[`0a65727f0a`](https://github.com/nodejs/node/commit/0a65727f0a)] - **src**: remove calls to deprecated v8 functions (ToString) (Ujjwal Sharma) [#21935](https://github.com/nodejs/node/pull/21935)
- [[`75a9192549`](https://github.com/nodejs/node/commit/75a9192549)] - **src**: fix external memory usage going negative (Mathias Buus) [#22594](https://github.com/nodejs/node/pull/22594)
- [[`99146772e0`](https://github.com/nodejs/node/commit/99146772e0)] - **src**: remove calls to deprecated v8 functions (BooleanValue) (Ujjwal Sharma) [#22075](https://github.com/nodejs/node/pull/22075)
- [[`a7c0cb87be`](https://github.com/nodejs/node/commit/a7c0cb87be)] - **src**: do not pass code to ScriptCompiler::CreateCodeCacheForFunction (Michaël Zasso) [#22596](https://github.com/nodejs/node/pull/22596)
- [[`332b035a96`](https://github.com/nodejs/node/commit/332b035a96)] - **src**: use String::Utf8Length with isolate (Michaël Zasso) [#22531](https://github.com/nodejs/node/pull/22531)
- [[`8375f753c0`](https://github.com/nodejs/node/commit/8375f753c0)] - **src**: use String::Write{OneByte,Utf8} with isolate (Michaël Zasso) [#22531](https://github.com/nodejs/node/pull/22531)
- [[`9478f29387`](https://github.com/nodejs/node/commit/9478f29387)] - **src**: use StackFrame::GetFrame with isolate (Michaël Zasso) [#22531](https://github.com/nodejs/node/pull/22531)
- [[`f8feb0253d`](https://github.com/nodejs/node/commit/f8feb0253d)] - **src**: add missing `NODE_WANT_INTERNALS` guards (Anna Henningsen) [#22514](https://github.com/nodejs/node/pull/22514)
- [[`2c5dfef393`](https://github.com/nodejs/node/commit/2c5dfef393)] - **src**: fix NODE_OPTIONS parsing bug (Anna Henningsen) [#22529](https://github.com/nodejs/node/pull/22529)
- [[`034ba7322f`](https://github.com/nodejs/node/commit/034ba7322f)] - **src**: fix --without-ssl build (Ian McKellar) [#22484](https://github.com/nodejs/node/pull/22484)
- [[`2767ebad2f`](https://github.com/nodejs/node/commit/2767ebad2f)] - **src**: move more to node_process.cc from node.cc (James M Snell) [#22422](https://github.com/nodejs/node/pull/22422)
- [[`8fd55fffee`](https://github.com/nodejs/node/commit/8fd55fffee)] - **(SEMVER-MINOR)** **src**: refactor options parsing (Anna Henningsen) [#22392](https://github.com/nodejs/node/pull/22392)
- [[`198cf417b5`](https://github.com/nodejs/node/commit/198cf417b5)] - **src**: yield empty maybes for failed AsyncWrap::MakeCallback calls (Anna Henningsen) [#22078](https://github.com/nodejs/node/pull/22078)
- [[`02e3daaa57`](https://github.com/nodejs/node/commit/02e3daaa57)] - **src**: implement v8::Platform::CallDelayedOnWorkerThread (Alexey Kozyatinskiy) [#22383](https://github.com/nodejs/node/pull/22383)
- [[`c207865e24`](https://github.com/nodejs/node/commit/c207865e24)] - **src**: encode 0x27 (') for special URLs (Timothy Gu) [#22022](https://github.com/nodejs/node/pull/22022)
- [[`4638ce6f03`](https://github.com/nodejs/node/commit/4638ce6f03)] - **src**: perform integrity checks on built-in code cache (Joyee Cheung) [#22152](https://github.com/nodejs/node/pull/22152)
- [[`866965ec0e`](https://github.com/nodejs/node/commit/866965ec0e)] - **src**: fix race on modpending (Ryan Petrich) [#21611](https://github.com/nodejs/node/pull/21611)
- [[`383d578d76`](https://github.com/nodejs/node/commit/383d578d76)] - **src,deps**: add isolate parameter to String::Concat (Michaël Zasso) [#22521](https://github.com/nodejs/node/pull/22521)
- [[`4ed300a585`](https://github.com/nodejs/node/commit/4ed300a585)] - **stream**: update emit readable debug statement (Daniel Bevenius) [#22613](https://github.com/nodejs/node/pull/22613)
- [[`53fb7af1b2`](https://github.com/nodejs/node/commit/53fb7af1b2)] - **stream**: restore flow if there are 'data' handlers after once('readable') (Matteo Collina) [#22209](https://github.com/nodejs/node/pull/22209)
- [[`dd772c1f13`](https://github.com/nodejs/node/commit/dd772c1f13)] - **test**: refactor test-gc-tls-external-memory (Anna Henningsen) [#22651](https://github.com/nodejs/node/pull/22651)
- [[`7a3bbd21f3`](https://github.com/nodejs/node/commit/7a3bbd21f3)] - **_Revert_** "**test**: mark async-hooks/test-callback-error as flaky" (Anna Henningsen) [#22655](https://github.com/nodejs/node/pull/22655)
- [[`4791cd7f0a`](https://github.com/nodejs/node/commit/4791cd7f0a)] - **test**: fix flaky async-hooks/test-callback-error (Anna Henningsen) [#22655](https://github.com/nodejs/node/pull/22655)
- [[`c26747d9af`](https://github.com/nodejs/node/commit/c26747d9af)] - **test**: fix flaky test-worker-message-port-transfer-self (Anna Henningsen) [#22658](https://github.com/nodejs/node/pull/22658)
- [[`e5b732f25d`](https://github.com/nodejs/node/commit/e5b732f25d)] - **test**: add test to dynamic enablement of trace-events (Ali Ijaz Sheikh) [#22114](https://github.com/nodejs/node/pull/22114)
- [[`2025eaf999`](https://github.com/nodejs/node/commit/2025eaf999)] - **test**: improve assertion in process test (Anna Henningsen) [#22634](https://github.com/nodejs/node/pull/22634)
- [[`7a70dce251`](https://github.com/nodejs/node/commit/7a70dce251)] - **test**: fix test-trace-events-dns (Rich Trott) [#22674](https://github.com/nodejs/node/pull/22674)
- [[`cb15017bfe`](https://github.com/nodejs/node/commit/cb15017bfe)] - **test**: fix flaky parallel/test-fs-write-file-typedarrays (Anna Henningsen) [#22659](https://github.com/nodejs/node/pull/22659)
- [[`7627b0430a`](https://github.com/nodejs/node/commit/7627b0430a)] - **test**: use module.exports consistently (James M Snell) [#22557](https://github.com/nodejs/node/pull/22557)
- [[`d3740d843a`](https://github.com/nodejs/node/commit/d3740d843a)] - **test**: improve assertions in test-cli-node-print-help (Anna Henningsen) [#22489](https://github.com/nodejs/node/pull/22489)
- [[`67372016bb`](https://github.com/nodejs/node/commit/67372016bb)] - **test**: move test that depends on dns query to internet (Joyee Cheung) [#22516](https://github.com/nodejs/node/pull/22516)
- [[`82732ef4f7`](https://github.com/nodejs/node/commit/82732ef4f7)] - **test**: fix typo in test name (Rich Trott) [#22605](https://github.com/nodejs/node/pull/22605)
- [[`d3bb7419f2`](https://github.com/nodejs/node/commit/d3bb7419f2)] - **test**: refacor spawn\[Sync\]Pwd (Refael Ackermann) [#22522](https://github.com/nodejs/node/pull/22522)
- [[`4cdc61bc8c`](https://github.com/nodejs/node/commit/4cdc61bc8c)] - **test**: move AEAD test vectors out of script (Tobias Nießen) [#21873](https://github.com/nodejs/node/pull/21873)
- [[`d27e463ca6`](https://github.com/nodejs/node/commit/d27e463ca6)] - **test**: properly extend process.env in child_process (Lucas Woo) [#22430](https://github.com/nodejs/node/pull/22430)
- [[`863899970b`](https://github.com/nodejs/node/commit/863899970b)] - **test**: add test for internalConnect() when address type is IPv6 (Yaniv Friedensohn) [#22444](https://github.com/nodejs/node/pull/22444)
- [[`7f85288808`](https://github.com/nodejs/node/commit/7f85288808)] - **test**: remove string literal from strictEqual() (Scott Van Gilder) [#22512](https://github.com/nodejs/node/pull/22512)
- [[`81d824b132`](https://github.com/nodejs/node/commit/81d824b132)] - **test**: move custom WHATWG URL tests into separate files (Joyee Cheung) [#22442](https://github.com/nodejs/node/pull/22442)
- [[`6f31478229`](https://github.com/nodejs/node/commit/6f31478229)] - **test**: remove third argument from strictEqual() (Neeraj Laad) [#22451](https://github.com/nodejs/node/pull/22451)
- [[`d02fb36379`](https://github.com/nodejs/node/commit/d02fb36379)] - **test**: move common.isCPPSymbolsNotMapped to tick-processor tests (James M Snell) [#22459](https://github.com/nodejs/node/pull/22459)
- [[`9ec105ccdc`](https://github.com/nodejs/node/commit/9ec105ccdc)] - **test**: improve code coverage for string decoder (Benjamin Chen) [#22306](https://github.com/nodejs/node/pull/22306)
- [[`1e7deb72d2`](https://github.com/nodejs/node/commit/1e7deb72d2)] - **test**: add streams benchmark test (Denys Otrishko) [#22335](https://github.com/nodejs/node/pull/22335)
- [[`ef60a8d7a5`](https://github.com/nodejs/node/commit/ef60a8d7a5)] - **test**: add vm benchmark test (Denys Otrishko) [#22335](https://github.com/nodejs/node/pull/22335)
- [[`400aac8c5f`](https://github.com/nodejs/node/commit/400aac8c5f)] - **test**: add v8 benchmark test (Denys Otrishko) [#22335](https://github.com/nodejs/node/pull/22335)
- [[`a8b8d3fe56`](https://github.com/nodejs/node/commit/a8b8d3fe56)] - **test**: move common.onGC to individual module (James M Snell) [#22446](https://github.com/nodejs/node/pull/22446)
- [[`6d0c3d19b8`](https://github.com/nodejs/node/commit/6d0c3d19b8)] - **test**: flaky everywhere test-trace-events-fs-sync (Refael Ackermann) [#22483](https://github.com/nodejs/node/pull/22483)
- [[`7f2d3d0ed4`](https://github.com/nodejs/node/commit/7f2d3d0ed4)] - **test**: move hijackstdio out of require('common') (James M Snell) [#22462](https://github.com/nodejs/node/pull/22462)
- [[`fcf059a667`](https://github.com/nodejs/node/commit/fcf059a667)] - **test**: add test unknown credential error of process.setgroups (Masashi Hirano) [#22368](https://github.com/nodejs/node/pull/22368)
- [[`ae016c8e6d`](https://github.com/nodejs/node/commit/ae016c8e6d)] - **test**: add tests for dnsPromises.lookup (Masashi Hirano) [#21559](https://github.com/nodejs/node/pull/21559)
- [[`98af1704ae`](https://github.com/nodejs/node/commit/98af1704ae)] - **test**: move common.ArrayStream to separate module (James M Snell) [#22447](https://github.com/nodejs/node/pull/22447)
- [[`e68438246e`](https://github.com/nodejs/node/commit/e68438246e)] - **test**: remove isGlibc from common (James M Snell) [#22443](https://github.com/nodejs/node/pull/22443)
- [[`acfb29cbd8`](https://github.com/nodejs/node/commit/acfb29cbd8)] - **test**: harden sequential/test-performance (Ruben Bridgewater) [#22404](https://github.com/nodejs/node/pull/22404)
- [[`38b0c1f04d`](https://github.com/nodejs/node/commit/38b0c1f04d)] - **test**: remove redundant cli tests (Bryan English) [#22355](https://github.com/nodejs/node/pull/22355)
- [[`e8e014a8dc`](https://github.com/nodejs/node/commit/e8e014a8dc)] - **test**: improve assert message in http timeout test (Rich Trott) [#22403](https://github.com/nodejs/node/pull/22403)
- [[`22adebfc9a`](https://github.com/nodejs/node/commit/22adebfc9a)] - **test**: move http timeout test to parallel (Rich Trott) [#22403](https://github.com/nodejs/node/pull/22403)
- [[`5aa3100c29`](https://github.com/nodejs/node/commit/5aa3100c29)] - **test**: fix flaky http timeout test (Rich Trott) [#22403](https://github.com/nodejs/node/pull/22403)
- [[`33994d896a`](https://github.com/nodejs/node/commit/33994d896a)] - **test**: remove third argument from assert.strictEqual() (Dzmitry_Prudnikau) [#22371](https://github.com/nodejs/node/pull/22371)
- [[`fbc189b9eb`](https://github.com/nodejs/node/commit/fbc189b9eb)] - **test**: cover error case in os getCheckedFunction() (cjihrig) [#22394](https://github.com/nodejs/node/pull/22394)
- [[`149c209171`](https://github.com/nodejs/node/commit/149c209171)] - **test**: harden test-gc-http-client (Ruben Bridgewater) [#22373](https://github.com/nodejs/node/pull/22373)
- [[`acfb72486d`](https://github.com/nodejs/node/commit/acfb72486d)] - **test**: remove harmony flags (Ruben Bridgewater) [#22285](https://github.com/nodejs/node/pull/22285)
- [[`44bcc1d71a`](https://github.com/nodejs/node/commit/44bcc1d71a)] - **test**: fix cctest URLTest.ToFilePath on Win32 without Intl (James M Snell) [#22265](https://github.com/nodejs/node/pull/22265)
- [[`2ed22dfa3a`](https://github.com/nodejs/node/commit/2ed22dfa3a)] - **test**: mark async-hooks/test-callback-error as flaky (Joyee Cheung) [#22330](https://github.com/nodejs/node/pull/22330)
- [[`4a28d38788`](https://github.com/nodejs/node/commit/4a28d38788)] - **test**: mark async-hooks/test-statwatcher as flaky (Joyee Cheung) [#22330](https://github.com/nodejs/node/pull/22330)
- [[`5cfab145a1`](https://github.com/nodejs/node/commit/5cfab145a1)] - **test**: remove common.hasTracing (Rich Trott) [#22250](https://github.com/nodejs/node/pull/22250)
- [[`7794d4e0b8`](https://github.com/nodejs/node/commit/7794d4e0b8)] - **test,stream**: fix pipeline test so it runs well on Windows in older nodes (Matteo Collina) [#22456](https://github.com/nodejs/node/pull/22456)
- [[`696f7a54b5`](https://github.com/nodejs/node/commit/696f7a54b5)] - **tls**: improve debugging assertion (Anna Henningsen) [#22625](https://github.com/nodejs/node/pull/22625)
- [[`2ca21998d3`](https://github.com/nodejs/node/commit/2ca21998d3)] - **tools**: add \[src\] links to async_hooks.html (Sam Ruby) [#22656](https://github.com/nodejs/node/pull/22656)
- [[`c32d5577b6`](https://github.com/nodejs/node/commit/c32d5577b6)] - **tools**: add \[src\] links to assert.html (Sam Ruby) [#22601](https://github.com/nodejs/node/pull/22601)
- [[`f5520cc53d`](https://github.com/nodejs/node/commit/f5520cc53d)] - **tools**: specify rule disabled in test-assert.js (Rich Trott) [#22563](https://github.com/nodejs/node/pull/22563)
- [[`15b7f75e49`](https://github.com/nodejs/node/commit/15b7f75e49)] - **tools**: specify rules disabled in common/dns.js (Rich Trott) [#22563](https://github.com/nodejs/node/pull/22563)
- [[`50100f3a9c`](https://github.com/nodejs/node/commit/50100f3a9c)] - **tools**: Include links to source code in documentation (Sam Ruby) [#22405](https://github.com/nodejs/node/pull/22405)
- [[`14ac77e2e2`](https://github.com/nodejs/node/commit/14ac77e2e2)] - **tools**: add missing package-lock to clang-format (Michaël Zasso) [#22500](https://github.com/nodejs/node/pull/22500)
- [[`9d246f97d1`](https://github.com/nodejs/node/commit/9d246f97d1)] - **tools**: update ESLint to 5.4.0 (Rich Trott) [#22454](https://github.com/nodejs/node/pull/22454)
- [[`725a2b14f2`](https://github.com/nodejs/node/commit/725a2b14f2)] - **tools**: simplify ESLint invocation in Makefile (Rich Trott) [#22348](https://github.com/nodejs/node/pull/22348)
- [[`5b14066c14`](https://github.com/nodejs/node/commit/5b14066c14)] - **util**: restore all information in inspect (Ruben Bridgewater) [#22437](https://github.com/nodejs/node/pull/22437)
- [[`f86ca8948a`](https://github.com/nodejs/node/commit/f86ca8948a)] - **util**: Fix number format for `pad` (MaleDong) [#21906](https://github.com/nodejs/node/pull/21906)
- [[`1828017053`](https://github.com/nodejs/node/commit/1828017053)] - **util**: mark special entries as such (Ruben Bridgewater) [#22287](https://github.com/nodejs/node/pull/22287)
- [[`f763ac7dd0`](https://github.com/nodejs/node/commit/f763ac7dd0)] - **util**: escape symbol and non-enumerable keys (Ruben Bridgewater) [#22300](https://github.com/nodejs/node/pull/22300)
- [[`3dc3a3196a`](https://github.com/nodejs/node/commit/3dc3a3196a)] - **util**: improve empty typed array inspection (Ruben Bridgewater) [#22284](https://github.com/nodejs/node/pull/22284)
- [[`e9ac683efc`](https://github.com/nodejs/node/commit/e9ac683efc)] - **util**: properly indent special properties (Ruben Bridgewater) [#22291](https://github.com/nodejs/node/pull/22291)
- [[`459d676203`](https://github.com/nodejs/node/commit/459d676203)] - **util**: harden util.inspect (Ruben Bridgewater) [#21869](https://github.com/nodejs/node/pull/21869)
- [[`cdf6471234`](https://github.com/nodejs/node/commit/cdf6471234)] - **util**: fix sparse array inspection (Ruben Bridgewater) [#22283](https://github.com/nodejs/node/pull/22283)
- [[`2b1cb3b01f`](https://github.com/nodejs/node/commit/2b1cb3b01f)] - **util,assert**: improve performance (Ruben Bridgewater) [#22197](https://github.com/nodejs/node/pull/22197)
- [[`4d4180b46b`](https://github.com/nodejs/node/commit/4d4180b46b)] - **util,assert**: improve comparison performance (Ruben Bridgewater) [#22258](https://github.com/nodejs/node/pull/22258)
- [[`2937a79c45`](https://github.com/nodejs/node/commit/2937a79c45)] - **(SEMVER-MINOR)** **vm**: add bindings for v8::CompileFunctionInContext (Ujjwal Sharma) [#21571](https://github.com/nodejs/node/pull/21571)
- [[`eebcec7db5`](https://github.com/nodejs/node/commit/eebcec7db5)] - **win, build**: remove superfluous error message (Bartosz Sosnowski) [#22580](https://github.com/nodejs/node/pull/22580)
- [[`041c779814`](https://github.com/nodejs/node/commit/041c779814)] - **win,build**: build N-API addons in parallel (Bartosz Sosnowski) [#22582](https://github.com/nodejs/node/pull/22582)
- [[`1daa82a8fc`](https://github.com/nodejs/node/commit/1daa82a8fc)] - **worker**: display MessagePort status in util.inspect() (Anna Henningsen) [#22658](https://github.com/nodejs/node/pull/22658)
- [[`887c43ffa7`](https://github.com/nodejs/node/commit/887c43ffa7)] - **worker**: remove redundant function call to `setupPortReferencing` (Ouyang Yadong) [#22298](https://github.com/nodejs/node/pull/22298)
- [[`8e542eaf5f`](https://github.com/nodejs/node/commit/8e542eaf5f)] - **zlib**: fix memory leak for invalid input (Anna Henningsen) [#22713](https://github.com/nodejs/node/pull/22713)

Windows 32-bit Installer: https://nodejs.org/dist/v10.10.0/node-v10.10.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v10.10.0/node-v10.10.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v10.10.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v10.10.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v10.10.0/node-v10.10.0.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v10.10.0/node-v10.10.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v10.10.0/node-v10.10.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v10.10.0/node-v10.10.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v10.10.0/node-v10.10.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v10.10.0/node-v10.10.0-aix-ppc64.tar.gz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v10.10.0/node-v10.10.0-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v10.10.0/node-v10.10.0-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v10.10.0/node-v10.10.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v10.10.0/node-v10.10.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v10.10.0/node-v10.10.0.tar.gz \
Other release files: https://nodejs.org/dist/v10.10.0/ \
Documentation: https://nodejs.org/docs/v10.10.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

d37fa6cb6ac55ae8b3529492a3fd0a0c3210b22a8acb6b6a7228907ae941cbb1  node-v10.10.0-aix-ppc64.tar.gz
00b7a8426e076e9bf9d12ba2d571312e833fe962c70afafd10ad3682fdeeaa5e  node-v10.10.0-darwin-x64.tar.gz
5df69865b0525b14151078a9b3ddf8840991010d4454f0a8049fe7e707d874ea  node-v10.10.0-darwin-x64.tar.xz
2ca866de9ec4a20218d1ae01247352678b6507028a2dc4d271bc31380e623fd8  node-v10.10.0-headers.tar.gz
03e85f5942a2d8b7778ad50e76164cebba206648cbd442cbc8c8b5834bf287e4  node-v10.10.0-headers.tar.xz
0b83a3e427d076947b1deca943a48fba0258772f9c037de19d8b1261632d1385  node-v10.10.0-linux-arm64.tar.gz
7d57961d18ea70b4e99906d87f9de1a2defc45a36fcd8cd84295e9ae5c4281da  node-v10.10.0-linux-arm64.tar.xz
716a66abd62e04dcd0f5c95dec3561e354712da7268e550a06bca1abc02710ea  node-v10.10.0-linux-armv6l.tar.gz
2361dff30e7f007397637a902f0bd87dd6ab5f0e9580ec9cab464519ec202ae9  node-v10.10.0-linux-armv6l.tar.xz
4f6c18630bafa70a62505d54e4ac4d88fd3d5082875cb00d30edf394b0dd22a7  node-v10.10.0-linux-armv7l.tar.gz
d41130f651b517166b28b4c5f72a1144323b748aa8b6fba2ee1ec065701eb9b9  node-v10.10.0-linux-armv7l.tar.xz
0ad5ca19acb72a734d095848b132034457c7a57603fdbfd78e36eb68d76237cd  node-v10.10.0-linux-ppc64le.tar.gz
7493927b6cc72f7463f4a77320319514134bd250625494082b11d51782dbb79f  node-v10.10.0-linux-ppc64le.tar.xz
ddbbda42c7183637558dce648f7805d11f04d39c1bd05d19157d1d1d8b4cbd04  node-v10.10.0-linux-s390x.tar.gz
0fbeec8a29daf2452efca29a828ec0106c3d43cc57d904de9c1056ec4764572f  node-v10.10.0-linux-s390x.tar.xz
789994b9ad5d2b274e949c268480a197d2af8861cb00911fc1d2ce4a01631e0d  node-v10.10.0-linux-x64.tar.gz
686d2c7b7698097e67bcd68edc3d6b5d28d81f62436c7cf9e7779d134ec262a9  node-v10.10.0-linux-x64.tar.xz
8d6771b24b79a156aac2600fbc8ec34957542ba3ea6e63b2e7a9d56f3af68637  node-v10.10.0.pkg
a591d590272786b4e6e734e3a07a88bd037dbd20c2d073b44097c06f0a2e7235  node-v10.10.0-sunos-x64.tar.gz
36cea962446f14b76f8f78b73c897e8895ddbef7803126f7c43ca5426f12dfc2  node-v10.10.0-sunos-x64.tar.xz
8cfcb0d6f859645934a9318d44872e9ebcf30dde6f567f453e6380912b2acff5  node-v10.10.0.tar.gz
860eff976d83dd619849c0b4fab4cadb093ece2c718aaa5af8fb5ebbfa7d8a80  node-v10.10.0.tar.xz
4422454afcc3d5acc322b90a26794ed57b169d2dfb09ef989c9b0daf2d116cde  node-v10.10.0-win-x64.7z
70c46e6451798be9d052b700ce5dadccb75cf917f6bf0d6ed54344c856830cfb  node-v10.10.0-win-x64.zip
1477bc7ffa23e20a7595c4af5893585b09b6c6fdf1e00c1a22458f0d9d8b6ca4  node-v10.10.0-win-x86.7z
6e8c8d13a14001621d9548f599d2a8975c6fc37ddfce7a934d4f9b4cfd36d1cd  node-v10.10.0-win-x86.zip
2ab622f855406120fb57405e384c82f108745188dc65e60635b4e195a1a94312  node-v10.10.0-x64.msi
e0d19eed4054bcbbee5bc60a4e74e78db921738c04ba73ea8415ce52427935dc  node-v10.10.0-x86.msi
dfa6da0633e1e962c5a903909923e91b5459f5da7ce8bd5fb4398deedefd7453  win-x64/node.exe
35a2fe13f023fe12c9f8c6787447330f34e5f5e635ed4740302b778fea0b4634  win-x64/node.lib
c5b41aca2f0aea058b9a3efb0bb643ca226f756c2f98c5105769af2239c5a5e2  win-x64/node_pdb.7z
128713d65cd29c67b970314ffb2153984129121c878a660e155eae808af3b2bb  win-x64/node_pdb.zip
d5f9d3ecde5e73d9b7fd3310bb463a6d5d50b238fdeae9ac30fdb03e5f2b54f6  win-x86/node.exe
5177bdb144ed30a22e722032a99513ceace5de1700f8f7b322dbdbd6c5150e18  win-x86/node.lib
8ea845327af7f76408da3073e492e54477957e365390466a8cc33ff0cd06ad16  win-x86/node_pdb.7z
f09d540bac39835fbdc217bf49c6b96b742150b6e728a75280e2af468a9a2dcc  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEj8yhP+8dDC6RAI4Jdw96mlrhVgAFAluRoy0ACgkQdw96mlrh
VgB3DA/+Pxz6zGoNxFugRUvxP5XS0DWmqg5a6/AX72NWoA0A7XiVVBN+3ruimRcQ
B/GsZBFEMxqRZNvCXQkJT5N5bV0TNyfZxhysbgt6jBM8H9CQMrAnP17SmxGc7V99
M2LnI2dsNr8U/HPs+L02kUjwJtfiq/claR55SpJBP8GRitw0gmsZNgbQ6Ze9knUh
rSNeM8LdI+blm0HVuh9n2NIgtxIe9Cbhlrz4Q6Z32pJNcB0AKJBXahMLKTvqqnsP
CFWcM8pmXtDvisbCx1+jL8QJ5lOrpCEDZE3UT3uN3m0wYOcd5oG+fpiwhyZ3UeTB
dFFoSmjWtPMMrpvA0e2IV7tw3pxyUBlCpreWN/WaZCnmwIor8YtfoK17PxqiXuje
R0bZ8kiL2xFdLEgvIVxB/fcEPbzhdf3P1GADhEX+E+UX36ryOIVb85lg39LFyRUU
JfGAaOyiL0sJ1ah4eIOssHaYy+UGhjLSh9DQSJWLXNiauKR7umnXdB0fGWBASeTV
G/bGz+2ngOSpKzbkanYFp1AJqeu9ciIEvo6bbBLhfaUHSP03gIcjThfTopk6MCOf
HFnVJdrfdWN6I3HznXQvKR6AYsQdubmy/PyQoApRvK5BqIYAwP9d37qMrQSEQZo3
212YltwTguzNxriue1N+9ik2y8BllauV23aaSrNRVEX1xbaFkOI=
=orV1
-----END PGP SIGNATURE-----

```
