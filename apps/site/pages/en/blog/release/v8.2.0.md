---
date: '2017-07-19T22:41:05.171Z'
category: release
title: Node v8.2.0 (Current)
layout: blog-post
author: <PERSON>
---

Big thanks to @addaleax who prepared the vast majority of this release.

### Notable changes

- **Async Hooks**

  - Multiple improvements to Promise support in `async_hooks` have been made.

- **Build**

  - The compiler version requirement to build Node with GCC has been raised to
    GCC 4.9.4.
    [[`820b011ed6`](https://github.com/nodejs/node/commit/820b011ed6)]
    [#13466](https://github.com/nodejs/node/pull/13466)

- **Cluster**

  - Users now have more fine-grained control over the inspector port used by
    individual cluster workers. Previously, cluster workers were restricted to
    incrementing from the master's debug port.
    [[`dfc46e262a`](https://github.com/nodejs/node/commit/dfc46e262a)]
    [#14140](https://github.com/nodejs/node/pull/14140)

- **DNS**

  - The server used for DNS queries can now use a custom port.
    [[`ebe7bb29aa`](https://github.com/nodejs/node/commit/ebe7bb29aa)]
    [#13723](https://github.com/nodejs/node/pull/13723)
  - Support for `dns.resolveAny()` has been added.
    [[`6e30e2558e`](https://github.com/nodejs/node/commit/6e30e2558e)]
    [#13137](https://github.com/nodejs/node/pull/13137)

- **npm**
  - The `npm` CLI has been updated to version 5.3.0. In particular, it now comes
    with the `npx` binary, which is also shipped with Node.
    [[`dc3f6b9ac1`](https://github.com/nodejs/node/commit/dc3f6b9ac1)]
    [#14235](https://github.com/nodejs/node/pull/14235)
  - `npm` Changelogs:
    - [v5.0.4](https://github.com/npm/npm/releases/tag/v5.0.4)
    - [v5.1.0](https://github.com/npm/npm/releases/tag/v5.1.0)
    - [v5.2.0](https://github.com/npm/npm/releases/tag/v5.2.0)
    - [v5.3.0](https://github.com/npm/npm/releases/tag/v5.3.0)

### Commits

- [[`53c52ac38e`](https://github.com/nodejs/node/commit/53c52ac38e)] - **N-API**: Reuse ObjectTemplate instances (Gabriel Schulhof) [#13999](https://github.com/nodejs/node/pull/13999)
- [[`86c06c01ec`](https://github.com/nodejs/node/commit/86c06c01ec)] - **async-hooks,net**: ensure asyncId=null if no handle (Matt Sergeant) [#13938](https://github.com/nodejs/node/pull/13938)
- [[`71ee15d340`](https://github.com/nodejs/node/commit/71ee15d340)] - **async_hooks**: make AsyncResource match emitInit (Andreas Madsen) [#14152](https://github.com/nodejs/node/pull/14152)
- [[`1aac2c09e7`](https://github.com/nodejs/node/commit/1aac2c09e7)] - **async_hooks**: rename internal emit functions (Andreas Madsen) [#14152](https://github.com/nodejs/node/pull/14152)
- [[`0c69ec12a9`](https://github.com/nodejs/node/commit/0c69ec12a9)] - **async_hooks**: fix nested hooks mutation (Andreas Madsen) [#14143](https://github.com/nodejs/node/pull/14143)
- [[`3211eff935`](https://github.com/nodejs/node/commit/3211eff935)] - **async_hooks**: move restoreTmpHooks call to init (Ruben Bridgewater) [#14054](https://github.com/nodejs/node/pull/14054)
- [[`76ba1b59bc`](https://github.com/nodejs/node/commit/76ba1b59bc)] - **async_hooks**: C++ Embedder API overhaul (Andreas Madsen) [#14040](https://github.com/nodejs/node/pull/14040)
- [[`544300ee48`](https://github.com/nodejs/node/commit/544300ee48)] - **async_hooks**: require parameter in emitBefore (Andreas Madsen) [#14050](https://github.com/nodejs/node/pull/14050)
- [[`9f66f1924f`](https://github.com/nodejs/node/commit/9f66f1924f)] - **async_hooks**: use common emitBefore and emitAfter (Andreas Madsen) [#14050](https://github.com/nodejs/node/pull/14050)
- [[`7b369d12cf`](https://github.com/nodejs/node/commit/7b369d12cf)] - **async_hooks**: fix default nextTick triggerAsyncId (Andreas Madsen) [#14026](https://github.com/nodejs/node/pull/14026)
- [[`2eabd92639`](https://github.com/nodejs/node/commit/2eabd92639)] - **async_hooks**: reduce duplication with factory (Ruben Bridgewater) [#13755](https://github.com/nodejs/node/pull/13755)
- [[`8f37f5dd01`](https://github.com/nodejs/node/commit/8f37f5dd01)] - **async_hooks**: proper id stacking for Promises (Anna Henningsen) [#13585](https://github.com/nodejs/node/pull/13585)
- [[`3bb4ec80ae`](https://github.com/nodejs/node/commit/3bb4ec80ae)] - **(SEMVER-MINOR)** **async_hooks**: rename currentId and triggerId (Andreas Madsen) [#13490](https://github.com/nodejs/node/pull/13490)
- [[`8b57b09c15`](https://github.com/nodejs/node/commit/8b57b09c15)] - **_Revert_** "**async_hooks**: only set up hooks if used" (Trevor Norris) [#13509](https://github.com/nodejs/node/pull/13509)
- [[`a44260326c`](https://github.com/nodejs/node/commit/a44260326c)] - **(SEMVER-MINOR)** **async_hooks**: use resource objects for Promises (Anna Henningsen) [#13452](https://github.com/nodejs/node/pull/13452)
- [[`2122e2fe89`](https://github.com/nodejs/node/commit/2122e2fe89)] - **async_wrap**: use kTotals to enable PromiseHook (Trevor Norris) [#13509](https://github.com/nodejs/node/pull/13509)
- [[`96279e83e7`](https://github.com/nodejs/node/commit/96279e83e7)] - **async_wrap**: expose enable/disablePromiseHook API (Anna Henningsen) [#13509](https://github.com/nodejs/node/pull/13509)
- [[`1c0f20fcf3`](https://github.com/nodejs/node/commit/1c0f20fcf3)] - **benchmark**: fix typo in inspect-proxy (Vse Mozhet Byt) [#14237](https://github.com/nodejs/node/pull/14237)
- [[`65a2e80596`](https://github.com/nodejs/node/commit/65a2e80596)] - **benchmark**: Improve event performance tests. (Benedikt Meurer) [#14052](https://github.com/nodejs/node/pull/14052)
- [[`3d0b66a7c2`](https://github.com/nodejs/node/commit/3d0b66a7c2)] - **benchmark,lib,test**: use braces for multiline block (Rich Trott) [#13995](https://github.com/nodejs/node/pull/13995)
- [[`bed13444b1`](https://github.com/nodejs/node/commit/bed13444b1)] - **buffer**: remove MAX_SAFE_INTEGER check on length (Rich Trott) [#14131](https://github.com/nodejs/node/pull/14131)
- [[`683f743e61`](https://github.com/nodejs/node/commit/683f743e61)] - **(SEMVER-MINOR)** **buffer**: support boxed strings and toPrimitive (James M Snell) [#13725](https://github.com/nodejs/node/pull/13725)
- [[`7794030700`](https://github.com/nodejs/node/commit/7794030700)] - **(SEMVER-MINOR)** **buffer**: add constants object (Anna Henningsen) [#13467](https://github.com/nodejs/node/pull/13467)
- [[`1444601a57`](https://github.com/nodejs/node/commit/1444601a57)] - **build**: prevent VsDevCmd.bat from changing cwd (Nikolai Vavilov) [#14303](https://github.com/nodejs/node/pull/14303)
- [[`6b052e7c42`](https://github.com/nodejs/node/commit/6b052e7c42)] - **(SEMVER-MINOR)** **build**: add npx to installers (Kat Marchán) [#14235](https://github.com/nodejs/node/pull/14235)
- [[`922f58f8ca`](https://github.com/nodejs/node/commit/922f58f8ca)] - **build**: run test-hash-seed at the end of test-v8 (Michaël Zasso) [#14219](https://github.com/nodejs/node/pull/14219)
- [[`b757105862`](https://github.com/nodejs/node/commit/b757105862)] - **build**: allow enabling the --trace-maps flag in V8 (Evan Lucas) [#14018](https://github.com/nodejs/node/pull/14018)
- [[`9ee271d92b`](https://github.com/nodejs/node/commit/9ee271d92b)] - **build**: split up cpplint to avoid long cmd lines (Kyle Farnung) [#14116](https://github.com/nodejs/node/pull/14116)
- [[`651af59e6b`](https://github.com/nodejs/node/commit/651af59e6b)] - **build**: add async-hooks testing to vcbuild.bat (Refael Ackermann) [#13381](https://github.com/nodejs/node/pull/13381)
- [[`c972364848`](https://github.com/nodejs/node/commit/c972364848)] - **build**: remove dependency on icu io library (Ben Noordhuis) [#13656](https://github.com/nodejs/node/pull/13656)
- [[`f2d7b803f1`](https://github.com/nodejs/node/commit/f2d7b803f1)] - **build**: clean up config_fips.gypi (Daniel Bevenius) [#13837](https://github.com/nodejs/node/pull/13837)
- [[`897405d62c`](https://github.com/nodejs/node/commit/897405d62c)] - **build,win**: skip `vcvarsall.bat` if env is set (Refael Ackermann) [#13806](https://github.com/nodejs/node/pull/13806)
- [[`dc0ae8be56`](https://github.com/nodejs/node/commit/dc0ae8be56)] - **build,win**: respect VS version for building addons (João Reis) [#13911](https://github.com/nodejs/node/pull/13911)
- [[`cd9ef939ba`](https://github.com/nodejs/node/commit/cd9ef939ba)] - **build,win**: use latest installed VS by default (João Reis) [#13911](https://github.com/nodejs/node/pull/13911)
- [[`79ead795b9`](https://github.com/nodejs/node/commit/79ead795b9)] - **build,windows**: restore DISTTYPEDIR (Refael Ackermann) [#13969](https://github.com/nodejs/node/pull/13969)
- [[`949f7be5a0`](https://github.com/nodejs/node/commit/949f7be5a0)] - **build,windows**: implement PEP514 python detection (Refael Ackermann) [#13900](https://github.com/nodejs/node/pull/13900)
- [[`096080b69c`](https://github.com/nodejs/node/commit/096080b69c)] - **child_process**: refactor normalizeSpawnArguments() (Rich Trott) [#14149](https://github.com/nodejs/node/pull/14149)
- [[`09eb58894e`](https://github.com/nodejs/node/commit/09eb58894e)] - **child_process**: fix handleless NODE_HANDLE handling (Santiago Gimeno) [#13235](https://github.com/nodejs/node/pull/13235)
- [[`16f2600ecf`](https://github.com/nodejs/node/commit/16f2600ecf)] - **child_process**: emit IPC messages on next tick (cjihrig) [#13856](https://github.com/nodejs/node/pull/13856)
- [[`dfc46e262a`](https://github.com/nodejs/node/commit/dfc46e262a)] - **(SEMVER-MINOR)** **cluster**: overriding inspector port (cornholio) [#14140](https://github.com/nodejs/node/pull/14140)
- [[`26f85e75f9`](https://github.com/nodejs/node/commit/26f85e75f9)] - **cluster**: remove obsolete todo (Ruben Bridgewater) [#13734](https://github.com/nodejs/node/pull/13734)
- [[`816f98f5d0`](https://github.com/nodejs/node/commit/816f98f5d0)] - **console**: use a plain object for the the error stack (Ruben Bridgewater) [#13743](https://github.com/nodejs/node/pull/13743)
- [[`932791063b`](https://github.com/nodejs/node/commit/932791063b)] - **(SEMVER-MINOR)** **deps**: hotfix to bump npx version (Kat Marchán) [#14235](https://github.com/nodejs/node/pull/14235)
- [[`dc3f6b9ac1`](https://github.com/nodejs/node/commit/dc3f6b9ac1)] - **(SEMVER-MINOR)** **deps**: upgrade npm to 5.3.0 (Kat Marchán) [#14235](https://github.com/nodejs/node/pull/14235)
- [[`fe6ca44f84`](https://github.com/nodejs/node/commit/fe6ca44f84)] - **deps**: upgrade libuv to 1.13.1 (cjihrig) [#14117](https://github.com/nodejs/node/pull/14117)
- [[`46cc80abf5`](https://github.com/nodejs/node/commit/46cc80abf5)] - **deps**: delete deps/icu-small/source/io (Ben Noordhuis) [#13656](https://github.com/nodejs/node/pull/13656)
- [[`6e30e2558e`](https://github.com/nodejs/node/commit/6e30e2558e)] - **(SEMVER-MINOR)** **dns**: add resolveAny support (XadillaX) [#13137](https://github.com/nodejs/node/pull/13137)
- [[`ebe7bb29aa`](https://github.com/nodejs/node/commit/ebe7bb29aa)] - **(SEMVER-MINOR)** **dns**: make `dns.setServers` support customized port (XadillaX) [#13723](https://github.com/nodejs/node/pull/13723)
- [[`7df10f529d`](https://github.com/nodejs/node/commit/7df10f529d)] - **doc**: fix inspectPort documentation in cluster.md (Anna Henningsen) [#14349](https://github.com/nodejs/node/pull/14349)
- [[`7a116d4a60`](https://github.com/nodejs/node/commit/7a116d4a60)] - **doc**: add guidance on testing new errors (Michael Dawson) [#14207](https://github.com/nodejs/node/pull/14207)
- [[`6f13d7da67`](https://github.com/nodejs/node/commit/6f13d7da67)] - **doc**: move LTS README link to increase prominence (Gibson Fahnestock) [#14259](https://github.com/nodejs/node/pull/14259)
- [[`c0703f0d4c`](https://github.com/nodejs/node/commit/c0703f0d4c)] - **(SEMVER-MINOR)** **doc**: fixes in cluster.md (cornholio) [#14140](https://github.com/nodejs/node/pull/14140)
- [[`e91a7a447d`](https://github.com/nodejs/node/commit/e91a7a447d)] - **doc**: update umask for clarity (James Sumners) [#14170](https://github.com/nodejs/node/pull/14170)
- [[`157ef23fc3`](https://github.com/nodejs/node/commit/157ef23fc3)] - **doc**: add notice about useGlobal option in repl docs (starkwang) [#13866](https://github.com/nodejs/node/pull/13866)
- [[`1b3cf97198`](https://github.com/nodejs/node/commit/1b3cf97198)] - **doc**: prefix of the stacktrace in errors.md (Roman Shoryn) [#14150](https://github.com/nodejs/node/pull/14150)
- [[`eb90ad61fb`](https://github.com/nodejs/node/commit/eb90ad61fb)] - **doc**: add missing space (Timothy Gu) [#14181](https://github.com/nodejs/node/pull/14181)
- [[`01b98a769f`](https://github.com/nodejs/node/commit/01b98a769f)] - **doc**: removed redundant mentions to error codes (jklepatch) [#13627](https://github.com/nodejs/node/pull/13627)
- [[`575dcdcf0e`](https://github.com/nodejs/node/commit/575dcdcf0e)] - **doc**: correct stream Duplex allowHalfOpen doc (Rich Trott) [#14127](https://github.com/nodejs/node/pull/14127)
- [[`cfa5e0c3b6`](https://github.com/nodejs/node/commit/cfa5e0c3b6)] - **doc**: note 'resize' event conditions on Windows (Dean Coakley) [#13576](https://github.com/nodejs/node/pull/13576)
- [[`217e1dc7b1`](https://github.com/nodejs/node/commit/217e1dc7b1)] - **doc**: fix mistake in http.md (Moogen Tian) [#14126](https://github.com/nodejs/node/pull/14126)
- [[`32ddb666b6`](https://github.com/nodejs/node/commit/32ddb666b6)] - **doc**: match debugger output & instructions to master behavior (Jan Krems) [#13885](https://github.com/nodejs/node/pull/13885)
- [[`9e6a4d6e27`](https://github.com/nodejs/node/commit/9e6a4d6e27)] - **doc**: add documentation on ICU (Timothy Gu) [#13916](https://github.com/nodejs/node/pull/13916)
- [[`23c67de3df`](https://github.com/nodejs/node/commit/23c67de3df)] - **doc**: fix padding mode of crypto.publicDecrypt (MoonBall) [#14036](https://github.com/nodejs/node/pull/14036)
- [[`99f0a6bdb5`](https://github.com/nodejs/node/commit/99f0a6bdb5)] - **doc**: add CTC members to Collaborators list (Rich Trott) [#13284](https://github.com/nodejs/node/pull/13284)
- [[`199e905249`](https://github.com/nodejs/node/commit/199e905249)] - **doc**: fix example in child_process.md (Ruslan Iusupov) [#13716](https://github.com/nodejs/node/pull/13716)
- [[`310040c89e`](https://github.com/nodejs/node/commit/310040c89e)] - **doc**: add default values to functions in fs.md (Matej Krajčovič) [#13767](https://github.com/nodejs/node/pull/13767)
- [[`26ed901730`](https://github.com/nodejs/node/commit/26ed901730)] - **doc**: fix some broken references (Alexander Gromnitsky) [#13811](https://github.com/nodejs/node/pull/13811)
- [[`e36561a828`](https://github.com/nodejs/node/commit/e36561a828)] - **doc**: move module-specific "globals" to modules.md (Tobias Nießen) [#13962](https://github.com/nodejs/node/pull/13962)
- [[`f1d92fb489`](https://github.com/nodejs/node/commit/f1d92fb489)] - **doc**: fix indentation issues in sample code (Rich Trott) [#13950](https://github.com/nodejs/node/pull/13950)
- [[`f53bfe4945`](https://github.com/nodejs/node/commit/f53bfe4945)] - **doc**: use stricter indentation checking for docs (Rich Trott) [#13950](https://github.com/nodejs/node/pull/13950)
- [[`adb0f4601d`](https://github.com/nodejs/node/commit/adb0f4601d)] - **doc**: note that fs.futimes only works on AIX \>7.1 (Gibson Fahnestock) [#13659](https://github.com/nodejs/node/pull/13659)
- [[`8fe77225ab`](https://github.com/nodejs/node/commit/8fe77225ab)] - **doc**: add @nodejs/documentation to CC table (Vse Mozhet Byt) [#13952](https://github.com/nodejs/node/pull/13952)
- [[`4c43ff271f`](https://github.com/nodejs/node/commit/4c43ff271f)] - **doc**: doc lifetime of n-api last error info (Michael Dawson) [#13939](https://github.com/nodejs/node/pull/13939)
- [[`7332e7ef5c`](https://github.com/nodejs/node/commit/7332e7ef5c)] - **doc**: add gireeshpunathil to collaborators (Gireesh Punathil) [#13967](https://github.com/nodejs/node/pull/13967)
- [[`9ff5212d5f`](https://github.com/nodejs/node/commit/9ff5212d5f)] - **doc**: fix mistake in path.relative (Tobias Nießen) [#13912](https://github.com/nodejs/node/pull/13912)
- [[`0fc7a5077f`](https://github.com/nodejs/node/commit/0fc7a5077f)] - **doc**: unify ERR_FALSY_VALUE_REJECTION description (Tobias Nießen) [#13869](https://github.com/nodejs/node/pull/13869)
- [[`502be7c085`](https://github.com/nodejs/node/commit/502be7c085)] - **doc**: fixed formatting issue in cli docs (Chris Young) [#13808](https://github.com/nodejs/node/pull/13808)
- [[`12b6765cd1`](https://github.com/nodejs/node/commit/12b6765cd1)] - **doc**: fix link in async_hooks.md (Azard) [#13930](https://github.com/nodejs/node/pull/13930)
- [[`04bca73bd7`](https://github.com/nodejs/node/commit/04bca73bd7)] - **doc**: add missing zlib link to stream API docs (Rob Wu) [#13838](https://github.com/nodejs/node/pull/13838)
- [[`f1b7e8d50d`](https://github.com/nodejs/node/commit/f1b7e8d50d)] - **doc**: fix nits in guides/using-internal-errors.md (Vse Mozhet Byt) [#13820](https://github.com/nodejs/node/pull/13820)
- [[`46756acb95`](https://github.com/nodejs/node/commit/46756acb95)] - **doc**: document res.connection and res.socket (Justin Beckwith) [#13617](https://github.com/nodejs/node/pull/13617)
- [[`70f3935130`](https://github.com/nodejs/node/commit/70f3935130)] - **doc**: fix api docs style (Daijiro Wachi) [#13700](https://github.com/nodejs/node/pull/13700)
- [[`820b011ed6`](https://github.com/nodejs/node/commit/820b011ed6)] - **doc**: update minimum g++ version to 4.9.4 (Ben Noordhuis) [#13466](https://github.com/nodejs/node/pull/13466)
- [[`d4a6ca6ed3`](https://github.com/nodejs/node/commit/d4a6ca6ed3)] - **doc, util, console**: clarify ambiguous docs (Natanael Log) [#14027](https://github.com/nodejs/node/pull/14027)
- [[`4f0eb6f024`](https://github.com/nodejs/node/commit/4f0eb6f024)] - **doc,test**: fs - reserved characters under win32 (XadillaX) [#13875](https://github.com/nodejs/node/pull/13875)
- [[`ad8b1588a2`](https://github.com/nodejs/node/commit/ad8b1588a2)] - **errors**: prevent stack recalculation (Ruben Bridgewater) [#13743](https://github.com/nodejs/node/pull/13743)
- [[`e8780ba7ae`](https://github.com/nodejs/node/commit/e8780ba7ae)] - **errors**: add missing ERR\_ prefix on util.callbackify error (James M Snell) [#13750](https://github.com/nodejs/node/pull/13750)
- [[`2a02868934`](https://github.com/nodejs/node/commit/2a02868934)] - **fs**: two minor optimizations (Ruben Bridgewater) [#14055](https://github.com/nodejs/node/pull/14055)
- [[`4587f21716`](https://github.com/nodejs/node/commit/4587f21716)] - **gyp**: implement LD/LDXX for ninja and FIPS (Sam Roberts) [#14227](https://github.com/nodejs/node/pull/14227)
- [[`63aee3b4c8`](https://github.com/nodejs/node/commit/63aee3b4c8)] - **http**: OutgoingMessage change writable after end (Roee Kasher) [#14024](https://github.com/nodejs/node/pull/14024)
- [[`c652845a61`](https://github.com/nodejs/node/commit/c652845a61)] - **http**: guard against failed sockets creation (Refael Ackermann) [#13839](https://github.com/nodejs/node/pull/13839)
- [[`b22a04b2c6`](https://github.com/nodejs/node/commit/b22a04b2c6)] - **http**: always cork outgoing writes (Brian White) [#13522](https://github.com/nodejs/node/pull/13522)
- [[`74741fa52b`](https://github.com/nodejs/node/commit/74741fa52b)] - **(SEMVER-MINOR)** **https**: make opts optional & immutable when create (XadillaX) [#13599](https://github.com/nodejs/node/pull/13599)
- [[`a45792a383`](https://github.com/nodejs/node/commit/a45792a383)] - **inspector**: perform DNS lookup for host (Eugene Ostroukhov) [#13478](https://github.com/nodejs/node/pull/13478)
- [[`b0db2b9fc2`](https://github.com/nodejs/node/commit/b0db2b9fc2)] - **inspector, test**: Fix test bug detected by Coverity (Eugene Ostroukhov) [#13799](https://github.com/nodejs/node/pull/13799)
- [[`6361565915`](https://github.com/nodejs/node/commit/6361565915)] - **lib**: update indentation of ternaries (Rich Trott) [#14247](https://github.com/nodejs/node/pull/14247)
- [[`b12b8c2f7c`](https://github.com/nodejs/node/commit/b12b8c2f7c)] - **lib**: normalize indentation in parentheses (Rich Trott) [#14125](https://github.com/nodejs/node/pull/14125)
- [[`a0866b6b0c`](https://github.com/nodejs/node/commit/a0866b6b0c)] - **lib**: remove excess indentation (Rich Trott) [#14090](https://github.com/nodejs/node/pull/14090)
- [[`07642552cb`](https://github.com/nodejs/node/commit/07642552cb)] - **lib**: use consistent indentation for ternaries (Rich Trott) [#14078](https://github.com/nodejs/node/pull/14078)
- [[`4bb1a3a8ac`](https://github.com/nodejs/node/commit/4bb1a3a8ac)] - **lib**: fix typos (Ruben Bridgewater) [#14044](https://github.com/nodejs/node/pull/14044)
- [[`3bd18c51e0`](https://github.com/nodejs/node/commit/3bd18c51e0)] - **n-api**: add napi_fatal_error API (Kyle Farnung) [#13971](https://github.com/nodejs/node/pull/13971)
- [[`b1eb6d5485`](https://github.com/nodejs/node/commit/b1eb6d5485)] - **n-api**: wrap test macros in do/while (Kyle Farnung) [#14095](https://github.com/nodejs/node/pull/14095)
- [[`f2054f330a`](https://github.com/nodejs/node/commit/f2054f330a)] - **n-api**: Implement stricter wrapping (Gabriel Schulhof) [#13872](https://github.com/nodejs/node/pull/13872)
- [[`e25c5ef7da`](https://github.com/nodejs/node/commit/e25c5ef7da)] - **n-api**: fix warning in test_general (Daniel Bevenius) [#14104](https://github.com/nodejs/node/pull/14104)
- [[`2a86650562`](https://github.com/nodejs/node/commit/2a86650562)] - **n-api**: add napi_has_own_property() (cjihrig) [#14063](https://github.com/nodejs/node/pull/14063)
- [[`f3933049e5`](https://github.com/nodejs/node/commit/f3933049e5)] - **n-api**: fix -Wmaybe-uninitialized compiler warning (Ben Noordhuis) [#14053](https://github.com/nodejs/node/pull/14053)
- [[`de744ba232`](https://github.com/nodejs/node/commit/de744ba232)] - **n-api**: use Maybe version of Object::SetPrototype() (Ben Noordhuis) [#14053](https://github.com/nodejs/node/pull/14053)
- [[`820d97df5d`](https://github.com/nodejs/node/commit/820d97df5d)] - **n-api**: add napi_delete_property() (cjihrig) [#13934](https://github.com/nodejs/node/pull/13934)
- [[`6316c9a0f8`](https://github.com/nodejs/node/commit/6316c9a0f8)] - **n-api**: add napi_delete_element() (cjihrig) [#13949](https://github.com/nodejs/node/pull/13949)
- [[`4843d4da8c`](https://github.com/nodejs/node/commit/4843d4da8c)] - **n-api**: fix section title typo (Kyle Farnung) [#13972](https://github.com/nodejs/node/pull/13972)
- [[`a839aede3e`](https://github.com/nodejs/node/commit/a839aede3e)] - **(SEMVER-MINOR)** **net**: return this from getConnections() (Sam Roberts) [#13553](https://github.com/nodejs/node/pull/13553)
- [[`69f806cc55`](https://github.com/nodejs/node/commit/69f806cc55)] - **(SEMVER-MINOR)** **net**: return this from destroy() (Sam Roberts) [#13530](https://github.com/nodejs/node/pull/13530)
- [[`e30fc2c5ba`](https://github.com/nodejs/node/commit/e30fc2c5ba)] - **process**: improve nextTick() performance (Brian White) [#13446](https://github.com/nodejs/node/pull/13446)
- [[`c56a89013c`](https://github.com/nodejs/node/commit/c56a89013c)] - **querystring**: fix up lastPos usage (Timothy Gu) [#14151](https://github.com/nodejs/node/pull/14151)
- [[`b4b27b2edd`](https://github.com/nodejs/node/commit/b4b27b2edd)] - **readline**: properly handle 0-width characters (Timothy Gu) [#13918](https://github.com/nodejs/node/pull/13918)
- [[`3683f6b787`](https://github.com/nodejs/node/commit/3683f6b787)] - **repl**: fix crash with large buffer tab completion (XadillaX) [#13817](https://github.com/nodejs/node/pull/13817)
- [[`f237ad55ff`](https://github.com/nodejs/node/commit/f237ad55ff)] - **src**: fix memory leak in DH key setters (Ben Noordhuis) [#14122](https://github.com/nodejs/node/pull/14122)
- [[`0bbdb78962`](https://github.com/nodejs/node/commit/0bbdb78962)] - **src**: reduce allocations in exportPublicKey() (Ben Noordhuis) [#14122](https://github.com/nodejs/node/pull/14122)
- [[`e4b70199b3`](https://github.com/nodejs/node/commit/e4b70199b3)] - **src**: guard against double free in randomBytes() (Ben Noordhuis) [#14122](https://github.com/nodejs/node/pull/14122)
- [[`ad0669bfe6`](https://github.com/nodejs/node/commit/ad0669bfe6)] - **src**: simplify PBKDF2Request (Ben Noordhuis) [#14122](https://github.com/nodejs/node/pull/14122)
- [[`8f4b84ba42`](https://github.com/nodejs/node/commit/8f4b84ba42)] - **src**: remove PBKDF2Request::release() (Ben Noordhuis) [#14122](https://github.com/nodejs/node/pull/14122)
- [[`b5802c7bf1`](https://github.com/nodejs/node/commit/b5802c7bf1)] - **src**: avoid heap allocation in crypto.pbkdf2() (Ben Noordhuis) [#14122](https://github.com/nodejs/node/pull/14122)
- [[`1c3e090eba`](https://github.com/nodejs/node/commit/1c3e090eba)] - **src**: make array arg length compile-time checkable (Ben Noordhuis) [#14122](https://github.com/nodejs/node/pull/14122)
- [[`41f79fb22f`](https://github.com/nodejs/node/commit/41f79fb22f)] - **src**: refactor PBKDF2Request (Ben Noordhuis) [#14122](https://github.com/nodejs/node/pull/14122)
- [[`233740c594`](https://github.com/nodejs/node/commit/233740c594)] - **src**: remove extra heap allocations in DH functions (Ben Noordhuis) [#14122](https://github.com/nodejs/node/pull/14122)
- [[`8e51d3151d`](https://github.com/nodejs/node/commit/8e51d3151d)] - **src**: avoid heap allocation in hmac.digest() (Ben Noordhuis) [#14122](https://github.com/nodejs/node/pull/14122)
- [[`8be9bd139f`](https://github.com/nodejs/node/commit/8be9bd139f)] - **src**: remove extra heap allocation in GetSession() (Ben Noordhuis) [#14122](https://github.com/nodejs/node/pull/14122)
- [[`8dd6866303`](https://github.com/nodejs/node/commit/8dd6866303)] - **src**: make CipherBase::kind\_ const (Ben Noordhuis) [#14122](https://github.com/nodejs/node/pull/14122)
- [[`0fcb8b1029`](https://github.com/nodejs/node/commit/0fcb8b1029)] - **src**: remove unused Local (Ben Noordhuis) [#14122](https://github.com/nodejs/node/pull/14122)
- [[`db65422f0d`](https://github.com/nodejs/node/commit/db65422f0d)] - **src**: remove superfluous cipher\_ data member (Ben Noordhuis) [#14122](https://github.com/nodejs/node/pull/14122)
- [[`1af064bf7c`](https://github.com/nodejs/node/commit/1af064bf7c)] - **src**: don't heap allocate GCM cipher auth tag (Ben Noordhuis) [#14122](https://github.com/nodejs/node/pull/14122)
- [[`174f8c8d91`](https://github.com/nodejs/node/commit/174f8c8d91)] - **src**: avoid heap allocation in sign.final() (Ben Noordhuis) [#14122](https://github.com/nodejs/node/pull/14122)
- [[`efb7aef676`](https://github.com/nodejs/node/commit/efb7aef676)] - **src**: remove unneeded const_cast (Ben Noordhuis) [#14122](https://github.com/nodejs/node/pull/14122)
- [[`2ee31aa261`](https://github.com/nodejs/node/commit/2ee31aa261)] - **src**: remove extra heap allocations in CipherBase (Ben Noordhuis) [#14122](https://github.com/nodejs/node/pull/14122)
- [[`50913b168d`](https://github.com/nodejs/node/commit/50913b168d)] - **(SEMVER-MINOR)** **src**: whitelist v8 options with `'_'` or `'-'` (Sam Roberts) [#14093](https://github.com/nodejs/node/pull/14093)
- [[`b799498e8a`](https://github.com/nodejs/node/commit/b799498e8a)] - **src**: document --abort-on-uncaught-exception (Sam Roberts) [#13931](https://github.com/nodejs/node/pull/13931)
- [[`21ee4b1b97`](https://github.com/nodejs/node/commit/21ee4b1b97)] - **src**: --abort-on-uncaught-exception in NODE_OPTIONS (Sam Roberts) [#13932](https://github.com/nodejs/node/pull/13932)
- [[`ef67f7c8ca`](https://github.com/nodejs/node/commit/ef67f7c8ca)] - **src**: move crypto_bio/clienthello to crypto ns (Daniel Bevenius) [#13957](https://github.com/nodejs/node/pull/13957)
- [[`dff506c5c5`](https://github.com/nodejs/node/commit/dff506c5c5)] - **src**: add missing new line to printed message (Timothy Gu) [#13940](https://github.com/nodejs/node/pull/13940)
- [[`98cb59e9f0`](https://github.com/nodejs/node/commit/98cb59e9f0)] - **src**: revise character width calculation (Timothy Gu) [#13918](https://github.com/nodejs/node/pull/13918)
- [[`5579bc8fb6`](https://github.com/nodejs/node/commit/5579bc8fb6)] - **src,fs**: calculate times as unsigned long (Refael Ackermann) [#13281](https://github.com/nodejs/node/pull/13281)
- [[`864abc567e`](https://github.com/nodejs/node/commit/864abc567e)] - **src,lib,test,doc**: correct misspellings (Roman Reiss) [#13719](https://github.com/nodejs/node/pull/13719)
- [[`6eb53e5611`](https://github.com/nodejs/node/commit/6eb53e5611)] - **stream**: avoid possible slow path w UInt8Array (Matteo Collina) [#13956](https://github.com/nodejs/node/pull/13956)
- [[`6512fd7614`](https://github.com/nodejs/node/commit/6512fd7614)] - **stream**: improve Transform performance (Brian White) [#13322](https://github.com/nodejs/node/pull/13322)
- [[`86e55eff27`](https://github.com/nodejs/node/commit/86e55eff27)] - **test**: add test for http outgoing internal headers (Gergely Nemeth) [#13980](https://github.com/nodejs/node/pull/13980)
- [[`0f52b41cbd`](https://github.com/nodejs/node/commit/0f52b41cbd)] - **test**: use regex error check in test-crypto-random (Zhang Weijie) [#14273](https://github.com/nodejs/node/pull/14273)
- [[`bf663a8550`](https://github.com/nodejs/node/commit/bf663a8550)] - **test**: check error with regex in test-signal-safety (shaman) [#14285](https://github.com/nodejs/node/pull/14285)
- [[`784102f2d1`](https://github.com/nodejs/node/commit/784102f2d1)] - **test**: use regex error checks in test-util-format (Superwoods) [#14299](https://github.com/nodejs/node/pull/14299)
- [[`f9b292c954`](https://github.com/nodejs/node/commit/f9b292c954)] - **test**: change style in test-cli-bad-options (boydfd) [#14274](https://github.com/nodejs/node/pull/14274)
- [[`9257e7ef70`](https://github.com/nodejs/node/commit/9257e7ef70)] - **test**: use template literals in test-writewrap (vercent deng) [#14292](https://github.com/nodejs/node/pull/14292)
- [[`f5e8342057`](https://github.com/nodejs/node/commit/f5e8342057)] - **test**: improve regexps for error checking (xinglong.wangwxl) [#14271](https://github.com/nodejs/node/pull/14271)
- [[`337a8652c7`](https://github.com/nodejs/node/commit/337a8652c7)] - **test**: replace string concatenation with template (weiyuanyue) [#14279](https://github.com/nodejs/node/pull/14279)
- [[`85c181ab78`](https://github.com/nodejs/node/commit/85c181ab78)] - **test**: use template literals as appropriate (blade254353074) [#14289](https://github.com/nodejs/node/pull/14289)
- [[`65bccd519e`](https://github.com/nodejs/node/commit/65bccd519e)] - **test**: use template literal for string concat (tobewhatwewant) [#14288](https://github.com/nodejs/node/pull/14288)
- [[`802783d34a`](https://github.com/nodejs/node/commit/802783d34a)] - **test**: simplify string concatenation (jiangplus) [#14278](https://github.com/nodejs/node/pull/14278)
- [[`76a4671729`](https://github.com/nodejs/node/commit/76a4671729)] - **test**: use regexp to confir error message (Bang Wu) [#14268](https://github.com/nodejs/node/pull/14268)
- [[`e37510a0c7`](https://github.com/nodejs/node/commit/e37510a0c7)] - **test**: use regluar expression in vm test (akira.xue) [#14266](https://github.com/nodejs/node/pull/14266)
- [[`a338b94214`](https://github.com/nodejs/node/commit/a338b94214)] - **test**: use regular expression to match error msg (Flandre) [#14265](https://github.com/nodejs/node/pull/14265)
- [[`c8087c05e8`](https://github.com/nodejs/node/commit/c8087c05e8)] - **test**: replace string concat with template literal (Song, Bintao Garfield) [#14269](https://github.com/nodejs/node/pull/14269)
- [[`c44d899ca1`](https://github.com/nodejs/node/commit/c44d899ca1)] - **test**: check complete error message (Fraser Xu) [#14264](https://github.com/nodejs/node/pull/14264)
- [[`bf9457276b`](https://github.com/nodejs/node/commit/bf9457276b)] - **test**: fix flaky test-net-can-reset-timeout (Rich Trott) [#14257](https://github.com/nodejs/node/pull/14257)
- [[`9efd328d5d`](https://github.com/nodejs/node/commit/9efd328d5d)] - **test**: disable MultipleEnvironmentsPerIsolate (Refael Ackermann) [#14246](https://github.com/nodejs/node/pull/14246)
- [[`724e7e1acf`](https://github.com/nodejs/node/commit/724e7e1acf)] - **test**: make common.PIPE process unique (Refael Ackermann) [#14168](https://github.com/nodejs/node/pull/14168)
- [[`d651a01641`](https://github.com/nodejs/node/commit/d651a01641)] - **(SEMVER-MINOR)** **test**: reduce offset in test-inspector-port-cluster (cornholio) [#14140](https://github.com/nodejs/node/pull/14140)
- [[`f5bea638df`](https://github.com/nodejs/node/commit/f5bea638df)] - **test**: http outgoing \_renderHeaders (Peter Czibik) [#13981](https://github.com/nodejs/node/pull/13981)
- [[`1671fe4506`](https://github.com/nodejs/node/commit/1671fe4506)] - **test**: decrease duration of test-cli-syntax (Evan Lucas) [#14187](https://github.com/nodejs/node/pull/14187)
- [[`3fcc7e6772`](https://github.com/nodejs/node/commit/3fcc7e6772)] - **test**: handle missing V8 tests in n-api test (cjihrig) [#14123](https://github.com/nodejs/node/pull/14123)
- [[`3bc713e45a`](https://github.com/nodejs/node/commit/3bc713e45a)] - **test**: reduce run time for test-benchmark-crypto (Rich Trott) [#14189](https://github.com/nodejs/node/pull/14189)
- [[`73257045a5`](https://github.com/nodejs/node/commit/73257045a5)] - **test**: reduce run time for test-benchmark-http (Rich Trott) [#14180](https://github.com/nodejs/node/pull/14180)
- [[`cd9eba9da8`](https://github.com/nodejs/node/commit/cd9eba9da8)] - **test**: reduce test-benchmark-net run duration (Rich Trott) [#14183](https://github.com/nodejs/node/pull/14183)
- [[`de842498fa`](https://github.com/nodejs/node/commit/de842498fa)] - **test**: fix flaky test-https-set-timeout-server (Rich Trott) [#14134](https://github.com/nodejs/node/pull/14134)
- [[`e879a56aec`](https://github.com/nodejs/node/commit/e879a56aec)] - **test**: remove common.noop (Rich Trott) [#12822](https://github.com/nodejs/node/pull/12822)
- [[`697ea62f39`](https://github.com/nodejs/node/commit/697ea62f39)] - **test**: add get/set effective uid/gid tests (Evan Lucas) [#14091](https://github.com/nodejs/node/pull/14091)
- [[`d0e4e2b5c5`](https://github.com/nodejs/node/commit/d0e4e2b5c5)] - **test**: fix cctest failure on Windows (Jimmy Thomson) [#14111](https://github.com/nodejs/node/pull/14111)
- [[`e080fb349e`](https://github.com/nodejs/node/commit/e080fb349e)] - **test**: ignore connection errors for hostname check (Refael Ackermann) [#14073](https://github.com/nodejs/node/pull/14073)
- [[`9cfa52a568`](https://github.com/nodejs/node/commit/9cfa52a568)] - **test**: check and fail inspector-cluster-port-clash (Daniel Bevenius) [#14074](https://github.com/nodejs/node/pull/14074)
- [[`2a91d59c49`](https://github.com/nodejs/node/commit/2a91d59c49)] - **test**: add coverage for napi_typeof (Michael Dawson) [#13990](https://github.com/nodejs/node/pull/13990)
- [[`e71b98f9f7`](https://github.com/nodejs/node/commit/e71b98f9f7)] - **test**: restore no-op function in test (Rich Trott) [#14065](https://github.com/nodejs/node/pull/14065)
- [[`d288cf10cc`](https://github.com/nodejs/node/commit/d288cf10cc)] - **test**: skip test-fs-readdir-ucs2 if no support (Rich Trott) [#14029](https://github.com/nodejs/node/pull/14029)
- [[`32a8f368ab`](https://github.com/nodejs/node/commit/32a8f368ab)] - **test**: simplify test skipping (Vse Mozhet Byt) [#14021](https://github.com/nodejs/node/pull/14021)
- [[`0cc12fc646`](https://github.com/nodejs/node/commit/0cc12fc646)] - **test**: fix require nits in some test-tls-\* tests (Vse Mozhet Byt) [#14008](https://github.com/nodejs/node/pull/14008)
- [[`0707a6b2b5`](https://github.com/nodejs/node/commit/0707a6b2b5)] - **test**: refactor test-http-hostname-typechecking (Rich Trott) [#13993](https://github.com/nodejs/node/pull/13993)
- [[`534ae446c6`](https://github.com/nodejs/node/commit/534ae446c6)] - **test**: refactor test-http(s)-set-timeout-server (Alexey Orlenko) [#13935](https://github.com/nodejs/node/pull/13935)
- [[`81c644795d`](https://github.com/nodejs/node/commit/81c644795d)] - **test**: refactor test-http-invalidheaderfield (Rich Trott) [#13996](https://github.com/nodejs/node/pull/13996)
- [[`8edde98f16`](https://github.com/nodejs/node/commit/8edde98f16)] - **test**: change var to const in ./common (Ruben Bridgewater) [#13732](https://github.com/nodejs/node/pull/13732)
- [[`cfb6f94b30`](https://github.com/nodejs/node/commit/cfb6f94b30)] - **test**: mark test-npm-install flaky on arm (Refael Ackermann) [#14035](https://github.com/nodejs/node/pull/14035)
- [[`50ee4bd598`](https://github.com/nodejs/node/commit/50ee4bd598)] - **test**: replace indexOf with includes and startsWith (Nataly Shrits) [#13852](https://github.com/nodejs/node/pull/13852)
- [[`f1ef692454`](https://github.com/nodejs/node/commit/f1ef692454)] - **test**: refactor test-fs-options-immutable (Rich Trott) [#13977](https://github.com/nodejs/node/pull/13977)
- [[`bb198dcda9`](https://github.com/nodejs/node/commit/bb198dcda9)] - **test**: refactor test-crypto-pbkdf2 (Rich Trott) [#13975](https://github.com/nodejs/node/pull/13975)
- [[`4ba1d32609`](https://github.com/nodejs/node/commit/4ba1d32609)] - **test**: remove undef NDEBUG from at-exit addons test (Daniel Bevenius) [#13998](https://github.com/nodejs/node/pull/13998)
- [[`f400939206`](https://github.com/nodejs/node/commit/f400939206)] - **test**: verify napi_get_property() walks prototype (cjihrig) [#13961](https://github.com/nodejs/node/pull/13961)
- [[`100ccf9ad4`](https://github.com/nodejs/node/commit/100ccf9ad4)] - **test**: refactor test-fs-watchfile (Rich Trott) [#13721](https://github.com/nodejs/node/pull/13721)
- [[`f7383eb80e`](https://github.com/nodejs/node/commit/f7383eb80e)] - **test**: verify isNativeError accepts internal errors (cjihrig) [#13965](https://github.com/nodejs/node/pull/13965)
- [[`071ecb0dd2`](https://github.com/nodejs/node/commit/071ecb0dd2)] - **test**: refactor test-child-process-send-type-error (Rich Trott) [#13904](https://github.com/nodejs/node/pull/13904)
- [[`e5d32b8b13`](https://github.com/nodejs/node/commit/e5d32b8b13)] - **test**: mark test-fs-readdir-ucs2 flaky (João Reis) [#13989](https://github.com/nodejs/node/pull/13989)
- [[`fa9e647385`](https://github.com/nodejs/node/commit/fa9e647385)] - **test**: fix failure in test-icu-data-dir.js (Tobias Nießen) [#13987](https://github.com/nodejs/node/pull/13987)
- [[`b43547acc6`](https://github.com/nodejs/node/commit/b43547acc6)] - **test**: refactor test-cluster-basic (Rich Trott) [#13905](https://github.com/nodejs/node/pull/13905)
- [[`98ec8aaa30`](https://github.com/nodejs/node/commit/98ec8aaa30)] - **test**: refactor test-vm-sigint (Rich Trott) [#13902](https://github.com/nodejs/node/pull/13902)
- [[`949d1b1d4a`](https://github.com/nodejs/node/commit/949d1b1d4a)] - **test**: refactor test-tls-two-cas-one-string (Rich Trott) [#13896](https://github.com/nodejs/node/pull/13896)
- [[`c4018e8a48`](https://github.com/nodejs/node/commit/c4018e8a48)] - **test**: remove unneeded HandleScope usage (Ezequiel Garcia) [#13859](https://github.com/nodejs/node/pull/13859)
- [[`6120a0de6c`](https://github.com/nodejs/node/commit/6120a0de6c)] - **test**: skip fips tests using OpenSSL config file (Daniel Bevenius) [#13786](https://github.com/nodejs/node/pull/13786)
- [[`74aed0b6bd`](https://github.com/nodejs/node/commit/74aed0b6bd)] - **test**: refactor test-tls-invoked-queued (Rich Trott) [#13893](https://github.com/nodejs/node/pull/13893)
- [[`a767367123`](https://github.com/nodejs/node/commit/a767367123)] - **test**: refactor test-tls-env-extra-ca (Rich Trott) [#13886](https://github.com/nodejs/node/pull/13886)
- [[`265957334c`](https://github.com/nodejs/node/commit/265957334c)] - **test**: make http(s)-set-timeout-server more similar (Julien Klepatch) [#13822](https://github.com/nodejs/node/pull/13822)
- [[`587c905d11`](https://github.com/nodejs/node/commit/587c905d11)] - **test**: check uv_ip4_addr return value (Eugene Ostroukhov) [#13878](https://github.com/nodejs/node/pull/13878)
- [[`005e343339`](https://github.com/nodejs/node/commit/005e343339)] - **test**: remove `require('buffer')` from 4 test files (XadillaX) [#13844](https://github.com/nodejs/node/pull/13844)
- [[`df3c2929b9`](https://github.com/nodejs/node/commit/df3c2929b9)] - **test**: remove unnecessary require('buffer').Buffer (lena) [#13851](https://github.com/nodejs/node/pull/13851)
- [[`ec3761b1da`](https://github.com/nodejs/node/commit/ec3761b1da)] - **test**: remove `require('buffer')` from 4 test files (Zongmin Lei) [#13846](https://github.com/nodejs/node/pull/13846)
- [[`c3c6699bb3`](https://github.com/nodejs/node/commit/c3c6699bb3)] - **test**: remove require('buffer') from 4 buffer tests (OriLev) [#13855](https://github.com/nodejs/node/pull/13855)
- [[`4a6604193f`](https://github.com/nodejs/node/commit/4a6604193f)] - **test**: remove require('buffer') on 6 fs test files (sallen450) [#13845](https://github.com/nodejs/node/pull/13845)
- [[`76cdaec2b3`](https://github.com/nodejs/node/commit/76cdaec2b3)] - **test**: remove unnecessary Buffer import (Steven Winston) [#13860](https://github.com/nodejs/node/pull/13860)
- [[`b15378cc90`](https://github.com/nodejs/node/commit/b15378cc90)] - **test**: improve async-hooks/test-callback-error (Refael Ackermann) [#13559](https://github.com/nodejs/node/pull/13559)
- [[`7e3bab779a`](https://github.com/nodejs/node/commit/7e3bab779a)] - **test**: use string instead of RegExp in split() (Vse Mozhet Byt) [#13710](https://github.com/nodejs/node/pull/13710)
- [[`0e857a5ee4`](https://github.com/nodejs/node/commit/0e857a5ee4)] - **test**: remove needless RegExp flags (Vse Mozhet Byt) [#13690](https://github.com/nodejs/node/pull/13690)
- [[`022c6d080c`](https://github.com/nodejs/node/commit/022c6d080c)] - **test**: add crypto check to test-tls-wrap-econnreset (Daniel Bevenius) [#13691](https://github.com/nodejs/node/pull/13691)
- [[`bf22514ae4`](https://github.com/nodejs/node/commit/bf22514ae4)] - **test**: increase util.callbackify() coverage (cjihrig) [#13705](https://github.com/nodejs/node/pull/13705)
- [[`b717609e86`](https://github.com/nodejs/node/commit/b717609e86)] - **test,async_hooks**: match test-ttywrap.readstream (Trevor Norris) [#13991](https://github.com/nodejs/node/pull/13991)
- [[`1fc5c29f28`](https://github.com/nodejs/node/commit/1fc5c29f28)] - **test,async_hooks**: skip whether TTY is available (Trevor Norris) [#13991](https://github.com/nodejs/node/pull/13991)
- [[`3d9bc01734`](https://github.com/nodejs/node/commit/3d9bc01734)] - **test,async_hooks**: stabilize tests on Windows (Refael Ackermann) [#13381](https://github.com/nodejs/node/pull/13381)
- [[`b9e07f9fec`](https://github.com/nodejs/node/commit/b9e07f9fec)] - **test,fs**: delay unlink in test-regress-GH-4027.js (Jaime Bernardo) [#14010](https://github.com/nodejs/node/pull/14010)
- [[`e2d325403f`](https://github.com/nodejs/node/commit/e2d325403f)] - **(SEMVER-MINOR)** **tls**: add host and port info to ECONNRESET errors (José F. Romaniello) [#7476](https://github.com/nodejs/node/pull/7476)
- [[`55438024a6`](https://github.com/nodejs/node/commit/55438024a6)] - **tools**: update package.json `engine` field (AJ Jordan) [#14165](https://github.com/nodejs/node/pull/14165)
- [[`36c267cbe9`](https://github.com/nodejs/node/commit/36c267cbe9)] - **tools**: increase test timeouts (Rich Trott) [#14197](https://github.com/nodejs/node/pull/14197)
- [[`ef53149203`](https://github.com/nodejs/node/commit/ef53149203)] - **tools**: update ESLint to 4.2.0 (Rich Trott) [#14155](https://github.com/nodejs/node/pull/14155)
- [[`b97e140241`](https://github.com/nodejs/node/commit/b97e140241)] - **tools**: generate template literal for addon tests (Rich Trott) [#14094](https://github.com/nodejs/node/pull/14094)
- [[`e17fb82c06`](https://github.com/nodejs/node/commit/e17fb82c06)] - **tools**: fix error in eslintrc comment (Rich Trott) [#14108](https://github.com/nodejs/node/pull/14108)
- [[`f8d76dcc82`](https://github.com/nodejs/node/commit/f8d76dcc82)] - **tools**: remove align-multiline-assignment lint rule (Rich Trott) [#14079](https://github.com/nodejs/node/pull/14079)
- [[`7d7da98703`](https://github.com/nodejs/node/commit/7d7da98703)] - **tools**: eslint - use `error` and `off` (Refael Ackermann) [#14061](https://github.com/nodejs/node/pull/14061)
- [[`aa4a700ddb`](https://github.com/nodejs/node/commit/aa4a700ddb)] - **tools**: update: eslint-plugin-markdown@1.0.0-beta.7 (Vse Mozhet Byt) [#14047](https://github.com/nodejs/node/pull/14047)
- [[`e03774236a`](https://github.com/nodejs/node/commit/e03774236a)] - **tools**: use no-use-before-define ESLint rule (Vse Mozhet Byt) [#14032](https://github.com/nodejs/node/pull/14032)
- [[`d69527f426`](https://github.com/nodejs/node/commit/d69527f426)] - **tools**: change var to const in ./eslint-rules (Ruben Bridgewater) [#13732](https://github.com/nodejs/node/pull/13732)
- [[`d454add7ce`](https://github.com/nodejs/node/commit/d454add7ce)] - **tools**: change var to const in ./doc/html (Ruben Bridgewater) [#13732](https://github.com/nodejs/node/pull/13732)
- [[`7ed7b22e67`](https://github.com/nodejs/node/commit/7ed7b22e67)] - **tools**: change var to const in ./license2rtf (Ruben Bridgewater) [#13732](https://github.com/nodejs/node/pull/13732)
- [[`f3bff93e21`](https://github.com/nodejs/node/commit/f3bff93e21)] - **tools**: change var to const in ./doc/preprocess (Ruben Bridgewater) [#13732](https://github.com/nodejs/node/pull/13732)
- [[`148f49fcdc`](https://github.com/nodejs/node/commit/148f49fcdc)] - **tools**: change var to const in ./doc/json (Ruben Bridgewater) [#13732](https://github.com/nodejs/node/pull/13732)
- [[`b89c27d360`](https://github.com/nodejs/node/commit/b89c27d360)] - **tools**: change var to const in ./doc/addon-verify (Ruben Bridgewater) [#13732](https://github.com/nodejs/node/pull/13732)
- [[`17636f64db`](https://github.com/nodejs/node/commit/17636f64db)] - **tools**: update to ESLint 4.1.1 (Rich Trott) [#13946](https://github.com/nodejs/node/pull/13946)
- [[`42ef8f9161`](https://github.com/nodejs/node/commit/42ef8f9161)] - **tools**: remove comment in eslint rule (Daniel Bevenius) [#13945](https://github.com/nodejs/node/pull/13945)
- [[`84b1641182`](https://github.com/nodejs/node/commit/84b1641182)] - **tools**: disable legacy indentation linting in tools (Rich Trott) [#13895](https://github.com/nodejs/node/pull/13895)
- [[`c732bf613d`](https://github.com/nodejs/node/commit/c732bf613d)] - **tools**: add script to update ESLint (Rich Trott) [#13895](https://github.com/nodejs/node/pull/13895)
- [[`6a5c37655d`](https://github.com/nodejs/node/commit/6a5c37655d)] - **tools**: update to ESLint 4.1.0 (Rich Trott) [#13895](https://github.com/nodejs/node/pull/13895)
- [[`4ecff6cad7`](https://github.com/nodejs/node/commit/4ecff6cad7)] - **tools,benchmark**: use stricter indentation linting (Rich Trott) [#13895](https://github.com/nodejs/node/pull/13895)
- [[`d23c49f951`](https://github.com/nodejs/node/commit/d23c49f951)] - **url**: do not use HandleScope in ToObject (Bradley Farias) [#14096](https://github.com/nodejs/node/pull/14096)
- [[`cf6afe3331`](https://github.com/nodejs/node/commit/cf6afe3331)] - **url**: normalize port on scheme change (Timothy Gu) [#13997](https://github.com/nodejs/node/pull/13997)
- [[`783cf50a76`](https://github.com/nodejs/node/commit/783cf50a76)] - **util**: delete unused argument 'depth' (kadoufall) [#14267](https://github.com/nodejs/node/pull/14267)
- [[`a675c3d3b7`](https://github.com/nodejs/node/commit/a675c3d3b7)] - **util**: remove redundant declaration (Vse Mozhet Byt) [#14199](https://github.com/nodejs/node/pull/14199)
- [[`8cba959a93`](https://github.com/nodejs/node/commit/8cba959a93)] - **util**: add callbackify (Refael Ackermann) [#13750](https://github.com/nodejs/node/pull/13750)

Windows 32-bit Installer: https://nodejs.org/dist/v8.2.0/node-v8.2.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v8.2.0/node-v8.2.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v8.2.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v8.2.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v8.2.0/node-v8.2.0.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v8.2.0/node-v8.2.0-darwin-x64.tar.gz \
Linux 32-bit Binary: https://nodejs.org/dist/v8.2.0/node-v8.2.0-linux-x86.tar.xz \
Linux 64-bit Binary: https://nodejs.org/dist/v8.2.0/node-v8.2.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v8.2.0/node-v8.2.0-linux-ppc64le.tar.xz \
Linux PPC BE 64-bit Binary: https://nodejs.org/dist/v8.2.0/node-v8.2.0-linux-ppc64.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v8.2.0/node-v8.2.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v8.2.0/node-v8.2.0-aix-ppc64.tar.gz \
SmartOS 32-bit Binary: https://nodejs.org/dist/v8.2.0/node-v8.2.0-sunos-x86.tar.xz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v8.2.0/node-v8.2.0-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v8.2.0/node-v8.2.0-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v8.2.0/node-v8.2.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v8.2.0/node-v8.2.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v8.2.0/node-v8.2.0.tar.gz \
Other release files: https://nodejs.org/dist/v8.2.0/ \
Documentation: https://nodejs.org/docs/v8.2.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

502f4620457ac966ae5890b0a77a09a456e4e838b41d732d853db1ae21f0a3ca  node-v8.2.0-aix-ppc64.tar.gz
da5cca37ae0afdbff3a269bb3dc71ff0d86e282920286f71ae5eb6757cfa5356  node-v8.2.0-darwin-x64.tar.gz
85be2cb401e40bdc75b53772fddd7c7e18fc36673699a77e087c6bfd3db1d4dc  node-v8.2.0-darwin-x64.tar.xz
e4c94286d4a2c1a83e0021cc55fdd8e6757a949158f630193b8ac9220cf83b7b  node-v8.2.0-headers.tar.gz
67c3e571e9d3c9c3eb79e9dbab4d0771538b7527e0434cb423d4219f3c3dcf63  node-v8.2.0-headers.tar.xz
87c7d40bbf374c2d5f17804a8a1158e171e1cec97bb040def6cd1c27ba16b79a  node-v8.2.0-linux-arm64.tar.gz
3298ae4ae67c4ad714bed23383fd08db5711b164a077a25fb6b282533e65cf77  node-v8.2.0-linux-arm64.tar.xz
90bb7f51db594e7e79cfda9e03f2da058bffd6d882c2f5271b96873f18112aa1  node-v8.2.0-linux-armv7l.tar.gz
6d7152bfe0b2d377a33c188958d378d58d63a49a8064b8105594d7d669339ed5  node-v8.2.0-linux-armv7l.tar.xz
23068f37966573997fa23539696744a20217f8bb494f032845e5a4b80023a04a  node-v8.2.0-linux-ppc64le.tar.gz
dd0370ea09ee4212340845cbe47cdda560cc8dd4f403961c1a9e999c3b57ba8f  node-v8.2.0-linux-ppc64le.tar.xz
df0cf729661f7630c8bdc195f7056783ca6e36a769ddc991e9cc984ff9112d9c  node-v8.2.0-linux-ppc64.tar.gz
af20c0ad35585a7a612c31642b73239b85eab437f63edb0971db34f87575aab1  node-v8.2.0-linux-ppc64.tar.xz
1fef79035426878472bde5ac721d180244de739b57dbabdcf3df55eda835538c  node-v8.2.0-linux-s390x.tar.gz
1b963af31bf545e2c14313946b9196ba898b080be364a731ac6c342a3f60f45b  node-v8.2.0-linux-s390x.tar.xz
efffb62d6c595aed358fa01eafe122031269e4f3248c093d4b5bc5b01cdc2f54  node-v8.2.0-linux-x64.tar.gz
e8ceaac3ee4385d53bcebc552a7a6b545b09b9652f5ca1b254aae698fd9a25f9  node-v8.2.0-linux-x64.tar.xz
97ea77f2b8c209d01830754bb9eccfa30406cab3f12aeec3b50f48e137a291a9  node-v8.2.0-linux-x86.tar.gz
2aaeca8059bb75a26e26678466c12d1ad8c284c2f128fcd86a73df9b58f3cade  node-v8.2.0-linux-x86.tar.xz
abcf260de7b5595130359b3756378ef6508efad2e4d37bd633acfba23323a14b  node-v8.2.0.pkg
9908c031ccb45dc19b881d479c4ee712d4953a8ed48e72a33f80111107fb82e5  node-v8.2.0-sunos-x64.tar.gz
c71b13f8eeb76d8fe6097ae33769a4616beda6bb327e51ad79fdc76970cfa258  node-v8.2.0-sunos-x64.tar.xz
1b7f8f99c294bab0a172ef7d5c5a612f4bf242f760672c29e4d04dda112e7703  node-v8.2.0-sunos-x86.tar.gz
e38cda812f964d63aa10d7692d245f7be4dcfcb9bb4dc9509400820785368e2a  node-v8.2.0-sunos-x86.tar.xz
abd4c5dcbb0feb0db1f4fa2b6a8b8ddb69ceacfdae8cf4e089a8976510aac534  node-v8.2.0.tar.gz
4ce8975176630eb60cc8da256a6039e1d3d0e8bed472948a83ed4f22e8134781  node-v8.2.0.tar.xz
87a301c6780193ab1f48e1c7dd2bed1c66c95bd4b32bb8e31fc9d0cbf82c2aeb  node-v8.2.0-win-x64.7z
6bc3cc580f0e8190e6a03d6a5967c861c29d6a6eb50f995b3afb7d60b8cee4e1  node-v8.2.0-win-x64.zip
2917af11647e353978607e70b135001b8a36fb4a55aeb8d4c4bb2b231f2e31c3  node-v8.2.0-win-x86.7z
9fe3d23cf2545b6599ac4d28a708d04c3685c77af4d6fc95ab705ba6ade765f3  node-v8.2.0-win-x86.zip
3723f88184aedbe3f11a1aac23da1d04bfa62b8168240298d649b7eecce68393  node-v8.2.0-x64.msi
febce335f10e9111c17299d1fea0393bd6be711389196a609a5847a15bbfa06a  node-v8.2.0-x86.msi
c50c40716fdc60060f996121df76fc803e0357f9d84fae92743518aa45b07b7f  win-x64/node.exe
b0b52c711754384e1fedeccc2e9de519694e6ba8f208262012d199e6540d8a78  win-x64/node.lib
7c1b576290694996540243c3f21989d4b0d2270f9f8260e579ce0949f7c1848d  win-x64/node_pdb.7z
5bb0cf2a436cf0868d1a230ec0dc984bf664e1945eee6d9fafe9213558f58f48  win-x64/node_pdb.zip
3dac71744de2a50579e2a1fdd658230e107592526aefd35c7c83877146defa79  win-x86/node.exe
0ae93ef6a35df4046ddea1976750b5b90d44c607488394a1aeb181cda58b7949  win-x86/node.lib
75375629507543c5ec2a3b56bc0ab9e3f9c17726276d4fa6c8b007e4a8d9d545  win-x86/node_pdb.7z
308a402b1f09593657be304f4d41c2c9cb34fbb885253c476ad9613fc9fe1ff2  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----
Comment: GPGTools - https://gpgtools.org

iQIcBAEBCAAGBQJZb921AAoJEEX17r2BPa6OTxMP/1Wtj04jCGPqOsj8Plyqpv/K
I4yFsu5zS9IBkv1+/d30s7lkLDEtii3lF620h6kcDSdltycyfj6iBe/EM8xOzqdT
r7uq+uEFK5nEgsHhvWtyFAyRYmVmqyYGZOm+UnVXNWGSnoaSwkvNKMscAMlzVwlc
MaY2oLvcEABAzwsOrQgT+B8o5FPwP+mUyo4R23M2D+hkJGxYJ7Ci8UmOShzJK//M
1A1H3VpeccsP6i7em5wZQGmOaXGwa62rFT+WerdFyCtNfOVvqq1vBWtvCUVcKSVq
PcKI//ebVPLawvMdq6euq46syQSXjhidZH74lLMTuwdvvf/WBQscmxq3/4k72XOg
9XE8HHWQYlmKX1SlyZrQt1YIM4X87UNyP7a0FYLMY9wQKRahHoBUc46WmsFAbzEB
1eTzr5YI4jni24pPVdZMAwU+jLdeJzedeOejvWSxzZ0Ne4aIGJEyuEVdj+yAzy1W
sT7BuPaq/+WXf1B7p3bDWC/xsxwf3xmPt95Al9jaqjoUw/gmYL/fyyBjGsPdKj15
8M2g8czEPXBPhuSvfdDw4RCBr9xDO03iJ/9f64+9D+k6wcrUF91diZ74AIOaZIBg
d7oO64EfiyYnwBUzdMtbuS13ENaLuDyvWNoFJpI5eF6XfZ3rDfxaOt9waHQnbyY8
hl5FTaash3HF2lFJ8pn8
=OSDr
-----END PGP SIGNATURE-----

```
