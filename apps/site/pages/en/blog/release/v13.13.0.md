---
date: '2020-04-14T18:34:06.433Z'
category: release
title: Node v13.13.0 (Current)
layout: blog-post
author: <PERSON><PERSON><PERSON>
---

### Notable Changes

#### New file system APIs

- Added a new function, `fs.readv` (with sync and promisified versions). This
  function takes an array of `ArrayBufferView` elements and will write the data
  it reads sequentially to the buffers (Sk <PERSON>) [#32356](https://github.com/nodejs/node/pull/32356).
- A new overload is available for `fs.readSync`, which allows to optionally pass
  any of the `offset`, `length` and `position` parameters (<PERSON>) [#32460](https://github.com/nodejs/node/pull/32460).

#### Other changes

- **dns**:
  - Added the `dns.ALL` flag, that can be passed to `dns.lookup()` with `dns.V4MAPPED`
    to return resolved IPv6 addresses as well as IPv4 mapped IPv6 addresses (murgatroid99) [#32183](https://github.com/nodejs/node/pull/32183).
- **http**:
  - The default maximum HTTP header size was changed from 8KB to 16KB (rosaxny) [#32520](https://github.com/nodejs/node/pull/32520).
- **n-api**:
  - Calls to `napi_call_threadsafe_function` from the main thread can now return
    the `napi_would_deadlock` status in certain circumstances (Gabriel Schulhof) [#32689](https://github.com/nodejs/node/pull/32689).
- **util**:
  - Added a new `maxStrLength` option to `util.inspect`, to control the maximum
    length of printed strings. Its default value is `Infinity` (rosaxny) [#32392](https://github.com/nodejs/node/pull/32392).
- **worker**:
  - Added support for passing a `transferList` along with `workerData` to the
    `Worker` constructor (Juan José Arboleda) [#32278](https://github.com/nodejs/node/pull/32278).

#### New core collaborators

With this release, we welcome three new Node.js core collaborators:

- himself65 [#32734](https://github.com/nodejs/node/pull/32734).
- flarna (Gerhard Stoebich) [#32620](https://github.com/nodejs/node/pull/32620).
- mildsunrise (Alba Mendez) [#32525](https://github.com/nodejs/node/pull/32525).

### Commits

- [[`a25ceeff72`](https://github.com/nodejs/node/commit/a25ceeff72)] - **async_hooks**: use hasHooks function internally (rickyes) [#32656](https://github.com/nodejs/node/pull/32656)
- [[`b63223114b`](https://github.com/nodejs/node/commit/b63223114b)] - **async_hooks**: move to lazy destroy hook registration in AsyncResource (Andrey Pechkurov) [#32429](https://github.com/nodejs/node/pull/32429)
- [[`78b90d9bc4`](https://github.com/nodejs/node/commit/78b90d9bc4)] - **benchmark**: fix error on server close in AsyncLocalStorage benchmark (Andrey Pechkurov) [#32503](https://github.com/nodejs/node/pull/32503)
- [[`b556670d55`](https://github.com/nodejs/node/commit/b556670d55)] - **benchmark**: use let instead of var in zlib (Daniele Belardi) [#31794](https://github.com/nodejs/node/pull/31794)
- [[`d8316654fb`](https://github.com/nodejs/node/commit/d8316654fb)] - **benchmark**: use let instead of var in worker (Daniele Belardi) [#31794](https://github.com/nodejs/node/pull/31794)
- [[`f1d3fb067b`](https://github.com/nodejs/node/commit/f1d3fb067b)] - **benchmark**: use let instead of var in util (Daniele Belardi) [#31794](https://github.com/nodejs/node/pull/31794)
- [[`148df0a743`](https://github.com/nodejs/node/commit/148df0a743)] - **benchmark**: use let instead of var in url (Daniele Belardi) [#31794](https://github.com/nodejs/node/pull/31794)
- [[`108e91fb85`](https://github.com/nodejs/node/commit/108e91fb85)] - **benchmark**: use let instead of var in tls (Daniele Belardi) [#31794](https://github.com/nodejs/node/pull/31794)
- [[`f1720145af`](https://github.com/nodejs/node/commit/f1720145af)] - **benchmark**: use let instead of var in timers (Daniele Belardi) [#31794](https://github.com/nodejs/node/pull/31794)
- [[`4ce6fc5f9b`](https://github.com/nodejs/node/commit/4ce6fc5f9b)] - **benchmark**: use let instead of var in run.js (Daniele Belardi) [#31794](https://github.com/nodejs/node/pull/31794)
- [[`93b3997452`](https://github.com/nodejs/node/commit/93b3997452)] - **benchmark**: use let instead of var in dns (Daniele Belardi) [#31794](https://github.com/nodejs/node/pull/31794)
- [[`54c6219c0b`](https://github.com/nodejs/node/commit/54c6219c0b)] - **benchmark**: use let instead of var in common.js (Daniele Belardi) [#31794](https://github.com/nodejs/node/pull/31794)
- [[`b188b3c1ba`](https://github.com/nodejs/node/commit/b188b3c1ba)] - **benchmark**: use const instead of var in async_hooks (Daniele Belardi) [#31794](https://github.com/nodejs/node/pull/31794)
- [[`05111c4377`](https://github.com/nodejs/node/commit/05111c4377)] - **benchmark**: add `no-var` rule in .eslintrc.yaml (Daniele Belardi) [#31794](https://github.com/nodejs/node/pull/31794)
- [[`34f05ced8d`](https://github.com/nodejs/node/commit/34f05ced8d)] - **build**: output dots instead of tap in GitHub actions (Michaël Zasso) [#32714](https://github.com/nodejs/node/pull/32714)
- [[`f9a2276548`](https://github.com/nodejs/node/commit/f9a2276548)] - **build**: move doc versions JSON file out of out/doc (Richard Lau) [#32728](https://github.com/nodejs/node/pull/32728)
- [[`d7b526c446`](https://github.com/nodejs/node/commit/d7b526c446)] - **build**: fix LINT_MD_NEWER assignment (Rich Trott) [#32712](https://github.com/nodejs/node/pull/32712)
- [[`809d42ccc1`](https://github.com/nodejs/node/commit/809d42ccc1)] - **build**: remove `.txt` files from .gitignore (Rich Trott) [#32710](https://github.com/nodejs/node/pull/32710)
- [[`a11e3ef912`](https://github.com/nodejs/node/commit/a11e3ef912)] - **build**: log detected compilers in --verbose mode (Richard Lau) [#32715](https://github.com/nodejs/node/pull/32715)
- [[`135f4b9a99`](https://github.com/nodejs/node/commit/135f4b9a99)] - **build**: use tabs for indentation in Makefile (Luigi Pinca) [#32614](https://github.com/nodejs/node/pull/32614)
- [[`655ff39a4c`](https://github.com/nodejs/node/commit/655ff39a4c)] - **build**: remove make lint on lint-py (himself65) [#32599](https://github.com/nodejs/node/pull/32599)
- [[`432e58fcf0`](https://github.com/nodejs/node/commit/432e58fcf0)] - **build**: disable -Wattributes warnings on aix (Ben Noordhuis) [#32419](https://github.com/nodejs/node/pull/32419)
- [[`eda165feb0`](https://github.com/nodejs/node/commit/eda165feb0)] - **build**: drop Travis in favor of Actions (Matheus Marchini) [#32450](https://github.com/nodejs/node/pull/32450)
- [[`814d88a01a`](https://github.com/nodejs/node/commit/814d88a01a)] - **console**: fixup error message (James M Snell) [#32475](https://github.com/nodejs/node/pull/32475)
- [[`2c32e59d8d`](https://github.com/nodejs/node/commit/2c32e59d8d)] - **crypto**: clear openssl error stack after en/decrypt (Ben Noordhuis) [#32248](https://github.com/nodejs/node/pull/32248)
- [[`4874db72b3`](https://github.com/nodejs/node/commit/4874db72b3)] - **deps**: fix zlib compilation for CPUs without SIMD features (Anna Henningsen) [#32627](https://github.com/nodejs/node/pull/32627)
- [[`8586838feb`](https://github.com/nodejs/node/commit/8586838feb)] - **deps**: update archs files for OpenSSL-1.1.1f (Hassaan Pasha) [#32583](https://github.com/nodejs/node/pull/32583)
- [[`3417cc5777`](https://github.com/nodejs/node/commit/3417cc5777)] - **deps**: upgrade openssl sources to 1.1.1f (Hassaan Pasha) [#32583](https://github.com/nodejs/node/pull/32583)
- [[`f690fc93d6`](https://github.com/nodejs/node/commit/f690fc93d6)] - **deps**: update acorn to v7.1.1 (Ruben Bridgewater) [#32310](https://github.com/nodejs/node/pull/32310)
- [[`e0e73f6850`](https://github.com/nodejs/node/commit/e0e73f6850)] - **dns**: remove duplicate code (rickyes) [#32664](https://github.com/nodejs/node/pull/32664)
- [[`e14317a840`](https://github.com/nodejs/node/commit/e14317a840)] - **(SEMVER-MINOR)** **dns**: add dns.ALL hints flag constant (murgatroid99) [#32183](https://github.com/nodejs/node/pull/32183)
- [[`0a8e07599b`](https://github.com/nodejs/node/commit/0a8e07599b)] - **doc**: add link to code ide configs (Robert Nagy) [#32767](https://github.com/nodejs/node/pull/32767)
- [[`18b5e04e75`](https://github.com/nodejs/node/commit/18b5e04e75)] - **doc**: replace node-test-pull-request-lite-pipeline from onboarding (Juan José Arboleda) [#32736](https://github.com/nodejs/node/pull/32736)
- [[`66aafcf298`](https://github.com/nodejs/node/commit/66aafcf298)] - **doc**: add useful v8 option section (Nimit) [#32262](https://github.com/nodejs/node/pull/32262)
- [[`9788b8438b`](https://github.com/nodejs/node/commit/9788b8438b)] - **doc**: add himself65 to collaborators (himself65) [#32734](https://github.com/nodejs/node/pull/32734)
- [[`19deaa5ddf`](https://github.com/nodejs/node/commit/19deaa5ddf)] - **doc**: clarify behavior of napi_get_typedarray_info (Michael Dawson) [#32603](https://github.com/nodejs/node/pull/32603)
- [[`f41660a5c2`](https://github.com/nodejs/node/commit/f41660a5c2)] - **doc**: remove optional parameter from markdown anchor link (Rich Trott) [#32671](https://github.com/nodejs/node/pull/32671)
- [[`6b32877f82`](https://github.com/nodejs/node/commit/6b32877f82)] - **doc**: clarify `listening` event (Harshitha KP) [#32581](https://github.com/nodejs/node/pull/32581)
- [[`c1bb041202`](https://github.com/nodejs/node/commit/c1bb041202)] - **doc**: update Ninja information in build guide (Adrian Estrada) [#32629](https://github.com/nodejs/node/pull/32629)
- [[`ba0ea79c82`](https://github.com/nodejs/node/commit/ba0ea79c82)] - **doc**: correct version metadata for Readable.from (Dave Vandyke) [#32639](https://github.com/nodejs/node/pull/32639)
- [[`7ae8ce3320`](https://github.com/nodejs/node/commit/7ae8ce3320)] - **doc**: make openssl commit messages be valid (Sam Roberts) [#32602](https://github.com/nodejs/node/pull/32602)
- [[`1e72605703`](https://github.com/nodejs/node/commit/1e72605703)] - **doc**: adjust paths in openssl maintenance guide (Hassaan Pasha) [#32593](https://github.com/nodejs/node/pull/32593)
- [[`5c70db48bd`](https://github.com/nodejs/node/commit/5c70db48bd)] - **doc**: clarify docs fs.watch exception may be emitted (Juan José Arboleda) [#32513](https://github.com/nodejs/node/pull/32513)
- [[`b567a63cc0`](https://github.com/nodejs/node/commit/b567a63cc0)] - **doc**: add unreachable code on events example (himself65) [#32364](https://github.com/nodejs/node/pull/32364)
- [[`0f1f572d28`](https://github.com/nodejs/node/commit/0f1f572d28)] - **doc**: clarify `length` param in `buffer.write` (Harshitha KP) [#32119](https://github.com/nodejs/node/pull/32119)
- [[`31b2cbb7e4`](https://github.com/nodejs/node/commit/31b2cbb7e4)] - **doc**: document that server.address() can return null (Thomas Watson Steen) [#32519](https://github.com/nodejs/node/pull/32519)
- [[`7f971b3fd9`](https://github.com/nodejs/node/commit/7f971b3fd9)] - **doc**: return type of `crypto.getFips()` may change (Richard Lau) [#32580](https://github.com/nodejs/node/pull/32580)
- [[`cf4f188fd6`](https://github.com/nodejs/node/commit/cf4f188fd6)] - **doc**: fix return type of `crypto.getFips()` (Richard Lau) [#32580](https://github.com/nodejs/node/pull/32580)
- [[`34074aa095`](https://github.com/nodejs/node/commit/34074aa095)] - **doc**: clarify `requireManualDestroy` option (Harshitha KP) [#32514](https://github.com/nodejs/node/pull/32514)
- [[`a1bb93ac7c`](https://github.com/nodejs/node/commit/a1bb93ac7c)] - **doc**: fix wordy sentence (Moni) [#32567](https://github.com/nodejs/node/pull/32567)
- [[`329635975b`](https://github.com/nodejs/node/commit/329635975b)] - **doc**: add missing changes: entry for dns.ALL (Anna Henningsen) [#32617](https://github.com/nodejs/node/pull/32617)
- [[`1dee8c13a9`](https://github.com/nodejs/node/commit/1dee8c13a9)] - **doc**: fix more links (Alba Mendez) [#32586](https://github.com/nodejs/node/pull/32586)
- [[`d513b55891`](https://github.com/nodejs/node/commit/d513b55891)] - **doc**: improve markdown link checker (Alba Mendez) [#32586](https://github.com/nodejs/node/pull/32586)
- [[`7d93a3fa7c`](https://github.com/nodejs/node/commit/7d93a3fa7c)] - **doc**: add flarna to collaborators (Gerhard Stoebich) [#32620](https://github.com/nodejs/node/pull/32620)
- [[`b6f71969a0`](https://github.com/nodejs/node/commit/b6f71969a0)] - **doc**: improve fs.read documentation (Hachimi Aa (Sfeir)) [#29270](https://github.com/nodejs/node/pull/29270)
- [[`f0a31e33a8`](https://github.com/nodejs/node/commit/f0a31e33a8)] - **doc**: update releaser list in README.md (Myles Borins) [#32577](https://github.com/nodejs/node/pull/32577)
- [[`9ee2afa0f7`](https://github.com/nodejs/node/commit/9ee2afa0f7)] - **doc**: add ASAN build instructions (gengjiawen) [#32436](https://github.com/nodejs/node/pull/32436)
- [[`979fb155ff`](https://github.com/nodejs/node/commit/979fb155ff)] - **doc**: update context-aware section of addon doc (Gabriel Schulhof) [#28659](https://github.com/nodejs/node/pull/28659)
- [[`b494053745`](https://github.com/nodejs/node/commit/b494053745)] - **doc**: update AUTHORS list (Luigi Pinca) [#32222](https://github.com/nodejs/node/pull/32222)
- [[`6d4d299f4d`](https://github.com/nodejs/node/commit/6d4d299f4d)] - **doc**: tests local links in markdown documents (Antoine du HAMEL) [#32359](https://github.com/nodejs/node/pull/32359)
- [[`002048ef9f`](https://github.com/nodejs/node/commit/002048ef9f)] - **doc**: fix typo in http2 docs (Nitin Kumar) [#32292](https://github.com/nodejs/node/pull/32292)
- [[`02b0c9e469`](https://github.com/nodejs/node/commit/02b0c9e469)] - **doc**: fix typo in maintaining-zlib guide (Nitin Kumar) [#32292](https://github.com/nodejs/node/pull/32292)
- [[`6cdccc8f28`](https://github.com/nodejs/node/commit/6cdccc8f28)] - **doc**: fix typo in maintaining-openssl guide (Nitin Kumar) [#32292](https://github.com/nodejs/node/pull/32292)
- [[`7d4ec42b3a`](https://github.com/nodejs/node/commit/7d4ec42b3a)] - **doc**: fix profile type of --heap-prof-name (Syohei YOSHIDA) [#32404](https://github.com/nodejs/node/pull/32404)
- [[`e7e3aeec34`](https://github.com/nodejs/node/commit/e7e3aeec34)] - **doc**: use uppercase on windows path (himself65) [#32294](https://github.com/nodejs/node/pull/32294)
- [[`1b97d25a6c`](https://github.com/nodejs/node/commit/1b97d25a6c)] - **doc**: rename cve_management_process.md to fit doc style guide (Ling Samuel) [#32456](https://github.com/nodejs/node/pull/32456)
- [[`1e27f66ce6`](https://github.com/nodejs/node/commit/1e27f66ce6)] - **doc**: add missing changes: entry for mkdir (Anna Henningsen) [#32490](https://github.com/nodejs/node/pull/32490)
- [[`edee4ecade`](https://github.com/nodejs/node/commit/edee4ecade)] - **doc**: add mildsunrise to collaborators (Alba Mendez) [#32525](https://github.com/nodejs/node/pull/32525)
- [[`7f0ed89892`](https://github.com/nodejs/node/commit/7f0ed89892)] - **doc**: add link to DNS definition (unknown) [#32228](https://github.com/nodejs/node/pull/32228)
- [[`394f8ca333`](https://github.com/nodejs/node/commit/394f8ca333)] - **doc,crypto**: clarify oaepHash option's impact (Filip Skokan) [#32340](https://github.com/nodejs/node/pull/32340)
- [[`991aca329d`](https://github.com/nodejs/node/commit/991aca329d)] - **(SEMVER-MINOR)** **fs**: make parameters optional for readSync (Lucas Holmquist) [#32460](https://github.com/nodejs/node/pull/32460)
- [[`b8b8e82591`](https://github.com/nodejs/node/commit/b8b8e82591)] - **fs**: fix fs.read when passing null value (himself65) [#32479](https://github.com/nodejs/node/pull/32479)
- [[`30d55a3517`](https://github.com/nodejs/node/commit/30d55a3517)] - **(SEMVER-MINOR)** **fs**: add fs.readv() (Sk Sajidul Kadir) [#32356](https://github.com/nodejs/node/pull/32356)
- [[`8770fd96a7`](https://github.com/nodejs/node/commit/8770fd96a7)] - **fs**: fixup error message for invalid options.recursive (James M Snell) [#32472](https://github.com/nodejs/node/pull/32472)
- [[`8597df48f7`](https://github.com/nodejs/node/commit/8597df48f7)] - **http**: fix incorrect headersTimeout measurement (Alex R) [#32329](https://github.com/nodejs/node/pull/32329)
- [[`ff3615d5d9`](https://github.com/nodejs/node/commit/ff3615d5d9)] - **http**: move free socket error handling to agent (Robert Nagy) [#32003](https://github.com/nodejs/node/pull/32003)
- [[`7c3c06224c`](https://github.com/nodejs/node/commit/7c3c06224c)] - **http**: don't emit 'readable' after 'close' (Robert Nagy) [#32277](https://github.com/nodejs/node/pull/32277)
- [[`bd9f4d2954`](https://github.com/nodejs/node/commit/bd9f4d2954)] - **http**: increase default header size from 8KB to 16KB (unknown) [#32520](https://github.com/nodejs/node/pull/32520)
- [[`567b352062`](https://github.com/nodejs/node/commit/567b352062)] - **http**: fixup options.method error message (James M Snell) [#32471](https://github.com/nodejs/node/pull/32471)
- [[`23e56ff21c`](https://github.com/nodejs/node/commit/23e56ff21c)] - **lib**: fix return type of setTimeout in net.Socket (龙腾道) [#32722](https://github.com/nodejs/node/pull/32722)
- [[`180e43711c`](https://github.com/nodejs/node/commit/180e43711c)] - **lib**: removes unnecessary params (Jesus Hernandez) [#32694](https://github.com/nodejs/node/pull/32694)
- [[`94251c463b`](https://github.com/nodejs/node/commit/94251c463b)] - **lib**: changed functional logic in cluster schedulers (Yash Ladha) [#32505](https://github.com/nodejs/node/pull/32505)
- [[`5740a70e5d`](https://github.com/nodejs/node/commit/5740a70e5d)] - **lib**: removed unused error code (Yash Ladha) [#32481](https://github.com/nodejs/node/pull/32481)
- [[`68608b2bdc`](https://github.com/nodejs/node/commit/68608b2bdc)] - **lib**: replace Array to ArrayIsArray by primordials (himself65) [#32258](https://github.com/nodejs/node/pull/32258)
- [[`537d2c1170`](https://github.com/nodejs/node/commit/537d2c1170)] - **module**: expose exports conditions to loaders (Jan Krems) [#31303](https://github.com/nodejs/node/pull/31303)
- [[`bc7f819263`](https://github.com/nodejs/node/commit/bc7f819263)] - **module**: path-only CJS exports extension searching (Guy Bedford) [#32351](https://github.com/nodejs/node/pull/32351)
- [[`3907de7d24`](https://github.com/nodejs/node/commit/3907de7d24)] - **(SEMVER-MINOR)** **n-api**: detect deadlocks in thread-safe function (Gabriel Schulhof) [#32689](https://github.com/nodejs/node/pull/32689)
- [[`dd74601f96`](https://github.com/nodejs/node/commit/dd74601f96)] - **net**: fix crash if POLLHUP is received (Santiago Gimeno) [#32590](https://github.com/nodejs/node/pull/32590)
- [[`3c8bf9022a`](https://github.com/nodejs/node/commit/3c8bf9022a)] - **net**: wait for shutdown to complete before closing (Robert Nagy) [#32491](https://github.com/nodejs/node/pull/32491)
- [[`1a01ac3425`](https://github.com/nodejs/node/commit/1a01ac3425)] - **perf_hooks**: allow omitted parameters in 'performance.measure' (himself65) [#32651](https://github.com/nodejs/node/pull/32651)
- [[`8e00f0d2a2`](https://github.com/nodejs/node/commit/8e00f0d2a2)] - **repl**: fixup error message (James M Snell) [#32474](https://github.com/nodejs/node/pull/32474)
- [[`9b84103273`](https://github.com/nodejs/node/commit/9b84103273)] - **report**: fix stderr matching for fatal error (gengjiawen) [#32699](https://github.com/nodejs/node/pull/32699)
- [[`c09552063b`](https://github.com/nodejs/node/commit/c09552063b)] - **report**: add missing locks for report_on_fatalerror accessors (Anna Henningsen) [#32535](https://github.com/nodejs/node/pull/32535)
- [[`611dbf8d7f`](https://github.com/nodejs/node/commit/611dbf8d7f)] - **src**: removes unused v8::Integer and v8::Array namespace (Jesus Hernandez) [#32779](https://github.com/nodejs/node/pull/32779)
- [[`c8a007f91e`](https://github.com/nodejs/node/commit/c8a007f91e)] - **src**: remove unused v8::TryCatch namespace (Juan José Arboleda) [#32729](https://github.com/nodejs/node/pull/32729)
- [[`ea1785597c`](https://github.com/nodejs/node/commit/ea1785597c)] - **src**: remove duplicated code (himself65) [#32719](https://github.com/nodejs/node/pull/32719)
- [[`1763649c51`](https://github.com/nodejs/node/commit/1763649c51)] - **src**: sync access for report and openssl options (Sam Roberts) [#32618](https://github.com/nodejs/node/pull/32618)
- [[`246b789771`](https://github.com/nodejs/node/commit/246b789771)] - **src**: refactor to avoid goto in node_file.cc (Tobias Nießen) [#32637](https://github.com/nodejs/node/pull/32637)
- [[`d77998096b`](https://github.com/nodejs/node/commit/d77998096b)] - **src**: munmap(2) upon class instance destructor (Gabriel Schulhof) [#32570](https://github.com/nodejs/node/pull/32570)
- [[`1fb4f9d922`](https://github.com/nodejs/node/commit/1fb4f9d922)] - **src**: fix warnings on SPrintF (himself65) [#32558](https://github.com/nodejs/node/pull/32558)
- [[`3b5c4fbc7c`](https://github.com/nodejs/node/commit/3b5c4fbc7c)] - **src**: replace goto with lambda in options parser (Tobias Nießen) [#32635](https://github.com/nodejs/node/pull/32635)
- [[`42a28d0214`](https://github.com/nodejs/node/commit/42a28d0214)] - **src**: fix extra includes of "env.h" and "env-inl.h" (Nick Kreeger) [#32293](https://github.com/nodejs/node/pull/32293)
- [[`fcfde57806`](https://github.com/nodejs/node/commit/fcfde57806)] - **src**: avoid using elevated v8 namespaces in node_perf.h (James M Snell) [#32468](https://github.com/nodejs/node/pull/32468)
- [[`9600332c53`](https://github.com/nodejs/node/commit/9600332c53)] - **src**: avoid using elevated v8 namespaces in node_errors.h (James M Snell) [#32468](https://github.com/nodejs/node/pull/32468)
- [[`62db9a0678`](https://github.com/nodejs/node/commit/62db9a0678)] - **src**: minor http2 refactorings (James M Snell) [#32551](https://github.com/nodejs/node/pull/32551)
- [[`8f766e8397`](https://github.com/nodejs/node/commit/8f766e8397)] - **src**: rename http2 class and suppress compile warnings (James M Snell) [#32551](https://github.com/nodejs/node/pull/32551)
- [[`afc6a25f42`](https://github.com/nodejs/node/commit/afc6a25f42)] - **src**: use smart pointers for nghttp2 objects (James M Snell) [#32551](https://github.com/nodejs/node/pull/32551)
- [[`4df3ac2a63`](https://github.com/nodejs/node/commit/4df3ac2a63)] - **src**: remove loop_init_failed\_ from Worker class (Anna Henningsen) [#32562](https://github.com/nodejs/node/pull/32562)
- [[`0faaa7c84c`](https://github.com/nodejs/node/commit/0faaa7c84c)] - **src**: clean up worker thread creation code (Anna Henningsen) [#32562](https://github.com/nodejs/node/pull/32562)
- [[`f284d599bb`](https://github.com/nodejs/node/commit/f284d599bb)] - **src**: move JSONWriter into its own file (Anna Henningsen) [#32552](https://github.com/nodejs/node/pull/32552)
- [[`e066584d94`](https://github.com/nodejs/node/commit/e066584d94)] - **src**: align PerformanceState class name with conventions (Anna Henningsen) [#32539](https://github.com/nodejs/node/pull/32539)
- [[`04237eca55`](https://github.com/nodejs/node/commit/04237eca55)] - **src**: handle report options on fatalerror (Sam Roberts) [#32497](https://github.com/nodejs/node/pull/32497)
- [[`5080491ae4`](https://github.com/nodejs/node/commit/5080491ae4)] - **src**: refactoring and cleanup of node_i18n (James M Snell) [#32438](https://github.com/nodejs/node/pull/32438)
- [[`e2b08f0ea8`](https://github.com/nodejs/node/commit/e2b08f0ea8)] - **src**: remove unnecessary 'Local.As' operation (himself65) [#32286](https://github.com/nodejs/node/pull/32286)
- [[`928a49004e`](https://github.com/nodejs/node/commit/928a49004e)] - **src**: add test/abort build tasks (Christian Niederer) [#31740](https://github.com/nodejs/node/pull/31740)
- [[`9c901a5ef0`](https://github.com/nodejs/node/commit/9c901a5ef0)] - **src**: add aliased-buffer-overflow abort test (Christian Niederer) [#31740](https://github.com/nodejs/node/pull/31740)
- [[`1e76bc67dd`](https://github.com/nodejs/node/commit/1e76bc67dd)] - **src**: check for overflow when extending AliasedBufferBase (Christian Niederer) [#31740](https://github.com/nodejs/node/pull/31740)
- [[`c71736efd8`](https://github.com/nodejs/node/commit/c71736efd8)] - **src**: unify Linux and FreeBSD large pages implem (Gabriel Schulhof) [#32534](https://github.com/nodejs/node/pull/32534)
- [[`06bff18fa8`](https://github.com/nodejs/node/commit/06bff18fa8)] - **src**: replace handle dereference with ContainerOf (Harshitha KP) [#32298](https://github.com/nodejs/node/pull/32298)
- [[`b973b938a2`](https://github.com/nodejs/node/commit/b973b938a2)] - **src**: enhance template function 'MakeUtf8String' (himself65) [#32322](https://github.com/nodejs/node/pull/32322)
- [[`fbf0493b05`](https://github.com/nodejs/node/commit/fbf0493b05)] - **src**: fix compiler warnings in node_report_module (Daniel Bevenius) [#32498](https://github.com/nodejs/node/pull/32498)
- [[`1de9718b54`](https://github.com/nodejs/node/commit/1de9718b54)] - **src**: remove excess v8 namespace (himself65) [#32191](https://github.com/nodejs/node/pull/32191)
- [[`09cd7449e2`](https://github.com/nodejs/node/commit/09cd7449e2)] - **src**: simplify large pages mapping code (Gabriel Schulhof) [#32396](https://github.com/nodejs/node/pull/32396)
- [[`778dcc8f1a`](https://github.com/nodejs/node/commit/778dcc8f1a)] - **src**: clean v8 namespaces in env.cc file (Juan José Arboleda) [#32374](https://github.com/nodejs/node/pull/32374)
- [[`aa282276ec`](https://github.com/nodejs/node/commit/aa282276ec)] - **src**: check for empty maybe local (Xavier Stouder) [#32339](https://github.com/nodejs/node/pull/32339)
- [[`13377a0f0f`](https://github.com/nodejs/node/commit/13377a0f0f)] - **src**: cleanup DestroyParam when Environment exits (Anna Henningsen) [#32421](https://github.com/nodejs/node/pull/32421)
- [[`055c5686ad`](https://github.com/nodejs/node/commit/055c5686ad)] - **src,test**: add regression test for nested Worker termination (Anna Henningsen) [#32623](https://github.com/nodejs/node/pull/32623)
- [[`1c47bba607`](https://github.com/nodejs/node/commit/1c47bba607)] - **stream**: complete pipeline with stdio (Robert Nagy) [#32373](https://github.com/nodejs/node/pull/32373)
- [[`cad768eb86`](https://github.com/nodejs/node/commit/cad768eb86)] - **stream**: change var to let/const in stream files (Saajan) [#32214](https://github.com/nodejs/node/pull/32214)
- [[`bdb2df7e34`](https://github.com/nodejs/node/commit/bdb2df7e34)] - **test**: replace console.log/error with debuglog (Agustin Daguerre) [#32695](https://github.com/nodejs/node/pull/32695)
- [[`756a049a1a`](https://github.com/nodejs/node/commit/756a049a1a)] - **test**: make sure that inspector tests finish (Anna Henningsen) [#32673](https://github.com/nodejs/node/pull/32673)
- [[`a7a70fa986`](https://github.com/nodejs/node/commit/a7a70fa986)] - **test**: save test file in temporary directory (Luigi Pinca) [#32670](https://github.com/nodejs/node/pull/32670)
- [[`6d479588cb`](https://github.com/nodejs/node/commit/6d479588cb)] - **test**: fix check error name on error instance (himself65) [#32508](https://github.com/nodejs/node/pull/32508)
- [[`9df274ad03`](https://github.com/nodejs/node/commit/9df274ad03)] - **_Revert_** "**test**: mark empty udp tests flaky on OS X" (Luigi Pinca) [#32489](https://github.com/nodejs/node/pull/32489)
- [[`6d122429c1`](https://github.com/nodejs/node/commit/6d122429c1)] - **test**: remove unused variables on async hook test (Julian Duque) [#32630](https://github.com/nodejs/node/pull/32630)
- [[`8c68dd4a11`](https://github.com/nodejs/node/commit/8c68dd4a11)] - **test**: check that --expose-internals is disallowed in NODE_OPTIONS (Juan José Arboleda) [#32554](https://github.com/nodejs/node/pull/32554)
- [[`4ffa138c81`](https://github.com/nodejs/node/commit/4ffa138c81)] - **test**: refactor test-worker (himself65) [#32509](https://github.com/nodejs/node/pull/32509)
- [[`17b2526162`](https://github.com/nodejs/node/commit/17b2526162)] - **test**: add Worker initialization failure test case (Harshitha KP) [#31929](https://github.com/nodejs/node/pull/31929)
- [[`ed89863c6d`](https://github.com/nodejs/node/commit/ed89863c6d)] - **test**: fix tool path in test-doctool-versions.js (Richard Lau) [#32645](https://github.com/nodejs/node/pull/32645)
- [[`17a3dcea90`](https://github.com/nodejs/node/commit/17a3dcea90)] - **test**: copy addons .gitignore to test/abort/ (Anna Henningsen) [#32624](https://github.com/nodejs/node/pull/32624)
- [[`e501ba2146`](https://github.com/nodejs/node/commit/e501ba2146)] - **test**: refactor test-http2-buffersize (Rich Trott) [#32540](https://github.com/nodejs/node/pull/32540)
- [[`cede0cb841`](https://github.com/nodejs/node/commit/cede0cb841)] - **test**: skip crypto test on arm buildbots (Ben Noordhuis) [#32636](https://github.com/nodejs/node/pull/32636)
- [[`e01d061669`](https://github.com/nodejs/node/commit/e01d061669)] - **test**: replace console.error() with debuglog calls (Rich Trott) [#32588](https://github.com/nodejs/node/pull/32588)
- [[`a7b6a10e2a`](https://github.com/nodejs/node/commit/a7b6a10e2a)] - **test**: fix python-version selection with actions (Myles Borins) [#32609](https://github.com/nodejs/node/pull/32609)
- [[`93ff4ffca9`](https://github.com/nodejs/node/commit/93ff4ffca9)] - **test**: add a missing common.mustCall (Harshitha KP) [#32305](https://github.com/nodejs/node/pull/32305)
- [[`30505d7c10`](https://github.com/nodejs/node/commit/30505d7c10)] - **test**: remove unnecessary console.log() calls (Juan José Arboleda) [#32541](https://github.com/nodejs/node/pull/32541)
- [[`8f0c1069b9`](https://github.com/nodejs/node/commit/8f0c1069b9)] - **test**: replace console.log() with debuglog() (Juan José Arboleda) [#32550](https://github.com/nodejs/node/pull/32550)
- [[`408437d7c6`](https://github.com/nodejs/node/commit/408437d7c6)] - **test**: validate util.format when the value is 'Infinity' (Andrés M. Gómez) [#32573](https://github.com/nodejs/node/pull/32573)
- [[`2e015e5b5e`](https://github.com/nodejs/node/commit/2e015e5b5e)] - **test**: fix fs test-fs-utimes strictEqual arg order (Ben Noordhuis) [#32420](https://github.com/nodejs/node/pull/32420)
- [[`edf35db27e`](https://github.com/nodejs/node/commit/edf35db27e)] - **test**: replace flag expose_internals to expose-internals (Juan José Arboleda) [#32542](https://github.com/nodejs/node/pull/32542)
- [[`079a32e31c`](https://github.com/nodejs/node/commit/079a32e31c)] - **test**: use common.mustCall in test-worker-esm-exit (himself65) [#32544](https://github.com/nodejs/node/pull/32544)
- [[`cca269c3a0`](https://github.com/nodejs/node/commit/cca269c3a0)] - **test**: use template strings in parallel tests (Daniel Estiven Rico Posada) [#32549](https://github.com/nodejs/node/pull/32549)
- [[`0e4ce8f50a`](https://github.com/nodejs/node/commit/0e4ce8f50a)] - **test**: add known issues test for #31733 (Ben Noordhuis) [#31734](https://github.com/nodejs/node/pull/31734)
- [[`28077a01cc`](https://github.com/nodejs/node/commit/28077a01cc)] - **test**: mark test-http2-reset-flood flaky on bsd (Myles Borins) [#32595](https://github.com/nodejs/node/pull/32595)
- [[`ca2662012e`](https://github.com/nodejs/node/commit/ca2662012e)] - **test**: add test-worker-prof to the SLOW list for debug (Myles Borins) [#32589](https://github.com/nodejs/node/pull/32589)
- [[`8bcbb8d7dd`](https://github.com/nodejs/node/commit/8bcbb8d7dd)] - **test**: refactor test-http-information-processing (Rich Trott) [#32547](https://github.com/nodejs/node/pull/32547)
- [[`1fc19b0fb0`](https://github.com/nodejs/node/commit/1fc19b0fb0)] - **test**: fix a typo on test-fs-read-optional-params (himself65) [#32461](https://github.com/nodejs/node/pull/32461)
- [[`986a60544a`](https://github.com/nodejs/node/commit/986a60544a)] - **test**: skip a wasi test on IBMi PASE (Xu Meng) [#32459](https://github.com/nodejs/node/pull/32459)
- [[`73fec7cd00`](https://github.com/nodejs/node/commit/73fec7cd00)] - **test**: harden the tick sampling logic (Harshitha KP) [#32190](https://github.com/nodejs/node/pull/32190)
- [[`1905b9ecce`](https://github.com/nodejs/node/commit/1905b9ecce)] - **test**: als variant of test-timers-clearImmediate (Harshitha KP) [#32303](https://github.com/nodejs/node/pull/32303)
- [[`72983d2e4f`](https://github.com/nodejs/node/commit/72983d2e4f)] - **test**: skip some binding tests on IBMi PASE (Xu Meng) [#31967](https://github.com/nodejs/node/pull/31967)
- [[`02eea7773a`](https://github.com/nodejs/node/commit/02eea7773a)] - **test**: revise test-http-response-multi-content-length (Rich Trott) [#32526](https://github.com/nodejs/node/pull/32526)
- [[`f179a223d7`](https://github.com/nodejs/node/commit/f179a223d7)] - **test**: remove a duplicated test (himself65) [#32453](https://github.com/nodejs/node/pull/32453)
- [[`fbb51b9c41`](https://github.com/nodejs/node/commit/fbb51b9c41)] - **test**: check bundled binaries are signed on macOS (Richard Lau) [#32522](https://github.com/nodejs/node/pull/32522)
- [[`36c6d22113`](https://github.com/nodejs/node/commit/36c6d22113)] - **test**: unflake async-hooks/test-statwatcher (Bartosz Sosnowski) [#32484](https://github.com/nodejs/node/pull/32484)
- [[`b1e6f297cf`](https://github.com/nodejs/node/commit/b1e6f297cf)] - **test**: use Promise.all() in test-cluster-net-listen-ipv6only-false (Rich Trott) [#32398](https://github.com/nodejs/node/pull/32398)
- [[`11d7cf155a`](https://github.com/nodejs/node/commit/11d7cf155a)] - **test**: replace Map with Array in test-cluster-net-listen-ipv6only-false (Rich Trott) [#32398](https://github.com/nodejs/node/pull/32398)
- [[`2f8f619c7e`](https://github.com/nodejs/node/commit/2f8f619c7e)] - **test**: revise test-http-client-default-headers-exist (Rich Trott) [#32493](https://github.com/nodejs/node/pull/32493)
- [[`567e671bd2`](https://github.com/nodejs/node/commit/567e671bd2)] - **tools**: update Boxstarter script and document (himself65) [#32299](https://github.com/nodejs/node/pull/32299)
- [[`97a3e2f0e2`](https://github.com/nodejs/node/commit/97a3e2f0e2)] - **tools**: update ESLint to 7.0.0-alpha.3 (Colin Ihrig) [#32533](https://github.com/nodejs/node/pull/32533)
- [[`ffdd82ba3f`](https://github.com/nodejs/node/commit/ffdd82ba3f)] - **tools**: only fetch previous versions when necessary (Richard Lau) [#32518](https://github.com/nodejs/node/pull/32518)
- [[`4e5271acfb`](https://github.com/nodejs/node/commit/4e5271acfb)] - **tracing**: do not attempt to call into JS when disallowed (Anna Henningsen) [#32548](https://github.com/nodejs/node/pull/32548)
- [[`0087eb1b98`](https://github.com/nodejs/node/commit/0087eb1b98)] - **(SEMVER-MINOR)** **util**: add `maxStrLength` option to `inspect` function (unknown) [#32392](https://github.com/nodejs/node/pull/32392)
- [[`848d81cf23`](https://github.com/nodejs/node/commit/848d81cf23)] - **util**: only inspect error properties that are not visible otherwise (Ruben Bridgewater) [#32327](https://github.com/nodejs/node/pull/32327)
- [[`6d86651076`](https://github.com/nodejs/node/commit/6d86651076)] - **util**: fix inspecting document.all (Gus Caplan) [#31938](https://github.com/nodejs/node/pull/31938)
- [[`4f98b7178d`](https://github.com/nodejs/node/commit/4f98b7178d)] - **wasi**: clean up options validation (Denys Otrishko) [#31797](https://github.com/nodejs/node/pull/31797)
- [[`2ee684a7b9`](https://github.com/nodejs/node/commit/2ee684a7b9)] - **win,build**: set exit_code on configure failure (Bartlomiej Brzozowski) [#32205](https://github.com/nodejs/node/pull/32205)
- [[`8cb5e41807`](https://github.com/nodejs/node/commit/8cb5e41807)] - **(SEMVER-MINOR)** **worker**: support MessagePort to workers data (Juan José Arboleda) [#32278](https://github.com/nodejs/node/pull/32278)
- [[`4acd7f4390`](https://github.com/nodejs/node/commit/4acd7f4390)] - **worker**: do not emit 'exit' events during process.exit() (Anna Henningsen) [#32546](https://github.com/nodejs/node/pull/32546)
- [[`833d78afcf`](https://github.com/nodejs/node/commit/833d78afcf)] - **worker**: runtime error on pthread creation (Harshitha KP) [#32344](https://github.com/nodejs/node/pull/32344)

Windows 32-bit Installer: https://nodejs.org/dist/v13.13.0/node-v13.13.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v13.13.0/node-v13.13.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v13.13.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v13.13.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v13.13.0/node-v13.13.0.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v13.13.0/node-v13.13.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v13.13.0/node-v13.13.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v13.13.0/node-v13.13.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v13.13.0/node-v13.13.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v13.13.0/node-v13.13.0-aix-ppc64.tar.gz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v13.13.0/node-v13.13.0-sunos-x64.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v13.13.0/node-v13.13.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v13.13.0/node-v13.13.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v13.13.0/node-v13.13.0.tar.gz \
Other release files: https://nodejs.org/dist/v13.13.0/ \
Documentation: https://nodejs.org/docs/v13.13.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

acaeb22af1eb81204d9b73a365bd2355b45625d36c341d9f4b2b3240b1998eac  node-v13.13.0-aix-ppc64.tar.gz
28ae2abedafb250a9bbe706650fd79d2b25273f6445adcc1d85c777359dc5390  node-v13.13.0-darwin-x64.tar.gz
5518ab3b0c5d0d2ddb127a69b3251fb9b9c04f8e3b5b528f726ca975dd592977  node-v13.13.0-darwin-x64.tar.xz
dd76d867e929039d971e18c2d886c6fbda439d0bcc2416fb63e2bf1f6b53ca83  node-v13.13.0-headers.tar.gz
3de9089d1ae1144087c3c3971980973bed7a47c58bbb755664f8afa0f0c9c8f4  node-v13.13.0-headers.tar.xz
fd1a25910a77084ecd2f092f74a0bfb68526f219fbd07cec64beaf29c91619a1  node-v13.13.0-linux-arm64.tar.gz
eded8da4633134cae0766a2f6f3c2237f8e728df77895c85607acaca13216226  node-v13.13.0-linux-arm64.tar.xz
0fed3e5f42b69898cca326c17e94eb8f4b279058e5d49b00aa9fa1716c96b865  node-v13.13.0-linux-armv7l.tar.gz
9da8eeadccbfbd3bba2ae3227d56a740f71ffad6ee7b4a1556f4100983fa9698  node-v13.13.0-linux-armv7l.tar.xz
b91babd63a606cb3d60d808b1505a1a0cde0da537c450abb494319963985da5a  node-v13.13.0-linux-ppc64le.tar.gz
7bf80dafa26c34d53efe5bf39327fd9fd7a992e40986cdd9920a5ddde16cacee  node-v13.13.0-linux-ppc64le.tar.xz
217cea0e9b485cc1a73e57c57a1a891459fc7c20038158a33972b6ce996983a1  node-v13.13.0-linux-s390x.tar.gz
827b81fcba5582afff1e2202eb8f61925436100a3948d5a46513b5b0d7d664ce  node-v13.13.0-linux-s390x.tar.xz
8272d2825b68be55ac48e6270ab9179a126306c914a6dfdf1f175d04e006131e  node-v13.13.0-linux-x64.tar.gz
41d60cda7422f53156711626d670f9b5079075b1ecc6736aea99319fe36c20a6  node-v13.13.0-linux-x64.tar.xz
38cfece99f51db156b183b539079da1dc3d5783a49e7267978f805512c248d56  node-v13.13.0.pkg
cf33c2eda7cd2b83dbecde9753094af0eb3c15e094418632b9075a859b68c190  node-v13.13.0-sunos-x64.tar.gz
6fcd6eeba4d42a2cc8107c87167f5133887877efdb5ca4ae7b81f0b007c617dd  node-v13.13.0-sunos-x64.tar.xz
bbcb1bbcf78777e402ca220edd8f5c23475ed0164025d8a6bc3367e3e3ac9577  node-v13.13.0.tar.gz
6711730dfaae2423c8f74f4b5f389bb3f3f0d770eac271287fa9444a9568c773  node-v13.13.0.tar.xz
a071c0b94a1571d0f1b11e9e39eed39bb7684a420333174b3a4c89f8ed0c371f  node-v13.13.0-win-x64.7z
79f2a39c1047666f2bbf2efe53c069a38b8f6ef18d569bf338463aea1914b89d  node-v13.13.0-win-x64.zip
66ed2b7b981101249a932aaac6f4a35b7e17ea5d81f76468a731924c9d79c862  node-v13.13.0-win-x86.7z
a3b9b2ff87afcdc2ab4e5633b5ca8f33499c5d9cf3c403564239eb98737431c5  node-v13.13.0-win-x86.zip
c5496d41424dd860610501901c2a2bba60633d6f0fdc56e4362a50a7d30bce7f  node-v13.13.0-x64.msi
9d359b6b899748f905416f258c06fed01f0caa26fa54a47e2d38dc565568e7ac  node-v13.13.0-x86.msi
c61b9279be8701a3f66c482a166c4fff9ba43da64403158e4c4f82e271f309fa  win-x64/node.exe
2e989289f12461098a357340266afdf658989d13b32e5dfc98c5f43940052e12  win-x64/node.lib
dd28964514299575979c63e01d5a8f085e674dfc00e0c9cec4cea1901e5781b8  win-x64/node_pdb.7z
827e9aacb43c9353540044487307eca0d47cc0f822ebcff0c656fb08d0eea3bf  win-x64/node_pdb.zip
ea44a293ff72e92b6d8d9a5d5f6a7a7bf22eb6cc9914f7eee3ed25abae77fc4c  win-x86/node.exe
9bef778099abe591c282b6cd5f097c965292a7dfa0e1967b8e107371d5c37a16  win-x86/node.lib
db80bfc25365274d109fd3f9727b646d0562360b4f719d9f5da3e16d2d753d71  win-x86/node_pdb.7z
52b44c41f2a96ab2b988e8067b7a3946e690c5c7b5ee87fb7812adda948f6382  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEj8yhP+8dDC6RAI4Jdw96mlrhVgAFAl6WAIsACgkQdw96mlrh
VgBt3g/7BOiQI8nWHqic3nWcdACYfDdPKT3NR9VQ3N4ihmEpK9wfA6gX9UNnuK+w
bjXQPY4lAsP+7wmqTIm2RJSAJ7KYTWTTWMGAJinXpnX69Gs9oSqeM/4wH2UhIxvx
7++WD9aRK8ZfZomj05CtSRqQBX4c38SsvCvPbzurFwQbNwQ6N2tAbb3UKlRU6OUA
5bHP8C34nI/3z6B1CroCieRWKHuPjNTGN8/d6hSlR6vwNc2tHao5Pzms8V5fn75V
3HvQtkO0g8GNQY4EO/qnqK7ZwUkl0KPiNX2M2TX9VJVyakwNZt1HqUNMYNKeLDPc
1ILiBi4kP+FEiXqp+A3rStoAVNOuNV8YFRW4trqNiYovGuYp9mTKGXD9bRgxbt2U
S4M6LDamf6ZjfvlIchUWFyb4A0sl9TCc18NWX96aCvPyNcg1Ixlaz0BwJKVOPdlp
7JS3O7EyNlph8/FH9ADiomkEK92H7yMNERWjTchEymNm5YhmyIZ7T59YsSj+tAih
/PjpF7s/vOaqK6rDS003+qsvJ6o2588HbNmxtrz0lDI3OHAMJeOhlGXZCIHXK8A7
bskx+YjLX53g9HPl3HoT20JRumkXlVr3+wjxCFHzBHUoDtvVqAjaf6RQRh90f9RW
IuyPo1Mijy9nSSmg4Yh2JwR6lGgGR87fHAI1D5gpUGlEoPOXHOQ=
=E4n3
-----END PGP SIGNATURE-----

```
