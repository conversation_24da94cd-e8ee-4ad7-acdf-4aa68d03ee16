---
date: '2019-08-15T22:38:49.767Z'
category: release
title: Node v8.16.1 (LTS)
layout: blog-post
author: <PERSON>
---

### Notable changes

This is a security release.

Node.js, as well as many other implementations of HTTP/2, have been found
vulnerable to Denial of Service attacks.
See https://github.com/Netflix/security-bulletins/blob/master/advisories/third-party/2019-002.md
for more information.

Vulnerabilities fixed:

- **CVE-2019-9511 “Data Dribble”**: The attacker requests a large amount of data from a specified resource over multiple streams. They manipulate window size and stream priority to force the server to queue the data in 1-byte chunks. Depending on how efficiently this data is queued, this can consume excess CPU, memory, or both, potentially leading to a denial of service.
- **CVE-2019-9512 “Ping Flood”**: The attacker sends continual pings to an HTTP/2 peer, causing the peer to build an internal queue of responses. Depending on how efficiently this data is queued, this can consume excess CPU, memory, or both, potentially leading to a denial of service.
- **CVE-2019-9513 “Resource Loop”**: The attacker creates multiple request streams and continually shuffles the priority of the streams in a way that causes substantial churn to the priority tree. This can consume excess CPU, potentially leading to a denial of service.
- **CVE-2019-9514 “Reset Flood”**: The attacker opens a number of streams and sends an invalid request over each stream that should solicit a stream of RST_STREAM frames from the peer. Depending on how the peer queues the RST_STREAM frames, this can consume excess memory, CPU, or both, potentially leading to a denial of service.
- **CVE-2019-9515 “Settings Flood”**: The attacker sends a stream of SETTINGS frames to the peer. Since the RFC requires that the peer reply with one acknowledgement per SETTINGS frame, an empty SETTINGS frame is almost equivalent in behavior to a ping. Depending on how efficiently this data is queued, this can consume excess CPU, memory, or both, potentially leading to a denial of service.
- **CVE-2019-9516 “0-Length Headers Leak”**: The attacker sends a stream of headers with a 0-length header name and 0-length header value, optionally Huffman encoded into 1-byte or greater headers. Some implementations allocate memory for these headers and keep the allocation alive until the session dies. This can consume excess memory, potentially leading to a denial of service.
- **CVE-2019-9517 “Internal Data Buffering”**: The attacker opens the HTTP/2 window so the peer can send without constraint; however, they leave the TCP window closed so the peer cannot actually write (many of) the bytes on the wire. The attacker then sends a stream of requests for a large response object. Depending on how the servers queue the responses, this can consume excess memory, CPU, or both, potentially leading to a denial of service.
- **CVE-2019-9518 “Empty Frames Flood”**: The attacker sends a stream of frames with an empty payload and without the end-of-stream flag. These frames can be DATA, HEADERS, CONTINUATION and/or PUSH_PROMISE. The peer spends time processing each frame disproportionate to attack bandwidth. This can consume excess CPU, potentially leading to a denial of service. (Discovered by Piotr Sikora of Google)

### Commits

- [[`6d427378c0`](https://github.com/nodejs/node/commit/6d427378c0)] - **deps**: update nghttp2 to 1.39.2 (Anna Henningsen) [#29122](https://github.com/nodejs/node/pull/29122)
- [[`33d4d916d5`](https://github.com/nodejs/node/commit/33d4d916d5)] - **deps**: update nghttp2 to 1.39.1 (gengjiawen) [#28448](https://github.com/nodejs/node/pull/28448)
- [[`17fad97113`](https://github.com/nodejs/node/commit/17fad97113)] - **deps**: update nghttp2 to 1.38.0 (gengjiawen) [#27295](https://github.com/nodejs/node/pull/27295)
- [[`0b44733695`](https://github.com/nodejs/node/commit/0b44733695)] - **deps**: update nghttp2 to 1.37.0 (gengjiawen) [#26990](https://github.com/nodejs/node/pull/26990)
- [[`5afc77b044`](https://github.com/nodejs/node/commit/5afc77b044)] - **deps**: update nghttp2 to 1.34.0 (James M Snell) [#23284](https://github.com/nodejs/node/pull/23284)
- [[`073108c855`](https://github.com/nodejs/node/commit/073108c855)] - **http2**: allow security revert for Ping/Settings Flood (Anna Henningsen) [#29122](https://github.com/nodejs/node/pull/29122)
- [[`6d687f7af8`](https://github.com/nodejs/node/commit/6d687f7af8)] - **http2**: pause input processing if sending output (Anna Henningsen) [#29122](https://github.com/nodejs/node/pull/29122)
- [[`854dba649e`](https://github.com/nodejs/node/commit/854dba649e)] - **http2**: stop reading from socket if writes are in progress (Anna Henningsen) [#29122](https://github.com/nodejs/node/pull/29122)
- [[`a3191689dd`](https://github.com/nodejs/node/commit/a3191689dd)] - **http2**: consider 0-length non-end DATA frames an error (Anna Henningsen) [#29122](https://github.com/nodejs/node/pull/29122)
- [[`156f2f35df`](https://github.com/nodejs/node/commit/156f2f35df)] - **http2**: shrink default `vector::reserve()` allocations (Anna Henningsen) [#29122](https://github.com/nodejs/node/pull/29122)
- [[`10f05b65c4`](https://github.com/nodejs/node/commit/10f05b65c4)] - **http2**: handle 0-length headers better (Anna Henningsen) [#29122](https://github.com/nodejs/node/pull/29122)
- [[`ac28a628a5`](https://github.com/nodejs/node/commit/ac28a628a5)] - **http2**: limit number of invalid incoming frames (Anna Henningsen) [#29122](https://github.com/nodejs/node/pull/29122)
- [[`11b4e2c0db`](https://github.com/nodejs/node/commit/11b4e2c0db)] - **http2**: limit number of rejected stream openings (Anna Henningsen) [#29122](https://github.com/nodejs/node/pull/29122)
- [[`7de642b6f9`](https://github.com/nodejs/node/commit/7de642b6f9)] - **http2**: do not create ArrayBuffers when no DATA received (Anna Henningsen) [#29122](https://github.com/nodejs/node/pull/29122)
- [[`dd60d3561a`](https://github.com/nodejs/node/commit/dd60d3561a)] - **http2**: only call into JS when necessary for session events (Anna Henningsen) [#29122](https://github.com/nodejs/node/pull/29122)
- [[`00f6846b73`](https://github.com/nodejs/node/commit/00f6846b73)] - **http2**: improve JS-side debug logging (Anna Henningsen) [#29122](https://github.com/nodejs/node/pull/29122)
- [[`b095e35f1f`](https://github.com/nodejs/node/commit/b095e35f1f)] - **http2**: improve http2 code a bit (James M Snell) [#23984](https://github.com/nodejs/node/pull/23984)
- [[`cc282239c1`](https://github.com/nodejs/node/commit/cc282239c1)] - **test**: apply test-http2-max-session-memory-leak from v12.x (Anna Henningsen) [#29122](https://github.com/nodejs/node/pull/29122)

Windows 32-bit Installer: https://nodejs.org/dist/v8.16.1/node-v8.16.1-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v8.16.1/node-v8.16.1-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v8.16.1/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v8.16.1/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v8.16.1/node-v8.16.1.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v8.16.1/node-v8.16.1-darwin-x64.tar.gz \
Linux 32-bit Binary: https://nodejs.org/dist/v8.16.1/node-v8.16.1-linux-x86.tar.xz \
Linux 64-bit Binary: https://nodejs.org/dist/v8.16.1/node-v8.16.1-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v8.16.1/node-v8.16.1-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v8.16.1/node-v8.16.1-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v8.16.1/node-v8.16.1-aix-ppc64.tar.gz \
SmartOS 32-bit Binary: https://nodejs.org/dist/v8.16.1/node-v8.16.1-sunos-x86.tar.xz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v8.16.1/node-v8.16.1-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v8.16.1/node-v8.16.1-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v8.16.1/node-v8.16.1-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v8.16.1/node-v8.16.1-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v8.16.1/node-v8.16.1.tar.gz \
Other release files: https://nodejs.org/dist/v8.16.1/ \
Documentation: https://nodejs.org/docs/v8.16.1/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

be210d9ce20155877e56aa57901f71b45b214ebb6f78fc48e96ad3c37ec2ea0c  node-v8.16.1-aix-ppc64.tar.gz
ef1cb93f03bca4b9528e5d3226bdf8efa135e4b12285eee1e4760da06bac631a  node-v8.16.1-darwin-x64.tar.gz
8eac60c9d6687fb77a052a04712cc792a6cd1f55003e96d09404986690f8ad76  node-v8.16.1-darwin-x64.tar.xz
a9e646dd27f29611002298a5a69e753e37b501ebb587165c258fd18425bbd7ff  node-v8.16.1-headers.tar.gz
96671638be9e66d81227647123218e0077b7af4105854ac190bc7840d22db91c  node-v8.16.1-headers.tar.xz
880cdfba7072398b2f7ca84474d3a689a9325182b866e6705f04f1cde10fea94  node-v8.16.1-linux-arm64.tar.gz
0c61c6ae8a70d96ab19848a09b2010d29b806f2dd79177da22c743fd5e352a98  node-v8.16.1-linux-arm64.tar.xz
1995c8a31e6939f47a93b101e1cb7f9d7caa2eacd01b1ffd90e50af5e8a776a2  node-v8.16.1-linux-armv7l.tar.gz
c71297cdb4336969ee10d5ac7daecdc380bb876a1e17db1cbf0479983cfe6f59  node-v8.16.1-linux-armv7l.tar.xz
22a89b8dc86da0f844ffc57b0d4a693795a4b6f006f9a930f3b8534a5b4927b8  node-v8.16.1-linux-ppc64le.tar.gz
5cfb323a649b7d9e6f54b5e3132141b18756a0d2b78435097ae6800acc286802  node-v8.16.1-linux-ppc64le.tar.xz
b3bbef731a4f058c6e86437220e14a246b64cf54e923fbceffa2538197446fa5  node-v8.16.1-linux-s390x.tar.gz
efaf43ddcf0f84d0ca6295fd35c1029b99150d94ce17f23764a5c2840afe4384  node-v8.16.1-linux-s390x.tar.xz
8ef575b64edbb6c04e506d8c8e0c5f92b90f4752841892c5adbb3a1e02863f46  node-v8.16.1-linux-x64.tar.gz
22a2580569c787ea83960bda0eae5dfaf1fe79382a52ad5fa5cd3accb93a1818  node-v8.16.1-linux-x64.tar.xz
a7f60fc6f41bedd2a387bc99067df11d53161fa235b8c90c6b5e73b0dff9af8e  node-v8.16.1-linux-x86.tar.gz
ad19074012d957e8e524b3ef2ebad8cf36bdf58f86505e0ec2c131c1004ea9bb  node-v8.16.1-linux-x86.tar.xz
b479883439ac4dbbbf509de5699f2b64b7788479b7715fc140bf6e89d2205d51  node-v8.16.1.pkg
8bf305578e0020ea3643a59f1b8bf2153e6eaf4e13527d24c30e60150ff5bc69  node-v8.16.1-sunos-x64.tar.gz
da4c673ab4fcefd892a833bb57b4b8d25e1c18d3d876fe810477b1ef3d7af98a  node-v8.16.1-sunos-x64.tar.xz
2ac82a583da792d04c336350a7f482c716c1abe1a8db84daaaaf98e117a16e56  node-v8.16.1-sunos-x86.tar.gz
779ff5950e03b48ba2fa36c133a05d5f3c94e25849b94025e9305be36218ecb0  node-v8.16.1-sunos-x86.tar.xz
7666207212ac5cbd766b052951f57da62aaa641fedf83fabcb31dbb19f61169d  node-v8.16.1.tar.gz
d8c190acdf2d967faf49c22df883d31a8d4e249d67852dae3c2d8a0f756b0512  node-v8.16.1.tar.xz
a70cd3c72351675bc3f4c51bedd293b173cb42ea973ef1c1dc19d90c6f769f17  node-v8.16.1-win-x64.7z
396dc41740bca0355d11865780e2af8e88eb17581cd2c445ccaf29b862901ab8  node-v8.16.1-win-x64.zip
c524468cf0d278b8daf4f4e864fad75fe457fadfc9f920aa93be8271529bf189  node-v8.16.1-win-x86.7z
18345ecb826ff63ab82b3688b53f4586fa67605b3adb0e87b853c6d15891b1aa  node-v8.16.1-win-x86.zip
e5ad1a5e9e2de3ce98e4accbaaf59b8e78e2e3e08fed6c1863616e2886499250  node-v8.16.1-x64.msi
26a7e5fb30710d54fa3b93cd4cba3b805b1b4b1e6d3980387ab335c4bbb3d302  node-v8.16.1-x86.msi
68eb7432f06a63b4456ab88e0ca984df4c993e60a6c6f05e6f8d12de177941ef  win-x64/node.exe
b277fd2148b2d06d7279976cb5a87224281db8a1417d334fef1402cddc3a606c  win-x64/node.lib
558341be54e43a9c4ed0845b36afc547e7413d3c977a879f0f4494fb2863c092  win-x64/node_pdb.7z
663ea606cc994bd0508843a40d079f3beeb8a4814eb4607d7c05a859f577e547  win-x64/node_pdb.zip
b009f4a4bc0eada5c4430b6e3140061728bc2b01e4e33c06b467321a9c4db329  win-x86/node.exe
a8d422d7eac6539a43d53a59d50f3a37020c8ea53080d2cdfd55eb07ce027ab4  win-x86/node.lib
8520521b679d21a75aa3794042ecadfc11b523c140a71e085c4d9e947d7ee231  win-x86/node_pdb.7z
11ed30cc63a58e30564c599235ab4705a54aff3008ce090f5edd50cd05d8e907  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQEzBAEBCAAdFiEETtd49TnjY0x3nIfG1wYoSKGrAFwFAl1V3f8ACgkQ1wYoSKGr
AFzOEwf/e1u2Qrg8cujswF7GYYPluq8F+Dq5vlllAfAIFYgyAhZ1cxjV0rNY+mYk
ySQAhr1423HmuuPltfVY6sMwpuQbH6UJoX6GFAr8XOlYqQcOLVuxGQ/JdOniKo46
uye6f2/I/Qy12RB8r/OS+WqoIZfzxDcfOeHPzRNnxnqWRnkiCRf60OMiUgnrxde2
ndrWkwze80CnmrcXnx88s9ffEb26pQeX2eEkRA62jA9lgJF6r11332HZFauQn5Q2
IypdLOQLluKqPkHCJiau2H846jrECVQHMzFMhxsqgu0IhlGwpb8YlCSqXk9m7Oet
vqKOPOTPX8Ut6R58O9dYQyZujfvL2Q==
=cvvu
-----END PGP SIGNATURE-----

```
