---
date: '2020-11-24T16:31:11.709Z'
category: release
title: Node v15.3.0 (Current)
layout: blog-post
author: <PERSON>
---

### Notable Changes

- [[`6349b1d673`](https://github.com/nodejs/node/commit/6349b1d673)] - **(SEMVER-MINOR)** **dns**: add a cancel() method to the promise Resolver (<PERSON><PERSON><PERSON>) [#33099](https://github.com/nodejs/node/pull/33099)
- [[`9ce9b016e6`](https://github.com/nodejs/node/commit/9ce9b016e6)] - **(SEMVER-MINOR)** **events**: add max listener warning for EventTarget (<PERSON>) [#36001](https://github.com/nodejs/node/pull/36001)
- [[`8390f8a86b`](https://github.com/nodejs/node/commit/8390f8a86b)] - **(SEMVER-MINOR)** **http**: add support for abortsignal to http.request (<PERSON>) [#36048](https://github.com/nodejs/node/pull/36048)
- [[`9c6be3cc90`](https://github.com/nodejs/node/commit/9c6be3cc90)] - **(SEMVER-MINOR)** **http2**: allow setting the local window size of a session (Yongsheng Zhang) [#35978](https://github.com/nodejs/node/pull/35978)
- [[`15ff155c12`](https://github.com/nodejs/node/commit/15ff155c12)] - **(SEMVER-MINOR)** **lib**: add throws option to fs.f/l/statSync (Andrew Casey) [#33716](https://github.com/nodejs/node/pull/33716)
- [[`85c85d368a`](https://github.com/nodejs/node/commit/85c85d368a)] - **(SEMVER-MINOR)** **path**: add `path/posix` and `path/win32` alias modules (ExE Boss) [#34962](https://github.com/nodejs/node/pull/34962)
- [[`d1baae3640`](https://github.com/nodejs/node/commit/d1baae3640)] - **(SEMVER-MINOR)** **readline**: add getPrompt to get the current prompt (Mattias Runge-Broberg) [#33675](https://github.com/nodejs/node/pull/33675)
- [[`5729478509`](https://github.com/nodejs/node/commit/5729478509)] - **(SEMVER-MINOR)** **src**: add loop idle time in diagnostic report (Gireesh Punathil) [#35940](https://github.com/nodejs/node/pull/35940)
- [[`baa87c1a7d`](https://github.com/nodejs/node/commit/baa87c1a7d)] - **(SEMVER-MINOR)** **util**: add `util/types` alias module (ExE Boss) [#34055](https://github.com/nodejs/node/pull/34055)

### Commits

- [[`34aa0c868e`](https://github.com/nodejs/node/commit/34aa0c868e)] - **assert**: refactor to use more primordials (Antoine du Hamel) [#35998](https://github.com/nodejs/node/pull/35998)
- [[`28d710164a`](https://github.com/nodejs/node/commit/28d710164a)] - **async_hooks**: refactor to use more primordials (Antoine du Hamel) [#36168](https://github.com/nodejs/node/pull/36168)
- [[`1924255fdb`](https://github.com/nodejs/node/commit/1924255fdb)] - **async_hooks**: fix leak in AsyncLocalStorage exit (Stephen Belanger) [#35779](https://github.com/nodejs/node/pull/35779)
- [[`3ee556a867`](https://github.com/nodejs/node/commit/3ee556a867)] - **benchmark**: fix build warnings (Gabriel Schulhof) [#36157](https://github.com/nodejs/node/pull/36157)
- [[`fcc38a1312`](https://github.com/nodejs/node/commit/fcc38a1312)] - **build**: replace which with command -v (raisinten) [#36118](https://github.com/nodejs/node/pull/36118)
- [[`60874ba941`](https://github.com/nodejs/node/commit/60874ba941)] - **build**: try “python3” as a last resort for 3.x (Ole André Vadla Ravnås) [#35983](https://github.com/nodejs/node/pull/35983)
- [[`fbe210b2a1`](https://github.com/nodejs/node/commit/fbe210b2a1)] - **build**: conditionally clear vcinstalldir (Brian Ingenito) [#36009](https://github.com/nodejs/node/pull/36009)
- [[`56f83e6876`](https://github.com/nodejs/node/commit/56f83e6876)] - **build**: refactor configure.py to use argparse (raisinten) [#35755](https://github.com/nodejs/node/pull/35755)
- [[`0b70822461`](https://github.com/nodejs/node/commit/0b70822461)] - **child_process**: refactor to use more primordials (Antoine du Hamel) [#36003](https://github.com/nodejs/node/pull/36003)
- [[`e54108f2e4`](https://github.com/nodejs/node/commit/e54108f2e4)] - **cluster**: refactor to use more primordials (Antoine du Hamel) [#36011](https://github.com/nodejs/node/pull/36011)
- [[`272fc794b2`](https://github.com/nodejs/node/commit/272fc794b2)] - **crypto**: fix format warning in AdditionalConfig (raisinten) [#36060](https://github.com/nodejs/node/pull/36060)
- [[`63a138e02f`](https://github.com/nodejs/node/commit/63a138e02f)] - **crypto**: fix passing TypedArray to webcrypto AES methods (Antoine du Hamel) [#36087](https://github.com/nodejs/node/pull/36087)
- [[`4a88c73fa5`](https://github.com/nodejs/node/commit/4a88c73fa5)] - **deps**: upgrade npm to 7.0.14 (nlf) [#36238](https://github.com/nodejs/node/pull/36238)
- [[`d16e8622a7`](https://github.com/nodejs/node/commit/d16e8622a7)] - **deps**: upgrade npm to 7.0.13 (Ruy Adorno) [#36202](https://github.com/nodejs/node/pull/36202)
- [[`c23ee3744f`](https://github.com/nodejs/node/commit/c23ee3744f)] - **deps**: upgrade npm to 7.0.12 (Ruy Adorno) [#36153](https://github.com/nodejs/node/pull/36153)
- [[`0fcbb1c0d5`](https://github.com/nodejs/node/commit/0fcbb1c0d5)] - **deps**: V8: cherry-pick 3176bfd447a9 (Anna Henningsen) [#35612](https://github.com/nodejs/node/pull/35612)
- [[`27f1bc05fd`](https://github.com/nodejs/node/commit/27f1bc05fd)] - **deps**: upgrade npm to 7.0.11 (Darcy Clarke) [#36112](https://github.com/nodejs/node/pull/36112)
- [[`8ae3ffe2be`](https://github.com/nodejs/node/commit/8ae3ffe2be)] - **deps**: V8: cherry-pick 1d0f426311d4 (Ole André Vadla Ravnås) [#35986](https://github.com/nodejs/node/pull/35986)
- [[`4b7ba11d67`](https://github.com/nodejs/node/commit/4b7ba11d67)] - **deps**: V8: cherry-pick 4e077ff0444a (Ole André Vadla Ravnås) [#35986](https://github.com/nodejs/node/pull/35986)
- [[`098a5b1298`](https://github.com/nodejs/node/commit/098a5b1298)] - **deps**: V8: cherry-pick 086eecbd96b6 (Ole André Vadla Ravnås) [#35986](https://github.com/nodejs/node/pull/35986)
- [[`d2c757ab19`](https://github.com/nodejs/node/commit/d2c757ab19)] - **deps**: V8: cherry-pick 27e1ac1a79ff (Ole André Vadla Ravnås) [#35986](https://github.com/nodejs/node/pull/35986)
- [[`6349b1d673`](https://github.com/nodejs/node/commit/6349b1d673)] - **(SEMVER-MINOR)** **dns**: add a cancel() method to the promise Resolver (Szymon Marczak) [#33099](https://github.com/nodejs/node/pull/33099)
- [[`0fbade38ef`](https://github.com/nodejs/node/commit/0fbade38ef)] - **doc**: add arm64 macOS as experimental (Richard Lau) [#36189](https://github.com/nodejs/node/pull/36189)
- [[`42dfda8f78`](https://github.com/nodejs/node/commit/42dfda8f78)] - **doc**: remove stray comma in url.md (Rich Trott) [#36175](https://github.com/nodejs/node/pull/36175)
- [[`8bbdbccbb6`](https://github.com/nodejs/node/commit/8bbdbccbb6)] - **doc**: revise agent.destroy() text (Rich Trott) [#36163](https://github.com/nodejs/node/pull/36163)
- [[`545ac1fec5`](https://github.com/nodejs/node/commit/545ac1fec5)] - **doc**: fix punctuation in v8.md (Rich Trott) [#36192](https://github.com/nodejs/node/pull/36192)
- [[`a6a90af8c0`](https://github.com/nodejs/node/commit/a6a90af8c0)] - **doc**: add compatibility/interop technical value (Geoffrey Booth) [#35323](https://github.com/nodejs/node/pull/35323)
- [[`4ab4a99900`](https://github.com/nodejs/node/commit/4ab4a99900)] - **doc**: de-emphasize wrapping in napi_define_class (Gabriel Schulhof) [#36159](https://github.com/nodejs/node/pull/36159)
- [[`bb29508e8f`](https://github.com/nodejs/node/commit/bb29508e8f)] - **doc**: add link for v8.takeCoverage() (Rich Trott) [#36135](https://github.com/nodejs/node/pull/36135)
- [[`24065b92f1`](https://github.com/nodejs/node/commit/24065b92f1)] - **doc**: mark modules implementation as stable (Guy Bedford) [#35781](https://github.com/nodejs/node/pull/35781)
- [[`142cacdc63`](https://github.com/nodejs/node/commit/142cacdc63)] - **doc**: clarify text about process not responding (Rich Trott) [#36117](https://github.com/nodejs/node/pull/36117)
- [[`0ff384b0be`](https://github.com/nodejs/node/commit/0ff384b0be)] - **doc**: esm docs consolidation and reordering (Guy Bedford) [#36046](https://github.com/nodejs/node/pull/36046)
- [[`b17a83a00d`](https://github.com/nodejs/node/commit/b17a83a00d)] - **doc**: claim ABI version for Electron v13 (Shelley Vohr) [#36101](https://github.com/nodejs/node/pull/36101)
- [[`e8a8513b2c`](https://github.com/nodejs/node/commit/e8a8513b2c)] - **doc**: fix invalid link in worker_threads.md (Rich Trott) [#36109](https://github.com/nodejs/node/pull/36109)
- [[`cd33594a0d`](https://github.com/nodejs/node/commit/cd33594a0d)] - **doc**: move shigeki to emeritus (Rich Trott) [#36093](https://github.com/nodejs/node/pull/36093)
- [[`eefc6aa6c9`](https://github.com/nodejs/node/commit/eefc6aa6c9)] - **doc**: document the error when cwd not exists in child_process.spawn (FeelyChau) [#34505](https://github.com/nodejs/node/pull/34505)
- [[`841a2812d0`](https://github.com/nodejs/node/commit/841a2812d0)] - **doc**: fix typo in debugger.md (Rich Trott) [#36066](https://github.com/nodejs/node/pull/36066)
- [[`500e709439`](https://github.com/nodejs/node/commit/500e709439)] - **doc**: update list styles for remark-parse@9 rendering (Rich Trott) [#36049](https://github.com/nodejs/node/pull/36049)
- [[`a8dab217eb`](https://github.com/nodejs/node/commit/a8dab217eb)] - **doc,url**: fix url.hostname example (Rishabh Mehan) [#33735](https://github.com/nodejs/node/pull/33735)
- [[`e48ec703ba`](https://github.com/nodejs/node/commit/e48ec703ba)] - **domain**: improve deprecation warning text for DEP0097 (Anna Henningsen) [#36136](https://github.com/nodejs/node/pull/36136)
- [[`bcbf176c22`](https://github.com/nodejs/node/commit/bcbf176c22)] - **errors**: refactor to use more primordials (Antoine du Hamel) [#36167](https://github.com/nodejs/node/pull/36167)
- [[`66788970ac`](https://github.com/nodejs/node/commit/66788970ac)] - **esm**: refactor to use more primordials (Antoine du Hamel) [#36019](https://github.com/nodejs/node/pull/36019)
- [[`9ce9b016e6`](https://github.com/nodejs/node/commit/9ce9b016e6)] - **(SEMVER-MINOR)** **events**: add max listener warning for EventTarget (James M Snell) [#36001](https://github.com/nodejs/node/pull/36001)
- [[`1550073dbc`](https://github.com/nodejs/node/commit/1550073dbc)] - **events**: disabled manual construction AbortSignal (raisinten) [#36094](https://github.com/nodejs/node/pull/36094)
- [[`8a6cabbb23`](https://github.com/nodejs/node/commit/8a6cabbb23)] - **events**: port some wpt tests (Ethan Arrowood) [#34169](https://github.com/nodejs/node/pull/34169)
- [[`3691eccf0a`](https://github.com/nodejs/node/commit/3691eccf0a)] - **fs**: remove experimental from promises.rmdir recursive (Anders Kaseorg) [#36131](https://github.com/nodejs/node/pull/36131)
- [[`76b1863240`](https://github.com/nodejs/node/commit/76b1863240)] - **fs**: filehandle read now accepts object as argument (Nikola Glavina) [#34180](https://github.com/nodejs/node/pull/34180)
- [[`2fdf509268`](https://github.com/nodejs/node/commit/2fdf509268)] - **http**: fix typo in comment (Hollow Man) [#36193](https://github.com/nodejs/node/pull/36193)
- [[`8390f8a86b`](https://github.com/nodejs/node/commit/8390f8a86b)] - **(SEMVER-MINOR)** **http**: add support for abortsignal to http.request (Benjamin Gruenbaum) [#36048](https://github.com/nodejs/node/pull/36048)
- [[`387d92fd0e`](https://github.com/nodejs/node/commit/387d92fd0e)] - **http**: onFinish will not be triggered again when finished (rickyes) [#35845](https://github.com/nodejs/node/pull/35845)
- [[`48bf59bb8b`](https://github.com/nodejs/node/commit/48bf59bb8b)] - **http2**: add support for AbortSignal to http2Session.request (Madara Uchiha) [#36070](https://github.com/nodejs/node/pull/36070)
- [[`8a0c3b9c76`](https://github.com/nodejs/node/commit/8a0c3b9c76)] - **http2**: refactor to use more primordials (Antoine du Hamel) [#36142](https://github.com/nodejs/node/pull/36142)
- [[`f0aed8c01c`](https://github.com/nodejs/node/commit/f0aed8c01c)] - **http2**: add support for TypedArray to getUnpackedSettings (Antoine du Hamel) [#36141](https://github.com/nodejs/node/pull/36141)
- [[`9c6be3cc90`](https://github.com/nodejs/node/commit/9c6be3cc90)] - **(SEMVER-MINOR)** **http2**: allow setting the local window size of a session (Yongsheng Zhang) [#35978](https://github.com/nodejs/node/pull/35978)
- [[`0b40568afe`](https://github.com/nodejs/node/commit/0b40568afe)] - **http2**: delay session.receive() by a tick (Szymon Marczak) [#35985](https://github.com/nodejs/node/pull/35985)
- [[`1a4d43f840`](https://github.com/nodejs/node/commit/1a4d43f840)] - **lib**: refactor to use more primordials (Antoine du Hamel) [#36140](https://github.com/nodejs/node/pull/36140)
- [[`d6ea12e003`](https://github.com/nodejs/node/commit/d6ea12e003)] - **lib**: set abort-controller toStringTag (Benjamin Gruenbaum) [#36115](https://github.com/nodejs/node/pull/36115)
- [[`82f1cde57e`](https://github.com/nodejs/node/commit/82f1cde57e)] - **lib**: remove primordials.SafePromise (Antoine du Hamel) [#36149](https://github.com/nodejs/node/pull/36149)
- [[`15ff155c12`](https://github.com/nodejs/node/commit/15ff155c12)] - **(SEMVER-MINOR)** **lib**: add throws option to fs.f/l/statSync (Andrew Casey) [#33716](https://github.com/nodejs/node/pull/33716)
- [[`75707f45eb`](https://github.com/nodejs/node/commit/75707f45eb)] - **lib,tools**: enforce access to prototype from primordials (Antoine du Hamel) [#36025](https://github.com/nodejs/node/pull/36025)
- [[`79b2ba6744`](https://github.com/nodejs/node/commit/79b2ba6744)] - **n-api**: clean up binding creation (Gabriel Schulhof) [#36170](https://github.com/nodejs/node/pull/36170)
- [[`5698cc08f0`](https://github.com/nodejs/node/commit/5698cc08f0)] - **n-api**: fix test_async_context warnings (Gabriel Schulhof) [#36171](https://github.com/nodejs/node/pull/36171)
- [[`3d623d850c`](https://github.com/nodejs/node/commit/3d623d850c)] - **n-api**: improve consistency of how we get context (Michael Dawson) [#36068](https://github.com/nodejs/node/pull/36068)
- [[`89da0c3353`](https://github.com/nodejs/node/commit/89da0c3353)] - **n-api**: factor out calling pattern (Gabriel Schulhof) [#36113](https://github.com/nodejs/node/pull/36113)
- [[`5c0ddbca01`](https://github.com/nodejs/node/commit/5c0ddbca01)] - **net**: fix invalid write after end error (Robert Nagy) [#36043](https://github.com/nodejs/node/pull/36043)
- [[`85c85d368a`](https://github.com/nodejs/node/commit/85c85d368a)] - **(SEMVER-MINOR)** **path**: add `path/posix` and `path/win32` alias modules (ExE Boss) [#34962](https://github.com/nodejs/node/pull/34962)
- [[`ed8af3a8b7`](https://github.com/nodejs/node/commit/ed8af3a8b7)] - **perf_hooks**: make nodeTiming a first-class object (Momtchil Momtchev) [#35977](https://github.com/nodejs/node/pull/35977)
- [[`eb9295b583`](https://github.com/nodejs/node/commit/eb9295b583)] - **promise**: emit error on domain unhandled rejections (Benjamin Gruenbaum) [#36082](https://github.com/nodejs/node/pull/36082)
- [[`59af919d6b`](https://github.com/nodejs/node/commit/59af919d6b)] - **querystring**: reduce memory usage by Int8Array (sapics) [#34179](https://github.com/nodejs/node/pull/34179)
- [[`d1baae3640`](https://github.com/nodejs/node/commit/d1baae3640)] - **(SEMVER-MINOR)** **readline**: add getPrompt to get the current prompt (Mattias Runge-Broberg) [#33675](https://github.com/nodejs/node/pull/33675)
- [[`6d1b1c7ad0`](https://github.com/nodejs/node/commit/6d1b1c7ad0)] - **src**: integrate URL::href() and use in inspector (Daijiro Wachi) [#35912](https://github.com/nodejs/node/pull/35912)
- [[`7086f2e653`](https://github.com/nodejs/node/commit/7086f2e653)] - **src**: refactor using-declarations node_env_var.cc (raisinten) [#36128](https://github.com/nodejs/node/pull/36128)
- [[`122797e87f`](https://github.com/nodejs/node/commit/122797e87f)] - **src**: remove duplicate logic for getting buffer (Yash Ladha) [#34553](https://github.com/nodejs/node/pull/34553)
- [[`5729478509`](https://github.com/nodejs/node/commit/5729478509)] - **(SEMVER-MINOR)** **src**: add loop idle time in diagnostic report (Gireesh Punathil) [#35940](https://github.com/nodejs/node/pull/35940)
- [[`a81dc9ae18`](https://github.com/nodejs/node/commit/a81dc9ae18)] - **src,crypto**: refactoring of crypto_context, SecureContext (James M Snell) [#35665](https://github.com/nodejs/node/pull/35665)
- [[`5fa35f6934`](https://github.com/nodejs/node/commit/5fa35f6934)] - **test**: update comments in test-fs-read-offset-null (Rich Trott) [#36152](https://github.com/nodejs/node/pull/36152)
- [[`73bb54af77`](https://github.com/nodejs/node/commit/73bb54af77)] - **test**: update wpt url and resource (Daijiro Wachi) [#36032](https://github.com/nodejs/node/pull/36032)
- [[`77b47dfd08`](https://github.com/nodejs/node/commit/77b47dfd08)] - **test**: fix typo in inspector-helper.js (Luigi Pinca) [#36127](https://github.com/nodejs/node/pull/36127)
- [[`474664963c`](https://github.com/nodejs/node/commit/474664963c)] - **test**: deflake test-http-destroyed-socket-write2 (Luigi Pinca) [#36120](https://github.com/nodejs/node/pull/36120)
- [[`f9bbd35937`](https://github.com/nodejs/node/commit/f9bbd35937)] - **test**: make test-http2-client-jsstream-destroy.js reliable (Rich Trott) [#36129](https://github.com/nodejs/node/pull/36129)
- [[`c19df17acb`](https://github.com/nodejs/node/commit/c19df17acb)] - **test**: add test for fs.read when offset key is null (mayank agarwal) [#35918](https://github.com/nodejs/node/pull/35918)
- [[`9405cddbee`](https://github.com/nodejs/node/commit/9405cddbee)] - **test**: improve test-stream-duplex-readable-end (Luigi Pinca) [#36056](https://github.com/nodejs/node/pull/36056)
- [[`3be5e86c57`](https://github.com/nodejs/node/commit/3be5e86c57)] - **test**: add util.inspect test for null maxStringLength (Rich Trott) [#36086](https://github.com/nodejs/node/pull/36086)
- [[`6a4cc43028`](https://github.com/nodejs/node/commit/6a4cc43028)] - **test**: replace var with const (Aleksandr Krutko) [#36069](https://github.com/nodejs/node/pull/36069)
- [[`a367c0dfc2`](https://github.com/nodejs/node/commit/a367c0dfc2)] - **timers**: refactor to use more primordials (Antoine du Hamel) [#36132](https://github.com/nodejs/node/pull/36132)
- [[`a6ef92bc27`](https://github.com/nodejs/node/commit/a6ef92bc27)] - **tools**: bump unist-util-find@1.0.1 to unist-util-find@1.0.2 (Rich Trott) [#36106](https://github.com/nodejs/node/pull/36106)
- [[`2d2491284e`](https://github.com/nodejs/node/commit/2d2491284e)] - **tools**: only use 2 cores for macos action (Myles Borins) [#36169](https://github.com/nodejs/node/pull/36169)
- [[`d8fcf2c324`](https://github.com/nodejs/node/commit/d8fcf2c324)] - **tools**: remove bashisms from license builder script (Antoine du Hamel) [#36122](https://github.com/nodejs/node/pull/36122)
- [[`7e7ddb11c0`](https://github.com/nodejs/node/commit/7e7ddb11c0)] - **tools**: hide commit queue action link (Antoine du Hamel) [#36124](https://github.com/nodejs/node/pull/36124)
- [[`63494e434a`](https://github.com/nodejs/node/commit/63494e434a)] - **tools**: update doc tools to remark-parse@9.0.0 (Rich Trott) [#36049](https://github.com/nodejs/node/pull/36049)
- [[`bf0550ce4e`](https://github.com/nodejs/node/commit/bf0550ce4e)] - **tools**: enforce use of single quotes in editorconfig (Antoine du Hamel) [#36020](https://github.com/nodejs/node/pull/36020)
- [[`49649a499e`](https://github.com/nodejs/node/commit/49649a499e)] - **tools**: fix config serialization w/ long strings (Ole André Vadla Ravnås) [#35982](https://github.com/nodejs/node/pull/35982)
- [[`be220b213d`](https://github.com/nodejs/node/commit/be220b213d)] - **tools**: update ESLint to 7.13.0 (Luigi Pinca) [#36031](https://github.com/nodejs/node/pull/36031)
- [[`4140f491fd`](https://github.com/nodejs/node/commit/4140f491fd)] - **util**: fix to inspect getters that access this (raisinten) [#36052](https://github.com/nodejs/node/pull/36052)
- [[`baa87c1a7d`](https://github.com/nodejs/node/commit/baa87c1a7d)] - **(SEMVER-MINOR)** **util**: add `util/types` alias module (ExE Boss) [#34055](https://github.com/nodejs/node/pull/34055)
- [[`f7b2fce1c1`](https://github.com/nodejs/node/commit/f7b2fce1c1)] - **vm**: refactor to use more primordials (Antoine du Hamel) [#36023](https://github.com/nodejs/node/pull/36023)
- [[`4e3883ec2d`](https://github.com/nodejs/node/commit/4e3883ec2d)] - **win,build,tools**: support VS prerelease (Baruch Odem) [#36033](https://github.com/nodejs/node/pull/36033)

Windows 32-bit Installer: https://nodejs.org/dist/v15.3.0/node-v15.3.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v15.3.0/node-v15.3.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v15.3.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v15.3.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v15.3.0/node-v15.3.0.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v15.3.0/node-v15.3.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v15.3.0/node-v15.3.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v15.3.0/node-v15.3.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v15.3.0/node-v15.3.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v15.3.0/node-v15.3.0-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v15.3.0/node-v15.3.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v15.3.0/node-v15.3.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v15.3.0/node-v15.3.0.tar.gz \
Other release files: https://nodejs.org/dist/v15.3.0/ \
Documentation: https://nodejs.org/docs/v15.3.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

5cf19b4cb855147b1fdd0e85b9d283af726e1f9e8f7274e81885a1011b33a672  node-v15.3.0-aix-ppc64.tar.gz
58376f9bf566f89a664dd3c89e087b28fddb2b99a008a4952671d9ec4cf83e42  node-v15.3.0-darwin-x64.tar.gz
32777a3f04661f936e2bbd177f9555378e0ebdecf5573b2692d66486eb85a1c5  node-v15.3.0-darwin-x64.tar.xz
0d3e4ac3b403a008eb7f59043e7c5143f34250d2cab965480c0ebfd97d4c008f  node-v15.3.0-headers.tar.gz
2735a769c788cd1fd7ec6a807efadd5d14ae9ee007aeb5238302cf86c7a8f6cf  node-v15.3.0-headers.tar.xz
3becebd1e981df27a16bde02b5ead6bd9e6bdc0840477721c6805f9089f6179d  node-v15.3.0-linux-arm64.tar.gz
3e942f20f757574bd460951f47222b64ddebc0bf0a161ba1caf63442ec9602a9  node-v15.3.0-linux-arm64.tar.xz
056942a36e4484373f9df3ed8f2c31470ca48c98f768538ce61639d9d1f67e12  node-v15.3.0-linux-armv7l.tar.gz
633be42d347b6ff37b5d45312e31893f386f0f2562524f9fa27d617014d2dfb1  node-v15.3.0-linux-armv7l.tar.xz
3532cd405fb84da3c4c6c73ee74e96669c757ff664889f44a82d543f15e67812  node-v15.3.0-linux-ppc64le.tar.gz
f1f8e1e1475a260f959aca990ba14838da97cf72ef647d12b50dbcb681eca685  node-v15.3.0-linux-ppc64le.tar.xz
34741d76044a1ce274704fd409503c5b8d9dd3e9310cb4efd217f70a36ae5ef2  node-v15.3.0-linux-s390x.tar.gz
dc78d66928d7ca8fc1aa88e2b1e4e82e29455acf75ce44232c2e1fb1d0ebe3aa  node-v15.3.0-linux-s390x.tar.xz
c3f6c64d98e623c783b7de7580365be74d8a2dba87529447ae66061609b5d0ec  node-v15.3.0-linux-x64.tar.gz
02741db3f55022a94f43fa1774e9fc389848949ec5f5cff822833d8b9711ad93  node-v15.3.0-linux-x64.tar.xz
0c275d43079164251659ee6234546ae6252016117c1d6f208abe37bef3c47769  node-v15.3.0.pkg
cadfa384a5f14591b84ce07a1afe529f28deb0d43366fb0ae4e78afba96bfaf2  node-v15.3.0.tar.gz
3bb0b9fd6ea372dc247330e67e27caab7887c454b13325e3cd6573ae212cc240  node-v15.3.0.tar.xz
bde2cbe77b67b2de72ecf955387c511d95852e6108c3b7e6b357932b10388730  node-v15.3.0-win-x64.7z
757c3f5617d28f1fc56ccb8dd7008ea865e482ae9baf5e4bf4d003afa8ae9c8b  node-v15.3.0-win-x64.zip
0e035388c090dbdbd874e93b2e2ffbe129cf99c427e42c51f3f3a6353516c6f7  node-v15.3.0-win-x86.7z
cd0042a9871469786453938e1171847599c7d9c1d6e9af14e28d96b9cff1e9af  node-v15.3.0-win-x86.zip
7d80b9c0597a0f43da16a36016e1bbdf48ca5290229ad8e84dcc1c701e4e4e74  node-v15.3.0-x64.msi
8538691b63364b2851f836174debf52ac39ee2df9d05361423e7d28823535515  node-v15.3.0-x86.msi
2a534acf12a96018368651314f6dc57d9429696e7c637810671c08c2c255dfd0  win-x64/node.exe
9023f75555d018f68f2790a92aa0de8016f19a2cd105969e21956f747686d11b  win-x64/node.lib
cd30c1283667410ea3566d134f8458258409677830253a2986541954c8063752  win-x64/node_pdb.7z
a2dbc783147f9e4ed85b157c66914ecede7b11e749b1d8bef4fd872f6e1f0a61  win-x64/node_pdb.zip
ea4dc370bca5a2ebb1b3f0c06610e7b3cb07ba0d6ad9cabb6ee925bac4bfa090  win-x86/node.exe
cda58dc8c59f44e63526e7e55b7eb9756c64a975056ce5e5eb3d2e1be34f5e26  win-x86/node.lib
fa3478d2ec5015eab3652c69fd0e07bdf55f8cd636546cb4704446bcb7dd7190  win-x86/node_pdb.7z
c03a36a8f73d8632a290cd36e7998d14eac654ab33f615a942d03a221a660842  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEueL1mBqm4M0oFg2f8TmTp1WZZTwFAl+9NTAACgkQ8TmTp1WZ
ZTy12Q//Tt9tajxEI3OjDNou6bpmEVu3nsOOOf7i0XOJ+oP0MBSJy916WgMZs43V
QZaqxmsT9WP3pU1A48UOdfjEq8klOrJAexx3LYlRYbpjv12BsmvYg7QjjFXBmke0
unZDIHRfsNfK2Tls4NfV2PWV1lcb0Z/fDrAR4zlmbatkGVJM9rjpMFSlTSLMILFl
TNLdYcUQAgbTGVFobBX98cNq4EZc0nyZ7f+W4dO0FfQ06+Uv7fmDU0eCn1kuHRiP
VVp+6J/+srjrOvZGm2ywyTCdphx+pGXQ3vJ8duoVnUmDQLt6qXR44mU+uhQgBZo9
muM1mLQ4FIjBk2dpfIGUrmfQh/O0n8xQD65Ygka6HpfkgzabJvu5E6cNd8yrUTDx
W0kDnmpq9ccGqwKGsby3GDqN91FHM9ekYZwBRM9RFXDGSME1iRgaftH/RlqFACIp
fofeB/0YqTOZfp4MYxJoVBMaXs99Nb9YnhedcwHMVlSBwxkNIEe4qwcYew0x4BCy
3YHiwA9K9VWf9yv/LpUj0NfSesvFaEyaWf231bery8H8vGaqJRsGM0LNNKqLQp1Y
6L03OeORNm7UJMQsqYRX8Bmg/Uu29xX1qX4y+YD+DeSX5BIRZNLRXfMFvEFxMCL+
xGZvBOrkrMHAQefHPS9tyRZpuDmL6hAQONdqZjac5Wed0cr6AqI=
=Kn9Q
-----END PGP SIGNATURE-----

```
