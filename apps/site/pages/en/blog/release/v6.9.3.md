---
date: '2017-01-04T00:02:08.199Z'
category: release
title: Node v6.9.3 (LTS)
layout: blog-post
author: <PERSON><PERSON>
---

While promoting additional platforms for v6.9.3 after the release, the tarballs
on the release server were overwritten and now have different shasums. In order
to remove any ambiguity around the release we have opted to do a semver patch
release, v6.9.4, with no changes.

### Notable Changes

- **build**: shared library support is now working for AIX builds (<PERSON>) [#9675](https://github.com/nodejs/node/pull/9675)
- **deps**:
  - _npm_: upgrade npm to 3.10.10 (<PERSON>) [#9847](https://github.com/nodejs/node/pull/9847)
  - _V8_: Destructuring of arrow function arguments via computed property no longer throws (<PERSON><PERSON><PERSON>) [#10386](https://github.com/nodejs/node/pull/10386)
- **inspector**: /json/version returns object, not an object wrapped in an array (<PERSON>) [#9762](https://github.com/nodejs/node/pull/9762)
- **module**: using --debug-brk and --eval together now works as expected (<PERSON><PERSON>) [#8876](https://github.com/nodejs/node/pull/8876)
- **process**: improve performance of nextTick up to 20% (Evan Lucas) [#8932](https://github.com/nodejs/node/pull/8932)
- **repl**:
  - the division operator will no longer be accidentally parsed as regex (Teddy Katz) [#10103](https://github.com/nodejs/node/pull/10103)
  - improved support for generator functions (Teddy Katz) [#9852](https://github.com/nodejs/node/pull/9852)
- **timers**: Re canceling a cancelled timers will no longer throw (Jeremiah Senkpiel) [#9685](https://github.com/nodejs/node/pull/9685)

### Commits

- [[`98b2eae328`](https://github.com/nodejs/node/commit/98b2eae328)] - **benchmark**: split timers benchmark and refactor (Rich Trott) [#9497](https://github.com/nodejs/node/pull/9497)
- [[`c32c86b3c3`](https://github.com/nodejs/node/commit/c32c86b3c3)] - **benchmark**: reformat code for clarity (Rich Trott) [#9790](https://github.com/nodejs/node/pull/9790)
- [[`a8909b833e`](https://github.com/nodejs/node/commit/a8909b833e)] - **benchmark,lib,test,tools**: remove unneeded . escape (Rich Trott) [#9449](https://github.com/nodejs/node/pull/9449)
- [[`a9d528be5b`](https://github.com/nodejs/node/commit/a9d528be5b)] - **buffer**: fix range checks for slice() (Trevor Norris) [#9174](https://github.com/nodejs/node/pull/9174)
- [[`868e5e624c`](https://github.com/nodejs/node/commit/868e5e624c)] - **build**: remove node.dsYM directory (Michaël Zasso) [#10463](https://github.com/nodejs/node/pull/10463)
- [[`66687c0906`](https://github.com/nodejs/node/commit/66687c0906)] - **build**: prioritise --shared-X-Y over pkg-config (Rod Vagg) [#9368](https://github.com/nodejs/node/pull/9368)
- [[`9703bf14ef`](https://github.com/nodejs/node/commit/9703bf14ef)] - **build**: add MAKEFLAGS="-j1" to node-gyp (Daniel Bevenius) [#9450](https://github.com/nodejs/node/pull/9450)
- [[`18b8e7bd8b`](https://github.com/nodejs/node/commit/18b8e7bd8b)] - **build**: Make configure file parseable on python3 (kalrover) [#9657](https://github.com/nodejs/node/pull/9657)
- [[`12993b298a`](https://github.com/nodejs/node/commit/12993b298a)] - **build**: default to ppc64 on AIX (Gibson Fahnestock) [#9645](https://github.com/nodejs/node/pull/9645)
- [[`5c0d82bae6`](https://github.com/nodejs/node/commit/5c0d82bae6)] - **build**: Add option to compile for coverage reports (Wayne Andrews) [#9463](https://github.com/nodejs/node/pull/9463)
- [[`168241a98a`](https://github.com/nodejs/node/commit/168241a98a)] - **build**: add shared library support to AIX build (Stewart Addison) [#9675](https://github.com/nodejs/node/pull/9675)
- [[`9a526cb8fe`](https://github.com/nodejs/node/commit/9a526cb8fe)] - **child_process**: remove unreachable code (cjihrig) [#9307](https://github.com/nodejs/node/pull/9307)
- [[`166eea7534`](https://github.com/nodejs/node/commit/166eea7534)] - **constants**: errors -> errno (Bryan English) [#9349](https://github.com/nodejs/node/pull/9349)
- [[`3c09579eee`](https://github.com/nodejs/node/commit/3c09579eee)] - **crypto**: use SSL_get_servername. (Adam Langley) [#9347](https://github.com/nodejs/node/pull/9347)
- [[`106e6cdebd`](https://github.com/nodejs/node/commit/106e6cdebd)] - **debugger**: refactor \_debugger.js (Rich Trott) [#9860](https://github.com/nodejs/node/pull/9860)
- [[`e60cafdb3b`](https://github.com/nodejs/node/commit/e60cafdb3b)] - **deps**: backport f795a79 from upstream V8 (Michaël Zasso) [#10386](https://github.com/nodejs/node/pull/10386)
- [[`284d3cc3b7`](https://github.com/nodejs/node/commit/284d3cc3b7)] - **deps**: upgrade npm to 3.10.10 (Rebecca Turner) [#9847](https://github.com/nodejs/node/pull/9847)
- [[`ee09828622`](https://github.com/nodejs/node/commit/ee09828622)] - **deps**: backport 2bd7464 from upstream V8 (Cristian Cavalli) [#10169](https://github.com/nodejs/node/pull/10169)
- [[`10222128e9`](https://github.com/nodejs/node/commit/10222128e9)] - **deps**: backport GYP fix to fix AIX shared suffix (Stewart Addison) [#9675](https://github.com/nodejs/node/pull/9675)
- [[`1684d6d65e`](https://github.com/nodejs/node/commit/1684d6d65e)] - **doc**: update CONTRIBUTING.MD with link to V8 guide (sarahmeyer) [#10070](https://github.com/nodejs/node/pull/10070)
- [[`f9d0cce9ae`](https://github.com/nodejs/node/commit/f9d0cce9ae)] - **doc**: update process.versions.modules documentation (Kevin Zurawel) [#9901](https://github.com/nodejs/node/pull/9901)
- [[`acebfedf80`](https://github.com/nodejs/node/commit/acebfedf80)] - **doc**: add return types and props types to OS module (imatvieiev) [#9648](https://github.com/nodejs/node/pull/9648)
- [[`241470cfbe`](https://github.com/nodejs/node/commit/241470cfbe)] - **doc**: small improvements in readline code examples (Vse Mozhet Byt) [#9628](https://github.com/nodejs/node/pull/9628)
- [[`d33520cdd2`](https://github.com/nodejs/node/commit/d33520cdd2)] - **doc**: consistent 'Returns:' (Roman Reiss) [#9554](https://github.com/nodejs/node/pull/9554)
- [[`c87ccfa3c3`](https://github.com/nodejs/node/commit/c87ccfa3c3)] - **doc**: added types to path docs (imatvieiev) [#9514](https://github.com/nodejs/node/pull/9514)
- [[`d2a1a670e1`](https://github.com/nodejs/node/commit/d2a1a670e1)] - **doc**: add process api data types to documentation (imatvieiev) [#9505](https://github.com/nodejs/node/pull/9505)
- [[`912cae626b`](https://github.com/nodejs/node/commit/912cae626b)] - **doc**: clarify eventType in fs.watch (Nikolai Vavilov) [#9318](https://github.com/nodejs/node/pull/9318)
- [[`30f7802b78`](https://github.com/nodejs/node/commit/30f7802b78)] - **doc**: clarify fs.link and fs.linkSync arguments (Kyle E. Mitchell) [#9145](https://github.com/nodejs/node/pull/9145)
- [[`c55fb737c5`](https://github.com/nodejs/node/commit/c55fb737c5)] - **doc**: adding missing - in README (Italo A. Casas) [#10170](https://github.com/nodejs/node/pull/10170)
- [[`7f4cef1170`](https://github.com/nodejs/node/commit/7f4cef1170)] - **doc**: removing extra space in README (Italo A. Casas) [#10168](https://github.com/nodejs/node/pull/10168)
- [[`e0dbb453e4`](https://github.com/nodejs/node/commit/e0dbb453e4)] - **doc**: remove repeated info onboarding.md (BethGriggs) [#9635](https://github.com/nodejs/node/pull/9635)
- [[`fa7d378335`](https://github.com/nodejs/node/commit/fa7d378335)] - **doc**: correct it's vs. its usage (Rich Trott) [#10098](https://github.com/nodejs/node/pull/10098)
- [[`176f680432`](https://github.com/nodejs/node/commit/176f680432)] - **doc**: add people to cc for async_wrap (Anna Henningsen) [#9471](https://github.com/nodejs/node/pull/9471)
- [[`b77d3d86f7`](https://github.com/nodejs/node/commit/b77d3d86f7)] - **doc**: add link to `net.Server` in tls.md (Devon Rifkin) [#10109](https://github.com/nodejs/node/pull/10109)
- [[`b167727dcc`](https://github.com/nodejs/node/commit/b167727dcc)] - **doc**: fix typo for `decipher.final`. (iamchenxin) [#10086](https://github.com/nodejs/node/pull/10086)
- [[`adb30676b2`](https://github.com/nodejs/node/commit/adb30676b2)] - **doc**: suggest Buffer.alloc instead of Buffer#fill (Teddy Katz) [#10000](https://github.com/nodejs/node/pull/10000)
- [[`36b45c1112`](https://github.com/nodejs/node/commit/36b45c1112)] - **doc**: clarify fs.createReadStream options (Wes Tyler) [#10078](https://github.com/nodejs/node/pull/10078)
- [[`fc6c666d49`](https://github.com/nodejs/node/commit/fc6c666d49)] - **doc**: var => const in js code examples of addons.md (Vse Mozhet Byt) [#10092](https://github.com/nodejs/node/pull/10092)
- [[`0e46f7e745`](https://github.com/nodejs/node/commit/0e46f7e745)] - **doc**: rename writing_tests.md to writing-tests.md (Safia Abdalla) [#9867](https://github.com/nodejs/node/pull/9867)
- [[`99ed5ed5f4`](https://github.com/nodejs/node/commit/99ed5ed5f4)] - **doc**: it’s -> its in api/child_process.md (Devon Rifkin) [#10090](https://github.com/nodejs/node/pull/10090)
- [[`d068fe5ab6`](https://github.com/nodejs/node/commit/d068fe5ab6)] - **doc**: update Collaborators list in README (Rich Trott) [#9846](https://github.com/nodejs/node/pull/9846)
- [[`e5ab0e8670`](https://github.com/nodejs/node/commit/e5ab0e8670)] - **doc**: remove minor contradiction in debugger doc (Rich Trott) [#9832](https://github.com/nodejs/node/pull/9832)
- [[`b74d8cdbdb`](https://github.com/nodejs/node/commit/b74d8cdbdb)] - **doc**: clarify introductory module material (Rich Trott) [#9816](https://github.com/nodejs/node/pull/9816)
- [[`ba077a424b`](https://github.com/nodejs/node/commit/ba077a424b)] - **doc**: improve description of module `exports` (Sam Roberts) [#9622](https://github.com/nodejs/node/pull/9622)
- [[`5396408690`](https://github.com/nodejs/node/commit/5396408690)] - **doc**: fix crypto Verify cut-n-paste from Sign (子丶言) [#9796](https://github.com/nodejs/node/pull/9796)
- [[`9c3f4d63cc`](https://github.com/nodejs/node/commit/9c3f4d63cc)] - **doc**: minor fixes event-loop-timers-and-nexttick.md (Dan Koster) [#9126](https://github.com/nodejs/node/pull/9126)
- [[`87f008393e`](https://github.com/nodejs/node/commit/87f008393e)] - **doc**: changed order of invocations in https.request() example. (atrioom) [#9614](https://github.com/nodejs/node/pull/9614)
- [[`7051ea8606`](https://github.com/nodejs/node/commit/7051ea8606)] - **doc**: fix crypto "decipher.setAAD()" typo (子丶言) [#9782](https://github.com/nodejs/node/pull/9782)
- [[`4b7200ef7b`](https://github.com/nodejs/node/commit/4b7200ef7b)] - **doc**: clarify slashes-appending in url module (Rich Trott) [#9731](https://github.com/nodejs/node/pull/9731)
- [[`1c9817cbeb`](https://github.com/nodejs/node/commit/1c9817cbeb)] - **doc**: "util" is not needed to extend ES6 classes (Adam Brunner) [#9737](https://github.com/nodejs/node/pull/9737)
- [[`4334d6a85a`](https://github.com/nodejs/node/commit/4334d6a85a)] - **doc**: fix typo in assert code example (Vse Mozhet Byt) [#9704](https://github.com/nodejs/node/pull/9704)
- [[`cbea672214`](https://github.com/nodejs/node/commit/cbea672214)] - **doc**: fix typo in BUILDING.md (monkick) [#9569](https://github.com/nodejs/node/pull/9569)
- [[`7a02eb2b03`](https://github.com/nodejs/node/commit/7a02eb2b03)] - **doc**: remove backtick escaping for manpage refs (Anna Henningsen) [#9632](https://github.com/nodejs/node/pull/9632)
- [[`cd3b91bc4e`](https://github.com/nodejs/node/commit/cd3b91bc4e)] - **doc**: improve description of urlObject.query (Rahat Ahmed) [#9625](https://github.com/nodejs/node/pull/9625)
- [[`ff7d85647b`](https://github.com/nodejs/node/commit/ff7d85647b)] - **doc**: remove invalid padding from privateEncrypt (JungMinu) [#9611](https://github.com/nodejs/node/pull/9611)
- [[`50947b7b0f`](https://github.com/nodejs/node/commit/50947b7b0f)] - **doc**: remove Sam Roberts from release team (Sam Roberts) [#9862](https://github.com/nodejs/node/pull/9862)
- [[`cf131bfa90`](https://github.com/nodejs/node/commit/cf131bfa90)] - **doc**: add guide for maintaining V8 (Ali Ijaz Sheikh) [#9777](https://github.com/nodejs/node/pull/9777)
- [[`9796efabc6`](https://github.com/nodejs/node/commit/9796efabc6)] - **doc**: strip trailing whitespace (Sam Roberts) [#9620](https://github.com/nodejs/node/pull/9620)
- [[`35b094b8fe`](https://github.com/nodejs/node/commit/35b094b8fe)] - **doc**: fix "either as either" typo (Sam Roberts) [#9665](https://github.com/nodejs/node/pull/9665)
- [[`1a58a21d29`](https://github.com/nodejs/node/commit/1a58a21d29)] - **doc**: fix tls "the the" typo (Sam Roberts) [#9665](https://github.com/nodejs/node/pull/9665)
- [[`4f205deb66`](https://github.com/nodejs/node/commit/4f205deb66)] - **doc**: describe when a tls server emits 'close' (Sam Roberts) [#9665](https://github.com/nodejs/node/pull/9665)
- [[`4d4ac071da`](https://github.com/nodejs/node/commit/4d4ac071da)] - **doc**: fix an SNI mistyped as SNS (Sam Roberts) [#9665](https://github.com/nodejs/node/pull/9665)
- [[`8475ba294d`](https://github.com/nodejs/node/commit/8475ba294d)] - **doc**: move TSC and CTC meeting minutes out of core repo (James M Snell) [#9503](https://github.com/nodejs/node/pull/9503)
- [[`d343595216`](https://github.com/nodejs/node/commit/d343595216)] - **doc**: fix typo in doc/repl.md line: 6 (Mitsuo Utano) [#9582](https://github.com/nodejs/node/pull/9582)
- [[`d283704cd6`](https://github.com/nodejs/node/commit/d283704cd6)] - **doc**: make comment indentation consistent (Daniel Bevenius) [#9518](https://github.com/nodejs/node/pull/9518)
- [[`b99a9e9a81`](https://github.com/nodejs/node/commit/b99a9e9a81)] - **doc**: wrap long lines in http.request (Timothy Gu) [#9584](https://github.com/nodejs/node/pull/9584)
- [[`fd75b25cbf`](https://github.com/nodejs/node/commit/fd75b25cbf)] - **doc**: fix type of http.request's `agent` option (Timothy Gu) [#9584](https://github.com/nodejs/node/pull/9584)
- [[`1783d3b5fc`](https://github.com/nodejs/node/commit/1783d3b5fc)] - **doc**: fix a typo in the assert.md (Vse Mozhet Byt) [#9598](https://github.com/nodejs/node/pull/9598)
- [[`c286f5719c`](https://github.com/nodejs/node/commit/c286f5719c)] - **doc**: fix typo e.g., => e.g. (Daijiro Yamada) [#9563](https://github.com/nodejs/node/pull/9563)
- [[`82fd0e192f`](https://github.com/nodejs/node/commit/82fd0e192f)] - **doc**: fix typo about cluster doc, (eg. -> e.g.) (YutamaKotaro) [#9568](https://github.com/nodejs/node/pull/9568)
- [[`1bbc2c682a`](https://github.com/nodejs/node/commit/1bbc2c682a)] - **doc**: fix typo in doc/tls.md (Syuhei Kobayashi) [#9566](https://github.com/nodejs/node/pull/9566)
- [[`97093314c2`](https://github.com/nodejs/node/commit/97093314c2)] - **doc**: fix e.g., to e.g. in doc/http.md (ikasumi_wt) [#9564](https://github.com/nodejs/node/pull/9564)
- [[`caa0a7876d`](https://github.com/nodejs/node/commit/caa0a7876d)] - **doc**: fix the index order in pseudocode of modules (kohta ito) [#9562](https://github.com/nodejs/node/pull/9562)
- [[`0088ed87ea`](https://github.com/nodejs/node/commit/0088ed87ea)] - **doc**: remove Roadmap Working Group (William Kapke) [#9545](https://github.com/nodejs/node/pull/9545)
- [[`3219ecea2a`](https://github.com/nodejs/node/commit/3219ecea2a)] - **doc**: fix fs constants link (Timothy) [#9508](https://github.com/nodejs/node/pull/9508)
- [[`cb367ec1b3`](https://github.com/nodejs/node/commit/cb367ec1b3)] - **doc**: fix minor style issue in code examples (Daniel Bevenius) [#9482](https://github.com/nodejs/node/pull/9482)
- [[`ff8c31abfd`](https://github.com/nodejs/node/commit/ff8c31abfd)] - **doc**: grammar and structure revisions of wg doc (Ryan Lewis) [#9495](https://github.com/nodejs/node/pull/9495)
- [[`6f850f4037`](https://github.com/nodejs/node/commit/6f850f4037)] - **doc**: clarify the exit code part of writing_tests (Jeremiah Senkpiel) [#9502](https://github.com/nodejs/node/pull/9502)
- [[`16b53141f7`](https://github.com/nodejs/node/commit/16b53141f7)] - **doc**: fix link to Event Loop page (timathon) [#9527](https://github.com/nodejs/node/pull/9527)
- [[`2d581f1cb5`](https://github.com/nodejs/node/commit/2d581f1cb5)] - **doc**: Fix inaccuracy in https.request docs (Andreas Lind) [#9453](https://github.com/nodejs/node/pull/9453)
- [[`f707b006eb`](https://github.com/nodejs/node/commit/f707b006eb)] - **doc**: add npm link to README (Oscar Morrison) [#7894](https://github.com/nodejs/node/pull/7894)
- [[`2ce6916ddc`](https://github.com/nodejs/node/commit/2ce6916ddc)] - **events**: remove unnecessary checks (cjihrig) [#9330](https://github.com/nodejs/node/pull/9330)
- [[`fe821fbefa`](https://github.com/nodejs/node/commit/fe821fbefa)] - **fs**: clarify fs.link and fs.linkSync arguments (Kyle E. Mitchell) [#9145](https://github.com/nodejs/node/pull/9145)
- [[`a3ba4ff49f`](https://github.com/nodejs/node/commit/a3ba4ff49f)] - **inspector**: /json/version returns object, not array (Ben Noordhuis) [#9762](https://github.com/nodejs/node/pull/9762)
- [[`6632b3d1ab`](https://github.com/nodejs/node/commit/6632b3d1ab)] - **lib**: use === in \_http_server and \_tls_wrap (Walter Beller-Morales) [#9849](https://github.com/nodejs/node/pull/9849)
- [[`f3861c200d`](https://github.com/nodejs/node/commit/f3861c200d)] - **lib,test**: remove unneeded escaping of / (Rich Trott) [#9485](https://github.com/nodejs/node/pull/9485)
- [[`0be56cd1e9`](https://github.com/nodejs/node/commit/0be56cd1e9)] - **meta**: whitelist dotfiles in .gitignore (Claudio Rodriguez) [#8016](https://github.com/nodejs/node/pull/8016)
- [[`3689813fdd`](https://github.com/nodejs/node/commit/3689813fdd)] - **module**: check -e flag in debug break setup (Kelvin Jin) [#8876](https://github.com/nodejs/node/pull/8876)
- [[`db10e94083`](https://github.com/nodejs/node/commit/db10e94083)] - **process**: improve performance of nextTick (Evan Lucas) [#8932](https://github.com/nodejs/node/pull/8932)
- [[`fac61118f9`](https://github.com/nodejs/node/commit/fac61118f9)] - **repl**: avoid parsing division operator as regex (Teddy Katz) [#10103](https://github.com/nodejs/node/pull/10103)
- [[`86efc93a41`](https://github.com/nodejs/node/commit/86efc93a41)] - **repl**: preprocess only for defaultEval (Prince J Wesley) [#9752](https://github.com/nodejs/node/pull/9752)
- [[`eba4f9a3ff`](https://github.com/nodejs/node/commit/eba4f9a3ff)] - **repl**: fix generator function preprocessing (Teddy Katz) [#9852](https://github.com/nodejs/node/pull/9852)
- [[`70062f7cd7`](https://github.com/nodejs/node/commit/70062f7cd7)] - **repl**: refactor lib/repl.js (Rich Trott) [#9374](https://github.com/nodejs/node/pull/9374)
- [[`f9fd53d82d`](https://github.com/nodejs/node/commit/f9fd53d82d)] - **src**: fix method name, output format (Josh Gavant) [#9627](https://github.com/nodejs/node/pull/9627)
- [[`dee5a7f9be`](https://github.com/nodejs/node/commit/dee5a7f9be)] - **test**: invalid package.json causes error when require()ing in directory (Sam Shull) [#10044](https://github.com/nodejs/node/pull/10044)
- [[`487f91a097`](https://github.com/nodejs/node/commit/487f91a097)] - **test**: refactor domain test (Adao Junior) [#10269](https://github.com/nodejs/node/pull/10269)
- [[`26aa148ac5`](https://github.com/nodejs/node/commit/26aa148ac5)] - **test**: refactor test-child-process-stdin (Segu Riluvan) [#10420](https://github.com/nodejs/node/pull/10420)
- [[`25b76a44a7`](https://github.com/nodejs/node/commit/25b76a44a7)] - **test**: test error messages in test-dns-regress-7070 (Wallace Zhang) [#10058](https://github.com/nodejs/node/pull/10058)
- [[`8389a1eef4`](https://github.com/nodejs/node/commit/8389a1eef4)] - **test**: refactor test-pipe-file-to-http (Josh Mays) [#10054](https://github.com/nodejs/node/pull/10054)
- [[`f9f2cda5dc`](https://github.com/nodejs/node/commit/f9f2cda5dc)] - **test**: refactor test-cluster-send-handle-twice.js (Amar Zavery) [#10049](https://github.com/nodejs/node/pull/10049)
- [[`aba15fb9f2`](https://github.com/nodejs/node/commit/aba15fb9f2)] - **test**: add regex check in test-buffer-bad-overload (Sam Shull) [#10038](https://github.com/nodejs/node/pull/10038)
- [[`ac9348d79f`](https://github.com/nodejs/node/commit/ac9348d79f)] - **test**: refactoring test-pipe-head (Travis Bretton) [#10036](https://github.com/nodejs/node/pull/10036)
- [[`0ab2cfc64e`](https://github.com/nodejs/node/commit/0ab2cfc64e)] - **test**: add second argument to assert.throws() (Ken Russo) [#9987](https://github.com/nodejs/node/pull/9987)
- [[`610ec557a6`](https://github.com/nodejs/node/commit/610ec557a6)] - **test**: refactor test-child-process-ipc (malen) [#9990](https://github.com/nodejs/node/pull/9990)
- [[`ff3a1e69f5`](https://github.com/nodejs/node/commit/ff3a1e69f5)] - **test**: cleanup test-stdout-close-catch.js (Travis Bretton) [#10006](https://github.com/nodejs/node/pull/10006)
- [[`49e7029283`](https://github.com/nodejs/node/commit/49e7029283)] - **test**: refactor test-internal-modules (Christy Leung) [#10016](https://github.com/nodejs/node/pull/10016)
- [[`21a60912a8`](https://github.com/nodejs/node/commit/21a60912a8)] - **test**: refactor test-tls-interleave (Brian Chirgwin) [#10017](https://github.com/nodejs/node/pull/10017)
- [[`e53506ac01`](https://github.com/nodejs/node/commit/e53506ac01)] - **test**: update test-tls-check-server-identity.js (Kevin Cox) [#9986](https://github.com/nodejs/node/pull/9986)
- [[`bdf633a32e`](https://github.com/nodejs/node/commit/bdf633a32e)] - **test**: use const/let and common.mustCall (Outsider) [#9959](https://github.com/nodejs/node/pull/9959)
- [[`77334a2143`](https://github.com/nodejs/node/commit/77334a2143)] - **test**: refactoring test-cluster-worker-constructor (Christopher Rokita) [#9956](https://github.com/nodejs/node/pull/9956)
- [[`3c3d2d6776`](https://github.com/nodejs/node/commit/3c3d2d6776)] - **test**: refactor test-tls-client-getephemeralkeyinfo (Harish Tejwani) [#9954](https://github.com/nodejs/node/pull/9954)
- [[`da3ccb969d`](https://github.com/nodejs/node/commit/da3ccb969d)] - **test**: improve test-cluster-net-listen.js (Rico Cai) [#9953](https://github.com/nodejs/node/pull/9953)
- [[`9991bcbada`](https://github.com/nodejs/node/commit/9991bcbada)] - **test**: improve domain-top-level-error-handler-throw (CodeVana) [#9950](https://github.com/nodejs/node/pull/9950)
- [[`eff85e659f`](https://github.com/nodejs/node/commit/eff85e659f)] - **test**: refactor test-tls-0-dns-altname (Richard Karmazin) [#9948](https://github.com/nodejs/node/pull/9948)
- [[`b37fce91d3`](https://github.com/nodejs/node/commit/b37fce91d3)] - **test**: test: refactor test-sync-fileread (Jason Wohlgemuth) [#9941](https://github.com/nodejs/node/pull/9941)
- [[`420b3b851e`](https://github.com/nodejs/node/commit/420b3b851e)] - **test**: clean up repl-reset-event file (Kailean Courtney) [#9931](https://github.com/nodejs/node/pull/9931)
- [[`b8511aba04`](https://github.com/nodejs/node/commit/b8511aba04)] - **test**: replace var with const in test-require-dot (Amar Zavery) [#9916](https://github.com/nodejs/node/pull/9916)
- [[`68836ec455`](https://github.com/nodejs/node/commit/68836ec455)] - **test**: added validation regex argument to test (Avery, Frank) [#9918](https://github.com/nodejs/node/pull/9918)
- [[`70d3b808a0`](https://github.com/nodejs/node/commit/70d3b808a0)] - **test**: fix test-buffer-slow (Michaël Zasso) [#9809](https://github.com/nodejs/node/pull/9809)
- [[`3d368d0322`](https://github.com/nodejs/node/commit/3d368d0322)] - **test**: refactor test-buffer-bytelength (Michaël Zasso) [#9808](https://github.com/nodejs/node/pull/9808)
- [[`b5c8b355c8`](https://github.com/nodejs/node/commit/b5c8b355c8)] - **test**: add stdin-setrawmode.out file (Jonathan Darling) [#10149](https://github.com/nodejs/node/pull/10149)
- [[`e057925316`](https://github.com/nodejs/node/commit/e057925316)] - **test**: set stdin too for pseudo-tty tests (Anna Henningsen) [#10149](https://github.com/nodejs/node/pull/10149)
- [[`272a97178d`](https://github.com/nodejs/node/commit/272a97178d)] - **test**: refactor and fix test-crypto (Michaël Zasso) [#9807](https://github.com/nodejs/node/pull/9807)
- [[`65e27176f6`](https://github.com/nodejs/node/commit/65e27176f6)] - **test**: add child_process customFds test (cjihrig) [#9307](https://github.com/nodejs/node/pull/9307)
- [[`dc76aaef50`](https://github.com/nodejs/node/commit/dc76aaef50)] - **test**: refactor test-crypto-hmac (eudaimos) [#9958](https://github.com/nodejs/node/pull/9958)
- [[`1bbf143898`](https://github.com/nodejs/node/commit/1bbf143898)] - **test**: renamed assert.Equal to assert.strictEqual (Jared Young)
- [[`6dbff7aaed`](https://github.com/nodejs/node/commit/6dbff7aaed)] - **test**: convert assert.equal to assert.strictEqual (Jonathan Darling) [#9925](https://github.com/nodejs/node/pull/9925)
- [[`bbebebe087`](https://github.com/nodejs/node/commit/bbebebe087)] - **test**: strictEqual() and RegExp in test-buffer-fill.js (J Scott Chapman) [#9895](https://github.com/nodejs/node/pull/9895)
- [[`afbd8df7fd`](https://github.com/nodejs/node/commit/afbd8df7fd)] - **test**: increase coverage for lib/events.js (Safia Abdalla) [#9865](https://github.com/nodejs/node/pull/9865)
- [[`99ef3c0e45`](https://github.com/nodejs/node/commit/99ef3c0e45)] - **test**: run cpplint on files in test/cctest (Ben Noordhuis) [#9787](https://github.com/nodejs/node/pull/9787)
- [[`2ffd13e90d`](https://github.com/nodejs/node/commit/2ffd13e90d)] - **test**: move tick-processor tests to own directory (Rich Trott) [#9506](https://github.com/nodejs/node/pull/9506)
- [[`fb525f1507`](https://github.com/nodejs/node/commit/fb525f1507)] - **test**: fix error in test-cluster-worker-death.js (Bruce Lai) [#9981](https://github.com/nodejs/node/pull/9981)
- [[`1288d074ab`](https://github.com/nodejs/node/commit/1288d074ab)] - **test**: use `assert.strictEqual` (anoff) [#9975](https://github.com/nodejs/node/pull/9975)
- [[`653f2b76f3`](https://github.com/nodejs/node/commit/653f2b76f3)] - **test**: change assert.equal to assert.strictEqual (Aileen) [#9946](https://github.com/nodejs/node/pull/9946)
- [[`70c5e4fca2`](https://github.com/nodejs/node/commit/70c5e4fca2)] - **test**: changed assert.equal to assert.strictEqual (vazina robertson) [#10015](https://github.com/nodejs/node/pull/10015)
- [[`690cc2a88f`](https://github.com/nodejs/node/commit/690cc2a88f)] - **test**: improves test-tls-client-verify (Paul Graham) [#10051](https://github.com/nodejs/node/pull/10051)
- [[`780d444d3f`](https://github.com/nodejs/node/commit/780d444d3f)] - **test**: refactor test-https-agent-session-reuse (Diego Paez) [#10105](https://github.com/nodejs/node/pull/10105)
- [[`3686687cd2`](https://github.com/nodejs/node/commit/3686687cd2)] - **test**: refactor test-beforeexit-event (Rob Adelmann) [#10121](https://github.com/nodejs/node/pull/10121)
- [[`314b04d2d9`](https://github.com/nodejs/node/commit/314b04d2d9)] - **test**: improve test-fs-read-stream.js (Jenna Vuong) [#9629](https://github.com/nodejs/node/pull/9629)
- [[`a6bc868bf9`](https://github.com/nodejs/node/commit/a6bc868bf9)] - **test**: refactor test-domain-from-timer (Daniel Sims) [#9889](https://github.com/nodejs/node/pull/9889)
- [[`793addf585`](https://github.com/nodejs/node/commit/793addf585)] - **test**: refactor test-domain-exit-dispose-again (Ethan Arrowood) [#10003](https://github.com/nodejs/node/pull/10003)
- [[`faf0f2d254`](https://github.com/nodejs/node/commit/faf0f2d254)] - **test**: use const and strictEqual in test-os-homedir-no-envvar (CodeVana) [#9899](https://github.com/nodejs/node/pull/9899)
- [[`a696934faa`](https://github.com/nodejs/node/commit/a696934faa)] - **test**: check result of uv_loop_init and uv_write (Ben Noordhuis) [#10126](https://github.com/nodejs/node/pull/10126)
- [[`c2d7e67458`](https://github.com/nodejs/node/commit/c2d7e67458)] - **test**: refactor test-dgram-bind-default-address (Michael-Bryant Choa) [#9947](https://github.com/nodejs/node/pull/9947)
- [[`3c46ab69af`](https://github.com/nodejs/node/commit/3c46ab69af)] - **test**: assert.throws() should include a RegExp (Chris Bystrek) [#9976](https://github.com/nodejs/node/pull/9976)
- [[`0e3593a454`](https://github.com/nodejs/node/commit/0e3593a454)] - **test**: refactor test-listen-fd-ebadf (Richard Karmazin) [#10034](https://github.com/nodejs/node/pull/10034)
- [[`e49c7bbae3`](https://github.com/nodejs/node/commit/e49c7bbae3)] - **test**: refactor test-event-emitter-method-names (Rodrigo Palma) [#10027](https://github.com/nodejs/node/pull/10027)
- [[`290f359857`](https://github.com/nodejs/node/commit/290f359857)] - **test**: refactor tls-ticket-cluster (Yojan Shrestha) [#10023](https://github.com/nodejs/node/pull/10023)
- [[`5d9c224384`](https://github.com/nodejs/node/commit/5d9c224384)] - **test**: refactor test-domain-exit-dispose (Chris Henney) [#9938](https://github.com/nodejs/node/pull/9938)
- [[`7c929591c1`](https://github.com/nodejs/node/commit/7c929591c1)] - **test**: refactor test-stdin-from-file.js (amrios) [#10012](https://github.com/nodejs/node/pull/10012)
- [[`9af076e97d`](https://github.com/nodejs/node/commit/9af076e97d)] - **test**: use ES6 to update let & const (Jason Humphrey) [#9917](https://github.com/nodejs/node/pull/9917)
- [[`dd4586bd41`](https://github.com/nodejs/node/commit/dd4586bd41)] - **test**: fix test for buffer regression #649 (joyeecheung) [#9924](https://github.com/nodejs/node/pull/9924)
- [[`fed9acd8af`](https://github.com/nodejs/node/commit/fed9acd8af)] - **test**: update parallel/test-crypto-hash.js (Deepti Agrawal) [#10009](https://github.com/nodejs/node/pull/10009)
- [[`d64cb1e04e`](https://github.com/nodejs/node/commit/d64cb1e04e)] - **test**: refactor test-require-extensions-main (Daryl Thayil) [#9912](https://github.com/nodejs/node/pull/9912)
- [[`cdb803d18b`](https://github.com/nodejs/node/commit/cdb803d18b)] - **test**: refactor test-tls-ocsp-callback (k3kathy) [#9970](https://github.com/nodejs/node/pull/9970)
- [[`78b5a8d5c2`](https://github.com/nodejs/node/commit/78b5a8d5c2)] - **test**: use assert.strictEqual and fix setTimeout (Matt Phillips) [#9957](https://github.com/nodejs/node/pull/9957)
- [[`f4c8044007`](https://github.com/nodejs/node/commit/f4c8044007)] - **test**: clean up tls junk test (Danny Guo) [#9940](https://github.com/nodejs/node/pull/9940)
- [[`626d59f7a6`](https://github.com/nodejs/node/commit/626d59f7a6)] - **test**: update test-stdout-to-file (scalkpdev) [#9939](https://github.com/nodejs/node/pull/9939)
- [[`223ec17080`](https://github.com/nodejs/node/commit/223ec17080)] - **test**: changed assert.Equal to asset.strictEqual (Paul Chin) [#9973](https://github.com/nodejs/node/pull/9973)
- [[`230d552a85`](https://github.com/nodejs/node/commit/230d552a85)] - **test**: refactor test-domain-multi (Wes Tyler) [#9963](https://github.com/nodejs/node/pull/9963)
- [[`b893dc986c`](https://github.com/nodejs/node/commit/b893dc986c)] - **test**: refactor test-fs-write.js (hirabhullar) [#9982](https://github.com/nodejs/node/pull/9982)
- [[`c506b7be90`](https://github.com/nodejs/node/commit/c506b7be90)] - **test**: refactor test-child-fork-exec-path.js (hirabhullar) [#9982](https://github.com/nodejs/node/pull/9982)
- [[`050bae63f1`](https://github.com/nodejs/node/commit/050bae63f1)] - **test**: use assert.strictEqual in test-cli-eval (Nigel Kibodeaux) [#9919](https://github.com/nodejs/node/pull/9919)
- [[`2a514f20e1`](https://github.com/nodejs/node/commit/2a514f20e1)] - **test**: refactor test-tls-connect-simple (Russell Sherman) [#9934](https://github.com/nodejs/node/pull/9934)
- [[`75c37fa8a2`](https://github.com/nodejs/node/commit/75c37fa8a2)] - **test**: refactor test-signal-unregister (mark hughes) [#9920](https://github.com/nodejs/node/pull/9920)
- [[`093adcac9a`](https://github.com/nodejs/node/commit/093adcac9a)] - **test**: update test-net-connect-handle-econnrefused (Punit Buch) [#9932](https://github.com/nodejs/node/pull/9932)
- [[`75712a3032`](https://github.com/nodejs/node/commit/75712a3032)] - **test**: refactor test-require-resolve (blugavere) [#10120](https://github.com/nodejs/node/pull/10120)
- [[`4a28eac54b`](https://github.com/nodejs/node/commit/4a28eac54b)] - **test**: refactor test-fs-symlink-dir-junction (Walter Beller-Morales) [#9928](https://github.com/nodejs/node/pull/9928)
- [[`09de7149f2`](https://github.com/nodejs/node/commit/09de7149f2)] - **test**: refactor test-fs-read-stream-resume (Matt Webb) [#9927](https://github.com/nodejs/node/pull/9927)
- [[`8ce6dd2a57`](https://github.com/nodejs/node/commit/8ce6dd2a57)] - **test**: replace equal with strictEqual (Tracy Hinds) [#10011](https://github.com/nodejs/node/pull/10011)
- [[`3b765cb231`](https://github.com/nodejs/node/commit/3b765cb231)] - **test**: use strictEqual instead of equal (Uttam Pawar) [#9921](https://github.com/nodejs/node/pull/9921)
- [[`baa0adfe46`](https://github.com/nodejs/node/commit/baa0adfe46)] - **test**: using const and strictEqual (Fabrice Tatieze) [#9926](https://github.com/nodejs/node/pull/9926)
- [[`8ceca4a135`](https://github.com/nodejs/node/commit/8ceca4a135)] - **test**: changed assert.equal to assert.strictEqual (Scott Smereka) [#9936](https://github.com/nodejs/node/pull/9936)
- [[`f248c67da6`](https://github.com/nodejs/node/commit/f248c67da6)] - **test**: test-file-write-stream3.js refactor (Richard Karmazin) [#10035](https://github.com/nodejs/node/pull/10035)
- [[`dd4f9195f1`](https://github.com/nodejs/node/commit/dd4f9195f1)] - **test**: implemented es6 conventions (Erez Weiss) [#9669](https://github.com/nodejs/node/pull/9669)
- [[`c30332daa6`](https://github.com/nodejs/node/commit/c30332daa6)] - **test**: Modernize test-tls-peer-certificate.js (Ilya Potuzhnov) [#10014](https://github.com/nodejs/node/pull/10014)
- [[`ba5e37765a`](https://github.com/nodejs/node/commit/ba5e37765a)] - **test**: strictCompare and explcit inputs mprovement to test-buffer-slice (Michael Alexander) [#10048](https://github.com/nodejs/node/pull/10048)
- [[`ec7df6c0a4`](https://github.com/nodejs/node/commit/ec7df6c0a4)] - **test**: add test for process.stdin.setRawMode() (Jonathan Darling) [#10037](https://github.com/nodejs/node/pull/10037)
- [[`4fce85554a`](https://github.com/nodejs/node/commit/4fce85554a)] - **test**: refactor test for net listen on fd0 (Julian Duque) [#10025](https://github.com/nodejs/node/pull/10025)
- [[`b7619e3b16`](https://github.com/nodejs/node/commit/b7619e3b16)] - **test**: update assert.equal() to assert.strictEqual() (Peter Diaz) [#10024](https://github.com/nodejs/node/pull/10024)
- [[`a6096041c6`](https://github.com/nodejs/node/commit/a6096041c6)] - **test**: use const or let and assert.strictEqual (Christopher Rokita) [#10001](https://github.com/nodejs/node/pull/10001)
- [[`cc8100a529`](https://github.com/nodejs/node/commit/cc8100a529)] - **test**: fix buffer alloc tests (levsoroka) [#9998](https://github.com/nodejs/node/pull/9998)
- [[`eb61d918b1`](https://github.com/nodejs/node/commit/eb61d918b1)] - **test**: Added more validations to setEncoding (Paul Lucas) [#9997](https://github.com/nodejs/node/pull/9997)
- [[`fe59a67b6e`](https://github.com/nodejs/node/commit/fe59a67b6e)] - **test**: use strictEqual() domain-http (cdnadmin) [#9996](https://github.com/nodejs/node/pull/9996)
- [[`ced89ede03`](https://github.com/nodejs/node/commit/ced89ede03)] - **test**: refactor test-cluster-worker-events (fmizzell) [#9994](https://github.com/nodejs/node/pull/9994)
- [[`aea0d47b77`](https://github.com/nodejs/node/commit/aea0d47b77)] - **test**: update repl tests (makenova) [#9991](https://github.com/nodejs/node/pull/9991)
- [[`a749604e11`](https://github.com/nodejs/node/commit/a749604e11)] - **test**: modernize test-fs-truncate-fd (Nigel Kibodeaux) [#9978](https://github.com/nodejs/node/pull/9978)
- [[`1addb3ba53`](https://github.com/nodejs/node/commit/1addb3ba53)] - **test**: update tls test to use const/let and common.mustCall (rgoodwin) [#9968](https://github.com/nodejs/node/pull/9968)
- [[`6d79c0cd2c`](https://github.com/nodejs/node/commit/6d79c0cd2c)] - **test**: adding strictEqual to test-buffer-indexof.js (Eric Gonzalez) [#9955](https://github.com/nodejs/node/pull/9955)
- [[`eeab546fb6`](https://github.com/nodejs/node/commit/eeab546fb6)] - **test**: strictEqual in test-beforeexit-event.js (CodeTheInternet) [#10004](https://github.com/nodejs/node/pull/10004)
- [[`b71d3fd748`](https://github.com/nodejs/node/commit/b71d3fd748)] - **test**: refactor test-child-process-double-pipe (Dan Villa) [#9930](https://github.com/nodejs/node/pull/9930)
- [[`47c925a4ac`](https://github.com/nodejs/node/commit/47c925a4ac)] - **test**: updated tls-getcipher test (Ethan Arrowood) [#9923](https://github.com/nodejs/node/pull/9923)
- [[`bc3b77f525`](https://github.com/nodejs/node/commit/bc3b77f525)] - **test**: replace equal with strictEqual in test-freelist.js (Adrian Estrada) [#9910](https://github.com/nodejs/node/pull/9910)
- [[`5afcf3ac78`](https://github.com/nodejs/node/commit/5afcf3ac78)] - **test**: updated test-stream-pipe-unpipe-stream (Raja Panidepu) [#10100](https://github.com/nodejs/node/pull/10100)
- [[`3aa51ecb6f`](https://github.com/nodejs/node/commit/3aa51ecb6f)] - **test**: refactor test-crypto-ecb (michael6) [#10029](https://github.com/nodejs/node/pull/10029)
- [[`af5c4a9958`](https://github.com/nodejs/node/commit/af5c4a9958)] - **test**: refactor test-require-exceptions (Oscar Martinez) [#9882](https://github.com/nodejs/node/pull/9882)
- [[`26d61c3dbc`](https://github.com/nodejs/node/commit/26d61c3dbc)] - **test**: refactor test-console (Matt Crummey) [#9873](https://github.com/nodejs/node/pull/9873)
- [[`5ba08d9473`](https://github.com/nodejs/node/commit/5ba08d9473)] - **test**: refactor test-crypto-certificate (Josh Mays) [#9911](https://github.com/nodejs/node/pull/9911)
- [[`81def1857d`](https://github.com/nodejs/node/commit/81def1857d)] - **test**: refactor dgram-send-multi-buffer-copy (Konstantin Likhter) [#9909](https://github.com/nodejs/node/pull/9909)
- [[`6fc75ba498`](https://github.com/nodejs/node/commit/6fc75ba498)] - **test**: refactor test-domain (Johnny Reading) [#9890](https://github.com/nodejs/node/pull/9890)
- [[`b343a584e6`](https://github.com/nodejs/node/commit/b343a584e6)] - **test**: refactor test-cli-syntax (Exlipse7) [#10057](https://github.com/nodejs/node/pull/10057)
- [[`76dda9ca37`](https://github.com/nodejs/node/commit/76dda9ca37)] - **test**: refactor test-child-process-constructor (k3kathy) [#10060](https://github.com/nodejs/node/pull/10060)
- [[`f78b81750d`](https://github.com/nodejs/node/commit/f78b81750d)] - **test**: refactor test-repl-mode.js (Cesar Hernandez) [#10061](https://github.com/nodejs/node/pull/10061)
- [[`2127798eaa`](https://github.com/nodejs/node/commit/2127798eaa)] - **test**: var to const, assert.equal to assert.strictEqual in net (Sean Villars) [#9907](https://github.com/nodejs/node/pull/9907)
- [[`cf9f6f8dbf`](https://github.com/nodejs/node/commit/cf9f6f8dbf)] - **test**: changed vars to const in test-net-better-error-messages-listen-path.js (anoff) [#9905](https://github.com/nodejs/node/pull/9905)
- [[`e9d4665b29`](https://github.com/nodejs/node/commit/e9d4665b29)] - **test**: use const instead of var in test-require-json.js (Sarah Meyer) [#9904](https://github.com/nodejs/node/pull/9904)
- [[`f4b6b9faa7`](https://github.com/nodejs/node/commit/f4b6b9faa7)] - **test**: refactor test-http-dns-error (Outsider) [#10062](https://github.com/nodejs/node/pull/10062)
- [[`7a228fe4ae`](https://github.com/nodejs/node/commit/7a228fe4ae)] - **test**: Changed assert.equal to assert.strictEqual (Daniel Pittman) [#9902](https://github.com/nodejs/node/pull/9902)
- [[`7c4b59f9b1`](https://github.com/nodejs/node/commit/7c4b59f9b1)] - **test**: refactor test-vm-syntax-error-stderr.js (Jay Brownlee) [#9900](https://github.com/nodejs/node/pull/9900)
- [[`5d28864d7f`](https://github.com/nodejs/node/commit/5d28864d7f)] - **test**: refactor test-tls-destroy-whilst-write (Chris Bystrek) [#10064](https://github.com/nodejs/node/pull/10064)
- [[`1c3227fd8c`](https://github.com/nodejs/node/commit/1c3227fd8c)] - **test**: refactor test-net-dns-custom-lookup (Kent.Fan) [#10071](https://github.com/nodejs/node/pull/10071)
- [[`9b7d7487ef`](https://github.com/nodejs/node/commit/9b7d7487ef)] - **test**: refactor test-https-truncate (davidmarkclements) [#10074](https://github.com/nodejs/node/pull/10074)
- [[`d698f9d0ac`](https://github.com/nodejs/node/commit/d698f9d0ac)] - **test**: refactor test-tls-server-verify (Hutson Betts) [#10076](https://github.com/nodejs/node/pull/10076)
- [[`7277c376c2`](https://github.com/nodejs/node/commit/7277c376c2)] - **test**: use strictEqual in test-cli-eval-event.js (Richard Karmazin) [#9964](https://github.com/nodejs/node/pull/9964)
- [[`404306fd0e`](https://github.com/nodejs/node/commit/404306fd0e)] - **test**: refactor test-crypto-padding.js (Konstantin Likhter) [#9971](https://github.com/nodejs/node/pull/9971)
- [[`821518d4e4`](https://github.com/nodejs/node/commit/821518d4e4)] - **test**: refactor test-tls-friendly-error-message.js (Adrian Estrada) [#9967](https://github.com/nodejs/node/pull/9967)
- [[`55269c106b`](https://github.com/nodejs/node/commit/55269c106b)] - **test**: refactor test-fs-append-file.js (adelmann) [#10110](https://github.com/nodejs/node/pull/10110)
- [[`bffbf6881a`](https://github.com/nodejs/node/commit/bffbf6881a)] - **test**: assert.equal -> assert.strictEqual (davidmarkclements) [#10065](https://github.com/nodejs/node/pull/10065)
- [[`f10c3210a1`](https://github.com/nodejs/node/commit/f10c3210a1)] - **test**: refactor test-dgram-exclusive-implicit-bind (Cesar Hernandez) [#10066](https://github.com/nodejs/node/pull/10066)
- [[`67b11a429b`](https://github.com/nodejs/node/commit/67b11a429b)] - **test**: assert.equal -> assert.strictEqual (davidmarkclements) [#10067](https://github.com/nodejs/node/pull/10067)
- [[`bb950a6997`](https://github.com/nodejs/node/commit/bb950a6997)] - **test**: improve test for crypto padding (Julian Duque) [#9906](https://github.com/nodejs/node/pull/9906)
- [[`7bf13f3834`](https://github.com/nodejs/node/commit/7bf13f3834)] - **test**: polish test-net-better-error-messages-listen (Hitesh Kanwathirtha) [#10087](https://github.com/nodejs/node/pull/10087)
- [[`776cfc5898`](https://github.com/nodejs/node/commit/776cfc5898)] - **test**: change var to const in test-tls-key-mismatch.js (bjdelro) [#9897](https://github.com/nodejs/node/pull/9897)
- [[`f3ef0d9c1b`](https://github.com/nodejs/node/commit/f3ef0d9c1b)] - **test**: use strictEqual in cwd-enoent (JDHarmon) [#10077](https://github.com/nodejs/node/pull/10077)
- [[`5377f72b5a`](https://github.com/nodejs/node/commit/5377f72b5a)] - **test**: refactor test-fs-read-stream-inherit.js (Jonathan Darling) [#9894](https://github.com/nodejs/node/pull/9894)
- [[`a11f9ab82e`](https://github.com/nodejs/node/commit/a11f9ab82e)] - **test**: refactor test-child-process-stdio-inherit (Wes Tyler) [#9893](https://github.com/nodejs/node/pull/9893)
- [[`80e3aabedd`](https://github.com/nodejs/node/commit/80e3aabedd)] - **test**: change var to const for require and strict equality checks (Harish Tejwani) [#9892](https://github.com/nodejs/node/pull/9892)
- [[`8097b3b1ec`](https://github.com/nodejs/node/commit/8097b3b1ec)] - **test**: Update to const and use regex for assertions (Daniel Flores) [#9891](https://github.com/nodejs/node/pull/9891)
- [[`6d5b21517a`](https://github.com/nodejs/node/commit/6d5b21517a)] - **test**: swap var->const/let and equal->strictEqual (Peter Masucci) [#9888](https://github.com/nodejs/node/pull/9888)
- [[`5446b3c6a0`](https://github.com/nodejs/node/commit/5446b3c6a0)] - **test**: replace equal with strictEqual in crypto (Julian Duque) [#9886](https://github.com/nodejs/node/pull/9886)
- [[`0c66f68fe2`](https://github.com/nodejs/node/commit/0c66f68fe2)] - **test**: replace equal with strictEqual (Julian Duque) [#9879](https://github.com/nodejs/node/pull/9879)
- [[`c5bfe90900`](https://github.com/nodejs/node/commit/c5bfe90900)] - **test**: var to const/let in test-tls-set-ciphers (rajatk) [#9877](https://github.com/nodejs/node/pull/9877)
- [[`7fa3b6fad3`](https://github.com/nodejs/node/commit/7fa3b6fad3)] - **test**: refactor test-tls-timeout-server-2 (Devon Rifkin) [#9876](https://github.com/nodejs/node/pull/9876)
- [[`e6747048e7`](https://github.com/nodejs/node/commit/e6747048e7)] - **test**: Updating vars to const and tsl server test (Matt Webb) [#9874](https://github.com/nodejs/node/pull/9874)
- [[`393cb97cbb`](https://github.com/nodejs/node/commit/393cb97cbb)] - **test**: refactor test-crypto-hash-stream-pipe (Matt Wilson) [#10055](https://github.com/nodejs/node/pull/10055)
- [[`b48d83190a`](https://github.com/nodejs/node/commit/b48d83190a)] - **test**: crypto-hash-stream-pipe use strict equal (Mitchell Stoutin) [#9935](https://github.com/nodejs/node/pull/9935)
- [[`91b942ec8a`](https://github.com/nodejs/node/commit/91b942ec8a)] - **test**: refactor child-process-spawn-error (Johnny Reading) [#9951](https://github.com/nodejs/node/pull/9951)
- [[`a2e88f6e0c`](https://github.com/nodejs/node/commit/a2e88f6e0c)] - **test**: refactor test-child-process-spawn-error (stokingerl) [#9937](https://github.com/nodejs/node/pull/9937)
- [[`52cc1bb08e`](https://github.com/nodejs/node/commit/52cc1bb08e)] - **test**: refactor test-vm-static-this.js (David Bradford) [#9887](https://github.com/nodejs/node/pull/9887)
- [[`895472474b`](https://github.com/nodejs/node/commit/895472474b)] - **test**: refactor test-crypto-cipheriv-decipheriv (Aileen) [#10018](https://github.com/nodejs/node/pull/10018)
- [[`c4277d9b5e`](https://github.com/nodejs/node/commit/c4277d9b5e)] - **test**: refactor test for crypto cipher/decipher iv (Julian Duque) [#9943](https://github.com/nodejs/node/pull/9943)
- [[`346ea432b4`](https://github.com/nodejs/node/commit/346ea432b4)] - **test**: refactor test-cluster-setup-master-argv (Oscar Martinez) [#9960](https://github.com/nodejs/node/pull/9960)
- [[`5009de4a53`](https://github.com/nodejs/node/commit/5009de4a53)] - **test**: refactor test-cluster-setup-master-argv (Christine Hong) [#9993](https://github.com/nodejs/node/pull/9993)
- [[`e75f81495d`](https://github.com/nodejs/node/commit/e75f81495d)] - **test**: refactor test-fs-append-file-sync (Chris Bystrek) [#10056](https://github.com/nodejs/node/pull/10056)
- [[`61225eac1a`](https://github.com/nodejs/node/commit/61225eac1a)] - **test**: refactor test-fs-append-file-sync (Ian White) [#9977](https://github.com/nodejs/node/pull/9977)
- [[`f093760166`](https://github.com/nodejs/node/commit/f093760166)] - **test**: use assert.strictEqual in test-crypto-ecb (Daniel Pittman) [#9980](https://github.com/nodejs/node/pull/9980)
- [[`1bbaace480`](https://github.com/nodejs/node/commit/1bbaace480)] - **test**: refactor test-fs-write-file (adelmann) [#10030](https://github.com/nodejs/node/pull/10030)
- [[`ac5bf86fd1`](https://github.com/nodejs/node/commit/ac5bf86fd1)] - **test**: refactor test/parallel/test-fs-write-file.js (Kyle Carter) [#9992](https://github.com/nodejs/node/pull/9992)
- [[`bf71b63444`](https://github.com/nodejs/node/commit/bf71b63444)] - **test**: update to const iin cluster test (Greg Valdez) [#10007](https://github.com/nodejs/node/pull/10007)
- [[`a36389ee08`](https://github.com/nodejs/node/commit/a36389ee08)] - **test**: use assert.strictEqual() cluster test (Bidur Adhikari) [#10042](https://github.com/nodejs/node/pull/10042)
- [[`effa15ead9`](https://github.com/nodejs/node/commit/effa15ead9)] - **test**: use const in test-crypto-pbkdf2 (Greg Valdez) [#9974](https://github.com/nodejs/node/pull/9974)
- [[`59f643096a`](https://github.com/nodejs/node/commit/59f643096a)] - **test**: improve test for crypto pbkdf2 (joyeecheung) [#9883](https://github.com/nodejs/node/pull/9883)
- [[`503167ba36`](https://github.com/nodejs/node/commit/503167ba36)] - **test**: var -> let/const, .equal -> .strictEqual (shiya) [#9913](https://github.com/nodejs/node/pull/9913)
- [[`f1d0bf4757`](https://github.com/nodejs/node/commit/f1d0bf4757)] - **test**: increase coverage for timers (lrlna) [#10068](https://github.com/nodejs/node/pull/10068)
- [[`e14a597d35`](https://github.com/nodejs/node/commit/e14a597d35)] - **test**: change equal to strictEqual (Kevin Zurawel) [#9872](https://github.com/nodejs/node/pull/9872)
- [[`076c2c2c54`](https://github.com/nodejs/node/commit/076c2c2c54)] - **test**: test for http.request() invalid method error (Ashton Kinslow) [#10080](https://github.com/nodejs/node/pull/10080)
- [[`f38cb9bbe2`](https://github.com/nodejs/node/commit/f38cb9bbe2)] - **test**: update net-local-address-port (scalkpdev) [#9885](https://github.com/nodejs/node/pull/9885)
- [[`c9ac2b366f`](https://github.com/nodejs/node/commit/c9ac2b366f)] - **test**: refactor test-tls-ecdh (Adriana Rios) [#9878](https://github.com/nodejs/node/pull/9878)
- [[`5186604fa9`](https://github.com/nodejs/node/commit/5186604fa9)] - **test**: refactor test-vm-debug-context (makenova) [#9875](https://github.com/nodejs/node/pull/9875)
- [[`fcc511a57b`](https://github.com/nodejs/node/commit/fcc511a57b)] - **test**: use strictEqual in test-zlib-truncated (ben_cripps) [#9858](https://github.com/nodejs/node/pull/9858)
- [[`e55a5936cd`](https://github.com/nodejs/node/commit/e55a5936cd)] - **test**: use strictEqual in test-debugger-client.js (ben_cripps) [#9857](https://github.com/nodejs/node/pull/9857)
- [[`a773843c01`](https://github.com/nodejs/node/commit/a773843c01)] - **test**: refactor test-debug-args (Rich Trott) [#9833](https://github.com/nodejs/node/pull/9833)
- [[`015812e2b8`](https://github.com/nodejs/node/commit/015812e2b8)] - **test**: refactor test-fs-non-number-arguments-throw (Michaël Zasso) [#9844](https://github.com/nodejs/node/pull/9844)
- [[`74919eb5ef`](https://github.com/nodejs/node/commit/74919eb5ef)] - **test**: replace assert.equal with assert.strictEqual (brad-decker) [#9842](https://github.com/nodejs/node/pull/9842)
- [[`0605c74538`](https://github.com/nodejs/node/commit/0605c74538)] - **test**: refactor test-crypto-timing-safe-equal (Michaël Zasso) [#9843](https://github.com/nodejs/node/pull/9843)
- [[`b93c3d89f5`](https://github.com/nodejs/node/commit/b93c3d89f5)] - **test**: add toASCII and toUnicode punycode tests (Claudio Rodriguez) [#9741](https://github.com/nodejs/node/pull/9741)
- [[`51c8fe0c66`](https://github.com/nodejs/node/commit/51c8fe0c66)] - **test**: refactor test-util-inspect (Rich Trott) [#9804](https://github.com/nodejs/node/pull/9804)
- [[`e2e51c52c9`](https://github.com/nodejs/node/commit/e2e51c52c9)] - **test**: refactor test-preload (Rich Trott) [#9803](https://github.com/nodejs/node/pull/9803)
- [[`8c6b127c93`](https://github.com/nodejs/node/commit/8c6b127c93)] - **test**: refine test-http-status-reason-invalid-chars (Rich Trott) [#9802](https://github.com/nodejs/node/pull/9802)
- [[`ca0e577673`](https://github.com/nodejs/node/commit/ca0e577673)] - **test**: refactor test-crypto-binary-default (Michaël Zasso) [#9810](https://github.com/nodejs/node/pull/9810)
- [[`3219586512`](https://github.com/nodejs/node/commit/3219586512)] - **test**: refactor test-net-pingpong (Michaël Zasso) [#9812](https://github.com/nodejs/node/pull/9812)
- [[`ca461bf561`](https://github.com/nodejs/node/commit/ca461bf561)] - **test**: refactor and fix test-dns (Michaël Zasso) [#9811](https://github.com/nodejs/node/pull/9811)
- [[`aebc8dfa57`](https://github.com/nodejs/node/commit/aebc8dfa57)] - **test**: fix flaky test-cluster-dgram-2 (Rich Trott) [#9791](https://github.com/nodejs/node/pull/9791)
- [[`5542a72558`](https://github.com/nodejs/node/commit/5542a72558)] - **test**: fix test-tls-connect-address-family (mkamakura) [#9573](https://github.com/nodejs/node/pull/9573)
- [[`6105c91f89`](https://github.com/nodejs/node/commit/6105c91f89)] - **test**: fix test-http-status-reason-invalid-chars (Yosuke Saito) [#9572](https://github.com/nodejs/node/pull/9572)
- [[`d1f5e99a2a`](https://github.com/nodejs/node/commit/d1f5e99a2a)] - **test**: refactor test-child-process-exec-error (Rich Trott) [#9780](https://github.com/nodejs/node/pull/9780)
- [[`0772984cb3`](https://github.com/nodejs/node/commit/0772984cb3)] - **test**: refactor common.js (Rich Trott) [#9732](https://github.com/nodejs/node/pull/9732)
- [[`605c84f8d6`](https://github.com/nodejs/node/commit/605c84f8d6)] - **test**: exclude no_interleaved_stdio test for AIX (Michael Dawson) [#9772](https://github.com/nodejs/node/pull/9772)
- [[`75bebbf3a0`](https://github.com/nodejs/node/commit/75bebbf3a0)] - **test**: fix flaky test-dgram-empty-packet & friends (Rich Trott) [#9724](https://github.com/nodejs/node/pull/9724)
- [[`1296a689b6`](https://github.com/nodejs/node/commit/1296a689b6)] - **test**: fix flaky test-inspector (Rich Trott) [#9727](https://github.com/nodejs/node/pull/9727)
- [[`c5e806c894`](https://github.com/nodejs/node/commit/c5e806c894)] - **test**: refactor test-tls-hello-parser-failure (Rich Trott) [#9715](https://github.com/nodejs/node/pull/9715)
- [[`9e4ce6fc8e`](https://github.com/nodejs/node/commit/9e4ce6fc8e)] - **test**: refactor test-async-wrap-\* (Rich Trott) [#9663](https://github.com/nodejs/node/pull/9663)
- [[`31304144cd`](https://github.com/nodejs/node/commit/31304144cd)] - **test**: Use strictEqual in test-tls-writewrap-leak (Aaron Petcoff) [#9666](https://github.com/nodejs/node/pull/9666)
- [[`333e5d1c49`](https://github.com/nodejs/node/commit/333e5d1c49)] - **test**: run tests even if os.cpus() fails (Bethany Griggs) [#9616](https://github.com/nodejs/node/pull/9616)
- [[`1bb626610a`](https://github.com/nodejs/node/commit/1bb626610a)] - **test**: use setImmediate() in test of stream2 (masashi.g) [#9583](https://github.com/nodejs/node/pull/9583)
- [[`70691e3c53`](https://github.com/nodejs/node/commit/70691e3c53)] - **test**: add test case of PassThrough (Yoshiya Hinosawa) [#9581](https://github.com/nodejs/node/pull/9581)
- [[`7e031170a6`](https://github.com/nodejs/node/commit/7e031170a6)] - **test**: check that `process.execPath` is a realpath (Anna Henningsen) [#9229](https://github.com/nodejs/node/pull/9229)
- [[`14fead0299`](https://github.com/nodejs/node/commit/14fead0299)] - **test**: add test for broken child process stdio (cjihrig) [#9528](https://github.com/nodejs/node/pull/9528)
- [[`2c5e6afd97`](https://github.com/nodejs/node/commit/2c5e6afd97)] - **test**: ensure nextTick is not scheduled in exit (Jeremiah Senkpiel) [#9555](https://github.com/nodejs/node/pull/9555)
- [[`f7a2f75fa9`](https://github.com/nodejs/node/commit/f7a2f75fa9)] - **test**: increase coverage of process.emitWarning (Jeremiah Senkpiel) [#9556](https://github.com/nodejs/node/pull/9556)
- [[`7bd9ecdb14`](https://github.com/nodejs/node/commit/7bd9ecdb14)] - **test**: refactor test-zlib.js (Rich Trott) [#9544](https://github.com/nodejs/node/pull/9544)
- [[`8a94c69c79`](https://github.com/nodejs/node/commit/8a94c69c79)] - **test**: change from setTimeout to setImmediate (MURAKAMI Masahiko) [#9578](https://github.com/nodejs/node/pull/9578)
- [[`92f8073879`](https://github.com/nodejs/node/commit/92f8073879)] - **test**: improve test-stream2-objects.js (Yoshiya Hinosawa) [#9565](https://github.com/nodejs/node/pull/9565)
- [[`64d7ea9ce4`](https://github.com/nodejs/node/commit/64d7ea9ce4)] - **test**: refactor test-next-tick-error-spin (Rich Trott) [#9537](https://github.com/nodejs/node/pull/9537)
- [[`d8eb4c2595`](https://github.com/nodejs/node/commit/d8eb4c2595)] - **test**: refactor test-tls-inception (Rich Trott) [#9536](https://github.com/nodejs/node/pull/9536)
- [[`0db54ab98e`](https://github.com/nodejs/node/commit/0db54ab98e)] - **test**: refactor inspector-helper.js (Rich Trott) [#9499](https://github.com/nodejs/node/pull/9499)
- [[`31a3328269`](https://github.com/nodejs/node/commit/31a3328269)] - **test**: refactor make-callback-recurse test (Rich Trott) [#9498](https://github.com/nodejs/node/pull/9498)
- [[`9808985689`](https://github.com/nodejs/node/commit/9808985689)] - **test**: move timer-dependent test to sequential (Rich Trott) [#9487](https://github.com/nodejs/node/pull/9487)
- [[`e97c610850`](https://github.com/nodejs/node/commit/e97c610850)] - **test**: fix helper-debugger-repl.js (Rich Trott) [#9486](https://github.com/nodejs/node/pull/9486)
- [[`13b16881ef`](https://github.com/nodejs/node/commit/13b16881ef)] - **test,url**: improve escaping in url.parse (joyeecheung) [#10083](https://github.com/nodejs/node/pull/10083)
- [[`8bb66cd920`](https://github.com/nodejs/node/commit/8bb66cd920)] - **timers**: use consistent checks for canceled timers (Jeremiah Senkpiel) [#9685](https://github.com/nodejs/node/pull/9685)
- [[`e355604aa5`](https://github.com/nodejs/node/commit/e355604aa5)] - **tools**: forbid template literals in assert.throws (Michaël Zasso) [#10301](https://github.com/nodejs/node/pull/10301)
- [[`9c85d0f396`](https://github.com/nodejs/node/commit/9c85d0f396)] - **tools**: add ESLint rule for assert.throws arguments (Michaël Zasso) [#10089](https://github.com/nodejs/node/pull/10089)
- [[`a5d27f3515`](https://github.com/nodejs/node/commit/a5d27f3515)] - **tools**: enable final newline in .editorconfig (Roman Reiss) [#9410](https://github.com/nodejs/node/pull/9410)
- [[`e94b72e41e`](https://github.com/nodejs/node/commit/e94b72e41e)] - **tools**: remove unneeded escaping in generate.js (Rich Trott) [#9781](https://github.com/nodejs/node/pull/9781)
- [[`f05f0fe74e`](https://github.com/nodejs/node/commit/f05f0fe74e)] - **tools**: disallow trailing whitespace for markdown (Sam Roberts) [#9676](https://github.com/nodejs/node/pull/9676)
- [[`0256b7b057`](https://github.com/nodejs/node/commit/0256b7b057)] - **tools**: use better regexp for manpage references (Anna Henningsen) [#9632](https://github.com/nodejs/node/pull/9632)
- [[`232026d8b9`](https://github.com/nodejs/node/commit/232026d8b9)] - **tools**: improve docopen target in Makefile (Sakthipriyan Vairamani (thefourtheye)) [#9436](https://github.com/nodejs/node/pull/9436)
- [[`79e0577702`](https://github.com/nodejs/node/commit/79e0577702)] - **tools**: make run-valgrind.py useful (Ben Noordhuis) [#9520](https://github.com/nodejs/node/pull/9520)
- [[`8a8646c0b2`](https://github.com/nodejs/node/commit/8a8646c0b2)] - **tools**: fix run-valgrind.py script (Ben Noordhuis) [#9520](https://github.com/nodejs/node/pull/9520)
- [[`5401b04648`](https://github.com/nodejs/node/commit/5401b04648)] - **tools**: copy run-valgrind.py to tools/ (Ben Noordhuis) [#9520](https://github.com/nodejs/node/pull/9520)
- [[`12fe071abf`](https://github.com/nodejs/node/commit/12fe071abf)] - **util**: move the case 'latin1' (Jackson Tian) [#9646](https://github.com/nodejs/node/pull/9646)

Windows 32-bit Installer: https://nodejs.org/dist/v6.9.3/node-v6.9.3-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v6.9.3/node-v6.9.3-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v6.9.3/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v6.9.3/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v6.9.3/node-v6.9.3.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v6.9.3/node-v6.9.3-darwin-x64.tar.gz \
Linux 32-bit Binary: https://nodejs.org/dist/v6.9.3/node-v6.9.3-linux-x86.tar.xz \
Linux 64-bit Binary: https://nodejs.org/dist/v6.9.3/node-v6.9.3-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v6.9.3/node-v6.9.3-linux-ppc64le.tar.xz \
Linux PPC BE 64-bit Binary: https://nodejs.org/dist/v6.9.3/node-v6.9.3-linux-ppc64.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v6.9.3/node-v6.9.3-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v6.9.3/node-v6.9.3-aix-ppc64.tar.gz \
SmartOS 32-bit Binary: https://nodejs.org/dist/v6.9.3/node-v6.9.3-sunos-x86.tar.xz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v6.9.3/node-v6.9.3-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v6.9.3/node-v6.9.3-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v6.9.3/node-v6.9.3-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v6.9.3/node-v6.9.3-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v6.9.3/node-v6.9.3.tar.gz \
Other release files: https://nodejs.org/dist/v6.9.3/ \
Documentation: https://nodejs.org/docs/v6.9.3/api/

### SHASUMS

#### The Original SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

c55e35ccf71f868d6b7075f20c14b9d0c2c8a3ca98db823b0c5a83ba09e5a984  node-v6.9.3-aix-ppc64.tar.gz
ae79277f15b8b2f173b97e44e2d4c65a8de4254c2c7da0dcd754b4e39658a779  node-v6.9.3-darwin-x64.tar.gz
239c196ab56ee875ad300159cfc3f5bd0a87ce457961046ce9518868a983d618  node-v6.9.3-darwin-x64.tar.xz
43764ddd3829cd3ad22b1e6870fd7d058e2f9a2cf3fd3ea21a25772a18fe0a88  node-v6.9.3-headers.tar.gz
d0cd948b6d585f64e6ef9ba61c1ee6b3c703670f0bad04e613ad6914b011151f  node-v6.9.3-headers.tar.xz
f9eaf8dbd926770795ec5a5670a824bab25ec5b19c9803584c342777daef272c  node-v6.9.3-linux-arm64.tar.gz
fc461a64ef0d2f6267436e95f966df8673276a6344c9389d41cfa06da07ab878  node-v6.9.3-linux-arm64.tar.xz
5247665cac023be266cdff06abff0f784f0b5d737edff7dcabb12ceb115cdb36  node-v6.9.3-linux-armv6l.tar.gz
27941dcafa8d9cb0529f1b88831fc40837118a6471410cfca77fa42c5d57415e  node-v6.9.3-linux-armv6l.tar.xz
01793465bd7ddd6cdb798799c5e4ee107fd6dc77e013bfb602d9f677395d9465  node-v6.9.3-linux-armv7l.tar.gz
30fa10c799db76732998913a2195f45041f5c2417800740c43cee9b7dcaf7b33  node-v6.9.3-linux-armv7l.tar.xz
15fceb4cac03ea4cfa54e202d39ac260aec21575057029a0b5b21463ee88c683  node-v6.9.3-linux-ppc64le.tar.gz
0dc6cf753cc25f14f2f310d22d40dbc8c273dce38a9776b430bac319cb27ac6b  node-v6.9.3-linux-ppc64le.tar.xz
3ccf0fa6543714e1a745648fea9ab86e0566599e3618d578e9009835bfcc79a3  node-v6.9.3-linux-ppc64.tar.gz
ce25234a057f1c0a744bfcfafd2f6d0fc78bc554451c422ec3220c7f1e755d7d  node-v6.9.3-linux-ppc64.tar.xz
8d387365cd3a7c56c5f603561458cb303351803cea3c409b7ff14a2f88bebf40  node-v6.9.3-linux-s390x.tar.gz
c3ea05fd0a7e2216d699da8fda2223538f3b5ecc88fbdb5cdcefe10e4439bf19  node-v6.9.3-linux-s390x.tar.xz
89fbe01f6ccba0295a121ca32e3f0da772319406a8dba5f63e4797a4df6cb5ad  node-v6.9.3-linux-x64.tar.gz
7e60f6f54a836ab8346d0dc60f8c35522a839872084e76acba892920502392d2  node-v6.9.3-linux-x64.tar.xz
5e5e95f47a71eb3316ff4aa520f5174f622196eb591d11a0948314dc211d0e0e  node-v6.9.3-linux-x86.tar.gz
514ff425fd85179c8c065eb7c44c37416d5a80b2e6b87d3a1dcb496616cbc42e  node-v6.9.3-linux-x86.tar.xz
b2898e8261a28df40d640672ee4fb61b4e46b4b87d601c863d2003ff97b5230b  node-v6.9.3.pkg
a29cab343c4695c6609a80503b9a1fcab12952c1632f821f6d7a5851dadc6549  node-v6.9.3-sunos-x86.tar.gz
47e38e6ade9c300f003b28873163e193f76b5137dc9ef6fbf31b6ff7fd72fcee  node-v6.9.3-sunos-x86.tar.xz
5abdc3b77e011d664e13d74cab130680a8652b5cd23a63d2a17496d91399d5b5  node-v6.9.3.tar.gz
98ea92695e9df27c8a2719406e0be51967f06c5ed4e0f6ee5f1e8460814d2723  node-v6.9.3.tar.xz
a58c42b95d5359de9c72c01e1c7abf772a294cafb2ac7428011f8b4c99efc868  node-v6.9.3-win-x64.7z
bda76db75bed655b5b8f01022f33ca7e61b8be175b871dcc218bdf84f6403a1f  node-v6.9.3-win-x64.zip
76c13d2814c5bc2dd51cb6e5e49f6da8986a01d6f606e5aae2b024313a01b62b  node-v6.9.3-win-x86.7z
b971e08b0dbe4285f743a2b612065ef187273731f59e10a012143c80f0c7861c  node-v6.9.3-win-x86.zip
791185c3a771350cd1ea62dac0b6ad0958eb7cf1f1f5e67e5c2ae68ca6db1e32  node-v6.9.3-x64.msi
5ead92cfd27d501ca60889cee1fd3c5bfe3b7ddf2e07a7f927cfeae52cfcf94c  node-v6.9.3-x86.msi
47fef39ededc0dcb348689676de82973ea42715367b84f841167c01cb6398725  win-x64/node.exe
4b1586514603e3d4f78374e05b0617681ae7729a8ecef230b57617714cfafab6  win-x64/node.lib
66968b714afe766c4ff7cbee9bcc77b330649a1e6861c9b6b61d35db81d41e5a  win-x64/node_pdb.7z
4363c4f40461fc08b53076984c6910147ebcbbf93ba43f243eb0d51311fa7d0f  win-x64/node_pdb.zip
9ffd3eec57eebb87cb5b5377dd2213d6c00e40bc8ae4cd5a74582a3cfc037556  win-x86/node.exe
1b044b08e8e2a0f78781c85cc5facdabd04c0a8e73fd991ae3e5a3053784c14f  win-x86/node.lib
921953d2ffb57fa6aa53c001e4bcfec2494f7a68400cd3985c8eaa23786247eb  win-x86/node_pdb.7z
ff1e5f746eb731f81f80b75e41c27b104888361e0187d05195a242eaf9358d52  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQEcBAEBCAAGBQJYbDoeAAoJEJM7AfQLXKlG2QoH/jN2p9Np5CGXlmaJn7hZJEf+
3Z4MIE5uLeoADWsb+QKYQiS4OBQZDoKyivXzctv4EK0q8VlLt4QrSDUdziF3Uh4A
hsyqoM4SjRHBsEJVFTcwuBbZUKOtGSvjkJJ97pFki70kd+R9xXCqHzyPmTVU3hLH
awlPLIQENI1UxbjMXiOz1TOquP0J6PVUONd3Qe3qJ3oLG8e4aPDha75SFE84vvqa
VnsFKMi45ngzt0nBrxW+ASpahPdEGgeJ1IIXYXG9sdCZBR+J0MYYL/r7HGmx6HlS
jwYtJHsz/zd76JKpRnekk1m8VgEA9o7wRyUSI7bepRw2Q67wv3rFGG7IQHlccLU=
=XtXG
-----END PGP SIGNATURE-----
```

#### The Current SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

ced91ebd70173714117e3d4787036b7047c5000ab28896ac128fbd3607dd99f1  node-v6.9.3-aix-ppc64.tar.gz
a313821570ac5ba5ce66e13a5ba3064bff07e076393d66fcda16fe7e1a2fbd46  node-v6.9.3-darwin-x64.tar.gz
0d9884db10675f216627bf2564b07bcf69c76014f9371bea4d8da8a6af71f137  node-v6.9.3-darwin-x64.tar.xz
abd4571c984f9a75fe707e0e494112e2e70a4eebb6f9794c5ae6fef12620e847  node-v6.9.3-headers.tar.gz
3a549c28f606e281de93a05ab6e71c22854d0bc446a31171f79f0ff099e804bd  node-v6.9.3-headers.tar.xz
2b0aec9caf1afb5b4cb417dafb2701a2a104e669a0dcb2005497c7f636211ed8  node-v6.9.3-linux-arm64.tar.gz
a45a79d03b48704e140a640a45130aa7b84be9e57f070f9aade8448ce07d18dc  node-v6.9.3-linux-arm64.tar.xz
a9d165c32a0d9c55ef0dcc0e043951af4e1d2283ebdc18defcd1715d0d3cdb4a  node-v6.9.3-linux-armv6l.tar.gz
9b83ae8015902ab296782b1ac4083f742093e814605ba7e71c642839fe4d434f  node-v6.9.3-linux-armv6l.tar.xz
ba7cec96cf4893ac5eaf2aaf8768cf8e5d7b69ecf25a48e18e8832183fc39e9a  node-v6.9.3-linux-armv7l.tar.gz
d86187fbf2ee4875438cc8fc53506db998398f165227ec162160a6fedef957cf  node-v6.9.3-linux-armv7l.tar.xz
df59c6bd0b2de004fcec8101f2f671107f63a3e29d436eb02c922348a423fcc2  node-v6.9.3-linux-ppc64le.tar.gz
a4d708f07a44a534c85ddc3a35d0557ce9412af3ad2a16373a871cea104942fd  node-v6.9.3-linux-ppc64le.tar.xz
979f1bec0deac73fdaf45c07f121f9ab7fad0f28c1543710851c878c4a14ac12  node-v6.9.3-linux-ppc64.tar.gz
2227f81699effb0ef9582582e5ad40172a1378aa1b1a0ece0a7a67083f94491d  node-v6.9.3-linux-ppc64.tar.xz
3acc43559272d5e2d17cc2eab797cc8646e16daa1655dbbceecccc18bc6cbf5f  node-v6.9.3-linux-s390x.tar.gz
09f428e4598be8d289a4c49e4dad1491335a15527fb6277cd542034b8772fa4d  node-v6.9.3-linux-s390x.tar.xz
5957fd9b65c346f0d0afb1adc8bde98fa04bf613ee51ef9570d287bda73314a2  node-v6.9.3-linux-x64.tar.gz
f072719f5810a0fd8ba1d882eb19e546d54bf675b393b8478ff89b304669876b  node-v6.9.3-linux-x64.tar.xz
fd25af7ac2728d2321417dfaa408c2f29a8fc1a230f9dbda1a43a2345e6be338  node-v6.9.3-linux-x86.tar.gz
5fd85f7e99bff5e3fa23293bb379ff8f0063c6dbf9bc6caa7bcb160edd38d76b  node-v6.9.3-linux-x86.tar.xz
7616af707e37081c75a3c80f43ce7983514bec051186f61e0349ed125ecf6e2a  node-v6.9.3.pkg
229b336d9be8ce86a0a3d96ef11333df2e3fd2b57cfc4e54bb2355e0b6215a97  node-v6.9.3-sunos-x64.tar.gz
edfb132cd42a51524a7a5b9970db65ba188667f74cefbadb51a3a44f1263c46f  node-v6.9.3-sunos-x64.tar.xz
36cbeeed50d4539cc650a7373c79f553d5ef44038e6ffe7d296553d1a12b0443  node-v6.9.3-sunos-x86.tar.gz
a97a83f05234cf7a27bbaa6c680795a163496e07f7f801b8773c6f660fe1f51d  node-v6.9.3-sunos-x86.tar.xz
74b3a5c5419c674598376eefd435f472e9a36a3c27029b5fa64ba3037ed08fc9  node-v6.9.3.tar.gz
d1ece6ef97837367f36c3259a0fb48e0e0574682e5b8370e6b4d809ba02d1e97  node-v6.9.3.tar.xz
abc083584830e01042655c18c9fd178177835da72ace54489163a65613091068  node-v6.9.3-win-x64.7z
a6670c5790052c626e72bc5a0ef983236a54a71de784076784e8565688fcf7fc  node-v6.9.3-win-x64.zip
3613a8d0aa30450b8803d06badd42eaecffb37978ff691e557657a93c87cccf0  node-v6.9.3-win-x86.7z
338c19fbb7d7655fea1ccb65b85299269420ffa1f1b10cc0362d10ce6b7e818e  node-v6.9.3-win-x86.zip
7c4829e708ae9491b8af76c45ac5ab0eeeb04f084527d049b673a054a084bf11  node-v6.9.3-x64.msi
b94eb09987990fc4b5dfa6df16106d23ad8089b26a9b87c0576de98aacbcfb69  node-v6.9.3-x86.msi
2551c19ee4e6c03ce55dd916ee4dfcbcfec3cd71300164abf4be8a06b77c2ea4  win-x64/node.exe
c8ec2fae6968ff9ebf056fa95a42f7edc25146a46c1f70429d76d6acb4474a0f  win-x64/node.lib
18d84d29c7472bdc3974490e7dfcc7d6a4fd50a9d7a126351a13fa644d453d8f  win-x64/node_pdb.7z
83503d437d7869575bb69c02203b29871a4a51301bf8118aa391ed732a56b114  win-x64/node_pdb.zip
4d803cde8e701c67c434b05232f2686fa4dd1317a19dacc1e2f76d6219b85feb  win-x86/node.exe
54b43a3f880a175d75faead2a20a4a56103ba29ab6d325092d835cc480dc5b63  win-x86/node.lib
a672dcf5206896ae1925273173c5ef7d4b7aec39dcb98929170b0e68a03aeafa  win-x86/node_pdb.7z
2db6c76614c56b4b43ad4ffc47b9bdab5e6fc548358eeff877e193e54c751ee9  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQEcBAEBCAAGBQJYboFHAAoJEJM7AfQLXKlGm60H/3PiC12GfzfGxAQZLWBTDuaG
LJhc7xY5E3xCtTo9DmQtk6AhBxtSJf0hwUdw+g0n2MEmim8OXkm/x8Au9pcvCpzP
rJ79NBedxvri6fQ9HngRH8oODKMCVgG5qafL0c1N1I53wNK/3dmD/9oh1Nw4ZHvd
gDkH1ZxMEwCKLpXWzT0xEjVkKwNyJZ/yGylVwAkKTv0KPUTGYVjO3SvJcDw/r3w8
sKcSdHi7d81qgR2wjEj8V2m79tGPDmnrg8LBZsNbePhmHsoFtggJyWataco+gjmm
wfCnZ0MpJJuqaIBPswPYiyeTBau6r4h6coIpgGSRtvEqliD03+IQvS9FTFomuCU=
=EJXd
-----END PGP SIGNATURE-----
```
