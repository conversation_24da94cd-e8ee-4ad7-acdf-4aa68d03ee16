---
date: '2023-04-13T00:46:01.762Z'
category: release
title: Node v18.16.0 (LTS)
layout: blog-post
author: <PERSON>
---

### Notable changes

#### Add initial support for single executable applications

Compile a JavaScript file into a single executable application:

```console
$ echo 'console.log(`Hello, ${process.argv[2]}!`);' > hello.js

$ cp $(command -v node) hello

# On systems other than macOS:
$ npx postject hello NODE_JS_CODE hello.js \
    --sentinel-fuse NODE_JS_FUSE_fce680ab2cc467b6e072b8b5df1996b2

# On macOS:
$ npx postject hello NODE_JS_CODE hello.js \
    --sentinel-fuse NODE_JS_FUSE_fce680ab2cc467b6e072b8b5df1996b2 \
    --macho-segment-name NODE_JS

$ ./hello world
Hello, world!
```

Contributed by <PERSON><PERSON> in <https://github.com/nodejs/node/pull/45038>

#### Replace url parser with Ada

Node.js gets a new URL parser called <PERSON> that is compliant with the WHATWG URL Specification and provides more than 100% performance improvement to the existing implementation.

Contributed by Yagiz Nizipli in <https://github.com/nodejs/node/pull/46410>

#### Other notable changes

- **buffer**:
  - **(SEMVER-MINOR)** add Buffer.copyBytesFrom(...) (<PERSON> M Snell) [#46500](https://github.com/nodejs/node/pull/46500)
- **doc**:
  - add marco-ippolito to collaborators (Marco Ippolito) [#46816](https://github.com/nodejs/node/pull/46816)
  - add debadree25 to collaborators (Debadree Chatterjee) [#46716](https://github.com/nodejs/node/pull/46716)
  - add deokjinkim to collaborators (Deokjin Kim) [#46444](https://github.com/nodejs/node/pull/46444)
- **events**:
  - **(SEMVER-MINOR)** add listener argument to listenerCount (Paolo Insogna) [#46523](https://github.com/nodejs/node/pull/46523)
- **lib**:
  - **(SEMVER-MINOR)** add AsyncLocalStorage.bind() and .snapshot() (flakey5) [#46387](https://github.com/nodejs/node/pull/46387)
  - **(SEMVER-MINOR)** add aborted() utility function (Debadree Chatterjee) [#46494](https://github.com/nodejs/node/pull/46494)
- **src**:
  - **(SEMVER-MINOR)** allow optional Isolate termination in node::Stop() (Shelley Vohr) [#46583](https://github.com/nodejs/node/pull/46583)
  - **(SEMVER-MINOR)** allow embedder control of code generation policy (Shelley Vohr) [#46368](https://github.com/nodejs/node/pull/46368)
- **stream**:
  - **(SEMVER-MINOR)** add abort signal for ReadableStream and WritableStream (Debadree Chatterjee) [#46273](https://github.com/nodejs/node/pull/46273)
- **tls**:
  - **(SEMVER-MINOR)** support automatic DHE (Tobias Nießen) [#46978](https://github.com/nodejs/node/pull/46978)
- **url**:
  - **(SEMVER-MINOR)** implement URLSearchParams size getter (James M Snell) [#46308](https://github.com/nodejs/node/pull/46308)
- **worker**:
  - **(SEMVER-MINOR)** add support for worker name in inspector and trace_events (Debadree Chatterjee) [#46832](https://github.com/nodejs/node/pull/46832)

### Commits

- \[[`c742493b61`](https://github.com/nodejs/node/commit/c742493b61)] - **assert**: fix exception message for assert(0) on try catch block (hidecology) [#46760](https://github.com/nodejs/node/pull/46760)
- \[[`0ddf73ae7c`](https://github.com/nodejs/node/commit/0ddf73ae7c)] - **assert**: remove deprecated getFunction() usage (Ruben Bridgewater) [#46661](https://github.com/nodejs/node/pull/46661)
- \[[`97ad72f19f`](https://github.com/nodejs/node/commit/97ad72f19f)] - **async_hooks**: add async local storage propagation benchmarks (Chengzhong Wu) [#46414](https://github.com/nodejs/node/pull/46414)
- \[[`b1bde69574`](https://github.com/nodejs/node/commit/b1bde69574)] - **async_hooks**: remove experimental onPropagate option (James M Snell) [#46386](https://github.com/nodejs/node/pull/46386)
- \[[`b5db3b579a`](https://github.com/nodejs/node/commit/b5db3b579a)] - **benchmark**: add a benchmark for URLSearchParams creation and toString() (Debadree Chatterjee) [#46810](https://github.com/nodejs/node/pull/46810)
- \[[`ff94f9ffbe`](https://github.com/nodejs/node/commit/ff94f9ffbe)] - **benchmark**: replace table in docs with description of file tree structure (Theodor Steiner) [#46991](https://github.com/nodejs/node/pull/46991)
- \[[`d4af671f09`](https://github.com/nodejs/node/commit/d4af671f09)] - **benchmark**: split `Buffer.byteLength` benchmark (Joyee Cheung) [#46616](https://github.com/nodejs/node/pull/46616)
- \[[`5f647fb7b4`](https://github.com/nodejs/node/commit/5f647fb7b4)] - **benchmark**: add benchmark for EventTarget add and remove (Debadree Chatterjee) [#46779](https://github.com/nodejs/node/pull/46779)
- \[[`d7d634bd67`](https://github.com/nodejs/node/commit/d7d634bd67)] - **benchmark**: fix worker startup benchmark (Joyee Cheung) [#46680](https://github.com/nodejs/node/pull/46680)
- \[[`f7c4796c56`](https://github.com/nodejs/node/commit/f7c4796c56)] - **benchmark**: add trailing commas in `benchmark/path` (Antoine du Hamel) [#46628](https://github.com/nodejs/node/pull/46628)
- \[[`9b0d5030a5`](https://github.com/nodejs/node/commit/9b0d5030a5)] - **benchmark**: add trailing commas in `benchmark/http` (Antoine du Hamel) [#46609](https://github.com/nodejs/node/pull/46609)
- \[[`e0f436041e`](https://github.com/nodejs/node/commit/e0f436041e)] - **benchmark**: add trailing commas in `benchmark/crypto` (Antoine du Hamel) [#46553](https://github.com/nodejs/node/pull/46553)
- \[[`a383aee386`](https://github.com/nodejs/node/commit/a383aee386)] - **benchmark**: add trailing commas in `benchmark/url` (Antoine du Hamel) [#46551](https://github.com/nodejs/node/pull/46551)
- \[[`a10c3558c6`](https://github.com/nodejs/node/commit/a10c3558c6)] - **benchmark**: add trailing commas in `benchmark/http2` (Antoine du Hamel) [#46552](https://github.com/nodejs/node/pull/46552)
- \[[`8036583f1f`](https://github.com/nodejs/node/commit/8036583f1f)] - **benchmark**: add trailing commas in `benchmark/process` (Antoine du Hamel) [#46481](https://github.com/nodejs/node/pull/46481)
- \[[`1497244078`](https://github.com/nodejs/node/commit/1497244078)] - **benchmark**: add trailing commas in `benchmark/misc` (Antoine du Hamel) [#46474](https://github.com/nodejs/node/pull/46474)
- \[[`057e3f5309`](https://github.com/nodejs/node/commit/057e3f5309)] - **benchmark**: add trailing commas in `benchmark/buffers` (Antoine du Hamel) [#46473](https://github.com/nodejs/node/pull/46473)
- \[[`26e1a81243`](https://github.com/nodejs/node/commit/26e1a81243)] - **benchmark**: add trailing commas in `benchmark/module` (Antoine du Hamel) [#46461](https://github.com/nodejs/node/pull/46461)
- \[[`bd6c828cf3`](https://github.com/nodejs/node/commit/bd6c828cf3)] - **benchmark**: add trailing commas in `benchmark/net` (Antoine du Hamel) [#46439](https://github.com/nodejs/node/pull/46439)
- \[[`01cf87aca7`](https://github.com/nodejs/node/commit/01cf87aca7)] - **benchmark**: add trailing commas in `benchmark/util` (Antoine du Hamel) [#46438](https://github.com/nodejs/node/pull/46438)
- \[[`f006b2f9dc`](https://github.com/nodejs/node/commit/f006b2f9dc)] - **benchmark**: add trailing commas in `benchmark/async_hooks` (Antoine du Hamel) [#46424](https://github.com/nodejs/node/pull/46424)
- \[[`f969cc30ab`](https://github.com/nodejs/node/commit/f969cc30ab)] - **benchmark**: add trailing commas in `benchmark/fs` (Antoine du Hamel) [#46426](https://github.com/nodejs/node/pull/46426)
- \[[`5202b84382`](https://github.com/nodejs/node/commit/5202b84382)] - **bootstrap**: print stack trace during environment creation failure (Joyee Cheung) [#46533](https://github.com/nodejs/node/pull/46533)
- \[[`c6e722aca4`](https://github.com/nodejs/node/commit/c6e722aca4)] - **(SEMVER-MINOR)** **buffer**: add Buffer.copyBytesFrom(...) (James M Snell) [#46500](https://github.com/nodejs/node/pull/46500)
- \[[`886504fdf8`](https://github.com/nodejs/node/commit/886504fdf8)] - **build**: fix Visual Studio installation detection for Arm64 (Radek Bartoň) [#46420](https://github.com/nodejs/node/pull/46420)
- \[[`2b72a453cf`](https://github.com/nodejs/node/commit/2b72a453cf)] - **build**: add GitHub Action for coverage with --without-intl (Rich Trott) [#37954](https://github.com/nodejs/node/pull/37954)
- \[[`ff07aa7fe3`](https://github.com/nodejs/node/commit/ff07aa7fe3)] - **build**: do not disable inspector when intl is disabled (Rich Trott) [#37954](https://github.com/nodejs/node/pull/37954)
- \[[`4b25b98bd8`](https://github.com/nodejs/node/commit/4b25b98bd8)] - **build,test**: add proper support for IBM i (Xu Meng) [#46739](https://github.com/nodejs/node/pull/46739)
- \[[`535311097c`](https://github.com/nodejs/node/commit/535311097c)] - **child_process**: add trailing commas in source files (Antoine du Hamel) [#46758](https://github.com/nodejs/node/pull/46758)
- \[[`d2692c65df`](https://github.com/nodejs/node/commit/d2692c65df)] - **cluster**: add trailing commas in source files (Antoine du Hamel) [#46695](https://github.com/nodejs/node/pull/46695)
- \[[`effdca8b10`](https://github.com/nodejs/node/commit/effdca8b10)] - **crypto**: don't assume FIPS is disabled by default (Michael Dawson) [#46532](https://github.com/nodejs/node/pull/46532)
- \[[`bce37c60ce`](https://github.com/nodejs/node/commit/bce37c60ce)] - **debugger**: improve validations and documents for watch and unwatch (Eungyu Lee) [#46947](https://github.com/nodejs/node/pull/46947)
- \[[`51253bae83`](https://github.com/nodejs/node/commit/51253bae83)] - **debugger**: add a command to set which lines to check for context (Eungyu Lee) [#46812](https://github.com/nodejs/node/pull/46812)
- \[[`44375c6a3c`](https://github.com/nodejs/node/commit/44375c6a3c)] - **debugger**: add trailing commas in source files (Antoine du Hamel) [#46714](https://github.com/nodejs/node/pull/46714)
- \[[`e5c4d69681`](https://github.com/nodejs/node/commit/e5c4d69681)] - **deps**: update ada to 1.0.4 (Node.js GitHub Bot) [#46853](https://github.com/nodejs/node/pull/46853)
- \[[`94f83536d7`](https://github.com/nodejs/node/commit/94f83536d7)] - **deps**: update ada to 1.0.3 (Node.js GitHub Bot) [#46784](https://github.com/nodejs/node/pull/46784)
- \[[`484c4f6674`](https://github.com/nodejs/node/commit/484c4f6674)] - **deps**: update ada to v1.0.1 (Yagiz Nizipli) [#46550](https://github.com/nodejs/node/pull/46550)
- \[[`0bc4c17e57`](https://github.com/nodejs/node/commit/0bc4c17e57)] - **deps**: add ada as a dependency (Yagiz Nizipli) [#46410](https://github.com/nodejs/node/pull/46410)
- \[[`956f786499`](https://github.com/nodejs/node/commit/956f786499)] - **deps**: update undici to 5.21.0 (Node.js GitHub Bot) [#47063](https://github.com/nodejs/node/pull/47063)
- \[[`73be4f8ef5`](https://github.com/nodejs/node/commit/73be4f8ef5)] - **deps**: update simdutf to 3.2.2 (Node.js GitHub Bot) [#46841](https://github.com/nodejs/node/pull/46841)
- \[[`0e78fd5883`](https://github.com/nodejs/node/commit/0e78fd5883)] - **deps**: update corepack to 0.17.0 (Node.js GitHub Bot) [#46842](https://github.com/nodejs/node/pull/46842)
- \[[`61c9433d8a`](https://github.com/nodejs/node/commit/61c9433d8a)] - **deps**: update simdutf to 3.2.1 (Node.js GitHub Bot) [#46800](https://github.com/nodejs/node/pull/46800)
- \[[`63a62ed532`](https://github.com/nodejs/node/commit/63a62ed532)] - **deps**: upgrade npm to 9.5.1 (npm team) [#46783](https://github.com/nodejs/node/pull/46783)
- \[[`c8974d678a`](https://github.com/nodejs/node/commit/c8974d678a)] - **deps**: update nghttp2 to 1.52.0 (Michaël Zasso) [#46636](https://github.com/nodejs/node/pull/46636)
- \[[`2b439a2cdf`](https://github.com/nodejs/node/commit/2b439a2cdf)] - **deps**: fix libuv for android (Julian Dropmann) [#46746](https://github.com/nodejs/node/pull/46746)
- \[[`d5eb1df869`](https://github.com/nodejs/node/commit/d5eb1df869)] - **deps**: update simdutf to 3.2.0 (Node.js GitHub Bot) [#46621](https://github.com/nodejs/node/pull/46621)
- \[[`dd97b05aeb`](https://github.com/nodejs/node/commit/dd97b05aeb)] - **deps**: update corepack to 0.16.0 (Node.js GitHub Bot) [#46710](https://github.com/nodejs/node/pull/46710)
- \[[`65b877de45`](https://github.com/nodejs/node/commit/65b877de45)] - **deps**: copy `postject-api.h` and `LICENSE` to the `deps` folder (Darshan Sen) [#46582](https://github.com/nodejs/node/pull/46582)
- \[[`a918ac886e`](https://github.com/nodejs/node/commit/a918ac886e)] - **deps**: update c-ares to 1.19.0 (Michaël Zasso) [#46415](https://github.com/nodejs/node/pull/46415)
- \[[`1ac639a240`](https://github.com/nodejs/node/commit/1ac639a240)] - **deps**: V8: cherry-pick 9ec4e9095a25 (Kleis Auke Wolthuizen) [#47092](https://github.com/nodejs/node/pull/47092)
- \[[`f8d4bf8540`](https://github.com/nodejs/node/commit/f8d4bf8540)] - **deps,test**: update postject to 1.0.0-alpha.5 (Node.js GitHub Bot) [#46934](https://github.com/nodejs/node/pull/46934)
- \[[`8646b06c1b`](https://github.com/nodejs/node/commit/8646b06c1b)] - **dgram**: fix unhandled exception aborting a closed udp socket (Ramana Venkata) [#46770](https://github.com/nodejs/node/pull/46770)
- \[[`e435199ccc`](https://github.com/nodejs/node/commit/e435199ccc)] - **doc**: remove remaining SSL_OP_NETSCAPE\_\*\_BUG (Tobias Nießen) [#47066](https://github.com/nodejs/node/pull/47066)
- \[[`01d82670c7`](https://github.com/nodejs/node/commit/01d82670c7)] - **doc**: fix typo in test.md (Victor Hiairrassary) [#47053](https://github.com/nodejs/node/pull/47053)
- \[[`0e3077dc48`](https://github.com/nodejs/node/commit/0e3077dc48)] - **doc**: amend support tier qualifier (Gireesh Punathil) [#42805](https://github.com/nodejs/node/pull/42805)
- \[[`a5bf6693b9`](https://github.com/nodejs/node/commit/a5bf6693b9)] - **doc**: fix typo on esm loaders example (Ruy Adorno) [#47015](https://github.com/nodejs/node/pull/47015)
- \[[`6a0c1d053e`](https://github.com/nodejs/node/commit/6a0c1d053e)] - **doc**: add missing test runner flags to man page (Colin Ihrig) [#46982](https://github.com/nodejs/node/pull/46982)
- \[[`43b94b0f13`](https://github.com/nodejs/node/commit/43b94b0f13)] - **doc**: fix history information for `node:diagnostics_channel` (Thomas Hunter II) [#46984](https://github.com/nodejs/node/pull/46984)
- \[[`b37d53a1ba`](https://github.com/nodejs/node/commit/b37d53a1ba)] - **doc**: fix myUrl is not defined in url (Youngmin Yoo) [#46968](https://github.com/nodejs/node/pull/46968)
- \[[`257c5ac1fa`](https://github.com/nodejs/node/commit/257c5ac1fa)] - **doc**: remove useless SSL_OP\_\* options (Tobias Nießen) [#46954](https://github.com/nodejs/node/pull/46954)
- \[[`09c5e6a9f3`](https://github.com/nodejs/node/commit/09c5e6a9f3)] - **doc**: fix description of TLS dhparam option (Tobias Nießen) [#46949](https://github.com/nodejs/node/pull/46949)
- \[[`8907732fcf`](https://github.com/nodejs/node/commit/8907732fcf)] - **doc**: improve fs code example quality (jakecastelli) [#46948](https://github.com/nodejs/node/pull/46948)
- \[[`17a25f1153`](https://github.com/nodejs/node/commit/17a25f1153)] - **doc**: fix port of destination server is not defined in http2 (Deokjin Kim) [#46940](https://github.com/nodejs/node/pull/46940)
- \[[`ad06168a5c`](https://github.com/nodejs/node/commit/ad06168a5c)] - **doc**: use number which is bigger than 1024 as port in http2 (Deokjin Kim) [#46938](https://github.com/nodejs/node/pull/46938)
- \[[`4e6dda5be4`](https://github.com/nodejs/node/commit/4e6dda5be4)] - **doc**: add release key for Juan Arboleda (Juan José) [#46922](https://github.com/nodejs/node/pull/46922)
- \[[`f49c6e64ba`](https://github.com/nodejs/node/commit/f49c6e64ba)] - **doc**: fix links to SSL_CTX_set_options (Tobias Nießen) [#46953](https://github.com/nodejs/node/pull/46953)
- \[[`ea7fb16e5c`](https://github.com/nodejs/node/commit/ea7fb16e5c)] - **doc**: fix fs missing import (jakecastelli) [#46907](https://github.com/nodejs/node/pull/46907)
- \[[`11885a7351`](https://github.com/nodejs/node/commit/11885a7351)] - **doc**: add request to hold off publicising sec releases (Michael Dawson) [#46702](https://github.com/nodejs/node/pull/46702)
- \[[`0254fd1da6`](https://github.com/nodejs/node/commit/0254fd1da6)] - **doc**: fix stream iterator helpers examples (Benjamin Gruenbaum) [#46897](https://github.com/nodejs/node/pull/46897)
- \[[`0a983f7125`](https://github.com/nodejs/node/commit/0a983f7125)] - **doc**: add history info for `node:test` (Antoine du Hamel) [#46851](https://github.com/nodejs/node/pull/46851)
- \[[`810d393ded`](https://github.com/nodejs/node/commit/810d393ded)] - **doc**: sort import order (jakecastelli) [#46847](https://github.com/nodejs/node/pull/46847)
- \[[`6e03499437`](https://github.com/nodejs/node/commit/6e03499437)] - **doc**: use destructing import (jakecastelli) [#46847](https://github.com/nodejs/node/pull/46847)
- \[[`8b636c3cd6`](https://github.com/nodejs/node/commit/8b636c3cd6)] - **doc**: add marco-ippolito to collaborators (Marco Ippolito) [#46816](https://github.com/nodejs/node/pull/46816)
- \[[`7e08ca125a`](https://github.com/nodejs/node/commit/7e08ca125a)] - **doc**: document how to use the tls.DEFAULT_CIPHERS (Andreas Martens) [#46482](https://github.com/nodejs/node/pull/46482)
- \[[`3dae6f2f81`](https://github.com/nodejs/node/commit/3dae6f2f81)] - **doc**: add document for profiling and heap snapshot (cola119) [#46787](https://github.com/nodejs/node/pull/46787)
- \[[`eef30513b9`](https://github.com/nodejs/node/commit/eef30513b9)] - **doc**: add test:coverage event to custom reporter examples (Richie McColl) [#46752](https://github.com/nodejs/node/pull/46752)
- \[[`e6db6bedf7`](https://github.com/nodejs/node/commit/e6db6bedf7)] - **doc**: include context on .toWeb() parameters (Debadree Chatterjee) [#46617](https://github.com/nodejs/node/pull/46617)
- \[[`a24350e49f`](https://github.com/nodejs/node/commit/a24350e49f)] - **doc**: add in security steward for recent release (Michael Dawson) [#46701](https://github.com/nodejs/node/pull/46701)
- \[[`55360e9386`](https://github.com/nodejs/node/commit/55360e9386)] - **doc**: clarify semver-minor notable changes approach (Beth Griggs) [#46592](https://github.com/nodejs/node/pull/46592)
- \[[`a384dd42ff`](https://github.com/nodejs/node/commit/a384dd42ff)] - **doc**: maintaining nghttp2 (Marco Ippolito) [#46539](https://github.com/nodejs/node/pull/46539)
- \[[`45fccc9737`](https://github.com/nodejs/node/commit/45fccc9737)] - **doc**: add emit to NodeEventTarget (Deokjin Kim) [#46356](https://github.com/nodejs/node/pull/46356)
- \[[`760616890c`](https://github.com/nodejs/node/commit/760616890c)] - **doc**: add debadree25 to collaborators (Debadree Chatterjee) [#46716](https://github.com/nodejs/node/pull/46716)
- \[[`b9dd876e7c`](https://github.com/nodejs/node/commit/b9dd876e7c)] - **doc**: move bcoe to emeriti (Benjamin Coe) [#46703](https://github.com/nodejs/node/pull/46703)
- \[[`3afbb92bb4`](https://github.com/nodejs/node/commit/3afbb92bb4)] - **doc**: add response.strictContentLength to documentation (Marco Ippolito) [#46627](https://github.com/nodejs/node/pull/46627)
- \[[`2c0e1aa095`](https://github.com/nodejs/node/commit/2c0e1aa095)] - **doc**: remove unused functions from example of `streamConsumers.text` (Deokjin Kim) [#46581](https://github.com/nodejs/node/pull/46581)
- \[[`61268303fc`](https://github.com/nodejs/node/commit/61268303fc)] - **doc**: fix test runner examples (Richie McColl) [#46565](https://github.com/nodejs/node/pull/46565)
- \[[`2b702c98c2`](https://github.com/nodejs/node/commit/2b702c98c2)] - **doc**: update test concurrency description / default values (richiemccoll) [#46457](https://github.com/nodejs/node/pull/46457)
- \[[`f1de3f7a31`](https://github.com/nodejs/node/commit/f1de3f7a31)] - **doc**: enrich test command with executable (Tony Gorez) [#44347](https://github.com/nodejs/node/pull/44347)
- \[[`68b5cf8e38`](https://github.com/nodejs/node/commit/68b5cf8e38)] - **doc**: fix wrong location of `requestTimeout`'s default value (Deokjin Kim) [#46423](https://github.com/nodejs/node/pull/46423)
- \[[`4d5d6d2193`](https://github.com/nodejs/node/commit/4d5d6d2193)] - **doc**: add deokjinkim to collaborators (Deokjin Kim) [#46444](https://github.com/nodejs/node/pull/46444)
- \[[`de7f6182be`](https://github.com/nodejs/node/commit/de7f6182be)] - **doc**: fix -C flag usage (三咲智子 Kevin Deng) [#46388](https://github.com/nodejs/node/pull/46388)
- \[[`4165cf34ba`](https://github.com/nodejs/node/commit/4165cf34ba)] - **doc**: add note about major release rotation (Rafael Gonzaga) [#46436](https://github.com/nodejs/node/pull/46436)
- \[[`f088ce2dc7`](https://github.com/nodejs/node/commit/f088ce2dc7)] - **doc**: update threat model based on discussions (Michael Dawson) [#46373](https://github.com/nodejs/node/pull/46373)
- \[[`5b94e2bcdb`](https://github.com/nodejs/node/commit/5b94e2bcdb)] - **esm**: fix import assertion warning (Antoine du Hamel) [#46971](https://github.com/nodejs/node/pull/46971)
- \[[`96a39d1a99`](https://github.com/nodejs/node/commit/96a39d1a99)] - **esm**: add a runtime warning when using import assertions (Antoine du Hamel) [#46901](https://github.com/nodejs/node/pull/46901)
- \[[`320a8adb45`](https://github.com/nodejs/node/commit/320a8adb45)] - **esm**: misc test refactors (Geoffrey Booth) [#46631](https://github.com/nodejs/node/pull/46631)
- \[[`b08687f739`](https://github.com/nodejs/node/commit/b08687f739)] - **events**: add trailing commas in source files (Antoine du Hamel) [#46759](https://github.com/nodejs/node/pull/46759)
- \[[`cc6deeaf7a`](https://github.com/nodejs/node/commit/cc6deeaf7a)] - **(SEMVER-MINOR)** **events**: add listener argument to listenerCount (Paolo Insogna) [#46523](https://github.com/nodejs/node/pull/46523)
- \[[`efc24097a6`](https://github.com/nodejs/node/commit/efc24097a6)] - **fs**: add trailing commas in source files (Antoine du Hamel) [#46696](https://github.com/nodejs/node/pull/46696)
- \[[`80b4e6da53`](https://github.com/nodejs/node/commit/80b4e6da53)] - **http**: use listenerCount when adding noop event (Paolo Insogna) [#46769](https://github.com/nodejs/node/pull/46769)
- \[[`3538521bf6`](https://github.com/nodejs/node/commit/3538521bf6)] - **http**: correctly calculate strict content length (Robert Nagy) [#46601](https://github.com/nodejs/node/pull/46601)
- \[[`9582c8ef3a`](https://github.com/nodejs/node/commit/9582c8ef3a)] - **http**: fix validation of "Link" header (Steve Herzog) [#46466](https://github.com/nodejs/node/pull/46466)
- \[[`23c1e2fa52`](https://github.com/nodejs/node/commit/23c1e2fa52)] - **http**: unify header treatment (Marco Ippolito) [#46528](https://github.com/nodejs/node/pull/46528)
- \[[`abeee994c4`](https://github.com/nodejs/node/commit/abeee994c4)] - **http**: add note about clientError event (Paolo Insogna) [#46584](https://github.com/nodejs/node/pull/46584)
- \[[`3d0602c96c`](https://github.com/nodejs/node/commit/3d0602c96c)] - **http**: use v8::Array::New() with a prebuilt vector (Joyee Cheung) [#46447](https://github.com/nodejs/node/pull/46447)
- \[[`62cbddd86f`](https://github.com/nodejs/node/commit/62cbddd86f)] - **lib**: fix trailing commas and leftover function from rebasing (Danielle Adams) [#47503](https://github.com/nodejs/node/pull/47503)
- \[[`c463f133bd`](https://github.com/nodejs/node/commit/c463f133bd)] - **lib**: enforce use of trailing commas (Antoine du Hamel) [#46881](https://github.com/nodejs/node/pull/46881)
- \[[`0f33bb0961`](https://github.com/nodejs/node/commit/0f33bb0961)] - **lib**: add trailing commas to all public core modules (Antoine du Hamel) [#46848](https://github.com/nodejs/node/pull/46848)
- \[[`06e0dd3e15`](https://github.com/nodejs/node/commit/06e0dd3e15)] - **lib**: rename internal module declaration as internal bindings (okmttdhr, okp) [#46663](https://github.com/nodejs/node/pull/46663)
- \[[`31578ab1b4`](https://github.com/nodejs/node/commit/31578ab1b4)] - **lib**: add trailing commas to more internal files (Antoine du Hamel) [#46811](https://github.com/nodejs/node/pull/46811)
- \[[`ad510d9029`](https://github.com/nodejs/node/commit/ad510d9029)] - **lib**: update punycode to 2.3.0 (Yagiz Nizipli) [#46719](https://github.com/nodejs/node/pull/46719)
- \[[`4cf3de8b02`](https://github.com/nodejs/node/commit/4cf3de8b02)] - **lib**: add trailing commas in `internal/perf` (Antoine du Hamel) [#46697](https://github.com/nodejs/node/pull/46697)
- \[[`f1b79828bc`](https://github.com/nodejs/node/commit/f1b79828bc)] - **(SEMVER-MINOR)** **lib**: add AsyncLocalStorage.bind() and .snapshot() (flakey5) [#46387](https://github.com/nodejs/node/pull/46387)
- \[[`48cd712c0d`](https://github.com/nodejs/node/commit/48cd712c0d)] - **lib**: add trailing commas in `internal/process` (Antoine du Hamel) [#46687](https://github.com/nodejs/node/pull/46687)
- \[[`46a22ab601`](https://github.com/nodejs/node/commit/46a22ab601)] - **lib**: do not crash using workers with disabled shared array buffers (Ruben Bridgewater) [#41023](https://github.com/nodejs/node/pull/41023)
- \[[`1395e36e64`](https://github.com/nodejs/node/commit/1395e36e64)] - **lib**: delete module findPath unused params (sinkhaha) [#45371](https://github.com/nodejs/node/pull/45371)
- \[[`c410572620`](https://github.com/nodejs/node/commit/c410572620)] - **lib**: enforce use of trailing commas in more files (Antoine du Hamel) [#46655](https://github.com/nodejs/node/pull/46655)
- \[[`36e080cd13`](https://github.com/nodejs/node/commit/36e080cd13)] - **lib**: enforce use of trailing commas for functions (Antoine du Hamel) [#46629](https://github.com/nodejs/node/pull/46629)
- \[[`71249a6c00`](https://github.com/nodejs/node/commit/71249a6c00)] - **lib**: predeclare Event.isTrusted prop descriptor (Santiago Gimeno) [#46527](https://github.com/nodejs/node/pull/46527)
- \[[`2b80d19c06`](https://github.com/nodejs/node/commit/2b80d19c06)] - **lib**: tighten `AbortSignal.prototype.throwIfAborted` implementation (Antoine du Hamel) [#46521](https://github.com/nodejs/node/pull/46521)
- \[[`2f79f3fa38`](https://github.com/nodejs/node/commit/2f79f3fa38)] - **(SEMVER-MINOR)** **lib**: add aborted() utility function (Debadree Chatterjee) [#46494](https://github.com/nodejs/node/pull/46494)
- \[[`7f9e60aa1a`](https://github.com/nodejs/node/commit/7f9e60aa1a)] - **lib,src**: fix a few typos in comments (Tobias Nießen) [#46835](https://github.com/nodejs/node/pull/46835)
- \[[`c4f192b592`](https://github.com/nodejs/node/commit/c4f192b592)] - **meta**: add single-executable labels and code owners (Joyee Cheung) [#47004](https://github.com/nodejs/node/pull/47004)
- \[[`abe1488f86`](https://github.com/nodejs/node/commit/abe1488f86)] - **meta**: remove AUTHORS file (Rich Trott) [#46845](https://github.com/nodejs/node/pull/46845)
- \[[`e53774c7bf`](https://github.com/nodejs/node/commit/e53774c7bf)] - **meta**: remove unnecessary onboarding step (Rich Trott) [#46793](https://github.com/nodejs/node/pull/46793)
- \[[`55f1cfd7e4`](https://github.com/nodejs/node/commit/55f1cfd7e4)] - **meta**: update CODEOWNERS of url implementations (Yagiz Nizipli) [#46775](https://github.com/nodejs/node/pull/46775)
- \[[`ccaa7cabdd`](https://github.com/nodejs/node/commit/ccaa7cabdd)] - **meta**: update AUTHORS (Node.js GitHub Bot) [#46726](https://github.com/nodejs/node/pull/46726)
- \[[`260ddd5b0f`](https://github.com/nodejs/node/commit/260ddd5b0f)] - **meta**: update AUTHORS (Node.js GitHub Bot) [#46624](https://github.com/nodejs/node/pull/46624)
- \[[`5ef78e50a0`](https://github.com/nodejs/node/commit/5ef78e50a0)] - **meta**: move one or more collaborators to emeritus (Node.js GitHub Bot) [#46513](https://github.com/nodejs/node/pull/46513)
- \[[`5efb829156`](https://github.com/nodejs/node/commit/5efb829156)] - **meta**: update AUTHORS (Node.js GitHub Bot) [#46504](https://github.com/nodejs/node/pull/46504)
- \[[`f0705f7de1`](https://github.com/nodejs/node/commit/f0705f7de1)] - **meta**: move one or more collaborators to emeritus (Node.js GitHub Bot) [#46411](https://github.com/nodejs/node/pull/46411)
- \[[`331073a7aa`](https://github.com/nodejs/node/commit/331073a7aa)] - **node-api**: add \_\_wasm32\_\_ guards on async works (Chengzhong Wu) [#46633](https://github.com/nodejs/node/pull/46633)
- \[[`594bb9d558`](https://github.com/nodejs/node/commit/594bb9d558)] - **os**: improve network interface performance (Ruben Bridgewater) [#46598](https://github.com/nodejs/node/pull/46598)
- \[[`c231aae6c5`](https://github.com/nodejs/node/commit/c231aae6c5)] - **process**: print versions by sort (Himself65) [#46428](https://github.com/nodejs/node/pull/46428)
- \[[`fe318dd095`](https://github.com/nodejs/node/commit/fe318dd095)] - **punycode**: add pending deprecation (Antoine du Hamel) [#46719](https://github.com/nodejs/node/pull/46719)
- \[[`34ba230d9b`](https://github.com/nodejs/node/commit/34ba230d9b)] - **repl**: remove lastInputPreview conditional check (Duy Mac Van) [#46857](https://github.com/nodejs/node/pull/46857)
- \[[`09739a20b1`](https://github.com/nodejs/node/commit/09739a20b1)] - **repl**: fix .load infinite loop caused by shared use of lineEnding RegExp (Theodor Steiner) [#46742](https://github.com/nodejs/node/pull/46742)
- \[[`91fbad617d`](https://github.com/nodejs/node/commit/91fbad617d)] - **repl**: add trailing commas in source files (Antoine du Hamel) [#46757](https://github.com/nodejs/node/pull/46757)
- \[[`e4cd81b526`](https://github.com/nodejs/node/commit/e4cd81b526)] - **src**: use string_view for report and related code (Anna Henningsen) [#46723](https://github.com/nodejs/node/pull/46723)
- \[[`8ea835419e`](https://github.com/nodejs/node/commit/8ea835419e)] - **(SEMVER-MINOR)** **src**: add initial support for single executable applications (Darshan Sen) [#45038](https://github.com/nodejs/node/pull/45038)
- \[[`4652dfdc88`](https://github.com/nodejs/node/commit/4652dfdc88)] - **src**: remove TLSEXT_TYPE_alpn guard (Tobias Nießen) [#46956](https://github.com/nodejs/node/pull/46956)
- \[[`a4a433fc0b`](https://github.com/nodejs/node/commit/a4a433fc0b)] - **src**: remove use of SSL_OP_SINGLE_DH_USE (Tobias Nießen) [#46955](https://github.com/nodejs/node/pull/46955)
- \[[`339b52f3bc`](https://github.com/nodejs/node/commit/339b52f3bc)] - **src**: make util.h self-containted (Joyee Cheung) [#46817](https://github.com/nodejs/node/pull/46817)
- \[[`b25ee523ea`](https://github.com/nodejs/node/commit/b25ee523ea)] - **src**: remove shadowed variable in OptionsParser (Shelley Vohr) [#46672](https://github.com/nodejs/node/pull/46672)
- \[[`88baf98fa3`](https://github.com/nodejs/node/commit/88baf98fa3)] - **src**: do not track BaseObjects directly in Realm (Joyee Cheung) [#46470](https://github.com/nodejs/node/pull/46470)
- \[[`03e8d824db`](https://github.com/nodejs/node/commit/03e8d824db)] - **src**: fix cb scope bugs involved in termination (ywave620) [#45596](https://github.com/nodejs/node/pull/45596)
- \[[`7fb32b43dc`](https://github.com/nodejs/node/commit/7fb32b43dc)] - **src**: add SetFastMethodNoSideEffect() (Joyee Cheung) [#46619](https://github.com/nodejs/node/pull/46619)
- \[[`ea411d6422`](https://github.com/nodejs/node/commit/ea411d6422)] - **(SEMVER-MINOR)** **src**: allow optional Isolate termination in node::Stop() (Shelley Vohr) [#46583](https://github.com/nodejs/node/pull/46583)
- \[[`f8d029183a`](https://github.com/nodejs/node/commit/f8d029183a)] - **src**: remove icu usage from node_string.cc (Yagiz Nizipli) [#46548](https://github.com/nodejs/node/pull/46548)
- \[[`95674360b2`](https://github.com/nodejs/node/commit/95674360b2)] - **src**: add fflush() to SnapshotData::ToFile() (Anna Henningsen) [#46531](https://github.com/nodejs/node/pull/46531)
- \[[`9445a0a5f6`](https://github.com/nodejs/node/commit/9445a0a5f6)] - **src**: make edge names in BaseObjects more descriptive in heap snapshots (Joyee Cheung) [#46492](https://github.com/nodejs/node/pull/46492)
- \[[`483cd1922f`](https://github.com/nodejs/node/commit/483cd1922f)] - **src**: avoid leaking snapshot fp on error (Tobias Nießen) [#46497](https://github.com/nodejs/node/pull/46497)
- \[[`bc9a577625`](https://github.com/nodejs/node/commit/bc9a577625)] - **src**: check return value of ftell() (Tobias Nießen) [#46495](https://github.com/nodejs/node/pull/46495)
- \[[`b3b3b33799`](https://github.com/nodejs/node/commit/b3b3b33799)] - **src**: remove unused includes from main thread (Yagiz Nizipli) [#46471](https://github.com/nodejs/node/pull/46471)
- \[[`b9ca302558`](https://github.com/nodejs/node/commit/b9ca302558)] - **src**: use string_view instead of std::string& (Yagiz Nizipli) [#46471](https://github.com/nodejs/node/pull/46471)
- \[[`f1c770065a`](https://github.com/nodejs/node/commit/f1c770065a)] - **src**: use simdutf utf8 to utf16 instead of icu (Yagiz Nizipli) [#46471](https://github.com/nodejs/node/pull/46471)
- \[[`aaf04f498a`](https://github.com/nodejs/node/commit/aaf04f498a)] - **src**: replace icu with simdutf for char counts (Yagiz Nizipli) [#46472](https://github.com/nodejs/node/pull/46472)
- \[[`3aef68d079`](https://github.com/nodejs/node/commit/3aef68d079)] - **src**: add additional utilities to crypto::SecureContext (James M Snell) [#45912](https://github.com/nodejs/node/pull/45912)
- \[[`92fdb24fac`](https://github.com/nodejs/node/commit/92fdb24fac)] - **src**: add KeyObjectHandle::HasInstance (James M Snell) [#45912](https://github.com/nodejs/node/pull/45912)
- \[[`a639e2a557`](https://github.com/nodejs/node/commit/a639e2a557)] - **src**: add GetCurrentCipherName/Version to crypto_common (James M Snell) [#45912](https://github.com/nodejs/node/pull/45912)
- \[[`8dfa748373`](https://github.com/nodejs/node/commit/8dfa748373)] - **src**: back snapshot I/O with a std::vector sink (Joyee Cheung) [#46463](https://github.com/nodejs/node/pull/46463)
- \[[`17eea1812e`](https://github.com/nodejs/node/commit/17eea1812e)] - **(SEMVER-MINOR)** **src**: allow embedder control of code generation policy (Shelley Vohr) [#46368](https://github.com/nodejs/node/pull/46368)
- \[[`dffe7d814e`](https://github.com/nodejs/node/commit/dffe7d814e)] - **stream**: enable usage of webstreams on compose() (Debadree Chatterjee) [#46675](https://github.com/nodejs/node/pull/46675)
- \[[`654b7474e3`](https://github.com/nodejs/node/commit/654b7474e3)] - **stream**: always delay construct callback by a nextTick (Matteo Collina) [#46818](https://github.com/nodejs/node/pull/46818)
- \[[`ed2faa7c7c`](https://github.com/nodejs/node/commit/ed2faa7c7c)] - **stream**: fix respondWithNewView() errors when view\.byteOffset != 0 (Debadree Chatterjee) [#46465](https://github.com/nodejs/node/pull/46465)
- \[[`247caacc7a`](https://github.com/nodejs/node/commit/247caacc7a)] - **stream**: fix pipeline callback not called on ended stream (Debadree Chatterjee) [#46600](https://github.com/nodejs/node/pull/46600)
- \[[`c40eec3f3b`](https://github.com/nodejs/node/commit/c40eec3f3b)] - **stream**: add trailing commas in webstream source files (Antoine du Hamel) [#46685](https://github.com/nodejs/node/pull/46685)
- \[[`d669f22567`](https://github.com/nodejs/node/commit/d669f22567)] - **stream**: add trailing commas in stream source files (Antoine du Hamel) [#46686](https://github.com/nodejs/node/pull/46686)
- \[[`c60816a68d`](https://github.com/nodejs/node/commit/c60816a68d)] - **(SEMVER-MINOR)** **stream**: add abort signal for ReadableStream and WritableStream (Debadree Chatterjee) [#46273](https://github.com/nodejs/node/pull/46273)
- \[[`17b4e9bc5f`](https://github.com/nodejs/node/commit/17b4e9bc5f)] - **stream**: refactor to use `validateAbortSignal` (Antoine du Hamel) [#46520](https://github.com/nodejs/node/pull/46520)
- \[[`4d81124397`](https://github.com/nodejs/node/commit/4d81124397)] - **stream**: allow transfer of readable byte streams (MrBBot) [#45955](https://github.com/nodejs/node/pull/45955)
- \[[`6c454af5ea`](https://github.com/nodejs/node/commit/6c454af5ea)] - **stream**: add pipeline() for webstreams (Debadree Chatterjee) [#46307](https://github.com/nodejs/node/pull/46307)
- \[[`91a550e5a1`](https://github.com/nodejs/node/commit/91a550e5a1)] - **stream**: add suport for abort signal in finished() for webstreams (Debadree Chatterjee) [#46403](https://github.com/nodejs/node/pull/46403)
- \[[`406ff320bf`](https://github.com/nodejs/node/commit/406ff320bf)] - **stream**: dont access Object.prototype.type during TransformStream init (Debadree Chatterjee) [#46389](https://github.com/nodejs/node/pull/46389)
- \[[`972bdee8c4`](https://github.com/nodejs/node/commit/972bdee8c4)] - **test**: update web-platform tests for url (Yagiz Nizipli) [#46547](https://github.com/nodejs/node/pull/46547)
- \[[`ba340a0ddb`](https://github.com/nodejs/node/commit/ba340a0ddb)] - **test**: move `test-tls-autoselectfamily-servername` to `test/internet` (Antoine du Hamel) [#47029](https://github.com/nodejs/node/pull/47029)
- \[[`0edaf016cc`](https://github.com/nodejs/node/commit/0edaf016cc)] - **test**: fallback to IPv4 if IPv6 is unavailable (Abdirahim Musse) [#47017](https://github.com/nodejs/node/pull/47017)
- \[[`ef6c86c59f`](https://github.com/nodejs/node/commit/ef6c86c59f)] - **test**: simplify test-tls-ecdh-multiple (Tobias Nießen) [#46963](https://github.com/nodejs/node/pull/46963)
- \[[`b8cdd1502f`](https://github.com/nodejs/node/commit/b8cdd1502f)] - **test**: improve test coverage of lib/dns (Anderson Paiva) [#46910](https://github.com/nodejs/node/pull/46910)
- \[[`33991954b6`](https://github.com/nodejs/node/commit/33991954b6)] - **test**: simplify test-tls-ecdh-auto (Tobias Nießen) [#46911](https://github.com/nodejs/node/pull/46911)
- \[[`512f28fa8b`](https://github.com/nodejs/node/commit/512f28fa8b)] - **test**: move testPath from CWD to temporary directory (Livia Medeiros) [#46890](https://github.com/nodejs/node/pull/46890)
- \[[`3fdcf7bef4`](https://github.com/nodejs/node/commit/3fdcf7bef4)] - **test**: assume priv ports start at 1024 if it can't be changed (KrayzeeKev) [#46536](https://github.com/nodejs/node/pull/46536)
- \[[`966d0d8b16`](https://github.com/nodejs/node/commit/966d0d8b16)] - **test**: fix os-release check for Ubuntu in SEA test (Anna Henningsen) [#46838](https://github.com/nodejs/node/pull/46838)
- \[[`b344e74bc1`](https://github.com/nodejs/node/commit/b344e74bc1)] - **test**: move socket from CWD to temporary directory (Livia Medeiros) [#46863](https://github.com/nodejs/node/pull/46863)
- \[[`dfb566aec2`](https://github.com/nodejs/node/commit/dfb566aec2)] - **test**: fix test-net-connect-reset-until-connected (Vita Batrla) [#46781](https://github.com/nodejs/node/pull/46781)
- \[[`364479644c`](https://github.com/nodejs/node/commit/364479644c)] - **test**: simplify test-tls-alert (Tobias Nießen) [#46805](https://github.com/nodejs/node/pull/46805)
- \[[`df149448b5`](https://github.com/nodejs/node/commit/df149448b5)] - **test**: update encoding WPTs (Filip Skokan) [#46802](https://github.com/nodejs/node/pull/46802)
- \[[`928998dd2d`](https://github.com/nodejs/node/commit/928998dd2d)] - **test**: remove useless WPT init scripts (Filip Skokan) [#46801](https://github.com/nodejs/node/pull/46801)
- \[[`bb182461a3`](https://github.com/nodejs/node/commit/bb182461a3)] - **test**: remove unreachable return (jakecastelli) [#46807](https://github.com/nodejs/node/pull/46807)
- \[[`ea71a2ae1b`](https://github.com/nodejs/node/commit/ea71a2ae1b)] - **test**: fix test-v8-collect-gc-profile-in-worker.js (theanarkh) [#46735](https://github.com/nodejs/node/pull/46735)
- \[[`25cab7eab5`](https://github.com/nodejs/node/commit/25cab7eab5)] - **test**: improve control flow in test-tls-dhe (Tobias Nießen) [#46751](https://github.com/nodejs/node/pull/46751)
- \[[`3b70e7a34e`](https://github.com/nodejs/node/commit/3b70e7a34e)] - **test**: include strace openat test (Rafael Gonzaga) [#46150](https://github.com/nodejs/node/pull/46150)
- \[[`533170d456`](https://github.com/nodejs/node/commit/533170d456)] - **test**: remove OpenSSL 1.0.2 error message compat (Tobias Nießen) [#46709](https://github.com/nodejs/node/pull/46709)
- \[[`b8948996b4`](https://github.com/nodejs/node/commit/b8948996b4)] - **test**: fix flaky test-watch-mode-files_watcher (Moshe Atlow) [#46738](https://github.com/nodejs/node/pull/46738)
- \[[`931111609e`](https://github.com/nodejs/node/commit/931111609e)] - **test**: use newish OpenSSL feature in test-tls-dhe (Tobias Nießen) [#46708](https://github.com/nodejs/node/pull/46708)
- \[[`847d74005c`](https://github.com/nodejs/node/commit/847d74005c)] - **test**: fix assertions in test-snapshot-dns-lookup\* (Tobias Nießen) [#46618](https://github.com/nodejs/node/pull/46618)
- \[[`d7b995cd38`](https://github.com/nodejs/node/commit/d7b995cd38)] - **test**: cover publicExponent validation in OpenSSL (Tobias Nießen) [#46632](https://github.com/nodejs/node/pull/46632)
- \[[`a2b103a761`](https://github.com/nodejs/node/commit/a2b103a761)] - **test**: add trailing commas in `test/pummel` (Antoine du Hamel) [#46610](https://github.com/nodejs/node/pull/46610)
- \[[`4fb2fc7536`](https://github.com/nodejs/node/commit/4fb2fc7536)] - **test**: enable api-invalid-label.any.js in encoding WPTs (Filip Skokan) [#46506](https://github.com/nodejs/node/pull/46506)
- \[[`b0f2415312`](https://github.com/nodejs/node/commit/b0f2415312)] - **test**: add trailing commas in `test/js-native-api` (Antoine du Hamel) [#46385](https://github.com/nodejs/node/pull/46385)
- \[[`534562f51d`](https://github.com/nodejs/node/commit/534562f51d)] - **test**: make more crypto tests work with BoringSSL (Shelley Vohr) [#46429](https://github.com/nodejs/node/pull/46429)
- \[[`9bb08527f0`](https://github.com/nodejs/node/commit/9bb08527f0)] - **test**: add trailing commas in `test/known_issues` (Antoine du Hamel) [#46408](https://github.com/nodejs/node/pull/46408)
- \[[`231f0ee9b2`](https://github.com/nodejs/node/commit/231f0ee9b2)] - **test**: add trailing commas in `test/internet` (Antoine du Hamel) [#46407](https://github.com/nodejs/node/pull/46407)
- \[[`5e008e7d38`](https://github.com/nodejs/node/commit/5e008e7d38)] - **test,crypto**: update WebCryptoAPI WPT (Filip Skokan) [#47010](https://github.com/nodejs/node/pull/47010)
- \[[`99bfbed78a`](https://github.com/nodejs/node/commit/99bfbed78a)] - **test,crypto**: update WebCryptoAPI WPT (Filip Skokan) [#46575](https://github.com/nodejs/node/pull/46575)
- \[[`8398f9fcb6`](https://github.com/nodejs/node/commit/8398f9fcb6)] - **test_runner**: default to spec reporter when on TTY environment (Moshe Atlow) [#46969](https://github.com/nodejs/node/pull/46969)
- \[[`16db3ad1f8`](https://github.com/nodejs/node/commit/16db3ad1f8)] - **test_runner**: handle errors not bound to tests (Colin Ihrig) [#46962](https://github.com/nodejs/node/pull/46962)
- \[[`d60eef25a1`](https://github.com/nodejs/node/commit/d60eef25a1)] - **test_runner**: throw if harness is not bootstrapped (Colin Ihrig) [#46962](https://github.com/nodejs/node/pull/46962)
- \[[`ab5b3185f1`](https://github.com/nodejs/node/commit/ab5b3185f1)] - **test_runner**: track bootstrapping process (Colin Ihrig) [#46962](https://github.com/nodejs/node/pull/46962)
- \[[`413493cab9`](https://github.com/nodejs/node/commit/413493cab9)] - **test_runner**: avoid running twice tests in describe (Moshe Atlow) [#46888](https://github.com/nodejs/node/pull/46888)
- \[[`524eaf5488`](https://github.com/nodejs/node/commit/524eaf5488)] - **test_runner**: fix reconstruction of errors extracted from YAML (Moshe Atlow) [#46872](https://github.com/nodejs/node/pull/46872)
- \[[`17fbfb23bd`](https://github.com/nodejs/node/commit/17fbfb23bd)] - **test_runner**: reset count on watch mode (Moshe Atlow) [#46577](https://github.com/nodejs/node/pull/46577)
- \[[`3b077a6885`](https://github.com/nodejs/node/commit/3b077a6885)] - **test_runner**: remove root tracking set (Colin Ihrig) [#46961](https://github.com/nodejs/node/pull/46961)
- \[[`15503ffd12`](https://github.com/nodejs/node/commit/15503ffd12)] - **test_runner**: give the root test a harness reference (Colin Ihrig) [#46962](https://github.com/nodejs/node/pull/46962)
- \[[`e52b169ef4`](https://github.com/nodejs/node/commit/e52b169ef4)] - **test_runner**: align behavior of it and test (Moshe Atlow) [#46889](https://github.com/nodejs/node/pull/46889)
- \[[`9b58857630`](https://github.com/nodejs/node/commit/9b58857630)] - **timers**: cleanup no-longer relevant TODOs in timers/promises (James M Snell) [#46499](https://github.com/nodejs/node/pull/46499)
- \[[`32c527d80f`](https://github.com/nodejs/node/commit/32c527d80f)] - **(SEMVER-MINOR)** **tls**: support automatic DHE (Tobias Nießen) [#46978](https://github.com/nodejs/node/pull/46978)
- \[[`6ff90270ca`](https://github.com/nodejs/node/commit/6ff90270ca)] - **tls**: add trailing commas in source files (Antoine du Hamel) [#46715](https://github.com/nodejs/node/pull/46715)
- \[[`958da453dd`](https://github.com/nodejs/node/commit/958da453dd)] - **tools**: fix update-ada script (Yagiz Nizipli) [#46550](https://github.com/nodejs/node/pull/46550)
- \[[`a3f272e667`](https://github.com/nodejs/node/commit/a3f272e667)] - **tools**: automate cares update (Marco Ippolito) [#46993](https://github.com/nodejs/node/pull/46993)
- \[[`77f5b69840`](https://github.com/nodejs/node/commit/77f5b69840)] - **tools**: update lint-md-dependencies to rollup\@3.18.0 (Node.js GitHub Bot) [#46935](https://github.com/nodejs/node/pull/46935)
- \[[`36ea9e7f65`](https://github.com/nodejs/node/commit/36ea9e7f65)] - **tools**: refactor dep_updaters (Tony Gorez) [#46488](https://github.com/nodejs/node/pull/46488)
- \[[`c0b6794f82`](https://github.com/nodejs/node/commit/c0b6794f82)] - **tools**: update eslint to 8.35.0 (Node.js GitHub Bot) [#46854](https://github.com/nodejs/node/pull/46854)
- \[[`307d096e21`](https://github.com/nodejs/node/commit/307d096e21)] - **tools**: create llhttp update action (Marco Ippolito) [#46766](https://github.com/nodejs/node/pull/46766)
- \[[`fff31862ac`](https://github.com/nodejs/node/commit/fff31862ac)] - **tools**: fix linter message when using global `DOMException` (Antoine du Hamel) [#46822](https://github.com/nodejs/node/pull/46822)
- \[[`89c2e8ac97`](https://github.com/nodejs/node/commit/89c2e8ac97)] - **tools**: update lint-md-dependencies to rollup\@3.17.3 (Node.js GitHub Bot) [#46843](https://github.com/nodejs/node/pull/46843)
- \[[`3bf54ff624`](https://github.com/nodejs/node/commit/3bf54ff624)] - **tools**: update lint-md-dependencies to rollup\@3.17.2 (Node.js GitHub Bot) [#46771](https://github.com/nodejs/node/pull/46771)
- \[[`4065e5cda6`](https://github.com/nodejs/node/commit/4065e5cda6)] - **tools**: run format-cpp on node-api test c files (Chengzhong Wu) [#46694](https://github.com/nodejs/node/pull/46694)
- \[[`7349708527`](https://github.com/nodejs/node/commit/7349708527)] - **tools**: use actions pinned by commit hash in coverage-linux (Gabriela Gutierrez) [#46294](https://github.com/nodejs/node/pull/46294)
- \[[`f80e93d71f`](https://github.com/nodejs/node/commit/f80e93d71f)] - **tools**: fix regex strings in Python tools (Jan Osusky) [#46671](https://github.com/nodejs/node/pull/46671)
- \[[`8fd176055f`](https://github.com/nodejs/node/commit/8fd176055f)] - **tools**: fixed path (Marco Ippolito) [#46700](https://github.com/nodejs/node/pull/46700)
- \[[`15b0e8b1aa`](https://github.com/nodejs/node/commit/15b0e8b1aa)] - **tools**: update nghttp2 action (Marco Ippolito) [#46700](https://github.com/nodejs/node/pull/46700)
- \[[`4da3eecbd7`](https://github.com/nodejs/node/commit/4da3eecbd7)] - **tools**: update-nghttp2 preserve config.h (Marco Ippolito) [#46698](https://github.com/nodejs/node/pull/46698)
- \[[`3714d8f236`](https://github.com/nodejs/node/commit/3714d8f236)] - **tools**: update lint-md-dependencies to rollup\@3.17.1 (Node.js GitHub Bot) [#46712](https://github.com/nodejs/node/pull/46712)
- \[[`474084e063`](https://github.com/nodejs/node/commit/474084e063)] - **tools**: update lint-md-dependencies to rollup\@3.17.0 (Node.js GitHub Bot) [#46712](https://github.com/nodejs/node/pull/46712)
- \[[`665b7424eb`](https://github.com/nodejs/node/commit/665b7424eb)] - **tools**: fix bug in `prefer-primordials` lint rule (Antoine du Hamel) [#46659](https://github.com/nodejs/node/pull/46659)
- \[[`7eb1456892`](https://github.com/nodejs/node/commit/7eb1456892)] - **tools**: update eslint to 8.34.0 (Node.js GitHub Bot) [#46625](https://github.com/nodejs/node/pull/46625)
- \[[`678b8f53c1`](https://github.com/nodejs/node/commit/678b8f53c1)] - **tools**: update lint-md-dependencies to rollup\@3.15.0 to-vfile\@7.2.4 (Node.js GitHub Bot) [#46623](https://github.com/nodejs/node/pull/46623)
- \[[`ee81da5883`](https://github.com/nodejs/node/commit/ee81da5883)] - **tools**: update doc to remark-html\@15.0.2 to-vfile\@7.2.4 (Node.js GitHub Bot) [#46622](https://github.com/nodejs/node/pull/46622)
- \[[`92e3e0e4e4`](https://github.com/nodejs/node/commit/92e3e0e4e4)] - **tools**: update lint-md-dependencies to rollup\@3.13.0 vfile-reporter\@7.0.5 (Node.js GitHub Bot) [#46503](https://github.com/nodejs/node/pull/46503)
- \[[`728f96861e`](https://github.com/nodejs/node/commit/728f96861e)] - **tools**: update ESLint custom rules to not use the deprecated format (Antoine du Hamel) [#46460](https://github.com/nodejs/node/pull/46460)
- \[[`78b79c0220`](https://github.com/nodejs/node/commit/78b79c0220)] - **typings**: improve `primordials` typings (Antoine du Hamel) [#46970](https://github.com/nodejs/node/pull/46970)
- \[[`a39dd37653`](https://github.com/nodejs/node/commit/a39dd37653)] - **url**: simplify and improve url formatting (Yagiz Nizipli) [#46736](https://github.com/nodejs/node/pull/46736)
- \[[`01b6525706`](https://github.com/nodejs/node/commit/01b6525706)] - **url**: improve performance by removing host (Yagiz Nizipli) [#46547](https://github.com/nodejs/node/pull/46547)
- \[[`0c67a7ad57`](https://github.com/nodejs/node/commit/0c67a7ad57)] - **url**: fix url spec compliance issues (Yagiz Nizipli) [#46547](https://github.com/nodejs/node/pull/46547)
- \[[`e9263541a7`](https://github.com/nodejs/node/commit/e9263541a7)] - **url**: replace url-parser with ada (Yagiz Nizipli) [#46410](https://github.com/nodejs/node/pull/46410)
- \[[`4d0faf4ddc`](https://github.com/nodejs/node/commit/4d0faf4ddc)] - **url**: improve `isURLThis` detection (Yagiz Nizipli) [#46866](https://github.com/nodejs/node/pull/46866)
- \[[`1ece4a72b7`](https://github.com/nodejs/node/commit/1ece4a72b7)] - **(SEMVER-MINOR)** **url**: implement URLSearchParams size getter (James M Snell) [#46308](https://github.com/nodejs/node/pull/46308)
- \[[`8f14002459`](https://github.com/nodejs/node/commit/8f14002459)] - **url**: remove unused `URL::ToFilePath()` (Yagiz Nizipli) [#46487](https://github.com/nodejs/node/pull/46487)
- \[[`f57e7bcc11`](https://github.com/nodejs/node/commit/f57e7bcc11)] - **url**: remove unused `URL::toObject` (Yagiz Nizipli) [#46486](https://github.com/nodejs/node/pull/46486)
- \[[`04930562f8`](https://github.com/nodejs/node/commit/04930562f8)] - **url**: remove unused `setURLConstructor` function (Yagiz Nizipli) [#46485](https://github.com/nodejs/node/pull/46485)
- \[[`b801873a8a`](https://github.com/nodejs/node/commit/b801873a8a)] - **vm**: properly support symbols on globals (Nicolas DUBIEN) [#46458](https://github.com/nodejs/node/pull/46458)
- \[[`f7423bdbaa`](https://github.com/nodejs/node/commit/f7423bdbaa)] - **(SEMVER-MINOR)** **worker**: add support for worker name in inspector and trace_events (Debadree Chatterjee) [#46832](https://github.com/nodejs/node/pull/46832)

Windows 32-bit Installer: https://nodejs.org/dist/v18.16.0/node-v18.16.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v18.16.0/node-v18.16.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v18.16.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v18.16.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v18.16.0/node-v18.16.0.pkg \
macOS Apple Silicon 64-bit Binary: https://nodejs.org/dist/v18.16.0/node-v18.16.0-darwin-arm64.tar.gz \
macOS Intel 64-bit Binary: https://nodejs.org/dist/v18.16.0/node-v18.16.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v18.16.0/node-v18.16.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v18.16.0/node-v18.16.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v18.16.0/node-v18.16.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v18.16.0/node-v18.16.0-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v18.16.0/node-v18.16.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v18.16.0/node-v18.16.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v18.16.0/node-v18.16.0.tar.gz \
Other release files: https://nodejs.org/dist/v18.16.0/ \
Documentation: https://nodejs.org/docs/v18.16.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

a65bd3fe91ffeb31d12a208e811943e3ebba4706553a4845a03d857beaeec51e  node-v18.16.0-aix-ppc64.tar.gz
82c7bb4869419ce7338669e6739a786dfc7e72f276ffbed663f85ffc905dcdb4  node-v18.16.0-darwin-arm64.tar.gz
b23cdf4fa0e9f77273720ab18eabdd7691edbb69e08ec3b65afd69bef23fe209  node-v18.16.0-darwin-arm64.tar.xz
cd520da6e2e89fab881c66a3e9aff02cb0d61d68104b1d6a571dd71bef920870  node-v18.16.0-darwin-x64.tar.gz
2c8aa0333111c2411564bfb85be44186aeb581392f73c4be5912cbb125d99043  node-v18.16.0-darwin-x64.tar.xz
effeb73616e5297922ed89a1b94d2664390040a83184504c1cc1305b0c0c853f  node-v18.16.0-headers.tar.gz
0eb9823c2cc72792c2d4413f57b5a36232e173d7edefb1909c37e364a823f9c7  node-v18.16.0-headers.tar.xz
dc3dfaee899ed21682e47eaf15525f85aff29013c392490e9b25219cd95b1c35  node-v18.16.0-linux-arm64.tar.gz
c81dfa0bada232cb4583c44d171ea207934f7356f85f9184b32d0dde69e2e0ea  node-v18.16.0-linux-arm64.tar.xz
a3968db44e5ae17243d126ff79b1756016b198f7cc94c6fad8522aac481b4ff3  node-v18.16.0-linux-armv7l.tar.gz
57ba6b71eb039fa896c329e68669b21f6717622c560c6f61a0c97d18ca866b2d  node-v18.16.0-linux-armv7l.tar.xz
b4e66dcda5ba4a3697be3fded122dabb6a677deee3d7f4d3c7c13ebb5a13844c  node-v18.16.0-linux-ppc64le.tar.gz
c43142fb9ef30658620ed095f8203beca92f469c1121eeb724df9a48bf0e59a5  node-v18.16.0-linux-ppc64le.tar.xz
a8b607c3c06f585c4fe9ba45be6dc76ce9459238c91b3f43533aa30344caed87  node-v18.16.0-linux-s390x.tar.gz
39b15c16347000b0be97133437bde0317dd2307d3fdfce15ddd8680b07a963ef  node-v18.16.0-linux-s390x.tar.xz
fc83046a93d2189d919005a348db3b2372b598a145d84eb9781a3a4b0f032e95  node-v18.16.0-linux-x64.tar.gz
44d93d9b4627fe5ae343012d855491d62c7381b236c347f7666a7ad070f26548  node-v18.16.0-linux-x64.tar.xz
156aa5b9580288fb0b3c6134eb8fac64e50745d78d33eebe9e29eb7ff87b8e1e  node-v18.16.0.pkg
6a4f5c5d76e5c50cef673099e56f19bc3266ae363f56ca0ab77dd2f3c5088c6d  node-v18.16.0.tar.gz
33d81a233e235a509adda4a4f2209008d04591979de6b3f0f67c1c906093f118  node-v18.16.0.tar.xz
007848640ba414f32d968d303e75d9841ecd2cd95d6fdd81f80bc3dcbd74ae44  node-v18.16.0-win-x64.7z
4b3bd4cb5570cc217490639e93a7e1b7a7a341981366661e514ce61941824a85  node-v18.16.0-win-x64.zip
681be28e0acd057b4798f357d21eec5f49e21bc803bbbefeb1072bb4f166025a  node-v18.16.0-win-x86.7z
2a7e0fb22e1a36144ee8183c80ef2705cd9754c1d894f94bb6c94a681de47924  node-v18.16.0-win-x86.zip
5bfb6f3ab89e198539408f7e0e8ec0b0bd5efe8898573ec05b381228efb45a5d  node-v18.16.0-x64.msi
09534d1949c795c3e49d257fb72a9fd865ee28955673b87d569d4aec541333e7  node-v18.16.0-x86.msi
b548a55c2b5ef5de34f4636610bab27077fb9313d34f52280b9ec11dd25e9dd1  win-x64/node.exe
72b7fab9381af8f4958c8212f3d4cdfff8c7c5b1e33eaad0e7d5888293568cd5  win-x64/node.lib
3b9474e18a1bbb38b05b1876b4b37056063c2af82212d356a8a5cf91c1a3acf3  win-x64/node_pdb.7z
6b506b1fe654ca7161373916c7ba7e38f62545236698342fa97fd2faf39ebc4e  win-x64/node_pdb.zip
36bf0f0a364ca8edc176776764831f9e88bef6d1e8056f6edc474a37b652a794  win-x86/node.exe
6a85c15a69238f0902b9a734d262bf36d211b273a46d5e3249857d4bb7f6d9b7  win-x86/node.lib
9256bdefae4491acfd523ca06d4f4344ddc4f1a28aac868b5efb6a72d8023e2a  win-x86/node_pdb.7z
53c6b29afd58904e5143d9f3298b55695b8ecb2b6c08a9612ed30e9b0ed9589a  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEdPEmArbxxOkT+qN606iWE2Q7YgEFAmQ3TroACgkQ06iWE2Q7
YgFY0Q/+KCSvLgss6JvOm5aLpg6yEZqRk2zAuVwpXJaRhWTR+vK4Vs3Zc0/Tld59
QVFbVfXrh8OMSFkQ1kcT4uKEtD9VT12hLMF+pdxazHyDI/yabfQabflf+zk+xMOh
zUy9nDPjqJOB96f8mvUEsJZl4Ixr6NNVWd1J2x55ZFC2ddXdzNaiY8Zij8SM2JjQ
V36OeFvsOSVopr9zFWyfCm6jLLvjtOdxMJMd+x9+B9JxIGu+QeJOTmgIMzHBSGac
mL2OKfy/oS+a27HUe2/FdhEUwaxZCoZ33N1ku5WMzPhcE2UXH5Q6kTNP0rRy35Nj
kNb7x82xXWbNWan8vAVXpn1ancwkPfADW/3KpFPn7mFVelaRYbY7Xu+ZTSho/fax
xcpOINsg2vSBPVX7nFu9w9/qaWUkBbGkrilCMXUtXyC4buORQsFXswyFnsguJfK5
FiQ7N46/9FVwNAd7bj3/0thCKZaJGaXmmk/S+gSichpzptukRpDcvT8O3gYewWio
RuxYimqeZ7n4YCOxG41ewvdluH0E9c9nFUAd4v+yxHU2LP/XLjhEY2kFW2jI3yoI
oCmYolVDFYJ/zS0Vu3L1CegSzl7q0hsSPuTjUrgq0qjaTgRkMGCEBeyqS9M3Zx+g
M2tYiU/JXjtPySwkx4keXy7tABR/pcfs/gseHw41pmi/BmPjgMc=
=UugX
-----END PGP SIGNATURE-----

```
