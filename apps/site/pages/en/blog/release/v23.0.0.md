---
date: '2024-10-16T14:07:50.232Z'
category: release
title: Node v23.0.0 (Current)
layout: blog-post
author: <PERSON>
---

## 2024-10-16, Version 23.0.0 (Current), @RafaelGSS

We’re excited to announce the release of Node.js 23! Key highlights include:

- Enabling `require(esm)` by default for Node.js applications
- Removing support for Windows 32-bit systems
- Stabilizing the `node --run` command
- Enhancements to the test runner, including glob pattern support for coverage files

Node.js 23 will replace Node.js 22 as the ‘Current’ release line when Node.js 22 enters long-term support (LTS) later this month.
According to the release schedule, Node.js 23 will remain the ‘Current’ release for the next six months, until April 2025.

### `require(esm)` is now enabled by default

Support for loading native ES modules using `require()` had been available on v20.x and v22.x under the command line flag `--experimental-require-module`. In v23.x, this feature is now enabled by default. This feature is still experimental, and we are looking for user feedback to make more final tweaks before fully stabilizing it. For this reason, on v23.x, when the Node.js instance encounters a native ES module in `require()` for the first time, it will emit an experimental warning.

If there happens to be any regressions caused by this feature, users can report it to the Node.js issue tracker. Meanwhile this feature can also be disabled using `--no-experimental-require-module` as a workaround. We expect to test this feature using v23.x, find out any potential regressions and make adjustments accordingly, before backporting the unflagging to v22.x. This will likely happen in a later semver-minor release after the the LTS promotion at the end of October.

With this feature enabled, Node.js will no longer throw `ERR_REQUIRE_ESM` if `require()` is used to load a ES module. It can, however, throw `ERR_REQUIRE_ASYNC_MODULE` if the ES module being loaded or its dependencies contain top-level `await`. When the ES module is loaded successfully by `require()`, the returned object will be a ES module namespace object similar to what's returned by `import()`, and can be checked using `util.isModuleNamespaceObject()`.

Users can check `process.features.require_module` to see whether `require(esm)` is enabled in the current Node.js instance. For packages, the `"module-sync"` exports condition can be used as a way to detect `require(esm)` support in the current Node.js instance and allow both `require()` and `import` to load the same native ES module. See [the documentation](https://nodejs.org/docs/latest/api/modules.html#loading-ecmascript-modules-using-require) for more details about this feature.

### Other Notable Changes

- \[[`7ad0cc3e57`](https://github.com/nodejs/node/commit/7ad0cc3e57)] - **(SEMVER-MAJOR)** **build**: remove support for 32-bit Windows (Michaël Zasso) [#53184](https://github.com/nodejs/node/pull/53184)
- \[[`83eb4f2855`](https://github.com/nodejs/node/commit/83eb4f2855)] - **(SEMVER-MINOR)** **deps**: V8: cherry-pick cd10ad7cdbe5 (Joyee Cheung) [#52535](https://github.com/nodejs/node/pull/52535)
- \[[`b8493a5789`](https://github.com/nodejs/node/commit/b8493a5789)] - **doc**: add abmusse to collaborators (Abdirahim Musse) [#55086](https://github.com/nodejs/node/pull/55086)
- \[[`7fab6e8885`](https://github.com/nodejs/node/commit/7fab6e8885)] - **(SEMVER-MAJOR)** **doc**: use gcc 12 on AIX for Node.js >=23 (Richard Lau) [#54338](https://github.com/nodejs/node/pull/54338)
- \[[`d473606040`](https://github.com/nodejs/node/commit/d473606040)] - **(SEMVER-MINOR)** **lib**: propagate aborted state to dependent signals before firing events (jazelly) [#54826](https://github.com/nodejs/node/pull/54826)
- \[[`06206af181`](https://github.com/nodejs/node/commit/06206af181)] - **(SEMVER-MINOR)** **module**: unflag --experimental-require-module (Joyee Cheung) [#55085](https://github.com/nodejs/node/pull/55085)
- \[[`0b9249e335`](https://github.com/nodejs/node/commit/0b9249e335)] - **(SEMVER-MINOR)** **module**: implement the "module-sync" exports condition (Joyee Cheung) [#54648](https://github.com/nodejs/node/pull/54648)
- \[[`92a25abca9`](https://github.com/nodejs/node/commit/92a25abca9)] - **(SEMVER-MINOR)** **path**: add `matchGlob` method (Aviv Keller) [#52881](https://github.com/nodejs/node/pull/52881)
- \[[`12dd4c7575`](https://github.com/nodejs/node/commit/12dd4c7575)] - **src**: mark node --run as stable (Yagiz Nizipli) [#53763](https://github.com/nodejs/node/pull/53763)
- \[[`4174b73153`](https://github.com/nodejs/node/commit/4174b73153)] - **test**: support glob matching coverage files (Aviv Keller) [#53553](https://github.com/nodejs/node/pull/53553)

### Semver-Major Commits

- \[[`764b13d75c`](https://github.com/nodejs/node/commit/764b13d75c)] - **(SEMVER-MAJOR)** **assert,util**: change WeakMap and WeakSet comparison handling (Cristian Barlutiu) [#53495](https://github.com/nodejs/node/pull/53495)
- \[[`3800d60c66`](https://github.com/nodejs/node/commit/3800d60c66)] - **(SEMVER-MAJOR)** **buffer**: throw when writing beyond buffer" (Robert Nagy) [#54588](https://github.com/nodejs/node/pull/54588)
- \[[`17fd32790a`](https://github.com/nodejs/node/commit/17fd32790a)] - **(SEMVER-MAJOR)** **buffer**: make File cloneable (Matthew Aitken) [#47613](https://github.com/nodejs/node/pull/47613)
- \[[`f68d7d2acc`](https://github.com/nodejs/node/commit/f68d7d2acc)] - **(SEMVER-MAJOR)** **build**: reset embedder string to "-node.0" (Michaël Zasso) [#54536](https://github.com/nodejs/node/pull/54536)
- \[[`9d0748c5df`](https://github.com/nodejs/node/commit/9d0748c5df)] - **(SEMVER-MAJOR)** **build**: disable ICF for mksnapshot (Leszek Swirski) [#54077](https://github.com/nodejs/node/pull/54077)
- \[[`b7bcf3e121`](https://github.com/nodejs/node/commit/b7bcf3e121)] - **(SEMVER-MAJOR)** **build**: include v8-sandbox.h header in distribution (Michaël Zasso) [#54077](https://github.com/nodejs/node/pull/54077)
- \[[`1dfa3b8255`](https://github.com/nodejs/node/commit/1dfa3b8255)] - **(SEMVER-MAJOR)** **build**: reset embedder string to "-node.0" (Michaël Zasso) [#54077](https://github.com/nodejs/node/pull/54077)
- \[[`046343ea9d`](https://github.com/nodejs/node/commit/046343ea9d)] - **(SEMVER-MAJOR)** **build**: warn for GCC versions earlier than 12.2 (Michaël Zasso) [#54081](https://github.com/nodejs/node/pull/54081)
- \[[`a5decd4c8d`](https://github.com/nodejs/node/commit/a5decd4c8d)] - **(SEMVER-MAJOR)** **build**: drop experimental support for Windows <10 (Michaël Zasso) [#54079](https://github.com/nodejs/node/pull/54079)
- \[[`7ad0cc3e57`](https://github.com/nodejs/node/commit/7ad0cc3e57)] - **(SEMVER-MAJOR)** **build**: remove support for 32-bit Windows (Michaël Zasso) [#53184](https://github.com/nodejs/node/pull/53184)
- \[[`c7e42092f3`](https://github.com/nodejs/node/commit/c7e42092f3)] - **(SEMVER-MAJOR)** **build**: compile with C++20 support (Michaël Zasso) [#45427](https://github.com/nodejs/node/pull/45427)
- \[[`e2b7e41e23`](https://github.com/nodejs/node/commit/e2b7e41e23)] - **(SEMVER-MAJOR)** **child_process**: remove unused internal event (Rich Trott) [#53793](https://github.com/nodejs/node/pull/53793)
- \[[`4f1fe8a015`](https://github.com/nodejs/node/commit/4f1fe8a015)] - **(SEMVER-MAJOR)** **cli**: remove deprecated V8 flag (Omer Katz) [#54761](https://github.com/nodejs/node/pull/54761)
- \[[`8f37492b65`](https://github.com/nodejs/node/commit/8f37492b65)] - **(SEMVER-MAJOR)** **cli**: move --trace-atomics-wait to eol (Marco Ippolito) [#52747](https://github.com/nodejs/node/pull/52747)
- \[[`f7e73cd1f2`](https://github.com/nodejs/node/commit/f7e73cd1f2)] - **(SEMVER-MAJOR)** **cli**: remove --no-experimental-global-customevent flag (Daeyeon Jeong) [#52723](https://github.com/nodejs/node/pull/52723)
- \[[`311504125f`](https://github.com/nodejs/node/commit/311504125f)] - **(SEMVER-MAJOR)** **cli**: remove --no-experimental-fetch flag (Filip Skokan) [#52611](https://github.com/nodejs/node/pull/52611)
- \[[`a30ae50860`](https://github.com/nodejs/node/commit/a30ae50860)] - **(SEMVER-MAJOR)** **cli**: remove --no-experimental-global-webcrypto flag (Filip Skokan) [#52564](https://github.com/nodejs/node/pull/52564)
- \[[`afe56aa58b`](https://github.com/nodejs/node/commit/afe56aa58b)] - **(SEMVER-MAJOR)** **crypto**: runtime deprecate crypto.fips (Yagiz Nizipli) [#55019](https://github.com/nodejs/node/pull/55019)
- \[[`33a6d1fe3a`](https://github.com/nodejs/node/commit/33a6d1fe3a)] - **(SEMVER-MAJOR)** **crypto**: remove ERR_CRYPTO_SCRYPT_INVALID_PARAMETER (Tobias Nießen) [#53305](https://github.com/nodejs/node/pull/53305)
- \[[`ff826069a8`](https://github.com/nodejs/node/commit/ff826069a8)] - **(SEMVER-MAJOR)** **crypto**: move DEP0182 to runtime deprecation (Tobias Nießen) [#52552](https://github.com/nodejs/node/pull/52552)
- \[[`6e150f9527`](https://github.com/nodejs/node/commit/6e150f9527)] - **(SEMVER-MAJOR)** **deps**: V8: cherry-pick 97199f686e2f (Michaël Zasso) [#54536](https://github.com/nodejs/node/pull/54536)
- \[[`1e16779fa1`](https://github.com/nodejs/node/commit/1e16779fa1)] - **(SEMVER-MAJOR)** **deps**: V8: cherry-pick 01a47f3ffff2 (Michaël Zasso) [#54536](https://github.com/nodejs/node/pull/54536)
- \[[`762a440e68`](https://github.com/nodejs/node/commit/762a440e68)] - **(SEMVER-MAJOR)** **deps**: patch V8 to support older Clang versions (Michaël Zasso) [#54536](https://github.com/nodejs/node/pull/54536)
- \[[`95f2213eed`](https://github.com/nodejs/node/commit/95f2213eed)] - **(SEMVER-MAJOR)** **deps**: always define V8_NODISCARD as no-op (Michaël Zasso) [#54536](https://github.com/nodejs/node/pull/54536)
- \[[`09d997f181`](https://github.com/nodejs/node/commit/09d997f181)] - **(SEMVER-MAJOR)** **deps**: fix FP16 bitcasts.h (Stefan Stojanovic) [#54536](https://github.com/nodejs/node/pull/54536)
- \[[`1866363854`](https://github.com/nodejs/node/commit/1866363854)] - **(SEMVER-MAJOR)** **deps**: patch V8 to support compilation with MSVC (StefanStojanovic) [#54536](https://github.com/nodejs/node/pull/54536)
- \[[`6f4f22f84c`](https://github.com/nodejs/node/commit/6f4f22f84c)] - **(SEMVER-MAJOR)** **deps**: patch V8 to avoid duplicated zlib symbol (Michaël Zasso) [#54536](https://github.com/nodejs/node/pull/54536)
- \[[`dfff61475e`](https://github.com/nodejs/node/commit/dfff61475e)] - **(SEMVER-MAJOR)** **deps**: disable V8 concurrent sparkplug compilation (Michaël Zasso) [#54536](https://github.com/nodejs/node/pull/54536)
- \[[`69ad89f8eb`](https://github.com/nodejs/node/commit/69ad89f8eb)] - **(SEMVER-MAJOR)** **deps**: always define V8_EXPORT_PRIVATE as no-op (Michaël Zasso) [#54536](https://github.com/nodejs/node/pull/54536)
- \[[`5ab3140dfb`](https://github.com/nodejs/node/commit/5ab3140dfb)] - **(SEMVER-MAJOR)** **deps**: update V8 to 12.9.202.18 (Michaël Zasso) [#54536](https://github.com/nodejs/node/pull/54536)
- \[[`fba06eb34a`](https://github.com/nodejs/node/commit/fba06eb34a)] - **(SEMVER-MAJOR)** **deps**: remove bogus V8 DCHECK (Michaël Zasso) [#54077](https://github.com/nodejs/node/pull/54077)
- \[[`5355603fb5`](https://github.com/nodejs/node/commit/5355603fb5)] - **(SEMVER-MAJOR)** **deps**: V8: cherry-pick 00e9eeb3fb2c (Michaël Zasso) [#54077](https://github.com/nodejs/node/pull/54077)
- \[[`bcc1e2716c`](https://github.com/nodejs/node/commit/bcc1e2716c)] - **(SEMVER-MAJOR)** **deps**: V8: cherry-pick b1397772c70c (Michaël Zasso) [#54077](https://github.com/nodejs/node/pull/54077)
- \[[`415bc750a5`](https://github.com/nodejs/node/commit/415bc750a5)] - **(SEMVER-MAJOR)** **deps**: V8: cherry-pick 35888fee7bba (Joyee Cheung) [#54077](https://github.com/nodejs/node/pull/54077)
- \[[`28f3e5c9d1`](https://github.com/nodejs/node/commit/28f3e5c9d1)] - **(SEMVER-MAJOR)** **deps**: always define V8_NODISCARD as no-op (Michaël Zasso) [#54077](https://github.com/nodejs/node/pull/54077)
- \[[`a41c381cde`](https://github.com/nodejs/node/commit/a41c381cde)] - **(SEMVER-MAJOR)** **deps**: fix FP16 bitcasts.h (Stefan Stojanovic) [#54077](https://github.com/nodejs/node/pull/54077)
- \[[`16c9348e60`](https://github.com/nodejs/node/commit/16c9348e60)] - **(SEMVER-MAJOR)** **deps**: V8: revert CL 5331688 (Michaël Zasso) [#54077](https://github.com/nodejs/node/pull/54077)
- \[[`dc4e702a45`](https://github.com/nodejs/node/commit/dc4e702a45)] - **(SEMVER-MAJOR)** **deps**: patch V8 to support compilation with MSVC (StefanStojanovic) [#54077](https://github.com/nodejs/node/pull/54077)
- \[[`f626acc328`](https://github.com/nodejs/node/commit/f626acc328)] - **(SEMVER-MAJOR)** **deps**: silence internal V8 deprecation warning (Michaël Zasso) [#54077](https://github.com/nodejs/node/pull/54077)
- \[[`ed187faa64`](https://github.com/nodejs/node/commit/ed187faa64)] - **(SEMVER-MAJOR)** **deps**: patch V8 to avoid duplicated zlib symbol (Michaël Zasso) [#54077](https://github.com/nodejs/node/pull/54077)
- \[[`ed029bded7`](https://github.com/nodejs/node/commit/ed029bded7)] - **(SEMVER-MAJOR)** **deps**: avoid compilation error with ASan (Michaël Zasso) [#54077](https://github.com/nodejs/node/pull/54077)
- \[[`e600de93cf`](https://github.com/nodejs/node/commit/e600de93cf)] - **(SEMVER-MAJOR)** **deps**: disable V8 concurrent sparkplug compilation (Michaël Zasso) [#54077](https://github.com/nodejs/node/pull/54077)
- \[[`cc36db7c06`](https://github.com/nodejs/node/commit/cc36db7c06)] - **(SEMVER-MAJOR)** **deps**: always define V8_EXPORT_PRIVATE as no-op (Michaël Zasso) [#54077](https://github.com/nodejs/node/pull/54077)
- \[[`9d7cd9b864`](https://github.com/nodejs/node/commit/9d7cd9b864)] - **(SEMVER-MAJOR)** **deps**: update V8 to 12.8.374.13 (Michaël Zasso) [#54077](https://github.com/nodejs/node/pull/54077)
- \[[`4f70132972`](https://github.com/nodejs/node/commit/4f70132972)] - **(SEMVER-MAJOR)** **doc**: reflect toolchains used for official binaries (Richard Lau) [#54967](https://github.com/nodejs/node/pull/54967)
- \[[`7fab6e8885`](https://github.com/nodejs/node/commit/7fab6e8885)] - **(SEMVER-MAJOR)** **doc**: use gcc 12 on AIX for Node.js >=23 (Richard Lau) [#54338](https://github.com/nodejs/node/pull/54338)
- \[[`1d5ed725e9`](https://github.com/nodejs/node/commit/1d5ed725e9)] - **(SEMVER-MAJOR)** **esm**: export 'module.exports' on ESM CJS wrapper (Guy Bedford) [#53848](https://github.com/nodejs/node/pull/53848)
- \[[`d5c29ba12d`](https://github.com/nodejs/node/commit/d5c29ba12d)] - **(SEMVER-MAJOR)** **events**: set EventEmitterAsyncResource fields private (Yagiz Nizipli) [#54889](https://github.com/nodejs/node/pull/54889)
- \[[`f202322ea4`](https://github.com/nodejs/node/commit/f202322ea4)] - **(SEMVER-MAJOR)** **fs**: adjust typecheck for `type` in `fs.symlink()` (Livia Medeiros) [#49741](https://github.com/nodejs/node/pull/49741)
- \[[`15e7563062`](https://github.com/nodejs/node/commit/15e7563062)] - **(SEMVER-MAJOR)** **fs**: runtime deprecate `dirent.path` (Antoine du Hamel) [#51050](https://github.com/nodejs/node/pull/51050)
- \[[`00b2f07f9d`](https://github.com/nodejs/node/commit/00b2f07f9d)] - **(SEMVER-MAJOR)** **fs,win**: fix bug in paths with trailing slashes (Hüseyin Açacak) [#54160](https://github.com/nodejs/node/pull/54160)
- \[[`e973c3e94b`](https://github.com/nodejs/node/commit/e973c3e94b)] - **(SEMVER-MAJOR)** **lib**: validate signals with interface converter (Jason Zhang) [#54965](https://github.com/nodejs/node/pull/54965)
- \[[`a5a946d8a5`](https://github.com/nodejs/node/commit/a5a946d8a5)] - **(SEMVER-MAJOR)** **lib**: implement interface converter in webidl (Jason Zhang) [#54965](https://github.com/nodejs/node/pull/54965)
- \[[`6ed93b4d69`](https://github.com/nodejs/node/commit/6ed93b4d69)] - **(SEMVER-MAJOR)** **lib**: expose global CloseEvent (Matthew Aitken) [#53355](https://github.com/nodejs/node/pull/53355)
- \[[`52322aa42a`](https://github.com/nodejs/node/commit/52322aa42a)] - **(SEMVER-MAJOR)** **net**: validate host name for server listen (Jason Zhang) [#54470](https://github.com/nodejs/node/pull/54470)
- \[[`efbba60e5b`](https://github.com/nodejs/node/commit/efbba60e5b)] - **(SEMVER-MAJOR)** **path**: fix bugs and inconsistencies (Hüseyin Açacak) [#54224](https://github.com/nodejs/node/pull/54224)
- \[[`c237eabf4c`](https://github.com/nodejs/node/commit/c237eabf4c)] - **(SEMVER-MAJOR)** **process**: remove `process.assert` (Aviv Keller) [#55035](https://github.com/nodejs/node/pull/55035)
- \[[`17a17164d6`](https://github.com/nodejs/node/commit/17a17164d6)] - **(SEMVER-MAJOR)** **src**: update NODE_MODULE_VERSION to 131 (Michaël Zasso) [#54536](https://github.com/nodejs/node/pull/54536)
- \[[`f0134fa6c3`](https://github.com/nodejs/node/commit/f0134fa6c3)] - **(SEMVER-MAJOR)** **src**: stop using deprecated fields of `v8::FastApiCallbackOptions` (Andreas Haas) [#54077](https://github.com/nodejs/node/pull/54077)
- \[[`0be79f4deb`](https://github.com/nodejs/node/commit/0be79f4deb)] - **(SEMVER-MAJOR)** **src**: remove dependency on wrapper-descriptor-based CppHeap (Joyee Cheung) [#54077](https://github.com/nodejs/node/pull/54077)
- \[[`525b3f22d1`](https://github.com/nodejs/node/commit/525b3f22d1)] - **(SEMVER-MAJOR)** **src**: add source location to v8::TaskRunner (François Doray) [#54077](https://github.com/nodejs/node/pull/54077)
- \[[`e945bd9525`](https://github.com/nodejs/node/commit/e945bd9525)] - **(SEMVER-MAJOR)** **src**: update NODE_MODULE_VERSION to 129 (Michaël Zasso) [#54077](https://github.com/nodejs/node/pull/54077)
- \[[`bb8d2936ab`](https://github.com/nodejs/node/commit/bb8d2936ab)] - **(SEMVER-MAJOR)** **src**: do not use soon-to-be-deprecated V8 API (Igor Sheludko) [#53174](https://github.com/nodejs/node/pull/53174)
- \[[`75884678d7`](https://github.com/nodejs/node/commit/75884678d7)] - **(SEMVER-MAJOR)** **src**: add UV_PIPE_NO_TRUNCATE for bind in pipe_wrap.cc (theanarkh) [#52347](https://github.com/nodejs/node/pull/52347)
- \[[`922feb1ff5`](https://github.com/nodejs/node/commit/922feb1ff5)] - **(SEMVER-MAJOR)** **stream**: pipe to a closed or destroyed stream is not allowed in pipeline (jakecastelli) [#53241](https://github.com/nodejs/node/pull/53241)
- \[[`ffe0dc5b87`](https://github.com/nodejs/node/commit/ffe0dc5b87)] - **(SEMVER-MAJOR)** **string_decoder**: refactor encoding validation (Yagiz Nizipli) [#54957](https://github.com/nodejs/node/pull/54957)
- \[[`df9efba2ce`](https://github.com/nodejs/node/commit/df9efba2ce)] - **(SEMVER-MAJOR)** **test**: update v8-stats test for V8 12.6 (Michaël Zasso) [#54077](https://github.com/nodejs/node/pull/54077)
- \[[`dbaef339aa`](https://github.com/nodejs/node/commit/dbaef339aa)] - **(SEMVER-MAJOR)** **test_runner**: detect only tests when --test is not used (Colin Ihrig) [#54881](https://github.com/nodejs/node/pull/54881)
- \[[`eb7e18fe94`](https://github.com/nodejs/node/commit/eb7e18fe94)] - **(SEMVER-MAJOR)** **test_runner**: always make spec the default reporter (Colin Ihrig) [#54548](https://github.com/nodejs/node/pull/54548)
- \[[`0db38f0f99`](https://github.com/nodejs/node/commit/0db38f0f99)] - **(SEMVER-MAJOR)** **test_runner**: expose lcov reporter as newable function (Chemi Atlow) [#52403](https://github.com/nodejs/node/pull/52403)
- \[[`f5ed3386fd`](https://github.com/nodejs/node/commit/f5ed3386fd)] - **(SEMVER-MAJOR)** **timers**: emit warning if delay is negative or NaN (jakecastelli) [#46678](https://github.com/nodejs/node/pull/46678)
- \[[`f666a1b754`](https://github.com/nodejs/node/commit/f666a1b754)] - **(SEMVER-MAJOR)** **tls**: fix 'ERR_TLS_PSK_SET_IDENTIY_HINT_FAILED' typo (Aviv Keller) [#52627](https://github.com/nodejs/node/pull/52627)
- \[[`c8c108f9b0`](https://github.com/nodejs/node/commit/c8c108f9b0)] - **(SEMVER-MAJOR)** **tools**: add additonal include dirs for V8 on AIX (Abdirahim Musse) [#54536](https://github.com/nodejs/node/pull/54536)
- \[[`64e8646618`](https://github.com/nodejs/node/commit/64e8646618)] - **(SEMVER-MAJOR)** **tools**: update V8 gypfiles for 12.8 (Michaël Zasso) [#54077](https://github.com/nodejs/node/pull/54077)
- \[[`dc352a5ff2`](https://github.com/nodejs/node/commit/dc352a5ff2)] - **(SEMVER-MAJOR)** **tools**: update V8 gypfiles for 12.7 (Richard Lau) [#54077](https://github.com/nodejs/node/pull/54077)
- \[[`8044051ce3`](https://github.com/nodejs/node/commit/8044051ce3)] - **(SEMVER-MAJOR)** **tools**: update V8 gypfiles for 12.6 (Michaël Zasso) [#54077](https://github.com/nodejs/node/pull/54077)
- \[[`982f6ad516`](https://github.com/nodejs/node/commit/982f6ad516)] - **(SEMVER-MAJOR)** **util**: move util.log to eol (marco-ippolito) [#52744](https://github.com/nodejs/node/pull/52744)
- \[[`1d817dcb52`](https://github.com/nodejs/node/commit/1d817dcb52)] - **(SEMVER-MAJOR)** **util**: move util.isPrimitive to eol (marco-ippolito) [#52744](https://github.com/nodejs/node/pull/52744)
- \[[`72240942ed`](https://github.com/nodejs/node/commit/72240942ed)] - **(SEMVER-MAJOR)** **util**: move util.isFunction to eol (marco-ippolito) [#52744](https://github.com/nodejs/node/pull/52744)
- \[[`dc379626ab`](https://github.com/nodejs/node/commit/dc379626ab)] - **(SEMVER-MAJOR)** **util**: move util.isError to eol (marco-ippolito) [#52744](https://github.com/nodejs/node/pull/52744)
- \[[`b5cae4fea6`](https://github.com/nodejs/node/commit/b5cae4fea6)] - **(SEMVER-MAJOR)** **util**: move util.isDate to eol (marco-ippolito) [#52744](https://github.com/nodejs/node/pull/52744)
- \[[`bd559e3e5a`](https://github.com/nodejs/node/commit/bd559e3e5a)] - **(SEMVER-MAJOR)** **util**: move util.isObject to eol (marco-ippolito) [#52744](https://github.com/nodejs/node/pull/52744)
- \[[`d3068b9cfa`](https://github.com/nodejs/node/commit/d3068b9cfa)] - **(SEMVER-MAJOR)** **util**: move util.isRegExp to eol (marco-ippolito) [#52744](https://github.com/nodejs/node/pull/52744)
- \[[`a59c7aeb27`](https://github.com/nodejs/node/commit/a59c7aeb27)] - **(SEMVER-MAJOR)** **util**: move util.isUndefined to eol (marco-ippolito) [#52744](https://github.com/nodejs/node/pull/52744)
- \[[`05e72c939a`](https://github.com/nodejs/node/commit/05e72c939a)] - **(SEMVER-MAJOR)** **util**: move util.isSymbol to eol (marco-ippolito) [#52744](https://github.com/nodejs/node/pull/52744)
- \[[`832a77c003`](https://github.com/nodejs/node/commit/832a77c003)] - **(SEMVER-MAJOR)** **util**: move util.isString to eol (marco-ippolito) [#52744](https://github.com/nodejs/node/pull/52744)
- \[[`708f57ea49`](https://github.com/nodejs/node/commit/708f57ea49)] - **(SEMVER-MAJOR)** **util**: move util.isNumber to eol (marco-ippolito) [#52744](https://github.com/nodejs/node/pull/52744)
- \[[`6ec403fe91`](https://github.com/nodejs/node/commit/6ec403fe91)] - **(SEMVER-MAJOR)** **util**: move util.isNullOrUndefined to eol (marco-ippolito) [#52744](https://github.com/nodejs/node/pull/52744)
- \[[`7cd8bb26d1`](https://github.com/nodejs/node/commit/7cd8bb26d1)] - **(SEMVER-MAJOR)** **util**: move util.isNull to eol (marco-ippolito) [#52744](https://github.com/nodejs/node/pull/52744)
- \[[`e32b0c1eab`](https://github.com/nodejs/node/commit/e32b0c1eab)] - **(SEMVER-MAJOR)** **util**: move util.isBuffer to eol (marco-ippolito) [#52744](https://github.com/nodejs/node/pull/52744)
- \[[`be528ab11e`](https://github.com/nodejs/node/commit/be528ab11e)] - **(SEMVER-MAJOR)** **util**: move util.isBoolean to eol (marco-ippolito) [#52744](https://github.com/nodejs/node/pull/52744)
- \[[`ac97a532f5`](https://github.com/nodejs/node/commit/ac97a532f5)] - **(SEMVER-MAJOR)** **util**: move util.\_extend to eol (marco-ippolito) [#52744](https://github.com/nodejs/node/pull/52744)
- \[[`e225f00034`](https://github.com/nodejs/node/commit/e225f00034)] - **(SEMVER-MAJOR)** **zlib**: remove `zlib.bytesRead` (Yagiz Nizipli) [#55020](https://github.com/nodejs/node/pull/55020)

### Semver-Minor Commits

- \[[`90e3e5e173`](https://github.com/nodejs/node/commit/90e3e5e173)] - **(SEMVER-MINOR)** **crypto**: add KeyObject.prototype.toCryptoKey (Filip Skokan) [#55262](https://github.com/nodejs/node/pull/55262)
- \[[`29f31c6a76`](https://github.com/nodejs/node/commit/29f31c6a76)] - **(SEMVER-MINOR)** **crypto**: add Date fields for `validTo` and `validFrom` (Andrew Moon) [#54159](https://github.com/nodejs/node/pull/54159)
- \[[`83eb4f2855`](https://github.com/nodejs/node/commit/83eb4f2855)] - **(SEMVER-MINOR)** **deps**: V8: cherry-pick cd10ad7cdbe5 (Joyee Cheung) [#52535](https://github.com/nodejs/node/pull/52535)
- \[[`6c6562ce8b`](https://github.com/nodejs/node/commit/6c6562ce8b)] - **(SEMVER-MINOR)** **http2**: expose nghttp2_option_set_stream_reset_rate_limit as an option (Maël Nison) [#54875](https://github.com/nodejs/node/pull/54875)
- \[[`d473606040`](https://github.com/nodejs/node/commit/d473606040)] - **(SEMVER-MINOR)** **lib**: propagate aborted state to dependent signals before firing events (jazelly) [#54826](https://github.com/nodejs/node/pull/54826)
- \[[`772b35bdc4`](https://github.com/nodejs/node/commit/772b35bdc4)] - **(SEMVER-MINOR)** **module**: support loading entrypoint as url (RedYetiDev) [#54933](https://github.com/nodejs/node/pull/54933)
- \[[`06206af181`](https://github.com/nodejs/node/commit/06206af181)] - **(SEMVER-MINOR)** **module**: unflag --experimental-require-module (Joyee Cheung) [#55085](https://github.com/nodejs/node/pull/55085)
- \[[`0b9249e335`](https://github.com/nodejs/node/commit/0b9249e335)] - **(SEMVER-MINOR)** **module**: implement the "module-sync" exports condition (Joyee Cheung) [#54648](https://github.com/nodejs/node/pull/54648)
- \[[`62383cd113`](https://github.com/nodejs/node/commit/62383cd113)] - **(SEMVER-MINOR)** **module**: implement flushCompileCache() (Joyee Cheung) [#54971](https://github.com/nodejs/node/pull/54971)
- \[[`4dfed556ba`](https://github.com/nodejs/node/commit/4dfed556ba)] - **(SEMVER-MINOR)** **module**: throw when invalid argument is passed to enableCompileCache() (Joyee Cheung) [#54971](https://github.com/nodejs/node/pull/54971)
- \[[`9a73aa0d15`](https://github.com/nodejs/node/commit/9a73aa0d15)] - **(SEMVER-MINOR)** **module**: write compile cache to temporary file and then rename it (Joyee Cheung) [#54971](https://github.com/nodejs/node/pull/54971)
- \[[`92a25abca9`](https://github.com/nodejs/node/commit/92a25abca9)] - **(SEMVER-MINOR)** **path**: add `matchGlob` method (Aviv Keller) [#52881](https://github.com/nodejs/node/pull/52881)
- \[[`b0f025208f`](https://github.com/nodejs/node/commit/b0f025208f)] - **(SEMVER-MINOR)** **process**: add process.features.require_module (Joyee Cheung) [#55241](https://github.com/nodejs/node/pull/55241)
- \[[`bf11e5793b`](https://github.com/nodejs/node/commit/bf11e5793b)] - **(SEMVER-MINOR)** **test_runner**: support custom arguments in `run()` (Aviv Keller) [#55126](https://github.com/nodejs/node/pull/55126)
- \[[`059e08bb21`](https://github.com/nodejs/node/commit/059e08bb21)] - **(SEMVER-MINOR)** **test_runner**: add 'test:summary' event (Colin Ihrig) [#54851](https://github.com/nodejs/node/pull/54851)
- \[[`f79fd03f41`](https://github.com/nodejs/node/commit/f79fd03f41)] - **(SEMVER-MINOR)** **test_runner**: add support for coverage via run() (Chemi Atlow) [#53937](https://github.com/nodejs/node/pull/53937)
- \[[`d2ad9b4fb6`](https://github.com/nodejs/node/commit/d2ad9b4fb6)] - **(SEMVER-MINOR)** **worker**: add `markAsUncloneable` api (Jason Zhang) [#55234](https://github.com/nodejs/node/pull/55234)

### Semver-Patch Commits

- \[[`e1d8b4f038`](https://github.com/nodejs/node/commit/e1d8b4f038)] - **assert**: show the diff when deep comparing data with a custom message (Giovanni) [#54759](https://github.com/nodejs/node/pull/54759)
- \[[`4eeeab09f0`](https://github.com/nodejs/node/commit/4eeeab09f0)] - **benchmark**: rewrite detect-esm-syntax benchmark (Joyee Cheung) [#55238](https://github.com/nodejs/node/pull/55238)
- \[[`834316d541`](https://github.com/nodejs/node/commit/834316d541)] - **benchmark**: add no-warnings to process.has bench (Rafael Gonzaga) [#55159](https://github.com/nodejs/node/pull/55159)
- \[[`00d4f8073c`](https://github.com/nodejs/node/commit/00d4f8073c)] - **benchmark**: create benchmark for typescript (Marco Ippolito) [#54904](https://github.com/nodejs/node/pull/54904)
- \[[`96ec7eede9`](https://github.com/nodejs/node/commit/96ec7eede9)] - **benchmark**: add webstorage benchmark (jakecastelli) [#55040](https://github.com/nodejs/node/pull/55040)
- \[[`29357cb0ef`](https://github.com/nodejs/node/commit/29357cb0ef)] - **benchmark**: include ascii to fs/readfile (Rafael Gonzaga) [#54988](https://github.com/nodejs/node/pull/54988)
- \[[`53cba82e55`](https://github.com/nodejs/node/commit/53cba82e55)] - **benchmark**: add dotenv benchmark (Aviv Keller) [#54278](https://github.com/nodejs/node/pull/54278)
- \[[`4062b3fb43`](https://github.com/nodejs/node/commit/4062b3fb43)] - **buffer**: coerce extrema to int in `blob.slice` (Antoine du Hamel) [#55141](https://github.com/nodejs/node/pull/55141)
- \[[`f805d0be95`](https://github.com/nodejs/node/commit/f805d0be95)] - **buffer**: correctly apply prototype to cloned `File` / `Blob` (Aviv Keller) [#55138](https://github.com/nodejs/node/pull/55138)
- \[[`da5887d8e9`](https://github.com/nodejs/node/commit/da5887d8e9)] - **buffer**: extract Blob's .arrayBuffer() & webidl changes (Matthew Aitken) [#53372](https://github.com/nodejs/node/pull/53372)
- \[[`0d4387ebe2`](https://github.com/nodejs/node/commit/0d4387ebe2)] - **buffer**: use simdutf convert_latin1_to_utf8_safe (Robert Nagy) [#54798](https://github.com/nodejs/node/pull/54798)
- \[[`ae1e2b53b7`](https://github.com/nodejs/node/commit/ae1e2b53b7)] - **build**: fix notify-on-review-wanted action (Rafael Gonzaga) [#55304](https://github.com/nodejs/node/pull/55304)
- \[[`22bc15764b`](https://github.com/nodejs/node/commit/22bc15764b)] - **build**: include `.nycrc` in coverage workflows (Wuli Zuo) [#55210](https://github.com/nodejs/node/pull/55210)
- \[[`28ffa4b751`](https://github.com/nodejs/node/commit/28ffa4b751)] - **build**: fix not valid json in coverage (jakecastelli) [#55179](https://github.com/nodejs/node/pull/55179)
- \[[`1398c04c47`](https://github.com/nodejs/node/commit/1398c04c47)] - **build**: notify via slack when review-wanted (Rafael Gonzaga) [#55102](https://github.com/nodejs/node/pull/55102)
- \[[`b2c42dbcbb`](https://github.com/nodejs/node/commit/b2c42dbcbb)] - **build**: add more information to Makefile help (Aviv Keller) [#53381](https://github.com/nodejs/node/pull/53381)
- \[[`a1cd3c8777`](https://github.com/nodejs/node/commit/a1cd3c8777)] - **build**: update ruff and add `lint-py-fix` (Aviv Keller) [#54410](https://github.com/nodejs/node/pull/54410)
- \[[`6a6c957be7`](https://github.com/nodejs/node/commit/6a6c957be7)] - **build**: remove -v flag to reduce noise (iwuliz) [#55025](https://github.com/nodejs/node/pull/55025)
- \[[`5f6bb7d007`](https://github.com/nodejs/node/commit/5f6bb7d007)] - **build**: display free disk space after build in the test-macOS workflow (iwuliz) [#55025](https://github.com/nodejs/node/pull/55025)
- \[[`415b82d8b8`](https://github.com/nodejs/node/commit/415b82d8b8)] - **build**: support up to python 3.13 in android-configure (Aviv Keller) [#54529](https://github.com/nodejs/node/pull/54529)
- \[[`beb1892036`](https://github.com/nodejs/node/commit/beb1892036)] - **build**: add the option to generate compile_commands.json in vcbuild.bat (Segev Finer) [#52279](https://github.com/nodejs/node/pull/52279)
- \[[`81cc72996a`](https://github.com/nodejs/node/commit/81cc72996a)] - **build**: fix eslint makefile target (Aviv Keller) [#54999](https://github.com/nodejs/node/pull/54999)
- \[[`7e00be7650`](https://github.com/nodejs/node/commit/7e00be7650)] - _**Revert**_ "**build**: upgrade clang-format to v18" (Chengzhong Wu) [#54994](https://github.com/nodejs/node/pull/54994)
- \[[`96e057093f`](https://github.com/nodejs/node/commit/96e057093f)] - **build**: print `Running XYZ linter...` for py and yml (Aviv Keller) [#54386](https://github.com/nodejs/node/pull/54386)
- \[[`ab5e58bf29`](https://github.com/nodejs/node/commit/ab5e58bf29)] - _**Revert**_ "**build**: only generate specified build type files" (Chengzhong Wu) [#53580](https://github.com/nodejs/node/pull/53580)
- \[[`6cb940a546`](https://github.com/nodejs/node/commit/6cb940a546)] - **build**: only generate specified build type files (Chengzhong Wu) [#53511](https://github.com/nodejs/node/pull/53511)
- \[[`27f8d9e9d2`](https://github.com/nodejs/node/commit/27f8d9e9d2)] - **build,win**: enable pch for clang-cl (Stefan Stojanovic) [#55249](https://github.com/nodejs/node/pull/55249)
- \[[`bbf08c6a1b`](https://github.com/nodejs/node/commit/bbf08c6a1b)] - **build,win**: add winget config to set up env (Hüseyin Açacak) [#54729](https://github.com/nodejs/node/pull/54729)
- \[[`653b96527a`](https://github.com/nodejs/node/commit/653b96527a)] - **build,win**: float VS 17.11 compilation patch (Stefan Stojanovic) [#54970](https://github.com/nodejs/node/pull/54970)
- \[[`0c5fa57bc7`](https://github.com/nodejs/node/commit/0c5fa57bc7)] - **cli**: ensure --run has proper pwd (Yagiz Nizipli) [#54949](https://github.com/nodejs/node/pull/54949)
- \[[`65768bca59`](https://github.com/nodejs/node/commit/65768bca59)] - **cli**: fix spacing for port range error (Aviv Keller) [#54495](https://github.com/nodejs/node/pull/54495)
- \[[`2d77ba5d30`](https://github.com/nodejs/node/commit/2d77ba5d30)] - _**Revert**_ "**console**: colorize console error and warn" (Aviv Keller) [#54677](https://github.com/nodejs/node/pull/54677)
- \[[`b64006c0ed`](https://github.com/nodejs/node/commit/b64006c0ed)] - **crypto**: ensure invalid SubtleCrypto JWK data import results in DataError (Filip Skokan) [#55041](https://github.com/nodejs/node/pull/55041)
- \[[`7a3027d563`](https://github.com/nodejs/node/commit/7a3027d563)] - **deps**: update undici to 6.20.0 (Node.js GitHub Bot) [#55329](https://github.com/nodejs/node/pull/55329)
- \[[`54b5ec94e0`](https://github.com/nodejs/node/commit/54b5ec94e0)] - **deps**: patch V8 to *********** (Node.js GitHub Bot) [#55161](https://github.com/nodejs/node/pull/55161)
- \[[`20d8b85d34`](https://github.com/nodejs/node/commit/20d8b85d34)] - **deps**: upgrade npm to 10.9.0 (npm team) [#55255](https://github.com/nodejs/node/pull/55255)
- \[[`fe45be207b`](https://github.com/nodejs/node/commit/fe45be207b)] - **deps**: V8: backport 0d5d6e71bbb0 (Yagiz Nizipli) [#55115](https://github.com/nodejs/node/pull/55115)
- \[[`5ff9b072b2`](https://github.com/nodejs/node/commit/5ff9b072b2)] - **deps**: update archs files for openssl-3.0.15+quic1 (Node.js GitHub Bot) [#55184](https://github.com/nodejs/node/pull/55184)
- \[[`302e6afe8c`](https://github.com/nodejs/node/commit/302e6afe8c)] - **deps**: upgrade openssl sources to quictls/openssl-3.0.15+quic1 (Node.js GitHub Bot) [#55184](https://github.com/nodejs/node/pull/55184)
- \[[`5f78e2c880`](https://github.com/nodejs/node/commit/5f78e2c880)] - **deps**: update timezone to 2024b (Node.js GitHub Bot) [#55056](https://github.com/nodejs/node/pull/55056)
- \[[`5ed3296051`](https://github.com/nodejs/node/commit/5ed3296051)] - **deps**: patch V8 to *********** (Node.js GitHub Bot) [#55057](https://github.com/nodejs/node/pull/55057)
- \[[`a6ece28604`](https://github.com/nodejs/node/commit/a6ece28604)] - **deps**: update acorn-walk to 8.3.4 (Node.js GitHub Bot) [#54950](https://github.com/nodejs/node/pull/54950)
- \[[`a428b21066`](https://github.com/nodejs/node/commit/a428b21066)] - **deps**: update corepack to 0.29.4 (Node.js GitHub Bot) [#54845](https://github.com/nodejs/node/pull/54845)
- \[[`260f1f4608`](https://github.com/nodejs/node/commit/260f1f4608)] - **deps**: patch V8 to 12.8.374.33 (Node.js GitHub Bot) [#54952](https://github.com/nodejs/node/pull/54952)
- \[[`b887942e6b`](https://github.com/nodejs/node/commit/b887942e6b)] - **deps**: patch V8 to 12.8.374.32 (Node.js GitHub Bot) [#54884](https://github.com/nodejs/node/pull/54884)
- \[[`9087056060`](https://github.com/nodejs/node/commit/9087056060)] - **deps**: patch V8 to 12.8.374.31 (Michaël Zasso) [#54682](https://github.com/nodejs/node/pull/54682)
- \[[`6bce6f69c6`](https://github.com/nodejs/node/commit/6bce6f69c6)] - _**Revert**_ "**deps**: remove bogus V8 DCHECK" (Michaël Zasso) [#54682](https://github.com/nodejs/node/pull/54682)
- \[[`0c771c35fa`](https://github.com/nodejs/node/commit/0c771c35fa)] - **deps**: patch V8 to 12.8.374.22 (Node.js GitHub Bot) [#54435](https://github.com/nodejs/node/pull/54435)
- \[[`543d1a9cb9`](https://github.com/nodejs/node/commit/543d1a9cb9)] - **deps**: update archs files for openssl-3.0.14+quic1 (Node.js GitHub Bot) [#54336](https://github.com/nodejs/node/pull/54336)
- \[[`94d062bc78`](https://github.com/nodejs/node/commit/94d062bc78)] - **deps**: upgrade openssl sources to quictls/openssl-3.0.14+quic1 (Node.js GitHub Bot) [#54336](https://github.com/nodejs/node/pull/54336)
- \[[`8e33f20a64`](https://github.com/nodejs/node/commit/8e33f20a64)] - _**Revert**_ "**deps**: V8: cherry-pick 9ebca66a5740" (Joyee Cheung) [#53582](https://github.com/nodejs/node/pull/53582)
- \[[`4c730aed7f`](https://github.com/nodejs/node/commit/4c730aed7f)] - **deps**: V8: cherry-pick 9ebca66a5740 (Chengzhong Wu) [#53522](https://github.com/nodejs/node/pull/53522)
- \[[`e9904fe49a`](https://github.com/nodejs/node/commit/e9904fe49a)] - **doc**: edit onboarding guide to clarify when mailmap addition is needed (Antoine du Hamel) [#55334](https://github.com/nodejs/node/pull/55334)
- \[[`acd698a5c8`](https://github.com/nodejs/node/commit/acd698a5c8)] - **doc**: fix the return type of outgoingMessage.setHeaders() (Jimmy Leung) [#55290](https://github.com/nodejs/node/pull/55290)
- \[[`d620755661`](https://github.com/nodejs/node/commit/d620755661)] - **doc**: add release key for aduh95 (Antoine du Hamel) [#55349](https://github.com/nodejs/node/pull/55349)
- \[[`4a3fffaf58`](https://github.com/nodejs/node/commit/4a3fffaf58)] - **doc**: move `ERR_INVALID_PERFORMANCE_MARK` to legacy errors (Antoine du Hamel) [#55247](https://github.com/nodejs/node/pull/55247)
- \[[`e79ae1bf0c`](https://github.com/nodejs/node/commit/e79ae1bf0c)] - **doc**: reserve 132 for Electron 34 (Michaela Laurencin) [#55306](https://github.com/nodejs/node/pull/55306)
- \[[`33fe88a0b3`](https://github.com/nodejs/node/commit/33fe88a0b3)] - **doc**: add pmarchini to collaborators (Pietro Marchini) [#55331](https://github.com/nodejs/node/pull/55331)
- \[[`755b89772d`](https://github.com/nodejs/node/commit/755b89772d)] - **doc**: fix `events.once()` example using `AbortSignal` (Ivo Janssen) [#55144](https://github.com/nodejs/node/pull/55144)
- \[[`accb239272`](https://github.com/nodejs/node/commit/accb239272)] - **doc**: add onboarding details for ambassador program (Marco Ippolito) [#55284](https://github.com/nodejs/node/pull/55284)
- \[[`a301596c41`](https://github.com/nodejs/node/commit/a301596c41)] - **doc**: move `ERR_NAPI_TSFN_START/STOP_IDLE_LOOP` to legacy errors (Antoine du Hamel) [#55248](https://github.com/nodejs/node/pull/55248)
- \[[`32efeea0c0`](https://github.com/nodejs/node/commit/32efeea0c0)] - **doc**: fix initial default value of autoSelectFamily (Ihor Rohovets) [#55245](https://github.com/nodejs/node/pull/55245)
- \[[`cc9b9a7f70`](https://github.com/nodejs/node/commit/cc9b9a7f70)] - **doc**: tweak onboarding instructions (Michael Dawson) [#55212](https://github.com/nodejs/node/pull/55212)
- \[[`c9cffb73b3`](https://github.com/nodejs/node/commit/c9cffb73b3)] - **doc**: update test context.assert (Pietro Marchini) [#55186](https://github.com/nodejs/node/pull/55186)
- \[[`348d865652`](https://github.com/nodejs/node/commit/348d865652)] - **doc**: update `require(ESM)` history and stability status (Antoine du Hamel) [#55199](https://github.com/nodejs/node/pull/55199)
- \[[`14b53df33c`](https://github.com/nodejs/node/commit/14b53df33c)] - **doc**: fix unordered error anchors (Antoine du Hamel) [#55242](https://github.com/nodejs/node/pull/55242)
- \[[`dec10991e7`](https://github.com/nodejs/node/commit/dec10991e7)] - **doc**: mention addons to experimental permission (Rafael Gonzaga) [#55166](https://github.com/nodejs/node/pull/55166)
- \[[`cebf21dfa5`](https://github.com/nodejs/node/commit/cebf21dfa5)] - **doc**: use correct dash in stability status (Antoine du Hamel) [#55200](https://github.com/nodejs/node/pull/55200)
- \[[`0f02810fc9`](https://github.com/nodejs/node/commit/0f02810fc9)] - **doc**: fix link in `test/README.md` (Livia Medeiros) [#55165](https://github.com/nodejs/node/pull/55165)
- \[[`22b4b7c626`](https://github.com/nodejs/node/commit/22b4b7c626)] - **doc**: fix typos (Nathan Baulch) [#55066](https://github.com/nodejs/node/pull/55066)
- \[[`e6427e1d87`](https://github.com/nodejs/node/commit/e6427e1d87)] - **doc**: add esm examples to node:net (Alfredo González) [#55134](https://github.com/nodejs/node/pull/55134)
- \[[`6d1cd506b5`](https://github.com/nodejs/node/commit/6d1cd506b5)] - **doc**: remove outdated https import reference (Edigleysson Silva (Edy)) [#55111](https://github.com/nodejs/node/pull/55111)
- \[[`5368cdcf8a`](https://github.com/nodejs/node/commit/5368cdcf8a)] - **doc**: move the YAML changes element (sendoru) [#55112](https://github.com/nodejs/node/pull/55112)
- \[[`23743f63fb`](https://github.com/nodejs/node/commit/23743f63fb)] - **doc**: remove random horizontal separators in `process.md` (Antoine du Hamel) [#55149](https://github.com/nodejs/node/pull/55149)
- \[[`18acff0d01`](https://github.com/nodejs/node/commit/18acff0d01)] - **doc**: put --env-file-if-exists=config right under --env-file=config (Edigleysson Silva (Edy)) [#55131](https://github.com/nodejs/node/pull/55131)
- \[[`fd787c96e1`](https://github.com/nodejs/node/commit/fd787c96e1)] - **doc**: fix the require resolve algorithm in `modules.md` (chirsz) [#55117](https://github.com/nodejs/node/pull/55117)
- \[[`668e523392`](https://github.com/nodejs/node/commit/668e523392)] - **doc**: update style guide (Aviv Keller) [#53223](https://github.com/nodejs/node/pull/53223)
- \[[`ae82b455d1`](https://github.com/nodejs/node/commit/ae82b455d1)] - **doc**: add missing `:` to `run()`'s `globPatterns` (Aviv Keller) [#55135](https://github.com/nodejs/node/pull/55135)
- \[[`7f480818b7`](https://github.com/nodejs/node/commit/7f480818b7)] - **doc**: correct `cleanup` option in stream.(promises.)finished (René) [#55043](https://github.com/nodejs/node/pull/55043)
- \[[`b8493a5789`](https://github.com/nodejs/node/commit/b8493a5789)] - **doc**: add abmusse to collaborators (Abdirahim Musse) [#55086](https://github.com/nodejs/node/pull/55086)
- \[[`f20c42e964`](https://github.com/nodejs/node/commit/f20c42e964)] - **doc**: add note about `--expose-internals` (Aviv Keller) [#52861](https://github.com/nodejs/node/pull/52861)
- \[[`1c61a83444`](https://github.com/nodejs/node/commit/1c61a83444)] - **doc**: remove `parseREPLKeyword` from REPL documentation (Aviv Keller) [#54749](https://github.com/nodejs/node/pull/54749)
- \[[`65362f0181`](https://github.com/nodejs/node/commit/65362f0181)] - **doc**: add missing EventSource docs to globals (Matthew Aitken) [#55022](https://github.com/nodejs/node/pull/55022)
- \[[`5e25c2a79a`](https://github.com/nodejs/node/commit/5e25c2a79a)] - **doc**: cover --experimental-test-module-mocks flag (Jonathan Sharpe) [#55021](https://github.com/nodejs/node/pull/55021)
- \[[`99433a2d7a`](https://github.com/nodejs/node/commit/99433a2d7a)] - **doc**: add more details for localStorage and sessionStorage (Batuhan Tomo) [#53881](https://github.com/nodejs/node/pull/53881)
- \[[`b446a587ba`](https://github.com/nodejs/node/commit/b446a587ba)] - **doc**: mark v21 as End-of-Life (Aviv Keller) [#54984](https://github.com/nodejs/node/pull/54984)
- \[[`5e87577b4f`](https://github.com/nodejs/node/commit/5e87577b4f)] - **doc**: change backporting guide with updated info (Aviv Keller) [#53746](https://github.com/nodejs/node/pull/53746)
- \[[`de47b3122a`](https://github.com/nodejs/node/commit/de47b3122a)] - **doc**: add missing definitions to `internal-api.md` (Aviv Keller) [#53303](https://github.com/nodejs/node/pull/53303)
- \[[`421977cd48`](https://github.com/nodejs/node/commit/421977cd48)] - **doc**: fix history of `process.features` (Antoine du Hamel) [#54982](https://github.com/nodejs/node/pull/54982)
- \[[`305137faae`](https://github.com/nodejs/node/commit/305137faae)] - **doc**: fix typo callsite.lineNumber (Rafael Gonzaga) [#54969](https://github.com/nodejs/node/pull/54969)
- \[[`7feff2434d`](https://github.com/nodejs/node/commit/7feff2434d)] - **doc**: update documentation for externalizing deps (Michael Dawson) [#54792](https://github.com/nodejs/node/pull/54792)
- \[[`cb20c5b9f4`](https://github.com/nodejs/node/commit/cb20c5b9f4)] - **doc**: add documentation for process.features (Marco Ippolito) [#54897](https://github.com/nodejs/node/pull/54897)
- \[[`24302c9fe9`](https://github.com/nodejs/node/commit/24302c9fe9)] - **doc**: fix typo in CppgcMixin docs (Joyee Cheung) [#54762](https://github.com/nodejs/node/pull/54762)
- \[[`7327e44a05`](https://github.com/nodejs/node/commit/7327e44a05)] - **doc**: sort versions to fix the linter error (Rafael Gonzaga) [#54229](https://github.com/nodejs/node/pull/54229)
- \[[`fb852798dc`](https://github.com/nodejs/node/commit/fb852798dc)] - **esm**: do not interpret `"main"` as a URL (Antoine du Hamel) [#55003](https://github.com/nodejs/node/pull/55003)
- \[[`8fd90938f9`](https://github.com/nodejs/node/commit/8fd90938f9)] - **esm**: remove --no-import-harmony-assertions (Shu-yu Guo) [#54890](https://github.com/nodejs/node/pull/54890)
- \[[`a9081b5391`](https://github.com/nodejs/node/commit/a9081b5391)] - **events**: allow null/undefined eventInitDict (Matthew Aitken) [#54643](https://github.com/nodejs/node/pull/54643)
- \[[`0de1cf004c`](https://github.com/nodejs/node/commit/0de1cf004c)] - **events**: return `currentTarget` when dispatching (Matthew Aitken) [#54642](https://github.com/nodejs/node/pull/54642)
- \[[`9f9069d313`](https://github.com/nodejs/node/commit/9f9069d313)] - **fs**: fix linter issue (Antoine du Hamel) [#55353](https://github.com/nodejs/node/pull/55353)
- \[[`36ca010bef`](https://github.com/nodejs/node/commit/36ca010bef)] - **fs**: acknowledge `signal` option in `filehandle.createReadStream()` (Livia Medeiros) [#55148](https://github.com/nodejs/node/pull/55148)
- \[[`7fe5bcd29e`](https://github.com/nodejs/node/commit/7fe5bcd29e)] - **fs**: check subdir correctly in cpSync (Jason Zhang) [#55033](https://github.com/nodejs/node/pull/55033)
- \[[`090add7864`](https://github.com/nodejs/node/commit/090add7864)] - **fs**: refactoring declaratively with `Array.fromAsync` (Sonny) [#54644](https://github.com/nodejs/node/pull/54644)
- \[[`77ca5ca075`](https://github.com/nodejs/node/commit/77ca5ca075)] - **fs**: convert to u8 string for filesystem path (Jason Zhang) [#54653](https://github.com/nodejs/node/pull/54653)
- \[[`cf2bce6386`](https://github.com/nodejs/node/commit/cf2bce6386)] - **fs**: fix regression on rmsync (Yagiz Nizipli) [#53982](https://github.com/nodejs/node/pull/53982)
- \[[`7168295e7a`](https://github.com/nodejs/node/commit/7168295e7a)] - **fs**: move `rmSync` implementation to c++ (Yagiz Nizipli) [#53617](https://github.com/nodejs/node/pull/53617)
- \[[`71785889c8`](https://github.com/nodejs/node/commit/71785889c8)] - **lib**: prefer logical assignment (Aviv Keller) [#55044](https://github.com/nodejs/node/pull/55044)
- \[[`78f421de88`](https://github.com/nodejs/node/commit/78f421de88)] - **lib**: fix module print timing when specifier includes `"` (Antoine du Hamel) [#55150](https://github.com/nodejs/node/pull/55150)
- \[[`d5eb9a378e`](https://github.com/nodejs/node/commit/d5eb9a378e)] - **lib**: remove `Symbol[Async]Dispose` polyfills (Michaël Zasso) [#55276](https://github.com/nodejs/node/pull/55276)
- \[[`4c045351c1`](https://github.com/nodejs/node/commit/4c045351c1)] - **lib**: fix typos (Nathan Baulch) [#55065](https://github.com/nodejs/node/pull/55065)
- \[[`574f2dd517`](https://github.com/nodejs/node/commit/574f2dd517)] - **lib**: prefer optional chaining (Aviv Keller) [#55045](https://github.com/nodejs/node/pull/55045)
- \[[`76edde5cd0`](https://github.com/nodejs/node/commit/76edde5cd0)] - **lib**: remove lib/internal/idna.js (Yagiz Nizipli) [#55050](https://github.com/nodejs/node/pull/55050)
- \[[`7014e50ca3`](https://github.com/nodejs/node/commit/7014e50ca3)] - **lib**: the REPL should survive deletion of Array.prototype methods (Jordan Harband) [#31457](https://github.com/nodejs/node/pull/31457)
- \[[`5c22d19f44`](https://github.com/nodejs/node/commit/5c22d19f44)] - **lib, tools**: remove duplicate requires (Aviv Keller) [#54987](https://github.com/nodejs/node/pull/54987)
- \[[`24648b5769`](https://github.com/nodejs/node/commit/24648b5769)] - **lib,esm**: handle bypass network-import via data: (Rafael Gonzaga) [#53764](https://github.com/nodejs/node/pull/53764)
- \[[`1d38bd1122`](https://github.com/nodejs/node/commit/1d38bd1122)] - **meta**: move one or more collaborators to emeritus (Node.js GitHub Bot) [#55300](https://github.com/nodejs/node/pull/55300)
- \[[`98788dace6`](https://github.com/nodejs/node/commit/98788dace6)] - **meta**: bump mozilla-actions/sccache-action from 0.0.5 to 0.0.6 (dependabot\[bot]) [#55225](https://github.com/nodejs/node/pull/55225)
- \[[`8de2695fe5`](https://github.com/nodejs/node/commit/8de2695fe5)] - **meta**: bump actions/checkout from 4.1.7 to 4.2.0 (dependabot\[bot]) [#55224](https://github.com/nodejs/node/pull/55224)
- \[[`ccae9c0fef`](https://github.com/nodejs/node/commit/ccae9c0fef)] - **meta**: bump actions/setup-node from 4.0.3 to 4.0.4 (dependabot\[bot]) [#55223](https://github.com/nodejs/node/pull/55223)
- \[[`fd4959c67a`](https://github.com/nodejs/node/commit/fd4959c67a)] - **meta**: bump peter-evans/create-pull-request from 7.0.1 to 7.0.5 (dependabot\[bot]) [#55219](https://github.com/nodejs/node/pull/55219)
- \[[`c08bb75618`](https://github.com/nodejs/node/commit/c08bb75618)] - **meta**: add mailmap entry for abmusse (Abdirahim Musse) [#55182](https://github.com/nodejs/node/pull/55182)
- \[[`18800da280`](https://github.com/nodejs/node/commit/18800da280)] - **meta**: add more information about nightly releases (Aviv Keller) [#55084](https://github.com/nodejs/node/pull/55084)
- \[[`eda98728da`](https://github.com/nodejs/node/commit/eda98728da)] - **meta**: add `linux` to OS labels in collaborator guide (Aviv Keller) [#54986](https://github.com/nodejs/node/pull/54986)
- \[[`8aa57918c2`](https://github.com/nodejs/node/commit/8aa57918c2)] - **meta**: remove never-used workflow trigger (Aviv Keller) [#54983](https://github.com/nodejs/node/pull/54983)
- \[[`c6ae161237`](https://github.com/nodejs/node/commit/c6ae161237)] - **meta**: remove unneeded ignore rules from ruff (Aviv Keller) [#54360](https://github.com/nodejs/node/pull/54360)
- \[[`ccc7ce09f2`](https://github.com/nodejs/node/commit/ccc7ce09f2)] - **meta**: remove `build-windows.yml` (Aviv Keller) [#54662](https://github.com/nodejs/node/pull/54662)
- \[[`f88fe776ef`](https://github.com/nodejs/node/commit/f88fe776ef)] - **meta**: add links to alternative issue trackers (Aviv Keller) [#54401](https://github.com/nodejs/node/pull/54401)
- \[[`90f56dbad9`](https://github.com/nodejs/node/commit/90f56dbad9)] - **module**: throw ERR_NO_TYPESCRIPT when compiled without amaro (Marco Ippolito) [#55332](https://github.com/nodejs/node/pull/55332)
- \[[`31a37e777d`](https://github.com/nodejs/node/commit/31a37e777d)] - **module**: wrap swc error in ERR_INVALID_TYPESCRIPT_SYNTAX (Marco Ippolito) [#55316](https://github.com/nodejs/node/pull/55316)
- \[[`3fb7426f83`](https://github.com/nodejs/node/commit/3fb7426f83)] - **module**: check --experimental-require-module separately from detection (Joyee Cheung) [#55250](https://github.com/nodejs/node/pull/55250)
- \[[`bdd590be73`](https://github.com/nodejs/node/commit/bdd590be73)] - **module**: use kNodeModulesRE to detect node_modules (Joyee Cheung) [#55243](https://github.com/nodejs/node/pull/55243)
- \[[`5e4da33d97`](https://github.com/nodejs/node/commit/5e4da33d97)] - **module**: add internal type def for `flushCompileCache` (Jacob Smith) [#55226](https://github.com/nodejs/node/pull/55226)
- \[[`d24c7313f7`](https://github.com/nodejs/node/commit/d24c7313f7)] - **module**: support 'module.exports' interop export in require(esm) (Guy Bedford) [#54563](https://github.com/nodejs/node/pull/54563)
- \[[`12f92b04f4`](https://github.com/nodejs/node/commit/12f92b04f4)] - **module**: remove duplicated import (Aviv Keller) [#54942](https://github.com/nodejs/node/pull/54942)
- \[[`be4babb3c2`](https://github.com/nodejs/node/commit/be4babb3c2)] - **module**: report unfinished TLA in ambiguous modules (Antoine du Hamel) [#54980](https://github.com/nodejs/node/pull/54980)
- \[[`3ac5b49d85`](https://github.com/nodejs/node/commit/3ac5b49d85)] - **module**: refator ESM loader for adding future synchronous hooks (Joyee Cheung) [#54769](https://github.com/nodejs/node/pull/54769)
- \[[`3c4ef343ee`](https://github.com/nodejs/node/commit/3c4ef343ee)] - **module**: remove bogus assertion in CJS entrypoint handling with --import (Joyee Cheung) [#54592](https://github.com/nodejs/node/pull/54592)
- \[[`e35902cddb`](https://github.com/nodejs/node/commit/e35902cddb)] - **module**: fix discrepancy between .ts and .js (Marco Ippolito) [#54461](https://github.com/nodejs/node/pull/54461)
- \[[`fdf838aee6`](https://github.com/nodejs/node/commit/fdf838aee6)] - **node-api**: add napi_create_buffer_from_arraybuffer method (Mert Can Altin) [#54505](https://github.com/nodejs/node/pull/54505)
- \[[`87e7aeb672`](https://github.com/nodejs/node/commit/87e7aeb672)] - **os**: use const with early return for path (Trivikram Kamat) [#54959](https://github.com/nodejs/node/pull/54959)
- \[[`e42ca5c1a9`](https://github.com/nodejs/node/commit/e42ca5c1a9)] - **path**: remove repetitive conditional operator in `posix.resolve` (Wiyeong Seo) [#54835](https://github.com/nodejs/node/pull/54835)
- \[[`04750afb1e`](https://github.com/nodejs/node/commit/04750afb1e)] - **perf_hooks**: add missing type argument to getEntriesByName (Luke Taher) [#54767](https://github.com/nodejs/node/pull/54767)
- \[[`f98d9c125c`](https://github.com/nodejs/node/commit/f98d9c125c)] - **process**: fix `process.features.typescript` when Amaro is unavailable (Antoine du Hamel) [#55323](https://github.com/nodejs/node/pull/55323)
- \[[`bbdfeebd9e`](https://github.com/nodejs/node/commit/bbdfeebd9e)] - **process**: add `process.features.typescript` (Aviv Keller) [#54295](https://github.com/nodejs/node/pull/54295)
- \[[`cdae315706`](https://github.com/nodejs/node/commit/cdae315706)] - **quic**: start adding in the internal quic js api (James M Snell) [#53256](https://github.com/nodejs/node/pull/53256)
- \[[`c6d20a034d`](https://github.com/nodejs/node/commit/c6d20a034d)] - **repl**: catch `\v` and `\r` in new-line detection (Aviv Keller) [#54512](https://github.com/nodejs/node/pull/54512)
- \[[`09d10b50dc`](https://github.com/nodejs/node/commit/09d10b50dc)] - **sqlite**: disable DQS misfeature by default (Tobias Nießen) [#55297](https://github.com/nodejs/node/pull/55297)
- \[[`7af434fc19`](https://github.com/nodejs/node/commit/7af434fc19)] - **sqlite**: make sourceSQL and expandedSQL string-valued properties (Tobias Nießen) [#54721](https://github.com/nodejs/node/pull/54721)
- \[[`a49abec6c3`](https://github.com/nodejs/node/commit/a49abec6c3)] - **sqlite**: enable foreign key constraints by default (Tobias Nießen) [#54777](https://github.com/nodejs/node/pull/54777)
- \[[`14353387eb`](https://github.com/nodejs/node/commit/14353387eb)] - **src**: implement IsInsideNodeModules() in C++ (Joyee Cheung) [#55286](https://github.com/nodejs/node/pull/55286)
- \[[`18536d95e2`](https://github.com/nodejs/node/commit/18536d95e2)] - **src**: apply getCallSite optimization (RafaelGSS) [#55174](https://github.com/nodejs/node/pull/55174)
- \[[`317d2450f9`](https://github.com/nodejs/node/commit/317d2450f9)] - **src**: modernize likely/unlikely hints (Yagiz Nizipli) [#55155](https://github.com/nodejs/node/pull/55155)
- \[[`33bbf3751b`](https://github.com/nodejs/node/commit/33bbf3751b)] - **src**: fixup Error.stackTraceLimit during snapshot building (Joyee Cheung) [#55121](https://github.com/nodejs/node/pull/55121)
- \[[`65fbc95949`](https://github.com/nodejs/node/commit/65fbc95949)] - **src**: parse --stack-trace-limit and use it in --trace-\* flags (Joyee Cheung) [#55121](https://github.com/nodejs/node/pull/55121)
- \[[`858bce5698`](https://github.com/nodejs/node/commit/858bce5698)] - **src**: make minor tweaks to quic c++ for c++20 (James M Snell) [#53256](https://github.com/nodejs/node/pull/53256)
- \[[`ac53a5b29d`](https://github.com/nodejs/node/commit/ac53a5b29d)] - **src**: move more key handling to ncrypto (James M Snell) [#55108](https://github.com/nodejs/node/pull/55108)
- \[[`f5d454ac7e`](https://github.com/nodejs/node/commit/f5d454ac7e)] - **src**: add receiver to fast api callback methods (Carlos Espa) [#54408](https://github.com/nodejs/node/pull/54408)
- \[[`b5fb2ff81e`](https://github.com/nodejs/node/commit/b5fb2ff81e)] - **src**: fix typos (Nathan Baulch) [#55064](https://github.com/nodejs/node/pull/55064)
- \[[`812806a757`](https://github.com/nodejs/node/commit/812806a757)] - **src**: move more stuff over to use Maybe\<void> (James M Snell) [#54831](https://github.com/nodejs/node/pull/54831)
- \[[`84966703e0`](https://github.com/nodejs/node/commit/84966703e0)] - **src**: track BaseObjects with an efficient list (Chengzhong Wu) [#55104](https://github.com/nodejs/node/pull/55104)
- \[[`02cdf7b809`](https://github.com/nodejs/node/commit/02cdf7b809)] - **src**: decode native error messages as UTF-8 (Joyee Cheung) [#55024](https://github.com/nodejs/node/pull/55024)
- \[[`6fb9f56994`](https://github.com/nodejs/node/commit/6fb9f56994)] - **src**: update clang-tidy and focus on modernization (Yagiz Nizipli) [#53757](https://github.com/nodejs/node/pull/53757)
- \[[`773e7c67cf`](https://github.com/nodejs/node/commit/773e7c67cf)] - **src**: do not call path.back() when it is empty (Cheng) [#55072](https://github.com/nodejs/node/pull/55072)
- \[[`c4681d55ae`](https://github.com/nodejs/node/commit/c4681d55ae)] - **src**: move evp stuff to ncrypto (James M Snell) [#54911](https://github.com/nodejs/node/pull/54911)
- \[[`5a966714c1`](https://github.com/nodejs/node/commit/5a966714c1)] - **src**: revert filesystem::path changes (Yagiz Nizipli) [#55015](https://github.com/nodejs/node/pull/55015)
- \[[`12dd4c7575`](https://github.com/nodejs/node/commit/12dd4c7575)] - **src**: mark node --run as stable (Yagiz Nizipli) [#53763](https://github.com/nodejs/node/pull/53763)
- \[[`8b8fc53c9a`](https://github.com/nodejs/node/commit/8b8fc53c9a)] - **src**: cleanup per env handles directly without a list (Chengzhong Wu) [#54993](https://github.com/nodejs/node/pull/54993)
- \[[`fd8c762fab`](https://github.com/nodejs/node/commit/fd8c762fab)] - **src**: add unistd.h import if node posix credentials is defined (Jonas) [#54528](https://github.com/nodejs/node/pull/54528)
- \[[`d496d44145`](https://github.com/nodejs/node/commit/d496d44145)] - **src**: remove duplicate code setting AF_INET (He Yang) [#54939](https://github.com/nodejs/node/pull/54939)
- \[[`d2a4f92920`](https://github.com/nodejs/node/commit/d2a4f92920)] - **src**: use `Maybe<void>` where bool isn't needed (Michaël Zasso) [#54575](https://github.com/nodejs/node/pull/54575)
- \[[`8191e1f575`](https://github.com/nodejs/node/commit/8191e1f575)] - **src**: improve utf8 string generation performance (Yagiz Nizipli) [#54873](https://github.com/nodejs/node/pull/54873)
- \[[`9f5977fdac`](https://github.com/nodejs/node/commit/9f5977fdac)] - **src**: simplify string_bytes with views (Daniel Lemire) [#54876](https://github.com/nodejs/node/pull/54876)
- \[[`849db10fb3`](https://github.com/nodejs/node/commit/849db10fb3)] - **src**: add helpers for creating cppgc-managed wrappers (Joyee Cheung) [#52295](https://github.com/nodejs/node/pull/52295)
- \[[`4568df4c6d`](https://github.com/nodejs/node/commit/4568df4c6d)] - **src**: support v8::Data in heap utils (Joyee Cheung) [#52295](https://github.com/nodejs/node/pull/52295)
- \[[`4f1c27af8c`](https://github.com/nodejs/node/commit/4f1c27af8c)] - **src**: handle errors correctly in webstorage (Michaël Zasso) [#54544](https://github.com/nodejs/node/pull/54544)
- \[[`c062b5242a`](https://github.com/nodejs/node/commit/c062b5242a)] - **src**: use correct way to signal interceptor error (Michaël Zasso) [#54418](https://github.com/nodejs/node/pull/54418)
- \[[`097a52848e`](https://github.com/nodejs/node/commit/097a52848e)] - **src**: do not save c_str of a temp string (Cheng) [#53941](https://github.com/nodejs/node/pull/53941)
- \[[`3111ed7011`](https://github.com/nodejs/node/commit/3111ed7011)] - **stream**: handle undefined chunks correctly in decode stream (devstone) [#55153](https://github.com/nodejs/node/pull/55153)
- \[[`87a79cd8a1`](https://github.com/nodejs/node/commit/87a79cd8a1)] - **stream**: treat null asyncIterator as undefined (Jason Zhang) [#55119](https://github.com/nodejs/node/pull/55119)
- \[[`0e52836c35`](https://github.com/nodejs/node/commit/0e52836c35)] - **stream**: set stream prototype to closest transferable superclass (Jason Zhang) [#55067](https://github.com/nodejs/node/pull/55067)
- \[[`82dab76d63`](https://github.com/nodejs/node/commit/82dab76d63)] - **test**: fix tests when Amaro is unavailable (Richard Lau) [#55320](https://github.com/nodejs/node/pull/55320)
- \[[`fdc23b2f6b`](https://github.com/nodejs/node/commit/fdc23b2f6b)] - **test**: use more informative errors in `test-runner-cli` (Antoine du Hamel) [#55321](https://github.com/nodejs/node/pull/55321)
- \[[`a05cb0d1b0`](https://github.com/nodejs/node/commit/a05cb0d1b0)] - **test**: make `test-loaders-workers-spawned` less flaky (Antoine du Hamel) [#55172](https://github.com/nodejs/node/pull/55172)
- \[[`6c92c1391a`](https://github.com/nodejs/node/commit/6c92c1391a)] - **test**: add resource to internal module stat test (RafaelGSS) [#55157](https://github.com/nodejs/node/pull/55157)
- \[[`1d95b79b66`](https://github.com/nodejs/node/commit/1d95b79b66)] - **test**: move coverage source map tests to new file (Aviv Keller) [#55123](https://github.com/nodejs/node/pull/55123)
- \[[`2755551c3c`](https://github.com/nodejs/node/commit/2755551c3c)] - **test**: adding more tests for strip-types (Kevin Toshihiro Uehara) [#54929](https://github.com/nodejs/node/pull/54929)
- \[[`371ed85e4e`](https://github.com/nodejs/node/commit/371ed85e4e)] - **test**: update wpt test for encoding (devstone) [#55151](https://github.com/nodejs/node/pull/55151)
- \[[`99e0d0d218`](https://github.com/nodejs/node/commit/99e0d0d218)] - **test**: add `escapePOSIXShell` util (Antoine du Hamel) [#55125](https://github.com/nodejs/node/pull/55125)
- \[[`56c1786475`](https://github.com/nodejs/node/commit/56c1786475)] - **test**: remove unnecessary `await` in test-watch-mode (Wuli) [#55142](https://github.com/nodejs/node/pull/55142)
- \[[`28c7394319`](https://github.com/nodejs/node/commit/28c7394319)] - **test**: fix typos (Nathan Baulch) [#55063](https://github.com/nodejs/node/pull/55063)
- \[[`fbc6fcb018`](https://github.com/nodejs/node/commit/fbc6fcb018)] - **test**: remove duplicated test descriptions (Christos Koutsiaris) [#54140](https://github.com/nodejs/node/pull/54140)
- \[[`66a2cb210a`](https://github.com/nodejs/node/commit/66a2cb210a)] - **test**: deflake test/pummel/test-timers.js (jakecastelli) [#55098](https://github.com/nodejs/node/pull/55098)
- \[[`9bb6a1a790`](https://github.com/nodejs/node/commit/9bb6a1a790)] - **test**: deflake test-http-remove-header-stays-removed (Luigi Pinca) [#55004](https://github.com/nodejs/node/pull/55004)
- \[[`0f7bdcc17f`](https://github.com/nodejs/node/commit/0f7bdcc17f)] - **test**: fix test-tls-junk-closes-server (Michael Dawson) [#55089](https://github.com/nodejs/node/pull/55089)
- \[[`2118e32d9b`](https://github.com/nodejs/node/commit/2118e32d9b)] - **test**: fix more tests that fail when path contains a space (Antoine du Hamel) [#55088](https://github.com/nodejs/node/pull/55088)
- \[[`bdddc04dff`](https://github.com/nodejs/node/commit/bdddc04dff)] - **test**: fix `assertSnapshot` when path contains a quote (Antoine du Hamel) [#55087](https://github.com/nodejs/node/pull/55087)
- \[[`7d0ce254e8`](https://github.com/nodejs/node/commit/7d0ce254e8)] - **test**: fix some tests when path contains `%` (Antoine du Hamel) [#55082](https://github.com/nodejs/node/pull/55082)
- \[[`61ad74fb0f`](https://github.com/nodejs/node/commit/61ad74fb0f)] - _**Revert**_ "**test**: mark test-fs-watch-non-recursive flaky on Windows" (Luigi Pinca) [#55079](https://github.com/nodejs/node/pull/55079)
- \[[`02e8972169`](https://github.com/nodejs/node/commit/02e8972169)] - **test**: remove interval and give more time to unsync (Pietro Marchini) [#55006](https://github.com/nodejs/node/pull/55006)
- \[[`3c5ceff85f`](https://github.com/nodejs/node/commit/3c5ceff85f)] - **test**: deflake test-inspector-strip-types (Luigi Pinca) [#55058](https://github.com/nodejs/node/pull/55058)
- \[[`8b70e6bdee`](https://github.com/nodejs/node/commit/8b70e6bdee)] - **test**: make `test-runner-assert` more robust (Aviv Keller) [#55036](https://github.com/nodejs/node/pull/55036)
- \[[`2cec716c48`](https://github.com/nodejs/node/commit/2cec716c48)] - **test**: update tls test to support OpenSSL32 (Michael Dawson) [#55030](https://github.com/nodejs/node/pull/55030)
- \[[`1fcb128771`](https://github.com/nodejs/node/commit/1fcb128771)] - **test**: do not assume `process.execPath` contains no spaces (Antoine du Hamel) [#55028](https://github.com/nodejs/node/pull/55028)
- \[[`7ecc48d061`](https://github.com/nodejs/node/commit/7ecc48d061)] - **test**: fix `test-vm-context-dont-contextify` when path contains a space (Antoine du Hamel) [#55026](https://github.com/nodejs/node/pull/55026)
- \[[`cfe58cfdc4`](https://github.com/nodejs/node/commit/cfe58cfdc4)] - **test**: adjust tls-set-ciphers for OpenSSL32 (Michael Dawson) [#55016](https://github.com/nodejs/node/pull/55016)
- \[[`941635473d`](https://github.com/nodejs/node/commit/941635473d)] - **test**: add `util.stripVTControlCharacters` test (RedYetiDev) [#54865](https://github.com/nodejs/node/pull/54865)
- \[[`b23d1c37b9`](https://github.com/nodejs/node/commit/b23d1c37b9)] - **test**: improve coverage for timer promises schedular (Aviv Keller) [#53370](https://github.com/nodejs/node/pull/53370)
- \[[`a65e4418e5`](https://github.com/nodejs/node/commit/a65e4418e5)] - **test**: remove `getCallSite` from common (RedYetiDev) [#54947](https://github.com/nodejs/node/pull/54947)
- \[[`5116578b8a`](https://github.com/nodejs/node/commit/5116578b8a)] - **test**: remove unused common utilities (RedYetiDev) [#54825](https://github.com/nodejs/node/pull/54825)
- \[[`a9677db91b`](https://github.com/nodejs/node/commit/a9677db91b)] - **test**: deflake test-http-header-overflow (Luigi Pinca) [#54978](https://github.com/nodejs/node/pull/54978)
- \[[`9be0057859`](https://github.com/nodejs/node/commit/9be0057859)] - **test**: fix `soucre` to `source` (Aviv Keller) [#55038](https://github.com/nodejs/node/pull/55038)
- \[[`29b9c72b05`](https://github.com/nodejs/node/commit/29b9c72b05)] - **test**: add asserts to validate test assumptions (Michael Dawson) [#54997](https://github.com/nodejs/node/pull/54997)
- \[[`e35299ae62`](https://github.com/nodejs/node/commit/e35299ae62)] - **test**: add runner watch mode isolation tests (Pietro Marchini) [#54888](https://github.com/nodejs/node/pull/54888)
- \[[`2a1607cc2e`](https://github.com/nodejs/node/commit/2a1607cc2e)] - **test**: fix invalid wasm test (Aviv Keller) [#54935](https://github.com/nodejs/node/pull/54935)
- \[[`a6ed2148a0`](https://github.com/nodejs/node/commit/a6ed2148a0)] - **test**: move test-http-max-sockets to parallel (Luigi Pinca) [#54977](https://github.com/nodejs/node/pull/54977)
- \[[`636b3432d3`](https://github.com/nodejs/node/commit/636b3432d3)] - **test**: remove test-http-max-sockets flaky designation (Luigi Pinca) [#54976](https://github.com/nodejs/node/pull/54976)
- \[[`291d90acbc`](https://github.com/nodejs/node/commit/291d90acbc)] - **test**: refactor test-whatwg-webstreams-encoding to be shorter (David Dong) [#54569](https://github.com/nodejs/node/pull/54569)
- \[[`6dfa3e46d3`](https://github.com/nodejs/node/commit/6dfa3e46d3)] - **test**: adjust key sizes to support OpenSSL32 (Michael Dawson) [#54972](https://github.com/nodejs/node/pull/54972)
- \[[`f8b7a17146`](https://github.com/nodejs/node/commit/f8b7a17146)] - **test**: update test to support OpenSSL32 (Michael Dawson) [#54968](https://github.com/nodejs/node/pull/54968)
- \[[`b470e2fcb2`](https://github.com/nodejs/node/commit/b470e2fcb2)] - **test**: update DOM events web platform tests (Matthew Aitken) [#54642](https://github.com/nodejs/node/pull/54642)
- \[[`9cbef482df`](https://github.com/nodejs/node/commit/9cbef482df)] - **test**: update multiple assert tests to use node:test (James M Snell) [#54585](https://github.com/nodejs/node/pull/54585)
- \[[`259163802c`](https://github.com/nodejs/node/commit/259163802c)] - **test**: validate promise-version `setTimeout` behavior with `NaN` (Benjamin Gruenbaum) [#53622](https://github.com/nodejs/node/pull/53622)
- \[[`4174b73153`](https://github.com/nodejs/node/commit/4174b73153)] - **test**: support glob matching coverage files (Aviv Keller) [#53553](https://github.com/nodejs/node/pull/53553)
- \[[`0e187e4a21`](https://github.com/nodejs/node/commit/0e187e4a21)] - **test,crypto**: update WebCryptoAPI WPT (Filip Skokan) [#55029](https://github.com/nodejs/node/pull/55029)
- \[[`ccd4faf4bf`](https://github.com/nodejs/node/commit/ccd4faf4bf)] - _**Revert**_ "**test_runner**: ignore unmapped lines for coverage" (Aviv Keller) [#55339](https://github.com/nodejs/node/pull/55339)
- \[[`3a42085ee4`](https://github.com/nodejs/node/commit/3a42085ee4)] - **test_runner**: ignore unmapped lines for coverage (Edigleysson Silva (Edy)) [#55228](https://github.com/nodejs/node/pull/55228)
- \[[`9a9409ff1f`](https://github.com/nodejs/node/commit/9a9409ff1f)] - **test_runner**: throw on invalid source map (Aviv Keller) [#55055](https://github.com/nodejs/node/pull/55055)
- \[[`980b91a1ef`](https://github.com/nodejs/node/commit/980b91a1ef)] - **test_runner**: assert entry is a valid object (Edigleysson Silva (Edy)) [#55231](https://github.com/nodejs/node/pull/55231)
- \[[`1c7795e52e`](https://github.com/nodejs/node/commit/1c7795e52e)] - **test_runner**: add cwd option to run (Pietro Marchini) [#54705](https://github.com/nodejs/node/pull/54705)
- \[[`103b8439ca`](https://github.com/nodejs/node/commit/103b8439ca)] - **test_runner**: avoid spread operator on arrays (Antoine du Hamel) [#55143](https://github.com/nodejs/node/pull/55143)
- \[[`27dab9d916`](https://github.com/nodejs/node/commit/27dab9d916)] - **test_runner**: support typescript files in default glob (Aviv Keller) [#55081](https://github.com/nodejs/node/pull/55081)
- \[[`e32521a7b9`](https://github.com/nodejs/node/commit/e32521a7b9)] - **test_runner**: close and flush destinations on forced exit (Colin Ihrig) [#55099](https://github.com/nodejs/node/pull/55099)
- \[[`aac8ba7bd7`](https://github.com/nodejs/node/commit/aac8ba7bd7)] - **test_runner**: fix mocking modules with quote in their URL (Antoine du Hamel) [#55083](https://github.com/nodejs/node/pull/55083)
- \[[`4f881790e9`](https://github.com/nodejs/node/commit/4f881790e9)] - **test_runner**: report error on missing sourcemap source (Aviv Keller) [#55037](https://github.com/nodejs/node/pull/55037)
- \[[`b264cbe5e8`](https://github.com/nodejs/node/commit/b264cbe5e8)] - **test_runner**: use `test:` symbol on second print of parent test (RedYetiDev) [#54956](https://github.com/nodejs/node/pull/54956)
- \[[`0c8c107aaa`](https://github.com/nodejs/node/commit/0c8c107aaa)] - **test_runner**: replace ansi clear with ansi reset (Pietro Marchini) [#55013](https://github.com/nodejs/node/pull/55013)
- \[[`bb405210c5`](https://github.com/nodejs/node/commit/bb405210c5)] - **test_runner**: support typescript module mocking (Marco Ippolito) [#54878](https://github.com/nodejs/node/pull/54878)
- \[[`50136a167d`](https://github.com/nodejs/node/commit/50136a167d)] - **test_runner**: avoid coverage report partial file names (Pietro Marchini) [#54379](https://github.com/nodejs/node/pull/54379)
- \[[`4988bb549e`](https://github.com/nodejs/node/commit/4988bb549e)] - **tools**: enforce ordering of error codes in `errors.md` (Antoine du Hamel) [#55324](https://github.com/nodejs/node/pull/55324)
- \[[`5a3da7b4e4`](https://github.com/nodejs/node/commit/5a3da7b4e4)] - **tools**: enforce errors to not be documented in legacy section (Aviv Keller) [#55218](https://github.com/nodejs/node/pull/55218)
- \[[`8dbca2d35b`](https://github.com/nodejs/node/commit/8dbca2d35b)] - **tools**: update gyp-next to 0.18.2 (Node.js GitHub Bot) [#55160](https://github.com/nodejs/node/pull/55160)
- \[[`b2161d3a13`](https://github.com/nodejs/node/commit/b2161d3a13)] - **tools**: bump the eslint group in /tools/eslint with 4 updates (dependabot\[bot]) [#55227](https://github.com/nodejs/node/pull/55227)
- \[[`e7d27320c3`](https://github.com/nodejs/node/commit/e7d27320c3)] - **tools**: only check teams on the default branch (Antoine du Hamel) [#55124](https://github.com/nodejs/node/pull/55124)
- \[[`e8127db032`](https://github.com/nodejs/node/commit/e8127db032)] - **tools**: make `choco install` script more readable (Aviv Keller) [#54002](https://github.com/nodejs/node/pull/54002)
- \[[`779e6bdd5e`](https://github.com/nodejs/node/commit/779e6bdd5e)] - **tools**: bump Rollup from 4.18.1 to 4.22.4 for `lint-md` (dependabot\[bot]) [#55093](https://github.com/nodejs/node/pull/55093)
- \[[`**********`](https://github.com/nodejs/node/commit/**********)] - **tools**: unlock versions of irrelevant DB deps (Michaël Zasso) [#55042](https://github.com/nodejs/node/pull/55042)
- \[[`f43424ac2d`](https://github.com/nodejs/node/commit/f43424ac2d)] - **tools**: remove redudant code from eslint require rule (Aviv Keller) [#54892](https://github.com/nodejs/node/pull/54892)
- \[[`6a52e81260`](https://github.com/nodejs/node/commit/6a52e81260)] - **tools**: update error message for ICU in license-builder (Aviv Keller) [#54742](https://github.com/nodejs/node/pull/54742)
- \[[`cde6dccb65`](https://github.com/nodejs/node/commit/cde6dccb65)] - **tools**: refactor js2c.cc to use c++20 (Yagiz Nizipli) [#54849](https://github.com/nodejs/node/pull/54849)
- \[[`59c7c55aad`](https://github.com/nodejs/node/commit/59c7c55aad)] - **tools**: bump the eslint group in /tools/eslint with 7 updates (dependabot\[bot]) [#54821](https://github.com/nodejs/node/pull/54821)
- \[[`c6269cb069`](https://github.com/nodejs/node/commit/c6269cb069)] - **tools**: fix path of abseil file in v8.gyp (Michaël Zasso) [#54659](https://github.com/nodejs/node/pull/54659)
- \[[`d17fefcd71`](https://github.com/nodejs/node/commit/d17fefcd71)] - **tools**: update github_reporter to 1.7.1 (Node.js GitHub Bot) [#54951](https://github.com/nodejs/node/pull/54951)
- \[[`29a4fcf918`](https://github.com/nodejs/node/commit/29a4fcf918)] - **tty**: fix links for terminal colors (Aviv Keller) [#54596](https://github.com/nodejs/node/pull/54596)
- \[[`e42ad5e80c`](https://github.com/nodejs/node/commit/e42ad5e80c)] - **util**: update ansi regex (Aviv Keller) [#54865](https://github.com/nodejs/node/pull/54865)
- \[[`b5aae52c71`](https://github.com/nodejs/node/commit/b5aae52c71)] - _**Revert**_ "**util**: move util.\_extend to eol" (Marco Ippolito) [#53429](https://github.com/nodejs/node/pull/53429)
- \[[`deb5effe01`](https://github.com/nodejs/node/commit/deb5effe01)] - **v8**: out of bounds copy (Robert Nagy) [#55261](https://github.com/nodejs/node/pull/55261)
- \[[`3b0617dd19`](https://github.com/nodejs/node/commit/3b0617dd19)] - **vm**: migrate ContextifyScript to cppgc (Joyee Cheung) [#52295](https://github.com/nodejs/node/pull/52295)
- \[[`35b8e5cb0c`](https://github.com/nodejs/node/commit/35b8e5cb0c)] - _**Revert**_ "**vm,src**: add property query interceptors" (Michaël Zasso) [#53348](https://github.com/nodejs/node/pull/53348)
- \[[`d1f18b0bf1`](https://github.com/nodejs/node/commit/d1f18b0bf1)] - **vm,src**: add property query interceptors (Michaël Zasso) [#53172](https://github.com/nodejs/node/pull/53172)
- \[[`89a2f565b7`](https://github.com/nodejs/node/commit/89a2f565b7)] - **watch**: preserve output when gracefully restarted (Théo LUDWIG) [#54323](https://github.com/nodejs/node/pull/54323)
- \[[`6b9413e41a`](https://github.com/nodejs/node/commit/6b9413e41a)] - **worker**: throw InvalidStateError in postMessage after close (devstone) [#55206](https://github.com/nodejs/node/pull/55206)
- \[[`6031a4bc7c`](https://github.com/nodejs/node/commit/6031a4bc7c)] - **worker**: handle `--input-type` more consistently (Antoine du Hamel) [#54979](https://github.com/nodejs/node/pull/54979)
- \[[`5b3f3c5a3b`](https://github.com/nodejs/node/commit/5b3f3c5a3b)] - **zlib**: throw brotli initialization error from c++ (Yagiz Nizipli) [#54698](https://github.com/nodejs/node/pull/54698)
- \[[`c42d8461b0`](https://github.com/nodejs/node/commit/c42d8461b0)] - **zlib**: remove prototype primordials usage (Yagiz Nizipli) [#54695](https://github.com/nodejs/node/pull/54695)

Windows 64-bit Installer: https://nodejs.org/dist/v23.0.0/node-v23.0.0-x64.msi \
Windows ARM 64-bit Installer: https://nodejs.org/dist/v23.0.0/node-v23.0.0-arm64.msi \
Windows 64-bit Binary: https://nodejs.org/dist/v23.0.0/win-x64/node.exe \
Windows ARM 64-bit Binary: https://nodejs.org/dist/v23.0.0/win-arm64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v23.0.0/node-v23.0.0.pkg \
macOS Apple Silicon 64-bit Binary: https://nodejs.org/dist/v23.0.0/node-v23.0.0-darwin-arm64.tar.gz \
macOS Intel 64-bit Binary: https://nodejs.org/dist/v23.0.0/node-v23.0.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v23.0.0/node-v23.0.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v23.0.0/node-v23.0.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v23.0.0/node-v23.0.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v23.0.0/node-v23.0.0-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v23.0.0/node-v23.0.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v23.0.0/node-v23.0.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v23.0.0/node-v23.0.0.tar.gz \
Other release files: https://nodejs.org/dist/v23.0.0/ \
Documentation: https://nodejs.org/docs/v23.0.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

1dd33c1cf52d16829283af113e90b5d1e8813ba6c8e1bef3f2808160fa078d93  node-v23.0.0-aix-ppc64.tar.gz
f058272692a954458be42a5a4cfe5234e39471cd9cc459ec4123d34fb437f08d  node-v23.0.0-arm64.msi
72ce7905b83f9499b92501675cf76e53b545cb9d0a42dca497fa80c8eb5fbcf9  node-v23.0.0-darwin-arm64.tar.gz
4e8d821e046f2af7712c7f1a0f42be095bb920bc039643cdf1cf3698b7b1ea17  node-v23.0.0-darwin-arm64.tar.xz
13915842f15bc32f76a24f8ea17fd43d650898d12d21c7b676b01d0e00dcb7fd  node-v23.0.0-darwin-x64.tar.gz
1c4ae0c9cb11dea40aceb11bd622ce1c2e4f12c076316150f7709dbd016882dc  node-v23.0.0-darwin-x64.tar.xz
ac1328fb5d5d1a58b7591a230dc63f46f1d3ec0e10fa371edbef3a67b45f1a58  node-v23.0.0-headers.tar.gz
d83e313738988ad49809c10815ce3d16f52ff374f6781a9014cfa3e80d9b1f9b  node-v23.0.0-headers.tar.xz
d908fc167d2242a50394e894d4ba258757a8a12b7bef29bd360f4f55c25acf3f  node-v23.0.0-linux-arm64.tar.gz
6140e29a66da2e57b5e561209de8f9c61e560305480d8c739d82f997df20599f  node-v23.0.0-linux-arm64.tar.xz
a37a90d903c30d757deaac78bd78e31c7a20b0244fe0f925535dddefb7ca1efc  node-v23.0.0-linux-armv7l.tar.gz
3bcc293201769317841128ae5049e0562ea5c29a27d64b65abb02dbad4ecb50e  node-v23.0.0-linux-armv7l.tar.xz
4f2ac682752c904cb142e2585ec957a711cdb11f9268c1227863129e3c7c7421  node-v23.0.0-linux-ppc64le.tar.gz
877b54081a5a3e074c8fdae46c5d0bcf03a8cd44ac6c5d70c9df977b4c3ed57e  node-v23.0.0-linux-ppc64le.tar.xz
41448aced57e51e91e914849d41a758303c130ae30033fb6352eb6754c5e9df8  node-v23.0.0-linux-s390x.tar.gz
de56f8823dd5a98b440a32ac2491df2427bca21b65d3f2bbfa43761a4635f5fc  node-v23.0.0-linux-s390x.tar.xz
702cbc710fcf1102cef1aced74443fee34eff8df4827de30ec970d377ce31d9e  node-v23.0.0-linux-x64.tar.gz
705857276700e61f9d141ac05681e4bd666bcd7f5461575fe60d2467d3722a12  node-v23.0.0-linux-x64.tar.xz
d384f7792812fdfdbbcce22da17684a65b21012e9644aec8eac986503723be57  node-v23.0.0-win-arm64.7z
749971643ec19ebedcb15e2fca2c842a3832a964bec3943f2600bf6761275a60  node-v23.0.0-win-arm64.zip
3978724c1ca71e18e0f2791c29ea8429a8586d253f41f503d71539294316c2ea  node-v23.0.0-win-x64.7z
d3dfb7e64c314b887832165966adf7f199c8f280ef38e66922e00221c47f182e  node-v23.0.0-win-x64.zip
b9323180d10cf565840a5976411cb6b6db5c11702fcebc95a6a5c64c0b0e111c  node-v23.0.0-x64.msi
6ee135d9ac5a3116085aaac709aeee80a76f9e09c290cbd7d493257badef3a34  node-v23.0.0.pkg
f686cd4be4994416e099ce23f9fe6ef3983f37c917d8d36963e17e4634a5edd1  node-v23.0.0.tar.gz
79c800b2c97c497bf7de21ac7435dc5f587c7d4735f750056ac6310d43b79eca  node-v23.0.0.tar.xz
cf9112e30e5556f0a1253374cceba4801086aa01a7d2fd6ff32ae82d98b1ede3  win-arm64/node.exe
10c78fa0a77d6dc065c381089a17845a81f87fa6547a5b645807e3bd36b0f33f  win-arm64/node.lib
b1227da5d51a99f09682016837a14f7ff8020080e8c9d839adca289790cd9f5d  win-arm64/node_pdb.7z
b2646fc94045b75fc3f9af59e3538bb61295601ad24e5abe02a5b400cbaf9017  win-arm64/node_pdb.zip
c86aca1f8d5b76c048233c9d90d6aa0d32a35f692bde4f088fdc2f963b4eeb11  win-x64/node.exe
d86a8d40cb8a3c23b1e7f1d5756d719da4c40f9998c5eb106eb9ae92fe00296b  win-x64/node.lib
c12dc932caa89d4dc4d8f32688e46cfc4f6a2efd327190c606e9becfc8fb7548  win-x64/node_pdb.7z
b8175d1a32d68fbf396b4ddb43c8091b6437464b7668800eec8555c2888eb2cb  win-x64/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQGzBAEBCAAdFiEEiQwI24V5Fi/uDfnbi+q0389VXvQFAmcPyJwACgkQi+q0389V
XvTqkgv/dkNgstURAHu/pBghDJjUmS6F6A2pAkb5Pe1+Jq7vAYtcm4KgKwqVSnOi
Yche6DH4fkMwyjUEgTbjKQ023IgZPd0TGs3pHn0qe0NK2qf+11LhTHIaRbPf/E60
rF7plV5zVzS/TE3SYF+akoxw/TAs0hFBt22g/H0W4YMRmiIidBfoRn+kkb3iWIA3
Dvk/mQBwXQZ6+1MvkzS4fVxGCASZHKECa9UTHbCsEhV3t5dWqqZYXSyZZGIlOpTU
kQ7n0mXrtK822NCTpGgjPzSZr1U/agwPHs3swuk4Zv95l2MLC2UFYwdh7QD1+xVx
VYC8XCq16zrHDVg6lsHmf3eIKh36YcHPSLtxrYHhWmTd62V3NnhWU2KoKnauRjfb
lP4XbtidWriT3yNQJCgaKgkPYic86QOFHeZqexn/Tqstt88VQCzALEqKfUIXrgoE
nIN3K+aA4awE6baFHEya6xCP6U8fpkWZVdLStWErUYG891YVCpo+0En2N2gYM6kK
6gLUSbl0
=sluh
-----END PGP SIGNATURE-----
```
