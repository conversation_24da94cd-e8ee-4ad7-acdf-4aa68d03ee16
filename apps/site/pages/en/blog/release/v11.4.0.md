---
date: '2018-12-07T18:13:43.812Z'
category: release
title: Node v11.4.0 (Current)
layout: blog-post
author: <PERSON><PERSON>
---

### Notable Changes

- **console,util**:
  - `console` functions now handle symbols as defined in the spec. https://github.com/nodejs/node/pull/23708
  - The inspection `depth` default is now back at 2. https://github.com/nodejs/node/pull/24326
- **dgram,net**:
  - Added ipv6Only option for `net` and `dgram`. https://github.com/nodejs/node/pull/23798
- **http**:
  - Choosing between the http parser is now possible per runtime flag. https://github.com/nodejs/node/pull/24739
- **readline**:
  - The `readline` module now supports async iterators. https://github.com/nodejs/node/pull/23916
- **repl**:
  - The multiline history feature is removed. https://github.com/nodejs/node/pull/24804
- **tls**:
  - Added min/max protocol version options. https://github.com/nodejs/node/pull/24405
  - The X.509 public key info now includes the RSA bit size and the elliptic curve. https://github.com/nodejs/node/pull/24358
- **url**:
  - `pathToFileURL()` now supports LF, CR and TAB. https://github.com/nodejs/node/pull/23720
- **Windows**:
  - Tools are not installed using Boxstarter anymore. https://github.com/nodejs/node/pull/24677
  - The install-tools scripts or now included in the dist. https://github.com/nodejs/node/pull/24233
- **Added new collaborator**:
  - [antsmartian](https://github.com/antsmartian) - Anto Aravinth. https://github.com/nodejs/node/pull/24655

### Commits

- [[`7fb8d319fa`](https://github.com/nodejs/node/commit/7fb8d319fa)] - **assert**: fix loose deepEqual map comparison (Ruben Bridgewater) [#24749](https://github.com/nodejs/node/pull/24749)
- [[`8905518650`](https://github.com/nodejs/node/commit/8905518650)] - **assert,util**: fix sparse array comparison (Ruben Bridgewater) [#24749](https://github.com/nodejs/node/pull/24749)
- [[`ef63bb287d`](https://github.com/nodejs/node/commit/ef63bb287d)] - **benchmark**: support URL inputs in create-clientrequest (Joyee Cheung) [#24302](https://github.com/nodejs/node/pull/24302)
- [[`f5d4db1e9c`](https://github.com/nodejs/node/commit/f5d4db1e9c)] - **benchmark**: pre-generate data set for URL benchmarks (Joyee Cheung) [#24302](https://github.com/nodejs/node/pull/24302)
- [[`73786c854a`](https://github.com/nodejs/node/commit/73786c854a)] - **buffer**: remove checkNumberType() (cjihrig) [#24815](https://github.com/nodejs/node/pull/24815)
- [[`a22ac0bb66`](https://github.com/nodejs/node/commit/a22ac0bb66)] - **build**: add '.git' to 'make lint-py' exclude list (cclauss) [#24802](https://github.com/nodejs/node/pull/24802)
- [[`bfec6a4eb3`](https://github.com/nodejs/node/commit/bfec6a4eb3)] - **build**: fix check-xz for platforms defaulting to sh (Rod Vagg) [#24841](https://github.com/nodejs/node/pull/24841)
- [[`3a24c91c7d`](https://github.com/nodejs/node/commit/3a24c91c7d)] - **build**: make tar.xz creation opt-out, fail if no xz (Rod Vagg) [#24551](https://github.com/nodejs/node/pull/24551)
- [[`6b71099303`](https://github.com/nodejs/node/commit/6b71099303)] - **build**: add line break as soon tests are done (Ruben Bridgewater) [#24748](https://github.com/nodejs/node/pull/24748)
- [[`e0e15da6ca`](https://github.com/nodejs/node/commit/e0e15da6ca)] - **build**: fix line length off by one error (Ruben Bridgewater) [#24748](https://github.com/nodejs/node/pull/24748)
- [[`3fe4498fe1`](https://github.com/nodejs/node/commit/3fe4498fe1)] - **build**: fix c++ code coverage on macOS (Refael Ackermann) [#24520](https://github.com/nodejs/node/pull/24520)
- [[`955819e0a3`](https://github.com/nodejs/node/commit/955819e0a3)] - **build**: only check REPLACEME & DEP...X for releases (Rod Vagg) [#24575](https://github.com/nodejs/node/pull/24575)
- [[`3fa4def6ea`](https://github.com/nodejs/node/commit/3fa4def6ea)] - **build**: replace `-not` with `!` in `find` (Rich Trott) [#24635](https://github.com/nodejs/node/pull/24635)
- [[`e37c6182e5`](https://github.com/nodejs/node/commit/e37c6182e5)] - **build**: fix Python detection when depot_tools are in PATH in Windows (Guy Bedford) [#22539](https://github.com/nodejs/node/pull/22539)
- [[`39614add79`](https://github.com/nodejs/node/commit/39614add79)] - **build**: remove sudo:false from .travis.yml (Rich Trott) [#24511](https://github.com/nodejs/node/pull/24511)
- [[`21e59a68cf`](https://github.com/nodejs/node/commit/21e59a68cf)] - **build**: use print() function in configure.py (cclauss) [#24484](https://github.com/nodejs/node/pull/24484)
- [[`4dc1e785a3`](https://github.com/nodejs/node/commit/4dc1e785a3)] - **build**: check minimum ICU in configure for system-icu (Steven R. Loomis) [#24255](https://github.com/nodejs/node/pull/24255)
- [[`c5e32fdebf`](https://github.com/nodejs/node/commit/c5e32fdebf)] - **build**: remove unnecessary prerequisite in Makefile (Rich Trott) [#24342](https://github.com/nodejs/node/pull/24342)
- [[`383d8092b1`](https://github.com/nodejs/node/commit/383d8092b1)] - **build, tools, win**: add .S files support to GYP (Bartosz Sosnowski) [#24553](https://github.com/nodejs/node/pull/24553)
- [[`bd4df5b326`](https://github.com/nodejs/node/commit/bd4df5b326)] - **build,src**: sync src files with node.gyp (Refael Ackermann) [#24505](https://github.com/nodejs/node/pull/24505)
- [[`331b26eda9`](https://github.com/nodejs/node/commit/331b26eda9)] - **build,tools**: update make-v8.sh for ppc64le (Refael Ackermann) [#24293](https://github.com/nodejs/node/pull/24293)
- [[`706bc414b9`](https://github.com/nodejs/node/commit/706bc414b9)] - **(SEMVER-MINOR)** **build,win**: pack the install-tools scripts for dist (Refael Ackermann) [#24233](https://github.com/nodejs/node/pull/24233)
- [[`b214ae44c8`](https://github.com/nodejs/node/commit/b214ae44c8)] - **cli**: add missing env vars to --help (cjihrig) [#24383](https://github.com/nodejs/node/pull/24383)
- [[`50005e7ddf`](https://github.com/nodejs/node/commit/50005e7ddf)] - **console**: improve code readability (gengjiawen) [#24412](https://github.com/nodejs/node/pull/24412)
- [[`12feb9e492`](https://github.com/nodejs/node/commit/12feb9e492)] - **crypto**: harden bignum-to-binary conversions (Ben Noordhuis) [#24719](https://github.com/nodejs/node/pull/24719)
- [[`c15efcec92`](https://github.com/nodejs/node/commit/c15efcec92)] - **crypto**: convert to arrow function (yosuke ota) [#24597](https://github.com/nodejs/node/pull/24597)
- [[`16d70603a1`](https://github.com/nodejs/node/commit/16d70603a1)] - **crypto**: allow monkey patching of pseudoRandomBytes (Gerhard Stoebich) [#24108](https://github.com/nodejs/node/pull/24108)
- [[`7c29e9b83b`](https://github.com/nodejs/node/commit/7c29e9b83b)] - **crypto**: remove unnecessary fully qualified names (Gagandeep Singh) [#24452](https://github.com/nodejs/node/pull/24452)
- [[`0afcb9ad3a`](https://github.com/nodejs/node/commit/0afcb9ad3a)] - **deps**: cherry-pick 88f8fe1 from upstream V8 (Yang Guo) [#24514](https://github.com/nodejs/node/pull/24514)
- [[`61179e6cfe`](https://github.com/nodejs/node/commit/61179e6cfe)] - **deps**: cherry-pick 073073b from upstream V8 (Yang Guo) [#24515](https://github.com/nodejs/node/pull/24515)
- [[`230eb0dde9`](https://github.com/nodejs/node/commit/230eb0dde9)] - **deps**: update llhttp to 1.0.1 (Fedor Indutny) [#24508](https://github.com/nodejs/node/pull/24508)
- [[`06c28b9d75`](https://github.com/nodejs/node/commit/06c28b9d75)] - **deps**: upgrade to libuv 1.24.0 (cjihrig) [#24332](https://github.com/nodejs/node/pull/24332)
- [[`2dfaa480de`](https://github.com/nodejs/node/commit/2dfaa480de)] - **dns**: simplify dns.promises warning logic (cjihrig) [#24788](https://github.com/nodejs/node/pull/24788)
- [[`5a1fb1e663`](https://github.com/nodejs/node/commit/5a1fb1e663)] - **doc**: mention util depth default change (Ruben Bridgewater) [#24805](https://github.com/nodejs/node/pull/24805)
- [[`d800998161`](https://github.com/nodejs/node/commit/d800998161)] - **doc**: list all versions WHATWG URL api was added (Thomas Watson) [#24847](https://github.com/nodejs/node/pull/24847)
- [[`71e520cfa6`](https://github.com/nodejs/node/commit/71e520cfa6)] - **doc**: add authority and scheme psuedo headers (Kenigbolo Meya Stephen) [#24777](https://github.com/nodejs/node/pull/24777)
- [[`5b78d2c504`](https://github.com/nodejs/node/commit/5b78d2c504)] - **doc**: remove duplicate whitespaces in doc/api (Yusuke Kawasaki)
- [[`162b3a12b6`](https://github.com/nodejs/node/commit/162b3a12b6)] - **doc**: add triaging section to releases.md (Beth Griggs) [#20165](https://github.com/nodejs/node/pull/20165)
- [[`b8611a384a`](https://github.com/nodejs/node/commit/b8611a384a)] - **doc**: use author's titles for linked resources (Rich Trott) [#24837](https://github.com/nodejs/node/pull/24837)
- [[`566046ca4e`](https://github.com/nodejs/node/commit/566046ca4e)] - **doc**: revise code review guidelines (Rich Trott) [#24790](https://github.com/nodejs/node/pull/24790)
- [[`3d1853b178`](https://github.com/nodejs/node/commit/3d1853b178)] - **doc**: add a note on usage scope of AliasedBuffer (Gireesh Punathil) [#24724](https://github.com/nodejs/node/pull/24724)
- [[`997c0e05a4`](https://github.com/nodejs/node/commit/997c0e05a4)] - **doc**: hide undocumented object artifacts in async_hooks (Gireesh Punathil) [#24741](https://github.com/nodejs/node/pull/24741)
- [[`58e5c00c9b`](https://github.com/nodejs/node/commit/58e5c00c9b)] - **doc**: fix added version of randomFill+randomFillSync (Thomas Watson) [#24812](https://github.com/nodejs/node/pull/24812)
- [[`751d961d29`](https://github.com/nodejs/node/commit/751d961d29)] - **doc**: streamline Accepting Modifications in Collaborator Guide (Rich Trott) [#24807](https://github.com/nodejs/node/pull/24807)
- [[`c09ea83869`](https://github.com/nodejs/node/commit/c09ea83869)] - **doc**: make release README link be consistent with text (ZYSzys) [#24783](https://github.com/nodejs/node/pull/24783)
- [[`06011f501d`](https://github.com/nodejs/node/commit/06011f501d)] - **doc**: fix REPLACEME for tls min/max protocol option (Sam Roberts) [#24759](https://github.com/nodejs/node/pull/24759)
- [[`4d41c8f6d6`](https://github.com/nodejs/node/commit/4d41c8f6d6)] - **doc**: add missing changes entry (Ruben Bridgewater) [#24758](https://github.com/nodejs/node/pull/24758)
- [[`25e5164cf1`](https://github.com/nodejs/node/commit/25e5164cf1)] - **doc**: cookie is joined using '; ' (Gerhard Stoebich) [#24740](https://github.com/nodejs/node/pull/24740)
- [[`66d83305f8`](https://github.com/nodejs/node/commit/66d83305f8)] - **doc**: sort bottom-of-file markdown links (Sam Roberts) [#24679](https://github.com/nodejs/node/pull/24679)
- [[`654bd65464`](https://github.com/nodejs/node/commit/654bd65464)] - **doc**: remove trailing whitespace (Daijiro Wachi) [#24642](https://github.com/nodejs/node/pull/24642)
- [[`68dc100565`](https://github.com/nodejs/node/commit/68dc100565)] - **doc**: describe current HTTP header size limit (Sam Roberts) [#24700](https://github.com/nodejs/node/pull/24700)
- [[`b3e77a5690`](https://github.com/nodejs/node/commit/b3e77a5690)] - **doc**: fix nits in http(s) server.headersTimeout (Vse Mozhet Byt) [#24697](https://github.com/nodejs/node/pull/24697)
- [[`3288c27453`](https://github.com/nodejs/node/commit/3288c27453)] - **doc**: add antsmartian to collaborators (Anto Aravinth) [#24655](https://github.com/nodejs/node/pull/24655)
- [[`85aa03085d`](https://github.com/nodejs/node/commit/85aa03085d)] - **doc**: revise accepting-modifications in guide (Rich Trott) [#24650](https://github.com/nodejs/node/pull/24650)
- [[`2ebb32b480`](https://github.com/nodejs/node/commit/2ebb32b480)] - **doc**: document fs.write limitation with TTY (Matteo Collina) [#24571](https://github.com/nodejs/node/pull/24571)
- [[`5a47c2e7d3`](https://github.com/nodejs/node/commit/5a47c2e7d3)] - **doc**: clarify symlink resolution for \_\_filename (Rich Trott) [#24587](https://github.com/nodejs/node/pull/24587)
- [[`b65ffd5b1d`](https://github.com/nodejs/node/commit/b65ffd5b1d)] - **doc**: use arrow function for anonymous callbacks (koki-oshima) [#24606](https://github.com/nodejs/node/pull/24606)
- [[`d4491a48ba`](https://github.com/nodejs/node/commit/d4491a48ba)] - **doc**: revise handling-own-pull-requests text (Rich Trott) [#24583](https://github.com/nodejs/node/pull/24583)
- [[`663d1c8823`](https://github.com/nodejs/node/commit/663d1c8823)] - **doc**: fix duplicate "this" and "the" on http2.md (Yusuke Kawasaki) [#24611](https://github.com/nodejs/node/pull/24611)
- [[`8d550f7888`](https://github.com/nodejs/node/commit/8d550f7888)] - **doc**: replace anonymous function with arrow function (ka2jun8) [#24617](https://github.com/nodejs/node/pull/24617)
- [[`657d7a5f9d`](https://github.com/nodejs/node/commit/657d7a5f9d)] - **doc**: use arrow function (sadness_ojisan) [#24590](https://github.com/nodejs/node/pull/24590)
- [[`f80e7a13fb`](https://github.com/nodejs/node/commit/f80e7a13fb)] - **doc**: replace anonymous function with arrow function (yuriettys) [#24627](https://github.com/nodejs/node/pull/24627)
- [[`5796c6aba4`](https://github.com/nodejs/node/commit/5796c6aba4)] - **doc**: mark napi_add_finalizer experimental (Michael Dawson) [#24572](https://github.com/nodejs/node/pull/24572)
- [[`4da44ada88`](https://github.com/nodejs/node/commit/4da44ada88)] - **doc**: clarify who may land on an LTS staging branch (Myles Borins) [#24465](https://github.com/nodejs/node/pull/24465)
- [[`7463a7f5cf`](https://github.com/nodejs/node/commit/7463a7f5cf)] - **doc**: revise `author ready` explanation (Rich Trott) [#24558](https://github.com/nodejs/node/pull/24558)
- [[`41f2e36046`](https://github.com/nodejs/node/commit/41f2e36046)] - **doc**: add readable and writable property to Readable and Writable (Dexter Leng) [#23933](https://github.com/nodejs/node/pull/23933)
- [[`580eb5ba66`](https://github.com/nodejs/node/commit/580eb5ba66)] - **doc**: move trott to tsc emeritus (Rich Trott) [#24492](https://github.com/nodejs/node/pull/24492)
- [[`1a74fad1cd`](https://github.com/nodejs/node/commit/1a74fad1cd)] - **doc**: add Ruben Bridgewater to release team (Ruben Bridgewater) [#23432](https://github.com/nodejs/node/pull/23432)
- [[`672a31c91b`](https://github.com/nodejs/node/commit/672a31c91b)] - **doc**: edit COLLABORATOR_GUIDE.md on closing issues (Rich Trott) [#24477](https://github.com/nodejs/node/pull/24477)
- [[`6d147efa92`](https://github.com/nodejs/node/commit/6d147efa92)] - **doc**: move Timothy to TSC emeritus (Timothy Gu) [#24535](https://github.com/nodejs/node/pull/24535)
- [[`91494bf023`](https://github.com/nodejs/node/commit/91494bf023)] - **doc**: add NODE_DEBUG_NATIVE to API docs (cjihrig) [#24383](https://github.com/nodejs/node/pull/24383)
- [[`6e4a12062a`](https://github.com/nodejs/node/commit/6e4a12062a)] - **doc**: add missing env variables to man page (cjihrig) [#24383](https://github.com/nodejs/node/pull/24383)
- [[`48852cc51f`](https://github.com/nodejs/node/commit/48852cc51f)] - **doc**: minor cleanup of tls.getProtocol() (Sam Roberts) [#24533](https://github.com/nodejs/node/pull/24533)
- [[`d34527177c`](https://github.com/nodejs/node/commit/d34527177c)] - **doc**: add Beth Griggs to release team (Beth Griggs) [#24532](https://github.com/nodejs/node/pull/24532)
- [[`dadc2eb62d`](https://github.com/nodejs/node/commit/dadc2eb62d)] - **(SEMVER-MINOR)** **doc**: describe certificate object properties (Sam Roberts) [#24358](https://github.com/nodejs/node/pull/24358)
- [[`9ab2bcf97c`](https://github.com/nodejs/node/commit/9ab2bcf97c)] - **doc**: update 11.0.0 changelog with missing commit (Rich Trott) [#24404](https://github.com/nodejs/node/pull/24404)
- [[`a499db714c`](https://github.com/nodejs/node/commit/a499db714c)] - **doc**: add filehandle.write(string\[, position\[, encoding\]\]) (Dara Hayes) [#23224](https://github.com/nodejs/node/pull/23224)
- [[`cf2306d380`](https://github.com/nodejs/node/commit/cf2306d380)] - **doc**: udpate list item spacing in changelogs (Rich Trott) [#24391](https://github.com/nodejs/node/pull/24391)
- [[`ed78339a6b`](https://github.com/nodejs/node/commit/ed78339a6b)] - **doc**: update crypto examples to not use deprecated api (Mayank Asthana) [#24107](https://github.com/nodejs/node/pull/24107)
- [[`5c4f569857`](https://github.com/nodejs/node/commit/5c4f569857)] - **doc**: simplify first-time contributors section of Collaborator Guide (Rich Trott) [#24387](https://github.com/nodejs/node/pull/24387)
- [[`81ec97ba3d`](https://github.com/nodejs/node/commit/81ec97ba3d)] - **doc**: adjusting formatting when printing (Thomas Hunter II) [#24325](https://github.com/nodejs/node/pull/24325)
- [[`a3599a5067`](https://github.com/nodejs/node/commit/a3599a5067)] - **doc**: better linkage to node-addon-api (Michael Dawson) [#24371](https://github.com/nodejs/node/pull/24371)
- [[`5f747f1dc5`](https://github.com/nodejs/node/commit/5f747f1dc5)] - **doc**: add help on fixing IPv6 test failures (Michael Dawson) [#24372](https://github.com/nodejs/node/pull/24372)
- [[`85f9201687`](https://github.com/nodejs/node/commit/85f9201687)] - **doc**: update collaborator guide with LTS labels (Charalampos Fanoulis) [#24379](https://github.com/nodejs/node/pull/24379)
- [[`2245e5e484`](https://github.com/nodejs/node/commit/2245e5e484)] - **doc,meta**: update PR approving info (Vse Mozhet Byt) [#24561](https://github.com/nodejs/node/pull/24561)
- [[`1743568975`](https://github.com/nodejs/node/commit/1743568975)] - **esm**: refactor dynamic modules (Myles Borins) [#24560](https://github.com/nodejs/node/pull/24560)
- [[`dd89cfeb30`](https://github.com/nodejs/node/commit/dd89cfeb30)] - **events**: extract listener check as a function (ZYSzys) [#24303](https://github.com/nodejs/node/pull/24303)
- [[`124fca0267`](https://github.com/nodejs/node/commit/124fca0267)] - **fs**: simplify fs.promises warning logic (cjihrig) [#24788](https://github.com/nodejs/node/pull/24788)
- [[`b1622a2c92`](https://github.com/nodejs/node/commit/b1622a2c92)] - **fs**: inline typeof check (dexterleng) [#24390](https://github.com/nodejs/node/pull/24390)
- [[`c8d5e31db4`](https://github.com/nodejs/node/commit/c8d5e31db4)] - **(SEMVER-MINOR)** **http**: make parser choice a runtime flag (Anna Henningsen) [#24739](https://github.com/nodejs/node/pull/24739)
- [[`1f8787c32d`](https://github.com/nodejs/node/commit/1f8787c32d)] - **http**: destroy the socket on parse error (Luigi Pinca) [#24757](https://github.com/nodejs/node/pull/24757)
- [[`3fe3bc961f`](https://github.com/nodejs/node/commit/3fe3bc961f)] - **http**: fix error return in `Finish()` (Fedor Indutny) [#24738](https://github.com/nodejs/node/pull/24738)
- [[`798504a8c9`](https://github.com/nodejs/node/commit/798504a8c9)] - **http2**: make compat writeHead not crash if the stream is destroyed (Matteo Collina) [#24723](https://github.com/nodejs/node/pull/24723)
- [[`61e0103d60`](https://github.com/nodejs/node/commit/61e0103d60)] - **http2**: add compat support for nested array headers (Sebastiaan Deckers) [#24665](https://github.com/nodejs/node/pull/24665)
- [[`091238a9a7`](https://github.com/nodejs/node/commit/091238a9a7)] - **http2**: fix session\[kSession\] undefined issue (leeight) [#24547](https://github.com/nodejs/node/pull/24547)
- [[`5051e1bdab`](https://github.com/nodejs/node/commit/5051e1bdab)] - **http2**: cleanup endStream logic (James M Snell) [#24063](https://github.com/nodejs/node/pull/24063)
- [[`81a7056378`](https://github.com/nodejs/node/commit/81a7056378)] - **http2**: set js callbacks once (James M Snell) [#24063](https://github.com/nodejs/node/pull/24063)
- [[`cd7df56903`](https://github.com/nodejs/node/commit/cd7df56903)] - **http2**: throw from mapToHeaders (James M Snell) [#24063](https://github.com/nodejs/node/pull/24063)
- [[`f5e9bb1b39`](https://github.com/nodejs/node/commit/f5e9bb1b39)] - **http2**: replace unreachable error with assertion (Rich Trott) [#24407](https://github.com/nodejs/node/pull/24407)
- [[`1f544999af`](https://github.com/nodejs/node/commit/1f544999af)] - **http2**: order declarations in http2.js (ZYSzys) [#24411](https://github.com/nodejs/node/pull/24411)
- [[`454883b6ce`](https://github.com/nodejs/node/commit/454883b6ce)] - **http2**: elevate v8 namespaces of repeated references (Gagandeep Singh) [#24453](https://github.com/nodejs/node/pull/24453)
- [[`73bc5fd39a`](https://github.com/nodejs/node/commit/73bc5fd39a)] - **_Revert_** "**lib**: repl multiline history support" (Ruben Bridgewater) [#24804](https://github.com/nodejs/node/pull/24804)
- [[`6c8a73de33`](https://github.com/nodejs/node/commit/6c8a73de33)] - **lib**: remove some useless assignments (Gus Caplan) [#23199](https://github.com/nodejs/node/pull/23199)
- [[`1ec4f8dc3d`](https://github.com/nodejs/node/commit/1ec4f8dc3d)] - **lib**: remove duplicated noop function (ZYSzys) [#24770](https://github.com/nodejs/node/pull/24770)
- [[`eab981e76f`](https://github.com/nodejs/node/commit/eab981e76f)] - **lib**: do not register DOMException in a module (Joyee Cheung) [#24708](https://github.com/nodejs/node/pull/24708)
- [[`d77cf929cf`](https://github.com/nodejs/node/commit/d77cf929cf)] - **lib**: move setupAllowedFlags() into per_thread.js (Joyee Cheung) [#24704](https://github.com/nodejs/node/pull/24704)
- [[`b1d3747b5b`](https://github.com/nodejs/node/commit/b1d3747b5b)] - **lib**: convert to arrow function in fs.js (exoego) [#24604](https://github.com/nodejs/node/pull/24604)
- [[`97b803fa13`](https://github.com/nodejs/node/commit/97b803fa13)] - **lib**: change callbacks to arrow function (/Jesse) [#24625](https://github.com/nodejs/node/pull/24625)
- [[`1c4bc86388`](https://github.com/nodejs/node/commit/1c4bc86388)] - **lib**: chenged anonymous function to arrow function (nakashima) [#24605](https://github.com/nodejs/node/pull/24605)
- [[`83ab5f4049`](https://github.com/nodejs/node/commit/83ab5f4049)] - **lib**: rearm pre-existing signal event registrations (Gireesh Punathil) [#24651](https://github.com/nodejs/node/pull/24651)
- [[`6f42b98a1a`](https://github.com/nodejs/node/commit/6f42b98a1a)] - **lib**: convert to arrow function (horihiro) [#24623](https://github.com/nodejs/node/pull/24623)
- [[`e5c85ef886`](https://github.com/nodejs/node/commit/e5c85ef886)] - **lib**: convert to Arrow Function (Daiki Arai) [#24615](https://github.com/nodejs/node/pull/24615)
- [[`1063e0c92c`](https://github.com/nodejs/node/commit/1063e0c92c)] - **lib**: fix comment nits in bootstrap\\loaders.js (Vse Mozhet Byt) [#24641](https://github.com/nodejs/node/pull/24641)
- [[`3df8633b86`](https://github.com/nodejs/node/commit/3df8633b86)] - **lib**: suppress crypto related env vars in help msg (Daniel Bevenius) [#24556](https://github.com/nodejs/node/pull/24556)
- [[`59c2ee0c37`](https://github.com/nodejs/node/commit/59c2ee0c37)] - **lib**: convert to arrow function (Naojirou Hisada) [#24596](https://github.com/nodejs/node/pull/24596)
- [[`a8e93f7691`](https://github.com/nodejs/node/commit/a8e93f7691)] - **lib**: change anonymous function to arrow function (takato) [#24589](https://github.com/nodejs/node/pull/24589)
- [[`b2c243ff8b`](https://github.com/nodejs/node/commit/b2c243ff8b)] - **lib**: simplify own keys retrieval (Vse Mozhet Byt) [#24582](https://github.com/nodejs/node/pull/24582)
- [[`35a76460b8`](https://github.com/nodejs/node/commit/35a76460b8)] - **lib**: fix nits in lib/internal/bootstrap/cache.js (Vse Mozhet Byt) [#24581](https://github.com/nodejs/node/pull/24581)
- [[`daeb34809a`](https://github.com/nodejs/node/commit/daeb34809a)] - **lib**: move encodeStr function to internal for reusable (ZYSzys) [#24242](https://github.com/nodejs/node/pull/24242)
- [[`e14abfe432`](https://github.com/nodejs/node/commit/e14abfe432)] - **lib**: refactor setupInspector in bootstrap/node.js (leeight) [#24446](https://github.com/nodejs/node/pull/24446)
- [[`e16ff521d4`](https://github.com/nodejs/node/commit/e16ff521d4)] - **lib**: set stderr.\_destroy to dummyDestroy (Joyee Cheung) [#24398](https://github.com/nodejs/node/pull/24398)
- [[`bc5a0d3c05`](https://github.com/nodejs/node/commit/bc5a0d3c05)] - **lib**: gather all errors constant in the same place for consistency (ZYSzys) [#24038](https://github.com/nodejs/node/pull/24038)
- [[`0c51fc51b0`](https://github.com/nodejs/node/commit/0c51fc51b0)] - **n-api**: handle reference delete before finalize (Michael Dawson) [#24494](https://github.com/nodejs/node/pull/24494)
- [[`7ef516a9de`](https://github.com/nodejs/node/commit/7ef516a9de)] - **n-api,test**: remove last argument in assert.strictEqual() (susantruong) [#24584](https://github.com/nodejs/node/pull/24584)
- [[`e82f67d710`](https://github.com/nodejs/node/commit/e82f67d710)] - **_Revert_** "**net**: partially revert "simplify Socket.prototype.\_final"" (Anna Henningsen) [#24290](https://github.com/nodejs/node/pull/24290)
- [[`a1254a3e90`](https://github.com/nodejs/node/commit/a1254a3e90)] - **(SEMVER-MINOR)** **net,dgram**: add ipv6Only option for net and dgram (Ouyang Yadong) [#23798](https://github.com/nodejs/node/pull/23798)
- [[`24acd53cc4`](https://github.com/nodejs/node/commit/24acd53cc4)] - **net,http2**: merge after-write code (Anna Henningsen) [#24380](https://github.com/nodejs/node/pull/24380)
- [[`5874a03f39`](https://github.com/nodejs/node/commit/5874a03f39)] - **process**: refactor the bootstrap mode branching for readability (Joyee Cheung) [#24673](https://github.com/nodejs/node/pull/24673)
- [[`effe30777b`](https://github.com/nodejs/node/commit/effe30777b)] - **process**: fix omitting `--` from `process.execArgv` (Anna Henningsen) [#24654](https://github.com/nodejs/node/pull/24654)
- [[`81b42d2258`](https://github.com/nodejs/node/commit/81b42d2258)] - **process**: emit unhandled warning immediately (Anatoli Papirovski) [#24632](https://github.com/nodejs/node/pull/24632)
- [[`b22e95d5ed`](https://github.com/nodejs/node/commit/b22e95d5ed)] - **(SEMVER-MINOR)** **readline**: add support for async iteration (Timothy Gu) [#23916](https://github.com/nodejs/node/pull/23916)
- [[`6fed6f5e1f`](https://github.com/nodejs/node/commit/6fed6f5e1f)] - **_Revert_** "**repl**: handle buffered string logic on finish" (Ruben Bridgewater) [#24804](https://github.com/nodejs/node/pull/24804)
- [[`bd8be407b1`](https://github.com/nodejs/node/commit/bd8be407b1)] - **repl**: handle buffered string logic on finish (Anto Aravinth) [#24389](https://github.com/nodejs/node/pull/24389)
- [[`5bd33f18ea`](https://github.com/nodejs/node/commit/5bd33f18ea)] - **src**: fix type mismatch warnings from missing priv (Sam Roberts) [#24737](https://github.com/nodejs/node/pull/24737)
- [[`7c70b6192b`](https://github.com/nodejs/node/commit/7c70b6192b)] - **src**: move version metadata into node_metadata{.h, .cc} (Joyee Cheung) [#24774](https://github.com/nodejs/node/pull/24774)
- [[`53b59b4066`](https://github.com/nodejs/node/commit/53b59b4066)] - **src**: move READONLY\_\* macros into util.h (Joyee Cheung) [#24774](https://github.com/nodejs/node/pull/24774)
- [[`c957adb171`](https://github.com/nodejs/node/commit/c957adb171)] - **src**: use custom TryCatch subclass (Gus Caplan) [#24751](https://github.com/nodejs/node/pull/24751)
- [[`ecbe616b9d`](https://github.com/nodejs/node/commit/ecbe616b9d)] - **src**: use arraysize instead of hardcode number (leeight) [#24473](https://github.com/nodejs/node/pull/24473)
- [[`0e88f44547`](https://github.com/nodejs/node/commit/0e88f44547)] - **src**: set HAS_USERNAME/PASSWORD more strictly (Timothy Gu) [#24495](https://github.com/nodejs/node/pull/24495)
- [[`193f315560`](https://github.com/nodejs/node/commit/193f315560)] - **src**: elevate v8 namespaces for node_process.cc (Jayasankar) [#24578](https://github.com/nodejs/node/pull/24578)
- [[`f28fdc96ef`](https://github.com/nodejs/node/commit/f28fdc96ef)] - **src**: remove unused context variable in node_serdes (Daniel Bevenius) [#24713](https://github.com/nodejs/node/pull/24713)
- [[`0148c1d4f9`](https://github.com/nodejs/node/commit/0148c1d4f9)] - **src**: elevate v8 namespaces referenced (Juan José Arboleda) [#24657](https://github.com/nodejs/node/pull/24657)
- [[`f31292dff3`](https://github.com/nodejs/node/commit/f31292dff3)] - **src**: move C++ binding/addon related code into node_binding{.h, .cc} (Joyee Cheung) [#24701](https://github.com/nodejs/node/pull/24701)
- [[`87c864cd5e`](https://github.com/nodejs/node/commit/87c864cd5e)] - **src**: remove unused variables in node_util.cc (Daniel Bevenius) [#24717](https://github.com/nodejs/node/pull/24717)
- [[`a122ba598e`](https://github.com/nodejs/node/commit/a122ba598e)] - **src**: simplify LibuvStreamWrap::DoWrite (Anna Henningsen) [#24588](https://github.com/nodejs/node/pull/24588)
- [[`b554ff7620`](https://github.com/nodejs/node/commit/b554ff7620)] - **src**: replace create new Array (kohta ito) [#24618](https://github.com/nodejs/node/pull/24618)
- [[`c26b10caeb`](https://github.com/nodejs/node/commit/c26b10caeb)] - **src**: migrate to new V8 array API (Yoshiya Hinosawa) [#24613](https://github.com/nodejs/node/pull/24613)
- [[`c708abb3ba`](https://github.com/nodejs/node/commit/c708abb3ba)] - **src**: use NativeModuleLoader to compile per_context.js (Joyee Cheung) [#24660](https://github.com/nodejs/node/pull/24660)
- [[`9caad06d6f`](https://github.com/nodejs/node/commit/9caad06d6f)] - **src**: simplify uptime and ppid return values (cjihrig) [#24562](https://github.com/nodejs/node/pull/24562)
- [[`dca1ecffbd`](https://github.com/nodejs/node/commit/dca1ecffbd)] - **src**: replace array implementation (kazuya kawaguchi) [#24614](https://github.com/nodejs/node/pull/24614)
- [[`955a8a720a`](https://github.com/nodejs/node/commit/955a8a720a)] - **src**: replace new Array creation (kohta ito) [#24601](https://github.com/nodejs/node/pull/24601)
- [[`8a91fc1af0`](https://github.com/nodejs/node/commit/8a91fc1af0)] - **src**: elevate v8 namespaces for node_url.cc (Jayasankar) [#24573](https://github.com/nodejs/node/pull/24573)
- [[`aa220cf9d7`](https://github.com/nodejs/node/commit/aa220cf9d7)] - **src**: enable detailed source positions in V8 (Yang Guo) [#24515](https://github.com/nodejs/node/pull/24515)
- [[`b9bd4e9d09`](https://github.com/nodejs/node/commit/b9bd4e9d09)] - **src**: add include for standalone compile (Gary Hsu) [#24498](https://github.com/nodejs/node/pull/24498)
- [[`2565ff0785`](https://github.com/nodejs/node/commit/2565ff0785)] - **src**: elevate namespaces for repeated entities (Sarath Govind K K) [#24475](https://github.com/nodejs/node/pull/24475)
- [[`b8ed930674`](https://github.com/nodejs/node/commit/b8ed930674)] - **src**: elevate namespaces of repeated artifacts (Maya Anilson) [#24429](https://github.com/nodejs/node/pull/24429)
- [[`216f751b2a`](https://github.com/nodejs/node/commit/216f751b2a)] - **src**: elevate v8 namespaces of node_trace_events.cc (Jayasankar) [#24469](https://github.com/nodejs/node/pull/24469)
- [[`21e9aa2bf4`](https://github.com/nodejs/node/commit/21e9aa2bf4)] - **src**: use STL containers instead of v8 values for static module data (Joyee Cheung) [#24384](https://github.com/nodejs/node/pull/24384)
- [[`873dee9789`](https://github.com/nodejs/node/commit/873dee9789)] - **src**: elevate v8 namespaces of repeated references (leeight) [#24460](https://github.com/nodejs/node/pull/24460)
- [[`aa481c4198`](https://github.com/nodejs/node/commit/aa481c4198)] - **src**: elevate repeated use of v8 namespaced type (Shubham Urkade) [#24427](https://github.com/nodejs/node/pull/24427)
- [[`ea862acc7a`](https://github.com/nodejs/node/commit/ea862acc7a)] - **src**: use smart pointers in cares_wrap.cc (Daniel Bevenius) [#23813](https://github.com/nodejs/node/pull/23813)
- [[`53fac5c0d3`](https://github.com/nodejs/node/commit/53fac5c0d3)] - **src**: fix compiler warning (cjihrig) [#23954](https://github.com/nodejs/node/pull/23954)
- [[`c2fde2124f`](https://github.com/nodejs/node/commit/c2fde2124f)] - **src**: remove unused variables (Anna Henningsen) [#23880](https://github.com/nodejs/node/pull/23880)
- [[`dba003cbff`](https://github.com/nodejs/node/commit/dba003cbff)] - **src**: include util-inl.h in worker_agent.cc (Anna Henningsen) [#23880](https://github.com/nodejs/node/pull/23880)
- [[`25a9eee9fd`](https://github.com/nodejs/node/commit/25a9eee9fd)] - **src**: add direct dependency on `*-inl.h` file (Refael Ackermann) [#23808](https://github.com/nodejs/node/pull/23808)
- [[`33e7f6e953`](https://github.com/nodejs/node/commit/33e7f6e953)] - **src**: add AliasedBuffer::reserve (Refael Ackermann) [#23808](https://github.com/nodejs/node/pull/23808)
- [[`74c0a97a96`](https://github.com/nodejs/node/commit/74c0a97a96)] - **src**: clean clang-tidy errors in node_file.h (Refael Ackermann) [#23793](https://github.com/nodejs/node/pull/23793)
- [[`260d77710e`](https://github.com/nodejs/node/commit/260d77710e)] - **src**: fix resource leak in node::fs::FileHandle (Refael Ackermann) [#23793](https://github.com/nodejs/node/pull/23793)
- [[`c0a9a83c51`](https://github.com/nodejs/node/commit/c0a9a83c51)] - **src**: refactor FillStatsArray (Refael Ackermann) [#23793](https://github.com/nodejs/node/pull/23793)
- [[`5061610094`](https://github.com/nodejs/node/commit/5061610094)] - **src**: remove `Environment::tracing_agent_writer()` (Anna Henningsen) [#23781](https://github.com/nodejs/node/pull/23781)
- [[`af3c7efffc`](https://github.com/nodejs/node/commit/af3c7efffc)] - **src**: factor out Node.js-agnostic N-APIs (Gabriel Schulhof) [#23786](https://github.com/nodejs/node/pull/23786)
- [[`b44623e776`](https://github.com/nodejs/node/commit/b44623e776)] - **src**: elevate v8 namespaces of referenced artifacts (Kanika Singhal) [#24424](https://github.com/nodejs/node/pull/24424)
- [[`a7f6c043a4`](https://github.com/nodejs/node/commit/a7f6c043a4)] - **_Revert_** "**src**: enable detailed source positions in V8" (Refael Ackermann) [#24394](https://github.com/nodejs/node/pull/24394)
- [[`5d67eeca1a`](https://github.com/nodejs/node/commit/5d67eeca1a)] - **src**: emit warnings from V8 (Gus Caplan) [#24365](https://github.com/nodejs/node/pull/24365)
- [[`fa9e03c1a7`](https://github.com/nodejs/node/commit/fa9e03c1a7)] - **src**: re-sort the symbol macros (Sam Roberts) [#24382](https://github.com/nodejs/node/pull/24382)
- [[`2d885ed0f9`](https://github.com/nodejs/node/commit/2d885ed0f9)] - **src**: fix compiler warning in node_os (Daniel Bevenius) [#24356](https://github.com/nodejs/node/pull/24356)
- [[`806570d80a`](https://github.com/nodejs/node/commit/806570d80a)] - **src**: remove unused variables (Daniel Bevenius) [#24355](https://github.com/nodejs/node/pull/24355)
- [[`88a54497e5`](https://github.com/nodejs/node/commit/88a54497e5)] - **src,lib**: make process.binding('config') internal (Masashi Hirano) [#23400](https://github.com/nodejs/node/pull/23400)
- [[`b809fa8571`](https://github.com/nodejs/node/commit/b809fa8571)] - **stream**: make async iterator .next() always resolve (Matteo Collina) [#24668](https://github.com/nodejs/node/pull/24668)
- [[`99b018bf48`](https://github.com/nodejs/node/commit/99b018bf48)] - **stream**: use arrow function for callback (DoiChris) [#24609](https://github.com/nodejs/node/pull/24609)
- [[`ba1ebb4a40`](https://github.com/nodejs/node/commit/ba1ebb4a40)] - **stream**: correctly pause and resume after once('readable') (Matteo Collina) [#24366](https://github.com/nodejs/node/pull/24366)
- [[`7bc2011ad9`](https://github.com/nodejs/node/commit/7bc2011ad9)] - **stream**: do not use crypto.DEFAULT_ENCODING in lazy_transform.js (Joyee Cheung) [#24396](https://github.com/nodejs/node/pull/24396)
- [[`01e8a3a8d5`](https://github.com/nodejs/node/commit/01e8a3a8d5)] - **stream**: change comment on duplex stream options (Jesse W. Collins) [#24247](https://github.com/nodejs/node/pull/24247)
- [[`0ed669cf65`](https://github.com/nodejs/node/commit/0ed669cf65)] - **test**: remove unused addons-napi directory (Rich Trott) [#24839](https://github.com/nodejs/node/pull/24839)
- [[`7069ed7546`](https://github.com/nodejs/node/commit/7069ed7546)] - **test**: add .gitignore file for node-api (Rich Trott) [#24839](https://github.com/nodejs/node/pull/24839)
- [[`c227b1be16`](https://github.com/nodejs/node/commit/c227b1be16)] - **test**: partition N-API tests (Gabriel Schulhof) [#24557](https://github.com/nodejs/node/pull/24557)
- [[`63b06b55d7`](https://github.com/nodejs/node/commit/63b06b55d7)] - **test**: fix `common.mustNotCall()` usage in HTTP test (Anna Henningsen) [#24750](https://github.com/nodejs/node/pull/24750)
- [[`cc133c4432`](https://github.com/nodejs/node/commit/cc133c4432)] - **test**: use ES2017 syntax in test-fs-open-\* (jy95) [#23031](https://github.com/nodejs/node/pull/23031)
- [[`a7a1cb48f5`](https://github.com/nodejs/node/commit/a7a1cb48f5)] - **test**: check for the correct strict equal arguments order (Ruben Bridgewater) [#24752](https://github.com/nodejs/node/pull/24752)
- [[`95720089d5`](https://github.com/nodejs/node/commit/95720089d5)] - **test**: add flag scenario in test-fs-write-file-sync (Gireesh Punathil) [#24766](https://github.com/nodejs/node/pull/24766)
- [[`5f58928b06`](https://github.com/nodejs/node/commit/5f58928b06)] - **test**: improve comparison coverage to 100% (Ruben Bridgewater) [#24749](https://github.com/nodejs/node/pull/24749)
- [[`7577e754bb`](https://github.com/nodejs/node/commit/7577e754bb)] - **test**: check invalid argument error for option (timothy searcy) [#24736](https://github.com/nodejs/node/pull/24736)
- [[`2916b592d3`](https://github.com/nodejs/node/commit/2916b592d3)] - **test**: increase assert test coverage (Ruben Bridgewater) [#24745](https://github.com/nodejs/node/pull/24745)
- [[`085f5b6366`](https://github.com/nodejs/node/commit/085f5b6366)] - **test**: show stdout and stderr in test-cli-syntax when it fails (Joyee Cheung) [#24720](https://github.com/nodejs/node/pull/24720)
- [[`026e03cf35`](https://github.com/nodejs/node/commit/026e03cf35)] - **test**: minor refactoring of onticketkeycallback (Daniel Bevenius) [#24718](https://github.com/nodejs/node/pull/24718)
- [[`10c2773da8`](https://github.com/nodejs/node/commit/10c2773da8)] - **test**: mark test_threadsafe_function/test as flaky (Gireesh Punathil) [#24714](https://github.com/nodejs/node/pull/24714)
- [[`8ffe04f533`](https://github.com/nodejs/node/commit/8ffe04f533)] - **test**: verify order of error in h2 server stream (Myles Borins) [#24685](https://github.com/nodejs/node/pull/24685)
- [[`3c3ebe57f6`](https://github.com/nodejs/node/commit/3c3ebe57f6)] - **test**: cover path empty string case (lakatostamas) [#24569](https://github.com/nodejs/node/pull/24569)
- [[`089489965c`](https://github.com/nodejs/node/commit/089489965c)] - **test**: use arrow syntax for anonymous callbacks (Shubham Urkade) [#24691](https://github.com/nodejs/node/pull/24691)
- [[`d5bf7362b9`](https://github.com/nodejs/node/commit/d5bf7362b9)] - **test**: fix the arguments order in assert.strictEqual (pastak) [#24620](https://github.com/nodejs/node/pull/24620)
- [[`1035e36de6`](https://github.com/nodejs/node/commit/1035e36de6)] - **test**: mark test-vm-timeout-escape-nexttick flaky (Gireesh Punathil) [#24712](https://github.com/nodejs/node/pull/24712)
- [[`603bc2751e`](https://github.com/nodejs/node/commit/603bc2751e)] - **test**: fix the arguments order in assert.strictEqual (sigwyg) [#24624](https://github.com/nodejs/node/pull/24624)
- [[`969ae7a598`](https://github.com/nodejs/node/commit/969ae7a598)] - **test**: fix the arguments order in `assert.strictEqual` (rt33) [#24626](https://github.com/nodejs/node/pull/24626)
- [[`e96c60e472`](https://github.com/nodejs/node/commit/e96c60e472)] - **test**: reach res.\_dump after abort ClientRequest (Tadhg Creedon) [#24191](https://github.com/nodejs/node/pull/24191)
- [[`053f3d6289`](https://github.com/nodejs/node/commit/053f3d6289)] - **test**: validate fs.rename() when NODE_TEST_DIR on separate mount (Drew Folta) [#24707](https://github.com/nodejs/node/pull/24707)
- [[`9e1c6eb6aa`](https://github.com/nodejs/node/commit/9e1c6eb6aa)] - **test**: test and docs for detached fork process (timothy searcy) [#24524](https://github.com/nodejs/node/pull/24524)
- [[`992a9040bf`](https://github.com/nodejs/node/commit/992a9040bf)] - **test**: fix arguments order in `assert.strictEqual` (sota1235) [#24607](https://github.com/nodejs/node/pull/24607)
- [[`f8acf73ae7`](https://github.com/nodejs/node/commit/f8acf73ae7)] - **test**: fix arguments order in assert.strictEqual (grimrose) [#24608](https://github.com/nodejs/node/pull/24608)
- [[`84249dfac6`](https://github.com/nodejs/node/commit/84249dfac6)] - **test**: make test-uv-binding-constant JS engine neutral (Rich Trott) [#24666](https://github.com/nodejs/node/pull/24666)
- [[`0a492c730a`](https://github.com/nodejs/node/commit/0a492c730a)] - **test**: use arrow function (sagirk) [#24482](https://github.com/nodejs/node/pull/24482)
- [[`8072a2b85c`](https://github.com/nodejs/node/commit/8072a2b85c)] - **test**: fix arguments order in `assert.strictEqual` (Takahiro Nakamura) [#24621](https://github.com/nodejs/node/pull/24621)
- [[`9d5455515c`](https://github.com/nodejs/node/commit/9d5455515c)] - **test**: use arrow functions in callbacks (apoorvanand) [#24441](https://github.com/nodejs/node/pull/24441)
- [[`99dbdca73b`](https://github.com/nodejs/node/commit/99dbdca73b)] - **test**: update strictEqual argument order (VeysonD) [#24622](https://github.com/nodejs/node/pull/24622)
- [[`3b99191e13`](https://github.com/nodejs/node/commit/3b99191e13)] - **test**: fix argument order in assert.strictEqual (feng jianmei) [#24594](https://github.com/nodejs/node/pull/24594)
- [[`d6fff0e618`](https://github.com/nodejs/node/commit/d6fff0e618)] - **test**: add test for socket.end callback (ajido) [#24087](https://github.com/nodejs/node/pull/24087)
- [[`abb1c64c2d`](https://github.com/nodejs/node/commit/abb1c64c2d)] - **test**: replace anonymous closure functions with arrow functions (tpanthera) [#24443](https://github.com/nodejs/node/pull/24443)
- [[`b7aa312672`](https://github.com/nodejs/node/commit/b7aa312672)] - **test**: fix arguments order in `assert.strictEqual` (tottokotkd) [#24612](https://github.com/nodejs/node/pull/24612)
- [[`a82b420883`](https://github.com/nodejs/node/commit/a82b420883)] - **test**: convert callback to arrow function (jamesgeorge007) [#24513](https://github.com/nodejs/node/pull/24513)
- [[`7edea030af`](https://github.com/nodejs/node/commit/7edea030af)] - **test**: change anonymous function to arrow function (Gagandeep Singh) [#24528](https://github.com/nodejs/node/pull/24528)
- [[`a701dfbb2b`](https://github.com/nodejs/node/commit/a701dfbb2b)] - **test**: split out http2 from test-stream-pipeline (Rich Trott) [#24631](https://github.com/nodejs/node/pull/24631)
- [[`8849d8073a`](https://github.com/nodejs/node/commit/8849d8073a)] - **test**: cover path.basename when path and ext are the same (Laszlo.Moczo) [#24570](https://github.com/nodejs/node/pull/24570)
- [[`12d7107edc`](https://github.com/nodejs/node/commit/12d7107edc)] - **test**: fix assert.strictEqual (mki-skt) [#24619](https://github.com/nodejs/node/pull/24619)
- [[`54778a082a`](https://github.com/nodejs/node/commit/54778a082a)] - **test**: fix arguments order in assert.strictEqual (teppeis) [#24591](https://github.com/nodejs/node/pull/24591)
- [[`cd1aa2b0b5`](https://github.com/nodejs/node/commit/cd1aa2b0b5)] - **test**: fix http2-binding strictEqual order (dominikeinkemmer) [#24616](https://github.com/nodejs/node/pull/24616)
- [[`82ef618e98`](https://github.com/nodejs/node/commit/82ef618e98)] - **test**: fix the arguments order in `assert.strictEqual` (sota1235) [#24595](https://github.com/nodejs/node/pull/24595)
- [[`1067653221`](https://github.com/nodejs/node/commit/1067653221)] - **test**: replace callback with arrow functions (prodroy1) [#24434](https://github.com/nodejs/node/pull/24434)
- [[`363d3c6deb`](https://github.com/nodejs/node/commit/363d3c6deb)] - **test**: use destructuring on require (Juan José Arboleda) [#24455](https://github.com/nodejs/node/pull/24455)
- [[`34b40af5ab`](https://github.com/nodejs/node/commit/34b40af5ab)] - **test**: fix test case in test-child-process-fork-dgram.js (gengjiawen) [#24459](https://github.com/nodejs/node/pull/24459)
- [[`40701520ce`](https://github.com/nodejs/node/commit/40701520ce)] - **test**: replace callback with arrow functions (sreepurnajasti) [#24541](https://github.com/nodejs/node/pull/24541)
- [[`2a67a49053`](https://github.com/nodejs/node/commit/2a67a49053)] - **test**: replace callback with arrow function (potham) [#24531](https://github.com/nodejs/node/pull/24531)
- [[`39adfc8d48`](https://github.com/nodejs/node/commit/39adfc8d48)] - **test**: replace anonymous function with arrow (Gagandeep Singh) [#24527](https://github.com/nodejs/node/pull/24527)
- [[`6b88541fe2`](https://github.com/nodejs/node/commit/6b88541fe2)] - **test**: replace anonymous function with arrow (Gagandeep Singh) [#24526](https://github.com/nodejs/node/pull/24526)
- [[`765a81e32a`](https://github.com/nodejs/node/commit/765a81e32a)] - **test**: add information to assertion (Rich Trott) [#24566](https://github.com/nodejs/node/pull/24566)
- [[`759ed86e5c`](https://github.com/nodejs/node/commit/759ed86e5c)] - **test**: replace anonymous function with arrow func (Gagandeep Singh) [#24525](https://github.com/nodejs/node/pull/24525)
- [[`9bf2659af4`](https://github.com/nodejs/node/commit/9bf2659af4)] - **test**: change anonymous closure function to arrow function (Nethra Ravindran) [#24433](https://github.com/nodejs/node/pull/24433)
- [[`e8c0fcee95`](https://github.com/nodejs/node/commit/e8c0fcee95)] - **test**: replace closure functions with arrow functions (Gagandeep Singh) [#24522](https://github.com/nodejs/node/pull/24522)
- [[`2c8c7b882d`](https://github.com/nodejs/node/commit/2c8c7b882d)] - **test**: replace anonymous function with arrow function (Gagandeep Singh) [#24529](https://github.com/nodejs/node/pull/24529)
- [[`7b0292a839`](https://github.com/nodejs/node/commit/7b0292a839)] - **test**: favor arrow function in callback (Pranay Kothapalli) [#24542](https://github.com/nodejs/node/pull/24542)
- [[`8fcf3b3c59`](https://github.com/nodejs/node/commit/8fcf3b3c59)] - **test**: remove unused reject handlers (Dan Foley) [#24540](https://github.com/nodejs/node/pull/24540)
- [[`46b5df0f1f`](https://github.com/nodejs/node/commit/46b5df0f1f)] - **test**: refactor test to use arrow functions (sagirk) [#24479](https://github.com/nodejs/node/pull/24479)
- [[`c28ec86c90`](https://github.com/nodejs/node/commit/c28ec86c90)] - **test**: replace closure with arrow function (Maya Anilson) [#24489](https://github.com/nodejs/node/pull/24489)
- [[`1cd73a81fa`](https://github.com/nodejs/node/commit/1cd73a81fa)] - **test**: using arrow functions (NoSkillGirl) [#24436](https://github.com/nodejs/node/pull/24436)
- [[`b309dd2be3`](https://github.com/nodejs/node/commit/b309dd2be3)] - **test**: replace anonymous closure with arrow func (suman-mitra) [#24480](https://github.com/nodejs/node/pull/24480)
- [[`c4f16ddccd`](https://github.com/nodejs/node/commit/c4f16ddccd)] - **test**: replace callback with arrow functions (sreepurnajasti) [#24490](https://github.com/nodejs/node/pull/24490)
- [[`dbf14ce17b`](https://github.com/nodejs/node/commit/dbf14ce17b)] - **test**: replcae anonymous closure with arrow function (Sarath Govind K K) [#24476](https://github.com/nodejs/node/pull/24476)
- [[`4792bea514`](https://github.com/nodejs/node/commit/4792bea514)] - **test**: refactor test-http-write-empty-string to use arrow functions (sagirk) [#24483](https://github.com/nodejs/node/pull/24483)
- [[`c45660fd53`](https://github.com/nodejs/node/commit/c45660fd53)] - **test**: replace anonymous closure with arrow functions (suman-mitra) [#24481](https://github.com/nodejs/node/pull/24481)
- [[`f19dae33e6`](https://github.com/nodejs/node/commit/f19dae33e6)] - **test**: replace anonymous closure functions with arrow functions (sagirk) [#24478](https://github.com/nodejs/node/pull/24478)
- [[`fbb228be97`](https://github.com/nodejs/node/commit/fbb228be97)] - **test**: replace anonymous closure functions with arrow function (Abhishek Dixit) [#24420](https://github.com/nodejs/node/pull/24420)
- [[`c15208cb8f`](https://github.com/nodejs/node/commit/c15208cb8f)] - **test**: replace anonymous closure with arrow funct (Prabu Subra) [#24439](https://github.com/nodejs/node/pull/24439)
- [[`8f18f0d5bd`](https://github.com/nodejs/node/commit/8f18f0d5bd)] - **test**: add whatwg-encoding TextDecoder custom inspection with showHidden (ZauberNerd) [#24166](https://github.com/nodejs/node/pull/24166)
- [[`33b524203b`](https://github.com/nodejs/node/commit/33b524203b)] - **test**: use Worker scope in WPT (Joyee Cheung) [#24410](https://github.com/nodejs/node/pull/24410)
- [[`ed714a2e79`](https://github.com/nodejs/node/commit/ed714a2e79)] - **test**: modify order of parameters for assertion (Mrityunjoy Saha) [#24430](https://github.com/nodejs/node/pull/24430)
- [[`3bfa953990`](https://github.com/nodejs/node/commit/3bfa953990)] - **test**: replace closure with arrow functions (kanishk30) [#24440](https://github.com/nodejs/node/pull/24440)
- [[`7d743e659d`](https://github.com/nodejs/node/commit/7d743e659d)] - **test**: replace anonymous closure function with arrow function (Kunda Sunil Kumar) [#24435](https://github.com/nodejs/node/pull/24435)
- [[`e9abf42751`](https://github.com/nodejs/node/commit/e9abf42751)] - **test**: add typeerror test for EC crypto keygen (Matteo) [#24400](https://github.com/nodejs/node/pull/24400)
- [[`237e479196`](https://github.com/nodejs/node/commit/237e479196)] - **test**: change anonymous closure functions to arrow functions (Namit Bhalla) [#24418](https://github.com/nodejs/node/pull/24418)
- [[`2f0a5b6a45`](https://github.com/nodejs/node/commit/2f0a5b6a45)] - **test**: favor arrow functions in callbacks (UjjwalUpadhyay) [#24425](https://github.com/nodejs/node/pull/24425)
- [[`957ecbe019`](https://github.com/nodejs/node/commit/957ecbe019)] - **test**: use print() function on both Python 2 and 3 (cclauss) [#24485](https://github.com/nodejs/node/pull/24485)
- [[`b1dee7dab6`](https://github.com/nodejs/node/commit/b1dee7dab6)] - **test**: replace anonymous closure functions with arrow function (Amanpreet) [#24417](https://github.com/nodejs/node/pull/24417)
- [[`4348ffede5`](https://github.com/nodejs/node/commit/4348ffede5)] - **test**: fix arguments order in napi test_exception (kanishk30) [#24413](https://github.com/nodejs/node/pull/24413)
- [[`0a08cd714e`](https://github.com/nodejs/node/commit/0a08cd714e)] - **test**: fix the arguments order in `assert.strictEqual` (Jay Arthanareeswaran) [#24416](https://github.com/nodejs/node/pull/24416)
- [[`585ebfffa7`](https://github.com/nodejs/node/commit/585ebfffa7)] - **test**: replace closure with arrow functions (Amanpreet) [#24438](https://github.com/nodejs/node/pull/24438)
- [[`d5543ead7c`](https://github.com/nodejs/node/commit/d5543ead7c)] - **test**: change callback function to arrow function (Jay Arthanareeswaran) [#24419](https://github.com/nodejs/node/pull/24419)
- [[`5d663100a0`](https://github.com/nodejs/node/commit/5d663100a0)] - **test**: fix the arguments order in `assert.strictEqual` (apoorvanand) [#24431](https://github.com/nodejs/node/pull/24431)
- [[`9b2ab12b8c`](https://github.com/nodejs/node/commit/9b2ab12b8c)] - **test**: assertion equality fix (NoSkillGirl) [#24422](https://github.com/nodejs/node/pull/24422)
- [[`2777bc42aa`](https://github.com/nodejs/node/commit/2777bc42aa)] - **test**: remove unused function arguments in async-hooks tests (Simon Bruce) [#24406](https://github.com/nodejs/node/pull/24406)
- [[`59723d4b2b`](https://github.com/nodejs/node/commit/59723d4b2b)] - **test**: fix actual parameter order for 'assert.strictEqual' (Selvaraj) [#24428](https://github.com/nodejs/node/pull/24428)
- [[`658df6ba26`](https://github.com/nodejs/node/commit/658df6ba26)] - **test**: swap actual&optional params (Nikhil M) [#24426](https://github.com/nodejs/node/pull/24426)
- [[`de378c0c2d`](https://github.com/nodejs/node/commit/de378c0c2d)] - **test**: skip test that use --tls-v1.x flags (Daniel Bevenius) [#24376](https://github.com/nodejs/node/pull/24376)
- [[`c1777990ae`](https://github.com/nodejs/node/commit/c1777990ae)] - **test**: change callback function to arrow function (Lakshmi Shanmugam) [#24421](https://github.com/nodejs/node/pull/24421)
- [[`ffb5e5da4b`](https://github.com/nodejs/node/commit/ffb5e5da4b)] - **test**: replace anonymous closure for test-http-expect-handling.js (Jayasankar) [#24423](https://github.com/nodejs/node/pull/24423)
- [[`3fadc809bb`](https://github.com/nodejs/node/commit/3fadc809bb)] - **test**: replace callback functions with arrow functions (potham) [#24432](https://github.com/nodejs/node/pull/24432)
- [[`856a0fc8e4`](https://github.com/nodejs/node/commit/856a0fc8e4)] - **test**: use arrow functions for callbacks (Pushkal B) [#24444](https://github.com/nodejs/node/pull/24444)
- [[`f112c06b3e`](https://github.com/nodejs/node/commit/f112c06b3e)] - **test**: replace anonymous closure function (Jayasankar) [#24415](https://github.com/nodejs/node/pull/24415)
- [[`6dd29252c7`](https://github.com/nodejs/node/commit/6dd29252c7)] - **test**: fixed the arguments order in `assert.strictEqual` (Lakshmi Shanmugam) [#24414](https://github.com/nodejs/node/pull/24414)
- [[`7e2a2849db`](https://github.com/nodejs/node/commit/7e2a2849db)] - **test**: use destructuring and remove unused arguments (Julia) [#24375](https://github.com/nodejs/node/pull/24375)
- [[`cdda7f4f18`](https://github.com/nodejs/node/commit/cdda7f4f18)] - **test**: https agent clientcertengine coverage (Osmond van Hemert) [#24248](https://github.com/nodejs/node/pull/24248)
- [[`92f826622b`](https://github.com/nodejs/node/commit/92f826622b)] - **test**: confirm tls server suite default is its own (Sam Roberts) [#24374](https://github.com/nodejs/node/pull/24374)
- [[`261aa7884c`](https://github.com/nodejs/node/commit/261aa7884c)] - **test**: cover tls multi-identity option mixtures (Sam Roberts) [#24374](https://github.com/nodejs/node/pull/24374)
- [[`3c2fb883b4`](https://github.com/nodejs/node/commit/3c2fb883b4)] - **test**: add independent multi-alg crypto identities (Sam Roberts) [#24374](https://github.com/nodejs/node/pull/24374)
- [[`2fc9550280`](https://github.com/nodejs/node/commit/2fc9550280)] - **test**: rename agent1-pfx.pem to agent1.pfx (Sam Roberts) [#24374](https://github.com/nodejs/node/pull/24374)
- [[`ee64ae0f6d`](https://github.com/nodejs/node/commit/ee64ae0f6d)] - **test**: remove unused function arguments in async-hooks tests (Rich Trott) [#24368](https://github.com/nodejs/node/pull/24368)
- [[`d2e9b76c1d`](https://github.com/nodejs/node/commit/d2e9b76c1d)] - **timers**: fix setTimeout expiration logic (Suguru Motegi) [#24214](https://github.com/nodejs/node/pull/24214)
- [[`acb73518b7`](https://github.com/nodejs/node/commit/acb73518b7)] - **(SEMVER-MINOR)** **tls**: add min/max protocol version options (Sam Roberts) [#24405](https://github.com/nodejs/node/pull/24405)
- [[`f30c7c4911`](https://github.com/nodejs/node/commit/f30c7c4911)] - **(SEMVER-MINOR)** **tls**: include RSA bit size in X.509 public key info (Sam Roberts) [#24358](https://github.com/nodejs/node/pull/24358)
- [[`37f0bd7e3a`](https://github.com/nodejs/node/commit/37f0bd7e3a)] - **(SEMVER-MINOR)** **tls**: include elliptic curve X.509 public key info (Sam Roberts) [#24358](https://github.com/nodejs/node/pull/24358)
- [[`71a9c987b2`](https://github.com/nodejs/node/commit/71a9c987b2)] - **tls**: destroy TLS socket if StreamWrap is destroyed (Anna Henningsen) [#24290](https://github.com/nodejs/node/pull/24290)
- [[`0c93b125e4`](https://github.com/nodejs/node/commit/0c93b125e4)] - **tls**: do not rely on 'drain' handlers in StreamWrap (Anna Henningsen) [#24290](https://github.com/nodejs/node/pull/24290)
- [[`249c143703`](https://github.com/nodejs/node/commit/249c143703)] - **tools**: prepare tools/install.py for Python 3 (cclauss) [#24800](https://github.com/nodejs/node/pull/24800)
- [[`1ea01c5790`](https://github.com/nodejs/node/commit/1ea01c5790)] - **tools**: replace rollup with ncc (Rich Trott) [#24813](https://github.com/nodejs/node/pull/24813)
- [[`09cd2ec034`](https://github.com/nodejs/node/commit/09cd2ec034)] - **tools**: fix eslint usage for Node.js 8 and before (Ruben Bridgewater) [#24753](https://github.com/nodejs/node/pull/24753)
- [[`9e5a79a192`](https://github.com/nodejs/node/commit/9e5a79a192)] - **tools**: don't use GH API for commit message checks (Rod Vagg) [#24574](https://github.com/nodejs/node/pull/24574)
- [[`e3649c8e09`](https://github.com/nodejs/node/commit/e3649c8e09)] - **tools**: only sign release if promotion successful (Rod Vagg) [#24669](https://github.com/nodejs/node/pull/24669)
- [[`2ef6aed58a`](https://github.com/nodejs/node/commit/2ef6aed58a)] - **tools**: check for git tag before promoting release (Rod Vagg) [#24670](https://github.com/nodejs/node/pull/24670)
- [[`e7fbdf5784`](https://github.com/nodejs/node/commit/e7fbdf5784)] - **tools**: update remark-preset-lint-node to v1.3.1 (Daijiro Wachi) [#24642](https://github.com/nodejs/node/pull/24642)
- [[`23d815292f`](https://github.com/nodejs/node/commit/23d815292f)] - **tools**: use print() function on both Python 2 and 3 (cclauss) [#24486](https://github.com/nodejs/node/pull/24486)
- [[`13a4d10f67`](https://github.com/nodejs/node/commit/13a4d10f67)] - **tools**: update to remark-lint-preset-node@1.2.0 (Rich Trott) [#24391](https://github.com/nodejs/node/pull/24391)
- [[`5748e862b0`](https://github.com/nodejs/node/commit/5748e862b0)] - **tools**: fix `make lint-md-rollup` and run it (Daijiro Wachi) [#24333](https://github.com/nodejs/node/pull/24333)
- [[`7ffc8b7778`](https://github.com/nodejs/node/commit/7ffc8b7778)] - **tools**: update remark-lint to v6.0.3 from v6.0.2 (Daijiro Wachi) [#24333](https://github.com/nodejs/node/pull/24333)
- [[`b9a4bc15c2`](https://github.com/nodejs/node/commit/b9a4bc15c2)] - **tools**: update remark version to v10 from v8 (Daijiro Wachi) [#24333](https://github.com/nodejs/node/pull/24333)
- [[`1625329fbf`](https://github.com/nodejs/node/commit/1625329fbf)] - **tools,doc**: fix version picker bug in html.js (Rich Trott) [#24638](https://github.com/nodejs/node/pull/24638)
- [[`b6004b3651`](https://github.com/nodejs/node/commit/b6004b3651)] - **trace_events**: forbid tracing modifications from worker threads (Anna Henningsen) [#23781](https://github.com/nodejs/node/pull/23781)
- [[`d881b33028`](https://github.com/nodejs/node/commit/d881b33028)] - **(SEMVER-MINOR)** **url**: support LF, CR and TAB in pathToFileURL (Charles Samborski) [#23720](https://github.com/nodejs/node/pull/23720)
- [[`540929d597`](https://github.com/nodejs/node/commit/540929d597)] - **url**: simplify native URL object construction (Timothy Gu) [#24495](https://github.com/nodejs/node/pull/24495)
- [[`0d7ee19786`](https://github.com/nodejs/node/commit/0d7ee19786)] - **url**: reuse existing context in href setter (Timothy Gu) [#24495](https://github.com/nodejs/node/pull/24495)
- [[`96e6873dd0`](https://github.com/nodejs/node/commit/96e6873dd0)] - **_Revert_** "**url**: make the context non-enumerable" (Timothy Gu) [#24495](https://github.com/nodejs/node/pull/24495)
- [[`be54dc0f72`](https://github.com/nodejs/node/commit/be54dc0f72)] - **url**: use SafeSet to filter known special protocols (Mike Samuel) [#24703](https://github.com/nodejs/node/pull/24703)
- [[`5a853a093c`](https://github.com/nodejs/node/commit/5a853a093c)] - **_Revert_** "**util**: change util.inspect depth default" (Gus Caplan)
- [[`807c108be8`](https://github.com/nodejs/node/commit/807c108be8)] - **util**: improve internal `isError()` validation (Ruben Bridgewater) [#24746](https://github.com/nodejs/node/pull/24746)
- [[`764d76f684`](https://github.com/nodejs/node/commit/764d76f684)] - **_Revert_** "**util**: change %o depth default" (Ruben Bridgewater) [#24806](https://github.com/nodejs/node/pull/24806)
- [[`9e8f91dbc7`](https://github.com/nodejs/node/commit/9e8f91dbc7)] - **util**: remove unreachable branch (rahulshuklab4u) [#24447](https://github.com/nodejs/node/pull/24447)
- [[`e13571c199`](https://github.com/nodejs/node/commit/e13571c199)] - **(SEMVER-MINOR)** **util,console**: handle symbols as defined in the spec (Ruben Bridgewater) [#23708](https://github.com/nodejs/node/pull/23708)
- [[`4d9a2650b2`](https://github.com/nodejs/node/commit/4d9a2650b2)] - **win**: do not use Boxstarter to install tools (João Reis) [#24677](https://github.com/nodejs/node/pull/24677)
- [[`899e7c30b0`](https://github.com/nodejs/node/commit/899e7c30b0)] - **win, build**: skip building cctest by default (Bartosz Sosnowski) [#21408](https://github.com/nodejs/node/pull/21408)

Windows 32-bit Installer: https://nodejs.org/dist/v11.4.0/node-v11.4.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v11.4.0/node-v11.4.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v11.4.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v11.4.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v11.4.0/node-v11.4.0.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v11.4.0/node-v11.4.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v11.4.0/node-v11.4.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v11.4.0/node-v11.4.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v11.4.0/node-v11.4.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v11.4.0/node-v11.4.0-aix-ppc64.tar.gz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v11.4.0/node-v11.4.0-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v11.4.0/node-v11.4.0-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v11.4.0/node-v11.4.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v11.4.0/node-v11.4.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v11.4.0/node-v11.4.0.tar.gz \
Other release files: https://nodejs.org/dist/v11.4.0/ \
Documentation: https://nodejs.org/docs/v11.4.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

fe7842762be7a0385c13a45b476a0b4dc6509487209ec9b7cb84f7f4a5d31953  node-v11.4.0-aix-ppc64.tar.gz
05a515146d5bc397625f442a6ecbbc7f0d071a2a7efbf1e2b2ed46d728bc7b30  node-v11.4.0-darwin-x64.tar.gz
95468d3b67ccd979932132b1f35abe6b9b93f4571bc40e3009b683a7f944e896  node-v11.4.0-darwin-x64.tar.xz
317fcd37fde032e0c0393c0c6a09ba1e6ec49cbd20e9f3fd2930187492f53b64  node-v11.4.0-headers.tar.gz
e865ff16e01f34b0575b393e457c16423698048ddac38aee00678f26222039c1  node-v11.4.0-headers.tar.xz
d92963d7999fb8bcd3c373f8c6c0ab84e19477c0615e5185c527ac0f4ba36aee  node-v11.4.0-linux-arm64.tar.gz
c32e752e74794a4254ae4caa892b7beb3be60dba6851e8e5d637febfb659ee3a  node-v11.4.0-linux-arm64.tar.xz
9298d703d58810a51d8cfccb87874b5adf0e4bd273745521021ef92c80064761  node-v11.4.0-linux-armv6l.tar.gz
86c92be855f3f7abeca082ab897a9c9a79188b3453b13694bb6d71288c168659  node-v11.4.0-linux-armv6l.tar.xz
4d6a73ae890b21f9452907ea5408c134876fdb0af8ee5bb85fce972e844f3630  node-v11.4.0-linux-armv7l.tar.gz
2766d57b0b697cfe557474e245ab25cf978af6736b5f01abab8d196adaff1224  node-v11.4.0-linux-armv7l.tar.xz
335f9fd926d592cbe832b82ecadb778f27fcc081391b0c1c299a4b9a709370ed  node-v11.4.0-linux-ppc64le.tar.gz
9a1e0dcf44978cdb0d078143fb19af5245430de4829d61e6a2d0eff95b6c49be  node-v11.4.0-linux-ppc64le.tar.xz
3407836d310f2897a0fa5c9a83cec7d7c54c8fd4c47c59e84b6cd18c66a00f08  node-v11.4.0-linux-s390x.tar.gz
ee279bc284c97c5fe609028e4a867345baf7d4447b016bc908fd4523fbd87e90  node-v11.4.0-linux-s390x.tar.xz
4ef7dea131453da3a93cc1f32bff948da8953958dcbe3b413debae8bb41aa7a0  node-v11.4.0-linux-x64.tar.gz
24d9be161e7fb28e761801639cb452ff223269ed53d967e5745b5c6391eb3fbd  node-v11.4.0-linux-x64.tar.xz
89c2778ddf9451f6ceb61499038f5f4a3bf454c19d30d70b5459c357dc5b55f6  node-v11.4.0-sunos-x64.tar.gz
d7ad28d573ae52d9c79ed58f5d8ed359833874c2dd60069445908581b35534d1  node-v11.4.0-sunos-x64.tar.xz
8ea241e29af658d8d0d80b5d0730e22ef637193f4222cbb35d5b766b96817e14  node-v11.4.0-win-x64.7z
30b84ab0101c8916694e6cd6c0ccb5182e4555da5e06deb080e906ef5b3893df  node-v11.4.0-win-x64.zip
a8243433652bb5558efd28599cadbdb403a9845c32bf030117dad66bfdf0b1ca  node-v11.4.0-win-x86.7z
e470c0b4234403b1e03e41c6009afcc18b7bcbe88c554a9170311cc0ae83c475  node-v11.4.0-win-x86.zip
ca494b558c8a8885beb4ded633b11b6f34a6b903eda725d156edb15f8f07a5ad  node-v11.4.0-x64.msi
a9502e588c14ee56810c64392f4f696591a8b4e76197abf1ccc1cb3228ceed8e  node-v11.4.0-x86.msi
05fa746e76e00d45db69a4a417b2d3b2761a96df1ff566afb2301d5ad27f324f  node-v11.4.0.pkg
4dd9c7983d38bf373ab4e768645ae6887ccf50e284c7a44c279d6f190e607cab  node-v11.4.0.tar.gz
b7261dd70dcac28f208e8f444dd91dc919e7ec2f5a0aeba9416eb07165a0d684  node-v11.4.0.tar.xz
a40bba1de549cd368f36814034f5d660d28c1ab0a3d0c651145533a754690d9f  win-x64/node.exe
4da72bf352dcecf2bb60a3cf6808e68ac4913db21d623002fbe558b59ee8b202  win-x64/node.lib
5502dfe43e8c77ee504b2b5732d86fc2d5934df13eeb71fc8440bce443d7e104  win-x64/node_pdb.7z
414a4338a29c7b7ae209fbe94ad1f59d7da0f748cbeb653808ac5b41af3b5239  win-x64/node_pdb.zip
92e9e91375bdab14419e09c7631e7b061138185cd551b01d7fb049c4967c2149  win-x86/node.exe
4a9402cb668b2c5ba63f46d81d7f814854286bbba8841709a745d40931ad2617  win-x86/node.lib
bddb2a4a8b317928caca79b6b924007b1e869d424ec65f92dd709a230a23991c  win-x86/node_pdb.7z
08952458ac7d91018b57744475e31d0bbc432b12300f9fa61cd8570fbb33d016  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEpIwr7mgOhBYyzU5E8HSWs+s8F2IFAlwKtbMACgkQ8HSWs+s8
F2JZMg/8CDc8ZeXU1ywxZh9/rcjRSZLV4DOwha0FNdJ6+GM3rqb+ahuJxuWGt7nU
4tm/n0jmBwUUe9aKQiNrUhqSv2UZ4ZF3zerJjppYXibLuF5zMwYlJX3j4B1SE/2/
WPfOftUSheTA1tYCGmdtwWEfU5FwEkG8PnMkQQXp27Xe7uOvC/p6Ym5nmHcBZDNO
6wgn2x0ZjYlivp2vRVPvs6CcCLTPbEgSWcJgYwuGHkchmKUBmXk9Nme5sNpdZ1/k
LhqlZtc4JaBDw7CtvhIrZpE9I+OG2hhTQ9gZjaFb//e3R7pHmEMtu21L85/37SI8
JRq80nlYHU3W0/ov/0U499eMEddBU3tp73UXlQhr91a8CrM14P/vQYUw7PSEhyhy
gKt/tSUKAP6YFtu2HQRaaaOYjlZeSsWFki98z+Xt1HkitJtAMxSbD8ZZhsFd+gow
vndjYp7LmO9FA0COIuFPJWoRoXVmzDbHHaIhEJJlrrvZWM3oZNIkOWjI3BgjbWUk
AoV5YJcsi2oVBJde7gi3b+SQCIafRfy24+rkA0FKFNbZGCHGGHE6j3j/ugs150ab
vScdLzIWMJ0Uosdkna+2GqG7vvWKwHN0e14h2c3ehQJIRYBPp8bJqdBuVcY1tpem
DxvFkfJkvb3caK7MhLZtSGKqbWKLXy1Mn2L+CL4r34sMmoGFgpk=
=XHeN
-----END PGP SIGNATURE-----

```
