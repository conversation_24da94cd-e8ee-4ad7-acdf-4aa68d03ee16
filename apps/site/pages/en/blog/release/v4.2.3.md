---
date: '2015-12-04T03:03:00.000Z'
category: release
title: 'Node v4.2.3 "Argon" (LTS)'
layout: blog-post
author: <PERSON>
---

**This is an important security release**. All Node.js users should consult our [December Security Release Summary](/blog/vulnerability/december-2015-security-releases/) for details on patched vulnerabilities.

### Notable changes(/

- **http**: Fix a bug where an HTTP socket may no longer have an associated parser but a pipelined request triggers a pause or resume, a potential denial-of-service vector. (<PERSON><PERSON>)
- **openssl**: Upgrade to 1.0.2e, containing fixes for:
  - CVE-2015-3193 "BN_mod_exp may produce incorrect results on x86_64", an attack may be feasible against a Node.js TLS server using DHE key exchange. Details are available at <http://openssl.org/news/secadv/20151203.txt>.
  - CVE-2015-3194 "Certificate verify crash with missing PSS parameter", a potential denial-of-service vector for Node.js TLS servers using client authentication; TLS clients are also impacted. Details are available at <http://openssl.org/news/secadv/20151203.txt>.
    (<PERSON><PERSON><PERSON>) [#4134](https://github.com/nodejs/node/pull/4134)
- **v8**: Backport fixes for a bug in `JSON.stringify()` that can result in out-of-bounds reads for arrays. (Ben Noordhuis)

### Known issues

- Some problems with unreferenced timers running during `beforeExit` are still to be resolved. See [#1264](https://github.com/nodejs/node/issues/1264).
- Surrogate pair in REPL can freeze terminal. [#690](https://github.com/nodejs/node/issues/690)
- Calling `dns.setServers()` while a DNS query is in progress can cause the process to crash on a failed assertion. [#894](https://github.com/nodejs/node/issues/894)
- `url.resolve` may transfer the auth portion of the url when resolving between two full hosts, see [#1435](https://github.com/nodejs/node/issues/1435).

### Commits

- [[`49bbd563be`](https://github.com/nodejs/node/commit/49bbd563be)] - **deps**: upgrade openssl sources to 1.0.2e (Shigeki Ohtsu) [#4134](https://github.com/nodejs/node/pull/4134)
- [[`9a063fd492`](https://github.com/nodejs/node/commit/9a063fd492)] - **deps**: backport a7e50a5 from upstream v8 (Ben Noordhuis)
- [[`07233206e9`](https://github.com/nodejs/node/commit/07233206e9)] - **deps**: backport 6df9a1d from upstream v8 (Ben Noordhuis)
- [[`1c8e6de78e`](https://github.com/nodejs/node/commit/1c8e6de78e)] - **http**: fix pipeline regression (Fedor Indutny)

Windows 32-bit Installer: https://nodejs.org/dist/v4.2.3/node-v4.2.3-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v4.2.3/node-v4.2.3-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v4.2.3/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v4.2.3/win-x64/node.exe \
Mac OS X 64-bit Installer: https://nodejs.org/dist/v4.2.3/node-v4.2.3.pkg \
Mac OS X 64-bit Binary: https://nodejs.org/dist/v4.2.3/node-v4.2.3-darwin-x64.tar.gz \
Linux 32-bit Binary: https://nodejs.org/dist/v4.2.3/node-v4.2.3-linux-x86.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v4.2.3/node-v4.2.3-linux-x64.tar.gz \
SmartOS 32-bit Binary: https://nodejs.org/dist/v4.2.3/node-v4.2.3-sunos-x86.tar.gz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v4.2.3/node-v4.2.3-sunos-x64.tar.gz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v4.2.3/node-v4.2.3-linux-armv6l.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v4.2.3/node-v4.2.3-linux-armv7l.tar.gz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v4.2.3/node-v4.2.3-linux-arm64.tar.gz \
Source Code: https://nodejs.org/dist/v4.2.3/node-v4.2.3.tar.gz \
Other release files: https://nodejs.org/dist/v4.2.3/ \
Documentation: https://nodejs.org/docs/v4.2.3/api/

Shasums (GPG signing hash: SHA512, file hash: SHA256):

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA1

078b033d98367da2248b443ed74f0b8a5808783d07cf0c24884457fd66b68fc1  node-v4.2.3-darwin-x64.tar.gz
9890cba5c90e6bd0465140dda2471e3545a7fd19a8f927af8f6c6ce145cb33d1  node-v4.2.3-darwin-x64.tar.xz
e8fab75b9183a4f35c358813e3c5d451daf2fef737a21494f7aa64bdd538c87c  node-v4.2.3-headers.tar.gz
27d79e6b6f1613f9a9cb97f6cfecd0cb710bc6719f24cae2e284b8693eb40d1e  node-v4.2.3-headers.tar.xz
9ec1becd52959920a0f06c92f01b3c3e8c09dd35b4b4f591d975f1975a5f1689  node-v4.2.3-linux-arm64.tar.gz
d6b7233fe980b647bb4349ec577387baf38177cf5952596358b1c3ee7dd11fc6  node-v4.2.3-linux-arm64.tar.xz
50b158bc4c324e78bc5ceb08e2d5e6aefb34f2570798e7ac4fd12bacd6733478  node-v4.2.3-linux-armv6l.tar.gz
095fe43b1a224ec1afae2ee9c19586fddfe4135fea6c1f3cec9907128a436203  node-v4.2.3-linux-armv6l.tar.xz
bebe529dd9ef576193cd7ef40f3f8a16f51317251b624f5e6a9998861b1778f3  node-v4.2.3-linux-armv7l.tar.gz
f5faa99318d7c89de6706b5d7e3602fd613eb7fd002222f7674d28136311cc13  node-v4.2.3-linux-armv7l.tar.xz
644d4c0b206ebcb75383fbe42f6025e7253a61992816289359d0f4dcdb6087d7  node-v4.2.3-linux-x64.tar.gz
feac5b14a28fa32513bcbbbee1712e24996597510d9d1718ce8b0e22e019f8a2  node-v4.2.3-linux-x64.tar.xz
fd30b15327348520dc840bb95a1441d0495e7a843d5a7ec102f012aedc1b3a75  node-v4.2.3-linux-x86.tar.gz
6706cb10ea2252ddd167bcedb77e9884eee3ce2a683a9e21ec417e9084a9187a  node-v4.2.3-linux-x86.tar.xz
0d72b52f99291bef3961ca78b9add920524eae84b4879c0e003546bd28f7a604  node-v4.2.3.pkg
28096b317320bec8d40263975bdc3fdd1b77c941304abcb3fd6c106970b3a936  node-v4.2.3-sunos-x64.tar.gz
d647fe76ffe5bb8d3359d0d9196e972b3f7ca895246c5e7379b3e1c0e1539697  node-v4.2.3-sunos-x64.tar.xz
b37e7652c5d0e08c6c2087e03c0fa860ab55b9c005db33c50373de891eb48dba  node-v4.2.3-sunos-x86.tar.gz
e5ef81350f32320fca5067573b391a1a4ebcc5b9b73e27d60317f6f6b7ff3881  node-v4.2.3-sunos-x86.tar.xz
5008ade5feb4b089f59163f66bffddc113f27de5d78edf203e39435c2c5d554f  node-v4.2.3.tar.gz
9e8aef1e47b317575c421c8d10a80e6c319b26969b566d3b84e49e65a92837f4  node-v4.2.3.tar.xz
3dc276d247684cf45ace30fb99bc44e1af1467108075016a4cd17f980aae086a  node-v4.2.3-x64.msi
a425efc379bca298f3bb1395aafbf851b1d8c6c27fdacf5155bbf4b0b0749332  node-v4.2.3-x86.msi
b987313753634a22e79876fa42b8e85ef33d735164d291416d60bbd7b1ff2603  win-x64/node.exe
109e4dc21a761e5a0707798f6c7575faac5c5f83fda8f6903aea05a89e5e9a14  win-x64/node.lib
ecc0e06bce6e95d849ba2224a3e0666537da08b0e99e132b39633a53d670c05f  win-x86/node.exe
dc35cce7b4928c7635d1ab0b76b6cea35b5029ca4241191dc126a0fc16bd0382  win-x86/node.lib
-----BEGIN PGP SIGNATURE-----
Version: GnuPG v1

iQEcBAEBAgAGBQJWYPvzAAoJEMJzeS99g1RdUDAH/0vQha25eANiUxKhIMOWFAF6
CdsqGDVjfHq6NsUfx3Ehlwu0gVGpDNDU4Pi7M5unXd17UzOlvFBnqGdY5Hcz/4w+
bA2UMJQwr8O28FJ0rShGrYThlM0YMMHnJKtECRzsv54FuCUaUiKkB2omqPY/0aIZ
nHeqAyfvR1y4k/Elx9y2Ni3tMaikQBAeMif7exIYvTrB8H5P4lZoEECRBt1/If87
ui9CRj28Q0fv9HS24iQuXCf4sNhMLq2JEgzH7hzG35GZijj9fby9k/loq8NXs7li
5p4YId7nQhciSaAY3zS1JaUnofBdg35zIIorz4fJZ5dLreeZN7XCjC7eqFpsjMg=
=GNHn
-----END PGP SIGNATURE-----

```
