---
date: '2018-08-16T01:53:16.421Z'
category: release
title: Node v8.11.4 (LTS)
layout: blog-post
author: <PERSON>
---

**This is a security release, fixing a number of vulnerabilities in OpenSSL and Node.js.** Refer to the [August 2018 Security Releases](/blog/vulnerability/august-2018-security-releases/) announcement for full details.

### Notable Changes

- **buffer**: Fix out-of-bounds (OOB) write in `Buffer.write()` for UCS-2 encoding (CVE-2018-12115)
- **deps**: Upgrade to OpenSSL 1.0.2p, fixing:
  - Client DoS due to large DH parameter (CVE-2018-0732)
  - ECDSA key extraction via local side-channel (CVE not assigned)

### Commits

- [[`fc14d812b7`](https://github.com/nodejs/node/commit/fc14d812b7)] - **buffer**: avoid overrun on UCS-2 string write (<PERSON> Vagg) [nodejs-private/node-private#138](https://github.com/nodejs-private/node-private/pull/138)
- [[`8f59838ae7`](https://github.com/nodejs/node/commit/8f59838ae7)] - **deps**: add -no_rand_screen to openssl s_client (Shigeki Ohtsu) [#1836](https://github.com/nodejs/node/pull/1836)
- [[`97607f8622`](https://github.com/nodejs/node/commit/97607f8622)] - **deps**: fix asm build error of openssl in x86_win32 (Shigeki Ohtsu) [#1389](https://github.com/nodejs/node/pull/1389)
- [[`46e4917d98`](https://github.com/nodejs/node/commit/46e4917d98)] - **deps**: fix openssl assembly error on ia32 win32 (Fedor Indutny) [#1389](https://github.com/nodejs/node/pull/1389)
- [[`1b93677a81`](https://github.com/nodejs/node/commit/1b93677a81)] - **deps**: copy all openssl header files to include dir (Shigeki Ohtsu) [#22320](https://github.com/nodejs/node/pull/22320)
- [[`ebf399473b`](https://github.com/nodejs/node/commit/ebf399473b)] - **deps**: upgrade openssl sources to 1.0.2p (Shigeki Ohtsu) [#22320](https://github.com/nodejs/node/pull/22320)
- [[`131c5ed438`](https://github.com/nodejs/node/commit/131c5ed438)] - **openssl**: fix keypress requirement in apps on win32 (Shigeki Ohtsu) [#1389](https://github.com/nodejs/node/pull/1389)
- [[`3139897ff5`](https://github.com/nodejs/node/commit/3139897ff5)] - **test**: fix error messages for OpenSSL-1.0.2p (Shigeki Ohtsu) [#22320](https://github.com/nodejs/node/pull/22320)
- [[`0c047c4d9a`](https://github.com/nodejs/node/commit/0c047c4d9a)] - **test**: update certificates and private keys (Fedor Indutny) [#22184](https://github.com/nodejs/node/pull/22184)
- [[`7c6d0f604b`](https://github.com/nodejs/node/commit/7c6d0f604b)] - **test**: update keys/Makefile to clean and build all (Daniel Bevenius) [#19975](https://github.com/nodejs/node/pull/19975)

Windows 32-bit Installer: https://nodejs.org/dist/v8.11.4/node-v8.11.4-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v8.11.4/node-v8.11.4-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v8.11.4/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v8.11.4/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v8.11.4/node-v8.11.4.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v8.11.4/node-v8.11.4-darwin-x64.tar.gz \
Linux 32-bit Binary: https://nodejs.org/dist/v8.11.4/node-v8.11.4-linux-x86.tar.xz \
Linux 64-bit Binary: https://nodejs.org/dist/v8.11.4/node-v8.11.4-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v8.11.4/node-v8.11.4-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v8.11.4/node-v8.11.4-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v8.11.4/node-v8.11.4-aix-ppc64.tar.gz \
SmartOS 32-bit Binary: https://nodejs.org/dist/v8.11.4/node-v8.11.4-sunos-x86.tar.xz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v8.11.4/node-v8.11.4-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v8.11.4/node-v8.11.4-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v8.11.4/node-v8.11.4-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v8.11.4/node-v8.11.4-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v8.11.4/node-v8.11.4.tar.gz \
Other release files: https://nodejs.org/dist/v8.11.4/ \
Documentation: https://nodejs.org/docs/v8.11.4/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

8d80ab9743983ced0936b1a05e2ec94df32d32080e7030b0e8524a450c88c4b9  node-v8.11.4-aix-ppc64.tar.gz
aa1de83b388581d0d9ec3276f4526ee67e17e0f1bc0deb5133f960ce5dc9f1ef  node-v8.11.4-darwin-x64.tar.gz
c2e26dea26b7f2ce5c9bcebbc437225913470a2b0200325789b2110f8b78ed18  node-v8.11.4-darwin-x64.tar.xz
74f92ed7b0896041d4c5c54b366371ebd082528c57fb81c6cd0cbe897c8f10c7  node-v8.11.4-headers.tar.gz
624ffa954afcf973e346a7096263006d32be85c6642d6a34614d27037d0dfe6f  node-v8.11.4-headers.tar.xz
667b9935e9aab43cd0eab492ec15a0330797cb261ab2df4e18e5a9548817a1c6  node-v8.11.4-linux-arm64.tar.gz
46e90dd916ddbf88c866de300c1b2a26f9216b19abd92b29e89439f62fb6fc1c  node-v8.11.4-linux-arm64.tar.xz
0173429e03d6c9a1b055468361782a923b40269e040be9e1ed5f5879e8b7ccff  node-v8.11.4-linux-armv6l.tar.gz
baa9a7efc0d862f683eeed9954103b0260f201b0c66379af9f418ce95d4d532b  node-v8.11.4-linux-armv6l.tar.xz
7ddd1517096aab3fcf535d870cb9129f65f017862ce2927e6ee96345cc0490ea  node-v8.11.4-linux-armv7l.tar.gz
b78298ac251477290acca6457fb57a24b08ed1d183cd54419b327f88858962d0  node-v8.11.4-linux-armv7l.tar.xz
0a041ea9fcd0beb7b0dbe5cf5e3f54a03c47e9f44d2a96c0613208dc400afc60  node-v8.11.4-linux-ppc64le.tar.gz
4cc845be3e88425c774b6969a0306b95c931919ecd78c43dec1093454104ec3a  node-v8.11.4-linux-ppc64le.tar.xz
bc0f8548f37ee6179a74c71535d86a1923b469525244efed0b83dcbbe06bd251  node-v8.11.4-linux-s390x.tar.gz
9d3304bc008da18b26493baa243033393cc9f4616f549ac3a0b03478747e6259  node-v8.11.4-linux-s390x.tar.xz
c69abe770f002a7415bd00f7ea13b086650c1dd925ef0c3bf8de90eabecc8790  node-v8.11.4-linux-x64.tar.gz
85ea7cbb5bf624e130585bfe3946e99c85ce5cb84c2aee474038bdbe912f908c  node-v8.11.4-linux-x64.tar.xz
e9e6efc1fdcaa7283dc6e2428f8dc88260060ee46fb9e4683a09715c7e3354bf  node-v8.11.4-linux-x86.tar.gz
373d752ac5c10b7403d9a4c8d039457d6c32ada133c0d2ce9d82bc54dba6d551  node-v8.11.4-linux-x86.tar.xz
7a6aa21c443e6aaff8e28b815278478291c862c23f56b63e3d86e4f93a4c9296  node-v8.11.4.pkg
c188c482899aad906ca7a41ff5f76b1e46086301cfd82c11bd1069ca5f1f8987  node-v8.11.4-sunos-x64.tar.gz
a0ca36b316f986d96cab9f7c3d6794deffc8aff5578821319a907e369bec14c4  node-v8.11.4-sunos-x64.tar.xz
26643ef09dfc464d583d1c92c3ab3cd5acc4a4b57051a8fa14694e1a677c602a  node-v8.11.4-sunos-x86.tar.gz
7ed1d34624d4367977f512a5f0ad482851854559448779b4b0cd679ac92477f6  node-v8.11.4-sunos-x86.tar.xz
459144e361d64ca7362c37cc9717c044ef909d348cb5aa3f2b62538560a6085a  node-v8.11.4.tar.gz
fbce7de6d96b0bcb0db0bf77f0e6ea999b6755e6930568aedaab06847552a609  node-v8.11.4.tar.xz
0b5bc0b0bb992abec341dd70aa71b89cfa3c2fd9590a555c83cb3bb3bbcd5ebc  node-v8.11.4-win-x64.7z
72a21e2fcd3703994f57cf707b92e7f939df99c3e0298102e7436849e4948536  node-v8.11.4-win-x64.zip
6766a873857346553d854b0f34d5f00c49e08c75710c1da8c4fee5d1fe3cc9fe  node-v8.11.4-win-x86.7z
83086e6ce4f420e4e6115646d11f301b2df263b0f9f6aaefb6413504e5750aa1  node-v8.11.4-win-x86.zip
452a0fa9cbac277558808ff888e0c924deb5b51945fad67e83fad6c7391c1203  node-v8.11.4-x64.msi
2b6f4e2381976578a751ef3180d127205abc0a13f61fe57a13f11e13410348e9  node-v8.11.4-x86.msi
6cc1bda17eeaf68d0f8645f791de7862d168de6af191b0570b9f79f79e400a08  win-x64/node.exe
fe688901695941ae1e74ea23cc6be5cd48878749f890cbb356a87a53815fdc2b  win-x64/node.lib
e39f51f9b54570c02fd629e52a3931ab7af0fea160cb971378bf38ca7102199e  win-x64/node_pdb.7z
6699add5dfd6a99c879ba058b08dfb9167007c1b928f693eee16e59f9fb5c38a  win-x64/node_pdb.zip
791ecc3f9545dd50c5bdb85bfebf098ff70e229b2430321f425e6355b2e764b5  win-x86/node.exe
cb7972b07e702dd8cbeaa60fd537b6b0e46c6c6b391ea18117e311a755c25a0f  win-x86/node.lib
ad77199833ed6f4f5e98f4256ed3438d40a350b0d34771e4c4076f81031e6e25  win-x86/node_pdb.7z
64b132aa4e8a8cd2614e0419194be7de868a19434e9d98eaed367cf12f75cfec  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQEzBAEBCAAdFiEE3Y8jOLrnUB491ax4wnN5L32DVF0FAlt012gACgkQwnN5L32D
VF2Z5Qf/X3rFnatr+qrZPvyoIUtq06DwkZbyN9cmcl5Uh8kmCMoMG1+jNmh9hJlh
7HCmyX0JKnFZfsz+RtP3/MtASGJcDph6MzuwlOVE7FAG949g67VtRC7Oa+3qvDth
iw24fijFxSKaTgLCldLn8l1gGVc+SQpbI9xqAWHauwtSDQfmqtsugETnJSdhFn/E
OYU//P2j4IJSh0aYpHtuBNcqIFuTUJg8lH9HqSj1b/XaRS1mqlCpi5Wu4NQORc42
6+tc8uXcfd+v7KLGWg1cbsIlknpKisRyBVxC2QRGInm5VivD/uAw/4twOiiXidN8
hNHIlDZ14pbzKPtCCF12ci7FpEiJFA==
=YWdO
-----END PGP SIGNATURE-----

```
