---
date: '2013-03-06T21:51:00.000Z'
category: release
title: Node v0.9.12 (Unstable)
layout: blog-post
author: The Node.js Project
---

As of this release, Node v0.9 is finally performing faster than v0.8
on HTTP, file system, and TLS benchmarks.

Unless there are serious problems found, v0.10.0 will be released on
Monday, 2013.03.11, along with more details about the benchmark
results and the plans for what comes next.

Please consider this a v0.10 Release Candidate. Use it in your
programs. It is as stable as 0.9 is going to get.

---

2013.03.06, Version 0.9.12 (Unstable)

- stream: Allow strings in Readable.push/unshift (isaacs)

- stream: Remove bufferSize option (isaacs)

- stream: Increase highWaterMark on large reads (isaacs)

- stream: \_write: takes an encoding argument (isaacs)

- stream: \_transform: remove output() method, provide encoding (isaacs)

- stream: Don't require read(0) to emit 'readable' event (isaacs)

- node: Add --throw-deprecation (isaacs)

- http: fix multiple timeout events (<PERSON>)

- http: More useful setTimeout API on server (isaacs)

- net: use close callback, not process.nextTick (<PERSON>)

- net: Provide better error when writing after FIN (isaacs)

- dns: Support NAPTR queries (Pavel Lang)

- dns: fix ReferenceError in resolve() error path (Xidorn Quan)

- child_process: handle ENOENT correctly on Windows (Scott Blomquist)

- cluster: Rename destroy() to kill(signal=SIGTERM) (isaacs)

- build: define nightly tag external to build system (Timothy J Fontaine)

- build: make msi build work when spaces are present in the path (Bert Belder)

- build: fix msi build issue with WiX 3.7/3.8 (Raymond Feng)

- repl: make compatible with domains (Dave Olszewski)

- events: Code cleanup and performance improvements (Trevor Norris)

Source Code: https://nodejs.org/dist/v0.9.12/node-v0.9.12.tar.gz

Macintosh Installer (Universal): https://nodejs.org/dist/v0.9.12/node-v0.9.12.pkg

Windows Installer: https://nodejs.org/dist/v0.9.12/node-v0.9.12-x86.msi

Windows x64 Installer: https://nodejs.org/dist/v0.9.12/x64/node-v0.9.12-x64.msi

Windows x64 Files: https://nodejs.org/dist/v0.9.12/x64/

Linux 32-bit Binary: https://nodejs.org/dist/v0.9.12/node-v0.9.12-linux-x86.tar.gz

Linux 64-bit Binary: https://nodejs.org/dist/v0.9.12/node-v0.9.12-linux-x64.tar.gz

Solaris 32-bit Binary: https://nodejs.org/dist/v0.9.12/node-v0.9.12-sunos-x86.tar.gz

Solaris 64-bit Binary: https://nodejs.org/dist/v0.9.12/node-v0.9.12-sunos-x64.tar.gz

Other release files: https://nodejs.org/dist/v0.9.12/

Website: https://nodejs.org/docs/v0.9.12/

Documentation: https://nodejs.org/docs/v0.9.12/api/

Shasums:

```
caa171a35f7eb36fd585f4e16dbf1c60f9d056af  node-v0.9.12-darwin-x64.tar.gz
ffd98e60cdb630c4239c3d94e89b5ba84fb8d234  node-v0.9.12-darwin-x86.tar.gz
85c008721b33872085c3af6253b6e92a6587a467  node-v0.9.12-linux-x64.tar.gz
cc3d50b8ba0474f1907959aae5dfa5e64801bc6b  node-v0.9.12-linux-x86.tar.gz
bcedc7c18f9984ea8873752672a09622e56008e1  node-v0.9.12-sunos-x64.tar.gz
4a7e2baf41fc994044c204a9c90eb894b60ee4c3  node-v0.9.12-sunos-x86.tar.gz
0f7c4f600768736c0e7107e7da33a748a4e2d61e  node-v0.9.12-x86.msi
a9e610225a6cad24923a7242eba97ab1577f3886  node-v0.9.12.pkg
2353d3e5c6518f75202b74236fa9d8eeecd26ca3  node-v0.9.12.tar.gz
9be76f46fbfc66a84fbd02210b6422f14d5dd7eb  node.exe
1f528fdf910036bb791fc6031e2dc9aff300f7eb  node.exp
6aec59165f64fea2e89b6ec4ddbf55ab630b318f  node.lib
fd575f13dc714fdeb64f814e67db9ef5d142e7f8  node.pdb
442dae32457159a579eb44da12c7c5f182c1ae4c  x64/node-v0.9.12-x64.msi
8dc7f6fec9172a5e606ca1b6b3520178313caa29  x64/node.exe
f441fb3807930ee658a105db97ec61a2b5e331d4  x64/node.exp
f8b3880e5fa00151343bf0000f324ea9e96155a7  x64/node.lib
7adb54566301d39866e93506ffc9391870f9c644  x64/node.pdb
```
