---
date: '2017-12-08T16:14:06.684Z'
category: release
title: Node v6.12.2 (LTS)
layout: blog-post
author: <PERSON><PERSON>
---

### Notable Changes

- **deps**:
  - openssl updated to 1.0.2n (<PERSON><PERSON><PERSON>) [#17526](https://github.com/nodejs/node/pull/17526)

### Commits

- [[`6314a46c48`](https://github.com/nodejs/node/commit/6314a46c48)] - **deps**: update openssl asm and asm_obsolete files (Shige<PERSON> Oh<PERSON>) [#17526](https://github.com/nodejs/node/pull/17526)
- [[`f2121a8583`](https://github.com/nodejs/node/commit/f2121a8583)] - **deps**: add -no_rand_screen to openssl s_client (Shige<PERSON> Ohtsu) [nodejs/io.js#1836](https://github.com/nodejs/io.js/pull/1836)
- [[`741651cc4b`](https://github.com/nodejs/node/commit/741651cc4b)] - **deps**: fix asm build error of openssl in x86_win32 (Shigeki <PERSON>) [iojs/io.js#1389](https://github.com/iojs/io.js/pull/1389)
- [[`5956aead33`](https://github.com/nodejs/node/commit/5956aead33)] - **deps**: fix openssl assembly error on ia32 win32 (Fedor Indutny) [iojs/io.js#1389](https://github.com/iojs/io.js/pull/1389)
- [[`ac53d01646`](https://github.com/nodejs/node/commit/ac53d01646)] - **deps**: copy all openssl header files to include dir (Shigeki Ohtsu) [#17526](https://github.com/nodejs/node/pull/17526)
- [[`03651ad9d6`](https://github.com/nodejs/node/commit/03651ad9d6)] - **deps**: upgrade openssl sources to 1.0.2n (Shigeki Ohtsu) [#17526](https://github.com/nodejs/node/pull/17526)
- [[`eb30387c6d`](https://github.com/nodejs/node/commit/eb30387c6d)] - **openssl**: fix keypress requirement in apps on win32 (Shigeki Ohtsu) [iojs/io.js#1389](https://github.com/iojs/io.js/pull/1389)

Windows 32-bit Installer: https://nodejs.org/dist/v6.12.2/node-v6.12.2-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v6.12.2/node-v6.12.2-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v6.12.2/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v6.12.2/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v6.12.2/node-v6.12.2.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v6.12.2/node-v6.12.2-darwin-x64.tar.gz \
Linux 32-bit Binary: https://nodejs.org/dist/v6.12.2/node-v6.12.2-linux-x86.tar.xz \
Linux 64-bit Binary: https://nodejs.org/dist/v6.12.2/node-v6.12.2-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v6.12.2/node-v6.12.2-linux-ppc64le.tar.xz \
Linux PPC BE 64-bit Binary: https://nodejs.org/dist/v6.12.2/node-v6.12.2-linux-ppc64.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v6.12.2/node-v6.12.2-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v6.12.2/node-v6.12.2-aix-ppc64.tar.gz \
SmartOS 32-bit Binary: https://nodejs.org/dist/v6.12.2/node-v6.12.2-sunos-x86.tar.xz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v6.12.2/node-v6.12.2-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v6.12.2/node-v6.12.2-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v6.12.2/node-v6.12.2-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v6.12.2/node-v6.12.2-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v6.12.2/node-v6.12.2.tar.gz \
Other release files: https://nodejs.org/dist/v6.12.2/ \
Documentation: https://nodejs.org/docs/v6.12.2/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

a9ac3110966718640e8da7692b6e8438264a6dec766ff0565ebb0b7b6d8f20b2  node-v6.12.2-aix-ppc64.tar.gz
ef78e01bab2e59643f70dd7384477c1816e3c327a71841cc58826baa7ab8a5e9  node-v6.12.2-darwin-x64.tar.gz
7684b91c7a23a1a61a81d8f3f6d080481cdda8f3b452cd64cc142172f7ea1e4b  node-v6.12.2-darwin-x64.tar.xz
c5e77f5a6d9dffeb8d8f311dc5e26873a65188ba157f72ba0fe972368c6b7685  node-v6.12.2-headers.tar.gz
44cdfe1819a0e9bc84c0ff69433dada84e1a344ae6b29d1c427a48f93da74327  node-v6.12.2-headers.tar.xz
8a1aa367e8bdc95a56837b0e96620bd6f68fba56a1773607f3199bf191bdf1e7  node-v6.12.2-linux-arm64.tar.gz
a9b37f4ad08ffbb86dd2c20e0d86504cfb9131f804167fbd61bd4c6a706e1a09  node-v6.12.2-linux-arm64.tar.xz
d1011acb0d10b88894cb48f00efbe3621990be8c698c938e9ae2a4949bbd20e6  node-v6.12.2-linux-armv6l.tar.gz
6144541afc59b9ada04d324257ad7933b184eab1c42d02f6b907bb47b305a263  node-v6.12.2-linux-armv6l.tar.xz
4d60d4a2bb3987e051a93e9c139cde98a6e9c413eaaa6dd867bfc658fb75ff7d  node-v6.12.2-linux-armv7l.tar.gz
8262d425b43555c563a88a351a7c5cbc776bd1a8aa7f8ba8a2c211c3cf95dbf0  node-v6.12.2-linux-armv7l.tar.xz
891737bd7b623e00b3eab3e9575d4e3d1574fcae42b40e4f0db29a5517560282  node-v6.12.2-linux-ppc64le.tar.gz
198f9e68381cd7aa8bea55e6f391566d630078b1ac8e1b2e75c952840f0d79e1  node-v6.12.2-linux-ppc64le.tar.xz
f6ee44f96ddd5d3d0dee05c6a5496fba9414b3dbc8c302441b4fdd3fa3a28431  node-v6.12.2-linux-ppc64.tar.gz
1ea311f531ed193aeebda4a84d17267e7010940288322a113c3a4bca2518af5f  node-v6.12.2-linux-ppc64.tar.xz
74729d6c9f84316931e0f11ca4c099d81fd7b6150e095c59c65dd536e1b3786e  node-v6.12.2-linux-s390x.tar.gz
de91af1d338e236ffd4435371b4fde8da13376dd9d50dc39a66eb91a0863e650  node-v6.12.2-linux-s390x.tar.xz
05c29ffd17a4d5e0c1c6d4a09244e43e7af7a70ec11e67eecbffdf5ec1e1b45a  node-v6.12.2-linux-x64.tar.gz
dd49e31bddec9d5b3774ccffd8b99a416825d933a3a5f3cbc3823ee136ba3e7a  node-v6.12.2-linux-x64.tar.xz
32feecfde5522fcedd7ad92edb0cb4538fbddfa466cda9cf5310a63eaab83ac0  node-v6.12.2-linux-x86.tar.gz
dc021d1591b2fa4ce3413f5ab7307fc6ef555ae7ab788b0dea24388f84a0c6c1  node-v6.12.2-linux-x86.tar.xz
3636ac9ea2eb5a28201b700f16fac1840bc0f1d5df17c982df01f2c355695d51  node-v6.12.2.pkg
69fe50bc059a72e576e234839de0b7e187fbf07bb10442f4b41dec939e7c3c96  node-v6.12.2-sunos-x64.tar.gz
c42f407aac25d1c517e190a5ca5d1e90bbaa7de2b16dc956f66d27c4e66da4df  node-v6.12.2-sunos-x64.tar.xz
1c71ee28fd0b6dadbb43e09660dfb8c94469cac83853c977846dc4b77a7f382d  node-v6.12.2-sunos-x86.tar.gz
34a97e418c12a87a630e70adb1797c28c336ec242385b45099e1bd701fd9e017  node-v6.12.2-sunos-x86.tar.xz
1bb1d3a033d69ccfa4051ffa79bedad9bcfd43bc0d4b2b6678c3e53883bfd6eb  node-v6.12.2.tar.gz
33677c1fcf6a2f35d2718834fd0afdb36166b0cc68349820e05d8f9316b1dafc  node-v6.12.2.tar.xz
39359e021687fad5c8e19ce9689d449d766cb405edf7e02361e7f7f39652d29f  node-v6.12.2-win-x64.7z
27833478c4b3254f82f39ce7bf2b448df0fa9499800c162d9a180350ccacdf90  node-v6.12.2-win-x64.zip
3bbd322da3d84678e261444909d5ca078410988cb744795d4d01ffcd3902794f  node-v6.12.2-win-x86.7z
9eb86d28ee283926beb042f8095c0f94dc78734fafcb44bf7d2c61f083258c61  node-v6.12.2-win-x86.zip
68728b538b76d9b96b25c38c75c410c04f274f67ab6cc42d794393d3b3ffac6c  node-v6.12.2-x64.msi
d3acea94d31681542863bc7a967294c704568b653f150ae79f1d91156a2fec81  node-v6.12.2-x86.msi
6d1ed6521bbe56ac3cd57ed07f7f867573040a8d63dbe78bdaad10ef380ca745  win-x64/node.exe
c1fc6e4201341b7b0576255ff532915409a779a47e6a7018f27b539f488afb1c  win-x64/node.lib
ae078b04636fd3361592ca5ac789a2724cec5a4d9a1fdceef91b69815da00619  win-x64/node_pdb.7z
461eece5d004963d8d0cd3f98a5681adb6c85bba32ee55a02355a007845659e2  win-x64/node_pdb.zip
d239710fbfe822d0346c978b3fb6388a2bec2e66b5627f268b74358310951aa6  win-x86/node.exe
7c59c0ca192725e7a35dd4bde8d5e2772e0b6e84b4724281b958e3d51a02b6a7  win-x86/node.lib
531e14ce085300a07f492c2dc3c5ccdf93385a0dc16a1d9e8cba9446b6b7110b  win-x86/node_pdb.7z
e08ca9544df52d221d32ca056cf8d103de7206da86445163fff53259befd754f  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQEzBAEBCAAdFiEEDv/hvO/ZyE49CYFSkzsB9AtcqUYFAloqt3UACgkQkzsB9Atc
qUZq6Qf8DNTGsi0qh07gpzZQS6Z4CzJoU2X4CcdKNqaPOxVSPxeMH0Jll0x7anYJ
ZjB+h6a92BAlKhmOuvr9p/FRf2AGvHRRiP7IAKpgL1cdsQRcZIzWVECnNmQvse9H
/6ifBgz34t4TEarqNEdmTIwbtMvGHaJkgOiwoJjRe3I7egmHo3Y7s6Mf3/S20oDG
6WLSM3+3oSIEGItAVdYdBfDlETWjkm7NE1nwMJMjuCz10d26OBC97DJviqwU5eW9
a6wb8sEPD2lmFKxYzCpNH81gLqYbZxg6mrkqibqQcqMr7MaWubdgwYwCFnVjt5ak
si5bpJpfkcBR1V3hnEQdpfq4n+Z7/w==
=jor8
-----END PGP SIGNATURE-----

```
