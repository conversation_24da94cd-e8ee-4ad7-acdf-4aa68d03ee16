---
date: '2017-02-01T02:51:31.151Z'
category: release
title: Node v7.5.0 (Current)
layout: blog-post
author: <PERSON>
---

### Notable changes

- **crypto**:
  - ability to select cert store at runtime (<PERSON>) [#8334](https://github.com/nodejs/node/pull/8334)
  - Use system CAs instead of using bundled ones (<PERSON>) [#8334](https://github.com/nodejs/node/pull/8334)
- **deps**:
  - upgrade npm to 4.1.2 (<PERSON>) [#11020](https://github.com/nodejs/node/pull/11020)
  - upgrade openssl sources to 1.0.2k (<PERSON><PERSON><PERSON>) [#11021](https://github.com/nodejs/node/pull/11021)
- **doc**: add basic documentation for WHATWG URL API (<PERSON>) [#10620](https://github.com/nodejs/node/pull/10620)
- **process**: add NODE_NO_WARNINGS environment variable (cjihrig) [#10842](https://github.com/nodejs/node/pull/10842)
- **url**: allow use of URL with http.request and https.request (<PERSON>) [#10638](https://github.com/nodejs/node/pull/10638)

### Commits

- [[`61f6f1260d`](https://github.com/nodejs/node/commit/61f6f1260d)] - **(SEMVER-MINOR)** src/doc: improve man page and --help (Roman Reiss) [#10157](https://github.com/nodejs/node/pull/10157)
- [[`b2d0c44fb1`](https://github.com/nodejs/node/commit/b2d0c44fb1)] - **assert**: update comments (Kai Cataldo) [#10579](https://github.com/nodejs/node/pull/10579)
- [[`c217b438f2`](https://github.com/nodejs/node/commit/c217b438f2)] - **assert, tools**: enforce strict (not)equal in eslint (Gibson Fahnestock) [#10698](https://github.com/nodejs/node/pull/10698)
- [[`94c4323d56`](https://github.com/nodejs/node/commit/94c4323d56)] - **async_wrap**: close the destroy*ids_idle_handle* (René Schünemann) [#10385](https://github.com/nodejs/node/pull/10385)
- [[`f61c71b533`](https://github.com/nodejs/node/commit/f61c71b533)] - **benchmark**: add progress indicator to compare.js (Joyee Cheung) [#10823](https://github.com/nodejs/node/pull/10823)
- [[`ccdc922ada`](https://github.com/nodejs/node/commit/ccdc922ada)] - **benchmark**: move setImmediate benchmarks to timers (Joshua Colvin) [#11010](https://github.com/nodejs/node/pull/11010)
- [[`062c8513ad`](https://github.com/nodejs/node/commit/062c8513ad)] - **benchmark**: add more thorough timers benchmarks (Jeremiah Senkpiel) [#10925](https://github.com/nodejs/node/pull/10925)
- [[`1e0294ccc9`](https://github.com/nodejs/node/commit/1e0294ccc9)] - **benchmark**: add benchmark for object properties (Michaël Zasso) [#10949](https://github.com/nodejs/node/pull/10949)
- [[`47c0953b12`](https://github.com/nodejs/node/commit/47c0953b12)] - **benchmark**: add benchmark for vm.runIn\*() (Rich Trott) [#10816](https://github.com/nodejs/node/pull/10816)
- [[`2f339e7200`](https://github.com/nodejs/node/commit/2f339e7200)] - **benchmark**: cleanup child_process IPC benchmark (Yuya Tanaka) [#10557](https://github.com/nodejs/node/pull/10557)
- [[`eac1871c45`](https://github.com/nodejs/node/commit/eac1871c45)] - **benchmark**: improve WHATWG URL benchmarks (Joyee Cheung) [#10678](https://github.com/nodejs/node/pull/10678)
- [[`ecf72d8b54`](https://github.com/nodejs/node/commit/ecf72d8b54)] - **benchmark**: use "confidence" in output of compare.R (Joyee Cheung) [#10737](https://github.com/nodejs/node/pull/10737)
- [[`35334273b9`](https://github.com/nodejs/node/commit/35334273b9)] - **benchmark**: don't lint autogenerated modules (Brian White) [#10756](https://github.com/nodejs/node/pull/10756)
- [[`4f96272f12`](https://github.com/nodejs/node/commit/4f96272f12)] - **benchmark**: fix typo "categoty" -> "category" (Victor Felder) [#10568](https://github.com/nodejs/node/pull/10568)
- [[`2f4577c07d`](https://github.com/nodejs/node/commit/2f4577c07d)] - **benchmark**: keep decimals in results (Brian White) [#10559](https://github.com/nodejs/node/pull/10559)
- [[`372e3eeb4b`](https://github.com/nodejs/node/commit/372e3eeb4b)] - **benchmark**: improve readability of net benchmarks (Brian White) [#10446](https://github.com/nodejs/node/pull/10446)
- [[`d19136da84`](https://github.com/nodejs/node/commit/d19136da84)] - **benchmark**: move punycode benchmark out of net (Brian White) [#10446](https://github.com/nodejs/node/pull/10446)
- [[`be24cc0187`](https://github.com/nodejs/node/commit/be24cc0187)] - **benchmark**: add ClientRequest creation benchmark (Brian White) [#10654](https://github.com/nodejs/node/pull/10654)
- [[`1438d00119`](https://github.com/nodejs/node/commit/1438d00119)] - **benchmark,lib,test**: adjust for linting (Rich Trott) [#10561](https://github.com/nodejs/node/pull/10561)
- [[`d13aba8499`](https://github.com/nodejs/node/commit/d13aba8499)] - **buffer**: improve compare() performance (Brian White) [#10927](https://github.com/nodejs/node/pull/10927)
- [[`6549bc2a35`](https://github.com/nodejs/node/commit/6549bc2a35)] - **buffer**: fix comments in bidirectionalIndexOf (<EMAIL>) [#10162](https://github.com/nodejs/node/pull/10162)
- [[`a114f63627`](https://github.com/nodejs/node/commit/a114f63627)] - **buffer**: improve toJSON() performance (Brian White) [#10895](https://github.com/nodejs/node/pull/10895)
- [[`9c2f686f7e`](https://github.com/nodejs/node/commit/9c2f686f7e)] - **build**: don't build deps/zlib if --shared-zlib set (Gibson Fahnestock) [#10657](https://github.com/nodejs/node/pull/10657)
- [[`659428fe1d`](https://github.com/nodejs/node/commit/659428fe1d)] - **build**: sort sources alphabetically (Daniel Bevenius) [#10892](https://github.com/nodejs/node/pull/10892)
- [[`74f9cc9f0a`](https://github.com/nodejs/node/commit/74f9cc9f0a)] - **build**: move source files from headers section (Daniel Bevenius) [#10850](https://github.com/nodejs/node/pull/10850)
- [[`a408ba6454`](https://github.com/nodejs/node/commit/a408ba6454)] - **build**: don't squash signal handlers with --shared (Stewart X Addison) [#10539](https://github.com/nodejs/node/pull/10539)
- [[`ddcd1a202f`](https://github.com/nodejs/node/commit/ddcd1a202f)] - **child_process**: optimize IPC for large data (Yuya Tanaka) [#10557](https://github.com/nodejs/node/pull/10557)
- [[`d751afae0f`](https://github.com/nodejs/node/commit/d751afae0f)] - **cluster**: refactor module into multiple files (cjihrig) [#10746](https://github.com/nodejs/node/pull/10746)
- [[`6687b95263`](https://github.com/nodejs/node/commit/6687b95263)] - **crypto**: return the retval of HMAC_Update (Travis Meisenheimer) [#10891](https://github.com/nodejs/node/pull/10891)
- [[`a1897c1445`](https://github.com/nodejs/node/commit/a1897c1445)] - **(SEMVER-MINOR)** **crypto**: ability to select cert store at runtime (Adam Majer) [#8334](https://github.com/nodejs/node/pull/8334)
- [[`aeea13b6f6`](https://github.com/nodejs/node/commit/aeea13b6f6)] - **(SEMVER-MINOR)** **crypto**: Use system CAs instead of using bundled ones (Adam Majer) [#8334](https://github.com/nodejs/node/pull/8334)
- [[`ac2b059500`](https://github.com/nodejs/node/commit/ac2b059500)] - **(SEMVER-MINOR)** **crypto**: do not use pointers to std::vector (Adam Majer) [#8334](https://github.com/nodejs/node/pull/8334)
- [[`5fd0f9ae63`](https://github.com/nodejs/node/commit/5fd0f9ae63)] - **crypto**: freelist_max_len is gone in OpenSSL 1.1.0 (Adam Langley) [#10859](https://github.com/nodejs/node/pull/10859)
- [[`4e7a31b3a0`](https://github.com/nodejs/node/commit/4e7a31b3a0)] - **crypto,tls**: fix mutability of return values (Rich Trott) [#10795](https://github.com/nodejs/node/pull/10795)
- [[`84a9c158ef`](https://github.com/nodejs/node/commit/84a9c158ef)] - **deps**: fix npm files from upgrade to 4.1.2 (João Reis) [#11085](https://github.com/nodejs/node/pull/11085)
- [[`9e60af893c`](https://github.com/nodejs/node/commit/9e60af893c)] - **deps**: upgrade npm to 4.1.2 (Kat Marchán) [#11020](https://github.com/nodejs/node/pull/11020)
- [[`da59a57d60`](https://github.com/nodejs/node/commit/da59a57d60)] - **deps**: limit regress/regress-crbug-514081 v8 test (Michael Dawson) [#6678](https://github.com/nodejs/node/pull/6678)
- [[`edd20720ac`](https://github.com/nodejs/node/commit/edd20720ac)] - **deps**: update openssl asm and asm_obsolete files (Shigeki Ohtsu) [#11021](https://github.com/nodejs/node/pull/11021)
- [[`ce20ad76ec`](https://github.com/nodejs/node/commit/ce20ad76ec)] - **deps**: add -no_rand_screen to openssl s_client (Shigeki Ohtsu) [nodejs/io.js#1836](https://github.com/nodejs/io.js/pull/1836)
- [[`06f87c3e0a`](https://github.com/nodejs/node/commit/06f87c3e0a)] - **deps**: fix asm build error of openssl in x86_win32 (Shigeki Ohtsu) [iojs/io.js#1389](https://github.com/iojs/io.js/pull/1389)
- [[`093cacf61b`](https://github.com/nodejs/node/commit/093cacf61b)] - **deps**: fix openssl assembly error on ia32 win32 (Fedor Indutny) [iojs/io.js#1389](https://github.com/iojs/io.js/pull/1389)
- [[`a3b3b35c53`](https://github.com/nodejs/node/commit/a3b3b35c53)] - **deps**: copy all openssl header files to include dir (Shigeki Ohtsu) [#11021](https://github.com/nodejs/node/pull/11021)
- [[`6a0f1fabb1`](https://github.com/nodejs/node/commit/6a0f1fabb1)] - **deps**: upgrade openssl sources to 1.0.2k (Shigeki Ohtsu) [#11021](https://github.com/nodejs/node/pull/11021)
- [[`1fae98b833`](https://github.com/nodejs/node/commit/1fae98b833)] - **deps**: upgrade npm to 4.1.1 (Rebecca Turner) [#10781](https://github.com/nodejs/node/pull/10781)
- [[`6e0888ad9e`](https://github.com/nodejs/node/commit/6e0888ad9e)] - **deps**: add test for v8 bug in toUpper('ç') (Steven R. Loomis) [#9828](https://github.com/nodejs/node/pull/9828)
- [[`1c4bf9e8ff`](https://github.com/nodejs/node/commit/1c4bf9e8ff)] - **deps**: cherry-pick 2f5da9a from V8 upstream (Steven R. Loomis) [#9828](https://github.com/nodejs/node/pull/9828)
- [[`ffd938a694`](https://github.com/nodejs/node/commit/ffd938a694)] - **deps**: upgrade libuv to 1.10.2 (cjihrig) [#10717](https://github.com/nodejs/node/pull/10717)
- [[`aa6b9f979e`](https://github.com/nodejs/node/commit/aa6b9f979e)] - **deps**: cherry-pick baba152 from V8 upstream (Michaël Zasso) [#10688](https://github.com/nodejs/node/pull/10688)
- [[`5887396150`](https://github.com/nodejs/node/commit/5887396150)] - **deps**: cherry-pick a814b8a from upstream V8 (<EMAIL>) [#10733](https://github.com/nodejs/node/pull/10733)
- [[`cfc4c62249`](https://github.com/nodejs/node/commit/cfc4c62249)] - **doc**: correct and complete dgram's Socket.bind docs (Alex Jordan) [#11025](https://github.com/nodejs/node/pull/11025)
- [[`55e98c66c0`](https://github.com/nodejs/node/commit/55e98c66c0)] - **doc**: add abouthiroppy to collaborators (Yuta Hiroto) [#11080](https://github.com/nodejs/node/pull/11080)
- [[`722ae8700f`](https://github.com/nodejs/node/commit/722ae8700f)] - **doc**: add who to CC list for dgram (cjihrig) [#11035](https://github.com/nodejs/node/pull/11035)
- [[`a8533acee6`](https://github.com/nodejs/node/commit/a8533acee6)] - **doc**: change logical to bitwise OR in dns lookup (Sakthipriyan Vairamani (thefourtheye)) [#11037](https://github.com/nodejs/node/pull/11037)
- [[`ac36d78d56`](https://github.com/nodejs/node/commit/ac36d78d56)] - **doc**: fix typo in http.md (Peter Mescalchin) [#10975](https://github.com/nodejs/node/pull/10975)
- [[`0bf3b24771`](https://github.com/nodejs/node/commit/0bf3b24771)] - **doc**: remove Chris Dickinson from active releasers (Ben Noordhuis) [#11011](https://github.com/nodejs/node/pull/11011)
- [[`9ca404ed25`](https://github.com/nodejs/node/commit/9ca404ed25)] - **doc**: for style, remove "isn't" contraction (Sam Roberts) [#10981](https://github.com/nodejs/node/pull/10981)
- [[`4be9e98448`](https://github.com/nodejs/node/commit/4be9e98448)] - **doc**: make os api doc more consistent (Evan Lucas) [#10994](https://github.com/nodejs/node/pull/10994)
- [[`7dff6aa67f`](https://github.com/nodejs/node/commit/7dff6aa67f)] - **doc**: update http.md for consistency and clarity (Lance Ball) [#10715](https://github.com/nodejs/node/pull/10715)
- [[`dd608591a8`](https://github.com/nodejs/node/commit/dd608591a8)] - **doc**: clarify Buffer.indexOf/lastIndexOf edge cases (<EMAIL>) [#10162](https://github.com/nodejs/node/pull/10162)
- [[`5250b3358e`](https://github.com/nodejs/node/commit/5250b3358e)] - **doc**: document argument variant in the repl.md (Vse Mozhet Byt) [#10221](https://github.com/nodejs/node/pull/10221)
- [[`c4b9f0a75e`](https://github.com/nodejs/node/commit/c4b9f0a75e)] - **doc**: DEFAULT_ECDH_CURVE was added in 0.11.13 (Sam Roberts) [#10983](https://github.com/nodejs/node/pull/10983)
- [[`84e2ff3738`](https://github.com/nodejs/node/commit/84e2ff3738)] - **(SEMVER-MINOR)** **doc**: add basic documentation for WHATWG URL API (James M Snell) [#10620](https://github.com/nodejs/node/pull/10620)
- [[`9d91bf9788`](https://github.com/nodejs/node/commit/9d91bf9788)] - **doc**: HTTP response getHeader doc fix (Faiz Halde) [#10817](https://github.com/nodejs/node/pull/10817)
- [[`06acf88117`](https://github.com/nodejs/node/commit/06acf88117)] - **doc**: remove duplicate properties bullet in readme (Javis Sullivan) [#10741](https://github.com/nodejs/node/pull/10741)
- [[`09ac2a2cb7`](https://github.com/nodejs/node/commit/09ac2a2cb7)] - **doc**: specify sorted requires in tests (Sam Roberts) [#10716](https://github.com/nodejs/node/pull/10716)
- [[`f380a5fb5a`](https://github.com/nodejs/node/commit/f380a5fb5a)] - **doc**: mention cc-ing nodejs/python team for reviews (Anna Henningsen) [#10637](https://github.com/nodejs/node/pull/10637)
- [[`58bb263438`](https://github.com/nodejs/node/commit/58bb263438)] - **doc**: update TheAlphaNerd to MylesBorins (Myles Borins) [#10586](https://github.com/nodejs/node/pull/10586)
- [[`1253650cf4`](https://github.com/nodejs/node/commit/1253650cf4)] - **doc**: update examples in api/crypto.md (Vse Mozhet Byt) [#10909](https://github.com/nodejs/node/pull/10909)
- [[`3177d6557a`](https://github.com/nodejs/node/commit/3177d6557a)] - **doc**: move topics/guides to website (Evan Lucas) [#10896](https://github.com/nodejs/node/pull/10896)
- [[`d2896d92a7`](https://github.com/nodejs/node/commit/d2896d92a7)] - **doc**: update AUTHORS list to fix name (Noah Rose Ledesma) [#10945](https://github.com/nodejs/node/pull/10945)
- [[`4ffcefdc09`](https://github.com/nodejs/node/commit/4ffcefdc09)] - **doc**: add TimothyGu to collaborators (Timothy Gu) [#10954](https://github.com/nodejs/node/pull/10954)
- [[`3fcf0aed4a`](https://github.com/nodejs/node/commit/3fcf0aed4a)] - **doc**: mention moderation repo in onboarding doc (Anna Henningsen) [#10869](https://github.com/nodejs/node/pull/10869)
- [[`79d8db0fef`](https://github.com/nodejs/node/commit/79d8db0fef)] - **doc**: add edsadr to collaborators (Adrian Estrada) [#10883](https://github.com/nodejs/node/pull/10883)
- [[`520b1f7853`](https://github.com/nodejs/node/commit/520b1f7853)] - **doc**: clarifying variables in fs.write() (Jessica Quynh Tran) [#9792](https://github.com/nodejs/node/pull/9792)
- [[`daf1bf588b`](https://github.com/nodejs/node/commit/daf1bf588b)] - **doc**: add links for zlib convenience methods (Anna Henningsen) [#10829](https://github.com/nodejs/node/pull/10829)
- [[`aeaf887700`](https://github.com/nodejs/node/commit/aeaf887700)] - **doc**: fix markdown escaping in CHANGELOG_V7.md (Anna Henningsen) [#10827](https://github.com/nodejs/node/pull/10827)
- [[`c8b0fc6d8b`](https://github.com/nodejs/node/commit/c8b0fc6d8b)] - **doc**: remove duplicate PR link from changelog (Anna Henningsen) [#10827](https://github.com/nodejs/node/pull/10827)
- [[`049258b062`](https://github.com/nodejs/node/commit/049258b062)] - **doc**: fixup `added` tags in cli.md (Anna Henningsen) [#10826](https://github.com/nodejs/node/pull/10826)
- [[`61798d1fa4`](https://github.com/nodejs/node/commit/61798d1fa4)] - **doc**: add missing `added:` tag for `zlib.constants` (Anna Henningsen) [#10826](https://github.com/nodejs/node/pull/10826)
- [[`73939ec701`](https://github.com/nodejs/node/commit/73939ec701)] - **doc**: clarify memory sharing behavior of buffer ctor (Zach Bjornson) [#10778](https://github.com/nodejs/node/pull/10778)
- [[`f8b081b519`](https://github.com/nodejs/node/commit/f8b081b519)] - **doc**: fix broken internal link in process.md (Anna Henningsen) [#10828](https://github.com/nodejs/node/pull/10828)
- [[`a53f881f57`](https://github.com/nodejs/node/commit/a53f881f57)] - **doc**: update writable.write return value (Nathan Phillip Brink) [#10582](https://github.com/nodejs/node/pull/10582)
- [[`6e1a3d1e57`](https://github.com/nodejs/node/commit/6e1a3d1e57)] - **doc**: use correct tls certificate property name (Sam Roberts) [#10389](https://github.com/nodejs/node/pull/10389)
- [[`23edfe00b3`](https://github.com/nodejs/node/commit/23edfe00b3)] - **doc**: edit writing-tests.md (Rich Trott) [#10585](https://github.com/nodejs/node/pull/10585)
- [[`9b73a8524f`](https://github.com/nodejs/node/commit/9b73a8524f)] - **doc**: fix misleading language in vm docs (Alexey Orlenko) [#10708](https://github.com/nodejs/node/pull/10708)
- [[`56ea7eb9a7`](https://github.com/nodejs/node/commit/56ea7eb9a7)] - **doc**: mention cc-ing nodejs/url team for reviews (Anna Henningsen) [#10652](https://github.com/nodejs/node/pull/10652)
- [[`66b34eac2f`](https://github.com/nodejs/node/commit/66b34eac2f)] - **doc**: sort require statements in tests (Sam Roberts) [#10616](https://github.com/nodejs/node/pull/10616)
- [[`238466bcf0`](https://github.com/nodejs/node/commit/238466bcf0)] - **doc**: handle backpressure when write() return false (Matteo Collina) [#10631](https://github.com/nodejs/node/pull/10631)
- [[`ec226a2a3b`](https://github.com/nodejs/node/commit/ec226a2a3b)] - **doc**: add test naming information to guide (Rich Trott) [#10584](https://github.com/nodejs/node/pull/10584)
- [[`b73e98bf48`](https://github.com/nodejs/node/commit/b73e98bf48)] - **doc**: fix missing negation in stream.md (Johannes Rieken) [#10712](https://github.com/nodejs/node/pull/10712)
- [[`bf95b074cd`](https://github.com/nodejs/node/commit/bf95b074cd)] - **doc**: "s/git apply/git am -3" in V8 guide (Myles Borins) [#10665](https://github.com/nodejs/node/pull/10665)
- [[`9c89b2f704`](https://github.com/nodejs/node/commit/9c89b2f704)] - **doc**: update LTS info for current releases (Evan Lucas) [#10720](https://github.com/nodejs/node/pull/10720)
- [[`3f1775707e`](https://github.com/nodejs/node/commit/3f1775707e)] - **doc**: correct vcbuild options for windows testing (Jonathan Boarman) [#10686](https://github.com/nodejs/node/pull/10686)
- [[`8314d9ee73`](https://github.com/nodejs/node/commit/8314d9ee73)] - **doc**: killSignal option accepts integer values (Sakthipriyan Vairamani (thefourtheye)) [#10424](https://github.com/nodejs/node/pull/10424)
- [[`736a7f3dd3`](https://github.com/nodejs/node/commit/736a7f3dd3)] - **doc**: update BUILDING.md (Lukasz Gasior) [#10669](https://github.com/nodejs/node/pull/10669)
- [[`f81bd48818`](https://github.com/nodejs/node/commit/f81bd48818)] - **doc**: document use of Refs: for references (Gibson Fahnestock) [#10670](https://github.com/nodejs/node/pull/10670)
- [[`b70dde0050`](https://github.com/nodejs/node/commit/b70dde0050)] - **doc**: new TLSSocket has no secure context options (Sam Roberts) [#10545](https://github.com/nodejs/node/pull/10545)
- [[`d3628d9e47`](https://github.com/nodejs/node/commit/d3628d9e47)] - **doc**: modernize child_process example code (Vse Mozhet Byt) [#10102](https://github.com/nodejs/node/pull/10102)
- [[`3270d4c89b`](https://github.com/nodejs/node/commit/3270d4c89b)] - **doc**: clarify information about ABI version (Rich Trott) [#10419](https://github.com/nodejs/node/pull/10419)
- [[`1fca69c263`](https://github.com/nodejs/node/commit/1fca69c263)] - **doc,test**: tls .ca option supports multi-PEM files (Sam Roberts) [#10389](https://github.com/nodejs/node/pull/10389)
- [[`78a495e1a4`](https://github.com/nodejs/node/commit/78a495e1a4)] - **eslint**: remove dangling eslint symlink (Sam Roberts) [#10771](https://github.com/nodejs/node/pull/10771)
- [[`5cca69320f`](https://github.com/nodejs/node/commit/5cca69320f)] - **events**: avoid emit() eager deopt (Victor Felder) [#10568](https://github.com/nodejs/node/pull/10568)
- [[`ded17579e5`](https://github.com/nodejs/node/commit/ded17579e5)] - **events**: improve removeListener() performance (Brian White) [#10572](https://github.com/nodejs/node/pull/10572)
- [[`d047f8e8f8`](https://github.com/nodejs/node/commit/d047f8e8f8)] - **fs**: remove unused parameter for encodeRealpathResult (Jackson Tian) [#10862](https://github.com/nodejs/node/pull/10862)
- [[`4c0f29723c`](https://github.com/nodejs/node/commit/4c0f29723c)] - **http**: use direct parameters instead (Jackson Tian) [#10833](https://github.com/nodejs/node/pull/10833)
- [[`c32984361a`](https://github.com/nodejs/node/commit/c32984361a)] - **http**: make request.abort() destroy the socket (Luigi Pinca) [#10818](https://github.com/nodejs/node/pull/10818)
- [[`8ba2cf9c51`](https://github.com/nodejs/node/commit/8ba2cf9c51)] - **http**: define all used properties in constructors (vitkarpov) [#9116](https://github.com/nodejs/node/pull/9116)
- [[`75aa6050ab`](https://github.com/nodejs/node/commit/75aa6050ab)] - **http**: eliminate capture of ClientRequest in Agent (Evan Torrie) [#10134](https://github.com/nodejs/node/pull/10134)
- [[`5059b76cbc`](https://github.com/nodejs/node/commit/5059b76cbc)] - **http**: misc ClientRequest cleanup (Brian White) [#10654](https://github.com/nodejs/node/pull/10654)
- [[`44c0e4f1ad`](https://github.com/nodejs/node/commit/44c0e4f1ad)] - **http**: avoid duplicate isArray() (Brian White) [#10654](https://github.com/nodejs/node/pull/10654)
- [[`e7859c217f`](https://github.com/nodejs/node/commit/e7859c217f)] - **http**: optimize default method case (Brian White) [#10654](https://github.com/nodejs/node/pull/10654)
- [[`c9bff043c7`](https://github.com/nodejs/node/commit/c9bff043c7)] - **http**: optimize short path validation (Brian White) [#10654](https://github.com/nodejs/node/pull/10654)
- [[`c012dd79dc`](https://github.com/nodejs/node/commit/c012dd79dc)] - **https**: Use secureProtocol in Agent#getName (Andreas Lind) [#9452](https://github.com/nodejs/node/pull/9452)
- [[`9a111e701e`](https://github.com/nodejs/node/commit/9a111e701e)] - **inspector**: no crash when WS server can't start (Eugene Ostroukhov) [#10878](https://github.com/nodejs/node/pull/10878)
- [[`2d08bbadd6`](https://github.com/nodejs/node/commit/2d08bbadd6)] - **inspector**: stop relying on magic strings (Eugene Ostroukhov) [#10159](https://github.com/nodejs/node/pull/10159)
- [[`e30e307a70`](https://github.com/nodejs/node/commit/e30e307a70)] - **inspector**: move options parsing (Eugene Ostroukhov) [#9691](https://github.com/nodejs/node/pull/9691)
- [[`60f27f91e4`](https://github.com/nodejs/node/commit/60f27f91e4)] - **inspector**: remove unused uv_async_t (Eugene Ostroukhov) [#10392](https://github.com/nodejs/node/pull/10392)
- [[`a3abba0b1a`](https://github.com/nodejs/node/commit/a3abba0b1a)] - **lib**: remove unnecessary parameter for assertCrypto() (Jackson Tian) [#10834](https://github.com/nodejs/node/pull/10834)
- [[`4de7b03a7d`](https://github.com/nodejs/node/commit/4de7b03a7d)] - **lib**: refactor bootstrap_node.js regular expression (Rich Trott) [#10749](https://github.com/nodejs/node/pull/10749)
- [[`a6c93af244`](https://github.com/nodejs/node/commit/a6c93af244)] - **lib**: refactor crypto cipher/hash/curve getters (Rich Trott) [#10682](https://github.com/nodejs/node/pull/10682)
- [[`6e8d627217`](https://github.com/nodejs/node/commit/6e8d627217)] - **lib,src**: support values > 4GB in heap statistics (Ben Noordhuis) [#10186](https://github.com/nodejs/node/pull/10186)
- [[`de8eee6b16`](https://github.com/nodejs/node/commit/de8eee6b16)] - **meta**: decharter the http working group (James M Snell) [#10604](https://github.com/nodejs/node/pull/10604)
- [[`4caa0126aa`](https://github.com/nodejs/node/commit/4caa0126aa)] - **openssl**: fix keypress requirement in apps on win32 (Shigeki Ohtsu) [iojs/io.js#1389](https://github.com/iojs/io.js/pull/1389)
- [[`d24491c6a7`](https://github.com/nodejs/node/commit/d24491c6a7)] - **(SEMVER-MINOR)** **process**: add NODE_NO_WARNINGS environment variable (cjihrig) [#10842](https://github.com/nodejs/node/pull/10842)
- [[`97ff43232b`](https://github.com/nodejs/node/commit/97ff43232b)] - **querystring**: improve unescapeBuffer performance (Brian White) [#10837](https://github.com/nodejs/node/pull/10837)
- [[`f4796d5f6e`](https://github.com/nodejs/node/commit/f4796d5f6e)] - **querystring**: improve stringify() performance (Brian White) [#10852](https://github.com/nodejs/node/pull/10852)
- [[`53421b174c`](https://github.com/nodejs/node/commit/53421b174c)] - **querystring**: improve parse() performance (Brian White) [#10874](https://github.com/nodejs/node/pull/10874)
- [[`d64e2371f6`](https://github.com/nodejs/node/commit/d64e2371f6)] - **readline**: refactor construct Interface (Jackson Tian) [#4740](https://github.com/nodejs/node/pull/4740)
- [[`e7b656db6e`](https://github.com/nodejs/node/commit/e7b656db6e)] - **_Revert_** "**repl**: disable Ctrl+C support on win32 for now" (Anna Henningsen) [#8645](https://github.com/nodejs/node/pull/8645)
- [[`a24264eb18`](https://github.com/nodejs/node/commit/a24264eb18)] - **src**: fix v8 local handling in node_url.cc (Anna Henningsen) [#11064](https://github.com/nodejs/node/pull/11064)
- [[`8a6367cb20`](https://github.com/nodejs/node/commit/8a6367cb20)] - **_Revert_** "**src**: don't overwrite non-writable vm globals" (Anna Henningsen) [#10920](https://github.com/nodejs/node/pull/10920)
- [[`978acd138f`](https://github.com/nodejs/node/commit/978acd138f)] - **(SEMVER-MINOR)** **src**: support "--" after "-e" as end-of-options (John Barboza) [#10651](https://github.com/nodejs/node/pull/10651)
- [[`cd94642356`](https://github.com/nodejs/node/commit/cd94642356)] - **src**: add NODE_NO_WARNINGS to --help output (cjihrig) [#10918](https://github.com/nodejs/node/pull/10918)
- [[`63f43021b0`](https://github.com/nodejs/node/commit/63f43021b0)] - **src**: remove unused PROTOCOL_JSON array (Ben Noordhuis) [#10407](https://github.com/nodejs/node/pull/10407)
- [[`5a976decf7`](https://github.com/nodejs/node/commit/5a976decf7)] - **src**: remove unnecessary req_wrap_obj (Daniel Bevenius) [#10942](https://github.com/nodejs/node/pull/10942)
- [[`0c0334f7a4`](https://github.com/nodejs/node/commit/0c0334f7a4)] - **src**: add a missing space in node_os.cc (Alexey Orlenko) [#10931](https://github.com/nodejs/node/pull/10931)
- [[`b89d848b36`](https://github.com/nodejs/node/commit/b89d848b36)] - **src**: enable writev for pipe handles on Unix (Alexey Orlenko) [#10677](https://github.com/nodejs/node/pull/10677)
- [[`f0de955220`](https://github.com/nodejs/node/commit/f0de955220)] - **src**: reduce test_inspector_socket_server output (Daniel Bevenius) [#10537](https://github.com/nodejs/node/pull/10537)
- [[`59196af646`](https://github.com/nodejs/node/commit/59196af646)] - **stream**: avoid additional validation for Buffers (Brian White) [#10580](https://github.com/nodejs/node/pull/10580)
- [[`fe80bd9600`](https://github.com/nodejs/node/commit/fe80bd9600)] - **test**: add 2nd argument to throws in test-assert (Marlena Compton) [#11061](https://github.com/nodejs/node/pull/11061)
- [[`8ef4add4c3`](https://github.com/nodejs/node/commit/8ef4add4c3)] - **test**: require handler to be run in sigwinch test (Rich Trott) [#11068](https://github.com/nodejs/node/pull/11068)
- [[`e367b74c4f`](https://github.com/nodejs/node/commit/e367b74c4f)] - **test**: add an exception test to http-write-head (Yuta Hiroto) [#11034](https://github.com/nodejs/node/pull/11034)
- [[`65691d68d5`](https://github.com/nodejs/node/commit/65691d68d5)] - **test**: increase coverage of internal/util (DavidCai) [#10964](https://github.com/nodejs/node/pull/10964)
- [[`0753bc17b6`](https://github.com/nodejs/node/commit/0753bc17b6)] - **test**: increase timeout in break-on-uncaught (Sakthipriyan Vairamani (thefourtheye)) [#10822](https://github.com/nodejs/node/pull/10822)
- [[`eff3a48e63`](https://github.com/nodejs/node/commit/eff3a48e63)] - **test**: add known_issues test for #10223 (AnnaMag) [#11024](https://github.com/nodejs/node/pull/11024)
- [[`72a97b66dc`](https://github.com/nodejs/node/commit/72a97b66dc)] - **test**: guarantee test runs in test-readline-keys (Rich Trott) [#11023](https://github.com/nodejs/node/pull/11023)
- [[`e3a316f3e6`](https://github.com/nodejs/node/commit/e3a316f3e6)] - **test**: check error message in test-http-outgoing-proto (Alex Ling) [#10943](https://github.com/nodejs/node/pull/10943)
- [[`fcd08b8a1e`](https://github.com/nodejs/node/commit/fcd08b8a1e)] - **test**: add tests for searchParams (abouthiroppy) [#10952](https://github.com/nodejs/node/pull/10952)
- [[`f3efaeed35`](https://github.com/nodejs/node/commit/f3efaeed35)] - **test**: increase coverage for stream's duplex (abouthiroppy) [#10963](https://github.com/nodejs/node/pull/10963)
- [[`b5e8413c3f`](https://github.com/nodejs/node/commit/b5e8413c3f)] - **test**: allow for slow hosts in spawnSync() test (Rich Trott) [#10998](https://github.com/nodejs/node/pull/10998)
- [[`cfd1b19c34`](https://github.com/nodejs/node/commit/cfd1b19c34)] - **test**: expand test coverage of fs.js (Vinícius do Carmo) [#10947](https://github.com/nodejs/node/pull/10947)
- [[`4aedde8d82`](https://github.com/nodejs/node/commit/4aedde8d82)] - **test**: expand test coverage of events.js (Vinícius do Carmo) [#10947](https://github.com/nodejs/node/pull/10947)
- [[`c1e166a168`](https://github.com/nodejs/node/commit/c1e166a168)] - **test**: check noAssert option in buf.write\*() (larissayvette) [#10790](https://github.com/nodejs/node/pull/10790)
- [[`580a453fcf`](https://github.com/nodejs/node/commit/580a453fcf)] - **test**: expand test coverage of fs.js (Vinícius do Carmo) [#10972](https://github.com/nodejs/node/pull/10972)
- [[`fa8baa2aa1`](https://github.com/nodejs/node/commit/fa8baa2aa1)] - **test**: enhance test-timers (Rich Trott) [#10960](https://github.com/nodejs/node/pull/10960)
- [[`74ff804dbd`](https://github.com/nodejs/node/commit/74ff804dbd)] - **test**: add regression tests for vm bugs (Anna Henningsen) [#10920](https://github.com/nodejs/node/pull/10920)
- [[`1a39bfb7e2`](https://github.com/nodejs/node/commit/1a39bfb7e2)] - **test**: increase coverage for exec() functions (cjihrig) [#10919](https://github.com/nodejs/node/pull/10919)
- [[`4b38744e9b`](https://github.com/nodejs/node/commit/4b38744e9b)] - **test**: add process.assert's test (abouthiroppy) [#10911](https://github.com/nodejs/node/pull/10911)
- [[`e7c953a5f9`](https://github.com/nodejs/node/commit/e7c953a5f9)] - **test**: update Buffer.lastIndexOf (<EMAIL>) [#10162](https://github.com/nodejs/node/pull/10162)
- [[`eb7ee50717`](https://github.com/nodejs/node/commit/eb7ee50717)] - **test**: improve code in test-crypto-verify (Adrian Estrada) [#10845](https://github.com/nodejs/node/pull/10845)
- [[`efa9845946`](https://github.com/nodejs/node/commit/efa9845946)] - **test**: refactor test-cli-eval.js (cjihrig) [#10898](https://github.com/nodejs/node/pull/10898)
- [[`b7bf43aa2b`](https://github.com/nodejs/node/commit/b7bf43aa2b)] - **test**: use common.fail() instead of assert(false) (cjihrig) [#10899](https://github.com/nodejs/node/pull/10899)
- [[`90a99177a3`](https://github.com/nodejs/node/commit/90a99177a3)] - **test**: add dgram.Socket.prototype.bind's test (abouthiroppy) [#10894](https://github.com/nodejs/node/pull/10894)
- [[`dc826caed2`](https://github.com/nodejs/node/commit/dc826caed2)] - **test**: update V8 flag in test (Franziska Hinkelmann) [#10917](https://github.com/nodejs/node/pull/10917)
- [[`537d954ed2`](https://github.com/nodejs/node/commit/537d954ed2)] - **test**: increase coverage of string-decoder (abouthiroppy) [#10863](https://github.com/nodejs/node/pull/10863)
- [[`3cd9833eff`](https://github.com/nodejs/node/commit/3cd9833eff)] - **test**: add tests for rs+, sr+ to test-fs-open-flags.js (abouthiroppy) [#10780](https://github.com/nodejs/node/pull/10780)
- [[`c8a069e544`](https://github.com/nodejs/node/commit/c8a069e544)] - **test**: improving coverage of dns-lookup (abouthiroppy) [#10844](https://github.com/nodejs/node/pull/10844)
- [[`939517abfd`](https://github.com/nodejs/node/commit/939517abfd)] - **test**: refactor test-fs-read-zero-length.js (abouthiroppy) [#10729](https://github.com/nodejs/node/pull/10729)
- [[`ffdf605f14`](https://github.com/nodejs/node/commit/ffdf605f14)] - **test**: improving coverage for dgram (abouthiroppy) [#10783](https://github.com/nodejs/node/pull/10783)
- [[`1666600f16`](https://github.com/nodejs/node/commit/1666600f16)] - **test**: improve code in test-console-instance (Adrian Estrada) [#10813](https://github.com/nodejs/node/pull/10813)
- [[`b496374363`](https://github.com/nodejs/node/commit/b496374363)] - **test**: improve code in test-domain-multi (Adrian Estrada) [#10798](https://github.com/nodejs/node/pull/10798)
- [[`46bbabe6c2`](https://github.com/nodejs/node/commit/46bbabe6c2)] - **test**: improve test-stream2-large-read-stall (stefan judis) [#10725](https://github.com/nodejs/node/pull/10725)
- [[`7f043779eb`](https://github.com/nodejs/node/commit/7f043779eb)] - **test**: improve code in test-http-host-headers (Adrian Estrada) [#10830](https://github.com/nodejs/node/pull/10830)
- [[`66c57a24c2`](https://github.com/nodejs/node/commit/66c57a24c2)] - **test**: add test case to test-http-response-statuscode.js (abouthiroppy) [#10808](https://github.com/nodejs/node/pull/10808)
- [[`4a7bb5b4d1`](https://github.com/nodejs/node/commit/4a7bb5b4d1)] - **test**: improve the code in test-crypto-dh (Adrian Estrada) [#10734](https://github.com/nodejs/node/pull/10734)
- [[`825842c185`](https://github.com/nodejs/node/commit/825842c185)] - **test**: getgroups() may contain duplicate GIDs (Sam Roberts) [#10389](https://github.com/nodejs/node/pull/10389)
- [[`c6618df2cc`](https://github.com/nodejs/node/commit/c6618df2cc)] - **test**: improve test stream transform constructor (Adrian Estrada) [#10699](https://github.com/nodejs/node/pull/10699)
- [[`51f4c8bf5c`](https://github.com/nodejs/node/commit/51f4c8bf5c)] - **test**: s/assert.equal/assert.strictEqual/ (Gibson Fahnestock) [#10698](https://github.com/nodejs/node/pull/10698)
- [[`a6bd287724`](https://github.com/nodejs/node/commit/a6bd287724)] - **test**: use eslint to fix var->const/let (Gibson Fahnestock) [#10685](https://github.com/nodejs/node/pull/10685)
- [[`e6b6ce429c`](https://github.com/nodejs/node/commit/e6b6ce429c)] - **test**: refactor test-http-mutable-headers.js (cjihrig) [#10664](https://github.com/nodejs/node/pull/10664)
- [[`8262d49a44`](https://github.com/nodejs/node/commit/8262d49a44)] - **test**: refactor cluster-preload.js (abouthiroppy) [#10701](https://github.com/nodejs/node/pull/10701)
- [[`fc0551072b`](https://github.com/nodejs/node/commit/fc0551072b)] - **test**: improve test-crypto-rsa-dsa (Adrian Estrada) [#10681](https://github.com/nodejs/node/pull/10681)
- [[`727c5e3c96`](https://github.com/nodejs/node/commit/727c5e3c96)] - **test**: improve test-fs-write-file-sync (Adrian Estrada) [#10624](https://github.com/nodejs/node/pull/10624)
- [[`50130220dc`](https://github.com/nodejs/node/commit/50130220dc)] - **test**: s/assert.notEqual()/assert.notStrictEqual()/ (cjihrig) [#10541](https://github.com/nodejs/node/pull/10541)
- [[`44174a52a6`](https://github.com/nodejs/node/commit/44174a52a6)] - **test**: refactor the code in test-util-debug.js (sivaprasanna) [#10531](https://github.com/nodejs/node/pull/10531)
- [[`b1c742e107`](https://github.com/nodejs/node/commit/b1c742e107)] - **test**: improve test-fs-access (Adrian Estrada) [#10542](https://github.com/nodejs/node/pull/10542)
- [[`db7b27abb9`](https://github.com/nodejs/node/commit/db7b27abb9)] - **test**: refactor beforeExit tests (Rich Trott) [#10581](https://github.com/nodejs/node/pull/10581)
- [[`33851d1e2c`](https://github.com/nodejs/node/commit/33851d1e2c)] - **test**: fix process.title expectation (Sakthipriyan Vairamani (thefourtheye)) [#10597](https://github.com/nodejs/node/pull/10597)
- [[`af2bea70e0`](https://github.com/nodejs/node/commit/af2bea70e0)] - **test**: refactor test-beforeexit-event-exit.js (cjihrig) [#10577](https://github.com/nodejs/node/pull/10577)
- [[`0a2fb0d3e1`](https://github.com/nodejs/node/commit/0a2fb0d3e1)] - **test**: refactor several parallel/test-timer tests (Beth Griggs) [#10524](https://github.com/nodejs/node/pull/10524)
- [[`dba8d20ccc`](https://github.com/nodejs/node/commit/dba8d20ccc)] - **test**: improve the code in test-fs-read-stream (Adrian Estrada) [#10556](https://github.com/nodejs/node/pull/10556)
- [[`eba9add48e`](https://github.com/nodejs/node/commit/eba9add48e)] - **test**: refactor test-timer-close (BethGriggs) [#10517](https://github.com/nodejs/node/pull/10517)
- [[`dd9aefde69`](https://github.com/nodejs/node/commit/dd9aefde69)] - **test**: use const for all require() calls (cjihrig) [#10550](https://github.com/nodejs/node/pull/10550)
- [[`807e99b81d`](https://github.com/nodejs/node/commit/807e99b81d)] - **test**: validate errors in test-buffer-indexof (Adrian Estrada) [#10752](https://github.com/nodejs/node/pull/10752)
- [[`32da59ab18`](https://github.com/nodejs/node/commit/32da59ab18)] - **test**: fix broken assertion (cjihrig) [#10840](https://github.com/nodejs/node/pull/10840)
- [[`29a4d354bc`](https://github.com/nodejs/node/commit/29a4d354bc)] - **test**: refactor test-cli-eval.js (Sumit Goel) [#10759](https://github.com/nodejs/node/pull/10759)
- [[`a06419b045`](https://github.com/nodejs/node/commit/a06419b045)] - **test**: refactor test-stream2-readable-wrap.js (David Goussev) [#10551](https://github.com/nodejs/node/pull/10551)
- [[`55377db9b0`](https://github.com/nodejs/node/commit/55377db9b0)] - **test**: refactor test-stream-transform-object (Rich Trott) [#10588](https://github.com/nodejs/node/pull/10588)
- [[`fb35ca3598`](https://github.com/nodejs/node/commit/fb35ca3598)] - **test**: test hmac binding robustness (Sam Roberts) [#10923](https://github.com/nodejs/node/pull/10923)
- [[`94a266e1ef`](https://github.com/nodejs/node/commit/94a266e1ef)] - **test**: refactor the code in test-fs-watch.js (sivaprasanna) [#10357](https://github.com/nodejs/node/pull/10357)
- [[`3575f5159e`](https://github.com/nodejs/node/commit/3575f5159e)] - **test**: reduce unmanaged parallelism in domain test (Joyee Cheung) [#10329](https://github.com/nodejs/node/pull/10329)
- [[`7822d86ee6`](https://github.com/nodejs/node/commit/7822d86ee6)] - **test**: increase usage of assert.ifError() (cjihrig) [#10543](https://github.com/nodejs/node/pull/10543)
- [[`e161dcf1fc`](https://github.com/nodejs/node/commit/e161dcf1fc)] - **test**: add dgram.Socket.prototype.sendto's test (abouthiroppy) [#10901](https://github.com/nodejs/node/pull/10901)
- [[`be3e82dbbb`](https://github.com/nodejs/node/commit/be3e82dbbb)] - **test**: check error message in test-fs-make-callback (legalcodes) [#10914](https://github.com/nodejs/node/pull/10914)
- [[`67d97bce5a`](https://github.com/nodejs/node/commit/67d97bce5a)] - **test**: improve test-assert (richnologies) [#10916](https://github.com/nodejs/node/pull/10916)
- [[`69a04a9c7b`](https://github.com/nodejs/node/commit/69a04a9c7b)] - **test**: increase coverage for punycode's decode (abouthiroppy) [#10940](https://github.com/nodejs/node/pull/10940)
- [[`8778fca82b`](https://github.com/nodejs/node/commit/8778fca82b)] - **test**: check fd 0,1,2 are used, not access mode (John Barboza) [#10339](https://github.com/nodejs/node/pull/10339)
- [[`e80f35c973`](https://github.com/nodejs/node/commit/e80f35c973)] - **test**: verify shell option internals (cjihrig) [#10924](https://github.com/nodejs/node/pull/10924)
- [[`9d5170f850`](https://github.com/nodejs/node/commit/9d5170f850)] - **test**: fix flaky test-regress-GH-897 (Rich Trott) [#10903](https://github.com/nodejs/node/pull/10903)
- [[`c60d87b1ad`](https://github.com/nodejs/node/commit/c60d87b1ad)] - **test**: don't connect to :: (use localhost instead) (Gibson Fahnestock) [#10854](https://github.com/nodejs/node/pull/10854)
- [[`aa4b028523`](https://github.com/nodejs/node/commit/aa4b028523)] - **test**: improve test-fs-open-flags (Vinícius do Carmo)
- [[`35d665958e`](https://github.com/nodejs/node/commit/35d665958e)] - **test**: increase coverage of \_http_outgoing (abouthiroppy) [#10820](https://github.com/nodejs/node/pull/10820)
- [[`c4f16949b8`](https://github.com/nodejs/node/commit/c4f16949b8)] - **test**: add message verification on assert.throws (Travis Meisenheimer) [#10890](https://github.com/nodejs/node/pull/10890)
- [[`5ce2ac800b`](https://github.com/nodejs/node/commit/5ce2ac800b)] - **test**: refactor test-repl-tab-complete (Rich Trott) [#10879](https://github.com/nodejs/node/pull/10879)
- [[`999f685a69`](https://github.com/nodejs/node/commit/999f685a69)] - **test**: simplify array initialization (Rich Trott) [#10860](https://github.com/nodejs/node/pull/10860)
- [[`c77078f29f`](https://github.com/nodejs/node/commit/c77078f29f)] - **test**: have inspector test pick an open port (Eugene Ostroukhov) [#10861](https://github.com/nodejs/node/pull/10861)
- [[`aa8771f842`](https://github.com/nodejs/node/commit/aa8771f842)] - **test**: use common.hasIntl in tests related to ICU (Daijiro Wachi) [#10841](https://github.com/nodejs/node/pull/10841)
- [[`5b38776243`](https://github.com/nodejs/node/commit/5b38776243)] - **test**: add http-common's test (abouthiroppy) [#10832](https://github.com/nodejs/node/pull/10832)
- [[`96babb2090`](https://github.com/nodejs/node/commit/96babb2090)] - **test**: tests for \_readableStream.awaitDrain (Mark) [#8914](https://github.com/nodejs/node/pull/8914)
- [[`7165f1d409`](https://github.com/nodejs/node/commit/7165f1d409)] - **test**: improve the code in test-process-cpuUsage (Adrian Estrada) [#10714](https://github.com/nodejs/node/pull/10714)
- [[`b5c0b43efa`](https://github.com/nodejs/node/commit/b5c0b43efa)] - **test**: increase test-crypto.js strictness (Rich Trott) [#10784](https://github.com/nodejs/node/pull/10784)
- [[`d818cfaaad`](https://github.com/nodejs/node/commit/d818cfaaad)] - **test**: improve test-fs-write-stream-throw-type (Adrian Estrada) [#10779](https://github.com/nodejs/node/pull/10779)
- [[`77cbc26a96`](https://github.com/nodejs/node/commit/77cbc26a96)] - **test**: delete duplicate test of noAssert in readUInt\* (larissayvette) [#10791](https://github.com/nodejs/node/pull/10791)
- [[`36db5a663a`](https://github.com/nodejs/node/commit/36db5a663a)] - **test**: add http_incoming's matchKnownFields test (abouthiroppy) [#10811](https://github.com/nodejs/node/pull/10811)
- [[`31d3a22989`](https://github.com/nodejs/node/commit/31d3a22989)] - **test**: skip test-icu-transcode if Intl is not present (Daijiro Wachi) [#10707](https://github.com/nodejs/node/pull/10707)
- [[`8b02b4ebb4`](https://github.com/nodejs/node/commit/8b02b4ebb4)] - **test**: check error msg test-writeint.js (Irene Li) [#10755](https://github.com/nodejs/node/pull/10755)
- [[`5aad0ccefe`](https://github.com/nodejs/node/commit/5aad0ccefe)] - **test**: no unused args test-fs-watch-file.js (istinson) [#10758](https://github.com/nodejs/node/pull/10758)
- [[`fca0da711d`](https://github.com/nodejs/node/commit/fca0da711d)] - **test**: improve tests in pummel/test-exec (Chase Starr) [#10757](https://github.com/nodejs/node/pull/10757)
- [[`7d917dcb27`](https://github.com/nodejs/node/commit/7d917dcb27)] - **test**: fix temp-dir option in tools/test.py (Gibson Fahnestock) [#10723](https://github.com/nodejs/node/pull/10723)
- [[`6b54024324`](https://github.com/nodejs/node/commit/6b54024324)] - **test**: use realpath for NODE_TEST_DIR in common.js (Gibson Fahnestock) [#10723](https://github.com/nodejs/node/pull/10723)
- [[`c6aeb4491b`](https://github.com/nodejs/node/commit/c6aeb4491b)] - **test**: fix linting for test-tls-add-ca-cert.js (Sam Roberts) [#10771](https://github.com/nodejs/node/pull/10771)
- [[`542f65c66b`](https://github.com/nodejs/node/commit/542f65c66b)] - **test**: tls cert chain completion scenarios (Sam Roberts) [#10389](https://github.com/nodejs/node/pull/10389)
- [[`97a8bd20c6`](https://github.com/nodejs/node/commit/97a8bd20c6)] - **test**: check tls server verification with addCACert (Sam Roberts) [#10389](https://github.com/nodejs/node/pull/10389)
- [[`ebcf834c14`](https://github.com/nodejs/node/commit/ebcf834c14)] - **test**: move common tls connect setup into fixtures (Sam Roberts) [#10389](https://github.com/nodejs/node/pull/10389)
- [[`30926ac6d6`](https://github.com/nodejs/node/commit/30926ac6d6)] - **test**: move resource intensive test to sequential (Rich Trott) [#10744](https://github.com/nodejs/node/pull/10744)
- [[`06a1e9eb7b`](https://github.com/nodejs/node/commit/06a1e9eb7b)] - **test**: add test for noAssert option in buf.read\*() (larissayvette) [#10713](https://github.com/nodejs/node/pull/10713)
- [[`160d0381d2`](https://github.com/nodejs/node/commit/160d0381d2)] - **test**: refactor test-crypto-padding-aes256 (adelmann) [#10622](https://github.com/nodejs/node/pull/10622)
- [[`cb111c96cc`](https://github.com/nodejs/node/commit/cb111c96cc)] - **test**: refactor the code of test-keep-alive.js (sivaprasanna) [#10684](https://github.com/nodejs/node/pull/10684)
- [[`dbb7727320`](https://github.com/nodejs/node/commit/dbb7727320)] - **test**: validate 'expected' argument to mustCall() (Nathan Friedly) [#10692](https://github.com/nodejs/node/pull/10692)
- [[`e408f0a1f5`](https://github.com/nodejs/node/commit/e408f0a1f5)] - **test**: fix misplaced ) in http response statuscode test (Nathan Friedly) [#10692](https://github.com/nodejs/node/pull/10692)
- [[`4c8676bc26`](https://github.com/nodejs/node/commit/4c8676bc26)] - **test**: refactor test-doctool-html.js (abouthiroppy) [#10696](https://github.com/nodejs/node/pull/10696)
- [[`da572131db`](https://github.com/nodejs/node/commit/da572131db)] - **test**: improve the code in test-process-hrtime (Adrian Estrada) [#10667](https://github.com/nodejs/node/pull/10667)
- [[`17d9a739c1`](https://github.com/nodejs/node/commit/17d9a739c1)] - **test**: refactor test-watch-file.js (sivaprasanna) [#10679](https://github.com/nodejs/node/pull/10679)
- [[`cf5579d746`](https://github.com/nodejs/node/commit/cf5579d746)] - **test**: improve zlib-from-gzip-with-trailing-garbage (Michael Lefkowitz) [#10674](https://github.com/nodejs/node/pull/10674)
- [[`2d856097b3`](https://github.com/nodejs/node/commit/2d856097b3)] - **test**: refactor the code in test-child-process-spawn-loop.js (sivaprasanna) [#10605](https://github.com/nodejs/node/pull/10605)
- [[`1329eb47f0`](https://github.com/nodejs/node/commit/1329eb47f0)] - **test**: allow testing uid and gid separately (cjihrig) [#10647](https://github.com/nodejs/node/pull/10647)
- [[`4aa32c196a`](https://github.com/nodejs/node/commit/4aa32c196a)] - **test**: improve code in test-https-strict (Adrian Estrada) [#10648](https://github.com/nodejs/node/pull/10648)
- [[`e78de99bcb`](https://github.com/nodejs/node/commit/e78de99bcb)] - **test**: improve test-http-chunked-304 (Adrian Estrada) [#10462](https://github.com/nodejs/node/pull/10462)
- [[`ff23d8112a`](https://github.com/nodejs/node/commit/ff23d8112a)] - **test**: improve test-fs-readfile-zero-byte-liar (Adrian Estrada) [#10570](https://github.com/nodejs/node/pull/10570)
- [[`38bdfb0b8e`](https://github.com/nodejs/node/commit/38bdfb0b8e)] - **test**: refactor test-fs-utimes (Junshu Okamoto) [#9290](https://github.com/nodejs/node/pull/9290)
- [[`09f35a49e3`](https://github.com/nodejs/node/commit/09f35a49e3)] - **test**: provide duration/interval to timers (Rich Trott) [#9472](https://github.com/nodejs/node/pull/9472)
- [[`06a82436c2`](https://github.com/nodejs/node/commit/06a82436c2)] - **test**: improve test-event-emitter-modify-in-emit (Adrian Estrada) [#10600](https://github.com/nodejs/node/pull/10600)
- [[`736b95a617`](https://github.com/nodejs/node/commit/736b95a617)] - **test**: check error and cleanups in test-fs-read-buffer (Anna Henningsen) [#10611](https://github.com/nodejs/node/pull/10611)
- [[`a77940c2d5`](https://github.com/nodejs/node/commit/a77940c2d5)] - **test**: mark test-tty-wrap as flaky for AIX (Michael Dawson) [#10618](https://github.com/nodejs/node/pull/10618)
- [[`cf875d17f3`](https://github.com/nodejs/node/commit/cf875d17f3)] - **test**: improve test-fs-null-bytes (Adrian Estrada) [#10521](https://github.com/nodejs/node/pull/10521)
- [[`656ba86a27`](https://github.com/nodejs/node/commit/656ba86a27)] - **test**: fix Coverity warning in inspector test (Eugene Ostroukhov) [#10510](https://github.com/nodejs/node/pull/10510)
- [[`9916ee8c36`](https://github.com/nodejs/node/commit/9916ee8c36)] - **test**: refactor test-https-truncate (Rich Trott) [#10225](https://github.com/nodejs/node/pull/10225)
- [[`4ff1d3107f`](https://github.com/nodejs/node/commit/4ff1d3107f)] - **test**: add http.ClientRequest defaults test (Brian White) [#10654](https://github.com/nodejs/node/pull/10654)
- [[`1555ced404`](https://github.com/nodejs/node/commit/1555ced404)] - **test, win**: fix up symlink tests (Hitesh Kanwathirtha) [#10477](https://github.com/nodejs/node/pull/10477)
- [[`4323c8018e`](https://github.com/nodejs/node/commit/4323c8018e)] - **test,cluster**: add test-cluster-worker-deprecated (Rich Trott) [#10675](https://github.com/nodejs/node/pull/10675)
- [[`33af09fe6a`](https://github.com/nodejs/node/commit/33af09fe6a)] - **test,net**: add tests for server.connections (Rich Trott) [#10762](https://github.com/nodejs/node/pull/10762)
- [[`fc2db50021`](https://github.com/nodejs/node/commit/fc2db50021)] - **test,repl**: add coverage for repl .clear+useGlobal (Rich Trott) [#10777](https://github.com/nodejs/node/pull/10777)
- [[`84bf04b0c1`](https://github.com/nodejs/node/commit/84bf04b0c1)] - **test,util**: remove lint workarounds (Rich Trott) [#10785](https://github.com/nodejs/node/pull/10785)
- [[`c6af766ad9`](https://github.com/nodejs/node/commit/c6af766ad9)] - **test-console**: streamline arrow fn and refine regex (John Maguire) [#11039](https://github.com/nodejs/node/pull/11039)
- [[`8fae9d4cfb`](https://github.com/nodejs/node/commit/8fae9d4cfb)] - **tools**: rename eslintrc to an undeprecated format (Sakthipriyan Vairamani) [#7699](https://github.com/nodejs/node/pull/7699)
- [[`dd5a4e1d75`](https://github.com/nodejs/node/commit/dd5a4e1d75)] - **tools**: update ESLint to current version (Rich Trott) [#10561](https://github.com/nodejs/node/pull/10561)
- [[`c92b8ecd81`](https://github.com/nodejs/node/commit/c92b8ecd81)] - **(SEMVER-MINOR)** **tools**: add mdn link for Iterator (James M Snell) [#10620](https://github.com/nodejs/node/pull/10620)
- [[`dec5900c42`](https://github.com/nodejs/node/commit/dec5900c42)] - **tools**: add lint rule to enforce timer arguments (Rich Trott) [#9472](https://github.com/nodejs/node/pull/9472)
- [[`891874406a`](https://github.com/nodejs/node/commit/891874406a)] - **tools**: remove no-useless-regex-char-class-escape (Rich Trott) [#10561](https://github.com/nodejs/node/pull/10561)
- [[`1634a7017e`](https://github.com/nodejs/node/commit/1634a7017e)] - **tools**: remove custom align-function-arguments rule (Rich Trott) [#10561](https://github.com/nodejs/node/pull/10561)
- [[`31f8f6f768`](https://github.com/nodejs/node/commit/31f8f6f768)] - **tools, test**: require const/let in test (Gibson Fahnestock) [#10685](https://github.com/nodejs/node/pull/10685)
- [[`d39543e73d`](https://github.com/nodejs/node/commit/d39543e73d)] - **tools,doc**: add Google Analytics tracking. (Phillip Johnsen) [#6601](https://github.com/nodejs/node/pull/6601)
- [[`3adda4b2ad`](https://github.com/nodejs/node/commit/3adda4b2ad)] - **tools,test**: enforce assert.ifError with lint rule (Teddy Katz) [#10671](https://github.com/nodejs/node/pull/10671)
- [[`438a98ca95`](https://github.com/nodejs/node/commit/438a98ca95)] - **url**: make URLSearchParams/Iterator match spec (Timothy Gu) [#11057](https://github.com/nodejs/node/pull/11057)
- [[`2bfd58adb1`](https://github.com/nodejs/node/commit/2bfd58adb1)] - **url**: define @@toStringTag as a data property (Timothy Gu) [#10906](https://github.com/nodejs/node/pull/10906)
- [[`f1851cb8e4`](https://github.com/nodejs/node/commit/f1851cb8e4)] - **url**: do not public expose inspect methods on URL (Timothy Gu) [#10906](https://github.com/nodejs/node/pull/10906)
- [[`b48b80f630`](https://github.com/nodejs/node/commit/b48b80f630)] - **url**: stop exporting originFor() (Timothy Gu) [#10955](https://github.com/nodejs/node/pull/10955)
- [[`c0c1a4c029`](https://github.com/nodejs/node/commit/c0c1a4c029)] - **url**: refactor lib/internal/url.js (Rich Trott) [#10912](https://github.com/nodejs/node/pull/10912)
- [[`2f9fdc454f`](https://github.com/nodejs/node/commit/2f9fdc454f)] - **(SEMVER-MINOR)** **url**: allow use of URL with http.request and https.request (James M Snell) [#10638](https://github.com/nodejs/node/pull/10638)
- [[`95faa55ab9`](https://github.com/nodejs/node/commit/95faa55ab9)] - **url**: check forEach callback is a function (Timothy Gu) [#10905](https://github.com/nodejs/node/pull/10905)
- [[`3642f35d09`](https://github.com/nodejs/node/commit/3642f35d09)] - **url**: add return value to ToUnicode/ToAscii stubs (Birunthan Mohanathas) [#10893](https://github.com/nodejs/node/pull/10893)
- [[`021338dc6d`](https://github.com/nodejs/node/commit/021338dc6d)] - **url**: export URLSearchParams (Timothy Gu)
- [[`5d33c96679`](https://github.com/nodejs/node/commit/5d33c96679)] - **url**: improving URLSearchParams (Timothy Gu) [#10399](https://github.com/nodejs/node/pull/10399)
- [[`824978e337`](https://github.com/nodejs/node/commit/824978e337)] - **url**: do not decode arbitrary %2e sequences in paths (James M Snell) [#10602](https://github.com/nodejs/node/pull/10602)
- [[`e46bdcf2bb`](https://github.com/nodejs/node/commit/e46bdcf2bb)] - **url**: change null password handling (James M Snell) [#10601](https://github.com/nodejs/node/pull/10601)
- [[`2b01138451`](https://github.com/nodejs/node/commit/2b01138451)] - **url**: TupleOrigin#toString use unicode by default (Joyee Cheung) [#10552](https://github.com/nodejs/node/pull/10552)
- [[`9f6d1f6fc2`](https://github.com/nodejs/node/commit/9f6d1f6fc2)] - **util**: improve readability of normalizeEncoding (Joyee Cheung) [#10439](https://github.com/nodejs/node/pull/10439)
- [[`d628f3a227`](https://github.com/nodejs/node/commit/d628f3a227)] - **util**: avoid out-of-bounds arguments index access (Teddy Katz) [#10569](https://github.com/nodejs/node/pull/10569)
- [[`2641cd496d`](https://github.com/nodejs/node/commit/2641cd496d)] - **vm**: improve performance of vm.runIn\*() (Rich Trott) [#10816](https://github.com/nodejs/node/pull/10816)

Windows 32-bit Installer: https://nodejs.org/dist/v7.5.0/node-v7.5.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v7.5.0/node-v7.5.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v7.5.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v7.5.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v7.5.0/node-v7.5.0.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v7.5.0/node-v7.5.0-darwin-x64.tar.gz \
Linux 32-bit Binary: https://nodejs.org/dist/v7.5.0/node-v7.5.0-linux-x86.tar.xz \
Linux 64-bit Binary: https://nodejs.org/dist/v7.5.0/node-v7.5.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v7.5.0/node-v7.5.0-linux-ppc64le.tar.xz \
Linux PPC BE 64-bit Binary: https://nodejs.org/dist/v7.5.0/node-v7.5.0-linux-ppc64.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v7.5.0/node-v7.5.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v7.5.0/node-v7.5.0-aix-ppc64.tar.gz \
SmartOS 32-bit Binary: https://nodejs.org/dist/v7.5.0/node-v7.5.0-sunos-x86.tar.xz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v7.5.0/node-v7.5.0-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v7.5.0/node-v7.5.0-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v7.5.0/node-v7.5.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v7.5.0/node-v7.5.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v7.5.0/node-v7.5.0.tar.gz \
Other release files: https://nodejs.org/dist/v7.5.0/ \
Documentation: https://nodejs.org/docs/v7.5.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

b97c867927ee4d32526ff411b2fc53b6131f5defbf3c1f487c7068da7d2ad3bf  node-v7.5.0-aix-ppc64.tar.gz
97b54cc473710585c7277c1786cd7085eae157f6dc8f77b71bc4de1cb3dacda7  node-v7.5.0-darwin-x64.tar.gz
e9e3537e9adc24b6845a36c2545492c24d550b4c87223ed0c0e951aad543f5a4  node-v7.5.0-darwin-x64.tar.xz
f32d269310ef465ccb0937a90ee2a495749988c54acb75348989aed2aed9fe3f  node-v7.5.0-headers.tar.gz
6c13bb11ea0d925b8d348932d459c876cfd0b754b369015a75661a8293c4c469  node-v7.5.0-headers.tar.xz
101e89fd4547569bbc4bcf1081bf6bb6c8410bdfe913ffbf35f25789eda9bca9  node-v7.5.0-linux-arm64.tar.gz
3b548d106dccb53ad5468eb3120f9db9b71c5ae854dfe5cfde9ba6f5d7519bfa  node-v7.5.0-linux-arm64.tar.xz
18e6710519eefb0345878618e9f8139361f9efb966a72e522e4011bd38b5d9f9  node-v7.5.0-linux-armv6l.tar.gz
9560e61603ef8a36ee881f55e4f505fe1c62377a3479a5f3dc8a8d8da6f0c2a1  node-v7.5.0-linux-armv6l.tar.xz
cc8feda1f4f2eb995857f98ca96390d3fd794edc8e14363072f5923d81ae96f7  node-v7.5.0-linux-armv7l.tar.gz
b4ceb22b2ea519d41be76161be95ac96c01b5fd8cf17060635497621b89d171b  node-v7.5.0-linux-armv7l.tar.xz
79fe8b93e2c3ffc9636d96a91fb3b8728462132ba4b6a4b0dca1a50fe1523237  node-v7.5.0-linux-ppc64le.tar.gz
3603ea26ac041d2bedda91680117b7046d6c78d9dbc905599309725c8e2337b8  node-v7.5.0-linux-ppc64le.tar.xz
b3fc5671dfd8a646abbd4db13a4eb16f6ae8d77688584b17d3fa3ff0b242fa64  node-v7.5.0-linux-ppc64.tar.gz
5c52f7215c8f460c562e4441cc06c2ffb48391acf30e5530cd5b3c18fe4fbb50  node-v7.5.0-linux-ppc64.tar.xz
bf2ec793df9ad0f543e37b6d02ef53d12162f96a8bd2e8359b2405b9e34d3d11  node-v7.5.0-linux-s390x.tar.gz
702cc054dc7072a1690b6211cef13aa5ff210665aa40dfcdc763158651a1c06a  node-v7.5.0-linux-s390x.tar.xz
901cc6ef6a12e6807d21090868b16bd41df3e9c41a92065bd0dfea1ef1a35730  node-v7.5.0-linux-x64.tar.gz
600e38b3b76baa2d9537ecf9b6d39b6c021326683db0f42e12178530ff867b61  node-v7.5.0-linux-x64.tar.xz
976dca95f2a06034c37415869472bbce288143d1660af4b79304caa063ac766f  node-v7.5.0-linux-x86.tar.gz
28069d4ade798a940c7844e47b1220b062e31759d0205c233a4943a02fd7fce5  node-v7.5.0-linux-x86.tar.xz
2049c13ff11cce47a31cbf434aedc7449cd1248e630d997e7f8c03967b27d054  node-v7.5.0.pkg
5eb6d7e2305b59bc0acf4d85eb07282ee06bbcf55677535935bb8069b7477029  node-v7.5.0-sunos-x64.tar.gz
7e5b15d9adacaa52e19381db64f4494c18034562385b5520da1271d4122682c8  node-v7.5.0-sunos-x64.tar.xz
62ffc2f5f8f6d5006d31c64bfab0826fbacc6a00625810bd23ddfcece5bf820d  node-v7.5.0-sunos-x86.tar.gz
c30d9bc9ad01c43bcd95f83ff7e43da3d88c1eab57f83fbe08cbe4d0ee4c75dc  node-v7.5.0-sunos-x86.tar.xz
0da8e0288b5c0f136e650b7119219968720caf88b5a67ef0591555113f0844c2  node-v7.5.0.tar.gz
f99ee74647fe223eb03f2dd1dc6acdc14d9a881621376c848236c8d2ac8afd03  node-v7.5.0.tar.xz
0bc433d59d2ce1b991fc5f56ab69bd170e935841650bae40d3626da61f1fce6d  node-v7.5.0-win-x64.7z
b47b31db2d78745c223c98d62a2d12a29a683eafa8277ce13f384ba86e2177cb  node-v7.5.0-win-x64.zip
953162b30208ccc77b3a3548c5e5dc9a6fb3e5489c5fd1ef313c5d0983a1eea9  node-v7.5.0-win-x86.7z
ff73fa43bd5e5a34a9a1d1dac86f940d0cd1159127adc9e6cc3e108b891a0bfc  node-v7.5.0-win-x86.zip
be6f9b7575a8a2d26b959814899d6aa4a91bc06bf65081064bb3b1cbe7a3e142  node-v7.5.0-x64.msi
2e63fde6af03e510dee11fbf0217a3a96a64fed712ae5ec6a8ae6541df791e77  node-v7.5.0-x86.msi
b9c25d0a25c1f884351d7f9a45b0aa006401587cf94c99ac5045126de2daf0d7  win-x64/node.exe
1c9a1a9daf54fd24bd53f72189ad71c9620de827865d4448aabb0d58f8f8dd41  win-x64/node.lib
0e3a3dda9a32c1c153c5a5ae852dc01f345dad4822342800c36ee39cb31828ac  win-x64/node_pdb.7z
e46d1e2ed36d04230f68c08fc7710ad61ce734c8cb7367e29bd3a0e62e89febf  win-x64/node_pdb.zip
1ff170e3fa92765b676f12a514944a3f3f95df463b335bb897ddaafa108e06be  win-x86/node.exe
6b1796289ac29a6b242e7a9b2ab4c1c2064527e6efb06af30b8c60a24e723268  win-x86/node.lib
cc91420405a7d34bf5a46d2915d1265c625532919f681eab6e70345af193f8a3  win-x86/node_pdb.7z
4f0c765e1687b76003b4152cdd4e66acaead160cb9f44d40bb4f8e9ab51a6d91  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----
Comment: GPGTools - https://gpgtools.org

iQIcBAEBCAAGBQJYkUvJAAoJELY7U1pMIGyp37AQANX1qwusXhOyNuUxH3Ugbmva
qqlVrgXkZIH6O18+u45ibg/mDAejaV/G5pdRh3IIoPCkhwXfetqaqZD8oYEZ8h37
HG6IkqbYoagbzAjgywtXtQkrdFI1N+HcgHFKbT1N+NYFQpc162MBWGm9xDby04ze
oy+gLbicfXGD/g8C3NbLB0EhfMSuYvY80WjTMC+HVDViS598FPFwXUXjUxAP0/+F
7nBwvftDnO0kjKMgg45RTyZHxV+kaYP9UxSZqM9M8DFTyGXflJUh8GWkkZFbw50q
lGBUkTuaNOEqbnHq8Sl5M31xlH+qufZpTNjQuMRjK+Zo6xLOdESz/r/eRXyXoFK4
vtt7UDM0rxOi+rFBVDfZUG0qud/6vxWj0koGXgQ3ZX3ToC7dRlXcCh5QwcoKmoF5
xkH17gjlUTkl6Y03C7jz265N2IWZpGzmjiQe9J/W+Ud4haQEYe6xsiSqadhaHFcq
1p0DuCgsByJ8smxZGVqVhrmIfHD5NzhWNLdW3rsR5U0FrDGiUjrYYgFe8tfR6b4i
gAYVAYqxYWCZWNk5cjeVvCw4d1JBmWTNv4MJKN+jKA6cQdNxMVXWjP0E16XbgWcb
cGcXNdmLapwbFEBy+nIymru1hc2mLVlhrMQG2El+Dssf3VeSPKN4xZP4E9kpKjGa
OfEyfjbqnwLz45aYM9C6
=5zK/
-----END PGP SIGNATURE-----

```
