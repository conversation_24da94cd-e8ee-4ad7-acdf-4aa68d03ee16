---
date: '2018-11-02T12:34:59.111Z'
category: release
title: Node v11.1.0 (Current)
layout: blog-post
author: <PERSON><PERSON><PERSON>
---

### Notable changes

- **deps**
  - Updated ICU to 63.1. [#23715](https://github.com/nodejs/node/pull/23715)
- **repl**
  - Top-level for-await-of is now supported in the REPL. [#23841](https://github.com/nodejs/node/pull/23841)
- **timers**
  - Fixed an issue that could cause timers to enter an infinite loop. [#23870](https://github.com/nodejs/node/pull/23870)

### Commits

- [[`2c2e2b53ab`](https://github.com/nodejs/node/commit/2c2e2b53ab)] - **benchmark**: fix bench-mkdirp to use recursive option (<PERSON>) [#23699](https://github.com/nodejs/node/pull/23699)
- [[`787e13b41c`](https://github.com/nodejs/node/commit/787e13b41c)] - **build**: expose more openssl categories for addons (<PERSON>) [#23344](https://github.com/nodejs/node/pull/23344)
- [[`b8f3bb107e`](https://github.com/nodejs/node/commit/b8f3bb107e)] - **build**: add lint-py which uses flake8 (cclauss) [#21952](https://github.com/nodejs/node/pull/21952)
- [[`35c3c4ba68`](https://github.com/nodejs/node/commit/35c3c4ba68)] - **build**: allow for overwriting of use_openssl_def (Shelley Vohr) [#23763](https://github.com/nodejs/node/pull/23763)
- [[`5c35d0db47`](https://github.com/nodejs/node/commit/5c35d0db47)] - **build,meta**: switch to gcc-4.9 on travis (Refael Ackermann) [#23778](https://github.com/nodejs/node/pull/23778)
- [[`141aec9564`](https://github.com/nodejs/node/commit/141aec9564)] - **crypto**: add SET_INTEGER_CONSANT macro (Daniel Bevenius) [#23687](https://github.com/nodejs/node/pull/23687)
- [[`4112a10abe`](https://github.com/nodejs/node/commit/4112a10abe)] - **crypto**: strip unwanted space from openssl version (Sam Roberts) [#23678](https://github.com/nodejs/node/pull/23678)
- [[`2cc4f5c923`](https://github.com/nodejs/node/commit/2cc4f5c923)] - **deps**: patch V8 to 7.0.276.32 (Michaël Zasso) [#23851](https://github.com/nodejs/node/pull/23851)
- [[`0312d8b2cd`](https://github.com/nodejs/node/commit/0312d8b2cd)] - **deps**: fix shim for `v8::Value::IntegerValue()` (Anna Henningsen) [#23898](https://github.com/nodejs/node/pull/23898)
- [[`9011db426e`](https://github.com/nodejs/node/commit/9011db426e)] - **(SEMVER-MINOR)** **deps**: move more deprecations to V8_DEPRECATED (Anna Henningsen) [#23414](https://github.com/nodejs/node/pull/23414)
- [[`e5b51cc496`](https://github.com/nodejs/node/commit/e5b51cc496)] - **(SEMVER-MINOR)** **deps**: icu 63.1 bump (CLDR 34) (Steven R. Loomis) [#23715](https://github.com/nodejs/node/pull/23715)
- [[`ab58439916`](https://github.com/nodejs/node/commit/ab58439916)] - **deps**: icu: apply workaround patch (Steven R. Loomis) [#23764](https://github.com/nodejs/node/pull/23764)
- [[`3b66a8d893`](https://github.com/nodejs/node/commit/3b66a8d893)] - **deps**: fix wrong default for v8 handle zapping (Refael Ackermann) [#23801](https://github.com/nodejs/node/pull/23801)
- [[`26510fbd8e`](https://github.com/nodejs/node/commit/26510fbd8e)] - **doc**: add branding to style guide (Rich Trott) [#23967](https://github.com/nodejs/node/pull/23967)
- [[`33053ec8d7`](https://github.com/nodejs/node/commit/33053ec8d7)] - **doc**: use Node.js instead of Node (Rich Trott) [#23967](https://github.com/nodejs/node/pull/23967)
- [[`ec009f620c`](https://github.com/nodejs/node/commit/ec009f620c)] - **doc**: revise BUILDING.md (Rich Trott) [#23966](https://github.com/nodejs/node/pull/23966)
- [[`da494ef889`](https://github.com/nodejs/node/commit/da494ef889)] - **doc**: clarify fd behaviour with {read,write}File (Sakthipriyan Vairamani (thefourtheye)) [#23706](https://github.com/nodejs/node/pull/23706)
- [[`539e1233b0`](https://github.com/nodejs/node/commit/539e1233b0)] - **doc**: moved test instructions to BUILDING.md (Kamat, Trivikram) [#23949](https://github.com/nodejs/node/pull/23949)
- [[`cc65fee1d3`](https://github.com/nodejs/node/commit/cc65fee1d3)] - **doc**: fix typographical issues (Denis McDonald) [#23970](https://github.com/nodejs/node/pull/23970)
- [[`ee6b0395f5`](https://github.com/nodejs/node/commit/ee6b0395f5)] - **doc**: sort markdown refs in errors (Sam Roberts) [#23972](https://github.com/nodejs/node/pull/23972)
- [[`ee299c7ef1`](https://github.com/nodejs/node/commit/ee299c7ef1)] - **doc**: remove "idiomatic choice" from queueMicrotask (Rod Vagg) [#23885](https://github.com/nodejs/node/pull/23885)
- [[`147e5d5792`](https://github.com/nodejs/node/commit/147e5d5792)] - **doc**: document HPE_HEADER_OVERFLOW error (Sam Roberts) [#23963](https://github.com/nodejs/node/pull/23963)
- [[`24c6a02930`](https://github.com/nodejs/node/commit/24c6a02930)] - **doc**: add documentation for http.IncomingMessage$complete (James M Snell) [#23914](https://github.com/nodejs/node/pull/23914)
- [[`82ee6c3e47`](https://github.com/nodejs/node/commit/82ee6c3e47)] - **doc**: remove mailing list (Rich Trott) [#23932](https://github.com/nodejs/node/pull/23932)
- [[`99fffff6e0`](https://github.com/nodejs/node/commit/99fffff6e0)] - **doc**: remove notice of dashes in V8 options (Denys Otrishko) [#23903](https://github.com/nodejs/node/pull/23903)
- [[`8b5339da14`](https://github.com/nodejs/node/commit/8b5339da14)] - **doc**: rename README section for Release Keys (Rich Trott) [#23927](https://github.com/nodejs/node/pull/23927)
- [[`676875195b`](https://github.com/nodejs/node/commit/676875195b)] - **doc**: add note about ABI compatibility (Myles Borins) [#22237](https://github.com/nodejs/node/pull/22237)
- [[`f01a806276`](https://github.com/nodejs/node/commit/f01a806276)] - **doc**: add optional callback to socket.end() (Ajido) [#23937](https://github.com/nodejs/node/pull/23937)
- [[`64c205d9bc`](https://github.com/nodejs/node/commit/64c205d9bc)] - **doc**: make example more clarified in cluster.md (ZYSzys) [#23931](https://github.com/nodejs/node/pull/23931)
- [[`748dbf9778`](https://github.com/nodejs/node/commit/748dbf9778)] - **doc**: simplify valid security issue descriptions (Rich Trott) [#23881](https://github.com/nodejs/node/pull/23881)
- [[`e241398ef6`](https://github.com/nodejs/node/commit/e241398ef6)] - **doc**: simplify path.basename() on POSIX and Windows (ZYSzys) [#23864](https://github.com/nodejs/node/pull/23864)
- [[`49b32af5ab`](https://github.com/nodejs/node/commit/49b32af5ab)] - **doc**: document nullptr comparisons in style guide (Anna Henningsen) [#23805](https://github.com/nodejs/node/pull/23805)
- [[`0ba49fec12`](https://github.com/nodejs/node/commit/0ba49fec12)] - **doc**: remove problematic example from README (Rich Trott) [#23817](https://github.com/nodejs/node/pull/23817)
- [[`d808d27120`](https://github.com/nodejs/node/commit/d808d27120)] - **doc**: use Cookie in request.setHeader() examples (Luigi Pinca) [#23707](https://github.com/nodejs/node/pull/23707)
- [[`1baba9b061`](https://github.com/nodejs/node/commit/1baba9b061)] - **doc**: NODE_EXTRA_CA_CERTS is ignored if setuid root (Ben Noordhuis) [#23770](https://github.com/nodejs/node/pull/23770)
- [[`dd5afbe05f`](https://github.com/nodejs/node/commit/dd5afbe05f)] - **doc**: add review suggestions to require() (erickwendel) [#23605](https://github.com/nodejs/node/pull/23605)
- [[`db113a24e0`](https://github.com/nodejs/node/commit/db113a24e0)] - **doc**: document and warn if the ICU version is too old (Steven R. Loomis) [#23766](https://github.com/nodejs/node/pull/23766)
- [[`c30de85ca5`](https://github.com/nodejs/node/commit/c30de85ca5)] - **doc**: move @phillipj to emeriti (Phillip Johnsen) [#23790](https://github.com/nodejs/node/pull/23790)
- [[`84fdb1cc0e`](https://github.com/nodejs/node/commit/84fdb1cc0e)] - **doc**: add note about removeListener order (James M Snell) [#23762](https://github.com/nodejs/node/pull/23762)
- [[`f4c4b2b41b`](https://github.com/nodejs/node/commit/f4c4b2b41b)] - **doc**: document ACL limitation for fs.access on Windows (James M Snell) [#23772](https://github.com/nodejs/node/pull/23772)
- [[`83b776c864`](https://github.com/nodejs/node/commit/83b776c864)] - **doc**: document that addMembership must be called once in a cluster (James M Snell) [#23746](https://github.com/nodejs/node/pull/23746)
- [[`1851cf4f83`](https://github.com/nodejs/node/commit/1851cf4f83)] - **doc, test**: document and test vm timeout escapes (James M Snell) [#23743](https://github.com/nodejs/node/pull/23743)
- [[`b4b101fed6`](https://github.com/nodejs/node/commit/b4b101fed6)] - **(SEMVER-MINOR)** **fs**: default open/openSync flags argument to 'r' (Ben Noordhuis) [#23767](https://github.com/nodejs/node/pull/23767)
- [[`1c5ffb3ec5`](https://github.com/nodejs/node/commit/1c5ffb3ec5)] - **(SEMVER-MINOR)** **lib**: add escapeCodeTimeout as an option to createInterface (Raoof) [#19780](https://github.com/nodejs/node/pull/19780)
- [[`1cda41b7da`](https://github.com/nodejs/node/commit/1cda41b7da)] - **lib**: migrate from process.binding('config') to getOptions() (Vladimir Ilic) [#23588](https://github.com/nodejs/node/pull/23588)
- [[`22cd53791a`](https://github.com/nodejs/node/commit/22cd53791a)] - **lib**: trigger uncaught exception handler for microtasks (Gus Caplan) [#23794](https://github.com/nodejs/node/pull/23794)
- [[`97496f0fd9`](https://github.com/nodejs/node/commit/97496f0fd9)] - **n-api**: make per-`Context`-ness of `napi_env` explicit (Anna Henningsen) [#23689](https://github.com/nodejs/node/pull/23689)
- [[`3e512f1897`](https://github.com/nodejs/node/commit/3e512f1897)] - **os**: fix memory leak in `userInfo()` (Anna Henningsen) [#23893](https://github.com/nodejs/node/pull/23893)
- [[`02f13abde3`](https://github.com/nodejs/node/commit/02f13abde3)] - **repl**: support top-level for-await-of (Shelley Vohr) [#23841](https://github.com/nodejs/node/pull/23841)
- [[`86cf01404c`](https://github.com/nodejs/node/commit/86cf01404c)] - **repl**: migrate from process.binding('config') to getOptions() (Jose Bucio) [#23684](https://github.com/nodejs/node/pull/23684)
- [[`4a79b2568f`](https://github.com/nodejs/node/commit/4a79b2568f)] - **src**: improve StreamBase write throughput (Anna Henningsen) [#23843](https://github.com/nodejs/node/pull/23843)
- [[`dcaf72311b`](https://github.com/nodejs/node/commit/dcaf72311b)] - **src**: minor refactor to node_errors.h (Anna Henningsen) [#23879](https://github.com/nodejs/node/pull/23879)
- [[`fef17b716d`](https://github.com/nodejs/node/commit/fef17b716d)] - **src**: avoid extra `Persistent` in `DefaultTriggerAsyncIdScope` (Anna Henningsen) [#23844](https://github.com/nodejs/node/pull/23844)
- [[`ce106df728`](https://github.com/nodejs/node/commit/ce106df728)] - **src**: use maybe version v8::Function::Call (Ouyang Yadong) [#23826](https://github.com/nodejs/node/pull/23826)
- [[`1bdbf8765d`](https://github.com/nodejs/node/commit/1bdbf8765d)] - **src**: reduce duplication in tcp_wrap Connect (Daniel Bevenius) [#23753](https://github.com/nodejs/node/pull/23753)
- [[`9fbe91a061`](https://github.com/nodejs/node/commit/9fbe91a061)] - **src**: refactor deprecated v8::String::NewFromTwoByte call (Romain Lanz) [#23803](https://github.com/nodejs/node/pull/23803)
- [[`48ed81fad2`](https://github.com/nodejs/node/commit/48ed81fad2)] - **src**: improve StreamBase read throughput (Anna Henningsen) [#23797](https://github.com/nodejs/node/pull/23797)
- [[`a6fe2caaae`](https://github.com/nodejs/node/commit/a6fe2caaae)] - **src**: simplify `TimerFunctionCall()` in `node_perf.cc` (Anna Henningsen) [#23782](https://github.com/nodejs/node/pull/23782)
- [[`30be5cbdb0`](https://github.com/nodejs/node/commit/30be5cbdb0)] - **src**: memory management using smart pointer (Uttam Pawar) [#23628](https://github.com/nodejs/node/pull/23628)
- [[`df05ddfd72`](https://github.com/nodejs/node/commit/df05ddfd72)] - **src**: refactor deprecated v8::Function::Call call (Romain Lanz) [#23804](https://github.com/nodejs/node/pull/23804)
- [[`7bbc072529`](https://github.com/nodejs/node/commit/7bbc072529)] - **stream**: do not error async iterators on destroy(null) (Matteo Collina) [#23901](https://github.com/nodejs/node/pull/23901)
- [[`5ce3b6d7a4`](https://github.com/nodejs/node/commit/5ce3b6d7a4)] - **stream**: ended streams should resolve the async iteration (Matteo Collina) [#23901](https://github.com/nodejs/node/pull/23901)
- [[`aaddf97d9b`](https://github.com/nodejs/node/commit/aaddf97d9b)] - **stream**: async iteration should work with destroyed stream (Matteo Collina) [#23785](https://github.com/nodejs/node/pull/23785)
- [[`871e32789a`](https://github.com/nodejs/node/commit/871e32789a)] - **test**: fixed error message in test-buffer-read (Arvind Pandey) [#23957](https://github.com/nodejs/node/pull/23957)
- [[`ed10a91e83`](https://github.com/nodejs/node/commit/ed10a91e83)] - **test**: add test-benchmark-http2 (Rich Trott) [#23863](https://github.com/nodejs/node/pull/23863)
- [[`22bbece323`](https://github.com/nodejs/node/commit/22bbece323)] - **test**: fix regression when compiled with FIPS (Adam Majer) [#23871](https://github.com/nodejs/node/pull/23871)
- [[`22caa26c69`](https://github.com/nodejs/node/commit/22caa26c69)] - **test**: fix strictEqual() argument order (Loic) [#23829](https://github.com/nodejs/node/pull/23829)
- [[`572ea60378`](https://github.com/nodejs/node/commit/572ea60378)] - **test**: verify `performance.timerify()` works w/ non-Node Contexts (Anna Henningsen) [#23784](https://github.com/nodejs/node/pull/23784)
- [[`0f00ac9c7a`](https://github.com/nodejs/node/commit/0f00ac9c7a)] - **test**: mark test-vm-timeout-\* known issue tests flaky (James M Snell) [#23743](https://github.com/nodejs/node/pull/23743)
- [[`a80452a1ab`](https://github.com/nodejs/node/commit/a80452a1ab)] - **test**: add test-benchmark-napi (Emily Marigold Klassen) [#23585](https://github.com/nodejs/node/pull/23585)
- [[`086ee5e57f`](https://github.com/nodejs/node/commit/086ee5e57f)] - **test**: increase coverage of internal/stream/end-of-stream (Tyler Vann-Campbell) [#23751](https://github.com/nodejs/node/pull/23751)
- [[`ee8fa528e2`](https://github.com/nodejs/node/commit/ee8fa528e2)] - **test**: fix strictEqual() arguments order (Nolan Rigo) [#23800](https://github.com/nodejs/node/pull/23800)
- [[`83ddd3e7d0`](https://github.com/nodejs/node/commit/83ddd3e7d0)] - **test**: fix flaky test (cjihrig) [#23811](https://github.com/nodejs/node/pull/23811)
- [[`1521d8991d`](https://github.com/nodejs/node/commit/1521d8991d)] - **test**: fix invalid modulesLength for DSA keygen (Adam Majer) [#23732](https://github.com/nodejs/node/pull/23732)
- [[`dfecf85ded`](https://github.com/nodejs/node/commit/dfecf85ded)] - **test**: fix test-require-symlink on Windows (Bartosz Sosnowski) [#23691](https://github.com/nodejs/node/pull/23691)
- [[`ddd9ccf1d8`](https://github.com/nodejs/node/commit/ddd9ccf1d8)] - **test**: fix strictEqual() argument order (Romain Lanz) [#23768](https://github.com/nodejs/node/pull/23768)
- [[`a666d3ea24`](https://github.com/nodejs/node/commit/a666d3ea24)] - **test**: fix strictEqual() arguments order (Thomas GENTILHOMME) [#23771](https://github.com/nodejs/node/pull/23771)
- [[`fa1373fc74`](https://github.com/nodejs/node/commit/fa1373fc74)] - **test**: fix assertion arguments order (Elian Gutierrez) [#23787](https://github.com/nodejs/node/pull/23787)
- [[`167e99b9a1`](https://github.com/nodejs/node/commit/167e99b9a1)] - **timers**: fix priority queue removeAt fn (Anatoli Papirovski) [#23870](https://github.com/nodejs/node/pull/23870)
- [[`09f25af16f`](https://github.com/nodejs/node/commit/09f25af16f)] - **tls**: throw if protocol too long (Andre Jodat-Danbrani) [#23606](https://github.com/nodejs/node/pull/23606)
- [[`45a20a8d78`](https://github.com/nodejs/node/commit/45a20a8d78)] - **tools**: update ESLint to 5.8.0 (cjihrig) [#23904](https://github.com/nodejs/node/pull/23904)
- [[`c20eb4f2bd`](https://github.com/nodejs/node/commit/c20eb4f2bd)] - **(SEMVER-MINOR)** **tools, icu**: actually failover if there are multiple URLs (Steven R. Loomis) [#23715](https://github.com/nodejs/node/pull/23715)
- [[`b07cb4810c`](https://github.com/nodejs/node/commit/b07cb4810c)] - **zlib**: do not leak on destroy (Mathias Buus) [#23734](https://github.com/nodejs/node/pull/23734)

Windows 32-bit Installer: https://nodejs.org/dist/v11.1.0/node-v11.1.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v11.1.0/node-v11.1.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v11.1.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v11.1.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v11.1.0/node-v11.1.0.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v11.1.0/node-v11.1.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v11.1.0/node-v11.1.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v11.1.0/node-v11.1.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v11.1.0/node-v11.1.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v11.1.0/node-v11.1.0-aix-ppc64.tar.gz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v11.1.0/node-v11.1.0-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v11.1.0/node-v11.1.0-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v11.1.0/node-v11.1.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v11.1.0/node-v11.1.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v11.1.0/node-v11.1.0.tar.gz \
Other release files: https://nodejs.org/dist/v11.1.0/ \
Documentation: https://nodejs.org/docs/v11.1.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

71e08bec907dfb5f32d71c88e0b5a449984eefba64dbf89c6e31ae3a2e339ed1  node-v11.1.0-aix-ppc64.tar.gz
5d6b84d2b0fd6afee07c371bc815a9e4b6671b85bedcb38815310bd0f884d3c8  node-v11.1.0-darwin-x64.tar.gz
d05c965afde1756960f7c5a7a0584693c7fa676718d29299cca174837870398a  node-v11.1.0-darwin-x64.tar.xz
37fb17cc0cf2214924c59ed38a0fdfe9285682ab65194e342c5b6cc9ecd6ff5e  node-v11.1.0-headers.tar.gz
149b0de625ce3cc71ef99ca463607b36543cbe53a156f8254d61af9829fe7625  node-v11.1.0-headers.tar.xz
4bdada732428603e215ca3b6a4e06814706bc48a2681ec48446319312bff2489  node-v11.1.0-linux-arm64.tar.gz
cbba27e1c90701fbb9db66cc2c6cc3049aaf08adb16cabd0cad970b74cdaf6d3  node-v11.1.0-linux-arm64.tar.xz
2621c0423fd846982958504f68b4d93399294e6dd889179d3c19bf1d719eba2a  node-v11.1.0-linux-armv6l.tar.gz
4888321722b5de2d21ed2af719b791ac69067118e255372549c0034b69ac10be  node-v11.1.0-linux-armv6l.tar.xz
cca050517ba05f800cb501679bc8316c2e2c688b14735b34bccfe5994a12c414  node-v11.1.0-linux-armv7l.tar.gz
9eca717516f4bf7b16ae89cf27855d6fb97e820a70d0e1f71b689873ff33d25f  node-v11.1.0-linux-armv7l.tar.xz
718e8e56b0cf0e8881e6e5c62124c2fd1950c2e9a49d6b542cf28348d533af44  node-v11.1.0-linux-ppc64le.tar.gz
f6731d09047955546cb439caf78a40003c24651bc7cd8030ff42ae118628ae5e  node-v11.1.0-linux-ppc64le.tar.xz
a47d967ccce43fe05d2e5c43a4e79611f064c22d2974eac8d420e0a06a3f223e  node-v11.1.0-linux-s390x.tar.gz
e17d9551edbed725ea405d74bf442b020e0ed43e1c4f4752f9699afd2e8d4a40  node-v11.1.0-linux-s390x.tar.xz
52289a646a27511f5808290357798c7ebd4b5132a8fc3bf7d5bf53183b89c668  node-v11.1.0-linux-x64.tar.gz
c70419674d932452017556080264de2b6d1105c112647dd1dd495b739456dd91  node-v11.1.0-linux-x64.tar.xz
f1dc54855509af31e7c003fbb9434acd87d885a87e3828ea94aac39671d3f82e  node-v11.1.0.pkg
1a2186578fbbf13e006e3aac7708ad4b084598364904c08d95538ea943a3c414  node-v11.1.0-sunos-x64.tar.gz
5dda47b8a7d0f83950b36903e024722bf9756c5842aff43686576735c943e664  node-v11.1.0-sunos-x64.tar.xz
3f53b5ac25b2d36ad538267083c0e603d9236867a936c22a9116d95fa10c60d5  node-v11.1.0.tar.gz
50279fb2cfea1eefc7d1a3ce5c4fecdd16131a4397867d1af839d47cda556d0a  node-v11.1.0.tar.xz
a20b54297d9b9341e0af2aec82a9df5067e1dcb92fc5c6ecd0bdcec28ce4aa66  node-v11.1.0-win-x64.7z
985e4edc758cb5f77f85cddda0155616b92f163b8d3842c542b1c8a395068418  node-v11.1.0-win-x64.zip
1617d3ad4635f65ef578a6f2a3a44ed2826dd45df57e510425df858f0a2f3f4a  node-v11.1.0-win-x86.7z
a8a7be0e7d281f754301cf4a47c66a2984dbbf2fbf3111d0da5ea43920bc890d  node-v11.1.0-win-x86.zip
c710576f38196e952458ede8ee4530f999808b29c969c080b72988c2af782827  node-v11.1.0-x64.msi
dd7b667fef034618b7e67582ee55f3157ad4b5254aa7a42e6f289e98ef688595  node-v11.1.0-x86.msi
ff3f63b485122fe1d9f1ceab65dc72e489c237f6a749c9e4f3a87f0fa6dd31cc  win-x64/node.exe
b4179ea3b39a574bac11796354672edca83c7b570757711617293bc951f50318  win-x64/node.lib
884030e01f678c1b61eabffab5840d1464b3c5d8a9b8809c22e062486cf182ff  win-x64/node_pdb.7z
197f3d0b74fd6a7c7cf545427c894b3a62901dc35975cda58d9a1550c84ebb1c  win-x64/node_pdb.zip
121237886f019821676c0971adbcbe95834eff327cf6f885a486e2e3767f2c45  win-x86/node.exe
097ce4f6f30df47d71dbaeaabe5582bfa856568be0e99d700811605d1d95c495  win-x86/node.lib
9377c2d2b011565cc0c5bc3a8b9dbc0e72327605040621d302d10c1cf6533c17  win-x86/node_pdb.7z
2b382f0c3ed1edbc13f9505de2a896d09ef490d4fe54343792825820fe432bec  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEj8yhP+8dDC6RAI4Jdw96mlrhVgAFAlvcQ90ACgkQdw96mlrh
VgB+nA/+NMRUkojMdyZjUWB5wMHAEnHBSG2FsrpPQOu5onyw8hADft3RMHZ8SlWE
ynP4REtfbkQSqvZi0n8UvP8TdrBvmM326P0HFfnGw6vxZ+F7GbwzaCNgE0Onj/Ir
EULYASYbtGksY58+wh5xcLdAC+TzUccoF+F0wpn2JCcdPw7oLKAt0w/Iprc7Za3W
k7PmSqGGLkC/+vzP5cWHJDhQ9HY1/1HtW52aXZ84CIV7f5fiVykHfTprS1QIQ9bp
voP1jzf2944PJ1qoWnnFV0Kt/Z1BHk7Z0X4W4s8A3e/hQaVtEGhUYkqFTmwwv8eu
xvdK3ljJ9CMQOshkhWoMgENL9tICN/NcYGoP1RQQEygLHUvmhChMQ6r+ol3LYrKJ
fn18QzUgXhraPGNGde0wbJlxARSM31Emy5VxGxY7h+RyC8+kG62LV455oTp89dte
0pbM5eMhV0MMBPAWsSmR5I4drzw858DffrCCGFDpbupvnLs0cirRrnYlNuEGWfc/
fDjkbP7GSzGrp9EBsXX715rQpqplFGdxRZkOuSAl4hVZ6pOr4cstbdbfu4e9RWcZ
o2LxXwKXDZVi1x4uV9YOGdqOGCpmnIMJSpXKrI//gt7z43hC3fGeGXlhJIYdOK+/
xASAUeKMokLO/uvebdoPJhN8v6rLSMncANVAT6Ag5m/05I0Cfe4=
=ZRP6
-----END PGP SIGNATURE-----

```
