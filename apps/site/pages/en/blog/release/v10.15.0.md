---
date: '2018-12-26T16:30:38.591Z'
category: release
title: Node v10.15.0 (LTS)
layout: blog-post
author: <PERSON><PERSON>
---

The 10.14.0 security release introduced some unexpected breakages on the 10.x release line.
This is a special release to fix a regression in the HTTP binary upgrade response body and add
a missing CLI flag to adjust the max header size of the http parser.

### Notable Changes

- **cli**:
  - add --max-http-header-size flag (cjihrig) [#24811](https://github.com/nodejs/node/pull/24811)
- **http**:
  - add maxHeaderSize property (cjihrig) [#24860](https://github.com/nodejs/node/pull/24860)

### Commits

- [[`9b2ffc81c0`](https://github.com/nodejs/node/commit/9b2ffc81c0)] - **(SEMVER-MINOR)** **cli**: add --max-http-header-size flag (cjihrig) [#24811](https://github.com/nodejs/node/pull/24811)
- [[`6183c7107d`](https://github.com/nodejs/node/commit/6183c7107d)] - **(SEMVER-MINOR)** **deps**: cherry-pick http_parser_set_max_header_size (cjihrig) [#24811](https://github.com/nodejs/node/pull/24811)
- [[`e669733595`](https://github.com/nodejs/node/commit/e669733595)] - **doc**: describe current HTTP header size limit (Sam Roberts) [#24700](https://github.com/nodejs/node/pull/24700)
- [[`b6d3afb257`](https://github.com/nodejs/node/commit/b6d3afb257)] - **(SEMVER-MINOR)** **http**: add maxHeaderSize property (cjihrig) [#24860](https://github.com/nodejs/node/pull/24860)
- [[`1aea1e3634`](https://github.com/nodejs/node/commit/1aea1e3634)] - **http**: fix regression of binary upgrade response body (Matteo Collina) [#25039](https://github.com/nodejs/node/pull/25039)
- [[`a57aed144a`](https://github.com/nodejs/node/commit/a57aed144a)] - **(SEMVER-MINOR)** **src**: add kUInteger parsing (Matteo Collina) [#24811](https://github.com/nodejs/node/pull/24811)
- [[`527407c49f`](https://github.com/nodejs/node/commit/527407c49f)] - **src**: cache the result of GetOptions() in JS land (Joyee Cheung) [#24091](https://github.com/nodejs/node/pull/24091)
- [[`728bc631e5`](https://github.com/nodejs/node/commit/728bc631e5)] - **test**: fix expectation in test-bootstrap-modules (Ali Ijaz Sheikh) [#25112](https://github.com/nodejs/node/pull/25112)
- [[`3e14212f0e`](https://github.com/nodejs/node/commit/3e14212f0e)] - **test**: remove magic numbers in test-gc-http-client-onerror (Rich Trott) [#24943](https://github.com/nodejs/node/pull/24943)

Windows 32-bit Installer: https://nodejs.org/dist/v10.15.0/node-v10.15.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v10.15.0/node-v10.15.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v10.15.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v10.15.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v10.15.0/node-v10.15.0.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v10.15.0/node-v10.15.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v10.15.0/node-v10.15.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v10.15.0/node-v10.15.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v10.15.0/node-v10.15.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v10.15.0/node-v10.15.0-aix-ppc64.tar.gz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v10.15.0/node-v10.15.0-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v10.15.0/node-v10.15.0-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v10.15.0/node-v10.15.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v10.15.0/node-v10.15.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v10.15.0/node-v10.15.0.tar.gz \
Other release files: https://nodejs.org/dist/v10.15.0/ \
Documentation: https://nodejs.org/docs/v10.15.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

5a0eac2db2dc6114a9095190a0e9e835e210d5cdf5c0042607bb50331c32313f  node-v10.15.0-aix-ppc64.tar.gz
353402461c898c569658d0a963790476f4d9828cc6c9286d81617ee8afcba4e8  node-v10.15.0-darwin-x64.tar.gz
90c991ad51528705b47312fb63f52cd770c66757b02b782168e4bc6c5165b8be  node-v10.15.0-darwin-x64.tar.xz
36a5b15184bec31a83d7abd4569b6808401e55cd53ed27c09e4c7b9d90048e6b  node-v10.15.0-headers.tar.gz
339167b1d4d5b2fdb3263cd6512d43adead92e2402dfe69ad4c1a48f66172a33  node-v10.15.0-headers.tar.xz
69a86c71df32320dc8dfccd1aca124c73dc2b274c7ce50104dad733a06dc26f3  node-v10.15.0-linux-arm64.tar.gz
77aa4a02c5471b6eb7ba935cbc6829889a27115353cff7226a208c08b654e972  node-v10.15.0-linux-arm64.tar.xz
701ea80df70f86cb1980b1ccc8f38fe9d55e4155acc13a73717c9ea4aeed466c  node-v10.15.0-linux-armv6l.tar.gz
6e3b97d35ab51fe7b236f78c2fff8e0d0c77587a5a297b6469c7b8f6e68f8314  node-v10.15.0-linux-armv6l.tar.xz
81a248baa989667d14a0868adf87e5048a53518e9aed169131194e88b39c20aa  node-v10.15.0-linux-armv7l.tar.gz
a1f3fc1fade97ff44914a033a176118b8323575984c5897c464ab1bf03ce6be0  node-v10.15.0-linux-armv7l.tar.xz
fc4ce0770bfabbe6be61b52bf6ed48c4ffd10f4fa8a698e4bf22594525a3116a  node-v10.15.0-linux-ppc64le.tar.gz
ebeae2719bba8fca47bb81e543bf5bd6391b2813db289b1e1af5ae6e90eecdfd  node-v10.15.0-linux-ppc64le.tar.xz
80ae06a0f2fc936d663043b5b709ec050016ab846aa29faf9718c6fe3f20cc14  node-v10.15.0-linux-s390x.tar.gz
934263cd756983f734964ec3b5923ac4e257221a029dee95e2bb851c459338e3  node-v10.15.0-linux-s390x.tar.xz
f0b4ff9a74cbc0106bbf3ee7715f970101ac5b1bbe814404d7a0673d1da9f674  node-v10.15.0-linux-x64.tar.gz
4ee8503c1133797777880ebf75dcf6ae3f9b894c66fd2d5da507e407064c13b5  node-v10.15.0-linux-x64.tar.xz
b80f5b3136ed993f31c28cd37b54fd8528ff2620bc2423ea440aa41f61a57412  node-v10.15.0.pkg
77254ecb8576cf54af53df72842cbdb756b2b34b23309194649be7891ff9bd1f  node-v10.15.0-sunos-x64.tar.gz
10c5605ea949118224e570a5723c4c97473a1cd5b052290efd2be71485649bfa  node-v10.15.0-sunos-x64.tar.xz
dbe467e3dabb6854fcb0cd96e04082268cb1e313ce97a4b7100b2ed152b0a0ab  node-v10.15.0.tar.gz
797ab34c74b83b21b7d6ea261b5ca235d34c61a7da5aebb32459a963097ede3e  node-v10.15.0.tar.xz
ac2115dcd5ee53c0c8b10521368aec540370e75cd4f7ff1544a44791c7d3362d  node-v10.15.0-win-x64.7z
c1dbc9372ad789cd21727cb5f63b4a44ed3eae216763959cff8e68e68c6fcfe1  node-v10.15.0-win-x64.zip
ba089a8120c63b322c888efd3d959c9b9ce2b3b954a39cd996531c4e4982be43  node-v10.15.0-win-x86.7z
3b7076ea74551f05747bb2db1803dc0406ce543f3744c6896359c619c7e5296e  node-v10.15.0-win-x86.zip
1cf90ea486607a1e4e3191e4880d4e6a256168d800fe33a6e46680149b88e3ed  node-v10.15.0-x64.msi
9b4b01c5e8181caa2b48ded5b481773b504b54b6145754a09aae8de948e46053  node-v10.15.0-x86.msi
7884185dcbadbb14a81a56caea569da87f21563dd60b8770763bb63de236b0a1  win-x64/node.exe
48dcf15c1eb32836e3124a5d4bfc8489e2e84c22b49576aa08a405b224e70fb6  win-x64/node.lib
be6bebbe07cf6f5ac840d889283406f0ec834b3076a7742bf79942910f78138d  win-x64/node_pdb.7z
eeff351d2f79d30b3da9afc1026a28b589763e20b1a46294077549016389943a  win-x64/node_pdb.zip
71bfd4449aa4c4505a6bbc23b1a9870bad4edaee5eae99a4d22fd0cad704fdae  win-x86/node.exe
ede58d70f22373d7abfd71582ad3c93ec8f6a8b59efd7aa8e207d7cb30481010  win-x86/node.lib
f3c26eeb7921b2db8ad1eda410856a79d1de9e5cf69bdd30b3e06554c4ce42a1  win-x86/node_pdb.7z
bca112bc708f34cadc936fe71d50665175adf4436d382923888ab9c2c0ea8fc9  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQEzBAEBCAAdFiEEDv/hvO/ZyE49CYFSkzsB9AtcqUYFAlwjq4sACgkQkzsB9Atc
qUbWtAf8CkdNBnT2R5mSbLwkxAr8mZfK0HBjI8zSa9G2tUhIr9MMfWibABfKWDpd
00QMobKl8uEswbNxwNm1Bygjsa13w2p33IT3HkP3K0VpGcZNQUy0cGrQImFTKAh2
OoKsEOHsJDsVuCcXoi4rDRt66dQDxfmDhxvCfuHRsixjL8RwjpoFYlq8j0aCxlBU
4zEn3aS1naUP9QIiLgAZl9GcLqd4xcdm0dpRTMCxk4J6nKI+byg8eesjMIv/3q7y
wJQfS8VKtxcBV8EKXPOsqzE2Oj9b4RwkPkV3nFjGFAmfRoYX1o/hTI4QQK3zi4vK
Z3Pc9Q/WlqxYpe2+yWh90rowQNrQrA==
=+WLA
-----END PGP SIGNATURE-----

```
