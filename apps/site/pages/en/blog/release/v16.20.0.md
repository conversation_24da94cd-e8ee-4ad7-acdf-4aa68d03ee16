---
date: '2023-03-29T18:11:26.675Z'
category: release
title: Node v16.20.0 (LTS)
layout: blog-post
author: <PERSON>
---

### Notable Changes

- **deps:**
  - update undici to 5.20.0 (Node.js GitHub Bot) [#46711](https://github.com/nodejs/node/pull/46711)
  - update c-ares to 1.19.0 (<PERSON><PERSON><PERSON>) [#46415](https://github.com/nodejs/node/pull/46415)
  - upgrade npm to 8.19.4 (npm team) [#46677](https://github.com/nodejs/node/pull/46677)
  - update corepack to 0.17.0 (Node.js GitHub Bot) [#46842](https://github.com/nodejs/node/pull/46842)
- **(SEMVER-MINOR)** **src**: add support for externally shared js builtins (<PERSON>) [#44376](https://github.com/nodejs/node/pull/44376)

### Commits

- \[[`de6dd67790`](https://github.com/nodejs/node/commit/de6dd67790)] - **crypto**: avoid hang when no algorithm available (Richard Lau) [#46237](https://github.com/nodejs/node/pull/46237)
- \[[`4617512788`](https://github.com/nodejs/node/commit/4617512788)] - **crypto**: ensure auth tag set for chacha20-poly1305 (Ben Noordhuis) [#46185](https://github.com/nodejs/node/pull/46185)
- \[[`24972164fc`](https://github.com/nodejs/node/commit/24972164fc)] - **deps**: update undici to 5.20.0 (Node.js GitHub Bot) [#46711](https://github.com/nodejs/node/pull/46711)
- \[[`85f88c6a8d`](https://github.com/nodejs/node/commit/85f88c6a8d)] - **deps**: V8: cherry-pick 90be99fab31c (Michaël Zasso) [#46646](https://github.com/nodejs/node/pull/46646)
- \[[`b4ebe6d47b`](https://github.com/nodejs/node/commit/b4ebe6d47b)] - **deps**: update c-ares to 1.19.0 (Michaël Zasso) [#46415](https://github.com/nodejs/node/pull/46415)
- \[[`56cbc7fdda`](https://github.com/nodejs/node/commit/56cbc7fdda)] - **deps**: V8: cherry-pick c2792e58035f (Jiawen Geng) [#44961](https://github.com/nodejs/node/pull/44961)
- \[[`7af9bdb31e`](https://github.com/nodejs/node/commit/7af9bdb31e)] - **deps**: upgrade npm to 8.19.4 (npm team) [#46677](https://github.com/nodejs/node/pull/46677)
- \[[`962a7471b5`](https://github.com/nodejs/node/commit/962a7471b5)] - **deps**: update corepack to 0.17.0 (Node.js GitHub Bot) [#46842](https://github.com/nodejs/node/pull/46842)
- \[[`748bc96e35`](https://github.com/nodejs/node/commit/748bc96e35)] - **deps**: update corepack to 0.16.0 (Node.js GitHub Bot) [#46710](https://github.com/nodejs/node/pull/46710)
- \[[`a467782499`](https://github.com/nodejs/node/commit/a467782499)] - **deps**: update corepack to 0.15.3 (Node.js GitHub Bot) [#46037](https://github.com/nodejs/node/pull/46037)
- \[[`1913b6763d`](https://github.com/nodejs/node/commit/1913b6763d)] - **deps**: update corepack to 0.15.2 (Node.js GitHub Bot) [#45635](https://github.com/nodejs/node/pull/45635)
- \[[`809371a15f`](https://github.com/nodejs/node/commit/809371a15f)] - **module**: require.resolve.paths returns null with node schema (MURAKAMI Masahiko) [#45147](https://github.com/nodejs/node/pull/45147)
- \[[`086bb2f8d4`](https://github.com/nodejs/node/commit/086bb2f8d4)] - _**Revert**_ "**src**: let http2 streams end after session close" (Rich Trott) [#46721](https://github.com/nodejs/node/pull/46721)
- \[[`6a01d39120`](https://github.com/nodejs/node/commit/6a01d39120)] - **(SEMVER-MINOR)** **src**: add support for externally shared js builtins (Michael Dawson) [#44376](https://github.com/nodejs/node/pull/44376)
- \[[`d081032a60`](https://github.com/nodejs/node/commit/d081032a60)] - **test**: fix test-net-connect-reset-until-connected (Vita Batrla) [#46781](https://github.com/nodejs/node/pull/46781)
- \[[`efe1be47ec`](https://github.com/nodejs/node/commit/efe1be47ec)] - **test**: skip test depending on `overlapped-checker` when not available (Antoine du Hamel) [#45015](https://github.com/nodejs/node/pull/45015)
- \[[`fc47d58abe`](https://github.com/nodejs/node/commit/fc47d58abe)] - **test**: remove cjs loader from stack traces (Geoffrey Booth) [#44197](https://github.com/nodejs/node/pull/44197)
- \[[`cf76d0790d`](https://github.com/nodejs/node/commit/cf76d0790d)] - **test**: fix WPT title when no META title is present (Filip Skokan) [#46804](https://github.com/nodejs/node/pull/46804)
- \[[`0d1485b924`](https://github.com/nodejs/node/commit/0d1485b924)] - **test**: fix default WPT titles (Filip Skokan) [#46778](https://github.com/nodejs/node/pull/46778)
- \[[`088e9cde3d`](https://github.com/nodejs/node/commit/088e9cde3d)] - **test**: add WPTRunner support for variants and generating WPT reports (Filip Skokan) [#46498](https://github.com/nodejs/node/pull/46498)
- \[[`908c4dff44`](https://github.com/nodejs/node/commit/908c4dff44)] - **test**: mark test-crypto-key-objects flaky on Linux (Richard Lau) [#46684](https://github.com/nodejs/node/pull/46684)
- \[[`768e56227e`](https://github.com/nodejs/node/commit/768e56227e)] - **tools**: make `utils.SearchFiles` deterministic (Bruno Pitrus) [#44496](https://github.com/nodejs/node/pull/44496)

Windows 32-bit Installer: https://nodejs.org/dist/v16.20.0/node-v16.20.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v16.20.0/node-v16.20.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v16.20.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v16.20.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v16.20.0/node-v16.20.0.pkg \
macOS Apple Silicon 64-bit Binary: https://nodejs.org/dist/v16.20.0/node-v16.20.0-darwin-arm64.tar.gz \
macOS Intel 64-bit Binary: https://nodejs.org/dist/v16.20.0/node-v16.20.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v16.20.0/node-v16.20.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v16.20.0/node-v16.20.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v16.20.0/node-v16.20.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v16.20.0/node-v16.20.0-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v16.20.0/node-v16.20.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v16.20.0/node-v16.20.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v16.20.0/node-v16.20.0.tar.gz \
Other release files: https://nodejs.org/dist/v16.20.0/ \
Documentation: https://nodejs.org/docs/v16.20.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

87f579933a45a4ad77dffd60e6f78cfb2c3f2d3a762824c90d75fea3062352a6  node-v16.20.0-aix-ppc64.tar.gz
15d0857009f13e85057010b605e57b418318fdf422b5f9dd7e0ef32115da9c10  node-v16.20.0-darwin-arm64.tar.gz
302afc30606e3d12c5a0e6115765769d5e63fc850a9d4edf1fda36ce3d762049  node-v16.20.0-darwin-arm64.tar.xz
263d5b4871972028e204087fc8a67e21d8a0e2a420d1247375089ec8fd12759e  node-v16.20.0-darwin-x64.tar.gz
751a3d5ae55ffa70624ffb0128e7df5a0a09a70867345e2b1b9473b63d722ca9  node-v16.20.0-darwin-x64.tar.xz
4ab673791303ba8284c6230a401b4e6054acfbbde05f4b4937769692cb3e80ed  node-v16.20.0-headers.tar.gz
860fbb4631479856a619fd887cf340322e2e51c83db25c244db0a45cc1c7289d  node-v16.20.0-headers.tar.xz
58ea2f702936832fcf7d9cf1e9249bb7d9769185f8ad2ece05a70a7f61dbf879  node-v16.20.0-linux-arm64.tar.gz
aca4794ca60f2e17689a700eeaa95d7adcd5fd01cdd2e4f9d596ac41b600b796  node-v16.20.0-linux-arm64.tar.xz
01f71cca760c2e3ee0178c29dc7352a579f4a006ba8d628744dcd82b126b1fad  node-v16.20.0-linux-armv7l.tar.gz
5b79addab37bbdc31b442f8e648efad0b5d3aa5f3e0d682f23f4db483408eb2a  node-v16.20.0-linux-armv7l.tar.xz
dc51e9f9aa825e4d2793c17b526a1c16c90a7d177dfff3f7188a035e67069c58  node-v16.20.0-linux-ppc64le.tar.gz
b439a8f874e8c2fad287ba75a3229407551f5ff9eb41e45e43e9bf67f184f618  node-v16.20.0-linux-ppc64le.tar.xz
7699426ea06b3d64c663b37d6f9a0d8442436c2daca9f26f34fbccc2f7aedc8d  node-v16.20.0-linux-s390x.tar.gz
e71415a21ad5091b6dcbd550d10c8d9a57cd21592617950e5ee0ffe983098d8a  node-v16.20.0-linux-s390x.tar.xz
7abc0e558fa3b3c4cc0fd3c7fa5dbe61500ba7213f5e87ed560c65a733c6a5c4  node-v16.20.0-linux-x64.tar.gz
dff21020b555cc165a1ac36da7d4f6c810b35409c94e00afc51d5d370aae47ae  node-v16.20.0-linux-x64.tar.xz
2f73889db5375aeb470eb61343c2d84e336b7b10bf93134bbb54f8ba3296a458  node-v16.20.0.pkg
58c10af148bce26f17417f58ab8f71b91003cae87df17cc48e9f09c94edd908f  node-v16.20.0.tar.gz
e0990f992234e40a51fe11f92c3816c93a77e1b081145d3dd762cd1026345349  node-v16.20.0.tar.xz
f181d59a41b5ee0deef374d100edaba48f681cdc49eed7bece7853238730cd2c  node-v16.20.0-win-x64.7z
1ad6e19300860bafdd9f18fd4ac65c32e4d396c56591c861c0d1bdcc55fe9c19  node-v16.20.0-win-x64.zip
567bbda7f49deee9618e7b90c6dd07dcd77ac6ab6364ead482b1a8f85b5dc281  node-v16.20.0-win-x86.7z
72980a40544b7d5e37907b3fbe26804d1f239130d6c85c4354394c1f91f9f403  node-v16.20.0-win-x86.zip
f76f7ce6520eda1baacb42283db9533dba573771a1009278bd893b8f12eb2e11  node-v16.20.0-x64.msi
7824c87b68c050c8a3feb88588f009f76f718f4a709550e527209eab1665582c  node-v16.20.0-x86.msi
a49f9e852f80cfc00ddcd75dcfccbe2987672f78dcbf7f5454a832479c77f448  win-x64/node.exe
e80291db5962cc9f7ddada615e685d6af3d33f7e7a688775807369de626bd6ff  win-x64/node.lib
f44f8e78fef5fd7e4c2b25de93df4000946d9869b1ec53a1f66c0699fe97a6c1  win-x64/node_pdb.7z
21fbfc832246c2e9d8c1d4a9d725e2e5e6fb8cee1622f94928a3b6f30de2b7c4  win-x64/node_pdb.zip
029343acebfbe9b9f858da0e68bc532878a8844a7f59e7451668059f72e49f0f  win-x86/node.exe
8876bbef0392631001bdc034e6256d607972090ded068cb84f222752d3b828cf  win-x86/node.lib
75b6cf61c344d4f351bcdaddc2d4ae6a37b2ea33c5cae18f7ec0fb48e9a6025d  win-x86/node_pdb.7z
184ab2918943995eda76ed758116abac28d2092dc0ca7a3b735cac4f43212ccb  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQEzBAEBCAAdFiEETtd49TnjY0x3nIfG1wYoSKGrAFwFAmQkfscACgkQ1wYoSKGr
AFxjIAf6ArRusKcxdjsYWy7kw1t73Vk/e3/jM3pB681sF5zrZHK20SYh8WA0vnZ1
JQpFvMxZ6Wk647/cFRZnH6qNiDUEcV4g2awjUUNxQOBEd2QkAT1NMLIPDctp8OTt
VIk4WHReikAa1OIsHkTmXequHNUa7982JDS75y0HLt5xXbVmzHWv8QjFv/7EylCs
2bF3DVelP1PLSYDDlyp4e12elsnkqj9vk0fSaqulzgiDt2UBDPBuCtQlXneII9HA
jFFwv0v49fiyfVxXWXbkSUCRL8lBVo29CXGBJgeu3bpj8p2zQSaS2HBzGqYtM4Fd
rET8azjRDUzkIrZsvrrJJ5sOBopecA==
=MOTM
-----END PGP SIGNATURE-----

```
