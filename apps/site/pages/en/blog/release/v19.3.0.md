---
date: '2022-12-14T13:58:44.999Z'
category: release
title: Node v19.3.0 (Current)
layout: blog-post
author: <PERSON><PERSON><PERSON>
---

### Notable Changes

#### Updated npm to 9.2.0

Based on the [list of guidelines we've established on integrating `npm` and `node`](https://github.com/npm/cli/wiki/Integrating-with-node),
here is a grouped list of the breaking changes with the reasoning as to why they
fit within the guidelines linked above. Note that all the breaking changes were
made in [9.0.0](https://github.com/npm/cli/releases/tag/v9.0.0).
All subsequent minor and patch releases after `npm@9.0.0` do not contain any
breaking changes.

##### Engines

> Explanation: the node engines supported by `npm@9` make it safe to allow `npm@9` as the default in any LTS version of `14` or `16`, as well as anything later than or including `18.0.0`

- `npm` is now compatible with the following semver range for node: `^14.17.0 || ^16.13.0 || >=18.0.0`

##### Filesystem

> Explanation: when run as root previous versions of npm attempted to manage file ownership automatically on the user's behalf. this behavior was problematic in many cases and has been removed in favor of allowing users to manage their own filesystem permissions

- `npm` will no longer attempt to modify ownership of files it creates.

##### Auth

> Explanation: any errors thrown from users having unsupported auth configurations will show `npm config fix` in the remediation instructions, which will allow the user to automatically have their auth config fixed.

- The presence of auth related settings that are not scoped to a specific
  registry found in a config file is no longer supported and will throw errors.

##### Login

> Explanation: the default `auth-type` has changed and users can opt back into the old behavior with `npm config set auth-type=legacy`. `login` and `adduser` have also been seperated making each command more closely match it's name instead of being aliases for each other.

- Legacy auth types `sso`, `saml` & `legacy` have been consolidated into `"legacy"`.
- `auth-type` defaults to `"web"`
- `login` and `adduser` are now separate commands that send different data to the registry.
- `auth-type` config values `web` and `legacy` only try their respective methods,
  npm no longer tries them all and waits to see which one doesn't fail.

##### Tarball Packing

> Explanation: previously using multiple ignore/allow lists when packing was an undefined behavior, and now the order of operations is strictly defined when packing a tarball making it easier to follow and should only affect users relying on the previously undefined behavior.

- `npm pack` now follows a strict order of operations when applying ignore rules.
  If a `files` array is present in the `package.json`, then rules in `.gitignore`
  and `.npmignore` files from the root will be ignored.

##### Display/Debug/Timing Info

> Explanation: these changes center around the display of information to the terminal including timing and debug log info. We do not anticipate these changes breaking any existing workflows.

- Links generated from git urls will now use `HEAD` instead of `master` as the default ref.
- `timing` has been removed as a value for `--loglevel`.
- `--timing` will show timing information regardless of `--loglevel`, except when `--silent`.
- When run with the `--timing` flag, `npm` now writes timing data to a file
  alongside the debug log data, respecting the `logs-dir` option and falling
  back to `<CACHE>/_logs/` dir, instead of directly inside the cache directory.
- The timing file data is no longer newline delimited JSON, and instead each run
  will create a uniquely named `<ID>-timing.json` file, with the `<ID>` portion
  being the same as the debug log.
- `npm` now outputs some json errors on stdout. Previously `npm` would output
  all json formatted errors on stderr, making it difficult to parse as the
  stderr stream usually has logs already written to it.

##### Config/Command Deprecations or Removals

> Explanation: `install-links` is the only config or command in the list that has an effect on package installs. We fixed a number of issues that came up during prereleases with this change. It will also only be applied to new package trees created without a package-lock.json file. Any install with an existing lock file will not be changed.

- Deprecate boolean install flags in favor of `--install-strategy`.
- `npm config set` will no longer accept deprecated or invalid config options.
- `install-links` config defaults to `"true"`.
- `node-version` config has been removed.
- `npm-version` config has been removed.
- `npm access` subcommands have been renamed.
- `npm birthday` has been removed.
- `npm set-script` has been removed.
- `npm bin` has been removed (use `npx` or `npm exec` to execute binaries).

#### Other notable changes

- \[[`03db415540`](https://github.com/nodejs/node/commit/03db415540)] - **build**: disable v8 snapshot compression by default (Joyee Cheung) [#45716](https://github.com/nodejs/node/pull/45716)
- \[[`9f51b9e50d`](https://github.com/nodejs/node/commit/9f51b9e50d)] - **doc**: add doc-only deprecation for headers/trailers setters (Rich Trott) [#45697](https://github.com/nodejs/node/pull/45697)
- \[[`b010820c4e`](https://github.com/nodejs/node/commit/b010820c4e)] - **doc**: add Rafael Gonzaga to the TSC (Michael Dawson) [#45691](https://github.com/nodejs/node/pull/45691)
- \[[`b8b13dccd9`](https://github.com/nodejs/node/commit/b8b13dccd9)] - **(SEMVER-MINOR)** **net**: add autoSelectFamily and autoSelectFamilyAttemptTimeout options (Paolo Insogna) [#44731](https://github.com/nodejs/node/pull/44731)
- \[[`5d7cd363ab`](https://github.com/nodejs/node/commit/5d7cd363ab)] - **(SEMVER-MINOR)** **src**: add uvwasi version (Jithil P Ponnan) [#45639](https://github.com/nodejs/node/pull/45639)
- \[[`4165dcddf0`](https://github.com/nodejs/node/commit/95af851a25)] - **(SEMVER-MINOR)** **test_runner**: add t.after() hook (Colin Ihrig) [#45792](https://github.com/nodejs/node/pull/45792)
- \[[`d1bd7796ad`](https://github.com/nodejs/node/commit/d1bd7796ad)] - **(SEMVER-MINOR)** **test_runner**: don't use a symbol for runHook() (Colin Ihrig) [#45792](https://github.com/nodejs/node/pull/45792)
- \[[`691f58e76c`](https://github.com/nodejs/node/commit/691f58e76c)] - **tls**: remove trustcor root ca certificates (Ben Noordhuis) [#45776](https://github.com/nodejs/node/pull/45776)

### Commits

- \[[`382efdf460`](https://github.com/nodejs/node/commit/382efdf460)] - **benchmark**: add variety of inputs to text-encoder (Yagiz Nizipli) [#45787](https://github.com/nodejs/node/pull/45787)
- \[[`102c2dc071`](https://github.com/nodejs/node/commit/102c2dc071)] - **benchmark**: make benchmarks runnable in older versions of Node.js (Joyee Cheung) [#45746](https://github.com/nodejs/node/pull/45746)
- \[[`e2caf7ced9`](https://github.com/nodejs/node/commit/e2caf7ced9)] - **bootstrap**: lazy load non-essential modules (Joyee Cheung) [#45659](https://github.com/nodejs/node/pull/45659)
- \[[`49840d443c`](https://github.com/nodejs/node/commit/49840d443c)] - **buffer**: remove unnecessary lazy loading (Antoine du Hamel) [#45807](https://github.com/nodejs/node/pull/45807)
- \[[`17847683dc`](https://github.com/nodejs/node/commit/17847683dc)] - **buffer**: make decodeUTF8 params loose (Yagiz Nizipli) [#45610](https://github.com/nodejs/node/pull/45610)
- \[[`03db415540`](https://github.com/nodejs/node/commit/03db415540)] - **build**: disable v8 snapshot compression by default (Joyee Cheung) [#45716](https://github.com/nodejs/node/pull/45716)
- \[[`95a23e24f3`](https://github.com/nodejs/node/commit/95a23e24f3)] - **build**: add python 3.11 support for android (Mohammed Keyvanzadeh) [#45765](https://github.com/nodejs/node/pull/45765)
- \[[`09bc89daba`](https://github.com/nodejs/node/commit/09bc89daba)] - **build**: rework gyp files for zlib (Richard Lau) [#45589](https://github.com/nodejs/node/pull/45589)
- \[[`b5b56b6b45`](https://github.com/nodejs/node/commit/b5b56b6b45)] - **crypto**: simplify lazy loading of internal modules (Antoine du Hamel) [#45809](https://github.com/nodejs/node/pull/45809)
- \[[`2e4d37e3f0`](https://github.com/nodejs/node/commit/2e4d37e3f0)] - **crypto**: fix CipherBase Update int32 overflow (Marco Ippolito) [#45769](https://github.com/nodejs/node/pull/45769)
- \[[`573eab9235`](https://github.com/nodejs/node/commit/573eab9235)] - **crypto**: refactor ArrayBuffer to bigint conversion utils (Antoine du Hamel) [#45567](https://github.com/nodejs/node/pull/45567)
- \[[`845f805490`](https://github.com/nodejs/node/commit/845f805490)] - **crypto**: refactor verify acceptable key usage functions (Filip Skokan) [#45569](https://github.com/nodejs/node/pull/45569)
- \[[`7cc9998737`](https://github.com/nodejs/node/commit/7cc9998737)] - **crypto**: fix ECDH webcrypto public CryptoKey usages (Filip Skokan) [#45569](https://github.com/nodejs/node/pull/45569)
- \[[`d030963f37`](https://github.com/nodejs/node/commit/d030963f37)] - **crypto**: validate CFRG webcrypto JWK import "d" and "x" are a pair (Filip Skokan) [#45569](https://github.com/nodejs/node/pull/45569)
- \[[`9cd106efdc`](https://github.com/nodejs/node/commit/9cd106efdc)] - **crypto**: use DataError for CFRG webcrypto raw and jwk import key checks (Filip Skokan) [#45569](https://github.com/nodejs/node/pull/45569)
- \[[`9e2e3de6ce`](https://github.com/nodejs/node/commit/9e2e3de6ce)] - **crypto**: use DataError for webcrypto keyData import failures (Filip Skokan) [#45569](https://github.com/nodejs/node/pull/45569)
- \[[`40037b4e79`](https://github.com/nodejs/node/commit/40037b4e79)] - **crypto**: fix X25519 and X448 webcrypto public CryptoKey usages (Filip Skokan) [#45569](https://github.com/nodejs/node/pull/45569)
- \[[`de2b6b97b9`](https://github.com/nodejs/node/commit/de2b6b97b9)] - **crypto**: ensure "x" is present when importing private CFRG webcrypto keys (Filip Skokan) [#45569](https://github.com/nodejs/node/pull/45569)
- \[[`75dbce9a07`](https://github.com/nodejs/node/commit/75dbce9a07)] - **deps**: upgrade npm to 9.2.0 (npm team) [#45780](https://github.com/nodejs/node/pull/45780)
- \[[`677eb62bf2`](https://github.com/nodejs/node/commit/677eb62bf2)] - **deps**: upgrade npm to 9.1.3 (npm team) [#45693](https://github.com/nodejs/node/pull/45693)
- \[[`1d823a6d30`](https://github.com/nodejs/node/commit/1d823a6d30)] - _**Revert**_ "**deps**: fix zlib compilation for CPUs without SIMD features" (Luigi Pinca) [#45589](https://github.com/nodejs/node/pull/45589)
- \[[`6b15994597`](https://github.com/nodejs/node/commit/6b15994597)] - **deps**: update undici to 5.13.0 (Node.js GitHub Bot) [#45634](https://github.com/nodejs/node/pull/45634)
- \[[`fbd2d27789`](https://github.com/nodejs/node/commit/fbd2d27789)] - **deps**: update corepack to 0.15.2 (Node.js GitHub Bot) [#45635](https://github.com/nodejs/node/pull/45635)
- \[[`60c9ac5178`](https://github.com/nodejs/node/commit/60c9ac5178)] - **deps**: update nghttp2 to 1.51.0 (Yagiz Nizipli) [#45537](https://github.com/nodejs/node/pull/45537)
- \[[`c8421204b0`](https://github.com/nodejs/node/commit/c8421204b0)] - **deps**: patch V8 to *********** (Michaël Zasso) [#45749](https://github.com/nodejs/node/pull/45749)
- \[[`c5277417c9`](https://github.com/nodejs/node/commit/c5277417c9)] - **diagnostics_channel**: fix diagnostics channel memory leak (theanarkh) [#45633](https://github.com/nodejs/node/pull/45633)
- \[[`8a90f5c784`](https://github.com/nodejs/node/commit/8a90f5c784)] - **doc**: buffer.fill empty value (Marco Ippolito) [#45794](https://github.com/nodejs/node/pull/45794)
- \[[`9d6af617ea`](https://github.com/nodejs/node/commit/9d6af617ea)] - **doc**: add args of filter option of fs.cp (MURAKAMI Masahiko) [#45739](https://github.com/nodejs/node/pull/45739)
- \[[`8c728d2f02`](https://github.com/nodejs/node/commit/8c728d2f02)] - **doc**: disambiguate `native module` to `addon` (Daeyeon Jeong) [#45673](https://github.com/nodejs/node/pull/45673)
- \[[`7718ff82a4`](https://github.com/nodejs/node/commit/7718ff82a4)] - **doc**: using console.error for error cases in crypto and events (emirgoren) [#45640](https://github.com/nodejs/node/pull/45640)
- \[[`029060e6e4`](https://github.com/nodejs/node/commit/029060e6e4)] - **doc**: fix actual result of example is different in events (Deokjin Kim) [#45656](https://github.com/nodejs/node/pull/45656)
- \[[`9f51b9e50d`](https://github.com/nodejs/node/commit/9f51b9e50d)] - **doc**: add doc-only deprecation for headers/trailers setters (Rich Trott) [#45697](https://github.com/nodejs/node/pull/45697)
- \[[`801fe30488`](https://github.com/nodejs/node/commit/801fe30488)] - **doc**: add detail on how api docs are published (Michael Dawson) [#45626](https://github.com/nodejs/node/pull/45626)
- \[[`e124e2a6ee`](https://github.com/nodejs/node/commit/e124e2a6ee)] - **doc**: use console.error for error case in child_process and dgram (Deokjin Kim) [#45690](https://github.com/nodejs/node/pull/45690)
- \[[`1b920287b6`](https://github.com/nodejs/node/commit/1b920287b6)] - **doc**: move streaming instruc to doc/contributing (Michael Dawson) [#45582](https://github.com/nodejs/node/pull/45582)
- \[[`b010820c4e`](https://github.com/nodejs/node/commit/b010820c4e)] - **doc**: add Rafael to the tsc (Michael Dawson) [#45691](https://github.com/nodejs/node/pull/45691)
- \[[`4fb7cf88e2`](https://github.com/nodejs/node/commit/4fb7cf88e2)] - **doc**: add missing line in debugger (Deokjin Kim) [#45632](https://github.com/nodejs/node/pull/45632)
- \[[`c0df265fea`](https://github.com/nodejs/node/commit/c0df265fea)] - **doc**: fix actual result of example is different in stream (Deokjin Kim) [#45619](https://github.com/nodejs/node/pull/45619)
- \[[`027e738064`](https://github.com/nodejs/node/commit/027e738064)] - **doc**: add `options` parameter to eventTarget.removeEventListener (Deokjin Kim) [#45667](https://github.com/nodejs/node/pull/45667)
- \[[`23ff5057b2`](https://github.com/nodejs/node/commit/23ff5057b2)] - **doc**: define "react-native" community condition (Alex Hunt) [#45367](https://github.com/nodejs/node/pull/45367)
- \[[`2e767bf18b`](https://github.com/nodejs/node/commit/2e767bf18b)] - **doc**: move os.machine() docs to sorted position (Colin Ihrig) [#45647](https://github.com/nodejs/node/pull/45647)
- \[[`aabfdef861`](https://github.com/nodejs/node/commit/aabfdef861)] - **doc**: use console.error for error case in fs, https, net and process (Deokjin Kim) [#45606](https://github.com/nodejs/node/pull/45606)
- \[[`3a02d50d35`](https://github.com/nodejs/node/commit/3a02d50d35)] - **doc**: add link to doc with social processes (Michael Dawson) [#45584](https://github.com/nodejs/node/pull/45584)
- \[[`e4316124fa`](https://github.com/nodejs/node/commit/e4316124fa)] - **fs**: fix `nonNativeWatcher` watching folder with existing files (Moshe Atlow) [#45500](https://github.com/nodejs/node/pull/45500)
- \[[`d272faa54d`](https://github.com/nodejs/node/commit/d272faa54d)] - **fs**: fix `nonNativeWatcher` leak of `StatWatchers` (Moshe Atlow) [#45501](https://github.com/nodejs/node/pull/45501)
- \[[`d64e773168`](https://github.com/nodejs/node/commit/d64e773168)] - **http**: make `OutgoingMessage` more streamlike (Robert Nagy) [#45672](https://github.com/nodejs/node/pull/45672)
- \[[`ed8ae88f30`](https://github.com/nodejs/node/commit/ed8ae88f30)] - **lib**: remove unnecessary lazy loading in `internal/encoding` (Antoine du Hamel) [#45810](https://github.com/nodejs/node/pull/45810)
- \[[`302c5240c5`](https://github.com/nodejs/node/commit/302c5240c5)] - **lib**: allow Writeable.toWeb() to work on http.Outgoing message (Debadree Chatterjee) [#45642](https://github.com/nodejs/node/pull/45642)
- \[[`e8745083b9`](https://github.com/nodejs/node/commit/e8745083b9)] - **lib**: check number of arguments in `EventTarget`'s function (Deokjin Kim) [#45668](https://github.com/nodejs/node/pull/45668)
- \[[`9f7bb5ce0e`](https://github.com/nodejs/node/commit/9f7bb5ce0e)] - **lib**: disambiguate `native module` to `binding` (Daeyeon Jeong) [#45673](https://github.com/nodejs/node/pull/45673)
- \[[`353339a552`](https://github.com/nodejs/node/commit/353339a552)] - **lib**: disambiguate `native module` to `builtin module` (Daeyeon Jeong) [#45673](https://github.com/nodejs/node/pull/45673)
- \[[`99410efd19`](https://github.com/nodejs/node/commit/99410efd19)] - **lib**: added SuiteContext class (Debadree Chatterjee) [#45687](https://github.com/nodejs/node/pull/45687)
- \[[`a79f37a0a7`](https://github.com/nodejs/node/commit/a79f37a0a7)] - **lib**: add missing type of removeEventListener in question (Deokjin Kim) [#45676](https://github.com/nodejs/node/pull/45676)
- \[[`e0750467e8`](https://github.com/nodejs/node/commit/e0750467e8)] - **meta**: update AUTHORS (Node.js GitHub Bot) [#45814](https://github.com/nodejs/node/pull/45814)
- \[[`376f3468b9`](https://github.com/nodejs/node/commit/376f3468b9)] - **meta**: update AUTHORS (Node.js GitHub Bot) [#45732](https://github.com/nodejs/node/pull/45732)
- \[[`a6e2cf2d6f`](https://github.com/nodejs/node/commit/a6e2cf2d6f)] - **meta**: add .mailmap entry for Stefan Stojanovic (Rich Trott) [#45703](https://github.com/nodejs/node/pull/45703)
- \[[`eb9a383d2a`](https://github.com/nodejs/node/commit/eb9a383d2a)] - **meta**: update AUTHORS info for nstepien (Nicolas Stepien) [#45692](https://github.com/nodejs/node/pull/45692)
- \[[`049ef342c6`](https://github.com/nodejs/node/commit/049ef342c6)] - **meta**: update AUTHORS (Node.js GitHub Bot) [#45637](https://github.com/nodejs/node/pull/45637)
- \[[`b9c2fc7623`](https://github.com/nodejs/node/commit/b9c2fc7623)] - **net**: check `autoSelectFamilyAttemptTimeout` is positive (Deokjin Kim) [#45740](https://github.com/nodejs/node/pull/45740)
- \[[`b8b13dccd9`](https://github.com/nodejs/node/commit/b8b13dccd9)] - **(SEMVER-MINOR)** **net**: add autoSelectFamily and autoSelectFamilyAttemptTimeout options (Paolo Insogna) [#44731](https://github.com/nodejs/node/pull/44731)
- \[[`6962ef0df1`](https://github.com/nodejs/node/commit/6962ef0df1)] - **readline**: improve robustness against prototype mutation (Antoine du Hamel) [#45614](https://github.com/nodejs/node/pull/45614)
- \[[`7892e23e68`](https://github.com/nodejs/node/commit/7892e23e68)] - **repl**: do not define `wasi` on global with no flag (Kohei Ueno) [#45595](https://github.com/nodejs/node/pull/45595)
- \[[`349b4f8817`](https://github.com/nodejs/node/commit/349b4f8817)] - **src**: add internal isArrayBufferDetached (Yagiz Nizipli) [#45568](https://github.com/nodejs/node/pull/45568)
- \[[`5d7cd363ab`](https://github.com/nodejs/node/commit/5d7cd363ab)] - **(SEMVER-MINOR)** **src**: add uvwasi version (Jithil P Ponnan) [#45639](https://github.com/nodejs/node/pull/45639)
- \[[`8a03684018`](https://github.com/nodejs/node/commit/8a03684018)] - **src**: simplify NodeBIO::GetMethod initialization (Anna Henningsen) [#45799](https://github.com/nodejs/node/pull/45799)
- \[[`b35ebebc0e`](https://github.com/nodejs/node/commit/b35ebebc0e)] - **src**: make structuredClone work for process.env (Ben Noordhuis) [#45698](https://github.com/nodejs/node/pull/45698)
- \[[`81ab54035f`](https://github.com/nodejs/node/commit/81ab54035f)] - **src**: mark generated `snapshot_data` as `const` (Anna Henningsen) [#45786](https://github.com/nodejs/node/pull/45786)
- \[[`79edf257bb`](https://github.com/nodejs/node/commit/79edf257bb)] - **src**: cleanup on disambiguating native modules (Michael Dawson) [#45665](https://github.com/nodejs/node/pull/45665)
- \[[`c9cba2e873`](https://github.com/nodejs/node/commit/c9cba2e873)] - **src**: use `enum class` instead of `enum` in node_i18n (Deokjin Kim) [#45646](https://github.com/nodejs/node/pull/45646)
- \[[`818028caba`](https://github.com/nodejs/node/commit/818028caba)] - **src**: rename internal module declaration as internal bindings (Chengzhong Wu) [#45551](https://github.com/nodejs/node/pull/45551)
- \[[`2fbe2f9f0a`](https://github.com/nodejs/node/commit/2fbe2f9f0a)] - **src,lib**: group properties used as constants from `util` binding (Daeyeon Jeong) [#45539](https://github.com/nodejs/node/pull/45539)
- \[[`56eee72abb`](https://github.com/nodejs/node/commit/56eee72abb)] - **stream**: use structuredClone instead of v8 (Yagiz Nizipli) [#45611](https://github.com/nodejs/node/pull/45611)
- \[[`b297dd5393`](https://github.com/nodejs/node/commit/b297dd5393)] - **test**: remove flaky parallel/test-process-wrap test (Ben Noordhuis) [#45806](https://github.com/nodejs/node/pull/45806)
- \[[`924f6ab3a1`](https://github.com/nodejs/node/commit/924f6ab3a1)] - **test**: order list alphabetically in `test-bootstrap-modules` (Antoine du Hamel) [#45808](https://github.com/nodejs/node/pull/45808)
- \[[`5c4475dab9`](https://github.com/nodejs/node/commit/5c4475dab9)] - **test**: fix invalid output TAP if there newline in test name (Pulkit Gupta) [#45742](https://github.com/nodejs/node/pull/45742)
- \[[`4c51c5c97a`](https://github.com/nodejs/node/commit/4c51c5c97a)] - **test**: fix -Wunused-variable on report-fatalerror (Santiago Gimeno) [#45747](https://github.com/nodejs/node/pull/45747)
- \[[`764725040c`](https://github.com/nodejs/node/commit/764725040c)] - **test**: fix test-watch-mode (Stefan Stojanovic) [#45585](https://github.com/nodejs/node/pull/45585)
- \[[`cd36250fcb`](https://github.com/nodejs/node/commit/cd36250fcb)] - **test**: fix test-watch-mode-inspect (Stefan Stojanovic) [#45586](https://github.com/nodejs/node/pull/45586)
- \[[`b55bd6e8c1`](https://github.com/nodejs/node/commit/b55bd6e8c1)] - **test**: fix typos in test/parallel (Deokjin Kim) [#45583](https://github.com/nodejs/node/pull/45583)
- \[[`358e2fe217`](https://github.com/nodejs/node/commit/358e2fe217)] - **test,crypto**: update WebCryptoAPI WPT (Filip Skokan) [#45569](https://github.com/nodejs/node/pull/45569)
- \[[`424419c2b4`](https://github.com/nodejs/node/commit/424419c2b4)] - **test_runner**: refactor `tap_lexer` to use more primordials (Antoine du Hamel) [#45744](https://github.com/nodejs/node/pull/45744)
- \[[`ffc0f3d7be`](https://github.com/nodejs/node/commit/ffc0f3d7be)] - **test_runner**: refactor `tap_parser` to use more primordials (Antoine du Hamel) [#45745](https://github.com/nodejs/node/pull/45745)
- \[[`4165dcddf0`](https://github.com/nodejs/node/commit/4165dcddf0)] - **(SEMVER-MINOR)** **test_runner**: add t.after() hook (Colin Ihrig) [#45792](https://github.com/nodejs/node/pull/45792)
- \[[`d1bd7796ad`](https://github.com/nodejs/node/commit/d1bd7796ad)] - **(SEMVER-MINOR)** **test_runner**: don't use a symbol for runHook() (Colin Ihrig) [#45792](https://github.com/nodejs/node/pull/45792)
- \[[`6bc7b7e6f4`](https://github.com/nodejs/node/commit/6bc7b7e6f4)] - **test_runner**: add resetCalls to MockFunctionContext (MURAKAMI Masahiko) [#45710](https://github.com/nodejs/node/pull/45710)
- \[[`3e485365ec`](https://github.com/nodejs/node/commit/3e485365ec)] - **test_runner**: don't parse TAP from stderr (Colin Ihrig) [#45618](https://github.com/nodejs/node/pull/45618)
- \[[`efc44567c9`](https://github.com/nodejs/node/commit/efc44567c9)] - **test_runner**: add getter and setter to MockTracker (MURAKAMI Masahiko) [#45506](https://github.com/nodejs/node/pull/45506)
- \[[`c9cbd1d396`](https://github.com/nodejs/node/commit/c9cbd1d396)] - **test_runner**: remove stdout and stderr from error (Colin Ihrig) [#45592](https://github.com/nodejs/node/pull/45592)
- \[[`691f58e76c`](https://github.com/nodejs/node/commit/691f58e76c)] - **tls**: remove trustcor root ca certificates (Ben Noordhuis) [#45776](https://github.com/nodejs/node/pull/45776)
- \[[`d384b73f76`](https://github.com/nodejs/node/commit/d384b73f76)] - **tools**: update lint-md-dependencies (Node.js GitHub Bot) [#45730](https://github.com/nodejs/node/pull/45730)
- \[[`324ae3d5dd`](https://github.com/nodejs/node/commit/324ae3d5dd)] - **tools**: add GitHub token permissions to label flaky-test issues (Gabriela Gutierrez) [#45308](https://github.com/nodejs/node/pull/45308)
- \[[`418ae9be56`](https://github.com/nodejs/node/commit/418ae9be56)] - **tools**: remove dependency vulnerability checker (Facundo Tuesca) [#45675](https://github.com/nodejs/node/pull/45675)
- \[[`238fc64c38`](https://github.com/nodejs/node/commit/238fc64c38)] - **tools**: update lint-md-dependencies to rollup\@3.4.0 (Node.js GitHub Bot) [#45638](https://github.com/nodejs/node/pull/45638)
- \[[`1b98f17876`](https://github.com/nodejs/node/commit/1b98f17876)] - **tools**: update doc to highlight.js\@11.7.0 (Node.js GitHub Bot) [#45636](https://github.com/nodejs/node/pull/45636)
- \[[`470384e7be`](https://github.com/nodejs/node/commit/470384e7be)] - **util**: use private symbols in JS land directly (Joyee Cheung) [#45379](https://github.com/nodejs/node/pull/45379)
- \[[`cee6f382d8`](https://github.com/nodejs/node/commit/cee6f382d8)] - **watch**: add CLI flag to preserve output (Debadree Chatterjee) [#45717](https://github.com/nodejs/node/pull/45717)

Windows 32-bit Installer: https://nodejs.org/dist/v19.3.0/node-v19.3.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v19.3.0/node-v19.3.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v19.3.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v19.3.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v19.3.0/node-v19.3.0.pkg \
macOS Apple Silicon 64-bit Binary: https://nodejs.org/dist/v19.3.0/node-v19.3.0-darwin-arm64.tar.gz \
macOS Intel 64-bit Binary: https://nodejs.org/dist/v19.3.0/node-v19.3.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v19.3.0/node-v19.3.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v19.3.0/node-v19.3.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v19.3.0/node-v19.3.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v19.3.0/node-v19.3.0-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v19.3.0/node-v19.3.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v19.3.0/node-v19.3.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v19.3.0/node-v19.3.0.tar.gz \
Other release files: https://nodejs.org/dist/v19.3.0/ \
Documentation: https://nodejs.org/docs/v19.3.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

a5ff43eab40dfa812eeb804a7e064cc9e549e04347f0a0e2250a2a8253eaee6f  node-v19.3.0-aix-ppc64.tar.gz
a50be904794d083fa8ecd4113845cea37968cbe3e5c1e758b0ec6215e1e7495e  node-v19.3.0-darwin-arm64.tar.gz
9b4811af441296e61edfca3186bb694d99daa2ba4344041d4de891b9be587782  node-v19.3.0-darwin-arm64.tar.xz
d9692a5f153d2527ec43860e40fa0e77825543f554384aa8d26d33417ffb9069  node-v19.3.0-darwin-x64.tar.gz
3bc9a06f53b48a5bb2f4b3d370d5ad6b53e3c36fada85f6850120130fde2a444  node-v19.3.0-darwin-x64.tar.xz
40db0bd09a0e71f4447f15b485fc66715c55453c8e89c3b25872c1fdcd3ac29d  node-v19.3.0-headers.tar.gz
e54e2a91c9959d59b7d27e1fa161a699aeca85da4a736891ca4c946573d9ee77  node-v19.3.0-headers.tar.xz
f892b536b00f780aba42b7ccc8974e57e58c4f1de3f9c85d1bbf13c60c680974  node-v19.3.0-linux-arm64.tar.gz
b9fbcd1d4433c073528abcac3d31347847e5ef229248cbe1dccca1a1379a5d2c  node-v19.3.0-linux-arm64.tar.xz
2c2ee707bcb93d5151ff0dbb5825060d364f0966dc5aab3fe58715d0e4527da8  node-v19.3.0-linux-armv7l.tar.gz
494934a42eb76982b5cbbb84bef0726b828287a03fcf6bf05e78eaf2c8dab703  node-v19.3.0-linux-armv7l.tar.xz
d0d34510a7951afe4369876b671678ccd9bb11b7d24b07af10cabe464e371300  node-v19.3.0-linux-ppc64le.tar.gz
662efb12dc05b603ea51b46224b7885a3a56be652282a6234a2a13e081ceb43b  node-v19.3.0-linux-ppc64le.tar.xz
24578073d7339f9a6a8763df30e37bbf1caf3dbc44a6c6c6335c58c274d13b76  node-v19.3.0-linux-s390x.tar.gz
17a1582063ad9b6a60aca5323de76df657d08fb3b2018a6786e1fa782f7f29b0  node-v19.3.0-linux-s390x.tar.xz
b525028ae5bb71b5b32cb7fce903ccce261dbfef4c7dd0f3e0ffc27cd6fc0b3f  node-v19.3.0-linux-x64.tar.gz
de94d6db26edee92a03512552c8be9db38a7bfefa7b3228328afdfb5094e3a76  node-v19.3.0-linux-x64.tar.xz
335daf080bf4763c42070b01d38159d91cb6ee0dd8cde813bbb99cff01faf23e  node-v19.3.0.pkg
4dc4c4e0c510913ed6c4f37b516243ab96a2d98eff1b7d78cf8f8f8b6d415b98  node-v19.3.0.tar.gz
d3189574ef9849c713822e7f31de7a1b9dd8a2c6b5fc78ddb811aaa259a22b1e  node-v19.3.0.tar.xz
fdc3f9c0991a9e54640b792ceb758831f91f6384b0429149b8f3514fa2734e8c  node-v19.3.0-win-x64.7z
9b93f6cb78e23a4cab9e401a8418daa9112bbf5bd3127642adf22cfdd4241348  node-v19.3.0-win-x64.zip
2149012398fb720e1c6e79422344b8281bb4c90024461de3d72dcae06ad3d666  node-v19.3.0-win-x86.7z
baf2de80f5c3f9bc679c3d377f48a0b98e75d12fa4f8ebea166883df7067b7b6  node-v19.3.0-win-x86.zip
2efe9f3a91de1375ab581266f35fe89a779af8389b80612401ea6a28ed2dd16e  node-v19.3.0-x64.msi
6eca60b54ff2a8fe6508b4d0beb0a316077c135d35e8223445a7c9a23fbd11ee  node-v19.3.0-x86.msi
c7a1db0fb60ffec3b06bfaa9c2d58778134c28258ecf75ab93968e536d364bf1  win-x64/node.exe
fde595f3ac64db4522d934f4a4b09c6a11406d8e4f431c6b5374aac38041e6cf  win-x64/node.lib
517ef6f4451280e59bccb77fe77bc3202e32938bad379b6626bf50f6e1fc88d7  win-x64/node_pdb.7z
a428af310d552710e828d5ca41865c9cadf81d31a19297917098b45b5ab39c93  win-x64/node_pdb.zip
32365d11881180435c0efac9d18e5ee396c75b2aef6cd1a3c30ec8c2322b6bb4  win-x86/node.exe
dd1f1c3bbb3a6dd4c3ab1d5663688949580cd4ec24b523df23cab90cf666d391  win-x86/node.lib
6a80d7c2930f1bf4fb894350da3194949bc1408b7aea1dd2f409d195148d755f  win-x86/node_pdb.7z
233c08350135d2b572ac5067fa0cd1f8fb35b6770c2863f26f41b94ec574e798  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEj8yhP+8dDC6RAI4Jdw96mlrhVgAFAmOZ1jcACgkQdw96mlrh
VgDHwA/9EguDL6MKW3kEKhY48AFB3gWNG6b5VZMENSHPo/cELBVrWjCsw0hD9bZV
xAdE2GnRg8PjmJH76DwjaiW/axijiD/7U12esomhgnFg2LBIXnzSsUeNbRsrupFu
4jVJTcC59M2xPXpdo7SXmrfCLFVJD9nOeaV1orS6Q4/Z8wE41nGXWtILWPWGmIRw
T+RZ09elhcAzAjFBKzWTQl/XOkc15YqgwHoeOVZ4b3txNL2pD7CWw8HMPuwUdi3A
b9B1aZuz9OxOV2RF/cxZFL6P+xbgSa1OeRIqz0UdvslxOmzAG6s3flr+5nVJMu2B
IdSFmCcb2mhbiDjgzpmqBU43vxIHBpAx9NRjCcOG9xpPZs+nAcyftOetetvWohKV
mppmKVfhIsSn1gYTmjtZ1vxvLAffRymGkbpJLLpvN81wQGWHIN46ZjNRDmSHI+CG
t1AYHScWA+FaPdrXs7e4vT6nxVQ/ZaohDBz79UN3o2hO43MXxM1HByZb1tnIqcpc
rcdyUBYO42sJrYnJ7x1u6IdE8z+chHORR1Q+v8uiUCnaxpTPzf6DEcjm0otpI5QB
ZvQz3GIDNQKmydal1yUFr+hSiY6hEpQOLc5n8qOyPrwBSIkjNCPF4D1kYoVj/9ue
t2Zc94IzjzIap8kWRf7sYtH0ILibAzTzd0Qc2hEJd4XK58JXjZw=
=3xD9
-----END PGP SIGNATURE-----

```
