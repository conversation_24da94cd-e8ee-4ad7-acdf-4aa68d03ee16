---
date: '2024-07-08T18:16:32.440Z'
category: release
title: Node v20.15.1 (LTS)
layout: blog-post
author: <PERSON>
---

## 2024-07-08, Version 20.15.1 'Iron' (LTS), @RafaelGSS

This is a security release.

### Notable Changes

- CVE-2024-36138 - Bypass incomplete fix of CVE-2024-27980 (High)
- CVE-2024-22020 - Bypass network import restriction via data URL (Medium)
- CVE-2024-22018 - fs.lstat bypasses permission model (Low)
- CVE-2024-36137 - fs.fchown/fchmod bypasses permission model (Low)
- CVE-2024-37372 - Permission model improperly processes UNC paths (Low)

### Commits

- \[[`60e184a6e4`](https://github.com/nodejs/node/commit/60e184a6e4)] - **lib,esm**: handle bypass network-import via data: (RafaelGSS) [nodejs-private/node-private#522](https://github.com/nodejs-private/node-private/pull/522)
- \[[`025cbd6936`](https://github.com/nodejs/node/commit/025cbd6936)] - **lib,permission**: support fs.lstat (RafaelGSS) [nodejs-private/node-private#486](https://github.com/nodejs-private/node-private/pull/486)
- \[[`d38ea17341`](https://github.com/nodejs/node/commit/d38ea17341)] - **lib,permission**: disable fchmod/fchown when pm enabled (RafaelGSS) [nodejs-private/node-private#584](https://github.com/nodejs-private/node-private/pull/584)
- \[[`1ba624cd3b`](https://github.com/nodejs/node/commit/1ba624cd3b)] - **src**: handle permissive extension on cmd check (RafaelGSS) [nodejs-private/node-private#596](https://github.com/nodejs-private/node-private/pull/596)
- \[[`2524d00c3d`](https://github.com/nodejs/node/commit/2524d00c3d)] - **src,permission**: fix UNC path resolution (RafaelGSS) [nodejs-private/node-private#581](https://github.com/nodejs-private/node-private/pull/581)
- \[[`484cb0f13c`](https://github.com/nodejs/node/commit/484cb0f13c)] - **src,permission**: resolve path on fs_permission (Rafael Gonzaga) [#52761](https://github.com/nodejs/node/pull/52761)

Windows 32-bit Installer: https://nodejs.org/dist/v20.15.1/node-v20.15.1-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v20.15.1/node-v20.15.1-x64.msi \
Windows ARM 64-bit Installer: https://nodejs.org/dist/v20.15.1/node-v20.15.1-arm64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v20.15.1/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v20.15.1/win-x64/node.exe \
Windows ARM 64-bit Binary: https://nodejs.org/dist/v20.15.1/win-arm64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v20.15.1/node-v20.15.1.pkg \
macOS Apple Silicon 64-bit Binary: https://nodejs.org/dist/v20.15.1/node-v20.15.1-darwin-arm64.tar.gz \
macOS Intel 64-bit Binary: https://nodejs.org/dist/v20.15.1/node-v20.15.1-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v20.15.1/node-v20.15.1-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v20.15.1/node-v20.15.1-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v20.15.1/node-v20.15.1-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v20.15.1/node-v20.15.1-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v20.15.1/node-v20.15.1-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v20.15.1/node-v20.15.1-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v20.15.1/node-v20.15.1.tar.gz \
Other release files: https://nodejs.org/dist/v20.15.1/ \
Documentation: https://nodejs.org/docs/v20.15.1/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

dd24c8b6fdaf46361e130c894fd7282266f944b54196636e6df583fdec1e836f  node-v20.15.1-aix-ppc64.tar.gz
9cbfc9d496427893505f8cb81aa4c1554fe449881cb4a6c5410e494c5fc36674  node-v20.15.1-arm64.msi
4743bc042f90ba5d9edf09403207290a9cdd2f6061bdccf7caaa0bbfd49f343e  node-v20.15.1-darwin-arm64.tar.gz
106ad5288f1da94bf25cf9fba4a070b442e3213e25ce8af3ad35bf6e266213f6  node-v20.15.1-darwin-arm64.tar.xz
f5379772ffae1404cfd1fcc8cf0c6c5971306b8fb2090d348019047306de39dc  node-v20.15.1-darwin-x64.tar.gz
34ad01b42025f72d486f9775a2f170913ad6b9fe2d4ceb67746a08de0e475b88  node-v20.15.1-darwin-x64.tar.xz
8c2305c6df5d14525e0711f0da38295600987df4c2710c738c01400862a176b4  node-v20.15.1-headers.tar.gz
d6e4f101f8734f96be558ad4b84a35a81f33decc050a7d2d8e5b39573b79bdf8  node-v20.15.1-headers.tar.xz
8554c91ccd32782351035d3a9b168ad01c6922480800a21870fc5d6d86c2bb70  node-v20.15.1-linux-arm64.tar.gz
10d47a46ef208b3e4b226e4d595a82659123b22397ed77b7975d989114ec317e  node-v20.15.1-linux-arm64.tar.xz
2c16717da7d2d7b00f6af146cdf436a0297cbcee52c85b754e4c9ed7cee34b51  node-v20.15.1-linux-armv7l.tar.gz
7bc120efdd8018f6915471b963d9b80adf4ed406d6dc9edb4ae944b85f505c4c  node-v20.15.1-linux-armv7l.tar.xz
b91df4971b428f9cb2fbe427c919ad382c4cd206a85e5c918c60c15f1e3d2e32  node-v20.15.1-linux-ppc64le.tar.gz
b33e684802251397ad62ad3f8a1836267ee8b7723f87f669470018ad0035287b  node-v20.15.1-linux-ppc64le.tar.xz
393f511b5623c8a872e58203914a54bc7e086b8ca870d34833766d4f9c4e2448  node-v20.15.1-linux-s390x.tar.gz
e2c36cdccc8a7c1000a349dd6fea8b0ce39884eae7b3dd1950d0105120f20848  node-v20.15.1-linux-s390x.tar.xz
a9db028c0a1c63e3aa0d97de24b0966bc507d8239b3aedc4e752eea6b0580665  node-v20.15.1-linux-x64.tar.gz
26700f8d3e78112ad4a2618a9c8e2816e38a49ecf0213ece80e54c38cb02563f  node-v20.15.1-linux-x64.tar.xz
4f437463e708c4c7faaa436bed46c3ea814ec3796cfe1e02515ab21d2038b4b1  node-v20.15.1-win-arm64.7z
6cc4f9ca826f5b3e0c555d156bc6adcc371bd96c2874ee748d0f97e2938d3c2b  node-v20.15.1-win-arm64.zip
5dbaf27053a0566395f81ebe9e4660141de1bc7b0fe80583447bb36804643f75  node-v20.15.1-win-x64.7z
ba6c3711e2c3d0638c5f7cea3c234553808a73c52a5962a6cdb47b5210b70b04  node-v20.15.1-win-x64.zip
2281b04df475efa64ef483529fc9cad1715d42d5766e68541b64970297247692  node-v20.15.1-win-x86.7z
9a08021e4bcc4694bc72d00ce1ce0686e6de6a9a855678239625f96b09c70b07  node-v20.15.1-win-x86.zip
b139ba1b82807918af40fbed49a5b529f67ba198e87bcabdac907b734ff83ab5  node-v20.15.1-x64.msi
6079df4ab0d457180b4b730fab76d0b60b14342d797cc10a4f2d7c8b61fba584  node-v20.15.1-x86.msi
93b9549a65d459cc2e035c0d583101f827607f43376b5f23a3a2a900f5467321  node-v20.15.1.pkg
da228a0c27922f02001d9a781793696432096ab2da658eb77d7fc21693f4c5cb  node-v20.15.1.tar.gz
fdd53a5729d936691a2a1151046fb4897721cb8b0fca2af957823a9b40fe0c34  node-v20.15.1.tar.xz
8e3f84e8ec7e41f98a048eb0c1365cfe54426a556ead98c4803df45d29e0335d  win-arm64/node.exe
a4f01329c1c211082ac3ed387ff6651530040bbf7250ec419ce8f95b10d7804a  win-arm64/node.lib
493292505fd7a156b1e7b46c7f05001a0684fba6f734f83abfcf7fed88625453  win-arm64/node_pdb.7z
88d4af538deadf8fa2638df84a76bd7dd26f0aeac8dc584f213da736f322377c  win-arm64/node_pdb.zip
229fb64aeb10d3cc18eaaa2f5a4c3f1c81792dd3647c5c4350e142db528d0f89  win-x64/node.exe
87056190b7cd06f40058f8e059efd328cdcc7600b825afa102c0aa5039865af5  win-x64/node.lib
bb2198b381bb5d7bc08e2cdda3db911996e310b944b05cb8c7c271a5a7ab0901  win-x64/node_pdb.7z
316ee3fbbe976981e8ee0b81204aece9d3c2337c83f1644d90bb552c3068ca44  win-x64/node_pdb.zip
6e7f3cbb46569a58babe99de2df8a69e98ad613674d4fed71b1dca866e1a72e8  win-x86/node.exe
fa02ae7feca7eb6c4a0f1b929126df400719f5d18a2ec4b7d12c52fbe0b13814  win-x86/node.lib
e8f6da56c9bc73add71a41c4d5ed92fc6cf9e7c5067d7a0d3f7b9fd6391f07c4  win-x86/node_pdb.7z
1b4e9dbc5a8b0a5121d32351f9654c1ab451e88680982d487a4a6c40d50bd730  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQGzBAEBCAAdFiEEiQwI24V5Fi/uDfnbi+q0389VXvQFAmaMK0kACgkQi+q0389V
XvRgaQv+N9/ZpHlLeTQUW+rVBWeuqnPDRTUtL68DDSz5hNTy7BG3TcHtRLXx8Vla
ycwlWM5agMl8Ffg05r+F4OwlwWBnLCS0MI+VLfaEKqLoV/VdHyz32bxH7XcmpN/N
DTqm+yKkqVZRjF/ZMkEPR7jj0ZEPu5aczwund8vsEq3LASXCt7xO3Rdbpu1lffiK
YS97ZdiXH6/o5j8+AzqLM0fqNtofJh1/QK1OXQdJfWr9647wJLbQuJEVVt19re1R
7UKkIwT0kdX75+il/z3pm7WFhr9mt4uVZuqIv3cAdq70pFg3W1Z3qbW6MF/+a2Tn
8Ll6eFRuz07cj4gwczcpyBc9i1/8itP1sP0XnknFvF4DRwqqKAn9rZ3+C+LD7uoO
HlOvUVvMdrz+/mFX7u1J2foQLUySSnmP3s24tIlyfgIKxUXy8KUX6hp9mT73U2b8
wDesvSmMwVDg6e7BQOyJf1n6Sp6DjrjbK0GHsFnxfe8h60eA3oiFNzMYG9NWRr5z
M1gW4Q74
=798C
-----END PGP SIGNATURE-----
```
