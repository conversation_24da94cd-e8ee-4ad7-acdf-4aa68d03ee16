---
date: '2019-04-30T17:56:44.870Z'
category: release
title: Node v11.15.0 (Current)
layout: blog-post
author: <PERSON>
---

### Notable changes

- **deps**: add s390 asm rules for OpenSSL-1.1.1 (<PERSON><PERSON><PERSON>) [#19794](https://github.com/nodejs/node/pull/19794)
- **src**: add .code and SSL specific error properties (<PERSON>) [#25093](https://github.com/nodejs/node/pull/25093)
- **tls**:
  - add --tls-min-v1.2 CLI switch (<PERSON>) [#26951](https://github.com/nodejs/node/pull/26951)
  - supported shared openssl 1.1.0 (<PERSON>) [#26951](https://github.com/nodejs/node/pull/26951)
  - revert default max toTLSv1.2 (<PERSON>) [#26951](https://github.com/nodejs/node/pull/26951)
  - revert change to invalid protocol error type (<PERSON>) [#26951](https://github.com/nodejs/node/pull/26951)
  - support TLSv1.3 (<PERSON>) [#26209](https://github.com/nodejs/node/pull/26209)
  - add code for ERR_TLS_INVALID_PROTOCOL_METHOD (Sam Roberts) [#24729](https://github.com/nodejs/node/pull/24729)

### Commits

- [[`7da23dcbfa`](https://github.com/nodejs/node/commit/7da23dcbfa)] - **deps**: V8: backport 61f4c22 (Anna Henningsen) [#27259](https://github.com/nodejs/node/pull/27259)
- [[`8db791d0fe`](https://github.com/nodejs/node/commit/8db791d0fe)] - **deps**: update archs files for OpenSSL-1.1.1b (Sam Roberts) [#26327](https://github.com/nodejs/node/pull/26327)
- [[`1c98b720b1`](https://github.com/nodejs/node/commit/1c98b720b1)] - **(SEMVER-MINOR)** **deps**: add s390 asm rules for OpenSSL-1.1.1 (Shigeki Ohtsu) [#19794](https://github.com/nodejs/node/pull/19794)
- [[`d8cc478ae9`](https://github.com/nodejs/node/commit/d8cc478ae9)] - **deps**: upgrade openssl sources to 1.1.1b (Sam Roberts) [#26327](https://github.com/nodejs/node/pull/26327)
- [[`fa6f0f1644`](https://github.com/nodejs/node/commit/fa6f0f1644)] - **doc**: describe tls.DEFAULT_MIN_VERSION/\_MAX_VERSION (Sam Roberts) [#26821](https://github.com/nodejs/node/pull/26821)
- [[`8b5d350a35`](https://github.com/nodejs/node/commit/8b5d350a35)] - **(SEMVER-MINOR)** **src**: add .code and SSL specific error properties (Sam Roberts) [#25093](https://github.com/nodejs/node/pull/25093)
- [[`bf2c283555`](https://github.com/nodejs/node/commit/bf2c283555)] - **(SEMVER-MINOR)** **tls**: add --tls-min-v1.2 CLI switch (Sam Roberts) [#26951](https://github.com/nodejs/node/pull/26951)
- [[`7aeca270f6`](https://github.com/nodejs/node/commit/7aeca270f6)] - **(SEMVER-MINOR)** **tls**: supported shared openssl 1.1.0 (Sam Roberts) [#26951](https://github.com/nodejs/node/pull/26951)
- [[`d2666e6ded`](https://github.com/nodejs/node/commit/d2666e6ded)] - **tls**: add debugging to native TLS code (Anna Henningsen) [#26843](https://github.com/nodejs/node/pull/26843)
- [[`225417b849`](https://github.com/nodejs/node/commit/225417b849)] - **tls**: add CHECK for impossible condition (AnnaHenningsen) [#26843](https://github.com/nodejs/node/pull/26843)
- [[`109c097797`](https://github.com/nodejs/node/commit/109c097797)] - **(SEMVER-MINOR)** **tls**: revert default max toTLSv1.2 (Sam Roberts) [#26951](https://github.com/nodejs/node/pull/26951)
- [[`7393e37af1`](https://github.com/nodejs/node/commit/7393e37af1)] - **(SEMVER-MINOR)** **tls**: support TLSv1.3 (Sam Roberts) [#26209](https://github.com/nodejs/node/pull/26209)
- [[`8e14859459`](https://github.com/nodejs/node/commit/8e14859459)] - **(SEMVER-MINOR)** **tls**: revert change to invalid protocol error type (Sam Roberts) [#26951](https://github.com/nodejs/node/pull/26951)
- [[`00688b6042`](https://github.com/nodejs/node/commit/00688b6042)] - **(SEMVER-MINOR)** **tls**: add code for ERR_TLS_INVALID_PROTOCOL_METHOD (Sam Roberts) [#24729](https://github.com/nodejs/node/pull/24729)

Windows 32-bit Installer: https://nodejs.org/dist/v11.15.0/node-v11.15.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v11.15.0/node-v11.15.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v11.15.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v11.15.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v11.15.0/node-v11.15.0.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v11.15.0/node-v11.15.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v11.15.0/node-v11.15.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v11.15.0/node-v11.15.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v11.15.0/node-v11.15.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v11.15.0/node-v11.15.0-aix-ppc64.tar.gz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v11.15.0/node-v11.15.0-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v11.15.0/node-v11.15.0-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v11.15.0/node-v11.15.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v11.15.0/node-v11.15.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v11.15.0/node-v11.15.0.tar.gz \
Other release files: https://nodejs.org/dist/v11.15.0/ \
Documentation: https://nodejs.org/docs/v11.15.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

f6cbaa83f176f7b267e8751c5b99b4d4f5cd43b3a441593a2e44287d733cc976  node-v11.15.0-aix-ppc64.tar.gz
e953b657b1049e1de509a3fd0700cfeecd175f75a0d141d71393aa0d71fa29a9  node-v11.15.0-darwin-x64.tar.gz
fef95bb4ed017dafc3cc61e140b10630ef30bd22468fd64740f948339b790f14  node-v11.15.0-darwin-x64.tar.xz
e738ca0fa48e1b43a4edb8d3accb7ed6ce65aa27e74ccfd26989eb87a0add509  node-v11.15.0-headers.tar.gz
e85fd7fbc63fe495f187d3df94667dbac740e91a0fd30e131cde3825ee5e9241  node-v11.15.0-headers.tar.xz
78237456386d66ac2143a25530dd5b39326a874079ba7c0676a4639e894567c4  node-v11.15.0-linux-arm64.tar.gz
e458aa4c69da9ca2ae566c8eb56dc8b36d573b415bfd8eebca4ff2229fc4983d  node-v11.15.0-linux-arm64.tar.xz
d376ea6412859ed2e59739bd478219f832f11545e4f727e00e16d83a386960c0  node-v11.15.0-linux-armv6l.tar.gz
167c944d08316e002e350647881e657a73d7c33a0d43b40f588bf3dc0b0615b1  node-v11.15.0-linux-armv6l.tar.xz
ae6c41d78df4c5ef4032128eda9abd49a549bff5c8a20fd32d1072b6e5e8556b  node-v11.15.0-linux-armv7l.tar.gz
7396062da8af802124440305ccb8f62b6db2345581682735703be1b4bf05b213  node-v11.15.0-linux-armv7l.tar.xz
cfac98ac524f28e4a1283b8148f81fafde4d0cebfceadcb2609e4a4f8427e94c  node-v11.15.0-linux-ppc64le.tar.gz
4f4913250a2baf091f81b35d60d61a81ce34bb8e3ed5168fcfd395f7fed04b67  node-v11.15.0-linux-ppc64le.tar.xz
51fb61db4312ec72b015db11ad95f599d9c931b65d6a187430a54dd1ddc578da  node-v11.15.0-linux-s390x.tar.gz
d1587187251be765415ea5a9cf7c7aeba034fe3d45093d2de2add53802f02a87  node-v11.15.0-linux-s390x.tar.xz
98bd18051cbdb39bbcda1ab169ca3fd3935d87e9cfc36e1b6fd6f609d46856bb  node-v11.15.0-linux-x64.tar.gz
17424aef198fa322b93c79217ce7e8cdd264fed40383abbbd3e63c305ce1d7d8  node-v11.15.0-linux-x64.tar.xz
1f8502e6652ab5c90210eafc2a1573f5aa88697644dadd718a0a94c77c3fa736  node-v11.15.0.pkg
e52a29d968d5d0c7176578840b3612329b4bfc3dab071b75f9a3256d09fd14ac  node-v11.15.0-sunos-x64.tar.gz
057550a4b6e4c5ea8c30f222fe9071dc0ee4e1239e2f358166be37fcd29a4969  node-v11.15.0-sunos-x64.tar.xz
2045ace2fcf130b0f18b82b027015dd31b262c2c97fe9bf2533227c52b59c01c  node-v11.15.0.tar.gz
68a776c5d8b8b91a8f2adac2ca4ce4390ae1804883ec7ec9c0d6a6a64d306a76  node-v11.15.0.tar.xz
9308998adc6a70a73647e426561ff5f05bda926675888cdf98bc71ab104c5989  node-v11.15.0-win-x64.7z
f3cef50acf566724a5ec5df7697fb527d7339cafdae6c7c406a39358aee6cdf8  node-v11.15.0-win-x64.zip
fef827207376d90712f3c805616519d6ef95bac42bc3625f0546d17fdf7e12f8  node-v11.15.0-win-x86.7z
44265a6474561c9e34c2ec0cdfe3b60cbb0d1ece81b56d702f028f9249451a7a  node-v11.15.0-win-x86.zip
d383c648f637aa672f425431d4b97d3fd921bdb86e8502bb245794ad4832b1eb  node-v11.15.0-x64.msi
b2d824007bfaa20b2194830f53f9a58f48a029ec6c78e0820fea7927a78e9f48  node-v11.15.0-x86.msi
3567c207637df8cfbc17ed9705db80f61eb33f25636c634747450d35bf3f0dbf  win-x64/node.exe
5d3b7f0cfec81d8005443ff7c2dd9ea8c02b86856ea8c7c395342219c48a4a9b  win-x64/node.lib
8b8d643081d0f499fc587509da10d4fe23dd74d0e46d4aa2dee3a07cdd80b77b  win-x64/node_pdb.7z
27e7bf1d545066861aa12b32924880470ed939ee0e5438231e3a02221324a360  win-x64/node_pdb.zip
70d09f36f31376d97bd7142717597def00cd481bf1eb10cef25a0861419a9c72  win-x86/node.exe
f8e3ac52d04d170b3d697697fa442520a25f59e27d9d75bbcaa3addb8ee2125d  win-x86/node.lib
e55446bd49f5a5de19008e7983b6dc7b0b3b7f72e58d3ac73ece34542c58ad3a  win-x86/node_pdb.7z
4d9f45f557ea3b59b8459b70b74c1a688b83dbb922ca81171374181c68847775  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEueL1mBqm4M0oFg2f8TmTp1WZZTwFAlzIi04ACgkQ8TmTp1WZ
ZTzP7g//fjLQqWOiiO8Jxq9AkWwKZEMQoY7fm9GwYKO2WvIOZk0AbisODhpeO99l
OttkkaDfT/+EXpanE2rgv22RglIzZ4Bz3vyofJSZ+Im5O4zDm1H7Lmm9Xv1ykjMi
yC0yeyD2EDt0X63ZT8iplLtw7L7io97xP0iT7Ok1jmLtYs3RasMYB1N8rBUFnF2p
GK9R+IDiHZKrE4PEImJNJDE8RngY/RMqxrkCBacDHAB80Jwvw/c1wf7QhbNZpN//
TK6i49L8+/22gPWmDWqb0hE2LRmdyxk35B8uvrYpo+NhxRCppoKWQMgTabAsvrg6
5xweZe9Dej/LuffuFblkn5iHo7JTUEFnU6mxcNE1YQnqhZDVoyAlrvUFBTiZNZjp
oIslrH+2p4deqEWV2MItuxuExX3FCOXYYcraovFkUCKomv1CGM0f5ubkyHUYntGE
NkFyyTDHrE/6C/bJe/foh1cLOHnqdky8jVRRnhLBM8YmxA68Hh//eIvOvwXWoCIY
MKB/WsL9iA+4O131fR9eRYACfe5znpoa+jKd3oa97hZprvAqQm2DoivDOosk24yh
5flvV4zZyR8iu7yFYLJCH5/NSD+MN0GbGgjlqZ8epeAWn4fUahru8fpF3HnG9tiS
MOURTDs/G/+krNEPIAHcn4d6l8cb3VzrLpjdlAywvxcj1k9dVdw=
=VC2t
-----END PGP SIGNATURE-----

```
