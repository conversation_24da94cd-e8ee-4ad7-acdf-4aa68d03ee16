---
date: '2016-09-28T00:47:06.792Z'
category: release
title: Node v4.6.0 (LTS)
layout: blog-post
author: <PERSON>
---

**This is an important security release**. All Node.js users should consult the [security release summary](/blog/vulnerability/september-2016-security-releases/) at for details on patched vulnerabilities.

### Notable Changes

Semver Minor:

- **openssl**:
  - Upgrade to 1.0.2i, fixes a number of defects impacting Node.js: CVE-2016-6304 ("OCSP Status Request extension unbounded memory growth", high severity), CVE-2016-2183, CVE-2016-6303, CVE-2016-2178 and CVE-2016-6306. (<PERSON><PERSON><PERSON>) [#8714](https://github.com/nodejs/node/pull/8714)
  - Upgrade to 1.0.2j, fixes a defect included in 1.0.2i resulting in a crash when using CRLs, CVE-2016-7052. (<PERSON><PERSON><PERSON>) [#8786](https://github.com/nodejs/node/pull/8786)
  - Remove support for loading dynamic third-party engine modules. An attacker may be able to hide malicious code to be inserted into Node.js at runtime by masquerading as one of the dynamic engine modules. Originally reported by <PERSON> (Skype). (<PERSON> <PERSON>ordhuis) [nodejs/node-private#70](https://github.com/nodejs/node-private/pull/70)
- **http**: CVE-2016-5325 - Properly validate for allowable characters in the `reason` argument in `ServerResponse#writeHead()`. Fixes a possible response splitting attack vector. This introduces a new case where `throw` may occur when configuring HTTP responses, users should already be adopting try/catch here. Originally reported independently by Evan Lucas and Romain Gaucher. (Evan Lucas) [nodejs/node-private#46](https://github.com/nodejs/node-private/pull/46)

Semver Patch:

- **buffer**: Zero-fill excess bytes in new `Buffer` objects created with `Buffer.concat()` while providing a `totalLength` parameter that exceeds the total length of the original `Buffer` objects being concatenated. (Сковорода Никита Андреевич) [nodejs/node-private#65](https://github.com/nodejs/node-private/pull/65)
- **tls**: CVE-2016-7099 - Fix invalid wildcard certificate validation check whereby a TLS server may be able to serve an invalid wildcard certificate for its hostname due to improper validation of `*.` in the wildcard string. Originally reported by Alexander Minozhenko and James Bunton (Atlassian). (Ben Noordhuis) [nodejs/node-private#63](https://github.com/nodejs/node-private/pull/63)

### Commits

- [[`93b10fbec2`](https://github.com/nodejs/node/commit/93b10fbec2)] - **buffer**: zero-fill uninitialized bytes in .concat() (Сковорода Никита Андреевич) [nodejs/node-private#65](https://github.com/nodejs/node-private/pull/65)
- [[`c214e8847d`](https://github.com/nodejs/node/commit/c214e8847d)] - **crypto**: don't build hardware engines (Ben Noordhuis) [nodejs/node-private#70](https://github.com/nodejs/node-private/pull/70)
- [[`af9dda152c`](https://github.com/nodejs/node/commit/af9dda152c)] - **deps**: add -no_rand_screen to openssl s_client (Shigeki Ohtsu) [nodejs/node#1836](https://github.com/nodejs/node/pull/1836)
- [[`6bb9749c33`](https://github.com/nodejs/node/commit/6bb9749c33)] - **deps**: fix asm build error of openssl in x86_win32 (Shigeki Ohtsu) [nodejs/node#1389](https://github.com/nodejs/node/pull/1389)
- [[`5176a8ad57`](https://github.com/nodejs/node/commit/5176a8ad57)] - **deps**: fix openssl assembly error on ia32 win32 (Fedor Indutny) [nodejs/node#1389](https://github.com/nodejs/node/pull/1389)
- [[`aa9ed60a51`](https://github.com/nodejs/node/commit/aa9ed60a51)] - **deps**: copy all openssl header files to include dir (Shigeki Ohtsu) [#8786](https://github.com/nodejs/node/pull/8786)
- [[`0c74e2ad35`](https://github.com/nodejs/node/commit/0c74e2ad35)] - **deps**: upgrade openssl sources to 1.0.2j (Shigeki Ohtsu) [#8786](https://github.com/nodejs/node/pull/8786)
- [[`8f3d6760cf`](https://github.com/nodejs/node/commit/8f3d6760cf)] - **deps**: update openssl asm and asm_obsolete files (Shigeki Ohtsu) [#8714](https://github.com/nodejs/node/pull/8714)
- [[`e8f29e2ba8`](https://github.com/nodejs/node/commit/e8f29e2ba8)] - **deps**: add -no_rand_screen to openssl s_client (Shigeki Ohtsu) [nodejs/node#1836](https://github.com/nodejs/node/pull/1836)
- [[`01cf5b0ae7`](https://github.com/nodejs/node/commit/01cf5b0ae7)] - **deps**: fix asm build error of openssl in x86_win32 (Shigeki Ohtsu) [nodejs/node#1389](https://github.com/nodejs/node/pull/1389)
- [[`19ae4e8ae1`](https://github.com/nodejs/node/commit/19ae4e8ae1)] - **deps**: fix openssl assembly error on ia32 win32 (Fedor Indutny) [nodejs/node#1389](https://github.com/nodejs/node/pull/1389)
- [[`cbed5e64be`](https://github.com/nodejs/node/commit/cbed5e64be)] - **deps**: copy all openssl header files to include dir (Shigeki Ohtsu) [#8714](https://github.com/nodejs/node/pull/8714)
- [[`e7fdace18f`](https://github.com/nodejs/node/commit/e7fdace18f)] - **deps**: upgrade openssl sources to 1.0.2i (Shigeki Ohtsu) [#8714](https://github.com/nodejs/node/pull/8714)
- [[`b5c57ff772`](https://github.com/nodejs/node/commit/b5c57ff772)] - **http**: check reason chars in writeHead (Evan Lucas) [nodejs/node-private#46](https://github.com/nodejs/node-private/pull/46)
- [[`3ff82deb2c`](https://github.com/nodejs/node/commit/3ff82deb2c)] - **lib**: make tls.checkServerIdentity() more strict (Ben Noordhuis) [nodejs/node-private#63](https://github.com/nodejs/node-private/pull/63)
- [[`7c696e201a`](https://github.com/nodejs/node/commit/7c696e201a)] - **openssl**: fix keypress requirement in apps on win32 (Shigeki Ohtsu) [nodejs/node#1389](https://github.com/nodejs/node/pull/1389)
- [[`44e5776c0f`](https://github.com/nodejs/node/commit/44e5776c0f)] - **openssl**: fix keypress requirement in apps on win32 (Shigeki Ohtsu) [nodejs/node#1389](https://github.com/nodejs/node/pull/1389)
- [[`c7a601c090`](https://github.com/nodejs/node/commit/c7a601c090)] - **test**: remove openssl options of -no\_\<prot> (Shigeki Ohtsu) [#8714](https://github.com/nodejs/node/pull/8714)

Windows 32-bit Installer: https://nodejs.org/dist/v4.6.0/node-v4.6.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v4.6.0/node-v4.6.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v4.6.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v4.6.0/win-x64/node.exe \
Mac OS X 64-bit Installer: https://nodejs.org/dist/v4.6.0/node-v4.6.0.pkg \
Mac OS X 64-bit Binary: https://nodejs.org/dist/v4.6.0/node-v4.6.0-darwin-x64.tar.gz \
Linux 32-bit Binary: https://nodejs.org/dist/v4.6.0/node-v4.6.0-linux-x86.tar.xz \
Linux 64-bit Binary: https://nodejs.org/dist/v4.6.0/node-v4.6.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v4.6.0/node-v4.6.0-linux-ppc64le.tar.xz \
SmartOS 32-bit Binary: https://nodejs.org/dist/v4.6.0/node-v4.6.0-sunos-x86.tar.xz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v4.6.0/node-v4.6.0-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v4.6.0/node-v4.6.0-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v4.6.0/node-v4.6.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v4.6.0/node-v4.6.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v4.6.0/node-v4.6.0.tar.gz \
Other release files: https://nodejs.org/dist/v4.6.0/ \
Documentation: https://nodejs.org/docs/v4.6.0/api/

Shasums (GPG signing hash: SHA512, file hash: SHA256):

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA1

525ab42c767525edb7e512c600dedb20d826a6f58e1d6d1b774651a1c782a267  node-v4.6.0-darwin-x64.tar.gz
3c728c25b541fd8b88826568e7867098658df7c45d2389b60877c093a9803bd0  node-v4.6.0-darwin-x64.tar.xz
5eb4b4324d72297066b4b8c91d0b1e7c82cabde9986c986682be66202f37176b  node-v4.6.0-headers.tar.gz
862ce573bcfd592ea0c24861c0097bd23ca842d263e03f5dfa1ce08be888f20f  node-v4.6.0-headers.tar.xz
bf03e7384b727bc80c0c59cf38ba5704d83faa7f455f40fa62a67c8331dde7d6  node-v4.6.0-linux-arm64.tar.gz
7683e664b648c4ec3f86935f4b4f9fbf56f19d171e1e29d5adf687fc4c392b5b  node-v4.6.0-linux-arm64.tar.xz
e7db1c612eb9dd55e3ff246bfa7c35f0b87664e6e2bc7b32891de8cc1e48f5a7  node-v4.6.0-linux-armv6l.tar.gz
766d10a73886bbe1a3abd4b78563a825408cab7e116e590f1bbdc9b88cc3aa09  node-v4.6.0-linux-armv6l.tar.xz
9e46082bef5b521afd483532c8d3715f33d1d4302b7980b904bea3182817275f  node-v4.6.0-linux-armv7l.tar.gz
def976771b4a2a4488b87a06c8295ffea55671f7f42df13e3718341d28bf2d40  node-v4.6.0-linux-armv7l.tar.xz
2aa9518ea637cc06877a01c40d4608cf9a7f1588000cf3e550e4ab24c170aee6  node-v4.6.0-linux-ppc64le.tar.gz
b06c39da4fae47e2d204cae183425a3a77849944c5be47c5807f4f08cef51f64  node-v4.6.0-linux-ppc64le.tar.xz
ee77fb6a1dfbe166c9faee25b4f110af25723c64b0abcb9085507b8445fa2e7b  node-v4.6.0-linux-ppc64.tar.gz
e35955a846c1082e1681fdcbf488a66e43f56fb0aa7205b86a4aa0ce69dfb1eb  node-v4.6.0-linux-ppc64.tar.xz
acf08148cecf245f28126122ac9128ff9909f00938b18d80fc0b92648d1c98a8  node-v4.6.0-linux-x64.tar.gz
a77ceb75a05984153304ad0f09b11d234ca54a67714ba575b52e4298df0343d1  node-v4.6.0-linux-x64.tar.xz
9aab75618de0dca640d747aa25073cbb5a01342dd8aa177df8112e26a39541f4  node-v4.6.0-linux-x86.tar.gz
8994ee2c180a97fc4280bfb390444a4bcb2629290aa8243e7ab6271efab593f4  node-v4.6.0-linux-x86.tar.xz
0359c50c5d7e887c7f17d7ea4f42b1776ac8df263c6471bf8054b5c9f3d42a67  node-v4.6.0.pkg
e9a02da71d0cd6a1874f4a7d227dfcbe6ab9492eba419b5c9a83c8c95065195f  node-v4.6.0-sunos-x64.tar.gz
8ea3d2887b4850fb92f75573f30bbb257b7cd11f71cda12becc34868c535acf8  node-v4.6.0-sunos-x64.tar.xz
f8536a25629ef1ad3228b2d712e2fa43bf66980673d3cdf469da37c0407e9633  node-v4.6.0-sunos-x86.tar.gz
5750a8256356f43c6b80854b7c6ce46d6933e64cf5f2efecdf4841e4fe582a28  node-v4.6.0-sunos-x86.tar.xz
0838f12e329edb252e6e6baddca85632bf5ff2ec900e737e88f9bf9b38946b1b  node-v4.6.0.tar.gz
42910dbd34e49bfc40580e06753947c30d31101455a38e9f0343a23d67c0c694  node-v4.6.0.tar.xz
0c6509c13cfa9795f08b9bf694383de7e4d93cde14a9e8979a92f21736e19498  node-v4.6.0-win-x64.7z
0782bd50251c2a159fba5b874c56fb4a6680f454cc16892cee8e62d17b7d6f60  node-v4.6.0-win-x64.zip
413f98f2b765fe862ff6971724c3f265dbfe5a2cb865dd1894b4447426542c91  node-v4.6.0-win-x86.7z
13a5dcb90a8397f62c55945b65cb1c7b9d7576af3cbfc8d9cb67f72edcf68201  node-v4.6.0-win-x86.zip
80926b2df6e7efc8adda2e1fcb6328b99fe878d728cf93f39b0c710adc1bcb35  node-v4.6.0-x64.msi
5f91bf57512c1fa96d016c8f6236c689998ed926faa13aaf2170154342ca915a  node-v4.6.0-x86.msi
7564472c672e729a724ffe890ba06ec318c9e311684516a25a47b3f1e549504e  win-x64/node.exe
24178152fc3a99b9b83a1620897c5624cb7e0ba0544da38e18ca0cde807435d4  win-x64/node.lib
44dbbec125f3c4804ed5d002628c7ddb8e51cd352af0542b9edebcfd718967b5  win-x64/node_pdb.7z
b92e5e5031f19f201ec4568d7761c263af9a20e02b34bdd9e5f7191750aee3fb  win-x64/node_pdb.zip
7c9287cec4379082393d85af919a36a3512aa6bfcbf3deba3261a472580041f8  win-x86/node.exe
7d5988939f1567a4d7180010f49ec36b8d3897a8eccb78e461a774d8d2de614e  win-x86/node.lib
98f955f69195f12ec429e4cff629c650a6b1dcb43a1c18cef9cf79a11067c88d  win-x86/node_pdb.7z
0d0faf3bf0fcf50a943d8202d24d8eb8bb0695ea99498360c1a8a745c7811fd7  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQEcBAEBAgAGBQJX6xhwAAoJEMJzeS99g1RdoksH/139ljOj+Vjc0nVNRn5m2KxC
3gldiKMaNBeefV9JOA3tG1fei3KPvO/PRHVCYogQO8IEEpJ5Yer+zQpsLOg/xGDR
nsg9xOBupnSlUAfALilWhkDkBDgcauuiII3tP98GjDaSS+cH6Pctt08l2XTCROYk
YThc0nonmobDGSsHVf4biv+ySMocmpZGU0h10xS2lRVlrxMpEzsxVuCSv52fRDKy
gD0Hf9ZSFi9i1MIxKOIolYpdIVmrS29c6J0LbjW2WcTk21jIOENXEk1uEl71OWHD
plT4hauehu/3a89FcqsOu10MqLStFuEm0T1CXtmn4/Vm2FhJnZfdiCDT1YABD00=
=LyAk
-----END PGP SIGNATURE-----

```
