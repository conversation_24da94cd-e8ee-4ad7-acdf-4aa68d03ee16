---
date: '2017-03-01T03:05:14.302Z'
category: release
title: Node v7.7.0 (Current)
layout: blog-post
author: <PERSON><PERSON> <PERSON><PERSON>
---

### Note about this Release

**This release contains a bug that will prevent all native modules from loading.**

A patch release (7.7.1) will be coming shortly.

Please refer to [this issue](https://github.com/nodejs/node/issues/11628) for more information on the problem.

### Notable changes

- **child_process**: spawnSync() exit code now is null when the child is killed via signal (cjihrig) [#11288](https://github.com/nodejs/node/pull/11288)
- **http**: new functions to access the headers for an outgoing HTTP message (<PERSON>) [#11562](https://github.com/nodejs/node/pull/11562)
- **lib**: deprecate node --debug at runtime (<PERSON>) [#11275](https://github.com/nodejs/node/pull/11275)
- **tls**: new tls.TLSSocket() supports sec ctx options (<PERSON>) [#11005](https://github.com/nodejs/node/pull/11005)
- **url**: adding URL.prototype.toJSON support (Micha<PERSON>asso) [#11236](https://github.com/nodejs/node/pull/11236)
- **doc**: items in the API documentation may now have changelogs (Anna Henningsen) [#11489](https://github.com/nodejs/node/pull/11489)
- **crypto**: adding support for OPENSSL_CONF again (<PERSON>) [#11006](https://github.com/nodejs/node/pull/11006)
- **src**: adding support for trace-event tracing (misterpoe) [#11106](https://github.com/nodejs/node/pull/11106)

### Commits

- [[`18599fc3d7`](https://github.com/nodejs/node/commit/18599fc3d7)] - doc/url: various improvements to WHATWG API (Timothy Gu)
- [[`e7d37a3f09`](https://github.com/nodejs/node/commit/e7d37a3f09)] - tools/doc: add more intrinsic and custom types (Timothy Gu)
- [[`6bcc841786`](https://github.com/nodejs/node/commit/6bcc841786)] - **assert**: apply minor refactoring (Rich Trott) [#11511](https://github.com/nodejs/node/pull/11511)
- [[`6a2f330dbd`](https://github.com/nodejs/node/commit/6a2f330dbd)] - **assert**: remove unneeded condition (Rich Trott) [#11314](https://github.com/nodejs/node/pull/11314)
- [[`0762482339`](https://github.com/nodejs/node/commit/0762482339)] - **assert**: unlock the assert API (Rich Trott) [#11304](https://github.com/nodejs/node/pull/11304)
- [[`842ac583f6`](https://github.com/nodejs/node/commit/842ac583f6)] - **benchmark**: add url.domainTo\*() (Timothy Gu) [#11464](https://github.com/nodejs/node/pull/11464)
- [[`3951bd9ac1`](https://github.com/nodejs/node/commit/3951bd9ac1)] - **benchmark**: strip BOM in dgram/bind-params (Anna Henningsen) [#11479](https://github.com/nodejs/node/pull/11479)
- [[`e1573b9fb7`](https://github.com/nodejs/node/commit/e1573b9fb7)] - **benchmark**: add dgram bind(+/- params) benchmark (Vse Mozhet Byt) [#11313](https://github.com/nodejs/node/pull/11313)
- [[`48f6660d78`](https://github.com/nodejs/node/commit/48f6660d78)] - **benchmark**: fix timer display in progress output (Brian White) [#11235](https://github.com/nodejs/node/pull/11235)
- [[`5a81031fd8`](https://github.com/nodejs/node/commit/5a81031fd8)] - **benchmark**: clean up legacy url benchmarks (Joyee Cheung)
- [[`7e37628c51`](https://github.com/nodejs/node/commit/7e37628c51)] - **benchmark**: add url/url-searchparams-sort.js (Timothy Gu)
- [[`4ffad094ba`](https://github.com/nodejs/node/commit/4ffad094ba)] - **buffer**: refactor slowToString (James M Snell) [#11358](https://github.com/nodejs/node/pull/11358)
- [[`d08a8e68e8`](https://github.com/nodejs/node/commit/d08a8e68e8)] - **buffer**: avoid use of arguments (James M Snell) [#11358](https://github.com/nodejs/node/pull/11358)
- [[`4408437796`](https://github.com/nodejs/node/commit/4408437796)] - **build**: add rule to clean addon tests build (Joyee Cheung) [#11519](https://github.com/nodejs/node/pull/11519)
- [[`8d323bb91a`](https://github.com/nodejs/node/commit/8d323bb91a)] - **build**: fail on CI if leftover processes (Rich Trott) [#11269](https://github.com/nodejs/node/pull/11269)
- [[`d4a8631bd1`](https://github.com/nodejs/node/commit/d4a8631bd1)] - **build**: fix newlines in addon build output (Brian White) [#11466](https://github.com/nodejs/node/pull/11466)
- [[`bc9c381027`](https://github.com/nodejs/node/commit/bc9c381027)] - **build**: add code coverage to make (Wayne Andrews) [#10856](https://github.com/nodejs/node/pull/10856)
- [[`9c45758cdf`](https://github.com/nodejs/node/commit/9c45758cdf)] - **build**: fix building with ninja on linux (Kenan Yildirim) [#11348](https://github.com/nodejs/node/pull/11348)
- [[`86a647899f`](https://github.com/nodejs/node/commit/86a647899f)] - **build**: don't rebuild test/gc add-on unnecessarily (Ben Noordhuis) [#11311](https://github.com/nodejs/node/pull/11311)
- [[`c942e2037c`](https://github.com/nodejs/node/commit/c942e2037c)] - **child_process**: refactor internal/child_process.js (Arseniy Maximov) [#11366](https://github.com/nodejs/node/pull/11366)
- [[`0240eb99a2`](https://github.com/nodejs/node/commit/0240eb99a2)] - **child_process**: remove empty if condition (cjihrig) [#11427](https://github.com/nodejs/node/pull/11427)
- [[`60fc567952`](https://github.com/nodejs/node/commit/60fc567952)] - **child_process**: move anonymous class to top level (Jackson Tian) [#11147](https://github.com/nodejs/node/pull/11147)
- [[`58e2517fc0`](https://github.com/nodejs/node/commit/58e2517fc0)] - **child_process**: exit spawnSync with null on signal (cjihrig) [#11288](https://github.com/nodejs/node/pull/11288)
- [[`4b4bc13758`](https://github.com/nodejs/node/commit/4b4bc13758)] - **cluster**: properly handle --inspect-{brk,port} (Ali Ijaz Sheikh) [#11386](https://github.com/nodejs/node/pull/11386)
- [[`570c5e1da8`](https://github.com/nodejs/node/commit/570c5e1da8)] - **(SEMVER-MINOR)** **crypto**: support OPENSSL_CONF again (Sam Roberts) [#11006](https://github.com/nodejs/node/pull/11006)
- [[`d4000e73ed`](https://github.com/nodejs/node/commit/d4000e73ed)] - **deps**: cherry-pick 7c982e7 from V8 upstream (Jaideep Bajwa) [#11263](https://github.com/nodejs/node/pull/11263)
- [[`bd4ccc892c`](https://github.com/nodejs/node/commit/bd4ccc892c)] - **src**: add tracing controller (misterpoe) [#11106](https://github.com/nodejs/node/pull/11106)
- [[`aef67cfe39`](https://github.com/nodejs/node/commit/aef67cfe39)] - **dgram**: fix possibly deoptimizing use of arguments (Vse Mozhet Byt) [#11242](https://github.com/nodejs/node/pull/11242)
- [[`662b0c31ce`](https://github.com/nodejs/node/commit/662b0c31ce)] - **dns**: avoid use of arguments (James M Snell) [#11359](https://github.com/nodejs/node/pull/11359)
- [[`fedf26b235`](https://github.com/nodejs/node/commit/fedf26b235)] - **doc**: update V8 debugger doc to mention --inspect-brk (James Ide) [#11495](https://github.com/nodejs/node/pull/11495)
- [[`1c7f221ef5`](https://github.com/nodejs/node/commit/1c7f221ef5)] - **doc**: adding deprecations.md (Italo A. Casas) [#11621](https://github.com/nodejs/node/pull/11621)
- [[`90bdf16507`](https://github.com/nodejs/node/commit/90bdf16507)] - **doc**: link to readable and writeable stream section (Sebastian Van Sande) [#11517](https://github.com/nodejs/node/pull/11517)
- [[`3b66ccf0ff`](https://github.com/nodejs/node/commit/3b66ccf0ff)] - **doc**: document clientRequest.aborted (Zach Bjornson) [#11544](https://github.com/nodejs/node/pull/11544)
- [[`128f812157`](https://github.com/nodejs/node/commit/128f812157)] - **doc**: argument types for assert methods (Amelia Clarke) [#11548](https://github.com/nodejs/node/pull/11548)
- [[`b1b6b8b730`](https://github.com/nodejs/node/commit/b1b6b8b730)] - **doc**: add changelogs for buffer (Anna Henningsen) [#11489](https://github.com/nodejs/node/pull/11489)
- [[`597945136e`](https://github.com/nodejs/node/commit/597945136e)] - **doc**: add changelogs for v8 (Anna Henningsen) [#11489](https://github.com/nodejs/node/pull/11489)
- [[`b01fd8ce3a`](https://github.com/nodejs/node/commit/b01fd8ce3a)] - **doc**: fix sorting in API references (Vse Mozhet Byt) [#11529](https://github.com/nodejs/node/pull/11529)
- [[`56cd1932c1`](https://github.com/nodejs/node/commit/56cd1932c1)] - **doc**: note message event listeners ref IPC channels (Diego Rodríguez Baquero) [#11494](https://github.com/nodejs/node/pull/11494)
- [[`47034e12ad`](https://github.com/nodejs/node/commit/47034e12ad)] - **doc**: change broken fg(1) links to fg(1p) (Karan Thakkar) [#11504](https://github.com/nodejs/node/pull/11504)
- [[`47dc5662f3`](https://github.com/nodejs/node/commit/47dc5662f3)] - **doc**: add changelogs for zlib (Anna Henningsen) [#11489](https://github.com/nodejs/node/pull/11489)
- [[`4d122700ab`](https://github.com/nodejs/node/commit/4d122700ab)] - **doc**: add changelogs for vm (Anna Henningsen) [#11489](https://github.com/nodejs/node/pull/11489)
- [[`b868468942`](https://github.com/nodejs/node/commit/b868468942)] - **doc**: add changelogs for util (Anna Henningsen) [#11489](https://github.com/nodejs/node/pull/11489)
- [[`93e7639c12`](https://github.com/nodejs/node/commit/93e7639c12)] - **doc**: add changelogs for url (Anna Henningsen) [#11489](https://github.com/nodejs/node/pull/11489)
- [[`f7d59e5568`](https://github.com/nodejs/node/commit/f7d59e5568)] - **doc**: add changelogs for tls (Anna Henningsen) [#11489](https://github.com/nodejs/node/pull/11489)
- [[`fc53547ed4`](https://github.com/nodejs/node/commit/fc53547ed4)] - **doc**: add changelogs for stream (Anna Henningsen) [#11489](https://github.com/nodejs/node/pull/11489)
- [[`c373e07a09`](https://github.com/nodejs/node/commit/c373e07a09)] - **doc**: add changelogs for repl (Anna Henningsen) [#11489](https://github.com/nodejs/node/pull/11489)
- [[`962d27dbde`](https://github.com/nodejs/node/commit/962d27dbde)] - **doc**: add changelogs for readline (Anna Henningsen) [#11489](https://github.com/nodejs/node/pull/11489)
- [[`7c609dc30a`](https://github.com/nodejs/node/commit/7c609dc30a)] - **doc**: add changelogs for querystring (Anna Henningsen) [#11489](https://github.com/nodejs/node/pull/11489)
- [[`6285ff2275`](https://github.com/nodejs/node/commit/6285ff2275)] - **doc**: add changelogs for punycode (Anna Henningsen) [#11489](https://github.com/nodejs/node/pull/11489)
- [[`df30bc869a`](https://github.com/nodejs/node/commit/df30bc869a)] - **doc**: add changelogs for process (Anna Henningsen) [#11489](https://github.com/nodejs/node/pull/11489)
- [[`c1477b9bd3`](https://github.com/nodejs/node/commit/c1477b9bd3)] - **doc**: add changelogs for path (Anna Henningsen) [#11489](https://github.com/nodejs/node/pull/11489)
- [[`ac10a3b306`](https://github.com/nodejs/node/commit/ac10a3b306)] - **doc**: add changelogs for os (Anna Henningsen) [#11489](https://github.com/nodejs/node/pull/11489)
- [[`3183397c8a`](https://github.com/nodejs/node/commit/3183397c8a)] - **doc**: add changelogs for net (Anna Henningsen) [#11489](https://github.com/nodejs/node/pull/11489)
- [[`6cc8f19e99`](https://github.com/nodejs/node/commit/6cc8f19e99)] - **doc**: add changelogs for http (Anna Henningsen) [#11489](https://github.com/nodejs/node/pull/11489)
- [[`f0cee80de7`](https://github.com/nodejs/node/commit/f0cee80de7)] - **doc**: add changelogs for fs (Anna Henningsen) [#11489](https://github.com/nodejs/node/pull/11489)
- [[`354161d804`](https://github.com/nodejs/node/commit/354161d804)] - **doc**: add changelogs for events (Anna Henningsen) [#11489](https://github.com/nodejs/node/pull/11489)
- [[`4f936014ff`](https://github.com/nodejs/node/commit/4f936014ff)] - **doc**: add changelogs for dns (Anna Henningsen) [#11489](https://github.com/nodejs/node/pull/11489)
- [[`5bc9349d40`](https://github.com/nodejs/node/commit/5bc9349d40)] - **doc**: add changelogs for dgram (Anna Henningsen) [#11489](https://github.com/nodejs/node/pull/11489)
- [[`e23598d09f`](https://github.com/nodejs/node/commit/e23598d09f)] - **doc**: add changelogs for crypto (Anna Henningsen) [#11489](https://github.com/nodejs/node/pull/11489)
- [[`296e22adce`](https://github.com/nodejs/node/commit/296e22adce)] - **doc**: add changelogs for console (Anna Henningsen) [#11489](https://github.com/nodejs/node/pull/11489)
- [[`de83e215cb`](https://github.com/nodejs/node/commit/de83e215cb)] - **doc**: add changelogs for cluster (Anna Henningsen) [#11489](https://github.com/nodejs/node/pull/11489)
- [[`5d4e638e34`](https://github.com/nodejs/node/commit/5d4e638e34)] - **doc**: add changelogs for cli (Anna Henningsen) [#11489](https://github.com/nodejs/node/pull/11489)
- [[`ad1ad4d06d`](https://github.com/nodejs/node/commit/ad1ad4d06d)] - **doc**: add changelogs for child_process (Anna Henningsen) [#11489](https://github.com/nodejs/node/pull/11489)
- [[`42413b611b`](https://github.com/nodejs/node/commit/42413b611b)] - **doc**: add changelogs for assert (Anna Henningsen) [#11489](https://github.com/nodejs/node/pull/11489)
- [[`d3013678fb`](https://github.com/nodejs/node/commit/d3013678fb)] - **doc**: change STYLE-GUIDE to STYLE_GUIDE (Dean Coakley) [#11460](https://github.com/nodejs/node/pull/11460)
- [[`c5ff76dadf`](https://github.com/nodejs/node/commit/c5ff76dadf)] - **doc**: restrict the ES.Next features usage in tests (DavidCai) [#11452](https://github.com/nodejs/node/pull/11452)
- [[`98eb18ba3f`](https://github.com/nodejs/node/commit/98eb18ba3f)] - **doc**: add comment for net.Server's error event (QianJin2013) [#11136](https://github.com/nodejs/node/pull/11136)
- [[`20d86db9bb`](https://github.com/nodejs/node/commit/20d86db9bb)] - **doc**: add version meta for SSL_CERT_DIR/FILE (Sam Roberts) [#11007](https://github.com/nodejs/node/pull/11007)
- [[`66f9506c63`](https://github.com/nodejs/node/commit/66f9506c63)] - **doc**: improve test/README.md (Joyee Cheung) [#11237](https://github.com/nodejs/node/pull/11237)
- [[`5d12fd9a4b`](https://github.com/nodejs/node/commit/5d12fd9a4b)] - **doc**: add benchmark/README.md and fix guide (Joyee Cheung) [#11237](https://github.com/nodejs/node/pull/11237)
- [[`22a6eddc5c`](https://github.com/nodejs/node/commit/22a6eddc5c)] - **doc**: move benchmark/README.md to doc/guides (Joyee Cheung) [#11237](https://github.com/nodejs/node/pull/11237)
- [[`12cf359423`](https://github.com/nodejs/node/commit/12cf359423)] - **doc**: add comment for net.Server.listen IPv6 '::' (QianJin2013) [#11134](https://github.com/nodejs/node/pull/11134)
- [[`83fe819131`](https://github.com/nodejs/node/commit/83fe819131)] - **doc**: add STYLE_GUIDE (moved from nodejs/docs) (Gibson Fahnestock) [#11321](https://github.com/nodejs/node/pull/11321)
- [[`ef1731d972`](https://github.com/nodejs/node/commit/ef1731d972)] - **doc**: add missing function to test common doc (Rich Trott) [#11382](https://github.com/nodejs/node/pull/11382)
- [[`c3c874f514`](https://github.com/nodejs/node/commit/c3c874f514)] - **doc**: dns examples implied string args were arrays (Sam Roberts) [#11350](https://github.com/nodejs/node/pull/11350)
- [[`5f1a568ccc`](https://github.com/nodejs/node/commit/5f1a568ccc)] - **doc**: describe when stdout/err is sync (Sam Roberts) [#10884](https://github.com/nodejs/node/pull/10884)
- [[`5a2db15736`](https://github.com/nodejs/node/commit/5a2db15736)] - **doc**: add documentation for url.format(URL\[, options\]); (James M Snell)
- [[`4d7c9427c1`](https://github.com/nodejs/node/commit/4d7c9427c1)] - **doc**: synchronize + update \_toc.md and all.md (Vse Mozhet Byt) [#11206](https://github.com/nodejs/node/pull/11206)
- [[`6a45265e81`](https://github.com/nodejs/node/commit/6a45265e81)] - **doc**: update code examples in domain.md (Vse Mozhet Byt) [#11110](https://github.com/nodejs/node/pull/11110)
- [[`89b66dc636`](https://github.com/nodejs/node/commit/89b66dc636)] - **doc,test**: args to `buffer.copy` can be Uint8Arrays (Anna Henningsen) [#11486](https://github.com/nodejs/node/pull/11486)
- [[`4f6a3d38c3`](https://github.com/nodejs/node/commit/4f6a3d38c3)] - **domain,events**: support non-object 'error' argument (Ben Noordhuis) [#11438](https://github.com/nodejs/node/pull/11438)
- [[`214a39294a`](https://github.com/nodejs/node/commit/214a39294a)] - **(SEMVER-MINOR)** **errors**: add internal/errors.js (James M Snell) [#11220](https://github.com/nodejs/node/pull/11220)
- [[`758126301e`](https://github.com/nodejs/node/commit/758126301e)] - **fs**: improve performance for sync stat() functions (Brian White) [#11522](https://github.com/nodejs/node/pull/11522)
- [[`3e8d43d165`](https://github.com/nodejs/node/commit/3e8d43d165)] - **http**: add new functions to OutgoingMessage (Brian White) [#11562](https://github.com/nodejs/node/pull/11562)
- [[`614742b67f`](https://github.com/nodejs/node/commit/614742b67f)] - **(SEMVER-MINOR)** **lib**: deprecate node --debug at runtime (Josh Gavant) [#11275](https://github.com/nodejs/node/pull/11275)
- [[`a710167c79`](https://github.com/nodejs/node/commit/a710167c79)] - **lib**: rename kMaxCallbacksUntilQueueIsShortened (JungMinu) [#11473](https://github.com/nodejs/node/pull/11473)
- [[`61e1af2155`](https://github.com/nodejs/node/commit/61e1af2155)] - **lib**: remove unnecessary assignments with \_extend (Sakthipriyan Vairamani (thefourtheye)) [#11364](https://github.com/nodejs/node/pull/11364)
- [[`d1549bf8d9`](https://github.com/nodejs/node/commit/d1549bf8d9)] - **lib**: add constant kMaxCallbacksUntilQueueIsShortened (Daniel Bevenius) [#11199](https://github.com/nodejs/node/pull/11199)
- [[`3afe90dc9b`](https://github.com/nodejs/node/commit/3afe90dc9b)] - **net**: prefer === to == (Arseniy Maximov) [#11513](https://github.com/nodejs/node/pull/11513)
- [[`db06c7311b`](https://github.com/nodejs/node/commit/db06c7311b)] - **os**: improve loadavg() performance (Brian White) [#11516](https://github.com/nodejs/node/pull/11516)
- [[`fe7a722468`](https://github.com/nodejs/node/commit/fe7a722468)] - **process**: fix typo in comments (levsthings) [#11503](https://github.com/nodejs/node/pull/11503)
- [[`54e1f0c219`](https://github.com/nodejs/node/commit/54e1f0c219)] - **process**: improve memoryUsage() performance (Brian White) [#11497](https://github.com/nodejs/node/pull/11497)
- [[`fb85f5049e`](https://github.com/nodejs/node/commit/fb85f5049e)] - **src**: clean up MaybeStackBuffer (Timothy Gu) [#11464](https://github.com/nodejs/node/pull/11464)
- [[`beda32675f`](https://github.com/nodejs/node/commit/beda32675f)] - **src**: don't assume v8::Local is using-declared (Timothy Gu) [#11464](https://github.com/nodejs/node/pull/11464)
- [[`64a92565e0`](https://github.com/nodejs/node/commit/64a92565e0)] - **src**: update http-parser link (Daniel Bevenius) [#11477](https://github.com/nodejs/node/pull/11477)
- [[`539e83a820`](https://github.com/nodejs/node/commit/539e83a820)] - **src**: remove usage of deprecated debug API (Yang Guo) [#11437](https://github.com/nodejs/node/pull/11437)
- [[`8be6702539`](https://github.com/nodejs/node/commit/8be6702539)] - **(SEMVER-MINOR)** **src**: add SafeGetenv() to internal API (Sam Roberts) [#11006](https://github.com/nodejs/node/pull/11006)
- [[`7d47f27049`](https://github.com/nodejs/node/commit/7d47f27049)] - **src**: remove unused variable in node_crypto (cjihrig) [#11361](https://github.com/nodejs/node/pull/11361)
- [[`8a5c0fb0ff`](https://github.com/nodejs/node/commit/8a5c0fb0ff)] - **src**: remove unused typedef (Ben Noordhuis) [#11322](https://github.com/nodejs/node/pull/11322)
- [[`39b00349b8`](https://github.com/nodejs/node/commit/39b00349b8)] - **src, i18n**: cleanup usage of MaybeStackBuffer (Timothy Gu) [#11464](https://github.com/nodejs/node/pull/11464)
- [[`d0483ee47b`](https://github.com/nodejs/node/commit/d0483ee47b)] - **test**: change common.expectsError() signature (Rich Trott) [#11512](https://github.com/nodejs/node/pull/11512)
- [[`f193c6f996`](https://github.com/nodejs/node/commit/f193c6f996)] - **test**: favor assertions over console logging (Rich Trott) [#11547](https://github.com/nodejs/node/pull/11547)
- [[`4b05ec3b95`](https://github.com/nodejs/node/commit/4b05ec3b95)] - **test**: run test-setproctitle where supported (Howard Hellyer) [#11416](https://github.com/nodejs/node/pull/11416)
- [[`ff854834b6`](https://github.com/nodejs/node/commit/ff854834b6)] - **test**: fix flaky test-vm-timeout-rethrow (Kunal Pathak) [#11530](https://github.com/nodejs/node/pull/11530)
- [[`d7fd694cee`](https://github.com/nodejs/node/commit/d7fd694cee)] - **test**: remove redundant additional url tests (Joyee Cheung) [#11439](https://github.com/nodejs/node/pull/11439)
- [[`e92ddd46bb`](https://github.com/nodejs/node/commit/e92ddd46bb)] - **test**: synchronize WPT url test data (Joyee Cheung) [#11439](https://github.com/nodejs/node/pull/11439)
- [[`4109e0edc4`](https://github.com/nodejs/node/commit/4109e0edc4)] - **test**: remove WHATWG URL test data file extension (Joyee Cheung) [#11439](https://github.com/nodejs/node/pull/11439)
- [[`ecb3a7e933`](https://github.com/nodejs/node/commit/ecb3a7e933)] - **(SEMVER-MINOR)** **test**: make tls-socket-default-options tests run (Sam Roberts) [#11005](https://github.com/nodejs/node/pull/11005)
- [[`f5b4849208`](https://github.com/nodejs/node/commit/f5b4849208)] - **test**: test bottom-up merge sort in URLSearchParams (Daijiro Wachi) [#11399](https://github.com/nodejs/node/pull/11399)
- [[`ff927b2cf8`](https://github.com/nodejs/node/commit/ff927b2cf8)] - **test**: add cases for unescape & unescapeBuffer (Daijiro Wachi) [#11326](https://github.com/nodejs/node/pull/11326)
- [[`ea29d4852a`](https://github.com/nodejs/node/commit/ea29d4852a)] - **test**: use expectsError in test-debug-agent.js (Arseniy Maximov) [#11410](https://github.com/nodejs/node/pull/11410)
- [[`8e455a9093`](https://github.com/nodejs/node/commit/8e455a9093)] - **test**: add test for URLSearchParams inspection (Daijiro Wachi) [#11428](https://github.com/nodejs/node/pull/11428)
- [[`ae9b891a39`](https://github.com/nodejs/node/commit/ae9b891a39)] - **test**: use expectsError in require-invalid-package (Rich Trott) [#11409](https://github.com/nodejs/node/pull/11409)
- [[`91fac08c3b`](https://github.com/nodejs/node/commit/91fac08c3b)] - **test**: use common.expectsError() (Rich Trott) [#11408](https://github.com/nodejs/node/pull/11408)
- [[`46084e3270`](https://github.com/nodejs/node/commit/46084e3270)] - **test**: refactor common.expectsError() (Rich Trott) [#11381](https://github.com/nodejs/node/pull/11381)
- [[`8fdb6c24f9`](https://github.com/nodejs/node/commit/8fdb6c24f9)] - **test**: throw check in test-zlib-write-after-close (Jason Wilson) [#11482](https://github.com/nodejs/node/pull/11482)
- [[`b395ed9407`](https://github.com/nodejs/node/commit/b395ed9407)] - **test**: increase coverage of vm (DavidCai) [#11377](https://github.com/nodejs/node/pull/11377)
- [[`000b2a14c1`](https://github.com/nodejs/node/commit/000b2a14c1)] - **test**: add support for --gtest_filter (Daniel Bevenius) [#11474](https://github.com/nodejs/node/pull/11474)
- [[`34220b75e2`](https://github.com/nodejs/node/commit/34220b75e2)] - **test**: add regex check to test-module-loading (Tarang Hirani) [#11413](https://github.com/nodejs/node/pull/11413)
- [[`4509d84095`](https://github.com/nodejs/node/commit/4509d84095)] - **test**: improve coverage in test-crypto.dh (Eric Christie) [#11253](https://github.com/nodejs/node/pull/11253)
- [[`da10e2649d`](https://github.com/nodejs/node/commit/da10e2649d)] - **test**: add error checking in callback (Rich Trott) [#11446](https://github.com/nodejs/node/pull/11446)
- [[`7b8087630f`](https://github.com/nodejs/node/commit/7b8087630f)] - **test**: refactor test-http-response-splitting (Arseniy Maximov) [#11429](https://github.com/nodejs/node/pull/11429)
- [[`c37e2b7690`](https://github.com/nodejs/node/commit/c37e2b7690)] - **test**: add test cases for path (Yuta Hiroto) [#11453](https://github.com/nodejs/node/pull/11453)
- [[`a523482cca`](https://github.com/nodejs/node/commit/a523482cca)] - **test**: enhance test-common.js (Rich Trott) [#11433](https://github.com/nodejs/node/pull/11433)
- [[`1d86a9f5eb`](https://github.com/nodejs/node/commit/1d86a9f5eb)] - **test**: fix over-dependence on native promise impl (Ali Ijaz Sheikh) [#11437](https://github.com/nodejs/node/pull/11437)
- [[`b457f38e68`](https://github.com/nodejs/node/commit/b457f38e68)] - **test**: add coverage for utf8CheckIncomplete() (xiaoyu) [#11419](https://github.com/nodejs/node/pull/11419)
- [[`ca1bae6f3e`](https://github.com/nodejs/node/commit/ca1bae6f3e)] - **test**: remove unused args and comparison fix (Alexander) [#11396](https://github.com/nodejs/node/pull/11396)
- [[`8ee236f85a`](https://github.com/nodejs/node/commit/8ee236f85a)] - **test**: improve crypto coverage (樋口　彰) [#11279](https://github.com/nodejs/node/pull/11279)
- [[`add762550c`](https://github.com/nodejs/node/commit/add762550c)] - **test**: consolidate buffer.read() in a file (larissayvette) [#11297](https://github.com/nodejs/node/pull/11297)
- [[`e416967244`](https://github.com/nodejs/node/commit/e416967244)] - **test**: cases to querystring related to empty string (Daijiro Wachi) [#11329](https://github.com/nodejs/node/pull/11329)
- [[`5723087cdd`](https://github.com/nodejs/node/commit/5723087cdd)] - **test**: refactor test-dgram-membership (Rich Trott) [#11388](https://github.com/nodejs/node/pull/11388)
- [[`aea0d501d7`](https://github.com/nodejs/node/commit/aea0d501d7)] - **test**: improve message in net-connect-local-error (Rich Trott) [#11393](https://github.com/nodejs/node/pull/11393)
- [[`82882f4e90`](https://github.com/nodejs/node/commit/82882f4e90)] - **test**: cover dgram socket close during bind case (cjihrig) [#11383](https://github.com/nodejs/node/pull/11383)
- [[`f495389d67`](https://github.com/nodejs/node/commit/f495389d67)] - **test**: refactor test-tls-cert-chains-in-ca (Rich Trott) [#11367](https://github.com/nodejs/node/pull/11367)
- [[`348f2ef59f`](https://github.com/nodejs/node/commit/348f2ef59f)] - **test**: improve crypto coverage (Akito Ito) [#11280](https://github.com/nodejs/node/pull/11280)
- [[`e7978f04a4`](https://github.com/nodejs/node/commit/e7978f04a4)] - **test**: cover dgram socket close during cluster bind (cjihrig) [#11292](https://github.com/nodejs/node/pull/11292)
- [[`66081d1ddb`](https://github.com/nodejs/node/commit/66081d1ddb)] - **test**: increase coverage of buffer (DavidCai) [#11312](https://github.com/nodejs/node/pull/11312)
- [[`7aaa960f4c`](https://github.com/nodejs/node/commit/7aaa960f4c)] - **test, url**: synchronize WPT url tests (Joyee Cheung)
- [[`506a1cb03f`](https://github.com/nodejs/node/commit/506a1cb03f)] - **timer,domain**: maintain order of timer callbacks (John Barboza) [#10522](https://github.com/nodejs/node/pull/10522)
- [[`4e327708a9`](https://github.com/nodejs/node/commit/4e327708a9)] - **(SEMVER-MINOR)** **tls**: new tls.TLSSocket() supports sec ctx options (Sam Roberts) [#11005](https://github.com/nodejs/node/pull/11005)
- [[`f37ab7968e`](https://github.com/nodejs/node/commit/f37ab7968e)] - **tls**: do not crash on STARTTLS when OCSP requested (Fedor Indutny) [#10706](https://github.com/nodejs/node/pull/10706)
- [[`5f94ff6231`](https://github.com/nodejs/node/commit/5f94ff6231)] - **tls**: avoid potentially deoptimizing use of arguments (James M Snell) [#11357](https://github.com/nodejs/node/pull/11357)
- [[`0934a27c75`](https://github.com/nodejs/node/commit/0934a27c75)] - **tools**: enable unicode-bom ESLint rule (Anna Henningsen) [#11479](https://github.com/nodejs/node/pull/11479)
- [[`eea2eb9111`](https://github.com/nodejs/node/commit/eea2eb9111)] - **tools**: enable one-var-declaration-per-line ESLint rule (Michaël Zasso) [#11462](https://github.com/nodejs/node/pull/11462)
- [[`5b5dca9076`](https://github.com/nodejs/node/commit/5b5dca9076)] - **tools**: suggest python2 command in configure (Roman Reiss) [#11375](https://github.com/nodejs/node/pull/11375)
- [[`d9d541d564`](https://github.com/nodejs/node/commit/d9d541d564)] - **tools,doc**: enable changelogs for items (Anna Henningsen) [#11489](https://github.com/nodejs/node/pull/11489)
- [[`4ee9220565`](https://github.com/nodejs/node/commit/4ee9220565)] - **tty**: avoid oob warning in TTYWrap::GetWindowSize() (Dmitry Tsvettsikh) [#11454](https://github.com/nodejs/node/pull/11454)
- [[`5f10827248`](https://github.com/nodejs/node/commit/5f10827248)] - **url**: fix handling of ? in URLSearchParams creation (Timothy Gu) [#11372](https://github.com/nodejs/node/pull/11372)
- [[`72da362d6e`](https://github.com/nodejs/node/commit/72da362d6e)] - **url**: fix file state clarification in binding (Daijiro Wachi) [#11123](https://github.com/nodejs/node/pull/11123)
- [[`4366ab539f`](https://github.com/nodejs/node/commit/4366ab539f)] - **url**: implement URL.prototype.toJSON (Michaël Zasso) [#11236](https://github.com/nodejs/node/pull/11236)
- [[`8dbd562590`](https://github.com/nodejs/node/commit/8dbd562590)] - **url**: fix surrogate handling in encodeAuth() (Timothy Gu)
- [[`c25c16cc1b`](https://github.com/nodejs/node/commit/c25c16cc1b)] - **url**: add urlSearchParams.sort() (Timothy Gu)
- [[`d8cb65aa6e`](https://github.com/nodejs/node/commit/d8cb65aa6e)] - **url, test**: synchronize WPT url tests for file URL (Daijiro Wachi) [#11123](https://github.com/nodejs/node/pull/11123)
- [[`237db9c497`](https://github.com/nodejs/node/commit/237db9c497)] - **util**: cleanup internalUtil.deprecate (James M Snell) [#11450](https://github.com/nodejs/node/pull/11450)
- [[`95bee8f202`](https://github.com/nodejs/node/commit/95bee8f202)] - **util**: eliminate unnecessary exports (James M Snell) [#11451](https://github.com/nodejs/node/pull/11451)
- [[`3bdac54e67`](https://github.com/nodejs/node/commit/3bdac54e67)] - **util**: use ES2015+ Object.is to check negative zero (Shinnosuke Watanabe) [#11332](https://github.com/nodejs/node/pull/11332)
- [[`3d133ebd3d`](https://github.com/nodejs/node/commit/3d133ebd3d)] - **util, debugger**: remove internalUtil.error (James M Snell) [#11448](https://github.com/nodejs/node/pull/11448)
- [[`f55c628b2a`](https://github.com/nodejs/node/commit/f55c628b2a)] - **vm**: refactor vm module (James M Snell) [#11392](https://github.com/nodejs/node/pull/11392)

Windows 32-bit Installer: https://nodejs.org/dist/v7.7.0/node-v7.7.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v7.7.0/node-v7.7.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v7.7.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v7.7.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v7.7.0/node-v7.7.0.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v7.7.0/node-v7.7.0-darwin-x64.tar.gz \
Linux 32-bit Binary: https://nodejs.org/dist/v7.7.0/node-v7.7.0-linux-x86.tar.xz \
Linux 64-bit Binary: https://nodejs.org/dist/v7.7.0/node-v7.7.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v7.7.0/node-v7.7.0-linux-ppc64le.tar.xz \
Linux PPC BE 64-bit Binary: https://nodejs.org/dist/v7.7.0/node-v7.7.0-linux-ppc64.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v7.7.0/node-v7.7.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v7.7.0/node-v7.7.0-aix-ppc64.tar.gz \
SmartOS 32-bit Binary: https://nodejs.org/dist/v7.7.0/node-v7.7.0-sunos-x86.tar.xz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v7.7.0/node-v7.7.0-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v7.7.0/node-v7.7.0-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v7.7.0/node-v7.7.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v7.7.0/node-v7.7.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v7.7.0/node-v7.7.0.tar.gz \
Other release files: https://nodejs.org/dist/v7.7.0/ \
Documentation: https://nodejs.org/docs/v7.7.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

2faca08ec1b3266625fc7c3b130f3598f4552a780242dc077d2f26205b2ef837  node-v7.7.0-aix-ppc64.tar.gz
168a11e744bd50eb49cf654ff2e51671657fecb3ceb71a2466903005cb3fa209  node-v7.7.0-darwin-x64.tar.gz
40186490d3e6650ab6616aa5f97c63b4677f7582d7eee814dc68d957b91ea8c1  node-v7.7.0-darwin-x64.tar.xz
14f06806449af11951ee0bd4056f0cd09dc16533eea2275eba993d7f9e5baa32  node-v7.7.0-headers.tar.gz
ebf477f96ffebcd08b1c992e857dc98a9ecaf55cbf5de5a9c33ba4a0fe022601  node-v7.7.0-headers.tar.xz
339a9e9602114a84420bcb0b732cf2ce0a76b983f6f1569808a5c5668911441e  node-v7.7.0-linux-arm64.tar.gz
76d5c5f50c8a5c1ac16036dc5e19a8ae17c5038d9404be3e7b5fc96f5c430c56  node-v7.7.0-linux-arm64.tar.xz
da7b4b376052b20d9aad5f04b4da62202e69693c1785c8f9688357440df98180  node-v7.7.0-linux-armv6l.tar.gz
9b5f722cd75daf49b4eb7cbfc98f1bf57369f36840360e1ebe432eb00aaafcf5  node-v7.7.0-linux-armv6l.tar.xz
230ee629bd7fd46fbc7355b8fb082ed687cdd55ac4ca93e92f060168500cfd68  node-v7.7.0-linux-armv7l.tar.gz
350f189ccfd6de3e1ef3dcc0af747eefd1decf93c6a6d55eb147f9749fdc3d66  node-v7.7.0-linux-armv7l.tar.xz
59476c540794ca7b75384c31d13ce4cb49d8c2a451c7974b836bc771d3ec6240  node-v7.7.0-linux-ppc64le.tar.gz
7bb7809f617934c79b45a8649de8e772b0bc51da7e27498f48b26aa137782445  node-v7.7.0-linux-ppc64le.tar.xz
9c353ca2e25640a56e5758f9c0ece2107d871dc81ec32e7fe90892a4775b2331  node-v7.7.0-linux-ppc64.tar.gz
c210c98640eee2034c7d9f8038ace007e2dd2ece3dce1ae783be9d8da598556d  node-v7.7.0-linux-ppc64.tar.xz
d86dae214aef2b955e6014e01a70270efa1a7197f3f7064bc464765e80f5c17f  node-v7.7.0-linux-s390x.tar.gz
9481c092ed0d8f6e1b24c2bfb93d31ad727869332136b2d07d64b8783b493fc8  node-v7.7.0-linux-s390x.tar.xz
67c21170ed0a19da6f859666510ebc19b24f79a1f6b4acc5fc0d7af233d5dab3  node-v7.7.0-linux-x64.tar.gz
679d65591cd84fa0e0998477d58498c2b5e668eba5eab75ed70e07553a5250a8  node-v7.7.0-linux-x64.tar.xz
6be0d11303a2ae0179d2a6fff3d58d6f3b181f4c19ed67fe6e8138f73054cc1c  node-v7.7.0-linux-x86.tar.gz
042a0a5cdb34b608e7d70e6464de4c53dc7ecd6aacdf74bbd60b9de0d9b2159f  node-v7.7.0-linux-x86.tar.xz
4020686534a8ea31c9bbb84b43b5d3628546fc09b3a05c02d16458d2c778e679  node-v7.7.0.pkg
d7648db62f6912a1dcebd014dff66cf965e904ccbcfb30d9668b7286907190ed  node-v7.7.0-sunos-x64.tar.gz
c292d6b14ffa370028bb56b5a116abdd67c58c5a75e5694fb4a250d33c4f8f6d  node-v7.7.0-sunos-x64.tar.xz
57c1dfaa14e78260b962ff55a02ebc244e41bc23b47f84f19c7828a3e6dcc031  node-v7.7.0-sunos-x86.tar.gz
9d739cc35e5bade11b6ce5f3503b2d30923fdae2fd0f8f7a2226fd55b85e36d1  node-v7.7.0-sunos-x86.tar.xz
6716b933ff69f23399e743fd34e08352cd265e559caec5f06ee04b9222721efe  node-v7.7.0.tar.gz
a0f743d63d5b29f96864ab74d5b13ab12f50c11c53395f2fc8d1e75ba13f1086  node-v7.7.0.tar.xz
b57d8dbfb8871805024618d7db8b5318163dd838ca543b69ffd509e8389ad002  node-v7.7.0-win-x64.7z
627749137dec7ad583a208919e1f5ea3a7a043911a6429cb3b6b9395a4efc773  node-v7.7.0-win-x64.zip
8a9a58d912936d24cd920af7e0771da935d73c343e0c954118d6b660c6e39798  node-v7.7.0-win-x86.7z
d5e32ee55d2529f6ce88bc0b1878af21322f93e2b2ce996c24756ea9321226d2  node-v7.7.0-win-x86.zip
bff87fb2927e852e75f03c54d9365b47f6100b9e542c939f092c44c0a82148c3  node-v7.7.0-x64.msi
164f62bb9a304ea1407b89f30bf784c64feb910ba612c4382337f99758213c20  node-v7.7.0-x86.msi
cb673070192d52fdd3b55d7b72deb536cc2477fe5f720f2b106e10384af143c7  win-x64/node.exe
6678abc1d660d563b5907ffbccdd6900e9fcd4b600f452d9cf1443aa276831a3  win-x64/node.lib
d5eb68912ca034bdf472a9d82c41e0d1f0969f0eafe1204e4640eae20c8085f4  win-x64/node_pdb.7z
ab6610dcdd3590b3eb93c4b55811ce6a60ed086ccf4e862219f4efd40813ac56  win-x64/node_pdb.zip
07262f4947c2ab76567b7bde2fb5ab26141741c77d79ac38a398d5de32ea1f80  win-x86/node.exe
5cd1430677af55e18cdcffce5b49a6b69e4d007063bf13b79d9e99681f774c09  win-x86/node.lib
669dbcc4f41e61e2175ca9daa579fe97b12f1be8e081a65e2a8f806cd64923f5  win-x86/node_pdb.7z
59e50d34d1b7ebc9d0a1ffd659e8cc794234157565aa450df4cc69e465718a2f  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----
Version: GnuPG v2

iQIcBAEBCAAGBQJYtjXEAAoJECPv7+k8TP/+n/4QAJ1NWHgOUhiW3u6zMIFX5H0D
kOhnyVMCb9M+rHeeHaJ4TlXNZ/NgwDEeM9E8R/R9JAM/Xy+z5ThQdSo5snhx6Jnd
zGouptogm5AVSCW5klabK1ECwOPH5AA9nZusS67H2mDZfCusvIA8oAKXP+NookPr
AOlTH5gl5GSrHN7c3vaa2IlLSDGUNrczfA1IHWyx3SArOj+YpJvtjF5jOSRUQxl8
UbsdPSkbyiVgqFKaOwhLzT7Uq2XmAj1tkfu/hKijrA4KSi4fE1poSMsJ50RGFnKH
OPQpSnLjbvAQGD3b4F6jFtVG+zcX9arBlUdPHoDIvKt/GZNYise2WtkDbMZxuh+3
wxrWZu2Pu/6U6ngm5QjT4c5cJuvdlyYNho6gNPt80+tJvVP0rA4mzKXFKi7mjaww
sWajEnQiMkAT/FGFqwQmYGfaanXgztFg/SAVPT9uUjlwmEb1CLZGjQsROrcyKXqm
PIeevqQqOdhZhLdJUoYv+VD+Qz50REXS+CgrKkY0BXFaiwmhIhFrdmQ2y248x3uQ
P6vpKs6ljS8tLOrvvVtW0z09cMAfJ0E9dJU7DgwGAkk+UY8GhqtYjn60bEcJJO0i
cFT9wlywzJvvKZFKDLUZh4jtaJptJ+plJgvESU8VFeqlppBIhOVlg0obIdJyFEaU
f8LpcH3mTvDt2HHcEELO
=Jguv
-----END PGP SIGNATURE-----

```
