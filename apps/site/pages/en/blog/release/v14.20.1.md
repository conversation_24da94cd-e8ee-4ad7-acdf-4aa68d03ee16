---
date: '2022-09-23T16:13:35.053Z'
category: release
title: Node v14.20.1 (LTS)
layout: blog-post
author: <PERSON> English
---

### Notable changes

The following CVEs are fixed in this release:

- **[CVE-2022-32212](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2022-32212)**: DNS rebinding in --inspect on macOS (High)
- **[CVE-2022-32213](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2022-32213)**: bypass via obs-fold mechanic (Medium)
- **[CVE-2022-35256](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2022-35256)**: HTTP Request Smuggling Due to Incorrect Parsing of Header Fields (Medium)

More detailed information on each of the vulnerabilities can be found in [September 22nd 2022 Security Releases](/blog/vulnerability/september-2022-security-releases/) blog post.

### Commits

- \[[`a9f1146b88`](https://github.com/nodejs/node/commit/a9f1146b88)] - **http**: disable chunked encoding when OBS fold is used (Paolo Insogna) [nodejs-private/node-private#341](https://github.com/nodejs-private/node-private/pull/341)
- \[[`a1121b456c`](https://github.com/nodejs/node/commit/a1121b456c)] - **src**: fix IPv4 non routable validation (RafaelGSS) [nodejs-private/node-private#337](https://github.com/nodejs-private/node-private/pull/337)
- \[[`de80707870`](https://github.com/nodejs/node/commit/de80707870)] - **src**: fix IS_LTS and IS_RELEASE flags (Richard Lau) [#43761](https://github.com/nodejs/node/pull/43761)

Windows 32-bit Installer: https://nodejs.org/dist/v14.20.1/node-v14.20.1-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v14.20.1/node-v14.20.1-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v14.20.1/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v14.20.1/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v14.20.1/node-v14.20.1.pkg \
macOS Intel 64-bit Binary: https://nodejs.org/dist/v14.20.1/node-v14.20.1-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v14.20.1/node-v14.20.1-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v14.20.1/node-v14.20.1-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v14.20.1/node-v14.20.1-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v14.20.1/node-v14.20.1-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v14.20.1/node-v14.20.1-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v14.20.1/node-v14.20.1-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v14.20.1/node-v14.20.1.tar.gz \
Other release files: https://nodejs.org/dist/v14.20.1/ \
Documentation: https://nodejs.org/docs/v14.20.1/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

1140709298833d74fad652fd4f7e3e76eff0ce496756df2acf7b9872c9d0b7e7  node-v14.20.1-aix-ppc64.tar.gz
7fe00e1e5949c9b99d0b888b9d32b8b2f35a117cd3a94a16c0a2524cb71e3c59  node-v14.20.1-darwin-x64.tar.gz
f00cabf8ad67e79ba3aeac7cc6fd648fe7481ea8360c731cb977d46272fb9e99  node-v14.20.1-darwin-x64.tar.xz
af150706890310e22968dfad7bb237f3c2ae3b278759244e2aeb407aa54e3b3d  node-v14.20.1-headers.tar.gz
55f017d3a4add8465071a859e248457a945c89016dd19b017eb186856809f33e  node-v14.20.1-headers.tar.xz
05fe791367dbce8d76be7e18bac0c9b88a0ed6ab721c31321b96a2dbc31355ce  node-v14.20.1-linux-arm64.tar.gz
8d9e4792b14a1340f08aa35b7e604c0d6151429952ec00b533be1d94ee23896b  node-v14.20.1-linux-arm64.tar.xz
ddf58c8e8a259afbe341f257d3f34646bfdbaffab27d2228e93a99327ce3d14f  node-v14.20.1-linux-armv7l.tar.gz
909ad4f8f56c9970d4bb574f8dc619eb85a241e0cc457bba2edeb0160dc58c83  node-v14.20.1-linux-armv7l.tar.xz
80fb1ea56a4e858ceeccd9deff99dd894778e97c593489789a0a2cd3bfa48729  node-v14.20.1-linux-ppc64le.tar.gz
201ab2b87d7fd65ed1e1816cc2c4af8ff7748f57875ac6e4546a2ef2bdb8053c  node-v14.20.1-linux-ppc64le.tar.xz
bca79e1931e1962c6b9f1c7045521db5d8adea7998ea50ecfc2f335e62ce1f4b  node-v14.20.1-linux-s390x.tar.gz
572e93ab6b49e6f859abf1ad736313cf7634791304bcd0d8477368b01af4ed18  node-v14.20.1-linux-s390x.tar.xz
0aab09a55c11fbd1e6c40356809a86eaaf3330fc96e26f9443f82d46d8f8da5f  node-v14.20.1-linux-x64.tar.gz
2938c38c3654810f99101c9dc91e4783f14ed94eba878fef2906b6a9b95868a8  node-v14.20.1-linux-x64.tar.xz
c0ce39ba47b553e907328b514327a898ba7a4bbb3b4e6c798bbba92a41f69b05  node-v14.20.1.pkg
fd261ab13e8f72ba11afeae713a9e40b1e2318012db3170abc69ecfddef4fe89  node-v14.20.1.tar.gz
365057ea661923cbfa71bdd7a8d0ace9ddff8d22d431ad92355f8433cecff14d  node-v14.20.1.tar.xz
273bd6319232e685b66c5cebff02f1e7b8f30069dbe1d775045bd4894270a300  node-v14.20.1-win-x64.7z
b9b1f99d50d0cca7755ba25211e0dd1d3b39db56805631f551b53a6ce81f8731  node-v14.20.1-win-x64.zip
3b35474d1e6329dbd95e7d867057e805ed007b1363acf158f613c050b2f2bcc2  node-v14.20.1-win-x86.7z
efc4fbfb6d1e751cee1c9a83eaf003c73227342a3b719ba5c82f5908564830ce  node-v14.20.1-win-x86.zip
17cc36f938e6506ff5b0eb02d0287553dbd4353206a67e20c150302ce4f983a1  node-v14.20.1-x64.msi
2164fe760df13ff8a2c14714a511dd56e2bb2c2d4a8e697e6301b3c97fdefde2  node-v14.20.1-x86.msi
80d44b959c3b521de46c9023f8ba9cf70244f4eda0130e033ea7a660577760be  win-x64/node.exe
dea0607a3fca01c7bce5dbe6e275f149bbe1076811d156c6e34e1910028365e7  win-x64/node.lib
128ba7ac4775b15c9ffbafd240956eeb775b302393bf9995c6ffede0f39d2bc7  win-x64/node_pdb.7z
a4729ac8ec7aed4af2ed54a660c57e764cc027da77547bbaeb0c86e0ff06b72d  win-x64/node_pdb.zip
95949fdad5d488589eca51571ad43f4c6eb8aec0eb90ff465c55e66b8617db75  win-x86/node.exe
b7ede8c768bc23de8d155724fcfdc828e7b90a2c5831ae95f0d619ea335b3eb0  win-x86/node.lib
2f4eda6272535509143a436afd3b66a912694955ef4ba86e4e8a158012b7c67a  win-x86/node_pdb.7z
24c2ee2d380a30e8834dc435adcd1d18ee7bb01007f5d39a14acd9269d086ba0  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEFB8HWVt7P/50MJqTdAVTO+V8fVcFAmMt2WQACgkQdAVTO+V8
fVdq7xAAuBp1nRNjkLL5P/bLzCK374VoH0oQC6Q3FVQWKVNgkuVKekNLs/yZry/y
mAEgAl2B3qI9n6n33eDiRTAXcHQM+ZY9D/3MjN++PY5AN4kAWpUcESsbF0WnJ1bb
pmHCvG+z+NpxxowXXi/dIZoekCr6JwmBDZM8qJ93RIHq8gLlCQUEGO1rlA03aLKt
ecY/UlZ5c/Fktjtvzpeo8QKpzLDBAd/uLM/xfGi/dHhCc8+5TwYKVVHEhnKYIEr5
qyuZK7ZODu7XMkWa+g7rsN/Hn7RUxBgzJKFUiNqVSiEkuWIHSADMAmN0YmFsnltb
W+qkEDbUfwwOPKnkbw0uGSjbPMerBoVhnib+9XD3hBzDR944USLKwxv9/fMK9X3v
ZV+Cye+FDOxaxY7xnGPJfONb/6puU5wsQ4jhvIEqlXmSBcE0yHvYv/rjb+p8A+oc
P2U+lNuHpvQgec+GpOpItlUVNvVDRJ2+DMzEf6jA2GU5UgDzSzynzhl05E6yu6fM
a58r7XK9lO0f0ewTzWVhaiG91GWBR7UUFoGeeb0z81baCU685Z6n8tcrNJJhz+db
RzbsDgbdY/n1aShTuGdFA6ka5j/e10Gxps735Lokz4r7woFPU+GFv0tMJo/M7f3T
A36A+iaS+bqSIX5piZHCl0UR6ggqIwcjwQELO9fgtKRpd3Kd9mw=
=Yn73
-----END PGP SIGNATURE-----

```
