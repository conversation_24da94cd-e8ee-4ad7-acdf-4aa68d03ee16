---
date: '2023-11-29T17:28:42.041Z'
category: release
title: Node v18.19.0 (LTS)
layout: blog-post
author: <PERSON><PERSON><PERSON>
---

## 2023-11-29, Version 18.19.0 'Hydrogen' (LTS), @targos

### Notable Changes

#### npm updated to v10

After two months of baking time in Node.js 20, npm 10 is backported, so that all
release lines include a supported version of npm. This release includes npm v10.2.3.

Refer to <https://github.com/nodejs/Release/issues/884> for the plan to land npm 10.

#### ESM and customization hook changes

##### Leverage loaders when resolving subsequent loaders

Loaders now apply to subsequent loaders, for example: `--experimental-loader ts-node --experimental-loader loader-written-in-typescript`.

Contributed by <PERSON><PERSON> in [#43772](https://github.com/nodejs/node/pull/43772).

##### New `node:module` API `register` for module customization hooks; new `initialize` hook

There is a new API `register` available on `node:module` to specify a file that exports module customization hooks, and pass data to the hooks, and establish communication channels with them. The “define the file with the hooks” part was previously handled by a flag `--experimental-loader`, but when the hooks moved into a dedicated thread in 20.0.0 there was a need to provide a way to communicate between the main (application) thread and the hooks thread. This can now be done by calling `register` from the main thread and passing data, including `MessageChannel` instances.

We encourage users to migrate to an approach that uses [`--import`](https://nodejs.org/api/cli.html#--importmodule) with `register`, such as:

```bash
node --import ./file-that-calls-register.js ./app.js
```

Using `--import` ensures that the customization hooks are registered before any application code runs, even the entry point.

Contributed by João Lenon and Jacob Smith in [#46826](https://github.com/nodejs/node/pull/46826), Izaak Schroeder and Jacob Smith in [#48842](https://github.com/nodejs/node/pull/48842) and [#48559](https://github.com/nodejs/node/pull/48559).

##### `import.meta.resolve` unflagged

In ES modules, [`import.meta.resolve(specifier)`](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Operators/import.meta/resolve)
can be used to get an absolute URL string to which `specifier` resolves, similar
to `require.resolve` in CommonJS. This aligns Node.js with browsers and other server-side runtimes.

Contributed by Guy Bedford in [#49028](https://github.com/nodejs/node/pull/49028).

##### `--experimental-default-type` flag to flip module defaults

The new flag `--experimental-default-type` can be used to flip the default
module system used by Node.js. Input that is already explicitly defined as ES
modules or CommonJS, such as by a `package.json` `"type"` field or `.mjs`/`.cjs`
file extension or the `--input-type` flag, is unaffected. What is currently
implicitly CommonJS would instead be interpreted as ES modules under
`--experimental-default-type=module`:

- String input provided via `--eval` or STDIN, if `--input-type` is unspecified.

- Files ending in `.js` or with no extension, if there is no `package.json` file
  present in the same folder or any parent folder.

- Files ending in `.js` or with no extension, if the nearest parent
  `package.json` field lacks a `type` field; unless the folder is inside a
  `node_modules` folder.

In addition, extensionless files are interpreted as Wasm if
`--experimental-wasm-modules` is passed and the file contains the "magic bytes"
Wasm header.

Contributed by Geoffrey Booth in [#49869](https://github.com/nodejs/node/pull/49869).

##### Other ESM-related changes

- \[[`ed2d46f4cc`](https://github.com/nodejs/node/commit/ed2d46f4cc)] - **doc**: move and rename loaders section (Geoffrey Booth) [#49261](https://github.com/nodejs/node/pull/49261)
- \[[`92734d4480`](https://github.com/nodejs/node/commit/92734d4480)] - **esm**: use import attributes instead of import assertions (Antoine du Hamel) [#50140](https://github.com/nodejs/node/pull/50140)
- \[[`e96f7ef881`](https://github.com/nodejs/node/commit/e96f7ef881)] - **(SEMVER-MINOR)** **vm**: use import attributes instead of import assertions (Antoine du Hamel) [#50141](https://github.com/nodejs/node/pull/50141)

#### Test runner changes

Many changes to the built-in test runner have been backported. This includes
the following additions:

- \[[`b283ae4238`](https://github.com/nodejs/node/commit/b283ae4238)] - **(SEMVER-MINOR)** **test_runner**: accept `testOnly` in `run` (Moshe Atlow) [#49753](https://github.com/nodejs/node/pull/49753)
- \[[`059b1945d8`](https://github.com/nodejs/node/commit/059b1945d8)] - **(SEMVER-MINOR)** **test_runner**: add junit reporter (Moshe Atlow) [#49614](https://github.com/nodejs/node/pull/49614)
- \[[`d61a505546`](https://github.com/nodejs/node/commit/d61a505546)] - **(SEMVER-MINOR)** **test_runner**: expose location of tests (Colin Ihrig) [#48975](https://github.com/nodejs/node/pull/48975)
- \[[`b55eb2a8d1`](https://github.com/nodejs/node/commit/b55eb2a8d1)] - **(SEMVER-MINOR)** **test_runner**: add shards support (Raz Luvaton) [#48639](https://github.com/nodejs/node/pull/48639)
- \[[`05e7f28b40`](https://github.com/nodejs/node/commit/05e7f28b40)] - **(SEMVER-MINOR)** **test_runner**: add initial draft for fakeTimers (Erick Wendel) [#47775](https://github.com/nodejs/node/pull/47775)
- \[[`428301ad27`](https://github.com/nodejs/node/commit/428301ad27)] - **(SEMVER-MINOR)** **test_runner, cli**: add --test-concurrency flag (Colin Ihrig) [#49996](https://github.com/nodejs/node/pull/49996)

#### Other notable changes

- \[[`0c4a84e8e9`](https://github.com/nodejs/node/commit/0c4a84e8e9)] - **(SEMVER-MINOR)** **deps**: update uvwasi to 0.0.19 (Node.js GitHub Bot) [#49908](https://github.com/nodejs/node/pull/49908)
- \[[`fae60c5841`](https://github.com/nodejs/node/commit/fae60c5841)] - **stream**: use bitmap in readable state (Benjamin Gruenbaum) [#49745](https://github.com/nodejs/node/pull/49745)
- \[[`17246be158`](https://github.com/nodejs/node/commit/17246be158)] - **(SEMVER-MINOR)** **lib**: add api to detect whether source-maps are enabled (翠 / green) [#46391](https://github.com/nodejs/node/pull/46391)
- \[[`2e9f7284a1`](https://github.com/nodejs/node/commit/2e9f7284a1)] - **(SEMVER-MINOR)** **lib**: add tracing channel to diagnostics_channel (Stephen Belanger) [#44943](https://github.com/nodejs/node/pull/44943)
- \[[`cc7bf1f641`](https://github.com/nodejs/node/commit/cc7bf1f641)] - **(SEMVER-MINOR)** **src**: add cjs_module_lexer_version base64_version (Jithil P Ponnan) [#45629](https://github.com/nodejs/node/pull/45629)
- \[[`b5d16cd8f0`](https://github.com/nodejs/node/commit/b5d16cd8f0)] - **(SEMVER-MINOR)** **tls**: add ALPNCallback server option for dynamic ALPN negotiation (Tim Perry) [#45190](https://github.com/nodejs/node/pull/45190)

### Commits

- \[[`0d0eb47e2a`](https://github.com/nodejs/node/commit/0d0eb47e2a)] - **benchmark**: add benchmarks for the test_runner (Raz Luvaton) [#48931](https://github.com/nodejs/node/pull/48931)
- \[[`8bb03d10f4`](https://github.com/nodejs/node/commit/8bb03d10f4)] - **benchmark**: differentiate whatwg and legacy url (Yagiz Nizipli) [#47377](https://github.com/nodejs/node/pull/47377)
- \[[`3d7734cbe3`](https://github.com/nodejs/node/commit/3d7734cbe3)] - **benchmark**: lower URL.canParse runs (Khafra) [#47351](https://github.com/nodejs/node/pull/47351)
- \[[`24d3fcf415`](https://github.com/nodejs/node/commit/24d3fcf415)] - **benchmark**: stablize encode benchmark (Joyee Cheung) [#46658](https://github.com/nodejs/node/pull/46658)
- \[[`e08fd98bcc`](https://github.com/nodejs/node/commit/e08fd98bcc)] - **bootstrap**: use correct descriptor for Symbol.{dispose,asyncDispose} (Jordan Harband) [#48703](https://github.com/nodejs/node/pull/48703)
- \[[`cf9ddcd6c8`](https://github.com/nodejs/node/commit/cf9ddcd6c8)] - **bootstrap**: simplify initialization of source map handlers (Joyee Cheung) [#48304](https://github.com/nodejs/node/pull/48304)
- \[[`12d731e431`](https://github.com/nodejs/node/commit/12d731e431)] - **bootstrap**: log isolate data info in mksnapshot debug logs (Joyee Cheung) [#47768](https://github.com/nodejs/node/pull/47768)
- \[[`d66873871a`](https://github.com/nodejs/node/commit/d66873871a)] - **bootstrap**: store internal loaders in C++ via a binding (Joyee Cheung) [#47215](https://github.com/nodejs/node/pull/47215)
- \[[`1a499c5082`](https://github.com/nodejs/node/commit/1a499c5082)] - **bootstrap**: optimize modules loaded in the built-in snapshot (Joyee Cheung) [#45849](https://github.com/nodejs/node/pull/45849)
- \[[`e0e09caafe`](https://github.com/nodejs/node/commit/e0e09caafe)] - **bootstrap**: make CJS loader snapshotable (Joyee Cheung) [#45849](https://github.com/nodejs/node/pull/45849)
- \[[`5f37decd56`](https://github.com/nodejs/node/commit/5f37decd56)] - **bootstrap**: include event_target into the built-in snapshot (Joyee Cheung) [#45849](https://github.com/nodejs/node/pull/45849)
- \[[`c3f56a3dee`](https://github.com/nodejs/node/commit/c3f56a3dee)] - **bootstrap**: support module_wrap binding in snapshot (Joyee Cheung) [#45849](https://github.com/nodejs/node/pull/45849)
- \[[`6466acbc89`](https://github.com/nodejs/node/commit/6466acbc89)] - **bootstrap**: lazy load non-essential modules (Joyee Cheung) [#45659](https://github.com/nodejs/node/pull/45659)
- \[[`a0d4b69df4`](https://github.com/nodejs/node/commit/a0d4b69df4)] - **bootstrap**: lazy-load Performance.prototype.timeOrigin (Joyee Cheung) [#46425](https://github.com/nodejs/node/pull/46425)
- \[[`c1bc8118e3`](https://github.com/nodejs/node/commit/c1bc8118e3)] - **bootstrap**: generate bootstrapper arguments in BuiltinLoader (Joyee Cheung) [#44488](https://github.com/nodejs/node/pull/44488)
- \[[`075c57e88b`](https://github.com/nodejs/node/commit/075c57e88b)] - **build**: add symlink to `compile_commands.json` file if needed (Juan José) [#49260](https://github.com/nodejs/node/pull/49260)
- \[[`9e1c531b8d`](https://github.com/nodejs/node/commit/9e1c531b8d)] - **build**: expand when we run internet tests (Michael Dawson) [#49218](https://github.com/nodejs/node/pull/49218)
- \[[`a781d24624`](https://github.com/nodejs/node/commit/a781d24624)] - **build**: fix typo `libray` -> `library` (configure.py) (michalbiesek) [#49106](https://github.com/nodejs/node/pull/49106)
- \[[`f2eccb7a04`](https://github.com/nodejs/node/commit/f2eccb7a04)] - **build**: fix `configure --link-module` (Richard Lau) [#48522](https://github.com/nodejs/node/pull/48522)
- \[[`a44d555494`](https://github.com/nodejs/node/commit/a44d555494)] - **build**: fix IBM i build with Python 3.9 (Richard Lau) [#48056](https://github.com/nodejs/node/pull/48056)
- \[[`7f68e14ea2`](https://github.com/nodejs/node/commit/7f68e14ea2)] - **child_process**: improve spawn performance on Linux (Keyhan Vakil) [#48523](https://github.com/nodejs/node/pull/48523)
- \[[`76e4d4117c`](https://github.com/nodejs/node/commit/76e4d4117c)] - **crypto**: ensure valid point on elliptic curve in SubtleCrypto.importKey (Filip Skokan) [#50234](https://github.com/nodejs/node/pull/50234)
- \[[`7af54279b5`](https://github.com/nodejs/node/commit/7af54279b5)] - **deps**: V8: cherry-pick 70caf337c3f6 (kxxt) [#50506](https://github.com/nodejs/node/pull/50506)
- \[[`49c5495339`](https://github.com/nodejs/node/commit/49c5495339)] - **deps**: update zlib to ********-motley-fef5869 (Node.js GitHub Bot) [#50085](https://github.com/nodejs/node/pull/50085)
- \[[`e0fd52bf07`](https://github.com/nodejs/node/commit/e0fd52bf07)] - **deps**: update googletest to 2dd1c13 (Node.js GitHub Bot) [#50081](https://github.com/nodejs/node/pull/50081)
- \[[`1b103cc567`](https://github.com/nodejs/node/commit/1b103cc567)] - **deps**: update googletest to e47544a (Node.js GitHub Bot) [#49982](https://github.com/nodejs/node/pull/49982)
- \[[`736c869eeb`](https://github.com/nodejs/node/commit/736c869eeb)] - **deps**: update googletest to d1467f5 (Node.js GitHub Bot) [#49676](https://github.com/nodejs/node/pull/49676)
- \[[`cd99ee1f35`](https://github.com/nodejs/node/commit/cd99ee1f35)] - **deps**: update googletest to 8a6feab (Node.js GitHub Bot) [#49463](https://github.com/nodejs/node/pull/49463)
- \[[`5c338573ff`](https://github.com/nodejs/node/commit/5c338573ff)] - **deps**: update zlib to ********-motley-f5fd0ad (Node.js GitHub Bot) [#49252](https://github.com/nodejs/node/pull/49252)
- \[[`374ec3d623`](https://github.com/nodejs/node/commit/374ec3d623)] - **deps**: update googletest to 7e33b6a (Node.js GitHub Bot) [#49034](https://github.com/nodejs/node/pull/49034)
- \[[`c15dd6679b`](https://github.com/nodejs/node/commit/c15dd6679b)] - **deps**: update zlib to ********-motley-526382e (Node.js GitHub Bot) [#49033](https://github.com/nodejs/node/pull/49033)
- \[[`588bd5e524`](https://github.com/nodejs/node/commit/588bd5e524)] - **deps**: update googletest to c875c4e (Node.js GitHub Bot) [#48964](https://github.com/nodejs/node/pull/48964)
- \[[`6059b59018`](https://github.com/nodejs/node/commit/6059b59018)] - **deps**: update zlib to ********-motley-61dc0bd (Node.js GitHub Bot) [#48788](https://github.com/nodejs/node/pull/48788)
- \[[`e455dd4003`](https://github.com/nodejs/node/commit/e455dd4003)] - **deps**: update googletest to cc36671 (Node.js GitHub Bot) [#48789](https://github.com/nodejs/node/pull/48789)
- \[[`747fbb49ca`](https://github.com/nodejs/node/commit/747fbb49ca)] - **deps**: V8: cherry-pick 1a782f6543ae (Keyhan Vakil) [#48523](https://github.com/nodejs/node/pull/48523)
- \[[`272e55c66f`](https://github.com/nodejs/node/commit/272e55c66f)] - **deps**: upgrade npm to 10.2.3 (npm team) [#50531](https://github.com/nodejs/node/pull/50531)
- \[[`3f6dcc62e5`](https://github.com/nodejs/node/commit/3f6dcc62e5)] - **deps**: update archs files for openssl-3.0.12+quic1 (Node.js GitHub Bot) [#50411](https://github.com/nodejs/node/pull/50411)
- \[[`da26cdbe84`](https://github.com/nodejs/node/commit/da26cdbe84)] - **deps**: upgrade openssl sources to quictls/openssl-3.0.12+quic1 (Node.js GitHub Bot) [#50411](https://github.com/nodejs/node/pull/50411)
- \[[`23069c34b2`](https://github.com/nodejs/node/commit/23069c34b2)] - **deps**: V8: cherry-pick d69c7937c99d (Michaël Zasso) [#46425](https://github.com/nodejs/node/pull/46425)
- \[[`5f852cc9fe`](https://github.com/nodejs/node/commit/5f852cc9fe)] - **deps**: V8: cherry-pick f7d000a7ae7b (Luke Albao) [#50344](https://github.com/nodejs/node/pull/50344)
- \[[`0c4a84e8e9`](https://github.com/nodejs/node/commit/0c4a84e8e9)] - **(SEMVER-MINOR)** **deps**: update uvwasi to 0.0.19 (Node.js GitHub Bot) [#49908](https://github.com/nodejs/node/pull/49908)
- \[[`8682b90d02`](https://github.com/nodejs/node/commit/8682b90d02)] - **deps**: update simdutf to 3.2.18 (Node.js GitHub Bot) [#50091](https://github.com/nodejs/node/pull/50091)
- \[[`11ecd06aeb`](https://github.com/nodejs/node/commit/11ecd06aeb)] - **deps**: update simdutf to 3.2.17 (Node.js GitHub Bot) [#49019](https://github.com/nodejs/node/pull/49019)
- \[[`43bfe5f020`](https://github.com/nodejs/node/commit/43bfe5f020)] - **deps**: upgrade npm to 10.2.0 (npm team) [#50027](https://github.com/nodejs/node/pull/50027)
- \[[`a140bc284b`](https://github.com/nodejs/node/commit/a140bc284b)] - **deps**: upgrade npm to 10.1.0 (npm team) [#49570](https://github.com/nodejs/node/pull/49570)
- \[[`65ca41c276`](https://github.com/nodejs/node/commit/65ca41c276)] - **(SEMVER-MINOR)** **deps**: upgrade npm to 10.0.0 (npm team) [#49423](https://github.com/nodejs/node/pull/49423)
- \[[`df1ff8e3da`](https://github.com/nodejs/node/commit/df1ff8e3da)] - **deps**: fix call to undeclared functions 'ntohl' and 'htons' (MatteoBax) [#49979](https://github.com/nodejs/node/pull/49979)
- \[[`f228dc7955`](https://github.com/nodejs/node/commit/f228dc7955)] - **deps**: update corepack to 0.22.0 (Node.js GitHub Bot) [#50325](https://github.com/nodejs/node/pull/50325)
- \[[`4324ebab67`](https://github.com/nodejs/node/commit/4324ebab67)] - **deps**: update corepack to 0.21.0 (Node.js GitHub Bot) [#50088](https://github.com/nodejs/node/pull/50088)
- \[[`1cabb77659`](https://github.com/nodejs/node/commit/1cabb77659)] - **deps**: update corepack to 0.20.0 (Node.js GitHub Bot) [#49464](https://github.com/nodejs/node/pull/49464)
- \[[`04227b287e`](https://github.com/nodejs/node/commit/04227b287e)] - **deps**: update c-ares to 1.20.1 (Node.js GitHub Bot) [#50082](https://github.com/nodejs/node/pull/50082)
- \[[`13e69ee11c`](https://github.com/nodejs/node/commit/13e69ee11c)] - **deps**: update c-ares to 1.20.0 (Node.js GitHub Bot) [#50082](https://github.com/nodejs/node/pull/50082)
- \[[`ac717df17e`](https://github.com/nodejs/node/commit/ac717df17e)] - **deps**: update ada to 2.7.2 (Node.js GitHub Bot) [#50338](https://github.com/nodejs/node/pull/50338)
- \[[`6885fc9386`](https://github.com/nodejs/node/commit/6885fc9386)] - **deps**: update ada to 2.6.10 (Node.js GitHub Bot) [#49984](https://github.com/nodejs/node/pull/49984)
- \[[`76c5f4039f`](https://github.com/nodejs/node/commit/76c5f4039f)] - **deps**: update ada to 2.6.9 (Node.js GitHub Bot) [#49340](https://github.com/nodejs/node/pull/49340)
- \[[`597ea77422`](https://github.com/nodejs/node/commit/597ea77422)] - **deps**: update ada to 2.6.8 (Node.js GitHub Bot) [#49340](https://github.com/nodejs/node/pull/49340)
- \[[`8e7dcba553`](https://github.com/nodejs/node/commit/8e7dcba553)] - **deps**: update ada to 2.6.7 (Node.js GitHub Bot) [#49340](https://github.com/nodejs/node/pull/49340)
- \[[`9f2037e8ee`](https://github.com/nodejs/node/commit/9f2037e8ee)] - **deps**: update ada to 2.6.5 (Node.js GitHub Bot) [#49340](https://github.com/nodejs/node/pull/49340)
- \[[`4723976703`](https://github.com/nodejs/node/commit/4723976703)] - **deps**: update ada to 2.6.3 (Node.js GitHub Bot) [#49340](https://github.com/nodejs/node/pull/49340)
- \[[`7ccb478664`](https://github.com/nodejs/node/commit/7ccb478664)] - **deps**: update undici to 5.26.4 (Node.js GitHub Bot) [#50274](https://github.com/nodejs/node/pull/50274)
- \[[`88f9ebb770`](https://github.com/nodejs/node/commit/88f9ebb770)] - **diagnostics_channel**: fix ref counting bug when reaching zero subscribers (Stephen Belanger) [#47520](https://github.com/nodejs/node/pull/47520)
- \[[`284a869540`](https://github.com/nodejs/node/commit/284a869540)] - **dns**: call `ada::idna::to_ascii` directly from c++ (Yagiz Nizipli) [#47920](https://github.com/nodejs/node/pull/47920)
- \[[`10968370b5`](https://github.com/nodejs/node/commit/10968370b5)] - **doc**: drop github actions check in sec release process (Rafael Gonzaga) [#48978](https://github.com/nodejs/node/pull/48978)
- \[[`07c3b88c74`](https://github.com/nodejs/node/commit/07c3b88c74)] - **doc**: remove `@anonrig` from performance initiative (Yagiz Nizipli) [#49641](https://github.com/nodejs/node/pull/49641)
- \[[`e26b89e8be`](https://github.com/nodejs/node/commit/e26b89e8be)] - **doc**: fix node-api call example (Chengzhong Wu) [#49395](https://github.com/nodejs/node/pull/49395)
- \[[`4c93905f6c`](https://github.com/nodejs/node/commit/4c93905f6c)] - **doc**: add news issue for Diagnostics WG (Michael Dawson) [#49306](https://github.com/nodejs/node/pull/49306)
- \[[`3f1a237a8f`](https://github.com/nodejs/node/commit/3f1a237a8f)] - **doc**: add print results for examples in `StringDecoder` (Jungku Lee) [#49326](https://github.com/nodejs/node/pull/49326)
- \[[`45caad82bb`](https://github.com/nodejs/node/commit/45caad82bb)] - **doc**: update outdated reference to NIST SP 800-131A (Tobias Nießen) [#49316](https://github.com/nodejs/node/pull/49316)
- \[[`62f823d5a2`](https://github.com/nodejs/node/commit/62f823d5a2)] - **doc**: use `cjs` as block code's type in `MockTimers` (Deokjin Kim) [#49309](https://github.com/nodejs/node/pull/49309)
- \[[`0dda724d3f`](https://github.com/nodejs/node/commit/0dda724d3f)] - **doc**: update `options.filter` description for `fs.cp` (Shubham Pandey) [#49289](https://github.com/nodejs/node/pull/49289)
- \[[`4ba11e352b`](https://github.com/nodejs/node/commit/4ba11e352b)] - **doc**: avoid "not currently recommended" (Tobias Nießen) [#49300](https://github.com/nodejs/node/pull/49300)
- \[[`9ca85b58b3`](https://github.com/nodejs/node/commit/9ca85b58b3)] - **doc**: modify param description for end(),write() in `StringDecoder` (Jungku Lee) [#49285](https://github.com/nodejs/node/pull/49285)
- \[[`3f771cab67`](https://github.com/nodejs/node/commit/3f771cab67)] - **doc**: use NODE_API_SUPPORTED_VERSION_MAX in release doc (Cheng Zhao) [#49268](https://github.com/nodejs/node/pull/49268)
- \[[`f181c37e75`](https://github.com/nodejs/node/commit/f181c37e75)] - **doc**: fix typo in `stream.finished` documentation (Antoine du Hamel) [#49271](https://github.com/nodejs/node/pull/49271)
- \[[`c70945ddc2`](https://github.com/nodejs/node/commit/c70945ddc2)] - **doc**: update description for `percent_encode` sets in `WHATWG API` (Jungku Lee) [#49258](https://github.com/nodejs/node/pull/49258)
- \[[`f9c2a3fb3e`](https://github.com/nodejs/node/commit/f9c2a3fb3e)] - **doc**: clarify use of Uint8Array for n-api (Fedor Indutny) [#48742](https://github.com/nodejs/node/pull/48742)
- \[[`bf22a5f66e`](https://github.com/nodejs/node/commit/bf22a5f66e)] - **doc**: use same name in the doc as in the code (Hyunjin Kim) [#49216](https://github.com/nodejs/node/pull/49216)
- \[[`72bd527fb6`](https://github.com/nodejs/node/commit/72bd527fb6)] - **doc**: add notable-change label mention to PR template (Rafael Gonzaga) [#49188](https://github.com/nodejs/node/pull/49188)
- \[[`2247e52fe0`](https://github.com/nodejs/node/commit/2247e52fe0)] - **doc**: add h1 summary to security release process (Rafael Gonzaga) [#49112](https://github.com/nodejs/node/pull/49112)
- \[[`3b82e9aed1`](https://github.com/nodejs/node/commit/3b82e9aed1)] - **doc**: fix wording in napi_async_init (Tobias Nießen) [#49180](https://github.com/nodejs/node/pull/49180)
- \[[`55171d88a0`](https://github.com/nodejs/node/commit/55171d88a0)] - **doc**: fix `Type` notation in webstreams (Deokjin Kim) [#49121](https://github.com/nodejs/node/pull/49121)
- \[[`79c0497398`](https://github.com/nodejs/node/commit/79c0497398)] - **doc**: make the NODE_VERSION_IS_RELEASE revert clear (Rafael Gonzaga) [#49114](https://github.com/nodejs/node/pull/49114)
- \[[`7ee26fb8df`](https://github.com/nodejs/node/commit/7ee26fb8df)] - **doc**: update with latest security release (Rafael Gonzaga) [#49085](https://github.com/nodejs/node/pull/49085)
- \[[`9ce73964be`](https://github.com/nodejs/node/commit/9ce73964be)] - **doc**: add description for `--port` flag of `node inspect` (Michael Bianco) [#48785](https://github.com/nodejs/node/pull/48785)
- \[[`633b8cd181`](https://github.com/nodejs/node/commit/633b8cd181)] - **doc**: add missing period (Rich Trott) [#49094](https://github.com/nodejs/node/pull/49094)
- \[[`6daa9ec2a4`](https://github.com/nodejs/node/commit/6daa9ec2a4)] - **doc**: add ESM examples in http.md (btea) [#47763](https://github.com/nodejs/node/pull/47763)
- \[[`12b83e81b9`](https://github.com/nodejs/node/commit/12b83e81b9)] - **doc**: detailed description of keystrokes Ctrl-Y and Meta-Y (Ray) [#43529](https://github.com/nodejs/node/pull/43529)
- \[[`ead654f976`](https://github.com/nodejs/node/commit/ead654f976)] - **doc**: clarify use of process.env in worker threads on Windows (Daeyeon Jeong) [#49008](https://github.com/nodejs/node/pull/49008)
- \[[`**********`](https://github.com/nodejs/node/commit/**********)] - **doc**: remove v14 mention (Rafael Gonzaga) [#49005](https://github.com/nodejs/node/pull/49005)
- \[[`833c643eb4`](https://github.com/nodejs/node/commit/833c643eb4)] - **doc**: run license-builder (github-actions\[bot]) [#48898](https://github.com/nodejs/node/pull/48898)
- \[[`cb43717c97`](https://github.com/nodejs/node/commit/cb43717c97)] - **doc**: add ver of 18.x where Node-api 9 is supported (Michael Dawson) [#48876](https://github.com/nodejs/node/pull/48876)
- \[[`a8d5c16a2a`](https://github.com/nodejs/node/commit/a8d5c16a2a)] - **doc**: include experimental features assessment (Rafael Gonzaga) [#48824](https://github.com/nodejs/node/pull/48824)
- \[[`e6d8735e2b`](https://github.com/nodejs/node/commit/e6d8735e2b)] - **doc**: add new TSC members (Michael Dawson) [#48841](https://github.com/nodejs/node/pull/48841)
- \[[`d4fe00d0c7`](https://github.com/nodejs/node/commit/d4fe00d0c7)] - **doc**: refactor node-api support matrix (Michael Dawson) [#48774](https://github.com/nodejs/node/pull/48774)
- \[[`629132d84c`](https://github.com/nodejs/node/commit/629132d84c)] - **doc**: declare `path` on example of `async_hooks.executionAsyncId()` (Deokjin Kim) [#48556](https://github.com/nodejs/node/pull/48556)
- \[[`dfd368ac9f`](https://github.com/nodejs/node/commit/dfd368ac9f)] - **doc**: remove the . in the end to reduce confusing (Jason) [#48719](https://github.com/nodejs/node/pull/48719)
- \[[`74d8f96413`](https://github.com/nodejs/node/commit/74d8f96413)] - **doc**: nodejs-social over nodejs/tweet (Rafael Gonzaga) [#48769](https://github.com/nodejs/node/pull/48769)
- \[[`73a7e00d06`](https://github.com/nodejs/node/commit/73a7e00d06)] - **doc**: add missing history info for `import.meta.resolve` (Antoine du Hamel) [#49700](https://github.com/nodejs/node/pull/49700)
- \[[`c20fdb4e52`](https://github.com/nodejs/node/commit/c20fdb4e52)] - **doc**: edit `import.meta.resolve` documentation (Antoine du Hamel) [#49247](https://github.com/nodejs/node/pull/49247)
- \[[`1ac389ecef`](https://github.com/nodejs/node/commit/1ac389ecef)] - **doc**: update module hooks docs (Geoffrey Booth) [#49265](https://github.com/nodejs/node/pull/49265)
- \[[`ed2d46f4cc`](https://github.com/nodejs/node/commit/ed2d46f4cc)] - **doc**: move and rename loaders section (Geoffrey Booth) [#49261](https://github.com/nodejs/node/pull/49261)
- \[[`258df0e72d`](https://github.com/nodejs/node/commit/258df0e72d)] - **doc**: add signature for `module.register` (Geoffrey Booth) [#49251](https://github.com/nodejs/node/pull/49251)
- \[[`58eaf3f6ae`](https://github.com/nodejs/node/commit/58eaf3f6ae)] - **doc**: caveat unavailability of `import.meta.resolve` in custom loaders (Jacob Smith) [#49242](https://github.com/nodejs/node/pull/49242)
- \[[`2fef28b2b9`](https://github.com/nodejs/node/commit/2fef28b2b9)] - **doc**: fix name of the flag in `initialize()` docs (Antoine du Hamel) [#49158](https://github.com/nodejs/node/pull/49158)
- \[[`15280fb42c`](https://github.com/nodejs/node/commit/15280fb42c)] - **doc**: add steps about signing the binary in single-executable docs (Darshan Sen) [#46764](https://github.com/nodejs/node/pull/46764)
- \[[`e374ba296c`](https://github.com/nodejs/node/commit/e374ba296c)] - **doc**: add "type" to test runner event details (Phil Nash) [#49014](https://github.com/nodejs/node/pull/49014)
- \[[`ec0a6c1f1b`](https://github.com/nodejs/node/commit/ec0a6c1f1b)] - **doc**: add new reporter events to custom reporter examples (Chemi Atlow) [#48903](https://github.com/nodejs/node/pull/48903)
- \[[`e8a32fb49b`](https://github.com/nodejs/node/commit/e8a32fb49b)] - **doc**: change duration to duration_ms on test documentation (Ardi_Nugraha) [#48892](https://github.com/nodejs/node/pull/48892)
- \[[`2b30c8b8a3`](https://github.com/nodejs/node/commit/2b30c8b8a3)] - **doc**: fix `globalPreload` example (bmacnaughton) [#50300](https://github.com/nodejs/node/pull/50300)
- \[[`8a57182769`](https://github.com/nodejs/node/commit/8a57182769)] - **doc,test**: extend the list of platforms supported by single-executables (Darshan Sen) [#47026](https://github.com/nodejs/node/pull/47026)
- \[[`92734d4480`](https://github.com/nodejs/node/commit/92734d4480)] - **esm**: use import attributes instead of import assertions (Antoine du Hamel) [#50140](https://github.com/nodejs/node/pull/50140)
- \[[`c3c945e654`](https://github.com/nodejs/node/commit/c3c945e654)] - **esm**: bypass CommonJS loader under --default-type (Geoffrey Booth) [#49986](https://github.com/nodejs/node/pull/49986)
- \[[`fe691984b2`](https://github.com/nodejs/node/commit/fe691984b2)] - **esm**: unflag extensionless javascript and wasm in module scope (Geoffrey Booth) [#49974](https://github.com/nodejs/node/pull/49974)
- \[[`56bd9a88ac`](https://github.com/nodejs/node/commit/56bd9a88ac)] - **esm**: --experimental-default-type flag to flip module defaults (Geoffrey Booth) [#49869](https://github.com/nodejs/node/pull/49869)
- \[[`72644d62e7`](https://github.com/nodejs/node/commit/72644d62e7)] - **esm**: improve JSDoc annotation of internal functions (Antoine du Hamel) [#49959](https://github.com/nodejs/node/pull/49959)
- \[[`957725f601`](https://github.com/nodejs/node/commit/957725f601)] - **esm**: require braces for modules code (Geoffrey Booth) [#49657](https://github.com/nodejs/node/pull/49657)
- \[[`c12685f82d`](https://github.com/nodejs/node/commit/c12685f82d)] - **esm**: fix cache collision on JSON files using file: URL (Antoine du Hamel) [#49887](https://github.com/nodejs/node/pull/49887)
- \[[`ed8dd33493`](https://github.com/nodejs/node/commit/ed8dd33493)] - **esm**: identify parent importing a url with invalid host (Jacob Smith) [#49736](https://github.com/nodejs/node/pull/49736)
- \[[`46d730ab75`](https://github.com/nodejs/node/commit/46d730ab75)] - **esm**: fix return type of `import.meta.resolve` (Antoine du Hamel) [#49698](https://github.com/nodejs/node/pull/49698)
- \[[`12cb700478`](https://github.com/nodejs/node/commit/12cb700478)] - **esm**: update loaders warning (Geoffrey Booth) [#49633](https://github.com/nodejs/node/pull/49633)
- \[[`47193a347e`](https://github.com/nodejs/node/commit/47193a347e)] - **esm**: fix support for `URL` instances in `register` (Antoine du Hamel) [#49655](https://github.com/nodejs/node/pull/49655)
- \[[`51ced0f1a1`](https://github.com/nodejs/node/commit/51ced0f1a1)] - **esm**: clarify ERR_REQUIRE_ESM errors (Daniel Compton) [#49521](https://github.com/nodejs/node/pull/49521)
- \[[`4be5612bae`](https://github.com/nodejs/node/commit/4be5612bae)] - **esm**: remove return value for `Module.register` (Antoine du Hamel) [#49529](https://github.com/nodejs/node/pull/49529)
- \[[`0875867e27`](https://github.com/nodejs/node/commit/0875867e27)] - **esm**: refactor test-esm-loader-resolve-type (Geoffrey Booth) [#49493](https://github.com/nodejs/node/pull/49493)
- \[[`9b7c9d93e9`](https://github.com/nodejs/node/commit/9b7c9d93e9)] - **esm**: refactor test-esm-named-exports (Geoffrey Booth) [#49493](https://github.com/nodejs/node/pull/49493)
- \[[`d1f5514917`](https://github.com/nodejs/node/commit/d1f5514917)] - **esm**: refactor mocking test (Geoffrey Booth) [#49465](https://github.com/nodejs/node/pull/49465)
- \[[`01ca6d277d`](https://github.com/nodejs/node/commit/01ca6d277d)] - **esm**: fix `globalPreload` warning (Antoine du Hamel) [#49069](https://github.com/nodejs/node/pull/49069)
- \[[`c1a84398b4`](https://github.com/nodejs/node/commit/c1a84398b4)] - **esm**: unflag import.meta.resolve (Guy Bedford) [#49028](https://github.com/nodejs/node/pull/49028)
- \[[`ef43f084e9`](https://github.com/nodejs/node/commit/ef43f084e9)] - **esm**: import.meta.resolve exact module not found errors should return (Guy Bedford) [#49038](https://github.com/nodejs/node/pull/49038)
- \[[`385f24c9cc`](https://github.com/nodejs/node/commit/385f24c9cc)] - **esm**: protect `ERR_UNSUPPORTED_DIR_IMPORT` against prototype pollution (Antoine du Hamel) [#49060](https://github.com/nodejs/node/pull/49060)
- \[[`10e7c3a376`](https://github.com/nodejs/node/commit/10e7c3a376)] - **esm**: add `initialize` hook, integrate with `register` (Izaak Schroeder) [#48842](https://github.com/nodejs/node/pull/48842)
- \[[`f96b610268`](https://github.com/nodejs/node/commit/f96b610268)] - **esm**: fix typo `parentUrl` -> `parentURL` (Antoine du Hamel) [#48999](https://github.com/nodejs/node/pull/48999)
- \[[`03c1b5e647`](https://github.com/nodejs/node/commit/03c1b5e647)] - **esm**: unflag `Module.register` and allow nested loader `import()` (Izaak Schroeder) [#48559](https://github.com/nodejs/node/pull/48559)
- \[[`63aa5d7270`](https://github.com/nodejs/node/commit/63aa5d7270)] - **esm**: add back `globalPreload` tests and fix failing ones (Antoine du Hamel) [#48779](https://github.com/nodejs/node/pull/48779)
- \[[`1c7be606f1`](https://github.com/nodejs/node/commit/1c7be606f1)] - **esm**: remove support for arrays in `import` internal method (Antoine du Hamel) [#48296](https://github.com/nodejs/node/pull/48296)
- \[[`655111fa00`](https://github.com/nodejs/node/commit/655111fa00)] - **esm**: handle `globalPreload` hook returning a nullish value (Antoine du Hamel) [#48249](https://github.com/nodejs/node/pull/48249)
- \[[`9938a8bf13`](https://github.com/nodejs/node/commit/9938a8bf13)] - **esm**: handle more error types thrown from the loader thread (Antoine du Hamel) [#48247](https://github.com/nodejs/node/pull/48247)
- \[[`8cab32a5d1`](https://github.com/nodejs/node/commit/8cab32a5d1)] - **esm**: do not use `'beforeExit'` on the main thread (Antoine du Hamel) [#47964](https://github.com/nodejs/node/pull/47964)
- \[[`b61efcce95`](https://github.com/nodejs/node/commit/b61efcce95)] - **esm**: rename `URLCanParse` to be consistent (Antoine du Hamel) [#47668](https://github.com/nodejs/node/pull/47668)
- \[[`ca20f5931d`](https://github.com/nodejs/node/commit/ca20f5931d)] - **esm**: remove support for deprecated hooks (Antoine du Hamel) [#47580](https://github.com/nodejs/node/pull/47580)
- \[[`5de37a1e37`](https://github.com/nodejs/node/commit/5de37a1e37)] - **esm**: initialize `import.meta` on eval (Antoine du Hamel) [#47551](https://github.com/nodejs/node/pull/47551)
- \[[`39fbce7313`](https://github.com/nodejs/node/commit/39fbce7313)] - **esm**: propagate `process.exit` from the loader thread to the main thread (Antoine du Hamel) [#47548](https://github.com/nodejs/node/pull/47548)
- \[[`2a528b76e6`](https://github.com/nodejs/node/commit/2a528b76e6)] - **esm**: avoid try/catch when validating urls (Yagiz Nizipli) [#47541](https://github.com/nodejs/node/pull/47541)
- \[[`bac9b1758f`](https://github.com/nodejs/node/commit/bac9b1758f)] - **esm**: move hook execution to separate thread (Jacob Smith) [#44710](https://github.com/nodejs/node/pull/44710)
- \[[`dfa444477a`](https://github.com/nodejs/node/commit/dfa444477a)] - **esm**: skip file: URL conversion to path when possible (Antoine du Hamel) [#46305](https://github.com/nodejs/node/pull/46305)
- \[[`45de8d1bd7`](https://github.com/nodejs/node/commit/45de8d1bd7)] - **esm**: allow resolve to return import assertions (Geoffrey Booth) [#46153](https://github.com/nodejs/node/pull/46153)
- \[[`5ffc90a06b`](https://github.com/nodejs/node/commit/5ffc90a06b)] - **esm**: move hooks handling into separate class (Geoffrey Booth) [#45869](https://github.com/nodejs/node/pull/45869)
- \[[`490b598dbf`](https://github.com/nodejs/node/commit/490b598dbf)] - **esm**: leverage loaders when resolving subsequent loaders (Maël Nison) [#43772](https://github.com/nodejs/node/pull/43772)
- \[[`acd987287c`](https://github.com/nodejs/node/commit/acd987287c)] - **events**: remove weak listener for event target (Raz Luvaton) [#48952](https://github.com/nodejs/node/pull/48952)
- \[[`69b7f91a92`](https://github.com/nodejs/node/commit/69b7f91a92)] - **fs**: remove redundant code in readableWebStream() (Deokjin Kim) [#49298](https://github.com/nodejs/node/pull/49298)
- \[[`ae8bb162b4`](https://github.com/nodejs/node/commit/ae8bb162b4)] - **fs**: remove redundant `nullCheck` (Livia Medeiros) [#48826](https://github.com/nodejs/node/pull/48826)
- \[[`48c25b154b`](https://github.com/nodejs/node/commit/48c25b154b)] - **fs**: make `mkdtemp` accept buffers and URL (LiviaMedeiros) [#48828](https://github.com/nodejs/node/pull/48828)
- \[[`edf46c1b59`](https://github.com/nodejs/node/commit/edf46c1b59)] - **fs**: move fs_use_promises_symbol to per-isolate symbols (Joyee Cheung) [#47768](https://github.com/nodejs/node/pull/47768)
- \[[`fe41d22afc`](https://github.com/nodejs/node/commit/fe41d22afc)] - **fs**: use kResistStopPropagation (Chemi Atlow) [#48521](https://github.com/nodejs/node/pull/48521)
- \[[`7c758f60ab`](https://github.com/nodejs/node/commit/7c758f60ab)] - **fs**: fix readdir recursive sync & callback (Ethan Arrowood) [#48698](https://github.com/nodejs/node/pull/48698)
- \[[`8874b2e11d`](https://github.com/nodejs/node/commit/8874b2e11d)] - **http**: start connections checking interval on listen (Paolo Insogna) [#48611](https://github.com/nodejs/node/pull/48611)
- \[[`29697229b6`](https://github.com/nodejs/node/commit/29697229b6)] - **https**: fix connection checking interval not clearing on server close (Nitzan Uziely) [#48383](https://github.com/nodejs/node/pull/48383)
- \[[`981aa7866d`](https://github.com/nodejs/node/commit/981aa7866d)] - **lib**: fix MIME overmatch in data URLs (André Alves) [#49104](https://github.com/nodejs/node/pull/49104)
- \[[`fe26f8a860`](https://github.com/nodejs/node/commit/fe26f8a860)] - **lib**: merge cjs and esm package json reader caches (Yagiz Nizipli) [#48477](https://github.com/nodejs/node/pull/48477)
- \[[`17246be158`](https://github.com/nodejs/node/commit/17246be158)] - **(SEMVER-MINOR)** **lib**: add api to detect whether source-maps are enabled (翠 / green) [#46391](https://github.com/nodejs/node/pull/46391)
- \[[`2e9f7284a1`](https://github.com/nodejs/node/commit/2e9f7284a1)] - **(SEMVER-MINOR)** **lib**: add tracing channel to diagnostics_channel (Stephen Belanger) [#44943](https://github.com/nodejs/node/pull/44943)
- \[[`04dad9c2f6`](https://github.com/nodejs/node/commit/04dad9c2f6)] - **lib**: fix BroadcastChannel initialization location (Shelley Vohr) [#46864](https://github.com/nodejs/node/pull/46864)
- \[[`671d2c0067`](https://github.com/nodejs/node/commit/671d2c0067)] - **lib**: fix DOMException property descriptors after being lazy loaded (Filip Skokan) [#46799](https://github.com/nodejs/node/pull/46799)
- \[[`9a4b57d6d4`](https://github.com/nodejs/node/commit/9a4b57d6d4)] - **lib**: improve esm resolve performance (Yagiz Nizipli) [#46652](https://github.com/nodejs/node/pull/46652)
- \[[`c6b2f56723`](https://github.com/nodejs/node/commit/c6b2f56723)] - **lib**: lazy-load deps in modules/run_main.js (Joyee Cheung) [#45849](https://github.com/nodejs/node/pull/45849)
- \[[`baa280a4f6`](https://github.com/nodejs/node/commit/baa280a4f6)] - **lib**: lazy-load deps in source_map_cache.js (Joyee Cheung) [#45849](https://github.com/nodejs/node/pull/45849)
- \[[`00bdb72b6e`](https://github.com/nodejs/node/commit/00bdb72b6e)] - **lib**: add getLazy() method to internal/util (Joyee Cheung) [#45849](https://github.com/nodejs/node/pull/45849)
- \[[`b77a6b2376`](https://github.com/nodejs/node/commit/b77a6b2376)] - **loader**: use default loader as cascaded loader in the in loader worker (Joyee Cheung) [#47620](https://github.com/nodejs/node/pull/47620)
- \[[`7778e190b0`](https://github.com/nodejs/node/commit/7778e190b0)] - **meta**: move Trott to TSC regular member (Rich Trott) [#50297](https://github.com/nodejs/node/pull/50297)
- \[[`5b9575dd30`](https://github.com/nodejs/node/commit/5b9575dd30)] - **meta**: ping TSC for offboarding (Tobias Nießen) [#50147](https://github.com/nodejs/node/pull/50147)
- \[[`d24c3a0692`](https://github.com/nodejs/node/commit/d24c3a0692)] - **meta**: update website team with new name (Rich Trott) [#49883](https://github.com/nodejs/node/pull/49883)
- \[[`332d2aedb4`](https://github.com/nodejs/node/commit/332d2aedb4)] - **meta**: fix linter error (Antoine du Hamel) [#49755](https://github.com/nodejs/node/pull/49755)
- \[[`dc70c444f4`](https://github.com/nodejs/node/commit/dc70c444f4)] - **meta**: add primordials strategic initiative (Benjamin Gruenbaum) [#49706](https://github.com/nodejs/node/pull/49706)
- \[[`213a9f8a4b`](https://github.com/nodejs/node/commit/213a9f8a4b)] - **meta**: bump rtCamp/action-slack-notify from 2.2.0 to 2.2.1 (dependabot\[bot]) [#49437](https://github.com/nodejs/node/pull/49437)
- \[[`2827779faa`](https://github.com/nodejs/node/commit/2827779faa)] - **meta**: remove modules team from CODEOWNERS (Benjamin Gruenbaum) [#49412](https://github.com/nodejs/node/pull/49412)
- \[[`5d6daf0d01`](https://github.com/nodejs/node/commit/5d6daf0d01)] - **meta**: add test/reporters to codeowners (Chemi Atlow) [#49186](https://github.com/nodejs/node/pull/49186)
- \[[`dee7dc5d54`](https://github.com/nodejs/node/commit/dee7dc5d54)] - **meta**: bump actions/upload-artifact from 3.1.2 to 3.1.3 (dependabot\[bot]) [#50000](https://github.com/nodejs/node/pull/50000)
- \[[`98294fdeee`](https://github.com/nodejs/node/commit/98294fdeee)] - **meta**: bump actions/cache from 3.3.1 to 3.3.2 (dependabot\[bot]) [#50003](https://github.com/nodejs/node/pull/50003)
- \[[`79270327d0`](https://github.com/nodejs/node/commit/79270327d0)] - **meta**: bump github/codeql-action from 2.21.5 to 2.21.9 (dependabot\[bot]) [#50002](https://github.com/nodejs/node/pull/50002)
- \[[`6591a03c89`](https://github.com/nodejs/node/commit/6591a03c89)] - **meta**: bump github/codeql-action from 2.21.2 to 2.21.5 (dependabot\[bot]) [#49438](https://github.com/nodejs/node/pull/49438)
- \[[`3a107f80bc`](https://github.com/nodejs/node/commit/3a107f80bc)] - **meta**: bump actions/checkout from 3.6.0 to 4.1.0 (dependabot\[bot]) [#50001](https://github.com/nodejs/node/pull/50001)
- \[[`26c5c3c4a2`](https://github.com/nodejs/node/commit/26c5c3c4a2)] - **meta**: bump actions/checkout from 3.5.3 to 3.6.0 (dependabot\[bot]) [#49436](https://github.com/nodejs/node/pull/49436)
- \[[`0fc0b88d97`](https://github.com/nodejs/node/commit/0fc0b88d97)] - **meta**: bump step-security/harden-runner from 2.5.0 to 2.5.1 (dependabot\[bot]) [#49435](https://github.com/nodejs/node/pull/49435)
- \[[`dad5785d5d`](https://github.com/nodejs/node/commit/dad5785d5d)] - **meta**: bump actions/setup-node from 3.7.0 to 3.8.1 (dependabot\[bot]) [#49434](https://github.com/nodejs/node/pull/49434)
- \[[`155a275acb`](https://github.com/nodejs/node/commit/155a275acb)] - **meta**: move one or more collaborators to emeritus (Node.js GitHub Bot) [#49264](https://github.com/nodejs/node/pull/49264)
- \[[`bd17f0b992`](https://github.com/nodejs/node/commit/bd17f0b992)] - **meta**: mention nodejs/tsc when changing GH templates (Rafael Gonzaga) [#49189](https://github.com/nodejs/node/pull/49189)
- \[[`f6e68a0238`](https://github.com/nodejs/node/commit/f6e68a0238)] - **meta**: bump github/codeql-action from 2.20.1 to 2.21.2 (dependabot\[bot]) [#48986](https://github.com/nodejs/node/pull/48986)
- \[[`5c352eeecb`](https://github.com/nodejs/node/commit/5c352eeecb)] - **meta**: bump step-security/harden-runner from 2.4.1 to 2.5.0 (dependabot\[bot]) [#48985](https://github.com/nodejs/node/pull/48985)
- \[[`42ac5a6e5f`](https://github.com/nodejs/node/commit/42ac5a6e5f)] - **meta**: bump actions/setup-node from 3.6.0 to 3.7.0 (dependabot\[bot]) [#48984](https://github.com/nodejs/node/pull/48984)
- \[[`b0d769fe7c`](https://github.com/nodejs/node/commit/b0d769fe7c)] - **meta**: bump actions/setup-python from 4.6.1 to 4.7.0 (dependabot\[bot]) [#48983](https://github.com/nodejs/node/pull/48983)
- \[[`f62b24276c`](https://github.com/nodejs/node/commit/f62b24276c)] - **meta**: add mailmap entry for atlowChemi (Chemi Atlow) [#48810](https://github.com/nodejs/node/pull/48810)
- \[[`8c55f317a3`](https://github.com/nodejs/node/commit/8c55f317a3)] - **module**: move helpers out of cjs loader (Geoffrey Booth) [#49912](https://github.com/nodejs/node/pull/49912)
- \[[`14e148ee6c`](https://github.com/nodejs/node/commit/14e148ee6c)] - **module**: ensure successful import returns the same result (Antoine du Hamel) [#46662](https://github.com/nodejs/node/pull/46662)
- \[[`65dfe85f03`](https://github.com/nodejs/node/commit/65dfe85f03)] - **module**: implement `register` utility (João Lenon) [#46826](https://github.com/nodejs/node/pull/46826)
- \[[`6f0458d0a6`](https://github.com/nodejs/node/commit/6f0458d0a6)] - **module**: refactor to use `normalizeRequirableId` in the CJS module loader (Darshan Sen) [#47896](https://github.com/nodejs/node/pull/47896)
- \[[`89ed24b94a`](https://github.com/nodejs/node/commit/89ed24b94a)] - **module**: do less CJS module loader initialization at run time (Joyee Cheung) [#47194](https://github.com/nodejs/node/pull/47194)
- \[[`939c8764b8`](https://github.com/nodejs/node/commit/939c8764b8)] - **module**: move callbacks and conditions into modules/esm/utils.js (Joyee Cheung) [#45849](https://github.com/nodejs/node/pull/45849)
- \[[`a3b0f4cf55`](https://github.com/nodejs/node/commit/a3b0f4cf55)] - **module**: move modules/cjs/helpers.js to modules/helpers.js (Joyee Cheung) [#45849](https://github.com/nodejs/node/pull/45849)
- \[[`97579895f2`](https://github.com/nodejs/node/commit/97579895f2)] - **module, esm**: jsdoc for modules files (Geoffrey Booth) [#49523](https://github.com/nodejs/node/pull/49523)
- \[[`daca87bbef`](https://github.com/nodejs/node/commit/daca87bbef)] - **net**: use asserts in JS Socket Stream to catch races in future (Tim Perry) [#49400](https://github.com/nodejs/node/pull/49400)
- \[[`03063bd022`](https://github.com/nodejs/node/commit/03063bd022)] - **net**: fix crash due to simultaneous close/shutdown on JS Stream Sockets (Tim Perry) [#49400](https://github.com/nodejs/node/pull/49400)
- \[[`67fe7d8822`](https://github.com/nodejs/node/commit/67fe7d8822)] - **net**: fix setting of value in 'setDefaultAutoSelectFamilyAttemptTimeout' (Deokjin Kim) [#47012](https://github.com/nodejs/node/pull/47012)
- \[[`f449b222fd`](https://github.com/nodejs/node/commit/f449b222fd)] - **node-api**: update headers for better wasm support (Toyo Li) [#49037](https://github.com/nodejs/node/pull/49037)
- \[[`5148f030b8`](https://github.com/nodejs/node/commit/5148f030b8)] - **node-api**: run finalizers directly from GC (Vladimir Morozov) [#42651](https://github.com/nodejs/node/pull/42651)
- \[[`edef4fa668`](https://github.com/nodejs/node/commit/edef4fa668)] - **node-api**: enable uncaught exceptions policy by default (Chengzhong Wu) [#49313](https://github.com/nodejs/node/pull/49313)
- \[[`48a1b9336b`](https://github.com/nodejs/node/commit/48a1b9336b)] - **node-api**: fix compiler warning in node_api.h (Michael Graeb) [#49103](https://github.com/nodejs/node/pull/49103)
- \[[`57966318fe`](https://github.com/nodejs/node/commit/57966318fe)] - **node-api**: avoid macro redefinition (Tobias Nießen) [#48879](https://github.com/nodejs/node/pull/48879)
- \[[`d4f26f4651`](https://github.com/nodejs/node/commit/d4f26f4651)] - **policy**: fix path to URL conversion (Antoine du Hamel) [#49133](https://github.com/nodejs/node/pull/49133)
- \[[`a625f22acb`](https://github.com/nodejs/node/commit/a625f22acb)] - **readline**: add paste bracket mode (Jakub Jankiewicz) [#47150](https://github.com/nodejs/node/pull/47150)
- \[[`bbafd42d75`](https://github.com/nodejs/node/commit/bbafd42d75)] - **repl**: display dynamic import variant in static import error messages (Hemanth HM) [#48129](https://github.com/nodejs/node/pull/48129)
- \[[`b8634eeb16`](https://github.com/nodejs/node/commit/b8634eeb16)] - **sea**: allow requiring core modules with the "node:" prefix (Darshan Sen) [#47779](https://github.com/nodejs/node/pull/47779)
- \[[`066d9d4492`](https://github.com/nodejs/node/commit/066d9d4492)] - **src**: remove unused function `GetName()` in node_perf (Jungku Lee) [#49244](https://github.com/nodejs/node/pull/49244)
- \[[`158c91a38a`](https://github.com/nodejs/node/commit/158c91a38a)] - **src**: use ARES_SUCCESS instead of 0 (Jungku Lee) [#49048](https://github.com/nodejs/node/pull/49048)
- \[[`8c33731ac6`](https://github.com/nodejs/node/commit/8c33731ac6)] - **src**: add a condition if the argument of `DomainToUnicode` is empty (Jungku Lee) [#49097](https://github.com/nodejs/node/pull/49097)
- \[[`67dba57d77`](https://github.com/nodejs/node/commit/67dba57d77)] - **src**: use ARES_SUCCESS instead of 0 (Hyunjin Kim) [#48834](https://github.com/nodejs/node/pull/48834)
- \[[`97d87495c7`](https://github.com/nodejs/node/commit/97d87495c7)] - **src**: remove unnecessary temporary creation (Jason) [#48734](https://github.com/nodejs/node/pull/48734)
- \[[`f5384c3262`](https://github.com/nodejs/node/commit/f5384c3262)] - **src**: fix nullptr access on realm (Jan Olaf Krems) [#48802](https://github.com/nodejs/node/pull/48802)
- \[[`358273d77f`](https://github.com/nodejs/node/commit/358273d77f)] - **src**: remove OnScopeLeaveImpl's move assignment overload (Jason) [#48732](https://github.com/nodejs/node/pull/48732)
- \[[`cc7bf1f641`](https://github.com/nodejs/node/commit/cc7bf1f641)] - **(SEMVER-MINOR)** **src**: add cjs_module_lexer_version base64_version (Jithil P Ponnan) [#45629](https://github.com/nodejs/node/pull/45629)
- \[[`0a950c3752`](https://github.com/nodejs/node/commit/0a950c3752)] - **src**: add missing to_ascii method in dns queries (Daniel Lemire) [#48354](https://github.com/nodejs/node/pull/48354)
- \[[`3552afb904`](https://github.com/nodejs/node/commit/3552afb904)] - **src**: fix duplication of externalized builtin code (Keyhan Vakil) [#47079](https://github.com/nodejs/node/pull/47079)
- \[[`66e4ba5062`](https://github.com/nodejs/node/commit/66e4ba5062)] - **src**: fix AliasedBuffer memory attribution in heap snapshots (Joyee Cheung) [#46817](https://github.com/nodejs/node/pull/46817)
- \[[`946f19b5e3`](https://github.com/nodejs/node/commit/946f19b5e3)] - **src**: move AliasedBuffer implementation to -inl.h (Joyee Cheung) [#46817](https://github.com/nodejs/node/pull/46817)
- \[[`d35f8702c9`](https://github.com/nodejs/node/commit/d35f8702c9)] - **src**: bootstrap prepare stack trace callback in shadow realm (Chengzhong Wu) [#47107](https://github.com/nodejs/node/pull/47107)
- \[[`3551a19205`](https://github.com/nodejs/node/commit/3551a19205)] - **src**: make BuiltinLoader threadsafe and non-global (Anna Henningsen) [#45942](https://github.com/nodejs/node/pull/45942)
- \[[`92311a0801`](https://github.com/nodejs/node/commit/92311a0801)] - **src**: define per-isolate internal bindings registration callback (Chengzhong Wu) [#45547](https://github.com/nodejs/node/pull/45547)
- \[[`629fc774ca`](https://github.com/nodejs/node/commit/629fc774ca)] - **src**: use an array for faster binding data lookup (Joyee Cheung) [#46620](https://github.com/nodejs/node/pull/46620)
- \[[`62e2e590fc`](https://github.com/nodejs/node/commit/62e2e590fc)] - **src**: fix TLSWrap lifetime bug in ALPN callback (Ben Noordhuis) [#49635](https://github.com/nodejs/node/pull/49635)
- \[[`fae60c5841`](https://github.com/nodejs/node/commit/fae60c5841)] - **stream**: use bitmap in readable state (Benjamin Gruenbaum) [#49745](https://github.com/nodejs/node/pull/49745)
- \[[`ee4fc7d78c`](https://github.com/nodejs/node/commit/ee4fc7d78c)] - **stream**: use Buffer.from when constructor is a Buffer (Matthew Aitken) [#49250](https://github.com/nodejs/node/pull/49250)
- \[[`651e4504ce`](https://github.com/nodejs/node/commit/651e4504ce)] - **stream**: add `highWaterMark` for the map operator (Raz Luvaton) [#49249](https://github.com/nodejs/node/pull/49249)
- \[[`d585d13127`](https://github.com/nodejs/node/commit/d585d13127)] - **stream**: improve WebStreams performance (Raz Luvaton) [#49089](https://github.com/nodejs/node/pull/49089)
- \[[`7f39f8e805`](https://github.com/nodejs/node/commit/7f39f8e805)] - **test**: replace forEach with for..of in test-http-perf_hooks.js (Niya Shiyas) [#49818](https://github.com/nodejs/node/pull/49818)
- \[[`2f0ffde842`](https://github.com/nodejs/node/commit/2f0ffde842)] - **test**: replace forEach with for..of in test-net-isipv4.js (Niya Shiyas) [#49822](https://github.com/nodejs/node/pull/49822)
- \[[`bbd302b5ab`](https://github.com/nodejs/node/commit/bbd302b5ab)] - **test**: replace forEach with for..of in test-http2-server (Niya Shiyas) [#49819](https://github.com/nodejs/node/pull/49819)
- \[[`128ca3e213`](https://github.com/nodejs/node/commit/128ca3e213)] - **test**: replace forEach with for..of in test-http2-client-destroy.js (Niya Shiyas) [#49820](https://github.com/nodejs/node/pull/49820)
- \[[`a2ca1a605f`](https://github.com/nodejs/node/commit/a2ca1a605f)] - **test**: print instruction for creating missing snapshot in assertSnapshot (Raz Luvaton) [#48914](https://github.com/nodejs/node/pull/48914)
- \[[`a0bb30cdca`](https://github.com/nodejs/node/commit/a0bb30cdca)] - **test**: set `test-watch-mode-inspect` as flaky (Yagiz Nizipli) [#50259](https://github.com/nodejs/node/pull/50259)
- \[[`1047d95698`](https://github.com/nodejs/node/commit/1047d95698)] - **test**: set `test-emit-after-on-destroyed` as flaky (Yagiz Nizipli) [#50246](https://github.com/nodejs/node/pull/50246)
- \[[`91a3b57962`](https://github.com/nodejs/node/commit/91a3b57962)] - **test**: set inspector async stack test as flaky (Yagiz Nizipli) [#50244](https://github.com/nodejs/node/pull/50244)
- \[[`b41aa7b82a`](https://github.com/nodejs/node/commit/b41aa7b82a)] - **test**: set test-worker-nearheaplimit-deadlock flaky (StefanStojanovic) [#50277](https://github.com/nodejs/node/pull/50277)
- \[[`e81b066fb1`](https://github.com/nodejs/node/commit/e81b066fb1)] - **test**: set `test-cli-node-options` as flaky (Yagiz Nizipli) [#50296](https://github.com/nodejs/node/pull/50296)
- \[[`0c05c25c4e`](https://github.com/nodejs/node/commit/0c05c25c4e)] - **test**: set crypto-timing test as flaky (Yagiz Nizipli) [#50232](https://github.com/nodejs/node/pull/50232)
- \[[`83e339dbba`](https://github.com/nodejs/node/commit/83e339dbba)] - **test**: set `test-structuredclone-*` as flaky (Yagiz Nizipli) [#50261](https://github.com/nodejs/node/pull/50261)
- \[[`866a399488`](https://github.com/nodejs/node/commit/866a399488)] - **test**: set inspector async hook test as flaky (Yagiz Nizipli) [#50252](https://github.com/nodejs/node/pull/50252)
- \[[`cb0bd2116b`](https://github.com/nodejs/node/commit/cb0bd2116b)] - **test**: set parallel http server test as flaky (Yagiz Nizipli) [#50227](https://github.com/nodejs/node/pull/50227)
- \[[`54f3d877ae`](https://github.com/nodejs/node/commit/54f3d877ae)] - **test**: set test-worker-nearheaplimit-deadlock flaky (Stefan Stojanovic) [#50238](https://github.com/nodejs/node/pull/50238)
- \[[`5953a255b6`](https://github.com/nodejs/node/commit/5953a255b6)] - **test**: set `test-runner-watch-mode` as flaky (Yagiz Nizipli) [#50221](https://github.com/nodejs/node/pull/50221)
- \[[`5820d7e14d`](https://github.com/nodejs/node/commit/5820d7e14d)] - **test**: deflake test-runner-output (Moshe Atlow) [#49878](https://github.com/nodejs/node/pull/49878)
- \[[`1d75da43f2`](https://github.com/nodejs/node/commit/1d75da43f2)] - _**Revert**_ "**test**: mark test-http-regr-gh-2928 as flaky" (Luigi Pinca) [#49708](https://github.com/nodejs/node/pull/49708)
- \[[`5df23520e7`](https://github.com/nodejs/node/commit/5df23520e7)] - **test**: mark test-runner-watch-mode as flaky (Joyee Cheung) [#49627](https://github.com/nodejs/node/pull/49627)
- \[[`7e714e6497`](https://github.com/nodejs/node/commit/7e714e6497)] - **test**: remove --no-warnings flag in test_runner fixtures (Raz Luvaton) [#48989](https://github.com/nodejs/node/pull/48989)
- \[[`0fea550641`](https://github.com/nodejs/node/commit/0fea550641)] - **test**: reorder test files fixtures for better understanding (Raz Luvaton) [#48787](https://github.com/nodejs/node/pull/48787)
- \[[`c5857bde42`](https://github.com/nodejs/node/commit/c5857bde42)] - **test**: avoid copying test source files (Chengzhong Wu) [#49515](https://github.com/nodejs/node/pull/49515)
- \[[`67cb6f9a34`](https://github.com/nodejs/node/commit/67cb6f9a34)] - **test**: reduce length in crypto keygen tests (Joyee Cheung) [#49221](https://github.com/nodejs/node/pull/49221)
- \[[`958e114655`](https://github.com/nodejs/node/commit/958e114655)] - **test**: split JWK async elliptic curve keygen tests (Joyee Cheung) [#49221](https://github.com/nodejs/node/pull/49221)
- \[[`344592dc00`](https://github.com/nodejs/node/commit/344592dc00)] - **test**: split test-crypto-keygen.js (Joyee Cheung) [#49221](https://github.com/nodejs/node/pull/49221)
- \[[`5f1a1aeeed`](https://github.com/nodejs/node/commit/5f1a1aeeed)] - **test**: rename test-crypto-modp1-error (Tobias Nießen) [#49348](https://github.com/nodejs/node/pull/49348)
- \[[`107015b45d`](https://github.com/nodejs/node/commit/107015b45d)] - **test**: migrate message source map tests from Python to JS (Yiyun Lei) [#49238](https://github.com/nodejs/node/pull/49238)
- \[[`ddebd29515`](https://github.com/nodejs/node/commit/ddebd29515)] - **test**: fix compiler warning in NodeCryptoEnv (Tobias Nießen) [#49206](https://github.com/nodejs/node/pull/49206)
- \[[`0261c5de56`](https://github.com/nodejs/node/commit/0261c5de56)] - **test**: make test-perf-hooks more robust and work with workers (Joyee Cheung) [#49197](https://github.com/nodejs/node/pull/49197)
- \[[`e18de802c8`](https://github.com/nodejs/node/commit/e18de802c8)] - **test**: use gcUntil() in test-v8-serialize-leak (Joyee Cheung) [#49168](https://github.com/nodejs/node/pull/49168)
- \[[`52d5dd8dba`](https://github.com/nodejs/node/commit/52d5dd8dba)] - **test**: add expectSyncExitWithoutError() and expectSyncExit() utils (Joyee Cheung) [#49020](https://github.com/nodejs/node/pull/49020)
- \[[`67925bb914`](https://github.com/nodejs/node/commit/67925bb914)] - **test**: add Symbol.dispose support to mock timers (Benjamin Gruenbaum) [#48549](https://github.com/nodejs/node/pull/48549)
- \[[`00c08539b6`](https://github.com/nodejs/node/commit/00c08539b6)] - **test**: fix edge snapshot stack traces (Geoffrey Booth) [#49659](https://github.com/nodejs/node/pull/49659)
- \[[`48f6d9a975`](https://github.com/nodejs/node/commit/48f6d9a975)] - **test**: refactor `test-node-output-errors` (Antoine du Hamel) [#48992](https://github.com/nodejs/node/pull/48992)
- \[[`c1a8ae38c0`](https://github.com/nodejs/node/commit/c1a8ae38c0)] - **test**: deflake `test-loaders-workers-spawned` (Antoine du Hamel) [#50251](https://github.com/nodejs/node/pull/50251)
- \[[`d5cc5d7956`](https://github.com/nodejs/node/commit/d5cc5d7956)] - **test**: deflake `test-esm-loader-resolve-type` (Antoine du Hamel) [#50273](https://github.com/nodejs/node/pull/50273)
- \[[`866d646b0e`](https://github.com/nodejs/node/commit/866d646b0e)] - **test**: increase coverage of `Module.register` and `initialize` hook (Antoine du Hamel) [#49532](https://github.com/nodejs/node/pull/49532)
- \[[`65201ab36b`](https://github.com/nodejs/node/commit/65201ab36b)] - **test**: isolate `globalPreload` tests (Geoffrey Booth) [#49545](https://github.com/nodejs/node/pull/49545)
- \[[`65058d9a35`](https://github.com/nodejs/node/commit/65058d9a35)] - **test**: add `tmpdir.fileURL()` (Livia Medeiros) [#49040](https://github.com/nodejs/node/pull/49040)
- \[[`e1c6f46926`](https://github.com/nodejs/node/commit/e1c6f46926)] - **test**: reduce flakiness of `test-esm-loader-hooks` (Antoine du Hamel) [#49248](https://github.com/nodejs/node/pull/49248)
- \[[`e8d475ffc4`](https://github.com/nodejs/node/commit/e8d475ffc4)] - **test**: refactor `test-esm-loader-hooks` for easier debugging (Antoine du Hamel) [#49131](https://github.com/nodejs/node/pull/49131)
- \[[`26f01669fc`](https://github.com/nodejs/node/commit/26f01669fc)] - **test**: reduce flakiness of `test-esm-loader-hooks` (Antoine du Hamel) [#49105](https://github.com/nodejs/node/pull/49105)
- \[[`d5a1153970`](https://github.com/nodejs/node/commit/d5a1153970)] - **test**: use `fixtures.fileURL` when appropriate (Antoine du Hamel) [#48990](https://github.com/nodejs/node/pull/48990)
- \[[`3424793013`](https://github.com/nodejs/node/commit/3424793013)] - **test**: fix snapshot tests when cwd contains spaces or backslashes (Antoine du Hamel) [#48959](https://github.com/nodejs/node/pull/48959)
- \[[`a9347a4635`](https://github.com/nodejs/node/commit/a9347a4635)] - **test**: order `common.mjs` in ASCII order (Antoine du Hamel) [#48960](https://github.com/nodejs/node/pull/48960)
- \[[`adb9280d8e`](https://github.com/nodejs/node/commit/adb9280d8e)] - **test**: fix some assumptions in tests (Antoine du Hamel) [#48958](https://github.com/nodejs/node/pull/48958)
- \[[`f02637e805`](https://github.com/nodejs/node/commit/f02637e805)] - **test**: fix `es-module/test-esm-initialization` (Antoine du Hamel) [#48880](https://github.com/nodejs/node/pull/48880)
- \[[`8c918e5e59`](https://github.com/nodejs/node/commit/8c918e5e59)] - **test**: relax version check with shared OpenSSL (Luigi Pinca) [#50505](https://github.com/nodejs/node/pull/50505)
- \[[`c6caf13ad5`](https://github.com/nodejs/node/commit/c6caf13ad5)] - **test**: fix crypto-dh error message for OpenSSL 3.x (Kerem Kat) [#50395](https://github.com/nodejs/node/pull/50395)
- \[[`65b41ebd1f`](https://github.com/nodejs/node/commit/65b41ebd1f)] - **test**: split test-crypto-dh to avoid timeout on slow machines in the CI (Joyee Cheung) [#49492](https://github.com/nodejs/node/pull/49492)
- \[[`7606921e1d`](https://github.com/nodejs/node/commit/7606921e1d)] - **test**: fix testsuite against zlib version 1.3 (Dominique Leuenberger) [#50364](https://github.com/nodejs/node/pull/50364)
- \[[`4f78233254`](https://github.com/nodejs/node/commit/4f78233254)] - **test**: verify tracePromise does not do runStores (Stephen Belanger) [#47349](https://github.com/nodejs/node/pull/47349)
- \[[`cb6ef74cf2`](https://github.com/nodejs/node/commit/cb6ef74cf2)] - **test**: fix IPv6 checks on IBM i (Abdirahim Musse) [#46546](https://github.com/nodejs/node/pull/46546)
- \[[`bcf97ab1b2`](https://github.com/nodejs/node/commit/bcf97ab1b2)] - **test**: fix flaky test-runner-exit-code.js (Colin Ihrig) [#46138](https://github.com/nodejs/node/pull/46138)
- \[[`f0e8ff90eb`](https://github.com/nodejs/node/commit/f0e8ff90eb)] - **test**: use an array for WPT gloablThis initialization scripts (Joyee Cheung) [#46425](https://github.com/nodejs/node/pull/46425)
- \[[`0e0dd1fe90`](https://github.com/nodejs/node/commit/0e0dd1fe90)] - **test**: adapt tests for OpenSSL 3.1 (OttoHollmann) [#47859](https://github.com/nodejs/node/pull/47859)
- \[[`e3ea906988`](https://github.com/nodejs/node/commit/e3ea906988)] - **test**: disambiguate AIX and IBM i (Richard Lau) [#48056](https://github.com/nodejs/node/pull/48056)
- \[[`088460d80c`](https://github.com/nodejs/node/commit/088460d80c)] - **test**: add os setPriority, getPriority test coverage (Wael) [#38771](https://github.com/nodejs/node/pull/38771)
- \[[`b011a498c5`](https://github.com/nodejs/node/commit/b011a498c5)] - **test,benchmark**: use `tmpdir.fileURL()` (Livia Medeiros) [#49138](https://github.com/nodejs/node/pull/49138)
- \[[`54168b7364`](https://github.com/nodejs/node/commit/54168b7364)] - **test_runner**: add test location for FileTests (Colin Ihrig) [#49999](https://github.com/nodejs/node/pull/49999)
- \[[`1d9c37161d`](https://github.com/nodejs/node/commit/1d9c37161d)] - **test_runner**: replace spurious if with else (Colin Ihrig) [#49943](https://github.com/nodejs/node/pull/49943)
- \[[`b283ae4238`](https://github.com/nodejs/node/commit/b283ae4238)] - **(SEMVER-MINOR)** **test_runner**: accept `testOnly` in `run` (Moshe Atlow) [#49753](https://github.com/nodejs/node/pull/49753)
- \[[`103c4a088a`](https://github.com/nodejs/node/commit/103c4a088a)] - **test_runner**: catch reporter errors (Moshe Atlow) [#49646](https://github.com/nodejs/node/pull/49646)
- \[[`e459598cf2`](https://github.com/nodejs/node/commit/e459598cf2)] - **test_runner**: fix test runner watch mode when no positional arguments (Moshe Atlow) [#49578](https://github.com/nodejs/node/pull/49578)
- \[[`059b1945d8`](https://github.com/nodejs/node/commit/059b1945d8)] - **(SEMVER-MINOR)** **test_runner**: add junit reporter (Moshe Atlow) [#49614](https://github.com/nodejs/node/pull/49614)
- \[[`76a35632d3`](https://github.com/nodejs/node/commit/76a35632d3)] - **test_runner**: add jsdocs to mock.js (Caio Borghi) [#49555](https://github.com/nodejs/node/pull/49555)
- \[[`ba57c24fde`](https://github.com/nodejs/node/commit/ba57c24fde)] - **test_runner**: fix invalid timer call (Erick Wendel) [#49477](https://github.com/nodejs/node/pull/49477)
- \[[`5949ae016f`](https://github.com/nodejs/node/commit/5949ae016f)] - **test_runner**: add jsdocs to MockTimers (Erick Wendel) [#49476](https://github.com/nodejs/node/pull/49476)
- \[[`429846f258`](https://github.com/nodejs/node/commit/429846f258)] - **test_runner**: fix typescript coverage (Moshe Atlow) [#49406](https://github.com/nodejs/node/pull/49406)
- \[[`760c98280e`](https://github.com/nodejs/node/commit/760c98280e)] - **test_runner**: preserve original property descriptor (Erick Wendel) [#49433](https://github.com/nodejs/node/pull/49433)
- \[[`95cc98e14b`](https://github.com/nodejs/node/commit/95cc98e14b)] - **test_runner**: expose spec reporter as newable function (Chemi Atlow) [#49184](https://github.com/nodejs/node/pull/49184)
- \[[`d83964031b`](https://github.com/nodejs/node/commit/d83964031b)] - **test_runner**: reland run global after() hook earlier (Colin Ihrig) [#49116](https://github.com/nodejs/node/pull/49116)
- \[[`d61a505546`](https://github.com/nodejs/node/commit/d61a505546)] - **(SEMVER-MINOR)** **test_runner**: expose location of tests (Colin Ihrig) [#48975](https://github.com/nodejs/node/pull/48975)
- \[[`00c70e12f7`](https://github.com/nodejs/node/commit/00c70e12f7)] - **test_runner**: dont set exit code on todo tests (Moshe Atlow) [#48929](https://github.com/nodejs/node/pull/48929)
- \[[`71f7ed728d`](https://github.com/nodejs/node/commit/71f7ed728d)] - **test_runner**: fix global after not failing the tests (Raz Luvaton) [#48913](https://github.com/nodejs/node/pull/48913)
- \[[`11fbd924ed`](https://github.com/nodejs/node/commit/11fbd924ed)] - **test_runner**: add support for setImmediate (Erick Wendel) [#49397](https://github.com/nodejs/node/pull/49397)
- \[[`fb64d415be`](https://github.com/nodejs/node/commit/fb64d415be)] - **test_runner**: fix timeout in \*Each hook failing further tests (Raz Luvaton) [#48925](https://github.com/nodejs/node/pull/48925)
- \[[`67d7faddb2`](https://github.com/nodejs/node/commit/67d7faddb2)] - **test_runner**: cleanup test timeout abort listener (Raz Luvaton) [#48915](https://github.com/nodejs/node/pull/48915)
- \[[`d217435cd3`](https://github.com/nodejs/node/commit/d217435cd3)] - **test_runner**: fix todo and only in spec reporter (Moshe Atlow) [#48929](https://github.com/nodejs/node/pull/48929)
- \[[`9f122be15a`](https://github.com/nodejs/node/commit/9f122be15a)] - **test_runner**: unwrap error message in TAP reporter (Colin Ihrig) [#48942](https://github.com/nodejs/node/pull/48942)
- \[[`3a74316624`](https://github.com/nodejs/node/commit/3a74316624)] - **test_runner**: add `__proto__` null (Raz Luvaton) [#48663](https://github.com/nodejs/node/pull/48663)
- \[[`66ea9bdb4d`](https://github.com/nodejs/node/commit/66ea9bdb4d)] - **test_runner**: fix global before not called when no global test exists (Raz Luvaton) [#48877](https://github.com/nodejs/node/pull/48877)
- \[[`0bd9704018`](https://github.com/nodejs/node/commit/0bd9704018)] - **test_runner**: use os.availableParallelism() (Colin Ihrig) [#45969](https://github.com/nodejs/node/pull/45969)
- \[[`b55eb2a8d1`](https://github.com/nodejs/node/commit/b55eb2a8d1)] - **(SEMVER-MINOR)** **test_runner**: add shards support (Raz Luvaton) [#48639](https://github.com/nodejs/node/pull/48639)
- \[[`c2575c8db0`](https://github.com/nodejs/node/commit/c2575c8db0)] - **test_runner**: fix test_runner `test:fail` event type (Ethan Arrowood) [#48854](https://github.com/nodejs/node/pull/48854)
- \[[`6b186d41cd`](https://github.com/nodejs/node/commit/6b186d41cd)] - **test_runner**: fix async callback in describe not awaited (Raz Luvaton) [#48856](https://github.com/nodejs/node/pull/48856)
- \[[`2d4511baab`](https://github.com/nodejs/node/commit/2d4511baab)] - **test_runner**: call abort on test finish (Raz Luvaton) [#48827](https://github.com/nodejs/node/pull/48827)
- \[[`05e7f28b40`](https://github.com/nodejs/node/commit/05e7f28b40)] - **(SEMVER-MINOR)** **test_runner**: add initial draft for fakeTimers (Erick Wendel) [#47775](https://github.com/nodejs/node/pull/47775)
- \[[`428301ad27`](https://github.com/nodejs/node/commit/428301ad27)] - **(SEMVER-MINOR)** **test_runner, cli**: add --test-concurrency flag (Colin Ihrig) [#49996](https://github.com/nodejs/node/pull/49996)
- \[[`4b2e258c76`](https://github.com/nodejs/node/commit/4b2e258c76)] - **test_runner,test**: fix flaky test-runner-cli-concurrency.js (Colin Ihrig) [#50108](https://github.com/nodejs/node/pull/50108)
- \[[`b5d16cd8f0`](https://github.com/nodejs/node/commit/b5d16cd8f0)] - **(SEMVER-MINOR)** **tls**: add ALPNCallback server option for dynamic ALPN negotiation (Tim Perry) [#45190](https://github.com/nodejs/node/pull/45190)
- \[[`a1c94037ab`](https://github.com/nodejs/node/commit/a1c94037ab)] - **tools**: fix --v8-non-optimized-debug for v18.x (Joyee Cheung) [#50612](https://github.com/nodejs/node/pull/50612)
- \[[`3b65c61f5c`](https://github.com/nodejs/node/commit/3b65c61f5c)] - **tools**: update lint-md-dependencies (Node.js GitHub Bot) [#50083](https://github.com/nodejs/node/pull/50083)
- \[[`07941a3609`](https://github.com/nodejs/node/commit/07941a3609)] - **tools**: update lint-md-dependencies (Node.js GitHub Bot) [#49983](https://github.com/nodejs/node/pull/49983)
- \[[`ad6e7a6270`](https://github.com/nodejs/node/commit/ad6e7a6270)] - **tools**: update lint-md-dependencies to rollup\@3.29.2 (Node.js GitHub Bot) [#49679](https://github.com/nodejs/node/pull/49679)
- \[[`3511b13c94`](https://github.com/nodejs/node/commit/3511b13c94)] - **tools**: update lint-md-dependencies to rollup\@3.29.0 unified\@11.0.3 (Node.js GitHub Bot) [#49584](https://github.com/nodejs/node/pull/49584)
- \[[`b6522a2b6b`](https://github.com/nodejs/node/commit/b6522a2b6b)] - **tools**: update lint-md-dependencies (Node.js GitHub Bot) [#49342](https://github.com/nodejs/node/pull/49342)
- \[[`01b2588acf`](https://github.com/nodejs/node/commit/01b2588acf)] - **tools**: update lint-md-dependencies (Node.js GitHub Bot) [#49253](https://github.com/nodejs/node/pull/49253)
- \[[`0cb42ccb5c`](https://github.com/nodejs/node/commit/0cb42ccb5c)] - **tools**: update lint-md-dependencies (Node.js GitHub Bot) [#49122](https://github.com/nodejs/node/pull/49122)
- \[[`caa7ecca30`](https://github.com/nodejs/node/commit/caa7ecca30)] - **tools**: update lint-md-dependencies to rollup\@3.27.2 (Node.js GitHub Bot) [#49035](https://github.com/nodejs/node/pull/49035)
- \[[`42846a02b3`](https://github.com/nodejs/node/commit/42846a02b3)] - **tools**: limit the number of auto start CIs (Antoine du Hamel) [#49067](https://github.com/nodejs/node/pull/49067)
- \[[`9cc3b2061f`](https://github.com/nodejs/node/commit/9cc3b2061f)] - **tools**: update lint-md-dependencies to rollup\@3.27.0 (Node.js GitHub Bot) [#48965](https://github.com/nodejs/node/pull/48965)
- \[[`1cbb186039`](https://github.com/nodejs/node/commit/1cbb186039)] - **tools**: update lint-md-dependencies to rollup\@3.26.3 (Node.js GitHub Bot) [#48888](https://github.com/nodejs/node/pull/48888)
- \[[`f7d6e9ba43`](https://github.com/nodejs/node/commit/f7d6e9ba43)] - **tools**: update lint-md-dependencies to @rollup/plugin-commonjs\@25.0.3 (Node.js GitHub Bot) [#48791](https://github.com/nodejs/node/pull/48791)
- \[[`a98addb626`](https://github.com/nodejs/node/commit/a98addb626)] - **tools**: skip ruff on tools/node_modules (Moshe Atlow) [#49838](https://github.com/nodejs/node/pull/49838)
- \[[`6cf8dd95d1`](https://github.com/nodejs/node/commit/6cf8dd95d1)] - **tools**: update eslint to 8.51.0 (Node.js GitHub Bot) [#50084](https://github.com/nodejs/node/pull/50084)
- \[[`b8d2a29ff5`](https://github.com/nodejs/node/commit/b8d2a29ff5)] - **tools**: update eslint to 8.50.0 (Node.js GitHub Bot) [#49989](https://github.com/nodejs/node/pull/49989)
- \[[`1c1cbf06f0`](https://github.com/nodejs/node/commit/1c1cbf06f0)] - **tools**: update eslint to 8.49.0 (Node.js GitHub Bot) [#49586](https://github.com/nodejs/node/pull/49586)
- \[[`92d21864c5`](https://github.com/nodejs/node/commit/92d21864c5)] - **tools**: update eslint to 8.48.0 (Node.js GitHub Bot) [#49343](https://github.com/nodejs/node/pull/49343)
- \[[`0940de36ee`](https://github.com/nodejs/node/commit/0940de36ee)] - **tools**: update eslint to 8.47.0 (Node.js GitHub Bot) [#49124](https://github.com/nodejs/node/pull/49124)
- \[[`4880a05fa2`](https://github.com/nodejs/node/commit/4880a05fa2)] - **tools**: update eslint to 8.46.0 (Node.js GitHub Bot) [#48966](https://github.com/nodejs/node/pull/48966)
- \[[`e9632454cd`](https://github.com/nodejs/node/commit/e9632454cd)] - **tools**: update eslint to 8.45.0 (Node.js GitHub Bot) [#48793](https://github.com/nodejs/node/pull/48793)
- \[[`ae49f319c4`](https://github.com/nodejs/node/commit/ae49f319c4)] - **tools**: drop support for osx notarization with gon (Ulises Gascón) [#50291](https://github.com/nodejs/node/pull/50291)
- \[[`014b65e9b7`](https://github.com/nodejs/node/commit/014b65e9b7)] - **tools**: use osx notarytool for future releases (Ulises Gascon) [#48701](https://github.com/nodejs/node/pull/48701)
- \[[`71c386a25c`](https://github.com/nodejs/node/commit/71c386a25c)] - **typings**: update JSDoc for `cwd` in `child_process` (LiviaMedeiros) [#49029](https://github.com/nodejs/node/pull/49029)
- \[[`4d70a2c344`](https://github.com/nodejs/node/commit/4d70a2c344)] - **typings**: sync JSDoc with the actual implementation (Hyunjin Kim) [#48853](https://github.com/nodejs/node/pull/48853)
- \[[`945609487c`](https://github.com/nodejs/node/commit/945609487c)] - **typings**: fix missing property in `ExportedHooks` (Antoine du Hamel) [#49567](https://github.com/nodejs/node/pull/49567)
- \[[`2ef80f12b9`](https://github.com/nodejs/node/commit/2ef80f12b9)] - **typings**: fix JSDoc in ESM loader modules (Antoine du Hamel) [#48424](https://github.com/nodejs/node/pull/48424)
- \[[`7fc15b6d42`](https://github.com/nodejs/node/commit/7fc15b6d42)] - **url**: fix `isURL` detection by checking `path` (Zhuo Zhang) [#48928](https://github.com/nodejs/node/pull/48928)
- \[[`916a63b124`](https://github.com/nodejs/node/commit/916a63b124)] - **url**: improve `isURL` detection (Yagiz Nizipli) [#47886](https://github.com/nodejs/node/pull/47886)
- \[[`ac27431372`](https://github.com/nodejs/node/commit/ac27431372)] - **url**: validate `pathToFileURL(path)` argument as string (LiviaMedeiros) [#49161](https://github.com/nodejs/node/pull/49161)
- \[[`f256b160bf`](https://github.com/nodejs/node/commit/f256b160bf)] - **url**: handle unicode hostname if empty (Yagiz Nizipli) [#49396](https://github.com/nodejs/node/pull/49396)
- \[[`2441415c68`](https://github.com/nodejs/node/commit/2441415c68)] - **url**: reduce `pathToFileURL` cpp calls (Yagiz Nizipli) [#48709](https://github.com/nodejs/node/pull/48709)
- \[[`227e749888`](https://github.com/nodejs/node/commit/227e749888)] - **url**: validate URL constructor arg length (Matthew Aitken) [#47513](https://github.com/nodejs/node/pull/47513)
- \[[`bf4ee17d18`](https://github.com/nodejs/node/commit/bf4ee17d18)] - **url**: validate argument length in canParse (Matthew Aitken) [#47513](https://github.com/nodejs/node/pull/47513)
- \[[`a67fa2a107`](https://github.com/nodejs/node/commit/a67fa2a107)] - **util**: add `getCwdSafe` internal util fn (João Lenon) [#48434](https://github.com/nodejs/node/pull/48434)
- \[[`e96f7ef881`](https://github.com/nodejs/node/commit/e96f7ef881)] - **(SEMVER-MINOR)** **vm**: use import attributes instead of import assertions (Antoine du Hamel) [#50141](https://github.com/nodejs/node/pull/50141)
- \[[`525de686a7`](https://github.com/nodejs/node/commit/525de686a7)] - **(SEMVER-MINOR)** **wasi**: updates required for latest uvwasi version (Michael Dawson) [#49908](https://github.com/nodejs/node/pull/49908)
- \[[`f27d505805`](https://github.com/nodejs/node/commit/f27d505805)] - **watch**: decrease debounce rate (Moshe Atlow) [#48926](https://github.com/nodejs/node/pull/48926)
- \[[`83a6d20d70`](https://github.com/nodejs/node/commit/83a6d20d70)] - **watch**: use debounce instead of throttle (Moshe Atlow) [#48926](https://github.com/nodejs/node/pull/48926)
- \[[`879b958184`](https://github.com/nodejs/node/commit/879b958184)] - **worker**: protect against user mutating well-known prototypes (Antoine du Hamel) [#49270](https://github.com/nodejs/node/pull/49270)

Windows 32-bit Installer: https://nodejs.org/dist/v18.19.0/node-v18.19.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v18.19.0/node-v18.19.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v18.19.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v18.19.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v18.19.0/node-v18.19.0.pkg \
macOS Apple Silicon 64-bit Binary: https://nodejs.org/dist/v18.19.0/node-v18.19.0-darwin-arm64.tar.gz \
macOS Intel 64-bit Binary: https://nodejs.org/dist/v18.19.0/node-v18.19.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v18.19.0/node-v18.19.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v18.19.0/node-v18.19.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v18.19.0/node-v18.19.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v18.19.0/node-v18.19.0-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v18.19.0/node-v18.19.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v18.19.0/node-v18.19.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v18.19.0/node-v18.19.0.tar.gz \
Other release files: https://nodejs.org/dist/v18.19.0/ \
Documentation: https://nodejs.org/docs/v18.19.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

cb4a41694e46e9e3d744be83fb9d4aebcf8004cfb51b9460253054b5356f686c  node-v18.19.0-aix-ppc64.tar.gz
8907c42a968765b77730fb319458d63ec4ed009265f8012097c3a052407aa99b  node-v18.19.0-darwin-arm64.tar.gz
857885257c586fb13c93d33d326ca82056ba8b7445ce27cf34dd406add04158e  node-v18.19.0-darwin-arm64.tar.xz
0a749fcdf5d6bf46e1c17b3ea01e050b4d1ec3f3073b14aa745527b45a759c74  node-v18.19.0-darwin-x64.tar.gz
4f60b40071c6a67482b99d22a5265839177b8706a2b13b493396f368f0b42b00  node-v18.19.0-darwin-x64.tar.xz
e55820818dc52fd1cf3a86380c6244a8723be2551d97d7d0768d9716b296e0dd  node-v18.19.0-headers.tar.gz
4045feaea77904480ee7e6b78913408a0335d1b78a249a492bb1979ff7feb3ed  node-v18.19.0-headers.tar.xz
4297548671897a1ba6a9a78726cb3f53458048948b4f71d597886ccf799db603  node-v18.19.0-linux-arm64.tar.gz
cf94ab72e45b855257545fec1c017bdf30a9e23611561382eaf64576b999e72d  node-v18.19.0-linux-arm64.tar.xz
dadebc4ffccc75e2185ec1d8223745c010a68f6644583eb1396c3ae1f9431b97  node-v18.19.0-linux-armv7l.tar.gz
274c8747763e5d4a764f00c931da9bb541ccf0633d21e69bf14434a5b49f35e5  node-v18.19.0-linux-armv7l.tar.xz
51ef1765099dffdb4c36545e91533aa7ec7bf3b748dac00f79f67d8229708990  node-v18.19.0-linux-ppc64le.tar.gz
80a41e72de675d5ce9336f1527822763253da932cdf4dfb599ac30a7f202bca8  node-v18.19.0-linux-ppc64le.tar.xz
83a8a3efb96fa7d9002f17d50d871a1a22453307003e57ba1a87713a2b223789  node-v18.19.0-linux-s390x.tar.gz
cd3ce5eaf1300f31329fd36e19103dfb0c5a45f7c547166959392c79c99bc864  node-v18.19.0-linux-s390x.tar.xz
153312ae9fe8684f345100e4d141a521dc542b36d8c1e09c31ac290eae98c62a  node-v18.19.0-linux-x64.tar.gz
61632bb78ee828d6e8f42adc0bc2238a6b8200007093988d3927176a372281e8  node-v18.19.0-linux-x64.tar.xz
06f3103a629dd864eca32464462bfceb0ae4f6beb134b5cdcdc6be4fecfaf594  node-v18.19.0.pkg
dd4c1dc1cb94e1e29f65a3a592247edeb8ceb23483123b0e1847d75c5f0b0c17  node-v18.19.0.tar.gz
f52b41af20596a9abd8ed75241837ec43945468221448bbf841361e2091819b6  node-v18.19.0.tar.xz
791dacc5009229c6aaea3bbb36b902b796d2408af4a1d9360417be6302b19e1f  node-v18.19.0-win-x64.7z
5311913d45e1fcc3643c58d1e3926eb85437b180f025fe5857413c9f02403645  node-v18.19.0-win-x64.zip
20f25cae81d514a0027f8e0b52bacdb2a4960298f6bfb061d29875043720ef1b  node-v18.19.0-win-x86.7z
3171148f735954bdca8adccb2200379e61142e0f5a2bdb4ca6432afb0d9951df  node-v18.19.0-win-x86.zip
31976e025ea9b9324ec5db25cf04af8b52d8c0505e8bffe9a68176c48761ad06  node-v18.19.0-x64.msi
ac65503bf86cd45cc50f50695395bb04f1ac5aa49a4214e2588ca6cac032cd5a  node-v18.19.0-x86.msi
770f43909f282d2ee020aa90fd580ee767fcdc7067bfa25d2095b56f5d74f0e9  win-x64/node.exe
e196651f3f29544b7f390de22ec170e401b75d445bc6032b0cc2848904565fdd  win-x64/node.lib
b72208250f938d07600fd5bbce2cb59af272743488e29035e6b8275a2710beb0  win-x64/node_pdb.7z
894c0d31ae63ee9f5b19a0a1c0a2028a2ce02af73230caf22c6422865b6df6c2  win-x64/node_pdb.zip
826f1377dcf11257d10fc84d1c32bab8d1e9e726ac90b6b854828a432f28faaa  win-x86/node.exe
2b92b61e4507a95f00bf7788ba156077cbf432857d17ac1da9d994cae75fe987  win-x86/node.lib
0d33381e0f60392cb6e09e92c1a37fe06b029ab5d39aac2f0ea5b7995a4da9ba  win-x86/node_pdb.7z
26136d71c36d938667520796e928a77326ff84e2f55fdd7b436ac2b6a4739037  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEj8yhP+8dDC6RAI4Jdw96mlrhVgAFAmVndD0ACgkQdw96mlrh
VgCD9A//UGfNg5jM9A1LlrwwzVy2WZWScavUItG/3UPPVyfl+XMHPAm9nllmh0bw
cAi0MWdaxhn9Iv6xoZwDJQiYotoRQi36UZ//D1Ebo1OsO+QfdT/Gvc1OvsFA+iam
5Vo1SruVidTezWE9k5f5NsCx5HyYDhio96bQfFjkpdOlPf5fEPem8q6feUR04p8H
prqr+uNJZHqTusm1Hovg7s3N//8C9MTJrNZtEetvou6/gDoWacnVj+sp/IxBj+NB
qXDv2G0FZqMPQv4V2XsCwWlXaRqIvg4loZTuwOElw64FQw/dPq4rF0MhH1JAHgUM
2fGgAfvmnd5VRniU+AfvCyaXFebEFfYNHeXW0y2mNJn5XDQe458XsQLyFvpba1By
lnTsfHlKvwCdKEqyWgfys+fixDJKBfNvt/ubpktfD3gYhl8rVGfCFiyNfTSQXRyI
+7zxwMfhkwQNl+eO9tp6fKokJYkKEuNLKowIbpWY5FIM7GxwUwRXPvClvgkstnMA
SCC4fmwqyZuHz489xSJQ9uLTamv5A9RVP2ph0ATJ6SMZvtWdjhiQM7Z6SNtHIRXS
yqoMYPpsExT99lFsrK1WsO/P48pDT2scSLcZUSMgtmvQcMlmVDhxV/kXjR/Tv+ld
Ui+ZRzyr87bgpt3XK2xk9EJgbp9WydJUnWjdNNLf9GWxl+SR3tI=
=9UBU
-----END PGP SIGNATURE-----
```
