---
date: '2017-03-21T22:06:12.949Z'
category: release
title: Node v7.7.4 (Current)
layout: blog-post
author: <PERSON>
---

### Notable changes

Thank you to @italoaca<PERSON><PERSON> for preparing the majority of this release.

- **deps**: Upgraded internal node-inspect version to 1.10.6, containing several fixes. (<PERSON>) [#11869](https://github.com/nodejs/node/pull/11869)
- **inspector**: Use proper WebSockets URLs when bound to 0.0.0.0 (<PERSON>) [#11850](https://github.com/nodejs/node/pull/11850)
- **tls**: Fixed a segfault when the handle was destroyed after a partial read. (<PERSON>) [#11898](https://github.com/nodejs/node/pull/11898)

### Commits

- [[`f48763c5b9`](https://github.com/nodejs/node/commit/f48763c5b9)] - **benchmark**: remove benchmarks forced optimizations (<PERSON><PERSON><PERSON>)
- [[`dcac2d8f04`](https://github.com/nodejs/node/commit/dcac2d8f04)] - **benchmark**: benchmark comparing forEach with for (<PERSON> Snell) [#11582](https://github.com/nodejs/node/pull/11582)
- [[`80949f3d88`](https://github.com/nodejs/node/commit/80949f3d88)] - **build**: add cpp linting to windows build (liusi) [#11856](https://github.com/nodejs/node/pull/11856)
- [[`5244ee346b`](https://github.com/nodejs/node/commit/5244ee346b)] - **build**: mac OBJ_DIR should point to obj.target (Daniel Bevenius) [#11857](https://github.com/nodejs/node/pull/11857)
- [[`5b1d61ce09`](https://github.com/nodejs/node/commit/5b1d61ce09)] - **child_process**: fix deoptimizing use of arguments (Vse Mozhet Byt) [#11748](https://github.com/nodejs/node/pull/11748)
- [[`ca319862fd`](https://github.com/nodejs/node/commit/ca319862fd)] - **deps**: cherry-pick ca0f9573 from V8 upstream (Ali Ijaz Sheikh)
- [[`a7e4b029da`](https://github.com/nodejs/node/commit/a7e4b029da)] - **deps**: Add node-inspect 1.10.6 (Jan Krems) [#11869](https://github.com/nodejs/node/pull/11869)
- [[`0c00b655d8`](https://github.com/nodejs/node/commit/0c00b655d8)] - **doc**: Fix #7065: cli help documentation for --inspect (Noj Vek) [#11660](https://github.com/nodejs/node/pull/11660)
- [[`60ad7af65e`](https://github.com/nodejs/node/commit/60ad7af65e)] - **doc**: deprecate debug protocol (Jan Krems) [#10320](https://github.com/nodejs/node/pull/10320)
- [[`a5f7393541`](https://github.com/nodejs/node/commit/a5f7393541)] - **doc**: add vsemozhetbyt to collaborators (Vse Mozhet Byt) [#11932](https://github.com/nodejs/node/pull/11932)
- [[`0c091262bd`](https://github.com/nodejs/node/commit/0c091262bd)] - **doc**: add note that vm module is not a security mechanism (Ruslan Bekenev) [#11557](https://github.com/nodejs/node/pull/11557)
- [[`6d6a65e2ad`](https://github.com/nodejs/node/commit/6d6a65e2ad)] - **doc**: linkable commit message guidelines (Sam Roberts) [#11792](https://github.com/nodejs/node/pull/11792)
- [[`7c7228ed4b`](https://github.com/nodejs/node/commit/7c7228ed4b)] - **doc**: gcc version is at least 4.8.5 in BUILDING.md (detailyang) [#11840](https://github.com/nodejs/node/pull/11840)
- [[`9861ec93d4`](https://github.com/nodejs/node/commit/9861ec93d4)] - **doc**: increase Buffer.concat() documentation (cjihrig) [#11845](https://github.com/nodejs/node/pull/11845)
- [[`54879ab7d1`](https://github.com/nodejs/node/commit/54879ab7d1)] - **doc**: fix mistakes in stream doc (object mode) (Christian d'Heureuse) [#11807](https://github.com/nodejs/node/pull/11807)
- [[`78ca15dd78`](https://github.com/nodejs/node/commit/78ca15dd78)] - **doc**: argument types for dns methods (Amelia Clarke) [#11764](https://github.com/nodejs/node/pull/11764)
- [[`e84e33c87c`](https://github.com/nodejs/node/commit/e84e33c87c)] - **doc**: fix a typo in api/process.md (Gaara) [#11780](https://github.com/nodejs/node/pull/11780)
- [[`75fcf53173`](https://github.com/nodejs/node/commit/75fcf53173)] - **doc**: missing argument types for events methods (Amelia Clarke) [#11802](https://github.com/nodejs/node/pull/11802)
- [[`ae52b63df2`](https://github.com/nodejs/node/commit/ae52b63df2)] - **doc**: correct comment error in stream.md (Alexander) [#11804](https://github.com/nodejs/node/pull/11804)
- [[`e6f113d3d5`](https://github.com/nodejs/node/commit/e6f113d3d5)] - **doc**: console.log() -> console.error() in events.md (Vse Mozhet Byt) [#11810](https://github.com/nodejs/node/pull/11810)
- [[`cde5d71db1`](https://github.com/nodejs/node/commit/cde5d71db1)] - **doc**: var -> let / const in events.md (Vse Mozhet Byt) [#11810](https://github.com/nodejs/node/pull/11810)
- [[`d0fb578d64`](https://github.com/nodejs/node/commit/d0fb578d64)] - **fs**: avoid using forEach (James M Snell) [#11582](https://github.com/nodejs/node/pull/11582)
- [[`14e3ad0c5e`](https://github.com/nodejs/node/commit/14e3ad0c5e)] - **inspector**: proper WS URLs when bound to 0.0.0.0 (Eugene Ostroukhov) [#11850](https://github.com/nodejs/node/pull/11850)
- [[`fbbcd1aa89`](https://github.com/nodejs/node/commit/fbbcd1aa89)] - **lib**: Fix swallowed events in inspect integration (Jan Krems) [#11869](https://github.com/nodejs/node/pull/11869)
- [[`9cc712ca18`](https://github.com/nodejs/node/commit/9cc712ca18)] - **lib**: remove unused msg parameter in debug_agent (mr-spd) [#11833](https://github.com/nodejs/node/pull/11833)
- [[`77c69f7ace`](https://github.com/nodejs/node/commit/77c69f7ace)] - **lib, test**: add duplicate symbol checking in E() (DavidCai) [#11829](https://github.com/nodejs/node/pull/11829)
- [[`7e230727fc`](https://github.com/nodejs/node/commit/7e230727fc)] - **module**: avoid using forEach (James M Snell) [#11582](https://github.com/nodejs/node/pull/11582)
- [[`c0a2e02f51`](https://github.com/nodejs/node/commit/c0a2e02f51)] - **net**: avoid using forEach (James M Snell) [#11582](https://github.com/nodejs/node/pull/11582)
- [[`a0b1aa1161`](https://github.com/nodejs/node/commit/a0b1aa1161)] - **readline**: avoid using forEach (James M Snell) [#11582](https://github.com/nodejs/node/pull/11582)
- [[`e19ca8ba11`](https://github.com/nodejs/node/commit/e19ca8ba11)] - **readline**: remove unneeded eslint-disable comment (Rich Trott) [#11836](https://github.com/nodejs/node/pull/11836)
- [[`62e726109a`](https://github.com/nodejs/node/commit/62e726109a)] - **repl**: avoid using forEach (James M Snell) [#11582](https://github.com/nodejs/node/pull/11582)
- [[`90be5a1f19`](https://github.com/nodejs/node/commit/90be5a1f19)] - **stream**: avoid using forEach (James M Snell) [#11582](https://github.com/nodejs/node/pull/11582)
- [[`2cab00aec0`](https://github.com/nodejs/node/commit/2cab00aec0)] - **test**: fix assertion in vm test (AnnaMag) [#11862](https://github.com/nodejs/node/pull/11862)
- [[`8bda7b8d39`](https://github.com/nodejs/node/commit/8bda7b8d39)] - **test**: add coverage for child_process bounds check (Rich Trott) [#11800](https://github.com/nodejs/node/pull/11800)
- [[`3ae58acd29`](https://github.com/nodejs/node/commit/3ae58acd29)] - **test**: failing behaviour on sandboxed Proxy (AnnaMag) [#11671](https://github.com/nodejs/node/pull/11671)
- [[`560d8eed9a`](https://github.com/nodejs/node/commit/560d8eed9a)] - **test**: delay child exit in AIX for pseudo-tty tests (Gireesh Punathil) [#11715](https://github.com/nodejs/node/pull/11715)
- [[`f9c831f4b1`](https://github.com/nodejs/node/commit/f9c831f4b1)] - **test**: fix flaky test-domain-abort-on-uncaught (Rich Trott) [#11817](https://github.com/nodejs/node/pull/11817)
- [[`2649dab274`](https://github.com/nodejs/node/commit/2649dab274)] - **test**: added test for indexed properties (AnnaMag) [#11769](https://github.com/nodejs/node/pull/11769)
- [[`2df662c95a`](https://github.com/nodejs/node/commit/2df662c95a)] - **test**: test resolveObject with an empty path (Daijiro Wachi) [#11811](https://github.com/nodejs/node/pull/11811)
- [[`d2c9111614`](https://github.com/nodejs/node/commit/d2c9111614)] - **test**: fix repl-function-redefinition-edge-case (Alexey Orlenko) [#11772](https://github.com/nodejs/node/pull/11772)
- [[`c9cf922248`](https://github.com/nodejs/node/commit/c9cf922248)] - **test**: add regex to assert.throws (Matej Krajčovič) [#11815](https://github.com/nodejs/node/pull/11815)
- [[`5f6025ba68`](https://github.com/nodejs/node/commit/5f6025ba68)] - **test**: fail when child dies in fork-net (Joyee Cheung) [#11684](https://github.com/nodejs/node/pull/11684)
- [[`c626734409`](https://github.com/nodejs/node/commit/c626734409)] - **tls**: fix segfault on destroy after partial read (Ben Noordhuis) [#11898](https://github.com/nodejs/node/pull/11898)
- [[`646ee559df`](https://github.com/nodejs/node/commit/646ee559df)] - **tls**: avoid using forEach (James M Snell) [#11582](https://github.com/nodejs/node/pull/11582)
- [[`540830116b`](https://github.com/nodejs/node/commit/540830116b)] - **tls**: keep track of stream that is closed (jBarz) [#11776](https://github.com/nodejs/node/pull/11776)
- [[`9a59913039`](https://github.com/nodejs/node/commit/9a59913039)] - **util**: avoid using forEach (James M Snell) [#11582](https://github.com/nodejs/node/pull/11582)

Windows 32-bit Installer: https://nodejs.org/dist/v7.7.4/node-v7.7.4-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v7.7.4/node-v7.7.4-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v7.7.4/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v7.7.4/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v7.7.4/node-v7.7.4.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v7.7.4/node-v7.7.4-darwin-x64.tar.gz \
Linux 32-bit Binary: https://nodejs.org/dist/v7.7.4/node-v7.7.4-linux-x86.tar.xz \
Linux 64-bit Binary: https://nodejs.org/dist/v7.7.4/node-v7.7.4-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v7.7.4/node-v7.7.4-linux-ppc64le.tar.xz \
Linux PPC BE 64-bit Binary: https://nodejs.org/dist/v7.7.4/node-v7.7.4-linux-ppc64.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v7.7.4/node-v7.7.4-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v7.7.4/node-v7.7.4-aix-ppc64.tar.gz \
SmartOS 32-bit Binary: https://nodejs.org/dist/v7.7.4/node-v7.7.4-sunos-x86.tar.xz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v7.7.4/node-v7.7.4-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v7.7.4/node-v7.7.4-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v7.7.4/node-v7.7.4-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v7.7.4/node-v7.7.4-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v7.7.4/node-v7.7.4.tar.gz \
Other release files: https://nodejs.org/dist/v7.7.4/ \
Documentation: https://nodejs.org/docs/v7.7.4/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

0d7cb85072d99e47305fbdf31541704557038a8811d4ba1d7a76f68cfddedf78  node-v7.7.4-aix-ppc64.tar.gz
901ba252ca9bc3b41c5a5999409308b202143fc5b0b24d9da9575e231214dd70  node-v7.7.4-darwin-x64.tar.gz
44f3bffbe8c102e4b8980ba80f34e2e5f6bada2f1600975df1307e75d96f8b53  node-v7.7.4-darwin-x64.tar.xz
6702f3590b985fb4d6cdd49a0dba9a43d864c5bfaecaa27d8595d9c43d837bdc  node-v7.7.4-headers.tar.gz
7ed02ad944f0b2cf102216b1532b8a31d26d4173a1791a38fc2391887377f6d7  node-v7.7.4-headers.tar.xz
3c56a567f42a8a409b505459acae5c3dbd08daa8c8f8da71876a4511f55f57a9  node-v7.7.4-linux-arm64.tar.gz
c2a57b7539dd30adbe87af57dccfaa6061955e1aae391c03df297fbfb257bf71  node-v7.7.4-linux-arm64.tar.xz
377b1d9b23cd9931185b7c74aed469e78c80135beb4c8d7cff243ae7ba1ac70f  node-v7.7.4-linux-armv6l.tar.gz
8b231ba000b7a447b287d03fffe7613a575f037df8b5f569c37b289cc0ebc995  node-v7.7.4-linux-armv6l.tar.xz
77e36e4b27d571c03215c9a73cd4e443bd2f9158c5b03e15ea787a9352cee4bb  node-v7.7.4-linux-armv7l.tar.gz
755d6fde58f820c72a9b3a79c6bde4899d9e8ff201f46b765a1821f6414bbb64  node-v7.7.4-linux-armv7l.tar.xz
986da6138629d0157e66ea648bd59a5ba72d9e23aa2d5d14eb1940392ca3e5e6  node-v7.7.4-linux-ppc64le.tar.gz
f8fb7c00ec65353158e8a7d60527864aa874e410f126f87946f0606be3c456b0  node-v7.7.4-linux-ppc64le.tar.xz
4be66527b9662d499d023aea3dbce4a22d1324b90cd45568301afe671c994285  node-v7.7.4-linux-ppc64.tar.gz
3ad5ed233d7b27787858fe93db1e9649bb30ebd81fa07e2be4cf675acfd6bb62  node-v7.7.4-linux-ppc64.tar.xz
db245b83e8108b514fa84f3475c56d811fa085024e4d58f7ac444e744d94831f  node-v7.7.4-linux-s390x.tar.gz
43388bb03dcbf04185798ace86c410fe22b44390d40be02db2e3bd53cf67eb08  node-v7.7.4-linux-s390x.tar.xz
419dab870cb5c5bff95a08d7ef90a07717457e9a0eba8efff72d6ff6b91a01cb  node-v7.7.4-linux-x64.tar.gz
c1eda5dd5e5b5bce83edaaa4e2eb0ef9ea73dee55ca95ad6f8d0c43d162814d0  node-v7.7.4-linux-x64.tar.xz
6016d16e04519c97377069da8f97055d7b355cc23901468064244b37d7481c1e  node-v7.7.4-linux-x86.tar.gz
39147203d1c923fab33ff2f4fba0228ad09aab30abacc33cd4c4e985819fa7f1  node-v7.7.4-linux-x86.tar.xz
21acb3a63a097f1cd00135d845502de8eb5793e8d7b31d63689f4c223cfa0180  node-v7.7.4.pkg
15958208d0156322e6a626f77a1c961c0d2047f4b6bd601cdd538bb1db93beff  node-v7.7.4-sunos-x64.tar.gz
e319597cb0e9bf10c3a3b50c1774102dd3974eef7359febdc204ed3b17068748  node-v7.7.4-sunos-x64.tar.xz
ddc74e34e9bba547a6cf8f44171fc1de51a5ea82c36c704533259531a8c155d2  node-v7.7.4-sunos-x86.tar.gz
1fae5ea71216f32439634e3b34fd31810ad75aaa37edb8660f84afefc82e2276  node-v7.7.4-sunos-x86.tar.xz
d76bad6e843005aa016f285e983493e344fde80eac4258b4bf9ee8654f5d6e43  node-v7.7.4.tar.gz
807c61b1e90a6fd8af3f3b5c1929effa4e1cb4569e7a88357b73197feeba5719  node-v7.7.4.tar.xz
715f5873d08cff372392c2a318c8fae48c0c817c298f36367dda9d16d5ef6a35  node-v7.7.4-win-x64.7z
dd573367cda68db3594544b973be2367c0df8fc5345402672079e6be873931cd  node-v7.7.4-win-x64.zip
f35b623a1236c367c9f316c37d5e1e829e49548a723f00e3c0433541cb8fbe7c  node-v7.7.4-win-x86.7z
9709bb87735c4a82ec4d23de001549cd4a1eebbc9cc6f6cf2fdf305ea8b53dd2  node-v7.7.4-win-x86.zip
55738bb03d48318fe505847eb4675debe8bf90adb1a572ad018b10702ea40819  node-v7.7.4-x64.msi
2888f2303bcaa35f05b3dce7cbfee58af77dcea6bed4b9ff549b181c65eb4565  node-v7.7.4-x86.msi
7b2eff667c37db90eff8fe14c8fb86551b9b54afa401ad137202c7a23bcaa149  win-x64/node.exe
327be9c7a75340caf23c69c39b240e3ffe02d5584ea0da64ec784565e7e8cdbe  win-x64/node.lib
7922432be1d095c343f203a8d4295bde30f0ec3161ee6844742ee1428f836cba  win-x64/node_pdb.7z
5f13c17cf71e5c1d6900bfaf8a09130e15edfea8ee71bdf9132ff762501394ef  win-x64/node_pdb.zip
4210e52d42edede4cd2c244eb58f1b4491b9926fd636df4790074f5090d2b486  win-x86/node.exe
2c70bfa4fd0a5ca4707d0afe87dc901febb57bf18ca7bf40e5e8dca59add048d  win-x86/node.lib
e92eeee20e847ad5a02601d98d79db530e2c62c522980370aef621fd86798e8c  win-x86/node_pdb.7z
5381a27c0717817db26dc4825380096fe2b46dd0f649a8409e81165cab3c81cd  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----
Comment: GPGTools - https://gpgtools.org

iQIcBAEBCAAGBQJY0aLsAAoJEHQ0OQvb6bnFPwgP/0bl2IUNQ0lGhcr1RLwkc6WS
twKTpiLe0qibnmnwA3w1FsSZiBR+WyRJegAkWKx+ewRuiagEcdi0XJ/VJXDyFgtT
R1b4h0XovqZrjJUISaounvHZS8M6AZ/yyQrBUoAqjRLX4Qdt2Z6Iz8qya6C+9pRA
4cZyrNojSSRQYbEhkT4otMkVyHOSMDd3Ul+P1F57q7acK62RSd01u/p/ucHuzbB5
X6TYZF+jRM1NKOhypqadNL7LsRgbUj3XP6X/8dDJ6SMDOojPdvAdy42Br2A3z7N5
aSktcM1iq3N/GQdiuWU0r8b+dLevsIzGi1+2k+/rr6apJToYWncDM81yUKiVpWbs
oSNt8kpMBJ7vT/Jv9A7hDSUo28G7tlY2RnPh41TDf9fVsXQFQJTb3JL/cfrD5bH3
h8AWX8MxAVtC213cdE7l/5BunBhMPn6JfHhMbqeLd1auQsfwVHY8DTnMP6ZLlN0l
mJNh11tSKSqFjY/K1MQB3nNF27zMSECxFTqoUGpcNlTItpeHqEoA8vJpWZvSzfQE
s4HdHzxpak1klLYmTDrWMX33q6JclXnM3yAyOe6n5ZJekvAj93wIq/gAoTeRlxxN
riaeSkVbFJZYZtidRXNLcPjXhR6ogxYyLw9vJ3nRmm9GiyETb0sd7c0KiY5GKbTC
ao/GTsImOuVfgq032ug9
=WA5+
-----END PGP SIGNATURE-----

```
