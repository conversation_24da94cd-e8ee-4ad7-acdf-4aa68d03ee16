---
date: '2016-01-12T23:59:28.459Z'
category: release
title: Node v5.4.1 (Current)
layout: blog-post
author: <PERSON><PERSON>
---

### Notable Changes

- Minor performance improvements:
  - **module**: move unnecessary work for early return (<PERSON><PERSON>) [#3579](https://github.com/nodejs/node/pull/3579)
- Various bug fixes
- Various doc fixes
- Various test improvements

### Known issues

- Surrogate pair in REPL can freeze terminal. [#690](https://github.com/nodejs/node/issues/690)
- Calling `dns.setServers()` while a DNS query is in progress can cause the process to crash on a failed assertion. [#894](https://github.com/nodejs/node/issues/894)
- `url.resolve` may transfer the auth portion of the url when resolving between two full hosts, see [#1435](https://github.com/nodejs/node/issues/1435).
- Unicode characters in filesystem paths are not handled consistently across platforms or Node.js APIs. See [#2088](https://github.com/nodejs/node/issues/2088), [#3401](https://github.com/nodejs/node/issues/3401) and [#3519](https://github.com/nodejs/node/issues/3519).

### Commits

- [[`ff539c5bb5`](https://github.com/nodejs/node/commit/ff539c5bb5)] - **cluster**: ignore queryServer msgs on disconnection (Santiago Gimeno) [#4465](https://github.com/nodejs/node/pull/4465)
- [[`00148b3de1`](https://github.com/nodejs/node/commit/00148b3de1)] - **deps**: backport 066747e from upstream V8 (Ali Ijaz Sheikh) [#4625](https://github.com/nodejs/node/pull/4625)
- [[`3912b5cbda`](https://github.com/nodejs/node/commit/3912b5cbda)] - **doc**: adds usage of readline line-by-line parsing (Robert Jefe Lindstaedt) [#4609](https://github.com/nodejs/node/pull/4609)
- [[`102fb7d3a1`](https://github.com/nodejs/node/commit/102fb7d3a1)] - **doc**: remove "above" and "below" references (Richard Sun) [#4499](https://github.com/nodejs/node/pull/4499)
- [[`df87176ae0`](https://github.com/nodejs/node/commit/df87176ae0)] - **doc**: update stylesheet to match frontpage (Roman Reiss) [#4621](https://github.com/nodejs/node/pull/4621)
- [[`ede98d1f98`](https://github.com/nodejs/node/commit/ede98d1f98)] - **doc**: stronger suggestion for userland assert (Wyatt Preul) [#4535](https://github.com/nodejs/node/pull/4535)
- [[`fdfc72c977`](https://github.com/nodejs/node/commit/fdfc72c977)] - **doc**: label http.IncomingMessage as a Class (Sequoia McDowell) [#4589](https://github.com/nodejs/node/pull/4589)
- [[`b181e26975`](https://github.com/nodejs/node/commit/b181e26975)] - **doc**: document http's server.listen return value (Sequoia McDowell) [#4590](https://github.com/nodejs/node/pull/4590)
- [[`97aaeb8519`](https://github.com/nodejs/node/commit/97aaeb8519)] - **doc**: fix description about the latest-codename (Minwoo Jung) [#4583](https://github.com/nodejs/node/pull/4583)
- [[`0126615d1e`](https://github.com/nodejs/node/commit/0126615d1e)] - **doc**: add Evan Lucas to Release Team (Evan Lucas) [#4579](https://github.com/nodejs/node/pull/4579)
- [[`ec73c69412`](https://github.com/nodejs/node/commit/ec73c69412)] - **doc**: add Myles Borins to Release Team (Myles Borins) [#4578](https://github.com/nodejs/node/pull/4578)
- [[`e703c9a4e2`](https://github.com/nodejs/node/commit/e703c9a4e2)] - **doc**: bring releases.md up to date (cjihrig) [#4540](https://github.com/nodejs/node/pull/4540)
- [[`ac1108d5e7`](https://github.com/nodejs/node/commit/ac1108d5e7)] - **doc**: add missing backtick for readline (Brian White) [#4549](https://github.com/nodejs/node/pull/4549)
- [[`09bc0c6a05`](https://github.com/nodejs/node/commit/09bc0c6a05)] - **doc**: improvements to crypto.markdown copy (James M Snell) [#4435](https://github.com/nodejs/node/pull/4435)
- [[`787c5d96bd`](https://github.com/nodejs/node/commit/787c5d96bd)] - **http**: remove variable redeclaration (Rich Trott) [#4612](https://github.com/nodejs/node/pull/4612)
- [[`145b66820f`](https://github.com/nodejs/node/commit/145b66820f)] - **module**: move unnecessary work for early return (Andres Suarez) [#3579](https://github.com/nodejs/node/pull/3579)
- [[`ffb7deb443`](https://github.com/nodejs/node/commit/ffb7deb443)] - **net**: remove hot path comment from connect (Evan Lucas) [#4648](https://github.com/nodejs/node/pull/4648)
- [[`799aa74d90`](https://github.com/nodejs/node/commit/799aa74d90)] - **net**: fix dns lookup for android (Josh Dague) [#4580](https://github.com/nodejs/node/pull/4580)
- [[`9accebe087`](https://github.com/nodejs/node/commit/9accebe087)] - **net, doc**: fix line wrapping lint in net.js (James M Snell) [#4588](https://github.com/nodejs/node/pull/4588)
- [[`37a546b490`](https://github.com/nodejs/node/commit/37a546b490)] - **src**: remove redeclarations of variables (Rich Trott) [#4605](https://github.com/nodejs/node/pull/4605)
- [[`b515ccc2a1`](https://github.com/nodejs/node/commit/b515ccc2a1)] - **stream**: remove useless if test in transform (zoubin) [#4617](https://github.com/nodejs/node/pull/4617)
- [[`ea6e26d904`](https://github.com/nodejs/node/commit/ea6e26d904)] - **test**: remove duplicate fork module import (Rich Trott) [#4634](https://github.com/nodejs/node/pull/4634)
- [[`b14b2aec5e`](https://github.com/nodejs/node/commit/b14b2aec5e)] - **test**: require common module only once (Rich Trott) [#4611](https://github.com/nodejs/node/pull/4611)
- [[`f28a640505`](https://github.com/nodejs/node/commit/f28a640505)] - **test**: only include http module once (Rich Trott) [#4606](https://github.com/nodejs/node/pull/4606)
- [[`6f9a96f497`](https://github.com/nodejs/node/commit/6f9a96f497)] - **test**: fix flaky unrefed timers test (Rich Trott) [#4599](https://github.com/nodejs/node/pull/4599)
- [[`b70eec8f7b`](https://github.com/nodejs/node/commit/b70eec8f7b)] - **tls_legacy**: do not read on OpenSSL's stack (Fedor Indutny) [#4624](https://github.com/nodejs/node/pull/4624)

Windows 32-bit Installer: https://nodejs.org/dist/v5.4.1/node-v5.4.1-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v5.4.1/node-v5.4.1-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v5.4.1/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v5.4.1/win-x64/node.exe \
Mac OS X 64-bit Installer: https://nodejs.org/dist/v5.4.1/node-v5.4.1.pkg \
Mac OS X 64-bit Binary: https://nodejs.org/dist/v5.4.1/node-v5.4.1-darwin-x64.tar.gz \
Linux 32-bit Binary: https://nodejs.org/dist/v5.4.1/node-v5.4.1-linux-x86.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v5.4.1/node-v5.4.1-linux-x64.tar.gz \
SmartOS 32-bit Binary: https://nodejs.org/dist/v5.4.1/node-v5.4.1-sunos-x86.tar.gz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v5.4.1/node-v5.4.1-sunos-x64.tar.gz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v5.4.1/node-v5.4.1-linux-armv6l.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v5.4.1/node-v5.4.1-linux-armv7l.tar.gz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v5.4.1/node-v5.4.1-linux-arm64.tar.gz \
Source Code: https://nodejs.org/dist/v5.4.1/node-v5.4.1.tar.gz \
Other release files: https://nodejs.org/dist/v5.4.1/ \
Documentation: https://nodejs.org/docs/v5.4.1/api/

Shasums (GPG signing hash: SHA512, file hash: SHA256):

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA512

c523472a5972823e8b6baf2419f837885321c772612ec508a65614c758e25a46  node-v5.4.1-darwin-x64.tar.gz
c4c721d7ef2fba4e5b21dfc8017bda10c8a4d8ff322f7545e74943ca9828ee89  node-v5.4.1-darwin-x64.tar.xz
fd91d8fcbe72c76d1e58ddb3ba8bc08073c8c1e6d595bfe4b8b22e9b92855ed7  node-v5.4.1-headers.tar.gz
17424fc015dc668480537fd32f67f4d85d6e78e37f87d5eef58ef12d40bcd335  node-v5.4.1-headers.tar.xz
362ae4539b6be075b6757ba689f0ae522cfc9340c81061aca880f92fce9595c7  node-v5.4.1-linux-arm64.tar.gz
40f8e6485759a5c5f166745f7d8f33285ef425d2fe569d085220fd954fb790b9  node-v5.4.1-linux-arm64.tar.xz
21617e86758f1f95a3b9444be965aa87907410d786529cfd6aa2169ab7b5e15b  node-v5.4.1-linux-armv6l.tar.gz
2698fdd1bcfb2234f65b786c0a46ac29bf2c301c992fd72045fd03553a57e298  node-v5.4.1-linux-armv6l.tar.xz
af36dff32934dea9446673a5453efcda6e4621ce7fa73e0a401f1444c2f929af  node-v5.4.1-linux-armv7l.tar.gz
09b346ee665e3510fe8679df95bc32e8dda5953e05807c21816a25b9f5969dd4  node-v5.4.1-linux-armv7l.tar.xz
1880f3421da5579678803a523c314b345f5db00799b51b7fd9484a3248efc068  node-v5.4.1-linux-x64.tar.gz
9d264641df98742246bfd868e84cf98f649077f31eeae58931a31b0b5eee05a0  node-v5.4.1-linux-x64.tar.xz
184790d2ec8f95a75e7e746b3007e848aeba91be14ad7cec415b425df0df92e5  node-v5.4.1-linux-x86.tar.gz
a18707f7ac559563220e3319f251a2a5479b8445538f757df3a6c2dead007f1b  node-v5.4.1-linux-x86.tar.xz
059aae464e48cc11c5af1a12f47e6859da28b0fcf794f5c33d14ab7235fee1db  node-v5.4.1.pkg
956b3b0790b5742765966c6851e43c6fca298ecfb8c7102fd87e5fb6294bebd4  node-v5.4.1-sunos-x64.tar.gz
5644e5998d58bfac337a7c8672de68ec0a6dc55366a098b2364b9fbad6b345d8  node-v5.4.1-sunos-x64.tar.xz
a3eb11d58760b4b9007ac882c02e7608549fe7f65124ca90a387ba71c524b3e2  node-v5.4.1-sunos-x86.tar.gz
b8e78738f2714e34705eebd207400af422cee0842e7c599ab5077a4e51ed8453  node-v5.4.1-sunos-x86.tar.xz
78455ef2e3dea06b7d13d393c36711009048a91e5de5892523ec4a9be5a55e0c  node-v5.4.1.tar.gz
4e7d3fdfeac85c46ddaef0224695620f218e7a178e97d6e210d6d643483f1d53  node-v5.4.1.tar.xz
a65a1eaeb1bd234516232803839ef5a2f6526730abfef98724c103f76a8b5440  node-v5.4.1-x64.msi
d06e7a697d7519b83dbaf631ad45103f8768f6bfef13fa3ce8bddba9d9f436f2  node-v5.4.1-x86.msi
85311ba0ce30a6f41c6d677c33f4c198e3a3f4a4e62754a84c6c3036a1fc6e4f  win-x64/node.exe
e2715e5383d61c67d4f7d41f1fc760ef311c89b365b931720a452f1409ccabb4  win-x64/node.lib
3edae51060629596c592e2bee4da15a4b3f7e36f0cb80c8a2fd6eedcefd0de13  win-x86/node.exe
0dbb491180a380170bb89dc6976207c96c50aaf849021c7b049065c169fd09ef  win-x86/node.lib
-----BEGIN PGP SIGNATURE-----
Comment: GPGTools - https://gpgtools.org

iQIcBAEBCgAGBQJWlZAFAAoJEEX17r2BPa6Oo3wQAKVEfZyDKAhbhg6r+x87tRqO
zsyNpefHA3KPvnRJbZsVD2vvj2Arvv8uaYwHk9rE0IdErP55MzGzJpaf6s2CsKaZ
qLZc+vcmvtdUFv5oiXrwCGZ2YSUtUrudHXyyve/qo4y1SuUagpDRT8ohjhn7Vlds
qnJ2FohUU4oVclHXYKxpH5+5rd4WP4mtz8WIXrmzXn+OHmc9io1qzyzx7tVTK9J4
BGZYZa1BAZ76ETCy8juipiN06T7CYnYYKJ/ti4dDCnG8cRZ5ohqPI5XtqLr4HVmv
6Pfrjw3RRRvmTgqHxX0MZF2TZjDi9Rl6fzYmOWJL9emG3lVZmCI96V7t7JGZWbFi
zVEIFoNE/UBw+Rqd4aUELJ4xALcj+vDWvvC2x+W5bEndorgbNWEBUyq6SwwQ1k/T
9xlXddo92LVWVQjD+PJzV517MKgyHcch2xzAxaAXIRsNuVGMpkThSXeTX0n11HAV
I+R0NHV5vuOdFt+KiENJmsD9RiszmRK3KWeRETwaelmHMR1jq25JcijOHokE0gB7
oV64W9uuVkzrOEQUgBvRF/4Ov+PVM8IPAj9EqZM6xkIGEyq9fCSn9ddJMTF2xKDO
9lD3o44MNAL/ml3wAnm10WltUkCLSHd3Cyi0/7ygeIp0KeUXuaix1sVrGJmZpgIn
bNZ8ZbcRnuEiPnh8a/if
=o+WC
-----END PGP SIGNATURE-----

```
