---
date: '2021-02-23T12:58:46.786Z'
category: release
title: Node v10.24.0 (LTS)
layout: blog-post
author: <PERSON>
---

### Notable changes

Vulnerabilities fixed:

- **CVE-2021-22883**: HTTP2 'unknownProtocol' cause Denial of Service by resource exhaustion
  - Affected Node.js versions are vulnerable to denial of service attacks when too many connection attempts with an 'unknownProtocol' are established. This leads to a leak of file descriptors. If a file descriptor limit is configured on the system, then the server is unable to accept new connections and prevent the process also from opening, e.g. a file. If no file descriptor limit is configured, then this lead to an excessive memory usage and cause the system to run out of memory.
- **CVE-2021-22884**: DNS rebinding in --inspect
  - Affected Node.js versions are vulnerable to denial of service attacks when the whitelist includes “localhost6”. When “localhost6” is not present in /etc/hosts, it is just an ordinary domain that is resolved via DNS, i.e., over network. If the attacker controls the victim's DNS server or can spoof its responses, the DNS rebinding protection can be bypassed by using the “localhost6” domain. As long as the attacker uses the “localhost6” domain, they can still apply the attack described in CVE-2018-7160.
- **CVE-2021-23840**: OpenSSL - Integer overflow in CipherUpdate
  - This is a vulnerability in OpenSSL which may be exploited through Node.js. You can read more about it in https://www.openssl.org/news/secadv/20210216.txt

### Commits

- [[`0afcb4f6bb`](https://github.com/nodejs/node/commit/0afcb4f6bb)] - **deps**: update archs files for OpenSSL-1.1.1j (Daniel Bevenius) [#37415](https://github.com/nodejs/node/pull/37415)
- [[`447be941cd`](https://github.com/nodejs/node/commit/447be941cd)] - **deps**: upgrade openssl sources to 1.1.1j (Daniel Bevenius) [#37415](https://github.com/nodejs/node/pull/37415)
- [[`3f2e9dc40c`](https://github.com/nodejs/node/commit/3f2e9dc40c)] - **(SEMVER-MINOR)** **http2**: add unknownProtocol timeout (Daniel Bevenius) [nodejs-private/node-private#246](https://github.com/nodejs-private/node-private/pull/246)
- [[`d1cf6a9b0f`](https://github.com/nodejs/node/commit/d1cf6a9b0f)] - **src**: drop localhost6 as allowed host for inspector (Matteo Collina) [nodejs-private/node-private#244](https://github.com/nodejs-private/node-private/pull/244)

Windows 32-bit Installer: https://nodejs.org/dist/v10.24.0/node-v10.24.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v10.24.0/node-v10.24.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v10.24.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v10.24.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v10.24.0/node-v10.24.0.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v10.24.0/node-v10.24.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v10.24.0/node-v10.24.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v10.24.0/node-v10.24.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v10.24.0/node-v10.24.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v10.24.0/node-v10.24.0-aix-ppc64.tar.gz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v10.24.0/node-v10.24.0-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v10.24.0/node-v10.24.0-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v10.24.0/node-v10.24.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v10.24.0/node-v10.24.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v10.24.0/node-v10.24.0.tar.gz \
Other release files: https://nodejs.org/dist/v10.24.0/ \
Documentation: https://nodejs.org/docs/v10.24.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

59bdb393035c605627bf4ba64ad8edcc74f067043790c7edc545333cca8630c4  node-v10.24.0-aix-ppc64.tar.gz
265ccad26fdfdcd86d6571b0bf5f1815b55f6a4a9b367816ad0369790501f55e  node-v10.24.0-darwin-x64.tar.gz
ba749262eb5599360cdfe5edf7516a98269defcb6d2de56a9bbfd95a76366a7d  node-v10.24.0-darwin-x64.tar.xz
165ca4182bcfa952d2405e53f480525dfe62c3fd453a893bc34df6cbb8fc6740  node-v10.24.0-headers.tar.gz
c7afbb814018f2bed87e85b2aa71c864c961a3754b0733bcfd077fbb068cfd76  node-v10.24.0-headers.tar.xz
65e6255c6f95b6dcf87f13c21994bc80205b4bd7c7d9a3fe1f8f2a18daec576d  node-v10.24.0-linux-arm64.tar.gz
41bbf035512a72d073e93440458ad6e48586853fc0a5b6396ded80a2d45cb49c  node-v10.24.0-linux-arm64.tar.xz
5a5dcc02bfd0ddcbeb2ef68f116bb72e416149f15f12767864bde77edd7f39d1  node-v10.24.0-linux-armv6l.tar.gz
076d387b1e9345c675745a453f642b6819b07b21cf21d6824f33c8d508f71559  node-v10.24.0-linux-armv6l.tar.xz
02feb052d0e1eb77c9beea5cfe3b67b90d5209ab509797f4f6c892c75cc30fda  node-v10.24.0-linux-armv7l.tar.gz
0b01cb43903bc2d06f0ea3bb6753da4c91fd9533f1bd74e8bd2ee55b470a9084  node-v10.24.0-linux-armv7l.tar.xz
227338ffe74d2c2a87bd1bd77f4c74d21d8027e8eff78eb8e7f686a470b83fbe  node-v10.24.0-linux-ppc64le.tar.gz
1d5b9c5a6ffb7027bbf9cf608d919c280039cea1f1f0308324aca871d874fca7  node-v10.24.0-linux-ppc64le.tar.xz
5a4478e6602c6c6fb28bc01b5356215e714ef0d3917fb1ede487c9b93e88741e  node-v10.24.0-linux-s390x.tar.gz
322d9faf2d724de4596cc021e5eb37553ceafc07fccd2f39afede8c56dde7432  node-v10.24.0-linux-s390x.tar.xz
d8d7ecb0667a9b86b7ce1994731f9c9d313b46f04de59f724259a6fda685617a  node-v10.24.0-linux-x64.tar.gz
a937fb43225289ada54c6c3272a2ad18e1e33b8c7d6211c289d421b5051fdbd0  node-v10.24.0-linux-x64.tar.xz
347004459f040a83bf7f1cb663dd9ba846df8def8967a9572801484768b8a754  node-v10.24.0.pkg
c5233cea13d3ce560cda1cdda873c2054bd3b5621da466fb4965668ef4259b93  node-v10.24.0-sunos-x64.tar.gz
2b43e85f73a0dbc1ec0e64394c2607cbfe53045aaa11f3d9a65ceb4cc6ee8394  node-v10.24.0-sunos-x64.tar.xz
c8d0a56279be77a9033b5f89603c6c491060a661c607fbf82bbe931ca662996e  node-v10.24.0.tar.gz
158273af66f891b2fca90aec7336c42f7574f467affad02c14e80ca163cb3acc  node-v10.24.0.tar.xz
bf839f4d96e1cb105c271a1ccb7a728ff8ce7dfd34a260afaf02e349b00831d2  node-v10.24.0-win-x64.7z
abf0aa48f642aa9ef6cc0021d2fe0275a60feece603664a76c31a812adc710bb  node-v10.24.0-win-x64.zip
7e0e4c6b43935ce194456bbf066bb72fad49427fa08bfd4e7fc9818b4f312d3c  node-v10.24.0-win-x86.7z
6e32b8c513ba209ae7ac2058c106d0b83b4c14c3472d3f1ad956fd3462691799  node-v10.24.0-win-x86.zip
a2c5dd02e43715127248d8533d260a9d4359b9f2d6ba6958df65631b8bf627cf  node-v10.24.0-x64.msi
afcfa989c331e92ed02aeb88b0865ac2264b7bc297685ea46de48d5a945d46c0  node-v10.24.0-x86.msi
58c529834cbc65363d07e1ee59bb577cc76f527a2b0db80d0784e9b6e3c7e6da  win-x64/node.exe
7688ed23318d253aa98ee198f94983e4b563fab188e6fd9dd32955e77111096a  win-x64/node.lib
2ae5424c759a3eb7aabfbb5d21ce8227f43d27150fbf6e1dd89173eeae9a4f8c  win-x64/node_pdb.7z
7a68fa70295977484f1b1dcffa7d590c5b5f84b28d0ea51ffea734850307933a  win-x64/node_pdb.zip
121c6d54aa31bb43a042e7cdedf0bdc916c39895f0f46c34cac76c3990895381  win-x86/node.exe
de1f3445597cbbee2e5eac435651f5dcab049a2d8bd3636877ab5803a87e269e  win-x86/node.lib
2e218cafa528cd3a35dd58ba621b3f182498db7f235c072f14d1426043cf2eb8  win-x86/node_pdb.7z
2e4d6d1c72a90bdff03412d525b764a445edc108cd0503c4baf7da708b081a6e  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEyC+jrhy+3Gvka5NgxDzsRcF6uTwFAmA0+7cACgkQxDzsRcF6
uTwCexAAhTRwBOfMu0XzBPBa+NdpJSDjbYtyI2BNcR8NWMnL18H2pQCbPL2zrm1T
a9kUiNgEuRElMjrxGTyUrx9sNkLPgJDnVeKTXGmwvYk6l3NwbKXW69qn2P/GUAdt
Y9/EnZUWY8s5gyPg4+OtQ5BwgGKrqW/Xsf3rUGFvqvoWqVeUTrQsZVCRrZWdm9oZ
pZyZaLWFCYH08PIq2Q3eYjpfu2Uv7HqWFVIM2NtFI6snZmVmnjUIsr8P1leJQVmM
CZ1Pb9O7+m4jgPGOqWXoJl7bjNrk5jn6o7kZK4AaavR8oMVkRNDikI3VlKBXytMg
lJYapT0a9ELYCVqXhCCar/zNhZDLwJd6b/rYIbP9SKqE9Zcxv2iABR1S3GZwjfEC
dPqTO2blff0CBEU/PsCZxdnSDXPHn3JCwtsuXl4tkyXsH4jd7ob0ur03P7mKfwOR
eZH4wIMPj/KVRKbcT/ts22WzaI21BI06/3MRj4i54fMsloZYCP1+99iovKSqnXrG
iOfse5AjBVWCVsfiJcQfDfDSnL2jl1OF8ii1ZR+gEE2ZczLeCDKszwpBM+66oIqT
cyz3elK64mKc95q9e/eZo9WNwokUsVY6uhqAN08Z1rBnF4ffYGN0YLzOy3/tgHv0
oUt/5184eDsTucMAdC8TbjF1XjFTx2aIc2y1e4ZzJF9GtUQ2FEE=
=V+LP
-----END PGP SIGNATURE-----

```
