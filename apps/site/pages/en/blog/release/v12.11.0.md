---
date: '2019-09-25T23:19:28.360Z'
category: release
title: Node v12.11.0 (Current)
layout: blog-post
author: <PERSON><PERSON>
---

### Notable changes

- **crypto**:
  - Add `oaepLabel` option [#29489](https://github.com/nodejs/node/pull/29489)
- **deps**:
  - Update V8 to 7.7.299.11 [#28918](https://github.com/nodejs/node/pull/28918)
    - More efficient memory handling
    - Stack trace serialization got faster
    - The `Intl.NumberFormat` API gained new functionality
    - For more information: https://v8.dev/blog/v8-release-77
- **events**:
  - Add support for `EventTarget` in `once` [#29498](https://github.com/nodejs/node/pull/29498)
- **fs**:
  - Expose memory file mapping flag `UV_FS_O_FILEMAP` [#29260](https://github.com/nodejs/node/pull/29260)
- **inspector**:
  - New API - `Session.connectToMainThread` [#28870](https://github.com/nodejs/node/pull/28870)
- **process**:
  - Initial SourceMap support via `env.NODE_V8_COVERAGE` [#28960](https://github.com/nodejs/node/pull/28960)
- **stream**:
  - Make `_write()` optional when `_writev()` is implemented [#29639](https://github.com/nodejs/node/pull/29639)
- **tls**:
  - Add option to override signature algorithms [#29598](https://github.com/nodejs/node/pull/29598)
- **util**:
  - Add `encodeInto` to `TextEncoder` [#29524](https://github.com/nodejs/node/pull/29524)
- **worker**:
  - The `worker_thread` module is now stable [#29512](https://github.com/nodejs/node/pull/29512)

### Commits

- [[`b9c7c9002f`](https://github.com/nodejs/node/commit/b9c7c9002f)] - **benchmark**: improve process.env benchmarks (Anna Henningsen) [#29188](https://github.com/nodejs/node/pull/29188)
- [[`6b8951231c`](https://github.com/nodejs/node/commit/6b8951231c)] - **bootstrap**: add exception handling for profiler bootstrap (Shobhit Chittora) [#29552](https://github.com/nodejs/node/pull/29552)
- [[`c052967636`](https://github.com/nodejs/node/commit/c052967636)] - **bootstrap**: provide usable error on missing internal module (Jeremiah Senkpiel) [#29593](https://github.com/nodejs/node/pull/29593)
- [[`5c24bc6c68`](https://github.com/nodejs/node/commit/5c24bc6c68)] - **build**: do not indent assignments in Makefile (Joyee Cheung) [#29623](https://github.com/nodejs/node/pull/29623)
- [[`f90740d734`](https://github.com/nodejs/node/commit/f90740d734)] - **build**: allow clang 10+ in configure.py (Kamil Rytarowski) [#29541](https://github.com/nodejs/node/pull/29541)
- [[`c304594536`](https://github.com/nodejs/node/commit/c304594536)] - **build**: re-run configure on node_version.h change (Anna Henningsen) [#29510](https://github.com/nodejs/node/pull/29510)
- [[`f622771079`](https://github.com/nodejs/node/commit/f622771079)] - **build**: improve `make coverage` (Anna Henningsen) [#29487](https://github.com/nodejs/node/pull/29487)
- [[`c1695c6635`](https://github.com/nodejs/node/commit/c1695c6635)] - **build**: add comment to .travis.yml on how to test Py3 (cclauss) [#29473](https://github.com/nodejs/node/pull/29473)
- [[`6f50c3f391`](https://github.com/nodejs/node/commit/6f50c3f391)] - **build**: update minimum AIX OS level (Michael Dawson) [#29476](https://github.com/nodejs/node/pull/29476)
- [[`ee18238f55`](https://github.com/nodejs/node/commit/ee18238f55)] - **build**: remove experimental Python 3 tests (Christian Clauss) [#29413](https://github.com/nodejs/node/pull/29413)
- [[`fe46054b14`](https://github.com/nodejs/node/commit/fe46054b14)] - **(SEMVER-MINOR)** **build**: reset embedder string to "-node.0" (Michaël Zasso) [#28918](https://github.com/nodejs/node/pull/28918)
- [[`42fd139279`](https://github.com/nodejs/node/commit/42fd139279)] - **build,win**: fix Python detection on localized OS (João Reis) [#29423](https://github.com/nodejs/node/pull/29423)
- [[`f61c5097e2`](https://github.com/nodejs/node/commit/f61c5097e2)] - **console**: skip/strip %c formatting (Gus Caplan) [#29606](https://github.com/nodejs/node/pull/29606)
- [[`68630c5b65`](https://github.com/nodejs/node/commit/68630c5b65)] - **console,util**: fix missing recursion end while inspecting prototypes (Ruben Bridgewater) [#29647](https://github.com/nodejs/node/pull/29647)
- [[`99c2cd8f08`](https://github.com/nodejs/node/commit/99c2cd8f08)] - **crypto**: use BoringSSL-compatible flag getter (Shelley Vohr) [#29604](https://github.com/nodejs/node/pull/29604)
- [[`dd5d944005`](https://github.com/nodejs/node/commit/dd5d944005)] - **(SEMVER-MINOR)** **crypto**: fix OpenSSL return code handling (Tobias Nießen) [#29489](https://github.com/nodejs/node/pull/29489)
- [[`54f327b4dc`](https://github.com/nodejs/node/commit/54f327b4dc)] - **(SEMVER-MINOR)** **crypto**: add oaepLabel option (Tobias Nießen) [#29489](https://github.com/nodejs/node/pull/29489)
- [[`5d60adf38b`](https://github.com/nodejs/node/commit/5d60adf38b)] - **deps**: patch V8 to 7.7.299.11 (Michaël Zasso) [#29628](https://github.com/nodejs/node/pull/29628)
- [[`c718c606c8`](https://github.com/nodejs/node/commit/c718c606c8)] - **deps**: V8: cherry-pick deac757 (Benjamin Coe) [#29626](https://github.com/nodejs/node/pull/29626)
- [[`e4a51ad980`](https://github.com/nodejs/node/commit/e4a51ad980)] - **deps**: patch V8 to 7.7.299.10 (Thomas) [#29472](https://github.com/nodejs/node/pull/29472)
- [[`bc3c0b2d65`](https://github.com/nodejs/node/commit/bc3c0b2d65)] - **deps**: V8: cherry-pick 35c6d4d (Sam Roberts) [#29585](https://github.com/nodejs/node/pull/29585)
- [[`fa7de9b27f`](https://github.com/nodejs/node/commit/fa7de9b27f)] - **deps**: update npm to 6.11.3 (claudiahdz) [#29430](https://github.com/nodejs/node/pull/29430)
- [[`f5f238de6c`](https://github.com/nodejs/node/commit/f5f238de6c)] - **deps**: upgrade to libuv 1.32.0 (cjihrig) [#29508](https://github.com/nodejs/node/pull/29508)
- [[`7957b392e4`](https://github.com/nodejs/node/commit/7957b392e4)] - **deps**: patch V8 to 7.7.299.8 (Michaël Zasso) [#29336](https://github.com/nodejs/node/pull/29336)
- [[`90713c6697`](https://github.com/nodejs/node/commit/90713c6697)] - **(SEMVER-MINOR)** **deps**: patch V8 to be API/ABI compatible with 7.4 (from 7.7) (Michaël Zasso) [#29241](https://github.com/nodejs/node/pull/29241)
- [[`e95f866956`](https://github.com/nodejs/node/commit/e95f866956)] - **deps**: patch V8 to be API/ABI compatible with 7.4 (from 7.6) (Michaël Zasso) [#28955](https://github.com/nodejs/node/pull/28955)
- [[`4eeb2a99e5`](https://github.com/nodejs/node/commit/4eeb2a99e5)] - **(SEMVER-MINOR)** **deps**: patch V8 to be API/ABI compatible with 7.4 (from 7.5) (Michaël Zasso) [#28005](https://github.com/nodejs/node/pull/28005)
- [[`60efc5fd52`](https://github.com/nodejs/node/commit/60efc5fd52)] - **deps**: V8: cherry-pick e3d7f8a (cclauss) [#29105](https://github.com/nodejs/node/pull/29105)
- [[`d1bedbe717`](https://github.com/nodejs/node/commit/d1bedbe717)] - **(SEMVER-MINOR)** **deps**: V8: fix linking issue for MSVS (Refael Ackermann) [#28016](https://github.com/nodejs/node/pull/28016)
- [[`19e38e0def`](https://github.com/nodejs/node/commit/19e38e0def)] - **(SEMVER-MINOR)** **deps**: V8: fix BUILDING_V8_SHARED issues (Refael Ackermann) [#27375](https://github.com/nodejs/node/pull/27375)
- [[`8aaa0abd26`](https://github.com/nodejs/node/commit/8aaa0abd26)] - **(SEMVER-MINOR)** **deps**: V8: add workaround for MSVC optimizer bug (Refael Ackermann) [#28016](https://github.com/nodejs/node/pull/28016)
- [[`07ed874446`](https://github.com/nodejs/node/commit/07ed874446)] - **(SEMVER-MINOR)** **deps**: V8: use ATOMIC_VAR_INIT instead of std::atomic_init (Refael Ackermann) [#27375](https://github.com/nodejs/node/pull/27375)
- [[`1ed3909ca3`](https://github.com/nodejs/node/commit/1ed3909ca3)] - **(SEMVER-MINOR)** **deps**: V8: forward declaration of `Rtl*FunctionTable` (Refael Ackermann) [#27375](https://github.com/nodejs/node/pull/27375)
- [[`242f6174e5`](https://github.com/nodejs/node/commit/242f6174e5)] - **(SEMVER-MINOR)** **deps**: V8: patch register-arm64.h (Refael Ackermann) [#27375](https://github.com/nodejs/node/pull/27375)
- [[`63093e9d49`](https://github.com/nodejs/node/commit/63093e9d49)] - **(SEMVER-MINOR)** **deps**: V8: update postmortem metadata generation script (cjihrig) [#28918](https://github.com/nodejs/node/pull/28918)
- [[`b54ee2185e`](https://github.com/nodejs/node/commit/b54ee2185e)] - **(SEMVER-MINOR)** **deps**: V8: silence irrelevant warning (Michaël Zasso) [#26685](https://github.com/nodejs/node/pull/26685)
- [[`c6f97bb7ce`](https://github.com/nodejs/node/commit/c6f97bb7ce)] - **(SEMVER-MINOR)** **deps**: V8: un-cherry-pick bd019bd (Refael Ackermann) [#26685](https://github.com/nodejs/node/pull/26685)
- [[`5df55c2626`](https://github.com/nodejs/node/commit/5df55c2626)] - **(SEMVER-MINOR)** **deps**: V8: fix filename manipulation for Windows (Refael Ackermann) [#28016](https://github.com/nodejs/node/pull/28016)
- [[`80ccae000e`](https://github.com/nodejs/node/commit/80ccae000e)] - **(SEMVER-MINOR)** **deps**: update V8 to 7.7.299.4 (Michaël Zasso) [#28918](https://github.com/nodejs/node/pull/28918)
- [[`325de437b3`](https://github.com/nodejs/node/commit/325de437b3)] - **doc**: update N-API version matrix (Gabriel Schulhof) [#29461](https://github.com/nodejs/node/pull/29461)
- [[`2707beb8b8`](https://github.com/nodejs/node/commit/2707beb8b8)] - **doc**: add code example to process.throwDeprecation property (Juan José Arboleda) [#29495](https://github.com/nodejs/node/pull/29495)
- [[`edaa2eebd6`](https://github.com/nodejs/node/commit/edaa2eebd6)] - **doc**: fix some signatures of .end() methods (Vse Mozhet Byt) [#29615](https://github.com/nodejs/node/pull/29615)
- [[`13d173f131`](https://github.com/nodejs/node/commit/13d173f131)] - **doc**: remove the suffix number of the anchor link (Maledong) [#29468](https://github.com/nodejs/node/pull/29468)
- [[`3ba64646ed`](https://github.com/nodejs/node/commit/3ba64646ed)] - **doc**: explain stream.finished cleanup (Robert Nagy) [#28935](https://github.com/nodejs/node/pull/28935)
- [[`84b353cf5d`](https://github.com/nodejs/node/commit/84b353cf5d)] - **doc**: fix require call for spawn() in code example (Marian Rusnak) [#29621](https://github.com/nodejs/node/pull/29621)
- [[`39b17706ea`](https://github.com/nodejs/node/commit/39b17706ea)] - **doc**: make minor improvements to stream.md (Robert Nagy) [#28970](https://github.com/nodejs/node/pull/28970)
- [[`50b5ad1638`](https://github.com/nodejs/node/commit/50b5ad1638)] - **doc**: fix nits in net.md (Vse Mozhet Byt) [#29577](https://github.com/nodejs/node/pull/29577)
- [[`4954792991`](https://github.com/nodejs/node/commit/4954792991)] - **doc**: correct trivial misspelling in AUTHORS (gcr) [#29597](https://github.com/nodejs/node/pull/29597)
- [[`0074c8adb7`](https://github.com/nodejs/node/commit/0074c8adb7)] - **doc**: update list style in misc README docs (Rich Trott) [#29594](https://github.com/nodejs/node/pull/29594)
- [[`38028ef818`](https://github.com/nodejs/node/commit/38028ef818)] - **doc**: add missing complete property to http2 docs (Javier Ledezma) [#29571](https://github.com/nodejs/node/pull/29571)
- [[`55631f4f0a`](https://github.com/nodejs/node/commit/55631f4f0a)] - **doc**: add leap second behavior notes for napi methods (Levhita) [#29569](https://github.com/nodejs/node/pull/29569)
- [[`7fd32619c1`](https://github.com/nodejs/node/commit/7fd32619c1)] - **doc**: explain esm options for package authors (Geoffrey Booth) [#29497](https://github.com/nodejs/node/pull/29497)
- [[`f2217cdafe`](https://github.com/nodejs/node/commit/f2217cdafe)] - **doc**: update experimental loader hooks example code (Denis Zavershinskiy) [#29373](https://github.com/nodejs/node/pull/29373)
- [[`bf08c08384`](https://github.com/nodejs/node/commit/bf08c08384)] - **doc**: use consistent unordered list style (Nick Schonning) [#29516](https://github.com/nodejs/node/pull/29516)
- [[`ca8e87a6d3`](https://github.com/nodejs/node/commit/ca8e87a6d3)] - **doc**: add Bethany to TSC (Michael Dawson) [#29546](https://github.com/nodejs/node/pull/29546)
- [[`aa541bbc88`](https://github.com/nodejs/node/commit/aa541bbc88)] - **doc**: add Tobias to the TSC (Michael Dawson) [#29545](https://github.com/nodejs/node/pull/29545)
- [[`9abee075ad`](https://github.com/nodejs/node/commit/9abee075ad)] - **doc**: mention unit for process.hrtime.bigint() (Anna Henningsen) [#29482](https://github.com/nodejs/node/pull/29482)
- [[`3aea277b72`](https://github.com/nodejs/node/commit/3aea277b72)] - **doc**: add documentation for stream readableFlowing (Chetan Karande) [#29506](https://github.com/nodejs/node/pull/29506)
- [[`a262e2f8d4`](https://github.com/nodejs/node/commit/a262e2f8d4)] - **doc**: indent child list items for remark-lint (Nick Schonning) [#29488](https://github.com/nodejs/node/pull/29488)
- [[`2a5340144c`](https://github.com/nodejs/node/commit/2a5340144c)] - **doc**: space around lists (Nick Schonning) [#29467](https://github.com/nodejs/node/pull/29467)
- [[`9e63f914da`](https://github.com/nodejs/node/commit/9e63f914da)] - **doc**: exitedAfterDisconnect value can be false (Nimit Aggarwal) [#29404](https://github.com/nodejs/node/pull/29404)
- [[`b1509e8f8e`](https://github.com/nodejs/node/commit/b1509e8f8e)] - **doc**: remove wrong escapes (XhmikosR) [#29452](https://github.com/nodejs/node/pull/29452)
- [[`7dd897f49a`](https://github.com/nodejs/node/commit/7dd897f49a)] - **doc**: prepare markdown files for more stringent blank-line linting (Rich Trott) [#29447](https://github.com/nodejs/node/pull/29447)
- [[`a9d16b5e30`](https://github.com/nodejs/node/commit/a9d16b5e30)] - **doc**: simplify wording in n-api doc (Michael Dawson) [#29441](https://github.com/nodejs/node/pull/29441)
- [[`c95e9ca6dc`](https://github.com/nodejs/node/commit/c95e9ca6dc)] - **doc**: update release guide with notes for major releases (James M Snell) [#25497](https://github.com/nodejs/node/pull/25497)
- [[`a7331da863`](https://github.com/nodejs/node/commit/a7331da863)] - **doc**: indent ordered list child content (Nick Schonning) [#29332](https://github.com/nodejs/node/pull/29332)
- [[`32bb58ba9c`](https://github.com/nodejs/node/commit/32bb58ba9c)] - **doc**: fix unsafe writable stream code example (Chetan Karande) [#29425](https://github.com/nodejs/node/pull/29425)
- [[`735ef8b235`](https://github.com/nodejs/node/commit/735ef8b235)] - **doc**: async_hooks.createHook promiseResolve option (Ben Noordhuis) [#29405](https://github.com/nodejs/node/pull/29405)
- [[`844b45bf4f`](https://github.com/nodejs/node/commit/844b45bf4f)] - **doc**: change urls directly from 'http' to 'https' (Maledong) [#29422](https://github.com/nodejs/node/pull/29422)
- [[`4374d28c52`](https://github.com/nodejs/node/commit/4374d28c52)] - **doc**: use consistent indenting for unordered list items (Nick Schonning) [#29390](https://github.com/nodejs/node/pull/29390)
- [[`835d1cabf6`](https://github.com/nodejs/node/commit/835d1cabf6)] - **doc**: start unorded lists at start of line (Nick Schonning) [#29390](https://github.com/nodejs/node/pull/29390)
- [[`8023e43e1d`](https://github.com/nodejs/node/commit/8023e43e1d)] - **doc**: change the 'txt' to 'console' for a command (Maledong) [#29389](https://github.com/nodejs/node/pull/29389)
- [[`b9c082d764`](https://github.com/nodejs/node/commit/b9c082d764)] - **esm**: make dynamic import work in the REPL (Bradley Farias) [#29437](https://github.com/nodejs/node/pull/29437)
- [[`0a47d06150`](https://github.com/nodejs/node/commit/0a47d06150)] - **events**: improve performance of EventEmitter.emit (Matteo Collina) [#29633](https://github.com/nodejs/node/pull/29633)
- [[`9150c4dc72`](https://github.com/nodejs/node/commit/9150c4dc72)] - **(SEMVER-MINOR)** **events**: add support for EventTarget in once (Jenia) [#29498](https://github.com/nodejs/node/pull/29498)
- [[`67f5de9b34`](https://github.com/nodejs/node/commit/67f5de9b34)] - **fs**: remove unnecessary argument check (Robert Nagy) [#29043](https://github.com/nodejs/node/pull/29043)
- [[`a20a8f48f7`](https://github.com/nodejs/node/commit/a20a8f48f7)] - **gyp**: make StringIO work in ninja.py (Christian Clauss) [#29414](https://github.com/nodejs/node/pull/29414)
- [[`31b0b52a71`](https://github.com/nodejs/node/commit/31b0b52a71)] - **http**: refactor responseKeepAlive() (Robert Nagy) [#28700](https://github.com/nodejs/node/pull/28700)
- [[`6a7d24b69c`](https://github.com/nodejs/node/commit/6a7d24b69c)] - **http2**: do not crash on stream listener removal w/ destroyed session (Anna Henningsen) [#29459](https://github.com/nodejs/node/pull/29459)
- [[`fa949ca365`](https://github.com/nodejs/node/commit/fa949ca365)] - **http2**: send out pending data earlier (Anna Henningsen) [#29398](https://github.com/nodejs/node/pull/29398)
- [[`d6ba106f8c`](https://github.com/nodejs/node/commit/d6ba106f8c)] - **http2**: do not start reading after write if new write is on wire (Anna Henningsen) [#29399](https://github.com/nodejs/node/pull/29399)
- [[`a268658496`](https://github.com/nodejs/node/commit/a268658496)] - **(SEMVER-MINOR)** **inspector**: new API - Session.connectToMainThread (Eugene Ostroukhov) [#28870](https://github.com/nodejs/node/pull/28870)
- [[`144aeeac68`](https://github.com/nodejs/node/commit/144aeeac68)] - **lib**: remove the use of util.isFunction (himself65) [#29566](https://github.com/nodejs/node/pull/29566)
- [[`91d99ce41c`](https://github.com/nodejs/node/commit/91d99ce41c)] - **(SEMVER-MINOR)** **lib,test**: fix error message check after V8 update (Michaël Zasso) [#28918](https://github.com/nodejs/node/pull/28918)
- [[`13fa966b7b`](https://github.com/nodejs/node/commit/13fa966b7b)] - **module**: error for CJS .js load within type: module (Guy Bedford) [#29492](https://github.com/nodejs/node/pull/29492)
- [[`ce45aae2ab`](https://github.com/nodejs/node/commit/ce45aae2ab)] - **module**: reintroduce package exports dot main (Guy Bedford) [#29494](https://github.com/nodejs/node/pull/29494)
- [[`8474b82e35`](https://github.com/nodejs/node/commit/8474b82e35)] - **n-api**: delete callback bundle via reference (Gabriel Schulhof) [#29479](https://github.com/nodejs/node/pull/29479)
- [[`50d7c39d91`](https://github.com/nodejs/node/commit/50d7c39d91)] - **n-api**: mark version 5 N-APIs as stable (Gabriel Schulhof) [#29401](https://github.com/nodejs/node/pull/29401)
- [[`6b30802471`](https://github.com/nodejs/node/commit/6b30802471)] - **perf_hooks**: remove non-existent entries from inspect (Kirill Fomichev) [#29528](https://github.com/nodejs/node/pull/29528)
- [[`c146fff307`](https://github.com/nodejs/node/commit/c146fff307)] - **perf_hooks**: ignore duplicated entries in observer (Kirill Fomichev) [#29442](https://github.com/nodejs/node/pull/29442)
- [[`9b4a49c844`](https://github.com/nodejs/node/commit/9b4a49c844)] - **perf_hooks**: remove GC callbacks on zero observers count (Kirill Fomichev) [#29444](https://github.com/nodejs/node/pull/29444)
- [[`b30c40bd5d`](https://github.com/nodejs/node/commit/b30c40bd5d)] - **perf_hooks**: import http2 only once (Kirill Fomichev) [#29419](https://github.com/nodejs/node/pull/29419)
- [[`95431eace9`](https://github.com/nodejs/node/commit/95431eace9)] - **policy**: minor perf opts and cleanup (Bradley Farias) [#29322](https://github.com/nodejs/node/pull/29322)
- [[`6ba39d4fe4`](https://github.com/nodejs/node/commit/6ba39d4fe4)] - **(SEMVER-MINOR)** **process**: initial SourceMap support via NODE_V8_COVERAGE (Benjamin Coe) [#28960](https://github.com/nodejs/node/pull/28960)
- [[`03a3468666`](https://github.com/nodejs/node/commit/03a3468666)] - **process**: use public readableFlowing property (Chetan Karande) [#29502](https://github.com/nodejs/node/pull/29502)
- [[`a5bd7e3b2a`](https://github.com/nodejs/node/commit/a5bd7e3b2a)] - **repl**: convert var to let and const (Lucas Holmquist) [#29575](https://github.com/nodejs/node/pull/29575)
- [[`7eae707fd9`](https://github.com/nodejs/node/commit/7eae707fd9)] - **repl**: fix bug in fs module autocompletion (zhangyongsheng) [#29555](https://github.com/nodejs/node/pull/29555)
- [[`596dd9fe34`](https://github.com/nodejs/node/commit/596dd9fe34)] - **repl**: add autocomplete support for fs.promises (antsmartian) [#29400](https://github.com/nodejs/node/pull/29400)
- [[`70a0c170d4`](https://github.com/nodejs/node/commit/70a0c170d4)] - **repl**: add missing variable declaration (Lucas Holmquist) [#29535](https://github.com/nodejs/node/pull/29535)
- [[`3878e1ed31`](https://github.com/nodejs/node/commit/3878e1ed31)] - **src**: perform check before running in runMicrotasks() (Jeremy Apthorp) [#29581](https://github.com/nodejs/node/pull/29581)
- [[`6f8ef2cbab`](https://github.com/nodejs/node/commit/6f8ef2cbab)] - **src**: discard remaining foreground tasks on platform shutdown (Anna Henningsen) [#29587](https://github.com/nodejs/node/pull/29587)
- [[`f84f1dbd98`](https://github.com/nodejs/node/commit/f84f1dbd98)] - **src**: fix closing weak `HandleWrap`s on GC (Anna Henningsen) [#29640](https://github.com/nodejs/node/pull/29640)
- [[`6284b498b4`](https://github.com/nodejs/node/commit/6284b498b4)] - **src**: use libuv to get env vars (Anna Henningsen) [#29188](https://github.com/nodejs/node/pull/29188)
- [[`3a6bc90c29`](https://github.com/nodejs/node/commit/3a6bc90c29)] - **src**: re-delete Atomics.wake (Gus Caplan) [#29586](https://github.com/nodejs/node/pull/29586)
- [[`51a1dfab94`](https://github.com/nodejs/node/commit/51a1dfab94)] - **src**: print exceptions from PromiseRejectCallback (Anna Henningsen) [#29513](https://github.com/nodejs/node/pull/29513)
- [[`4a5ba60e00`](https://github.com/nodejs/node/commit/4a5ba60e00)] - **src**: modified RealEnvStore methods to use libuv functions (Devendra Satram) [#27310](https://github.com/nodejs/node/pull/27310)
- [[`67aa5ef12b`](https://github.com/nodejs/node/commit/67aa5ef12b)] - **src**: make ELDHistogram a HandleWrap (Anna Henningsen) [#29317](https://github.com/nodejs/node/pull/29317)
- [[`5c3d484c21`](https://github.com/nodejs/node/commit/5c3d484c21)] - **src**: check microtasks before running them (Shelley Vohr) [#29434](https://github.com/nodejs/node/pull/29434)
- [[`010d29d74f`](https://github.com/nodejs/node/commit/010d29d74f)] - **src**: fix ValidateDSAParameters when fips is enabled (Daniel Bevenius) [#29407](https://github.com/nodejs/node/pull/29407)
- [[`59b464026f`](https://github.com/nodejs/node/commit/59b464026f)] - **(SEMVER-MINOR)** **src**: update v8abbr.h for V8 7.7 (cjihrig) [#28918](https://github.com/nodejs/node/pull/28918)
- [[`78af92dda5`](https://github.com/nodejs/node/commit/78af92dda5)] - **(SEMVER-MINOR)** **src,lib**: expose memory file mapping flag (João Reis) [#29260](https://github.com/nodejs/node/pull/29260)
- [[`f016823929`](https://github.com/nodejs/node/commit/f016823929)] - **stream**: add test for multiple .push(null) (Chetan Karande) [#29645](https://github.com/nodejs/node/pull/29645)
- [[`b1008973e9`](https://github.com/nodejs/node/commit/b1008973e9)] - **stream**: cleanup use of internal ended state (Chetan Karande) [#29645](https://github.com/nodejs/node/pull/29645)
- [[`e71bdadf52`](https://github.com/nodejs/node/commit/e71bdadf52)] - **(SEMVER-MINOR)** **stream**: make \_write() optional when \_writev() is implemented (Robert Nagy) [#29639](https://github.com/nodejs/node/pull/29639)
- [[`123437bcc3`](https://github.com/nodejs/node/commit/123437bcc3)] - **stream**: apply special logic in removeListener for readable.off() (Robert Nagy) [#29486](https://github.com/nodejs/node/pull/29486)
- [[`322bc6f0a6`](https://github.com/nodejs/node/commit/322bc6f0a6)] - **stream**: do not call \_read() after destroy() (Robert Nagy) [#29491](https://github.com/nodejs/node/pull/29491)
- [[`78cbdf3286`](https://github.com/nodejs/node/commit/78cbdf3286)] - **stream**: optimize creation (Robert Nagy) [#29135](https://github.com/nodejs/node/pull/29135)
- [[`2dc52ad09c`](https://github.com/nodejs/node/commit/2dc52ad09c)] - **stream**: simplify isUint8Array helper (Anna Henningsen) [#29514](https://github.com/nodejs/node/pull/29514)
- [[`560511924f`](https://github.com/nodejs/node/commit/560511924f)] - **test**: fix race condition in test-worker-process-cwd.js (Ruben Bridgewater) [#28609](https://github.com/nodejs/node/pull/28609)
- [[`78ee065a11`](https://github.com/nodejs/node/commit/78ee065a11)] - **test**: fix flaky test-inspector-connect-main-thread (Anna Henningsen) [#29588](https://github.com/nodejs/node/pull/29588)
- [[`87fd55c387`](https://github.com/nodejs/node/commit/87fd55c387)] - **test**: unmark test-worker-prof as flaky (Anna Henningsen) [#29511](https://github.com/nodejs/node/pull/29511)
- [[`79a277ed10`](https://github.com/nodejs/node/commit/79a277ed10)] - **test**: improve test-worker-message-port-message-before-close (Anna Henningsen) [#29483](https://github.com/nodejs/node/pull/29483)
- [[`909c669c04`](https://github.com/nodejs/node/commit/909c669c04)] - **test**: disable core dumps before running crash test (Ben Noordhuis) [#29478](https://github.com/nodejs/node/pull/29478)
- [[`561d504d71`](https://github.com/nodejs/node/commit/561d504d71)] - **test**: permit test-signalwrap to work without test runner (Rich Trott) [#28306](https://github.com/nodejs/node/pull/28306)
- [[`75c559dc3a`](https://github.com/nodejs/node/commit/75c559dc3a)] - **test**: remove flaky status for test-statwatcher (Rich Trott) [#29392](https://github.com/nodejs/node/pull/29392)
- [[`f056d55346`](https://github.com/nodejs/node/commit/f056d55346)] - **(SEMVER-MINOR)** **test**: update postmortem metadata test for V8 7.7 (cjihrig) [#28918](https://github.com/nodejs/node/pull/28918)
- [[`b43d2dd852`](https://github.com/nodejs/node/commit/b43d2dd852)] - **timers**: set \_destroyed even if there are no destroy-hooks (Jeremiah Senkpiel) [#29595](https://github.com/nodejs/node/pull/29595)
- [[`6272f82c07`](https://github.com/nodejs/node/commit/6272f82c07)] - **(SEMVER-MINOR)** **tls**: add option to override signature algorithms (Anton Gerasimov) [#29598](https://github.com/nodejs/node/pull/29598)
- [[`b7488c2a5c`](https://github.com/nodejs/node/commit/b7488c2a5c)] - **tools**: cleanup getnodeversion.py for readability (Christian Clauss) [#29648](https://github.com/nodejs/node/pull/29648)
- [[`7bc2f06e0b`](https://github.com/nodejs/node/commit/7bc2f06e0b)] - **tools**: update ESLint to 6.4.0 (zhangyongsheng) [#29553](https://github.com/nodejs/node/pull/29553)
- [[`0db7ebe073`](https://github.com/nodejs/node/commit/0db7ebe073)] - **tools**: fix iculslocs to support ICU 65.1 (Steven R. Loomis) [#29523](https://github.com/nodejs/node/pull/29523)
- [[`bc7cc348cc`](https://github.com/nodejs/node/commit/bc7cc348cc)] - **tools**: python3 compat for inspector code generator (Ben Noordhuis) [#29340](https://github.com/nodejs/node/pull/29340)
- [[`9de417ad59`](https://github.com/nodejs/node/commit/9de417ad59)] - **tools**: delete v8_external_snapshot.gypi (Ujjwal Sharma) [#29369](https://github.com/nodejs/node/pull/29369)
- [[`2f81d59e75`](https://github.com/nodejs/node/commit/2f81d59e75)] - **tools**: fix GYP ninja generator for Python 3 (Michaël Zasso) [#29416](https://github.com/nodejs/node/pull/29416)
- [[`027dcff207`](https://github.com/nodejs/node/commit/027dcff207)] - **(SEMVER-MINOR)** **tools**: sync gypfiles with V8 7.7 (Michaël Zasso) [#28918](https://github.com/nodejs/node/pull/28918)
- [[`bbf209b5df`](https://github.com/nodejs/node/commit/bbf209b5df)] - **tty**: add color support for mosh (Aditya) [#27843](https://github.com/nodejs/node/pull/27843)
- [[`ced89ad75d`](https://github.com/nodejs/node/commit/ced89ad75d)] - **util**: include reference anchor for circular structures (Ruben Bridgewater) [#27685](https://github.com/nodejs/node/pull/27685)
- [[`772a5e0658`](https://github.com/nodejs/node/commit/772a5e0658)] - **(SEMVER-MINOR)** **util**: add encodeInto to TextEncoder (Anna Henningsen) [#29524](https://github.com/nodejs/node/pull/29524)
- [[`97d8b33ffc`](https://github.com/nodejs/node/commit/97d8b33ffc)] - **(SEMVER-MINOR)** **worker**: mark as stable (Anna Henningsen) [#29512](https://github.com/nodejs/node/pull/29512)
- [[`fa77dc5f3b`](https://github.com/nodejs/node/commit/fa77dc5f3b)] - **worker**: make terminate() resolve for unref’ed Workers (Anna Henningsen) [#29484](https://github.com/nodejs/node/pull/29484)
- [[`53f23715df`](https://github.com/nodejs/node/commit/53f23715df)] - **worker**: prevent event loop starvation through MessagePorts (Anna Henningsen) [#29315](https://github.com/nodejs/node/pull/29315)
- [[`d2b0568890`](https://github.com/nodejs/node/commit/d2b0568890)] - **worker**: make transfer list behave like web MessagePort (Anna Henningsen) [#29319](https://github.com/nodejs/node/pull/29319)

Windows 32-bit Installer: https://nodejs.org/dist/v12.11.0/node-v12.11.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v12.11.0/node-v12.11.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v12.11.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v12.11.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v12.11.0/node-v12.11.0.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v12.11.0/node-v12.11.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v12.11.0/node-v12.11.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v12.11.0/node-v12.11.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v12.11.0/node-v12.11.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v12.11.0/node-v12.11.0-aix-ppc64.tar.gz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v12.11.0/node-v12.11.0-sunos-x64.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v12.11.0/node-v12.11.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v12.11.0/node-v12.11.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v12.11.0/node-v12.11.0.tar.gz \
Other release files: https://nodejs.org/dist/v12.11.0/ \
Documentation: https://nodejs.org/docs/v12.11.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

e6016150756787756e019d86f3cc677ae26348abc1cae48c7faaa393b5be7b08  node-v12.11.0-aix-ppc64.tar.gz
a0fd5c1c9e67099f52b73c732aa52a878c6ff67f50ff0e94c2c5628a87455130  node-v12.11.0-darwin-x64.tar.gz
dbe933891a865ba9bb836f57eebf2f0b74d90011d7397f5bf650f9c17be3dcfc  node-v12.11.0-darwin-x64.tar.xz
fe567022214e919106c99543e24c563703510da581388fcb8b298baf33ceb3b0  node-v12.11.0-headers.tar.gz
5c5b0329837d70bedcad958ccab65b0c3b1e854ec84145ee29e0298c2dba63ef  node-v12.11.0-headers.tar.xz
8988bf487317766b3d84f9b9075c302eaa2a35c768640c99d1f7b0c4ba10bbda  node-v12.11.0-linux-arm64.tar.gz
d1126439ff6d079b682a7cd6308cb226afd247e5cb0f7cc60cfb0e609a096622  node-v12.11.0-linux-arm64.tar.xz
f20cdbbe0a6129d25cd64e03610a0e036864dec5ee0973e1b9db61bd84a11e51  node-v12.11.0-linux-armv7l.tar.gz
47696cdc559c40466ff808db43ab52b8647efb7d7b8e2d8538e7534352000813  node-v12.11.0-linux-armv7l.tar.xz
6659a7abfd228fdd0e6765a22407d17f427620b767fed327e4d754faa5236e56  node-v12.11.0-linux-ppc64le.tar.gz
3baa3d1cef34979495d4b731707f2953d7f8f01a61a25f528f601d60ab98b44b  node-v12.11.0-linux-ppc64le.tar.xz
46f4edecac8be70de411c7ee3321172c418d7b4ac58a1cefba74ccc109502015  node-v12.11.0-linux-s390x.tar.gz
d995a611e644e98145814e0507f0cf4ff199ab0ae3726909ecaf7b177de4f424  node-v12.11.0-linux-s390x.tar.xz
66f99ceb83128fae568659caaa8f2202680c2e89296513605883f81e88d75dc3  node-v12.11.0-linux-x64.tar.gz
c0dc88110ac3ee095e3d09077545435b72d4cd52e35c43cd3fa666cff7446d46  node-v12.11.0-linux-x64.tar.xz
8d4cbe561db8ad6a3e135df2421238c3b9822a87863f835214c38f2fe5d6e2be  node-v12.11.0-sunos-x64.tar.gz
9cafb99e45bed6dc2d4fe1170b9ba9a7eb1f8653499968b2ecff63a5e76124cd  node-v12.11.0-sunos-x64.tar.xz
33a5737ee979fdcfb02b5bdac75c3d78d81a0200507e77efe69ca607d9d1de94  node-v12.11.0-win-x64.7z
457157358bd029196d47865dee66f8c263a3a4a5a8cdd1c0a8cf43ac45e9eda3  node-v12.11.0-win-x64.zip
002810af6331182b9859406f050aac26694135a36cccbfd18df005ec2a70a135  node-v12.11.0-win-x86.7z
71a5199c3cb99c95362fcfc2f24b6d1e9017e3cc5c4579030dfc2bdee58adba3  node-v12.11.0-win-x86.zip
162c601eb883387d2797a60a7078ee470ed4eaa2a8353abe061ea1f623a29075  node-v12.11.0-x64.msi
9325cdc55fdafc7042b1d3e69b8d0ce06a5fe4fa7d02e07845fd32b070b2660f  node-v12.11.0-x86.msi
98cffabac5db02ff8b4c5fe9a2523747980f42d84674826b81ae1e525e480eb4  node-v12.11.0.pkg
030a22b86d0f0686bc205f8f68501d7d174bf8e0829c6381c042783b71697984  node-v12.11.0.tar.gz
8306491e9b7bb95f8ee34dcd8daa4c071b94d523e63c0f2b8c605f2ca43dcf91  node-v12.11.0.tar.xz
13c53690b5439092d8e6791cc4f371ea2e8ab2792e6eedee35e747bf211dbe6b  win-x64/node.exe
af5a725e43d1b2db398f1de782bf777ff2b67a28970c10166f79b74c28700a77  win-x64/node.lib
3c63e60c3c4f72b8db92d1244a115bde82ef77ed1e2a17596c5e500d42c37444  win-x64/node_pdb.7z
4c130fde961107d9d4813ba5d2cc4f46ac68cbed386764609bf9a5b1cf84b2a4  win-x64/node_pdb.zip
e333fcf81813c6e6d50379210e223f0562660b3b38f22574528c8fdde6c655e3  win-x86/node.exe
a6d2a3240366b683aaedbe58a5a0a637b3bd1054ae485c22d25dbb83d590ea29  win-x86/node.lib
56b088463c869523e060769eff721d58591979c89cb98127e51ba6c89f38c7a7  win-x86/node_pdb.7z
0f787921b6812a76162fcd463ad4428fa86d9e22fcb795dd94c2a2ce4e7b23e1  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEpIwr7mgOhBYyzU5E8HSWs+s8F2IFAl2L9U4ACgkQ8HSWs+s8
F2II2A//XYBrouutOiIRPre5t72I9SovvQWz6c93XlkxnJVLpMU1qFWHWbEHeeBs
I+7gN0zvrDYX2v+mgPXGLS7O5a8EKoQ2HHc8U43VolEMppmg6o08aOrzpeEIHsYQ
m/0ho1/dmXGx+8LzrdS3i2WYiKoDmPDDRqXdipSOlgnMhgb6hNo47EFhhb9wIQWJ
9fuBu2WTtnyyMNmFpACFx3/2rBrDqfG4/pzvCJaIx7V+COtFHDuiul8lcVZvTfnm
7xhKiq3UsPBFNdQbcFBUQM7rfUvz+uNJ+xoww9Kgvd40qgJCpNw4jFMfagU5/xxd
xvDTFwlTvBKG44OEywTjKvd1zfFiz9b38qWPdrNa/jLLEnHFPH691WIuHZOuJqmK
wT50gfANNPI3UeM9Tar+B7bUYHQrzc19AdPoo0re0lawxsSI5TxcfnbMu1A+zWit
VKRAg2UjTp4zOgrbTm5S/aBj3F8lGpJxzwKCeR8g54phbJQmc1iQ78IexOBafCAA
6IeBf0CPKvH9ldXYQUgup+RF1E2LCNmONmtX0lvTc+mv9an3Z9++ZKPAHMqaTkaP
9YGqXRGBW9HnqpE5avCKV0t9yeSdSCLwIm79WIeiJRg9wLMVVsxqcnZioW8CiJFY
C29iNGAb6eDNaA/IMIQ4KZgykYEy69RI57So+7+o9TjZGlaK1ME=
=VFND
-----END PGP SIGNATURE-----

```
