---
date: '2016-04-01T03:31:50.474Z'
category: release
title: Node v5.10.0 (Current)
layout: blog-post
author: <PERSON>
---

This release includes a security update for npm. For more details you can read
[this post on our blog](/blog/vulnerability/npm-tokens-leak-march-2016/)
written by [<PERSON>](https://github.com/othi<PERSON>23) from npm.

### Notable changes

- **buffer**:
  - make byteLength work with ArrayBuffer & DataView (<PERSON>) [#5255](https://github.com/nodejs/node/pull/5255)
  - backport --zero-fill-buffers command line option (<PERSON>) [#5744](https://github.com/nodejs/node/pull/5744)
  - backport new buffer constructor APIs (<PERSON>) [#5763](https://github.com/nodejs/node/pull/5763)
  - add swap16() and swap32() methods (<PERSON>) [#5724](https://github.com/nodejs/node/pull/5724)
- **fs**: add the fs.mkdtemp() function. (<PERSON><PERSON><PERSON>) [#5333](https://github.com/nodejs/node/pull/5333)
- **net**: emit host in lookup event (HUANG Wei) [#5598](https://github.com/nodejs/node/pull/5598)
- **node**: --no-browser-globals configure flag (Fedor Indutny) [#5853](https://github.com/nodejs/node/pull/5853)
- **npm**: Upgrade to v3.8.3. Fixes a security flaw in the use of authentication tokens in HTTP requests that
  would allow an attacker to set up a server that could collect tokens from users of the command-line interface.
  Authentication tokens have previously been sent with every request made by the CLI for logged-in users,
  regardless of the destination of the request. This update fixes this by only including those tokens for requests
  made against the registry or registries used for the current install. (Forrest L Norvell) [npm/node#6](https://github.com/npm/node/pull/6)
- **repl**: support standalone blocks (Prince J Wesley) [#5581](https://github.com/nodejs/node/pull/5581)
- **src**: override v8 thread defaults using cli options (Tom Gallacher) [#4344](https://github.com/nodejs/node/pull/4344)

### Commits

- [[`2cbbaafca9`](https://github.com/nodejs/node/commit/2cbbaafca9)] - **async_wrap**: don't abort on callback exception (Trevor Norris) [#5756](https://github.com/nodejs/node/pull/5756)
- [[`6f16882733`](https://github.com/nodejs/node/commit/6f16882733)] - **async_wrap**: notify post if intercepted exception (Trevor Norris) [#5756](https://github.com/nodejs/node/pull/5756)
- [[`a4856122d3`](https://github.com/nodejs/node/commit/a4856122d3)] - **async_wrap**: setupHooks now accepts object (Trevor Norris) [#5756](https://github.com/nodejs/node/pull/5756)
- [[`ee83c956c5`](https://github.com/nodejs/node/commit/ee83c956c5)] - **(SEMVER-MINOR)** **buffer**: make byteLength work with ArrayBuffer & DataView (Jackson Tian) [#5255](https://github.com/nodejs/node/pull/5255)
- [[`1f8e4b54ce`](https://github.com/nodejs/node/commit/1f8e4b54ce)] - **(SEMVER-MINOR)** **buffer**: add swap16() and swap32() methods (James M Snell) [#5724](https://github.com/nodejs/node/pull/5724)
- [[`bdf933bece`](https://github.com/nodejs/node/commit/bdf933bece)] - **buffer**: changing let in for loops back to var (Gareth Ellis) [#5819](https://github.com/nodejs/node/pull/5819)
- [[`c1534e7eaf`](https://github.com/nodejs/node/commit/c1534e7eaf)] - **(SEMVER-MINOR)** **buffer**: backport new buffer constructor APIs (James M Snell) [#5763](https://github.com/nodejs/node/pull/5763)
- [[`3c02727055`](https://github.com/nodejs/node/commit/3c02727055)] - **(SEMVER-MINOR)** **buffer**: backport --zero-fill-buffers command line option (James M Snell) [#5744](https://github.com/nodejs/node/pull/5744)
- [[`58b5c1e19f`](https://github.com/nodejs/node/commit/58b5c1e19f)] - **build**: add suport for x86 architecture (Robert Chiras) [#5544](https://github.com/nodejs/node/pull/5544)
- [[`389f5a85e6`](https://github.com/nodejs/node/commit/389f5a85e6)] - **build**: add script to create Android .mk files (Robert Chiras) [#5544](https://github.com/nodejs/node/pull/5544)
- [[`5ee5fa292f`](https://github.com/nodejs/node/commit/5ee5fa292f)] - **build**: add missing `openssl_fips%` to common.gypi (Fedor Indutny) [#5919](https://github.com/nodejs/node/pull/5919)
- [[`5681ffecf7`](https://github.com/nodejs/node/commit/5681ffecf7)] - **build**: enable compilation for linuxOne (Michael Dawson) [#5941](https://github.com/nodejs/node/pull/5941)
- [[`660ec9f889`](https://github.com/nodejs/node/commit/660ec9f889)] - **child_process**: refactor self=this in socket_list (Benjamin Gruenbaum) [#5860](https://github.com/nodejs/node/pull/5860)
- [[`e1a012f277`](https://github.com/nodejs/node/commit/e1a012f277)] - **deps**: upgrade npm to 3.8.3 (Forrest L Norvell)
- [[`ec1813199d`](https://github.com/nodejs/node/commit/ec1813199d)] - **deps**: backport 8d00c2c from v8 upstream (Ben Noordhuis) [#5577](https://github.com/nodejs/node/pull/5577)
- [[`2a5c6d7006`](https://github.com/nodejs/node/commit/2a5c6d7006)] - **dns**: Refactor forEach to map (Benjamin Gruenbaum) [#5803](https://github.com/nodejs/node/pull/5803)
- [[`6a6112a2f3`](https://github.com/nodejs/node/commit/6a6112a2f3)] - **dns**: Use object without protoype for map (Benjamin Gruenbaum) [#5843](https://github.com/nodejs/node/pull/5843)
- [[`8fa0b5c1da`](https://github.com/nodejs/node/commit/8fa0b5c1da)] - **doc**: Add @mhdawson back to the CTC (James M Snell) [#5633](https://github.com/nodejs/node/pull/5633)
- [[`858a524325`](https://github.com/nodejs/node/commit/858a524325)] - **doc**: typo: interal->internal. (Corey Kosak) [#5849](https://github.com/nodejs/node/pull/5849)
- [[`5676a35bd9`](https://github.com/nodejs/node/commit/5676a35bd9)] - **doc**: explain path.format expected properties (John Eversole) [#5801](https://github.com/nodejs/node/pull/5801)
- [[`29778393a0`](https://github.com/nodejs/node/commit/29778393a0)] - **doc**: use consistent event name parameter (Benjamin Gruenbaum) [#5850](https://github.com/nodejs/node/pull/5850)
- [[`949b17ff6d`](https://github.com/nodejs/node/commit/949b17ff6d)] - **doc**: fix order of end tags of list after heading (firedfox) [#5874](https://github.com/nodejs/node/pull/5874)
- [[`8e790b7a0c`](https://github.com/nodejs/node/commit/8e790b7a0c)] - **doc**: add instructions to only sign a release (Jeremiah Senkpiel) [#5876](https://github.com/nodejs/node/pull/5876)
- [[`f1f9aff855`](https://github.com/nodejs/node/commit/f1f9aff855)] - **doc**: fix doc for Buffer.readInt32LE() (ghaiklor) [#5890](https://github.com/nodejs/node/pull/5890)
- [[`731f7b8055`](https://github.com/nodejs/node/commit/731f7b8055)] - **etw**: fix descriptors of events 9 and 23 (João Reis) [#5742](https://github.com/nodejs/node/pull/5742)
- [[`aac9ead379`](https://github.com/nodejs/node/commit/aac9ead379)] - **etw,build**: always generate .rc and .h files (João Reis) [#5657](https://github.com/nodejs/node/pull/5657)
- [[`80155d398c`](https://github.com/nodejs/node/commit/80155d398c)] - **(SEMVER-MINOR)** **fs**: add the fs.mkdtemp() function. (Florian MARGAINE) [#5333](https://github.com/nodejs/node/pull/5333)
- [[`ae15d68ad1`](https://github.com/nodejs/node/commit/ae15d68ad1)] - **governance**: remove target size for CTC (Rich Trott) [#5879](https://github.com/nodejs/node/pull/5879)
- [[`63c601bc15`](https://github.com/nodejs/node/commit/63c601bc15)] - **http**: speed up checkIsHttpToken (Jackson Tian) [#4790](https://github.com/nodejs/node/pull/4790)
- [[`40847b0b8b`](https://github.com/nodejs/node/commit/40847b0b8b)] - **lib**: rename /node.js to /bootstrap_node.js (Jeremiah Senkpiel) [#5103](https://github.com/nodejs/node/pull/5103)
- [[`e644eb3d69`](https://github.com/nodejs/node/commit/e644eb3d69)] - **lib**: refactor code with startsWith/endsWith (Jackson Tian) [#5753](https://github.com/nodejs/node/pull/5753)
- [[`a757e0583c`](https://github.com/nodejs/node/commit/a757e0583c)] - **lib,src**: move src/node.js to lib/internal/node.js (Jeremiah Senkpiel) [#5103](https://github.com/nodejs/node/pull/5103)
- [[`e3c7b46326`](https://github.com/nodejs/node/commit/e3c7b46326)] - **lib,src**: refactor src/node.js into internal files (Jeremiah Senkpiel) [#5103](https://github.com/nodejs/node/pull/5103)
- [[`b07bc5d996`](https://github.com/nodejs/node/commit/b07bc5d996)] - **(SEMVER-MINOR)** **net**: emit host in lookup event (HUANG Wei) [#5598](https://github.com/nodejs/node/pull/5598)
- [[`2fa959be15`](https://github.com/nodejs/node/commit/2fa959be15)] - **(SEMVER-MINOR)** **node**: --no-browser-globals configure flag (Fedor Indutny) [#5853](https://github.com/nodejs/node/pull/5853)
- [[`a2ad21645f`](https://github.com/nodejs/node/commit/a2ad21645f)] - **querystring**: don't stringify bad surrogate pair (Brian White) [#5858](https://github.com/nodejs/node/pull/5858)
- [[`427173204e`](https://github.com/nodejs/node/commit/427173204e)] - **(SEMVER-MINOR)** **repl**: support standalone blocks (Prince J Wesley) [#5581](https://github.com/nodejs/node/pull/5581)
- [[`d044898495`](https://github.com/nodejs/node/commit/d044898495)] - **src**: Add missing `using v8::MaybeLocal` (Anna Henningsen) [#5974](https://github.com/nodejs/node/pull/5974)
- [[`0d0c57ff5e`](https://github.com/nodejs/node/commit/0d0c57ff5e)] - **(SEMVER-MINOR)** **src**: override v8 thread defaults using cli options (Tom Gallacher) [#4344](https://github.com/nodejs/node/pull/4344)
- [[`f9d0166291`](https://github.com/nodejs/node/commit/f9d0166291)] - **src**: reword command and add ternary (Trevor Norris) [#5756](https://github.com/nodejs/node/pull/5756)
- [[`f1488bb24c`](https://github.com/nodejs/node/commit/f1488bb24c)] - **src,http_parser**: remove KickNextTick call (Trevor Norris) [#5756](https://github.com/nodejs/node/pull/5756)
- [[`8e8768ecbb`](https://github.com/nodejs/node/commit/8e8768ecbb)] - **test**: add known_issues test for GH-2148 (Rich Trott) [#5920](https://github.com/nodejs/node/pull/5920)
- [[`bf94b5a1b9`](https://github.com/nodejs/node/commit/bf94b5a1b9)] - **test**: mitigate flaky test-https-agent (Rich Trott) [#5939](https://github.com/nodejs/node/pull/5939)
- [[`2192528326`](https://github.com/nodejs/node/commit/2192528326)] - **test**: fix flaky test-repl (Brian White) [#5914](https://github.com/nodejs/node/pull/5914)
- [[`aebe6245b7`](https://github.com/nodejs/node/commit/aebe6245b7)] - **test**: add test for piping large input from stdin (Anna Henningsen) [#5949](https://github.com/nodejs/node/pull/5949)
- [[`a19de97d2f`](https://github.com/nodejs/node/commit/a19de97d2f)] - **test**: remove the use of curl in the test suite (Santiago Gimeno) [#5750](https://github.com/nodejs/node/pull/5750)
- [[`6928a17aa3`](https://github.com/nodejs/node/commit/6928a17aa3)] - **test**: exclude new fs watch test for AIX (Michael Dawson) [#5937](https://github.com/nodejs/node/pull/5937)
- [[`3238bff3b3`](https://github.com/nodejs/node/commit/3238bff3b3)] - **test**: confirm globals not used internally (Rich Trott) [#5882](https://github.com/nodejs/node/pull/5882)
- [[`a41fd93f68`](https://github.com/nodejs/node/commit/a41fd93f68)] - **test**: fix flaky test-net-socket-timeout (Brian White) [#5902](https://github.com/nodejs/node/pull/5902)
- [[`82a50d3def`](https://github.com/nodejs/node/commit/82a50d3def)] - **test**: move dns test to test/internet (Ben Noordhuis) [#5905](https://github.com/nodejs/node/pull/5905)
- [[`fb0c5bcac2`](https://github.com/nodejs/node/commit/fb0c5bcac2)] - **test**: fix flaky test-http-set-timeout (Rich Trott) [#5856](https://github.com/nodejs/node/pull/5856)
- [[`8344a522a8`](https://github.com/nodejs/node/commit/8344a522a8)] - **test**: fix test-debugger-client.js (Rich Trott) [#5851](https://github.com/nodejs/node/pull/5851)
- [[`7ec5397954`](https://github.com/nodejs/node/commit/7ec5397954)] - **timers**: fixing API refs to use safe internal refs (Kyle Simpson) [#5882](https://github.com/nodejs/node/pull/5882)
- [[`cb676cf3e7`](https://github.com/nodejs/node/commit/cb676cf3e7)] - **tools**: fix json doc generation (firedfox) [#5943](https://github.com/nodejs/node/pull/5943)
- [[`77bed269ad`](https://github.com/nodejs/node/commit/77bed269ad)] - **win,build**: build and test add-ons on test-ci (Bogdan Lobor) [#5886](https://github.com/nodejs/node/pull/5886)
- [[`afcd276ecc`](https://github.com/nodejs/node/commit/afcd276ecc)] - **zlib**: Fix handling of gzip magic bytes mid-file (Anna Henningsen) [#5863](https://github.com/nodejs/node/pull/5863)

Windows 32-bit Installer: https://nodejs.org/dist/v5.10.0/node-v5.10.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v5.10.0/node-v5.10.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v5.10.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v5.10.0/win-x64/node.exe \
Mac OS X 64-bit Installer: https://nodejs.org/dist/v5.10.0/node-v5.10.0.pkg \
Mac OS X 64-bit Binary: https://nodejs.org/dist/v5.10.0/node-v5.10.0-darwin-x64.tar.gz \
Linux 32-bit Binary: https://nodejs.org/dist/v5.10.0/node-v5.10.0-linux-x86.tar.xz \
Linux 64-bit Binary: https://nodejs.org/dist/v5.10.0/node-v5.10.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v5.10.0/node-v5.10.0-linux-ppc64le.tar.xz \
SmartOS 32-bit Binary: https://nodejs.org/dist/v5.10.0/node-v5.10.0-sunos-x86.tar.xz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v5.10.0/node-v5.10.0-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v5.10.0/node-v5.10.0-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v5.10.0/node-v5.10.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v5.10.0/node-v5.10.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v5.10.0/node-v5.10.0.tar.gz \
Other release files: https://nodejs.org/dist/v5.10.0/ \
Documentation: https://nodejs.org/docs/v5.10.0/api/

Shasums (GPG signing hash: SHA512, file hash: SHA256):

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA512

00407892416649f7567cc20ae6f0c091650dee6186fe58eb33d2bd886f276799  node-v5.10.0-darwin-x64.tar.gz
32bb55aa33da388e0ffb653678b1598511a12e9664ff42ff80e9072e75cbad3a  node-v5.10.0-darwin-x64.tar.xz
726c61ca135198ef238078ab4f4f5a7ccd1980d049f08f2e1f2dd767c510f000  node-v5.10.0-headers.tar.gz
ab520da8a88f00cdab394eb2064a293ab549f44d91f03cf3c20b671cfee48834  node-v5.10.0-headers.tar.xz
df88803bda234b32240906b620315c8f6d6200332047a88cb0ec83009cf25dd5  node-v5.10.0-linux-arm64.tar.gz
bee70770110caa640bc56d3da40a80de8a5c0f5c2bfc8b7363b9372c0a321faa  node-v5.10.0-linux-arm64.tar.xz
019a257faa5eebf6304686dfeffdbcb4c22f0547aa366f6e563aad39ab1b1ab1  node-v5.10.0-linux-armv6l.tar.gz
a9b085d19b8abac65ffa245d2959c2ff697b3a26b904be964a0b3c797c1e8d24  node-v5.10.0-linux-armv6l.tar.xz
3f7524d3db60175c2323bb2a0a13ad1ca7d47d4ede6f42834b6b8425be70e0a2  node-v5.10.0-linux-armv7l.tar.gz
e2988e55b603d08705137bb26690abe8c870144c5674f8c20a643df8c4833281  node-v5.10.0-linux-armv7l.tar.xz
07058fb1c82e7cd3d21729ab6a5ed5523fdb9f664d975438974668710b7d8ec6  node-v5.10.0-linux-ppc64le.tar.gz
05d9ecab3d2e9e82cd6ca046dcb7389b4e8d322a5ee6acc3948bbbeb0f475180  node-v5.10.0-linux-ppc64le.tar.xz
a458ddab5f8d071c9b4f24ccfa685aedd57ccf7338c3ea0e2b99546cf35a3958  node-v5.10.0-linux-x64.tar.gz
6e6442d1a44a0df0949ef7ade04caf43d994205e65ca57f97303b6462fa90377  node-v5.10.0-linux-x64.tar.xz
e884f070542f49f577fd9785f09cf7734e6c2107d23b6c1b58453d43183c5ec0  node-v5.10.0-linux-x86.tar.gz
fa3d63bfc2e6497b8ce5c5d7d6cde69e36669b246f2fe6ad0811ce76bf1d70d6  node-v5.10.0-linux-x86.tar.xz
581fda48da967e8f5d3ba5ea5e1f6e12e689b91ee26393f398ab261dfe1fcfd2  node-v5.10.0.pkg
78017489238f8ac86176079d1aa73cd8c40849f410d705080e00345512de8c93  node-v5.10.0-sunos-x64.tar.gz
94f1963a760a2b874baafd856b610708dfcdf857bd6423b7f613c53384ee8ce7  node-v5.10.0-sunos-x64.tar.xz
f9540debe8a396158f21a47f275d2fff564b42193f6621960d04c959e8ac5cc3  node-v5.10.0-sunos-x86.tar.gz
202afe2bae1fa483ded7e6317af7a1445ab01f6cb390b32d8ba1473920bc231f  node-v5.10.0-sunos-x86.tar.xz
8faeb9af5bde641b6e5dfae32bfa99253df2bccf59cf8a62162760026411762b  node-v5.10.0.tar.gz
e3cc8e84b38b2d86ddf802f690eacabd97f5e32d37b9c70e19ecfdd2bef6e13a  node-v5.10.0.tar.xz
d2c6cf6664e8fdb01d7078dd25191149ba44f6f2b4b5b2e441d2a25b1db12809  node-v5.10.0-x64.msi
b807fb855048df7cae320aa55c4eca902a745dafc96571f720d9d3e1c129bd97  node-v5.10.0-x86.msi
a37a8698d74a0204de958e53f1c44b158f0e46719664f2bdd195c3538b0ecacb  win-x64/node.exe
7563f3c29e4b66a885dfaa9e3023eaec316dfb0e5cdbc98c1a8c09aa1049e759  win-x64/node.lib
878b6c8a10622966a11dae7934f72dfb6abbe75896a21bd277acaa4076473187  win-x86/node.exe
dc50c17be9a65d8e49939f497d73b7cb8b785c5aac5b20c28dc35dd286113064  win-x86/node.lib
-----BEGIN PGP SIGNATURE-----
Comment: GPGTools - https://gpgtools.org

iQIcBAEBCgAGBQJW/en1AAoJELY7U1pMIGyp5F0QAKEASK8atK36nh0kVWIEftfo
ttvy73f+6uAIPiTplGuGy8n3z0kh1i9FcEpstJaJnzNVcosZl4tNY1IZfASFHHjt
xrybtRqpVmTR7ocLqEODDtqMNqKH/m+j7yhiGpewuDwSwIoXOM3nCj3axrVKFodV
izYeInTBjIbr7KZmbTlDipLctMJfT7jGCZVR5bVYla5AEL7R9LHtecmDk7j5p3qu
eEjohgAxcazam81LaG5YV6PNdL8wbZ5QmOAeQb47yZh6ZOal+kEtautQMb0N5eea
F5CyFCPthhOuiU+Gg0KVeQI6S2bIq7N0RXPSFuwiXmosBXn57LtXN9Mw6GwXbRoJ
YZQTOpaE20LJ/h3Vi1mx0cS6dRdsj5SaG/ks/q0t16S4Niybys7u7ZofteOThcjV
6suuUM5WHCcfm5N8ABZtINZwth9za0bJkwSN8GhCugCDFWxk89zMnniQ9YIpSbWm
WMN3JMWovyeRgicpje5sXRdBlfv26+FWN2VJ8C65RPQWGQHDm6WNb8ImTrjTqzI6
mwcDfh98qYCVhWQ8laBfaVKweQLR4BhhLJSscFEjhOeu4TlA3ymKRgUvi9XnbX5b
gwqGKpTfmwMzudgxRCqZHtPL1LMR3LGEztTk4mVXvPtr6lefYvIrnzZW2PffTEbS
ZuQFj1HhTZwxX1mx0MpM
=SX5M
-----END PGP SIGNATURE-----

```
