---
date: '2021-06-15T12:50:28.048Z'
category: release
title: Node v14.17.1 (LTS)
layout: blog-post
author: <PERSON><PERSON><PERSON>
---

### Notable Changes

- [[`6035492c8f`](https://github.com/nodejs/node/commit/6035492c8f)] - **deps**: update ICU to 69.1 (<PERSON><PERSON><PERSON>) [#38178](https://github.com/nodejs/node/pull/38178)
- [[`9417fd0bc8`](https://github.com/nodejs/node/commit/9417fd0bc8)] - **errors**: align source-map stacks with spec (<PERSON>) [#37252](https://github.com/nodejs/node/pull/37252)

### Commits

- [[`87fa636953`](https://github.com/nodejs/node/commit/87fa636953)] - **assert**: refactor to use more primordials (<PERSON>) [#36234](https://github.com/nodejs/node/pull/36234)
- [[`cfff3b4462`](https://github.com/nodejs/node/commit/cfff3b4462)] - **assert**: refactor to avoid unsafe array iteration (<PERSON> du Hamel) [#37344](https://github.com/nodejs/node/pull/37344)
- [[`dd18def7db`](https://github.com/nodejs/node/commit/dd18def7db)] - **async_hooks**: refactor to avoid unsafe array iteration (Antoine du Hamel) [#37125](https://github.com/nodejs/node/pull/37125)
- [[`5f3e96b570`](https://github.com/nodejs/node/commit/5f3e96b570)] - **async_hooks,doc**: replace process.stdout.fd with 1 (Darshan Sen) [#38382](https://github.com/nodejs/node/pull/38382)
- [[`f4cb8b8281`](https://github.com/nodejs/node/commit/f4cb8b8281)] - **benchmark**: avoid using `console.log()` (Antoine du Hamel) [#38370](https://github.com/nodejs/node/pull/38370)
- [[`9e4c1f2f2c`](https://github.com/nodejs/node/commit/9e4c1f2f2c)] - **benchmark**: use `process.hrtime.bigint()` (Antoine du Hamel) [#38369](https://github.com/nodejs/node/pull/38369)
- [[`3c886e0ad6`](https://github.com/nodejs/node/commit/3c886e0ad6)] - **buffer**: remove TODOs in `atob` / `btoa` (Khaidi Chu) [#38548](https://github.com/nodejs/node/pull/38548)
- [[`c5b86f8c2f`](https://github.com/nodejs/node/commit/c5b86f8c2f)] - **buffer**: remove unreachable code (Rongjian Zhang) [#38537](https://github.com/nodejs/node/pull/38537)
- [[`9ae2a27d44`](https://github.com/nodejs/node/commit/9ae2a27d44)] - **buffer**: make FastBuffer safe to construct (Antoine du Hamel) [#36587](https://github.com/nodejs/node/pull/36587)
- [[`ff546ff744`](https://github.com/nodejs/node/commit/ff546ff744)] - **buffer**: refactor to use primordials instead of Array#reduce (Antoine du Hamel) [#36392](https://github.com/nodejs/node/pull/36392)
- [[`5acf0a5ba3`](https://github.com/nodejs/node/commit/5acf0a5ba3)] - **buffer**: refactor to use more primordials (Antoine du Hamel) [#36166](https://github.com/nodejs/node/pull/36166)
- [[`52fd42ec46`](https://github.com/nodejs/node/commit/52fd42ec46)] - **build**: work around bug in MSBuild v16.10.0 (Michaël Zasso) [#38873](https://github.com/nodejs/node/pull/38873)
- [[`5df0f35bf6`](https://github.com/nodejs/node/commit/5df0f35bf6)] - **build**: add workaround for V8 builds (Richard Lau) [#38632](https://github.com/nodejs/node/pull/38632)
- [[`754aa384e0`](https://github.com/nodejs/node/commit/754aa384e0)] - **build**: remove dependency on `distutils.spawn` (Richard Lau) [#38600](https://github.com/nodejs/node/pull/38600)
- [[`5de7e64f3a`](https://github.com/nodejs/node/commit/5de7e64f3a)] - **build**: fix make test-npm (Ruy Adorno) [#36369](https://github.com/nodejs/node/pull/36369)
- [[`e5fae63108`](https://github.com/nodejs/node/commit/e5fae63108)] - **child_process**: reduce abort handler code duplication (Rich Trott) [#36644](https://github.com/nodejs/node/pull/36644)
- [[`598d2bdf09`](https://github.com/nodejs/node/commit/598d2bdf09)] - **child_process**: treat already-aborted controller as aborting (Rich Trott) [#36644](https://github.com/nodejs/node/pull/36644)
- [[`8d7708bdef`](https://github.com/nodejs/node/commit/8d7708bdef)] - **child_process**: refactor to use more primordials (Antoine du Hamel) [#36003](https://github.com/nodejs/node/pull/36003)
- [[`b8c4d30e77`](https://github.com/nodejs/node/commit/b8c4d30e77)] - **deps**: update to cjs-module-lexer@1.2.1 (Guy Bedford) [#38450](https://github.com/nodejs/node/pull/38450)
- [[`6035492c8f`](https://github.com/nodejs/node/commit/6035492c8f)] - **deps**: update ICU to 69.1 (Michaël Zasso) [#38178](https://github.com/nodejs/node/pull/38178)
- [[`51dad8cabb`](https://github.com/nodejs/node/commit/51dad8cabb)] - **deps**: V8: cherry-pick 035c305ce776 (Michaël Zasso) [#38497](https://github.com/nodejs/node/pull/38497)
- [[`a467125c8d`](https://github.com/nodejs/node/commit/a467125c8d)] - **deps**: V8: cherry-pick dfcdf7837e23 (Benjamin Coe) [#36573](https://github.com/nodejs/node/pull/36573)
- [[`acc9fad2ba`](https://github.com/nodejs/node/commit/acc9fad2ba)] - **deps**: V8: cherry-pick 86991d0587a1 (Benjamin Coe) [#36254](https://github.com/nodejs/node/pull/36254)
- [[`d67827744b`](https://github.com/nodejs/node/commit/d67827744b)] - **deps**: V8: cherry-pick 530080c44af2 (Milad Fa) [#38508](https://github.com/nodejs/node/pull/38508)
- [[`bac9ba4f8a`](https://github.com/nodejs/node/commit/bac9ba4f8a)] - **dgram**: extract cluster lazy loading method to make it testable (Rongjian Zhang) [#38563](https://github.com/nodejs/node/pull/38563)
- [[`f5b2115b76`](https://github.com/nodejs/node/commit/f5b2115b76)] - **dgram**: refactor to use more primordials (Antoine du Hamel) [#36286](https://github.com/nodejs/node/pull/36286)
- [[`cd7cf0547a`](https://github.com/nodejs/node/commit/cd7cf0547a)] - **dns**: refactor to use more primordials (Antoine du Hamel) [#36314](https://github.com/nodejs/node/pull/36314)
- [[`9f67c0852c`](https://github.com/nodejs/node/commit/9f67c0852c)] - **doc**: cleanup events.md structure (James M Snell) [#36100](https://github.com/nodejs/node/pull/36100)
- [[`41cfe645c0`](https://github.com/nodejs/node/commit/41cfe645c0)] - **doc**: fix JS flavor selection (Antoine du Hamel) [#37791](https://github.com/nodejs/node/pull/37791)
- [[`7c4748b0dc`](https://github.com/nodejs/node/commit/7c4748b0dc)] - **doc**: use `HEAD` instead of `master` for links (Antoine du Hamel) [#38518](https://github.com/nodejs/node/pull/38518)
- [[`26426577ff`](https://github.com/nodejs/node/commit/26426577ff)] - **doc**: remove import.meta.resolve parent URL type (Kevin Locke) [#38585](https://github.com/nodejs/node/pull/38585)
- [[`88055abf19`](https://github.com/nodejs/node/commit/88055abf19)] - **doc**: document buffer.kStringMaxLength (Tobias Nießen) [#38688](https://github.com/nodejs/node/pull/38688)
- [[`2e8dfee165`](https://github.com/nodejs/node/commit/2e8dfee165)] - **doc**: clarify synchronous blocking of Worker stdio (James M Snell) [#38658](https://github.com/nodejs/node/pull/38658)
- [[`212cd5cf05`](https://github.com/nodejs/node/commit/212cd5cf05)] - **doc**: update contact info (Gabriel Schulhof) [#38689](https://github.com/nodejs/node/pull/38689)
- [[`fa35c0662b`](https://github.com/nodejs/node/commit/fa35c0662b)] - **doc**: change color of doctag on night mode (Qingyu Deng) [#38652](https://github.com/nodejs/node/pull/38652)
- [[`4c67437c53`](https://github.com/nodejs/node/commit/4c67437c53)] - **doc**: clarify DiffieHellmanGroup class docs (Nitzan Uziely) [#38363](https://github.com/nodejs/node/pull/38363)
- [[`e90c60b1e3`](https://github.com/nodejs/node/commit/e90c60b1e3)] - **doc**: use AIX instead of Aix in fs.md (Rich Trott) [#38535](https://github.com/nodejs/node/pull/38535)
- [[`dc67fec1b4`](https://github.com/nodejs/node/commit/dc67fec1b4)] - **doc**: remove extraneous dash from flag prefix (Rodolfo Carvalho) [#38532](https://github.com/nodejs/node/pull/38532)
- [[`4c54d81a59`](https://github.com/nodejs/node/commit/4c54d81a59)] - **doc**: document `'secureConnect'` event limitation (James M Snell) [#38447](https://github.com/nodejs/node/pull/38447)
- [[`839e8d1672`](https://github.com/nodejs/node/commit/839e8d1672)] - **doc**: mark querystring api as legacy (James M Snell) [#38436](https://github.com/nodejs/node/pull/38436)
- [[`a75b7af9bd`](https://github.com/nodejs/node/commit/a75b7af9bd)] - **doc**: add arguments for stream event of Http2Server and Http2SecureServer (Qingyu Deng) [#37892](https://github.com/nodejs/node/pull/37892)
- [[`cf0007edc4`](https://github.com/nodejs/node/commit/cf0007edc4)] - **doc**: indicate that abort tests do not generate core files (Rich Trott) [#38422](https://github.com/nodejs/node/pull/38422)
- [[`945450b5cf`](https://github.com/nodejs/node/commit/945450b5cf)] - **doc**: add try/catch in http2 respondWithFile example (Matteo Collina) [#38410](https://github.com/nodejs/node/pull/38410)
- [[`1f7cd7148a`](https://github.com/nodejs/node/commit/1f7cd7148a)] - **doc**: note the system requirements for V8 tests (DeeDeeG) [#38319](https://github.com/nodejs/node/pull/38319)
- [[`cd54834854`](https://github.com/nodejs/node/commit/cd54834854)] - **doc**: minor clarification to pathObject (James M Snell) [#38437](https://github.com/nodejs/node/pull/38437)
- [[`ba117c2c6f`](https://github.com/nodejs/node/commit/ba117c2c6f)] - **doc**: document new TCP_KEEPCNT and TCP_KEEPINTVL socket option defaults (Arnold Zokas) [#38313](https://github.com/nodejs/node/pull/38313)
- [[`dcdbaffced`](https://github.com/nodejs/node/commit/dcdbaffced)] - **doc**: do not mention TCP in the allowHalfOpen option description (Luigi Pinca) [#38360](https://github.com/nodejs/node/pull/38360)
- [[`fe8003e5de`](https://github.com/nodejs/node/commit/fe8003e5de)] - **doc**: update message to match actual output (Rich Trott) [#35271](https://github.com/nodejs/node/pull/35271)
- [[`c03f23e126`](https://github.com/nodejs/node/commit/c03f23e126)] - **doc**: request default snap track be updated for LTS (Rod Vagg) [#37708](https://github.com/nodejs/node/pull/37708)
- [[`a9f7aeed12`](https://github.com/nodejs/node/commit/a9f7aeed12)] - **doc**: mark `process.hrtime()` as legacy (Antoine du Hamel) [#38371](https://github.com/nodejs/node/pull/38371)
- [[`cede0c57b8`](https://github.com/nodejs/node/commit/cede0c57b8)] - **doc**: fix version history for `"exports"` patterns (Antoine du Hamel) [#38355](https://github.com/nodejs/node/pull/38355)
- [[`9702f22397`](https://github.com/nodejs/node/commit/9702f22397)] - **doc**: fix `package.json` `"imports"` field history (Antoine du Hamel) [#38356](https://github.com/nodejs/node/pull/38356)
- [[`2d96da875e`](https://github.com/nodejs/node/commit/2d96da875e)] - **doc**: fix typo in buffer.md (divlo) [#38323](https://github.com/nodejs/node/pull/38323)
- [[`6b58f28472`](https://github.com/nodejs/node/commit/6b58f28472)] - **doc**: add nodejs-sec email template (Daniel Bevenius) [#38290](https://github.com/nodejs/node/pull/38290)
- [[`5a532e4725`](https://github.com/nodejs/node/commit/5a532e4725)] - **doc**: update TSC members list with three new members (Rich Trott) [#38352](https://github.com/nodejs/node/pull/38352)
- [[`e994d6a27c`](https://github.com/nodejs/node/commit/e994d6a27c)] - **doc**: use `foo.prototype.bar` notation in buffer.md (Voltrex) [#38032](https://github.com/nodejs/node/pull/38032)
- [[`c61f363d66`](https://github.com/nodejs/node/commit/c61f363d66)] - **doc**: internal/test/binding for testing (Bradley Meck) [#38026](https://github.com/nodejs/node/pull/38026)
- [[`0bb6fe31b3`](https://github.com/nodejs/node/commit/0bb6fe31b3)] - **doc**: add missing events.on metadata (Anna Henningsen) [#37965](https://github.com/nodejs/node/pull/37965)
- [[`30c82b2745`](https://github.com/nodejs/node/commit/30c82b2745)] - **doc**: fix wording in outgoingMessage.write (Tobias Nießen) [#37894](https://github.com/nodejs/node/pull/37894)
- [[`932000020a`](https://github.com/nodejs/node/commit/932000020a)] - **doc**: fix grammar errors in http document (Qingyu Deng) [#37265](https://github.com/nodejs/node/pull/37265)
- [[`19e8ae44c4`](https://github.com/nodejs/node/commit/19e8ae44c4)] - **doc**: add document for http.OutgoingMessage (Qingyu Deng) [#37265](https://github.com/nodejs/node/pull/37265)
- [[`a6c123363d`](https://github.com/nodejs/node/commit/a6c123363d)] - **doc**: remove generated from dsaEncoding description (Marko Kaznovac) [#37459](https://github.com/nodejs/node/pull/37459)
- [[`bc6ea63e48`](https://github.com/nodejs/node/commit/bc6ea63e48)] - **doc**: document how to register external bindings for snapshot (Joyee Cheung) [#37463](https://github.com/nodejs/node/pull/37463)
- [[`2168e954aa`](https://github.com/nodejs/node/commit/2168e954aa)] - **doc**: document the NO_COLOR and FORCE_COLOR env vars (James M Snell) [#37477](https://github.com/nodejs/node/pull/37477)
- [[`2907848fc9`](https://github.com/nodejs/node/commit/2907848fc9)] - **doc**: clarify event.isTrusted text (Rich Trott) [#36827](https://github.com/nodejs/node/pull/36827)
- [[`7efa020892`](https://github.com/nodejs/node/commit/7efa020892)] - **doc**: expand openssl instructions (Michael Dawson) [#36554](https://github.com/nodejs/node/pull/36554)
- [[`b197a44152`](https://github.com/nodejs/node/commit/b197a44152)] - **doc**: document ABORT_ERR code (Benjamin Gruenbaum) [#36319](https://github.com/nodejs/node/pull/36319)
- [[`1d80f89442`](https://github.com/nodejs/node/commit/1d80f89442)] - **doc**: document changes for `*/promises` alias modules (ExE Boss) [#34002](https://github.com/nodejs/node/pull/34002)
- [[`9417fd0bc8`](https://github.com/nodejs/node/commit/9417fd0bc8)] - **errors**: align source-map stacks with spec (Benjamin Coe) [#37252](https://github.com/nodejs/node/pull/37252)
- [[`dcd221ce69`](https://github.com/nodejs/node/commit/dcd221ce69)] - **errors**: refactor to use more primordials (Antoine du Hamel) [#36651](https://github.com/nodejs/node/pull/36651)
- [[`ee444473e9`](https://github.com/nodejs/node/commit/ee444473e9)] - **errors**: display original symbol name (Benjamin Coe) [#36042](https://github.com/nodejs/node/pull/36042)
- [[`83d28374d6`](https://github.com/nodejs/node/commit/83d28374d6)] - **errors**: refactor to use more primordials (Antoine du Hamel) [#36167](https://github.com/nodejs/node/pull/36167)
- [[`7d7e34c15a`](https://github.com/nodejs/node/commit/7d7e34c15a)] - **errors**: refactor to use more primordials (Antoine du Hamel) [#35944](https://github.com/nodejs/node/pull/35944)
- [[`18e5c0f3e2`](https://github.com/nodejs/node/commit/18e5c0f3e2)] - **events**: refactor to use optional chaining (ZiJian Liu) [#36763](https://github.com/nodejs/node/pull/36763)
- [[`4fdcbae583`](https://github.com/nodejs/node/commit/4fdcbae583)] - **events**: refactor to use more primordials (Antoine du Hamel) [#36304](https://github.com/nodejs/node/pull/36304)
- [[`c4e7dca8f3`](https://github.com/nodejs/node/commit/c4e7dca8f3)] - **fs**: fix error when writing buffers \> INT32_MAX (Zach Bjornson) [#38546](https://github.com/nodejs/node/pull/38546)
- [[`07c55d2844`](https://github.com/nodejs/node/commit/07c55d2844)] - **_Revert_** "**http**: make HEAD method to work with keep-alive" (Michaël Zasso) [#38949](https://github.com/nodejs/node/pull/38949)
- [[`d8da265c81`](https://github.com/nodejs/node/commit/d8da265c81)] - **http2**: treat non-EOF empty frames like other invalid frames (Anna Henningsen) [#37875](https://github.com/nodejs/node/pull/37875)
- [[`c3bd0fdb73`](https://github.com/nodejs/node/commit/c3bd0fdb73)] - **http2**: fix setting options before handle exists (Anna Henningsen) [#37875](https://github.com/nodejs/node/pull/37875)
- [[`74fe1d8f0c`](https://github.com/nodejs/node/commit/74fe1d8f0c)] - **http2**: add support for TypedArray to getUnpackedSettings (Antoine du Hamel) [#36141](https://github.com/nodejs/node/pull/36141)
- [[`c90f1dbeb3`](https://github.com/nodejs/node/commit/c90f1dbeb3)] - **https**: refactor to use more primordials (Antoine du Hamel) [#36195](https://github.com/nodejs/node/pull/36195)
- [[`8258799472`](https://github.com/nodejs/node/commit/8258799472)] - **inspector**: remove redundant method for connection check (Yash Ladha) [#37986](https://github.com/nodejs/node/pull/37986)
- [[`ba19313e1e`](https://github.com/nodejs/node/commit/ba19313e1e)] - **inspector**: refactor to use more primordials (Antoine du Hamel) [#36356](https://github.com/nodejs/node/pull/36356)
- [[`eb8f7ee634`](https://github.com/nodejs/node/commit/eb8f7ee634)] - **lib**: revert primordials in a hot path (Antoine du Hamel) [#38248](https://github.com/nodejs/node/pull/38248)
- [[`cea8b4265c`](https://github.com/nodejs/node/commit/cea8b4265c)] - **lib**: make `IterableWeakMap` safe to iterate (Antoine du Hamel) [#38523](https://github.com/nodejs/node/pull/38523)
- [[`490bc58229`](https://github.com/nodejs/node/commit/490bc58229)] - **lib**: fix and improve os typings (Akhil Marsonya) [#38316](https://github.com/nodejs/node/pull/38316)
- [[`af39df6d03`](https://github.com/nodejs/node/commit/af39df6d03)] - **lib**: add URI handling functions to primordials (Antoine du Hamel) [#37394](https://github.com/nodejs/node/pull/37394)
- [[`16691be80e`](https://github.com/nodejs/node/commit/16691be80e)] - **lib**: fix WebIDL `object` and dictionary type conversion (ExE Boss) [#37047](https://github.com/nodejs/node/pull/37047)
- [[`47ed512312`](https://github.com/nodejs/node/commit/47ed512312)] - **lib**: refactor to use optional chaining in internal/options.js (raisinten) [#36939](https://github.com/nodejs/node/pull/36939)
- [[`346fc0ac21`](https://github.com/nodejs/node/commit/346fc0ac21)] - **lib**: support returning Safe collections from C++ (ExE Boss) [#36989](https://github.com/nodejs/node/pull/36989)
- [[`8912caba64`](https://github.com/nodejs/node/commit/8912caba64)] - **lib**: expose primordials object (Antoine du Hamel) [#36872](https://github.com/nodejs/node/pull/36872)
- [[`46c019b988`](https://github.com/nodejs/node/commit/46c019b988)] - **lib**: refactor source_map to use more primordials (Antoine du Hamel) [#36733](https://github.com/nodejs/node/pull/36733)
- [[`cf9556d8f7`](https://github.com/nodejs/node/commit/cf9556d8f7)] - **lib**: refactor source_map to avoid unsafe array iteration (Antoine du Hamel) [#36734](https://github.com/nodejs/node/pull/36734)
- [[`6eaf357f49`](https://github.com/nodejs/node/commit/6eaf357f49)] - **lib**: simplify `primordials.uncurryThis` (ExE Boss) [#36866](https://github.com/nodejs/node/pull/36866)
- [[`9338759b01`](https://github.com/nodejs/node/commit/9338759b01)] - **lib**: remove v8_prof_polyfill from eslint ignore list (Antoine du Hamel) [#36537](https://github.com/nodejs/node/pull/36537)
- [[`c9861a344a`](https://github.com/nodejs/node/commit/c9861a344a)] - **lib**: remove unused code (Brian White) [#36632](https://github.com/nodejs/node/pull/36632)
- [[`01a71dd393`](https://github.com/nodejs/node/commit/01a71dd393)] - **lib**: refactor to use more primordials in internal/encoding.js (raisinten) [#36480](https://github.com/nodejs/node/pull/36480)
- [[`e6c0877604`](https://github.com/nodejs/node/commit/e6c0877604)] - **lib**: refactor to use primordials in internal/priority_queue.js (ZiJian Liu) [#36560](https://github.com/nodejs/node/pull/36560)
- [[`6e3a2ffb98`](https://github.com/nodejs/node/commit/6e3a2ffb98)] - **lib**: add primordials.SafeStringIterator (Antoine du Hamel) [#36526](https://github.com/nodejs/node/pull/36526)
- [[`bf0738bc07`](https://github.com/nodejs/node/commit/bf0738bc07)] - **lib**: make safe primordials safe to construct (Antoine du Hamel) [#36428](https://github.com/nodejs/node/pull/36428)
- [[`7ebc18f293`](https://github.com/nodejs/node/commit/7ebc18f293)] - **lib**: make safe primordials safe to iterate (Antoine du Hamel) [#36391](https://github.com/nodejs/node/pull/36391)
- [[`e12dbc8519`](https://github.com/nodejs/node/commit/e12dbc8519)] - **lib**: refactor to use more primordials in internal/histogram.js (raisinten) [#36455](https://github.com/nodejs/node/pull/36455)
- [[`5daeac64a4`](https://github.com/nodejs/node/commit/5daeac64a4)] - **lib**: add uncurried accessor properties to `primordials` (ExE Boss) [#36329](https://github.com/nodejs/node/pull/36329)
- [[`bb4900d9eb`](https://github.com/nodejs/node/commit/bb4900d9eb)] - **lib**: refactor primordials.uncurryThis (Antoine du Hamel) [#36221](https://github.com/nodejs/node/pull/36221)
- [[`0fbe945ebb`](https://github.com/nodejs/node/commit/0fbe945ebb)] - **lib**: refactor to use more primordials (Antoine du Hamel) [#36140](https://github.com/nodejs/node/pull/36140)
- [[`24d4d63308`](https://github.com/nodejs/node/commit/24d4d63308)] - **lib**: add %TypedArray% abstract constructor to primordials (ExE Boss) [#36016](https://github.com/nodejs/node/pull/36016)
- [[`e2395b0f3b`](https://github.com/nodejs/node/commit/e2395b0f3b)] - **lib**: use Object static properties from primordials (Michaël Zasso) [#35380](https://github.com/nodejs/node/pull/35380)
- [[`b3e22e1612`](https://github.com/nodejs/node/commit/b3e22e1612)] - **lib,tools**: enforce access to prototype from primordials (Antoine du Hamel) [#36025](https://github.com/nodejs/node/pull/36025)
- [[`e94e0b488e`](https://github.com/nodejs/node/commit/e94e0b488e)] - **meta**: add v8 team (Jiawen Geng) [#38566](https://github.com/nodejs/node/pull/38566)
- [[`fcc6a00f1a`](https://github.com/nodejs/node/commit/fcc6a00f1a)] - **meta**: post comment when pr labeled fast-track (James M Snell) [#38446](https://github.com/nodejs/node/pull/38446)
- [[`bd0d9647ca`](https://github.com/nodejs/node/commit/bd0d9647ca)] - **module**: clarify CJS global-like variables not defined error message (Antoine du Hamel) [#37852](https://github.com/nodejs/node/pull/37852)
- [[`0fdb5d59f7`](https://github.com/nodejs/node/commit/0fdb5d59f7)] - **module**: refactor NativeModule to avoid unsafe array iteration (Antoine du Hamel) [#37656](https://github.com/nodejs/node/pull/37656)
- [[`77c7d979b6`](https://github.com/nodejs/node/commit/77c7d979b6)] - **module**: simplify tryStatSync with throwIfNoEntry option (Antoine du Hamel) [#36971](https://github.com/nodejs/node/pull/36971)
- [[`1aae572220`](https://github.com/nodejs/node/commit/1aae572220)] - **module**: refactor to use more primordials (Antoine du Hamel) [#36348](https://github.com/nodejs/node/pull/36348)
- [[`9e7f166161`](https://github.com/nodejs/node/commit/9e7f166161)] - **module**: refactor to use more primordials (Antoine du Hamel) [#36024](https://github.com/nodejs/node/pull/36024)
- [[`eee1d291cf`](https://github.com/nodejs/node/commit/eee1d291cf)] - **module**: refactor to use iterable-weak-map (Benjamin Coe) [#35915](https://github.com/nodejs/node/pull/35915)
- [[`52cbe89f7f`](https://github.com/nodejs/node/commit/52cbe89f7f)] - **net**: refactor to use more primordials (Antoine du Hamel) [#36303](https://github.com/nodejs/node/pull/36303)
- [[`779ad54078`](https://github.com/nodejs/node/commit/779ad54078)] - **node-api**: faster threadsafe_function (Fedor Indutny) [#38506](https://github.com/nodejs/node/pull/38506)
- [[`5995221ced`](https://github.com/nodejs/node/commit/5995221ced)] - **node-api**: fix shutdown crashes (Michael Dawson) [#38492](https://github.com/nodejs/node/pull/38492)
- [[`d8acec4cb1`](https://github.com/nodejs/node/commit/d8acec4cb1)] - **node-api**: make reference weak parameter an indirect link to references (Chengzhong Wu) [#38000](https://github.com/nodejs/node/pull/38000)
- [[`c442d89ad6`](https://github.com/nodejs/node/commit/c442d89ad6)] - **os**: refactor to use more primordials (Antoine du Hamel) [#36284](https://github.com/nodejs/node/pull/36284)
- [[`daeb6fcd78`](https://github.com/nodejs/node/commit/daeb6fcd78)] - **path**: inline conditions (Voltrex) [#38613](https://github.com/nodejs/node/pull/38613)
- [[`e2f531f646`](https://github.com/nodejs/node/commit/e2f531f646)] - **path**: refactor to use more primordials (Akhil Marsonya) [#37893](https://github.com/nodejs/node/pull/37893)
- [[`c1364d15a2`](https://github.com/nodejs/node/commit/c1364d15a2)] - **path**: refactor to use more primordials (Antoine du Hamel) [#36302](https://github.com/nodejs/node/pull/36302)
- [[`726ef40fcb`](https://github.com/nodejs/node/commit/726ef40fcb)] - **perf_hooks**: throw ERR_INVALID_ARG_VALUE if histogram.percentile param is NaN (ZiJian Liu) [#36937](https://github.com/nodejs/node/pull/36937)
- [[`4686f4f41b`](https://github.com/nodejs/node/commit/4686f4f41b)] - **perf_hooks**: refactor to avoid unsafe array iteration (Antoine du Hamel) [#36723](https://github.com/nodejs/node/pull/36723)
- [[`6adec6351e`](https://github.com/nodejs/node/commit/6adec6351e)] - **perf_hooks**: refactor to use more primordials (Antoine du Hamel) [#36297](https://github.com/nodejs/node/pull/36297)
- [[`bf9aa425c0`](https://github.com/nodejs/node/commit/bf9aa425c0)] - **policy**: refactor to use more primordials (Antoine du Hamel) [#36210](https://github.com/nodejs/node/pull/36210)
- [[`0f6c3f76b3`](https://github.com/nodejs/node/commit/0f6c3f76b3)] - **querystring**: refactor to use more primordials (Antoine du Hamel) [#36315](https://github.com/nodejs/node/pull/36315)
- [[`b5b8a996f3`](https://github.com/nodejs/node/commit/b5b8a996f3)] - **readline**: refactor to use more primordials (Antoine du Hamel) [#36296](https://github.com/nodejs/node/pull/36296)
- [[`cd981808b4`](https://github.com/nodejs/node/commit/cd981808b4)] - **repl**: document top level await limitation with const/let (James M Snell) [#38449](https://github.com/nodejs/node/pull/38449)
- [[`a4eb5571eb`](https://github.com/nodejs/node/commit/a4eb5571eb)] - **repl**: display prompt once after error callback (Anna Henningsen) [#38314](https://github.com/nodejs/node/pull/38314)
- [[`163fcecb69`](https://github.com/nodejs/node/commit/163fcecb69)] - **src**: fix multiple AddLinkedBinding() calls (Anna Henningsen) [#39012](https://github.com/nodejs/node/pull/39012)
- [[`8809ef98f9`](https://github.com/nodejs/node/commit/8809ef98f9)] - **src**: update cares_wrap OpenBSD defines (Anna Henningsen) [#38670](https://github.com/nodejs/node/pull/38670)
- [[`d66f88ce97`](https://github.com/nodejs/node/commit/d66f88ce97)] - **src**: remove extra semi after member fn (Shelley Vohr) [#38686](https://github.com/nodejs/node/pull/38686)
- [[`bc2111c7e6`](https://github.com/nodejs/node/commit/bc2111c7e6)] - **src**: make workers messaging more resilient (Juan José Arboleda) [#38510](https://github.com/nodejs/node/pull/38510)
- [[`378e0e778b`](https://github.com/nodejs/node/commit/378e0e778b)] - **src**: fix validation of negative offset to avoid abort (James M Snell) [#38421](https://github.com/nodejs/node/pull/38421)
- [[`c170026b7b`](https://github.com/nodejs/node/commit/c170026b7b)] - **src**: use %progbits instead of @progbits (Stephen Gallagher) [#38312](https://github.com/nodejs/node/pull/38312)
- [[`d177541b0e`](https://github.com/nodejs/node/commit/d177541b0e)] - **src**: fix setting Converter sub char length (James M Snell) [#38331](https://github.com/nodejs/node/pull/38331)
- [[`e279b029c0`](https://github.com/nodejs/node/commit/e279b029c0)] - **src**: avoid deferred gc/cleanup for Buffer.from (James M Snell) [#38337](https://github.com/nodejs/node/pull/38337)
- [[`006c7b78da`](https://github.com/nodejs/node/commit/006c7b78da)] - **src**: indent long help text properly (David Glasser) [#37911](https://github.com/nodejs/node/pull/37911)
- [[`f5541ddea3`](https://github.com/nodejs/node/commit/f5541ddea3)] - **src**: fix ETW_WRITE_EMPTY_EVENT macro (Michaël Zasso) [#37334](https://github.com/nodejs/node/pull/37334)
- [[`6b1052d034`](https://github.com/nodejs/node/commit/6b1052d034)] - **src**: disable unfixable MSVC warnings (Michaël Zasso) [#37334](https://github.com/nodejs/node/pull/37334)
- [[`38afa3fa79`](https://github.com/nodejs/node/commit/38afa3fa79)] - **src**: avoid implicit type conversions (take 2) (Michaël Zasso) [#37334](https://github.com/nodejs/node/pull/37334)
- [[`8a60ae2161`](https://github.com/nodejs/node/commit/8a60ae2161)] - **src**: fix compiler warnings in node_buffer.cc (Darshan Sen) [#38722](https://github.com/nodejs/node/pull/38722)
- [[`78cde14c45`](https://github.com/nodejs/node/commit/78cde14c45)] - **src**: fix compiler warning in env.cc (Anna Henningsen) [#35547](https://github.com/nodejs/node/pull/35547)
- [[`ea311a41cc`](https://github.com/nodejs/node/commit/ea311a41cc)] - **src**: add check against non-weak BaseObjects at process exit (Anna Henningsen) [#35490](https://github.com/nodejs/node/pull/35490)
- [[`a1b4681efc`](https://github.com/nodejs/node/commit/a1b4681efc)] - **src**: use transferred consistently (Daniel Bevenius) [#36340](https://github.com/nodejs/node/pull/36340)
- [[`29c623e5cb`](https://github.com/nodejs/node/commit/29c623e5cb)] - **src**: fix label indentation (Rich Trott) [#36213](https://github.com/nodejs/node/pull/36213)
- [[`dbb0d2612c`](https://github.com/nodejs/node/commit/dbb0d2612c)] - **stream**: fix multiple Writable.destroy() calls (Robert Nagy) [#38221](https://github.com/nodejs/node/pull/38221)
- [[`a18b1ff80b`](https://github.com/nodejs/node/commit/a18b1ff80b)] - **stream**: the position of \_read() is wrong (helloyou2012) [#38292](https://github.com/nodejs/node/pull/38292)
- [[`ab130e929a`](https://github.com/nodejs/node/commit/ab130e929a)] - **stream**: only use legacy close listeners if not willEmitClose (Robert Nagy) [#36649](https://github.com/nodejs/node/pull/36649)
- [[`c31e2f6b0f`](https://github.com/nodejs/node/commit/c31e2f6b0f)] - **stream**: fix legacy pipe error handling (Robert Nagy) [#35257](https://github.com/nodejs/node/pull/35257)
- [[`1dc4dea215`](https://github.com/nodejs/node/commit/1dc4dea215)] - **string_decoder**: throw ERR_STRING_TOO_LONG for UTF-8 (Michaël Zasso) [#36661](https://github.com/nodejs/node/pull/36661)
- [[`0db9101922`](https://github.com/nodejs/node/commit/0db9101922)] - **string_decoder**: refactor to use more primordials (Antoine du Hamel) [#36358](https://github.com/nodejs/node/pull/36358)
- [[`8a44ee478e`](https://github.com/nodejs/node/commit/8a44ee478e)] - **test**: improve coverage of lib/\_http_client.js (Rongjian Zhang) [#38599](https://github.com/nodejs/node/pull/38599)
- [[`8a45b85dbd`](https://github.com/nodejs/node/commit/8a45b85dbd)] - **test**: improve coverage of lib/os.js (Rongjian Zhang) [#38653](https://github.com/nodejs/node/pull/38653)
- [[`d7c6a3eb03`](https://github.com/nodejs/node/commit/d7c6a3eb03)] - **test**: call functions internally (Voltrex) [#38560](https://github.com/nodejs/node/pull/38560)
- [[`726cb48bd8`](https://github.com/nodejs/node/commit/726cb48bd8)] - **test**: complete coverage of querystring (Rongjian Zhang) [#38520](https://github.com/nodejs/node/pull/38520)
- [[`4f1ba79eb8`](https://github.com/nodejs/node/commit/4f1ba79eb8)] - **test**: increase coverage for AbortController (ZiJian Liu) [#38514](https://github.com/nodejs/node/pull/38514)
- [[`d98b355336`](https://github.com/nodejs/node/commit/d98b355336)] - **test**: run message and pseudo-tty tests in parallel (Richard Lau) [#38502](https://github.com/nodejs/node/pull/38502)
- [[`7938af6565`](https://github.com/nodejs/node/commit/7938af6565)] - **test**: move test-net-connect-econnrefused from pummel to sequential (Rich Trott) [#38462](https://github.com/nodejs/node/pull/38462)
- [[`52f3837518`](https://github.com/nodejs/node/commit/52f3837518)] - **test**: fix `common.mustCall` `length` and `name` properties (Antoine du Hamel) [#38464](https://github.com/nodejs/node/pull/38464)
- [[`fdfb39e692`](https://github.com/nodejs/node/commit/fdfb39e692)] - **test**: address deprecation warning (Rich Trott) [#38448](https://github.com/nodejs/node/pull/38448)
- [[`25e5afe3be`](https://github.com/nodejs/node/commit/25e5afe3be)] - **test**: move abort test from pummel to abort directory (Rich Trott) [#38396](https://github.com/nodejs/node/pull/38396)
- [[`296b969e0a`](https://github.com/nodejs/node/commit/296b969e0a)] - **test**: skip some pummel tests on slower machines (Rich Trott) [#38394](https://github.com/nodejs/node/pull/38394)
- [[`a9ff9c0918`](https://github.com/nodejs/node/commit/a9ff9c0918)] - **test**: add ancestor package.json checks for tmpdir (Richard Lau) [#38285](https://github.com/nodejs/node/pull/38285)
- [[`c9ce98c377`](https://github.com/nodejs/node/commit/c9ce98c377)] - **test**: replace function with arrow function and remove unused argument (Andres) [#38235](https://github.com/nodejs/node/pull/38235)
- [[`c77abf5a89`](https://github.com/nodejs/node/commit/c77abf5a89)] - **test**: use .test domain for not found address (Richard Lau) [#38286](https://github.com/nodejs/node/pull/38286)
- [[`d9eb8b3ed0`](https://github.com/nodejs/node/commit/d9eb8b3ed0)] - **test**: increase fs promise coverage (Emil Sivervik) [#36813](https://github.com/nodejs/node/pull/36813)
- [[`d85b70fffa`](https://github.com/nodejs/node/commit/d85b70fffa)] - **test**: increase timeout on ASAN Action (Antoine du Hamel) [#37007](https://github.com/nodejs/node/pull/37007)
- [[`836fba52ea`](https://github.com/nodejs/node/commit/836fba52ea)] - **test**: improve coverage of `SourceTextModule` getters (Juan José Arboleda) [#37013](https://github.com/nodejs/node/pull/37013)
- [[`f43fc6b6cc`](https://github.com/nodejs/node/commit/f43fc6b6cc)] - **test**: improve coverage for `Module` getters (Juan José Arboleda) [#36950](https://github.com/nodejs/node/pull/36950)
- [[`a45d280c18`](https://github.com/nodejs/node/commit/a45d280c18)] - **test**: improve coverage on worker threads (Juan José Arboleda) [#36910](https://github.com/nodejs/node/pull/36910)
- [[`ec4d79e259`](https://github.com/nodejs/node/commit/ec4d79e259)] - **test**: improve coverage at `lib/internal/vm/module.js` (Juan José Arboleda) [#36898](https://github.com/nodejs/node/pull/36898)
- [[`c34de75687`](https://github.com/nodejs/node/commit/c34de75687)] - **test**: guard large string decoder allocation (Michaël Zasso) [#36795](https://github.com/nodejs/node/pull/36795)
- [[`3215a58843`](https://github.com/nodejs/node/commit/3215a58843)] - **test**: add already-aborted-controller test for spawn() (Rich Trott) [#36644](https://github.com/nodejs/node/pull/36644)
- [[`c3b116795b`](https://github.com/nodejs/node/commit/c3b116795b)] - **test**: add test for reused AbortController with execfile() (Rich Trott) [#36644](https://github.com/nodejs/node/pull/36644)
- [[`219ed0ba4c`](https://github.com/nodejs/node/commit/219ed0ba4c)] - **test**: add Actions annotation output (Mary Marchini) [#34590](https://github.com/nodejs/node/pull/34590)
- [[`89ee6abae0`](https://github.com/nodejs/node/commit/89ee6abae0)] - **test**: use `.then(common.mustCall())` for all async IIFEs (Anna Henningsen) [#34363](https://github.com/nodejs/node/pull/34363)
- [[`294b3e60a5`](https://github.com/nodejs/node/commit/294b3e60a5)] - **test,doc,lib**: adjust object literal newlines for lint rule (Rich Trott) [#37040](https://github.com/nodejs/node/pull/37040)
- [[`bfe02b8808`](https://github.com/nodejs/node/commit/bfe02b8808)] - **test,readline**: improve tab completion coverage (Antoine du Hamel) [#38465](https://github.com/nodejs/node/pull/38465)
- [[`1dc7fd238c`](https://github.com/nodejs/node/commit/1dc7fd238c)] - **timers**: fix unsafe array iteration (Darshan Sen) [#37223](https://github.com/nodejs/node/pull/37223)
- [[`679973866d`](https://github.com/nodejs/node/commit/679973866d)] - **timers**: reject with AbortError on cancellation (Benjamin Gruenbaum) [#36317](https://github.com/nodejs/node/pull/36317)
- [[`dec3610a31`](https://github.com/nodejs/node/commit/dec3610a31)] - **timers**: refactor to use more primordials (Antoine du Hamel) [#36132](https://github.com/nodejs/node/pull/36132)
- [[`d84b05a619`](https://github.com/nodejs/node/commit/d84b05a619)] - **timers**: cleanup abort listener on awaitable timers (James M Snell) [#36006](https://github.com/nodejs/node/pull/36006)
- [[`f6e4dbb779`](https://github.com/nodejs/node/commit/f6e4dbb779)] - **tls**: validate ticket keys buffer (Antoine du Hamel) [#38308](https://github.com/nodejs/node/pull/38308)
- [[`661e9809bd`](https://github.com/nodejs/node/commit/661e9809bd)] - **tls**: fix session and keylog add listener segfault (Nitzan Uziely) [#38180](https://github.com/nodejs/node/pull/38180)
- [[`de44e90523`](https://github.com/nodejs/node/commit/de44e90523)] - **tools**: refloat 7 Node.js patches to cpplint.py (Rich Trott) [#36324](https://github.com/nodejs/node/pull/36324)
- [[`37bc7d5945`](https://github.com/nodejs/node/commit/37bc7d5945)] - **tools**: bump cpplint to 1.5.4 (Rich Trott) [#36324](https://github.com/nodejs/node/pull/36324)
- [[`84e918858e`](https://github.com/nodejs/node/commit/84e918858e)] - **tools**: refloat 7 Node.js patches to cpplint.py (Rich Trott) [#36235](https://github.com/nodejs/node/pull/36235)
- [[`fb2bb93f95`](https://github.com/nodejs/node/commit/fb2bb93f95)] - **tools**: bump cpplint to 1.5.3 (Rich Trott) [#36235](https://github.com/nodejs/node/pull/36235)
- [[`3351910f97`](https://github.com/nodejs/node/commit/3351910f97)] - **tools**: refloat 7 Node.js patches to cpplint.py (Rich Trott) [#36213](https://github.com/nodejs/node/pull/36213)
- [[`193b18effa`](https://github.com/nodejs/node/commit/193b18effa)] - **tools**: bump cpplint.py to 1.5.2 (Rich Trott) [#36213](https://github.com/nodejs/node/pull/36213)
- [[`8a6c35d735`](https://github.com/nodejs/node/commit/8a6c35d735)] - **tools**: update ESLint to 7.27.0 (Luigi Pinca) [#38764](https://github.com/nodejs/node/pull/38764)
- [[`f8753b6299`](https://github.com/nodejs/node/commit/f8753b6299)] - **tools**: update ESLint to 7.26.0 (Colin Ihrig) [#38605](https://github.com/nodejs/node/pull/38605)
- [[`1098aec40b`](https://github.com/nodejs/node/commit/1098aec40b)] - **tools**: update ESLint to 7.25.0 (Colin Ihrig) [#38378](https://github.com/nodejs/node/pull/38378)
- [[`3fbabfa94d`](https://github.com/nodejs/node/commit/3fbabfa94d)] - **tools**: update ESLint to 7.24.0 (Colin Ihrig) [#38179](https://github.com/nodejs/node/pull/38179)
- [[`6ce779cd8b`](https://github.com/nodejs/node/commit/6ce779cd8b)] - **tools**: update ESLint to 7.23.0 (Luigi Pinca) [#37979](https://github.com/nodejs/node/pull/37979)
- [[`77f88e7725`](https://github.com/nodejs/node/commit/77f88e7725)] - **tools**: update ESLint to 7.22.0 (Colin Ihrig) [#37734](https://github.com/nodejs/node/pull/37734)
- [[`5de911eeaf`](https://github.com/nodejs/node/commit/5de911eeaf)] - **tools**: make update-eslint.sh work with npm@7 (Luigi Pinca) [#37566](https://github.com/nodejs/node/pull/37566)
- [[`839976669f`](https://github.com/nodejs/node/commit/839976669f)] - **tools**: add support for mjs and cjs JS snippet linting (Antoine du Hamel) [#37311](https://github.com/nodejs/node/pull/37311)
- [[`2463bd0689`](https://github.com/nodejs/node/commit/2463bd0689)] - **tools**: update eslint-plugin-markdown configuration (Colin Ihrig) [#37549](https://github.com/nodejs/node/pull/37549)
- [[`f868fac455`](https://github.com/nodejs/node/commit/f868fac455)] - **tools**: enable object-curly-newline in ESLint rules (Rich Trott) [#37040](https://github.com/nodejs/node/pull/37040)
- [[`d13508d219`](https://github.com/nodejs/node/commit/d13508d219)] - **tools**: make GH Actions workflows work if default branch is not master (Antoine du Hamel) [#38516](https://github.com/nodejs/node/pull/38516)
- [[`7021c31d06`](https://github.com/nodejs/node/commit/7021c31d06)] - **tools**: use mktemp to create the workspace directory (Luigi Pinca) [#38432](https://github.com/nodejs/node/pull/38432)
- [[`16a3e555ba`](https://github.com/nodejs/node/commit/16a3e555ba)] - **tools**: use a shallow clone of the npm/cli repository (Luigi Pinca) [#38463](https://github.com/nodejs/node/pull/38463)
- [[`3484a23140`](https://github.com/nodejs/node/commit/3484a23140)] - **tools**: remove fixer for non-ascii-character ESLint custom rule (Rich Trott) [#38413](https://github.com/nodejs/node/pull/38413)
- [[`aec4b295e4`](https://github.com/nodejs/node/commit/aec4b295e4)] - **tools**: fix doc generation when version info is not available (Antoine du Hamel) [#38398](https://github.com/nodejs/node/pull/38398)
- [[`0172b110a3`](https://github.com/nodejs/node/commit/0172b110a3)] - **tools**: add \_depot_tools to PATH (for V8 tests) (DeeDeeG) [#38299](https://github.com/nodejs/node/pull/38299)
- [[`d0eed18c87`](https://github.com/nodejs/node/commit/d0eed18c87)] - **tools**: fix type mismatch in test runner (Richard Lau) [#38289](https://github.com/nodejs/node/pull/38289)
- [[`11ca018db9`](https://github.com/nodejs/node/commit/11ca018db9)] - **tools**: simplify eslint comma-dangle configuration (tools) (Rich Trott) [#37883](https://github.com/nodejs/node/pull/37883)
- [[`f7c14e86a7`](https://github.com/nodejs/node/commit/f7c14e86a7)] - **tools**: simplify eslint comma-dangle configuration (Rich Trott) [#37850](https://github.com/nodejs/node/pull/37850)
- [[`241e05795b`](https://github.com/nodejs/node/commit/241e05795b)] - **tools**: run doctool tests on GitHub Actions CI (Antoine du Hamel) [#37398](https://github.com/nodejs/node/pull/37398)
- [[`a4dd50f8f9`](https://github.com/nodejs/node/commit/a4dd50f8f9)] - **tools**: refactor prefer-primordials (Antoine du Hamel) [#36018](https://github.com/nodejs/node/pull/36018)
- [[`4af3906e72`](https://github.com/nodejs/node/commit/4af3906e72)] - **tools**: update ESLint to 7.21.0 (Luigi Pinca) [#37546](https://github.com/nodejs/node/pull/37546)
- [[`955880de1a`](https://github.com/nodejs/node/commit/955880de1a)] - **tools**: update ESLint to 7.20.0 (Colin Ihrig) [#37339](https://github.com/nodejs/node/pull/37339)
- [[`42c1f98a31`](https://github.com/nodejs/node/commit/42c1f98a31)] - **tools**: update ESLint to 7.19.0 (Colin Ihrig) [#37159](https://github.com/nodejs/node/pull/37159)
- [[`25eb720b4d`](https://github.com/nodejs/node/commit/25eb720b4d)] - **tools**: update ESLint to 7.18.0 (Colin Ihrig) [#36955](https://github.com/nodejs/node/pull/36955)
- [[`4983ef205e`](https://github.com/nodejs/node/commit/4983ef205e)] - **tools**: update gyp-next to v0.7.0 (Michaël Zasso) [#36580](https://github.com/nodejs/node/pull/36580)
- [[`613378da1e`](https://github.com/nodejs/node/commit/613378da1e)] - **tools**: update ESLint to 7.17.0 (Colin Ihrig) [#36726](https://github.com/nodejs/node/pull/36726)
- [[`e6d01f6545`](https://github.com/nodejs/node/commit/e6d01f6545)] - **tools**: update ESLint to 7.16.0 (Yongsheng Zhang) [#36579](https://github.com/nodejs/node/pull/36579)
- [[`98806da810`](https://github.com/nodejs/node/commit/98806da810)] - **tools**: enable no-unsafe-optional-chaining lint rule (Colin Ihrig) [#36411](https://github.com/nodejs/node/pull/36411)
- [[`7d411920f6`](https://github.com/nodejs/node/commit/7d411920f6)] - **tools**: update ESLint to 7.15.0 (Colin Ihrig) [#36411](https://github.com/nodejs/node/pull/36411)
- [[`226a86c3b5`](https://github.com/nodejs/node/commit/226a86c3b5)] - **tools**: enable no-unused-expressions lint rule (Michaël Zasso) [#36248](https://github.com/nodejs/node/pull/36248)
- [[`24a81c7d6c`](https://github.com/nodejs/node/commit/24a81c7d6c)] - **tools**: enable no-nonoctal-decimal-escape lint rule (Colin Ihrig) [#36217](https://github.com/nodejs/node/pull/36217)
- [[`19d4eb17b9`](https://github.com/nodejs/node/commit/19d4eb17b9)] - **tools**: update ESLint to 7.14.0 (Colin Ihrig) [#36217](https://github.com/nodejs/node/pull/36217)
- [[`9fa8d2037f`](https://github.com/nodejs/node/commit/9fa8d2037f)] - **tools**: add linting rule for async IIFEs (Anna Henningsen) [#34363](https://github.com/nodejs/node/pull/34363)
- [[`55fc206d13`](https://github.com/nodejs/node/commit/55fc206d13)] - **tools**: update ESLint to 7.13.0 (Luigi Pinca) [#36031](https://github.com/nodejs/node/pull/36031)
- [[`937fc0a30c`](https://github.com/nodejs/node/commit/937fc0a30c)] - **tools**: update ESLint to 7.12.1 (Colin Ihrig) [#35799](https://github.com/nodejs/node/pull/35799)
- [[`29d0840a90`](https://github.com/nodejs/node/commit/29d0840a90)] - **tools**: update ESLint to 7.12.0 (Colin Ihrig) [#35799](https://github.com/nodejs/node/pull/35799)
- [[`dcbd44758c`](https://github.com/nodejs/node/commit/dcbd44758c)] - **tools**: update ESLint to 7.11.0 (Colin Ihrig) [#35578](https://github.com/nodejs/node/pull/35578)
- [[`c7751b4e69`](https://github.com/nodejs/node/commit/c7751b4e69)] - **tools**: add new ESLint rule: prefer-primordials (Leko) [#35448](https://github.com/nodejs/node/pull/35448)
- [[`9a5411a2b4`](https://github.com/nodejs/node/commit/9a5411a2b4)] - **tools,doc**: add support for several flavors of JS code snippets (Antoine du Hamel) [#37162](https://github.com/nodejs/node/pull/37162)
- [[`e19478aa76`](https://github.com/nodejs/node/commit/e19478aa76)] - **tools,lib**: recommend using safe primordials (Antoine du Hamel) [#36026](https://github.com/nodejs/node/pull/36026)
- [[`5f848a612d`](https://github.com/nodejs/node/commit/5f848a612d)] - **tools,lib**: tighten prefer-primordials rules for Error statics (Antoine du Hamel) [#36017](https://github.com/nodejs/node/pull/36017)
- [[`716076e389`](https://github.com/nodejs/node/commit/716076e389)] - **tty**: refactor to avoid unsafe array iteration (Antoine du Hamel) [#36771](https://github.com/nodejs/node/pull/36771)
- [[`41d74a4d9a`](https://github.com/nodejs/node/commit/41d74a4d9a)] - **tty**: refactor to use more primordials (Zijian Liu) [#36272](https://github.com/nodejs/node/pull/36272)
- [[`e35a3543fd`](https://github.com/nodejs/node/commit/e35a3543fd)] - **typings**: add JSDoc typings for util (Rohit Gohri) [#38213](https://github.com/nodejs/node/pull/38213)
- [[`c8b22185f7`](https://github.com/nodejs/node/commit/c8b22185f7)] - **url**: refactor to use more primordials (Antoine du Hamel) [#36316](https://github.com/nodejs/node/pull/36316)
- [[`e113035c9a`](https://github.com/nodejs/node/commit/e113035c9a)] - **util**: simplify constructor retrieval in inspect() (Rich Trott) [#36466](https://github.com/nodejs/node/pull/36466)
- [[`1551b40d01`](https://github.com/nodejs/node/commit/1551b40d01)] - **v8**: refactor to use more primordials (Antoine du Hamel) [#36527](https://github.com/nodejs/node/pull/36527)
- [[`6c1bbb5caf`](https://github.com/nodejs/node/commit/6c1bbb5caf)] - **v8**: refactor to use more primordials (Antoine du Hamel) [#36285](https://github.com/nodejs/node/pull/36285)
- [[`3aee77d279`](https://github.com/nodejs/node/commit/3aee77d279)] - **vm**: refactor to avoid unsafe array iteration (Antoine du Hamel) [#36752](https://github.com/nodejs/node/pull/36752)
- [[`0dea86634d`](https://github.com/nodejs/node/commit/0dea86634d)] - **wasi**: refactor to avoid unsafe array iteration (Antoine du Hamel) [#36724](https://github.com/nodejs/node/pull/36724)
- [[`2c66305ac4`](https://github.com/nodejs/node/commit/2c66305ac4)] - **_Revert_** "**worker**: remove `ERR_CLOSED_MESSAGE_PORT`" (Juan José Arboleda) [#38510](https://github.com/nodejs/node/pull/38510)
- [[`698bffaa90`](https://github.com/nodejs/node/commit/698bffaa90)] - **worker**: refactor to avoid unsafe array iteration (Antoine du Hamel) [#37346](https://github.com/nodejs/node/pull/37346)
- [[`3d4785c174`](https://github.com/nodejs/node/commit/3d4785c174)] - **worker**: refactor to use more primordials (Antoine du Hamel) [#36267](https://github.com/nodejs/node/pull/36267)
- [[`8702b045a4`](https://github.com/nodejs/node/commit/8702b045a4)] - **zlib**: fix brotli flush range (Khaidi Chu) [#38408](https://github.com/nodejs/node/pull/38408)
- [[`459fe6864e`](https://github.com/nodejs/node/commit/459fe6864e)] - **zlib**: refactor to avoid unsafe array iteration (Antoine du Hamel) [#36722](https://github.com/nodejs/node/pull/36722)
- [[`740638de0f`](https://github.com/nodejs/node/commit/740638de0f)] - **zlib**: refactor to use primordial instead of \<string\>.startsWith (Rohan Chougule) [#36718](https://github.com/nodejs/node/pull/36718)
- [[`32e10f388c`](https://github.com/nodejs/node/commit/32e10f388c)] - **zlib**: refactor to use more primordials (Antoine du Hamel) [#36347](https://github.com/nodejs/node/pull/36347)

Windows 32-bit Installer: https://nodejs.org/dist/v14.17.1/node-v14.17.1-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v14.17.1/node-v14.17.1-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v14.17.1/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v14.17.1/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v14.17.1/node-v14.17.1.pkg \
macOS Intel 64-bit Binary: https://nodejs.org/dist/v14.17.1/node-v14.17.1-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v14.17.1/node-v14.17.1-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v14.17.1/node-v14.17.1-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v14.17.1/node-v14.17.1-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v14.17.1/node-v14.17.1-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v14.17.1/node-v14.17.1-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v14.17.1/node-v14.17.1-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v14.17.1/node-v14.17.1.tar.gz \
Other release files: https://nodejs.org/dist/v14.17.1/ \
Documentation: https://nodejs.org/docs/v14.17.1/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

b2aaa7d5cffd4ea950aa65e92ffa88781e74b0dd29028963c2b74a58bd72ff04  node-v14.17.1-aix-ppc64.tar.gz
864d09627c8dc9038e0235fccf2110b60c8942713c15352de2d203278798ff0d  node-v14.17.1-darwin-x64.tar.gz
79c2b290ff06cd95ddcb42cf5e83322f8b533ff9f6f8665e780e3b06212ecda1  node-v14.17.1-darwin-x64.tar.xz
ee72ed2c935c0d162969c5bcdeb29fc83a2883c0b6757f4510d7bd70512a9418  node-v14.17.1-headers.tar.gz
da9534360a814e258226505a9f097e6c90b373f4ea75cb5fadd70506371ea85d  node-v14.17.1-headers.tar.xz
04e25f5511408288913dd1955f6829431e5096911aa3e35c9cd0ca8b39e6c4c5  node-v14.17.1-linux-arm64.tar.gz
5285c1716b0992984112255ef982c4e5ba3ec1b1202319be2f15ce3a24592a80  node-v14.17.1-linux-arm64.tar.xz
b3a7b0dfe6e841ce67ce8a947d13dde50d7bba4505558cc0cc617afe1ce08b48  node-v14.17.1-linux-armv7l.tar.gz
05784d934f93b6b7a22c2cf170ba88f3c647b85fb9953e178d6be7d692387609  node-v14.17.1-linux-armv7l.tar.xz
0a0a3b721d22c42309915c5951c8b701776914caa4fbcda570fba3f9b36efee1  node-v14.17.1-linux-ppc64le.tar.gz
c9e88eeebc139a4fedeb749452082fd15826b358709a1d16aeb84fd1ec8b4543  node-v14.17.1-linux-ppc64le.tar.xz
1abfd4a39b2656ea95b6b03e0f8d5a0ce225923f73f886ba6d877706ec0a172b  node-v14.17.1-linux-s390x.tar.gz
0ec89ac56f39ff75003d79b3a95c64336f711e580ab1b75e015a3662456cc693  node-v14.17.1-linux-s390x.tar.xz
4781b162129b19bdb3a7010cab12d06fc7c89421ea3fda03346ed17f09ceacd6  node-v14.17.1-linux-x64.tar.gz
2921eba80c839e06d68b60b27fbbcbc7822df437f3f8d58717ec5a7703563ba4  node-v14.17.1-linux-x64.tar.xz
dbcaa370c322325beaa56847c06169f11d2e5acc7719e4c8395e740c3bbefade  node-v14.17.1.pkg
f85297faa15529cf134e9cfd395371fea62e092c3fe2127f2b0fdf8504905cee  node-v14.17.1.tar.gz
ddf1d2d56ddf35ecd98c5ea5ddcd690b245899f289559b4330c921255f5a247f  node-v14.17.1.tar.xz
d99bf8769198188ce4609e222e988de66dd2c3c529a9cebebc56556879c85fe4  node-v14.17.1-win-x64.7z
c2264359aa15c0a95d347ebb382413a597d1698a4a2c8af73309b6e4e517ff04  node-v14.17.1-win-x64.zip
75396248fd5b5e0d6617aa8029fb07bc5dcf036e1d33ff82896e33224c43e0cd  node-v14.17.1-win-x86.7z
45b0996f28c8eeafc128e4a9d86f2f06e9f376a41b5db46dc69e89ce61e44a54  node-v14.17.1-win-x86.zip
f4cea916af6d80784f5d9daab59292ceba04bf636e4e2e6727153f89b1e6990f  node-v14.17.1-x64.msi
995420f483a181b0e535dd8f4661a7d6fe8f2d1601adb8dbc48f896d619d5ab0  node-v14.17.1-x86.msi
4b648906236eb32914407b46e1f9c217a23199306be05287bf912cf4362e41aa  win-x64/node.exe
44ff33abb17d86cb3be368527d018acb6fda3d724ff7f0d81eab5ba2e0ec46ae  win-x64/node.lib
019c8d96c5ce1e8875b11d411541f621f7b0aab1f355c35ad356dc89d2a85e74  win-x64/node_pdb.7z
01b4f47e6f16ee237c06512813914bdd5b6cca025fe7a568328193bd6d83ba31  win-x64/node_pdb.zip
7b0368e9a8f38cb13bb6940b94ced71a57a4ca823b58029f0587e784f7bf6a82  win-x86/node.exe
593ea194f25d7be97988d710d380320244b27c067067ebc4afdcf4bb22e4e78c  win-x86/node.lib
ba30a318e4700dbce5a781faf5a148ef80f5908f057591157af9b59b5f4191ef  win-x86/node_pdb.7z
45905a33717f05405443f8f29f186aba734d60ed8c668bd2d80e2f734b0b436f  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEj8yhP+8dDC6RAI4Jdw96mlrhVgAFAmDIoSgACgkQdw96mlrh
VgCfoQ//Zf0LnaX01O90ineCZmnlJM1RTON3F6GeR+c62jLiZ4JcQja+c4Lp2BDq
i9XGChx7beOgZOefiGv1S1El8dAeeo4pPcEvwP225pmJuNVy3wdMeIEeHpGeYMnw
kMfZGtnAjUkT+tnXSXMhqqYoOTGeMHf8yeviBkEQqVd4sriheXpK1/iztcOfjqtj
7D30V3IOllOQ2vn5B+slfALUJBstFo44dnAMmdeuMXl/ySX/yA7kBh1moEGfuuPP
s9d81T1MwKMJEg6EAVK+6k/wDx7tDgPfO0b/C4FgwaeyE0qspCfEPKq0tGmXM7t2
nwhq2EaTHPCEhpkwFjgjVdUhUELvH1fEkQO0vKLS4xEWIJ8l1E/Vf+JfVIWKxC1N
dJ94eF+9899HkT7mC1MOldDbrk8MQOgIhhHbl1vxqbU0wRv4FqBB+QLWPPSAZrgc
5QE2rAVBO4gVmTGEvBqYs0fXgFHnH2jxXknog7S0aV6rG3hhpF25fNFF5q3rLL+F
SnN5bu+fsZndIUJGLJ+Ph7Sjotalyrhijq9CNlhBeg0m53hlt/Ul9oYfTjwa3QIC
GzDncJ69ZcTLWio70cBI0llTJ21V1YDzgaWTFZIRIPe7CT68bvmDOpoG+cGZGAJM
c+OT13V1ZHmVf0puHir4bt2WcauaCt8rdRCCZZff8BBJMA90bsA=
=HnL5
-----END PGP SIGNATURE-----

```
