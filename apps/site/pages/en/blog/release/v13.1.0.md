---
date: '2019-11-06T07:52:53.897Z'
category: release
title: Node v13.1.0 (Current)
layout: blog-post
author: <PERSON><PERSON><PERSON>
---

### Notable Changes

- **cli**:
  - Added a new flag (`--trace-uncaught`) that makes Node.js print the stack
    trace at the time of throwing uncaught exceptions, rather than at the
    creation of the `Error` object, if there is any. This is disabled by default
    because it affects GC behavior (<PERSON>) [#30025](https://github.com/nodejs/node/pull/30025).
- **crypto**:
  - Added `Hash.prototype.copy()` method. It returns a new `Hash` object with
    its internal state cloned from the original one (<PERSON>) [#29910](https://github.com/nodejs/node/pull/29910).
- **dgram**:
  - Added source-specific multicast support. This adds methods to Datagram
    sockets to support [RFC 4607](https://tools.ietf.org/html/rfc4607) for IPv4
    and IPv6 (<PERSON>) [#15735](https://github.com/nodejs/node/pull/15735).
- **fs**:
  - Added a `bufferSize` option to `fs.opendir()`. It allows to control the
    number of entries that are buffered internally when reading from the
    directory (<PERSON> Henning<PERSON>) [#30114](https://github.com/nodejs/node/pull/30114).
- **meta**:
  - Added [Chengzhong Wu](https://github.com/legendecas) to collaborators [#30115](https://github.com/nodejs/node/pull/30115).

### Commits

- [[`445837851b`](https://github.com/nodejs/node/commit/445837851b)] - **async_hooks**: only emit `after` for AsyncResource if stack not empty (Anna Henningsen) [#30087](https://github.com/nodejs/node/pull/30087)
- [[`8860bd68b6`](https://github.com/nodejs/node/commit/8860bd68b6)] - **buffer**: improve performance caused by primordials (Jizu Sun) [#30235](https://github.com/nodejs/node/pull/30235)
- [[`1bded9841c`](https://github.com/nodejs/node/commit/1bded9841c)] - **build**: fix detection of Visual Studio 2017 (Richard Lau) [#30119](https://github.com/nodejs/node/pull/30119)
- [[`49e7f042f9`](https://github.com/nodejs/node/commit/49e7f042f9)] - **build**: add workaround for WSL (gengjiawen) [#30221](https://github.com/nodejs/node/pull/30221)
- [[`03827ddf38`](https://github.com/nodejs/node/commit/03827ddf38)] - **build**: allow Python 3.8 (Michaël Zasso) [#30194](https://github.com/nodejs/node/pull/30194)
- [[`54698113c0`](https://github.com/nodejs/node/commit/54698113c0)] - **build**: find Python syntax errors in dependencies (Christian Clauss) [#30143](https://github.com/nodejs/node/pull/30143)
- [[`b255688d5f`](https://github.com/nodejs/node/commit/b255688d5f)] - **build**: fix pkg-config search for libnghttp2 (Ben Noordhuis) [#30145](https://github.com/nodejs/node/pull/30145)
- [[`8980d8c25f`](https://github.com/nodejs/node/commit/8980d8c25f)] - **build**: vcbuild uses default Python, not Py2 (João Reis) [#30091](https://github.com/nodejs/node/pull/30091)
- [[`cedad02406`](https://github.com/nodejs/node/commit/cedad02406)] - **build**: prefer python 3 over 2 for configure (Sam Roberts) [#30091](https://github.com/nodejs/node/pull/30091)
- [[`5ba842b8f9`](https://github.com/nodejs/node/commit/5ba842b8f9)] - **build**: python3 support for configure (Rod Vagg) [#30047](https://github.com/nodejs/node/pull/30047)
- [[`d05f67caef`](https://github.com/nodejs/node/commit/d05f67caef)] - **cli**: whitelist new V8 flag in NODE_OPTIONS (Shelley Vohr) [#30094](https://github.com/nodejs/node/pull/30094)
- [[`5ca58646c1`](https://github.com/nodejs/node/commit/5ca58646c1)] - **(SEMVER-MINOR)** **cli**: add --trace-uncaught flag (Anna Henningsen) [#30025](https://github.com/nodejs/node/pull/30025)
- [[`8b75aabee9`](https://github.com/nodejs/node/commit/8b75aabee9)] - **crypto**: guard with OPENSSL_NO_GOST (Shelley Vohr) [#30050](https://github.com/nodejs/node/pull/30050)
- [[`1d03df4c5e`](https://github.com/nodejs/node/commit/1d03df4c5e)] - **(SEMVER-MINOR)** **crypto**: add Hash.prototype.copy() method (Ben Noordhuis) [#29910](https://github.com/nodejs/node/pull/29910)
- [[`46c9194ec8`](https://github.com/nodejs/node/commit/46c9194ec8)] - **deps**: V8: cherry-pick a7dffcd767be (Christian Clauss) [#30218](https://github.com/nodejs/node/pull/30218)
- [[`104bfb9a38`](https://github.com/nodejs/node/commit/104bfb9a38)] - **deps**: V8: cherry-pick e5dbc95 (Gabriel Schulhof) [#30130](https://github.com/nodejs/node/pull/30130)
- [[`e3124481c2`](https://github.com/nodejs/node/commit/e3124481c2)] - **deps**: update npm to 6.12.1 (Michael Perrotte) [#30164](https://github.com/nodejs/node/pull/30164)
- [[`f3d00c594d`](https://github.com/nodejs/node/commit/f3d00c594d)] - **deps**: V8: backport 777fa98 (Michaël Zasso) [#30062](https://github.com/nodejs/node/pull/30062)
- [[`1cfa98c23e`](https://github.com/nodejs/node/commit/1cfa98c23e)] - **deps**: V8: cherry-pick c721203 (Michaël Zasso) [#30065](https://github.com/nodejs/node/pull/30065)
- [[`0d9ae1b8f6`](https://github.com/nodejs/node/commit/0d9ae1b8f6)] - **deps**: V8: cherry-pick ed40ab1 (Michaël Zasso) [#30064](https://github.com/nodejs/node/pull/30064)
- [[`a63f7e73c4`](https://github.com/nodejs/node/commit/a63f7e73c4)] - **(SEMVER-MINOR)** **dgram**: add source-specific multicast support (Lucas Pardue) [#15735](https://github.com/nodejs/node/pull/15735)
- [[`fc407bb555`](https://github.com/nodejs/node/commit/fc407bb555)] - **doc**: add missing hash for header link (Nick Schonning) [#30188](https://github.com/nodejs/node/pull/30188)
- [[`201a60e6ba`](https://github.com/nodejs/node/commit/201a60e6ba)] - **doc**: linkify `.setupMaster()` in cluster doc (Trivikram Kamat) [#30204](https://github.com/nodejs/node/pull/30204)
- [[`b7070f315f`](https://github.com/nodejs/node/commit/b7070f315f)] - **doc**: explain http2 aborted event callback (dev-313) [#30179](https://github.com/nodejs/node/pull/30179)
- [[`f8fb2c06c5`](https://github.com/nodejs/node/commit/f8fb2c06c5)] - **doc**: linkify `.fork()` in cluster documentation (Anna Henningsen) [#30163](https://github.com/nodejs/node/pull/30163)
- [[`ae81360214`](https://github.com/nodejs/node/commit/ae81360214)] - **doc**: update AUTHORS list (Michaël Zasso) [#30142](https://github.com/nodejs/node/pull/30142)
- [[`1499a72a1f`](https://github.com/nodejs/node/commit/1499a72a1f)] - **doc**: improve doc Http2Session:Timeout (dev-313) [#30161](https://github.com/nodejs/node/pull/30161)
- [[`3709b5cc7e`](https://github.com/nodejs/node/commit/3709b5cc7e)] - **doc**: move inactive Collaborators to emeriti (Rich Trott) [#30177](https://github.com/nodejs/node/pull/30177)
- [[`a48d17900b`](https://github.com/nodejs/node/commit/a48d17900b)] - **doc**: add options description for send APIs (dev-313) [#29868](https://github.com/nodejs/node/pull/29868)
- [[`dfb4a24695`](https://github.com/nodejs/node/commit/dfb4a24695)] - **doc**: fix an error in resolution algorithm steps (Alex Zherdev) [#29940](https://github.com/nodejs/node/pull/29940)
- [[`403a648a16`](https://github.com/nodejs/node/commit/403a648a16)] - **doc**: fix numbering in require algorithm (Jan Krems) [#30117](https://github.com/nodejs/node/pull/30117)
- [[`e4ab6fced1`](https://github.com/nodejs/node/commit/e4ab6fced1)] - **doc**: remove incorrect and outdated example (Tobias Nießen) [#30138](https://github.com/nodejs/node/pull/30138)
- [[`3c23224a76`](https://github.com/nodejs/node/commit/3c23224a76)] - **doc**: adjust code sample for stream.finished (Cotton Hou) [#29983](https://github.com/nodejs/node/pull/29983)
- [[`d91d270416`](https://github.com/nodejs/node/commit/d91d270416)] - **doc**: claim NODE_MODULE_VERSION=80 for Electron 9 (Samuel Attard) [#30052](https://github.com/nodejs/node/pull/30052)
- [[`621eaf9ed5`](https://github.com/nodejs/node/commit/621eaf9ed5)] - **doc**: remove "it is important to" phrasing (Rich Trott) [#30108](https://github.com/nodejs/node/pull/30108)
- [[`9a71091098`](https://github.com/nodejs/node/commit/9a71091098)] - **doc**: revise os.md (Rich Trott) [#30102](https://github.com/nodejs/node/pull/30102)
- [[`381c6cd0d2`](https://github.com/nodejs/node/commit/381c6cd0d2)] - **doc**: delete "a number of" things in the docs (Rich Trott) [#30103](https://github.com/nodejs/node/pull/30103)
- [[`45c70a9793`](https://github.com/nodejs/node/commit/45c70a9793)] - **doc**: remove dashes (Rich Trott) [#30101](https://github.com/nodejs/node/pull/30101)
- [[`ea9d125536`](https://github.com/nodejs/node/commit/ea9d125536)] - **doc**: add legendecas to collaborators (legendecas) [#30115](https://github.com/nodejs/node/pull/30115)
- [[`39070bbed0`](https://github.com/nodejs/node/commit/39070bbed0)] - **doc**: make YAML matter consistent in crypto.md (Rich Trott) [#30016](https://github.com/nodejs/node/pull/30016)
- [[`978946e38b`](https://github.com/nodejs/node/commit/978946e38b)] - **doc,meta**: prefer aliases and stubs over Runtime Deprecations (Rich Trott) [#30153](https://github.com/nodejs/node/pull/30153)
- [[`32a538901f`](https://github.com/nodejs/node/commit/32a538901f)] - **doc,n-api**: sort bottom-of-the-page references (Gabriel Schulhof) [#30124](https://github.com/nodejs/node/pull/30124)
- [[`07b5584a3f`](https://github.com/nodejs/node/commit/07b5584a3f)] - **(SEMVER-MINOR)** **fs**: add `bufferSize` option to `fs.opendir()` (Anna Henningsen) [#30114](https://github.com/nodejs/node/pull/30114)
- [[`2505f678ef`](https://github.com/nodejs/node/commit/2505f678ef)] - **http**: support readable hwm in IncomingMessage (Colin Ihrig) [#30135](https://github.com/nodejs/node/pull/30135)
- [[`f01c5c51b0`](https://github.com/nodejs/node/commit/f01c5c51b0)] - **inspector**: turn platform tasks that outlive Agent into no-ops (Anna Henningsen) [#30031](https://github.com/nodejs/node/pull/30031)
- [[`050efebf24`](https://github.com/nodejs/node/commit/050efebf24)] - **meta**: use contact_links instead of issue templates (Michaël Zasso) [#30172](https://github.com/nodejs/node/pull/30172)
- [[`edfbee3727`](https://github.com/nodejs/node/commit/edfbee3727)] - **module**: resolve self-references (Jan Krems) [#29327](https://github.com/nodejs/node/pull/29327)
- [[`93b1bb8cb5`](https://github.com/nodejs/node/commit/93b1bb8cb5)] - **n-api,doc**: add info about building n-api addons (Jim Schlight) [#30032](https://github.com/nodejs/node/pull/30032)
- [[`cc1cd2b3c5`](https://github.com/nodejs/node/commit/cc1cd2b3c5)] - **src**: isolate-\>Dispose() order consistency (Shelley Vohr) [#30181](https://github.com/nodejs/node/pull/30181)
- [[`a0df91cce1`](https://github.com/nodejs/node/commit/a0df91cce1)] - **(SEMVER-MINOR)** **src**: expose granular SetIsolateUpForNode (Shelley Vohr) [#30150](https://github.com/nodejs/node/pull/30150)
- [[`ec7b69ff05`](https://github.com/nodejs/node/commit/ec7b69ff05)] - **src**: change env.h includes for forward declarations (Alexandre Ferrando) [#30133](https://github.com/nodejs/node/pull/30133)
- [[`98c8f76dd1`](https://github.com/nodejs/node/commit/98c8f76dd1)] - **src**: split up InitializeContext (Shelley Vohr) [#30067](https://github.com/nodejs/node/pull/30067)
- [[`d78e3176dd`](https://github.com/nodejs/node/commit/d78e3176dd)] - **src**: fix crash with SyntheticModule#setExport (Michaël Zasso) [#30062](https://github.com/nodejs/node/pull/30062)
- [[`fd0aded233`](https://github.com/nodejs/node/commit/fd0aded233)] - **src**: allow inspector without v8 platform (Shelley Vohr) [#30049](https://github.com/nodejs/node/pull/30049)
- [[`87f14e13b3`](https://github.com/nodejs/node/commit/87f14e13b3)] - **stream**: extract Readable.from in its own file (Matteo Collina) [#30140](https://github.com/nodejs/node/pull/30140)
- [[`1d9f4278dd`](https://github.com/nodejs/node/commit/1d9f4278dd)] - **test**: use arrow functions for callbacks (Minuk Park) [#30069](https://github.com/nodejs/node/pull/30069)
- [[`a03809d7dd`](https://github.com/nodejs/node/commit/a03809d7dd)] - **test**: verify npm compatibility with releases (Michaël Zasso) [#30082](https://github.com/nodejs/node/pull/30082)
- [[`68e4b5a1fc`](https://github.com/nodejs/node/commit/68e4b5a1fc)] - **tools**: fix Python 3 deprecation warning in test.py (Loris Zinsou) [#30208](https://github.com/nodejs/node/pull/30208)
- [[`348ec693ac`](https://github.com/nodejs/node/commit/348ec693ac)] - **tools**: fix Python 3 syntax error in mac_tool.py (Christian Clauss) [#30146](https://github.com/nodejs/node/pull/30146)
- [[`e2fb353df3`](https://github.com/nodejs/node/commit/e2fb353df3)] - **tools**: use print() function in buildbot_run.py (Christian Clauss) [#30148](https://github.com/nodejs/node/pull/30148)
- [[`bcbcce5983`](https://github.com/nodejs/node/commit/bcbcce5983)] - **tools**: undefined name opts -\> args in gyptest.py (Christian Clauss) [#30144](https://github.com/nodejs/node/pull/30144)
- [[`14981f5bba`](https://github.com/nodejs/node/commit/14981f5bba)] - **tools**: git rm -r tools/v8_gypfiles/broken (Christian Clauss) [#30149](https://github.com/nodejs/node/pull/30149)
- [[`d549a34597`](https://github.com/nodejs/node/commit/d549a34597)] - **tools**: update ESLint to 6.6.0 (Colin Ihrig) [#30123](https://github.com/nodejs/node/pull/30123)
- [[`a3757546e8`](https://github.com/nodejs/node/commit/a3757546e8)] - **tools**: doc: improve async workflow of generate.js (Theotime Poisseau) [#30106](https://github.com/nodejs/node/pull/30106)

Windows 32-bit Installer: https://nodejs.org/dist/v13.1.0/node-v13.1.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v13.1.0/node-v13.1.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v13.1.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v13.1.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v13.1.0/node-v13.1.0.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v13.1.0/node-v13.1.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v13.1.0/node-v13.1.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v13.1.0/node-v13.1.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v13.1.0/node-v13.1.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v13.1.0/node-v13.1.0-aix-ppc64.tar.gz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v13.1.0/node-v13.1.0-sunos-x64.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v13.1.0/node-v13.1.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v13.1.0/node-v13.1.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v13.1.0/node-v13.1.0.tar.gz \
Other release files: https://nodejs.org/dist/v13.1.0/ \
Documentation: https://nodejs.org/docs/v13.1.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

5faec026afe9052f277402500236dd9ec4d58e0e8de93f66989fc337c65e33c8  node-v13.1.0-aix-ppc64.tar.gz
6501c1bcf2babb5b9c81dcff8b52021f726da8f6ee28df1637acade1a16c7d39  node-v13.1.0-darwin-x64.tar.gz
b918bdc6ca5726084a737c926744cdaecde624ba39ac8aaed889f296007a5094  node-v13.1.0-darwin-x64.tar.xz
577785216f5a4097a9108fd05f3117556f0954f0a03a26cfbfd1ce7af94587d4  node-v13.1.0-headers.tar.gz
73ec6759903137d3e22806e745777cb7cda3150f0d81c5ba13e7599c2be03d32  node-v13.1.0-headers.tar.xz
dd36c7846f7713b6e55baf0b6ab7882c18b129d83a3d0f7ef62790d181461d22  node-v13.1.0-linux-arm64.tar.gz
646d597e6b0dc400429e46b703a5135c77bd71e653ea4c8254d0b60c17b6ec1d  node-v13.1.0-linux-arm64.tar.xz
88450bc38dac0be15c9bd09bfccf4ce79f1911930f37658c730c151b26c5aa97  node-v13.1.0-linux-armv7l.tar.gz
e15601106fbd9ed75a4642eab91bb3f3ddab8b5821e886bdb070c014d9ab9af2  node-v13.1.0-linux-armv7l.tar.xz
0c804f0671bcdada9be66e4ca844c08f897357fbf2426be8cc326d20e6362833  node-v13.1.0-linux-ppc64le.tar.gz
561dbaa36db7dcb4fff378e110b4ddd29d918754e73449208899fbfb9f3ce1ae  node-v13.1.0-linux-ppc64le.tar.xz
24c29e917230d9cc510ba764ff6ef57bcc816520c146b47dce3ed4aeb3f7d333  node-v13.1.0-linux-s390x.tar.gz
a3229a795ee8bda15396701badd31d695d9a6f487a730c73022305c98522767a  node-v13.1.0-linux-s390x.tar.xz
490e998198e152450e79bb65178813ce0c81708954697f91cfd82537acfcb588  node-v13.1.0-linux-x64.tar.gz
2eecb5a4b7975c3b406bee36b12c9a29e8bedf9553c88cad310b8f076db00881  node-v13.1.0-linux-x64.tar.xz
3b6abd2a5f7a5778bbe5363ea222910b19674648eb2b3d734d9ee751ec29da71  node-v13.1.0.pkg
8e17a613950018e27f34f7268e01cbb385777189961ccf386f7d0d05883687b3  node-v13.1.0-sunos-x64.tar.gz
e9dd61b7f537b98a6656c0e03766997aa712bf3d40e7cb08ba4019e942846739  node-v13.1.0-sunos-x64.tar.xz
df640a2f151f788d02dc25c91d80fffe06b4c3c72fbdee07ab9abd7c6879d6cd  node-v13.1.0.tar.gz
d42c056cbd33b35836b0f5cfb2c56712b965ea76e188ef79af492614cf14cb68  node-v13.1.0.tar.xz
8f242259fa929f759f06bb1cf399df3b81061eb1ce5ccaa9d206dc157fcf93fc  node-v13.1.0-win-x64.7z
d735e97bdeb7b74551b9d165c708a3fdea4dbb3801a65e70f6d6ae3539d48a03  node-v13.1.0-win-x64.zip
c4ecf16dd147956c9335b547fdfb1b0eb1c333c1a1722843b45ba04b9fee17fe  node-v13.1.0-win-x86.7z
81af54ba9e852073ac975aa0955da1efb4b2ea6ef213eb3ce90f3b22a82555d8  node-v13.1.0-win-x86.zip
22ec0be47b0f3f5f163f82e18c01df456921f39b756ee4a828534264b06d0fe6  node-v13.1.0-x64.msi
7f56fa7350eaf0da6d33b24578a281e8d759a9344e5c8ea851ea541eaebb94fe  node-v13.1.0-x86.msi
5c3b0006330a08743363ebf26ccdf1618ab0269f50756088b6e5257d7a32c5a1  win-x64/node.exe
aa183bbe6f7a2b63395ff83ac6f687274bafbc1e64be2e6e5bb600dc1e5c7bea  win-x64/node.lib
7e4bc02444eea52b90d15d1985789aa10efa5b058a6550aac389db3eba42a4b7  win-x64/node_pdb.7z
ae389590498393e57379d462b3d8d6c2639afcf04d6c279f99b8e2122b98b94e  win-x64/node_pdb.zip
20fb3aa33b88628c533e59fec344db02683434cfbd91d70a36e3a0c3f865ed03  win-x86/node.exe
caa896eb2249fdf32ed3dda1c7bdb8250b8dbdf6f5498b10a4dec91f541d2d11  win-x86/node.lib
e2301d3ae38b01fecceb8111955568a741183de87d05c88a6499abfca1e913b1  win-x86/node_pdb.7z
5d9cc82ef9f2165f08ad4057b23c90154bf6a334ed7f9d3b5757003f70f49108  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEj8yhP+8dDC6RAI4Jdw96mlrhVgAFAl3CewoACgkQdw96mlrh
VgAkxg/+LP/OscdB2TMVtDJRH2EsJaE3kzGupi4/RdDRGqVkl2NA1o014iYFXRvE
1hhh/zTSH6XKkc6hjByB+DFU0JIcyPtFePGbzHzZCf83e4sMJlZWZpY2z9lJtIYC
XU1YG2mlTl1D9YIfBuvZkCVdUEA1OORh4fAcOE6vlhppJ1WdX3onTTVAiom33ru3
Q/Uvqi6TKrS2RT37KRxvRRTln+WQLQMcmKc5mZqYxUPWw5cD1z5gSYgclDzIIqgg
jIRi7lZ3aIeXZBZ5IbQi2NhR6/J8po+Snq6woEjCcG+iuPY8pqlq3D00BGPNdi/1
QLpHnYBaGB4xO+W8vpj5BCkkFz8J977TAMFVIbaSQdtTs9Ios2q6rRXfz3/rAasd
SFTz5SPaE4OcCwc7csEqYbRkCarOwfOeDRt9j4ZWqsyDn9ngYEX0P3G5GXMTt284
eydw/avvKefSfZOC2TfGVUVxM2a8tAaBeO1zxcDVxn+t/M3YchgKYbh8Ui61MDRf
BT73EJ1WA4NLqp9kx2DI13+1M7CyIexUbK8AGhTDRnvXB4eP0zAOF+BpSfar4qQ9
qBFB88X7EPKCaCEDQ9tyKUKweKZZ0vWX0tW327j+5bqaNeoYsT/Yjx02Wxlry2K3
AmMSxyAuLoc5AsH0TE2lqwdsA+I8g+PrQqksQoZl3WK57beP1EU=
=PLvr
-----END PGP SIGNATURE-----

```
