---
date: '2021-02-23T13:01:45.553Z'
category: release
title: Node v12.21.0 (LTS)
layout: blog-post
author: <PERSON>
---

### Notable changes

Vulnerabilities fixed:

- **CVE-2021-22883**: HTTP2 'unknownProtocol' cause Denial of Service by resource exhaustion
  - Affected Node.js versions are vulnerable to denial of service attacks when too many connection attempts with an 'unknownProtocol' are established. This leads to a leak of file descriptors. If a file descriptor limit is configured on the system, then the server is unable to accept new connections and prevent the process also from opening, e.g. a file. If no file descriptor limit is configured, then this lead to an excessive memory usage and cause the system to run out of memory.
- **CVE-2021-22884**: DNS rebinding in --inspect
  - Affected Node.js versions are vulnerable to denial of service attacks when the whitelist includes “localhost6”. When “localhost6” is not present in /etc/hosts, it is just an ordinary domain that is resolved via DNS, i.e., over network. If the attacker controls the victim's DNS server or can spoof its responses, the DNS rebinding protection can be bypassed by using the “localhost6” domain. As long as the attacker uses the “localhost6” domain, they can still apply the attack described in CVE-2018-7160.
- **CVE-2021-23840**: OpenSSL - Integer overflow in CipherUpdate
  - This is a vulnerability in OpenSSL which may be exploited through Node.js. You can read more about it in https://www.openssl.org/news/secadv/20210216.txt

### Commits

- [[`e69177a088`](https://github.com/nodejs/node/commit/e69177a088)] - **deps**: update archs files for OpenSSL-1.1.1j (Daniel Bevenius) [#37413](https://github.com/nodejs/node/pull/37413)
- [[`0633ae77e6`](https://github.com/nodejs/node/commit/0633ae77e6)] - **deps**: upgrade openssl sources to 1.1.1j (Daniel Bevenius) [#37413](https://github.com/nodejs/node/pull/37413)
- [[`922ada7713`](https://github.com/nodejs/node/commit/922ada7713)] - **(SEMVER-MINOR)** **http2**: add unknownProtocol timeout (Daniel Bevenius) [nodejs-private/node-private#246](https://github.com/nodejs-private/node-private/pull/246)
- [[`1564752d55`](https://github.com/nodejs/node/commit/1564752d55)] - **src**: drop localhost6 as allowed host for inspector (Matteo Collina) [nodejs-private/node-private#244](https://github.com/nodejs-private/node-private/pull/244)

Windows 32-bit Installer: https://nodejs.org/dist/v12.21.0/node-v12.21.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v12.21.0/node-v12.21.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v12.21.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v12.21.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v12.21.0/node-v12.21.0.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v12.21.0/node-v12.21.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v12.21.0/node-v12.21.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v12.21.0/node-v12.21.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v12.21.0/node-v12.21.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v12.21.0/node-v12.21.0-aix-ppc64.tar.gz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v12.21.0/node-v12.21.0-sunos-x64.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v12.21.0/node-v12.21.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v12.21.0/node-v12.21.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v12.21.0/node-v12.21.0.tar.gz \
Other release files: https://nodejs.org/dist/v12.21.0/ \
Documentation: https://nodejs.org/docs/v12.21.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

10a487471ebd720f0d643c9e8e919db580baf852b812788f00db736d2e634d77  node-v12.21.0-aix-ppc64.tar.gz
4d0b5d07d41a16909fdeb41c3158c27bcdccf16231cccf76d5eb6835e2076e90  node-v12.21.0-darwin-x64.tar.gz
4184cc5412cdf256996aa7f559859abc355b48f03144349cf8531b6bf0526f49  node-v12.21.0-darwin-x64.tar.xz
f708e19706d1c486b80ee2420cb8ef5d25c0958bf05f7c880519f97b8bf6d46d  node-v12.21.0-headers.tar.gz
5a59cb03e24e7a23f3e9ba236627604f14c9296c461034fc6126beeacb1befa9  node-v12.21.0-headers.tar.xz
5748bfc5bbf7d9c1c8e79bd4f71d8f049c7fc7bc5b52e04685633319843c4f93  node-v12.21.0-linux-arm64.tar.gz
66fcb5a975fbc2dec449fab5eedd947b92cc2a4ad02085be5c3277896abab252  node-v12.21.0-linux-arm64.tar.xz
6edc31a210e47eb72b0a2a150f7fe604539c1b2a45e8c81d378ac9315053a54f  node-v12.21.0-linux-armv7l.tar.gz
00c0a0ac0841e3ca3eefd561b54ec5e32978255489d64f4077410660d3bee1a6  node-v12.21.0-linux-armv7l.tar.xz
6fea17dc8bc059692dce1a149b2eb49c837f8b8569ba1c5b9a51a955b9df22f3  node-v12.21.0-linux-ppc64le.tar.gz
19cb55de1337b1beb7a8a7241f624b70ddc39687f9f1aabc0a524f2cdee8bce7  node-v12.21.0-linux-ppc64le.tar.xz
e521bc915c0568995f3083bf069ef41a930585a35f4cac50e17208be125c865d  node-v12.21.0-linux-s390x.tar.gz
038872f0ac5d061c1f299383a7faf9abc1c2c82314e08b2c548a0e5baf99c29f  node-v12.21.0-linux-s390x.tar.xz
ab121de3c472d76ec425480b0594e43109ee607bd57c3d5314bdb65fa816bf1c  node-v12.21.0-linux-x64.tar.gz
eb89c02153cfa25e40170e5e9b0ab43ad55d456af8b72ad2a8c2a42b7a647432  node-v12.21.0-linux-x64.tar.xz
30d8bee18a5f874104997a0383abe9c8bc983971c1cb0d7be7b49ef957e3b196  node-v12.21.0.pkg
d44d09355caea12f280f2854c2f6e933446b49b65e926bcb5e50bd0ab2b17d73  node-v12.21.0-sunos-x64.tar.gz
08b76d199feb0f2c76742c192ee6040f61d6f551ca6bbc1618bd2e387699e27a  node-v12.21.0-sunos-x64.tar.xz
36e862555bebc04b13f7afebf4472c4dd7ebf4c891f9d6746ec545a4f099d05e  node-v12.21.0.tar.gz
052f37ace6f569b513b5a1154b2a45d3c4d8b07d7d7c807b79f1566db61e979d  node-v12.21.0.tar.xz
3d635d2cc254a3d203182d2051b57ad3e2000afecc3cb8da0128ae1bf610b040  node-v12.21.0-win-x64.7z
d8ae037fb8be60e74fb96124e341fdf1251eae0d5d88d7d86f056d4b0c9440f3  node-v12.21.0-win-x64.zip
892429ab069d325622040dc81f35c8903c9271dd2abcf21a50cc0eb260356426  node-v12.21.0-win-x86.7z
1e966131d9d65107d8ca1dbdaa997533a6f7375af4b9dbc2d38d786d37b271b5  node-v12.21.0-win-x86.zip
aa644a95369423095d274e3f7a4ee4826021c7b54c1b267de0c855578d9599db  node-v12.21.0-x64.msi
221d142409fd750c2eb7fc829e597b3a28f16622e64dd05f27bf62e17503cbb3  node-v12.21.0-x86.msi
dfc0a5056def827b5e16a9dcf0c8bad65e09fa327de03839116fc32f802d427d  win-x64/node.exe
2662b79e36ee678661554d290a3b8277c4c92cc74dffcda37a9f8f8e83287c73  win-x64/node.lib
6132bece56ab20f45388b83c16e8fadc2c1fa84ab2c7bcbccd2e729c08e99e9d  win-x64/node_pdb.7z
bd49185210e0a32410f42c3cc0c5dca12c9423a7a8e8db653557b0e2157cee2d  win-x64/node_pdb.zip
68f086de221ee2e0bd40968400181cb9bd4b0ca38f6370a5665b5fac0fd85f20  win-x86/node.exe
794dd4c597af2483d162426a37c99746d319aaa358219ace7bb179140f16d5f2  win-x86/node.lib
4f42fb8127392963c5144c24d5655cbd01cfb17b0a6337e4e8d9289498c28e8b  win-x86/node_pdb.7z
ba116759b56690967d41fc27101a4e5659a2f8fd853e9010a3b1407575423325  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEyC+jrhy+3Gvka5NgxDzsRcF6uTwFAmA0+9oACgkQxDzsRcF6
uTzz0w//WLPLC2aIyxwP2Gp733ZcdmKOD7Fjip1woPEWA9FQe49+uJMBdFrVuyM3
cdXehkqtCL/hVehSbQzMyWAnQraz5k631hXx1yjPIXHVd/kM/8Mo9m5eVUSXNn8o
yRAmWINI0RffbxlKcnslcyRE92Lq8QCJs4+/LB6VmHEv7jF/T34Gy493pMiwNBBU
kNQvslUJUcxrY546/45urOSsLraWDY6xOsNIBOQONUNX5GSLS7PZdKyUsjb8v46o
W0r0tnPrn5aT8HIVluSjxZi2MUChQYI63ytNfym7+NRa1sV8slaSiEH9h5UzOAIr
tAwY/Jm11Zx8M2VNNlkSMlA2U9LEcPjpZBsT5yofrHfMk+Om19J5JPlNMlOaOtSZ
O35VnT9xwvF3F9XQh9n2URm/d9WPCcnSg9Vum5AqjnCFuzfLpXBFaHqe67GkudGr
pQ39r40pnLLZ04vYiNDnfCyF7Jpkq39HCEMdLUVYtq5uztXfGE7vsK/VkNemr3Rs
nKGDAWQ4orV6BEivlPG41J+ed8k/FT2h/SIvGimVL6F2X5KXaQ11YMBujKQCXhc2
1xCsREXu/gNj1Khfg3bGhiLU39VQglaP3dudJ/JohbhAbK3r/hoI8dS/DZXMbsT+
r8Ku1F4q3TVyqkYpCskN88igVFhj4gZ4A/oOcZItEOka8+aAWmY=
=GYm4
-----END PGP SIGNATURE-----

```
