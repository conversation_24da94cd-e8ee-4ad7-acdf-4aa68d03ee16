---
date: '2021-08-11T16:41:05.351Z'
category: release
title: Node v12.22.5 (LTS)
layout: blog-post
author: <PERSON>
---

### Notable Changes

- **CVE-2021-3672/CVE-2021-22931**: Improper handling of untypical characters in domain names (High)
  - Node.js was vulnerable to Remote Code Execution, XSS, application crashes due to missing input validation of hostnames returned by Domain Name Servers in the Node.js DNS library which can lead to the output of wrong hostnames (leading to Domain Hijacking) and injection vulnerabilities in applications using the library. You can read more about it at https://nvd.nist.gov/vuln/detail/CVE-2021-22931.
- **CVE-2021-22940**: Use after free on close http2 on stream canceling (High)
  - Node.js was vulnerable to a use after free attack where an attacker might be able to exploit memory corruption to change process behavior. This release includes a follow-up fix for CVE-2021-22930 as the issue was not completely resolved by the previous fix. You can read more about it at https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2021-22940.
- **CVE-2021-22939**: Incomplete validation of rejectUnauthorized parameter (Low)
  - If the Node.js HTTPS API was used incorrectly and "undefined" was in passed for the "rejectUnauthorized" parameter, no error was returned and connections to servers with an expired certificate would have been accepted. You can read more about it at https://nvd.nist.gov/vuln/detail/CVE-2021-22939.

### Commits

- [[`5f947db68c`](https://github.com/nodejs/node/commit/5f947db68c)] - **deps**: update c-ares to 1.17.2 (Beth Griggs) [#39724](https://github.com/nodejs/node/pull/39724)
- [[`42695ea34b`](https://github.com/nodejs/node/commit/42695ea34b)] - **deps**: reflect c-ares source tree (Beth Griggs) [#39653](https://github.com/nodejs/node/pull/39653)
- [[`e4c9156b32`](https://github.com/nodejs/node/commit/e4c9156b32)] - **deps**: apply missed updates from c-ares 1.17.1 (Beth Griggs) [#39653](https://github.com/nodejs/node/pull/39653)
- [[`9cd1f53103`](https://github.com/nodejs/node/commit/9cd1f53103)] - **http2**: add tests for cancel event while client is paused reading (Akshay K) [#39622](https://github.com/nodejs/node/pull/39622)
- [[`2008c9722f`](https://github.com/nodejs/node/commit/2008c9722f)] - **http2**: update handling of rst_stream with error code NGHTTP2_CANCEL (Akshay K) [#39622](https://github.com/nodejs/node/pull/39622)
- [[`1780bbc329`](https://github.com/nodejs/node/commit/1780bbc329)] - **tls**: validate "rejectUnauthorized: undefined" (Matteo Collina) [nodejs-private/node-private#276](https://github.com/nodejs-private/node-private/pull/276)

Windows 32-bit Installer: https://nodejs.org/dist/v12.22.5/node-v12.22.5-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v12.22.5/node-v12.22.5-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v12.22.5/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v12.22.5/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v12.22.5/node-v12.22.5.pkg \
macOS Intel 64-bit Binary: https://nodejs.org/dist/v12.22.5/node-v12.22.5-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v12.22.5/node-v12.22.5-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v12.22.5/node-v12.22.5-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v12.22.5/node-v12.22.5-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v12.22.5/node-v12.22.5-aix-ppc64.tar.gz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v12.22.5/node-v12.22.5-sunos-x64.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v12.22.5/node-v12.22.5-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v12.22.5/node-v12.22.5-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v12.22.5/node-v12.22.5.tar.gz \
Other release files: https://nodejs.org/dist/v12.22.5/ \
Documentation: https://nodejs.org/docs/v12.22.5/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

88ec315734db12686d1ee8cc24c7590f125231b64159b23e8aae3c42083d5480  node-v12.22.5-aix-ppc64.tar.gz
7944aa8bcc25842cac70d7e5454fce3eed1a01867968a3734765a3d6d15a5050  node-v12.22.5-darwin-x64.tar.gz
ee3e7f5d5336de2078cc860356a028238b23dabfb61a9ac3c3345390b7aa9a64  node-v12.22.5-darwin-x64.tar.xz
fc96140443452d6c297857d7065b5c37652a78cc7f1926345fa7684f7723fcc2  node-v12.22.5-headers.tar.gz
6a34def054215d9b9c07e1dcf6c33f0972f65881bbfd89557d65c8e41ac95d8a  node-v12.22.5-headers.tar.xz
bfb436a87142e9dc73ed675c81c267490e575f9abfbbc7fa5db227a2ab6b555c  node-v12.22.5-linux-arm64.tar.gz
b5f7932ab8bd55aeab087ddc28e03b035350a7f5f929b0cec373351c168f48b0  node-v12.22.5-linux-arm64.tar.xz
0b271c210e26ffb20728dfffa02df47aca896c849968964e2019da67832ee839  node-v12.22.5-linux-armv7l.tar.gz
bdf378d7d35eed3d810145d9b4eaae96224830fbf927a84568ff1c1411d54bcb  node-v12.22.5-linux-armv7l.tar.xz
80e042119d98ff4af48e53e6720b696f8c4a2746efbc5d8f5a3857a6b17c1415  node-v12.22.5-linux-ppc64le.tar.gz
7249a1d39642486c27cd00161486673bb1ae9d2f3e5e284097fbab12fc33c0fd  node-v12.22.5-linux-ppc64le.tar.xz
d93d1d89a6181bb3c2e04c9bdce2998f19788d576081bb34a5443074d5cad623  node-v12.22.5-linux-s390x.tar.gz
afe6860ea6efa77146e983adcf22aaa5fa38101806fab5c04a8f6354f4421412  node-v12.22.5-linux-s390x.tar.xz
89eaf038c41439dcbc543d1783adc0e9f38ddf07c993c08e111d55fe35dadc21  node-v12.22.5-linux-x64.tar.gz
dde9aeb3fd6994bbadc37f80ec607b24e4681c87d41a27ac838dee7e2ebb887a  node-v12.22.5-linux-x64.tar.xz
028a8fadda1a03c5b799d22ec5ca9bb2e2b9cd5cda061e61b247c5edebbd6b6e  node-v12.22.5.pkg
72d4f8b840d7c85f15cbd2239c05890c04ddb8f82509dbdeafb0ea5a16ac7159  node-v12.22.5-sunos-x64.tar.gz
6be81560387291ac8905842ed1637162f6a81174492d418bf4607ad4788504a6  node-v12.22.5-sunos-x64.tar.xz
119cf027c9ba0a71268914d02d2aa430f7eeb6adef5a9fa0bc9ed6dd1b12cd8c  node-v12.22.5.tar.gz
f927ff6c2ac5a7234596031b18ba03febbcadd2650d375f1a3fd02426687fd14  node-v12.22.5.tar.xz
b61a40a1f51cfc4084c8bf35579bd335c48ebe7e833ab24ff1abb7cf3e27d28d  node-v12.22.5-win-x64.7z
dd65b399c28699ba9dfcc3fb158d1c1d361605c92e80aec5ca663ee1d6fb162f  node-v12.22.5-win-x64.zip
41f4c0f2dfa71c864dda560208d5fc4cb079e97ef822bc6a7289bc025bb7cf3b  node-v12.22.5-win-x86.7z
b5256aa515a58600ce2e8337de0a87fdca8e51f32c15caf5aae09091231945a0  node-v12.22.5-win-x86.zip
6f33477be7491f4911e24400db778f905d2472e5e4ac42b21da5e5965f328c96  node-v12.22.5-x64.msi
4389040aea7ee80135c5aa926277b7c2673c66093d2bad7c27f96fd0603376db  node-v12.22.5-x86.msi
c16d71b5a3dfc24f39fe64a3314e95cb6457359024e7834043f555b81664afc7  win-x64/node.exe
28e5c24831deedbf4fb8a9560f2c4f95205479c589f54a9a53ec346f6a5cf8bf  win-x64/node.lib
22a430cc5e22c9179120a7354c07a1559ded2b5f5c1067c63d8e9d28a10733ff  win-x64/node_pdb.7z
301596333a0f67f5fafe553f3d3ba27113690e39bc3698a21710dc93fd38d7bc  win-x64/node_pdb.zip
94702eaed0fed1b8957a960200de06076348a75852d03b1fd54dcf6e9646e7a6  win-x86/node.exe
dad0e6bef1c45f4f43fbf84c33df6b910ace8122eff3f8d39d5ebecd25320ba4  win-x86/node.lib
8e91ab02a99235525a7db217939969cc26cf4ce9d773f6629c61f3e2baa1b814  win-x86/node_pdb.7z
5cf239a1b0ba869ab4a566974d8185918b42cf566b338d4538ff322c6addb5fe  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQEzBAEBCAAdFiEETtd49TnjY0x3nIfG1wYoSKGrAFwFAmET+zcACgkQ1wYoSKGr
AFxbFQf/RHldR7NJv/iT+NOVyt4boV6o+qh06aRyITJ17DMB/93LN+6AlIPCQPsP
kbDPid5/WKDvANrFAuTeGGaZjRSKKO3o7FoO8HeZHUQK4ll+L7xCbMtpYD93mMJ2
svjyN57TwF3MjLffUw/Tf70mL7iTehvKrNvcNFqkGJHvKPSkxx30nkWgoJynvVjw
MCzROYgMrKiFPbPHtWmqJ7y43appY2voX/DBPCJ2n9JcD7e/ycL6aEzMN3Dgwhm0
6ggjN1xOT7VPeNkom3My/KWWQVWv1Sw0Hl/wVuSNoxCeIV/38FErDLdCqjEdguLt
isb4vq07Y7R1JGo06Io8m2+iG+c5FQ==
=5G9A
-----END PGP SIGNATURE-----

```
