---
date: '2024-05-15T19:25:43.852Z'
category: release
title: Node v22.2.0 (Current)
layout: blog-post
author: <PERSON><PERSON><PERSON>
---

## 2024-05-15, Version 22.2.0 (Current), @targos

### Notable Changes

- \[[`fb85d38e80`](https://github.com/nodejs/node/commit/fb85d38e80)] - **(SEMVER-MINOR)** **cli**: allow running wasm in limited vmem with --disable-wasm-trap-handler (<PERSON><PERSON>) [#52766](https://github.com/nodejs/node/pull/52766)
- \[[`23a0d3339f`](https://github.com/nodejs/node/commit/23a0d3339f)] - **doc**: add pimterry to collaborators (<PERSON>) [#52874](https://github.com/nodejs/node/pull/52874)
- \[[`7d7a762156`](https://github.com/nodejs/node/commit/7d7a762156)] - **(SEMVER-MINOR)** **fs**: allow 'withFileTypes' to be used with globs (<PERSON>) [#52837](https://github.com/nodejs/node/pull/52837)
- \[[`8748dd6477`](https://github.com/nodejs/node/commit/8748dd6477)] - **(SEMVER-MINOR)** **inspector**: introduce the `--inspect-wait` flag (Kohei Ueno) [#52734](https://github.com/nodejs/node/pull/52734)
- \[[`9a7ae9b6c4`](https://github.com/nodejs/node/commit/9a7ae9b6c4)] - **lib,src**: remove --experimental-policy (Rafael Gonzaga) [#52583](https://github.com/nodejs/node/pull/52583)
- \[[`1f7c2a93fc`](https://github.com/nodejs/node/commit/1f7c2a93fc)] - **(SEMVER-MINOR)** **perf_hooks**: add `deliveryType` and `responseStatus` fields (Matthew Aitken) [#51589](https://github.com/nodejs/node/pull/51589)
- \[[`2f59529dc5`](https://github.com/nodejs/node/commit/2f59529dc5)] - **(SEMVER-MINOR)** **test_runner**: support test plans (Colin Ihrig) [#52860](https://github.com/nodejs/node/pull/52860)
- \[[`6b4dac3eb5`](https://github.com/nodejs/node/commit/6b4dac3eb5)] - **(SEMVER-MINOR)** **zlib**: expose zlib.crc32() (Joyee Cheung) [#52692](https://github.com/nodejs/node/pull/52692)

### Commits

- \[[`0f5716c364`](https://github.com/nodejs/node/commit/0f5716c364)] - **assert**: add deep equal check for more Error type (Zhenwei Jin) [#51805](https://github.com/nodejs/node/pull/51805)
- \[[`2c7d7caa8a`](https://github.com/nodejs/node/commit/2c7d7caa8a)] - **benchmark**: filter non-present deps from `start-cli-version` (Adam Majer) [#51746](https://github.com/nodejs/node/pull/51746)
- \[[`5db4c54bd6`](https://github.com/nodejs/node/commit/5db4c54bd6)] - **bootstrap**: print `--help` message using `console.log` (Jacob Hummer) [#51463](https://github.com/nodejs/node/pull/51463)
- \[[`67fcb6b85e`](https://github.com/nodejs/node/commit/67fcb6b85e)] - **buffer**: even faster atob (Daniel Lemire) [#52443](https://github.com/nodejs/node/pull/52443)
- \[[`a5d63f9052`](https://github.com/nodejs/node/commit/a5d63f9052)] - **buffer**: use size_t instead of uint32_t to avoid segmentation fault (Xavier Stouder) [#48033](https://github.com/nodejs/node/pull/48033)
- \[[`f1bc994826`](https://github.com/nodejs/node/commit/f1bc994826)] - **buffer**: remove lines setting indexes to integer value (Zhenwei Jin) [#52588](https://github.com/nodejs/node/pull/52588)
- \[[`a97ff753ab`](https://github.com/nodejs/node/commit/a97ff753ab)] - **build**: add option to enable clang-cl on Windows (Michaël Zasso) [#52870](https://github.com/nodejs/node/pull/52870)
- \[[`f96466a92c`](https://github.com/nodejs/node/commit/f96466a92c)] - **build**: enable building with shared uvwasi lib (Pooja D P) [#43987](https://github.com/nodejs/node/pull/43987)
- \[[`b463385aa8`](https://github.com/nodejs/node/commit/b463385aa8)] - **build**: remove deprecated calls for argument groups (Mohammed Keyvanzadeh) [#52913](https://github.com/nodejs/node/pull/52913)
- \[[`daeb7dbb3e`](https://github.com/nodejs/node/commit/daeb7dbb3e)] - **build**: sync V8 warning cflags with BUILD.gn (Michaël Zasso) [#52873](https://github.com/nodejs/node/pull/52873)
- \[[`eed967430d`](https://github.com/nodejs/node/commit/eed967430d)] - **build**: harmonize Clang checks (Michaël Zasso) [#52873](https://github.com/nodejs/node/pull/52873)
- \[[`e4b187433d`](https://github.com/nodejs/node/commit/e4b187433d)] - **build**: compile with C++20 support (Michaël Zasso) [#52838](https://github.com/nodejs/node/pull/52838)
- \[[`aea6ca25ba`](https://github.com/nodejs/node/commit/aea6ca25ba)] - **build**: drop base64 dep in GN build (Cheng) [#52856](https://github.com/nodejs/node/pull/52856)
- \[[`7f866a8225`](https://github.com/nodejs/node/commit/7f866a8225)] - **build**: make simdjson a public dep in GN build (Cheng) [#52755](https://github.com/nodejs/node/pull/52755)
- \[[`e1bd53c098`](https://github.com/nodejs/node/commit/e1bd53c098)] - **build**: define `NOMINMAX` in common.gypi (Chengzhong Wu) [#52794](https://github.com/nodejs/node/pull/52794)
- \[[`18c530f8f7`](https://github.com/nodejs/node/commit/18c530f8f7)] - **build, tools**: copy release assets to staging R2 bucket once built (flakey5) [#51394](https://github.com/nodejs/node/pull/51394)
- \[[`fb85d38e80`](https://github.com/nodejs/node/commit/fb85d38e80)] - **(SEMVER-MINOR)** **cli**: allow running wasm in limited vmem with --disable-wasm-trap-handler (Joyee Cheung) [#52766](https://github.com/nodejs/node/pull/52766)
- \[[`11e978916f`](https://github.com/nodejs/node/commit/11e978916f)] - **cluster**: replace `forEach` with `for-of` loop (Jérôme Benoit) [#50317](https://github.com/nodejs/node/pull/50317)
- \[[`db76c58d68`](https://github.com/nodejs/node/commit/db76c58d68)] - **console**: colorize console error and warn (Jithil P Ponnan) [#51629](https://github.com/nodejs/node/pull/51629)
- \[[`0d040a3035`](https://github.com/nodejs/node/commit/0d040a3035)] - **crypto**: fix duplicated switch-case return values (Mustafa Ateş UZUN) [#49030](https://github.com/nodejs/node/pull/49030)
- \[[`ab7219f0b2`](https://github.com/nodejs/node/commit/ab7219f0b2)] - **deps**: update googletest to fa6de7f (Node.js GitHub Bot) [#52949](https://github.com/nodejs/node/pull/52949)
- \[[`4ab096eccc`](https://github.com/nodejs/node/commit/4ab096eccc)] - **deps**: update simdjson to 3.9.2 (Node.js GitHub Bot) [#52947](https://github.com/nodejs/node/pull/52947)
- \[[`89f275b1df`](https://github.com/nodejs/node/commit/89f275b1df)] - **deps**: update corepack to 0.28.1 (Node.js GitHub Bot) [#52946](https://github.com/nodejs/node/pull/52946)
- \[[`fc568b4b42`](https://github.com/nodejs/node/commit/fc568b4b42)] - **deps**: update simdutf to 5.2.8 (Node.js GitHub Bot) [#52727](https://github.com/nodejs/node/pull/52727)
- \[[`e399360182`](https://github.com/nodejs/node/commit/e399360182)] - **deps**: update simdutf to 5.2.6 (Node.js GitHub Bot) [#52727](https://github.com/nodejs/node/pull/52727)
- \[[`232831f013`](https://github.com/nodejs/node/commit/232831f013)] - **deps**: enable unbundling of simdjson, simdutf, ada (Daniel Lemire) [#52924](https://github.com/nodejs/node/pull/52924)
- \[[`7ca83a5abc`](https://github.com/nodejs/node/commit/7ca83a5abc)] - **deps**: update googletest to 2d16ed0 (Node.js GitHub Bot) [#51657](https://github.com/nodejs/node/pull/51657)
- \[[`3b15eb5911`](https://github.com/nodejs/node/commit/3b15eb5911)] - **deps**: update googletest to d83fee1 (Node.js GitHub Bot) [#51657](https://github.com/nodejs/node/pull/51657)
- \[[`4190d70035`](https://github.com/nodejs/node/commit/4190d70035)] - **deps**: update googletest to 5a37b51 (Node.js GitHub Bot) [#51657](https://github.com/nodejs/node/pull/51657)
- \[[`7a166a2871`](https://github.com/nodejs/node/commit/7a166a2871)] - **deps**: update googletest to 5197b1a (Node.js GitHub Bot) [#51657](https://github.com/nodejs/node/pull/51657)
- \[[`812dbd749f`](https://github.com/nodejs/node/commit/812dbd749f)] - **deps**: update googletest to eff443c (Node.js GitHub Bot) [#51657](https://github.com/nodejs/node/pull/51657)
- \[[`cb3ae4b9ef`](https://github.com/nodejs/node/commit/cb3ae4b9ef)] - **deps**: update googletest to c231e6f (Node.js GitHub Bot) [#51657](https://github.com/nodejs/node/pull/51657)
- \[[`d97317aaa1`](https://github.com/nodejs/node/commit/d97317aaa1)] - **deps**: update googletest to e4fdb87 (Node.js GitHub Bot) [#51657](https://github.com/nodejs/node/pull/51657)
- \[[`ad8ca1259f`](https://github.com/nodejs/node/commit/ad8ca1259f)] - **deps**: update googletest to 5df0241 (Node.js GitHub Bot) [#51657](https://github.com/nodejs/node/pull/51657)
- \[[`828f0d7096`](https://github.com/nodejs/node/commit/828f0d7096)] - **deps**: update googletest to b75ecf1 (Node.js GitHub Bot) [#51657](https://github.com/nodejs/node/pull/51657)
- \[[`3b60dbcf7b`](https://github.com/nodejs/node/commit/3b60dbcf7b)] - **deps**: update googletest to 4565741 (Node.js GitHub Bot) [#51657](https://github.com/nodejs/node/pull/51657)
- \[[`37098eb880`](https://github.com/nodejs/node/commit/37098eb880)] - **deps**: update simdjson to 3.9.1 (Node.js GitHub Bot) [#52397](https://github.com/nodejs/node/pull/52397)
- \[[`a13cf1c049`](https://github.com/nodejs/node/commit/a13cf1c049)] - **deps**: update uvwasi to 0.0.21 (Node.js GitHub Bot) [#52863](https://github.com/nodejs/node/pull/52863)
- \[[`faf8ada719`](https://github.com/nodejs/node/commit/faf8ada719)] - **deps**: V8: cherry-pick f6bef09b3b0a (Richard Lau) [#52802](https://github.com/nodejs/node/pull/52802)
- \[[`8e5844c2a4`](https://github.com/nodejs/node/commit/8e5844c2a4)] - **doc**: remove reference to AUTHORS file (Marco Ippolito) [#52960](https://github.com/nodejs/node/pull/52960)
- \[[`1f3634e30f`](https://github.com/nodejs/node/commit/1f3634e30f)] - **doc**: update hljs with the latest styles (Aviv Keller) [#52911](https://github.com/nodejs/node/pull/52911)
- \[[`9102255749`](https://github.com/nodejs/node/commit/9102255749)] - **doc**: mention quicker way to build docs (Alex Crawford) [#52937](https://github.com/nodejs/node/pull/52937)
- \[[`15db3ef5fb`](https://github.com/nodejs/node/commit/15db3ef5fb)] - **doc**: mention push.followTags config (Rafael Gonzaga) [#52906](https://github.com/nodejs/node/pull/52906)
- \[[`80fa675af2`](https://github.com/nodejs/node/commit/80fa675af2)] - **doc**: document pipeline with `end` option (Alois Klink) [#48970](https://github.com/nodejs/node/pull/48970)
- \[[`c0000f4118`](https://github.com/nodejs/node/commit/c0000f4118)] - **doc**: add example for `execFileSync` method and ref to stdio (Evan Shortiss) [#39412](https://github.com/nodejs/node/pull/39412)
- \[[`e0148e2653`](https://github.com/nodejs/node/commit/e0148e2653)] - **doc**: add examples and notes to http server.close et al (mary marchini) [#49091](https://github.com/nodejs/node/pull/49091)
- \[[`030f56ee6d`](https://github.com/nodejs/node/commit/030f56ee6d)] - **doc**: fix `dns.lookup` family `0` and `all` descriptions (Adam Jones) [#51653](https://github.com/nodejs/node/pull/51653)
- \[[`a6d624cd5a`](https://github.com/nodejs/node/commit/a6d624cd5a)] - **doc**: update `fs.realpath` documentation (sinkhaha) [#48170](https://github.com/nodejs/node/pull/48170)
- \[[`5dab187ca8`](https://github.com/nodejs/node/commit/5dab187ca8)] - **doc**: update fs read documentation for clarity (Mert Can Altin) [#52453](https://github.com/nodejs/node/pull/52453)
- \[[`5d3ee7205d`](https://github.com/nodejs/node/commit/5d3ee7205d)] - **doc**: watermark string behavior (Benjamin Gruenbaum) [#52842](https://github.com/nodejs/node/pull/52842)
- \[[`2dd8f092a8`](https://github.com/nodejs/node/commit/2dd8f092a8)] - **doc**: exclude commits with baking-for-lts (Marco Ippolito) [#52896](https://github.com/nodejs/node/pull/52896)
- \[[`0c2539b913`](https://github.com/nodejs/node/commit/0c2539b913)] - **doc**: add names next to release key bash commands (Aviv Keller) [#52878](https://github.com/nodejs/node/pull/52878)
- \[[`23a0d3339f`](https://github.com/nodejs/node/commit/23a0d3339f)] - **doc**: add pimterry to collaborators (Tim Perry) [#52874](https://github.com/nodejs/node/pull/52874)
- \[[`15aad62e0c`](https://github.com/nodejs/node/commit/15aad62e0c)] - **doc**: update BUILDING.md previous versions links (Michaël Zasso) [#52852](https://github.com/nodejs/node/pull/52852)
- \[[`f770a993d4`](https://github.com/nodejs/node/commit/f770a993d4)] - **doc**: add more definitions to GLOSSARY.md (Aviv Keller) [#52798](https://github.com/nodejs/node/pull/52798)
- \[[`f35b838a65`](https://github.com/nodejs/node/commit/f35b838a65)] - **doc**: make docs more welcoming and descriptive for newcomers (Serkan Özel) [#38056](https://github.com/nodejs/node/pull/38056)
- \[[`562a019a14`](https://github.com/nodejs/node/commit/562a019a14)] - **doc**: add OpenSSL errors to API docs (John Lamp) [#34213](https://github.com/nodejs/node/pull/34213)
- \[[`0cb7cf7aa9`](https://github.com/nodejs/node/commit/0cb7cf7aa9)] - **doc**: fix grammatical mistake (codershiba) [#52808](https://github.com/nodejs/node/pull/52808)
- \[[`a0147ff8d0`](https://github.com/nodejs/node/commit/a0147ff8d0)] - **doc**: simplify copy-pasting of `branch-diff` commands (Antoine du Hamel) [#52757](https://github.com/nodejs/node/pull/52757)
- \[[`fce31fc829`](https://github.com/nodejs/node/commit/fce31fc829)] - **doc**: add test_runner to subsystem (Raz Luvaton) [#52774](https://github.com/nodejs/node/pull/52774)
- \[[`ca5607bbc8`](https://github.com/nodejs/node/commit/ca5607bbc8)] - **events**: update MaxListenersExceededWarning message log (sinkhaha) [#51921](https://github.com/nodejs/node/pull/51921)
- \[[`96566fc696`](https://github.com/nodejs/node/commit/96566fc696)] - **events**: add stop propagation flag to `Event.stopImmediatePropagation` (Mickael Meausoone) [#39463](https://github.com/nodejs/node/pull/39463)
- \[[`5ee69243ed`](https://github.com/nodejs/node/commit/5ee69243ed)] - **events**: replace NodeCustomEvent with CustomEvent (Feng Yu) [#43876](https://github.com/nodejs/node/pull/43876)
- \[[`f076e721cb`](https://github.com/nodejs/node/commit/f076e721cb)] - **fs**: keep fs.promises.readFile read until EOF is reached (Zhenwei Jin) [#52178](https://github.com/nodejs/node/pull/52178)
- \[[`7d7a762156`](https://github.com/nodejs/node/commit/7d7a762156)] - **(SEMVER-MINOR)** **fs**: allow 'withFileTypes' to be used with globs (Aviv Keller) [#52837](https://github.com/nodejs/node/pull/52837)
- \[[`ad9c4bddb1`](https://github.com/nodejs/node/commit/ad9c4bddb1)] - **http**: correctly translate HTTP method (Paolo Insogna) [#52701](https://github.com/nodejs/node/pull/52701)
- \[[`8748dd6477`](https://github.com/nodejs/node/commit/8748dd6477)] - **(SEMVER-MINOR)** **inspector**: introduce the `--inspect-wait` flag (Kohei Ueno) [#52734](https://github.com/nodejs/node/pull/52734)
- \[[`9a7ae9b6c4`](https://github.com/nodejs/node/commit/9a7ae9b6c4)] - **lib,src**: remove --experimental-policy (Rafael Gonzaga) [#52583](https://github.com/nodejs/node/pull/52583)
- \[[`a850219600`](https://github.com/nodejs/node/commit/a850219600)] - **meta**: move `@anonrig` to TSC regular member (Yagiz Nizipli) [#52932](https://github.com/nodejs/node/pull/52932)
- \[[`4dc8a387b3`](https://github.com/nodejs/node/commit/4dc8a387b3)] - **meta**: add mailmap entry for legendecas (Chengzhong Wu) [#52795](https://github.com/nodejs/node/pull/52795)
- \[[`d10182d81d`](https://github.com/nodejs/node/commit/d10182d81d)] - **meta**: bump actions/checkout from 4.1.1 to 4.1.4 (dependabot\[bot]) [#52787](https://github.com/nodejs/node/pull/52787)
- \[[`48d0ac0665`](https://github.com/nodejs/node/commit/48d0ac0665)] - **meta**: bump github/codeql-action from 3.24.9 to 3.25.3 (dependabot\[bot]) [#52786](https://github.com/nodejs/node/pull/52786)
- \[[`7c7a25150e`](https://github.com/nodejs/node/commit/7c7a25150e)] - **meta**: bump actions/upload-artifact from 4.3.1 to 4.3.3 (dependabot\[bot]) [#52785](https://github.com/nodejs/node/pull/52785)
- \[[`d9abf18342`](https://github.com/nodejs/node/commit/d9abf18342)] - **meta**: bump actions/download-artifact from 4.1.4 to 4.1.7 (dependabot\[bot]) [#52784](https://github.com/nodejs/node/pull/52784)
- \[[`590e5c6c45`](https://github.com/nodejs/node/commit/590e5c6c45)] - **meta**: bump codecov/codecov-action from 4.1.1 to 4.3.1 (dependabot\[bot]) [#52783](https://github.com/nodejs/node/pull/52783)
- \[[`b3d1720515`](https://github.com/nodejs/node/commit/b3d1720515)] - **meta**: bump step-security/harden-runner from 2.7.0 to 2.7.1 (dependabot\[bot]) [#52782](https://github.com/nodejs/node/pull/52782)
- \[[`f74beb53de`](https://github.com/nodejs/node/commit/f74beb53de)] - **module**: cache synchronous module jobs before linking (Joyee Cheung) [#52868](https://github.com/nodejs/node/pull/52868)
- \[[`8fbf6628d6`](https://github.com/nodejs/node/commit/8fbf6628d6)] - **module**: have a single hooks thread for all workers (Gabriel Bota) [#52706](https://github.com/nodejs/node/pull/52706)
- \[[`609d90bb4b`](https://github.com/nodejs/node/commit/609d90bb4b)] - **path**: fix toNamespacedPath on Windows (Hüseyin Açacak) [#52915](https://github.com/nodejs/node/pull/52915)
- \[[`1f7c2a93fc`](https://github.com/nodejs/node/commit/1f7c2a93fc)] - **(SEMVER-MINOR)** **perf_hooks**: add `deliveryType` and `responseStatus` fields (Matthew Aitken) [#51589](https://github.com/nodejs/node/pull/51589)
- \[[`0bbc62c42a`](https://github.com/nodejs/node/commit/0bbc62c42a)] - **process**: improve event-loop (Aras Abbasi) [#52108](https://github.com/nodejs/node/pull/52108)
- \[[`619ac79abb`](https://github.com/nodejs/node/commit/619ac79abb)] - **quic**: address coverity warning (Michael Dawson) [#52824](https://github.com/nodejs/node/pull/52824)
- \[[`04de5766ee`](https://github.com/nodejs/node/commit/04de5766ee)] - **repl**: fix disruptive autocomplete without inspector (Nitzan Uziely) [#40661](https://github.com/nodejs/node/pull/40661)
- \[[`663bb973ab`](https://github.com/nodejs/node/commit/663bb973ab)] - **src**: fix Worker termination in `inspector.waitForDebugger` (Daeyeon Jeong) [#52527](https://github.com/nodejs/node/pull/52527)
- \[[`fca38b2d6e`](https://github.com/nodejs/node/commit/fca38b2d6e)] - **src**: use `S_ISDIR` to check if the file is a directory (theanarkh) [#52164](https://github.com/nodejs/node/pull/52164)
- \[[`b228db579f`](https://github.com/nodejs/node/commit/b228db579f)] - **src**: allow preventing debug signal handler start (Shelley Vohr) [#46681](https://github.com/nodejs/node/pull/46681)
- \[[`ace65a9aac`](https://github.com/nodejs/node/commit/ace65a9aac)] - **src**: make sure pass the `argv` to worker threads (theanarkh) [#52827](https://github.com/nodejs/node/pull/52827)
- \[[`75004d32ab`](https://github.com/nodejs/node/commit/75004d32ab)] - **src**: fix typo Unabled -> Unable (Simon Siefke) [#52820](https://github.com/nodejs/node/pull/52820)
- \[[`c40a8273ef`](https://github.com/nodejs/node/commit/c40a8273ef)] - **src**: avoid unused variable 'error' warning (Michaël Zasso) [#52886](https://github.com/nodejs/node/pull/52886)
- \[[`d169d0f181`](https://github.com/nodejs/node/commit/d169d0f181)] - **src**: fix positional args in task runner (Yagiz Nizipli) [#52810](https://github.com/nodejs/node/pull/52810)
- \[[`9c76c95c10`](https://github.com/nodejs/node/commit/9c76c95c10)] - **src**: only apply fix in main thread (Paolo Insogna) [#52702](https://github.com/nodejs/node/pull/52702)
- \[[`e1cba97df3`](https://github.com/nodejs/node/commit/e1cba97df3)] - **src**: fix test local edge case (Paolo Insogna) [#52702](https://github.com/nodejs/node/pull/52702)
- \[[`dc41c135d7`](https://github.com/nodejs/node/commit/dc41c135d7)] - **src**: reduce unnecessary serialization of CLI options in C++ (Joyee Cheung) [#52451](https://github.com/nodejs/node/pull/52451)
- \[[`fb24c4475c`](https://github.com/nodejs/node/commit/fb24c4475c)] - **src**: rewrite task runner in c++ (Yagiz Nizipli) [#52609](https://github.com/nodejs/node/pull/52609)
- \[[`323f95de9e`](https://github.com/nodejs/node/commit/323f95de9e)] - **src**: migrate to new V8 interceptors API (Michaël Zasso) [#52745](https://github.com/nodejs/node/pull/52745)
- \[[`850ff02931`](https://github.com/nodejs/node/commit/850ff02931)] - **src,permission**: resolve path on fs_permission (Rafael Gonzaga) [#52761](https://github.com/nodejs/node/pull/52761)
- \[[`8d3b0b7ade`](https://github.com/nodejs/node/commit/8d3b0b7ade)] - **stream**: use `ByteLengthQueuingStrategy` when not in `objectMode` (Jason) [#48847](https://github.com/nodejs/node/pull/48847)
- \[[`fa715437b0`](https://github.com/nodejs/node/commit/fa715437b0)] - **stream**: fix util.inspect for compression/decompressionStream (Mert Can Altin) [#52283](https://github.com/nodejs/node/pull/52283)
- \[[`b0e6a6b3d5`](https://github.com/nodejs/node/commit/b0e6a6b3d5)] - **string_decoder**: throw an error when writing a too long buffer (zhenweijin) [#52215](https://github.com/nodejs/node/pull/52215)
- \[[`e016e952e6`](https://github.com/nodejs/node/commit/e016e952e6)] - **test**: add `Debugger.setInstrumentationBreakpoint` known issue (Konstantin Ulitin) [#31137](https://github.com/nodejs/node/pull/31137)
- \[[`a589de0886`](https://github.com/nodejs/node/commit/a589de0886)] - **test**: use `for-of` instead of `forEach` (Gibby Free) [#49790](https://github.com/nodejs/node/pull/49790)
- \[[`578868ddf8`](https://github.com/nodejs/node/commit/578868ddf8)] - **test**: verify request payload is uploaded consistently (Austin Wright) [#34066](https://github.com/nodejs/node/pull/34066)
- \[[`c676e522e6`](https://github.com/nodejs/node/commit/c676e522e6)] - **test**: add fuzzer for native/js string conversion (Adam Korczynski) [#51120](https://github.com/nodejs/node/pull/51120)
- \[[`5f6415b41d`](https://github.com/nodejs/node/commit/5f6415b41d)] - **test**: add fuzzer for `ClientHelloParser` (AdamKorcz) [#51088](https://github.com/nodejs/node/pull/51088)
- \[[`4d50d51a5e`](https://github.com/nodejs/node/commit/4d50d51a5e)] - **test**: fix broken env fuzzer by initializing process (AdamKorcz) [#51080](https://github.com/nodejs/node/pull/51080)
- \[[`cd00cdcbc8`](https://github.com/nodejs/node/commit/cd00cdcbc8)] - **test**: replace `forEach()` in `test-stream-pipe-unpipe-stream` (Dario) [#50786](https://github.com/nodejs/node/pull/50786)
- \[[`5469adf458`](https://github.com/nodejs/node/commit/5469adf458)] - **test**: test pipeline `end` on transform streams (Alois Klink) [#48970](https://github.com/nodejs/node/pull/48970)
- \[[`ea6070b0e8`](https://github.com/nodejs/node/commit/ea6070b0e8)] - **test**: improve coverage of lib/readline.js (Rongjian Zhang) [#38646](https://github.com/nodejs/node/pull/38646)
- \[[`4f96b00307`](https://github.com/nodejs/node/commit/4f96b00307)] - **test**: updated for each to for of in test file (lyannel) [#50308](https://github.com/nodejs/node/pull/50308)
- \[[`5d91cf1976`](https://github.com/nodejs/node/commit/5d91cf1976)] - **test**: move `test-http-server-request-timeouts-mixed` to sequential (Madhuri) [#45722](https://github.com/nodejs/node/pull/45722)
- \[[`f47e8fccbb`](https://github.com/nodejs/node/commit/f47e8fccbb)] - **test**: fix DNS cancel tests (Szymon Marczak) [#44432](https://github.com/nodejs/node/pull/44432)
- \[[`0b073f885a`](https://github.com/nodejs/node/commit/0b073f885a)] - **test**: add http agent to `executionAsyncResource` (psj-tar-gz) [#34966](https://github.com/nodejs/node/pull/34966)
- \[[`fbce3178ba`](https://github.com/nodejs/node/commit/fbce3178ba)] - **test**: reduce memory usage of test-worker-stdio (Adam Majer) [#37769](https://github.com/nodejs/node/pull/37769)
- \[[`1f8eaec454`](https://github.com/nodejs/node/commit/1f8eaec454)] - **test**: add common.expectRequiredModule() (Joyee Cheung) [#52868](https://github.com/nodejs/node/pull/52868)
- \[[`5e731da572`](https://github.com/nodejs/node/commit/5e731da572)] - **test**: skip unstable shadow realm gc tests (Chengzhong Wu) [#52855](https://github.com/nodejs/node/pull/52855)
- \[[`30a35ae522`](https://github.com/nodejs/node/commit/30a35ae522)] - **test**: crypto-rsa-dsa testing for dynamic openssl (Michael Dawson) [#52781](https://github.com/nodejs/node/pull/52781)
- \[[`968fe6a8b1`](https://github.com/nodejs/node/commit/968fe6a8b1)] - **test**: skip some console tests on dumb terminal (Adam Majer) [#37770](https://github.com/nodejs/node/pull/37770)
- \[[`1448959e0d`](https://github.com/nodejs/node/commit/1448959e0d)] - **test**: skip v8-updates/test-linux-perf-logger (Michaël Zasso) [#52821](https://github.com/nodejs/node/pull/52821)
- \[[`30a4248b48`](https://github.com/nodejs/node/commit/30a4248b48)] - **test**: add env variable test for --run (Yagiz Nizipli) [#52811](https://github.com/nodejs/node/pull/52811)
- \[[`edb4ed3bc9`](https://github.com/nodejs/node/commit/edb4ed3bc9)] - **test**: drop test-crypto-timing-safe-equal-benchmarks (Rafael Gonzaga) [#52751](https://github.com/nodejs/node/pull/52751)
- \[[`944ae598b5`](https://github.com/nodejs/node/commit/944ae598b5)] - **test, crypto**: use correct object on assert (响马) [#51820](https://github.com/nodejs/node/pull/51820)
- \[[`a814e720fa`](https://github.com/nodejs/node/commit/a814e720fa)] - **test_runner**: fix watch mode race condition (Moshe Atlow) [#52954](https://github.com/nodejs/node/pull/52954)
- \[[`2f59529dc5`](https://github.com/nodejs/node/commit/2f59529dc5)] - **(SEMVER-MINOR)** **test_runner**: support test plans (Colin Ihrig) [#52860](https://github.com/nodejs/node/pull/52860)
- \[[`3267b3c063`](https://github.com/nodejs/node/commit/3267b3c063)] - **test_runner**: display failed test stack trace with dot reporter (Mihir Bhansali) [#52655](https://github.com/nodejs/node/pull/52655)
- \[[`b96868b4e7`](https://github.com/nodejs/node/commit/b96868b4e7)] - **test_runner**: preserve hook promise when executed twice (Moshe Atlow) [#52791](https://github.com/nodejs/node/pull/52791)
- \[[`74341ba3c9`](https://github.com/nodejs/node/commit/74341ba3c9)] - **tools**: fix v8-update workflow (Michaël Zasso) [#52957](https://github.com/nodejs/node/pull/52957)
- \[[`afe39ed0df`](https://github.com/nodejs/node/commit/afe39ed0df)] - **tools**: add --certify-safe to nci-ci (Matteo Collina) [#52940](https://github.com/nodejs/node/pull/52940)
- \[[`bb97e1ccdd`](https://github.com/nodejs/node/commit/bb97e1ccdd)] - **tools**: fix doc update action (Marco Ippolito) [#52890](https://github.com/nodejs/node/pull/52890)
- \[[`c6043fe6c8`](https://github.com/nodejs/node/commit/c6043fe6c8)] - **tools**: fix get_asan_state() in tools/test.py (Joyee Cheung) [#52766](https://github.com/nodejs/node/pull/52766)
- \[[`6e71accc5f`](https://github.com/nodejs/node/commit/6e71accc5f)] - **tools**: support max_virtual_memory test configuration (Joyee Cheung) [#52766](https://github.com/nodejs/node/pull/52766)
- \[[`1600bdac60`](https://github.com/nodejs/node/commit/1600bdac60)] - **tools**: support != in test status files (Joyee Cheung) [#52766](https://github.com/nodejs/node/pull/52766)
- \[[`8ce23dc9f3`](https://github.com/nodejs/node/commit/8ce23dc9f3)] - **tools**: update gyp-next to 0.18.0 (Node.js GitHub Bot) [#52835](https://github.com/nodejs/node/pull/52835)
- \[[`c5f832adc0`](https://github.com/nodejs/node/commit/c5f832adc0)] - **tools**: update gyp-next to 0.17.0 (Node.js GitHub Bot) [#52835](https://github.com/nodejs/node/pull/52835)
- \[[`646a094782`](https://github.com/nodejs/node/commit/646a094782)] - **tools**: prepare custom rules for ESLint v9 (Michaël Zasso) [#52889](https://github.com/nodejs/node/pull/52889)
- \[[`505566347d`](https://github.com/nodejs/node/commit/505566347d)] - **tools**: update lint-md-dependencies to rollup\@4.17.2 (Node.js GitHub Bot) [#52836](https://github.com/nodejs/node/pull/52836)
- \[[`466e0c1321`](https://github.com/nodejs/node/commit/466e0c1321)] - **tools**: update `gr2m/create-or-update-pull-request-action` (Antoine du Hamel) [#52843](https://github.com/nodejs/node/pull/52843)
- \[[`ce7a751ad1`](https://github.com/nodejs/node/commit/ce7a751ad1)] - **tools**: use sccache GitHub action (Michaël Zasso) [#52839](https://github.com/nodejs/node/pull/52839)
- \[[`1ee38a5ec1`](https://github.com/nodejs/node/commit/1ee38a5ec1)] - **tools**: specify a commit-message for V8 update workflow (Antoine du Hamel) [#52844](https://github.com/nodejs/node/pull/52844)
- \[[`317998a1e8`](https://github.com/nodejs/node/commit/317998a1e8)] - **tools**: fix V8 update workflow (Antoine du Hamel) [#52822](https://github.com/nodejs/node/pull/52822)
- \[[`ef6a2101e2`](https://github.com/nodejs/node/commit/ef6a2101e2)] - **url,tools,benchmark**: replace deprecated `substr()` (Jungku Lee) [#51546](https://github.com/nodejs/node/pull/51546)
- \[[`0deef2d2b1`](https://github.com/nodejs/node/commit/0deef2d2b1)] - **util**: fix `%s` format behavior with `Symbol.toPrimitive` (Chenyu Yang) [#50992](https://github.com/nodejs/node/pull/50992)
- \[[`a42b93b9aa`](https://github.com/nodejs/node/commit/a42b93b9aa)] - **util**: improve `isInsideNodeModules` (uzlopak) [#52147](https://github.com/nodejs/node/pull/52147)
- \[[`d71e16154a`](https://github.com/nodejs/node/commit/d71e16154a)] - **watch**: allow listening for grouped changes (Matthieu Sieben) [#52722](https://github.com/nodejs/node/pull/52722)
- \[[`e895f7cf32`](https://github.com/nodejs/node/commit/e895f7cf32)] - **watch**: enable passthrough ipc in watch mode (Zack) [#50890](https://github.com/nodejs/node/pull/50890)
- \[[`f5d925706a`](https://github.com/nodejs/node/commit/f5d925706a)] - **watch**: fix arguments parsing (Moshe Atlow) [#52760](https://github.com/nodejs/node/pull/52760)
- \[[`6b4dac3eb5`](https://github.com/nodejs/node/commit/6b4dac3eb5)] - **(SEMVER-MINOR)** **zlib**: expose zlib.crc32() (Joyee Cheung) [#52692](https://github.com/nodejs/node/pull/52692)

Windows 32-bit Installer: https://nodejs.org/dist/v22.2.0/node-v22.2.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v22.2.0/node-v22.2.0-x64.msi \
Windows ARM 64-bit Installer: https://nodejs.org/dist/v22.2.0/node-v22.2.0-arm64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v22.2.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v22.2.0/win-x64/node.exe \
Windows ARM 64-bit Binary: https://nodejs.org/dist/v22.2.0/win-arm64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v22.2.0/node-v22.2.0.pkg \
macOS Apple Silicon 64-bit Binary: https://nodejs.org/dist/v22.2.0/node-v22.2.0-darwin-arm64.tar.gz \
macOS Intel 64-bit Binary: https://nodejs.org/dist/v22.2.0/node-v22.2.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v22.2.0/node-v22.2.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v22.2.0/node-v22.2.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v22.2.0/node-v22.2.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v22.2.0/node-v22.2.0-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v22.2.0/node-v22.2.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v22.2.0/node-v22.2.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v22.2.0/node-v22.2.0.tar.gz \
Other release files: https://nodejs.org/dist/v22.2.0/ \
Documentation: https://nodejs.org/docs/v22.2.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

52d5cd604f2ac283c03f1ebb7746ee74e09e8f52d3e17d29fed1fd58da59d2a8  node-v22.2.0-aix-ppc64.tar.gz
c42ef85e163e696a377fa248c52aeea68a8e2ee14a92f900eb11a6a53b0128f2  node-v22.2.0-arm64.msi
66dd98bd28d19603f2e5ab0aa0e07b64f8cad28bbc446bb44fb61cc3da62e685  node-v22.2.0-darwin-arm64.tar.gz
c809cfaf7fc06bcf35ae469781c66dbc79be7fbea641937784fdefc297a2b335  node-v22.2.0-darwin-arm64.tar.xz
b3cd4ab4bb4ac7f9bd5c7603baf6bbdcf466c86bb6ca49abf5e221ab8fad7ceb  node-v22.2.0-darwin-x64.tar.gz
3a3058cb460899587bf5a3129309826886c704b8ad9ff69d9b55ad99b2c843e2  node-v22.2.0-darwin-x64.tar.xz
c8cd9be91fc4f24260068c44424bc11168fe5d525a29ee204475c8cd22ea435b  node-v22.2.0-headers.tar.gz
3dbef9a1558b0bbc1ac8d06ff8acdd1899d4509ee5a9e6af5a8e8b22e063d5bd  node-v22.2.0-headers.tar.xz
7fc74ddeb3e2317c905fdebd2b681d565fc2a2980515430d4f01dad4ce312175  node-v22.2.0-linux-arm64.tar.gz
e3d580cb7738dd9a0f8672f684de86b621d8755a6cf349df8c01b8dd875b59ab  node-v22.2.0-linux-arm64.tar.xz
eb14ff993a1a4b6c0417b5f086b1629c82a3665edfa1c83f3b5ab1250b721307  node-v22.2.0-linux-armv7l.tar.gz
a979f384fea317053ed435a245e3d3663280859b17487fc2b6515e14c6770711  node-v22.2.0-linux-armv7l.tar.xz
e70cb8c5dde31c5e0ee998124f74f7fb7dc7efd7e1549fa162e7fbc20bd937cc  node-v22.2.0-linux-ppc64le.tar.gz
235dc30116f378d1ec326b49ad0ea08c3d84cc057238749e7ada6bb4307b1186  node-v22.2.0-linux-ppc64le.tar.xz
48a1999e35c5f8b7cf2240239d588d52fad424635b4dd4933062eb9f2a3608f4  node-v22.2.0-linux-s390x.tar.gz
cb3cce70aeb29072aad450fd0b09130d34a36e38ad689f3bc4a6d72caade281f  node-v22.2.0-linux-s390x.tar.xz
2c6eaf8bfd0f886ed8764ffce19c795e909639105e4056b1d9f8f917bad12cf1  node-v22.2.0-linux-x64.tar.gz
3544eee9cb1414d6e9003efd56bc807ffb0f4445d2fc383e1df04c3e5e72c91b  node-v22.2.0-linux-x64.tar.xz
e8cdad84c555a6281487890d56676f385e35dc13b35089963e4d624a6c9f58f4  node-v22.2.0.pkg
2210ce0a40aa6aec3cc118228fdad6536607002319b1fde24260d179118c1055  node-v22.2.0.tar.gz
889908a8828d1484910d7e659b6aa57ade8d528ff0e390e9a77ef659a7628474  node-v22.2.0.tar.xz
10e5a2bcf72e568933643a652bd7728231f8d5dfc59d623f5fcca550b3f1d799  node-v22.2.0-win-arm64.7z
4b7c296503da5737448b9f5f620e0f961f23f53eafc0e04688a96944afb08fd7  node-v22.2.0-win-arm64.zip
740a4d3c613a77268ac856c2c209603236c51745506be1d3237b47491c27e7c7  node-v22.2.0-win-x64.7z
f83e956bd90c7f5066a7e96e9372839fcc263795525fa0c03cfdf4b43be9457f  node-v22.2.0-win-x64.zip
6c9bd5337b87ce6c31eae8145ccb9ce70d78ab5527125f0ab2fbbc170362953a  node-v22.2.0-win-x86.7z
efec272a434043b439ac32f7b11fa674d595772a4636b50481fa2aedb68578ac  node-v22.2.0-win-x86.zip
a2fbc72550ade8f9e391ea054af8fd91a3c2f224ea6d7f8eccdef215a5c30fde  node-v22.2.0-x64.msi
c03be5bc511dc5825471ee14d80b74d4de06e2ab3676a56fbe5fed10b6a48c92  node-v22.2.0-x86.msi
767231a4a8667fa41559a04857f5824a01ec46075289d4e1f868cc86dc8186fd  win-arm64/node.exe
30e63a6726cda6539eeb37c311adf915bccd5c1462723b97a6c07ac91e8ae728  win-arm64/node.lib
aa7fe0d9948ff3aab51c960e8d85dd7e28c66fd8f1a7c0e173c34a4862504d32  win-arm64/node_pdb.7z
376ee1f8d4b1870838466c9b33c60c6100ace5693ba1c955c6a4cc926a0dc56e  win-arm64/node_pdb.zip
9f281fc885999757b43ad229aec5b53206d1088abacb49cc5b3e830d39521a2f  win-x64/node.exe
c4d08d45267da3625a30730bf5c8e41518f25d9809179feb267f1b393f5c5f05  win-x64/node.lib
fb44de8c670d8c53a08c94ddfa2484f384af5e5c4caa0b9114224d0bb678796f  win-x64/node_pdb.7z
cb68da2ff89514145941181d89508f87b2248da0b011ba4ef1e2df697ad47a9d  win-x64/node_pdb.zip
6c7509b76bec8e0421be417555fe49619c4360f9c7f56e5622910bae73c835db  win-x86/node.exe
fc3bf3c1e561da1e1c152be9aa5ed1bce8d263a5124841a4ba41ebc37c727f3e  win-x86/node.lib
e83aa6ddd82ffb8e9ea7d22bf2d09b03880dd959c89e5c0cc7beab8fbb9d6da7  win-x86/node_pdb.7z
582f22f08bb045e8b6b59e6e8e0f3394eb1305157d48370084ae80e80b237fce  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEj8yhP+8dDC6RAI4Jdw96mlrhVgAFAmZFC6UACgkQdw96mlrh
VgChoA//djlP5Qq5QeNxYwguqKf2s9tjiMUqntC/rZygrFP1zFXDK4ceIngOOm1F
CI5JRAtvOSuoakJiw/oq8RNQf5ydUx1m8nQICPAAywxNlYIvUV2SLySlN4PAey5o
rRqeCamc3Hucsr5zXCl1yGNmDPYgBCmKugKyIVhbCt0FpndM6tm+MNTrdrpO2JPw
+zJLxX5K6B1DFVPzxI85aAgUoetgwruWvMjQaSMkEJvFrweu5h1Lt2KH4Q1t4iwF
RSvOcIlTU1tzz0CYB0kg8SXHhFn36W1bYlx+tr1JMlDy3L+D0KLHRARbSTT2mI9o
U2ZySxbpy4eCypVW+QMAW8mLMJT20UBG/+SkBr13na1moLeNrQ2H3hj+ykxSSxJQ
gZJI+NY4MLuwx1K74ADERyGHvMhQuHOWTGxqo5wQewDTS2qUramJxLzfKXVkzgSZ
F8KgPYzCN+rZ8SN4Cv7BelXk3s4ui+ZRFS4AOv3T3aKM/56vnRvg+pqWym4kzwQ2
cNiW+yni4KYO3VdN8IWbQXv61HKRs0cfZe1YvTju89KQon4XBrUBrselDv10IFjD
eQkzP4Rj2Docula8vnPmqwYStpiEEBwyMSLdf5H7e1Vi4e7LCn2bf/v7W/33ZoB1
EI19IVphrFgI1KhauV+OIuE3/+DWDXQ6EUkBexdQz8CdVQvnkkg=
=Y3mv
-----END PGP SIGNATURE-----
```
