---
date: '2022-01-18T15:07:53.421Z'
category: release
title: Node v17.4.0 (Current)
layout: blog-post
author: <PERSON><PERSON><PERSON>
---

### Notable Changes

- \[[`ef6f98c2e3`](https://github.com/nodejs/node/commit/ef6f98c2e3)] - **(SEMVER-MINOR)** **child_process**: add support for URL to `cp.fork` (<PERSON>) [#41225](https://github.com/nodejs/node/pull/41225)
- \[[`d62fe315c2`](https://github.com/nodejs/node/commit/d62fe315c2)] - **(SEMVER-MINOR)** **crypto**: alias webcrypto.subtle and webcrypto.getRandomValues on crypto (<PERSON>) [#41266](https://github.com/nodejs/node/pull/41266)
- \[[`fcb37e9ce5`](https://github.com/nodejs/node/commit/fcb37e9ce5)] - **doc**: add Mesteery to collaborators (Mestery) [#41543](https://github.com/nodejs/node/pull/41543)
- \[[`4079fc42b7`](https://github.com/nodejs/node/commit/4079fc42b7)] - **(SEMVER-MINOR)** **events**: graduate capturerejections to supported (James M Snell) [#41267](https://github.com/nodejs/node/pull/41267)
- \[[`fe21607901`](https://github.com/nodejs/node/commit/fe21607901)] - **(SEMVER-MINOR)** **events**: add EventEmitterAsyncResource to core (James M Snell) [#41246](https://github.com/nodejs/node/pull/41246)
- \[[`6d8eb6ace6`](https://github.com/nodejs/node/commit/6d8eb6ace6)] - **(SEMVER-MINOR)** **loader**: return package format from defaultResolve if known (Gabriel Bota) [#40980](https://github.com/nodejs/node/pull/40980)
- \[[`27c619140a`](https://github.com/nodejs/node/commit/27c619140a)] - **(SEMVER-MINOR)** **perf_hooks**: multiple fixes for Histogram (James M Snell) [#41153](https://github.com/nodejs/node/pull/41153)
- \[[`09c25bb224`](https://github.com/nodejs/node/commit/09c25bb224)] - **(SEMVER-MINOR)** **stream**: add filter method to readable (Benjamin Gruenbaum, Robert Nagy) [#41354](https://github.com/nodejs/node/pull/41354)
- \[[`1150963217`](https://github.com/nodejs/node/commit/1150963217)] - **(SEMVER-MINOR)** **stream**: add isReadable helper (Robert Nagy) [#41199](https://github.com/nodejs/node/pull/41199)
- \[[`9f5a873965`](https://github.com/nodejs/node/commit/9f5a873965)] - **(SEMVER-MINOR)** **stream**: add map method to Readable (Benjamin Gruenbaum, Robert Nagy) [#40815](https://github.com/nodejs/node/pull/40815)

### Commits

- \[[`314102b14d`](https://github.com/nodejs/node/commit/314102b14d)] - **async_hooks**: add missing initialization (Michael Dawson) [#41288](https://github.com/nodejs/node/pull/41288)
- \[[`56345a3f63`](https://github.com/nodejs/node/commit/56345a3f63)] - **async_hooks**: fix AsyncLocalStorage in unhandledRejection cases (Bradley Farias) [#41202](https://github.com/nodejs/node/pull/41202)
- \[[`fa84353952`](https://github.com/nodejs/node/commit/fa84353952)] - **benchmark**: simplify http benchmarker regular expression (Rich Trott) [#38206](https://github.com/nodejs/node/pull/38206)
- \[[`88d760c559`](https://github.com/nodejs/node/commit/88d760c559)] - **benchmark**: fix benchmark/run.js handling of --set (Rich Trott) [#41334](https://github.com/nodejs/node/pull/41334)
- \[[`dcf1ea0a3f`](https://github.com/nodejs/node/commit/dcf1ea0a3f)] - **benchmark,test**: use Object.hasOwn() where applicable (Rich Trott) [#41229](https://github.com/nodejs/node/pull/41229)
- \[[`4958c800da`](https://github.com/nodejs/node/commit/4958c800da)] - **build**: fix npm version detection (Michaël Zasso) [#41575](https://github.com/nodejs/node/pull/41575)
- \[[`e8538c3751`](https://github.com/nodejs/node/commit/e8538c3751)] - **build**: fix node build failures in WSL Ubuntu (MrJithil) [#41476](https://github.com/nodejs/node/pull/41476)
- \[[`3d88ea195e`](https://github.com/nodejs/node/commit/3d88ea195e)] - **build**: fix workflow access to git history (Rich Trott) [#41472](https://github.com/nodejs/node/pull/41472)
- \[[`b0f0ad1004`](https://github.com/nodejs/node/commit/b0f0ad1004)] - **build**: start build on z/OS (alexcfyung) [#41273](https://github.com/nodejs/node/pull/41273)
- \[[`80a3766ac7`](https://github.com/nodejs/node/commit/80a3766ac7)] - **build**: use list for mutable retval rather than tuple (Rich Trott) [#41372](https://github.com/nodejs/node/pull/41372)
- \[[`afe1e00509`](https://github.com/nodejs/node/commit/afe1e00509)] - **build**: remove Python 2 workaround (Rich Trott) [#41357](https://github.com/nodejs/node/pull/41357)
- \[[`011c5f6498`](https://github.com/nodejs/node/commit/011c5f6498)] - **build**: improve readability of texts in workflows (Mestery) [#40988](https://github.com/nodejs/node/pull/40988)
- \[[`2233f31069`](https://github.com/nodejs/node/commit/2233f31069)] - **build,tools,win**: trim unused VCBUILD_PYTHON_LOCATION variable (David Sanders) [#41235](https://github.com/nodejs/node/pull/41235)
- \[[`d9465ae614`](https://github.com/nodejs/node/commit/d9465ae614)] - **child_process**: queue pending messages (Erick Wendel) [#41221](https://github.com/nodejs/node/pull/41221)
- \[[`ed41fd110d`](https://github.com/nodejs/node/commit/ed41fd110d)] - **child_process**: revise argument processing (Rich Trott) [#41280](https://github.com/nodejs/node/pull/41280)
- \[[`ef6f98c2e3`](https://github.com/nodejs/node/commit/ef6f98c2e3)] - **(SEMVER-MINOR)** **child_process**: add support for URL to `cp.fork` (Antoine du Hamel) [#41225](https://github.com/nodejs/node/pull/41225)
- \[[`d62fe315c2`](https://github.com/nodejs/node/commit/d62fe315c2)] - **(SEMVER-MINOR)** **crypto**: alias webcrypto.subtle and webcrypto.getRandomValues on crypto (James M Snell) [#41266](https://github.com/nodejs/node/pull/41266)
- \[[`8ea56a9606`](https://github.com/nodejs/node/commit/8ea56a9606)] - **crypto**: fix error capture when loading engine (Tobias Nießen) [#41187](https://github.com/nodejs/node/pull/41187)
- \[[`f5b8aee1a1`](https://github.com/nodejs/node/commit/f5b8aee1a1)] - **deps**: upgrade npm to 8.3.1 (npm-robot) [#41503](https://github.com/nodejs/node/pull/41503)
- \[[`af3acecd7e`](https://github.com/nodejs/node/commit/af3acecd7e)] - **deps**: V8: cherry-pick 3b6b21f595f6 (Michaël Zasso) [#41457](https://github.com/nodejs/node/pull/41457)
- \[[`02ca5d7c7c`](https://github.com/nodejs/node/commit/02ca5d7c7c)] - **deps**: upgrade to libuv 1.43.0 (Colin Ihrig) [#41398](https://github.com/nodejs/node/pull/41398)
- \[[`48e4780fd7`](https://github.com/nodejs/node/commit/48e4780fd7)] - **doc**: remove statement about client private keys (Tobias Nießen) [#41505](https://github.com/nodejs/node/pull/41505)
- \[[`ba7160e815`](https://github.com/nodejs/node/commit/ba7160e815)] - **doc**: fix typo in `onboarding.md` (Antoine du Hamel) [#41544](https://github.com/nodejs/node/pull/41544)
- \[[`fcb37e9ce5`](https://github.com/nodejs/node/commit/fcb37e9ce5)] - **doc**: add Mesteery to collaborators (Mestery) [#41543](https://github.com/nodejs/node/pull/41543)
- \[[`abbfed8789`](https://github.com/nodejs/node/commit/abbfed8789)] - **doc**: add missing word in readable.read() text (Rich Trott) [#41524](https://github.com/nodejs/node/pull/41524)
- \[[`712dfdc11f`](https://github.com/nodejs/node/commit/712dfdc11f)] - **doc**: add missing YAML tag in `esm.md` (Antoine du Hamel) [#41516](https://github.com/nodejs/node/pull/41516)
- \[[`f443a4e8fa`](https://github.com/nodejs/node/commit/f443a4e8fa)] - **doc**: expand fs.access() mode parameter docs (Colin Ihrig) [#41484](https://github.com/nodejs/node/pull/41484)
- \[[`5c0c459976`](https://github.com/nodejs/node/commit/5c0c459976)] - **doc**: correct checkHost behavior with wildcards etc (Tobias Nießen) [#41468](https://github.com/nodejs/node/pull/41468)
- \[[`c632241440`](https://github.com/nodejs/node/commit/c632241440)] - **doc**: remove extraneous colon in legacy subject (Tobias Nießen) [#41477](https://github.com/nodejs/node/pull/41477)
- \[[`b7b0631b10`](https://github.com/nodejs/node/commit/b7b0631b10)] - **doc**: remove SameValue comparison reference (Rich Trott) [#41460](https://github.com/nodejs/node/pull/41460)
- \[[`524103d6bf`](https://github.com/nodejs/node/commit/524103d6bf)] - **doc**: update mailmap entries for mhdawson (Michael Dawson) [#41437](https://github.com/nodejs/node/pull/41437)
- \[[`62aa190c01`](https://github.com/nodejs/node/commit/62aa190c01)] - **doc**: add guidance on order vulns are listed in (Michael Dawson) [#41429](https://github.com/nodejs/node/pull/41429)
- \[[`d721a758b2`](https://github.com/nodejs/node/commit/d721a758b2)] - **doc**: update output in inspector examples (David Sanders) [#41390](https://github.com/nodejs/node/pull/41390)
- \[[`60025bde16`](https://github.com/nodejs/node/commit/60025bde16)] - **doc**: add note regarding unfinished TLA (Antoine du Hamel) [#41434](https://github.com/nodejs/node/pull/41434)
- \[[`10bdb5969e`](https://github.com/nodejs/node/commit/10bdb5969e)] - **doc**: add reference for `===` operator in assert.md (Rich Trott) [#41442](https://github.com/nodejs/node/pull/41442)
- \[[`edc6a7af42`](https://github.com/nodejs/node/commit/edc6a7af42)] - **doc**: clarify `uncaughtException` `origin` for ESM (Antoine du Hamel) [#41339](https://github.com/nodejs/node/pull/41339)
- \[[`4a369d03b4`](https://github.com/nodejs/node/commit/4a369d03b4)] - **doc**: revise HTTPRequestOptions text (Rich Trott) [#41407](https://github.com/nodejs/node/pull/41407)
- \[[`f43bfe2e16`](https://github.com/nodejs/node/commit/f43bfe2e16)] - **doc**: add reference for == and != operators (Rich Trott) [#41413](https://github.com/nodejs/node/pull/41413)
- \[[`d3111bf0cc`](https://github.com/nodejs/node/commit/d3111bf0cc)] - **doc**: add @RaisinTen to the TSC (Michael Dawson) [#41419](https://github.com/nodejs/node/pull/41419)
- \[[`e6bed4e972`](https://github.com/nodejs/node/commit/e6bed4e972)] - **doc**: update Abstract Equality Comparison text in assert.md (Rich Trott) [#41375](https://github.com/nodejs/node/pull/41375)
- \[[`19db19bb80`](https://github.com/nodejs/node/commit/19db19bb80)] - **doc**: fix example commands for `REPLACEME` updates (Richard Lau) [#41269](https://github.com/nodejs/node/pull/41269)
- \[[`16c0bea91d`](https://github.com/nodejs/node/commit/16c0bea91d)] - **doc**: document that `require.main` may be `undefined` (Antoine du Hamel) [#41384](https://github.com/nodejs/node/pull/41384)
- \[[`014d4836ec`](https://github.com/nodejs/node/commit/014d4836ec)] - **doc**: clarify entry point behavior when using loader hooks (Antoine du Hamel) [#41304](https://github.com/nodejs/node/pull/41304)
- \[[`6460b1b32d`](https://github.com/nodejs/node/commit/6460b1b32d)] - **doc**: clarify `require` behavior with non `.js` extensions (Antoine du Hamel) [#41345](https://github.com/nodejs/node/pull/41345)
- \[[`0d18a8c232`](https://github.com/nodejs/node/commit/0d18a8c232)] - **doc**: revise frozen-intrinsics text (Rich Trott) [#41342](https://github.com/nodejs/node/pull/41342)
- \[[`c267bb2192`](https://github.com/nodejs/node/commit/c267bb2192)] - **doc**: fix example description for worker_threads (Dmitry Petrov) [#41341](https://github.com/nodejs/node/pull/41341)
- \[[`ffe17a84f2`](https://github.com/nodejs/node/commit/ffe17a84f2)] - **doc**: make pull-request guide default branch agnostic (Antoine du Hamel) [#41299](https://github.com/nodejs/node/pull/41299)
- \[[`5cfc547997`](https://github.com/nodejs/node/commit/5cfc547997)] - **doc**: fix sync comment in observer snippet (Eric Jacobson) [#41262](https://github.com/nodejs/node/pull/41262)
- \[[`3a80104b29`](https://github.com/nodejs/node/commit/3a80104b29)] - **doc**: remove section about amending commits in PR guide (Thiago Santos) [#41287](https://github.com/nodejs/node/pull/41287)
- \[[`23f97ec04e`](https://github.com/nodejs/node/commit/23f97ec04e)] - **doc**: remove legacy in-page links in v8.md (Rich Trott) [#41291](https://github.com/nodejs/node/pull/41291)
- \[[`e819685cec`](https://github.com/nodejs/node/commit/e819685cec)] - **doc**: include stack trace difference in ES modules (Marcos Bérgamo) [#41157](https://github.com/nodejs/node/pull/41157)
- \[[`dac8407944`](https://github.com/nodejs/node/commit/dac8407944)] - **doc**: fix example in node-api docs (Michael Dawson) [#41264](https://github.com/nodejs/node/pull/41264)
- \[[`29563abd85`](https://github.com/nodejs/node/commit/29563abd85)] - **doc**: add usage recommendation for writable.\_destroy (Rafael Gonzaga) [#41040](https://github.com/nodejs/node/pull/41040)
- \[[`e27e8272f7`](https://github.com/nodejs/node/commit/e27e8272f7)] - **doc**: make function signature comply with JSDoc comment (Rich Trott) [#41242](https://github.com/nodejs/node/pull/41242)
- \[[`d83a02994c`](https://github.com/nodejs/node/commit/d83a02994c)] - **doc**: align maxHeaderSize default with current value (Gil Pedersen) [#41183](https://github.com/nodejs/node/pull/41183)
- \[[`730e25d7dd`](https://github.com/nodejs/node/commit/730e25d7dd)] - **doc**: add unhandledRejection to strict mode (Colin Ihrig) [#41194](https://github.com/nodejs/node/pull/41194)
- \[[`74742c3618`](https://github.com/nodejs/node/commit/74742c3618)] - **doc**: adding estimated execution time (mawaregetsuka) [#41142](https://github.com/nodejs/node/pull/41142)
- \[[`34ef5a7d4d`](https://github.com/nodejs/node/commit/34ef5a7d4d)] - **doc**: fix syntax error in nested conditions example (Mateusz Burzyński) [#41205](https://github.com/nodejs/node/pull/41205)
- \[[`c9a4603913`](https://github.com/nodejs/node/commit/c9a4603913)] - **esm**: make `process.exit()` default to exit code 0 (Gang Chen) [#41388](https://github.com/nodejs/node/pull/41388)
- \[[`8a94ca7a69`](https://github.com/nodejs/node/commit/8a94ca7a69)] - **esm**: refactor esm tests out of test/message (Geoffrey Booth) [#41352](https://github.com/nodejs/node/pull/41352)
- \[[`5ebe086ea6`](https://github.com/nodejs/node/commit/5ebe086ea6)] - **esm**: reconcile JSDoc vs. actual parameter name (Rich Trott) [#41238](https://github.com/nodejs/node/pull/41238)
- \[[`9fe304b8e8`](https://github.com/nodejs/node/commit/9fe304b8e8)] - **events**: clarify JSDoc entries (Rich Trott) [#41311](https://github.com/nodejs/node/pull/41311)
- \[[`4079fc42b7`](https://github.com/nodejs/node/commit/4079fc42b7)] - **(SEMVER-MINOR)** **events**: graduate capturerejections to supported (James M Snell) [#41267](https://github.com/nodejs/node/pull/41267)
- \[[`e3a0a9cb3a`](https://github.com/nodejs/node/commit/e3a0a9cb3a)] - **events**: add jsdoc details for Event and EventTarget (James M Snell) [#41274](https://github.com/nodejs/node/pull/41274)
- \[[`fe21607901`](https://github.com/nodejs/node/commit/fe21607901)] - **(SEMVER-MINOR)** **events**: add EventEmitterAsyncResource to core (James M Snell) [#41246](https://github.com/nodejs/node/pull/41246)
- \[[`d4a6f2caf1`](https://github.com/nodejs/node/commit/d4a6f2caf1)] - **fs**: use async directory processing in cp() (Colin Ihrig) [#41351](https://github.com/nodejs/node/pull/41351)
- \[[`0951bd94db`](https://github.com/nodejs/node/commit/0951bd94db)] - **fs**: correct param names in JSDoc comments (Rich Trott) [#41237](https://github.com/nodejs/node/pull/41237)
- \[[`1d75436a1c`](https://github.com/nodejs/node/commit/1d75436a1c)] - **http**: remove duplicate code (Shaw) [#39239](https://github.com/nodejs/node/pull/39239)
- \[[`0aacd4926d`](https://github.com/nodejs/node/commit/0aacd4926d)] - **http2**: handle existing socket data when creating HTTP/2 server sessions (Tim Perry) [#41185](https://github.com/nodejs/node/pull/41185)
- \[[`24fbbf2747`](https://github.com/nodejs/node/commit/24fbbf2747)] - **lib**: remove spurious JSDoc entry (Rich Trott) [#41240](https://github.com/nodejs/node/pull/41240)
- \[[`e457ec05d6`](https://github.com/nodejs/node/commit/e457ec05d6)] - **lib**: fix checking syntax of esm module (Qingyu Deng) [#41198](https://github.com/nodejs/node/pull/41198)
- \[[`f176124e8b`](https://github.com/nodejs/node/commit/f176124e8b)] - **lib,tools**: remove empty lines between JSDoc tags (Rich Trott) [#41147](https://github.com/nodejs/node/pull/41147)
- \[[`68fd2ac999`](https://github.com/nodejs/node/commit/68fd2ac999)] - **loader**: fix package resolution for edge case (Gabriel Bota) [#41218](https://github.com/nodejs/node/pull/41218)
- \[[`6d8eb6ace6`](https://github.com/nodejs/node/commit/6d8eb6ace6)] - **(SEMVER-MINOR)** **loader**: return package format from defaultResolve if known (Gabriel Bota) [#40980](https://github.com/nodejs/node/pull/40980)
- \[[`a6146c7e27`](https://github.com/nodejs/node/commit/a6146c7e27)] - **meta**: move one or more collaborators to emeritus (Node.js GitHub Bot) [#41456](https://github.com/nodejs/node/pull/41456)
- \[[`07353e9b8b`](https://github.com/nodejs/node/commit/07353e9b8b)] - **meta**: update AUTHORS (Node.js GitHub Bot) [#41475](https://github.com/nodejs/node/pull/41475)
- \[[`e1ff4521d7`](https://github.com/nodejs/node/commit/e1ff4521d7)] - **meta**: correct my name in AUTHORS (Jacob Smith) [#41444](https://github.com/nodejs/node/pull/41444)
- \[[`da1d5d6563`](https://github.com/nodejs/node/commit/da1d5d6563)] - **meta**: update AUTHORS (Node.js GitHub Bot) [#41449](https://github.com/nodejs/node/pull/41449)
- \[[`0f9afa58d5`](https://github.com/nodejs/node/commit/0f9afa58d5)] - **meta**: add required fields in issue templates (Rich Trott) [#41378](https://github.com/nodejs/node/pull/41378)
- \[[`da04408075`](https://github.com/nodejs/node/commit/da04408075)] - **meta**: update AUTHORS (Node.js GitHub Bot) [#41374](https://github.com/nodejs/node/pull/41374)
- \[[`1f6c4e819b`](https://github.com/nodejs/node/commit/1f6c4e819b)] - **meta**: replace API docs issue template with form (Rich Trott) [#41348](https://github.com/nodejs/node/pull/41348)
- \[[`253c3e5488`](https://github.com/nodejs/node/commit/253c3e5488)] - **meta**: move one or more collaborators to emeritus (Node.js GitHub Bot) [#41336](https://github.com/nodejs/node/pull/41336)
- \[[`3e188cacc2`](https://github.com/nodejs/node/commit/3e188cacc2)] - **meta**: replace feature request template with form (Rich Trott) [#41317](https://github.com/nodejs/node/pull/41317)
- \[[`e339220511`](https://github.com/nodejs/node/commit/e339220511)] - **meta**: update AUTHORS (Node.js GitHub Bot) [#41322](https://github.com/nodejs/node/pull/41322)
- \[[`d0d595f8f2`](https://github.com/nodejs/node/commit/d0d595f8f2)] - **meta**: update node-api team name (Richard Lau) [#41268](https://github.com/nodejs/node/pull/41268)
- \[[`a53fa2010b`](https://github.com/nodejs/node/commit/a53fa2010b)] - **meta**: move one or more collaborators to emeritus (Node.js GitHub Bot) [#41248](https://github.com/nodejs/node/pull/41248)
- \[[`edefb41ec1`](https://github.com/nodejs/node/commit/edefb41ec1)] - **meta**: update AUTHORS (Node.js GitHub Bot) [#41234](https://github.com/nodejs/node/pull/41234)
- \[[`6da7909797`](https://github.com/nodejs/node/commit/6da7909797)] - **meta**: remove community-committee from CODEOWNERS (Rich Trott) [#41169](https://github.com/nodejs/node/pull/41169)
- \[[`5fe011a24d`](https://github.com/nodejs/node/commit/5fe011a24d)] - **node-api**: add missing initialization of last error (Michael Dawson) [#41290](https://github.com/nodejs/node/pull/41290)
- \[[`27c619140a`](https://github.com/nodejs/node/commit/27c619140a)] - **(SEMVER-MINOR)** **perf_hooks**: multiple fixes for Histogram (James M Snell) [#41153](https://github.com/nodejs/node/pull/41153)
- \[[`e60187d8ab`](https://github.com/nodejs/node/commit/e60187d8ab)] - **policy**: add missing JSDoc @param entry (Rich Trott) [#41239](https://github.com/nodejs/node/pull/41239)
- \[[`ec9071f55d`](https://github.com/nodejs/node/commit/ec9071f55d)] - **src**: use `std::optional` for Worker thread id (Anna Henningsen) [#41453](https://github.com/nodejs/node/pull/41453)
- \[[`6aec92f959`](https://github.com/nodejs/node/commit/6aec92f959)] - **src**: gracefully handle errors in GetX509NameObject (Tobias Nießen) [#41490](https://github.com/nodejs/node/pull/41490)
- \[[`7ac126b75c`](https://github.com/nodejs/node/commit/7ac126b75c)] - **src**: fix out-of-bounds check of serialization indices (JoostK) [#41452](https://github.com/nodejs/node/pull/41452)
- \[[`93b3664b9a`](https://github.com/nodejs/node/commit/93b3664b9a)] - **src**: do IWYU for some STL includes (David Sanders) [#41236](https://github.com/nodejs/node/pull/41236)
- \[[`337ebfcd53`](https://github.com/nodejs/node/commit/337ebfcd53)] - **src**: split out async stack corruption detection from inline fn (Anna Henningsen) [#41331](https://github.com/nodejs/node/pull/41331)
- \[[`250e197a62`](https://github.com/nodejs/node/commit/250e197a62)] - **src**: store native async execution resources as `v8::Local` (Anna Henningsen) [#41331](https://github.com/nodejs/node/pull/41331)
- \[[`6187e81a8e`](https://github.com/nodejs/node/commit/6187e81a8e)] - **src**: guard slightly costly check in MakeCallback more strongly (Anna Henningsen) [#41331](https://github.com/nodejs/node/pull/41331)
- \[[`51d86fe6a0`](https://github.com/nodejs/node/commit/51d86fe6a0)] - **stream**: remove always-false condition check (Rich Trott) [#41488](https://github.com/nodejs/node/pull/41488)
- \[[`b08138f367`](https://github.com/nodejs/node/commit/b08138f367)] - **stream**: fix error-path function call (Rich Trott) [#41433](https://github.com/nodejs/node/pull/41433)
- \[[`d79f8c2987`](https://github.com/nodejs/node/commit/d79f8c2987)] - **stream**: remove unused function argument (Rich Trott) [#41403](https://github.com/nodejs/node/pull/41403)
- \[[`09c25bb224`](https://github.com/nodejs/node/commit/09c25bb224)] - **(SEMVER-MINOR)** **stream**: add filter method to readable (Benjamin Gruenbaum) [#41354](https://github.com/nodejs/node/pull/41354)
- \[[`1150963217`](https://github.com/nodejs/node/commit/1150963217)] - **(SEMVER-MINOR)** **stream**: add isReadable helper (Robert Nagy) [#41199](https://github.com/nodejs/node/pull/41199)
- \[[`9f5a873965`](https://github.com/nodejs/node/commit/9f5a873965)] - **(SEMVER-MINOR)** **stream**: add map method to Readable (Benjamin Gruenbaum) [#40815](https://github.com/nodejs/node/pull/40815)
- \[[`3dc65646c8`](https://github.com/nodejs/node/commit/3dc65646c8)] - **stream**: fix enqueue race condition on esm modules (Rafael Gonzaga) [#40901](https://github.com/nodejs/node/pull/40901)
- \[[`09f2fd36a4`](https://github.com/nodejs/node/commit/09f2fd36a4)] - **test**: improve test coverage of dns/promises (Yoshiki Kurihara) [#41425](https://github.com/nodejs/node/pull/41425)
- \[[`106ef0cef4`](https://github.com/nodejs/node/commit/106ef0cef4)] - **test**: remove broken wiki link from test/common doc (Yoshiki Kurihara) [#41426](https://github.com/nodejs/node/pull/41426)
- \[[`9d8d7c63cb`](https://github.com/nodejs/node/commit/9d8d7c63cb)] - **test**: do not OR F_OK in fs.access() test (Colin Ihrig) [#41484](https://github.com/nodejs/node/pull/41484)
- \[[`3e2154deda`](https://github.com/nodejs/node/commit/3e2154deda)] - **test**: mark test-performance-eventloopdelay flaky (Michael Dawson) [#41409](https://github.com/nodejs/node/pull/41409)
- \[[`e808ee68d0`](https://github.com/nodejs/node/commit/e808ee68d0)] - **test**: mark test-repl-sigint-nested-eval as flaky (Michael Dawson) [#41302](https://github.com/nodejs/node/pull/41302)
- \[[`f97f6c585d`](https://github.com/nodejs/node/commit/f97f6c585d)] - **test**: use spawnSync() full name in test-stdio-pipe-stderr (Rich Trott) [#41332](https://github.com/nodejs/node/pull/41332)
- \[[`75c565bf18`](https://github.com/nodejs/node/commit/75c565bf18)] - **test**: improve expectWarning error message (Rich Trott) [#41326](https://github.com/nodejs/node/pull/41326)
- \[[`c136d597f0`](https://github.com/nodejs/node/commit/c136d597f0)] - **test**: use spawnSync() full name (Rich Trott) [#41327](https://github.com/nodejs/node/pull/41327)
- \[[`b2a87f770d`](https://github.com/nodejs/node/commit/b2a87f770d)] - **test**: add comments explaining \_setSimultaneousAccepts deprecation tests (Yoshiki Kurihara) [#41307](https://github.com/nodejs/node/pull/41307)
- \[[`fac0871102`](https://github.com/nodejs/node/commit/fac0871102)] - **test**: mark test-worker-take-heapsnapshot flaky (Michael Dawson) [#41253](https://github.com/nodejs/node/pull/41253)
- \[[`90617b9303`](https://github.com/nodejs/node/commit/90617b9303)] - **test**: mark wpt/test-user-timing test flaky (Michael Dawson) [#41203](https://github.com/nodejs/node/pull/41203)
- \[[`8f08328a01`](https://github.com/nodejs/node/commit/8f08328a01)] - **test**: correct param name in JSDoc comment (Rich Trott) [#41241](https://github.com/nodejs/node/pull/41241)
- \[[`367ab2a55e`](https://github.com/nodejs/node/commit/367ab2a55e)] - **test**: mark test-crypto-keygen slow on windows (Michael Dawson) [#41207](https://github.com/nodejs/node/pull/41207)
- \[[`f067876338`](https://github.com/nodejs/node/commit/f067876338)] - **test**: improve test coverage of dns/promises (Yoshiki Kurihara) [#41133](https://github.com/nodejs/node/pull/41133)
- \[[`2e92f6f5d9`](https://github.com/nodejs/node/commit/2e92f6f5d9)] - **timers**: use ref counts to count timers (Darshan Sen) [#41231](https://github.com/nodejs/node/pull/41231)
- \[[`3c8b25bec8`](https://github.com/nodejs/node/commit/3c8b25bec8)] - **tls**: use optional chaining to simplify checks (Antoine du Hamel) [#41337](https://github.com/nodejs/node/pull/41337)
- \[[`a11ff31bca`](https://github.com/nodejs/node/commit/a11ff31bca)] - **tls**: permit null as a pfx value (CallMeLaNN) [#41170](https://github.com/nodejs/node/pull/41170)
- \[[`5129b7c802`](https://github.com/nodejs/node/commit/5129b7c802)] - **tools**: fix small not-quite-a-bug in find-inactive-tsc.mjs (Rich Trott) [#41469](https://github.com/nodejs/node/pull/41469)
- \[[`258ee4ba64`](https://github.com/nodejs/node/commit/258ee4ba64)] - **tools**: enable ESLint recommended configuration (Rich Trott) [#41463](https://github.com/nodejs/node/pull/41463)
- \[[`090a674a81`](https://github.com/nodejs/node/commit/090a674a81)] - **tools**: enable ESLint no-constant-condition rule (Rich Trott) [#41463](https://github.com/nodejs/node/pull/41463)
- \[[`1f4369a106`](https://github.com/nodejs/node/commit/1f4369a106)] - **tools**: enable ESLint require-yield rule (Rich Trott) [#41463](https://github.com/nodejs/node/pull/41463)
- \[[`8090ce7a6c`](https://github.com/nodejs/node/commit/8090ce7a6c)] - **tools**: enable ESLint no-sparse-arrays rule (Rich Trott) [#41463](https://github.com/nodejs/node/pull/41463)
- \[[`afa4f37faf`](https://github.com/nodejs/node/commit/afa4f37faf)] - **tools**: enable ESLint no-loss-of-precision rule (Rich Trott) [#41463](https://github.com/nodejs/node/pull/41463)
- \[[`ec337b2019`](https://github.com/nodejs/node/commit/ec337b2019)] - **tools**: replace for loop with map() (Rich Trott) [#41451](https://github.com/nodejs/node/pull/41451)
- \[[`c91ac205a5`](https://github.com/nodejs/node/commit/c91ac205a5)] - **tools**: use GITHUB_ACTIONS env var in inactivity scripts (Rich Trott) [#41422](https://github.com/nodejs/node/pull/41422)
- \[[`4a57d476a8`](https://github.com/nodejs/node/commit/4a57d476a8)] - **tools**: replace while+exec() with matchAll() (Rich Trott) [#41406](https://github.com/nodejs/node/pull/41406)
- \[[`583f8d969a`](https://github.com/nodejs/node/commit/583f8d969a)] - **tools**: fix argv bug in find-inactive-tsc.mjs (Rich Trott) [#41394](https://github.com/nodejs/node/pull/41394)
- \[[`dcada80f30`](https://github.com/nodejs/node/commit/dcada80f30)] - **tools**: remove conditional assignment in custom ESLint rule (Rich Trott) [#41325](https://github.com/nodejs/node/pull/41325)
- \[[`e15e1cb030`](https://github.com/nodejs/node/commit/e15e1cb030)] - **tools**: update lint-md-dependencies to @rollup/plugin-node-resolve\@13.1.2 (Node.js GitHub Bot) [#41369](https://github.com/nodejs/node/pull/41369)
- \[[`07683021b7`](https://github.com/nodejs/node/commit/07683021b7)] - **tools**: update doc to rehype-raw\@6.1.1 (Node.js GitHub Bot) [#41367](https://github.com/nodejs/node/pull/41367)
- \[[`bd8b95a5e8`](https://github.com/nodejs/node/commit/bd8b95a5e8)] - **tools**: remove last of error-masking in commit-queue.sh (Rich Trott) [#41356](https://github.com/nodejs/node/pull/41356)
- \[[`9284d24df6`](https://github.com/nodejs/node/commit/9284d24df6)] - **tools**: update eslint to 8.6.0 (Node.js GitHub Bot) [#41368](https://github.com/nodejs/node/pull/41368)
- \[[`5fc886f68e`](https://github.com/nodejs/node/commit/5fc886f68e)] - **tools**: do not mask errors on multiple commit retrieval (Rich Trott) [#41340](https://github.com/nodejs/node/pull/41340)
- \[[`0ca7cda962`](https://github.com/nodejs/node/commit/0ca7cda962)] - **tools**: enable jsdoc/check-param-names lint rule (Rich Trott) [#41311](https://github.com/nodejs/node/pull/41311)
- \[[`75ff8e6505`](https://github.com/nodejs/node/commit/75ff8e6505)] - **tools**: improve section tag additions in HTML doc generator (Rich Trott) [#41318](https://github.com/nodejs/node/pull/41318)
- \[[`9c4124706e`](https://github.com/nodejs/node/commit/9c4124706e)] - **tools**: simplify commit-queue.sh merge command (Rich Trott) [#41314](https://github.com/nodejs/node/pull/41314)
- \[[`137c814848`](https://github.com/nodejs/node/commit/137c814848)] - **tools**: update lint-md-dependencies to rollup\@2.62.0 (Node.js GitHub Bot) [#41315](https://github.com/nodejs/node/pull/41315)
- \[[`58da5d9b43`](https://github.com/nodejs/node/commit/58da5d9b43)] - **tools**: use Object.hasOwn() in alljson.mjs (Rich Trott) [#41306](https://github.com/nodejs/node/pull/41306)
- \[[`c12cbf2020`](https://github.com/nodejs/node/commit/c12cbf2020)] - **tools**: avoid generating duplicate id attributes (Rich Trott) [#41291](https://github.com/nodejs/node/pull/41291)
- \[[`80a114d1b7`](https://github.com/nodejs/node/commit/80a114d1b7)] - **tools**: be intentional about masking possible error in start-ci.sh (Rich Trott) [#41284](https://github.com/nodejs/node/pull/41284)
- \[[`198528426d`](https://github.com/nodejs/node/commit/198528426d)] - **tools**: use {N} for spaces in regex (Rich Trott) [#41295](https://github.com/nodejs/node/pull/41295)
- \[[`46b364a684`](https://github.com/nodejs/node/commit/46b364a684)] - **tools**: consolidate update-authors.js logic (Rich Trott) [#41255](https://github.com/nodejs/node/pull/41255)
- \[[`c546cef4bc`](https://github.com/nodejs/node/commit/c546cef4bc)] - **tools**: update doc dependency mdast-util-gfm-table to 1.0.2 (Rich Trott) [#41260](https://github.com/nodejs/node/pull/41260)
- \[[`60c059e4bc`](https://github.com/nodejs/node/commit/60c059e4bc)] - **tools**: make license-builder.sh comply with shellcheck 0.8.0 (Rich Trott) [#41258](https://github.com/nodejs/node/pull/41258)
- \[[`62e28f19f7`](https://github.com/nodejs/node/commit/62e28f19f7)] - **tools**: use arrow function for callback in lint-sh.js (Rich Trott) [#41256](https://github.com/nodejs/node/pull/41256)
- \[[`e2df381da9`](https://github.com/nodejs/node/commit/e2df381da9)] - **tools**: add double-quotes to make-v8.sh (Rich Trott) [#41257](https://github.com/nodejs/node/pull/41257)
- \[[`dae2e5fffa`](https://github.com/nodejs/node/commit/dae2e5fffa)] - **tools**: enable prefer-object-has-own lint rule (Rich Trott) [#41245](https://github.com/nodejs/node/pull/41245)
- \[[`aa7d14768d`](https://github.com/nodejs/node/commit/aa7d14768d)] - **tools**: update eslint to 8.5.0 (Node.js GitHub Bot) [#41228](https://github.com/nodejs/node/pull/41228)
- \[[`0c14e7e7c8`](https://github.com/nodejs/node/commit/0c14e7e7c8)] - **tools**: enable jsdoc/tag-lines ESLint rule (Rich Trott) [#41147](https://github.com/nodejs/node/pull/41147)
- \[[`c486da1715`](https://github.com/nodejs/node/commit/c486da1715)] - **tools**: update lint-md-dependencies to @rollup/plugin-node-resolve\@13.1.1 (Node.js GitHub Bot) [#41227](https://github.com/nodejs/node/pull/41227)
- \[[`82f492bbb0`](https://github.com/nodejs/node/commit/82f492bbb0)] - **tools**: fix CQ and auto-start-ci jobs (Antoine du Hamel) [#41230](https://github.com/nodejs/node/pull/41230)
- \[[`c44185ca37`](https://github.com/nodejs/node/commit/c44185ca37)] - **tools**: fix GitHub Actions status when CQ is empty (Antoine du Hamel) [#41193](https://github.com/nodejs/node/pull/41193)
- \[[`800640adf9`](https://github.com/nodejs/node/commit/800640adf9)] - **tools,benchmark,lib,test**: enable no-case-declarations lint rule (Rich Trott) [#41385](https://github.com/nodejs/node/pull/41385)
- \[[`4518fdda24`](https://github.com/nodejs/node/commit/4518fdda24)] - **tools,lib,test**: enable ESLint no-regex-spaces rule (Rich Trott) [#41463](https://github.com/nodejs/node/pull/41463)
- \[[`c8e8fc0ecb`](https://github.com/nodejs/node/commit/c8e8fc0ecb)] - **typings**: add types for symbol and accessor properties on `primordials` (ExE Boss) [#40992](https://github.com/nodejs/node/pull/40992)
- \[[`d733b56101`](https://github.com/nodejs/node/commit/d733b56101)] - **typings**: add JSDoc for `string_decoder` (Qingyu Deng) [#38229](https://github.com/nodejs/node/pull/38229)
- \[[`01ad8debd3`](https://github.com/nodejs/node/commit/01ad8debd3)] - **url,lib**: pass urlsearchparams-constructor.any.js (Khaidi Chu) [#41197](https://github.com/nodejs/node/pull/41197)
- \[[`5ed8a1c017`](https://github.com/nodejs/node/commit/5ed8a1c017)] - **util**: do not reduce to a single line if not appropriate using inspect (Ruben Bridgewater) [#41083](https://github.com/nodejs/node/pull/41083)
- \[[`ab5e94c832`](https://github.com/nodejs/node/commit/ab5e94c832)] - **util**: display a present-but-undefined error cause (Jordan Harband) [#41247](https://github.com/nodejs/node/pull/41247)

Windows 32-bit Installer: https://nodejs.org/dist/v17.4.0/node-v17.4.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v17.4.0/node-v17.4.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v17.4.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v17.4.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v17.4.0/node-v17.4.0.pkg \
macOS Apple Silicon 64-bit Binary: https://nodejs.org/dist/v17.4.0/node-v17.4.0-darwin-arm64.tar.gz \
macOS Intel 64-bit Binary: https://nodejs.org/dist/v17.4.0/node-v17.4.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v17.4.0/node-v17.4.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v17.4.0/node-v17.4.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v17.4.0/node-v17.4.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v17.4.0/node-v17.4.0-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v17.4.0/node-v17.4.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v17.4.0/node-v17.4.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v17.4.0/node-v17.4.0.tar.gz \
Other release files: https://nodejs.org/dist/v17.4.0/ \
Documentation: https://nodejs.org/docs/v17.4.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

91688cbeac365a5dde54f8615845f99c0b999599bd9924340ba5aa1ae9f590ff  node-v17.4.0-aix-ppc64.tar.gz
9bd53805faf6df658ecd4f54321b25eff89818efdcb52c20435ff1703fd7064c  node-v17.4.0-darwin-arm64.tar.gz
2d1c06b8273dd6a495167e383c9662210c362d292493398f12d2e9633e38d821  node-v17.4.0-darwin-arm64.tar.xz
27e24d9f7a9a83bb59353249cce7cff16067e0483a627b5b9a1f1478101e64ee  node-v17.4.0-darwin-x64.tar.gz
5f6417538275826f1739cccb949b1df47e79cfcc636eb0175f5c6aa3cf8751bb  node-v17.4.0-darwin-x64.tar.xz
8bcac1d1bdb8db7b301f7de50bf4892ebabd4c870936bf53b18ca01708bbb325  node-v17.4.0-headers.tar.gz
d0c54f2081f020e0020f11d6535d381a9112b1baf612ce7d7e3ba631da5d63b9  node-v17.4.0-headers.tar.xz
770ae5f8c6012138d877c858d1e6bcb53b65dbce4a20b3159cc58e8c4585d610  node-v17.4.0-linux-arm64.tar.gz
1b29a3a3be2c921d76a6a52a778df231d913fcf496e8237109cd3f528095d7b8  node-v17.4.0-linux-arm64.tar.xz
4926f5d58ece2330fb574ef77c683af705f35e69bd679ea2aea48c559b7eddd8  node-v17.4.0-linux-armv7l.tar.gz
6c414f3f243fed898b582a7a393143b4a2fa12a4d4fb5f3039ddd8ecd13d298f  node-v17.4.0-linux-armv7l.tar.xz
d3f2bb969120fbff3aef1e5d75c0f00c04549ee930743d9a51ed5e675cff7788  node-v17.4.0-linux-ppc64le.tar.gz
95a278989b33cbcfe410deb7fad8f90d56b206a89c4ca8ebef95d7e3791ba10d  node-v17.4.0-linux-ppc64le.tar.xz
2e252e33cb4445b5c16ddda64d25ed3e3e43f75af6a00a7fa32a716877bf3422  node-v17.4.0-linux-s390x.tar.gz
713e63513ca2a313f3a77cc88a429636051f1f779d9b0ada176089d2d1e05161  node-v17.4.0-linux-s390x.tar.xz
132c61652c315a6f784167b97a53e9638bac45853f1544a84d4cbb90fe7a3bda  node-v17.4.0-linux-x64.tar.gz
59fa3173da3b0f4ace81166a0facd7f1d7014e076842a85a9b356e0293b91988  node-v17.4.0-linux-x64.tar.xz
1cb3407c0ab76837a38fc985c5bbb4b6d131e99d2e068fe3514ad80c09c6110b  node-v17.4.0.pkg
19d354d17434ed257863a76a511e9af37f6ae4b318974fb9d9275f019e37de78  node-v17.4.0.tar.gz
cf8c71f07706c75178fb904a6d54ab33e9b3781287dfface5c7804860b8301b2  node-v17.4.0.tar.xz
c6eb65d6b9c9f162ad011223a0b264f0ed2fb3576572f56149a55c3b372ef7f9  node-v17.4.0-win-x64.7z
1fce1ac96d52cb5432640b416d8b58d0930ae336b2e2c78aad76f01d0cabfc92  node-v17.4.0-win-x64.zip
8d5ebfb8f54456ebdaa6b26cd232d6d1e2a29c90122fad7f7f56cf5fa075c0a7  node-v17.4.0-win-x86.7z
df61fc3b915e92778d8875533d1aa2dd312ccbd5ef87351348b60a220d6b4d5d  node-v17.4.0-win-x86.zip
cb0fb891cf19939e2c778aeabbc4bccf62e5ca6c2b8aacf0fcc64bc96da24560  node-v17.4.0-x64.msi
20aee5bcfefa252785c71336fca37726f880ef2f539541f594bb4c9c998549ba  node-v17.4.0-x86.msi
8d3d3558e9b5354d2af2c5a19e7d98b1184ea102f9822d352f8f3516612acffd  win-x64/node.exe
0a1967c413566a0791f4202cf7732293fe4c1f7f760b796271ff0a8f0e694cfb  win-x64/node.lib
820ad154dba718417ef86db1f6e8de709bc1ef323c819b0846c2073bc37d1478  win-x64/node_pdb.7z
b069633a00bb734bd4ac5d919652f8a0460cce642e6aa536039dff156a586021  win-x64/node_pdb.zip
755e65247e8e26627e229813693e68a8e6f34e56fe783bad7ca1c576e2207d61  win-x86/node.exe
f415cb6dfc55919e8ea215a4a2507deca68746defc3240a915244b526a61b566  win-x86/node.lib
d2c5513555fdbac128a1746ecdde86bab19ca0abd028606dc16354b5a577951d  win-x86/node_pdb.7z
7db7355a40f01d7f3dd1bc7bffd577af6fe68bf97a338f269520839e4d9d2eda  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEj8yhP+8dDC6RAI4Jdw96mlrhVgAFAmHm11UACgkQdw96mlrh
VgDTsw/9HHcEtxZF7Tzu1bZI2mBPTbwgPEg9hcwsKNXWwLjbFXnyPgWnsP0Qlnfm
chPW1Lnqd86yEG25jaSrdAHw45x9El36op7QHamcpvTXyu0YjXEtRFU7/fny1CYI
Isd3CRY2439qgWtmNSF5cMCSjNk9F9LzlB9bv0YrTqAl7B1iCT0Ag6cxHJXGVuT/
Ak6+AmrCiRhF1p3rlpM4RIaHPFC+KSA4Wjq/iygQiqPvtSY7EGXp8hfkHv2SULko
U3ACJhD+dF2U9+ujul8t7/mJ3gcYxGxJ0RXEiu/qkQDVOPOYij5ddC5QohiXGa9A
g2b2KKJX7L3Xx+/giNXe7jmRqeUE/P8rhKI11VbLgq9YZTWB5EOOoccKUMP4Cp4H
jqWyEynPpXCmRNXfXk7ebHX5GPm77LymW9v99WODNYjNzF48yDuCHabJ8xoaDjqL
jTxC4Xt4Om3uqtPfMnV7yHUWjugr9t4Yx9ccPpo+maHIP1OJzJ7bIZD0fCOOAQWR
O9+7OUkACOTG0jyyyOB2pfNVSbn7l5XzuOPvvLMHyiJaqmWBQZl5hxh4f/6/dbzy
Poc+0PQWKC4qa94N61mYT0UYoJa4UcJ/bx728AhwlRD+yYTYt4SFYjGzWtk6kGWg
W5LHB2MEQ3ikJEn9C0k/cmzGyqayvytGuwNjCtrnzvi7YXKjJYE=
=WuiM
-----END PGP SIGNATURE-----

```
