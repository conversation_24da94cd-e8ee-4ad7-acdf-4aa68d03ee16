---
date: '2021-01-04T18:20:48.579Z'
category: release
title: Node v10.23.1 (LTS)
layout: blog-post
author: <PERSON>
---

### Notable changes

This is a security release.

Vulnerabilities fixed:

- **CVE-2020-8265**: use-after-free in TLSWrap (High)
  Affected Node.js versions are vulnerable to a use-after-free bug in its
  TLS implementation. When writing to a TLS enabled socket,
  node::StreamBase::Write calls node::TLSWrap::DoWrite with a freshly
  allocated WriteWrap object as first argument. If the DoWrite method does
  not return an error, this object is passed back to the caller as part of
  a StreamWriteResult structure. This may be exploited to corrupt memory
  leading to a Denial of Service or potentially other exploits
- **CVE-2020-8287**: HTTP Request Smuggling in nodejs
  Affected versions of Node.js allow two copies of a header field in a
  http request. For example, two Transfer-Encoding header fields. In this
  case Node.js identifies the first header field and ignores the second.
  This can lead to HTTP Request Smuggling
  (https://cwe.mitre.org/data/definitions/444.html).
- **CVE-2020-1971**: OpenSSL - EDIPARTYNAME NULL pointer de-reference (High)
  This is a vulnerability in OpenSSL which may be exploited through Node.js.
  You can read more about it in
  https://www.openssl.org/news/secadv/20201208.txt

### Commits

- [[`bd44b0ee7f`](https://github.com/nodejs/node/commit/bd44b0ee7f)] - **build,win**: accept Python 3 if 2 is not available (João Reis) [#29236](https://github.com/nodejs/node/pull/29236)
- [[`d5c9b09bdc`](https://github.com/nodejs/node/commit/d5c9b09bdc)] - **build,win**: find Python in paths with spaces (João Reis) [#29236](https://github.com/nodejs/node/pull/29236)
- [[`323a6f114a`](https://github.com/nodejs/node/commit/323a6f114a)] - **deps**: update http-parser to http-parser@ec8b5ee63f (Richard Lau) [nodejs-private/node-private#235](https://github.com/nodejs-private/node-private/pull/235)
- [[`f08d0fef64`](https://github.com/nodejs/node/commit/f08d0fef64)] - **deps**: upgrade npm to 6.14.10 (Ruy Adorno) [#36571](https://github.com/nodejs/node/pull/36571)
- [[`b0608b574a`](https://github.com/nodejs/node/commit/b0608b574a)] - **deps**: update archs files for OpenSSL-1.1.1i (Richard Lau) [#36541](https://github.com/nodejs/node/pull/36541)
- [[`d936e1833f`](https://github.com/nodejs/node/commit/d936e1833f)] - **deps**: upgrade openssl sources to 1.1.1i (Myles Borins) [#36541](https://github.com/nodejs/node/pull/36541)
- [[`9c4970715c`](https://github.com/nodejs/node/commit/9c4970715c)] - **deps**: upgrade npm to 6.14.9 (Myles Borins) [#36450](https://github.com/nodejs/node/pull/36450)
- [[`aa6b97fb99`](https://github.com/nodejs/node/commit/aa6b97fb99)] - **http**: add test for http transfer encoding smuggling (Richard Lau) [nodejs-private/node-private#235](https://github.com/nodejs-private/node-private/pull/235)
- [[`fc70ce08f5`](https://github.com/nodejs/node/commit/fc70ce08f5)] - **http**: unset `F_CHUNKED` on new `Transfer-Encoding` (Fedor Indutny) [nodejs-private/node-private#235](https://github.com/nodejs-private/node-private/pull/235)
- [[`7f178663eb`](https://github.com/nodejs/node/commit/7f178663eb)] - **src**: use unique_ptr for WriteWrap (Daniel Bevenius) [nodejs-private/node-private#238](https://github.com/nodejs-private/node-private/pull/238)
- [[`357e2857c8`](https://github.com/nodejs/node/commit/357e2857c8)] - **test**: add test-tls-use-after-free-regression (Daniel Bevenius) [nodejs-private/node-private#238](https://github.com/nodejs-private/node-private/pull/238)

Windows 32-bit Installer: https://nodejs.org/dist/v10.23.1/node-v10.23.1-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v10.23.1/node-v10.23.1-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v10.23.1/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v10.23.1/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v10.23.1/node-v10.23.1.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v10.23.1/node-v10.23.1-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v10.23.1/node-v10.23.1-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v10.23.1/node-v10.23.1-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v10.23.1/node-v10.23.1-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v10.23.1/node-v10.23.1-aix-ppc64.tar.gz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v10.23.1/node-v10.23.1-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v10.23.1/node-v10.23.1-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v10.23.1/node-v10.23.1-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v10.23.1/node-v10.23.1-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v10.23.1/node-v10.23.1.tar.gz \
Other release files: https://nodejs.org/dist/v10.23.1/ \
Documentation: https://nodejs.org/docs/v10.23.1/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

33bf67ad4a5843b0c1a5a9f3800ccbe1f30b068889177049bd6faca4a843c64a  node-v10.23.1-aix-ppc64.tar.gz
07da39e4c122d1cee744f3a3ace904edf23c3256879adedafcca6a1da4ca4681  node-v10.23.1-darwin-x64.tar.gz
febfdabd98ab5b8f8fdff32ab8c111d834083805bf042ab65c923cc22609c99d  node-v10.23.1-darwin-x64.tar.xz
a877aa44822994b21312a054e05db8a7784391acffd1c1fc85291a014e129c76  node-v10.23.1-headers.tar.gz
d2492742a95a74f2bada841203e9390831c37bfec7580cf0605cab3f521f7bae  node-v10.23.1-headers.tar.xz
e7d0476b1e9add7b21297698517356bb7c7d7f10e75f5abad6ab5806518a6cd6  node-v10.23.1-linux-arm64.tar.gz
9b923a161d80d2802241538c8f2099705163fc7f0dcd886d2274d8d6adf4f58f  node-v10.23.1-linux-arm64.tar.xz
75f04c8c26b83afe40eb1de97a04efca1adf1dd2ad1b887bed297888d7760aaf  node-v10.23.1-linux-armv6l.tar.gz
5f77882103c03e6a486bd11a7218ef6ae9a720172f9369a4a3fa4c5fa21d653b  node-v10.23.1-linux-armv6l.tar.xz
8f965f2757efcf3077d655bfcea36f7a29c58958355e0eb23cfb725740c3ccbe  node-v10.23.1-linux-armv7l.tar.gz
a26aa4d9b2ac37c49d3f7c6198502faee3806f6386559aeabc8ac1bf02f99b59  node-v10.23.1-linux-armv7l.tar.xz
46e590ecef7bfa76e2de1f57e0a1b0b0df2bd0142a25e2329ccfbd9fc33e7cc8  node-v10.23.1-linux-ppc64le.tar.gz
4390158e7230be4968dafe94524a8c7fb65b7c622753146d4238306a5733340d  node-v10.23.1-linux-ppc64le.tar.xz
7b51c8bc1d15fac73245685437079ca6952c38560d94732630645a61303d6b47  node-v10.23.1-linux-s390x.tar.gz
615962749dfe497d455426bc3097978b0504e0988e8bd198c0d202896056c245  node-v10.23.1-linux-s390x.tar.xz
2a5f9d862468a4c677630923531e52339526cfd075cc6df30da4636782eb7bda  node-v10.23.1-linux-x64.tar.gz
207e5ec77ca655ba6fcde922d6b329acbb09898b0bd793ccfcce6c27a36fdff0  node-v10.23.1-linux-x64.tar.xz
8e963dd9cba374d610c33609faa36f37589e315e97b855c6aeccf02844d194f8  node-v10.23.1.pkg
5c6e8f40b3d36d59035d998bbcaffbd9a326efa57c15c32941c23529f1a2da6c  node-v10.23.1-sunos-x64.tar.gz
776c17af00d8a758f0fd41b00cd1f173a1f8787e5f850d1c7fe0a515655dfffc  node-v10.23.1-sunos-x64.tar.xz
a5348b5dfdbe32e0be2837576e66ac285a59907e11e5439fbcda25e70f52550d  node-v10.23.1.tar.gz
88aa16f5af79615b183ca55ed81393763169e75d1fb96013cf1831895c6cedfa  node-v10.23.1.tar.xz
b4808da108c43de909bc704ce6ef4bba6b9c1f054879253b9810d078c6e70764  node-v10.23.1-win-x64.7z
497996aa1037b6f7f492c1d275bb90140269223939525286b4e95babad16f0de  node-v10.23.1-win-x64.zip
9d4c520003d3aa151a53b43289a51bfb2f7af93b7f011dadccd9a1619b3e2510  node-v10.23.1-win-x86.7z
c3be0fd50c218cc52e85377c9e22d7d2110d72de95bdbc9447145b246330f818  node-v10.23.1-win-x86.zip
4495bdc0646cc535f5d5ad5ca52bb18302cda8d4da950bdecf3043f7d28d7cef  node-v10.23.1-x64.msi
ddc19192781e1b3aebfaee894ccba624c923b2691509b1883bb2a54d0bc396f1  node-v10.23.1-x86.msi
82e2bab0e8c9d16324de69e5f1cfcd46ab1b2db272c6d204fe09d2f5509a9c08  win-x64/node.exe
fe8c3392fafc6a09f59f9d9a2c6dbf922fcde9d072e1abb52df3b0eb09dd27c9  win-x64/node.lib
50cff21434ccb4628e53033167e822021fd0c5fe42d98e1322405179708f8508  win-x64/node_pdb.7z
8ff935da1e63c3a3137f7ff80c67cb8395e42d5c8c1b6209e6c5ce0e7b076409  win-x64/node_pdb.zip
5d2f4b8c34fdc1398b1cda50db5b518e4c42616b9ffc9b05d985e5fabb1f47eb  win-x86/node.exe
2cd7d4a696cd539b4592af0a245db1a8235f2c20e30ae0fa2921d8a130933d8a  win-x86/node.lib
8db9406024e50a833fe31e2805e31834183a18bcded7637721d559b444434779  win-x86/node_pdb.7z
5025c6f535d025d5fde0e64b55199c924300e78da7b665030cdd6948d522e4c1  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEyC+jrhy+3Gvka5NgxDzsRcF6uTwFAl/zWpUACgkQxDzsRcF6
uTwdTw//XCxqbJH/uzqUGNkl09ZpNbAXPBksX+UifPvtAxdN0S5N710Mxmo584MZ
Go7k/EgH6zdoO6s6nehtfQNnjuq2sfUuYD7T6ZzQWfwe7VZt43rkTRHq0Wj+VveX
MulN9R8J4T6dHb0KvaqHAdiWs3HkOH9XkdjhOoRLVEJ1aOn7JNztmg0LA+mCdbSP
A0JW+M0WY1Q3qyjGesSTj9YJ+jts0AgAzSbMUBFNETPXqIHjljN0nX2+SCq/CXeM
lcje2mbxe+DeKZ0s4fMHAvmsLXKq+hntqWxhtJ2Rp79wVWZGKV7XrTQPhtShfruK
K+v/mywfNLTxUjI419q7Pn/iXUbPb3UnAhEhoj1m9OEJU3tfw9up4DF3Fzupxc2w
y68TfG6JQXPq3X4LTMUwa4Hj0HRWMdE2fYAzHQFWgAfI+jLD+7IdBSkoyANccG40
tHqajytGJIMAbYU0m+B23aySjSkfai7Dy3GjThqaWvy+jmPLpFtSJ/74hyc/Wp2f
/aYuzbKb6cPCAg0OSLaScQpo0HqjsE8Kbsbt/RFHEEAcXBnTMaa98SgbjTDJgjaa
NipIayFSBALUJEWQNYcNIltMgBnpV2Rzh2U2imXuxZLjk+P1Do5/zXTED4E+lU6Q
KvSBzQPrbaXH0rgfSwcl7p7S6ntzZ+jUPXd7HAQrSvDMlZ0xelo=
=TVIl
-----END PGP SIGNATURE-----

```
