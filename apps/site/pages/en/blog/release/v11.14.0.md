---
date: '2019-04-11T17:13:10.246Z'
category: release
title: Node v11.14.0 (Current)
layout: blog-post
author: <PERSON>
---

### Notable changes

- **child_process**: doc deprecate ChildProcess.\_channel (cjihrig) [#26982](https://github.com/nodejs/node/pull/26982)
- **deps**: update nghttp2 to 1.37.0 (gengjiawen) [#26990](https://github.com/nodejs/node/pull/26990)
- **dns**:
  - make dns.promises enumerable (cjihrig) [#26592](https://github.com/nodejs/node/pull/26592)
  - remove dns.promises experimental warning (cjihrig) [#26592](https://github.com/nodejs/node/pull/26592)
- **fs**: remove experimental warning for fs.promises (<PERSON>) [#26581](https://github.com/nodejs/node/pull/26581)
- **stream**: make Symbol.asyncIterator support stable (<PERSON>) [#26989](https://github.com/nodejs/node/pull/26989)
- **worker**: use copy of process.env (<PERSON> Henningsen) [#26544](https://github.com/nodejs/node/pull/26544)

### Commits

- [[`ca7c4f485b`](https://github.com/nodejs/node/commit/ca7c4f485b)] - **async_hooks**: minor cleanup and improvements (Anatoli Papirovski) [#27034](https://github.com/nodejs/node/pull/27034)
- [[`e9bffa8166`](https://github.com/nodejs/node/commit/e9bffa8166)] - **benchmark**: improve module-loader benchmark (Ruben Bridgewater) [#26970](https://github.com/nodejs/node/pull/26970)
- [[`09d6dfb21d`](https://github.com/nodejs/node/commit/09d6dfb21d)] - **benchmark**: add new module loading benchmarks (Ruben Bridgewater) [#26970](https://github.com/nodejs/node/pull/26970)
- [[`5512ecb5b0`](https://github.com/nodejs/node/commit/5512ecb5b0)] - **benchmark**: tidy up eslint ignore in foreach-bench.js (gengjiawen) [#26925](https://github.com/nodejs/node/pull/26925)
- [[`de937375e4`](https://github.com/nodejs/node/commit/de937375e4)] - **benchmark**: remove unused field in class BenchmarkProgress (gengjiawen) [#26925](https://github.com/nodejs/node/pull/26925)
- [[`0aea4d1c77`](https://github.com/nodejs/node/commit/0aea4d1c77)] - **benchmark,lib**: change var to const (Ruben Bridgewater) [#26915](https://github.com/nodejs/node/pull/26915)
- [[`2ba58a6d54`](https://github.com/nodejs/node/commit/2ba58a6d54)] - **buffer**: fix concat error message (Ruben Bridgewater) [#27050](https://github.com/nodejs/node/pull/27050)
- [[`a64786f47f`](https://github.com/nodejs/node/commit/a64786f47f)] - **build**: fix inspector dependency resolution (Ben Noordhuis) [#27026](https://github.com/nodejs/node/pull/27026)
- [[`19a30f3b7e`](https://github.com/nodejs/node/commit/19a30f3b7e)] - **build**: fix inspector dependency resolution (Ben Noordhuis) [#27026](https://github.com/nodejs/node/pull/27026)
- [[`ab5dbf9eb0`](https://github.com/nodejs/node/commit/ab5dbf9eb0)] - **build**: only emit download ICU warnings once (Richard Lau) [#27031](https://github.com/nodejs/node/pull/27031)
- [[`7fe43bd81a`](https://github.com/nodejs/node/commit/7fe43bd81a)] - **build**: remove unused label from vcbuild.bat (Ben Noordhuis) [#26901](https://github.com/nodejs/node/pull/26901)
- [[`6cbd6b5d57`](https://github.com/nodejs/node/commit/6cbd6b5d57)] - **build**: fix skipping of flaky tests on Travis (Richard Lau) [#27002](https://github.com/nodejs/node/pull/27002)
- [[`769d12ca9f`](https://github.com/nodejs/node/commit/769d12ca9f)] - **build**: add a `Prepare ccache` job in Travis (Richard Lau) [#27002](https://github.com/nodejs/node/pull/27002)
- [[`d8aaf2e0db`](https://github.com/nodejs/node/commit/d8aaf2e0db)] - **build,meta**: tweak Travis config (Refael Ackermann) [#26969](https://github.com/nodejs/node/pull/26969)
- [[`b64b22377c`](https://github.com/nodejs/node/commit/b64b22377c)] - **build,win**: silence MSVC warning C4129 for V8 (Refael Ackermann) [#27017](https://github.com/nodejs/node/pull/27017)
- [[`23967431f5`](https://github.com/nodejs/node/commit/23967431f5)] - **child_process**: doc deprecate ChildProcess.\_channel (cjihrig) [#26982](https://github.com/nodejs/node/pull/26982)
- [[`4defe47228`](https://github.com/nodejs/node/commit/4defe47228)] - **child_process**: reduce internal usage of public require of util (toshi1127) [#26769](https://github.com/nodejs/node/pull/26769)
- [[`e43dbaaba4`](https://github.com/nodejs/node/commit/e43dbaaba4)] - **console**: remove unreachable code (Rich Trott) [#26906](https://github.com/nodejs/node/pull/26906)
- [[`2b791d8697`](https://github.com/nodejs/node/commit/2b791d8697)] - **crypto**: fix crash of encrypted private key export without cipher (Filip Skokan) [#27041](https://github.com/nodejs/node/pull/27041)
- [[`1d2f4c4c6f`](https://github.com/nodejs/node/commit/1d2f4c4c6f)] - **crypto**: fix crash of encrypted private key export without cipher (Filip Skokan) [#27041](https://github.com/nodejs/node/pull/27041)
- [[`98552f3630`](https://github.com/nodejs/node/commit/98552f3630)] - **crypto**: allow undefined for saltLength and padding (Tobias Nießen) [#26921](https://github.com/nodejs/node/pull/26921)
- [[`db7df0fb12`](https://github.com/nodejs/node/commit/db7df0fb12)] - **deps**: add ARM64 Windows configurations in openssl (Jon Kunkee) [#26001](https://github.com/nodejs/node/pull/26001)
- [[`341eacc949`](https://github.com/nodejs/node/commit/341eacc949)] - **deps**: add ARM64 Windows support in openssl (Shigeki Ohtsu) [#26001](https://github.com/nodejs/node/pull/26001)
- [[`247700f293`](https://github.com/nodejs/node/commit/247700f293)] - **(SEMVER-MINOR)** **deps**: update nghttp2 to 1.37.0 (gengjiawen) [#26990](https://github.com/nodejs/node/pull/26990)
- [[`af3ce38902`](https://github.com/nodejs/node/commit/af3ce38902)] - **dns**: refactor lib/internal/dns/utils.js (Rich Trott) [#27006](https://github.com/nodejs/node/pull/27006)
- [[`ac12109d14`](https://github.com/nodejs/node/commit/ac12109d14)] - **(SEMVER-MINOR)** **dns**: make dns.promises enumerable (cjihrig) [#26592](https://github.com/nodejs/node/pull/26592)
- [[`d3c1de313e`](https://github.com/nodejs/node/commit/d3c1de313e)] - **(SEMVER-MINOR)** **dns**: remove dns.promises experimental warning (cjihrig) [#26592](https://github.com/nodejs/node/pull/26592)
- [[`ff126ea13c`](https://github.com/nodejs/node/commit/ff126ea13c)] - **doc**: assign missed deprecation code (Richard Lau) [#27164](https://github.com/nodejs/node/pull/27164)
- [[`51dad0aaca`](https://github.com/nodejs/node/commit/51dad0aaca)] - **doc**: fix default maxBuffer size (kohta ito) [#22894](https://github.com/nodejs/node/pull/22894)
- [[`7eb73d301d`](https://github.com/nodejs/node/commit/7eb73d301d)] - **doc**: document the 'pause' and 'resume' events (Luigi Pinca) [#26999](https://github.com/nodejs/node/pull/26999)
- [[`57ced2db8c`](https://github.com/nodejs/node/commit/57ced2db8c)] - **doc**: remove unnecessary intro in governance doc (Rich Trott) [#27036](https://github.com/nodejs/node/pull/27036)
- [[`a5314a1af1`](https://github.com/nodejs/node/commit/a5314a1af1)] - **doc**: remove old system_errors (Minwoo Jung) [#27037](https://github.com/nodejs/node/pull/27037)
- [[`2d780f864b`](https://github.com/nodejs/node/commit/2d780f864b)] - **doc**: unify link formatting in buffer.md (Vse Mozhet Byt) [#27030](https://github.com/nodejs/node/pull/27030)
- [[`6e3b6c5e2c`](https://github.com/nodejs/node/commit/6e3b6c5e2c)] - **doc**: unify periods in comments in buffer.md (Vse Mozhet Byt) [#27030](https://github.com/nodejs/node/pull/27030)
- [[`5983cefbf9`](https://github.com/nodejs/node/commit/5983cefbf9)] - **doc**: add notes about negative offsets in buffer.md (Vse Mozhet Byt) [#27030](https://github.com/nodejs/node/pull/27030)
- [[`3567ff1378`](https://github.com/nodejs/node/commit/3567ff1378)] - **doc**: mark optional parameters in buffer.md (Vse Mozhet Byt) [#27030](https://github.com/nodejs/node/pull/27030)
- [[`eeee6360b9`](https://github.com/nodejs/node/commit/eeee6360b9)] - **doc**: add note about Buffer octets integer coercion (Vse Mozhet Byt) [#27030](https://github.com/nodejs/node/pull/27030)
- [[`c3d573d743`](https://github.com/nodejs/node/commit/c3d573d743)] - **doc**: fix error notes in `Buffer.from()` variants (Vse Mozhet Byt) [#27030](https://github.com/nodejs/node/pull/27030)
- [[`e18a0e8087`](https://github.com/nodejs/node/commit/e18a0e8087)] - **doc**: unify number/integer types in buffer.md (Vse Mozhet Byt) [#27030](https://github.com/nodejs/node/pull/27030)
- [[`0d75adcd71`](https://github.com/nodejs/node/commit/0d75adcd71)] - **doc**: add missing types in buffer.md (Vse Mozhet Byt) [#27030](https://github.com/nodejs/node/pull/27030)
- [[`231eff92ca`](https://github.com/nodejs/node/commit/231eff92ca)] - **doc**: fix possible typo in buffer.md (Vse Mozhet Byt) [#27030](https://github.com/nodejs/node/pull/27030)
- [[`f475e79db3`](https://github.com/nodejs/node/commit/f475e79db3)] - **doc**: remove description duplication in buffer.md (Vse Mozhet Byt) [#27030](https://github.com/nodejs/node/pull/27030)
- [[`7b37c65914`](https://github.com/nodejs/node/commit/7b37c65914)] - **doc**: improve the doc of the 'information' event (Luigi Pinca) [#27009](https://github.com/nodejs/node/pull/27009)
- [[`c4b790b62b`](https://github.com/nodejs/node/commit/c4b790b62b)] - **doc**: move "Prints: ..." under the code (simon3000) [#27035](https://github.com/nodejs/node/pull/27035)
- [[`0f08a8e081`](https://github.com/nodejs/node/commit/0f08a8e081)] - **doc**: add information about modules cache behavior (Ruben Bridgewater) [#26971](https://github.com/nodejs/node/pull/26971)
- [[`b88871e80b`](https://github.com/nodejs/node/commit/b88871e80b)] - **doc**: list when promiseResolve hook was added to async_hooks (Thomas Watson) [#26978](https://github.com/nodejs/node/pull/26978)
- [[`7a391961ea`](https://github.com/nodejs/node/commit/7a391961ea)] - **doc**: change code lang and update it with latest Node.js (gengjiawen) [#26987](https://github.com/nodejs/node/pull/26987)
- [[`17cc117f4a`](https://github.com/nodejs/node/commit/17cc117f4a)] - **doc**: update changelog for v10.x LTS (Beth Griggs) [#26931](https://github.com/nodejs/node/pull/26931)
- [[`28efecccd5`](https://github.com/nodejs/node/commit/28efecccd5)] - **doc**: remove "How is an LTS release cut?" section (Rich Trott) [#26955](https://github.com/nodejs/node/pull/26955)
- [[`d76c30c082`](https://github.com/nodejs/node/commit/d76c30c082)] - **doc**: add note about mkdtemp() platform differences (cjihrig) [#26944](https://github.com/nodejs/node/pull/26944)
- [[`4a7a84a6be`](https://github.com/nodejs/node/commit/4a7a84a6be)] - **(SEMVER-MINOR)** **doc**: move dns.promises to stable status (cjihrig) [#26592](https://github.com/nodejs/node/pull/26592)
- [[`25d5198001`](https://github.com/nodejs/node/commit/25d5198001)] - **doc**: change links to https in benchmark guide (gengjiawen) [#26925](https://github.com/nodejs/node/pull/26925)
- [[`a821a96b50`](https://github.com/nodejs/node/commit/a821a96b50)] - **doc**: correct typo: cert.issuerCertificate (Steven R. Loomis)
- [[`17bff5ca0d`](https://github.com/nodejs/node/commit/17bff5ca0d)] - **doc**: remove reference to "credentials object" (Sam Roberts) [#26908](https://github.com/nodejs/node/pull/26908)
- [[`5e64acd66b`](https://github.com/nodejs/node/commit/5e64acd66b)] - **(SEMVER-MINOR)** **embedding**: make `NewIsolate()` API more flexible (Anna Henningsen) [#26525](https://github.com/nodejs/node/pull/26525)
- [[`7671a65dbb`](https://github.com/nodejs/node/commit/7671a65dbb)] - **(SEMVER-MINOR)** **embedding**: refactor public `ArrayBufferAllocator` API (Anna Henningsen) [#26525](https://github.com/nodejs/node/pull/26525)
- [[`c756b84447`](https://github.com/nodejs/node/commit/c756b84447)] - **errors**: make range mandatory in ERR_OUT_OF_RANGE (Ruben Bridgewater) [#26924](https://github.com/nodejs/node/pull/26924)
- [[`3e386a77d5`](https://github.com/nodejs/node/commit/3e386a77d5)] - **(SEMVER-MINOR)** **fs**: remove experimental warning for fs.promises (Anna Henningsen) [#26581](https://github.com/nodejs/node/pull/26581)
- [[`bb9f1cce42`](https://github.com/nodejs/node/commit/bb9f1cce42)] - **fs**: reduce usage of require('util') (toshi1127) [#26783](https://github.com/nodejs/node/pull/26783)
- [[`5a29a94f0e`](https://github.com/nodejs/node/commit/5a29a94f0e)] - **http**: reduce usage of public util (ZYSzys) [#26548](https://github.com/nodejs/node/pull/26548)
- [[`760d089e92`](https://github.com/nodejs/node/commit/760d089e92)] - **inspector**: display error when ToggleAsyncHook fails (Joyee Cheung) [#26859](https://github.com/nodejs/node/pull/26859)
- [[`1b45704c19`](https://github.com/nodejs/node/commit/1b45704c19)] - **inspector**: patch C++ debug options instead of process.\_breakFirstLine (Joyee Cheung) [#26602](https://github.com/nodejs/node/pull/26602)
- [[`100bfc5131`](https://github.com/nodejs/node/commit/100bfc5131)] - **meta**: move ofrobots to TSC emeritus (Ali Ijaz Sheikh) [#27076](https://github.com/nodejs/node/pull/27076)
- [[`5c39687d01`](https://github.com/nodejs/node/commit/5c39687d01)] - **module**: add extra caching layer (Ruben Bridgewater) [#26970](https://github.com/nodejs/node/pull/26970)
- [[`9b27d5eebb`](https://github.com/nodejs/node/commit/9b27d5eebb)] - **module**: add path to the module object (Ruben Bridgewater) [#26970](https://github.com/nodejs/node/pull/26970)
- [[`3263264f43`](https://github.com/nodejs/node/commit/3263264f43)] - **module**: inline try catch (Ruben Bridgewater) [#26970](https://github.com/nodejs/node/pull/26970)
- [[`079368a6ab`](https://github.com/nodejs/node/commit/079368a6ab)] - **module**: fix repl require calling the same file again (Ruben Bridgewater) [#26928](https://github.com/nodejs/node/pull/26928)
- [[`3c9292642d`](https://github.com/nodejs/node/commit/3c9292642d)] - **module**: simpler esm loading (Ruben Bridgewater) [#26974](https://github.com/nodejs/node/pull/26974)
- [[`fd8de13bbe`](https://github.com/nodejs/node/commit/fd8de13bbe)] - **path**: refactor for less indentation (Ruben Bridgewater) [#26917](https://github.com/nodejs/node/pull/26917)
- [[`b62739c85c`](https://github.com/nodejs/node/commit/b62739c85c)] - **path**: remove dead code (Ruben Bridgewater) [#26916](https://github.com/nodejs/node/pull/26916)
- [[`bd006e1002`](https://github.com/nodejs/node/commit/bd006e1002)] - **path**: fix win32 parse regression (Ruben Bridgewater) [#26912](https://github.com/nodejs/node/pull/26912)
- [[`a232cd60dd`](https://github.com/nodejs/node/commit/a232cd60dd)] - **process**: store argv in Environment (Joyee Cheung) [#26945](https://github.com/nodejs/node/pull/26945)
- [[`4d06ef468e`](https://github.com/nodejs/node/commit/4d06ef468e)] - **process**: run RunBootstrapping in CreateEnvironment (Joyee Cheung) [#26788](https://github.com/nodejs/node/pull/26788)
- [[`a03552d246`](https://github.com/nodejs/node/commit/a03552d246)] - **process**: handle --expose-internals during pre-execution (Joyee Cheung) [#26759](https://github.com/nodejs/node/pull/26759)
- [[`75c5d9c5b7`](https://github.com/nodejs/node/commit/75c5d9c5b7)] - **process**: create legacy process properties during pre-execution (Joyee Cheung) [#26517](https://github.com/nodejs/node/pull/26517)
- [[`d4f95091d0`](https://github.com/nodejs/node/commit/d4f95091d0)] - **process**: delay process.argv\[0\] and process.argv0 handling (Joyee Cheung) [#26517](https://github.com/nodejs/node/pull/26517)
- [[`6c40f7f940`](https://github.com/nodejs/node/commit/6c40f7f940)] - **querystring**: simplify stringify method (ZYSzys) [#26591](https://github.com/nodejs/node/pull/26591)
- [[`dbd06088cf`](https://github.com/nodejs/node/commit/dbd06088cf)] - **(SEMVER-MINOR)** **readline**: make Symbol.asyncIterator support stable (Matteo Collina) [#26989](https://github.com/nodejs/node/pull/26989)
- [[`78fad3210c`](https://github.com/nodejs/node/commit/78fad3210c)] - **readline**: replace quadratic regex with linear one (Thomas) [#26778](https://github.com/nodejs/node/pull/26778)
- [[`003e085ab5`](https://github.com/nodejs/node/commit/003e085ab5)] - **report**: add cwd to report (cjihrig) [#27022](https://github.com/nodejs/node/pull/27022)
- [[`755609c682`](https://github.com/nodejs/node/commit/755609c682)] - **src**: prevent crash in TTYWrap::Initialize (Thomas) [#26832](https://github.com/nodejs/node/pull/26832)
- [[`32ec034bdc`](https://github.com/nodejs/node/commit/32ec034bdc)] - **src**: use sizeof(var) instead of sizeof(type) (Ben Noordhuis) [#27038](https://github.com/nodejs/node/pull/27038)
- [[`c537daf391`](https://github.com/nodejs/node/commit/c537daf391)] - **src**: apply clang-tidy rule bugprone-incorrect-roundings (gengjiawen) [#26885](https://github.com/nodejs/node/pull/26885)
- [[`80694949f2`](https://github.com/nodejs/node/commit/80694949f2)] - **src**: elevate v8::Task namespace (Juan José Arboleda) [#26909](https://github.com/nodejs/node/pull/26909)
- [[`aa6a741102`](https://github.com/nodejs/node/commit/aa6a741102)] - **src**: replace c-style cast (gengjiawen) [#26888](https://github.com/nodejs/node/pull/26888)
- [[`f65cb75c74`](https://github.com/nodejs/node/commit/f65cb75c74)] - **src**: remove internal includes from node_crypto.h (Sam Roberts) [#26966](https://github.com/nodejs/node/pull/26966)
- [[`d0ee1a3dbb`](https://github.com/nodejs/node/commit/d0ee1a3dbb)] - **src**: fix warning on mismatched fn signature (Sam Roberts) [#26950](https://github.com/nodejs/node/pull/26950)
- [[`fbdead7f35`](https://github.com/nodejs/node/commit/fbdead7f35)] - **src**: add missing uv_fs_req_cleanup() (cjihrig) [#27004](https://github.com/nodejs/node/pull/27004)
- [[`729e2f242f`](https://github.com/nodejs/node/commit/729e2f242f)] - **src**: implement generic backend for process.env (Anna Henningsen) [#26544](https://github.com/nodejs/node/pull/26544)
- [[`d3840bcf0d`](https://github.com/nodejs/node/commit/d3840bcf0d)] - **src**: allow per-Environment set of env vars (Anna Henningsen) [#26544](https://github.com/nodejs/node/pull/26544)
- [[`e776b013ad`](https://github.com/nodejs/node/commit/e776b013ad)] - **src**: do not call into JS in the maxAsyncCallStackDepthChanged interrupt (Joyee Cheung) [#26935](https://github.com/nodejs/node/pull/26935)
- [[`0427354a98`](https://github.com/nodejs/node/commit/0427354a98)] - **src**: delete useless code in cares_wrap.cc (gengjiawen) [#26815](https://github.com/nodejs/node/pull/26815)
- [[`6bfb17f528`](https://github.com/nodejs/node/commit/6bfb17f528)] - **src**: fix task release in cares_wrap.cc (gengjiawen) [#26815](https://github.com/nodejs/node/pull/26815)
- [[`c969731755`](https://github.com/nodejs/node/commit/c969731755)] - **src**: use deleted function for class BaseObject (gengjiawen) [#26815](https://github.com/nodejs/node/pull/26815)
- [[`c824127756`](https://github.com/nodejs/node/commit/c824127756)] - **src**: delete unused field in class ModuleWrap (gengjiawen) [#26815](https://github.com/nodejs/node/pull/26815)
- [[`ea7e2c0666`](https://github.com/nodejs/node/commit/ea7e2c0666)] - **src**: tidy up include headers in env.cc (gengjiawen) [#26815](https://github.com/nodejs/node/pull/26815)
- [[`c1def0701e`](https://github.com/nodejs/node/commit/c1def0701e)] - **src**: delete unreachable code in heap_utils.cc (gengjiawen) [#26815](https://github.com/nodejs/node/pull/26815)
- [[`c51cc9e85b`](https://github.com/nodejs/node/commit/c51cc9e85b)] - **src**: apply clang-tidy rule modernize-make-unique (gengjiawen) [#26493](https://github.com/nodejs/node/pull/26493)
- [[`ab70c96a79`](https://github.com/nodejs/node/commit/ab70c96a79)] - **src**: refactor coverage connection (Joyee Cheung) [#26513](https://github.com/nodejs/node/pull/26513)
- [[`63e7cc7694`](https://github.com/nodejs/node/commit/63e7cc7694)] - **src**: forbid access to CLI options before bootstrapping is done (Joyee Cheung) [#26476](https://github.com/nodejs/node/pull/26476)
- [[`e6c1ad5901`](https://github.com/nodejs/node/commit/e6c1ad5901)] - **src**: fix warnings around node_options (Refael Ackermann) [#26280](https://github.com/nodejs/node/pull/26280)
- [[`62f904974d`](https://github.com/nodejs/node/commit/62f904974d)] - **src**: refactor node options parsers to mitigate MSVC bug (Refael Ackermann) [#26280](https://github.com/nodejs/node/pull/26280)
- [[`b29afa212a`](https://github.com/nodejs/node/commit/b29afa212a)] - **(SEMVER-MINOR)** **stream**: make Symbol.asyncIterator support stable (Matteo Collina) [#26989](https://github.com/nodejs/node/pull/26989)
- [[`ea47189b40`](https://github.com/nodejs/node/commit/ea47189b40)] - **stream**: do not unconditionally call `_read()` on `resume()` (Anna Henningsen) [#26965](https://github.com/nodejs/node/pull/26965)
- [[`b359a7a7e5`](https://github.com/nodejs/node/commit/b359a7a7e5)] - **test**: make module test pass with NODE_PENDING_DEPRECATION (Anna Henningsen) [#27019](https://github.com/nodejs/node/pull/27019)
- [[`1b2a07855a`](https://github.com/nodejs/node/commit/1b2a07855a)] - **test**: remove test-trace-events-api-worker-disabled from flaky (Rich Trott) [#27020](https://github.com/nodejs/node/pull/27020)
- [[`ecac6547c0`](https://github.com/nodejs/node/commit/ecac6547c0)] - **test**: move test that creates 1Gb file to pummel (Rich Trott) [#27053](https://github.com/nodejs/node/pull/27053)
- [[`35119d60d9`](https://github.com/nodejs/node/commit/35119d60d9)] - **test**: add IPv6 brackets but no port to test-dns (Rich Trott) [#27006](https://github.com/nodejs/node/pull/27006)
- [[`8258f0704d`](https://github.com/nodejs/node/commit/8258f0704d)] - **test**: remove unused triggerAsyncId param in test (Juan José Arboleda) [#26800](https://github.com/nodejs/node/pull/26800)
- [[`06dce392ba`](https://github.com/nodejs/node/commit/06dce392ba)] - **test**: fix error code typo (cjihrig) [#27024](https://github.com/nodejs/node/pull/27024)
- [[`e5181f8dc4`](https://github.com/nodejs/node/commit/e5181f8dc4)] - **test**: simplify for loop in test-buffer-zero-fill-cli.js (Juan José Arboleda) [#26799](https://github.com/nodejs/node/pull/26799)
- [[`9330d7e4bf`](https://github.com/nodejs/node/commit/9330d7e4bf)] - **test**: add known_issues test for fs.copyFile() (Rich Trott) [#26939](https://github.com/nodejs/node/pull/26939)
- [[`fd6381b056`](https://github.com/nodejs/node/commit/fd6381b056)] - **test**: remove test-path-parse-6229.js from known issues (Ruben Bridgewater) [#26913](https://github.com/nodejs/node/pull/26913)
- [[`edad9afaf8`](https://github.com/nodejs/node/commit/edad9afaf8)] - **test**: move hasCrypto check (Ruben Bridgewater) [#26858](https://github.com/nodejs/node/pull/26858)
- [[`2ef1bd97c6`](https://github.com/nodejs/node/commit/2ef1bd97c6)] - **test**: do not require flags when executing a file (Ruben Bridgewater) [#26858](https://github.com/nodejs/node/pull/26858)
- [[`a1cf7453d8`](https://github.com/nodejs/node/commit/a1cf7453d8)] - **test**: refactor path parse test (Ruben Bridgewater) [#26912](https://github.com/nodejs/node/pull/26912)
- [[`80e845e787`](https://github.com/nodejs/node/commit/80e845e787)] - **test**: add test about unencrypted PKCS#8 private key for RSA (Daiki Ihara) [#26898](https://github.com/nodejs/node/pull/26898)
- [[`03bd649655`](https://github.com/nodejs/node/commit/03bd649655)] - **test**: show stderr on v8 coverage test failures (Joyee Cheung) [#26513](https://github.com/nodejs/node/pull/26513)
- [[`b24e45ab8d`](https://github.com/nodejs/node/commit/b24e45ab8d)] - **(SEMVER-MINOR)** **timers**: deprecate active() and \_unrefActive() (Jeremiah Senkpiel) [#26760](https://github.com/nodejs/node/pull/26760)
- [[`3ff3070442`](https://github.com/nodejs/node/commit/3ff3070442)] - **tools**: fix `test.py --time` (Richard Lau) [#27007](https://github.com/nodejs/node/pull/27007)
- [[`7cbe1214d0`](https://github.com/nodejs/node/commit/7cbe1214d0)] - **tools**: update ESLint to 5.16.0 (cjihrig) [#27005](https://github.com/nodejs/node/pull/27005)
- [[`dc9ce86aaa`](https://github.com/nodejs/node/commit/dc9ce86aaa)] - **tools**: update dependencies in lint-md-cli-rollup (Daijiro Wachi) [#26889](https://github.com/nodejs/node/pull/26889)
- [[`8798db3bf3`](https://github.com/nodejs/node/commit/8798db3bf3)] - **url**: add ws: and wss: to slashedProtocol set (Luigi Pinca) [#26941](https://github.com/nodejs/node/pull/26941)
- [[`12737b3789`](https://github.com/nodejs/node/commit/12737b3789)] - **util**: `inspect()` should not exceed `breakLength` (Ruben Bridgewater) [#26914](https://github.com/nodejs/node/pull/26914)
- [[`0f615d4216`](https://github.com/nodejs/node/commit/0f615d4216)] - **util**: add subclass and null prototype support for errors in inspect (Ruben Bridgewater) [#26923](https://github.com/nodejs/node/pull/26923)
- [[`1aa6e993e3`](https://github.com/nodejs/node/commit/1aa6e993e3)] - **util**: fix map entries inspection (Ruben Bridgewater) [#26918](https://github.com/nodejs/node/pull/26918)
- [[`1b08e622aa`](https://github.com/nodejs/node/commit/1b08e622aa)] - **util**: improve proxy inspection (Ruben Bridgewater) [#26919](https://github.com/nodejs/node/pull/26919)
- [[`21486e5c97`](https://github.com/nodejs/node/commit/21486e5c97)] - **util**: extract uncurryThis function for reuse (ZYSzys) [#23081](https://github.com/nodejs/node/pull/23081)
- [[`169f3f7166`](https://github.com/nodejs/node/commit/169f3f7166)] - **util**: require `isNativeError` from internalBinding (ZYSzys) [#23081](https://github.com/nodejs/node/pull/23081)
- [[`8bd7909d00`](https://github.com/nodejs/node/commit/8bd7909d00)] - **worker**: use copy of process.env (Anna Henningsen) [#26544](https://github.com/nodejs/node/pull/26544)
- [[`682b410581`](https://github.com/nodejs/node/commit/682b410581)] - **worker**: allow execArgv and eval in combination (Anna Henningsen) [#26533](https://github.com/nodejs/node/pull/26533)
- [[`5d9f819a14`](https://github.com/nodejs/node/commit/5d9f819a14)] - **worker**: remove usage of require('util') in worker_thread.js (toshi1127) [#26814](https://github.com/nodejs/node/pull/26814)
- [[`44450efa6b`](https://github.com/nodejs/node/commit/44450efa6b)] - **worker**: remove usage of require('util') (toshi1127) [#26810](https://github.com/nodejs/node/pull/26810)

Windows 32-bit Installer: https://nodejs.org/dist/v11.14.0/node-v11.14.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v11.14.0/node-v11.14.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v11.14.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v11.14.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v11.14.0/node-v11.14.0.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v11.14.0/node-v11.14.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v11.14.0/node-v11.14.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v11.14.0/node-v11.14.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v11.14.0/node-v11.14.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v11.14.0/node-v11.14.0-aix-ppc64.tar.gz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v11.14.0/node-v11.14.0-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v11.14.0/node-v11.14.0-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v11.14.0/node-v11.14.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v11.14.0/node-v11.14.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v11.14.0/node-v11.14.0.tar.gz \
Other release files: https://nodejs.org/dist/v11.14.0/ \
Documentation: https://nodejs.org/docs/v11.14.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

fd0db076d7b7b4fe93617906b0dc9af3c7dc88bcdebb683a3a7741d0b0746018  node-v11.14.0-aix-ppc64.tar.gz
37b8db4f6700a4b5aa3b6d4283538753953f6293aff99866aa9a81038848a4d9  node-v11.14.0-darwin-x64.tar.gz
bbfc442f30675279c7222924b45c567771604c2c3a08187d48eab136bac49cca  node-v11.14.0-darwin-x64.tar.xz
4ebb6ba969ba61ee3b089f9639d06225259291d200a738f314495c18671ced57  node-v11.14.0-headers.tar.gz
c2bfa723dd9e01dd88e2eafdccb3d45104ef49d73a69e40b191bfdc6942135fd  node-v11.14.0-headers.tar.xz
9ab9a285fe4b24809f787e4cb4b48f6f482246902981d10fa604fdb1c90b16b7  node-v11.14.0-linux-arm64.tar.gz
3d463f3b48e1060027f8a552657de6f3ee4724d718d546aa4ea5c301ee4ed083  node-v11.14.0-linux-arm64.tar.xz
a22ee2f5e4fe8b1d88759690ef79f47288416e191736b5693e7e784ebffe1252  node-v11.14.0-linux-armv6l.tar.gz
2c29df2d06bb8e7793a448a52969eddc1e8466286a805a86d3a3456335dac238  node-v11.14.0-linux-armv6l.tar.xz
f93f5615726ac1b5647840ea69c53d8f9a423970832b69214a3fb35635182bba  node-v11.14.0-linux-armv7l.tar.gz
64428edb86eacfa233d09ab40aeb04f0577021caee2c57719afc4353b106db66  node-v11.14.0-linux-armv7l.tar.xz
10384eba1300cfd04339cbc8a93f8bafeb91639c4e60f5830abc20f325ce2a52  node-v11.14.0-linux-ppc64le.tar.gz
b6dfd2d7df648f0015c94a6437eddc3c668e4e96a3caa3eb592b2eb7ef3324a5  node-v11.14.0-linux-ppc64le.tar.xz
139876fcd7eec99cec98f8d74ed74abcc8daeac5a380012f88984c33992dfab5  node-v11.14.0-linux-s390x.tar.gz
efc61e9943c978484a89223384dec8f4f97ecfa7d6a23717a5606b64b8a4e3bf  node-v11.14.0-linux-s390x.tar.xz
0d5c078137c992a919898c8fda7e5af40118f355bb938fccfd5aecddaf2dc123  node-v11.14.0-linux-x64.tar.gz
89e2cb0effa9bf075ee8bb6d92a6a50f7ccfdad03dd687f52ecdfab91230f9a1  node-v11.14.0-linux-x64.tar.xz
6de783e429a3d5b7237dbbb4f9c14b5a7132fc52a0beae631740b2b7f3edf596  node-v11.14.0.pkg
fcda52d0046a0481f227a17833f5204744d54324cd4032e66e710d8c5562cc1f  node-v11.14.0-sunos-x64.tar.gz
eb453e5a6f3502f32ae13ec119eb20b7416f1094e44ea340c53530b737b3d804  node-v11.14.0-sunos-x64.tar.xz
b903ba6dc8131b9357e7c5e26967a0f48aa460c708154244a6348d49a7d8ff9a  node-v11.14.0.tar.gz
9df61d885765fd56148807092d019f07ad593c85faab2c7caaad21b8e9f66de7  node-v11.14.0.tar.xz
4d112525851b429b387dd69c9c7042a30190be3b1563cf30ac685260836917d5  node-v11.14.0-win-x64.7z
58a5d3316c04335ff3da2ce66b89f3cce86b1d1ca878d1d6d68478160c66dc12  node-v11.14.0-win-x64.zip
6f210fd61a3c689bef77ff242c5250c514988f259dbc4d6fdab6e3cb3572d0d7  node-v11.14.0-win-x86.7z
fb68605de7968c71b0dce9859e92ce61f41ecd2947ec18684cde00e201a866ca  node-v11.14.0-win-x86.zip
f83bade48f37f75d3c47a48d090c71439a8b771ec150f9f377d924e1fd516239  node-v11.14.0-x64.msi
f3a4314ebb11c59e7312f634e027b7447d76ede2d06f4ac744beffbf08df9af6  node-v11.14.0-x86.msi
38a62eda6ac2b70c7f27949ea1541d8b900d9fb429be74ab9ef5b97947e094e2  win-x64/node.exe
4ae8ea43f9c087d7b72d732b8987044704e6b0f115eea8ea2389198cce05e307  win-x64/node.lib
0438a12565dfef14d93d622f72b911ddd8ff941c61ca27130818f6e76fcc60d4  win-x64/node_pdb.7z
7617a2412f4e4827791042d8e60c07712297291ed4fc1349dc57ffe99eab3711  win-x64/node_pdb.zip
fc3021ac552d93f29a982ed82daead784cec95874f006a278fe35bef76ea51bf  win-x86/node.exe
017f8cf44e31ae4c9f3c594d45c2b5bc753c486c6242dcd72f00ab7da6d055e6  win-x86/node.lib
845e81f207f3ef317d0cbc0695f7445e64a18d730666b130a4004e548608214b  win-x86/node_pdb.7z
51fec2fe650a36f8a956f9485a815730e4874385bacf9264c058771bcdc52f45  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQEzBAEBCAAdFiEETtd49TnjY0x3nIfG1wYoSKGrAFwFAlyvcyIACgkQ1wYoSKGr
AFwNDQf+LtWGNH8RAq7tRExZ/U8PeULyu5AhJPnR3+VmfaqF8f5///K7FifEh970
YskeMRku4Q+DW+yo7XCBNGZ+nhcUN2E+JkkSqrY/h/fer4hByJ7nIzmMghqHS7o1
zbtBmoro4pSF6kUpDAmBzBOuNiBYbm4xjoU2JC6J1+DSbderKv5WcaCamE0L7t7E
3v8Xugq78wGLA/JU7v5D8Hu0LaqxFO67NxGVrRzGMNb6o6+cXXUwFKqy3v6lP572
8r9kCTLT6a3JFIg7gwP3Bn+UAfjpid1lbrjAtsOXjlYQ3vWisW9t1SSvq0qxAGat
kwGjX+XBO/mHGMUfBl7sA4y+kgYEcw==
=0ol6
-----END PGP SIGNATURE-----

```
