---
date: '2023-02-16T22:21:38.949Z'
category: release
title: Node v19.6.1 (Current)
layout: blog-post
author: <PERSON>
---

### Notable Changes

The following CVEs are fixed in this release:

- **[CVE-2023-23919](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2023-23919)**: OpenSSL errors not cleared in error stack (Medium)
- **[CVE-2023-23918](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2023-23918)**: Experimental Policies bypass via `process.mainModule.require`(High)
- **[CVE-2023-23920](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2023-23920)**: Insecure loading of ICU data through ICU_DATA environment variable (Low)

More detailed information on each of the vulnerabilities can be found in [February 2023 Security Releases](/blog/vulnerability/february-2023-security-releases/) blog post.

This security release includes OpenSSL security updates as outlined in the recent
[OpenSSL security advisory](https://www.openssl.org/news/secadv/20230207.txt) and `undici` security update.

### Commits

- \[[`97d9d55d2f`](https://github.com/nodejs/node/commit/97d9d55d2f)] - **build**: build ICU with ICU_NO_USER_DATA_OVERRIDE (RafaelGSS) [nodejs-private/node-private#374](https://github.com/nodejs-private/node-private/pull/374)
- \[[`8ac90e6372`](https://github.com/nodejs/node/commit/8ac90e6372)] - **crypto**: clear OpenSSL error on invalid ca cert (RafaelGSS) [nodejs-private/node-private#368](https://github.com/nodejs-private/node-private/pull/368)
- \[[`10a4c47e3a`](https://github.com/nodejs/node/commit/10a4c47e3a)] - **deps**: update undici to 5.19.1 (Node.js GitHub Bot) [#46634](https://github.com/nodejs/node/pull/46634)
- \[[`b10fc75e4a`](https://github.com/nodejs/node/commit/b10fc75e4a)] - **deps**: update undici to 5.18.0 (Node.js GitHub Bot) [#46502](https://github.com/nodejs/node/pull/46502)
- \[[`e9b64ea8b9`](https://github.com/nodejs/node/commit/e9b64ea8b9)] - **deps**: update undici to 5.17.1 (Node.js GitHub Bot) [#46502](https://github.com/nodejs/node/pull/46502)
- \[[`66a24cec47`](https://github.com/nodejs/node/commit/66a24cec47)] - **deps**: cherry-pick Windows ARM64 fix for openssl (Richard Lau) [#46573](https://github.com/nodejs/node/pull/46573)
- \[[`d8559aa6f5`](https://github.com/nodejs/node/commit/d8559aa6f5)] - **deps**: update archs files for quictls/openssl-3.0.8+quic (RafaelGSS) [#46573](https://github.com/nodejs/node/pull/46573)
- \[[`dc477f547d`](https://github.com/nodejs/node/commit/dc477f547d)] - **deps**: upgrade openssl sources to quictls/openssl-3.0.8+quic (RafaelGSS) [#46573](https://github.com/nodejs/node/pull/46573)
- \[[`2aae197670`](https://github.com/nodejs/node/commit/2aae197670)] - **lib**: makeRequireFunction patch when experimental policy (RafaelGSS) [nodejs-private/node-private#358](https://github.com/nodejs-private/node-private/pull/358)
- \[[`6d17b693ec`](https://github.com/nodejs/node/commit/6d17b693ec)] - **policy**: makeRequireFunction on mainModule.require (RafaelGSS) [nodejs-private/node-private#358](https://github.com/nodejs-private/node-private/pull/358)

Windows 32-bit Installer: https://nodejs.org/dist/v19.6.1/node-v19.6.1-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v19.6.1/node-v19.6.1-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v19.6.1/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v19.6.1/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v19.6.1/node-v19.6.1.pkg \
macOS Apple Silicon 64-bit Binary: https://nodejs.org/dist/v19.6.1/node-v19.6.1-darwin-arm64.tar.gz \
macOS Intel 64-bit Binary: https://nodejs.org/dist/v19.6.1/node-v19.6.1-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v19.6.1/node-v19.6.1-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v19.6.1/node-v19.6.1-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v19.6.1/node-v19.6.1-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v19.6.1/node-v19.6.1-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v19.6.1/node-v19.6.1-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v19.6.1/node-v19.6.1-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v19.6.1/node-v19.6.1.tar.gz \
Other release files: https://nodejs.org/dist/v19.6.1/ \
Documentation: https://nodejs.org/docs/v19.6.1/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

0b84f9d4189adf1b3d955384db1aba971b4e5ac2dedadeaffa3c76b0efa8fade  node-v19.6.1-aix-ppc64.tar.gz
f95951571baaf2180ce41eb5f3e878f6580bc5b203d79fedb479bf5a1b147138  node-v19.6.1-darwin-arm64.tar.gz
bfc47b79ab01aa3e57c32011d4537aed97cb194775a89519c70b79f346cfed84  node-v19.6.1-darwin-arm64.tar.xz
a79442a282dec5a6bb1cb73adb926dbc4aa97fa3960193b9ecf56cc76fd6c114  node-v19.6.1-darwin-x64.tar.gz
e9c5bfd0a6893f555220086beee8067326d7c3dd41e8b6adccb90ecdea87c1b4  node-v19.6.1-darwin-x64.tar.xz
6f52107f83b6485a335dc4d72ae955d4ed289786a4628b09ac1f06d56d64f8dc  node-v19.6.1-headers.tar.gz
746ca43484b4ca74158c01e2f1652911c0a772c80587397ce7e7b9d9e8c86aee  node-v19.6.1-headers.tar.xz
1787b3795c64eac44ca6b88fe5bf29c7e7b72816ee7c1df1c64d6c323f2c1f71  node-v19.6.1-linux-arm64.tar.gz
a0ce4a79d1b43a401ba4158c354792b38aafc2f23d8b5d0b50812c7063b55c17  node-v19.6.1-linux-arm64.tar.xz
e9148d7110cc34ea9c22d9cd42eb3fd1876b1c203d72440e095ed4c0152b52d4  node-v19.6.1-linux-armv7l.tar.gz
b3814279cf48b20e62e55f4a68e9c9187397f7357cb1b9415c35a9d8402d230a  node-v19.6.1-linux-armv7l.tar.xz
4b53f9e36f81794d8ac803fed404f261c0a459092a5077e1f600f0bf67e22158  node-v19.6.1-linux-ppc64le.tar.gz
ce0e07bdd036d8490c1d3ce4376d91a220ee99275762e6f5424fb5817d22fdfa  node-v19.6.1-linux-ppc64le.tar.xz
67c86271321b0051511870c3d1c078f75400458c0ce7cb295bf8030cc633cf1e  node-v19.6.1-linux-s390x.tar.gz
ab999123afd6b44c8d5235300e6fb00fc8aedfebfa2158489079519dd9ca1dd6  node-v19.6.1-linux-s390x.tar.xz
c6a64d49003861bf465a9ab8e876d5c13c59f1df4507df7cf5dc8ae6e48edf9d  node-v19.6.1-linux-x64.tar.gz
2b11e02f29772b7ec475854bb40bc46efc0d380d4245e2480db56455c5120c3e  node-v19.6.1-linux-x64.tar.xz
18dc168c79ea5f3ea50a873581f46f866079e44de2b54fd6805a79784b91682d  node-v19.6.1.pkg
e44e67a4dc6a6a3574b8759326319de5062412343623eb6f8e9e9d1c2ef8c0ea  node-v19.6.1.tar.gz
7710e6c2851c956be4926fc2540678f3fddf8e6d3826ea3aa9b8c28d6ea89a9b  node-v19.6.1.tar.xz
87a6dce8ebdfebbe1adeb2fc9c6a7c4a91848b7c18c955b0670e9b14110678e0  node-v19.6.1-win-x64.7z
b3716a822786936879332abc16bc6df8bac0775dfdd4777268ad4f08d7f9aa8c  node-v19.6.1-win-x64.zip
5bad5675a6bbcf6ad3bf3cb6b2eb7b916b58ddc869800f3fdf7039faa187d780  node-v19.6.1-win-x86.7z
f1b46dab2af773e28582d36ec373dc90165639daca0f813fc8b2e25543222fe2  node-v19.6.1-win-x86.zip
51142803caf327801152f41890ba9ad9625e674cee75d3ff73d21366557faaa2  node-v19.6.1-x64.msi
19da4565d260557f1e31738793cd4e0fec34568bd3a58d211433446e5046d5b5  node-v19.6.1-x86.msi
386d0e2c3bbf87ef6ee59bc6e6d2aa6c6e7ea271805dab33e1130deea1a0ba2e  win-x64/node.exe
dccef7431ad9277c820827b7d5b2e63db6724b9713f7765e05a3125b70713f05  win-x64/node.lib
4305f2fbea5047cedf71d45355ca636a1ed675746f01349c4c8244f9be44fb6f  win-x64/node_pdb.7z
3c5f2e53784fbad2c82bed4c6f58a889b4ef7932504ea6a9db93e6e3a741eec7  win-x64/node_pdb.zip
a3a11b277b157ec631f92615288dd1c06e1bd9502b006308055a1b4b8a8a9ed4  win-x86/node.exe
d1506c2a6dc8af7d5fdf916f170bf68f7b7f9a5f3c7cf0b52b45cf40966f472f  win-x86/node.lib
c008653f941c08b95b58c3f2785fedab0c4ef4c50b963738ac4b767084f5ce90  win-x86/node_pdb.7z
6b9d10156f4d35b76ce89536deb2021767b5181f83fb60920e0712bddc693bab  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQGzBAEBCAAdFiEEiQwI24V5Fi/uDfnbi+q0389VXvQFAmPurAgACgkQi+q0389V
XvS0BAwAjGTSiwTZsifTgB9JImHBBJ+n+2wbJN13xJ6dYbpNFqcNtrVnE++jsghX
SXJpDBQj9rqAb4MuMBeoQqqkCFipixus9SgNX0TNIxXp87IjCKc5ZkEcrboVLdXo
spKvZDbvu32rUwQsX2B88PAe+/E0mS2Ykuhl5atfHEeq1phvAjdA/p7TE8q23m6n
kAhjqqq7PDC840GBOW2XaptGAs6cUy6qTBMXpYYwp1OJEl79B4K9cfFEt53s723H
pVtRzePyVXd3e7uDuLU4mkyiznsLGPaRdxRCsovcOH1cl33tybsXnadl233G6yxU
5TMvD9+4IYha/TiasUP0uODVcwBzKY8XFrIUS6qEReraJxBfUNiaKbJGqPCCOCPC
o4rTX10xFKYwsj42Gk9/KbJv+6SWOo6+DFeFSckmj16qDKkj3NOcScJ6u5WmNkdu
4fZmbWad+sJjIU4ItR/5/OIXOPKuBA0k78MJdrpCNvWidjpkTotqyAV5y+32GCBs
Dyg9h3Y+
=fsRY
-----END PGP SIGNATURE-----

```
