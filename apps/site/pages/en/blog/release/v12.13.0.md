---
date: '2019-10-21T09:21:21.812Z'
category: release
title: Node v12.13.0 (LTS)
layout: blog-post
author: <PERSON><PERSON><PERSON>
---

This release marks the transition of Node.js 12.x into Long Term Support (LTS)
with the codename 'Erbium'. The 12.x release line now moves into "Active LTS"
and will remain so until October 2020. After that time, it will move into
"Maintenance" until end of life in April 2022.

### Notable changes

npm was updated to 6.12.0. It now includes a version of `node-gyp` that
supports Python 3 for building native modules.

### Commits

- [[`b59209b118`](https://github.com/nodejs/node/commit/b59209b118)] - **deps**: update npm to 6.12.0 (isaacs) [#29885](https://github.com/nodejs/node/pull/29885)
- [[`1dde617491`](https://github.com/nodejs/node/commit/1dde617491)] - **doc**: fix --enable-source-maps flag in v12.12.0 changelog (Unlocked) [#29960](https://github.com/nodejs/node/pull/29960)
- [[`e5e2dfabdc`](https://github.com/nodejs/node/commit/e5e2dfabdc)] - **doc**: nest code fence under unordered list (Nick Schonning) [#29915](https://github.com/nodejs/node/pull/29915)
- [[`5b0c993d4c`](https://github.com/nodejs/node/commit/5b0c993d4c)] - **doc**: remove double word "where" (Nick Schonning) [#29915](https://github.com/nodejs/node/pull/29915)
- [[`ad318c6cec`](https://github.com/nodejs/node/commit/ad318c6cec)] - **doc**: add brackets to implicit markdown links (Nick Schonning) [#29911](https://github.com/nodejs/node/pull/29911)
- [[`3155ab4134`](https://github.com/nodejs/node/commit/3155ab4134)] - **doc**: use the WHATWG URL API in http code examples (Thomas Watson) [#29917](https://github.com/nodejs/node/pull/29917)
- [[`b916ea3010`](https://github.com/nodejs/node/commit/b916ea3010)] - **doc**: escape brackets not used as markdown reference links (Nick Schonning) [#29809](https://github.com/nodejs/node/pull/29809)
- [[`f3bf8be11c`](https://github.com/nodejs/node/commit/f3bf8be11c)] - **doc**: correct typos in security release process (Nick Schonning) [#29822](https://github.com/nodejs/node/pull/29822)
- [[`25fa2066a2`](https://github.com/nodejs/node/commit/25fa2066a2)] - **doc**: indent code fence under list item (Nick Schonning) [#29822](https://github.com/nodejs/node/pull/29822)
- [[`f3842892dd`](https://github.com/nodejs/node/commit/f3842892dd)] - **doc**: return type is number (exoego) [#29828](https://github.com/nodejs/node/pull/29828)
- [[`cbd12518d4`](https://github.com/nodejs/node/commit/cbd12518d4)] - **doc**: add note about forwarding stream options (Robert Nagy) [#29857](https://github.com/nodejs/node/pull/29857)
- [[`7683aa0bfb`](https://github.com/nodejs/node/commit/7683aa0bfb)] - **doc**: set module version 72 to node 12 (Gerhard Stoebich) [#29877](https://github.com/nodejs/node/pull/29877)
- [[`f58fe5099a`](https://github.com/nodejs/node/commit/f58fe5099a)] - **doc**: fix tls version values (Tobias Nießen) [#29839](https://github.com/nodejs/node/pull/29839)
- [[`8ebc94562c`](https://github.com/nodejs/node/commit/8ebc94562c)] - **fs**: do not emit 'finish' before 'open' on write empty file (Robert Nagy) [#29930](https://github.com/nodejs/node/pull/29930)
- [[`50f066087e`](https://github.com/nodejs/node/commit/50f066087e)] - **test**: do not force the process to exit (Luigi Pinca) [#29923](https://github.com/nodejs/node/pull/29923)
- [[`44c581ef0b`](https://github.com/nodejs/node/commit/44c581ef0b)] - **test**: add more recursive fs.rmdir() tests (Maria Paktiti) [#29815](https://github.com/nodejs/node/pull/29815)
- [[`fc5334513c`](https://github.com/nodejs/node/commit/fc5334513c)] - **test**: remove unnecessary --expose-internals flags (Anna Henningsen) [#29886](https://github.com/nodejs/node/pull/29886)

Windows 32-bit Installer: https://nodejs.org/dist/v12.13.0/node-v12.13.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v12.13.0/node-v12.13.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v12.13.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v12.13.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v12.13.0/node-v12.13.0.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v12.13.0/node-v12.13.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v12.13.0/node-v12.13.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v12.13.0/node-v12.13.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v12.13.0/node-v12.13.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v12.13.0/node-v12.13.0-aix-ppc64.tar.gz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v12.13.0/node-v12.13.0-sunos-x64.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v12.13.0/node-v12.13.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v12.13.0/node-v12.13.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v12.13.0/node-v12.13.0.tar.gz \
Other release files: https://nodejs.org/dist/v12.13.0/ \
Documentation: https://nodejs.org/docs/v12.13.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

c5a07d2915a787e8f73c987e8263bb33b453a854f7fba3c8873421be5b4d53b6  node-v12.13.0-aix-ppc64.tar.gz
49a7374670a111b033ce16611b20fd1aafd3296bbc662b184fe8fb26a29c22cc  node-v12.13.0-darwin-x64.tar.gz
d3a2cda4a4088b5f11985795b943fb34690ff3cf6a71aae715dac68a62c4725f  node-v12.13.0-darwin-x64.tar.xz
40a3b4e310d1c7c011e0d4af32695a91b8aad55611ad23d7c510fd2a108d35d7  node-v12.13.0-headers.tar.gz
99708ba426925bdc7dbf0668b60560f3a57d5b00778d6bad3127c54a6ecf7bc1  node-v12.13.0-headers.tar.xz
92371c7f1edd384a8acb0d2b9f2deac76e911588669b71de9f6453012196c970  node-v12.13.0-linux-arm64.tar.gz
d65b3ce27639f15ae22941e3ff98a1c900aa9049fcc15518038615b0676037d5  node-v12.13.0-linux-arm64.tar.xz
c8bb1fca0712f360eaeeeab064426f8fb6f9af50144658aa1b50c9703fc7f680  node-v12.13.0-linux-armv7l.tar.gz
961af6a5c4656967cc69198ea354d9ca77606abae1d1c2ae60505a5b06f54cde  node-v12.13.0-linux-armv7l.tar.xz
5943b35744921137078a3af71cda2abdc28372adbba7ccb138e840c9bbcfcb43  node-v12.13.0-linux-ppc64le.tar.gz
80e59fc569848e9509379bad2717e9d4fd528997277b055ee3c76652f584ce4e  node-v12.13.0-linux-ppc64le.tar.xz
43958efba7932f68a8c4fd6aa32afc588813de4dd015a9de3c28dc9dcc0c3d0d  node-v12.13.0-linux-s390x.tar.gz
a4d6518bd90dd4380a4c7f2c6ff9b78bc57f53ec34fbf03dc15cb47738621f13  node-v12.13.0-linux-s390x.tar.xz
c69671c89d0faa47b64bd5f37079e4480852857a9a9366ee86cdd8bc9670074a  node-v12.13.0-linux-x64.tar.gz
7a57ef2cb3036d7eacd50ae7ba07245a28336a93652641c065f747adb2a356d9  node-v12.13.0-linux-x64.tar.xz
2e4d999e3a3123c97f45f5d401486459fb4bbac9c619c2d7505bc2fa7aa69f42  node-v12.13.0.pkg
15d83c78d100f7705f1819531cc37d51722bd328148df73b29fcbf29e2b31d80  node-v12.13.0-sunos-x64.tar.gz
bcc65a629b52299fa48aac74073c8212002439cf151961918eb247c8a6aad450  node-v12.13.0-sunos-x64.tar.xz
2e5321e095fe673a3ab936cf77faf8c983cba62f27a9fbd00530a7edb739a040  node-v12.13.0.tar.gz
a82b1541cf670318a0102c32e06f296662b5ccccae764c1f32be4a3cf038bef6  node-v12.13.0.tar.xz
ca6cb0f7b0b8656f52aeb1d6e01726909a53ae4fc39076da1b3f66ab82e89a14  node-v12.13.0-win-x64.7z
6f920cebeecb4957b4ef0def6d9b04c49d4582864f8d1a207ce8d0665865781a  node-v12.13.0-win-x64.zip
38b6c8d1320646e50a2772cf1df8e700ff7a4021275415f30fd03c977f726079  node-v12.13.0-win-x86.7z
ca081dd9f0f3c686c1320551b8c8a3a6377cb60e59cf97e6dc4885a784f0fee7  node-v12.13.0-win-x86.zip
b6a92b7b6097aefa0d30d092d33f443bed8dba0e2a65bef2b920564373738c84  node-v12.13.0-x64.msi
c2a4812763056bd7f5a56883db0884171965d6c21525964a60b43e745a993297  node-v12.13.0-x86.msi
442d5b9cfe34d5d09b619eed2e982650cdc5f26937c6f371c084f3e1bc840efd  win-x64/node.exe
f359d9b3ebad0748cf1713990da39f8363dbc1df899d02aead3f38b6730c9821  win-x64/node.lib
931c6063740c13053f6885bfba19757988218596a4eb2738c85c17b4e85e8da8  win-x64/node_pdb.7z
3a516299edc67dadf310261ee48fee806d1558200c65f47f1547b2abfab2e3e6  win-x64/node_pdb.zip
3dd0ef59cac56bc51c763324fd083d39e5f8a250c8e75f99c8efec69005f7775  win-x86/node.exe
16e742472854b33130c4dd726637a225edca9a27f3427a5bb80e238ca0388872  win-x86/node.lib
1ed10a3794d7f7272377bd0edbb5e830d0b4db076b7d9370c2fcd4f4786b615a  win-x86/node_pdb.7z
407752e8840761622e8badb7611d894ddfb9bd4d06f3c64bdc6d5b428cf1301b  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEj8yhP+8dDC6RAI4Jdw96mlrhVgAFAl2td/UACgkQdw96mlrh
VgBMbA/9FzDli413Sk4MqldRc0AUoSE6j6K390W9JFIkTZs2TA0o3sevXiWu8acy
UmpeAHUjCMZuS0uD07s8hCAyKy3rLt/lv3WJiK8eMvjPyTY3OZVsqh8zqRMxIFVl
BIq43J99047e+8lbfWyIBs6c7t0IkvtEd4dW4on3acDWoqvfRM/vKD08ZVslf4zp
qilf1AW9PgB62J60lWkb8N1nL/eWl8JW9etV561UyJJ+n7orZQFGve7nekRPN03U
p0vTIpLRqZm9rzJlgQgmAs77IV9V3OxuFEPxBbIL0Qe4x4erO3EYUP6o5EX1eRl0
ZCBX8fK8cS3FbQzk8CBuLdRj5Y1ybZ023f8D/P0a715J+mZ+uciBW2eKiEFDJX66
g0JvBCNQiN2q5O4nzOyQfyU0kh7u47whKTDy7RLQTLTou6JaDVRXr4z7oot4KJVv
QkNu5FROVT1Lp1NhQNTerOaPkHJCVDMTIbCcxFitZJP14YGvT4bZ9SJK3c99KV22
v21CNC+kiqEG1SYySzB9qjZmtO9hyu8zY/K/ijvU8/aqVdxueHo37VsMKTvZ+TY2
RWsfXf416uN4jpjAr9BdpXuiuksTegHMIbpTNlvImmfDoeqa3StSirm8GPH8i7/G
iTbVftTqJL7mzt82dHqW6+0gMlcpwZIWnk1cm2kFv0c9/q99fiY=
=YWiu
-----END PGP SIGNATURE-----

```
