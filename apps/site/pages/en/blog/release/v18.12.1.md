---
date: '2022-11-04T22:34:29.818Z'
category: release
title: Node v18.12.1 (LTS)
layout: blog-post
author: <PERSON>
---

### Notable changes

The following CVEs are fixed in this release:

- **[CVE-2022-3602](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2022-3602)**: X.509 Email Address 4-byte Buffer Overflow (High)
- **[CVE-2022-3786](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2022-3786)**: X.509 Email Address Variable Length Buffer Overflow (High)
- **[CVE-2022-43548](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2022-43548)**: DNS rebinding in --inspect via invalid octal IP address (Medium)

More detailed information on each of the vulnerabilities can be found in [November 2022 Security Releases](/blog/vulnerability/november-2022-security-releases/) blog post.

### Commits

- \[[`39f8a672e3`](https://github.com/nodejs/node/commit/39f8a672e3)] - **deps**: update archs files for quictls/openssl-3.0.7+quic [nodejs/node#45286](https://github.com/nodejs/node/pull/45286)
- \[[`80218127c8`](https://github.com/nodejs/node/commit/80218127c8)] - **deps**: upgrade openssl sources to quictls/openssl-3.0.7+quic [nodejs/node#45286](https://github.com/nodejs/node/pull/45286)
- \[[`165342beac`](https://github.com/nodejs/node/commit/165342beac)] - **inspector**: harden IP address validation again (Tobias Nießen) [nodejs-private/node-private#354](https://github.com/nodejs-private/node-private/pull/354)

Windows 32-bit Installer: https://nodejs.org/dist/v18.12.1/node-v18.12.1-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v18.12.1/node-v18.12.1-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v18.12.1/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v18.12.1/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v18.12.1/node-v18.12.1.pkg \
macOS Apple Silicon 64-bit Binary: https://nodejs.org/dist/v18.12.1/node-v18.12.1-darwin-arm64.tar.gz \
macOS Intel 64-bit Binary: https://nodejs.org/dist/v18.12.1/node-v18.12.1-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v18.12.1/node-v18.12.1-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v18.12.1/node-v18.12.1-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v18.12.1/node-v18.12.1-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v18.12.1/node-v18.12.1-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v18.12.1/node-v18.12.1-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v18.12.1/node-v18.12.1-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v18.12.1/node-v18.12.1.tar.gz \
Other release files: https://nodejs.org/dist/v18.12.1/ \
Documentation: https://nodejs.org/docs/v18.12.1/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

adc237098880c85b3aacaf2dfbea86c3db065fa7e4cbf207b617a661f4627555  node-v18.12.1-aix-ppc64.tar.gz
9857042e18a0530a19f22f29a38de4fed5608e32543216f9afb6edea8d0dfdd3  node-v18.12.1-darwin-arm64.tar.gz
17f2e25d207d36d6b0964845062160d9ed16207c08d09af33b9a2fd046c5896f  node-v18.12.1-darwin-arm64.tar.xz
90ac0e8148f3fb52bcfc01d9f7d7963ce565dd0add0cd8d3e0698fbd7ecf1e5a  node-v18.12.1-darwin-x64.tar.gz
6c88d462550a024661e74e9377371d7e023321a652eafb3d14d58a866e6ac002  node-v18.12.1-darwin-x64.tar.xz
9d55ee072ba6d5a141db092cef1a0f715f7d3fc938285a6d927a1d0a0c7442f7  node-v18.12.1-headers.tar.gz
c16e93695b00520a085b999808883d40c0ec12759a9ee63448133060c3aab2eb  node-v18.12.1-headers.tar.xz
521587df6d2b9d9c524105c8f3f9d775dcfc5e7fbf7633e4455cc2e9af7d0ced  node-v18.12.1-linux-arm64.tar.gz
3904869935b7ecc51130b4b86486d2356539a174d11c9181180cab649f32cd2a  node-v18.12.1-linux-arm64.tar.xz
0c726bb061d10befb5e383e3e787446dc86ba99ae849fb24f27f88fee27433d5  node-v18.12.1-linux-armv7l.tar.gz
d0131a764c0f44821fdacb3c3ab8b35b52af060a98ac7a150ec49d4c540be3d7  node-v18.12.1-linux-armv7l.tar.xz
b6eb0af1311f6cb0349c7b7babc17bb32865a2eed6c7f304eddc111bf9576481  node-v18.12.1-linux-ppc64le.tar.gz
9646cd3dbaf80828a7b420a9b80fd3be3ecf5d182e1c080c85397b9986a9c818  node-v18.12.1-linux-ppc64le.tar.xz
28270961975e3c166e049d6ceff8cd7068802f54ddd7c9c12b6941ee129ead44  node-v18.12.1-linux-s390x.tar.gz
c4672a02aaf5311d32a1bd3ec8a8607f03b2f692142b5fb305cc3562f9cb316f  node-v18.12.1-linux-s390x.tar.xz
a8fcacb8033504e6d704bdee821f7005ee3774db25c799bcf2a13b5bda7de172  node-v18.12.1-linux-x64.tar.gz
4481a34bf32ddb9a9ff9540338539401320e8c3628af39929b4211ea3552a19e  node-v18.12.1-linux-x64.tar.xz
16acaf8d12c9f828fdd71cd499b324014e961c9d36d0d0f7a092863ce3100065  node-v18.12.1.pkg
ba8174dda00d5b90943f37c6a180a1d37c861d91e04a4cb38dc1c0c74981c186  node-v18.12.1.tar.gz
4fa406451bc52659a290e52cfdb2162a760bd549da4b8bbebe6a29f296d938df  node-v18.12.1.tar.xz
b8f73f9b7fa78aebf172893713b14908aa46585dbb88e333bf9c26102533a7f6  node-v18.12.1-win-x64.7z
5478a5a2dce2803ae22327a9f8ae8494c1dec4a4beca5bbf897027380aecf4c7  node-v18.12.1-win-x64.zip
a02b64d9a3f457e00a6138d6b51812b53dcc458632b228cba862081263d01d48  node-v18.12.1-win-x86.7z
099b42d27b7d05fe6feb04fa525364e960aa348f8ad2d95fef64e1608ee843be  node-v18.12.1-win-x86.zip
68e5d77f23c71168b6066444c36c9489c4165db6619c6cf5ac96b48684831fdd  node-v18.12.1-x64.msi
860682f15893741ad18c7c257d79a51b875c1136d5d9e3cc55e8c7f273780fbc  node-v18.12.1-x86.msi
215180104150568025cc41ba9b234b8f6c6ad4c7f75f2e55a97de85cfb545060  win-x64/node.exe
1bd376a23d181d85096d1a9c46e6be7fcd20d30f9b8f77a2a847d3dbff8e25c7  win-x64/node.lib
2b76e27f6cb0d30467b71a0ac22b55b36f657f6b38e6c38e49e98de6a1e60de9  win-x64/node_pdb.7z
fda35a2fd04d550831c5069d3b2f4024ec7d950e45dff9caa59e7b5f8b16e50c  win-x64/node_pdb.zip
4fedbbe7d609875d75c6361974828ff7d2bd65195b4e17472c8c407d991cd209  win-x86/node.exe
b1c6dc670911d85ef1704fa56f4cc4c7e1071f4869778398e6d88b3b0b565978  win-x86/node.lib
99f54dfb72497d84ad3326ee811c667187983707c0f12c96d96d0dd948b78152  win-x86/node_pdb.7z
3fc2c909af7f8039459b86f42332310eb591b44970439445917ecd5fe295130e  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEYfxoHfuSoHnxaF53lz8pVZTsRokFAmNlcicACgkQlz8pVZTs
RonxWhAAtn25zICJTqmq9tl+2P3gdMUcNtjlt1ukaO/ecTcXDrlXHpdKO75l9ddr
2twOvNKOFosiSLsKj1Te5pREEYsGHUqWjet9RIj2Ta205y4GoOeB0hLTBayjWwmE
/eDTLdeSzhJaP5lj9XnKp6rxQS6QQua+5gnl2Kr6Qyq+YnVorzQnBEN6FhaiKGYE
fCQHSLccbN1XOjvXGHbK8ZIEzD1PLCQAkejY5NASvNFoWraVimtcYXp/9WVf8H4w
2SVG2LbCnB4aJ0dyxAIFrrimduhjwdc9ItJO2/akzXIrTn5M0hzZjqwR6Glojdfx
CpSy9ywtuB1kZj/EtraKf8GDjyJcc3lYX3rviZ3zUH0rS+fJkYL+CET8CahrETtU
w2L06YO+XwZA8vADYJAepkulgM7L2GQwEMhOe9s/NmIPn48AzG0We6gBiuafz73o
L/TxIOIvmHGBQ3a7wLCcxN/K/AvJ+5fpJhfg7rI7iR2yRAE+jDatpWDybBmxW6rZ
MgjGMqsVsnnhJrDJURRw6SHwu7kKIMngWxwkjILDYMOft1vBjg/AGGq62sjxEKkY
5BkNhYtSoO3dUeX2cK3cgLRmKTjgjla/Ch9ITLkeIIanRFvyPScyt5suhb+4YqkJ
5qC/0AT0XtQ+5B/4VHE1oS0FcpjwamC2UxHvfXF3OLSkSya7FQM=
=1qY0
-----END PGP SIGNATURE-----

```
