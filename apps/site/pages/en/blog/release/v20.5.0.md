---
date: '2023-07-20T21:33:55.042Z'
category: release
title: Node v20.5.0 (Current)
layout: blog-post
author: <PERSON>
---

### Notable Changes

- \[[`45be29d89f`](https://github.com/nodejs/node/commit/45be29d89f)] - **doc**: add atlowChemi to collaborators (atlowChemi) [#48757](https://github.com/nodejs/node/pull/48757)
- \[[`a316808136`](https://github.com/nodejs/node/commit/a316808136)] - **(SEMVER-MINOR)** **events**: allow safely adding listener to abortSignal (<PERSON><PERSON><PERSON>) [#48596](https://github.com/nodejs/node/pull/48596)
- \[[`986b46a567`](https://github.com/nodejs/node/commit/986b46a567)] - **fs**: add a fast-path for readFileSync utf-8 (<PERSON><PERSON><PERSON>) [#48658](https://github.com/nodejs/node/pull/48658)
- \[[`0ef73ff6f0`](https://github.com/nodejs/node/commit/0ef73ff6f0)] - **(SEMVER-MINOR)** **test_runner**: add shards support (Raz Luvaton) [#48639](https://github.com/nodejs/node/pull/48639)

### Commits

- \[[`eb0aba59b8`](https://github.com/nodejs/node/commit/eb0aba59b8)] - **bootstrap**: use correct descriptor for Symbol.{dispose,asyncDispose} (Jordan Harband) [#48703](https://github.com/nodejs/node/pull/48703)
- \[[`e2d0195dcf`](https://github.com/nodejs/node/commit/e2d0195dcf)] - **bootstrap**: hide experimental web globals with flag kNoBrowserGlobals (Chengzhong Wu) [#48545](https://github.com/nodejs/node/pull/48545)
- \[[`67a1018389`](https://github.com/nodejs/node/commit/67a1018389)] - **build**: do not pass target toolchain flags to host toolchain (Ivan Trubach) [#48597](https://github.com/nodejs/node/pull/48597)
- \[[`7d843bb942`](https://github.com/nodejs/node/commit/7d843bb942)] - **child_process**: use addAbortListener (atlowChemi) [#48550](https://github.com/nodejs/node/pull/48550)
- \[[`4e08160f8c`](https://github.com/nodejs/node/commit/4e08160f8c)] - **child_process**: support `Symbol.dispose` (Moshe Atlow) [#48551](https://github.com/nodejs/node/pull/48551)
- \[[`ef7728bf36`](https://github.com/nodejs/node/commit/ef7728bf36)] - **deps**: update nghttp2 to 1.55.1 (Node.js GitHub Bot) [#48790](https://github.com/nodejs/node/pull/48790)
- \[[`1454f02499`](https://github.com/nodejs/node/commit/1454f02499)] - **deps**: update nghttp2 to 1.55.0 (Node.js GitHub Bot) [#48746](https://github.com/nodejs/node/pull/48746)
- \[[`fa94debf46`](https://github.com/nodejs/node/commit/fa94debf46)] - **deps**: update minimatch to 9.0.3 (Node.js GitHub Bot) [#48704](https://github.com/nodejs/node/pull/48704)
- \[[`c73cfcc144`](https://github.com/nodejs/node/commit/c73cfcc144)] - **deps**: update acorn to 8.10.0 (Node.js GitHub Bot) [#48713](https://github.com/nodejs/node/pull/48713)
- \[[`b7a076a052`](https://github.com/nodejs/node/commit/b7a076a052)] - **deps**: V8: cherry-pick cb00db4dba6c (Keyhan Vakil) [#48671](https://github.com/nodejs/node/pull/48671)
- \[[`150e15536b`](https://github.com/nodejs/node/commit/150e15536b)] - **deps**: upgrade npm to 9.8.0 (npm team) [#48665](https://github.com/nodejs/node/pull/48665)
- \[[`c47b2cbd35`](https://github.com/nodejs/node/commit/c47b2cbd35)] - **dgram**: socket add `asyncDispose` (atlowChemi) [#48717](https://github.com/nodejs/node/pull/48717)
- \[[`002ce31cca`](https://github.com/nodejs/node/commit/002ce31cca)] - **dgram**: use addAbortListener (atlowChemi) [#48550](https://github.com/nodejs/node/pull/48550)
- \[[`45be29d89f`](https://github.com/nodejs/node/commit/45be29d89f)] - **doc**: add atlowChemi to collaborators (atlowChemi) [#48757](https://github.com/nodejs/node/pull/48757)
- \[[`69b55d2261`](https://github.com/nodejs/node/commit/69b55d2261)] - **doc**: fix ambiguity in http.md and https.md (an5er) [#48692](https://github.com/nodejs/node/pull/48692)
- \[[`caccb051c7`](https://github.com/nodejs/node/commit/caccb051c7)] - **doc**: clarify transform.\_transform() callback argument logic (Rafael Sofi-zada) [#48680](https://github.com/nodejs/node/pull/48680)
- \[[`999ae0c8c3`](https://github.com/nodejs/node/commit/999ae0c8c3)] - **doc**: fix copy node executable in Windows (Yoav Vainrich) [#48624](https://github.com/nodejs/node/pull/48624)
- \[[`7daefaeb44`](https://github.com/nodejs/node/commit/7daefaeb44)] - **doc**: drop \<b> of v20 changelog (Rafael Gonzaga) [#48649](https://github.com/nodejs/node/pull/48649)
- \[[`dd7ea3e1df`](https://github.com/nodejs/node/commit/dd7ea3e1df)] - **doc**: mention git node release prepare (Rafael Gonzaga) [#48644](https://github.com/nodejs/node/pull/48644)
- \[[`cc7809df21`](https://github.com/nodejs/node/commit/cc7809df21)] - **esm**: fix emit deprecation on legacy main resolve (Antoine du Hamel) [#48664](https://github.com/nodejs/node/pull/48664)
- \[[`67b13d1dba`](https://github.com/nodejs/node/commit/67b13d1dba)] - **events**: fix bug listenerCount don't compare wrapped listener (yuzheng14) [#48592](https://github.com/nodejs/node/pull/48592)
- \[[`a316808136`](https://github.com/nodejs/node/commit/a316808136)] - **(SEMVER-MINOR)** **events**: allow safely adding listener to abortSignal (Chemi Atlow) [#48596](https://github.com/nodejs/node/pull/48596)
- \[[`986b46a567`](https://github.com/nodejs/node/commit/986b46a567)] - **fs**: add a fast-path for readFileSync utf-8 (Yagiz Nizipli) [#48658](https://github.com/nodejs/node/pull/48658)
- \[[`e4333ac41f`](https://github.com/nodejs/node/commit/e4333ac41f)] - **http2**: use addAbortListener (atlowChemi) [#48550](https://github.com/nodejs/node/pull/48550)
- \[[`4a0b66e4f9`](https://github.com/nodejs/node/commit/4a0b66e4f9)] - **http2**: send RST code 8 on AbortController signal (Devraj Mehta) [#48573](https://github.com/nodejs/node/pull/48573)
- \[[`1295c76fce`](https://github.com/nodejs/node/commit/1295c76fce)] - **lib**: use addAbortListener (atlowChemi) [#48550](https://github.com/nodejs/node/pull/48550)
- \[[`dff6c25a36`](https://github.com/nodejs/node/commit/dff6c25a36)] - **meta**: bump actions/checkout from 3.5.2 to 3.5.3 (dependabot\[bot]) [#48625](https://github.com/nodejs/node/pull/48625)
- \[[`b5cb69ceaa`](https://github.com/nodejs/node/commit/b5cb69ceaa)] - **meta**: bump step-security/harden-runner from 2.4.0 to 2.4.1 (dependabot\[bot]) [#48626](https://github.com/nodejs/node/pull/48626)
- \[[`332e480b46`](https://github.com/nodejs/node/commit/332e480b46)] - **meta**: bump ossf/scorecard-action from 2.1.3 to 2.2.0 (dependabot\[bot]) [#48628](https://github.com/nodejs/node/pull/48628)
- \[[`25c5a0aaee`](https://github.com/nodejs/node/commit/25c5a0aaee)] - **meta**: bump github/codeql-action from 2.3.6 to 2.20.1 (dependabot\[bot]) [#48627](https://github.com/nodejs/node/pull/48627)
- \[[`6406f50ab1`](https://github.com/nodejs/node/commit/6406f50ab1)] - **module**: add SourceMap.lineLengths (Isaac Z. Schlueter) [#48461](https://github.com/nodejs/node/pull/48461)
- \[[`cfa69bd48c`](https://github.com/nodejs/node/commit/cfa69bd48c)] - **net**: server add `asyncDispose` (atlowChemi) [#48717](https://github.com/nodejs/node/pull/48717)
- \[[`ac11264cc5`](https://github.com/nodejs/node/commit/ac11264cc5)] - **net**: use addAbortListener (atlowChemi) [#48550](https://github.com/nodejs/node/pull/48550)
- \[[`82d6b13bf6`](https://github.com/nodejs/node/commit/82d6b13bf6)] - **permission**: add debug log when inserting fs nodes (Rafael Gonzaga) [#48677](https://github.com/nodejs/node/pull/48677)
- \[[`f4333b1cdd`](https://github.com/nodejs/node/commit/f4333b1cdd)] - **permission**: v8.writeHeapSnapshot and process.report (Rafael Gonzaga) [#48564](https://github.com/nodejs/node/pull/48564)
- \[[`f691dca6c9`](https://github.com/nodejs/node/commit/f691dca6c9)] - **readline**: use addAbortListener (atlowChemi) [#48550](https://github.com/nodejs/node/pull/48550)
- \[[`227e6bd898`](https://github.com/nodejs/node/commit/227e6bd898)] - **src**: pass syscall on `fs.readFileSync` fail operation (Yagiz Nizipli) [#48815](https://github.com/nodejs/node/pull/48815)
- \[[`a9a4b73653`](https://github.com/nodejs/node/commit/a9a4b73653)] - **src**: make BaseObject iteration order deterministic (Joyee Cheung) [#48702](https://github.com/nodejs/node/pull/48702)
- \[[`d99ea4845a`](https://github.com/nodejs/node/commit/d99ea4845a)] - **src**: remove kEagerCompile for CompileFunction (Keyhan Vakil) [#48671](https://github.com/nodejs/node/pull/48671)
- \[[`df363d0010`](https://github.com/nodejs/node/commit/df363d0010)] - **src**: deduplicate X509 getter implementations (Tobias Nießen) [#48563](https://github.com/nodejs/node/pull/48563)
- \[[`9cf2e1f55b`](https://github.com/nodejs/node/commit/9cf2e1f55b)] - **src,lib**: reducing C++ calls of esm legacy main resolve (Vinicius Lourenço) [#48325](https://github.com/nodejs/node/pull/48325)
- \[[`daeb21dde9`](https://github.com/nodejs/node/commit/daeb21dde9)] - **stream**: fix deadlock when pipeing to full sink (Robert Nagy) [#48691](https://github.com/nodejs/node/pull/48691)
- \[[`5a382d02d6`](https://github.com/nodejs/node/commit/5a382d02d6)] - **stream**: use addAbortListener (atlowChemi) [#48550](https://github.com/nodejs/node/pull/48550)
- \[[`6e82077dd4`](https://github.com/nodejs/node/commit/6e82077dd4)] - **test**: deflake test-net-throttle (Luigi Pinca) [#48599](https://github.com/nodejs/node/pull/48599)
- \[[`d378b2c822`](https://github.com/nodejs/node/commit/d378b2c822)] - **test**: move test-net-throttle to parallel (Luigi Pinca) [#48599](https://github.com/nodejs/node/pull/48599)
- \[[`dfa0aee5bf`](https://github.com/nodejs/node/commit/dfa0aee5bf)] - _**Revert**_ "**test**: remove test-crypto-keygen flaky designation" (Luigi Pinca) [#48652](https://github.com/nodejs/node/pull/48652)
- \[[`0ef73ff6f0`](https://github.com/nodejs/node/commit/0ef73ff6f0)] - **(SEMVER-MINOR)** **test_runner**: add shards support (Raz Luvaton) [#48639](https://github.com/nodejs/node/pull/48639)
- \[[`e2442bb7ef`](https://github.com/nodejs/node/commit/e2442bb7ef)] - **timers**: support Symbol.dispose (Moshe Atlow) [#48633](https://github.com/nodejs/node/pull/48633)
- \[[`4398ade426`](https://github.com/nodejs/node/commit/4398ade426)] - **tools**: run fetch_deps.py with Python 3 (Richard Lau) [#48729](https://github.com/nodejs/node/pull/48729)
- \[[`38ce95d054`](https://github.com/nodejs/node/commit/38ce95d054)] - **tools**: update doc to unist-util-select\@5.0.0 unist-util-visit\@5.0.0 (Node.js GitHub Bot) [#48714](https://github.com/nodejs/node/pull/48714)
- \[[`b25e78a998`](https://github.com/nodejs/node/commit/b25e78a998)] - **tools**: update lint-md-dependencies to rollup\@3.26.2 (Node.js GitHub Bot) [#48705](https://github.com/nodejs/node/pull/48705)
- \[[`a1f4ff7c59`](https://github.com/nodejs/node/commit/a1f4ff7c59)] - **tools**: update eslint to 8.44.0 (Node.js GitHub Bot) [#48632](https://github.com/nodejs/node/pull/48632)
- \[[`42dc6eb698`](https://github.com/nodejs/node/commit/42dc6eb698)] - **tools**: update lint-md-dependencies to rollup\@3.26.0 (Node.js GitHub Bot) [#48631](https://github.com/nodejs/node/pull/48631)
- \[[`07bfcc45ab`](https://github.com/nodejs/node/commit/07bfcc45ab)] - **url**: fix `canParse` false value when v8 optimizes (Yagiz Nizipli) [#48817](https://github.com/nodejs/node/pull/48817)

Windows 32-bit Installer: https://nodejs.org/dist/v20.5.0/node-v20.5.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v20.5.0/node-v20.5.0-x64.msi \
Windows ARM 64-bit Installer: https://nodejs.org/dist/v20.5.0/node-v20.5.0-arm64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v20.5.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v20.5.0/win-x64/node.exe \
Windows ARM 64-bit Binary: https://nodejs.org/dist/v20.5.0/win-arm64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v20.5.0/node-v20.5.0.pkg \
macOS Apple Silicon 64-bit Binary: https://nodejs.org/dist/v20.5.0/node-v20.5.0-darwin-arm64.tar.gz \
macOS Intel 64-bit Binary: https://nodejs.org/dist/v20.5.0/node-v20.5.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v20.5.0/node-v20.5.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v20.5.0/node-v20.5.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v20.5.0/node-v20.5.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v20.5.0/node-v20.5.0-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v20.5.0/node-v20.5.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v20.5.0/node-v20.5.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v20.5.0/node-v20.5.0.tar.gz \
Other release files: https://nodejs.org/dist/v20.5.0/ \
Documentation: https://nodejs.org/docs/v20.5.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

daba6c69f21af9802ce571b33e75d79854dd2489475308058f38e65f17ac70a0  node-v20.5.0-aix-ppc64.tar.gz
04a02ddb479f8fb13c2106289c9e91a4569db299cf284dad139bcb2c4654a405  node-v20.5.0-arm64.msi
56d29a7c620415164e6226804cc1eb8eb7b05ea3123b60c86393fabb551bd5ea  node-v20.5.0-darwin-arm64.tar.gz
af89c7d605fc9ba0c4f1f6255fc6e8354b3ae9a498eb77ca1577f3a6913f209d  node-v20.5.0-darwin-arm64.tar.xz
3da7e64ac76309cbbb25524bae75cd335fed2795fcbd4f55e3162bcbcec18176  node-v20.5.0-darwin-x64.tar.gz
94f12573490c67cb50a080e0ed67ec82a49eae5acb752bfe6e42dde315a20f44  node-v20.5.0-darwin-x64.tar.xz
abd66823cb42618ccddcd1dd5939e6d62a824d26577016562c566a5bb066c335  node-v20.5.0-headers.tar.gz
72d4427470fc429eb04adccdcb316b45c0def7fe91a03a4135380ae82cd93bc5  node-v20.5.0-headers.tar.xz
fde339b181db5827b42b83bd20479a44fe5bd5b8e4b71a5d09ee2f1f8152de97  node-v20.5.0-linux-arm64.tar.gz
afddd830662bdc71f37d39d6cd74104acc663ecd6bbe0fd9264c581ee4f2559b  node-v20.5.0-linux-arm64.tar.xz
de0f017d1871675662fdfc59c7c7050a758327968f657eaaca8c77d9fa865958  node-v20.5.0-linux-armv7l.tar.gz
c934cf8b3d371b74febbf30a3ae1541bf0202b1fd8effa14f2a0bef678794ecb  node-v20.5.0-linux-armv7l.tar.xz
0e734edc679d7b86434fcb0e4b1a3c2abcc28c8588d147b2a278df7ff6cb5937  node-v20.5.0-linux-ppc64le.tar.gz
317d60c533f7dd96d811887ef9bc486ae857c73a89128eb3e720fce153872ce6  node-v20.5.0-linux-ppc64le.tar.xz
0f2c9e86da425a431eeb46a8da89f9757a8d7caf2a567778f7632ca3fd787773  node-v20.5.0-linux-s390x.tar.gz
205bfa0f48087c3bc25d7bc2752a6425d850688c2f2c5e77349b2e945c9e0b1d  node-v20.5.0-linux-s390x.tar.xz
6799042a2970dcecdb71a91d392c53d954ec06d36155c7d11bf7c9a4983b60ea  node-v20.5.0-linux-x64.tar.gz
c12ee9efe21f3ff9909fbf5d7d3780f16c86fad411f13d715016646c766e8213  node-v20.5.0-linux-x64.tar.xz
85ca539c3d51b6879d9b959b140df0e7b86a8a818bff0e4c20e121d4d41feddf  node-v20.5.0.pkg
22ee3adcbc4714ece1d4931b8240e78c8f9bfac6b1f7afd8a3d37e338829cf78  node-v20.5.0.tar.gz
cb32756958def1c04e069a79b71b52ca61ed1590c17f2cfa1ee3af641f72e058  node-v20.5.0.tar.xz
5a78475a2661c1ec113c9020d17c44197c20311e46fa9dee5d5a0102204b16ce  node-v20.5.0-win-arm64.7z
e74b9cfbfb556165ec68b94cfc74577e99f2bcac8451ba99d1c10e058b2edead  node-v20.5.0-win-arm64.zip
46d063e1442207924e38a650e52c151dded2a30626c4ac80978f9743523d79e8  node-v20.5.0-win-x64.7z
604e7308bb314fb8c27979929db2877940ce27a489ccafc6367f439943730b32  node-v20.5.0-win-x64.zip
3ddee03edb8b8e829f9ba930f29a0d2db7e93a5b67bd9f5f7c96a0b6544a9374  node-v20.5.0-win-x86.7z
95028262100da9d680ecc14ae26de78b2a16b9159a17dae0f9ebcfb520e8163e  node-v20.5.0-win-x86.zip
d895b18c6f69cb8789a0d19a925b07a80715aac62343200dab90fdba745db2ce  node-v20.5.0-x64.msi
e20e399932ae744705414f35430993ba74ff48bae7ba195c3393ace72236bfac  node-v20.5.0-x86.msi
a539946f587d414c1e99028e2a4cd48b862896c480ac07570082ce6765045c9b  win-arm64/node.exe
f9443121897f62db031af6493faf122b97ed73e08fe147fbc41db9d2d3ce05bb  win-arm64/node.lib
b00e0dd370bdb7d95e2080227f8541db891023c95a9c798c37a3735ace8e1164  win-arm64/node_pdb.7z
dcc6937174c9ce7908603b5b399d34087ca6eb39b5cc374e91a82575aae56fee  win-arm64/node_pdb.zip
025424856ccedf28ea55c8e1c2f9f041fc93cc83e729d9f2fde5eb60185621e3  win-x64/node.exe
2d5b066b768af52f08540adacea5d75864c13a65f3af30da935591d25401615f  win-x64/node.lib
f08ba3776448897c318537ffd1515d973de166139654d6c2a1a5ad98030dff90  win-x64/node_pdb.7z
4b9f3b0dd2b6001317212f9eb079da26bcfef3bbd9fbf94813db2614762e18dc  win-x64/node_pdb.zip
195581522be4aeec814844ba687142653f59cff4daa411eaeceeb64f63bc9260  win-x86/node.exe
019308f8ae206cac79b080d7833d10b552cf443f48fc7fe5486ccdc2438018e5  win-x86/node.lib
3c8ac41d701cb843d0758d858a8e58e5662f889678dc65fa86fe4e2cc12d39e1  win-x86/node_pdb.7z
affbd8b23249c7d15e92d8dc1da90f899b028d92269772f0daa0348e85e4cd9c  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iHUEARYIAB0WIQTdeS9Zc8beUsQyy9rHer+gDdvytwUCZLmkkAAKCRDHer+gDdvy
t0avAP4u2B9CEVErcU9gwcZJNJPNhK9pU80kMrrEwNbI0cQwFgEAnuImHPA5uumq
ALnoDX/PpyJiBElSdCAzkAZh0qRm7wI=
=eSwt
-----END PGP SIGNATURE-----

```
