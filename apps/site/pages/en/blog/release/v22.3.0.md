---
date: '2024-06-11T19:00:57.015Z'
category: release
title: Node v22.3.0 (Current)
layout: blog-post
author: <PERSON>
---

## 2024-06-11, Version 22.3.0 (Current), @RafaelGSS

### Notable Changes

- \[[`5a41bcf9ca`](https://github.com/nodejs/node/commit/5a41bcf9ca)] - **(SEMVER-MINOR)** **src**: traverse parent folders while running `--run` (<PERSON><PERSON><PERSON>) [#53154](https://github.com/nodejs/node/pull/53154)
- \[[`1d5934524b`](https://github.com/nodejs/node/commit/1d5934524b)] - **(SEMVER-MINOR)** **buffer**: add .bytes() method to Blob (<PERSON>) [#53221](https://github.com/nodejs/node/pull/53221)
- \[[`75e5612fae`](https://github.com/nodejs/node/commit/75e5612fae)] - **(SEMVER-MINOR)** **src,permission**: --allow-wasi & prevent WASI exec (Rafael Gonzaga) [#53124](https://github.com/nodejs/node/pull/53124)
- \[[`b5c30e2f5e`](https://github.com/nodejs/node/commit/b5c30e2f5e)] - **(SEMVER-MINOR)** **module**: print amount of load time of a cjs module (Vinicius Lourenço) [#52213](https://github.com/nodejs/node/pull/52213)
- \[[`8c6dffc269`](https://github.com/nodejs/node/commit/8c6dffc269)] - **(SEMVER-MINOR)** **test_runner**: add snapshot testing (Colin Ihrig) [#53169](https://github.com/nodejs/node/pull/53169)
- \[[`048478d351`](https://github.com/nodejs/node/commit/048478d351)] - **(SEMVER-MINOR)** **doc**: add context.assert docs (Colin Ihrig) [#53169](https://github.com/nodejs/node/pull/53169)
- \[[`f6d2af8ee7`](https://github.com/nodejs/node/commit/f6d2af8ee7)] - **(SEMVER-MINOR)** **test_runner**: add context.fullName (Colin Ihrig) [#53169](https://github.com/nodejs/node/pull/53169)
- \[[`a0766bdf0e`](https://github.com/nodejs/node/commit/a0766bdf0e)] - **(SEMVER-MINOR)** **net**: add new net.server.listen tracing channel (Paolo Insogna) [#53136](https://github.com/nodejs/node/pull/53136)
- \[[`374743cd4e`](https://github.com/nodejs/node/commit/374743cd4e)] - **(SEMVER-MINOR)** **process**: add process.getBuiltinModule(id) (Joyee Cheung) [#52762](https://github.com/nodejs/node/pull/52762)
- \[[`1eb55f3550`](https://github.com/nodejs/node/commit/1eb55f3550)] - **(SEMVER-MINOR)** **doc**: improve explanation about built-in modules (Joyee Cheung) [#52762](https://github.com/nodejs/node/pull/52762)
- \[[`6165894774`](https://github.com/nodejs/node/commit/6165894774)] - **fs**: mark recursive cp methods as stable (Théo LUDWIG) [#53127](https://github.com/nodejs/node/pull/53127)
- \[[`db5dd0c6df`](https://github.com/nodejs/node/commit/db5dd0c6df)] - **doc**: add StefanStojanovic to collaborators (StefanStojanovic) [#53118](https://github.com/nodejs/node/pull/53118)
- \[[`cfcde78513`](https://github.com/nodejs/node/commit/cfcde78513)] - **(SEMVER-MINOR)** **cli**: add `NODE_RUN_PACKAGE_JSON_PATH` env (Yagiz Nizipli) [#53058](https://github.com/nodejs/node/pull/53058)
- \[[`7a67ecf161`](https://github.com/nodejs/node/commit/7a67ecf161)] - **(SEMVER-MINOR)** **test_runner**: support module mocking (Colin Ihrig) [#52848](https://github.com/nodejs/node/pull/52848)
- \[[`ee56aecced`](https://github.com/nodejs/node/commit/ee56aecced)] - **(SEMVER-MINOR)** **lib**: add EventSource Client (Aras Abbasi) [#51575](https://github.com/nodejs/node/pull/51575)
- \[[`6413769bc7`](https://github.com/nodejs/node/commit/6413769bc7)] - **(SEMVER-MINOR)** **lib**: replace MessageEvent with undici's (Matthew Aitken) [#52370](https://github.com/nodejs/node/pull/52370)
- \[[`c70b2f7a76`](https://github.com/nodejs/node/commit/c70b2f7a76)] - **(SEMVER-MINOR)** **cli**: add `NODE_RUN_SCRIPT_NAME` env to `node --run` (Yagiz Nizipli) [#53032](https://github.com/nodejs/node/pull/53032)
- \[[`badec0c38b`](https://github.com/nodejs/node/commit/badec0c38b)] - **doc**: add Marco Ippolito to TSC (Rafael Gonzaga) [#53008](https://github.com/nodejs/node/pull/53008)

### Commits

- \[[`feb0ba2860`](https://github.com/nodejs/node/commit/feb0ba2860)] - **benchmark**: fix napi/ref addon (Michaël Zasso) [#53233](https://github.com/nodejs/node/pull/53233)
- \[[`bb844de4e1`](https://github.com/nodejs/node/commit/bb844de4e1)] - **benchmark**: fix api restriction for the permission category (Ryan Tsien) [#51528](https://github.com/nodejs/node/pull/51528)
- \[[`1d5934524b`](https://github.com/nodejs/node/commit/1d5934524b)] - **(SEMVER-MINOR)** **buffer**: add .bytes() method to Blob (Matthew Aitken) [#53221](https://github.com/nodejs/node/pull/53221)
- \[[`d87f9af5aa`](https://github.com/nodejs/node/commit/d87f9af5aa)] - **buffer**: make compare/equals faster (Tobias Nießen) [#52993](https://github.com/nodejs/node/pull/52993)
- \[[`ec83431d71`](https://github.com/nodejs/node/commit/ec83431d71)] - **build**: generate binlog in out directories (Chengzhong Wu) [#53325](https://github.com/nodejs/node/pull/53325)
- \[[`0976439417`](https://github.com/nodejs/node/commit/0976439417)] - **build**: fix --v8-lite-mode build (Daeyeon Jeong) [#52725](https://github.com/nodejs/node/pull/52725)
- \[[`350c733ae6`](https://github.com/nodejs/node/commit/350c733ae6)] - **build**: support python 3.13 (Chengzhong Wu) [#53190](https://github.com/nodejs/node/pull/53190)
- \[[`74cefa55a2`](https://github.com/nodejs/node/commit/74cefa55a2)] - **build**: update ruff to v0.4.5 (Yagiz Nizipli) [#53180](https://github.com/nodejs/node/pull/53180)
- \[[`33242ff042`](https://github.com/nodejs/node/commit/33242ff042)] - **build**: add `--skip-tests` to `test-ci-js` target (Antoine du Hamel) [#53105](https://github.com/nodejs/node/pull/53105)
- \[[`edcadf7f8a`](https://github.com/nodejs/node/commit/edcadf7f8a)] - **build**: fix building embedtest in GN build (Cheng) [#53145](https://github.com/nodejs/node/pull/53145)
- \[[`d711942fce`](https://github.com/nodejs/node/commit/d711942fce)] - **build**: use broader detection for 'help' (Aviv Keller) [#53045](https://github.com/nodejs/node/pull/53045)
- \[[`ca655b61a7`](https://github.com/nodejs/node/commit/ca655b61a7)] - **build**: fix -j propagation to ninja (Tobias Nießen) [#53088](https://github.com/nodejs/node/pull/53088)
- \[[`5fba67ff9f`](https://github.com/nodejs/node/commit/5fba67ff9f)] - **build**: exit on unsupported host OS for Android (Mohammed Keyvanzadeh) [#52882](https://github.com/nodejs/node/pull/52882)
- \[[`b7d7e9a084`](https://github.com/nodejs/node/commit/b7d7e9a084)] - **build**: fix `--enable-d8` builds (Richard Lau) [#53106](https://github.com/nodejs/node/pull/53106)
- \[[`14547c5d32`](https://github.com/nodejs/node/commit/14547c5d32)] - **build**: fix ./configure --help format error (Zhenwei Jin) [#53066](https://github.com/nodejs/node/pull/53066)
- \[[`f9490806d3`](https://github.com/nodejs/node/commit/f9490806d3)] - **build**: set "clang" in config.gypi in GN build (Cheng) [#53004](https://github.com/nodejs/node/pull/53004)
- \[[`638b510ce7`](https://github.com/nodejs/node/commit/638b510ce7)] - **cli**: add `--expose-gc` flag available to `NODE_OPTIONS` (Juan José) [#53078](https://github.com/nodejs/node/pull/53078)
- \[[`cfcde78513`](https://github.com/nodejs/node/commit/cfcde78513)] - **(SEMVER-MINOR)** **cli**: add `NODE_RUN_PACKAGE_JSON_PATH` env (Yagiz Nizipli) [#53058](https://github.com/nodejs/node/pull/53058)
- \[[`c70b2f7a76`](https://github.com/nodejs/node/commit/c70b2f7a76)] - **(SEMVER-MINOR)** **cli**: add `NODE_RUN_SCRIPT_NAME` env to `node --run` (Yagiz Nizipli) [#53032](https://github.com/nodejs/node/pull/53032)
- \[[`34f20983fd`](https://github.com/nodejs/node/commit/34f20983fd)] - **crypto**: fix propagation of "memory limit exceeded" (Tobias Nießen) [#53300](https://github.com/nodejs/node/pull/53300)
- \[[`fef067f4f4`](https://github.com/nodejs/node/commit/fef067f4f4)] - **deps**: update nghttp2 to 1.62.1 (Node.js GitHub Bot) [#52966](https://github.com/nodejs/node/pull/52966)
- \[[`fc949928ac`](https://github.com/nodejs/node/commit/fc949928ac)] - **deps**: update nghttp2 to 1.62.0 (Node.js GitHub Bot) [#52966](https://github.com/nodejs/node/pull/52966)
- \[[`4a17dda8dc`](https://github.com/nodejs/node/commit/4a17dda8dc)] - **deps**: update undici to 6.18.2 (Node.js GitHub Bot) [#53255](https://github.com/nodejs/node/pull/53255)
- \[[`e45cc2a551`](https://github.com/nodejs/node/commit/e45cc2a551)] - **deps**: update ada to 2.8.0 (Node.js GitHub Bot) [#53254](https://github.com/nodejs/node/pull/53254)
- \[[`77907a2619`](https://github.com/nodejs/node/commit/77907a2619)] - **deps**: update corepack to 0.28.2 (Node.js GitHub Bot) [#53253](https://github.com/nodejs/node/pull/53253)
- \[[`b688050778`](https://github.com/nodejs/node/commit/b688050778)] - **deps**: update simdjson to 3.9.3 (Node.js GitHub Bot) [#53252](https://github.com/nodejs/node/pull/53252)
- \[[`6303f19cbe`](https://github.com/nodejs/node/commit/6303f19cbe)] - **deps**: patch V8 to 12.4.254.20 (Node.js GitHub Bot) [#53159](https://github.com/nodejs/node/pull/53159)
- \[[`257004c68f`](https://github.com/nodejs/node/commit/257004c68f)] - **deps**: update c-ares to 1.29.0 (Node.js GitHub Bot) [#53155](https://github.com/nodejs/node/pull/53155)
- \[[`0b375a3e36`](https://github.com/nodejs/node/commit/0b375a3e36)] - **deps**: upgrade npm to 10.8.1 (npm team) [#53207](https://github.com/nodejs/node/pull/53207)
- \[[`728c861b1c`](https://github.com/nodejs/node/commit/728c861b1c)] - **deps**: fix FP16 bitcasts.h (Stefan Stojanovic) [#53134](https://github.com/nodejs/node/pull/53134)
- \[[`52a78737b1`](https://github.com/nodejs/node/commit/52a78737b1)] - **deps**: patch V8 to 12.4.254.19 (Node.js GitHub Bot) [#53094](https://github.com/nodejs/node/pull/53094)
- \[[`4d27b32e58`](https://github.com/nodejs/node/commit/4d27b32e58)] - **deps**: update undici to 6.18.1 (Node.js GitHub Bot) [#53073](https://github.com/nodejs/node/pull/53073)
- \[[`b94199240b`](https://github.com/nodejs/node/commit/b94199240b)] - **deps**: update undici to 6.18.0 (Node.js GitHub Bot) [#53073](https://github.com/nodejs/node/pull/53073)
- \[[`793af1b3e7`](https://github.com/nodejs/node/commit/793af1b3e7)] - **deps**: update undici to 6.17.0 (Node.js GitHub Bot) [#53034](https://github.com/nodejs/node/pull/53034)
- \[[`fe00becc03`](https://github.com/nodejs/node/commit/fe00becc03)] - **deps**: update undici to 6.16.1 (Node.js GitHub Bot) [#52948](https://github.com/nodejs/node/pull/52948)
- \[[`96f72ae54f`](https://github.com/nodejs/node/commit/96f72ae54f)] - **deps**: update undici to 6.15.0 (Matthew Aitken) [#52763](https://github.com/nodejs/node/pull/52763)
- \[[`af60fbb12b`](https://github.com/nodejs/node/commit/af60fbb12b)] - **deps**: update googletest to 33af80a (Node.js GitHub Bot) [#53053](https://github.com/nodejs/node/pull/53053)
- \[[`7b929df489`](https://github.com/nodejs/node/commit/7b929df489)] - **deps**: patch V8 to 12.4.254.18 (Node.js GitHub Bot) [#53054](https://github.com/nodejs/node/pull/53054)
- \[[`626037c0fc`](https://github.com/nodejs/node/commit/626037c0fc)] - **deps**: update zlib to 1.3.0.1-motley-4f653ff (Node.js GitHub Bot) [#53052](https://github.com/nodejs/node/pull/53052)
- \[[`6d8589e558`](https://github.com/nodejs/node/commit/6d8589e558)] - **deps**: patch V8 to 12.4.254.17 (Node.js GitHub Bot) [#52980](https://github.com/nodejs/node/pull/52980)
- \[[`fd91eaab34`](https://github.com/nodejs/node/commit/fd91eaab34)] - **deps**: upgrade npm to 10.8.0 (npm team) [#53014](https://github.com/nodejs/node/pull/53014)
- \[[`133cae0732`](https://github.com/nodejs/node/commit/133cae0732)] - **doc**: fix broken link in `static-analysis.md` (Richard Lau) [#53345](https://github.com/nodejs/node/pull/53345)
- \[[`7bc5f964fd`](https://github.com/nodejs/node/commit/7bc5f964fd)] - **doc**: indicate requirement on VS 17.6 or newer (Chengzhong Wu) [#53301](https://github.com/nodejs/node/pull/53301)
- \[[`8c71522ced`](https://github.com/nodejs/node/commit/8c71522ced)] - **doc**: remove cases for keys not containing "\*" in PATTERN_KEY_COMPARE (Maarten Zuidhoorn) [#53215](https://github.com/nodejs/node/pull/53215)
- \[[`718a3ab1ab`](https://github.com/nodejs/node/commit/718a3ab1ab)] - **doc**: add err param to fs.cp callback (Feng Yu) [#53234](https://github.com/nodejs/node/pull/53234)
- \[[`d89bde26ff`](https://github.com/nodejs/node/commit/d89bde26ff)] - **doc**: add `err` param to fs.copyFile callback (Feng Yu) [#53234](https://github.com/nodejs/node/pull/53234)
- \[[`91971ee344`](https://github.com/nodejs/node/commit/91971ee344)] - **doc**: reserve 128 for Electron 32 (Keeley Hammond) [#53203](https://github.com/nodejs/node/pull/53203)
- \[[`812f0e9e14`](https://github.com/nodejs/node/commit/812f0e9e14)] - **doc**: add note to ninjia build for macOS using -jn flag (jakecastelli) [#53187](https://github.com/nodejs/node/pull/53187)
- \[[`048478d351`](https://github.com/nodejs/node/commit/048478d351)] - **(SEMVER-MINOR)** **doc**: add context.assert docs (Colin Ihrig) [#53169](https://github.com/nodejs/node/pull/53169)
- \[[`c391923445`](https://github.com/nodejs/node/commit/c391923445)] - **doc**: include ESM import for HTTP (Aviv Keller) [#53165](https://github.com/nodejs/node/pull/53165)
- \[[`1eb55f3550`](https://github.com/nodejs/node/commit/1eb55f3550)] - **(SEMVER-MINOR)** **doc**: improve explanation about built-in modules (Joyee Cheung) [#52762](https://github.com/nodejs/node/pull/52762)
- \[[`67a766f7d4`](https://github.com/nodejs/node/commit/67a766f7d4)] - **doc**: fix minor grammar and style issues in SECURITY.md (Rich Trott) [#53168](https://github.com/nodejs/node/pull/53168)
- \[[`afbfe8922a`](https://github.com/nodejs/node/commit/afbfe8922a)] - **doc**: mention pm is not enforced when using fd (Rafael Gonzaga) [#53125](https://github.com/nodejs/node/pull/53125)
- \[[`1702d2632e`](https://github.com/nodejs/node/commit/1702d2632e)] - **doc**: fix format in `esm.md` (Pop Moore) [#53170](https://github.com/nodejs/node/pull/53170)
- \[[`070577e7d7`](https://github.com/nodejs/node/commit/070577e7d7)] - **doc**: fix wrong variable name in example of `timers.tick()` (Deokjin Kim) [#53147](https://github.com/nodejs/node/pull/53147)
- \[[`7147c1df1f`](https://github.com/nodejs/node/commit/7147c1df1f)] - **doc**: fix wrong function name in example of `context.plan()` (Deokjin Kim) [#53140](https://github.com/nodejs/node/pull/53140)
- \[[`cf47384148`](https://github.com/nodejs/node/commit/cf47384148)] - **doc**: add note for windows users and symlinks (Aviv Keller) [#53117](https://github.com/nodejs/node/pull/53117)
- \[[`088dff1074`](https://github.com/nodejs/node/commit/088dff1074)] - **doc**: move all TLS-PSK documentation to its section (Alba Mendez) [#35717](https://github.com/nodejs/node/pull/35717)
- \[[`db5dd0c6df`](https://github.com/nodejs/node/commit/db5dd0c6df)] - **doc**: add StefanStojanovic to collaborators (StefanStojanovic) [#53118](https://github.com/nodejs/node/pull/53118)
- \[[`0f0bc98ad7`](https://github.com/nodejs/node/commit/0f0bc98ad7)] - **doc**: improve ninja build for --built-in-modules-path (jakecastelli) [#53007](https://github.com/nodejs/node/pull/53007)
- \[[`4c65c52d30`](https://github.com/nodejs/node/commit/4c65c52d30)] - **doc**: avoid hiding by navigation bar in anchor jumping (Cloyd Lau) [#45131](https://github.com/nodejs/node/pull/45131)
- \[[`63fcbcfd62`](https://github.com/nodejs/node/commit/63fcbcfd62)] - **doc**: remove unavailable youtube link in pull requests (Deokjin Kim) [#52982](https://github.com/nodejs/node/pull/52982)
- \[[`77fd504636`](https://github.com/nodejs/node/commit/77fd504636)] - **doc**: add missing supported timer values in `timers.enable()` (Deokjin Kim) [#52969](https://github.com/nodejs/node/pull/52969)
- \[[`6708536b03`](https://github.com/nodejs/node/commit/6708536b03)] - **fs**: fix cp dir/non-dir mismatch error messages (Mathis Wiehl) [#53150](https://github.com/nodejs/node/pull/53150)
- \[[`6165894774`](https://github.com/nodejs/node/commit/6165894774)] - **fs**: mark recursive cp methods as stable (Théo LUDWIG) [#53127](https://github.com/nodejs/node/pull/53127)
- \[[`7940db7be1`](https://github.com/nodejs/node/commit/7940db7be1)] - **fs**: remove basename in favor of std::filesystem (Yagiz Nizipli) [#53062](https://github.com/nodejs/node/pull/53062)
- \[[`505e9a425b`](https://github.com/nodejs/node/commit/505e9a425b)] - **lib**: fix misleading argument of validateUint32 (Tobias Nießen) [#53307](https://github.com/nodejs/node/pull/53307)
- \[[`98ae1ebdd6`](https://github.com/nodejs/node/commit/98ae1ebdd6)] - **lib**: fix the name of the fetch global function (Gabriel Bota) [#53227](https://github.com/nodejs/node/pull/53227)
- \[[`fe007cd1b4`](https://github.com/nodejs/node/commit/fe007cd1b4)] - **lib**: allow CJS source map cache to be reclaimed (Chengzhong Wu) [#51711](https://github.com/nodejs/node/pull/51711)
- \[[`040be4a7b4`](https://github.com/nodejs/node/commit/040be4a7b4)] - **lib**: do not call callback if socket is closed (theanarkh) [#52829](https://github.com/nodejs/node/pull/52829)
- \[[`ee56aecced`](https://github.com/nodejs/node/commit/ee56aecced)] - **(SEMVER-MINOR)** **lib**: add EventSource Client (Aras Abbasi) [#51575](https://github.com/nodejs/node/pull/51575)
- \[[`6413769bc7`](https://github.com/nodejs/node/commit/6413769bc7)] - **(SEMVER-MINOR)** **lib**: replace MessageEvent with undici's (Matthew Aitken) [#52370](https://github.com/nodejs/node/pull/52370)
- \[[`879679e5a3`](https://github.com/nodejs/node/commit/879679e5a3)] - **lib,doc**: replace references to import assertions (Michaël Zasso) [#52998](https://github.com/nodejs/node/pull/52998)
- \[[`062a0c6f67`](https://github.com/nodejs/node/commit/062a0c6f67)] - **meta**: bump ossf/scorecard-action from 2.3.1 to 2.3.3 (dependabot\[bot]) [#53248](https://github.com/nodejs/node/pull/53248)
- \[[`e59b744b30`](https://github.com/nodejs/node/commit/e59b744b30)] - **meta**: bump actions/checkout from 4.1.4 to 4.1.6 (dependabot\[bot]) [#53247](https://github.com/nodejs/node/pull/53247)
- \[[`96924f48a0`](https://github.com/nodejs/node/commit/96924f48a0)] - **meta**: bump github/codeql-action from 3.25.3 to 3.25.7 (dependabot\[bot]) [#53246](https://github.com/nodejs/node/pull/53246)
- \[[`b7f5662dee`](https://github.com/nodejs/node/commit/b7f5662dee)] - **meta**: bump codecov/codecov-action from 4.3.1 to 4.4.1 (dependabot\[bot]) [#53244](https://github.com/nodejs/node/pull/53244)
- \[[`e079967eb4`](https://github.com/nodejs/node/commit/e079967eb4)] - **meta**: remove `initializeCommand` from devcontainer (Aviv Keller) [#53137](https://github.com/nodejs/node/pull/53137)
- \[[`3afeced572`](https://github.com/nodejs/node/commit/3afeced572)] - **meta**: move one or more collaborators to emeritus (Node.js GitHub Bot) [#53065](https://github.com/nodejs/node/pull/53065)
- \[[`4b9cdea8a6`](https://github.com/nodejs/node/commit/4b9cdea8a6)] - _**Revert**_ "**module**: have a single hooks thread for all workers" (Matteo Collina) [#53183](https://github.com/nodejs/node/pull/53183)
- \[[`b5c30e2f5e`](https://github.com/nodejs/node/commit/b5c30e2f5e)] - **(SEMVER-MINOR)** **module**: print amount of load time of a cjs module (Vinicius Lourenço) [#52213](https://github.com/nodejs/node/pull/52213)
- \[[`4cdb05a7a2`](https://github.com/nodejs/node/commit/4cdb05a7a2)] - **module**: do not set CJS variables for Worker eval (Antoine du Hamel) [#53050](https://github.com/nodejs/node/pull/53050)
- \[[`a0766bdf0e`](https://github.com/nodejs/node/commit/a0766bdf0e)] - **(SEMVER-MINOR)** **net**: add new net.server.listen tracing channel (Paolo Insogna) [#53136](https://github.com/nodejs/node/pull/53136)
- \[[`374743cd4e`](https://github.com/nodejs/node/commit/374743cd4e)] - **(SEMVER-MINOR)** **process**: add process.getBuiltinModule(id) (Joyee Cheung) [#52762](https://github.com/nodejs/node/pull/52762)
- \[[`e66eb376a0`](https://github.com/nodejs/node/commit/e66eb376a0)] - **repl**: fix await object patterns without values (Luke Haas) [#53331](https://github.com/nodejs/node/pull/53331)
- \[[`cb1329a8cf`](https://github.com/nodejs/node/commit/cb1329a8cf)] - **src**: use v8::(Des|S)erializeInternalFieldsCallback (Joyee Cheung) [#53217](https://github.com/nodejs/node/pull/53217)
- \[[`1886fe99af`](https://github.com/nodejs/node/commit/1886fe99af)] - **src**: use \_\_FUNCSIG\_\_ on Windows in backtrace (Joyee Cheung) [#53135](https://github.com/nodejs/node/pull/53135)
- \[[`3bfce6c816`](https://github.com/nodejs/node/commit/3bfce6c816)] - **src**: use new V8 API to define stream accessor (Igor Sheludko) [#53084](https://github.com/nodejs/node/pull/53084)
- \[[`11f790d911`](https://github.com/nodejs/node/commit/11f790d911)] - **src**: do not use deprecated V8 API (ishell) [#53084](https://github.com/nodejs/node/pull/53084)
- \[[`6b1731cbcc`](https://github.com/nodejs/node/commit/6b1731cbcc)] - **src**: convert all endian checks to constexpr (Tobias Nießen) [#52974](https://github.com/nodejs/node/pull/52974)
- \[[`7aa9519ad4`](https://github.com/nodejs/node/commit/7aa9519ad4)] - **src**: fix external module env and kDisableNodeOptionsEnv (Rafael Gonzaga) [#52905](https://github.com/nodejs/node/pull/52905)
- \[[`838fe59787`](https://github.com/nodejs/node/commit/838fe59787)] - **src**: fix execArgv in worker (theanarkh) [#53029](https://github.com/nodejs/node/pull/53029)
- \[[`4a2c6ff05d`](https://github.com/nodejs/node/commit/4a2c6ff05d)] - **src**: reduce unnecessary `GetCwd` calls (Yagiz Nizipli) [#53064](https://github.com/nodejs/node/pull/53064)
- \[[`ec44965b49`](https://github.com/nodejs/node/commit/ec44965b49)] - **src**: simplify node modules traverse path (Yagiz Nizipli) [#53061](https://github.com/nodejs/node/pull/53061)
- \[[`190129b48e`](https://github.com/nodejs/node/commit/190129b48e)] - **src**: remove unused `base64_table_url` (Yagiz Nizipli) [#53040](https://github.com/nodejs/node/pull/53040)
- \[[`d750a3c5c4`](https://github.com/nodejs/node/commit/d750a3c5c4)] - **src**: remove calls to recently deprecated V8 APIs (Adam Klein) [#52996](https://github.com/nodejs/node/pull/52996)
- \[[`f1890abb18`](https://github.com/nodejs/node/commit/f1890abb18)] - **src**: replace deprecated GetImportAssertions V8 API (Michaël Zasso) [#52997](https://github.com/nodejs/node/pull/52997)
- \[[`4347bd2acb`](https://github.com/nodejs/node/commit/4347bd2acb)] - **src**: improve node::Dotenv declarations (Tobias Nießen) [#52973](https://github.com/nodejs/node/pull/52973)
- \[[`e26166f30b`](https://github.com/nodejs/node/commit/e26166f30b)] - **src,permission**: handle process.chdir on pm (Rafael Gonzaga) [#53175](https://github.com/nodejs/node/pull/53175)
- \[[`75e5612fae`](https://github.com/nodejs/node/commit/75e5612fae)] - **(SEMVER-MINOR)** **src,permission**: --allow-wasi & prevent WASI exec (Rafael Gonzaga) [#53124](https://github.com/nodejs/node/pull/53124)
- \[[`7c66b27407`](https://github.com/nodejs/node/commit/7c66b27407)] - **stream**: micro-optimize writable condition (Orgad Shaneh) [#53189](https://github.com/nodejs/node/pull/53189)
- \[[`a656cf6bc8`](https://github.com/nodejs/node/commit/a656cf6bc8)] - **stream**: fix memory usage regression in writable (Orgad Shaneh) [#53188](https://github.com/nodejs/node/pull/53188)
- \[[`0e85a84fdc`](https://github.com/nodejs/node/commit/0e85a84fdc)] - **test**: fix test when compiled without engine support (Richard Lau) [#53232](https://github.com/nodejs/node/pull/53232)
- \[[`cebbd83e47`](https://github.com/nodejs/node/commit/cebbd83e47)] - **test**: update TLS trace tests for OpenSSL >= 3.2 (Richard Lau) [#53229](https://github.com/nodejs/node/pull/53229)
- \[[`45c1eb19f1`](https://github.com/nodejs/node/commit/45c1eb19f1)] - _**Revert**_ "**test**: skip v8-updates/test-linux-perf-logger" (Luke Albao) [#52869](https://github.com/nodejs/node/pull/52869)
- \[[`c1138db3c1`](https://github.com/nodejs/node/commit/c1138db3c1)] - **test**: unskip v8-updates/test-linux-perf-logger (Luke Albao) [#52869](https://github.com/nodejs/node/pull/52869)
- \[[`65b64cf0f1`](https://github.com/nodejs/node/commit/65b64cf0f1)] - **test**: fix Windows native test suites (Stefan Stojanovic) [#53173](https://github.com/nodejs/node/pull/53173)
- \[[`9a47792cd1`](https://github.com/nodejs/node/commit/9a47792cd1)] - **test**: skip `test-setproctitle` when `ps` is not available (Antoine du Hamel) [#53104](https://github.com/nodejs/node/pull/53104)
- \[[`a371dea699`](https://github.com/nodejs/node/commit/a371dea699)] - **test**: increase allocation so it fails for the test (Adam Majer) [#53099](https://github.com/nodejs/node/pull/53099)
- \[[`3ce7a9a1b5`](https://github.com/nodejs/node/commit/3ce7a9a1b5)] - **test**: remove timers from test-tls-socket-close (Luigi Pinca) [#53019](https://github.com/nodejs/node/pull/53019)
- \[[`494fa542af`](https://github.com/nodejs/node/commit/494fa542af)] - **test**: replace `.substr` with `.slice` (Antoine du Hamel) [#53070](https://github.com/nodejs/node/pull/53070)
- \[[`3f7d55b7db`](https://github.com/nodejs/node/commit/3f7d55b7db)] - **test**: add AbortController to knownGlobals (Luigi Pinca) [#53020](https://github.com/nodejs/node/pull/53020)
- \[[`c61f909ab6`](https://github.com/nodejs/node/commit/c61f909ab6)] - **test,doc**: enable running embedtest for Windows (Vladimir Morozov) [#52646](https://github.com/nodejs/node/pull/52646)
- \[[`2d1ecbf827`](https://github.com/nodejs/node/commit/2d1ecbf827)] - **test_runner**: calculate executed lines using source map (Moshe Atlow) [#53315](https://github.com/nodejs/node/pull/53315)
- \[[`d4f5f80f6c`](https://github.com/nodejs/node/commit/d4f5f80f6c)] - **test_runner**: handle file rename and deletion under watch mode (jakecastelli) [#53114](https://github.com/nodejs/node/pull/53114)
- \[[`07c601e32f`](https://github.com/nodejs/node/commit/07c601e32f)] - **test_runner**: refactor to use min/max of `validateInteger` (Deokjin Kim) [#53148](https://github.com/nodejs/node/pull/53148)
- \[[`8c6dffc269`](https://github.com/nodejs/node/commit/8c6dffc269)] - **(SEMVER-MINOR)** **test_runner**: add snapshot testing (Colin Ihrig) [#53169](https://github.com/nodejs/node/pull/53169)
- \[[`f6d2af8ee7`](https://github.com/nodejs/node/commit/f6d2af8ee7)] - **(SEMVER-MINOR)** **test_runner**: add context.fullName (Colin Ihrig) [#53169](https://github.com/nodejs/node/pull/53169)
- \[[`7a67ecf161`](https://github.com/nodejs/node/commit/7a67ecf161)] - **(SEMVER-MINOR)** **test_runner**: support module mocking (Colin Ihrig) [#52848](https://github.com/nodejs/node/pull/52848)
- \[[`3ff174f2bf`](https://github.com/nodejs/node/commit/3ff174f2bf)] - **test_runner**: fix t.assert methods (Colin Ihrig) [#53049](https://github.com/nodejs/node/pull/53049)
- \[[`e2211a07c2`](https://github.com/nodejs/node/commit/e2211a07c2)] - **test_runner**: avoid error when coverage line not found (Moshe Atlow) [#53000](https://github.com/nodejs/node/pull/53000)
- \[[`c249289121`](https://github.com/nodejs/node/commit/c249289121)] - **test_runner,doc**: align documentation with actual stdout/stderr behavior (Moshe Atlow) [#53131](https://github.com/nodejs/node/pull/53131)
- \[[`5110b19a07`](https://github.com/nodejs/node/commit/5110b19a07)] - **tls**: fix negative sessionTimeout handling (Tobias Nießen) [#53002](https://github.com/nodejs/node/pull/53002)
- \[[`0ecb770331`](https://github.com/nodejs/node/commit/0ecb770331)] - **tools**: remove no-goma arg from make-v8 script (Michaël Zasso) [#53336](https://github.com/nodejs/node/pull/53336)
- \[[`e7f3a3c296`](https://github.com/nodejs/node/commit/e7f3a3c296)] - **tools**: use sccache Github action (Moshe Atlow) [#53316](https://github.com/nodejs/node/pull/53316)
- \[[`98cc094bc5`](https://github.com/nodejs/node/commit/98cc094bc5)] - **tools**: update eslint to 9.4.0 (Node.js GitHub Bot) [#53298](https://github.com/nodejs/node/pull/53298)
- \[[`6409b1fe65`](https://github.com/nodejs/node/commit/6409b1fe65)] - **tools**: update gyp-next to 0.18.1 (Node.js GitHub Bot) [#53251](https://github.com/nodejs/node/pull/53251)
- \[[`86e80dcb9b`](https://github.com/nodejs/node/commit/86e80dcb9b)] - **tools**: move webcrypto into no-restricted-properties (Zihong Qu) [#53023](https://github.com/nodejs/node/pull/53023)
- \[[`6022346f0e`](https://github.com/nodejs/node/commit/6022346f0e)] - **tools**: update error message for Type Error (Aviv Keller) [#53047](https://github.com/nodejs/node/pull/53047)
- \[[`c1b3e0ed6f`](https://github.com/nodejs/node/commit/c1b3e0ed6f)] - _**Revert**_ "**tools**: add --certify-safe to nci-ci" (Antoine du Hamel) [#53098](https://github.com/nodejs/node/pull/53098)
- \[[`9f764a873c`](https://github.com/nodejs/node/commit/9f764a873c)] - **tools**: update ESLint to v9 and use flat config (Michaël Zasso) [#52780](https://github.com/nodejs/node/pull/52780)
- \[[`2859f4c027`](https://github.com/nodejs/node/commit/2859f4c027)] - **watch**: fix variable naming (jakecastelli) [#53101](https://github.com/nodejs/node/pull/53101)

Windows 32-bit Installer: https://nodejs.org/dist/v22.3.0/node-v22.3.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v22.3.0/node-v22.3.0-x64.msi \
Windows ARM 64-bit Installer: https://nodejs.org/dist/v22.3.0/node-v22.3.0-arm64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v22.3.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v22.3.0/win-x64/node.exe \
Windows ARM 64-bit Binary: https://nodejs.org/dist/v22.3.0/win-arm64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v22.3.0/node-v22.3.0.pkg \
macOS Apple Silicon 64-bit Binary: https://nodejs.org/dist/v22.3.0/node-v22.3.0-darwin-arm64.tar.gz \
macOS Intel 64-bit Binary: https://nodejs.org/dist/v22.3.0/node-v22.3.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v22.3.0/node-v22.3.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v22.3.0/node-v22.3.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v22.3.0/node-v22.3.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v22.3.0/node-v22.3.0-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v22.3.0/node-v22.3.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v22.3.0/node-v22.3.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v22.3.0/node-v22.3.0.tar.gz \
Other release files: https://nodejs.org/dist/v22.3.0/ \
Documentation: https://nodejs.org/docs/v22.3.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

8c349a9164f25d8a1de886a47db045b50ae11aba4c4c1e1a4d1ac34a1e5d20e3  node-v22.3.0-aix-ppc64.tar.gz
69ee53b3262ae727453d97f8e0fb3ba51363065351fcf2a389d0bdab688c021c  node-v22.3.0-arm64.msi
b6723f1e4972af1ca8a7ef9ec63305ee8cd4380fce3071e0e1630dfe055d77e3  node-v22.3.0-darwin-arm64.tar.gz
b63eac38d610ffcd9ae35340f3a28d16f566d44441845d1f73dd3e5294d0dcae  node-v22.3.0-darwin-arm64.tar.xz
7fe139f9d769d65c27212f8be8f858e1ee522edf3a66eed1d08d42ba102995f8  node-v22.3.0-darwin-x64.tar.gz
a633700fae61e3f078be40561df241ead763d30cfdc463b623e8b895c36bb481  node-v22.3.0-darwin-x64.tar.xz
d2460c13bb1b723d0773b3c18162ec8d3bc15c18c25643520c1f03d80e014999  node-v22.3.0-headers.tar.gz
6f62ffb3f189a4797471f0334888e2471ee7352e1c5d3bbfc6feaf2175a990fc  node-v22.3.0-headers.tar.xz
0e25b9a4bc78080de826a90dff82743bec6d9c5085186e75521dc195c8be9ce3  node-v22.3.0-linux-arm64.tar.gz
c0324bbcfd5627bdcdc18830e563af1742c2173e86297a502a86db54c15bba70  node-v22.3.0-linux-arm64.tar.xz
46b640d23708f899689059cc2a8431842c2e3ad50a9144828ddabea5e1a7c3ae  node-v22.3.0-linux-armv7l.tar.gz
973731137ea1ab9415115b9ec447d34628c5aa45c33115df1a2dfb20e7f79b5f  node-v22.3.0-linux-armv7l.tar.xz
a01c2263a01efa7c6efa3607d202487127e268d73b68b6cce9c44a481412ece0  node-v22.3.0-linux-ppc64le.tar.gz
50c91e0b1ba7472e3ff609ecd503810308c990a1fd1ea1a721f9029c01c9d2a7  node-v22.3.0-linux-ppc64le.tar.xz
3aa6a22f525a6f8ddb0fd2ce3646414c316a41cab6bdaac812276196607bc187  node-v22.3.0-linux-s390x.tar.gz
decbeb778aa4e490ba4b60a7d13ef92f6db4647ccd2d452d7e52067b5503d4a9  node-v22.3.0-linux-s390x.tar.xz
a6d4fbf4306a883b8e1d235a8a890be84b9d95d2d39b929520bed64da41ce540  node-v22.3.0-linux-x64.tar.gz
33429139d4c4416439bf023b2eb2dc257da188fd793b64f21c8c03a0f04a5840  node-v22.3.0-linux-x64.tar.xz
a76b8e529e5dc162f9739aa25d380b416e1bacc29cf36f2b178db24764ba359d  node-v22.3.0.pkg
6326484853093ab6b8f361a267445f4a5bff469042cda11a3585497b13136b55  node-v22.3.0.tar.gz
bfb85bd1dca517761f9046d61600f830d19935d6d6c36eded01578a19326104c  node-v22.3.0.tar.xz
57a44a7c956581e2939c8c040cb49f72dfa148c4e97178e54be67e78cc45ca69  node-v22.3.0-win-arm64.7z
727426f9a97238d2dc269fb00bbe50c77629f76adb99a19d68abc41e8cdb4bc5  node-v22.3.0-win-arm64.zip
5eead5f9946b5381ffb36430970a2e3d0bcf90383a9432ea76e93d0efdc70691  node-v22.3.0-win-x64.7z
3dadc19ba6b36c6fb93aeda08247107fdb2ed55c24831304566d32de6b6080d7  node-v22.3.0-win-x64.zip
e8e34fbef56216f8d58499215d3c5220ce429c455ee2bfa97b29bb0e9ba57e1b  node-v22.3.0-win-x86.7z
a56e1446e45adbfc716023c8e903eef829e84e5ac8aae3a65b455213bef9cdb1  node-v22.3.0-win-x86.zip
da5b1cbc773371fd11415a893ce229f51052e9aa9b656ddcbd79730ce4b93a7b  node-v22.3.0-x64.msi
ae86fec0828744ac9c9a9b0186cd984e64d45602b267deac6fc140eb1c13262f  node-v22.3.0-x86.msi
17608e0e2c587fca141bfc43ce9299db192b8506def389b8e30a9935e6fc6f83  win-arm64/node.exe
30e63a6726cda6539eeb37c311adf915bccd5c1462723b97a6c07ac91e8ae728  win-arm64/node.lib
8e71a3f8a27a14f0c0f5198aa0e34d9c58d0bf39cd3b0e5e89c3079884c427b3  win-arm64/node_pdb.7z
483e6e8e418fac0c311b2ca6ca5414dbbf61c8da1c1ced7a7736fc9c8a44ca94  win-arm64/node_pdb.zip
b3e0d6bf8224d43d5c6e756c8ebaffe1daef0d5ed0eeba40eef0ca62f1c4232a  win-x64/node.exe
c4d08d45267da3625a30730bf5c8e41518f25d9809179feb267f1b393f5c5f05  win-x64/node.lib
fdc88d7ef4ee2bee3bb94947786ea425a30c2d5fb26b0ad25cb33cad165c8a5c  win-x64/node_pdb.7z
689c6831018340256aa33e2cd0a5da8168c835e5d3070dd0688803c0cd1157cd  win-x64/node_pdb.zip
195a4cc5eb1d9235043a34f423a732d54f73d9b2b7404c86ef10ff1c17dff6d6  win-x86/node.exe
fc3bf3c1e561da1e1c152be9aa5ed1bce8d263a5124841a4ba41ebc37c727f3e  win-x86/node.lib
b471579503255732d862c8eaa9a3dff77cf2ef8e7c80ccb484b5e46f83cd6438  win-x86/node_pdb.7z
fadd1b6e3071a8d095913aa959be1f1a701621cc9cc7f6a685bcf3c74b884c84  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQGzBAEBCAAdFiEEiQwI24V5Fi/uDfnbi+q0389VXvQFAmZoniAACgkQi+q0389V
XvRTLAwAmIfWRtJ7u9cG1Bgk+2J2ybnCQeGjpwOWy8WH4AWPhS3BVvDXy1WVu/2d
Yo0rgAnV+DQz8Bvf0MSiTKvqzq3QJRcN5f8eavYW5dUFUnieegXKrYbED5UPHUyK
ou7XkzJ960VrbuUz9a8jmVTI6y1Kpyaft4Zq4fXURDb3TD9XaWHMofNSLCCf1My9
0gJoCJWKqRS2uFm/yHjAGSFGprZQGFavkax0aIrTAOFsTkWj6nacNWCFSJEbMWhK
+zAYoK7/hXtZn1jUzLDDeamlWPydLLn4kdTpYEdml6E5oXIGWqm5tbe9o7Qmv0wp
dssn7ZPWBr1cmA+obtC639xPv0ubRJfR4D9zm1bzQktaxZFMOUf0sthXlrHaJ8+q
bztXvPHtQ75JxGOV7QGMcrmgj4ikt7af8b2GNgGRgRvp5ySA81MaRwYajkqELKoU
eoO5gPx9FOwwmRW1yMMerv6GylnPei+4XJaDmAxO4yNnIdqSISt4BG+IpOoUKVNY
SMeq61tC
=ZPUe
-----END PGP SIGNATURE-----
```
