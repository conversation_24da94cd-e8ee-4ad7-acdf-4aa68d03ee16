---
date: '2025-01-21T17:06:07.900Z'
category: release
title: Node v23.6.1 (Current)
layout: blog-post
author: <PERSON>
---

## 2025-01-21, Version 23.6.1 (Current), @RafaelGSS

This is a security release.

### Notable Changes

- CVE-2025-23083 - src,loader,permission: throw on InternalWorker use when permission model is enabled (High)
- CVE-2025-23085 - src: fix HTTP2 mem leak on premature close and ERR_PROTO (Medium)
- CVE-2025-23084 - path: fix path traversal in normalize() on Windows (Medium)

Dependency update:

- CVE-2025-22150 - Use of Insufficiently Random Values in undici fetch() (Medium)

### Commits

- \[[`f2ad4d3af8`](https://github.com/nodejs/node/commit/f2ad4d3af8)] - **(CVE-2025-22150)** **deps**: update undici to v6.21.1 (<PERSON>) [nodejs-private/node-private#654](https://github.com/nodejs-private/node-private/pull/654)
- \[[`0afc6f9600`](https://github.com/nodejs/node/commit/0afc6f9600)] - **(CVE-2025-23084)** **path**: fix path traversal in normalize() on Windows (RafaelGSS) [nodejs-private/node-private#555](https://github.com/nodejs-private/node-private/pull/555)
- \[[`3c7686163e`](https://github.com/nodejs/node/commit/3c7686163e)] - **(CVE-2025-23085)** **src**: fix HTTP2 mem leak on premature close and ERR_PROTO (RafaelGSS) [nodejs-private/node-private#650](https://github.com/nodejs-private/node-private/pull/650)
- \[[`51938f023a`](https://github.com/nodejs/node/commit/51938f023a)] - **(CVE-2025-23083)** **src,loader,permission**: throw on InternalWorker use (RafaelGSS) [nodejs-private/node-private#629](https://github.com/nodejs-private/node-private/pull/629)

Windows 64-bit Installer: https://nodejs.org/dist/v23.6.1/node-v23.6.1-x64.msi \
Windows ARM 64-bit Installer: https://nodejs.org/dist/v23.6.1/node-v23.6.1-arm64.msi \
Windows 64-bit Binary: https://nodejs.org/dist/v23.6.1/win-x64/node.exe \
Windows ARM 64-bit Binary: https://nodejs.org/dist/v23.6.1/win-arm64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v23.6.1/node-v23.6.1.pkg \
macOS Apple Silicon 64-bit Binary: https://nodejs.org/dist/v23.6.1/node-v23.6.1-darwin-arm64.tar.gz \
macOS Intel 64-bit Binary: https://nodejs.org/dist/v23.6.1/node-v23.6.1-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v23.6.1/node-v23.6.1-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v23.6.1/node-v23.6.1-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v23.6.1/node-v23.6.1-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v23.6.1/node-v23.6.1-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v23.6.1/node-v23.6.1-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v23.6.1/node-v23.6.1-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v23.6.1/node-v23.6.1.tar.gz \
Other release files: https://nodejs.org/dist/v23.6.1/ \
Documentation: https://nodejs.org/docs/v23.6.1/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

1a05b4193005a912b99674ee1198808ed3b38850224928a8966e298a33b50471  node-v23.6.1-aix-ppc64.tar.gz
a3b0bf4eaffe7af44c88a901bdeeedace5f03bfee25d56cbf393a4574ad8e36e  node-v23.6.1-arm64.msi
b87c90f1efff3f2f49070ce4714a11228a43331bb590b06fc7bcb36e530b2ea0  node-v23.6.1-darwin-arm64.tar.gz
631742b69fd30235b89847fb9f36216e003f1efe8f013919fd30109ebeac565e  node-v23.6.1-darwin-arm64.tar.xz
bcdc29188f2d8d7a129b88a6c19830ac91e53e64c3744b18f412dce533f67ef7  node-v23.6.1-darwin-x64.tar.gz
a011faa573727a959c9f916672f7bf4114d118c9adb6a593ef331167c273e5b5  node-v23.6.1-darwin-x64.tar.xz
113ef099f137331e2bc34c886f206827948c8312de563449f2366947a7f39d6c  node-v23.6.1-headers.tar.gz
11e295df3f37dcc663a7c2c9473b94b3674c0c847b32e150cf9f3f7308b13cea  node-v23.6.1-headers.tar.xz
8b221bb5ef173e9b6fecf4fc4a31696a775bbfe07ea45f8c7bdf88d7b9c9460f  node-v23.6.1-linux-arm64.tar.gz
e9a709ea4142c0c269d7e670e37e65cf549bf69d62342907cd15a49ba7da1748  node-v23.6.1-linux-arm64.tar.xz
3bdb2e31de44038f166531edb2f3d99278b6c36576392c4939762029efa53439  node-v23.6.1-linux-armv7l.tar.gz
892a1285c5245a869eda0b0d6e5428b563d33a705d44de1d1d07f44d78c7b344  node-v23.6.1-linux-armv7l.tar.xz
c3db627673540e4c1ffd6f2f112e5dd8e6d89eaeba4fbdfdaa90e071c11d58b1  node-v23.6.1-linux-ppc64le.tar.gz
ec825715a1a48e359cffdd299e98d28b5cef27c4e7acfadb3440e9ab4b75fa42  node-v23.6.1-linux-ppc64le.tar.xz
68e8c2d68a749616ae82605ae85871a3e281c6ae0b68d5cb4ffa68320c949104  node-v23.6.1-linux-s390x.tar.gz
6d3c768b8ee3a648387a79e0ad2b43fc3e736f96eac924cff7e9aae01dc1f9c9  node-v23.6.1-linux-s390x.tar.xz
0aaa37396638ae6e71af66927dcc41414327051d5a8030d3057db1ceb7719854  node-v23.6.1-linux-x64.tar.gz
9387c4bf8f175e81cb2f004f3f4b2cd96abfb708df3755142e878effe035fcc5  node-v23.6.1-linux-x64.tar.xz
ae1746cea80934d7ba3d0a9a10e492822cbc23dc80945339090c39e2715b8b2d  node-v23.6.1.pkg
35c0f7957085083071fcf01c7eaea953fba98f17afc4d7c189e3ef56925b453c  node-v23.6.1.tar.gz
fefa49dede8733018ada4e30f885808cc4e22167b8ae3233c6d6a23737aff76f  node-v23.6.1.tar.xz
eb52ed2507285f3f6ad19553b28e0a6425f00ee7190e2cc6dc68709201e30403  node-v23.6.1-win-arm64.7z
74599c4c9c61f666497b2a501c92b463566681587beba69da5368b9ecfdeaac5  node-v23.6.1-win-arm64.zip
597e75859698e7062481a6da92e3c54108099f57c45e9b391d64a0afa642e06e  node-v23.6.1-win-x64.7z
9be8fe4eb81c6108ffca066590c160d9b6c94080c24b4dfa119eb4e3ae187aa8  node-v23.6.1-win-x64.zip
61ae8aee959557eda5056d07cd8ec58cab47cff631dcf18465b1dd31191f4752  node-v23.6.1-x64.msi
48b260b200236fe3960d81e39cf130012c8a877868ffdf2eba6a78afef44a0de  win-arm64/node.exe
46a85ee5432885387948e02ced2499975b34cc2080ea3ed4e99b6c7323d38d74  win-arm64/node.lib
d1471a48b2852173a87f8aa9644394986b8d924a087c939dd31e03a0be4d87f0  win-arm64/node_pdb.7z
7183292cab61ea6b6aa7f885a217b7b921f9f1d06fac4d475ec60386315988fb  win-arm64/node_pdb.zip
0cb029d3c8a66969e7c06ee01b7db1298b9300ef79f54b95505e532b24058536  win-x64/node.exe
4a54d5aeab30bb14dbad9260f6430c81f1e1afb430c68c40f6e5fec7ce288e2e  win-x64/node.lib
98aa064c688818d7c15d32341163e046a007ae9b9ed4a660fb99a6f01ac3b9ba  win-x64/node_pdb.7z
1bda974a08110f08e5b11bb04277da2728e27756cf94c154cfcce72fd6a3dcc6  win-x64/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQGzBAEBCAAdFiEEiQwI24V5Fi/uDfnbi+q0389VXvQFAmeP0ocACgkQi+q0389V
XvQgMgwAjohyA6mtco8lX/0oKW3+T5q4EWct2IVaF27qkFxkB5fN7YsHAKqeuBUK
zSAb/9ga9XJZgwv2zDl/NQJwzgvFZL1sSxViY6p8DqGGFev5Ueeoaapplnrg2C/k
KPggMCoZmBuJv2uYKVRVkrDxUfGB3K38P9wBc8k3LceSiehgS85IR4z2t4dn0a0q
RtkgZ2wtOD4KWPEIPpw8pFPdOemuB0eD4l0mG6vsWS+Rj/hSccIoRwMZ54GIbXF+
xMRL0X9eA01Izlqw8awNS9KKULni8T3C+4jMOc4BoY8QBXVrF+bgEnNnw/h967SI
iyYV2aIJQG9jTlhnfBkSFyNB0PM1KtqIPsKmLGD1SZqKoBFEyE74F2aB0P/IOnt6
OeKf5Rc1/IZ4BbMj626gMkvubClSdtHR/qmKri5AerQGDnczOV+k4ROhj/MhdwgS
A0er3cpzVbtB716+7d9rIOe8CrbXJY5y6eJMtvxyBwqvD5d9I1oplKYqPISjkjK5
HPq8a1pt
=MTqA
-----END PGP SIGNATURE-----
```
