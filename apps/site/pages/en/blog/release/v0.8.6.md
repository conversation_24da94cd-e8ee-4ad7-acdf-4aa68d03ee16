---
date: '2012-08-07T19:03:55.905Z'
category: release
title: Version 0.8.6 (stable)
layout: blog-post
author: The Node.js Project
---

<!--lint disable prohibited-strings-->
<!--lint disable maximum-line-length-->
<!--lint disable no-literal-urls-->
<!--lint disable no-shortcut-reference-link-->

2012.08.07, Version 0.8.6 (Stable)

This is the first release to include binary distributions for all
supported Unix operating systems (Linux, Darwin, and SmartOS). To use
the binary distribution tarballs, you can unpack them directly into a
destination directory:

```
cd ~/node/ # or /usr/local if you're feeling brave
tar xzvf /path/to/binary.tar.gz --strip=1
```

This is an experimental feature. Please use it and provide feedback.

- npm: Upgrade to v1.1.48

- Add 'make binary' to build binary tarballs for all Unixes (<PERSON>)

- zlib: Emit 'close' on destroy(). (<PERSON>)

- child_process: Fix stdout=null when stdio=['pipe'] (<PERSON>)

- installer: prevent ETXTBSY errors (<PERSON>)

- installer: honor --without-npm, default install path (<PERSON>)

- net: make pause work with connecting sockets (<PERSON>)

- installer: fix cross-compile installs (<PERSON> Noordhuis)

- net: fix .listen({fd:0}) (Ben Noordhuis)

- windows: map WSANO_DATA to UV_ENOENT (Bert Belder)

Source Code: https://nodejs.org/dist/v0.8.6/node-v0.8.6.tar.gz

Macintosh Installer (Universal): https://nodejs.org/dist/v0.8.6/node-v0.8.6.pkg

Windows Installer: https://nodejs.org/dist/v0.8.6/node-v0.8.6-x86.msi

Windows x64 Installer: https://nodejs.org/dist/v0.8.6/x64/node-v0.8.6-x64.msi

Windows x64 Files: https://nodejs.org/dist/v0.8.6/x64/

Linux 32-bit Binary Package: https://nodejs.org/dist/v0.8.6/node-v0.8.6-linux-x86.tar.gz

Linux 64-bit Binary Package: https://nodejs.org/dist/v0.8.6/node-v0.8.6-linux-x64.tar.gz

Solaris 32-bit Binary Package: https://nodejs.org/dist/v0.8.6/node-v0.8.6-sunos-x86.tar.gz

Solaris 64-bit Binary Package: https://nodejs.org/dist/v0.8.6/node-v0.8.6-sunos-x64.tar.gz

Other release files: https://nodejs.org/dist/v0.8.6/

Website: https://nodejs.org/docs/v0.8.6/

Documentation: https://nodejs.org/docs/v0.8.6/api/

Shasums:

```
c23a57601150b3ec59aeeb0eef607d9e430e17c2  node-v0.8.6-darwin-x64.tar.gz
8f7e4e837f61991eff4605678ab27c82e854bc38  node-v0.8.6-darwin-x86.tar.gz
32ce9d28d6a294878ce9ee8f23b6fa7ecb3130e7  node-v0.8.6-linux-x64.tar.gz
6f71518f044705ff1a7d9400a573906a99c5834c  node-v0.8.6-linux-x86.tar.gz
ec9c02e9713a81d8f4848924cc38e5ed28a06fc4  node-v0.8.6-sunos-x64.tar.gz
ac96cc4ce3eee4dc54ef7936ad4fd8eb04fbe359  node-v0.8.6-sunos-x86.tar.gz
0a2aca229c9cb2ec4a4a82ff88de7ea0868d1890  node-v0.8.6-x86.msi
84127d73a968f5951a9682b592a79779d1396c9e  node-v0.8.6.pkg
34c7ad2bb5450653748c65840155852d67742258  node-v0.8.6.tar.gz
42f3b792326efdfc9b0d95eebd7f9f716cadb1c0  node.exe
fc56e816081ebef450ce7ed92bfd543d53191ac3  node.exp
e91f1648e4e8f7586790443248326222101c286c  node.lib
8106b33d1cdae69103ca07b16c7f5d690308d751  node.pdb
6226474859e1cf2f1314d92b6207183bb36c6007  x64/node-v0.8.6-x64.msi
3c1ac597956ea9f1e7eab62f85a23e3e436cd0e8  x64/node.exe
599df091faecff536f52d17463c70e07cf9ed54f  x64/node.exp
70bac4dcb9f845c8c8cb9443ff09f839fc86aac7  x64/node.lib
eb59a0ed841c9e93c406b4c636b2048973cbfae4  x64/node.pdb
```
