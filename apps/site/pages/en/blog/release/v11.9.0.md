---
date: '2019-01-30T22:10:49.349Z'
category: release
title: Node v11.9.0 (Current)
layout: blog-post
author: <PERSON><PERSON><PERSON>
---

### Notable Changes

- **deps**:
  - OpenSSL has been updated to 1.1.1a, which is API/ABI compatible with the
    previous OpenSSL 1.1.0j. Note that while OpenSSL 1.1.1a supports TLS1.3,
    Node.js still does not. [#25381](https://github.com/nodejs/node/pull/25582))

### Commits

- [[`bc81a68f20`](https://github.com/nodejs/node/commit/bc81a68f20)] - **build**: make compress_json python3 compatible (<PERSON><PERSON><PERSON><PERSON><PERSON> (thefourtheye)) [#25582](https://github.com/nodejs/node/pull/25582)
- [[`30949f8dba`](https://github.com/nodejs/node/commit/30949f8dba)] - **build**: make configure.py compatible with python 3 (<PERSON><PERSON><PERSON><PERSON><PERSON> (thefourtheye)) [#25580](https://github.com/nodejs/node/pull/25580)
- [[`d4ec110c65`](https://github.com/nodejs/node/commit/d4ec110c65)] - **(SEMVER-MINOR)** **deps**: update archs files for OpenSSL-1.1.1a (Sam Roberts) [#25381](https://github.com/nodejs/node/pull/25381)
- [[`5225214d07`](https://github.com/nodejs/node/commit/5225214d07)] - **(SEMVER-MINOR)** **deps**: fix for non GNU assembler in AIX (Shigeki Ohtsu) [#25381](https://github.com/nodejs/node/pull/25381)
- [[`ad04d7bea1`](https://github.com/nodejs/node/commit/ad04d7bea1)] - **(SEMVER-MINOR)** **deps**: add only avx2 configs for OpenSSL-1.1.1 (Shigeki Ohtsu) [#25381](https://github.com/nodejs/node/pull/25381)
- [[`670f10053a`](https://github.com/nodejs/node/commit/670f10053a)] - **(SEMVER-MINOR)** **deps**: add s390 asm rules for OpenSSL-1.1.1 (Shigeki Ohtsu) [#25381](https://github.com/nodejs/node/pull/25381)
- [[`0a0f15f768`](https://github.com/nodejs/node/commit/0a0f15f768)] - **(SEMVER-MINOR)** **deps**: fix MacOS and Win build for OpenSSL-1.1.1 (Shigeki Ohtsu) [#25381](https://github.com/nodejs/node/pull/25381)
- [[`e2043999bd`](https://github.com/nodejs/node/commit/e2043999bd)] - **(SEMVER-MINOR)** **deps**: fix gyp/gypi for openssl-1.1.1 (Shigeki Ohtsu) [#25381](https://github.com/nodejs/node/pull/25381)
- [[`c581b9a253`](https://github.com/nodejs/node/commit/c581b9a253)] - **(SEMVER-MINOR)** **deps**: upgrade openssl sources to 1.1.1a (Sam Roberts) [#25381](https://github.com/nodejs/node/pull/25381)
- [[`c82f2445e5`](https://github.com/nodejs/node/commit/c82f2445e5)] - **dns**: use IDNA 2008 to encode non-ascii hostnames (Ben Noordhuis) [#25679](https://github.com/nodejs/node/pull/25679)
- [[`c56ddc7736`](https://github.com/nodejs/node/commit/c56ddc7736)] - **doc**: document uniqueness of worker.threadId (Anna Henningsen) [#25644](https://github.com/nodejs/node/pull/25644)
- [[`7c8d57d4a9`](https://github.com/nodejs/node/commit/7c8d57d4a9)] - **doc**: revise breaking changes material in COLLABORATOR_GUIDE (Rich Trott) [#25730](https://github.com/nodejs/node/pull/25730)
- [[`edc9ceb16e`](https://github.com/nodejs/node/commit/edc9ceb16e)] - **doc**: fix issue with worker_threads docs (Lee Byron) [#25712](https://github.com/nodejs/node/pull/25712)
- [[`1d6e18b128`](https://github.com/nodejs/node/commit/1d6e18b128)] - **doc**: fix http.Agent timeout option description (Luigi Pinca) [#25489](https://github.com/nodejs/node/pull/25489)
- [[`5d5c528120`](https://github.com/nodejs/node/commit/5d5c528120)] - **(SEMVER-MINOR)** **doc**: fix assembler requirement for OpenSSL-1.1.1 (Shigeki Ohtsu) [#25381](https://github.com/nodejs/node/pull/25381)
- [[`34bc69d376`](https://github.com/nodejs/node/commit/34bc69d376)] - **doc**: fix file extension on ESM file example (Eric Whitebloom) [#25692](https://github.com/nodejs/node/pull/25692)
- [[`b218b1204a`](https://github.com/nodejs/node/commit/b218b1204a)] - **doc**: remove outdated s_client information in tls.md (Rich Trott) [#25678](https://github.com/nodejs/node/pull/25678)
- [[`1aa7f4d72d`](https://github.com/nodejs/node/commit/1aa7f4d72d)] - **doc**: fix metadata for v11.8.0 doc changes (Richard Lau) [#25709](https://github.com/nodejs/node/pull/25709)
- [[`3c5a7a2f97`](https://github.com/nodejs/node/commit/3c5a7a2f97)] - **doc**: fix keyObject.symmetricSize to be keyObject.symmetricKeySize (Filip Skokan) [#25670](https://github.com/nodejs/node/pull/25670)
- [[`e47511943b`](https://github.com/nodejs/node/commit/e47511943b)] - **doc**: add metadata to report docs (Richard Lau) [#25708](https://github.com/nodejs/node/pull/25708)
- [[`237ec396d0`](https://github.com/nodejs/node/commit/237ec396d0)] - **doc**: fix 11.8.0 changelog (Myles Borins) [#25705](https://github.com/nodejs/node/pull/25705)
- [[`48149cfa3a`](https://github.com/nodejs/node/commit/48149cfa3a)] - **doc**: clarify what dns.setResolvers() affects (Sam Roberts) [#25570](https://github.com/nodejs/node/pull/25570)
- [[`3488f0df3b`](https://github.com/nodejs/node/commit/3488f0df3b)] - **doc**: link nextTick docs to the nextTick guide (Sam Roberts) [#25619](https://github.com/nodejs/node/pull/25619)
- [[`c93e5e1f65`](https://github.com/nodejs/node/commit/c93e5e1f65)] - **doc**: simplify process.binding() deprecation message (Rich Trott) [#25654](https://github.com/nodejs/node/pull/25654)
- [[`0640b09243`](https://github.com/nodejs/node/commit/0640b09243)] - **lib**: refactor policy code for readability (Anna Henningsen) [#25629](https://github.com/nodejs/node/pull/25629)
- [[`634cf131f4`](https://github.com/nodejs/node/commit/634cf131f4)] - **module**: do not use `process.exit()` (Anna Henningsen) [#25769](https://github.com/nodejs/node/pull/25769)
- [[`143274af38`](https://github.com/nodejs/node/commit/143274af38)] - **module**: silence ModuleJob unhandled rejection warnings (Anna Henningsen) [#25769](https://github.com/nodejs/node/pull/25769)
- [[`fc38b20c7c`](https://github.com/nodejs/node/commit/fc38b20c7c)] - **perf_hooks**: clean up GC listeners (Anna Henningsen) [#25647](https://github.com/nodejs/node/pull/25647)
- [[`f3179f7701`](https://github.com/nodejs/node/commit/f3179f7701)] - **policy**: ensure workers do not read fs for policy (Bradley Farias) [#25710](https://github.com/nodejs/node/pull/25710)
- [[`ee61ab6894`](https://github.com/nodejs/node/commit/ee61ab6894)] - **repl**: improve doc for disabling REPL history on Windows (Samuel D. Leslie) [#25672](https://github.com/nodejs/node/pull/25672)
- [[`ce28caf517`](https://github.com/nodejs/node/commit/ce28caf517)] - **report**: represent numbers as numbers (Anna Henningsen) [#25651](https://github.com/nodejs/node/pull/25651)
- [[`1dfdbc6cf7`](https://github.com/nodejs/node/commit/1dfdbc6cf7)] - **report**: refactor JSON writer (Anna Henningsen) [#25651](https://github.com/nodejs/node/pull/25651)
- [[`14bce1ea5a`](https://github.com/nodejs/node/commit/14bce1ea5a)] - **report**: do not use `uv_default_loop()` as fallback (Anna Henningsen) [#25652](https://github.com/nodejs/node/pull/25652)
- [[`152d633366`](https://github.com/nodejs/node/commit/152d633366)] - **src**: remove unused env\_ field from env.h (Daniel Bevenius) [#25784](https://github.com/nodejs/node/pull/25784)
- [[`c0951062b9`](https://github.com/nodejs/node/commit/c0951062b9)] - **src**: pass along errors from i18n converter instantiation (Anna Henningsen) [#25734](https://github.com/nodejs/node/pull/25734)
- [[`deebf10bd5`](https://github.com/nodejs/node/commit/deebf10bd5)] - **src**: pass along errors from vm data wrapper creation (Anna Henningsen) [#25734](https://github.com/nodejs/node/pull/25734)
- [[`8ee4810029`](https://github.com/nodejs/node/commit/8ee4810029)] - **src**: pass along errors from KeyObject instantiation (Anna Henningsen) [#25734](https://github.com/nodejs/node/pull/25734)
- [[`ced4e71504`](https://github.com/nodejs/node/commit/ced4e71504)] - **src**: pass along errors from perf obj instantiation (Anna Henningsen) [#25734](https://github.com/nodejs/node/pull/25734)
- [[`5add2b56ac`](https://github.com/nodejs/node/commit/5add2b56ac)] - **src**: pass along errors from process obj instantiation (Anna Henningsen) [#25734](https://github.com/nodejs/node/pull/25734)
- [[`2928672679`](https://github.com/nodejs/node/commit/2928672679)] - **src**: pass along errors from stream obj instantiation (Anna Henningsen) [#25734](https://github.com/nodejs/node/pull/25734)
- [[`ebcdbebcee`](https://github.com/nodejs/node/commit/ebcdbebcee)] - **src**: remove unused field in node_http2.h (gengjiawen) [#25727](https://github.com/nodejs/node/pull/25727)
- [[`6d9af41aef`](https://github.com/nodejs/node/commit/6d9af41aef)] - **src**: in-source comments and minor TLS cleanups (Sam Roberts) [#25713](https://github.com/nodejs/node/pull/25713)
- [[`09a10858f7`](https://github.com/nodejs/node/commit/09a10858f7)] - **src**: remove unnecessary call to SSL_get_mode (Sam Roberts) [#25711](https://github.com/nodejs/node/pull/25711)
- [[`86e79a521d`](https://github.com/nodejs/node/commit/86e79a521d)] - **src**: remove unused and unimplemented method in env.h (gengjiawen) [#25699](https://github.com/nodejs/node/pull/25699)
- [[`021d1975ff`](https://github.com/nodejs/node/commit/021d1975ff)] - **src**: fix macro duplicate declaration in env.h (gengjiawen) [#25703](https://github.com/nodejs/node/pull/25703)
- [[`845bcfa1ce`](https://github.com/nodejs/node/commit/845bcfa1ce)] - **src**: simplify inspector initialization in node::Start() (Joyee Cheung) [#25612](https://github.com/nodejs/node/pull/25612)
- [[`797111a69b`](https://github.com/nodejs/node/commit/797111a69b)] - **src**: avoid race condition in tracing code (Anna Henningsen) [#25624](https://github.com/nodejs/node/pull/25624)
- [[`b113332daf`](https://github.com/nodejs/node/commit/b113332daf)] - **src**: ensure no more platform foreground tasks after Deinit (Clemens Hammacher) [#25653](https://github.com/nodejs/node/pull/25653)
- [[`7cc51531a7`](https://github.com/nodejs/node/commit/7cc51531a7)] - **src**: remove has_experimental_policy option (Anna Henningsen) [#25628](https://github.com/nodejs/node/pull/25628)
- [[`4b43eeaf9a`](https://github.com/nodejs/node/commit/4b43eeaf9a)] - **src,test**: fix JSON escaping in node-report (Denys Otrishko) [#25626](https://github.com/nodejs/node/pull/25626)
- [[`af9592d6b1`](https://github.com/nodejs/node/commit/af9592d6b1)] - **test**: refactor test/common/report.js (cjihrig) [#25754](https://github.com/nodejs/node/pull/25754)
- [[`e2ee031060`](https://github.com/nodejs/node/commit/e2ee031060)] - **test**: move client renegotiation tests to parallel (Rich Trott) [#25757](https://github.com/nodejs/node/pull/25757)
- [[`b174dd7280`](https://github.com/nodejs/node/commit/b174dd7280)] - **test**: use fipsMode in test-crypto-fips (Daniel Bevenius) [#25563](https://github.com/nodejs/node/pull/25563)
- [[`fa2a857e6a`](https://github.com/nodejs/node/commit/fa2a857e6a)] - **test**: refactor test-http-client-timeout-option-with-agent (Rich Trott) [#25752](https://github.com/nodejs/node/pull/25752)
- [[`15f6b8e25d`](https://github.com/nodejs/node/commit/15f6b8e25d)] - **test**: add test for `worker.terminate()` + timeout fns (Anna Henningsen) [#25735](https://github.com/nodejs/node/pull/25735)
- [[`c2136348a1`](https://github.com/nodejs/node/commit/c2136348a1)] - **test**: move heapdump tests to pummel (Rich Trott) [#25181](https://github.com/nodejs/node/pull/25181)
- [[`ae19f944f8`](https://github.com/nodejs/node/commit/ae19f944f8)] - **test**: exit sequence sanity tests (Gireesh Punathil) [#25083](https://github.com/nodejs/node/pull/25083)
- [[`af6e439ad8`](https://github.com/nodejs/node/commit/af6e439ad8)] - **test**: enable marking of failing coverage tests (Michael Dawson) [#25671](https://github.com/nodejs/node/pull/25671)
- [[`6203d05a3c`](https://github.com/nodejs/node/commit/6203d05a3c)] - **test**: fix zlib-brotli output assumptions (Adam Majer) [#25697](https://github.com/nodejs/node/pull/25697)
- [[`77274d07d2`](https://github.com/nodejs/node/commit/77274d07d2)] - **test**: rewrite fs {f}utimes test file (Jeremiah Senkpiel) [#25656](https://github.com/nodejs/node/pull/25656)
- [[`29002ceb4e`](https://github.com/nodejs/node/commit/29002ceb4e)] - **(SEMVER-MINOR)** **test**: assert on client and server side seperately (Sam Roberts) [#25381](https://github.com/nodejs/node/pull/25381)
- [[`c7dbb72530`](https://github.com/nodejs/node/commit/c7dbb72530)] - **test**: remove pummel/test-exec (Rich Trott) [#25722](https://github.com/nodejs/node/pull/25722)
- [[`4b2a1eadbd`](https://github.com/nodejs/node/commit/4b2a1eadbd)] - **test**: replace s_client in test-https-ci-reneg-attack (Rich Trott) [#25720](https://github.com/nodejs/node/pull/25720)
- [[`7d682234a6`](https://github.com/nodejs/node/commit/7d682234a6)] - **test**: remove unused uncaughtException handler (Anna Henningsen) [#25641](https://github.com/nodejs/node/pull/25641)
- [[`271126ad3b`](https://github.com/nodejs/node/commit/271126ad3b)] - **test**: remove s_client from test-tls-ci-reneg-attack (Rich Trott) [#25700](https://github.com/nodejs/node/pull/25700)
- [[`190c063ecb`](https://github.com/nodejs/node/commit/190c063ecb)] - **test**: replace Google servers with localhost (Rich Trott) [#25694](https://github.com/nodejs/node/pull/25694)
- [[`f33d705033`](https://github.com/nodejs/node/commit/f33d705033)] - **test**: fix sequential/test-performance delay (Anatoli Papirovski) [#25695](https://github.com/nodejs/node/pull/25695)
- [[`1905f8ef55`](https://github.com/nodejs/node/commit/1905f8ef55)] - **test**: remove common.isOSXMojave (Rich Trott) [#25658](https://github.com/nodejs/node/pull/25658)
- [[`9f1b5c6193`](https://github.com/nodejs/node/commit/9f1b5c6193)] - **test**: remove known_issues/test-cluster-bind-privileged-port (Rich Trott) [#25649](https://github.com/nodejs/node/pull/25649)
- [[`d0705bd24b`](https://github.com/nodejs/node/commit/d0705bd24b)] - **timers**: truncate decimal values (Jeremiah Senkpiel) [#24819](https://github.com/nodejs/node/pull/24819)
- [[`d5b2135dde`](https://github.com/nodejs/node/commit/d5b2135dde)] - **tls**: fix malloc mismatch in SSL_set_tlsext_status_ocsp_resp call (David Benjamin) [#25706](https://github.com/nodejs/node/pull/25706)
- [[`6e80f6d9a1`](https://github.com/nodejs/node/commit/6e80f6d9a1)] - **(SEMVER-MINOR)** **tls**: workaround handshakedone in renegotiation (Shigeki Ohtsu) [#25381](https://github.com/nodejs/node/pull/25381)
- [[`c34c5694eb`](https://github.com/nodejs/node/commit/c34c5694eb)] - **(SEMVER-MINOR)** **tls**: make ossl 1.1.1 cipher list throw error (Sam Roberts) [#25381](https://github.com/nodejs/node/pull/25381)
- [[`8032969c69`](https://github.com/nodejs/node/commit/8032969c69)] - **tools**: make trailing commas consistent in .eslintrc (Rich Trott) [#25739](https://github.com/nodejs/node/pull/25739)
- [[`7ba66505e3`](https://github.com/nodejs/node/commit/7ba66505e3)] - **tools**: make test.py Queue part Python 3 compatible (gengjiawen) [#25701](https://github.com/nodejs/node/pull/25701)
- [[`e6ad7f4c9c`](https://github.com/nodejs/node/commit/e6ad7f4c9c)] - **tools**: make mkssldef.py Python 3 compatible (Sakthipriyan Vairamani (thefourtheye)) [#25584](https://github.com/nodejs/node/pull/25584)
- [[`bc81fef988`](https://github.com/nodejs/node/commit/bc81fef988)] - **vm**: mark scripts as shareable cross-origin (Jeremy Apthorp) [#25380](https://github.com/nodejs/node/pull/25380)
- [[`fb69b2bf14`](https://github.com/nodejs/node/commit/fb69b2bf14)] - **worker**: export workerData to ESM workers (Anna Henningsen) [#25768](https://github.com/nodejs/node/pull/25768)

Windows 32-bit Installer: https://nodejs.org/dist/v11.9.0/node-v11.9.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v11.9.0/node-v11.9.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v11.9.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v11.9.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v11.9.0/node-v11.9.0.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v11.9.0/node-v11.9.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v11.9.0/node-v11.9.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v11.9.0/node-v11.9.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v11.9.0/node-v11.9.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v11.9.0/node-v11.9.0-aix-ppc64.tar.gz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v11.9.0/node-v11.9.0-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v11.9.0/node-v11.9.0-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v11.9.0/node-v11.9.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v11.9.0/node-v11.9.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v11.9.0/node-v11.9.0.tar.gz \
Other release files: https://nodejs.org/dist/v11.9.0/ \
Documentation: https://nodejs.org/docs/v11.9.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

f603424e6efb7336d4fa9862c87f8edbfcc06cddcf9daabe495e98b5f6479d18  node-v11.9.0-aix-ppc64.tar.gz
1df3dd99d174bb8cb61cc8e2aa419a4998c7ada9454123c34991ce30632f1ef8  node-v11.9.0-darwin-x64.tar.gz
6bc546b93ea434aaf1a5e74d294db3a5007a300f25760a47552cdc602ea94ce4  node-v11.9.0-darwin-x64.tar.xz
13842cbbc839ed6506e2c18a861be82b1f033f3ababbb577bd0a121e0ae04fb9  node-v11.9.0-headers.tar.gz
c0506ee7a9b785dd57aad9d14c53685fcc231329c2ee844cd5ee35aed7533590  node-v11.9.0-headers.tar.xz
d5b984cc2ad734617ebb46e36ffbe6a60adc4308fb52edc594fb02d68f573a7c  node-v11.9.0-linux-arm64.tar.gz
f39baa8d212e9676bfa7f1601f94d789c6dc6b35caf440df6ec815d238978457  node-v11.9.0-linux-arm64.tar.xz
8a3a41cc6e3c1428b001a4bcde39292c0410ca04cc7e7e428861b641ae088f83  node-v11.9.0-linux-armv6l.tar.gz
a48e67cf29f279bdc336cf2f3366e9bab77f700f7e97a07fd0f8880a35facbf1  node-v11.9.0-linux-armv6l.tar.xz
a857b806bb1f1748b3db2207b3f8407dca0bb25aeee700c6af9cd179a4913aeb  node-v11.9.0-linux-armv7l.tar.gz
8b1df5c2cca459405a69ca379d739d190b6bb577c8d05ddd3b4b8b156f43370f  node-v11.9.0-linux-armv7l.tar.xz
edc4b7bade731e3aaf2d0d0773b914a178904462deb90e6cca311bb3bdb72af9  node-v11.9.0-linux-ppc64le.tar.gz
7294376b9000dd1bda487defb4db3f617e1084d3f672f982be4242e0eb88d0fb  node-v11.9.0-linux-ppc64le.tar.xz
217fa2579b348e5dd0ba2fa23a0281041a5a16e3f7d4f719a5d76a6552198b5f  node-v11.9.0-linux-s390x.tar.gz
8d82242ac223d17844c030ace8aaa8cae95efc9e16f7cc640466a539f6a0fc6d  node-v11.9.0-linux-s390x.tar.xz
0e872c288724e7de72eaa89d1fbc29979a60cdc8c4c0bc1ea65339328bbaaf4c  node-v11.9.0-linux-x64.tar.gz
9bc461fb7f5fd3344301abfda834160d5397a4e289f9749ab03ba0b7e9a4c853  node-v11.9.0-linux-x64.tar.xz
57bcb596b5b24fb8b7ff91cab3668d86486fc78e069991f6269567649da8e307  node-v11.9.0.pkg
4862d2f0fa143d22f622b75b16f7e2196d796059b465f2c780390771106606e0  node-v11.9.0-sunos-x64.tar.gz
deade33faff201d3923bca24e4118b9d83217311e90644ae38c9fe5cd0beecc8  node-v11.9.0-sunos-x64.tar.xz
d7f3d73d443f52c1c689fe88b9e55c35401b2207d251dc47b2d6a86a6d95758b  node-v11.9.0.tar.gz
2b224d7acaa559bc65c963d4508f66f283159d8e95fdafaaaaa425074368a71a  node-v11.9.0.tar.xz
19bd438bdbe26970d9fa98a6e7f85352e09c3595b87aafd22d1aa5408d41b600  node-v11.9.0-win-x64.7z
c62c01436f632858fea0194d4168b483e1aa485c3fc72247add4899a103c2677  node-v11.9.0-win-x64.zip
dbc39b0f539772e390aa24286938af177e9b57261aedb76deeea880c28c2be09  node-v11.9.0-win-x86.7z
b27a66f9849acef3ebf495e3ec3e6160da055295bb8facc727f8090fd2dd7fdc  node-v11.9.0-win-x86.zip
26e105f9b2629a414da57aca340f4618002b01000ce138a48430cfadd51da2c9  node-v11.9.0-x64.msi
e5c55283f9c9ac0938cdc1054f63e662a0a993d842906660d2650e0431a171a8  node-v11.9.0-x86.msi
09ddeafd771c363f7ed2d6ad11450e276f170f8a0279d037566214c4e890abaa  win-x64/node.exe
dd7e5961d921cf4049f4d83909e4a4df072b28d39208ac175fc580f37f6d3c9f  win-x64/node.lib
9465f9959806fd5eb4f5595a8968043e585b95dabe7ad3372fb3393737a22a3a  win-x64/node_pdb.7z
fbccd3837e155c9549bf6b6e980e2f2678c05050112186cb9941348ed05d4893  win-x64/node_pdb.zip
d37d484a699c7817a36d9e9427e0e320233b5f8d5ebd1588a6b77b98c048b2a8  win-x86/node.exe
5dcbbef9c961ccf3b59566ea56af58f36fdc93f14d93c37da1c2939f1e898985  win-x86/node.lib
0ae8a35d7908224b14ae5716272f5bf01c150bd76ffa675f24b6a0260999e86b  win-x86/node_pdb.7z
9543b624841085518f38c4d17ea5c87a21eade83e378fc9e3a79b11c837b10b4  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEj8yhP+8dDC6RAI4Jdw96mlrhVgAFAlxSIEoACgkQdw96mlrh
VgAhYw/8DVev66Ae+fRMCv6d3Kltl01GVVW1yHfX3vfSIiZfOCvsZnJl1HUYvKAk
lZZo5XAb4aODcw6qJkcIQdAW92gD/pLnErnK3Txkit5xhpdxk3Sa4lB1WMX6cza8
1o4UBzphAX76UOVuBnBQwfYnDcCxh8OkwCHpPadZsviMliKdiyh604AYue9ePD3f
acc0+6kvHe3d/eSRNsKJ04V6Eaga+pNhGltxPnLQ6EdVPwYpK/tQMrJegt9WPqMf
lCb/bi3780516eBFn+RBIsO2x4f6n44GGqx401ErUleEuORoEs8JuJaWq+ZyljfY
uxkP9XrwDCYhZ1YWkRIvzpJ0MSuu89nZfqHfIrq+jSWfNFM8cLVgIiYFZ2I7OIRe
mXZCTjtyk95ALSEDMJ7qgg4lEcX6OQ2II5dzPxSn7hETWnaBWpIEQoyIN1c88c3s
lli0MVlEWhLkoqaNbdskyWP3/Q+mHAjTSHl78W4eRnT0TMH5VsiERPcoAJAdvvzm
k4qw4M/8ZKeeTrCqIb/fcFXC/F5XB5itmW6VEhZAjYepDwV7VyDmuNH9aH1TA7Hu
D11VXRyMC1xGlWraDJDwj+ZZsGPBelEdiNTIAuuoikrk+jOw3o6CI8SYvEFOfWAC
Kkd0k11uAq3v8Ab1u6sx87tw9L6vff5MfqqT0i3NCiEks3pIbU0=
=Pjap
-----END PGP SIGNATURE-----

```
