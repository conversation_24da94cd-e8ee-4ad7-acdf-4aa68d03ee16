---
date: '2020-01-07T17:38:00.690Z'
category: release
title: Node v12.14.1 (LTS)
layout: blog-post
author: <PERSON>
---

### Notable changes

- **crypto**: fix key requirements in asymmetric cipher (<PERSON>) [#30249](https://github.com/nodejs/node/pull/30249)
- **deps**:
  - update llhttp to 2.0.1 (<PERSON><PERSON>) [#30553](https://github.com/nodejs/node/pull/30553)
  - update nghttp2 to 1.40.0 (gengjiawen) [#30493](https://github.com/nodejs/node/pull/30493)
- **v8**: mark serdes API as stable (<PERSON>) [#30234](https://github.com/nodejs/node/pull/30234)

### Commits

- [[`ddbe9826d0`](https://github.com/nodejs/node/commit/ddbe9826d0)] - **assert**: replace var with let in lib/assert.js (PerfectPan) [#30261](https://github.com/nodejs/node/pull/30261)
- [[`33e0bd6b7c`](https://github.com/nodejs/node/commit/33e0bd6b7c)] - **benchmark**: use let instead of var in async_hooks (dnlup) [#30470](https://github.com/nodejs/node/pull/30470)
- [[`351ee645c8`](https://github.com/nodejs/node/commit/351ee645c8)] - **benchmark**: use let instead of var in assert (dnlup) [#30450](https://github.com/nodejs/node/pull/30450)
- [[`da0c6736d0`](https://github.com/nodejs/node/commit/da0c6736d0)] - **benchmark,doc,lib,test**: prepare for padding lint rule (Rich Trott) [#30696](https://github.com/nodejs/node/pull/30696)
- [[`719e33ff40`](https://github.com/nodejs/node/commit/719e33ff40)] - **buffer**: fix 6-byte writeUIntBE() range check (Brian White) [#30459](https://github.com/nodejs/node/pull/30459)
- [[`58fca405c1`](https://github.com/nodejs/node/commit/58fca405c1)] - **buffer**: change var to let (Vladislav Botvin) [#30292](https://github.com/nodejs/node/pull/30292)
- [[`cc93a3742b`](https://github.com/nodejs/node/commit/cc93a3742b)] - **build**: store cache on timed out builds on Travis (Richard Lau) [#30469](https://github.com/nodejs/node/pull/30469)
- [[`3b096c2bc4`](https://github.com/nodejs/node/commit/3b096c2bc4)] - **build,win**: propagate error codes in vcbuild (João Reis) [#30724](https://github.com/nodejs/node/pull/30724)
- [[`f9f3ab293f`](https://github.com/nodejs/node/commit/f9f3ab293f)] - **child_process**: document kill() return value (cjihrig) [#30669](https://github.com/nodejs/node/pull/30669)
- [[`8d3e023061`](https://github.com/nodejs/node/commit/8d3e023061)] - **child_process**: replace var with let/const (dnlup) [#30389](https://github.com/nodejs/node/pull/30389)
- [[`6974f26e26`](https://github.com/nodejs/node/commit/6974f26e26)] - **child_process**: replace var with const/let in internal/child_process.js (Luis Camargo) [#30414](https://github.com/nodejs/node/pull/30414)
- [[`d70ec60839`](https://github.com/nodejs/node/commit/d70ec60839)] - **cluster**: replace vars in child.js (EmaSuriano) [#30383](https://github.com/nodejs/node/pull/30383)
- [[`98321448a6`](https://github.com/nodejs/node/commit/98321448a6)] - **cluster**: replace var with let (Herrmann, Rene R. (656)) [#30425](https://github.com/nodejs/node/pull/30425)
- [[`daa593263d`](https://github.com/nodejs/node/commit/daa593263d)] - **cluster**: replace var by let in shared_handle.js (poutch) [#30402](https://github.com/nodejs/node/pull/30402)
- [[`2938fe31ae`](https://github.com/nodejs/node/commit/2938fe31ae)] - **cluster**: destruct primordials in lib/internal/cluster/worker.js (peze) [#30246](https://github.com/nodejs/node/pull/30246)
- [[`c1cb639e84`](https://github.com/nodejs/node/commit/c1cb639e84)] - **crypto**: remove redundant validateUint32 argument (Tobias Nießen) [#30579](https://github.com/nodejs/node/pull/30579)
- [[`bb7e78a2a0`](https://github.com/nodejs/node/commit/bb7e78a2a0)] - **crypto**: fix key requirements in asymmetric cipher (Tobias Nießen) [#30249](https://github.com/nodejs/node/pull/30249)
- [[`74dd216886`](https://github.com/nodejs/node/commit/74dd216886)] - **crypto**: update root certificates (AshCripps) [#30195](https://github.com/nodejs/node/pull/30195)
- [[`7589486584`](https://github.com/nodejs/node/commit/7589486584)] - **deps**: update llhttp to 2.0.1 (Fedor Indutny) [#30553](https://github.com/nodejs/node/pull/30553)
- [[`d2e32ab6b2`](https://github.com/nodejs/node/commit/d2e32ab6b2)] - **deps**: update nghttp2 to 1.40.0 (gengjiawen) [#30493](https://github.com/nodejs/node/pull/30493)
- [[`3d39be73d4`](https://github.com/nodejs/node/commit/3d39be73d4)] - **dgram**: remove listeners on bind error (Anna Henningsen) [#30210](https://github.com/nodejs/node/pull/30210)
- [[`fd5fed1c89`](https://github.com/nodejs/node/commit/fd5fed1c89)] - **dgram**: reset bind state before emitting error (Anna Henningsen) [#30210](https://github.com/nodejs/node/pull/30210)
- [[`ded0748dea`](https://github.com/nodejs/node/commit/ded0748dea)] - **dns**: use length for building TXT string (Anna Henningsen) [#30690](https://github.com/nodejs/node/pull/30690)
- [[`7cf19ab069`](https://github.com/nodejs/node/commit/7cf19ab069)] - **dns**: switch var to const/let (Dmitriy Kikinskiy) [#30302](https://github.com/nodejs/node/pull/30302)
- [[`6a35c387a1`](https://github.com/nodejs/node/commit/6a35c387a1)] - **doc**: fix typographical error (Rich Trott) [#30735](https://github.com/nodejs/node/pull/30735)
- [[`043163a602`](https://github.com/nodejs/node/commit/043163a602)] - **doc**: revise REPL uncaught exception text (Rich Trott) [#30729](https://github.com/nodejs/node/pull/30729)
- [[`50a54ce713`](https://github.com/nodejs/node/commit/50a54ce713)] - **doc**: update signature algorithm in release doc (Myles Borins) [#30673](https://github.com/nodejs/node/pull/30673)
- [[`827c53bdbe`](https://github.com/nodejs/node/commit/827c53bdbe)] - **doc**: update README.md to fix active/maint times (Michael Dawson) [#30707](https://github.com/nodejs/node/pull/30707)
- [[`4e93f2333e`](https://github.com/nodejs/node/commit/4e93f2333e)] - **doc**: update socket.bufferSize text (Rich Trott) [#30723](https://github.com/nodejs/node/pull/30723)
- [[`5334f59519`](https://github.com/nodejs/node/commit/5334f59519)] - **doc**: note that buf.buffer's contents might differ (AJ Jordan) [#29651](https://github.com/nodejs/node/pull/29651)
- [[`7bc8c85d53`](https://github.com/nodejs/node/commit/7bc8c85d53)] - **doc**: clarify IncomingMessage.destroy() description (Sam Foxman) [#30255](https://github.com/nodejs/node/pull/30255)
- [[`ca960614af`](https://github.com/nodejs/node/commit/ca960614af)] - **doc**: fixed a typo in process.md (Harendra Singh) [#30277](https://github.com/nodejs/node/pull/30277)
- [[`efbe0a28a1`](https://github.com/nodejs/node/commit/efbe0a28a1)] - **doc**: documenting a bit more FreeBSD case (David Carlier) [#30325](https://github.com/nodejs/node/pull/30325)
- [[`42cb92f116`](https://github.com/nodejs/node/commit/42cb92f116)] - **doc**: add missing 'added' versions to module.builtinModules (Thomas Watson) [#30562](https://github.com/nodejs/node/pull/30562)
- [[`670e4b5e23`](https://github.com/nodejs/node/commit/670e4b5e23)] - **doc**: address nits for src/README.md (Anna Henningsen) [#30693](https://github.com/nodejs/node/pull/30693)
- [[`6fc562c97c`](https://github.com/nodejs/node/commit/6fc562c97c)] - **doc**: revise socket.connect() note (Rich Trott) [#30691](https://github.com/nodejs/node/pull/30691)
- [[`bea2069f22`](https://github.com/nodejs/node/commit/bea2069f22)] - **doc**: remove "this API is unstable" note for v8 serdes API (bruce-one) [#30631](https://github.com/nodejs/node/pull/30631)
- [[`2532bf347d`](https://github.com/nodejs/node/commit/2532bf347d)] - **doc**: minor updates to releases.md (Beth Griggs) [#30636](https://github.com/nodejs/node/pull/30636)
- [[`8b60065778`](https://github.com/nodejs/node/commit/8b60065778)] - **doc**: add 13 and 12 to previous versions (Andrew Hughes) [#30590](https://github.com/nodejs/node/pull/30590)
- [[`16a8daaae9`](https://github.com/nodejs/node/commit/16a8daaae9)] - **doc**: update AUTHORS list (Gus Caplan) [#30672](https://github.com/nodejs/node/pull/30672)
- [[`c705a8e816`](https://github.com/nodejs/node/commit/c705a8e816)] - **doc**: add explanation why keep var with for loop (Lucas Recknagel) [#30380](https://github.com/nodejs/node/pull/30380)
- [[`a493feb863`](https://github.com/nodejs/node/commit/a493feb863)] - **doc**: document "Resume Build" limitation (Richard Lau) [#30604](https://github.com/nodejs/node/pull/30604)
- [[`0d6fbe8b30`](https://github.com/nodejs/node/commit/0d6fbe8b30)] - **doc**: add note of caution about non-conforming streams (Robert Nagy) [#29895](https://github.com/nodejs/node/pull/29895)
- [[`7ebafda6c3`](https://github.com/nodejs/node/commit/7ebafda6c3)] - **doc**: add note about debugging worker_threads (Denys Otrishko) [#30594](https://github.com/nodejs/node/pull/30594)
- [[`e10f9224ac`](https://github.com/nodejs/node/commit/e10f9224ac)] - **doc**: add mention for using promisify on class methods (Denys Otrishko) [#30355](https://github.com/nodejs/node/pull/30355)
- [[`768bac5678`](https://github.com/nodejs/node/commit/768bac5678)] - **doc**: explain GIT_REMOTE_REF in COLLABORATOR_GUIDE (Denys Otrishko) [#30371](https://github.com/nodejs/node/pull/30371)
- [[`a836ac10ff`](https://github.com/nodejs/node/commit/a836ac10ff)] - **doc**: fix overriding of prefix option (Luigi Pinca) [#30518](https://github.com/nodejs/node/pull/30518)
- [[`ec5fe999e8`](https://github.com/nodejs/node/commit/ec5fe999e8)] - **doc**: adds NO_COLOR to assert doc page (Shobhit Chittora) [#30483](https://github.com/nodejs/node/pull/30483)
- [[`4b716a6798`](https://github.com/nodejs/node/commit/4b716a6798)] - **doc**: document timed out Travis CI builds (Richard Lau) [#30469](https://github.com/nodejs/node/pull/30469)
- [[`2e70ad391b`](https://github.com/nodejs/node/commit/2e70ad391b)] - **doc**: replace const / var with let (Duncan Healy) [#30446](https://github.com/nodejs/node/pull/30446)
- [[`793d360d26`](https://github.com/nodejs/node/commit/793d360d26)] - **doc**: update 8.x to 10.x in backporting guide (garygsc) [#30481](https://github.com/nodejs/node/pull/30481)
- [[`25c8a13fde`](https://github.com/nodejs/node/commit/25c8a13fde)] - **doc**: createRequire can take import.meta.url directly (Geoffrey Booth) [#30495](https://github.com/nodejs/node/pull/30495)
- [[`d979a9d391`](https://github.com/nodejs/node/commit/d979a9d391)] - **doc**: add entry to url.parse() changes metadata (Luigi Pinca) [#30348](https://github.com/nodejs/node/pull/30348)
- [[`2e0ef36a19`](https://github.com/nodejs/node/commit/2e0ef36a19)] - **doc**: simplify text in pull-requests.md (Rich Trott) [#30458](https://github.com/nodejs/node/pull/30458)
- [[`11d01700fd`](https://github.com/nodejs/node/commit/11d01700fd)] - **doc**: remove "multiple variants" from BUILDING.md (Rich Trott) [#30366](https://github.com/nodejs/node/pull/30366)
- [[`0c68515e8c`](https://github.com/nodejs/node/commit/0c68515e8c)] - **doc**: remove "maintenance is supported by" text in BUILDING.md (Rich Trott) [#30365](https://github.com/nodejs/node/pull/30365)
- [[`d2c85f32c6`](https://github.com/nodejs/node/commit/d2c85f32c6)] - **doc**: add lookup to http.request() options (Luigi Pinca) [#30353](https://github.com/nodejs/node/pull/30353)
- [[`b8b6f258fe`](https://github.com/nodejs/node/commit/b8b6f258fe)] - **doc**: fix up N-API doc (Michael Dawson) [#30254](https://github.com/nodejs/node/pull/30254)
- [[`3710068d3e`](https://github.com/nodejs/node/commit/3710068d3e)] - **doc**: add link to node-code-ide-configs in testing (Kamat, Trivikram) [#24012](https://github.com/nodejs/node/pull/24012)
- [[`d713e5a9bf`](https://github.com/nodejs/node/commit/d713e5a9bf)] - **doc**: update GOVERNANCE.md (Rich Trott) [#30259](https://github.com/nodejs/node/pull/30259)
- [[`f66f28ea7a`](https://github.com/nodejs/node/commit/f66f28ea7a)] - **doc**: move inactive Collaborators to emeriti (Rich Trott) [#30243](https://github.com/nodejs/node/pull/30243)
- [[`fd16e9f6ff`](https://github.com/nodejs/node/commit/fd16e9f6ff)] - **doc**: update examples in writing-tests.md (garygsc) [#30126](https://github.com/nodejs/node/pull/30126)
- [[`17963bbce6`](https://github.com/nodejs/node/commit/17963bbce6)] - **doc, console**: remove non-existant methods from docs (Simon Schick) [#30346](https://github.com/nodejs/node/pull/30346)
- [[`fe1296e507`](https://github.com/nodejs/node/commit/fe1296e507)] - **doc,meta**: allow Travis results for doc/comment changes (Rich Trott) [#30330](https://github.com/nodejs/node/pull/30330)
- [[`d32cd8595d`](https://github.com/nodejs/node/commit/d32cd8595d)] - **doc,meta**: remove wait period for npm pull requests (Rich Trott) [#30329](https://github.com/nodejs/node/pull/30329)
- [[`574b0f2104`](https://github.com/nodejs/node/commit/574b0f2104)] - **domain**: rename var to let and const (Maria Stogova) [#30312](https://github.com/nodejs/node/pull/30312)
- [[`c80a4d851d`](https://github.com/nodejs/node/commit/c80a4d851d)] - **encoding**: make TextDecoder handle BOM correctly (Anna Henningsen) [#30132](https://github.com/nodejs/node/pull/30132)
- [[`217cc13f29`](https://github.com/nodejs/node/commit/217cc13f29)] - **events**: improve performance caused by primordials (guzhizhou) [#30577](https://github.com/nodejs/node/pull/30577)
- [[`1a83c654f9`](https://github.com/nodejs/node/commit/1a83c654f9)] - **fs**: change var to let (Àlvar Pérez) [#30407](https://github.com/nodejs/node/pull/30407)
- [[`7ba8037244`](https://github.com/nodejs/node/commit/7ba8037244)] - **fs**: cover fs.opendir ERR_INVALID_CALLBACK (Vladislav Botvin) [#30307](https://github.com/nodejs/node/pull/30307)
- [[`d7c2911f8d`](https://github.com/nodejs/node/commit/d7c2911f8d)] - **fs**: change var to let (Nadya) [#30318](https://github.com/nodejs/node/pull/30318)
- [[`6b380cc791`](https://github.com/nodejs/node/commit/6b380cc791)] - **http**: set socket.server unconditionally (Anna Henningsen) [#30571](https://github.com/nodejs/node/pull/30571)
- [[`175b8fe5a7`](https://github.com/nodejs/node/commit/175b8fe5a7)] - **http**: replace var with let (Guilherme Goncalves) [#30421](https://github.com/nodejs/node/pull/30421)
- [[`fdfcf68360`](https://github.com/nodejs/node/commit/fdfcf68360)] - **http**: destructure primordials in lib/\_http_server.js (Artem Maksimov) [#30315](https://github.com/nodejs/node/pull/30315)
- [[`71e3c485ad`](https://github.com/nodejs/node/commit/71e3c485ad)] - **http**: revise \_http_server.js (telenord) [#30279](https://github.com/nodejs/node/pull/30279)
- [[`5b8c481906`](https://github.com/nodejs/node/commit/5b8c481906)] - **http**: http_common rename var to let and const (telenord) [#30288](https://github.com/nodejs/node/pull/30288)
- [[`5727bc3880`](https://github.com/nodejs/node/commit/5727bc3880)] - **http**: http_incoming rename var to let and const (telenord) [#30285](https://github.com/nodejs/node/pull/30285)
- [[`b0816c2926`](https://github.com/nodejs/node/commit/b0816c2926)] - **http**: replace vars with lets and consts in lib/\_http_agent.js (palmires) [#30301](https://github.com/nodejs/node/pull/30301)
- [[`652514233f`](https://github.com/nodejs/node/commit/652514233f)] - **http,async_hooks**: keep resource object alive from socket (Anna Henningsen) [#30196](https://github.com/nodejs/node/pull/30196)
- [[`9fd6b5e98b`](https://github.com/nodejs/node/commit/9fd6b5e98b)] - **http2**: fix session memory accounting after pausing (Michael Lehenbauer) [#30684](https://github.com/nodejs/node/pull/30684)
- [[`cb3e008c97`](https://github.com/nodejs/node/commit/cb3e008c97)] - **http2**: change var to let compact.js (Maria Emmanouil) [#30392](https://github.com/nodejs/node/pull/30392)
- [[`68f3dde04b`](https://github.com/nodejs/node/commit/68f3dde04b)] - **http2**: core.js replace var with let (Daniel Schuech) [#30403](https://github.com/nodejs/node/pull/30403)
- [[`bccef49b5b`](https://github.com/nodejs/node/commit/bccef49b5b)] - **http2**: replace var with let/const (Paolo Ceschi Berrini) [#30417](https://github.com/nodejs/node/pull/30417)
- [[`13f65d1f16`](https://github.com/nodejs/node/commit/13f65d1f16)] - **http2**: remove duplicated assertIsObject (ZYSzys) [#30541](https://github.com/nodejs/node/pull/30541)
- [[`eceeed7a11`](https://github.com/nodejs/node/commit/eceeed7a11)] - **https**: change var to let in lib/https.js (galina.prokofeva) [#30320](https://github.com/nodejs/node/pull/30320)
- [[`55e94cbba1`](https://github.com/nodejs/node/commit/55e94cbba1)] - **inspector**: properly shut down uv_async_t (Anna Henningsen) [#30612](https://github.com/nodejs/node/pull/30612)
- [[`d138e2db53`](https://github.com/nodejs/node/commit/d138e2db53)] - **lib**: use let instead of var (Shubham Chaturvedi) [#30375](https://github.com/nodejs/node/pull/30375)
- [[`d951209458`](https://github.com/nodejs/node/commit/d951209458)] - **lib**: replace var with let/const (jens-cappelle) [#30391](https://github.com/nodejs/node/pull/30391)
- [[`f963409f77`](https://github.com/nodejs/node/commit/f963409f77)] - **lib**: replace var w/ let (Chris Oyler) [#30386](https://github.com/nodejs/node/pull/30386)
- [[`a6625dd7b6`](https://github.com/nodejs/node/commit/a6625dd7b6)] - **lib**: replace var with let/const (Tijl Claessens) [#30390](https://github.com/nodejs/node/pull/30390)
- [[`7d0631aefc`](https://github.com/nodejs/node/commit/7d0631aefc)] - **lib**: adding perf notes js_stream_socket.js (ryan jarvinen) [#30415](https://github.com/nodejs/node/pull/30415)
- [[`1cfaccdc66`](https://github.com/nodejs/node/commit/1cfaccdc66)] - **lib**: replace var with let (Dennis Saenger) [#30396](https://github.com/nodejs/node/pull/30396)
- [[`c4fcd5b4ca`](https://github.com/nodejs/node/commit/c4fcd5b4ca)] - **lib**: main_thread_only change var to let (matijagaspar) [#30398](https://github.com/nodejs/node/pull/30398)
- [[`dc786c3315`](https://github.com/nodejs/node/commit/dc786c3315)] - **lib**: change var to let in stream_base_commons (Kyriakos Markakis) [#30426](https://github.com/nodejs/node/pull/30426)
- [[`e72be52c8e`](https://github.com/nodejs/node/commit/e72be52c8e)] - **lib**: use let instead of var (Semir Ajruli) [#30424](https://github.com/nodejs/node/pull/30424)
- [[`96c061552d`](https://github.com/nodejs/node/commit/96c061552d)] - **lib**: changed var to let (Oliver Belaifa) [#30427](https://github.com/nodejs/node/pull/30427)
- [[`03e6d0d408`](https://github.com/nodejs/node/commit/03e6d0d408)] - **lib**: replace var with let/const (Dries Stelten) [#30409](https://github.com/nodejs/node/pull/30409)
- [[`7eafe8acdd`](https://github.com/nodejs/node/commit/7eafe8acdd)] - **lib**: change var to let (Dimitris Ktistakis) [#30408](https://github.com/nodejs/node/pull/30408)
- [[`0c4bb4a70e`](https://github.com/nodejs/node/commit/0c4bb4a70e)] - **lib**: replace var with let/const (Tembrechts) [#30404](https://github.com/nodejs/node/pull/30404)
- [[`ff4f23623c`](https://github.com/nodejs/node/commit/ff4f23623c)] - **lib**: replace var to let in cli_table.js (Jing Lin) [#30400](https://github.com/nodejs/node/pull/30400)
- [[`80bfc08935`](https://github.com/nodejs/node/commit/80bfc08935)] - **lib**: replace var with let (David OLIVIER) [#30381](https://github.com/nodejs/node/pull/30381)
- [[`614949d25d`](https://github.com/nodejs/node/commit/614949d25d)] - **lib**: replace var with let and const in readline.js (VinceOPS) [#30377](https://github.com/nodejs/node/pull/30377)
- [[`4834a31880`](https://github.com/nodejs/node/commit/4834a31880)] - **lib**: change var to let/const in internal/querystring.js (Artem Maksimov) [#30286](https://github.com/nodejs/node/pull/30286)
- [[`df2dce08aa`](https://github.com/nodejs/node/commit/df2dce08aa)] - **lib**: change var to let in internal/streams (Kyriakos Markakis) [#30430](https://github.com/nodejs/node/pull/30430)
- [[`bd853cc709`](https://github.com/nodejs/node/commit/bd853cc709)] - **lib**: replace var with let/const (Kenza Houmani) [#30440](https://github.com/nodejs/node/pull/30440)
- [[`45e9c31885`](https://github.com/nodejs/node/commit/45e9c31885)] - **lib**: change var to let in string_decoder (mkdorff) [#30393](https://github.com/nodejs/node/pull/30393)
- [[`8725a5c935`](https://github.com/nodejs/node/commit/8725a5c935)] - **lib**: replaced var to let in lib/v8.js (Vadim Gorbachev) [#30305](https://github.com/nodejs/node/pull/30305)
- [[`bcc00e1e88`](https://github.com/nodejs/node/commit/bcc00e1e88)] - **lib**: change var to let in lib/\_stream_duplex.js (Ilia Safronov) [#30297](https://github.com/nodejs/node/pull/30297)
- [[`b1415564ea`](https://github.com/nodejs/node/commit/b1415564ea)] - **module**: fix for empty object in InternalModuleReadJSON (Guy Bedford) [#30256](https://github.com/nodejs/node/pull/30256)
- [[`dc9c7709ff`](https://github.com/nodejs/node/commit/dc9c7709ff)] - **n-api**: detach external ArrayBuffers on env exit (Anna Henningsen) [#30551](https://github.com/nodejs/node/pull/30551)
- [[`4d396fd874`](https://github.com/nodejs/node/commit/4d396fd874)] - **n-api**: add missed nullptr check in napi_has_own_property (Denys Otrishko) [#30626](https://github.com/nodejs/node/pull/30626)
- [[`21be4b1b95`](https://github.com/nodejs/node/commit/21be4b1b95)] - **net**: replaced vars to lets and consts (nathias) [#30401](https://github.com/nodejs/node/pull/30401)
- [[`0ae1d17517`](https://github.com/nodejs/node/commit/0ae1d17517)] - **net**: destructure primordials (Guilherme Goncalves) [#30447](https://github.com/nodejs/node/pull/30447)
- [[`1597626a02`](https://github.com/nodejs/node/commit/1597626a02)] - **net**: replaced vars to lets and consts (alexahdp) [#30287](https://github.com/nodejs/node/pull/30287)
- [[`f874aa1552`](https://github.com/nodejs/node/commit/f874aa1552)] - **path**: replace var with let in lib/path.js (peze) [#30260](https://github.com/nodejs/node/pull/30260)
- [[`956207fa8d`](https://github.com/nodejs/node/commit/956207fa8d)] - **process**: replace var with let/const (Jesper Ek) [#30382](https://github.com/nodejs/node/pull/30382)
- [[`db029650d9`](https://github.com/nodejs/node/commit/db029650d9)] - **process**: replace vars in per_thread.js (EmaSuriano) [#30385](https://github.com/nodejs/node/pull/30385)
- [[`02f606d528`](https://github.com/nodejs/node/commit/02f606d528)] - **process**: add coverage tests for sourceMapFromDataUrl method (Nolik) [#30319](https://github.com/nodejs/node/pull/30319)
- [[`56b3edcce8`](https://github.com/nodejs/node/commit/56b3edcce8)] - **process**: make source map getter resistant against prototype tampering (Anna Henningsen) [#30228](https://github.com/nodejs/node/pull/30228)
- [[`cbb2f81bf1`](https://github.com/nodejs/node/commit/cbb2f81bf1)] - **querystring**: replace var with let/const (Raoul Jaeckel) [#30429](https://github.com/nodejs/node/pull/30429)
- [[`b21f46d95d`](https://github.com/nodejs/node/commit/b21f46d95d)] - **readline**: change var to let (dnlup) [#30435](https://github.com/nodejs/node/pull/30435)
- [[`cc84dbfe7b`](https://github.com/nodejs/node/commit/cc84dbfe7b)] - **repl**: change var to let (Oliver Belaifa) [#30428](https://github.com/nodejs/node/pull/30428)
- [[`4b1f730357`](https://github.com/nodejs/node/commit/4b1f730357)] - **src**: remove unused variable in node_dir.cc (gengjiawen) [#30267](https://github.com/nodejs/node/pull/30267)
- [[`3bb085dbad`](https://github.com/nodejs/node/commit/3bb085dbad)] - **src**: cleanup unused headers (Alexandre Ferrando) [#30328](https://github.com/nodejs/node/pull/30328)
- [[`df4dddb0d2`](https://github.com/nodejs/node/commit/df4dddb0d2)] - **src**: replaced var with let (Aldo Ambrosioni) [#30397](https://github.com/nodejs/node/pull/30397)
- [[`8af33114e8`](https://github.com/nodejs/node/commit/8af33114e8)] - **src**: fix signal handler crash on close (Shelley Vohr) [#30582](https://github.com/nodejs/node/pull/30582)
- [[`12d7d645dd`](https://github.com/nodejs/node/commit/12d7d645dd)] - **src**: add file name to 'Module did not self-register' error (Jeremy Apthorp) [#30125](https://github.com/nodejs/node/pull/30125)
- [[`2f4069a932`](https://github.com/nodejs/node/commit/2f4069a932)] - **src**: enhance feature access `CHECK`s during bootstrap (Anna Henningsen) [#30452](https://github.com/nodejs/node/pull/30452)
- [[`3d7882e0d1`](https://github.com/nodejs/node/commit/3d7882e0d1)] - **src**: lib/internal/timers.js var -> let/const (Nikolay Krashnikov) [#30314](https://github.com/nodejs/node/pull/30314)
- [[`d8046fc0f8`](https://github.com/nodejs/node/commit/d8046fc0f8)] - **src**: persist strings that are used multiple times in the environment (Vadim Gorbachev) [#30321](https://github.com/nodejs/node/pull/30321)
- [[`e1a12446c5`](https://github.com/nodejs/node/commit/e1a12446c5)] - **src**: run RunBeforeExitCallbacks as part of EmitBeforeExit (Anna Henningsen) [#30229](https://github.com/nodejs/node/pull/30229)
- [[`da8ceb965d`](https://github.com/nodejs/node/commit/da8ceb965d)] - **src**: use unique_ptr for InitializeInspector() (Anna Henningsen) [#30229](https://github.com/nodejs/node/pull/30229)
- [[`fc9e7082fd`](https://github.com/nodejs/node/commit/fc9e7082fd)] - **src**: make WaitForInspectorDisconnect an exit hook (Anna Henningsen) [#30229](https://github.com/nodejs/node/pull/30229)
- [[`d14d9e8b1b`](https://github.com/nodejs/node/commit/d14d9e8b1b)] - **src**: make EndStartedProfilers an exit hook (Anna Henningsen) [#30229](https://github.com/nodejs/node/pull/30229)
- [[`eb8beb5f5f`](https://github.com/nodejs/node/commit/eb8beb5f5f)] - **src**: track no of active JS signal handlers (Anna Henningsen) [#30229](https://github.com/nodejs/node/pull/30229)
- [[`2e729f2dc9`](https://github.com/nodejs/node/commit/2e729f2dc9)] - **src**: make AtExit() callbacks run in reverse order (Anna Henningsen) [#30230](https://github.com/nodejs/node/pull/30230)
- [[`569f797917`](https://github.com/nodejs/node/commit/569f797917)] - **src**: remove unimplemented method from node.h (Anna Henningsen) [#30098](https://github.com/nodejs/node/pull/30098)
- [[`f6360c124c`](https://github.com/nodejs/node/commit/f6360c124c)] - **src,doc**: fix broken links (cjihrig) [#30662](https://github.com/nodejs/node/pull/30662)
- [[`a621ab8695`](https://github.com/nodejs/node/commit/a621ab8695)] - **src,doc**: add C++ internals documentation (Anna Henningsen) [#30552](https://github.com/nodejs/node/pull/30552)
- [[`ce6a865ab2`](https://github.com/nodejs/node/commit/ce6a865ab2)] - **stream**: improve performance for sync write finishes (Anna Henningsen) [#30710](https://github.com/nodejs/node/pull/30710)
- [[`8792147fd2`](https://github.com/nodejs/node/commit/8792147fd2)] - **stream**: replace var with let (daern91) [#30379](https://github.com/nodejs/node/pull/30379)
- [[`88adad26f5`](https://github.com/nodejs/node/commit/88adad26f5)] - **stream**: increase MAX_HWM (Robert Nagy) [#29938](https://github.com/nodejs/node/pull/29938)
- [[`a83ccf8a6b`](https://github.com/nodejs/node/commit/a83ccf8a6b)] - **test**: skip test-domain-error-types in debug mode temporariliy (Rich Trott) [#30629](https://github.com/nodejs/node/pull/30629)
- [[`f3c3b1d328`](https://github.com/nodejs/node/commit/f3c3b1d328)] - **test**: add coverage for ERR_TLS_INVALID_PROTOCOL_VERSION (Rich Trott) [#30741](https://github.com/nodejs/node/pull/30741)
- [[`197b61656d`](https://github.com/nodejs/node/commit/197b61656d)] - **test**: add an indicator `isIBMi` (Xu Meng) [#30714](https://github.com/nodejs/node/pull/30714)
- [[`6c6ffdd56c`](https://github.com/nodejs/node/commit/6c6ffdd56c)] - **test**: use arrow functions in async-hooks tests (garygsc) [#30137](https://github.com/nodejs/node/pull/30137)
- [[`2aa86547ff`](https://github.com/nodejs/node/commit/2aa86547ff)] - **test**: fix test-benchmark-streams (Rich Trott) [#30757](https://github.com/nodejs/node/pull/30757)
- [[`b37609d193`](https://github.com/nodejs/node/commit/b37609d193)] - **test**: remove unused callback argument (Rich Trott) [#30712](https://github.com/nodejs/node/pull/30712)
- [[`5bb7bf3767`](https://github.com/nodejs/node/commit/5bb7bf3767)] - **test**: simplify forEach() usage (Rich Trott) [#30712](https://github.com/nodejs/node/pull/30712)
- [[`276741ae75`](https://github.com/nodejs/node/commit/276741ae75)] - **test**: remove unused callback argument (Rich Trott) [#30712](https://github.com/nodejs/node/pull/30712)
- [[`e624bb529d`](https://github.com/nodejs/node/commit/e624bb529d)] - **test**: increase coverage for trace_events.js (Rich Trott) [#30705](https://github.com/nodejs/node/pull/30705)
- [[`9f49b978e4`](https://github.com/nodejs/node/commit/9f49b978e4)] - **test**: refactor createHook test (Jeny) [#30568](https://github.com/nodejs/node/pull/30568)
- [[`646b81c209`](https://github.com/nodejs/node/commit/646b81c209)] - **test**: port worker + buffer test to N-API (Anna Henningsen) [#30551](https://github.com/nodejs/node/pull/30551)
- [[`8554ff2a4c`](https://github.com/nodejs/node/commit/8554ff2a4c)] - **test**: move test-https-server-consumed-timeout to parallel (Rich Trott) [#30677](https://github.com/nodejs/node/pull/30677)
- [[`d3bac601c3`](https://github.com/nodejs/node/commit/d3bac601c3)] - **test**: remove unnecessary common.platformTimeout() call (Rich Trott) [#30677](https://github.com/nodejs/node/pull/30677)
- [[`564e477b37`](https://github.com/nodejs/node/commit/564e477b37)] - **test**: do not skip test-http-server-consumed-timeout (Rich Trott) [#30677](https://github.com/nodejs/node/pull/30677)
- [[`d3785941a9`](https://github.com/nodejs/node/commit/d3785941a9)] - **test**: remove unused function argument from http test (Rich Trott) [#30677](https://github.com/nodejs/node/pull/30677)
- [[`648318dc5c`](https://github.com/nodejs/node/commit/648318dc5c)] - **test**: add logging in case of infinite loop (Rich Trott) [#30649](https://github.com/nodejs/node/pull/30649)
- [[`196e08dafc`](https://github.com/nodejs/node/commit/196e08dafc)] - **test**: remove destructuring from test-inspector-contexts (Rich Trott) [#30649](https://github.com/nodejs/node/pull/30649)
- [[`ece08a5821`](https://github.com/nodejs/node/commit/ece08a5821)] - **test**: check for session.post() errors in test-insepctor-context (Rich Trott) [#30649](https://github.com/nodejs/node/pull/30649)
- [[`c3ad977867`](https://github.com/nodejs/node/commit/c3ad977867)] - **test**: add mustCall() to test-inspector-contexts (Rich Trott) [#30649](https://github.com/nodejs/node/pull/30649)
- [[`f5ac4ec49a`](https://github.com/nodejs/node/commit/f5ac4ec49a)] - **test**: add regression test for signal handler removal in exit (Anna Henningsen) [#30589](https://github.com/nodejs/node/pull/30589)
- [[`825b3057d1`](https://github.com/nodejs/node/commit/825b3057d1)] - **test**: move test-worker-prof to sequential (Rich Trott) [#30628](https://github.com/nodejs/node/pull/30628)
- [[`12ef7d99eb`](https://github.com/nodejs/node/commit/12ef7d99eb)] - **test**: dir class initialisation w/o handler (Dmitriy Kikinskiy) [#30313](https://github.com/nodejs/node/pull/30313)
- [[`2f8dcefa6d`](https://github.com/nodejs/node/commit/2f8dcefa6d)] - **test**: change object assign by spread operator (poutch) [#30438](https://github.com/nodejs/node/pull/30438)
- [[`e52237d66e`](https://github.com/nodejs/node/commit/e52237d66e)] - **test**: use useful message argument in test function (Rich Trott) [#30618](https://github.com/nodejs/node/pull/30618)
- [[`1c9ba2cdc3`](https://github.com/nodejs/node/commit/1c9ba2cdc3)] - **test**: test for minimum ICU version consistency (Richard Lau) [#30608](https://github.com/nodejs/node/pull/30608)
- [[`2e37828350`](https://github.com/nodejs/node/commit/2e37828350)] - **test**: code&learn var to let update (Nazar Malyy) [#30436](https://github.com/nodejs/node/pull/30436)
- [[`01da702fec`](https://github.com/nodejs/node/commit/01da702fec)] - **test**: change object assign to spread object (poutch) [#30422](https://github.com/nodejs/node/pull/30422)
- [[`d708887c0b`](https://github.com/nodejs/node/commit/d708887c0b)] - **test**: use spread instead of Object.assign (dnlup) [#30419](https://github.com/nodejs/node/pull/30419)
- [[`46f698fed5`](https://github.com/nodejs/node/commit/46f698fed5)] - **test**: changed var to let in module-errors (Jamar Torres) [#30413](https://github.com/nodejs/node/pull/30413)
- [[`78c7118ab7`](https://github.com/nodejs/node/commit/78c7118ab7)] - **test**: use spread instead of object.assign (Shubham Chaturvedi) [#30412](https://github.com/nodejs/node/pull/30412)
- [[`e7f1d57cdf`](https://github.com/nodejs/node/commit/e7f1d57cdf)] - **test**: replace var with let in pre_execution.js (Vladimir Adamic) [#30411](https://github.com/nodejs/node/pull/30411)
- [[`d077550c44`](https://github.com/nodejs/node/commit/d077550c44)] - **test**: change var to let in test-trace-events (Jon Church) [#30406](https://github.com/nodejs/node/pull/30406)
- [[`7f0e7fd4b2`](https://github.com/nodejs/node/commit/7f0e7fd4b2)] - **test**: dns utils replace var (Osmond van Hemert) [#30405](https://github.com/nodejs/node/pull/30405)
- [[`0a068db450`](https://github.com/nodejs/node/commit/0a068db450)] - **test**: test cover cases when trace is empty (telenord) [#30311](https://github.com/nodejs/node/pull/30311)
- [[`984c40629c`](https://github.com/nodejs/node/commit/984c40629c)] - **test**: switch to object spread in common/benchmark.js (palmires) [#30309](https://github.com/nodejs/node/pull/30309)
- [[`88bca0fcc0`](https://github.com/nodejs/node/commit/88bca0fcc0)] - **test**: add common.mustCall() to stream test (Rich Trott) [#30561](https://github.com/nodejs/node/pull/30561)
- [[`516bdaf54d`](https://github.com/nodejs/node/commit/516bdaf54d)] - **test**: move explanatory comment to expected location in file (Rich Trott) [#30561](https://github.com/nodejs/node/pull/30561)
- [[`8e36901748`](https://github.com/nodejs/node/commit/8e36901748)] - **test**: move stream test to parallel (Rich Trott) [#30561](https://github.com/nodejs/node/pull/30561)
- [[`0903f67a87`](https://github.com/nodejs/node/commit/0903f67a87)] - **test**: remove string literal as message in strictEqual() in stream test (Rich Trott) [#30561](https://github.com/nodejs/node/pull/30561)
- [[`59c9592679`](https://github.com/nodejs/node/commit/59c9592679)] - **test**: use arrow function for callback in stream test (Rich Trott) [#30561](https://github.com/nodejs/node/pull/30561)
- [[`4a5f00c35d`](https://github.com/nodejs/node/commit/4a5f00c35d)] - **test**: replace setTimeout with setImmediate in stream test (Rich Trott) [#30561](https://github.com/nodejs/node/pull/30561)
- [[`cd5076eb62`](https://github.com/nodejs/node/commit/cd5076eb62)] - **test**: refactor test-dgram-multicast-set-interface-lo.js (Taylor Gagne) [#30536](https://github.com/nodejs/node/pull/30536)
- [[`0d26263b7e`](https://github.com/nodejs/node/commit/0d26263b7e)] - **test**: move test not requiring internet from internet to parallel (Rich Trott) [#30545](https://github.com/nodejs/node/pull/30545)
- [[`9869aaaee4`](https://github.com/nodejs/node/commit/9869aaaee4)] - **test**: use reserved .invalid TLD for invalid address in test (Rich Trott) [#30545](https://github.com/nodejs/node/pull/30545)
- [[`d802393336`](https://github.com/nodejs/node/commit/d802393336)] - **test**: improve assertion message in internet dgram test (Rich Trott) [#30545](https://github.com/nodejs/node/pull/30545)
- [[`6da56e91a3`](https://github.com/nodejs/node/commit/6da56e91a3)] - **test**: add test for options validation of createServer (ZYSzys) [#30541](https://github.com/nodejs/node/pull/30541)
- [[`3fb0f7ebd0`](https://github.com/nodejs/node/commit/3fb0f7ebd0)] - **test**: clean up http-set-trailers (Denys Otrishko) [#30522](https://github.com/nodejs/node/pull/30522)
- [[`e4a1cffcbc`](https://github.com/nodejs/node/commit/e4a1cffcbc)] - **test**: handle undefined default_configuration (Shelley Vohr) [#30465](https://github.com/nodejs/node/pull/30465)
- [[`c0d9a545a3`](https://github.com/nodejs/node/commit/c0d9a545a3)] - **test**: Change from var to const (Jure Stepisnik) [#30431](https://github.com/nodejs/node/pull/30431)
- [[`91c6fe4b61`](https://github.com/nodejs/node/commit/91c6fe4b61)] - **test**: changed var to let in test-repl-editor (JL Phillips) [#30443](https://github.com/nodejs/node/pull/30443)
- [[`4dfcc12666`](https://github.com/nodejs/node/commit/4dfcc12666)] - **test**: improve test-fs-open (Artem Maksimov) [#30280](https://github.com/nodejs/node/pull/30280)
- [[`f0b6a236ac`](https://github.com/nodejs/node/commit/f0b6a236ac)] - **test**: change var to let (nathias) [#30444](https://github.com/nodejs/node/pull/30444)
- [[`df1d73539e`](https://github.com/nodejs/node/commit/df1d73539e)] - **test**: changed var to const in test (Kerry Mahne) [#30434](https://github.com/nodejs/node/pull/30434)
- [[`cf9da71e97`](https://github.com/nodejs/node/commit/cf9da71e97)] - **test**: var to const in test-repl-multiline.js (SoulMonk) [#30433](https://github.com/nodejs/node/pull/30433)
- [[`b49e63d51c`](https://github.com/nodejs/node/commit/b49e63d51c)] - **test**: deflake test-http-dump-req-when-res-ends.js (Luigi Pinca) [#30360](https://github.com/nodejs/node/pull/30360)
- [[`2e6b3be8dd`](https://github.com/nodejs/node/commit/2e6b3be8dd)] - **test**: change var to const in parallel/test-stream-transform-final\* (Kenza Houmani) [#30448](https://github.com/nodejs/node/pull/30448)
- [[`aaedc06ea4`](https://github.com/nodejs/node/commit/aaedc06ea4)] - **test**: replace Object.assign with object spread (Grigoriy Levanov) [#30306](https://github.com/nodejs/node/pull/30306)
- [[`d1483305ae`](https://github.com/nodejs/node/commit/d1483305ae)] - **test**: fix Python unittests in ./test and ./tools (cclauss) [#30340](https://github.com/nodejs/node/pull/30340)
- [[`5e2848d44f`](https://github.com/nodejs/node/commit/5e2848d44f)] - **test**: mark test-http-dump-req-when-res-ends as flaky on windows (AshCripps) [#30316](https://github.com/nodejs/node/pull/30316)
- [[`5b428571e2`](https://github.com/nodejs/node/commit/5b428571e2)] - **test**: fix test-benchmark-cluster (Rich Trott) [#30342](https://github.com/nodejs/node/pull/30342)
- [[`342031eac0`](https://github.com/nodejs/node/commit/342031eac0)] - **test**: deflake test-tls-close-notify.js (Luigi Pinca) [#30202](https://github.com/nodejs/node/pull/30202)
- [[`43cec65d6f`](https://github.com/nodejs/node/commit/43cec65d6f)] - **tls**: allow empty subject even with altNames defined (Jason Macgowan) [#22906](https://github.com/nodejs/node/pull/22906)
- [[`0f7281a305`](https://github.com/nodejs/node/commit/0f7281a305)] - **tls**: change loop var to let (Xavier Redondo) [#30445](https://github.com/nodejs/node/pull/30445)
- [[`6fe2c7a106`](https://github.com/nodejs/node/commit/6fe2c7a106)] - **tls**: replace var with let (Daniil Pletnev) [#30308](https://github.com/nodejs/node/pull/30308)
- [[`d59df36f58`](https://github.com/nodejs/node/commit/d59df36f58)] - **tls**: replace var with let and const (Nolik) [#30299](https://github.com/nodejs/node/pull/30299)
- [[`634aac5b94`](https://github.com/nodejs/node/commit/634aac5b94)] - **tls**: refactor tls_wrap.cc (Artem Maksimov) [#30303](https://github.com/nodejs/node/pull/30303)
- [[`a715c2506b`](https://github.com/nodejs/node/commit/a715c2506b)] - **tools**: enforce blank line between functions (Rich Trott) [#30696](https://github.com/nodejs/node/pull/30696)
- [[`da1e5ae1fd`](https://github.com/nodejs/node/commit/da1e5ae1fd)] - **tools**: add unified plugin changing links for html docs (Marek Łabuz) [#29946](https://github.com/nodejs/node/pull/29946)
- [[`df91d5fd66`](https://github.com/nodejs/node/commit/df91d5fd66)] - **tools**: enable more eslint rules (cjihrig) [#30598](https://github.com/nodejs/node/pull/30598)
- [[`bce08806c7`](https://github.com/nodejs/node/commit/bce08806c7)] - **tools**: update ESLint to 6.7.1 (cjihrig) [#30598](https://github.com/nodejs/node/pull/30598)
- [[`0797cc706a`](https://github.com/nodejs/node/commit/0797cc706a)] - **tools**: fix build at non-English windows (Rongjian Zhang) [#30492](https://github.com/nodejs/node/pull/30492)
- [[`5e8b2a8190`](https://github.com/nodejs/node/commit/5e8b2a8190)] - **tools**: make doctool work if no internet available (Richard Lau) [#30214](https://github.com/nodejs/node/pull/30214)
- [[`05290fd5ea`](https://github.com/nodejs/node/commit/05290fd5ea)] - **tools**: update certdata.txt (AshCripps) [#30195](https://github.com/nodejs/node/pull/30195)
- [[`d9e5b72c0b`](https://github.com/nodejs/node/commit/d9e5b72c0b)] - **tools**: check-imports using utf-8 (cclauss) [#30220](https://github.com/nodejs/node/pull/30220)
- [[`0e68f550e5`](https://github.com/nodejs/node/commit/0e68f550e5)] - **tty**: truecolor check moved before 256 check (Duncan Healy) [#30474](https://github.com/nodejs/node/pull/30474)
- [[`f2f45297a0`](https://github.com/nodejs/node/commit/f2f45297a0)] - **url**: replace var with let in lib/url.js (xefimx) [#30281](https://github.com/nodejs/node/pull/30281)
- [[`d96c76507f`](https://github.com/nodejs/node/commit/d96c76507f)] - **util**: fix inspection of errors with tampered name or stack property (Ruben Bridgewater) [#30576](https://github.com/nodejs/node/pull/30576)
- [[`7421cc8fbd`](https://github.com/nodejs/node/commit/7421cc8fbd)] - **util**: use let instead of var for util/inspect.js (Luciano) [#30399](https://github.com/nodejs/node/pull/30399)
- [[`ec49ea74fe`](https://github.com/nodejs/node/commit/ec49ea74fe)] - **util**: replace var with let (Susana Ferreira) [#30439](https://github.com/nodejs/node/pull/30439)
- [[`3f24a87f41`](https://github.com/nodejs/node/commit/3f24a87f41)] - **v8**: mark serdes API as stable (Anna Henningsen) [#30234](https://github.com/nodejs/node/pull/30234)
- [[`2994976ec7`](https://github.com/nodejs/node/commit/2994976ec7)] - **v8**: inspect unserializable objects (Anna Henningsen) [#30167](https://github.com/nodejs/node/pull/30167)

Windows 32-bit Installer: https://nodejs.org/dist/v12.14.1/node-v12.14.1-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v12.14.1/node-v12.14.1-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v12.14.1/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v12.14.1/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v12.14.1/node-v12.14.1.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v12.14.1/node-v12.14.1-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v12.14.1/node-v12.14.1-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v12.14.1/node-v12.14.1-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v12.14.1/node-v12.14.1-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v12.14.1/node-v12.14.1-aix-ppc64.tar.gz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v12.14.1/node-v12.14.1-sunos-x64.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v12.14.1/node-v12.14.1-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v12.14.1/node-v12.14.1-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v12.14.1/node-v12.14.1.tar.gz \
Other release files: https://nodejs.org/dist/v12.14.1/ \
Documentation: https://nodejs.org/docs/v12.14.1/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

7d7621ff0c037ea556284e382f3c5c98af0dad146786fef133a71cea1bd661fc  node-v12.14.1-aix-ppc64.tar.gz
0be10a28737527a1e5e3784d3ad844d742fe8b0718acd701fd48f718fd3af78f  node-v12.14.1-darwin-x64.tar.gz
aa6cfba37e018aec20f0870931b9a39b923697a2a1d84aad3292bdbad7bd6b18  node-v12.14.1-darwin-x64.tar.xz
944b436e1e8fe19b0c7397ebd3680abefe81d5958d9341bef99fbe0c9fffa93c  node-v12.14.1-headers.tar.gz
9cd7975e52f2caf2b086d9276c36500e40ac68ec13b07d211a260c94f51ca9f0  node-v12.14.1-headers.tar.xz
fb1a20f37ef918033b0f2f9436b4a82e15128ce61e0de2378a4306ba7667cf4a  node-v12.14.1-linux-arm64.tar.gz
6cd28a5e6340f596aec8dbfd6720f444f011e6b9018622290a60dbd17f9baff6  node-v12.14.1-linux-arm64.tar.xz
49c03bf2aa85b19a286a2dbad471b6dd89a8e4542564a41cd23b5b08e81b9c65  node-v12.14.1-linux-armv7l.tar.gz
ed4e625c84b877905eda4f356c8b4183c642e5ee6d59513d6329674ec23df234  node-v12.14.1-linux-armv7l.tar.xz
280c4f432261180004c33c6fe73b008b84926f9c5b2997e9df4da691d22e081b  node-v12.14.1-linux-ppc64le.tar.gz
871bede5bf54a3596b485d9e42c37dd5832aedd999998964dfe991aff7cb3778  node-v12.14.1-linux-ppc64le.tar.xz
9aeed5fd472028228db8dc88b27b8f11ffc9af3c8ca3dedba49e6e02ea42a3d2  node-v12.14.1-linux-s390x.tar.gz
d734271d8ce8c9c0b9887bf246843fbac71c37c42bd2ef28ea5e2bfef0cd0f8a  node-v12.14.1-linux-s390x.tar.xz
e21be7dd07cc143c480695d6214f40873a7791f437c1ca12fc94d45f539a47d9  node-v12.14.1-linux-x64.tar.gz
07cfcaa0aa9d0fcb6e99725408d9e0b07be03b844701588e3ab5dbc395b98e1b  node-v12.14.1-linux-x64.tar.xz
043b4d3d35c3ede0c496d2cb32ce51e37d70bf5f21c0dc0598db7e2af46ac667  node-v12.14.1.pkg
17bcbf5e7b1b57f73f00bbb8e77e8f6393db41e4d4a1bfc6c2f601606681e659  node-v12.14.1-sunos-x64.tar.gz
ec6e3e9b33cfd7083b2e7203bfe54afb53cd40d5e027dcc57820e21e2f77948f  node-v12.14.1-sunos-x64.tar.xz
42a7f0777fea8825611cb9250ff927824dba4f7aea854b47d522798acf4bdbc6  node-v12.14.1.tar.gz
877b4b842318b0e09bc754faf7343f2f097f0fc4f88ab9ae57cf9944e88e7adb  node-v12.14.1.tar.xz
5f071589df21a6d2e99fc7a52649f0a7e5d5b36ef8f619bd7dbd39e6e2f47be2  node-v12.14.1-win-x64.7z
1f96ccce3ba045ecea3f458e189500adb90b8bc1a34de5d82fc10a5bf66ce7e3  node-v12.14.1-win-x64.zip
b94daef84daaf40e2a3841d8ecad679462ad48c2d5db83bc7d1289a27882a00c  node-v12.14.1-win-x86.7z
0c780bb80cf24a036517bd293e676d56f1d75a0968bd75720e6d8436c64cfa2f  node-v12.14.1-win-x86.zip
630bc34155e7fdb65c02ed44a37cd27dcf3f76a061c220e7af8baacdb0c2bb9c  node-v12.14.1-x64.msi
ebb719fdb1e27782e0bc2110af1b836657fb1078353757da265d4256e5dfdb2b  node-v12.14.1-x86.msi
47502c82c22d24fa38fd3b3e4b12e5298297cb15a0ecb9528aae4ac0e40248be  win-x64/node.exe
30ec1ab45d9d22ad93e7c95691640d83818175b4fd74680d12a1f900a7d87f80  win-x64/node.lib
86ae4f23821a42f3bae166dc579fae1c59a1b0195f87b76bedc838c4b2570656  win-x64/node_pdb.7z
c4cac095f23af3272077d72dfe958238bf93c75effc25a387ce98adc56ec35d4  win-x64/node_pdb.zip
4a19c897619e96c3402608e1c4107c441efd05c3c0242b43bccffd7cee43385b  win-x86/node.exe
87a49b27dfc454c5f8ff71dffb0c27322a8148a661d216741156ad61c2493cd1  win-x86/node.lib
080876ee76462d59275902f03f676021fabc8ed6ef545406efbedf41273acb55  win-x86/node_pdb.7z
930bf28f9400e7333b460fa1e7512c1902b0a3de54d16d0fe5eeb183ede1b411  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQEzBAEBCAAdFiEETtd49TnjY0x3nIfG1wYoSKGrAFwFAl4UwbUACgkQ1wYoSKGr
AFwqhAgAof4m1zfjv28T72ZdCczeYW0BM0NraceGfEzrFRZfff3ey8wbJPSo+7vr
eQ4UFqP1ZldZO5nuIq2Nt4Vl7M69Vmsn2IpKuNhr+A3OfR7KWIR7h/Z7C98sKvPw
9PTBnS/dDjfcZgO075oRZn2RlDt3MJ2FABkain/UEiJOKhen325qlPtzOvclqqHw
mRdo3UH0ax9syjeHDZolT3cl1RrEQinL+jIFoEVjup1zh896MdFByGkbUspNeAF6
UAhZy2eNtD7sWaFkZTHvNpsxqZZUV6/SD2k6k1b5xOMd0b64mEvqd8usT1f9qgm6
UrKSzIpG8pK3Hec8gsaf0VOKG6ML3g==
=d0AL
-----END PGP SIGNATURE-----

```
