---
date: '2025-01-21T17:05:48.839Z'
category: release
title: Node v22.13.1 (LTS)
layout: blog-post
author: <PERSON>
---

## 2025-01-21, Version 22.13.1 'Jod' (LTS), @RafaelGSS

This is a security release.

### Notable Changes

- CVE-2025-23083 - src,loader,permission: throw on InternalWorker use when permission model is enabled (High)
- CVE-2025-23085 - src: fix HTTP2 mem leak on premature close and ERR_PROTO (Medium)
- CVE-2025-23084 - path: fix path traversal in normalize() on Windows (Medium)

Dependency update:

- CVE-2025-22150 - Use of Insufficiently Random Values in undici fetch() (Medium)

### Commits

- \[[`520da342e0`](https://github.com/nodejs/node/commit/520da342e0)] - **(CVE-2025-22150)** **deps**: update undici to v6.21.1 (<PERSON>) [nodejs-private/node-private#662](https://github.com/nodejs-private/node-private/pull/662)
- \[[`99f217369f`](https://github.com/nodejs/node/commit/99f217369f)] - **(CVE-2025-23084)** **path**: fix path traversal in normalize() on Windows (Tobias Nießen) [nodejs-private/node-private#555](https://github.com/nodejs-private/node-private/pull/555)
- \[[`984f735e35`](https://github.com/nodejs/node/commit/984f735e35)] - **(CVE-2025-23085)** **src**: fix HTTP2 mem leak on premature close and ERR_PROTO (RafaelGSS) [nodejs-private/node-private#650](https://github.com/nodejs-private/node-private/pull/650)
- \[[`2446870618`](https://github.com/nodejs/node/commit/2446870618)] - **(CVE-2025-23083)** **src,loader,permission**: throw on InternalWorker use (RafaelGSS) [nodejs-private/node-private#651](https://github.com/nodejs-private/node-private/pull/651)

Windows 32-bit Installer: https://nodejs.org/dist/v22.13.1/node-v22.13.1-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v22.13.1/node-v22.13.1-x64.msi \
Windows ARM 64-bit Installer: https://nodejs.org/dist/v22.13.1/node-v22.13.1-arm64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v22.13.1/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v22.13.1/win-x64/node.exe \
Windows ARM 64-bit Binary: https://nodejs.org/dist/v22.13.1/win-arm64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v22.13.1/node-v22.13.1.pkg \
macOS Apple Silicon 64-bit Binary: https://nodejs.org/dist/v22.13.1/node-v22.13.1-darwin-arm64.tar.gz \
macOS Intel 64-bit Binary: https://nodejs.org/dist/v22.13.1/node-v22.13.1-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v22.13.1/node-v22.13.1-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v22.13.1/node-v22.13.1-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v22.13.1/node-v22.13.1-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v22.13.1/node-v22.13.1-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v22.13.1/node-v22.13.1-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v22.13.1/node-v22.13.1-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v22.13.1/node-v22.13.1.tar.gz \
Other release files: https://nodejs.org/dist/v22.13.1/ \
Documentation: https://nodejs.org/docs/v22.13.1/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

ed52239294ad517fbe91a268146d5d2aa8a17d2d62d64873e43219078ba71c4e  node-v22.13.1-aix-ppc64.tar.gz
be127be1d98cad94c56f46245d0f2de89934d300028694456861a6d5ac558bf3  node-v22.13.1-arm64.msi
97483ff4361d239a56d038c6335767a56a291e78c10f07446f463f05d9d19b89  node-v22.13.1-darwin-arm64.tar.gz
9c169a9369f6c667a4de6b14c7492065adbae0fa830ef4c666bea2c53ac7b576  node-v22.13.1-darwin-arm64.tar.xz
6fdcc8412d434664238b0651ebd5ad55d15a08598ff42dcb6d9cf1d434a6c4be  node-v22.13.1-darwin-x64.tar.gz
cbc49a2f179ec51f3a7d49f91b05e16db2ea8ea4ca586e1e370d19661becf0eb  node-v22.13.1-darwin-x64.tar.xz
f9cde9ace585c3979f1b4ee247914f35fae6e7b7eabc6a40961f89ad39e78964  node-v22.13.1-headers.tar.gz
2722236564df6d33b1d953f23e21bf5247b62b38ea9000b47c655ee3a9a440e7  node-v22.13.1-headers.tar.xz
911d9c07af38c82be22cd0a3db613aabc578ba940b35380aeedadd6d48070bc1  node-v22.13.1-linux-arm64.tar.gz
0a237c413ccbab920640438bf6e1a32edb19845bdc21f0e1cd5b91545ce1c126  node-v22.13.1-linux-arm64.tar.xz
82be9fa5e74ee29d7342d38306dbee19d3e2239b5b753870c04fd03768916a7e  node-v22.13.1-linux-armv7l.tar.gz
f2be8dca2a7a518f6d187aa4b18abbeeafd71096a6d95f73f4d8bc0f8d2394ea  node-v22.13.1-linux-armv7l.tar.xz
e4d34550d791cc809cfbfe8d0e3082634796add404169484b0849fbae0714576  node-v22.13.1-linux-ppc64le.tar.gz
377a7a1ea66f39251e1657f419e9404d526fcca9910620d0ecf0a870c6308f6b  node-v22.13.1-linux-ppc64le.tar.xz
56375cf2c827a425d708bd0322fd635b6f2038e272468395f4e160e1ea4ae91e  node-v22.13.1-linux-s390x.tar.gz
22da01dbcead3ef7e69de6c1310a1c5c485039631f731a6ff0c35530cf5c811b  node-v22.13.1-linux-s390x.tar.xz
666148b9fe0c7e1301cc1b029e33a45e9e4a893f68d2d2bb1cc88a931a88a004  node-v22.13.1-linux-x64.tar.gz
0d2a5af33c7deab5555c8309cd3f373446fe1526c1b95833935ab3f019733b3b  node-v22.13.1-linux-x64.tar.xz
620a7b4008aa0406678987ce2dd22458a38bae163fe7c69fd243f1204725e6c2  node-v22.13.1.pkg
e7d5b1e84e7f3c3cebda81e2b138469eef41ba4ecf16a87fd15fc3f7afa3f701  node-v22.13.1.tar.gz
cfce282119390f7e0c2220410924428e90dadcb2df1744c0c4a0e7baae387cc2  node-v22.13.1.tar.xz
b2c537f24a725d7e6058d23b1b89bbf31e6c7299b51ac31e9c25dc3c6a61e2d9  node-v22.13.1-win-arm64.7z
db6d3d28e1b34acdbd9db7bac5ec37980e07e48a6a2edcd3747d605fc8a5468e  node-v22.13.1-win-arm64.zip
d495abe2ae53962065fad979814259735fd90a4e03c6b94ccd3e32bc933aeda5  node-v22.13.1-win-x64.7z
398a61e250a5584a62a5959e2f69f5d597fc83f1a5ebe3ed8fff29ba39d55f14  node-v22.13.1-win-x64.zip
3c87ddd4aac7f980ed11caf97942bd87a647ce61d644ca08321508836b3d1899  node-v22.13.1-win-x86.7z
504ed03c8596dfeabddfcd0736f987be93e3330a5c690306dedacef8880b592c  node-v22.13.1-win-x86.zip
821566022dc3b262ac2f76598ee4f46003a6edafe5dadb84e5fbc7daaa1a78c7  node-v22.13.1-x64.msi
79c36ea6aa2ede10b416bdae55b568ab31798ede2697440b266312bba95ef580  node-v22.13.1-x86.msi
afb7dcd60c7557843e5c2777205564950544297ba0588faa2aae573ccb735767  win-arm64/node.exe
988eb8c60a5ade17e652dbdb60d56d3c6ad5e599a99ce04932b8c4c86583cdaf  win-arm64/node.lib
b19efd6f54283a2c01027f0e74e54563d06495b87efe08d111e1a176afd14d02  win-arm64/node_pdb.7z
0bb490f44fc575dd570326fae9f0a5fca1187bfc342f57a50084ef86aa2e6679  win-arm64/node_pdb.zip
8f6945c55a51c893691534e7163372e4cedb62c8ad80a2a975df3f14a19fba16  win-x64/node.exe
65e45757c026c93a170743a811ef1b921ae12d6d9dd62d258bbbca0626687626  win-x64/node.lib
2ad1af26e2f78247473e1e05f78fff3be7401f47c327f45602c84dce5d552cc0  win-x64/node_pdb.7z
31099d09933aad429c36071503cd200eb66c41529524ac159873f97d9f097c83  win-x64/node_pdb.zip
0b7071f0a7d90d4e1567d94a37c5d94441a3ca71a4eaa9596bc06e992c06e9c8  win-x86/node.exe
79bae10059e833ce7fa4de05e5601034461327e2e7cb75c2144b87d4ab5ac547  win-x86/node.lib
c3fb150e58d16dadd95c24c8a0a4c020a18b3c852b63e36df39587a301512ca4  win-x86/node_pdb.7z
97890935271fe117a745584ca710f0fcd38bbf24b8c920800411df87a3e22dc3  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQGzBAEBCAAdFiEEiQwI24V5Fi/uDfnbi+q0389VXvQFAmeP0bUACgkQi+q0389V
XvTMfgv+JrSbhYgsQcE3md4lAbKnFvK6iitjeefWXednZAHU3IRauZQyfwW0FZfM
2k9VPHR2AhqgDbvaFWE5hfAV4tQ244U/RojS5K8irEm80Eryp1RWLJ92D4DtNFDH
p4hH1FRuOW5Rd+TzfuADPv/eNfFn6h9u/N+zcOHYVk6PeActzte+GKV56aiooYzg
LKDH9sMTjV792qJmEcesNnQ5D5y23gSUiEVC8p1GYyQBhingD3tzrIoc0wRUZ8LB
36GUn/v5EikD78ts3Jiq96NW+KcstqGSFLu7P57Opkgd/a5+lqYPcn6XB2vzTV0S
HAC07OQzNnafUoXipUqnbg/Z4PXsi+LayPzoaUKp+sQLIEY+CNvTCygI5poYIEp/
+yuHkF/tYGhYh+47jMJsh/dpcm/ak71ia0hE2Qj9oY92y3gTln7VdcbZdkmTEOVa
0TI7QhPeN4FACCBxsaWVTosW4tJw972h2mqieahJqLyrUEMbWBo/R5LuU1NXGlJf
nZrtP89S
=vTe7
-----END PGP SIGNATURE-----
```
