---
date: '2015-12-04T03:04:00.000Z'
category: release
title: Node v5.1.1 (Current)
layout: blog-post
author: <PERSON>
---

**This is an important security release**. All Node.js users should consult our [December Security Release Summary](/blog/vulnerability/december-2015-security-releases/) for details on patched vulnerabilities.

### Notable changes(/

- **http**: Fix a bug where an HTTP socket may no longer have an associated parser but a pipelined request triggers a pause or resume, a potential denial-of-service vector. (<PERSON>or <PERSON>)
- **openssl**: Upgrade to 1.0.2e, containing fixes for:
  - CVE-2015-3193 "BN_mod_exp may produce incorrect results on x86_64", an attack may be feasible against a Node.js TLS server using DHE key exchange. Details are available at <http://openssl.org/news/secadv/20151203.txt>.
  - CVE-2015-3194 "Certificate verify crash with missing PSS parameter", a potential denial-of-service vector for Node.js TLS servers using client authentication; TLS clients are also impacted. Details are available at <http://openssl.org/news/secadv/20151203.txt>.
    (<PERSON><PERSON><PERSON>) [#4134](https://github.com/nodejs/node/pull/4134)
- **v8**: Backport fixes for a bug in `JSON.stringify()` that can result in out-of-bounds reads for arrays. (Ben Noordhuis)

### Known issues

- Surrogate pair in REPL can freeze terminal. [#690](https://github.com/nodejs/node/issues/690)
- Calling `dns.setServers()` while a DNS query is in progress can cause the process to crash on a failed assertion. [#894](https://github.com/nodejs/node/issues/894)
- `url.resolve` may transfer the auth portion of the url when resolving between two full hosts, see [#1435](https://github.com/nodejs/node/issues/1435).
- Unicode characters in filesystem paths are not handled consistently across platforms or Node.js APIs. See [#2088](https://github.com/nodejs/node/issues/2088), [#3401](https://github.com/nodejs/node/issues/3401) and [#3519](https://github.com/nodejs/node/issues/3519).

### Commits

- [[`678398f250`](https://github.com/nodejs/node/commit/678398f250)] - **deps**: backport a7e50a5 from upstream v8 (Ben Noordhuis)
- [[`76a552c938`](https://github.com/nodejs/node/commit/76a552c938)] - **deps**: backport 6df9a1d from upstream v8 (Ben Noordhuis)
- [[`533881f889`](https://github.com/nodejs/node/commit/533881f889)] - **deps**: upgrade openssl sources to 1.0.2e (Shigeki Ohtsu) [#4134](https://github.com/nodejs/node/pull/4134)
- [[`12e70fafd3`](https://github.com/nodejs/node/commit/12e70fafd3)] - **http**: fix pipeline regression (Fedor Indutny)

Windows 32-bit Installer: https://nodejs.org/dist/v5.1.1/node-v5.1.1-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v5.1.1/node-v5.1.1-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v5.1.1/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v5.1.1/win-x64/node.exe \
Mac OS X 64-bit Installer: https://nodejs.org/dist/v5.1.1/node-v5.1.1.pkg \
Mac OS X 64-bit Binary: https://nodejs.org/dist/v5.1.1/node-v5.1.1-darwin-x64.tar.gz \
Linux 32-bit Binary: https://nodejs.org/dist/v5.1.1/node-v5.1.1-linux-x86.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v5.1.1/node-v5.1.1-linux-x64.tar.gz \
SmartOS 32-bit Binary: https://nodejs.org/dist/v5.1.1/node-v5.1.1-sunos-x86.tar.gz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v5.1.1/node-v5.1.1-sunos-x64.tar.gz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v5.1.1/node-v5.1.1-linux-armv6l.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v5.1.1/node-v5.1.1-linux-armv7l.tar.gz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v5.1.1/node-v5.1.1-linux-arm64.tar.gz \
Source Code: https://nodejs.org/dist/v5.1.1/node-v5.1.1.tar.gz \
Other release files: https://nodejs.org/dist/v5.1.1/ \
Documentation: https://nodejs.org/docs/v5.1.1/api/

Shasums (GPG signing hash: SHA512, file hash: SHA256):

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA1

cb6c831e7c3a8432a14a0e4ddb2000295c0166abce06b2d50134cc2cccb2dc9c  node-v5.1.1-darwin-x64.tar.gz
12566e3edfc24ad0efebcfa20ceed79ca87811bafe2c8b92432ff4614885c6ef  node-v5.1.1-darwin-x64.tar.xz
cc320d0a3df073e9e92816d5698d69ade780ce854f26146539f0b29cb5be616a  node-v5.1.1-headers.tar.gz
f0dba3ef1f953a8ebb4c610a1279a48ed1ba57fe24ab7783fd0fedadeaba3f9c  node-v5.1.1-headers.tar.xz
1723abf50ee9b2b2209af06374523ae657c5562166bdc44b7b8d32801484c572  node-v5.1.1-linux-arm64.tar.gz
5fefa92daf840076947a7ac2e97bcd2dcf35e1c632251b5ba73357318c8a7a1f  node-v5.1.1-linux-arm64.tar.xz
e7b154a5f7574155df5e3b5df90af762ab0edde128e36eeac0bd8ba2a8f00697  node-v5.1.1-linux-armv6l.tar.gz
30011d4caca9d07e076c11beed7e2c67c0e4e3b20ac29cb80d479db1131ab78f  node-v5.1.1-linux-armv6l.tar.xz
fd96f77310708097cf9e783b9842122a4e2859674965734b7b22a615cb756165  node-v5.1.1-linux-armv7l.tar.gz
ac0b4e73a180406883d8c5700623966093f0442988facf024d59129fa9017434  node-v5.1.1-linux-armv7l.tar.xz
0c1a0788dfc07d1cfac08b9789f0e52950e80e61944e1684b27600463a5d2623  node-v5.1.1-linux-x64.tar.gz
0de39988d293434eedb2c78e48dddfa5cb7c7f77cb4a1e6c2e4af4d44caf10aa  node-v5.1.1-linux-x64.tar.xz
30099a0f305899aacdfd974873807b6bd8b6971c0a26209220a6e4cd88c08d70  node-v5.1.1-linux-x86.tar.gz
e00bf38d9e24b4631ea0ff089ffeea39f37512900ca9e50628e716ecb0083184  node-v5.1.1-linux-x86.tar.xz
7311f4848381ded99fd5415e818337efb9b9138656fbab80eb5fcf7f42d7bde8  node-v5.1.1.pkg
14dfdf63f1ac8c9972199e7a62d8c732cf269e081aec6caa9e0e3f46d116d486  node-v5.1.1-sunos-x64.tar.gz
1c45a06cd78ef236cfb331a43677ca514b1e5f9d52603c608fae998986f43cfb  node-v5.1.1-sunos-x64.tar.xz
11ccaadb1e22b3c80548686caf19070c34017667866285fe8b6cb8b9e6afca30  node-v5.1.1-sunos-x86.tar.gz
3971913cfce5182b626b761e7435811837542424da8d84d146383b37bba6ff95  node-v5.1.1-sunos-x86.tar.xz
a779e024f800b5ec51f375fa1c14eda7254216daa36a1960cc1e4195b9fc22c3  node-v5.1.1.tar.gz
b3aaa01051576425afce753d30b16be67f391222ff445b0c716ccf9e12d1b94b  node-v5.1.1.tar.xz
d008ca8791145cf64db1a6ffc177fd70766619953d46570a1e39258c23001ed2  node-v5.1.1-x64.msi
cea518f4bccc4818f77f09bb7c6e77a1b84d3cff91f2316d9d1596bf0af484bb  node-v5.1.1-x86.msi
482cc88532d945e9e867c7f25f8182062dc8446b4457a6906a4bfdfafae9b947  win-x64/node.exe
82feb5cf14a34da483795ba2c012e63d405f36125912f6ac7584072618bf46da  win-x64/node.lib
0e86bac3fd75a07631b8048cd69dd515105a4e4c5bd300716bbf8678afce758e  win-x86/node.exe
ed4c4367f53e14e5bd3feb35446aa3f1f533892455fc646cd5efa8b7cfce24dc  win-x86/node.lib
-----BEGIN PGP SIGNATURE-----
Version: GnuPG v1

iQEcBAEBAgAGBQJWYPxuAAoJEMJzeS99g1RdDagIAKhiCiWHO3jRsTew/tHxROuQ
AA88yXh2SHafXgkE5/iSxAb/oecOOZLQGjg3MrIZnwgZJweqjFmePLc6XiEZNDU5
FiN/8SUTknW8no30SIr6yaE7S//Lw1xpeZLgYsjcqeKJ32P11hQ1TdfbXIigR0AM
EhZs4m55X76mD4+wn6LnZeQxoxdXtsk+HHhNGesPOiNw0dpaVidkWqzISvSHy6lS
zTD35CUdmjaHezu680il2WED3aublrRBwTmaXowUHnUGI0JQtUDkcA9ohMrfqO8F
PwoGrXDYw8YIZWUux4Jc3kBbgA+PsyVWw31hdT1mJjYqP9SIaGIbsh1j1dCsC7g=
=8elM
-----END PGP SIGNATURE-----

```
