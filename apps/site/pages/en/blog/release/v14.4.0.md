---
date: '2020-06-02T18:41:48.802Z'
category: release
title: Node v14.4.0 (Current)
layout: blog-post
author: <PERSON><PERSON><PERSON>
---

### Notable changes

This is a security release.

Vulnerabilities fixed:

- **CVE-2020-8172**: TLS session reuse can lead to host certificate verification bypass (High).
- **CVE-2020-11080**: HTTP/2 Large Settings Frame DoS (Low).
- **CVE-2020-8174**: `napi_get_value_string_*()` allows various kinds of memory corruption (High).

### Commits

- [[`07a4d5061f`](https://github.com/nodejs/node/commit/07a4d5061f)] - **crypto**: update root certificates (AshCripps) [#33682](https://github.com/nodejs/node/pull/33682)
- [[`0a7bf50fd4`](https://github.com/nodejs/node/commit/0a7bf50fd4)] - **(SEMVER-MINOR)** **deps**: update nghttp2 to 1.41.0 (<PERSON>) [nodejs-private/node-private#204](https://github.com/nodejs-private/node-private/pull/204)
- [[`55e4c72af8`](https://github.com/nodejs/node/commit/55e4c72af8)] - **(SEMVER-MINOR)** **http2**: implement support for max settings entries (James M Snell) [nodejs-private/node-private#204](https://github.com/nodejs-private/node-private/pull/204)
- [[`290720d16a`](https://github.com/nodejs/node/commit/290720d16a)] - **napi**: fix memory corruption vulnerability (Tobias Nießen) [nodejs-private/node-private#195](https://github.com/nodejs-private/node-private/pull/195)
- [[`94571c1001`](https://github.com/nodejs/node/commit/94571c1001)] - **tls**: emit `session` after verifying certificate (Fedor Indutny) [nodejs-private/node-private#200](https://github.com/nodejs-private/node-private/pull/200)
- [[`1658cf9ee6`](https://github.com/nodejs/node/commit/1658cf9ee6)] - **tools**: update certdata.txt (AshCripps) [#33682](https://github.com/nodejs/node/pull/33682)

Windows 32-bit Installer: https://nodejs.org/dist/v14.4.0/node-v14.4.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v14.4.0/node-v14.4.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v14.4.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v14.4.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v14.4.0/node-v14.4.0.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v14.4.0/node-v14.4.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v14.4.0/node-v14.4.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v14.4.0/node-v14.4.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v14.4.0/node-v14.4.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v14.4.0/node-v14.4.0-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v14.4.0/node-v14.4.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v14.4.0/node-v14.4.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v14.4.0/node-v14.4.0.tar.gz \
Other release files: https://nodejs.org/dist/v14.4.0/ \
Documentation: https://nodejs.org/docs/v14.4.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

5acdf3a6cf15342b07c9ef5e2292b949178dd69462bd07ea7b5f2d28cfa74296  node-v14.4.0-aix-ppc64.tar.gz
d95eaa6950d67895b5cdd0e2f913d2c44034178234f0cb7436c3397b54f64023  node-v14.4.0-darwin-x64.tar.gz
98af3b9c9a179fbf731c06a9acedeb00c7fff8c8cb18cb48fa6f6cccd0013cd2  node-v14.4.0-darwin-x64.tar.xz
81108ee463ff754f18cb5842708f55b62edd469ca28974419dd9b9691d4502d7  node-v14.4.0-headers.tar.gz
558203f5c6a029ecf42ef09bd27b20ff24b71b5c39ab9e11ce0803b2882ea2c1  node-v14.4.0-headers.tar.xz
5c7d88985ea82ca8ed3453b5bdf36391cf6f8fe63aabfb7661a6040c43769f89  node-v14.4.0-linux-arm64.tar.gz
9c9f84589b7bc6a05ac12a137e5097a5adb20b5c63ae9e4e912942da2c06d99d  node-v14.4.0-linux-arm64.tar.xz
2908687e2ebba6e8f60d692ccf4b2499376cd1da1dba66c300f366b3a570e427  node-v14.4.0-linux-armv7l.tar.gz
093747788c07e7c946386540de05bda2c52e30b53e54ed70967751e22d0efab3  node-v14.4.0-linux-armv7l.tar.xz
cce72cd773aae6ba0ee079643112cb0c600e9f58410294d44551e79b90a65f92  node-v14.4.0-linux-ppc64le.tar.gz
cb7ccdd1f436fcfc22967a925f4f017024eaa8287caa2d3c6f40f26bf149a969  node-v14.4.0-linux-ppc64le.tar.xz
f1d52b54498673ebd88e3f1c5558376b9bc6063b03a2ca2299b29a413bce7863  node-v14.4.0-linux-s390x.tar.gz
69ab7436e6bb3904015533862d5c1950abb0930d4ee20547322a4b5cf7cacc88  node-v14.4.0-linux-s390x.tar.xz
8e219f15f496d975910c3964d7ccb7b88d4dc68992b52a18396e05280b1cd642  node-v14.4.0-linux-x64.tar.gz
d65a9a8a547bfe67c6c08dae733a3e5a846700d5377c5f150164cc6bb5f6a039  node-v14.4.0-linux-x64.tar.xz
c2f0ed7cbf63b52cc5be5bb01e9eca71baf5b19c9c52edfad326c3e1c78677b1  node-v14.4.0.pkg
5769ce6cf619e3f38dab9d64eab204722665b8b0f5bd2568ac171e2edc6db7f7  node-v14.4.0.tar.gz
1d78f6a8c435a6b3f4ff0c51579c03ef89ed3b50ccce7f34f0fa52e7460e7db9  node-v14.4.0.tar.xz
e7acff0d26869df5565d3e448f617d0803dc4b400837db37645f099c61524323  node-v14.4.0-win-x64.7z
a4bac45af8252f6b677a79ed19be4913f4939c4509fb08b6f14f972597550bbe  node-v14.4.0-win-x64.zip
62643417a42ecae257a7f4d9cb5ddc86a2251012b33060070f4f5ac8af0f20c0  node-v14.4.0-win-x86.7z
2fe2f7c4f4c73cd81a93f3430889dde09fa772db9f9cecad70bc8c727084f12e  node-v14.4.0-win-x86.zip
b6eb977eaa934f610a5028784614fb2e7a34587f9f9e2e6215fb9a62f784e947  node-v14.4.0-x64.msi
67b9d369b9517ef030f4fdaf3dbab07e9b74048c5b0528335202deb8e3d7a768  node-v14.4.0-x86.msi
5dfd6635d3ccdbbe9e6074b7e382ffe2dc424dfa13f7f3654e94be212f05ca75  win-x64/node.exe
659671d49c051eedcd3dd529d5a238988f61d29307986607b1fb550afca5fad2  win-x64/node.lib
a9e29d58c6274f3af435a9524b04d78e8233730418e40606b5980e335be07dcf  win-x64/node_pdb.7z
2d33991f44e9632576f98d31dca073baf9a7bcd5e31461d66aaf96fc98b9569f  win-x64/node_pdb.zip
de201f347920b0c233ec427a5a9aff72c2874bbb5032302a73606e964e76e595  win-x86/node.exe
565e9b5549a7803661e9c2c3153525387c28a4c18b495c083fee4a038c552faf  win-x86/node.lib
8b0a32600772814743525b552c7ff3c869238bfc5fcbdb497d3b2bc85044bf36  win-x86/node_pdb.7z
bede61118fdc7c9ddc6e86e4f553990bc81f87adb4219a04a3fc77176b5e97ae  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEj8yhP+8dDC6RAI4Jdw96mlrhVgAFAl7WnOEACgkQdw96mlrh
VgDVaBAAi7/lyhl09zn/QXqrpffoNB4tHI0s8RAHbMUW2xT7lnjuY1eenzOS/FKe
xr5TCHkKwqIoWeW9sMhnJ61vG54d3UMtnOdIEic5/XbWy5+sOysImFJ5y4PQGnWC
CmW0VzKKVTwevWDsIZ88zjCOcS8bxBHm8UMvUKLsNx37upGGhSJ1h90Cd1hWsiId
w0zKFxoMGEGmfUWacUKuTfm1GH9mFgwiQr//yA7DHa1M0eqRdQNdULo/NyGtNK21
vFeJ4Cg56d8BBvmeqvEsBuh9nNVXB2fRrYpfRJePV021DVArY3otIBqFfBv2COVd
BueA7g+FSb2+Pz1aa+Xh36R1S4NeVoqSf4XxF3tqckILCoPYRrkDPc/YrwkesNu4
kpYo5UsWAnhRKM43WwVULIEs/u164ccVHoFsH3vmfxq/IduI9H3jyliy+PrPfl2c
pIxgkmZoqmWcyxoNNnRFqYNanPQrckdQvWcfczt6sBR3r7lXG/LEUKK15p2gny1d
nXww8LZagyANA7x2SrOWUAqT1FIvA22OZfqJzlMAgVnqdYd2UHPTWurmM26IZMpC
c5mQKvRSDJpj4oHIWL1tjYc8PbvghNJiv20YloZPPY+tMfO6Fddc8H63RzhC+rxM
QxAlXjPPrAhG2DSGNTCErV4+N2yZ+2kqrhAetBn3J9FByHdNSrE=
=DwWs
-----END PGP SIGNATURE-----

```
