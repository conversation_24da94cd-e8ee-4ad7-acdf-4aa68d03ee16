---
date: '2019-10-22T19:21:58.698Z'
category: release
title: Node v10.17.0 (LTS)
layout: blog-post
author: <PERSON>
---

### Notable changes

- **crypto**:
  - add support for chacha20-poly1305 for AEAD (chux0519) [#24081](https://github.com/nodejs/node/pull/24081)
  - increase maxmem range from 32 to 53 bits (<PERSON>) [#28799](https://github.com/nodejs/node/pull/28799)
- **deps**:
  - update npm to 6.11.3 (claudiahdz) [#29430](https://github.com/nodejs/node/pull/29430)
  - upgrade openssl sources to 1.1.1d (<PERSON>) [#29921](https://github.com/nodejs/node/pull/29921)
- **dns**: remove dns.promises experimental warning (cjihrig) [#26592](https://github.com/nodejs/node/pull/26592)
- **fs**: remove experimental warning for fs.promises (<PERSON>) [#26581](https://github.com/nodejs/node/pull/26581)
- **http**: makes response.writeHead return the response (Mark S. Everitt) [#25974](https://github.com/nodejs/node/pull/25974)
- **http2**: makes response.writeHead return the response (Mark S. Everitt) [#25974](https://github.com/nodejs/node/pull/25974)
- **n-api**:
  - make func argument of napi_create_threadsafe_function optional (legendecas) [#27791](https://github.com/nodejs/node/pull/27791)
  - mark version 5 N-APIs as stable (Gabriel Schulhof) [#29401](https://github.com/nodejs/node/pull/29401)
  - implement date object (Jarrod Connolly) [#25917](https://github.com/nodejs/node/pull/25917)
- **process**: add --unhandled-rejections flag (Ruben Bridgewater) [#26599](https://github.com/nodejs/node/pull/26599)
- **stream**:
  - implement Readable.from async iterator utility (Guy Bedford) [#27660](https://github.com/nodejs/node/pull/27660)
  - make Symbol.asyncIterator support stable (Matteo Collina) [#26989](https://github.com/nodejs/node/pull/26989)

### Commits

- [[`f1a5a36961`](https://github.com/nodejs/node/commit/f1a5a36961)] - **build**: update Windows icon to Feb 2016 rebrand (Mike MacCana) [#28524](https://github.com/nodejs/node/pull/28524)
- [[`63de2ade85`](https://github.com/nodejs/node/commit/63de2ade85)] - **(SEMVER-MINOR)** **crypto**: add support for chacha20-poly1305 for AEAD (chux0519) [#24081](https://github.com/nodejs/node/pull/24081)
- [[`4f0f12c3d6`](https://github.com/nodejs/node/commit/4f0f12c3d6)] - **crypto**: fix rsa key gen with non-default exponent (Sam Roberts) [#27092](https://github.com/nodejs/node/pull/27092)
- [[`7735824d2c`](https://github.com/nodejs/node/commit/7735824d2c)] - **(SEMVER-MINOR)** **crypto**: increase maxmem range from 32 to 53 bits (Tobias Nießen) [#28799](https://github.com/nodejs/node/pull/28799)
- [[`e53dbba6bc`](https://github.com/nodejs/node/commit/e53dbba6bc)] - **deps**: update npm to 6.11.3 (claudiahdz) [#29430](https://github.com/nodejs/node/pull/29430)
- [[`55cd01c5c3`](https://github.com/nodejs/node/commit/55cd01c5c3)] - **(SEMVER-MINOR)** **deps**: update npm to 6.10.3 (isaacs) [#29023](https://github.com/nodejs/node/pull/29023)
- [[`e2291cf805`](https://github.com/nodejs/node/commit/e2291cf805)] - **deps**: upgrade npm to 6.10.2 (isaacs) [#28853](https://github.com/nodejs/node/pull/28853)
- [[`03b69660f9`](https://github.com/nodejs/node/commit/03b69660f9)] - **deps**: upgrade npm to 6.10.0 (isaacs) [#28525](https://github.com/nodejs/node/pull/28525)
- [[`333963ef73`](https://github.com/nodejs/node/commit/333963ef73)] - **deps**: dlloads node static linked executable (Luca Lindhorst) [#28045](https://github.com/nodejs/node/pull/28045)
- [[`7202792ad3`](https://github.com/nodejs/node/commit/7202792ad3)] - **deps**: update archs files for OpenSSL-1.1.1d (Sam Roberts) [#29921](https://github.com/nodejs/node/pull/29921)
- [[`9c393f1d02`](https://github.com/nodejs/node/commit/9c393f1d02)] - **deps**: upgrade openssl sources to 1.1.1d (Sam Roberts) [#29921](https://github.com/nodejs/node/pull/29921)
- [[`7f48519413`](https://github.com/nodejs/node/commit/7f48519413)] - **deps**: do not link against librt (Sam Roberts) [#29729](https://github.com/nodejs/node/pull/29729)
- [[`fcc22d31a0`](https://github.com/nodejs/node/commit/fcc22d31a0)] - **(SEMVER-MINOR)** **dns**: make dns.promises enumerable (cjihrig) [#26592](https://github.com/nodejs/node/pull/26592)
- [[`fa27aac5fb`](https://github.com/nodejs/node/commit/fa27aac5fb)] - **(SEMVER-MINOR)** **dns**: remove dns.promises experimental warning (cjihrig) [#26592](https://github.com/nodejs/node/pull/26592)
- [[`90fb146933`](https://github.com/nodejs/node/commit/90fb146933)] - **(SEMVER-MINOR)** **doc**: move dns.promises to stable status (cjihrig) [#26592](https://github.com/nodejs/node/pull/26592)
- [[`65e68d1f4f`](https://github.com/nodejs/node/commit/65e68d1f4f)] - **doc**: add documentation for stream readableFlowing (Chetan Karande) [#29506](https://github.com/nodejs/node/pull/29506)
- [[`c285e694e2`](https://github.com/nodejs/node/commit/c285e694e2)] - **doc**: fix the links tls default version sections (Chetan Karande) [#28827](https://github.com/nodejs/node/pull/28827)
- [[`cef5010135`](https://github.com/nodejs/node/commit/cef5010135)] - **doc**: describe tls.DEFAULT_MIN_VERSION/\_MAX_VERSION (Chetan Karande) [#28827](https://github.com/nodejs/node/pull/28827)
- [[`15c2eb0e58`](https://github.com/nodejs/node/commit/15c2eb0e58)] - **doc**: update N-API version matrix (Gabriel Schulhof) [#29461](https://github.com/nodejs/node/pull/29461)
- [[`a3eda2896d`](https://github.com/nodejs/node/commit/a3eda2896d)] - **doc**: fixup changelog for v10.16.3 (Andrew Hughes) [#29159](https://github.com/nodejs/node/pull/29159)
- [[`56a834a53f`](https://github.com/nodejs/node/commit/56a834a53f)] - **doc,test**: clarify that Http2Stream is destroyed after data is read (Alba Mendez) [#27891](https://github.com/nodejs/node/pull/27891)
- [[`85ce8ef19a`](https://github.com/nodejs/node/commit/85ce8ef19a)] - **(SEMVER-MINOR)** **fs**: remove experimental warning for fs.promises (Anna Henningsen) [#26581](https://github.com/nodejs/node/pull/26581)
- [[`ccf2823f83`](https://github.com/nodejs/node/commit/ccf2823f83)] - **(SEMVER-MINOR)** **http**: makes response.writeHead return the response (Mark S. Everitt) [#25974](https://github.com/nodejs/node/pull/25974)
- [[`66387cd45e`](https://github.com/nodejs/node/commit/66387cd45e)] - **http2**: send out pending data earlier (Anna Henningsen) [#29398](https://github.com/nodejs/node/pull/29398)
- [[`925849650b`](https://github.com/nodejs/node/commit/925849650b)] - **(SEMVER-MINOR)** **http2**: makes response.writeHead return the response (Mark S. Everitt) [#25974](https://github.com/nodejs/node/pull/25974)
- [[`69b0212df3`](https://github.com/nodejs/node/commit/69b0212df3)] - **http2**: do not start reading after write if new write is on wire (Anna Henningsen) [#29399](https://github.com/nodejs/node/pull/29399)
- [[`36a0e9a063`](https://github.com/nodejs/node/commit/36a0e9a063)] - **http2**: do not crash on stream listener removal w/ destroyed session (Anna Henningsen) [#29459](https://github.com/nodejs/node/pull/29459)
- [[`c74c6a5ccf`](https://github.com/nodejs/node/commit/c74c6a5ccf)] - **n-api**: mark version 5 N-APIs as stable (Gabriel Schulhof) [#29401](https://github.com/nodejs/node/pull/29401)
- [[`f8622762e3`](https://github.com/nodejs/node/commit/f8622762e3)] - **(SEMVER-MINOR)** **n-api**: make func argument of napi_create_threadsafe_function optional (legendecas) [#27791](https://github.com/nodejs/node/pull/27791)
- [[`4f41e4f471`](https://github.com/nodejs/node/commit/4f41e4f471)] - **(SEMVER-MINOR)** **n-api**: implement date object (Jarrod Connolly) [#25917](https://github.com/nodejs/node/pull/25917)
- [[`69bf5b7944`](https://github.com/nodejs/node/commit/69bf5b7944)] - **net**: treat ENOTCONN at shutdown as success (Anna Henningsen) [#29912](https://github.com/nodejs/node/pull/29912)
- [[`d6c998a478`](https://github.com/nodejs/node/commit/d6c998a478)] - **process**: use public readableFlowing property (Chetan Karande) [#29502](https://github.com/nodejs/node/pull/29502)
- [[`b43d7e8f42`](https://github.com/nodejs/node/commit/b43d7e8f42)] - **(SEMVER-MINOR)** **process**: add --unhandled-rejections flag (Ruben Bridgewater) [#26599](https://github.com/nodejs/node/pull/26599)
- [[`79f3844fb0`](https://github.com/nodejs/node/commit/79f3844fb0)] - **(SEMVER-MINOR)** **readline**: make Symbol.asyncIterator support stable (Matteo Collina) [#26989](https://github.com/nodejs/node/pull/26989)
- [[`18b140ae75`](https://github.com/nodejs/node/commit/18b140ae75)] - **src**: use maybe version v8::Function::Call (Ouyang Yadong) [#23826](https://github.com/nodejs/node/pull/23826)
- [[`1bb5102999`](https://github.com/nodejs/node/commit/1bb5102999)] - **src**: use more explicit return type in Sign::SignFinal() (Anna Henningsen) [#23779](https://github.com/nodejs/node/pull/23779)
- [[`859d47593e`](https://github.com/nodejs/node/commit/859d47593e)] - **src**: reduce platform worker barrier lifetime (Ali Ijaz Sheikh) [#23419](https://github.com/nodejs/node/pull/23419)
- [[`00831f0293`](https://github.com/nodejs/node/commit/00831f0293)] - **(SEMVER-MINOR)** **stream**: make Symbol.asyncIterator support stable (Matteo Collina) [#26989](https://github.com/nodejs/node/pull/26989)
- [[`ddb5152e9b`](https://github.com/nodejs/node/commit/ddb5152e9b)] - **(SEMVER-MINOR)** **stream**: implement Readable.from async iterator utility (Guy Bedford) [#27660](https://github.com/nodejs/node/pull/27660)
- [[`13d8549abd`](https://github.com/nodejs/node/commit/13d8549abd)] - **test**: well-defined DH groups now verify clean (Sam Roberts) [#29550](https://github.com/nodejs/node/pull/29550)
- [[`f78ecc3f93`](https://github.com/nodejs/node/commit/f78ecc3f93)] - **test**: fix race in test-http2-origin (Alba Mendez) [#28903](https://github.com/nodejs/node/pull/28903)
- [[`2afbb3efab`](https://github.com/nodejs/node/commit/2afbb3efab)] - **test,win**: cleanup exec-timeout processes (João Reis) [#28723](https://github.com/nodejs/node/pull/28723)
- [[`fe58bca878`](https://github.com/nodejs/node/commit/fe58bca878)] - **tls**: group chunks into TLS segments (Alba Mendez) [#27861](https://github.com/nodejs/node/pull/27861)
- [[`2eae030a4b`](https://github.com/nodejs/node/commit/2eae030a4b)] - **(SEMVER-MINOR)** **worker**: add missing return value in case of fatal exceptions (Ruben Bridgewater) [#29036](https://github.com/nodejs/node/pull/29036)
- [[`e8c90bf4d1`](https://github.com/nodejs/node/commit/e8c90bf4d1)] - **zlib**: do not coalesce multiple `.flush()` calls (Anna Henningsen) [#28520](https://github.com/nodejs/node/pull/28520)

Windows 32-bit Installer: https://nodejs.org/dist/v10.17.0/node-v10.17.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v10.17.0/node-v10.17.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v10.17.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v10.17.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v10.17.0/node-v10.17.0.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v10.17.0/node-v10.17.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v10.17.0/node-v10.17.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v10.17.0/node-v10.17.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v10.17.0/node-v10.17.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v10.17.0/node-v10.17.0-aix-ppc64.tar.gz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v10.17.0/node-v10.17.0-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v10.17.0/node-v10.17.0-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v10.17.0/node-v10.17.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v10.17.0/node-v10.17.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v10.17.0/node-v10.17.0.tar.gz \
Other release files: https://nodejs.org/dist/v10.17.0/ \
Documentation: https://nodejs.org/docs/v10.17.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

0e6237b642ce93704395a61468990fb1156df33c0b60a83bfebb42aaeee272f5  node-v10.17.0-aix-ppc64.tar.gz
9b96140ad74b217f216c83ddf50d1f70a4296576f6edbbbfb65d0f478015d9df  node-v10.17.0-darwin-x64.tar.gz
540a8f636eabe470be454f63791165c8118bd7b5534fba0dd588d97c3ef0a0f2  node-v10.17.0-darwin-x64.tar.xz
d273faf81cdc0c5cc9457a095bc366f755427c9ffa67a438aa7169344d8adfbc  node-v10.17.0-headers.tar.gz
40f79432091ae09f4657ef10bb0c0de99efcec4dd48245970ff1afd63b39dfba  node-v10.17.0-headers.tar.xz
fca7862a435c48d634fd74464057edef0e6ed854678c4b1fee3f21f126f2d7c7  node-v10.17.0-linux-arm64.tar.gz
3ab8ee2b5c9aa4d060c68667ddef70cc2960e12bcfe4a0f2de7ebc0f008bf13d  node-v10.17.0-linux-arm64.tar.xz
bc47d0ec1b70525558508962453456fda2b0c21013e2a6996479efbb8b96c518  node-v10.17.0-linux-armv6l.tar.gz
4f8dd4d85ed0de8cce0d16f4444e48ec456caf89c242cc75d6e3d35ec15da479  node-v10.17.0-linux-armv6l.tar.xz
6e7316b9a3e48c9cadfaa09adb89ee31ca00d803cdf7dd63687f4a6bf87070d4  node-v10.17.0-linux-armv7l.tar.gz
6315d336e1ae3ab268ca8f83b654ce48cc5d3cf8ff4f2960268890b1e87c6bfc  node-v10.17.0-linux-armv7l.tar.xz
02d9af2a539c597980b55c0d04e949405213f1f1f4ecf62799c6bdde3d636a01  node-v10.17.0-linux-ppc64le.tar.gz
507d13d310d7d7c1e286cce24226dda0b48b33384233f148a17049b8663a7fe9  node-v10.17.0-linux-ppc64le.tar.xz
f98ff6a7949dc67f1fe5880a2407b57f246b3ac3c3a3c76a4c8a2f065e6d314f  node-v10.17.0-linux-s390x.tar.gz
76db3a9b7d2afda32b4267004e4c8f2530e6e95572e68a31ce639e263db9bdc4  node-v10.17.0-linux-s390x.tar.xz
417bdc5402f6510fe1a5a898a9cdf1d67bd0202b5f014051c382f05358999534  node-v10.17.0-linux-x64.tar.gz
2b49cd296f969ef0ffb7922719ffa6542bedb89d6c959a47c023d11ce222f5d6  node-v10.17.0-linux-x64.tar.xz
493f4110d9427c4e036719138edffff0e15f0377bf006ca21bac3768211f5e76  node-v10.17.0.pkg
653a2cdd950f839b832157f66d3e47a7630c2a95ed56709cc1933fc62d256bac  node-v10.17.0-sunos-x64.tar.gz
c6b410555a8a3a21b7c05c6e15b26bf8515591c915e19a4723e4c87bcf1de9bc  node-v10.17.0-sunos-x64.tar.xz
5204249d135176b547737d3eed2ca8a9d7f731fef6e545f741129cfa21f90573  node-v10.17.0.tar.gz
412667d76bd5273c07cb69c215998109fd5bb35c874654f93e6a0132d666c58e  node-v10.17.0.tar.xz
0889501b8933702c014bf44726820d79ee34e2c7deb7b4174248f6a6b24c07ee  node-v10.17.0-win-x64.7z
e84a1f3685219811bb4662eb3e3b55abd0c764c24cd2b224ba31b3f9f162baf6  node-v10.17.0-win-x64.zip
d77b3bf851b843c4acebada9af72bbe3c805b2873a8b01c905401f9a8db2c477  node-v10.17.0-win-x86.7z
1956af513eba539614f5428f4fccdbb438fb33593f789bc20033b6a88c005cf1  node-v10.17.0-win-x86.zip
259a6a29b6f39685e0752e5f5153ebc6f4146aefd27ff18e8c1605d0826597fe  node-v10.17.0-x64.msi
2281ceace216bbce7fa0a5ece8eb8650b961bbf03019400b3df5481ba3cd20b1  node-v10.17.0-x86.msi
07e22859fe23b8c4a8956dfed31e7a10d27894cee955a2a1589523907079f42f  win-x64/node.exe
18da4bd0b611038dac2fb4b6f18e7b35fa2e30857017fc36e5053e7daa8304d5  win-x64/node.lib
4a45b33504942385585976caa3eb57c399186078107285f0fa9e8b3a3cee10e9  win-x64/node_pdb.7z
64570ca71e0dc844b80c234e4eaa6cda0bba4fffd92bb02e7447c023c41b7c7e  win-x64/node_pdb.zip
035e0231a7b037b20803d0cf077d631518478fe76dbcf4d5e9ca6a33e5fe27d9  win-x86/node.exe
fea7d0c5a94fc834a78b44b7da206f0e60ef11dd20d84dc0d49d57ee77e20e16  win-x86/node.lib
62248995e9163c10691f421e573ba1d08aba52abaa9db13236d4b5c294b7f16a  win-x86/node_pdb.7z
99a755f35449f56e5dc435dccb438128d0173a16633cd53373d0d15dc232bdbc  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQEzBAEBCAAdFiEETtd49TnjY0x3nIfG1wYoSKGrAFwFAl2vVq0ACgkQ1wYoSKGr
AFyJ1gf8D7LwqIpXvge8vuF3SrpiVo2QYV6DgrnKSrFdZck1PglNG8gyG8vLyZ9y
vKibHQt9wcOLVV6NG5Cum3vkom/Xk8LoeS8KLfmfF27QG38zV0fjimiIfvvzgaKY
fprl4hxjzXT5MrvlIfLGTbPTunLPjC+k8xPCDJGn4KGLqtgfcb155NpvnNuuf8v/
Bt7ILmTo8r0wVELaAJKDvT7RDS2Dx+c2TAvGgQat1oxsuDSrIz+vSccDXFlMBplA
E24UE3SERndziC/oCV6jNfd/ZWb2Pu4SZoOkNMXDcVY8TR9Zz2jV6vSNNLUm/bGu
zbVtrzOjx0iYaQolQswPh3fE8+zbhw==
=uaGL
-----END PGP SIGNATURE-----

```
