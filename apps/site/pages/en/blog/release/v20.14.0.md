---
date: '2024-05-28T16:50:55.754Z'
category: release
title: Node v20.14.0 (LTS)
layout: blog-post
author: <PERSON>
---

## 2024-05-28, Version 20.14.0 'Iron' (LTS), @marco-ippolito

### Notable Changes

- \[[`28d2baa17c`](https://github.com/nodejs/node/commit/28d2baa17c)] - **src,permission**: throw async errors on async APIs (<PERSON>) [#52730](https://github.com/nodejs/node/pull/52730)
- \[[`77e2bf029a`](https://github.com/nodejs/node/commit/77e2bf029a)] - **(SEMVER-MINOR)** **test_runner**: support forced exit (<PERSON>) [#52038](https://github.com/nodejs/node/pull/52038)

### Commits

- \[[`e3ad05d8b0`](https://github.com/nodejs/node/commit/e3ad05d8b0)] - **deps**: V8: cherry-pick 500de8bd371b (<PERSON>) [#52676](https://github.com/nodejs/node/pull/52676)
- \[[`053282e661`](https://github.com/nodejs/node/commit/053282e661)] - **deps**: V8: backport c4be0a97f981 (<PERSON>) [#52183](https://github.com/nodejs/node/pull/52183)
- \[[`200dadb879`](https://github.com/nodejs/node/commit/200dadb879)] - **deps**: V8: cherry-pick f8d5e576b814 (Richard Lau) [#52183](https://github.com/nodejs/node/pull/52183)
- \[[`f5cd125e02`](https://github.com/nodejs/node/commit/f5cd125e02)] - **deps**: update googletest to fa6de7f (Node.js GitHub Bot) [#52949](https://github.com/nodejs/node/pull/52949)
- \[[`bbbfd7f4e1`](https://github.com/nodejs/node/commit/bbbfd7f4e1)] - **deps**: update corepack to 0.28.1 (Node.js GitHub Bot) [#52946](https://github.com/nodejs/node/pull/52946)
- \[[`7ba30a57a6`](https://github.com/nodejs/node/commit/7ba30a57a6)] - **deps**: update simdutf to 5.2.8 (Node.js GitHub Bot) [#52727](https://github.com/nodejs/node/pull/52727)
- \[[`b21a480a28`](https://github.com/nodejs/node/commit/b21a480a28)] - **deps**: update simdutf to 5.2.6 (Node.js GitHub Bot) [#52727](https://github.com/nodejs/node/pull/52727)
- \[[`6cfad60d97`](https://github.com/nodejs/node/commit/6cfad60d97)] - **deps**: update googletest to 2d16ed0 (Node.js GitHub Bot) [#51657](https://github.com/nodejs/node/pull/51657)
- \[[`34708d1429`](https://github.com/nodejs/node/commit/34708d1429)] - **deps**: update googletest to d83fee1 (Node.js GitHub Bot) [#51657](https://github.com/nodejs/node/pull/51657)
- \[[`c1d3e558e8`](https://github.com/nodejs/node/commit/c1d3e558e8)] - **deps**: update googletest to 5a37b51 (Node.js GitHub Bot) [#51657](https://github.com/nodejs/node/pull/51657)
- \[[`69959d0fca`](https://github.com/nodejs/node/commit/69959d0fca)] - **deps**: update googletest to 5197b1a (Node.js GitHub Bot) [#51657](https://github.com/nodejs/node/pull/51657)
- \[[`c8305f6057`](https://github.com/nodejs/node/commit/c8305f6057)] - **deps**: update googletest to eff443c (Node.js GitHub Bot) [#51657](https://github.com/nodejs/node/pull/51657)
- \[[`760b788704`](https://github.com/nodejs/node/commit/760b788704)] - **deps**: update googletest to c231e6f (Node.js GitHub Bot) [#51657](https://github.com/nodejs/node/pull/51657)
- \[[`301541cc8f`](https://github.com/nodejs/node/commit/301541cc8f)] - **deps**: update googletest to e4fdb87 (Node.js GitHub Bot) [#51657](https://github.com/nodejs/node/pull/51657)
- \[[`981d57e401`](https://github.com/nodejs/node/commit/981d57e401)] - **deps**: update googletest to 5df0241 (Node.js GitHub Bot) [#51657](https://github.com/nodejs/node/pull/51657)
- \[[`a1817f534d`](https://github.com/nodejs/node/commit/a1817f534d)] - **deps**: update googletest to b75ecf1 (Node.js GitHub Bot) [#51657](https://github.com/nodejs/node/pull/51657)
- \[[`42070ca189`](https://github.com/nodejs/node/commit/42070ca189)] - **deps**: update googletest to 4565741 (Node.js GitHub Bot) [#51657](https://github.com/nodejs/node/pull/51657)
- \[[`edc3e5d056`](https://github.com/nodejs/node/commit/edc3e5d056)] - **deps**: update uvwasi to 0.0.21 (Node.js GitHub Bot) [#52863](https://github.com/nodejs/node/pull/52863)
- \[[`26b1231ffb`](https://github.com/nodejs/node/commit/26b1231ffb)] - **deps**: upgrade npm to 10.7.0 (npm team) [#52767](https://github.com/nodejs/node/pull/52767)
- \[[`e6d9fbece2`](https://github.com/nodejs/node/commit/e6d9fbece2)] - **doc**: update process.versions properties (ishabi) [#52736](https://github.com/nodejs/node/pull/52736)
- \[[`8c1f837c0a`](https://github.com/nodejs/node/commit/8c1f837c0a)] - **doc**: remove mold use on mac for speeding up build (Cong Zhang) [#52252](https://github.com/nodejs/node/pull/52252)
- \[[`d9c5114694`](https://github.com/nodejs/node/commit/d9c5114694)] - **doc**: fix grammatical mistake (codershiba) [#52808](https://github.com/nodejs/node/pull/52808)
- \[[`b350f435b7`](https://github.com/nodejs/node/commit/b350f435b7)] - **meta**: add mailmap entry for legendecas (Chengzhong Wu) [#52795](https://github.com/nodejs/node/pull/52795)
- \[[`61f9f12eff`](https://github.com/nodejs/node/commit/61f9f12eff)] - **meta**: bump actions/checkout from 4.1.1 to 4.1.4 (dependabot\[bot]) [#52787](https://github.com/nodejs/node/pull/52787)
- \[[`ac563667d6`](https://github.com/nodejs/node/commit/ac563667d6)] - **meta**: bump github/codeql-action from 3.24.9 to 3.25.3 (dependabot\[bot]) [#52786](https://github.com/nodejs/node/pull/52786)
- \[[`70611d7924`](https://github.com/nodejs/node/commit/70611d7924)] - **meta**: bump actions/upload-artifact from 4.3.1 to 4.3.3 (dependabot\[bot]) [#52785](https://github.com/nodejs/node/pull/52785)
- \[[`30482ea273`](https://github.com/nodejs/node/commit/30482ea273)] - **meta**: bump actions/download-artifact from 4.1.4 to 4.1.7 (dependabot\[bot]) [#52784](https://github.com/nodejs/node/pull/52784)
- \[[`d1607cdebb`](https://github.com/nodejs/node/commit/d1607cdebb)] - **meta**: bump codecov/codecov-action from 4.1.1 to 4.3.1 (dependabot\[bot]) [#52783](https://github.com/nodejs/node/pull/52783)
- \[[`21f1b6bfc3`](https://github.com/nodejs/node/commit/21f1b6bfc3)] - **meta**: bump step-security/harden-runner from 2.7.0 to 2.7.1 (dependabot\[bot]) [#52782](https://github.com/nodejs/node/pull/52782)
- \[[`0c6019a222`](https://github.com/nodejs/node/commit/0c6019a222)] - **meta**: standardize regex (Aviv Keller) [#52693](https://github.com/nodejs/node/pull/52693)
- \[[`28d2baa17c`](https://github.com/nodejs/node/commit/28d2baa17c)] - **src,permission**: throw async errors on async APIs (Rafael Gonzaga) [#52730](https://github.com/nodejs/node/pull/52730)
- \[[`cffd2cc0c9`](https://github.com/nodejs/node/commit/cffd2cc0c9)] - _**Revert**_ "**stream**: revert fix cloned webstreams not being unref'd" (Marco Ippolito) [#53144](https://github.com/nodejs/node/pull/53144)
- \[[`3dd96f1fab`](https://github.com/nodejs/node/commit/3dd96f1fab)] - **stream**: implement TransformStream cleanup using "transformer.cancel" (Debadree Chatterjee) [#50126](https://github.com/nodejs/node/pull/50126)
- \[[`8e7e778e01`](https://github.com/nodejs/node/commit/8e7e778e01)] - **test**: skip v8-updates/test-linux-perf (Michaël Zasso) [#49639](https://github.com/nodejs/node/pull/49639)
- \[[`f8e18869e9`](https://github.com/nodejs/node/commit/f8e18869e9)] - **test**: replace always-opt flag with alway-turbofan (Michaël Zasso) [#50115](https://github.com/nodejs/node/pull/50115)
- \[[`a501860d63`](https://github.com/nodejs/node/commit/a501860d63)] - **test_runner**: don't await the same promise for each test (Colin Ihrig) [#52185](https://github.com/nodejs/node/pull/52185)
- \[[`e2ae4367f4`](https://github.com/nodejs/node/commit/e2ae4367f4)] - **test_runner**: run top level tests in a microtask (Colin Ihrig) [#52092](https://github.com/nodejs/node/pull/52092)
- \[[`77e2bf029a`](https://github.com/nodejs/node/commit/77e2bf029a)] - **(SEMVER-MINOR)** **test_runner**: support forced exit (Colin Ihrig) [#52038](https://github.com/nodejs/node/pull/52038)
- \[[`b7bc63565e`](https://github.com/nodejs/node/commit/b7bc63565e)] - **test_runner**: ignore todo flag when running suites (Colin Ihrig) [#52117](https://github.com/nodejs/node/pull/52117)
- \[[`be587e3ae3`](https://github.com/nodejs/node/commit/be587e3ae3)] - **test_runner**: use paths for test locations (Colin Ihrig) [#52010](https://github.com/nodejs/node/pull/52010)
- \[[`743281ab25`](https://github.com/nodejs/node/commit/743281ab25)] - **test_runner**: support source mapped test locations (Colin Ihrig) [#52010](https://github.com/nodejs/node/pull/52010)
- \[[`4051316d95`](https://github.com/nodejs/node/commit/4051316d95)] - **tools**: update lint-md-dependencies to rollup\@4.17.0 (Node.js GitHub Bot) [#52729](https://github.com/nodejs/node/pull/52729)

Windows 32-bit Installer: https://nodejs.org/dist/v20.14.0/node-v20.14.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v20.14.0/node-v20.14.0-x64.msi \
Windows ARM 64-bit Installer: https://nodejs.org/dist/v20.14.0/node-v20.14.0-arm64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v20.14.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v20.14.0/win-x64/node.exe \
Windows ARM 64-bit Binary: https://nodejs.org/dist/v20.14.0/win-arm64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v20.14.0/node-v20.14.0.pkg \
macOS Apple Silicon 64-bit Binary: https://nodejs.org/dist/v20.14.0/node-v20.14.0-darwin-arm64.tar.gz \
macOS Intel 64-bit Binary: https://nodejs.org/dist/v20.14.0/node-v20.14.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v20.14.0/node-v20.14.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v20.14.0/node-v20.14.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v20.14.0/node-v20.14.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v20.14.0/node-v20.14.0-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v20.14.0/node-v20.14.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v20.14.0/node-v20.14.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v20.14.0/node-v20.14.0.tar.gz \
Other release files: https://nodejs.org/dist/v20.14.0/ \
Documentation: https://nodejs.org/docs/v20.14.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

06ac9b1f35de7b05dc0aea58b99804edf608ee500b2e9b303d1a6044eb3eeab2  node-v20.14.0-aix-ppc64.tar.gz
7aa32a400a8d10bac35dcc22ff09ca2eccf2842dd3a762f94c77db096d6a8220  node-v20.14.0-arm64.msi
d2148d79e9ff04d2982d00faeae942ceba488ca327a91065e528235167b9e9d6  node-v20.14.0-darwin-arm64.tar.gz
63ee914bdbb1552213ec4e21ea83685e11d969d50fc7801d6db981089cd607fe  node-v20.14.0-darwin-arm64.tar.xz
1dcc18a199cb5f46d43ed1c3c61b87a247d1a1a11dd6b32a36a9c46ac1088f86  node-v20.14.0-darwin-x64.tar.gz
78e05a047b61f312db2de5e0127aeeb1274753c7f215f8892f3e5f76a629a022  node-v20.14.0-darwin-x64.tar.xz
289ecd13b5d8edfff527b258fd752f049e1c85351e6833de00daf088b17ef3b2  node-v20.14.0-headers.tar.gz
7c94f0f0d0b2a3a53ef9515aa24510dbfd6bc71373723edda309b85ecb8eaf12  node-v20.14.0-headers.tar.xz
d63e83fca4f81801396620c46a42892a2ef26e21a4508f68de373e61a12bd9c5  node-v20.14.0-linux-arm64.tar.gz
8fb7012589850390ec658f58aaa11656031f64a7a8efb0b37607ca140cefe3a9  node-v20.14.0-linux-arm64.tar.xz
af45ea0d09e55a4f05c0190636532bdf9f70b2eaf0a1c4d7594207cf21284df0  node-v20.14.0-linux-armv7l.tar.gz
73d7808e7f8d4ea0370c285cee9e63febdd07604ec400e3fc51213d4d9e9beec  node-v20.14.0-linux-armv7l.tar.xz
2405a23249ddb4d2495f212255f5f5f11892fac77f9835c491b9ffaee1404078  node-v20.14.0-linux-ppc64le.tar.gz
2476aa037046e707c1fd755be624f0144438c3ce02fa0a4d0bb8b29fae84b5af  node-v20.14.0-linux-ppc64le.tar.xz
74668f3e337980acf1579e31f6f9490ec28d98603bb2ff17390c115606af7806  node-v20.14.0-linux-s390x.tar.gz
fafd8e3ac753f7e1468c9390f9f40dd9227172b9cac0ae3902e6df91861519da  node-v20.14.0-linux-s390x.tar.xz
5b9bf40cfc7c21de14a1b4c367650e3c96eb101156bf9368ffc2f947414b6581  node-v20.14.0-linux-x64.tar.gz
fedf8fa73b6f51c4ffcc5da8f86cd1ed381bc9dceae0829832c7d683a78b8e36  node-v20.14.0-linux-x64.tar.xz
257caf3ae34ddf14aebe2a9cecba6994842548af94565af5369d3a834637273e  node-v20.14.0.pkg
f01109d3102754ac360fcf25aa588f3bef5c090a8eed3fb1d0be194149c46cf2  node-v20.14.0.tar.gz
08655028f0d8436e88163f9186044d635d3f36a85ee528f36bd05b6c5e46c1bb  node-v20.14.0.tar.xz
81530d6a3f62f2b2dc758ecda6510656b20d64fcdd24ef5befd08a94bf5c2408  node-v20.14.0-win-arm64.7z
4169ea1d91bbcdd1483cd6569f5c7402f6954ea01364204d7c3968d8a80046c8  node-v20.14.0-win-arm64.zip
2f287555ee39462995b8a728c41bba1ad5afd8d27cfeaed0946d5ac0df1b9b7a  node-v20.14.0-win-x64.7z
04cc745e572ff53a6b9ce5b573e4a18303e32351e60c278a93b84466b60d532f  node-v20.14.0-win-x64.zip
e808e46b4f4003b1080a90c29e1115deea20a94bac4d2a619c9413602915c77f  node-v20.14.0-win-x86.7z
370f08c5b80586a2b5123b6c6e4d92915988868de5f940280679e1faa54d9b0f  node-v20.14.0-win-x86.zip
4235f05b99ae5dabadb5c10c124a0f7f7d4223e52df0857e4c4462b13f19c40e  node-v20.14.0-x64.msi
148fffe0e2b68eb9ba5105d596b5302dda436fc1fca5e0e1bbd2994e729eca4a  node-v20.14.0-x86.msi
a6ec02119098cf92592539e06289953c4365be20ab15d4ad264669f931000b0c  win-arm64/node.exe
6855c9b01fc23139ccf9a2c4e079e629d1935df57f6d01075952d29e017b3642  win-arm64/node.lib
0eb75fe22b0e55302e3847a63c9e808d1d5f1ffc36c24cef1fbc69c0a3542208  win-arm64/node_pdb.7z
1967bc8ae74261492b355026a542c6174defaa7bd77302c471ac74b63662a1e9  win-arm64/node_pdb.zip
8f45741ec6ba07be8d199c0cebc838a58c0430c9228dbe50f8ac5d4859e58bae  win-x64/node.exe
bd5890fac4571d1cdfaca31d74ae791feef1edad3b70421969de2aa83f330797  win-x64/node.lib
2b739175308eeb31fb475b0f0f38e76453e69179f368c35aa7643ef5f03ae570  win-x64/node_pdb.7z
8e64314e79dfe3ef8c9e3f8fb9708645af3b4f5abe36718ba8b1141fc934b9cb  win-x64/node_pdb.zip
096e3d8e0e28b1683eab761fd5a43df978f93fa7bf0a42dfbda915340716f242  win-x86/node.exe
be02221a3f8c8e9fc2946bfec6d1b43da8798281857507730b3fee0a154af851  win-x86/node.lib
01e3b6c808cbd4051b318d26a565bea7710a511b60c7ae973c590c2615bc56d4  win-x86/node_pdb.7z
7601c510d956858c36f1c83697ac055c7d4b75876fb3519b68f69f07f41ca12f  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEzGj1oxBv9EgyLkjtJ/XjjVsKIV8FAmZWCokACgkQJ/XjjVsK
IV9BgxAAiR+7wu+SvNDhEyNRIHaviXeIwH04RLISm/72jvi22LbGfKT1/4F5GvcY
OvMOHQAgrx0rbF5i6CC2e6SmWWNsD+oFT0ksxRNGgAwl5aCyzjPSn9yfLEJzHJPa
+XZ3KOQ+Yuq9qPeIUVuiJWov/Yq6hU+XjVrmwpE4FILhR+pPwOdJirjY0eE+pGuE
RSKa1KdYJFMrlN40Wzhjqabj+YjT9YXsnlt/AgC2OfY/OwfKLTCkos+ZD8E62Xn7
GvRSfe7YLEue0YgN5XLxdCiOnxhwfco73pjOiNEO/ZGOcSZ3StNKWYelEUUq+GGx
crE2CuoXps6F3aPvHYmqur4arBuIKTOB/jO76ApvIjdWdGgRVP+ORdtwSItglwdy
9XldjrQzCvpJQlrArboeQBnXK972EgMEqe0iD6rwaIuNtcoupOa0HFhMb8x/uT9R
Y3vuAomwbM042rTC/wfF4u2/vZz2/qna/wDQ06l3g9oP9D/050REQri+4rembpFy
Q02IpehDtfD7NXD+4wfeeRB+ZvlNFWrCIy9Lrbt5UCGv638bXKOwTnOqnxpG2xIK
t17s3bw0aQV0wXeDDO2RCQu/izU+64jOGvl3UI8bKMsYYYMkD13mY4w3QrH/gY6u
/fKdLqmFzSUrxSeWp2Zy3Ymbuk5rkBUzP6Z3n0DRfSp8LHZehkk=
=5Spr
-----END PGP SIGNATURE-----
```
