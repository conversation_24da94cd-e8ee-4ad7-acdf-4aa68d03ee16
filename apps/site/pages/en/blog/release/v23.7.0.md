---
date: '2025-01-30T18:03:40.420Z'
category: release
title: Node v23.7.0 (Current)
layout: blog-post
author: <PERSON>
---

## 2025-01-30, Version 23.7.0 (Current), @aduh95

### Notable Changes

- \[[`36dd9ecc41`](https://github.com/nodejs/node/commit/36dd9ecc41)] - **crypto**: update root certificates to NSS 3.107 (Node.js GitHub Bot) [#56566](https://github.com/nodejs/node/pull/56566)
- \[[`9414d3cbf1`](https://github.com/nodejs/node/commit/9414d3cbf1)] - **(SEMVER-MINOR)** **fs**: allow `exclude` option in globs to accept glob patterns (Daeye<PERSON> Jeong) [#56489](https://github.com/nodejs/node/pull/56489)
- \[[`9c5c3b3115`](https://github.com/nodejs/node/commit/9c5c3b3115)] - **(SEMVER-MINOR)** **module**: add ERR_UNSUPPORTED_TYPESCRIPT_SYNTAX (Marco Ippolito) [#56610](https://github.com/nodejs/node/pull/56610)
- \[[`1e201fd5fd`](https://github.com/nodejs/node/commit/1e201fd5fd)] - **(SEMVER-MINOR)** **sqlite**: support TypedArray and DataView in `StatementSync` (Alex Yang) [#56385](https://github.com/nodejs/node/pull/56385)
- \[[`48c813fb67`](https://github.com/nodejs/node/commit/48c813fb67)] - **(SEMVER-MINOR)** **src**: add --disable-sigusr1 to prevent signal i/o thread (Rafael Gonzaga) [#56441](https://github.com/nodejs/node/pull/56441)
- \[[`cf16123785`](https://github.com/nodejs/node/commit/cf16123785)] - **(SEMVER-MINOR)** **src,worker**: add isInternalWorker (Carlos Espa) [#56469](https://github.com/nodejs/node/pull/56469)
- \[[`13bdd9c961`](https://github.com/nodejs/node/commit/13bdd9c961)] - **(SEMVER-MINOR)** **test_runner**: add TestContext.prototype.waitFor() (Colin Ihrig) [#56595](https://github.com/nodejs/node/pull/56595)
- \[[`00a1943858`](https://github.com/nodejs/node/commit/00a1943858)] - **(SEMVER-MINOR)** **test_runner**: add t.assert.fileSnapshot() (Colin Ihrig) [#56459](https://github.com/nodejs/node/pull/56459)
- \[[`3143566045`](https://github.com/nodejs/node/commit/3143566045)] - **(SEMVER-MINOR)** **test_runner**: add assert.register() API (Colin Ihrig) [#56434](https://github.com/nodejs/node/pull/56434)

### Commits

- \[[`334a3ac7c6`](https://github.com/nodejs/node/commit/334a3ac7c6)] - **assert**: make myers_diff function more performant (Giovanni Bucci) [#56303](https://github.com/nodejs/node/pull/56303)
- \[[`eb2bf460b7`](https://github.com/nodejs/node/commit/eb2bf460b7)] - **assert**: make partialDeepStrictEqual work with urls and File prototypes (Giovanni Bucci) [#56231](https://github.com/nodejs/node/pull/56231)
- \[[`d184453b90`](https://github.com/nodejs/node/commit/d184453b90)] - **assert**: show diff when doing partial comparisons (Giovanni Bucci) [#56211](https://github.com/nodejs/node/pull/56211)
- \[[`4aa1afd607`](https://github.com/nodejs/node/commit/4aa1afd607)] - **benchmark**: add validateStream to styleText bench (Rafael Gonzaga) [#56556](https://github.com/nodejs/node/pull/56556)
- \[[`8bbdb1203e`](https://github.com/nodejs/node/commit/8bbdb1203e)] - **child_process**: fix parsing messages with splitted length field (Maksim Gorkov) [#56106](https://github.com/nodejs/node/pull/56106)
- \[[`d83d89a08e`](https://github.com/nodejs/node/commit/d83d89a08e)] - **crypto**: add missing return value check (Michael Dawson) [#56615](https://github.com/nodejs/node/pull/56615)
- \[[`36dd9ecc41`](https://github.com/nodejs/node/commit/36dd9ecc41)] - **crypto**: update root certificates to NSS 3.107 (Node.js GitHub Bot) [#56566](https://github.com/nodejs/node/pull/56566)
- \[[`3915152c36`](https://github.com/nodejs/node/commit/3915152c36)] - **crypto**: fix checkPrime crash with large buffers (Santiago Gimeno) [#56559](https://github.com/nodejs/node/pull/56559)
- \[[`c8d1dcb063`](https://github.com/nodejs/node/commit/c8d1dcb063)] - **crypto**: fix warning of ignoring return value (Cheng) [#56527](https://github.com/nodejs/node/pull/56527)
- \[[`1994eaaf52`](https://github.com/nodejs/node/commit/1994eaaf52)] - **crypto**: make generatePrime/checkPrime interruptible (James M Snell) [#56460](https://github.com/nodejs/node/pull/56460)
- \[[`5f1ee05390`](https://github.com/nodejs/node/commit/5f1ee05390)] - **deps**: update corepack to 0.31.0 (Node.js GitHub Bot) [#56795](https://github.com/nodejs/node/pull/56795)
- \[[`9cfac712b8`](https://github.com/nodejs/node/commit/9cfac712b8)] - **deps**: move inspector_protocol to deps (Chengzhong Wu) [#56649](https://github.com/nodejs/node/pull/56649)
- \[[`b2ec816a31`](https://github.com/nodejs/node/commit/b2ec816a31)] - **deps**: macro ENODATA is deprecated in libc++ (Cheng) [#56698](https://github.com/nodejs/node/pull/56698)
- \[[`edd9361499`](https://github.com/nodejs/node/commit/edd9361499)] - **deps**: fixup some minor coverity warnings (James M Snell) [#56612](https://github.com/nodejs/node/pull/56612)
- \[[`9ffe3ad4b1`](https://github.com/nodejs/node/commit/9ffe3ad4b1)] - **deps**: update libuv to 1.50.0 (Node.js GitHub Bot) [#56616](https://github.com/nodejs/node/pull/56616)
- \[[`73ad3ca238`](https://github.com/nodejs/node/commit/73ad3ca238)] - **deps**: update amaro to 0.3.0 (Node.js GitHub Bot) [#56568](https://github.com/nodejs/node/pull/56568)
- \[[`0657f6270a`](https://github.com/nodejs/node/commit/0657f6270a)] - **deps**: update amaro to 0.2.2 (Node.js GitHub Bot) [#56568](https://github.com/nodejs/node/pull/56568)
- \[[`47fad8cbc0`](https://github.com/nodejs/node/commit/47fad8cbc0)] - **deps**: update simdutf to 6.0.3 (Node.js GitHub Bot) [#56567](https://github.com/nodejs/node/pull/56567)
- \[[`c9a211ae29`](https://github.com/nodejs/node/commit/c9a211ae29)] - **diagnostics_channel**: capture console messages (Stephen Belanger) [#56292](https://github.com/nodejs/node/pull/56292)
- \[[`cf5d2d6598`](https://github.com/nodejs/node/commit/cf5d2d6598)] - **doc**: move anatoli to emeritus (Michael Dawson) [#56592](https://github.com/nodejs/node/pull/56592)
- \[[`5dd08d10be`](https://github.com/nodejs/node/commit/5dd08d10be)] - **doc**: fix styles of the expandable TOC (Antoine du Hamel) [#56755](https://github.com/nodejs/node/pull/56755)
- \[[`09fb3adf80`](https://github.com/nodejs/node/commit/09fb3adf80)] - **doc**: add "Skip to content" button (Antoine du Hamel) [#56750](https://github.com/nodejs/node/pull/56750)
- \[[`ad012ca1f3`](https://github.com/nodejs/node/commit/ad012ca1f3)] - **doc**: improve accessibility of expandable lists (Antoine du Hamel) [#56749](https://github.com/nodejs/node/pull/56749)
- \[[`38acdb57eb`](https://github.com/nodejs/node/commit/38acdb57eb)] - **doc**: add note regarding commit message trailers (Dario Piotrowicz) [#56736](https://github.com/nodejs/node/pull/56736)
- \[[`f4a9b134c0`](https://github.com/nodejs/node/commit/f4a9b134c0)] - **doc**: fix typo in example code for util.styleText (Robin Mehner) [#56720](https://github.com/nodejs/node/pull/56720)
- \[[`8a61aaa734`](https://github.com/nodejs/node/commit/8a61aaa734)] - **doc**: fix inconsistencies in `WeakSet` and `WeakMap` comparison details (Shreyans Pathak) [#56683](https://github.com/nodejs/node/pull/56683)
- \[[`4ade128184`](https://github.com/nodejs/node/commit/4ade128184)] - **doc**: add RafaelGSS as latest sec release stewards (Rafael Gonzaga) [#56682](https://github.com/nodejs/node/pull/56682)
- \[[`e1e1200b79`](https://github.com/nodejs/node/commit/e1e1200b79)] - **doc**: clarify cjs/esm diff in `queueMicrotask()` vs `process.nextTick()` (Dario Piotrowicz) [#56659](https://github.com/nodejs/node/pull/56659)
- \[[`57a7b931fb`](https://github.com/nodejs/node/commit/57a7b931fb)] - **doc**: `WeakSet` and `WeakMap` comparison details (Shreyans Pathak) [#56648](https://github.com/nodejs/node/pull/56648)
- \[[`56b21489f4`](https://github.com/nodejs/node/commit/56b21489f4)] - **doc**: mention prepare --security (Rafael Gonzaga) [#56617](https://github.com/nodejs/node/pull/56617)
- \[[`67f39b597a`](https://github.com/nodejs/node/commit/67f39b597a)] - **doc**: tweak info on reposts in ambassador program (Michael Dawson) [#56589](https://github.com/nodejs/node/pull/56589)
- \[[`6381e0761d`](https://github.com/nodejs/node/commit/6381e0761d)] - **doc**: add type stripping to ambassadors program (Marco Ippolito) [#56598](https://github.com/nodejs/node/pull/56598)
- \[[`9bd438acd3`](https://github.com/nodejs/node/commit/9bd438acd3)] - **doc**: improve internal documentation on built-in snapshot (Joyee Cheung) [#56505](https://github.com/nodejs/node/pull/56505)
- \[[`f54118c84a`](https://github.com/nodejs/node/commit/f54118c84a)] - **doc**: correct customization hook types & clarify descriptions (Jacob Smith) [#56454](https://github.com/nodejs/node/pull/56454)
- \[[`6af5053153`](https://github.com/nodejs/node/commit/6af5053153)] - **doc**: document CLI way to open the nodejs/bluesky PR (Antoine du Hamel) [#56506](https://github.com/nodejs/node/pull/56506)
- \[[`4a77a9e1eb`](https://github.com/nodejs/node/commit/4a77a9e1eb)] - **doc**: add history info for Permission Model (Antoine du Hamel) [#56707](https://github.com/nodejs/node/pull/56707)
- \[[`097b8b4889`](https://github.com/nodejs/node/commit/097b8b4889)] - **doc**: add note for features using `InternalWorker` with permission model (Antoine du Hamel) [#56706](https://github.com/nodejs/node/pull/56706)
- \[[`f600466c73`](https://github.com/nodejs/node/commit/f600466c73)] - **doc**: add section about using npx with permission model (Rafael Gonzaga) [#56539](https://github.com/nodejs/node/pull/56539)
- \[[`c2d5a0c629`](https://github.com/nodejs/node/commit/c2d5a0c629)] - **doc**: update gcc-version for ubuntu-lts (Kunal Kumar) [#56553](https://github.com/nodejs/node/pull/56553)
- \[[`202af46793`](https://github.com/nodejs/node/commit/202af46793)] - **doc**: fix parentheses in options (Tobias Nießen) [#56563](https://github.com/nodejs/node/pull/56563)
- \[[`4e4b0c63d0`](https://github.com/nodejs/node/commit/4e4b0c63d0)] - **doc**: fix location of NO_COLOR in CLI docs (Colin Ihrig) [#56525](https://github.com/nodejs/node/pull/56525)
- \[[`92eeeb98a5`](https://github.com/nodejs/node/commit/92eeeb98a5)] - **doc**: include CVE to EOL lines as sec release process (Rafael Gonzaga) [#56520](https://github.com/nodejs/node/pull/56520)
- \[[`233a6a93a1`](https://github.com/nodejs/node/commit/233a6a93a1)] - **doc**: add esm examples to node:trace_events (Alfredo González) [#56514](https://github.com/nodejs/node/pull/56514)
- \[[`d9cff6c73f`](https://github.com/nodejs/node/commit/d9cff6c73f)] - **doc**: reserve NMV 133 for Electron 35 (Keeley Hammond) [#56513](https://github.com/nodejs/node/pull/56513)
- \[[`6047fd7c5c`](https://github.com/nodejs/node/commit/6047fd7c5c)] - **doc**: add message for Ambassadors to promote (Michael Dawson) [#56235](https://github.com/nodejs/node/pull/56235)
- \[[`a4045c9488`](https://github.com/nodejs/node/commit/a4045c9488)] - **doc**: allow request for TSC reviews via the GitHub UI (Antoine du Hamel) [#56493](https://github.com/nodejs/node/pull/56493)
- \[[`dd3f94873e`](https://github.com/nodejs/node/commit/dd3f94873e)] - **esm**: fix jsdoc type refs to `ModuleJobBase` in esm/loader (Jacob Smith) [#56499](https://github.com/nodejs/node/pull/56499)
- \[[`9414d3cbf1`](https://github.com/nodejs/node/commit/9414d3cbf1)] - **(SEMVER-MINOR)** **fs**: allow `exclude` option in globs to accept glob patterns (Daeyeon Jeong) [#56489](https://github.com/nodejs/node/pull/56489)
- \[[`4202045673`](https://github.com/nodejs/node/commit/4202045673)] - **http2**: omit server name when HTTP2 host is IP address (islandryu) [#56530](https://github.com/nodejs/node/pull/56530)
- \[[`f48a562776`](https://github.com/nodejs/node/commit/f48a562776)] - **inspector**: roll inspector_protocol (Chengzhong Wu) [#56649](https://github.com/nodejs/node/pull/56649)
- \[[`9a954fbf4a`](https://github.com/nodejs/node/commit/9a954fbf4a)] - **inspector**: add undici http tracking support (Chengzhong Wu) [#56488](https://github.com/nodejs/node/pull/56488)
- \[[`f185e8a34a`](https://github.com/nodejs/node/commit/f185e8a34a)] - **inspector**: report loadingFinished until the response data is consumed (Chengzhong Wu) [#56372](https://github.com/nodejs/node/pull/56372)
- \[[`2fb007fdce`](https://github.com/nodejs/node/commit/2fb007fdce)] - **lib**: allow skipping source maps in node_modules (Chengzhong Wu) [#56639](https://github.com/nodejs/node/pull/56639)
- \[[`2f69dc2659`](https://github.com/nodejs/node/commit/2f69dc2659)] - **meta**: move one or more collaborators to emeritus (Node.js GitHub Bot) [#56580](https://github.com/nodejs/node/pull/56580)
- \[[`0d869963e0`](https://github.com/nodejs/node/commit/0d869963e0)] - **meta**: add codeowners of security release document (Rafael Gonzaga) [#56521](https://github.com/nodejs/node/pull/56521)
- \[[`59510ab819`](https://github.com/nodejs/node/commit/59510ab819)] - **module**: fix bad `require.resolve` with option paths for `.` and `..` (Dario Piotrowicz) [#56735](https://github.com/nodejs/node/pull/56735)
- \[[`58d2dad67d`](https://github.com/nodejs/node/commit/58d2dad67d)] - **module**: integrate TypeScript into compile cache (Joyee Cheung) [#56629](https://github.com/nodejs/node/pull/56629)
- \[[`9f99a6acb5`](https://github.com/nodejs/node/commit/9f99a6acb5)] - **module**: use more defensive code when handling SWC errors (Antoine du Hamel) [#56646](https://github.com/nodejs/node/pull/56646)
- \[[`7347d34053`](https://github.com/nodejs/node/commit/7347d34053)] - **module**: fixing url change in load sync hook chain (Vitalii Akimov) [#56402](https://github.com/nodejs/node/pull/56402)
- \[[`9c5c3b3115`](https://github.com/nodejs/node/commit/9c5c3b3115)] - **(SEMVER-MINOR)** **module**: add ERR_UNSUPPORTED_TYPESCRIPT_SYNTAX (Marco Ippolito) [#56610](https://github.com/nodejs/node/pull/56610)
- \[[`afd1f91a1e`](https://github.com/nodejs/node/commit/afd1f91a1e)] - **module**: fix jsdoc for `format` parameter in cjs/loader (pacexy) [#56501](https://github.com/nodejs/node/pull/56501)
- \[[`86d783fa51`](https://github.com/nodejs/node/commit/86d783fa51)] - **module**: rethrow amaro error message (Marco Ippolito) [#56568](https://github.com/nodejs/node/pull/56568)
- \[[`7b6df4a97a`](https://github.com/nodejs/node/commit/7b6df4a97a)] - **process**: fix symbol key and mark experimental new `node:process` methods (Antoine du Hamel) [#56517](https://github.com/nodejs/node/pull/56517)
- \[[`21362cc4f4`](https://github.com/nodejs/node/commit/21362cc4f4)] - **punycode**: limit deprecation warning (Colin Ihrig) [#56632](https://github.com/nodejs/node/pull/56632)
- \[[`93f60a1c15`](https://github.com/nodejs/node/commit/93f60a1c15)] - **sqlite**: disable memstatus APIs at build time (Colin Ihrig) [#56541](https://github.com/nodejs/node/pull/56541)
- \[[`1e201fd5fd`](https://github.com/nodejs/node/commit/1e201fd5fd)] - **(SEMVER-MINOR)** **sqlite**: support TypedArray and DataView in `StatementSync` (Alex Yang) [#56385](https://github.com/nodejs/node/pull/56385)
- \[[`3aca628a11`](https://github.com/nodejs/node/commit/3aca628a11)] - **sqlite**: enable SQL math functions (Colin Ihrig) [#56447](https://github.com/nodejs/node/pull/56447)
- \[[`575251ae6a`](https://github.com/nodejs/node/commit/575251ae6a)] - **src**: add nullptr handling from X509_STORE_new() (Burkov Egor) [#56700](https://github.com/nodejs/node/pull/56700)
- \[[`8fb03d8f43`](https://github.com/nodejs/node/commit/8fb03d8f43)] - **src**: move more crypto to ncrypto (James M Snell) [#56653](https://github.com/nodejs/node/pull/56653)
- \[[`55a0135261`](https://github.com/nodejs/node/commit/55a0135261)] - **src**: add default value for RSACipherConfig mode field (Burkov Egor) [#56701](https://github.com/nodejs/node/pull/56701)
- \[[`83c56da328`](https://github.com/nodejs/node/commit/83c56da328)] - **src**: fix build with GCC 15 (tjuhaszrh) [#56740](https://github.com/nodejs/node/pull/56740)
- \[[`872d68d87c`](https://github.com/nodejs/node/commit/872d68d87c)] - **src**: fix to generate path from wchar_t via wstring (yamachu) [#56696](https://github.com/nodejs/node/pull/56696)
- \[[`2b6a82dcea`](https://github.com/nodejs/node/commit/2b6a82dcea)] - **src**: replace NoArrayBufferZeroFillScope with v8 option (James M Snell) [#56658](https://github.com/nodejs/node/pull/56658)
- \[[`a5f9023297`](https://github.com/nodejs/node/commit/a5f9023297)] - **src**: initialize FSReqWrapSync in path that uses it (Michaël Zasso) [#56613](https://github.com/nodejs/node/pull/56613)
- \[[`90f70ed8dd`](https://github.com/nodejs/node/commit/90f70ed8dd)] - **src**: use cppgc to manage ContextifyContext (Joyee Cheung) [#56522](https://github.com/nodejs/node/pull/56522)
- \[[`0b1ac9653e`](https://github.com/nodejs/node/commit/0b1ac9653e)] - **src**: handle duplicate paths granted (Rafael Gonzaga) [#56591](https://github.com/nodejs/node/pull/56591)
- \[[`33f5345002`](https://github.com/nodejs/node/commit/33f5345002)] - **src**: update ECKeyPointer in ncrypto (James M Snell) [#56526](https://github.com/nodejs/node/pull/56526)
- \[[`c7b95fcf95`](https://github.com/nodejs/node/commit/c7b95fcf95)] - **src**: update ECPointPointer in ncrypto (James M Snell) [#56526](https://github.com/nodejs/node/pull/56526)
- \[[`c008b15108`](https://github.com/nodejs/node/commit/c008b15108)] - **src**: update ECGroupPointer in ncrypto (James M Snell) [#56526](https://github.com/nodejs/node/pull/56526)
- \[[`5673dc7de7`](https://github.com/nodejs/node/commit/5673dc7de7)] - **src**: update ECDASSigPointer implementation in ncrypto (James M Snell) [#56526](https://github.com/nodejs/node/pull/56526)
- \[[`87ba48b2c6`](https://github.com/nodejs/node/commit/87ba48b2c6)] - **src**: cleaning up more crypto internals for ncrypto (James M Snell) [#56526](https://github.com/nodejs/node/pull/56526)
- \[[`48c813fb67`](https://github.com/nodejs/node/commit/48c813fb67)] - **(SEMVER-MINOR)** **src**: add --disable-sigusr1 to prevent signal i/o thread (Rafael Gonzaga) [#56441](https://github.com/nodejs/node/pull/56441)
- \[[`50c65eed78`](https://github.com/nodejs/node/commit/50c65eed78)] - **src**: fix undefined script name in error source (Chengzhong Wu) [#56502](https://github.com/nodejs/node/pull/56502)
- \[[`b3c66d2493`](https://github.com/nodejs/node/commit/b3c66d2493)] - **src**: refactor --trace-env to reuse option selection and handling (Joyee Cheung) [#56293](https://github.com/nodejs/node/pull/56293)
- \[[`17d59efe3c`](https://github.com/nodejs/node/commit/17d59efe3c)] - **src**: minor cleanups on OneByteString usage (James M Snell) [#56482](https://github.com/nodejs/node/pull/56482)
- \[[`3e6e0106f6`](https://github.com/nodejs/node/commit/3e6e0106f6)] - **src**: move more crypto impl detail to ncrypto dep (James M Snell) [#56421](https://github.com/nodejs/node/pull/56421)
- \[[`5e1ddd5d4c`](https://github.com/nodejs/node/commit/5e1ddd5d4c)] - **src**: fixup more ToLocalChecked uses in node_file (James M Snell) [#56484](https://github.com/nodejs/node/pull/56484)
- \[[`aa3fd2f58f`](https://github.com/nodejs/node/commit/aa3fd2f58f)] - **src**: make some minor ToLocalChecked cleanups (James M Snell) [#56483](https://github.com/nodejs/node/pull/56483)
- \[[`7dd8165b0b`](https://github.com/nodejs/node/commit/7dd8165b0b)] - **src**: lock the thread properly in snapshot builder (Joyee Cheung) [#56327](https://github.com/nodejs/node/pull/56327)
- \[[`edafab7248`](https://github.com/nodejs/node/commit/edafab7248)] - **src**: drain platform tasks before creating startup snapshot (Chengzhong Wu) [#56403](https://github.com/nodejs/node/pull/56403)
- \[[`e1887d2c58`](https://github.com/nodejs/node/commit/e1887d2c58)] - **src**: use LocalVector in more places (James M Snell) [#56457](https://github.com/nodejs/node/pull/56457)
- \[[`cf16123785`](https://github.com/nodejs/node/commit/cf16123785)] - **(SEMVER-MINOR)** **src,worker**: add isInternalWorker (Carlos Espa) [#56469](https://github.com/nodejs/node/pull/56469)
- \[[`df78515664`](https://github.com/nodejs/node/commit/df78515664)] - **stream**: fix typo in ReadableStreamBYOBReader.readIntoRequests (Mattias Buelens) [#56560](https://github.com/nodejs/node/pull/56560)
- \[[`4ff79fb22a`](https://github.com/nodejs/node/commit/4ff79fb22a)] - **test**: reduce number of written chunks (Luigi Pinca) [#56757](https://github.com/nodejs/node/pull/56757)
- \[[`2e7b7b7674`](https://github.com/nodejs/node/commit/2e7b7b7674)] - **test**: fix invalid common.mustSucceed() usage (Luigi Pinca) [#56756](https://github.com/nodejs/node/pull/56756)
- \[[`0af368ce5e`](https://github.com/nodejs/node/commit/0af368ce5e)] - **test**: use strict mode in global setters test (Rich Trott) [#56742](https://github.com/nodejs/node/pull/56742)
- \[[`e49f3e944c`](https://github.com/nodejs/node/commit/e49f3e944c)] - **test**: cleanup and simplify test-crypto-aes-wrap (James M Snell) [#56748](https://github.com/nodejs/node/pull/56748)
- \[[`85f7bbf4e4`](https://github.com/nodejs/node/commit/85f7bbf4e4)] - **test**: do not use common.isMainThread (Luigi Pinca) [#56768](https://github.com/nodejs/node/pull/56768)
- \[[`36b02bf1b1`](https://github.com/nodejs/node/commit/36b02bf1b1)] - **test**: make some requires lazy in common/index (James M Snell) [#56715](https://github.com/nodejs/node/pull/56715)
- \[[`bcb35c3fb7`](https://github.com/nodejs/node/commit/bcb35c3fb7)] - **test**: add test that uses multibyte for path and resolves modules (yamachu) [#56696](https://github.com/nodejs/node/pull/56696)
- \[[`917f98b29c`](https://github.com/nodejs/node/commit/917f98b29c)] - **test**: replace more uses of `global` with `globalThis` (James M Snell) [#56712](https://github.com/nodejs/node/pull/56712)
- \[[`bf34a49206`](https://github.com/nodejs/node/commit/bf34a49206)] - **test**: make common/index slightly less node.js specific (James M Snell) [#56712](https://github.com/nodejs/node/pull/56712)
- \[[`ef2ed71389`](https://github.com/nodejs/node/commit/ef2ed71389)] - **test**: rely less on duplicative common test harness utilities (James M Snell) [#56712](https://github.com/nodejs/node/pull/56712)
- \[[`e654c8b84a`](https://github.com/nodejs/node/commit/e654c8b84a)] - **test**: simplify common/index.js (James M Snell) [#56712](https://github.com/nodejs/node/pull/56712)
- \[[`a62345e73b`](https://github.com/nodejs/node/commit/a62345e73b)] - **test**: move hasMultiLocalhost to common/net (James M Snell) [#56716](https://github.com/nodejs/node/pull/56716)
- \[[`6edf04ee5e`](https://github.com/nodejs/node/commit/6edf04ee5e)] - **test**: move crypto related common utilities in common/crypto (James M Snell) [#56714](https://github.com/nodejs/node/pull/56714)
- \[[`c7a132229f`](https://github.com/nodejs/node/commit/c7a132229f)] - **test**: add missing test for env file (Jonas) [#56642](https://github.com/nodejs/node/pull/56642)
- \[[`2a219eddf6`](https://github.com/nodejs/node/commit/2a219eddf6)] - **test**: enforce strict mode in test-zlib-const (Rich Trott) [#56689](https://github.com/nodejs/node/pull/56689)
- \[[`f885496d9c`](https://github.com/nodejs/node/commit/f885496d9c)] - **test**: fix localization data for ICU 74.2 (Antoine du Hamel) [#56661](https://github.com/nodejs/node/pull/56661)
- \[[`eb3148fb5c`](https://github.com/nodejs/node/commit/eb3148fb5c)] - **test**: use --permission instead of --experimental-permission (Rafael Gonzaga) [#56685](https://github.com/nodejs/node/pull/56685)
- \[[`86d7ba09c4`](https://github.com/nodejs/node/commit/86d7ba09c4)] - **test**: test-stream-compose.js doesn't need internals (Meghan Denny) [#56619](https://github.com/nodejs/node/pull/56619)
- \[[`676276889e`](https://github.com/nodejs/node/commit/676276889e)] - **test**: add maxCount and gcOptions to gcUntil() (Joyee Cheung) [#56522](https://github.com/nodejs/node/pull/56522)
- \[[`5b7a012144`](https://github.com/nodejs/node/commit/5b7a012144)] - **test**: add line break at end of file (Rafael Gonzaga) [#56588](https://github.com/nodejs/node/pull/56588)
- \[[`27cfec619f`](https://github.com/nodejs/node/commit/27cfec619f)] - **test**: mark test-worker-prof as flaky on smartos (Joyee Cheung) [#56583](https://github.com/nodejs/node/pull/56583)
- \[[`7e58da68c1`](https://github.com/nodejs/node/commit/7e58da68c1)] - **test**: update ts eval snapshots (Marco Ippolito) [#56568](https://github.com/nodejs/node/pull/56568)
- \[[`b1c54439ae`](https://github.com/nodejs/node/commit/b1c54439ae)] - **test**: update test-child-process-bad-stdio to use node:test (Colin Ihrig) [#56562](https://github.com/nodejs/node/pull/56562)
- \[[`0d772a963e`](https://github.com/nodejs/node/commit/0d772a963e)] - **test**: disable openssl 3.4.0 incompatible tests (Jelle van der Waa) [#56160](https://github.com/nodejs/node/pull/56160)
- \[[`6fa6d699ff`](https://github.com/nodejs/node/commit/6fa6d699ff)] - **test**: make test-crypto-hash compatible with OpenSSL > 3.4.0 (Jelle van der Waa) [#56160](https://github.com/nodejs/node/pull/56160)
- \[[`90e12f2945`](https://github.com/nodejs/node/commit/90e12f2945)] - **test**: clarify fork inherit permission flags (Rafael Gonzaga) [#56523](https://github.com/nodejs/node/pull/56523)
- \[[`323f96f7b3`](https://github.com/nodejs/node/commit/323f96f7b3)] - **test**: add error only reporter for node:test (Carlos Espa) [#56438](https://github.com/nodejs/node/pull/56438)
- \[[`cbbcaf9108`](https://github.com/nodejs/node/commit/cbbcaf9108)] - **test**: mark test-http-server-request-timeouts-mixed as flaky (Joyee Cheung) [#56503](https://github.com/nodejs/node/pull/56503)
- \[[`295db19ba2`](https://github.com/nodejs/node/commit/295db19ba2)] - **test**: update error code in tls-psk-circuit for for OpenSSL 3.4 (sebastianas) [#56420](https://github.com/nodejs/node/pull/56420)
- \[[`f7563780a6`](https://github.com/nodejs/node/commit/f7563780a6)] - **test**: update compiled sqlite tests to match other tests (Colin Ihrig) [#56446](https://github.com/nodejs/node/pull/56446)
- \[[`8feb2737e7`](https://github.com/nodejs/node/commit/8feb2737e7)] - **test**: add initial test426 coverage (Chengzhong Wu) [#56436](https://github.com/nodejs/node/pull/56436)
- \[[`b9cd7895c0`](https://github.com/nodejs/node/commit/b9cd7895c0)] - **test**: update test-set-http-max-http-headers to use node:test (Colin Ihrig) [#56439](https://github.com/nodejs/node/pull/56439)
- \[[`332ce548cb`](https://github.com/nodejs/node/commit/332ce548cb)] - **test**: update test-child-process-windows-hide to use node:test (Colin Ihrig) [#56437](https://github.com/nodejs/node/pull/56437)
- \[[`e2668c0e00`](https://github.com/nodejs/node/commit/e2668c0e00)] - **test_runner**: print failing assertion only once with spec reporter (Pietro Marchini) [#56662](https://github.com/nodejs/node/pull/56662)
- \[[`f97cd5b02b`](https://github.com/nodejs/node/commit/f97cd5b02b)] - **test_runner**: remove unused errors (Pietro Marchini) [#56607](https://github.com/nodejs/node/pull/56607)
- \[[`13bdd9c961`](https://github.com/nodejs/node/commit/13bdd9c961)] - **(SEMVER-MINOR)** **test_runner**: add TestContext.prototype.waitFor() (Colin Ihrig) [#56595](https://github.com/nodejs/node/pull/56595)
- \[[`00a1943858`](https://github.com/nodejs/node/commit/00a1943858)] - **(SEMVER-MINOR)** **test_runner**: add t.assert.fileSnapshot() (Colin Ihrig) [#56459](https://github.com/nodejs/node/pull/56459)
- \[[`c4979ebfb2`](https://github.com/nodejs/node/commit/c4979ebfb2)] - **test_runner**: run single test file benchmark (Pietro Marchini) [#56479](https://github.com/nodejs/node/pull/56479)
- \[[`839a06e908`](https://github.com/nodejs/node/commit/839a06e908)] - **test_runner**: differentiate test types in enqueue dequeue events (Eddie Abbondanzio) [#54049](https://github.com/nodejs/node/pull/54049)
- \[[`3143566045`](https://github.com/nodejs/node/commit/3143566045)] - **(SEMVER-MINOR)** **test_runner**: add assert.register() API (Colin Ihrig) [#56434](https://github.com/nodejs/node/pull/56434)
- \[[`3aa864904f`](https://github.com/nodejs/node/commit/3aa864904f)] - **test_runner**: finish marking snapshot testing as stable (Colin Ihrig) [#56425](https://github.com/nodejs/node/pull/56425)
- \[[`b7b0768cda`](https://github.com/nodejs/node/commit/b7b0768cda)] - **tls**: fix error stack conversion in cryptoErrorListToException() (Joyee Cheung) [#56554](https://github.com/nodejs/node/pull/56554)
- \[[`8f59f5ba47`](https://github.com/nodejs/node/commit/8f59f5ba47)] - **tools**: update doc to new version (Node.js GitHub Bot) [#56259](https://github.com/nodejs/node/pull/56259)
- \[[`ebf4527730`](https://github.com/nodejs/node/commit/ebf4527730)] - **tools**: update inspector_protocol roller (Chengzhong Wu) [#56649](https://github.com/nodejs/node/pull/56649)
- \[[`649cf0c0f6`](https://github.com/nodejs/node/commit/649cf0c0f6)] - **tools**: do not throw on missing `create-release-proposal.sh` (Antoine du Hamel) [#56704](https://github.com/nodejs/node/pull/56704)
- \[[`69cb44e315`](https://github.com/nodejs/node/commit/69cb44e315)] - **tools**: fix tools-deps-update (Daniel Lemire) [#56684](https://github.com/nodejs/node/pull/56684)
- \[[`02f36ca11b`](https://github.com/nodejs/node/commit/02f36ca11b)] - **tools**: do not throw on missing `create-release-proposal.sh` (Antoine du Hamel) [#56695](https://github.com/nodejs/node/pull/56695)
- \[[`bcc1c65066`](https://github.com/nodejs/node/commit/bcc1c65066)] - **tools**: fix permissions in `lint-release-proposal` workflow (Antoine du Hamel) [#56614](https://github.com/nodejs/node/pull/56614)
- \[[`ab4cfef600`](https://github.com/nodejs/node/commit/ab4cfef600)] - **tools**: remove github reporter (Carlos Espa) [#56468](https://github.com/nodejs/node/pull/56468)
- \[[`477e674a2a`](https://github.com/nodejs/node/commit/477e674a2a)] - **tools**: edit `create-release-proposal` workflow (Antoine du Hamel) [#56540](https://github.com/nodejs/node/pull/56540)
- \[[`5f6785b1cb`](https://github.com/nodejs/node/commit/5f6785b1cb)] - **tools**: validate commit list as part of `lint-release-commit` (Antoine du Hamel) [#56291](https://github.com/nodejs/node/pull/56291)
- \[[`2a0fbd8731`](https://github.com/nodejs/node/commit/2a0fbd8731)] - **tools**: fix loong64 build failed (Xiao-Tao) [#56466](https://github.com/nodejs/node/pull/56466)
- \[[`aea088f79e`](https://github.com/nodejs/node/commit/aea088f79e)] - **tools**: disable unneeded rule ignoring in Python linting (Rich Trott) [#56429](https://github.com/nodejs/node/pull/56429)
- \[[`7a0dd2d04f`](https://github.com/nodejs/node/commit/7a0dd2d04f)] - **tools**: use a configurable value for number of open dependabot PRs (Antoine du Hamel) [#56427](https://github.com/nodejs/node/pull/56427)
- \[[`c249c9715a`](https://github.com/nodejs/node/commit/c249c9715a)] - **tools**: bump the eslint group in /tools/eslint with 4 updates (dependabot\[bot]) [#56426](https://github.com/nodejs/node/pull/56426)
- \[[`a9d332a16f`](https://github.com/nodejs/node/commit/a9d332a16f)] - **util**: inspect: do not crash on an Error stack that contains a Symbol (Jordan Harband) [#56573](https://github.com/nodejs/node/pull/56573)
- \[[`6a16012fd7`](https://github.com/nodejs/node/commit/6a16012fd7)] - **util**: inspect: do not crash on an Error with a regex `name` (Jordan Harband) [#56574](https://github.com/nodejs/node/pull/56574)
- \[[`c7f16192f4`](https://github.com/nodejs/node/commit/c7f16192f4)] - **util**: rename CallSite.column to columnNumber (Chengzhong Wu) [#56584](https://github.com/nodejs/node/pull/56584)
- \[[`e652781934`](https://github.com/nodejs/node/commit/e652781934)] - **util**: do not crash on inspecting function with `Symbol` name (Jordan Harband) [#56572](https://github.com/nodejs/node/pull/56572)
- \[[`d066acfcf9`](https://github.com/nodejs/node/commit/d066acfcf9)] - **util**: expose CallSite.scriptId (Chengzhong Wu) [#56551](https://github.com/nodejs/node/pull/56551)
- \[[`e1b0f44d19`](https://github.com/nodejs/node/commit/e1b0f44d19)] - **watch**: reload env file for --env-file-if-exists (Jonas) [#56643](https://github.com/nodejs/node/pull/56643)
- \[[`538e19489f`](https://github.com/nodejs/node/commit/538e19489f)] - **worker**: refactor stdio to improve performance (Matteo Collina) [#56630](https://github.com/nodejs/node/pull/56630)
- \[[`aab53e6965`](https://github.com/nodejs/node/commit/aab53e6965)] - **worker**: flush stdout and stderr on exit (Matteo Collina) [#56428](https://github.com/nodejs/node/pull/56428)

Windows 64-bit Installer: https://nodejs.org/dist/v23.7.0/node-v23.7.0-x64.msi \
Windows ARM 64-bit Installer: https://nodejs.org/dist/v23.7.0/node-v23.7.0-arm64.msi \
Windows 64-bit Binary: https://nodejs.org/dist/v23.7.0/win-x64/node.exe \
Windows ARM 64-bit Binary: https://nodejs.org/dist/v23.7.0/win-arm64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v23.7.0/node-v23.7.0.pkg \
macOS Apple Silicon 64-bit Binary: https://nodejs.org/dist/v23.7.0/node-v23.7.0-darwin-arm64.tar.gz \
macOS Intel 64-bit Binary: https://nodejs.org/dist/v23.7.0/node-v23.7.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v23.7.0/node-v23.7.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v23.7.0/node-v23.7.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v23.7.0/node-v23.7.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v23.7.0/node-v23.7.0-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v23.7.0/node-v23.7.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v23.7.0/node-v23.7.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v23.7.0/node-v23.7.0.tar.gz \
Other release files: https://nodejs.org/dist/v23.7.0/ \
Documentation: https://nodejs.org/docs/v23.7.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

798c6290890c79647af8614a85cffce0d6ee0a40dd1e5ba66cff2a1a3192dc9c  node-v23.7.0-aix-ppc64.tar.gz
e7679830296ddb84f5da6af3239ed0f4ca65c35b337c6a802112d5e1e6ec2058  node-v23.7.0-arm64.msi
0dac0de3308a87f84cb14bab349a3f0ae5f6cdb8da32600459ee407236f9cebc  node-v23.7.0-darwin-arm64.tar.gz
3c840c46d75a6fb969de22a71ebccd4001ac5722380c94b58e49ab0df38d0130  node-v23.7.0-darwin-arm64.tar.xz
d32b53d95a42a64388486490e7c3f6a23f00ae3bf6175c64c200954cd41ef2ba  node-v23.7.0-darwin-x64.tar.gz
db0a00a74c1bebd4d5180a60bd9995d8764c5a1e275545de769cbd65ac07078b  node-v23.7.0-darwin-x64.tar.xz
48d8ce27195e3fc014326da693a4a8cf90dde621d72694242f887d1df59755b5  node-v23.7.0-headers.tar.gz
cf9d91476998f7c2c0b9185b85045c250ea8d345ddb6abfeab059c3844f85dda  node-v23.7.0-headers.tar.xz
3997bbd0e56d5e3664b66025b13072de2851743a811c2afb16e349ab798203c4  node-v23.7.0-linux-arm64.tar.gz
023bb335f70a17cd628e905f772e7677fd54d80ce9245782a38e6c196c6a5166  node-v23.7.0-linux-arm64.tar.xz
af6958987ca97779fc36448adf425eef0255278a35a24590fa28762a016a0f78  node-v23.7.0-linux-armv7l.tar.gz
9466ef9545b14e1b7963c6ad00ecac67fd3b35fb91a390d7eb97b7018143abda  node-v23.7.0-linux-armv7l.tar.xz
ce3557951af359312744863425f7ce4923d390753997701e1f739262bf169e0d  node-v23.7.0-linux-ppc64le.tar.gz
4e05e52760f11ff7141321473840d7297a273efedca5e1cb793836778f531799  node-v23.7.0-linux-ppc64le.tar.xz
ca716813317d8af02fe79cd854f1507c495bde018bc64a8e8cec138f6eea7b32  node-v23.7.0-linux-s390x.tar.gz
a58cad0b6683fea01a332da9c82189457a6078f588646ea2df80cab529afaff9  node-v23.7.0-linux-s390x.tar.xz
d9173025ec1f18264bc5f1e70e99c19d9bba72c563143d150db0ced98a177c2f  node-v23.7.0-linux-x64.tar.gz
4d9482dc62f9b82279055637f2a92c7e8297d78be8d68e110ac3aae2e56d0ebc  node-v23.7.0-linux-x64.tar.xz
fc867f213a871a089d8d1b26f20769b709d50042023c6461e46be08e75151aee  node-v23.7.0-win-arm64.7z
50c325570224d0c6ee1ceea6cc679aa4e580edfa5dab174d9da399fe476c3371  node-v23.7.0-win-arm64.zip
49654dee86b43383e871e11bef8cb0c0b8a3373100219986de540934f61f2db0  node-v23.7.0-win-x64.7z
9b26ebc897984332d1b05948cae9da0ab32b3fc23e01133c04aab09f9bcbb4eb  node-v23.7.0-win-x64.zip
4bb9c2f05574c0580910cd2bdd7e2352fd6bfe09960be04993db47a22d8271dc  node-v23.7.0-x64.msi
28e948860cbe6c28f2389b4056e726d859485847253c9f7758cce9f565a5ba41  node-v23.7.0.pkg
44288a847f8c2659d225ed1c14bfb557926c571d08465c897c3f41db0216a7a8  node-v23.7.0.tar.gz
8de192ef2fee2ee8a230dd8d0e9aee182ee9c9856ccdb5fd95188abe84f77242  node-v23.7.0.tar.xz
055edfd91b382d5d87ff956ad6458ee12fbe75ac9651d0b3d1aa0ab38c59d8f8  win-arm64/node.exe
de5fc264f1b4833ef35b819ceb0198742ec2ad71d88cea0402a4145322b05a58  win-arm64/node.lib
d4cc6fb37eb00a905835d107dc3bf88969def5f56d6654080d32beb3cc3bd469  win-arm64/node_pdb.7z
d7e7f0b1dd031d84af85cda8a1a9016e32fd6bb8112e3c3dab323a7477573148  win-arm64/node_pdb.zip
6306c305da83a33e82179567f0303d6678d5602ff692c8dd7be2887390399776  win-x64/node.exe
3b8d94c6b2f4338e173d63d2929b0181715490f1781c89972ed770d21d5de9b0  win-x64/node.lib
9a6d7ff8b7bf76018fd6f57b4e9bc4705f53722352f7c01fe827b74cb01f577b  win-x64/node_pdb.7z
6f600811c695043010e1e0be72930b369cd71095fe782dca9867a54cd2c9a41d  win-x64/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEwNYkhDnx1WBKr/tAIdkA/9sjN1YFAmebvl0ACgkQIdkA/9sj
N1YjFA//amabuJMCqb7w6LGyPhHGzMwGZUFrz8OMrS3L9CHR+ZxA5WLKAAa6j8xA
/GPvBiUyFr+1cZyzyVM2sSKr7K1ui5E4L6ae9obOKkYzYmLRarGPjXDbEK9NtshT
PFEkOM5DchPK7wxnyIv3R4FXfjMxbaITghSGCsuqipfT5CuR+9wwpUR5y9hfYdlQ
HYwjA6CjBm2Oi69qYvR9jfWkxsvbfyRIpdKdns4WlWIz9vJ+1FldqM8DZXBsM397
fDnKYpKg5HWZgysLZ1V2r8sYRGmSvd3/8epSD34bbl7VeWDLSxTvLlDWFOJt0qTh
Z+oGJWACH9LxnuSuTR+DMy21PUC6dMnhwQ2txPznxKlBJdpsyU36+15CICyiNdLI
G5F+y/7sXLIRZYTUwMMC+TsaePUnE9C3dkorTaBijMEQ4vtkZDZBHOu1zlE4hk2A
B60fu6B9yF2hCMnw/AFuVs4AE3ZeZQ2U88ocHv/KCtNgx8oJIbBduv3PRhs319N/
mUm7p6TfBAOHMigoIF0IfuaAOl/Y6/KFcDhjhmSXXZrdF8DAkKycYOWtRsNmGj6W
FMungaIH6AgRUOycEUoXJ+Hk5TkpN3wT0eAUABw80L9mQUf4bXgFUlMVUWu/lh3E
YtfzHqChCQhxYFD7JiZDof1UUcWMlmUiETokrOZwh9ciTt750tY=
=voRk
-----END PGP SIGNATURE-----
```
