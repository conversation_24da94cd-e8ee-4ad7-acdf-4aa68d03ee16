---
date: '2020-05-05T18:30:07.465Z'
category: release
title: Node v14.2.0 (Current)
layout: blog-post
author: <PERSON><PERSON><PERSON>
---

### Notable Changes

#### Track function calls with `assert.CallTracker` (experimental)

`assert.CallTracker` is a new experimental API that allows to track and later
verify the number of times a function was called. This works by creating a
`CallTracker` object and using its `calls` method to create wrapper functions
that will count each time they are called. Then the `verify` method can be used
to assert that the expected number of calls happened:

```js
const assert = require('assert');

const tracker = new assert.CallTracker();

function func() {}
// callsfunc() must be called exactly twice before tracker.verify().
const callsfunc = tracker.calls(func, 2);
callsfunc();
callsfunc();

function otherFunc() {}
// The second parameter defaults to `1`.
const callsotherFunc = tracker.calls(otherFunc);
callsotherFunc();

// Calls tracker.verify() and verifies if all tracker.calls() functions have
// been called the right number of times.
process.on('exit', () => {
  tracker.verify();
});
```

Additionally, `tracker.report()` will return an array which contains information
about the errors, if there are any:

<!-- eslint-disable max-len -->

```js
const assert = require('assert');

const tracker = new assert.CallTracker();

function func() {}
const callsfunc = tracker.calls(func);

console.log(tracker.report());
/*
[
  {
    message: 'Expected the func function to be executed 1 time(s) but was executed 0 time(s).',
    actual: 0,
    expected: 1,
    operator: 'func',
    stack: Error
        ...
  }
]
*/
```

Contributed by ConorDavenport - [#31982](https://github.com/nodejs/node/pull/31982).

#### Console `groupIndentation` option

The Console constructor (`require('console').Console`) now supports different group indentations.

This is useful in case you want different grouping width than 2 spaces.

```js
const { Console } = require('console');
const customConsole = new Console({
  stdout: process.stdout,
  stderr: process.stderr,
  groupIndentation: 10,
});

customConsole.log('foo');
// 'foo'
customConsole.group();
customConsole.log('foo');
//           'foo'
```

Contributed by rickyes - [#32964](https://github.com/nodejs/node/pull/32964).

### Commits

#### Semver-minor commits

- [[`c87ed21fdf`](https://github.com/nodejs/node/commit/c87ed21fdf)] - **(SEMVER-MINOR)** **assert**: port common.mustCall() to assert (ConorDavenport) [#31982](https://github.com/nodejs/node/pull/31982)
- [[`c49e3ea20c`](https://github.com/nodejs/node/commit/c49e3ea20c)] - **(SEMVER-MINOR)** **console**: support console constructor groupIndentation option (rickyes) [#32964](https://github.com/nodejs/node/pull/32964)
- [[`bc9e413dae`](https://github.com/nodejs/node/commit/bc9e413dae)] - **(SEMVER-MINOR)** **worker**: add stack size resource limit option (Anna Henningsen) [#33085](https://github.com/nodejs/node/pull/33085)

#### Semver-patch commits

- [[`f62d92b900`](https://github.com/nodejs/node/commit/f62d92b900)] - **build**: add --error-on-warn configure flag (Daniel Bevenius) [#32685](https://github.com/nodejs/node/pull/32685)
- [[`db293c47dd`](https://github.com/nodejs/node/commit/db293c47dd)] - **cluster**: fix error on worker disconnect/destroy (Santiago Gimeno) [#32793](https://github.com/nodejs/node/pull/32793)
- [[`83e165bf88`](https://github.com/nodejs/node/commit/83e165bf88)] - **crypto**: check DiffieHellman p and g params (Ben Noordhuis) [#32739](https://github.com/nodejs/node/pull/32739)
- [[`e07cca6af6`](https://github.com/nodejs/node/commit/e07cca6af6)] - **crypto**: generator must be int32 in DiffieHellman() (Ben Noordhuis) [#32739](https://github.com/nodejs/node/pull/32739)
- [[`637442fec9`](https://github.com/nodejs/node/commit/637442fec9)] - **crypto**: key size must be int32 in DiffieHellman() (Ben Noordhuis) [#32739](https://github.com/nodejs/node/pull/32739)
- [[`c5a4534d5c`](https://github.com/nodejs/node/commit/c5a4534d5c)] - **deps**: V8: backport e29c62b74854 (Anna Henningsen) [#33125](https://github.com/nodejs/node/pull/33125)
- [[`8325c29e92`](https://github.com/nodejs/node/commit/8325c29e92)] - **deps**: update to uvwasi 0.0.8 (Colin Ihrig) [#33078](https://github.com/nodejs/node/pull/33078)
- [[`2174159598`](https://github.com/nodejs/node/commit/2174159598)] - **esm**: improve commonjs hint on module not found (Daniele Belardi) [#31906](https://github.com/nodejs/node/pull/31906)
- [[`74b0e8c3a8`](https://github.com/nodejs/node/commit/74b0e8c3a8)] - **http**: ensure client request emits close (Robert Nagy) [#33178](https://github.com/nodejs/node/pull/33178)
- [[`a4ec01c55b`](https://github.com/nodejs/node/commit/a4ec01c55b)] - **http**: simplify sending header (Robert Nagy) [#33200](https://github.com/nodejs/node/pull/33200)
- [[`451993ea94`](https://github.com/nodejs/node/commit/451993ea94)] - **http**: set default timeout in agent keepSocketAlive (Owen Smith) [#33127](https://github.com/nodejs/node/pull/33127)
- [[`3cb1713a59`](https://github.com/nodejs/node/commit/3cb1713a59)] - **http2,doc**: minor fixes (Alba Mendez) [#28044](https://github.com/nodejs/node/pull/28044)
- [[`eab4be1b93`](https://github.com/nodejs/node/commit/eab4be1b93)] - **lib**: cosmetic change to builtinLibs list for maintainability (James M Snell) [#33106](https://github.com/nodejs/node/pull/33106)
- [[`542da430ff`](https://github.com/nodejs/node/commit/542da430ff)] - **lib**: fix validateport error message when allowZero is false (rickyes) [#32861](https://github.com/nodejs/node/pull/32861)
- [[`5eccf1e9ad`](https://github.com/nodejs/node/commit/5eccf1e9ad)] - **module**: no type module resolver side effects (Guy Bedford) [#33086](https://github.com/nodejs/node/pull/33086)
- [[`466213d726`](https://github.com/nodejs/node/commit/466213d726)] - **n-api**: simplify uv_idle wrangling (Ben Noordhuis) [#32997](https://github.com/nodejs/node/pull/32997)
- [[`ed45b51642`](https://github.com/nodejs/node/commit/ed45b51642)] - **path**: fix comment grammar (thecodrr) [#32942](https://github.com/nodejs/node/pull/32942)
- [[`bb2d2f6e0e`](https://github.com/nodejs/node/commit/bb2d2f6e0e)] - **src**: remove unused v8 Message namespace (Adrian Estrada) [#33180](https://github.com/nodejs/node/pull/33180)
- [[`de643bc325`](https://github.com/nodejs/node/commit/de643bc325)] - **src**: use unique_ptr for CachedData in ContextifyScript::New (Anna Henningsen) [#33113](https://github.com/nodejs/node/pull/33113)
- [[`f61928ba35`](https://github.com/nodejs/node/commit/f61928ba35)] - **src**: return undefined when validation err == 0 (James M Snell) [#33107](https://github.com/nodejs/node/pull/33107)
- [[`f4e5ab14da`](https://github.com/nodejs/node/commit/f4e5ab14da)] - **src**: crypto::UseSNIContext to use BaseObjectPtr (James M Snell) [#33107](https://github.com/nodejs/node/pull/33107)
- [[`541ea035bf`](https://github.com/nodejs/node/commit/541ea035bf)] - **src**: separate out NgLibMemoryManagerBase (James M Snell) [#33104](https://github.com/nodejs/node/pull/33104)
- [[`10a87c81cf`](https://github.com/nodejs/node/commit/10a87c81cf)] - **src**: remove unnecessary fully qualified names (rickyes) [#33077](https://github.com/nodejs/node/pull/33077)
- [[`45032a39e8`](https://github.com/nodejs/node/commit/45032a39e8)] - **stream**: fix stream.finished on Duplex (Robert Nagy) [#33133](https://github.com/nodejs/node/pull/33133)
- [[`4cfa7e0716`](https://github.com/nodejs/node/commit/4cfa7e0716)] - **stream**: simplify Readable push/unshift logic (himself65) [#32899](https://github.com/nodejs/node/pull/32899)
- [[`bc40ed31b3`](https://github.com/nodejs/node/commit/bc40ed31b3)] - **stream**: add null check in Readable.from (Pranshu Srivastava) [#32873](https://github.com/nodejs/node/pull/32873)
- [[`b183d0a18a`](https://github.com/nodejs/node/commit/b183d0a18a)] - **stream**: let Duplex re-use Writable properties (Robert Nagy) [#33079](https://github.com/nodejs/node/pull/33079)
- [[`ec24577406`](https://github.com/nodejs/node/commit/ec24577406)] - **v8**: use AliasedBuffers for passing heap statistics around (Joyee Cheung) [#32929](https://github.com/nodejs/node/pull/32929)
- [[`d39254ada6`](https://github.com/nodejs/node/commit/d39254ada6)] - **vm**: fix vm.measureMemory() and introduce execution option (Joyee Cheung) [#32988](https://github.com/nodejs/node/pull/32988)
- [[`4423304ac4`](https://github.com/nodejs/node/commit/4423304ac4)] - **vm**: throw error when duplicated exportNames in SyntheticModule (himself65) [#32810](https://github.com/nodejs/node/pull/32810)
- [[`3866dc1311`](https://github.com/nodejs/node/commit/3866dc1311)] - **wasi**: use free() to release preopen array (Anna Henningsen) [#33110](https://github.com/nodejs/node/pull/33110)
- [[`d7d9960d38`](https://github.com/nodejs/node/commit/d7d9960d38)] - **wasi**: update start() behavior to match spec (Colin Ihrig) [#33073](https://github.com/nodejs/node/pull/33073)
- [[`8d5ac1bbf0`](https://github.com/nodejs/node/commit/8d5ac1bbf0)] - **wasi**: rename \_\_wasi_unstable_reactor_start() (Colin Ihrig) [#33073](https://github.com/nodejs/node/pull/33073)
- [[`c6d632a72a`](https://github.com/nodejs/node/commit/c6d632a72a)] - **worker**: unify custom error creation (Anna Henningsen) [#33084](https://github.com/nodejs/node/pull/33084)

#### Documentation commits

- [[`6925b358f9`](https://github.com/nodejs/node/commit/6925b358f9)] - **doc**: mark assert.CallTracker experimental (Ruben Bridgewater) [#33124](https://github.com/nodejs/node/pull/33124)
- [[`413f5d3581`](https://github.com/nodejs/node/commit/413f5d3581)] - **doc**: add missing deprecation not (Robert Nagy) [#33203](https://github.com/nodejs/node/pull/33203)
- [[`7893bde07e`](https://github.com/nodejs/node/commit/7893bde07e)] - **doc**: fix a typo in crypto.generateKeyPairSync() (himself65) [#33187](https://github.com/nodejs/node/pull/33187)
- [[`d02ced8af6`](https://github.com/nodejs/node/commit/d02ced8af6)] - **doc**: add util.types.isArrayBufferView() (Kevin Locke) [#33092](https://github.com/nodejs/node/pull/33092)
- [[`36d50027af`](https://github.com/nodejs/node/commit/36d50027af)] - **doc**: clarify when not to run CI on docs (Juan José Arboleda) [#33101](https://github.com/nodejs/node/pull/33101)
- [[`a99013718c`](https://github.com/nodejs/node/commit/a99013718c)] - **doc**: fix the spelling error in stream.md (白一梓) [#31561](https://github.com/nodejs/node/pull/31561)
- [[`23962191c1`](https://github.com/nodejs/node/commit/23962191c1)] - **doc**: correct Nodejs to Node.js spelling (Nick Schonning) [#33088](https://github.com/nodejs/node/pull/33088)
- [[`de15edcfc0`](https://github.com/nodejs/node/commit/de15edcfc0)] - **doc**: improve worker pool example (Ranjan Purbey) [#33082](https://github.com/nodejs/node/pull/33082)
- [[`289a5c8dfb`](https://github.com/nodejs/node/commit/289a5c8dfb)] - **doc**: some grammar fixes (Chris Holland) [#33081](https://github.com/nodejs/node/pull/33081)
- [[`82e459d9af`](https://github.com/nodejs/node/commit/82e459d9af)] - **doc**: don't check links in tmp dirs (Ben Noordhuis) [#32996](https://github.com/nodejs/node/pull/32996)
- [[`c5a2f9a02a`](https://github.com/nodejs/node/commit/c5a2f9a02a)] - **doc**: fix markdown parsing on doc/api/os.md (Juan José Arboleda) [#33067](https://github.com/nodejs/node/pull/33067)

#### Other commits

- [[`60ebbc4386`](https://github.com/nodejs/node/commit/60ebbc4386)] - **test**: update c8 ignore comment (Benjamin Coe) [#33151](https://github.com/nodejs/node/pull/33151)
- [[`e276524fcc`](https://github.com/nodejs/node/commit/e276524fcc)] - **test**: skip memory usage tests when ASAN is enabled (Anna Henningsen) [#33129](https://github.com/nodejs/node/pull/33129)
- [[`89ed7a5862`](https://github.com/nodejs/node/commit/89ed7a5862)] - **test**: move test-process-title to sequential (Anna Henningsen) [#33150](https://github.com/nodejs/node/pull/33150)
- [[`af7da46d9b`](https://github.com/nodejs/node/commit/af7da46d9b)] - **test**: fix out-of-bound reads from invalid sizeof usage (Anna Henningsen) [#33115](https://github.com/nodejs/node/pull/33115)
- [[`9ccb6b2e8c`](https://github.com/nodejs/node/commit/9ccb6b2e8c)] - **test**: add missing calls to napi_async_destroy (Anna Henningsen) [#33114](https://github.com/nodejs/node/pull/33114)
- [[`3c2f608a8d`](https://github.com/nodejs/node/commit/3c2f608a8d)] - **test**: correct typo in test name (Colin Ihrig) [#33083](https://github.com/nodejs/node/pull/33083)
- [[`92c7e0620f`](https://github.com/nodejs/node/commit/92c7e0620f)] - **test**: check args on SourceTextModule cachedData (Juan José Arboleda) [#32956](https://github.com/nodejs/node/pull/32956)
- [[`f79ef96fea`](https://github.com/nodejs/node/commit/f79ef96fea)] - **test**: mark test flaky on freebsd (Sam Roberts) [#32849](https://github.com/nodejs/node/pull/32849)
- [[`aced1f5d70`](https://github.com/nodejs/node/commit/aced1f5d70)] - **test**: flaky test-stdout-close-catch on freebsd (Sam Roberts) [#32849](https://github.com/nodejs/node/pull/32849)
- [[`6734cc43df`](https://github.com/nodejs/node/commit/6734cc43df)] - **tools**: bump remark-preset-lint-node to 1.15.0 (Rich Trott) [#33157](https://github.com/nodejs/node/pull/33157)
- [[`a87d371014`](https://github.com/nodejs/node/commit/a87d371014)] - **tools**: fix redundant-move warning in inspector (Daniel Bevenius) [#32685](https://github.com/nodejs/node/pull/32685)
- [[`12426f59f5`](https://github.com/nodejs/node/commit/12426f59f5)] - **tools**: update remark-preset-lint-node@1.14.0 (Rich Trott) [#33072](https://github.com/nodejs/node/pull/33072)
- [[`8c40ffc329`](https://github.com/nodejs/node/commit/8c40ffc329)] - **tools**: update broken types in type parser (Colin Ihrig) [#33068](https://github.com/nodejs/node/pull/33068)

Windows 32-bit Installer: https://nodejs.org/dist/v14.2.0/node-v14.2.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v14.2.0/node-v14.2.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v14.2.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v14.2.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v14.2.0/node-v14.2.0.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v14.2.0/node-v14.2.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v14.2.0/node-v14.2.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v14.2.0/node-v14.2.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v14.2.0/node-v14.2.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v14.2.0/node-v14.2.0-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v14.2.0/node-v14.2.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v14.2.0/node-v14.2.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v14.2.0/node-v14.2.0.tar.gz \
Other release files: https://nodejs.org/dist/v14.2.0/ \
Documentation: https://nodejs.org/docs/v14.2.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

9b4461a1af6d6fc012db5c580ece88bb72aaf33307974fe7736e83ec4bca1788  node-v14.2.0-aix-ppc64.tar.gz
2447241aefe71dea8ba1552549e4df2e894d1ac12203630db3af63d4ae35c016  node-v14.2.0-darwin-x64.tar.gz
3caec491f8f8a46c0c88eeebfff6616c7fdbca9695b1e74cb70354507ac3bfd4  node-v14.2.0-darwin-x64.tar.xz
16158ba804b9d4877624477b706a82796c5895a1f130eada3546dd6070f76b73  node-v14.2.0-headers.tar.gz
f6ca257360e99cb8158ef9cd432f7620aba4f8635dcf9fe0a9c5da1747fe2614  node-v14.2.0-headers.tar.xz
2fd9bf7b3fc8a0e72ec27f1d274b8eedd1c81e8af3f82739c787ddc288037a4c  node-v14.2.0-linux-arm64.tar.gz
4587d2c52cd348094bd46ee4ee8cdfeb549462ead9b4aadc9cfc3c5fc3ba7215  node-v14.2.0-linux-arm64.tar.xz
5d898328e8985cd2714a56800766e27d5dbecf3c7ba953e1df9d155328b3ee76  node-v14.2.0-linux-armv7l.tar.gz
530df44de700ced3ee09f77c84a9ec75f4b6e2842ae970a71f6082874f84e966  node-v14.2.0-linux-armv7l.tar.xz
45c6a4edfd3179e9f53fc76faa0bd3c255022e6491d9961d9ff0caca9947bd98  node-v14.2.0-linux-ppc64le.tar.gz
426aad83e3399c9bb9c5972781eba2536cc2244013ee293bbecd7f15830f76b6  node-v14.2.0-linux-ppc64le.tar.xz
47843ea36678a898679b934347f2ab4471b227cc088f57a53afd502d37009cf6  node-v14.2.0-linux-s390x.tar.gz
936acec34a3225c27cea055cd55d775f9b0bfa4c87f8f184c93932058908094d  node-v14.2.0-linux-s390x.tar.xz
3307d8b95014e78b43f85242a03fe3b28edfb90cc15e1d26393dcbbc51d05c8e  node-v14.2.0-linux-x64.tar.gz
468cbd92271da8c0cacaa3fa432a73a332e398bade8ad7359a94aa8ab3cc3cca  node-v14.2.0-linux-x64.tar.xz
ade90531fb98d5ba5fb58df42e0e1aebd8c11ae1e67c3c720135887a3431adea  node-v14.2.0.pkg
8c9aa909567589e97a22b2df1cf6a8d61e0a546b4c784703e6722f13da259493  node-v14.2.0.tar.gz
8efdcc3ae381909cc9c4bd08644481a594e08b5a6a7d05814e1c32b1279e16cf  node-v14.2.0.tar.xz
51cc7f4a712cb969a4153ca5c2ebfe8c052987fa4e025d3d98b1c7b1240f06f2  node-v14.2.0-win-x64.7z
99085f45a894e257123d7c729113cc00ed1413df432dbdce5fe53867e7c53b11  node-v14.2.0-win-x64.zip
9d616effae140f8f53b5659f07bb0dd5bc3af00b06dfd649401403416ea0e5b3  node-v14.2.0-win-x86.7z
ec5a318016e91a6bb38adb95f9890a483f70e522f4bf97229fe85eb19cd0dd2e  node-v14.2.0-win-x86.zip
f855ba61fad5ba16756b47038a1e4c5cb50685dbe2f5a1876c05fbc7300e6ea9  node-v14.2.0-x64.msi
82a93917b3025575ce5436c5cba7aead7876a89289f3ed189444a065d8b57324  node-v14.2.0-x86.msi
fc3481a669f071e6a1977ff348ae072f324610dc0a92d051d772b594c6988638  win-x64/node.exe
0084f3d15cc6ca50db917c684941a85f8c4c901f726e1c74bbe57431a1479211  win-x64/node.lib
6ef164e08b2edd08240bb3a465726fb801e766166293355153c641a55d815768  win-x64/node_pdb.7z
8a49eed2e4a93f290d874009a1d96c377495c931b159895bdcadc7fd4554b411  win-x64/node_pdb.zip
02900d6f56eb7820df1c75400f7bc839df50fe70f326dfa2621055c13bd4a725  win-x86/node.exe
b4287d2e5632595de8078815d3b7cd63396c8674146896e17c736c9fead23eae  win-x86/node.lib
8a9a50dc90a1ec43e0e9ecc890a52441c8395fd14a264808ec71e7e6c848fed3  win-x86/node_pdb.7z
9b25070a4c10b289fb6cab40a25078ad198d6fbf13e483768387fbdc12d98a51  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEj8yhP+8dDC6RAI4Jdw96mlrhVgAFAl6xr94ACgkQdw96mlrh
VgAGIA/7BfKeeB0R7n03c+NsoL5XoPzdGVA/ZGJbLrWB0gawIIcYeFv4+AHiSSLy
wmECt/whwgsGUBdtfjXa7uBhD0Cq56sybQfQrIPFIt5xJO5HH/hFaRe0qqHuL0tb
GWzjaNGZawgfS5DXka1fJ1AnqBWMkeaJKQB8owy5Jrzmphd37Pee6Wn+Ik5xSx3h
swNTNn1JyagtorfHdHsaAA2kectcyeUJQ9TOzEdxhbrGBehtGyPYGI1gq+seQsTi
f2IvftJNH8I4b+gwBI8hLE6kuZ6spC+VtWSV8K1LTboDgkroPcdQ6oOhiQW10WV6
XQzwDtFvok03O7yWtaA5mvb1fVgffXCXlytCET3ibiaEtyMblnVnOul7x98MHf6E
p1S2OLuu5Xl63yTXTwmvypppTVRMG+kNCJOWMwf+wz+c2nyfDKmXpaDFsACBKCQm
92c0vwoTZKLK2xB/cNrnHm2QmfAqGIiDaTURO6XNKtHTDEwlHfAvM9DzzVnA9WDL
q9bGAiAllUExnA4vuTMSsFHrUSnEfoF3+4xpeyZqy3+QWNbnwn5q8XmJWE/jWtLb
7lmG0DL39kY6IyKupTWjoBtZVLFLGTT/1Xt9KVKbQRhCCRp26aRchVzqKe/neYDq
roC9JLqdcLeE+UEXCGBR+WL8FrAmKZmeRN0mGJwVdTJJZXOOkKE=
=uF86
-----END PGP SIGNATURE-----

```
