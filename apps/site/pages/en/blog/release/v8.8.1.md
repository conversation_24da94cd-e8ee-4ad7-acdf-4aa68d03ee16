---
date: '2017-10-26T00:18:49.008Z'
category: release
title: Node v8.8.1 (Current)
layout: blog-post
author: <PERSON>
---

### Notable Changes

- **net**:
  - Fix timeout with null handle issue. This is a regression in Node 8.8.0 [#16489](https://github.com/nodejs/node/pull/16489)

### Commits

- [[`db8c92fb42`](https://github.com/nodejs/node/commit/db8c92fb42)] - **doc**: fix spelling in v8.8.0 changelog (<PERSON><PERSON>) [#16477](https://github.com/nodejs/node/pull/16477)
- [[`c8396b8370`](https://github.com/nodejs/node/commit/c8396b8370)] - **doc**: remove loader hooks from unsupported features (<PERSON><PERSON><PERSON>) [#16465](https://github.com/nodejs/node/pull/16465)
- [[`2b0bb57055`](https://github.com/nodejs/node/commit/2b0bb57055)] - **doc**: fix wrong URL (<PERSON>) [#16470](https://github.com/nodejs/node/pull/16470)
- [[`9ffc32974e`](https://github.com/nodejs/node/commit/9ffc32974e)] - **doc**: fix typo in changelog for 8.8.0 (Alec Perkins) [#16462](https://github.com/nodejs/node/pull/16462)
- [[`7facaa5031`](https://github.com/nodejs/node/commit/7facaa5031)] - **doc**: fix missing newline character (Daijiro Wachi) [#16447](https://github.com/nodejs/node/pull/16447)
- [[`16eb7d3a5f`](https://github.com/nodejs/node/commit/16eb7d3a5f)] - **doc**: fix doc styles (Daijiro Wachi) [#16385](https://github.com/nodejs/node/pull/16385)
- [[`99fdc1d04f`](https://github.com/nodejs/node/commit/99fdc1d04f)] - **doc**: add recommendations for first timers (Refael Ackermann) [#16350](https://github.com/nodejs/node/pull/16350)
- [[`6fbef7f350`](https://github.com/nodejs/node/commit/6fbef7f350)] - **doc**: fix typo in zlib.md (Luigi Pinca) [#16480](https://github.com/nodejs/node/pull/16480)
- [[`655e017e40`](https://github.com/nodejs/node/commit/655e017e40)] - **net**: fix timeout with null handle (Anatoli Papirovski) [#16489](https://github.com/nodejs/node/pull/16489)
- [[`7fad10cc7e`](https://github.com/nodejs/node/commit/7fad10cc7e)] - **test**: make test-v8-serdes work without stdin (Anna Henningsen)
- [[`12dc06e3e1`](https://github.com/nodejs/node/commit/12dc06e3e1)] - **test**: call toLowerCase on the resolved module (Daniel Bevenius) [#16486](https://github.com/nodejs/node/pull/16486)
- [[`10894c3835`](https://github.com/nodejs/node/commit/10894c3835)] - **test**: allow for different nsswitch.conf settings (Daniel Bevenius) [#16378](https://github.com/nodejs/node/pull/16378)
- [[`2a53165aa0`](https://github.com/nodejs/node/commit/2a53165aa0)] - **test**: add missing assertion (cjihrig) [#15519](https://github.com/nodejs/node/pull/15519)

Windows 32-bit Installer: https://nodejs.org/dist/v8.8.1/node-v8.8.1-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v8.8.1/node-v8.8.1-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v8.8.1/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v8.8.1/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v8.8.1/node-v8.8.1.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v8.8.1/node-v8.8.1-darwin-x64.tar.gz \
Linux 32-bit Binary: https://nodejs.org/dist/v8.8.1/node-v8.8.1-linux-x86.tar.xz \
Linux 64-bit Binary: https://nodejs.org/dist/v8.8.1/node-v8.8.1-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v8.8.1/node-v8.8.1-linux-ppc64le.tar.xz \
Linux PPC BE 64-bit Binary: https://nodejs.org/dist/v8.8.1/node-v8.8.1-linux-ppc64.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v8.8.1/node-v8.8.1-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v8.8.1/node-v8.8.1-aix-ppc64.tar.gz \
SmartOS 32-bit Binary: https://nodejs.org/dist/v8.8.1/node-v8.8.1-sunos-x86.tar.xz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v8.8.1/node-v8.8.1-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v8.8.1/node-v8.8.1-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v8.8.1/node-v8.8.1-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v8.8.1/node-v8.8.1-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v8.8.1/node-v8.8.1.tar.gz \
Other release files: https://nodejs.org/dist/v8.8.1/ \
Documentation: https://nodejs.org/docs/v8.8.1/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

532ab5404a992e061639e9fb552818719c67167b065d831e203e896baa9a68b4  node-v8.8.1-aix-ppc64.tar.gz
bf208e29418fb3efc836d3d32b62b9162f0f0b36a0665abc0990f4e292cfc84b  node-v8.8.1-darwin-x64.tar.gz
d79e34f4679c0359ca63fefd8f9f907af2eb3ab05335291c77d25408afe5ec18  node-v8.8.1-darwin-x64.tar.xz
a23bf22300253de45f38791d639deecc30f525591f892f7781f233cb25c25291  node-v8.8.1-headers.tar.gz
d343e71eff5256fa0dbfffff62f65827d6318f0483cceb1f0a314d8ccfbfe0b2  node-v8.8.1-headers.tar.xz
bfd5293f23f51601decb3521a8fdc62fbc6633d6eab9d8c3f406cdd7da68fb96  node-v8.8.1-linux-arm64.tar.gz
ea8ad0286a31e7519e979fcf99e503845a95da640cd140be5cff418a68fa6263  node-v8.8.1-linux-arm64.tar.xz
473a321a5d602ab2b6d4f9be910be46af8bac4bf5c553a27471974c60b4d374e  node-v8.8.1-linux-armv6l.tar.gz
ab515fa4fe9fc20b7369816d4b106e028758382970f3c861e899b5a1f223e7a3  node-v8.8.1-linux-armv6l.tar.xz
3e8ea812913a5a45d721e70e0b0874eec78433ee6ba6a980a59abfecdde5fca1  node-v8.8.1-linux-armv7l.tar.gz
72815b162d9ff06339e286cbb9cdcbf64ff43af12852ede3f266e56d8750100a  node-v8.8.1-linux-armv7l.tar.xz
dc6f3004d84af58cb450ed3f7ffb24aa149f655263b0a9b50f7f8a3c6ba5f60b  node-v8.8.1-linux-ppc64le.tar.gz
7e86a9124af20cef48ddf07ed48a49c45de35a2097ca9f622278d4d069c545a2  node-v8.8.1-linux-ppc64le.tar.xz
6989a28f9c7938d515b44424541e82ba584c0da9529ed2d05da6a6b7383e876b  node-v8.8.1-linux-ppc64.tar.gz
139c6567181fa7d74f85792600b323a6f51977af490010911278a448ea8d8952  node-v8.8.1-linux-ppc64.tar.xz
b2c1fb082b41c23ffa180c75c7e50f4b6891efe93909905ce2ecf686dd4bb260  node-v8.8.1-linux-s390x.tar.gz
afa4f725973c956ea7975cce43bd608f078b0c8a25b25d57d76cdefd0a3b6560  node-v8.8.1-linux-s390x.tar.xz
df83beb05af3e7aee4d16b74dd6d05967f47ee4ab6d6789ca0ed7f2b22c22c92  node-v8.8.1-linux-x64.tar.gz
004bc95267ef5d5b928f560582f681a679bada2201bf221735a02f4956f67b09  node-v8.8.1-linux-x64.tar.xz
8d3b3919fb9b3ebc90ff25a27d27a4ce5ad3b2de44719a7e942763c6e973b02e  node-v8.8.1-linux-x86.tar.gz
369d1a74a2e1f705d2ef3c045308b4887a6c0a8123a4b24299c967699b402167  node-v8.8.1-linux-x86.tar.xz
0ae178a4af5480edbd57feaa933d1acec73c0e0985e91e01c0458972412004ee  node-v8.8.1.pkg
252838a733996d3b5208f40636a972860f387dc21613afc4f95ec8462c2fa028  node-v8.8.1-sunos-x64.tar.gz
a13a3a68291c7df7c912c5c410eed727245197859c7a4f9c81a8603ffdae3521  node-v8.8.1-sunos-x64.tar.xz
28b7a1149e36fb4e27db1246c08ff343402404e75b779e7f136935480c3bbec1  node-v8.8.1-sunos-x86.tar.gz
b01de88222e08cf97764c3df9f46d3238bd671376d0b883ffc9619837de2c2b8  node-v8.8.1-sunos-x86.tar.xz
1725bbbe623d6a13ee14522730dfc90eac1c9ebe9a0a8f4c3322a402dd7e75a2  node-v8.8.1.tar.gz
5ce2c47ab779992db49942f757a01cc5131db5cef4e0dd270e48151b0887b57a  node-v8.8.1.tar.xz
e719657803800ed3176a2147384fbfc6ba092cfec6a2d3c202aa8184af8222b6  node-v8.8.1-win-x64.7z
a7e60a1a5f46ef309cbe74e423c17e69dd0a573f0c92c9e325caade3388d192a  node-v8.8.1-win-x64.zip
bac81bae523ae68b32594f8af68401fb5bd95a0069c98572de056b95f581769c  node-v8.8.1-win-x86.7z
37f741b3c891afd9addfb316a8c1310870156e1ac3cee6b919eea341e83071f2  node-v8.8.1-win-x86.zip
928bf0c044db146df4dd03b388b2117f176a20e49a88ce89ee4ae717efae8757  node-v8.8.1-x64.msi
b8e2792147d99e095fdb7add83edbd903205cbe7e2d58303acb1728035e0d334  node-v8.8.1-x86.msi
699cacc56a2f87d74b72f6709e1cf1de22d416b8c0ad66e5e7c1b2f9cc3e92f3  win-x64/node.exe
628caeed05b251ec6cd745b8ff116e9164a8741949dbcb020191d907c736b8d9  win-x64/node.lib
ca24a8255c2deced814a1db5f5ecaf6c30757e506bf91db16c58cfc122225d85  win-x64/node_pdb.7z
842758c85e813b05a9c72f2a57c3cdfd6bba7ec1fe565431c7aafe12d508c783  win-x64/node_pdb.zip
b838c4a28282371226e6141b3f83992949264b4ba7751a70108ba23fa1f5d89b  win-x86/node.exe
718039bfbb7d2c8c3f6d4516e810160ef15b489bab44d381897353f9ee89d4c9  win-x86/node.lib
f1e0fb7541f82b5f1981c1d3e55f166ff6ffbb1ff5fa56804035c40e0b0dd495  win-x86/node_pdb.7z
fa3258270dfc3665baf8fa1271dbb8252a9abd60329174ac8746ef758de65e38  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----
Comment: GPGTools - https://gpgtools.org

iQIcBAEBCAAGBQJZ8SYDAAoJEHQ0OQvb6bnF974P/39IBIFuNcRxQjOKVlRHLnWI
TZMw68mxMxSdph/E/5loR0syItvF3s98nlydVecu3dnI1IVPTsanz/+Z9Rx8ttCl
EnzVYxhcxX0ndA+T2MpN8Rp5HZY06iktU+nPNRNRwDMNZPgifmBlAVVAY2S4ZeS1
sfOuhXuQbVlzS4ha/hJUEI3pFY1KAiWIa6WVPx5oNoGT7JJRDvybVMK3WU68xi2b
GzcuwIMw8D7rhm+Mc++RVgGPCBrycp/HRfPiTrLH3/kSjRJh+WLBGNwYEMjj32Oz
ZqdDz3Axj3scDdA/0aOp4xgLIC0ufi6y5wSqY8M2C0iAZLp5RKdnNF93gqjT5hzt
bPcJtIwRNFyYj9WvB3lPpUfGhH+1/II2wdBk+BRTe9A8fZTn8WmGnE/LO0GT2CIB
8H2HQpa4ZXSPh52vZDsIm4Q9osV4f0AvAuF5jTz/z465zoz+yi2rambelv39s10t
lWIAjxTtOrLEScK8xsH1ClOZGE6cWU9JUgSzEMcqvzla1Js5UiIHyHJM7lqqb3AZ
vMNg/2r1yHKeQv/TZr4EoaPGw5tRkXJrHAJ9BP8fZsoQxE1XGqAO4k0yRn6Aq62d
ApwYpiAWbxo4STQ7wUWocf1SCnXqvyCNw+zLPNyNj6ySDMSWZmlbJPfEjNtuy8sR
6NDUbtBgPnqUajSIJsgi
=9H3X
-----END PGP SIGNATURE-----

```
