---
date: '2021-03-30T14:18:10.540Z'
category: release
title: Node v12.22.0 (LTS)
layout: blog-post
author: <PERSON>
---

### Notable changes

#### The legacy HTTP parser is runtime deprecated

The legacy HTTP parser, selected by the `--http-parser=legacy` command line
option, is deprecated with the pending End-of-Life of Node.js 10.x (where it
is the only HTTP parser implementation provided) at the end of April 2021. It
will now warn on use but otherwise continue to function and may be removed in
a future Node.js 12.x release.

The default HTTP parser based on llhttp is not affected. By default it is
stricter than the now deprecated legacy HTTP parser. If interoperability with
HTTP implementations that send invalid HTTP headers is required, the HTTP
parser can be started in a less secure mode with the
[`--insecure-http-parser`](https://nodejs.org/docs/latest-v12.x/api/cli.html#cli_insecure_http_parser)
command line option.

Contributed by <PERSON> [#37603](https://github.com/nodejs/node/pull/37603).

#### ES Modules

ES Modules are now considered stable.

Contributed by <PERSON> [#35781](https://github.com/nodejs/node/pull/35781)

#### node-api

Updated to node-api version 8 and added an experimental API to allow retrieval of the add-on file name.

Contributed by Gabriel Schulhof [#37652](https://github.com/nodejs/node/pull/37652) and [#37195](https://github.com/nodejs/node/pull/37195).

#### New API's to control code coverage data collection

`v8.stopCoverage()` and `v8.takeCoverage()` have been added.

Contributed by Joyee Cheung [#33807](https://github.com/nodejs/node/pull/33807).

#### New API to monitor event loop utilization by Worker threads

`worker.performance.eventLoopUtilization()` has been added.

Contributed by Trevor Norris [#35664](https://github.com/nodejs/node/pull/35664).

### Commits

- [[`1872625990`](https://github.com/nodejs/node/commit/1872625990)] - **(SEMVER-MINOR)** **deps**: update to cjs-module-lexer@1.1.0 (Guy Bedford) [#37712](https://github.com/nodejs/node/pull/37712)
- [[`dfa04d9035`](https://github.com/nodejs/node/commit/dfa04d9035)] - **deps**: V8: cherry-pick beebee4f80ff (Peter Marshall) [#37293](https://github.com/nodejs/node/pull/37293)
- [[`bf8733fe22`](https://github.com/nodejs/node/commit/bf8733fe22)] - **doc**: mark modules implementation as stable (Guy Bedford) [#35781](https://github.com/nodejs/node/pull/35781)
- [[`0a35d49f56`](https://github.com/nodejs/node/commit/0a35d49f56)] - **_Revert_** "**embedding**: make Stop() stop Workers" (Anna Henningsen) [#32623](https://github.com/nodejs/node/pull/32623)
- [[`a0b610450a`](https://github.com/nodejs/node/commit/a0b610450a)] - **(SEMVER-MINOR)** **http**: runtime deprecate legacy HTTP parser (Beth Griggs) [#37603](https://github.com/nodejs/node/pull/37603)
- [[`2da24ac302`](https://github.com/nodejs/node/commit/2da24ac302)] - **lib**: add URI handling functions to primordials (Antoine du Hamel) [#37394](https://github.com/nodejs/node/pull/37394)
- [[`7b0ed4ba92`](https://github.com/nodejs/node/commit/7b0ed4ba92)] - **module**: improve support of data: URLs (Antoine du Hamel) [#37392](https://github.com/nodejs/node/pull/37392)
- [[`93dd799a86`](https://github.com/nodejs/node/commit/93dd799a86)] - **(SEMVER-MINOR)** **node-api**: define version 8 (Gabriel Schulhof) [#37652](https://github.com/nodejs/node/pull/37652)
- [[`f5692093d3`](https://github.com/nodejs/node/commit/f5692093d3)] - **(SEMVER-MINOR)** **node-api**: allow retrieval of add-on file name (Gabriel Schulhof) [#37195](https://github.com/nodejs/node/pull/37195)
- [[`6cef0e3678`](https://github.com/nodejs/node/commit/6cef0e3678)] - **src,test**: add regression test for nested Worker termination (Anna Henningsen) [#32623](https://github.com/nodejs/node/pull/32623)
- [[`364bf03a68`](https://github.com/nodejs/node/commit/364bf03a68)] - **test**: fix races in test-performance-eventlooputil (Gerhard Stoebich) [#36028](https://github.com/nodejs/node/pull/36028)
- [[`d7a4ccdf09`](https://github.com/nodejs/node/commit/d7a4ccdf09)] - **test**: correct test-worker-eventlooputil (Gerhard Stoebich) [#35891](https://github.com/nodejs/node/pull/35891)
- [[`0f6d44500c`](https://github.com/nodejs/node/commit/0f6d44500c)] - **test**: add cpu-profiler-crash test (Santiago Gimeno) [#37293](https://github.com/nodejs/node/pull/37293)
- [[`86f34ee18c`](https://github.com/nodejs/node/commit/86f34ee18c)] - **(SEMVER-MINOR)** **v8**: implement v8.stopCoverage() (Joyee Cheung) [#33807](https://github.com/nodejs/node/pull/33807)
- [[`8ddea3f16d`](https://github.com/nodejs/node/commit/8ddea3f16d)] - **(SEMVER-MINOR)** **v8**: implement v8.takeCoverage() (Joyee Cheung) [#33807](https://github.com/nodejs/node/pull/33807)
- [[`eec7542781`](https://github.com/nodejs/node/commit/eec7542781)] - **(SEMVER-MINOR)** **worker**: add eventLoopUtilization() (Trevor Norris) [#35664](https://github.com/nodejs/node/pull/35664)

Windows 32-bit Installer: https://nodejs.org/dist/v12.22.0/node-v12.22.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v12.22.0/node-v12.22.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v12.22.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v12.22.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v12.22.0/node-v12.22.0.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v12.22.0/node-v12.22.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v12.22.0/node-v12.22.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v12.22.0/node-v12.22.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v12.22.0/node-v12.22.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v12.22.0/node-v12.22.0-aix-ppc64.tar.gz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v12.22.0/node-v12.22.0-sunos-x64.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v12.22.0/node-v12.22.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v12.22.0/node-v12.22.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v12.22.0/node-v12.22.0.tar.gz \
Other release files: https://nodejs.org/dist/v12.22.0/ \
Documentation: https://nodejs.org/docs/v12.22.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

64482b90fb13ca4be3a40386c958a41e49f6c915d3807d14d797bf101d363621  node-v12.22.0-aix-ppc64.tar.gz
7f72fd468cd00cf562c8fe2ea8b5e7a3b68027e8454e432db9ffbdd967bf420c  node-v12.22.0-darwin-x64.tar.gz
56eb67ecd3bdbcd48823aa20ac379e71473037569ec77638444069ca836baa44  node-v12.22.0-darwin-x64.tar.xz
7c1424543ae16e1e15d8a4d95fbb99cf4ae7c943c4aa0bf0be3751a883f38738  node-v12.22.0-headers.tar.gz
aad985525806713e4e88e67cf22ee937da3281a15c62647aa39ae8f0a27e1512  node-v12.22.0-headers.tar.xz
844d0ea80f0b71b015800d2089fe13a0dee1dd46b2957c458d06a5231bf6ac0b  node-v12.22.0-linux-arm64.tar.gz
7c784d17e2e4f72afe79df253d893afd8b73396d3a28a7709214673fd8d6fa87  node-v12.22.0-linux-arm64.tar.xz
942d71744f4001b11ac1be5a3093afdcd06509ab530fcf58f8bdaf16f3ee69c5  node-v12.22.0-linux-armv7l.tar.gz
09a9ab7188214b835456ec8f1248c7d820d945f87d2f6f0a74899b877312000d  node-v12.22.0-linux-armv7l.tar.xz
437fa2f1a2caae09120a1bf9a20544368391bfea382f1e485b440f2117febfeb  node-v12.22.0-linux-ppc64le.tar.gz
a509c5e0fc56c0c0368f8e1d0dfa017739695b88f08b85cc2b6ab28aafa92695  node-v12.22.0-linux-ppc64le.tar.xz
060bab6c4acf323cb66f8f2f23474d7158062e18daf249cc0b2d543413d7f1d5  node-v12.22.0-linux-s390x.tar.gz
dea901d4b14ada86bdc1eb9b8ff8dfd2204c912309dc6c3f54fb7beef61ee3cf  node-v12.22.0-linux-s390x.tar.xz
d941cb38b023a1c53a629c49425105f68069937569edd72c6fafab2221fc4533  node-v12.22.0-linux-x64.tar.gz
c5c9dc788ec08785203d7b02333aab393bd648ec4a8bb2efb9d56c67d24eba70  node-v12.22.0-linux-x64.tar.xz
2164706d8051ca3920036db45f901bb28b0b3e92dd3b98814791ec8751c4a633  node-v12.22.0.pkg
f2f8b3134fc343e635e6d3ccc37a6437e83d46125f707ed082ffbe43f4c7ce4b  node-v12.22.0-sunos-x64.tar.gz
37ef6028e13b58e93813cfee54e5668987d6f8bc2aae48c446794a30cc03194c  node-v12.22.0-sunos-x64.tar.xz
a65d108d824e956de0ab7c3e7f6763c70f6f5b85636dc4ee16404e5881b7d723  node-v12.22.0.tar.gz
df5c5f0ebad4889f5dd24e565eaaa9cbe2ca3274f78af469d9a20cc36f60ba23  node-v12.22.0.tar.xz
7261d78d96e80c265b1e3f459dc43fc664bff63e30d5c21b468ee6271876ac2c  node-v12.22.0-win-x64.7z
27dcfb4145bade7f03687d6ec620c55b0b5c966c583d02d29db36fb88bbd82c1  node-v12.22.0-win-x64.zip
5725c31de1ef07e5b09219fec87ae39e885172b2579d035315eb76a9dfcadcab  node-v12.22.0-win-x86.7z
0c880c1c0f5ff0755da21098b9fae7e76289525bdfdb73725fae7dc815ec2a9d  node-v12.22.0-win-x86.zip
51cfc0f8db30fc2bd45b6ac553867d3745aa73621661c099daaacb25d0d938bc  node-v12.22.0-x64.msi
62d3bbe291b146ff2856d4ef349377d7f20fea5ed7ce84be73c53193cab46329  node-v12.22.0-x86.msi
382561345ac811231b8502931496784b93d7f72a4084babedb6a3f8b27d9214d  win-x64/node.exe
28e5c24831deedbf4fb8a9560f2c4f95205479c589f54a9a53ec346f6a5cf8bf  win-x64/node.lib
dd6516bda3b3ffa953e30dfa79641f49290892d5fb7009e3b5f8f454c8753554  win-x64/node_pdb.7z
8efa373a135ef092e5a743064eef6423158190accdbcda4914fbed2a992e4a56  win-x64/node_pdb.zip
ba3752edef984118a77911f2ec78576fe039a8b0bca7dcb94efaff76c2272efb  win-x86/node.exe
8bbcf3b9305b83f54bd80f8ec19d4e237841bde5bfaeb2aec708c36daa6435f6  win-x86/node.lib
f34a56b04934e5382af0d288424e11a6d55e80894254835a8701102e089eafdd  win-x86/node_pdb.7z
80c580430404fb8cace7802e5c592123d1bd6bffa71ce7a6850358a254b05003  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEyC+jrhy+3Gvka5NgxDzsRcF6uTwFAmBjMiUACgkQxDzsRcF6
uTxj3BAAj6UhDhyUVTAzuaKVxJEfmnXeHdq3NNgWBkCk2H9ru6zJRa8nulzxFrmR
LR9/sDAr01of/Qca7q5ojeh2K5j66DbeG7RxvtwcISr9ASdZ7LmjcJCCie7fKnRz
eZn6qJ+6WXzD3lswI4jSwT8kIuNywAu7cMEkIcJM/OvLumpjx2euYjIdEE5BF80B
IwmkhyHvSiggPcAYBn1uxlDWHBxwdyF1F9u9MEGGmieIIloIMLeaDKRKdklSOBmr
ShwE2cnk+IEqJtQYaVEBLisayh8tkSdyxlbyowICkESICB+oiWMqF9dZotf6SFf6
I+SvfCQn3NH/LYG+gg06uAtna1hHUYU7DJ1QBGZtwff3NuVn0MrwZN3Qazj/ML8N
7ihL4twQM33U/QousCdXxaVVcW/J4+lurg0BKq0cijx/MvJRDF5+FDw/BLu3P7ul
3JA7+Ea0DquCt77oZpOra20TfrnjKmFUZfQjbqaG0hzQoEbJ8AcZlJeintOS2xT9
0Ri0Gp7JrCWpOfKobirjE/lIaV2Ehz9JpoPkwwFQbo62TPle7DIDihKWkshwuZj+
5X2K3rKjG9ff/0nZBkiWh6R7YGeMZCYscW71jTK7cIlKRggFIzUa+Od0x0975fQm
iDJMkx+Pmv2++GI3bcnZRxcCm+NeF6MryaN9xKNBblM6uoSemfQ=
=Wx4y
-----END PGP SIGNATURE-----

```
