---
date: '2021-07-29T16:52:20.191Z'
category: release
title: Node v14.17.4 (LTS)
layout: blog-post
author: <PERSON>
---

### Notable Changes

- **CVE-2021-22930**: Use after free on close http2 on stream canceling (High)
  - Node.js is vulnerable to a use after free attack where an attacker might be able to exploit the memory corruption, to change process behavior. You can read more about it in https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2021-22930

This releases also fixes some regressions with internationalization introduced by the ICU updates in Node.js 14.17.0 and 14.17.1.

### Commits

- [[`86477b2b53`](https://github.com/nodejs/node/commit/86477b2b53)] - **benchmark**: output JSON-compatible numbers (<PERSON><PERSON><PERSON>) [#38778](https://github.com/nodejs/node/pull/38778)
- [[`f9693cf0a0`](https://github.com/nodejs/node/commit/f9693cf0a0)] - **benchmark**: fix http elapsed time (<PERSON>) [#38743](https://github.com/nodejs/node/pull/38743)
- [[`1ab4f81abc`](https://github.com/nodejs/node/commit/1ab4f81abc)] - **build**: fix building with external builtins (Momtchil Momtchev) [#39091](https://github.com/nodejs/node/pull/39091)
- [[`a657f250f1`](https://github.com/nodejs/node/commit/a657f250f1)] - **build**: reconfigure when gyp files change on Windows (Joyee Cheung) [#39066](https://github.com/nodejs/node/pull/39066)
- [[`6962c647d6`](https://github.com/nodejs/node/commit/6962c647d6)] - **_Revert_** "**build**: work around bug in MSBuild v16.10.0" (Michaël Zasso) [#38977](https://github.com/nodejs/node/pull/38977)
- [[`069cf59e56`](https://github.com/nodejs/node/commit/069cf59e56)] - **build**: make build-addons errors fail the build (Richard Lau) [#38983](https://github.com/nodejs/node/pull/38983)
- [[`d341561ae0`](https://github.com/nodejs/node/commit/d341561ae0)] - **build**: fix commit-queue default branch (Mary Marchini) [#38998](https://github.com/nodejs/node/pull/38998)
- [[`0736dd833a`](https://github.com/nodejs/node/commit/0736dd833a)] - **build**: don't pass python override to V8 build (Richard Lau) [#38969](https://github.com/nodejs/node/pull/38969)
- [[`49a000683a`](https://github.com/nodejs/node/commit/49a000683a)] - **build**: correct Xcode spelling in .gitignore (bl-ue) [#38895](https://github.com/nodejs/node/pull/38895)
- [[`1ffbe3d5da`](https://github.com/nodejs/node/commit/1ffbe3d5da)] - **build**: remove outdated dont-land-on-v6.x label (Michaël Zasso) [#38886](https://github.com/nodejs/node/pull/38886)
- [[`7f53a0b349`](https://github.com/nodejs/node/commit/7f53a0b349)] - **build**: add lto build to CI (Jiawen Geng) [#38567](https://github.com/nodejs/node/pull/38567)
- [[`a6f8ba8f0c`](https://github.com/nodejs/node/commit/a6f8ba8f0c)] - **build**: allow LTO with Clang 3.9.1+ (Jesse Chan) [#38751](https://github.com/nodejs/node/pull/38751)
- [[`b5b1d1fb79`](https://github.com/nodejs/node/commit/b5b1d1fb79)] - **build**: replace non-POSIX test -a|o (Issam E. Maghni) [#38731](https://github.com/nodejs/node/pull/38731)
- [[`fc2b1ec308`](https://github.com/nodejs/node/commit/fc2b1ec308)] - **child_process**: refactor to use `validateBoolean` (Qingyu Deng) [#38927](https://github.com/nodejs/node/pull/38927)
- [[`55ea29eedd`](https://github.com/nodejs/node/commit/55ea29eedd)] - **child_process**: retain reference to data with advanced serialization (Anna Henningsen) [#38728](https://github.com/nodejs/node/pull/38728)
- [[`716ee1531c`](https://github.com/nodejs/node/commit/716ee1531c)] - **debugger**: rename internal library for clarity (Rich Trott) [#39080](https://github.com/nodejs/node/pull/39080)
- [[`b7ee9d8287`](https://github.com/nodejs/node/commit/b7ee9d8287)] - **debugger**: use ERR_DEBUGGER_STARTUP_ERROR in \_inspect.js (Rich Trott) [#39024](https://github.com/nodejs/node/pull/39024)
- [[`5d4d23dcf3`](https://github.com/nodejs/node/commit/5d4d23dcf3)] - **debugger**: use error codes in debugger REPL (Rich Trott) [#39024](https://github.com/nodejs/node/pull/39024)
- [[`a3991d7c18`](https://github.com/nodejs/node/commit/a3991d7c18)] - **debugger**: use ERR_DEBUGGER_ERROR in debugger client (Rich Trott) [#39024](https://github.com/nodejs/node/pull/39024)
- [[`052e1c5385`](https://github.com/nodejs/node/commit/052e1c5385)] - **debugger**: removed unused function argument (Rich Trott) [#38850](https://github.com/nodejs/node/pull/38850)
- [[`f9a4dcb30c`](https://github.com/nodejs/node/commit/f9a4dcb30c)] - **debugger**: refactor `inspect_repl` to use primordials (Antoine du Hamel) [#38551](https://github.com/nodejs/node/pull/38551)
- [[`ad8056659f`](https://github.com/nodejs/node/commit/ad8056659f)] - **debugger**: refactor to use internal modules (Antoine du Hamel) [#38550](https://github.com/nodejs/node/pull/38550)
- [[`b5724a1984`](https://github.com/nodejs/node/commit/b5724a1984)] - **debugger**: disable only the lint rules required by current file state (Rich Trott) [#38529](https://github.com/nodejs/node/pull/38529)
- [[`34659f2b7a`](https://github.com/nodejs/node/commit/34659f2b7a)] - **debugger**: avoid non-ASCII char in code file (Rich Trott) [#38529](https://github.com/nodejs/node/pull/38529)
- [[`ae90756582`](https://github.com/nodejs/node/commit/ae90756582)] - **debugger**: wrap lines longer than 80 chars (Rich Trott) [#38529](https://github.com/nodejs/node/pull/38529)
- [[`b30ff35a36`](https://github.com/nodejs/node/commit/b30ff35a36)] - **debugger**: align message with Node.js standard (Rich Trott) [#38400](https://github.com/nodejs/node/pull/38400)
- [[`d74d67f207`](https://github.com/nodejs/node/commit/d74d67f207)] - **debugger**: remove unnecessary boilerplate copyright comment (Rich Trott) [#38952](https://github.com/nodejs/node/pull/38952)
- [[`e58f938ab3`](https://github.com/nodejs/node/commit/e58f938ab3)] - **debugger**: enable linter on `internal/inspector/inspect_client` (Antoine du Hamel) [#38417](https://github.com/nodejs/node/pull/38417)
- [[`249acd5e69`](https://github.com/nodejs/node/commit/249acd5e69)] - **debugger**: reduce scope of eslint disable comment (Rich Trott) [#38946](https://github.com/nodejs/node/pull/38946)
- [[`0ef5e088c0`](https://github.com/nodejs/node/commit/0ef5e088c0)] - **debugger**: revise async iterator usage to comply with lint rules (Rich Trott) [#38847](https://github.com/nodejs/node/pull/38847)
- [[`79bfb0416b`](https://github.com/nodejs/node/commit/79bfb0416b)] - **debugger**: wait for V8 debugger to be enabled (Michaël Zasso) [#38811](https://github.com/nodejs/node/pull/38811)
- [[`721edeffd3`](https://github.com/nodejs/node/commit/721edeffd3)] - **debugger**: refactor `internal/inspector/_inspect` to use more primordials (Antoine du Hamel) [#38406](https://github.com/nodejs/node/pull/38406)
- [[`21ecee1b4b`](https://github.com/nodejs/node/commit/21ecee1b4b)] - **debugger**: add usage example for `--port` (Rafael Gonzaga) [#38400](https://github.com/nodejs/node/pull/38400)
- [[`cde72213d1`](https://github.com/nodejs/node/commit/cde72213d1)] - **_Revert_** "**debugger**: rename internal library for clarity" (Antoine du Hamel) [#39446](https://github.com/nodejs/node/pull/39446)
- [[`4c2b813799`](https://github.com/nodejs/node/commit/4c2b813799)] - **debugger**: rename internal library for clarity (Rich Trott) [#39080](https://github.com/nodejs/node/pull/39080)
- [[`61da371251`](https://github.com/nodejs/node/commit/61da371251)] - **debugger**: apply automatic lint fixes for inspect_repl.js (Rich Trott) [#38411](https://github.com/nodejs/node/pull/38411)
- [[`8dd1f70fe3`](https://github.com/nodejs/node/commit/8dd1f70fe3)] - **debugger**: apply automatic lint fixes for \_inspect.js (Rich Trott) [#38411](https://github.com/nodejs/node/pull/38411)
- [[`fb0ab4c034`](https://github.com/nodejs/node/commit/fb0ab4c034)] - **debugger**: removed unused function argument (Rich Trott) [#38850](https://github.com/nodejs/node/pull/38850)
- [[`9e28c6c946`](https://github.com/nodejs/node/commit/9e28c6c946)] - **debugger**: fix race condition/deadlock on initialization (Rich Trott) [#38161](https://github.com/nodejs/node/pull/38161)
- [[`a8924fa0fb`](https://github.com/nodejs/node/commit/a8924fa0fb)] - **debugger**: replace internal use of deprecated API (Rich Trott) [#38161](https://github.com/nodejs/node/pull/38161)
- [[`22afb7cbe6`](https://github.com/nodejs/node/commit/22afb7cbe6)] - **debugger**: allow longer time to connect (Rich Trott) [#38161](https://github.com/nodejs/node/pull/38161)
- [[`b172e6f436`](https://github.com/nodejs/node/commit/b172e6f436)] - **debugger**: accommodate line chunking in Windows (Rich Trott) [#38161](https://github.com/nodejs/node/pull/38161)
- [[`1da692185a`](https://github.com/nodejs/node/commit/1da692185a)] - **debugger**: fix inspect restart on Windows (Rich Trott) [#38161](https://github.com/nodejs/node/pull/38161)
- [[`0321c5b194`](https://github.com/nodejs/node/commit/0321c5b194)] - **debugger**: remove unused code (Rich Trott) [#38161](https://github.com/nodejs/node/pull/38161)
- [[`8bd2a3926a`](https://github.com/nodejs/node/commit/8bd2a3926a)] - **debugger**: move node-inspect to internal library (Rich Trott) [#38161](https://github.com/nodejs/node/pull/38161)
- [[`acf5279c39`](https://github.com/nodejs/node/commit/acf5279c39)] - **deps**: upgrade npm to 6.14.14 (Darcy Clarke) [#39553](https://github.com/nodejs/node/pull/39553)
- [[`4efefe02a8`](https://github.com/nodejs/node/commit/4efefe02a8)] - **deps**: V8: backport ae7bfb3f03b3 (Michaël Zasso) [#39051](https://github.com/nodejs/node/pull/39051)
- [[`5039f21396`](https://github.com/nodejs/node/commit/5039f21396)] - **deps**: V8: backport 16ffec97e5eb (Michaël Zasso) [#39051](https://github.com/nodejs/node/pull/39051)
- [[`9b69069f71`](https://github.com/nodejs/node/commit/9b69069f71)] - **deps**: V8: cherry-pick b0a7f5691113 (Michaël Zasso) [#39051](https://github.com/nodejs/node/pull/39051)
- [[`4213e97d26`](https://github.com/nodejs/node/commit/4213e97d26)] - **deps**: V8: cherry-pick 81181a8ad80a (thomasmichaelwallace) [#39187](https://github.com/nodejs/node/pull/39187)
- [[`ccecea5f72`](https://github.com/nodejs/node/commit/ccecea5f72)] - **deps**: restore minimum ICU version to 65 (Richard Lau) [#39068](https://github.com/nodejs/node/pull/39068)
- [[`7557e74cf4`](https://github.com/nodejs/node/commit/7557e74cf4)] - **deps**: V8: update build dependencies (Michaël Zasso) [#39244](https://github.com/nodejs/node/pull/39244)
- [[`a60a960406`](https://github.com/nodejs/node/commit/a60a960406)] - **deps**: V8: cherry-pick 895949419186 (Michaël Zasso) [#39244](https://github.com/nodejs/node/pull/39244)
- [[`7fdd6ecbb4`](https://github.com/nodejs/node/commit/7fdd6ecbb4)] - **deps**: V8: cherry-pick 0b3a4ecf7083 (Michaël Zasso) [#39244](https://github.com/nodejs/node/pull/39244)
- [[`4be2e878b7`](https://github.com/nodejs/node/commit/4be2e878b7)] - **deps**: V8: cherry-pick 7c182bd65f42 (Michaël Zasso) [#39244](https://github.com/nodejs/node/pull/39244)
- [[`a83b01a4af`](https://github.com/nodejs/node/commit/a83b01a4af)] - **deps**: V8: cherry-pick 92e6d3317082 (Michaël Zasso) [#39244](https://github.com/nodejs/node/pull/39244)
- [[`17eb561184`](https://github.com/nodejs/node/commit/17eb561184)] - **deps**: V8: backport 1b1eda0876aa (Michaël Zasso) [#39244](https://github.com/nodejs/node/pull/39244)
- [[`04032fa1a3`](https://github.com/nodejs/node/commit/04032fa1a3)] - **doc**: remove references to deleted freenode channels (devsnek) [#39047](https://github.com/nodejs/node/pull/39047)
- [[`797bd73849`](https://github.com/nodejs/node/commit/797bd73849)] - **doc**: add missing parameter types (Voltrex) [#39013](https://github.com/nodejs/node/pull/39013)
- [[`e474e984e5`](https://github.com/nodejs/node/commit/e474e984e5)] - **doc**: clarify that only one Python version is required to build (bl-ue) [#38894](https://github.com/nodejs/node/pull/38894)
- [[`cd48ee71d9`](https://github.com/nodejs/node/commit/cd48ee71d9)] - **doc**: fixed typo in process.md (Derevianchenko Maksym) [#38941](https://github.com/nodejs/node/pull/38941)
- [[`41fcbad2b2`](https://github.com/nodejs/node/commit/41fcbad2b2)] - **doc**: add missing semis after classes (Darshan Sen) [#38931](https://github.com/nodejs/node/pull/38931)
- [[`b40529643b`](https://github.com/nodejs/node/commit/b40529643b)] - **doc**: mark util.inherits as legacy (Voltrex) [#38896](https://github.com/nodejs/node/pull/38896)
- [[`b2d836b1ea`](https://github.com/nodejs/node/commit/b2d836b1ea)] - **doc**: clarify when `readable._read(...)` is called (Shaun Keys) [#38726](https://github.com/nodejs/node/pull/38726)
- [[`e36d2a6d6a`](https://github.com/nodejs/node/commit/e36d2a6d6a)] - **doc**: fixed typo in n-api.md (julianjany) [#38822](https://github.com/nodejs/node/pull/38822)
- [[`b4f60bb523`](https://github.com/nodejs/node/commit/b4f60bb523)] - **doc**: use "Long Term Support" in collaborator guide (Rich Trott) [#38841](https://github.com/nodejs/node/pull/38841)
- [[`7a9850a5fb`](https://github.com/nodejs/node/commit/7a9850a5fb)] - **doc**: use "Long Term Support" in technical values doc (Rich Trott) [#38841](https://github.com/nodejs/node/pull/38841)
- [[`dfe9698db0`](https://github.com/nodejs/node/commit/dfe9698db0)] - **doc**: use "Long Term Support" in README (Philip) [#38839](https://github.com/nodejs/node/pull/38839)
- [[`8699e622fc`](https://github.com/nodejs/node/commit/8699e622fc)] - **doc**: fix grammar in `fs.md` (yotamselementor) [#38818](https://github.com/nodejs/node/pull/38818)
- [[`826ae9b2e2`](https://github.com/nodejs/node/commit/826ae9b2e2)] - **doc**: fixup code sample in http.md (TodorTotev) [#38776](https://github.com/nodejs/node/pull/38776)
- [[`8049b69b7f`](https://github.com/nodejs/node/commit/8049b69b7f)] - **doc**: document null target pattern (Guy Bedford) [#38724](https://github.com/nodejs/node/pull/38724)
- [[`4d9129eb71`](https://github.com/nodejs/node/commit/4d9129eb71)] - **doc**: update code examples for `node:url` module (fisker Cheung) [#38645](https://github.com/nodejs/node/pull/38645)
- [[`2ff671e4c4`](https://github.com/nodejs/node/commit/2ff671e4c4)] - **doc,url**: clarify domainTo\* when built without ICU (Darshan Sen) [#38789](https://github.com/nodejs/node/pull/38789)
- [[`9b993edca8`](https://github.com/nodejs/node/commit/9b993edca8)] - **errors**: add ERR_DEBUGGER_STARTUP_ERROR (Rich Trott) [#39024](https://github.com/nodejs/node/pull/39024)
- [[`cfccf13e84`](https://github.com/nodejs/node/commit/cfccf13e84)] - **errors**: add ERR_DEBUGGER_ERROR (Rich Trott) [#39024](https://github.com/nodejs/node/pull/39024)
- [[`bb9a9adc2b`](https://github.com/nodejs/node/commit/bb9a9adc2b)] - **errors**: don't rekey on primitive type (Benjamin Coe) [#39025](https://github.com/nodejs/node/pull/39025)
- [[`d48b91ea2b`](https://github.com/nodejs/node/commit/d48b91ea2b)] - **http2**: on receiving rst_stream with cancel code add it to pending list (Akshay K) [#39423](https://github.com/nodejs/node/pull/39423)
- [[`d8cc2fffd6`](https://github.com/nodejs/node/commit/d8cc2fffd6)] - **lib**: add primordials.SafeArrayIterator (Antoine du Hamel) [#36532](https://github.com/nodejs/node/pull/36532)
- [[`e3223edb89`](https://github.com/nodejs/node/commit/e3223edb89)] - **lib**: harden lint checks for globals (Antoine du Hamel) [#38419](https://github.com/nodejs/node/pull/38419)
- [[`d4f96bb926`](https://github.com/nodejs/node/commit/d4f96bb926)] - **lib**: enforce using `primordials.globalThis` instead of `global` (Antoine du Hamel) [#38230](https://github.com/nodejs/node/pull/38230)
- [[`ea9003a559`](https://github.com/nodejs/node/commit/ea9003a559)] - **lib**: add `globalThis` to primordials (Antoine du Hamel) [#38211](https://github.com/nodejs/node/pull/38211)
- [[`097a7874d3`](https://github.com/nodejs/node/commit/097a7874d3)] - **lib**: remove semicolon in preparation for babel/eslint-parser update (Rich Trott) [#39094](https://github.com/nodejs/node/pull/39094)
- [[`199fe32cbc`](https://github.com/nodejs/node/commit/199fe32cbc)] - **lib**: make internal/options lazy (Joyee Cheung) [#38993](https://github.com/nodejs/node/pull/38993)
- [[`2bc2a232af`](https://github.com/nodejs/node/commit/2bc2a232af)] - **lib**: add JSDoc typings for child_process (Voltrex) [#38222](https://github.com/nodejs/node/pull/38222)
- [[`b0a1984d4d`](https://github.com/nodejs/node/commit/b0a1984d4d)] - **lib**: fix typos (bl-ue) [#38846](https://github.com/nodejs/node/pull/38846)
- [[`6c061d5f2c`](https://github.com/nodejs/node/commit/6c061d5f2c)] - **meta**: update label-pr-config (Michaël Zasso) [#38950](https://github.com/nodejs/node/pull/38950)
- [[`afb61786b9`](https://github.com/nodejs/node/commit/afb61786b9)] - **module**: fix legacy `node` specifier resolution to resolve `"main"` field (Antoine du Hamel) [#38979](https://github.com/nodejs/node/pull/38979)
- [[`cd3305a9e4`](https://github.com/nodejs/node/commit/cd3305a9e4)] - **node-api**: avoid SecondPassCallback crash (Michael Dawson) [#38899](https://github.com/nodejs/node/pull/38899)
- [[`e7f266e93d`](https://github.com/nodejs/node/commit/e7f266e93d)] - **src**: use SPrintF in ProcessEmitWarning (Darshan Sen) [#38758](https://github.com/nodejs/node/pull/38758)
- [[`43fe6c1d27`](https://github.com/nodejs/node/commit/43fe6c1d27)] - **src**: cleanup uv_fs_t regardless of success or not (legendecas) [#38996](https://github.com/nodejs/node/pull/38996)
- [[`dcfb182546`](https://github.com/nodejs/node/commit/dcfb182546)] - **src**: refactor to use locale functions (Darshan Sen) [#39014](https://github.com/nodejs/node/pull/39014)
- [[`bee477b000`](https://github.com/nodejs/node/commit/bee477b000)] - **src**: throw error in LoadBuiltinModuleSource when reading fails (Joyee Cheung) [#38904](https://github.com/nodejs/node/pull/38904)
- [[`ff7cc8f9ef`](https://github.com/nodejs/node/commit/ff7cc8f9ef)] - **src**: add not-weak DCHECK to PersistentToLocal::Strong (Anna Henningsen) [#38875](https://github.com/nodejs/node/pull/38875)
- [[`981217e48a`](https://github.com/nodejs/node/commit/981217e48a)] - **src**: replace `auto`s in node_api.cc (Khaidi Chu) [#38852](https://github.com/nodejs/node/pull/38852)
- [[`73e199d963`](https://github.com/nodejs/node/commit/73e199d963)] - **src**: fix typos (bl-ue) [#38845](https://github.com/nodejs/node/pull/38845)
- [[`2d32031724`](https://github.com/nodejs/node/commit/2d32031724)] - **src**: use HandleScope in StreamReq::Done() (Darshan Sen) [#38720](https://github.com/nodejs/node/pull/38720)
- [[`2c11d3ec0a`](https://github.com/nodejs/node/commit/2c11d3ec0a)] - **src**: remove commented code in `node_file.cc` (Juan José Arboleda) [#38693](https://github.com/nodejs/node/pull/38693)
- [[`846a138f54`](https://github.com/nodejs/node/commit/846a138f54)] - **src**: write named pipe info in diagnostic report (legendecas) [#38637](https://github.com/nodejs/node/pull/38637)
- [[`7d82200861`](https://github.com/nodejs/node/commit/7d82200861)] - **src**: replace `auto`s in node_contextify.cc (Khaidi Chu) [#38644](https://github.com/nodejs/node/pull/38644)
- [[`51da7d2048`](https://github.com/nodejs/node/commit/51da7d2048)] - **src,url**: separate some tables out of node_url.cc (Khaidi Chu) [#38988](https://github.com/nodejs/node/pull/38988)
- [[`45c2ea3b72`](https://github.com/nodejs/node/commit/45c2ea3b72)] - **test**: add NumberFormat resolvedOptions test (Richard Lau) [#39401](https://github.com/nodejs/node/pull/39401)
- [[`6b2fea38d1`](https://github.com/nodejs/node/commit/6b2fea38d1)] - **test**: move inspector-cli tests to sequential (Rich Trott) [#39079](https://github.com/nodejs/node/pull/39079)
- [[`6447cab7be`](https://github.com/nodejs/node/commit/6447cab7be)] - **test**: improve buffer coverage (Rongjian Zhang) [#38538](https://github.com/nodejs/node/pull/38538)
- [[`6f1862eab3`](https://github.com/nodejs/node/commit/6f1862eab3)] - **test**: fix name of variable in inspector-cli test (Tobias Nießen) [#38869](https://github.com/nodejs/node/pull/38869)
- [[`40093504bc`](https://github.com/nodejs/node/commit/40093504bc)] - **test**: fix typo (Houssem Chebab) [#39045](https://github.com/nodejs/node/pull/39045)
- [[`ab28f9b9a1`](https://github.com/nodejs/node/commit/ab28f9b9a1)] - **test**: remove obsolete TLS test (Rich Trott) [#39001](https://github.com/nodejs/node/pull/39001)
- [[`b3b59953fe`](https://github.com/nodejs/node/commit/b3b59953fe)] - **test**: improve coverage of lib/events.js (Rongjian Zhang) [#38582](https://github.com/nodejs/node/pull/38582)
- [[`c99a09f05f`](https://github.com/nodejs/node/commit/c99a09f05f)] - **test**: http outgoing \_headers setter null (ycjcl868) [#38881](https://github.com/nodejs/node/pull/38881)
- [[`660a97b1d5`](https://github.com/nodejs/node/commit/660a97b1d5)] - **test**: suppress warning in test_environment.cc (Daniel Bevenius) [#38868](https://github.com/nodejs/node/pull/38868)
- [[`0cca16ac4c`](https://github.com/nodejs/node/commit/0cca16ac4c)] - **test**: improve coverage of fs internal utils (Rongjian Zhang) [#38746](https://github.com/nodejs/node/pull/38746)
- [[`fecad40f27`](https://github.com/nodejs/node/commit/fecad40f27)] - **test**: fix writefile with fd (Nitzan Uziely) [#38820](https://github.com/nodejs/node/pull/38820)
- [[`01f00faaa8`](https://github.com/nodejs/node/commit/01f00faaa8)] - **test**: simplify test-path-resolve.js (himself65) [#38671](https://github.com/nodejs/node/pull/38671)
- [[`504bfd7a88`](https://github.com/nodejs/node/commit/504bfd7a88)] - **test**: improve coverage for `question` in readline (Qingyu Deng) [#38799](https://github.com/nodejs/node/pull/38799)
- [[`eb91932e77`](https://github.com/nodejs/node/commit/eb91932e77)] - **test**: os, replace custom flatten method with built-in Array.flat (Wael Almattar) [#38770](https://github.com/nodejs/node/pull/38770)
- [[`aeea252b96`](https://github.com/nodejs/node/commit/aeea252b96)] - **test**: improve coverage of lib/\_http_outgoing.js (Rongjian Zhang) [#38734](https://github.com/nodejs/node/pull/38734)
- [[`e265d8ee1b`](https://github.com/nodejs/node/commit/e265d8ee1b)] - **test**: give js-native-api tests consistent names (Gabriel Schulhof) [#38692](https://github.com/nodejs/node/pull/38692)
- [[`99fd8bfc6a`](https://github.com/nodejs/node/commit/99fd8bfc6a)] - **test**: fix flaky inspector-cli tests when breakpionts are restored (Rich Trott) [#38431](https://github.com/nodejs/node/pull/38431)
- [[`4d3a1fad28`](https://github.com/nodejs/node/commit/4d3a1fad28)] - **test**: extend timeout on debugger tests for slower machines (Rich Trott) [#38161](https://github.com/nodejs/node/pull/38161)
- [[`dd2642b5db`](https://github.com/nodejs/node/commit/dd2642b5db)] - **test**: fix comment typo (Rich Trott) [#38161](https://github.com/nodejs/node/pull/38161)
- [[`193ea8fd91`](https://github.com/nodejs/node/commit/193ea8fd91)] - **test**: fix test-inspector-cli-address (Rich Trott) [#38161](https://github.com/nodejs/node/pull/38161)
- [[`a62826bbe6`](https://github.com/nodejs/node/commit/a62826bbe6)] - **test,debugger**: migrate node-inspect tests to core (Rich Trott) [#38161](https://github.com/nodejs/node/pull/38161)
- [[`ab45ace9bd`](https://github.com/nodejs/node/commit/ab45ace9bd)] - **tools**: update babel-eslint-parser to 7.14.5 (Rich Trott) [#39094](https://github.com/nodejs/node/pull/39094)
- [[`b8e63b3c08`](https://github.com/nodejs/node/commit/b8e63b3c08)] - **tools**: update ESLint to 7.29.0 (Rich Trott) [#39083](https://github.com/nodejs/node/pull/39083)
- [[`54a250e79c`](https://github.com/nodejs/node/commit/54a250e79c)] - **tools**: update doctool dependencies, migrate to ESM (Michaël Zasso) [#38966](https://github.com/nodejs/node/pull/38966)
- [[`443db64eed`](https://github.com/nodejs/node/commit/443db64eed)] - **tools**: avoid crashing CQ when git push fails (Antoine du Hamel) [#36861](https://github.com/nodejs/node/pull/36861)
- [[`547f88b149`](https://github.com/nodejs/node/commit/547f88b149)] - **tools**: fix typo in commit-queue.sh (bl-ue) [#39000](https://github.com/nodejs/node/pull/39000)
- [[`1023433a81`](https://github.com/nodejs/node/commit/1023433a81)] - **tools**: update ESLint to 7.28.0 (Luigi Pinca) [#38955](https://github.com/nodejs/node/pull/38955)
- [[`9b4ae8fbb0`](https://github.com/nodejs/node/commit/9b4ae8fbb0)] - **tools**: bump remark-preset-lint-node to 2.3.0 (Rich Trott) [#38910](https://github.com/nodejs/node/pull/38910)
- [[`2ad0719e86`](https://github.com/nodejs/node/commit/2ad0719e86)] - **tools**: refloat 7 Node.js patches to cpplint.py (Rich Trott) [#38851](https://github.com/nodejs/node/pull/38851)
- [[`b7686d0c1e`](https://github.com/nodejs/node/commit/b7686d0c1e)] - **tools**: bump cpplint to 1.5.5 (Rich Trott) [#38851](https://github.com/nodejs/node/pull/38851)
- [[`2ec7c9de57`](https://github.com/nodejs/node/commit/2ec7c9de57)] - **tools**: remove exception for Node.js 8 and earlier (Rich Trott) [#38840](https://github.com/nodejs/node/pull/38840)
- [[`1dc71da302`](https://github.com/nodejs/node/commit/1dc71da302)] - **tools**: update setup-node to setup-node@v2 (pengjie) [#38825](https://github.com/nodejs/node/pull/38825)
- [[`fc219d862c`](https://github.com/nodejs/node/commit/fc219d862c)] - **tools**: remove node-inspect from license (Rich Trott) [#38161](https://github.com/nodejs/node/pull/38161)
- [[`4bb0bd0f0e`](https://github.com/nodejs/node/commit/4bb0bd0f0e)] - **tools,doc**: forbid CJS globals in ESM code snippets (Antoine du Hamel) [#38889](https://github.com/nodejs/node/pull/38889)
- [[`58154ce426`](https://github.com/nodejs/node/commit/58154ce426)] - **typings**: add JSDoc typings for https (Voltrex) [#38589](https://github.com/nodejs/node/pull/38589)
- [[`6ea1368a67`](https://github.com/nodejs/node/commit/6ea1368a67)] - **typings**: add JSDoc typings for events (Voltrex) [#38712](https://github.com/nodejs/node/pull/38712)
- [[`b6942a6138`](https://github.com/nodejs/node/commit/b6942a6138)] - **url,src**: simplify ipv6 logic by using uv_inet_pton (Khaidi Chu) [#38842](https://github.com/nodejs/node/pull/38842)
- [[`dd00547ada`](https://github.com/nodejs/node/commit/dd00547ada)] - **vm**: use missing validator (Voltrex) [#38935](https://github.com/nodejs/node/pull/38935)
- [[`2c28e00685`](https://github.com/nodejs/node/commit/2c28e00685)] - **worker**: do not look up context twice in PostMessage (Anna Henningsen) [#38784](https://github.com/nodejs/node/pull/38784)

Windows 32-bit Installer: https://nodejs.org/dist/v14.17.4/node-v14.17.4-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v14.17.4/node-v14.17.4-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v14.17.4/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v14.17.4/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v14.17.4/node-v14.17.4.pkg \
macOS Intel 64-bit Binary: https://nodejs.org/dist/v14.17.4/node-v14.17.4-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v14.17.4/node-v14.17.4-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v14.17.4/node-v14.17.4-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v14.17.4/node-v14.17.4-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v14.17.4/node-v14.17.4-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v14.17.4/node-v14.17.4-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v14.17.4/node-v14.17.4-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v14.17.4/node-v14.17.4.tar.gz \
Other release files: https://nodejs.org/dist/v14.17.4/ \
Documentation: https://nodejs.org/docs/v14.17.4/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

58e4ca29b0585ebeb1dbf5a701d9959dda219b5bdf6d8363213f51a779d395da  node-v14.17.4-aix-ppc64.tar.gz
5c055a295e030cb789e2925b4c0647f7aaf461ffe5f2a08145c0605fb83ad4e0  node-v14.17.4-darwin-x64.tar.gz
f86bb831a371b3720e4d0037e4e77ffa427afdbd14e96d9fd16202fbbd84ce7d  node-v14.17.4-darwin-x64.tar.xz
24fa6a3925027980e32cfa2555d1cc9eca989db6c0890fe1e12e1c9f9ef4baa7  node-v14.17.4-headers.tar.gz
9515893f1f6b844b179120d95e34ee2edbd47741291456ab69913184bdb9368c  node-v14.17.4-headers.tar.xz
88b130c8f08a2baafb4e4c953ad46ba69cc60210da7d95c558c7ae3456beb825  node-v14.17.4-linux-arm64.tar.gz
4c42f31e7b52980e6bb930a7c2872e6e29533828c40623ba39e1c847e9ee6c89  node-v14.17.4-linux-arm64.tar.xz
e5452a8786ea018fe9c588ffe05ca4b4b66d6a7cda1f6352bda9bd0d0421e325  node-v14.17.4-linux-armv7l.tar.gz
df65ca9aea52b693b82638077a46218ae555160a20a8a5b0edd15ff0b3438c2b  node-v14.17.4-linux-armv7l.tar.xz
67dc73c42d08b5b365da37354568555e404aa902bf17dfe35f5b3ecbf699700b  node-v14.17.4-linux-ppc64le.tar.gz
255fffa3b2a78b86aa7dce7b65442bb2092d99be74d2f326f1dd66f7a1931b5d  node-v14.17.4-linux-ppc64le.tar.xz
b169c8b3821e3360be90bde075e28bf4632c5d36ce97d8c30b10411abc960ac8  node-v14.17.4-linux-s390x.tar.gz
3e086d99c3e303a05657c04053df9e31e46a7cdf0245b1022ddecd0fc0e14663  node-v14.17.4-linux-s390x.tar.xz
99cc7115a30fe62abf06145d57b314092c9bf27499da85413a12f50140199619  node-v14.17.4-linux-x64.tar.gz
db18c54ebe01974d46198b08729249acbb0dcdc9aea82b53eec913f8c56035c6  node-v14.17.4-linux-x64.tar.xz
aaf06036afcc730971e9048b72ea6c79659a1fcc15d810ed822d33f51c35c848  node-v14.17.4.pkg
70c75f21ac601ae9e0fd86bdfd4e13e4d302f42b4fafcd6d21804b043a571c36  node-v14.17.4.tar.gz
ae7bf4e784f8c8027ffa1e3757f37d2bd5925d0c48988c4d7f07e4515853cf2c  node-v14.17.4.tar.xz
0de71309336bc324bc5155867dc9d8d6337d83c1eed4777141ae83e967b3aca1  node-v14.17.4-win-x64.7z
d82a3ca777b4dccc97aa391eb483325cda731e0ae9b3a5669dbf34bb8defde6e  node-v14.17.4-win-x64.zip
67caaa209d2d938f763f1a9ab08b3e30c06b2f18bf5c5d90b1198d0ddbd35feb  node-v14.17.4-win-x86.7z
6564c13aa47240231eff9c28fdafa479dda3186fbc7e2bbc97bb5b791ccd0419  node-v14.17.4-win-x86.zip
e889da1ee06e576de4f31c3e6e0f12c73cfec495a53db4dd166fd58b0fea9f22  node-v14.17.4-x64.msi
296378f482fed803adfc0dd63870ebdd925adfadd0f2e0e04a3c2ccd2b16999e  node-v14.17.4-x86.msi
dcef8bbee862ffbf498327d2ab0b9e1ccfc412d8e2270cbc0e45b0e6a1cdca86  win-x64/node.exe
859ceb82ba9af9df5831bb67f45427bfb774aae22e7c0ee52623a3196ec0e1eb  win-x64/node.lib
bd9b1ff379588006a22d27b2cfbfa8e9a6291c4eb44ab4ec4819d971e56c0485  win-x64/node_pdb.7z
65ffd6a70fb9164d1a340683462edf64c52dd05f9363d7add276d79bcc92e93f  win-x64/node_pdb.zip
300c4e8ca527361eab0e9128dd15913cf9e4edd0d3b00c3d623ab925d5fa2f91  win-x86/node.exe
1078be47b9315c81aa3bb989c4bba8ee23e0da9e4854a44006decf45d578833e  win-x86/node.lib
e3c8ad2df1de6be3478479ae2f45a88402c423850dda7bf7aab1f76432f4efc9  win-x86/node_pdb.7z
c5a49448e192b41e8f93992055f773f5d3915f1ee908d8b1c596020e84aafe62  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEyC+jrhy+3Gvka5NgxDzsRcF6uTwFAmEC2xcACgkQxDzsRcF6
uTz1SA/9FMgXqtX3xlVsqkhFSQEtf/PkqcnOxPvQ164R7iVeffzD/9I0mFrAAD/Q
nfVQWol6r6aZ60PzvUw3MsDj1SZgUDxIH0PvnQRjNr98HG4G3JZuDJeEwUe2JyA+
k9jK2TeYZECwKXRVqJ48Ge2A7iYdz9JS64ZTsxXwSiJjOuF5P3jRN4Xp/w9/jGT9
aB3PC/PtbpQ2W7TAGgChRZyDI/YJQsSwQ8ZNw7qSjfKHyTzJAyUO30bBBuivyDP4
GiF5CM4dhkGE0/2+ickCm7A7bDnkrSEQkmtuUkT2aVQsx4nZr3O/Uh9+zZirxm23
8F9oVJPLirwnULUmEVr9ekCkdkli6jak7xgL/4TepupjN7Hm+SxPZ9yB5LJWGSWk
TL8M1MoGo2hAlmXr0WABL9lDJylEmqr2fxuK6Ik8zWs+qUEseIWgiTb1PvN7Tak3
b4ZIlq6aZduv6yZxOCuypUKMk5RPv7awpSSquO8pT4Z6V0ODs/Xsnm8qdy8Mm3AR
qW4b9ocQcrjba8Mmy97jjsLegGuAMtLUwwf8EZIVoQil7Xelp2k+gNo7Rz9VCcno
Di8sSBaKjD9aNiJ06meDiFJ97B5RwLKzU8pEeGY51oKZF5rcpoqKPZU4w1ruM8Q/
1n92BYY1Py3aZTdCrPTBDnEpLIBywpNcneFEFZpgvHX+D/1ujRw=
=Tzuk
-----END PGP SIGNATURE-----

```
