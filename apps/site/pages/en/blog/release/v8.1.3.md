---
date: '2017-06-29T07:05:09.997Z'
category: release
title: Node v8.1.3 (Current)
layout: blog-post
author: '<PERSON> & <PERSON>'
---

### Notable changes

- **Stream**
  Two regressions with the `stream` module have been fixed:
  - The `finish` event will now always be emitted after the `error` event
    if one is emitted:
    [[`0a9e96e86c`](https://github.com/nodejs/node/commit/0a9e96e86c)]
    [#13850](https://github.com/nodejs/node/pull/13850)
  - In object mode, readable streams can now use `undefined` again.
    [[`5840138e70`](https://github.com/nodejs/node/commit/5840138e70)]
    [#13760](https://github.com/nodejs/node/pull/13760)

### Commits

- [[`11f45623ac`](https://github.com/nodejs/node/commit/11f45623ac)] - **benchmark**: remove needless RegExp capturing (<PERSON><PERSON>) [#13718](https://github.com/nodejs/node/pull/13718)
- [[`2ce236e173`](https://github.com/nodejs/node/commit/2ce236e173)] - **build**: check for linter in bin rather than lib (Rich Trott) [#13645](https://github.com/nodejs/node/pull/13645)
- [[`18f073f0fe`](https://github.com/nodejs/node/commit/18f073f0fe)] - **build**: fail linter if linting not available (Gibson Fahnestock) [#13658](https://github.com/nodejs/node/pull/13658)
- [[`465bd48b14`](https://github.com/nodejs/node/commit/465bd48b14)] - **configure**: add mips64el to valid_arch (Aditya Anand) [#13620](https://github.com/nodejs/node/pull/13620)
- [[`1fe455f525`](https://github.com/nodejs/node/commit/1fe455f525)] - **dgram**: change parameter name in set(Multicast)TTL (Tobias Nießen) [#13747](https://github.com/nodejs/node/pull/13747)
- [[`a63e54a94c`](https://github.com/nodejs/node/commit/a63e54a94c)] - **doc**: update backporting guide (Refael Ackermann) [#13749](https://github.com/nodejs/node/pull/13749)
- [[`0bb53a7aa2`](https://github.com/nodejs/node/commit/0bb53a7aa2)] - **doc**: make socket IPC examples more robust (cjihrig) [#13196](https://github.com/nodejs/node/pull/13196)
- [[`57b7285400`](https://github.com/nodejs/node/commit/57b7285400)] - **doc**: mention rebasing of v?.x-staging post release (Anna Henningsen) [#13742](https://github.com/nodejs/node/pull/13742)
- [[`cb932835d5`](https://github.com/nodejs/node/commit/cb932835d5)] - **doc**: `path.relative` uses `cwd` (DuanPengfei) [#13714](https://github.com/nodejs/node/pull/13714)
- [[`61714acbe5`](https://github.com/nodejs/node/commit/61714acbe5)] - **doc**: add hasIntl to test/common/README.md (Daniel Bevenius) [#13699](https://github.com/nodejs/node/pull/13699)
- [[`2a95cfb4ef`](https://github.com/nodejs/node/commit/2a95cfb4ef)] - **doc**: fix typo in changelog (Teddy Katz) [#13713](https://github.com/nodejs/node/pull/13713)
- [[`31ae193b99`](https://github.com/nodejs/node/commit/31ae193b99)] - **doc**: small makeover for onboarding.md (Anna Henningsen) [#13413](https://github.com/nodejs/node/pull/13413)
- [[`c27ffadf8e`](https://github.com/nodejs/node/commit/c27ffadf8e)] - **doc**: fix a few n-api doc issues (Michael Dawson) [#13650](https://github.com/nodejs/node/pull/13650)
- [[`c142f1d316`](https://github.com/nodejs/node/commit/c142f1d316)] - **doc**: fix minor issues reported in #9538 (Tobias Nießen) [#13491](https://github.com/nodejs/node/pull/13491)
- [[`f28dd8e680`](https://github.com/nodejs/node/commit/f28dd8e680)] - **doc**: fixes a typo in the async_hooks documentation (Chris Young) [#13666](https://github.com/nodejs/node/pull/13666)
- [[`58e177cde1`](https://github.com/nodejs/node/commit/58e177cde1)] - **doc**: document and test that methods return this (Sam Roberts) [#13531](https://github.com/nodejs/node/pull/13531)
- [[`f5f2a0e968`](https://github.com/nodejs/node/commit/f5f2a0e968)] - **doc**: sort and update /cc list for inspector issues (Aditya Anand) [#13632](https://github.com/nodejs/node/pull/13632)
- [[`dc06a0a85a`](https://github.com/nodejs/node/commit/dc06a0a85a)] - **doc**: note that EoL platforms are not supported (Gibson Fahnestock) [#12672](https://github.com/nodejs/node/pull/12672)
- [[`9b74dded0d`](https://github.com/nodejs/node/commit/9b74dded0d)] - **doc**: update async_hooks providers list (Anna Henningsen) [#13561](https://github.com/nodejs/node/pull/13561)
- [[`cc922310e3`](https://github.com/nodejs/node/commit/cc922310e3)] - **doc**: fix out of date napi_callback doc (XadillaX) [#13570](https://github.com/nodejs/node/pull/13570)
- [[`8cb7d96569`](https://github.com/nodejs/node/commit/8cb7d96569)] - **fs**: don't conflate data and callback in appendFile (Nikolai Vavilov) [#11607](https://github.com/nodejs/node/pull/11607)
- [[`233545a81c`](https://github.com/nodejs/node/commit/233545a81c)] - **inspector,cluster**: fix inspect port assignment (cornholio) [#13619](https://github.com/nodejs/node/pull/13619)
- [[`cbe7c5c617`](https://github.com/nodejs/node/commit/cbe7c5c617)] - **lib**: correct typo in createSecureContext (Daniel Bevenius) [#13653](https://github.com/nodejs/node/pull/13653)
- [[`f49dd21b2f`](https://github.com/nodejs/node/commit/f49dd21b2f)] - **n-api**: avoid crash in napi_escape_scope() (Michael Dawson) [#13651](https://github.com/nodejs/node/pull/13651)
- [[`28166770bd`](https://github.com/nodejs/node/commit/28166770bd)] - **net**: fix abort on bad address input (Ruben Bridgewater) [#13726](https://github.com/nodejs/node/pull/13726)
- [[`e786926de9`](https://github.com/nodejs/node/commit/e786926de9)] - **readline,repl,url,util**: remove needless capturing (Vse Mozhet Byt) [#13718](https://github.com/nodejs/node/pull/13718)
- [[`3322191d2f`](https://github.com/nodejs/node/commit/3322191d2f)] - **src**: don't set --icu_case_mapping flag on startup (Ben Noordhuis) [#13698](https://github.com/nodejs/node/pull/13698)
- [[`a27a35b997`](https://github.com/nodejs/node/commit/a27a35b997)] - **src**: fix decoding base64 with whitespace (Nikolai Vavilov) [#13660](https://github.com/nodejs/node/pull/13660)
- [[`5b3e5fac38`](https://github.com/nodejs/node/commit/5b3e5fac38)] - **src**: remove void casts for clear_error_on_return (Daniel Bevenius) [#13669](https://github.com/nodejs/node/pull/13669)
- [[`0a9e96e86c`](https://github.com/nodejs/node/commit/0a9e96e86c)] - **stream**: finish must always follow error (Matteo Collina) [#13850](https://github.com/nodejs/node/pull/13850)
- [[`5840138e70`](https://github.com/nodejs/node/commit/5840138e70)] - **stream**: fix `undefined` in Readable object mode (Anna Henningsen) [#13760](https://github.com/nodejs/node/pull/13760)
- [[`f1d96f0b2a`](https://github.com/nodejs/node/commit/f1d96f0b2a)] - **test**: refactor test-http-set-timeout-server (Rich Trott) [#13802](https://github.com/nodejs/node/pull/13802)
- [[`b23f2461cb`](https://github.com/nodejs/node/commit/b23f2461cb)] - **test**: refactor test-stream2-writable (Rich Trott) [#13823](https://github.com/nodejs/node/pull/13823)
- [[`9ff9782f66`](https://github.com/nodejs/node/commit/9ff9782f66)] - **test**: remove common module from test it thwarts (Rich Trott) [#13748](https://github.com/nodejs/node/pull/13748)
- [[`1f32d9ef5b`](https://github.com/nodejs/node/commit/1f32d9ef5b)] - **test**: fix RegExp nits (Vse Mozhet Byt) [#13770](https://github.com/nodejs/node/pull/13770)
- [[`3306fd1d97`](https://github.com/nodejs/node/commit/3306fd1d97)] - **test**: accommodate AIX by watching file (Rich Trott) [#13766](https://github.com/nodejs/node/pull/13766)
- [[`c8b134bc6d`](https://github.com/nodejs/node/commit/c8b134bc6d)] - **test**: remove node-tap lookalike (cjihrig) [#13707](https://github.com/nodejs/node/pull/13707)
- [[`d4a05b2d9c`](https://github.com/nodejs/node/commit/d4a05b2d9c)] - **test**: make test-http(s)-set-timeout-server alike (jklepatch) [#13625](https://github.com/nodejs/node/pull/13625)
- [[`d0f39cc38a`](https://github.com/nodejs/node/commit/d0f39cc38a)] - **test**: delete outdated fixtures/stdio-filter.js (Vse Mozhet Byt) [#13712](https://github.com/nodejs/node/pull/13712)
- [[`b2a5399760`](https://github.com/nodejs/node/commit/b2a5399760)] - **test**: refactor test-fs-watch-stop-sync (Rich Trott) [#13689](https://github.com/nodejs/node/pull/13689)
- [[`10aee10c0c`](https://github.com/nodejs/node/commit/10aee10c0c)] - **test**: check zlib version for createDeflateRaw (Daniel Bevenius) [#13697](https://github.com/nodejs/node/pull/13697)
- [[`0d3b52e9de`](https://github.com/nodejs/node/commit/0d3b52e9de)] - **test**: add hasIntl to failing test (Daniel Bevenius) [#13699](https://github.com/nodejs/node/pull/13699)
- [[`70fb1bd038`](https://github.com/nodejs/node/commit/70fb1bd038)] - **test**: improve http test reliability (Brian White) [#13693](https://github.com/nodejs/node/pull/13693)
- [[`5e59c2d21d`](https://github.com/nodejs/node/commit/5e59c2d21d)] - **test**: increase coverage for internal/module.js (Tamás Hódi) [#13673](https://github.com/nodejs/node/pull/13673)
- [[`ba20627520`](https://github.com/nodejs/node/commit/ba20627520)] - **test**: refactor test-fs-read-stream (Rich Trott) [#13643](https://github.com/nodejs/node/pull/13643)
- [[`e203e392d7`](https://github.com/nodejs/node/commit/e203e392d7)] - **test**: refactor test-cluster-worker-isconnected.js (cjihrig) [#13685](https://github.com/nodejs/node/pull/13685)
- [[`80e6524ff0`](https://github.com/nodejs/node/commit/80e6524ff0)] - **test**: fix nits in test-fs-mkdir-rmdir.js (Vse Mozhet Byt) [#13680](https://github.com/nodejs/node/pull/13680)
- [[`406c09aacb`](https://github.com/nodejs/node/commit/406c09aacb)] - **test**: fix test-inspector-port-zero-cluster (Refael Ackermann) [#13373](https://github.com/nodejs/node/pull/13373)
- [[`af46cf621b`](https://github.com/nodejs/node/commit/af46cf621b)] - **test**: refactor test-fs-watch-stop-async (Rich Trott) [#13661](https://github.com/nodejs/node/pull/13661)
- [[`6920d5c9f9`](https://github.com/nodejs/node/commit/6920d5c9f9)] - **test**: change deprecated method to recommended (Rich Trott) [#13649](https://github.com/nodejs/node/pull/13649)
- [[`0d87b3102a`](https://github.com/nodejs/node/commit/0d87b3102a)] - **test**: refactor test-fs-read-stream-inherit (Rich Trott) [#13618](https://github.com/nodejs/node/pull/13618)
- [[`80fa13b93f`](https://github.com/nodejs/node/commit/80fa13b93f)] - **test**: use mustNotCall() in test-fs-watch (Rich Trott) [#13595](https://github.com/nodejs/node/pull/13595)
- [[`7874360ca2`](https://github.com/nodejs/node/commit/7874360ca2)] - **test**: add mustCall() to child-process test (Rich Trott) [#13605](https://github.com/nodejs/node/pull/13605)
- [[`5cb3fac396`](https://github.com/nodejs/node/commit/5cb3fac396)] - **test**: use mustNotCall in test-http-eof-on-connect (Rich Trott) [#13587](https://github.com/nodejs/node/pull/13587)
- [[`4afa7483b1`](https://github.com/nodejs/node/commit/4afa7483b1)] - **test**: increase bufsize in child process write test (Rich Trott) [#13626](https://github.com/nodejs/node/pull/13626)
- [[`0ef687e858`](https://github.com/nodejs/node/commit/0ef687e858)] - **tools**: fix error in custom ESLint rule (Rich Trott) [#13758](https://github.com/nodejs/node/pull/13758)
- [[`b171e728e5`](https://github.com/nodejs/node/commit/b171e728e5)] - **tools**: apply stricter indentation rules to tools (Rich Trott) [#13758](https://github.com/nodejs/node/pull/13758)
- [[`9c2abc3e29`](https://github.com/nodejs/node/commit/9c2abc3e29)] - **tools**: fix indentation in required-modules.js (Rich Trott) [#13758](https://github.com/nodejs/node/pull/13758)
- [[`ff568d4b63`](https://github.com/nodejs/node/commit/ff568d4b63)] - **tools**: update ESLint to v4.0.0 (Rich Trott) [#13645](https://github.com/nodejs/node/pull/13645)
- [[`c046a21321`](https://github.com/nodejs/node/commit/c046a21321)] - **util**: ignore invalid format specifiers (Michaël Zasso) [#13674](https://github.com/nodejs/node/pull/13674)
- [[`c68e472b76`](https://github.com/nodejs/node/commit/c68e472b76)] - **v8**: fix RegExp nits in v8_prof_polyfill.js (Vse Mozhet Byt) [#13709](https://github.com/nodejs/node/pull/13709)

Windows 32-bit Installer: https://nodejs.org/dist/v8.1.3/node-v8.1.3-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v8.1.3/node-v8.1.3-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v8.1.3/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v8.1.3/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v8.1.3/node-v8.1.3.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v8.1.3/node-v8.1.3-darwin-x64.tar.gz \
Linux 32-bit Binary: https://nodejs.org/dist/v8.1.3/node-v8.1.3-linux-x86.tar.xz \
Linux 64-bit Binary: https://nodejs.org/dist/v8.1.3/node-v8.1.3-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v8.1.3/node-v8.1.3-linux-ppc64le.tar.xz \
Linux PPC BE 64-bit Binary: https://nodejs.org/dist/v8.1.3/node-v8.1.3-linux-ppc64.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v8.1.3/node-v8.1.3-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v8.1.3/node-v8.1.3-aix-ppc64.tar.gz \
SmartOS 32-bit Binary: https://nodejs.org/dist/v8.1.3/node-v8.1.3-sunos-x86.tar.xz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v8.1.3/node-v8.1.3-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v8.1.3/node-v8.1.3-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v8.1.3/node-v8.1.3-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v8.1.3/node-v8.1.3-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v8.1.3/node-v8.1.3.tar.gz \
Other release files: https://nodejs.org/dist/v8.1.3/ \
Documentation: https://nodejs.org/docs/v8.1.3/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

9f00c50d3065a6effe2b8f5c1097238bb7a00de9136b36e368fd30000135006f  node-v8.1.3-aix-ppc64.tar.gz
ae588038480a6acc57b6b04802fa876e0b602231e9846944dd1b4437e8c1205f  node-v8.1.3-darwin-x64.tar.gz
66ac1b3d3135dc7366b7b105768f334deb347770a7cd35f854638b8095ca1143  node-v8.1.3-darwin-x64.tar.xz
f1ac5b4d1778df43e102ba6a7bc884bdcdaa5cffbd228581794673babc36dd6c  node-v8.1.3-headers.tar.gz
a723e2cc1188fc14f3bf92c156c184d5e3854735ee5f38d62c27f76f5e0f4237  node-v8.1.3-headers.tar.xz
5a7837cc31f2d2e0a8db081f5cb856276a6da3bb7b7cae364a36918763816ced  node-v8.1.3-linux-arm64.tar.gz
cebc2edd89f20613a530509a2435ecc42757ce16032559ef174ebe84875a1536  node-v8.1.3-linux-arm64.tar.xz
a0386233c3e249c6bbfdb543581b01c3d0b006a17140bd791441c333b345c3a9  node-v8.1.3-linux-armv6l.tar.gz
3fefae29ab6028c0ad339527c08586588df5389df5228b1ed2016be1968d7907  node-v8.1.3-linux-armv6l.tar.xz
d63469cad1f71da249f931b9d7ab4b77dc83a08740cd6917ba7171b5abf0bc6e  node-v8.1.3-linux-armv7l.tar.gz
5f957d0bb3d6dcbb9db04de63a9ff21f1e5edcdd1e2e3f0fb372a394403f8882  node-v8.1.3-linux-armv7l.tar.xz
65853d72de7c2b30f7c79817e8c60b4063f6918c7651c80cf1a7a875e4d49bf9  node-v8.1.3-linux-ppc64le.tar.gz
b7d04eb8ac25a5c2b61423695289e207c0d82cf80633f1be55d01eac1848283a  node-v8.1.3-linux-ppc64le.tar.xz
59c16cb44800d4ca96f98bcf382f176591cbcc504b8b21a9ee9a7d28eaed06a1  node-v8.1.3-linux-ppc64.tar.gz
ba64edcb0274f3b413e5be9acbcdcc48188f0e2ce8c1afcac511f191b0eabea3  node-v8.1.3-linux-ppc64.tar.xz
302474d0a93055647596f8c962a6afe3fd1cc0e66e8b4d8f14f42813172973d0  node-v8.1.3-linux-s390x.tar.gz
ab54cde2b649002a6f322a460690438d20997a4ff2b3c294b66afc075df24fb6  node-v8.1.3-linux-s390x.tar.xz
1a526c56fb0fe0f4e91892874d89be2c8920a9d51eb6ed8bd68f66162b7a6b9e  node-v8.1.3-linux-x64.tar.gz
d41dc375ea7e33fadf0fb1bf89d9dfd222a2fb85633fba3d2cf48ac03522ba71  node-v8.1.3-linux-x64.tar.xz
38bc1b25fc3dea3984729d4457d8e29ea6f0be156ba2e2c2274d850ebda4b8bd  node-v8.1.3-linux-x86.tar.gz
68d895c6662c017ddcd54b788bfc90c310520c23853339fc815b58ef1b39c06e  node-v8.1.3-linux-x86.tar.xz
6534587fc70b4384a78f2a8557c9fef4e40e7843205a373c401b2b8511ff79c4  node-v8.1.3.pkg
7e91af1e8f9803593c464f1d06df6675c85d35e8fc81bb304caf267dbf6f7f27  node-v8.1.3-sunos-x64.tar.gz
33d2f7f1e865222b07c9b9bbd978b23e41e67ab7c28eccaebcf89acabf279d00  node-v8.1.3-sunos-x64.tar.xz
6d25d4ac6372bdb67cc8c8e6b625ea224d62a0d925659110927eaca09c64618b  node-v8.1.3-sunos-x86.tar.gz
accee86d6b1584a320ba0379292344a176786a3b7ce8712974b04698ec795bba  node-v8.1.3-sunos-x86.tar.xz
388998d052335af1fe0643dd4d68e2fb1c7109ed6ae22a22cc394f1e7aaf43d3  node-v8.1.3.tar.gz
67bc73136807190bc2f4bd840c647bace9077f24988a262f497eceab78f5acd4  node-v8.1.3.tar.xz
1688ab46105e46af3f0f4c0ed595a6e9481a7d5c4c8913c560208a2faf1ec03a  node-v8.1.3-win-x64.7z
be582920c723124ebad48c968f539ef66b1f628d8b6f2338dc68a32f95104856  node-v8.1.3-win-x64.zip
f074db9d173971a5606359c7eddbc2dca288a557542799eb257fdd71ee329523  node-v8.1.3-win-x86.7z
0c7079e6e51150e669aca9f158447504ca0de9254a81fcc68b3ab04e6ee2b901  node-v8.1.3-win-x86.zip
4e4af424435ef4ed18096099ae30880c999fda054e8faa664f5b930370aaad68  node-v8.1.3-x64.msi
53f53d56fa0ecf09bc27ade5980782499053cac7fccf668c017908a4ead11737  node-v8.1.3-x86.msi
6fc8439dbbf17641d993d7793ddc75bbaf401bdb73d4d95edc04db82cdc8d66a  win-x64/node.exe
713aee6504cd983b066489f20d916afd3dd188f8aae022f3abdcceec6ccde476  win-x64/node.lib
4ca44c36341e4218232cc7a8ff2ff1420c6be2208b8b9d84535614b43d9bd842  win-x64/node_pdb.7z
b022710661e1b6bdba24735c2d4530e35577bd0ea3b492deb384d7133dd73ada  win-x64/node_pdb.zip
a9e8086bc09b9f2830dca69bd6b6904e35cd24849fc0123111fd68418a6fccaf  win-x86/node.exe
9715458e3754537b4418d95e62a655d37c86b5aa3abae0ffdbe4413ae55a6130  win-x86/node.lib
7a46f4f812df8b39352f9d5319e82c4c9b46a4ae44e94b62461daef4d52b49d9  win-x86/node_pdb.7z
33c8f5381234b16f469d1940829e19a033cd23dffa18b508b28e1e6f015532d6  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQEcBAEBCAAGBQJZVKV3AAoJEMJzeS99g1RdrE4IANkA2Pskj6dild8ilLCJdxKP
nyn4/FDS1kcEIt1cciLk5CbE/p6Or7I5QlK+Y7dzDzZS0tQldx2Bpnt8ZAYZgsoQ
mx8DUunZIOhY3m6CBxe2G6wUwttLCwfx0kJlQ0wRa2FeemuBsNcJ7EDK/TwqaExv
cMMr+2qYk6f59cyFfcwe3lkYnXenZbcrXX8W0UxqNHrMcC9KmNElXlVtqOF2Au9Y
INgEwQK+whbUiRPgAhFCVUWwDuFR39ZVHTx3c7BB6SrHNWXtk7TVAu5amMqclUwH
mrO0LUurXT7QtEMQdiSa2cvxiTD52p/2enXeI6UkY2Deknf9GG3I2g/tXEq2hiw=
=cUle
-----END PGP SIGNATURE-----

```
