---
date: '2020-09-22T18:22:30.245Z'
category: release
title: Node v14.12.0 (Current)
layout: blog-post
author: <PERSON><PERSON>
---

### Notable changes

- **deps**:
  - update to uvwasi 0.0.11 (<PERSON>) [#35104](https://github.com/nodejs/node/pull/35104)
- **n-api**:
  - create N-API version 7 (<PERSON>) [#35199](https://github.com/nodejs/node/pull/35199)
  - add more property defaults (<PERSON>) [#35214](https://github.com/nodejs/node/pull/35214)

### Commits

- [[`5229ffadcf`](https://github.com/nodejs/node/commit/5229ffadcf)] - **buffer**: adjust validation to account for buffer.kMaxLength (<PERSON>) [#35134](https://github.com/nodejs/node/pull/35134)
- [[`3d78686987`](https://github.com/nodejs/node/commit/3d78686987)] - **build**: increase API requests for stale action (<PERSON>) [#35235](https://github.com/nodejs/node/pull/35235)
- [[`f2cc1c22c1`](https://github.com/nodejs/node/commit/f2cc1c22c1)] - **build**: filter issues & PRs to auto close by matching on stalled label (Phillip Johnsen) [#35159](https://github.com/nodejs/node/pull/35159)
- [[`f3c45a1cef`](https://github.com/nodejs/node/commit/f3c45a1cef)] - **_Revert_** "**build**: require "allow edits" to be checked" (Rich Trott) [#35094](https://github.com/nodejs/node/pull/35094)
- [[`1bb0ed3c6a`](https://github.com/nodejs/node/commit/1bb0ed3c6a)] - **crypto**: improve invalid arg type message for randomInt() (Rich Trott) [#35089](https://github.com/nodejs/node/pull/35089)
- [[`**********`](https://github.com/nodejs/node/commit/**********)] - **crypto**: improve randomInt out-of-range error message (Rich Trott) [#35088](https://github.com/nodejs/node/pull/35088)
- [[`d4389b59b4`](https://github.com/nodejs/node/commit/d4389b59b4)] - **deps**: update to uvwasi 0.0.11 (Colin Ihrig) [#35104](https://github.com/nodejs/node/pull/35104)
- [[`836680a4ea`](https://github.com/nodejs/node/commit/836680a4ea)] - **doc**: use present tense in error messages (Rich Trott) [#35164](https://github.com/nodejs/node/pull/35164)
- [[`b860a7f61c`](https://github.com/nodejs/node/commit/b860a7f61c)] - **doc**: standardize on \_backward\_ (Rich Trott) [#35243](https://github.com/nodejs/node/pull/35243)
- [[`d82dd0c773`](https://github.com/nodejs/node/commit/d82dd0c773)] - **doc**: revise stability section of values doc (Rich Trott) [#35242](https://github.com/nodejs/node/pull/35242)
- [[`2bc335dcf6`](https://github.com/nodejs/node/commit/2bc335dcf6)] - **doc**: clarify napi_property_attributes text (Rich Trott) [#35253](https://github.com/nodejs/node/pull/35253)
- [[`b62d9b47be`](https://github.com/nodejs/node/commit/b62d9b47be)] - **doc**: remove excessive formatting in dgram.md (Rich Trott) [#35234](https://github.com/nodejs/node/pull/35234)
- [[`b9161f408f`](https://github.com/nodejs/node/commit/b9161f408f)] - **doc**: sort repl references in ASCII order (Rich Trott) [#35230](https://github.com/nodejs/node/pull/35230)
- [[`d195d20dbc`](https://github.com/nodejs/node/commit/d195d20dbc)] - **doc**: relax prohibition on personal pronouns (Rich Trott) [#34353](https://github.com/nodejs/node/pull/34353)
- [[`6119e9511c`](https://github.com/nodejs/node/commit/6119e9511c)] - **doc**: clarify use of NAPI_EXPERIMENTAL (Michael Dawson) [#35195](https://github.com/nodejs/node/pull/35195)
- [[`6d4ab36175`](https://github.com/nodejs/node/commit/6d4ab36175)] - **doc**: update attributes used by n-api samples (#35220) (Gerhard Stoebich)
- [[`7610fe500e`](https://github.com/nodejs/node/commit/7610fe500e)] - **doc**: add issue labels sections to release guide (Michaël Zasso) [#35224](https://github.com/nodejs/node/pull/35224)
- [[`2308be06b3`](https://github.com/nodejs/node/commit/2308be06b3)] - **doc**: fix header level for error code (Rich Trott) [#35219](https://github.com/nodejs/node/pull/35219)
- [[`64ac5c68aa`](https://github.com/nodejs/node/commit/64ac5c68aa)] - **doc**: fix small grammatical issues in timers.md (Turner Jabbour) [#35203](https://github.com/nodejs/node/pull/35203)
- [[`92a14d3c72`](https://github.com/nodejs/node/commit/92a14d3c72)] - **doc**: add technical values document (Michael Dawson) [#35145](https://github.com/nodejs/node/pull/35145)
- [[`dbfd3b261d`](https://github.com/nodejs/node/commit/dbfd3b261d)] - **doc**: remove "end user" (Rich Trott) [#35200](https://github.com/nodejs/node/pull/35200)
- [[`c1c93a6cde`](https://github.com/nodejs/node/commit/c1c93a6cde)] - **doc**: use command-line/command line consistently (Rich Trott) [#35198](https://github.com/nodejs/node/pull/35198)
- [[`70ec369b76`](https://github.com/nodejs/node/commit/70ec369b76)] - **doc**: replace "you should do X" with "do X" (Rich Trott) [#35194](https://github.com/nodejs/node/pull/35194)
- [[`e5fffe2f8f`](https://github.com/nodejs/node/commit/e5fffe2f8f)] - **doc**: fix missing word in dgram.md (Tom Atkinson) [#35231](https://github.com/nodejs/node/pull/35231)
- [[`c1e16d15dd`](https://github.com/nodejs/node/commit/c1e16d15dd)] - **doc**: fix deprecation documentation inconsistencies (Antoine du HAMEL) [#35082](https://github.com/nodejs/node/pull/35082)
- [[`910deff2de`](https://github.com/nodejs/node/commit/910deff2de)] - **doc**: fix broken link in crypto.md (Rich Trott) [#35181](https://github.com/nodejs/node/pull/35181)
- [[`066148d229`](https://github.com/nodejs/node/commit/066148d229)] - **doc**: remove problematic auto-linking of curl man pages (Rich Trott) [#35174](https://github.com/nodejs/node/pull/35174)
- [[`aea3f77c8d`](https://github.com/nodejs/node/commit/aea3f77c8d)] - **doc**: update eventLoopUtilization documentation (Anna Henningsen) [#35155](https://github.com/nodejs/node/pull/35155)
- [[`32d1731ae1`](https://github.com/nodejs/node/commit/32d1731ae1)] - **doc**: update process.release (schamberg97) [#35167](https://github.com/nodejs/node/pull/35167)
- [[`176e9e4054`](https://github.com/nodejs/node/commit/176e9e4054)] - **doc**: fix broken links in modules.md (Rich Trott) [#35182](https://github.com/nodejs/node/pull/35182)
- [[`dc4c5696da`](https://github.com/nodejs/node/commit/dc4c5696da)] - **doc**: add missing copyFile change history (Shelley Vohr) [#35056](https://github.com/nodejs/node/pull/35056)
- [[`e8d479ed67`](https://github.com/nodejs/node/commit/e8d479ed67)] - **doc**: perform cleanup on security-release-process.md (Rich Trott) [#35154](https://github.com/nodejs/node/pull/35154)
- [[`99785e48ea`](https://github.com/nodejs/node/commit/99785e48ea)] - **doc**: fix minor punctuation issue in path.md (Amila Welihinda) [#35127](https://github.com/nodejs/node/pull/35127)
- [[`ae85228e54`](https://github.com/nodejs/node/commit/ae85228e54)] - **doc**: perform minor cleanup on cli.md (Rich Trott) [#35152](https://github.com/nodejs/node/pull/35152)
- [[`e4105140e7`](https://github.com/nodejs/node/commit/e4105140e7)] - **doc**: improve table accessibility (Rich Trott) [#35146](https://github.com/nodejs/node/pull/35146)
- [[`7dbcd24541`](https://github.com/nodejs/node/commit/7dbcd24541)] - **doc**: fix left nav color contrast (Rich Trott) [#35141](https://github.com/nodejs/node/pull/35141)
- [[`331142ca25`](https://github.com/nodejs/node/commit/331142ca25)] - **doc**: update contact info for Ash Cripps (Ash Cripps) [#35139](https://github.com/nodejs/node/pull/35139)
- [[`df70861863`](https://github.com/nodejs/node/commit/df70861863)] - **doc**: simplify circular dependencies text in modules.md (Rich Trott) [#35126](https://github.com/nodejs/node/pull/35126)
- [[`f4e714aaf5`](https://github.com/nodejs/node/commit/f4e714aaf5)] - **doc**: update my email address (Michael Dawson) [#35121](https://github.com/nodejs/node/pull/35121)
- [[`46b9f4b376`](https://github.com/nodejs/node/commit/46b9f4b376)] - **doc**: add missing changes entry for breakEvalOnSigint REPL option (Anna Henningsen) [#35143](https://github.com/nodejs/node/pull/35143)
- [[`122edad98b`](https://github.com/nodejs/node/commit/122edad98b)] - **doc**: update security process (Michael Dawson) [#35107](https://github.com/nodejs/node/pull/35107)
- [[`aa93c1c22d`](https://github.com/nodejs/node/commit/aa93c1c22d)] - **doc**: fix broken link in perf_hooks.md (Rich Trott) [#35113](https://github.com/nodejs/node/pull/35113)
- [[`5c8d2083c5`](https://github.com/nodejs/node/commit/5c8d2083c5)] - **doc**: fix broken link in http2.md (Rich Trott) [#35112](https://github.com/nodejs/node/pull/35112)
- [[`f4e958fc0c`](https://github.com/nodejs/node/commit/f4e958fc0c)] - **doc**: fix broken link in fs.md (Rich Trott) [#35111](https://github.com/nodejs/node/pull/35111)
- [[`79c6d92e49`](https://github.com/nodejs/node/commit/79c6d92e49)] - **doc**: fix broken links in deprecations.md (Rich Trott) [#35109](https://github.com/nodejs/node/pull/35109)
- [[`93e4d545d8`](https://github.com/nodejs/node/commit/93e4d545d8)] - **doc**: add note about path.basename on Windows (Tobias Nießen) [#35065](https://github.com/nodejs/node/pull/35065)
- [[`0a2610a7aa`](https://github.com/nodejs/node/commit/0a2610a7aa)] - **doc**: avoid double-while sentence in perf_hooks.md (Rich Trott) [#35078](https://github.com/nodejs/node/pull/35078)
- [[`1cf9934e4e`](https://github.com/nodejs/node/commit/1cf9934e4e)] - **doc**: make minor improvements to module.md (Rich Trott) [#35083](https://github.com/nodejs/node/pull/35083)
- [[`fb89be63be`](https://github.com/nodejs/node/commit/fb89be63be)] - **doc**: use correct Error type for EventEmitter.defaultMaxListener (Rich Trott) [#35069](https://github.com/nodejs/node/pull/35069)
- [[`b75822dd93`](https://github.com/nodejs/node/commit/b75822dd93)] - **doc,test**: specify and test CLI option precedence rules (Anna Henningsen) [#35106](https://github.com/nodejs/node/pull/35106)
- [[`4bde865145`](https://github.com/nodejs/node/commit/4bde865145)] - **errors**: simplify ERR_REQUIRE_ESM message generation (Rich Trott) [#35123](https://github.com/nodejs/node/pull/35123)
- [[`6e622d6337`](https://github.com/nodejs/node/commit/6e622d6337)] - **esm**: better package.json parser errors (Guy Bedford) [#35117](https://github.com/nodejs/node/pull/35117)
- [[`beb75bd031`](https://github.com/nodejs/node/commit/beb75bd031)] - **fs**: loosen validation to allow objects with an own toString function (Jordan Harband) [#34993](https://github.com/nodejs/node/pull/34993)
- [[`8ea28536d0`](https://github.com/nodejs/node/commit/8ea28536d0)] - **http**: only set keep-alive when not exists (<EMAIL>) [#35138](https://github.com/nodejs/node/pull/35138)
- [[`977b0ed865`](https://github.com/nodejs/node/commit/977b0ed865)] - **http**: allow Content-Length header for 304 responses (Arnaud Lefebvre) [#34835](https://github.com/nodejs/node/pull/34835)
- [[`d8b57140b4`](https://github.com/nodejs/node/commit/d8b57140b4)] - **https**: set requestTimeout default to 0 (Robert Nagy) [#35264](https://github.com/nodejs/node/pull/35264)
- [[`12f2934224`](https://github.com/nodejs/node/commit/12f2934224)] - **meta**: add links to OpenJSF Slack (Mary Marchini) [#35128](https://github.com/nodejs/node/pull/35128)
- [[`f21a5c6200`](https://github.com/nodejs/node/commit/f21a5c6200)] - **meta**: update my collab entry (devsnek) [#35160](https://github.com/nodejs/node/pull/35160)
- [[`76e24f9aa9`](https://github.com/nodejs/node/commit/76e24f9aa9)] - **module**: use isURLInstance instead of instanceof (Antoine du HAMEL) [#34951](https://github.com/nodejs/node/pull/34951)
- [[`314483cd09`](https://github.com/nodejs/node/commit/314483cd09)] - **module**: fix specifier resolution option value (himself65) [#35098](https://github.com/nodejs/node/pull/35098)
- [[`ca1181615e`](https://github.com/nodejs/node/commit/ca1181615e)] - **(SEMVER-MINOR)** **n-api**: create N-API version 7 (Gabriel Schulhof) [#35199](https://github.com/nodejs/node/pull/35199)
- [[`7f3b2b2a1f`](https://github.com/nodejs/node/commit/7f3b2b2a1f)] - **(SEMVER-MINOR)** **n-api**: add more property defaults (Gerhard Stoebich) [#35214](https://github.com/nodejs/node/pull/35214)
- [[`4f4faa8e3c`](https://github.com/nodejs/node/commit/4f4faa8e3c)] - **readline**: fix key name for function keys with modifiers (DrunkenPoney) [#35268](https://github.com/nodejs/node/pull/35268)
- [[`e29e2daf4c`](https://github.com/nodejs/node/commit/e29e2daf4c)] - **test**: improve assertions in pummel/test-timers (Rich Trott) [#35216](https://github.com/nodejs/node/pull/35216)
- [[`8357b56984`](https://github.com/nodejs/node/commit/8357b56984)] - **test**: add wasi readdir() test (Colin Ihrig) [#35202](https://github.com/nodejs/node/pull/35202)
- [[`49da459ce6`](https://github.com/nodejs/node/commit/49da459ce6)] - **test**: improve pummel/test-timers.js (Rich Trott) [#35175](https://github.com/nodejs/node/pull/35175)
- [[`379c5cefd7`](https://github.com/nodejs/node/commit/379c5cefd7)] - **test**: revise test-policy-integrity (Rich Trott) [#35101](https://github.com/nodejs/node/pull/35101)
- [[`6627c1f8e4`](https://github.com/nodejs/node/commit/6627c1f8e4)] - **test**: remove setMaxListeners in test-crypto-random (Tobias Nießen) [#35079](https://github.com/nodejs/node/pull/35079)
- [[`bc38485fb1`](https://github.com/nodejs/node/commit/bc38485fb1)] - **test**: fix comment about DNS lookup test (Tobias Nießen) [#35080](https://github.com/nodejs/node/pull/35080)
- [[`9faaa659b2`](https://github.com/nodejs/node/commit/9faaa659b2)] - **test**: separate the test fixtures between ICU and URL (Leko) [#35077](https://github.com/nodejs/node/pull/35077)
- [[`25f93f3ec3`](https://github.com/nodejs/node/commit/25f93f3ec3)] - **test**: add more valid results to test-trace-atomics-wait (Anna Henningsen) [#35066](https://github.com/nodejs/node/pull/35066)
- [[`0a97f44b50`](https://github.com/nodejs/node/commit/0a97f44b50)] - **tools**: update ESLint to 7.9.0 (Colin Ihrig) [#35170](https://github.com/nodejs/node/pull/35170)

Windows 32-bit Installer: https://nodejs.org/dist/v14.12.0/node-v14.12.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v14.12.0/node-v14.12.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v14.12.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v14.12.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v14.12.0/node-v14.12.0.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v14.12.0/node-v14.12.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v14.12.0/node-v14.12.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v14.12.0/node-v14.12.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v14.12.0/node-v14.12.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v14.12.0/node-v14.12.0-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v14.12.0/node-v14.12.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v14.12.0/node-v14.12.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v14.12.0/node-v14.12.0.tar.gz \
Other release files: https://nodejs.org/dist/v14.12.0/ \
Documentation: https://nodejs.org/docs/v14.12.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

2f9f3bed29027b30b331c673425edbd7b40be2bf623c4e08acb491a366858062  node-v14.12.0-aix-ppc64.tar.gz
c91a4ea40289886799115a8a309b844975d59d457cbad2060779286f0a8ad01b  node-v14.12.0-darwin-x64.tar.gz
8786bac80d2b7580df2c5479bf72ca6fbc1c969003ec257810e3fbdcb39d9b13  node-v14.12.0-darwin-x64.tar.xz
2a319b2f5a76d21b7944f2e722e8e2d1715aa3ff3357817f525ea7194744e913  node-v14.12.0-headers.tar.gz
6157a264e2c787dfb9493447cda86f5dd9e5f08c82974d7f4f87b9e81ad82c33  node-v14.12.0-headers.tar.xz
bd4feec12f8a4847a9f863f8819a74c30cddcd532a358a81b5bff0fb9e453275  node-v14.12.0-linux-arm64.tar.gz
c19c48f3b6806b02918d5ffa260e1f972f140e8583959295c8426f6684c6d534  node-v14.12.0-linux-arm64.tar.xz
1e8cc47511567173f6caf5a6af8c233e6d49e99045a5051a2880fff1ec7ef3e3  node-v14.12.0-linux-armv7l.tar.gz
a494bfad8c7587c84ff3c8547d60abeb6910388f076aca81797dddd5bbc6005b  node-v14.12.0-linux-armv7l.tar.xz
8c68ac374195921e5b079256343982d59a99be87e37dd49d2cb12cf4d78101aa  node-v14.12.0-linux-ppc64le.tar.gz
e5a085f14b06ab351f2529613b42a2c01ddb51609bdf5588c49589e4b6522669  node-v14.12.0-linux-ppc64le.tar.xz
f0a05440584984132214185cd0427485599dcc1501d43b617215bc739fed4e68  node-v14.12.0-linux-s390x.tar.gz
3f654af760359d3dbfe5901092cb66c77140965ba80aadc1d0f3228636022041  node-v14.12.0-linux-s390x.tar.xz
f430bf1d8352c18d628771e7c5f932dbc1e48cec1c8b6417a7bdc4027518f5ed  node-v14.12.0-linux-x64.tar.gz
3c7363b56239b8f357eed6e82e91f99624dd8faf49f49916bd1b54ea1fbd46b7  node-v14.12.0-linux-x64.tar.xz
81235be36b429a8d75da720017f01fdd8e821c2dd7bb46fbadec3fae50eed8a7  node-v14.12.0.pkg
caa31eb22f18b0d26d2867002f65f313b619bc3ebfae409fcefbd6a15372ca7e  node-v14.12.0.tar.gz
6c345b67122257fce8000992457f59f78978dbea343a592f08d34f8580d85530  node-v14.12.0.tar.xz
051e2cf294f715a5e2067bd6794c94b12a249a145a5bb66468bbbb54e482f3a0  node-v14.12.0-win-x64.7z
47adf6187f1dbfd5c8731583ef621342155e172df9577e862cca0f62b0edf544  node-v14.12.0-win-x64.zip
6925b00cb530bb4fbff8c69fb43f66b714d944e7757041979100dd3c243e7374  node-v14.12.0-win-x86.7z
1d1c69a95fef0ab41d6a94b46f9f1492156ff86d569d941dc9645bcbad8f9f65  node-v14.12.0-win-x86.zip
c7b0d86601a19eb5e699f51191bbba296c94580effdcf856c3f707ab93631634  node-v14.12.0-x64.msi
eb46c0b749e741fe36c72902e32ec6310a1799ae9bdddfb0e7af6852fc5721d6  node-v14.12.0-x86.msi
16fb671d2b06196482243fc31afb9cc0914c191b08181e71e20d872b51b09d99  win-x64/node.exe
a1968370c38c54d5eb66996c077c6b450cb8c3abde37b75148fe5f0ca632c27e  win-x64/node.lib
3a11d494c9fec7b8974fb88878ca25ced831be1f09a90a1f9482bf2968b8758d  win-x64/node_pdb.7z
1f5365fc17e309628dfba0bb07b16ae0451761ca5791bd61940e6853e4afa126  win-x64/node_pdb.zip
f3e275e04aa2f9452f411bc9f709d34ba0e5b7da9497733138a655e633927bba  win-x86/node.exe
96d8a893d60b725a41477f9d5448adaf84d1be56ab8c05ca32605ffcf6c65c48  win-x86/node.lib
b2664eef02df1613777d7a9a4aadeb1072f6002df422342c6d27491d71eb9714  win-x86/node_pdb.7z
b167d1c170c432fb534e115247a5c6164daa839e29cce8574b1bb5673e3b6280  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEEI9StI21e7DMQ5spl7AUGb2S+AoFAl9qP8EACgkQl7AUGb2S
+ApdGQ//bUCm6PHtl2KVftTAhVZJKIpnvbYe6o1Nfbjvf1tPh5MbNqGWN8LHl2vJ
+hVWMYT1Lwfv81iVDcUOzA7N4vj8wvNKpRz1o19KKaQIamXfshi98wUGmyQBM6KF
EyAA2R6NrEHfp21tQLcvbafIlkUtBQmD3/bL9Yk2i/qqfxZTddKU53vcVg8452LD
iRqyltwlMTPAtxyb3tz76Ikg1xLPzHfF4wFHmMFwrm8OfXY8rDzny2nmUYCLo2yg
lr1nkCDhENEd8QP3Gg36d3iOyFsDgDQn8FOHwuB9f4Uwtyp5y+qlPXd7A6DGOnaF
7DWujSt5n5LvLse240D0OlQlLEFPjW/4Y2B6Mg5ZHfTNYlm/0JfdCLtPrnEg57LE
HllyaQ7QYQIj/1KCty2e+QhNC03yy/KOFnHl3Avk0oa0nKCQupv6qvymqlda08CB
4HtYYNe8tFtThlCVRhPgQ8+LBgSvu/a2HOqXCeH6NIu+E25kb+Od9jKq8hBh+ivn
fRwfEUUwLO0ltd+J+hj1GAQkn9qWkktMI8EfuSNXLQBsqfs/D7lsqHh72qbYo0nX
P6udnbwM+ewiSu4X08XwG0T8QVV1mvdwvI0cfXUMbIasC8AWEEFais8BjvK180dT
JY85rhPIK0MD+HujIHBsBe418S/WnutrmFya1zyM5zBOatryU7k=
=Pntc
-----END PGP SIGNATURE-----

```
