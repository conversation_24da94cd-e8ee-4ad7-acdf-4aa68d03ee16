---
date: '2018-01-10T15:27:42.369Z'
category: release
title: Node v9.4.0 (Current)
layout: blog-post
author: <PERSON><PERSON>
---

### Notable Changes

- **async_hooks**:
  - deprecate AsyncHooks Sensitive API and runInAsyncIdScope. Neither API were documented. (<PERSON>) [#16972](https://github.com/nodejs/node/pull/16972)
- **deps**:
  - update nghttp2 to 1.29.0 (<PERSON>) [#17908](https://github.com/nodejs/node/pull/17908)
  - upgrade npm to 5.6.0 (<PERSON>) [#17535](https://github.com/nodejs/node/pull/17535)
  - cherry-pick 50f7455 from upstream V8 (<PERSON><PERSON><PERSON>) [#16591](https://github.com/nodejs/node/pull/16591)
- **events**:
  - remove reaches into \_events internals (<PERSON><PERSON><PERSON>) [#17440](https://github.com/nodejs/node/pull/17440)
- **http**:
  - add rawPacket in err of `clientError` event (XadillaX) [#17672](https://github.com/nodejs/node/pull/17672)
- **http2**:
  - implement maxSessionMemory (James M Snell) [#17967](https://github.com/nodejs/node/pull/17967)
  - add initial support for originSet (James M Snell) [#17935](https://github.com/nodejs/node/pull/17935)
  - add altsvc support (James M Snell) [#17917](https://github.com/nodejs/node/pull/17917)
  - perf_hooks integration (James M Snell) [#17906](https://github.com/nodejs/node/pull/17906)
  - Refactoring and cleanup of Http2Session and Http2Stream destroy (James M Snell) [#17406](https://github.com/nodejs/node/pull/17406)
- **net**:
  - remove Socket.prototype.listen (Ruben Bridgewater) [#13735](https://github.com/nodejs/node/pull/13735)
- **repl**:
  - show lexically scoped vars in tab completion (Michaël Zasso) [#16591](https://github.com/nodejs/node/pull/16591)
- **stream**:
  - rm {writeable/readable}State.length (Calvin Metcalf) [#12857](https://github.com/nodejs/node/pull/12857)
  - add flow and buffer properties to streams (Calvin Metcalf) [#12855](https://github.com/nodejs/node/pull/12855)
- **util**:
  - allow wildcards in NODE_DEBUG variable (Tyler) [#17609](https://github.com/nodejs/node/pull/17609)
- **zlib**:
  - add ArrayBuffer support (Jem Bezooyen) [#16042](https://github.com/nodejs/node/pull/16042)
- **Added new collaborator**
  - [starkwang](https://github.com/starkwang) Weijia Wang
- **Added new TSC member**
  - [danbev](https://github.com/danbev) Daniel Bevenius

### Commits

- [[`ec443c3430`](https://github.com/nodejs/node/commit/ec443c3430)] - **assert**: fix .throws operator (Ruben Bridgewater) [#17575](https://github.com/nodejs/node/pull/17575)
- [[`0843ed6ae7`](https://github.com/nodejs/node/commit/0843ed6ae7)] - **async_hooks**: use CHECK instead of throwing error (Jon Moss) [#17832](https://github.com/nodejs/node/pull/17832)
- [[`23f4433f89`](https://github.com/nodejs/node/commit/23f4433f89)] - **(SEMVER-MINOR)** **async_hooks**: deprecate undocumented API (Andreas Madsen) [#16972](https://github.com/nodejs/node/pull/16972)
- [[`63c23a1ff2`](https://github.com/nodejs/node/commit/63c23a1ff2)] - **benchmark**: fix timeout in write-stream-throughput (Anatoli Papirovski) [#17958](https://github.com/nodejs/node/pull/17958)
- [[`14eb97ebf7`](https://github.com/nodejs/node/commit/14eb97ebf7)] - **benchmark**: make temp file path configurable (Rich Trott) [#17811](https://github.com/nodejs/node/pull/17811)
- [[`27227cf4c7`](https://github.com/nodejs/node/commit/27227cf4c7)] - **benchmark**: refactor console benchmark (Ruben Bridgewater) [#17707](https://github.com/nodejs/node/pull/17707)
- [[`0aa403b649`](https://github.com/nodejs/node/commit/0aa403b649)] - **buffer**: optimize readDouble and readFloat methods (Ben Noordhuis) [#17775](https://github.com/nodejs/node/pull/17775)
- [[`d93b4765a5`](https://github.com/nodejs/node/commit/d93b4765a5)] - **build**: document targets in the Makefile (Joyee Cheung) [#16975](https://github.com/nodejs/node/pull/16975)
- [[`224033db56`](https://github.com/nodejs/node/commit/224033db56)] - **build**: put .PHONY directly before its target (Oky Antoro) [#17964](https://github.com/nodejs/node/pull/17964)
- [[`2d857ed7c8`](https://github.com/nodejs/node/commit/2d857ed7c8)] - **build**: remove duplicate async-hooks and known_issues test runs (Rich Trott) [#17912](https://github.com/nodejs/node/pull/17912)
- [[`d066db7014`](https://github.com/nodejs/node/commit/d066db7014)] - **cluster**: support windowsHide option for workers (Todd Wong) [#17412](https://github.com/nodejs/node/pull/17412)
- [[`28283efd89`](https://github.com/nodejs/node/commit/28283efd89)] - **console**: order functions and remove \n\n (Ruben Bridgewater) [#17707](https://github.com/nodejs/node/pull/17707)
- [[`41e2bb185d`](https://github.com/nodejs/node/commit/41e2bb185d)] - **console**: make variables and checks stricter (Ruben Bridgewater) [#17707](https://github.com/nodejs/node/pull/17707)
- [[`0573c0fb23`](https://github.com/nodejs/node/commit/0573c0fb23)] - **console**: make error handling engine agnostic (Ruben Bridgewater) [#17707](https://github.com/nodejs/node/pull/17707)
- [[`1b8d3ec5e7`](https://github.com/nodejs/node/commit/1b8d3ec5e7)] - **crypto**: add ocsp_request ClientHelloParser::Reset (Daniel Bevenius) [#17753](https://github.com/nodejs/node/pull/17753)
- [[`d387c178b2`](https://github.com/nodejs/node/commit/d387c178b2)] - **crypto**: warn on invalid authentication tag length (Tobias Nießen) [#17566](https://github.com/nodejs/node/pull/17566)
- [[`7153434fae`](https://github.com/nodejs/node/commit/7153434fae)] - **crypto**: reuse variable instead of reevaluation (Tobias Nießen) [#17735](https://github.com/nodejs/node/pull/17735)
- [[`7d03567287`](https://github.com/nodejs/node/commit/7d03567287)] - **crypto**: remove unused header in clienthello.h (Daniel Bevenius) [#17752](https://github.com/nodejs/node/pull/17752)
- [[`dfb9b5e83a`](https://github.com/nodejs/node/commit/dfb9b5e83a)] - **crypto**: move node_crypto_clienthello-inl.h to cc (Daniel Bevenius) [#17606](https://github.com/nodejs/node/pull/17606)
- [[`43fbc393e3`](https://github.com/nodejs/node/commit/43fbc393e3)] - **deps**: cherry-pick 50f7455 from upstream V8 (Michaël Zasso) [#16591](https://github.com/nodejs/node/pull/16591)
- [[`5df8c76ea9`](https://github.com/nodejs/node/commit/5df8c76ea9)] - **deps**: update nghttp2 to 1.29.0 (James M Snell) [#17908](https://github.com/nodejs/node/pull/17908)
- [[`8f3b2d7e8a`](https://github.com/nodejs/node/commit/8f3b2d7e8a)] - **deps**: V8: cherry-pick ac0fe8ec from upstream (Ali Ijaz Sheikh) [#17695](https://github.com/nodejs/node/pull/17695)
- [[`ffe1ad6c12`](https://github.com/nodejs/node/commit/ffe1ad6c12)] - **deps**: upgrade npm to 5.6.0 (Kat Marchán) [#17535](https://github.com/nodejs/node/pull/17535)
- [[`ffc2659964`](https://github.com/nodejs/node/commit/ffc2659964)] - **doc**: fix incorrect argument type in fs.readSync (Mykola Bilochub) [#18022](https://github.com/nodejs/node/pull/18022)
- [[`ef317014e2`](https://github.com/nodejs/node/commit/ef317014e2)] - **doc**: compact eslint directives in common/README (Vse Mozhet Byt) [#17971](https://github.com/nodejs/node/pull/17971)
- [[`3623cf7ec7`](https://github.com/nodejs/node/commit/3623cf7ec7)] - **doc**: add guide on maintaining build files (Joyee Cheung) [#16975](https://github.com/nodejs/node/pull/16975)
- [[`b593d946e4`](https://github.com/nodejs/node/commit/b593d946e4)] - **doc**: re-alphabetise sections in common/README.md (Vse Mozhet Byt) [#17971](https://github.com/nodejs/node/pull/17971)
- [[`3bcdb3b996`](https://github.com/nodejs/node/commit/3bcdb3b996)] - **doc**: fix code nits in common/README (Vse Mozhet Byt) [#17971](https://github.com/nodejs/node/pull/17971)
- [[`0ad783afaf`](https://github.com/nodejs/node/commit/0ad783afaf)] - **doc**: fix link for https api change (Myles Borins) [#17630](https://github.com/nodejs/node/pull/17630)
- [[`1181ff7ecc`](https://github.com/nodejs/node/commit/1181ff7ecc)] - **doc**: correct spelling (sreepurnajasti) [#17911](https://github.com/nodejs/node/pull/17911)
- [[`43ac36c6de`](https://github.com/nodejs/node/commit/43ac36c6de)] - **doc**: grammar fixes in http2.md (Rich Trott) [#17972](https://github.com/nodejs/node/pull/17972)
- [[`46f39b590b`](https://github.com/nodejs/node/commit/46f39b590b)] - **doc**: add docs for common/http2.js utility (James M Snell) [#17942](https://github.com/nodejs/node/pull/17942)
- [[`83c725dc73`](https://github.com/nodejs/node/commit/83c725dc73)] - **doc**: updates examples to use NULL (Michael Dawson) [#18008](https://github.com/nodejs/node/pull/18008)
- [[`72ed11ac78`](https://github.com/nodejs/node/commit/72ed11ac78)] - **doc**: move matthewloring to emeriti (Rich Trott) [#17998](https://github.com/nodejs/node/pull/17998)
- [[`6efef47c2a`](https://github.com/nodejs/node/commit/6efef47c2a)] - **doc**: move joshgav to TSC emeriti list (Rich Trott) [#17953](https://github.com/nodejs/node/pull/17953)
- [[`294c5f4ef0`](https://github.com/nodejs/node/commit/294c5f4ef0)] - **doc**: improve security section of README.md (Rich Trott) [#17929](https://github.com/nodejs/node/pull/17929)
- [[`445c911ba4`](https://github.com/nodejs/node/commit/445c911ba4)] - **doc**: edit for concision (Rich Trott) [#17891](https://github.com/nodejs/node/pull/17891)
- [[`3fd65815f8`](https://github.com/nodejs/node/commit/3fd65815f8)] - **doc**: remove x86 from os.arch() options (Gibson Fahnestock) [#17899](https://github.com/nodejs/node/pull/17899)
- [[`14499f8185`](https://github.com/nodejs/node/commit/14499f8185)] - **doc**: improve PR-review paragraph in CONTRIBUTING.md (Rich Trott) [#17931](https://github.com/nodejs/node/pull/17931)
- [[`54cf75ddb5`](https://github.com/nodejs/node/commit/54cf75ddb5)] - **doc**: fix typos in CONTRIBUTING.md (Rich Trott) [#17930](https://github.com/nodejs/node/pull/17930)
- [[`16fbd5718a`](https://github.com/nodejs/node/commit/16fbd5718a)] - **doc**: remove non-style information from style guide (Rich Trott) [#17866](https://github.com/nodejs/node/pull/17866)
- [[`a702fcbd4b`](https://github.com/nodejs/node/commit/a702fcbd4b)] - **doc**: copy-edit COLLABORATOR_GUIDE.md (Rich Trott) [#17922](https://github.com/nodejs/node/pull/17922)
- [[`240121ec42`](https://github.com/nodejs/node/commit/240121ec42)] - **doc**: improve alt text (Rich Trott) [#17922](https://github.com/nodejs/node/pull/17922)
- [[`312ad06cfe`](https://github.com/nodejs/node/commit/312ad06cfe)] - **doc**: fix spelling of contributors (Rich Trott) [#17922](https://github.com/nodejs/node/pull/17922)
- [[`2f7030de31`](https://github.com/nodejs/node/commit/2f7030de31)] - **doc**: add references to PR communication articles (Salame William) [#17902](https://github.com/nodejs/node/pull/17902)
- [[`d2b1601bd3`](https://github.com/nodejs/node/commit/d2b1601bd3)] - **doc**: replace wrong U+00A0 by common spaces (Vse Mozhet Byt) [#17940](https://github.com/nodejs/node/pull/17940)
- [[`658bdb34aa`](https://github.com/nodejs/node/commit/658bdb34aa)] - **doc**: remove duplicate words in API docs (Tobias Nießen) [#17937](https://github.com/nodejs/node/pull/17937)
- [[`181b8970b1`](https://github.com/nodejs/node/commit/181b8970b1)] - **doc**: fix duplicate words & spellings in docs (sreepurnajasti) [#17923](https://github.com/nodejs/node/pull/17923)
- [[`4850c87348`](https://github.com/nodejs/node/commit/4850c87348)] - **doc**: doc imitating the old behavior of http.Server.keepAliveTimeout (Tyson Andre) [#17660](https://github.com/nodejs/node/pull/17660)
- [[`b15f029b04`](https://github.com/nodejs/node/commit/b15f029b04)] - **doc**: fs doc improvements (James M Snell) [#17831](https://github.com/nodejs/node/pull/17831)
- [[`9fc9bb1c09`](https://github.com/nodejs/node/commit/9fc9bb1c09)] - **doc**: fix typo (Tobias Nießen) [#17900](https://github.com/nodejs/node/pull/17900)
- [[`2c9dab313e`](https://github.com/nodejs/node/commit/2c9dab313e)] - **doc**: use my legal name in README (Timothy Gu) [#17894](https://github.com/nodejs/node/pull/17894)
- [[`cb127de634`](https://github.com/nodejs/node/commit/cb127de634)] - **doc**: improve module.builtinModules text (Rich Trott) [#17865](https://github.com/nodejs/node/pull/17865)
- [[`1be0086ec8`](https://github.com/nodejs/node/commit/1be0086ec8)] - **doc**: use dashes instead of asterisks (Ruben Bridgewater) [#17722](https://github.com/nodejs/node/pull/17722)
- [[`26fbb0f78a`](https://github.com/nodejs/node/commit/26fbb0f78a)] - **doc**: use consistent new lines (Ruben Bridgewater) [#17722](https://github.com/nodejs/node/pull/17722)
- [[`a63d3c514d`](https://github.com/nodejs/node/commit/a63d3c514d)] - **doc**: update formatting to fit our 80 chars rule (Ruben Bridgewater) [#17722](https://github.com/nodejs/node/pull/17722)
- [[`59711ae42a`](https://github.com/nodejs/node/commit/59711ae42a)] - **doc**: update AUTHORS list (Ruben Bridgewater) [#17805](https://github.com/nodejs/node/pull/17805)
- [[`2d11f6b669`](https://github.com/nodejs/node/commit/2d11f6b669)] - **doc**: add starkwang to collaborators (Weijia Wang) [#17847](https://github.com/nodejs/node/pull/17847)
- [[`fe1f67f184`](https://github.com/nodejs/node/commit/fe1f67f184)] - **doc**: mark DEP0002 as end of life (Jon Moss) [#17815](https://github.com/nodejs/node/pull/17815)
- [[`d4666d0d7a`](https://github.com/nodejs/node/commit/d4666d0d7a)] - **doc**: require CI status indicator in PRs (Nikolai Vavilov) [#17151](https://github.com/nodejs/node/pull/17151)
- [[`541d189db9`](https://github.com/nodejs/node/commit/541d189db9)] - **doc**: use american spelling as per style guide (sreepurnajasti) [#17818](https://github.com/nodejs/node/pull/17818)
- [[`69945596e4`](https://github.com/nodejs/node/commit/69945596e4)] - **doc**: removed extra explanation in api/buffer.md (Waleed Ashraf) [#17796](https://github.com/nodejs/node/pull/17796)
- [[`c328e580d1`](https://github.com/nodejs/node/commit/c328e580d1)] - **doc**: improve module.builtinModules documentation (Thomas Watson) [#17712](https://github.com/nodejs/node/pull/17712)
- [[`1d935a0b2d`](https://github.com/nodejs/node/commit/1d935a0b2d)] - **doc**: instructions on how to make membership public (Michael Dawson) [#17688](https://github.com/nodejs/node/pull/17688)
- [[`b6d2090c8b`](https://github.com/nodejs/node/commit/b6d2090c8b)] - **doc**: improve fs api descriptions (Evan Lucas) [#17679](https://github.com/nodejs/node/pull/17679)
- [[`b1a8ac7774`](https://github.com/nodejs/node/commit/b1a8ac7774)] - **doc**: remove old console note (Ruben Bridgewater) [#17707](https://github.com/nodejs/node/pull/17707)
- [[`c982494433`](https://github.com/nodejs/node/commit/c982494433)] - **doc**: remove duplicate the from onboarding.md (sreepurnajasti) [#17733](https://github.com/nodejs/node/pull/17733)
- [[`206c4f85c5`](https://github.com/nodejs/node/commit/206c4f85c5)] - **doc**: fix typo in README.md (Weijia Wang) [#17729](https://github.com/nodejs/node/pull/17729)
- [[`dbc554a225`](https://github.com/nodejs/node/commit/dbc554a225)] - **doc**: fix typo in child_process.md (Rich Trott) [#17727](https://github.com/nodejs/node/pull/17727)
- [[`dd9d07caa7`](https://github.com/nodejs/node/commit/dd9d07caa7)] - **doc**: remove unused link definition (Jon Moss) [#17741](https://github.com/nodejs/node/pull/17741)
- [[`dcfe840a1e`](https://github.com/nodejs/node/commit/dcfe840a1e)] - **doc**: edit CONTRIBUTING.md preamble (Rich Trott) [#17700](https://github.com/nodejs/node/pull/17700)
- [[`ed9f2fef70`](https://github.com/nodejs/node/commit/ed9f2fef70)] - **doc**: improve release guide (Evan Lucas) [#17677](https://github.com/nodejs/node/pull/17677)
- [[`861f6adb70`](https://github.com/nodejs/node/commit/861f6adb70)] - **doc**: some fs doc improvements (James M Snell) [#17692](https://github.com/nodejs/node/pull/17692)
- [[`ecbc70fe5d`](https://github.com/nodejs/node/commit/ecbc70fe5d)] - **doc**: not all example code can be run without 1:1 (Jeremiah Senkpiel) [#17702](https://github.com/nodejs/node/pull/17702)
- [[`68722fd16e`](https://github.com/nodejs/node/commit/68722fd16e)] - **doc**: adjust TTY wording & add inter-doc links (Jeremiah Senkpiel) [#17702](https://github.com/nodejs/node/pull/17702)
- [[`d19343147b`](https://github.com/nodejs/node/commit/d19343147b)] - **doc**: fix fs.existsSync description (Jeremiah Senkpiel) [#17702](https://github.com/nodejs/node/pull/17702)
- [[`444362e048`](https://github.com/nodejs/node/commit/444362e048)] - **doc**: improve documentation.md (Jeremiah Senkpiel) [#17702](https://github.com/nodejs/node/pull/17702)
- [[`d1af106b76`](https://github.com/nodejs/node/commit/d1af106b76)] - **doc**: add countdown module to writing tests guide (Bamieh) [#17201](https://github.com/nodejs/node/pull/17201)
- [[`e059bc5503`](https://github.com/nodejs/node/commit/e059bc5503)] - **doc**: change "Node.js style cb" to "error-first cb" (Ram Goli) [#17638](https://github.com/nodejs/node/pull/17638)
- [[`712848bc7d`](https://github.com/nodejs/node/commit/712848bc7d)] - **doc**: change eventName type annotations (April Webster) [#17666](https://github.com/nodejs/node/pull/17666)
- [[`c24b4dd898`](https://github.com/nodejs/node/commit/c24b4dd898)] - **doc**: remove extra whitespace in module docs (Thomas Watson) [#17711](https://github.com/nodejs/node/pull/17711)
- [[`af1b340e39`](https://github.com/nodejs/node/commit/af1b340e39)] - **doc**: add C++ style comments to the style guide (Matheus Marchini) [#17617](https://github.com/nodejs/node/pull/17617)
- [[`5999a11526`](https://github.com/nodejs/node/commit/5999a11526)] - **doc**: include Daniel Bevenius as a TSC member (Rich Trott) [#17652](https://github.com/nodejs/node/pull/17652)
- [[`977fb13bd5`](https://github.com/nodejs/node/commit/977fb13bd5)] - **doc**: import() is supported now (Gus Caplan) [#17395](https://github.com/nodejs/node/pull/17395)
- [[`ed4d013f48`](https://github.com/nodejs/node/commit/ed4d013f48)] - **doc**: correct pbkdf2 salt length recommendation (Will Clark) [#17524](https://github.com/nodejs/node/pull/17524)
- [[`d70e6dc850`](https://github.com/nodejs/node/commit/d70e6dc850)] - **doc**: note that randomBytes throws when passed null (Tobias Nießen) [#17594](https://github.com/nodejs/node/pull/17594)
- [[`da448216cc`](https://github.com/nodejs/node/commit/da448216cc)] - **doc**: clearify promisify behavior for bad arguments (Ram Goli) [#17593](https://github.com/nodejs/node/pull/17593)
- [[`26025dec62`](https://github.com/nodejs/node/commit/26025dec62)] - **doc**: replace ArrayBufferView in crypto (Tobias Nießen) [#17595](https://github.com/nodejs/node/pull/17595)
- [[`1a84005150`](https://github.com/nodejs/node/commit/1a84005150)] - **doc,test**: mention Duplex support for TLS (Anna Henningsen) [#17599](https://github.com/nodejs/node/pull/17599)
- [[`7008719fb6`](https://github.com/nodejs/node/commit/7008719fb6)] - **(SEMVER-MINOR)** **events**: remove reaches into \_events internals (Anatoli Papirovski) [#17440](https://github.com/nodejs/node/pull/17440)
- [[`f1485565ef`](https://github.com/nodejs/node/commit/f1485565ef)] - **fs**: guarantee order of callbacks in ws.close (Matteo Collina) [#18002](https://github.com/nodejs/node/pull/18002)
- [[`66c1a038a1`](https://github.com/nodejs/node/commit/66c1a038a1)] - **gitignore**: ignore \*.VC.db files (Tobias Nießen) [#17898](https://github.com/nodejs/node/pull/17898)
- [[`8e1011f93b`](https://github.com/nodejs/node/commit/8e1011f93b)] - **http**: remove duplicate export (Evan Lucas) [#17982](https://github.com/nodejs/node/pull/17982)
- [[`f82439b6a0`](https://github.com/nodejs/node/commit/f82439b6a0)] - **(SEMVER-MINOR)** **http**: add rawPacket in err of `clientError` event (XadillaX) [#17672](https://github.com/nodejs/node/pull/17672)
- [[`9306de280f`](https://github.com/nodejs/node/commit/9306de280f)] - **http**: remove adapter frame from onParserExecute (Ben Noordhuis) [#17693](https://github.com/nodejs/node/pull/17693)
- [[`1ad7df6acc`](https://github.com/nodejs/node/commit/1ad7df6acc)] - **http2**: use aliased buffer for perf stats, add stats (James M Snell) [#18020](https://github.com/nodejs/node/pull/18020)
- [[`6a67dfd927`](https://github.com/nodejs/node/commit/6a67dfd927)] - **http2**: verify flood error and unsolicited frames (James M Snell) [#17969](https://github.com/nodejs/node/pull/17969)
- [[`6839283403`](https://github.com/nodejs/node/commit/6839283403)] - **http2**: verify that a dependency cycle may exist (James M Snell) [#17968](https://github.com/nodejs/node/pull/17968)
- [[`865da60e75`](https://github.com/nodejs/node/commit/865da60e75)] - **http2**: implement maxSessionMemory (James M Snell) [#17967](https://github.com/nodejs/node/pull/17967)
- [[`f17a5b92dc`](https://github.com/nodejs/node/commit/f17a5b92dc)] - **http2**: properly handle already closed stream error (James M Snell) [#17942](https://github.com/nodejs/node/pull/17942)
- [[`79d3198b7f`](https://github.com/nodejs/node/commit/79d3198b7f)] - **http2**: add aligned padding strategy (James M Snell) [#17938](https://github.com/nodejs/node/pull/17938)
- [[`2b6a5d90bd`](https://github.com/nodejs/node/commit/2b6a5d90bd)] - **http2**: add initial support for originSet (James M Snell) [#17935](https://github.com/nodejs/node/pull/17935)
- [[`9ad7a9a333`](https://github.com/nodejs/node/commit/9ad7a9a333)] - **http2**: add altsvc support (James M Snell) [#17917](https://github.com/nodejs/node/pull/17917)
- [[`e7a727e9ba`](https://github.com/nodejs/node/commit/e7a727e9ba)] - **http2**: strictly limit number on concurrent streams (James M Snell) [#16766](https://github.com/nodejs/node/pull/16766)
- [[`06aaaa8ad7`](https://github.com/nodejs/node/commit/06aaaa8ad7)] - **http2**: perf_hooks integration (James M Snell) [#17906](https://github.com/nodejs/node/pull/17906)
- [[`a003ded7fb`](https://github.com/nodejs/node/commit/a003ded7fb)] - **http2**: remove duplicate words in comments (Tobias Nießen) [#17939](https://github.com/nodejs/node/pull/17939)
- [[`1b7ce1ea02`](https://github.com/nodejs/node/commit/1b7ce1ea02)] - **http2**: implement ref() and unref() on client sessions (Kelvin Jin) [#17620](https://github.com/nodejs/node/pull/17620)
- [[`b8deb7522f`](https://github.com/nodejs/node/commit/b8deb7522f)] - **http2**: keep session objects alive during Http2Scope (Anna Henningsen) [#17863](https://github.com/nodejs/node/pull/17863)
- [[`e3c567f05b`](https://github.com/nodejs/node/commit/e3c567f05b)] - **http2**: fix compiling with `--debug-http2` (Anna Henningsen) [#17863](https://github.com/nodejs/node/pull/17863)
- [[`3a6b2ad19a`](https://github.com/nodejs/node/commit/3a6b2ad19a)] - **http2**: convert Http2Settings to an AsyncWrap (James M Snell) [#17763](https://github.com/nodejs/node/pull/17763)
- [[`bfc7e014cc`](https://github.com/nodejs/node/commit/bfc7e014cc)] - **http2**: refactor outgoing write mechanism (Anna Henningsen) [#17718](https://github.com/nodejs/node/pull/17718)
- [[`9592691d56`](https://github.com/nodejs/node/commit/9592691d56)] - **http2**: remove redundant write indirection (Anna Henningsen) [#17718](https://github.com/nodejs/node/pull/17718)
- [[`5abb60933e`](https://github.com/nodejs/node/commit/5abb60933e)] - **http2**: cleanup Http2Stream/Http2Session destroy (James M Snell) [#17406](https://github.com/nodejs/node/pull/17406)
- [[`f699a74e66`](https://github.com/nodejs/node/commit/f699a74e66)] - **http2**: be sure to destroy the Http2Stream (James M Snell) [#17406](https://github.com/nodejs/node/pull/17406)
- [[`30e75e601b`](https://github.com/nodejs/node/commit/30e75e601b)] - **http2**: only schedule write when necessary (Anna Henningsen) [#17183](https://github.com/nodejs/node/pull/17183)
- [[`d06ad0d4f0`](https://github.com/nodejs/node/commit/d06ad0d4f0)] - **http2**: don't call into JS from GC (Anna Henningsen) [#17183](https://github.com/nodejs/node/pull/17183)
- [[`f18d826660`](https://github.com/nodejs/node/commit/f18d826660)] - **http2**: simplify onSelectPadding (Anna Henningsen) [#17717](https://github.com/nodejs/node/pull/17717)
- [[`8d4fca3fb5`](https://github.com/nodejs/node/commit/8d4fca3fb5)] - **inspector**: make Coverity happy (Eugene Ostroukhov) [#17656](https://github.com/nodejs/node/pull/17656)
- [[`b817a8a6b2`](https://github.com/nodejs/node/commit/b817a8a6b2)] - **lib**: enable dot-notation eslint rule (Anatoli Papirovski) [#18007](https://github.com/nodejs/node/pull/18007)
- [[`2d61b9eb9f`](https://github.com/nodejs/node/commit/2d61b9eb9f)] - **lib, src**: use process.config instead of regex (Jon Moss) [#17814](https://github.com/nodejs/node/pull/17814)
- [[`3b2d8cba23`](https://github.com/nodejs/node/commit/3b2d8cba23)] - **module**: print better message on esm import error (Michaël Zasso) [#17786](https://github.com/nodejs/node/pull/17786)
- [[`79a283307a`](https://github.com/nodejs/node/commit/79a283307a)] - **n-api**: fix memory leak in napi_async_destroy() (alnyan) [#17714](https://github.com/nodejs/node/pull/17714)
- [[`74a5bbaff4`](https://github.com/nodejs/node/commit/74a5bbaff4)] - **net**: remove ADDRCONFIG DNS hint on Windows (Bartosz Sosnowski) [#17662](https://github.com/nodejs/node/pull/17662)
- [[`c3810e27bd`](https://github.com/nodejs/node/commit/c3810e27bd)] - **net**: remove Socket.prototype.write (Anna Henningsen) [#17644](https://github.com/nodejs/node/pull/17644)
- [[`e58a5ca854`](https://github.com/nodejs/node/commit/e58a5ca854)] - **net**: remove Socket.prototype.listen (Ruben Bridgewater) [#13735](https://github.com/nodejs/node/pull/13735)
- [[`0e116a01c8`](https://github.com/nodejs/node/commit/0e116a01c8)] - **perf_hooks**: fix scheduling regression (Anatoli Papirovski) [#18051](https://github.com/nodejs/node/pull/18051)
- [[`a329cf62ab`](https://github.com/nodejs/node/commit/a329cf62ab)] - **perf_hooks**: refactor internals (James M Snell) [#17822](https://github.com/nodejs/node/pull/17822)
- [[`bf0a7b6e13`](https://github.com/nodejs/node/commit/bf0a7b6e13)] - **process**: fix coverage generation (Evan Lucas) [#17651](https://github.com/nodejs/node/pull/17651)
- [[`b1bc768a57`](https://github.com/nodejs/node/commit/b1bc768a57)] - **readline**: refactor filter() callback (Rich Trott) [#17858](https://github.com/nodejs/node/pull/17858)
- [[`3831d87514`](https://github.com/nodejs/node/commit/3831d87514)] - **repl**: show lexically scoped vars in tab completion (Michaël Zasso) [#16591](https://github.com/nodejs/node/pull/16591)
- [[`2cc50530d2`](https://github.com/nodejs/node/commit/2cc50530d2)] - **repl**: fix coloring of `process.versions` (Ben Noordhuis) [#17861](https://github.com/nodejs/node/pull/17861)
- [[`bb9219bd19`](https://github.com/nodejs/node/commit/bb9219bd19)] - **src**: update make for new code coverage locations (Michael Dawson) [#17987](https://github.com/nodejs/node/pull/17987)
- [[`aa7519095c`](https://github.com/nodejs/node/commit/aa7519095c)] - **src**: remove duplicate words in comments (Tobias Nießen) [#17939](https://github.com/nodejs/node/pull/17939)
- [[`f9c84c557f`](https://github.com/nodejs/node/commit/f9c84c557f)] - **src**: silence http2 -Wunused-result warnings (cjihrig) [#17954](https://github.com/nodejs/node/pull/17954)
- [[`7e680807f8`](https://github.com/nodejs/node/commit/7e680807f8)] - **src**: add optional keep-alive object to SetImmediate (Anna Henningsen) [#17183](https://github.com/nodejs/node/pull/17183)
- [[`98dc554a2a`](https://github.com/nodejs/node/commit/98dc554a2a)] - **src**: inline HostentToAddresses() (Ben Noordhuis) [#17860](https://github.com/nodejs/node/pull/17860)
- [[`87b336a2e5`](https://github.com/nodejs/node/commit/87b336a2e5)] - **src**: remove unused GetHostByNameWrap (Ben Noordhuis) [#17860](https://github.com/nodejs/node/pull/17860)
- [[`2aa75a1f0b`](https://github.com/nodejs/node/commit/2aa75a1f0b)] - **src**: remove redundant `JSStream::DoAfterWrite` (Anna Henningsen) [#17713](https://github.com/nodejs/node/pull/17713)
- [[`99c62cc454`](https://github.com/nodejs/node/commit/99c62cc454)] - **src**: remove unused async hooks methods (Anna Henningsen) [#17757](https://github.com/nodejs/node/pull/17757)
- [[`d6c588586a`](https://github.com/nodejs/node/commit/d6c588586a)] - **src**: remove nonexistent method from header file (Anna Henningsen) [#17748](https://github.com/nodejs/node/pull/17748)
- [[`a93ed5c282`](https://github.com/nodejs/node/commit/a93ed5c282)] - **src**: replace SetAccessor w/ SetAccessorProperty (Jure Triglav) [#17665](https://github.com/nodejs/node/pull/17665)
- [[`d84d9be6ef`](https://github.com/nodejs/node/commit/d84d9be6ef)] - **src**: rename `On*` -\> `Emit*` for stream callbacks (Anna Henningsen) [#17701](https://github.com/nodejs/node/pull/17701)
- [[`6f520e3f69`](https://github.com/nodejs/node/commit/6f520e3f69)] - **src**: remove unused strings from env.h (Anna Henningsen) [#17643](https://github.com/nodejs/node/pull/17643)
- [[`6634dc4d0c`](https://github.com/nodejs/node/commit/6634dc4d0c)] - **src**: fix -Wundefined-inline warnings (Ben Noordhuis) [#17649](https://github.com/nodejs/node/pull/17649)
- [[`0c6d9ae72e`](https://github.com/nodejs/node/commit/0c6d9ae72e)] - **src**: fix compile warnings introduced in 73ad3f9bea (Ben Noordhuis) [#17649](https://github.com/nodejs/node/pull/17649)
- [[`008336c920`](https://github.com/nodejs/node/commit/008336c920)] - **src**: minor refactoring to StreamBase writes (Anna Henningsen) [#17564](https://github.com/nodejs/node/pull/17564)
- [[`7ed9e5de39`](https://github.com/nodejs/node/commit/7ed9e5de39)] - **src**: remove `StreamResourc::Cast()` (Anna Henningsen) [#17564](https://github.com/nodejs/node/pull/17564)
- [[`d879b63077`](https://github.com/nodejs/node/commit/d879b63077)] - **src**: make FSEventWrap/StatWatcher::Start more robust (Timothy Gu) [#17432](https://github.com/nodejs/node/pull/17432)
- [[`6ba00b8d48`](https://github.com/nodejs/node/commit/6ba00b8d48)] - **src**: refactor and harden `ProcessEmitWarning()` (Anna Henningsen) [#17420](https://github.com/nodejs/node/pull/17420)
- [[`316da5e667`](https://github.com/nodejs/node/commit/316da5e667)] - **src**: use correct OOB check for IPv6 parsing (Anna Henningsen) [#17470](https://github.com/nodejs/node/pull/17470)
- [[`ca3c2551b6`](https://github.com/nodejs/node/commit/ca3c2551b6)] - **src**: make url host a proper C++ class (Anna Henningsen) [#17470](https://github.com/nodejs/node/pull/17470)
- [[`9f1fe63c39`](https://github.com/nodejs/node/commit/9f1fe63c39)] - **src**: move url internals into anonymous namespace (Anna Henningsen) [#17470](https://github.com/nodejs/node/pull/17470)
- [[`75f99b7c16`](https://github.com/nodejs/node/commit/75f99b7c16)] - **src**: minor cleanups to node_url.cc (Anna Henningsen) [#17470](https://github.com/nodejs/node/pull/17470)
- [[`6bd0aff092`](https://github.com/nodejs/node/commit/6bd0aff092)] - **src**: remove unused variable in node_contextify (Daniel Bevenius) [#17491](https://github.com/nodejs/node/pull/17491)
- [[`df6acf9a84`](https://github.com/nodejs/node/commit/df6acf9a84)] - **src**: remove tracking for exception arrow data (Anna Henningsen) [#17394](https://github.com/nodejs/node/pull/17394)
- [[`e63e4a1fac`](https://github.com/nodejs/node/commit/e63e4a1fac)] - **src**: remove async_hooks destroy timer handle (Anna Henningsen) [#17117](https://github.com/nodejs/node/pull/17117)
- [[`e1f0846a2b`](https://github.com/nodejs/node/commit/e1f0846a2b)] - **src**: introduce internal C++ SetImmediate() mechanism (Anna Henningsen) [#17117](https://github.com/nodejs/node/pull/17117)
- [[`7d1d7390eb`](https://github.com/nodejs/node/commit/7d1d7390eb)] - **src**: fix inspector nullptr deref on abrupt exit (Ben Noordhuis) [#17577](https://github.com/nodejs/node/pull/17577)
- [[`c5c4a534d1`](https://github.com/nodejs/node/commit/c5c4a534d1)] - **(SEMVER-MINOR)** **stream**: rm {writeable/readable}State.length (Calvin Metcalf) [#12857](https://github.com/nodejs/node/pull/12857)
- [[`4b0c8759d3`](https://github.com/nodejs/node/commit/4b0c8759d3)] - **(SEMVER-MINOR)** **stream**: add flow and buffer properties to streams (Calvin Metcalf) [#12855](https://github.com/nodejs/node/pull/12855)
- [[`757e685803`](https://github.com/nodejs/node/commit/757e685803)] - **stream**: remove `undefined` check (Anna Henningsen) [#17644](https://github.com/nodejs/node/pull/17644)
- [[`b313e81783`](https://github.com/nodejs/node/commit/b313e81783)] - **test**: fix flaky test-http-pipeline-flood (Anatoli Papirovski) [#17955](https://github.com/nodejs/node/pull/17955)
- [[`51eab4b005`](https://github.com/nodejs/node/commit/51eab4b005)] - **test**: rename regression tests (Tobias Nießen) [#17948](https://github.com/nodejs/node/pull/17948)
- [[`8806e54c24`](https://github.com/nodejs/node/commit/8806e54c24)] - **test**: fix flaky test-http-highwatermark (Anatoli Papirovski) [#17949](https://github.com/nodejs/node/pull/17949)
- [[`3399e8ac5a`](https://github.com/nodejs/node/commit/3399e8ac5a)] - **test**: fix flaky test-pipe-unref (Anatoli Papirovski) [#17950](https://github.com/nodejs/node/pull/17950)
- [[`79980582b4`](https://github.com/nodejs/node/commit/79980582b4)] - **test**: fix flaky http-writable-true-after-close (Anatoli Papirovski) [#17952](https://github.com/nodejs/node/pull/17952)
- [[`591dd4e398`](https://github.com/nodejs/node/commit/591dd4e398)] - **test**: fix crypto test case to use correct encoding (Tobias Nießen) [#17956](https://github.com/nodejs/node/pull/17956)
- [[`f87a1a6ca8`](https://github.com/nodejs/node/commit/f87a1a6ca8)] - **test**: simplify test-buffer-slice.js (Weijia Wang) [#17962](https://github.com/nodejs/node/pull/17962)
- [[`3cc9882e8c`](https://github.com/nodejs/node/commit/3cc9882e8c)] - **test**: fix flaky test-resolve-async (Anatoli Papirovski) [#17957](https://github.com/nodejs/node/pull/17957)
- [[`3927c6f64e`](https://github.com/nodejs/node/commit/3927c6f64e)] - **test**: improve readability of some crypto tests (Tobias Nießen) [#17904](https://github.com/nodejs/node/pull/17904)
- [[`2f4da8b801`](https://github.com/nodejs/node/commit/2f4da8b801)] - **test**: use countdown in test file (sreepurnajasti) [#17874](https://github.com/nodejs/node/pull/17874)
- [[`ef533c99ba`](https://github.com/nodejs/node/commit/ef533c99ba)] - **test**: add hasCrypto when using binding('crypto') (Daniel Bevenius) [#17867](https://github.com/nodejs/node/pull/17867)
- [[`421eb750b2`](https://github.com/nodejs/node/commit/421eb750b2)] - **test**: improve to use template string (sreepurnajasti) [#17895](https://github.com/nodejs/node/pull/17895)
- [[`275970973e`](https://github.com/nodejs/node/commit/275970973e)] - **test**: replace map() with forEach() where appropriate (Rich Trott) [#17858](https://github.com/nodejs/node/pull/17858)
- [[`f25bab5606`](https://github.com/nodejs/node/commit/f25bab5606)] - **test**: fix flaky test-benchmark-fs (Rich Trott) [#17885](https://github.com/nodejs/node/pull/17885)
- [[`411e7724d4`](https://github.com/nodejs/node/commit/411e7724d4)] - **test**: make test-tls-invoke-queued use public API (Anna Henningsen) [#17864](https://github.com/nodejs/node/pull/17864)
- [[`1dd859d413`](https://github.com/nodejs/node/commit/1dd859d413)] - **test**: refactor test-tls-securepair-fiftharg (Anna Henningsen) [#17836](https://github.com/nodejs/node/pull/17836)
- [[`8b666d61c7`](https://github.com/nodejs/node/commit/8b666d61c7)] - **test**: reduce scope of variable in common module (Rich Trott) [#17830](https://github.com/nodejs/node/pull/17830)
- [[`9110654965`](https://github.com/nodejs/node/commit/9110654965)] - **test**: remove undefined function (Rich Trott) [#17845](https://github.com/nodejs/node/pull/17845)
- [[`ca35d08291`](https://github.com/nodejs/node/commit/ca35d08291)] - **test**: remove ambiguous error messages from test_error (Nicholas Drane) [#17812](https://github.com/nodejs/node/pull/17812)
- [[`ee4cbac52b`](https://github.com/nodejs/node/commit/ee4cbac52b)] - **test**: fix unreliable async-hooks/test-signalwrap (Rich Trott) [#17827](https://github.com/nodejs/node/pull/17827)
- [[`fea5d08d65`](https://github.com/nodejs/node/commit/fea5d08d65)] - **test**: fix flaky test-benchmark-fs (Rich Trott) [#17853](https://github.com/nodejs/node/pull/17853)
- [[`ded097a2bb`](https://github.com/nodejs/node/commit/ded097a2bb)] - **test**: use common module API in test-child-process-exec-stdout-stderr-data-string (sreepurnajasti) [#17751](https://github.com/nodejs/node/pull/17751)
- [[`06862f0c32`](https://github.com/nodejs/node/commit/06862f0c32)] - **test**: do not open fixture files for writing (Rich Trott) [#17810](https://github.com/nodejs/node/pull/17810)
- [[`e9ace7e4dd`](https://github.com/nodejs/node/commit/e9ace7e4dd)] - **test**: do not open fixture files for writing (Rich Trott) [#17808](https://github.com/nodejs/node/pull/17808)
- [[`f79d2efedb`](https://github.com/nodejs/node/commit/f79d2efedb)] - **test**: use valid authentication tag length (Tobias Nießen) [#17566](https://github.com/nodejs/node/pull/17566)
- [[`112b655107`](https://github.com/nodejs/node/commit/112b655107)] - **test**: improve flaky test-listen-fd-ebadf.js (Rich Trott) [#17797](https://github.com/nodejs/node/pull/17797)
- [[`dce7d7fc64`](https://github.com/nodejs/node/commit/dce7d7fc64)] - **test**: refactor test-repl-definecommand (Rich Trott) [#17795](https://github.com/nodejs/node/pull/17795)
- [[`60ae55680c`](https://github.com/nodejs/node/commit/60ae55680c)] - **test**: refactor test-net-connect-buffer (Anna Henningsen) [#17710](https://github.com/nodejs/node/pull/17710)
- [[`c9539678ca`](https://github.com/nodejs/node/commit/c9539678ca)] - **test**: increase diffie-hellman test coverage (Leko) [#17728](https://github.com/nodejs/node/pull/17728)
- [[`6d15185235`](https://github.com/nodejs/node/commit/6d15185235)] - **test**: increase pbkdf2 test coverage (Leko) [#17730](https://github.com/nodejs/node/pull/17730)
- [[`dd14004eed`](https://github.com/nodejs/node/commit/dd14004eed)] - **test**: fix typo in test-inspector-cluster-port-clash.js (Rich Trott) [#17782](https://github.com/nodejs/node/pull/17782)
- [[`5a9694eb60`](https://github.com/nodejs/node/commit/5a9694eb60)] - **test**: change callback function to arrow function (rt33) [#17734](https://github.com/nodejs/node/pull/17734)
- [[`305dd5671c`](https://github.com/nodejs/node/commit/305dd5671c)] - **test**: add test for postmortem metadata validation (cjihrig) [#17685](https://github.com/nodejs/node/pull/17685)
- [[`d9190c17ed`](https://github.com/nodejs/node/commit/d9190c17ed)] - **test**: Use countdown in test file (sreepurnajasti) [#17646](https://github.com/nodejs/node/pull/17646)
- [[`46f8a9eddc`](https://github.com/nodejs/node/commit/46f8a9eddc)] - **test**: update test-http-content-length to use countdown (Bamieh) [#17201](https://github.com/nodejs/node/pull/17201)
- [[`373d5df3b7`](https://github.com/nodejs/node/commit/373d5df3b7)] - **test**: coverage for emitExperimentalWarning (Mithun Sasidharan) [#17635](https://github.com/nodejs/node/pull/17635)
- [[`bc45354cce`](https://github.com/nodejs/node/commit/bc45354cce)] - **test**: change callback function to arrow function (routerman) [#17697](https://github.com/nodejs/node/pull/17697)
- [[`d48a1b99ee`](https://github.com/nodejs/node/commit/d48a1b99ee)] - **test**: change callback function to arrow function (you12724) [#17698](https://github.com/nodejs/node/pull/17698)
- [[`a9d83ce9e0`](https://github.com/nodejs/node/commit/a9d83ce9e0)] - **test**: change callback function to arrow function (Shinya Kanamaru) [#17699](https://github.com/nodejs/node/pull/17699)
- [[`bdddb82595`](https://github.com/nodejs/node/commit/bdddb82595)] - **test**: check socketOnDrain where needPause is false (Leko) [#17654](https://github.com/nodejs/node/pull/17654)
- [[`b8265285ff`](https://github.com/nodejs/node/commit/b8265285ff)] - **test**: fix flaky test-benchmark-misc (Rich Trott) [#17686](https://github.com/nodejs/node/pull/17686)
- [[`b1fd50a773`](https://github.com/nodejs/node/commit/b1fd50a773)] - **test**: remove literals that obscure assert messages (Rich Trott) [#17642](https://github.com/nodejs/node/pull/17642)
- [[`f16eca4383`](https://github.com/nodejs/node/commit/f16eca4383)] - **test**: improve coverage for util.promisify (Mithun Sasidharan) [#17591](https://github.com/nodejs/node/pull/17591)
- [[`97eaaf907f`](https://github.com/nodejs/node/commit/97eaaf907f)] - **test**: remove unused disposed\_ variable (Daniel Bevenius) [#17628](https://github.com/nodejs/node/pull/17628)
- [[`cc683bd0cb`](https://github.com/nodejs/node/commit/cc683bd0cb)] - **test**: expand test-https-keep-alive-large-write (Anna Henningsen) [#17564](https://github.com/nodejs/node/pull/17564)
- [[`6cb4cc2f1c`](https://github.com/nodejs/node/commit/6cb4cc2f1c)] - **test**: fix flaky test-child-process-pass-fd (Rich Trott) [#17598](https://github.com/nodejs/node/pull/17598)
- [[`5cd08d3a59`](https://github.com/nodejs/node/commit/5cd08d3a59)] - **test**: add unhandled rejection guard (babygoat) [#17275](https://github.com/nodejs/node/pull/17275)
- [[`b379d8d105`](https://github.com/nodejs/node/commit/b379d8d105)] - **test**: improve crypto/random.js coverage (Leko) [#17555](https://github.com/nodejs/node/pull/17555)
- [[`bc7dc65229`](https://github.com/nodejs/node/commit/bc7dc65229)] - **test**: add test description to fs.readFile tests (Jamie Davis) [#17610](https://github.com/nodejs/node/pull/17610)
- [[`70588f7f21`](https://github.com/nodejs/node/commit/70588f7f21)] - **test**: simplify common.expectsError (Ruben Bridgewater) [#17616](https://github.com/nodejs/node/pull/17616)
- [[`fb640c66cb`](https://github.com/nodejs/node/commit/fb640c66cb)] - **timers**: remove domain enter and exit (Anatoli Papirovski) [#17880](https://github.com/nodejs/node/pull/17880)
- [[`3997617869`](https://github.com/nodejs/node/commit/3997617869)] - **tls**: set servername on client side too (James M Snell) [#17935](https://github.com/nodejs/node/pull/17935)
- [[`e69ea78974`](https://github.com/nodejs/node/commit/e69ea78974)] - **tls**: fix SNICallback without .server option (Anna Henningsen) [#17835](https://github.com/nodejs/node/pull/17835)
- [[`b44f245b14`](https://github.com/nodejs/node/commit/b44f245b14)] - **tls**: comment about old-style errors (xortiz) [#17759](https://github.com/nodejs/node/pull/17759)
- [[`41702ef457`](https://github.com/nodejs/node/commit/41702ef457)] - **tls**: unconsume stream on destroy (Anna Henningsen) [#17478](https://github.com/nodejs/node/pull/17478)
- [[`5514330406`](https://github.com/nodejs/node/commit/5514330406)] - **tls**: use correct class name in deprecation message (Anna Henningsen) [#17561](https://github.com/nodejs/node/pull/17561)
- [[`4dacff72b5`](https://github.com/nodejs/node/commit/4dacff72b5)] - **tools**: do not override V8's gitignore (Yang Guo) [#18010](https://github.com/nodejs/node/pull/18010)
- [[`adc59a3e71`](https://github.com/nodejs/node/commit/adc59a3e71)] - **tools**: host remark-preset-lint-node in-tree (Jon Moss) [#17441](https://github.com/nodejs/node/pull/17441)
- [[`c91a7c09ae`](https://github.com/nodejs/node/commit/c91a7c09ae)] - **tools**: add check for using process.binding crypto (Daniel Bevenius) [#17867](https://github.com/nodejs/node/pull/17867)
- [[`4391ea4a57`](https://github.com/nodejs/node/commit/4391ea4a57)] - **tools**: enable array-callback-return ESLint rule (Rich Trott) [#17858](https://github.com/nodejs/node/pull/17858)
- [[`b89cda4cbd`](https://github.com/nodejs/node/commit/b89cda4cbd)] - **tools**: fix AttributeError: \_\_exit\_\_ on Python 2.6 (Dmitriy Kasyanov) [#17663](https://github.com/nodejs/node/pull/17663)
- [[`2d07243cac`](https://github.com/nodejs/node/commit/2d07243cac)] - **tools**: autofixer for lowercase-name-for-primitive (Shobhit Chittora) [#17715](https://github.com/nodejs/node/pull/17715)
- [[`7ef876d89d`](https://github.com/nodejs/node/commit/7ef876d89d)] - **tools**: fix man pages linking regex (Diego Rodríguez Baquero) [#17724](https://github.com/nodejs/node/pull/17724)
- [[`6531401cde`](https://github.com/nodejs/node/commit/6531401cde)] - **tools**: add number-isnan rule (Jon Moss) [#17556](https://github.com/nodejs/node/pull/17556)
- [[`eaa2d9116a`](https://github.com/nodejs/node/commit/eaa2d9116a)] - **tools**: simplify lowercase-name-for-primitive rule (cjihrig) [#17653](https://github.com/nodejs/node/pull/17653)
- [[`3ad8cf14f5`](https://github.com/nodejs/node/commit/3ad8cf14f5)] - **tools**: add lowercase-name-for-primitive eslint rule (Weijia Wang) [#17568](https://github.com/nodejs/node/pull/17568)
- [[`7bf6be0b7c`](https://github.com/nodejs/node/commit/7bf6be0b7c)] - **trace_events**: stop tracing agent in process.exit() (Andreas Madsen) [#18005](https://github.com/nodejs/node/pull/18005)
- [[`ed7f59a1ee`](https://github.com/nodejs/node/commit/ed7f59a1ee)] - **url**: added url fragment lookup table (Hakan Kimeiga) [#17627](https://github.com/nodejs/node/pull/17627)
- [[`28ef3de2ba`](https://github.com/nodejs/node/commit/28ef3de2ba)] - **url**: added space to class string of iterator objects (Haejin Jo) [#17558](https://github.com/nodejs/node/pull/17558)
- [[`6d9b1e4c83`](https://github.com/nodejs/node/commit/6d9b1e4c83)] - **util**: allow wildcards in NODE_DEBUG variable (Tyler) [#17609](https://github.com/nodejs/node/pull/17609)
- [[`6cc622f01b`](https://github.com/nodejs/node/commit/6cc622f01b)] - **vm**: allow modifying context name in inspector (Timothy Gu) [#17720](https://github.com/nodejs/node/pull/17720)
- [[`e2767114ff`](https://github.com/nodejs/node/commit/e2767114ff)] - **vm**: never abort on caught syntax error (Anna Henningsen) [#17394](https://github.com/nodejs/node/pull/17394)
- [[`7bf4102db9`](https://github.com/nodejs/node/commit/7bf4102db9)] - **win, build**: fix without-intl option (Bartosz Sosnowski) [#17614](https://github.com/nodejs/node/pull/17614)
- [[`584e74d8cc`](https://github.com/nodejs/node/commit/584e74d8cc)] - **(SEMVER-MINOR)** **zlib**: add ArrayBuffer support (Jem Bezooyen) [#16042](https://github.com/nodejs/node/pull/16042)

Windows 32-bit Installer: https://nodejs.org/dist/v9.4.0/node-v9.4.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v9.4.0/node-v9.4.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v9.4.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v9.4.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v9.4.0/node-v9.4.0.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v9.4.0/node-v9.4.0-darwin-x64.tar.gz \
Linux 32-bit Binary: https://nodejs.org/dist/v9.4.0/node-v9.4.0-linux-x86.tar.xz \
Linux 64-bit Binary: https://nodejs.org/dist/v9.4.0/node-v9.4.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v9.4.0/node-v9.4.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v9.4.0/node-v9.4.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v9.4.0/node-v9.4.0-aix-ppc64.tar.gz \
SmartOS 32-bit Binary: https://nodejs.org/dist/v9.4.0/node-v9.4.0-sunos-x86.tar.xz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v9.4.0/node-v9.4.0-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v9.4.0/node-v9.4.0-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v9.4.0/node-v9.4.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v9.4.0/node-v9.4.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v9.4.0/node-v9.4.0.tar.gz \
Other release files: https://nodejs.org/dist/v9.4.0/ \
Documentation: https://nodejs.org/docs/v9.4.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

8498d7e2c5293e697303c721ba465f34ea3898dea78ed9f33ab257a3da1eb3be  node-v9.4.0-aix-ppc64.tar.gz
fa271c6012d517851603829af2131b92dc7b03d6f167dcd197cb83468a4971e8  node-v9.4.0-darwin-x64.tar.gz
6f7cc91c7effc13fe3200f33f8927c9835e55e3479e825c5e74eceb3450f6d5e  node-v9.4.0-darwin-x64.tar.xz
8fcd2213a90c0a9c09e8af86261958eb2ece0decf14aaf004e2c2852f3cf2a5b  node-v9.4.0-headers.tar.gz
087660c1598358b98d5dea17ff47cfa111738dbb970d2340c3a741db1747bcf9  node-v9.4.0-headers.tar.xz
a0d4ac74d607b58755848e871a86ae76ae69cb75f56fa77b3c26fec34db781eb  node-v9.4.0-linux-arm64.tar.gz
ceed69e91723cb902fcf70e790d34147a91241509257c77a62327871dfad04fa  node-v9.4.0-linux-arm64.tar.xz
b6b8dac6d20dfe1a83dc2179c2beab1d8dc9571ee69057edaf7f4bea1b6f3566  node-v9.4.0-linux-armv6l.tar.gz
1538e11cdfafc5abb6cb39f1ae7818e0700a7bd21c64b73b5b269705d7725ba7  node-v9.4.0-linux-armv6l.tar.xz
134a54b5746e603b65b612ff15171a6a8ee77328dbf9ff51277a4742fdc1786c  node-v9.4.0-linux-armv7l.tar.gz
ba4961aacc47ff1874ea1d079a074cdef406a9cd1d22945abcbc683c94285d54  node-v9.4.0-linux-armv7l.tar.xz
29ca9c2238067be59d59123c8454469d0bc8ca8ffeeeff9389986b9e2f1d6d10  node-v9.4.0-linux-ppc64le.tar.gz
261f6efddc4deceeba0909e2e5981dcd36cdadde7f8b56d5113d716d4eedfbc4  node-v9.4.0-linux-ppc64le.tar.xz
82ea6cdad94d116aa41f23e034643af98075fb35c1c3e84c2a787cf75c6455f5  node-v9.4.0-linux-s390x.tar.gz
5a26890ad3e8a6d1ccfaa645c0e268984feb60d91244404a691bc96c9ad3de8a  node-v9.4.0-linux-s390x.tar.xz
ca0dc28e45f300c10a0a75dee65439f50014ed710550f2d1246891503627a278  node-v9.4.0-linux-x64.tar.gz
6d331d75a39fc5292dd128ad83f6dd14bbbdcb84ba0dfe793fade833be5de95a  node-v9.4.0-linux-x64.tar.xz
7201e5d9d90b33696d5e9b2619f98288a559f54ebb1989c29c52e54706242c12  node-v9.4.0-linux-x86.tar.gz
97c00aa4e62752ca4a32e889860b72d388fddb792d6bef5b2f67ca5ba37447f5  node-v9.4.0-linux-x86.tar.xz
5c14556b2f846fb5ef74c9a7a6ddb547777bfc2dac65f21e609c9400b7d9a487  node-v9.4.0.pkg
b68acd42d2791169f2d2937db48082b07cb75b14ca513937ad81ae8218296069  node-v9.4.0-sunos-x64.tar.gz
71756b2246122ec6e9c281caeb8612d0cd4405889f3c7313983095ffc2c141ae  node-v9.4.0-sunos-x64.tar.xz
9fa547b086a81bc6a1bbb8c90f82743fabe2704c29093f6d6086dbe61321972e  node-v9.4.0-sunos-x86.tar.gz
896cd73b759743cbecec085297c70ea155493096082f66b2620af79508757cc3  node-v9.4.0-sunos-x86.tar.xz
240733d272c87d593bf807618db73ef9682a6b765ed9911c05ea3ab6e221a967  node-v9.4.0.tar.gz
7503e1f0f81288ff6e56009c0f399c0b5ebfe6f446734c5beb2d45393b21b20c  node-v9.4.0.tar.xz
7ae0a1667ac3a71579785b93978c0f6b1996af2b9a9f09fa1b14e2019848824f  node-v9.4.0-win-x64.7z
89d49d73eb92483af0133c97e57a5b521c523960c130c76727daa3412af7d5d8  node-v9.4.0-win-x64.zip
38afcac73b8d8fa21724a68e7839bdb48b8cb7fcec409c09e042ce0ef41a675c  node-v9.4.0-win-x86.7z
1b1323db1ce07fc49c9cc312d1a79649b23cfdccfdaf06723e970ff5e62c1459  node-v9.4.0-win-x86.zip
1570cf95433b5fa860128926edfc2f950cc227c77863def80328598f81ee5a91  node-v9.4.0-x64.msi
be349639cc2b44819087e61f5e293c09a3c93ce81c7aa61715bfd598698647c0  node-v9.4.0-x86.msi
cfe7b0b8b4d0b9e21d7684d2e32b5d845dc8e783e87a7acf6e2b623a44a149fb  win-x64/node.exe
67f802faf4a0d953c5bee25244b75fb6ae63445e1e18959911429f766c9499ef  win-x64/node.lib
9ebcb5a3efafa7303ace3427c6d05bf8ed5bd3ce44747a6a7778f8f06d2f42af  win-x64/node_pdb.7z
13595dc4d02a1e44fe009fe5daa14051e14f23850b7c4fa45f7fd2db361f7ec8  win-x64/node_pdb.zip
ab93139b3d694b20e35d6b1b27c373728b28f3ec09797994a00fcfb648224dd5  win-x86/node.exe
d0f11d71de0186f98c37b426e76e1d31186a8089c68d6db69ef444a904bd0529  win-x86/node.lib
308cd5f7e4efdd4974ac0281db3cb65e944fae80507895ca94bada430c84b931  win-x86/node_pdb.7z
4514ce00509e396e3ca45a4982c50790e08d1bd3a43ef16b27fe4606f5922a7a  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQEzBAEBCAAdFiEEDv/hvO/ZyE49CYFSkzsB9AtcqUYFAlpWL/YACgkQkzsB9Atc
qUYfiggAocwsWFnnQbyN4ebKjmw3vw5hvCxAG9kr4ptUgmOPWWBRQ3ENb9oQV3ol
nCZezRGn2f82iXtxWa66Gyz283Nq8VAQheyzZTyzS6s7BMksKB9ZcJGgcoZ70b9D
K/K2gb86d+/try/Hp/VXSk8xaR/Cf9lhdzP/Q3QgKOBv7TqjIJlGU/7xZcX3+KOD
F8U+ovnIsg+T1NVV+PMxdMOiT2Bp312ERBx3Aut1qlwLxYHedQssnDBdflPYiIIw
zBlign14EKyT0Sr5XJnFE1UknHcIAvr0LQr/i2EzjDnXaCS6ncVxmBInuv7DWeyP
r0v8UPbi8EYHrpZoDSxVquPjFpTUQQ==
=uHxL
-----END PGP SIGNATURE-----

```
