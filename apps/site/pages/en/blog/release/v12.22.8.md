---
date: '2021-12-16T23:42:46.809Z'
category: release
title: Node v12.22.8 (LTS)
layout: blog-post
author: <PERSON>
---

### Notable Changes

This release contains a c-ares update to fix a regression introduced in
Node.js 12.22.5 resolving CNAME records containing underscores
[#39780](https://github.com/nodejs/node/issues/39780).

Root certificates have been updated to those from Mozilla's Network
Security Services 3.71 [#40281](https://github.com/nodejs/node/pull/40280).

### Commits

- \[[`2d42295d2a`](https://github.com/nodejs/node/commit/2d42295d2a)] - **build**: pin macOS GitHub runner to macos-10.15 (<PERSON>) [#41124](https://github.com/nodejs/node/pull/41124)
- \[[`41e09ec71b`](https://github.com/nodejs/node/commit/41e09ec71b)] - **child_process**: retain reference to data with advanced serialization (<PERSON>) [#38728](https://github.com/nodejs/node/pull/38728)
- \[[`f0be07796e`](https://github.com/nodejs/node/commit/f0be07796e)] - **crypto**: update root certificates (Richard Lau) [#40280](https://github.com/nodejs/node/pull/40280)
- \[[`4c9f920d34`](https://github.com/nodejs/node/commit/4c9f920d34)] - **deps**: update archs files for OpenSSL-1.1.1m (Richard Lau) [#41172](https://github.com/nodejs/node/pull/41172)
- \[[`60d7d4171e`](https://github.com/nodejs/node/commit/60d7d4171e)] - **deps**: upgrade openssl sources to 1.1.1m (Richard Lau) [#41172](https://github.com/nodejs/node/pull/41172)
- \[[`7feff67419`](https://github.com/nodejs/node/commit/7feff67419)] - **deps**: add -fno-strict-aliasing flag to libuv (Daniel Bevenius) [#40631](https://github.com/nodejs/node/pull/40631)
- \[[`534ac7c7c6`](https://github.com/nodejs/node/commit/534ac7c7c6)] - **deps**: update c-ares to 1.18.1 (Richard Lau) [#40660](https://github.com/nodejs/node/pull/40660)
- \[[`c019fa9b70`](https://github.com/nodejs/node/commit/c019fa9b70)] - **deps**: update to cjs-module-lexer\@1.2.2 (Guy Bedford) [#39402](https://github.com/nodejs/node/pull/39402)
- \[[`b13340eff4`](https://github.com/nodejs/node/commit/b13340eff4)] - **doc**: add alternative version links to the packages page (Filip Skokan) [#36915](https://github.com/nodejs/node/pull/36915)
- \[[`243b2fbfdb`](https://github.com/nodejs/node/commit/243b2fbfdb)] - **lib**: fix regular expression to detect \`/\` and \`\\\` (Francesco Trotta) [#40325](https://github.com/nodejs/node/pull/40325)
- \[[`70e094a26b`](https://github.com/nodejs/node/commit/70e094a26b)] - **repl**: fix error message printing (Anna Henningsen) [#38209](https://github.com/nodejs/node/pull/38209)
- \[[`02b432a704`](https://github.com/nodejs/node/commit/02b432a704)] - **src**: fix crash in AfterGetAddrInfo (Anna Henningsen) [#39735](https://github.com/nodejs/node/pull/39735)
- \[[`7479447d6a`](https://github.com/nodejs/node/commit/7479447d6a)] - **test**: deflake child-process-pipe-dataflow (Luigi Pinca) [#40838](https://github.com/nodejs/node/pull/40838)
- \[[`833e199393`](https://github.com/nodejs/node/commit/833e199393)] - **tools**: update certdata.txt (Richard Lau) [#40280](https://github.com/nodejs/node/pull/40280)
- \[[`e4339fe286`](https://github.com/nodejs/node/commit/e4339fe286)] - **tools**: add script to update c-ares (Richard Lau) [#40660](https://github.com/nodejs/node/pull/40660)
- \[[`f50b9c1e8a`](https://github.com/nodejs/node/commit/f50b9c1e8a)] - **worker**: avoid potential deadlock on NearHeapLimit (Santiago Gimeno) [#38403](https://github.com/nodejs/node/pull/38403)

Windows 32-bit Installer: https://nodejs.org/dist/v12.22.8/node-v12.22.8-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v12.22.8/node-v12.22.8-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v12.22.8/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v12.22.8/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v12.22.8/node-v12.22.8.pkg \
macOS Intel 64-bit Binary: https://nodejs.org/dist/v12.22.8/node-v12.22.8-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v12.22.8/node-v12.22.8-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v12.22.8/node-v12.22.8-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v12.22.8/node-v12.22.8-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v12.22.8/node-v12.22.8-aix-ppc64.tar.gz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v12.22.8/node-v12.22.8-sunos-x64.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v12.22.8/node-v12.22.8-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v12.22.8/node-v12.22.8-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v12.22.8/node-v12.22.8.tar.gz \
Other release files: https://nodejs.org/dist/v12.22.8/ \
Documentation: https://nodejs.org/docs/v12.22.8/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

e96e6df9a493c60e8d95ce9affb0b7404ea646050236995fc42ba3226e6f80a1  node-v12.22.8-aix-ppc64.tar.gz
53478fac030a1fc6f6044ba46b6cc194c8b9b33c9a5f68cdd05f98bd644bc50c  node-v12.22.8-darwin-x64.tar.gz
a0329d02a4f43c66f4d673968c91ef20eb4e0b1c46d6e13e57eaf3a812a02d90  node-v12.22.8-darwin-x64.tar.xz
2d619fd73b96e068ace1c73c7f114b7b494abaf211f7ad25e7bcb45c6222be25  node-v12.22.8-headers.tar.gz
ebef3145fa05020830a22744bddb29cf3f6617826fddc0151e8c7017da90a969  node-v12.22.8-headers.tar.xz
64b9d84cf202ce73f5a87d3dbbc56791f65ec31f3f77c7210713eeffb58d5dd5  node-v12.22.8-linux-arm64.tar.gz
7ffaa44a8ca391c0e4cbd25c40d66225f8da37c4db5572ffef6a69ee681ee694  node-v12.22.8-linux-arm64.tar.xz
69e24e091760e92dfe8720ce01c8cac502e1ea2ef3351a0993eb1bb50a5a71fa  node-v12.22.8-linux-armv7l.tar.gz
19b48897b48eb5320c3aca1cc1f904cd3f76ccbca04ed32336d281d38d3129c4  node-v12.22.8-linux-armv7l.tar.xz
054bb089df90b475956b6e023f08757ed5fc0d42695976d0490c5812e382d4bb  node-v12.22.8-linux-ppc64le.tar.gz
a02ceff8b41f530ff32422aaf67543053477dc8acd6e0e9ba19635b60a9ebebd  node-v12.22.8-linux-ppc64le.tar.xz
3a29916e50d1884c738ae54035c5b7300040ec0c6a77d231f3c500039c45f923  node-v12.22.8-linux-s390x.tar.gz
f6d92a1c2224cffcdfa96fcf183271227773eaa83404571a953b906b5f6a5a23  node-v12.22.8-linux-s390x.tar.xz
d3dbcdb0141ebdb923eec3245962ebaf2245052c9f2987195d8ac118f5abdd1c  node-v12.22.8-linux-x64.tar.gz
523b4efe0d81d7647a7340143fe30764e0e970bdedce6f0f5fb64f3a46382907  node-v12.22.8-linux-x64.tar.xz
c8399e56f34ad470d236eb32da1b8b0d1983e87f498223d270ad653109866672  node-v12.22.8.pkg
555e3206e7d69d3068700374cfcfbf2b19a65a4d3998c0040762bf9556ad111e  node-v12.22.8-sunos-x64.tar.gz
078fa21ad6fa81b9469bb2e22d3d0a3061e8d1355b4b4b9f4dbd25ca96fbcfb9  node-v12.22.8-sunos-x64.tar.xz
387dc25af4064f86ce4d28810c00589279e75c38f7d42eed9c067b2ac66fbe77  node-v12.22.8.tar.gz
2785cb1da108130083224b60e4b94009a153b26ef34bb22afc5180b6be84113c  node-v12.22.8.tar.xz
dc4f7808c66d12847264c33c5d3bd7d329ef1cb4d1960fb223de586e5fecfb71  node-v12.22.8-win-x64.7z
3714ea0ae016b36b5bc4bf75d8f897f41adbcfd5276e99f7a78df67f4bd72f85  node-v12.22.8-win-x64.zip
782c8c9b25d771e9564635dbda338997bf1b9dde26ddf42ca4aa894295f659ac  node-v12.22.8-win-x86.7z
dfe7e55d0fa44e0fa71482bbebfdebf4087fb0c5b92b8dbe9c94325ced4ca2ce  node-v12.22.8-win-x86.zip
64b5f7c17c3552194801905ebbc32b3208c2482a66cbbc0f3c6fb70459737743  node-v12.22.8-x64.msi
9c7f14a0807e26879e4f1cc085248ba7d2257aba3e6e2c7427951cc943b43962  node-v12.22.8-x86.msi
dc6c1eeaf3885e608d00c96d1ee97509a9d95315ff6f87fa0622fc7f2c1b0166  win-x64/node.exe
28e5c24831deedbf4fb8a9560f2c4f95205479c589f54a9a53ec346f6a5cf8bf  win-x64/node.lib
937439e20c08e59487a587b89b69e8934206365792f5ae7dddae50cc3b06095a  win-x64/node_pdb.7z
70e4d989d4c4e5e51c788c66b4cd85798fa2e01af312b371fc4c406e8654a6c2  win-x64/node_pdb.zip
2b5a145e97d0e41c72168913bab29c75b42804e348e68472097145e0d054197f  win-x86/node.exe
dad0e6bef1c45f4f43fbf84c33df6b910ace8122eff3f8d39d5ebecd25320ba4  win-x86/node.lib
1a238cf06718c197a03c8d4ae9af8ad0078f8e561ceec09f5030596cb5c8e73c  win-x86/node_pdb.7z
6324b8bf2181502198855cada0132c71cf7e677e735f67ffdc700b98d56201e3  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEyC+jrhy+3Gvka5NgxDzsRcF6uTwFAmG7zdMACgkQxDzsRcF6
uTzR3hAAjlqpmMRHrxHEmJGv0ffXrT+rLFC3bTF1LnKGT98Q+QFMbeX+DieHOIm0
pMFd+wJt73E2tv6eNRbCOYfODcL9cFY9ZK2PAw+jKXgyEBFTH4GXq44WB5homk7L
6LkfF6W4Ql6luIpQU+AY+hyLCBXvUhKZPU4TyZEuS7Sz3yT7qgel4vU2pdCro7l5
NRsbkLFBjVCDI2rpWafhYXddcLF7/gyuGrl2JnqhDJ78c5Pnnw1Ix9MGP8K6MMaO
6MAd5mz+/h5KzS44EHpCbmTotmsom1fCGywVx9/HGcUDDTwTS9RZRXYh7KJ2uyC0
xNj0x4lllnhZbx/Z/NVbQ/8I8DIUi5Sz1/d+IBPwUKZRxhu6zQJa4ESpTUTLM9AW
jyaRoyhLo0ymel2RqXw4rSI8zfU8KiilP7k96ZIeVmvLaGRLO8VLWk1qA7qA8STi
BUM8ZDIG45N8oYRsqx9ACYcDwE2YtV9ekJdl6cQ7o9JWlDZNSAAKNkckUXviXdHD
VSCTzjYUCD5gZG/r44zEtU+UzxVe8A5bGdlHlZA5MVIsUlUuKgjGV8sUxjSrJ3WS
B49WVYhQ9ntjJzpucYOM/UPGOSiKe1RojV9l9uyVZ6TBdBwNl7UAwYsAA94EDVkj
JevlPIworQGlw2oMP2QQH2uUg5TL3aHsY87K+xruoO6/8eG6DOU=
=If7J
-----END PGP SIGNATURE-----

```
