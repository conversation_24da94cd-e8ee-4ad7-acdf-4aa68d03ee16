---
date: '2023-06-20T20:33:42.579Z'
category: release
title: Node v18.16.1 (LTS)
layout: blog-post
author: <PERSON>
---

### Notable Changes

The following CVEs are fixed in this release:

- [CVE-2023-30581](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2023-30581): `mainModule.__proto__` Bypass Experimental Policy Mechanism (High)
- [CVE-2023-30585](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2023-30585): Privilege escalation via Malicious Registry Key manipulation during Node.js installer repair process (Medium)
- [CVE-2023-30588](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2023-30588): Process interuption due to invalid Public Key information in x509 certificates (Medium)
- [CVE-2023-30589](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2023-30589): HTTP Request Smuggling via Empty headers separated by CR (Medium)
- [CVE-2023-30590](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2023-30590): DiffieHellman does not generate keys after setting a private key (Medium)
- OpenSSL Security Releases
  - [OpenSSL security advisory 28th March](https://www.openssl.org/news/secadv/20230328.txt).
  - [OpenSSL security advisory 20th April](https://www.openssl.org/news/secadv/20230420.txt).
  - [OpenSSL security advisory 30th May](https://www.openssl.org/news/secadv/20230530.txt)
- c-ares vulnerabilities:
  - [GHSA-9g78-jv2r-p7vc](https://github.com/c-ares/c-ares/security/advisories/GHSA-9g78-jv2r-p7vc)
  - [GHSA-8r8p-23f3-64c2](https://github.com/c-ares/c-ares/security/advisories/GHSA-8r8p-23f3-64c2)
  - [GHSA-54xr-f67r-4pc4](https://github.com/c-ares/c-ares/security/advisories/GHSA-54xr-f67r-4pc4)
  - [GHSA-x6mf-cxr9-8q6v](https://github.com/c-ares/c-ares/security/advisories/GHSA-x6mf-cxr9-8q6v)

More detailed information on each of the vulnerabilities can be found in [June 2023 Security Releases](/blog/vulnerability/june-2023-security-releases/) blog post.

### Commits

- \[[`bf3e2c8928`](https://github.com/nodejs/node/commit/bf3e2c8928)] - **crypto**: handle cert with invalid SPKI gracefully (Tobias Nießen) [nodejs-private/node-private#393](https://github.com/nodejs-private/node-private/pull/393)
- \[[`70f9449072`](https://github.com/nodejs/node/commit/70f9449072)] - **deps**: set `CARES_RANDOM_FILE` for c-ares (Richard Lau) [#48156](https://github.com/nodejs/node/pull/48156)
- \[[`35d4efb57b`](https://github.com/nodejs/node/commit/35d4efb57b)] - **deps**: update c-ares to 1.19.1 (RafaelGSS) [#48115](https://github.com/nodejs/node/pull/48115)
- \[[`392dfedc77`](https://github.com/nodejs/node/commit/392dfedc77)] - **deps**: update archs files for openssl-3.0.9-quic1 (Node.js GitHub Bot) [#48402](https://github.com/nodejs/node/pull/48402)
- \[[`46cd5fe38b`](https://github.com/nodejs/node/commit/46cd5fe38b)] - **deps**: upgrade openssl sources to quictls/openssl-3.0.9-quic1 (Node.js GitHub Bot) [#48402](https://github.com/nodejs/node/pull/48402)
- \[[`7e3d2d85c2`](https://github.com/nodejs/node/commit/7e3d2d85c2)] - **doc,test**: clarify behavior of DH generateKeys (Tobias Nießen) [nodejs-private/node-private#426](https://github.com/nodejs-private/node-private/pull/426)
- \[[`4ff6ba050a`](https://github.com/nodejs/node/commit/4ff6ba050a)] - **http**: disable request smuggling via rempty headers (Paolo Insogna) [nodejs-private/node-private#428](https://github.com/nodejs-private/node-private/pull/428)
- \[[`ab269129a6`](https://github.com/nodejs/node/commit/ab269129a6)] - **msi**: do not create AppData\Roaming\npm (Tobias Nießen) [nodejs-private/node-private#408](https://github.com/nodejs-private/node-private/pull/408)
- \[[`925e8f5619`](https://github.com/nodejs/node/commit/925e8f5619)] - **policy**: handle mainModule.\_\_proto\_\_ bypass (RafaelGSS) [nodejs-private/node-private#416](https://github.com/nodejs-private/node-private/pull/416)
- \[[`d6fae8e47e`](https://github.com/nodejs/node/commit/d6fae8e47e)] - **test**: allow SIGBUS in signal-handler abort test (Michaël Zasso) [#47851](https://github.com/nodejs/node/pull/47851)

Windows 32-bit Installer: https://nodejs.org/dist/v18.16.1/node-v18.16.1-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v18.16.1/node-v18.16.1-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v18.16.1/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v18.16.1/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v18.16.1/node-v18.16.1.pkg \
macOS Apple Silicon 64-bit Binary: https://nodejs.org/dist/v18.16.1/node-v18.16.1-darwin-arm64.tar.gz \
macOS Intel 64-bit Binary: https://nodejs.org/dist/v18.16.1/node-v18.16.1-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v18.16.1/node-v18.16.1-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v18.16.1/node-v18.16.1-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v18.16.1/node-v18.16.1-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v18.16.1/node-v18.16.1-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v18.16.1/node-v18.16.1-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v18.16.1/node-v18.16.1-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v18.16.1/node-v18.16.1.tar.gz \
Other release files: https://nodejs.org/dist/v18.16.1/ \
Documentation: https://nodejs.org/docs/v18.16.1/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

9473b9a1224da16e376c3c8a7ee58cc1d0b76c7ec4caa98bf5fdc35bfe039935  node-v18.16.1-aix-ppc64.tar.gz
2ccb24e9211f4d17d8d8cfc0ea521198bb6a54e2f779f8feda952dbd3bb651ac  node-v18.16.1-darwin-arm64.tar.gz
fcdcc60732c0c9495ff3a156adec35107f69840fc664138318c5757704f2a8a9  node-v18.16.1-darwin-arm64.tar.xz
3040210287a0b8d05af49f57de191afa783e497abbb10c340bae9158cb51fdd4  node-v18.16.1-darwin-x64.tar.gz
4e2dba5746803c2f1bc32ffb9b0b671cc5efde641b45fae21044cae02418c7e1  node-v18.16.1-darwin-x64.tar.xz
8c72f0861237731a3e292a4be2b6a5b3c0f5114afe3a3a02b5fad35d7b3b2cc2  node-v18.16.1-headers.tar.gz
eaf3c9e3f91b046234b93f42be481c2b6fb7cf1bd795be1e7fb6507d8e03f18e  node-v18.16.1-headers.tar.xz
555b5c521e068acc976e672978ba0f5b1a0c030192b50639384c88143f4460bc  node-v18.16.1-linux-arm64.tar.gz
144eb4103e0193de8a41187817261d35970f1a13a11e779e16a4f1d9c99bcc82  node-v18.16.1-linux-arm64.tar.xz
ffac5b7627b086b16376751b641cb5c429f94cedf9a5f0f6cfc3cbe7aa0e6b89  node-v18.16.1-linux-armv7l.tar.gz
38ff8afb88e47e73311b0495d2e88998e464421669da5b2fb3fc7bb0b28d6e48  node-v18.16.1-linux-armv7l.tar.xz
4c4928f8b1b01c2a93ebf0eba2e179c63e97bca103339dc4152405531ca5c738  node-v18.16.1-linux-ppc64le.tar.gz
2dacb61734393758554155a2f62dac420b85aaa552f874ed4f6127b58e89bd0f  node-v18.16.1-linux-ppc64le.tar.xz
76a94fe99d7d229256c12254c8575ece370d6bc24d7fcdb548610872a8bc2b7c  node-v18.16.1-linux-s390x.tar.gz
f0f0527d31f2714ee9f5c8de771605f8855543807764859d416a1d4f8d969efd  node-v18.16.1-linux-s390x.tar.xz
59582f51570d0857de6333620323bdeee5ae36107318f86ce5eca24747cabf5b  node-v18.16.1-linux-x64.tar.gz
ecfe263dbd9c239f37b5adca823b60be1bb57feabbccd25db785e647ebc5ff5e  node-v18.16.1-linux-x64.tar.xz
ed730fa8724fabdb33fe34a2d1f1f98d5b36ec1a7a1006ebdaad60f185ffee25  node-v18.16.1.pkg
518f4c8dc23a26d8df77288d94e254b6015d792026ff9ba0c2316af255875fa5  node-v18.16.1.tar.gz
e8404f8c8d89fdfdf7e95bbbc6066bd0e571acba58f54492599b615fbeefe272  node-v18.16.1.tar.xz
9c4415ae27452f47a547ad162ca4ab21dc6c5f2edd6656307e8150d1e3d19f08  node-v18.16.1-win-x64.7z
145bd2f79eaa50b76559bd78266f4585e57b88dbb94613698a9514a601f84e7f  node-v18.16.1-win-x64.zip
466025104f9c321ed7c83fdaafccbc9b3e24cea5bb9fdc9f4a07cc5cd2096019  node-v18.16.1-win-x86.7z
950022d45729588421a535df7075c0b48fea26c41b66d545a300b2db67d949dc  node-v18.16.1-win-x86.zip
e1902d304867a9bf650c36073c25765a0d8dde00165f83f7e4229c598f3a5476  node-v18.16.1-x64.msi
3e6e447ecbf954332ba21fc02e9053ddb8d6e24a49f4421ac1e1229c1661a9a4  node-v18.16.1-x86.msi
605544a8a76439f328f9caa86fc519201a902f09d37b5ee8501e0d61ee08a622  win-x64/node.exe
72b7fab9381af8f4958c8212f3d4cdfff8c7c5b1e33eaad0e7d5888293568cd5  win-x64/node.lib
e547de68e630967719aba1a6510d0ac1c02690fe1e95f4ef08ad3f92cca8e035  win-x64/node_pdb.7z
81d167acd95c4b71bbdc4fc33f3e3c14156074df1a729351454969a1825721e5  win-x64/node_pdb.zip
d2cbc13d3cf1959d63c37aaefde410ae01f0af3496c73def733d19bbfc7d3b5f  win-x86/node.exe
6a85c15a69238f0902b9a734d262bf36d211b273a46d5e3249857d4bb7f6d9b7  win-x86/node.lib
7c88fd05f848ad8cdaf12f01054720ddd2a933fdca8611091c7aa0a3c2f3374e  win-x86/node_pdb.7z
658a2553f244c6a9e0177230979ed9a465be93f21485f8c70e6a98ef41a7817c  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQGzBAEBCAAdFiEEiQwI24V5Fi/uDfnbi+q0389VXvQFAmSSDPUACgkQi+q0389V
XvQUrQv/XEvcQK/tNCmM46qVPy3Tfm1ZdItHZ86q/y/Y/NNfxvUZFwwgZjJ9d/7M
+kyMb3EQFPBncLQ9Deny+eJRVaJ+CV7fiqGyYypjHbgtb0OG1J0dYqiG+mAP4gkz
74AzTsQeDzOMe4XUM0+Agppa1cC6Y4h2S+C+jSrF5qp1rx4rlMhNHowWOA5PA+4l
vvmC7NRYef5qgoOd63MpE8mA9HMQPDP8HmtAKUmwJVhAdL4jPLYzf6XSeoYDTZBR
2HU0RcyRhvp/aESV2gE5Ia5ad3eHYjtAusDjeYF5z/XZYLSg6BXskc7C623JC3VR
lErZowD3A1D0HR2eo17Be5mLaTuQcw2NiacR2rzu2q5p+rsxonNLUfeFPgEzXu+F
HVxNFsL/Nb+bRdwQ/Eqcho9MTpspTIc8c518msZ6a7Yil/rlKVJnY/r6q/QHUIrl
r2Qinj/qPOm0fmNR5AKfom8GHKuOIcm4E7M7dVtx5rllQIIlhGHMErpQU+1hDlys
760wiW8U
=JjYe
-----END PGP SIGNATURE-----

```
