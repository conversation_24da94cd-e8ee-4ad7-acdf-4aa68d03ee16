---
date: '2018-11-28T00:42:34.845Z'
category: release
title: Node v11.3.0 (Current)
layout: blog-post
author: <PERSON>
---

**This is a security release.** All Node.js users should consult the security release summary at /blog/vulnerability/november-2018-security-releases/ for details on patched vulnerabilities.

Fixes for the following CVEs are included in this release:

- Node.js: Denial of Service with large HTTP headers (CVE-2018-12121)
- Node.js: Slowloris HTTP Denial of Service (CVE-2018-12122 / Node.js)
- Node.js: Hostname spoofing in URL parser for javascript protocol (CVE-2018-12123)
- OpenSSL: Timing vulnerability in DSA signature generation (CVE-2018-0734)
- OpenSSL: Timing vulnerability in ECDSA signature generation (CVE-2019-0735)

### Notable Changes

- **deps**: Upgrade to OpenSSL 1.1.0j, fixing CVE-2018-0734 and CVE-2019-0735
- **http**:
  - Headers received by HTTP servers must not exceed 8192 bytes in total to prevent possible Denial of Service attacks. Reported by <PERSON>. (CVE-2018-12121 / <PERSON>)
  - A timeout of 40 seconds now applies to servers receiving HTTP headers. This value can be adjusted with `server.headersTimeout`. Where headers are not completely received within this period, the socket is destroyed on the next received chunk. In conjunction with `server.setTimeout()`, this aids in protecting against excessive resource retention and possible Denial of Service. Reported by Jan Maybach ([liebdich.com](https://liebdich.com)). (CVE-2018-12122 / Matteo Collina)
- **url**: Fix a bug that would allow a hostname being spoofed when parsing URLs with `url.parse()` with the `'javascript:'` protocol. Reported by [Martin Bajanik](https://twitter.com/_bayotop) ([Kentico](https://kenticocloud.com/)). (CVE-2018-12123 / Matteo Collina)

### Commits

- [[`8f191f3759`](https://github.com/nodejs/node/commit/8f191f3759)] - **deps**: update openssl 1.1.0 upgrade docs (Sam Roberts) [#24523](https://github.com/nodejs/node/pull/24523)
- [[`f20ac47d7a`](https://github.com/nodejs/node/commit/f20ac47d7a)] - **deps**: update archs files for OpenSSL-1.1.0 (Sam Roberts) [#24523](https://github.com/nodejs/node/pull/24523)
- [[`8248d227b7`](https://github.com/nodejs/node/commit/8248d227b7)] - **deps**: add s390 asm rules for OpenSSL-1.1.0 (Shigeki Ohtsu) [#24523](https://github.com/nodejs/node/pull/24523)
- [[`65d03f0180`](https://github.com/nodejs/node/commit/65d03f0180)] - **deps**: upgrade openssl sources to 1.1.0j (Sam Roberts) [#24523](https://github.com/nodejs/node/pull/24523)
- [[`a2b8aba23c`](https://github.com/nodejs/node/commit/a2b8aba23c)] - **deps,http**: llhttp set max header size to 8KB (Rod Vagg) [nodejs-private/node-private#149](https://github.com/nodejs-private/node-private/pull/149)
- [[`74e01d0020`](https://github.com/nodejs/node/commit/74e01d0020)] - **deps,http**: http_parser set max header size to 8KB (Matteo Collina) [nodejs-private/node-private#143](https://github.com/nodejs-private/node-private/pull/143)
- [[`4ecbd3bdaa`](https://github.com/nodejs/node/commit/4ecbd3bdaa)] - **http**: reset headers_nread\_ on llhttp parser reuse (Rod Vagg) [nodejs-private/node-private#149](https://github.com/nodejs-private/node-private/pull/149)
- [[`04e0620597`](https://github.com/nodejs/node/commit/04e0620597)] - **http**: fix header limit errors and test for llhttp (Fedor Indutny) [nodejs-private/node-private#149](https://github.com/nodejs-private/node-private/pull/149)
- [[`315ee2e626`](https://github.com/nodejs/node/commit/315ee2e626)] - **(SEMVER-MINOR)** **http,https**: protect against slow headers attack (Matteo Collina) [nodejs-private/node-private#144](https://github.com/nodejs-private/node-private/pull/144)
- [[`d7504324e1`](https://github.com/nodejs/node/commit/d7504324e1)] - **url**: avoid hostname spoofing w/ javascript protocol (Matteo Collina) [nodejs-private/node-private#145](https://github.com/nodejs-private/node-private/pull/145)

Windows 32-bit Installer: https://nodejs.org/dist/v11.3.0/node-v11.3.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v11.3.0/node-v11.3.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v11.3.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v11.3.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v11.3.0/node-v11.3.0.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v11.3.0/node-v11.3.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v11.3.0/node-v11.3.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v11.3.0/node-v11.3.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v11.3.0/node-v11.3.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v11.3.0/node-v11.3.0-aix-ppc64.tar.gz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v11.3.0/node-v11.3.0-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v11.3.0/node-v11.3.0-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v11.3.0/node-v11.3.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v11.3.0/node-v11.3.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v11.3.0/node-v11.3.0.tar.gz \
Other release files: https://nodejs.org/dist/v11.3.0/ \
Documentation: https://nodejs.org/docs/v11.3.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

37cd261b32d2c6e320d99d9ea5e3d57dc4efd0f279e2366563c2a1c58ea7bf5c  node-v11.3.0-aix-ppc64.tar.gz
54acc7bdeffae79fdd73f959712305aee1d8d487d56813b43cae96d151ec79db  node-v11.3.0-darwin-x64.tar.gz
8b5d9ea204126d01ae77804144f8f1719086b15abf24628a673b14665866f9dc  node-v11.3.0-darwin-x64.tar.xz
ec21aa45c8790e3ab8df531f760458222237a3b8fe61aeef0532d7a11d0aa2bd  node-v11.3.0-headers.tar.gz
7c5a8c50952288f2e059e77b5aa5dcb32d1f52f0bbc07683a77827de6d239589  node-v11.3.0-headers.tar.xz
e6e90080b95f780102980059ac3b2b2f7f6465f13ffa78d946f4c4df9ce97ff1  node-v11.3.0-linux-arm64.tar.gz
642cc3fc94a856ad6d09e76eaf869672bef925308afdad398a58f18eeaf4e4b8  node-v11.3.0-linux-arm64.tar.xz
9ba1cbaa7441173715eef1e81142fc131c609b9218f6168d152e2bae9f0e2875  node-v11.3.0-linux-armv6l.tar.gz
bc0e3cc5adb6360ba23910391b6a5f19b44bf750b24108b7ddd9f62ca2675fe6  node-v11.3.0-linux-armv6l.tar.xz
790a609e3355272ee243f9c9eee5530bf101cf7d2ba1f644de043362fea61dfb  node-v11.3.0-linux-armv7l.tar.gz
d6bce33d832363650bbf4c80367180ebaa8f71c4904acd608fa3e31127275591  node-v11.3.0-linux-armv7l.tar.xz
34b045a84d4731c9464d8c0d37f0fe894ca56e0003bc8cfdaabf69130ec4e490  node-v11.3.0-linux-ppc64le.tar.gz
d0499d85edf3293aa4c965ce0d32eb52249b2bba05142d46583d4d14440b6a06  node-v11.3.0-linux-ppc64le.tar.xz
02c2e8dbbd8ffe8b3b80c51a51545441cf005ac8540a74be787f91e7a02c778d  node-v11.3.0-linux-s390x.tar.gz
bc623c56da1fe9eea8b65571e6d451c70639f592da9191cd9263fca639e6443b  node-v11.3.0-linux-s390x.tar.xz
aac519aac1814e8590cd6b55fb2c6ddc1bbb825fc8c097abce5f5361aea61108  node-v11.3.0-linux-x64.tar.gz
d37fb7fae8a185409bccf106e91d8ffa3450115852795512fc62e6da0b5e3dbb  node-v11.3.0-linux-x64.tar.xz
d9de46dc842b10e0e73e9a8f9b78a891f224763fa943e593937c45666e1f9d78  node-v11.3.0.pkg
467bc51c794a27ef77a1c81128478c7558fcb8cd67edc59f42b6e29d902b29d9  node-v11.3.0-sunos-x64.tar.gz
3f4d37156edb83b70648819499c9184a72fbcb546129a243089ea5ece48c04c4  node-v11.3.0-sunos-x64.tar.xz
9db85052ec091a2a0ff6b928bed5030b6383846e8d677726648d042268169407  node-v11.3.0.tar.gz
08c4a159242af4c68752260d59ed209fc86b073ee669443fa591eecacb6093da  node-v11.3.0.tar.xz
a6a931e8c8f6dfd21f86da1ca14666a21069c4d033e39e734e787e4c50499bc6  node-v11.3.0-win-x64.7z
b801e908ec36a07f06df388845e22e0b7f3cede7a4030896712c8ee28cdb3f05  node-v11.3.0-win-x64.zip
8369634a081e77b5e21344a8dd57add942c760a8d1193175d0ee88b91b8e1902  node-v11.3.0-win-x86.7z
d3f4dbe65060bbec5ce02ea87880c81ec51494dad78b0cdd2bdd2efb29a91654  node-v11.3.0-win-x86.zip
2438bf1a549edfde2a1760692760b7b937533fa7a47b6554d2c28ee999ce03e5  node-v11.3.0-x64.msi
6550cf2519d3c76341900eb0cb0213d59933b7c6fcbcc84ca58691a80c4797c5  node-v11.3.0-x86.msi
cf75c2d68e73ec0b8ed43c437719a890956e3aaafda5b3f3e930a6c868e36098  win-x64/node.exe
9eab50d1f65165a3c9d685c5a05b0a957a46105f0d2179cb40b007e14464bc96  win-x64/node.lib
d49e907a1ca91f9483b8c6430c3323d9da7d30423c92242d29588334d08a4e06  win-x64/node_pdb.7z
1dc81c11fcea581de11f591f32e1b8a0fbd28b8dcf9140ab026cc744d277c568  win-x64/node_pdb.zip
50d631b43f94b7eca57fd5a8615aa74d4127a6188135a3ccc68365973dd33c20  win-x86/node.exe
d434239ba4597aaa820fd31126240bf1b327cf871a6b6108d891147b3112f728  win-x86/node.lib
b2d29aa640da3c70b5b57923d91fa27734776c45d771a9b5a3cb29e28995367a  win-x86/node_pdb.7z
65f5189ad28c9f59c77c4e1ff87763c6fcc0b3d5c39ba4969b1eeb4589b73b84  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQEzBAEBCAAdFiEE3Y8jOLrnUB491ax4wnN5L32DVF0FAlv95CwACgkQwnN5L32D
VF1Kpwf/TSV8EvpfiR6rYJ3GGdmmfmJregELYXz+Lt90C49jDbitI/8MSWMysGUa
P5qEdRcnSvW0wYNFbaVDFgtuMR7S3VwSKZ39ByKIaCP0e6ihb1IHuGC+0Iiy4vjx
or7cAT+lVe4Y06Vs0BwHlWUFI80V2zzBj2chkiWs6ZSbkxcs0AqCqlpxw+MtAYIz
GekqYj95BDStLJcP2n8Kow31Alkb9QF9NwL8J/8bPtPlBvmP/GRy//Bv5Y3+4GHy
iRJhiyVc8jReu+SpqDhPcS6VhBF9yv4flAGTGAmoM4Ky5xZ2B5AJzKtgYu2Ht94J
i16RRK7XOEeO1+iMLfGheyZ1gI1Jvw==
=62lQ
-----END PGP SIGNATURE-----

```
