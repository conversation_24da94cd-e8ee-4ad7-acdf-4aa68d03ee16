---
date: '2021-08-18T01:13:21.430Z'
category: release
title: Node v16.7.0 (Current)
layout: blog-post
author: <PERSON>
---

### Notable Changes

- **fs**:
  - experimental: add recursive cp method (<PERSON>) [#39372](https://github.com/nodejs/node/pull/39372)

### Commits

- [[`a80c989306`](https://github.com/nodejs/node/commit/a80c989306)] - **async_hooks**: merge resource_symbol with owner_symbol (<PERSON><PERSON>) [#38468](https://github.com/nodejs/node/pull/38468)
- [[`69a2a6b6c3`](https://github.com/nodejs/node/commit/69a2a6b6c3)] - **bootstrap**: call \_undestroy() inside \_destroy for stdout and stderr (<PERSON>) [#39685](https://github.com/nodejs/node/pull/39685)
- [[`5bc31ea0aa`](https://github.com/nodejs/node/commit/5bc31ea0aa)] - **buffer**: add endings option, remove Node.js specific encoding option (<PERSON> Snell) [#39708](https://github.com/nodejs/node/pull/39708)
- [[`091a579275`](https://github.com/nodejs/node/commit/091a579275)] - **(SEMVER-MINOR)** **buffer**: add Blob.prototype.stream method and other cleanups (James M Snell) [#39693](https://github.com/nodejs/node/pull/39693)
- [[`097d898e58`](https://github.com/nodejs/node/commit/097d898e58)] - **build**: run coverage for inspector protocol changes (Richard Lau) [#39725](https://github.com/nodejs/node/pull/39725)
- [[`cf028df0ed`](https://github.com/nodejs/node/commit/cf028df0ed)] - **build**: fix V8 build with pointer compression (Michaël Zasso) [#39664](https://github.com/nodejs/node/pull/39664)
- [[`9d38400de1`](https://github.com/nodejs/node/commit/9d38400de1)] - **build**: exclude markdown files from some GitHub Actions (Rich Trott) [#39565](https://github.com/nodejs/node/pull/39565)
- [[`eeb804a7b7`](https://github.com/nodejs/node/commit/eeb804a7b7)] - **build**: use lts shorthand in GitHub Actions (Rich Trott) [#39538](https://github.com/nodejs/node/pull/39538)
- [[`93a904d0ba`](https://github.com/nodejs/node/commit/93a904d0ba)] - **(SEMVER-MINOR)** **crypto**: implement webcrypto.randomUUID (Michaël Zasso) [#39648](https://github.com/nodejs/node/pull/39648)
- [[`3321b65a5a`](https://github.com/nodejs/node/commit/3321b65a5a)] - **debugger**: prevent simultaneous heap snapshots (Rich Trott) [#39638](https://github.com/nodejs/node/pull/39638)
- [[`6c375e18b6`](https://github.com/nodejs/node/commit/6c375e18b6)] - **debugger**: remove undefined parameter (Rich Trott) [#39570](https://github.com/nodejs/node/pull/39570)
- [[`103bf20988`](https://github.com/nodejs/node/commit/103bf20988)] - **deps**: V8: cherry-pick 81814ed44574 (Stephen Belanger) [#39719](https://github.com/nodejs/node/pull/39719)
- [[`cf5e5b5711`](https://github.com/nodejs/node/commit/cf5e5b5711)] - **deps**: upgrade to libuv 1.42.0 (Luigi Pinca) [#39525](https://github.com/nodejs/node/pull/39525)
- [[`5f92d2fe6d`](https://github.com/nodejs/node/commit/5f92d2fe6d)] - **dgram**: use simplified validator (Voltrex) [#39753](https://github.com/nodejs/node/pull/39753)
- [[`c7e918b06a`](https://github.com/nodejs/node/commit/c7e918b06a)] - **(SEMVER-MINOR)** **dns**: add "tries" option to Resolve options (Luan Devecchi) [#39610](https://github.com/nodejs/node/pull/39610)
- [[`5d66646b71`](https://github.com/nodejs/node/commit/5d66646b71)] - **doc**: correct cjs code to match mjs code (Raz Luvaton) [#39509](https://github.com/nodejs/node/pull/39509)
- [[`f18bb2a0f1`](https://github.com/nodejs/node/commit/f18bb2a0f1)] - **doc**: fix typo in hmac.paramNames default (Justin) [#39766](https://github.com/nodejs/node/pull/39766)
- [[`338a166e83`](https://github.com/nodejs/node/commit/338a166e83)] - **doc**: fix `fs.rmdir` `recursive` option deprecation history (Antoine du Hamel) [#39728](https://github.com/nodejs/node/pull/39728)
- [[`bfb1dc0a2c`](https://github.com/nodejs/node/commit/bfb1dc0a2c)] - **doc**: fixed variable names in queueMicrotask example (ashish maurya) [#39634](https://github.com/nodejs/node/pull/39634)
- [[`08b31f12f8`](https://github.com/nodejs/node/commit/08b31f12f8)] - **doc**: change "Version 4 UUID" to "version 4 UUID" (Tobias Nießen) [#39682](https://github.com/nodejs/node/pull/39682)
- [[`f5200f9785`](https://github.com/nodejs/node/commit/f5200f9785)] - **doc**: update debugger.md description and examples (Rich Trott) [#39661](https://github.com/nodejs/node/pull/39661)
- [[`4700f1e529`](https://github.com/nodejs/node/commit/4700f1e529)] - **doc**: fix color contrast issue in light mode (Rich Trott) [#39660](https://github.com/nodejs/node/pull/39660)
- [[`88c83a4698`](https://github.com/nodejs/node/commit/88c83a4698)] - **(SEMVER-MINOR)** **doc**: add missing change to resolver ctor (Luan Devecchi) [#39610](https://github.com/nodejs/node/pull/39610)
- [[`760cafa5ed`](https://github.com/nodejs/node/commit/760cafa5ed)] - **doc**: fix typo in `url.md` (Howie Zhao) [#39666](https://github.com/nodejs/node/pull/39666)
- [[`9ab5503693`](https://github.com/nodejs/node/commit/9ab5503693)] - **doc**: add point to ask H1 reporter about credit (Daniel Bevenius) [#39585](https://github.com/nodejs/node/pull/39585)
- [[`7514405456`](https://github.com/nodejs/node/commit/7514405456)] - **doc**: update min mac ver + move mac arm64 to tier 1 (Ash Cripps) [#39586](https://github.com/nodejs/node/pull/39586)
- [[`d7c8c6dcee`](https://github.com/nodejs/node/commit/d7c8c6dcee)] - **doc**: add missing `introduced_in` metadata (Richard Lau) [#39575](https://github.com/nodejs/node/pull/39575)
- [[`8072517097`](https://github.com/nodejs/node/commit/8072517097)] - **doc**: add code examples to `Writable.destroy()` and `Writable.destroyed` (Juan José Arboleda) [#39491](https://github.com/nodejs/node/pull/39491)
- [[`55f47cc2d0`](https://github.com/nodejs/node/commit/55f47cc2d0)] - **doc**: add `String.prototype.at` and `%TypedArray%.prototype.at` (Jordan Harband) [#39583](https://github.com/nodejs/node/pull/39583)
- [[`0c0412e2c4`](https://github.com/nodejs/node/commit/0c0412e2c4)] - **doc**: move `NODE_MODULE_VERSION` in release guide (Richard Lau) [#39544](https://github.com/nodejs/node/pull/39544)
- [[`5df74f9b21`](https://github.com/nodejs/node/commit/5df74f9b21)] - **doc**: remove outdated ARM information from release guide (Richard Lau) [#39544](https://github.com/nodejs/node/pull/39544)
- [[`8eccb11ea0`](https://github.com/nodejs/node/commit/8eccb11ea0)] - **doc**: fence command examples in release guide (Richard Lau) [#39544](https://github.com/nodejs/node/pull/39544)
- [[`0bd97e1f2d`](https://github.com/nodejs/node/commit/0bd97e1f2d)] - **doc**: update backport labels in release guide (Richard Lau) [#39544](https://github.com/nodejs/node/pull/39544)
- [[`2129ad6a0a`](https://github.com/nodejs/node/commit/2129ad6a0a)] - **doc**: add code example to `fs.truncate` method (Juan José Arboleda) [#39454](https://github.com/nodejs/node/pull/39454)
- [[`3ff5e153ef`](https://github.com/nodejs/node/commit/3ff5e153ef)] - **doc**: add code example to `http.createServer` method (Juan José Arboleda) [#39455](https://github.com/nodejs/node/pull/39455)
- [[`7d0c869cfa`](https://github.com/nodejs/node/commit/7d0c869cfa)] - **doc**: add PerformanceObserver `buffered` document (legendecas) [#39514](https://github.com/nodejs/node/pull/39514)
- [[`0dc167a03f`](https://github.com/nodejs/node/commit/0dc167a03f)] - **(SEMVER-MINOR)** **fs**: add recursive cp method (Benjamin Coe) [#39372](https://github.com/nodejs/node/pull/39372)
- [[`54dd3df943`](https://github.com/nodejs/node/commit/54dd3df943)] - **http**: decodes url.username and url.password for authorization header (Lew Gordon) [#39310](https://github.com/nodejs/node/pull/39310)
- [[`81e62f67bf`](https://github.com/nodejs/node/commit/81e62f67bf)] - **inspector**: update inspector_protocol to 89c4adf (Rich Trott) [#39650](https://github.com/nodejs/node/pull/39650)
- [[`793fee4915`](https://github.com/nodejs/node/commit/793fee4915)] - **inspector**: update inspector_protocol to 8ec18cf (Rich Trott) [#39614](https://github.com/nodejs/node/pull/39614)
- [[`5afdc1f4c0`](https://github.com/nodejs/node/commit/5afdc1f4c0)] - **lib**: simplify validators (Voltrex) [#39753](https://github.com/nodejs/node/pull/39753)
- [[`ca3cb96d25`](https://github.com/nodejs/node/commit/ca3cb96d25)] - **lib**: cleanup validation (Voltrex) [#39652](https://github.com/nodejs/node/pull/39652)
- [[`cc08d3062f`](https://github.com/nodejs/node/commit/cc08d3062f)] - **lib**: cleanup instance validation (Voltrex) [#39656](https://github.com/nodejs/node/pull/39656)
- [[`2751cdf6f9`](https://github.com/nodejs/node/commit/2751cdf6f9)] - **lib**: use helper for readability (Voltrex) [#39649](https://github.com/nodejs/node/pull/39649)
- [[`c68415cba2`](https://github.com/nodejs/node/commit/c68415cba2)] - **lib**: use validators (Voltrex) [#39663](https://github.com/nodejs/node/pull/39663)
- [[`be2d60dd1d`](https://github.com/nodejs/node/commit/be2d60dd1d)] - **lib**: use validator (Voltrex) [#39547](https://github.com/nodejs/node/pull/39547)
- [[`486d51ac0c`](https://github.com/nodejs/node/commit/486d51ac0c)] - **lib**: use `validateObject` (Voltrex) [#39605](https://github.com/nodejs/node/pull/39605)
- [[`058e882a2a`](https://github.com/nodejs/node/commit/058e882a2a)] - **lib**: use ERR_ILLEGAL_CONSTRUCTOR (Mestery) [#39556](https://github.com/nodejs/node/pull/39556)
- [[`07cadc4432`](https://github.com/nodejs/node/commit/07cadc4432)] - **meta**: consolidate AUTHORS entries for ooHmartY (Rich Trott) [#39705](https://github.com/nodejs/node/pull/39705)
- [[`6c788b8030`](https://github.com/nodejs/node/commit/6c788b8030)] - **meta**: consolidate AUTHORS entries for homosaur (Rich Trott) [#39705](https://github.com/nodejs/node/pull/39705)
- [[`07351edebe`](https://github.com/nodejs/node/commit/07351edebe)] - **meta**: consolidate AUTHORS entries for Ayase-252 (Rich Trott) [#39705](https://github.com/nodejs/node/pull/39705)
- [[`5fe282769b`](https://github.com/nodejs/node/commit/5fe282769b)] - **meta**: consolidate AUTHORS entries for robin-drexler (Rich Trott) [#39705](https://github.com/nodejs/node/pull/39705)
- [[`fc2a626357`](https://github.com/nodejs/node/commit/fc2a626357)] - **meta**: consolidate AUTHORS entries for samshull (Rich Trott) [#39705](https://github.com/nodejs/node/pull/39705)
- [[`67cfc66a47`](https://github.com/nodejs/node/commit/67cfc66a47)] - **meta**: update AUTHORS (Rich Trott) [#39705](https://github.com/nodejs/node/pull/39705)
- [[`91008fbdeb`](https://github.com/nodejs/node/commit/91008fbdeb)] - **meta**: consolidate email addresses for MarshallOfSound (Rich Trott) [#39651](https://github.com/nodejs/node/pull/39651)
- [[`a76b63536a`](https://github.com/nodejs/node/commit/a76b63536a)] - **meta**: consolidate email addresses for tadjik1 (Rich Trott) [#39651](https://github.com/nodejs/node/pull/39651)
- [[`aaab2095db`](https://github.com/nodejs/node/commit/aaab2095db)] - **meta**: consolidate email addresses for szmarczak (Rich Trott) [#39651](https://github.com/nodejs/node/pull/39651)
- [[`f413a9d83c`](https://github.com/nodejs/node/commit/f413a9d83c)] - **meta**: update AUTHORS (Rich Trott) [#39636](https://github.com/nodejs/node/pull/39636)
- [[`7a91d4bfe9`](https://github.com/nodejs/node/commit/7a91d4bfe9)] - **meta**: simplify mailmap (Rich Trott) [#39612](https://github.com/nodejs/node/pull/39612)
- [[`4ec5d2de5d`](https://github.com/nodejs/node/commit/4ec5d2de5d)] - **meta**: consolidate emails for tadhgcreedon (Rich Trott) [#39611](https://github.com/nodejs/node/pull/39611)
- [[`bb88c38eac`](https://github.com/nodejs/node/commit/bb88c38eac)] - **meta**: consolidate emails for timcosta (Rich Trott) [#39611](https://github.com/nodejs/node/pull/39611)
- [[`0920a8cf6f`](https://github.com/nodejs/node/commit/0920a8cf6f)] - **meta**: consolidate emails for timruffles (Rich Trott) [#39611](https://github.com/nodejs/node/pull/39611)
- [[`1474a9d4b1`](https://github.com/nodejs/node/commit/1474a9d4b1)] - **meta**: update AUTHORS (Rich Trott) [#39629](https://github.com/nodejs/node/pull/39629)
- [[`c59e3ec685`](https://github.com/nodejs/node/commit/c59e3ec685)] - **meta**: add mailmap entry for ryzokuken (Rich Trott) [#39596](https://github.com/nodejs/node/pull/39596)
- [[`34f4bb8277`](https://github.com/nodejs/node/commit/34f4bb8277)] - **meta**: add mailmap entry for uttampawar (Rich Trott) [#39596](https://github.com/nodejs/node/pull/39596)
- [[`fd213edda2`](https://github.com/nodejs/node/commit/fd213edda2)] - **meta**: add mailmap entry for dmabupt (Rich Trott) [#39596](https://github.com/nodejs/node/pull/39596)
- [[`6b664e224b`](https://github.com/nodejs/node/commit/6b664e224b)] - **meta**: align README/.mailmap/AUTHORS email entries (Rich Trott) [#39505](https://github.com/nodejs/node/pull/39505)
- [[`96d8ecbd66`](https://github.com/nodejs/node/commit/96d8ecbd66)] - **meta**: add mailmap entry for garygsc (Rich Trott) [#39588](https://github.com/nodejs/node/pull/39588)
- [[`16d85f3f48`](https://github.com/nodejs/node/commit/16d85f3f48)] - **meta**: add mailmap entry for ttzztztz (Rich Trott) [#39588](https://github.com/nodejs/node/pull/39588)
- [[`60ab111fdb`](https://github.com/nodejs/node/commit/60ab111fdb)] - **meta**: update AUTHORS (Rich Trott) [#39587](https://github.com/nodejs/node/pull/39587)
- [[`b43f87d729`](https://github.com/nodejs/node/commit/b43f87d729)] - **meta**: update .mailmap to remove duplication in AUTHORS (Rich Trott) [#39561](https://github.com/nodejs/node/pull/39561)
- [[`6f4a2aa5a4`](https://github.com/nodejs/node/commit/6f4a2aa5a4)] - **meta**: add .mailmap entries to remove AUTHORS duplicates (Rich Trott) [#39560](https://github.com/nodejs/node/pull/39560)
- [[`86d144c500`](https://github.com/nodejs/node/commit/86d144c500)] - **meta**: add .mailmap entry to remove duplication in AUTHORS (Rich Trott) [#39559](https://github.com/nodejs/node/pull/39559)
- [[`110c088f02`](https://github.com/nodejs/node/commit/110c088f02)] - **meta**: update collaborator email in AUTHORS/.mailmap (Rich Trott) [#39521](https://github.com/nodejs/node/pull/39521)
- [[`72af147bb5`](https://github.com/nodejs/node/commit/72af147bb5)] - **meta**: update collaborator email in README (Rich Trott) [#39521](https://github.com/nodejs/node/pull/39521)
- [[`23bc4cfb21`](https://github.com/nodejs/node/commit/23bc4cfb21)] - **meta**: update collaborator email in AUTHORS/.mailmap (Rich Trott) [#39521](https://github.com/nodejs/node/pull/39521)
- [[`e4289728c7`](https://github.com/nodejs/node/commit/e4289728c7)] - **meta**: move gdams to emeritus (Rich Trott) [#39539](https://github.com/nodejs/node/pull/39539)
- [[`4df59bc727`](https://github.com/nodejs/node/commit/4df59bc727)] - **module**: add some typings to `internal/modules/esm/resolve` (Antoine du Hamel) [#39504](https://github.com/nodejs/node/pull/39504)
- [[`b5858589d0`](https://github.com/nodejs/node/commit/b5858589d0)] - **node-api**: handle pending exception in cb wrapper (Michael Dawson) [#39476](https://github.com/nodejs/node/pull/39476)
- [[`016b7ba616`](https://github.com/nodejs/node/commit/016b7ba616)] - **perf_hooks**: fix PerformanceObserver gc crash (James M Snell) [#39550](https://github.com/nodejs/node/pull/39550)
- [[`b37575b67c`](https://github.com/nodejs/node/commit/b37575b67c)] - **perf_hooks**: fix performance timeline wpt failures (legendecas) [#39532](https://github.com/nodejs/node/pull/39532)
- [[`64c02eb3cc`](https://github.com/nodejs/node/commit/64c02eb3cc)] - **(SEMVER-MINOR)** **perf_hooks**: web performance timeline compliance (legendecas) [#39297](https://github.com/nodejs/node/pull/39297)
- [[`7ff21397d6`](https://github.com/nodejs/node/commit/7ff21397d6)] - **policy**: fix integrity when DEFAULT_ENCODING is set (Tobias Nießen) [#39750](https://github.com/nodejs/node/pull/39750)
- [[`03be967cad`](https://github.com/nodejs/node/commit/03be967cad)] - **src**: fix TextDecoder final flush size calculation (James M Snell) [#39737](https://github.com/nodejs/node/pull/39737)
- [[`9046e78943`](https://github.com/nodejs/node/commit/9046e78943)] - **src**: fix crash in AfterGetAddrInfo (Anna Henningsen) [#39735](https://github.com/nodejs/node/pull/39735)
- [[`2a00ef5ede`](https://github.com/nodejs/node/commit/2a00ef5ede)] - **(SEMVER-MINOR)** **src**: fix align in cares_wrap.h (Luan) [#39610](https://github.com/nodejs/node/pull/39610)
- [[`60a2b31c68`](https://github.com/nodejs/node/commit/60a2b31c68)] - **src**: add cosmetic space character to `async_wrap.h` file (Juan José Arboleda) [#39459](https://github.com/nodejs/node/pull/39459)
- [[`cd9b0bf68c`](https://github.com/nodejs/node/commit/cd9b0bf68c)] - **stream**: ensure text() stream consumer flushes correctly (James M Snell) [#39737](https://github.com/nodejs/node/pull/39737)
- [[`f57a0e4d8b`](https://github.com/nodejs/node/commit/f57a0e4d8b)] - **(SEMVER-MINOR)** **stream**: utility consumers for web and node.js streams (James M Snell) [#39594](https://github.com/nodejs/node/pull/39594)
- [[`975edf5330`](https://github.com/nodejs/node/commit/975edf5330)] - **stream**: clean `endWritableNT` (Mestery) [#39645](https://github.com/nodejs/node/pull/39645)
- [[`9e38fc6757`](https://github.com/nodejs/node/commit/9e38fc6757)] - **(SEMVER-MINOR)** **stream**: add readableDidRead if has been read from (Robert Nagy) [#39589](https://github.com/nodejs/node/pull/39589)
- [[`a5ded4a85a`](https://github.com/nodejs/node/commit/a5ded4a85a)] - **test**: use simplfied validator (voltrexmaster) [#39753](https://github.com/nodejs/node/pull/39753)
- [[`53cf53c95a`](https://github.com/nodejs/node/commit/53cf53c95a)] - **(SEMVER-MINOR)** **test**: enable blob.prototype.stream tests (James M Snell) [#39693](https://github.com/nodejs/node/pull/39693)
- [[`7e9884598f`](https://github.com/nodejs/node/commit/7e9884598f)] - **test**: update WPT abort tests (Michaël Zasso) [#39697](https://github.com/nodejs/node/pull/39697)
- [[`94381fbdf5`](https://github.com/nodejs/node/commit/94381fbdf5)] - **test**: update WPT common and resources (Michaël Zasso) [#39697](https://github.com/nodejs/node/pull/39697)
- [[`34a041a846`](https://github.com/nodejs/node/commit/34a041a846)] - **test**: fix test-debugger-heap-profiler for workers (Richard Lau) [#39687](https://github.com/nodejs/node/pull/39687)
- [[`9f5acfa90e`](https://github.com/nodejs/node/commit/9f5acfa90e)] - **test**: increase memory for coverage action (Benjamin Coe) [#39690](https://github.com/nodejs/node/pull/39690)
- [[`0be15cedc4`](https://github.com/nodejs/node/commit/0be15cedc4)] - **test**: use template to concatenate string (Himadri Ganguly) [#39621](https://github.com/nodejs/node/pull/39621)
- [[`952a5282e2`](https://github.com/nodejs/node/commit/952a5282e2)] - **(SEMVER-MINOR)** **test**: pull Web Platform Tests for WebCryptoAPI (Michaël Zasso) [#39648](https://github.com/nodejs/node/pull/39648)
- [[`3622fb1e03`](https://github.com/nodejs/node/commit/3622fb1e03)] - **test**: deflake test-http2-buffersize (Luigi Pinca) [#39591](https://github.com/nodejs/node/pull/39591)
- [[`1962c7c7b3`](https://github.com/nodejs/node/commit/1962c7c7b3)] - **test**: convert anonymous function to arrow function (Himadri Ganguly) [#39604](https://github.com/nodejs/node/pull/39604)
- [[`635e1a0274`](https://github.com/nodejs/node/commit/635e1a0274)] - **test**: add test-debugger-breakpoint-exists (Rich Trott) [#39570](https://github.com/nodejs/node/pull/39570)
- [[`cff2aea5df`](https://github.com/nodejs/node/commit/cff2aea5df)] - **test**: add known issues test for debugger heap snapshot race (Rich Trott) [#39557](https://github.com/nodejs/node/pull/39557)
- [[`5e1011238a`](https://github.com/nodejs/node/commit/5e1011238a)] - **tools**: bump remark-preset-lint-node to 3.0.0 (Rich Trott) [#39755](https://github.com/nodejs/node/pull/39755)
- [[`eb741253fd`](https://github.com/nodejs/node/commit/eb741253fd)] - **tools**: update path-parse in markdown linter package-lock file (Rich Trott) [#39729](https://github.com/nodejs/node/pull/39729)
- [[`52a172f983`](https://github.com/nodejs/node/commit/52a172f983)] - **tools**: fix more build warnings in inspector_protocol (Richard Lau) [#39725](https://github.com/nodejs/node/pull/39725)
- [[`77f9c1fa98`](https://github.com/nodejs/node/commit/77f9c1fa98)] - **tools**: cherry-pick ffb34b6d5dbf0 (Darshan Sen) [#39725](https://github.com/nodejs/node/pull/39725)
- [[`b9510d21c9`](https://github.com/nodejs/node/commit/b9510d21c9)] - **tools**: update inspector_protocol to e8ba1a7 (Rich Trott) [#39694](https://github.com/nodejs/node/pull/39694)
- [[`8d509d8773`](https://github.com/nodejs/node/commit/8d509d8773)] - **tools**: update inspector_protocol to 39ca567 (Rich Trott) [#39694](https://github.com/nodejs/node/pull/39694)
- [[`ee7142fa37`](https://github.com/nodejs/node/commit/ee7142fa37)] - **tools**: update inspector_protocol to 97d3146 (Rich Trott) [#39694](https://github.com/nodejs/node/pull/39694)
- [[`c6323d847d`](https://github.com/nodejs/node/commit/c6323d847d)] - **_Revert_** "**tools**: fix compiler warning in inspector_protocol" (Rich Trott) [#39694](https://github.com/nodejs/node/pull/39694)
- [[`6e19c166e4`](https://github.com/nodejs/node/commit/6e19c166e4)] - **tools**: update inspector_protocol to a53e96d31a2755eb16ca37 (Rich Trott) [#39694](https://github.com/nodejs/node/pull/39694)
- [[`61c53f39d2`](https://github.com/nodejs/node/commit/61c53f39d2)] - **tools**: update inspector_protocol to fe0467fd105a (Rich Trott) [#39694](https://github.com/nodejs/node/pull/39694)
- [[`b1b6f20353`](https://github.com/nodejs/node/commit/b1b6f20353)] - **tools**: improve error detection in find-inactive-collaborators (Rich Trott) [#39617](https://github.com/nodejs/node/pull/39617)
- [[`d1360fcf48`](https://github.com/nodejs/node/commit/d1360fcf48)] - **tools**: update ESLint to 7.32.0 (Luigi Pinca) [#39602](https://github.com/nodejs/node/pull/39602)
- [[`af1c782cad`](https://github.com/nodejs/node/commit/af1c782cad)] - **tools**: update ESLint to 7.31.0 (Colin Ihrig) [#39424](https://github.com/nodejs/node/pull/39424)
- [[`37dda19461`](https://github.com/nodejs/node/commit/37dda19461)] - **(SEMVER-MINOR)** **url,buffer**: implement URL.createObjectURL (James M Snell) [#39693](https://github.com/nodejs/node/pull/39693)
- [[`dcab88ad38`](https://github.com/nodejs/node/commit/dcab88ad38)] - **worker**: add brand checks for detached properties/methods (James M Snell) [#39763](https://github.com/nodejs/node/pull/39763)

Windows 32-bit Installer: https://nodejs.org/dist/v16.7.0/node-v16.7.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v16.7.0/node-v16.7.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v16.7.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v16.7.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v16.7.0/node-v16.7.0.pkg \
macOS Apple Silicon 64-bit Binary: https://nodejs.org/dist/v16.7.0/node-v16.7.0-darwin-arm64.tar.gz \
macOS Intel 64-bit Binary: https://nodejs.org/dist/v16.7.0/node-v16.7.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v16.7.0/node-v16.7.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v16.7.0/node-v16.7.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v16.7.0/node-v16.7.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v16.7.0/node-v16.7.0-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v16.7.0/node-v16.7.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v16.7.0/node-v16.7.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v16.7.0/node-v16.7.0.tar.gz \
Other release files: https://nodejs.org/dist/v16.7.0/ \
Documentation: https://nodejs.org/docs/v16.7.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

4c706dbaebab5c5787d36238b32405143742050fb0faccc81d9da6ebf05e8304  node-v16.7.0-aix-ppc64.tar.gz
969875c1a6b2790663d7b25d7641d1e3919225659921a98d2f1e4711bbec5ef3  node-v16.7.0-darwin-arm64.tar.gz
a7d5f71b0b0be342eeff8dbe0a29935e098cab1117af2cd617f988461b1c4a5e  node-v16.7.0-darwin-arm64.tar.xz
c9bf23c765c584f635a4065d58dadff9737aeb605676d1e45873eba66adaab8a  node-v16.7.0-darwin-x64.tar.gz
29265ebcf44a3a19ffc7ecd2e1ef35db562fc53be58529e74cf1340e21ced9a9  node-v16.7.0-darwin-x64.tar.xz
8b51402dd94b1f77fb661f2db79144b7c0b9056b2d43cff60c7c333caefb11e8  node-v16.7.0-headers.tar.gz
8a398c4e6f1941ac42aea159e3cf0007f68163dd74116d8bb246801692c698f0  node-v16.7.0-headers.tar.xz
8a1b770c81618353ca2f6fd296ccfa7d812e7f40d1e2a2b88579e6d9895ec463  node-v16.7.0-linux-arm64.tar.gz
28775e7d1463052e748d69b145b845e28361c467cabc42dc887003484b6a4ef1  node-v16.7.0-linux-arm64.tar.xz
453019d1514e5f7befe770cef757f2c761e23b3e91b34f98968170f0a504a1ba  node-v16.7.0-linux-armv7l.tar.gz
5efff171b966bf93ae34471b2c54d22f1a8a7e3b5128f8b9b6d82e229ce0dbfe  node-v16.7.0-linux-armv7l.tar.xz
e8bcc040152cea7564f97d6be6c2b2226417a9ee63b91d456e21744a886d1862  node-v16.7.0-linux-ppc64le.tar.gz
f3a6a599165b943f67a77967f7d304318f12382999013c3d925defe89c95c947  node-v16.7.0-linux-ppc64le.tar.xz
e1f8becb5ba6ec80b2f893e92145fe43b041fc8392414ff6466a624e9afeaa15  node-v16.7.0-linux-s390x.tar.gz
51a3acd86d52da8418400a451f4eca615ae023d17866b018e9e2f02cb1f03286  node-v16.7.0-linux-s390x.tar.xz
13a15e1934d356c9e8f97fcfff411d7d5236e90ed04d6aeeca5f10f529b58a57  node-v16.7.0-linux-x64.tar.gz
32f1563b8bab10981164dfc63a38ab303db6b54e888d5ac4190b4b6777184daf  node-v16.7.0-linux-x64.tar.xz
c3ace96357ba98d2dc87d0a4019e233967e576abc36a7db4cb35a4f994fed552  node-v16.7.0.pkg
0c4a82acc5ae67744d56f2c97db54b859f2b3ef8e78deacfb8aed0ed4c7cb690  node-v16.7.0.tar.gz
fa85fdfbd378e587fa04ad9548e7c9f50e690af9575730d3b330cba6fc3f2d37  node-v16.7.0.tar.xz
f7b0e8b0bfcfad7d62eba16fa4db9f085983c12c661bd4c66d8e3bd783befa65  node-v16.7.0-win-x64.7z
d9081e74db8f71e509a5ccedce877eb32eb25c9d95a146d98ff05a24284e4914  node-v16.7.0-win-x64.zip
8fcb32d38c702148250640a5a7967c0e5d5d55cb8202aa40afde9684e8a67ea8  node-v16.7.0-win-x86.7z
21a484ad901a8965e4e19d86fdc923c3d58aa708ae395b36cbdc140542a053c6  node-v16.7.0-win-x86.zip
5646507653636fb6ba2f2f75854fc01dded59631e13dd843d20358ff0580adab  node-v16.7.0-x64.msi
6bb969fd41c1105e9829001a21c0882d7ecb45ad93be366671b3e48e7a9f8691  node-v16.7.0-x86.msi
7f33cbe04cb2940427e6dd97867c1fcf3ddd60911d2ae0260da3cab9f6ea6365  win-x64/node.exe
8b9363be29a20a95f48a319434a33bb7021b21b017a343b334697689b471b92f  win-x64/node.lib
f61ce6e746861d65d4144e5f37c8e70e52d8fe3a1edfc1152159536f03dbe742  win-x64/node_pdb.7z
2c47b5f606119feea241ae5ce8d47de6dd85f3ef4016f8936fe34459100c45f9  win-x64/node_pdb.zip
8a1ab09efad03051be13622c50123b87475b86000684b4b35495c5da356c9ac5  win-x86/node.exe
944a6428f1ed03da56f6ac82f65051b346f41d69aa43ad8219a528b85babb36c  win-x86/node.lib
d93a13cd7335ba1c1a8422427bdb3077a866f900bc1039dabbace94c27d83e30  win-x86/node_pdb.7z
6b5d395cfd2c8f9d4f33d2c9961ed7f80dcbcb15bd57ddf89f7a9b564d96cabb  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEdPEmArbxxOkT+qN606iWE2Q7YgEFAmEcXmMACgkQ06iWE2Q7
YgGAaw/9GxeuTEAK4Lx7DvqhFO9omtPbbA8NiiBRXvs9ZIV3EAf4zWqh4KT7G65+
OtXDcCTpNVjUj8WC+U2DyQuoYRwlJ6TAIui/oGZEY0OHe0d5R87VB7IJlPuNMjD5
hpbETkI+mp7xgEv16TieWRkUkJ9cd7z6qE8c0Mr1+isvlUjcP/wGRco+XQ7V7xeU
/SvvGJAer09kI4oELrhr48GyXLljZkgOQE2q5/MVMNjZIAmnldx/eAK4hT/suzyM
243cEeba4l/K+ZXM3aSMnyvVd3VUQfAtZniQbvzzR0mO1vRYLaIO1R9kU25SCRuF
LS7kRt5zRsvmybBB+IOGQwo/Kfo9UnSxaWwiWNZWJtHbQ50B91lJyzldjTwoPEVs
PRVP1b7+jtoyHEqtqMo5SpRFPF0Tcm1U1mi7tEunubxF8EUOOdizD5EIsBY4Qyol
58Zj8rsMxqeKP2oSgPijgm6u//OvM03YeyRHAxkNXVTO/JVjHNoKiqTkm0hPTEra
cRsFXIHrKKP7fbCXPeNTU1LW4TDmcat8wFl9hhTw7XXVZZt/CYd4yVhry6MVlXWo
8yjUhpptbs1d2ms2VcBPYF6WCrc0EwYQ7NW6NG+OYiXldGWQ9qakBBmKl4AcLfEL
3Hq51d7hkF+oPcbW68vmeuulc8maHTvPMd+W13NjqxPil8wjtqc=
=dKrk
-----END PGP SIGNATURE-----

```
