---
date: '2020-07-22T17:02:33.449Z'
category: release
title: Node v12.18.3 (LTS)
layout: blog-post
author: <PERSON>
---

### Notable Changes

- **deps:**
  - upgrade npm to 6.14.6 (claudiahdz) [#34246](https://github.com/nodejs/node/pull/34246)
  - update node-inspect to v2.0.0 (<PERSON>) [#33447](https://github.com/nodejs/node/pull/33447)
  - uvwasi: cherry-pick 9e75217 (<PERSON>) [#33521](https://github.com/nodejs/node/pull/33521)

### Commits

- [[`0d79c533ef`](https://github.com/nodejs/node/commit/0d79c533ef)] - **async_hooks**: callback trampoline for <PERSON><PERSON>allback (<PERSON>) [#33801](https://github.com/nodejs/node/pull/33801)
- [[`bfffb977ad`](https://github.com/nodejs/node/commit/bfffb977ad)] - **benchmark**: fix async-resource benchmark (<PERSON>) [#33642](https://github.com/nodejs/node/pull/33642)
- [[`09277fa5e4`](https://github.com/nodejs/node/commit/09277fa5e4)] - **benchmark**: fixing http_server_for_chunky_client.js (Adrian Estrada) [#33271](https://github.com/nodejs/node/pull/33271)
- [[`5a6d80f25f`](https://github.com/nodejs/node/commit/5a6d80f25f)] - **buffer**: remove hoisted variable (Nikolai Vavilov) [#33470](https://github.com/nodejs/node/pull/33470)
- [[`e057189ee8`](https://github.com/nodejs/node/commit/e057189ee8)] - **build**: configure byte order for mips targets (Ben Noordhuis) [#33898](https://github.com/nodejs/node/pull/33898)
- [[`d77eaeefb8`](https://github.com/nodejs/node/commit/d77eaeefb8)] - **build**: add target specific build_type variable (Daniel Bevenius) [#33925](https://github.com/nodejs/node/pull/33925)
- [[`d56585ec8d`](https://github.com/nodejs/node/commit/d56585ec8d)] - **build**: add LINT_CPP_FILES to checkimports check (Daniel Bevenius) [#33697](https://github.com/nodejs/node/pull/33697)
- [[`a5ce90c46b`](https://github.com/nodejs/node/commit/a5ce90c46b)] - **build**: add --v8-lite-mode flag (Maciej Kacper Jagiełło) [#33541](https://github.com/nodejs/node/pull/33541)
- [[`11dad02e50`](https://github.com/nodejs/node/commit/11dad02e50)] - **build**: fix python-version selection with actions (Richard Lau) [#33589](https://github.com/nodejs/node/pull/33589)
- [[`bba41bf6e1`](https://github.com/nodejs/node/commit/bba41bf6e1)] - **build**: fix makefile script on windows (Thomas) [#33136](https://github.com/nodejs/node/pull/33136)
- [[`817f6593ee`](https://github.com/nodejs/node/commit/817f6593ee)] - **configure**: account for CLANG_VENDOR when checking for llvm version (Nathan Blair) [#33860](https://github.com/nodejs/node/pull/33860)
- [[`a9c5b3348c`](https://github.com/nodejs/node/commit/a9c5b3348c)] - **console**: name console functions appropriately (Ruben Bridgewater) [#33524](https://github.com/nodejs/node/pull/33524)
- [[`d8365bc71e`](https://github.com/nodejs/node/commit/d8365bc71e)] - **console**: mark special console properties as non-enumerable (Ruben Bridgewater) [#33524](https://github.com/nodejs/node/pull/33524)
- [[`80782cb261`](https://github.com/nodejs/node/commit/80782cb261)] - **console**: remove dead code (Ruben Bridgewater) [#33524](https://github.com/nodejs/node/pull/33524)
- [[`18dc03d6a5`](https://github.com/nodejs/node/commit/18dc03d6a5)] - **crypto**: fix wrong error message (Ben Bucksch) [#33482](https://github.com/nodejs/node/pull/33482)
- [[`b64963e5c3`](https://github.com/nodejs/node/commit/b64963e5c3)] - **deps**: upgrade npm to 6.14.6 (claudiahdz) [#34246](https://github.com/nodejs/node/pull/34246)
- [[`9ee9688fe0`](https://github.com/nodejs/node/commit/9ee9688fe0)] - **deps**: uvwasi: cherry-pick 9e75217 (Colin Ihrig) [#33521](https://github.com/nodejs/node/pull/33521)
- [[`8803d7e8cf`](https://github.com/nodejs/node/commit/8803d7e8cf)] - **deps**: update node-inspect to v2.0.0 (Jan Krems) [#33447](https://github.com/nodejs/node/pull/33447)
- [[`5d3f818e9e`](https://github.com/nodejs/node/commit/5d3f818e9e)] - **dns**: make dns.Resolver timeout configurable (Ben Noordhuis) [#33472](https://github.com/nodejs/node/pull/33472)
- [[`10b88cb117`](https://github.com/nodejs/node/commit/10b88cb117)] - **dns**: use ternary operator simplify statement (Wenning Zhang) [#33234](https://github.com/nodejs/node/pull/33234)
- [[`fbd6fe5839`](https://github.com/nodejs/node/commit/fbd6fe5839)] - **doc**: update code language flag for internal doc (Rich Trott) [#33852](https://github.com/nodejs/node/pull/33852)
- [[`24fd15778a`](https://github.com/nodejs/node/commit/24fd15778a)] - **doc**: specify maxHeaderCount alias for maxHeaderListPairs (Pranshu Srivastava) [#33519](https://github.com/nodejs/node/pull/33519)
- [[`04ceeaf5eb`](https://github.com/nodejs/node/commit/04ceeaf5eb)] - **doc**: add allowed info strings to style guide (Derek Lewis) [#34024](https://github.com/nodejs/node/pull/34024)
- [[`ee36c87fd7`](https://github.com/nodejs/node/commit/ee36c87fd7)] - **doc**: clarify thread-safe function references (legendecas) [#33871](https://github.com/nodejs/node/pull/33871)
- [[`30b5e76ffd`](https://github.com/nodejs/node/commit/30b5e76ffd)] - **doc**: use npm team for npm upgrades in collaborator guide (Rich Trott) [#33999](https://github.com/nodejs/node/pull/33999)
- [[`06937249d0`](https://github.com/nodejs/node/commit/06937249d0)] - **doc**: correct default values in http2 docs (Rich Trott) [#33997](https://github.com/nodejs/node/pull/33997)
- [[`498dfba33a`](https://github.com/nodejs/node/commit/498dfba33a)] - **doc**: use a single space between sentences (Rich Trott) [#33995](https://github.com/nodejs/node/pull/33995)
- [[`47ea3067d0`](https://github.com/nodejs/node/commit/47ea3067d0)] - **doc**: revise text in dns module documentation introduction (Rich Trott) [#33986](https://github.com/nodejs/node/pull/33986)
- [[`f29f77f111`](https://github.com/nodejs/node/commit/f29f77f111)] - **doc**: update fs.md (Shakil-Shahadat) [#33820](https://github.com/nodejs/node/pull/33820)
- [[`ddc5afdddc`](https://github.com/nodejs/node/commit/ddc5afdddc)] - **doc**: warn that tls.connect() doesn't set SNI (Alba Mendez) [#33855](https://github.com/nodejs/node/pull/33855)
- [[`732b80b474`](https://github.com/nodejs/node/commit/732b80b474)] - **doc**: fix lexical sorting of bottom-references in dns doc (Rich Trott) [#33987](https://github.com/nodejs/node/pull/33987)
- [[`6af2ed3fdc`](https://github.com/nodejs/node/commit/6af2ed3fdc)] - **doc**: change "GitHub Repo" to "Code repository" (Rich Trott) [#33985](https://github.com/nodejs/node/pull/33985)
- [[`322a51e582`](https://github.com/nodejs/node/commit/322a51e582)] - **doc**: use Class: consistently (Rich Trott) [#33978](https://github.com/nodejs/node/pull/33978)
- [[`410b23398d`](https://github.com/nodejs/node/commit/410b23398d)] - **doc**: update WASM code sample (Pragyan Das) [#33626](https://github.com/nodejs/node/pull/33626)
- [[`335f405f1b`](https://github.com/nodejs/node/commit/335f405f1b)] - **doc**: link readable.\_read in stream.md (Pranshu Srivastava) [#33767](https://github.com/nodejs/node/pull/33767)
- [[`3789c28c89`](https://github.com/nodejs/node/commit/3789c28c89)] - **doc**: specify default encoding in writable.write (Pranshu Srivastava) [#33765](https://github.com/nodejs/node/pull/33765)
- [[`5609b17e2d`](https://github.com/nodejs/node/commit/5609b17e2d)] - **doc**: move --force-context-aware option in cli.md (Daniel Bevenius) [#33823](https://github.com/nodejs/node/pull/33823)
- [[`f39ee7d245`](https://github.com/nodejs/node/commit/f39ee7d245)] - **doc**: add snippet for AsyncResource and EE integration (Andrey Pechkurov) [#33751](https://github.com/nodejs/node/pull/33751)
- [[`f8baeccaaa`](https://github.com/nodejs/node/commit/f8baeccaaa)] - **doc**: use single quotes in --tls-cipher-list (Daniel Bevenius) [#33709](https://github.com/nodejs/node/pull/33709)
- [[`4654e2321b`](https://github.com/nodejs/node/commit/4654e2321b)] - **doc**: fix misc. mislabeled code block info strings (Derek Lewis) [#33548](https://github.com/nodejs/node/pull/33548)
- [[`046dee6eb3`](https://github.com/nodejs/node/commit/046dee6eb3)] - **doc**: update V8 inspector example (Colin Ihrig) [#33758](https://github.com/nodejs/node/pull/33758)
- [[`d547d1c1bc`](https://github.com/nodejs/node/commit/d547d1c1bc)] - **doc**: fix linting in doc-style-guide.md (Pranshu Srivastava) [#33787](https://github.com/nodejs/node/pull/33787)
- [[`3b437416d5`](https://github.com/nodejs/node/commit/3b437416d5)] - **doc**: add formatting for version numbers to doc-style-guide.md (Rich Trott) [#33755](https://github.com/nodejs/node/pull/33755)
- [[`b00996ce35`](https://github.com/nodejs/node/commit/b00996ce35)] - **doc**: remove "currently" from repl.md (Rich Trott) [#33756](https://github.com/nodejs/node/pull/33756)
- [[`7595d15286`](https://github.com/nodejs/node/commit/7595d15286)] - **doc**: remove "currently" from vm.md (Rich Trott) [#33756](https://github.com/nodejs/node/pull/33756)
- [[`36a8af7a5e`](https://github.com/nodejs/node/commit/36a8af7a5e)] - **doc**: remove "currently" from addons.md (Rich Trott) [#33756](https://github.com/nodejs/node/pull/33756)
- [[`27e797687f`](https://github.com/nodejs/node/commit/27e797687f)] - **doc**: remove "currently" from util.md (Rich Trott) [#33756](https://github.com/nodejs/node/pull/33756)
- [[`94ac13678d`](https://github.com/nodejs/node/commit/94ac13678d)] - **doc**: change "pre Node.js v0.10" to "prior to Node.js 0.10" (Rich Trott) [#33754](https://github.com/nodejs/node/pull/33754)
- [[`f1a810880e`](https://github.com/nodejs/node/commit/f1a810880e)] - **doc**: normalize C++ code block info strings (Derek Lewis) [#33483](https://github.com/nodejs/node/pull/33483)
- [[`289d0bf105`](https://github.com/nodejs/node/commit/289d0bf105)] - **doc**: remove default parameter value from header (Rich Trott) [#33752](https://github.com/nodejs/node/pull/33752)
- [[`35cee03849`](https://github.com/nodejs/node/commit/35cee03849)] - **doc**: remove shell dollar signs without output (Nick Schonning) [#33692](https://github.com/nodejs/node/pull/33692)
- [[`d10fac73a3`](https://github.com/nodejs/node/commit/d10fac73a3)] - **doc**: add lint disabling comment for collaborator list (Rich Trott) [#33719](https://github.com/nodejs/node/pull/33719)
- [[`8dbf3349d0`](https://github.com/nodejs/node/commit/8dbf3349d0)] - **doc**: fix urls to avoid redirection (sapics) [#33614](https://github.com/nodejs/node/pull/33614)
- [[`5416635677`](https://github.com/nodejs/node/commit/5416635677)] - **doc**: improve buffer.md a tiny bit (Tom Nagle) [#33547](https://github.com/nodejs/node/pull/33547)
- [[`a3b6095db1`](https://github.com/nodejs/node/commit/a3b6095db1)] - **doc**: normalize Markdown code block info strings (Derek Lewis) [#33542](https://github.com/nodejs/node/pull/33542)
- [[`4fcbfdc45c`](https://github.com/nodejs/node/commit/4fcbfdc45c)] - **doc**: normalize JavaScript code block info strings (Derek Lewis) [#33531](https://github.com/nodejs/node/pull/33531)
- [[`543605782d`](https://github.com/nodejs/node/commit/543605782d)] - **doc**: outline when origin is set to unhandledRejection (Ruben Bridgewater) [#33530](https://github.com/nodejs/node/pull/33530)
- [[`7dc28ab4d3`](https://github.com/nodejs/node/commit/7dc28ab4d3)] - **doc**: update txt fandamental and raw code blocks (Zeke Sikelianos) [#33028](https://github.com/nodejs/node/pull/33028)
- [[`cf82adf87f`](https://github.com/nodejs/node/commit/cf82adf87f)] - **doc**: normalize Bash code block info strings (Derek Lewis) [#33510](https://github.com/nodejs/node/pull/33510)
- [[`7ea6b07b90`](https://github.com/nodejs/node/commit/7ea6b07b90)] - **doc**: normalize shell code block info strings (Derek Lewis) [#33486](https://github.com/nodejs/node/pull/33486)
- [[`74a1493441`](https://github.com/nodejs/node/commit/74a1493441)] - **doc**: normalize C code block info strings (Derek Lewis) [#33507](https://github.com/nodejs/node/pull/33507)
- [[`281d7f74d8`](https://github.com/nodejs/node/commit/281d7f74d8)] - **doc**: correct tls.rootCertificates to match implementation (Eric Bickle) [#33313](https://github.com/nodejs/node/pull/33313)
- [[`6133639d53`](https://github.com/nodejs/node/commit/6133639d53)] - **doc**: fix Buffer.from(object) documentation (Nikolai Vavilov) [#33327](https://github.com/nodejs/node/pull/33327)
- [[`b599037f78`](https://github.com/nodejs/node/commit/b599037f78)] - **doc**: fix typo in pathToFileURL example (Antoine du HAMEL) [#33418](https://github.com/nodejs/node/pull/33418)
- [[`78734c2698`](https://github.com/nodejs/node/commit/78734c2698)] - **doc**: eliminate dead space in API section's sidebar (John Gardner) [#33469](https://github.com/nodejs/node/pull/33469)
- [[`c76ec4d007`](https://github.com/nodejs/node/commit/c76ec4d007)] - **doc**: fixed a grammatical error in path.md (Deep310) [#33489](https://github.com/nodejs/node/pull/33489)
- [[`1b76377bce`](https://github.com/nodejs/node/commit/1b76377bce)] - **doc**: correct CommonJS self-resolve spec (Guy Bedford) [#33391](https://github.com/nodejs/node/pull/33391)
- [[`70d025f510`](https://github.com/nodejs/node/commit/70d025f510)] - **doc**: standardize on sentence case for headers (Rich Trott) [#33889](https://github.com/nodejs/node/pull/33889)
- [[`3e68d21c6f`](https://github.com/nodejs/node/commit/3e68d21c6f)] - **doc**: use sentence-case for headings in docs (Rich Trott) [#33889](https://github.com/nodejs/node/pull/33889)
- [[`dfa8028254`](https://github.com/nodejs/node/commit/dfa8028254)] - **doc**: fix readline key binding documentation (Ruben Bridgewater) [#33361](https://github.com/nodejs/node/pull/33361)
- [[`6f8b7a85d2`](https://github.com/nodejs/node/commit/6f8b7a85d2)] - **doc,tools**: properly syntax highlight API ref docs (Derek Lewis) [#33442](https://github.com/nodejs/node/pull/33442)
- [[`43d1d89d27`](https://github.com/nodejs/node/commit/43d1d89d27)] - **domain**: fix unintentional deprecation warning (Anna Henningsen) [#34245](https://github.com/nodejs/node/pull/34245)
- [[`ba476326dd`](https://github.com/nodejs/node/commit/ba476326dd)] - **domain**: remove native domain code (Stephen Belanger) [#33801](https://github.com/nodejs/node/pull/33801)
- [[`76b06e53c6`](https://github.com/nodejs/node/commit/76b06e53c6)] - **errors**: fully inspect errors on exit (Ruben Bridgewater) [#33523](https://github.com/nodejs/node/pull/33523)
- [[`9111fab663`](https://github.com/nodejs/node/commit/9111fab663)] - **esm**: fix loader hooks doc annotations (Derek Lewis) [#33563](https://github.com/nodejs/node/pull/33563)
- [[`3559471153`](https://github.com/nodejs/node/commit/3559471153)] - **esm**: share package.json cache between ESM and CJS loaders (Kirill Shatskiy) [#33229](https://github.com/nodejs/node/pull/33229)
- [[`d09f6d55c7`](https://github.com/nodejs/node/commit/d09f6d55c7)] - **esm**: doc & validate source values for formats (Bradley Farias) [#32202](https://github.com/nodejs/node/pull/32202)
- [[`a76fa60c63`](https://github.com/nodejs/node/commit/a76fa60c63)] - **fs**: fix readdir failure when libuv returns UV_DIRENT_UNKNOWN (Kirill Shatskiy) [#33395](https://github.com/nodejs/node/pull/33395)
- [[`b92c0cb15c`](https://github.com/nodejs/node/commit/b92c0cb15c)] - **fs**: fix realpath inode link caching (Denys Otrishko) [#33945](https://github.com/nodejs/node/pull/33945)
- [[`04fa6d675f`](https://github.com/nodejs/node/commit/04fa6d675f)] - **fs**: close file descriptor of promisified truncate (João Reis) [#34239](https://github.com/nodejs/node/pull/34239)
- [[`c9cf41d841`](https://github.com/nodejs/node/commit/c9cf41d841)] - **fs**: support util.promisify for fs.readv (Lucas Holmquist) [#33590](https://github.com/nodejs/node/pull/33590)
- [[`adb93f153b`](https://github.com/nodejs/node/commit/adb93f153b)] - **fs**: unify style in preprocessSymlinkDestination (Bartosz Sosnowski) [#33496](https://github.com/nodejs/node/pull/33496)
- [[`5fb1cc8cc1`](https://github.com/nodejs/node/commit/5fb1cc8cc1)] - **fs**: replace checkPosition with validateInteger (rickyes) [#33277](https://github.com/nodejs/node/pull/33277)
- [[`75107e23a8`](https://github.com/nodejs/node/commit/75107e23a8)] - **http2**: always call callback on Http2ServerResponse#end (Pranshu Srivastava) [#33911](https://github.com/nodejs/node/pull/33911)
- [[`0f0720a665`](https://github.com/nodejs/node/commit/0f0720a665)] - **http2**: add writable\* properties to compat api (Pranshu Srivastava) [#33506](https://github.com/nodejs/node/pull/33506)
- [[`8def93429e`](https://github.com/nodejs/node/commit/8def93429e)] - **http2**: add type checks for Http2ServerResponse.end (Pranshu Srivastava) [#33146](https://github.com/nodejs/node/pull/33146)
- [[`a3b7e5992d`](https://github.com/nodejs/node/commit/a3b7e5992d)] - **http2**: use `Object.create(null)` for `getHeaders` (Pranshu Srivastava) [#33188](https://github.com/nodejs/node/pull/33188)
- [[`bcdf4c808d`](https://github.com/nodejs/node/commit/bcdf4c808d)] - **http2**: reuse .\_onTimeout() in Http2Session and Http2Stream classes (rickyes) [#33354](https://github.com/nodejs/node/pull/33354)
- [[`103a9af673`](https://github.com/nodejs/node/commit/103a9af673)] - **inspector**: drop 'chrome-' from inspector url (Colin Ihrig) [#33758](https://github.com/nodejs/node/pull/33758)
- [[`0941635bb5`](https://github.com/nodejs/node/commit/0941635bb5)] - **inspector**: throw error when activating an already active inspector (Joyee Cheung) [#33015](https://github.com/nodejs/node/pull/33015)
- [[`0197ea4e56`](https://github.com/nodejs/node/commit/0197ea4e56)] - **lib**: replace charCodeAt with fixed Unicode (rickyes) [#32758](https://github.com/nodejs/node/pull/32758)
- [[`69291e4b7d`](https://github.com/nodejs/node/commit/69291e4b7d)] - **lib**: add Int16Array primordials (Sebastien Ahkrin) [#31205](https://github.com/nodejs/node/pull/31205)
- [[`83c9364bf1`](https://github.com/nodejs/node/commit/83c9364bf1)] - **lib**: update TODO comments (Ruben Bridgewater) [#33361](https://github.com/nodejs/node/pull/33361)
- [[`a94e7dabcc`](https://github.com/nodejs/node/commit/a94e7dabcc)] - **lib**: update executionAsyncId/triggerAsyncId comment (Daniel Bevenius) [#33396](https://github.com/nodejs/node/pull/33396)
- [[`857ff68485`](https://github.com/nodejs/node/commit/857ff68485)] - **meta**: introduce codeowners again (James M Snell) [#33895](https://github.com/nodejs/node/pull/33895)
- [[`f534ac06bd`](https://github.com/nodejs/node/commit/f534ac06bd)] - **meta**: fix a typo in the flaky test template (Colin Ihrig) [#33677](https://github.com/nodejs/node/pull/33677)
- [[`1376c3bab2`](https://github.com/nodejs/node/commit/1376c3bab2)] - **meta**: wrap flaky test template at 80 characters (Colin Ihrig) [#33677](https://github.com/nodejs/node/pull/33677)
- [[`b7ea7be2a8`](https://github.com/nodejs/node/commit/b7ea7be2a8)] - **meta**: add flaky test issue template (Ash Cripps) [#33500](https://github.com/nodejs/node/pull/33500)
- [[`0867ab7da5`](https://github.com/nodejs/node/commit/0867ab7da5)] - **module**: fix error message about importing names from cjs (Fábio Santos) [#33882](https://github.com/nodejs/node/pull/33882)
- [[`47f5eeb0d5`](https://github.com/nodejs/node/commit/47f5eeb0d5)] - **n-api**: add version to wasm registration (Gus Caplan) [#34045](https://github.com/nodejs/node/pull/34045)
- [[`2e97d82509`](https://github.com/nodejs/node/commit/2e97d82509)] - **n-api**: document nextTick timing in callbacks (Mathias Buus) [#33804](https://github.com/nodejs/node/pull/33804)
- [[`90ddf0aa2e`](https://github.com/nodejs/node/commit/90ddf0aa2e)] - **n-api**: ensure scope present for finalization (Michael Dawson) [#33508](https://github.com/nodejs/node/pull/33508)
- [[`ed741ecb1e`](https://github.com/nodejs/node/commit/ed741ecb1e)] - **n-api**: remove `napi_env::CallIntoModuleThrow` (Gabriel Schulhof) [#33570](https://github.com/nodejs/node/pull/33570)
- [[`0a949c3f93`](https://github.com/nodejs/node/commit/0a949c3f93)] - **napi**: add \_\_wasm32\_\_ guards (Gus Caplan) [#33597](https://github.com/nodejs/node/pull/33597)
- [[`7c7f5c8869`](https://github.com/nodejs/node/commit/7c7f5c8869)] - **net**: refactor check for Windows (rickyes) [#33497](https://github.com/nodejs/node/pull/33497)
- [[`578e731321`](https://github.com/nodejs/node/commit/578e731321)] - **querystring**: fix stringify for empty array (sapics) [#33918](https://github.com/nodejs/node/pull/33918)
- [[`13b693fd54`](https://github.com/nodejs/node/commit/13b693fd54)] - **querystring**: improve stringify() performance (Brian White) [#33669](https://github.com/nodejs/node/pull/33669)
- [[`d3737a1c32`](https://github.com/nodejs/node/commit/d3737a1c32)] - **src**: add errorProperties on process.report (himself65) [#28426](https://github.com/nodejs/node/pull/28426)
- [[`b57778ff26`](https://github.com/nodejs/node/commit/b57778ff26)] - **src**: tolerate EPERM returned from tcsetattr (patr0nus) [#33944](https://github.com/nodejs/node/pull/33944)
- [[`9e1185afee`](https://github.com/nodejs/node/commit/9e1185afee)] - **src**: clang_format base_object (Yash Ladha) [#33680](https://github.com/nodejs/node/pull/33680)
- [[`69f962953c`](https://github.com/nodejs/node/commit/69f962953c)] - **src**: remove unnecessary calculation in base64.h (sapics) [#33839](https://github.com/nodejs/node/pull/33839)
- [[`b1c9f75a20`](https://github.com/nodejs/node/commit/b1c9f75a20)] - **src**: use ToLocal in node_os.cc (wenningplus) [#33939](https://github.com/nodejs/node/pull/33939)
- [[`153f292a97`](https://github.com/nodejs/node/commit/153f292a97)] - **src**: handle empty Maybe(Local) in node_util.cc (Anna Henningsen) [#33867](https://github.com/nodejs/node/pull/33867)
- [[`6d5383de35`](https://github.com/nodejs/node/commit/6d5383de35)] - **src**: improve indention for upd_wrap.cc (gengjiawen) [#33976](https://github.com/nodejs/node/pull/33976)
- [[`437f387de9`](https://github.com/nodejs/node/commit/437f387de9)] - **src**: reduce scope of code cache mutex (Anna Henningsen) [#33980](https://github.com/nodejs/node/pull/33980)
- [[`9199808355`](https://github.com/nodejs/node/commit/9199808355)] - **src**: do not track BaseObjects via cleanup hooks (Anna Henningsen) [#33809](https://github.com/nodejs/node/pull/33809)
- [[`5b987c46b7`](https://github.com/nodejs/node/commit/5b987c46b7)] - **src**: remove ref to tools/generate_code_cache.js (Daniel Bevenius) [#33825](https://github.com/nodejs/node/pull/33825)
- [[`185657dfd7`](https://github.com/nodejs/node/commit/185657dfd7)] - **src**: remove unused vector include in string_bytes (Daniel Bevenius) [#33824](https://github.com/nodejs/node/pull/33824)
- [[`ec2452c4af`](https://github.com/nodejs/node/commit/ec2452c4af)] - **src**: avoid unnecessary ToLocalChecked calls (Daniel Bevenius) [#33824](https://github.com/nodejs/node/pull/33824)
- [[`74843db28c`](https://github.com/nodejs/node/commit/74843db28c)] - **src**: simplify format in node_file.cc (himself65) [#33660](https://github.com/nodejs/node/pull/33660)
- [[`86283aaa6a`](https://github.com/nodejs/node/commit/86283aaa6a)] - **src**: handle missing TracingController everywhere (Anna Henningsen) [#33815](https://github.com/nodejs/node/pull/33815)
- [[`e07c1c2508`](https://github.com/nodejs/node/commit/e07c1c2508)] - **src**: simplify Reindent function in json_utils.cc (sapics) [#33722](https://github.com/nodejs/node/pull/33722)
- [[`449d9ec1c5`](https://github.com/nodejs/node/commit/449d9ec1c5)] - **src**: add "missing" bash completion options (Daniel Bevenius) [#33744](https://github.com/nodejs/node/pull/33744)
- [[`4b4fb1381b`](https://github.com/nodejs/node/commit/4b4fb1381b)] - **src**: use Check() instead of FromJust in environment (Daniel Bevenius) [#33706](https://github.com/nodejs/node/pull/33706)
- [[`6f1d38cd8f`](https://github.com/nodejs/node/commit/6f1d38cd8f)] - **src**: use ToLocal in SafeGetenv (Daniel Bevenius) [#33695](https://github.com/nodejs/node/pull/33695)
- [[`5b8cac8cf5`](https://github.com/nodejs/node/commit/5b8cac8cf5)] - **src**: remove unnecessary ToLocalChecked call (Daniel Bevenius) [#33683](https://github.com/nodejs/node/pull/33683)
- [[`eb8d6f5fd8`](https://github.com/nodejs/node/commit/eb8d6f5fd8)] - **src**: simplify MaybeStackBuffer::capacity() (Ben Noordhuis) [#33602](https://github.com/nodejs/node/pull/33602)
- [[`e3beb781e0`](https://github.com/nodejs/node/commit/e3beb781e0)] - **src**: avoid OOB read in URL parser (Anna Henningsen) [#33640](https://github.com/nodejs/node/pull/33640)
- [[`99371ade2a`](https://github.com/nodejs/node/commit/99371ade2a)] - **src**: use MaybeLocal.ToLocal instead of IsEmpty worker (Daniel Bevenius) [#33599](https://github.com/nodejs/node/pull/33599)
- [[`9c69296990`](https://github.com/nodejs/node/commit/9c69296990)] - **src**: don't use semicolon outside function (Shelley Vohr) [#33592](https://github.com/nodejs/node/pull/33592)
- [[`41d879616f`](https://github.com/nodejs/node/commit/41d879616f)] - **src**: remove unused using declarations (Daniel Bevenius) [#33268](https://github.com/nodejs/node/pull/33268)
- [[`103479a0c5`](https://github.com/nodejs/node/commit/103479a0c5)] - **src**: use MaybeLocal.ToLocal instead of IsEmpty (Daniel Bevenius) [#33554](https://github.com/nodejs/node/pull/33554)
- [[`05cbd8f6f2`](https://github.com/nodejs/node/commit/05cbd8f6f2)] - **src**: use const in constant args.Length() (himself65) [#33555](https://github.com/nodejs/node/pull/33555)
- [[`48035a2a35`](https://github.com/nodejs/node/commit/48035a2a35)] - **src**: use MaybeLocal::FromMaybe to return exception (Daniel Bevenius) [#33514](https://github.com/nodejs/node/pull/33514)
- [[`e1050344f8`](https://github.com/nodejs/node/commit/e1050344f8)] - **_Revert_** "**src**: fix missing extra ca in tls.rootCertificates" (Eric Bickle) [#33313](https://github.com/nodejs/node/pull/33313)
- [[`77b6298b67`](https://github.com/nodejs/node/commit/77b6298b67)] - **src**: remove BeforeExit callback list (Ben Noordhuis) [#33386](https://github.com/nodejs/node/pull/33386)
- [[`a522c0e2c7`](https://github.com/nodejs/node/commit/a522c0e2c7)] - **src**: use MaybeLocal.ToLocal instead of IsEmpty (Daniel Bevenius) [#33457](https://github.com/nodejs/node/pull/33457)
- [[`0837c2cc99`](https://github.com/nodejs/node/commit/0837c2cc99)] - **src**: remove unused headers in src/util.h (Juan José Arboleda) [#33070](https://github.com/nodejs/node/pull/33070)
- [[`6f6fb1fcf5`](https://github.com/nodejs/node/commit/6f6fb1fcf5)] - **src**: prefer make_unique (Michael Dawson) [#33378](https://github.com/nodejs/node/pull/33378)
- [[`c697b96dea`](https://github.com/nodejs/node/commit/c697b96dea)] - **src**: remove unnecessary else in base_object-inl.h (Daniel Bevenius) [#33413](https://github.com/nodejs/node/pull/33413)
- [[`abf04b245a`](https://github.com/nodejs/node/commit/abf04b245a)] - **src,build**: add --openssl-default-cipher-list (Daniel Bevenius) [#33708](https://github.com/nodejs/node/pull/33708)
- [[`62edaaefc2`](https://github.com/nodejs/node/commit/62edaaefc2)] - **stream**: fix the spellings (antsmartian) [#33635](https://github.com/nodejs/node/pull/33635)
- [[`998b22cbbc`](https://github.com/nodejs/node/commit/998b22cbbc)] - **test**: add test for Http2ServerResponse#\[writableCorked,cork,uncork\] (Pranshu Srivastava) [#33956](https://github.com/nodejs/node/pull/33956)
- [[`9b8695fb35`](https://github.com/nodejs/node/commit/9b8695fb35)] - **test**: account for non-node basename (Shelley Vohr) [#33952](https://github.com/nodejs/node/pull/33952)
- [[`b9f8034f95`](https://github.com/nodejs/node/commit/b9f8034f95)] - **test**: fix typo in common/index.js (gengjiawen) [#33976](https://github.com/nodejs/node/pull/33976)
- [[`7744f66e0d`](https://github.com/nodejs/node/commit/7744f66e0d)] - **test**: print arguments passed to mustNotCall function (Denys Otrishko) [#33951](https://github.com/nodejs/node/pull/33951)
- [[`b5113d0b53`](https://github.com/nodejs/node/commit/b5113d0b53)] - **test**: temporarily exclude test on arm (Michael Dawson) [#33814](https://github.com/nodejs/node/pull/33814)
- [[`c50bd2f954`](https://github.com/nodejs/node/commit/c50bd2f954)] - **test**: fix invalid regular expressions in case test-trace-exit (legendecas) [#33769](https://github.com/nodejs/node/pull/33769)
- [[`d374e76428`](https://github.com/nodejs/node/commit/d374e76428)] - **test**: changed function to arrow function (Sagar Jadhav) [#33711](https://github.com/nodejs/node/pull/33711)
- [[`0982bf4234`](https://github.com/nodejs/node/commit/0982bf4234)] - **test**: uv_tty_init now returns EINVAL on IBM i (Xu Meng) [#33629](https://github.com/nodejs/node/pull/33629)
- [[`3032f0f38d`](https://github.com/nodejs/node/commit/3032f0f38d)] - **test**: make flaky test stricter (Robert Nagy) [#33539](https://github.com/nodejs/node/pull/33539)
- [[`ef27e6ce57`](https://github.com/nodejs/node/commit/ef27e6ce57)] - **test**: mark test-dgram-multicast-ssmv6-multi-process flaky (AshCripps) [#33498](https://github.com/nodejs/node/pull/33498)
- [[`a131c72586`](https://github.com/nodejs/node/commit/a131c72586)] - **tools**: enable no-else-return lint rule (Luigi Pinca) [#32667](https://github.com/nodejs/node/pull/32667)
- [[`6651bde34e`](https://github.com/nodejs/node/commit/6651bde34e)] - **tools**: update remark-preset-lint-node@1.15.1 to 1.16.0 (Rich Trott) [#33852](https://github.com/nodejs/node/pull/33852)
- [[`2e38f0dafd`](https://github.com/nodejs/node/commit/2e38f0dafd)] - **tools**: remove superfluous regex in tools/doc/json.js (Rich Trott) [#33998](https://github.com/nodejs/node/pull/33998)
- [[`ba813dd0dd`](https://github.com/nodejs/node/commit/ba813dd0dd)] - **tools**: prevent js2c from running if nothing changed (Daniel Bevenius) [#33844](https://github.com/nodejs/node/pull/33844)
- [[`fd5ab63d96`](https://github.com/nodejs/node/commit/fd5ab63d96)] - **tools**: remove unused vector include in mkdcodecache (Daniel Bevenius) [#33828](https://github.com/nodejs/node/pull/33828)
- [[`54a4a816a4`](https://github.com/nodejs/node/commit/54a4a816a4)] - **tools**: update ESLint to 7.2.0 (Colin Ihrig) [#33776](https://github.com/nodejs/node/pull/33776)
- [[`5328089c91`](https://github.com/nodejs/node/commit/5328089c91)] - **tools**: remove unused using declarations code_cache (Daniel Bevenius) [#33697](https://github.com/nodejs/node/pull/33697)
- [[`2f02fbac3a`](https://github.com/nodejs/node/commit/2f02fbac3a)] - **tools**: update remark-preset-lint-node from 1.15.0 to 1.15.1 (Rich Trott) [#33727](https://github.com/nodejs/node/pull/33727)
- [[`3d05e3d861`](https://github.com/nodejs/node/commit/3d05e3d861)] - **tools**: fix check-imports.py to match on word boundaries (Richard Lau) [#33268](https://github.com/nodejs/node/pull/33268)
- [[`ff4f9a9247`](https://github.com/nodejs/node/commit/ff4f9a9247)] - **tools**: update ESLint to 7.1.0 (Colin Ihrig) [#33526](https://github.com/nodejs/node/pull/33526)
- [[`f495ab3dcb`](https://github.com/nodejs/node/commit/f495ab3dcb)] - **tools**: add docserve target (Antoine du HAMEL) [#33221](https://github.com/nodejs/node/pull/33221)
- [[`a9dbb224af`](https://github.com/nodejs/node/commit/a9dbb224af)] - **util**: fix width detection for DEL without ICU (Ruben Bridgewater) [#33650](https://github.com/nodejs/node/pull/33650)
- [[`02ae3f5625`](https://github.com/nodejs/node/commit/02ae3f5625)] - **util**: support Combining Diacritical Marks for Symbols (Ruben Bridgewater) [#33650](https://github.com/nodejs/node/pull/33650)
- [[`524b230143`](https://github.com/nodejs/node/commit/524b230143)] - **util**: gracefully handle unknown colors (Ruben Bridgewater) [#33797](https://github.com/nodejs/node/pull/33797)
- [[`e3533ab337`](https://github.com/nodejs/node/commit/e3533ab337)] - **util**: mark classes while inspecting them (Ruben Bridgewater) [#32332](https://github.com/nodejs/node/pull/32332)
- [[`c4129f91e8`](https://github.com/nodejs/node/commit/c4129f91e8)] - **vm**: allow proxy callbacks to throw (Gus Caplan) [#33808](https://github.com/nodejs/node/pull/33808)
- [[`8adfb542eb`](https://github.com/nodejs/node/commit/8adfb542eb)] - **wasi**: allow WASI stdio to be configured (Colin Ihrig) [#33544](https://github.com/nodejs/node/pull/33544)
- [[`33984d6e4d`](https://github.com/nodejs/node/commit/33984d6e4d)] - **wasi**: simplify WASI memory management (Colin Ihrig) [#33525](https://github.com/nodejs/node/pull/33525)
- [[`5e5be9929b`](https://github.com/nodejs/node/commit/5e5be9929b)] - **wasi**: refactor and enable poll_oneoff() test (Colin Ihrig) [#33521](https://github.com/nodejs/node/pull/33521)
- [[`383c5b3962`](https://github.com/nodejs/node/commit/383c5b3962)] - **wasi**: relax WebAssembly.Instance type check (Ben Noordhuis) [#33431](https://github.com/nodejs/node/pull/33431)
- [[`7df79f498c`](https://github.com/nodejs/node/commit/7df79f498c)] - **wasi,worker**: handle termination exception (Ben Noordhuis) [#33386](https://github.com/nodejs/node/pull/33386)
- [[`3b46e7f148`](https://github.com/nodejs/node/commit/3b46e7f148)] - **win,fs**: use namespaced path in absolute symlinks (Bartosz Sosnowski) [#33351](https://github.com/nodejs/node/pull/33351)
- [[`4388dad537`](https://github.com/nodejs/node/commit/4388dad537)] - **win,msi**: add arm64 config for windows msi (Dennis Ameling) [#33689](https://github.com/nodejs/node/pull/33689)
- [[`032c64f1e4`](https://github.com/nodejs/node/commit/032c64f1e4)] - **worker**: fix variable referencing in template string (Harshitha KP) [#33467](https://github.com/nodejs/node/pull/33467)
- [[`1c64bc5e34`](https://github.com/nodejs/node/commit/1c64bc5e34)] - **worker**: perform initial port.unref() before preload modules (Anna Henningsen) [#33455](https://github.com/nodejs/node/pull/33455)
- [[`c502384ab7`](https://github.com/nodejs/node/commit/c502384ab7)] - **worker**: use \_writev in internal communication (Anna Henningsen) [#33454](https://github.com/nodejs/node/pull/33454)

Windows 32-bit Installer: https://nodejs.org/dist/v12.18.3/node-v12.18.3-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v12.18.3/node-v12.18.3-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v12.18.3/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v12.18.3/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v12.18.3/node-v12.18.3.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v12.18.3/node-v12.18.3-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v12.18.3/node-v12.18.3-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v12.18.3/node-v12.18.3-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v12.18.3/node-v12.18.3-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v12.18.3/node-v12.18.3-aix-ppc64.tar.gz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v12.18.3/node-v12.18.3-sunos-x64.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v12.18.3/node-v12.18.3-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v12.18.3/node-v12.18.3-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v12.18.3/node-v12.18.3.tar.gz \
Other release files: https://nodejs.org/dist/v12.18.3/ \
Documentation: https://nodejs.org/docs/v12.18.3/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

9b62fcc3138eaadfc4ea90776c5e02f508a1d7df8c0b1692734cd9d07a7d82dd  node-v12.18.3-aix-ppc64.tar.gz
af376caf114bdd5d7e566dbf7590e9077ffc01f9b2692eb2651f31d7219a30bb  node-v12.18.3-darwin-x64.tar.gz
4741270b9ff11159e775e9d66be2ec5b92a4d01ea6a186658d0101a0f1507546  node-v12.18.3-darwin-x64.tar.xz
164edc71e46cf86ac5d9fcac00dc69c09bb09614b767c4c775aa7747b42e8188  node-v12.18.3-headers.tar.gz
f5693c46f54e13eaf095e2b09383cd85ca6567c727b7008eedd3b64a66dd7308  node-v12.18.3-headers.tar.xz
f2b8b7f34966a03f03fcd89fa4924fb97ea680eae4c4e02ff1aafd9ea89ecad8  node-v12.18.3-linux-arm64.tar.gz
d330aab40029afc34f8b5d1abc2e67be233df4b31331cf08d538be2a666737bb  node-v12.18.3-linux-arm64.tar.xz
9d115013b6c3e34590169913dd521db71b2bb5a8203c89b00fc94e32ecc9d642  node-v12.18.3-linux-armv7l.tar.gz
d399268a181b9eb5d3ce3ed142e7d41ea72d65953fe8cc064bb7f22240e8ae58  node-v12.18.3-linux-armv7l.tar.xz
376f76be1e2512a6b6a69aadca063b2b64e476869da8c30c8c649303c2c19aa8  node-v12.18.3-linux-ppc64le.tar.gz
3a4d575c41697d4f24c8011206848eae945ed86f6635386ccc9a6e47e1842697  node-v12.18.3-linux-ppc64le.tar.xz
b658a78b1c194e9faf1b6955e1fd7eacaad228698a6b4744ffc6d44ffa31e74c  node-v12.18.3-linux-s390x.tar.gz
0cdf850df27166e1a7849d684870e2fac740faeeac69777ff14e6f7d33c9a50f  node-v12.18.3-linux-s390x.tar.xz
8cdacecc43c35bcfa5474c793b9e7a01835e4171264f7b13f3e57093371872e9  node-v12.18.3-linux-x64.tar.gz
b79e02e48d0a1ee4cd4ae138de97fda5413542f2a4f441a7d0e189697b8da563  node-v12.18.3-linux-x64.tar.xz
6b9d85b6fd7a16b33dd1a429a35a4f3bce325f509a0bdcdd0b5576696fbca45a  node-v12.18.3.pkg
0c3a37a3f4030d4c58bf6f04227c43bf7ef83a8035421147e144f595619577aa  node-v12.18.3-sunos-x64.tar.gz
e462dffce649d31bc1517fe6b083d2ff6ec59ce72abdcb3fe8123f055511acfb  node-v12.18.3-sunos-x64.tar.xz
6ea85f80e01b007cc9b566b8836513bc5102667d833bad4c1092be60fa60c2d4  node-v12.18.3.tar.gz
71158026579487422fd13cc2553b34cddb76519098aa6030faab52f88c6e0d0e  node-v12.18.3.tar.xz
e1f8fdab7f066af70ac085d2a58c9f533a20299b0de8ae8a09273655c3d787f4  node-v12.18.3-win-x64.7z
1fcd30c09f0ac9fbecf45161519eff2cf621c199eef4663b1db41b3f2fef4041  node-v12.18.3-win-x64.zip
4db428212a33e512a472c402b00505d1a0b5e90b28157bb1f944b73bc490b2b0  node-v12.18.3-win-x86.7z
6b0ffa5703de66b63037b0bddb99c1bb62a503b232b326e324339bb83b63be34  node-v12.18.3-win-x86.zip
8cc457d650137b468d40837cc939e8be64313974375410fdc33cf58670ada94b  node-v12.18.3-x64.msi
0df381eb604b92de74f4ff174acedeb3a6e20c85cf02d713c7f1de395d41127b  node-v12.18.3-x86.msi
f51806803e733dd5672f846f312de0a89775f6d184d029c8da2d1e205ed06eff  win-x64/node.exe
8dc997ba5be3529b376943dca1a5cd6e299034f2c922b3ff672b85e4236bf9ff  win-x64/node.lib
926df4442d8d5217403c5242fc23a998f197db985affe438db23c681101d1d6b  win-x64/node_pdb.7z
303f6714c2f4610cac2453be65c81e90c227a990d21ce402f5a9b59ba427eb08  win-x64/node_pdb.zip
e96323072c1da551d3519a43480c92421934a008ea4cb9038824251df56798aa  win-x86/node.exe
98fe66e39f2bd4d296c2922e38ac7a8ab6ae4d39d80cb75bb5b0d9c9e7ae8048  win-x86/node.lib
8d1abfe25f943b370834ff062748485cf4f47d5c268b71acaf5363b2bfcbe838  win-x86/node_pdb.7z
770390910ad632e6360b265ea5e1a5bd514e91d69e2d05deb4304cc99b26c587  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEueL1mBqm4M0oFg2f8TmTp1WZZTwFAl8Yb4sACgkQ8TmTp1WZ
ZTwxQA/8COGDF+UCBlmsX9zPeYXE20L2nejul3jRUyiEBovn6iK+cmluRsCJep1y
6vVRs4ZFMnq7R/9CsQk40H1UJ9UuVuUQbIFeHlv1xP3p+gqQH+DcVxgknl72fuY2
0HCZ594UxoUTJReOOwLMVsU5ov/anayRYGGTpirhy/2wF3lemA5CJ4raeG7ipEiL
etT/05aTXhhjh4S3mRyTBl2Br135vbUcMe1yLoxjxrV+jFjEmpGpd4Q1EydlCr/2
Az6taYu+cNy0Er9DDzykZ6OjEpdyy/AMMybftPJ5ROCReWTtnzmzo8sKxRzITXa0
u9f20IK16ZWXMULwm4lwDPs9Mlw20lqCRAt2LIGTHC1WqzPf9NCU8S0Xujwvpc8A
G1Vw/DE7lmVhH9+1MaeF44d7aZFJaNg2Mgb9lngbnJTusvKemFcQHwJ8QC3zumfN
VwemB7yUONAbbEJHIz38PmOLFj/Okr0RXm9lqOG3gWjWfu5dJ6JvgLLn+c3hoQK3
yJUcNtoQYNPxkCs/jLFFoTti/javrlHZaCAwhB/L6l73MbBeEZim1lloh9vZ/yUy
nT102QpykyRzOjEyKNCC0290u4Oe2DbacMaAHoDf0FpI4M/NuedUFlu2hE6o84da
vUw9LJqNShARYYka2LH5F1U9I0L4S94CApC5fzLd1Rjyp0+Y1vI=
=hDdx
-----END PGP SIGNATURE-----

```
