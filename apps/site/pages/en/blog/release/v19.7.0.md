---
date: '2023-02-21T18:22:47.676Z'
category: release
title: Node v19.7.0 (Current)
layout: blog-post
author: <PERSON><PERSON>
---

### Notable Changes

- \[[`60a612607e`](https://github.com/nodejs/node/commit/60a612607e)] - **deps**: upgrade npm to 9.5.0 (npm team) [#46673](https://github.com/nodejs/node/pull/46673)
- \[[`7d6c27eab1`](https://github.com/nodejs/node/commit/7d6c27eab1)] - **deps**: add ada as a dependency (<PERSON><PERSON><PERSON>) [#46410](https://github.com/nodejs/node/pull/46410)
- \[[`a79a8bf85a`](https://github.com/nodejs/node/commit/a79a8bf85a)] - **doc**: add debadree25 to collaborators (<PERSON><PERSON><PERSON>) [#46716](https://github.com/nodejs/node/pull/46716)
- \[[`0c2c322ee6`](https://github.com/nodejs/node/commit/0c2c322ee6)] - **doc**: add deokjinkim to collaborators (Deokjin Kim) [#46444](https://github.com/nodejs/node/pull/46444)
- \[[`9b23309f53`](https://github.com/nodejs/node/commit/9b23309f53)] - **doc,lib,src,test**: rename --test-coverage (Colin Ihrig) [#46017](https://github.com/nodejs/node/pull/46017)
- \[[`8590eb4830`](https://github.com/nodejs/node/commit/8590eb4830)] - **(SEMVER-MINOR)** **lib**: add aborted() utility function (Debadree Chatterjee) [#46494](https://github.com/nodejs/node/pull/46494)
- \[[`164bfe82cc`](https://github.com/nodejs/node/commit/164bfe82cc)] - **(SEMVER-MINOR)** **src**: add initial support for single executable applications (Darshan Sen) [#45038](https://github.com/nodejs/node/pull/45038)
- \[[`f3908411fd`](https://github.com/nodejs/node/commit/f3908411fd)] - **(SEMVER-MINOR)** **src**: allow optional Isolate termination in node::Stop() (Shelley Vohr) [#46583](https://github.com/nodejs/node/pull/46583)
- \[[`c34bac2fed`](https://github.com/nodejs/node/commit/c34bac2fed)] - **(SEMVER-MINOR)** **src**: allow blobs in addition to `FILE*`s in embedder snapshot API (Anna Henningsen) [#46491](https://github.com/nodejs/node/pull/46491)
- \[[`683a1f8f3e`](https://github.com/nodejs/node/commit/683a1f8f3e)] - **(SEMVER-MINOR)** **src**: allow snapshotting from the embedder API (Anna Henningsen) [#45888](https://github.com/nodejs/node/pull/45888)
- \[[`658d2f4710`](https://github.com/nodejs/node/commit/658d2f4710)] - **(SEMVER-MINOR)** **src**: make build_snapshot a per-Isolate option, rather than a global one (Anna Henningsen) [#45888](https://github.com/nodejs/node/pull/45888)
- \[[`6801d3753c`](https://github.com/nodejs/node/commit/6801d3753c)] - **(SEMVER-MINOR)** **src**: add snapshot support for embedder API (Anna Henningsen) [#45888](https://github.com/nodejs/node/pull/45888)
- \[[`e77d538d32`](https://github.com/nodejs/node/commit/e77d538d32)] - **(SEMVER-MINOR)** **src**: allow embedder control of code generation policy (Shelley Vohr) [#46368](https://github.com/nodejs/node/pull/46368)
- \[[`633d3f292d`](https://github.com/nodejs/node/commit/633d3f292d)] - **(SEMVER-MINOR)** **stream**: add abort signal for ReadableStream and WritableStream (Debadree Chatterjee) [#46273](https://github.com/nodejs/node/pull/46273)
- \[[`6119289251`](https://github.com/nodejs/node/commit/6119289251)] - **test_runner**: add initial code coverage support (Colin Ihrig) [#46017](https://github.com/nodejs/node/pull/46017)
- \[[`a51fe3c663`](https://github.com/nodejs/node/commit/a51fe3c663)] - **url**: replace url-parser with ada (Yagiz Nizipli) [#46410](https://github.com/nodejs/node/pull/46410)

### Commits

- \[[`731a7ae9da`](https://github.com/nodejs/node/commit/731a7ae9da)] - **async_hooks**: add async local storage propagation benchmarks (Chengzhong Wu) [#46414](https://github.com/nodejs/node/pull/46414)
- \[[`05ad792a07`](https://github.com/nodejs/node/commit/05ad792a07)] - **async_hooks**: remove experimental onPropagate option (James M Snell) [#46386](https://github.com/nodejs/node/pull/46386)
- \[[`6b21170b10`](https://github.com/nodejs/node/commit/6b21170b10)] - **benchmark**: add trailing commas in `benchmark/path` (Antoine du Hamel) [#46628](https://github.com/nodejs/node/pull/46628)
- \[[`4b89ec409f`](https://github.com/nodejs/node/commit/4b89ec409f)] - **benchmark**: add trailing commas in `benchmark/http` (Antoine du Hamel) [#46609](https://github.com/nodejs/node/pull/46609)
- \[[`ff95eb7386`](https://github.com/nodejs/node/commit/ff95eb7386)] - **benchmark**: add trailing commas in `benchmark/crypto` (Antoine du Hamel) [#46553](https://github.com/nodejs/node/pull/46553)
- \[[`638d9b8d4b`](https://github.com/nodejs/node/commit/638d9b8d4b)] - **benchmark**: add trailing commas in `benchmark/url` (Antoine du Hamel) [#46551](https://github.com/nodejs/node/pull/46551)
- \[[`7524871a9b`](https://github.com/nodejs/node/commit/7524871a9b)] - **benchmark**: add trailing commas in `benchmark/http2` (Antoine du Hamel) [#46552](https://github.com/nodejs/node/pull/46552)
- \[[`9d9b3f856f`](https://github.com/nodejs/node/commit/9d9b3f856f)] - **benchmark**: add trailing commas in `benchmark/process` (Antoine du Hamel) [#46481](https://github.com/nodejs/node/pull/46481)
- \[[`6c69ad6d43`](https://github.com/nodejs/node/commit/6c69ad6d43)] - **benchmark**: add trailing commas in `benchmark/misc` (Antoine du Hamel) [#46474](https://github.com/nodejs/node/pull/46474)
- \[[`7f8b292bee`](https://github.com/nodejs/node/commit/7f8b292bee)] - **benchmark**: add trailing commas in `benchmark/buffers` (Antoine du Hamel) [#46473](https://github.com/nodejs/node/pull/46473)
- \[[`897e3c2782`](https://github.com/nodejs/node/commit/897e3c2782)] - **benchmark**: add trailing commas in `benchmark/module` (Antoine du Hamel) [#46461](https://github.com/nodejs/node/pull/46461)
- \[[`7760d40c04`](https://github.com/nodejs/node/commit/7760d40c04)] - **benchmark**: add trailing commas in `benchmark/net` (Antoine du Hamel) [#46439](https://github.com/nodejs/node/pull/46439)
- \[[`8b88d605ca`](https://github.com/nodejs/node/commit/8b88d605ca)] - **benchmark**: add trailing commas in `benchmark/util` (Antoine du Hamel) [#46438](https://github.com/nodejs/node/pull/46438)
- \[[`2c8c9f978d`](https://github.com/nodejs/node/commit/2c8c9f978d)] - **benchmark**: add trailing commas in `benchmark/async_hooks` (Antoine du Hamel) [#46424](https://github.com/nodejs/node/pull/46424)
- \[[`b364b9bd60`](https://github.com/nodejs/node/commit/b364b9bd60)] - **benchmark**: add trailing commas in `benchmark/fs` (Antoine du Hamel) [#46426](https://github.com/nodejs/node/pull/46426)
- \[[`e15ddba7e7`](https://github.com/nodejs/node/commit/e15ddba7e7)] - **build**: add GitHub Action for coverage with --without-intl (Rich Trott) [#37954](https://github.com/nodejs/node/pull/37954)
- \[[`c781a48097`](https://github.com/nodejs/node/commit/c781a48097)] - **build**: do not disable inspector when intl is disabled (Rich Trott) [#37954](https://github.com/nodejs/node/pull/37954)
- \[[`b4deb2fcd5`](https://github.com/nodejs/node/commit/b4deb2fcd5)] - **crypto**: don't assume FIPS is disabled by default (Michael Dawson) [#46532](https://github.com/nodejs/node/pull/46532)
- \[[`60a612607e`](https://github.com/nodejs/node/commit/60a612607e)] - **deps**: upgrade npm to 9.5.0 (npm team) [#46673](https://github.com/nodejs/node/pull/46673)
- \[[`6c997035fc`](https://github.com/nodejs/node/commit/6c997035fc)] - **deps**: update corepack to 0.16.0 (Node.js GitHub Bot) [#46710](https://github.com/nodejs/node/pull/46710)
- \[[`2ed3875eee`](https://github.com/nodejs/node/commit/2ed3875eee)] - **deps**: update undici to 5.20.0 (Node.js GitHub Bot) [#46711](https://github.com/nodejs/node/pull/46711)
- \[[`20cb13bf7f`](https://github.com/nodejs/node/commit/20cb13bf7f)] - **deps**: update ada to v1.0.1 (Yagiz Nizipli) [#46550](https://github.com/nodejs/node/pull/46550)
- \[[`c0983cfc06`](https://github.com/nodejs/node/commit/c0983cfc06)] - **deps**: copy `postject-api.h` and `LICENSE` to the `deps` folder (Darshan Sen) [#46582](https://github.com/nodejs/node/pull/46582)
- \[[`7d6c27eab1`](https://github.com/nodejs/node/commit/7d6c27eab1)] - **deps**: add ada as a dependency (Yagiz Nizipli) [#46410](https://github.com/nodejs/node/pull/46410)
- \[[`7e7e2d037b`](https://github.com/nodejs/node/commit/7e7e2d037b)] - **deps**: update c-ares to 1.19.0 (Michaël Zasso) [#46415](https://github.com/nodejs/node/pull/46415)
- \[[`a79a8bf85a`](https://github.com/nodejs/node/commit/a79a8bf85a)] - **doc**: add debadree25 to collaborators (Debadree Chatterjee) [#46716](https://github.com/nodejs/node/pull/46716)
- \[[`6a8b04d709`](https://github.com/nodejs/node/commit/6a8b04d709)] - **doc**: move bcoe to emeriti (Benjamin Coe) [#46703](https://github.com/nodejs/node/pull/46703)
- \[[`a0a6ee0f54`](https://github.com/nodejs/node/commit/a0a6ee0f54)] - **doc**: add response.strictContentLength to documentation (Marco Ippolito) [#46627](https://github.com/nodejs/node/pull/46627)
- \[[`ffdd64dce3`](https://github.com/nodejs/node/commit/ffdd64dce3)] - **doc**: remove unused functions from example of `streamConsumers.text` (Deokjin Kim) [#46581](https://github.com/nodejs/node/pull/46581)
- \[[`c771d66864`](https://github.com/nodejs/node/commit/c771d66864)] - **doc**: fix test runner examples (Richie McColl) [#46565](https://github.com/nodejs/node/pull/46565)
- \[[`375bb22df9`](https://github.com/nodejs/node/commit/375bb22df9)] - **doc**: update test concurrency description / default values (richiemccoll) [#46457](https://github.com/nodejs/node/pull/46457)
- \[[`a7beac04ba`](https://github.com/nodejs/node/commit/a7beac04ba)] - **doc**: enrich test command with executable (Tony Gorez) [#44347](https://github.com/nodejs/node/pull/44347)
- \[[`aef57cd290`](https://github.com/nodejs/node/commit/aef57cd290)] - **doc**: fix wrong location of `requestTimeout`'s default value (Deokjin Kim) [#46423](https://github.com/nodejs/node/pull/46423)
- \[[`0c2c322ee6`](https://github.com/nodejs/node/commit/0c2c322ee6)] - **doc**: add deokjinkim to collaborators (Deokjin Kim) [#46444](https://github.com/nodejs/node/pull/46444)
- \[[`31d3e3c486`](https://github.com/nodejs/node/commit/31d3e3c486)] - **doc**: fix -C flag usage (三咲智子 Kevin Deng) [#46388](https://github.com/nodejs/node/pull/46388)
- \[[`905a6756a3`](https://github.com/nodejs/node/commit/905a6756a3)] - **doc**: add note about major release rotation (Rafael Gonzaga) [#46436](https://github.com/nodejs/node/pull/46436)
- \[[`33a98c42fa`](https://github.com/nodejs/node/commit/33a98c42fa)] - **doc**: update threat model based on discussions (Michael Dawson) [#46373](https://github.com/nodejs/node/pull/46373)
- \[[`9b23309f53`](https://github.com/nodejs/node/commit/9b23309f53)] - **doc,lib,src,test**: rename --test-coverage (Colin Ihrig) [#46017](https://github.com/nodejs/node/pull/46017)
- \[[`f192b83800`](https://github.com/nodejs/node/commit/f192b83800)] - **esm**: misc test refactors (Geoffrey Booth) [#46631](https://github.com/nodejs/node/pull/46631)
- \[[`7f2cdd36cf`](https://github.com/nodejs/node/commit/7f2cdd36cf)] - **http**: add note about clientError event (Paolo Insogna) [#46584](https://github.com/nodejs/node/pull/46584)
- \[[`d8c527f24f`](https://github.com/nodejs/node/commit/d8c527f24f)] - **http**: use v8::Array::New() with a prebuilt vector (Joyee Cheung) [#46447](https://github.com/nodejs/node/pull/46447)
- \[[`fa600fe003`](https://github.com/nodejs/node/commit/fa600fe003)] - **lib**: add trailing commas in `internal/process` (Antoine du Hamel) [#46687](https://github.com/nodejs/node/pull/46687)
- \[[`4aebee63f0`](https://github.com/nodejs/node/commit/4aebee63f0)] - **lib**: do not crash using workers with disabled shared array buffers (Ruben Bridgewater) [#41023](https://github.com/nodejs/node/pull/41023)
- \[[`a740908588`](https://github.com/nodejs/node/commit/a740908588)] - **lib**: delete module findPath unused params (sinkhaha) [#45371](https://github.com/nodejs/node/pull/45371)
- \[[`8b46c763d9`](https://github.com/nodejs/node/commit/8b46c763d9)] - **lib**: enforce use of trailing commas in more files (Antoine du Hamel) [#46655](https://github.com/nodejs/node/pull/46655)
- \[[`aae0020e27`](https://github.com/nodejs/node/commit/aae0020e27)] - **lib**: enforce use of trailing commas for functions (Antoine du Hamel) [#46629](https://github.com/nodejs/node/pull/46629)
- \[[`da9ebaf138`](https://github.com/nodejs/node/commit/da9ebaf138)] - **lib**: predeclare Event.isTrusted prop descriptor (Santiago Gimeno) [#46527](https://github.com/nodejs/node/pull/46527)
- \[[`35570e970e`](https://github.com/nodejs/node/commit/35570e970e)] - **lib**: tighten `AbortSignal.prototype.throwIfAborted` implementation (Antoine du Hamel) [#46521](https://github.com/nodejs/node/pull/46521)
- \[[`8590eb4830`](https://github.com/nodejs/node/commit/8590eb4830)] - **(SEMVER-MINOR)** **lib**: add aborted() utility function (Debadree Chatterjee) [#46494](https://github.com/nodejs/node/pull/46494)
- \[[`5d1a729f76`](https://github.com/nodejs/node/commit/5d1a729f76)] - **meta**: update AUTHORS (Node.js GitHub Bot) [#46624](https://github.com/nodejs/node/pull/46624)
- \[[`cb9b9ad879`](https://github.com/nodejs/node/commit/cb9b9ad879)] - **meta**: move one or more collaborators to emeritus (Node.js GitHub Bot) [#46513](https://github.com/nodejs/node/pull/46513)
- \[[`17b82c85d9`](https://github.com/nodejs/node/commit/17b82c85d9)] - **meta**: update AUTHORS (Node.js GitHub Bot) [#46504](https://github.com/nodejs/node/pull/46504)
- \[[`bb14a2b098`](https://github.com/nodejs/node/commit/bb14a2b098)] - **meta**: move one or more collaborators to emeritus (Node.js GitHub Bot) [#46411](https://github.com/nodejs/node/pull/46411)
- \[[`152a3c7d1d`](https://github.com/nodejs/node/commit/152a3c7d1d)] - **process**: print versions by sort (Himself65) [#46428](https://github.com/nodejs/node/pull/46428)
- \[[`164bfe82cc`](https://github.com/nodejs/node/commit/164bfe82cc)] - **(SEMVER-MINOR)** **src**: add initial support for single executable applications (Darshan Sen) [#45038](https://github.com/nodejs/node/pull/45038)
- \[[`f3908411fd`](https://github.com/nodejs/node/commit/f3908411fd)] - **(SEMVER-MINOR)** **src**: allow optional Isolate termination in node::Stop() (Shelley Vohr) [#46583](https://github.com/nodejs/node/pull/46583)
- \[[`bdba600d32`](https://github.com/nodejs/node/commit/bdba600d32)] - **src**: remove icu usage from node_string.cc (Yagiz Nizipli) [#46548](https://github.com/nodejs/node/pull/46548)
- \[[`31fb2e22a0`](https://github.com/nodejs/node/commit/31fb2e22a0)] - **src**: add fflush() to SnapshotData::ToFile() (Anna Henningsen) [#46531](https://github.com/nodejs/node/pull/46531)
- \[[`c34bac2fed`](https://github.com/nodejs/node/commit/c34bac2fed)] - **(SEMVER-MINOR)** **src**: allow blobs in addition to `FILE*`s in embedder snapshot API (Anna Henningsen) [#46491](https://github.com/nodejs/node/pull/46491)
- \[[`c3325bfc0d`](https://github.com/nodejs/node/commit/c3325bfc0d)] - **src**: make edge names in BaseObjects more descriptive in heap snapshots (Joyee Cheung) [#46492](https://github.com/nodejs/node/pull/46492)
- \[[`3c5db8f419`](https://github.com/nodejs/node/commit/3c5db8f419)] - **src**: avoid leaking snapshot fp on error (Tobias Nießen) [#46497](https://github.com/nodejs/node/pull/46497)
- \[[`1a808a4aad`](https://github.com/nodejs/node/commit/1a808a4aad)] - **src**: check return value of ftell() (Tobias Nießen) [#46495](https://github.com/nodejs/node/pull/46495)
- \[[`f72f643549`](https://github.com/nodejs/node/commit/f72f643549)] - **src**: remove unused includes from main thread (Yagiz Nizipli) [#46471](https://github.com/nodejs/node/pull/46471)
- \[[`60c2a863da`](https://github.com/nodejs/node/commit/60c2a863da)] - **src**: use string_view instead of std::string& (Yagiz Nizipli) [#46471](https://github.com/nodejs/node/pull/46471)
- \[[`f35f6d2218`](https://github.com/nodejs/node/commit/f35f6d2218)] - **src**: use simdutf utf8 to utf16 instead of icu (Yagiz Nizipli) [#46471](https://github.com/nodejs/node/pull/46471)
- \[[`00b81c7afe`](https://github.com/nodejs/node/commit/00b81c7afe)] - **src**: replace icu with simdutf for char counts (Yagiz Nizipli) [#46472](https://github.com/nodejs/node/pull/46472)
- \[[`683a1f8f3e`](https://github.com/nodejs/node/commit/683a1f8f3e)] - **(SEMVER-MINOR)** **src**: allow snapshotting from the embedder API (Anna Henningsen) [#45888](https://github.com/nodejs/node/pull/45888)
- \[[`658d2f4710`](https://github.com/nodejs/node/commit/658d2f4710)] - **(SEMVER-MINOR)** **src**: make build_snapshot a per-Isolate option, rather than a global one (Anna Henningsen) [#45888](https://github.com/nodejs/node/pull/45888)
- \[[`6801d3753c`](https://github.com/nodejs/node/commit/6801d3753c)] - **(SEMVER-MINOR)** **src**: add snapshot support for embedder API (Anna Henningsen) [#45888](https://github.com/nodejs/node/pull/45888)
- \[[`95065c3185`](https://github.com/nodejs/node/commit/95065c3185)] - **src**: add additional utilities to crypto::SecureContext (James M Snell) [#45912](https://github.com/nodejs/node/pull/45912)
- \[[`efc59d0843`](https://github.com/nodejs/node/commit/efc59d0843)] - **src**: add KeyObjectHandle::HasInstance (James M Snell) [#45912](https://github.com/nodejs/node/pull/45912)
- \[[`a8a2d0e2b1`](https://github.com/nodejs/node/commit/a8a2d0e2b1)] - **src**: add GetCurrentCipherName/Version to crypto_common (James M Snell) [#45912](https://github.com/nodejs/node/pull/45912)
- \[[`6cf860d3d6`](https://github.com/nodejs/node/commit/6cf860d3d6)] - **src**: back snapshot I/O with a std::vector sink (Joyee Cheung) [#46463](https://github.com/nodejs/node/pull/46463)
- \[[`e77d538d32`](https://github.com/nodejs/node/commit/e77d538d32)] - **(SEMVER-MINOR)** **src**: allow embedder control of code generation policy (Shelley Vohr) [#46368](https://github.com/nodejs/node/pull/46368)
- \[[`7756438c81`](https://github.com/nodejs/node/commit/7756438c81)] - **stream**: add trailing commas in webstream source files (Antoine du Hamel) [#46685](https://github.com/nodejs/node/pull/46685)
- \[[`6b64a945c6`](https://github.com/nodejs/node/commit/6b64a945c6)] - **stream**: add trailing commas in stream source files (Antoine du Hamel) [#46686](https://github.com/nodejs/node/pull/46686)
- \[[`633d3f292d`](https://github.com/nodejs/node/commit/633d3f292d)] - **(SEMVER-MINOR)** **stream**: add abort signal for ReadableStream and WritableStream (Debadree Chatterjee) [#46273](https://github.com/nodejs/node/pull/46273)
- \[[`f91260b32a`](https://github.com/nodejs/node/commit/f91260b32a)] - **stream**: refactor to use `validateAbortSignal` (Antoine du Hamel) [#46520](https://github.com/nodejs/node/pull/46520)
- \[[`6bf7388b62`](https://github.com/nodejs/node/commit/6bf7388b62)] - **stream**: allow transfer of readable byte streams (MrBBot) [#45955](https://github.com/nodejs/node/pull/45955)
- \[[`c2068537fa`](https://github.com/nodejs/node/commit/c2068537fa)] - **stream**: add pipeline() for webstreams (Debadree Chatterjee) [#46307](https://github.com/nodejs/node/pull/46307)
- \[[`4cf4b41c56`](https://github.com/nodejs/node/commit/4cf4b41c56)] - **stream**: add suport for abort signal in finished() for webstreams (Debadree Chatterjee) [#46403](https://github.com/nodejs/node/pull/46403)
- \[[`b844a09fa5`](https://github.com/nodejs/node/commit/b844a09fa5)] - **stream**: dont access Object.prototype.type during TransformStream init (Debadree Chatterjee) [#46389](https://github.com/nodejs/node/pull/46389)
- \[[`6ad01fd7b5`](https://github.com/nodejs/node/commit/6ad01fd7b5)] - **test**: fix `test-net-autoselectfamily` for kernel without IPv6 support (Livia Medeiros) [#45856](https://github.com/nodejs/node/pull/45856)
- \[[`2239e24306`](https://github.com/nodejs/node/commit/2239e24306)] - **test**: fix assertions in test-snapshot-dns-lookup\* (Tobias Nießen) [#46618](https://github.com/nodejs/node/pull/46618)
- \[[`c4ca98e786`](https://github.com/nodejs/node/commit/c4ca98e786)] - **test**: cover publicExponent validation in OpenSSL (Tobias Nießen) [#46632](https://github.com/nodejs/node/pull/46632)
- \[[`e60d3f2b1d`](https://github.com/nodejs/node/commit/e60d3f2b1d)] - **test**: add WPTRunner support for variants and generating WPT reports (Filip Skokan) [#46498](https://github.com/nodejs/node/pull/46498)
- \[[`217f2f6e2a`](https://github.com/nodejs/node/commit/217f2f6e2a)] - **test**: add trailing commas in `test/pummel` (Antoine du Hamel) [#46610](https://github.com/nodejs/node/pull/46610)
- \[[`641e1771c8`](https://github.com/nodejs/node/commit/641e1771c8)] - **test**: enable api-invalid-label.any.js in encoding WPTs (Filip Skokan) [#46506](https://github.com/nodejs/node/pull/46506)
- \[[`89aa161173`](https://github.com/nodejs/node/commit/89aa161173)] - **test**: fix tap parser fails if a test logs a number (Pulkit Gupta) [#46056](https://github.com/nodejs/node/pull/46056)
- \[[`faba8d4a30`](https://github.com/nodejs/node/commit/faba8d4a30)] - **test**: add trailing commas in `test/js-native-api` (Antoine du Hamel) [#46385](https://github.com/nodejs/node/pull/46385)
- \[[`d556ccdd26`](https://github.com/nodejs/node/commit/d556ccdd26)] - **test**: make more crypto tests work with BoringSSL (Shelley Vohr) [#46429](https://github.com/nodejs/node/pull/46429)
- \[[`c7f29b24a6`](https://github.com/nodejs/node/commit/c7f29b24a6)] - **test**: add trailing commas in `test/known_issues` (Antoine du Hamel) [#46408](https://github.com/nodejs/node/pull/46408)
- \[[`a66e7ca6c5`](https://github.com/nodejs/node/commit/a66e7ca6c5)] - **test**: add trailing commas in `test/internet` (Antoine du Hamel) [#46407](https://github.com/nodejs/node/pull/46407)
- \[[`0f75633086`](https://github.com/nodejs/node/commit/0f75633086)] - **test,crypto**: update WebCryptoAPI WPT (Filip Skokan) [#46575](https://github.com/nodejs/node/pull/46575)
- \[[`ddf5002782`](https://github.com/nodejs/node/commit/ddf5002782)] - **test_runner**: parse non-ascii character correctly (Mert Can Altın) [#45736](https://github.com/nodejs/node/pull/45736)
- \[[`5b748114d2`](https://github.com/nodejs/node/commit/5b748114d2)] - **test_runner**: allow nesting test within describe (Moshe Atlow) [#46544](https://github.com/nodejs/node/pull/46544)
- \[[`c526f9f70a`](https://github.com/nodejs/node/commit/c526f9f70a)] - **test_runner**: fix missing test diagnostics (Moshe Atlow) [#46450](https://github.com/nodejs/node/pull/46450)
- \[[`b31aabb101`](https://github.com/nodejs/node/commit/b31aabb101)] - **test_runner**: top-level diagnostics not ommited when running with --test (Pulkit Gupta) [#46441](https://github.com/nodejs/node/pull/46441)
- \[[`6119289251`](https://github.com/nodejs/node/commit/6119289251)] - **test_runner**: add initial code coverage support (Colin Ihrig) [#46017](https://github.com/nodejs/node/pull/46017)
- \[[`6f24f0621e`](https://github.com/nodejs/node/commit/6f24f0621e)] - **timers**: cleanup no-longer relevant TODOs in timers/promises (James M Snell) [#46499](https://github.com/nodejs/node/pull/46499)
- \[[`1cd22e7d19`](https://github.com/nodejs/node/commit/1cd22e7d19)] - **tools**: fix bug in `prefer-primordials` lint rule (Antoine du Hamel) [#46659](https://github.com/nodejs/node/pull/46659)
- \[[`87df34ac28`](https://github.com/nodejs/node/commit/87df34ac28)] - **tools**: fix update-ada script (Yagiz Nizipli) [#46550](https://github.com/nodejs/node/pull/46550)
- \[[`f62b58a623`](https://github.com/nodejs/node/commit/f62b58a623)] - **tools**: add a daily wpt.fyi synchronized report upload (Filip Skokan) [#46498](https://github.com/nodejs/node/pull/46498)
- \[[`803f00aa32`](https://github.com/nodejs/node/commit/803f00aa32)] - **tools**: update eslint to 8.34.0 (Node.js GitHub Bot) [#46625](https://github.com/nodejs/node/pull/46625)
- \[[`f87216bdb2`](https://github.com/nodejs/node/commit/f87216bdb2)] - **tools**: update lint-md-dependencies to rollup\@3.15.0 to-vfile\@7.2.4 (Node.js GitHub Bot) [#46623](https://github.com/nodejs/node/pull/46623)
- \[[`8ee9e48560`](https://github.com/nodejs/node/commit/8ee9e48560)] - **tools**: update doc to remark-html\@15.0.2 to-vfile\@7.2.4 (Node.js GitHub Bot) [#46622](https://github.com/nodejs/node/pull/46622)
- \[[`148c5d9239`](https://github.com/nodejs/node/commit/148c5d9239)] - **tools**: update lint-md-dependencies to rollup\@3.13.0 vfile-reporter\@7.0.5 (Node.js GitHub Bot) [#46503](https://github.com/nodejs/node/pull/46503)
- \[[`51c6c61a58`](https://github.com/nodejs/node/commit/51c6c61a58)] - **tools**: update ESLint custom rules to not use the deprecated format (Antoine du Hamel) [#46460](https://github.com/nodejs/node/pull/46460)
- \[[`a51fe3c663`](https://github.com/nodejs/node/commit/a51fe3c663)] - **url**: replace url-parser with ada (Yagiz Nizipli) [#46410](https://github.com/nodejs/node/pull/46410)
- \[[`129c9e7180`](https://github.com/nodejs/node/commit/129c9e7180)] - **url**: remove unused `URL::ToFilePath()` (Yagiz Nizipli) [#46487](https://github.com/nodejs/node/pull/46487)
- \[[`9a604d67c3`](https://github.com/nodejs/node/commit/9a604d67c3)] - **url**: remove unused `URL::toObject` (Yagiz Nizipli) [#46486](https://github.com/nodejs/node/pull/46486)
- \[[`d6fbebda54`](https://github.com/nodejs/node/commit/d6fbebda54)] - **url**: remove unused `setURLConstructor` function (Yagiz Nizipli) [#46485](https://github.com/nodejs/node/pull/46485)
- \[[`17b3ee33c2`](https://github.com/nodejs/node/commit/17b3ee33c2)] - **vm**: properly support symbols on globals (Nicolas DUBIEN) [#46458](https://github.com/nodejs/node/pull/46458)

Windows 32-bit Installer: https://nodejs.org/dist/v19.7.0/node-v19.7.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v19.7.0/node-v19.7.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v19.7.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v19.7.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v19.7.0/node-v19.7.0.pkg \
macOS Apple Silicon 64-bit Binary: https://nodejs.org/dist/v19.7.0/node-v19.7.0-darwin-arm64.tar.gz \
macOS Intel 64-bit Binary: https://nodejs.org/dist/v19.7.0/node-v19.7.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v19.7.0/node-v19.7.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v19.7.0/node-v19.7.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v19.7.0/node-v19.7.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v19.7.0/node-v19.7.0-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v19.7.0/node-v19.7.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v19.7.0/node-v19.7.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v19.7.0/node-v19.7.0.tar.gz \
Other release files: https://nodejs.org/dist/v19.7.0/ \
Documentation: https://nodejs.org/docs/v19.7.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

af6a7a636db2a10bfd563181095ed9bcce4a2e49b519649ed0620d48509c1afc  node-v19.7.0-aix-ppc64.tar.gz
7a96935baf731d0917a96370dda707b8195ae0a123d6c5ff777d41c3fdda949d  node-v19.7.0-darwin-arm64.tar.gz
16fc661b7f95a4a871fe32acf0b72eb3001e3a41cee9b7af2c50369e028c4d6d  node-v19.7.0-darwin-arm64.tar.xz
2b8593445a4ffc6f42020827dce134497204d55d1ac4a705c0919583d2e6a781  node-v19.7.0-darwin-x64.tar.gz
844ae93118f024ff00c47412456de53677ffeb85bbfdebec75d4c9fea84b8c1d  node-v19.7.0-darwin-x64.tar.xz
63faf9adcf3d35de8e4426811105beea03c5d93a09c0aa277dcff8b45dccbb32  node-v19.7.0-headers.tar.gz
53bcb1b0688dda574129cb9ee7ec30efe7c77b87166715b1265758a41cec49d1  node-v19.7.0-headers.tar.xz
a9d3c9c3bfdcf7849161b56df8de652552f723144bb05e3df015269523c6e14b  node-v19.7.0-linux-arm64.tar.gz
1b582ccfaa63c2b8eda93c4f8f08743da4c9d46b08c3bd831ba4bd2339db7e2d  node-v19.7.0-linux-arm64.tar.xz
ad22832d56d202bee48c3612efbc51e9460c86db9d81b6d026fb38374a5ba914  node-v19.7.0-linux-armv7l.tar.gz
4f06bbaf2d81d9094841a20f944aa472d57cb832adf244f1e435585ed6e10514  node-v19.7.0-linux-armv7l.tar.xz
66bcab8612a1213714c9e106a11c3647aa1991b93a218dd000aeeb1a4d0fe351  node-v19.7.0-linux-ppc64le.tar.gz
22ec61a92f5c75455e9a9182593b2ef460a1eed4b3ed2e535cb8d8da3b6baf82  node-v19.7.0-linux-ppc64le.tar.xz
e42d785ec282b200922702fd97b87c31bf2b660bda20eaab1f9e4be62e5de336  node-v19.7.0-linux-s390x.tar.gz
e7839510d3c09c0ce5fc909c516d61907b0be18f097a98ae9ae28638c48408c1  node-v19.7.0-linux-s390x.tar.xz
f5f0ab097f4d120045a327ed2cf9afff264e10c5d304d6ec9529beedfd0c0fd6  node-v19.7.0-linux-x64.tar.gz
66d352bd96bc947828e29d3524716fdf0569f900eaa4bbc0517b618ffd597970  node-v19.7.0-linux-x64.tar.xz
9e14df904e31753973224d1071c814a124f96e28b8befa1f11549151a830dcd0  node-v19.7.0.pkg
3b16e85ce3442caaac35e0f28974a6fa381529c3a6caac1cc36234efa24a9e72  node-v19.7.0.tar.gz
511847f724ea405f85e25803cbcf8b131b14cd06b2d7046698bd2760c404bfc3  node-v19.7.0.tar.xz
32e798d8f139eee1e49d5d91e5547cdcfc80ed3a6b52b0f8455635f9c71e1674  node-v19.7.0-win-x64.7z
b7a6df0d6f14c90ca11b9989a4d135ed5b9a1edc2b16227b353287cf2e750e4a  node-v19.7.0-win-x64.zip
ad8c1a3d6338b4f36df52af1860e5c3c2946b7c3c1552d8184c586acd4c594f5  node-v19.7.0-win-x86.7z
be83b05aee4bcbd0973f757d8d07917ab6ef03ec937e07c2d9d2da03b9dd8fd0  node-v19.7.0-win-x86.zip
7424560c62c5985750abe03e049297eeee68bdc8ea58c4269de2fc4d7194a8e4  node-v19.7.0-x64.msi
76f2adb694ad7f77486058940f66c3589f2cb3aca33fde31ee8323c9a8aba39e  node-v19.7.0-x86.msi
adac1dfe0af865c45b03d08dff6ab983c2429113f82e5dc1d7405f7b35608670  win-x64/node.exe
7159341b365d39d7a7c9115aed8b0113c94702248a9dc7a9b337dc20fb9278e5  win-x64/node.lib
aeaa00c8cfcfc53395eeee151a66f864876d457f9153d1ae8df2506608315dd4  win-x64/node_pdb.7z
deb144f70d224a1136c86a15f010f09bc57fedbb54a6f202d0bc4f7f20a649db  win-x64/node_pdb.zip
86320ab2f36e6832087d6102de89a9f5b15ff67e21d91c30675dbfd736060d92  win-x86/node.exe
56473b0dca973176374298479426c078b4e113e88c949a4a3e541994b76e8c36  win-x86/node.lib
d0eb21767de0502f6fbe956976cddbb65a05207ab337af7d78579ffaef844538  win-x86/node_pdb.7z
8605acbc2332d32dd764f76e60f778307fc41590f5ff71b054b791a0ca62ebae  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQEzBAEBCAAdFiEEDv/hvO/ZyE49CYFSkzsB9AtcqUYFAmP1C2MACgkQkzsB9Atc
qUbySwgAt4ajYUao8V0jA4qSKuy/ZAUv67st+iXCrzSgFovy1a+USLc/uoZodxPF
/750HerLQsgT6F+p2J3vSxplOvuC3TCZCEYX3uDB2fTph4hOdnuRW568LVlxS5KE
6AuLP4yRf/aweEUDIYWyCVP1Y+3yEhHxv0JXWubaMFINcrD6MrjeJ7Rb/ac66PMM
clJjQx9wS8F6/H8CQobyfgHlUeElGtaAzWAGvHBV+F5X49LgfRMEdO49J/07EC7q
58MN7iBYX9QXUYjsLTCXQixejLbL/V1wc+QXPKtvcerOCjfs6xFT2AJ1TeivLf5y
lARu3IftHM59pB491TezmpBW+eKgwQ==
=oi2I
-----END PGP SIGNATURE-----

```
