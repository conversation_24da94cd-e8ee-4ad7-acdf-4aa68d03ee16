---
date: '2022-11-01T21:42:42.856Z'
category: release
title: Node v14.21.0 (LTS)
layout: blog-post
author: <PERSON>
---

### Notable changes

- **deps**:
  - update corepack to 0.14.2 (Node.js GitHub Bot) [#44775](https://github.com/nodejs/node/pull/44775)
- **src**:
  - add --openssl-shared-config option (<PERSON>) [#43124](https://github.com/nodejs/node/pull/43124)

### Commits

- \[[`773f587912`](https://github.com/nodejs/node/commit/773f587912)] - **deps**: cherry-pick libuv/libuv\@3a7b955 (<PERSON>) [#43950](https://github.com/nodejs/node/pull/43950)
- \[[`a1dea66956`](https://github.com/nodejs/node/commit/a1dea66956)] - **deps**: cherry-pick libuv/libuv\@abb109f (<PERSON>) [#43950](https://github.com/nodejs/node/pull/43950)
- \[[`98c49d81f5`](https://github.com/nodejs/node/commit/98c49d81f5)] - **deps**: update corepack to 0.14.2 (Node.js GitHub Bot) [#44775](https://github.com/nodejs/node/pull/44775)
- \[[`18c43c8518`](https://github.com/nodejs/node/commit/18c43c8518)] - **deps**: update timezone to tz2022e (Richard Lau) [#45094](https://github.com/nodejs/node/pull/45094)
- \[[`a1f8e4db48`](https://github.com/nodejs/node/commit/a1f8e4db48)] - **deps**: update corepack to 0.14.1 (Node.js GitHub Bot) [#44704](https://github.com/nodejs/node/pull/44704)
- \[[`e55389ca86`](https://github.com/nodejs/node/commit/e55389ca86)] - **deps**: update corepack to 0.14.0 (Node.js GitHub Bot) [#44509](https://github.com/nodejs/node/pull/44509)
- \[[`0227462418`](https://github.com/nodejs/node/commit/0227462418)] - **deps**: update corepack to 0.13.0 (Node.js GitHub Bot) [#44318](https://github.com/nodejs/node/pull/44318)
- \[[`ee24c320ea`](https://github.com/nodejs/node/commit/ee24c320ea)] - **deps**: update corepack to 0.12.3 (Node.js GitHub Bot) [#44229](https://github.com/nodejs/node/pull/44229)
- \[[`28e9891449`](https://github.com/nodejs/node/commit/28e9891449)] - **deps**: update corepack to 0.12.2 (Node.js GitHub Bot) [#44159](https://github.com/nodejs/node/pull/44159)
- \[[`b6972c9df2`](https://github.com/nodejs/node/commit/b6972c9df2)] - **deps**: update corepack to 0.12.1 (Node.js GitHub Bot) [#43965](https://github.com/nodejs/node/pull/43965)
- \[[`9d6cb3b5f1`](https://github.com/nodejs/node/commit/9d6cb3b5f1)] - **deps**: update corepack to 0.12.0 (Node.js GitHub Bot) [#43748](https://github.com/nodejs/node/pull/43748)
- \[[`fa6c276b4f`](https://github.com/nodejs/node/commit/fa6c276b4f)] - **deps**: update Corepack to 0.11.2 (Maël Nison) [#43402](https://github.com/nodejs/node/pull/43402)
- \[[`4f83d75626`](https://github.com/nodejs/node/commit/4f83d75626)] - **(SEMVER-MAJOR)** **src,doc,test**: add --openssl-shared-config option (Daniel Bevenius) [#43124](https://github.com/nodejs/node/pull/43124)
- \[[`9487028043`](https://github.com/nodejs/node/commit/9487028043)] - **test**: fix intl tests on small-icu builds (Antoine du Hamel) [#41939](https://github.com/nodejs/node/pull/41939)
- \[[`a1d52097f8`](https://github.com/nodejs/node/commit/a1d52097f8)] - **tools**: add more options to track flaky tests (Antoine du Hamel) [#43954](https://github.com/nodejs/node/pull/43954)

Windows 32-bit Installer: https://nodejs.org/dist/v14.21.0/node-v14.21.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v14.21.0/node-v14.21.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v14.21.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v14.21.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v14.21.0/node-v14.21.0.pkg \
macOS Intel 64-bit Binary: https://nodejs.org/dist/v14.21.0/node-v14.21.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v14.21.0/node-v14.21.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v14.21.0/node-v14.21.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v14.21.0/node-v14.21.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v14.21.0/node-v14.21.0-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v14.21.0/node-v14.21.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v14.21.0/node-v14.21.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v14.21.0/node-v14.21.0.tar.gz \
Other release files: https://nodejs.org/dist/v14.21.0/ \
Documentation: https://nodejs.org/docs/v14.21.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

eb26168c8e6e251871581a812bd556ed8759189c262af2c6b44331daaab5b1bd  node-v14.21.0-aix-ppc64.tar.gz
027d7e5999ed890d658e87f96a5edb7d9a8f26ee67e732f632a7adb850c43b70  node-v14.21.0-darwin-x64.tar.gz
2fc0afda64a9a6deaa24621d66f66b94af5c6adb3cacf34dea632ddb96167ba2  node-v14.21.0-darwin-x64.tar.xz
f437b08ff3e6c12c48b9e73e32587770c2d7bac0210947c0b1ab1d36e79d465d  node-v14.21.0-headers.tar.gz
eb11a131d540fed96e78d4bcef3869bb48aef727a5fb20a1b08f1c0accc24f49  node-v14.21.0-headers.tar.xz
a06df30ae4393296872f2a80e73f2eea0634c3490edccb2d80bdee5f1449e96f  node-v14.21.0-linux-arm64.tar.gz
631d528a953f22f06bb208e076d361ac8dd0e71c1f6c3c091d81864a7c06bcdd  node-v14.21.0-linux-arm64.tar.xz
72bf674499dbf1beae2a0b210b635440d2f025bc065a654b7d262b130f6ee6f4  node-v14.21.0-linux-armv7l.tar.gz
6d8ecd9b52d1a0118140d413e7992da88d0263ecf15eedb8c1e61a32898bfae7  node-v14.21.0-linux-armv7l.tar.xz
0a9e0a66fabb27a289e13a132bf2b22bf3c23adb8818874e29e099d996092fd5  node-v14.21.0-linux-ppc64le.tar.gz
cf710493322ad1dbe224efb2b7c96367e1a077a03e7680a8b0c341624752469e  node-v14.21.0-linux-ppc64le.tar.xz
c0bea6289eef4d5286a2ad5967d6e6a8dd4b8b318821c50523aba720ba4a8cbf  node-v14.21.0-linux-s390x.tar.gz
bf9a61e2bdb81b7a147a60ee4a3a1262ff35d3c5f6d5824084976b42383ea4fe  node-v14.21.0-linux-s390x.tar.xz
ac808106e79f90bbb0ceb44c5c9c57306117f21d962f0ca54a58993266c514dc  node-v14.21.0-linux-x64.tar.gz
d58116156944be686b3858d95466e0b6437e59f33924ce83ff171e3ffda291d1  node-v14.21.0-linux-x64.tar.xz
515b727b30bd24a377d6c88dc329659c16b624e9a71c1024add709bf6e56b68d  node-v14.21.0.pkg
228f02347f7e4ae7ee844b911147fdf7e1d738dcf000abf0fc067b9161510785  node-v14.21.0.tar.gz
3b4bdac1baccc54baee2e3dbcab7bb635224c716ee76ee49aa4f6f54c28f7991  node-v14.21.0.tar.xz
43cc13e774346cf06f4fb562f911e1b8c779a3b72e7b934cbb932981a48570c5  node-v14.21.0-win-x64.7z
12dbfd835539448713744433042525b64a5ff5629fa5fd67a782d5fa46b973c3  node-v14.21.0-win-x64.zip
3dd71222b4dbbd4aa8a738a5c58882a0f1f3dbb3532071e58157c5a20b0f19c5  node-v14.21.0-win-x86.7z
c1f9bbb84e0893cb317ba3ffacbb19b40e629a933ca434e1f6deee516897fcdf  node-v14.21.0-win-x86.zip
897f34f47b2fded344ba9433ad7594e072616051be3137b628ab55d6231e6fee  node-v14.21.0-x64.msi
545c979ad448ecf7d605c2e233326cd2bae197bc8aea15ac92a2e4b3a6d3763f  node-v14.21.0-x86.msi
cc509058cebceb9dbb90f2ee4f24b41c4597e83367fc9195365e0f4ed0b8aee7  win-x64/node.exe
dea0607a3fca01c7bce5dbe6e275f149bbe1076811d156c6e34e1910028365e7  win-x64/node.lib
1c033898bc767395b781fc01608ffc63498063253dd1b665b2c60fd1a88805dd  win-x64/node_pdb.7z
eb183286c5cf4a6de76003e029fc4c4287a40175d1713096a3df75872c548619  win-x64/node_pdb.zip
30afe59d4ea6a14902e1db8b84b88d66a07811e3b364d0f8a2a6cc0d88e86ca6  win-x86/node.exe
b7ede8c768bc23de8d155724fcfdc828e7b90a2c5831ae95f0d619ea335b3eb0  win-x86/node.lib
060f648dfe04d0cc902e6453b3b791586e44d335b1810283f226f65bdaba93ac  win-x86/node_pdb.7z
7bc054e43674680f124b25fb4a05a8c6280ff4420e3d16fcc67098d144b71ee8  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEdPEmArbxxOkT+qN606iWE2Q7YgEFAmNhkhEACgkQ06iWE2Q7
YgFkBxAA3Majed9DWTR1lKmfOkLetzlbuAnAQ+lk/NZZmsZKhVUE//giFoQSQQ1G
AOsPNlPJH7TWL6XoHREpaIzAQvkbr07yikObUyrcIkncqivxKk+dIuK3rBuY8ofd
51Kqtb1GP0lJZ3u4EesM5pVwztHi5zH08dWvPhOg2GO8f4TyH5ldQIePYi27sodV
ZwqBzLGuxPso99smFKw5YlKdIJYkX+qHBoWrpAL46daLjccKK62u/65lJnE8pfEl
5rDg8qaMZrHCQqQhxZ1kR+tQqOMX3GPtNaUVH98MIg6kqZYDC1Dh7hRr7lnjj0ww
5BQ85xrQXSB7VSlF4vqAOg+/Lcok4VkS1nzmLBq4WqCs2wkdVnRW7kEMorvFOozl
jpfB+p8RIY95/blonqlkCLNFIac9gCWZKaIC9+dk6I2fX5VZ2SsrQpNsrBTQD5CQ
+TZhW3+dOcJHH471J1pD3hRSF4TRprhYrLMQjB3EQjhaH89eXDyux/qEBhBcUj9X
N2pYf3DvctI34DyVa0jnfAal73q62yUpDdvt8NWcOPi/hNYoxY3AoutZ/FSLfAGH
LCW3Scu/9H5vK/hvaUJIfOp5UQyOK7ZpzLP8z7IepXGQhTCZEMdD/NHbtOqhXCB+
FNvj/NCX2EJvwZVWkSS/IlUNE4Ef8qGW39IytuUpDwMpNx6Q1kw=
=l+sK
-----END PGP SIGNATURE-----

```
