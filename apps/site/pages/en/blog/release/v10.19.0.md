---
date: '2020-02-06T03:11:18.806Z'
category: release
title: Node v10.19.0 (LTS)
layout: blog-post
author: <PERSON>
---

### Notable changes

This is a security release.

Vulnerabilities fixed:

- **CVE-2019-15606**: HTTP header values do not have trailing OWS trimmed.
- **CVE-2019-15605**: HTTP request smuggling using malformed Transfer-Encoding header.
- **CVE-2019-15604**: Remotely trigger an assertion on a TLS server with a malformed certificate string.

Also, HTTP parsing is more strict to be more secure. Since this may
cause problems in interoperability with some non-conformant HTTP
implementations, it is possible to disable the strict checks with the
`--insecure-http-parser` command line flag, or the `insecureHTTPParser`
http option. Using the insecure HTTP parser should be avoided.

### Commits

- [[`f940bee3b7`](https://github.com/nodejs/node/commit/f940bee3b7)] - **crypto**: fix assertion caused by unsupported ext (<PERSON><PERSON>) [nodejs-private/node-private#175](https://github.com/nodejs-private/node-private/pull/175)
- [[`49f4220ce5`](https://github.com/nodejs/node/commit/49f4220ce5)] - **deps**: upgrade http-parser to v2.9.3 (Sam Roberts) [nodejs-private/http-parser-private#4](https://github.com/nodejs-private/http-parser-private/pull/4)
- [[`a28e5cc1ed`](https://github.com/nodejs/node/commit/a28e5cc1ed)] - **(SEMVER-MINOR)** **deps**: upgrade http-parser to v2.9.1 (Sam Roberts) [#30471](https://github.com/nodejs/node/pull/30471)
- [[`0082f62d9c`](https://github.com/nodejs/node/commit/0082f62d9c)] - **(SEMVER-MINOR)** **http**: make --insecure-http-parser configurable per-stream or per-server (Anna Henningsen) [#31448](https://github.com/nodejs/node/pull/31448)
- [[`a9849c0ff6`](https://github.com/nodejs/node/commit/a9849c0ff6)] - **(SEMVER-MINOR)** **http**: opt-in insecure HTTP header parsing (Sam Roberts) [#30567](https://github.com/nodejs/node/pull/30567)
- [[`2eee90e959`](https://github.com/nodejs/node/commit/2eee90e959)] - **http**: strip trailing OWS from header values (Sam Roberts) [nodejs-private/node-private#191](https://github.com/nodejs-private/node-private/pull/191)
- [[`e2c8f89b75`](https://github.com/nodejs/node/commit/e2c8f89b75)] - **test**: using TE to smuggle reqs is not possible (Sam Roberts) [nodejs-private/node-private#192](https://github.com/nodejs-private/node-private/pull/192)
- [[`d616722f65`](https://github.com/nodejs/node/commit/d616722f65)] - **test**: check that --insecure-http-parser works (Sam Roberts) [#31253](https://github.com/nodejs/node/pull/31253)

Windows 32-bit Installer: https://nodejs.org/dist/v10.19.0/node-v10.19.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v10.19.0/node-v10.19.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v10.19.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v10.19.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v10.19.0/node-v10.19.0.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v10.19.0/node-v10.19.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v10.19.0/node-v10.19.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v10.19.0/node-v10.19.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v10.19.0/node-v10.19.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v10.19.0/node-v10.19.0-aix-ppc64.tar.gz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v10.19.0/node-v10.19.0-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v10.19.0/node-v10.19.0-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v10.19.0/node-v10.19.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v10.19.0/node-v10.19.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v10.19.0/node-v10.19.0.tar.gz \
Other release files: https://nodejs.org/dist/v10.19.0/ \
Documentation: https://nodejs.org/docs/v10.19.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

9040615d614cf4039f4abbd62c799877c3c2efd517e4100d6f13064d368a25a0  node-v10.19.0-aix-ppc64.tar.gz
b16328570651be44213a2303c1f9515fc506e0a96a273806f71ed000e3ca3cb3  node-v10.19.0-darwin-x64.tar.gz
91725d2ed64e4ccd265259e3e29a0e64a4d26d9d1cd9ba390e0cdec13ea7b02f  node-v10.19.0-darwin-x64.tar.xz
e664f44dae563abdf9fa1eda0ce404dcc2109eb4d3cb3d5305516dca29f4c3b5  node-v10.19.0-headers.tar.gz
82a1796cc87ce66db92cdaa0e54f67c1e0c130ec4549a9591b9ff0edff618d10  node-v10.19.0-headers.tar.xz
3510172797b63bb6a7247f62a241bdfcf51fef8b1134eb7d3a27973e2008e482  node-v10.19.0-linux-arm64.tar.gz
77bdbf859fc38e6e860efd479b0a7b7b6bd3e7cb05337e5cc5638251eb5d3a59  node-v10.19.0-linux-arm64.tar.xz
96fa937b8d9a8a4e3c606b33e2d71a971f2069dc3fe6a9a038e7fa74f9444568  node-v10.19.0-linux-armv6l.tar.gz
6f650dc7610d7fee1cb6b5bd7339e94858d8d10ab324e17afc4d551008b36f0a  node-v10.19.0-linux-armv6l.tar.xz
838a92c63c0bf7d5bb63fbd62b5902e1281ea4bcccbd2de65a8d57edd9b003a1  node-v10.19.0-linux-armv7l.tar.gz
7eeddc7815885f665ecbfe2cf8ae2e71fab601eefece229673126ef8da2965f5  node-v10.19.0-linux-armv7l.tar.xz
65f9cf15490b33b45dff08e984a0786cf82dba7e7e9bbd74a2cffb63506061d5  node-v10.19.0-linux-ppc64le.tar.gz
6a0701f1b03321fb5789c0d6d6ccd5b11579001ad56635354b89fc423b080de0  node-v10.19.0-linux-ppc64le.tar.xz
273e264ee6338a7a520dd739620cb3b5388c86f522a77a1bfff011c55a3a2984  node-v10.19.0-linux-s390x.tar.gz
014c3fac92b0e3546a4d3de3b05bb00f3d6839f529455419cccc554f4c40409e  node-v10.19.0-linux-s390x.tar.xz
36d90bc58f0418f31dceda5b18eb260019fcc91e59b0820ffa66700772a8804b  node-v10.19.0-linux-x64.tar.gz
34127c7c6b1ba02d6d4dc3a926f38a5fb88bb37fc7f051349005ce331c7a53c6  node-v10.19.0-linux-x64.tar.xz
60eeec991f02e5564d4047387117c6c1884aa8d247c538dc93c51e134eec467f  node-v10.19.0.pkg
eb883a9c32b1352e42dafc503797a088fa881896a933785aff1b2e49643bde1f  node-v10.19.0-sunos-x64.tar.gz
015f31e0b2adb742021bd61c0566b5bdbf95e0275200d609d9f64944779a4ae7  node-v10.19.0-sunos-x64.tar.xz
db85b9992f1ec66629731d82f690987883dd2989abb4cc136eb65dd720b1bda8  node-v10.19.0.tar.gz
622721bc3e6b65faf7eb6a22bfb6e3e31817e42212aa6bf5a7991ea7d9b6f169  node-v10.19.0.tar.xz
46bdca8ce90ac091590c3473ed9ac9d7e0ae2010696ffb93474c272f4db218ba  node-v10.19.0-win-x64.7z
210efd45a7f79cf4c350d8f575f990becdd3833cd922796a4c83b27996f5679e  node-v10.19.0-win-x64.zip
8e90b780567178244c0716af43604f3ac4475e8cf21246f9c63386acddf7f841  node-v10.19.0-win-x86.7z
afd176d4f022b6a5dbd4a908d42c6d85d4f739c040f65430ab3bf60b8f3b9a96  node-v10.19.0-win-x86.zip
6151538702d4bd106b66d28ce606f9faa2a8fc8baa50762bea0baec564b5e79e  node-v10.19.0-x64.msi
eae8dc6511bd467729fef043167a18ca0843c9d1bab17c31a20b197b44d06251  node-v10.19.0-x86.msi
00047df9589b6a860886d653ed9f817852615211a53b9a2563ff3c56a5090fbf  win-x64/node.exe
bfc277d24ebc27c87642b9f8fd2b4a312feaadf57a4a27e81734bcb49752163d  win-x64/node.lib
3782acd379b59bca0009debdaa1bea3b28772518d7134c56279485c0a076d207  win-x64/node_pdb.7z
6de35811191c919c09cc11192a151ac0ecd76e3f5ec7f5f3f44d2a7d5feb177e  win-x64/node_pdb.zip
5480ed6bb4c83a4284737ffeb4bfe27b5d342f182f2a52939bf0eee8d9c4fac4  win-x86/node.exe
3484dacb6a6aa89ec532eb9150e0a45a6c55453814cc2f32660504f120c7a42a  win-x86/node.lib
a1953bc23236083e508c9e42672c457c3a21dd5a97addad7729481a965696c69  win-x86/node_pdb.7z
12c7d6b8a6c138dd36063c939867ef98713b16ad89d277f319ffdc9ec3932f6d  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQEzBAEBCAAdFiEETtd49TnjY0x3nIfG1wYoSKGrAFwFAl47g3gACgkQ1wYoSKGr
AFye0Qf+N0FiuaLnHvArA4TJoYOCj9fJouD6F0C/dkXEMRQ00bm3ui8lPNOMtXUg
7wvorCig8OcWrLHiGsW6d3U8Z+2KxY63hg7Zyiu1JNKHyohvri/gTtRETuO5IANR
sUuxD7BGJzXaafXinsfkz6sgN75E9PtogBPNMdbFuuwt53CYsVoh1o8kxhHAHn3L
mG9wIQj2dPPaEV4zxGut4kkvjMlsC5VAxNkFAbwPd7EzIFlm0DIuYMlN8RSvTfOW
s2hhFCoBB9nM8QtOo72MBMe8wnHGvIhUJpTDA9Cdh13MLZtM3KMAEg9z8gZiVCl4
+qmYTB5KB8H20Bw+lvVjWnxYgbACSQ==
=RWpz
-----END PGP SIGNATURE-----

```
