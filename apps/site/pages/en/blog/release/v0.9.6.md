---
date: '2013-01-11T19:23:56.000Z'
category: release
title: Node v0.9.6 (Unstable)
layout: blog-post
author: The Node.js Project
---

2013.01.11, Version 0.9.6 (Unstable)

- V8: update to *********

- node: remove ev-emul.h (<PERSON>)

- path: make basename and extname ignore trailing slashes (<PERSON>)

- typed arrays: fix sunos signed/unsigned char issue (<PERSON>)

- child_process: Fix {stdio:'inherit'} regression (<PERSON>)

- child_process: Fix pipe() from child stdio streams (<PERSON><PERSON><PERSON>)

- child_process: make fork() execPath configurable (<PERSON>)

- stream: Add readable.push(chunk) method (isaacs)

- dtrace: x64 ustack helper (Fedor Indutny)

- repl: fix floating point number parsing (Nirk Niggler)

- repl: allow overriding builtins (<PERSON>)

- net: add localAddress and localPort to Socket (<PERSON>)

- fs: make pool size coincide with ReadStream bufferSize (<PERSON><PERSON><PERSON>)

- typed arrays: implement load and store swizzling (<PERSON>)

- windows: fix perfctr crash on XP and 2003 (<PERSON>)

- dgram: fix double implicit bind error (<PERSON> Noordhuis)

Source Code: https://nodejs.org/dist/v0.9.6/node-v0.9.6.tar.gz

Macintosh Installer (Universal): https://nodejs.org/dist/v0.9.6/node-v0.9.6.pkg

Windows Installer: https://nodejs.org/dist/v0.9.6/node-v0.9.6-x86.msi

Windows x64 Installer: https://nodejs.org/dist/v0.9.6/x64/node-v0.9.6-x64.msi

Windows x64 Files: https://nodejs.org/dist/v0.9.6/x64/

Linux 32-bit Binary: https://nodejs.org/dist/v0.9.6/node-v0.9.6-linux-x86.tar.gz

Linux 64-bit Binary: https://nodejs.org/dist/v0.9.6/node-v0.9.6-linux-x64.tar.gz

Solaris 32-bit Binary: https://nodejs.org/dist/v0.9.6/node-v0.9.6-sunos-x86.tar.gz

Solaris 64-bit Binary: https://nodejs.org/dist/v0.9.6/node-v0.9.6-sunos-x64.tar.gz

Other release files: https://nodejs.org/dist/v0.9.6/

Website: https://nodejs.org/docs/v0.9.6/

Documentation: https://nodejs.org/docs/v0.9.6/api/

Shasums:

```
31ef1e0d875232c85ecde0bc84ddf19cf6cd63f7  node-v0.9.6-darwin-x64.tar.gz
62d5f6f2b39e87eb1f86da0423b6ec45180cd986  node-v0.9.6-darwin-x86.tar.gz
e3cb0989c8262349659424ea2f534c8afe12f93c  node-v0.9.6-linux-x64.tar.gz
ff4b68f274a52038fbd04a4ef143e1076ae71db9  node-v0.9.6-linux-x86.tar.gz
59ebb5e5c02c9e40a7a3cc2d8f554f2d722048eb  node-v0.9.6-sunos-x64.tar.gz
714b88c5f34bb0adad2d73b6d76fa423d14c29b8  node-v0.9.6-sunos-x86.tar.gz
3ecc617efcae5274c3a1e3452e8fb0646a3e6afc  node-v0.9.6-x86.msi
d865d044cc1e9379998aa6c3d34dbe2824e41b5e  node-v0.9.6.pkg
650d7c50e29ce7ab428de0617ad315063a53ea1a  node-v0.9.6.tar.gz
94b10c6945ec78af6d9b90fc083f97adfa44473f  node.exe
0b0eceee083ca9cfdc8c24fbdfe92f7ee25938c2  node.exp
9af3ceee0be869e866b2f516dc2b17690e442739  node.lib
7dfbdf0ac378e67cfa5aff550f057c31f8f92d38  node.pdb
02f27f503a16850a66f7e6fe8732bfa5fcbe311f  x64/node-v0.9.6-x64.msi
3f5990602e7268413602dd54a4d019b3330630ae  x64/node.exe
836a7be49bd7ed5f1794775d0c20e75f327b067d  x64/node.exp
e5e3abed1da31d94525f8731e2d67f0e19b20304  x64/node.lib
b61c1a7a5786a38864943bb3d4c78859d0430d7a  x64/node.pdb
```
