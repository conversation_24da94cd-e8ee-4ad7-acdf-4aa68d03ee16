---
date: '2023-02-16T22:08:13.523Z'
category: release
title: Node v14.21.3 (LTS)
layout: blog-post
author: <PERSON>
---

### Notable Changes

The following CVEs are fixed in this release:

- **[CVE-2023-23918](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2023-23918)**: Node.js Permissions policies can be bypassed via process.mainModule (High)
- **[CVE-2023-23920](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2023-23920)**: Node.js insecure loading of ICU data through ICU_DATA environment variable (Low)

More detailed information on each of the vulnerabilities can be found in [February 2023 Security Releases](/blog/vulnerability/february-2023-security-releases/) blog post.

This security release includes OpenSSL security updates as outlined in the recent
[OpenSSL security advisory](https://www.openssl.org/news/secadv/20230207.txt).

This security release also includes an npm update for Node.js 14 to address a number
of CVEs which either do not affect Node.js or are low severity in the context of Node.js. You
can get more details for the individual CVEs in
[nodejs-dependency-vuln-assessments](https://github.com/nodejs/nodejs-dependency-vuln-assessments).

### Commits

- \[[`97a0443f13`](https://github.com/nodejs/node/commit/97a0443f13)] - **build**: build ICU with ICU_NO_USER_DATA_OVERRIDE (RafaelGSS) [nodejs-private/node-private#374](https://github.com/nodejs-private/node-private/pull/374)
- \[[`9e6221529b`](https://github.com/nodejs/node/commit/9e6221529b)] - **deps**: cherry-pick Windows ARM64 fix for openssl (Richard Lau) [#46566](https://github.com/nodejs/node/pull/46566)
- \[[`0d5f86451d`](https://github.com/nodejs/node/commit/0d5f86451d)] - **deps**: update archs files for OpenSSL-1.1.1t (RafaelGSS) [#46566](https://github.com/nodejs/node/pull/46566)
- \[[`8c11d17b40`](https://github.com/nodejs/node/commit/8c11d17b40)] - **deps**: upgrade openssl sources to 1.1.1t (RafaelGSS) [#46566](https://github.com/nodejs/node/pull/46566)
- \[[`224e93c9ef`](https://github.com/nodejs/node/commit/224e93c9ef)] - **deps**: upgrade npm to 6.14.18 (Ruy Adorno) [#45936](https://github.com/nodejs/node/pull/45936)
- \[[`d73ea4de13`](https://github.com/nodejs/node/commit/d73ea4de13)] - **doc**: clarify release notes for Node.js 14.21.2 (Richard Lau) [#45846](https://github.com/nodejs/node/pull/45846)
- \[[`f7892c16be`](https://github.com/nodejs/node/commit/f7892c16be)] - **lib**: makeRequireFunction patch when experimental policy (RafaelGSS) [nodejs-private/node-private#358](https://github.com/nodejs-private/node-private/pull/358)
- \[[`fa115ee8ac`](https://github.com/nodejs/node/commit/fa115ee8ac)] - **module**: protect against prototype mutation (Antoine du Hamel) [#44007](https://github.com/nodejs/node/pull/44007)
- \[[`83975b7fb4`](https://github.com/nodejs/node/commit/83975b7fb4)] - **policy**: makeRequireFunction on mainModule.require (RafaelGSS) [nodejs-private/node-private#358](https://github.com/nodejs-private/node-private/pull/358)
- \[[`a5f8798d7a`](https://github.com/nodejs/node/commit/a5f8798d7a)] - **test**: avoid left behind child processes (Richard Lau) [#46276](https://github.com/nodejs/node/pull/46276)

Windows 32-bit Installer: https://nodejs.org/dist/v14.21.3/node-v14.21.3-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v14.21.3/node-v14.21.3-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v14.21.3/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v14.21.3/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v14.21.3/node-v14.21.3.pkg \
macOS Intel 64-bit Binary: https://nodejs.org/dist/v14.21.3/node-v14.21.3-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v14.21.3/node-v14.21.3-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v14.21.3/node-v14.21.3-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v14.21.3/node-v14.21.3-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v14.21.3/node-v14.21.3-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v14.21.3/node-v14.21.3-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v14.21.3/node-v14.21.3-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v14.21.3/node-v14.21.3.tar.gz \
Other release files: https://nodejs.org/dist/v14.21.3/ \
Documentation: https://nodejs.org/docs/v14.21.3/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

41b1e54022e2ed4340ae76ef3cdbb06c6693faaadd869b21c5a04664a34aa12e  node-v14.21.3-aix-ppc64.tar.gz
a024f0dd5a4c1f951b79959c3e991b30a5919a734ab3e197ae0ef439e5a538b5  node-v14.21.3-darwin-x64.tar.gz
2bc2ecc27a926114827fa26b47ac9bfbd805393a867bedd157b6b052daa20f6e  node-v14.21.3-darwin-x64.tar.xz
b9ee97f61ba64e2056baa6a7faec4d56c96e5020b56e5f05f5ee801f54f3b1f3  node-v14.21.3-headers.tar.gz
b5393b510a579e9bc48bce01dbc637f578a3bbc5cb50c757d8fc2873e06ac7ae  node-v14.21.3-headers.tar.xz
044b7ec3fea04cd3815d26901ee37203dcc942688b72ee6eac96f6a1ca3cc63f  node-v14.21.3-linux-arm64.tar.gz
f06642bfcf0b8cc50231624629bec58b183954641b638e38ed6f94cd39e8a6ef  node-v14.21.3-linux-arm64.tar.xz
260539da086a3f293e1b205c9075442224d6587bee7935d7880948277c55080f  node-v14.21.3-linux-armv7l.tar.gz
058941ad1b1250973f6dd49b2fcfdfd05a5b61de0f690cbd301b7a7fcc040f97  node-v14.21.3-linux-armv7l.tar.xz
20f0d9dafda50d5bcb94ea16fa388251898cad70231b50e17840facb9add3ac1  node-v14.21.3-linux-ppc64le.tar.gz
821a207405afe4c224da5aea6c4417b0b9bb6149cd49fe1fcde0fd7027bc113f  node-v14.21.3-linux-ppc64le.tar.xz
bb82e379ccb1db3704216136e5db216f8f232069bb5615af401d6251a5b826be  node-v14.21.3-linux-s390x.tar.gz
a241aa16cbf5485ebc54955bec42a81334278808174fcfd4199bcc3256f77eb0  node-v14.21.3-linux-s390x.tar.xz
bef2685d9469058c1229cc7789e171861044fe3f70316ec744e9bf3609cd45ed  node-v14.21.3-linux-x64.tar.gz
05c08a107c50572ab39ce9e8663a2a2d696b5d262d5bd6f98d84b997ce932d9a  node-v14.21.3-linux-x64.tar.xz
125be8d347f9d14f6f12ca3507c7a19b0939cbcbb3788463ae5a94b50520113f  node-v14.21.3.pkg
97eb4c0ea1ffb73eb0486db5125e5351f744e65df6b3d10fbc0611dec7fd27cb  node-v14.21.3.tar.gz
458ec092e60ad700ddcf079cb63d435c15da4c7bb3d3f99b9a8e58a99e54075e  node-v14.21.3.tar.xz
a00c71e662cb1752f180e54921ca2c79bdefc7200a51b8805888ac3723889579  node-v14.21.3-win-x64.7z
47cfb919bb86ab681369636a9cb925e2bd61991aad1638b2e38e61ec956796a6  node-v14.21.3-win-x64.zip
7a1f86386bb5f39c93bfd910a10966c69331c3f775457c0ea0c044ee6fbf000f  node-v14.21.3-win-x86.7z
d416decafadbc50dc9fa357f5b97b279b15a712ff432422e3abd8b65d26c07b2  node-v14.21.3-win-x86.zip
4fbb9ff48fe733cef0b65f2c0948ba34b5d4b65ebfa1aa2dcd9c913c7b2bf95e  node-v14.21.3-x64.msi
d07fb9f6c41d2b2568d42a76d3556706a626125981d12421fef6d4682cb7ae59  node-v14.21.3-x86.msi
af7e8acdcb41bf961cbfc062c8e52b4775703aa6403d34e0f4fe8359d34e78c5  win-x64/node.exe
5226634dfd3f9a99dd40ca374fe654b6ed4fdb1890c629a3426e34e212fb011c  win-x64/node.lib
4b9e6940a4d769c50391c573277b44f279644a19d22464de8fe8a09051d01149  win-x64/node_pdb.7z
607670ec389b44c0b5f5e231e3e40d3e39876740d3f86c2b61acea125bbb6dfe  win-x64/node_pdb.zip
32ee127315a72ac19017811632a5cae8359f8474eddf78de79462cf0321d5a33  win-x86/node.exe
e1e44fce63711f68656b246488b63e2ebbfd3e3ea8253d9a8fa264d526c80508  win-x86/node.lib
c6a5dd0c138cc41d69d1c40a5687c9c3c97ca17128b7a3e3f57e27841d8a512e  win-x86/node_pdb.7z
4c275b9ab908556499b649a9dde14e9061c727e0c264241b744dac86e0461f1d  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEyC+jrhy+3Gvka5NgxDzsRcF6uTwFAmPup+gACgkQxDzsRcF6
uTwvHw/+OfFLpAVfoHjOAwwp+ziQ2uO4K3UI0mCHw96c0y2bSJyX960RuUVLOT2C
srTIEhfaBnyxJtc0ldyFuscKWgOUg0C0HPb8scLh2E5bo5UxEL/VFQCe6wXX5kCY
RZjfOFyRHnKytTj/LvZB4WHOgHjduYJNLZlpvSzjVqRR2wmsKtAFgMP//mW0VBdU
lQ7GG1tj7VbR3aiB6c/VqFGannGVf3h1dZ1lYHINfHZJXUKBwOaGjEqjcy2ddqMz
39froNI82m94FX1QwYVVdyDIMHPsbZgKZm6YK173rUyroM7/52T0oTc63KPRRkMB
tDYkuux17vNsdaFiWr8E8nEkcuqASHYBWv8JwJqeYbtsnLQEY7vVnFfe6J7rI6uw
PWtukqXq8c59mZlD4bMFZCcwBmPpew2r+dpuo+6Az4JRRVVuCJosKO4RirVE45q3
4dSBnRep5IODyFUZM/Jq+ALf32UYfbL81JObIl6fBCQL+2kJ6Y3XeafKQYfVBNFJ
PUxVyCkk8uv09Z1XGmE+0dLiDrZmlCjqFkoWFO4zyTM1VEU1K9j2WeEU3Qe+A1+i
Dwz7Lka4L7mvwSRt/+EiNrLnl0thrmdZYnIn5uP+ttpln1uuWzGqPhUhU4yO3Qu3
ENXrECAECeMBkLCC1yEFPizMmPwy8GagDk5O7L6Dmh7oM1SM6lQ=
=g7My
-----END PGP SIGNATURE-----

```
