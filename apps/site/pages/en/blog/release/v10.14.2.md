---
date: '2018-12-11T20:40:12.978Z'
category: release
title: Node v10.14.2 (LTS)
layout: blog-post
author: <PERSON><PERSON>
---

### Notable Changes

- **deps**:
  - upgrade to c-ares v1.15.0 (<PERSON>) [#23854](https://github.com/nodejs/node/pull/23854)
- **Windows**:
  - A crashing process will now show the names of stack frames if the node.pdb file is available. (<PERSON><PERSON><PERSON>) [#23822](https://github.com/nodejs/node/pull/23822)
- **Added new collaborators**:
  - [psmarshall](https://github.com/psmarshall) - <PERSON>. https://github.com/nodejs/node/pull/24170
  - [shisama](https://github.com/shisama) - <PERSON><PERSON><PERSON>. https://github.com/nodejs/node/pull/24136

### Commits

- [[`dedf0e5cde`](https://github.com/nodejs/node/commit/dedf0e5cde)] - **assert**: remove unused catch bindings (cjihrig) [#24079](https://github.com/nodejs/node/pull/24079)
- [[`6a21d3e72f`](https://github.com/nodejs/node/commit/6a21d3e72f)] - **async_hooks**: add missing async_hooks destroys in AsyncReset (Bastian Krol) [#23272](https://github.com/nodejs/node/pull/23272)
- [[`eed47b022d`](https://github.com/nodejs/node/commit/eed47b022d)] - **benchmark**: remove unused catch bindings (cjihrig) [#24079](https://github.com/nodejs/node/pull/24079)
- [[`c75d4bb106`](https://github.com/nodejs/node/commit/c75d4bb106)] - **benchmark**: fix bench-mkdirp to use recursive option (Klaus Meinhardt) [#23699](https://github.com/nodejs/node/pull/23699)
- [[`60fe7f1f29`](https://github.com/nodejs/node/commit/60fe7f1f29)] - **benchmark**: coerce PORT to number (Ali Ijaz Sheikh) [#23721](https://github.com/nodejs/node/pull/23721)
- [[`192c2af64e`](https://github.com/nodejs/node/commit/192c2af64e)] - **benchmark**: added a test benchmark for worker (Muzafar Umarov) [#23475](https://github.com/nodejs/node/pull/23475)
- [[`0600e753b0`](https://github.com/nodejs/node/commit/0600e753b0)] - **bootstrap**: remove unused catch bindings (cjihrig) [#24079](https://github.com/nodejs/node/pull/24079)
- [[`e872089158`](https://github.com/nodejs/node/commit/e872089158)] - **bootstrapper**: move internalBinding to NativeModule (Gus Caplan) [#23025](https://github.com/nodejs/node/pull/23025)
- [[`95814f2b08`](https://github.com/nodejs/node/commit/95814f2b08)] - **build**: change repo to https protocol in Makefile (mritunjaygoutam12) [#24073](https://github.com/nodejs/node/pull/24073)
- [[`5ed39d987b`](https://github.com/nodejs/node/commit/5ed39d987b)] - **build**: use latest node on travis (cjihrig) [#24198](https://github.com/nodejs/node/pull/24198)
- [[`19bda8a225`](https://github.com/nodejs/node/commit/19bda8a225)] - **build**: fix Travis non-PR builds (Richard Lau) [#24093](https://github.com/nodejs/node/pull/24093)
- [[`3bac885b74`](https://github.com/nodejs/node/commit/3bac885b74)] - **build**: do not lint on non-PR Travis builds (Anna Henningsen) [#24076](https://github.com/nodejs/node/pull/24076)
- [[`aa8848321b`](https://github.com/nodejs/node/commit/aa8848321b)] - **build**: only check REPLACEME & DEP...X for releases (Rod Vagg) [#24575](https://github.com/nodejs/node/pull/24575)
- [[`d809e7485f`](https://github.com/nodejs/node/commit/d809e7485f)] - **build**: make benchmark/napi all prereq order-only (Daniel Bevenius) [#23951](https://github.com/nodejs/node/pull/23951)
- [[`2e83f1eb20`](https://github.com/nodejs/node/commit/2e83f1eb20)] - **build**: add -Werror=undefined-inline to clang builds (Refael Ackermann) [#23961](https://github.com/nodejs/node/pull/23961)
- [[`bf61fe5dd7`](https://github.com/nodejs/node/commit/bf61fe5dd7)] - **build**: add lint-py which uses flake8 (cclauss) [#21952](https://github.com/nodejs/node/pull/21952)
- [[`7a39fec1db`](https://github.com/nodejs/node/commit/7a39fec1db)] - **build**: allow for overwriting of use_openssl_def (Shelley Vohr) [#23763](https://github.com/nodejs/node/pull/23763)
- [[`dc38427c64`](https://github.com/nodejs/node/commit/dc38427c64)] - **build**: fix coverage generation (Michael Dawson) [#23769](https://github.com/nodejs/node/pull/23769)
- [[`e737c71936`](https://github.com/nodejs/node/commit/e737c71936)] - **build**: fix `./configure --enable-d8` (Ben Noordhuis) [#23656](https://github.com/nodejs/node/pull/23656)
- [[`d886095377`](https://github.com/nodejs/node/commit/d886095377)] - **build**: add .DS_store to .gitgnore (Marcos Frony) [#23554](https://github.com/nodejs/node/pull/23554)
- [[`83b8c1fb90`](https://github.com/nodejs/node/commit/83b8c1fb90)] - **build,meta**: don't fail Travis for commit message (Refael Ackermann) [#23739](https://github.com/nodejs/node/pull/23739)
- [[`156fa12674`](https://github.com/nodejs/node/commit/156fa12674)] - **build,meta**: switch to gcc-4.9 on travis (Refael Ackermann) [#23778](https://github.com/nodejs/node/pull/23778)
- [[`df5a19f468`](https://github.com/nodejs/node/commit/df5a19f468)] - **child_process**: handle undefined/null for fork() args (Shobhit Chittora) [#22416](https://github.com/nodejs/node/pull/22416)
- [[`a97e79ce12`](https://github.com/nodejs/node/commit/a97e79ce12)] - **crypto**: add SET_INTEGER_CONSANT macro (Daniel Bevenius) [#23687](https://github.com/nodejs/node/pull/23687)
- [[`90ac77440b`](https://github.com/nodejs/node/commit/90ac77440b)] - **crypto**: strip unwanted space from openssl version (Sam Roberts) [#23678](https://github.com/nodejs/node/pull/23678)
- [[`806242bf40`](https://github.com/nodejs/node/commit/806242bf40)] - **crypto**: remove DiffieHellman.initialised\_ (Tobias Nießen) [#23717](https://github.com/nodejs/node/pull/23717)
- [[`4b98ff299d`](https://github.com/nodejs/node/commit/4b98ff299d)] - **crypto**: simplify internal state handling (Tobias Nießen) [#23648](https://github.com/nodejs/node/pull/23648)
- [[`d4b5f9f591`](https://github.com/nodejs/node/commit/d4b5f9f591)] - **crypto**: simplify error handling in ECDH::New (Tobias Nießen) [#23647](https://github.com/nodejs/node/pull/23647)
- [[`0e0c7b8bf2`](https://github.com/nodejs/node/commit/0e0c7b8bf2)] - **crypto**: move field initialization to class (Diana Holland) [#23610](https://github.com/nodejs/node/pull/23610)
- [[`a81696accd`](https://github.com/nodejs/node/commit/a81696accd)] - **crypto**: fix length argument to snprintf() (Ben Noordhuis) [#23622](https://github.com/nodejs/node/pull/23622)
- [[`c8b018f930`](https://github.com/nodejs/node/commit/c8b018f930)] - **deps**: c-ares float, version number patch (Ben Noordhuis) [#23854](https://github.com/nodejs/node/pull/23854)
- [[`52afdae394`](https://github.com/nodejs/node/commit/52afdae394)] - **deps**: upgrade to c-ares v1.15.0 (Ben Noordhuis) [#23854](https://github.com/nodejs/node/pull/23854)
- [[`2e4ef5b5c0`](https://github.com/nodejs/node/commit/2e4ef5b5c0)] - **deps**: remove old c-ares configure files (Ben Noordhuis) [#23854](https://github.com/nodejs/node/pull/23854)
- [[`983e3a1b7c`](https://github.com/nodejs/node/commit/983e3a1b7c)] - **deps**: cherry-pick 2987946 from upstream V8 (Refael Ackermann) [#24555](https://github.com/nodejs/node/pull/24555)
- [[`1bed1107c7`](https://github.com/nodejs/node/commit/1bed1107c7)] - **deps**: icu: apply workaround patch (Steven R. Loomis) [#23764](https://github.com/nodejs/node/pull/23764)
- [[`e231bf5a3f`](https://github.com/nodejs/node/commit/e231bf5a3f)] - **deps**: V8: Add virtual dtor to avoid aix gcc error (Vasili Skurydzin) [#23695](https://github.com/nodejs/node/pull/23695)
- [[`1bea8db1ef`](https://github.com/nodejs/node/commit/1bea8db1ef)] - **deps**: cherry-pick 67b5499 from V8 upstream (Vasili Skurydzin) [#23695](https://github.com/nodejs/node/pull/23695)
- [[`4233bd977c`](https://github.com/nodejs/node/commit/4233bd977c)] - **deps**: cherry-pick d2e0166 from V8 upstream (Vasili Skurydzin) [#23695](https://github.com/nodejs/node/pull/23695)
- [[`bb55cc178c`](https://github.com/nodejs/node/commit/bb55cc178c)] - **deps**: cherry-pick a51f429 from V8 upstream (Vasili Skurydzin) [#23695](https://github.com/nodejs/node/pull/23695)
- [[`e9901dde82`](https://github.com/nodejs/node/commit/e9901dde82)] - **deps**: cherry-pick abab9fb from V8 upstream (Vasili Skurydzin) [#23695](https://github.com/nodejs/node/pull/23695)
- [[`e5f795a8af`](https://github.com/nodejs/node/commit/e5f795a8af)] - **deps**: cherry-pick d9e7832 from V8 upstream (Vasili Skurydzin) [#23695](https://github.com/nodejs/node/pull/23695)
- [[`c8304f6567`](https://github.com/nodejs/node/commit/c8304f6567)] - **deps**: backport 525b396195 from upstream V8 (Peter Marshall) [#23827](https://github.com/nodejs/node/pull/23827)
- [[`ff57f0fb7e`](https://github.com/nodejs/node/commit/ff57f0fb7e)] - **deps**: partially revert 'increase V8 deprecation levels' (Peter Marshall) [#24195](https://github.com/nodejs/node/pull/24195)
- [[`06782fe5a3`](https://github.com/nodejs/node/commit/06782fe5a3)] - **deps**: add missing ares_android.h file (cjihrig) [#23682](https://github.com/nodejs/node/pull/23682)
- [[`f928d99da9`](https://github.com/nodejs/node/commit/f928d99da9)] - **dns**: fix inconsistent (hostname vs host) (Ulises Gascón) [#23572](https://github.com/nodejs/node/pull/23572)
- [[`5ed5f034c7`](https://github.com/nodejs/node/commit/5ed5f034c7)] - **doc**: fix linting errors (cjihrig) [#24229](https://github.com/nodejs/node/pull/24229)
- [[`e0f99a2250`](https://github.com/nodejs/node/commit/e0f99a2250)] - **doc**: wrap GOVERNANCE.md at 80 characters (Rich Trott) [#24094](https://github.com/nodejs/node/pull/24094)
- [[`5fae45075c`](https://github.com/nodejs/node/commit/5fae45075c)] - **doc**: add text about error.code stability (Rich Trott) [#24090](https://github.com/nodejs/node/pull/24090)
- [[`2659a43905`](https://github.com/nodejs/node/commit/2659a43905)] - **doc**: update System Errors documentation (Rich Trott) [#24090](https://github.com/nodejs/node/pull/24090)
- [[`bcfb824154`](https://github.com/nodejs/node/commit/bcfb824154)] - **doc**: add psmarshall to collaborators (Peter Marshall) [#24170](https://github.com/nodejs/node/pull/24170)
- [[`ec43b3287e`](https://github.com/nodejs/node/commit/ec43b3287e)] - **doc**: add shisama to collaborators (Masashi Hirano) [#24136](https://github.com/nodejs/node/pull/24136)
- [[`0b6c1bb346`](https://github.com/nodejs/node/commit/0b6c1bb346)] - **doc**: implement minor text fixes to path.md (Rich Trott) [#24118](https://github.com/nodejs/node/pull/24118)
- [[`7f1e0e55f8`](https://github.com/nodejs/node/commit/7f1e0e55f8)] - **doc**: inspector security warning for changing host (Сковорода Никита Андреевич) [#23640](https://github.com/nodejs/node/pull/23640)
- [[`1609ddaa74`](https://github.com/nodejs/node/commit/1609ddaa74)] - **doc**: fix minor text issues in stream.md (Rich Trott) [#24116](https://github.com/nodejs/node/pull/24116)
- [[`91b81092c1`](https://github.com/nodejs/node/commit/91b81092c1)] - **doc**: streamline CONTRIBUTING.md (Rich Trott) [#24010](https://github.com/nodejs/node/pull/24010)
- [[`f081a8cccd`](https://github.com/nodejs/node/commit/f081a8cccd)] - **doc**: add table of contents to release guide (Michaël Zasso) [#24042](https://github.com/nodejs/node/pull/24042)
- [[`9fd209169a`](https://github.com/nodejs/node/commit/9fd209169a)] - **doc**: add missing comma in net documentation (Rich Trott) [#24074](https://github.com/nodejs/node/pull/24074)
- [[`f5f916f344`](https://github.com/nodejs/node/commit/f5f916f344)] - **doc**: correct link to test coverage command (mritunjaygoutam12) [#24049](https://github.com/nodejs/node/pull/24049)
- [[`d21a8b66d3`](https://github.com/nodejs/node/commit/d21a8b66d3)] - **doc**: fix socket.connecting description (Anna Henningsen) [#24066](https://github.com/nodejs/node/pull/24066)
- [[`6dc0a1c87c`](https://github.com/nodejs/node/commit/6dc0a1c87c)] - **doc**: add SECURITY.md to readme.md (warnerp18) [#24031](https://github.com/nodejs/node/pull/24031)
- [[`0079dd793c`](https://github.com/nodejs/node/commit/0079dd793c)] - **doc**: edit man page for superfluous "node" usage (Rich Trott) [#24029](https://github.com/nodejs/node/pull/24029)
- [[`a7523072b8`](https://github.com/nodejs/node/commit/a7523072b8)] - **doc**: fix dublication in net.createServer() docs (Ivan Filenko) [#24026](https://github.com/nodejs/node/pull/24026)
- [[`13ca306d4a`](https://github.com/nodejs/node/commit/13ca306d4a)] - **doc**: address bits of proof reading work (Jagannath Bhat) [#23978](https://github.com/nodejs/node/pull/23978)
- [[`93e2035e58`](https://github.com/nodejs/node/commit/93e2035e58)] - **doc**: revise COLLABORATOR_GUIDE.md (Rich Trott) [#23990](https://github.com/nodejs/node/pull/23990)
- [[`985c62d9e3`](https://github.com/nodejs/node/commit/985c62d9e3)] - **doc**: simplify CODE_OF_CONDUCT.md (Rich Trott) [#23989](https://github.com/nodejs/node/pull/23989)
- [[`f8ebd0efe2`](https://github.com/nodejs/node/commit/f8ebd0efe2)] - **doc**: revise CHANGELOG.md text (Rich Trott) [#23988](https://github.com/nodejs/node/pull/23988)
- [[`52215a4c35`](https://github.com/nodejs/node/commit/52215a4c35)] - **doc**: improve COLLABORATOR_GUIDE (Jagannath Bhat) [#23977](https://github.com/nodejs/node/pull/23977)
- [[`4871b116f8`](https://github.com/nodejs/node/commit/4871b116f8)] - **doc**: improve BUILDING.md (Jagannath Bhat) [#23976](https://github.com/nodejs/node/pull/23976)
- [[`8ec4856941`](https://github.com/nodejs/node/commit/8ec4856941)] - **doc**: add types and their corresponding return values (Ouyang Yadong) [#23998](https://github.com/nodejs/node/pull/23998)
- [[`10e66814ff`](https://github.com/nodejs/node/commit/10e66814ff)] - **doc**: add branding to style guide (Rich Trott) [#23967](https://github.com/nodejs/node/pull/23967)
- [[`3de7858f13`](https://github.com/nodejs/node/commit/3de7858f13)] - **doc**: use Node.js instead of Node (Rich Trott) [#23967](https://github.com/nodejs/node/pull/23967)
- [[`52fe6dd286`](https://github.com/nodejs/node/commit/52fe6dd286)] - **doc**: revise BUILDING.md (Rich Trott) [#23966](https://github.com/nodejs/node/pull/23966)
- [[`95812953be`](https://github.com/nodejs/node/commit/95812953be)] - **doc**: clarify fd behaviour with {read,write}File (Sakthipriyan Vairamani (thefourtheye)) [#23706](https://github.com/nodejs/node/pull/23706)
- [[`f54f640704`](https://github.com/nodejs/node/commit/f54f640704)] - **doc**: fix typographical issues (Denis McDonald) [#23970](https://github.com/nodejs/node/pull/23970)
- [[`2fb89d0b86`](https://github.com/nodejs/node/commit/2fb89d0b86)] - **doc**: document HPE_HEADER_OVERFLOW error (Sam Roberts) [#23963](https://github.com/nodejs/node/pull/23963)
- [[`c68aab1705`](https://github.com/nodejs/node/commit/c68aab1705)] - **doc**: add documentation for http.IncomingMessage$complete (James M Snell) [#23914](https://github.com/nodejs/node/pull/23914)
- [[`1ac473f3a4`](https://github.com/nodejs/node/commit/1ac473f3a4)] - **doc**: remove mailing list (Rich Trott) [#23932](https://github.com/nodejs/node/pull/23932)
- [[`c8bf42af51`](https://github.com/nodejs/node/commit/c8bf42af51)] - **doc**: remove notice of dashes in V8 options (Denys Otrishko) [#23903](https://github.com/nodejs/node/pull/23903)
- [[`96004d6b7a`](https://github.com/nodejs/node/commit/96004d6b7a)] - **doc**: rename README section for Release Keys (Rich Trott) [#23927](https://github.com/nodejs/node/pull/23927)
- [[`2752091628`](https://github.com/nodejs/node/commit/2752091628)] - **doc**: add note about ABI compatibility (Myles Borins) [#22237](https://github.com/nodejs/node/pull/22237)
- [[`cc22a2a847`](https://github.com/nodejs/node/commit/cc22a2a847)] - **doc**: add optional callback to socket.end() (Ajido) [#23937](https://github.com/nodejs/node/pull/23937)
- [[`6099f5cbdc`](https://github.com/nodejs/node/commit/6099f5cbdc)] - **doc**: make example more clarified in cluster.md (ZYSzys) [#23931](https://github.com/nodejs/node/pull/23931)
- [[`437908a90b`](https://github.com/nodejs/node/commit/437908a90b)] - **doc**: simplify valid security issue descriptions (Rich Trott) [#23881](https://github.com/nodejs/node/pull/23881)
- [[`abeba5f8b5`](https://github.com/nodejs/node/commit/abeba5f8b5)] - **doc**: simplify path.basename() on POSIX and Windows (ZYSzys) [#23864](https://github.com/nodejs/node/pull/23864)
- [[`d830033274`](https://github.com/nodejs/node/commit/d830033274)] - **doc**: document nullptr comparisons in style guide (Anna Henningsen) [#23805](https://github.com/nodejs/node/pull/23805)
- [[`8b358ecf4d`](https://github.com/nodejs/node/commit/8b358ecf4d)] - **doc**: remove problematic example from README (Rich Trott) [#23817](https://github.com/nodejs/node/pull/23817)
- [[`1921865be2`](https://github.com/nodejs/node/commit/1921865be2)] - **doc**: use Cookie in request.setHeader() examples (Luigi Pinca) [#23707](https://github.com/nodejs/node/pull/23707)
- [[`913c4910c7`](https://github.com/nodejs/node/commit/913c4910c7)] - **doc**: NODE_EXTRA_CA_CERTS is ignored if setuid root (Ben Noordhuis) [#23770](https://github.com/nodejs/node/pull/23770)
- [[`ace83a24a2`](https://github.com/nodejs/node/commit/ace83a24a2)] - **doc**: add review suggestions to require() (erickwendel) [#23605](https://github.com/nodejs/node/pull/23605)
- [[`e87e9f2567`](https://github.com/nodejs/node/commit/e87e9f2567)] - **doc**: document and warn if the ICU version is too old (Steven R. Loomis) [#23766](https://github.com/nodejs/node/pull/23766)
- [[`070d87af65`](https://github.com/nodejs/node/commit/070d87af65)] - **doc**: move @phillipj to emeriti (Phillip Johnsen) [#23790](https://github.com/nodejs/node/pull/23790)
- [[`98fc848545`](https://github.com/nodejs/node/commit/98fc848545)] - **doc**: add note about removeListener order (James M Snell) [#23762](https://github.com/nodejs/node/pull/23762)
- [[`540168ce57`](https://github.com/nodejs/node/commit/540168ce57)] - **doc**: document ACL limitation for fs.access on Windows (James M Snell) [#23772](https://github.com/nodejs/node/pull/23772)
- [[`32df77c21f`](https://github.com/nodejs/node/commit/32df77c21f)] - **doc**: document that addMembership must be called once in a cluster (James M Snell) [#23746](https://github.com/nodejs/node/pull/23746)
- [[`2a2882b470`](https://github.com/nodejs/node/commit/2a2882b470)] - **doc**: add missing YAML labels (Vse Mozhet Byt) [#23810](https://github.com/nodejs/node/pull/23810)
- [[`5d5f85eb2e`](https://github.com/nodejs/node/commit/5d5f85eb2e)] - **doc**: remove reference to sslv3 in tls.md (James M Snell) [#23745](https://github.com/nodejs/node/pull/23745)
- [[`0b1e41799d`](https://github.com/nodejs/node/commit/0b1e41799d)] - **doc**: revise security-reporting example text (Rich Trott) [#23759](https://github.com/nodejs/node/pull/23759)
- [[`75082ee15b`](https://github.com/nodejs/node/commit/75082ee15b)] - **doc**: formalize non-const reference usage in C++ style guide (Anna Henningsen) [#23155](https://github.com/nodejs/node/pull/23155)
- [[`4315899377`](https://github.com/nodejs/node/commit/4315899377)] - **doc**: add missing deprecation labels (James M Snell) [#23761](https://github.com/nodejs/node/pull/23761)
- [[`aaf669d284`](https://github.com/nodejs/node/commit/aaf669d284)] - **doc**: document use of buffer.swap16() for utf16be (James M Snell) [#23747](https://github.com/nodejs/node/pull/23747)
- [[`e85d15a47f`](https://github.com/nodejs/node/commit/e85d15a47f)] - **doc**: add Backport-PR-URL info in backport guide (Ali Ijaz Sheikh) [#23701](https://github.com/nodejs/node/pull/23701)
- [[`535f113aea`](https://github.com/nodejs/node/commit/535f113aea)] - **doc**: improve README.md (Rich Trott) [#23705](https://github.com/nodejs/node/pull/23705)
- [[`d81b143af0`](https://github.com/nodejs/node/commit/d81b143af0)] - **doc**: simplify security reporting text (Rich Trott) [#23686](https://github.com/nodejs/node/pull/23686)
- [[`2a19518d18`](https://github.com/nodejs/node/commit/2a19518d18)] - **doc**: cleanup and references in C++ guide (Refael Ackermann) [#23650](https://github.com/nodejs/node/pull/23650)
- [[`5bea331d6d`](https://github.com/nodejs/node/commit/5bea331d6d)] - **doc**: fix url example to match behavior (Сковорода Никита Андреевич) [#23359](https://github.com/nodejs/node/pull/23359)
- [[`3db26d9f2f`](https://github.com/nodejs/node/commit/3db26d9f2f)] - **doc**: use reserved domains for examples in url.md (Сковорода Никита Андреевич) [#23359](https://github.com/nodejs/node/pull/23359)
- [[`ac752c6a8c`](https://github.com/nodejs/node/commit/ac752c6a8c)] - **doc**: fix pr-url in repl.md (Сковорода Никита Андреевич) [#23359](https://github.com/nodejs/node/pull/23359)
- [[`6748c0517b`](https://github.com/nodejs/node/commit/6748c0517b)] - **doc**: wrap links in \<\> (Сковорода Никита Андреевич) [#23359](https://github.com/nodejs/node/pull/23359)
- [[`011116cc5b`](https://github.com/nodejs/node/commit/011116cc5b)] - **doc**: edit BUILDING.md (Rich Trott) [#23435](https://github.com/nodejs/node/pull/23435)
- [[`e449bbbd54`](https://github.com/nodejs/node/commit/e449bbbd54)] - **doc**: describe SNI host name format (Sam Roberts) [#23357](https://github.com/nodejs/node/pull/23357)
- [[`d5b1368173`](https://github.com/nodejs/node/commit/d5b1368173)] - **doc**: revise security-reporting text in README (Rich Trott) [#23407](https://github.com/nodejs/node/pull/23407)
- [[`2798ac1709`](https://github.com/nodejs/node/commit/2798ac1709)] - **doc**: rewrite consensus seeking in guide (Rich Trott) [#23349](https://github.com/nodejs/node/pull/23349)
- [[`56d1bc9762`](https://github.com/nodejs/node/commit/56d1bc9762)] - **doc**: edit for minor fixes to prcoess.md (Rich Trott) [#23347](https://github.com/nodejs/node/pull/23347)
- [[`4c04e7c321`](https://github.com/nodejs/node/commit/4c04e7c321)] - **doc**: remove personal pronoun from worker_threads (Rich Trott) [#23347](https://github.com/nodejs/node/pull/23347)
- [[`df512961ce`](https://github.com/nodejs/node/commit/df512961ce)] - **doc**: remove personal pronoun from domain.md (Rich Trott) [#23347](https://github.com/nodejs/node/pull/23347)
- [[`647eba6656`](https://github.com/nodejs/node/commit/647eba6656)] - **doc**: remove style instruction that is not followed (Rich Trott) [#23346](https://github.com/nodejs/node/pull/23346)
- [[`457d22fa43`](https://github.com/nodejs/node/commit/457d22fa43)] - **doc**: add WebAssembly to globals (Steven) [#23339](https://github.com/nodejs/node/pull/23339)
- [[`73f490a5fd`](https://github.com/nodejs/node/commit/73f490a5fd)] - **doc,meta**: assign PR semantics (Refael Ackermann) [#23292](https://github.com/nodejs/node/pull/23292)
- [[`2ddf867030`](https://github.com/nodejs/node/commit/2ddf867030)] - **doc,meta**: refresh wording in colab guide (Refael Ackermann) [#23292](https://github.com/nodejs/node/pull/23292)
- [[`04f27aa764`](https://github.com/nodejs/node/commit/04f27aa764)] - **doc,meta**: add references to outside C++ guides (Refael Ackermann) [#23317](https://github.com/nodejs/node/pull/23317)
- [[`1491f32144`](https://github.com/nodejs/node/commit/1491f32144)] - **esm**: remove unused catch bindings (cjihrig) [#24079](https://github.com/nodejs/node/pull/24079)
- [[`e2b96da6ba`](https://github.com/nodejs/node/commit/e2b96da6ba)] - **events**: remove unused catch bindings (cjihrig) [#24079](https://github.com/nodejs/node/pull/24079)
- [[`a5462baf57`](https://github.com/nodejs/node/commit/a5462baf57)] - **fs**: remove unused catch bindings (cjihrig) [#24079](https://github.com/nodejs/node/pull/24079)
- [[`765a3a9466`](https://github.com/nodejs/node/commit/765a3a9466)] - **fs**: handle result of access binding directly in fs.existsSync (Joyee Cheung) [#24015](https://github.com/nodejs/node/pull/24015)
- [[`63f2649577`](https://github.com/nodejs/node/commit/63f2649577)] - **http**: reduce duplicated code for cleaning parser (Weijia Wang) [#23351](https://github.com/nodejs/node/pull/23351)
- [[`d08d252a94`](https://github.com/nodejs/node/commit/d08d252a94)] - **http2**: make Http2Settings constructors delegate (Daniel Bevenius) [#23326](https://github.com/nodejs/node/pull/23326)
- [[`d79d978cd8`](https://github.com/nodejs/node/commit/d79d978cd8)] - **inspector**: remove unused catch bindings (cjihrig) [#24079](https://github.com/nodejs/node/pull/24079)
- [[`4035ca16c5`](https://github.com/nodejs/node/commit/4035ca16c5)] - **lib**: add crypto dependant modules cannotUseCache (Daniel Bevenius) [#24100](https://github.com/nodejs/node/pull/24100)
- [[`a0b4f7a10e`](https://github.com/nodejs/node/commit/a0b4f7a10e)] - **lib**: move process prototype manipulation into setupProcessObject (Joyee Cheung) [#24089](https://github.com/nodejs/node/pull/24089)
- [[`cb883bcbb0`](https://github.com/nodejs/node/commit/cb883bcbb0)] - **lib**: fix grammar error and make it clearer for comments (MaleDong) [#23799](https://github.com/nodejs/node/pull/23799)
- [[`8ef5de55fd`](https://github.com/nodejs/node/commit/8ef5de55fd)] - **lib**: move module exports proxy into a separate method (Joyee Cheung) [#24057](https://github.com/nodejs/node/pull/24057)
- [[`1841d0f776`](https://github.com/nodejs/node/commit/1841d0f776)] - **lib**: fix code cache generation (Joyee Cheung) [#23855](https://github.com/nodejs/node/pull/23855)
- [[`b5eacfa5bd`](https://github.com/nodejs/node/commit/b5eacfa5bd)] - **lib**: remove useless cwd in posix.resolve (ZYSzys) [#23902](https://github.com/nodejs/node/pull/23902)
- [[`d07cef7ed4`](https://github.com/nodejs/node/commit/d07cef7ed4)] - **lib**: migrate from process.binding('config') to getOptions() (Vladimir Ilic) [#23588](https://github.com/nodejs/node/pull/23588)
- [[`5ebe7f7fc7`](https://github.com/nodejs/node/commit/5ebe7f7fc7)] - **lib**: migrate process.binding to internalBinding (surreal8) [#23517](https://github.com/nodejs/node/pull/23517)
- [[`9b22e2a53a`](https://github.com/nodejs/node/commit/9b22e2a53a)] - **lib**: migrate process.binding to getOptions (Randy Wressell) [#23522](https://github.com/nodejs/node/pull/23522)
- [[`83f4fcc176`](https://github.com/nodejs/node/commit/83f4fcc176)] - **lib**: migrate process.binding('config') to getOptions() (Jonny Kalambay) [#23526](https://github.com/nodejs/node/pull/23526)
- [[`66119ed158`](https://github.com/nodejs/node/commit/66119ed158)] - **lib**: removed unused variable (Long Nguyen) [#23497](https://github.com/nodejs/node/pull/23497)
- [[`33cf3b26b0`](https://github.com/nodejs/node/commit/33cf3b26b0)] - **lib**: switch to internalBinding for cjs loader (Steven Scott) [#23492](https://github.com/nodejs/node/pull/23492)
- [[`cb597f231a`](https://github.com/nodejs/node/commit/cb597f231a)] - **lib**: migrate from process.binding to internalBinding (Andres Monge) [#23586](https://github.com/nodejs/node/pull/23586)
- [[`6f01a792c8`](https://github.com/nodejs/node/commit/6f01a792c8)] - **lib**: migrate to getOptions in loaders.js (David Xue) [#23455](https://github.com/nodejs/node/pull/23455)
- [[`f219f82eb7`](https://github.com/nodejs/node/commit/f219f82eb7)] - **lib**: remove an unused variable (Claire Liu) [#23482](https://github.com/nodejs/node/pull/23482)
- [[`2ce8f7a507`](https://github.com/nodejs/node/commit/2ce8f7a507)] - **lib**: remove unused 'e' from catch (Matt Holmes) [#23458](https://github.com/nodejs/node/pull/23458)
- [[`1fa9d91d37`](https://github.com/nodejs/node/commit/1fa9d91d37)] - **lib**: http server, friendly error messages (Sagi Tsofan) [#22995](https://github.com/nodejs/node/pull/22995)
- [[`9718919da9`](https://github.com/nodejs/node/commit/9718919da9)] - **meta**: clarify fast-track approval (James M Snell) [#23744](https://github.com/nodejs/node/pull/23744)
- [[`a116e32a09`](https://github.com/nodejs/node/commit/a116e32a09)] - **meta,doc**: ping community about new release (Refael Ackermann) [#24064](https://github.com/nodejs/node/pull/24064)
- [[`19e2e6d891`](https://github.com/nodejs/node/commit/19e2e6d891)] - **module**: removed unused variable (Martin Omander) [#23624](https://github.com/nodejs/node/pull/23624)
- [[`9f8349c49c`](https://github.com/nodejs/node/commit/9f8349c49c)] - **n-api**: add missing handle scopes (Daniel Bevenius) [#24011](https://github.com/nodejs/node/pull/24011)
- [[`02a54ed1fa`](https://github.com/nodejs/node/commit/02a54ed1fa)] - **n-api**: make per-`Context`-ness of `napi_env` explicit (Anna Henningsen) [#23689](https://github.com/nodejs/node/pull/23689)
- [[`56afb7b481`](https://github.com/nodejs/node/commit/56afb7b481)] - **net**: `net.Server.listen()` avoid operations on `null` when fail (Ouyang Yadong) [#23920](https://github.com/nodejs/node/pull/23920)
- [[`4a79bef6c6`](https://github.com/nodejs/node/commit/4a79bef6c6)] - **os**: fix memory leak in `userInfo()` (Anna Henningsen) [#23893](https://github.com/nodejs/node/pull/23893)
- [[`8001beefac`](https://github.com/nodejs/node/commit/8001beefac)] - **querystring**: remove unused catch bindings (cjihrig) [#24079](https://github.com/nodejs/node/pull/24079)
- [[`f11907c32d`](https://github.com/nodejs/node/commit/f11907c32d)] - **readline**: assert without the use of event listener (Lian Li) [#23472](https://github.com/nodejs/node/pull/23472)
- [[`4b456d566a`](https://github.com/nodejs/node/commit/4b456d566a)] - **repl**: remove unused catch bindings (cjihrig) [#24079](https://github.com/nodejs/node/pull/24079)
- [[`f866a0bcb9`](https://github.com/nodejs/node/commit/f866a0bcb9)] - **repl**: use promise#finally (Weijia Wang) [#23971](https://github.com/nodejs/node/pull/23971)
- [[`307b277b15`](https://github.com/nodejs/node/commit/307b277b15)] - **repl**: migrate from process.binding('config') to getOptions() (Jose Bucio) [#23684](https://github.com/nodejs/node/pull/23684)
- [[`d07ee1ca0b`](https://github.com/nodejs/node/commit/d07ee1ca0b)] - **repl**: remove unused variable from try catch (mmisiarek) [#23452](https://github.com/nodejs/node/pull/23452)
- [[`4c0e7b4d66`](https://github.com/nodejs/node/commit/4c0e7b4d66)] - **repl**: remove unused variable e from try catch (Khalid Adil) [#23449](https://github.com/nodejs/node/pull/23449)
- [[`9106ccf9ca`](https://github.com/nodejs/node/commit/9106ccf9ca)] - **src**: prefer param function check over args length (Shelley Vohr) [#23835](https://github.com/nodejs/node/pull/23835)
- [[`c6ad469ae4`](https://github.com/nodejs/node/commit/c6ad469ae4)] - **src**: fix fully-static & large-pages combination (Suresh Srinivas) [#23964](https://github.com/nodejs/node/pull/23964)
- [[`d82e818848`](https://github.com/nodejs/node/commit/d82e818848)] - **src**: use "constants" string instead of creating new one (Ouyang Yadong) [#23894](https://github.com/nodejs/node/pull/23894)
- [[`bb05aa3206`](https://github.com/nodejs/node/commit/bb05aa3206)] - **src**: reduce duplication in tcp_wrap Connect (Daniel Bevenius) [#23753](https://github.com/nodejs/node/pull/23753)
- [[`b53b0d1475`](https://github.com/nodejs/node/commit/b53b0d1475)] - **src**: refactor deprecated v8::String::NewFromTwoByte call (Romain Lanz) [#23803](https://github.com/nodejs/node/pull/23803)
- [[`17db407ba2`](https://github.com/nodejs/node/commit/17db407ba2)] - **src**: refactor deprecated v8::Function::Call call (Romain Lanz) [#23804](https://github.com/nodejs/node/pull/23804)
- [[`6bc66bf756`](https://github.com/nodejs/node/commit/6bc66bf756)] - **src**: fix CreatePlatform header param mismatch (Shelley Vohr) [#23947](https://github.com/nodejs/node/pull/23947)
- [[`511fa20983`](https://github.com/nodejs/node/commit/511fa20983)] - **src**: trace_event: secondary storage for metadata (Ali Ijaz Sheikh) [#20900](https://github.com/nodejs/node/pull/20900)
- [[`71557d3f70`](https://github.com/nodejs/node/commit/71557d3f70)] - **src**: initial large page (2M) support (Suresh Srinivas) [#22079](https://github.com/nodejs/node/pull/22079)
- [[`f1fc05b45c`](https://github.com/nodejs/node/commit/f1fc05b45c)] - **src**: changed stdio_pipes\_ to std::vector (Steven Auger) [#23615](https://github.com/nodejs/node/pull/23615)
- [[`018a8683d6`](https://github.com/nodejs/node/commit/018a8683d6)] - **src**: update v8::Object::GetPropertyNames() usage (cjihrig) [#23660](https://github.com/nodejs/node/pull/23660)
- [[`05409c9075`](https://github.com/nodejs/node/commit/05409c9075)] - **src**: remove OCB support ifdef OPENSSL_NO_OCB (Shelley Vohr) [#23635](https://github.com/nodejs/node/pull/23635)
- [[`e7bf838b1e`](https://github.com/nodejs/node/commit/e7bf838b1e)] - **src**: change macro to fn (Gino Notto) [#23603](https://github.com/nodejs/node/pull/23603)
- [[`d152d7fb60`](https://github.com/nodejs/node/commit/d152d7fb60)] - **src**: add default initializer in tls_wrap (Richard Hoehn) [#23567](https://github.com/nodejs/node/pull/23567)
- [[`3d76ab9287`](https://github.com/nodejs/node/commit/3d76ab9287)] - **src**: use MallocedBuffer abstraction for buffers (Cody Hazelwood) [#23543](https://github.com/nodejs/node/pull/23543)
- [[`b258b377db`](https://github.com/nodejs/node/commit/b258b377db)] - **src**: use default initializers over settings fields on the constructor (Andrew J D McCann) [#23532](https://github.com/nodejs/node/pull/23532)
- [[`e04a128731`](https://github.com/nodejs/node/commit/e04a128731)] - **src**: remove unused UVHandle methods (MarianneDr) [#23535](https://github.com/nodejs/node/pull/23535)
- [[`c854879dd4`](https://github.com/nodejs/node/commit/c854879dd4)] - **src**: ready background workers before bootstrap (Ali Ijaz Sheikh) [#23233](https://github.com/nodejs/node/pull/23233)
- [[`84aa6b2c59`](https://github.com/nodejs/node/commit/84aa6b2c59)] - **src**: move default assignment of async_id\_ in async_wrap.h (David Corona) [#23495](https://github.com/nodejs/node/pull/23495)
- [[`a663d5674d`](https://github.com/nodejs/node/commit/a663d5674d)] - **src**: fix bug in MallocedBuffer constructor (Tobias Nießen) [#23434](https://github.com/nodejs/node/pull/23434)
- [[`38f5644449`](https://github.com/nodejs/node/commit/38f5644449)] - **src**: improve SSL version extraction logic (Gireesh Punathil) [#23050](https://github.com/nodejs/node/pull/23050)
- [[`8cfbce3163`](https://github.com/nodejs/node/commit/8cfbce3163)] - **src**: revert removal of SecureContext `_external` getter (Vitaly Dyatlov) [#21711](https://github.com/nodejs/node/pull/21711)
- [[`7ed4079286`](https://github.com/nodejs/node/commit/7ed4079286)] - **src**: remove unused limits header from util-inl.h (Daniel Bevenius) [#23353](https://github.com/nodejs/node/pull/23353)
- [[`eb71ab5f99`](https://github.com/nodejs/node/commit/eb71ab5f99)] - **src**: replace NO_RETURN with \[\[noreturn\]\] (Refael Ackermann) [#23337](https://github.com/nodejs/node/pull/23337)
- [[`a22ef72afb`](https://github.com/nodejs/node/commit/a22ef72afb)] - **src**: fix usage of deprecated v8::Date::New (Michaël Zasso) [#23288](https://github.com/nodejs/node/pull/23288)
- [[`b4cdd478b9`](https://github.com/nodejs/node/commit/b4cdd478b9)] - **src,win**: informative stack traces (Refael Ackermann) [#23822](https://github.com/nodejs/node/pull/23822)
- [[`cb861a4897`](https://github.com/nodejs/node/commit/cb861a4897)] - **stream**: do not error async iterators on destroy(null) (Matteo Collina) [#23901](https://github.com/nodejs/node/pull/23901)
- [[`6f4a638175`](https://github.com/nodejs/node/commit/6f4a638175)] - **stream**: ended streams should resolve the async iteration (Matteo Collina) [#23901](https://github.com/nodejs/node/pull/23901)
- [[`ce79ae6544`](https://github.com/nodejs/node/commit/ce79ae6544)] - **stream**: async iteration should work with destroyed stream (Matteo Collina) [#23785](https://github.com/nodejs/node/pull/23785)
- [[`78be4771a6`](https://github.com/nodejs/node/commit/78be4771a6)] - **test**: remove unused catch bindings (cjihrig) [#24079](https://github.com/nodejs/node/pull/24079)
- [[`43097d9d7f`](https://github.com/nodejs/node/commit/43097d9d7f)] - **test**: disable color formating for test-internal-errors.js (Refael Ackermann) [#24204](https://github.com/nodejs/node/pull/24204)
- [[`dddb466f59`](https://github.com/nodejs/node/commit/dddb466f59)] - **test**: add crypto check to test-benchmark-http2 (Daniel Bevenius) [#24096](https://github.com/nodejs/node/pull/24096)
- [[`1b6c00e3fa`](https://github.com/nodejs/node/commit/1b6c00e3fa)] - **test**: increase --stack_size test-async-wrap-pop (Daniel Bevenius) [#23996](https://github.com/nodejs/node/pull/23996)
- [[`b3ab546b37`](https://github.com/nodejs/node/commit/b3ab546b37)] - **test**: assert that invalidcmd throws error code (Jerome Covington) [#23942](https://github.com/nodejs/node/pull/23942)
- [[`52c9209f1c`](https://github.com/nodejs/node/commit/52c9209f1c)] - **test**: fix strictEqual arguments order (Esteban Sotillo) [#23956](https://github.com/nodejs/node/pull/23956)
- [[`1d6dd961ef`](https://github.com/nodejs/node/commit/1d6dd961ef)] - **test**: add property for RangeError in test-buffer-copy (mritunjaygoutam12) [#23968](https://github.com/nodejs/node/pull/23968)
- [[`5bedd1ce2f`](https://github.com/nodejs/node/commit/5bedd1ce2f)] - **test**: fix test-fs-watch-system-limit (Ali Ijaz Sheikh) [#23986](https://github.com/nodejs/node/pull/23986)
- [[`c5577c17ac`](https://github.com/nodejs/node/commit/c5577c17ac)] - **test**: run code cache test by default and test generator (Joyee Cheung) [#23855](https://github.com/nodejs/node/pull/23855)
- [[`4eab8a1079`](https://github.com/nodejs/node/commit/4eab8a1079)] - **test**: fix regression when compiled with FIPS (Adam Majer) [#23871](https://github.com/nodejs/node/pull/23871)
- [[`682361cd4a`](https://github.com/nodejs/node/commit/682361cd4a)] - **test**: fix strictEqual() argument order (Loic) [#23829](https://github.com/nodejs/node/pull/23829)
- [[`d1c6238e1d`](https://github.com/nodejs/node/commit/d1c6238e1d)] - **test**: verify `performance.timerify()` works w/ non-Node Contexts (Anna Henningsen) [#23784](https://github.com/nodejs/node/pull/23784)
- [[`55ca2a71fd`](https://github.com/nodejs/node/commit/55ca2a71fd)] - **test**: add test-benchmark-napi (Emily Marigold Klassen) [#23585](https://github.com/nodejs/node/pull/23585)
- [[`ce6ddc404d`](https://github.com/nodejs/node/commit/ce6ddc404d)] - **test**: increase coverage of internal/stream/end-of-stream (Tyler Vann-Campbell) [#23751](https://github.com/nodejs/node/pull/23751)
- [[`0ce76be843`](https://github.com/nodejs/node/commit/0ce76be843)] - **test**: fix strictEqual() arguments order (Nolan Rigo) [#23800](https://github.com/nodejs/node/pull/23800)
- [[`1747e473bd`](https://github.com/nodejs/node/commit/1747e473bd)] - **test**: fix invalid modulesLength for DSA keygen (Adam Majer) [#23732](https://github.com/nodejs/node/pull/23732)
- [[`9b6b2800e6`](https://github.com/nodejs/node/commit/9b6b2800e6)] - **test**: fix test-require-symlink on Windows (Bartosz Sosnowski) [#23691](https://github.com/nodejs/node/pull/23691)
- [[`874d02375b`](https://github.com/nodejs/node/commit/874d02375b)] - **test**: fix strictEqual() argument order (Romain Lanz) [#23768](https://github.com/nodejs/node/pull/23768)
- [[`3d43679907`](https://github.com/nodejs/node/commit/3d43679907)] - **test**: fix strictEqual() arguments order (Thomas GENTILHOMME) [#23771](https://github.com/nodejs/node/pull/23771)
- [[`cc040f6887`](https://github.com/nodejs/node/commit/cc040f6887)] - **test**: fix assertion arguments order (Elian Gutierrez) [#23787](https://github.com/nodejs/node/pull/23787)
- [[`5844932b6c`](https://github.com/nodejs/node/commit/5844932b6c)] - **test**: add blocks and comments to fs-promises tests (Ian Sutherland) [#23627](https://github.com/nodejs/node/pull/23627)
- [[`9aced4c384`](https://github.com/nodejs/node/commit/9aced4c384)] - **test**: add a test for `tls.Socket` with `allowHalfOpen` (Ouyang Yadong) [#23866](https://github.com/nodejs/node/pull/23866)
- [[`9979c71d67`](https://github.com/nodejs/node/commit/9979c71d67)] - **test**: increase coverage for readfile with withFileTypes (christian-bromann) [#23557](https://github.com/nodejs/node/pull/23557)
- [[`3bc7b5efd2`](https://github.com/nodejs/node/commit/3bc7b5efd2)] - **test**: skip failing tests for osx mojave (jn99) [#23550](https://github.com/nodejs/node/pull/23550)
- [[`51ceaf5bc1`](https://github.com/nodejs/node/commit/51ceaf5bc1)] - **test**: enable trace-events tests for workers (Richard Lau) [#23698](https://github.com/nodejs/node/pull/23698)
- [[`3e143dfdd9`](https://github.com/nodejs/node/commit/3e143dfdd9)] - **test**: improve test coverage for fs module (<EMAIL>) [#23601](https://github.com/nodejs/node/pull/23601)
- [[`65b37324c2`](https://github.com/nodejs/node/commit/65b37324c2)] - **test**: fix argument order in assertion (Illescas, Ricardo) [#23581](https://github.com/nodejs/node/pull/23581)
- [[`d61901499a`](https://github.com/nodejs/node/commit/d61901499a)] - **test**: reversed params in assert.strictEqual() (Dusan Radovanovic) [#23591](https://github.com/nodejs/node/pull/23591)
- [[`6baba1d54a`](https://github.com/nodejs/node/commit/6baba1d54a)] - **test**: correct order of args in buffer compare (James Irwin) [#23521](https://github.com/nodejs/node/pull/23521)
- [[`232fe58a4c`](https://github.com/nodejs/node/commit/232fe58a4c)] - **test**: check codes of thrown errors (Nancy Truong) [#23519](https://github.com/nodejs/node/pull/23519)
- [[`c2aa762fd7`](https://github.com/nodejs/node/commit/c2aa762fd7)] - **test**: fix strictEqual arguments order (Jonathan Samines) [#23486](https://github.com/nodejs/node/pull/23486)
- [[`dc12fa19ab`](https://github.com/nodejs/node/commit/dc12fa19ab)] - **test**: add test coverage for fs.truncate (christian-bromann) [#23620](https://github.com/nodejs/node/pull/23620)
- [[`4daf8e0910`](https://github.com/nodejs/node/commit/4daf8e0910)] - **test**: use smaller keys for a faster keygen test (Sam Roberts) [#23430](https://github.com/nodejs/node/pull/23430)
- [[`6d8669c5d8`](https://github.com/nodejs/node/commit/6d8669c5d8)] - **test**: increased code coverage for slowCases (Jared Haines) [#23592](https://github.com/nodejs/node/pull/23592)
- [[`51c3685dbd`](https://github.com/nodejs/node/commit/51c3685dbd)] - **test**: assertions arguments match docs (Amanuel Ghebreweldi) [#23594](https://github.com/nodejs/node/pull/23594)
- [[`4ab822329e`](https://github.com/nodejs/node/commit/4ab822329e)] - **test**: fix assert.strictEqual() argument order (Derek) [#23598](https://github.com/nodejs/node/pull/23598)
- [[`779d5ec0fe`](https://github.com/nodejs/node/commit/779d5ec0fe)] - **test**: fix assert parameter order in test-https-localaddress.js (Ian Sutherland) [#23599](https://github.com/nodejs/node/pull/23599)
- [[`fad9d805ef`](https://github.com/nodejs/node/commit/fad9d805ef)] - **test**: change order of assert.strictEquals arguments (Chuck Theobald) [#23600](https://github.com/nodejs/node/pull/23600)
- [[`07c8a9e7a7`](https://github.com/nodejs/node/commit/07c8a9e7a7)] - **test**: fix assert equal order of arguments (David Jiang) [#23602](https://github.com/nodejs/node/pull/23602)
- [[`591af98268`](https://github.com/nodejs/node/commit/591af98268)] - **test**: fix order of assert args in client response domain test (Emily Kolar) [#23604](https://github.com/nodejs/node/pull/23604)
- [[`ede9ce14d5`](https://github.com/nodejs/node/commit/ede9ce14d5)] - **test**: re-order strictEqual paramater calls (Paul Tichonczuk) [#23607](https://github.com/nodejs/node/pull/23607)
- [[`61cf1cfb20`](https://github.com/nodejs/node/commit/61cf1cfb20)] - **test**: fix assertions args order (Milton Sosa) [#23608](https://github.com/nodejs/node/pull/23608)
- [[`7b2e7aa64e`](https://github.com/nodejs/node/commit/7b2e7aa64e)] - **test**: fix parameters in test-repl.js (Israel Ortiz) [#23609](https://github.com/nodejs/node/pull/23609)
- [[`f7f5c5c477`](https://github.com/nodejs/node/commit/f7f5c5c477)] - **test**: reverse arguments in assert.strictEqual (Vsevolod Geraskin) [#23613](https://github.com/nodejs/node/pull/23613)
- [[`8be279af17`](https://github.com/nodejs/node/commit/8be279af17)] - **test**: update assertion parameter order (Sean Healy) [#23614](https://github.com/nodejs/node/pull/23614)
- [[`4c35953fb2`](https://github.com/nodejs/node/commit/4c35953fb2)] - **test**: fix backward assertion arguments (Stéphane Vasseur) [#23616](https://github.com/nodejs/node/pull/23616)
- [[`3eb8938778`](https://github.com/nodejs/node/commit/3eb8938778)] - **test**: reversed 1st and 2nd arguments for assert.strictEqual() (vchoubey08) [#23617](https://github.com/nodejs/node/pull/23617)
- [[`5ce59a148f`](https://github.com/nodejs/node/commit/5ce59a148f)] - **test**: correct assertion argument order (Jeff Marvin) [#23618](https://github.com/nodejs/node/pull/23618)
- [[`1957001f19`](https://github.com/nodejs/node/commit/1957001f19)] - **test**: fix assertion order (erickwendel) [#23626](https://github.com/nodejs/node/pull/23626)
- [[`5d7676c047`](https://github.com/nodejs/node/commit/5d7676c047)] - **test**: updated assert test values to doc standards (keeysnc) [#23593](https://github.com/nodejs/node/pull/23593)
- [[`7c35f7f608`](https://github.com/nodejs/node/commit/7c35f7f608)] - **test**: switch order of assertion arguments (Mel) [#23563](https://github.com/nodejs/node/pull/23563)
- [[`46ca12f81e`](https://github.com/nodejs/node/commit/46ca12f81e)] - **test**: fix assert.strictEqual() argument order (Savio Resende) [#23564](https://github.com/nodejs/node/pull/23564)
- [[`bf32cde70a`](https://github.com/nodejs/node/commit/bf32cde70a)] - **test**: fix parameter order of assertions (Pete Lombardo) [#23565](https://github.com/nodejs/node/pull/23565)
- [[`d9e58b8bed`](https://github.com/nodejs/node/commit/d9e58b8bed)] - **test**: fix assert value order (Ethan Weber) [#23566](https://github.com/nodejs/node/pull/23566)
- [[`d574d865d8`](https://github.com/nodejs/node/commit/d574d865d8)] - **test**: fix strictEqual order for timers test (Saleh Abdel Motaal) [#23568](https://github.com/nodejs/node/pull/23568)
- [[`f33fe7428e`](https://github.com/nodejs/node/commit/f33fe7428e)] - **test**: corrected assertion arguments order (francois) [#23569](https://github.com/nodejs/node/pull/23569)
- [[`524222e55a`](https://github.com/nodejs/node/commit/524222e55a)] - **test**: fix strictEqual input parameters order (AlixAng) [#23570](https://github.com/nodejs/node/pull/23570)
- [[`0f8709ce4b`](https://github.com/nodejs/node/commit/0f8709ce4b)] - **test**: fix order of arguments passed to strictEqual (Joe Shindelar) [#23571](https://github.com/nodejs/node/pull/23571)
- [[`8f1cea6e18`](https://github.com/nodejs/node/commit/8f1cea6e18)] - **test**: fix arguments ordering for assertions to match the docs (Liran Tal) [#23575](https://github.com/nodejs/node/pull/23575)
- [[`7fed93d699`](https://github.com/nodejs/node/commit/7fed93d699)] - **test**: fixed strictEqual arguments order (Ruy Adorno) [#23576](https://github.com/nodejs/node/pull/23576)
- [[`86fd1fc0f7`](https://github.com/nodejs/node/commit/86fd1fc0f7)] - **test**: add crypto.scrypt test case with different encoding (Yitong) [#23578](https://github.com/nodejs/node/pull/23578)
- [[`94c7406546`](https://github.com/nodejs/node/commit/94c7406546)] - **test**: reversed actual and expected values for .strictEqual() (Salman Shakeel) [#23579](https://github.com/nodejs/node/pull/23579)
- [[`3d1e51e130`](https://github.com/nodejs/node/commit/3d1e51e130)] - **test**: increased code coverage for proxySessionHandler (Justin Lee) [#23583](https://github.com/nodejs/node/pull/23583)
- [[`56043109da`](https://github.com/nodejs/node/commit/56043109da)] - **test**: fix assertion arguments order (seantcoyote) [#23584](https://github.com/nodejs/node/pull/23584)
- [[`38b6fffb78`](https://github.com/nodejs/node/commit/38b6fffb78)] - **test**: fix assert.strictEqual() parameter order in test-path-maklong.js (blakehall) [#23587](https://github.com/nodejs/node/pull/23587)
- [[`88cbb4afe6`](https://github.com/nodejs/node/commit/88cbb4afe6)] - **test**: fix argument order in assertions (Illescas, Ricardo) [#23589](https://github.com/nodejs/node/pull/23589)
- [[`ff22625173`](https://github.com/nodejs/node/commit/ff22625173)] - **test**: fix order of parameters to assert.strictEqual (Jason Nutter) [#23590](https://github.com/nodejs/node/pull/23590)
- [[`2498369f29`](https://github.com/nodejs/node/commit/2498369f29)] - **test**: removed unused variable in fs-watch-file-slow (Maki Toda) [#23548](https://github.com/nodejs/node/pull/23548)
- [[`9a79824348`](https://github.com/nodejs/node/commit/9a79824348)] - **test**: update strictEqual arguments order (Clinton Pahl) [#23552](https://github.com/nodejs/node/pull/23552)
- [[`720262bacb`](https://github.com/nodejs/node/commit/720262bacb)] - **test**: removed unused error variable in try catch (Murtaza H) [#23553](https://github.com/nodejs/node/pull/23553)
- [[`f55a5027b6`](https://github.com/nodejs/node/commit/f55a5027b6)] - **test**: reverse order of args in reconnect-error assert (Jackelin Herrera) [#23555](https://github.com/nodejs/node/pull/23555)
- [[`402cb65b8f`](https://github.com/nodejs/node/commit/402cb65b8f)] - **test**: added async-hook benchmark (peter) [#23556](https://github.com/nodejs/node/pull/23556)
- [[`fcd273e6a7`](https://github.com/nodejs/node/commit/fcd273e6a7)] - **test**: fix order of assert arguments in vm-new-script-this-context (Victor Poriazov) [#23558](https://github.com/nodejs/node/pull/23558)
- [[`699a1ace7c`](https://github.com/nodejs/node/commit/699a1ace7c)] - **test**: modernize test-crypto-domain (naris93) [#23559](https://github.com/nodejs/node/pull/23559)
- [[`c64126905c`](https://github.com/nodejs/node/commit/c64126905c)] - **test**: fix strictEqual assertion order on readline tests (Joe Grosspietsch) [#23561](https://github.com/nodejs/node/pull/23561)
- [[`29db84a7cc`](https://github.com/nodejs/node/commit/29db84a7cc)] - **test**: switch strictEqual parameters - actual first before expected (Chris Bautista) [#23537](https://github.com/nodejs/node/pull/23537)
- [[`9bc7d839af`](https://github.com/nodejs/node/commit/9bc7d839af)] - **test**: assert.strictEqual parameters ordered correctly (Justin denBroeder) [#23538](https://github.com/nodejs/node/pull/23538)
- [[`f1c0538d32`](https://github.com/nodejs/node/commit/f1c0538d32)] - **test**: fix assert.strictEqual() arguments order (Ivan Lukasevych) [#23539](https://github.com/nodejs/node/pull/23539)
- [[`f2d10452b7`](https://github.com/nodejs/node/commit/f2d10452b7)] - **test**: reverse the order of assertion statement arguments in pingpong test (Allan Zheng) [#23540](https://github.com/nodejs/node/pull/23540)
- [[`5292f54316`](https://github.com/nodejs/node/commit/5292f54316)] - **test**: added test for generateKeyPair (David Xue) [#23541](https://github.com/nodejs/node/pull/23541)
- [[`a8501e4ade`](https://github.com/nodejs/node/commit/a8501e4ade)] - **test**: swap expected and actual arguments in assert.strictEqual() (Erin Bush) [#23542](https://github.com/nodejs/node/pull/23542)
- [[`a1ad454615`](https://github.com/nodejs/node/commit/a1ad454615)] - **test**: fix assertions argument order (KelvinLawHF1) [#23544](https://github.com/nodejs/node/pull/23544)
- [[`643004f08b`](https://github.com/nodejs/node/commit/643004f08b)] - **test**: fix assertion argument order (Carl Richmond) [#23545](https://github.com/nodejs/node/pull/23545)
- [[`d54e15f6f7`](https://github.com/nodejs/node/commit/d54e15f6f7)] - **test**: refactor callback functions to arrow functions (Sean Healy) [#23546](https://github.com/nodejs/node/pull/23546)
- [[`299285a2ff`](https://github.com/nodejs/node/commit/299285a2ff)] - **test**: updating assertion and expect order in test-tls-client-verify.js (Eli Itah) [#23547](https://github.com/nodejs/node/pull/23547)
- [[`91816adb8f`](https://github.com/nodejs/node/commit/91816adb8f)] - **test**: use correct argument order for assert.strictEqual() (Oktavianus Ludiro) [#23527](https://github.com/nodejs/node/pull/23527)
- [[`c91497b29b`](https://github.com/nodejs/node/commit/c91497b29b)] - **test**: corrected the order of arguments in assert.strictEqual() (Diana Lee) [#23528](https://github.com/nodejs/node/pull/23528)
- [[`8a7fa5ef4b`](https://github.com/nodejs/node/commit/8a7fa5ef4b)] - **test**: fix assert.strictEqual() argument order (ssamuels0916) [#23529](https://github.com/nodejs/node/pull/23529)
- [[`e25f288367`](https://github.com/nodejs/node/commit/e25f288367)] - **test**: fix strictEqual assertion argument in test-tls-ecdh-auto (jaxyz) [#23530](https://github.com/nodejs/node/pull/23530)
- [[`04ec990857`](https://github.com/nodejs/node/commit/04ec990857)] - **test**: correct labelling of asserts errors (nofwayy) [#23531](https://github.com/nodejs/node/pull/23531)
- [[`3c5d312662`](https://github.com/nodejs/node/commit/3c5d312662)] - **test**: reorder asserts arguments (Marcos Frony) [#23534](https://github.com/nodejs/node/pull/23534)
- [[`a665e3ac1e`](https://github.com/nodejs/node/commit/a665e3ac1e)] - **test**: updating assertion on test so it fits the new method signature (<EMAIL>) [#23536](https://github.com/nodejs/node/pull/23536)
- [[`bf39c9c56e`](https://github.com/nodejs/node/commit/bf39c9c56e)] - **test**: refactor functions to es6 (Michael Chen) [#23510](https://github.com/nodejs/node/pull/23510)
- [[`0267e2fbec`](https://github.com/nodejs/node/commit/0267e2fbec)] - **test**: replaced functions with arrow functions (edgarzapeka) [#23511](https://github.com/nodejs/node/pull/23511)
- [[`a059180f57`](https://github.com/nodejs/node/commit/a059180f57)] - **test**: corret assertion arg order in test-regress-GH-892.js (Elvis-Philip N) [#23513](https://github.com/nodejs/node/pull/23513)
- [[`abb583bc4f`](https://github.com/nodejs/node/commit/abb583bc4f)] - **test**: fix test-dgram-pingpong assertion arg order (David Ward) [#23514](https://github.com/nodejs/node/pull/23514)
- [[`283766474a`](https://github.com/nodejs/node/commit/283766474a)] - **test**: fix assert.strictEqual() argument order (Ben Schaaf) [#23515](https://github.com/nodejs/node/pull/23515)
- [[`1e0a969b85`](https://github.com/nodejs/node/commit/1e0a969b85)] - **test**: fix assert.strictEqual arg order in test-tls-ecdh-multiple.js (Takdeer Sodhan) [#23516](https://github.com/nodejs/node/pull/23516)
- [[`d54d2f0743`](https://github.com/nodejs/node/commit/d54d2f0743)] - **test**: use the correct parameter order on assert.strictEqual() (Tyler Vann-Campbell) [#23520](https://github.com/nodejs/node/pull/23520)
- [[`98b4a94ebc`](https://github.com/nodejs/node/commit/98b4a94ebc)] - **test**: fix assert order in test-vm-context (Lee Gray) [#23523](https://github.com/nodejs/node/pull/23523)
- [[`168cc9d9d5`](https://github.com/nodejs/node/commit/168cc9d9d5)] - **test**: switch arguments of assert() (Arne Schramm) [#23524](https://github.com/nodejs/node/pull/23524)
- [[`1cf532fc06`](https://github.com/nodejs/node/commit/1cf532fc06)] - **test**: swap assert argument order in test-vm-create-and-run-in-context.js (Pascal Lambert) [#23525](https://github.com/nodejs/node/pull/23525)
- [[`cf0dbbd49f`](https://github.com/nodejs/node/commit/cf0dbbd49f)] - **test**: fix order of assert.strictEqual() args to actual, expected (Joshua Belcher) [#23501](https://github.com/nodejs/node/pull/23501)
- [[`30c31ba005`](https://github.com/nodejs/node/commit/30c31ba005)] - **test**: fixed incorrect variable order in assert.strictEqual() (Daniyal Mokhammad) [#23502](https://github.com/nodejs/node/pull/23502)
- [[`2e879a6979`](https://github.com/nodejs/node/commit/2e879a6979)] - **test**: properly order test assertion variables (David Scott) [#23503](https://github.com/nodejs/node/pull/23503)
- [[`e3f34934fc`](https://github.com/nodejs/node/commit/e3f34934fc)] - **test**: modernize test-child-process-flush-stdio (Viacheslav Liakhov) [#23504](https://github.com/nodejs/node/pull/23504)
- [[`a8a30a9167`](https://github.com/nodejs/node/commit/a8a30a9167)] - **test**: put expected assert value in correct place (Jean-Francois Arseneau) [#23505](https://github.com/nodejs/node/pull/23505)
- [[`ac70ab0bc9`](https://github.com/nodejs/node/commit/ac70ab0bc9)] - **test**: fix argument order in assertions (Illescas, Ricardo) [#23506](https://github.com/nodejs/node/pull/23506)
- [[`7577de59b2`](https://github.com/nodejs/node/commit/7577de59b2)] - **test**: fix assertions args order in test/parallel/test-fs-chmod.js (Milton Sosa) [#23507](https://github.com/nodejs/node/pull/23507)
- [[`99d3c66d1c`](https://github.com/nodejs/node/commit/99d3c66d1c)] - **test**: fix strictEqual assertion arguments (Alejandro Oviedo Garcia) [#23508](https://github.com/nodejs/node/pull/23508)
- [[`c1b752355c`](https://github.com/nodejs/node/commit/c1b752355c)] - **test**: fix ordering of assertion values (Andrew MacCuaig)
- [[`894fe38814`](https://github.com/nodejs/node/commit/894fe38814)] - **test**: update function keywords to fat arrows (Robert Monks) [#23493](https://github.com/nodejs/node/pull/23493)
- [[`91ec3137fc`](https://github.com/nodejs/node/commit/91ec3137fc)] - **test**: reversed arguments in strictqual to reflect documentation (scabhi) [#23494](https://github.com/nodejs/node/pull/23494)
- [[`85059a94f6`](https://github.com/nodejs/node/commit/85059a94f6)] - **test**: modernized test to use arrow functions (Greg Goforth) [#23496](https://github.com/nodejs/node/pull/23496)
- [[`164c7daebc`](https://github.com/nodejs/node/commit/164c7daebc)] - **test**: use arrow functions in test-exception-handler (Jenna Zeigen) [#23498](https://github.com/nodejs/node/pull/23498)
- [[`1eed9caf53`](https://github.com/nodejs/node/commit/1eed9caf53)] - **test**: fix argument order in asserts (@CAYdenberg) [#23499](https://github.com/nodejs/node/pull/23499)
- [[`283decb45a`](https://github.com/nodejs/node/commit/283decb45a)] - **test**: modernizing test-dgram-listen-after-bind with arrow functions (chrisforrette) [#23500](https://github.com/nodejs/node/pull/23500)
- [[`189d74669c`](https://github.com/nodejs/node/commit/189d74669c)] - **test**: fix strictEqual argument order (Felix Schlenkrich) [#23490](https://github.com/nodejs/node/pull/23490)
- [[`f4ef1361fb`](https://github.com/nodejs/node/commit/f4ef1361fb)] - **test**: rename process.argv\[0\] to process.execPath, rename ex to err (Kayla Altepeter) [#23488](https://github.com/nodejs/node/pull/23488)
- [[`01bfb22441`](https://github.com/nodejs/node/commit/01bfb22441)] - **test**: fix assertion argument order (Carl Richmond) [#23489](https://github.com/nodejs/node/pull/23489)
- [[`0753c9e1b4`](https://github.com/nodejs/node/commit/0753c9e1b4)] - **test**: fix assertion order test-tls-server-verify (Carolina Pinzon) [#23549](https://github.com/nodejs/node/pull/23549)
- [[`860a097567`](https://github.com/nodejs/node/commit/860a097567)] - **test**: fix assertion order (Chris Nguyen) [#23533](https://github.com/nodejs/node/pull/23533)
- [[`925a3df275`](https://github.com/nodejs/node/commit/925a3df275)] - **test**: change to arrow functions in send-bad-arguments (Anna Zhao) [#23483](https://github.com/nodejs/node/pull/23483)
- [[`b6b3dde360`](https://github.com/nodejs/node/commit/b6b3dde360)] - **test**: removed unused variable (Michal Hynek) [#23481](https://github.com/nodejs/node/pull/23481)
- [[`d14b07b501`](https://github.com/nodejs/node/commit/d14b07b501)] - **test**: fix argument order for assert.strictEqual (Stacey) [#23485](https://github.com/nodejs/node/pull/23485)
- [[`c6f166bf21`](https://github.com/nodejs/node/commit/c6f166bf21)] - **test**: fix assert.strictEqual params order (Rock Hu) [#23480](https://github.com/nodejs/node/pull/23480)
- [[`5432957af0`](https://github.com/nodejs/node/commit/5432957af0)] - **test**: removed mustCallAsync from common and added inside testcase (Quinn Langille) [#23467](https://github.com/nodejs/node/pull/23467)
- [[`1c648fb161`](https://github.com/nodejs/node/commit/1c648fb161)] - **test**: remove unused "e" from catch in http2 test (Stephen Heitman) [#23476](https://github.com/nodejs/node/pull/23476)
- [[`012d848767`](https://github.com/nodejs/node/commit/012d848767)] - **test**: remove unused variable from catch (Paige Kato) [#23477](https://github.com/nodejs/node/pull/23477)
- [[`f6e1acf9ac`](https://github.com/nodejs/node/commit/f6e1acf9ac)] - **test**: inline common module boolean (ashleysimpson) [#23479](https://github.com/nodejs/node/pull/23479)
- [[`c23cc5763c`](https://github.com/nodejs/node/commit/c23cc5763c)] - **test**: swap the order arguments are passed to assert (Dylson Valente Neto) [#23580](https://github.com/nodejs/node/pull/23580)
- [[`8515a7d507`](https://github.com/nodejs/node/commit/8515a7d507)] - **test**: flip assertion arguments for make-callback/test.js (Tim Cheung) [#23470](https://github.com/nodejs/node/pull/23470)
- [[`1ae0aaf089`](https://github.com/nodejs/node/commit/1ae0aaf089)] - **test**: replace function with arrow function (Yitong) [#23474](https://github.com/nodejs/node/pull/23474)
- [[`762c2d03fc`](https://github.com/nodejs/node/commit/762c2d03fc)] - **test**: swap actual and expected in assertions (Yitong) [#23474](https://github.com/nodejs/node/pull/23474)
- [[`5b95bdf256`](https://github.com/nodejs/node/commit/5b95bdf256)] - **test**: correctly order assertion arguments (Emily Kolar) [#23473](https://github.com/nodejs/node/pull/23473)
- [[`51aa50c60c`](https://github.com/nodejs/node/commit/51aa50c60c)] - **test**: mark `test-http2-session-timeout` as flake on ARM (Refael Ackermann) [#23639](https://github.com/nodejs/node/pull/23639)
- [[`434ca4ff27`](https://github.com/nodejs/node/commit/434ca4ff27)] - **test**: update test-cluster-worker-events to use arrow functions (S. Everett Abbott) [#23469](https://github.com/nodejs/node/pull/23469)
- [[`3d284c856c`](https://github.com/nodejs/node/commit/3d284c856c)] - **test**: correct order for assert.strictEqual for inspector-helper test (Maggie Nolan) [#23468](https://github.com/nodejs/node/pull/23468)
- [[`6ca3876b6d`](https://github.com/nodejs/node/commit/6ca3876b6d)] - **test**: fix incorrect expectation order (Amie) [#23466](https://github.com/nodejs/node/pull/23466)
- [[`f007c8b513`](https://github.com/nodejs/node/commit/f007c8b513)] - **test**: remove unused e variable in catch statement (Denny Scott) [#23465](https://github.com/nodejs/node/pull/23465)
- [[`f6b59ced0b`](https://github.com/nodejs/node/commit/f6b59ced0b)] - **test**: correct assert test (Richard Markins) [#23463](https://github.com/nodejs/node/pull/23463)
- [[`6e0b984431`](https://github.com/nodejs/node/commit/6e0b984431)] - **test**: fix incorrect ordering of args in assert.strictEqual() (mdaum) [#23461](https://github.com/nodejs/node/pull/23461)
- [[`deb180e3c6`](https://github.com/nodejs/node/commit/deb180e3c6)] - **test**: swap assert.strictEqual args to actual, expected (epeden) [#23459](https://github.com/nodejs/node/pull/23459)
- [[`bc7c872e8f`](https://github.com/nodejs/node/commit/bc7c872e8f)] - **test**: fix assert.strictEqual argument order (andy addington) [#23457](https://github.com/nodejs/node/pull/23457)
- [[`55e23000ad`](https://github.com/nodejs/node/commit/55e23000ad)] - **test**: strictEqual correct order for http-information-processing test (Ivan Sieder) [#23456](https://github.com/nodejs/node/pull/23456)
- [[`ee3c23d568`](https://github.com/nodejs/node/commit/ee3c23d568)] - **test**: fix http local address test assertion (Danu Widatama) [#23451](https://github.com/nodejs/node/pull/23451)
- [[`7e38e9e580`](https://github.com/nodejs/node/commit/7e38e9e580)] - **test**: fix order of values in test assertions (Jared Haines) [#23450](https://github.com/nodejs/node/pull/23450)
- [[`0eba1b25e4`](https://github.com/nodejs/node/commit/0eba1b25e4)] - **test**: fix `assert.strictEqual` arguments in test/parallel/test-c-ares.js (jungkumseok) [#23448](https://github.com/nodejs/node/pull/23448)
- [[`3adfe99e5c`](https://github.com/nodejs/node/commit/3adfe99e5c)] - **test**: fix parameter order passed to strictEqual (Shannon) [#23577](https://github.com/nodejs/node/pull/23577)
- [[`f55b4f1cdf`](https://github.com/nodejs/node/commit/f55b4f1cdf)] - **test**: adding test coverage for SourceTextModule.evaluate (Kayla Altepeter) [#23595](https://github.com/nodejs/node/pull/23595)
- [[`9003c74f16`](https://github.com/nodejs/node/commit/9003c74f16)] - **test**: rename common.ddCommand() (Rich Trott) [#23411](https://github.com/nodejs/node/pull/23411)
- [[`1cdfd1ae03`](https://github.com/nodejs/node/commit/1cdfd1ae03)] - **test**: refactor common.ddCommand() (Rich Trott) [#23411](https://github.com/nodejs/node/pull/23411)
- [[`a29c8da033`](https://github.com/nodejs/node/commit/a29c8da033)] - **test**: move some gc tests back to parallel/, unmark flaky (Anna Henningsen) [#23356](https://github.com/nodejs/node/pull/23356)
- [[`8d091c2dd3`](https://github.com/nodejs/node/commit/8d091c2dd3)] - **test**: improve test-gc-http-client-onerror (Denys Otrishko) [#23196](https://github.com/nodejs/node/pull/23196)
- [[`6bea43c392`](https://github.com/nodejs/node/commit/6bea43c392)] - **test**: improve test-gc-http-client-connaborted (Denys Otrishko) [#23193](https://github.com/nodejs/node/pull/23193)
- [[`b2e173be48`](https://github.com/nodejs/node/commit/b2e173be48)] - **test**: fix assert.strictEqual argument order (et4891) [#23518](https://github.com/nodejs/node/pull/23518)
- [[`45151bc0d9`](https://github.com/nodejs/node/commit/45151bc0d9)] - **test**: fixing assertion value order (Joe Sepi) [#23574](https://github.com/nodejs/node/pull/23574)
- [[`2ea6546f73`](https://github.com/nodejs/node/commit/2ea6546f73)] - **test**: separate WPT console test from other test (Rich Trott) [#23340](https://github.com/nodejs/node/pull/23340)
- [[`d4f25ed656`](https://github.com/nodejs/node/commit/d4f25ed656)] - **test**: add WPT console-label-conversion test (Rich Trott) [#23340](https://github.com/nodejs/node/pull/23340)
- [[`9b9b3eb5c9`](https://github.com/nodejs/node/commit/9b9b3eb5c9)] - **test**: rename WPT console test (Rich Trott) [#23340](https://github.com/nodejs/node/pull/23340)
- [[`ad47ae7608`](https://github.com/nodejs/node/commit/ad47ae7608)] - **test**: add logging to test-worker-memory (Rich Trott) [#23418](https://github.com/nodejs/node/pull/23418)
- [[`6493e0165a`](https://github.com/nodejs/node/commit/6493e0165a)] - **test**: add test for a vm indexed property (conectado) [#23318](https://github.com/nodejs/node/pull/23318)
- [[`7b4c4db3ad`](https://github.com/nodejs/node/commit/7b4c4db3ad)] - **test**: fix compiler warning in doc/api/addons.md (Daniel Bevenius) [#23323](https://github.com/nodejs/node/pull/23323)
- [[`317b51caad`](https://github.com/nodejs/node/commit/317b51caad)] - **tls**: close StreamWrap and its stream correctly (Ouyang Yadong) [#23654](https://github.com/nodejs/node/pull/23654)
- [[`ed0a97a524`](https://github.com/nodejs/node/commit/ed0a97a524)] - **tls**: prevent multiple connection errors (cjihrig) [#23636](https://github.com/nodejs/node/pull/23636)
- [[`9487c42424`](https://github.com/nodejs/node/commit/9487c42424)] - **tls**: make StreamWrap work correctly in "drain" callback (Ouyang Yadong) [#23294](https://github.com/nodejs/node/pull/23294)
- [[`a67e04ee8a`](https://github.com/nodejs/node/commit/a67e04ee8a)] - **tools**: lint for unused catch bindings (cjihrig) [#24079](https://github.com/nodejs/node/pull/24079)
- [[`2152c07102`](https://github.com/nodejs/node/commit/2152c07102)] - **tools**: enable 80-char line length markdown linting (Rich Trott) [#24094](https://github.com/nodejs/node/pull/24094)
- [[`1538148d2a`](https://github.com/nodejs/node/commit/1538148d2a)] - **tools**: add script to lint first PR commit message (Richard Lau) [#24030](https://github.com/nodejs/node/pull/24030)
- [[`fe75543af6`](https://github.com/nodejs/node/commit/fe75543af6)] - **tools**: update alternative docs versions (Richard Lau) [#23980](https://github.com/nodejs/node/pull/23980)
- [[`c5d909886a`](https://github.com/nodejs/node/commit/c5d909886a)] - **tools**: update ESLint to 5.8.0 (cjihrig) [#23904](https://github.com/nodejs/node/pull/23904)
- [[`7670bc5917`](https://github.com/nodejs/node/commit/7670bc5917)] - **tools**: clarify commit message linting (Rich Trott) [#23742](https://github.com/nodejs/node/pull/23742)
- [[`36bd9a98d7`](https://github.com/nodejs/node/commit/36bd9a98d7)] - **tools**: do not lint commit message if var undefined (Rich Trott) [#23725](https://github.com/nodejs/node/pull/23725)
- [[`ca1c42f3c7`](https://github.com/nodejs/node/commit/ca1c42f3c7)] - **tools**: prefer filter to remove empty strings (Sakthipriyan Vairamani (thefourtheye)) [#23727](https://github.com/nodejs/node/pull/23727)
- [[`9182ad38f5`](https://github.com/nodejs/node/commit/9182ad38f5)] - **tools**: update ESLint to 5.7.0 (cjihrig) [#23629](https://github.com/nodejs/node/pull/23629)
- [[`4bcbc86230`](https://github.com/nodejs/node/commit/4bcbc86230)] - **tools**: update node-lint-md-cli-rollup (Rich Trott) [#23358](https://github.com/nodejs/node/pull/23358)
- [[`cae703ce33`](https://github.com/nodejs/node/commit/cae703ce33)] - **tools,icu**: read full ICU version info from file (Refael Ackermann) [#23269](https://github.com/nodejs/node/pull/23269)
- [[`e1f7924b42`](https://github.com/nodejs/node/commit/e1f7924b42)] - **tools,test**: add list of slow tests (Refael Ackermann) [#23251](https://github.com/nodejs/node/pull/23251)
- [[`8e08a27edf`](https://github.com/nodejs/node/commit/8e08a27edf)] - **tools,test**: cleanup and dedup code (Refael Ackermann) [#23251](https://github.com/nodejs/node/pull/23251)
- [[`1b8b5e525c`](https://github.com/nodejs/node/commit/1b8b5e525c)] - **trace_events**: destroy platform before tracing (Ali Ijaz Sheikh) [#22938](https://github.com/nodejs/node/pull/22938)
- [[`90014b6c3c`](https://github.com/nodejs/node/commit/90014b6c3c)] - **util**: handle null prototype on inspect (Anto Aravinth) [#22331](https://github.com/nodejs/node/pull/22331)
- [[`80ab31eb49`](https://github.com/nodejs/node/commit/80ab31eb49)] - **v8_prof_polyfill**: remove unused catch bindings (cjihrig) [#24079](https://github.com/nodejs/node/pull/24079)
- [[`9503365d20`](https://github.com/nodejs/node/commit/9503365d20)] - **vm**: clarify timeout option in vm (Vladimir de Turckheim) [#23512](https://github.com/nodejs/node/pull/23512)
- [[`e9c7243909`](https://github.com/nodejs/node/commit/e9c7243909)] - **vm**: pass parsing_context to ScriptCompiler::CompileFunctionInContext (Dara Hayes) [#23206](https://github.com/nodejs/node/pull/23206)
- [[`a5dd25fc1b`](https://github.com/nodejs/node/commit/a5dd25fc1b)] - **worker**: remove delete MessagePort.prototype.hasRef (James Traver) [#23471](https://github.com/nodejs/node/pull/23471)
- [[`bae674ee5d`](https://github.com/nodejs/node/commit/bae674ee5d)] - **zlib**: refactor zlib internals (Anna Henningsen) [#23360](https://github.com/nodejs/node/pull/23360)
- [[`0763d256dc`](https://github.com/nodejs/node/commit/0763d256dc)] - **zlib**: generate error code names in C++ (Anna Henningsen) [#23413](https://github.com/nodejs/node/pull/23413)

Windows 32-bit Installer: https://nodejs.org/dist/v10.14.2/node-v10.14.2-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v10.14.2/node-v10.14.2-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v10.14.2/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v10.14.2/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v10.14.2/node-v10.14.2.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v10.14.2/node-v10.14.2-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v10.14.2/node-v10.14.2-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v10.14.2/node-v10.14.2-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v10.14.2/node-v10.14.2-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v10.14.2/node-v10.14.2-aix-ppc64.tar.gz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v10.14.2/node-v10.14.2-sunos-x64.tar.xz \
ARMv6 32-bit Binary: https://nodejs.org/dist/v10.14.2/node-v10.14.2-linux-armv6l.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v10.14.2/node-v10.14.2-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v10.14.2/node-v10.14.2-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v10.14.2/node-v10.14.2.tar.gz \
Other release files: https://nodejs.org/dist/v10.14.2/ \
Documentation: https://nodejs.org/docs/v10.14.2/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

c5df726d847b7b2744b7a2cd7dcb687a5dbac4ab5920d6e93cc8a1b85b7055d8  node-v10.14.2-aix-ppc64.tar.gz
5306da5db576d9c984167b4693600a2e3074cc5a701961279837753fa2139baa  node-v10.14.2-darwin-x64.tar.gz
a9fcff3ccd047292cdc6ebfd326693f5315eae07d88ac706e9d5ee649011f9ae  node-v10.14.2-darwin-x64.tar.xz
f390eed5832408b973a183401f2864d6ea463cf2d16e28bedf72daceedfb51f0  node-v10.14.2-headers.tar.gz
244b08dd86dbec0713ef0e79a6e1f9b4276483b2418c7c04cd41b7926e26c4ae  node-v10.14.2-headers.tar.xz
23c82e8a3569bb463a0ed40602195c4280041a30a68858fd84ede7cd532e555e  node-v10.14.2-linux-arm64.tar.gz
8493b30c99d697b24fbaa5bfb3b43108ab3e334ab674188b7b7982fef903aa04  node-v10.14.2-linux-arm64.tar.xz
c4bc11ec655040e32cba4214d80cd5001850b9dfa428cd29d0d31a6ecf30a869  node-v10.14.2-linux-armv6l.tar.gz
be06a84638190f8097d9682c2ef531d699110939e99288c0cb3f03f9ac0d7b82  node-v10.14.2-linux-armv6l.tar.xz
ab04028c0d4c94896673f241e148729eba67df2a295ee1b90c28015621e99e61  node-v10.14.2-linux-armv7l.tar.gz
c99c6ecf5e8d30ea63a416912f187a8b76f6988858ace2a67066f81478564f4a  node-v10.14.2-linux-armv7l.tar.xz
e7f10ba28883ac206684c96cf9ccba6d6d2d39f8b377fbcc4cf28943ee96e57f  node-v10.14.2-linux-ppc64le.tar.gz
2664d622b7cc4a43e14ee1990a8abaa534d41aa25ee35c84d547618585be65e9  node-v10.14.2-linux-ppc64le.tar.xz
71408583c53a3ef12f8d0c4df4654dbaa1d6ce03c6127b5bdaeec7c21ef79cf8  node-v10.14.2-linux-s390x.tar.gz
8276357c908ded66bd4d5aa4f338febe43f97d1b46afd2ecf8e0181be51304f2  node-v10.14.2-linux-s390x.tar.xz
0552b0f6fc9c0cd078bbb794c876e2546ba63a1dfcf8e3c206387936696ca128  node-v10.14.2-linux-x64.tar.gz
e43de13bf7bee440a106a844c1bc3a2adb8829fd58b857702c8f1838fdd02a2a  node-v10.14.2-linux-x64.tar.xz
020fb3586b5209bb21cf26924ad9a4b32af94bb0ea5c2ff39117de706ede3279  node-v10.14.2.pkg
f604e37713e39032bc5093b5acfcadf419b03a4ab9e5f264e60f4c2b1d2b4da9  node-v10.14.2-sunos-x64.tar.gz
81e8232253d620c57563ea0bea1a7bbc904a95dfb71607a7bba8cf3bef11e0a3  node-v10.14.2-sunos-x64.tar.xz
5b8a55d829d951d2a5ccefd4ffe4f9154673ebc621fd6c676bea09bba95cf96b  node-v10.14.2.tar.gz
ae1aec55e3ca8f4debfb8c07489209fd84d3c47b26e6672e1cf7a7820f95acd3  node-v10.14.2.tar.xz
54d002af81c7e1a0c005949466ef10945e507f8e71248682e8786e86eb40bcbf  node-v10.14.2-win-x64.7z
45841fe5ffe87378c748dcb9799507f6192c34117409b2c6c18480d112a337de  node-v10.14.2-win-x64.zip
22453b48b9cf0e33d4759e700c1467407a41d06376453d39babf7003287c5602  node-v10.14.2-win-x86.7z
c1b56ba1e8b086b315d3c7902dacf8b7bf6a62ce059e869be659287c6ba30b6d  node-v10.14.2-win-x86.zip
1e19793063ea30bc0c24fe19502e4b99453044bdf1ce57b6fb351dbbd5da7933  node-v10.14.2-x64.msi
fc13ee83911b75ca4457ed99cfc22c3aa469f7fb97a0c3abbd1211af8b99c875  node-v10.14.2-x86.msi
252164b94c98aec7082414d20d904770fa4677d6b1f7a2e1a80cc34cbb100381  win-x64/node.exe
24bb1aef77abf1251081acdb4e32bcd240b34ca87fcc63bc86bfec06b2da2018  win-x64/node.lib
bf0113293be83c9f7e1281a6b452f68bddbf580ee3144751d36f47c87dcf5a3a  win-x64/node_pdb.7z
5954ca15a53478008348a61a468c410572c6398f353840be215afe6b11e7346e  win-x64/node_pdb.zip
effe8f9383cb16ad98afd685bbf73967d79e320987bf2ffa86ac9fea8e124617  win-x86/node.exe
dbfe313a534fbd3e96cb139df5a35d5489e654b0e199e89d0c4fcf0f049c8525  win-x86/node.lib
02ad5f235343879c567248c6e84490b06ca5c1e439e8e05de325ff89300e0552  win-x86/node_pdb.7z
aaa1affe2f5af673e05d7fa21d4c6cd6dfc4b794609d376a1527859fcd6108c2  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQEzBAEBCAAdFiEEDv/hvO/ZyE49CYFSkzsB9AtcqUYFAlwQIGsACgkQkzsB9Atc
qUaWJQf9GWOyy+HVscbt69pxft3Kcy8Uj8pCP8FdfIdbRsewq5pMpW6bYIXIbVtu
VZkr4/bA83rC9o+NRYZSHniYAmeMTkEUZ10wzN27Tsp9zJxeAzUH5GJFAHD2Aklo
i7uiSoXQFBFO/KxVw3JTzwLshb6fTNCntZiGNmHk/ux8edofp4u2JdvLXu/mvb6F
ZB65o51gIp0/0Yrdzle8YKanVrwDk7iErQ1SXCiWopgEMVJSwwgZkXjkTaGZupwO
XdwNntA1bzFJJI2AxzSRCRGmKZJcZn5UR5E8J+UCx2NRPoVogsNpRuoH6CtzfAHm
RH8cyzq67YCBQ/zqDSbD5FdNtbJFYw==
=0Jok
-----END PGP SIGNATURE-----

```
