---
date: '2019-11-19T10:53:56.889Z'
category: release
title: Node v12.13.1 (LTS)
layout: blog-post
author: <PERSON><PERSON><PERSON>
---

### Notable changes

- Experimental support for building Node.js with Python 3 is improved.
- ICU time zone data is updated to version 2019c. This fixes the date offset
  in Brazil.

### Commits

- [[`56be32d22d`](https://github.com/nodejs/node/commit/56be32d22d)] - **async_hooks**: only emit `after` for AsyncResource if stack not empty (<PERSON>) [#30087](https://github.com/nodejs/node/pull/30087)
- [[`e16e3d5b90`](https://github.com/nodejs/node/commit/e16e3d5b90)] - **benchmark**: remove double word "then" in comments (<PERSON>) [#29823](https://github.com/nodejs/node/pull/29823)
- [[`dcdb96c7bb`](https://github.com/nodejs/node/commit/dcdb96c7bb)] - **benchmark**: add benchmark for vm.createContext (<PERSON><PERSON>) [#29845](https://github.com/nodejs/node/pull/29845)
- [[`680e9cc7e1`](https://github.com/nodejs/node/commit/680e9cc7e1)] - **buffer**: improve performance caused by primordials (Jizu Sun) [#30235](https://github.com/nodejs/node/pull/30235)
- [[`bcd2238b3e`](https://github.com/nodejs/node/commit/bcd2238b3e)] - **build**: add workaround for WSL (gengjiawen) [#30221](https://github.com/nodejs/node/pull/30221)
- [[`c5d312f821`](https://github.com/nodejs/node/commit/c5d312f821)] - **build**: find Python syntax errors in dependencies (Christian Clauss) [#30143](https://github.com/nodejs/node/pull/30143)
- [[`468f203809`](https://github.com/nodejs/node/commit/468f203809)] - **build**: fix pkg-config search for libnghttp2 (Ben Noordhuis) [#30145](https://github.com/nodejs/node/pull/30145)
- [[`0415dd7cb3`](https://github.com/nodejs/node/commit/0415dd7cb3)] - **build**: python3 support for configure (Rod Vagg) [#30047](https://github.com/nodejs/node/pull/30047)
- [[`032c23d360`](https://github.com/nodejs/node/commit/032c23d360)] - **build**: make linter failures fail `test-doc` target (Richard Lau) [#30012](https://github.com/nodejs/node/pull/30012)
- [[`a86648c8d2`](https://github.com/nodejs/node/commit/a86648c8d2)] - **build**: log the found compiler version if too old (Richard Lau) [#30028](https://github.com/nodejs/node/pull/30028)
- [[`02f6e5cc40`](https://github.com/nodejs/node/commit/02f6e5cc40)] - **build**: fix version checks in configure.py (Michaël Zasso) [#29965](https://github.com/nodejs/node/pull/29965)
- [[`a1adce1b4f`](https://github.com/nodejs/node/commit/a1adce1b4f)] - **build**: build benchmark addons like test addons (Richard Lau) [#29995](https://github.com/nodejs/node/pull/29995)
- [[`735ec1bf96`](https://github.com/nodejs/node/commit/735ec1bf96)] - **build**: fix version checks in gyp files (Ben Noordhuis) [#29931](https://github.com/nodejs/node/pull/29931)
- [[`8da83e8c24`](https://github.com/nodejs/node/commit/8da83e8c24)] - **build**: always use strings for compiler version in gyp files (Michaël Zasso) [#29897](https://github.com/nodejs/node/pull/29897)
- [[`b7bdfd346c`](https://github.com/nodejs/node/commit/b7bdfd346c)] - **crypto**: guard with OPENSSL_NO_GOST (Shelley Vohr) [#30050](https://github.com/nodejs/node/pull/30050)
- [[`e175d0beb6`](https://github.com/nodejs/node/commit/e175d0beb6)] - **crypto**: reject public keys properly (Tobias Nießen) [#29913](https://github.com/nodejs/node/pull/29913)
- [[`b1529c6bc2`](https://github.com/nodejs/node/commit/b1529c6bc2)] - **deps**: V8: cherry-pick a7dffcd767be (Christian Clauss) [#30218](https://github.com/nodejs/node/pull/30218)
- [[`6bc7a6db0e`](https://github.com/nodejs/node/commit/6bc7a6db0e)] - **deps**: V8: cherry-pick e5dbc95 (Gabriel Schulhof) [#30130](https://github.com/nodejs/node/pull/30130)
- [[`b88314f735`](https://github.com/nodejs/node/commit/b88314f735)] - **deps**: update npm to 6.12.1 (Michael Perrotte) [#30164](https://github.com/nodejs/node/pull/30164)
- [[`ce49a412ef`](https://github.com/nodejs/node/commit/ce49a412ef)] - **deps**: V8: cherry-pick c721203 (Michaël Zasso) [#30065](https://github.com/nodejs/node/pull/30065)
- [[`d2756fd14d`](https://github.com/nodejs/node/commit/d2756fd14d)] - **deps**: V8: cherry-pick ed40ab1 (Michaël Zasso) [#30064](https://github.com/nodejs/node/pull/30064)
- [[`58c585e3ed`](https://github.com/nodejs/node/commit/58c585e3ed)] - **deps**: npm: patch support for 13.x (Jordan Harband) [#30079](https://github.com/nodejs/node/pull/30079)
- [[`2764567f90`](https://github.com/nodejs/node/commit/2764567f90)] - **deps**: upgrade to libuv 1.33.1 (Colin Ihrig) [#29996](https://github.com/nodejs/node/pull/29996)
- [[`33bd1281fc`](https://github.com/nodejs/node/commit/33bd1281fc)] - **doc**: add missing hash for header link (Nick Schonning) [#30188](https://github.com/nodejs/node/pull/30188)
- [[`b159b91798`](https://github.com/nodejs/node/commit/b159b91798)] - **doc**: linkify `.setupMaster()` in cluster doc (Trivikram Kamat) [#30204](https://github.com/nodejs/node/pull/30204)
- [[`9c4a9e7337`](https://github.com/nodejs/node/commit/9c4a9e7337)] - **doc**: explain http2 aborted event callback (dev-313) [#30179](https://github.com/nodejs/node/pull/30179)
- [[`d7bfc6c987`](https://github.com/nodejs/node/commit/d7bfc6c987)] - **doc**: linkify `.fork()` in cluster documentation (Anna Henningsen) [#30163](https://github.com/nodejs/node/pull/30163)
- [[`a71f210206`](https://github.com/nodejs/node/commit/a71f210206)] - **doc**: update AUTHORS list (Michaël Zasso) [#30142](https://github.com/nodejs/node/pull/30142)
- [[`7b5047454b`](https://github.com/nodejs/node/commit/7b5047454b)] - **doc**: improve doc Http2Session:Timeout (dev-313) [#30161](https://github.com/nodejs/node/pull/30161)
- [[`0efe9a0c97`](https://github.com/nodejs/node/commit/0efe9a0c97)] - **doc**: move inactive Collaborators to emeriti (Rich Trott) [#30177](https://github.com/nodejs/node/pull/30177)
- [[`98d31da342`](https://github.com/nodejs/node/commit/98d31da342)] - **doc**: add options description for send APIs (dev-313) [#29868](https://github.com/nodejs/node/pull/29868)
- [[`d0f5bc1aa7`](https://github.com/nodejs/node/commit/d0f5bc1aa7)] - **doc**: fix an error in resolution algorithm steps (Alex Zherdev) [#29940](https://github.com/nodejs/node/pull/29940)
- [[`28db99932a`](https://github.com/nodejs/node/commit/28db99932a)] - **doc**: remove incorrect and outdated example (Tobias Nießen) [#30138](https://github.com/nodejs/node/pull/30138)
- [[`c2108d4919`](https://github.com/nodejs/node/commit/c2108d4919)] - **doc**: adjust code sample for stream.finished (Cotton Hou) [#29983](https://github.com/nodejs/node/pull/29983)
- [[`2ac76e3055`](https://github.com/nodejs/node/commit/2ac76e3055)] - **doc**: remove "it is important to" phrasing (Rich Trott) [#30108](https://github.com/nodejs/node/pull/30108)
- [[`ec992878e8`](https://github.com/nodejs/node/commit/ec992878e8)] - **doc**: revise os.md (Rich Trott) [#30102](https://github.com/nodejs/node/pull/30102)
- [[`a56e78c8c8`](https://github.com/nodejs/node/commit/a56e78c8c8)] - **doc**: delete "a number of" things in the docs (Rich Trott) [#30103](https://github.com/nodejs/node/pull/30103)
- [[`ee954d5570`](https://github.com/nodejs/node/commit/ee954d5570)] - **doc**: remove dashes (Rich Trott) [#30101](https://github.com/nodejs/node/pull/30101)
- [[`c4c8e01af1`](https://github.com/nodejs/node/commit/c4c8e01af1)] - **doc**: add legendecas to collaborators (legendecas) [#30115](https://github.com/nodejs/node/pull/30115)
- [[`22e10fd15a`](https://github.com/nodejs/node/commit/22e10fd15a)] - **doc**: --enable-source-maps and prepareStackTrace are incompatible (Benjamin Coe) [#30046](https://github.com/nodejs/node/pull/30046)
- [[`870c320f31`](https://github.com/nodejs/node/commit/870c320f31)] - **doc**: join parts of disrupt section in cli.md (vsemozhetbyt) [#30038](https://github.com/nodejs/node/pull/30038)
- [[`8df5bdbd66`](https://github.com/nodejs/node/commit/8df5bdbd66)] - **doc**: update collaborator email address (Minwoo Jung) [#30007](https://github.com/nodejs/node/pull/30007)
- [[`d9b5508fc8`](https://github.com/nodejs/node/commit/d9b5508fc8)] - **doc**: fix tls version typo (akitsu-sanae) [#29984](https://github.com/nodejs/node/pull/29984)
- [[`5616f22839`](https://github.com/nodejs/node/commit/5616f22839)] - **doc**: clarify readable.unshift null/EOF (Robert Nagy) [#29950](https://github.com/nodejs/node/pull/29950)
- [[`b57fe3b370`](https://github.com/nodejs/node/commit/b57fe3b370)] - **doc**: remove unused Markdown reference links (Nick Schonning) [#29961](https://github.com/nodejs/node/pull/29961)
- [[`12f24542b8`](https://github.com/nodejs/node/commit/12f24542b8)] - **doc**: re-enable passing remark-lint rule (Nick Schonning) [#29961](https://github.com/nodejs/node/pull/29961)
- [[`c0cbfae0e3`](https://github.com/nodejs/node/commit/c0cbfae0e3)] - **doc**: add server header into the discarded list of http message.headers (Huachao Mao) [#29962](https://github.com/nodejs/node/pull/29962)
- [[`a23b5cbf61`](https://github.com/nodejs/node/commit/a23b5cbf61)] - **doc**: prepare miscellaneous docs for new markdown lint rules (Rich Trott) [#29963](https://github.com/nodejs/node/pull/29963)
- [[`c66bc20bbf`](https://github.com/nodejs/node/commit/c66bc20bbf)] - **doc**: fix some recent nits in fs.md (vsemozhetbyt) [#29906](https://github.com/nodejs/node/pull/29906)
- [[`1fefd7fddc`](https://github.com/nodejs/node/commit/1fefd7fddc)] - **doc**: fs dir modifications may not be reflected by dir.read (Anna Henningsen) [#29893](https://github.com/nodejs/node/pull/29893)
- [[`66c6818473`](https://github.com/nodejs/node/commit/66c6818473)] - **doc,meta**: prefer aliases and stubs over Runtime Deprecations (Rich Trott) [#30153](https://github.com/nodejs/node/pull/30153)
- [[`5ade490505`](https://github.com/nodejs/node/commit/5ade490505)] - **doc,meta**: reduce npm PR wait period to one week (Rich Trott) [#29922](https://github.com/nodejs/node/pull/29922)
- [[`0ec63ee27a`](https://github.com/nodejs/node/commit/0ec63ee27a)] - **doc,n-api**: sort bottom-of-the-page references (Gabriel Schulhof) [#30124](https://github.com/nodejs/node/pull/30124)
- [[`8a333a4519`](https://github.com/nodejs/node/commit/8a333a4519)] - **domain**: do not import util for a simple type check (Ruben Bridgewater) [#29825](https://github.com/nodejs/node/pull/29825)
- [[`94ac44f3fc`](https://github.com/nodejs/node/commit/94ac44f3fc)] - **esm**: modify resolution order for specifier flag (Myles Borins) [#29974](https://github.com/nodejs/node/pull/29974)
- [[`216e200fa9`](https://github.com/nodejs/node/commit/216e200fa9)] - **fs**: buffer dir entries in opendir() (Anna Henningsen) [#29893](https://github.com/nodejs/node/pull/29893)
- [[`5959023b76`](https://github.com/nodejs/node/commit/5959023b76)] - **http2**: fix file close error condition at respondWithFd (Anna Henningsen) [#29884](https://github.com/nodejs/node/pull/29884)
- [[`4277066afd`](https://github.com/nodejs/node/commit/4277066afd)] - **inspector**: turn platform tasks that outlive Agent into no-ops (Anna Henningsen) [#30031](https://github.com/nodejs/node/pull/30031)
- [[`b0837fead3`](https://github.com/nodejs/node/commit/b0837fead3)] - **meta**: use contact_links instead of issue templates (Michaël Zasso) [#30172](https://github.com/nodejs/node/pull/30172)
- [[`2695f822bc`](https://github.com/nodejs/node/commit/2695f822bc)] - **module**: warn on require of .js inside type: module (Guy Bedford) [#29909](https://github.com/nodejs/node/pull/29909)
- [[`ee3c3ad0f5`](https://github.com/nodejs/node/commit/ee3c3ad0f5)] - **n-api,doc**: add info about building n-api addons (Jim Schlight) [#30032](https://github.com/nodejs/node/pull/30032)
- [[`da58301054`](https://github.com/nodejs/node/commit/da58301054)] - **net**: treat ENOTCONN at shutdown as success (Anna Henningsen) [#29912](https://github.com/nodejs/node/pull/29912)
- [[`62bc80c906`](https://github.com/nodejs/node/commit/62bc80c906)] - **process**: add lineLength to source-map-cache (Benjamin Coe) [#29863](https://github.com/nodejs/node/pull/29863)
- [[`ab03c29587`](https://github.com/nodejs/node/commit/ab03c29587)] - **src**: isolate-\>Dispose() order consistency (Shelley Vohr) [#30181](https://github.com/nodejs/node/pull/30181)
- [[`c52b292adf`](https://github.com/nodejs/node/commit/c52b292adf)] - **src**: change env.h includes for forward declarations (Alexandre Ferrando) [#30133](https://github.com/nodejs/node/pull/30133)
- [[`b215b1665a`](https://github.com/nodejs/node/commit/b215b1665a)] - **src**: split up InitializeContext (Shelley Vohr) [#30067](https://github.com/nodejs/node/pull/30067)
- [[`d586070388`](https://github.com/nodejs/node/commit/d586070388)] - **src**: allow inspector without v8 platform (Shelley Vohr) [#30049](https://github.com/nodejs/node/pull/30049)
- [[`f6655b41fa`](https://github.com/nodejs/node/commit/f6655b41fa)] - **src**: remove unnecessary std::endl usage (Daniel Bevenius) [#30003](https://github.com/nodejs/node/pull/30003)
- [[`abfac9640e`](https://github.com/nodejs/node/commit/abfac9640e)] - **src**: make implementing CancelPendingDelayedTasks for platform optional (Anna Henningsen) [#30034](https://github.com/nodejs/node/pull/30034)
- [[`693bf73b06`](https://github.com/nodejs/node/commit/693bf73b06)] - **src**: expose ListNode\<T\>::prev\_ on postmortem metadata (legendecas) [#30027](https://github.com/nodejs/node/pull/30027)
- [[`4b57088c25`](https://github.com/nodejs/node/commit/4b57088c25)] - **src**: fewer uses of NODE_USE_V8_PLATFORM (Shelley Vohr) [#30029](https://github.com/nodejs/node/pull/30029)
- [[`6269a3c92a`](https://github.com/nodejs/node/commit/6269a3c92a)] - **src**: remove unused iomanip include (Daniel Bevenius) [#30004](https://github.com/nodejs/node/pull/30004)
- [[`aa0aacbba9`](https://github.com/nodejs/node/commit/aa0aacbba9)] - **src**: initialize openssl only once (Sam Roberts) [#29999](https://github.com/nodejs/node/pull/29999)
- [[`45c5ad7922`](https://github.com/nodejs/node/commit/45c5ad7922)] - **src**: refine maps parsing for large pages (Gabriel Schulhof) [#29973](https://github.com/nodejs/node/pull/29973)
- [[`aac2476346`](https://github.com/nodejs/node/commit/aac2476346)] - **src**: render N-API weak callbacks as cleanup hooks (Gabriel Schulhof) [#28428](https://github.com/nodejs/node/pull/28428)
- [[`f3115c4d62`](https://github.com/nodejs/node/commit/f3115c4d62)] - **src**: fix largepages regression (Gabriel Schulhof) [#29914](https://github.com/nodejs/node/pull/29914)
- [[`ddbf150edb`](https://github.com/nodejs/node/commit/ddbf150edb)] - **src**: remove unused using declarations in worker.cc (Daniel Bevenius) [#29883](https://github.com/nodejs/node/pull/29883)
- [[`8a31136a95`](https://github.com/nodejs/node/commit/8a31136a95)] - **stream**: extract Readable.from in its own file (Matteo Collina) [#30140](https://github.com/nodejs/node/pull/30140)
- [[`21a43bd2fd`](https://github.com/nodejs/node/commit/21a43bd2fd)] - **stream**: simplify uint8ArrayToBuffer helper (Luigi Pinca) [#30041](https://github.com/nodejs/node/pull/30041)
- [[`ae390393b6`](https://github.com/nodejs/node/commit/ae390393b6)] - **stream**: remove dead code (Luigi Pinca) [#30041](https://github.com/nodejs/node/pull/30041)
- [[`56e986aa23`](https://github.com/nodejs/node/commit/56e986aa23)] - **test**: do not run release-npm test without crypto (Michaël Zasso) [#30265](https://github.com/nodejs/node/pull/30265)
- [[`d96e8b662e`](https://github.com/nodejs/node/commit/d96e8b662e)] - **test**: use arrow functions for callbacks (Minuk Park) [#30069](https://github.com/nodejs/node/pull/30069)
- [[`00dab3495d`](https://github.com/nodejs/node/commit/00dab3495d)] - **test**: verify npm compatibility with releases (Michaël Zasso) [#30082](https://github.com/nodejs/node/pull/30082)
- [[`ecf6ae89f4`](https://github.com/nodejs/node/commit/ecf6ae89f4)] - **test**: expand Worker test for non-shared ArrayBuffer (Anna Henningsen) [#30044](https://github.com/nodejs/node/pull/30044)
- [[`2ebd1a0d3f`](https://github.com/nodejs/node/commit/2ebd1a0d3f)] - **test**: fix test runner for Python 3 on Windows (Michaël Zasso) [#30023](https://github.com/nodejs/node/pull/30023)
- [[`9fed62f7cb`](https://github.com/nodejs/node/commit/9fed62f7cb)] - **test**: remove common.skipIfInspectorEnabled() (Rich Trott) [#29993](https://github.com/nodejs/node/pull/29993)
- [[`3e39909022`](https://github.com/nodejs/node/commit/3e39909022)] - **test**: add cb error test for fs.close() (Matteo Rossi) [#29970](https://github.com/nodejs/node/pull/29970)
- [[`b93c8a77a3`](https://github.com/nodejs/node/commit/b93c8a77a3)] - **test**: fix flaky doctool and test (Rich Trott) [#29979](https://github.com/nodejs/node/pull/29979)
- [[`aec8e77ae1`](https://github.com/nodejs/node/commit/aec8e77ae1)] - **test**: fix fs benchmark test (Rich Trott) [#29967](https://github.com/nodejs/node/pull/29967)
- [[`b9fd18f9fb`](https://github.com/nodejs/node/commit/b9fd18f9fb)] - **tools**: pull xcode_emulation.py from node-gyp (Christian Clauss) [#30272](https://github.com/nodejs/node/pull/30272)
- [[`2810f1aec3`](https://github.com/nodejs/node/commit/2810f1aec3)] - **tools**: update tzdata to 2019c (Myles Borins) [#30478](https://github.com/nodejs/node/pull/30478)
- [[`41d1f166bc`](https://github.com/nodejs/node/commit/41d1f166bc)] - **tools**: fix Python 3 deprecation warning in test.py (Loris Zinsou) [#30208](https://github.com/nodejs/node/pull/30208)
- [[`b6546736a0`](https://github.com/nodejs/node/commit/b6546736a0)] - **tools**: fix Python 3 syntax error in mac_tool.py (Christian Clauss) [#30146](https://github.com/nodejs/node/pull/30146)
- [[`87cb6b2418`](https://github.com/nodejs/node/commit/87cb6b2418)] - **tools**: use print() function in buildbot_run.py (Christian Clauss) [#30148](https://github.com/nodejs/node/pull/30148)
- [[`309c395aba`](https://github.com/nodejs/node/commit/309c395aba)] - **tools**: undefined name opts -\> args in gyptest.py (Christian Clauss) [#30144](https://github.com/nodejs/node/pull/30144)
- [[`df0fbf2e46`](https://github.com/nodejs/node/commit/df0fbf2e46)] - **tools**: git rm -r tools/v8_gypfiles/broken (Christian Clauss) [#30149](https://github.com/nodejs/node/pull/30149)
- [[`375f349760`](https://github.com/nodejs/node/commit/375f349760)] - **tools**: update ESLint to 6.6.0 (Colin Ihrig) [#30123](https://github.com/nodejs/node/pull/30123)
- [[`0b6fb3d1db`](https://github.com/nodejs/node/commit/0b6fb3d1db)] - **tools**: doc: improve async workflow of generate.js (Theotime Poisseau) [#30106](https://github.com/nodejs/node/pull/30106)
- [[`8d030131a4`](https://github.com/nodejs/node/commit/8d030131a4)] - **tools**: fix test runner in presence of NODE_REPL_EXTERNAL_MODULE (Gus Caplan) [#29956](https://github.com/nodejs/node/pull/29956)
- [[`59033f618a`](https://github.com/nodejs/node/commit/59033f618a)] - **tools**: fix GYP MSVS solution generator for Python 3 (Michaël Zasso) [#29897](https://github.com/nodejs/node/pull/29897)
- [[`41430bea3c`](https://github.com/nodejs/node/commit/41430bea3c)] - **tools**: port Python 3 compat patches from node-gyp to gyp (Michaël Zasso) [#29897](https://github.com/nodejs/node/pull/29897)

Windows 32-bit Installer: https://nodejs.org/dist/v12.13.1/node-v12.13.1-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v12.13.1/node-v12.13.1-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v12.13.1/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v12.13.1/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v12.13.1/node-v12.13.1.pkg \
macOS 64-bit Binary: https://nodejs.org/dist/v12.13.1/node-v12.13.1-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v12.13.1/node-v12.13.1-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v12.13.1/node-v12.13.1-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v12.13.1/node-v12.13.1-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v12.13.1/node-v12.13.1-aix-ppc64.tar.gz \
SmartOS 64-bit Binary: https://nodejs.org/dist/v12.13.1/node-v12.13.1-sunos-x64.tar.xz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v12.13.1/node-v12.13.1-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v12.13.1/node-v12.13.1-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v12.13.1/node-v12.13.1.tar.gz \
Other release files: https://nodejs.org/dist/v12.13.1/ \
Documentation: https://nodejs.org/docs/v12.13.1/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

81a5eed8c2215816aad4551683189a48953a11cb669f2e942a903e2cd5a5e4d7  node-v12.13.1-aix-ppc64.tar.gz
12d14c7fbd98876a163a2b7e0aeb13657dc3e967e993efaf2dcacbe475a285e8  node-v12.13.1-darwin-x64.tar.gz
70104b8c7f21d89886da907781330af7795835992f97771b9a78a8e92ee4b0d7  node-v12.13.1-darwin-x64.tar.xz
1f67d63627197ffa2263d90a63167d86ff27afcbe5cc100477e0361170aefc7e  node-v12.13.1-headers.tar.gz
7b611d8574a280de2149aa130180a85c9760e33f6b7f806b9ba15ed643339413  node-v12.13.1-headers.tar.xz
a1c183f175344f492188543fa789576ed266b7542763ad07d880f9819d9f23d3  node-v12.13.1-linux-arm64.tar.gz
3aef0178a8ab74c8e5c133e23d1896e53ed5c273415d165a0e72e005f5467cba  node-v12.13.1-linux-arm64.tar.xz
c078a009d21fc54e8d810af7d5b7952a7679891231be42caaca2d12a8cf446c2  node-v12.13.1-linux-armv7l.tar.gz
c085c4fdf9ef8b74515eb44c211ec51f145d1085195bb9568fa2a87b37e1e8cf  node-v12.13.1-linux-armv7l.tar.xz
07f2f97615ef03b60140097cdfc627b0e97d408153974ccf8699847c8803fbe1  node-v12.13.1-linux-ppc64le.tar.gz
1bfaab87df6848d358ef42300a14b6adf6c4350b255ca086ac6d7e73a4fddf1d  node-v12.13.1-linux-ppc64le.tar.xz
3a8f688f3df47d4cd0531ca0cf090026cd6129f59683f4f0b36b9316642b2d2f  node-v12.13.1-linux-s390x.tar.gz
4568971972b5ccbdbb9b8c26ec590dce24185ac54c9c4954c88ecccf5b1cd266  node-v12.13.1-linux-s390x.tar.xz
074a6129da34b768b791f39e8b74c6e4ab3349d1296f1a303ef3547a7f9cf9be  node-v12.13.1-linux-x64.tar.gz
aca06db37589966829b1ef0f163a5859b156a1d8e51b415bf47590f667c30a25  node-v12.13.1-linux-x64.tar.xz
bb12c7684e58188a616e4ba653a2316a730631acc41c8b3a1da889443f979c51  node-v12.13.1.pkg
beb713c6537d83b30e085d15927a6ecdca5b736a8a3623db655e3f882cd78ac6  node-v12.13.1-sunos-x64.tar.gz
545afeabb2506f74fe6f705b2190c3f8c48e2a6e6dabc055a6b1cb2eef0de266  node-v12.13.1-sunos-x64.tar.xz
4ee710087687c8de142329d95085f5cba66e454a2c9ea7ec11e1f4b476d6d1ac  node-v12.13.1.tar.gz
349e3a739cc26bb0975c0ada12b11933568ecbea459297fe8ae0a2acc351b192  node-v12.13.1.tar.xz
cf0938b607ba0ef2f304e96a07be5abe1b6a48e24e492e32d2e104d90653aa7d  node-v12.13.1-win-x64.7z
db33fb758ba49b96d073311ef9e9134b51bf96246ffd938909b2e02c65a6e890  node-v12.13.1-win-x64.zip
509426c34c56b0fb65af7a36e889a904d8ff29f3709920c7caace9764274e867  node-v12.13.1-win-x86.7z
a2e22034a977a1dc5f44ec80ed5169f3b674cabff25216e33234d7e9e191b124  node-v12.13.1-win-x86.zip
b0b4fcae7531a0509fc1f29e814ea59487c38787df671e6bc04b17ee355b24f3  node-v12.13.1-x64.msi
54e9c3a65fb563f7426749b4351342406b87495375ebfc628bcc82b88147b7f9  node-v12.13.1-x86.msi
f477816eaf1edf57ecba57419fff891084ebd9f55af1570466a8701a0efcaac6  win-x64/node.exe
30ec1ab45d9d22ad93e7c95691640d83818175b4fd74680d12a1f900a7d87f80  win-x64/node.lib
95f0e6c03814fe60360f03df6a4728dc9ac5a5f075d7fbc08eb3b847412a20c1  win-x64/node_pdb.7z
5123732c74f1a595cd7a3b0294c06db6ab7f1369818c3b98a9f1c064ca059268  win-x64/node_pdb.zip
027c715341003212baca83c7eaed28852cf7a6b5f97e9bc466eec56b8fccc2ba  win-x86/node.exe
87a49b27dfc454c5f8ff71dffb0c27322a8148a661d216741156ad61c2493cd1  win-x86/node.lib
446f3f7564df6fca5aa44019090f26aa1262c74276962513c932d95b81490814  win-x86/node_pdb.7z
2eacef60ab30815ceb324a6520a0f75dfc2110e2fe39d8f156255915cc940be5  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEj8yhP+8dDC6RAI4Jdw96mlrhVgAFAl3TyUsACgkQdw96mlrh
VgB5QA//cb7tmMjLdE3f0dD3gKz+CJiNnHLkgNlrnrAC+dXCbQryPIGVS9Q6Embs
cLu8J5uwSaUGhH0JwIWKJ+mDq5oAOFiPNHpem+OWUuAW0VzdJ0yCpn96REQRcORp
/+WOw4uiON3fneuCyv9+1SWRzVYeBlvJxj0f4V9hdohZNFT3BVu8LEws960jk2LL
GWgc1r1+t0HQxZsMJuia6c+pVv2Yek3emrVSjZXk683QXMcFpa3mNkT0ATqOK2LV
GBteeakJF+PaNYXP7kGLu6zh/zhEDlIkyUNCM7ajdqF7q5ulShtWzS0SIJ4/Uszj
Qp3EFfZfRKxBVnvu/Sar3C+rJ7XEBErPxz7NjV+OuCEQ04OFFLCZGsGzaVakhZv4
cqwkJ8PpowMjSF1GCySVNk7a7CaReRAVcPXg91z/MeeDHLQf62cNTueNh6EqGimj
SpJSN2jgNQ2WJ2Lq/uFO0PEePS0P+Kfcld2Ub3n/CB2f3Z1EZ/Jai+i1Rx88Y8/G
Z6C1aI/AWuddG7IzfA9tSMCi39zqSubp9o11WxZxl+3oVVjvsGdZQMkFLRhNpUue
2l6lmv94Qsl6xGoi6I2p0TGusGPKFGtT6zKCq3OiB/3yXpBCIRmco83lAnhc64nZ
SAIu+wgJgw/4WY1tEX53aaoj+NdCa12F1QJm1CVtCqDUq+DLE/I=
=+UdC
-----END PGP SIGNATURE-----

```
