---
date: '2022-07-26T22:25:47.570Z'
category: release
title: Node v18.7.0 (Current)
layout: blog-post
author: <PERSON>
---

### Notable changes

- **doc**:
  - add F3n67u to collaborators (<PERSON>) [#43953](https://github.com/nodejs/node/pull/43953)
  - deprecate coercion to integer in process.exit (Daeyeon Jeong) [#43738](https://github.com/nodejs/node/pull/43738)
  - **(SEMVER-MINOR)** deprecate diagnostics_channel object subscribe method (<PERSON>) [#42714](https://github.com/nodejs/node/pull/42714)
- **events**:
  - **(SEMVER-MINOR)** expose CustomEvent on global with CLI flag (Daeyeon Jeong) [#43885](https://github.com/nodejs/node/pull/43885)
  - **(SEMVER-MINOR)** add `CustomEvent` (Daeyeon Jeong) [#43514](https://github.com/nodejs/node/pull/43514)
- **http**:
  - **(SEMVER-MINOR)** add drop request event for http server (theanarkh) [#43806](https://github.com/nodejs/node/pull/43806)
- **lib**:
  - **(SEMVER-MINOR)** improved diagnostics_channel subscribe/unsubscribe (Stephen Belanger) [#42714](https://github.com/nodejs/node/pull/42714)
- **util**:
  - **(SEMVER-MINOR)** add tokens to parseArgs (John Gee) [#43459](https://github.com/nodejs/node/pull/43459)

### Commits

- \[[`0aa255ab72`](https://github.com/nodejs/node/commit/0aa255ab72)] - **bootstrap**: handle snapshot errors gracefully (Joyee Cheung) [#43531](https://github.com/nodejs/node/pull/43531)
- \[[`0783ddf57e`](https://github.com/nodejs/node/commit/0783ddf57e)] - **buffer**: do not leak memory if buffer is too big (Keyhan Vakil) [#43938](https://github.com/nodejs/node/pull/43938)
- \[[`12657accdd`](https://github.com/nodejs/node/commit/12657accdd)] - **build**: add .gitattributes for npm and other shims (Hrishikesh Kadam) [#43879](https://github.com/nodejs/node/pull/43879)
- \[[`c2db4f4581`](https://github.com/nodejs/node/commit/c2db4f4581)] - **build**: make GitPod less noisy (Rich Trott) [#43829](https://github.com/nodejs/node/pull/43829)
- \[[`364deeadcd`](https://github.com/nodejs/node/commit/364deeadcd)] - **build**: add GitHub token permissions for workflows (Varun Sharma) [#43743](https://github.com/nodejs/node/pull/43743)
- \[[`8b83b4d5be`](https://github.com/nodejs/node/commit/8b83b4d5be)] - **child_process**: do not need to count length when maxBuffer is Infinity (theanarkh) [#43822](https://github.com/nodejs/node/pull/43822)
- \[[`c1893b7a7c`](https://github.com/nodejs/node/commit/c1893b7a7c)] - **child_process**: avoid repeated calls to `normalizeSpawnArguments` (木杉) [#43345](https://github.com/nodejs/node/pull/43345)
- \[[`7b276b89b9`](https://github.com/nodejs/node/commit/7b276b89b9)] - **cluster**: send connection to other server when worker drop it (theanarkh) [#43747](https://github.com/nodejs/node/pull/43747)
- \[[`e8c66f92a5`](https://github.com/nodejs/node/commit/e8c66f92a5)] - **crypto**: remove unneeded guard (Rich Trott) [#43856](https://github.com/nodejs/node/pull/43856)
- \[[`c95132e9ea`](https://github.com/nodejs/node/commit/c95132e9ea)] - **deps**: cherry-pick libuv/libuv\@3a7b955 (Ben Noordhuis) [#43950](https://github.com/nodejs/node/pull/43950)
- \[[`cc8d5426d2`](https://github.com/nodejs/node/commit/cc8d5426d2)] - **deps**: cherry-pick libuv/libuv\@abb109f (Ben Noordhuis) [#43950](https://github.com/nodejs/node/pull/43950)
- \[[`7762e463d6`](https://github.com/nodejs/node/commit/7762e463d6)] - **deps**: update corepack to 0.12.1 (Node.js GitHub Bot) [#43965](https://github.com/nodejs/node/pull/43965)
- \[[`1256c4dad5`](https://github.com/nodejs/node/commit/1256c4dad5)] - **deps**: update hast-util-raw (Moshe Atlow) [#43927](https://github.com/nodejs/node/pull/43927)
- \[[`aac97c2d2a`](https://github.com/nodejs/node/commit/aac97c2d2a)] - **deps**: update undici to 5.8.0 (Node.js GitHub Bot) [#43886](https://github.com/nodejs/node/pull/43886)
- \[[`cdff61917d`](https://github.com/nodejs/node/commit/cdff61917d)] - **deps**: clean archs files for OpenSSL (RafaelGSS) [#43735](https://github.com/nodejs/node/pull/43735)
- \[[`fc936a84e4`](https://github.com/nodejs/node/commit/fc936a84e4)] - **deps**: remove not used architectures (RafaelGSS) [#43735](https://github.com/nodejs/node/pull/43735)
- \[[`361a643d8b`](https://github.com/nodejs/node/commit/361a643d8b)] - **deps**: V8: backport f3cad8cec656 (Joyee Cheung) [#43531](https://github.com/nodejs/node/pull/43531)
- \[[`2e1732ebd0`](https://github.com/nodejs/node/commit/2e1732ebd0)] - **deps**: V8: backport 22698d267667 (Chengzhong Wu) [#43751](https://github.com/nodejs/node/pull/43751)
- \[[`979f469d3a`](https://github.com/nodejs/node/commit/979f469d3a)] - **deps**: upgrade npm to 8.15.0 (npm team) [#43917](https://github.com/nodejs/node/pull/43917)
- \[[`4096d81988`](https://github.com/nodejs/node/commit/4096d81988)] - **deps**: upgrade npm to 8.14.0 (npm team) [#43826](https://github.com/nodejs/node/pull/43826)
- \[[`2ec8092e2c`](https://github.com/nodejs/node/commit/2ec8092e2c)] - **deps,src**: use SIMD for normal base64 encoding (Brian White) [#39775](https://github.com/nodejs/node/pull/39775)
- \[[`67b4edde37`](https://github.com/nodejs/node/commit/67b4edde37)] - **dns**: fix getServers return undefined (jiahao.si) [#43922](https://github.com/nodejs/node/pull/43922)
- \[[`7c75539a88`](https://github.com/nodejs/node/commit/7c75539a88)] - **dns**: fix cares memory leak (theanarkh) [#43912](https://github.com/nodejs/node/pull/43912)
- \[[`1f80b88da5`](https://github.com/nodejs/node/commit/1f80b88da5)] - **doc**: update email and mailmap for BethGriggs (Beth Griggs) [#43985](https://github.com/nodejs/node/pull/43985)
- \[[`8a2a6e16eb`](https://github.com/nodejs/node/commit/8a2a6e16eb)] - **doc**: add 15.x - 18.x to Other Versions section (shhh7612) [#43940](https://github.com/nodejs/node/pull/43940)
- \[[`51cb0d42ca`](https://github.com/nodejs/node/commit/51cb0d42ca)] - **doc**: inspector.close undefined in worker threads (Keyhan Vakil) [#43867](https://github.com/nodejs/node/pull/43867)
- \[[`c789c0f5f7`](https://github.com/nodejs/node/commit/c789c0f5f7)] - **doc**: improve documentation for safe `Promise` statics alternatives (Antoine du Hamel) [#43759](https://github.com/nodejs/node/pull/43759)
- \[[`cb9b0e0011`](https://github.com/nodejs/node/commit/cb9b0e0011)] - **doc**: recommend git-node-v8 (Keyhan Vakil) [#43934](https://github.com/nodejs/node/pull/43934)
- \[[`d7e9bd1830`](https://github.com/nodejs/node/commit/d7e9bd1830)] - **doc**: clarify subprocess.stdout/in/err property (Kohei Ueno) [#43910](https://github.com/nodejs/node/pull/43910)
- \[[`808793ebb5`](https://github.com/nodejs/node/commit/808793ebb5)] - **doc**: fix typo in `src/crypto/README.md` (Jianru Lin) [#43968](https://github.com/nodejs/node/pull/43968)
- \[[`bbc455c4f9`](https://github.com/nodejs/node/commit/bbc455c4f9)] - **doc**: remind backporter about v8_embedder_string (Keyhan Vakil) [#43924](https://github.com/nodejs/node/pull/43924)
- \[[`a86b66c8b4`](https://github.com/nodejs/node/commit/a86b66c8b4)] - **doc**: fix typo in http.md (Airing) [#43933](https://github.com/nodejs/node/pull/43933)
- \[[`a96af37233`](https://github.com/nodejs/node/commit/a96af37233)] - **doc**: add F3n67u to collaborators (Feng Yu) [#43953](https://github.com/nodejs/node/pull/43953)
- \[[`aa7d4e59f7`](https://github.com/nodejs/node/commit/aa7d4e59f7)] - **doc**: improve test runner timeout docs (Tobias Nießen) [#43836](https://github.com/nodejs/node/pull/43836)
- \[[`80c2fa8212`](https://github.com/nodejs/node/commit/80c2fa8212)] - **doc**: mention Win 32-bit openssl build issue (RafaelGSS) [#43853](https://github.com/nodejs/node/pull/43853)
- \[[`8b8c55df7e`](https://github.com/nodejs/node/commit/8b8c55df7e)] - **doc**: add security release specifics to releases.md (Beth Griggs) [#43835](https://github.com/nodejs/node/pull/43835)
- \[[`42693aaf9f`](https://github.com/nodejs/node/commit/42693aaf9f)] - **doc**: add history info for `global.performance` (Antoine du Hamel) [#43841](https://github.com/nodejs/node/pull/43841)
- \[[`140d6af572`](https://github.com/nodejs/node/commit/140d6af572)] - **doc**: add platform-windows-arm to who to CC (Michael Dawson) [#43808](https://github.com/nodejs/node/pull/43808)
- \[[`976093efe3`](https://github.com/nodejs/node/commit/976093efe3)] - **doc**: document ES2022's Error "cause" property (James Ide) [#43830](https://github.com/nodejs/node/pull/43830)
- \[[`ec7e45e4a2`](https://github.com/nodejs/node/commit/ec7e45e4a2)] - **doc**: include make clean to openssl arch (RafaelGSS) [#43735](https://github.com/nodejs/node/pull/43735)
- \[[`d64dfd53c9`](https://github.com/nodejs/node/commit/d64dfd53c9)] - **doc**: add link to diagnostic tools (Rafael Gonzaga) [#43736](https://github.com/nodejs/node/pull/43736)
- \[[`2910136920`](https://github.com/nodejs/node/commit/2910136920)] - **doc**: update links to MDN page about dynamic imports (Jannis R) [#43847](https://github.com/nodejs/node/pull/43847)
- \[[`d88a9fae79`](https://github.com/nodejs/node/commit/d88a9fae79)] - **doc**: deprecate coercion to integer in process.exit (Daeyeon Jeong) [#43738](https://github.com/nodejs/node/pull/43738)
- \[[`fc843e103d`](https://github.com/nodejs/node/commit/fc843e103d)] - **doc**: add MoLow to triagers (Moshe Atlow) [#43799](https://github.com/nodejs/node/pull/43799)
- \[[`8c8c97da61`](https://github.com/nodejs/node/commit/8c8c97da61)] - **(SEMVER-MINOR)** **doc**: deprecate diagnostics_channel object subscribe method (Stephen Belanger) [#42714](https://github.com/nodejs/node/pull/42714)
- \[[`9b53a694b5`](https://github.com/nodejs/node/commit/9b53a694b5)] - **doc**: revert anachronistic 'node:' module require()s in API history notes (DeeDeeG) [#43768](https://github.com/nodejs/node/pull/43768)
- \[[`2815bd3002`](https://github.com/nodejs/node/commit/2815bd3002)] - **doc**: clarify release process for new releasers (Rafael Gonzaga) [#43739](https://github.com/nodejs/node/pull/43739)
- \[[`50b3750e67`](https://github.com/nodejs/node/commit/50b3750e67)] - **doc**: fix typo in ngtcp2 readme (Dan Castillo) [#43767](https://github.com/nodejs/node/pull/43767)
- \[[`6bcd40dd85`](https://github.com/nodejs/node/commit/6bcd40dd85)] - **domain**: fix vm promise tracking while keeping isolation (Stephen Belanger) [#43556](https://github.com/nodejs/node/pull/43556)
- \[[`e89e0b470b`](https://github.com/nodejs/node/commit/e89e0b470b)] - **esm**: remove superfluous argument (Rich Trott) [#43884](https://github.com/nodejs/node/pull/43884)
- \[[`0d2921f396`](https://github.com/nodejs/node/commit/0d2921f396)] - **esm**: fix erroneous re-initialization of ESMLoader (Jacob Smith) [#43763](https://github.com/nodejs/node/pull/43763)
- \[[`9b5b8d78c3`](https://github.com/nodejs/node/commit/9b5b8d78c3)] - **esm**: throw on any non-2xx response (LiviaMedeiros) [#43742](https://github.com/nodejs/node/pull/43742)
- \[[`dfc4832ef1`](https://github.com/nodejs/node/commit/dfc4832ef1)] - **(SEMVER-MINOR)** **events**: expose CustomEvent on global with CLI flag (Daeyeon Jeong) [#43885](https://github.com/nodejs/node/pull/43885)
- \[[`e4473952ae`](https://github.com/nodejs/node/commit/e4473952ae)] - **(SEMVER-MINOR)** **events**: add `CustomEvent` (Daeyeon Jeong) [#43514](https://github.com/nodejs/node/pull/43514)
- \[[`100f6deb09`](https://github.com/nodejs/node/commit/100f6deb09)] - **fs**: use signed types for stat data (LiviaMedeiros) [#43714](https://github.com/nodejs/node/pull/43714)
- \[[`25ec71db63`](https://github.com/nodejs/node/commit/25ec71db63)] - **http**: fix http server connection list when close (theanarkh) [#43949](https://github.com/nodejs/node/pull/43949)
- \[[`ca658c8afe`](https://github.com/nodejs/node/commit/ca658c8afe)] - **(SEMVER-MINOR)** **http**: add drop request event for http server (theanarkh) [#43806](https://github.com/nodejs/node/pull/43806)
- \[[`9c699bd8a8`](https://github.com/nodejs/node/commit/9c699bd8a8)] - **http**: wait for pending responses in closeIdleConnections (Paolo Insogna) [#43890](https://github.com/nodejs/node/pull/43890)
- \[[`781d5e54e3`](https://github.com/nodejs/node/commit/781d5e54e3)] - **inspector**: set sampling interval before start (Shelley Vohr) [#43779](https://github.com/nodejs/node/pull/43779)
- \[[`0b5dbb2a56`](https://github.com/nodejs/node/commit/0b5dbb2a56)] - **lib**: refactor PriorityQueue to use private field (Finn Yu) [#43889](https://github.com/nodejs/node/pull/43889)
- \[[`324473ca32`](https://github.com/nodejs/node/commit/324473ca32)] - **(SEMVER-MINOR)** **lib**: improved diagnostics_channel subscribe/unsubscribe (Stephen Belanger) [#42714](https://github.com/nodejs/node/pull/42714)
- \[[`5aa3b213ac`](https://github.com/nodejs/node/commit/5aa3b213ac)] - **meta**: update AUTHORS (Node.js GitHub Bot) [#43966](https://github.com/nodejs/node/pull/43966)
- \[[`e707552357`](https://github.com/nodejs/node/commit/e707552357)] - **meta**: update `node-api` in label-pr-config (Daeyeon Jeong) [#43794](https://github.com/nodejs/node/pull/43794)
- \[[`8a8de94034`](https://github.com/nodejs/node/commit/8a8de94034)] - **meta**: update AUTHORS (Node.js GitHub Bot) [#43872](https://github.com/nodejs/node/pull/43872)
- \[[`7d49fc766c`](https://github.com/nodejs/node/commit/7d49fc766c)] - **meta**: use platform dropdown on flaky template (Rafael Gonzaga) [#43855](https://github.com/nodejs/node/pull/43855)
- \[[`e4aa50fc3f`](https://github.com/nodejs/node/commit/e4aa50fc3f)] - **meta**: enable blank issues (Matteo Collina) [#43775](https://github.com/nodejs/node/pull/43775)
- \[[`ceb7c150ec`](https://github.com/nodejs/node/commit/ceb7c150ec)] - **meta**: move one or more collaborators to emeritus (Node.js GitHub Bot) [#43770](https://github.com/nodejs/node/pull/43770)
- \[[`29bcd47738`](https://github.com/nodejs/node/commit/29bcd47738)] - **net**: fix socket.\_getpeername (Daeyeon Jeong) [#43010](https://github.com/nodejs/node/pull/43010)
- \[[`380659daf1`](https://github.com/nodejs/node/commit/380659daf1)] - **process**: use `defineProperty` instead of assignment (Mark S. Miller) [#43907](https://github.com/nodejs/node/pull/43907)
- \[[`aba9c8ebea`](https://github.com/nodejs/node/commit/aba9c8ebea)] - **repl**: fix overzealous top-level await (Tobias Nießen) [#43827](https://github.com/nodejs/node/pull/43827)
- \[[`1deb6b73b7`](https://github.com/nodejs/node/commit/1deb6b73b7)] - **repl**: use `SafePromiseAll` and `SafePromiseRace` (Antoine du Hamel) [#43758](https://github.com/nodejs/node/pull/43758)
- \[[`bf8f2e23ff`](https://github.com/nodejs/node/commit/bf8f2e23ff)] - **src**: refactor DH groups to delete crypto_groups.h (Tobias Nießen) [#43896](https://github.com/nodejs/node/pull/43896)
- \[[`9435fbf8cd`](https://github.com/nodejs/node/commit/9435fbf8cd)] - **src**: remove dead code in base64_encode (Tobias Nießen) [#43979](https://github.com/nodejs/node/pull/43979)
- \[[`2c47e58ea0`](https://github.com/nodejs/node/commit/2c47e58ea0)] - **src**: fix regression that a source marker is lost (cola119) [#43086](https://github.com/nodejs/node/pull/43086)
- \[[`d084150320`](https://github.com/nodejs/node/commit/d084150320)] - **src**: per-isolate eternal template properties (Chengzhong Wu) [#43802](https://github.com/nodejs/node/pull/43802)
- \[[`9f9d00ccbb`](https://github.com/nodejs/node/commit/9f9d00ccbb)] - **src**: merge NativeModuleEnv into NativeModuleLoader (Joyee Cheung) [#43824](https://github.com/nodejs/node/pull/43824)
- \[[`bb512904e9`](https://github.com/nodejs/node/commit/bb512904e9)] - **src**: use named struct instead of typedef (Tobias Nießen) [#43881](https://github.com/nodejs/node/pull/43881)
- \[[`bb5511e8cc`](https://github.com/nodejs/node/commit/bb5511e8cc)] - **src**: use named enum instead of typedef (Tobias Nießen) [#43880](https://github.com/nodejs/node/pull/43880)
- \[[`5db0c8f667`](https://github.com/nodejs/node/commit/5db0c8f667)] - **src**: pass only Isolate\* and env_vars to EnabledDebugList::Parse() (Darshan Sen) [#43668](https://github.com/nodejs/node/pull/43668)
- \[[`249365524e`](https://github.com/nodejs/node/commit/249365524e)] - **src**: fix node watchdog race condition (theanarkh) [#43780](https://github.com/nodejs/node/pull/43780)
- \[[`17cb27237d`](https://github.com/nodejs/node/commit/17cb27237d)] - **src**: deduplicate `SetALPN` implementations (Tobias Nießen) [#43756](https://github.com/nodejs/node/pull/43756)
- \[[`b4c75a96be`](https://github.com/nodejs/node/commit/b4c75a96be)] - **src**: fix `napi_check_object_type_tag()` (Daeyeon Jeong) [#43788](https://github.com/nodejs/node/pull/43788)
- \[[`8432d6596f`](https://github.com/nodejs/node/commit/8432d6596f)] - **src**: slim down env-inl.h (Ben Noordhuis) [#43745](https://github.com/nodejs/node/pull/43745)
- \[[`2266a4b6d6`](https://github.com/nodejs/node/commit/2266a4b6d6)] - **stream**: improve `respondWithNewView()` (Daeyeon Jeong) [#43866](https://github.com/nodejs/node/pull/43866)
- \[[`bf3991b406`](https://github.com/nodejs/node/commit/bf3991b406)] - **stream**: fix 0 transform hwm backpressure (Robert Nagy) [#43685](https://github.com/nodejs/node/pull/43685)
- \[[`a057510037`](https://github.com/nodejs/node/commit/a057510037)] - **stream**: initial approach to include strategy options on Readable.toWeb() (txxnano) [#43515](https://github.com/nodejs/node/pull/43515)
- \[[`198cf59d2c`](https://github.com/nodejs/node/commit/198cf59d2c)] - **test**: update WPT encoding tests (Kohei Ueno) [#43958](https://github.com/nodejs/node/pull/43958)
- \[[`f0ed1aed8d`](https://github.com/nodejs/node/commit/f0ed1aed8d)] - **test**: remove test-whatwg-events-add-event-listener-options-once.js (Feng Yu) [#43877](https://github.com/nodejs/node/pull/43877)
- \[[`88505556fe`](https://github.com/nodejs/node/commit/88505556fe)] - **test**: work scheduled in process.nextTick can keep the event loop alive (Andreu Botella) [#43787](https://github.com/nodejs/node/pull/43787)
- \[[`81a21946eb`](https://github.com/nodejs/node/commit/81a21946eb)] - **test**: simplify test-tls-set-secure-context (Tobias Nießen) [#43878](https://github.com/nodejs/node/pull/43878)
- \[[`61cd11a8a7`](https://github.com/nodejs/node/commit/61cd11a8a7)] - **test**: use `common.mustNotMutateObjectDeep()` in fs tests (LiviaMedeiros) [#43819](https://github.com/nodejs/node/pull/43819)
- \[[`b1081dbe12`](https://github.com/nodejs/node/commit/b1081dbe12)] - **test**: fix test http upload timeout (theanarkh) [#43935](https://github.com/nodejs/node/pull/43935)
- \[[`efd5e0e925`](https://github.com/nodejs/node/commit/efd5e0e925)] - **test**: simplify ReplStream.wait() (Tobias Nießen) [#43857](https://github.com/nodejs/node/pull/43857)
- \[[`ef21ad2996`](https://github.com/nodejs/node/commit/ef21ad2996)] - **test**: merge test-crypto-dh-hash with modp18 test (Tobias Nießen) [#43891](https://github.com/nodejs/node/pull/43891)
- \[[`e502c50a90`](https://github.com/nodejs/node/commit/e502c50a90)] - **test**: refactor `test/es-module/test-esm-resolve-type` (Antoine du Hamel) [#43178](https://github.com/nodejs/node/pull/43178)
- \[[`c782c3dc69`](https://github.com/nodejs/node/commit/c782c3dc69)] - **test**: ensure NODE_EXTRA_CA_CERTS not set before tests (KrayzeeKev) [#43858](https://github.com/nodejs/node/pull/43858)
- \[[`bb6787cb57`](https://github.com/nodejs/node/commit/bb6787cb57)] - **test**: add check to test-fs-readfile-tostring-fail (Richard Lau) [#43850](https://github.com/nodejs/node/pull/43850)
- \[[`7571704186`](https://github.com/nodejs/node/commit/7571704186)] - **test**: complete TODO in test/wpt/test-url.js (Kohei Ueno) [#43797](https://github.com/nodejs/node/pull/43797)
- \[[`6f1d2dfb9d`](https://github.com/nodejs/node/commit/6f1d2dfb9d)] - **test**: add test on worker process.exit in async modules (Chengzhong Wu) [#43751](https://github.com/nodejs/node/pull/43751)
- \[[`776cc3abbd`](https://github.com/nodejs/node/commit/776cc3abbd)] - **test**: use `common.mustNotMutateObjectDeep()` in immutability tests (LiviaMedeiros) [#43196](https://github.com/nodejs/node/pull/43196)
- \[[`42f2deb3a0`](https://github.com/nodejs/node/commit/42f2deb3a0)] - **test**: add `common.mustNotMutateObjectDeep()` (LiviaMedeiros) [#43196](https://github.com/nodejs/node/pull/43196)
- \[[`f3fc51c508`](https://github.com/nodejs/node/commit/f3fc51c508)] - **test**: fix coverity warning in test (Michael Dawson) [#43631](https://github.com/nodejs/node/pull/43631)
- \[[`a9ecba2fa8`](https://github.com/nodejs/node/commit/a9ecba2fa8)] - **test**: mark test-http-client-response-timeout flaky (Tobias Nießen) [#43792](https://github.com/nodejs/node/pull/43792)
- \[[`cd0d9ddb7c`](https://github.com/nodejs/node/commit/cd0d9ddb7c)] - **test_runner**: add support for boolean values for `concurrency` option (Lenvin Gonsalves) [#43887](https://github.com/nodejs/node/pull/43887)
- \[[`f98020138a`](https://github.com/nodejs/node/commit/f98020138a)] - **test_runner**: validate `timeout` option (Antoine du Hamel) [#43843](https://github.com/nodejs/node/pull/43843)
- \[[`58d15b3687`](https://github.com/nodejs/node/commit/58d15b3687)] - **test_runner**: pass signal on timeout (Moshe Atlow) [#43911](https://github.com/nodejs/node/pull/43911)
- \[[`8b0248506f`](https://github.com/nodejs/node/commit/8b0248506f)] - **test_runner**: do not report an error when tests are passing (Antoine du Hamel) [#43919](https://github.com/nodejs/node/pull/43919)
- \[[`aa8053e1fa`](https://github.com/nodejs/node/commit/aa8053e1fa)] - **test_runner**: recieve and pass AbortSignal (Moshe Atlow) [#43554](https://github.com/nodejs/node/pull/43554)
- \[[`f13e4c1be9`](https://github.com/nodejs/node/commit/f13e4c1be9)] - **test_runner**: fix `it` concurrency (Moshe Atlow) [#43757](https://github.com/nodejs/node/pull/43757)
- \[[`e404a3ef6d`](https://github.com/nodejs/node/commit/e404a3ef6d)] - **test_runner**: support timeout for tests (Moshe Atlow) [#43505](https://github.com/nodejs/node/pull/43505)
- \[[`f28198cc05`](https://github.com/nodejs/node/commit/f28198cc05)] - **test_runner**: catch errors thrown within `describe` (Moshe Atlow) [#43729](https://github.com/nodejs/node/pull/43729)
- \[[`bfe0ac6cd0`](https://github.com/nodejs/node/commit/bfe0ac6cd0)] - **tools**: add more options to track flaky tests (Antoine du Hamel) [#43954](https://github.com/nodejs/node/pull/43954)
- \[[`17a4e5e775`](https://github.com/nodejs/node/commit/17a4e5e775)] - **tools**: add verbose flag to inactive TSC finder (Rich Trott) [#43913](https://github.com/nodejs/node/pull/43913)
- \[[`373304b0c7`](https://github.com/nodejs/node/commit/373304b0c7)] - **tools**: add support for using API key to vuln checking script (Facundo Tuesca) [#43909](https://github.com/nodejs/node/pull/43909)
- \[[`ed45088c14`](https://github.com/nodejs/node/commit/ed45088c14)] - **tools**: support versioned node shared libs on z/OS (alexcfyung) [#42256](https://github.com/nodejs/node/pull/42256)
- \[[`c9ecd6d21f`](https://github.com/nodejs/node/commit/c9ecd6d21f)] - **tools**: update doc to highlight.js\@11.6.0 (Node.js GitHub Bot) [#43870](https://github.com/nodejs/node/pull/43870)
- \[[`c92135aa0f`](https://github.com/nodejs/node/commit/c92135aa0f)] - **tools**: update lint-md-dependencies to rollup\@2.77.0 (Node.js GitHub Bot) [#43871](https://github.com/nodejs/node/pull/43871)
- \[[`e12bf40fd1`](https://github.com/nodejs/node/commit/e12bf40fd1)] - **tools**: update eslint to 8.20.0 (Node.js GitHub Bot) [#43873](https://github.com/nodejs/node/pull/43873)
- \[[`09fe9b30a9`](https://github.com/nodejs/node/commit/09fe9b30a9)] - **tools**: add script for vulnerability checking (Facundo Tuesca) [#43362](https://github.com/nodejs/node/pull/43362)
- \[[`19e8876877`](https://github.com/nodejs/node/commit/19e8876877)] - **trace_events**: trace net connect event (theanarkh) [#43903](https://github.com/nodejs/node/pull/43903)
- \[[`1af7f24143`](https://github.com/nodejs/node/commit/1af7f24143)] - **util**: remove unicode support todo for perf implications (Rhys) [#43762](https://github.com/nodejs/node/pull/43762)
- \[[`acfc33ca8c`](https://github.com/nodejs/node/commit/acfc33ca8c)] - **(SEMVER-MINOR)** **util**: add tokens to parseArgs (John Gee) [#43459](https://github.com/nodejs/node/pull/43459)
- \[[`f32aec8a6d`](https://github.com/nodejs/node/commit/f32aec8a6d)] - **util**: refactor to use validateObject (Kohei Ueno) [#43769](https://github.com/nodejs/node/pull/43769)
- \[[`d7cfd0c5ba`](https://github.com/nodejs/node/commit/d7cfd0c5ba)] - **v8**: serialize BigInt64Array and BigUint64Array (Ben Noordhuis) [#43571](https://github.com/nodejs/node/pull/43571)

Windows 32-bit Installer: https://nodejs.org/dist/v18.7.0/node-v18.7.0-x86.msi \
Windows 64-bit Installer: https://nodejs.org/dist/v18.7.0/node-v18.7.0-x64.msi \
Windows 32-bit Binary: https://nodejs.org/dist/v18.7.0/win-x86/node.exe \
Windows 64-bit Binary: https://nodejs.org/dist/v18.7.0/win-x64/node.exe \
macOS 64-bit Installer: https://nodejs.org/dist/v18.7.0/node-v18.7.0.pkg \
macOS Apple Silicon 64-bit Binary: https://nodejs.org/dist/v18.7.0/node-v18.7.0-darwin-arm64.tar.gz \
macOS Intel 64-bit Binary: https://nodejs.org/dist/v18.7.0/node-v18.7.0-darwin-x64.tar.gz \
Linux 64-bit Binary: https://nodejs.org/dist/v18.7.0/node-v18.7.0-linux-x64.tar.xz \
Linux PPC LE 64-bit Binary: https://nodejs.org/dist/v18.7.0/node-v18.7.0-linux-ppc64le.tar.xz \
Linux s390x 64-bit Binary: https://nodejs.org/dist/v18.7.0/node-v18.7.0-linux-s390x.tar.xz \
AIX 64-bit Binary: https://nodejs.org/dist/v18.7.0/node-v18.7.0-aix-ppc64.tar.gz \
ARMv7 32-bit Binary: https://nodejs.org/dist/v18.7.0/node-v18.7.0-linux-armv7l.tar.xz \
ARMv8 64-bit Binary: https://nodejs.org/dist/v18.7.0/node-v18.7.0-linux-arm64.tar.xz \
Source Code: https://nodejs.org/dist/v18.7.0/node-v18.7.0.tar.gz \
Other release files: https://nodejs.org/dist/v18.7.0/ \
Documentation: https://nodejs.org/docs/v18.7.0/api/

### SHASUMS

```
-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

2ecce3603a75464e45e5e3c451662ff5993b7608aa838e0bf17ba1f48b989b11  node-v18.7.0-aix-ppc64.tar.gz
ea24b35067bd0dc40ea8fda1087acc87672cbcbba881f7477dbd432e3c03343d  node-v18.7.0-darwin-arm64.tar.gz
ef593cbb3a3f0aae9879b74a7850d794abab26178aa5e0f67ff182894811e6f0  node-v18.7.0-darwin-arm64.tar.xz
ce95b924b450edbcfeaf422b3635a6b44b17ad23cd1f5efff6b051c60db548c8  node-v18.7.0-darwin-x64.tar.gz
b55634dd34b8b5e4368afdafe523fa831db53e7268254582522df3614cc136c8  node-v18.7.0-darwin-x64.tar.xz
66ec4c75fa26b49486c8fe558750a9e5590a76c68e1c9d728c8e119a0f317b3c  node-v18.7.0-headers.tar.gz
61dc295894be4b0fbd08c734625e7cf38c1328586d4bdf3d31d107f8ea42efa0  node-v18.7.0-headers.tar.xz
d971f644d3143422eb7a517e08c38a45a43cae14d977b3b96ff61b64f3a26b04  node-v18.7.0-linux-arm64.tar.gz
c3ac4905ec3993d00a45d2c7af8417e79e907be51b8ffecb54c5b9ab8ef0bc9f  node-v18.7.0-linux-arm64.tar.xz
25e78e9463e10c68df0f7e2af6fbc18786ae4353896201948f369c2036aa5e16  node-v18.7.0-linux-armv7l.tar.gz
72a20d455f29aed766ef9c8484ab02fd0fd9a2a7a7653e39f7f8cc3b07482054  node-v18.7.0-linux-armv7l.tar.xz
a5d2a43630f0a381bace91c31a7e7752b64341c3d8b2eaf5515f814fad07a231  node-v18.7.0-linux-ppc64le.tar.gz
277bacafc1107f41bb2a503044f3f224852fe69ebf9049ba812eac23bc08e8fc  node-v18.7.0-linux-ppc64le.tar.xz
3d5330337892ff21a5fb7058a68aee3274ab36637b212380cbb78c45cd15244b  node-v18.7.0-linux-s390x.tar.gz
02a66802b042de524b5f314ee55b85e779066f95279d34988a248fc4c6198103  node-v18.7.0-linux-s390x.tar.xz
0bef16a77faed5220c2ea1555f7bd19ea79bfbb848dba62fbe9d43eb1a36fce0  node-v18.7.0-linux-x64.tar.gz
8bc6a1b9deaed2586d726fc62d4bee9c1bfc5a30b96c1c4cff7edd15225a11a2  node-v18.7.0-linux-x64.tar.xz
acee24578cee62ee95665e8294d28f72b573b205bd43bd25e952f702451b238f  node-v18.7.0.pkg
7dcf86ccd6831032531bcbec0cdcc993f9997bba2f53f6d29c72603ea69285ba  node-v18.7.0.tar.gz
8834a33c92dfe6ba8903e6715caeaa25dff4657e703c54cd06ec113493e2c3c2  node-v18.7.0.tar.xz
8af2dd97c0250287960cca4604337ed2e0e0a3b1d414cef5f68056a84f5966ee  node-v18.7.0-win-x64.7z
9c0abfe32291dd5bed717463cb3590004289f03ab66011e383daa0fcec674683  node-v18.7.0-win-x64.zip
ef934a8315044b7588fe6762e73d3175a08c9ef4f1288b97f11520b1fc3ef172  node-v18.7.0-win-x86.7z
26bad4738fe5f3c69aff5f9ccaaddd4fac3885e38f742e3e39f5da24fb4b7332  node-v18.7.0-win-x86.zip
6ebd9c15f87ced5af4760383c3b6aa31395ded0d4decbf61c008a4855f1ce4d3  node-v18.7.0-x64.msi
4bcb571dbb7d1b436e29a9bc1cc0afdba24a6e369e62c4313134af61054be833  node-v18.7.0-x86.msi
e12623ab72eed023f1920db52654cad72de8c22c1ee4a3d9c6efc1519a557b02  win-x64/node.exe
ea47f748e3103867a877c6f6bb48224a3d0d71309ba31789807ac52fd9af6b14  win-x64/node.lib
a24a1d6d9187caaa992d71b4dfe6e7baed405e281a229124e2c601edaa31feb0  win-x64/node_pdb.7z
98baeb194f86511b90fb87ca84b37305137b5c105e928ff0e7e68e02f1f12f9d  win-x64/node_pdb.zip
1292e7d6c2465602b35741246cbfa330080db0712913d70f648737fed2317eea  win-x86/node.exe
158bc043ed958d1590b5f170dd84aa89c9593bb22595406dae0294a505da1480  win-x86/node.lib
033ed5581f738d077b407cafa2214c13c2a6dabe744f93263271f58af6ec7846  win-x86/node_pdb.7z
0c9150109cf5b3d7ec96e3b933a0831313f34998df1e2380c6b328e7e55598c8  win-x86/node_pdb.zip
-----BEGIN PGP SIGNATURE-----

iQIzBAEBCAAdFiEEdPEmArbxxOkT+qN606iWE2Q7YgEFAmLgaGQACgkQ06iWE2Q7
YgGKhRAAjkHnEUNDxjnG9gpCnHUFGiiMOTwXfjFd04Wf9WUgZGrk6txuyMAgr4OM
cUPuf/+lU9KW1Jab2h2WXc+/jKIK0CmFEL4mx3buGO+rlltGPkGJF82zwtwkzWlA
Fnsz94vfKws4RL8qiwztj1OCfTazHiSEQyj/5C2tCEPyiJehwikkOPUBulyeuu5S
nuQr3yB/4v+RJStlGmcL9zcWh3/Popsv3WQ6LPoBTP71Y2ZDj3zwtViKIZ/X2slD
VxAaZpgI58jN8uPGwJDsOTAOBhqsI7w5d04j67XRNSR3dzIGlRKgBQ7PDrd9xjtP
TlF8zd8gTlQdk6gRGcb6Qj+k1UxYRMJtt3MjDUDKm+Dvz5qL8mvmP7i8uiz7NbqS
ke8q/XOdsqmSpZK1Np7DB8zfM6tXWqlOxNLdR7O3++5F+hI/mhgYXdWj022INLmr
ctqcXAsHfJqOL5l75vgKd2Ihp5ejHsXCud9ITb8bpQPUOo50UjSCku+jwNZ9J3u3
foF0YGYLGFYQIoip7Qz+rkOmBaWjTjBmdgiY+pmhchMHHHLLlBLVsAbswHCnvr7w
Yf0CRQEkrg80tqLB3ioJDzciG0lWu4acnt7Ip3lwH/cISIL2DdHHx8+vx/2hqooZ
u05y+0hseMT3etmuypi6Ie4X+hbINrq/AkgNXLbHT1DjO907rtU=
=+8aX
-----END PGP SIGNATURE-----

```
