---
date: '2016-09-12T16:00:00.000Z'
category: announcements
title: Node.js Foundation Announces Schedule for Second Annual Node.js Interactive North America
layout: blog-post
author: The Node.js Project
---

> IBM, Netflix, Microsoft, and leading community experts to showcase the current and future of Node.js

**SAN FRANCISCO, September 12, 2016** — [The Node.js Foundation](https://foundation.nodejs.org/), a community-led and industry-backed consortium to advance the development of the Node.js platform, today announced the keynotes and agenda for Node.js Interactive North America, November 29 - December 2, 2016, in Austin, TX. The event attracts enterprise users, developers, and community stakeholders, providing them with the tools and training they need to optimize the Node.js platform.

With almost 5 million users a month and adoption across numerous industries, Node.js is a universal platform for web applications, IoT, enterprise application development, and microservice architectures. Its liberal contribution policies have also allowed the platform to increase the number of contributors working on the project by a sustained 100% year-over-year growth for the last several years.

Node.js Interactive offers a unique mix of skill-building and knowledge-sharing sessions, panels, and workshops to help developers accelerate their use of Node.js. It is the only vendor-neutral event that offers the community better insight into Node.js and its working groups, combined with best practices and instruction on how to improve performance, debugging, security, and tooling for mainstream enterprise users.

The event is designed to appeal to both experienced and new developers and architects with a NodeSchool event happening during the conferences. Additionally, NodeTogether, a beginner tutorial event that also aims to improve diversity within the Node.js community, is holding a special teachers training at the event.

“Node.js has clearly become a high-priority platform for digital transformation. Node.js Interactive will have a roster of expert technical and community speakers who will discuss the future of Node.js and new growth in areas like artificial intelligence, cloud native architectures, container-packaged applications and more,” said Mikeal Rogers, community manager of the Node.js Foundation.

Keynotes for the conference will provide an update and future look at Node.js and its growing ecosystem of related modules. A sampling of keynotes includes:

- “The Road Forward on Education and Diversity” - Tracy Hinds of the Node.js Foundation and Emily Rose of Salesforce
- “npm State of the Union” - Ashley Williams of npm
- “Node.js State of the Union” - Rod Vagg of NodeSource and Technical Steering Committee Director of Node.js Foundation
- “Express State of the Union” - Doug Wilson, Express lead maintainer

Experts from the leading open source projects and enterprises will share their expertise with Node.js and JavaScript in areas ranging from artificial intelligence to full stack development. Highlights include:

### Node.js Everywhere

- “Node.js Releases, How Do They Work?” - Myles Borins of IBM
- “Slaying Monoliths with Docker and Node.js” - Yunong Xiao of Netflix
- “Instrumentation and Tracing in Node.js” - Thomas Watson of Opbeat
- “Surviving Web Security Using Node.js” - Gergely Nemeth of RisingStack
- “Writing Secure Node Code: Understanding and Avoiding the Most Common Node.js Security Mistakes” - Guy Podjarny of Snyk

### Cloud and Back End

- “Hitchhiker’s Guide to ‘Serverless’ JavaScript” - Steven Faulkner of Bustle
- “Making Magic in the Cloud with Node.js at Google” - Justin Beckwith of Google
- “Buzzword Bingo: Architecting a Cloud-Native Internet Time Machine” - Ross Kukulinski of NodeSource

### Diagnosing, Debugging, and DevOps

- “Building and Shipping Node.js Apps with Docker” - Mano Marks of Docker
- “The Morality of Code” - Glen Goodwin of SAS Institute, Inc.

### Machine Learning, Big Data, Artificial Intelligence

- “Real-Time Machine Learning with Node.js” - Phillip Burckhardt of Carnegie Mellon University
- “Math in V8 is Broken and How We Can Fix It” - Athan Reines of Fourier

### Node.js Core

- “Contributing to Node.js: Coding Not Required” - William Kapke of Kap Co, LLC
- “A Beginner’s Guide To Reading Node.js Core Source” - Rich Trott of University of California, San Francisco
- “Node.js and ChakraCore” - Arunesh Chandra of Microsoft
- “Implementing HTTP/2 for Node.js Core” - James Snell of IBM

### The New Full Stack

- “Serverless Front-End Deployments using npm” - Charlie Robbins of GoDaddy
- “API Design Through the Lens of Photography” - Bryan Hughes of Microsoft
- “JavaScript will Let Your Site Work without JavaScript” - Sarah Meyer of Buzzfeed
- “Nodifying the Enterprise” - Shweta Sharma from To The New
- “Full Stack Testing with Node.js” - Stacy Kirk of Quality Works

### IoT

- “IoT & Developer Happiness” - Emily Rose of Salesforce
- “Taking on Genetically Evolving Cellular Automata with JavaScript” - Irina Shestak of Small Media Foundation

### Operations and Performance

- “Scaling State” - Matteo Collina of nearForm
- “Don't Let Just Node.js Take the Blame!” - Daniel Khan of Dynatrace

### Workshops

- “Games as Conversational Interfaces” - Kevin Zurawel of Braintree
- “Agile Security for Web Developers” - Kim Carter of BinaryMist
- “Science Meets Industry: Online Behavioral Experiments with nodeGame” - Stefano Balietti of Northeastern University
- “Building Desktop Applications With Node.js Using Electron” - Steve Kinney of Turing School of Software and Design

Free onsite childcare for attendees is available as well as ASL, interpretation and transcription assistance upon request. <NAME_EMAIL> for more information.

Node.js Interactive is made possible by support from Platinum Sponsors IBM and Google Cloud Platform; Gold Sponsors nearForm and NodeSource; Silver Sponsors GoDaddy, Langa, Opbeat, Rollbar, and Sauce Labs; and Bronze Sponsors Codiscope, Sqreen, and Stormpath. If you are interested in sponsoring please contact Todd <NAME_EMAIL>.

For attendees who register before November 14, the standard registration fee is $600; registration increases to $800 after November 14. Visit [here](http://events.linuxfoundation.org/events/node-interactive) to register.

### About the Node.js Foundation

Node.js is used by tens of thousands of organizations in more than 200 countries and amasses more than 4.5 million active users per month. It is the runtime of choice for high-performance, low latency applications, powering everything from enterprise applications, robots, API engines, cloud stacks, and mobile websites.

The Foundation is made up of a diverse group of companies including Platinum members IBM, Intel, Joyent, Microsoft, PayPal, and Red Hat. Gold members include GoDaddy and NodeSource, and Silver members include Apigee, AppDynamics, Cars.com, Codefresh, DigitalOcean, Dynatrace, Fidelity, Google, Groupon, nearForm, New Relic, npm, Opbeat, RisingStack, Sauce Labs, SAP, StrongLoop (an IBM company), Sphinx, YLD, and Yahoo!. Get involved here: https://nodejs.org.

### Media Contact

Zibby Keaton
Node.js Foundation
<EMAIL>
