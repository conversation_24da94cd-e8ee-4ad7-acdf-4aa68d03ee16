---
date: '2016-04-12T13:00:00.000Z'
category: announcements
title: 'New Node.js Foundation Survey Reports New “Full Stack” In Demand Among Enterprise Developers'
layout: blog-post
author: The Node.js Project
---

> Nearly 50 percent of Node.js developers surveyed using container technology, strong growth emerges in cloud, front end, mobile and devices

**SAN FRANCISCO, April, 12, 2016** — [The Node.js Foundation](http://ctt.marketwire.com/?release=11G082331-001&id=8448115&type=0&url=https%3a%2f%2fnodejs.org%2fen%2ffoundation%2f),
a community-led and industry-backed consortium to advance the development of the Node.js
platform, today announced the availability of its first ever Node.js User Survey Report.

With over 3.5 million users and an annual growth rate of 100 percent, Node.js is emerging as
a universal platform used for web applications, IoT, and enterprise. The Node.js User Survey
report features insights on emerging trends happening in this massive community that serves
as a leading indicator on trends like microservices architectures, real-time web applications,
Internet of Things (IoT). The report paints a detailed picture of the technologies that are
being used, in particular, with Node.js in production and language preferences (current and
future) for front end, back end and IoT developers.

## Key findings from the Node.js Foundation survey

### Node.js and Containers Take Off Together

Both Node.js and containers are a good match for efficiently developing and deploying
microservices architectures. And, while the surge in container use is relatively new, **45
percent of developers that responded to the survey use Node.js with the technology**. Other
container-related data points:

- 58 percent of respondents that identified as IoT developers use Node.js with Docker.
- 39 percent of respondents that identified as back end developers use Node.js with Docker.
- 37 percent of respondents that identified as front end developers use Node.js with Docker.

### Node.js — the Engine that Drives IoT

JavaScript and Node.js have risen to be the language and platform of choice for IoT as both
are suited for data intensive environments that require parallel programming without
disruption. JavaScript, including Node.js and frameworks, such as React, have become the de
facto choice of developers working in these connected, device-driven environments with **96
percent of IoT respondents indicating they use JavaScript/Node.js for development**.

“Data about developer choices is catnip for developers,” said James Governor, RedMonk
co-founder. “In this survey, the Node.js Foundation identifies some interesting results,
notably about languages programmers are using alongside Node.js and IoT demographics.”

These environments are challenging, and the survey revealed that on average, IoT developers
using Node.js have more experience than their front end and back end counterparts with more
than 40 percent of IoT developers surveyed having over 10+ years of development experience.

Additionally, although Docker is a server technology, many IoT developers (58%) are using
Node.js with Docker compared to only 39 percent of back end developers. This metric is
significant as it means that the new IoT world also is quickly adopting containers and
microservices.

### Node.js Becoming Universal Platform

**The full stack is no longer “front end and back end,” but rather “front end, back end and
connected devices,”** which is a combination of everything from the browser to a toaster all
being run in JavaScript and enabled by Node.js. The survey revealed that 62 percent of
respondents are using Node.js for both front end and back end development, and nearly 10
percent are using Node.js for front end, back end, and IoT development.

### Node.js Pervasive in Enterprises

Node.js is increasingly used in the enterprise, and used within huge enterprises like PayPal,
Go Daddy, Capital One, and Intel. The survey found:

- **More than 45 percent already using the Node.js Long Term Support release (v4) geared
  toward medium to large enterprise users who require stability and high performance.**
- Of those who haven’t upgraded, 80 percent report definite plans to upgrade to v4, with half
  of respondents planning to do so this year.
- Strong interest in enterprise tooling among 34 percent of tech leaders.

### Full “MEAN” Stack Explodes

The popularity of real-time, social networking and interactive game applications is pushing a
new stack among developers. The MEAN stack is able to handle lots of concurrent connections
and extreme scalability, which these applications demand. Node.js, in combination with
MongoDB, Express, AngularJS, allows developers to tackle the needs of front end and back end
development. Not surprisingly, all of these technologies were commonly used alongside
Node.js. **Express, cited the most, is used by an average of 83 percent of developers**.

### Popularity of JavaScript and Node.js

JavaScript and Node.js were popular among back end, front end, and IoT developers. Other
languages, beyond JavaScript, that were popular for all three developer types included PHP,
Python and Java. However, when looking to the future, back end, front end and IoT developers
planned to decrease their use of Java, .Net and PHP (PHP averages a 15% decrease) and
increase the use of Python and C++.

## About the Survey

The survey was open for 15 days, from January 13 to January 28, 2016. During this time, 1,760
people from around the world completed the survey. Seventy percent were developer's, 22
percent technical management and 64 percent run Node.js in production. Geographic
representation of survey covered: 35 percent from United States, 22 percent from Continental
Europe, 6 percent India, and 6 percent from United Kingdom with the remaining respondents
hailing from Asia, Latin America, Africa, Russia and the Middle East.

**Additional Resources:**

- [Node.js Foundation User survey infographic](/static/documents/2016-survey-infographic.png)
- [Report summarizing Node.js Foundation User Survey 2016](/static/documents/2016-survey-report.pdf)

**About Node.js Foundation**

Node.js is used by tens of thousands of organizations in more than 200 countries and amasses
more than 3 million active users per month. It is the runtime of choice for high-performance,
low latency applications, powering everything from enterprise applications, robots, API
engines, cloud stacks and mobile websites.

The Foundation is made up of a diverse group of companies including Platinum members Famous,
IBM, Intel, Joyent, Microsoft, PayPal and Red Hat. Gold members include GoDaddy, NodeSource
and Modulus/Progress Software, and Silver members include Apigee, AppDynamics, Codefresh,
DigitalOcean, Fidelity, Google, Groupon, nearForm, New Relic, npm, Opbeat, RisingStack, Sauce
Labs, SAP, StrongLoop (an IBM company), Sphinx, YLD!, and Yahoo!. Get involved here:
<https://nodejs.org/>.
