---
date: '2015-10-20T17:00:00.000Z'
category: announcements
title: Node.js Foundation Announces Programming For Node.js Interactive
layout: blog-post
author: The Node.js Project
---

> Inaugural Conference to Advance the Use of Node.js Within Backend, Frontend, IoT Applications

SAN FRANCISCO, Oct. 20, 2015 – [The Node.js Foundation](https://foundation.nodejs.org/), a community-led and industry-backed consortium to advance the development of the Node.js platform, today announced initial programming for [Node.js Interactive](http://events.linuxfoundation.org/events/node-interactive). This inaugural event, which is being led by the newly formed Node.js Foundation in cooperation with the Linux Foundation, will be held December 8-9, 2015, in Portland, Ore.

Node.js has become ubiquitous in almost every ecosystem in technology and is consistently being used more in mainstream enterprises. To continue to evolve the platform, Node.js Interactive brings together a wide range of community, projects, products and companies to create an educational and collaborative space. With more than 700 attendees expected, Node.js Interactive will provide a way to network with other developers and engineers within this diverse community.

Node.js Interactive will also focus on three tracks: Frontend, Backend and the Internet of Things (IoT); talks for each track were selected in collaboration with track chairs [<PERSON>](https://github.com/jlord/) (Frontend), [C <PERSON>](https://github.com/ceejbot) (Backend) and [Kassandra Perch](https://github.com/nodebotanist) (IoT). A few highlights include:

Frontend Session Highlights:

- JavaScript, For Science! _with_ Max Ogden, Computer Programmer for Dat Project
- Making Your Node.js Applications Debuggable _with_ Patrick Mueller, Senior Node Engineer at NodeSource
- Node Intl: Where We Are, What's Next _with_ Steven Loomis, Senior Software Engineer at IBM
- Rapid Development of Data Mining Applications in Node.js _with_ Blaz Fortuna, Research Consultant for Bloomberg L.P., Senior Researcher at Jožef Stefan Institute and Partner at Quintelligence
- Real-Time Collaboration Sync Strategies _with_ Todd Kennedy, CTO of Scripto
- Rebuilding the Ship as It Sails: Making Large Legacy Sites Responsive _with_ Philip James, Senior Software Engineer at Eventbrite

Backend Session Highlights:

- Building and Engaging High-Performance Teams in the Node.js Ecosystem _with_ Chanda Dharap, Director of Engineering at StrongLoop, an IBM company
- Microservice Developer Experience _with_ Peter Elger, Director of Engineering at nearForm
- Modernizing Winston for Node.js v4 _with_ Charlie Robbins, Director of Engineering UX Platform at GoDaddy
- Node.js API Pitfalls, Can You Spot Them? _with_ Sam Roberts, Node/Ops Developer at StrongLoop, an IBM Company
- Node.js Performance Optimization Case Study _with_ Bryce Baril, Senior Node Engineer at NodeSource
- Resource Management in Node.js _with_ Bradley Meck, Software Engineer at NodeSource

IoT Session Highlights:

- Contributing to Node Core _with_ Jeremiah Senkpiel, Node Core Contributor at NodeSource
- Hands on Hardware Workshop _with_ Tessel with Kelsey Breseman, Engineering Project Manager at 3D Robotics and Steering Committee Member and Board Co-Creator of Tessel Project
- Internet of Cats _with_ Rachel White, Front-End Engineer for IBM Watson
- IoT && Node.js && You _with_ Emily Rose, Senior Software Engineer at Particle IO
- Node.s Bots at Scale _with_ Matteo Collina, Architect at nearForm
- Node.js Development for the Next Generation of IoT _with_ Melissa Evers-Hood, Software Product Line Manager at Intel Corporation
- Node.js While Crafting: Make Textile to Compute! _with_ Mariko Kosaka, JavaScript Engineer at Scripto

“Node.js has become pervasive within the last few years, with so many community accomplishments to highlight, including forming the new Node.js Foundation and the convergence of io.js and Node.js,” said Mikeal Rogers, Community Manager, Node.js Foundation. “We created this conference to help showcase this growth, to accommodate the Node.js community’s many different needs, and to help accelerate adoption as it expands into enterprises.”

Early bird registration ends October 23, 2015. Standard registration closes November 21, 2015, after which the conference price will increase from $425 to $525. Discounted hotel rates are also available until Wednesday, November 11, 2015. To register visit [https://www.regonline.com/Register/Checkin.aspx?EventID=1753707](https://www.regonline.com/Register/Checkin.aspx?EventID=1753707).

Node.js Interactive is made possible by platinum sponsor IBM, gold sponsor Microsoft, and silver sponsors NodeSource and nearForm.

Additional panels and keynotes will be announced in the coming weeks; to see the initial program visit: [http://nodejspdx2015.sched.org](http://nodejspdx2015.sched.org). For more information visit [http://events.linuxfoundation.org/events/node-interactive](http://events.linuxfoundation.org/events/node-interactive).

Additional Resources

Learn more about the [Node.js Foundation](https://foundation.nodejs.org/), and get involved with [the project](/about/get-involved/).
Want to keep abreast of Node.js Foundation news? Sign up for our newsletter at the bottom of the [Node.js Foundation page](https://foundation.nodejs.org/).
Follow on [Twitter](https://twitter.com/nodejs?ref_src=twsrc%5Egoogle%7Ctwcamp%5Eserp%7Ctwgr%5Eauthor) and [Google+](https://plus.google.com/u/1/100598160817214911030/posts).

About Node.js Foundation Node.js Foundation is a collaborative open source project dedicated to building and supporting the Node.js platform and other related modules. Node.js is used by tens of thousands of organizations in more than 200 countries and amasses more than 2 million downloads per month. It is the runtime of choice for high-performance, low latency applications, powering everything from enterprise applications, robots, API engines, cloud stacks and mobile websites.

The Foundation is made up of a diverse group of companies including Platinum members Famous, IBM, Intel, Joyent, Microsoft, PayPal and Red Hat. Gold members include GoDaddy, NodeSource and Modulus/Progress Software, and Silver members include Apigee, Codefresh, DigitalOcean, Fidelity, Groupon, nearForm, npm, Rising Stack, Sauce Labs, SAP, and YLD!. Get involved here: <https://nodejs.org/>.
The Node.js Foundation is a Collaborative Project at The Linux Foundation. Linux Foundation Collaborative Projects are independently funded software projects that harness the power of collaborative development to fuel innovation across industries and ecosystems. [https://foundation.nodejs.org/](https://foundation.nodejs.org/)
