---
date: '2021-10-07T20:00:00.000Z'
category: announcements
title: Retiring the Node.js Community Committee
layout: blog-post
author: <PERSON><PERSON>
---

# Retiring the Node.js Community Committee

**tl;dr:** we're going to be retiring the Node.js Community Committee, moving our existing Initiatives to exist under the Node.js Technical Steering Committee (TSC).

From the Community Committee's side, we've seen a convergence of our initiatives' goals with the goals of the work that is generally under the TSC. Further, we've seen a decline in the number of people who can consistently dedicate the necessary amount of time/energy. As such, separation between the TSC and Community Committee has become more of a barrier to accomplishing our collective goals rather than the helpful and necessary construct it once was.

## The Past

I want to start with a bit of history as a form of preservation of the context I've collected as the Community Committee's chair for the majority of its existence.

On January 11th, 2017, [<PERSON>](https://twitter.com/hackygolucky) made the first commit to the [nodejs/community-committee](https://github.com/nodejs/community-committee/commit/1902cdc71c8e62e65a5bab4cca9a21d5b5e744c0) repository. This commit was the result of in-person discussion with a number of key Node.js community members at the 2016 North America Collaborator Summit in Austin, Texas, though there'd been a rising discourse to push for something like it for some time in various forums including the (now-archived) [Inclusivity WG](https://github.com/nodejs/inclusivity).

The stated goal of the Community Committee has been to be a _top-level_ commitment of and investment in the Node.js project to support community efforts.

At the time, this was particularly important. Node.js as a project was still figuring out its identity as a project independent from a Corporation while existing in a neutral Foundation. The Node.js community was foundational not only in the project's success - be it in the v0.x era in the early 2010s, during the io.js fork, or post-reunification - and those starting the Committee wanted to be sure that we were effectively representing and enabling that from the project directly.

## The Present

Since the creation of the Node.js Community Committee, members have largely spent project and committee time on outward-facing efforts with the goal of continuing to enable and grow the Node.js community. There've been multiple facets to this approach, some of which have been relatively successful and others that have been entirely unsuccessful.

The broad trend that I've personally witnessed is that the Community Committee's interest and activity have slowed dramatically since its inception. My perception is that this is due to a couple of different factors:

- **Sponsorship and Investment:**
  - A majority of work in Node.js is done by people who can dedicate non-trivial amounts of their paid time to progress the project, sponsored by their employer. Initially, there was already a small number of people who were able to focus their employer-sponsored time on "community" work - work that doesn't ship features they need - in Node.js, and that number has only gone down over time.
    - I _do not_ think that this will be a universal experience in open source. Several other massive-scale projects are relatively successful in approaching this. They also have fundamentally different models and investment than Node.js does, which is likely a contributing factor to their sustainability. The [Electron Outreach WG](https://github.com/electron/governance/tree/main/wg-outreach) and the [Kubernetes Contributor Experience SIG](https://github.com/kubernetes/community/tree/master/sig-contributor-experience) are both good examples of "success" here, in my opinion.
  - In general, JavaScript occupies a relatively unique space. It is ubiquitous, yet very few companies are willing to substantially invest time, energy, and resources into it despite their reliance on it. Lack of investment into community sustainability is one facet of this.
- **Necessity:**
  - The Node.js Community Committee was created at a time in which the Node.js project was larger, with louder voices sharing relatively differing opinions on how we should approach the future. The reality is that we're smaller now than we were then, and there's generally less conflict around how we should approach community, safety, and governance. As such, the necessity for a distinct "community" focus is not only less but - in my opinion - actively detrimental to progress. It splits the project's collaborators into different, disconnected groups rather than unifying the project towards the same goal.
  - The Node.js Community Committee was also created at a time when Node.js was relatively alone. Under the Node.js Foundation, we had to do a lot of community organization within the project directly. Under the OpenJS Foundation, we have shifted several initiatives that the Community Committee was charged with under the Node.js foundation up to the OpenJS Foundation Cross-project Council. As such, certain tasks that we initially envisioned being core to the Community Committee are now living in a different home.

## The Future

I don't believe that retiring the Node.js Community Committee means we'll see a lack of investment in the community from the Node.js project.

Rather, I think it's an enabling function for Node.js to continue to **sustainably** invest in the community. This means fewer barriers, more connectedness, and allowing for resilience in the ebb and flow of those who can invest to do so.

I look forward to what we'll collectively work on next.

## A Big, Heartfelt Thanks

Over the years, we've seen [dozens of people](https://github.com/nodejs/community-committee/graphs/contributors) contribute to the Node.js Community Committee and [its initiatives](https://github.com/nodejs/community-committee#current-initiatives), and I'd like to be explicit:

Y'all have been lovely over the past five years. You all care deeply about the Node.js ecosystem, community, and _people_. It's been truly a privilege to have the opportunity to work on something like this with each and every one of you.

As we retire the CommComm, I hope that you see this as yet another evolution of the Node.js project's commitment to the community... just as the CommComm itself was.
