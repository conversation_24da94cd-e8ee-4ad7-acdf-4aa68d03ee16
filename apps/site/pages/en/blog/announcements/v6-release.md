---
date: '2016-04-26T12:00:00.000Z'
category: announcements
title: 'World’s Fastest Growing Open Source Platform Pushes Out New Release'
layout: blog-post
author: The Node.js Project
---

> New “Current” version line focuses on performance improvements, increased reliability and
> better security for its 3.5 million users

SAN FRANCISCO, April, 26, 2016 — [The Node.js Foundation](http://ctt.marketwire.com/?release=11G082331-001&id=8448115&type=0&url=https%3a%2f%2fnodejs.org%2fen%2ffoundation%2f), a
community-led and industry-backed consortium to advance the development of the Node.js
platform, today announced the release of Node.js version 6 (Node.js v6). This release
provides major performance improvements, increased reliability and better security.

With over 3.5 million users and an annual growth rate of 100 percent, Node.js is emerging as
a universal platform used for web applications, IoT, mobile, enterprise application
development, and microservice architectures. The technology is ubiquitous across numerous
industries, from startups to Fortune 500 companies, and is the only unified platform that
full stack JavaScript developers can use for front end, back end, mobile and IoT projects.

Performance improvements are key in this latest release with one of the most significant
improvements coming from module loading, which is currently four times faster than Node.js
version 4 (Node.js v4). This will help developers dramatically decrease the startup time of
large applications for the best productivity in development cycles and more seamless
experience with end users. In addition, Node.js v6 comes equipped with V8 JavaScript engine
5.0, which has improved ECMAScript 2015 (ES6) support. Ninety-three percent of
[ES6](https://node.green/) features are also now supported in the Node.js v6 release, up from
56 percent for Node.js v5 and 50 percent for Node.js v4. Key features from ES6 include:
default and rest parameters, destructuring, class and super keywords.

Security is top-of-mind for enterprises and startups alike, and Node.js v6 has added several
features that impact security, making it easier to write secure code. The new Buffer API will
reduce the risk of bugs and vulnerabilities leaking into applications through a new
constructor method used to create Buffer instances, as well as a zero-fill-buffers
command-line flag. Using the new command line flag, developers can continue to safely use
older modules that have not been updated to use the new constructor API. In addition, V8 has
improved their implementation of Math.random() to be more secure — this feature is added into
Node.js v6.

“The Node.js Project has done an incredible job of bringing this version to life in the
timeline that we initially proposed in September 2015. It’s important for us to continue to
deliver new versions of Node.js equipped with all the cutting-edge JavaScript features to
serve the needs of developers and to continue to improve the performance and stability
enterprises rely on,” said Mikeal Rogers, Community Manager of the Node.js Foundation. “This
release is committed to Long Term Support, which allows predictable long-term stability,
reliability, performance and security to the growing number of enterprise users that are
adopting Node.js as a key technology in their infrastructure.”

To increase reliability of Node.js, there has been increased documentation and testing done
around Node.js v6 for enterprises that are using and looking to implement the platform.

Node.js release versioning follows the Semantic Versioning Specification, a specification for
version numbers of software libraries similar to dependencies. Under the Node.js’ [Long-Term
Support (LTS)](https://github.com/nodejs/LTS/), version 6 is now the “Current” release line
while version 5 will be maintained for a few more months. In October 2016, Node.js v6 will
become the LTS release and the LTS release line (version 4) will go under maintenance mode in
April 2017, meaning only critical bugs, critical security fixes and documentation updates
will be permitted. Users should begin transitioning from v4 to v6 in October when v6 goes
into LTS.

Additional Resources

- [Download version 6](https://nodejs.org/download/release/v6.0.0/)
- [Download version 4](/download/)
- [Technical blog with additional new features and updates](/blog/)

About Node.js Foundation
Node.js is used by tens of thousands of organizations in more than 200 countries and amasses
more than 3.5 million active users per month. It is the runtime of choice for
high-performance, low latency applications, powering everything from enterprise applications,
robots, API engines, cloud stacks and mobile websites.

The Foundation is made up of a diverse group of companies including Platinum members Famous,
IBM, Intel, Joyent, Microsoft, PayPal and Red Hat. Gold members include GoDaddy, NodeSource
and Modulus/Progress Software, and Silver members include Apigee, AppDynamics, Codefresh,
DigitalOcean, Fidelity, Google, Groupon, nearForm, New Relic, npm, Opbeat, RisingStack, Sauce
Labs, SAP, StrongLoop (an IBM company), Sphinx, YLD!, and Yahoo!. Get involved here:
<https://nodejs.org/>.
