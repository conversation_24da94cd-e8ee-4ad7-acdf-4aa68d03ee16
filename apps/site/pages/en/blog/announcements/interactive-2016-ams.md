---
date: '2016-06-29T12:00:00.000Z'
category: announcements
title: Node.js Foundation Announces Keynotes and Programming for Node.js Interactive Europe
layout: blog-post
author: The Node.js Project
---

> Event provides neutral forum for learning about the future of Node.js and JavaScript from the community and enterprise alike

**SAN FRANCISCO, June 29, 2016** – [Node.js Foundation](https://foundation.nodejs.org/), a community-led and industry-backed consortium to advance the development of the Node.js platform, today announced the initial programming for Node.js Interactive Europe, September 15 -16, 2016, in Amsterdam, Netherlands. The event will showcase workshops, community and technical talks, and use cases that will inform the future development of Node.js and JavaScript.

With 4 million users a month and adoption across numerous industries, Node.js is emerging as a universal platform used for web applications, IoT, enterprise application development and microservice architectures. This marquee event attracts enterprise users, developers and community stakeholders, providing them with a unique opportunity for cross-disciplinary discussions that are aimed to provide new insights and new opportunities around Node.js development.

“We’ve hand-selected a range of presenters and content that will showcase the future of Node.js and how pervasive it has become in the market through both a community and enterprise lens,” said <PERSON><PERSON>, community manager of Node.js Foundation. “This is a perfect conference if you are a front end, back end, mobile, IoT or full stack developer.”

The keynotes will focus on the future of Node.js and corresponding technologies. The initial keynotes include:

- **Ashley Williams**, Node.js Foundation community board chair, founder of NodeTogether, and developer community and content manager at npm
- **Doug Wilson**, Express lead maintainer
- **James Snell**, IBM engineer and Node.js Foundation TSC member
- **Kat Marchán**, CLI engineer at npm
- **<PERSON>al Rogers**, community manager at the Node.js Foundation

Experts from the world’s leading companies and most important open source projects will deep dive into tracks ranging from artificial intelligence to security. A sampling of this year’s sessions include:

## Cloud and Back End

- Node.js and Containers go together like Peanut Butter and Jelly from **Ross Kukulinski of NodeSource**
- Building the Node.js Global Distribution Network from **Guillermo Rauch creator of Socket.io**
- SWIMming in the microservices Ocean from **Luca Maraschi of Sporti and nearForm**

## Diagnosing, Debugging, and DevOps

- Instrumentation and Tracing in Node.js from **Thomas Watson of Opbeat**
- The Cost of Logging from **Matteo Collina of nearForm**

## Machine Learning, Big Data, Artificial Intelligence

- Taking on Genetically Evolving Cellular Automata with JavaScript from **Irina Shestak of Small Media Foundation**
- From Pterodactyls and Cactus to Artificial Intelligence by **Ivan Seidel of Tenda Digital**

## Node.js Core

- Keeping the Node.js Community Infrastructure Humming: An Update from the Build Workgroup from **Michael Dawson of IBM**
- Creating Native Addons - General Principles from **Gabriel Schulhof of Intel**
- The CITGM Diaries from **Myles Borins of IBM**

## Security

- FIPS Comes to Node.js from **Stefan Budeanu of IBM**
- Take Data Validation Seriously from **Paul Milham of WildWorks**

## IoT

- Node.js on Hardware: Where We Are, Where We're Going, and How We'll Get There from **Kassandra Perch of NodeBots**
- Why did the robot cross the road? Computer vision, robots and mobile games from **Pawel Szymczykowski of Wedgies**
- The Future is Now: How to Realize your New Potential as a Cyborg from **Emily Rose of Salesforce**

## Node.js Everywhere

- Bitcoin, Blockchain and Node from **Portia Burton of The Atlantic**
- Node.js and the African Market from **Ogatcha Prudence of Pilby**
- The Radical Modularity from **Aria Stewart of npm**

## Workshops

- Deploying Node.js applications using plain old Linux from **Luke Bond of YLD**
- Build a real-time multiplayer chess game with Socket.io from **David Washington of Microsoft**
- Isomorphic JavaScript with React + Express from **Azat Mardan of Capital One**

The event will provide free onsite childcare for attendees and offers ASL, interpretation and transcription assistance upon request. The Node.js Foundation is offering three diversity scholarships this year. More information can be found [here](http://events.linuxfoundation.org/events/node-interactive-europe/attend/diversity-scholarship).

Node.js Interactive is made possible by support from Platinum Sponsor IBM; Gold Sponsors nearForm and YLD; and Silver Sponsor Opbeat. If you are interested in sponsoring please contact Todd <NAME_EMAIL>.

For attendees who register before July 4th, the early bird registration fee is $400. Visit [here](https://www.regonline.com/Register/Checkin.aspx?EventID=1811779) to register.

## About the Node.js Foundation

Node.js is used by tens of thousands of organizations in more than 200 countries and amasses more than 4 million active users per month. It is the runtime of choice for high-performance, low latency applications, powering everything from enterprise applications, robots, API engines, cloud stacks and mobile websites.

The Foundation is made up of a diverse group of companies including Platinum members IBM, Intel, Joyent, Microsoft, PayPal and Red Hat. Gold members include GoDaddy and NodeSource, and Silver members include Apigee, AppDynamics, Codefresh, DigitalOcean, Fidelity, Google, Groupon, nearForm, New Relic, npm, Opbeat, RisingStack, Sauce Labs, SAP, StrongLoop (an IBM company), Sphinx, YLD, and Yahoo!. Get involved here: <https://nodejs.org/>.
