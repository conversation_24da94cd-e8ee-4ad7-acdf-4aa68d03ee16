---
date: '2020-04-03T20:26:28.000Z'
category: announcements
title: Changes to Release Schedule
layout: blog-post
author: <PERSON>
---

The Node.js project will be adjusting its release cadence in response to adjusted work schedules.

The stability and reliability of our release lines is tantamount, and as we respond to the shifting situation around us, and the global pandemic, we feel that Node.js users will be more effectively served by this new schedule. Updates to individual release lines are detailed below.

### `v10.x`

The next planned release of `v10.x` will now be on `2020-04-07`.

The Maintenance date for `v10.x` has been pushed back to 2020-05-19, and there may now be another Semver-Patch release for the `v10.x` line before this line enters Maintenance.

### `v12.x`

The schedule for `v12.x` has been strategically delayed in the service of ensuring stability for downstream consumers.

The next planned release of `v12.x` will now be on `2020-04-07`.

We have specifically delayed the Semver-Minor releases of `v12.x` which will now occur on the following dates:

- `12.17.0` on 2020-05-26
- `v12.18.0` on 2020-08-25

`v12.x` remains slated to enter Maintenance on 2020-10-20, but this date is now subject to change as deemed appropriate by the Release Team and with appropriate notice.

### `v13.x`

There are no scheduled changes to the Current release schedule, nor will there be changes to the current End of Life date scheduled for June 2020.

### `v14.x`

There are no changes currently scheduled to the initial release date of `v14.x` on 2020-04-21, and this release line will follow the Current release schedule following initial release.

At this point in time, `v14.x` is still planned to enter LTS on 2020-10-20, but this date is now subject to change as deemed appropriate by the Release Team and with appropriate notice.
