---
date: '2016-11-30T12:00:00.000Z'
category: announcements
title: The Node.js Platform and Node.js Foundation Continue to Grow
layout: blog-post
author: The Node.js Project
---

> With 5.7 million users; increased community participation and a solid Foundation backing: 2016 was a good year for the platform.

**NODE.JS INTERACTIVE 2016, AUSTIN, TX., Nov. 30, 2016** — [The Node.js Foundation](https://foundation.nodejs.org/), a community-led and industry-backed consortium to advance the development of the Node.js platform, today announced [Snyk](https://snyk.io) as a Silver Member, major community and code growth, and the opening of an expansive Node.js user survey.

Founded in 2015, the Node.js Foundation was created to accelerate the development of Node.js and support the large ecosystem that encompasses it through open governance. Membership has grown 30 percent growth since the Foundation’s inception and represents a mix of Fortune 500 companies and startups alike. The newest member Snyk is a security company that finds, fixes and monitors known vulnerabilities in Node.js and Ruby on Rails applications.

“Snyk is focused on securing the vast module ecosystem that makes Node.js one of the most powerful runtime platforms today,” said <PERSON>, CEO and co-founder of Snyk. “We are excited to join the Node.js Foundation as our efforts align with the organization's focus to support and grow Node.js and its module ecosystem.”

With more than 15 million downloads per month and more than a billion package downloads per week, Node.js is considered the biggest open source platform powering everything from web, IoT and desktop applications to microservice architectures. In 2016, the Node.js Project issued 63 new releases with seven different release managers. Node.js version 4 was the most popular release with release downloads increasingly 220% year over year.

From a community growth standpoint, there were more than twice the number of new contributors than in 2015 and 1.5 times the number of unique contributors to the codebase per month compared to 2015.

“The Node.js Project is focused on a new type of open source contribution philosophy, participatory governance, which liberalizes contribution policies and provides more direct ownership to contributors,” said Mikeal Rogers, community manager of the Node.js Foundation. “Through this approach, we’ve seen an explosion in contributor growth, which is critical to sustaining such an important open source project.”

The second Node.js Interactive North America is in full swing with more than 700 developers, DevOps professionals, IoT engineers, engineering managers, and more in Austin. Node.js Interactive brings together a broad range of speakers to help experienced and new technologists better understand the Node.js platform and get insights into the future development of the project.

Attendees are also getting a first look at Node.js advancements announced and demoed this week including:

**The Node.js Foundation** announced progress with efforts to make Node.js VM-neutral - more information on this news can be found on the Node.js Foundation [Medium blog](https://medium.com/@nodejs/ibm-intel-microsoft-mozilla-and-nodesource-join-forces-on-node-js-48e21ffb697d#.jylk1mc0l). This morning, the Foundation announced it would oversee the Node.js Security Project to further improve stability for enterprises. More information [here](http://www.marketwired.com/press-release/nodejs-foundation-to-oversee-nodejs-security-project-to-further-improve-stability-enterprises-2179602.htm).

**NodeSource** announced NodeSource Certified Modules™ to bring security and trust to untrusted, third-party JavaScript. With NodeSource Certified Modules, consumers of the npm ecosystem can now rely on NodeSource as a secure, trusted and verifiable source. Learn more [here](https://certified.nodesource.com/). The team is also demoing its latest [N|Solidv2.0](https://nodesource.com/products/nsolid).

During the conference, the Node.js Foundation also launched **its second user survey**, which will remain open until the end of December. This survey builds on the questions asked in the first Node.js Foundation survey, which was conducted in January 2016, and adds a number of questions designed to shed even more light on who uses Node.js, where they are located, how they learned Node.js, what they use it for, what other technologies they use it with and more.

As with the January survey, the Node.js Foundation will produce and make available publicly for free a report with the survey’s key findings. If you use Node.js, please take the survey and tell your friends and colleagues: [https://www.surveymonkey.com/r/Node16](https://www.surveymonkey.com/r/Node16)

If you are interested in receiving the latest updates from the conference and what’s to come in 2017, be sure to follow [@nodejs](https://twitter.com/nodejs) on Twitter and subscribe to the [Node.js Foundation mailing list](http://go.linuxfoundation.org/l/6342/2015-09-15/2sgqpp).

About Node.js Foundation
Node.js is used by tens of thousands of organizations in more than 200 countries and amasses more than 4.5 million active users per month. It is the runtime of choice for high-performance, low latency applications, powering everything from enterprise applications, robots, API engines, cloud stacks, and mobile websites.

The Foundation is made up of a diverse group of companies including Platinum members GoDaddy, IBM, Intel, Joyent, Microsoft, NodeSource, PayPal, and Red Hat. Silver members include Apigee, AppDynamics, Cars.com, Codefresh, DigitalOcean, Dynatrace, Fidelity, Google, Groupon, nearForm, New Relic, npm, Opbeat, RisingStack, Sauce Labs, SAP, Snyk, StrongLoop (an IBM company), Sphinx, YLD, and Yahoo!. Get involved here: [https://nodejs.org](https://nodejs.org/).

The Node.js Foundation is a Linux Foundation Project, which are independently funded software projects that harness the power of collaborative development to fuel innovation across industries and ecosystems. [www.linuxfoundation.org](http://www.linuxfoundation.org/)
