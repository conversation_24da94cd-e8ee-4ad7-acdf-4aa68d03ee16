---
date: '2025-03-17T10:00:00-04:00'
category: announcements
title: Node.js Launches Official Community Space on Discord
layout: blog-post
author: <PERSON>, <PERSON>
---

First, the news: [The OpenJS Foundation](https://openjsf.org/) and [Reactiflux](https://reactiflux.com/) have collaborated to bring forth an official Discord community for Node.js 🎉 You can [join the Node.js Discord](/discord) now.

Over the past several years, Discord has become the de-facto platform for communities to connect and communicate. Many Node.js community members already use Discord to discuss Node.js, seek advice, and share their projects. By establishing an official Node.js Discord server, we aim to gather these conversations and provide a safe and well-moderated space for our online community to congregate. Lots of other open-source projects, such as [TypeScript](https://discord.gg/typescript), [Rust](https://discord.gg/rust-lang), and [Python](https://discord.gg/python), have successfully built their communities on Discord.

Our Node.js Discord space will be perfect for:

- Hosting livestreams by Node.js Ambassadors and community members
- Facilitating discussions about Node.js, answering questions, and sharing projects
- Connecting with Node.js maintainers, influencers, and contributors
- Receiving the latest updates on Node.js releases and events hosted by the OpenJS Foundation

But this is not a professionally-operated corporate space with full-time staff and a marketing budget, this is an organic community operated by volunteers because they love using JavaScript on the backend. If there's something you feel is missing, shout it out in [#meta-admin](https://discord.com/channels/425824580918181889/425824906882580492) — resources are limited but enthusiasm can go a long way 🫶

### The Journey So Far

Our journey began with [this issue on GitHub](https://github.com/nodejs/admin/issues/872). We partnered with the Reactiflux/Nodeiflux community to rebrand their existing Nodeiflux server as the official Node.js Discord server. Over the past few months, we have been preparing the server for everyone to use, and you can follow some of our progress in the issue.

Reactiflux was one of the first large commuinities on Discord, joining the platform less than 6 months after its publish launch and operating continuously since then. In 2018, one of its administrators started a "sister server" for those using JS on the backend; Nodeiflux!

Now, 7 years on, the Nodeiflux server has joined up with the OpenJS Foundation to serve as the official Node.js Discord server. We are excited to see how our community will thrive in this new environment — the server will be jointly managed by the Node.js project and the Nodeiflux community, with the Node.js Technical Steering Committee (TSC) providing advisory support. The Nodeiflux community will handle the day-to-day administration of the server.

For all intents and purposes, this is an official Node.js space, and we are eager to see how it will grow and evolve. [Join the Node.js Discord](/discord) and become part of our growing community. See you there!

### Looking Ahead

We are eager to see how our community will grow and interact in this new space. We look forward to hosting events and livestreams in the future. If you have any ideas or feedback, please share them in the #discord-feedback channel on the server.

---

A warm hug from [@vcarl](https://github.com/vcarl) and [@ovflowd](https://github.com/ovflowd). We hope to see you on the Node.js Discord server soon!
