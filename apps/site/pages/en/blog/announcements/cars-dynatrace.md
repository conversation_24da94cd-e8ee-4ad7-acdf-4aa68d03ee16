---
date: '2016-08-17T12:00:00.000Z'
category: announcements
title: Cars.com and Dynatrace join the Foundation to support the stability and success of the Node.js platform
layout: blog-post
author: The Node.js Project
---

> New Node.js Foundation Members Drive Enterprise Growth

**SAN FRANCISCO, Aug. 17, 2016** —
[The Node.js Foundation](https://foundation.nodejs.org/), a community-led
and industry-backed consortium to advance the development of the Node.js
platform, today announced [Cars.com](https://www.cars.com/) and
[Dynatrace](https://www.dynatrace.com) have joined the Foundation as
Silver Members.

An ecosystem of tools and services for enterprises is rapidly coalescing around
Node.js, giving it a boost as a universal platform powering everything from
enterprise applications, robots, API engines, cloud stacks and mobile websites.
The JavaScript runtime environment is resource-efficient, high-performing and
well-suited to scalability, making it an increasingly mainstream technology in
startups and enterprises alike for application development and microservice
architectures. Furthermore, Node.js ranks among the Top 10 languages for full
stack, frontend and backend developers surveyed by Stack Overflow in its 2016
developer survey.

More about today’s new Node.js Foundation members:

Launched in 1998 and headquartered in Chicago, [Cars.com](https://www.cars.com/)
is a leading online destination that offers information from experts and
consumers to help car shoppers and owners buy, sell and service their vehicles.
The site offers millions of new and used vehicle listings, an extensive
database of consumer reviews, research and pricing tools, unbiased expert
content, and multiple options to sell a vehicle. Cars.com uses Node.js to help
scale its high-trafficked website to meet its users’ demands and service
expectations.

“Prior to Node.js, we were using older content management solutions that were
not allowing us to effectively meet the demands of Cars.com, which receives
around 30 to 35 million visits to its web properties each month,” said Darrell
Pratt, director of software development at Cars.com. “Node.js provides the
necessary utility to allow us to grow and change to a microservice
infrastructure. There is so much potential in the future of Node.js through its
community, extensive libraries, and excellent tooling, and we are excited to
help fuel that growth through the Node.js Foundation.”

[Dynatrace](https://www.dynatrace.com), a digital performance management
software company, believes Node.js is a core component for its enterprise
customers. As more enterprises begin to go through digital transformations and
implement microservice-type architectures, Node.js is a key technology that
ties frontend and backend systems together to provide an end-to-end view of
application performance with the help of Dynatrace offerings.

“Digital transformation is constantly changing how companies do business,
something we see first-hand working directly with enterprises to meet their
performance monitoring needs,” said Alois Reitbauer, chief technology
strategist at Dynatrace. “Node.js very often plays a key role in their
transformation process, so we are excited to actively contribute to the Node.js
platform and help developers improve their monitoring capabilities.”

“By joining the Node.js Foundation, companies are investing in the stability of
Node.js and helping to accelerate the widespread adoption and development of
Node.js,” said Mikeal Rogers, community manager at the Node.js Foundation. “New
members like Cars.com and Dynatrace are incredibly important to the
Foundation's work to develop services, education, training and events that
support the needs and demands of enterprise users.”

The Node.js Foundation will be holding
[Node.js Interactive Europe](http://events.linuxfoundation.org/events/node-interactive-europe) in
Amsterdam September 15-18; and
[Node.js Interactive North America](http://events.linuxfoundation.org/events/node-interactive) in Austin, Texas,
November 29 - December 2. This is the only vendor-neutral event that offers an
in-depth look at the future of Node.js from
the developers who are driving the code forward. Node.js Core contributors will
offer insights into new developments, while enterprise users and vendors will
share best practices around tools, training and other services needed to
optimize Node.js.

### About the Node.js Foundation

Node.js is used by tens of thousands of organizations in more than 200 countries and amasses more than 4 million active users per month. It is the runtime of choice for high-performance, low latency applications, powering everything from enterprise applications, robots, API engines, cloud stacks and mobile websites.

The Foundation is made up of a diverse group of companies including Platinum members IBM, Intel, Joyent, Microsoft, PayPal and Red Hat. Gold members include GoDaddy and NodeSource, and Silver members include Apigee, AppDynamics, Cars.com, Codefresh, DigitalOcean, Dynatrace, Fidelity, Google, Groupon, nearForm, New Relic, npm, Opbeat, RisingStack, Sauce Labs, SAP, StrongLoop (an IBM company), Sphinx, YLD, and Yahoo!. Get involved here: https://nodejs.org.
