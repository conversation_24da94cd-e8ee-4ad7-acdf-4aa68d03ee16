---
date: '2016-11-30T12:00:00.000Z'
category: announcements
title: Node.js Foundation To Oversee Node.js Security Project To Further Improve Stability for Enterprises
layout: blog-post
author: The Node.js Project
---

> Node.js Security Project to become one of the largest community projects focused on detecting and fixing vulnerabilities for the fast-growing platform

**SAN FRANCISCO, Nov. 30, 2016** — [The Node.js Foundation](https://foundation.nodejs.org/), a community-led and industry-backed consortium to advance the development of the Node.js platform, today announced that the Node.js Security Project will become a part of the Node.js Foundation. Under the Node.js Foundation, the Node.js Security Project will provide a unified
process for discovering and disclosing security vulnerabilities found in the Node.js module ecosystem.

Last year Node.js Foundation worked with The Linux Foundation’s Core Infrastructure Initiative to form the Node.js Core Security Group to encourage security best practices. By overseeing datasets of vulnerability disclosures, which will be publicly available and openly licensed, the Foundation is building on this work and expanding its role in fortifying Node.js through strong security governance. It will also allow the Foundation to drive standardization around security data and encourage a broader ecosystem of open source and vendor based tools on top of it.

All security vendors are encouraged to contribute to the common vulnerability repository. Once it is openly licensed, the Foundation expects the repository to grow quickly as other vendors add to it.

With 15 million downloads per month, more than a billion package downloads per week, and growing adoption across numerous industries, Node.js and its module ecosystem underpins some of the most heavily used desktop, web, mobile, cloud and IoT applications in the world. The need for a more open, robust, and standard process for finding and fixing vulnerabilities
within the module ecosystem that surrounds Node.js is essential, according to Mikeal Rogers, community manager for Node.js Foundation.

“The Node.js Security Project will become one of the largest projects to build a community around detecting and fixing vulnerabilities,” said Rogers. “Given the maturity of Node.js and how widely used it is in enterprise environments, it makes sense to tackle this endeavor under open governance facilitated by the Node.js Foundation. This allows for more collaboration and communication within the broad community of developers and end users, ensuring the stability and longevity of the large, continually growing Node.js ecosystem.”

A Node.js Security Project Working Group will be established in the next few weeks to begin validating vulnerability disclosures and maintaining the base dataset. Individuals and anyone from the Technical Steering Committee and Core Technical Committee are encouraged to join the working group and provide input on GitHub.

The Node.js Security Project, founded by Adam Baldwin and previously managed by [^Lift Security](https://liftsecurity.io/), an application security company, collects data around vulnerability and security flaws in the Node.js module ecosystem. The Node.js Foundation will take over the following responsibilities from ^Lift:

- Maintaining an entry point for ecosystem vulnerability disclosure;
- Maintaining a private communication channel for vulnerabilities to be vetted;
- Vetting participants in the private security disclosure group;
- Facilitating ongoing research and testing of security data;
- Owning and publishing the base dataset of disclosures, and
- Defining a standard for the data, which tool vendors can build on top of, and security and vendors can add data and value to as well.

“We are very excited about the opportunity to donate this project to the Node.js Foundation” said Adam Baldwin, team lead at ^[Lift](https://liftsecurity.io/)[Security](https://liftsecurity.io/) and founder of the Node.js Security Project. “The Foundation will be able to funnel contributions from numerous vendors, developers and end users to create an incredibly useful baseline of data sets that will be available to anyone. This ensures broader reach and long-lasting viability of the project to encourage availability of more security tools, which is increasingly in demand among Node.js enterprise developers and users.”

^Lift plans to provide upstream contributions to the project based on any new flaws their team uncovers through working with their customers.

### About the Node.js Foundation

Node.js is used by tens of thousands of organizations in more than 200 countries and amasses more than 4.5 million active users per month. It is the runtime of choice for high-performance, low latency applications, powering everything from enterprise applications, robots, API engines, cloud stacks, and mobile websites. The Foundation is made up of a diverse group of companies including Platinum members GoDaddy, IBM, Intel, Joyent, Microsoft, NodeSource, PayPal, and Red Hat. Silver members include Apigee, AppDynamics, Cars.com, Codefresh, DigitalOcean, Dynatrace, Fidelity,Google, Groupon, nearForm, New Relic, npm, Opbeat, RisingStack, Sauce Labs, SAP, StrongLoop (an IBM company), Sphinx, YLD, and Yahoo!. Get involved here: [https://nodejs.org](https://nodejs.org/).
