---
date: '2017-01-26T12:00:00.000Z'
category: announcements
title: The Node.js Foundation Partners with The Linux Foundation on New Node.js Certification Program
layout: blog-post
author: The Node.js Project
---

_Node.js Foundation to launch
vendor-neutral certification program for fastest growing platform_

**SAN FRANCISCO, Jan. 26, 2017** — [The Node.js Foundation](https://foundation.nodejs.org/), a community-led and
industry-backed consortium to advance the development of the Node.js platform,
today announced development of the inaugural Node.js certification program
aimed to establish a baseline competency in Node.js.

Node.js is one of the most popular programming languages with more than 4.5 million
active users per month. While Node.js is increasingly pervasive with
enterprises of all sizes today, organizations that are eager to expand their
use of Node.js often struggle when retraining Java developers and recruiting
new talent.

“The Node.js Foundation, with help from incredible community members and core
experts, is creating a comprehensive certification program that broadens the
funnel of skilled Node.js expertise available. Whether working in enterprise
environments or as individual consultants, those who become Node.js Certified
Developers will be well-positioned to hit the ground running as a Node.js
developer, possessing skills that are in high demand,” said <PERSON>,
education community manager for the Node.js Foundation.

The Node.js Certified Developer program, which is being developed with input from
leading Node.js experts and contributors, is expected to be available in Q2 of 2017. The program will provide a framework for general Node.js competency,
helping enterprises quickly identify qualified Node.js engineers, while
providing developers, contractors and consultants with a way to differentiate
themselves in the market.

Node.js Foundation is worked closely with [The Linux Foundation](https://training.linuxfoundation.org/certification/why-certify-with-us) to create the blueprint
and process for administering the program. The Linux Foundation offers a
neutral home for running training and certification programs, thanks to its
close involvement with the open source community. It offers several open online
courses (MOOCs), including an [Intro to Linux](https://www.edx.org/course/introduction-linux-linuxfoundationx-lfs101x-0), [Intro to DevOps:
Transforming and Improving Operations](https://www.edx.org/course/introduction-devops-transforming-linuxfoundationx-lfs161x); [Developing
Applications for Linux](https://training.linuxfoundation.org/linux-courses/development-training/developing-applications-for-linux); [Kubernetes
Fundamentals](https://training.linuxfoundation.org/linux-courses/system-administration-training/kubernetes-fundamentals); among many others.

Ideal Node.js Certified Developer candidates are early intermediate-level developers
who can already work proficiently in JavaScript with the Node.js platform.
Pricing for the self-paced, online exam is still
to be determined.

Currently the Node.js Foundation is working with the community to determine specific questions that will be asked on the exam. To
contribute to the Node.js Foundation Certification Development Item Writing
Workshop Sessions, fill out this [application](https://docs.google.com/a/linuxfoundation.org/forms/d/10X9RJ4oLu2IU7cXppnXmwDMdJTetq3i9focw-R7GB8s/viewform?edit_requested=true).

Exam topics will be published publicly as will resources to help prepare for the
certification, allowing others to leverage the source materials for their own
Node.js learning.
