---
date: '2015-05-08T19:00:00.000Z'
category: community
title: Next Chapter
layout: blog-post
author: tj<PERSON>nta<PERSON>
---

Open source projects are about the software, the users, and the community. Since
becoming project lead in 2014, I've been privileged to be a part of the most
passionate, diverse, and vibrant community in the ecosystem. The community is
responsible for Node.js' meteoric rise and continued adoption by users and
companies all over the world. Given the strength of its community, I'm confident
that Node.js is heading in the right direction. With that said, it's time for me
to step back.

For the past year, I've worked directly with community members to improve
Node.js, focusing on improving the parts of the project that benefit everyone.
We wanted to know what in Node.js was working for them and what wasn't. During
the life of a project, it's crucial to constantly reset yourself and not lose
sight of your identity. Node.js is a small set of stable core modules, doing one
thing, and one thing well. Every change we make, we tried to make sure we were
being true to ourselves and not violating our ethos. We've focused on
eliminating bugs and critical performance issues, as well as improving our
workflows. Ultimately, our goal was to ensure Node.js was on the right path.

The formation of the Node.js Foundation couldn't have happened at a better time
in the life of Node.js. I believe this will be the tipping point that cements
Node's place in technology. Soon, the foundation will be announcing its first
meeting, initial membership, and future plans for Node.js. The project is on the
right path, has the right contributors and is not tied to one person. It has a
vibrant and loyal community supporting it.

I want to take some time to highlight a few of those who have made an impact on
Node.js. This list only scratches the surface, but these are a few of the unsung
contributors that deserve some attention:

Node.js wanted to have a [living breathing
site](https://github.com/joyent/node-website), one that could attract our
community and be the canonical source of documentation and tutorials for
Node.js. Leading the charge has been [Robert
Kowalski](https://github.com/robertkowalski) and [Wyatt
Preul](https://github.com/geek), who have been incredibly helpful to the Node.js
ecosystem in many ways, but most notably by helping breathe life in the website.

One key point of the maturity for Node.js has been its growing predominance
worldwide. Therefore, we've been working to improve our support for
internationalization and localization. Node.js is so widely accepted that our
users need Node.js to support internationalization so they can better support
their own customers. Luckily, we have [Steven Loomis](https://github.com/srl295)
leading the charge on this — he has the unique privilege of being a member of
both ICU and Node.js.

Node.js is seeing adoption across many new platforms, which means we need to
collaborate with the community to support those platforms. Much like we have
[Alexis Campilla](https://github.com/orangemocha) working to support the Windows
platform, we have people like [Michael Dawson](https://github.com/mhdawson)
working on adding support for PowerPC and zSeries. Additionally, he's been able
to leverage the technical depth of IBM to help squash bugs and do work on our VM
backend of V8.

OpenSSL has had its share of issues recently, but it's not the only dependency
that can be sensitive to upgrade -- so many thanks go to [James
Snell](https://github.com/jasnell) for working to help simplify and manage those
upgrades. James has also been working together with our large, diverse, and
complex community to make sure our development policies are easy to understand
and approachable for other new contributors.

Finally, I want to make a very special mention of [Julien
Gilli](https://github.com/misterdjules), who has been an incredible addition to
the team. Julien has been responsible for the last few releases of Node.js —
both the v0.10 and v0.12 branches. He's done wonders for the project, mostly
behind the scenes, as he has spent tons of time working on shoring up our CI
environment and the tests we run. Thanks to him, we were able to ship v0.12.0
with all our tests passing and on all of our supported platforms. This was the
first Node.js release ever to have that feature. He has also been working
tirelessly to iterate on the process by which the team manages Node.js. Case in
point is the excellent
[documentation](https://nodejs.org/documentation/workflow/) he's put together
describing how to manage the workflow of developing and contributing to the
project.

In short, hiring Julien to work full time on Node.js has been one of the best
things for the project. His care and concern for Node.js, its users, and their
combined future is evident in all of his actions. Node.js is incredibly lucky to
have him at its core and I am truly indebted to him.

It's because of this strong team, community, and the formation of the Foundation
that it makes it the right time for me to step back. The foundation is here, the
software is stable, and the contributors pushing it forward are people I have a
lot of faith in. I can't wait to see just how far Node.js' star will rise. I am
excited to see how the contributors grow, shape and deliver on the promise of
Node.js, for themselves and for our users.

Moving forward, I will still remain involved with Node.js and will provide as
much help and support to the rest of the core team as they need. However, I
won't have the time to participate at the level needed to remain a core
contributor. With the core team and the community working together, I know they
won't miss a step.
