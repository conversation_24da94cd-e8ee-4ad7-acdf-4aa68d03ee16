---
date: '2015-05-15T23:50:46.000Z'
category: community
title: Node.js and io.js leaders are building an open, neutral Node.js Foundation to support the future of the platform
layout: blog-post
author: <PERSON>
---

Just a couple months ago a variety of members of the Node.js and io.js
community announced they would discuss establishing a neutral foundation for
the community. The Linux Foundation has since been helping guide discussions
with contributors, developers, users and leaders in these communities,
increasingly expanding the scope of discussion to more stakeholders. Node.js
and io.js have a long, complex history and the facilitated discussions have
brought together key leaders to focus on what the future might mean for these
technologies.

A lot of progress has been made in just a few short months, and we're
entering the final stages of discussions and decisions that will guide the
projects forward. Most recently [the io.js TC voted to join in the
Foundation](https://github.com/nodejs/node/issues/1705) effort and planning is
already underway to begin the process of converging the codebases. The neutral
organization, or foundation, will be a key element of that work and has been
discussed at length by those involved. When a technology and community reach a
level of maturity and adoption that outgrows one company or project, a
foundation becomes a critical enabler for ongoing growth.

Foundations can be used to support industrial-scale open source projects that
require a legal entity to hold assets or conduct business (hiring, internship
programs, compliance, licensing trademarks, marketing and event services,
fundraising, etc). Ultimately foundations enable communities to participate in
large scale collaboration under agreed upon terms that no one company, person
or entity can change or dictate.

It's important to note that while critical, an open governance model does not
guarantee success or growth. The io.js project has a strong developer
community, for example, but to grow further needs a model to enable funding
and investments in the project. If you haven't already, please [take a look
at Mikeal <PERSON> blog post](https://medium.com/node-js-javascript/growing-up-27d6cc8b7c53).
The Node.js community has needed an avenue for other companies
to participate as equals in a neutral field. rowing a community and widening
the adoption of a technology all takes resources and a governance model that
serves everyone involved. A foundation becomes the place where participants
can meet, agree on paths forward, ensure a neutral playing field in the
community and invest resources to grow the community even more. It can also
allow for broad community engagement through liberal contribution policies,
community self organization and working groups.

At The Linux Foundation, we've helped set up neutral organizations that
support a variety of open source projects and communities through open and
neutral governance and believe the future is bright for the Node.js and io.js
communities. The technology being created has incredible value and expanding
use cases,which is why getting the governance model and defining the role of
the Foundation to support the developer community is the number one priority.

While I'm a relative "newbie" to both the Node.js and io.js communities, I've
been able to identify with our team at Linux Foundation a number of
opportunities, as well as very common challenges in both communities that
relate to other projects we've helped before. What we've found is the
challenges the Node.js and io.js communities have are not unique; many open
source projects struggle with the same challenges and many have been
successful. As I've [previously written on
Linux.com](https://www.linux.com/news/featured-blogs/205-mike-dolan/763051-five-key-features-of-a-project-designed-for-open-collaboration),
there are five key features that we see in successful open governance:

1. open participation
2. open, transparent technical decision making
3. open design and architecture
4. an open source license
5. an open, level playing field for intellectual property.

I think these same features apply to the case for a foundation in the Node.js
and io.js communities. The io.js project has certainly been founded on many of
these principles and has taken off in terms of growing its developer
community. Many in the io.js community joined because they felt these
principles were not present elsewhere. For all of these reasons, we leveraged
the governance provisions from io.js to [draft proposals for the technical
community governance](https://github.com/joyent/nodejs-advisory-board/tree/master/governance-proposal).

Now I'd like to share specific next steps for establishing the Node.js
Foundation (all of this is of course subject to change based on input from the
communities). We've started with a core group that offered advice on how to
address key governance issues. We've expanded the circle to the technical
committees of both communities and are now taking the discussion to the
entirety of both communities.

1. Draft technical governance documents are [up for review and
   comment](https://github.com/joyent/nodejs-advisory-board/tree/master/governance-proposal).

2. The Foundation Bylaws and Membership Agreements based on our LF templates are
   available for companies to sign up as members. There is no need to sign any
   agreements as a community developer. If your company is interested in
   participating, [now is the time to sign
   up](http://f.cl.ly/items/0N1m3x0I3S2L203M1h1r/nodejs-foundation-membership-agreement-2015-march-04.pdf).

3. Hold elections for the foundation's Gold and Silver member Board Directors and
   the Technical Steering Committee elects a TSC Chair. The process typically
   entails 1 week of nominations, 3-5 days of voting and then announcing the
   election winners.

4. Set up an initial Board meeting, likely mid-June. The first Board meeting will
   put in place all of the key legal documents, policies, operations, etc that
   are being discussed (the reason for wrapping up edits on May 8).

5. Initiate TSC meetings under the new foundation by upon resolution of both
   technical committees. The TSC will meet regularly on open, recorded calls.
   Details will be posted on a foundation wiki or page. The combined io.js and
   Node.js TCs have been meeting roughly every other week to work through the
   [Convergence planning](https://github.com/jasnell/dev-policy/blob/6601ca1cd2886f336ac65ddb3f67d3e741a021c9/convergence.md).

6. May 25 - June 5: Announce the new foundation, members, initial Board Directors
   (elections may be pending), TSC members and any reconciliation plans agreed to
   by the TSC (if ready).

And so I ask both communities to review the ideas being proposed, including
how best to align goals, align resources and establish a platform for growing
adoption of an amazing technology the development community working to build.
I would like to thank the people building this future. Some you know; others
you do not. It takes a lot of personal strength to voice opinions and stand up
for new ideas in large communities. I appreciate the candor of the discussions
but also ask you to seek out those putting forth ideas to understand them and
to question them in a constructive dialogue. This community has another decade
or more ahead of it; now is the time to set the right foundational elements to
move forward.
