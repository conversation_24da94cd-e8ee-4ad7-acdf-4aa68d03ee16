---
date: '2015-05-15T22:50:46.000Z'
category: community
title: The Node.js Foundation benefits all
layout: blog-post
author: <PERSON>
---

When I joined Joyent last summer I quickly realized that, despite the huge
success of Node.js in the market and the tireless work of many here at Joyent,
there were challenges in the project that we needed to address. Through
discussions with various project contributors, Node.js users, ecosystem
vendors and the [Node.js Advisory Board](http://nodeadvisoryboard.com), it
became clear that the best way to address the concerns of all key stakeholders
(and the best thing for Node.js as a whole) was to establish the Foundation as
a path for the future.

The biggest and most obvious challenge we sought to address with the
Foundation was the friction that existed amongst some developers in the
Node.js community. Historically, leadership ran the project fairly tightly,
with a small core of developers working in a BDFL model. It was difficult for
new people to join the project, and there wasn’t enough transparency for such
a diverse, passionate community to have a sense of ownership. Consequently, a
group of developers who wanted to operate under a more open governance model
created the io.js fork. That team has done a great job innovating on
governance and engagement models, and the Node.js Foundation’s models will be
based on those policies to ensure broader community engagement in the future
of Node.js. We welcome community review and feedback on [the draft governance
documents](https://github.com/joyent/nodejs-advisory-board/tree/master/governance-proposal).

With the recent vote by the io.js TC to join the Node.js Foundation, we took a
giant leap toward rebuilding a unified community. @mikeal, @piscisaureus and
others have done an excellent job evangelizing the value of the Foundation,
and it’s great to see it have such positive impact this early in its
formation.

Reunification of the Node.js developer community remains an important goal of
the Foundation. But to have a successful project, we must also maintain focus
on addressing the concerns of Node.js users and the ecosystem of vendors. If
we succeed, Node.js will continue its meteoric rise as the defacto server side
JavaScript platform, and everyone wins. If we get it wrong, we jeopardize the
momentum and critical mass that's driven that growth, and everyone loses.

In the user community, enterprise adoption of Node.js has skyrocketed with an
abundance of success stories. But behind every successful project is someone
who is betting their career on the choice to build with Node.js. Their primary
“ask” is to de-risk the project. They want stable, production-grade code that
will handle their technical requirements and an LTS that matches what they get
from other software. The Foundation will get that right. Donations to the
Foundation will provide the resources we need to broaden and automate the
necessary test suites and expand coverage across a large set of platforms. We
are working now on codifying the LTS policy (comments welcome
[here](https://github.com/nodejs/dev-policy/issues/67)) and will establish the
right 6-9 month release cadence with rigor on backward compatibility and EOL
horizon.

Users also want the project to be insulated from the direction of any single
company or individual. Putting the project into a foundation insulates it from
the commercial aspirations of Joyent or any other single company. It also
facilitates the creation of the vibrant vendor ecosystem around Node.js that
users want. Users want to see relevant innovation from a strong group of
contributors and vendors.

The vendors themselves have a clear set of requirements that can best be
addressed by the Foundation. They want a level playing field and they want to
know they can monetize the contributions they make to the project. We need a
vibrant ecosystem to complete the solution for the users of Node.js and drive
additional value and innovation around the core project. The ecosystem is the
force multiplier of value for every piece of technology and Node.js is no
exception.

Finally, in addition to risk mitigation, transparency, neutrality and an open
governance model, the Foundation will provide needed resources. Over the past
few years Joyent and other members of the community have invested thousands of
hours and millions of dollars into the project, and much has been
accomplished. Going forward, Joyent will continue to invest aggressively in
the success and growth of Node.js. But now, with the support of new Foundation
members, we will be able to do even more. Investments from new members can be
used to expand coverage of testing harnesses, establish API compatibility
tests and certifications, extend coverage for additional platforms, underwrite
travel expenses for technical meetups for core contributors, build training
programs for users and developers, expand community development efforts, fund
full-time developers and more.

I’m convinced the Foundation is the best vehicle for balancing the needs of
Node.js users, vendors and contributors. The project has a brilliant future
ahead of it and I am more optimistic than ever that we can work together as
one strong community to secure that future.
