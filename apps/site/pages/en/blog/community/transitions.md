---
date: '2015-05-08T18:00:00.000Z'
category: community
title: Transitions
layout: blog-post
author: <PERSON>
---

In February, we announced the [Node.js
Foundation](https://www.joyent.com/blog/introducing-the-nodejs-foundation),
which will steward Node.js moving forward and open its future up to the
community in a fashion that has not been available before. Organizations like
IBM, SAP, Apigee, F5, Fidelity, Microsoft, PayPal, Red Hat, and others are
sponsoring the Foundation, and they’re adding more contributors to the project.
The mission of the Foundation is to accelerate the adoption of Node and ensure
that the project is driven by the community under a transparent, open governance
model.

Under the aegis of the Foundation, the Node.js project is entering the next
phase of maturity and adopting a model in which there is no BD or project lead.
Instead, the technical direction of the project will be established by a
technical steering committee run with an open governance model. There has been a
lot of discussion on the dev policies and [governance
model](https://github.com/joyent/nodejs-advisory-board/tree/master/governance-proposal)
on GitHub. As we move toward the Foundation model, the core team on Node.js is
already adopting some of these policies [as shown
here](https://github.com/joyent/node-website/pull/111).

As we open a new chapter with the Foundation, we also close a remarkable chapter
in Node.js, as <PERSON><PERSON> will be stepping back from his post as Node.js
Project Lead. T<PERSON> has come to be an integral member of our team, and his
contributions will have long-lasting effects on the future of Node.js. Although
he will not be as active, TJ will continue to act as a resource for helping the
Node.js project as needed.

I would like to thank TJ for his time and contributions to Node.js and to
Joyent. I have witnessed firsthand the kind of impact he can have on a team, and
his technical chops will be missed. As we take this next major step in the
growth of Node.js, we wish TJ luck in his future endeavors.
