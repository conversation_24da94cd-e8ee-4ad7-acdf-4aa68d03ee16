---
date: '2015-11-04T12:00:00.000Z'
category: community
title: Node.js Foundation Individual Membership Now Open
layout: blog-post
author: mikeal
---

The Node.js Foundation is a member-supported organization. To date we've added over 20 corporate members who provide the financial support necessary for the Foundation to thrive.

With the support of the Linux Foundation we are now able to launch an Individual Membership program. These members will be electing two representatives to the Board of Directors this January who will be
responsible for representing the diverse needs of the Node.js community in the administration of the Node.js Foundation.

## How do I become a member?

Membership costs [$100 a year, or $25 for students](https://identity.linuxfoundation.org/pid/99).
Contributors to the Node.js project, including all Working Groups and sub-projects, are eligible for free membership.

You are required to have a GitHub account to register.

## Who can run for the board of directors?

Any registered member.

Keep in mind that every meeting of the Board must reach quorum in order to pass resolutions, so only people who can make themselves available on a recurring and consistent basis should consider running.

## What does the Board of Directors do?

The Board meets every month to approve resolutions and discuss Node.js Foundation administrative matters. This includes legal considerations, budgeting and approving Foundation-led conferences and other initiatives. Technical governance is overseen by the TSC, not the Board of Directors.

The current board members are listed [here](https://foundation.nodejs.org/about/leadership).

## What are the term lengths?

The standard term length for those elected by the individual membership is 2 years, with an election each year to select a new representative for a new term.

However, in the first election two representatives will be elected; the representative with the most votes will be elected for the standard 2 year term and the runner-up will serve a special 1-year term so that in 2017 we can elect a single new director for a 2 year staggered term.

## When is the election?

- Nominations are being solicited until January 15th.
- A ballot will be distributed on January 20th.
- The election will be completed by January 30th.

## How do I run in the 2016 election?

After you've registered as a member follow the instructions [here](https://github.com/nodejs/membership/issues/12).
