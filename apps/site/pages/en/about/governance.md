---
title: Project Governance
layout: about
---

# Project Governance

## Consensus Seeking Process

The Node.js project follows a [Consensus Seeking](https://en.wikipedia.org/wiki/Consensus-seeking_decision-making) decision making model.

## Collaborators

The [nodejs/node](https://github.com/nodejs/node) core GitHub repository is maintained by the Collaborators
who are nominated by other existing Collaborators on an ongoing basis.

Individuals making significant and valuable contributions are made Collaborators
and given commit-access to the project. These individuals are identified by other
Collaborators and their nomination is discussed with the existing Collaborators.

For the current list of Collaborators, see the project's [README.md](https://github.com/nodejs/node/blob/main/README.md#current-project-team-members).

A guide for Collaborators is maintained at [collaborator-guide.md](https://github.com/nodejs/node/blob/main/doc/contributing/collaborator-guide.md).

## Technical Steering Committee

The project is governed by the [Technical Steering Committee (TSC)](https://github.com/nodejs/TSC/blob/main/TSC-Charter.md)
which is responsible for high-level guidance of the project. TSC is a
subset of active Collaborators who are nominated by other existing TSC
members.
