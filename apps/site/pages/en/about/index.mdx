---
title: About Node.js®
layout: about
---

# About Node.js®

As an asynchronous event-driven JavaScript runtime, Node.js is designed to build
scalable network applications. In the following "hello world" example, many
connections can be handled concurrently. Upon each connection, the callback is
fired, but if there is no work to be done, Node.js will sleep.

```cjs
const { createServer } = require('node:http');

const hostname = '127.0.0.1';
const port = 3000;

const server = createServer((req, res) => {
  res.statusCode = 200;
  res.setHeader('Content-Type', 'text/plain');
  res.end('Hello World');
});

server.listen(port, hostname, () => {
  console.log(`Server running at http://${hostname}:${port}/`);
});
```

```mjs
import { createServer } from 'node:http';

const hostname = '127.0.0.1';
const port = 3000;

const server = createServer((req, res) => {
  res.statusCode = 200;
  res.setHeader('Content-Type', 'text/plain');
  res.end('Hello World');
});

server.listen(port, hostname, () => {
  console.log(`Server running at http://${hostname}:${port}/`);
});
```

This is in contrast to today's more common concurrency model, in which OS threads
are employed. Thread-based networking is relatively inefficient and very
difficult to use. Furthermore, users of Node.js are free from worries of
dead-locking the process, since there are no locks. Almost no function in
Node.js directly performs I/O, so the process never blocks except when the I/O is performed using
synchronous methods of Node.js standard library. Because nothing blocks, scalable systems are very
reasonable to develop in Node.js.

If some of this language is unfamiliar, there is a full article on
[Blocking vs. Non-Blocking](/learn/asynchronous-work/overview-of-blocking-vs-non-blocking).

---

Node.js is similar in design to, and influenced by, systems like Ruby's
[Event Machine](https://github.com/eventmachine/eventmachine) and Python's [Twisted](https://twisted.org/). Node.js takes the event model a bit
further. It presents an event loop as a runtime construct instead of as a library. In other systems,
there is always a blocking call to start the event-loop.
Typically, behavior is defined through callbacks at the beginning of a script, and
at the end a server is started through a blocking call like `EventMachine::run()`.
In Node.js, there is no such start-the-event-loop call. Node.js simply enters the event loop after executing the input script. Node.js
exits the event loop when there are no more callbacks to perform. This behavior
is like browser JavaScript — the event loop is hidden from the user.

HTTP is a first-class citizen in Node.js, designed with streaming and low
latency in mind. This makes Node.js well suited for the foundation of a web
library or framework.

Node.js being designed without threads doesn't mean you can't take
advantage of multiple cores in your environment. Child processes can be spawned
by using our [`child_process.fork()`](https://nodejs.org/api/child_process.html) API, and are designed to be easy to
communicate with. Built upon that same interface is the [`cluster`](https://nodejs.org/api/cluster.html) module,
which allows you to share sockets between processes to enable load balancing
over your cores.

## Official Node.js Resources

To ensure authenticity and security when working with Node.js, always use official sources. Avoid trusting emails,
binaries, or downloads from unofficial sources.

### Official Node.js Domains

For downloading Node.js binaries and accessing official documentation, use only these domains:

- [nodejs.org](https://nodejs.org)
- [nodejs.dev](https://nodejs.dev) _(Redirects to https://nodejs.org)_
- [iojs.org](https://iojs.org) _(Redirects to https://nodejs.org)_

### Official npm Packages

The Node.js team maintains the following official npm package scopes:

- [`@node-core`](https://npmjs.com/~node-core)
- [`@pkgjs`](https://npmjs.com/~pkgjs)

Additionally, the Node.js team maintains packages published by the [`nodejs-foundation`](https://npmjs.com/~nodejs-foundation) npm account,
though other Node.js-related packages (like [`undici`](https://www.npmjs.com/package/undici)) may also be maintained by contributors closely
tied to the project.

Using packages from the Node.js team guarantees that you are working with officially supported Node.js components.

### Official GitHub Organizations

Node.js and related projects are maintained under these official GitHub organizations:

- [nodejs](https://github.com/nodejs)
- [pkgjs](https://github.com/pkgjs)

### Official Communication Channels

Node.js and the OpenJS Foundation communicate through various official and community-supported channels. You can find details on
how to get involved on the [Get Involved](https://nodejs.org/en/about/get-involved) page.

### Reporting Website Issues & Downtime

If you encounter issues with the Node.js website, report them at the [Node.js website repository](https://github.com/nodejs/nodejs.org/issues).
For real-time updates on outages, visit the [Node.js Status Page](https://status.nodejs.org).
