---
title: Get involved
layout: about
---

# Get Involved

If you are interested in getting involved with the Node.js community, there are many ways to do so. The Node.js project is a large and diverse community with many ways to contribute beyond just writing code.

## Community Discussion

- The [`nodejs/node` GitHub repository](https://github.com/nodejs/node/issues) is the place to discuss Node.js core features and reporting issues.
- The [`nodejs/help` GitHub repository](https://github.com/nodejs/help/issues) is the official place to ask questions about Node.js.
- Node.js's [official Discord server](/discord) is a place to chat with other Node.js developers and get official news from the Node.js project.
- Node.js's [project calendar](https://nodejs.org/calendar) with all public Node.js team meetings.

## Learning Materials

If you are looking to learn more about Node.js, there are many resources available to you.

- Node.js's [official learning materials](https://nodejs.org/en/learn/).
- Node.js's [official API reference documentation](https://nodejs.org/api/).
- [NodeSchool.io](https://nodeschool.io/) teaches Node.js concepts via interactive command-line games.
- [StackOverflow's Node.js tag](https://stackoverflow.com/questions/tagged/node.js) contains a large number of threads with helpful resources.
- [The DEV Community Node.js's tag](https://dev.to/t/node) contains articles and content related to Node.js.

## Unofficial Discussion Areas

There are several unofficial discussion areas if you are looking for a more informal place to discuss Node.js.
Please note that the Node.js project does not officially endorse these. Please follow their respective codes of conduct/rules.

- [Node Slackers](https://www.nodeslackers.com/) is a Node.js-focused Slack community.
- [OpenJSF Slack](https://slack-invite.openjsf.org/) is a Slack workspace for the OpenJS Foundation. There are several channels related to Node.js. _(channels prefixed by `#nodejs-` are related to the project)_
- `irc.libera.chat` in the `#node.js` channel with an [IRC client](https://en.wikipedia.org/wiki/Comparison_of_Internet_Relay_Chat_clients) or connect in your web browser to the channel using [a web client](https://kiwiirc.com/nextclient/).
