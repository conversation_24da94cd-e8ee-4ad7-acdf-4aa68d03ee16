---
title: Collaboration Summit
layout: about
---

# Collaboration Summit

Node.js's Collaboration Summit is an un-conference for bringing current and
potential contributors together to discuss Node.js with lively collaboration,
education, and knowledge sharing. Teams, working groups and contributors
from the community come together twice per year to have discussions that
help decision-making while also working on some exciting efforts they
want to push forward in-person.

## Who attends?

The Collaboration Summit is primarily attended by existing contributors and
community members, but it also welcomes those who are not yet a contributor
and want to get onboard. If you are new to contributing to Node.js, the
Collaboration Summit can be a good opportunity to help you learn what is
happening within the community and contribute with the skills you have
and would like to hone.

Prior to the summit, contributors and community members send session proposals to
create a schedule. Attendees can familiarize themselves with the session before
getting onsite, having the general collaborator discussions, and then diving
into sessions. There will also be plenty of opportunities for hallway tracks
and brainstorms.

For information about upcoming and past Collaboration Summits, check out the
[Summit repo](https://github.com/openjs-foundation/summit). Have a look at the
[issues filed](https://github.com/nodejs/summit/issues) that share what
contributors and community members are proposing to discuss in-person.
