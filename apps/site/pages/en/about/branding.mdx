---
title: Branding of Node.js
layout: about
---

# Branding of Node.js

Please review the [trademark policy](https://trademark-policy.openjsf.org/) for information about permissible use of Node.js® logos and marks.

## Node.js® Mascot

Credit to [<PERSON>](https://www.linkedin.com/in/angeliningl/) for designing and contributing the Rocket Turtle.

<img
  alt="Node.js mascot"
  src="/static/images/node-mascot.svg"
  className="w-[100px]"
  width="100"
  height="114"
/>

## Node.js® Logo

### Node.js® Horizontal Logo

<table>
  <tbody>
    <tr>
      <td>
        <img
          alt="Node.js Dark Horizontal Logo"
          src="/static/logos/nodejsDark.svg"
          className="h-[80px] w-[267px]"
          width="267"
          height="80"
        />
      </td>
      <td>
        <img
          alt="Node.js Light Horizontal Logo"
          src="/static/logos/nodejsLight.svg"
          className="h-[80px] w-[267px] bg-neutral-950 p-2 dark:bg-transparent"
          width="267"
          height="80"
        />
      </td>
    </tr>
  </tbody>
</table>

### Node.js® Stacked Logo

<table>
  <tbody>
    <tr>
      <td>
        <img
          alt="Node.js Dark Stacked Logo"
          src="/static/logos/nodejsStackedDark.svg"
          className="h-[164px] w-[267px]"
          width="267"
          height="164"
        />
      </td>
      <td>
        <img
          alt="Node.js Light Stacked Logo"
          src="/static/logos/nodejsStackedLight.svg"
          className="rounded-xs h-[164px] w-[267px] bg-neutral-950 p-2 dark:bg-transparent"
          width="267"
          height="164"
        />
      </td>
    </tr>
    <tr>
      <td>
        <img
          alt="Node.js Black Stacked Logo"
          src="/static/logos/nodejsStackedBlack.svg"
        />
      </td>
      <td>
        <img
          alt="Node.js White Stacked Logo"
          src="/static/logos/nodejsStackedWhite.svg"
          className="rounded-xs bg-neutral-950 p-2 dark:bg-transparent"
        />
      </td>
    </tr>
  </tbody>
</table>

### JS Icons

<table>
  <tbody>
    <tr>
      <td>
        <img
          alt="JS Icons Green"
          src="/static/logos/jsIconGreen.svg"
          className="height-[80px] mx-auto w-[71px]"
          width="71"
          height="80"
        />
      </td>
      <td>
        <img
          alt="White JS Icons"
          src="/static/logos/jsIconWhite.svg"
          className="height-[80px] rounded-xs mx-auto w-[71px] bg-neutral-950 p-2 dark:bg-transparent"
          width="71"
          height="80"
        />
      </td>
    </tr>
  </tbody>
</table>
